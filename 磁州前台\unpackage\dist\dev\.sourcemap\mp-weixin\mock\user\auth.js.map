{"version": 3, "file": "auth.js", "sources": ["mock/user/auth.js"], "sourcesContent": ["// 用户认证模拟数据\r\nexport const authData = {\r\n  token: 'mock-token-12345',\r\n  refreshToken: 'mock-refresh-token-12345',\r\n  expiry: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7天后过期\r\n};\r\n\r\n// 用户登录的API函数\r\nexport const login = (phone, code) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      if (phone && code) {\r\n        resolve({\r\n          success: true,\r\n          token: 'mock-token-' + Date.now(),\r\n          refreshToken: 'mock-refresh-token-' + Date.now(),\r\n          expiry: Date.now() + 7 * 24 * 60 * 60 * 1000,\r\n          userInfo: {\r\n            id: 'user-001',\r\n            nickname: '磁州居民',\r\n            avatar: '/static/images/tabbar/user-blue.png',\r\n            phone: phone\r\n          }\r\n        });\r\n      } else {\r\n        resolve({\r\n          success: false,\r\n          message: '手机号或验证码错误'\r\n        });\r\n      }\r\n    }, 500);\r\n  });\r\n};\r\n\r\n// 用户注册的API函数\r\nexport const register = (phone, code, password) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      if (phone && code) {\r\n        resolve({\r\n          success: true,\r\n          token: 'mock-token-' + Date.now(),\r\n          refreshToken: 'mock-refresh-token-' + Date.now(),\r\n          expiry: Date.now() + 7 * 24 * 60 * 60 * 1000,\r\n          userInfo: {\r\n            id: 'user-' + Date.now(),\r\n            nickname: '磁州新用户',\r\n            avatar: '/static/images/tabbar/user-blue.png',\r\n            phone: phone\r\n          }\r\n        });\r\n      } else {\r\n        resolve({\r\n          success: false,\r\n          message: '注册信息不完整'\r\n        });\r\n      }\r\n    }, 700);\r\n  });\r\n};\r\n\r\n// 获取验证码的API函数\r\nexport const getVerificationCode = (phone) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      if (phone && phone.length === 11) {\r\n        resolve({\r\n          success: true,\r\n          message: '验证码已发送',\r\n          code: '1234' // 模拟环境下固定验证码\r\n        });\r\n      } else {\r\n        resolve({\r\n          success: false,\r\n          message: '手机号格式错误'\r\n        });\r\n      }\r\n    }, 300);\r\n  });\r\n};\r\n\r\n// 用户登出的API函数\r\nexport const logout = () => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve({ \r\n        success: true,\r\n        message: '退出登录成功'\r\n      });\r\n    }, 300);\r\n  });\r\n};\r\n\r\n// 刷新token的API函数\r\nexport const refreshAuthToken = (refreshToken) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      if (refreshToken) {\r\n        resolve({\r\n          success: true,\r\n          token: 'mock-token-' + Date.now(),\r\n          refreshToken: 'mock-refresh-token-' + Date.now(),\r\n          expiry: Date.now() + 7 * 24 * 60 * 60 * 1000\r\n        });\r\n      } else {\r\n        resolve({\r\n          success: false,\r\n          message: 'refresh token无效'\r\n        });\r\n      }\r\n    }, 400);\r\n  });\r\n}; "], "names": [], "mappings": ";AAQY,MAAC,QAAQ,CAAC,OAAO,SAAS;AACpC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,UAAI,SAAS,MAAM;AACjB,gBAAQ;AAAA,UACN,SAAS;AAAA,UACT,OAAO,gBAAgB,KAAK,IAAK;AAAA,UACjC,cAAc,wBAAwB,KAAK,IAAK;AAAA,UAChD,QAAQ,KAAK,IAAK,IAAG,IAAI,KAAK,KAAK,KAAK;AAAA,UACxC,UAAU;AAAA,YACR,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,QAAQ;AAAA,YACR;AAAA,UACD;AAAA,QACX,CAAS;AAAA,MACT,OAAa;AACL,gBAAQ;AAAA,UACN,SAAS;AAAA,UACT,SAAS;AAAA,QACnB,CAAS;AAAA,MACF;AAAA,IACF,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,WAAW,CAAC,OAAO,MAAM,aAAa;AACjD,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,UAAI,SAAS,MAAM;AACjB,gBAAQ;AAAA,UACN,SAAS;AAAA,UACT,OAAO,gBAAgB,KAAK,IAAK;AAAA,UACjC,cAAc,wBAAwB,KAAK,IAAK;AAAA,UAChD,QAAQ,KAAK,IAAK,IAAG,IAAI,KAAK,KAAK,KAAK;AAAA,UACxC,UAAU;AAAA,YACR,IAAI,UAAU,KAAK,IAAK;AAAA,YACxB,UAAU;AAAA,YACV,QAAQ;AAAA,YACR;AAAA,UACD;AAAA,QACX,CAAS;AAAA,MACT,OAAa;AACL,gBAAQ;AAAA,UACN,SAAS;AAAA,UACT,SAAS;AAAA,QACnB,CAAS;AAAA,MACF;AAAA,IACF,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,sBAAsB,CAAC,UAAU;AAC5C,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,UAAI,SAAS,MAAM,WAAW,IAAI;AAChC,gBAAQ;AAAA,UACN,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA;AAAA,QAChB,CAAS;AAAA,MACT,OAAa;AACL,gBAAQ;AAAA,UACN,SAAS;AAAA,UACT,SAAS;AAAA,QACnB,CAAS;AAAA,MACF;AAAA,IACF,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,SAAS,MAAM;AAC1B,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,cAAQ;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,MACjB,CAAO;AAAA,IACF,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,mBAAmB,CAAC,iBAAiB;AAChD,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,UAAI,cAAc;AAChB,gBAAQ;AAAA,UACN,SAAS;AAAA,UACT,OAAO,gBAAgB,KAAK,IAAK;AAAA,UACjC,cAAc,wBAAwB,KAAK,IAAK;AAAA,UAChD,QAAQ,KAAK,IAAK,IAAG,IAAI,KAAK,KAAK,KAAK;AAAA,QAClD,CAAS;AAAA,MACT,OAAa;AACL,gBAAQ;AAAA,UACN,SAAS;AAAA,UACT,SAAS;AAAA,QACnB,CAAS;AAAA,MACF;AAAA,IACF,GAAE,GAAG;AAAA,EACV,CAAG;AACH;;;;;;"}