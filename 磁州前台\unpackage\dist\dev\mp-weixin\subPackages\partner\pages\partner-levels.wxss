/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-eeb58a13, html.data-v-eeb58a13, #app.data-v-eeb58a13, .index-container.data-v-eeb58a13 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.levels-container.data-v-eeb58a13 {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 40rpx;
}

/* 自定义导航栏 */
.custom-navbar.data-v-eeb58a13 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  padding: 0 30rpx;
  padding-top: 44px;
  /* 状态栏高度 */
  position: fixed;
  /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  background-image: linear-gradient(135deg, #0066FF, #0052CC);
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.15);
  z-index: 100;
  /* 提高z-index确保在最上层 */
}
.navbar-title.data-v-eeb58a13 {
  position: absolute;
  left: 0;
  right: 0;
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 700;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  text-align: center;
}
.navbar-left.data-v-eeb58a13 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 20;
  /* 确保在标题上层，可以被点击 */
}
.back-icon.data-v-eeb58a13 {
  width: 100%;
  height: 100%;
}
.safe-area-top.data-v-eeb58a13 {
  height: 180rpx;
  width: 100%;
}

/* 当前等级卡片 */
.current-level-card.data-v-eeb58a13 {
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.level-header.data-v-eeb58a13 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.level-info.data-v-eeb58a13 {
  display: flex;
  align-items: center;
}
.level-badge.data-v-eeb58a13 {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}
.level-detail.data-v-eeb58a13 {
  display: flex;
  flex-direction: column;
}
.level-name.data-v-eeb58a13 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 6rpx;
}
.level-desc.data-v-eeb58a13 {
  font-size: 24rpx;
  color: #666666;
}
.upgrade-btn.data-v-eeb58a13 {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  padding: 12rpx 30rpx;
  border-radius: 30rpx;
  color: #ffffff;
  font-size: 26rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
}
.level-progress.data-v-eeb58a13 {
  margin-top: 20rpx;
}
.progress-info.data-v-eeb58a13 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
}
.progress-text.data-v-eeb58a13 {
  font-size: 26rpx;
  color: #666666;
}
.progress-value.data-v-eeb58a13 {
  font-size: 26rpx;
  color: #FF6B00;
  font-weight: 500;
}
.progress-track.data-v-eeb58a13 {
  height: 16rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  overflow: hidden;
}
.progress-bar.data-v-eeb58a13 {
  height: 100%;
  background: linear-gradient(to right, #0066FF, #36CBCB);
  border-radius: 8rpx;
  transition: width 0.5s ease;
}

/* 等级特权卡片 */
.privileges-card.data-v-eeb58a13 {
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.card-title.data-v-eeb58a13 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 30rpx;
  position: relative;
}
.card-title.data-v-eeb58a13::after {
  content: "";
  position: absolute;
  bottom: -10rpx;
  left: 0;
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(to right, #0066FF, #36CBCB);
  border-radius: 2rpx;
}
.privileges-table.data-v-eeb58a13 {
  width: 100%;
  border-radius: 12rpx;
  overflow: hidden;
  border: 1px solid #e0e0e0;
}
.table-header.data-v-eeb58a13 {
  display: flex;
  background-color: #f5f7fa;
}
.header-cell.data-v-eeb58a13 {
  flex: 1;
  padding: 20rpx 10rpx;
  text-align: center;
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
  border-right: 1px solid #e0e0e0;
}
.header-cell.data-v-eeb58a13:last-child {
  border-right: none;
}
.level-cell.data-v-eeb58a13 {
  flex: 1.5;
}
.table-row.data-v-eeb58a13 {
  display: flex;
  border-top: 1px solid #e0e0e0;
}
.table-row.active.data-v-eeb58a13 {
  background-color: rgba(0, 102, 255, 0.05);
}
.table-cell.data-v-eeb58a13 {
  flex: 1;
  padding: 20rpx 10rpx;
  text-align: center;
  font-size: 26rpx;
  color: #666666;
  border-right: 1px solid #e0e0e0;
}
.table-cell.data-v-eeb58a13:last-child {
  border-right: none;
}
.level-tag.data-v-eeb58a13 {
  display: flex;
  align-items: center;
  justify-content: center;
}
.level-tag .mini-badge.data-v-eeb58a13 {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}
.level-tag text.data-v-eeb58a13 {
  font-size: 24rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 升级规则卡片 */
.rules-card.data-v-eeb58a13 {
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.rule-item.data-v-eeb58a13 {
  margin-bottom: 20rpx;
}
.rule-item.data-v-eeb58a13:last-child {
  margin-bottom: 0;
}
.rule-title.data-v-eeb58a13 {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.rule-dot.data-v-eeb58a13 {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #0066FF;
  margin-right: 10rpx;
}
.rule-title text.data-v-eeb58a13 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}
.rule-content.data-v-eeb58a13 {
  padding-left: 22rpx;
}
.rule-content text.data-v-eeb58a13 {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.6;
}

/* FAQ卡片 */
.faq-card.data-v-eeb58a13 {
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.faq-item.data-v-eeb58a13 {
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #f0f0f0;
}
.faq-item.data-v-eeb58a13:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}
.faq-question.data-v-eeb58a13 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 0;
}
.faq-question text.data-v-eeb58a13 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}
.arrow-icon.data-v-eeb58a13 {
  width: 32rpx;
  height: 32rpx;
  transition: transform 0.3s ease;
}
.arrow-icon.rotated.data-v-eeb58a13 {
  transform: rotate(180deg);
}
.faq-answer.data-v-eeb58a13 {
  margin-top: 15rpx;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
}
.faq-answer text.data-v-eeb58a13 {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.6;
}

/* 升级弹窗 */
.upgrade-modal.data-v-eeb58a13 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}
.modal-content.data-v-eeb58a13 {
  width: 80%;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
}
.modal-header.data-v-eeb58a13 {
  padding: 30rpx;
  background: linear-gradient(135deg, #0066FF, #0052CC);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.modal-title.data-v-eeb58a13 {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
}
.close-btn.data-v-eeb58a13 {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 40rpx;
  font-weight: 300;
}
.modal-body.data-v-eeb58a13 {
  padding: 30rpx;
}
.upgrade-info.data-v-eeb58a13 {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}