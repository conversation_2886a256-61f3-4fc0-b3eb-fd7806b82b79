{"version": 3, "file": "info-detail.js", "sources": ["pages/publish/info-detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHVibGlzaC9pbmZvLWRldGFpbC52dWU"], "sourcesContent": ["<template>\n  <view class=\"post-detail-container\">\n    <!-- 隐藏的分享按钮，用于自动触发 -->\n    <button id=\"shareButton\" class=\"hidden-share-btn\" open-type=\"share\"></button>\n    \n    <view class=\"post-detail-wrapper\">\n      <!-- 信息主体卡片 -->\n      <view class=\"content-card main-info\">\n        <view class=\"post-header\">\n          <view class=\"post-title\">{{ post.title }}</view>\n          <view class=\"post-meta\">\n            <text class=\"post-time\">发布于 {{ formatTime(post.createdAt) }}</text>\n            <text class=\"post-category\">{{ post.category }}</text>\n            <view class=\"post-views\">\n              <text class=\"iconfont icon-eye\"></text>\n              <text>{{ post.views }}</text>\n            </view>\n          </view>\n        </view>\n\n        <view class=\"post-gallery\" v-if=\"post.images && post.images.length > 0\">\n          <swiper class=\"swiper\" \n                indicator-dots \n                :autoplay=\"true\" \n                :interval=\"4000\" \n                :duration=\"500\" \n                circular\n                v-if=\"post.images.length > 1\">\n            <swiper-item v-for=\"(image, index) in post.images\" :key=\"index\" @click=\"previewImage(index)\">\n              <image :src=\"image\" class=\"carousel-image\" mode=\"aspectFill\"></image>\n          </swiper-item>\n        </swiper>\n          <view class=\"single-image\" v-else>\n            <image :src=\"post.images[0]\" @click=\"previewImage(0)\" mode=\"widthFix\"></image>\n          </view>\n        </view>\n\n        <view class=\"divider\"></view>\n\n        <view class=\"post-content\">\n          <rich-text :nodes=\"post.content\"></rich-text>\n        </view>\n\n        <view class=\"post-tags\" v-if=\"post.tags && post.tags.length > 0\">\n          <uni-tag v-for=\"tag in post.tags\" :key=\"tag\" :text=\"tag\" size=\"small\" type=\"default\" :inverted=\"true\" class=\"tag\"></uni-tag>\n        </view>\n      </view>\n\n      <!-- 发布者信息卡片 -->\n      <view class=\"content-card publisher-info\">\n        <view class=\"publisher-header\">\n          <text class=\"card-title\">发布者信息</text>\n        </view>\n        <view class=\"publisher-content\">\n          <view class=\"publisher-avatar\">\n            <image class=\"avatar-image\" :src=\"post.publisher?.avatar\" mode=\"aspectFill\"></image>\n          </view>\n          <view class=\"publisher-details\">\n            <view class=\"publisher-name\">{{ post.publisher?.username }}</view>\n            <view class=\"publisher-stats\">\n              <view class=\"stat-item\">\n                <view class=\"stat-value\">{{ post.publisher?.posts || 0 }}</view>\n                <view class=\"stat-label\">发布</view>\n              </view>\n              <view class=\"stat-item\">\n                <view class=\"stat-value\">{{ post.publisher?.followers || 0 }}</view>\n                <view class=\"stat-label\">粉丝</view>\n              </view>\n              <view class=\"stat-item\">\n                <view class=\"stat-value\">{{ post.publisher?.rating || '5.0' }}</view>\n                <view class=\"stat-label\">评分</view>\n              </view>\n            </view>\n          </view>\n          <button class=\"contact-btn\" type=\"primary\" size=\"mini\" @click=\"contactPublisher\">联系TA</button>\n        </view>\n      </view>\n\n      <!-- 位置信息卡片 -->\n      <view class=\"content-card location-info\" v-if=\"post.location\">\n        <view class=\"location-header\">\n          <text class=\"card-title\">位置信息</text>\n        </view>\n        <view class=\"location-content\">\n          <text class=\"iconfont icon-location\"></text>\n          <text>{{ post.location.address }}</text>\n        </view>\n        <view class=\"location-map\">\n          <image src=\"https://via.placeholder.com/600x200?text=Map+Preview\" mode=\"widthFix\" class=\"map-preview\"></image>\n        </view>\n      </view>\n\n      <!-- 评论区卡片 -->\n      <view class=\"content-card comment-section\">\n        <view class=\"comment-header\">\n          <text class=\"card-title\">评论区</text>\n        </view>\n        <!-- 评论组件，可以自定义或使用uni-app插件 -->\n        <view class=\"comment-list\">\n          <view class=\"comment-empty\" v-if=\"comments.length === 0\">\n            <text>暂无评论，快来发表第一条评论吧！</text>\n          </view>\n          <view class=\"comment-item\" v-for=\"(comment, index) in comments\" :key=\"index\">\n            <view class=\"comment-user\">\n              <image class=\"comment-avatar\" :src=\"comment.avatar\" mode=\"aspectFill\"></image>\n              <view class=\"comment-user-info\">\n                <text class=\"comment-username\">{{ comment.username }}</text>\n                <text class=\"comment-time\">{{ formatTime(comment.createdAt) }}</text>\n              </view>\n            </view>\n            <view class=\"comment-content\">{{ comment.content }}</view>\n          </view>\n        </view>\n        <view class=\"comment-input-area\">\n          <input class=\"comment-input\" type=\"text\" placeholder=\"写下你的评论...\" v-model=\"commentText\" confirm-type=\"send\" @confirm=\"submitComment\" />\n          <button class=\"comment-submit\" type=\"primary\" size=\"mini\" @click=\"submitComment\">发送</button>\n      </view>\n      </view>\n      \n      <!-- 相关信息推荐 -->\n      <view class=\"content-card related-posts-card\">\n        <view class=\"section-title\">相关信息推荐</view>\n        <view class=\"related-posts-content\">\n          <!-- 简洁的信息列表 -->\n          <view class=\"related-posts-list\">\n            <view class=\"related-post-item\" \n                 v-for=\"(relatedPost, index) in relatedPosts.slice(0, 3)\" \n                 :key=\"index\" \n                 @click=\"navigateToPostDetail(relatedPost.id)\">\n              <view class=\"post-item-content\">\n                <view class=\"post-item-left\" v-if=\"relatedPost.image\">\n                  <image class=\"post-image\" :src=\"relatedPost.image\" mode=\"aspectFill\"></image>\n                </view>\n                <view class=\"post-item-middle\">\n                  <text class=\"post-item-title\">{{relatedPost.title}}</text>\n                  <view class=\"post-item-category\">{{relatedPost.category}}</view>\n                  <view class=\"post-item-meta\">\n                    <text class=\"post-item-time\">{{formatTime(relatedPost.createdAt)}}</text>\n                    <text class=\"post-item-views\">{{relatedPost.views}}浏览</text>\n                  </view>\n                </view>\n              </view>\n            </view>\n            \n            <!-- 暂无数据提示 -->\n            <view class=\"empty-related-posts\" v-if=\"relatedPosts.length === 0\">\n              <image src=\"/static/images/empty.png\" class=\"empty-image\" mode=\"aspectFit\"></image>\n              <text class=\"empty-text\">暂无相关信息</text>\n            </view>\n          </view>\n          \n          <!-- 查看更多按钮 -->\n          <view class=\"view-more-btn\" v-if=\"relatedPosts.length > 0\" @click.stop=\"navigateToInfoList\">\n            <text class=\"view-more-text\">查看更多信息</text>\n            <text class=\"view-more-icon iconfont icon-right\"></text>\n          </view>\n        </view>\n      </view>\n\n      <RedPacketEntry v-model=\"form.redPacket\" />\n      \n      <!-- 添加红包展示部分 -->\n      <view class=\"content-card red-packet-card\" v-if=\"form.redPacket && form.redPacket.amount\">\n        <view class=\"section-title\">红包福利</view>\n        <view class=\"red-packet-details\">\n          <view class=\"red-packet-amount\">\n            <text class=\"amount-label\">红包金额</text>\n            <text class=\"amount-value\">{{form.redPacket.amount}}元</text>\n          </view>\n          <view class=\"red-packet-usage\">\n            <text class=\"usage-label\">红包个数</text>\n            <text class=\"usage-value\">共{{form.redPacket.count}}个 剩余{{form.redPacket.remain}}个</text>\n          </view>\n          <view class=\"red-packet-method\">\n            <text class=\"method-label\">红包类型</text>\n            <text class=\"method-value\">{{form.redPacket.type === 'fixed' ? '普通红包' : '拼手气红包'}}</text>\n          </view>\n        </view>\n        <view class=\"red-packet-receive\" @click=\"receiveRedPacket\">\n          立即领取\n        </view>\n      </view>\n    </view>\n\n    <!-- 底部互动工具栏 -->\n    <view class=\"interaction-toolbar\">\n      <view class=\"toolbar-item\" @click=\"goToHome\">\n        <image src=\"/static/images/tabbar/a首页.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">首页</text>\n      </view>\n      <view class=\"toolbar-item\" @click=\"toggleCollect\">\n        <image src=\"/static/images/tabbar/a收藏.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">收藏</text>\n      </view>\n      <button class=\"share-button toolbar-item\" open-type=\"share\">\n        <image src=\"/static/images/tabbar/a分享.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">分享</text>\n      </button>\n      <view class=\"toolbar-item\" @click=\"openChat\">\n        <image src=\"/static/images/tabbar/a消息.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">私信</text>\n      </view>\n      <view class=\"toolbar-item call-button\" @click=\"makePhoneCall\">\n        <view class=\"call-button-content\">\n          <text class=\"call-text\">打电话</text>\n          <text class=\"call-subtitle\">请说在磁州生活网看到的</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 修改分享引导浮层，替换为更简单的UI并添加直接分享按钮 -->\n    <view class=\"share-guide-layer\" v-if=\"showShareGuideLayer\" @click=\"showShareGuideLayer = false\">\n      <view class=\"guide-content\" @click.stop>\n        <view class=\"guide-close\" @click=\"showShareGuideLayer = false\">×</view>\n        <view class=\"guide-title\">一键分享</view>\n        <view class=\"guide-desc\">点击下方按钮，即可分享给朋友或群聊</view>\n        <view class=\"guide-tips\">分享可获得信息置顶特权</view>\n        \n        <!-- 直接添加分享按钮 -->\n        <button class=\"guide-share-btn\" open-type=\"share\">\n          立即分享\n        </button>\n      </view>\n    </view>\n\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\">\n      <text class=\"navbar-title\">信息详情</text>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted, reactive } from 'vue'\nimport RedPacketEntry from '@/components/RedPacket/RedPacketEntry.vue'\n\n// 格式化时间函数\nconst formatTime = (timestamp) => {\n  const date = new Date(timestamp);\n  return `${date.getMonth() + 1}月${date.getDate()}日`;\n};\n\n// 响应式数据\nconst postId = ref('')\nconst commentText = ref('')\nconst comments = ref([])\nconst isLiked = ref(false)\nconst isCollected = ref(false)\nconst shareButtonHighlight = ref(false)\nconst showShareGuideLayer = ref(false)\nconst post = ref({\n  id: '',\n  title: '精装修三室两厅，交通便利，拎包入住',\n  content: '位于城市中心，周边配套设施齐全，距离地铁站仅500米。房屋为精装修，家具家电齐全，随时可以入住。小区环境优美，安静舒适，24小时保安巡逻，安全有保障。适合一家人居住，看房方便，欢迎随时联系。',\n  category: '房屋出租',\n  createdAt: Date.now() - 3600000 * 24 * 3,\n  views: 256,\n  tags: ['精装修', '地铁附近', '拎包入住', '家电齐全'],\n        images: [\n    'https://via.placeholder.com/800x450?text=Room+1',\n    'https://via.placeholder.com/800x450?text=Room+2',\n    'https://via.placeholder.com/800x450?text=Room+3',\n  ],\n  publisher: {\n    username: '房产小能手',\n    avatar: 'https://via.placeholder.com/100?text=Avatar',\n    posts: 32,\n    followers: 128,\n    rating: 4.8,\n    phone: '13912345678'\n  },\n  location: {\n    address: '某某市某某区某某街123号',\n    latitude: 30.123456,\n    longitude: 120.123456\n  }\n})\n\n// 相关信息列表\nconst relatedPosts = ref([]);\n\n// 加载相关信息\nconst loadRelatedPosts = () => {\n  // 这里可以调用API获取数据\n  // 实际项目中应该根据当前信息的分类、标签等进行相关性匹配\n  \n  // 模拟数据\n  setTimeout(() => {\n    relatedPosts.value = [\n      {\n        id: 'post001',\n        title: '全新装修两室一厅出租，家电齐全',\n        category: '房屋出租',\n        createdAt: Date.now() - 86400000 * 1,\n        views: 158,\n        image: 'https://via.placeholder.com/100?text=Room'\n      },\n      {\n        id: 'post002',\n        title: '市中心单身公寓，拎包入住',\n        category: '房屋出租',\n        createdAt: Date.now() - 86400000 * 2,\n        views: 243,\n        image: 'https://via.placeholder.com/100?text=Apartment'\n      },\n      {\n        id: 'post003',\n        title: '南北通透三居室，学区房，周边配套齐全',\n        category: '房屋出租',\n        createdAt: Date.now() - 86400000 * 3,\n        views: 325,\n        image: 'https://via.placeholder.com/100?text=House'\n      }\n    ];\n  }, 500);\n};\n\n// 跳转到信息详情页\nconst navigateToPostDetail = (id) => {\n  // 避免重复跳转当前页面\n  if (id === postId.value) {\n    return;\n  }\n  \n  uni.navigateTo({\n    url: `/pages/publish/info-detail?id=${id}`\n  });\n};\n\n// 跳转到信息列表页\nconst navigateToInfoList = (e) => {\n  if (e) e.stopPropagation();\n  const infoCategory = post.value.tags?.[0] || '';\n  uni.navigateTo({ \n    url: `/subPackages/service/pages/filter?type=info&title=${encodeURIComponent('生活信息')}&category=${encodeURIComponent(infoCategory)}&active=info` \n  });\n};\n\n// 生命周期钩子\nonMounted(() => {\n  // 修改页面标题和背景色\n  uni.setNavigationBarTitle({\n    title: '信息详情'\n  })\n  \n  uni.setNavigationBarColor({\n    frontColor: '#ffffff',\n    backgroundColor: '#0052CC'\n  })\n  \n  // 获取路由参数\n  const pages = getCurrentPages()\n  const currentPage = pages[pages.length - 1]\n  const options = currentPage.$page?.options || {}\n  postId.value = options.id || '1'\n  post.value.id = postId.value\n  console.log('加载信息详情:', postId.value)\n  \n  // 加载相关信息推荐\n  loadRelatedPosts()\n  \n  // 检查是否需要直接分享\n  const shouldDirectShare = options.directShare === '1' || uni.getStorageSync('directShare') === 1\n  \n  // 清除存储的标记\n  if (uni.getStorageSync('directShare')) {\n    uni.removeStorageSync('directShare')\n  }\n  \n  // 处理autoShare参数，自动打开分享菜单\n  if (options.autoShare === 'true') {\n    console.log('检测到autoShare参数，准备显示分享菜单')\n    \n    // 延迟执行，确保页面已完全加载\n    setTimeout(() => {\n      // 直接显示分享引导层\n      showShareGuideLayer.value = true\n    }, 500)\n  }\n  \n  // 如果需要直接分享，模拟点击分享按钮\n  if (shouldDirectShare) {\n    console.log('检测到直接分享标记，准备直接分享')\n    \n    // 延迟一点以确保页面加载完成，微信有时候需要一点时间准备分享功能\n    setTimeout(() => {\n      // 尝试直接触发分享\n      triggerDirectShare()\n    }, 1000)\n  }\n  \n  // 模拟评论数据\n  comments.value = [\n    {\n      id: 1,\n      username: '用户123',\n      avatar: 'https://via.placeholder.com/40?text=U1',\n      content: '房子位置很好，周边设施齐全，交通便利。',\n      createdAt: Date.now() - 3600000 * 48\n    },\n    {\n      id: 2,\n      username: '小明',\n      avatar: 'https://via.placeholder.com/40?text=U2',\n      content: '请问有停车位吗？',\n      createdAt: Date.now() - 3600000 * 24\n    }\n  ]\n  \n  // 处理红包信息\n  const { hasRedPacket, redPacketAmount, redPacketType, redPacketCount, redPacketRemain } = options;\n  if (hasRedPacket === '1' && redPacketAmount) {\n    form.value.redPacket = {\n      amount: redPacketAmount,\n      count: redPacketCount || '0',\n      remain: redPacketRemain || '0',\n      type: redPacketType || 'fixed',\n      taskType: 0 // 默认任务类型\n    };\n    console.log('设置红包信息:', JSON.stringify(form.value.redPacket));\n  }\n})\n\n// 添加一个方法用于触发分享\nconst triggerShare = () => {\n  // 在小程序环境中，尝试使用API触发分享\n  if (uni.canIUse('showShareMenu')) {\n  uni.showShareMenu({\n    withShareTicket: true,\n      menus: ['shareAppMessage', 'shareTimeline'],\n    success: () => {\n        console.log('显示分享菜单成功')\n      \n        // 尝试模拟点击分享按钮\n      setTimeout(() => {\n          // 获取页面中的所有 button 元素\n          const query = uni.createSelectorQuery()\n          query.selectAll('.share-button').boundingClientRect()\n          query.exec((res) => {\n            if (res && res[0] && res[0].length > 0) {\n              console.log('找到分享按钮，尝试模拟点击')\n              \n              // 由于无法直接模拟点击，我们可以通过其他方式提示用户\n              uni.showModal({\n                title: '分享提示',\n                content: '请点击\"分享\"按钮将信息分享给好友或群聊',\n                showCancel: false,\n            success: () => {\n                  // 用户确认后，保持分享引导层显示\n                  showShareGuideLayer.value = true\n                }\n              })\n            } else {\n              console.log('未找到分享按钮')\n                }\n        })\n      }, 500)\n      },\n      fail: (err) => {\n        console.error('显示分享菜单失败', err)\n    }\n  })\n  } else {\n    // 如果API不可用，显示分享引导层\n    showShareGuideLayer.value = true\n  }\n}\n\nconst previewImage = (index) => {\n      uni.previewImage({\n        current: index,\n    urls: post.value.images\n  })\n}\n\nconst goBack = () => {\n  uni.navigateBack()\n}\n\nconst contactPublisher = () => {\n  // 假设发布者电话号码保存在post.publisher.phone中\n  const phone = post.value.publisher?.phone || '13800138000'; // 默认值为示例电话\n  \n  // 显示确认提示\n  uni.showModal({\n    title: '联系发布者',\n    content: `确定要拨打${phone}吗？\\n记得说在\"磁州生活网\"看到的哦~`,\n    confirmText: '立即拨打',\n    confirmColor: '#0052CC',\n    success: (res) => {\n      if (res.confirm) {\n        // 拨打电话\n        uni.makePhoneCall({\n          phoneNumber: phone,\n          success: () => {\n            console.log('拨打电话成功');\n          },\n          fail: (err) => {\n            console.error('拨打电话失败', err);\n            uni.showToast({\n              title: '拨打电话失败',\n              icon: 'none'\n            });\n          }\n        });\n      }\n    }\n  });\n}\n\n// 打开私信聊天\nconst openChat = () => {\n      uni.navigateTo({\n    url: `/pages/chat/index?userId=${post.value.publisher.id}&username=${encodeURIComponent(post.value.publisher.username)}`\n  });\n};\n\nconst submitComment = () => {\n  if (!commentText.value.trim()) {\n    uni.showToast({\n      title: '评论内容不能为空',\n      icon: 'none'\n    })\n    return\n  }\n  \n  // 模拟添加评论\n  comments.value.unshift({\n    id: Date.now(),\n    username: '当前用户',\n    avatar: 'https://via.placeholder.com/40?text=Me',\n    content: commentText.value,\n    createdAt: Date.now()\n  })\n  \n  commentText.value = ''\n  \n  uni.showToast({\n    title: '评论成功',\n    icon: 'success'\n  })\n}\n\nconst toggleLike = () => {\n  isLiked.value = !isLiked.value\n  uni.showToast({\n    title: isLiked.value ? '点赞成功' : '已取消点赞',\n    icon: 'none'\n  })\n}\n\nconst toggleCollect = () => {\n  isCollected.value = !isCollected.value\n  uni.showToast({\n    title: isCollected.value ? '收藏成功' : '已取消收藏',\n    icon: 'none'\n  })\n}\n\n// 跳转到首页\nconst goToHome = () => {\n  uni.switchTab({\n    url: '/pages/index/index'\n  });\n};\n\n// 打电话\nconst makePhoneCall = () => {\n  // 判断是否有联系电话\n  if (post.value.publisher && post.value.publisher.phone) {\n    uni.makePhoneCall({\n      phoneNumber: post.value.publisher.phone,\n      success: () => {\n        console.log('拨打电话成功');\n      },\n      fail: (err) => {\n        console.error('拨打电话失败:', err);\n        uni.showToast({\n          title: '拨打电话失败',\n          icon: 'none'\n        });\n      }\n    });\n  } else {\n    // 如果没有电话，提示用户\n    uni.showModal({\n      title: '提示',\n      content: '该信息暂无联系电话，请通过私信联系发布者',\n      showCancel: false\n    });\n  }\n};\n\n// 加载信息详情数据\nconst loadInfoData = (id) => {\n  // 判断是否是从发布页过来的临时数据\n  if (id && id.startsWith('temp_')) {\n    // 从本地缓存中获取数据\n    const publishDataList = uni.getStorageSync('publishDataList') || [];\n    const publishData = publishDataList.find(item => item.id === id);\n    \n    if (publishData) {\n      // 更新信息数据\n      updateInfoDataFromPublish(publishData);\n    } else {\n      uni.showToast({\n        title: '数据加载失败',\n        icon: 'none'\n      });\n    }\n  } else {\n    // 从服务器获取数据（这里使用模拟数据）\n    console.log('获取信息ID:', id);\n    // 保持默认的静态数据\n  }\n};\n\n// 根据发布数据更新信息详情\nconst updateInfoDataFromPublish = (publishData) => {\n  // 更新基本信息\n  post.value = {\n    ...post.value,\n    id: publishData.id,\n    title: publishData.title || '便民信息',\n    price: publishData.price || '价格待定',\n    publishTime: publishData.publishTime || Date.now(),\n    viewCount: Math.floor(Math.random() * 100) + 50, // 随机生成浏览量\n    location: publishData.location || '磁县城区',\n    description: publishData.introduction || publishData.description || post.value.description\n  };\n  \n  // 更新联系信息\n  if (publishData.contact && publishData.phone) {\n    post.value.contact = {\n      name: publishData.contact,\n      phone: publishData.phone\n    };\n    \n    // 同步更新发布者信息\n    post.value.publisher = {\n      id: `user_${Date.now()}`,\n      name: publishData.contact,\n      avatar: '/static/images/avatar.jpg',\n      rating: 4.5,\n      publishCount: Math.floor(Math.random() * 10) + 1\n    };\n  }\n  \n  // 更新图片\n  if (publishData.images && publishData.images.length > 0) {\n    post.value.images = publishData.images;\n  }\n};\n\n// 添加领取红包功能\nconst receiveRedPacket = () => {\n      uni.showModal({\n    title: '领取红包',\n    content: '确定要领取这个红包吗？需要完成转发或助力任务后才能获得。',\n        success: (res) => {\n          if (res.confirm) {\n        // 实际应用中应该调用API来领取红包\n        uni.showToast({\n          title: '领取成功，请完成任务',\n          icon: 'success'\n            });\n          }\n        }\n      });\n};\n\n// 添加直接触发分享的方法\nconst triggerDirectShare = () => {\n  console.log('尝试直接触发分享')\n    \n  // 微信小程序无法直接通过代码触发分享，但我们可以尝试以下方法\n  \n  // 1. 设置分享目标可见，并显示提示\n  uni.showShareMenu({\n    withShareTicket: true,\n    showShareItems: ['wechatFriends', 'wechatMoment'],\n    success: () => {\n      console.log('成功显示分享菜单')\n      \n      // 2. 尝试寻找页面中的分享按钮并模拟点击\n      setTimeout(() => {\n        // 这里尝试通过DOM模拟点击，但可能不是在所有平台都有效\n        // #ifdef H5\n        try {\n          const shareBtn = document.querySelector('#shareButton')\n          if (shareBtn) {\n            console.log('找到隐藏的分享按钮，尝试点击')\n            shareBtn.click()\n          }\n        } catch (e) {\n          console.error('尝试触发分享按钮失败', e)\n        }\n        // #endif\n        \n        // 3. 如果以上方法都失败，使用系统API尝试呼出分享菜单\n        // #ifdef MP-WEIXIN\n        try {\n          // 微信小程序分享API\n          wx.showShareMenu({\n            withShareTicket: true,\n            menus: ['shareAppMessage', 'shareTimeline'],\n            success: () => {\n              console.log('微信API成功显示分享菜单')\n              \n              // 除了显示分享菜单，还可以尝试弹出更明确的提示\n              wx.updateShareMenu({\n                withShareTicket: true,\n                success: () => {\n                  console.log('更新分享菜单成功')\n                  }\n                })\n            }\n          })\n        } catch (e) {\n          console.error('微信分享API调用失败', e)\n        }\n        // #endif\n        \n        // 4. 最后的备用方案：使用动画提示\n        uni.showToast({\n          title: '请点击右上角进行分享',\n          icon: 'none',\n          duration: 2000\n        })\n      }, 500)\n    }\n  })\n}\n\nconst form = reactive({\n  redPacket: null\n})\n</script>\n\n<style>\n/* 全局样式 */\n.post-detail-container {\n  min-height: 100vh;\n  background-color: #f5f7fa;\n  padding-bottom: 110rpx;\n  padding-top: 150rpx; /* 增加顶部内边距，为固定导航栏留出空间 */\n}\n\n.post-detail-wrapper {\n  max-width: 750rpx;\n  margin: 0 auto;\n  padding: 24rpx;\n  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */\n}\n\n/* 通用卡片样式 */\n.content-card {\n  background-color: #fff;\n  border-radius: 16rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);\n  margin-bottom: 30rpx;\n  padding: 30rpx;\n}\n\n.card-title {\n  font-size: 34rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20rpx;\n  display: block;\n}\n\n/* 信息主体卡片 */\n.main-info {\n  margin-top: 0; /* 调整顶部边距，因为现在使用系统导航栏 */\n}\n\n.post-header {\n  margin-bottom: 30rpx;\n}\n\n.post-title {\n  font-size: 38rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20rpx;\n  line-height: 1.4;\n}\n\n.post-meta {\n  display: flex;\n  flex-wrap: wrap;\n  color: #8c8c8c;\n  font-size: 26rpx;\n  gap: 20rpx;\n  align-items: center;\n}\n\n.post-category {\n  color: #0052CC;\n  background-color: rgba(0, 82, 204, 0.1);\n  padding: 4rpx 16rpx;\n  border-radius: 8rpx;\n}\n\n.post-gallery {\n  margin: 30rpx 0;\n  border-radius: 12rpx;\n  overflow: hidden;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.08);\n}\n\n.swiper {\n  height: 400rpx;\n  border-radius: 12rpx;\n}\n\n.carousel-image {\n  width: 100%;\n  height: 100%;\n  border-radius: 12rpx;\n}\n\n.single-image image {\n  width: 100%;\n  border-radius: 12rpx;\n}\n\n.divider {\n  height: 2rpx;\n  background-color: #f0f0f0;\n  margin: 30rpx 0;\n}\n\n.post-content {\n  font-size: 30rpx;\n  line-height: 1.8;\n  color: #444;\n  margin-bottom: 30rpx;\n}\n\n.post-tags {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16rpx;\n  margin-top: 20rpx;\n}\n\n.tag {\n  margin-right: 0;\n}\n\n/* 发布者信息卡片 */\n.publisher-header {\n  margin-bottom: 20rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n  padding-bottom: 20rpx;\n}\n\n.publisher-content {\n  display: flex;\n  align-items: center;\n}\n\n.publisher-avatar {\n  margin-right: 20rpx;\n}\n\n.avatar-image {\n  width: 100rpx;\n  height: 100rpx;\n  border-radius: 50%;\n}\n\n.publisher-details {\n  flex-grow: 1;\n}\n\n.publisher-name {\n  font-size: 32rpx;\n  font-weight: bold;\n  margin-bottom: 10rpx;\n  color: #333;\n}\n\n.publisher-stats {\n  display: flex;\n  gap: 40rpx;\n}\n\n.stat-item {\n  text-align: center;\n}\n\n.stat-value {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.stat-label {\n  font-size: 24rpx;\n  color: #8c8c8c;\n}\n\n.contact-btn {\n  padding: 10rpx 30rpx;\n  border-radius: 40rpx;\n}\n\n/* 位置信息卡片样式 */\n.location-info {\n  margin-bottom: 20rpx;\n}\n\n.location-content {\n  display: flex;\n  align-items: center;\n  padding: 15rpx 0;\n}\n\n.location-content .iconfont {\n  font-size: 32rpx;\n  color: #0052CC;\n  margin-right: 10rpx;\n}\n\n.map-preview {\n  width: 100%;\n  border-radius: 10rpx;\n  margin-top: 10rpx;\n}\n\n/* 评论区样式 */\n.comment-header {\n  margin-bottom: 20rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n  padding-bottom: 20rpx;\n}\n\n.comment-list {\n  margin-bottom: 30rpx;\n}\n\n.comment-empty {\n  text-align: center;\n  color: #999;\n  padding: 40rpx 0;\n}\n\n.comment-item {\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #f5f5f5;\n}\n\n.comment-user {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10rpx;\n}\n\n.comment-avatar {\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 50%;\n  margin-right: 15rpx;\n}\n\n.comment-user-info {\n  flex: 1;\n}\n\n.comment-username {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.comment-time {\n  font-size: 24rpx;\n  color: #999;\n  margin-left: 15rpx;\n}\n\n.comment-content {\n  font-size: 28rpx;\n  color: #444;\n  line-height: 1.5;\n  padding-left: 75rpx;\n}\n\n.comment-input-area {\n  display: flex;\n  align-items: center;\n  border-top: 1rpx solid #f0f0f0;\n  padding-top: 20rpx;\n}\n\n.comment-input {\n  flex: 1;\n  height: 70rpx;\n  background-color: #f5f5f5;\n  border-radius: 35rpx;\n  padding: 0 20rpx;\n  margin-right: 20rpx;\n}\n\n.comment-submit {\n  border-radius: 35rpx;\n}\n\n/* 适配样式 */\n@media screen and (max-width: 375px) {\n  .post-detail-wrapper {\n    padding: 0 20rpx;\n  }\n  \n  .post-title {\n    font-size: 34rpx;\n  }\n  \n  .content-card {\n    padding: 20rpx;\n  }\n  \n  .related-item {\n    width: 200rpx;\n  }\n}\n\n/* 底部互动工具栏 */\n.interaction-toolbar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background-color: #fff;\n  padding: 10rpx 10rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-top: 1rpx solid #f0f0f0;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  height: 120rpx;\n  z-index: 100;\n}\n\n.toolbar-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 6rpx 0;\n  margin: 0 4rpx;\n}\n\n.toolbar-icon {\n  width: 44rpx;\n  height: 44rpx;\n  margin-bottom: 6rpx;\n}\n\n.toolbar-text {\n  font-size: 22rpx;\n  color: #666;\n}\n\n.share-button {\n  background: transparent;\n  border: none;\n  margin: 0;\n  padding: 0;\n  line-height: normal;\n  border-radius: 0;\n  flex: 1;\n}\n\n.share-button::after {\n  display: none;\n}\n\n.call-button {\n  flex: 3; /* 占据更多空间，原来是2，现在改为3让按钮更长 */\n  background: linear-gradient(135deg, #0052CC, #0066FF);\n  height: 90rpx;\n  margin: 0 0 0 10rpx;\n  border-radius: 45rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n}\n\n.call-button-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n}\n\n.call-text {\n  color: #fff;\n  font-size: 30rpx;\n  font-weight: bold;\n  line-height: 1.2;\n}\n\n.call-subtitle {\n  color: rgba(255, 255, 255, 0.9);\n  font-size: 20rpx;\n  line-height: 1.2;\n}\n\n/* 隐藏的分享按钮 */\n.hidden-share-btn {\n  position: fixed;\n  width: 2rpx;\n  height: 2rpx;\n  opacity: 0;\n  top: -999rpx;\n  left: -999rpx;\n  z-index: -1;\n  overflow: hidden;\n  padding: 0;\n  margin: 0;\n  border: none;\n}\n\n.hidden-share-btn::after {\n  display: none;\n}\n\n/* 添加分享按钮高亮样式 */\n.share-button-highlight {\n  animation: shareButtonPulse 0.8s infinite alternate;\n  background-color: rgba(0, 102, 255, 0.1);\n  border-radius: 8rpx;\n}\n\n@keyframes shareButtonPulse {\n  from { transform: scale(1); }\n  to { transform: scale(1.1); background-color: rgba(0, 102, 255, 0.2); }\n}\n\n/* 分享引导浮层 */\n.share-guide-layer {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.7);\n  z-index: 999;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.guide-content {\n  background-color: #fff;\n  border-radius: 16rpx;\n  padding: 40rpx 30rpx;\n  width: 80%;\n  max-width: 600rpx;\n  text-align: center;\n  position: relative;\n}\n\n.guide-close {\n  position: absolute;\n  top: 10rpx;\n  right: 10rpx;\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 40rpx;\n  color: #333;\n  transition: transform 0.3s;\n}\n\n.guide-close:active {\n  transform: scale(0.9);\n}\n\n.guide-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20rpx;\n}\n\n.guide-desc {\n  font-size: 28rpx;\n  color: #666;\n  margin-bottom: 20rpx;\n}\n\n.guide-tips {\n  font-size: 26rpx;\n  color: #ff6600;\n  font-weight: bold;\n  margin-bottom: 30rpx;\n}\n\n/* 分享按钮样式 */\n.guide-share-btn {\n  background-color: #007AFF;\n  color: #FFFFFF !important;\n  font-size: 32rpx;\n  font-weight: bold;\n  border-radius: 40rpx;\n  padding: 20rpx 0;\n  width: 80%;\n  margin: 30rpx auto 10rpx;\n  box-shadow: 0 6rpx 15rpx rgba(0, 122, 255, 0.3);\n  border: none;\n  line-height: 1.5;\n}\n\n.guide-share-btn::after {\n  display: none;\n}\n\n/* 相关信息推荐样式 */\n.related-posts-card {\n  margin-top: 20rpx;\n  background-color: #fff;\n  border-radius: 16rpx;\n  overflow: hidden;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\n}\n\n.section-title {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 20rpx;\n  position: relative;\n  padding-left: 16rpx;\n}\n\n.section-title::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 6rpx;\n  width: 6rpx;\n  height: 28rpx;\n  background-color: #0052CC;\n  border-radius: 3rpx;\n}\n\n.related-posts-content {\n  padding: 0 16px 16px;\n  overflow: hidden;\n}\n\n/* 相关信息列表样式 */\n.related-posts-list {\n  margin-bottom: 12px;\n}\n\n.related-post-item {\n  padding: 12px 0;\n  border-bottom: 1px solid #f5f5f5;\n}\n\n.related-post-item:last-child {\n  border-bottom: none;\n}\n\n.post-item-content {\n  display: flex;\n  align-items: center;\n}\n\n.post-item-left {\n  margin-right: 12px;\n}\n\n.post-image {\n  width: 80px;\n  height: 60px;\n  border-radius: 8px;\n  background-color: #f5f7fa;\n}\n\n.post-item-middle {\n  flex: 1;\n  overflow: hidden;\n}\n\n.post-item-title {\n  font-size: 15px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 4px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.post-item-category {\n  font-size: 13px;\n  color: #0052CC;\n  background-color: rgba(0, 82, 204, 0.1);\n  padding: 2px 8px;\n  border-radius: 4px;\n  display: inline-block;\n  margin-bottom: 6px;\n}\n\n.post-item-meta {\n  display: flex;\n  font-size: 12px;\n  color: #999;\n}\n\n.post-item-time {\n  margin-right: 16px;\n}\n\n/* 查看更多按钮样式 */\n.view-more-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 44px;\n  background-color: #f7f9fc;\n  border-radius: 8px;\n  margin-top: 8px;\n}\n\n.view-more-text {\n  font-size: 14px;\n  color: #0052CC;\n}\n\n.view-more-icon {\n  margin-left: 4px;\n  font-size: 12px;\n  color: #0052CC;\n}\n\n/* 空数据提示样式 */\n.empty-related-posts {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 24px 0;\n}\n\n.empty-image {\n  width: 80px;\n  height: 80px;\n  margin-bottom: 12px;\n}\n\n.empty-text {\n  font-size: 14px;\n  color: #999;\n}\n\n/* 红包相关样式 */\n.red-packet-details {\n  background-color: #FFF5F5;\n  border-radius: 16rpx;\n  padding: 20rpx;\n  margin: 20rpx 0;\n}\n\n.red-packet-amount,\n.red-packet-usage,\n.red-packet-method {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10rpx;\n  padding: 10rpx 0;\n  border-bottom: 1rpx solid #FFE6E6;\n}\n\n.amount-label,\n.usage-label,\n.method-label {\n  color: #666;\n  font-size: 28rpx;\n}\n\n.amount-value,\n.usage-value,\n.method-value {\n  color: #FF4D4F;\n  font-size: 32rpx;\n  font-weight: bold;\n}\n\n.red-packet-receive {\n  flex: 1;\n  background-color: #FF4D4F;\n  color: white;\n  text-align: center;\n  padding: 20rpx;\n  font-size: 32rpx;\n  font-weight: bold;\n  border-radius: 8rpx;\n  margin-top: 10rpx;\n}\n\n.red-packet-card {\n  border-left: 6rpx solid #FF4D4F;\n}\n\n/* 自定义导航栏样式 */\n.custom-navbar {\n  background: linear-gradient(135deg, #0066FF, #0052CC);\n  height: 88rpx;\n  padding-top: 44px; /* 状态栏高度 */\n  display: flex;\n  align-items: center;\n  position: fixed; /* 改为固定定位 */\n  top: 0;\n  left: 0;\n  right: 0;\n  padding-bottom: 10rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n  z-index: 100; /* 提高z-index确保在最上层 */\n}\n\n.navbar-title {\n  font-size: 34rpx;\n  font-weight: bold;\n  color: #fff;\n  margin: 0 auto;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/publish/info-detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "onMounted", "wx", "reactive", "MiniProgramPage"], "mappings": ";;;;;;;;;;AA0OA,MAAM,iBAAiB,MAAW;;;;AAGlC,UAAM,aAAa,CAAC,cAAc;AAChC,YAAM,OAAO,IAAI,KAAK,SAAS;AAC/B,aAAO,GAAG,KAAK,aAAa,CAAC,IAAI,KAAK,SAAS;AAAA,IACjD;AAGA,UAAM,SAASA,cAAG,IAAC,EAAE;AACrB,UAAM,cAAcA,cAAG,IAAC,EAAE;AAC1B,UAAM,WAAWA,cAAG,IAAC,EAAE;AACPA,kBAAG,IAAC,KAAK;AACzB,UAAM,cAAcA,cAAG,IAAC,KAAK;AACAA,kBAAG,IAAC,KAAK;AACtC,UAAM,sBAAsBA,cAAG,IAAC,KAAK;AACrC,UAAM,OAAOA,cAAAA,IAAI;AAAA,MACf,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,WAAW,KAAK,IAAK,IAAG,OAAU,KAAK;AAAA,MACvC,OAAO;AAAA,MACP,MAAM,CAAC,OAAO,QAAQ,QAAQ,MAAM;AAAA,MAC9B,QAAQ;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,MACD,WAAW;AAAA,QACT,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,OAAO;AAAA,MACR;AAAA,MACD,UAAU;AAAA,QACR,SAAS;AAAA,QACT,UAAU;AAAA,QACV,WAAW;AAAA,MACZ;AAAA,IACH,CAAC;AAGD,UAAM,eAAeA,cAAAA,IAAI,CAAA,CAAE;AAG3B,UAAM,mBAAmB,MAAM;AAK7B,iBAAW,MAAM;AACf,qBAAa,QAAQ;AAAA,UACnB;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,UAAU;AAAA,YACV,WAAW,KAAK,IAAK,IAAG,QAAW;AAAA,YACnC,OAAO;AAAA,YACP,OAAO;AAAA,UACR;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,UAAU;AAAA,YACV,WAAW,KAAK,IAAK,IAAG,QAAW;AAAA,YACnC,OAAO;AAAA,YACP,OAAO;AAAA,UACR;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,UAAU;AAAA,YACV,WAAW,KAAK,IAAK,IAAG,QAAW;AAAA,YACnC,OAAO;AAAA,YACP,OAAO;AAAA,UACR;AAAA,QACP;AAAA,MACG,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,uBAAuB,CAAC,OAAO;AAEnC,UAAI,OAAO,OAAO,OAAO;AACvB;AAAA,MACD;AAEDC,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,iCAAiC,EAAE;AAAA,MAC5C,CAAG;AAAA,IACH;AAGA,UAAM,qBAAqB,CAAC,MAAM;;AAChC,UAAI;AAAG,UAAE;AACT,YAAM,iBAAe,UAAK,MAAM,SAAX,mBAAkB,OAAM;AAC7CA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,qDAAqD,mBAAmB,MAAM,CAAC,aAAa,mBAAmB,YAAY,CAAC;AAAA,MACrI,CAAG;AAAA,IACH;AAGAC,kBAAAA,UAAU,MAAM;;AAEdD,oBAAAA,MAAI,sBAAsB;AAAA,QACxB,OAAO;AAAA,MACX,CAAG;AAEDA,oBAAAA,MAAI,sBAAsB;AAAA,QACxB,YAAY;AAAA,QACZ,iBAAiB;AAAA,MACrB,CAAG;AAGD,YAAM,QAAQ,gBAAiB;AAC/B,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,YAAU,iBAAY,UAAZ,mBAAmB,YAAW,CAAE;AAChD,aAAO,QAAQ,QAAQ,MAAM;AAC7B,WAAK,MAAM,KAAK,OAAO;AACvBA,oBAAY,MAAA,MAAA,OAAA,wCAAA,WAAW,OAAO,KAAK;AAGnC,uBAAkB;AAGlB,YAAM,oBAAoB,QAAQ,gBAAgB,OAAOA,cAAAA,MAAI,eAAe,aAAa,MAAM;AAG/F,UAAIA,cAAG,MAAC,eAAe,aAAa,GAAG;AACrCA,sBAAG,MAAC,kBAAkB,aAAa;AAAA,MACpC;AAGD,UAAI,QAAQ,cAAc,QAAQ;AAChCA,sBAAAA,MAAA,MAAA,OAAA,wCAAY,yBAAyB;AAGrC,mBAAW,MAAM;AAEf,8BAAoB,QAAQ;AAAA,QAC7B,GAAE,GAAG;AAAA,MACP;AAGD,UAAI,mBAAmB;AACrBA,sBAAAA,MAAA,MAAA,OAAA,wCAAY,kBAAkB;AAG9B,mBAAW,MAAM;AAEf,6BAAoB;AAAA,QACrB,GAAE,GAAI;AAAA,MACR;AAGD,eAAS,QAAQ;AAAA,QACf;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,WAAW,KAAK,IAAK,IAAG,OAAU;AAAA,QACnC;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,WAAW,KAAK,IAAK,IAAG,OAAU;AAAA,QACnC;AAAA,MACF;AAGD,YAAM,EAAE,cAAc,iBAAiB,eAAe,gBAAgB,gBAAiB,IAAG;AAC1F,UAAI,iBAAiB,OAAO,iBAAiB;AAC3C,aAAK,MAAM,YAAY;AAAA,UACrB,QAAQ;AAAA,UACR,OAAO,kBAAkB;AAAA,UACzB,QAAQ,mBAAmB;AAAA,UAC3B,MAAM,iBAAiB;AAAA,UACvB,UAAU;AAAA;AAAA,QAChB;AACIA,sBAAAA,MAAY,MAAA,OAAA,wCAAA,WAAW,KAAK,UAAU,KAAK,MAAM,SAAS,CAAC;AAAA,MAC5D;AAAA,IACH,CAAC;AA+CD,UAAM,eAAe,CAAC,UAAU;AAC1BA,oBAAAA,MAAI,aAAa;AAAA,QACf,SAAS;AAAA,QACb,MAAM,KAAK,MAAM;AAAA,MACrB,CAAG;AAAA,IACH;AAMA,UAAM,mBAAmB,MAAM;;AAE7B,YAAM,UAAQ,UAAK,MAAM,cAAX,mBAAsB,UAAS;AAG7CA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,QAAQ,KAAK;AAAA;AAAA,QACtB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEfA,0BAAAA,MAAI,cAAc;AAAA,cAChB,aAAa;AAAA,cACb,SAAS,MAAM;AACbA,8BAAAA,MAAY,MAAA,OAAA,wCAAA,QAAQ;AAAA,cACrB;AAAA,cACD,MAAM,CAAC,QAAQ;AACbA,8BAAc,MAAA,MAAA,SAAA,wCAAA,UAAU,GAAG;AAC3BA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACpB,CAAa;AAAA,cACF;AAAA,YACX,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACjBA,oBAAAA,MAAI,WAAW;AAAA,QACjB,KAAK,4BAA4B,KAAK,MAAM,UAAU,EAAE,aAAa,mBAAmB,KAAK,MAAM,UAAU,QAAQ,CAAC;AAAA,MAC1H,CAAG;AAAA,IACH;AAEA,UAAM,gBAAgB,MAAM;AAC1B,UAAI,CAAC,YAAY,MAAM,QAAQ;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAGD,eAAS,MAAM,QAAQ;AAAA,QACrB,IAAI,KAAK,IAAK;AAAA,QACd,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,SAAS,YAAY;AAAA,QACrB,WAAW,KAAK,IAAK;AAAA,MACzB,CAAG;AAED,kBAAY,QAAQ;AAEpBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAUA,UAAM,gBAAgB,MAAM;AAC1B,kBAAY,QAAQ,CAAC,YAAY;AACjCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,YAAY,QAAQ,SAAS;AAAA,QACpC,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,MAAM;AAE1B,UAAI,KAAK,MAAM,aAAa,KAAK,MAAM,UAAU,OAAO;AACtDA,sBAAAA,MAAI,cAAc;AAAA,UAChB,aAAa,KAAK,MAAM,UAAU;AAAA,UAClC,SAAS,MAAM;AACbA,0BAAAA,MAAA,MAAA,OAAA,wCAAY,QAAQ;AAAA,UACrB;AAAA,UACD,MAAM,CAAC,QAAQ;AACbA,0BAAc,MAAA,MAAA,SAAA,wCAAA,WAAW,GAAG;AAC5BA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACP,CAAK;AAAA,MACL,OAAS;AAELA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,QAClB,CAAK;AAAA,MACF;AAAA,IACH;AAgEA,UAAM,mBAAmB,MAAM;AACzBA,oBAAAA,MAAI,UAAU;AAAA,QAChB,OAAO;AAAA,QACP,SAAS;AAAA,QACL,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEnBA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAa;AAAA,UACF;AAAA,QACF;AAAA,MACT,CAAO;AAAA,IACP;AAGA,UAAM,qBAAqB,MAAM;AAC/BA,oBAAAA,MAAA,MAAA,OAAA,wCAAY,UAAU;AAKtBA,oBAAAA,MAAI,cAAc;AAAA,QAChB,iBAAiB;AAAA,QACjB,gBAAgB,CAAC,iBAAiB,cAAc;AAAA,QAChD,SAAS,MAAM;AACbA,wBAAAA,2DAAY,UAAU;AAGtB,qBAAW,MAAM;AAgBf,gBAAI;AAEFE,4BAAAA,KAAG,cAAc;AAAA,gBACf,iBAAiB;AAAA,gBACjB,OAAO,CAAC,mBAAmB,eAAe;AAAA,gBAC1C,SAAS,MAAM;AACbF,gCAAAA,MAAA,MAAA,OAAA,wCAAY,eAAe;AAG3BE,gCAAAA,KAAG,gBAAgB;AAAA,oBACjB,iBAAiB;AAAA,oBACjB,SAAS,MAAM;AACbF,oCAAAA,MAAA,MAAA,OAAA,wCAAY,UAAU;AAAA,oBACrB;AAAA,kBACnB,CAAiB;AAAA,gBACJ;AAAA,cACb,CAAW;AAAA,YACF,SAAQ,GAAG;AACVA,4BAAAA,MAAc,MAAA,SAAA,wCAAA,eAAe,CAAC;AAAA,YAC/B;AAIDA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YACpB,CAAS;AAAA,UACF,GAAE,GAAG;AAAA,QACP;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,OAAOG,cAAAA,SAAS;AAAA,MACpB,WAAW;AAAA,IACb,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/tBD,GAAG,WAAWC,SAAe;"}