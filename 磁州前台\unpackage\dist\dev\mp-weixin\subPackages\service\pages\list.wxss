/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-09770677, html.data-v-09770677, #app.data-v-09770677, .index-container.data-v-09770677 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 全局样式 */
.service-list-container.data-v-09770677 {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f6f8fa;
}
.service-list-container.premium-style.data-v-09770677 {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", "Helvetica Neue", Helvetica, Arial, sans-serif;
}

/* 固定头部区域 */
.custom-navbar.data-v-09770677,
.search-container.data-v-09770677,
.category-tabs.data-v-09770677,
.subcategory-tabs.data-v-09770677,
.filter-container.data-v-09770677 {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}

/* 自定义导航栏 - 最顶层 */
.custom-navbar.data-v-09770677 {
  display: flex;
  align-items: center;
  height: 44px;
  background: linear-gradient(120deg, #0070f3, #00a1ff);
  padding: 0 16px;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 105;
  box-shadow: 0 1px 12px rgba(0, 112, 243, 0.18);
}

/* 搜索框 - 第二层 */
.search-container.data-v-09770677 {
  padding: 12px 16px 16px;
  background: #fff;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  margin-bottom: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: -webkit-sticky;
  position: sticky;
  top: 44px;
  z-index: 104;
}

/* 一级分类标签栏 - 第三层 */
.category-tabs.data-v-09770677 {
  background: linear-gradient(to right, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.98));
  padding: 14px 0 12px;
  white-space: nowrap;
  border-radius: 16px;
  margin: 0 12px 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  position: -webkit-sticky;
  position: sticky;
  top: 112px;
  /* 导航栏高度 + 搜索框高度 */
  z-index: 103;
}

/* 二级分类标签栏 - 第四层 */
.subcategory-tabs.data-v-09770677 {
  background: rgba(255, 255, 255, 0.95);
  padding: 10px 0;
  white-space: nowrap;
  border-radius: 16px;
  margin: 0 12px 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  position: -webkit-sticky;
  position: sticky;
  top: 172px;
  /* 导航栏高度 + 搜索框高度 + 一级分类高度 */
  z-index: 102;
}

/* 筛选栏 - 第五层 */
.filter-container.data-v-09770677 {
  display: flex;
  height: 54px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  position: -webkit-sticky;
  position: sticky;
  top: 226px;
  /* 导航栏高度 + 搜索框高度 + 一级分类高度 + 二级分类高度 */
  justify-content: center;
  padding: 0;
  z-index: 101;
  margin: 0 12px 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

/* 列表内容区域 - 需要添加足够的上边距以避免被固定头部遮挡 */
.service-scroll.data-v-09770677 {
  flex: 1;
  box-sizing: border-box;
  padding: 0 12px;
  margin-top: 12px;
  /* 添加一些顶部边距 */
}
.back-btn.data-v-09770677 {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  z-index: 2;
}
.back-icon.data-v-09770677 {
  width: 12px;
  height: 12px;
  border-top: 2px solid #fff;
  border-left: 2px solid #fff;
  transform: rotate(-45deg);
  transition: transform 0.2s ease;
}
.back-btn:active .back-icon.data-v-09770677 {
  transform: rotate(-45deg) scale(0.9);
}
.navbar-title.data-v-09770677 {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: -0.2px;
}
.navbar-right.data-v-09770677 {
  width: 36px;
  position: relative;
  z-index: 2;
}
.search-box.data-v-09770677 {
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 12px;
  padding: 0 12px;
  height: 40px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
}
.search-icon.data-v-09770677 {
  width: 18px;
  height: 18px;
  margin-right: 8px;
  opacity: 0.6;
}
.search-input.data-v-09770677 {
  flex: 1;
  height: 40px;
  font-size: 15px;
  color: #333;
  background: transparent;
}
.search-cancel.data-v-09770677 {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #999;
  border-radius: 50%;
  background-color: #f0f0f0;
}
.tab-item.data-v-09770677 {
  display: inline-block;
  padding: 8px 20px;
  margin: 0 5px;
  font-size: 14px;
  color: #555;
  border-radius: 20px;
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  position: relative;
}
.tab-item.data-v-09770677:first-child {
  margin-left: 16px;
}
.tab-item.data-v-09770677:last-child {
  margin-right: 16px;
}
.tab-item.active.data-v-09770677 {
  background: linear-gradient(135deg, #0070f3, #00a1ff);
  color: #fff;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.25);
}
.subtab-item.data-v-09770677 {
  display: inline-block;
  padding: 6px 16px;
  margin: 0 5px;
  font-size: 13px;
  color: #666;
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  background-color: #f1f2f6;
}
.subtab-item.data-v-09770677:first-child {
  margin-left: 16px;
}
.subtab-item.data-v-09770677:last-child {
  margin-right: 16px;
}
.subtab-item.active.data-v-09770677 {
  background: linear-gradient(135deg, #0070f3, #00a1ff);
  color: #fff;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.25);
}
.filter-wrapper.data-v-09770677 {
  display: flex;
  width: 100%;
  height: 54px;
  border-radius: 16px;
  overflow: hidden;
}
.filter-item.data-v-09770677 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 100%;
  font-size: 15px;
  color: #333;
  font-weight: 500;
  transition: background-color 0.2s ease;
}
.filter-item.data-v-09770677:active {
  background-color: rgba(0, 0, 0, 0.02);
}
.filter-item.data-v-09770677:not(:last-child)::after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 24px;
  background-color: rgba(0, 0, 0, 0.05);
}
.active-filter.data-v-09770677 {
  color: #0070f3;
  font-weight: 600;
}
.filter-arrow.data-v-09770677 {
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #999;
  margin-left: 8px;
  transition: transform 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}
.arrow-up.data-v-09770677 {
  transform: rotate(180deg);
  border-top-color: #0070f3;
}

/* 高级筛选下拉菜单 */
.filter-dropdown.data-v-09770677 {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.98);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  z-index: 99;
  max-height: 60vh;
  animation: dropDown-09770677 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  border-radius: 16px;
  overflow: hidden;
  margin-top: 8px;
}
@keyframes dropDown-09770677 {
from {
    opacity: 0;
    transform: translateY(-12px);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
.area-dropdown.data-v-09770677 {
  left: 16px;
  width: calc(50% - 24px);
}
.sort-dropdown.data-v-09770677 {
  right: 16px;
  width: calc(50% - 24px);
}
.dropdown-scroll.data-v-09770677 {
  max-height: 50vh;
}
.dropdown-item.data-v-09770677 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  font-size: 15px;
  color: #333;
  border-bottom: 1px solid rgba(0, 0, 0, 0.03);
  transition: all 0.2s;
}
.dropdown-item.data-v-09770677:active {
  background-color: rgba(0, 0, 0, 0.02);
}
.dropdown-item.active-item.data-v-09770677 {
  color: #0070f3;
  font-weight: 600;
  background-color: rgba(0, 112, 243, 0.05);
}
.dropdown-item-text.data-v-09770677 {
  flex: 1;
}
.dropdown-item-check.data-v-09770677 {
  color: #0070f3;
  font-weight: bold;
  margin-left: 8px;
}

/* 遮罩层 */
.filter-mask.data-v-09770677 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 90;
  -webkit-backdrop-filter: blur(2px);
          backdrop-filter: blur(2px);
  animation: fadeIn-09770677 0.3s ease;
}
@keyframes fadeIn-09770677 {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.service-list.data-v-09770677 {
  padding-bottom: 16px;
}
.service-item.data-v-09770677 {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
  border-radius: 18px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  border: 1px solid rgba(255, 255, 255, 0.8);
}
.service-item-hover.data-v-09770677 {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}
.service-content.data-v-09770677 {
  padding: 18px;
}
.service-header.data-v-09770677 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}
.service-header-left.data-v-09770677 {
  display: flex;
  align-items: center;
}
.service-meta-right.data-v-09770677 {
  display: flex;
  align-items: center;
}
.service-tag.data-v-09770677 {
  background: linear-gradient(135deg, rgba(0, 112, 243, 0.1), rgba(0, 161, 255, 0.15));
  color: #0070f3;
  font-size: 13px;
  padding: 4px 12px;
  border-radius: 8px;
  font-weight: 600;
  letter-spacing: -0.2px;
}
.service-subcategory.data-v-09770677 {
  background-color: rgba(0, 112, 243, 0.06);
  color: #0070f3;
  font-size: 13px;
  padding: 4px 12px;
  border-radius: 8px;
  margin-left: 8px;
  letter-spacing: -0.2px;
}
.service-area.data-v-09770677 {
  background-color: #f1f2f6;
  color: #666;
  font-size: 13px;
  padding: 4px 12px;
  border-radius: 8px;
}
.service-main.data-v-09770677 {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.service-title-wrapper.data-v-09770677 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.service-title.data-v-09770677 {
  font-size: 17px;
  color: #222;
  line-height: 1.5;
  font-weight: 500;
  letter-spacing: -0.2px;
  flex: 1;
}
.service-price.data-v-09770677 {
  color: #ff3b30;
  font-weight: 600;
  font-size: 17px;
  margin-left: 12px;
}
.service-images.data-v-09770677 {
  display: flex;
  gap: 8px;
  margin: 4px 0;
}
.service-image.data-v-09770677 {
  width: 32%;
  height: 100px;
  border-radius: 12px;
  background-color: #f5f5f5;
  object-fit: cover;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}
.service-image.single-image.data-v-09770677 {
  width: 100%;
  height: 160px;
}
.service-footer.data-v-09770677 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 4px;
}
.service-meta.data-v-09770677 {
  display: flex;
  align-items: center;
  gap: 12px;
}
.meta-tag.data-v-09770677 {
  background-color: rgba(0, 0, 0, 0.04);
  padding: 4px 12px;
  border-radius: 8px;
  display: flex;
  align-items: center;
}
.meta-views.data-v-09770677 {
  font-size: 13px;
  color: #666;
}
.meta-time.data-v-09770677 {
  font-size: 13px;
  color: #999;
}
.service-actions.data-v-09770677 {
  display: flex;
}
.action-btn.data-v-09770677 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 20px;
  background: linear-gradient(135deg, #0070f3, #00a1ff);
  border-radius: 24px;
  font-size: 14px;
  color: #fff;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.25);
  transition: all 0.2s ease;
}
.action-btn.data-v-09770677:active {
  transform: scale(0.96);
  box-shadow: 0 2px 8px rgba(0, 112, 243, 0.2);
}
.contact-btn.data-v-09770677 {
  letter-spacing: 1px;
}

/* 高级空状态 */
.empty-state.data-v-09770677 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 0;
}
.empty-image.data-v-09770677 {
  width: 140px;
  height: 140px;
  margin-bottom: 24px;
  opacity: 0.8;
}
.empty-text.data-v-09770677 {
  color: #333;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  letter-spacing: -0.3px;
}
.empty-subtext.data-v-09770677 {
  color: #999;
  font-size: 15px;
  margin-bottom: 24px;
}
.empty-btn.data-v-09770677 {
  padding: 10px 24px;
  background: linear-gradient(135deg, #0070f3, #00a1ff);
  color: #fff;
  font-size: 15px;
  font-weight: 500;
  border-radius: 24px;
  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.25);
}

/* 加载更多状态 */
.loading-more.data-v-09770677 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  gap: 8px;
}
.loading-indicator.data-v-09770677 {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(0, 112, 243, 0.3);
  border-top: 2px solid #0070f3;
  border-radius: 50%;
  animation: spin-09770677 0.8s linear infinite;
}
@keyframes spin-09770677 {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.loading-text.data-v-09770677 {
  color: #777;
  font-size: 14px;
}
.loading-done.data-v-09770677 {
  text-align: center;
  padding: 20px 0;
}
.loading-done-text.data-v-09770677 {
  color: #999;
  font-size: 14px;
}

/* 高级悬浮发布按钮 */
.publish-btn.data-v-09770677 {
  position: fixed;
  right: 20px;
  bottom: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 24px 0 22px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(135deg, #0070f3, #00a1ff);
  box-shadow: 0 6px 16px rgba(0, 112, 243, 0.3);
  z-index: 90;
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}
.publish-btn-hover.data-v-09770677 {
  transform: scale(0.95);
  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.25);
}
.publish-icon.data-v-09770677 {
  color: #fff;
  font-size: 24px;
  font-weight: 300;
  line-height: 1;
  margin-right: 6px;
}
.publish-text.data-v-09770677 {
  color: #fff;
  font-size: 16px;
  font-weight: 500;
}