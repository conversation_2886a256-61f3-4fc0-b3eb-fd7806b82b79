"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
const _sfc_main = {
  __name: "channels",
  setup(__props) {
    const dateRange = common_vendor.ref("2023-04-01 ~ 2023-04-30");
    const channelStats = common_vendor.reactive({
      totalChannels: 248,
      channelsTrend: "up",
      channelsGrowth: "12%",
      activeChannels: 186,
      activeTrend: "up",
      activeGrowth: "8%",
      totalCommission: 15820.5,
      commissionTrend: "up",
      commissionGrowth: "15%",
      totalOrders: 1245,
      ordersTrend: "down",
      ordersGrowth: "3%"
    });
    const formatNumber = (num) => {
      return num.toLocaleString("zh-CN", { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    };
    const showDatePicker = () => {
      common_vendor.index.showToast({
        title: "日期筛选功能开发中",
        icon: "none"
      });
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const addChannel = () => {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/distribution/channels/add"
      });
    };
    const filterTabs = common_vendor.ref([
      { name: "全部", value: "all" },
      { name: "活跃", value: "active" },
      { name: "新增", value: "new" },
      { name: "休眠", value: "inactive" }
    ]);
    const currentTab = common_vendor.ref("all");
    const searchKeyword = common_vendor.ref("");
    const channels = common_vendor.reactive([
      {
        id: "1001",
        name: "张小明",
        avatar: "/static/images/avatars/avatar1.png",
        status: "online",
        level: 3,
        levelName: "钻石会员",
        orders: 89,
        commission: "4,526.50",
        fans: 245,
        joinTime: "2023-03-15"
      },
      {
        id: "1002",
        name: "王丽丽",
        avatar: "/static/images/avatars/avatar2.png",
        status: "online",
        level: 2,
        levelName: "黄金会员",
        orders: 56,
        commission: "2,830.80",
        fans: 128,
        joinTime: "2023-03-18"
      },
      {
        id: "1003",
        name: "李大壮",
        avatar: "/static/images/avatars/avatar3.png",
        status: "offline",
        level: 1,
        levelName: "白银会员",
        orders: 34,
        commission: "1,254.30",
        fans: 76,
        joinTime: "2023-03-22"
      },
      {
        id: "1004",
        name: "赵小红",
        avatar: "/static/images/avatars/avatar4.png",
        status: "online",
        level: 2,
        levelName: "黄金会员",
        orders: 42,
        commission: "2,102.60",
        fans: 94,
        joinTime: "2023-03-25"
      },
      {
        id: "1005",
        name: "刘伟",
        avatar: "/static/images/avatars/avatar5.png",
        status: "offline",
        level: 1,
        levelName: "白银会员",
        orders: 28,
        commission: "986.40",
        fans: 52,
        joinTime: "2023-04-01"
      }
    ]);
    const filteredChannels = common_vendor.computed(() => {
      let result = [...channels];
      if (currentTab.value !== "all") {
        if (currentTab.value === "active") {
          result = result.filter((item) => item.status === "online");
        } else if (currentTab.value === "inactive") {
          result = result.filter((item) => item.status === "offline");
        } else if (currentTab.value === "new") {
          const twoWeeksAgo = /* @__PURE__ */ new Date();
          twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);
          result = result.filter((item) => {
            const joinDate = new Date(item.joinTime);
            return joinDate >= twoWeeksAgo;
          });
        }
      }
      if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase();
        result = result.filter(
          (item) => item.name.toLowerCase().includes(keyword) || item.id.includes(keyword)
        );
      }
      return result;
    });
    const switchTab = (tab) => {
      currentTab.value = tab;
    };
    const handleSearch = () => {
    };
    const viewChannelDetail = (channel) => {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/distribution/channels/detail?id=${channel.id}`
      });
    };
    const viewQrcode = (channel) => {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/distribution/qrcode/index?channelId=${channel.id}&channelName=${encodeURIComponent(channel.name)}`
      });
    };
    const editChannel = (channel) => {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/distribution/channels/edit?id=${channel.id}`
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(goBack),
        b: common_vendor.p({
          d: "M12 5V19M5 12H19",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        c: common_vendor.p({
          width: "20",
          height: "20",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        d: common_vendor.o(addChannel),
        e: common_vendor.t(dateRange.value),
        f: common_vendor.p({
          d: "M6 9L12 15L18 9",
          stroke: "#6B0FBE",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        g: common_vendor.p({
          width: "12",
          height: "12",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        h: common_vendor.o(showDatePicker),
        i: common_vendor.t(channelStats.totalChannels),
        j: common_vendor.t(channelStats.channelsGrowth),
        k: common_vendor.n(channelStats.channelsTrend),
        l: common_vendor.p({
          d: "M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21",
          stroke: "#6B0FBE",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        m: common_vendor.p({
          d: "M9 11C11.2091 11 13 9.20914 13 7C13 4.79086 11.2091 3 9 3C6.79086 3 5 4.79086 5 7C5 9.20914 6.79086 11 9 11Z",
          stroke: "#6B0FBE",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        n: common_vendor.p({
          d: "M23 21V19C22.9993 18.1137 22.7044 17.2528 22.1614 16.5523C21.6184 15.8519 20.8581 15.3516 20 15.13",
          stroke: "#6B0FBE",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        o: common_vendor.p({
          d: "M16 3.13C16.8604 3.35031 17.623 3.85071 18.1676 4.55232C18.7122 5.25392 19.0078 6.11683 19.0078 7.005C19.0078 7.89318 18.7122 8.75608 18.1676 9.45769C17.623 10.1593 16.8604 10.6597 16 10.88",
          stroke: "#6B0FBE",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        p: common_vendor.p({
          width: "24",
          height: "24",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        q: common_vendor.t(channelStats.activeChannels),
        r: common_vendor.t(channelStats.activeGrowth),
        s: common_vendor.n(channelStats.activeTrend),
        t: common_vendor.p({
          d: "M16 21V19C16 17.9391 15.5786 16.9217 14.8284 16.1716C14.0783 15.4214 13.0609 15 12 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21",
          stroke: "#34C759",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        v: common_vendor.p({
          d: "M8.5 11C10.7091 11 12.5 9.20914 12.5 7C12.5 4.79086 10.7091 3 8.5 3C6.29086 3 4.5 4.79086 4.5 7C4.5 9.20914 6.29086 11 8.5 11Z",
          stroke: "#34C759",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        w: common_vendor.p({
          d: "M20 8V14",
          stroke: "#34C759",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        x: common_vendor.p({
          d: "M23 11H17",
          stroke: "#34C759",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        y: common_vendor.p({
          width: "24",
          height: "24",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        z: common_vendor.t(formatNumber(channelStats.totalCommission)),
        A: common_vendor.t(channelStats.commissionGrowth),
        B: common_vendor.n(channelStats.commissionTrend),
        C: common_vendor.p({
          d: "M12 1V23M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",
          stroke: "#FF9500",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        D: common_vendor.p({
          width: "24",
          height: "24",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        E: common_vendor.t(channelStats.totalOrders),
        F: common_vendor.t(channelStats.ordersGrowth),
        G: common_vendor.n(channelStats.ordersTrend),
        H: common_vendor.p({
          d: "M9 17H15M9 13H15M9 9H15M5 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3H5C3.89543 3 3 3.89543 3 5V19C3 20.1046 3.89543 21 5 21Z",
          stroke: "#007AFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        I: common_vendor.p({
          width: "24",
          height: "24",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        J: common_vendor.p({
          d: "M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z",
          stroke: "#999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        K: common_vendor.p({
          d: "M21 21L16.65 16.65",
          stroke: "#999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        L: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        M: common_vendor.o([($event) => searchKeyword.value = $event.detail.value, handleSearch]),
        N: searchKeyword.value,
        O: common_vendor.f(filterTabs.value, (tab, index, i0) => {
          return {
            a: common_vendor.t(tab.name),
            b: index,
            c: currentTab.value === tab.value ? 1 : "",
            d: common_vendor.o(($event) => switchTab(tab.value), index)
          };
        }),
        P: common_vendor.f(filteredChannels.value, (channel, index, i0) => {
          return {
            a: channel.avatar,
            b: common_vendor.n(channel.status),
            c: common_vendor.t(channel.name),
            d: common_vendor.t(channel.levelName),
            e: common_vendor.n("level-" + channel.level),
            f: common_vendor.t(channel.orders),
            g: common_vendor.t(channel.commission),
            h: common_vendor.t(channel.fans),
            i: "44e3ee12-22-" + i0 + "," + ("44e3ee12-21-" + i0),
            j: "44e3ee12-21-" + i0,
            k: common_vendor.o(($event) => viewQrcode(channel), index),
            l: "44e3ee12-24-" + i0 + "," + ("44e3ee12-23-" + i0),
            m: "44e3ee12-25-" + i0 + "," + ("44e3ee12-23-" + i0),
            n: "44e3ee12-23-" + i0,
            o: common_vendor.o(($event) => editChannel(channel), index),
            p: index,
            q: common_vendor.o(($event) => viewChannelDetail(channel), index)
          };
        }),
        Q: common_vendor.p({
          d: "M3 11V3H11V11H3ZM3 21V13H11V21H3ZM13 11V3H21V11H13ZM13 21V13H21V21H13ZM5 9H9V5H5V9ZM15 9H19V5H15V9ZM5 19H9V15H5V19ZM15 19H19V15H15V19Z",
          stroke: "#6B0FBE",
          ["stroke-width"]: "1.5",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        R: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        S: common_vendor.p({
          d: "M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13",
          stroke: "#6B0FBE",
          ["stroke-width"]: "1.5",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        T: common_vendor.p({
          d: "M18.5 2.5C18.8978 2.10217 19.4374 1.87868 20 1.87868C20.5626 1.87868 21.1022 2.10217 21.5 2.5C21.8978 2.89782 22.1213 3.43739 22.1213 4C22.1213 4.56261 21.8978 5.10217 21.5 5.5L12 15L8 16L9 12L18.5 2.5Z",
          stroke: "#6B0FBE",
          ["stroke-width"]: "1.5",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        U: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        V: filteredChannels.value.length === 0
      }, filteredChannels.value.length === 0 ? {
        W: common_assets._imports_1$40,
        X: common_vendor.o(addChannel)
      } : {}, {
        Y: common_vendor.p({
          d: "M12 5V19M5 12H19",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        Z: common_vendor.p({
          width: "24",
          height: "24",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        aa: common_vendor.o(addChannel)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/distribution/channels.js.map
