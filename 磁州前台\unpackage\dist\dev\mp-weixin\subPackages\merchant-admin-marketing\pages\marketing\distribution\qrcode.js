"use strict";
const common_vendor = require("../../../../../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_line = common_vendor.resolveComponent("line");
  (_component_path + _component_svg + _component_circle + _component_line)();
}
const _sfc_main = {
  __name: "qrcode",
  setup(__props) {
    const promotionParams = common_vendor.reactive({
      type: "product",
      // 默认类型
      id: "",
      title: "",
      image: "",
      extraParams: {}
      // 存储额外参数
    });
    const storeInfo = common_vendor.reactive({
      name: "磁州同城生活",
      slogan: "本地生活好物优选",
      logo: "/static/images/logo.png"
    });
    const distributorId = common_vendor.ref("D88652");
    const qrcodeTypes = [
      {
        name: "店铺",
        value: "store",
        icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M9 22V12H15V22" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
      },
      {
        name: "商品",
        value: "product",
        icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20 7L12 3L4 7M20 7V17L12 21M20 7L12 11M12 21L4 17V7M12 21V11M4 7L12 11" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
      },
      {
        name: "活动",
        value: "activity",
        icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
      },
      {
        name: "会员",
        value: "member",
        icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
      }
    ];
    const dynamicQrcodeTypes = {
      "carpool": {
        name: "拼车",
        value: "carpool",
        icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M7 17H2V12H7M17 17H22V12H17M14 5H10L8 10H16L14 5ZM5 11V17H8V19H16V17H19V11L16 4H8L5 11Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
      },
      "secondhand": {
        name: "二手",
        value: "secondhand",
        icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M16 4H8V12H16V4ZM16 16H8V20H16V16ZM4 20H6V4H4V20ZM18 4V20H20V4H18Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
      },
      "house": {
        name: "房屋",
        value: "house",
        icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M9 22V12H15V22" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
      },
      "service": {
        name: "服务",
        value: "service",
        icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
      },
      "community": {
        name: "社区",
        value: "community",
        icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21M23 21V19C22.9993 18.1137 22.7044 17.2528 22.1614 16.5523C21.6184 15.8519 20.8581 15.3516 20 15.13M16 3.13C16.8604 3.3503 17.623 3.8507 18.1676 4.55231C18.7122 5.25392 19.0078 6.11683 19.0078 7.005C19.0078 7.89317 18.7122 8.75608 18.1676 9.45769C17.623 10.1593 16.8604 10.6597 16 10.88M13 7C13 9.20914 11.2091 11 9 11C6.79086 11 5 9.20914 5 7C5 4.79086 6.79086 3 9 3C11.2091 3 13 4.79086 13 7Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
      }
    };
    const themeColors = [
      { bgColor: "#FFFFFF", textColor: "#333333" },
      { bgColor: "#6B0FBE", textColor: "#FFFFFF" },
      { bgColor: "#A764CA", textColor: "#FFFFFF" },
      { bgColor: "#F5F7FA", textColor: "#333333" },
      { bgColor: "#FFE8F0", textColor: "#FF3B30" },
      { bgColor: "#E8F8FF", textColor: "#007AFF" }
    ];
    const historyQrcodes = common_vendor.reactive([
      { image: "/static/images/distribution/qrcode-1.png", date: "2023-04-25" },
      { image: "/static/images/distribution/qrcode-2.png", date: "2023-04-20" },
      { image: "/static/images/distribution/qrcode-3.png", date: "2023-04-15" }
    ]);
    const selectedType = common_vendor.ref("product");
    const selectedTheme = common_vendor.ref(themeColors[0]);
    const showLogo = common_vendor.ref(true);
    const showId = common_vendor.ref(true);
    const customText = common_vendor.ref("");
    const currentQrcode = common_vendor.ref("/static/images/distribution/qrcode-sample.png");
    const displayQrcodeTypes = common_vendor.computed(() => {
      let types = [...qrcodeTypes];
      if (dynamicQrcodeTypes[promotionParams.type]) {
        types = [dynamicQrcodeTypes[promotionParams.type], ...types];
      }
      return types;
    });
    common_vendor.onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      parsePromotionParams(options);
      generateQrcode();
      generatePromotionText();
    });
    const parsePromotionParams = (options) => {
      promotionParams.type = options.type || "product";
      promotionParams.id = options.id || "";
      promotionParams.title = options.title ? decodeURIComponent(options.title) : "";
      promotionParams.image = options.image ? decodeURIComponent(options.image) : "";
      const extraParams = {};
      Object.keys(options).forEach((key) => {
        if (!["type", "id", "title", "image"].includes(key)) {
          extraParams[key] = decodeURIComponent(options[key] || "");
        }
      });
      promotionParams.extraParams = extraParams;
      selectedType.value = promotionParams.type;
    };
    const generateQrcode = () => {
      currentQrcode.value = "/static/images/distribution/qrcode-sample.png";
    };
    const generatePromotionText = () => {
      switch (promotionParams.type) {
        case "carpool":
          customText.value = `扫码查看【${promotionParams.title}】拼车信息`;
          break;
        case "secondhand":
          customText.value = `扫码查看【${promotionParams.title}】二手商品`;
          break;
        case "house":
          customText.value = `扫码查看【${promotionParams.title}】房源信息`;
          break;
        case "service":
          customText.value = `扫码预约【${promotionParams.title}】服务`;
          break;
        case "product":
          customText.value = `扫码购买【${promotionParams.title}】`;
          break;
        case "content":
          customText.value = `扫码阅读【${promotionParams.title}】`;
          break;
        case "community":
          customText.value = `扫码查看【${promotionParams.title}】社区信息`;
          break;
        default:
          customText.value = `扫码查看【${promotionParams.title}】`;
      }
    };
    const selectType = (type) => {
      selectedType.value = type;
      generateQrcode();
    };
    const selectTheme = (theme) => {
      selectedTheme.value = theme;
    };
    const toggleLogo = (e) => {
      showLogo.value = e.detail.value;
    };
    const toggleId = (e) => {
      showId.value = e.detail.value;
    };
    const saveQrcode = () => {
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "保存成功",
          icon: "success"
        });
        const now = /* @__PURE__ */ new Date();
        const dateStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, "0")}-${String(now.getDate()).padStart(2, "0")}`;
        historyQrcodes.unshift({
          image: currentQrcode.value,
          date: dateStr
        });
      }, 1500);
    };
    const shareQrcode = () => {
      common_vendor.index.showActionSheet({
        itemList: ["分享给朋友", "分享到朋友圈"],
        success: (res) => {
          common_vendor.index.showToast({
            title: "分享成功",
            icon: "success"
          });
          const shareType = res.tapIndex === 0 ? "friend" : "timeline";
          logShareEvent(shareType);
        }
      });
    };
    const logShareEvent = (shareType) => {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/qrcode.vue:426", "记录分享事件", {
        type: promotionParams.type,
        id: promotionParams.id,
        shareType
      });
    };
    const loadHistoryQrcode = (item) => {
      common_vendor.index.showToast({
        title: "加载历史二维码",
        icon: "none"
      });
    };
    const clearHistory = () => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要清空历史记录吗？",
        success: (res) => {
          if (res.confirm) {
            historyQrcodes.splice(0, historyQrcodes.length);
            common_vendor.index.showToast({
              title: "已清空",
              icon: "success"
            });
          }
        }
      });
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const showHelp = () => {
      common_vendor.index.showModal({
        title: "推广二维码使用帮助",
        content: "选择喜欢的二维码样式，自定义颜色和文案，生成精美二维码进行分享推广。",
        showCancel: false
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(goBack),
        b: common_vendor.o(showHelp),
        c: storeInfo.logo,
        d: common_vendor.t(storeInfo.name),
        e: common_vendor.t(storeInfo.slogan),
        f: currentQrcode.value,
        g: showLogo.value
      }, showLogo.value ? {
        h: storeInfo.logo
      } : {}, {
        i: common_vendor.t(customText.value),
        j: common_vendor.t(distributorId.value),
        k: selectedTheme.value.bgColor,
        l: common_vendor.p({
          d: "M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        m: common_vendor.p({
          d: "M7 10L12 15L17 10",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        n: common_vendor.p({
          d: "M12 15V3",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        o: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        p: common_vendor.o(saveQrcode),
        q: common_vendor.p({
          cx: "18",
          cy: "5",
          r: "3",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        r: common_vendor.p({
          cx: "6",
          cy: "12",
          r: "3",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        s: common_vendor.p({
          cx: "18",
          cy: "19",
          r: "3",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        t: common_vendor.p({
          x1: "8.59",
          y1: "13.51",
          x2: "15.42",
          y2: "17.49",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        v: common_vendor.p({
          x1: "15.41",
          y1: "6.51",
          x2: "8.59",
          y2: "10.49",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        w: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        x: common_vendor.o(shareQrcode),
        y: common_vendor.f(displayQrcodeTypes.value, (type, index, i0) => {
          return {
            a: type.icon,
            b: common_vendor.t(type.name),
            c: index,
            d: selectedType.value === type.value ? 1 : "",
            e: common_vendor.o(($event) => selectType(type.value), index)
          };
        }),
        z: common_vendor.f(themeColors, (theme, index, i0) => {
          return common_vendor.e({
            a: selectedTheme.value === theme
          }, selectedTheme.value === theme ? {
            b: "4bf3f175-11-" + i0 + "," + ("4bf3f175-10-" + i0),
            c: common_vendor.p({
              d: "M20 6L9 17L4 12",
              stroke: "white",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            d: "4bf3f175-10-" + i0,
            e: common_vendor.p({
              width: "16",
              height: "16",
              viewBox: "0 0 24 24",
              fill: "none",
              xmlns: "http://www.w3.org/2000/svg"
            })
          } : {}, {
            f: index,
            g: selectedTheme.value === theme ? 1 : "",
            h: theme.bgColor,
            i: common_vendor.o(($event) => selectTheme(theme), index)
          });
        }),
        A: showLogo.value,
        B: common_vendor.o(toggleLogo),
        C: showId.value,
        D: common_vendor.o(toggleId),
        E: customText.value,
        F: common_vendor.o(($event) => customText.value = $event.detail.value),
        G: common_vendor.o(clearHistory),
        H: common_vendor.f(historyQrcodes, (item, index, i0) => {
          return {
            a: item.image,
            b: common_vendor.t(item.date),
            c: index,
            d: common_vendor.o(($event) => loadHistoryQrcode(), index)
          };
        })
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/distribution/qrcode.js.map
