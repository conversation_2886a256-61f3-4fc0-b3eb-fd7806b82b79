{"version": 3, "file": "serviceList.js", "sources": ["mock/service/serviceList.js"], "sourcesContent": ["// 服务列表模拟数据\r\nexport const serviceList = [\r\n  {\r\n    id: 'service-001',\r\n    title: '家电维修',\r\n    icon: '/static/images/service/repair.png',\r\n    description: '提供各类家电维修服务，包括空调、冰箱、洗衣机、电视等',\r\n    price: '上门费30元起',\r\n    rating: 4.8,\r\n    orderCount: 1256,\r\n    category: '家居服务',\r\n    tags: ['上门服务', '快速响应', '专业维修']\r\n  },\r\n  {\r\n    id: 'service-002',\r\n    title: '保洁服务',\r\n    icon: '/static/images/service/cleaning.png',\r\n    description: '提供专业的家庭保洁、办公室保洁、开荒保洁等服务',\r\n    price: '50元/小时起',\r\n    rating: 4.7,\r\n    orderCount: 986,\r\n    category: '家居服务',\r\n    tags: ['专业保洁', '定期保洁', '深度清洁']\r\n  },\r\n  {\r\n    id: 'service-003',\r\n    title: '搬家服务',\r\n    icon: '/static/images/service/moving.png',\r\n    description: '提供专业的居民搬家、企业搬迁、小型搬运等服务',\r\n    price: '200元/次起',\r\n    rating: 4.6,\r\n    orderCount: 758,\r\n    category: '家居服务',\r\n    tags: ['专业搬运', '包装服务', '全程保险']\r\n  },\r\n  {\r\n    id: 'service-004',\r\n    title: '管道疏通',\r\n    icon: '/static/images/service/plumbing.png',\r\n    description: '提供厨房、卫生间等各类管道疏通服务',\r\n    price: '80元/次起',\r\n    rating: 4.5,\r\n    orderCount: 632,\r\n    category: '家居服务',\r\n    tags: ['快速响应', '彻底疏通', '免费复查']\r\n  },\r\n  {\r\n    id: 'service-005',\r\n    title: '上门按摩',\r\n    icon: '/static/images/service/massage.png',\r\n    description: '提供专业的上门按摩、推拿、足疗等服务',\r\n    price: '120元/小时起',\r\n    rating: 4.9,\r\n    orderCount: 1458,\r\n    category: '生活服务',\r\n    tags: ['专业技师', '舒适体验', '中医理疗']\r\n  },\r\n  {\r\n    id: 'service-006',\r\n    title: '美甲服务',\r\n    icon: '/static/images/service/nail.png',\r\n    description: '提供专业的美甲、修甲、卸甲等服务',\r\n    price: '80元/次起',\r\n    rating: 4.7,\r\n    orderCount: 865,\r\n    category: '生活服务',\r\n    tags: ['时尚设计', '环保材料', '持久不掉色']\r\n  },\r\n  {\r\n    id: 'service-007',\r\n    title: '宠物寄养',\r\n    icon: '/static/images/service/pet.png',\r\n    description: '提供专业的宠物寄养、喂食、洗澡、遛狗等服务',\r\n    price: '60元/天起',\r\n    rating: 4.8,\r\n    orderCount: 723,\r\n    category: '生活服务',\r\n    tags: ['专业护理', '舒适环境', '实时监控']\r\n  },\r\n  {\r\n    id: 'service-008',\r\n    title: '代驾服务',\r\n    icon: '/static/images/service/driving.png',\r\n    description: '提供专业的代驾服务，让您安全到家',\r\n    price: '30元/次起',\r\n    rating: 4.6,\r\n    orderCount: 1562,\r\n    category: '出行服务',\r\n    tags: ['专业驾驶', '安全保障', '全程保险']\r\n  }\r\n];\r\n\r\n// 服务分类\r\nexport const serviceCategories = [\r\n  { id: 0, name: '全部' },\r\n  { id: 1, name: '家居服务' },\r\n  { id: 2, name: '生活服务' },\r\n  { id: 3, name: '出行服务' },\r\n  { id: 4, name: '教育培训' },\r\n  { id: 5, name: '健康医疗' },\r\n  { id: 6, name: '商务服务' },\r\n  { id: 7, name: '其他服务' }\r\n];\r\n\r\n// 获取服务列表的API函数\r\nexport const fetchServiceList = (categoryId = 0, page = 1, pageSize = 10) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      let result = [...serviceList];\r\n      \r\n      // 按分类筛选\r\n      if (categoryId !== 0) {\r\n        const categoryMap = {\r\n          1: '家居服务',\r\n          2: '生活服务',\r\n          3: '出行服务',\r\n          4: '教育培训',\r\n          5: '健康医疗',\r\n          6: '商务服务',\r\n          7: '其他服务'\r\n        };\r\n        result = result.filter(item => item.category === categoryMap[categoryId]);\r\n      }\r\n      \r\n      // 分页处理\r\n      const start = (page - 1) * pageSize;\r\n      const end = start + pageSize;\r\n      const data = result.slice(start, end);\r\n      \r\n      // 返回数据和分页信息\r\n      resolve({\r\n        list: data,\r\n        total: result.length,\r\n        hasMore: end < result.length\r\n      });\r\n    }, 500);\r\n  });\r\n};\r\n\r\n// 获取服务分类的API函数\r\nexport const fetchServiceCategories = () => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve(serviceCategories);\r\n    }, 300);\r\n  });\r\n};\r\n\r\n// 获取热门服务的API函数\r\nexport const fetchHotServices = () => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      // 按订单量排序，取前4个\r\n      const hotServices = [...serviceList]\r\n        .sort((a, b) => b.orderCount - a.orderCount)\r\n        .slice(0, 4);\r\n      \r\n      resolve(hotServices);\r\n    }, 500);\r\n  });\r\n}; "], "names": [], "mappings": ";AACY,MAAC,cAAc;AAAA,EACzB;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,IACb,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,EAC9B;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,IACb,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,EAC9B;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,IACb,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,EAC9B;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,IACb,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,EAC9B;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,IACb,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,EAC9B;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,IACb,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,MAAM,CAAC,QAAQ,QAAQ,OAAO;AAAA,EAC/B;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,IACb,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,EAC9B;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,IACb,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,EAC9B;AACH;AAGY,MAAC,oBAAoB;AAAA,EAC/B,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,EACrB,EAAE,IAAI,GAAG,MAAM,OAAQ;AAAA,EACvB,EAAE,IAAI,GAAG,MAAM,OAAQ;AAAA,EACvB,EAAE,IAAI,GAAG,MAAM,OAAQ;AAAA,EACvB,EAAE,IAAI,GAAG,MAAM,OAAQ;AAAA,EACvB,EAAE,IAAI,GAAG,MAAM,OAAQ;AAAA,EACvB,EAAE,IAAI,GAAG,MAAM,OAAQ;AAAA,EACvB,EAAE,IAAI,GAAG,MAAM,OAAQ;AACzB;AAGY,MAAC,mBAAmB,CAAC,aAAa,GAAG,OAAO,GAAG,WAAW,OAAO;AAC3E,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,UAAI,SAAS,CAAC,GAAG,WAAW;AAG5B,UAAI,eAAe,GAAG;AACpB,cAAM,cAAc;AAAA,UAClB,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,QACb;AACQ,iBAAS,OAAO,OAAO,UAAQ,KAAK,aAAa,YAAY,UAAU,CAAC;AAAA,MACzE;AAGD,YAAM,SAAS,OAAO,KAAK;AAC3B,YAAM,MAAM,QAAQ;AACpB,YAAM,OAAO,OAAO,MAAM,OAAO,GAAG;AAGpC,cAAQ;AAAA,QACN,MAAM;AAAA,QACN,OAAO,OAAO;AAAA,QACd,SAAS,MAAM,OAAO;AAAA,MAC9B,CAAO;AAAA,IACF,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,yBAAyB,MAAM;AAC1C,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,cAAQ,iBAAiB;AAAA,IAC1B,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,mBAAmB,MAAM;AACpC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AAEf,YAAM,cAAc,CAAC,GAAG,WAAW,EAChC,KAAK,CAAC,GAAG,MAAM,EAAE,aAAa,EAAE,UAAU,EAC1C,MAAM,GAAG,CAAC;AAEb,cAAQ,WAAW;AAAA,IACpB,GAAE,GAAG;AAAA,EACV,CAAG;AACH;;;;;;"}