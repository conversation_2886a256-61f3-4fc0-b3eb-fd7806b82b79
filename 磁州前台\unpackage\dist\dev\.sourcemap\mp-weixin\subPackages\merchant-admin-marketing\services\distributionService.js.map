{"version": 3, "file": "distributionService.js", "sources": ["subPackages/merchant-admin-marketing/services/distributionService.js"], "sourcesContent": ["/**\r\n * 分销服务\r\n * 处理商家后台分销相关的业务逻辑\r\n */\r\n\r\n// 修改导入路径\r\nimport requestModule from '/utils/request.js';\r\nimport { showToast } from '/utils/uniapi.js';\r\n\r\n// 解构出 request 方法\r\nconst { request } = requestModule;\r\n\r\n/**\r\n * 分销服务类\r\n */\r\nclass DistributionService {\r\n  /**\r\n   * 检查商家是否开通分销功能\r\n   * @param {Object} merchantInfo 商家信息\r\n   * @returns {Promise<boolean>} 是否开通分销功能\r\n   */\r\n  async checkMerchantDistribution(merchantInfo) {\r\n    try {\r\n      const { data } = await request({\r\n        url: '/merchant/distribution/status',\r\n        method: 'GET',\r\n        data: {\r\n          merchantId: merchantInfo.id\r\n        }\r\n      });\r\n      return data.enabled || false;\r\n    } catch (error) {\r\n      console.error('检查分销状态失败', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取商家分销默认设置\r\n   * @param {Object} merchantInfo 商家信息\r\n   * @returns {Promise<Object>} 分销默认设置\r\n   */\r\n  async getMerchantDistributionSettings(merchantInfo) {\r\n    try {\r\n      const { data } = await request({\r\n        url: '/merchant/distribution/settings',\r\n        method: 'GET',\r\n        data: {\r\n          merchantId: merchantInfo.id\r\n        }\r\n      });\r\n      \r\n      return {\r\n        enabled: false, // 默认不启用\r\n        commissionMode: data.commissionMode || 'percentage',\r\n        commissions: {\r\n          level1: data.defaultCommission?.level1 || '',\r\n          level2: data.defaultCommission?.level2 || '',\r\n          level3: data.defaultCommission?.level3 || ''\r\n        },\r\n        enableLevel3: data.enableLevel3 || false\r\n      };\r\n    } catch (error) {\r\n      console.error('获取分销设置失败', error);\r\n      return {\r\n        enabled: false,\r\n        commissionMode: 'percentage',\r\n        commissions: {\r\n          level1: '',\r\n          level2: '',\r\n          level3: ''\r\n        },\r\n        enableLevel3: false\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 保存活动分销设置\r\n   * @param {string} activityType 活动类型：coupon-优惠券, discount-满减, flash-秒杀, group-拼团\r\n   * @param {string} activityId 活动ID\r\n   * @param {Object} distributionSettings 分销设置\r\n   * @returns {Promise<boolean>} 是否保存成功\r\n   */\r\n  async saveActivityDistributionSettings(activityType, activityId, distributionSettings) {\r\n    try {\r\n      const { data } = await request({\r\n        url: '/merchant/distribution/activity/settings',\r\n        method: 'POST',\r\n        data: {\r\n          activityType,\r\n          activityId,\r\n          settings: distributionSettings\r\n        }\r\n      });\r\n      \r\n      return data.success || false;\r\n    } catch (error) {\r\n      console.error('保存活动分销设置失败', error);\r\n      showToast('保存分销设置失败，请重试');\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取活动分销设置\r\n   * @param {string} activityType 活动类型：coupon-优惠券, discount-满减, flash-秒杀, group-拼团\r\n   * @param {string} activityId 活动ID\r\n   * @returns {Promise<Object>} 分销设置\r\n   */\r\n  async getActivityDistributionSettings(activityType, activityId) {\r\n    try {\r\n      const { data } = await request({\r\n        url: '/merchant/distribution/activity/settings',\r\n        method: 'GET',\r\n        data: {\r\n          activityType,\r\n          activityId\r\n        }\r\n      });\r\n      \r\n      return data.settings || {\r\n        enabled: false,\r\n        commissionMode: 'percentage',\r\n        commissions: {\r\n          level1: '',\r\n          level2: '',\r\n          level3: ''\r\n        },\r\n        enableLevel3: false\r\n      };\r\n    } catch (error) {\r\n      console.error('获取活动分销设置失败', error);\r\n      return {\r\n        enabled: false,\r\n        commissionMode: 'percentage',\r\n        commissions: {\r\n          level1: '',\r\n          level2: '',\r\n          level3: ''\r\n        },\r\n        enableLevel3: false\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 计算预估佣金\r\n   * @param {Object} settings 分销设置\r\n   * @param {number} price 商品价格\r\n   * @returns {Object} 预估佣金\r\n   */\r\n  calculateEstimatedCommission(settings, price) {\r\n    if (!settings || !settings.enabled || !price) {\r\n      return {\r\n        level1: 0,\r\n        level2: 0,\r\n        level3: 0,\r\n        total: 0\r\n      };\r\n    }\r\n\r\n    const { commissionMode, commissions, enableLevel3 } = settings;\r\n    let level1 = 0, level2 = 0, level3 = 0;\r\n\r\n    if (commissionMode === 'percentage') {\r\n      level1 = price * (parseFloat(commissions.level1 || 0) / 100);\r\n      level2 = price * (parseFloat(commissions.level2 || 0) / 100);\r\n      if (enableLevel3) {\r\n        level3 = price * (parseFloat(commissions.level3 || 0) / 100);\r\n      }\r\n    } else {\r\n      level1 = parseFloat(commissions.level1 || 0);\r\n      level2 = parseFloat(commissions.level2 || 0);\r\n      if (enableLevel3) {\r\n        level3 = parseFloat(commissions.level3 || 0);\r\n      }\r\n    }\r\n\r\n    const total = level1 + level2 + level3;\r\n\r\n    return {\r\n      level1: parseFloat(level1.toFixed(2)),\r\n      level2: parseFloat(level2.toFixed(2)),\r\n      level3: parseFloat(level3.toFixed(2)),\r\n      total: parseFloat(total.toFixed(2))\r\n    };\r\n  }\r\n\r\n  /**\r\n   * 验证分销设置是否有效\r\n   * @param {Object} settings 分销设置\r\n   * @returns {Object} 验证结果\r\n   */\r\n  validateDistributionSettings(settings) {\r\n    if (!settings.enabled) {\r\n      return { valid: true };\r\n    }\r\n\r\n    const { commissionMode, commissions, enableLevel3 } = settings;\r\n    const errors = [];\r\n\r\n    // 验证一级佣金\r\n    if (!commissions.level1) {\r\n      errors.push('请设置一级分销佣金');\r\n    } else if (commissionMode === 'percentage' && (parseFloat(commissions.level1) <= 0 || parseFloat(commissions.level1) > 100)) {\r\n      errors.push('一级分销比例应在0-100%之间');\r\n    } else if (commissionMode === 'fixed' && parseFloat(commissions.level1) < 0) {\r\n      errors.push('一级分销金额不能为负数');\r\n    }\r\n\r\n    // 验证二级佣金\r\n    if (!commissions.level2) {\r\n      errors.push('请设置二级分销佣金');\r\n    } else if (commissionMode === 'percentage' && (parseFloat(commissions.level2) < 0 || parseFloat(commissions.level2) > 100)) {\r\n      errors.push('二级分销比例应在0-100%之间');\r\n    } else if (commissionMode === 'fixed' && parseFloat(commissions.level2) < 0) {\r\n      errors.push('二级分销金额不能为负数');\r\n    }\r\n\r\n    // 验证三级佣金\r\n    if (enableLevel3) {\r\n      if (!commissions.level3) {\r\n        errors.push('请设置三级分销佣金');\r\n      } else if (commissionMode === 'percentage' && (parseFloat(commissions.level3) < 0 || parseFloat(commissions.level3) > 100)) {\r\n        errors.push('三级分销比例应在0-100%之间');\r\n      } else if (commissionMode === 'fixed' && parseFloat(commissions.level3) < 0) {\r\n        errors.push('三级分销金额不能为负数');\r\n      }\r\n    }\r\n\r\n    // 验证总佣金比例\r\n    if (commissionMode === 'percentage') {\r\n      const totalPercentage = parseFloat(commissions.level1 || 0) + \r\n                             parseFloat(commissions.level2 || 0) + \r\n                             (enableLevel3 ? parseFloat(commissions.level3 || 0) : 0);\r\n      \r\n      if (totalPercentage > 100) {\r\n        errors.push('分销佣金总比例不能超过100%');\r\n      }\r\n    }\r\n\r\n    return {\r\n      valid: errors.length === 0,\r\n      errors\r\n    };\r\n  }\r\n}\r\n\r\n// 导出分销服务实例\r\nexport const distributionService = new DistributionService(); "], "names": ["requestModule", "uni", "showToast"], "mappings": ";;;;AAUA,MAAM,EAAE,QAAS,IAAGA;AAKpB,MAAM,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxB,MAAM,0BAA0B,cAAc;AAC5C,QAAI;AACF,YAAM,EAAE,SAAS,MAAM,QAAQ;AAAA,QAC7B,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,MAAM;AAAA,UACJ,YAAY,aAAa;AAAA,QAC1B;AAAA,MACT,CAAO;AACD,aAAO,KAAK,WAAW;AAAA,IACxB,SAAQ,OAAO;AACdC,uHAAc,YAAY,KAAK;AAC/B,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,gCAAgC,cAAc;;AAClD,QAAI;AACF,YAAM,EAAE,SAAS,MAAM,QAAQ;AAAA,QAC7B,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,MAAM;AAAA,UACJ,YAAY,aAAa;AAAA,QAC1B;AAAA,MACT,CAAO;AAED,aAAO;AAAA,QACL,SAAS;AAAA;AAAA,QACT,gBAAgB,KAAK,kBAAkB;AAAA,QACvC,aAAa;AAAA,UACX,UAAQ,UAAK,sBAAL,mBAAwB,WAAU;AAAA,UAC1C,UAAQ,UAAK,sBAAL,mBAAwB,WAAU;AAAA,UAC1C,UAAQ,UAAK,sBAAL,mBAAwB,WAAU;AAAA,QAC3C;AAAA,QACD,cAAc,KAAK,gBAAgB;AAAA,MAC3C;AAAA,IACK,SAAQ,OAAO;AACdA,uHAAc,YAAY,KAAK;AAC/B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,gBAAgB;AAAA,QAChB,aAAa;AAAA,UACX,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,QACD,cAAc;AAAA,MACtB;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,MAAM,iCAAiC,cAAc,YAAY,sBAAsB;AACrF,QAAI;AACF,YAAM,EAAE,SAAS,MAAM,QAAQ;AAAA,QAC7B,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,MAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA,UAAU;AAAA,QACX;AAAA,MACT,CAAO;AAED,aAAO,KAAK,WAAW;AAAA,IACxB,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,8EAAA,cAAc,KAAK;AACjCC,mBAAS,UAAC,cAAc;AACxB,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,MAAM,gCAAgC,cAAc,YAAY;AAC9D,QAAI;AACF,YAAM,EAAE,SAAS,MAAM,QAAQ;AAAA,QAC7B,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,MAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACD;AAAA,MACT,CAAO;AAED,aAAO,KAAK,YAAY;AAAA,QACtB,SAAS;AAAA,QACT,gBAAgB;AAAA,QAChB,aAAa;AAAA,UACX,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,QACD,cAAc;AAAA,MACtB;AAAA,IACK,SAAQ,OAAO;AACdD,oBAAc,MAAA,MAAA,SAAA,+EAAA,cAAc,KAAK;AACjC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,gBAAgB;AAAA,QAChB,aAAa;AAAA,UACX,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,QACD,cAAc;AAAA,MACtB;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,6BAA6B,UAAU,OAAO;AAC5C,QAAI,CAAC,YAAY,CAAC,SAAS,WAAW,CAAC,OAAO;AAC5C,aAAO;AAAA,QACL,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,MACf;AAAA,IACK;AAED,UAAM,EAAE,gBAAgB,aAAa,aAAY,IAAK;AACtD,QAAI,SAAS,GAAG,SAAS,GAAG,SAAS;AAErC,QAAI,mBAAmB,cAAc;AACnC,eAAS,SAAS,WAAW,YAAY,UAAU,CAAC,IAAI;AACxD,eAAS,SAAS,WAAW,YAAY,UAAU,CAAC,IAAI;AACxD,UAAI,cAAc;AAChB,iBAAS,SAAS,WAAW,YAAY,UAAU,CAAC,IAAI;AAAA,MACzD;AAAA,IACP,OAAW;AACL,eAAS,WAAW,YAAY,UAAU,CAAC;AAC3C,eAAS,WAAW,YAAY,UAAU,CAAC;AAC3C,UAAI,cAAc;AAChB,iBAAS,WAAW,YAAY,UAAU,CAAC;AAAA,MAC5C;AAAA,IACF;AAED,UAAM,QAAQ,SAAS,SAAS;AAEhC,WAAO;AAAA,MACL,QAAQ,WAAW,OAAO,QAAQ,CAAC,CAAC;AAAA,MACpC,QAAQ,WAAW,OAAO,QAAQ,CAAC,CAAC;AAAA,MACpC,QAAQ,WAAW,OAAO,QAAQ,CAAC,CAAC;AAAA,MACpC,OAAO,WAAW,MAAM,QAAQ,CAAC,CAAC;AAAA,IACxC;AAAA,EACG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,6BAA6B,UAAU;AACrC,QAAI,CAAC,SAAS,SAAS;AACrB,aAAO,EAAE,OAAO;IACjB;AAED,UAAM,EAAE,gBAAgB,aAAa,aAAY,IAAK;AACtD,UAAM,SAAS,CAAA;AAGf,QAAI,CAAC,YAAY,QAAQ;AACvB,aAAO,KAAK,WAAW;AAAA,IACxB,WAAU,mBAAmB,iBAAiB,WAAW,YAAY,MAAM,KAAK,KAAK,WAAW,YAAY,MAAM,IAAI,MAAM;AAC3H,aAAO,KAAK,kBAAkB;AAAA,IACpC,WAAe,mBAAmB,WAAW,WAAW,YAAY,MAAM,IAAI,GAAG;AAC3E,aAAO,KAAK,aAAa;AAAA,IAC1B;AAGD,QAAI,CAAC,YAAY,QAAQ;AACvB,aAAO,KAAK,WAAW;AAAA,IACxB,WAAU,mBAAmB,iBAAiB,WAAW,YAAY,MAAM,IAAI,KAAK,WAAW,YAAY,MAAM,IAAI,MAAM;AAC1H,aAAO,KAAK,kBAAkB;AAAA,IACpC,WAAe,mBAAmB,WAAW,WAAW,YAAY,MAAM,IAAI,GAAG;AAC3E,aAAO,KAAK,aAAa;AAAA,IAC1B;AAGD,QAAI,cAAc;AAChB,UAAI,CAAC,YAAY,QAAQ;AACvB,eAAO,KAAK,WAAW;AAAA,MACxB,WAAU,mBAAmB,iBAAiB,WAAW,YAAY,MAAM,IAAI,KAAK,WAAW,YAAY,MAAM,IAAI,MAAM;AAC1H,eAAO,KAAK,kBAAkB;AAAA,MACtC,WAAiB,mBAAmB,WAAW,WAAW,YAAY,MAAM,IAAI,GAAG;AAC3E,eAAO,KAAK,aAAa;AAAA,MAC1B;AAAA,IACF;AAGD,QAAI,mBAAmB,cAAc;AACnC,YAAM,kBAAkB,WAAW,YAAY,UAAU,CAAC,IACnC,WAAW,YAAY,UAAU,CAAC,KACjC,eAAe,WAAW,YAAY,UAAU,CAAC,IAAI;AAE7E,UAAI,kBAAkB,KAAK;AACzB,eAAO,KAAK,iBAAiB;AAAA,MAC9B;AAAA,IACF;AAED,WAAO;AAAA,MACL,OAAO,OAAO,WAAW;AAAA,MACzB;AAAA,IACN;AAAA,EACG;AACH;AAGY,MAAC,sBAAsB,IAAI,oBAAmB;;"}