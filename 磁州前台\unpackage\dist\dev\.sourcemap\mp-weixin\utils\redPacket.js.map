{"version": 3, "file": "redPacket.js", "sources": ["utils/redPacket.js"], "sourcesContent": ["/**\r\n * 红包系统工具函数\r\n */\r\n\r\n// 红包状态常量\r\nexport const RED_PACKET_STATUS = {\r\n  ACTIVE: 0,           // 进行中\r\n  FINISHED: 1,         // 已领完\r\n  EXPIRED: 2           // 已过期\r\n};\r\n\r\n// 红包类型常量\r\nexport const RED_PACKET_TYPE = {\r\n  NORMAL: 'normal',    // 普通红包\r\n  LUCKY: 'lucky',      // 拼手气红包\r\n  FIXED: 'fixed',      // 固定金额红包\r\n  CONSUME: 'consume'   // 消费满额红包\r\n};\r\n\r\n// 消费满额红包配置\r\nexport const CONSUME_RED_PACKET_CONFIG = {\r\n  THRESHOLD: 100,      // 消费门槛（元）\r\n  MIN_AMOUNT: 1,       // 最小红包金额\r\n  MAX_AMOUNT: 88       // 最大红包金额\r\n};\r\n\r\nimport request from './request';\r\n\r\n/**\r\n * 获取商家红包列表\r\n * @param {Object} params 查询参数\r\n * @param {number} params.page 页码\r\n * @param {number} params.pageSize 每页数量\r\n * @param {string} params.status 红包状态\r\n * @param {string} params.type 红包类型\r\n * @returns {Promise<Object>} 红包列表数据\r\n */\r\nexport async function getMerchantRedPackets(params) {\r\n  try {\r\n    const res = await request({\r\n      url: '/api/redpacket/merchant',\r\n      method: 'GET',\r\n      data: params\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.error('获取商家红包列表失败:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * 获取我的红包列表\r\n * @param {Object} params 查询参数\r\n * @param {string} params.type 红包类型：received-收到的红包，sent-发出的红包\r\n * @param {number} params.page 页码\r\n * @param {number} params.pageSize 每页数量\r\n * @returns {Promise<Object>} 红包列表数据\r\n */\r\nexport async function getMyRedPackets(params) {\r\n  try {\r\n    const res = await request({\r\n      url: '/api/redpacket/my',\r\n      method: 'GET',\r\n      data: params\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.error('获取红包列表失败:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * 获取红包详情\r\n * @param {string} id 红包ID\r\n * @returns {Promise<Object>} 红包详情数据\r\n */\r\nexport async function getRedPacketDetail(id) {\r\n  try {\r\n    const res = await request({\r\n      url: `/api/redpacket/${id}`,\r\n      method: 'GET'\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.error('获取红包详情失败:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * 抢红包\r\n * @param {string} id 红包ID\r\n * @returns {Promise<Object>} 抢红包结果\r\n */\r\nexport async function grabRedPacket(id) {\r\n  try {\r\n    const res = await request({\r\n      url: `/api/redpacket/${id}/grab`,\r\n      method: 'POST'\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.error('抢红包失败:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * 创建红包\r\n * @param {Object} data 红包数据\r\n * @returns {Promise<Object>} 创建结果\r\n */\r\nexport async function createRedPacket(data) {\r\n  try {\r\n    const res = await request({\r\n      url: '/api/redpacket',\r\n      method: 'POST',\r\n      data\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.error('创建红包失败:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * 格式化红包金额\r\n * @param {number} amount 金额\r\n * @returns {string} 格式化后的金额\r\n */\r\nexport function formatRedPacketAmount(amount) {\r\n  if (typeof amount !== 'number') return '0.00';\r\n  return (amount / 100).toFixed(2);\r\n}\r\n\r\n/**\r\n * 获取红包状态文本\r\n * @param {string} status 状态码\r\n * @returns {string} 状态文本\r\n */\r\nexport function getRedPacketStatusText(status) {\r\n  const statusMap = {\r\n    'pending': '待领取',\r\n    'active': '进行中',\r\n    'completed': '已领完',\r\n    'expired': '已过期',\r\n    'closed': '已关闭'\r\n  };\r\n  return statusMap[status] || '未知状态';\r\n}\r\n\r\n/**\r\n * 获取红包类型文本\r\n * @param {string} type 类型码\r\n * @returns {string} 类型文本\r\n */\r\nexport function getRedPacketTypeText(type) {\r\n  const typeMap = {\r\n    'normal': '普通红包',\r\n    'lucky': '拼手气红包',\r\n    'fixed': '固定金额红包',\r\n    'merchant': '商家红包'\r\n  };\r\n  return typeMap[type] || '未知类型';\r\n}\r\n\r\n/**\r\n * 检查红包是否可抢\r\n * @param {Object} redPacket 红包对象\r\n * @returns {boolean} 是否可抢\r\n */\r\nexport function isRedPacketGrabable(redPacket) {\r\n  if (!redPacket) return false;\r\n  \r\n  const now = Date.now();\r\n  const startTime = new Date(redPacket.startTime).getTime();\r\n  const endTime = new Date(redPacket.endTime).getTime();\r\n  \r\n  return (\r\n    redPacket.status === 'active' &&\r\n    now >= startTime &&\r\n    now <= endTime &&\r\n    redPacket.remainCount > 0\r\n  );\r\n}\r\n\r\n/**\r\n * 计算红包状态\r\n * @param {Object} redPacket 红包数据\r\n * @returns {Number} 返回红包状态\r\n */\r\nexport function calculateRedPacketStatus(redPacket) {\r\n  const now = Date.now();\r\n  \r\n  // 已过期\r\n  if (redPacket.expireTime && now > redPacket.expireTime) {\r\n    return RED_PACKET_STATUS.EXPIRED;\r\n  }\r\n  \r\n  // 已领完\r\n  if (redPacket.remainCount === 0) {\r\n    return RED_PACKET_STATUS.FINISHED;\r\n  }\r\n  \r\n  // 默认为进行中\r\n  return RED_PACKET_STATUS.ACTIVE;\r\n}\r\n\r\n/**\r\n * 获取用户红包记录\r\n * @param {Object} params 查询参数\r\n * @returns {Promise} 返回红包记录\r\n */\r\nexport function getUserRedPacketRecords(params) {\r\n  return new Promise((resolve, reject) => {\r\n    uni.request({\r\n      url: '/api/red-packet/user-records',\r\n      method: 'GET',\r\n      data: params,\r\n      success: (res) => {\r\n        if (res.data.code === 0) {\r\n          resolve(res.data.data);\r\n        } else {\r\n          reject(new Error(res.data.message || '获取红包记录失败'));\r\n        }\r\n      },\r\n      fail: (err) => {\r\n        reject(new Error(err.errMsg || '网络请求失败'));\r\n      }\r\n    });\r\n  });\r\n}\r\n\r\n/**\r\n * 检查用户是否有资格参与消费红包活动\r\n * @param {string} userId 用户ID\r\n * @param {string} merchantId 商家ID\r\n * @returns {Promise<boolean>} 是否有资格\r\n */\r\nexport async function checkConsumeRedPacketEligibility(userId, merchantId) {\r\n  try {\r\n    // 查询用户在该商家的消费记录\r\n    const consumeAmount = await getConsumeAmount(userId, merchantId);\r\n    \r\n    // 判断是否达到门槛\r\n    return consumeAmount >= CONSUME_RED_PACKET_CONFIG.THRESHOLD;\r\n  } catch (error) {\r\n    console.error('检查红包资格失败', error);\r\n    return false;\r\n  }\r\n}\r\n\r\n/**\r\n * 获取用户在商家的消费金额\r\n * @param {string} userId 用户ID\r\n * @param {string} merchantId 商家ID\r\n * @returns {Promise<number>} 消费金额\r\n */\r\nasync function getConsumeAmount(userId, merchantId) {\r\n  try {\r\n    // 调用API获取用户消费记录\r\n    const res = await uni.request({\r\n      url: '/api/merchant/consume/amount',\r\n      method: 'GET',\r\n      data: {\r\n        userId,\r\n        merchantId,\r\n        timeRange: 30 // 查询最近30天的消费\r\n      }\r\n    });\r\n    \r\n    if (res.data.code === 0) {\r\n      return res.data.data.amount || 0;\r\n    }\r\n    return 0;\r\n  } catch (error) {\r\n    console.error('获取消费金额失败', error);\r\n    return 0;\r\n  }\r\n}\r\n\r\n/**\r\n * 抽取红包金额\r\n * @param {string} redPacketId 红包ID\r\n * @param {string} type 红包类型\r\n * @returns {Promise<number>} 红包金额\r\n */\r\nexport async function getRandomRedPacketAmount(redPacketId, type) {\r\n  try {\r\n    if (type === RED_PACKET_TYPE.CONSUME) {\r\n      // 消费红包随机金额\r\n      const min = CONSUME_RED_PACKET_CONFIG.MIN_AMOUNT * 100; // 转为分\r\n      const max = CONSUME_RED_PACKET_CONFIG.MAX_AMOUNT * 100; // 转为分\r\n      const amount = Math.floor(Math.random() * (max - min + 1)) + min;\r\n      return amount / 100; // 转回元\r\n    }\r\n    \r\n    // 其他类型红包需调用API\r\n    const res = await uni.request({\r\n      url: `/api/red-packets/${redPacketId}/draw`,\r\n      method: 'POST'\r\n    });\r\n    \r\n    if (res.data.code === 0) {\r\n      return res.data.data.amount || 0;\r\n    }\r\n    return 0;\r\n  } catch (error) {\r\n    console.error('抽取红包金额失败', error);\r\n    return 0;\r\n  }\r\n}\r\n\r\n/**\r\n * 保存红包领取记录\r\n * @param {Object} record 红包记录\r\n * @returns {Promise<boolean>} 是否保存成功\r\n */\r\nexport async function saveRedPacketRecord(record) {\r\n  try {\r\n    const res = await uni.request({\r\n      url: '/api/red-packets/records',\r\n      method: 'POST',\r\n      data: record\r\n    });\r\n    \r\n    return res.data.code === 0;\r\n  } catch (error) {\r\n    console.error('保存红包记录失败', error);\r\n    return false;\r\n  }\r\n}\r\n\r\n/**\r\n * 检查用户是否已领取过红包\r\n * @param {string} userId 用户ID\r\n * @param {string} redPacketId 红包ID\r\n * @returns {Promise<boolean>} 是否已领取\r\n */\r\nexport async function hasGrabbedRedPacket(userId, redPacketId) {\r\n  try {\r\n    const res = await uni.request({\r\n      url: '/api/red-packets/check',\r\n      method: 'GET',\r\n      data: {\r\n        userId,\r\n        redPacketId\r\n      }\r\n    });\r\n    \r\n    return res.data.code === 0 && res.data.data.hasGrabbed;\r\n  } catch (error) {\r\n    console.error('检查红包领取状态失败', error);\r\n    return false;\r\n  }\r\n} "], "names": ["request", "uni"], "mappings": ";;;AAYY,MAAC,kBAAkB;AAAA,EAC7B,QAAQ;AAAA;AAAA,EACR,OAAO;AAAA;AAAA,EACP,OAAO;AAAA;AAAA,EACP,SAAS;AAAA;AACX;AA0CO,eAAe,gBAAgB,QAAQ;AAC5C,MAAI;AACF,UAAM,MAAM,MAAMA,sBAAQ;AAAA,MACxB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,IACZ,CAAK;AACD,WAAO,IAAI;AAAA,EACZ,SAAQ,OAAO;AACdC,kBAAc,MAAA,MAAA,SAAA,4BAAA,aAAa,KAAK;AAChC,UAAM;AAAA,EACP;AACH;AAyBO,eAAe,cAAc,IAAI;AACtC,MAAI;AACF,UAAM,MAAM,MAAMD,sBAAQ;AAAA,MACxB,KAAK,kBAAkB,EAAE;AAAA,MACzB,QAAQ;AAAA,IACd,CAAK;AACD,WAAO,IAAI;AAAA,EACZ,SAAQ,OAAO;AACdC,kBAAA,MAAA,MAAA,SAAA,6BAAc,UAAU,KAAK;AAC7B,UAAM;AAAA,EACP;AACH;AAOO,eAAe,gBAAgB,MAAM;AAC1C,MAAI;AACF,UAAM,MAAM,MAAMD,sBAAQ;AAAA,MACxB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AACD,WAAO,IAAI;AAAA,EACZ,SAAQ,OAAO;AACdC,kBAAA,MAAA,MAAA,SAAA,6BAAc,WAAW,KAAK;AAC9B,UAAM;AAAA,EACP;AACH;AAOO,SAAS,sBAAsB,QAAQ;AAC5C,MAAI,OAAO,WAAW;AAAU,WAAO;AACvC,UAAQ,SAAS,KAAK,QAAQ,CAAC;AACjC;;;;;;"}