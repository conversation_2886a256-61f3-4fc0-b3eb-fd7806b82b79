<template>
  <view class="mine-page">
    <!-- 优化导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <text class="back-text">返回</text>
      </view>
      <view class="navbar-title">我的</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 页面内容，添加上边距以避免被导航栏遮挡 -->
    <view class="page-content" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
      <!-- 用户信息卡片 -->
      <view class="user-card">
        <view class="user-avatar">
          <image class="avatar-image" :src="userInfo.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
        </view>
        <view class="user-info">
          <text class="user-name">{{ userInfo.nickname || '未登录' }}</text>
          <text class="user-id" v-if="userInfo.userId">ID: {{ userInfo.userId }}</text>
        </view>
        <view class="user-level" v-if="userInfo.userId">
          <text class="level-text">{{ userInfo.level || '普通会员' }}</text>
        </view>
      </view>
      
      <!-- 功能模块 -->
      <view class="function-section">
        <view class="section-title">
          <text>我的功能</text>
        </view>
        
        <view class="function-grid">
          <!-- 返利商城入口 -->
          <view class="function-item" @tap="navigateToCashback">
            <view class="function-icon-wrapper" style="background: linear-gradient(135deg, #6B66FF 0%, #4F46E5 100%);">
              <svg class="function-icon" viewBox="0 0 24 24" width="24" height="24">
                <path fill="#FFFFFF" d="M12,13A5,5 0 0,1 7,8H9A3,3 0 0,0 12,11A3,3 0 0,0 15,8H17A5,5 0 0,1 12,13M12,3A3,3 0 0,1 15,6H9A3,3 0 0,1 12,3M19,6H17A5,5 0 0,0 12,1A5,5 0 0,0 7,6H5C3.89,6 3,6.89 3,8V20A2,2 0 0,0 5,22H19A2,2 0 0,0 21,20V8C21,6.89 20.1,6 19,6Z" />
              </svg>
            </view>
            <text class="function-name">返利商城</text>
          </view>
          
          <!-- 订单管理 -->
          <view class="function-item" @tap="navigateToOrders">
            <view class="function-icon-wrapper" style="background: linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%);">
              <svg class="function-icon" viewBox="0 0 24 24" width="24" height="24">
                <path fill="#FFFFFF" d="M19,3H14.82C14.4,1.84 13.3,1 12,1C10.7,1 9.6,1.84 9.18,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M12,3A1,1 0 0,1 13,4A1,1 0 0,1 12,5A1,1 0 0,1 11,4A1,1 0 0,1 12,3M7,7H17V5H19V19H5V5H7V7Z" />
              </svg>
            </view>
            <text class="function-name">我的订单</text>
          </view>
          
          <!-- 钱包 -->
          <view class="function-item" @tap="navigateToWallet">
            <view class="function-icon-wrapper" style="background: linear-gradient(135deg, #51CF66 0%, #20C997 100%);">
              <svg class="function-icon" viewBox="0 0 24 24" width="24" height="24">
                <path fill="#FFFFFF" d="M21,18V19A2,2 0 0,1 19,21H5C3.89,21 3,20.1 3,19V5A2,2 0 0,1 5,3H19A2,2 0 0,1 21,5V6H12C10.89,6 10,6.9 10,8V16A2,2 0 0,0 12,18M12,16H22V8H12M16,13.5A1.5,1.5 0 0,1 14.5,12A1.5,1.5 0 0,1 16,10.5A1.5,1.5 0 0,1 17.5,12A1.5,1.5 0 0,1 16,13.5Z" />
              </svg>
            </view>
            <text class="function-name">我的钱包</text>
          </view>
          
          <!-- 收藏 -->
          <view class="function-item" @tap="navigateToFavorites">
            <view class="function-icon-wrapper" style="background: linear-gradient(135deg, #FAB005 0%, #FF922B 100%);">
              <svg class="function-icon" viewBox="0 0 24 24" width="24" height="24">
                <path fill="#FFFFFF" d="M12,17.27L18.18,21L16.54,13.97L22,9.24L14.81,8.62L12,2L9.19,8.62L2,9.24L7.45,13.97L5.82,21L12,17.27Z" />
              </svg>
            </view>
            <text class="function-name">我的收藏</text>
          </view>
        </view>
      </view>
      
      <!-- 更多功能模块 -->
      <view class="menu-section">
        <view class="menu-item" @tap="navigateToSettings">
          <view class="menu-item-left">
            <view class="menu-icon-wrapper" style="background-color: rgba(107, 102, 255, 0.1);">
              <svg class="menu-icon" viewBox="0 0 24 24" width="20" height="20">
                <path fill="#6B66FF" d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.21,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.21,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.67 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z" />
              </svg>
            </view>
            <text class="menu-text">设置</text>
          </view>
          <svg class="menu-arrow" viewBox="0 0 24 24" width="20" height="20">
            <path fill="#CCCCCC" d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
          </svg>
        </view>
        
        <view class="menu-item" @tap="navigateToHelp">
          <view class="menu-item-left">
            <view class="menu-icon-wrapper" style="background-color: rgba(81, 207, 102, 0.1);">
              <svg class="menu-icon" viewBox="0 0 24 24" width="20" height="20">
                <path fill="#51CF66" d="M11,18H13V16H11V18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,6A4,4 0 0,0 8,10H10A2,2 0 0,1 12,8A2,2 0 0,1 14,10C14,12 11,11.75 11,15H13C13,12.75 16,12.5 16,10A4,4 0 0,0 12,6Z" />
              </svg>
            </view>
            <text class="menu-text">帮助与反馈</text>
          </view>
          <svg class="menu-arrow" viewBox="0 0 24 24" width="20" height="20">
            <path fill="#CCCCCC" d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
          </svg>
        </view>
        
        <view class="menu-item" @tap="navigateToAbout">
          <view class="menu-item-left">
            <view class="menu-icon-wrapper" style="background-color: rgba(255, 107, 107, 0.1);">
              <svg class="menu-icon" viewBox="0 0 24 24" width="20" height="20">
                <path fill="#FF6B6B" d="M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z" />
              </svg>
            </view>
            <text class="menu-text">关于我们</text>
          </view>
          <svg class="menu-arrow" viewBox="0 0 24 24" width="20" height="20">
            <path fill="#CCCCCC" d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
          </svg>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20,
      userInfo: {
        avatar: '',
        nickname: '游客',
        userId: '',
        level: ''
      }
    }
  },
  onLoad() {
    // 获取状态栏高度
    const sysInfo = uni.getWindowInfo();
    this.statusBarHeight = sysInfo.statusBarHeight;
    
    // 获取用户信息
    this.getUserInfo();
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack({
        fail: () => {
          // 如果没有上一页，则跳转到首页
          uni.switchTab({
            url: '/pages/index/index'
          });
        }
      });
    },
    
    // 获取用户信息
    getUserInfo() {
      // 这里应该从全局状态或本地存储获取用户信息
      // 示例代码，实际应根据项目情况调整
      const userInfo = uni.getStorageSync('userInfo');
      if (userInfo) {
        this.userInfo = JSON.parse(userInfo);
      }
    },
    
    // 导航到返利商城
    navigateToCashback() {
      uni.navigateTo({
        url: '/subPackages/cashback/pages/index/index'
      });
    },
    
    // 导航到订单页面
    navigateToOrders() {
      uni.navigateTo({
        url: '/subPackages/cashback/pages/orders/index'
      });
    },
    
    // 导航到钱包页面
    navigateToWallet() {
      uni.navigateTo({
        url: '/subPackages/cashback/pages/wallet/index'
      });
    },
    
    // 导航到收藏页面
    navigateToFavorites() {
      uni.navigateTo({
        url: '/subPackages/cashback/pages/favorites/index'
      });
    },
    
    // 导航到设置页面
    navigateToSettings() {
      uni.navigateTo({
        url: '/pages/settings/index'
      });
    },
    
    // 导航到帮助页面
    navigateToHelp() {
      uni.navigateTo({
        url: '/pages/help/index'
      });
    },
    
    // 导航到关于页面
    navigateToAbout() {
      uni.navigateTo({
        url: '/pages/about/index'
      });
    }
  }
}
</script>

<style lang="scss" scoped>
/* 导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  z-index: 999;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.navbar-left {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 44px;
  width: 60px;
  z-index: 1000;
}

.back-text {
  font-size: 16px;
  color: #007AFF;
  font-weight: 500;
}

.navbar-right {
  width: 60px;
  height: 44px;
}

.navbar-title {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 17px;
  font-weight: 600;
  color: #000000;
}

/* 页面内容样式 */
.page-content {
  width: 100%;
  box-sizing: border-box;
  padding: 20rpx;
  background-color: #F5F7FA;
  min-height: 100vh;
}

/* 用户卡片样式 */
.user-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  overflow: hidden;
  margin-right: 20rpx;
  background-color: #F5F5F5;
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.user-id {
  font-size: 24rpx;
  color: #999999;
}

.user-level {
  padding: 6rpx 16rpx;
  background-color: rgba(107, 102, 255, 0.1);
  border-radius: 30rpx;
}

.level-text {
  font-size: 24rpx;
  color: #6B66FF;
}

/* 功能区域样式 */
.function-section {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
}

.function-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.function-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.function-icon {
  width: 40rpx;
  height: 40rpx;
}

.function-name {
  font-size: 24rpx;
  color: #666666;
}

/* 菜单区域样式 */
.menu-section {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #F5F5F5;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item-left {
  display: flex;
  align-items: center;
}

.menu-icon-wrapper {
  width: 60rpx;
  height: 60rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
}

.menu-icon {
  width: 32rpx;
  height: 32rpx;
}

.menu-text {
  font-size: 28rpx;
  color: #333333;
}

.menu-arrow {
  color: #CCCCCC;
}

/* 响应式调整 */
@media screen and (max-width: 375px) {
  .function-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 10rpx;
  }
  
  .function-icon-wrapper {
    width: 70rpx;
    height: 70rpx;
  }
  
  .function-name {
    font-size: 22rpx;
  }
}
</style> 