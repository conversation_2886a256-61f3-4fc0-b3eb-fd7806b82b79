/**
 * 导航工具类
 * 提供安全的页面导航方法，包含错误处理
 */

/**
 * 安全导航到指定页面
 * @param {string} url 目标页面URL
 * @param {Object} options 额外选项
 * @returns {Promise} 导航结果Promise
 */
export const navigateTo = (url, options = {}) => {
  return new Promise((resolve, reject) => {
    try {
      // 导航前检查URL格式
      if (!url || typeof url !== 'string') {
        const error = new Error('导航URL无效');
        error.code = 'INVALID_URL';
        
        // 根据选项决定是否显示提示
        if (options.showToast !== false) {
          uni.showToast({
            title: error.message,
            icon: 'none',
            duration: 2000
          });
        }
        
        return reject(error);
      }
      
      // 执行导航
      uni.navigateTo({
        url,
        success: (res) => {
          console.log('页面导航成功:', url);
          resolve(res);
        },
        fail: (err) => {
          console.error('页面导航失败:', err);
          
          // 根据错误类型进行处理
          let errorMessage = '页面跳转失败';
          
          if (err.errMsg && err.errMsg.includes('is not found')) {
            errorMessage = '该功能正在开发中';
          } else if (err.errMsg && err.errMsg.includes('can not navigate')) {
            errorMessage = '无法导航到该页面';
          }
          
          // 根据选项决定是否显示提示
          if (options.showToast !== false) {
            uni.showToast({
              title: options.failMessage || errorMessage,
              icon: 'none',
              duration: 2000
            });
          }
          
          reject(err);
        }
      });
    } catch (error) {
      console.error('导航方法异常:', error);
      
      // 根据选项决定是否显示提示
      if (options.showToast !== false) {
        uni.showToast({
          title: '系统异常，请稍后再试',
          icon: 'none',
          duration: 2000
        });
      }
      
      reject(error);
    }
  });
};

/**
 * 重定向到指定页面
 * @param {string} url 目标页面URL
 * @param {Object} options 额外选项
 * @returns {Promise} 导航结果Promise
 */
export const redirectTo = (url, options = {}) => {
  return new Promise((resolve, reject) => {
    try {
      uni.redirectTo({
        url,
        success: (res) => {
          console.log('页面重定向成功:', url);
          resolve(res);
        },
        fail: (err) => {
          console.error('页面重定向失败:', err);
          
          // 显示提示
          if (options.showToast !== false) {
            uni.showToast({
              title: options.failMessage || '页面跳转失败',
              icon: 'none',
              duration: 2000
            });
          }
          
          reject(err);
        }
      });
    } catch (error) {
      console.error('重定向方法异常:', error);
      
      if (options.showToast !== false) {
        uni.showToast({
          title: '系统异常，请稍后再试',
          icon: 'none',
          duration: 2000
        });
      }
      
      reject(error);
    }
  });
};

/**
 * 导航到Tab页面
 * @param {string} url 目标页面URL
 * @param {Object} options 额外选项
 * @returns {Promise} 导航结果Promise
 */
export const switchTab = (url, options = {}) => {
  return new Promise((resolve, reject) => {
    try {
      uni.switchTab({
        url,
        success: (res) => {
          console.log('切换到Tab页面成功:', url);
          resolve(res);
        },
        fail: (err) => {
          console.error('切换到Tab页面失败:', err);
          
          if (options.showToast !== false) {
            uni.showToast({
              title: options.failMessage || '页面切换失败',
              icon: 'none',
              duration: 2000
            });
          }
          
          reject(err);
        }
      });
    } catch (error) {
      console.error('切换Tab方法异常:', error);
      
      if (options.showToast !== false) {
        uni.showToast({
          title: '系统异常，请稍后再试',
          icon: 'none',
          duration: 2000
        });
      }
      
      reject(error);
    }
  });
};

/**
 * 智能导航方法 - 自动选择最合适的导航方式
 * @param {string|Object} urlOrConfig 目标页面URL或配置对象
 * @param {Object} options 额外选项
 * @returns {Promise} 导航结果Promise
 */
export const smartNavigate = (urlOrConfig, options = {}) => {
  // 处理兼容性：接受字符串或对象参数
  let url = urlOrConfig;
  if (typeof urlOrConfig === 'object' && urlOrConfig !== null) {
    // 如果传入的是对象，尝试从对象中获取url
    url = urlOrConfig.url;
    
    // 合并options
    if (urlOrConfig.options) {
      options = { ...options, ...urlOrConfig.options };
    }
  }
  
  // 如果url不是字符串，抛出错误
  if (typeof url !== 'string') {
    const error = new Error('导航URL无效');
    console.error('smartNavigate 参数错误:', urlOrConfig);
    return Promise.reject(error);
  }
  
  // 判断是否是Tabbar页面
  const tabPages = [
    '/pages/index/index',
    '/pages/recommend/index',
    '/pages/publish/index',
    '/pages/message/index',
    '/pages/my/my'
  ];
  
  const isTabPage = tabPages.some(tabUrl => url === tabUrl || url.startsWith(tabUrl + '?'));
  
  // 根据页面类型选择导航方式
  if (isTabPage) {
    return switchTab(url, options);
  } else {
    return navigateTo(url, options);
  }
};

export default {
  navigateTo,
  redirectTo,
  switchTab,
  smartNavigate
}; 