{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/products/reviews/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNccHJvZHVjdHNccmV2aWV3c1xpbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"reviews-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\">\r\n      <view class=\"navbar-bg\"></view>\r\n      <view class=\"navbar-content\">\r\n        <view class=\"navbar-left\" @click=\"goBack\">\r\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n            <path d=\"M19 12H5M12 19l-7-7 7-7\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n          </svg>\r\n        </view>\r\n        <view class=\"navbar-title\">商品评价</view>\r\n        <view class=\"navbar-right\"></view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 内容区域 -->\r\n    <scroll-view \r\n      class=\"content-scroll\" \r\n      scroll-y \r\n      @scrolltolower=\"loadMore\"\r\n    >\r\n      <!-- 商品信息 -->\r\n      <view class=\"product-info\">\r\n        <image class=\"product-image\" :src=\"product.image\" mode=\"aspectFill\"></image>\r\n        <view class=\"product-detail\">\r\n          <view class=\"product-name\">{{ product.name }}</view>\r\n          <view class=\"product-price\">¥{{ product.price.toFixed(2) }}</view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 评价统计 -->\r\n      <view class=\"review-stats\">\r\n        <view class=\"stats-header\">\r\n          <view class=\"stats-title\">商品评价({{ totalReviews }})</view>\r\n          <view class=\"positive-rate\">好评率 {{ positiveRate }}%</view>\r\n        </view>\r\n        \r\n        <view class=\"tag-list\">\r\n          <view \r\n            class=\"tag-item\" \r\n            :class=\"{ active: activeTag === tag.id }\" \r\n            v-for=\"tag in reviewTags\" \r\n            :key=\"tag.id\"\r\n            @click=\"selectTag(tag.id)\"\r\n          >\r\n            {{ tag.name }}({{ tag.count }})\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 评价列表 -->\r\n      <view class=\"review-list\" v-if=\"reviews.length > 0\">\r\n        <view class=\"review-item\" v-for=\"(review, index) in reviews\" :key=\"index\">\r\n          <view class=\"review-header\">\r\n            <image class=\"user-avatar\" :src=\"review.userAvatar\" mode=\"aspectFill\"></image>\r\n            <view class=\"user-info\">\r\n              <view class=\"user-name\">{{ review.userName }}</view>\r\n              <view class=\"review-time\">{{ review.time }}</view>\r\n            </view>\r\n            <view class=\"rating\">\r\n              <view \r\n                class=\"star\" \r\n                v-for=\"i in 5\" \r\n                :key=\"i\" \r\n                :class=\"{ filled: i <= review.rating }\"\r\n              ></view>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"review-content\">{{ review.content }}</view>\r\n          \r\n          <view class=\"review-images\" v-if=\"review.images && review.images.length > 0\">\r\n            <image \r\n              class=\"review-image\" \r\n              v-for=\"(image, imgIndex) in review.images\" \r\n              :key=\"imgIndex\" \r\n              :src=\"image\" \r\n              mode=\"aspectFill\"\r\n              @click=\"previewImage(review.images, imgIndex)\"\r\n            ></image>\r\n          </view>\r\n          \r\n          <view class=\"review-specs\" v-if=\"review.specs\">{{ review.specs }}</view>\r\n          \r\n          <view class=\"shop-reply\" v-if=\"review.shopReply\">\r\n            <view class=\"reply-title\">商家回复：</view>\r\n            <view class=\"reply-content\">{{ review.shopReply }}</view>\r\n          </view>\r\n          \r\n          <view class=\"review-actions\">\r\n            <view class=\"action-item\" @click=\"toggleLike(review)\">\r\n              <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\" :class=\"{ active: review.isLiked }\">\r\n                <path d=\"M14 9V5a3 3 0 00-3-3l-4 9v11h11.28a2 2 0 002-1.7l1.38-9a2 2 0 00-2-2.3H14zM7 22H4a2 2 0 01-2-2v-7a2 2 0 012-2h3\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n              <text>{{ review.likes || 0 }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 空评价 -->\r\n      <view class=\"empty-reviews\" v-else>\r\n        <image class=\"empty-icon\" src=\"/static/images/empty-reviews.png\" mode=\"aspectFit\"></image>\r\n        <view class=\"empty-text\">暂无相关评价</view>\r\n      </view>\r\n      \r\n      <!-- 底部安全区域 -->\r\n      <view class=\"safe-area-bottom\"></view>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue';\r\n\r\n// 商品ID\r\nconst productId = ref('');\r\n\r\n// 商品信息\r\nconst product = ref({\r\n  id: '',\r\n  name: '商品加载中...',\r\n  price: 0,\r\n  image: 'https://via.placeholder.com/300x300'\r\n});\r\n\r\n// 评价标签\r\nconst reviewTags = ref([\r\n  { id: 'all', name: '全部', count: 235 },\r\n  { id: 'positive', name: '好评', count: 220 },\r\n  { id: 'neutral', name: '中评', count: 10 },\r\n  { id: 'negative', name: '差评', count: 5 },\r\n  { id: 'hasImage', name: '有图', count: 120 },\r\n  { id: 'hasVideo', name: '有视频', count: 30 }\r\n]);\r\n\r\n// 当前选中的标签\r\nconst activeTag = ref('all');\r\n\r\n// 评价列表\r\nconst reviews = ref([]);\r\n\r\n// 评价总数\r\nconst totalReviews = computed(() => {\r\n  return reviewTags.value.find(tag => tag.id === 'all')?.count || 0;\r\n});\r\n\r\n// 好评率\r\nconst positiveRate = computed(() => {\r\n  const positive = reviewTags.value.find(tag => tag.id === 'positive')?.count || 0;\r\n  const total = totalReviews.value;\r\n  return total > 0 ? Math.round((positive / total) * 100) : 100;\r\n});\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 选择标签\r\nconst selectTag = (tagId) => {\r\n  activeTag.value = tagId;\r\n  loadReviews();\r\n};\r\n\r\n// 切换点赞状态\r\nconst toggleLike = (review) => {\r\n  review.isLiked = !review.isLiked;\r\n  if (review.isLiked) {\r\n    review.likes = (review.likes || 0) + 1;\r\n  } else {\r\n    review.likes = Math.max(0, (review.likes || 0) - 1);\r\n  }\r\n};\r\n\r\n// 预览图片\r\nconst previewImage = (images, current) => {\r\n  uni.previewImage({\r\n    urls: images,\r\n    current: images[current]\r\n  });\r\n};\r\n\r\n// 加载更多\r\nconst loadMore = () => {\r\n  // 模拟加载更多数据\r\n  uni.showToast({\r\n    title: '已加载全部评价',\r\n    icon: 'none'\r\n  });\r\n};\r\n\r\n// 加载评价\r\nconst loadReviews = () => {\r\n  // 模拟评价数据\r\n  reviews.value = [\r\n    {\r\n      id: '1',\r\n      userName: '用户***123',\r\n      userAvatar: 'https://via.placeholder.com/100',\r\n      rating: 5,\r\n      content: '手机质量很好，拍照效果出色，电池续航也不错，总体来说非常满意！屏幕显示效果特别好，色彩鲜艳，动态岛设计很有创意。',\r\n      time: '2023-09-15',\r\n      specs: '颜色：深空黑；内存：256G',\r\n      images: [\r\n        'https://via.placeholder.com/300x300',\r\n        'https://via.placeholder.com/300x300',\r\n        'https://via.placeholder.com/300x300'\r\n      ],\r\n      likes: 12,\r\n      isLiked: false\r\n    },\r\n    {\r\n      id: '2',\r\n      userName: '张**',\r\n      userAvatar: 'https://via.placeholder.com/100',\r\n      rating: 4,\r\n      content: '手机整体不错，就是电池续航一般，重度使用一天需要充两次电。相机效果很棒，尤其是夜景模式表现优秀。',\r\n      time: '2023-09-10',\r\n      specs: '颜色：银色；内存：256G',\r\n      images: [\r\n        'https://via.placeholder.com/300x300',\r\n        'https://via.placeholder.com/300x300'\r\n      ],\r\n      shopReply: '感谢您的评价！关于电池续航问题，建议您检查是否有后台应用过度耗电，或者可以尝试更新到最新系统版本，这可能会优化电池性能。如有其他问题，欢迎随时联系我们的客服。',\r\n      likes: 8,\r\n      isLiked: false\r\n    },\r\n    {\r\n      id: '3',\r\n      userName: '李**',\r\n      userAvatar: 'https://via.placeholder.com/100',\r\n      rating: 5,\r\n      content: '物流超快，第二天就收到了。包装很好，手机外观漂亮，系统流畅，总之很满意的一次购物体验！',\r\n      time: '2023-09-05',\r\n      specs: '颜色：紫色；内存：512G',\r\n      likes: 5,\r\n      isLiked: false\r\n    },\r\n    {\r\n      id: '4',\r\n      userName: '王**',\r\n      userAvatar: 'https://via.placeholder.com/100',\r\n      rating: 3,\r\n      content: '手机还行吧，但感觉不值这个价，性能提升不是很明显，还不如用旧手机多用几年。',\r\n      time: '2023-09-01',\r\n      specs: '颜色：深空黑；内存：128G',\r\n      shopReply: '感谢您的反馈。我们的新款手机在处理器、相机系统和屏幕显示等方面都有显著提升。如果您有任何使用问题，欢迎联系我们的售后服务团队，我们将竭诚为您服务。',\r\n      likes: 2,\r\n      isLiked: false\r\n    }\r\n  ];\r\n};\r\n\r\n// 获取商品信息\r\nconst getProductInfo = (id) => {\r\n  // 模拟获取商品数据\r\n  setTimeout(() => {\r\n    product.value = {\r\n      id: id,\r\n      name: 'iPhone 14 Pro 深空黑 256G',\r\n      price: 7999,\r\n      image: 'https://via.placeholder.com/300x300'\r\n    };\r\n  }, 500);\r\n};\r\n\r\nonMounted(() => {\r\n  const query = uni.getSystemInfoSync().platform === 'devtools' \r\n    ? { id: '1' } // 开发环境模拟数据\r\n    : uni.$u.route.query;\r\n    \r\n  if (query.id) {\r\n    productId.value = query.id;\r\n    getProductInfo(query.id);\r\n  }\r\n  \r\n  loadReviews();\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.reviews-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  background-color: #F2F2F7;\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.custom-navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: calc(var(--status-bar-height, 25px) + 62px);\r\n  width: 100%;\r\n  z-index: 100;\r\n  \r\n  .navbar-bg {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);\r\n    backdrop-filter: blur(10px);\r\n    -webkit-backdrop-filter: blur(10px);\r\n    box-shadow: 0 4px 6px rgba(255,59,105,0.15);\r\n  }\r\n  \r\n  .navbar-content {\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: 100%;\r\n    padding: 0 30rpx;\r\n    padding-top: var(--status-bar-height, 25px);\r\n    box-sizing: border-box;\r\n  }\r\n  \r\n  .navbar-left, .navbar-right {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    width: 80rpx;\r\n    height: 80rpx;\r\n  }\r\n  \r\n  .navbar-title {\r\n    font-size: 36rpx;\r\n    font-weight: 600;\r\n    color: #FFFFFF;\r\n    letter-spacing: 0.5px;\r\n  }\r\n  \r\n  .icon {\r\n    width: 48rpx;\r\n    height: 48rpx;\r\n  }\r\n}\r\n\r\n/* 内容区域 */\r\n.content-scroll {\r\n  flex: 1;\r\n  margin-top: calc(var(--status-bar-height, 25px) + 62px);\r\n}\r\n\r\n/* 商品信息 */\r\n.product-info {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20rpx 30rpx;\r\n  background-color: #FFFFFF;\r\n  \r\n  .product-image {\r\n    width: 120rpx;\r\n    height: 120rpx;\r\n    border-radius: 8rpx;\r\n  }\r\n  \r\n  .product-detail {\r\n    flex: 1;\r\n    margin-left: 20rpx;\r\n    \r\n    .product-name {\r\n      font-size: 28rpx;\r\n      color: #333333;\r\n      line-height: 1.4;\r\n      display: -webkit-box;\r\n      -webkit-box-orient: vertical;\r\n      -webkit-line-clamp: 2;\r\n      overflow: hidden;\r\n    }\r\n    \r\n    .product-price {\r\n      font-size: 28rpx;\r\n      color: #FF3B69;\r\n      font-weight: 600;\r\n      margin-top: 10rpx;\r\n    }\r\n  }\r\n}\r\n\r\n/* 评价统计 */\r\n.review-stats {\r\n  margin-top: 20rpx;\r\n  padding: 20rpx 30rpx;\r\n  background-color: #FFFFFF;\r\n  \r\n  .stats-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 20rpx;\r\n    \r\n    .stats-title {\r\n      font-size: 32rpx;\r\n      font-weight: 600;\r\n      color: #333333;\r\n    }\r\n    \r\n    .positive-rate {\r\n      font-size: 28rpx;\r\n      color: #FF3B69;\r\n    }\r\n  }\r\n  \r\n  .tag-list {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    \r\n    .tag-item {\r\n      margin-right: 20rpx;\r\n      margin-bottom: 20rpx;\r\n      padding: 10rpx 20rpx;\r\n      border-radius: 30rpx;\r\n      font-size: 24rpx;\r\n      color: #666666;\r\n      background-color: #F2F2F7;\r\n      \r\n      &.active {\r\n        color: #FFFFFF;\r\n        background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 评价列表 */\r\n.review-list {\r\n  margin-top: 20rpx;\r\n  background-color: #FFFFFF;\r\n}\r\n\r\n.review-item {\r\n  padding: 30rpx;\r\n  border-bottom: 1rpx solid #F2F2F7;\r\n  \r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n  \r\n  .review-header {\r\n    display: flex;\r\n    align-items: center;\r\n    \r\n    .user-avatar {\r\n      width: 60rpx;\r\n      height: 60rpx;\r\n      border-radius: 50%;\r\n    }\r\n    \r\n    .user-info {\r\n      flex: 1;\r\n      margin-left: 20rpx;\r\n      \r\n      .user-name {\r\n        font-size: 28rpx;\r\n        color: #333333;\r\n      }\r\n      \r\n      .review-time {\r\n        font-size: 24rpx;\r\n        color: #999999;\r\n        margin-top: 4rpx;\r\n      }\r\n    }\r\n    \r\n    .rating {\r\n      display: flex;\r\n      \r\n      .star {\r\n        width: 24rpx;\r\n        height: 24rpx;\r\n        margin-left: 4rpx;\r\n        background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23E0E0E0'%3E%3Cpath d='M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z'/%3E%3C/svg%3E\");\r\n        background-size: contain;\r\n        background-repeat: no-repeat;\r\n        \r\n        &.filled {\r\n          background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF9500'%3E%3Cpath d='M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z'/%3E%3C/svg%3E\");\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .review-content {\r\n    margin-top: 20rpx;\r\n    font-size: 28rpx;\r\n    color: #333333;\r\n    line-height: 1.6;\r\n  }\r\n  \r\n  .review-images {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    margin-top: 20rpx;\r\n    \r\n    .review-image {\r\n      width: 160rpx;\r\n      height: 160rpx;\r\n      margin-right: 10rpx;\r\n      margin-bottom: 10rpx;\r\n      border-radius: 8rpx;\r\n    }\r\n  }\r\n  \r\n  .review-specs {\r\n    margin-top: 20rpx;\r\n    font-size: 24rpx;\r\n    color: #999999;\r\n  }\r\n  \r\n  .shop-reply {\r\n    margin-top: 20rpx;\r\n    padding: 20rpx;\r\n    background-color: #F9F9F9;\r\n    border-radius: 8rpx;\r\n    \r\n    .reply-title {\r\n      font-size: 24rpx;\r\n      color: #666666;\r\n      font-weight: 600;\r\n    }\r\n    \r\n    .reply-content {\r\n      margin-top: 10rpx;\r\n      font-size: 24rpx;\r\n      color: #666666;\r\n      line-height: 1.6;\r\n    }\r\n  }\r\n  \r\n  .review-actions {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    margin-top: 20rpx;\r\n    \r\n    .action-item {\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 10rpx;\r\n      \r\n      .icon {\r\n        color: #999999;\r\n        margin-right: 6rpx;\r\n        \r\n        &.active {\r\n          color: #FF3B69;\r\n        }\r\n      }\r\n      \r\n      text {\r\n        font-size: 24rpx;\r\n        color: #999999;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 空评价 */\r\n.empty-reviews {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 100rpx 0;\r\n  background-color: #FFFFFF;\r\n  margin-top: 20rpx;\r\n  \r\n  .empty-icon {\r\n    width: 200rpx;\r\n    height: 200rpx;\r\n    margin-bottom: 30rpx;\r\n  }\r\n  \r\n  .empty-text {\r\n    font-size: 28rpx;\r\n    color: #999999;\r\n  }\r\n}\r\n\r\n/* 底部安全区域 */\r\n.safe-area-bottom {\r\n  height: 34px; /* iOS 安全区域高度 */\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/products/reviews/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "onMounted"], "mappings": ";;;;;;;;;;;AAqHA,UAAM,YAAYA,cAAAA,IAAI,EAAE;AAGxB,UAAM,UAAUA,cAAAA,IAAI;AAAA,MAClB,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,IACT,CAAC;AAGD,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACrB,EAAE,IAAI,OAAO,MAAM,MAAM,OAAO,IAAK;AAAA,MACrC,EAAE,IAAI,YAAY,MAAM,MAAM,OAAO,IAAK;AAAA,MAC1C,EAAE,IAAI,WAAW,MAAM,MAAM,OAAO,GAAI;AAAA,MACxC,EAAE,IAAI,YAAY,MAAM,MAAM,OAAO,EAAG;AAAA,MACxC,EAAE,IAAI,YAAY,MAAM,MAAM,OAAO,IAAK;AAAA,MAC1C,EAAE,IAAI,YAAY,MAAM,OAAO,OAAO,GAAI;AAAA,IAC5C,CAAC;AAGD,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAG3B,UAAM,UAAUA,cAAAA,IAAI,CAAA,CAAE;AAGtB,UAAM,eAAeC,cAAQ,SAAC,MAAM;;AAClC,eAAO,gBAAW,MAAM,KAAK,SAAO,IAAI,OAAO,KAAK,MAA7C,mBAAgD,UAAS;AAAA,IAClE,CAAC;AAGD,UAAM,eAAeA,cAAQ,SAAC,MAAM;;AAClC,YAAM,aAAW,gBAAW,MAAM,KAAK,SAAO,IAAI,OAAO,UAAU,MAAlD,mBAAqD,UAAS;AAC/E,YAAM,QAAQ,aAAa;AAC3B,aAAO,QAAQ,IAAI,KAAK,MAAO,WAAW,QAAS,GAAG,IAAI;AAAA,IAC5D,CAAC;AAGD,UAAM,SAAS,MAAM;AACnBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,YAAY,CAAC,UAAU;AAC3B,gBAAU,QAAQ;AAClB;IACF;AAGA,UAAM,aAAa,CAAC,WAAW;AAC7B,aAAO,UAAU,CAAC,OAAO;AACzB,UAAI,OAAO,SAAS;AAClB,eAAO,SAAS,OAAO,SAAS,KAAK;AAAA,MACzC,OAAS;AACL,eAAO,QAAQ,KAAK,IAAI,IAAI,OAAO,SAAS,KAAK,CAAC;AAAA,MACnD;AAAA,IACH;AAGA,UAAM,eAAe,CAAC,QAAQ,YAAY;AACxCA,oBAAAA,MAAI,aAAa;AAAA,QACf,MAAM;AAAA,QACN,SAAS,OAAO,OAAO;AAAA,MAC3B,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AAErBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,cAAc,MAAM;AAExB,cAAQ,QAAQ;AAAA,QACd;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA,YACN;AAAA,YACA;AAAA,YACA;AAAA,UACD;AAAA,UACD,OAAO;AAAA,UACP,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA,YACN;AAAA,YACA;AAAA,UACD;AAAA,UACD,WAAW;AAAA,UACX,OAAO;AAAA,UACP,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,UACP,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW;AAAA,UACX,OAAO;AAAA,UACP,SAAS;AAAA,QACV;AAAA,MACL;AAAA,IACA;AAGA,UAAM,iBAAiB,CAAC,OAAO;AAE7B,iBAAW,MAAM;AACf,gBAAQ,QAAQ;AAAA,UACd;AAAA,UACA,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,QACb;AAAA,MACG,GAAE,GAAG;AAAA,IACR;AAEAC,kBAAAA,UAAU,MAAM;AACd,YAAM,QAAQD,cAAG,MAAC,kBAAmB,EAAC,aAAa,aAC/C,EAAE,IAAI,IAAK,IACXA,oBAAI,GAAG,MAAM;AAEjB,UAAI,MAAM,IAAI;AACZ,kBAAU,QAAQ,MAAM;AACxB,uBAAe,MAAM,EAAE;AAAA,MACxB;AAED;IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtRD,GAAG,WAAW,eAAe;"}