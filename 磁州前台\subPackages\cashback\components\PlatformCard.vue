<template>
  <view class="platform-card" @tap="navigateToPlatform">
    <view class="platform-logo-container">
      <image class="platform-logo" :src="platform.logo" mode="aspectFit" />
    </view>
    <view class="platform-info">
      <view class="platform-name">
        <text>{{ platform.name }}</text>
      </view>
      <view class="platform-cashback">
        <text>最高返{{ platform.maxCashbackRate }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'PlatformCard',
  props: {
    platform: {
      type: Object,
      required: true
    }
  },
  methods: {
    navigateToPlatform() {
      uni.navigateTo({
        url: `/subPackages/cashback/pages/platform-detail/index?id=${this.platform.id}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.platform-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  
  .platform-logo-container {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    overflow: hidden;
    margin-bottom: 12px;
    background-color: #F5F7FA;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .platform-logo {
      width: 48px;
      height: 48px;
    }
  }
  
  .platform-info {
    width: 100%;
    text-align: center;
  }
  
  .platform-name {
    margin-bottom: 6px;
    
    text {
      font-size: 14px;
      color: #333333;
      font-weight: 500;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  
  .platform-cashback {
    padding: 2px 8px;
    background-color: rgba(156, 39, 176, 0.1);
    border-radius: 10px;
    display: inline-block;
    
    text {
      font-size: 12px;
      color: #9C27B0;
    }
  }
}
</style> 