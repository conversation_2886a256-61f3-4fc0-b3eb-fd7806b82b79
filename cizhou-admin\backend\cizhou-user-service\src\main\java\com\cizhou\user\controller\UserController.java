package com.cizhou.user.controller;

import com.cizhou.common.core.page.PageQuery;
import com.cizhou.common.core.page.PageResult;
import com.cizhou.common.core.result.Result;
import com.cizhou.user.dto.UserQueryDTO;
import com.cizhou.user.dto.UserUpdateDTO;
import com.cizhou.user.service.UserService;
import com.cizhou.user.vo.UserDetailVO;
import com.cizhou.user.vo.UserListVO;
import com.cizhou.user.vo.UserStatsVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户管理控制器
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/users")
@RequiredArgsConstructor
@Tag(name = "用户管理", description = "C端用户管理相关接口")
public class UserController {

    private final UserService userService;

    @Operation(summary = "获取用户列表")
    @GetMapping
    public Result<PageResult<UserListVO>> getUserList(@Valid UserQueryDTO queryDTO, PageQuery pageQuery) {
        log.info("获取用户列表，查询条件: {}", queryDTO);
        PageResult<UserListVO> result = userService.getUserList(queryDTO, pageQuery);
        return Result.success("获取用户列表成功", result);
    }

    @Operation(summary = "获取用户详情")
    @GetMapping("/{id}")
    public Result<UserDetailVO> getUserDetail(@Parameter(description = "用户ID") @PathVariable Long id) {
        log.info("获取用户详情，用户ID: {}", id);
        UserDetailVO result = userService.getUserDetail(id);
        return Result.success("获取用户详情成功", result);
    }

    @Operation(summary = "更新用户信息")
    @PutMapping("/{id}")
    public Result<Void> updateUser(@Parameter(description = "用户ID") @PathVariable Long id,
                                   @Valid @RequestBody UserUpdateDTO updateDTO) {
        log.info("更新用户信息，用户ID: {}, 更新内容: {}", id, updateDTO);
        userService.updateUser(id, updateDTO);
        return Result.success("用户信息更新成功");
    }

    @Operation(summary = "更新用户状态")
    @PutMapping("/{id}/status")
    public Result<Void> updateUserStatus(@Parameter(description = "用户ID") @PathVariable Long id,
                                         @Parameter(description = "状态") @RequestParam Integer status) {
        log.info("更新用户状态，用户ID: {}, 状态: {}", id, status);
        userService.updateUserStatus(id, status);
        return Result.success("用户状态更新成功");
    }

    @Operation(summary = "批量更新用户状态")
    @PutMapping("/batch/status")
    public Result<Void> batchUpdateUserStatus(@RequestBody List<Long> ids,
                                              @Parameter(description = "状态") @RequestParam Integer status) {
        log.info("批量更新用户状态，用户IDs: {}, 状态: {}", ids, status);
        userService.batchUpdateUserStatus(ids, status);
        return Result.success("批量更新用户状态成功");
    }

    @Operation(summary = "重置用户密码")
    @PostMapping("/{id}/reset-password")
    public Result<String> resetUserPassword(@Parameter(description = "用户ID") @PathVariable Long id) {
        log.info("重置用户密码，用户ID: {}", id);
        String newPassword = userService.resetUserPassword(id);
        return Result.success("密码重置成功", newPassword);
    }

    @Operation(summary = "删除用户")
    @DeleteMapping("/{id}")
    public Result<Void> deleteUser(@Parameter(description = "用户ID") @PathVariable Long id) {
        log.info("删除用户，用户ID: {}", id);
        userService.deleteUser(id);
        return Result.success("用户删除成功");
    }

    @Operation(summary = "批量删除用户")
    @DeleteMapping("/batch")
    public Result<Void> batchDeleteUsers(@RequestBody List<Long> ids) {
        log.info("批量删除用户，用户IDs: {}", ids);
        userService.batchDeleteUsers(ids);
        return Result.success("批量删除用户成功");
    }

    @Operation(summary = "获取用户统计数据")
    @GetMapping("/stats")
    public Result<UserStatsVO> getUserStats() {
        log.info("获取用户统计数据");
        UserStatsVO result = userService.getUserStats();
        return Result.success("获取用户统计数据成功", result);
    }

    @Operation(summary = "导出用户数据")
    @GetMapping("/export")
    public Result<String> exportUsers(@Valid UserQueryDTO queryDTO) {
        log.info("导出用户数据，查询条件: {}", queryDTO);
        String downloadUrl = userService.exportUsers(queryDTO);
        return Result.success("用户数据导出成功", downloadUrl);
    }
}
