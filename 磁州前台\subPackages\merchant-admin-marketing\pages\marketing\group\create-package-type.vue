<template>
  <view class="create-package-type-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">拼团活动</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 步骤指示器 -->
    <view class="step-indicator">
      <view class="step-progress">
        <view class="step-progress-bar" style="width: 20%"></view>
      </view>
      <view class="step-text">步骤 1/5</view>
    </view>
    
    <!-- 页面内容 -->
    <scroll-view scroll-y class="page-content">
      <view class="page-title">选择套餐类型</view>
      <view class="page-subtitle">请选择适合您业务的团购套餐类型</view>
      
      <view class="package-types">
        <view class="package-type" :class="{ active: selectedType === 'food' }" @tap="selectType('food')">
          <view class="type-icon food-icon"></view>
          <view class="type-info">
            <text class="type-name">餐饮套餐</text>
            <text class="type-desc">适合餐厅、饭店等餐饮商家</text>
          </view>
          <view class="type-check" v-if="selectedType === 'food'"></view>
        </view>
        
        <view class="package-type" :class="{ active: selectedType === 'service' }" @tap="selectType('service')">
          <view class="type-icon service-icon"></view>
          <view class="type-info">
            <text class="type-name">服务套餐</text>
            <text class="type-desc">适合美容、洗车等服务商家</text>
          </view>
          <view class="type-check" v-if="selectedType === 'service'"></view>
        </view>
        
        <view class="package-type" :class="{ active: selectedType === 'product' }" @tap="selectType('product')">
          <view class="type-icon product-icon"></view>
          <view class="type-info">
            <text class="type-name">商品套餐</text>
            <text class="type-desc">适合零售、超市等商品销售</text>
          </view>
          <view class="type-check" v-if="selectedType === 'product'"></view>
        </view>
        
        <view class="package-type" :class="{ active: selectedType === 'custom' }" @tap="selectType('custom')">
          <view class="type-icon custom-icon"></view>
          <view class="type-info">
            <text class="type-name">自定义套餐</text>
            <text class="type-desc">自由组合商品和服务</text>
          </view>
          <view class="type-check" v-if="selectedType === 'custom'"></view>
        </view>
      </view>
      
      <!-- 支付和核销设置 -->
      <view class="settings-section">
        <view class="section-title">支付和核销设置</view>
        
        <view class="setting-item">
          <view class="setting-label">支付方式</view>
          <view class="setting-options">
            <view class="option" :class="{ active: paymentType === 'offline' }" @tap="setPaymentType('offline')">
              <text>到店支付</text>
              <view class="option-check" v-if="paymentType === 'offline'"></view>
            </view>
            <view class="option" :class="{ active: paymentType === 'online' }" @tap="setPaymentType('online')">
              <text>在线支付</text>
              <view class="option-check" v-if="paymentType === 'online'"></view>
            </view>
          </view>
        </view>
        
        <view class="setting-item">
          <view class="setting-label">核销方式</view>
          <view class="setting-options">
            <view class="option" :class="{ active: verifyType === 'offline' }" @tap="setVerifyType('offline')">
              <text>到店核销</text>
              <view class="option-check" v-if="verifyType === 'offline'"></view>
            </view>
            <view class="option" :class="{ active: verifyType === 'online' }" @tap="setVerifyType('online')">
              <text>在线核销</text>
              <view class="option-check" v-if="verifyType === 'online'"></view>
            </view>
          </view>
        </view>
        
        <view class="setting-item" v-if="verifyType === 'offline'">
          <view class="setting-label">核销码有效期</view>
          <view class="setting-input">
            <input type="number" v-model="verifyCodeValidDays" class="days-input" />
            <text class="unit">天</text>
          </view>
        </view>
        
        <view class="setting-item" v-if="verifyType === 'offline'">
          <view class="setting-label">可核销次数</view>
          <view class="setting-input">
            <input type="number" v-model="verifyTimes" class="times-input" />
            <text class="unit">次</text>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部按钮 -->
    <view class="footer-buttons">
      <button class="btn btn-secondary" @click="goBack">取消</button>
      <button class="btn btn-primary" @click="nextStep">下一步</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      selectedType: 'food',
      paymentType: 'offline', // offline: 到店支付, online: 在线支付
      verifyType: 'offline', // offline: 到店核销, online: 在线核销
      verifyCodeValidDays: 30,
      verifyTimes: 1
    }
  },
  onLoad() {
    // 尝试从本地存储获取之前保存的数据
    try {
      const savedType = uni.getStorageSync('packageType');
      const savedPaymentInfo = uni.getStorageSync('packagePaymentInfo');
      
      if (savedType) {
        this.selectedType = savedType;
      }
      
      if (savedPaymentInfo) {
        const paymentInfo = JSON.parse(savedPaymentInfo);
        this.paymentType = paymentInfo.paymentType;
        this.verifyType = paymentInfo.verifyType;
        this.verifyCodeValidDays = paymentInfo.verifyCodeValidDays;
        this.verifyTimes = paymentInfo.verifyTimes;
      }
    } catch (e) {
      console.error('读取本地存储失败:', e);
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    selectType(type) {
      this.selectedType = type;
    },
    setPaymentType(type) {
      this.paymentType = type;
    },
    setVerifyType(type) {
      this.verifyType = type;
    },
    nextStep() {
      // 保存选择的套餐类型和支付核销设置
      try {
        uni.setStorageSync('packageType', this.selectedType);
        
        const paymentInfo = {
          paymentType: this.paymentType,
          verifyType: this.verifyType,
          verifyCodeValidDays: this.verifyCodeValidDays,
          verifyTimes: this.verifyTimes
        };
        
        uni.setStorageSync('packagePaymentInfo', JSON.stringify(paymentInfo));
      } catch (e) {
        console.error('保存数据失败:', e);
      }
      
      // 跳转到下一步
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-info'
      });
    },
    showHelp() {
      uni.showToast({
        title: '帮助信息',
        icon: 'none'
      });
    }
  }
}
</script>

<style lang="scss">
.create-package-type-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  display: flex;
  flex-direction: column;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #fff;
}

/* 步骤指示器 */
.step-indicator {
  padding: 15px;
  background: #FFFFFF;
}

.step-progress {
  height: 4px;
  background-color: #EBEDF5;
  border-radius: 2px;
  margin-bottom: 5px;
  position: relative;
}

.step-progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #9040FF, #5E35B1);
  border-radius: 2px;
}

.step-text {
  font-size: 12px;
  color: #999;
  text-align: right;
}

/* 页面内容 */
.page-content {
  flex: 1;
  padding: 20px 15px;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.page-subtitle {
  font-size: 14px;
  color: #999;
  margin-bottom: 20px;
}

/* 套餐类型样式 */
.package-types {
  margin-bottom: 20px;
}

.package-type {
  display: flex;
  align-items: center;
  background: #FFFFFF;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  position: relative;
}

.package-type.active {
  border: 2px solid #9040FF;
}

.type-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  margin-right: 15px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 24px;
}

.food-icon {
  background-color: #FFE8CC;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FF9500"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5-9h10v2H7z"/></svg>');
}

.service-icon {
  background-color: #E3F2FD;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%230A84FF"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5-9h10v2H7z"/></svg>');
}

.product-icon {
  background-color: #E8F5E9;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2334C759"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5-9h10v2H7z"/></svg>');
}

.custom-icon {
  background-color: #F0E8FF;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%239040FF"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5-9h10v2H7z"/></svg>');
}

.type-info {
  flex: 1;
}

.type-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  display: block;
}

.type-desc {
  font-size: 12px;
  color: #999;
}

.type-check {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #9040FF;
  position: absolute;
  top: 15px;
  right: 15px;
}

.type-check::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 10px;
  height: 5px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: translate(-50%, -60%) rotate(-45deg);
}

/* 设置区域样式 */
.settings-section {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px dashed #EBEDF5;
}

.setting-item {
  margin-bottom: 15px;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.setting-options {
  display: flex;
  gap: 15px;
}

.option {
  flex: 1;
  height: 40px;
  background: #F5F7FA;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #333;
  position: relative;
  border: 1px solid #EBEDF5;
}

.option.active {
  background: #F0E8FF;
  border-color: #9040FF;
  color: #9040FF;
}

.option-check {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #9040FF;
  position: absolute;
  top: -8px;
  right: -8px;
}

.option-check::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8px;
  height: 4px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: translate(-50%, -60%) rotate(-45deg);
}

.setting-input {
  display: flex;
  align-items: center;
}

.days-input, .times-input {
  width: 60px;
  height: 40px;
  background: #F5F7FA;
  border-radius: 8px;
  border: 1px solid #EBEDF5;
  text-align: center;
  font-size: 14px;
  color: #333;
  margin-right: 10px;
}

.unit {
  font-size: 14px;
  color: #666;
}

/* 底部按钮 */
.footer-buttons {
  padding: 15px;
  background: #FFFFFF;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  gap: 15px;
}

.btn {
  flex: 1;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #FFFFFF;
}

.btn-secondary {
  background: #F5F7FA;
  color: #666;
  border: 1px solid #EBEDF5;
}
</style>
