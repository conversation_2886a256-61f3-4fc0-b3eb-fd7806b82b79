{"version": 3, "file": "IconButton.js", "sources": ["subPackages/merchant-admin/components/ui/IconButton.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7Avc3ViUGFja2FnZXMvbWVyY2hhbnQtYWRtaW4vY29tcG9uZW50cy91aS9JY29uQnV0dG9uLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"icon-button\" :class=\"[type, size]\" @tap=\"onClick\">\r\n    <view class=\"icon-container\">\r\n      <svg-icon :name=\"icon\" :size=\"iconSize\" :color=\"iconColor\" />\r\n    </view>\r\n    <text v-if=\"text\" class=\"button-text\">{{ text }}</text>\r\n    <slot></slot>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport SvgIcon from './SvgIcon.vue';\r\n\r\nexport default {\r\n  name: 'IconButton',\r\n  components: {\r\n    SvgIcon\r\n  },\r\n  props: {\r\n    icon: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    text: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    type: {\r\n      type: String,\r\n      default: 'default', // default, primary, success, warning, danger\r\n      validator: value => ['default', 'primary', 'success', 'warning', 'danger'].includes(value)\r\n    },\r\n    size: {\r\n      type: String,\r\n      default: 'medium', // small, medium, large\r\n      validator: value => ['small', 'medium', 'large'].includes(value)\r\n    }\r\n  },\r\n  computed: {\r\n    iconSize() {\r\n      const sizes = {\r\n        small: 16,\r\n        medium: 20,\r\n        large: 24\r\n      };\r\n      return sizes[this.size] || 20;\r\n    },\r\n    iconColor() {\r\n      if (this.type === 'default') return '#666';\r\n      return 'white';\r\n    }\r\n  },\r\n  methods: {\r\n    onClick() {\r\n      this.$emit('click');\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.icon-button {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 8px;\r\n  padding: 8px 16px;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  \r\n  &.small {\r\n    padding: 6px 12px;\r\n    font-size: 12px;\r\n  }\r\n  \r\n  &.medium {\r\n    padding: 8px 16px;\r\n    font-size: 14px;\r\n  }\r\n  \r\n  &.large {\r\n    padding: 10px 20px;\r\n    font-size: 16px;\r\n  }\r\n  \r\n  &.default {\r\n    background-color: #F5F5F5;\r\n    color: #333;\r\n    \r\n    &:active {\r\n      background-color: #E5E5E5;\r\n    }\r\n  }\r\n  \r\n  &.primary {\r\n    background-color: #0A84FF;\r\n    color: white;\r\n    \r\n    &:active {\r\n      background-color: #0071E3;\r\n    }\r\n  }\r\n  \r\n  &.success {\r\n    background-color: #34C759;\r\n    color: white;\r\n    \r\n    &:active {\r\n      background-color: #30B352;\r\n    }\r\n  }\r\n  \r\n  &.warning {\r\n    background-color: #FF9500;\r\n    color: white;\r\n    \r\n    &:active {\r\n      background-color: #E68600;\r\n    }\r\n  }\r\n  \r\n  &.danger {\r\n    background-color: #FF3B30;\r\n    color: white;\r\n    \r\n    &:active {\r\n      background-color: #E63028;\r\n    }\r\n  }\r\n}\r\n\r\n.icon-container {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 6px;\r\n}\r\n\r\n.button-text {\r\n  font-weight: 500;\r\n}\r\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin/components/ui/IconButton.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;AAWA,MAAO,UAAS,MAAW;AAE3B,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,UAAU;AAAA,IACX;AAAA,IACD,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA;AAAA,MACT,WAAW,WAAS,CAAC,WAAW,WAAW,WAAW,WAAW,QAAQ,EAAE,SAAS,KAAK;AAAA,IAC1F;AAAA,IACD,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA;AAAA,MACT,WAAW,WAAS,CAAC,SAAS,UAAU,OAAO,EAAE,SAAS,KAAK;AAAA,IACjE;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,WAAW;AACT,YAAM,QAAQ;AAAA,QACZ,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,OAAO;AAAA;AAET,aAAO,MAAM,KAAK,IAAI,KAAK;AAAA,IAC5B;AAAA,IACD,YAAY;AACV,UAAI,KAAK,SAAS;AAAW,eAAO;AACpC,aAAO;AAAA,IACT;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,UAAU;AACR,WAAK,MAAM,OAAO;AAAA,IACpB;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;ACxDA,GAAG,gBAAgB,SAAS;"}