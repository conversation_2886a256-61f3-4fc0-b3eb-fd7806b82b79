"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_redPacket = require("../../utils/redPacket.js");
const utils_format = require("../../utils/format.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      tabs: [
        { name: "收到的红包", type: "received" },
        { name: "发出的红包", type: "sent" }
      ],
      currentTab: 0,
      redPacketList: [],
      page: 1,
      pageSize: 20,
      loading: false,
      hasMore: true,
      cache: /* @__PURE__ */ new Map()
      // 缓存机制
    };
  },
  created() {
    this.loadData();
  },
  methods: {
    // 切换标签页
    switchTab(index) {
      if (this.currentTab === index)
        return;
      this.currentTab = index;
      this.page = 1;
      this.redPacketList = [];
      this.hasMore = true;
      this.loadData();
    },
    // 加载数据
    async loadData() {
      if (this.loading || !this.hasMore)
        return;
      this.loading = true;
      const type = this.tabs[this.currentTab].type;
      const cacheKey = `${type}_${this.page}`;
      try {
        if (this.cache.has(cacheKey)) {
          const cachedData = this.cache.get(cacheKey);
          this.redPacketList = [...this.redPacketList, ...cachedData.list];
          this.hasMore = cachedData.hasMore;
        } else {
          const res = await utils_redPacket.getMyRedPackets({
            type,
            page: this.page,
            pageSize: this.pageSize
          });
          this.redPacketList = [...this.redPacketList, ...res.list];
          this.hasMore = res.hasMore;
          this.cache.set(cacheKey, {
            list: res.list,
            hasMore: res.hasMore
          });
        }
        this.page++;
      } catch (err) {
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 下拉刷新
    async onRefresh() {
      this.page = 1;
      this.redPacketList = [];
      this.hasMore = true;
      this.cache.clear();
      await this.loadData();
      common_vendor.index.stopPullDownRefresh();
    },
    // 加载更多
    loadMore() {
      this.loadData();
    },
    // 查看红包详情
    viewRedPacketDetail(item) {
      common_vendor.index.navigateTo({
        url: `/pages/red-packet/detail?id=${item.id}`
      });
    },
    // 格式化时间
    formatTime: utils_format.formatTime,
    // 格式化金额
    formatAmount: utils_format.formatAmount,
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        pending: "待领取",
        received: "已领取",
        expired: "已过期",
        completed: "已领完"
      };
      return statusMap[status] || status;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.tabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab.name),
        b: index,
        c: $data.currentTab === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    }),
    b: common_vendor.f($data.redPacketList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.title),
        b: common_vendor.t($options.formatTime(item.createTime)),
        c: item.type === "received"
      }, item.type === "received" ? {
        d: common_vendor.t($options.formatAmount(item.amount))
      } : {
        e: common_vendor.t($options.formatAmount(item.totalAmount))
      }, {
        f: common_vendor.t($options.getStatusText(item.status)),
        g: common_vendor.n(item.status),
        h: index,
        i: common_vendor.o(($event) => $options.viewRedPacketDetail(item), index)
      });
    }),
    c: $data.loading
  }, $data.loading ? {} : {}, {
    d: !$data.hasMore && $data.redPacketList.length > 0
  }, !$data.hasMore && $data.redPacketList.length > 0 ? {} : {}, {
    e: !$data.loading && $data.redPacketList.length === 0
  }, !$data.loading && $data.redPacketList.length === 0 ? {
    f: common_assets._imports_0$21
  } : {}, {
    g: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args)),
    h: common_vendor.o((...args) => $options.onRefresh && $options.onRefresh(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user/my-red-packets.js.map
