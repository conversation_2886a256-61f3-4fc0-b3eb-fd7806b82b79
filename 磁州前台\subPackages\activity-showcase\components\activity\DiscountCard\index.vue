<template>
  <!-- 满减活动卡片 - 苹果风格设计 -->
  <view class="discount-card">
    <!-- 使用基础活动卡片 -->
    <ActivityCard 
      :item="item" 
      @favorite="$emit('favorite', item.id)"
      @action="$emit('action', { id: item.id, type: item.type, status: item.status })"
    >
      <!-- 满减特有信息插槽 -->
      <template #special-info>
        <view class="discount-special">
          <!-- 满减规则区域 -->
          <view class="discount-rules">
            <view class="rules-header">
              <view class="rules-title">满减规则</view>
              <view class="merchant-count" v-if="item.merchantCount">
                <text>{{item.merchantCount}}家商家参与</text>
              </view>
            </view>
            
            <view class="rules-list">
              <view 
                class="rule-item" 
                v-for="(rule, index) in item.discountRules" 
                :key="index"
                :class="{'best-rule': index === item.discountRules.length - 1}"
              >
                <view class="rule-badge" v-if="index === item.discountRules.length - 1">
                  <text>最优</text>
                </view>
                <view class="rule-content">
                  <text class="rule-text">满{{rule.threshold}}减{{rule.discount}}</text>
                  <text class="rule-save">立省{{rule.discount}}元</text>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 活动时间 -->
          <view class="discount-period" v-if="item.startDate && item.endDate">
            <view class="period-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="16" y1="2" x2="16" y2="6"></line>
                <line x1="8" y1="2" x2="8" y2="6"></line>
                <line x1="3" y1="10" x2="21" y2="10"></line>
              </svg>
            </view>
            <text class="period-text">活动时间: {{item.startDate}} - {{item.endDate}}</text>
          </view>
          
          <!-- 使用范围 -->
          <view class="discount-scope" v-if="item.scope">
            <view class="scope-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
              </svg>
            </view>
            <text class="scope-text">{{item.scope}}</text>
          </view>
          
          <!-- 参与商家 -->
          <view class="discount-merchants" v-if="item.merchants && item.merchants.length > 0">
            <view class="merchants-header">
              <text class="merchants-title">参与商家</text>
              <text class="merchants-count">共{{item.merchants.length}}家</text>
            </view>
            <view class="merchants-list">
              <view 
                class="merchant-item" 
                v-for="(merchant, index) in displayMerchants" 
                :key="index"
              >
                <image 
                  :src="merchant.logo" 
                  class="merchant-logo" 
                  mode="aspectFill"
                ></image>
                <text class="merchant-name">{{merchant.name}}</text>
              </view>
              <view class="more-merchants" v-if="item.merchants.length > maxDisplayMerchants">
                <text>更多 ></text>
              </view>
            </view>
          </view>
        </view>
      </template>
    </ActivityCard>
  </view>
</template>

<script setup>
import { computed } from 'vue';
import ActivityCard from '../ActivityCard.vue';

const props = defineProps({
  item: {
    type: Object,
    required: true
  }
});

// 最大显示商家数量
const maxDisplayMerchants = 3;

// 显示的商家
const displayMerchants = computed(() => {
  if (!props.item.merchants || !props.item.merchants.length) return [];
  return props.item.merchants.slice(0, maxDisplayMerchants);
});
</script>

<style scoped>
/* 满减活动卡片特有样式 */
.discount-card {
  /* 继承基础卡片样式 */
}

/* 满减特有信息区域 */
.discount-special {
  padding: 20rpx;
  background-color: rgba(90, 200, 250, 0.05);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
}

/* 满减规则区域 */
.discount-rules {
  margin-bottom: 20rpx;
}

.rules-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.rules-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
}

.merchant-count {
  font-size: 22rpx;
  color: #5ac8fa;
  background-color: rgba(90, 200, 250, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
}

.rules-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.rule-item {
  position: relative;
  background-color: rgba(90, 200, 250, 0.08);
  border-radius: 16rpx;
  padding: 12rpx 16rpx;
  border: 1rpx solid rgba(90, 200, 250, 0.2);
  transition: all 0.3s ease;
}

.best-rule {
  background-color: rgba(90, 200, 250, 0.15);
  border: 1rpx solid rgba(90, 200, 250, 0.4);
  transform: scale(1.02);
}

.rule-badge {
  position: absolute;
  top: -10rpx;
  right: 16rpx;
  background-color: #5ac8fa;
  color: #ffffff;
  font-size: 20rpx;
  font-weight: 600;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 8rpx rgba(90, 200, 250, 0.3);
}

.rule-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rule-text {
  font-size: 26rpx;
  color: #333333;
  font-weight: 600;
}

.rule-save {
  font-size: 22rpx;
  color: #5ac8fa;
}

/* 活动时间 */
.discount-period {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.period-icon {
  margin-right: 8rpx;
  color: #5ac8fa;
}

.period-text {
  font-size: 24rpx;
  color: #333333;
}

/* 使用范围 */
.discount-scope {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.scope-icon {
  margin-right: 8rpx;
  color: #5ac8fa;
}

.scope-text {
  font-size: 24rpx;
  color: #333333;
}

/* 参与商家 */
.discount-merchants {
  margin-top: 16rpx;
  border-top: 1rpx dashed rgba(90, 200, 250, 0.3);
  padding-top: 16rpx;
}

.merchants-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.merchants-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
}

.merchants-count {
  font-size: 22rpx;
  color: #5ac8fa;
}

.merchants-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.merchant-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: calc((100% - 32rpx) / 3);
}

.merchant-logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  border: 1rpx solid rgba(90, 200, 250, 0.2);
  margin-bottom: 8rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
}

.merchant-name {
  font-size: 22rpx;
  color: #333333;
  width: 100%;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.more-merchants {
  display: flex;
  justify-content: center;
  align-items: center;
  width: calc((100% - 32rpx) / 3);
  height: 110rpx;
}

.more-merchants text {
  font-size: 24rpx;
  color: #5ac8fa;
}
</style> 