{"version": 3, "file": "qrcode.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/distribution/qrcode.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xkaXN0cmlidXRpb25ccXJjb2RlLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"qrcode-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">推广二维码</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 二维码预览 -->\n    <view class=\"qrcode-preview-section\">\n      <view class=\"preview-card\" :style=\"{ backgroundColor: selectedTheme.bgColor }\">\n        <view class=\"preview-header\">\n          <image class=\"store-logo\" :src=\"storeInfo.logo\" mode=\"aspectFill\"></image>\n          <view class=\"store-info\">\n            <text class=\"store-name\">{{storeInfo.name}}</text>\n            <text class=\"store-slogan\">{{storeInfo.slogan}}</text>\n          </view>\n        </view>\n        \n        <view class=\"qrcode-wrapper\">\n          <view class=\"qrcode-image-container\">\n            <image class=\"qrcode-image\" :src=\"currentQrcode\" mode=\"aspectFit\"></image>\n            <view class=\"qrcode-logo\" v-if=\"showLogo\">\n              <image :src=\"storeInfo.logo\" mode=\"aspectFill\"></image>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"preview-footer\">\n          <text class=\"scan-tip\">{{customText}}</text>\n          <text class=\"distributor-id\">推广员ID: {{distributorId}}</text>\n        </view>\n      </view>\n      \n      <view class=\"action-buttons\">\n        <button class=\"action-button save\" @click=\"saveQrcode\">\n          <view class=\"button-icon\">\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M7 10L12 15L17 10\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M12 15V3\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            </svg>\n          </view>\n          <text>保存到相册</text>\n        </button>\n        \n        <button class=\"action-button share\" @click=\"shareQrcode\">\n          <view class=\"button-icon\">\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <circle cx=\"18\" cy=\"5\" r=\"3\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <circle cx=\"6\" cy=\"12\" r=\"3\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <circle cx=\"18\" cy=\"19\" r=\"3\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <line x1=\"8.59\" y1=\"13.51\" x2=\"15.42\" y2=\"17.49\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <line x1=\"15.41\" y1=\"6.51\" x2=\"8.59\" y2=\"10.49\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            </svg>\n          </view>\n          <text>分享二维码</text>\n        </button>\n      </view>\n    </view>\n    \n    <!-- 自定义选项 -->\n    <view class=\"customization-section\">\n      <!-- 二维码类型选择 -->\n      <view class=\"option-group\">\n        <text class=\"option-title\">二维码类型</text>\n        <view class=\"type-options\">\n          <view \n            v-for=\"(type, index) in displayQrcodeTypes\" \n            :key=\"index\" \n            class=\"type-option\" \n            :class=\"{ active: selectedType === type.value }\"\n            @click=\"selectType(type.value)\"\n          >\n            <view class=\"option-icon\" v-html=\"type.icon\"></view>\n            <text class=\"option-name\">{{type.name}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 主题颜色选择 -->\n      <view class=\"option-group\">\n        <text class=\"option-title\">主题颜色</text>\n        <view class=\"theme-options\">\n          <view \n            v-for=\"(theme, index) in themeColors\" \n            :key=\"index\" \n            class=\"theme-option\" \n            :class=\"{ active: selectedTheme === theme }\"\n            :style=\"{ backgroundColor: theme.bgColor }\"\n            @click=\"selectTheme(theme)\"\n          >\n            <view class=\"theme-check\" v-if=\"selectedTheme === theme\">\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M20 6L9 17L4 12\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              </svg>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 其他选项 -->\n      <view class=\"option-group\">\n        <view class=\"toggle-option\">\n          <text class=\"toggle-label\">显示店铺LOGO</text>\n          <switch :checked=\"showLogo\" @change=\"toggleLogo\" color=\"#6B0FBE\" />\n        </view>\n        \n        <view class=\"toggle-option\">\n          <text class=\"toggle-label\">显示推广员ID</text>\n          <switch :checked=\"showId\" @change=\"toggleId\" color=\"#6B0FBE\" />\n        </view>\n      </view>\n      \n      <!-- 自定义文案 -->\n      <view class=\"option-group\">\n        <text class=\"option-title\">自定义文案</text>\n        <input \n          class=\"custom-input\" \n          type=\"text\" \n          v-model=\"customText\" \n          placeholder=\"输入自定义文案（最多20字）\" \n          maxlength=\"20\"\n        />\n      </view>\n    </view>\n    \n    <!-- 二维码历史记录 -->\n    <view class=\"history-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">历史记录</text>\n        <text class=\"clear-history\" @click=\"clearHistory\">清空</text>\n      </view>\n      \n      <scroll-view class=\"history-scroll\" scroll-x>\n        <view class=\"history-list\">\n          <view \n            v-for=\"(item, index) in historyQrcodes\" \n            :key=\"index\" \n            class=\"history-item\"\n            @click=\"loadHistoryQrcode(item)\"\n          >\n            <image class=\"history-image\" :src=\"item.image\" mode=\"aspectFit\"></image>\n            <text class=\"history-date\">{{item.date}}</text>\n          </view>\n        </view>\n      </scroll-view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, computed, onMounted } from 'vue';\nimport promotionService from '@/utils/promotionService';\n\n// 推广参数\nconst promotionParams = reactive({\n  type: 'product', // 默认类型\n  id: '',\n  title: '',\n  image: '',\n  extraParams: {} // 存储额外参数\n});\n\n// 店铺信息\nconst storeInfo = reactive({\n  name: '磁州同城生活',\n  slogan: '本地生活好物优选',\n  logo: '/static/images/logo.png'\n});\n\n// 分销员ID\nconst distributorId = ref('D88652');\n\n// 二维码类型\nconst qrcodeTypes = [\n  {\n    name: '店铺',\n    value: 'store',\n    icon: '<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z\" stroke=\"#6B0FBE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M9 22V12H15V22\" stroke=\"#6B0FBE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>'\n  },\n  {\n    name: '商品',\n    value: 'product',\n    icon: '<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M20 7L12 3L4 7M20 7V17L12 21M20 7L12 11M12 21L4 17V7M12 21V11M4 7L12 11\" stroke=\"#6B0FBE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>'\n  },\n  {\n    name: '活动',\n    value: 'activity',\n    icon: '<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"#6B0FBE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>'\n  },\n  {\n    name: '会员',\n    value: 'member',\n    icon: '<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21\" stroke=\"#6B0FBE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z\" stroke=\"#6B0FBE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>'\n  }\n];\n\n// 根据业务类型动态添加二维码类型\nconst dynamicQrcodeTypes = {\n  'carpool': {\n    name: '拼车',\n    value: 'carpool',\n    icon: '<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M7 17H2V12H7M17 17H22V12H17M14 5H10L8 10H16L14 5ZM5 11V17H8V19H16V17H19V11L16 4H8L5 11Z\" stroke=\"#6B0FBE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>'\n  },\n  'secondhand': {\n    name: '二手',\n    value: 'secondhand',\n    icon: '<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M16 4H8V12H16V4ZM16 16H8V20H16V16ZM4 20H6V4H4V20ZM18 4V20H20V4H18Z\" stroke=\"#6B0FBE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>'\n  },\n  'house': {\n    name: '房屋',\n    value: 'house',\n    icon: '<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z\" stroke=\"#6B0FBE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M9 22V12H15V22\" stroke=\"#6B0FBE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>'\n  },\n  'service': {\n    name: '服务',\n    value: 'service',\n    icon: '<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z\" stroke=\"#6B0FBE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>'\n  },\n  'community': {\n    name: '社区',\n    value: 'community',\n    icon: '<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21M23 21V19C22.9993 18.1137 22.7044 17.2528 22.1614 16.5523C21.6184 15.8519 20.8581 15.3516 20 15.13M16 3.13C16.8604 3.3503 17.623 3.8507 18.1676 4.55231C18.7122 5.25392 19.0078 6.11683 19.0078 7.005C19.0078 7.89317 18.7122 8.75608 18.1676 9.45769C17.623 10.1593 16.8604 10.6597 16 10.88M13 7C13 9.20914 11.2091 11 9 11C6.79086 11 5 9.20914 5 7C5 4.79086 6.79086 3 9 3C11.2091 3 13 4.79086 13 7Z\" stroke=\"#6B0FBE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>'\n  }\n};\n\n// 主题颜色\nconst themeColors = [\n  { bgColor: '#FFFFFF', textColor: '#333333' },\n  { bgColor: '#6B0FBE', textColor: '#FFFFFF' },\n  { bgColor: '#A764CA', textColor: '#FFFFFF' },\n  { bgColor: '#F5F7FA', textColor: '#333333' },\n  { bgColor: '#FFE8F0', textColor: '#FF3B30' },\n  { bgColor: '#E8F8FF', textColor: '#007AFF' }\n];\n\n// 历史记录\nconst historyQrcodes = reactive([\n  { image: '/static/images/distribution/qrcode-1.png', date: '2023-04-25' },\n  { image: '/static/images/distribution/qrcode-2.png', date: '2023-04-20' },\n  { image: '/static/images/distribution/qrcode-3.png', date: '2023-04-15' }\n]);\n\n// 状态变量\nconst selectedType = ref('product');\nconst selectedTheme = ref(themeColors[0]);\nconst showLogo = ref(true);\nconst showId = ref(true);\nconst customText = ref('');\n\n// 当前二维码\nconst currentQrcode = ref('/static/images/distribution/qrcode-sample.png');\n\n// 显示的二维码类型选项\nconst displayQrcodeTypes = computed(() => {\n  // 基础类型\n  let types = [...qrcodeTypes];\n  \n  // 如果有特定业务类型，添加到选项中\n  if (dynamicQrcodeTypes[promotionParams.type]) {\n    types = [dynamicQrcodeTypes[promotionParams.type], ...types];\n  }\n  \n  return types;\n});\n\n// 页面加载\nonMounted(() => {\n  // 获取页面参数\n  const pages = getCurrentPages();\n  const currentPage = pages[pages.length - 1];\n  const options = currentPage.options || {};\n  \n  // 解析推广参数\n  parsePromotionParams(options);\n  \n  // 生成二维码\n  generateQrcode();\n  \n  // 生成推广文案\n  generatePromotionText();\n});\n\n// 解析推广参数\nconst parsePromotionParams = (options) => {\n  // 设置基本参数\n  promotionParams.type = options.type || 'product';\n  promotionParams.id = options.id || '';\n  promotionParams.title = options.title ? decodeURIComponent(options.title) : '';\n  promotionParams.image = options.image ? decodeURIComponent(options.image) : '';\n  \n  // 设置额外参数\n  const extraParams = {};\n  Object.keys(options).forEach(key => {\n    if (!['type', 'id', 'title', 'image'].includes(key)) {\n      extraParams[key] = decodeURIComponent(options[key] || '');\n    }\n  });\n  promotionParams.extraParams = extraParams;\n  \n  // 设置默认选中类型\n  selectedType.value = promotionParams.type;\n};\n\n// 生成二维码\nconst generateQrcode = () => {\n  // 这里应该调用实际的二维码生成API\n  // 暂时使用示例图片\n  currentQrcode.value = '/static/images/distribution/qrcode-sample.png';\n  \n  // 实际项目中，应该根据参数生成二维码\n  // const params = promotionService.generateQrcodeParams(\n  //   promotionParams.type,\n  //   promotionParams.id,\n  //   distributorId.value\n  // );\n  // 调用二维码生成API\n};\n\n// 生成推广文案\nconst generatePromotionText = () => {\n  // 根据不同类型生成不同的推广文案\n  switch(promotionParams.type) {\n    case 'carpool':\n      customText.value = `扫码查看【${promotionParams.title}】拼车信息`;\n      break;\n      \n    case 'secondhand':\n      customText.value = `扫码查看【${promotionParams.title}】二手商品`;\n      break;\n      \n    case 'house':\n      customText.value = `扫码查看【${promotionParams.title}】房源信息`;\n      break;\n      \n    case 'service':\n      customText.value = `扫码预约【${promotionParams.title}】服务`;\n      break;\n      \n    case 'product':\n      customText.value = `扫码购买【${promotionParams.title}】`;\n      break;\n      \n    case 'content':\n      customText.value = `扫码阅读【${promotionParams.title}】`;\n      break;\n      \n    case 'community':\n      customText.value = `扫码查看【${promotionParams.title}】社区信息`;\n      break;\n      \n    default:\n      customText.value = `扫码查看【${promotionParams.title}】`;\n  }\n};\n\n// 选择二维码类型\nconst selectType = (type) => {\n  selectedType.value = type;\n  generateQrcode();\n};\n\n// 选择主题颜色\nconst selectTheme = (theme) => {\n  selectedTheme.value = theme;\n};\n\n// 切换显示店铺LOGO\nconst toggleLogo = (e) => {\n  showLogo.value = e.detail.value;\n};\n\n// 切换显示推广员ID\nconst toggleId = (e) => {\n  showId.value = e.detail.value;\n};\n\n// 保存二维码到相册\nconst saveQrcode = () => {\n  uni.showLoading({\n    title: '保存中...'\n  });\n  \n  setTimeout(() => {\n    uni.hideLoading();\n    uni.showToast({\n      title: '保存成功',\n      icon: 'success'\n    });\n    \n    // 记录到历史记录\n    const now = new Date();\n    const dateStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;\n    historyQrcodes.unshift({\n      image: currentQrcode.value,\n      date: dateStr\n    });\n  }, 1500);\n};\n\n// 分享二维码\nconst shareQrcode = () => {\n  uni.showActionSheet({\n    itemList: ['分享给朋友', '分享到朋友圈'],\n    success: (res) => {\n      uni.showToast({\n        title: '分享成功',\n        icon: 'success'\n      });\n      \n      // 记录分享事件\n      const shareType = res.tapIndex === 0 ? 'friend' : 'timeline';\n      logShareEvent(shareType);\n    }\n  });\n};\n\n// 记录分享事件\nconst logShareEvent = (shareType) => {\n  console.log('记录分享事件', {\n    type: promotionParams.type,\n    id: promotionParams.id,\n    shareType\n  });\n};\n\n// 加载历史二维码\nconst loadHistoryQrcode = (item) => {\n  // 实际应用中应该加载历史二维码的配置\n  uni.showToast({\n    title: '加载历史二维码',\n    icon: 'none'\n  });\n};\n\n// 清空历史记录\nconst clearHistory = () => {\n  uni.showModal({\n    title: '提示',\n    content: '确定要清空历史记录吗？',\n    success: (res) => {\n      if (res.confirm) {\n        historyQrcodes.splice(0, historyQrcodes.length);\n        uni.showToast({\n          title: '已清空',\n          icon: 'success'\n        });\n      }\n    }\n  });\n};\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n\n// 显示帮助\nconst showHelp = () => {\n  uni.showModal({\n    title: '推广二维码使用帮助',\n    content: '选择喜欢的二维码样式，自定义颜色和文案，生成精美二维码进行分享推广。',\n    showCancel: false\n  });\n};\n</script>\n\n<style lang=\"scss\">\n.qrcode-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: env(safe-area-inset-bottom);\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\n  color: #fff;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* 二维码预览部分 */\n.qrcode-preview-section {\n  margin: 16px;\n}\n\n.preview-card {\n  background-color: #FFFFFF;\n  border-radius: 20px;\n  padding: 20px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  margin-bottom: 16px;\n  overflow: hidden;\n  position: relative;\n}\n\n.preview-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.store-logo {\n  width: 40px;\n  height: 40px;\n  border-radius: 10px;\n  margin-right: 12px;\n  border: 1px solid rgba(0, 0, 0, 0.05);\n}\n\n.store-info {\n  flex: 1;\n}\n\n.store-name {\n  font-size: 16px;\n  font-weight: 600;\n  color: inherit;\n  margin-bottom: 4px;\n}\n\n.store-slogan {\n  font-size: 12px;\n  color: inherit;\n  opacity: 0.7;\n}\n\n.qrcode-wrapper {\n  display: flex;\n  justify-content: center;\n  margin: 10px 0 20px;\n}\n\n.qrcode-image-container {\n  position: relative;\n  width: 200px;\n  height: 200px;\n}\n\n.qrcode-image {\n  width: 100%;\n  height: 100%;\n  background-color: #FFFFFF;\n  border-radius: 10px;\n}\n\n.qrcode-logo {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 40px;\n  height: 40px;\n  background-color: #FFFFFF;\n  border-radius: 8px;\n  padding: 2px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.qrcode-logo image {\n  width: 100%;\n  height: 100%;\n  border-radius: 6px;\n}\n\n.preview-footer {\n  text-align: center;\n}\n\n.scan-tip {\n  font-size: 14px;\n  font-weight: 500;\n  color: inherit;\n  margin-bottom: 8px;\n  display: block;\n}\n\n.distributor-id {\n  font-size: 12px;\n  color: inherit;\n  opacity: 0.7;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 12px;\n}\n\n.action-button {\n  flex: 1;\n  height: 44px;\n  border-radius: 22px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: 500;\n  border: none;\n}\n\n.action-button.save {\n  background: linear-gradient(135deg, #34C759, #32D74B);\n  color: #FFFFFF;\n}\n\n.action-button.share {\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\n  color: #FFFFFF;\n}\n\n.button-icon {\n  margin-right: 6px;\n}\n\n/* 自定义选项部分 */\n.customization-section {\n  margin: 16px;\n  background-color: #FFFFFF;\n  border-radius: 20px;\n  padding: 20px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);\n}\n\n.option-group {\n  margin-bottom: 20px;\n}\n\n.option-group:last-child {\n  margin-bottom: 0;\n}\n\n.option-title {\n  font-size: 15px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 12px;\n  display: block;\n}\n\n.type-options {\n  display: flex;\n  justify-content: space-between;\n}\n\n.type-option {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 12px 0;\n  border-radius: 12px;\n  transition: all 0.3s ease;\n}\n\n.type-option.active {\n  background-color: rgba(107, 15, 190, 0.05);\n}\n\n.option-icon {\n  margin-bottom: 8px;\n}\n\n.option-name {\n  font-size: 12px;\n  color: #666;\n}\n\n.type-option.active .option-name {\n  color: #6B0FBE;\n  font-weight: 500;\n}\n\n.theme-options {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12px;\n}\n\n.theme-option {\n  width: 40px;\n  height: 40px;\n  border-radius: 20px;\n  border: 2px solid transparent;\n  position: relative;\n  transition: all 0.3s ease;\n}\n\n.theme-option.active {\n  border-color: #6B0FBE;\n  transform: scale(1.05);\n  box-shadow: 0 2px 8px rgba(107, 15, 190, 0.2);\n}\n\n.theme-check {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n\n.toggle-option {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid #F0F0F0;\n}\n\n.toggle-option:last-child {\n  border-bottom: none;\n}\n\n.toggle-label {\n  font-size: 14px;\n  color: #333;\n}\n\n.custom-input {\n  width: 100%;\n  height: 44px;\n  background-color: #F5F7FA;\n  border-radius: 12px;\n  padding: 0 16px;\n  font-size: 14px;\n  color: #333;\n  border: 1px solid transparent;\n}\n\n.custom-input:focus {\n  border-color: #6B0FBE;\n  background-color: #FFFFFF;\n}\n\n/* 历史记录部分 */\n.history-section {\n  margin: 16px;\n  background-color: #FFFFFF;\n  border-radius: 20px;\n  padding: 20px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);\n  margin-bottom: 32px;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.section-title {\n  font-size: 15px;\n  font-weight: 600;\n  color: #333;\n}\n\n.clear-history {\n  font-size: 14px;\n  color: #FF3B30;\n}\n\n.history-scroll {\n  width: 100%;\n}\n\n.history-list {\n  display: flex;\n  padding-bottom: 8px;\n}\n\n.history-item {\n  width: 80px;\n  margin-right: 12px;\n  flex-shrink: 0;\n}\n\n.history-item:last-child {\n  margin-right: 0;\n}\n\n.history-image {\n  width: 80px;\n  height: 80px;\n  border-radius: 10px;\n  border: 1px solid #EEEEEE;\n  margin-bottom: 6px;\n}\n\n.history-date {\n  font-size: 10px;\n  color: #999;\n  text-align: center;\n  display: block;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/distribution/qrcode.vue'\nwx.createPage(MiniProgramPage)"], "names": ["reactive", "ref", "computed", "onMounted", "uni", "MiniProgramPage"], "mappings": ";;;;;;;;;;;;AAiKA,UAAM,kBAAkBA,cAAAA,SAAS;AAAA,MAC/B,MAAM;AAAA;AAAA,MACN,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,aAAa,CAAE;AAAA;AAAA,IACjB,CAAC;AAGD,UAAM,YAAYA,cAAAA,SAAS;AAAA,MACzB,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAGD,UAAM,gBAAgBC,cAAAA,IAAI,QAAQ;AAGlC,UAAM,cAAc;AAAA,MAClB;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACP;AAAA,IACH;AAGA,UAAM,qBAAqB;AAAA,MACzB,WAAW;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACP;AAAA,MACD,cAAc;AAAA,QACZ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACP;AAAA,MACD,SAAS;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACP;AAAA,MACD,WAAW;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACP;AAAA,MACD,aAAa;AAAA,QACX,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACP;AAAA,IACH;AAGA,UAAM,cAAc;AAAA,MAClB,EAAE,SAAS,WAAW,WAAW,UAAW;AAAA,MAC5C,EAAE,SAAS,WAAW,WAAW,UAAW;AAAA,MAC5C,EAAE,SAAS,WAAW,WAAW,UAAW;AAAA,MAC5C,EAAE,SAAS,WAAW,WAAW,UAAW;AAAA,MAC5C,EAAE,SAAS,WAAW,WAAW,UAAW;AAAA,MAC5C,EAAE,SAAS,WAAW,WAAW,UAAW;AAAA,IAC9C;AAGA,UAAM,iBAAiBD,cAAAA,SAAS;AAAA,MAC9B,EAAE,OAAO,4CAA4C,MAAM,aAAc;AAAA,MACzE,EAAE,OAAO,4CAA4C,MAAM,aAAc;AAAA,MACzE,EAAE,OAAO,4CAA4C,MAAM,aAAc;AAAA,IAC3E,CAAC;AAGD,UAAM,eAAeC,cAAAA,IAAI,SAAS;AAClC,UAAM,gBAAgBA,cAAG,IAAC,YAAY,CAAC,CAAC;AACxC,UAAM,WAAWA,cAAAA,IAAI,IAAI;AACzB,UAAM,SAASA,cAAAA,IAAI,IAAI;AACvB,UAAM,aAAaA,cAAAA,IAAI,EAAE;AAGzB,UAAM,gBAAgBA,cAAAA,IAAI,+CAA+C;AAGzE,UAAM,qBAAqBC,cAAQ,SAAC,MAAM;AAExC,UAAI,QAAQ,CAAC,GAAG,WAAW;AAG3B,UAAI,mBAAmB,gBAAgB,IAAI,GAAG;AAC5C,gBAAQ,CAAC,mBAAmB,gBAAgB,IAAI,GAAG,GAAG,KAAK;AAAA,MAC5D;AAED,aAAO;AAAA,IACT,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AAEd,YAAM,QAAQ;AACd,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,UAAU,YAAY,WAAW;AAGvC,2BAAqB,OAAO;AAG5B;AAGA;IACF,CAAC;AAGD,UAAM,uBAAuB,CAAC,YAAY;AAExC,sBAAgB,OAAO,QAAQ,QAAQ;AACvC,sBAAgB,KAAK,QAAQ,MAAM;AACnC,sBAAgB,QAAQ,QAAQ,QAAQ,mBAAmB,QAAQ,KAAK,IAAI;AAC5E,sBAAgB,QAAQ,QAAQ,QAAQ,mBAAmB,QAAQ,KAAK,IAAI;AAG5E,YAAM,cAAc,CAAA;AACpB,aAAO,KAAK,OAAO,EAAE,QAAQ,SAAO;AAClC,YAAI,CAAC,CAAC,QAAQ,MAAM,SAAS,OAAO,EAAE,SAAS,GAAG,GAAG;AACnD,sBAAY,GAAG,IAAI,mBAAmB,QAAQ,GAAG,KAAK,EAAE;AAAA,QACzD;AAAA,MACL,CAAG;AACD,sBAAgB,cAAc;AAG9B,mBAAa,QAAQ,gBAAgB;AAAA,IACvC;AAGA,UAAM,iBAAiB,MAAM;AAG3B,oBAAc,QAAQ;AAAA,IASxB;AAGA,UAAM,wBAAwB,MAAM;AAElC,cAAO,gBAAgB,MAAI;AAAA,QACzB,KAAK;AACH,qBAAW,QAAQ,QAAQ,gBAAgB,KAAK;AAChD;AAAA,QAEF,KAAK;AACH,qBAAW,QAAQ,QAAQ,gBAAgB,KAAK;AAChD;AAAA,QAEF,KAAK;AACH,qBAAW,QAAQ,QAAQ,gBAAgB,KAAK;AAChD;AAAA,QAEF,KAAK;AACH,qBAAW,QAAQ,QAAQ,gBAAgB,KAAK;AAChD;AAAA,QAEF,KAAK;AACH,qBAAW,QAAQ,QAAQ,gBAAgB,KAAK;AAChD;AAAA,QAEF,KAAK;AACH,qBAAW,QAAQ,QAAQ,gBAAgB,KAAK;AAChD;AAAA,QAEF,KAAK;AACH,qBAAW,QAAQ,QAAQ,gBAAgB,KAAK;AAChD;AAAA,QAEF;AACE,qBAAW,QAAQ,QAAQ,gBAAgB,KAAK;AAAA,MACnD;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,SAAS;AAC3B,mBAAa,QAAQ;AACrB;IACF;AAGA,UAAM,cAAc,CAAC,UAAU;AAC7B,oBAAc,QAAQ;AAAA,IACxB;AAGA,UAAM,aAAa,CAAC,MAAM;AACxB,eAAS,QAAQ,EAAE,OAAO;AAAA,IAC5B;AAGA,UAAM,WAAW,CAAC,MAAM;AACtB,aAAO,QAAQ,EAAE,OAAO;AAAA,IAC1B;AAGA,UAAM,aAAa,MAAM;AACvBC,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAGD,cAAM,MAAM,oBAAI;AAChB,cAAM,UAAU,GAAG,IAAI,YAAa,CAAA,IAAI,OAAO,IAAI,SAAU,IAAG,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,IAAI,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC;AAC7H,uBAAe,QAAQ;AAAA,UACrB,OAAO,cAAc;AAAA,UACrB,MAAM;AAAA,QACZ,CAAK;AAAA,MACF,GAAE,IAAI;AAAA,IACT;AAGA,UAAM,cAAc,MAAM;AACxBA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,SAAS,QAAQ;AAAA,QAC5B,SAAS,CAAC,QAAQ;AAChBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAGD,gBAAM,YAAY,IAAI,aAAa,IAAI,WAAW;AAClD,wBAAc,SAAS;AAAA,QACxB;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,CAAC,cAAc;AACnCA,oBAAAA,MAAA,MAAA,OAAA,uFAAY,UAAU;AAAA,QACpB,MAAM,gBAAgB;AAAA,QACtB,IAAI,gBAAgB;AAAA,QACpB;AAAA,MACJ,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,CAAC,SAAS;AAElCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,eAAe,MAAM;AACzBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,2BAAe,OAAO,GAAG,eAAe,MAAM;AAC9CA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MAChB,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrdA,GAAG,WAAWC,SAAe;"}