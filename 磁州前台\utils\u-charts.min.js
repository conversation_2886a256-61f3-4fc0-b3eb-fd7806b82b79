/*
 * uCharts®
 * 高性能跨平台图表库，支持H5、APP、小程序（微信/支付宝/百度/头条/QQ/360）、Vue、Taro等支持canvas的框架平台
 * Copyright (c) 2021 QIUN®秋云 https://www.ucharts.cn All rights reserved.
 * Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
 * 复制使用请保留本段注释，感谢支持开源！
 * 
 * uCharts®官方网站
 * https://www.uCharts.cn
 * 
 * 开源地址:
 * https://gitee.com/uCharts/uCharts
 * 
 * uni-app插件市场地址：
 * http://ext.dcloud.net.cn/plugin?id=271
 * 
 */

// 主要是压缩版的uCharts库，这里只提供一个简化版本用于示例
// 实际使用时应该使用完整的uCharts库

'use strict';

var config = {
  yAxisWidth: 15,
  yAxisSplit: 5,
  xAxisHeight: 22,
  xAxisLineHeight: 22,
  legendHeight: 15,
  yAxisTitleWidth: 15,
  padding: [10, 10, 10, 10],
  pixelRatio: 1,
  rotate: false,
  columePadding: 3,
  fontSize: 13,
  fontColor: '#666666',
  dataPointShape: ['circle', 'circle', 'circle', 'circle'],
  color: ['#1890FF', '#91CB74', '#FAC858', '#EE6666', '#73C0DE', '#3CA272', '#FC8452', '#9A60B4', '#ea7ccc'],
  linearColor: ['#0983FF', '#13CDE3'],
  pieChartLinePadding: 15,
  pieChartTextPadding: 5,
  xAxisTextPadding: 3,
  titleColor: '#333333',
  titleFontSize: 20,
  subtitleColor: '#999999',
  subtitleFontSize: 15,
  toolTipPadding: 3,
  toolTipBackground: '#000000',
  toolTipOpacity: 0.7,
  toolTipLineHeight: 20,
  radarLabelTextMargin: 13,
  gaugeLabelTextMargin: 13
};

var assign = function(target, ...varArgs) {
  if (target == null) {
    throw new TypeError('[uCharts] Cannot convert undefined or null to object');
  }
  if (!varArgs || varArgs.length <= 0) {
    return target;
  }
  // 深度合并对象
  function deepAssign(obj1, obj2) {
    for (let key in obj2) {
      obj1[key] = obj1[key] && obj1[key].toString() === "[object Object]" ?
        deepAssign(obj1[key], obj2[key]) : obj1[key] = obj2[key];
    }
    return obj1;
  }
  varArgs.forEach(val => {
    target = deepAssign(target, val);
  });
  return target;
};

var util = {
  toFixed: function toFixed(num, limit) {
    limit = limit || 2;
    if (this.isFloat(num)) {
      num = num.toFixed(limit);
    }
    return num;
  },
  isFloat: function isFloat(num) {
    return num % 1 !== 0;
  },
  approximatelyEqual: function approximatelyEqual(num1, num2) {
    return Math.abs(num1 - num2) < 1e-10;
  },
  isSameSign: function isSameSign(num1, num2) {
    return Math.abs(num1) === num1 && Math.abs(num2) === num2 || Math.abs(num1) !== num1 && Math.abs(num2) !== num2;
  },
  isSameXCoordinateArea: function isSameXCoordinateArea(p1, p2) {
    return this.isSameSign(p1.x, p2.x);
  },
  isCollision: function isCollision(obj1, obj2) {
    obj1.end = {};
    obj1.end.x = obj1.start.x + obj1.width;
    obj1.end.y = obj1.start.y - obj1.height;
    obj2.end = {};
    obj2.end.x = obj2.start.x + obj2.width;
    obj2.end.y = obj2.start.y - obj2.height;
    var flag = obj2.start.x > obj1.end.x || obj2.end.x < obj1.start.x || obj2.end.y > obj1.start.y || obj2.start.y < obj1.end.y;
    return !flag;
  }
};

// 简化版uCharts类
function uCharts(opts) {
  this.opts = opts;
  this.config = {};
  this.context = opts.context;
  this.chartData = {};
  this.event = {};
  this.scrollOption = {
    currentOffset: 0,
    startTouchX: 0,
    distance: 0,
    lastMoveTime: 0
  };
  
  // 初始化
  this.initConfig();
  this.initContext();
  
  // 绘制图表
  this.drawChart();
}

uCharts.prototype.initConfig = function() {
  this.config = assign({}, config);
  this.config = assign(this.config, this.opts);
};

uCharts.prototype.initContext = function() {
  if (!this.context) {
    console.error('[uCharts] 未获取到context！');
    return;
  }
};

uCharts.prototype.drawChart = function() {
  this.drawLine();
};

uCharts.prototype.drawLine = function() {
  // 简化版的线图绘制逻辑
  const { width, height, series, categories } = this.opts;
  
  if (!series || series.length === 0) {
    return;
  }
  
  // 计算坐标轴范围
  let minValue = 0;
  let maxValue = 0;
  
  series.forEach(item => {
    const data = item.data;
    const min = Math.min(...data);
    const max = Math.max(...data);
    
    if (min < minValue) minValue = min;
    if (max > maxValue) maxValue = max;
  });
  
  // 确保最小值为0或更小
  if (minValue > 0) {
    minValue = 0;
  }
  
  // 增加最大值的一点余量
  maxValue = maxValue * 1.1;
  
  // 绘制坐标轴
  this.context.beginPath();
  this.context.setLineWidth(1);
  this.context.setStrokeStyle('#CCCCCC');
  
  // X轴
  this.context.moveTo(40, height - 30);
  this.context.lineTo(width - 20, height - 30);
  
  // Y轴
  this.context.moveTo(40, 20);
  this.context.lineTo(40, height - 30);
  
  this.context.stroke();
  this.context.closePath();
  
  // 绘制X轴刻度和标签
  const xStep = (width - 60) / (categories.length - 1);
  
  categories.forEach((category, index) => {
    const x = 40 + index * xStep;
    
    // 刻度线
    this.context.beginPath();
    this.context.moveTo(x, height - 30);
    this.context.lineTo(x, height - 25);
    this.context.stroke();
    this.context.closePath();
    
    // 标签
    this.context.setFontSize(10);
    this.context.setFillStyle('#666666');
    this.context.fillText(category, x - 15, height - 15);
  });
  
  // 绘制Y轴刻度和标签
  const yStep = (height - 50) / 5;
  const valueStep = (maxValue - minValue) / 5;
  
  for (let i = 0; i <= 5; i++) {
    const y = height - 30 - i * yStep;
    const value = minValue + i * valueStep;
    
    // 刻度线
    this.context.beginPath();
    this.context.moveTo(40, y);
    this.context.lineTo(35, y);
    this.context.stroke();
    this.context.closePath();
    
    // 标签
    this.context.setFontSize(10);
    this.context.setFillStyle('#666666');
    this.context.fillText(value.toFixed(0), 10, y + 5);
  }
  
  // 绘制数据线
  series.forEach((serie, serieIndex) => {
    this.context.beginPath();
    this.context.setLineWidth(2);
    this.context.setStrokeStyle(serie.color || this.config.color[serieIndex]);
    
    const data = serie.data;
    
    data.forEach((value, index) => {
      const x = 40 + index * xStep;
      const y = height - 30 - ((value - minValue) / (maxValue - minValue)) * (height - 50);
      
      if (index === 0) {
        this.context.moveTo(x, y);
      } else {
        this.context.lineTo(x, y);
      }
    });
    
    this.context.stroke();
    this.context.closePath();
    
    // 绘制数据点
    data.forEach((value, index) => {
      const x = 40 + index * xStep;
      const y = height - 30 - ((value - minValue) / (maxValue - minValue)) * (height - 50);
      
      this.context.beginPath();
      this.context.arc(x, y, 3, 0, 2 * Math.PI);
      this.context.setFillStyle(serie.color || this.config.color[serieIndex]);
      this.context.fill();
      this.context.closePath();
    });
  });
  
  // 绘制完成
  this.context.draw();
};

uCharts.prototype.updateData = function(data) {
  this.opts.series = data.series;
  this.opts.categories = data.categories;
  this.drawChart();
};

// 修改导出方式，支持默认导出
export default uCharts; 