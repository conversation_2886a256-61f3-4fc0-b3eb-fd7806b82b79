"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const categories = common_vendor.ref([
      { id: 0, name: "全部" },
      { id: 1, name: "活动" },
      { id: 2, name: "商品" },
      { id: 3, name: "优惠券" },
      { id: 4, name: "拼团" },
      { id: 5, name: "秒杀" }
    ]);
    const currentCategory = common_vendor.ref(0);
    const loading = common_vendor.ref(false);
    const noMore = common_vendor.ref(false);
    const historyList = common_vendor.ref([
      {
        id: 1,
        title: "Apple iPhone 14 Pro Max 256GB 暗夜紫 移动联通电信5G双卡双待手机",
        image: "https://via.placeholder.com/200",
        shop: "Apple官方旗舰店",
        price: "8999.00",
        originalPrice: "9999.00",
        type: "product",
        viewTime: "14:30",
        date: "2023-05-15",
        isFavorite: true
      },
      {
        id: 2,
        title: "618年中大促全场低至5折起",
        image: "https://via.placeholder.com/200",
        shop: "京东自营",
        price: "0.00",
        originalPrice: "",
        type: "activity",
        viewTime: "09:15",
        date: "2023-05-15",
        isFavorite: false
      },
      {
        id: 3,
        title: "满300减50全场优惠券",
        image: "https://via.placeholder.com/200",
        shop: "京东自营",
        price: "50.00",
        originalPrice: "",
        type: "coupon",
        viewTime: "16:42",
        date: "2023-05-14",
        isFavorite: false
      },
      {
        id: 4,
        title: "小米12S Ultra 12GB+256GB 丹青黑 骁龙8+旗舰处理器 徕卡专业光学镜头",
        image: "https://via.placeholder.com/200",
        shop: "小米官方旗舰店",
        price: "5999.00",
        originalPrice: "6999.00",
        type: "product",
        viewTime: "11:23",
        date: "2023-05-14",
        isFavorite: true
      },
      {
        id: 5,
        title: "3人团：小米空气净化器",
        image: "https://via.placeholder.com/200",
        shop: "小米官方旗舰店",
        price: "699.00",
        originalPrice: "999.00",
        type: "group",
        viewTime: "18:05",
        date: "2023-05-13",
        isFavorite: false
      },
      {
        id: 6,
        title: "限时秒杀：iPhone 14 Pro",
        image: "https://via.placeholder.com/200",
        shop: "Apple授权专卖店",
        price: "6999.00",
        originalPrice: "8999.00",
        type: "flash",
        viewTime: "10:30",
        date: "2023-05-12",
        isFavorite: false
      }
    ]);
    const filteredHistory = common_vendor.computed(() => {
      if (currentCategory.value === 0) {
        return historyList.value;
      } else {
        const categoryMap = {
          1: "activity",
          2: "product",
          3: "coupon",
          4: "group",
          5: "flash"
        };
        return historyList.value.filter((item) => item.type === categoryMap[currentCategory.value]);
      }
    });
    const groupedHistory = common_vendor.computed(() => {
      const groups = {};
      filteredHistory.value.forEach((item) => {
        if (!groups[item.date]) {
          groups[item.date] = {
            date: formatDate(item.date),
            items: []
          };
        }
        groups[item.date].items.push(item);
      });
      return Object.values(groups).sort((a, b) => {
        const dateA = new Date(a.date.replace("今天", (/* @__PURE__ */ new Date()).toISOString().split("T")[0]).replace("昨天", new Date(Date.now() - 864e5).toISOString().split("T")[0]).replace("前天", new Date(Date.now() - 2 * 864e5).toISOString().split("T")[0]));
        const dateB = new Date(b.date.replace("今天", (/* @__PURE__ */ new Date()).toISOString().split("T")[0]).replace("昨天", new Date(Date.now() - 864e5).toISOString().split("T")[0]).replace("前天", new Date(Date.now() - 2 * 864e5).toISOString().split("T")[0]));
        return dateB - dateA;
      });
    });
    function goBack() {
      common_vendor.index.navigateBack();
    }
    function switchCategory(index) {
      currentCategory.value = index;
    }
    function viewDetail(item) {
      let url = "";
      switch (item.type) {
        case "product":
          url = `/subPackages/activity-showcase/pages/detail/index?id=${item.id}&type=product`;
          break;
        case "activity":
          url = `/subPackages/activity-showcase/pages/detail/index?id=${item.id}&type=activity`;
          break;
        case "coupon":
          url = `/subPackages/activity-showcase/pages/coupon/detail?id=${item.id}`;
          break;
        case "group":
          url = `/subPackages/activity-showcase/pages/group-buy/detail?id=${item.id}`;
          break;
        case "flash":
          url = `/subPackages/activity-showcase/pages/flash-sale/detail?id=${item.id}`;
          break;
        default:
          url = `/subPackages/activity-showcase/pages/detail/index?id=${item.id}`;
      }
      common_vendor.index.navigateTo({ url });
    }
    function toggleFavorite(item) {
      item.isFavorite = !item.isFavorite;
      common_vendor.index.showToast({
        title: item.isFavorite ? "已收藏" : "已取消收藏",
        icon: "success"
      });
    }
    function deleteHistory(item) {
      common_vendor.index.showModal({
        title: "删除记录",
        content: "确定要删除该浏览记录吗？",
        success: (res) => {
          if (res.confirm) {
            const index = historyList.value.findIndex((record) => record.id === item.id);
            if (index !== -1) {
              historyList.value.splice(index, 1);
            }
            common_vendor.index.showToast({
              title: "已删除",
              icon: "success"
            });
          }
        }
      });
    }
    function showClearConfirm() {
      common_vendor.index.showModal({
        title: "清空记录",
        content: "确定要清空所有浏览记录吗？",
        success: (res) => {
          if (res.confirm) {
            historyList.value = [];
            common_vendor.index.showToast({
              title: "已清空",
              icon: "success"
            });
          }
        }
      });
    }
    function goExplore() {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    }
    function loadMore() {
      if (loading.value || noMore.value)
        return;
      loading.value = true;
      setTimeout(() => {
        noMore.value = true;
        loading.value = false;
      }, 1500);
    }
    function getTagBackground(type) {
      switch (type) {
        case "product":
          return "linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%)";
        case "activity":
          return "linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)";
        case "coupon":
          return "linear-gradient(135deg, #FF3B30 0%, #FF5E3A 100%)";
        case "group":
          return "linear-gradient(135deg, #34C759 0%, #30D158 100%)";
        case "flash":
          return "linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)";
        default:
          return "linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)";
      }
    }
    function getTagText(type) {
      switch (type) {
        case "product":
          return "商品";
        case "activity":
          return "活动";
        case "coupon":
          return "优惠券";
        case "group":
          return "拼团";
        case "flash":
          return "秒杀";
        default:
          return "其他";
      }
    }
    function formatDate(dateStr) {
      const today = (/* @__PURE__ */ new Date()).toISOString().split("T")[0];
      const yesterday = new Date(Date.now() - 864e5).toISOString().split("T")[0];
      const beforeYesterday = new Date(Date.now() - 2 * 864e5).toISOString().split("T")[0];
      if (dateStr === today) {
        return "今天";
      } else if (dateStr === yesterday) {
        return "昨天";
      } else if (dateStr === beforeYesterday) {
        return "前天";
      } else {
        return dateStr;
      }
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: common_vendor.f(categories.value, (category, index, i0) => {
          return {
            a: common_vendor.t(category.name),
            b: index,
            c: currentCategory.value === index ? 1 : "",
            d: common_vendor.o(($event) => switchCategory(index), index),
            e: currentCategory.value === index ? "#FFFFFF" : "#666666",
            f: currentCategory.value === index ? "linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)" : "#F2F2F7",
            g: currentCategory.value === index ? "0 4px 10px rgba(255,59,105,0.2)" : "none"
          };
        }),
        d: common_vendor.o(showClearConfirm),
        e: common_vendor.f(groupedHistory.value, (group, groupIndex, i0) => {
          return {
            a: common_vendor.t(group.date),
            b: common_vendor.f(group.items, (item, itemIndex, i1) => {
              return common_vendor.e({
                a: item.image,
                b: common_vendor.t(item.title),
                c: common_vendor.t(item.shop),
                d: common_vendor.t(item.price),
                e: item.originalPrice
              }, item.originalPrice ? {
                f: common_vendor.t(item.originalPrice)
              } : {}, {
                g: common_vendor.t(getTagText(item.type)),
                h: getTagBackground(item.type),
                i: common_vendor.o(($event) => viewDetail(item), itemIndex),
                j: common_vendor.t(item.viewTime),
                k: "95b4133c-1-" + i0 + "-" + i1 + "," + ("95b4133c-0-" + i0 + "-" + i1),
                l: common_vendor.p({
                  d: "M19 21l-7-5-7 5V5a2 2 0 012-2h10a2 2 0 012 2v16z",
                  fill: item.isFavorite ? "#FF3B69" : "none",
                  stroke: item.isFavorite ? "#FF3B69" : "#999999",
                  ["stroke-width"]: "2",
                  ["stroke-linecap"]: "round",
                  ["stroke-linejoin"]: "round"
                }),
                m: "95b4133c-0-" + i0 + "-" + i1,
                n: common_vendor.t(item.isFavorite ? "已收藏" : "收藏"),
                o: item.isFavorite ? "#FF3B69" : "#999999",
                p: common_vendor.o(($event) => toggleFavorite(item), itemIndex),
                q: "95b4133c-3-" + i0 + "-" + i1 + "," + ("95b4133c-2-" + i0 + "-" + i1),
                r: "95b4133c-2-" + i0 + "-" + i1,
                s: common_vendor.o(($event) => deleteHistory(item), itemIndex),
                t: itemIndex
              });
            }),
            c: groupIndex
          };
        }),
        f: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        g: common_vendor.p({
          d: "M3 6h18M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2M10 11v6M14 11v6",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        h: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        i: filteredHistory.value.length === 0
      }, filteredHistory.value.length === 0 ? {
        j: common_assets._imports_1$57,
        k: common_vendor.o(goExplore)
      } : {}, {
        l: filteredHistory.value.length > 0 && !noMore.value
      }, filteredHistory.value.length > 0 && !noMore.value ? common_vendor.e({
        m: loading.value
      }, loading.value ? {} : {
        n: common_vendor.o(loadMore)
      }) : {}, {
        o: filteredHistory.value.length > 0 && noMore.value
      }, filteredHistory.value.length > 0 && noMore.value ? {} : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-95b4133c"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/history/index.js.map
