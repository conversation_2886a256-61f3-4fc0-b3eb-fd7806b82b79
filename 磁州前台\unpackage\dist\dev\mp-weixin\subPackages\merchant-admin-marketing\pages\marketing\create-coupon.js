"use strict";
const common_vendor = require("../../../../common/vendor.js");
const DistributionSetting = () => "./distribution/components/DistributionSetting.js";
const distributionMixin = require("/subPackages/merchant-admin-marketing/mixins/distributionMixin").default;
const MarketingPromotionActions = () => "../../components/MarketingPromotionActions.js";
const _sfc_main = {
  name: "CouponCreate",
  components: {
    DistributionSetting,
    MarketingPromotionActions
  },
  mixins: [distributionMixin],
  // 使用分销混入
  data() {
    return {
      currentStep: 1,
      scrollToId: "step1",
      formData: {
        // 基本信息
        name: "",
        type: "amount",
        // amount: 满减券, discount: 折扣券
        value: "",
        // 金额或者折扣值
        description: "",
        couponColor: "linear-gradient(135deg, #FF9966, #FF5E62)",
        // 使用规则
        minAmount: "",
        // 最低消费金额
        goodsLimit: "all",
        // all: 全部商品, category: 品类, specific: 指定商品
        selectedCategories: [],
        selectedGoods: [],
        validityType: "days",
        // days: 领取后N天有效, fixed: 固定日期
        validPeriod: "",
        // validityType为days时使用
        startDate: "",
        // validityType为fixed时使用
        endDate: "",
        // validityType为fixed时使用
        useTimeLimit: false,
        // 是否限制使用时间段
        useTimeStart: "",
        // 使用时间段开始
        useTimeEnd: "",
        // 使用时间段结束
        // 发放设置
        totalQuantity: "",
        // 发行总量
        perUserLimit: "1",
        // 每人限领
        issueType: "manual",
        // manual: 手动领取, auto: 自动发放
        autoIssueCondition: "new",
        // new: 新用户注册, amount: 订单满额
        autoIssueAmount: "",
        // issueCondition为amount时使用
        // 新增属性
        useThreshold: "no",
        // 使用门槛: no - 无门槛, yes - 满额可用
        useInstructions: "",
        // 使用说明
        quantityType: "unlimited",
        // 发行总量类型: unlimited - 不限制, limited - 限制数量
        userLimitType: "limited",
        // 每人限领类型: unlimited - 不限制, limited - 限制数量
        issueTimeType: "now",
        // 发放时间类型: now - 立即发放, scheduled - 定时发放
        issueTime: "",
        // 定时发放时间
        showInCenter: true,
        // 是否在领券中心展示
        // 分销设置
        distributionSettings: {
          enabled: false,
          commissionMode: "percentage",
          commissions: {
            level1: "",
            level2: "",
            level3: ""
          },
          enableLevel3: false
        }
      },
      // 颜色选项
      colorOptions: [
        "linear-gradient(135deg, #FF9966, #FF5E62)",
        "linear-gradient(135deg, #FFA62E, #EA4D2C)",
        "linear-gradient(135deg, #36D1DC, #5B86E5)",
        "linear-gradient(135deg, #3A1C71, #D76D77)",
        "linear-gradient(135deg, #4776E6, #8E54E9)",
        "linear-gradient(135deg, #00B09B, #96C93D)"
      ],
      // 模拟数据
      selectedCategories: [
        { id: 1, name: "女装" },
        { id: 2, name: "男装" }
      ],
      selectedGoods: [
        {
          id: 1,
          name: "2023春季新款连衣裙",
          price: 299,
          image: "/static/images/goods-1.jpg"
        }
      ],
      hasMerchantDistribution: false,
      // 商家是否开通分销功能
      tempCouponId: "temp-" + Date.now()
      // 临时ID，实际应该从后端获取
    };
  },
  computed: {
    progressPercentage() {
      return this.currentStep / 4 * 100;
    }
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    prevStep() {
      if (this.currentStep > 1) {
        this.currentStep -= 1;
        this.scrollToId = `step${this.currentStep}`;
      }
    },
    nextStep() {
      if (this.currentStep === 1) {
        if (!this.formData.name || !this.formData.value) {
          common_vendor.index.showToast({
            title: "请填写必填项",
            icon: "none"
          });
          return;
        }
        if (this.formData.type === "amount") {
          if (isNaN(this.formData.value) || parseFloat(this.formData.value) <= 0) {
            common_vendor.index.showToast({
              title: "请输入有效的优惠金额",
              icon: "none"
            });
            return;
          }
        } else {
          const discValue = parseFloat(this.formData.value);
          if (isNaN(discValue) || discValue <= 0 || discValue >= 10) {
            common_vendor.index.showToast({
              title: "请输入有效的折扣(1-9.9)",
              icon: "none"
            });
            return;
          }
        }
      }
      if (this.currentStep < 4) {
        this.currentStep += 1;
        this.scrollToId = `step${this.currentStep}`;
      } else {
        this.submitForm();
      }
    },
    submitForm() {
      common_vendor.index.showLoading({
        title: "创建中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "创建成功",
          icon: "success",
          duration: 2e3,
          success: () => {
            setTimeout(() => {
              common_vendor.index.navigateBack();
            }, 2e3);
          }
        });
      }, 1500);
    },
    toggleTimeLimit(e) {
      this.formData.useTimeLimit = e.detail.value;
    },
    removeCategory(index) {
      this.selectedCategories.splice(index, 1);
    },
    removeGoods(index) {
      this.selectedGoods.splice(index, 1);
    },
    showCategoryPicker() {
      common_vendor.index.showToast({
        title: "品类选择功能待开发",
        icon: "none"
      });
    },
    selectGoods() {
      common_vendor.index.showToast({
        title: "商品选择功能待开发",
        icon: "none"
      });
    },
    showDatePicker(type) {
      common_vendor.index.showToast({
        title: `${type === "start" ? "开始" : "结束"}日期选择功能待开发`,
        icon: "none"
      });
    },
    showTimePicker(type) {
      common_vendor.index.showToast({
        title: `${type === "start" ? "开始" : "结束"}时间选择功能待开发`,
        icon: "none"
      });
    },
    toggleShowInCenter(e) {
      this.formData.showInCenter = e.detail.value;
    },
    showDateTimePicker(type) {
      common_vendor.index.showToast({
        title: "日期时间选择功能待开发",
        icon: "none"
      });
    },
    getValidityText() {
      if (this.formData.validityType === "days") {
        return this.formData.validPeriod ? `领取后${this.formData.validPeriod}天内有效` : "领取后N天内有效";
      } else {
        if (this.formData.startDate && this.formData.endDate) {
          return `${this.formData.startDate} 至 ${this.formData.endDate}`;
        } else {
          return "2023.10.01-2023.10.31";
        }
      }
    },
    getGoodsLimitText() {
      switch (this.formData.goodsLimit) {
        case "all":
          return "全部商品";
        case "category":
          return `指定品类(${this.selectedCategories.length}个)`;
        case "specific":
          return `指定商品(${this.selectedGoods.length}个)`;
        default:
          return "全部商品";
      }
    },
    getIssueTypeText() {
      switch (this.formData.issueType) {
        case "manual":
          return "用户手动领取";
        case "auto":
          return "系统自动发放";
        case "admin":
          return "商家手动发放";
        case "share":
          return "分享领取";
        default:
          return "用户手动领取";
      }
    },
    getAutoIssueConditionText() {
      switch (this.formData.autoIssueCondition) {
        case "new":
          return "新用户注册";
        case "birthday":
          return "会员生日";
        case "amount":
          return `订单满${this.formData.autoIssueAmount || 0}元`;
        default:
          return "新用户注册";
      }
    },
    // 更新分销设置
    updateDistributionSettings(settings) {
      this.formData.distributionSettings = settings;
    },
    // 检查商家是否开通分销功能
    checkMerchantDistribution() {
      setTimeout(() => {
        this.hasMerchantDistribution = true;
      }, 500);
    },
    // 保存优惠券
    async saveCoupon() {
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "保存成功",
          icon: "success",
          duration: 2e3,
          success: () => {
            setTimeout(() => {
              common_vendor.index.navigateBack();
            }, 2e3);
          }
        });
      }, 1500);
      if (this.hasMerchantDistribution && this.formData.distributionSettings.enabled) {
        const success = await this.saveActivityDistributionSettings("coupon", this.tempCouponId);
        if (!success) {
          return;
        }
      }
    },
    // 处理推广操作完成事件
    handlePromotionCompleted(data) {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-marketing/pages/marketing/create-coupon.vue:988", "推广操作完成:", data);
      if (data.action === "publish") {
        common_vendor.index.showToast({
          title: "发布成功",
          icon: "success"
        });
      } else if (data.action === "top") {
        common_vendor.index.showToast({
          title: "置顶成功",
          icon: "success"
        });
      } else if (data.action === "refresh") {
        common_vendor.index.showToast({
          title: "刷新成功",
          icon: "success"
        });
      }
    }
  },
  mounted() {
    this.checkMerchantDistribution();
  }
};
if (!Array) {
  const _component_MarketingPromotionActions = common_vendor.resolveComponent("MarketingPromotionActions");
  _component_MarketingPromotionActions();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: $options.progressPercentage + "%",
    c: $data.currentStep <= 1
  }, $data.currentStep <= 1 ? {} : {}, {
    d: $data.currentStep >= 1 ? 1 : "",
    e: $data.currentStep > 1 ? 1 : "",
    f: $data.currentStep > 1 ? 1 : "",
    g: $data.currentStep <= 2
  }, $data.currentStep <= 2 ? {} : {}, {
    h: $data.currentStep >= 2 ? 1 : "",
    i: $data.currentStep > 2 ? 1 : "",
    j: $data.currentStep > 2 ? 1 : "",
    k: $data.currentStep <= 3
  }, $data.currentStep <= 3 ? {} : {}, {
    l: $data.currentStep >= 3 ? 1 : "",
    m: $data.currentStep > 3 ? 1 : "",
    n: $data.currentStep > 3 ? 1 : "",
    o: $data.currentStep >= 4 ? 1 : "",
    p: $data.currentStep === 1
  }, $data.currentStep === 1 ? common_vendor.e({
    q: common_vendor.t($data.formData.name || "优惠券名称"),
    r: $data.formData.type === "amount"
  }, $data.formData.type === "amount" ? {} : {}, {
    s: common_vendor.t($data.formData.type === "amount" ? $data.formData.value : $data.formData.value + "折"),
    t: common_vendor.t($data.formData.description || "优惠券描述文字"),
    v: common_vendor.t($data.formData.validPeriod ? $data.formData.validPeriod + "天" : "2023.10.01-2023.10.31"),
    w: common_vendor.t($data.formData.minAmount ? "满" + $data.formData.minAmount + "元可用" : "无门槛"),
    x: $data.formData.couponColor,
    y: $data.formData.name,
    z: common_vendor.o(($event) => $data.formData.name = $event.detail.value),
    A: common_vendor.t($data.formData.name.length),
    B: $data.formData.type === "amount"
  }, $data.formData.type === "amount" ? {} : {}, {
    C: $data.formData.type === "amount" ? 1 : "",
    D: common_vendor.o(($event) => $data.formData.type = "amount"),
    E: $data.formData.type === "discount"
  }, $data.formData.type === "discount" ? {} : {}, {
    F: $data.formData.type === "discount" ? 1 : "",
    G: common_vendor.o(($event) => $data.formData.type = "discount"),
    H: common_vendor.t($data.formData.type === "amount" ? "优惠金额" : "折扣力度"),
    I: $data.formData.type === "amount" ? "请输入优惠金额" : "请输入折扣(1-9.9)",
    J: $data.formData.value,
    K: common_vendor.o(($event) => $data.formData.value = $event.detail.value),
    L: common_vendor.t($data.formData.type === "amount" ? "元" : "折"),
    M: common_vendor.f($data.colorOptions, (color, index, i0) => {
      return common_vendor.e({
        a: $data.formData.couponColor === color
      }, $data.formData.couponColor === color ? {} : {}, {
        b: index,
        c: color,
        d: $data.formData.couponColor === color ? 1 : "",
        e: common_vendor.o(($event) => $data.formData.couponColor = color, index)
      });
    }),
    N: $data.formData.description,
    O: common_vendor.o(($event) => $data.formData.description = $event.detail.value),
    P: common_vendor.t($data.formData.description.length)
  }) : {}, {
    Q: $data.currentStep === 2
  }, $data.currentStep === 2 ? common_vendor.e({
    R: $data.formData.useThreshold === "no"
  }, $data.formData.useThreshold === "no" ? {} : {}, {
    S: $data.formData.useThreshold === "no" ? 1 : "",
    T: common_vendor.o(($event) => {
      $data.formData.useThreshold = "no";
      $data.formData.minAmount = "";
    }),
    U: $data.formData.useThreshold === "yes"
  }, $data.formData.useThreshold === "yes" ? {} : {}, {
    V: $data.formData.useThreshold === "yes" ? 1 : "",
    W: common_vendor.o(($event) => $data.formData.useThreshold = "yes"),
    X: $data.formData.useThreshold === "yes"
  }, $data.formData.useThreshold === "yes" ? {
    Y: $data.formData.minAmount,
    Z: common_vendor.o(($event) => $data.formData.minAmount = $event.detail.value)
  } : {}, {
    aa: $data.formData.goodsLimit === "all"
  }, $data.formData.goodsLimit === "all" ? {} : {}, {
    ab: $data.formData.goodsLimit === "all" ? 1 : "",
    ac: common_vendor.o(($event) => $data.formData.goodsLimit = "all"),
    ad: $data.formData.goodsLimit === "category"
  }, $data.formData.goodsLimit === "category" ? {} : {}, {
    ae: $data.formData.goodsLimit === "category" ? 1 : "",
    af: common_vendor.o(($event) => $data.formData.goodsLimit = "category"),
    ag: $data.formData.goodsLimit === "specific"
  }, $data.formData.goodsLimit === "specific" ? {} : {}, {
    ah: $data.formData.goodsLimit === "specific" ? 1 : "",
    ai: common_vendor.o(($event) => $data.formData.goodsLimit = "specific"),
    aj: $data.formData.goodsLimit === "category"
  }, $data.formData.goodsLimit === "category" ? {
    ak: common_vendor.f($data.selectedCategories, (cat, index, i0) => {
      return {
        a: common_vendor.t(cat.name),
        b: common_vendor.o(($event) => $options.removeCategory(index), index),
        c: index
      };
    }),
    al: common_vendor.o((...args) => $options.showCategoryPicker && $options.showCategoryPicker(...args))
  } : {}, {
    am: $data.formData.goodsLimit === "specific"
  }, $data.formData.goodsLimit === "specific" ? common_vendor.e({
    an: $data.selectedGoods.length === 0
  }, $data.selectedGoods.length === 0 ? {} : {}, {
    ao: common_vendor.f($data.selectedGoods, (goods, index, i0) => {
      return {
        a: goods.image,
        b: common_vendor.t(goods.name),
        c: common_vendor.t(goods.price),
        d: common_vendor.o(($event) => $options.removeGoods(index), index),
        e: index
      };
    }),
    ap: common_vendor.o((...args) => $options.selectGoods && $options.selectGoods(...args))
  }) : {}, {
    aq: $data.formData.validityType === "days"
  }, $data.formData.validityType === "days" ? {} : {}, {
    ar: $data.formData.validityType === "days" ? 1 : "",
    as: common_vendor.o(($event) => $data.formData.validityType = "days"),
    at: $data.formData.validityType === "fixed"
  }, $data.formData.validityType === "fixed" ? {} : {}, {
    av: $data.formData.validityType === "fixed" ? 1 : "",
    aw: common_vendor.o(($event) => $data.formData.validityType = "fixed"),
    ax: $data.formData.validityType === "days"
  }, $data.formData.validityType === "days" ? {
    ay: $data.formData.validPeriod,
    az: common_vendor.o(($event) => $data.formData.validPeriod = $event.detail.value)
  } : {}, {
    aA: $data.formData.validityType === "fixed"
  }, $data.formData.validityType === "fixed" ? {
    aB: common_vendor.t($data.formData.startDate || "开始日期"),
    aC: !$data.formData.startDate ? 1 : "",
    aD: common_vendor.o(($event) => $options.showDatePicker("start")),
    aE: common_vendor.t($data.formData.endDate || "结束日期"),
    aF: !$data.formData.endDate ? 1 : "",
    aG: common_vendor.o(($event) => $options.showDatePicker("end"))
  } : {}, {
    aH: common_vendor.o((...args) => $options.toggleTimeLimit && $options.toggleTimeLimit(...args)),
    aI: $data.formData.useTimeLimit
  }, $data.formData.useTimeLimit ? {
    aJ: common_vendor.t($data.formData.useTimeStart || "开始时间"),
    aK: !$data.formData.useTimeStart ? 1 : "",
    aL: common_vendor.o(($event) => $options.showTimePicker("start")),
    aM: common_vendor.t($data.formData.useTimeEnd || "结束时间"),
    aN: !$data.formData.useTimeEnd ? 1 : "",
    aO: common_vendor.o(($event) => $options.showTimePicker("end"))
  } : {}, {
    aP: $data.formData.useInstructions,
    aQ: common_vendor.o(($event) => $data.formData.useInstructions = $event.detail.value),
    aR: common_vendor.t(($data.formData.useInstructions || "").length)
  }) : {}, {
    aS: $data.currentStep === 3
  }, $data.currentStep === 3 ? common_vendor.e({
    aT: $data.formData.quantityType === "unlimited"
  }, $data.formData.quantityType === "unlimited" ? {} : {}, {
    aU: $data.formData.quantityType === "unlimited" ? 1 : "",
    aV: common_vendor.o(($event) => {
      $data.formData.quantityType = "unlimited";
      $data.formData.totalQuantity = "";
    }),
    aW: $data.formData.quantityType === "limited"
  }, $data.formData.quantityType === "limited" ? {} : {}, {
    aX: $data.formData.quantityType === "limited" ? 1 : "",
    aY: common_vendor.o(($event) => $data.formData.quantityType = "limited"),
    aZ: $data.formData.quantityType === "limited"
  }, $data.formData.quantityType === "limited" ? {
    ba: $data.formData.totalQuantity,
    bb: common_vendor.o(($event) => $data.formData.totalQuantity = $event.detail.value)
  } : {}, {
    bc: $data.formData.userLimitType === "unlimited"
  }, $data.formData.userLimitType === "unlimited" ? {} : {}, {
    bd: $data.formData.userLimitType === "unlimited" ? 1 : "",
    be: common_vendor.o(($event) => {
      $data.formData.userLimitType = "unlimited";
      $data.formData.perUserLimit = "";
    }),
    bf: $data.formData.userLimitType === "limited"
  }, $data.formData.userLimitType === "limited" ? {} : {}, {
    bg: $data.formData.userLimitType === "limited" ? 1 : "",
    bh: common_vendor.o(($event) => $data.formData.userLimitType = "limited"),
    bi: $data.formData.userLimitType === "limited"
  }, $data.formData.userLimitType === "limited" ? {
    bj: $data.formData.perUserLimit,
    bk: common_vendor.o(($event) => $data.formData.perUserLimit = $event.detail.value)
  } : {}, {
    bl: $data.formData.issueType === "manual" ? 1 : "",
    bm: $data.formData.issueType === "manual"
  }, $data.formData.issueType === "manual" ? {} : {}, {
    bn: $data.formData.issueType === "manual" ? 1 : "",
    bo: common_vendor.o(($event) => $data.formData.issueType = "manual"),
    bp: $data.formData.issueType === "auto" ? 1 : "",
    bq: $data.formData.issueType === "auto"
  }, $data.formData.issueType === "auto" ? {} : {}, {
    br: $data.formData.issueType === "auto" ? 1 : "",
    bs: common_vendor.o(($event) => $data.formData.issueType = "auto"),
    bt: $data.formData.issueType === "admin" ? 1 : "",
    bv: $data.formData.issueType === "admin"
  }, $data.formData.issueType === "admin" ? {} : {}, {
    bw: $data.formData.issueType === "admin" ? 1 : "",
    bx: common_vendor.o(($event) => $data.formData.issueType = "admin"),
    by: $data.formData.issueType === "share" ? 1 : "",
    bz: $data.formData.issueType === "share"
  }, $data.formData.issueType === "share" ? {} : {}, {
    bA: $data.formData.issueType === "share" ? 1 : "",
    bB: common_vendor.o(($event) => $data.formData.issueType = "share"),
    bC: $data.formData.issueType === "auto"
  }, $data.formData.issueType === "auto" ? common_vendor.e({
    bD: $data.formData.autoIssueCondition === "new"
  }, $data.formData.autoIssueCondition === "new" ? {} : {}, {
    bE: $data.formData.autoIssueCondition === "new" ? 1 : "",
    bF: common_vendor.o(($event) => $data.formData.autoIssueCondition = "new"),
    bG: $data.formData.autoIssueCondition === "birthday"
  }, $data.formData.autoIssueCondition === "birthday" ? {} : {}, {
    bH: $data.formData.autoIssueCondition === "birthday" ? 1 : "",
    bI: common_vendor.o(($event) => $data.formData.autoIssueCondition = "birthday"),
    bJ: $data.formData.autoIssueCondition === "amount"
  }, $data.formData.autoIssueCondition === "amount" ? {} : {}, {
    bK: $data.formData.autoIssueCondition === "amount" ? 1 : "",
    bL: common_vendor.o(($event) => $data.formData.autoIssueCondition = "amount"),
    bM: $data.formData.autoIssueCondition === "amount"
  }, $data.formData.autoIssueCondition === "amount" ? {
    bN: $data.formData.autoIssueAmount,
    bO: common_vendor.o(($event) => $data.formData.autoIssueAmount = $event.detail.value)
  } : {}) : {}, {
    bP: ["manual", "share"].includes($data.formData.issueType)
  }, ["manual", "share"].includes($data.formData.issueType) ? {
    bQ: common_vendor.o((...args) => $options.toggleShowInCenter && $options.toggleShowInCenter(...args))
  } : {}, {
    bR: $data.formData.issueTimeType === "now"
  }, $data.formData.issueTimeType === "now" ? {} : {}, {
    bS: $data.formData.issueTimeType === "now" ? 1 : "",
    bT: common_vendor.o(($event) => $data.formData.issueTimeType = "now"),
    bU: $data.formData.issueTimeType === "scheduled"
  }, $data.formData.issueTimeType === "scheduled" ? {} : {}, {
    bV: $data.formData.issueTimeType === "scheduled" ? 1 : "",
    bW: common_vendor.o(($event) => $data.formData.issueTimeType = "scheduled"),
    bX: $data.formData.issueTimeType === "scheduled"
  }, $data.formData.issueTimeType === "scheduled" ? {
    bY: common_vendor.t($data.formData.issueTime || "请选择发放时间"),
    bZ: !$data.formData.issueTime ? 1 : "",
    ca: common_vendor.o(($event) => $options.showDateTimePicker("issue"))
  } : {}) : {}, {
    cb: $data.currentStep === 4
  }, $data.currentStep === 4 ? common_vendor.e({
    cc: common_vendor.t($data.formData.name),
    cd: $data.formData.type === "amount"
  }, $data.formData.type === "amount" ? {} : {}, {
    ce: common_vendor.t($data.formData.type === "amount" ? $data.formData.value : $data.formData.value + "折"),
    cf: common_vendor.t($data.formData.description || "暂无描述"),
    cg: common_vendor.t($data.formData.validPeriod ? $data.formData.validPeriod + "天" : "2023.10.01-2023.10.31"),
    ch: common_vendor.t($data.formData.minAmount ? "满" + $data.formData.minAmount + "元可用" : "无门槛"),
    ci: $data.formData.couponColor,
    cj: common_vendor.t($data.formData.name),
    ck: common_vendor.t($data.formData.type === "amount" ? "满减券" : "折扣券"),
    cl: common_vendor.t($data.formData.type === "amount" ? "优惠金额" : "折扣力度"),
    cm: common_vendor.t($data.formData.type === "amount" ? $data.formData.value + "元" : $data.formData.value + "折"),
    cn: common_vendor.t($data.formData.useThreshold === "yes" ? "满" + $data.formData.minAmount + "元可用" : "无门槛"),
    co: common_vendor.t($options.getValidityText()),
    cp: common_vendor.t($data.formData.totalQuantity || "不限"),
    cq: common_vendor.t($data.formData.perUserLimit || "不限"),
    cr: common_vendor.o($options.handlePromotionCompleted),
    cs: common_vendor.p({
      ["activity-type"]: "coupon",
      ["activity-id"]: $data.tempCouponId,
      ["publish-mode-only"]: true,
      ["show-actions"]: ["publish"]
    }),
    ct: common_vendor.o((...args) => $options.prevStep && $options.prevStep(...args)),
    cv: common_vendor.o((...args) => $options.saveCoupon && $options.saveCoupon(...args))
  }) : {}, {
    cw: $data.currentStep > 1
  }, $data.currentStep > 1 ? {
    cx: common_vendor.o((...args) => $options.prevStep && $options.prevStep(...args))
  } : {}, {
    cy: common_vendor.t($data.currentStep < 4 ? "下一步" : "创建优惠券"),
    cz: common_vendor.o((...args) => $options.nextStep && $options.nextStep(...args)),
    cA: $data.scrollToId
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/create-coupon.js.map
