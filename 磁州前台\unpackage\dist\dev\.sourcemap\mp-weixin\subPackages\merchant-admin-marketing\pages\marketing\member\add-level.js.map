{"version": 3, "file": "add-level.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/member/add-level.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xtZW1iZXJcYWRkLWxldmVsLnZ1ZQ"], "sourcesContent": ["<!-- 添加会员等级页面开始 -->\r\n<template>\r\n  <view class=\"add-level-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">添加会员等级</text>\r\n      <view class=\"navbar-right\"></view>\r\n    </view>\r\n    \r\n    <!-- 表单内容 -->\r\n    <view class=\"form-content\">\r\n      <!-- 基本信息 -->\r\n      <view class=\"section-card\">\r\n        <view class=\"section-title\">基本信息</view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">等级名称</text>\r\n          <input class=\"form-input\" v-model=\"levelForm.name\" placeholder=\"请输入等级名称\" maxlength=\"10\" />\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">等级图标</text>\r\n          <view class=\"icon-upload\" @click=\"chooseIcon\">\r\n            <image class=\"preview-icon\" v-if=\"levelForm.icon\" :src=\"levelForm.icon\" mode=\"aspectFit\"></image>\r\n            <view class=\"upload-placeholder\" v-else>\r\n              <text class=\"upload-icon\">+</text>\r\n              <text class=\"upload-text\">上传图标</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">等级颜色</text>\r\n          <view class=\"color-picker\">\r\n            <view class=\"color-option\" \r\n                  v-for=\"(color, index) in colorOptions\" \r\n                  :key=\"index\" \r\n                  :style=\"{ background: color.value }\"\r\n                  :class=\"{ active: levelForm.color === color.value }\"\r\n                  @click=\"selectColor(color.value)\">\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 升级条件 -->\r\n      <view class=\"section-card\">\r\n        <view class=\"section-title\">升级条件</view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">条件类型</text>\r\n          <view class=\"radio-group\">\r\n            <view class=\"radio-item\" :class=\"{ active: levelForm.conditionType === 'consumption' }\" @click=\"setConditionType('consumption')\">\r\n              <text class=\"radio-text\">累计消费金额</text>\r\n            </view>\r\n            <view class=\"radio-item\" :class=\"{ active: levelForm.conditionType === 'growth' }\" @click=\"setConditionType('growth')\">\r\n              <text class=\"radio-text\">成长值</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">{{ levelForm.conditionType === 'consumption' ? '消费金额' : '成长值' }}</text>\r\n          <view class=\"form-input-group\">\r\n            <input type=\"number\" class=\"form-input\" v-model=\"levelForm.conditionValue\" :placeholder=\"`请输入${levelForm.conditionType === 'consumption' ? '消费金额' : '成长值'}`\" />\r\n            <text class=\"input-suffix\">{{ levelForm.conditionType === 'consumption' ? '元' : '分' }}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"form-item switch-item\">\r\n          <text class=\"form-label\">自动升级</text>\r\n          <switch :checked=\"levelForm.autoUpgrade\" @change=\"toggleAutoUpgrade\" color=\"#4A00E0\" />\r\n        </view>\r\n        \r\n        <view class=\"form-item switch-item\">\r\n          <text class=\"form-label\">等级保护</text>\r\n          <switch :checked=\"levelForm.levelProtection\" @change=\"toggleLevelProtection\" color=\"#4A00E0\" />\r\n        </view>\r\n        \r\n        <view class=\"form-item\" v-if=\"levelForm.levelProtection\">\r\n          <text class=\"form-label\">保护期限</text>\r\n          <view class=\"form-input-group\">\r\n            <input type=\"number\" class=\"form-input\" v-model=\"levelForm.protectionDays\" placeholder=\"请输入保护天数\" />\r\n            <text class=\"input-suffix\">天</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 等级权益 -->\r\n      <view class=\"section-card\">\r\n        <view class=\"section-title\">等级权益</view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">折扣比例</text>\r\n          <view class=\"form-input-group\">\r\n            <input type=\"digit\" class=\"form-input\" v-model=\"levelForm.discount\" placeholder=\"请输入折扣比例\" />\r\n            <text class=\"input-suffix\">折</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">积分倍率</text>\r\n          <view class=\"form-input-group\">\r\n            <input type=\"digit\" class=\"form-input\" v-model=\"levelForm.pointsRatio\" placeholder=\"请输入积分倍率\" />\r\n            <text class=\"input-suffix\">倍</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">特权选择</text>\r\n          <view class=\"privilege-list\">\r\n            <view class=\"privilege-item\" v-for=\"(privilege, index) in privileges\" :key=\"index\">\r\n              <view class=\"privilege-checkbox\" :class=\"{ checked: privilege.selected }\" @click=\"togglePrivilege(privilege)\">\r\n                <view class=\"checkbox-inner\" v-if=\"privilege.selected\"></view>\r\n              </view>\r\n              <text class=\"privilege-name\">{{privilege.name}}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">自定义权益</text>\r\n          <view class=\"custom-privileges\">\r\n            <view class=\"custom-privilege-item\" v-for=\"(item, index) in levelForm.customPrivileges\" :key=\"index\">\r\n              <input class=\"custom-privilege-input\" v-model=\"levelForm.customPrivileges[index]\" placeholder=\"请输入权益内容\" />\r\n              <view class=\"delete-btn\" @click=\"deleteCustomPrivilege(index)\">×</view>\r\n            </view>\r\n            <view class=\"add-custom-btn\" @click=\"addCustomPrivilege\">\r\n              <text class=\"add-custom-text\">+ 添加自定义权益</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 底部按钮 -->\r\n    <view class=\"bottom-bar\">\r\n      <button class=\"cancel-btn\" @click=\"goBack\">取消</button>\r\n      <button class=\"save-btn\" @click=\"saveLevel\">保存</button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 表单数据\r\n      levelForm: {\r\n        name: '',\r\n        icon: '',\r\n        color: 'linear-gradient(135deg, #8E2DE2, #4A00E0)',\r\n        conditionType: 'consumption',\r\n        conditionValue: '',\r\n        autoUpgrade: true,\r\n        levelProtection: false,\r\n        protectionDays: 30,\r\n        discount: '9.5',\r\n        pointsRatio: '1.2',\r\n        customPrivileges: ['']\r\n      },\r\n      \r\n      // 颜色选项\r\n      colorOptions: [\r\n        { name: '紫色', value: 'linear-gradient(135deg, #8E2DE2, #4A00E0)' },\r\n        { name: '金色', value: 'linear-gradient(135deg, #FFC837, #FF8008)' },\r\n        { name: '银色', value: 'linear-gradient(135deg, #D4D4D8, #A1A1AA)' },\r\n        { name: '蓝色', value: 'linear-gradient(135deg, #2196F3, #0D47A1)' },\r\n        { name: '红色', value: 'linear-gradient(135deg, #FF5E3A, #FF2A68)' },\r\n        { name: '绿色', value: 'linear-gradient(135deg, #43E97B, #38F9D7)' }\r\n      ],\r\n      \r\n      // 特权列表\r\n      privileges: [\r\n        { id: 1, name: '会员折扣', selected: true },\r\n        { id: 2, name: '积分加速', selected: true },\r\n        { id: 3, name: '免费配送', selected: false },\r\n        { id: 4, name: '生日礼包', selected: true },\r\n        { id: 5, name: '专属客服', selected: false },\r\n        { id: 6, name: '优先发货', selected: false }\r\n      ]\r\n    };\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    \r\n    chooseIcon() {\r\n      uni.chooseImage({\r\n        count: 1,\r\n        sizeType: ['compressed'],\r\n        sourceType: ['album', 'camera'],\r\n        success: (res) => {\r\n          this.levelForm.icon = res.tempFilePaths[0];\r\n        }\r\n      });\r\n    },\r\n    \r\n    selectColor(color) {\r\n      this.levelForm.color = color;\r\n    },\r\n    \r\n    setConditionType(type) {\r\n      this.levelForm.conditionType = type;\r\n    },\r\n    \r\n    toggleAutoUpgrade(e) {\r\n      this.levelForm.autoUpgrade = e.detail.value;\r\n    },\r\n    \r\n    toggleLevelProtection(e) {\r\n      this.levelForm.levelProtection = e.detail.value;\r\n    },\r\n    \r\n    togglePrivilege(privilege) {\r\n      const index = this.privileges.findIndex(item => item.id === privilege.id);\r\n      if (index !== -1) {\r\n        this.privileges[index].selected = !this.privileges[index].selected;\r\n      }\r\n    },\r\n    \r\n    addCustomPrivilege() {\r\n      this.levelForm.customPrivileges.push('');\r\n    },\r\n    \r\n    deleteCustomPrivilege(index) {\r\n      if (this.levelForm.customPrivileges.length > 1) {\r\n        this.levelForm.customPrivileges.splice(index, 1);\r\n      } else {\r\n        this.levelForm.customPrivileges = [''];\r\n      }\r\n    },\r\n    \r\n    validateForm() {\r\n      if (!this.levelForm.name) {\r\n        uni.showToast({\r\n          title: '请输入等级名称',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      \r\n      if (!this.levelForm.icon) {\r\n        uni.showToast({\r\n          title: '请上传等级图标',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      \r\n      if (!this.levelForm.conditionValue) {\r\n        uni.showToast({\r\n          title: `请输入${this.levelForm.conditionType === 'consumption' ? '消费金额' : '成长值'}`,\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      \r\n      return true;\r\n    },\r\n    \r\n    saveLevel() {\r\n      if (!this.validateForm()) return;\r\n      \r\n      uni.showLoading({\r\n        title: '保存中...'\r\n      });\r\n      \r\n      setTimeout(() => {\r\n        uni.hideLoading();\r\n        uni.showToast({\r\n          title: '会员等级添加成功',\r\n          icon: 'success'\r\n        });\r\n        \r\n        setTimeout(() => {\r\n          uni.navigateBack();\r\n        }, 1500);\r\n      }, 1000);\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n/* 添加会员等级页面样式开始 */\r\n.add-level-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: 120rpx;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #8E2DE2, #4A00E0);\r\n  color: #fff;\r\n  padding: 44px 16px 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 2px 10px rgba(74, 0, 224, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.navbar-right {\r\n  width: 36px;\r\n  height: 36px;\r\n}\r\n\r\n/* 表单内容样式 */\r\n.form-content {\r\n  padding: 20rpx;\r\n}\r\n\r\n.section-card {\r\n  background: #fff;\r\n  border-radius: 12rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.section-title {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 20rpx;\r\n  padding-bottom: 15rpx;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n/* 表单项样式 */\r\n.form-item {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.form-label {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  margin-bottom: 10rpx;\r\n  display: block;\r\n}\r\n\r\n.form-input {\r\n  width: 100%;\r\n  height: 80rpx;\r\n  border: 1rpx solid #ddd;\r\n  border-radius: 8rpx;\r\n  padding: 0 20rpx;\r\n  font-size: 28rpx;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.form-input-group {\r\n  display: flex;\r\n  align-items: center;\r\n  border: 1rpx solid #ddd;\r\n  border-radius: 8rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.form-input-group .form-input {\r\n  flex: 1;\r\n  border: none;\r\n}\r\n\r\n.input-suffix {\r\n  padding: 0 20rpx;\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  background: #f5f5f5;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n}\r\n\r\n/* 图标上传 */\r\n.icon-upload {\r\n  width: 120rpx;\r\n  height: 120rpx;\r\n  border: 1rpx dashed #ddd;\r\n  border-radius: 8rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f9f9f9;\r\n}\r\n\r\n.preview-icon {\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 8rpx;\r\n}\r\n\r\n.upload-placeholder {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.upload-icon {\r\n  font-size: 40rpx;\r\n  color: #999;\r\n  line-height: 1;\r\n}\r\n\r\n.upload-text {\r\n  font-size: 20rpx;\r\n  color: #999;\r\n  margin-top: 8rpx;\r\n}\r\n\r\n/* 颜色选择器 */\r\n.color-picker {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20rpx;\r\n}\r\n\r\n.color-option {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  border-radius: 30rpx;\r\n  border: 2rpx solid transparent;\r\n}\r\n\r\n.color-option.active {\r\n  border-color: #333;\r\n  transform: scale(1.1);\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 单选按钮组 */\r\n.radio-group {\r\n  display: flex;\r\n  gap: 20rpx;\r\n}\r\n\r\n.radio-item {\r\n  flex: 1;\r\n  height: 80rpx;\r\n  border: 1rpx solid #ddd;\r\n  border-radius: 8rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f9f9f9;\r\n}\r\n\r\n.radio-item.active {\r\n  background: rgba(74, 0, 224, 0.1);\r\n  border-color: #4A00E0;\r\n}\r\n\r\n.radio-text {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n}\r\n\r\n.radio-item.active .radio-text {\r\n  color: #4A00E0;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 开关项 */\r\n.switch-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n/* 特权列表 */\r\n.privilege-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20rpx;\r\n  margin-top: 10rpx;\r\n}\r\n\r\n.privilege-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10rpx 20rpx;\r\n  background: #f9f9f9;\r\n  border-radius: 8rpx;\r\n}\r\n\r\n.privilege-checkbox {\r\n  width: 36rpx;\r\n  height: 36rpx;\r\n  border: 1rpx solid #ddd;\r\n  border-radius: 50%;\r\n  margin-right: 10rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.privilege-checkbox.checked {\r\n  border-color: #4A00E0;\r\n  background: #4A00E0;\r\n}\r\n\r\n.checkbox-inner {\r\n  width: 18rpx;\r\n  height: 18rpx;\r\n  border-radius: 50%;\r\n  background: #fff;\r\n}\r\n\r\n.privilege-name {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n}\r\n\r\n/* 自定义权益 */\r\n.custom-privileges {\r\n  margin-top: 10rpx;\r\n}\r\n\r\n.custom-privilege-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15rpx;\r\n}\r\n\r\n.custom-privilege-input {\r\n  flex: 1;\r\n  height: 80rpx;\r\n  border: 1rpx solid #ddd;\r\n  border-radius: 8rpx;\r\n  padding: 0 20rpx;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.delete-btn {\r\n  width: 60rpx;\r\n  height: 80rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 40rpx;\r\n  color: #999;\r\n}\r\n\r\n.add-custom-btn {\r\n  height: 80rpx;\r\n  border: 1rpx dashed #ddd;\r\n  border-radius: 8rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f9f9f9;\r\n}\r\n\r\n.add-custom-text {\r\n  font-size: 26rpx;\r\n  color: #4A00E0;\r\n}\r\n\r\n/* 底部按钮栏 */\r\n.bottom-bar {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: #fff;\r\n  padding: 20rpx;\r\n  display: flex;\r\n  gap: 20rpx;\r\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.cancel-btn {\r\n  flex: 1;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n  background: #f5f5f5;\r\n  color: #666;\r\n  border: none;\r\n  border-radius: 40rpx;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.save-btn {\r\n  flex: 1;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n  background: #4A00E0;\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 40rpx;\r\n  font-size: 28rpx;\r\n}\r\n/* 添加会员等级页面样式结束 */\r\n</style>\r\n<!-- 添加会员等级页面结束 --> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/member/add-level.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAmJA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA;AAAA,MAEL,WAAW;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,UAAU;AAAA,QACV,aAAa;AAAA,QACb,kBAAkB,CAAC,EAAE;AAAA,MACtB;AAAA;AAAA,MAGD,cAAc;AAAA,QACZ,EAAE,MAAM,MAAM,OAAO,4CAA6C;AAAA,QAClE,EAAE,MAAM,MAAM,OAAO,4CAA6C;AAAA,QAClE,EAAE,MAAM,MAAM,OAAO,4CAA6C;AAAA,QAClE,EAAE,MAAM,MAAM,OAAO,4CAA6C;AAAA,QAClE,EAAE,MAAM,MAAM,OAAO,4CAA6C;AAAA,QAClE,EAAE,MAAM,MAAM,OAAO,4CAA4C;AAAA,MAClE;AAAA;AAAA,MAGD,YAAY;AAAA,QACV,EAAE,IAAI,GAAG,MAAM,QAAQ,UAAU,KAAM;AAAA,QACvC,EAAE,IAAI,GAAG,MAAM,QAAQ,UAAU,KAAM;AAAA,QACvC,EAAE,IAAI,GAAG,MAAM,QAAQ,UAAU,MAAO;AAAA,QACxC,EAAE,IAAI,GAAG,MAAM,QAAQ,UAAU,KAAM;AAAA,QACvC,EAAE,IAAI,GAAG,MAAM,QAAQ,UAAU,MAAO;AAAA,QACxC,EAAE,IAAI,GAAG,MAAM,QAAQ,UAAU,MAAM;AAAA,MACzC;AAAA;EAEH;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IAED,aAAa;AACXA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAChB,eAAK,UAAU,OAAO,IAAI,cAAc,CAAC;AAAA,QAC3C;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,YAAY,OAAO;AACjB,WAAK,UAAU,QAAQ;AAAA,IACxB;AAAA,IAED,iBAAiB,MAAM;AACrB,WAAK,UAAU,gBAAgB;AAAA,IAChC;AAAA,IAED,kBAAkB,GAAG;AACnB,WAAK,UAAU,cAAc,EAAE,OAAO;AAAA,IACvC;AAAA,IAED,sBAAsB,GAAG;AACvB,WAAK,UAAU,kBAAkB,EAAE,OAAO;AAAA,IAC3C;AAAA,IAED,gBAAgB,WAAW;AACzB,YAAM,QAAQ,KAAK,WAAW,UAAU,UAAQ,KAAK,OAAO,UAAU,EAAE;AACxE,UAAI,UAAU,IAAI;AAChB,aAAK,WAAW,KAAK,EAAE,WAAW,CAAC,KAAK,WAAW,KAAK,EAAE;AAAA,MAC5D;AAAA,IACD;AAAA,IAED,qBAAqB;AACnB,WAAK,UAAU,iBAAiB,KAAK,EAAE;AAAA,IACxC;AAAA,IAED,sBAAsB,OAAO;AAC3B,UAAI,KAAK,UAAU,iBAAiB,SAAS,GAAG;AAC9C,aAAK,UAAU,iBAAiB,OAAO,OAAO,CAAC;AAAA,aAC1C;AACL,aAAK,UAAU,mBAAmB,CAAC,EAAE;AAAA,MACvC;AAAA,IACD;AAAA,IAED,eAAe;AACb,UAAI,CAAC,KAAK,UAAU,MAAM;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,KAAK,UAAU,MAAM;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,KAAK,UAAU,gBAAgB;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,KAAK,UAAU,kBAAkB,gBAAgB,SAAS,KAAK;AAAA,UAC5E,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACR;AAAA,IAED,YAAY;AACV,UAAI,CAAC,KAAK,aAAY;AAAI;AAE1BA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAED,mBAAW,MAAM;AACfA,wBAAG,MAAC,aAAY;AAAA,QACjB,GAAE,IAAI;AAAA,MACR,GAAE,GAAI;AAAA,IACT;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5RA,GAAG,WAAW,eAAe;"}