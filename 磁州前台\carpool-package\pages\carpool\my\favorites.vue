<template>
  <view class="favorites-container">
    <!-- 自定义导航栏 -->
    <view class="custom-header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="left-action" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="action-icon back-icon"></image>
        </view>
        <view class="title-area">
          <text class="page-title">我的收藏</text>
        </view>
        <view class="right-action">
          <!-- 右侧占位 -->
        </view>
      </view>
    </view>
    
    <!-- 内容区域 -->
    <scroll-view class="scrollable-content" scroll-y enable-back-to-top="true" @scrolltolower="loadMore" refresher-enabled @refresherrefresh="onRefresh" :refresher-triggered="isRefreshing">
      <!-- 收藏内容列表 -->
      <view class="card-container">
        <view class="card-item" v-for="(item, index) in favoritesList" :key="item.id">
          <!-- 编辑模式选择 -->
          <view class="select-area" v-if="isEditMode" @click.stop="toggleSelectItem(item)">
            <view class="checkbox" :class="{ checked: selectedItems.includes(item.id) }">
              <view class="checkbox-inner" v-if="selectedItems.includes(item.id)"></view>
            </view>
          </view>
          
          <!-- 卡片内容 -->
          <view class="card-content" @click="viewDetail(item)">
            <view class="user-info">
              <image :src="item.userAvatar" mode="aspectFill" class="user-avatar"></image>
              <view class="user-details">
                <text class="user-name">{{item.userName}}</text>
                <view class="user-meta">
                  <text class="publish-time">{{item.publishTime}}</text>
                  <text class="trip-type">{{item.type}}</text>
                </view>
              </view>
            </view>
            
            <view class="route-info">
              <view class="route-points">
                <view class="start-point">
                  <view class="point-marker start"></view>
                  <text class="point-text">{{item.startPoint}}</text>
                </view>
                <view class="route-line"></view>
                <view class="end-point">
                  <view class="point-marker end"></view>
                  <text class="point-text">{{item.endPoint}}</text>
                </view>
              </view>
              <view class="trip-info">
                <view class="info-item">
                  <image src="/static/images/icons/calendar.png" mode="aspectFit" class="info-icon"></image>
                  <text class="info-text">{{item.departureTime}}</text>
                </view>
                <view class="info-item">
                  <image src="/static/images/icons/people.png" mode="aspectFit" class="info-icon"></image>
                  <text class="info-text">{{item.seatCount}}个座位</text>
                </view>
                <view class="info-item" v-if="item.price">
                  <image src="/static/images/icons/price.png" mode="aspectFit" class="info-icon"></image>
                  <text class="info-text price">¥{{item.price}}/人</text>
                </view>
              </view>
            </view>
            
            <!-- 底部状态 -->
            <view class="card-footer">
              <view class="meta-info">
                <view class="views">
                  <image src="/static/images/icons/eye.png" mode="aspectFit" class="meta-icon"></image>
                  <text class="meta-text">{{item.viewCount}}</text>
                </view>
                <view class="messages">
                  <image src="/static/images/icons/message.png" mode="aspectFit" class="meta-icon"></image>
                  <text class="meta-text">{{item.messageCount}}</text>
                </view>
              </view>
              <view class="action-area">
                <view class="cancel-btn" @click.stop="cancelFavorite(item)" v-if="!isEditMode">
                  取消收藏
              </view>
              <view class="status-tag" :class="getStatusClass(item.status)">{{getStatusText(item.status)}}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 无数据提示 -->
      <view class="empty-state" v-if="favoritesList.length === 0 && !isLoading">
        <image src="/static/images/empty/no-favorites.png" mode="aspectFit" class="empty-image"></image>
        <text class="empty-text">暂无收藏信息</text>
        <button class="browse-button" @click="goBrowse">去浏览</button>
      </view>
      
      <!-- 加载状态 -->
      <view class="loading-state" v-if="isLoading && !isRefreshing">
        <text class="loading-text">加载中...</text>
      </view>
      
      <!-- 到底提示 -->
      <view class="list-bottom" v-if="favoritesList.length > 0 && !hasMore">
        <text class="bottom-text">— 已经到底啦 —</text>
      </view>
    </scroll-view>
    
    <!-- 批量操作栏 -->
    <view class="batch-action-bar" v-if="isEditMode">
      <view class="select-all" @click="toggleSelectAll">
        <view class="checkbox" :class="{ checked: isAllSelected }">
          <view class="checkbox-inner" v-if="isAllSelected"></view>
        </view>
        <text class="select-text">全选</text>
      </view>
      <button class="batch-button" :disabled="selectedItems.length === 0" @click="batchCancel">取消收藏({{selectedItems.length}})</button>
    </view>

    <!-- 悬浮编辑按钮 -->
    <view class="float-edit-btn" @click="toggleEditMode">
      <text>{{ isEditMode ? '完成' : '编辑' }}</text>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 状态栏高度
const statusBarHeight = ref(20);

// 数据
const favoritesList = ref([]);
const page = ref(1);
const pageSize = ref(10);
const hasMore = ref(true);
const isLoading = ref(false);
const isRefreshing = ref(false);
const isEditMode = ref(false);
const selectedItems = ref([]);

// 计算属性
const isAllSelected = computed(() => {
  return favoritesList.value.length > 0 && selectedItems.value.length === favoritesList.value.length;
});

// 页面加载时执行
onMounted(() => {
  // 获取状态栏高度
  const sysInfo = uni.getSystemInfoSync();
  statusBarHeight.value = sysInfo.statusBarHeight || 20;
  
  loadData();
});

// 加载数据
const loadData = () => {
  if (isLoading.value) return;
  isLoading.value = true;
  
  // 模拟数据加载
  setTimeout(() => {
    // 模拟数据
    const mockData = [
      {
        id: '4001',
        type: '长途拼车',
        publishTime: '2023-10-15 16:30',
        status: 'active',
        startPoint: '磁县政府',
        endPoint: '石家庄火车站',
        departureTime: '明天 10:30',
        seatCount: 3,
        price: 50,
        userName: '张先生',
        userAvatar: '/static/images/avatar/user1.png',
        viewCount: 128,
        messageCount: 5
      },
      {
        id: '4002',
        type: '上下班拼车',
        publishTime: '2023-10-14 14:15',
        status: 'active',
        startPoint: '磁县老城区',
        endPoint: '邯郸科技学院',
        departureTime: '工作日 07:30',
        seatCount: 4,
        price: 12,
        userName: '李女士',
        userAvatar: '/static/images/avatar/user2.png',
        viewCount: 86,
        messageCount: 2
      },
      {
        id: '4003',
        type: '短途拼车',
        publishTime: '2023-10-13 11:40',
        status: 'expired',
        startPoint: '磁县体育场',
        endPoint: '磁县汽车站',
        departureTime: '今天 16:00',
        seatCount: 2,
        price: 5,
        userName: '王先生',
        userAvatar: '/static/images/avatar/user3.png',
        viewCount: 56,
        messageCount: 0
      }
    ];
    
    if (page.value === 1) {
      favoritesList.value = mockData;
    } else {
      favoritesList.value = [...favoritesList.value, ...mockData];
    }
    
    // 模拟没有更多数据
    if (page.value >= 2) {
      hasMore.value = false;
    }
    
    isLoading.value = false;
    isRefreshing.value = false;
  }, 1000);
};

// 加载更多
const loadMore = () => {
  if (!hasMore.value || isLoading.value) return;
  page.value++;
  loadData();
};

// 下拉刷新
const onRefresh = () => {
  isRefreshing.value = true;
  page.value = 1;
  hasMore.value = true;
  loadData();
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 前往浏览页面
const goBrowse = () => {
  uni.navigateTo({
    url: '/carpool-package/pages/carpool-main/index'
  });
};

// 查看详情
const viewDetail = (item) => {
  if (isEditMode.value) {
    toggleSelectItem(item);
    return;
  }
  
  uni.navigateTo({
    url: `/carpool-package/pages/carpool/detail/index?id=${item.id}`
  });
};

// 取消收藏
const cancelFavorite = (item) => {
  uni.showModal({
    title: '提示',
    content: '确定要取消收藏此条信息吗？',
    success: (res) => {
      if (res.confirm) {
        // 模拟取消收藏操作
        favoritesList.value = favoritesList.value.filter(i => i.id !== item.id);
        uni.showToast({
          title: '已取消收藏',
          icon: 'success'
        });
      }
    }
  });
};

// 切换编辑模式
const toggleEditMode = () => {
  isEditMode.value = !isEditMode.value;
  if (!isEditMode.value) {
    // 退出编辑模式，清空选择
    selectedItems.value = [];
  }
};

// 切换选择项
const toggleSelectItem = (item) => {
  const index = selectedItems.value.indexOf(item.id);
  if (index === -1) {
    selectedItems.value.push(item.id);
  } else {
    selectedItems.value.splice(index, 1);
  }
};

// 切换全选
const toggleSelectAll = () => {
  if (isAllSelected.value) {
    // 已全选，取消全选
    selectedItems.value = [];
  } else {
    // 未全选，设为全选
    selectedItems.value = favoritesList.value.map(item => item.id);
  }
};

// 批量取消收藏
const batchCancel = () => {
  if (selectedItems.value.length === 0) return;
  
  uni.showModal({
    title: '提示',
    content: `确定要取消收藏这 ${selectedItems.value.length} 条信息吗？`,
    success: (res) => {
      if (res.confirm) {
        // 模拟批量取消收藏操作
        favoritesList.value = favoritesList.value.filter(item => !selectedItems.value.includes(item.id));
        selectedItems.value = [];
        uni.showToast({
          title: '已取消收藏',
          icon: 'success'
        });
      }
    }
  });
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    active: '可乘坐',
    pending: '审核中',
    expired: '已过期'
  };
  return statusMap[status] || status;
};

// 获取状态样式类
const getStatusClass = (status) => {
  const classMap = {
    active: 'status-active',
    pending: 'status-pending',
    expired: 'status-expired'
  };
  return classMap[status] || '';
};
</script>

<style lang="scss">
.favorites-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
}

/* 导航栏样式 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #1677FF;
}

.header-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
}

.left-action, .right-action {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  width: 24px;
  height: 24px;
}

.back-icon {
  width: 24px;
  height: 24px;
  /* 图标是黑色的，需要转为白色 */
  filter: brightness(0) invert(1);
}

.title-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.edit-text {
  color: #FFFFFF;
  font-size: 14px;
}

/* 内容区域 */
.scrollable-content {
  flex: 1;
  margin-top: calc(44px + var(--status-bar-height) + 5px);
  padding: 0;
  margin-bottom: 60px;
}

/* 卡片容器 */
.card-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  padding: 5px 0;
}

/* 卡片项目 */
.card-item {
  width: 90%;
  display: flex;
  position: relative;
}

/* 选择区域 */
.select-area {
  position: absolute;
  left: 10px;
  top: 0;
  bottom: 0;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
}

/* 复选框 */
.checkbox {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #DDDDDD;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FFFFFF;
}

.checkbox.checked {
  border-color: #9C27B0;
  background-color: #9C27B0;
}

.checkbox-inner {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #FFFFFF;
}

/* 卡片内容 */
.card-content {
  flex: 1;
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-left: 0;
}

/* 用户信息 */
.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 10px;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 15px;
  font-weight: 500;
  color: #333333;
}

.user-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 2px;
}

.publish-time, .trip-type {
  font-size: 12px;
  color: #999999;
}

/* 路线信息 */
.route-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
  border-top: 1px solid #F5F5F5;
  padding-top: 12px;
}

.route-points {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.start-point, .end-point {
  display: flex;
  align-items: center;
  gap: 10px;
}

.point-marker {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.start {
  background-color: #1677FF;
}

.end {
  background-color: #FF5722;
}

.route-line {
  width: 2px;
  height: 20px;
  background-color: #DDDDDD;
  margin-left: 5px;
}

.point-text {
  font-size: 16px;
  color: #333333;
}

.trip-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 10px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.info-icon {
  width: 24px;
  height: 24px;
}

.info-text {
  font-size: 14px;
  color: #666666;
}

.price {
  color: #FF5722;
  font-weight: 500;
}

/* 卡片底部 */
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #F5F5F5;
}

.meta-info {
  display: flex;
  gap: 16px;
}

.views, .messages {
  display: flex;
  align-items: center;
  gap: 4px;
}

.meta-icon {
  width: 24px;
  height: 24px;
}

.meta-text {
  font-size: 12px;
  color: #999999;
}

.action-area {
  display: flex;
  align-items: center;
  gap: 10px;
}

.cancel-btn {
  font-size: 13px;
  font-weight: 500;
  padding: 4px 14px;
  border-radius: 16px;
  background-color: #FF5722;
  color: #FFFFFF;
  text-align: center;
  box-shadow: 0 2px 6px rgba(255, 87, 34, 0.25);
  margin-right: 6px;
}

.cancel-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
  filter: brightness(0) invert(1);
}

.status-tag {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.status-active {
  background-color: rgba(22, 119, 255, 0.1);
  color: #1677FF;
}

.status-pending {
  background-color: rgba(255, 152, 0, 0.1);
  color: #FF9800;
}

.status-expired {
  background-color: rgba(153, 153, 153, 0.1);
  color: #999999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  color: #999999;
  margin-bottom: 20px;
}

.browse-button {
  background-color: #9C27B0;
  color: #FFFFFF;
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 14px;
}

/* 加载状态 */
.loading-state {
  padding: 16px 0;
  text-align: center;
}

.loading-text {
  font-size: 14px;
  color: #999999;
}

/* 列表底部 */
.list-bottom {
  padding: 16px 0;
  text-align: center;
}

.bottom-text {
  font-size: 14px;
  color: #999999;
}

/* 批量操作栏 */
.batch-action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 60px;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  padding: 0 16px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  z-index: 99;
}

.select-all {
  display: flex;
  align-items: center;
  gap: 8px;
}

.select-text {
  font-size: 14px;
  color: #333333;
}

.batch-button {
  flex: 1;
  height: 36px;
  line-height: 36px;
  border-radius: 18px;
  background-color: #9C27B0;
  color: #FFFFFF;
  font-size: 14px;
  margin-left: 16px;
}

.batch-button[disabled] {
  background-color: #E0E0E0;
  color: #999999;
}

/* 悬浮编辑按钮 */
.float-edit-btn {
  position: fixed;
  right: 20px;
  bottom: 80px;
  background-color: #1677FF;
  color: #FFFFFF;
  width: 60px;
  height: 60px;
  border-radius: 30px;
  font-size: 14px;
  z-index: 99;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}
</style> 