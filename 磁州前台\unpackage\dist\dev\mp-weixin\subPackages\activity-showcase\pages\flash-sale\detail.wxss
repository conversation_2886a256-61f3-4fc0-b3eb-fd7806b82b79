/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-c0c46a90, html.data-v-c0c46a90, #app.data-v-c0c46a90, .index-container.data-v-c0c46a90 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.flash-detail-container.data-v-c0c46a90 {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 120rpx;
  /* 为底部购买栏留出空间 */
}

/* 自定义导航栏 */
.custom-navbar.data-v-c0c46a90 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
}
.custom-navbar .navbar-bg.data-v-c0c46a90 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
}
.custom-navbar .navbar-content.data-v-c0c46a90 {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding-top: var(--status-bar-height, 25px);
  padding-left: 30rpx;
  padding-right: 30rpx;
  box-sizing: border-box;
}
.custom-navbar .back-btn.data-v-c0c46a90 {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}
.custom-navbar .back-icon.data-v-c0c46a90 {
  width: 100%;
  height: 100%;
}
.custom-navbar .navbar-title.data-v-c0c46a90 {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

/* 加载状态 */
.loading-container.data-v-c0c46a90 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}
.loading-container .loading-spinner.data-v-c0c46a90 {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #FF3B30;
  border-radius: 50%;
  animation: spin-c0c46a90 1s linear infinite;
  margin-bottom: 20rpx;
}
.loading-container .loading-text.data-v-c0c46a90 {
  font-size: 28rpx;
  color: #8E8E93;
}
@keyframes spin-c0c46a90 {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
/* 商品轮播图 */
.product-swiper.data-v-c0c46a90 {
  width: 100%;
  height: 750rpx;
  /* 轮播图高度 */
  border-bottom-left-radius: 35rpx;
  border-bottom-right-radius: 35rpx;
  overflow: hidden;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.15);
  margin-bottom: 30rpx;
}
.product-swiper .swiper-image.data-v-c0c46a90 {
  width: 100%;
  height: 100%;
}

/* 商品基本信息卡片 */
.product-info-card.data-v-c0c46a90 {
  background-color: #FFFFFF;
  border-radius: 35rpx;
  padding: 30rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);
  margin: 0 30rpx 30rpx 30rpx;
  transform: translateZ(0);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.product-info-card.data-v-c0c46a90:active {
  transform: translateY(5rpx);
  box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.06);
}
.product-info-card .countdown-section.data-v-c0c46a90 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.product-info-card .countdown-section .countdown-label.data-v-c0c46a90 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-right: 15rpx;
}
.product-info-card .countdown-section .countdown-timer.data-v-c0c46a90 {
  display: flex;
  align-items: center;
}
.product-info-card .countdown-section .time-block.data-v-c0c46a90 {
  background-color: #FF3B30;
  border-radius: 8rpx;
  padding: 8rpx 12rpx;
  font-size: 28rpx;
  font-weight: 700;
  color: #FFFFFF;
  box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.3);
}
.product-info-card .countdown-section .time-colon.data-v-c0c46a90 {
  font-size: 28rpx;
  font-weight: 700;
  color: #333333;
  margin: 0 8rpx;
}
.product-info-card .price-section.data-v-c0c46a90 {
  display: flex;
  align-items: baseline;
  margin-bottom: 20rpx;
}
.product-info-card .price-section .price-main.data-v-c0c46a90 {
  display: flex;
  align-items: baseline;
  margin-right: 15rpx;
}
.product-info-card .price-section .price-symbol.data-v-c0c46a90 {
  font-size: 36rpx;
  font-weight: 700;
  color: #FF3B30;
  margin-right: 5rpx;
}
.product-info-card .price-section .price-value.data-v-c0c46a90 {
  font-size: 56rpx;
  font-weight: 800;
  color: #FF3B30;
  text-shadow: 0 2rpx 4rpx rgba(255, 59, 48, 0.2);
}
.product-info-card .price-section .price-tag.data-v-c0c46a90 {
  background: linear-gradient(135deg, #FF3B30 0%, #FF6B6B 100%);
  color: #FFFFFF;
  font-size: 24rpx;
  font-weight: 600;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  margin-left: 15rpx;
  box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.2);
}
.product-info-card .price-section .original-price.data-v-c0c46a90 {
  font-size: 28rpx;
  color: #8E8E93;
  text-decoration: line-through;
}
.product-info-card .product-title-row.data-v-c0c46a90 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.product-info-card .product-title-row .flash-tag.data-v-c0c46a90 {
  background: linear-gradient(135deg, #FF3B30 0%, #FF6B6B 100%);
  color: #FFFFFF;
  font-size: 24rpx;
  font-weight: 600;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  margin-right: 15rpx;
  box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.2);
}
.product-info-card .product-title-row .product-title.data-v-c0c46a90 {
  font-size: 36rpx;
  font-weight: 700;
  color: #333333;
  flex: 1;
  line-height: 1.4;
}
.product-info-card .sales-stock.data-v-c0c46a90 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  font-size: 26rpx;
  color: #8E8E93;
}
.product-info-card .progress-section.data-v-c0c46a90 {
  margin-top: 20rpx;
  margin-bottom: 30rpx;
}
.product-info-card .progress-section .progress-bar.data-v-c0c46a90 {
  width: 100%;
  height: 12rpx;
  background-color: #E0E0E0;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
  box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.product-info-card .progress-section .progress-fill.data-v-c0c46a90 {
  height: 100%;
  background: linear-gradient(to right, #FF3B30, #FF6B6B);
  border-radius: 6rpx;
  box-shadow: 0 2rpx 4rpx rgba(255, 59, 48, 0.2);
}
.product-info-card .progress-section .progress-text.data-v-c0c46a90 {
  font-size: 24rpx;
  color: #8E8E93;
  text-align: right;
}

/* 商家信息卡片 */
.shop-card.data-v-c0c46a90 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #FFFFFF;
  border-radius: 35rpx;
  padding: 25rpx 30rpx;
  margin: 0 30rpx 30rpx 30rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);
  transform: translateZ(0);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.shop-card.data-v-c0c46a90:active {
  transform: translateY(5rpx);
  box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.06);
}
.shop-card .shop-info.data-v-c0c46a90 {
  display: flex;
  align-items: center;
}
.shop-card .shop-logo.data-v-c0c46a90 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}
.shop-card .shop-details.data-v-c0c46a90 {
  display: flex;
  flex-direction: column;
}
.shop-card .shop-name.data-v-c0c46a90 {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 10rpx;
}
.shop-card .shop-rating.data-v-c0c46a90 {
  display: flex;
  align-items: center;
}
.shop-card .rating-stars.data-v-c0c46a90 {
  display: flex;
  margin-right: 10rpx;
}
.shop-card .star-icon.data-v-c0c46a90 {
  width: 24rpx;
  height: 24rpx;
  margin-right: 4rpx;
}
.shop-card .rating-score.data-v-c0c46a90 {
  font-size: 24rpx;
  color: #FF9500;
  font-weight: 600;
}
.shop-card .shop-action.data-v-c0c46a90 {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #8E8E93;
}
.shop-card .shop-action .arrow-icon.data-v-c0c46a90 {
  font-size: 32rpx;
  margin-left: 5rpx;
}

/* 商品详情卡片 */
.detail-card.data-v-c0c46a90 {
  background-color: #FFFFFF;
  border-radius: 35rpx;
  padding: 30rpx;
  margin: 0 30rpx 30rpx 30rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);
}
.detail-card .detail-header.data-v-c0c46a90 {
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #EFEFEF;
  padding-bottom: 20rpx;
}
.detail-card .detail-title.data-v-c0c46a90 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.detail-card .detail-content.data-v-c0c46a90 {
  display: flex;
  flex-direction: column;
}
.detail-card .detail-text.data-v-c0c46a90 {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 20rpx;
}
.detail-card .detail-image.data-v-c0c46a90 {
  width: 100%;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.05);
}

/* 底部购买栏 */
.bottom-bar.data-v-c0c46a90 {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100rpx;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.05);
  z-index: 99;
}
.bottom-bar .bottom-left.data-v-c0c46a90 {
  display: flex;
  align-items: center;
}
.bottom-bar .action-btn.data-v-c0c46a90 {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 30rpx;
}
.bottom-bar .action-btn .action-icon.data-v-c0c46a90 {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 5rpx;
}
.bottom-bar .action-btn text.data-v-c0c46a90 {
  font-size: 22rpx;
  color: #8E8E93;
}
.bottom-bar .buy-buttons.data-v-c0c46a90 {
  display: flex;
}
.bottom-bar .buy-btn.data-v-c0c46a90 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 240rpx;
  height: 80rpx;
  border-radius: 40rpx;
  box-shadow: 0 8rpx 16rpx rgba(255, 59, 48, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.bottom-bar .buy-btn.data-v-c0c46a90:active {
  transform: translateY(5rpx);
  box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.15);
}
.bottom-bar .flash-buy.data-v-c0c46a90 {
  background: linear-gradient(135deg, #FF3B30 0%, #FF6B6B 100%);
}
.bottom-bar .flash-buy .buy-price.data-v-c0c46a90 {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2rpx;
}
.bottom-bar .flash-buy .buy-label.data-v-c0c46a90 {
  font-size: 28rpx;
  font-weight: 600;
  color: #FFFFFF;
}