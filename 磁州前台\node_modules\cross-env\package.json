{"name": "cross-env", "version": "5.2.1", "description": "Run scripts that set and use environment variables across platforms", "main": "dist/index.js", "bin": {"cross-env": "dist/bin/cross-env.js", "cross-env-shell": "dist/bin/cross-env-shell.js"}, "engines": {"node": ">=4.0"}, "scripts": {"add-contributor": "kcd-scripts contributors add", "build": "kcd-scripts build", "lint": "kcd-scripts lint", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot --coverage", "validate": "kcd-scripts validate", "precommit": "kcd-scripts precommit"}, "files": ["dist"], "keywords": ["cross-environment", "environment variable", "windows"], "author": "<PERSON> <PERSON> <<EMAIL>> (http://kentcdodds.com/)", "license": "MIT", "dependencies": {"cross-spawn": "^6.0.5"}, "devDependencies": {"kcd-scripts": "^0.3.4"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "repository": {"type": "git", "url": "https://github.com/kentcdodds/cross-env.git"}, "bugs": {"url": "https://github.com/kentcdodds/cross-env/issues"}, "homepage": "https://github.com/kentcdodds/cross-env#readme"}