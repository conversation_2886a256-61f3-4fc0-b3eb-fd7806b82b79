<template>
  <view class="activity-detail-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">活动详情</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 活动封面 -->
    <view class="activity-cover" :style="{ paddingTop: navbarHeight + 'px' }">
      <image class="cover-image" :src="activityInfo.coverImage" mode="aspectFill"></image>
      <view class="activity-status" :class="getStatusClass(activityInfo.status)">
        {{getStatusText(activityInfo.status)}}
      </view>
    </view>
    
    <!-- 活动信息卡片 -->
    <view class="activity-card">
      <view class="activity-title">{{activityInfo.title}}</view>
      <view class="activity-meta">
        <view class="meta-item">
          <image class="meta-icon" src="/static/images/tabbar/时间.png"></image>
          <text class="meta-text">{{activityInfo.time}}</text>
        </view>
        <view class="meta-item">
          <image class="meta-icon" src="/static/images/tabbar/地址.png"></image>
          <text class="meta-text">{{activityInfo.location}}</text>
        </view>
        <view class="meta-item">
          <image class="meta-icon" src="/static/images/tabbar/人数.png"></image>
          <text class="meta-text">{{activityInfo.participants}}人已参与</text>
        </view>
        <view class="meta-item">
          <image class="meta-icon" src="/static/images/tabbar/商家.png"></image>
          <text class="meta-text">{{activityInfo.organizer}}</text>
        </view>
      </view>
    </view>
    
    <!-- 我的奖励 -->
    <view class="rewards-card" v-if="userRewards.length > 0">
      <view class="section-header">
        <text class="section-title">我的奖励</text>
        <text class="section-more" @click="navigateTo('/pages/user-center/activity-rewards')">查看全部</text>
      </view>
      
      <view class="rewards-list">
        <view class="reward-item" v-for="(item, index) in userRewards" :key="index" @click="viewRewardDetail(item)">
          <view class="reward-left">
            <image class="reward-icon" :src="getRewardTypeIcon(item.type)"></image>
          </view>
          <view class="reward-info">
            <text class="reward-name">{{item.name}}</text>
            <text class="reward-desc">{{item.description}}</text>
            <text class="reward-time">{{item.time}}</text>
          </view>
          <view class="reward-right">
            <text class="reward-amount" v-if="item.type === 'coupon' || item.type === 'redPacket'">¥{{item.amount}}</text>
            <text class="reward-amount" v-else-if="item.type === 'points'">+{{item.amount}}</text>
            <text class="reward-status" :class="{'status-used': item.status === 'used', 'status-expired': item.status === 'expired'}">
              {{getRewardStatusText(item.status)}}
            </text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 活动详情 -->
    <view class="detail-card">
      <view class="section-header">
        <text class="section-title">活动详情</text>
      </view>
      <rich-text class="detail-content" :nodes="activityInfo.content"></rich-text>
    </view>
    
    <!-- 活动规则 -->
    <view class="rules-card">
      <view class="section-header">
        <text class="section-title">活动规则</text>
      </view>
      <view class="rules-content">
        <view class="rule-item" v-for="(rule, index) in activityInfo.rules" :key="index">
          <text class="rule-number">{{index + 1}}</text>
          <text class="rule-text">{{rule}}</text>
        </view>
      </view>
    </view>
    
    <!-- 相关推荐 -->
    <view class="related-card">
      <view class="section-header">
        <text class="section-title">相关推荐</text>
        <text class="section-more" @click="navigateTo('/subPackages/activity/pages/list')">更多活动</text>
      </view>
      
      <scroll-view scroll-x class="related-scroll">
        <view class="related-list">
          <view class="related-item" v-for="(item, index) in relatedActivities" :key="index" @click="navigateTo('/subPackages/activity/pages/detail?id=' + item.id)">
            <image class="related-image" :src="item.image" mode="aspectFill"></image>
            <view class="related-info">
              <text class="related-title">{{item.title}}</text>
              <text class="related-time">{{item.time}}</text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="footer-bar">
      <view class="footer-left">
        <button class="share-btn" open-type="share">
          <image class="share-icon" src="/static/images/tabbar/分享.png"></image>
          <text class="share-text">分享</text>
        </button>
      </view>
      <view class="footer-right">
        <view class="action-btn primary-btn" v-if="activityInfo.status === 'ongoing'" @click="joinActivity">
          立即参与
        </view>
        <view class="action-btn disabled-btn" v-else-if="activityInfo.status === 'upcoming'">
          即将开始
        </view>
        <view class="action-btn disabled-btn" v-else-if="activityInfo.status === 'ended'">
          活动已结束
        </view>
        <view class="action-btn secondary-btn" @click="navigateTo('/pages/merchant-center/merchant')">
          商家入口
        </view>
      </view>
    </view>
    
    <!-- 参与成功弹窗 -->
    <view class="success-popup" v-if="showSuccessPopup" @click="closeSuccessPopup">
      <view class="success-content" @click.stop>
        <view class="success-header">
          <text class="success-title">参与成功</text>
          <view class="close-btn" @click="closeSuccessPopup">×</view>
        </view>
        <view class="success-body">
          <image class="success-icon" src="/static/images/success.png"></image>
          <text class="success-message">恭喜您成功参与活动</text>
          <text class="success-desc">您获得了以下奖励</text>
          
          <view class="popup-rewards">
            <view class="popup-reward-item" v-for="(item, index) in newRewards" :key="index">
              <image class="popup-reward-icon" :src="getRewardTypeIcon(item.type)"></image>
              <view class="popup-reward-info">
                <text class="popup-reward-name">{{item.name}}</text>
                <text class="popup-reward-desc">{{item.description}}</text>
              </view>
              <text class="popup-reward-amount" v-if="item.type === 'coupon' || item.type === 'redPacket'">¥{{item.amount}}</text>
              <text class="popup-reward-amount" v-else-if="item.type === 'points'">+{{item.amount}}</text>
            </view>
          </view>
        </view>
        <view class="success-footer">
          <view class="success-btn" @click="navigateTo('/pages/user-center/my-benefits')">
            查看我的福利
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20,
      navbarHeight: 64,
      activityId: '',
      activityInfo: {
        id: '1',
        title: '夏日狂欢购物节',
        coverImage: '/static/images/activity/shopping.jpg',
        time: '2023-05-20 至 2023-05-30',
        location: '磁州商业广场',
        participants: 328,
        organizer: '磁州商业联盟',
        status: 'ongoing', // upcoming, ongoing, ended
        content: '<p style="text-indent:2em;">夏日狂欢购物节盛大开启！活动期间，参与商家推出多重优惠，满100减50，多买多送。</p><p style="text-indent:2em;">同时，现场设有抽奖环节，有机会赢取iPhone、平板电脑等大奖。</p><p style="text-indent:2em;">此外，消费满额即可获得精美礼品一份，数量有限，先到先得。</p><p style="text-indent:2em;">活动详情请咨询各参与商家。</p>',
        rules: [
          '活动时间：2023年5月20日至5月30日',
          '参与方式：在活动期间到店消费即可参与',
          '奖励发放：系统自动发放至"我的福利"中',
          '活动规则最终解释权归商家所有'
        ]
      },
      userRewards: [
        {
          id: '1',
          type: 'coupon',
          name: '满100减20优惠券',
          description: '适用于全部商家',
          amount: 20,
          time: '2023-05-20 获得',
          status: 'available'
        },
        {
          id: '2',
          type: 'redPacket',
          name: '5元现金红包',
          description: '可直接提现至微信钱包',
          amount: 5,
          time: '2023-05-20 获得',
          status: 'available'
        }
      ],
      relatedActivities: [
        {
          id: '2',
          title: '美食品鉴会',
          time: '05-25 19:00',
          image: '/static/images/activity/food.jpg'
        },
        {
          id: '3',
          title: '亲子嘉年华',
          time: '05-28 全天',
          image: '/static/images/activity/family.jpg'
        },
        {
          id: '4',
          title: '文化艺术节',
          time: '06-01 至 06-05',
          image: '/static/images/activity/art.jpg'
        }
      ],
      showSuccessPopup: false,
      newRewards: []
    }
  },
  onLoad(options) {
    // 获取状态栏高度
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 44;
    
    // 获取活动ID
    if (options.id) {
      this.activityId = options.id;
      this.loadActivityDetail();
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 页面跳转
    navigateTo(url) {
      uni.navigateTo({
        url: url
      });
    },
    
    // 加载活动详情
    loadActivityDetail() {
      // 这里应该是实际的API调用，获取活动详情
      console.log('加载活动ID:', this.activityId);
      // 当前使用模拟数据
    },
    
    // 获取活动状态样式类
    getStatusClass(status) {
      switch (status) {
        case 'upcoming':
          return 'status-upcoming';
        case 'ongoing':
          return 'status-ongoing';
        case 'ended':
          return 'status-ended';
        default:
          return '';
      }
    },
    
    // 获取活动状态文本
    getStatusText(status) {
      switch (status) {
        case 'upcoming':
          return '即将开始';
        case 'ongoing':
          return '进行中';
        case 'ended':
          return '已结束';
        default:
          return '未知';
      }
    },
    
    // 获取奖励类型图标
    getRewardTypeIcon(type) {
      switch (type) {
        case 'coupon':
          return '/static/images/tabbar/卡券.png';
        case 'redPacket':
          return '/static/images/tabbar/我的红包.png';
        case 'points':
          return '/static/images/tabbar/每日签到.png';
        case 'gift':
          return '/static/images/tabbar/礼品.png';
        default:
          return '/static/images/tabbar/活动.png';
      }
    },
    
    // 获取奖励状态文本
    getRewardStatusText(status) {
      switch (status) {
        case 'available':
          return '可使用';
        case 'used':
          return '已使用';
        case 'expired':
          return '已过期';
        default:
          return '未知';
      }
    },
    
    // 查看奖励详情
    viewRewardDetail(item) {
      uni.showToast({
        title: '查看详情: ' + item.name,
        icon: 'none'
      });
    },
    
    // 参与活动
    joinActivity() {
      // 模拟参与活动
      setTimeout(() => {
        // 模拟获得的新奖励
        this.newRewards = [
          {
            id: '3',
            type: 'coupon',
            name: '满200减50优惠券',
            description: '限时特惠',
            amount: 50
          },
          {
            id: '4',
            type: 'points',
            name: '活动积分',
            description: '可在积分商城兑换礼品',
            amount: 100
          }
        ];
        
        // 显示成功弹窗
        this.showSuccessPopup = true;
        
        // 将新奖励添加到用户奖励列表
        this.userRewards = [...this.newRewards.map(item => ({
          ...item,
          time: '刚刚获得',
          status: 'available'
        })), ...this.userRewards];
      }, 1000);
    },
    
    // 关闭成功弹窗
    closeSuccessPopup() {
      this.showSuccessPopup = false;
    }
  },
  // 分享功能
  onShareAppMessage() {
    return {
      title: this.activityInfo.title,
      path: '/subPackages/activity/pages/detail-with-rewards?id=' + this.activityId,
      imageUrl: this.activityInfo.coverImage
    }
  }
}
</script>

<style lang="scss" scoped>
.activity-detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 100rpx;
}

/* 导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  background: transparent;
  color: #fff;
  z-index: 100;
}

.navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 36rpx;
  height: 36rpx;
}

.navbar-title {
  font-size: 18px;
  font-weight: 500;
}

.navbar-right {
  width: 60rpx;
}

/* 活动封面 */
.activity-cover {
  position: relative;
  width: 100%;
  height: 400rpx;
}

.cover-image {
  width: 100%;
  height: 100%;
}

.activity-status {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #fff;
}

.status-upcoming {
  background-color: #faad14;
}

.status-ongoing {
  background-color: #52c41a;
}

.status-ended {
  background-color: #999;
}

/* 活动信息卡片 */
.activity-card {
  margin: -40rpx 20rpx 20rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 10;
}

.activity-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.activity-meta {
  display: flex;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  margin-bottom: 16rpx;
  width: calc(50% - 30rpx);
}

.meta-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.meta-text {
  font-size: 26rpx;
  color: #666;
}

/* 我的奖励 */
.rewards-card {
  margin: 20rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.section-more {
  font-size: 24rpx;
  color: #3a7afe;
}

.rewards-list {
  margin-top: 20rpx;
}

.reward-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.reward-item:last-child {
  border-bottom: none;
}

.reward-left {
  margin-right: 20rpx;
}

.reward-icon {
  width: 80rpx;
  height: 80rpx;
}

.reward-info {
  flex: 1;
}

.reward-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
  display: block;
}

.reward-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 6rpx;
  display: block;
}

.reward-time {
  font-size: 22rpx;
  color: #999;
  display: block;
}

.reward-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.reward-amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff4d4f;
  margin-bottom: 6rpx;
}

.reward-status {
  font-size: 24rpx;
  color: #3a7afe;
  padding: 4rpx 12rpx;
  background-color: #f0f5ff;
  border-radius: 6rpx;
}

.status-used {
  color: #52c41a;
  background-color: #f6ffed;
}

.status-expired {
  color: #999;
  background-color: #f5f5f5;
}

/* 活动详情 */
.detail-card {
  margin: 20rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.detail-content {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

/* 活动规则 */
.rules-card {
  margin: 20rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.rules-content {
  margin-top: 20rpx;
}

.rule-item {
  display: flex;
  margin-bottom: 16rpx;
}

.rule-number {
  width: 40rpx;
  height: 40rpx;
  background-color: #f0f5ff;
  color: #3a7afe;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.rule-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  flex: 1;
}

/* 相关推荐 */
.related-card {
  margin: 20rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.related-scroll {
  width: 100%;
  white-space: nowrap;
}

.related-list {
  display: flex;
  padding: 10rpx 0;
}

.related-item {
  width: 300rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  display: inline-block;
}

.related-image {
  width: 100%;
  height: 180rpx;
}

.related-info {
  padding: 16rpx;
}

.related-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.related-time {
  font-size: 22rpx;
  color: #999;
}

/* 底部操作栏 */
.footer-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 99;
}

.footer-left {
  display: flex;
  align-items: center;
}

.share-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: transparent;
  padding: 0;
  margin: 0;
  line-height: 1;
  border: none;
  outline: none;
}

.share-btn::after {
  border: none;
}

.share-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 6rpx;
}

.share-text {
  font-size: 22rpx;
  color: #666;
}

.footer-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  height: 70rpx;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 30rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
}

.primary-btn {
  background: linear-gradient(135deg, #3a7afe, #6ca6ff);
  color: #fff;
}

.secondary-btn {
  background-color: #f0f5ff;
  color: #3a7afe;
}

.disabled-btn {
  background-color: #f5f5f5;
  color: #999;
}

/* 参与成功弹窗 */
.success-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.success-content {
  width: 600rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.success-header {
  position: relative;
  padding: 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.success-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.success-body {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.success-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.success-message {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.success-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.popup-rewards {
  width: 100%;
  margin-top: 20rpx;
}

.popup-reward-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.popup-reward-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 16rpx;
}

.popup-reward-info {
  flex: 1;
}

.popup-reward-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
  display: block;
}

.popup-reward-desc {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.popup-reward-amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff4d4f;
}

.success-footer {
  padding: 20rpx 30rpx 30rpx;
}

.success-btn {
  height: 80rpx;
  background: linear-gradient(135deg, #3a7afe, #6ca6ff);
  color: #fff;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}
</style>
