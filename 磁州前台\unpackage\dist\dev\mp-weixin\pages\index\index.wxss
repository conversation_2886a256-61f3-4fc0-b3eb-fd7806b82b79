/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.index-container {
  position: relative;
  background-color: #F5F6FA;
  min-height: 100vh;
}

/* 导航栏图标样式 */
.navbar-icon {
  width: 44rpx;
  height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transition: all 0.3s;
}
.navbar-icon-hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}
.icon-image {
  width: 28rpx;
  height: 28rpx;
  filter: brightness(0) invert(1);
}

/* 可滚动的弧形背景 */
.content-bg-scroll {
  position: absolute;
  top: 15rpx;
  /* 向下移动15rpx */
  left: 0;
  right: 0;
  height: 400rpx;
  /* 保持原始高度不变 */
  background-color: #1677FF;
  border-bottom-left-radius: 80rpx;
  border-bottom-right-radius: 80rpx;
  z-index: 1;
}

/* 页面内容区域 */
.scrollable-content {
  position: relative;
  z-index: 2;
  box-sizing: border-box;
  padding-top: 20rpx;
  /* 添加顶部内边距，与导航栏保持一定距离 */
}

/* 引导关注模块样式 */
.follow-guide-in-bg {
  position: relative;
  padding: 0 60rpx;
  margin: 15rpx 0 30rpx 0;
  /* 将顶部边距调整为15rpx，从原始10rpx增加5rpx */
  z-index: 10;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
}
.follow-guide-content {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border-radius: 25rpx;
  padding: 16rpx 26rpx 16rpx 75rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 12rpx 25rpx rgba(0, 0, 0, 0.15), 0 4rpx 8rpx rgba(0, 0, 0, 0.08), inset 0 0 0 1px rgba(255, 255, 255, 0.8);
  width: 100%;
  box-sizing: border-box;
  max-width: 650rpx;
  position: relative;
  /* overflow: hidden; */
}
.follow-guide-content::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(22, 119, 255, 0.05), rgba(0, 82, 204, 0.02));
  z-index: 0;
}
.follow-avatar {
  width: 55rpx;
  height: 55rpx;
  border-radius: 50%;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.4), 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  object-fit: contain;
  position: absolute;
  left: 10rpx;
  top: 50%;
  transform: translateY(-50%);
  border: 3rpx solid #fff;
  z-index: 5;
  background: #fff;
  padding: 0;
}
@keyframes gentle-pulse {
0% {
    transform: translateY(-50%) scale(1);
    -webkit-transform: translateY(-50%) scale(1);
}
50% {
    transform: translateY(-50%) scale(1.03);
    -webkit-transform: translateY(-50%) scale(1.03);
}
100% {
    transform: translateY(-50%) scale(1);
    -webkit-transform: translateY(-50%) scale(1);
}
}
.follow-guide-content:hover .follow-avatar {
  box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.45), 0 5rpx 10rpx rgba(0, 0, 0, 0.25);
  animation: none;
  transform: translateY(-50%) scale(1.06);
  -webkit-transform: translateY(-50%) scale(1.06);
}
.follow-text {
  flex: 1;
  font-size: 20rpx;
  color: #333;
  line-height: 1.3;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 4rpx;
  margin-left: 8rpx;
  position: relative;
  z-index: 1;
}
.follow-btn-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}
.follow-btn {
  background: linear-gradient(145deg, #394FC2, #4A67D7);
  color: #ffffff;
  font-size: 22rpx;
  padding: 0 22rpx;
  height: 54rpx;
  line-height: 54rpx;
  border-radius: 27rpx;
  margin: 0;
  position: relative;
  z-index: 2;
  box-shadow: 5rpx 5rpx 10rpx rgba(61, 86, 193, 0.3), -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.3), inset 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.2);
  font-weight: 600;
  border: none;
  transition: all 0.2s ease;
  text-align: center;
}
.follow-btn:active {
  transform: scale(0.96);
  box-shadow: 3rpx 3rpx 6rpx rgba(61, 86, 193, 0.3), -1rpx -1rpx 3rpx rgba(255, 255, 255, 0.3), inset 1rpx 1rpx 3rpx rgba(0, 0, 0, 0.1);
}
.follow-close {
  position: absolute;
  top: -5rpx;
  right: -5rpx;
  width: 26rpx;
  height: 26rpx;
  background-color: rgba(0, 0, 0, 0.3);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18rpx;
  z-index: 10;
}

/* 轮播图 */
.banner-outer {
  position: relative;
  margin: 25rpx 30rpx 50rpx;
  /* 将顶部边距调整为25rpx，从原始20rpx增加5rpx */
  border-radius: 30rpx;
  overflow: hidden;
  box-shadow: 0 25rpx 45rpx rgba(0, 0, 0, 0.35), 0 15rpx 25rpx rgba(0, 0, 0, 0.2);
  border: 12rpx solid #ffffff;
  transform: translateZ(0);
  z-index: 3;
  animation: float 6s ease-in-out infinite;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  padding: 2rpx;
  background-color: rgba(255, 255, 255, 0.8);
}
@keyframes float {
0% {
    transform: translateY(0) translateZ(0);
}
50% {
    transform: translateY(-10rpx) translateZ(0);
}
100% {
    transform: translateY(0) translateZ(0);
}
}
.banner-swiper {
  width: 100%;
  height: 300rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
}
.banner-item {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FFFFFF;
}
.banner-image {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
  box-shadow: inset 0 0 15rpx rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
}

/* 白色内容区域 */
.white-content {
  background: #f8f9fc;
  position: relative;
  padding-top: 40rpx;
  padding-bottom: 20rpx;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  box-shadow: 0 -6rpx 20rpx rgba(0, 0, 0, 0.03);
  margin-top: -40rpx;
  z-index: 4;
}

/* 搜索框样式 */
.search-container {
  padding: 10rpx 30rpx 35rpx;
}
.search-box {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  border-radius: 40rpx;
  padding: 0 20rpx 0 30rpx;
  height: 88rpx;
  border: 1rpx solid #eaedf2;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.search-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
  opacity: 0.4;
}
.search-input {
  flex: 1;
  height: 88rpx;
  font-size: 28rpx;
  color: #333;
}
.search-placeholder {
  color: #b8bdcc;
  font-size: 28rpx;
}
.search-button {
  width: auto;
  min-width: 90rpx;
  height: 60rpx;
  line-height: 60rpx;
  background: #3A86FF;
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
  border-radius: 30rpx;
  padding: 0 20rpx;
  margin-right: 10rpx;
  letter-spacing: 1rpx;
  text-align: center;
}

/* 二维码弹窗 */
.qrcode-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.qrcode-card {
  width: 560rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
}
.qrcode-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 20rpx 20rpx;
  border-bottom: 1px solid #F5F5F5;
}
.qrcode-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.close-btn {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 40rpx;
}
.qrcode-content {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.qrcode-image {
  width: 320rpx;
  height: 320rpx;
  margin-bottom: 20rpx;
}
.qrcode-tips {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.qrcode-desc {
  font-size: 24rpx;
  color: #999;
}

/* 服务分类弹窗 */
.service-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  flex-direction: column;
}
.service-popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.service-popup-content {
  position: relative;
  background-color: #FFFFFF;
  border-radius: 30rpx 30rpx 0 0;
  padding: 30rpx;
  margin-top: auto;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease;
}
@keyframes slideUp {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
.service-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.service-popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.service-popup-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 40rpx;
}
.service-popup-scroll {
  flex: 1;
  max-height: 60vh;
}
.service-popup-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 10rpx 0;
}
.service-popup-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}
.service-popup-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}
.service-popup-name {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

/* 位置选择弹窗 */
.location-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  flex-direction: column;
}
.location-popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.location-popup-content {
  position: relative;
  background-color: #FFFFFF;
  border-radius: 30rpx 30rpx 0 0;
  padding: 30rpx;
  margin-top: auto;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease;
}
.location-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.location-popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.location-popup-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 40rpx;
}
.location-list {
  max-height: 60vh;
  overflow-y: auto;
}
.location-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #F2F2F7;
}
.location-item:last-child {
  border-bottom: none;
}
.location-item.active {
  color: #1677FF;
}
.location-item-name {
  font-size: 30rpx;
}

/* 统一字体样式 */
text, input, button {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Helvetica, Arial, sans-serif;
}

/* 去除所有按钮的默认边框 */
button {
  border: none;
  outline: none;
  background-color: transparent;
  -webkit-appearance: none;
  appearance: none;
}
button::after {
  border: none;
  outline: none;
}

/* -- 自定义导航栏样式 -- */
.custom-nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #1677FF;
  z-index: 999;
  box-shadow: 0 4rpx 12rpx rgba(22, 119, 255, 0.2);
  display: flex;
  flex-direction: column;
}
.nav-status-bar {
  width: 100%;
}
.nav-bar-content {
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.nav-bar-title {
  color: #FFFFFF;
  font-size: 34rpx;
  font-weight: 600;
  text-align: center;
  margin-right: 24px;
  /* 右侧留出一定空间，平衡胶囊按钮 */
}

/* -- 自定义导航栏样式结束 -- */