{"version": 3, "file": "permission.js", "sources": ["utils/permission.js"], "sourcesContent": ["/**\n * 权限管理工具\n */\n\n// 检查位置权限\nexport const checkLocationPermission = () => {\n  return new Promise((resolve) => {\n    uni.getSetting({\n      success: (res) => {\n        // 检查位置权限状态\n        if (res.authSetting['scope.userLocation'] === true) {\n          // 已授权\n          resolve('authorized');\n        } else if (res.authSetting['scope.userLocation'] === false) {\n          // 已拒绝\n          resolve('denied');\n        } else {\n          // 未设置过（首次使用）\n          resolve('not_determined');\n        }\n      },\n      fail: () => {\n        // 获取设置失败，返回未确定状态\n        resolve('not_determined');\n      }\n    });\n  });\n};\n\n// 请求位置权限\nexport const requestLocationPermission = () => {\n  return new Promise((resolve, reject) => {\n    uni.authorize({\n      scope: 'scope.userLocation',\n      success: () => {\n        resolve('authorized');\n      },\n      fail: (err) => {\n        // 用户拒绝授权\n        reject({\n          errMsg: '用户拒绝位置授权',\n          errCode: 'AUTH_DENIED',\n          originalError: err\n        });\n      }\n    });\n  });\n};\n\n// 打开设置页面\nexport const openSettings = () => {\n  return new Promise((resolve, reject) => {\n    uni.openSetting({\n      success: (res) => {\n        resolve(res.authSetting);\n      },\n      fail: (err) => {\n        reject(err);\n      }\n    });\n  });\n};\n\n// 检查并请求通知权限\nexport const checkNotificationPermission = () => {\n  return new Promise((resolve, reject) => {\n    // #ifdef MP-WEIXIN\n    uni.getSetting({\n      withSubscriptions: true,\n      success: (res) => {\n        if (res.authSetting['scope.userNotification']) {\n          resolve(true);\n        } else {\n          uni.authorize({\n            scope: 'scope.userNotification',\n            success: () => {\n              resolve(true);\n            },\n            fail: (err) => {\n              reject({\n                errMsg: '用户拒绝授权通知',\n                errCode: 'AUTH_FAILED',\n                originalError: err\n              });\n            }\n          });\n        }\n      },\n      fail: reject\n    });\n    // #endif\n    \n    // #ifdef APP-PLUS || H5\n    // App或H5环境下通知逻辑\n    resolve(true);\n    // #endif\n  });\n};\n\n// 检查用户信息权限\nexport const checkUserInfoPermission = () => {\n  return new Promise((resolve, reject) => {\n    // #ifdef MP-WEIXIN\n    uni.getSetting({\n      success: (res) => {\n        if (res.authSetting['scope.userInfo']) {\n          resolve(true);\n        } else {\n          reject({\n            errMsg: '未授权用户信息',\n            errCode: 'NO_AUTH'\n          });\n        }\n      },\n      fail: reject\n    });\n    // #endif\n    \n    // #ifdef APP-PLUS || H5\n    // App或H5环境下用户信息逻辑\n    resolve(true);\n    // #endif\n  });\n}; "], "names": ["uni"], "mappings": ";;AAKY,MAAC,0BAA0B,MAAM;AAC3C,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9BA,kBAAAA,MAAI,WAAW;AAAA,MACb,SAAS,CAAC,QAAQ;AAEhB,YAAI,IAAI,YAAY,oBAAoB,MAAM,MAAM;AAElD,kBAAQ,YAAY;AAAA,QACrB,WAAU,IAAI,YAAY,oBAAoB,MAAM,OAAO;AAE1D,kBAAQ,QAAQ;AAAA,QAC1B,OAAe;AAEL,kBAAQ,gBAAgB;AAAA,QACzB;AAAA,MACF;AAAA,MACD,MAAM,MAAM;AAEV,gBAAQ,gBAAgB;AAAA,MACzB;AAAA,IACP,CAAK;AAAA,EACL,CAAG;AACH;AAuBY,MAAC,eAAe,MAAM;AAChC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,kBAAAA,MAAI,YAAY;AAAA,MACd,SAAS,CAAC,QAAQ;AAChB,gBAAQ,IAAI,WAAW;AAAA,MACxB;AAAA,MACD,MAAM,CAAC,QAAQ;AACb,eAAO,GAAG;AAAA,MACX;AAAA,IACP,CAAK;AAAA,EACL,CAAG;AACH;;;"}