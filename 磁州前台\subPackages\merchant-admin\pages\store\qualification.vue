<template>
  <view class="qualification-container">
    <!-- 顶部导航栏 -->
    <view class="header-area">
      <view class="safe-area-top"></view>
      <view class="custom-navbar">
        <view class="navbar-left" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="back-icon" style="filter: brightness(0) invert(1);"></image>
        </view>
        <view class="navbar-title">资质管理</view>
        <view class="navbar-right"></view>
      </view>
    </view>
    
    <!-- 背景装饰 -->
    <view class="bg-decoration bg-circle-1"></view>
    <view class="bg-decoration bg-circle-2"></view>
    <view class="bg-decoration bg-circle-3"></view>
    
    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 资质管理介绍 -->
      <view class="intro-section">
        <view class="intro-content">
          <view class="intro-title">提升店铺等级，获得更多曝光</view>
          <view class="intro-desc">根据行业特点，完善相关资质，提高商户星级与可信度</view>
        </view>
        <image src="/static/images/qualification-banner.png" class="intro-image"></image>
      </view>
      
      <!-- 资质类别 -->
      <view class="qualification-grid">
        <!-- 商家认证 -->
        <view class="grid-item" @click="navigate('/subPackages/merchant-admin/pages/store/verify')">
          <view class="item-icon" :class="{ 'verified': qualificationStatus.storeVerify }">
            <image src="/static/images/verify-icon.png" class="icon-image"></image>
            <view class="status-badge" v-if="qualificationStatus.storeVerify">√</view>
          </view>
          <view class="item-content">
            <view class="item-name">商家认证</view>
            <view class="item-desc">企业/个体工商户认证</view>
          </view>
        </view>
        
        <!-- 资质管理 -->
        <view class="grid-item" @click="navigate('/subPackages/merchant-admin/pages/store/license')">
          <view class="item-icon" :class="{ 'verified': qualificationStatus.license }">
            <image src="/static/images/license-icon.png" class="icon-image"></image>
            <view class="status-badge" v-if="qualificationStatus.license">√</view>
          </view>
          <view class="item-content">
            <view class="item-name">资质管理</view>
            <view class="item-desc">营业执照，行业资质</view>
          </view>
        </view>
        
        <!-- 到期提醒 -->
        <view class="grid-item" @click="navigate('/subPackages/merchant-admin/pages/store/expiration')">
          <view class="item-icon" :class="{ 'expired': hasExpiredItems }">
            <image src="/static/images/expiration-icon.png" class="icon-image"></image>
            <view class="warning-badge" v-if="hasExpiredItems">!</view>
          </view>
          <view class="item-content">
            <view class="item-name">到期提醒</view>
            <view class="item-desc">资质到期提醒和续期</view>
          </view>
        </view>
      </view>
      
      <!-- 资质提交记录 -->
      <view class="records-section">
        <view class="section-header">
          <text class="section-title">资质提交记录</text>
        </view>
        
        <view class="records-list" v-if="records.length > 0">
          <view class="record-item" v-for="(record, index) in records" :key="index">
            <view class="record-icon">
              <image :src="getStatusIcon(record.status)" class="status-image"></image>
            </view>
            <view class="record-content">
              <view class="record-title">{{record.title}}</view>
              <view class="record-desc">{{record.desc}}</view>
              <view class="record-time">{{record.time}}</view>
            </view>
            <view class="record-status" :class="record.status">{{getStatusText(record.status)}}</view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-state" v-else>
          <image class="empty-icon" src="/static/images/tabbar/无数据.png"></image>
          <text class="empty-text">暂无资质提交记录</text>
        </view>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20,
      qualificationStatus: {
        storeVerify: true,
        license: false
      },
      hasExpiredItems: true,
      records: [
        {
          title: '餐饮服务许可证上传',
          desc: '已通过审核，请在到期前及时更新',
          time: '2023-10-10 15:30',
          status: 'approved'
        },
        {
          title: '健康证上传',
          desc: '已提交，等待平台审核',
          time: '2023-10-05 09:15',
          status: 'pending'
        },
        {
          title: '食品经营许可证更新',
          desc: '审核未通过，请修改后重新提交',
          time: '2023-09-28 14:45',
          status: 'rejected'
        }
      ]
    }
  },
  onLoad() {
    this.setStatusBarHeight();
  },
  methods: {
    setStatusBarHeight() {
      uni.getSystemInfo({
        success: (res) => {
          this.statusBarHeight = res.statusBarHeight;
          if (typeof document !== 'undefined') {
            document.documentElement.style.setProperty('--status-bar-height', `${this.statusBarHeight}px`);
          }
        }
      });
    },
    goBack() {
      uni.navigateBack();
    },
    navigate(url) {
      uni.navigateTo({
        url: url
      });
    },
    getStatusIcon(status) {
      const icons = {
        'pending': '/static/images/pending-icon.png',
        'approved': '/static/images/approved-icon.png',
        'rejected': '/static/images/rejected-icon.png'
      };
      return icons[status] || icons.pending;
    },
    getStatusText(status) {
      const texts = {
        'pending': '审核中',
        'approved': '已通过',
        'rejected': '未通过'
      };
      return texts[status] || '未知状态';
    }
  }
}
</script>

<style lang="scss">
.qualification-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(110rpx + var(--status-bar-height, 20px));
}

/* 顶部导航栏样式 */
.header-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
  color: #fff;
}

.safe-area-top {
  height: var(--status-bar-height, 20px);
  width: 100%;
}

.custom-navbar {
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
}

.navbar-left, .navbar-right {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 48rpx;
  height: 48rpx;
}

/* 背景装饰样式 */
.bg-decoration {
  position: absolute;
  z-index: -1;
  border-radius: 50%;
  opacity: 0.07;
  background-color: #0A84FF;
}

.bg-circle-1 {
  top: -100rpx;
  right: -150rpx;
  width: 400rpx;
  height: 400rpx;
}

.bg-circle-2 {
  top: 20%;
  left: -200rpx;
  width: 500rpx;
  height: 500rpx;
}

.bg-circle-3 {
  bottom: 10%;
  right: -100rpx;
  width: 300rpx;
  height: 300rpx;
}

.content-scroll {
  padding: 30rpx;
}

/* 介绍区域 */
.intro-section {
  background: linear-gradient(135deg, #0A84FF, #0055FF);
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  color: #FFFFFF;
  box-shadow: 0 8rpx 20rpx rgba(10, 132, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.intro-content {
  flex: 1;
  padding-right: 20rpx;
  z-index: 1;
}

.intro-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
}

.intro-desc {
  font-size: 26rpx;
  opacity: 0.9;
}

.intro-image {
  width: 140rpx;
  height: 140rpx;
  z-index: 1;
}

/* 资质网格 */
.qualification-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
  margin-bottom: 30rpx;
}

.grid-item {
  width: calc(33.33% - 20rpx);
  margin: 10rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.item-icon {
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(10, 132, 255, 0.1);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-bottom: 16rpx;
}

.item-icon.verified {
  background-color: rgba(52, 199, 89, 0.1);
}

.item-icon.expired {
  background-color: rgba(255, 59, 48, 0.1);
}

.icon-image {
  width: 40rpx;
  height: 40rpx;
}

.status-badge {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  width: 30rpx;
  height: 30rpx;
  background-color: #34C759;
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  font-size: 20rpx;
}

.warning-badge {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  width: 30rpx;
  height: 30rpx;
  background-color: #FF3B30;
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  font-size: 20rpx;
}

.item-content {
  text-align: center;
}

.item-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 6rpx;
}

.item-desc {
  font-size: 22rpx;
  color: #666;
}

/* 资质记录区域 */
.records-section {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 8rpx;
  height: 32rpx;
  background-color: #0A84FF;
  border-radius: 4rpx;
}

.records-list {
  margin-top: 20rpx;
}

.record-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #F2F2F7;
}

.record-item:last-child {
  border-bottom: none;
}

.record-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.status-image {
  width: 100%;
  height: 100%;
}

.record-content {
  flex: 1;
  margin-right: 20rpx;
}

.record-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.record-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 6rpx;
}

.record-time {
  font-size: 22rpx;
  color: #999;
}

.record-status {
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
}

.record-status.approved {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.record-status.pending {
  background-color: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}

.record-status.rejected {
  background-color: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}

/* 空状态 */
.empty-state {
  padding: 60rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 40rpx;
}

/* 适配暗黑模式 */
@media (prefers-color-scheme: dark) {
  .qualification-container {
    background-color: #1C1C1E;
  }
  
  .grid-item,
  .records-section {
    background-color: #2C2C2E;
  }
  
  .section-title,
  .item-name,
  .record-title {
    color: #FFFFFF;
  }
  
  .item-desc,
  .record-desc {
    color: #A8A8A8;
  }
  
  .record-time {
    color: #8E8E93;
  }
  
  .record-item {
    border-color: #3A3A3C;
  }
  
  .empty-text {
    color: #8E8E93;
  }
}
</style> 