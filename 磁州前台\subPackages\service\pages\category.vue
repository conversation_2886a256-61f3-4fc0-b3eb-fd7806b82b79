<template>
  <view class="service-category-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="back-btn" @click="navigateBack">
        <view class="back-icon"></view>
      </view>
      <view class="navbar-title">选择服务类型</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 提示文本 -->
    <view class="service-tip">
      <text class="tip-text">请选择服务类型</text>
      <text class="tip-desc">选择您需要的服务类型，查看相关信息</text>
    </view>
    
    <!-- 服务类型列表 -->
    <view class="service-grid">
      <!-- 家政服务 -->
      <view class="service-item" @click="selectServiceType('家政服务', 'home_cleaning')">
        <view class="service-icon-wrapper">
          <image class="service-icon" src="/static/images/tabbar/123/家政服务.png" mode="aspectFit"></image>
        </view>
        <text class="service-name">家政服务</text>
      </view>
      
      <!-- 维修改造 -->
      <view class="service-item" @click="selectServiceType('维修改造', 'repair')">
        <view class="service-icon-wrapper">
          <image class="service-icon" src="/static/images/tabbar/123/维修改造.png" mode="aspectFit"></image>
        </view>
        <text class="service-name">维修改造</text>
      </view>
      
      <!-- 上门安装 -->
      <view class="service-item" @click="selectServiceType('上门安装', 'installation')">
        <view class="service-icon-wrapper">
          <image class="service-icon" src="/static/images/tabbar/123/上门安装.png" mode="aspectFit"></image>
        </view>
        <text class="service-name">上门安装</text>
      </view>
      
      <!-- 开锁换锁 -->
      <view class="service-item" @click="selectServiceType('开锁换锁', 'locksmith')">
        <view class="service-icon-wrapper">
          <image class="service-icon" src="/static/images/tabbar/123/开锁换锁.png" mode="aspectFit"></image>
        </view>
        <text class="service-name">开锁换锁</text>
      </view>
      
      <!-- 搬家拉货 -->
      <view class="service-item" @click="selectServiceType('搬家拉货', 'moving')">
        <view class="service-icon-wrapper">
          <image class="service-icon" src="/static/images/tabbar/123/搬家拉货.png" mode="aspectFit"></image>
        </view>
        <text class="service-name">搬家拉货</text>
      </view>
      
      <!-- 上门美容 -->
      <view class="service-item" @click="selectServiceType('上门美容', 'beauty')">
        <view class="service-icon-wrapper">
          <image class="service-icon" src="/static/images/tabbar/123/上门美容.png" mode="aspectFit"></image>
        </view>
        <text class="service-name">上门美容</text>
      </view>
      
      <!-- 上门家教 -->
      <view class="service-item" @click="selectServiceType('上门家教', 'tutor')">
        <view class="service-icon-wrapper">
          <image class="service-icon" src="/static/images/tabbar/123/上门家教.png" mode="aspectFit"></image>
        </view>
        <text class="service-name">上门家教</text>
      </view>
      
      <!-- 宠物服务 -->
      <view class="service-item" @click="selectServiceType('宠物服务', 'pet_service')">
        <view class="service-icon-wrapper">
          <image class="service-icon" src="/static/images/tabbar/123/宠物服务.png" mode="aspectFit"></image>
        </view>
        <text class="service-name">宠物服务</text>
      </view>
      
      <!-- 上门疏通 -->
      <view class="service-item" @click="selectServiceType('上门疏通', 'plumbing')">
        <view class="service-icon-wrapper">
          <image class="service-icon" src="/static/images/tabbar/123/上门疏通.png" mode="aspectFit"></image>
        </view>
        <text class="service-name">上门疏通</text>
      </view>
      
      <!-- 其他类型 -->
      <view class="service-item" @click="selectServiceType('其他类型', 'other')">
        <view class="service-icon-wrapper">
          <image class="service-icon" src="/static/images/tabbar/123/其他类型.png" mode="aspectFit"></image>
        </view>
        <text class="service-name">其他类型</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 响应式状态
const statusBarHeight = ref(20);

// 方法
// 返回上一页
const navigateBack = () => {
  uni.navigateBack();
};

// 选择服务类型
const selectServiceType = (name, type) => {
  uni.navigateTo({
    url: `/subPackages/service/pages/home-service-list?subType=${type}&subName=${encodeURIComponent(name)}`
  });
};

// 生命周期钩子
onMounted(() => {
  // 获取状态栏高度
  const sysInfo = uni.getSystemInfoSync();
  statusBarHeight.value = sysInfo.statusBarHeight;
});
</script>

<style>
.service-category-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 30rpx;
}

.custom-navbar {
  display: flex;
  align-items: center;
  height: 44px;
  background-color: #1677FF;
  padding-left: 10px;
  padding-right: 10px;
  position: relative;
  z-index: 100;
}

.back-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-top: 2px solid #fff;
  border-left: 2px solid #fff;
  transform: rotate(-45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  color: #fff;
  font-size: 18px;
  font-weight: 500;
}

.navbar-right {
  width: 40px;
  height: 40px;
}

.service-tip {
  padding: 30rpx;
  background-color: #fff;
}

.tip-text {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.tip-desc {
  font-size: 28rpx;
  color: #999;
}

.service-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
  background-color: #fff;
  margin-top: 20rpx;
}

.service-item {
  width: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.service-icon-wrapper {
  width: 140rpx;
  height: 140rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16rpx;
}

.service-icon {
  width: 86rpx;
  height: 86rpx;
  object-fit: contain;
}

.service-name {
  font-size: 26rpx;
  color: #333;
  text-align: center;
}
</style> 
