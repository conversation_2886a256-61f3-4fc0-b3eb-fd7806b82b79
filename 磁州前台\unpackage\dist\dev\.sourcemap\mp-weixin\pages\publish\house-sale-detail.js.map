{"version": 3, "file": "house-sale-detail.js", "sources": ["pages/publish/house-sale-detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHVibGlzaC9ob3VzZS1zYWxlLWRldGFpbC52dWU"], "sourcesContent": ["<template>\n  <view class=\"house-sale-container\">\n    <!-- 添加自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"navbar-left\" @click=\"goBack\">\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\n      </view>\n      <view class=\"navbar-title\">房屋出售详情</view>\n      <view class=\"navbar-right\">\n        <!-- 占位 -->\n      </view>\n    </view>\n    \n    <!-- 隐藏的分享按钮，用于自动触发 -->\n    <button id=\"shareButton\" class=\"hidden-share-btn\" open-type=\"share\"></button>\n    \n    <view class=\"house-sale-wrapper\">\n      <!-- 房屋基本信息卡片 -->\n      <view class=\"content-card house-info-card\">\n        <view class=\"house-header\">\n          <view class=\"house-title-row\">\n            <text class=\"house-title\">{{houseData.title}}</text>\n            <text class=\"house-price\">{{houseData.price}}</text>\n          </view>\n          <view class=\"house-meta\">\n            <view class=\"house-tag-group\">\n              <view class=\"house-tag\" v-for=\"(tag, index) in houseData.tags\" :key=\"index\">{{tag}}</view>\n            </view>\n            <text class=\"house-publish-time\">发布于 {{formatTime(houseData.publishTime)}}</text>\n          </view>\n        </view>\n        \n        <!-- 房屋图片轮播 -->\n        <swiper class=\"house-swiper\" :indicator-dots=\"true\" :autoplay=\"true\" :interval=\"3000\" :duration=\"500\">\n          <swiper-item v-for=\"(image, index) in houseData.images\" :key=\"index\">\n            <image :src=\"image\" mode=\"aspectFill\" class=\"house-image\"></image>\n          </swiper-item>\n        </swiper>\n        \n        <!-- 基本信息 -->\n        <view class=\"house-basic-info\">\n          <view class=\"info-item\">\n            <text class=\"info-label\">房屋类型</text>\n            <text class=\"info-value\">{{houseData.type}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">所在区域</text>\n            <text class=\"info-value\">{{houseData.area}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">房屋面积</text>\n            <text class=\"info-value\">{{houseData.size}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">房屋朝向</text>\n            <text class=\"info-value\">{{houseData.orientation}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 房屋配置 -->\n      <view class=\"content-card house-config-card\">\n        <view class=\"section-title\">房屋配置</view>\n        <view class=\"config-list\">\n          <view class=\"config-item\" v-for=\"(item, index) in houseData.configs\" :key=\"index\">\n            <text class=\"config-icon iconfont\" :class=\"item.icon\"></text>\n            <text class=\"config-text\">{{item.name}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 房屋详情 -->\n      <view class=\"content-card house-detail-card\">\n        <view class=\"section-title\">房屋详情</view>\n        <view class=\"detail-list\">\n          <view class=\"detail-item\" v-for=\"(item, index) in houseData.details\" :key=\"index\">\n            <text class=\"detail-label\">{{item.label}}</text>\n            <text class=\"detail-value\">{{item.value}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 位置信息 -->\n      <view class=\"content-card location-card\">\n        <view class=\"section-title\">位置信息</view>\n        <view class=\"location-content\">\n          <view class=\"location-address\">\n            <text class=\"address-icon iconfont icon-location\"></text>\n            <text class=\"address-text\">{{houseData.location.address}}</text>\n          </view>\n          <view class=\"location-map\">\n            <map class=\"map\" :latitude=\"houseData.location.latitude\" :longitude=\"houseData.location.longitude\" :markers=\"markers\"></map>\n          </view>\n          <view class=\"location-surroundings\">\n            <view class=\"surrounding-item\" v-for=\"(item, index) in houseData.location.surroundings\" :key=\"index\">\n              <text class=\"surrounding-icon iconfont\" :class=\"item.icon\"></text>\n              <text class=\"surrounding-text\">{{item.name}}</text>\n              <text class=\"surrounding-distance\">{{item.distance}}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 交易信息 -->\n      <view class=\"content-card transaction-card\">\n        <view class=\"section-title\">交易信息</view>\n        <view class=\"transaction-content\">\n          <view class=\"transaction-item\" v-for=\"(item, index) in houseData.transaction\" :key=\"index\">\n            <text class=\"transaction-label\">{{item.label}}</text>\n            <text class=\"transaction-value\">{{item.value}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 产权信息 -->\n      <view class=\"content-card property-card\">\n        <view class=\"section-title\">产权信息</view>\n        <view class=\"property-content\">\n          <view class=\"property-item\" v-for=\"(item, index) in houseData.property\" :key=\"index\">\n            <text class=\"property-label\">{{item.label}}</text>\n            <text class=\"property-value\">{{item.value}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 房源描述 -->\n      <view class=\"content-card description-card\">\n        <view class=\"section-title\">房源描述</view>\n        <view class=\"description-content\">\n          <text class=\"description-text\">{{houseData.description}}</text>\n        </view>\n      </view>\n      \n      <!-- 业主信息 -->\n      <view class=\"content-card owner-card\">\n        <view class=\"owner-header\">\n          <view class=\"owner-avatar\">\n            <image :src=\"houseData.owner.avatar\" mode=\"aspectFill\"></image>\n          </view>\n          <view class=\"owner-info\">\n            <text class=\"owner-name\">{{houseData.owner.name}}</text>\n            <view class=\"owner-meta\">\n              <text class=\"owner-type\">{{houseData.owner.type}}</text>\n              <text class=\"owner-rating\">信用等级 {{houseData.owner.rating}}</text>\n            </view>\n          </view>\n          <view class=\"owner-auth\" v-if=\"houseData.owner.isVerified\">\n            <text class=\"iconfont icon-verified\"></text>\n            <text class=\"auth-text\">已认证</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 联系方式 -->\n      <view class=\"content-card contact-card\">\n        <view class=\"contact-header\">\n          <text class=\"section-title\">联系方式</text>\n        </view>\n        <view class=\"contact-content\">\n          <view class=\"contact-item\">\n            <text class=\"contact-label\">联系人</text>\n            <text class=\"contact-value\">{{houseData.contact.name}}</text>\n          </view>\n          <view class=\"contact-item\">\n            <text class=\"contact-label\">电话</text>\n            <text class=\"contact-value contact-phone\" @click=\"callPhone\">{{houseData.contact.phone}}</text>\n          </view>\n          <view class=\"contact-tips\">\n            <text class=\"tips-icon iconfont icon-info\"></text>\n            <text class=\"tips-text\">请说明在\"磁州生活网\"看到的信息</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 相关推荐模块 -->\n      <view class=\"content-card related-houses-card\">\n        <view class=\"section-title\">相关推荐</view>\n        <view class=\"related-houses-content\">\n          <view class=\"related-houses-list\">\n            <view class=\"related-house-item\" v-for=\"(house, index) in relatedHouses.slice(0, 3)\" :key=\"index\" @click=\"navigateToHouse(house.id)\">\n              <view class=\"house-item-content\">\n                <view class=\"house-item-left\">\n                  <image class=\"house-image\" :src=\"house.image\" mode=\"aspectFill\"></image>\n                </view>\n                <view class=\"house-item-middle\">\n                  <text class=\"house-item-title\">{{house.title}}</text>\n                  <view class=\"house-item-meta\">{{house.type}} · {{house.area}}</view>\n                  <view class=\"house-item-tags\">\n                    <text class=\"house-item-tag\" v-for=\"(tag, tagIndex) in house.tags.slice(0, 2)\" :key=\"tagIndex\">{{tag}}</text>\n                    <text class=\"house-item-tag-more\" v-if=\"house.tags && house.tags.length > 2\">+{{house.tags.length - 2}}</text>\n                  </view>\n                </view>\n                <view class=\"house-item-right\">\n                  <text class=\"house-item-price\">{{house.price}}</text>\n                </view>\n              </view>\n            </view>\n            <view class=\"empty-related-houses\" v-if=\"relatedHouses.length === 0\">\n              <image src=\"/static/images/empty.png\" class=\"empty-image\" mode=\"aspectFit\"></image>\n              <text class=\"empty-text\">暂无相关推荐</text>\n            </view>\n          </view>\n          <view class=\"view-more-btn\" v-if=\"relatedHouses.length > 0\" @click.stop=\"navigateToHouseList\">\n            <text class=\"view-more-text\">查看更多房源</text>\n            <text class=\"view-more-icon iconfont icon-right\"></text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"interaction-toolbar\">\n      <view class=\"toolbar-item\" @click=\"goToHome\">\n        <image src=\"/static/images/tabbar/a首页.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">首页</text>\n      </view>\n      <view class=\"toolbar-item\" @click=\"toggleCollect\">\n        <image src=\"/static/images/tabbar/a收藏.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">收藏</text>\n      </view>\n      <button class=\"share-button toolbar-item\" open-type=\"share\">\n        <image src=\"/static/images/tabbar/a分享.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">分享</text>\n      </button>\n      <view class=\"toolbar-item\" @click=\"openChat\">\n        <image src=\"/static/images/tabbar/a消息.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">私信</text>\n      </view>\n      <view class=\"toolbar-item call-button\" @click=\"callPhone\">\n        <view class=\"call-button-content\">\n          <text class=\"call-text\">打电话</text>\n          <text class=\"call-subtitle\">请说在磁州生活网看到的</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\n\n// 状态栏高度\nconst statusBarHeight = ref(20);\n\n// 获取状态栏高度\nonMounted(() => {\n  try {\n    const sysInfo = uni.getSystemInfoSync();\n    statusBarHeight.value = sysInfo.statusBarHeight || 20;\n  } catch (e) {\n    console.error('获取状态栏高度失败', e);\n  }\n});\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack({\n    fail: () => {\n      uni.switchTab({\n        url: '/pages/index/index'\n      });\n    }\n  });\n};\n\n// 格式化时间\nconst formatTime = (timestamp) => {\n  const date = new Date(timestamp);\n  return `${date.getMonth() + 1}月${date.getDate()}日`;\n};\n\n// 响应式数据\nconst isCollected = ref(false);\nconst houseData = ref({\n  id: 'house12345',\n  title: '精装三室两厅出售',\n  price: '120万',\n  tags: ['精装修', '满五唯一', '学区房'],\n  publishTime: Date.now() - 86400000 * 2, // 2天前\n  images: [\n    '/static/images/house1.jpg',\n    '/static/images/house2.jpg',\n    '/static/images/house3.jpg'\n  ],\n  type: '三室两厅',\n  area: '磁县城区',\n  size: '120平方米',\n  orientation: '南北通透',\n  configs: [\n    { name: '空调', icon: 'icon-ac' },\n    { name: '热水器', icon: 'icon-water-heater' },\n    { name: '洗衣机', icon: 'icon-washer' },\n    { name: '冰箱', icon: 'icon-fridge' },\n    { name: '电视', icon: 'icon-tv' },\n    { name: '宽带', icon: 'icon-wifi' }\n  ],\n  details: [\n    { label: '装修情况', value: '精装修' },\n    { label: '所在楼层', value: '6/18层' },\n    { label: '建筑年代', value: '2018年' },\n    { label: '房屋用途', value: '住宅' }\n  ],\n  location: {\n    address: '磁县城区XX路XX号',\n    latitude: 36.123456,\n    longitude: 114.123456,\n    surroundings: [\n      { name: '地铁站', distance: '500米', icon: 'icon-subway' },\n      { name: '公交站', distance: '200米', icon: 'icon-bus' },\n      { name: '超市', distance: '300米', icon: 'icon-supermarket' },\n      { name: '学校', distance: '800米', icon: 'icon-school' }\n    ]\n  },\n  transaction: [\n    { label: '房屋总价', value: '120万' },\n    { label: '单价', value: '10000元/㎡' },\n    { label: '首付比例', value: '30%' },\n    { label: '月供参考', value: '约5000元' }\n  ],\n  property: [\n    { label: '产权年限', value: '70年' },\n    { label: '产权类型', value: '商品房' },\n    { label: '产权状态', value: '满五唯一' },\n    { label: '抵押情况', value: '无抵押' }\n  ],\n  description: '房屋位于磁县城区核心地段，交通便利，配套齐全。精装修，拎包入住。满五唯一，税费低。周边有地铁、公交、超市、学校等配套设施。',\n  owner: {\n    name: '王先生',\n    avatar: '/static/images/avatar.png',\n    type: '个人',\n    rating: 'A+',\n    isVerified: true\n  },\n  contact: {\n    name: '王先生',\n    phone: '13912345678'\n  }\n});\n\nconst markers = ref([{\n  id: 1,\n  latitude: houseData.value.location.latitude,\n  longitude: houseData.value.location.longitude,\n  title: houseData.value.title\n}]);\n\nconst relatedHouses = ref([\n  {\n    id: 'house001',\n    title: '精装三室两厅',\n    price: '110万',\n    type: '三室两厅',\n    area: '磁县城区',\n    image: '/static/images/house-similar1.jpg',\n    tags: ['精装修', '学区房', '满五唯一']\n  },\n  {\n    id: 'house002',\n    title: '南北通透大三居',\n    price: '128万',\n    type: '三室两厅',\n    area: '磁县城区',\n    image: '/static/images/house-similar2.jpg',\n    tags: ['南北通透', '带车位']\n  },\n  {\n    id: 'house003',\n    title: '高层景观房',\n    price: '135万',\n    type: '三室两厅',\n    area: '磁县城区',\n    image: '/static/images/house-similar3.jpg',\n    tags: ['高层', '景观好']\n  }\n])\n\n// 方法\nconst toggleCollect = () => {\n  isCollected.value = !isCollected.value;\n  if (isCollected.value) {\n    uni.showToast({\n      title: '收藏成功',\n      icon: 'success'\n    });\n  }\n};\n\nconst showShareOptions = () => {\n  uni.showShareMenu({\n    withShareTicket: true,\n    menus: ['shareAppMessage', 'shareTimeline']\n  });\n};\n\nconst callPhone = () => {\n  uni.makePhoneCall({\n    phoneNumber: houseData.value.contact.phone,\n    fail: () => {\n      uni.showToast({\n        title: '拨打电话失败',\n        icon: 'none'\n      });\n    }\n  });\n};\n\nconst navigateToHouse = (id) => {\n  if (id === houseData.value.id) return;\n  uni.navigateTo({ url: `/pages/publish/house-sale-detail?id=${id}` });\n}\nconst navigateToHouseList = (e) => {\n  if (e) e.stopPropagation();\n  const houseCategory = houseData.value.tags?.[0] || '';\n  uni.navigateTo({ \n    url: `/subPackages/service/pages/filter?type=house_sell&title=${encodeURIComponent('房屋出售')}&category=${encodeURIComponent(houseCategory)}&active=house_sell` \n  });\n}\n\n// 跳转到首页\nconst goToHome = () => {\n  uni.switchTab({\n    url: '/pages/index/index'\n  });\n};\n\n// 打开私信聊天\nconst openChat = () => {\n  if (!houseData.value.owner || !houseData.value.owner.id) {\n    uni.showToast({\n      title: '无法获取业主信息',\n      icon: 'none'\n    });\n    return;\n  }\n  \n  // 跳转到聊天页面\n  uni.navigateTo({\n    url: `/pages/chat/index?userId=${houseData.value.owner.id}&username=${encodeURIComponent(houseData.value.owner.name || '业主')}`\n  });\n};\n\n// 生命周期钩子\nonMounted(() => {\n  // 修改页面标题\n  uni.setNavigationBarTitle({\n    title: '房屋详情'\n  });\n});\n</script>\n\n<style>\n.house-sale-container {\n  min-height: 100vh;\n  background-color: #f5f7fa;\n  padding-bottom: 150rpx;\n}\n\n.house-sale-wrapper {\n  padding: 150rpx 20rpx 120rpx; /* 增加顶部内边距，为固定导航栏留出空间 */\n}\n\n.content-card {\n  background-color: #fff;\n  border-radius: 16rpx;\n  margin-bottom: 24rpx;\n  padding: 30rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n  margin-top: 20rpx; /* 添加与标题栏的间隙 */\n}\n\n/* 房屋基本信息卡片 */\n.house-title-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.house-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.house-price {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #ff4d4f;\n}\n\n.house-meta {\n  margin-bottom: 24rpx;\n}\n\n.house-tag-group {\n  display: flex;\n  flex-wrap: wrap;\n  margin-bottom: 12rpx;\n}\n\n.house-tag {\n  font-size: 24rpx;\n  color: #1890ff;\n  background-color: rgba(24, 144, 255, 0.1);\n  padding: 4rpx 16rpx;\n  border-radius: 6rpx;\n  margin-right: 16rpx;\n  margin-bottom: 12rpx;\n}\n\n.house-publish-time {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 轮播图 */\n.house-swiper {\n  height: 400rpx;\n  border-radius: 12rpx;\n  overflow: hidden;\n  margin-bottom: 24rpx;\n}\n\n.house-image {\n  width: 100%;\n  height: 100%;\n}\n\n/* 基本信息 */\n.house-basic-info {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 24rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n}\n\n.info-item {\n  width: 50%;\n  padding: 12rpx 24rpx;\n  box-sizing: border-box;\n}\n\n.info-label {\n  font-size: 26rpx;\n  color: #999;\n  margin-bottom: 8rpx;\n  display: block;\n}\n\n.info-value {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n/* 房屋配置 */\n.config-list {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -12rpx;\n}\n\n.config-item {\n  width: 33.33%;\n  padding: 12rpx;\n  box-sizing: border-box;\n  display: flex;\n  align-items: center;\n}\n\n.config-icon {\n  font-size: 32rpx;\n  color: #1890ff;\n  margin-right: 8rpx;\n}\n\n.config-text {\n  font-size: 26rpx;\n  color: #666;\n}\n\n/* 房屋详情 */\n.detail-list {\n  display: flex;\n  flex-direction: column;\n}\n\n.detail-item {\n  display: flex;\n  padding: 16rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.detail-item:last-child {\n  border-bottom: none;\n}\n\n.detail-label {\n  width: 160rpx;\n  font-size: 28rpx;\n  color: #999;\n}\n\n.detail-value {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333;\n}\n\n/* 位置信息 */\n.location-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.location-address {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.address-icon {\n  font-size: 32rpx;\n  color: #1890ff;\n  margin-right: 8rpx;\n}\n\n.address-text {\n  font-size: 28rpx;\n  color: #333;\n}\n\n.location-map {\n  height: 300rpx;\n  border-radius: 12rpx;\n  overflow: hidden;\n  margin-bottom: 16rpx;\n}\n\n.map {\n  width: 100%;\n  height: 100%;\n}\n\n.location-surroundings {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -8rpx;\n}\n\n.surrounding-item {\n  width: 50%;\n  padding: 8rpx;\n  box-sizing: border-box;\n  display: flex;\n  align-items: center;\n}\n\n.surrounding-icon {\n  font-size: 28rpx;\n  color: #1890ff;\n  margin-right: 8rpx;\n}\n\n.surrounding-text {\n  font-size: 26rpx;\n  color: #666;\n  margin-right: 8rpx;\n}\n\n.surrounding-distance {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 交易信息 */\n.transaction-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.transaction-item {\n  display: flex;\n  padding: 16rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.transaction-item:last-child {\n  border-bottom: none;\n}\n\n.transaction-label {\n  width: 160rpx;\n  font-size: 28rpx;\n  color: #999;\n}\n\n.transaction-value {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333;\n}\n\n/* 产权信息 */\n.property-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.property-item {\n  display: flex;\n  padding: 16rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.property-item:last-child {\n  border-bottom: none;\n}\n\n.property-label {\n  width: 160rpx;\n  font-size: 28rpx;\n  color: #999;\n}\n\n.property-value {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333;\n}\n\n/* 房源描述 */\n.description-content {\n  padding: 24rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n}\n\n.description-text {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.6;\n}\n\n/* 业主信息 */\n.owner-header {\n  display: flex;\n  align-items: center;\n}\n\n.owner-avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  overflow: hidden;\n  margin-right: 20rpx;\n}\n\n.owner-avatar image {\n  width: 100%;\n  height: 100%;\n}\n\n.owner-info {\n  flex: 1;\n}\n\n.owner-name {\n  font-size: 30rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.owner-meta {\n  display: flex;\n  align-items: center;\n}\n\n.owner-type, .owner-rating {\n  font-size: 24rpx;\n  color: #666;\n  margin-right: 16rpx;\n}\n\n/* 联系方式 */\n.contact-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.contact-content {\n  padding: 24rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n}\n\n.contact-item {\n  display: flex;\n  padding: 12rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.contact-item:last-child {\n  border-bottom: none;\n}\n\n.contact-label {\n  width: 160rpx;\n  font-size: 28rpx;\n  color: #999;\n}\n\n.contact-value {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333;\n}\n\n.contact-phone {\n  color: #1890ff;\n}\n\n.contact-tips {\n  display: flex;\n  align-items: center;\n  margin-top: 16rpx;\n}\n\n.tips-icon {\n  font-size: 28rpx;\n  color: #999;\n  margin-right: 8rpx;\n}\n\n.tips-text {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 相关推荐模块 */\n.related-houses-card {\n  margin-top: 24rpx;\n}\n\n.related-houses-content {\n  padding: 24rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n}\n\n.related-houses-list {\n  margin-bottom: 24rpx;\n}\n\n.related-house-item {\n  display: flex;\n  padding: 20rpx;\n  background-color: #fff;\n  border-radius: 12rpx;\n  margin-bottom: 16rpx;\n}\n\n.house-item-content {\n  display: flex;\n  align-items: center;\n}\n\n.house-item-left {\n  width: 200rpx;\n  height: 150rpx;\n  border-radius: 8rpx;\n  overflow: hidden;\n  margin-right: 20rpx;\n}\n\n.house-item-middle {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.house-item-title {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n}\n\n.house-item-meta {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.house-item-tags {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.house-item-tag {\n  font-size: 24rpx;\n  color: #1890ff;\n  background-color: rgba(24, 144, 255, 0.1);\n  padding: 4rpx 16rpx;\n  border-radius: 6rpx;\n  margin-right: 8rpx;\n  margin-bottom: 8rpx;\n}\n\n.house-item-tag-more {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.house-item-right {\n  width: 160rpx;\n  font-size: 28rpx;\n  color: #ff4d4f;\n  font-weight: 500;\n}\n\n.empty-related-houses {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 40rpx;\n  background-color: #fff;\n  border-radius: 12rpx;\n}\n\n.empty-image {\n  width: 120rpx;\n  height: 120rpx;\n  margin-bottom: 16rpx;\n}\n\n.empty-text {\n  font-size: 28rpx;\n  color: #999;\n}\n\n.view-more-btn {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 16rpx;\n  background-color: #fff;\n  border-radius: 12rpx;\n  border: 1rpx solid #f0f0f0;\n}\n\n.view-more-text {\n  font-size: 28rpx;\n  color: #1890ff;\n  margin-right: 8rpx;\n}\n\n.view-more-icon {\n  font-size: 28rpx;\n  color: #1890ff;\n}\n\n/* 底部互动工具栏 */\n.interaction-toolbar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background-color: #fff;\n  padding: 10rpx 10rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-top: 1rpx solid #f0f0f0;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  height: 120rpx;\n  z-index: 100;\n}\n\n.toolbar-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 6rpx 0;\n  margin: 0 4rpx;\n}\n\n.toolbar-icon {\n  width: 44rpx;\n  height: 44rpx;\n  margin-bottom: 6rpx;\n}\n\n.toolbar-text {\n  font-size: 22rpx;\n  color: #666;\n}\n\n.share-button {\n  background: transparent;\n  border: none;\n  margin: 0;\n  padding: 0;\n  line-height: normal;\n  border-radius: 0;\n  flex: 1;\n}\n\n.share-button::after {\n  display: none;\n}\n\n.call-button {\n  flex: 3; /* 占据更多空间，原来是2，现在改为3让按钮更长 */\n  background: linear-gradient(135deg, #0052CC, #0066FF);\n  height: 90rpx;\n  margin: 0 0 0 10rpx;\n  border-radius: 45rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n}\n\n.call-button-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n}\n\n.call-text {\n  color: #fff;\n  font-size: 30rpx;\n  font-weight: bold;\n  line-height: 1.2;\n}\n\n.call-subtitle {\n  color: rgba(255, 255, 255, 0.9);\n  font-size: 20rpx;\n  line-height: 1.2;\n}\n\n/* 隐藏原来的底部操作栏 */\n.action-bar {\n  display: none;\n}\n\n/* 隐藏的分享按钮 */\n.hidden-share-btn {\n  position: absolute;\n  top: -9999rpx;\n  left: -9999rpx;\n  width: 0;\n  height: 0;\n  padding: 0;\n  margin: 0;\n  opacity: 0;\n}\n\n/* 原来的section-title样式 */\n.section-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n  position: relative;\n  padding-left: 20rpx;\n}\n\n.section-title::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 8rpx;\n  height: 32rpx;\n  background-color: #1890ff;\n  border-radius: 4rpx;\n}\n\n/* 自定义导航栏样式 */\n.custom-navbar {\n  background: linear-gradient(135deg, #0066FF, #0052CC);\n  height: 88rpx;\n  padding-top: 44px; /* 状态栏高度 */\n  display: flex;\n  align-items: center;\n  position: fixed; /* 改为固定定位 */\n  top: 0;\n  left: 0;\n  right: 0;\n  padding-bottom: 10rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n  z-index: 100; /* 提高z-index确保在最上层 */\n}\n\n.navbar-left {\n  width: 60px;\n  display: flex;\n  align-items: center;\n}\n\n.back-icon {\n  width: 20px;\n  height: 20px;\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 17px;\n  font-weight: 500;\n  color: #fff;\n}\n\n.navbar-right {\n  width: 60px;\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/publish/house-sale-detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni", "MiniProgramPage"], "mappings": ";;;;;;AAkPA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAG9BC,kBAAAA,UAAU,MAAM;AACd,UAAI;AACF,cAAM,UAAUC,oBAAI;AACpB,wBAAgB,QAAQ,QAAQ,mBAAmB;AAAA,MACpD,SAAQ,GAAG;AACVA,yFAAc,aAAa,CAAC;AAAA,MAC7B;AAAA,IACH,CAAC;AAGD,UAAM,SAAS,MAAM;AACnBA,oBAAAA,MAAI,aAAa;AAAA,QACf,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,KAAK;AAAA,UACb,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,cAAc;AAChC,YAAM,OAAO,IAAI,KAAK,SAAS;AAC/B,aAAO,GAAG,KAAK,aAAa,CAAC,IAAI,KAAK,SAAS;AAAA,IACjD;AAGA,UAAM,cAAcF,cAAAA,IAAI,KAAK;AAC7B,UAAM,YAAYA,cAAAA,IAAI;AAAA,MACpB,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM,CAAC,OAAO,QAAQ,KAAK;AAAA,MAC3B,aAAa,KAAK,IAAK,IAAG,QAAW;AAAA;AAAA,MACrC,QAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,SAAS;AAAA,QACP,EAAE,MAAM,MAAM,MAAM,UAAW;AAAA,QAC/B,EAAE,MAAM,OAAO,MAAM,oBAAqB;AAAA,QAC1C,EAAE,MAAM,OAAO,MAAM,cAAe;AAAA,QACpC,EAAE,MAAM,MAAM,MAAM,cAAe;AAAA,QACnC,EAAE,MAAM,MAAM,MAAM,UAAW;AAAA,QAC/B,EAAE,MAAM,MAAM,MAAM,YAAa;AAAA,MAClC;AAAA,MACD,SAAS;AAAA,QACP,EAAE,OAAO,QAAQ,OAAO,MAAO;AAAA,QAC/B,EAAE,OAAO,QAAQ,OAAO,QAAS;AAAA,QACjC,EAAE,OAAO,QAAQ,OAAO,QAAS;AAAA,QACjC,EAAE,OAAO,QAAQ,OAAO,KAAM;AAAA,MAC/B;AAAA,MACD,UAAU;AAAA,QACR,SAAS;AAAA,QACT,UAAU;AAAA,QACV,WAAW;AAAA,QACX,cAAc;AAAA,UACZ,EAAE,MAAM,OAAO,UAAU,QAAQ,MAAM,cAAe;AAAA,UACtD,EAAE,MAAM,OAAO,UAAU,QAAQ,MAAM,WAAY;AAAA,UACnD,EAAE,MAAM,MAAM,UAAU,QAAQ,MAAM,mBAAoB;AAAA,UAC1D,EAAE,MAAM,MAAM,UAAU,QAAQ,MAAM,cAAe;AAAA,QACtD;AAAA,MACF;AAAA,MACD,aAAa;AAAA,QACX,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,QAChC,EAAE,OAAO,MAAM,OAAO,WAAY;AAAA,QAClC,EAAE,OAAO,QAAQ,OAAO,MAAO;AAAA,QAC/B,EAAE,OAAO,QAAQ,OAAO,SAAU;AAAA,MACnC;AAAA,MACD,UAAU;AAAA,QACR,EAAE,OAAO,QAAQ,OAAO,MAAO;AAAA,QAC/B,EAAE,OAAO,QAAQ,OAAO,MAAO;AAAA,QAC/B,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,QAChC,EAAE,OAAO,QAAQ,OAAO,MAAO;AAAA,MAChC;AAAA,MACD,aAAa;AAAA,MACb,OAAO;AAAA,QACL,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,YAAY;AAAA,MACb;AAAA,MACD,SAAS;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACH,CAAC;AAED,UAAM,UAAUA,cAAG,IAAC,CAAC;AAAA,MACnB,IAAI;AAAA,MACJ,UAAU,UAAU,MAAM,SAAS;AAAA,MACnC,WAAW,UAAU,MAAM,SAAS;AAAA,MACpC,OAAO,UAAU,MAAM;AAAA,IACzB,CAAC,CAAC;AAEF,UAAM,gBAAgBA,cAAAA,IAAI;AAAA,MACxB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM,CAAC,OAAO,OAAO,MAAM;AAAA,MAC5B;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM,CAAC,QAAQ,KAAK;AAAA,MACrB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM,CAAC,MAAM,KAAK;AAAA,MACnB;AAAA,IACH,CAAC;AAGD,UAAM,gBAAgB,MAAM;AAC1B,kBAAY,QAAQ,CAAC,YAAY;AACjC,UAAI,YAAY,OAAO;AACrBE,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AASA,UAAM,YAAY,MAAM;AACtBA,oBAAAA,MAAI,cAAc;AAAA,QAChB,aAAa,UAAU,MAAM,QAAQ;AAAA,QACrC,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,kBAAkB,CAAC,OAAO;AAC9B,UAAI,OAAO,UAAU,MAAM;AAAI;AAC/BA,oBAAG,MAAC,WAAW,EAAE,KAAK,uCAAuC,EAAE,GAAE,CAAE;AAAA,IACrE;AACA,UAAM,sBAAsB,CAAC,MAAM;;AACjC,UAAI;AAAG,UAAE;AACT,YAAM,kBAAgB,eAAU,MAAM,SAAhB,mBAAuB,OAAM;AACnDA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,2DAA2D,mBAAmB,MAAM,CAAC,aAAa,mBAAmB,aAAa,CAAC;AAAA,MAC5I,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrB,UAAI,CAAC,UAAU,MAAM,SAAS,CAAC,UAAU,MAAM,MAAM,IAAI;AACvDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAGDA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4BAA4B,UAAU,MAAM,MAAM,EAAE,aAAa,mBAAmB,UAAU,MAAM,MAAM,QAAQ,IAAI,CAAC;AAAA,MAChI,CAAG;AAAA,IACH;AAGAD,kBAAAA,UAAU,MAAM;AAEdC,oBAAAA,MAAI,sBAAsB;AAAA,QACxB,OAAO;AAAA,MACX,CAAG;AAAA,IACH,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9bD,GAAG,WAAWC,SAAe;"}