"use strict";
const common_vendor = require("../../../../common/vendor.js");
const CustomNavbar = () => "../../components/CustomNavbar.js";
const _sfc_main = {
  components: {
    CustomNavbar
  },
  data() {
    return {
      link: "",
      product: {
        id: 1,
        title: "Apple iPhone 15 Pro Max (A2850) 256GB 原色钛金属",
        image: "/static/images/cashback/product-1.png",
        price: "9999.00",
        cashback: "300.00",
        platform: "京东",
        platformIcon: "/static/images/cashback/platform-jd.png",
        description: "<div><p>iPhone 15 Pro Max采用航空级钛金属材质，搭载A17 Pro芯片，4nm工艺制程，全新USB-C接口，支持USB 3传输速度，最高10Gbps...</p></div>"
      },
      priceList: [
        {
          platform: "京东",
          platformIcon: "/static/images/cashback/platform-jd.png",
          price: "9999.00",
          cashback: "300.00",
          isBest: true,
          bestTag: "返利最高",
          url: "https://item.jd.com/example"
        },
        {
          platform: "天猫",
          platformIcon: "/static/images/cashback/platform-tmall.png",
          price: "9989.00",
          cashback: "280.00",
          isBest: true,
          bestTag: "价格最低",
          url: "https://detail.tmall.com/example"
        },
        {
          platform: "苏宁",
          platformIcon: "/static/images/cashback/platform-suning.png",
          price: "10099.00",
          cashback: "290.00",
          isBest: false,
          bestTag: "",
          url: "https://product.suning.com/example"
        },
        {
          platform: "拼多多",
          platformIcon: "/static/images/cashback/platform-pdd.png",
          price: "10199.00",
          cashback: "250.00",
          isBest: false,
          bestTag: "",
          url: "https://mobile.yangkeduo.com/example"
        },
        {
          platform: "抖音",
          platformIcon: "/static/images/cashback/platform-douyin.png",
          price: "10299.00",
          cashback: "260.00",
          isBest: false,
          bestTag: "",
          url: "https://haohuo.douyin.com/example"
        }
      ],
      sortBy: "price"
      // 默认按价格排序
    };
  },
  computed: {
    sortedPriceList() {
      if (this.sortBy === "price") {
        return [...this.priceList].sort((a, b) => parseFloat(a.price) - parseFloat(b.price));
      } else {
        return [...this.priceList].sort((a, b) => parseFloat(b.cashback) - parseFloat(a.cashback));
      }
    }
  },
  onLoad(options) {
    common_vendor.index.setNavigationBarColor({
      frontColor: "#ffffff",
      backgroundColor: "#9C27B0"
    });
    if (options.link) {
      this.link = decodeURIComponent(options.link);
      this.fetchProductInfo();
    }
    if (options.id) {
      this.fetchProductById(options.id);
    }
  },
  methods: {
    // 获取商品信息
    fetchProductInfo() {
      common_vendor.index.showLoading({
        title: "获取商品信息..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("log", "at subPackages/cashback/pages/product-detail/index.vue:199", "获取商品信息:", this.link);
      }, 1e3);
    },
    // 根据ID获取商品
    fetchProductById(id) {
      common_vendor.index.showLoading({
        title: "获取商品信息..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("log", "at subPackages/cashback/pages/product-detail/index.vue:215", "获取商品信息:", id);
      }, 1e3);
    },
    // 排序价格列表
    sortPrices(type) {
      this.sortBy = type;
    },
    // 跳转到商店
    navigateToStore(item) {
      common_vendor.index.showModal({
        title: "跳转提示",
        content: `即将跳转到${item.platform}购买，确认继续吗？`,
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: `正在跳转到${item.platform}`,
              icon: "none"
            });
          }
        }
      });
    }
  }
};
if (!Array) {
  const _component_custom_navbar = common_vendor.resolveComponent("custom-navbar");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_custom_navbar + _component_path + _component_svg)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.p({
      title: "商品详情",
      ["show-back"]: true
    }),
    b: $data.product.image,
    c: common_vendor.t($data.product.title),
    d: common_vendor.t($data.product.price),
    e: common_vendor.t($data.product.cashback),
    f: $data.product.platformIcon,
    g: common_vendor.t($data.product.platform),
    h: $data.sortBy === "price" ? 1 : "",
    i: common_vendor.o(($event) => $options.sortPrices("price")),
    j: $data.sortBy === "cashback" ? 1 : "",
    k: common_vendor.o(($event) => $options.sortPrices("cashback")),
    l: common_vendor.f($options.sortedPriceList, (item, index, i0) => {
      return common_vendor.e({
        a: item.platformIcon,
        b: common_vendor.t(item.platform),
        c: item.isBest
      }, item.isBest ? {
        d: common_vendor.t(item.bestTag)
      } : {}, {
        e: common_vendor.t(item.price),
        f: common_vendor.t(item.cashback),
        g: "ffbebc8a-2-" + i0 + "," + ("ffbebc8a-1-" + i0),
        h: "ffbebc8a-1-" + i0,
        i: index,
        j: item.isBest ? 1 : "",
        k: common_vendor.o(($event) => $options.navigateToStore(item), index)
      });
    }),
    m: common_vendor.p({
      fill: "#FFFFFF",
      d: "M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"
    }),
    n: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "16",
      height: "16"
    }),
    o: $data.product.description
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-ffbebc8a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/cashback/pages/product-detail/index.js.map
