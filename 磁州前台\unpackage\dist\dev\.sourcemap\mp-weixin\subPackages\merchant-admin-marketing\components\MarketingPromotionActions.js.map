{"version": 3, "file": "MarketingPromotionActions.js", "sources": ["subPackages/merchant-admin-marketing/components/MarketingPromotionActions.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7Avc3ViUGFja2FnZXMvbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nL2NvbXBvbmVudHMvTWFya2V0aW5nUHJvbW90aW9uQWN0aW9ucy52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"marketing-promotion-container\">\r\n    <!-- 发布区域 -->\r\n    <view class=\"promotion-section\" v-if=\"showPublish\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">{{ publishModeOnly ? '选择发布方式' : '活动发布' }}</text>\r\n        <text class=\"section-desc\">{{ publishModeOnly ? '' : '发布活动到客户端，提升曝光率' }}</text>\r\n      </view>\r\n      \r\n      <!-- 发布方式选择卡片 - 新样式 -->\r\n      <view class=\"publish-options-card\" v-if=\"publishModeOnly\">\r\n        <!-- 看广告发布 -->\r\n        <view class=\"publish-option-row\">\r\n          <view class=\"publish-option-left\">\r\n            <view class=\"publish-option-icon ad-icon\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n                <path d=\"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z\" />\r\n                <polyline points=\"7.5 4.21 12 6.81 16.5 4.21\" />\r\n                <polyline points=\"7.5 19.79 7.5 14.6 3 12\" />\r\n                <polyline points=\"21 12 16.5 14.6 16.5 19.79\" />\r\n                <polyline points=\"3.27 6.96 12 12.01 20.73 6.96\" />\r\n                <line x1=\"12\" y1=\"22.08\" x2=\"12\" y2=\"12\" />\r\n              </svg>\r\n            </view>\r\n            <view class=\"publish-option-content\">\r\n              <text class=\"publish-option-title\">看广告发布</text>\r\n              <text class=\"publish-option-desc\">看一个广告免费发布一天</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"publish-option-right\">\r\n            <button class=\"publish-btn free-btn\" @click=\"selectDirectOption('publish', 'ad')\">免费</button>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 付费发布 -->\r\n        <view class=\"publish-option-row\">\r\n          <view class=\"publish-option-left\">\r\n            <view class=\"publish-option-icon paid-icon\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n                <rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\" />\r\n                <path d=\"M12 8v8M8 12h8\" />\r\n              </svg>\r\n            </view>\r\n            <view class=\"publish-option-content\">\r\n              <text class=\"publish-option-title\">付费发布</text>\r\n              <text class=\"publish-option-desc\">3天/1周/1个月任选</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"publish-option-right\">\r\n            <button class=\"publish-btn paid-btn\" @click=\"selectDirectOption('publish', 'paid')\">付费</button>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 发布时长选择 -->\r\n        <view class=\"publish-duration-select\" v-if=\"showDurationSelect\">\r\n          <text class=\"duration-title\">选择发布时长</text>\r\n          <view class=\"duration-options\">\r\n            <view \r\n              class=\"duration-option\" \r\n              :class=\"{ active: selectedDuration === '3d' }\"\r\n              @click=\"selectDuration('3d')\"\r\n            >\r\n              <text class=\"duration-text\">3天</text>\r\n              <text class=\"duration-price\">+2.8</text>\r\n            </view>\r\n            <view \r\n              class=\"duration-option\" \r\n              :class=\"{ active: selectedDuration === '1w' }\"\r\n              @click=\"selectDuration('1w')\"\r\n            >\r\n              <text class=\"duration-text\">1周</text>\r\n              <text class=\"duration-price\">+5.8</text>\r\n            </view>\r\n            <view \r\n              class=\"duration-option\" \r\n              :class=\"{ active: selectedDuration === '1m' }\"\r\n              @click=\"selectDuration('1m')\"\r\n            >\r\n              <text class=\"duration-text\">1个月</text>\r\n              <text class=\"duration-price\">+13.8</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 原有的发布选项 -->\r\n      <view class=\"promotion-options\" v-else>\r\n        <!-- 看广告发布选项 -->\r\n        <view class=\"promotion-option\" @click=\"selectDirectOption('publish', 'ad')\">\r\n          <view class=\"option-icon-container normal-icon\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n              <path d=\"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z\" />\r\n              <polyline points=\"7.5 4.21 12 6.81 16.5 4.21\" />\r\n              <polyline points=\"7.5 19.79 7.5 14.6 3 12\" />\r\n              <polyline points=\"21 12 16.5 14.6 16.5 19.79\" />\r\n              <polyline points=\"3.27 6.96 12 12.01 20.73 6.96\" />\r\n              <line x1=\"12\" y1=\"22.08\" x2=\"12\" y2=\"12\" />\r\n            </svg>\r\n          </view>\r\n          <view class=\"option-content\">\r\n            <text class=\"option-title\">看广告发布</text>\r\n            <text class=\"option-desc\">观看一个广告免费发布活动</text>\r\n          </view>\r\n          <view class=\"option-tag free-tag\">\r\n            <text>广告</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 精选发布选项 -->\r\n        <view class=\"promotion-option\" @click=\"selectDirectOption('publish', 'featured')\">\r\n          <view class=\"option-icon-container featured-icon\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n              <polygon points=\"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2\" />\r\n            </svg>\r\n          </view>\r\n          <view class=\"option-content\">\r\n            <text class=\"option-title\">精选发布</text>\r\n            <text class=\"option-desc\">在首页精选区域展示，提高曝光</text>\r\n          </view>\r\n          <view class=\"option-tag paid-tag\">\r\n            <text>付费</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 置顶区域 -->\r\n    <view class=\"promotion-section\" v-if=\"showTop\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">活动置顶</text>\r\n        <text class=\"section-desc\">置顶活动到列表顶部，获得优先展示</text>\r\n      </view>\r\n      \r\n      <view class=\"promotion-options\">\r\n        <!-- 看广告置顶选项 -->\r\n        <view class=\"promotion-option\" @click=\"selectDirectOption('top', 'ad')\">\r\n          <view class=\"option-icon-container normal-top-icon\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n              <path d=\"M12 19V5M5 12l7-7 7 7\" />\r\n              <rect x=\"3\" y=\"3\" width=\"18\" height=\"4\" rx=\"1\" />\r\n            </svg>\r\n          </view>\r\n          <view class=\"option-content\">\r\n            <text class=\"option-title\">看广告置顶</text>\r\n            <text class=\"option-desc\">观看广告获得2小时置顶展示</text>\r\n          </view>\r\n          <view class=\"option-tag free-tag\">\r\n            <text>广告</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 普通置顶选项 -->\r\n        <view class=\"promotion-option\" @click=\"selectDirectOption('top', 'normal')\">\r\n          <view class=\"option-icon-container normal-top-icon\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n              <path d=\"M12 19V5M5 12l7-7 7 7\" />\r\n            </svg>\r\n          </view>\r\n          <view class=\"option-content\">\r\n            <text class=\"option-title\">普通置顶</text>\r\n            <text class=\"option-desc\">活动列表置顶展示3天</text>\r\n          </view>\r\n          <view class=\"option-tag paid-tag\">\r\n            <text>¥9.9</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 高级置顶选项 -->\r\n        <view class=\"promotion-option\" @click=\"selectDirectOption('top', 'premium')\">\r\n          <view class=\"option-icon-container premium-top-icon\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n              <path d=\"M12 19V5M5 12l7-7 7 7\" />\r\n              <circle cx=\"12\" cy=\"5\" r=\"2\" />\r\n            </svg>\r\n          </view>\r\n          <view class=\"option-content\">\r\n            <text class=\"option-title\">高级置顶</text>\r\n            <text class=\"option-desc\">活动列表置顶展示7天，首页推荐</text>\r\n          </view>\r\n          <view class=\"option-tag paid-tag\">\r\n            <text>¥19.9</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 刷新区域 -->\r\n    <view class=\"promotion-section\" v-if=\"showRefresh\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">活动刷新</text>\r\n        <text class=\"section-desc\">刷新活动展示时间，提升排名</text>\r\n      </view>\r\n      \r\n      <view class=\"promotion-options\">\r\n        <!-- 看广告刷新选项 -->\r\n        <view class=\"promotion-option\" @click=\"selectDirectOption('refresh', 'ad')\">\r\n          <view class=\"option-icon-container refresh-icon\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n              <path d=\"M23 4v6h-6M1 20v-6h6\" />\r\n              <path d=\"M3.51 9a9 9 0 0114.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0020.49 15\" />\r\n              <rect x=\"10\" y=\"1\" width=\"4\" height=\"4\" rx=\"1\" />\r\n            </svg>\r\n          </view>\r\n          <view class=\"option-content\">\r\n            <text class=\"option-title\">看广告刷新</text>\r\n            <text class=\"option-desc\">观看广告免费刷新一次活动</text>\r\n          </view>\r\n          <view class=\"option-tag free-tag\">\r\n            <text>广告</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 单次刷新选项 -->\r\n        <view class=\"promotion-option\" @click=\"handleRefreshClick\">\r\n          <view class=\"option-icon-container refresh-icon\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n              <path d=\"M23 4v6h-6M1 20v-6h6\" />\r\n              <path d=\"M3.51 9a9 9 0 0114.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0020.49 15\" />\r\n            </svg>\r\n          </view>\r\n          <view class=\"option-content\">\r\n            <text class=\"option-title\">单次刷新</text>\r\n            <text class=\"option-desc\" v-if=\"remainingRefreshCount > 0\">剩余刷新次数: {{ remainingRefreshCount }}次</text>\r\n            <text class=\"option-desc\" v-else>刷新一次活动展示时间</text>\r\n          </view>\r\n          <view class=\"option-tag paid-tag\">\r\n            <text>¥2.9</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 刷新套餐选项 -->\r\n        <view class=\"promotion-option\" @click=\"selectDirectOption('refresh', 'package')\">\r\n          <view class=\"option-icon-container package-icon\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n              <rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\" />\r\n              <path d=\"M3 9h18M9 21V9\" />\r\n            </svg>\r\n          </view>\r\n          <view class=\"option-content\">\r\n            <text class=\"option-title\">刷新套餐</text>\r\n            <text class=\"option-desc\">10次刷新，有效期30天</text>\r\n          </view>\r\n          <view class=\"option-tag paid-tag\">\r\n            <text>¥19.9</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 支付确认弹窗 -->\r\n    <view class=\"payment-modal\" v-if=\"showPaymentModal\">\r\n      <view class=\"modal-overlay\" @click=\"closePaymentModal\"></view>\r\n      <view class=\"modal-container\">\r\n        <view class=\"modal-header\">\r\n          <text class=\"modal-title\">{{ paymentModalTitle }}</text>\r\n          <view class=\"modal-close\" @click=\"closePaymentModal\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n              <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\" />\r\n              <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\" />\r\n            </svg>\r\n          </view>\r\n        </view>\r\n        <view class=\"modal-content\">\r\n          <view class=\"payment-info\">\r\n            <text class=\"payment-desc\">{{ paymentModalDesc }}</text>\r\n            <text class=\"payment-price\">{{ paymentModalPrice }}</text>\r\n          </view>\r\n          <view class=\"payment-methods\">\r\n            <text class=\"methods-title\">支付方式</text>\r\n            <view class=\"method-options\">\r\n              <view \r\n                class=\"method-option\" \r\n                :class=\"{ active: paymentMethod === 'wxpay' }\"\r\n                @click=\"paymentMethod = 'wxpay'\"\r\n              >\r\n                <view class=\"method-icon wxpay-icon\">\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n                    <path d=\"M9 8.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3z\" />\r\n                    <path d=\"M15 8.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3z\" />\r\n                    <path d=\"M9 15.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3z\" />\r\n                    <path d=\"M15 15.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3z\" />\r\n                    <rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" />\r\n                  </svg>\r\n                </view>\r\n                <text class=\"method-name\">微信支付</text>\r\n                <view class=\"method-check\">\r\n                  <view class=\"check-inner\" v-if=\"paymentMethod === 'wxpay'\"></view>\r\n                </view>\r\n              </view>\r\n              <view \r\n                class=\"method-option\" \r\n                :class=\"{ active: paymentMethod === 'alipay' }\"\r\n                @click=\"paymentMethod = 'alipay'\"\r\n              >\r\n                <view class=\"method-icon alipay-icon\">\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n                    <path d=\"M22 9.3V5H2v4.3L12 15l10-5.7z\" />\r\n                    <path d=\"M2 9.3V19h20V9.3\" />\r\n                    <path d=\"M12 15v4\" />\r\n                  </svg>\r\n                </view>\r\n                <text class=\"method-name\">支付宝</text>\r\n                <view class=\"method-check\">\r\n                  <view class=\"check-inner\" v-if=\"paymentMethod === 'alipay'\"></view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <view class=\"modal-footer\">\r\n          <button class=\"btn-cancel\" @click=\"closePaymentModal\">取消</button>\r\n          <button class=\"btn-confirm\" @click=\"confirmPayment\">确认支付</button>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 刷新次数用完后的付费弹窗 -->\r\n    <view class=\"refresh-payment-modal\" v-if=\"showRefreshPaymentModal\">\r\n      <view class=\"refresh-payment-overlay\" @click=\"closeRefreshPaymentModal\"></view>\r\n      <view class=\"refresh-payment-content modern-design\">\r\n        <!-- 卡片顶部 -->\r\n        <view class=\"refresh-header-gradient\">\r\n          <view class=\"refresh-badge\">\r\n            <svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n              <path fill=\"currentColor\" d=\"M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z\" />\r\n            </svg>\r\n            <text>活动刷新</text>\r\n          </view>\r\n          <text class=\"refresh-title\">提升活动曝光力</text>\r\n          <text class=\"refresh-subtitle\">获得更多精准客户</text>\r\n        </view>\r\n        \r\n        <!-- 价格区域 -->\r\n        <view class=\"refresh-price-area\">\r\n          <view class=\"price-tag\">\r\n            <text class=\"price-symbol\">¥</text>\r\n            <text class=\"price-value\">{{ refreshPrice }}</text>\r\n          </view>\r\n          <view class=\"price-label\">单次刷新</view>\r\n        </view>\r\n        \r\n        <!-- 提示信息区 -->\r\n        <view class=\"refresh-info-box\">\r\n          <view class=\"info-highlight\">刷新后置顶信息排名立刻升到置顶第一位</view>\r\n          <view class=\"info-detail\">未置顶的会升到未置顶第一位</view>\r\n        </view>\r\n        \r\n        <!-- 套餐提示 -->\r\n        <view class=\"refresh-package-hint\">\r\n          <view class=\"package-hint-left\">\r\n            <text>购买刷新套餐</text>\r\n          </view>\r\n          <view class=\"package-hint-right\">\r\n            <text>最高可省</text>\r\n            <text class=\"highlight-discount\">70%</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 按钮区 -->\r\n        <view class=\"refresh-action-buttons\">\r\n          <button class=\"btn-check-package\" @click=\"buyRefreshPackage\">查看套餐</button>\r\n          <button class=\"btn-refresh-now\" @click=\"confirmPaidRefresh\">立即刷新</button>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 刷新套餐选择弹窗 -->\r\n    <view class=\"refresh-packages-modal\" v-if=\"showRefreshOptions\">\r\n      <view class=\"refresh-packages-overlay\" @click=\"closeRefreshOptions\"></view>\r\n      <view class=\"refresh-packages-content\">\r\n        <view class=\"refresh-packages-header\">\r\n          <text class=\"refresh-packages-title\">\r\n            <view class=\"refresh-icon-wrapper\">\r\n              <svg class=\"refresh-svg-icon\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\">\r\n                <path fill=\"#0066FF\" d=\"M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z\" />\r\n              </svg>\r\n            </view>\r\n            刷新套餐\r\n          </text>\r\n        </view>\r\n        <view class=\"refresh-packages-body\">\r\n          <text class=\"refresh-packages-desc\">您当前有{{ remainingRefreshCount }}次刷新次数</text>\r\n          <view class=\"refresh-packages-list\">\r\n            <view \r\n              class=\"refresh-package-item\" \r\n              v-for=\"(pkg, index) in refreshPackages\" \r\n              :key=\"index\"\r\n              @click=\"selectRefreshPackage(pkg)\"\r\n            >\r\n              <text class=\"refresh-package-count\">{{ pkg.count }}次</text>\r\n              <text class=\"refresh-package-price\">¥{{ pkg.price }}</text>\r\n              <text class=\"refresh-package-discount\" v-if=\"pkg.count === 10\">省50%</text>\r\n              <text class=\"refresh-package-discount\" v-if=\"pkg.count === 30\">省60%</text>\r\n              <text class=\"refresh-package-discount\" v-if=\"pkg.count === 100\">省70%</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <view class=\"refresh-packages-actions\">\r\n          <button class=\"refresh-packages-confirm-btn\" @click=\"useRefreshCount\" v-if=\"remainingRefreshCount > 0\">使用刷新次数</button>\r\n          <button class=\"refresh-packages-cancel-btn\" @click=\"closeRefreshOptions\">取消</button>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, reactive } from 'vue';\r\n\r\n// 组件属性\r\nconst props = defineProps({\r\n  // 活动ID\r\n  activityId: {\r\n    type: String,\r\n    default: ''\r\n  },\r\n  // 活动类型：coupon, discount, flash, group\r\n  activityType: {\r\n    type: String,\r\n    default: 'coupon',\r\n    validator: (value) => {\r\n      return ['coupon', 'discount', 'flash', 'group'].includes(value);\r\n    }\r\n  },\r\n  // 控制显示哪些功能\r\n  showActions: {\r\n    type: Array,\r\n    default: () => ['publish', 'top', 'refresh'],\r\n    validator: (value) => {\r\n      return value.every(item => ['publish', 'top', 'refresh'].includes(item));\r\n    }\r\n  },\r\n  // 是否只显示发布模式（新的UI样式）\r\n  publishModeOnly: {\r\n    type: Boolean,\r\n    default: false\r\n  },\r\n  // 显示模式：standard(标准模式) 或 direct(直接显示选项)\r\n  showMode: {\r\n    type: String,\r\n    default: 'direct',\r\n    validator: (value) => {\r\n      return ['standard', 'direct'].includes(value);\r\n    }\r\n  },\r\n  // 刷新次数\r\n  refreshCount: {\r\n    type: Number,\r\n    default: 0\r\n  }\r\n});\r\n\r\n// 组件事件\r\nconst emit = defineEmits(['action-completed']);\r\n\r\n// 响应式状态\r\nconst isPanelExpanded = ref(false);\r\nconst showPaymentModal = ref(false);\r\nconst paymentModalTitle = ref('');\r\nconst paymentModalDesc = ref('');\r\nconst paymentModalPrice = ref('');\r\nconst paymentMethod = ref('wxpay');\r\nconst currentAction = ref('');\r\nconst currentActionType = ref('');\r\nconst showDurationSelect = ref(false);\r\nconst selectedDuration = ref('3d');\r\nconst showRefreshOptions = ref(false); // 是否显示刷新选项\r\nconst showRefreshPaymentModal = ref(false); // 是否显示刷新付费弹窗\r\nconst selectedOption = ref(null);\r\nconst selectedPrice = ref(null);\r\n\r\n// 刷新次数\r\nconst remainingRefreshCount = ref(props.refreshCount);\r\n\r\n// 时长选项\r\nconst durationOptions = [\r\n  { label: '3天', price: '2.8' },\r\n  { label: '1周', price: '5.8' },\r\n  { label: '1个月', price: '13.8' }\r\n];\r\n\r\n// 刷新价格\r\nconst refreshPrice = ref('2');\r\n\r\n// 刷新套餐选项\r\nconst refreshPackages = [\r\n  { count: 10, price: '15', label: '10次刷新套餐' },\r\n  { count: 30, price: '39', label: '30次刷新套餐' },\r\n  { count: 100, price: '99', label: '100次刷新套餐' }\r\n];\r\n\r\n// 计算属性\r\nconst showPublish = computed(() => {\r\n  return props.showActions.includes('publish');\r\n});\r\n\r\nconst showTop = computed(() => {\r\n  return !props.publishModeOnly && props.showActions.includes('top');\r\n});\r\n\r\nconst showRefresh = computed(() => {\r\n  return !props.publishModeOnly && props.showActions.includes('refresh');\r\n});\r\n\r\n// 判断是否为拼车系统发布页面\r\nconst isCarpool = computed(() => {\r\n  // 检查当前页面路径是否包含carpool\r\n  const pages = getCurrentPages();\r\n  const currentPage = pages[pages.length - 1];\r\n  const route = currentPage ? currentPage.route || '' : '';\r\n  return route.includes('carpool');\r\n});\r\n\r\n// 模态框配置\r\nconst modalConfig = reactive({\r\n  title: '',\r\n  description: '',\r\n  options: []\r\n});\r\n\r\n// 切换面板展开/折叠\r\nconst togglePanel = () => {\r\n  isPanelExpanded.value = !isPanelExpanded.value;\r\n};\r\n\r\n// 处理操作\r\nconst handleAction = (action, type) => {\r\n  currentAction.value = action;\r\n  currentActionType.value = type;\r\n  \r\n  // 根据不同操作类型配置支付弹窗\r\n  switch(action) {\r\n    case 'publish':\r\n      if (type === 'ad') {\r\n        // 看广告发布，显示广告\r\n        showAdForAction('publish');\r\n      } else if (type === 'paid') {\r\n        // 付费发布，显示时长选择\r\n        showDurationSelect.value = true;\r\n      } else if (type === 'featured') {\r\n        // 精选发布，显示支付弹窗\r\n        showPaymentDialog('精选发布', '在首页精选区域展示，提高曝光', '¥29.9');\r\n      }\r\n      break;\r\n    case 'top':\r\n      if (type === 'ad') {\r\n        // 看广告置顶，显示广告\r\n        showAdForAction('top');\r\n      } else if (type === 'normal') {\r\n        showPaymentDialog('普通置顶', '活动列表置顶展示3天', '¥9.9');\r\n      } else if (type === 'premium') {\r\n        showPaymentDialog('高级置顶', '活动列表置顶展示7天，首页推荐', '¥19.9');\r\n      }\r\n      break;\r\n    case 'refresh':\r\n      if (type === 'ad') {\r\n        // 看广告刷新，显示广告\r\n        showAdForAction('refresh');\r\n      } else if (type === 'single') {\r\n        showPaymentDialog('单次刷新', '刷新一次活动展示时间', '¥2.9');\r\n      } else if (type === 'package') {\r\n        showPaymentDialog('刷新套餐', '10次刷新，有效期30天', '¥19.9');\r\n      }\r\n      break;\r\n  }\r\n};\r\n\r\n// 直接选择选项（用于直接显示模式）\r\nconst selectDirectOption = (action, optionType) => {\r\n  currentAction.value = action;\r\n  \r\n  // 如果是付费选项，显示时长选择\r\n  if (optionType === 'paid') {\r\n    // 拼车系统直接处理付费发布，无需选择时长\r\n    if (isCarpool.value && action === 'publish') {\r\n      const option = {\r\n        title: '付费发布',\r\n        subtitle: '付费1元发布一条信息',\r\n        price: '¥1.00',\r\n        icon: '/static/images/premium/paid-publish.png',\r\n        type: 'paid',\r\n        duration: '一条信息',\r\n        amount: '1'\r\n      };\r\n      selectedOption.value = option;\r\n      processPaidAction();\r\n      return;\r\n    }\r\n    \r\n    showDurationSelect.value = true;\r\n    return;\r\n  }\r\n  \r\n  // 创建选项对象\r\n  let option = null;\r\n  \r\n  if (action === 'publish') {\r\n    if (optionType === 'ad') {\r\n      if (isCarpool.value) {\r\n        option = {\r\n          title: '免费发布',\r\n          subtitle: '观看15秒广告发布一条信息',\r\n          price: '免费',\r\n          icon: '/static/images/premium/ad-publish.png',\r\n          type: 'ad',\r\n          duration: '一条信息'\r\n        };\r\n      } else {\r\n      option = {\r\n        title: '免费发布',\r\n        subtitle: '观看15秒广告后发布',\r\n        price: '免费',\r\n        icon: '/static/images/premium/ad-publish.png',\r\n        type: 'ad',\r\n        duration: '1天'\r\n      };\r\n      }\r\n    }\r\n  } else if (action === 'top') {\r\n    if (optionType === 'ad') {\r\n      option = {\r\n        title: '广告置顶',\r\n        subtitle: '观看30秒广告获得2小时置顶',\r\n        price: '免费',\r\n        icon: '/static/images/premium/ad-top.png',\r\n        type: 'ad',\r\n        duration: '2小时'\r\n      };\r\n    }\r\n  } else if (action === 'refresh') {\r\n    if (optionType === 'ad') {\r\n      option = {\r\n        title: '广告刷新',\r\n        subtitle: '观看15秒广告刷新一次',\r\n        price: '免费',\r\n        icon: '/static/images/premium/ad-refresh.png',\r\n        type: 'ad'\r\n      };\r\n    }\r\n  }\r\n  \r\n  if (option) {\r\n    selectedOption.value = option;\r\n    \r\n    // 直接处理选择的选项\r\n    if (optionType === 'ad') {\r\n      showAd();\r\n    }\r\n  }\r\n};\r\n\r\n// 选择发布时长\r\nconst selectDuration = (duration, price) => {\r\n  selectedDuration.value = duration;\r\n  selectedPrice.value = price;\r\n  \r\n  let durationText = '';\r\n  if (duration === '3d') {\r\n    durationText = '3天';\r\n    price = '2.8';\r\n  } else if (duration === '1w') {\r\n    durationText = '1周';\r\n    price = '5.8';\r\n  } else if (duration === '1m') {\r\n    durationText = '1个月';\r\n    price = '13.8';\r\n  }\r\n  \r\n  // 创建付费选项\r\n  const option = {\r\n    title: currentAction.value === 'publish' ? '付费发布' : \r\n           currentAction.value === 'top' ? '付费置顶' : '付费刷新',\r\n    subtitle: `${currentAction.value === 'publish' ? '付费发布' : \r\n               currentAction.value === 'top' ? '付费置顶' : '付费刷新'}${durationText}`,\r\n    price: `¥${price}`,\r\n    icon: currentAction.value === 'publish' ? '/static/images/premium/paid-publish.png' : \r\n          currentAction.value === 'top' ? '/static/images/premium/paid-top.png' : \r\n          '/static/images/premium/paid-refresh.png',\r\n    type: 'paid',\r\n    duration: durationText,\r\n    amount: price\r\n  };\r\n  \r\n  selectedOption.value = option;\r\n  processPaidAction();\r\n  \r\n  // 显示支付弹窗\r\n  showPaymentDialog('付费发布', `发布${durationText}`, `¥${price}`);\r\n  showDurationSelect.value = false;\r\n};\r\n\r\n// 显示支付弹窗\r\nconst showPaymentDialog = (title, desc, price) => {\r\n  paymentModalTitle.value = title;\r\n  paymentModalDesc.value = desc;\r\n  paymentModalPrice.value = price;\r\n  showPaymentModal.value = true;\r\n};\r\n\r\n// 关闭支付弹窗\r\nconst closePaymentModal = () => {\r\n  showPaymentModal.value = false;\r\n};\r\n\r\n// 确认支付\r\nconst confirmPayment = () => {\r\n  // 显示加载提示\r\n  uni.showLoading({\r\n    title: '处理支付中...'\r\n  });\r\n  \r\n  // 模拟支付过程\r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    \r\n    // 关闭支付弹窗\r\n    closePaymentModal();\r\n    \r\n    // 根据不同操作类型处理结果\r\n    switch(currentAction.value) {\r\n      case 'publish':\r\n        processPublish();\r\n        break;\r\n      case 'top':\r\n        processTop();\r\n        break;\r\n      case 'refresh':\r\n        processRefresh();\r\n        break;\r\n    }\r\n  }, 1500);\r\n};\r\n\r\n// 显示广告进行操作\r\nconst showAdForAction = (action) => {\r\n  let actionText = '';\r\n  switch(action) {\r\n    case 'publish':\r\n      actionText = '发布';\r\n      break;\r\n    case 'top':\r\n      actionText = '置顶';\r\n      break;\r\n    case 'refresh':\r\n      actionText = '刷新';\r\n      break;\r\n  }\r\n  \r\n  uni.showLoading({\r\n    title: '准备广告中...'\r\n  });\r\n  \r\n  // 模拟广告加载\r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    \r\n    // 显示广告观看提示\r\n    uni.showModal({\r\n      title: '广告观看',\r\n      content: `观看一个广告即可免费${actionText}活动`,\r\n      confirmText: '观看广告',\r\n      cancelText: '取消',\r\n      success: (res) => {\r\n        if (res.confirm) {\r\n          // 用户确认观看广告\r\n          playAdvertisementForAction(action);\r\n        }\r\n      }\r\n    });\r\n  }, 1000);\r\n};\r\n\r\n// 显示广告\r\nconst showAd = () => {\r\n  // 模拟广告显示\r\n  uni.showLoading({\r\n    title: '正在加载广告...'\r\n  });\r\n  \r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    \r\n    // 模拟广告播放完成\r\n    uni.showModal({\r\n      title: '广告观看完成',\r\n      content: '您已成功观看广告，即将完成操作',\r\n      showCancel: false,\r\n      success: () => {\r\n        processActionCompletion();\r\n      }\r\n    });\r\n  }, 1500);\r\n};\r\n\r\n// 播放广告\r\nconst playAdvertisementForAction = (action) => {\r\n  uni.showLoading({\r\n    title: '加载广告中...'\r\n  });\r\n  \r\n  // 模拟广告播放\r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    \r\n    // 显示广告播放中\r\n    uni.showToast({\r\n      title: '广告播放中...',\r\n      icon: 'none',\r\n      duration: 3000\r\n    });\r\n    \r\n    // 模拟广告播放完成\r\n    setTimeout(() => {\r\n      // 广告播放完成，处理相应操作\r\n      switch(action) {\r\n        case 'publish':\r\n          processPublish();\r\n          break;\r\n        case 'top':\r\n          processTop();\r\n          break;\r\n        case 'refresh':\r\n          processRefresh();\r\n          break;\r\n      }\r\n      \r\n      // 显示成功提示\r\n      uni.showToast({\r\n        title: `广告观看完成，${action === 'publish' ? '发布' : action === 'top' ? '置顶' : '刷新'}成功`,\r\n        icon: 'success',\r\n        duration: 2000\r\n      });\r\n    }, 3000);\r\n  }, 1000);\r\n};\r\n\r\n// 处理付费操作\r\nconst processPaidAction = () => {\r\n  // 关闭时长选择面板\r\n  showDurationSelect.value = false;\r\n  \r\n  // 模拟支付流程\r\n  uni.showLoading({\r\n    title: '正在处理支付...'\r\n  });\r\n  \r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    \r\n    // 模拟支付成功\r\n    uni.showModal({\r\n      title: '支付成功',\r\n      content: '您已成功支付，即将完成操作',\r\n      showCancel: false,\r\n      success: () => {\r\n        processActionCompletion();\r\n      }\r\n    });\r\n  }, 1500);\r\n};\r\n\r\n// 处理发布操作\r\nconst processPublish = () => {\r\n  uni.showLoading({\r\n    title: '发布中...'\r\n  });\r\n  \r\n  // 模拟API调用\r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    \r\n    // 显示成功提示\r\n    uni.showToast({\r\n      title: currentActionType.value === 'normal' ? '发布成功' : '精选发布成功',\r\n      icon: 'success'\r\n    });\r\n    \r\n    // 触发事件\r\n    emit('action-completed', {\r\n      action: 'publish',\r\n      type: currentActionType.value,\r\n      activityId: props.activityId,\r\n      activityType: props.activityType\r\n    });\r\n  }, 1000);\r\n};\r\n\r\n// 处理置顶操作\r\nconst processTop = () => {\r\n  uni.showLoading({\r\n    title: '置顶中...'\r\n  });\r\n  \r\n  // 模拟API调用\r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    \r\n    // 显示成功提示\r\n    uni.showToast({\r\n      title: currentActionType.value === 'normal' ? '普通置顶成功' : '高级置顶成功',\r\n      icon: 'success'\r\n    });\r\n    \r\n    // 触发事件\r\n    emit('action-completed', {\r\n      action: 'top',\r\n      type: currentActionType.value,\r\n      activityId: props.activityId,\r\n      activityType: props.activityType\r\n    });\r\n  }, 1000);\r\n};\r\n\r\n// 处理刷新操作\r\nconst processRefresh = () => {\r\n  uni.showLoading({\r\n    title: '刷新中...'\r\n  });\r\n  \r\n  // 模拟API调用\r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    \r\n    // 显示成功提示\r\n    uni.showToast({\r\n      title: currentActionType.value === 'single' ? '刷新成功' : '刷新套餐购买成功',\r\n      icon: 'success'\r\n    });\r\n    \r\n    // 触发事件\r\n    emit('action-completed', {\r\n      action: 'refresh',\r\n      type: currentActionType.value,\r\n      activityId: props.activityId,\r\n      activityType: props.activityType\r\n    });\r\n  }, 1000);\r\n};\r\n\r\n// 处理操作完成\r\nconst processActionCompletion = () => {\r\n  // 根据不同操作类型进行处理\r\n  const actionType = currentAction.value;\r\n  const optionType = selectedOption.value.type;\r\n  \r\n  // 发送操作完成事件\r\n  let eventType = '';\r\n  if (optionType === 'ad') {\r\n    eventType = 'adPublish';\r\n  } else if (optionType === 'paid') {\r\n    eventType = 'paidPublish';\r\n  } else if (optionType === 'count') {\r\n    eventType = 'countRefresh';\r\n  }\r\n  \r\n  emit('action-completed', eventType, {\r\n    action: actionType,\r\n    type: optionType,\r\n    activityId: props.activityId,\r\n    activityType: props.activityType,\r\n    option: selectedOption.value,\r\n    amount: selectedOption.value.amount || '0',\r\n    remainingCount: optionType === 'count' ? selectedOption.value.remainingCount : undefined\r\n  });\r\n  \r\n  // 显示操作成功提示\r\n  let successMessage = '';\r\n  switch(actionType) {\r\n    case 'publish':\r\n      successMessage = '发布成功！';\r\n      break;\r\n    case 'top':\r\n      successMessage = `置顶成功！有效期${selectedOption.value.duration || ''}`;\r\n      break;\r\n    case 'refresh':\r\n      if (optionType === 'count') {\r\n        successMessage = `刷新成功！剩余${selectedOption.value.remainingCount}次`;\r\n      } else {\r\n        successMessage = '刷新成功！';\r\n      }\r\n      break;\r\n  }\r\n  \r\n  uni.showToast({\r\n    title: successMessage,\r\n    icon: 'success'\r\n  });\r\n};\r\n\r\n// 处理刷新操作\r\nconst handleRefreshClick = () => {\r\n  if (remainingRefreshCount.value > 0) {\r\n    showRefreshOptions.value = true;\r\n  } else {\r\n    showRefreshPaymentModal.value = true;\r\n  }\r\n};\r\n\r\n// 关闭刷新选项\r\nconst closeRefreshOptions = () => {\r\n  showRefreshOptions.value = false;\r\n};\r\n\r\n// 关闭刷新付费弹窗\r\nconst closeRefreshPaymentModal = () => {\r\n  showRefreshPaymentModal.value = false;\r\n};\r\n\r\n// 购买刷新套餐\r\nconst buyRefreshPackage = () => {\r\n  closeRefreshPaymentModal();\r\n  showRefreshOptions.value = true;\r\n};\r\n\r\n// 确认付费刷新\r\nconst confirmPaidRefresh = () => {\r\n  closeRefreshPaymentModal();\r\n  \r\n  // 模拟支付流程\r\n  uni.showLoading({\r\n    title: '正在处理支付...'\r\n  });\r\n  \r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    \r\n    // 模拟支付成功\r\n    uni.showModal({\r\n      title: '支付成功',\r\n      content: '您已成功支付，即将刷新信息',\r\n      showCancel: false,\r\n      success: () => {\r\n        // 创建付费刷新选项\r\n        const option = {\r\n          title: '付费刷新',\r\n          subtitle: '立即刷新信息至列表顶部',\r\n          price: `¥${refreshPrice.value}`,\r\n          icon: '/static/images/premium/paid-refresh.png',\r\n          type: 'paid',\r\n          amount: refreshPrice.value\r\n        };\r\n        \r\n        selectedOption.value = option;\r\n        currentAction.value = 'refresh';\r\n        \r\n        // 处理操作完成\r\n        processActionCompletion();\r\n      }\r\n    });\r\n  }, 1500);\r\n};\r\n\r\n// 选择刷新套餐\r\nconst selectRefreshPackage = (pkg) => {\r\n  // 模拟支付流程\r\n  uni.showLoading({\r\n    title: '正在处理支付...'\r\n  });\r\n  \r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    \r\n    // 模拟支付成功\r\n    uni.showModal({\r\n      title: '购买成功',\r\n      content: `您已成功购买${pkg.count}次刷新次数`,\r\n      showCancel: false,\r\n      success: () => {\r\n        // 更新刷新次数\r\n        remainingRefreshCount.value += pkg.count;\r\n        closeRefreshOptions();\r\n        \r\n        // 通知父组件刷新次数已更新\r\n        emit('action-completed', 'refreshPackage', {\r\n          count: pkg.count,\r\n          price: pkg.price,\r\n          totalCount: remainingRefreshCount.value\r\n        });\r\n      }\r\n    });\r\n  }, 1500);\r\n};\r\n\r\n// 使用刷新次数\r\nconst useRefreshCount = () => {\r\n  if (remainingRefreshCount.value <= 0) {\r\n    uni.showToast({\r\n      title: '您没有可用的刷新次数',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n  \r\n  // 减少刷新次数\r\n  remainingRefreshCount.value--;\r\n  closeRefreshOptions();\r\n  \r\n  uni.showLoading({\r\n    title: '正在刷新...'\r\n  });\r\n  \r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    \r\n    // 创建使用刷新次数的选项\r\n    const option = {\r\n      title: '使用刷新次数',\r\n      subtitle: '使用刷新次数刷新信息',\r\n      price: '免费',\r\n      icon: '/static/images/premium/paid-refresh.png',\r\n      type: 'count',\r\n      remainingCount: remainingRefreshCount.value\r\n    };\r\n    \r\n    selectedOption.value = option;\r\n    currentAction.value = 'refresh';\r\n    \r\n    // 处理操作完成\r\n    processActionCompletion();\r\n    \r\n    // 通知父组件刷新次数已更新\r\n    emit('action-completed', 'useRefreshCount', {\r\n      remainingCount: remainingRefreshCount.value\r\n    });\r\n  }, 1000);\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.marketing-promotion-container {\r\n  padding: 20rpx 0;\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Arial, sans-serif;\r\n}\r\n\r\n.promotion-section {\r\n  margin-bottom: 30rpx;\r\n  background-color: #FFFFFF;\r\n  border-radius: 12rpx;\r\n  padding: 24rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.section-header {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.section-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n}\r\n\r\n.section-desc {\r\n  font-size: 24rpx;\r\n  color: #999999;\r\n  margin-top: 6rpx;\r\n}\r\n\r\n.promotion-options {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16rpx;\r\n}\r\n\r\n.promotion-option {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20rpx;\r\n  background-color: #F8F9FA;\r\n  border-radius: 10rpx;\r\n  position: relative;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.promotion-option:active {\r\n  background-color: #F0F0F0;\r\n  transform: scale(0.99);\r\n}\r\n\r\n.option-icon-container {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.normal-icon {\r\n  background: linear-gradient(135deg, #4FACFE, #00F2FE);\r\n  color: white;\r\n}\r\n\r\n.featured-icon {\r\n  background: linear-gradient(135deg, #FFD26F, #FF9A44);\r\n  color: white;\r\n}\r\n\r\n.normal-top-icon {\r\n  background: linear-gradient(135deg, #FF9A9E, #FAD0C4);\r\n  color: white;\r\n}\r\n\r\n.premium-top-icon {\r\n  background: linear-gradient(135deg, #FF6B6B, #FF3366);\r\n  color: white;\r\n}\r\n\r\n.refresh-icon {\r\n  background: linear-gradient(135deg, #A1C4FD, #C2E9FB);\r\n  color: white;\r\n}\r\n\r\n.package-icon {\r\n  background: linear-gradient(135deg, #84FAB0, #8FD3F4);\r\n  color: white;\r\n}\r\n\r\n.option-content {\r\n  flex: 1;\r\n}\r\n\r\n.option-title {\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  color: #333333;\r\n}\r\n\r\n.option-desc {\r\n  font-size: 24rpx;\r\n  color: #999999;\r\n  margin-top: 4rpx;\r\n}\r\n\r\n.option-tag {\r\n  padding: 6rpx 16rpx;\r\n  border-radius: 20rpx;\r\n  font-size: 24rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.free-tag {\r\n  background-color: #E8F5E9;\r\n  color: #4CAF50;\r\n}\r\n\r\n.paid-tag {\r\n  background-color: #FFF3E0;\r\n  color: #FF9800;\r\n}\r\n\r\n/* 支付弹窗样式 */\r\n.payment-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 1000;\r\n}\r\n\r\n.modal-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.modal-container {\r\n  position: absolute;\r\n  left: 50%;\r\n  top: 50%;\r\n  transform: translate(-50%, -50%);\r\n  width: 80%;\r\n  max-width: 600rpx;\r\n  background-color: #FFFFFF;\r\n  border-radius: 16rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.modal-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 30rpx;\r\n  border-bottom: 1rpx solid #F0F0F0;\r\n}\r\n\r\n.modal-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n}\r\n\r\n.modal-close {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #999999;\r\n}\r\n\r\n.modal-content {\r\n  padding: 30rpx;\r\n}\r\n\r\n.payment-info {\r\n  margin-bottom: 30rpx;\r\n  text-align: center;\r\n}\r\n\r\n.payment-desc {\r\n  font-size: 28rpx;\r\n  color: #666666;\r\n}\r\n\r\n.payment-price {\r\n  font-size: 48rpx;\r\n  font-weight: 600;\r\n  color: #FF6B6B;\r\n  margin-top: 16rpx;\r\n  display: block;\r\n}\r\n\r\n.payment-methods {\r\n  margin-top: 30rpx;\r\n}\r\n\r\n.methods-title {\r\n  font-size: 28rpx;\r\n  color: #333333;\r\n  margin-bottom: 16rpx;\r\n  display: block;\r\n}\r\n\r\n.method-options {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16rpx;\r\n}\r\n\r\n.method-option {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20rpx;\r\n  background-color: #F8F9FA;\r\n  border-radius: 10rpx;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.method-option.active {\r\n  background-color: #F0F7FF;\r\n  border: 1rpx solid #4FACFE;\r\n}\r\n\r\n.method-icon {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 20rpx;\r\n  color: white;\r\n}\r\n\r\n.wxpay-icon {\r\n  background-color: #09BB07;\r\n}\r\n\r\n.alipay-icon {\r\n  background-color: #00A0E9;\r\n}\r\n\r\n.method-name {\r\n  flex: 1;\r\n  font-size: 28rpx;\r\n  color: #333333;\r\n}\r\n\r\n.method-check {\r\n  width: 36rpx;\r\n  height: 36rpx;\r\n  border-radius: 50%;\r\n  border: 1rpx solid #DDDDDD;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.method-option.active .method-check {\r\n  border-color: #4FACFE;\r\n}\r\n\r\n.check-inner {\r\n  width: 20rpx;\r\n  height: 20rpx;\r\n  border-radius: 50%;\r\n  background-color: #4FACFE;\r\n}\r\n\r\n.modal-footer {\r\n  display: flex;\r\n  border-top: 1rpx solid #F0F0F0;\r\n}\r\n\r\n.btn-cancel, .btn-confirm {\r\n  flex: 1;\r\n  height: 90rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 32rpx;\r\n  border: none;\r\n  background-color: transparent;\r\n}\r\n\r\n.btn-cancel {\r\n  color: #999999;\r\n  border-right: 1rpx solid #F0F0F0;\r\n}\r\n\r\n.btn-confirm {\r\n  color: #4FACFE;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 新的发布选项卡片样式 */\r\n.publish-options-card {\r\n  background-color: #FFFFFF;\r\n  border-radius: 12rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.publish-option-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 30rpx 24rpx;\r\n  border-bottom: 1rpx solid #F5F5F5;\r\n}\r\n\r\n.publish-option-row:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.publish-option-left {\r\n  display: flex;\r\n  align-items: center;\r\n  flex: 1;\r\n}\r\n\r\n.publish-option-icon {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.ad-icon {\r\n  background: linear-gradient(135deg, #4FACFE, #00F2FE);\r\n  color: white;\r\n}\r\n\r\n.paid-icon {\r\n  background: linear-gradient(135deg, #FF9A44, #FF6B6B);\r\n  color: white;\r\n}\r\n\r\n.publish-option-content {\r\n  flex: 1;\r\n}\r\n\r\n.publish-option-title {\r\n  font-size: 32rpx;\r\n  font-weight: 500;\r\n  color: #333333;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.publish-option-desc {\r\n  font-size: 24rpx;\r\n  color: #999999;\r\n}\r\n\r\n.publish-option-right {\r\n  margin-left: 20rpx;\r\n}\r\n\r\n.publish-btn {\r\n  min-width: 120rpx;\r\n  height: 64rpx;\r\n  border-radius: 32rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  border: none;\r\n}\r\n\r\n.free-btn {\r\n  background: linear-gradient(135deg, #4FACFE, #00F2FE);\r\n  color: white;\r\n}\r\n\r\n.paid-btn {\r\n  background: linear-gradient(135deg, #FF9A44, #FF6B6B);\r\n  color: white;\r\n}\r\n\r\n/* 发布时长选择 */\r\n.publish-duration-select {\r\n  padding: 24rpx;\r\n  background-color: #F8F9FA;\r\n}\r\n\r\n.duration-title {\r\n  font-size: 28rpx;\r\n  color: #333333;\r\n  margin-bottom: 16rpx;\r\n  display: block;\r\n}\r\n\r\n.duration-options {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.duration-option {\r\n  flex: 1;\r\n  height: 100rpx;\r\n  background-color: #FFFFFF;\r\n  border-radius: 8rpx;\r\n  margin: 0 10rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 2rpx solid #EEEEEE;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.duration-option:first-child {\r\n  margin-left: 0;\r\n}\r\n\r\n.duration-option:last-child {\r\n  margin-right: 0;\r\n}\r\n\r\n.duration-option.active {\r\n  border-color: #FF9A44;\r\n  background-color: #FFF9F5;\r\n}\r\n\r\n.duration-text {\r\n  font-size: 28rpx;\r\n  color: #333333;\r\n  margin-bottom: 4rpx;\r\n}\r\n\r\n.duration-price {\r\n  font-size: 24rpx;\r\n  color: #FF6B6B;\r\n  font-weight: 500;\r\n}\r\n\r\n.duration-option.active .duration-text {\r\n  color: #FF6B6B;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 刷新付费弹窗样式 */\r\n.refresh-payment-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.7);\r\n  z-index: 1000;\r\n  backdrop-filter: blur(8px);\r\n}\r\n\r\n.refresh-payment-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n}\r\n\r\n.refresh-payment-content {\r\n  position: fixed;\r\n  left: 50%;\r\n  top: 50%;\r\n  transform: translate(-50%, -50%);\r\n  width: 90%;\r\n  max-width: 650rpx;\r\n  z-index: 1001;\r\n  background: linear-gradient(135deg, #ffffff, #f8f9ff);\r\n  border-radius: 16rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);\r\n  border: 1rpx solid rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n/* 现代设计风格的弹窗样式 */\r\n.modern-design {\r\n  border-radius: 18px;\r\n  overflow: hidden;\r\n  background: #ffffff;\r\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.07);\r\n  width: 85%;\r\n  max-width: 330px;\r\n  padding: 0;\r\n  border: none;\r\n}\r\n\r\n.refresh-header-gradient {\r\n  background: linear-gradient(135deg, #3a7bd5, #00d2ff);\r\n  padding: 22px 20px;\r\n  color: #fff;\r\n  position: relative;\r\n  text-align: center;\r\n}\r\n\r\n.refresh-badge {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  background: rgba(255, 255, 255, 0.25);\r\n  backdrop-filter: blur(5px);\r\n  border-radius: 20px;\r\n  padding: 4px 12px;\r\n  margin-bottom: 14px;\r\n  color: #fff;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.refresh-badge svg {\r\n  margin-right: 6px;\r\n}\r\n\r\n.refresh-title {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  margin-bottom: 4px;\r\n  display: block;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.refresh-subtitle {\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n  display: block;\r\n}\r\n\r\n.refresh-price-area {\r\n  background: #f8faff;\r\n  padding: 16px 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  border-bottom: 1px solid #eef2f9;\r\n}\r\n\r\n.price-tag {\r\n  display: flex;\r\n  align-items: baseline;\r\n}\r\n\r\n.price-symbol {\r\n  font-size: 16px;\r\n  color: #3a7bd5;\r\n  margin-right: 2px;\r\n}\r\n\r\n.price-value {\r\n  font-size: 28px;\r\n  font-weight: 700;\r\n  color: #3a7bd5;\r\n}\r\n\r\n.price-label {\r\n  color: #8a9ab0;\r\n  font-size: 14px;\r\n}\r\n\r\n.refresh-info-box {\r\n  margin: 0 20px;\r\n  padding: 12px 14px;\r\n  background: #ebf3ff;\r\n  border-left: 3px solid #3a7bd5;\r\n  border-radius: 8px;\r\n}\r\n\r\n.info-highlight {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #333;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.info-detail {\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n.highlight-text {\r\n  color: #ff6b42;\r\n  font-weight: 600;\r\n}\r\n\r\n.refresh-package-hint {\r\n  margin: 16px 20px 0;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.package-hint-left {\r\n  font-size: 13px;\r\n  font-weight: 500;\r\n  color: #3a7bd5;\r\n  background: rgba(58, 123, 213, 0.1);\r\n  padding: 4px 12px;\r\n  border-radius: 14px;\r\n}\r\n\r\n.package-hint-right {\r\n  font-size: 12px;\r\n  color: #8a9ab0;\r\n}\r\n\r\n.highlight-discount {\r\n  margin-left: 4px;\r\n  font-size: 16px;\r\n  font-weight: 700;\r\n  color: #ff6b42;\r\n}\r\n\r\n.refresh-action-buttons {\r\n  display: flex;\r\n  padding: 16px 20px 20px;\r\n  gap: 10px;\r\n}\r\n\r\n.btn-check-package {\r\n  flex: 1;\r\n  height: 40px;\r\n  border-radius: 20px;\r\n  border: 1px solid #3a7bd5;\r\n  background: transparent;\r\n  color: #3a7bd5;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.btn-refresh-now {\r\n  flex: 1;\r\n  height: 40px;\r\n  border-radius: 20px;\r\n  background: linear-gradient(135deg, #3a7bd5, #00d2ff);\r\n  color: #fff;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  box-shadow: 0 4px 10px rgba(58, 123, 213, 0.3);\r\n  border: none;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* 刷新套餐选择弹窗样式 */\r\n.refresh-packages-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.7);\r\n  z-index: 1000;\r\n  backdrop-filter: blur(8px);\r\n}\r\n\r\n.refresh-packages-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n}\r\n\r\n.refresh-packages-content {\r\n  position: fixed;\r\n  left: 50%;\r\n  top: 50%;\r\n  transform: translate(-50%, -50%);\r\n  width: 90%;\r\n  max-width: 650rpx;\r\n  z-index: 1001;\r\n  background: linear-gradient(135deg, #ffffff, #f8f9ff);\r\n  border-radius: 24rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);\r\n  border: 1rpx solid rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.refresh-packages-header {\r\n  padding: 30rpx;\r\n  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);\r\n  background: linear-gradient(135deg, #f0f7ff, #e8f4ff);\r\n}\r\n\r\n.refresh-packages-title {\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  text-align: center;\r\n  position: relative;\r\n}\r\n\r\n.refresh-icon-wrapper {\r\n  display: inline-block;\r\n  margin-right: 8rpx;\r\n  font-size: 32rpx;\r\n  animation: spin 2s infinite linear;\r\n}\r\n\r\n@keyframes spin {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n.refresh-packages-title::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -10rpx;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 60rpx;\r\n  height: 4rpx;\r\n  background: linear-gradient(90deg, #007aff, #5856d6);\r\n  border-radius: 2rpx;\r\n}\r\n\r\n.refresh-packages-body {\r\n  padding: 40rpx 30rpx;\r\n}\r\n\r\n.refresh-packages-desc {\r\n  font-size: 28rpx;\r\n  color: #555;\r\n  margin-bottom: 30rpx;\r\n  text-align: center;\r\n  font-weight: 500;\r\n  background: rgba(0, 122, 255, 0.08);\r\n  padding: 16rpx;\r\n  border-radius: 12rpx;\r\n  line-height: 1.5;\r\n}\r\n\r\n.refresh-packages-list {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  flex-wrap: wrap;\r\n  margin-bottom: 40rpx;\r\n}\r\n\r\n.refresh-package-item {\r\n  width: 30%;\r\n  height: 180rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: linear-gradient(135deg, #fff8f0 0%, #fff1e6 100%);\r\n  border-radius: 16rpx;\r\n  box-shadow: 0 8rpx 16rpx rgba(255, 145, 85, 0.15);\r\n  border: 1rpx solid rgba(255, 145, 85, 0.1);\r\n  position: relative;\r\n  overflow: hidden;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.refresh-package-item:active {\r\n  transform: scale(0.98);\r\n  box-shadow: 0 4rpx 8rpx rgba(255, 145, 85, 0.1);\r\n}\r\n\r\n.refresh-package-item::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 6rpx;\r\n  background: linear-gradient(90deg, #ff6b6b, #ff9f43);\r\n}\r\n\r\n.refresh-package-count {\r\n  font-size: 36rpx;\r\n  font-weight: 700;\r\n  color: #ff6b6b;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.refresh-package-price {\r\n  font-size: 30rpx;\r\n  font-weight: 600;\r\n  color: #ff9f43;\r\n}\r\n\r\n.refresh-package-discount {\r\n  position: absolute;\r\n  top: 10rpx;\r\n  right: 10rpx;\r\n  font-size: 22rpx;\r\n  color: #fff;\r\n  font-weight: 600;\r\n  background: linear-gradient(135deg, #ff6b6b, #ff9f43);\r\n  padding: 4rpx 10rpx;\r\n  border-radius: 8rpx;\r\n  transform: rotate(10deg);\r\n  box-shadow: 0 2rpx 6rpx rgba(255, 107, 107, 0.3);\r\n}\r\n\r\n.refresh-packages-actions {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  padding: 20rpx 30rpx 40rpx;\r\n  gap: 20rpx;\r\n}\r\n\r\n.refresh-packages-confirm-btn {\r\n  flex: 1;\r\n  height: 90rpx;\r\n  line-height: 90rpx;\r\n  text-align: center;\r\n  font-size: 30rpx;\r\n  font-weight: 500;\r\n  border-radius: 45rpx;\r\n  background: linear-gradient(135deg, #007aff, #5856d6);\r\n  color: #FFFFFF;\r\n  box-shadow: 0 6rpx 12rpx rgba(0, 122, 255, 0.2);\r\n  transition: all 0.3s;\r\n}\r\n\r\n.refresh-packages-confirm-btn:active {\r\n  transform: scale(0.98);\r\n  box-shadow: 0 3rpx 6rpx rgba(0, 122, 255, 0.15);\r\n}\r\n\r\n.refresh-packages-cancel-btn {\r\n  flex: 1;\r\n  height: 90rpx;\r\n  line-height: 90rpx;\r\n  text-align: center;\r\n  font-size: 30rpx;\r\n  font-weight: 500;\r\n  border-radius: 45rpx;\r\n  background: rgba(0, 0, 0, 0.05);\r\n  color: #666;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.refresh-packages-cancel-btn:active {\r\n  transform: scale(0.98);\r\n  background: rgba(0, 0, 0, 0.08);\r\n}\r\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/components/MarketingPromotionActions.vue'\nwx.createComponent(Component)"], "names": ["ref", "computed", "reactive", "option", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0ZA,UAAM,QAAQ;AA2Cd,UAAM,OAAO;AAGWA,kBAAG,IAAC,KAAK;AACjC,UAAM,mBAAmBA,cAAAA,IAAI,KAAK;AAClC,UAAM,oBAAoBA,cAAAA,IAAI,EAAE;AAChC,UAAM,mBAAmBA,cAAAA,IAAI,EAAE;AAC/B,UAAM,oBAAoBA,cAAAA,IAAI,EAAE;AAChC,UAAM,gBAAgBA,cAAAA,IAAI,OAAO;AACjC,UAAM,gBAAgBA,cAAAA,IAAI,EAAE;AAC5B,UAAM,oBAAoBA,cAAAA,IAAI,EAAE;AAChC,UAAM,qBAAqBA,cAAAA,IAAI,KAAK;AACpC,UAAM,mBAAmBA,cAAAA,IAAI,IAAI;AACjC,UAAM,qBAAqBA,cAAAA,IAAI,KAAK;AACpC,UAAM,0BAA0BA,cAAAA,IAAI,KAAK;AACzC,UAAM,iBAAiBA,cAAAA,IAAI,IAAI;AAC/B,UAAM,gBAAgBA,cAAAA,IAAI,IAAI;AAG9B,UAAM,wBAAwBA,cAAG,IAAC,MAAM,YAAY;AAUpD,UAAM,eAAeA,cAAAA,IAAI,GAAG;AAG5B,UAAM,kBAAkB;AAAA,MACtB,EAAE,OAAO,IAAI,OAAO,MAAM,OAAO,UAAW;AAAA,MAC5C,EAAE,OAAO,IAAI,OAAO,MAAM,OAAO,UAAW;AAAA,MAC5C,EAAE,OAAO,KAAK,OAAO,MAAM,OAAO,WAAY;AAAA,IAChD;AAGA,UAAM,cAAcC,cAAQ,SAAC,MAAM;AACjC,aAAO,MAAM,YAAY,SAAS,SAAS;AAAA,IAC7C,CAAC;AAED,UAAM,UAAUA,cAAQ,SAAC,MAAM;AAC7B,aAAO,CAAC,MAAM,mBAAmB,MAAM,YAAY,SAAS,KAAK;AAAA,IACnE,CAAC;AAED,UAAM,cAAcA,cAAQ,SAAC,MAAM;AACjC,aAAO,CAAC,MAAM,mBAAmB,MAAM,YAAY,SAAS,SAAS;AAAA,IACvE,CAAC;AAGD,UAAM,YAAYA,cAAQ,SAAC,MAAM;AAE/B,YAAM,QAAQ;AACd,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,QAAQ,cAAc,YAAY,SAAS,KAAK;AACtD,aAAO,MAAM,SAAS,SAAS;AAAA,IACjC,CAAC;AAGmBC,kBAAAA,SAAS;AAAA,MAC3B,OAAO;AAAA,MACP,aAAa;AAAA,MACb,SAAS,CAAE;AAAA,IACb,CAAC;AAkDD,UAAM,qBAAqB,CAAC,QAAQ,eAAe;AACjD,oBAAc,QAAQ;AAGtB,UAAI,eAAe,QAAQ;AAEzB,YAAI,UAAU,SAAS,WAAW,WAAW;AAC3C,gBAAMC,UAAS;AAAA,YACb,OAAO;AAAA,YACP,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,YACN,MAAM;AAAA,YACN,UAAU;AAAA,YACV,QAAQ;AAAA,UAChB;AACM,yBAAe,QAAQA;AACvB;AACA;AAAA,QACD;AAED,2BAAmB,QAAQ;AAC3B;AAAA,MACD;AAGD,UAAI,SAAS;AAEb,UAAI,WAAW,WAAW;AACxB,YAAI,eAAe,MAAM;AACvB,cAAI,UAAU,OAAO;AACnB,qBAAS;AAAA,cACP,OAAO;AAAA,cACP,UAAU;AAAA,cACV,OAAO;AAAA,cACP,MAAM;AAAA,cACN,MAAM;AAAA,cACN,UAAU;AAAA,YACpB;AAAA,UACA,OAAa;AACP,qBAAS;AAAA,cACP,OAAO;AAAA,cACP,UAAU;AAAA,cACV,OAAO;AAAA,cACP,MAAM;AAAA,cACN,MAAM;AAAA,cACN,UAAU;AAAA,YAClB;AAAA,UACO;AAAA,QACF;AAAA,MACL,WAAa,WAAW,OAAO;AAC3B,YAAI,eAAe,MAAM;AACvB,mBAAS;AAAA,YACP,OAAO;AAAA,YACP,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,YACN,MAAM;AAAA,YACN,UAAU;AAAA,UAClB;AAAA,QACK;AAAA,MACL,WAAa,WAAW,WAAW;AAC/B,YAAI,eAAe,MAAM;AACvB,mBAAS;AAAA,YACP,OAAO;AAAA,YACP,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,YACN,MAAM;AAAA,UACd;AAAA,QACK;AAAA,MACF;AAED,UAAI,QAAQ;AACV,uBAAe,QAAQ;AAGvB,YAAI,eAAe,MAAM;AACvB;QACD;AAAA,MACF;AAAA,IACH;AAGA,UAAM,iBAAiB,CAAC,UAAU,UAAU;AAC1C,uBAAiB,QAAQ;AACzB,oBAAc,QAAQ;AAEtB,UAAI,eAAe;AACnB,UAAI,aAAa,MAAM;AACrB,uBAAe;AACf,gBAAQ;AAAA,MACZ,WAAa,aAAa,MAAM;AAC5B,uBAAe;AACf,gBAAQ;AAAA,MACZ,WAAa,aAAa,MAAM;AAC5B,uBAAe;AACf,gBAAQ;AAAA,MACT;AAGD,YAAM,SAAS;AAAA,QACb,OAAO,cAAc,UAAU,YAAY,SACpC,cAAc,UAAU,QAAQ,SAAS;AAAA,QAChD,UAAU,GAAG,cAAc,UAAU,YAAY,SACtC,cAAc,UAAU,QAAQ,SAAS,MAAM,GAAG,YAAY;AAAA,QACzE,OAAO,IAAI,KAAK;AAAA,QAChB,MAAM,cAAc,UAAU,YAAY,4CACpC,cAAc,UAAU,QAAQ,wCAChC;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,QAAQ;AAAA,MACZ;AAEE,qBAAe,QAAQ;AACvB;AAGA,wBAAkB,QAAQ,KAAK,YAAY,IAAI,IAAI,KAAK,EAAE;AAC1D,yBAAmB,QAAQ;AAAA,IAC7B;AAGA,UAAM,oBAAoB,CAAC,OAAO,MAAM,UAAU;AAChD,wBAAkB,QAAQ;AAC1B,uBAAiB,QAAQ;AACzB,wBAAkB,QAAQ;AAC1B,uBAAiB,QAAQ;AAAA,IAC3B;AAGA,UAAM,oBAAoB,MAAM;AAC9B,uBAAiB,QAAQ;AAAA,IAC3B;AAGA,UAAM,iBAAiB,MAAM;AAE3BC,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGf;AAGA,gBAAO,cAAc,OAAK;AAAA,UACxB,KAAK;AACH;AACA;AAAA,UACF,KAAK;AACH;AACA;AAAA,UACF,KAAK;AACH;AACA;AAAA,QACH;AAAA,MACF,GAAE,IAAI;AAAA,IACT;AA0CA,UAAM,SAAS,MAAM;AAEnBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,SAAS,MAAM;AACb;UACD;AAAA,QACP,CAAK;AAAA,MACF,GAAE,IAAI;AAAA,IACT;AA6CA,UAAM,oBAAoB,MAAM;AAE9B,yBAAmB,QAAQ;AAG3BA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,SAAS,MAAM;AACb;UACD;AAAA,QACP,CAAK;AAAA,MACF,GAAE,IAAI;AAAA,IACT;AAGA,UAAM,iBAAiB,MAAM;AAC3BA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,kBAAkB,UAAU,WAAW,SAAS;AAAA,UACvD,MAAM;AAAA,QACZ,CAAK;AAGD,aAAK,oBAAoB;AAAA,UACvB,QAAQ;AAAA,UACR,MAAM,kBAAkB;AAAA,UACxB,YAAY,MAAM;AAAA,UAClB,cAAc,MAAM;AAAA,QAC1B,CAAK;AAAA,MACF,GAAE,GAAI;AAAA,IACT;AAGA,UAAM,aAAa,MAAM;AACvBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,kBAAkB,UAAU,WAAW,WAAW;AAAA,UACzD,MAAM;AAAA,QACZ,CAAK;AAGD,aAAK,oBAAoB;AAAA,UACvB,QAAQ;AAAA,UACR,MAAM,kBAAkB;AAAA,UACxB,YAAY,MAAM;AAAA,UAClB,cAAc,MAAM;AAAA,QAC1B,CAAK;AAAA,MACF,GAAE,GAAI;AAAA,IACT;AAGA,UAAM,iBAAiB,MAAM;AAC3BA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,kBAAkB,UAAU,WAAW,SAAS;AAAA,UACvD,MAAM;AAAA,QACZ,CAAK;AAGD,aAAK,oBAAoB;AAAA,UACvB,QAAQ;AAAA,UACR,MAAM,kBAAkB;AAAA,UACxB,YAAY,MAAM;AAAA,UAClB,cAAc,MAAM;AAAA,QAC1B,CAAK;AAAA,MACF,GAAE,GAAI;AAAA,IACT;AAGA,UAAM,0BAA0B,MAAM;AAEpC,YAAM,aAAa,cAAc;AACjC,YAAM,aAAa,eAAe,MAAM;AAGxC,UAAI,YAAY;AAChB,UAAI,eAAe,MAAM;AACvB,oBAAY;AAAA,MAChB,WAAa,eAAe,QAAQ;AAChC,oBAAY;AAAA,MAChB,WAAa,eAAe,SAAS;AACjC,oBAAY;AAAA,MACb;AAED,WAAK,oBAAoB,WAAW;AAAA,QAClC,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,YAAY,MAAM;AAAA,QAClB,cAAc,MAAM;AAAA,QACpB,QAAQ,eAAe;AAAA,QACvB,QAAQ,eAAe,MAAM,UAAU;AAAA,QACvC,gBAAgB,eAAe,UAAU,eAAe,MAAM,iBAAiB;AAAA,MACnF,CAAG;AAGD,UAAI,iBAAiB;AACrB,cAAO,YAAU;AAAA,QACf,KAAK;AACH,2BAAiB;AACjB;AAAA,QACF,KAAK;AACH,2BAAiB,WAAW,eAAe,MAAM,YAAY,EAAE;AAC/D;AAAA,QACF,KAAK;AACH,cAAI,eAAe,SAAS;AAC1B,6BAAiB,UAAU,eAAe,MAAM,cAAc;AAAA,UACtE,OAAa;AACL,6BAAiB;AAAA,UAClB;AACD;AAAA,MACH;AAEDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,qBAAqB,MAAM;AAC/B,UAAI,sBAAsB,QAAQ,GAAG;AACnC,2BAAmB,QAAQ;AAAA,MAC/B,OAAS;AACL,gCAAwB,QAAQ;AAAA,MACjC;AAAA,IACH;AAGA,UAAM,sBAAsB,MAAM;AAChC,yBAAmB,QAAQ;AAAA,IAC7B;AAGA,UAAM,2BAA2B,MAAM;AACrC,8BAAwB,QAAQ;AAAA,IAClC;AAGA,UAAM,oBAAoB,MAAM;AAC9B;AACA,yBAAmB,QAAQ;AAAA,IAC7B;AAGA,UAAM,qBAAqB,MAAM;AAC/B;AAGAA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,SAAS,MAAM;AAEb,kBAAM,SAAS;AAAA,cACb,OAAO;AAAA,cACP,UAAU;AAAA,cACV,OAAO,IAAI,aAAa,KAAK;AAAA,cAC7B,MAAM;AAAA,cACN,MAAM;AAAA,cACN,QAAQ,aAAa;AAAA,YAC/B;AAEQ,2BAAe,QAAQ;AACvB,0BAAc,QAAQ;AAGtB;UACD;AAAA,QACP,CAAK;AAAA,MACF,GAAE,IAAI;AAAA,IACT;AAGA,UAAM,uBAAuB,CAAC,QAAQ;AAEpCA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS,SAAS,IAAI,KAAK;AAAA,UAC3B,YAAY;AAAA,UACZ,SAAS,MAAM;AAEb,kCAAsB,SAAS,IAAI;AACnC;AAGA,iBAAK,oBAAoB,kBAAkB;AAAA,cACzC,OAAO,IAAI;AAAA,cACX,OAAO,IAAI;AAAA,cACX,YAAY,sBAAsB;AAAA,YAC5C,CAAS;AAAA,UACF;AAAA,QACP,CAAK;AAAA,MACF,GAAE,IAAI;AAAA,IACT;AAGA,UAAM,kBAAkB,MAAM;AAC5B,UAAI,sBAAsB,SAAS,GAAG;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAGD,4BAAsB;AACtB;AAEAA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGf,cAAM,SAAS;AAAA,UACb,OAAO;AAAA,UACP,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,gBAAgB,sBAAsB;AAAA,QAC5C;AAEI,uBAAe,QAAQ;AACvB,sBAAc,QAAQ;AAGtB;AAGA,aAAK,oBAAoB,mBAAmB;AAAA,UAC1C,gBAAgB,sBAAsB;AAAA,QAC5C,CAAK;AAAA,MACF,GAAE,GAAI;AAAA,IACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpmCA,GAAG,gBAAgB,SAAS;"}