{"version": 3, "file": "team.js", "sources": ["subPackages/distribution/pages/team.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcZGlzdHJpYnV0aW9uXHBhZ2VzXHRlYW0udnVl"], "sourcesContent": ["<template>\r\n  <view class=\"team-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">我的团队</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 团队统计卡片 -->\r\n    <view class=\"stats-card\">\r\n      <view class=\"stats-header\">\r\n        <text class=\"stats-title\">团队概览</text>\r\n      </view>\r\n      \r\n      <view class=\"stats-content\">\r\n        <view class=\"stats-item\">\r\n          <text class=\"item-value\">{{teamSummary.level1Count + teamSummary.level2Count}}</text>\r\n          <text class=\"item-label\">团队总人数</text>\r\n        </view>\r\n        \r\n        <view class=\"stats-item\">\r\n          <text class=\"item-value\">¥{{formatCommission(teamSummary.totalContribution)}}</text>\r\n          <text class=\"item-label\">团队总业绩</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"level-stats\">\r\n        <view class=\"level-item\">\r\n          <view class=\"level-icon level1\"></view>\r\n          <view class=\"level-info\">\r\n            <text class=\"level-name\">一级成员</text>\r\n            <text class=\"level-count\">{{teamSummary.level1Count}}人</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"level-item\">\r\n          <view class=\"level-icon level2\"></view>\r\n          <view class=\"level-info\">\r\n            <text class=\"level-name\">二级成员</text>\r\n            <text class=\"level-count\">{{teamSummary.level2Count}}人</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 筛选栏 -->\r\n    <view class=\"filter-bar\">\r\n      <view \r\n        v-for=\"(tab, index) in tabs\" \r\n        :key=\"index\" \r\n        class=\"tab-item\" \r\n        :class=\"{ 'active': activeTab === tab.value }\"\r\n        @click=\"switchTab(tab.value)\"\r\n      >\r\n        <text>{{tab.name}}</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 团队成员列表 -->\r\n    <view class=\"member-list\" v-if=\"teamMembers.length > 0\">\r\n      <view \r\n        v-for=\"(member, index) in teamMembers\" \r\n        :key=\"index\" \r\n        class=\"member-item\"\r\n      >\r\n        <view class=\"member-info\">\r\n          <image class=\"member-avatar\" :src=\"member.avatar\" mode=\"aspectFill\"></image>\r\n          <view class=\"member-details\">\r\n            <text class=\"member-name\">{{member.nickname}}</text>\r\n            <view class=\"member-meta\">\r\n              <text class=\"member-level\">{{member.levelName}}</text>\r\n              <text class=\"member-time\">{{formatTime(member.joinTime)}}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"member-stats\">\r\n          <view class=\"stat-item\">\r\n            <text class=\"stat-value\">¥{{formatCommission(member.contribution)}}</text>\r\n            <text class=\"stat-label\">贡献佣金</text>\r\n          </view>\r\n          \r\n          <view class=\"stat-item\">\r\n            <text class=\"stat-value\">{{member.orderCount}}</text>\r\n            <text class=\"stat-label\">订单数</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 空状态 -->\r\n    <view class=\"empty-state\" v-else>\r\n      <image class=\"empty-image\" src=\"/static/images/empty-team.png\" mode=\"aspectFit\"></image>\r\n      <text class=\"empty-text\">暂无团队成员</text>\r\n      <button class=\"invite-btn\" @click=\"navigateTo('/subPackages/distribution/pages/promotion')\">邀请好友</button>\r\n    </view>\r\n    \r\n    <!-- 加载更多 -->\r\n    <view class=\"load-more\" v-if=\"hasMoreData && teamMembers.length > 0\">\r\n      <text v-if=\"loading\">加载中...</text>\r\n      <text v-else @click=\"loadMore\">点击加载更多</text>\r\n    </view>\r\n    \r\n    <!-- 团队规则 -->\r\n    <view class=\"rules-card\">\r\n      <view class=\"card-header\">\r\n        <text class=\"card-title\">团队规则</text>\r\n      </view>\r\n      \r\n      <view class=\"rules-content\">\r\n        <view class=\"rule-item\">\r\n          <view class=\"rule-icon\"></view>\r\n          <text class=\"rule-text\">通过您的推广链接注册的用户为您的一级团队成员</text>\r\n        </view>\r\n        \r\n        <view class=\"rule-item\">\r\n          <view class=\"rule-icon\"></view>\r\n          <text class=\"rule-text\">一级团队成员发展的分销员为您的二级团队成员</text>\r\n        </view>\r\n        \r\n        <view class=\"rule-item\">\r\n          <view class=\"rule-icon\"></view>\r\n          <text class=\"rule-text\">您可以获得一级团队成员订单佣金的{{commissionRates.level1}}%</text>\r\n        </view>\r\n        \r\n        <view class=\"rule-item\">\r\n          <view class=\"rule-icon\"></view>\r\n          <text class=\"rule-text\">您可以获得二级团队成员订单佣金的{{commissionRates.level2}}%</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 团队发展建议 -->\r\n    <view class=\"tips-card\">\r\n      <view class=\"card-header\">\r\n        <text class=\"card-title\">发展建议</text>\r\n      </view>\r\n      \r\n      <view class=\"tips-content\">\r\n        <view class=\"tip-item\">\r\n          <view class=\"tip-icon\"></view>\r\n          <text class=\"tip-text\">分享优质商品和专属优惠，吸引更多用户加入</text>\r\n        </view>\r\n        \r\n        <view class=\"tip-item\">\r\n          <view class=\"tip-icon\"></view>\r\n          <text class=\"tip-text\">定期与团队成员沟通，分享推广技巧</text>\r\n        </view>\r\n        \r\n        <view class=\"tip-item\">\r\n          <view class=\"tip-icon\"></view>\r\n          <text class=\"tip-text\">鼓励团队成员发展下线，共同壮大团队</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted } from 'vue';\r\nimport distributionService from '@/utils/distributionService';\r\n\r\n// 选项卡\r\nconst tabs = [\r\n  { name: '全部成员', value: 'all' },\r\n  { name: '一级成员', value: 1 },\r\n  { name: '二级成员', value: 2 }\r\n];\r\n\r\n// 当前选中的选项卡\r\nconst activeTab = ref('all');\r\n\r\n// 团队成员\r\nconst teamMembers = ref([]);\r\n\r\n// 团队统计\r\nconst teamSummary = reactive({\r\n  level1Count: 0,\r\n  level2Count: 0,\r\n  totalContribution: 0\r\n});\r\n\r\n// 佣金比例\r\nconst commissionRates = reactive({\r\n  level1: 30,\r\n  level2: 10\r\n});\r\n\r\n// 分页信息\r\nconst pagination = reactive({\r\n  page: 1,\r\n  pageSize: 10,\r\n  total: 0,\r\n  totalPages: 0\r\n});\r\n\r\n// 是否有更多数据\r\nconst hasMoreData = ref(false);\r\n\r\n// 是否正在加载\r\nconst loading = ref(false);\r\n\r\n// 页面加载\r\nonMounted(async () => {\r\n  // 获取团队成员\r\n  await getTeamMembers();\r\n});\r\n\r\n// 获取团队成员\r\nconst getTeamMembers = async (loadMore = false) => {\r\n  if (loading.value) return;\r\n  \r\n  try {\r\n    loading.value = true;\r\n    \r\n    const page = loadMore ? pagination.page + 1 : 1;\r\n    const level = activeTab.value === 'all' ? undefined : activeTab.value;\r\n    \r\n    const result = await distributionService.getTeamMembers({\r\n      page,\r\n      pageSize: pagination.pageSize,\r\n      level\r\n    });\r\n    \r\n    if (result) {\r\n      // 更新团队成员\r\n      if (loadMore) {\r\n        teamMembers.value = [...teamMembers.value, ...result.list];\r\n      } else {\r\n        teamMembers.value = result.list;\r\n      }\r\n      \r\n      // 更新分页信息\r\n      pagination.page = page;\r\n      pagination.total = result.pagination.total;\r\n      pagination.totalPages = result.pagination.totalPages;\r\n      \r\n      // 更新是否有更多数据\r\n      hasMoreData.value = pagination.page < pagination.totalPages;\r\n      \r\n      // 更新团队统计\r\n      if (result.summary) {\r\n        Object.assign(teamSummary, result.summary);\r\n      }\r\n    }\r\n  } catch (error) {\r\n    console.error('获取团队成员失败', error);\r\n    uni.showToast({\r\n      title: '获取团队成员失败',\r\n      icon: 'none'\r\n    });\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\n// 切换选项卡\r\nconst switchTab = (tab) => {\r\n  if (activeTab.value === tab) return;\r\n  \r\n  activeTab.value = tab;\r\n  getTeamMembers();\r\n};\r\n\r\n// 加载更多\r\nconst loadMore = () => {\r\n  if (hasMoreData.value && !loading.value) {\r\n    getTeamMembers(true);\r\n  }\r\n};\r\n\r\n// 格式化佣金\r\nconst formatCommission = (amount) => {\r\n  return distributionService.formatCommission(amount);\r\n};\r\n\r\n// 格式化时间\r\nconst formatTime = (time) => {\r\n  if (!time) return '';\r\n  \r\n  const date = new Date(time);\r\n  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;\r\n};\r\n\r\n// 页面导航\r\nconst navigateTo = (url) => {\r\n  uni.navigateTo({ url });\r\n};\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 显示帮助\r\nconst showHelp = () => {\r\n  uni.showModal({\r\n    title: '团队帮助',\r\n    content: '团队页面显示您发展的分销员信息和业绩。一级成员是直接通过您的邀请加入的分销员，二级成员是您的一级成员邀请的分销员。',\r\n    showCancel: false\r\n  });\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.team-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: 30rpx;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\r\n  color: #fff;\r\n  padding: 88rpx 32rpx 30rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 72rpx;\r\n  height: 72rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 24rpx;\r\n  height: 24rpx;\r\n  border-left: 4rpx solid #fff;\r\n  border-bottom: 4rpx solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  letter-spacing: 1rpx;\r\n}\r\n\r\n.navbar-right {\r\n  width: 72rpx;\r\n  height: 72rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.help-icon {\r\n  width: 48rpx;\r\n  height: 48rpx;\r\n  border-radius: 24rpx;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 团队统计卡片 */\r\n.stats-card {\r\n  margin: 30rpx;\r\n  background: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.stats-header {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.stats-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.stats-content {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.stats-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.item-value {\r\n  font-size: 40rpx;\r\n  font-weight: 600;\r\n  color: #6B0FBE;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.item-label {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n}\r\n\r\n.level-stats {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  padding-top: 30rpx;\r\n  border-top: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.level-item {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.level-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  margin-right: 16rpx;\r\n  border-radius: 40rpx;\r\n}\r\n\r\n.level-icon.level1 {\r\n  background-color: #6B0FBE;\r\n}\r\n\r\n.level-icon.level2 {\r\n  background-color: #409EFF;\r\n}\r\n\r\n.level-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.level-name {\r\n  font-size: 26rpx;\r\n  color: #333;\r\n  margin-bottom: 4rpx;\r\n}\r\n\r\n.level-count {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n}\r\n\r\n/* 筛选栏 */\r\n.filter-bar {\r\n  display: flex;\r\n  background: #FFFFFF;\r\n  padding: 0 30rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.tab-item {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 80rpx;\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  position: relative;\r\n}\r\n\r\n.tab-item.active {\r\n  color: #6B0FBE;\r\n  font-weight: 600;\r\n}\r\n\r\n.tab-item.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 40rpx;\r\n  height: 4rpx;\r\n  background-color: #6B0FBE;\r\n  border-radius: 2rpx;\r\n}\r\n\r\n/* 团队成员列表 */\r\n.member-list {\r\n  margin: 0 30rpx;\r\n}\r\n\r\n.member-item {\r\n  background: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  margin-bottom: 20rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.member-info {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.member-avatar {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  border-radius: 40rpx;\r\n  margin-right: 20rpx;\r\n  border: 2rpx solid #f0f0f0;\r\n}\r\n\r\n.member-details {\r\n  flex: 1;\r\n}\r\n\r\n.member-name {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.member-meta {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.member-level {\r\n  font-size: 24rpx;\r\n  color: #6B0FBE;\r\n  background-color: rgba(107, 15, 190, 0.1);\r\n  padding: 4rpx 12rpx;\r\n  border-radius: 20rpx;\r\n  margin-right: 16rpx;\r\n}\r\n\r\n.member-time {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.member-stats {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  padding-top: 20rpx;\r\n  border-top: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.stat-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 4rpx;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 100rpx 0;\r\n}\r\n\r\n.empty-image {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.invite-btn {\r\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 40rpx;\r\n  font-size: 28rpx;\r\n  padding: 12rpx 60rpx;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 加载更多 */\r\n.load-more {\r\n  text-align: center;\r\n  padding: 30rpx 0;\r\n  font-size: 26rpx;\r\n  color: #666;\r\n}\r\n\r\n/* 卡片通用样式 */\r\n.rules-card,\r\n.tips-card {\r\n  margin: 30rpx;\r\n  background: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.card-header {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.card-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n/* 规则和提示 */\r\n.rules-content,\r\n.tips-content {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.rule-item,\r\n.tip-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.rule-item:last-child,\r\n.tip-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.rule-icon,\r\n.tip-icon {\r\n  width: 16rpx;\r\n  height: 16rpx;\r\n  border-radius: 40rpx;\r\n  background-color: #6B0FBE;\r\n  margin-right: 16rpx;\r\n  margin-top: 10rpx;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.rule-text,\r\n.tip-text {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  line-height: 1.6;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/distribution/pages/team.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "onMounted", "loadMore", "distributionService", "uni", "MiniProgramPage"], "mappings": ";;;;;;;AAwKA,UAAM,OAAO;AAAA,MACX,EAAE,MAAM,QAAQ,OAAO,MAAO;AAAA,MAC9B,EAAE,MAAM,QAAQ,OAAO,EAAG;AAAA,MAC1B,EAAE,MAAM,QAAQ,OAAO,EAAG;AAAA,IAC5B;AAGA,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAG3B,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAG1B,UAAM,cAAcC,cAAAA,SAAS;AAAA,MAC3B,aAAa;AAAA,MACb,aAAa;AAAA,MACb,mBAAmB;AAAA,IACrB,CAAC;AAGD,UAAM,kBAAkBA,cAAAA,SAAS;AAAA,MAC/B,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV,CAAC;AAGD,UAAM,aAAaA,cAAAA,SAAS;AAAA,MAC1B,MAAM;AAAA,MACN,UAAU;AAAA,MACV,OAAO;AAAA,MACP,YAAY;AAAA,IACd,CAAC;AAGD,UAAM,cAAcD,cAAAA,IAAI,KAAK;AAG7B,UAAM,UAAUA,cAAAA,IAAI,KAAK;AAGzBE,kBAAAA,UAAU,YAAY;AAEpB,YAAM,eAAc;AAAA,IACtB,CAAC;AAGD,UAAM,iBAAiB,OAAOC,YAAW,UAAU;AACjD,UAAI,QAAQ;AAAO;AAEnB,UAAI;AACF,gBAAQ,QAAQ;AAEhB,cAAM,OAAOA,YAAW,WAAW,OAAO,IAAI;AAC9C,cAAM,QAAQ,UAAU,UAAU,QAAQ,SAAY,UAAU;AAEhE,cAAM,SAAS,MAAMC,0BAAmB,oBAAC,eAAe;AAAA,UACtD;AAAA,UACA,UAAU,WAAW;AAAA,UACrB;AAAA,QACN,CAAK;AAED,YAAI,QAAQ;AAEV,cAAID,WAAU;AACZ,wBAAY,QAAQ,CAAC,GAAG,YAAY,OAAO,GAAG,OAAO,IAAI;AAAA,UACjE,OAAa;AACL,wBAAY,QAAQ,OAAO;AAAA,UAC5B;AAGD,qBAAW,OAAO;AAClB,qBAAW,QAAQ,OAAO,WAAW;AACrC,qBAAW,aAAa,OAAO,WAAW;AAG1C,sBAAY,QAAQ,WAAW,OAAO,WAAW;AAGjD,cAAI,OAAO,SAAS;AAClB,mBAAO,OAAO,aAAa,OAAO,OAAO;AAAA,UAC1C;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdE,sBAAA,MAAA,MAAA,SAAA,kDAAc,YAAY,KAAK;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,UAAY;AACR,gBAAQ,QAAQ;AAAA,MACjB;AAAA,IACH;AAGA,UAAM,YAAY,CAAC,QAAQ;AACzB,UAAI,UAAU,UAAU;AAAK;AAE7B,gBAAU,QAAQ;AAClB;IACF;AAGA,UAAM,WAAW,MAAM;AACrB,UAAI,YAAY,SAAS,CAAC,QAAQ,OAAO;AACvC,uBAAe,IAAI;AAAA,MACpB;AAAA,IACH;AAGA,UAAM,mBAAmB,CAAC,WAAW;AACnC,aAAOD,0BAAmB,oBAAC,iBAAiB,MAAM;AAAA,IACpD;AAGA,UAAM,aAAa,CAAC,SAAS;AAC3B,UAAI,CAAC;AAAM,eAAO;AAElB,YAAM,OAAO,IAAI,KAAK,IAAI;AAC1B,aAAO,GAAG,KAAK,YAAW,CAAE,IAAI,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,KAAK,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,IACzH;AAGA,UAAM,aAAa,CAAC,QAAQ;AAC1BC,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IACxB;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MAChB,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjTA,GAAG,WAAWC,SAAe;"}