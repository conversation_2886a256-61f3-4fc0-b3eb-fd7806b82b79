<template>
  <view class="coupon-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @tap="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">优惠券管理</text>
      <view class="navbar-right">
        <view class="help-icon">?</view>
      </view>
    </view>
    
    <!-- 顶部操作区 -->
    <view class="top-actions">
      <CreateButton text="创建优惠券" theme="coupon" @click="createNewCoupon" />
    </view>
    
    <!-- 数据概览 -->
    <view class="overview-section">
      <view class="overview-cards">
        <view class="overview-card">
          <text class="card-value">{{statistics.total}}</text>
          <text class="card-label">优惠券总数</text>
        </view>
        <view class="overview-card">
          <text class="card-value">{{statistics.active}}</text>
          <text class="card-label">进行中</text>
        </view>
        <view class="overview-card">
          <text class="card-value">{{statistics.used}}</text>
          <text class="card-label">已使用</text>
        </view>
        <view class="overview-card">
          <text class="card-value">{{statistics.conversion}}%</text>
          <text class="card-label">转化率</text>
        </view>
      </view>
    </view>
    
    <!-- 操作栏 -->
    <view class="action-bar">
      <view class="search-box">
        <view class="search-icon">
          <view class="icon-svg">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8"></circle>
              <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            </svg>
          </view>
        </view>
        <input type="text" class="search-input" placeholder="搜索优惠券" v-model="searchKeyword" @input="onSearch" />
      </view>
      
      <view class="filter-btn" @tap="showFilterOptions">
        <view class="btn-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
          </svg>
        </view>
        <text class="btn-text">筛选</text>
      </view>
      
      <view class="sort-btn" @tap="showSortOptions">
        <view class="btn-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="4" y1="9" x2="20" y2="9"></line>
            <line x1="4" y1="15" x2="20" y2="15"></line>
            <line x1="10" y1="3" x2="8" y2="21"></line>
            <line x1="16" y1="3" x2="14" y2="21"></line>
          </svg>
        </view>
        <text class="btn-text">排序</text>
      </view>
    </view>
    
    <!-- 优惠券列表 -->
    <view class="coupon-list">
      <view 
        class="coupon-card" 
        v-for="(item, index) in displayCoupons" 
        :key="index"
        @tap="viewCouponDetail(item)"
        :class="{'hover': hoveredCoupon === item.id}"
        @mouseenter="setHoveredCoupon(item.id)"
        @mouseleave="clearHoveredCoupon()">
        <view class="coupon-header">
          <text class="coupon-title">{{item.title}}</text>
          <view class="coupon-status" :class="'status-'+item.status">{{item.statusText}}</view>
        </view>
        
        <view class="coupon-value">
          <text class="discount-symbol">¥</text>
          <text class="discount-amount">{{item.value}}</text>
          <text class="discount-condition">满{{item.minSpend}}元可用</text>
        </view>
        
        <view class="coupon-info">
          <view class="info-item">
            <text class="info-label">有效期至:</text>
            <text class="info-value">{{item.expireDate}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">使用次数:</text>
            <text class="info-value">{{item.usedCount}}/{{item.totalCount}}</text>
          </view>
        </view>
        
        <view class="coupon-actions">
          <view class="action-btn edit" @tap.stop="editCoupon(item)">
            <view class="btn-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path>
              </svg>
            </view>
            <text class="btn-text">编辑</text>
          </view>
          
          <view 
            class="action-btn" 
            :class="item.status === 'active' ? 'pause' : 'activate'"
            @tap.stop="toggleCouponStatus(item)">
            <view class="btn-icon">
              <svg v-if="item.status === 'active'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="6" y="4" width="4" height="16"></rect>
                <rect x="14" y="4" width="4" height="16"></rect>
              </svg>
              <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polygon points="5 3 19 12 5 21 5 3"></polygon>
              </svg>
            </view>
            <text class="btn-text">{{item.status === 'active' ? '暂停' : '启用'}}</text>
          </view>
          
          <view class="action-btn delete" @tap.stop="confirmDeleteCoupon(item)">
            <view class="btn-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="3 6 5 6 21 6"></polyline>
                <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                <line x1="10" y1="11" x2="10" y2="17"></line>
                <line x1="14" y1="11" x2="14" y2="17"></line>
              </svg>
            </view>
            <text class="btn-text">删除</text>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="displayCoupons.length === 0">
        <view class="empty-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="2" y="4" width="20" height="16" rx="2" ry="2"></rect>
            <path d="M12 8v8"></path>
            <path d="M8 12h8"></path>
          </svg>
        </view>
        <text class="empty-text">暂无优惠券</text>
        <text class="empty-subtext">点击下方按钮创建新的优惠券</text>
      </view>
    </view>
    
    <!-- 创建优惠券按钮 -->
    <!-- 悬浮按钮已移除，改用顶部CreateButton组件 -->
  </view>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue';
import CreateButton from '/subPackages/merchant-admin-marketing/components/CreateButton.vue';

export default {
  components: {
    CreateButton
  },
  setup() {
    // 响应式状态
    const statistics = reactive({
      total: 35,
      active: 12,
      used: 258,
      conversion: 46.8
    });
    
    const coupons = ref([
      {
        id: 1,
        title: '新客专享优惠',
        status: 'active',
        statusText: '进行中',
        value: 10,
        minSpend: 100,
        expireDate: '2023-05-15',
        usedCount: 234,
        totalCount: 500,
        conversionRate: 46.8
      },
      {
        id: 2,
        title: '满减优惠券',
        status: 'active',
        statusText: '进行中',
        value: 20,
        minSpend: 200,
        expireDate: '2023-05-20',
        usedCount: 156,
        totalCount: 300,
        conversionRate: 52.0
      },
      {
        id: 3,
        title: '节日特别券',
        status: 'upcoming',
        statusText: '未开始',
        value: 50,
        minSpend: 300,
        expireDate: '2023-06-10',
        usedCount: 0,
        totalCount: 200,
        conversionRate: 0
      },
      {
        id: 4,
        title: '会员专享券',
        status: 'active',
        statusText: '进行中',
        value: 15,
        minSpend: 150,
        expireDate: '2023-05-25',
        usedCount: 89,
        totalCount: 200,
        conversionRate: 44.5
      },
      {
        id: 5,
        title: '周末特惠券',
        status: 'expired',
        statusText: '已结束',
        value: 30,
        minSpend: 250,
        expireDate: '2023-04-30',
        usedCount: 178,
        totalCount: 300,
        conversionRate: 59.3
      }
    ]);
    
    const searchKeyword = ref('');
    const hoveredCoupon = ref(null);
    
    // 计算属性
    const displayCoupons = computed(() => {
      if (!searchKeyword.value) {
        return coupons.value;
      }
      
      const keyword = searchKeyword.value.toLowerCase();
      return coupons.value.filter(coupon => {
        return coupon.title.toLowerCase().includes(keyword);
      });
    });
    
    // 方法
    function goBack() {
      uni.navigateBack();
    }
    
    function onSearch(e) {
      // 实时搜索处理
    }
    
    function showFilterOptions() {
      uni.showActionSheet({
        itemList: ['全部', '进行中', '未开始', '已结束'],
        success: (res) => {
          // 处理筛选结果
        }
      });
    }
    
    function showSortOptions() {
      uni.showActionSheet({
        itemList: ['默认排序', '面额从高到低', '面额从低到高', '使用率从高到低', '使用率从低到高'],
        success: (res) => {
          // 处理排序结果
        }
      });
    }
    
    function viewCouponDetail(coupon) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/coupon/detail?id=${coupon.id}`,
        animationType: 'slide-in-right'
      });
    }
    
    function editCoupon(coupon) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/coupon/edit?id=${coupon.id}`,
        animationType: 'slide-in-right'
      });
    }
    
    function toggleCouponStatus(coupon) {
      const isActive = coupon.status === 'active';
      const newStatus = isActive ? 'paused' : 'active';
      const statusText = isActive ? '已暂停' : '进行中';
      
      // 在实际应用中，这里应该调用API更新状态
      
      // 本地状态更新示例
      coupon.status = newStatus;
      coupon.statusText = statusText;
      
      uni.showToast({
        title: isActive ? '已暂停优惠券' : '已启用优惠券',
        icon: 'success'
      });
    }
    
    function confirmDeleteCoupon(coupon) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除"${coupon.title}"吗？此操作无法撤销。`,
        confirmColor: '#FF3B30',
        success: (res) => {
          if (res.confirm) {
            deleteCoupon(coupon);
          }
        }
      });
    }
    
    function deleteCoupon(coupon) {
      // 在实际应用中，这里应该调用API删除
      
      // 本地状态更新示例
      const index = coupons.value.findIndex(item => item.id === coupon.id);
      if (index > -1) {
        coupons.value.splice(index, 1);
      }
      
      uni.showToast({
        title: '已删除优惠券',
        icon: 'success'
      });
    }
    
    function createNewCoupon() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/coupon/create',
        animationType: 'slide-in-right'
      });
    }
    
    function setHoveredCoupon(couponId) {
      hoveredCoupon.value = couponId;
    }
    
    function clearHoveredCoupon() {
      hoveredCoupon.value = null;
    }
    
    onMounted(() => {
      // 可以在这里加载数据
    });
    
    return {
      statistics,
      displayCoupons,
      searchKeyword,
      hoveredCoupon,
      goBack,
      onSearch,
      showFilterOptions,
      showSortOptions,
      viewCouponDetail,
      editCoupon,
      toggleCouponStatus,
      confirmDeleteCoupon,
      createNewCoupon,
      setHoveredCoupon,
      clearHoveredCoupon
    };
  }
}
</script>

<style lang="scss" scoped>
.coupon-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF9966, #FF5E62);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(255, 94, 98, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 概览部分样式 */
.overview-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.overview-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px;
}

.overview-card {
  width: 25%;
  padding: 0 5px;
  box-sizing: border-box;
}

.card-value {
  display: block;
  font-size: 18px;
  font-weight: bold;
  color: #FF5E62;
  text-align: center;
  margin-bottom: 5px;
}

.card-label {
  display: block;
  font-size: 12px;
  color: #666;
  text-align: center;
}

/* 操作栏样式 */
.action-bar {
  margin: 0 15px 15px;
  display: flex;
  align-items: center;
}

.search-box {
  flex: 1;
  height: 36px;
  background: #fff;
  border-radius: 18px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.search-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  color: #999;
}

.icon-svg {
  width: 16px;
  height: 16px;
}

.search-input {
  flex: 1;
  height: 36px;
  font-size: 14px;
  color: #333;
  border: none;
  background: transparent;
}

.filter-btn, .sort-btn {
  height: 36px;
  padding: 0 12px;
  background: #fff;
  border-radius: 18px;
  margin-left: 10px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.btn-icon {
  width: 16px;
  height: 16px;
  margin-right: 5px;
  color: #666;
}

.btn-text {
  font-size: 14px;
  color: #666;
}

/* 优惠券列表样式 */
.coupon-list {
  padding: 0 15px;
  margin-bottom: 80px; /* 为悬浮按钮留出空间 */
}

.coupon-card {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
}

.coupon-card.hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.coupon-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.coupon-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.coupon-status {
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 12px;
  color: white;
}

.status-active {
  background: #34C759;
}

.status-expired {
  background: #8E8E93;
}

.status-upcoming {
  background: #FF9500;
}

.status-paused {
  background: #FF9500;
}

.coupon-value {
  display: flex;
  align-items: baseline;
  margin-bottom: 12px;
}

.discount-symbol {
  font-size: 16px;
  color: #FF5E62;
  margin-right: 2px;
}

.discount-amount {
  font-size: 24px;
  font-weight: bold;
  color: #FF5E62;
  margin-right: 8px;
}

.discount-condition {
  font-size: 12px;
  color: #666;
}

.coupon-info {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 15px;
}

.info-item {
  width: 50%;
  margin-bottom: 5px;
  display: flex;
}

.info-label {
  font-size: 12px;
  color: #999;
  margin-right: 5px;
}

.info-value {
  font-size: 12px;
  color: #333;
  font-weight: 500;
}

.coupon-actions {
  display: flex;
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 5px 0;
}

.action-btn .btn-icon {
  width: 20px;
  height: 20px;
  margin-right: 0;
  margin-bottom: 3px;
}

.action-btn .btn-text {
  font-size: 12px;
}

.action-btn.edit {
  color: #007AFF;
}

.action-btn.pause {
  color: #FF9500;
}

.action-btn.activate {
  color: #34C759;
}

.action-btn.delete {
  color: #FF3B30;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.empty-icon {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background: rgba(255, 94, 98, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  color: #FF5E62;
}

.empty-text {
  font-size: 16px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.empty-subtext {
  font-size: 14px;
  color: #999;
}

/* 顶部操作区样式 */
.top-actions {
  padding: 15px;
  display: flex;
  justify-content: flex-end;
  background-color: #fff;
}

/* 悬浮操作按钮样式已移除，使用CreateButton组件 */
</style> 