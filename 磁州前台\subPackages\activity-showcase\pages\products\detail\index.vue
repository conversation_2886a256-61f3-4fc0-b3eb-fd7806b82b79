<template>
  <view class="product-detail-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M19 12H5M12 19l-7-7 7-7" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        <view class="navbar-title">商品详情</view>
        <view class="navbar-right">
          <view class="share-btn" @click="showShare">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <path d="M4 12v8a2 2 0 002 2h12a2 2 0 002-2v-8M16 6l-4-4-4 4M12 2v13" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 商品轮播图 -->
      <swiper class="product-swiper" circular indicator-dots autoplay>
        <swiper-item v-for="(image, index) in product.images" :key="index">
          <image class="swiper-image" :src="image" mode="aspectFill"></image>
        </swiper-item>
      </swiper>
      
      <!-- 商品价格信息 -->
      <view class="price-section">
        <view class="price-row">
          <view class="current-price">¥{{ product.price }}</view>
          <view class="original-price">¥{{ product.originalPrice }}</view>
          <view class="discount-tag">{{ getDiscountPercent(product.price, product.originalPrice) }}折</view>
        </view>
        <view class="sales-info">已售 {{ product.soldCount }} | 库存 {{ product.stock }}</view>
      </view>
      
      <!-- 商品标题信息 -->
      <view class="title-section">
        <view class="product-title">{{ product.title }}</view>
        <view class="product-tags">
          <view class="tag" v-for="(label, index) in product.labels" :key="index" :class="label.type">
            {{ label.text }}
          </view>
        </view>
        <view class="product-desc">{{ product.description }}</view>
      </view>
      
      <!-- 店铺信息 -->
      <view class="shop-section" @click="viewShop">
        <image class="shop-logo" :src="product.shopLogo" mode="aspectFill"></image>
        <view class="shop-info">
          <view class="shop-name">{{ product.shopName }}</view>
          <view class="shop-rating">
            <view class="stars">
              <view class="star-filled" v-for="i in Math.floor(product.shopRating || 5)" :key="i"></view>
              <view class="star-empty" v-for="i in 5 - Math.floor(product.shopRating || 5)" :key="i + 5"></view>
            </view>
            <text>{{ product.shopRating || 5.0 }}</text>
          </view>
        </view>
        <view class="shop-enter">
          <text>进店</text>
          <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
            <path d="M9 18l6-6-6-6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
      </view>
      
      <!-- 规格选择 -->
      <view class="specs-section" @click="showSpecsPopup">
        <view class="section-title">规格</view>
        <view class="specs-preview">
          <text>{{ selectedSpecs || '请选择规格' }}</text>
          <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
            <path d="M9 18l6-6-6-6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
      </view>
      
      <!-- 评价信息 -->
      <view class="reviews-section" @click="viewReviews">
        <view class="section-header">
          <view class="section-title">商品评价 ({{ product.reviewCount || 0 }})</view>
          <view class="section-more">
            <text>查看全部</text>
            <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
              <path d="M9 18l6-6-6-6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
        
        <view class="review-preview" v-if="product.reviewPreview">
          <view class="review-user">
            <image class="user-avatar" :src="product.reviewPreview.userAvatar" mode="aspectFill"></image>
            <text class="user-name">{{ product.reviewPreview.userName }}</text>
          </view>
          <view class="review-content">{{ product.reviewPreview.content }}</view>
          <view class="review-images" v-if="product.reviewPreview.images && product.reviewPreview.images.length">
            <image 
              v-for="(img, index) in product.reviewPreview.images.slice(0, 3)" 
              :key="index" 
              :src="img" 
              mode="aspectFill"
              class="review-image"
            ></image>
          </view>
        </view>
        <view class="no-reviews" v-else>
          <text>暂无评价</text>
        </view>
      </view>
      
      <!-- 商品详情 -->
      <view class="detail-section">
        <view class="section-title">商品详情</view>
        <rich-text :nodes="product.detailHtml || '暂无详情'"></rich-text>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
    
    <!-- 底部操作栏 -->
    <view class="action-bar">
      <view class="action-left">
        <view class="action-btn shop" @click="viewShop">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M3 9l2.45-4.9A2 2 0 017.24 3h9.52a2 2 0 011.8 1.1L21 9M3 9h18M3 9v10a2 2 0 002 2h14a2 2 0 002-2V9M12 13v5M8 13v5M16 13v5" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
          <text>店铺</text>
        </view>
        <view class="action-btn cart" @click="viewCart">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M9 20a1 1 0 100 2 1 1 0 000-2zM20 20a1 1 0 100 2 1 1 0 000-2z" fill="#333333" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M1 1h4l2.68 13.39a2 2 0 002 1.61h9.72a2 2 0 002-1.61L23 6H6" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
          <text>购物车</text>
          <view class="badge" v-if="cartCount > 0">{{ cartCount > 99 ? '99+' : cartCount }}</view>
        </view>
      </view>
      <view class="action-right">
        <button class="add-to-cart-btn" @click="addToCart">加入购物车</button>
        <button class="buy-now-btn" @click="buyNow">立即购买</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';

// 商品ID
const productId = ref('');

// 购物车数量
const cartCount = ref(5);

// 选中的规格
const selectedSpecs = ref('');

// 商品数据
const product = reactive({
  id: '',
  title: '商品加载中...',
  price: 0,
  originalPrice: 0,
  stock: 0,
  soldCount: 0,
  description: '',
  images: [
    'https://via.placeholder.com/750x750',
    'https://via.placeholder.com/750x750',
    'https://via.placeholder.com/750x750'
  ],
  labels: [],
  shopName: '',
  shopLogo: 'https://via.placeholder.com/100',
  shopRating: 5.0,
  reviewCount: 0,
  reviewPreview: null,
  detailHtml: ''
});

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 显示分享菜单
const showShare = () => {
  uni.showShareMenu({
    withShareTicket: true,
    success() {
      uni.showToast({
        title: '打开分享菜单成功',
        icon: 'none'
      });
    }
  });
};

// 计算折扣百分比
const getDiscountPercent = (currentPrice, originalPrice) => {
  if (!originalPrice || originalPrice <= 0) return 10;
  const discount = (currentPrice / originalPrice * 10).toFixed(1);
  return discount;
};

// 查看店铺
const viewShop = () => {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/shops/detail?id=${product.shopId}`
  });
};

// 查看购物车
const viewCart = () => {
  uni.navigateTo({
    url: '/subPackages/activity-showcase/pages/cart/index'
  });
};

// 查看评价
const viewReviews = () => {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/products/reviews?id=${product.id}`
  });
};

// 显示规格选择弹窗
const showSpecsPopup = () => {
  uni.showToast({
    title: '规格选择功能开发中',
    icon: 'none'
  });
};

// 加入购物车
const addToCart = () => {
  if (!selectedSpecs.value) {
    showSpecsPopup();
    return;
  }
  
  uni.showToast({
    title: '已加入购物车',
    icon: 'success'
  });
  
  cartCount.value++;
};

// 立即购买
const buyNow = () => {
  if (!selectedSpecs.value) {
    showSpecsPopup();
    return;
  }
  
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/orders/confirm?productId=${product.id}&specs=${encodeURIComponent(selectedSpecs.value)}`
  });
};

// 获取商品详情
const getProductDetail = (id) => {
  // 模拟获取商品数据
  setTimeout(() => {
    Object.assign(product, {
      id: id,
      title: 'iPhone 14 Pro 深空黑 256G',
      price: 7999,
      originalPrice: 8999,
      stock: 999,
      soldCount: 235,
      description: '搭载 A16 仿生芯片，4800万像素主摄，超瓷晶面板，动态岛设计',
      images: [
        'https://via.placeholder.com/750x750',
        'https://via.placeholder.com/750x750',
        'https://via.placeholder.com/750x750'
      ],
      labels: [
        { type: 'discount', text: '满3000减300' },
        { type: 'new', text: '新品' }
      ],
      shopId: '1',
      shopName: 'Apple授权专卖店',
      shopLogo: 'https://via.placeholder.com/100',
      shopRating: 4.9,
      reviewCount: 235,
      reviewPreview: {
        userAvatar: 'https://via.placeholder.com/100',
        userName: '用户***123',
        content: '手机质量很好，拍照效果出色，电池续航也不错，总体来说非常满意！',
        images: [
          'https://via.placeholder.com/200',
          'https://via.placeholder.com/200',
          'https://via.placeholder.com/200'
        ]
      },
      detailHtml: '<div style="text-align:center;"><img src="https://via.placeholder.com/750x500" /><p>商品详情内容</p></div>'
    });
  }, 500);
};

onMounted(() => {
  const query = uni.getSystemInfoSync().platform === 'devtools' 
    ? { id: '1' } // 开发环境模拟数据
    : uni.$u.route.query;
    
  if (query.id) {
    productId.value = query.id;
    getProductDetail(query.id);
  }
});
</script>

<style lang="scss" scoped>
.product-detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F2F2F7;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
  
  .navbar-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 4px 6px rgba(255,59,105,0.15);
  }
  
  .navbar-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 30rpx;
    padding-top: var(--status-bar-height, 25px);
    box-sizing: border-box;
  }
  
  .navbar-left, .navbar-right {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80rpx;
    height: 80rpx;
  }
  
  .navbar-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #FFFFFF;
    letter-spacing: 0.5px;
  }
  
  .icon {
    width: 48rpx;
    height: 48rpx;
  }
}

/* 内容区域 */
.content-scroll {
  flex: 1;
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
  margin-bottom: 100rpx; /* 底部操作栏高度 */
}

/* 商品轮播图 */
.product-swiper {
  width: 100%;
  height: 750rpx;
  
  .swiper-image {
    width: 100%;
    height: 100%;
  }
}

/* 价格信息 */
.price-section {
  background-color: #FFFFFF;
  padding: 30rpx;
  
  .price-row {
    display: flex;
    align-items: center;
    
    .current-price {
      font-size: 48rpx;
      font-weight: 600;
      color: #FF3B69;
    }
    
    .original-price {
      font-size: 28rpx;
      color: #999999;
      text-decoration: line-through;
      margin-left: 20rpx;
    }
    
    .discount-tag {
      margin-left: 20rpx;
      background-color: #FFF0F5;
      color: #FF3B69;
      font-size: 24rpx;
      padding: 4rpx 12rpx;
      border-radius: 8rpx;
    }
  }
  
  .sales-info {
    margin-top: 10rpx;
    font-size: 24rpx;
    color: #999999;
  }
}

/* 标题信息 */
.title-section {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-top: 2rpx;
  
  .product-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
    line-height: 1.4;
  }
  
  .product-tags {
    display: flex;
    flex-wrap: wrap;
    margin-top: 20rpx;
    
    .tag {
      margin-right: 10rpx;
      margin-bottom: 10rpx;
      padding: 4rpx 12rpx;
      border-radius: 8rpx;
      font-size: 22rpx;
      
      &.discount {
        background-color: #FFF0F5;
        color: #FF3B69;
      }
      
      &.new {
        background-color: #E8F5FF;
        color: #007AFF;
      }
      
      &.hot {
        background-color: #FFF4E8;
        color: #FF9500;
      }
      
      &.coupon {
        background-color: #F0FFF0;
        color: #34C759;
      }
    }
  }
  
  .product-desc {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #666666;
    line-height: 1.5;
  }
}

/* 店铺信息 */
.shop-section {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-top: 20rpx;
  display: flex;
  align-items: center;
  
  .shop-logo {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
  }
  
  .shop-info {
    flex: 1;
    margin-left: 20rpx;
    
    .shop-name {
      font-size: 28rpx;
      font-weight: 500;
      color: #333333;
    }
    
    .shop-rating {
      display: flex;
      align-items: center;
      margin-top: 6rpx;
      
      .stars {
        display: flex;
        margin-right: 10rpx;
        
        .star-filled, .star-empty {
          width: 24rpx;
          height: 24rpx;
          margin-right: 4rpx;
          background-size: contain;
          background-repeat: no-repeat;
        }
        
        .star-filled {
          background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF9500'%3E%3Cpath d='M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z'/%3E%3C/svg%3E");
        }
        
        .star-empty {
          background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23E0E0E0'%3E%3Cpath d='M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z'/%3E%3C/svg%3E");
        }
      }
      
      text {
        font-size: 24rpx;
        color: #FF9500;
      }
    }
  }
  
  .shop-enter {
    display: flex;
    align-items: center;
    padding: 10rpx 20rpx;
    border: 1rpx solid #EEEEEE;
    border-radius: 30rpx;
    
    text {
      font-size: 24rpx;
      color: #666666;
    }
    
    .icon {
      margin-left: 4rpx;
    }
  }
}

/* 规格选择 */
.specs-section {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-top: 2rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .section-title {
    font-size: 28rpx;
    color: #333333;
  }
  
  .specs-preview {
    display: flex;
    align-items: center;
    
    text {
      font-size: 28rpx;
      color: #666666;
    }
    
    .icon {
      margin-left: 4rpx;
    }
  }
}

/* 评价信息 */
.reviews-section {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-top: 20rpx;
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .section-title {
      font-size: 28rpx;
      font-weight: 500;
      color: #333333;
    }
    
    .section-more {
      display: flex;
      align-items: center;
      
      text {
        font-size: 24rpx;
        color: #999999;
      }
      
      .icon {
        margin-left: 4rpx;
      }
    }
  }
  
  .review-preview {
    .review-user {
      display: flex;
      align-items: center;
      margin-bottom: 10rpx;
      
      .user-avatar {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
      }
      
      .user-name {
        margin-left: 10rpx;
        font-size: 26rpx;
        color: #333333;
      }
    }
    
    .review-content {
      font-size: 28rpx;
      color: #333333;
      line-height: 1.5;
      margin-bottom: 20rpx;
    }
    
    .review-images {
      display: flex;
      
      .review-image {
        width: 160rpx;
        height: 160rpx;
        margin-right: 10rpx;
        border-radius: 8rpx;
      }
    }
  }
  
  .no-reviews {
    padding: 30rpx 0;
    text-align: center;
    color: #999999;
    font-size: 28rpx;
  }
}

/* 商品详情 */
.detail-section {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-top: 20rpx;
  
  .section-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #333333;
    margin-bottom: 20rpx;
  }
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 34px; /* iOS 安全区域高度 */
}

/* 底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  padding-bottom: env(safe-area-inset-bottom); /* 适配 iPhone X 以上的底部安全区域 */
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 99;
  
  .action-left {
    display: flex;
    padding: 0 20rpx;
    
    .action-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100rpx;
      position: relative;
      
      .icon {
        width: 48rpx;
        height: 48rpx;
      }
      
      text {
        font-size: 22rpx;
        color: #666666;
        margin-top: 4rpx;
      }
      
      .badge {
        position: absolute;
        top: -6rpx;
        right: 10rpx;
        min-width: 32rpx;
        height: 32rpx;
        border-radius: 16rpx;
        background: #FF3B30;
        color: #FFFFFF;
        font-size: 20rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 6rpx;
        box-sizing: border-box;
      }
    }
  }
  
  .action-right {
    flex: 1;
    display: flex;
    padding: 0 20rpx;
    
    button {
      flex: 1;
      height: 80rpx;
      border-radius: 40rpx;
      font-size: 28rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 10rpx;
      border: none;
    }
    
    .add-to-cart-btn {
      background: linear-gradient(135deg, #FFC107 0%, #FF9800 100%);
      color: #FFFFFF;
    }
    
    .buy-now-btn {
      background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
      color: #FFFFFF;
    }
  }
}
</style> 