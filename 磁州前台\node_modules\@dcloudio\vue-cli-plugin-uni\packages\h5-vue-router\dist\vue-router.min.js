/*!
  * vue-router v3.0.1
  * (c) 2020 Evan You
  * @license MIT
  */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.VueRouter=e()}(this,function(){"use strict";function l(t){return-1<Object.prototype.toString.call(t).indexOf("Error")}function R(t,e){for(var r in e)t[r]=e[r];return t}var i={name:"RouterView",functional:!0,props:{name:{type:String,default:"default"}},render:function(t,e){var r=e.props,n=e.children,o=e.parent,i=e.data;i.routerView=!0;for(var a=o.$createElement,s=r.name,u=o.$route,c=o._routerViewCache||(o._routerViewCache={}),p=0,f=!1;o&&o._routerRoot!==o;)o.$vnode&&o.$vnode.data.routerView&&p++,o._inactive&&(f=!0),o=o.$parent;if(i.routerViewDepth=p,f)return a(c[s],i,n);var h=u.matched[p];if(!h)return c[s]=null,a();var l=c[s]=h.components[s];i.registerRouteInstance=function(t,e){var r=h.instances[s];(e&&r!==t||!e&&r===t)&&(h.instances[s]=e)},(i.hook||(i.hook={})).prepatch=function(t,e){h.instances[s]=e.componentInstance};var d=i.props=function(t,e){switch(typeof e){case"undefined":return;case"object":return e;case"function":return e(t);case"boolean":return e?t.params:void 0}}(u,h.props&&h.props[s]);if(d){d=i.props=R({},d);var y=i.attrs=i.attrs||{};for(var v in d)l.props&&v in l.props||(y[v]=d[v],delete d[v])}return a(l,i,n)}};var e=/[!'()*]/g,r=function(t){return"%"+t.charCodeAt(0).toString(16)},n=/%2C/g,o=function(t){return encodeURIComponent(t).replace(e,r).replace(n,",")},a=decodeURIComponent;function h(t,e,r){void 0===e&&(e={});var n,o=r||s;try{n=o(t||"")}catch(t){n={}}for(var i in e)n[i]=e[i];return n}function s(t){var o={};return(t=t.trim().replace(/^(\?|#|&)/,""))&&t.split("&").forEach(function(t){var e=t.replace(/\+/g," ").split("="),r=a(e.shift()),n=0<e.length?a(e.join("=")):null;void 0===o[r]?o[r]=n:Array.isArray(o[r])?o[r].push(n):o[r]=[o[r],n]}),o}function u(n){var t=n?Object.keys(n).map(function(e){var t=n[e];if(void 0===t)return"";if(null===t)return o(e);if(Array.isArray(t)){var r=[];return t.forEach(function(t){void 0!==t&&(null===t?r.push(o(e)):r.push(o(e)+"="+o(t)))}),r.join("&")}return o(e)+"="+o(t)}).filter(function(t){return 0<t.length}).join("&"):null;return t?"?"+t:""}var E=/\/?$/;function j(t,e,r,n){var o=n&&n.options.stringifyQuery,i=e.query||{};try{i=c(i)}catch(t){}var a={name:e.name||t&&t.name,meta:t&&t.meta||{},path:e.path||"/",hash:e.hash||"",type:e.type,query:i,params:e.params||{},fullPath:p(e,o),matched:t?function(t){var e=[];for(;t;)e.unshift(t),t=t.parent;return e}(t):[]};return r&&(a.redirectedFrom=p(r,o)),Object.freeze(a)}function c(t){if(Array.isArray(t))return t.map(c);if(t&&"object"==typeof t){var e={};for(var r in t)e[r]=c(t[r]);return e}return t}var O=j(null,{path:"/"});function p(t,e){var r=t.path,n=t.query;void 0===n&&(n={});var o=t.hash;return void 0===o&&(o=""),(r||"/")+(e||u)(n)+o}function C(n,o){if(void 0===n&&(n={}),void 0===o&&(o={}),!n||!o)return n===o;var t=Object.keys(n),e=Object.keys(o);return t.length===e.length&&t.every(function(t){var e=n[t],r=o[t];return"object"==typeof e&&"object"==typeof r?C(e,r):String(e)===String(r)})}var d,t=[String,Object],f=[String,Array],y={name:"RouterLink",props:{to:{type:t,required:!0},tag:{type:String,default:"a"},exact:Boolean,append:Boolean,replace:Boolean,activeClass:String,exactActiveClass:String,event:{type:f,default:"click"}},render:function(t){var e,r,n,o,i=this,a=this.$router,s=this.$route,u=a.resolve(this.to,s,this.append),c=u.location,p=u.route,f=u.href,h={},l=a.options.linkActiveClass,d=a.options.linkExactActiveClass,y=null==l?"router-link-active":l,v=null==d?"router-link-exact-active":d,m=null==this.activeClass?y:this.activeClass,g=null==this.exactActiveClass?v:this.exactActiveClass,_=c.path?j(null,c,null,a):p;h[g]=(e=s,(r=_)===O?e===r:!!r&&(e.path&&r.path?e.path.replace(E,"")===r.path.replace(E,"")&&e.hash===r.hash&&C(e.query,r.query):!(!e.name||!r.name)&&e.name===r.name&&e.hash===r.hash&&C(e.query,r.query)&&C(e.params,r.params))),h[m]=this.exact?h[g]:(o=_,0===(n=s).path.replace(E,"/").indexOf(o.path.replace(E,"/"))&&(!o.hash||n.hash===o.hash)&&function(t,e){for(var r in e)if(!(r in t))return!1;return!0}(n.query,o.query));var b=function(t){T(t)&&(i.replace?a.replace(c):a.push(c))},w={click:T};Array.isArray(this.event)?this.event.forEach(function(t){w[t]=b}):w[this.event]=b;var x={class:h};if("a"===this.tag)x.on=w,x.attrs={href:f};else{var k=function t(e){if(e)for(var r,n=0;n<e.length;n++){if("a"===(r=e[n]).tag)return r;if(r.children&&(r=t(r.children)))return r}}(this.$slots.default);if(k)k.isStatic=!1,(k.data=R({},k.data)).on=w,(k.data.attrs=R({},k.data.attrs)).href=f;else x.on=w}return t(this.tag,x,this.$slots.default)}};function T(t){if(!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey||t.defaultPrevented||void 0!==t.button&&0!==t.button)){if(t.currentTarget&&t.currentTarget.getAttribute){var e=t.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(e))return}return t.preventDefault&&t.preventDefault(),!0}}var v="undefined"!=typeof window;function m(t,e,r){var n=t.charAt(0);if("/"===n)return t;if("?"===n||"#"===n)return e+t;var o=e.split("/");r&&o[o.length-1]||o.pop();for(var i=t.replace(/^\//,"").split("/"),a=0;a<i.length;a++){var s=i[a];".."===s?o.pop():"."!==s&&o.push(s)}return""!==o[0]&&o.unshift(""),o.join("/")}function g(t){return t.replace(/\/\//g,"/")}var _=Array.isArray||function(t){return"[object Array]"==Object.prototype.toString.call(t)},b=B,w=$,x=function(t,e){return q($(t,e))},k=q,A=M,S=new RegExp(["(\\\\.)","([\\/.])?(?:(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?|(\\*))"].join("|"),"g");function $(t,e){for(var r,n,o=[],i=0,a=0,s="",u=e&&e.delimiter||"/";null!=(r=S.exec(t));){var c=r[0],p=r[1],f=r.index;if(s+=t.slice(a,f),a=f+c.length,p)s+=p[1];else{var h=t[a],l=r[2],d=r[3],y=r[4],v=r[5],m=r[6],g=r[7];s&&(o.push(s),s="");var _=null!=l&&null!=h&&h!==l,b="+"===m||"*"===m,w="?"===m||"*"===m,x=r[2]||u,k=y||v;o.push({name:d||i++,prefix:l||"",delimiter:x,optional:w,repeat:b,partial:_,asterisk:!!g,pattern:k?(n=k,n.replace(/([=!:$\/()])/g,"\\$1")):g?".*":"[^"+P(x)+"]+?"})}}return a<t.length&&(s+=t.substr(a)),s&&o.push(s),o}function L(t){return encodeURI(t).replace(/[\/?#]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()})}function q(p){for(var f=new Array(p.length),t=0;t<p.length;t++)"object"==typeof p[t]&&(f[t]=new RegExp("^(?:"+p[t].pattern+")$"));return function(t,e){for(var r="",n=t||{},o=(e||{}).pretty?L:encodeURIComponent,i=0;i<p.length;i++){var a=p[i];if("string"!=typeof a){var s,u=n[a.name];if(null==u){if(a.optional){a.partial&&(r+=a.prefix);continue}throw new TypeError('Expected "'+a.name+'" to be defined')}if(_(u)){if(!a.repeat)throw new TypeError('Expected "'+a.name+'" to not repeat, but received `'+JSON.stringify(u)+"`");if(0===u.length){if(a.optional)continue;throw new TypeError('Expected "'+a.name+'" to not be empty')}for(var c=0;c<u.length;c++){if(s=o(u[c]),!f[i].test(s))throw new TypeError('Expected all "'+a.name+'" to match "'+a.pattern+'", but received `'+JSON.stringify(s)+"`");r+=(0===c?a.prefix:a.delimiter)+s}}else{if(s=a.asterisk?encodeURI(u).replace(/[?#]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()}):o(u),!f[i].test(s))throw new TypeError('Expected "'+a.name+'" to match "'+a.pattern+'", but received "'+s+'"');r+=a.prefix+s}}else r+=a}return r}}function P(t){return t.replace(/([.+*?=^!:${}()[\]|\/\\])/g,"\\$1")}function U(t,e){return t.keys=e,t}function I(t){return t.sensitive?"":"i"}function M(t,e,r){_(e)||(r=e||r,e=[]);for(var n=(r=r||{}).strict,o=!1!==r.end,i="",a=0;a<t.length;a++){var s=t[a];if("string"==typeof s)i+=P(s);else{var u=P(s.prefix),c="(?:"+s.pattern+")";e.push(s),s.repeat&&(c+="(?:"+u+c+")*"),i+=c=s.optional?s.partial?u+"("+c+")?":"(?:"+u+"("+c+"))?":u+"("+c+")"}}var p=P(r.delimiter||"/"),f=i.slice(-p.length)===p;return n||(i=(f?i.slice(0,-p.length):i)+"(?:"+p+"(?=$))?"),i+=o?"$":n&&f?"":"(?="+p+"|$)",U(new RegExp("^"+i,I(r)),e)}function B(t,e,r){return _(e)||(r=e||r,e=[]),r=r||{},t instanceof RegExp?function(t,e){var r=t.source.match(/\((?!\?)/g);if(r)for(var n=0;n<r.length;n++)e.push({name:n,prefix:null,delimiter:null,optional:!1,repeat:!1,partial:!1,asterisk:!1,pattern:null});return U(t,e)}(t,e):_(t)?function(t,e,r){for(var n=[],o=0;o<t.length;o++)n.push(B(t[o],e,r).source);return U(new RegExp("(?:"+n.join("|")+")",I(r)),e)}(t,e,r):(n=e,M($(t,o=r),n,o));var n,o}b.parse=w,b.compile=x,b.tokensToFunction=k,b.tokensToRegExp=A;var H=Object.create(null);function V(t,e,r){try{return(H[t]||(H[t]=b.compile(t)))(e||{},{pretty:!0})}catch(t){return""}}function z(t,e,r,n){var o=e||[],i=r||Object.create(null),a=n||Object.create(null);t.forEach(function(t){!function r(n,o,i,a,s,u){var t=a.path;var e=a.name;var c=a.pathToRegexpOptions||{};var p=function(t,e,r){r||(t=t.replace(/\/$/,""));if("/"===t[0])return t;if(null==e)return t;return g(e.path+"/"+t)}(t,s,c.strict);"boolean"==typeof a.caseSensitive&&(c.sensitive=a.caseSensitive);var f={path:p,regex:(h=p,l=c,b(h,[],l)),components:a.components||{default:a.component},instances:{},name:e,parent:s,matchAs:u,redirect:a.redirect,beforeEnter:a.beforeEnter,meta:a.meta||{},props:null==a.props?{}:a.components?a.props:{default:a.props}};var h,l;a.children&&a.children.forEach(function(t){var e=u?g(u+"/"+t.path):void 0;r(n,o,i,t,f,e)});if(void 0!==a.alias){var d=Array.isArray(a.alias)?a.alias:[a.alias];d.forEach(function(t){var e={path:t,children:a.children};r(n,o,i,e,s,f.path||"/")})}o[f.path]||(n.push(f.path),o[f.path]=f);e&&(i[e]||(i[e]=f))}(o,i,a,t)});for(var s=0,u=o.length;s<u;s++)"*"===o[s]&&(o.push(o.splice(s,1)[0]),u--,s--);return{pathList:o,pathMap:i,nameMap:a}}function D(t,e,r,n){var o="string"==typeof t?{path:t}:t;if(o.name||o._normalized)return o;if(!o.path&&o.params&&e){(o=R({},o))._normalized=!0;var i=R(R({},e.params),o.params);if(e.name)o.name=e.name,o.params=i;else if(e.matched.length){var a=e.matched[e.matched.length-1].path;o.path=V(a,i,e.path)}return o}var s=function(t){var e="",r="",n=t.indexOf("#");0<=n&&(e=t.slice(n),t=t.slice(0,n));var o=t.indexOf("?");return 0<=o&&(r=t.slice(o+1),t=t.slice(0,o)),{path:t,query:r,hash:e}}(o.path||""),u=e&&e.path||"/",c=s.path?m(s.path,u,r||o.append):u,p=h(s.query,o.query,n&&n.options.parseQuery),f=o.hash||s.hash;return f&&"#"!==f.charAt(0)&&(f="#"+f),{_normalized:!0,type:o.type,params:o.params||{},path:c,query:p,hash:f}}function F(t,h){var e=z(t),f=e.pathList,l=e.pathMap,d=e.nameMap;function y(t,e,r){var n=D(t,e,!1,h),o=n.name;if(o){var i=d[o];if(!i)return v(null,n);var a=i.regex.keys.filter(function(t){return!t.optional}).map(function(t){return t.name});if("object"!=typeof n.params&&(n.params={}),e&&"object"==typeof e.params)for(var s in e.params)!(s in n.params)&&-1<a.indexOf(s)&&(n.params[s]=e.params[s]);if(i)return n.path=V(i.path,n.params),v(i,n,r)}else if(n.path){n.params=n.params||{};for(var u=0;u<f.length;u++){var c=f[u],p=l[c];if(K(p.regex,n.path,n.params))return v(p,n,r)}}return v(null,n)}function n(t,e){var r=t.redirect,n="function"==typeof r?r(j(t,e,null,h)):r;if("string"==typeof n&&(n={path:n}),!n||"object"!=typeof n)return v(null,e);var o,i=n,a=i.name,s=i.path,u=e.query,c=e.hash,p=e.params;if(u=i.hasOwnProperty("query")?i.query:u,c=i.hasOwnProperty("hash")?i.hash:c,p=i.hasOwnProperty("params")?i.params:p,a){d[a];return y({_normalized:!0,name:a,query:u,hash:c,params:p},void 0,e)}if(s){var f=m(s,(o=t).parent?o.parent.path:"/",!0);return y({_normalized:!0,path:V(f,p),query:u,hash:c},void 0,e)}return v(null,e)}function v(t,e,r){return t&&t.redirect?n(t,r||e):t&&t.matchAs?function(t,e,r){var n=y({_normalized:!0,path:V(r,e.params)});if(n){var o=n.matched,i=o[o.length-1];return e.params=n.params,v(i,e)}return v(null,e)}(0,e,t.matchAs):(e.params=e.params||{},t&&t.meta&&t.meta.id?e.params.__id__=t.meta.id:e.params.__id__||(e.params.__id__=h.id),t&&t.meta&&t.meta.name&&(t.meta.id?t.components.default.name=t.meta.name+"-"+e.params.__id__:(t=R({},t)).components={default:{name:t.meta.name+"-"+e.params.__id__,render:t.components.default.render}}),j(t,e,r,h))}return{match:y,addRoutes:function(t){z(t,f,l,d)}}}function K(t,e,r){var n=e.match(t);if(!n)return!1;if(!r)return!0;for(var o=1,i=n.length;o<i;++o){var a=t.keys[o-1],s="string"==typeof n[o]?decodeURIComponent(n[o]):n[o];a&&(r[a.name||"pathMatch"]=s)}return!0}var J=Object.create(null);function N(t){window.history.replaceState({key:it(),id:t.currentRoute&&t.currentRoute.params.__id__||t.id},"",window.location.href.replace(window.location.origin,"")),window.addEventListener("popstate",function(t){var e;X(),t.state&&t.state.key&&(e=t.state.key,nt=e)})}function Q(r,n,o,i){if(r.app){var a=r.options.scrollBehavior;a&&r.app.$nextTick(function(){var e=function(){var t=it();if(t)return J[t]}(),t=a.call(r,n,o,i?e:null);t&&("function"==typeof t.then?t.then(function(t){Z(t,e)}).catch(function(t){}):Z(t,e))})}}function X(){var t=it();t&&(J[t]={x:window.pageXOffset,y:window.pageYOffset})}function Y(t){return G(t.x)||G(t.y)}function W(t){return{x:G(t.x)?t.x:window.pageXOffset,y:G(t.y)?t.y:window.pageYOffset}}function G(t){return"number"==typeof t}function Z(t,e){var r,n,o,i,a,s="object"==typeof t;if(s&&"string"==typeof t.selector){var u=document.querySelector(t.selector);if(u){var c=t.offset&&"object"==typeof t.offset?t.offset:{};c={x:G((a=c).x)?a.x:0,y:G(a.y)?a.y:0},r=u,n=c,o=document.documentElement.getBoundingClientRect(),e={x:(i=r.getBoundingClientRect()).left-o.left-n.x,y:i.top-o.top-n.y}}else Y(t)&&(e=W(t))}else s&&Y(t)&&(e=W(t));e&&window.scrollTo(e.x,e.y)}var tt,et=v&&((-1===(tt=window.navigator.userAgent).indexOf("Android 2.")&&-1===tt.indexOf("Android 4.0")||-1===tt.indexOf("Mobile Safari")||-1!==tt.indexOf("Chrome")||-1!==tt.indexOf("Windows Phone"))&&window.history&&"pushState"in window.history),rt=v&&window.performance&&window.performance.now?window.performance:Date,nt=ot();function ot(){return rt.now().toFixed(3)}function it(){return nt}function at(e,t,r){X();var n=window.history;try{r?n.replaceState({id:t,key:nt},"",e):(nt=ot(),n.pushState({id:t,key:nt},"",e))}catch(t){window.location[r?"replace":"assign"](e)}}function st(t,e){at(t,e,!0)}function ut(e,r,n){var o=function(t){t>=e.length?n():e[t]?r(e[t],function(){o(t+1)}):o(t+1)};o(0)}function ct(r){return function(t,e,u){var c=!1,p=0,f=null;pt(r,function(r,t,n,o){if("function"==typeof r&&void 0===r.cid){c=!0,p++;var e,i=lt(function(t){var e;((e=t).__esModule||ht&&"Module"===e[Symbol.toStringTag])&&(t=t.default),r.resolved="function"==typeof t?t:d.extend(t),n.components[o]=t,--p<=0&&u()}),a=lt(function(t){var e="Failed to resolve async component "+o+": "+t;f||(f=l(t)?t:new Error(e),u(f))});try{e=r(i,a)}catch(t){a(t)}if(e)if("function"==typeof e.then)e.then(i,a);else{var s=e.component;s&&"function"==typeof s.then&&s.then(i,a)}}}),c||u()}}function pt(t,r){return ft(t.map(function(e){return Object.keys(e.components).map(function(t){return r(e.components[t],e.instances[t],e,t)})}))}function ft(t){return Array.prototype.concat.apply([],t)}var ht="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag;function lt(r){var n=!1;return function(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];if(!n)return n=!0,r.apply(this,t)}}var dt=function(t,e){this.router=t,this.base=function(t){if(!t)if(v){var e=document.querySelector("base");t=(t=e&&e.getAttribute("href")||"/").replace(/^https?:\/\/[^\/]+/,"")}else t="/";"/"!==t.charAt(0)&&(t="/"+t);return t.replace(/\/$/,"")}(e),this.current=O,this.pending=null,this.ready=!1,this.readyCbs=[],this.readyErrorCbs=[],this.errorCbs=[]};function yt(t,i,a,e){var r=pt(t,function(t,e,r,n){var o=function(t,e){"function"!=typeof t&&(t=d.extend(t));return t.options[e]}(t,i);if(o)return Array.isArray(o)?o.map(function(t){return a(t,e,r,n)}):a(o,e,r,n)});return ft(e?r.reverse():r)}function vt(t,e){if(e)return function(){return t.apply(e,arguments)}}dt.prototype.listen=function(t){this.cb=t},dt.prototype.onReady=function(t,e){this.ready?t():(this.readyCbs.push(t),e&&this.readyErrorCbs.push(e))},dt.prototype.onError=function(t){this.errorCbs.push(t)},dt.prototype.transitionTo=function(t,e,r){var n=this,o=this.router.match(t,this.current);this.confirmTransition(o,function(){n.updateRoute(o),e&&e(o),n.ensureURL(),n.ready||(n.ready=!0,n.readyCbs.forEach(function(t){t(o)}))},function(e){r&&r(e),e&&!n.ready&&(n.ready=!0,n.readyErrorCbs.forEach(function(t){t(e)}))})},dt.prototype.confirmTransition=function(r,e,t){var n=this,o=this.current,i=function(e){l(e)&&(n.errorCbs.length?n.errorCbs.forEach(function(t){t(e)}):console.error(e)),t&&t(e)},a=function(t,e){var r,n=Math.max(t.length,e.length);for(r=0;r<n&&t[r]===e[r];r++);return{updated:e.slice(0,r),activated:e.slice(r),deactivated:t.slice(r)}}(this.current.matched,r.matched),s=a.updated,u=a.deactivated,f=a.activated,c=[].concat(yt(u,"beforeRouteLeave",vt,!0),this.router.beforeHooks,yt(s,"beforeRouteUpdate",vt),f.map(function(t){return t.beforeEnter}),ct(f));this.pending=r;var h=function(t,e){if(n.pending!==r)return i();try{t(r,o,function(t){!1===t||l(t)?(n.ensureURL(!0),i(t)):"string"==typeof t||"object"==typeof t&&("string"==typeof t.path||"string"==typeof t.name)?(i(),"object"==typeof t&&t.replace?n.replace(t):n.push(t)):e(t)})}catch(t){i(t)}};ut(c,h,function(){var c,p,t=[];ut((c=t,p=function(){return n.current===r},yt(f,"beforeRouteEnter",function(t,e,r,n){return o=t,i=r,a=n,s=c,u=p,function(t,e,r){return o(t,e,function(t){r(t),"function"==typeof t&&s.push(function(){!function t(e,r,n,o){r[n]&&!r[n]._isBeingDestroyed?e(r[n]):o()&&setTimeout(function(){t(e,r,n,o)},16)}(t,i.instances,a,u)})})};var o,i,a,s,u})).concat(n.router.resolveHooks),h,function(){if(n.pending!==r)return i();n.pending=null,e(r),n.router.app&&n.router.app.$nextTick(function(){t.forEach(function(t){t()})})})})},dt.prototype.updateRoute=function(e){var r=this.current;this.current=e,this.cb&&this.cb(e),this.router.afterHooks.forEach(function(t){t&&t(e,r)})};var mt=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),((e.prototype=Object.create(t&&t.prototype)).constructor=e).prototype.setupListeners=function(){var o=this,i=this.router,t=i.options.scrollBehavior,a=et&&t;a&&N(i);var s=gt(this.base);window.addEventListener("popstate",function(t){var e=o.current,r=gt(o.base);if(o.current!==O||r!==s){var n=t.state&&t.state.id;if(!n)return window.location.reload();o.transitionTo({path:r,params:{__id__:n}},function(t){a&&Q(i,t,e,!0)})}})},e.prototype.go=function(t){window.history.go(t)},e.prototype.push=function(e,r,t){var n=this;if("object"==typeof e){e.params=e.params||{};var o=e.params.__id__;switch(e.type){case"navigateTo":o||this.router.id++;break;case"redirectTo":case"reLaunch":this.router.id++}o||(e.params.__id__=this.router.id)}var i=this.current;this.transitionTo(e,function(t){at(g(n.base+t.fullPath),e.params.__id__),Q(n.router,t,i,!1),r&&r(t)},t)},e.prototype.replace=function(e,r,t){var n=this;if("object"==typeof e){switch(e.type){case"navigateTo":case"redirectTo":case"reLaunch":this.router.id++}e.params=e.params||{},e.params.__id__=this.router.id}var o=this.current;this.transitionTo(e,function(t){st(g(n.base+t.fullPath),e.params.__id__),Q(n.router,t,o,!1),r&&r(t)},t)},e.prototype.ensureURL=function(t){if(gt(this.base)!==this.current.fullPath){var e=g(this.base+this.current.fullPath),r=this.current.params.__id__;t?at(e,r):st(e,r)}},e.prototype.getCurrentLocation=function(){return{path:gt(this.base),params:{__id__:++this.router.id}}},e}(dt);function gt(t){var e=decodeURI(window.location.pathname);return t&&0===e.indexOf(t)&&(e=e.slice(t.length)),(e||"/")+u(h(window.location.search))+window.location.hash}var _t=function(n){function t(t,e,r){n.call(this,t,e),r&&function(t){var e=gt(t);if(!/^\/#/.test(e))return window.location.replace(g(t+"/#"+e)),!0}(this.base)||bt()}return n&&(t.__proto__=n),((t.prototype=Object.create(n&&n.prototype)).constructor=t).prototype.setupListeners=function(){var n=this,t=this.router,e=t.options.scrollBehavior,o=et&&e;o&&N(t),window.addEventListener(et?"popstate":"hashchange",function(t){var e=n.current;if(bt()){var r=t.state&&t.state.id;if(!r)return window.location.reload();n.transitionTo({path:wt(),params:{__id__:r}},function(t){o&&Q(n.router,t,e,!0),et||Rt(t.fullPath,t.params.__id__)})}})},t.prototype.push=function(t,e,r){var n=this;if("object"==typeof t){t.params=t.params||{};var o=t.params.__id__;switch(t.type){case"navigateTo":o||this.router.id++;break;case"redirectTo":case"reLaunch":this.router.id++}o||(t.params.__id__=this.router.id)}var i=this.current,a=this.router.id;this.transitionTo(t,function(t){kt(t.fullPath,a),Q(n.router,t,i,!1),e&&e(t)},r)},t.prototype.replace=function(t,e,r){var n=this;if("object"==typeof t){switch(t.type){case"navigateTo":case"redirectTo":case"reLaunch":this.router.id++}t.params=t.params||{},t.params.__id__=this.router.id}var o=this.current,i=this.router.id;this.transitionTo(t,function(t){Rt(t.fullPath,i),Q(n.router,t,o,!1),e&&e(t)},r)},t.prototype.go=function(t){window.history.go(t)},t.prototype.ensureURL=function(t){var e=this.current.fullPath;wt()!==e&&(t?kt(e,this.current.params.__id__):Rt(e,this.current.params.__id__))},t.prototype.getCurrentLocation=function(){return{path:wt(),params:{__id__:++this.router.id}}},t}(dt);function bt(){var t=wt();return"/"===t.charAt(0)||(Rt("/"+t),!1)}function wt(){var t=window.location.href,e=t.indexOf("#");return-1===e?"":decodeURI(t.slice(e+1))}function xt(t){var e=window.location.href,r=e.indexOf("#");return(0<=r?e.slice(0,r):e)+"#"+t}function kt(t,e){et?at(xt(t),e):window.location.hash=t}function Rt(t,e){et?st(xt(t),e):window.location.replace(xt(t))}var Et=function(r){function t(t,e){r.call(this,t,e),this.stack=[],this.index=-1}return r&&(t.__proto__=r),((t.prototype=Object.create(r&&r.prototype)).constructor=t).prototype.push=function(t,e,r){var n=this;this.transitionTo(t,function(t){n.stack=n.stack.slice(0,n.index+1).concat(t),n.index++,e&&e(t)},r)},t.prototype.replace=function(t,e,r){var n=this;this.transitionTo(t,function(t){n.stack=n.stack.slice(0,n.index).concat(t),e&&e(t)},r)},t.prototype.go=function(t){var e=this,r=this.index+t;if(!(r<0||r>=this.stack.length)){var n=this.stack[r];this.confirmTransition(n,function(){e.index=r,e.updateRoute(n)})}},t.prototype.getCurrentLocation=function(){var t=this.stack[this.stack.length-1];return t?t.fullPath:"/"},t.prototype.ensureURL=function(){},t}(dt),jt=function(t){void 0===t&&(t={}),this.app=null,this.apps=[],this.options=t,this.beforeHooks=[],this.resolveHooks=[],this.afterHooks=[],this.matcher=F(t.routes||[],this),this.id=t.id||1,this.minId=t.id||1;var e=t.mode||"hash";switch(this.fallback="history"===e&&!et&&!1!==t.fallback,this.fallback&&(e="hash"),v||(e="abstract"),this.mode=e){case"history":this.history=new mt(this,t.base);break;case"hash":this.history=new _t(this,t.base,this.fallback);break;case"abstract":this.history=new Et(this,t.base)}},Ot={currentRoute:{configurable:!0}};function Ct(e,r){return e.push(r),function(){var t=e.indexOf(r);-1<t&&e.splice(t,1)}}return jt.prototype.match=function(t,e,r){return this.matcher.match(t,e,r)},Ot.currentRoute.get=function(){return this.history&&this.history.current},jt.prototype.init=function(t){var r=this;if(this.apps.push(t),!this.app){this.app=t;var e=this.history;if(e instanceof mt){var n=function(){e.setupListeners()};e.transitionTo(e.getCurrentLocation(),n,n)}else if(e instanceof _t){var o=function(){e.setupListeners()};e.transitionTo(e.getCurrentLocation(),o,o)}e.listen(function(e){r.apps.forEach(function(t){t._route=e})})}},jt.prototype.beforeEach=function(t){return Ct(this.beforeHooks,t)},jt.prototype.beforeResolve=function(t){return Ct(this.resolveHooks,t)},jt.prototype.afterEach=function(t){return Ct(this.afterHooks,t)},jt.prototype.onReady=function(t,e){this.history.onReady(t,e)},jt.prototype.onError=function(t){this.history.onError(t)},jt.prototype.push=function(t,e,r){this.history.push(t,e,r)},jt.prototype.replace=function(t,e,r){this.history.replace(t,e,r)},jt.prototype.go=function(t){this.history.go(t)},jt.prototype.back=function(){this.go(-1)},jt.prototype.forward=function(){this.go(1)},jt.prototype.getMatchedComponents=function(t){var e=t?t.matched?t:this.resolve(t).route:this.currentRoute;return e?[].concat.apply([],e.matched.map(function(e){return Object.keys(e.components).map(function(t){return e.components[t]})})):[]},jt.prototype.resolve=function(t,e,r){var n,o,i,a,s=D(t,e||this.history.current,r,this),u=this.match(s,e),c=u.redirectedFrom||u.fullPath,p=this.history.base;return{location:s,route:u,href:(n=p,o=c,i=this.mode,a="hash"===i?"#"+o:o,n?g(n+"/"+a):a),normalizedTo:s,resolved:u}},jt.prototype.addRoutes=function(t){this.matcher.addRoutes(t),this.history.current!==O&&this.history.transitionTo(this.history.getCurrentLocation())},Object.defineProperties(jt.prototype,Ot),jt.install=function t(e){if(!t.installed||d!==e){t.installed=!0;var n=function(t){return void 0!==t},r=function(t,e){var r=t.$options._parentVnode;n(r)&&n(r=r.data)&&n(r=r.registerRouteInstance)&&r(t,e)};(d=e).mixin({beforeCreate:function(){n(this.$options.router)?((this._routerRoot=this)._router=this.$options.router,this._router.init(this),e.util.defineReactive(this,"_route",this._router.history.current)):this._routerRoot=this.$parent&&this.$parent._routerRoot||this,r(this,this)},destroyed:function(){r(this)}}),Object.defineProperty(e.prototype,"$router",{get:function(){return this._routerRoot._router}}),Object.defineProperty(e.prototype,"$route",{get:function(){return this._routerRoot._route}}),e.component("RouterView",i),e.component("RouterLink",y);var o=e.config.optionMergeStrategies;o.beforeRouteEnter=o.beforeRouteLeave=o.beforeRouteUpdate=o.created}},jt.version="3.0.1",jt});