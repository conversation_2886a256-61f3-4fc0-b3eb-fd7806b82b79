<template>
  <view class="agreement-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">会员协议</text>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 会员协议内容 -->
    <view class="agreement-content">
      <!-- 协议开关 -->
      <view class="section-card">
        <view class="switch-item">
          <view class="switch-content">
            <text class="switch-title">会员协议</text>
            <text class="switch-desc">开启后，用户注册成为会员时需要同意会员协议</text>
          </view>
          <switch :checked="agreementSettings.enabled" @change="toggleAgreement" color="#4A00E0" />
        </view>
      </view>
      
      <block v-if="agreementSettings.enabled">
        <!-- 协议编辑 -->
        <view class="section-card">
          <view class="section-title">协议标题</view>
          <view class="form-item">
            <input class="form-input-full" v-model="agreementSettings.title" placeholder="请输入协议标题" />
          </view>
          
          <view class="section-title">协议内容</view>
          <view class="editor-toolbar">
            <view class="toolbar-btn" @click="formatText('bold')">
              <text class="toolbar-icon bold">B</text>
            </view>
            <view class="toolbar-btn" @click="formatText('italic')">
              <text class="toolbar-icon italic">I</text>
            </view>
            <view class="toolbar-btn" @click="formatText('underline')">
              <text class="toolbar-icon underline">U</text>
            </view>
            <view class="toolbar-btn" @click="formatText('header')">
              <text class="toolbar-icon">H</text>
            </view>
            <view class="toolbar-btn" @click="formatText('list')">
              <text class="toolbar-icon">•</text>
            </view>
          </view>
          <view class="form-item">
            <textarea class="form-textarea" v-model="agreementSettings.content" placeholder="请输入会员协议内容" />
          </view>
          
          <view class="form-tips">提示：可以使用富文本编辑器编辑更复杂的格式</view>
        </view>
        
        <!-- 协议预览 -->
        <view class="section-card">
          <view class="section-title">协议预览</view>
          
          <view class="preview-container">
            <view class="preview-title">{{agreementSettings.title || '会员协议'}}</view>
            <view class="preview-content">
              <text class="preview-text">{{agreementSettings.content || '请输入会员协议内容'}}</text>
            </view>
          </view>
          
          <button class="preview-btn" @click="showFullPreview">查看完整预览</button>
        </view>
        
        <!-- 协议设置 -->
        <view class="section-card">
          <view class="section-title">协议设置</view>
          
          <view class="form-item switch-item">
            <text class="form-label">强制阅读</text>
            <switch :checked="agreementSettings.forceRead" @change="toggleForceRead" color="#4A00E0" />
          </view>
          
          <view class="form-item" v-if="agreementSettings.forceRead">
            <text class="form-label">最短阅读时间</text>
            <view class="form-input-group">
              <input type="number" class="form-input" v-model="agreementSettings.minReadTime" />
              <text class="input-suffix">秒</text>
            </view>
          </view>
          
          <view class="form-item switch-item">
            <text class="form-label">更新提醒</text>
            <switch :checked="agreementSettings.updateNotify" @change="toggleUpdateNotify" color="#4A00E0" />
          </view>
          
          <view class="form-item switch-item">
            <text class="form-label">更新后重新同意</text>
            <switch :checked="agreementSettings.reconfirmAfterUpdate" @change="toggleReconfirm" color="#4A00E0" />
          </view>
        </view>
        
        <!-- 隐私政策 -->
        <view class="section-card">
          <view class="section-title">隐私政策</view>
          
          <view class="form-item switch-item">
            <text class="form-label">启用隐私政策</text>
            <switch :checked="agreementSettings.privacyEnabled" @change="togglePrivacyPolicy" color="#4A00E0" />
          </view>
          
          <view class="form-item" v-if="agreementSettings.privacyEnabled">
            <text class="form-label">隐私政策链接</text>
            <view class="form-input-group">
              <input class="form-input" v-model="agreementSettings.privacyUrl" placeholder="请输入隐私政策链接" />
              <view class="input-btn" @click="editPrivacyPolicy">编辑</view>
            </view>
          </view>
        </view>
      </block>
    </view>
    
    <!-- 保存按钮 -->
    <view class="bottom-bar" v-if="agreementSettings.enabled">
      <button class="save-btn" @click="saveSettings">保存设置</button>
    </view>
    
    <!-- 全屏预览弹窗 -->
    <view class="fullscreen-preview" v-if="showPreview">
      <view class="preview-header">
        <text class="preview-header-title">协议预览</text>
        <view class="preview-close" @click="closePreview">×</view>
      </view>
      <scroll-view class="preview-scroll" scroll-y>
        <view class="preview-title">{{agreementSettings.title || '会员协议'}}</view>
        <view class="preview-content">
          <text class="preview-text">{{agreementSettings.content || '请输入会员协议内容'}}</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 协议设置
      agreementSettings: {
        enabled: true,
        title: '磁州生活网会员服务协议',
        content: `尊敬的用户：

感谢您选择磁州生活网！本协议是您与磁州生活网之间关于成为会员、使用会员服务所订立的契约。请您仔细阅读本协议，确保理解协议中各条款的含义，特别是免责条款和服务限制条款。

一、会员服务内容
1.1 会员等级：本平台设有多个会员等级，不同等级享受不同权益。
1.2 会员权益：包括但不限于会员折扣、积分加速、免费配送、生日礼包、专属客服等。
1.3 会员积分：会员可通过消费、签到等方式获取积分，积分可用于兑换商品或服务。

二、会员规则
2.1 会员资格获取：用户可通过注册账号并满足相应条件获取会员资格。
2.2 会员等级晋升：会员等级根据累计消费金额、成长值等因素自动晋升。
2.3 会员有效期：除特殊说明外，会员资格长期有效。

三、会员权利与义务
3.1 会员有权享受平台提供的各项会员权益。
3.2 会员应遵守平台各项规则，不得利用会员身份从事违法或损害平台利益的行为。
3.3 会员应妥善保管账号信息，因会员个人原因导致的账号安全问题由会员自行承担责任。

四、协议修改
4.1 本平台有权根据业务发展需要修改本协议，修改后的协议将通过网站公告或其他方式通知会员。
4.2 会员如不同意修改后的协议，可申请终止会员服务；继续使用会员服务则视为接受修改后的协议。

五、免责声明
5.1 因不可抗力或第三方原因导致的服务中断或终止，本平台不承担责任。
5.2 本平台有权根据实际情况调整会员权益，但会提前通知会员。

六、其他条款
6.1 本协议的解释权归磁州生活网所有。
6.2 本协议自会员同意之日起生效。

如您对本协议有任何疑问，请联系客服咨询。`,
        forceRead: true,
        minReadTime: 10,
        updateNotify: true,
        reconfirmAfterUpdate: true,
        privacyEnabled: true,
        privacyUrl: 'https://www.cizhou.com/privacy'
      },
      
      // 预览控制
      showPreview: false
    };
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    
    toggleAgreement(e) {
      this.agreementSettings.enabled = e.detail.value;
    },
    
    formatText(type) {
      uni.showToast({
        title: '富文本编辑功能开发中',
        icon: 'none'
      });
    },
    
    toggleForceRead(e) {
      this.agreementSettings.forceRead = e.detail.value;
    },
    
    toggleUpdateNotify(e) {
      this.agreementSettings.updateNotify = e.detail.value;
    },
    
    toggleReconfirm(e) {
      this.agreementSettings.reconfirmAfterUpdate = e.detail.value;
    },
    
    togglePrivacyPolicy(e) {
      this.agreementSettings.privacyEnabled = e.detail.value;
    },
    
    editPrivacyPolicy() {
      uni.showToast({
        title: '隐私政策编辑功能开发中',
        icon: 'none'
      });
    },
    
    showFullPreview() {
      this.showPreview = true;
    },
    
    closePreview() {
      this.showPreview = false;
    },
    
    saveSettings() {
      uni.showLoading({
        title: '保存中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '会员协议设置保存成功',
          icon: 'success'
        });
      }, 1000);
    }
  }
}
</script>

<style>
/* 会员协议页面样式开始 */
.agreement-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 100rpx;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #8E2DE2, #4A00E0);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(74, 0, 224, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
}

/* 协议内容样式 */
.agreement-content {
  padding: 20rpx;
}

.section-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 开关样式 */
.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.switch-content {
  flex: 1;
}

.switch-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.switch-desc {
  font-size: 24rpx;
  color: #999;
}

/* 表单样式 */
.form-item {
  margin-bottom: 20rpx;
}

.form-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.form-input-group {
  display: flex;
  align-items: center;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  overflow: hidden;
}

.form-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.form-input-full {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  box-sizing: border-box;
}

.input-suffix {
  padding: 0 20rpx;
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  height: 80rpx;
  line-height: 80rpx;
}

.input-btn {
  padding: 0 30rpx;
  font-size: 26rpx;
  color: #4A00E0;
  background: rgba(74, 0, 224, 0.1);
  height: 80rpx;
  line-height: 80rpx;
}

.form-textarea {
  width: 100%;
  height: 400rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.form-tips {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 编辑器工具栏 */
.editor-toolbar {
  display: flex;
  background: #f5f5f5;
  border-radius: 8rpx 8rpx 0 0;
  border: 1rpx solid #ddd;
  border-bottom: none;
}

.toolbar-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1rpx solid #ddd;
}

.toolbar-btn:last-child {
  border-right: none;
}

.toolbar-icon {
  font-size: 28rpx;
  color: #666;
}

.toolbar-icon.bold {
  font-weight: bold;
}

.toolbar-icon.italic {
  font-style: italic;
}

.toolbar-icon.underline {
  text-decoration: underline;
}

/* 预览样式 */
.preview-container {
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.preview-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  padding: 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.preview-content {
  padding: 30rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.preview-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  white-space: pre-wrap;
}

.preview-btn {
  background: rgba(74, 0, 224, 0.1);
  color: #4A00E0;
  border: none;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
}

/* 全屏预览 */
.fullscreen-preview {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.preview-header {
  height: 100rpx;
  background: #4A00E0;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.preview-header-title {
  font-size: 32rpx;
  font-weight: 600;
}

.preview-close {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 40rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-scroll {
  flex: 1;
  padding: 30rpx;
}

/* 底部保存栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.save-btn {
  background: #4A00E0;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  width: 100%;
}
/* 会员协议页面样式结束 */
</style> 