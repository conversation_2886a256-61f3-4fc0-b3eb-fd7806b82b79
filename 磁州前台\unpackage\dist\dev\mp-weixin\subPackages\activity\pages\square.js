"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      currentNav: 0,
      refreshing: false,
      navItems: ["商品服务", "店铺形象", "位置配送", "认证资质", "基本信息"],
      storeInfo: {
        id: "12345678",
        name: "磁州生活家居旗舰店",
        avatar: "/static/images/store-logo.png",
        coverImage: "/static/images/store-cover.jpg",
        description: "为您提供高品质家居产品，打造舒适生活空间",
        rating: 4.8,
        orderCount: 1286,
        followers: 568,
        status: "open",
        operationHours: "09:00 - 22:00",
        isVerified: true,
        isPremium: true,
        styleId: 2,
        story: '我们是一家专注于家居产品的本地商户，创立于2018年。始终坚持"品质优先，用户至上"的原则，精选每一件商品，只为给客户带来舒适的居家体验。',
        address: "河北省邯郸市磁县磁州生活广场A座1202室",
        providesDelivery: true,
        deliveryRadius: 5,
        deliveryFeeType: "fixed",
        fixedDeliveryFee: "5",
        minimumOrder: "20",
        supportsPickup: true,
        pickupInstructions: "请到店铺前台出示订单号领取商品，工作时间9:00-22:00。",
        certificationStatus: "certified",
        // uncertified, pending, certified, failed
        certType: "enterprise",
        certTime: "2023-01-15",
        certNumber: "91130427MA0CXY2R5B",
        certExpiringSoon: false,
        expirationReminder: 30,
        promotionVideo: ""
      },
      productStats: {
        total: 128,
        onSale: 96,
        outOfStock: 8,
        services: 12
      },
      // 热门商品数据
      hotProducts: [
        {
          id: 1,
          name: "北欧风格实木沙发",
          price: "1299.00",
          image: "/static/images/product-1.jpg",
          sales: 56,
          rating: 98
        },
        {
          id: 2,
          name: "天然乳胶床垫",
          price: "2399.00",
          image: "/static/images/product-2.jpg",
          sales: 42,
          rating: 95
        },
        {
          id: 3,
          name: "日式简约茶几",
          price: "499.00",
          image: "/static/images/product-3.jpg",
          sales: 38,
          rating: 92
        },
        {
          id: 4,
          name: "环保棉麻窗帘",
          price: "298.00",
          image: "/static/images/product-4.jpg",
          sales: 127,
          rating: 97
        }
      ],
      // 店铺风格数据
      storeStyles: [
        {
          id: 1,
          name: "简约现代",
          preview: "/static/images/style-modern.jpg"
        },
        {
          id: 2,
          name: "轻奢风格",
          preview: "/static/images/style-luxury.jpg"
        },
        {
          id: 3,
          name: "田园清新",
          preview: "/static/images/style-country.jpg"
        },
        {
          id: 4,
          name: "工业风",
          preview: "/static/images/style-industrial.jpg"
        }
      ],
      // 店铺相册数据
      storeGallery: [
        {
          id: 1,
          url: "/static/images/gallery-1.jpg",
          tag: "店铺环境"
        },
        {
          id: 2,
          url: "/static/images/gallery-2.jpg",
          tag: "产品展示"
        },
        {
          id: 3,
          url: "/static/images/gallery-3.jpg",
          tag: "团队风采"
        },
        {
          id: 4,
          url: "/static/images/gallery-4.jpg",
          tag: "活动现场"
        },
        {
          id: 5,
          url: "/static/images/gallery-5.jpg",
          tag: "顾客好评"
        }
      ],
      // 配送费梯度
      distanceFeeTiers: [
        { distance: 3, price: "3" },
        { distance: 5, price: "5" },
        { distance: 10, price: "10" }
      ],
      // 行业资质数据
      qualifications: [
        {
          id: 1,
          name: "食品经营许可证",
          type: "food",
          validUntil: "2025-12-31",
          status: "approved",
          image: "/static/images/qual-food.jpg"
        },
        {
          id: 2,
          name: "卫生许可证",
          type: "health",
          validUntil: "2024-10-15",
          status: "approved",
          image: "/static/images/qual-health.jpg"
        }
      ],
      // 认证状态文本
      certStatusText: {
        "uncertified": "未认证",
        "pending": "审核中",
        "certified": "已认证",
        "failed": "未通过"
      },
      // 资质状态文本
      qualStatusText: {
        "pending": "审核中",
        "approved": "已通过",
        "rejected": "未通过",
        "expired": "已过期"
      },
      // 提醒方式
      reminderMethods: ["app", "sms"]
    };
  },
  methods: {
    // 通用导航方法
    goBack() {
      common_vendor.index.navigateBack();
    },
    navigateTo(url) {
      common_vendor.index.navigateTo({
        url
      });
    },
    // 切换导航选项卡
    switchNav(index) {
      this.currentNav = index;
    },
    // 下拉刷新
    refreshData(e) {
      this.refreshing = true;
      setTimeout(() => {
        this.refreshing = false;
      }, 1500);
    },
    // 商品服务相关方法
    previewStore() {
      common_vendor.index.showToast({
        title: "预览功能开发中",
        icon: "none"
      });
    },
    uploadStoreAvatar() {
      common_vendor.index.chooseImage({
        count: 1,
        success: (res) => {
          setTimeout(() => {
            this.storeInfo.avatar = res.tempFilePaths[0];
            common_vendor.index.showToast({
              title: "上传成功",
              icon: "success"
            });
          }, 1e3);
        }
      });
    },
    editOperationHours() {
      common_vendor.index.showToast({
        title: "编辑营业时间功能开发中",
        icon: "none"
      });
    },
    // 店铺形象相关方法
    uploadCoverImage() {
      common_vendor.index.chooseImage({
        count: 1,
        success: (res) => {
          setTimeout(() => {
            this.storeInfo.coverImage = res.tempFilePaths[0];
            common_vendor.index.showToast({
              title: "上传成功",
              icon: "success"
            });
          }, 1e3);
        }
      });
    },
    selectStoreStyle(styleId) {
      this.storeInfo.styleId = styleId;
    },
    addGalleryImage() {
      common_vendor.index.chooseImage({
        count: 1,
        success: (res) => {
          setTimeout(() => {
            this.storeGallery.push({
              id: this.storeGallery.length + 1,
              url: res.tempFilePaths[0],
              tag: "新增图片"
            });
            common_vendor.index.showToast({
              title: "上传成功",
              icon: "success"
            });
          }, 1e3);
        }
      });
    },
    uploadVideo() {
      common_vendor.index.chooseVideo({
        sourceType: ["album", "camera"],
        maxDuration: 120,
        camera: "back",
        success: (res) => {
          setTimeout(() => {
            this.storeInfo.promotionVideo = res.tempFilePath;
            common_vendor.index.showToast({
              title: "上传成功",
              icon: "success"
            });
          }, 1500);
        }
      });
    },
    saveStoryContent() {
      common_vendor.index.showToast({
        title: "保存成功",
        icon: "success"
      });
    },
    // 位置配送相关方法
    editAddress() {
      common_vendor.index.showToast({
        title: "编辑地址功能开发中",
        icon: "none"
      });
    },
    navigateToStore() {
      common_vendor.index.showToast({
        title: "导航功能开发中",
        icon: "none"
      });
    },
    openMapSelection() {
      common_vendor.index.showToast({
        title: "地图选点功能开发中",
        icon: "none"
      });
    },
    previewMapInApp() {
      common_vendor.index.showToast({
        title: "地图预览功能开发中",
        icon: "none"
      });
    },
    toggleDelivery(e) {
      this.storeInfo.providesDelivery = e.detail.value;
    },
    updateDeliveryRadius(e) {
      this.storeInfo.deliveryRadius = e.detail.value;
    },
    addFeeTier() {
      if (this.distanceFeeTiers.length >= 5) {
        common_vendor.index.showToast({
          title: "最多添加5个梯度",
          icon: "none"
        });
        return;
      }
      const lastTier = this.distanceFeeTiers[this.distanceFeeTiers.length - 1];
      this.distanceFeeTiers.push({
        distance: lastTier.distance + 5,
        price: (parseFloat(lastTier.price) + 5).toString()
      });
    },
    removeFeeTier(index) {
      this.distanceFeeTiers.splice(index, 1);
    },
    togglePickup(e) {
      this.storeInfo.supportsPickup = e.detail.value;
    },
    saveLocationAndDelivery() {
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "保存成功",
          icon: "success"
        });
      }, 1500);
    },
    // 认证资质相关方法
    startCertification() {
      common_vendor.index.navigateTo({
        url: "/pages/store/certification/apply"
      });
    },
    retryCertification() {
      common_vendor.index.navigateTo({
        url: "/pages/store/certification/retry"
      });
    },
    renewCertification() {
      common_vendor.index.navigateTo({
        url: "/pages/store/certification/renew"
      });
    },
    viewQualification(qual) {
      common_vendor.index.previewImage({
        urls: [qual.image],
        current: qual.image
      });
    },
    editQualification(qual) {
      common_vendor.index.navigateTo({
        url: `/pages/store/qualification/edit?id=${qual.id}`
      });
    },
    deleteQualification(qual) {
      common_vendor.index.showModal({
        title: "确认删除",
        content: `确定要删除"${qual.name}"资质吗？`,
        success: (res) => {
          if (res.confirm) {
            this.qualifications = this.qualifications.filter((q) => q.id !== qual.id);
            common_vendor.index.showToast({
              title: "删除成功",
              icon: "success"
            });
          }
        }
      });
    },
    addQualification() {
      common_vendor.index.navigateTo({
        url: "/pages/store/qualification/add"
      });
    },
    toggleReminderMethod(method) {
      const index = this.reminderMethods.indexOf(method);
      if (index > -1) {
        this.reminderMethods.splice(index, 1);
      } else {
        this.reminderMethods.push(method);
      }
    },
    saveReminderSettings() {
      common_vendor.index.showToast({
        title: "设置已保存",
        icon: "success"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: $data.storeInfo.avatar || "/static/images/default-store.png",
    c: common_vendor.o((...args) => $options.uploadStoreAvatar && $options.uploadStoreAvatar(...args)),
    d: common_vendor.t($data.storeInfo.name),
    e: $data.storeInfo.isVerified
  }, $data.storeInfo.isVerified ? {} : {}, {
    f: $data.storeInfo.isPremium
  }, $data.storeInfo.isPremium ? {} : {}, {
    g: common_vendor.t($data.storeInfo.description || "暂无店铺介绍"),
    h: common_vendor.t($data.storeInfo.rating),
    i: common_vendor.t($data.storeInfo.orderCount),
    j: common_vendor.t($data.storeInfo.followers),
    k: common_vendor.o((...args) => $options.previewStore && $options.previewStore(...args)),
    l: $data.storeInfo.status === "open" ? 1 : "",
    m: common_vendor.t($data.storeInfo.status === "open" ? "营业中" : "休息中"),
    n: common_vendor.t($data.storeInfo.operationHours),
    o: common_vendor.o((...args) => $options.editOperationHours && $options.editOperationHours(...args)),
    p: common_vendor.f($data.navItems, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: index,
        c: $data.currentNav === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchNav(index), index)
      };
    }),
    q: common_vendor.t($data.productStats.total),
    r: common_vendor.t($data.productStats.onSale),
    s: common_vendor.t($data.productStats.outOfStock),
    t: common_vendor.t($data.productStats.services),
    v: common_vendor.o(($event) => $options.navigateTo("/pages/store/product/list")),
    w: common_vendor.o(($event) => $options.navigateTo("/pages/store/category/list")),
    x: common_vendor.o(($event) => $options.navigateTo("/pages/store/service/list")),
    y: common_vendor.o(($event) => $options.navigateTo("/pages/store/stock/manage")),
    z: common_vendor.o(($event) => $options.navigateTo("/pages/store/product/hot")),
    A: common_vendor.f($data.hotProducts, (product, index, i0) => {
      return {
        a: product.image,
        b: common_vendor.t(product.name),
        c: common_vendor.t(product.price),
        d: common_vendor.t(product.sales),
        e: common_vendor.t(product.rating),
        f: index
      };
    }),
    B: common_vendor.o(($event) => $options.navigateTo("/pages/store/product/add")),
    C: common_vendor.o(($event) => $options.navigateTo("/pages/store/product/batch")),
    D: common_vendor.o(($event) => $options.navigateTo("/pages/store/product/import")),
    E: $data.currentNav === 0,
    F: $data.storeInfo.coverImage || "/static/images/default-cover.jpg",
    G: common_vendor.o((...args) => $options.uploadCoverImage && $options.uploadCoverImage(...args)),
    H: common_vendor.f($data.storeStyles, (style, index, i0) => {
      return common_vendor.e({
        a: style.preview,
        b: common_vendor.t(style.name),
        c: $data.storeInfo.styleId === style.id
      }, $data.storeInfo.styleId === style.id ? {} : {}, {
        d: index,
        e: $data.storeInfo.styleId === style.id ? 1 : "",
        f: common_vendor.o(($event) => $options.selectStoreStyle(style.id), index)
      });
    }),
    I: common_vendor.o(($event) => $options.navigateTo("/pages/store/gallery")),
    J: common_vendor.f($data.storeGallery, (image, index, i0) => {
      return common_vendor.e({
        a: image.url,
        b: image.tag
      }, image.tag ? {
        c: common_vendor.t(image.tag)
      } : {}, {
        d: index
      });
    }),
    K: common_vendor.o((...args) => $options.addGalleryImage && $options.addGalleryImage(...args)),
    L: $data.storeInfo.promotionVideo
  }, $data.storeInfo.promotionVideo ? {
    M: $data.storeInfo.promotionVideo,
    N: common_assets._imports_0$25
  } : {
    O: common_vendor.o((...args) => $options.uploadVideo && $options.uploadVideo(...args))
  }, {
    P: $data.storeInfo.story,
    Q: common_vendor.o(($event) => $data.storeInfo.story = $event.detail.value),
    R: common_vendor.t(($data.storeInfo.story || "").length),
    S: common_vendor.o((...args) => $options.saveStoryContent && $options.saveStoryContent(...args)),
    T: $data.currentNav === 1,
    U: common_vendor.t($data.storeInfo.address || "点击设置店铺地址"),
    V: common_vendor.o((...args) => $options.editAddress && $options.editAddress(...args)),
    W: common_vendor.o((...args) => $options.navigateToStore && $options.navigateToStore(...args)),
    X: common_assets._imports_1$27,
    Y: common_vendor.o((...args) => $options.openMapSelection && $options.openMapSelection(...args)),
    Z: common_vendor.o((...args) => $options.previewMapInApp && $options.previewMapInApp(...args)),
    aa: $data.storeInfo.providesDelivery,
    ab: common_vendor.o((...args) => $options.toggleDelivery && $options.toggleDelivery(...args)),
    ac: $data.storeInfo.providesDelivery
  }, $data.storeInfo.providesDelivery ? common_vendor.e({
    ad: $data.storeInfo.deliveryRadius,
    ae: common_vendor.o((...args) => $options.updateDeliveryRadius && $options.updateDeliveryRadius(...args)),
    af: common_vendor.t($data.storeInfo.deliveryRadius),
    ag: $data.storeInfo.deliveryFeeType === "fixed" ? 1 : "",
    ah: common_vendor.o(($event) => $data.storeInfo.deliveryFeeType = "fixed"),
    ai: $data.storeInfo.deliveryFeeType === "distance" ? 1 : "",
    aj: common_vendor.o(($event) => $data.storeInfo.deliveryFeeType = "distance"),
    ak: $data.storeInfo.deliveryFeeType === "fixed"
  }, $data.storeInfo.deliveryFeeType === "fixed" ? {
    al: $data.storeInfo.fixedDeliveryFee,
    am: common_vendor.o(($event) => $data.storeInfo.fixedDeliveryFee = $event.detail.value)
  } : {}, {
    an: $data.storeInfo.deliveryFeeType === "distance"
  }, $data.storeInfo.deliveryFeeType === "distance" ? {
    ao: common_vendor.f($data.distanceFeeTiers, (fee, index, i0) => {
      return common_vendor.e({
        a: index === 0
      }, index === 0 ? {
        b: common_vendor.t(fee.distance)
      } : {
        c: common_vendor.t($data.distanceFeeTiers[index - 1].distance),
        d: common_vendor.t(fee.distance)
      }, {
        e: fee.price,
        f: common_vendor.o(($event) => fee.price = $event.detail.value, index)
      }, $data.distanceFeeTiers.length > 1 ? {
        g: common_vendor.o(($event) => $options.removeFeeTier(index), index)
      } : {}, {
        h: index
      });
    }),
    ap: $data.distanceFeeTiers.length > 1,
    aq: common_vendor.o((...args) => $options.addFeeTier && $options.addFeeTier(...args))
  } : {}, {
    ar: $data.storeInfo.minimumOrder,
    as: common_vendor.o(($event) => $data.storeInfo.minimumOrder = $event.detail.value)
  }) : {
    at: common_assets._imports_2$22
  }, {
    av: $data.storeInfo.supportsPickup,
    aw: common_vendor.o((...args) => $options.togglePickup && $options.togglePickup(...args)),
    ax: $data.storeInfo.supportsPickup
  }, $data.storeInfo.supportsPickup ? {
    ay: $data.storeInfo.pickupInstructions,
    az: common_vendor.o(($event) => $data.storeInfo.pickupInstructions = $event.detail.value),
    aA: common_vendor.t(($data.storeInfo.pickupInstructions || "").length)
  } : {
    aB: common_assets._imports_3$18
  }, {
    aC: common_vendor.o((...args) => $options.saveLocationAndDelivery && $options.saveLocationAndDelivery(...args)),
    aD: $data.currentNav === 2,
    aE: common_vendor.t($data.certStatusText[$data.storeInfo.certificationStatus] || "未认证"),
    aF: common_vendor.n($data.storeInfo.certificationStatus),
    aG: $data.storeInfo.certificationStatus === "uncertified"
  }, $data.storeInfo.certificationStatus === "uncertified" ? {
    aH: common_vendor.o((...args) => $options.startCertification && $options.startCertification(...args))
  } : $data.storeInfo.certificationStatus === "pending" ? {
    aJ: common_vendor.t($data.storeInfo.certificationETA || "3个工作日")
  } : $data.storeInfo.certificationStatus === "certified" ? common_vendor.e({
    aL: common_vendor.t($data.storeInfo.certType === "individual" ? "个体工商户认证" : "企业认证"),
    aM: common_vendor.t($data.storeInfo.certTime || "2023-04-01"),
    aN: common_vendor.t($data.storeInfo.certType === "individual" ? "营业执照" : "统一社会信用代码"),
    aO: common_vendor.t($data.storeInfo.certNumber),
    aP: $data.storeInfo.certExpiringSoon
  }, $data.storeInfo.certExpiringSoon ? {
    aQ: common_vendor.o((...args) => $options.renewCertification && $options.renewCertification(...args))
  } : {}) : $data.storeInfo.certificationStatus === "failed" ? {
    aS: common_vendor.t($data.storeInfo.failReason || "资料不完整，请补充完整的营业执照信息"),
    aT: common_vendor.o((...args) => $options.retryCertification && $options.retryCertification(...args))
  } : {}, {
    aI: $data.storeInfo.certificationStatus === "pending",
    aK: $data.storeInfo.certificationStatus === "certified",
    aR: $data.storeInfo.certificationStatus === "failed",
    aU: $data.qualifications.length === 0
  }, $data.qualifications.length === 0 ? {
    aV: common_assets._imports_4$14
  } : {
    aW: common_vendor.f($data.qualifications, (qual, index, i0) => {
      return {
        a: common_vendor.n(qual.type),
        b: common_vendor.t(qual.name),
        c: common_vendor.t(qual.validUntil ? "有效期至：" + qual.validUntil : "长期有效"),
        d: common_vendor.t($data.qualStatusText[qual.status] || "待审核"),
        e: common_vendor.n(qual.status),
        f: common_vendor.o(($event) => $options.viewQualification(qual), index),
        g: common_vendor.o(($event) => $options.editQualification(qual), index),
        h: common_vendor.o(($event) => $options.deleteQualification(qual), index),
        i: index
      };
    })
  }, {
    aX: common_vendor.o((...args) => $options.addQualification && $options.addQualification(...args)),
    aY: $data.storeInfo.expirationReminder,
    aZ: common_vendor.o(($event) => $data.storeInfo.expirationReminder = $event.detail.value),
    ba: $data.reminderMethods.includes("app")
  }, $data.reminderMethods.includes("app") ? {} : {}, {
    bb: $data.reminderMethods.includes("app") ? 1 : "",
    bc: common_vendor.o(($event) => $options.toggleReminderMethod("app")),
    bd: $data.reminderMethods.includes("sms")
  }, $data.reminderMethods.includes("sms") ? {} : {}, {
    be: $data.reminderMethods.includes("sms") ? 1 : "",
    bf: common_vendor.o(($event) => $options.toggleReminderMethod("sms")),
    bg: $data.reminderMethods.includes("email")
  }, $data.reminderMethods.includes("email") ? {} : {}, {
    bh: $data.reminderMethods.includes("email") ? 1 : "",
    bi: common_vendor.o(($event) => $options.toggleReminderMethod("email")),
    bj: common_vendor.o((...args) => $options.saveReminderSettings && $options.saveReminderSettings(...args)),
    bk: $data.currentNav === 3,
    bl: common_vendor.o((...args) => $options.refreshData && $options.refreshData(...args)),
    bm: $data.refreshing
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/activity/pages/square.js.map
