"use strict";
const common_vendor = require("../../../../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_rect = common_vendor.resolveComponent("rect");
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  const _component_line = common_vendor.resolveComponent("line");
  (_component_path + _component_svg + _component_rect + _component_circle + _component_polyline + _component_line)();
}
const maxDisplayParticipants = 3;
const _sfc_main = {
  __name: "ActivityCard",
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  emits: ["favorite", "action"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const displayParticipants = common_vendor.computed(() => {
      if (!props.item.participants)
        return [];
      return props.item.participants.slice(0, maxDisplayParticipants);
    });
    const typeText = common_vendor.computed(() => {
      switch (props.item.type) {
        case "groupBuy":
          return "拼团";
        case "flashSale":
          return "秒杀";
        case "coupon":
          return "优惠券";
        case "discount":
          return "满减";
        default:
          return "活动";
      }
    });
    const groupProgress = common_vendor.computed(() => {
      if (props.item.type !== "groupBuy")
        return 0;
      return props.item.currentGroupMembers / props.item.groupSize * 100;
    });
    const stockProgress = common_vendor.computed(() => {
      if (props.item.type !== "flashSale")
        return 0;
      return props.item.soldCount / props.item.totalStock * 100;
    });
    const discountPercent = common_vendor.computed(() => {
      if (props.item.type !== "flashSale")
        return "";
      return (props.item.salePrice / props.item.originalPrice * 10).toFixed(1);
    });
    const countdownHours = common_vendor.ref("00");
    const countdownMinutes = common_vendor.ref("00");
    const countdownSeconds = common_vendor.ref("00");
    if (props.item.type === "flashSale" && (props.item.status === "ongoing" || props.item.status === "upcoming")) {
      countdownHours.value = "01";
      countdownMinutes.value = "30";
      countdownSeconds.value = "45";
    }
    const actionBtnText = common_vendor.computed(() => {
      if (props.item.status === "ended")
        return "查看详情";
      switch (props.item.type) {
        case "groupBuy":
          return props.item.status === "upcoming" ? "预约拼团" : "立即拼团";
        case "flashSale":
          return props.item.status === "upcoming" ? "提醒我" : "立即抢购";
        case "coupon":
          return "立即领取";
        case "discount":
          return "去使用";
        default:
          return "立即参与";
      }
    });
    const actionBtnClass = common_vendor.computed(() => {
      if (props.item.status === "ended")
        return "btn-disabled";
      return `btn-${props.item.type}`;
    });
    function getDistributionProfit() {
      switch (props.item.type) {
        case "groupBuy":
          return (props.item.groupPrice * 0.1).toFixed(2);
        case "flashSale":
          return (props.item.salePrice * 0.15).toFixed(2);
        case "coupon":
          return props.item.couponType === "cash" ? (props.item.couponValue * 0.05).toFixed(2) : "5.00";
        case "discount":
          return props.item.discountRules && props.item.discountRules.length > 0 ? (props.item.discountRules[0].discount * 0.2).toFixed(2) : "8.00";
        default:
          return "10.00";
      }
    }
    function toggleFavorite(event) {
      event.stopPropagation();
      emit("favorite", props.item.id);
    }
    function handleActionClick(event) {
      event.stopPropagation();
      emit("action", {
        id: props.item.id,
        type: props.item.type,
        status: props.item.status
      });
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(typeText.value),
        b: common_vendor.n(`type-${__props.item.type}`),
        c: __props.item.coverImage,
        d: common_vendor.p({
          d: "M12 2v4M12 18v4M4.93 4.93l2.83 2.83M16.24 16.24l2.83 2.83M2 12h4M18 12h4M4.93 19.07l2.83-2.83M16.24 7.76l2.83-2.83"
        }),
        e: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "14",
          height: "14",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        f: __props.item.isPaidTop
      }, __props.item.isPaidTop ? {
        g: common_vendor.p({
          d: "M12 2v4M12 18v4M4.93 4.93l2.83 2.83M16.24 16.24l2.83 2.83M2 12h4M18 12h4M4.93 19.07l2.83-2.83M16.24 7.76l2.83-2.83"
        }),
        h: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "14",
          height: "14",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        })
      } : {}, {
        i: __props.item.isAdTop
      }, __props.item.isAdTop ? {
        j: common_vendor.p({
          x: "2",
          y: "7",
          width: "20",
          height: "14",
          rx: "2",
          ry: "2"
        }),
        k: common_vendor.p({
          d: "M16 3L12 7 8 3"
        }),
        l: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "14",
          height: "14",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        })
      } : {}, {
        m: common_vendor.p({
          d: "M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
        }),
        n: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "20",
          height: "20",
          viewBox: "0 0 24 24",
          fill: "item.isFavorite ? 'currentColor' : 'none'",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        o: common_vendor.o(toggleFavorite),
        p: __props.item.hot
      }, __props.item.hot ? {
        q: common_vendor.p({
          d: "M8 2h8l4 10-4 10H8L4 12 8 2z"
        }),
        r: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "14",
          height: "14",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        })
      } : {}, {
        s: common_vendor.t(__props.item.title),
        t: common_vendor.p({
          cx: "12",
          cy: "12",
          r: "10"
        }),
        v: common_vendor.p({
          points: "12 6 12 12 16 14"
        }),
        w: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "14",
          height: "14",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        x: common_vendor.t(__props.item.startTime),
        y: common_vendor.t(__props.item.endTime),
        z: common_vendor.p({
          d: "M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"
        }),
        A: common_vendor.p({
          cx: "12",
          cy: "10",
          r: "3"
        }),
        B: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "14",
          height: "14",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        C: common_vendor.t(__props.item.location),
        D: __props.item.type === "groupBuy"
      }, __props.item.type === "groupBuy" ? {
        E: common_vendor.t(__props.item.groupPrice),
        F: common_vendor.t(__props.item.originalPrice),
        G: groupProgress.value + "%",
        H: common_vendor.t(__props.item.groupSize - __props.item.currentGroupMembers)
      } : __props.item.type === "flashSale" ? common_vendor.e({
        J: common_vendor.t(__props.item.salePrice),
        K: common_vendor.t(__props.item.originalPrice),
        L: common_vendor.t(discountPercent.value),
        M: __props.item.status === "ongoing" || __props.item.status === "upcoming"
      }, __props.item.status === "ongoing" || __props.item.status === "upcoming" ? {
        N: common_vendor.t(__props.item.status === "ongoing" ? "距结束" : "距开始"),
        O: common_vendor.t(countdownHours.value),
        P: common_vendor.t(countdownMinutes.value),
        Q: common_vendor.t(countdownSeconds.value)
      } : {}, {
        R: stockProgress.value + "%",
        S: common_vendor.t(__props.item.soldCount),
        T: common_vendor.t(__props.item.totalStock)
      }) : __props.item.type === "coupon" ? common_vendor.e({
        V: __props.item.couponType === "cash"
      }, __props.item.couponType === "cash" ? {} : {}, {
        W: common_vendor.t(__props.item.couponValue),
        X: __props.item.couponType === "discount"
      }, __props.item.couponType === "discount" ? {} : {}, {
        Y: __props.item.couponCondition
      }, __props.item.couponCondition ? {
        Z: common_vendor.t(__props.item.couponCondition)
      } : {}, {
        aa: common_vendor.t(__props.item.couponValidity)
      }) : __props.item.type === "discount" ? {
        ac: common_vendor.f(__props.item.discountRules, (rule, index, i0) => {
          return {
            a: common_vendor.t(rule.threshold),
            b: common_vendor.t(rule.discount),
            c: index
          };
        }),
        ad: common_vendor.t(__props.item.merchantCount)
      } : {}, {
        I: __props.item.type === "flashSale",
        U: __props.item.type === "coupon",
        ab: __props.item.type === "discount",
        ae: __props.item.participants && __props.item.participants.length > 0
      }, __props.item.participants && __props.item.participants.length > 0 ? common_vendor.e({
        af: common_vendor.f(displayParticipants.value, (participant, index, i0) => {
          return {
            a: index,
            b: participant.avatar,
            c: displayParticipants.value.length - index
          };
        }),
        ag: __props.item.participants.length > maxDisplayParticipants
      }, __props.item.participants.length > maxDisplayParticipants ? {
        ah: common_vendor.t(__props.item.participants.length - maxDisplayParticipants)
      } : {}, {
        ai: common_vendor.t(__props.item.participants.length)
      }) : {}, {
        aj: common_vendor.p({
          x1: "12",
          y1: "1",
          x2: "12",
          y2: "23"
        }),
        ak: common_vendor.p({
          d: "M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"
        }),
        al: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "14",
          height: "14",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        am: common_vendor.t(getDistributionProfit()),
        an: common_vendor.t(actionBtnText.value),
        ao: common_vendor.n(actionBtnClass.value),
        ap: common_vendor.o(handleActionClick),
        aq: common_vendor.n(`activity-type-${__props.item.type}`),
        ar: common_vendor.n({
          "activity-ended": __props.item.status === "ended"
        })
      });
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-456ec6cc"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/components/activity/ActivityCard.js.map
