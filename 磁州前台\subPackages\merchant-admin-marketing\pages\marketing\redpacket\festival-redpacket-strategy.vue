<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">节日红包攻略</text>
      <view class="navbar-right">
        <view class="share-icon" @click="shareGuide">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="18" cy="5" r="3"></circle>
            <circle cx="6" cy="12" r="3"></circle>
            <circle cx="18" cy="19" r="3"></circle>
            <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
            <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
          </svg>
        </view>
      </view>
    </view>
    
    <!-- 页面内容 -->
    <scroll-view scroll-y class="content-scroll">
      <!-- 页面头部 -->
      <view class="page-header">
        <view class="header-bg" style="background: linear-gradient(135deg, #FF9A8B, #FF6B6B);">
          <text class="header-title">节日期间红包营销策略指南</text>
          <text class="header-subtitle">提升节日营销效果的实用攻略</text>
        </view>
      </view>
      
      <!-- 内容部分 -->
      <view class="content-section">
        <view class="section-title">
          <view class="title-icon" style="background-color: rgba(255, 107, 107, 0.2);">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#FF6B6B" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="16" y1="2" x2="16" y2="6"></line>
              <line x1="8" y1="2" x2="8" y2="6"></line>
              <line x1="3" y1="10" x2="21" y2="10"></line>
            </svg>
          </view>
          <text class="title-text">节日红包的重要性</text>
        </view>
        
        <view class="content-text">
          <text>节日期间是商家营销的黄金时期，用户消费意愿强，社交分享频率高。节日红包营销能够抓住用户情感共鸣，提升品牌好感度，刺激消费决策，是节日营销的重要手段。数据显示，节日期间发放红包的商家比普通时期销售额平均提升35%以上。</text>
        </view>
        
        <view class="content-image">
          <image src="/static/images/festival-redpacket.png" mode="widthFix"></image>
        </view>
        
        <view class="section-title">
          <view class="title-icon" style="background-color: rgba(255, 107, 107, 0.2);">
<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#FF6B6B" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
  <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path>
  <line x1="7" y1="7" x2="7.01" y2="7"></line>
</svg>
          </view>
          <text class="title-text">主要节日红包策略</text>
        </view>
        
        <view class="festival-list">
          <view class="festival-item">
            <view class="festival-header">
              <view class="festival-icon" style="background-color: rgba(255, 107, 107, 0.2);">
                <text>春</text>
              </view>
              <text class="festival-name">春节红包</text>
            </view>
            <view class="festival-content">
              <text class="festival-desc">春节是中国最重要的传统节日，红包寓意"压岁钱"，象征着祝福和好运。</text>
              <view class="strategy-points">
                <view class="strategy-point">
                  <text class="point-dot"></text>
                  <text class="point-text">设计传统喜庆的红包封面，融入"福"、"财"等元素</text>
                </view>
                <view class="strategy-point">
                  <text class="point-dot"></text>
                  <text class="point-text">发放金额可设置为"6.66"、"8.88"等吉利数字</text>
                </view>
                <view class="strategy-point">
                  <text class="point-dot"></text>
                  <text class="point-text">结合"集五福"等互动玩法提高用户参与度</text>
                </view>
              </view>
            </view>
          </view>
          
          <view class="festival-item">
            <view class="festival-header">
              <view class="festival-icon" style="background-color: rgba(255, 107, 107, 0.2);">
                <text>元</text>
              </view>
              <text class="festival-name">元旦红包</text>
            </view>
            <view class="festival-content">
              <text class="festival-desc">元旦是新年的第一天，象征着新的开始，红包寓意新年好运。</text>
              <view class="strategy-points">
                <view class="strategy-point">
                  <text class="point-dot"></text>
                  <text class="point-text">设计以"新年新气象"为主题的红包封面</text>
                </view>
                <view class="strategy-point">
                  <text class="point-dot"></text>
                  <text class="point-text">结合年终盘点或新年愿望清单等互动活动</text>
                </view>
                <view class="strategy-point">
                  <text class="point-dot"></text>
                  <text class="point-text">推出"跨年红包雨"活动增强节日氛围</text>
                </view>
              </view>
            </view>
          </view>
          
          <view class="festival-item">
            <view class="festival-header">
              <view class="festival-icon" style="background-color: rgba(255, 107, 107, 0.2);">
                <text>情</text>
              </view>
              <text class="festival-name">情人节红包</text>
            </view>
            <view class="festival-content">
              <text class="festival-desc">情人节是表达爱意的节日，红包可以作为爱的礼物和惊喜。</text>
              <view class="strategy-points">
                <view class="strategy-point">
                  <text class="point-dot"></text>
                  <text class="point-text">设计浪漫粉色或红色系的红包封面</text>
                </view>
                <view class="strategy-point">
                  <text class="point-dot"></text>
                  <text class="point-text">推出"情侣专享"双人红包，两人均可领取</text>
                </view>
                <view class="strategy-point">
                  <text class="point-dot"></text>
                  <text class="point-text">结合表白、告白等互动玩法增强情感连接</text>
                </view>
              </view>
            </view>
          </view>
          
          <view class="festival-item">
            <view class="festival-header">
              <view class="festival-icon" style="background-color: rgba(255, 107, 107, 0.2);">
                <text>618</text>
              </view>
              <text class="festival-name">618购物节</text>
            </view>
            <view class="festival-content">
              <text class="festival-desc">618已成为仅次于双11的电商购物节，红包是刺激消费的重要手段。</text>
              <view class="strategy-points">
                <view class="strategy-point">
                  <text class="point-dot"></text>
                  <text class="point-text">设计突出"618"数字的红包封面</text>
                </view>
                <view class="strategy-point">
                  <text class="point-dot"></text>
                  <text class="point-text">推出满减、折扣等多种类型的红包</text>
                </view>
                <view class="strategy-point">
                  <text class="point-dot"></text>
                  <text class="point-text">设置阶梯式红包，消费越多优惠越大</text>
                </view>
              </view>
            </view>
          </view>
          
          <view class="festival-item">
            <view class="festival-header">
              <view class="festival-icon" style="background-color: rgba(255, 107, 107, 0.2);">
                <text>双</text>
              </view>
              <text class="festival-name">双11购物节</text>
            </view>
            <view class="festival-content">
              <text class="festival-desc">双11是全球最大的购物狂欢节，红包是引流和促销的核心工具。</text>
              <view class="strategy-points">
                <view class="strategy-point">
                  <text class="point-dot"></text>
                  <text class="point-text">设计"双11"主题的红包封面</text>
                </view>
                <view class="strategy-point">
                  <text class="point-dot"></text>
                  <text class="point-text">推出预售红包、定金膨胀等玩法</text>
                </view>
                <view class="strategy-point">
                  <text class="point-dot"></text>
                  <text class="point-text">设置"秒杀红包"增强紧迫感</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <view class="section-title">
          <view class="title-icon" style="background-color: rgba(255, 107, 107, 0.2);">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#FF6B6B" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="8" x2="12" y2="16"></line>
              <line x1="8" y1="12" x2="16" y2="12"></line>
            </svg>
          </view>
          <text class="title-text">节日红包设计要点</text>
        </view>
        
        <view class="design-points">
          <view class="design-point">
            <view class="point-header">
              <view class="point-icon" style="background-color: rgba(255, 107, 107, 0.2);">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#FF6B6B" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                  <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
                </svg>
              </view>
              <text class="point-title">节日主题化设计</text>
            </view>
            <text class="point-desc">红包的视觉设计应与节日氛围相符，使用节日相关的元素、色彩和图案，增强用户的节日感受。</text>
          </view>
          
          <view class="design-point">
            <view class="point-header">
              <view class="point-icon" style="background-color: rgba(255, 107, 107, 0.2);">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#FF6B6B" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                </svg>
              </view>
              <text class="point-title">差异化红包金额</text>
            </view>
            <text class="point-desc">根据节日特点和用户价值设置不同金额的红包，如春节可设置较大金额，日常节日可设置小额多次的红包。</text>
          </view>
          
          <view class="design-point">
            <view class="point-header">
              <view class="point-icon" style="background-color: rgba(255, 107, 107, 0.2);">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#FF6B6B" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                  <circle cx="9" cy="7" r="4"></circle>
                  <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                </svg>
              </view>
              <text class="point-title">社交裂变传播</text>
            </view>
            <text class="point-desc">设计能够在社交媒体上分享的红包形式，鼓励用户将红包分享给亲友，扩大品牌影响力。</text>
          </view>
          
          <view class="design-point">
            <view class="point-header">
              <view class="point-icon" style="background-color: rgba(255, 107, 107, 0.2);">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#FF6B6B" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                  <line x1="8" y1="21" x2="16" y2="21"></line>
                  <line x1="12" y1="17" x2="12" y2="21"></line>
                </svg>
              </view>
              <text class="point-title">多平台联动</text>
            </view>
            <text class="point-desc">将红包活动与社交媒体、小程序、公众号等多平台联动，形成全渠道的节日营销矩阵。</text>
          </view>
        </view>
        
        <view class="section-title">
          <view class="title-icon" style="background-color: rgba(255, 107, 107, 0.2);">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#FF6B6B" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
          </view>
          <text class="title-text">节日红包投放时机</text>
        </view>
        
        <view class="timing-chart">
          <view class="chart-header">
            <text class="chart-title">节日红包投放时间轴</text>
          </view>
          <view class="timeline">
            <view class="timeline-item">
              <view class="timeline-point"></view>
              <view class="timeline-content">
                <text class="timeline-title">节前预热期</text>
                <text class="timeline-desc">节日前7-15天，发放预热红包，提前引导用户关注</text>
              </view>
            </view>
            <view class="timeline-item">
              <view class="timeline-point"></view>
              <view class="timeline-content">
                <text class="timeline-title">节日预售期</text>
                <text class="timeline-desc">节日前3-7天，发放预售红包，刺激用户提前下单</text>
              </view>
            </view>
            <view class="timeline-item">
              <view class="timeline-point"></view>
              <view class="timeline-content">
                <text class="timeline-title">节日高峰期</text>
                <text class="timeline-desc">节日当天及前后1-2天，发放大额红包，促进转化</text>
              </view>
            </view>
            <view class="timeline-item">
              <view class="timeline-point"></view>
              <view class="timeline-content">
                <text class="timeline-title">节后延续期</text>
                <text class="timeline-desc">节日后3-5天，发放复购红包，延续节日效应</text>
              </view>
            </view>
          </view>
        </view>
        
        <view class="section-title">
          <view class="title-icon" style="background-color: rgba(255, 107, 107, 0.2);">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#FF6B6B" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14 2 14 8 20 8"></polyline>
              <line x1="16" y1="13" x2="8" y2="13"></line>
              <line x1="16" y1="17" x2="8" y2="17"></line>
              <polyline points="10 9 9 9 8 9"></polyline>
            </svg>
          </view>
          <text class="title-text">成功案例分析</text>
        </view>
        
        <view class="case-study">
          <view class="case-header">
            <text class="case-title">某餐饮品牌春节红包案例</text>
          </view>
          <view class="case-content">
            <text class="case-desc">某全国连锁餐饮品牌在春节期间推出"团圆红包"活动，用户可通过扫描餐厅二维码获取红包，并可分享给亲友。红包金额设置为8.8元、18.8元、88.8元等吉利数字，使用门槛为消费满88元。活动期间，该品牌客流量提升了45%，新客获取增长了60%，销售额同比增长了52%。</text>
          </view>
          <view class="case-results">
            <view class="result-item">
              <text class="result-label">客流提升</text>
              <text class="result-value">45%</text>
            </view>
            <view class="result-item">
              <text class="result-label">新客增长</text>
              <text class="result-value">60%</text>
            </view>
            <view class="result-item">
              <text class="result-label">销售增长</text>
              <text class="result-value">52%</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 联系我们 -->
      <view class="contact-section">
        <text class="contact-title">需要更多节日营销方案？</text>
        <text class="contact-desc">我们的专业团队可为您定制节日红包营销方案</text>
        <button class="contact-btn" @click="contactService">获取方案</button>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    shareGuide() {
      uni.showActionSheet({
        itemList: ['分享给好友', '分享到朋友圈', '复制链接'],
        success: function(res) {
          uni.showToast({
            title: '分享成功',
            icon: 'success'
          });
        }
      });
    },
    contactService() {
      uni.makePhoneCall({
        phoneNumber: '************'
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏样式 */
.navbar {
  display: flex;
  align-items: center;
  height: 44px;
  background-color: #fff;
  padding: 0 15px;
  position: relative;
}

.navbar-back {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-top: 2px solid #333;
  border-left: 2px solid #333;
  transform: rotate(-45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 600;
  color: #333;
}

.navbar-right {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.share-icon {
  color: #333;
}

/* 内容滚动区 */
.content-scroll {
  flex: 1;
}

/* 页面头部 */
.page-header {
  height: 180px;
  position: relative;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20px;
}

.header-title {
  font-size: 24px;
  font-weight: 700;
  color: #fff;
  margin-bottom: 10px;
}

.header-subtitle {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
}

/* 内容部分 */
.content-section {
  padding: 20px 15px;
  background-color: #fff;
  border-radius: 15px 15px 0 0;
  margin-top: -20px;
  position: relative;
  z-index: 1;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  margin-top: 25px;
}

.section-title:first-child {
  margin-top: 0;
}

.title-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.title-text {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.content-text {
  font-size: 15px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.content-image {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.content-image image {
  width: 100%;
}

/* 节日列表 */
.festival-list {
  margin-bottom: 20px;
}

.festival-item {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.festival-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.festival-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.festival-icon text {
  font-size: 16px;
  font-weight: 600;
  color: #FF6B6B;
}

.festival-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.festival-content {
  padding-left: 50px;
}

.festival-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 10px;
}

.strategy-points {
  margin-top: 10px;
}

.strategy-point {
  display: flex;
  margin-bottom: 8px;
}

.point-dot {
  width: 6px;
  height: 6px;
  border-radius: 3px;
  background-color: #FF6B6B;
  margin-top: 6px;
  margin-right: 8px;
  flex-shrink: 0;
}

.point-text {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* 设计要点 */
.design-points {
  margin-bottom: 20px;
}

.design-point {
  margin-bottom: 15px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.point-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.point-icon {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.point-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.point-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

/* 时间轴 */
.timing-chart {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.chart-header {
  margin-bottom: 15px;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.timeline {
  position: relative;
  padding-left: 20px;
}

.timeline::before {
  content: '';
  position: absolute;
  top: 0;
  left: 4px;
  height: 100%;
  width: 2px;
  background-color: #FF6B6B;
}

.timeline-item {
  position: relative;
  padding-bottom: 20px;
}

.timeline-item:last-child {
  padding-bottom: 0;
}

.timeline-point {
  position: absolute;
  left: -20px;
  top: 5px;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #FF6B6B;
}

.timeline-content {
  padding-left: 10px;
}

.timeline-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 5px;
}

.timeline-desc {
  font-size: 14px;
  color: #666;
}

/* 案例分析 */
.case-study {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.case-header {
  margin-bottom: 10px;
}

.case-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.case-content {
  margin-bottom: 15px;
}

.case-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.case-results {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #eee;
  padding-top: 15px;
}

.result-item {
  text-align: center;
  flex: 1;
}

.result-label {
  font-size: 12px;
  color: #999;
  display: block;
  margin-bottom: 5px;
}

.result-value {
  font-size: 18px;
  font-weight: 600;
  color: #FF6B6B;
}

/* 联系我们 */
.contact-section {
  margin: 0 15px 30px;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.contact-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.contact-desc {
  font-size: 14px;
  color: #666;
  display: block;
  margin-bottom: 15px;
}

.contact-btn {
  background-color: #FF6B6B;
  color: #fff;
  border: none;
  border-radius: 20px;
  padding: 8px 20px;
  font-size: 14px;
}
</style>