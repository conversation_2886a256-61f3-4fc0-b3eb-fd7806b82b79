/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.ratings-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(90rpx + var(--status-bar-height, 40px));
  padding-bottom: 40rpx;
}

/* 自定义导航栏 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
}
.header-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
}
.left-action, .right-action {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-icon {
  width: 24px;
  height: 24px;
}
.back-icon {
  width: 24px;
  height: 24px;
  /* 图标是黑色的，需要转为白色 */
  filter: brightness(0) invert(1);
}
.title-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 评分统计 */
.rating-summary {
  margin: 20rpx 32rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.summary-left {
  width: 30%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-right: 1px solid #F2F2F7;
  padding-right: 24rpx;
}
.average-score {
  font-size: 48rpx;
  font-weight: 600;
  color: #FF9F0A;
  margin-bottom: 8rpx;
}
.total-ratings {
  font-size: 24rpx;
  color: #8E8E93;
}
.summary-right {
  flex: 1;
  padding-left: 24rpx;
}
.star-row {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.star-level {
  width: 60rpx;
  font-size: 24rpx;
  color: #8E8E93;
}
.progress-bar {
  flex: 1;
  height: 16rpx;
  background-color: #F2F2F7;
  border-radius: 8rpx;
  margin: 0 16rpx;
  overflow: hidden;
}
.progress-fill {
  height: 100%;
  background-color: #FF9F0A;
  border-radius: 8rpx;
}
.star-count {
  width: 40rpx;
  font-size: 24rpx;
  color: #8E8E93;
  text-align: right;
}

/* 标签统计 */
.tags-summary {
  margin: 0 32rpx 32rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
}
.tags-container {
  display: flex;
  flex-wrap: wrap;
}
.tag-item {
  padding: 10rpx 20rpx;
  background-color: #F5F5F5;
  border-radius: 30rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
}
.tag-text {
  font-size: 26rpx;
  color: #333333;
}
.tag-count {
  font-size: 24rpx;
  color: #FF9F0A;
  margin-left: 8rpx;
}

/* 筛选选项 */
.filter-options {
  margin: 0 32rpx 20rpx;
}
.filter-tabs {
  display: flex;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.tab-item {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666666;
  position: relative;
}
.tab-item.active {
  color: #0A84FF;
  font-weight: 500;
}
.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 25%;
  width: 50%;
  height: 4rpx;
  background-color: #0A84FF;
  border-radius: 2rpx;
}

/* 评价列表 */
.ratings-list {
  margin: 0 32rpx;
}
.empty-state {
  padding: 60rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.empty-text {
  font-size: 28rpx;
  color: #8E8E93;
}
.rating-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.rating-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.user-info {
  display: flex;
  align-items: center;
}
.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  margin-right: 12rpx;
}
.user-name {
  font-size: 28rpx;
  color: #333333;
}
.rating-score {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.star-display {
  display: flex;
  margin-bottom: 6rpx;
}
.star-icon-small {
  width: 24rpx;
  height: 24rpx;
  margin-left: 4rpx;
}
.rating-date {
  font-size: 22rpx;
  color: #8E8E93;
}
.rating-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}
.tag-pill {
  padding: 6rpx 16rpx;
  background-color: #F5F5F5;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #666666;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}
.rating-comment {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.5;
  margin-bottom: 16rpx;
}
.trip-info {
  padding-top: 16rpx;
  border-top: 1px solid #F2F2F7;
  display: flex;
  justify-content: space-between;
}
.trip-route {
  font-size: 24rpx;
  color: #666666;
}
.trip-time {
  font-size: 24rpx;
  color: #8E8E93;
}
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  width: 100%;
}