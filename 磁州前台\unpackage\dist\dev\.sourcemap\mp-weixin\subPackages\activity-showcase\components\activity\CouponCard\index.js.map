{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/components/activity/CouponCard/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7Avc3ViUGFja2FnZXMvYWN0aXZpdHktc2hvd2Nhc2UvY29tcG9uZW50cy9hY3Rpdml0eS9Db3Vwb25DYXJkL2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <!-- 优惠券活动卡片 - 苹果风格设计 -->\r\n  <view class=\"coupon-card\">\r\n    <!-- 使用基础活动卡片 -->\r\n    <ActivityCard \r\n      :item=\"item\" \r\n      @favorite=\"$emit('favorite', item.id)\"\r\n      @action=\"$emit('action', { id: item.id, type: item.type, status: item.status })\"\r\n    >\r\n      <!-- 优惠券特有信息插槽 -->\r\n      <template #special-info>\r\n        <view class=\"coupon-special\">\r\n          <!-- 优惠券价值区域 -->\r\n          <view class=\"coupon-value-container\">\r\n            <view class=\"coupon-value-wrapper\">\r\n              <text class=\"value-symbol\" v-if=\"item.couponType === 'cash'\">¥</text>\r\n              <text class=\"value-number\">{{item.couponValue}}</text>\r\n              <text class=\"value-unit\" v-if=\"item.couponType === 'discount'\">折</text>\r\n            </view>\r\n            <view class=\"coupon-type\">\r\n              <text>{{couponTypeText}}</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 使用条件 -->\r\n          <view class=\"coupon-condition\" v-if=\"item.couponCondition\">\r\n            <view class=\"condition-icon\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\r\n                <path d=\"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z\"></path>\r\n              </svg>\r\n            </view>\r\n            <text class=\"condition-text\">{{item.couponCondition}}</text>\r\n          </view>\r\n          \r\n          <!-- 使用范围 -->\r\n          <view class=\"coupon-scope\" v-if=\"item.couponScope\">\r\n            <view class=\"scope-icon\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\r\n                <rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"></rect>\r\n                <line x1=\"9\" y1=\"3\" x2=\"9\" y2=\"21\"></line>\r\n              </svg>\r\n            </view>\r\n            <text class=\"scope-text\">{{item.couponScope}}</text>\r\n          </view>\r\n          \r\n          <!-- 有效期 -->\r\n          <view class=\"coupon-validity\">\r\n            <view class=\"validity-icon\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\r\n                <rect x=\"3\" y=\"4\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"></rect>\r\n                <line x1=\"16\" y1=\"2\" x2=\"16\" y2=\"6\"></line>\r\n                <line x1=\"8\" y1=\"2\" x2=\"8\" y2=\"6\"></line>\r\n                <line x1=\"3\" y1=\"10\" x2=\"21\" y2=\"10\"></line>\r\n              </svg>\r\n            </view>\r\n            <text class=\"validity-text\">有效期至: {{item.couponValidity}}</text>\r\n          </view>\r\n          \r\n          <!-- 领取进度 -->\r\n          <view class=\"coupon-progress\" v-if=\"item.totalCount && item.claimedCount !== undefined\">\r\n            <view class=\"progress-header\">\r\n              <text class=\"progress-title\">领取进度</text>\r\n              <text class=\"progress-status\">{{item.claimedCount}}/{{item.totalCount}}张</text>\r\n            </view>\r\n            <view class=\"progress-bar\">\r\n              <view class=\"progress-inner\" :style=\"{width: progressWidth + '%'}\"></view>\r\n            </view>\r\n            <view class=\"progress-tip\" v-if=\"remainCount > 0\">\r\n              <text class=\"tip-text\">仅剩{{remainCount}}张，先到先得！</text>\r\n            </view>\r\n            <view class=\"progress-tip\" v-else>\r\n              <text class=\"tip-text\">已领完，下次早点来哦~</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </template>\r\n    </ActivityCard>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { computed } from 'vue';\r\nimport ActivityCard from '../ActivityCard.vue';\r\n\r\nconst props = defineProps({\r\n  item: {\r\n    type: Object,\r\n    required: true\r\n  }\r\n});\r\n\r\n// 优惠券类型文本\r\nconst couponTypeText = computed(() => {\r\n  if (!props.item.couponType) return '优惠券';\r\n  \r\n  switch(props.item.couponType) {\r\n    case 'cash':\r\n      return '现金券';\r\n    case 'discount':\r\n      return '折扣券';\r\n    case 'exchange':\r\n      return '兑换券';\r\n    case 'gift':\r\n      return '礼品券';\r\n    default:\r\n      return '优惠券';\r\n  }\r\n});\r\n\r\n// 计算进度条宽度\r\nconst progressWidth = computed(() => {\r\n  if (!props.item.claimedCount || !props.item.totalCount) return 0;\r\n  return (props.item.claimedCount / props.item.totalCount) * 100;\r\n});\r\n\r\n// 计算剩余数量\r\nconst remainCount = computed(() => {\r\n  if (props.item.claimedCount === undefined || !props.item.totalCount) return 0;\r\n  return props.item.totalCount - props.item.claimedCount;\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n/* 优惠券活动卡片特有样式 */\r\n.coupon-card {\r\n  /* 继承基础卡片样式 */\r\n}\r\n\r\n/* 优惠券特有信息区域 */\r\n.coupon-special {\r\n  padding: 20rpx;\r\n  background: linear-gradient(135deg, rgba(255, 149, 0, 0.05), rgba(255, 59, 48, 0.05));\r\n  border-radius: 20rpx;\r\n  margin-bottom: 20rpx;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.coupon-special::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-image: radial-gradient(circle at 10rpx 10rpx, transparent 12rpx, rgba(255, 149, 0, 0.03) 13rpx);\r\n  background-size: 30rpx 30rpx;\r\n  opacity: 0.5;\r\n  z-index: 0;\r\n  pointer-events: none;\r\n}\r\n\r\n/* 优惠券价值区域 */\r\n.coupon-value-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.coupon-value-wrapper {\r\n  display: flex;\r\n  align-items: baseline;\r\n  color: #ff9500;\r\n}\r\n\r\n.value-symbol {\r\n  font-size: 30rpx;\r\n  font-weight: 600;\r\n}\r\n\r\n.value-number {\r\n  font-size: 60rpx;\r\n  font-weight: 700;\r\n  line-height: 1;\r\n}\r\n\r\n.value-unit {\r\n  font-size: 30rpx;\r\n  font-weight: 600;\r\n  margin-left: 4rpx;\r\n}\r\n\r\n.coupon-type {\r\n  margin-top: 8rpx;\r\n  padding: 4rpx 16rpx;\r\n  background-color: rgba(255, 149, 0, 0.1);\r\n  border-radius: 20rpx;\r\n}\r\n\r\n.coupon-type text {\r\n  font-size: 22rpx;\r\n  color: #ff9500;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 使用条件 */\r\n.coupon-condition {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 16rpx;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.condition-icon {\r\n  margin-right: 8rpx;\r\n  color: #ff9500;\r\n}\r\n\r\n.condition-text {\r\n  font-size: 24rpx;\r\n  color: #333333;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 使用范围 */\r\n.coupon-scope {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 16rpx;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.scope-icon {\r\n  margin-right: 8rpx;\r\n  color: #ff9500;\r\n}\r\n\r\n.scope-text {\r\n  font-size: 24rpx;\r\n  color: #333333;\r\n}\r\n\r\n/* 有效期 */\r\n.coupon-validity {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.validity-icon {\r\n  margin-right: 8rpx;\r\n  color: #ff9500;\r\n}\r\n\r\n.validity-text {\r\n  font-size: 24rpx;\r\n  color: #333333;\r\n}\r\n\r\n/* 领取进度 */\r\n.coupon-progress {\r\n  margin-top: 16rpx;\r\n  border-top: 1rpx dashed rgba(255, 149, 0, 0.2);\r\n  padding-top: 16rpx;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.progress-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.progress-title {\r\n  font-size: 24rpx;\r\n  color: #000000;\r\n  font-weight: 500;\r\n}\r\n\r\n.progress-status {\r\n  font-size: 24rpx;\r\n  color: #ff9500;\r\n  font-weight: 500;\r\n}\r\n\r\n.progress-bar {\r\n  height: 10rpx;\r\n  background-color: rgba(255, 149, 0, 0.1);\r\n  border-radius: 5rpx;\r\n  overflow: hidden;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.progress-inner {\r\n  height: 100%;\r\n  background-color: #ff9500;\r\n  border-radius: 5rpx;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.progress-tip {\r\n  margin-top: 8rpx;\r\n}\r\n\r\n.tip-text {\r\n  font-size: 22rpx;\r\n  color: #ff9500;\r\n}\r\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/components/activity/CouponCard/index.vue'\nwx.createComponent(Component)"], "names": ["computed"], "mappings": ";;;;;;;;;;;;AAkFA,MAAM,eAAe,MAAW;;;;;;;;;;AAEhC,UAAM,QAAQ;AAQd,UAAM,iBAAiBA,cAAQ,SAAC,MAAM;AACpC,UAAI,CAAC,MAAM,KAAK;AAAY,eAAO;AAEnC,cAAO,MAAM,KAAK,YAAU;AAAA,QAC1B,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH,CAAC;AAGD,UAAM,gBAAgBA,cAAQ,SAAC,MAAM;AACnC,UAAI,CAAC,MAAM,KAAK,gBAAgB,CAAC,MAAM,KAAK;AAAY,eAAO;AAC/D,aAAQ,MAAM,KAAK,eAAe,MAAM,KAAK,aAAc;AAAA,IAC7D,CAAC;AAGD,UAAM,cAAcA,cAAQ,SAAC,MAAM;AACjC,UAAI,MAAM,KAAK,iBAAiB,UAAa,CAAC,MAAM,KAAK;AAAY,eAAO;AAC5E,aAAO,MAAM,KAAK,aAAa,MAAM,KAAK;AAAA,IAC5C,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtHD,GAAG,gBAAgB,SAAS;"}