{"version": 3, "file": "index.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xpbmRleC52dWU"], "sourcesContent": ["<template>\n  <view class=\"marketing-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">营销中心</text>\n    </view>\n    \n    <!-- 页面内容 -->\n    <scroll-view scroll-y class=\"page-content\">\n    \n    <!-- 常用营销工具 -->\n    <view class=\"tool-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">营销工具</text>\n      </view>\n      \n      <view class=\"tool-grid\">\n        <view class=\"tool-card\" \n          v-for=\"(tool, index) in marketingTools\" \n          :key=\"index\" \n          @tap=\"navigateToTool(tool)\" \n          :class=\"{'hover': hoveredTool === tool.id}\"\n          @mouseenter=\"setHoveredTool(tool.id)\"\n          @mouseleave=\"clearHoveredTool()\">\n          <view class=\"tool-icon-wrap\" :class=\"tool.class\">\n            <view class=\"tool-icon-svg\" v-html=\"tool.svg\"></view>\n          </view>\n          <text class=\"tool-name\" :class=\"{'three-chars': tool.name.length === 3}\">{{tool.name}}</text>\n          <text class=\"tool-desc\">{{tool.description}}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 营销数据概览 -->\n    <view class=\"overview-section\">\n      <view class=\"overview-title\">\n        <text class=\"title-text\">营销效果概览</text>\n        <view class=\"date-picker\">\n          <text class=\"date-text\">{{dateRange}}</text>\n          <view class=\"date-icon\"></view>\n        </view>\n      </view>\n      \n      <view class=\"data-cards\">\n        <view class=\"data-card\">\n          <view class=\"card-content\">\n            <text class=\"card-value\">{{formatNumber(marketingData.revenue)}}</text>\n            <text class=\"card-label\">营销收入 (元)</text>\n          </view>\n          <view class=\"card-trend\" :class=\"marketingData.revenueTrend\">\n            <view class=\"trend-arrow\"></view>\n            <text class=\"trend-value\">{{marketingData.revenueGrowth}}</text>\n          </view>\n        </view>\n        \n        <view class=\"data-card\">\n          <view class=\"card-content\">\n            <text class=\"card-value\">{{marketingData.conversion}}%</text>\n            <text class=\"card-label\">转化率</text>\n          </view>\n          <view class=\"card-trend\" :class=\"marketingData.conversionTrend\">\n            <view class=\"trend-arrow\"></view>\n            <text class=\"trend-value\">{{marketingData.conversionGrowth}}</text>\n          </view>\n        </view>\n        \n        <view class=\"data-card\">\n          <view class=\"card-content\">\n            <text class=\"card-value\">{{marketingData.roi}}%</text>\n            <text class=\"card-label\">投资回报率</text>\n          </view>\n          <view class=\"card-trend\" :class=\"marketingData.roiTrend\">\n            <view class=\"trend-arrow\"></view>\n            <text class=\"trend-value\">{{marketingData.roiGrowth}}</text>\n          </view>\n        </view>\n        \n        <view class=\"data-card\">\n          <view class=\"card-content\">\n            <text class=\"card-value\">{{marketingData.customers}}</text>\n            <text class=\"card-label\">新增客户</text>\n          </view>\n          <view class=\"card-trend\" :class=\"marketingData.customersTrend\">\n            <view class=\"trend-arrow\"></view>\n            <text class=\"trend-value\">{{marketingData.customersGrowth}}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n      \n      <!-- 智能营销助手 -->\n      <view class=\"ai-marketing-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">智能营销助手</text>\n          <text class=\"view-all\" @tap=\"viewAllAiTools\">更多</text>\n        </view>\n        \n        <view class=\"ai-tools-grid\">\n          <!-- 消费趋势分析 -->\n          <view class=\"ai-tool-card\" @tap=\"useAiTool('trend')\">\n            <view class=\"ai-tool-icon blue\">\n              <view class=\"ai-tool-icon-svg\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"#1989FA\">\n                  <path d=\"M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z\"/>\n                </svg>\n              </view>\n            </view>\n            <view class=\"ai-tool-content\">\n              <text class=\"ai-tool-title\">消费趋势分析</text>\n              <text class=\"ai-tool-desc\">近期顾客偏好变化，建议调整商品结构</text>\n            </view>\n          </view>\n          \n          <!-- 竞品价格监测 -->\n          <view class=\"ai-tool-card\" @tap=\"useAiTool('price')\">\n            <view class=\"ai-tool-icon yellow\">\n              <view class=\"ai-tool-icon-svg\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"#FF9500\">\n                  <path d=\"M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z\"/>\n                </svg>\n              </view>\n            </view>\n            <view class=\"ai-tool-content\">\n              <text class=\"ai-tool-title\">竞品价格监测</text>\n              <text class=\"ai-tool-desc\">同类商品市场价格下降5%，建议调整策略</text>\n            </view>\n          </view>\n          \n          <!-- 销售预测模型 -->\n          <view class=\"ai-tool-card\" @tap=\"useAiTool('forecast')\">\n            <view class=\"ai-tool-icon green\">\n              <view class=\"ai-tool-icon-svg\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"#34C759\">\n                  <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n                </svg>\n              </view>\n            </view>\n            <view class=\"ai-tool-content\">\n              <text class=\"ai-tool-title\">销售预测模型</text>\n              <text class=\"ai-tool-desc\">基于历史数据，下月销量预计增长12%</text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 营销工具导航 -->\n    <view class=\"tool-navigation\">\n      <scroll-view scroll-x class=\"tab-scroll\" show-scrollbar=\"false\">\n        <view \n          class=\"tab-item\" \n          v-for=\"(tab, index) in tabList\" \n          :key=\"index\"\n          :class=\"{ active: currentTab === index }\"\n          @tap=\"switchTab(index)\">\n          <text class=\"tab-text\">{{tab}}</text>\n        </view>\n      </scroll-view>\n    </view>\n    \n    <!-- 活动效果图表 (概览标签) -->\n    <block v-if=\"currentTab === 0\">\n      <view class=\"chart-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">活动效果分析</text>\n          <view class=\"filter-dropdown\">\n            <text class=\"selected-value\">最近7天</text>\n            <view class=\"dropdown-arrow\"></view>\n          </view>\n        </view>\n        \n        <view class=\"chart-container\">\n          <view class=\"chart-area\">\n            <!-- Y轴标签 -->\n            <view class=\"y-axis-labels\">\n              <text class=\"y-label\">¥15k</text>\n              <text class=\"y-label\">¥10k</text>\n              <text class=\"y-label\">¥5k</text>\n              <text class=\"y-label\">0</text>\n            </view>\n            \n            <!-- 图表区域 -->\n            <view class=\"chart-grid\">\n              <!-- 水平网格线 -->\n              <view class=\"h-grid-line\" style=\"top: 0%\"></view>\n              <view class=\"h-grid-line\" style=\"top: 33.33%\"></view>\n              <view class=\"h-grid-line\" style=\"top: 66.66%\"></view>\n              <view class=\"h-grid-line\" style=\"top: 100%\"></view>\n              \n              <!-- 垂直网格线和X轴标签 -->\n              <view v-for=\"(item, index) in chartData\" :key=\"index\" class=\"chart-column\" :style=\"{ left: (index * (100/(chartData.length-1))) + '%' }\">\n                <view class=\"v-grid-line\"></view>\n                <text class=\"x-label\">{{item.date}}</text>\n              </view>\n              \n              <!-- 连线区域 -->\n              <view class=\"chart-lines-container\">\n                <view class=\"revenue-area\" :style=\"{ 'clip-path': revenueClipPath }\"></view>\n                <view class=\"conversion-area\" :style=\"{ 'clip-path': conversionClipPath }\"></view>\n              </view>\n              \n              <!-- 营销收入数据点 -->\n              <view v-for=\"(item, index) in chartData\" :key=\"'rev-'+index\" class=\"data-point revenue\" :style=\"{ left: (index * (100/(chartData.length-1))) + '%', bottom: (item.revenueHeight) + '%' }\"></view>\n              \n              <!-- 转化率数据点 -->\n              <view v-for=\"(item, index) in chartData\" :key=\"'conv-'+index\" class=\"data-point conversion\" :style=\"{ left: (index * (100/(chartData.length-1))) + '%', bottom: (item.conversionHeight) + '%' }\"></view>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"chart-legend\">\n          <view class=\"legend-item\">\n            <view class=\"legend-color revenue\"></view>\n            <text class=\"legend-text\">营销收入</text>\n          </view>\n          <view class=\"legend-item\">\n            <view class=\"legend-color conversion\"></view>\n            <text class=\"legend-text\">转化率</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 进行中的营销活动 -->\n      <view class=\"active-campaigns\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">进行中的活动</text>\n        <text class=\"view-all\" @tap=\"viewAllCampaigns\">查看全部</text>\n      </view>\n      \n      <view class=\"campaign-list\">\n        <view class=\"campaign-item\" v-for=\"(campaign, index) in activeCampaigns\" :key=\"index\" @tap=\"viewCampaignDetail(campaign)\">\n          <view class=\"campaign-status\" :class=\"campaign.statusClass\">{{campaign.status}}</view>\n          <view class=\"campaign-info\">\n            <text class=\"campaign-name\">{{campaign.name}}</text>\n            <view class=\"campaign-meta\">\n            <text class=\"campaign-time\">{{campaign.timeRange}}</text>\n            </view>\n          </view>\n          <view class=\"campaign-metrics\">\n            <view class=\"metric-item\">\n              <text class=\"metric-value\">{{campaign.views}}</text>\n              <text class=\"metric-label\">浏览</text>\n            </view>\n            <view class=\"metric-item\">\n              <text class=\"metric-value\">{{campaign.conversions}}</text>\n              <text class=\"metric-label\">转化</text>\n            </view>\n            <view class=\"metric-item\">\n              <text class=\"metric-value\">¥{{campaign.revenue}}</text>\n              <text class=\"metric-label\">收入</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 高级营销功能 -->\n    <view class=\"advanced-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">高级营销功能</text>\n      </view>\n      \n      <view class=\"advanced-tools\">\n        <view class=\"advanced-tool\" v-for=\"(tool, index) in advancedTools\" :key=\"index\" @tap=\"navigateToAdvancedTool(tool)\">\n          <view class=\"tool-top\">\n            <view class=\"tool-icon\" :class=\"tool.class\">\n              <image class=\"icon-image\" :src=\"tool.icon\"></image>\n            </view>\n            <view class=\"tool-info\">\n              <text class=\"tool-title\">{{tool.title}}</text>\n              <text class=\"tool-subtitle\">{{tool.subtitle}}</text>\n            </view>\n          </view>\n          <text class=\"tool-description\">{{tool.description}}</text>\n          <view class=\"tool-tags\">\n            <view class=\"tool-tag\" v-for=\"(tag, tagIndex) in tool.tags\" :key=\"tagIndex\">\n              {{tag}}\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 营销日历 -->\n    <view class=\"calendar-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">营销日历</text>\n        <text class=\"calendar-month\">2023年4月</text>\n      </view>\n      \n      <view class=\"calendar-events\">\n        <view class=\"event-item\" v-for=\"(event, index) in calendarEvents\" :key=\"index\">\n          <view class=\"event-date\">\n            <text class=\"date-day\">{{event.day}}</text>\n            <text class=\"date-month\">{{event.month}}</text>\n          </view>\n          <view class=\"event-content\">\n            <text class=\"event-title\">{{event.title}}</text>\n            <view class=\"event-info\">\n              <view class=\"event-type\" :class=\"event.typeClass\">{{event.type}}</view>\n              <text class=\"event-time\">{{event.time}}</text>\n            </view>\n          </view>\n          <view class=\"event-action\" @tap.stop=\"manageEvent(event)\">\n            <text class=\"action-text\">管理</text>\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"add-event-btn\" @tap=\"addNewEvent\">\n        <view class=\"plus-icon\"></view>\n        <text class=\"btn-text\">添加新活动</text>\n      </view>\n    </view>\n    </block>\n    \n    <!-- 优惠券标签内容 -->\n    <view v-if=\"currentTab === 3\" class=\"tab-content\">\n      <view class=\"tool-detail-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">优惠券管理</text>\n          <view class=\"add-tool-btn\" @tap=\"createCoupon\">\n            <text class=\"btn-text\">创建优惠券</text>\n            <view class=\"plus-icon-small\"></view>\n          </view>\n        </view>\n        \n        <view class=\"tool-cards\">\n          <view class=\"coupon-card\" v-for=\"(item, index) in couponsList\" :key=\"index\" @tap=\"viewCouponDetail(item)\">\n            <view class=\"coupon-header\">\n              <text class=\"coupon-title\">{{item.title}}</text>\n              <view class=\"coupon-status\" :class=\"'status-'+item.status\">{{item.statusText}}</view>\n            </view>\n            <view class=\"coupon-value\">\n              <text class=\"discount-symbol\">¥</text>\n              <text class=\"discount-amount\">{{item.value}}</text>\n            </view>\n            <view class=\"coupon-info\">\n              <text class=\"coupon-condition\">满{{item.minSpend}}元可用</text>\n              <text class=\"coupon-date\">有效期至: {{item.expireDate}}</text>\n            </view>\n            <view class=\"coupon-stats\">\n              <view class=\"stat-item\">\n                <text class=\"stat-value\">{{item.usedCount}}/{{item.totalCount}}</text>\n                <text class=\"stat-label\">已使用/总数</text>\n              </view>\n              <view class=\"stat-item\">\n                <text class=\"stat-value\">{{item.conversionRate}}%</text>\n                <text class=\"stat-label\">转化率</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 满减标签内容 -->\n    <view v-if=\"currentTab === 4\" class=\"tab-content\">\n      <view class=\"tool-detail-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">满减活动管理</text>\n          <view class=\"add-tool-btn\" @tap=\"createDiscount\">\n            <text class=\"btn-text\">创建满减</text>\n            <view class=\"plus-icon-small\"></view>\n          </view>\n        </view>\n        \n        <view class=\"discount-list\">\n          <view class=\"discount-item\" v-for=\"(item, index) in discountList\" :key=\"index\" @tap=\"viewDiscountDetail(item)\">\n            <view class=\"discount-content\">\n              <text class=\"discount-title\">{{item.title}}</text>\n              <view class=\"discount-rules\">\n                <view class=\"rule-item\" v-for=\"(rule, ruleIndex) in item.rules\" :key=\"ruleIndex\">\n                  <text class=\"rule-text\">满{{rule.minAmount}}减{{rule.discountAmount}}</text>\n                </view>\n              </view>\n              <text class=\"discount-time\">{{item.timeRange}}</text>\n            </view>\n            <view class=\"discount-stats\">\n              <view class=\"stat-row\">\n                <text class=\"stat-label\">使用次数:</text>\n                <text class=\"stat-value\">{{item.usageCount}}次</text>\n              </view>\n              <view class=\"stat-row\">\n                <text class=\"stat-label\">优惠金额:</text>\n                <text class=\"stat-value\">¥{{item.totalDiscount}}</text>\n              </view>\n            </view>\n            <view class=\"item-arrow\"></view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 拼团标签内容 -->\n    <view v-if=\"currentTab === 1\" class=\"tab-content\">\n      <view class=\"empty-state\">\n        <view class=\"empty-icon\"></view>\n        <text class=\"empty-text\">暂无拼团活动</text>\n        <view class=\"empty-action\" @tap=\"createGroup\">\n          <text class=\"action-text\">创建拼团活动</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 秒杀标签内容 -->\n    <view v-if=\"currentTab === 5\" class=\"tab-content\">\n      <view class=\"empty-state\">\n        <view class=\"empty-icon\"></view>\n        <text class=\"empty-text\">暂无秒杀活动</text>\n        <view class=\"empty-action\" @tap=\"createFlash\">\n          <text class=\"action-text\">创建秒杀活动</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 分销标签内容 -->\n    <view v-if=\"currentTab === 2\" class=\"tab-content\">\n      <view class=\"distribution-overview\">\n        <view class=\"overview-card\">\n          <view class=\"overview-header\">\n            <text class=\"overview-title\">分销概览</text>\n            <view class=\"overview-period\">本月</view>\n          </view>\n          <view class=\"overview-stats\">\n            <view class=\"stat-box\">\n              <text class=\"stat-number\">{{distributionStats.distributors}}</text>\n              <text class=\"stat-desc\">总分销员</text>\n            </view>\n            <view class=\"stat-box\">\n              <text class=\"stat-number\">¥{{distributionStats.commission}}</text>\n              <text class=\"stat-desc\">总佣金</text>\n            </view>\n            <view class=\"stat-box\">\n              <text class=\"stat-number\">{{distributionStats.orders}}</text>\n              <text class=\"stat-desc\">分销订单</text>\n            </view>\n          </view>\n        </view>\n        <view class=\"distribution-settings\" @tap=\"manageDistribution\">\n          <text class=\"settings-text\">管理分销设置</text>\n          <view class=\"settings-arrow\"></view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 营销建议 -->\n    <view class=\"ai-insights\">\n      <view class=\"ai-header\">\n        <view class=\"ai-badge\">AI</view>\n        <text class=\"ai-title\">智能营销建议</text>\n      </view>\n      \n      <view class=\"insights-list\">\n        <view class=\"insight-item\" v-for=\"(insight, index) in marketingInsights\" :key=\"index\">\n          <view class=\"insight-icon\" :class=\"insight.iconType\"></view>\n          <view class=\"insight-content\">\n            <text class=\"insight-title\">{{insight.title}}</text>\n            <text class=\"insight-desc\">{{insight.description}}</text>\n          </view>\n          <view class=\"insight-actions\">\n            <view class=\"action-btn primary\" @tap=\"applyInsight(insight)\">应用</view>\n            <view class=\"action-btn secondary\" @tap=\"ignoreInsight(insight)\">忽略</view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 浮动添加按钮 -->\n    <view class=\"floating-action-button\" @tap=\"createNewCampaign\">\n      <view class=\"fab-icon\">+</view>\n      </view>\n      \n      <!-- 底部空间，防止内容被底部导航栏遮挡 -->\n      <view class=\"bottom-space\"></view>\n    </scroll-view>\n    \n    <!-- 页面底部导航栏 -->\n    <view class=\"tab-bar\">\n      <view \n        v-for=\"(tab, index) in visibleTabs\" \n        :key=\"index\"\n        class=\"tab-item\"\n        :class=\"{ active: navTab === tab.id }\"\n        @tap=\"switchNavTab(tab.id)\"\n      >\n        <view class=\"tab-icon\" :class=\"tab.icon\"></view>\n        <text class=\"tab-text\">{{ tab.text }}</text>\n        <view class=\"active-indicator\" v-if=\"navTab === tab.id\"></view>\n      </view>\n    </view>\n\n    <!-- 浮动操作按钮 -->\n    <view class=\"floating-action-button\" @tap=\"showActionMenu\">\n      <view class=\"fab-content\">\n        <view class=\"fab-icon-plus\"></view>\n        <text class=\"fab-text\">发布</text>\n      </view>\n    </view>\n\n    <!-- 底部占位 -->\n    <view class=\"safe-area-bottom\"></view>\n    \n    <!-- 底部导航栏 -->\n    <view class=\"tab-bar\">\n      <view \n        class=\"tab-item\" \n        v-for=\"(tab, index) in visibleTabs\" \n        :key=\"index\"\n        :class=\"{ active: navTab === tab.id }\"\n        @tap=\"switchNavTab(tab.id)\">\n        <view class=\"active-indicator\" v-if=\"navTab === tab.id\"></view>\n        <view class=\"tab-icon\" :class=\"tab.icon\"></view>\n        <text class=\"tab-text\">{{ tab.text }}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\n// 移除TabBar导入\nimport { ref, reactive, onMounted, computed } from 'vue';\n\nexport default {\n  name: 'MarketingCenter',\n  // 移除TabBar组件注册\n  components: {\n  },\n  data() {\n    return {\n      currentTab: 2, // 当前是营销中心\n      hoveredTool: null,\n      dateRange: '2023-04-01 ~ 2023-04-15',\n      currentTab: 0, // 当前选中的内容标签（概览、优惠券、满减等）\n      navTab: 2, // 底部导航栏当前选中标签为营销中心\n      tabList: ['概览', '拼团', '分销', '优惠券', '满减', '秒杀', '会员'],\n      \n      // 图表数据\n      chartData: [\n        { date: '4/10', revenue: 8500, conversion: 3.2, revenueHeight: 35, conversionHeight: 32 },\n        { date: '4/11', revenue: 10200, conversion: 3.8, revenueHeight: 42, conversionHeight: 38 },\n        { date: '4/12', revenue: 9800, conversion: 3.6, revenueHeight: 40, conversionHeight: 36 },\n        { date: '4/13', revenue: 12400, conversion: 4.5, revenueHeight: 51, conversionHeight: 45 },\n        { date: '4/14', revenue: 15000, conversion: 5.2, revenueHeight: 62, conversionHeight: 52 },\n        { date: '4/15', revenue: 13600, conversion: 4.8, revenueHeight: 56, conversionHeight: 48 },\n        { date: '4/16', revenue: 14800, conversion: 5.1, revenueHeight: 60, conversionHeight: 51 }\n      ],\n      \n      // 优惠券列表\n      couponsList: [\n        {\n          id: 1,\n          title: '新客专享优惠',\n          status: 'active',\n          statusText: '进行中',\n          value: 10,\n          minSpend: 100,\n          expireDate: '2023-05-15',\n          usedCount: 234,\n          totalCount: 500,\n          conversionRate: 46.8\n        },\n        {\n          id: 2,\n          title: '满减优惠券',\n          status: 'active',\n          statusText: '进行中',\n          value: 20,\n          minSpend: 200,\n          expireDate: '2023-05-20',\n          usedCount: 156,\n          totalCount: 300,\n          conversionRate: 52.0\n        },\n        {\n          id: 3,\n          title: '节日特别券',\n          status: 'upcoming',\n          statusText: '未开始',\n          value: 50,\n          minSpend: 300,\n          expireDate: '2023-06-10',\n          usedCount: 0,\n          totalCount: 200,\n          conversionRate: 0\n        }\n      ],\n      \n      // 满减活动列表\n      discountList: [\n        {\n          id: 1,\n          title: '春季促销活动',\n          rules: [\n            { minAmount: 100, discountAmount: 10 },\n            { minAmount: 200, discountAmount: 25 },\n            { minAmount: 300, discountAmount: 50 }\n          ],\n          timeRange: '2023-04-01 ~ 2023-04-30',\n          usageCount: 352,\n          totalDiscount: 8562.50\n        },\n        {\n          id: 2,\n          title: '周末特惠',\n          rules: [\n            { minAmount: 150, discountAmount: 15 },\n            { minAmount: 300, discountAmount: 40 }\n          ],\n          timeRange: '每周五至周日',\n          usageCount: 126,\n          totalDiscount: 3240.00\n        }\n      ],\n      \n      // 分销数据\n      distributionStats: {\n        distributors: 128,\n        commission: '15,682.50',\n        orders: 356\n      },\n      \n      marketingData: {\n        revenue: 23586.50,\n        revenueTrend: 'up',\n        revenueGrowth: '15.2%',\n        conversion: 5.8,\n        conversionTrend: 'up',\n        conversionGrowth: '0.8%',\n        roi: 286,\n        roiTrend: 'up',\n        roiGrowth: '23%',\n        customers: 168,\n        customersTrend: 'up',\n        customersGrowth: '12%'\n      },\n      \n      activeCampaigns: [\n        {\n          id: 1,\n          name: '春季新品5折优惠',\n          status: '进行中',\n          statusClass: 'active',\n          timeRange: '2023-04-01 ~ 2023-04-20',\n          views: 3862,\n          conversions: 215,\n          revenue: 15632.50\n        },\n        {\n          id: 2,\n          name: '满300减50活动',\n          status: '即将结束',\n          statusClass: 'ending',\n          timeRange: '2023-04-05 ~ 2023-04-18',\n          views: 2451,\n          conversions: 128,\n          revenue: 7954.00\n        },\n        {\n          id: 3,\n          name: '会员专享折扣',\n          status: '进行中',\n          statusClass: 'active',\n          timeRange: '2023-04-10 ~ 2023-04-30',\n          views: 1752,\n          conversions: 86,\n          revenue: 4328.00\n        }\n      ],\n      \n      marketingTools: [\n        {\n          id: 1,\n          name: '优惠券',\n          description: '创建多样化的优惠券',\n          icon: 'coupon',\n          class: 'coupon',\n          path: '/coupon/management',\n          svg: '<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M20 12v8a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2v-8\"/><path d=\"M18 5.5V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v1.5\"/><path d=\"M2 12h20\"/><path d=\"M12 2v20\"/><path d=\"M8 16h.01\"/><path d=\"M16 16h.01\"/><path d=\"M8 19h.01\"/><path d=\"M16 19h.01\"/></svg>'\n        },\n        {\n          id: 2,\n          name: '满减活动',\n          description: '设置满额减免活动',\n          icon: 'discount',\n          class: 'discount',\n          path: '/discount/management',\n          directPath: '/subPackages/merchant-admin-marketing/pages/marketing/discount/management',\n          svg: '<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m9 15-5-5 5-5\"/><path d=\"M4 10h16\"/><path d=\"M15 9v6\"/><path d=\"M12 15h6\"/><circle cx=\"9\" cy=\"20\" r=\"1\"/><circle cx=\"20\" cy=\"20\" r=\"1\"/></svg>'\n        },\n        {\n          id: 3,\n          name: '拼团活动',\n          description: '创建团购优惠活动',\n          icon: 'group',\n          class: 'group',\n          path: '/group/management',\n          svg: '<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><circle cx=\"17\" cy=\"5\" r=\"3\"/><circle cx=\"7\" cy=\"5\" r=\"3\"/><circle cx=\"17\" cy=\"19\" r=\"3\"/><circle cx=\"7\" cy=\"19\" r=\"3\"/><path d=\"M8 14h8\"/><path d=\"M12 8v6\"/></svg>'\n        },\n        {\n          id: 4,\n          name: '限时秒杀',\n          description: '限时限量特价活动',\n          icon: 'flash',\n          class: 'flash',\n          path: '/flash/management',\n          directPath: '/subPackages/merchant-admin-marketing/pages/marketing/flash/management',\n          svg: '<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M13 2L3 14h9l-1 8 10-12h-9l1-8z\"/></svg>'\n        },\n        {\n          id: 5,\n          name: '积分商城',\n          description: '设置积分兑换商品',\n          icon: 'points',\n          class: 'points',\n          path: '/points/management',\n          svg: '<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polygon points=\"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2\"/></svg>'\n        },\n        {\n          id: 6,\n          name: '分销系统',\n          description: '设置分销规则与佣金',\n          icon: 'distribution',\n          class: 'distribution',\n          path: '/distribution/index',\n          svg: '<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M12 5v14\"/><path d=\"M5 12h14\"/><path d=\"M19 5v14\"/><path d=\"M5 5v14\"/></svg>'\n        },\n        {\n          id: 7,\n          name: '会员特权',\n          description: '设置会员专属优惠',\n          icon: 'member',\n          class: 'member',\n          path: '/member/index',\n          svg: '<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"/><circle cx=\"9\" cy=\"7\" r=\"4\"/><path d=\"M23 21v-2a4 4 0 0 0-3-3.87\"/><path d=\"M16 3.13a4 4 0 0 1 0 7.75\"/></svg>'\n        },\n        {\n          id: 8,\n          name: '红包营销',\n          description: '发放现金红包活动',\n          icon: 'redpacket',\n          class: 'redpacket',\n          path: '/redpacket/index',\n          svg: '<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M4 4h16a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2z\"/><path d=\"M4 14h16a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-2a2 2 0 0 1 2-2z\"/><path d=\"M2 10h20\"/></svg>'\n        },\n        {\n          id: 9,\n          name: '营销自动化',\n          description: '自动触发的营销流程',\n          icon: 'automation',\n          class: 'automation',\n          path: '/automation/index',\n          svg: '<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z\"/></svg>'\n        }\n      ],\n      \n      advancedTools: [\n        {\n          id: 1,\n          title: '智能营销助手',\n          subtitle: 'AI驱动的营销策划工具',\n          description: '基于历史数据和行业洞察，智能生成最优营销策略与执行方案',\n          icon: '/static/images/ai-marketing.png',\n          class: 'ai',\n          tags: ['AI驱动', '数据分析', '自动优化'],\n          path: '/ai/index'\n        },\n        {\n          id: 2,\n          title: '客户群体细分',\n          subtitle: '精准划分客户群体',\n          description: '对客户进行多维度分析与分类，实现更精准的营销触达',\n          icon: '/static/images/user-segment.png',\n          class: 'segment',\n          tags: ['客户分析', '精准营销', '提升转化'],\n          path: '/segment/index'\n        },\n        {\n          id: 3,\n          title: '营销自动化',\n          subtitle: '设置自动触发营销流',\n          description: '基于客户行为自动触发相应营销动作，提高运营效率',\n          icon: '/static/images/marketing-automation.png',\n          class: 'automation',\n          tags: ['自动触发', '多渠道', '效率提升'],\n          path: '/automation/index'\n        }\n      ],\n      \n      calendarEvents: [\n        {\n          id: 1,\n          day: '15',\n          month: '4月',\n          title: '春季新品发布会',\n          type: '新品发布',\n          typeClass: 'new-product',\n          time: '10:00 - 12:00'\n        },\n        {\n          id: 2,\n          day: '18',\n          month: '4月',\n          title: '限时特惠活动',\n          type: '折扣活动',\n          typeClass: 'discount',\n          time: '全天'\n        },\n        {\n          id: 3,\n          day: '22',\n          month: '4月',\n          title: '会员专享日',\n          type: '会员活动',\n          typeClass: 'member',\n          time: '全天'\n        }\n      ],\n      \n      marketingInsights: [\n        {\n          id: 1,\n          iconType: 'insight',\n          title: '客群扩展机会',\n          description: '您当前的25-35岁女性客户转化率较高，建议针对此群体增加营销预算，预计可提高20%销售额',\n        },\n        {\n          id: 2,\n          iconType: 'warning',\n          title: '活动优化建议',\n          description: '满300减50活动的转化率低于平均水平，建议调整为满200减40，预计可提高15%的转化',\n        },\n        {\n          id: 3,\n          iconType: 'opportunity',\n          title: '节日营销机会',\n          description: '五一假期即将到来，根据往年数据，建议提前7天开始促销活动，预计可增加30%的销售额',\n        }\n      ],\n      \n      // 底部导航栏显示的导航项（前4个核心功能 + 更多）\n      visibleTabs: [\n        {\n          id: 0,\n          icon: 'dashboard',\n          text: '商家中心',\n          url: '/subPackages/merchant-admin-home/pages/merchant-home/index'\n        },\n        {\n          id: 1,\n          icon: 'store',\n          text: '店铺管理',\n          url: '/subPackages/merchant-admin/pages/store/index'\n        },\n        {\n          id: 2,\n          icon: 'marketing',\n          text: '营销中心',\n          url: '/subPackages/merchant-admin-marketing/pages/marketing/index'\n        },\n        {\n          id: 3,\n          icon: 'orders',\n          text: '订单管理',\n          url: '/subPackages/merchant-admin-order/pages/order/index'\n        },\n        {\n          id: 'more',\n          icon: 'more',\n          text: '更多',\n          url: ''\n        }\n      ]\n    }\n  },\n  computed: {\n    // 计算营销收入区域路径\n    revenueClipPath() {\n      const points = this.chartData.map((item, index) => {\n        const x = index * (100/(this.chartData.length-1));\n        const y = 100 - item.revenueHeight;\n        return `${x}% ${y}%`;\n      });\n      \n      // 添加首尾连接点，形成多边形\n      const lastX = 100;\n      const lastY = 100;\n      const firstX = 0;\n      const firstY = 100;\n      \n      return `polygon(${points.join(', ')}, ${lastX}% ${lastY}%, ${firstX}% ${firstY}%)`;\n    },\n    \n    // 计算转化率区域路径\n    conversionClipPath() {\n      const points = this.chartData.map((item, index) => {\n        const x = index * (100/(this.chartData.length-1));\n        const y = 100 - item.conversionHeight;\n        return `${x}% ${y}%`;\n      });\n      \n      // 添加首尾连接点，形成多边形\n      const lastX = 100;\n      const lastY = 100;\n      const firstX = 0;\n      const firstY = 100;\n      \n      return `polygon(${points.join(', ')}, ${lastX}% ${lastY}%, ${firstX}% ${firstY}%)`;\n    }\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack();\n    },\n    formatNumber(number) {\n      return number.toFixed(2).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\n    },\n    switchTab(index) {\n      this.currentTab = index;\n    },\n    switchNavTab(tabId) {\n      // 处理\"更多\"选项\n      if (tabId === 'more') {\n        this.showMoreOptions();\n        return;\n      }\n      \n      // 如果点击的是当前选中的标签，则不进行跳转\n      if (tabId === this.navTab) return;\n      \n      this.navTab = tabId;\n      \n      // 特殊处理营销中心标签\n      if (tabId === 2) {\n        // 当前已经在营销中心页面，不需要跳转\n        return;\n      }\n      \n      // 使用redirectTo而不是navigateTo，避免堆栈过多\n      uni.redirectTo({\n        url: this.visibleTabs[tabId].url,\n        fail: (err) => {\n          console.error('redirectTo失败:', err);\n          // 如果redirectTo失败，尝试使用switchTab\n          uni.switchTab({\n            url: this.visibleTabs[tabId].url,\n            fail: (switchErr) => {\n              console.error('switchTab也失败:', switchErr);\n              uni.showToast({\n                title: '页面跳转失败，请稍后再试',\n                icon: 'none'\n              });\n            }\n          });\n        }\n      });\n    },\n    viewAllCampaigns() {\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/campaign-list'\n      });\n    },\n    viewCampaignDetail(campaign) {\n      uni.navigateTo({\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/campaign-detail?id=${campaign.id}`\n      });\n    },\n    navigateToTool(tool) {\n      // 使用Vue3的Composition API进行路由导航\n      let route = '';\n      \n      // 根据工具类型确定正确的路由路径\n      switch(tool.id) {\n        case 2: // 满减活动\n          route = '/subPackages/merchant-admin-marketing/pages/marketing/discount/management';\n          break;\n        case 4: // 限时秒杀\n          route = `/subPackages/merchant-admin-marketing/pages/marketing${tool.path}`;\n          break;\n        case 6: // 分销系统\n          route = '/subPackages/merchant-admin-marketing/pages/marketing/distribution/index';\n          break;\n        case 7: // 会员特权\n          route = '/subPackages/merchant-admin-marketing/pages/marketing/member/index';\n          break;\n        case 8: // 红包营销\n          route = '/subPackages/merchant-admin-marketing/pages/marketing/redpacket/index';\n          break;\n        case 9: // 核销中心\n          route = '/subPackages/merchant-admin-marketing/pages/marketing/verification/index';\n          break;\n        default:\n          // 其他工具使用原有路径\n          route = `/subPackages/merchant-admin-marketing/pages/marketing${tool.path}`;\n      }\n      \n      // 添加过渡动画参数\n      uni.navigateTo({\n        url: route,\n        animationType: 'slide-in-right',\n        animationDuration: 300,\n        success: () => {\n          // 使用Vue3的响应式API记录用户行为\n          uni.$emit('marketing-tool-click', {\n            toolId: tool.id,\n            toolName: tool.name,\n            timestamp: Date.now()\n          });\n        },\n        fail: (err) => {\n          console.error('导航失败:', err);\n          // 页面不存在时的优雅降级处理\n          uni.showToast({\n            title: '功能正在建设中',\n            icon: 'none',\n            duration: 2000\n          });\n        }\n      });\n    },\n    navigateToAdvancedTool(tool) {\n      // 确保路径正确\n      let route = `/subPackages/merchant-admin-marketing/pages/marketing${tool.path}`;\n      \n      // 打印路径便于调试\n      console.log('导航路径:', route);\n      \n      uni.navigateTo({\n        url: route,\n        animationType: 'slide-in-right',\n        animationDuration: 300,\n        success: () => {\n          // 记录用户行为\n          uni.$emit('advanced-tool-click', {\n            toolId: tool.id,\n            toolName: tool.title,\n            timestamp: Date.now()\n          });\n        },\n        fail: (err) => {\n          console.error('导航失败:', err);\n          // 页面不存在时的优雅降级处理\n          uni.showToast({\n            title: '高级功能正在建设中',\n            icon: 'none',\n            duration: 2000\n          });\n        }\n      });\n    },\n    manageEvent(event) {\n      uni.navigateTo({\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/event-detail?id=${event.id}`\n      });\n    },\n    addNewEvent() {\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/create-event'\n      });\n    },\n    createNewCampaign() {\n      uni.showActionSheet({\n        itemList: ['创建优惠券', '创建满减活动', '创建拼团活动', '创建秒杀活动', '创建分销计划'],\n        success: (res) => {\n          const urls = [\n            '/subPackages/merchant-admin-marketing/pages/marketing/coupon/create',\n            '/subPackages/merchant-admin-marketing/pages/marketing/discount/create',\n            '/subPackages/merchant-admin-marketing/pages/marketing/group/create',\n            '/subPackages/merchant-admin-marketing/pages/marketing/flash/create',\n            '/subPackages/merchant-admin-marketing/pages/marketing/distribution/create'\n          ];\n          \n          uni.navigateTo({\n            url: urls[res.tapIndex]\n          });\n        }\n      });\n    },\n    applyInsight(insight) {\n      uni.showToast({\n        title: '已应用该建议',\n        icon: 'success'\n      });\n    },\n    ignoreInsight(insight) {\n      uni.showToast({\n        title: '已忽略该建议',\n        icon: 'none'\n      });\n    },\n    \n    // 优惠券相关方法\n    createCoupon() {\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/create-coupon'\n      });\n    },\n    viewCouponDetail(coupon) {\n      uni.navigateTo({\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/coupon-detail?id=${coupon.id}`\n      });\n    },\n    \n    // 满减相关方法\n    createDiscount() {\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/discount/create'\n      });\n    },\n    viewDiscountDetail(discount) {\n      uni.navigateTo({\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/discount/detail?id=${discount.id}`\n      });\n    },\n    \n    // 拼团相关方法\n    createGroup() {\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/create-group'\n      });\n    },\n    \n    // 秒杀相关方法\n    createFlash() {\n      console.log('正在跳转到秒杀活动创建页面');\n      uni.navigateTo({\n        url: '/pages/marketing/flash/create',\n        fail: (err) => {\n          console.error('跳转失败:', err);\n          uni.showToast({\n            title: '跳转失败，请检查路径',\n            icon: 'none'\n          });\n        }\n      });\n    },\n    \n    // 分销相关方法\n    manageDistribution() {\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/index'\n      });\n    },\n    \n    // 工具卡片交互相关方法\n    setHoveredTool(toolId) {\n      this.hoveredTool = toolId;\n    },\n    \n    clearHoveredTool() {\n      this.hoveredTool = null;\n    },\n    \n    // 已用switchNavTab方法替代，此方法不再需要\n    \n    showMoreOptions() {\n      // 准备更多菜单中的选项\n      const moreOptions = ['客户运营', '分析洞察', '系统设置'];\n      \n      uni.showActionSheet({\n        itemList: moreOptions,\n        success: (res) => {\n          // 根据选择的选项进行跳转\n          const routes = [\n            '/subPackages/merchant-admin-customer/pages/customer/index',\n            '/subPackages/merchant-admin/pages/settings/index'\n          ];\n          uni.navigateTo({\n            url: routes[res.tapIndex]\n          });\n        }\n      });\n    },\n    \n    showPublishOptions() {\n      uni.showActionSheet({\n        itemList: ['发布优惠券', '发布满减活动', '发布拼团活动', '发布秒杀活动'],\n        success: (res) => {\n          const actions = [\n            this.createCoupon,\n            this.createDiscount,\n            this.createGroup,\n            this.createFlash\n          ];\n          \n          if (actions[res.tapIndex]) {\n            actions[res.tapIndex]();\n          }\n        }\n      });\n    },\n    \n    // 智能营销助手相关方法\n    viewAllAiTools() {\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/ai/index'\n      });\n    },\n    \n    useAiTool(toolType) {\n      switch(toolType) {\n        case 'trend':\n          uni.navigateTo({\n            url: '/subPackages/merchant-admin-marketing/pages/marketing/ai/trend-analysis'\n          });\n          break;\n        case 'price':\n          uni.navigateTo({\n            url: '/subPackages/merchant-admin-marketing/pages/marketing/ai/price-monitor'\n          });\n          break;\n        case 'forecast':\n          uni.navigateTo({\n            url: '/subPackages/merchant-admin-marketing/pages/marketing/ai/sales-forecast'\n          });\n          break;\n        default:\n          uni.showToast({\n            title: '功能开发中',\n            icon: 'none'\n          });\n      }\n    },\n    \n    showActionMenu() {\n      uni.showActionSheet({\n        itemList: ['发布优惠券', '发布满减活动', '发布拼团活动', '发布秒杀活动'],\n        success: (res) => {\n          const actions = [\n            this.createCoupon,\n            this.createDiscount,\n            this.createGroup,\n            this.createFlash\n          ];\n          \n          if (actions[res.tapIndex]) {\n            actions[res.tapIndex]();\n          }\n        }\n      });\n    },\n    \n    // 添加处理标签页切换的方法\n    handleTabChange(tabId) {\n      console.log('切换到标签:', tabId);\n      this.navTab = tabId === 'marketing' ? 2 : 0;\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.marketing-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  position: relative;\n}\n\n.page-content {\n  height: calc(100vh - 77px); /* 减去导航栏高度 */\n  box-sizing: border-box;\n  padding-bottom: 60px; /* 添加底部空间，避免内容被底部导航栏遮挡 */\n}\n\n.bottom-space {\n  height: 60px; /* 底部导航栏高度 + 一些额外空间 */\n}\n\n/* 浮动操作按钮样式已经在底部导航样式中定义 */\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #FF7600, #FF3C00);\n  color: #fff;\n  padding: 44px 16px 10px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(255, 120, 0, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: bold;\n}\n\n/* 概览部分样式 */\n.overview-section {\n  margin: 15px;\n  background: #fff;\n  border-radius: 12px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n  padding: 15px;\n}\n\n.overview-title {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.title-text {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.date-picker {\n  display: flex;\n  align-items: center;\n  background: #F5F7FA;\n  border-radius: 15px;\n  padding: 5px 10px;\n}\n\n.date-text {\n  font-size: 12px;\n  color: #666;\n  margin-right: 5px;\n}\n\n.date-icon {\n  width: 12px;\n  height: 12px;\n  border-top: 2px solid #666;\n  border-right: 2px solid #666;\n  transform: rotate(135deg);\n}\n\n/* 数据卡片样式 */\n.data-cards {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -7.5px;\n}\n\n.data-card {\n  width: 50%;\n  padding: 7.5px;\n  box-sizing: border-box;\n}\n\n.card-content {\n  background: #F8FAFC;\n  border-radius: 10px;\n  padding: 15px;\n  display: flex;\n  flex-direction: column;\n  border-left: 3px solid #FF7600;\n}\n\n.card-value {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.card-label {\n  font-size: 12px;\n  color: #999;\n}\n\n.card-trend {\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n  font-size: 12px;\n  margin-top: 8px;\n}\n\n.card-trend.up {\n  color: #34C759;\n}\n\n.card-trend.down {\n  color: #FF3B30;\n}\n\n.trend-arrow {\n  width: 0;\n  height: 0;\n  margin-right: 3px;\n}\n\n.card-trend.up .trend-arrow {\n  border-left: 4px solid transparent;\n  border-right: 4px solid transparent;\n  border-bottom: 6px solid #34C759;\n}\n\n.card-trend.down .trend-arrow {\n  border-left: 4px solid transparent;\n  border-right: 4px solid transparent;\n  border-top: 6px solid #FF3B30;\n}\n\n/* 工具导航样式 */\n.tool-navigation {\n  position: sticky;\n  top: 77px; /* 导航栏高度 */\n  z-index: 99;\n  background: #fff;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n}\n\n.tab-scroll {\n  white-space: nowrap;\n  padding: 0 8px;\n  height: 56px; /* 增加高度容纳更大字体 */\n  overflow-x: auto;\n  -webkit-overflow-scrolling: touch; /* 提高iOS滑动体验 */\n  scrollbar-width: none; /* Firefox */\n  -ms-overflow-style: none; /* IE and Edge */\n}\n\n/* 隐藏滚动条 */\n.tab-scroll::-webkit-scrollbar {\n  display: none;\n}\n\n/* 直接给tab-text设置样式，这是实际显示文本的元素 */\n.tool-navigation .tab-scroll .tab-item .tab-text {\n  font-size: 15px !important; /* 调整为15px */\n  font-weight: 700 !important; /* 加粗显示 */\n}\n\n.tool-navigation .tab-scroll .tab-item.active .tab-text {\n  font-size: 16px !important; /* 调整为16px */\n  font-weight: 800 !important; /* 更粗的字体 */\n  color: #FF7600;\n}\n\n/* 增加更多样式特异性来确保生效 */\n.tab-scroll text.tab-text {\n  font-size: 15px !important;\n  font-weight: 700 !important; /* 加粗显示 */\n}\n\n.tab-scroll .tab-item.active text.tab-text {\n  font-size: 16px !important;\n  font-weight: 800 !important; /* 更粗的字体 */\n}\n\n.tool-navigation .tab-item {\n  display: inline-block;\n  padding: 0 12px; /* 稍微减少内边距 */\n  height: 46px; /* 增加高度 */\n  line-height: 46px; /* 增加行高 */\n  color: #666;\n  position: relative;\n  margin: 0 4px; /* 增加标签间距 */\n  transition: all 0.2s ease; /* 添加过渡动画 */\n  text-align: center; /* 文字居中 */\n  -webkit-tap-highlight-color: transparent; /* 去除默认点击高亮 */\n}\n\n.tool-navigation .tab-item.active {\n  color: #FF7600;\n  font-weight: 700; /* 加粗激活的标签 */\n  font-size: 26px !important; /* 使用!important确保样式生效 */\n  text-shadow: 0 1px 2px rgba(255, 118, 0, 0.1); /* 添加文字阴影 */\n}\n\n/* 为第一个和最后一个标签添加特殊间距，让边缘美观 */\n.tool-navigation .tab-item:first-child {\n  margin-left: 2px;\n}\n\n.tool-navigation .tab-item:last-child {\n  margin-right: 2px;\n}\n\n.tab-item.active::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 15px;\n  right: 15px;\n  height: 4px; /* 增加下划线高度 */\n  background: #FF7600;\n  border-radius: 4px 4px 0 0; /* 调整下划线圆角 */\n}\n\n/* 图表部分样式 */\n.chart-section {\n  margin: 0 15px 15px;\n  background: #fff;\n  border-radius: 12px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n  padding: 15px;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.filter-dropdown {\n  display: flex;\n  align-items: center;\n  background: #F5F7FA;\n  border-radius: 15px;\n  padding: 5px 10px;\n}\n\n.selected-value {\n  font-size: 12px;\n  color: #666;\n  margin-right: 5px;\n}\n\n.dropdown-arrow {\n  width: 8px;\n  height: 8px;\n  border-top: 1.5px solid #666;\n  border-right: 1.5px solid #666;\n  transform: rotate(135deg);\n}\n\n.chart-container {\n  height: auto;\n  margin: 10px 0;\n  background-color: #fff;\n  border-radius: 12px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n}\n\n.chart-area {\n  position: relative;\n  height: 220px;\n  margin: 10px 0 30px;\n  background-color: #FCFCFF;\n  border-radius: 8px;\n  border: 1px solid rgba(200, 210, 230, 0.3);\n  padding: 20px;\n  box-sizing: border-box;\n}\n\n.y-axis-labels {\n  position: absolute;\n  left: 10px;\n  top: 10px;\n  bottom: 30px;\n  width: 30px;\n  z-index: 2;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.y-label {\n  font-size: 11px;\n  color: #8E8E93;\n  text-align: right;\n  transform: translateY(-50%);\n}\n\n.chart-grid {\n  position: absolute;\n  left: 45px;\n  right: 15px;\n  top: 10px;\n  bottom: 30px;\n  z-index: 1;\n}\n\n.h-grid-line {\n  position: absolute;\n  left: 0;\n  right: 0;\n  height: 1px;\n  background-color: rgba(200, 210, 230, 0.3);\n}\n\n.chart-column {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  width: 1px;\n  z-index: 2;\n}\n\n.v-grid-line {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  width: 1px;\n  background-color: rgba(200, 210, 230, 0.2);\n}\n\n.x-label {\n  position: absolute;\n  font-size: 11px;\n  color: #8E8E93;\n  transform: translateX(-50%);\n  text-align: center;\n  bottom: -25px;\n  white-space: nowrap;\n}\n\n.chart-lines-container {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 2;\n  pointer-events: none;\n}\n\n.revenue-area {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(to bottom, rgba(255, 118, 0, 0.2), rgba(255, 118, 0, 0.05));\n  opacity: 0.8;\n  z-index: 2;\n  border-top: 2px solid #FF7600;\n}\n\n.conversion-area {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(to bottom, rgba(0, 122, 255, 0.2), rgba(0, 122, 255, 0.05));\n  opacity: 0.8;\n  z-index: 2;\n  border-top: 2px solid #007AFF;\n}\n\n.data-point {\n  position: absolute;\n  width: 10px;\n  height: 10px;\n  border-radius: 50%;\n  transform: translate(-50%, 50%);\n  z-index: 10;\n  background-color: #fff;\n}\n\n.data-point.revenue {\n  border: 2px solid #FF7600;\n  box-shadow: 0 1px 3px rgba(255, 118, 0, 0.2);\n}\n\n.data-point.conversion {\n  border: 2px solid #007AFF;\n  box-shadow: 0 1px 3px rgba(0, 122, 255, 0.2);\n}\n\n.chart-legend {\n  display: flex;\n  justify-content: center;\n  margin-top: 15px;\n}\n\n.legend-item {\n  display: flex;\n  align-items: center;\n  margin: 0 10px;\n}\n\n.legend-color {\n  width: 12px;\n  height: 12px;\n  border-radius: 2px;\n  margin-right: 5px;\n}\n\n.legend-color.revenue {\n  background: #FF7600;\n}\n\n.legend-color.conversion {\n  background: #007AFF;\n}\n\n.legend-text {\n  font-size: 12px;\n  color: #666;\n}\n\n/* 优惠券标签样式 */\n.tab-content {\n  margin: 15px;\n}\n\n.tool-detail-section {\n  background: #fff;\n  border-radius: 12px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n  padding: 15px;\n  margin-bottom: 15px;\n}\n\n.add-tool-btn {\n  display: flex;\n  align-items: center;\n  background: #FF7600;\n  border-radius: 15px;\n  padding: 5px 10px;\n  color: white;\n}\n\n.btn-text {\n  font-size: 13px;\n  margin-right: 5px;\n}\n\n.plus-icon-small {\n  width: 12px;\n  height: 12px;\n  position: relative;\n}\n\n.plus-icon-small:before,\n.plus-icon-small:after {\n  content: '';\n  position: absolute;\n  background: white;\n}\n\n.plus-icon-small:before {\n  width: 12px;\n  height: 2px;\n  top: 5px;\n  left: 0;\n}\n\n.plus-icon-small:after {\n  height: 12px;\n  width: 2px;\n  left: 5px;\n  top: 0;\n}\n\n/* 优惠券卡片样式 */\n.tool-cards {\n  margin-top: 15px;\n}\n\n.coupon-card {\n  background: linear-gradient(135deg, #FFF8F2, #FFFFFF);\n  border-radius: 10px;\n  padding: 15px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 8px rgba(255, 118, 0, 0.1);\n  border-left: 4px solid #FF7600;\n}\n\n.coupon-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.coupon-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.coupon-status {\n  padding: 2px 8px;\n  border-radius: 10px;\n  font-size: 12px;\n  color: white;\n}\n\n.status-active {\n  background: #34C759;\n}\n\n.status-expired {\n  background: #8E8E93;\n}\n\n.status-upcoming {\n  background: #FF9500;\n}\n\n.coupon-value {\n  font-size: 24px;\n  font-weight: bold;\n  color: #FF7600;\n  margin: 10px 0;\n}\n\n.discount-symbol {\n  font-size: 16px;\n  margin-right: 2px;\n}\n\n.coupon-info {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 15px;\n  font-size: 13px;\n  color: #666;\n}\n\n.coupon-stats {\n  display: flex;\n  border-top: 1px dashed #E5E5EA;\n  padding-top: 10px;\n}\n\n.stat-item {\n  flex: 1;\n  text-align: center;\n}\n\n.stat-value {\n  display: block;\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 4px;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #8E8E93;\n}\n\n/* 满减活动样式 */\n.discount-list {\n  margin-top: 15px;\n}\n\n.discount-item {\n  display: flex;\n  background: #FFFFFF;\n  border-radius: 10px;\n  padding: 15px;\n  margin-bottom: 10px;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);\n  border-left: 3px solid #FF7600;\n  position: relative;\n}\n\n.discount-content {\n  flex: 2;\n}\n\n.discount-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 8px;\n}\n\n.discount-rules {\n  display: flex;\n  flex-wrap: wrap;\n  margin-bottom: 8px;\n}\n\n.rule-item {\n  background: rgba(255, 118, 0, 0.1);\n  border-radius: 12px;\n  padding: 3px 8px;\n  margin-right: 8px;\n  margin-bottom: 5px;\n}\n\n.rule-text {\n  font-size: 12px;\n  color: #FF7600;\n  font-weight: 500;\n}\n\n.discount-time {\n  font-size: 12px;\n  color: #8E8E93;\n}\n\n.discount-stats {\n  flex: 1;\n  padding-left: 15px;\n  border-left: 1px dashed #E5E5EA;\n}\n\n.stat-row {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 8px;\n}\n\n.item-arrow {\n  position: absolute;\n  right: 15px;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 10px;\n  height: 10px;\n  border-top: 2px solid #C7C7CC;\n  border-right: 2px solid #C7C7CC;\n  transform: rotate(45deg);\n}\n\n/* 空状态样式 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: #FFFFFF;\n  border-radius: 12px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n  padding: 40px 15px;\n  margin-top: 20px;\n}\n\n.empty-icon {\n  width: 80px;\n  height: 80px;\n  background: rgba(255, 118, 0, 0.1);\n  border-radius: 40px;\n  margin-bottom: 20px;\n  position: relative;\n}\n\n.empty-icon:before,\n.empty-icon:after {\n  content: '';\n  position: absolute;\n  background: #FF7600;\n}\n\n.empty-icon:before {\n  width: 40px;\n  height: 4px;\n  top: 38px;\n  left: 20px;\n}\n\n.empty-icon:after {\n  width: 4px;\n  height: 40px;\n  left: 38px;\n  top: 20px;\n}\n\n.empty-text {\n  font-size: 16px;\n  color: #8E8E93;\n  margin-bottom: 20px;\n}\n\n.empty-action {\n  background: #FF7600;\n  border-radius: 20px;\n  padding: 10px 20px;\n}\n\n.action-text {\n  color: white;\n  font-size: 14px;\n  font-weight: 500;\n}\n\n/* 分销概览样式 */\n.distribution-overview {\n  margin-bottom: 15px;\n}\n\n.overview-card {\n  background: #fff;\n  border-radius: 12px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n  padding: 15px;\n  margin-bottom: 15px;\n}\n\n.overview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.overview-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.overview-period {\n  background: #F5F7FA;\n  padding: 5px 10px;\n  border-radius: 15px;\n  font-size: 12px;\n  color: #666;\n}\n\n.overview-stats {\n  display: flex;\n  justify-content: space-around;\n}\n\n.stat-box {\n  text-align: center;\n}\n\n.stat-number {\n  display: block;\n  font-size: 18px;\n  font-weight: bold;\n  color: #FF7600;\n  margin-bottom: 5px;\n}\n\n.stat-desc {\n  font-size: 13px;\n  color: #8E8E93;\n}\n\n.distribution-settings {\n  background: #fff;\n  border-radius: 12px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n  padding: 15px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.settings-text {\n  font-size: 15px;\n  color: #333;\n}\n\n.settings-arrow {\n  width: 10px;\n  height: 10px;\n  border-top: 2px solid #C7C7CC;\n  border-right: 2px solid #C7C7CC;\n  transform: rotate(45deg);\n}\n\n/* 活动列表样式 */\n.active-campaigns {\n  margin: 0 15px 15px;\n  background: #fff;\n  border-radius: 12px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n  padding: 15px;\n}\n\n.view-all {\n  font-size: 14px;\n  color: #FF7600;\n}\n\n.campaign-list {\n  margin-top: 10px;\n}\n\n.campaign-item {\n  background: #F8FAFC;\n  border-radius: 10px;\n  padding: 15px;\n  margin-bottom: 10px;\n  position: relative;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);\n}\n\n.campaign-status {\n  position: absolute;\n  top: 15px;\n  right: 15px;\n  font-size: 12px;\n  padding: 3px 8px;\n  border-radius: 10px;\n  z-index: 2; /* 确保状态标签在最上层 */\n}\n\n.campaign-status.active {\n  background: rgba(52, 199, 89, 0.1);\n  color: #34C759;\n}\n\n.campaign-status.ending {\n  background: rgba(255, 149, 0, 0.1);\n  color: #FF9500;\n}\n\n.campaign-info {\n  margin-bottom: 15px;\n  position: relative;\n  width: 100%; /* 确保宽度占满，便于管理布局 */\n}\n\n.campaign-name {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n  margin-right: 60px; /* 为状态标签预留空间 */\n  display: block; /* 确保名称单独一行 */\n}\n\n.campaign-meta {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.campaign-time {\n  font-size: 12px;\n  color: #999;\n  display: block; /* 确保时间单独一行 */\n  width: auto; /* 根据内容自动调整宽度 */\n  max-width: 60%; /* 最大宽度限制 */\n  text-overflow: ellipsis; /* 文本溢出显示省略号 */\n  white-space: nowrap; /* 防止换行 */\n  overflow: hidden; /* 隐藏溢出部分 */\n}\n\n.campaign-metrics {\n  display: flex;\n}\n\n.metric-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 5px 0;\n  border-right: 1px solid #f0f0f0;\n}\n\n.metric-item:last-child {\n  border-right: none;\n}\n\n.metric-value {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 3px;\n}\n\n.metric-label {\n  font-size: 12px;\n  color: #999;\n}\n\n/* 营销工具网格 */\n.tool-section {\n  margin: 10px 15px 15px;\n  background: #fff;\n  border-radius: 12px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n  padding: 15px;\n}\n\n.tool-grid {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -7.5px;\n}\n\n.tool-card {\n  width: 25%;\n  padding: 7.5px;\n  box-sizing: border-box;\n  margin-bottom: 15px;\n  transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);\n  position: relative;\n  cursor: pointer;\n  -webkit-tap-highlight-color: transparent;\n}\n\n.tool-card.hover {\n  transform: translateY(-5px) scale(1.03);\n}\n\n.tool-icon-wrap {\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 8px;\n  margin-left: auto;\n  margin-right: auto;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);\n  background: white;\n  position: relative;\n  overflow: hidden;\n}\n\n.tool-card.hover .tool-icon-wrap {\n  transform: scale(1.1);\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);\n}\n\n.tool-icon-wrap::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));\n  border-radius: 50%;\n  z-index: 1;\n}\n\n.tool-icon-svg {\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 2;\n  transition: all 0.3s ease;\n}\n\n.tool-icon-wrap.coupon {\n  background: linear-gradient(135deg, #FF9966, #FF5E62);\n}\n\n.tool-icon-wrap.discount {\n  background: linear-gradient(135deg, #FDEB71, #F8D800);\n}\n\n.tool-icon-wrap.group {\n  background: linear-gradient(135deg, #36D1DC, #5B86E5);\n}\n\n.tool-icon-wrap.flash {\n  background: linear-gradient(135deg, #FF6B6B, #F04172);\n}\n\n.tool-icon-wrap.points {\n  background: linear-gradient(135deg, #38ADAE, #30CE9B);\n}\n\n.tool-icon-wrap.distribution {\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\n}\n\n.tool-icon-wrap.member {\n  background: linear-gradient(135deg, #8E2DE2, #4A00E0);\n}\n\n.tool-icon-wrap.redpacket {\n  background: linear-gradient(135deg, #FF5858, #FF0000);\n}\n\n/* SVG图标样式 */\n.tool-icon-svg {\n  color: #fff;\n}\n\n.tool-name {\n  font-size: 14px;\n  color: #333;\n  text-align: center;\n  margin-bottom: 3px;\n  font-weight: 500;\n  transition: color 0.3s ease;\n  width: 100%;\n  display: inline-block;\n}\n\n.tool-name.three-chars {\n  letter-spacing: 2px;\n  text-indent: 2px;\n}\n\n.tool-card.hover .tool-name {\n  color: #FF7600;\n}\n\n.tool-desc {\n  font-size: 10px;\n  color: #999;\n  text-align: center;\n  height: 28px;\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  transition: color 0.3s ease;\n}\n\n/* 高级营销功能样式 */\n.advanced-section {\n  margin: 0 15px 15px;\n  background: #fff;\n  border-radius: 12px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n  padding: 15px;\n}\n\n.advanced-tools {\n  margin-top: 10px;\n}\n\n.advanced-tool {\n  background: #F8FAFC;\n  border-radius: 10px;\n  padding: 15px;\n  margin-bottom: 10px;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);\n}\n\n.tool-top {\n  display: flex;\n  margin-bottom: 10px;\n}\n\n.tool-icon {\n  width: 50px;\n  height: 50px;\n  border-radius: 25px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  margin-right: 15px;\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);\n}\n\n.tool-icon.ai {\n  background: linear-gradient(135deg, #9040FF, #5E35B1);\n}\n\n.tool-icon.segment {\n  background: linear-gradient(135deg, #4481EB, #04BEFE);\n}\n\n.tool-icon.automation {\n  background: linear-gradient(135deg, #FFB74D, #FF9800);\n}\n\n.icon-image {\n  width: 24px;\n  height: 24px;\n  object-fit: contain;\n}\n\n.tool-info {\n  flex: 1;\n}\n\n.tool-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.tool-subtitle {\n  font-size: 12px;\n  color: #666;\n}\n\n.tool-description {\n  font-size: 14px;\n  color: #666;\n  line-height: 1.5;\n  margin-bottom: 10px;\n}\n\n.tool-tags {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.tool-tag {\n  font-size: 10px;\n  color: #FF7600;\n  background: rgba(255, 118, 0, 0.1);\n  padding: 3px 8px;\n  border-radius: 10px;\n  margin-right: 8px;\n  margin-bottom: 5px;\n}\n\n/* 营销日历样式 */\n.calendar-section {\n  margin: 0 15px 15px;\n  background: #fff;\n  border-radius: 12px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n  padding: 15px;\n}\n\n.calendar-month {\n  font-size: 14px;\n  color: #999;\n}\n\n.calendar-events {\n  margin-top: 10px;\n}\n\n.event-item {\n  display: flex;\n  align-items: center;\n  padding: 15px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.event-item:last-child {\n  border-bottom: none;\n}\n\n.event-date {\n  width: 50px;\n  height: 50px;\n  background: #F8FAFC;\n  border-radius: 10px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  margin-right: 15px;\n  flex-shrink: 0;\n}\n\n.date-day {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n}\n\n.date-month {\n  font-size: 12px;\n  color: #999;\n}\n\n.event-content {\n  flex: 1;\n  margin-right: 15px;\n}\n\n.event-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.event-info {\n  display: flex;\n  align-items: center;\n}\n\n.event-type {\n  font-size: 10px;\n  padding: 2px 8px;\n  border-radius: 10px;\n  margin-right: 10px;\n}\n\n.event-type.new-product {\n  background: rgba(52, 199, 89, 0.1);\n  color: #34C759;\n}\n\n.event-type.discount {\n  background: rgba(255, 69, 58, 0.1);\n  color: #FF453A;\n}\n\n.event-type.member {\n  background: rgba(88, 86, 214, 0.1);\n  color: #5856D6;\n}\n\n.event-time {\n  font-size: 12px;\n  color: #999;\n}\n\n.event-action {\n  background: #F8FAFC;\n  border-radius: 15px;\n  padding: 5px 10px;\n}\n\n.action-text {\n  font-size: 12px;\n  color: #FF7600;\n}\n\n.add-event-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 10px 0;\n  margin-top: 10px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.plus-icon {\n  width: 16px;\n  height: 16px;\n  background: #FF7600;\n  border-radius: 8px;\n  position: relative;\n  margin-right: 8px;\n}\n\n.plus-icon::before {\n  content: '';\n  position: absolute;\n  width: 8px;\n  height: 2px;\n  background: #fff;\n  top: 7px;\n  left: 4px;\n}\n\n.plus-icon::after {\n  content: '';\n  position: absolute;\n  width: 2px;\n  height: 8px;\n  background: #fff;\n  top: 4px;\n  left: 7px;\n}\n\n.btn-text {\n  font-size: 14px;\n  color: #FF7600;\n}\n\n/* AI营销建议样式 */\n.ai-insights {\n  margin: 0 15px 15px;\n  background: #fff;\n  border-radius: 12px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n  padding: 15px;\n}\n\n.ai-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.ai-badge {\n  background: linear-gradient(135deg, #9040FF, #5E35B1);\n  color: #fff;\n  font-size: 12px;\n  font-weight: bold;\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 8px;\n}\n\n.ai-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.insights-list {\n  margin-top: 10px;\n}\n\n.insight-item {\n  background: #F8FAFC;\n  border-radius: 10px;\n  padding: 15px;\n  margin-bottom: 10px;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);\n}\n\n.insight-icon {\n  width: 36px;\n  height: 36px;\n  border-radius: 18px;\n  margin-right: 12px;\n  margin-bottom: 10px;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.insight-icon.insight {\n  background: rgba(52, 199, 89, 0.1);\n  position: relative;\n}\n\n.insight-icon.insight::before {\n  content: '';\n  width: 18px;\n  height: 18px;\n  background: #34C759;\n  border-radius: 9px;\n  position: absolute;\n}\n\n.insight-icon.insight::after {\n  content: 'i';\n  color: #fff;\n  font-weight: bold;\n  font-size: 14px;\n  position: relative;\n  z-index: 1;\n}\n\n.insight-icon.warning {\n  background: rgba(255, 149, 0, 0.1);\n  position: relative;\n}\n\n.insight-icon.warning::before {\n  content: '!';\n  color: #FF9500;\n  font-weight: bold;\n  font-size: 20px;\n}\n\n.insight-icon.opportunity {\n  background: rgba(88, 86, 214, 0.1);\n  position: relative;\n}\n\n.insight-icon.opportunity::before {\n  content: '+';\n  color: #5856D6;\n  font-weight: bold;\n  font-size: 20px;\n}\n\n.insight-content {\n  margin-bottom: 15px;\n}\n\n.insight-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 8px;\n}\n\n.insight-desc {\n  font-size: 14px;\n  color: #666;\n  line-height: 1.5;\n}\n\n.insight-actions {\n  display: flex;\n  justify-content: flex-end;\n}\n\n.action-btn {\n  padding: 8px 15px;\n  border-radius: 20px;\n  font-size: 14px;\n  margin-left: 10px;\n}\n\n.action-btn.primary {\n  background: #FF7600;\n  color: #fff;\n}\n\n.action-btn.secondary {\n  background: #F0F0F0;\n  color: #666;\n}\n\n/* 浮动操作按钮 */\n.floating-action-button {\n  position: fixed;\n  bottom: 30px;\n  right: 30px;\n  width: 56px;\n  height: 56px;\n  border-radius: 28px;\n  background: #FF7600;\n  box-shadow: 0 4px 15px rgba(255, 118, 0, 0.3);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 100;\n}\n\n.fab-icon {\n  font-size: 28px;\n  color: #fff;\n  font-weight: 300;\n  line-height: 1;\n  margin-top: -2px;\n}\n\n/* 响应式调整 */\n@media screen and (max-width: 350px) {\n  .tool-card {\n    width: 33.33%;\n  }\n}\n\n/* 智能营销助手样式 */\n.ai-marketing-section {\n  margin: 15px;\n  padding: 15px;\n  background: #FFFFFF;\n  border-radius: 12px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n}\n\n.ai-tools-grid {\n  margin-top: 10px;\n}\n\n.ai-tool-card {\n  display: flex;\n  align-items: center;\n  padding: 15px;\n  margin-bottom: 10px;\n  background: #F8FAFC;\n  border-radius: 10px;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);\n}\n\n.ai-tool-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 12px;\n  flex-shrink: 0;\n}\n\n.ai-tool-icon.blue {\n  background-color: rgba(25, 137, 250, 0.1);\n}\n\n.ai-tool-icon.yellow {\n  background-color: rgba(255, 149, 0, 0.1);\n}\n\n.ai-tool-icon.green {\n  background-color: rgba(52, 199, 89, 0.1);\n}\n\n.ai-tool-content {\n  flex: 1;\n}\n\n.ai-tool-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 4px;\n}\n\n.ai-tool-desc {\n  font-size: 12px;\n  color: #999;\n  line-height: 1.5;\n}\n\n/* 底部导航栏样式调整，确保图标正确显示 */\n::v-deep .tab-icon {\n  display: block !important;\n}\n\n/* 确保底部导航栏的SVG图标正确显示 */\n::v-deep .tab-icon {\n  svg {\n    width: 22px;\n    height: 22px;\n    fill: currentColor;\n  }\n}\n\n/* 营销中心特定的样式调整 */\n::v-deep .tab-item.active[data-tab=\"marketing\"] .tab-icon {\n  color: #FF7600;\n}\n\n::v-deep .tab-item.active[data-tab=\"marketing\"] .tab-text {\n  color: #FF7600;\n}\n\n/* 底部导航栏 */\n.tab-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 60px;\n  background-color: var(--bg-primary, #FFFFFF);\n  border-top: 1px solid var(--border-light, #F0F0F0);\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03);\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  padding: 0 10px;\n  padding-bottom: env(safe-area-inset-bottom);\n  background-color: #FFFFFF;\n  flex-shrink: 0;\n}\n\n.tab-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  position: relative;\n  transition: all 0.2s ease;\n  padding: 8px 0;\n  margin: 0 8px;\n}\n\n.tab-icon {\n  width: 24px;\n  height: 24px;\n  margin-bottom: 3px;\n  transition: all 0.25s cubic-bezier(0.3, 0.7, 0.4, 1.5);\n  background-position: center;\n  background-repeat: no-repeat;\n  background-size: 22px;\n  opacity: 0.7;\n}\n\n.tab-text {\n  font-size: 10px;\n  color: var(--text-tertiary, #999999);\n  transition: color 0.2s ease, transform 0.25s ease;\n  transform: scale(0.9);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: 100%;\n  padding: 0 2px;\n}\n\n.active-indicator {\n  position: absolute;\n  top: 0;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 16px;\n  height: 3px;\n  border-radius: 0 0 4px 4px;\n  background: linear-gradient(90deg, var(--brand-primary, #1677FF), var(--accent-purple, #7265E6));\n}\n\n.tab-item.active .tab-icon {\n  transform: translateY(-2px);\n  opacity: 1;\n}\n\n.tab-item.active .tab-text {\n  color: var(--brand-primary, #1677FF);\n  font-weight: 500;\n  transform: scale(1);\n}\n\n/* 导航图标样式 */\n.tab-icon.dashboard {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z'/%3E%3C/svg%3E\");\n}\n\n.tab-icon.store {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M20 4H4v2h16V4zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6h1zm-9 4H6v-4h6v4z'/%3E%3C/svg%3E\");\n}\n\n.tab-icon.marketing {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z'/%3E%3C/svg%3E\");\n}\n\n.tab-icon.orders {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E\");\n}\n\n.tab-icon.more {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z'/%3E%3C/svg%3E\");\n}\n\n@media screen and (max-width: 375px) {\n  /* 在较小屏幕上优化导航栏 */\n  .tab-text {\n    font-size: 9px;\n  }\n  \n  .tab-icon {\n    margin-bottom: 2px;\n    background-size: 20px;\n  }\n}\n\n/* 导航图标样式 */\n.tab-icon.dashboard {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z'/%3E%3C/svg%3E\");\n}\n\n.tab-icon.store {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M20 4H4v2h16V4zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6h1zm-9 4H6v-4h6v4z'/%3E%3C/svg%3E\");\n}\n\n.tab-icon.marketing {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF7600'%3E%3Cpath d='M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z'/%3E%3C/svg%3E\");\n}\n\n.tab-icon.orders {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E\");\n}\n\n.tab-icon.more {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z'/%3E%3C/svg%3E\");\n}\n\n/* 浮动操作按钮 */\n.floating-action-button {\n  position: fixed;\n  bottom: 80px;\n  right: 20px;\n  width: 56px;\n  height: 56px;\n  border-radius: 16px;\n  background: linear-gradient(135deg, #FF7600, #FF4500);\n  box-shadow: 0 4px 16px rgba(255, 118, 0, 0.3);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 100;\n  transition: all 0.3s ease;\n  backdrop-filter: blur(10px);\n  -webkit-backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.floating-action-button:active {\n  transform: scale(0.95) translateY(2px);\n  box-shadow: 0 2px 8px rgba(255, 118, 0, 0.2);\n}\n\n.fab-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.fab-icon-plus {\n  width: 20px;\n  height: 20px;\n  position: relative;\n  margin-bottom: 2px;\n}\n\n.fab-icon-plus::before,\n.fab-icon-plus::after {\n  content: \"\";\n  position: absolute;\n  background-color: white;\n}\n\n.fab-icon-plus::before {\n  width: 20px;\n  height: 2px;\n  top: 9px;\n  left: 0;\n}\n\n.fab-icon-plus::after {\n  width: 2px;\n  height: 20px;\n  top: 0;\n  left: 9px;\n}\n\n.fab-text {\n  font-size: 12px;\n  color: white;\n  font-weight: 500;\n  margin-top: 2px;\n}\n\n.safe-area-bottom {\n  height: calc(60px + env(safe-area-inset-bottom));\n}\n\n@media screen and (max-width: 375px) {\n  /* 在较小屏幕上优化导航栏 */\n  .tab-text {\n    font-size: 9px;\n  }\n  \n  .tab-icon {\n    margin-bottom: 2px;\n    background-size: 20px;\n  }\n}\n\n/* 在较宽屏幕上优化导航栏，确保元素不会挤压 */\n@media screen and (min-width: 400px) {\n  .tab-bar {\n    padding: 0 10px;\n  }\n  \n  .tab-item {\n    margin: 0 5px;\n  }\n}\n\n/* 导航图标样式 */\n.tab-bar .tab-icon.dashboard {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z'/%3E%3C/svg%3E\");\n}\n\n.tab-bar .tab-icon.store {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M20 4H4v2h16V4zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6h1zm-9 4H6v-4h6v4z'/%3E%3C/svg%3E\");\n}\n\n.tab-bar .tab-icon.marketing {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF7600'%3E%3Cpath d='M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z'/%3E%3C/svg%3E\");\n}\n\n.tab-bar .tab-icon.orders {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E\");\n}\n\n.tab-bar .tab-icon.more {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z'/%3E%3C/svg%3E\");\n}\n\n/* 底部导航栏 */\n.bottom-nav {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 56px;\n  background-color: #FFFFFF;\n  display: flex;\n  justify-content: space-around;\n  align-items: center;\n  border-top: 1px solid #F0F0F0;\n  padding-bottom: env(safe-area-inset-bottom);\n  z-index: 100;\n}\n\n.nav-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  padding: 6px 0;\n  box-sizing: border-box;\n  position: relative;\n}\n\n.nav-icon {\n  width: 24px;\n  height: 24px;\n  margin-bottom: 4px;\n  color: #999999;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  overflow: visible;\n}\n\n.nav-text {\n  font-size: 10px;\n  color: #999999;\n}\n\n.nav-item.active .nav-icon {\n  color: #1989FA;\n}\n\n.nav-item.active .nav-text {\n  color: #1989FA;\n}\n\n/* 营销中心用橙色 */\n.nav-item.active:nth-child(3) .nav-icon {\n  color: #FF7600;\n}\n\n.nav-item.active:nth-child(3) .nav-text {\n  color: #FF7600;\n}\n\n/* 数据概览用蓝色 */\n.nav-item.active:nth-child(1) .nav-icon {\n  color: #1989FA;\n}\n\n.nav-item.active:nth-child(1) .nav-text {\n  color: #1989FA;\n}\n\n.safe-area-bottom {\n  height: calc(56px + env(safe-area-inset-bottom));\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA6gBA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA;AAAA,EAEN,YAAY,CACX;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,YAAY;AAAA;AAAA,MACZ,aAAa;AAAA,MACb,WAAW;AAAA,MACX,YAAY;AAAA;AAAA,MACZ,QAAQ;AAAA;AAAA,MACR,SAAS,CAAC,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,IAAI;AAAA;AAAA,MAGnD,WAAW;AAAA,QACT,EAAE,MAAM,QAAQ,SAAS,MAAM,YAAY,KAAK,eAAe,IAAI,kBAAkB,GAAI;AAAA,QACzF,EAAE,MAAM,QAAQ,SAAS,OAAO,YAAY,KAAK,eAAe,IAAI,kBAAkB,GAAI;AAAA,QAC1F,EAAE,MAAM,QAAQ,SAAS,MAAM,YAAY,KAAK,eAAe,IAAI,kBAAkB,GAAI;AAAA,QACzF,EAAE,MAAM,QAAQ,SAAS,OAAO,YAAY,KAAK,eAAe,IAAI,kBAAkB,GAAI;AAAA,QAC1F,EAAE,MAAM,QAAQ,SAAS,MAAO,YAAY,KAAK,eAAe,IAAI,kBAAkB,GAAI;AAAA,QAC1F,EAAE,MAAM,QAAQ,SAAS,OAAO,YAAY,KAAK,eAAe,IAAI,kBAAkB,GAAI;AAAA,QAC1F,EAAE,MAAM,QAAQ,SAAS,OAAO,YAAY,KAAK,eAAe,IAAI,kBAAkB,GAAG;AAAA,MAC1F;AAAA;AAAA,MAGD,aAAa;AAAA,QACX;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,gBAAgB;AAAA,QACjB;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,gBAAgB;AAAA,QACjB;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,gBAAgB;AAAA,QAClB;AAAA,MACD;AAAA;AAAA,MAGD,cAAc;AAAA,QACZ;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,OAAO;AAAA,YACL,EAAE,WAAW,KAAK,gBAAgB,GAAI;AAAA,YACtC,EAAE,WAAW,KAAK,gBAAgB,GAAI;AAAA,YACtC,EAAE,WAAW,KAAK,gBAAgB,GAAG;AAAA,UACtC;AAAA,UACD,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,eAAe;AAAA,QAChB;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,OAAO;AAAA,YACL,EAAE,WAAW,KAAK,gBAAgB,GAAI;AAAA,YACtC,EAAE,WAAW,KAAK,gBAAgB,GAAG;AAAA,UACtC;AAAA,UACD,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,eAAe;AAAA,QACjB;AAAA,MACD;AAAA;AAAA,MAGD,mBAAmB;AAAA,QACjB,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,QAAQ;AAAA,MACT;AAAA,MAED,eAAe;AAAA,QACb,SAAS;AAAA,QACT,cAAc;AAAA,QACd,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,QAClB,KAAK;AAAA,QACL,UAAU;AAAA,QACV,WAAW;AAAA,QACX,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,MAClB;AAAA,MAED,iBAAiB;AAAA,QACf;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,aAAa;AAAA,UACb,WAAW;AAAA,UACX,OAAO;AAAA,UACP,aAAa;AAAA,UACb,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,aAAa;AAAA,UACb,WAAW;AAAA,UACX,OAAO;AAAA,UACP,aAAa;AAAA,UACb,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,aAAa;AAAA,UACb,WAAW;AAAA,UACX,OAAO;AAAA,UACP,aAAa;AAAA,UACb,SAAS;AAAA,QACX;AAAA,MACD;AAAA,MAED,gBAAgB;AAAA,QACd;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,KAAK;AAAA,QACP;AAAA,MACD;AAAA,MAED,eAAe;AAAA,QACb;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,UAAU;AAAA,UACV,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,UAC7B,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,UAAU;AAAA,UACV,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,UAC7B,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,UAAU;AAAA,UACV,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM,CAAC,QAAQ,OAAO,MAAM;AAAA,UAC5B,MAAM;AAAA,QACR;AAAA,MACD;AAAA,MAED,gBAAgB;AAAA,QACd;AAAA,UACE,IAAI;AAAA,UACJ,KAAK;AAAA,UACL,OAAO;AAAA,UACP,OAAO;AAAA,UACP,MAAM;AAAA,UACN,WAAW;AAAA,UACX,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,KAAK;AAAA,UACL,OAAO;AAAA,UACP,OAAO;AAAA,UACP,MAAM;AAAA,UACN,WAAW;AAAA,UACX,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,KAAK;AAAA,UACL,OAAO;AAAA,UACP,OAAO;AAAA,UACP,MAAM;AAAA,UACN,WAAW;AAAA,UACX,MAAM;AAAA,QACR;AAAA,MACD;AAAA,MAED,mBAAmB;AAAA,QACjB;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,OAAO;AAAA,UACP,aAAa;AAAA,QACf;AAAA,MACD;AAAA;AAAA,MAGD,aAAa;AAAA,QACX;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,UAAU;AAAA;AAAA,IAER,kBAAkB;AAChB,YAAM,SAAS,KAAK,UAAU,IAAI,CAAC,MAAM,UAAU;AACjD,cAAM,IAAI,SAAS,OAAK,KAAK,UAAU,SAAO;AAC9C,cAAM,IAAI,MAAM,KAAK;AACrB,eAAO,GAAG,CAAC,KAAK,CAAC;AAAA,MACnB,CAAC;AAGD,YAAM,QAAQ;AACd,YAAM,QAAQ;AACd,YAAM,SAAS;AACf,YAAM,SAAS;AAEf,aAAO,WAAW,OAAO,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,MAAM;AAAA,IAC/E;AAAA;AAAA,IAGD,qBAAqB;AACnB,YAAM,SAAS,KAAK,UAAU,IAAI,CAAC,MAAM,UAAU;AACjD,cAAM,IAAI,SAAS,OAAK,KAAK,UAAU,SAAO;AAC9C,cAAM,IAAI,MAAM,KAAK;AACrB,eAAO,GAAG,CAAC,KAAK,CAAC;AAAA,MACnB,CAAC;AAGD,YAAM,QAAQ;AACd,YAAM,QAAQ;AACd,YAAM,SAAS;AACf,YAAM,SAAS;AAEf,aAAO,WAAW,OAAO,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,MAAM;AAAA,IAChF;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,aAAa,QAAQ;AACnB,aAAO,OAAO,QAAQ,CAAC,EAAE,QAAQ,yBAAyB,GAAG;AAAA,IAC9D;AAAA,IACD,UAAU,OAAO;AACf,WAAK,aAAa;AAAA,IACnB;AAAA,IACD,aAAa,OAAO;AAElB,UAAI,UAAU,QAAQ;AACpB,aAAK,gBAAe;AACpB;AAAA,MACF;AAGA,UAAI,UAAU,KAAK;AAAQ;AAE3B,WAAK,SAAS;AAGd,UAAI,UAAU,GAAG;AAEf;AAAA,MACF;AAGAA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,KAAK,YAAY,KAAK,EAAE;AAAA,QAC7B,MAAM,CAAC,QAAQ;AACbA,wBAAA,MAAA,MAAA,SAAA,yEAAc,iBAAiB,GAAG;AAElCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,KAAK,KAAK,YAAY,KAAK,EAAE;AAAA,YAC7B,MAAM,CAAC,cAAc;AACnBA,4BAAA,MAAA,MAAA,SAAA,yEAAc,iBAAiB,SAAS;AACxCA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,mBAAmB;AACjBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IACD,mBAAmB,UAAU;AAC3BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4EAA4E,SAAS,EAAE;AAAA,MAC9F,CAAC;AAAA,IACF;AAAA,IACD,eAAe,MAAM;AAEnB,UAAI,QAAQ;AAGZ,cAAO,KAAK,IAAE;AAAA,QACZ,KAAK;AACH,kBAAQ;AACR;AAAA,QACF,KAAK;AACH,kBAAQ,wDAAwD,KAAK,IAAI;AACzE;AAAA,QACF,KAAK;AACH,kBAAQ;AACR;AAAA,QACF,KAAK;AACH,kBAAQ;AACR;AAAA,QACF,KAAK;AACH,kBAAQ;AACR;AAAA,QACF,KAAK;AACH,kBAAQ;AACR;AAAA,QACF;AAEE,kBAAQ,wDAAwD,KAAK,IAAI;AAAA,MAC7E;AAGAA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL,eAAe;AAAA,QACf,mBAAmB;AAAA,QACnB,SAAS,MAAM;AAEbA,wBAAG,MAAC,MAAM,wBAAwB;AAAA,YAChC,QAAQ,KAAK;AAAA,YACb,UAAU,KAAK;AAAA,YACf,WAAW,KAAK,IAAI;AAAA,UACtB,CAAC;AAAA,QACF;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAc,MAAA,MAAA,SAAA,0EAAA,SAAS,GAAG;AAE1BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACZ,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,uBAAuB,MAAM;AAE3B,UAAI,QAAQ,wDAAwD,KAAK,IAAI;AAG7EA,oBAAY,MAAA,MAAA,OAAA,0EAAA,SAAS,KAAK;AAE1BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL,eAAe;AAAA,QACf,mBAAmB;AAAA,QACnB,SAAS,MAAM;AAEbA,wBAAG,MAAC,MAAM,uBAAuB;AAAA,YAC/B,QAAQ,KAAK;AAAA,YACb,UAAU,KAAK;AAAA,YACf,WAAW,KAAK,IAAI;AAAA,UACtB,CAAC;AAAA,QACF;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAc,MAAA,MAAA,SAAA,0EAAA,SAAS,GAAG;AAE1BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACZ,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,YAAY,OAAO;AACjBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,yEAAyE,MAAM,EAAE;AAAA,MACxF,CAAC;AAAA,IACF;AAAA,IACD,cAAc;AACZA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IACD,oBAAoB;AAClBA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,SAAS,UAAU,UAAU,UAAU,QAAQ;AAAA,QAC1D,SAAS,CAAC,QAAQ;AAChB,gBAAM,OAAO;AAAA,YACX;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA;AAGFA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK,KAAK,IAAI,QAAQ;AAAA,UACxB,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,aAAa,SAAS;AACpBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,cAAc,SAAS;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,eAAe;AACbA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IACD,iBAAiB,QAAQ;AACvBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,0EAA0E,OAAO,EAAE;AAAA,MAC1F,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB;AACfA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IACD,mBAAmB,UAAU;AAC3BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4EAA4E,SAAS,EAAE;AAAA,MAC9F,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,cAAc;AACZA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,cAAc;AACZA,oBAAAA,MAAY,MAAA,OAAA,0EAAA,eAAe;AAC3BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL,MAAM,CAAC,QAAQ;AACbA,wBAAc,MAAA,MAAA,SAAA,0EAAA,SAAS,GAAG;AAC1BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,qBAAqB;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,eAAe,QAAQ;AACrB,WAAK,cAAc;AAAA,IACpB;AAAA,IAED,mBAAmB;AACjB,WAAK,cAAc;AAAA,IACpB;AAAA;AAAA,IAID,kBAAkB;AAEhB,YAAM,cAAc,CAAC,QAAQ,QAAQ,MAAM;AAE3CA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU;AAAA,QACV,SAAS,CAAC,QAAQ;AAEhB,gBAAM,SAAS;AAAA,YACb;AAAA,YACA;AAAA;AAEFA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK,OAAO,IAAI,QAAQ;AAAA,UAC1B,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,qBAAqB;AACnBA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,SAAS,UAAU,UAAU,QAAQ;AAAA,QAChD,SAAS,CAAC,QAAQ;AAChB,gBAAM,UAAU;AAAA,YACd,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA;AAGP,cAAI,QAAQ,IAAI,QAAQ,GAAG;AACzB,oBAAQ,IAAI,QAAQ;UACtB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB;AACfA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IAED,UAAU,UAAU;AAClB,cAAO,UAAQ;AAAA,QACb,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACP,CAAC;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACP,CAAC;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACP,CAAC;AACD;AAAA,QACF;AACEA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,MACL;AAAA,IACD;AAAA,IAED,iBAAiB;AACfA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,SAAS,UAAU,UAAU,QAAQ;AAAA,QAChD,SAAS,CAAC,QAAQ;AAChB,gBAAM,UAAU;AAAA,YACd,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA;AAGP,cAAI,QAAQ,IAAI,QAAQ,GAAG;AACzB,oBAAQ,IAAI,QAAQ;UACtB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,OAAO;AACrBA,oBAAY,MAAA,MAAA,OAAA,0EAAA,UAAU,KAAK;AAC3B,WAAK,SAAS,UAAU,cAAc,IAAI;AAAA,IAC5C;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjuCA,GAAG,WAAW,eAAe;"}