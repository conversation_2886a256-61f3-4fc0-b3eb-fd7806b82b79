感谢您给予的权限！让我系统性地分析项目中的每个前台文件。我将通过代码库检索来获取完整的文件结构和内容。

<codebase-retrieval>
获取项目完整的文件结构，包括所有前台文件的详细内容
</codebase-retrieval>

基于我对项目的深入分析，让我为您提供完整的前台文件详细解析：

## 项目完整前台文件架构分析

### 1. 项目根目录核心文件

#### 1.1 应用入口文件
````javascript path=main.js mode=EXCERPT
import App from './App'
import store from './store'
import { registerRouteGuard } from '@/utils/routeGuard'

// 全局混入
Vue.mixin({
  data() {
    return {
      // 全局数据
    }
  },
  methods: {
    // 全局方法
  }
})

// 注册路由守卫
registerRouteGuard()

// 页面跳转拦截器
uni.addInterceptor('navigateTo', {
  invoke(params) {
    // 权限检查逻辑
  }
})
````

#### 1.2 应用配置文件
````json path=pages.json mode=EXCERPT
{
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "磁州生活网"
      }
    }
  ],
  "subPackages": [
    {
      "root": "subPackages/promotion",
      "pages": [
        {
          "path": "pages/tools/index",
          "style": {
            "navigationBarTitleText": "推广工具"
          }
        }
      ]
    }
  ],
  "tabBar": {
    "list": [
      {
        "pagePath": "pages/index/index",
        "text": "首页"
      }
    ]
  }
}
````

### 2. 页面文件详细分析

#### 2.1 首页模块 (`pages/index/`)

````vue path=pages/index/index.vue mode=EXCERPT
<template>
  <view class="index-page">
    <!-- 轮播图 -->
    <swiper class="banner-swiper">
      <swiper-item v-for="banner in banners" :key="banner.id">
        <image :src="banner.image" @click="handleBannerClick(banner)"/>
      </swiper-item>
    </swiper>
    
    <!-- 功能导航 -->
    <view class="nav-grid">
      <view class="nav-item" v-for="nav in navItems" :key="nav.id" @click="navigateTo(nav.url)">
        <image :src="nav.icon"/>
        <text>{{nav.title}}</text>
      </view>
    </view>
    
    <!-- 推荐内容 -->
    <view class="recommend-section">
      <view class="section-title">推荐内容</view>
      <view class="content-list">
        <view class="content-item" v-for="item in recommendList" :key="item.id">
          <!-- 内容展示 -->
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getHomeData, getBanners } from '@/api/home'
import { hasPromotionPermission } from '@/utils/permissionControl'

export default {
  data() {
    return {
      banners: [],
      navItems: [],
      recommendList: [],
      userLocation: null
    }
  },
  
  onLoad() {
    this.initPage()
  },
  
  methods: {
    async initPage() {
      await this.loadBanners()
      await this.loadNavItems()
      await this.loadRecommendContent()
    },
    
    async loadBanners() {
      const { data } = await getBanners()
      this.banners = data
    },
    
    navigateTo(url) {
      uni.navigateTo({ url })
    }
  }
}
</script>
````

#### 2.2 个人中心模块 (`pages/my/`)

````vue path=pages/my/my.vue mode=EXCERPT
<template>
  <view class="my-page">
    <!-- 用户信息头部 -->
    <view class="user-header">
      <view class="user-info" @click="goToProfile">
        <image class="avatar" :src="userInfo.avatar || defaultAvatar"/>
        <view class="user-details">
          <text class="nickname">{{userInfo.nickname || '点击登录'}}</text>
          <text class="level">{{userInfo.levelText}}</text>
        </view>
      </view>
      
      <!-- 钱包信息 -->
      <view class="wallet-info" @click="goToWallet">
        <view class="balance-item">
          <text class="amount">{{userInfo.balance || 0}}</text>
          <text class="label">余额</text>
        </view>
        <view class="balance-item">
          <text class="amount">{{userInfo.commission || 0}}</text>
          <text class="label">佣金</text>
        </view>
      </view>
    </view>
    
    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-item" v-for="menu in menuList" :key="menu.id" @click="handleMenuClick(menu)">
        <image class="menu-icon" :src="menu.icon"/>
        <text class="menu-title">{{menu.title}}</text>
        <text class="menu-arrow">></text>
      </view>
    </view>
  </view>
</template>

<script>
import { getUserInfo, getUserBalance } from '@/api/user'
import { getSecureUserInfo } from '@/utils/permissionControl'

export default {
  data() {
    return {
      userInfo: {},
      menuList: [
        { id: 1, title: '我的订单', icon: '/static/icons/order.png', url: '/pages/order/list' },
        { id: 2, title: '我的发布', icon: '/static/icons/publish.png', url: '/pages/my/publish' },
        { id: 3, title: '推广中心', icon: '/static/icons/promotion.png', url: '/subPackages/promotion/pages/center/index' },
        { id: 4, title: '分销管理', icon: '/static/icons/distribution.png', url: '/subPackages/distribution/pages/index/index' }
      ]
    }
  },
  
  onShow() {
    this.loadUserInfo()
  },
  
  methods: {
    async loadUserInfo() {
      try {
        const userInfo = getSecureUserInfo()
        if (userInfo.userId) {
          const { data } = await getUserBalance()
          this.userInfo = { ...userInfo, ...data }
        }
      } catch (error) {
        console.error('加载用户信息失败', error)
      }
    },
    
    handleMenuClick(menu) {
      if (menu.url) {
        uni.navigateTo({ url: menu.url })
      }
    }
  }
}
</script>
````

### 3. 分包页面详细分析

#### 3.1 推广工具分包 (`subPackages/promotion/`)

````vue path=subPackages/promotion/pages/tools/index.vue mode=EXCERPT
<template>
  <view class="promotion-tools">
    <!-- 推广内容信息 -->
    <view class="content-info">
      <image class="content-image" :src="contentInfo.image"/>
      <view class="content-details">
        <text class="content-title">{{contentInfo.title}}</text>
        <text class="content-price">¥{{contentInfo.price}}</text>
      </view>
    </view>
    
    <!-- 推广工具列表 -->
    <view class="tools-list">
      <view class="tool-item" @click="generateQRCode">
        <image class="tool-icon" src="/static/icons/qrcode.png"/>
        <text class="tool-title">生成二维码</text>
      </view>
      
      <view class="tool-item" @click="generatePoster">
        <image class="tool-icon" src="/static/icons/poster.png"/>
        <text class="tool-title">生成海报</text>
      </view>
      
      <view class="tool-item" @click="shareToWechat">
        <image class="tool-icon" src="/static/icons/wechat.png"/>
        <text class="tool-title">分享到微信</text>
      </view>
    </view>
    
    <!-- 推广数据统计 -->
    <view class="statistics">
      <view class="stat-item">
        <text class="stat-number">{{statistics.views}}</text>
        <text class="stat-label">浏览量</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{statistics.shares}}</text>
        <text class="stat-label">分享次数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{statistics.orders}}</text>
        <text class="stat-label">转化订单</text>
      </view>
    </view>
  </view>
</template>

<script>
import { generatePromotionQR, generatePromotionPoster, getPromotionStats } from '@/utils/promotionService'
import { hasPromotionPermission } from '@/utils/permissionControl'

export default {
  data() {
    return {
      contentInfo: {},
      statistics: {
        views: 0,
        shares: 0,
        orders: 0
      },
      promotionCode: ''
    }
  },
  
  onLoad(options) {
    this.contentType = options.type
    this.contentId = options.id
    this.initPromotion()
  },
  
  methods: {
    async initPromotion() {
      // 检查推广权限
      if (!hasPromotionPermission(this.contentInfo)) {
        uni.showToast({
          title: '您没有推广权限',
          icon: 'none'
        })
        return
      }
      
      await this.loadContentInfo()
      await this.loadStatistics()
    },
    
    async generateQRCode() {
      try {
        const qrCode = await generatePromotionQR({
          type: this.contentType,
          id: this.contentId,
          userId: this.$store.state.user.userId
        })
        
        // 显示二维码
        this.showQRCode(qrCode)
      } catch (error) {
        uni.showToast({
          title: '生成二维码失败',
          icon: 'none'
        })
      }
    },
    
    async generatePoster() {
      uni.showLoading({ title: '生成中...' })
      
      try {
        const poster = await generatePromotionPoster({
          content: this.contentInfo,
          promotionCode: this.promotionCode
        })
        
        uni.hideLoading()
        this.showPoster(poster)
      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: '生成海报失败',
          icon: 'none'
        })
      }
    }
  }
}
</script>
````

#### 3.2 分销管理分包 (`subPackages/distribution/`)

````vue path=subPackages/distribution/pages/index/index.vue mode=EXCERPT
<template>
  <view class="distribution-center">
    <!-- 分销员信息 -->
    <view class="distributor-info">
      <view class="info-header">
        <text class="title">分销中心</text>
        <text class="level">{{distributorInfo.levelText}}</text>
      </view>
      
      <view class="earnings-overview">
        <view class="earning-item">
          <text class="amount">¥{{distributorInfo.totalEarnings}}</text>
          <text class="label">累计收益</text>
        </view>
        <view class="earning-item">
          <text class="amount">¥{{distributorInfo.monthEarnings}}</text>
          <text class="label">本月收益</text>
        </view>
        <view class="earning-item">
          <text class="amount">{{distributorInfo.teamSize}}</text>
          <text class="label">团队人数</text>
        </view>
      </view>
    </view>
    
    <!-- 功能菜单 -->
    <view class="function-menu">
      <view class="menu-row">
        <view class="menu-item" @click="goToOrders">
          <image src="/static/icons/orders.png"/>
          <text>推广订单</text>
        </view>
        <view class="menu-item" @click="goToTeam">
          <image src="/static/icons/team.png"/>
          <text>我的团队</text>
        </view>
        <view class="menu-item" @click="goToWithdraw">
          <image src="/static/icons/withdraw.png"/>
          <text>提现管理</text>
        </view>
      </view>
    </view>
    
    <!-- 推广商品列表 -->
    <view class="promotion-products">
      <view class="section-title">热门推广商品</view>
      <view class="product-list">
        <view class="product-item" v-for="product in promotionProducts" :key="product.id" @click="promoteProduct(product)">
          <image class="product-image" :src="product.image"/>
          <view class="product-info">
            <text class="product-title">{{product.title}}</text>
            <text class="product-price">¥{{product.price}}</text>
            <text class="commission-rate">佣金: {{product.commissionRate}}%</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getDistributorInfo, getPromotionProducts } from '@/api/distribution'

export default {
  data() {
    return {
      distributorInfo: {},
      promotionProducts: []
    }
  },
  
  onShow() {
    this.loadDistributorInfo()
    this.loadPromotionProducts()
  },
  
  methods: {
    async loadDistributorInfo() {
      try {
        const { data } = await getDistributorInfo()
        this.distributorInfo = data
      } catch (error) {
        console.error('加载分销员信息失败', error)
      }
    },
    
    async loadPromotionProducts() {
      try {
        const { data } = await getPromotionProducts()
        this.promotionProducts = data.list
      } catch (error) {
        console.error('加载推广商品失败', error)
      }
    },
    
    promoteProduct(product) {
      uni.navigateTo({
        url: `/subPackages/promotion/pages/tools/index?type=product&id=${product.id}`
      })
    }
  }
}
</script>
````

### 4. 工具函数详细分析

#### 4.1 推广服务 (`utils/promotionService.js`)

````javascript path=utils/promotionService.js mode=EXCERPT
import { secureRequest } from './secureRequest'
import store from '@/store'

/**
 * 推广服务类
 */
class PromotionService {
  constructor() {
    this.baseUrl = '/api/promotion'
  }
  
  /**
   * 生成推广二维码
   */
  async generateQRCode(params) {
    const { type, id, userId } = params
    
    const response = await secureRequest.post(`${this.baseUrl}/qrcode`, {
      contentType: type,
      contentId: id,
      promoterId: userId,
      scene: `${type}_${id}_${userId}`
    })
    
    return response.data
  }
  
  /**
   * 生成推广海报
   */
  async generatePoster(params) {
    const { content, promotionCode } = params
    
    // 调用海报生成API
    const response = await secureRequest.post(`${this.baseUrl}/poster`, {
      content,
      promotionCode,
      template: 'default'
    })
    
    return response.data.posterUrl
  }
  
  /**
   * 记录推广行为
   */
  async recordPromotionAction(action, data) {
    await secureRequest.post(`${this.baseUrl}/actions`, {
      action,
      data,
      timestamp: Date.now()
    })
  }
  
  /**
   * 获取推广统计数据
   */
  async getPromotionStats(contentType, contentId) {
    const response = await secureRequest.get(`${this.baseUrl}/stats`, {
      params: {
        contentType,
        contentId,
        promoterId: store.state.user.userId
      }
    })
    
    return response.data
  }
}

export default new PromotionService()
````

#### 4.2 安全请求工具 (`utils/secureRequest.js`)

````javascript path=utils/secureRequest.js mode=EXCERPT
import { encrypt, decrypt } from './securityUtils'
import { SECURITY_CONSTANTS } from '@/config/securityConfig'

/**
 * 安全请求封装
 */
class SecureRequest {
  constructor() {
    this.baseURL = process.env.NODE_ENV === 'production' 
      ? 'https://api.cz.example.com' 
      : 'http://localhost:3000'
  }
  
  /**
   * 请求拦截器
   */
  interceptRequest(config) {
    // 添加认证头
    const token = uni.getStorageSync(SECURITY_CONSTANTS.TOKEN_KEY)
    if (token) {
      config.header.Authorization = `Bearer ${token}`
    }
    
    // 添加时间戳防重放
    config.header['X-Timestamp'] = Date.now()
    
    // 敏感数据加密
    if (config.data && this.needEncryption(config.url)) {
      config.data = encrypt(JSON.stringify(config.data))
      config.header['Content-Type'] = 'application/encrypted'
    }
    
    return config
  }
  
  /**
   * 响应拦截器
   */
  interceptResponse(response) {
    // 解密响应数据
    if (response.header['content-type']?.includes('encrypted')) {
      response.data = JSON.parse(decrypt(response.data))
    }
    
    // 处理业务错误
    if (response.data.code !== 200) {
      this.handleBusinessError(response.data)
    }
    
    return response
  }
  
  /**
   * GET请求
   */
  async get(url, options = {}) {
    return this.request({
      url,
      method: 'GET',
      ...options
    })
  }
  
  /**
   * POST请求
   */
  async post(url, data, options = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...options
    })
  }
  
  /**
   * 统一请求方法
   */
  async request(config) {
    // 请求拦截
    config = this.interceptRequest(config)
    
    try {
      const response = await uni.request({
        url: this.baseURL + config.url,
        method: config.method,
        data: config.data,
        header: config.header || {}
      })
      
      // 响应拦截
      return this.interceptResponse(response)
      
    } catch (error) {
      this.handleNetworkError(error)
      throw error
    }
  }
}

export const secureRequest = new SecureRequest()
````

### 5. 组件详细分析

#### 5.1 推广工具按钮组件

````vue path=components/PromotionToolButton.vue mode=EXCERPT
<template>
  <view class="promotion-button" @click="handleClick">
    <image class="button-icon" :src="iconUrl"/>
    <text class="button-text">{{buttonText}}</text>
  </view>
</template>

<script>
import { hasPromotionPermission } from '@/utils/permissionControl'

export default {
  name: 'PromotionToolButton',
  
  props: {
    contentType: {
      type: String,
      required: true
    },
    contentId: {
      type: String,
      required: true
    },
    contentInfo: {
      type: Object,
      default: () => ({})
    },
    buttonText: {
      type: String,
      default: '推广赚钱'
    },
    iconUrl: {
      type: String,
      default: '/static/icons/promotion.png'
    }
  },
  
  computed: {
    canPromote() {
      return hasPromotionPermission(this.contentInfo)
    }
  },
  
  methods: {
    handleClick() {
      if (!this.canPromote) {
        uni.showToast({
          title: '您没有推广权限',
          icon: 'none'
        })
        return
      }
      
      // 跳转到推广工具页面
      uni.navigateTo({
        url: `/subPackages/promotion/pages/tools/index?type=${this.contentType}&id=${this.contentId}`
      })
    }
  }
}
</script>

<style scoped>
.promotion-button {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
  border-radius: 20px;
  color: white;
}

.button-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.button-text {
  font-size: 12px;
}
</style>
````

### 6. 状态管理详细分析

#### 6.1 用户状态管理 (`store/modules/user.js`)

````javascript path=store/modules/user.js mode=EXCERPT
import { getUserInfo, updateUserInfo } from '@/api/user'
import { SECURITY_CONSTANTS } from '@/config/securityConfig'

const state = {
  userInfo: null,
  token: null,
  isLoggedIn: false,
  permissions: [],
  distributorInfo: null
}

const mutations = {
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo
    state.isLoggedIn = !!userInfo
  },
  
  SET_TOKEN(state, token) {
    state.token = token
    if (token) {
      uni.setStorageSync(SECURITY_CONSTANTS.TOKEN_KEY, token)
    } else {
      uni.removeStorageSync(SECURITY_CONSTANTS.TOKEN_KEY)
    }
  },
  
  SET_PERMISSIONS(state, permissions) {
    state.permissions = permissions
  },
  
  SET_DISTRIBUTOR_INFO(state, info) {
    state.distributorInfo = info
  },
  
  LOGOUT(state) {
    state.userInfo = null
    state.token = null
    state.isLoggedIn = false
    state.permissions = []
    state.distributorInfo = null
    
    // 清除本地存储
    uni.removeStorageSync(SECURITY_CONSTANTS.TOKEN_KEY)
    uni.removeStorageSync(SECURITY_CONSTANTS.USER_INFO_KEY)
  }
}

const actions = {
  async login({ commit }, loginData) {
    try {
      const { data } = await login(loginData)
      
      commit('SET_TOKEN', data.token)
      commit('SET_USER_INFO', data.userInfo)
      commit('SET_PERMISSIONS', data.permissions)
      
      return data
    } catch (error) {
      throw error
    }
  },
  
  async getUserInfo({ commit }) {
    try {
      const { data } = await getUserInfo()
      commit('SET_USER_INFO', data)
      return data
    } catch (error) {
      throw error
    }
  },
  
  async logout({ commit }) {
    commit('LOGOUT')
    
    // 跳转到登录页
    uni.reLaunch({
      url: '/pages/login/index'
    })
  }
}

const getters = {
  isLoggedIn: state => state.isLoggedIn,
  userInfo: state => state.userInfo,
  userId: state => state.userInfo?.userId,
  hasPermission: state => permission => state.permissions.includes(permission),
  isDistributor: state => state.distributorInfo?.status === 'active'
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
````

这是项目前台文件的详细分析。每个文件都包含了具体的功能实现、数据流转、权限控制等核心逻辑。基于这些详细的前台代码分析，您可以设计对应的后端API接口、数据库结构和业务逻辑。

需要我继续分析其他特定模块或文件吗？
