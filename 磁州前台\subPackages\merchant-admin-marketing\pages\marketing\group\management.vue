<template>
  <view class="group-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">拼团活动</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 顶部操作区 -->
    <view class="top-actions">
      <CreateButton text="创建拼团" theme="group" @click="createNewGroup" />
    </view>
    
    <!-- 拼团系统开关 -->
    <view class="system-switch">
      <view class="switch-content">
        <text class="switch-title">拼团系统</text>
        <text class="switch-desc">开启后，用户可以参与商品拼团活动</text>
      </view>
      <switch :checked="groupSystemEnabled" @change="toggleGroupSystem" color="#7E30E1" />
    </view>
    
    <!-- 拼团数据概览 -->
    <view class="overview-section">
      <view class="overview-header">
        <text class="section-title">拼团数据概览</text>
        <view class="date-picker" @click="showDatePicker">
          <text class="date-text">{{dateRange}}</text>
          <view class="date-icon"></view>
        </view>
      </view>
      
      <view class="stats-cards">
        <view class="stats-card">
          <view class="card-icon groups">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="#9040FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="9" cy="7" r="4" stroke="#9040FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87" stroke="#9040FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M16 3.13a4 4 0 0 1 0 7.75" stroke="#9040FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
          <view class="card-value">{{groupData.totalGroups}}</view>
          <view class="card-label">拼团总数</view>
          <view class="card-trend" :class="groupData.groupsTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{groupData.groupsGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-icon rate">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" stroke="#34C759" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M22 4 12 14.01l-3-3" stroke="#34C759" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
          <view class="card-value">{{groupData.successRate}}%</view>
          <view class="card-label">成团率</view>
          <view class="card-trend" :class="groupData.successRateTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{groupData.successRateGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-icon revenue">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 1v22M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" stroke="#FF9500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
          <view class="card-value">¥{{formatNumber(groupData.totalRevenue)}}</view>
          <view class="card-label">拼团收入</view>
          <view class="card-trend" :class="groupData.revenueTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{groupData.revenueGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-icon participants">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="#007AFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="9" cy="7" r="4" stroke="#007AFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
          <view class="card-value">{{groupData.participantsCount}}</view>
          <view class="card-label">参与人数</view>
          <view class="card-trend" :class="groupData.participantsTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{groupData.participantsGrowth}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 进行中的拼团活动 -->
    <view class="active-groups-section">
      <view class="section-header">
        <text class="section-title">进行中的拼团</text>
        <text class="view-all" @click="viewAllGroups">查看全部</text>
      </view>
      
      <view class="group-list">
        <view class="group-item" v-for="(group, index) in activeGroups" :key="index" @click="viewGroupDetail(group)">
          <image class="group-image" :src="group.image" mode="aspectFill"></image>
          <view class="group-content">
            <view class="group-title-row">
              <text class="group-name">{{group.name}}</text>
              <view class="group-status" :class="group.statusClass">{{group.statusText}}</view>
            </view>
            <view class="group-info">
              <view class="info-item">
                <text class="info-label">原价：</text>
                <text class="info-value original-price">¥{{group.originalPrice}}</text>
              </view>
              <view class="info-item">
                <text class="info-label">拼团价：</text>
                <text class="info-value group-price">¥{{group.groupPrice}}</text>
              </view>
              <view class="info-item">
                <text class="info-label">成团人数：</text>
                <text class="info-value">{{group.requiredMembers}}人</text>
              </view>
              <view class="info-item">
                <text class="info-label">剩余时间：</text>
                <text class="info-value time-left">{{group.timeLeft}}</text>
              </view>
            </view>
            <view class="group-progress">
              <view class="progress-text">
                <text>已参与：{{group.currentMembers}}/{{group.requiredMembers}}人</text>
                <text>{{group.progressPercent}}%</text>
              </view>
              <view class="progress-bar">
                <view class="progress-fill" :style="{ width: group.progressPercent + '%' }"></view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 拼团设置 -->
    <view class="settings-section">
      <view class="section-header">
        <text class="section-title">拼团设置</text>
      </view>
      
      <view class="settings-list">
        <view class="settings-item" @click="navigateToSetting('rules')">
          <view class="item-left">
            <view class="item-icon rules"></view>
            <text class="item-title">拼团规则</text>
          </view>
          <view class="item-right">
            <text class="item-value">{{groupSettings.rulesCount}}项规则</text>
            <view class="item-arrow"></view>
          </view>
        </view>
        
        <view class="settings-item" @click="navigateToSetting('time')">
          <view class="item-left">
            <view class="item-icon time"></view>
            <text class="item-title">拼团时间</text>
          </view>
          <view class="item-right">
            <text class="item-value">{{groupSettings.timeLimit}}小时</text>
            <view class="item-arrow"></view>
          </view>
        </view>
        
        <view class="settings-item" @click="navigateToSetting('discount')">
          <view class="item-left">
            <view class="item-icon discount"></view>
            <text class="item-title">拼团折扣</text>
          </view>
          <view class="item-right">
            <text class="item-value">{{groupSettings.discountRange}}</text>
            <view class="item-arrow"></view>
          </view>
        </view>
        
        <view class="settings-item" @click="navigateToSetting('notification')">
          <view class="item-left">
            <view class="item-icon notification"></view>
            <text class="item-title">通知设置</text>
          </view>
          <view class="item-right">
            <text class="item-value">{{groupSettings.notificationEnabled ? '已开启' : '未开启'}}</text>
            <view class="item-arrow"></view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 拼团工具 -->
    <view class="tools-section">
      <view class="section-header">
        <text class="section-title">拼团工具</text>
      </view>
      
      <view class="tools-grid">
        <view class="tool-card" v-for="(tool, index) in groupTools" :key="index" @click="useTool(tool)">
          <view class="tool-icon" :style="{ background: tool.color }">
            <view class="tool-icon-svg" v-html="tool.svg"></view>
          </view>
          <text class="tool-name">{{tool.name}}</text>
          <text class="tool-desc">{{tool.description}}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import CreateButton from '/subPackages/merchant-admin-marketing/components/CreateButton.vue';

export default {
  components: {
    CreateButton
  },
  data() {
    return {
      groupSystemEnabled: true,
      dateRange: '2023-04-01 ~ 2023-04-30',
      
      // 拼团数据
      groupData: {
        totalGroups: 24,
        groupsTrend: 'up',
        groupsGrowth: '12%',
        
        successRate: 85,
        successRateTrend: 'up',
        successRateGrowth: '5%',
        
        totalRevenue: 12580.50,
        revenueTrend: 'up',
        revenueGrowth: '18%',
        
        participantsCount: 156,
        participantsTrend: 'up',
        participantsGrowth: '15%'
      },
      
      // 进行中的拼团
      activeGroups: [
        {
          id: 1,
          name: 'iPhone 14 Pro Max',
          image: '/static/images/products/iphone.jpg',
          originalPrice: '9999.00',
          groupPrice: '8799.00',
          requiredMembers: 3,
          currentMembers: 2,
          progressPercent: 66,
          timeLeft: '12小时23分',
          statusText: '进行中',
          statusClass: 'active'
        },
        {
          id: 2,
          name: 'MacBook Air M2',
          image: '/static/images/products/macbook.jpg',
          originalPrice: '7999.00',
          groupPrice: '7299.00',
          requiredMembers: 5,
          currentMembers: 3,
          progressPercent: 60,
          timeLeft: '5小时47分',
          statusText: '即将结束',
          statusClass: 'ending'
        },
        {
          id: 3,
          name: '四菜一汤家庭套餐',
          image: '/static/images/products/food-package.jpg',
          originalPrice: '168.00',
          groupPrice: '99.00',
          requiredMembers: 2,
          currentMembers: 1,
          progressPercent: 50,
          timeLeft: '23小时59分',
          statusText: '套餐拼团',
          statusClass: 'package'
        }
      ],
      
      // 拼团设置
      groupSettings: {
        rulesCount: 5,
        timeLimit: 24,
        discountRange: '7折-9折',
        notificationEnabled: true
      },
      
      // 拼团工具
      groupTools: [
        {
          name: '创建拼团',
          description: '创建新的拼团活动',
          color: '#34C759',
          svg: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-4.41 0-8 3.59-8 8s3.59 8 8 8 8-3.59 8-8-3.59-8-8-8zm4 10c0 2.21-1.79 4-4 4s-4-1.79-4-4 1.79-4 4-4 4 1.79 4 4zm0-6c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4z"/></svg>'
        },
        {
          name: '拼团管理',
          description: '管理拼团活动',
          color: '#FF3B30',
          svg: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-4.41 0-8 3.59-8 8s3.59 8 8 8 8-3.59 8-8-3.59-8-8-8zm4 10c0 2.21-1.79 4-4 4s-4-1.79-4-4 1.79-4 4-4 4 1.79 4 4zm0-6c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4z"/></svg>'
        },
        {
          name: '数据统计',
          description: '查看拼团相关数据',
          color: '#007AFF',
          svg: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/></svg>'
        },
        {
          name: '团购套餐',
          description: '管理团购套餐',
          color: '#FF9500',
          svg: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3"></path><rect x="9" y="9" width="6" height="6"></rect></svg>'
        }
      ]
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showHelp() {
      uni.showToast({
        title: '拼团活动帮助',
        icon: 'none'
      });
    },
    toggleGroupSystem(e) {
      this.groupSystemEnabled = e.detail.value;
      uni.showToast({
        title: this.groupSystemEnabled ? '已开启拼团系统' : '已关闭拼团系统',
        icon: 'none'
      });
    },
    showDatePicker() {
      // 显示日期选择器
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    formatNumber(num) {
      return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    },
    viewAllGroups() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/group/list'
      });
    },
    viewGroupDetail(group) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/group/detail?id=${group.id}`
      });
    },
    navigateToSetting(setting) {
      // 实现导航到设置页面的逻辑
      uni.showToast({
        title: `导航到${setting}设置页面`,
        icon: 'none'
      });
    },
    useTool(tool) {
      // 实现使用工具的逻辑
      if (tool.name === '创建拼团') {
        uni.navigateTo({
          url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create'
        });
      } else if (tool.name === '拼团管理') {
        uni.navigateTo({
          url: '/subPackages/merchant-admin-marketing/pages/marketing/group/list'
        });
      } else if (tool.name === '数据统计') {
        uni.navigateTo({
          url: '/subPackages/merchant-admin-marketing/pages/marketing/group/statistics'
        });
      } else if (tool.name === '团购套餐') {
        uni.navigateTo({
          url: '/subPackages/merchant-admin-marketing/pages/marketing/group/package-management'
        });
      } else {
        uni.showToast({
          title: `使用${tool.name}工具`,
          icon: 'none'
        });
      }
    },
    createNewGroup() {
      // 跳转到创建拼团活动页面
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create'
      });
    }
  }
}
</script>

<style lang="scss">
.group-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30px;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 顶部操作区样式 */
.top-actions {
  position: sticky;
  top: 100px; /* 确保在导航栏下方 */
  left: 0;
  right: 0;
  background: #FFFFFF;
  padding: 15px 16px;
  display: flex;
  justify-content: flex-end;
  z-index: 90;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* 系统开关样式 */
.system-switch {
  margin: 15px;
  padding: 15px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.switch-content {
  flex: 1;
}

.switch-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.switch-desc {
  font-size: 12px;
  color: #999;
}

/* 概览部分样式 */
.overview-section {
  margin: 15px;
  padding: 15px;
  background: #FFFFFF;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 8px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 16px;
  background: linear-gradient(to bottom, #9040FF, #5E35B1);
  border-radius: 3px;
}

.date-picker {
  display: flex;
  align-items: center;
  background: rgba(144, 64, 255, 0.08);
  border-radius: 20px;
  padding: 6px 12px;
  transition: all 0.3s ease;
}

.date-picker:hover {
  background: rgba(144, 64, 255, 0.12);
}

.date-text {
  font-size: 12px;
  color: #9040FF;
  margin-right: 6px;
  font-weight: 500;
}

.date-icon {
  width: 6px;
  height: 6px;
  border-top: 1.5px solid #9040FF;
  border-right: 1.5px solid #9040FF;
  transform: rotate(135deg);
}

/* 数据卡片样式 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  margin-top: 10px;
}

.stats-card {
  background: #F8FAFC;
  border-radius: 12px;
  padding: 12px;
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.03);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.card-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.card-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.card-trend {
  display: flex;
  align-items: center;
  font-size: 11px;
  margin-top: auto;
}

.card-trend.up {
  color: #34C759;
}

.card-trend.down {
  color: #FF3B30;
}

.trend-arrow {
  width: 0;
  height: 0;
  margin-right: 4px;
}

.up .trend-arrow {
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  border-bottom: 5px solid #34C759;
}

.down .trend-arrow {
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  border-top: 5px solid #FF3B30;
}

.card-icon {
  width: 28px;
  height: 28px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.card-icon.groups {
  background-color: rgba(144, 64, 255, 0.1);
}

.card-icon.rate {
  background-color: rgba(52, 199, 89, 0.1);
}

.card-icon.revenue {
  background-color: rgba(255, 149, 0, 0.1);
}

.card-icon.participants {
  background-color: rgba(0, 122, 255, 0.1);
}

/* 进行中的拼团活动样式 */
.active-groups-section {
  margin: 15px;
  padding: 15px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.view-all {
  font-size: 14px;
  color: #9040FF;
}

.group-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.group-item {
  display: flex;
  background: #F8FAFC;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.group-image {
  width: 100px;
  height: 100px;
  object-fit: cover;
}

.group-content {
  flex: 1;
  padding: 10px;
}

.group-title-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 5px;
}

.group-name {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-right: 60px;
}

.group-status {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.group-status.active {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.group-status.ending {
  background: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}

.group-status.package {
  background: rgba(144, 64, 255, 0.1);
  color: #9040FF;
}

.group-info {
  margin-bottom: 10px;
}

.info-item {
  display: flex;
  font-size: 12px;
  margin-bottom: 3px;
}

.info-label {
  color: #999;
  width: 65px;
}

.info-value {
  color: #666;
}

.original-price {
  text-decoration: line-through;
  color: #999;
}

.group-price {
  color: #FF3B30;
  font-weight: 600;
}

.time-left {
  color: #FF9500;
}

.group-progress {
  margin-top: 5px;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.progress-bar {
  height: 4px;
  background-color: #EBEDF5;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #9040FF, #5E35B1);
  border-radius: 2px;
}

/* 拼团设置样式 */
.settings-section {
  margin: 15px;
  padding: 15px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-header {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.settings-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}

.settings-item {
  width: 50%;
  padding: 7.5px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 7.5px;
}

.item-left {
  display: flex;
  align-items: center;
}

.item-icon {
  width: 24px;
  height: 24px;
  margin-right: 10px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.item-icon.rules {
  background-color: rgba(126, 48, 225, 0.1);
  position: relative;
}

.item-icon.rules::before {
  content: '';
  width: 12px;
  height: 12px;
  border: 2px solid #7E30E1;
  border-radius: 2px;
}

.item-icon.time {
  background-color: rgba(52, 199, 89, 0.1);
  position: relative;
}

.item-icon.time::before {
  content: '';
  width: 12px;
  height: 12px;
  border: 2px solid #34C759;
  border-radius: 6px;
}

.item-icon.discount {
  background-color: rgba(255, 59, 48, 0.1);
  position: relative;
}

.item-icon.discount::before {
  content: '%';
  color: #FF3B30;
  font-size: 14px;
  font-weight: bold;
}

.item-icon.notification {
  background-color: rgba(0, 122, 255, 0.1);
  position: relative;
}

.item-icon.notification::before {
  content: '';
  width: 12px;
  height: 12px;
  border: 2px solid #007AFF;
  border-radius: 6px;
  position: relative;
}

.item-icon.notification::after {
  content: '';
  position: absolute;
  width: 6px;
  height: 6px;
  background: #007AFF;
  border-radius: 3px;
  top: 9px;
  left: 9px;
}

.item-title {
  font-size: 12px;
  color: #333;
}

.item-right {
  display: flex;
  align-items: center;
}

.item-value {
  font-size: 12px;
  color: #333;
  margin-right: 10px;
}

.item-arrow {
  width: 8px;
  height: 8px;
  border-top: 2px solid #333;
  border-right: 2px solid #333;
  transform: rotate(45deg);
}

/* 拼团工具样式 */
.tools-section {
  margin: 15px;
  padding: 15px 12px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.tools-grid {
  display: flex;
  justify-content: space-between;
  margin: 0 -2px;
}

.tool-card {
  flex: 1;
  padding: 4px 2px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 0;
}

.tool-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background-color: #7E30E1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.tool-icon-svg {
  width: 20px;
  height: 20px;
}

.tool-name {
  font-size: 12px;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  margin-bottom: 4px;
}

.tool-desc {
  font-size: 10px;
  color: #999;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  max-width: 90px;
}
</style> 