{"version": 3, "file": "chat.js", "sources": ["pages/message/chat.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbWVzc2FnZS9jaGF0LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"chat-container\" :class=\"{'has-more-panel': showMorePanel}\">\n    <!-- 顶部安全区域 -->\n    <view class=\"safe-area-top\"></view>\n    \n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\">\n      <view class=\"navbar-left\" @click=\"goBack\">\n        <image src=\"/static/images/tabbar/最新返回键.png\" class=\"back-icon\"></image>\n      </view>\n      <view class=\"navbar-title\">{{chatTitle}}</view>\n      <view class=\"navbar-right\">\n        <!-- 预留位置 -->\n      </view>\n    </view>\n\n    <!-- 聊天内容区域 -->\n    <scroll-view \n      class=\"chat-content\" \n      scroll-y \n      :scroll-into-view=\"scrollIntoView\"\n      @scrolltoupper=\"loadMoreMessages\"\n      upper-threshold=\"50\"\n      @tap=\"closeMorePanel\"\n    >\n      <!-- 加载更多 -->\n      <view class=\"load-more\" v-if=\"hasMoreMessages\">\n        <text v-if=\"!isLoading\">加载更多</text>\n        <text v-else>加载中...</text>\n      </view>\n      \n      <!-- 消息列表 -->\n      <view class=\"message-list\">\n        <block v-for=\"(item, index) in messages\" :key=\"index\">\n          <!-- 时间分割线 -->\n          <view class=\"time-divider\" v-if=\"showTimeDivider(index)\">\n            <text>{{formatTime(item.time)}}</text>\n          </view>\n          \n          <view \n            class=\"message-item\" \n            :id=\"'msg-' + index\"\n            :class=\"{'self': item.isSelf}\"\n          >\n            <!-- 头像 -->\n            <view class=\"avatar-container\" v-if=\"!item.isSelf\">\n              <image class=\"avatar\" :src=\"item.avatar\" mode=\"aspectFill\"></image>\n            </view>\n            \n            <!-- 消息气泡 -->\n            <view class=\"message-bubble\" :class=\"{'self': item.isSelf}\">\n              <!-- 普通文本消息 -->\n              <text v-if=\"!item.type || item.type === 'text'\" class=\"message-text\">{{item.content}}</text>\n              \n              <!-- 定位消息 -->\n              <view v-else-if=\"item.type === 'location'\" class=\"location-message\" @tap=\"viewLocation(item.locationData)\">\n                <view class=\"location-icon\">\n                  <image src=\"/static/images/tabbar/定位.png\" mode=\"aspectFit\"></image>\n                </view>\n                <view class=\"location-info\">\n                  <text class=\"location-name\">{{item.locationData.name}}</text>\n                  <text class=\"location-address\">{{item.locationData.address}}</text>\n                </view>\n                <view class=\"location-arrow\">\n                  <text class=\"arrow-icon\">></text>\n                </view>\n              </view>\n              \n              <!-- 图片消息 -->\n              <view v-else-if=\"item.type === 'image'\" class=\"image-message\" @tap=\"previewImage(item.imageUrl)\">\n                <image :src=\"item.imageUrl\" mode=\"widthFix\" class=\"message-image\"></image>\n              </view>\n            </view>\n            \n            <!-- 头像 -->\n            <view class=\"avatar-container\" v-if=\"item.isSelf\">\n              <image class=\"avatar\" :src=\"userInfo.avatar\" mode=\"aspectFill\"></image>\n            </view>\n          </view>\n        </block>\n      </view>\n    </scroll-view>\n\n    <!-- 输入区域 -->\n    <view class=\"input-area\" :style=\"{ bottom: isKeyboardShow ? keyboardHeight + 'px' : '0' }\">\n      <view class=\"input-box\">\n        <textarea \n          class=\"input-field\" \n          v-model=\"messageText\" \n          placeholder=\"请输入消息...\" \n          :adjust-position=\"true\"\n          :show-confirm-bar=\"false\"\n          :cursor-spacing=\"20\"\n          :maxlength=\"-1\"\n          @focus=\"onInputFocus\"\n          @blur=\"onInputBlur\"\n          @confirm=\"sendMessage\"\n          @input=\"onInput\"\n          :style=\"{ 'max-height': '120rpx' }\"\n        ></textarea>\n      </view>\n      <view class=\"action-btn\" :class=\"{'has-text': messageText.trim().length > 0}\" @tap=\"messageText.trim().length > 0 ? sendMessage() : toggleMorePanel()\">\n        <text v-if=\"messageText.trim().length > 0\" class=\"send-text\">发送</text>\n        <image v-else src=\"/static/images/tabbar/添加.png\" mode=\"aspectFit\" class=\"add-icon\"></image>\n      </view>\n    </view>\n    \n    <!-- 底部安全区域 -->\n    <view class=\"safe-area-bottom\"></view>\n    \n    <!-- 扩展功能面板（抽屉式） -->\n    <view class=\"more-panel-mask\" v-if=\"showMorePanel\" @tap.stop=\"closeMorePanel\"></view>\n    <view class=\"more-panel-drawer\" :class=\"{'show': showMorePanel}\">\n      <view class=\"more-panel-content\">\n        <view class=\"more-grid\">\n          <view class=\"more-item\" @tap.stop=\"sendLocation\">\n            <view class=\"more-icon location-icon\">\n              <image src=\"/static/images/tabbar/定位.png\" mode=\"aspectFit\" class=\"panel-icon-image\"></image>\n            </view>\n            <text class=\"more-text\">位置</text>\n          </view>\n          <view class=\"more-item\" @tap.stop=\"sendImage\">\n            <view class=\"more-icon image-icon\">\n              <image src=\"/static/images/tabbar/相册.png\" mode=\"aspectFit\" class=\"panel-icon-image\"></image>\n            </view>\n            <text class=\"more-text\">图片</text>\n          </view>\n          <view class=\"more-item\" @tap.stop=\"sendFile\">\n            <view class=\"more-icon file-icon\">\n              <image src=\"/static/images/tabbar/文件.png\" mode=\"aspectFit\" class=\"panel-icon-image\"></image>\n            </view>\n            <text class=\"more-text\">文件</text>\n          </view>\n          <view class=\"more-item\" @tap.stop=\"sendContact\">\n            <view class=\"more-icon contact-icon\">\n              <image src=\"/static/images/tabbar/联系人.png\" mode=\"aspectFit\" class=\"panel-icon-image\"></image>\n            </view>\n            <text class=\"more-text\">名片</text>\n          </view>\n        </view>\n      </view>\n      <view class=\"more-panel-handle\" @tap.stop=\"closeMorePanel\">\n        <view class=\"handle-line\"></view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { getLocalUserInfo } from '@/utils/userProfile.js';\n\nexport default {\n  data() {\n    return {\n      userId: '', // 聊天对象ID\n      nickname: '', // 聊天对象昵称\n      chatTitle: '私信', // 聊天标题\n      messages: [], // 消息列表\n      messageText: '', // 输入框文本\n      scrollIntoView: '', // 滚动到指定位置\n      hasMoreMessages: false, // 是否有更多历史消息\n      isLoading: false, // 是否正在加载\n      page: 1, // 当前页码\n      userInfo: {\n        avatar: '/static/images/default-avatar.png'\n      }, // 当前用户信息\n      isKeyboardShow: false,\n      keyboardHeight: 0,\n      showMorePanel: false, // 是否显示扩展功能面板\n    }\n  },\n  onLoad(options) {\n    // 获取参数\n    this.userId = options.userId || '';\n    this.nickname = options.nickname || '用户';\n    this.chatTitle = this.nickname;\n    \n    // 设置导航栏样式\n    uni.setNavigationBarColor({\n      frontColor: '#ffffff',\n      backgroundColor: '#0A84FF'\n    });\n    \n    // 获取当前用户信息\n    this.getUserInfo();\n    \n    // 加载历史消息\n    this.loadMessages();\n  },\n  methods: {\n    // 返回上一页\n    goBack() {\n      uni.navigateBack();\n    },\n    \n    // 获取当前用户信息\n    getUserInfo() {\n      const userInfo = getLocalUserInfo();\n      if (userInfo) {\n        this.userInfo = userInfo;\n      }\n    },\n    \n    // 加载消息\n    loadMessages() {\n      this.isLoading = true;\n      \n      // 模拟API请求延迟\n      setTimeout(() => {\n        // 模拟消息数据\n        const mockMessages = this.generateMockMessages();\n        \n        if (this.page === 1) {\n          this.messages = mockMessages;\n          // 滚动到最新消息\n          this.$nextTick(() => {\n            this.scrollToBottom();\n          });\n        } else {\n          this.messages = [...mockMessages, ...this.messages];\n        }\n        \n        this.hasMoreMessages = this.page < 3; // 模拟只有3页历史消息\n        this.isLoading = false;\n      }, 500);\n    },\n    \n    // 加载更多历史消息\n    loadMoreMessages() {\n      if (this.isLoading || !this.hasMoreMessages) return;\n      \n      this.page++;\n      this.loadMessages();\n    },\n    \n    // 发送消息\n    sendMessage() {\n      const content = this.messageText.trim();\n      if (!content) return;\n      \n      // 添加新消息\n      const newMessage = {\n        id: Date.now().toString(),\n        content,\n        time: new Date(),\n        isSelf: true,\n        avatar: this.userInfo.avatar\n      };\n      \n      this.messages.push(newMessage);\n      this.messageText = '';\n      \n      // 滚动到最新消息\n      this.$nextTick(() => {\n        this.scrollToBottom();\n      });\n      \n      // 模拟对方回复\n      setTimeout(() => {\n        const replyMessage = {\n          id: (Date.now() + 1).toString(),\n          content: this.getRandomReply(),\n          time: new Date(),\n          isSelf: false,\n          avatar: '/static/images/default-avatar.png'\n        };\n        \n        this.messages.push(replyMessage);\n        \n        // 滚动到最新消息\n        this.$nextTick(() => {\n          this.scrollToBottom();\n        });\n      }, 1000);\n    },\n    \n    // 滚动到底部\n    scrollToBottom() {\n      if (this.messages.length > 0) {\n        this.scrollIntoView = 'msg-' + (this.messages.length - 1);\n      }\n    },\n    \n    // 输入框获取焦点\n    onInputFocus(e) {\n      this.isKeyboardShow = true;\n      this.keyboardHeight = e.detail.height || 0;\n      // 滚动到最新消息\n      this.scrollToBottom();\n    },\n    \n    // 输入框失去焦点\n    onInputBlur() {\n      this.isKeyboardShow = false;\n      this.keyboardHeight = 0;\n    },\n    \n    // 判断是否显示时间分割线\n    showTimeDivider(index) {\n      if (index === 0) return true;\n      \n      const currentMsg = this.messages[index];\n      const prevMsg = this.messages[index - 1];\n      \n      // 如果与上一条消息时间相差超过5分钟，显示时间分割线\n      return new Date(currentMsg.time) - new Date(prevMsg.time) > 5 * 60 * 1000;\n    },\n    \n    // 格式化时间\n    formatTime(time) {\n      const date = new Date(time);\n      const now = new Date();\n      const isToday = date.toDateString() === now.toDateString();\n      \n      const hours = date.getHours().toString().padStart(2, '0');\n      const minutes = date.getMinutes().toString().padStart(2, '0');\n      \n      if (isToday) {\n        return `${hours}:${minutes}`;\n      } else {\n        const month = (date.getMonth() + 1).toString().padStart(2, '0');\n        const day = date.getDate().toString().padStart(2, '0');\n        return `${month}-${day} ${hours}:${minutes}`;\n      }\n    },\n    \n    // 生成模拟消息数据\n    generateMockMessages() {\n      const mockMessages = [];\n      const count = 10; // 每页10条消息\n      \n      // 基准时间，根据页码设置\n      const baseTime = new Date();\n      baseTime.setHours(baseTime.getHours() - (this.page * 2));\n      \n      for (let i = 0; i < count; i++) {\n        const isSelf = Math.random() > 0.5;\n        const messageTime = new Date(baseTime);\n        messageTime.setMinutes(messageTime.getMinutes() + i * 10);\n        \n        mockMessages.push({\n          id: `msg_${this.page}_${i}`,\n          content: this.getRandomMessage(isSelf),\n          time: messageTime,\n          isSelf,\n          avatar: isSelf ? this.userInfo.avatar : '/static/images/default-avatar.png'\n        });\n      }\n      \n      return mockMessages;\n    },\n    \n    // 获取随机消息内容\n    getRandomMessage(isSelf) {\n      const selfMessages = [\n        '你好，最近怎么样？',\n        '有时间一起出来喝杯咖啡吗？',\n        '推荐一下附近有什么好吃的地方？',\n        '最近有什么新活动吗？',\n        '谢谢你的关注！'\n      ];\n      \n      const otherMessages = [\n        '你好，我很好，谢谢关心！',\n        '最近有点忙，改天约时间吧',\n        '附近新开了一家火锅店，味道不错',\n        '周末有个音乐节活动，要一起去吗？',\n        '不客气，我很喜欢你分享的内容'\n      ];\n      \n      const messages = isSelf ? selfMessages : otherMessages;\n      return messages[Math.floor(Math.random() * messages.length)];\n    },\n    \n    // 获取随机回复\n    getRandomReply() {\n      const replies = [\n        '好的，我知道了',\n        '嗯嗯，没问题',\n        '谢谢你告诉我这些',\n        '我会考虑的',\n        '有空再聊吧',\n        '这个主意不错',\n        '我同意你的看法',\n        '需要我帮忙吗？',\n        '我会尽快回复你的',\n        '我明白你的意思了'\n      ];\n      \n      return replies[Math.floor(Math.random() * replies.length)];\n    },\n    \n    // 发送定位\n    sendLocation() {\n      // 关闭更多面板\n      this.showMorePanel = false;\n      \n      uni.authorize({\n        scope: 'scope.userLocation',\n        success: () => {\n          uni.chooseLocation({\n            success: (res) => {\n              if (res.name && res.address) {\n                // 创建定位消息\n                const locationMsg = {\n                  id: Date.now().toString(),\n                  type: 'location',\n                  content: `[位置] ${res.name}`,\n                  locationData: {\n                    name: res.name,\n                    address: res.address,\n                    latitude: res.latitude,\n                    longitude: res.longitude\n                  },\n                  time: new Date(),\n                  isSelf: true,\n                  avatar: this.userInfo.avatar\n                };\n                \n                // 添加到消息列表\n                this.messages.push(locationMsg);\n                \n                // 滚动到最新消息\n                this.$nextTick(() => {\n                  this.scrollToBottom();\n                });\n                \n                // 模拟对方回复\n                setTimeout(() => {\n                  const replyMessage = {\n                    id: (Date.now() + 1).toString(),\n                    content: '我已收到您的位置信息',\n                    time: new Date(),\n                    isSelf: false,\n                    avatar: '/static/images/default-avatar.png'\n                  };\n                  \n                  this.messages.push(replyMessage);\n                  \n                  // 滚动到最新消息\n                  this.$nextTick(() => {\n                    this.scrollToBottom();\n                  });\n                }, 1000);\n              }\n            },\n            fail: (err) => {\n              console.error('选择位置失败', err);\n              uni.showToast({\n                title: '选择位置失败',\n                icon: 'none'\n              });\n            }\n          });\n        },\n        fail: () => {\n          uni.showModal({\n            title: '提示',\n            content: '需要您授权获取位置信息，是否前往设置？',\n            confirmText: '去设置',\n            success: (res) => {\n              if (res.confirm) {\n                uni.openSetting();\n              }\n            }\n          });\n        }\n      });\n    },\n    \n    // 查看定位\n    viewLocation(locationData) {\n      uni.openLocation({\n        latitude: locationData.latitude,\n        longitude: locationData.longitude,\n        name: locationData.name,\n        address: locationData.address,\n        success: () => {\n          console.log('打开位置成功');\n        },\n        fail: (err) => {\n          console.error('打开位置失败', err);\n          uni.showToast({\n            title: '打开位置失败',\n            icon: 'none'\n          });\n        }\n      });\n    },\n    \n    // 输入变化\n    onInput(e) {\n      // 输入时自动关闭更多面板\n      if (this.messageText.trim().length > 0 && this.showMorePanel) {\n        this.showMorePanel = false;\n      }\n    },\n    \n    // 切换扩展功能面板\n    toggleMorePanel() {\n      if (this.messageText.trim().length > 0) {\n        this.sendMessage();\n      } else {\n        // 切换面板状态\n        if (!this.showMorePanel) {\n          uni.hideKeyboard();\n          // 延迟显示面板，确保键盘收起\n          setTimeout(() => {\n            this.showMorePanel = true;\n            // 监听点击事件，点击内容区域关闭面板\n            setTimeout(() => {\n              uni.$once('chat-content-tap', () => {\n                this.showMorePanel = false;\n              });\n            }, 300);\n          }, 50);\n        } else {\n          this.showMorePanel = false;\n        }\n      }\n    },\n    \n    // 发送图片\n    sendImage() {\n      uni.chooseImage({\n        count: 1,\n        success: (res) => {\n          const tempFilePath = res.tempFilePaths[0];\n          \n          // 创建图片消息\n          const imageMsg = {\n            id: Date.now().toString(),\n            type: 'image',\n            content: '[图片]',\n            imageUrl: tempFilePath,\n            time: new Date(),\n            isSelf: true,\n            avatar: this.userInfo.avatar\n          };\n          \n          // 添加到消息列表\n          this.messages.push(imageMsg);\n          \n          // 关闭更多面板\n          this.showMorePanel = false;\n          \n          // 滚动到最新消息\n          this.$nextTick(() => {\n            this.scrollToBottom();\n          });\n          \n          // 模拟对方回复\n          setTimeout(() => {\n            const replyMessage = {\n              id: (Date.now() + 1).toString(),\n              content: '收到您发送的图片了',\n              time: new Date(),\n              isSelf: false,\n              avatar: '/static/images/default-avatar.png'\n            };\n            \n            this.messages.push(replyMessage);\n            \n            // 滚动到最新消息\n            this.$nextTick(() => {\n              this.scrollToBottom();\n            });\n          }, 1000);\n        }\n      });\n    },\n    \n    // 发送文件\n    sendFile() {\n      // 提示功能开发中\n      uni.showToast({\n        title: '文件发送功能开发中',\n        icon: 'none'\n      });\n      this.showMorePanel = false;\n    },\n    \n    // 发送名片\n    sendContact() {\n      // 提示功能开发中\n      uni.showToast({\n        title: '名片发送功能开发中',\n        icon: 'none'\n      });\n      this.showMorePanel = false;\n    },\n    \n    // 预览图片\n    previewImage(url) {\n      uni.previewImage({\n        urls: [url],\n        current: url\n      });\n    },\n    \n    // 关闭扩展功能面板\n    closeMorePanel() {\n      if (this.showMorePanel) {\n        this.showMorePanel = false;\n      }\n    },\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.chat-container {\n  min-height: 100vh;\n  background-color: #f9f9f9;\n  background-image: linear-gradient(rgba(226, 226, 226, 0.3) 1px, transparent 1px),\n                    linear-gradient(90deg, rgba(226, 226, 226, 0.3) 1px, transparent 1px);\n  background-size: 20px 20px;\n  display: flex;\n  flex-direction: column;\n  padding-top: 0;\n  box-sizing: border-box;\n  position: relative;\n  \n  &.has-more-panel {\n    padding-bottom: 400rpx;\n  }\n}\n\n/* 自定义导航栏 */\n.custom-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: calc(110rpx + var(--status-bar-height));\n  padding-top: var(--status-bar-height);\n  background: linear-gradient(135deg, #0A84FF, #0040DD);\n  z-index: 999;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: var(--status-bar-height) 30rpx 0;\n  box-sizing: border-box;\n  width: 100%;\n}\n\n.navbar-title {\n  position: absolute;\n  left: 0;\n  right: 0;\n  color: #FFFFFF;\n  font-size: 36rpx;\n  font-weight: 700;\n  text-align: center;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n  letter-spacing: 1rpx;\n}\n\n.navbar-left, .navbar-right {\n  width: 64rpx;\n  height: 64rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  z-index: 1000;\n}\n\n.back-icon {\n  width: 44rpx;\n  height: 44rpx;\n  filter: brightness(0) invert(1);\n}\n\n.safe-area-top {\n  height: var(--status-bar-height);\n  width: 100%;\n  background: linear-gradient(135deg, #0A84FF, #0040DD);\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 998;\n}\n\n/* 聊天内容区域 */\n.chat-content {\n  height: 100vh;\n  padding: 20rpx 30rpx;\n  padding-top: calc(110rpx + var(--status-bar-height) + 20rpx);\n  padding-bottom: calc(104rpx + 20rpx);\n  box-sizing: border-box;\n  width: 100%;\n  background-color: #f9f9f9;\n}\n\n.load-more {\n  text-align: center;\n  font-size: 24rpx;\n  color: #999999;\n  padding: 20rpx 0;\n}\n\n.message-list {\n  padding-bottom: 20rpx;\n  width: 100%;\n}\n\n.message-item {\n  display: flex;\n  margin-bottom: 30rpx;\n  width: 100%;\n  animation: fadeIn 0.3s ease-out;\n  \n  &.self {\n    flex-direction: row-reverse;\n    justify-content: flex-start;\n  }\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.time-divider {\n  width: 100%;\n  text-align: center;\n  margin: 20rpx 0;\n  position: relative;\n  left: 0;\n  right: 0;\n  \n  text {\n    font-size: 24rpx;\n    color: #8e8e93;\n    background-color: rgba(142, 142, 147, 0.1);\n    padding: 4rpx 12rpx;\n    border-radius: 10rpx;\n  }\n}\n\n.avatar-container {\n  margin: 0 20rpx;\n  flex-shrink: 0;\n  width: 80rpx;\n  height: 80rpx;\n}\n\n.avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  background-color: #f0f0f0;\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);\n}\n\n.message-bubble {\n  max-width: 55%;\n  padding: 20rpx 24rpx;\n  border-radius: 20rpx;\n  background-color: #ffffff;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n  position: relative;\n  word-break: break-all;\n  word-wrap: break-word;\n  flex-shrink: 1;\n  \n  &.self {\n    background-color: #007AFF;\n    color: #ffffff;\n    margin-right: 16rpx;\n  }\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: 20rpx;\n    left: -16rpx;\n    width: 0;\n    height: 0;\n    border-style: solid;\n    border-width: 8rpx 16rpx 8rpx 0;\n    border-color: transparent #ffffff transparent transparent;\n  }\n  \n  &.self::before {\n    left: auto;\n    right: -16rpx;\n    border-width: 8rpx 0 8rpx 16rpx;\n    border-color: transparent transparent transparent #007AFF;\n  }\n}\n\n.message-text {\n  font-size: 32rpx;\n  line-height: 1.5;\n  word-break: break-all;\n  word-wrap: break-word;\n  white-space: normal;\n  display: inline-block;\n  max-width: 100%;\n}\n\n/* 图片消息样式 */\n.image-message {\n  padding: 0;\n  border-radius: 20rpx;\n  overflow: hidden;\n}\n\n.message-image {\n  width: 400rpx;\n  max-width: 400rpx;\n  border-radius: 20rpx;\n}\n\n/* 定位消息样式 */\n.location-message {\n  display: flex;\n  align-items: center;\n  padding: 20rpx;\n  width: 400rpx;\n  background-color: rgba(255, 255, 255, 0.9);\n  border-radius: 16rpx;\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);\n  \n  .location-icon {\n    width: 48rpx;\n    height: 48rpx;\n    margin-right: 16rpx;\n    \n    image {\n      width: 100%;\n      height: 100%;\n    }\n  }\n  \n  .location-info {\n    flex: 1;\n    display: flex;\n    flex-direction: column;\n  }\n  \n  .location-name {\n    font-size: 28rpx;\n    font-weight: 600;\n    margin-bottom: 8rpx;\n    color: #333333;\n  }\n  \n  .location-address {\n    font-size: 24rpx;\n    color: #8e8e93;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n  }\n  \n  .location-arrow {\n    width: 24rpx;\n    height: 24rpx;\n    margin-left: 16rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  }\n  \n  .arrow-icon {\n    font-size: 24rpx;\n    color: #8e8e93;\n  }\n}\n\n/* 输入区域 */\n.input-area {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  padding: 12rpx 20rpx;\n  padding-bottom: calc(12rpx + env(safe-area-inset-bottom));\n  background-color: rgba(249, 249, 249, 0.95);\n  backdrop-filter: blur(10px);\n  -webkit-backdrop-filter: blur(10px);\n  border-top: 1px solid rgba(0, 0, 0, 0.1);\n  display: flex;\n  align-items: center;\n  z-index: 99;\n}\n\n.input-box {\n  flex: 1;\n  background-color: #f2f2f7;\n  border-radius: 36rpx;\n  padding: 10rpx 20rpx;\n  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);\n  height: 80rpx;\n  display: flex;\n  align-items: center;\n}\n\n.input-field {\n  width: 100%;\n  height: 60rpx;\n  font-size: 30rpx;\n  line-height: 1.4;\n}\n\n.action-btn {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  background-color: #f2f2f7;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-left: 20rpx;\n  transition: all 0.15s ease;\n  position: relative;\n  \n  &:active {\n    transform: scale(0.9);\n    background-color: #e0e0e0;\n  }\n  \n  &.has-text {\n    background-color: #0A84FF;\n  }\n}\n\n.send-text {\n  font-size: 28rpx;\n  color: #ffffff;\n  font-weight: 500;\n}\n\n.add-icon {\n  width: 30rpx;\n  height: 30rpx;\n}\n\n.more-icon {\n  width: 76rpx;\n  height: 76rpx;\n  border-radius: 16rpx;\n  background-color: #ffffff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 8rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);\n}\n\n.panel-icon-image {\n  width: 30rpx;\n  height: 30rpx;\n}\n\n.safe-area-bottom {\n  height: env(safe-area-inset-bottom);\n  width: 100%;\n  background-color: #ffffff;\n}\n\n/* 抽屉式扩展功能面板 */\n.more-panel-mask {\n  position: fixed;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  background-color: rgba(0, 0, 0, 0.4);\n  z-index: 90;\n}\n\n.more-panel-drawer {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 300rpx;\n  background-color: #ffffff;\n  border-top-left-radius: 24rpx;\n  border-top-right-radius: 24rpx;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);\n  z-index: 100;\n  transform: translateY(100%);\n  transition: transform 0.3s ease;\n  \n  &.show {\n    transform: translateY(0);\n  }\n}\n\n.more-panel-content {\n  padding: 20rpx;\n  height: 100%;\n  box-sizing: border-box;\n}\n\n.more-panel-handle {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 40rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.handle-line {\n  width: 60rpx;\n  height: 6rpx;\n  background-color: #e0e0e0;\n  border-radius: 3rpx;\n}\n\n.more-grid {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 20rpx 0;\n}\n\n.more-item {\n  width: 25%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n}\n\n.more-text {\n  font-size: 22rpx;\n  color: #333333;\n  font-weight: 400;\n}\n\n.location-icon {\n  background-color: #07C160;\n  \n  image {\n    filter: brightness(0) invert(1);\n  }\n}\n\n.image-icon {\n  background-color: #1677FF;\n  \n  image {\n    filter: brightness(0) invert(1);\n  }\n}\n\n.file-icon {\n  background-color: #FA5151;\n  \n  image {\n    filter: brightness(0) invert(1);\n  }\n}\n\n.contact-icon {\n  background-color: #6467F0;\n  \n  image {\n    filter: brightness(0) invert(1);\n  }\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/message/chat.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "getLocalUserInfo"], "mappings": ";;;;AAuJA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,QAAQ;AAAA;AAAA,MACR,UAAU;AAAA;AAAA,MACV,WAAW;AAAA;AAAA,MACX,UAAU,CAAE;AAAA;AAAA,MACZ,aAAa;AAAA;AAAA,MACb,gBAAgB;AAAA;AAAA,MAChB,iBAAiB;AAAA;AAAA,MACjB,WAAW;AAAA;AAAA,MACX,MAAM;AAAA;AAAA,MACN,UAAU;AAAA,QACR,QAAQ;AAAA,MACT;AAAA;AAAA,MACD,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,eAAe;AAAA;AAAA,IACjB;AAAA,EACD;AAAA,EACD,OAAO,SAAS;AAEd,SAAK,SAAS,QAAQ,UAAU;AAChC,SAAK,WAAW,QAAQ,YAAY;AACpC,SAAK,YAAY,KAAK;AAGtBA,kBAAAA,MAAI,sBAAsB;AAAA,MACxB,YAAY;AAAA,MACZ,iBAAiB;AAAA,IACnB,CAAC;AAGD,SAAK,YAAW;AAGhB,SAAK,aAAY;AAAA,EAClB;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA;AAAA,IAGD,cAAc;AACZ,YAAM,WAAWC,kBAAAA;AACjB,UAAI,UAAU;AACZ,aAAK,WAAW;AAAA,MAClB;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACb,WAAK,YAAY;AAGjB,iBAAW,MAAM;AAEf,cAAM,eAAe,KAAK;AAE1B,YAAI,KAAK,SAAS,GAAG;AACnB,eAAK,WAAW;AAEhB,eAAK,UAAU,MAAM;AACnB,iBAAK,eAAc;AAAA,UACrB,CAAC;AAAA,eACI;AACL,eAAK,WAAW,CAAC,GAAG,cAAc,GAAG,KAAK,QAAQ;AAAA,QACpD;AAEA,aAAK,kBAAkB,KAAK,OAAO;AACnC,aAAK,YAAY;AAAA,MAClB,GAAE,GAAG;AAAA,IACP;AAAA;AAAA,IAGD,mBAAmB;AACjB,UAAI,KAAK,aAAa,CAAC,KAAK;AAAiB;AAE7C,WAAK;AACL,WAAK,aAAY;AAAA,IAClB;AAAA;AAAA,IAGD,cAAc;AACZ,YAAM,UAAU,KAAK,YAAY,KAAI;AACrC,UAAI,CAAC;AAAS;AAGd,YAAM,aAAa;AAAA,QACjB,IAAI,KAAK,IAAK,EAAC,SAAU;AAAA,QACzB;AAAA,QACA,MAAM,oBAAI,KAAM;AAAA,QAChB,QAAQ;AAAA,QACR,QAAQ,KAAK,SAAS;AAAA;AAGxB,WAAK,SAAS,KAAK,UAAU;AAC7B,WAAK,cAAc;AAGnB,WAAK,UAAU,MAAM;AACnB,aAAK,eAAc;AAAA,MACrB,CAAC;AAGD,iBAAW,MAAM;AACf,cAAM,eAAe;AAAA,UACnB,KAAK,KAAK,IAAG,IAAK,GAAG,SAAU;AAAA,UAC/B,SAAS,KAAK,eAAgB;AAAA,UAC9B,MAAM,oBAAI,KAAM;AAAA,UAChB,QAAQ;AAAA,UACR,QAAQ;AAAA;AAGV,aAAK,SAAS,KAAK,YAAY;AAG/B,aAAK,UAAU,MAAM;AACnB,eAAK,eAAc;AAAA,QACrB,CAAC;AAAA,MACF,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,IAGD,iBAAiB;AACf,UAAI,KAAK,SAAS,SAAS,GAAG;AAC5B,aAAK,iBAAiB,UAAU,KAAK,SAAS,SAAS;AAAA,MACzD;AAAA,IACD;AAAA;AAAA,IAGD,aAAa,GAAG;AACd,WAAK,iBAAiB;AACtB,WAAK,iBAAiB,EAAE,OAAO,UAAU;AAEzC,WAAK,eAAc;AAAA,IACpB;AAAA;AAAA,IAGD,cAAc;AACZ,WAAK,iBAAiB;AACtB,WAAK,iBAAiB;AAAA,IACvB;AAAA;AAAA,IAGD,gBAAgB,OAAO;AACrB,UAAI,UAAU;AAAG,eAAO;AAExB,YAAM,aAAa,KAAK,SAAS,KAAK;AACtC,YAAM,UAAU,KAAK,SAAS,QAAQ,CAAC;AAGvC,aAAO,IAAI,KAAK,WAAW,IAAI,IAAI,IAAI,KAAK,QAAQ,IAAI,IAAI,IAAI,KAAK;AAAA,IACtE;AAAA;AAAA,IAGD,WAAW,MAAM;AACf,YAAM,OAAO,IAAI,KAAK,IAAI;AAC1B,YAAM,MAAM,oBAAI;AAChB,YAAM,UAAU,KAAK,aAAe,MAAI,IAAI,aAAY;AAExD,YAAM,QAAQ,KAAK,SAAU,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACxD,YAAM,UAAU,KAAK,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAE5D,UAAI,SAAS;AACX,eAAO,GAAG,KAAK,IAAI,OAAO;AAAA,aACrB;AACL,cAAM,SAAS,KAAK,aAAa,GAAG,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC9D,cAAM,MAAM,KAAK,QAAS,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACrD,eAAO,GAAG,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,OAAO;AAAA,MAC5C;AAAA,IACD;AAAA;AAAA,IAGD,uBAAuB;AACrB,YAAM,eAAe,CAAA;AACrB,YAAM,QAAQ;AAGd,YAAM,WAAW,oBAAI;AACrB,eAAS,SAAS,SAAS,SAAQ,IAAM,KAAK,OAAO,CAAE;AAEvD,eAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,cAAM,SAAS,KAAK,OAAM,IAAK;AAC/B,cAAM,cAAc,IAAI,KAAK,QAAQ;AACrC,oBAAY,WAAW,YAAY,WAAU,IAAK,IAAI,EAAE;AAExD,qBAAa,KAAK;AAAA,UAChB,IAAI,OAAO,KAAK,IAAI,IAAI,CAAC;AAAA,UACzB,SAAS,KAAK,iBAAiB,MAAM;AAAA,UACrC,MAAM;AAAA,UACN;AAAA,UACA,QAAQ,SAAS,KAAK,SAAS,SAAS;AAAA,QAC1C,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,iBAAiB,QAAQ;AACvB,YAAM,eAAe;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAGF,YAAM,gBAAgB;AAAA,QACpB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAGF,YAAM,WAAW,SAAS,eAAe;AACzC,aAAO,SAAS,KAAK,MAAM,KAAK,WAAW,SAAS,MAAM,CAAC;AAAA,IAC5D;AAAA;AAAA,IAGD,iBAAiB;AACf,YAAM,UAAU;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAGF,aAAO,QAAQ,KAAK,MAAM,KAAK,WAAW,QAAQ,MAAM,CAAC;AAAA,IAC1D;AAAA;AAAA,IAGD,eAAe;AAEb,WAAK,gBAAgB;AAErBD,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,MAAM;AACbA,wBAAAA,MAAI,eAAe;AAAA,YACjB,SAAS,CAAC,QAAQ;AAChB,kBAAI,IAAI,QAAQ,IAAI,SAAS;AAE3B,sBAAM,cAAc;AAAA,kBAClB,IAAI,KAAK,IAAK,EAAC,SAAU;AAAA,kBACzB,MAAM;AAAA,kBACN,SAAS,QAAQ,IAAI,IAAI;AAAA,kBACzB,cAAc;AAAA,oBACZ,MAAM,IAAI;AAAA,oBACV,SAAS,IAAI;AAAA,oBACb,UAAU,IAAI;AAAA,oBACd,WAAW,IAAI;AAAA,kBAChB;AAAA,kBACD,MAAM,oBAAI,KAAM;AAAA,kBAChB,QAAQ;AAAA,kBACR,QAAQ,KAAK,SAAS;AAAA;AAIxB,qBAAK,SAAS,KAAK,WAAW;AAG9B,qBAAK,UAAU,MAAM;AACnB,uBAAK,eAAc;AAAA,gBACrB,CAAC;AAGD,2BAAW,MAAM;AACf,wBAAM,eAAe;AAAA,oBACnB,KAAK,KAAK,IAAG,IAAK,GAAG,SAAU;AAAA,oBAC/B,SAAS;AAAA,oBACT,MAAM,oBAAI,KAAM;AAAA,oBAChB,QAAQ;AAAA,oBACR,QAAQ;AAAA;AAGV,uBAAK,SAAS,KAAK,YAAY;AAG/B,uBAAK,UAAU,MAAM;AACnB,yBAAK,eAAc;AAAA,kBACrB,CAAC;AAAA,gBACF,GAAE,GAAI;AAAA,cACT;AAAA,YACD;AAAA,YACD,MAAM,CAAC,QAAQ;AACbA,4BAAA,MAAA,MAAA,SAAA,iCAAc,UAAU,GAAG;AAC3BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACF;AAAA,QACD,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS;AAAA,YACT,aAAa;AAAA,YACb,SAAS,CAAC,QAAQ;AAChB,kBAAI,IAAI,SAAS;AACfA,8BAAG,MAAC,YAAW;AAAA,cACjB;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,aAAa,cAAc;AACzBA,oBAAAA,MAAI,aAAa;AAAA,QACf,UAAU,aAAa;AAAA,QACvB,WAAW,aAAa;AAAA,QACxB,MAAM,aAAa;AAAA,QACnB,SAAS,aAAa;AAAA,QACtB,SAAS,MAAM;AACbA,wBAAAA,oDAAY,QAAQ;AAAA,QACrB;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAA,MAAA,MAAA,SAAA,iCAAc,UAAU,GAAG;AAC3BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,QAAQ,GAAG;AAET,UAAI,KAAK,YAAY,KAAI,EAAG,SAAS,KAAK,KAAK,eAAe;AAC5D,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB;AAChB,UAAI,KAAK,YAAY,KAAI,EAAG,SAAS,GAAG;AACtC,aAAK,YAAW;AAAA,aACX;AAEL,YAAI,CAAC,KAAK,eAAe;AACvBA,wBAAG,MAAC,aAAY;AAEhB,qBAAW,MAAM;AACf,iBAAK,gBAAgB;AAErB,uBAAW,MAAM;AACfA,kCAAI,MAAM,oBAAoB,MAAM;AAClC,qBAAK,gBAAgB;AAAA,cACvB,CAAC;AAAA,YACF,GAAE,GAAG;AAAA,UACP,GAAE,EAAE;AAAA,eACA;AACL,eAAK,gBAAgB;AAAA,QACvB;AAAA,MACF;AAAA,IACD;AAAA;AAAA,IAGD,YAAY;AACVA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,SAAS,CAAC,QAAQ;AAChB,gBAAM,eAAe,IAAI,cAAc,CAAC;AAGxC,gBAAM,WAAW;AAAA,YACf,IAAI,KAAK,IAAK,EAAC,SAAU;AAAA,YACzB,MAAM;AAAA,YACN,SAAS;AAAA,YACT,UAAU;AAAA,YACV,MAAM,oBAAI,KAAM;AAAA,YAChB,QAAQ;AAAA,YACR,QAAQ,KAAK,SAAS;AAAA;AAIxB,eAAK,SAAS,KAAK,QAAQ;AAG3B,eAAK,gBAAgB;AAGrB,eAAK,UAAU,MAAM;AACnB,iBAAK,eAAc;AAAA,UACrB,CAAC;AAGD,qBAAW,MAAM;AACf,kBAAM,eAAe;AAAA,cACnB,KAAK,KAAK,IAAG,IAAK,GAAG,SAAU;AAAA,cAC/B,SAAS;AAAA,cACT,MAAM,oBAAI,KAAM;AAAA,cAChB,QAAQ;AAAA,cACR,QAAQ;AAAA;AAGV,iBAAK,SAAS,KAAK,YAAY;AAG/B,iBAAK,UAAU,MAAM;AACnB,mBAAK,eAAc;AAAA,YACrB,CAAC;AAAA,UACF,GAAE,GAAI;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,WAAW;AAETA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AACD,WAAK,gBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,cAAc;AAEZA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AACD,WAAK,gBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,aAAa,KAAK;AAChBA,oBAAAA,MAAI,aAAa;AAAA,QACf,MAAM,CAAC,GAAG;AAAA,QACV,SAAS;AAAA,MACX,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB;AACf,UAAI,KAAK,eAAe;AACtB,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACD;AAAA,EACH;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9lBA,GAAG,WAAW,eAAe;"}