{"version": 3, "file": "trip-records.js", "sources": ["carpool-package/pages/carpool/my/trip-records.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/Y2FycG9vbC1wYWNrYWdlXHBhZ2VzXGNhcnBvb2xcbXlcdHJpcC1yZWNvcmRzLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"trip-records-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-header\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"header-content\">\n        <view class=\"left-action\" @click=\"goBack\">\n          <image src=\"/static/images/tabbar/最新返回键.png\" class=\"action-icon back-icon\"></image>\n        </view>\n        <view class=\"title-area\">\n          <text class=\"page-title\">行程记录</text>\n        </view>\n        <view class=\"right-action\">\n          <!-- 预留位置 -->\n        </view>\n      </view>\n    </view>\n    \n    <!-- 切换标签 -->\n    <view class=\"tab-container\" :style=\"{ marginTop: (statusBarHeight + 44) + 'px' }\">\n      <view class=\"tab-item\" \n        v-for=\"(tab, index) in tabs\" \n        :key=\"index\" \n        :class=\"{ active: currentTab === tab.value }\"\n        @click=\"switchTab(tab.value)\">\n        <text class=\"tab-text\">{{tab.label}}</text>\n      </view>\n    </view>\n    \n    <!-- 行程列表 -->\n    <scroll-view class=\"trip-list-scroll\" scroll-y @scrolltolower=\"loadMore\" refresher-enabled @refresherrefresh=\"onRefresh\" :refresher-triggered=\"isRefreshing\">\n      <view class=\"trip-list\" v-if=\"tripList.length > 0\">\n        <view class=\"trip-item\" v-for=\"(item, index) in tripList\" :key=\"item.id\">\n          <view class=\"trip-header\">\n            <view class=\"trip-type\" :class=\"{'driver': item.role === 'driver', 'passenger': item.role === 'passenger'}\">\n              <text class=\"type-text\">{{item.role === 'driver' ? '我是车主' : '我是乘客'}}</text>\n            </view>\n            <view class=\"trip-status\" :class=\"item.status\">\n              <text class=\"status-text\">{{getStatusText(item.status)}}</text>\n            </view>\n          </view>\n          \n          <view class=\"trip-route\">\n            <view class=\"route-point start\">\n              <view class=\"point-icon start\"></view>\n              <text class=\"point-name\">{{item.startPoint}}</text>\n            </view>\n            <view class=\"route-line\"></view>\n            <view class=\"route-point end\">\n              <view class=\"point-icon end\"></view>\n              <text class=\"point-name\">{{item.endPoint}}</text>\n            </view>\n          </view>\n          \n          <view class=\"trip-info\">\n            <view class=\"info-row\">\n              <text class=\"info-label\">出发时间</text>\n              <text class=\"info-value\">{{item.departureTime}}</text>\n            </view>\n            <view class=\"info-row\">\n              <text class=\"info-label\">{{item.role === 'driver' ? '乘客数' : '同行人数'}}</text>\n              <text class=\"info-value\">{{item.passengerCount}}人</text>\n            </view>\n            <view class=\"info-row\">\n              <text class=\"info-label\">费用</text>\n              <text class=\"info-value price\">¥{{item.price.toFixed(2)}}</text>\n            </view>\n          </view>\n          \n          <view class=\"trip-actions\">\n            <button class=\"action-btn detail\" @click=\"viewTripDetail(item)\">查看详情</button>\n            <button class=\"action-btn contact\" v-if=\"item.status === 'ongoing'\" @click=\"contactDriver(item)\">联系{{item.role === 'driver' ? '乘客' : '车主'}}</button>\n            <button class=\"action-btn rate\" v-if=\"item.status === 'completed' && !item.isRated\" @click=\"rateTrip(item)\">评价</button>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 无数据提示 -->\n      <view class=\"empty-state\" v-if=\"tripList.length === 0 && !isLoading\">\n        <image src=\"/static/images/empty/no-trips.png\" mode=\"aspectFit\" class=\"empty-image\"></image>\n        <text class=\"empty-text\">暂无行程记录</text>\n      </view>\n      \n      <!-- 加载状态 -->\n      <view class=\"loading-state\" v-if=\"isLoading && !isRefreshing\">\n        <text class=\"loading-text\">加载中...</text>\n      </view>\n      \n      <!-- 到底提示 -->\n      <view class=\"list-bottom\" v-if=\"tripList.length > 0 && !hasMore\">\n        <text class=\"bottom-text\">— 已经到底啦 —</text>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\n\n// 状态栏高度\nconst statusBarHeight = ref(20)\n\n// 数据定义\nconst tabs = ref([\n  { label: '全部', value: 'all' },\n  { label: '进行中', value: 'ongoing' },\n  { label: '已完成', value: 'completed' },\n  { label: '已取消', value: 'canceled' }\n])\nconst currentTab = ref('all')\nconst tripList = ref([])\nconst page = ref(1)\nconst pageSize = ref(10)\nconst hasMore = ref(true)\nconst isLoading = ref(false)\nconst isRefreshing = ref(false)\n\n// 生命周期钩子\nonMounted(() => {\n  // 获取状态栏高度\n  const systemInfo = uni.getSystemInfoSync()\n  statusBarHeight.value = systemInfo.statusBarHeight || 20\n  \n  loadTrips()\n})\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack()\n}\n\n// 切换标签\nconst switchTab = (tab) => {\n  if (currentTab.value === tab) return\n  currentTab.value = tab\n  page.value = 1\n  tripList.value = []\n  hasMore.value = true\n  loadTrips()\n}\n\n// 加载行程数据\nconst loadTrips = () => {\n  if (isLoading.value) return\n  isLoading.value = true\n  \n  // 模拟数据加载\n  setTimeout(() => {\n    // 模拟行程数据\n    const mockTrips = [\n      {\n        id: '1001',\n        role: 'driver',\n        status: 'completed',\n        startPoint: '磁县政府',\n        endPoint: '邯郸火车站',\n        departureTime: '2023-11-15 10:30',\n        passengerCount: 3,\n        price: 35.00,\n        isRated: true\n      },\n      {\n        id: '1002',\n        role: 'passenger',\n        status: 'ongoing',\n        startPoint: '磁县老城区',\n        endPoint: '邯郸科技学院',\n        departureTime: '2023-12-05 07:30',\n        passengerCount: 1,\n        price: 25.00,\n        isRated: false\n      },\n      {\n        id: '1003',\n        role: 'driver',\n        status: 'canceled',\n        startPoint: '磁县新城区',\n        endPoint: '邯郸东站',\n        departureTime: '2023-11-05 09:15',\n        passengerCount: 0,\n        price: 30.00,\n        isRated: false\n      }\n    ]\n    \n    // 根据当前标签筛选数据\n    let filteredTrips = mockTrips\n    if (currentTab.value !== 'all') {\n      filteredTrips = mockTrips.filter(item => item.status === currentTab.value)\n    }\n    \n    if (page.value === 1) {\n      tripList.value = filteredTrips\n    } else {\n      tripList.value = [...tripList.value, ...filteredTrips]\n    }\n    \n    // 模拟没有更多数据\n    if (page.value >= 2) {\n      hasMore.value = false\n    }\n    \n    isLoading.value = false\n    isRefreshing.value = false\n  }, 1000)\n}\n\n// 获取状态文本\nconst getStatusText = (status) => {\n  const statusMap = {\n    'ongoing': '进行中',\n    'completed': '已完成',\n    'canceled': '已取消'\n  }\n  return statusMap[status] || '未知状态'\n}\n\n// 加载更多\nconst loadMore = () => {\n  if (hasMore.value && !isLoading.value) {\n    page.value++\n    loadTrips()\n  }\n}\n\n// 下拉刷新\nconst onRefresh = () => {\n  isRefreshing.value = true\n  page.value = 1\n  hasMore.value = true\n  loadTrips()\n}\n\n// 查看行程详情\nconst viewTripDetail = (item) => {\n  uni.navigateTo({\n    url: `/carpool-package/pages/carpool/trip-detail/index?id=${item.id}`\n  })\n}\n\n// 联系车主/乘客\nconst contactDriver = (item) => {\n  uni.showModal({\n    title: `联系${item.role === 'driver' ? '乘客' : '车主'}`,\n    content: `是否拨打${item.role === 'driver' ? '乘客' : '车主'}电话？`,\n    success: (res) => {\n      if (res.confirm) {\n        uni.makePhoneCall({\n          phoneNumber: '13812345678',\n          fail: () => {\n            uni.showToast({\n              title: '拨打电话失败',\n              icon: 'none'\n            })\n          }\n        })\n      }\n    }\n  })\n}\n\n// 评价行程\nconst rateTrip = (item) => {\n  uni.navigateTo({\n    url: `/carpool-package/pages/carpool/rating/index?id=${item.id}&role=${item.role}`\n  })\n}\n\n// 暴露方法给外部访问\ndefineExpose({\n  loadTrips,\n  switchTab\n})\n</script>\n\n<style lang=\"scss\">\n.trip-records-container {\n  min-height: 100vh;\n  background-color: #F5F8FC;\n  position: relative;\n  padding-top: calc(90rpx + var(--status-bar-height, 40px));\n}\n\n/* 自定义导航栏 */\n.custom-header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background-color: #0A84FF;\n}\n\n.header-content {\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 12px;\n}\n\n.left-action, .right-action {\n  width: 44px;\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.action-icon {\n  width: 24px;\n  height: 24px;\n}\n\n.back-icon {\n  width: 24px;\n  height: 24px;\n  /* 图标是黑色的，需要转为白色 */\n  filter: brightness(0) invert(1);\n}\n\n.title-area {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.page-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #FFFFFF;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\n}\n\n/* 切换标签 */\n.tab-container {\n  display: flex;\n  background-color: #FFFFFF;\n  margin: 20rpx;\n  border-radius: 16rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n  overflow: hidden;\n}\n\n.tab-item {\n  flex: 1;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 80rpx;\n  position: relative;\n}\n\n.tab-item.active {\n  background-color: rgba(10, 132, 255, 0.1);\n}\n\n.tab-item.active .tab-text {\n  color: #0A84FF;\n  font-weight: 600;\n}\n\n.tab-item.active::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 40rpx;\n  height: 4rpx;\n  background-color: #0A84FF;\n}\n\n.tab-text {\n  font-size: 28rpx;\n  color: #666666;\n}\n\n/* 行程列表 */\n.trip-list-scroll {\n  height: calc(100vh - 90rpx - var(--status-bar-height, 40px) - 120rpx);\n}\n\n.trip-list {\n  padding: 0 20rpx;\n}\n\n.trip-item {\n  background-color: #FFFFFF;\n  border-radius: 16rpx;\n  padding: 24rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.trip-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.trip-type {\n  padding: 6rpx 16rpx;\n  border-radius: 20rpx;\n  background-color: #F2F2F7;\n}\n\n.trip-type.driver {\n  background-color: rgba(10, 132, 255, 0.1);\n}\n\n.trip-type.passenger {\n  background-color: rgba(52, 199, 89, 0.1);\n}\n\n.type-text {\n  font-size: 24rpx;\n  color: #666666;\n}\n\n.trip-type.driver .type-text {\n  color: #0A84FF;\n}\n\n.trip-type.passenger .type-text {\n  color: #34C759;\n}\n\n.trip-status {\n  padding: 6rpx 16rpx;\n  border-radius: 20rpx;\n}\n\n.trip-status.ongoing {\n  background-color: rgba(255, 159, 10, 0.1);\n}\n\n.trip-status.completed {\n  background-color: rgba(52, 199, 89, 0.1);\n}\n\n.trip-status.canceled {\n  background-color: rgba(142, 142, 147, 0.1);\n}\n\n.status-text {\n  font-size: 24rpx;\n}\n\n.trip-status.ongoing .status-text {\n  color: #FF9F0A;\n}\n\n.trip-status.completed .status-text {\n  color: #34C759;\n}\n\n.trip-status.canceled .status-text {\n  color: #8E8E93;\n}\n\n/* 行程路线 */\n.trip-route {\n  background-color: #F9F9F9;\n  padding: 20rpx;\n  border-radius: 12rpx;\n  margin-bottom: 20rpx;\n}\n\n.route-point {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.route-point.end {\n  margin-bottom: 0;\n}\n\n.point-icon {\n  width: 20rpx;\n  height: 20rpx;\n  border-radius: 50%;\n  margin-right: 16rpx;\n}\n\n.point-icon.start {\n  background-color: #34C759;\n}\n\n.point-icon.end {\n  background-color: #FF3B30;\n}\n\n.point-name {\n  font-size: 28rpx;\n  color: #333333;\n}\n\n.route-line {\n  width: 2rpx;\n  height: 30rpx;\n  background-color: #DDDDDD;\n  margin-left: 9rpx;\n  margin-bottom: 16rpx;\n}\n\n/* 行程信息 */\n.trip-info {\n  margin-bottom: 20rpx;\n}\n\n.info-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12rpx;\n}\n\n.info-row:last-child {\n  margin-bottom: 0;\n}\n\n.info-label {\n  font-size: 26rpx;\n  color: #666666;\n}\n\n.info-value {\n  font-size: 26rpx;\n  color: #333333;\n  font-weight: 500;\n}\n\n.info-value.price {\n  color: #FF3B30;\n}\n\n/* 行程操作 */\n.trip-actions {\n  display: flex;\n  justify-content: flex-end;\n  border-top: 1px solid #F2F2F7;\n  padding-top: 20rpx;\n}\n\n.action-btn {\n  font-size: 26rpx;\n  padding: 8rpx 24rpx;\n  border-radius: 20rpx;\n  margin-left: 16rpx;\n  line-height: 1.5;\n}\n\n.action-btn.detail {\n  background-color: rgba(142, 142, 147, 0.1);\n  color: #666666;\n}\n\n.action-btn.contact {\n  background-color: rgba(10, 132, 255, 0.1);\n  color: #0A84FF;\n}\n\n.action-btn.rate {\n  background-color: rgba(255, 159, 10, 0.1);\n  color: #FF9F0A;\n}\n\n/* 空状态 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 100rpx 0;\n}\n\n.empty-image {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 20rpx;\n}\n\n.empty-text {\n  font-size: 28rpx;\n  color: #8E8E93;\n}\n\n/* 加载状态 */\n.loading-state {\n  display: flex;\n  justify-content: center;\n  padding: 30rpx 0;\n}\n\n.loading-text {\n  font-size: 28rpx;\n  color: #8E8E93;\n}\n\n/* 到底提示 */\n.list-bottom {\n  text-align: center;\n  padding: 30rpx 0;\n}\n\n.bottom-text {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/carpool-package/pages/carpool/my/trip-records.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni", "MiniProgramPage"], "mappings": ";;;;;;AAmGA,UAAM,kBAAkBA,cAAG,IAAC,EAAE;AAG9B,UAAM,OAAOA,cAAAA,IAAI;AAAA,MACf,EAAE,OAAO,MAAM,OAAO,MAAO;AAAA,MAC7B,EAAE,OAAO,OAAO,OAAO,UAAW;AAAA,MAClC,EAAE,OAAO,OAAO,OAAO,YAAa;AAAA,MACpC,EAAE,OAAO,OAAO,OAAO,WAAY;AAAA,IACrC,CAAC;AACD,UAAM,aAAaA,cAAG,IAAC,KAAK;AAC5B,UAAM,WAAWA,cAAG,IAAC,EAAE;AACvB,UAAM,OAAOA,cAAG,IAAC,CAAC;AACDA,kBAAG,IAAC,EAAE;AACvB,UAAM,UAAUA,cAAG,IAAC,IAAI;AACxB,UAAM,YAAYA,cAAG,IAAC,KAAK;AAC3B,UAAM,eAAeA,cAAG,IAAC,KAAK;AAG9BC,kBAAAA,UAAU,MAAM;AAEd,YAAM,aAAaC,cAAG,MAAC,kBAAmB;AAC1C,sBAAgB,QAAQ,WAAW,mBAAmB;AAEtD,gBAAW;AAAA,IACb,CAAC;AAGD,UAAM,SAAS,MAAM;AACnBA,oBAAAA,MAAI,aAAc;AAAA,IACpB;AAGA,UAAM,YAAY,CAAC,QAAQ;AACzB,UAAI,WAAW,UAAU;AAAK;AAC9B,iBAAW,QAAQ;AACnB,WAAK,QAAQ;AACb,eAAS,QAAQ,CAAE;AACnB,cAAQ,QAAQ;AAChB,gBAAW;AAAA,IACb;AAGA,UAAM,YAAY,MAAM;AACtB,UAAI,UAAU;AAAO;AACrB,gBAAU,QAAQ;AAGlB,iBAAW,MAAM;AAEf,cAAM,YAAY;AAAA,UAChB;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,eAAe;AAAA,YACf,gBAAgB;AAAA,YAChB,OAAO;AAAA,YACP,SAAS;AAAA,UACV;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,eAAe;AAAA,YACf,gBAAgB;AAAA,YAChB,OAAO;AAAA,YACP,SAAS;AAAA,UACV;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,eAAe;AAAA,YACf,gBAAgB;AAAA,YAChB,OAAO;AAAA,YACP,SAAS;AAAA,UACV;AAAA,QACF;AAGD,YAAI,gBAAgB;AACpB,YAAI,WAAW,UAAU,OAAO;AAC9B,0BAAgB,UAAU,OAAO,UAAQ,KAAK,WAAW,WAAW,KAAK;AAAA,QAC1E;AAED,YAAI,KAAK,UAAU,GAAG;AACpB,mBAAS,QAAQ;AAAA,QACvB,OAAW;AACL,mBAAS,QAAQ,CAAC,GAAG,SAAS,OAAO,GAAG,aAAa;AAAA,QACtD;AAGD,YAAI,KAAK,SAAS,GAAG;AACnB,kBAAQ,QAAQ;AAAA,QACjB;AAED,kBAAU,QAAQ;AAClB,qBAAa,QAAQ;AAAA,MACtB,GAAE,GAAI;AAAA,IACT;AAGA,UAAM,gBAAgB,CAAC,WAAW;AAChC,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,aAAa;AAAA,QACb,YAAY;AAAA,MACb;AACD,aAAO,UAAU,MAAM,KAAK;AAAA,IAC9B;AAGA,UAAM,WAAW,MAAM;AACrB,UAAI,QAAQ,SAAS,CAAC,UAAU,OAAO;AACrC,aAAK;AACL,kBAAW;AAAA,MACZ;AAAA,IACH;AAGA,UAAM,YAAY,MAAM;AACtB,mBAAa,QAAQ;AACrB,WAAK,QAAQ;AACb,cAAQ,QAAQ;AAChB,gBAAW;AAAA,IACb;AAGA,UAAM,iBAAiB,CAAC,SAAS;AAC/BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,uDAAuD,KAAK,EAAE;AAAA,MACvE,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,CAAC,SAAS;AAC9BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,KAAK,KAAK,SAAS,WAAW,OAAO,IAAI;AAAA,QAChD,SAAS,OAAO,KAAK,SAAS,WAAW,OAAO,IAAI;AAAA,QACpD,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,cAAc;AAAA,cAChB,aAAa;AAAA,cACb,MAAM,MAAM;AACVA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACpB,CAAa;AAAA,cACF;AAAA,YACX,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,CAAC,SAAS;AACzBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,kDAAkD,KAAK,EAAE,SAAS,KAAK,IAAI;AAAA,MACpF,CAAG;AAAA,IACH;AAGA,aAAa;AAAA,MACX;AAAA,MACA;AAAA,IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9QD,GAAG,WAAWC,SAAe;"}