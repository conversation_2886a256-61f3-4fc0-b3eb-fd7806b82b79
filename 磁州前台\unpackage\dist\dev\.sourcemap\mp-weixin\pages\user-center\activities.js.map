{"version": 3, "file": "activities.js", "sources": ["pages/user-center/activities.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci1jZW50ZXIvYWN0aXZpdGllcy52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"activities-container\">\r\n    <cu-custom bgColor=\"bg-white\" :isBack=\"true\">\r\n      <template slot=\"backText\">返回</template>\r\n      <template slot=\"content\">我参与的活动</template>\r\n    </cu-custom>\r\n    \r\n    <view class=\"content\">\r\n      <!-- 活动类型选择 -->\r\n      <view class=\"tab-nav\">\r\n        <view \r\n          class=\"tab-item\" \r\n          v-for=\"(tab, index) in tabs\" \r\n          :key=\"index\"\r\n          :class=\"{active: currentTab === index}\"\r\n          @tap=\"switchTab(index)\"\r\n        >\r\n          <text>{{tab.name}}</text>\r\n          <view class=\"tab-badge\" v-if=\"tab.count > 0\">{{tab.count}}</view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 活动列表 -->\r\n      <swiper class=\"tab-content\" :current=\"currentTab\" @change=\"onSwiperChange\" :style=\"{height: swiperHeight + 'px'}\">\r\n        <!-- 全部活动 -->\r\n        <swiper-item>\r\n          <scroll-view scroll-y class=\"scroll-view\" @scrolltolower=\"loadMore(0)\">\r\n            <view class=\"activity-list\">\r\n              <view class=\"activity-item\" v-for=\"(item, index) in allActivities\" :key=\"index\" @tap=\"viewActivity(item)\">\r\n                <image class=\"activity-image\" :src=\"item.image\" mode=\"aspectFill\"></image>\r\n                <view class=\"activity-info\">\r\n                  <view class=\"activity-header\">\r\n                    <text class=\"activity-tag\" :class=\"'tag-' + item.type\">{{getTypeName(item.type)}}</text>\r\n                    <text class=\"activity-status\" :class=\"'status-' + item.status\">{{getStatusText(item.status)}}</text>\r\n                  </view>\r\n                  <text class=\"activity-title\">{{item.title}}</text>\r\n                  <view class=\"activity-shop\">\r\n                    <image class=\"shop-logo\" :src=\"item.shop.logo\" mode=\"aspectFill\"></image>\r\n                    <text class=\"shop-name\">{{item.shop.name}}</text>\r\n                  </view>\r\n                  <view class=\"activity-time\">\r\n                    <text>{{item.time}}</text>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n              \r\n              <!-- 加载更多 -->\r\n              <view class=\"load-more\" v-if=\"loadingStatus[0]\">\r\n                <text>加载中...</text>\r\n              </view>\r\n              \r\n              <!-- 没有更多数据 -->\r\n              <view class=\"no-more\" v-if=\"!hasMore[0] && allActivities.length > 0\">\r\n                <text>没有更多活动了</text>\r\n              </view>\r\n              \r\n              <!-- 空状态 -->\r\n              <view class=\"empty-state\" v-if=\"allActivities.length === 0\">\r\n                <image class=\"empty-image\" src=\"/static/images/empty-activity.png\" mode=\"aspectFit\"></image>\r\n                <text class=\"empty-text\">暂无参与的活动</text>\r\n              </view>\r\n            </view>\r\n          </scroll-view>\r\n        </swiper-item>\r\n        \r\n        <!-- 拼团活动 -->\r\n        <swiper-item>\r\n          <scroll-view scroll-y class=\"scroll-view\" @scrolltolower=\"loadMore(1)\">\r\n            <view class=\"activity-list\">\r\n              <view class=\"activity-item\" v-for=\"(item, index) in grouponActivities\" :key=\"index\" @tap=\"viewActivity(item)\">\r\n                <image class=\"activity-image\" :src=\"item.image\" mode=\"aspectFill\"></image>\r\n                <view class=\"activity-info\">\r\n                  <view class=\"activity-header\">\r\n                    <text class=\"activity-tag tag-groupon\">拼团</text>\r\n                    <text class=\"activity-status\" :class=\"'status-' + item.status\">{{getStatusText(item.status)}}</text>\r\n                  </view>\r\n                  <text class=\"activity-title\">{{item.title}}</text>\r\n                  <view class=\"activity-shop\">\r\n                    <image class=\"shop-logo\" :src=\"item.shop.logo\" mode=\"aspectFill\"></image>\r\n                    <text class=\"shop-name\">{{item.shop.name}}</text>\r\n                  </view>\r\n                  <view class=\"activity-progress\">\r\n                    <text class=\"progress-text\">{{item.currentCount}}/{{item.needCount}}人</text>\r\n                    <view class=\"progress-bar\">\r\n                      <view class=\"progress-inner\" :style=\"{width: (item.currentCount / item.needCount * 100) + '%'}\"></view>\r\n                    </view>\r\n                  </view>\r\n                  <view class=\"activity-time\">\r\n                    <text>{{item.time}}</text>\r\n                    <text class=\"countdown\" v-if=\"item.status === 'ongoing'\">剩余 {{item.leftTime}}</text>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n              \r\n              <!-- 加载更多 -->\r\n              <view class=\"load-more\" v-if=\"loadingStatus[1]\">\r\n                <text>加载中...</text>\r\n              </view>\r\n              \r\n              <!-- 没有更多数据 -->\r\n              <view class=\"no-more\" v-if=\"!hasMore[1] && grouponActivities.length > 0\">\r\n                <text>没有更多拼团了</text>\r\n              </view>\r\n              \r\n              <!-- 空状态 -->\r\n              <view class=\"empty-state\" v-if=\"grouponActivities.length === 0\">\r\n                <image class=\"empty-image\" src=\"/static/images/empty-groupon.png\" mode=\"aspectFit\"></image>\r\n                <text class=\"empty-text\">暂无参与的拼团</text>\r\n              </view>\r\n            </view>\r\n          </scroll-view>\r\n        </swiper-item>\r\n        \r\n        <!-- 红包活动 -->\r\n        <swiper-item>\r\n          <scroll-view scroll-y class=\"scroll-view\" @scrolltolower=\"loadMore(2)\">\r\n            <view class=\"activity-list\">\r\n              <view class=\"activity-item\" v-for=\"(item, index) in redpacketActivities\" :key=\"index\" @tap=\"viewActivity(item)\">\r\n                <image class=\"activity-image\" :src=\"item.image\" mode=\"aspectFill\"></image>\r\n                <view class=\"activity-info\">\r\n                  <view class=\"activity-header\">\r\n                    <text class=\"activity-tag tag-redpacket\">红包</text>\r\n                    <text class=\"activity-status\" :class=\"'status-' + item.status\">{{getStatusText(item.status)}}</text>\r\n                  </view>\r\n                  <text class=\"activity-title\">{{item.title}}</text>\r\n                  <view class=\"activity-shop\">\r\n                    <image class=\"shop-logo\" :src=\"item.shop.logo\" mode=\"aspectFill\"></image>\r\n                    <text class=\"shop-name\">{{item.shop.name}}</text>\r\n                  </view>\r\n                  <view class=\"redpacket-amount\">\r\n                    <text class=\"amount-value\">¥{{item.amount}}</text>\r\n                    <text class=\"amount-desc\">{{item.desc}}</text>\r\n                  </view>\r\n                  <view class=\"activity-time\">\r\n                    <text>{{item.time}}</text>\r\n                    <text class=\"validity\">有效期至 {{item.validTime}}</text>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n              \r\n              <!-- 加载更多 -->\r\n              <view class=\"load-more\" v-if=\"loadingStatus[2]\">\r\n                <text>加载中...</text>\r\n              </view>\r\n              \r\n              <!-- 没有更多数据 -->\r\n              <view class=\"no-more\" v-if=\"!hasMore[2] && redpacketActivities.length > 0\">\r\n                <text>没有更多红包了</text>\r\n              </view>\r\n              \r\n              <!-- 空状态 -->\r\n              <view class=\"empty-state\" v-if=\"redpacketActivities.length === 0\">\r\n                <image class=\"empty-image\" src=\"/static/images/empty-redpacket.png\" mode=\"aspectFit\"></image>\r\n                <text class=\"empty-text\">暂无参与的红包活动</text>\r\n              </view>\r\n            </view>\r\n          </scroll-view>\r\n        </swiper-item>\r\n      </swiper>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      currentTab: 0,\r\n      swiperHeight: 500,\r\n      tabs: [\r\n        { name: '全部活动', count: 0 },\r\n        { name: '我的拼团', count: 0 },\r\n        { name: '我的红包', count: 0 }\r\n      ],\r\n      allActivities: [],\r\n      grouponActivities: [],\r\n      redpacketActivities: [],\r\n      hasMore: [true, true, true],\r\n      loadingStatus: [false, false, false],\r\n      page: [1, 1, 1]\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    // 根据URL参数切换标签页\r\n    if (options && options.tab) {\r\n      this.currentTab = parseInt(options.tab)\r\n    }\r\n    \r\n    this.loadAllActivities()\r\n    this.loadGrouponActivities()\r\n    this.loadRedpacketActivities()\r\n    this.calcSwiperHeight()\r\n  },\r\n  onReady() {\r\n    this.calcSwiperHeight()\r\n  },\r\n  methods: {\r\n    // 计算swiper高度\r\n    calcSwiperHeight() {\r\n      const query = uni.createSelectorQuery().in(this)\r\n      query.select('.content').boundingClientRect(data => {\r\n        const windowHeight = uni.getSystemInfoSync().windowHeight\r\n        const navHeight = uni.getSystemInfoSync().statusBarHeight + 44 // 导航栏高度\r\n        const tabHeight = 50 // tab栏高度\r\n        this.swiperHeight = windowHeight - navHeight - tabHeight - 20 // 20是内边距\r\n      }).exec()\r\n    },\r\n    \r\n    // 切换标签\r\n    switchTab(index) {\r\n      this.currentTab = index\r\n    },\r\n    \r\n    // 滑动切换\r\n    onSwiperChange(e) {\r\n      this.currentTab = e.detail.current\r\n    },\r\n    \r\n    // 加载全部活动\r\n    loadAllActivities() {\r\n      if (!this.hasMore[0] || this.loadingStatus[0]) return\r\n      \r\n      this.loadingStatus[0] = true\r\n      \r\n      // 模拟API请求\r\n      setTimeout(() => {\r\n        const activities = [\r\n          {\r\n            id: 1,\r\n            type: 'groupon',\r\n            title: '新鲜水果大礼包限时拼团',\r\n            image: '/static/images/activity-1.jpg',\r\n            status: 'ongoing',\r\n            time: '2023-05-03 14:23',\r\n            shop: {\r\n              id: 101,\r\n              name: '鲜果日记',\r\n              logo: '/static/images/shop-1.jpg'\r\n            }\r\n          },\r\n          {\r\n            id: 2,\r\n            type: 'redpacket',\r\n            title: '新店开业红包大派送',\r\n            image: '/static/images/activity-2.jpg',\r\n            status: 'used',\r\n            time: '2023-05-02 10:15',\r\n            shop: {\r\n              id: 102,\r\n              name: '时尚精品店',\r\n              logo: '/static/images/shop-2.jpg'\r\n            }\r\n          },\r\n          {\r\n            id: 3,\r\n            type: 'coupon',\r\n            title: '满100减30优惠券',\r\n            image: '/static/images/activity-4.jpg',\r\n            status: 'unused',\r\n            time: '2023-05-01 09:45',\r\n            shop: {\r\n              id: 104,\r\n              name: '日用百货',\r\n              logo: '/static/images/shop-4.jpg'\r\n            }\r\n          }\r\n        ]\r\n        \r\n        this.allActivities = [...this.allActivities, ...activities]\r\n        this.hasMore[0] = this.page[0] < 2 // 模拟只有2页数据\r\n        this.loadingStatus[0] = false\r\n        this.page[0]++\r\n        \r\n        // 更新tab计数\r\n        this.tabs[0].count = this.allActivities.length\r\n      }, 500)\r\n    },\r\n    \r\n    // 加载拼团活动\r\n    loadGrouponActivities() {\r\n      if (!this.hasMore[1] || this.loadingStatus[1]) return\r\n      \r\n      this.loadingStatus[1] = true\r\n      \r\n      // 模拟API请求\r\n      setTimeout(() => {\r\n        const activities = [\r\n          {\r\n            id: 1,\r\n            title: '新鲜水果大礼包限时拼团',\r\n            image: '/static/images/activity-1.jpg',\r\n            status: 'ongoing',\r\n            currentCount: 2,\r\n            needCount: 3,\r\n            leftTime: '11:24:36',\r\n            time: '2023-05-03 14:23',\r\n            shop: {\r\n              id: 101,\r\n              name: '鲜果日记',\r\n              logo: '/static/images/shop-1.jpg'\r\n            }\r\n          },\r\n          {\r\n            id: 5,\r\n            title: '美食团购大优惠',\r\n            image: '/static/images/activity-5.jpg',\r\n            status: 'success',\r\n            currentCount: 3,\r\n            needCount: 3,\r\n            leftTime: '00:00:00',\r\n            time: '2023-04-28 19:45',\r\n            shop: {\r\n              id: 105,\r\n              name: '美食广场',\r\n              logo: '/static/images/shop-5.jpg'\r\n            }\r\n          }\r\n        ]\r\n        \r\n        this.grouponActivities = [...this.grouponActivities, ...activities]\r\n        this.hasMore[1] = this.page[1] < 2 // 模拟只有2页数据\r\n        this.loadingStatus[1] = false\r\n        this.page[1]++\r\n        \r\n        // 更新tab计数\r\n        this.tabs[1].count = this.grouponActivities.length\r\n      }, 500)\r\n    },\r\n    \r\n    // 加载红包活动\r\n    loadRedpacketActivities() {\r\n      if (!this.hasMore[2] || this.loadingStatus[2]) return\r\n      \r\n      this.loadingStatus[2] = true\r\n      \r\n      // 模拟API请求\r\n      setTimeout(() => {\r\n        const activities = [\r\n          {\r\n            id: 2,\r\n            title: '新店开业红包大派送',\r\n            image: '/static/images/activity-2.jpg',\r\n            status: 'used',\r\n            amount: '15.00',\r\n            desc: '已使用',\r\n            time: '2023-05-02 10:15',\r\n            validTime: '2023-05-10',\r\n            shop: {\r\n              id: 102,\r\n              name: '时尚精品店',\r\n              logo: '/static/images/shop-2.jpg'\r\n            }\r\n          },\r\n          {\r\n            id: 6,\r\n            title: '周末红包雨',\r\n            image: '/static/images/activity-6.jpg',\r\n            status: 'unused',\r\n            amount: '10.00',\r\n            desc: '满100可用',\r\n            time: '2023-04-30 16:28',\r\n            validTime: '2023-05-15',\r\n            shop: {\r\n              id: 106,\r\n              name: '数码专营店',\r\n              logo: '/static/images/shop-6.jpg'\r\n            }\r\n          },\r\n          {\r\n            id: 8,\r\n            title: '五一假期红包',\r\n            image: '/static/images/activity-8.jpg',\r\n            status: 'expired',\r\n            amount: '20.00',\r\n            desc: '已过期',\r\n            time: '2023-04-25 09:10',\r\n            validTime: '2023-05-01',\r\n            shop: {\r\n              id: 108,\r\n              name: '生活超市',\r\n              logo: '/static/images/shop-8.jpg'\r\n            }\r\n          }\r\n        ]\r\n        \r\n        this.redpacketActivities = [...this.redpacketActivities, ...activities]\r\n        this.hasMore[2] = this.page[2] < 2 // 模拟只有2页数据\r\n        this.loadingStatus[2] = false\r\n        this.page[2]++\r\n        \r\n        // 更新tab计数\r\n        this.tabs[2].count = this.redpacketActivities.length\r\n      }, 500)\r\n    },\r\n    \r\n    // 加载更多\r\n    loadMore(tabIndex) {\r\n      switch(tabIndex) {\r\n        case 0:\r\n          this.loadAllActivities()\r\n          break\r\n        case 1:\r\n          this.loadGrouponActivities()\r\n          break\r\n        case 2:\r\n          this.loadRedpacketActivities()\r\n          break\r\n      }\r\n    },\r\n    \r\n    // 查看活动详情\r\n    viewActivity(item) {\r\n      let url = ''\r\n      \r\n      if (item.type === 'groupon' || this.currentTab === 1) {\r\n        url = `/pages/user-center/groupon-detail?id=${item.id}`\r\n      } else if (item.type === 'redpacket' || this.currentTab === 2) {\r\n        url = `/pages/user-center/redpacket-detail?id=${item.id}`\r\n      } else if (item.type === 'coupon') {\r\n        url = `/pages/user-center/coupon-detail?id=${item.id}`\r\n      } else {\r\n        url = `/pages/activity/detail?id=${item.id}`\r\n      }\r\n      \r\n      uni.navigateTo({ url })\r\n    },\r\n    \r\n    // 获取活动类型名称\r\n    getTypeName(type) {\r\n      const typeMap = {\r\n        'groupon': '拼团',\r\n        'redpacket': '红包',\r\n        'seckill': '秒杀',\r\n        'coupon': '优惠券'\r\n      }\r\n      return typeMap[type] || '活动'\r\n    },\r\n    \r\n    // 获取活动状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        'ongoing': '进行中',\r\n        'success': '已成功',\r\n        'failed': '已失败',\r\n        'used': '已使用',\r\n        'unused': '未使用',\r\n        'expired': '已过期'\r\n      }\r\n      return statusMap[status] || '未知'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.activities-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F5F7;\r\n}\r\n\r\n.content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: calc(100vh - 44px); /* 减去导航栏高度 */\r\n}\r\n\r\n/* 标签导航 */\r\n.tab-nav {\r\n  display: flex;\r\n  background-color: #FFFFFF;\r\n  height: 90rpx;\r\n  border-bottom: 1rpx solid #F2F2F7;\r\n}\r\n\r\n.tab-item {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  position: relative;\r\n  \r\n  &.active {\r\n    color: #007AFF;\r\n    font-weight: 500;\r\n    \r\n    &::after {\r\n      content: '';\r\n      position: absolute;\r\n      bottom: 0;\r\n      left: 50%;\r\n      transform: translateX(-50%);\r\n      width: 40rpx;\r\n      height: 4rpx;\r\n      background-color: #007AFF;\r\n      border-radius: 2rpx;\r\n    }\r\n  }\r\n  \r\n  .tab-badge {\r\n    min-width: 32rpx;\r\n    height: 32rpx;\r\n    padding: 0 6rpx;\r\n    background-color: #FF3B30;\r\n    color: #FFFFFF;\r\n    font-size: 20rpx;\r\n    border-radius: 16rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-left: 8rpx;\r\n  }\r\n}\r\n\r\n/* 标签内容 */\r\n.tab-content {\r\n  flex: 1;\r\n}\r\n\r\n.scroll-view {\r\n  height: 100%;\r\n}\r\n\r\n/* 活动列表 */\r\n.activity-list {\r\n  padding: 24rpx;\r\n  \r\n  .activity-item {\r\n    background-color: #FFFFFF;\r\n    border-radius: 16rpx;\r\n    margin-bottom: 24rpx;\r\n    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);\r\n    overflow: hidden;\r\n  }\r\n  \r\n  .activity-image {\r\n    width: 100%;\r\n    height: 240rpx;\r\n  }\r\n  \r\n  .activity-info {\r\n    padding: 20rpx;\r\n  }\r\n  \r\n  .activity-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    margin-bottom: 16rpx;\r\n  }\r\n  \r\n  .activity-tag {\r\n    font-size: 24rpx;\r\n    padding: 4rpx 16rpx;\r\n    border-radius: 20rpx;\r\n    \r\n    &.tag-groupon {\r\n      background-color: #FFF3E0;\r\n      color: #FF9500;\r\n    }\r\n    \r\n    &.tag-redpacket {\r\n      background-color: #FFEBEE;\r\n      color: #FF3B30;\r\n    }\r\n    \r\n    &.tag-seckill {\r\n      background-color: #E8F5E9;\r\n      color: #4CD964;\r\n    }\r\n    \r\n    &.tag-coupon {\r\n      background-color: #E3F2FD;\r\n      color: #007AFF;\r\n    }\r\n  }\r\n  \r\n  .activity-status {\r\n    font-size: 24rpx;\r\n    \r\n    &.status-ongoing {\r\n      color: #4CD964;\r\n    }\r\n    \r\n    &.status-success {\r\n      color: #007AFF;\r\n    }\r\n    \r\n    &.status-failed {\r\n      color: #8E8E93;\r\n    }\r\n    \r\n    &.status-used {\r\n      color: #8E8E93;\r\n    }\r\n    \r\n    &.status-unused {\r\n      color: #FF9500;\r\n    }\r\n    \r\n    &.status-expired {\r\n      color: #8E8E93;\r\n    }\r\n  }\r\n  \r\n  .activity-title {\r\n    font-size: 32rpx;\r\n    font-weight: 600;\r\n    color: #333;\r\n    margin-bottom: 16rpx;\r\n    display: block;\r\n  }\r\n  \r\n  .activity-shop {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 16rpx;\r\n    \r\n    .shop-logo {\r\n      width: 40rpx;\r\n      height: 40rpx;\r\n      border-radius: 20rpx;\r\n      margin-right: 12rpx;\r\n    }\r\n    \r\n    .shop-name {\r\n      font-size: 26rpx;\r\n      color: #666;\r\n    }\r\n  }\r\n  \r\n  .activity-time {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    font-size: 24rpx;\r\n    color: #8E8E93;\r\n  }\r\n  \r\n  .activity-progress {\r\n    margin-bottom: 16rpx;\r\n    \r\n    .progress-text {\r\n      font-size: 24rpx;\r\n      color: #666;\r\n      margin-bottom: 8rpx;\r\n      display: block;\r\n    }\r\n    \r\n    .progress-bar {\r\n      height: 16rpx;\r\n      background-color: #F2F2F7;\r\n      border-radius: 8rpx;\r\n      overflow: hidden;\r\n    }\r\n    \r\n    .progress-inner {\r\n      height: 100%;\r\n      background-color: #FF9500;\r\n      border-radius: 8rpx;\r\n    }\r\n  }\r\n  \r\n  .countdown {\r\n    color: #FF3B30;\r\n  }\r\n  \r\n  .redpacket-amount {\r\n    display: flex;\r\n    align-items: baseline;\r\n    margin-bottom: 16rpx;\r\n    \r\n    .amount-value {\r\n      font-size: 32rpx;\r\n      color: #FF3B30;\r\n      font-weight: 600;\r\n      margin-right: 12rpx;\r\n    }\r\n    \r\n    .amount-desc {\r\n      font-size: 24rpx;\r\n      color: #8E8E93;\r\n    }\r\n  }\r\n  \r\n  .validity {\r\n    color: #FF9500;\r\n  }\r\n}\r\n\r\n/* 加载更多 */\r\n.load-more, .no-more {\r\n  text-align: center;\r\n  padding: 30rpx 0;\r\n  font-size: 26rpx;\r\n  color: #8E8E93;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 100rpx 0;\r\n  \r\n  .empty-image {\r\n    width: 200rpx;\r\n    height: 200rpx;\r\n    margin-bottom: 30rpx;\r\n  }\r\n  \r\n  .empty-text {\r\n    font-size: 28rpx;\r\n    color: #8E8E93;\r\n  }\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/user-center/activities.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAoKA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,MAAM;AAAA,QACJ,EAAE,MAAM,QAAQ,OAAO,EAAG;AAAA,QAC1B,EAAE,MAAM,QAAQ,OAAO,EAAG;AAAA,QAC1B,EAAE,MAAM,QAAQ,OAAO,EAAE;AAAA,MAC1B;AAAA,MACD,eAAe,CAAE;AAAA,MACjB,mBAAmB,CAAE;AAAA,MACrB,qBAAqB,CAAE;AAAA,MACvB,SAAS,CAAC,MAAM,MAAM,IAAI;AAAA,MAC1B,eAAe,CAAC,OAAO,OAAO,KAAK;AAAA,MACnC,MAAM,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB;AAAA,EACD;AAAA,EACD,OAAO,SAAS;AAEd,QAAI,WAAW,QAAQ,KAAK;AAC1B,WAAK,aAAa,SAAS,QAAQ,GAAG;AAAA,IACxC;AAEA,SAAK,kBAAkB;AACvB,SAAK,sBAAsB;AAC3B,SAAK,wBAAwB;AAC7B,SAAK,iBAAiB;AAAA,EACvB;AAAA,EACD,UAAU;AACR,SAAK,iBAAiB;AAAA,EACvB;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,mBAAmB;AACjB,YAAM,QAAQA,cAAG,MAAC,oBAAmB,EAAG,GAAG,IAAI;AAC/C,YAAM,OAAO,UAAU,EAAE,mBAAmB,UAAQ;AAClD,cAAM,eAAeA,cAAAA,MAAI,kBAAiB,EAAG;AAC7C,cAAM,YAAYA,cAAG,MAAC,kBAAmB,EAAC,kBAAkB;AAC5D,cAAM,YAAY;AAClB,aAAK,eAAe,eAAe,YAAY,YAAY;AAAA,MAC5D,CAAA,EAAE,KAAK;AAAA,IACT;AAAA;AAAA,IAGD,UAAU,OAAO;AACf,WAAK,aAAa;AAAA,IACnB;AAAA;AAAA,IAGD,eAAe,GAAG;AAChB,WAAK,aAAa,EAAE,OAAO;AAAA,IAC5B;AAAA;AAAA,IAGD,oBAAoB;AAClB,UAAI,CAAC,KAAK,QAAQ,CAAC,KAAK,KAAK,cAAc,CAAC;AAAG;AAE/C,WAAK,cAAc,CAAC,IAAI;AAGxB,iBAAW,MAAM;AACf,cAAM,aAAa;AAAA,UACjB;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,OAAO;AAAA,YACP,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,MAAM;AAAA,YACN,MAAM;AAAA,cACJ,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,MAAM;AAAA,YACR;AAAA,UACD;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,OAAO;AAAA,YACP,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,MAAM;AAAA,YACN,MAAM;AAAA,cACJ,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,MAAM;AAAA,YACR;AAAA,UACD;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,OAAO;AAAA,YACP,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,MAAM;AAAA,YACN,MAAM;AAAA,cACJ,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAEA,aAAK,gBAAgB,CAAC,GAAG,KAAK,eAAe,GAAG,UAAU;AAC1D,aAAK,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI;AACjC,aAAK,cAAc,CAAC,IAAI;AACxB,aAAK,KAAK,CAAC;AAGX,aAAK,KAAK,CAAC,EAAE,QAAQ,KAAK,cAAc;AAAA,MACzC,GAAE,GAAG;AAAA,IACP;AAAA;AAAA,IAGD,wBAAwB;AACtB,UAAI,CAAC,KAAK,QAAQ,CAAC,KAAK,KAAK,cAAc,CAAC;AAAG;AAE/C,WAAK,cAAc,CAAC,IAAI;AAGxB,iBAAW,MAAM;AACf,cAAM,aAAa;AAAA,UACjB;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,cAAc;AAAA,YACd,WAAW;AAAA,YACX,UAAU;AAAA,YACV,MAAM;AAAA,YACN,MAAM;AAAA,cACJ,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,MAAM;AAAA,YACR;AAAA,UACD;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,cAAc;AAAA,YACd,WAAW;AAAA,YACX,UAAU;AAAA,YACV,MAAM;AAAA,YACN,MAAM;AAAA,cACJ,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAEA,aAAK,oBAAoB,CAAC,GAAG,KAAK,mBAAmB,GAAG,UAAU;AAClE,aAAK,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI;AACjC,aAAK,cAAc,CAAC,IAAI;AACxB,aAAK,KAAK,CAAC;AAGX,aAAK,KAAK,CAAC,EAAE,QAAQ,KAAK,kBAAkB;AAAA,MAC7C,GAAE,GAAG;AAAA,IACP;AAAA;AAAA,IAGD,0BAA0B;AACxB,UAAI,CAAC,KAAK,QAAQ,CAAC,KAAK,KAAK,cAAc,CAAC;AAAG;AAE/C,WAAK,cAAc,CAAC,IAAI;AAGxB,iBAAW,MAAM;AACf,cAAM,aAAa;AAAA,UACjB;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,MAAM;AAAA,YACN,MAAM;AAAA,YACN,WAAW;AAAA,YACX,MAAM;AAAA,cACJ,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,MAAM;AAAA,YACR;AAAA,UACD;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,MAAM;AAAA,YACN,MAAM;AAAA,YACN,WAAW;AAAA,YACX,MAAM;AAAA,cACJ,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,MAAM;AAAA,YACR;AAAA,UACD;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,MAAM;AAAA,YACN,MAAM;AAAA,YACN,WAAW;AAAA,YACX,MAAM;AAAA,cACJ,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAEA,aAAK,sBAAsB,CAAC,GAAG,KAAK,qBAAqB,GAAG,UAAU;AACtE,aAAK,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI;AACjC,aAAK,cAAc,CAAC,IAAI;AACxB,aAAK,KAAK,CAAC;AAGX,aAAK,KAAK,CAAC,EAAE,QAAQ,KAAK,oBAAoB;AAAA,MAC/C,GAAE,GAAG;AAAA,IACP;AAAA;AAAA,IAGD,SAAS,UAAU;AACjB,cAAO,UAAQ;AAAA,QACb,KAAK;AACH,eAAK,kBAAkB;AACvB;AAAA,QACF,KAAK;AACH,eAAK,sBAAsB;AAC3B;AAAA,QACF,KAAK;AACH,eAAK,wBAAwB;AAC7B;AAAA,MACJ;AAAA,IACD;AAAA;AAAA,IAGD,aAAa,MAAM;AACjB,UAAI,MAAM;AAEV,UAAI,KAAK,SAAS,aAAa,KAAK,eAAe,GAAG;AACpD,cAAM,wCAAwC,KAAK,EAAE;AAAA,iBAC5C,KAAK,SAAS,eAAe,KAAK,eAAe,GAAG;AAC7D,cAAM,0CAA0C,KAAK,EAAE;AAAA,iBAC9C,KAAK,SAAS,UAAU;AACjC,cAAM,uCAAuC,KAAK,EAAE;AAAA,aAC/C;AACL,cAAM,6BAA6B,KAAK,EAAE;AAAA,MAC5C;AAEAA,0BAAI,WAAW,EAAE,KAAK;AAAA,IACvB;AAAA;AAAA,IAGD,YAAY,MAAM;AAChB,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,aAAa;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AACA,aAAO,QAAQ,IAAI,KAAK;AAAA,IACzB;AAAA;AAAA,IAGD,cAAc,QAAQ;AACpB,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,WAAW;AAAA,MACb;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC9B;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClcA,GAAG,WAAW,eAAe;"}