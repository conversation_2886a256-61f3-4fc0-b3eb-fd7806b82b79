<template>
  <view class="order-confirm-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M19 12H5M12 19l-7-7 7-7" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        <view class="navbar-title">订单确认</view>
        <view class="navbar-right"></view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view 
      class="content-scroll" 
      scroll-y
    >
      <!-- 收货地址 -->
      <view class="address-section" @click="selectAddress">
        <view class="address-content" v-if="address">
          <view class="address-info">
            <view class="user-info">
              <text class="user-name">{{ address.name }}</text>
              <text class="user-phone">{{ address.phone }}</text>
            </view>
            <view class="address-detail">{{ address.province }} {{ address.city }} {{ address.district }} {{ address.detail }}</view>
          </view>
          <svg class="arrow-icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M9 18l6-6-6-6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        <view class="address-empty" v-else>
          <view class="add-address">
            <text>添加收货地址</text>
            <svg class="arrow-icon" viewBox="0 0 24 24" width="24" height="24">
              <path d="M9 18l6-6-6-6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
        
        <view class="address-divider">
          <image class="divider-image" src="/static/images/address-divider.png" mode="scaleToFill"></image>
        </view>
      </view>
      
      <!-- 商品列表 -->
      <view class="products-section">
        <view class="shop-group" v-for="(shop, shopIndex) in orderShops" :key="shopIndex">
          <view class="shop-header">
            <image class="shop-logo" :src="shop.logo" mode="aspectFill"></image>
            <view class="shop-name">{{ shop.name }}</view>
          </view>
          
          <view class="product-list">
            <view class="product-item" v-for="(item, itemIndex) in shop.items" :key="itemIndex">
              <image class="product-image" :src="item.image" mode="aspectFill"></image>
              <view class="product-info">
                <view class="product-name">{{ item.name }}</view>
                <view class="product-specs">{{ item.specs }}</view>
                <view class="product-price-row">
                  <view class="product-price">¥{{ item.price.toFixed(2) }}</view>
                  <view class="product-quantity">x{{ item.quantity }}</view>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 配送方式 -->
          <view class="delivery-section">
            <view class="section-title">配送方式</view>
            <view class="delivery-options">
              <view 
                class="delivery-option" 
                v-for="(option, index) in deliveryOptions" 
                :key="index"
                :class="{ active: shop.deliveryType === option.type }"
                @click="selectDelivery(shop, option.type)"
              >
                {{ option.name }}
              </view>
            </view>
          </view>
          
          <!-- 订单备注 -->
          <view class="remark-section">
            <view class="section-title">订单备注</view>
            <input 
              class="remark-input" 
              type="text" 
              placeholder="选填，请先和商家协商一致" 
              v-model="shop.remark"
              maxlength="50"
            />
          </view>
          
          <!-- 优惠券 -->
          <view class="coupon-section" @click="selectCoupon(shop)">
            <view class="section-title">优惠券</view>
            <view class="coupon-info">
              <text>{{ shop.coupon ? `${shop.coupon.name}` : '暂无可用优惠券' }}</text>
              <svg class="arrow-icon" viewBox="0 0 24 24" width="16" height="16">
                <path d="M9 18l6-6-6-6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
            </view>
          </view>
          
          <!-- 店铺小计 -->
          <view class="shop-subtotal">
            <text>共{{ getTotalQuantity(shop) }}件商品</text>
            <text>小计：¥{{ getShopTotal(shop).toFixed(2) }}</text>
          </view>
        </view>
      </view>
      
      <!-- 支付方式 -->
      <view class="payment-section">
        <view class="section-title">支付方式</view>
        <view class="payment-options">
          <view 
            class="payment-option" 
            v-for="(option, index) in paymentOptions" 
            :key="index"
            :class="{ active: paymentType === option.type }"
            @click="selectPayment(option.type)"
          >
            <image class="payment-icon" :src="option.icon" mode="aspectFit"></image>
            <text>{{ option.name }}</text>
            <view class="checkbox" v-if="paymentType === option.type">
              <svg class="check-icon" viewBox="0 0 24 24" width="16" height="16">
                <path d="M20 6L9 17l-5-5" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 订单金额 -->
      <view class="amount-section">
        <view class="amount-row">
          <text>商品金额</text>
          <text>¥{{ getProductTotal().toFixed(2) }}</text>
        </view>
        <view class="amount-row">
          <text>运费</text>
          <text>¥{{ getDeliveryFee().toFixed(2) }}</text>
        </view>
        <view class="amount-row">
          <text>优惠金额</text>
          <text>-¥{{ getDiscountAmount().toFixed(2) }}</text>
        </view>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
    
    <!-- 底部结算栏 -->
    <view class="checkout-bar">
      <view class="total-amount">
        <text>实付款：</text>
        <text class="price">¥{{ getTotalAmount().toFixed(2) }}</text>
      </view>
      <view class="submit-btn" @click="submitOrder">提交订单</view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 收货地址
const address = ref({
  id: '1',
  name: '张三',
  phone: '138****1234',
  province: '北京市',
  city: '北京市',
  district: '朝阳区',
  detail: '三里屯SOHO 5号楼2单元801'
});

// 订单商品（按店铺分组）
const orderShops = ref([]);

// 支付方式
const paymentType = ref('wechat');

// 支付方式选项
const paymentOptions = ref([
  {
    type: 'wechat',
    name: '微信支付',
    icon: '/static/images/payment/wechat.png'
  },
  {
    type: 'alipay',
    name: '支付宝',
    icon: '/static/images/payment/alipay.png'
  },
  {
    type: 'balance',
    name: '余额支付',
    icon: '/static/images/payment/balance.png'
  }
]);

// 配送方式选项
const deliveryOptions = ref([
  {
    type: 'express',
    name: '快递配送'
  },
  {
    type: 'self',
    name: '到店自提'
  }
]);

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 选择收货地址
const selectAddress = () => {
  uni.navigateTo({
    url: '/subPackages/activity-showcase/pages/my/address'
  });
};

// 选择配送方式
const selectDelivery = (shop, type) => {
  shop.deliveryType = type;
};

// 选择优惠券
const selectCoupon = (shop) => {
  uni.showToast({
    title: '优惠券功能开发中',
    icon: 'none'
  });
};

// 选择支付方式
const selectPayment = (type) => {
  paymentType.value = type;
};

// 获取店铺商品总数量
const getTotalQuantity = (shop) => {
  return shop.items.reduce((total, item) => total + item.quantity, 0);
};

// 获取店铺商品总金额
const getShopTotal = (shop) => {
  return shop.items.reduce((total, item) => total + item.price * item.quantity, 0);
};

// 获取所有商品总金额
const getProductTotal = () => {
  return orderShops.value.reduce((total, shop) => {
    return total + getShopTotal(shop);
  }, 0);
};

// 获取运费
const getDeliveryFee = () => {
  return orderShops.value.reduce((total, shop) => {
    return total + (shop.deliveryFee || 0);
  }, 0);
};

// 获取优惠金额
const getDiscountAmount = () => {
  return orderShops.value.reduce((total, shop) => {
    return total + (shop.coupon ? shop.coupon.amount : 0);
  }, 0);
};

// 获取订单总金额
const getTotalAmount = () => {
  return getProductTotal() + getDeliveryFee() - getDiscountAmount();
};

// 提交订单
const submitOrder = () => {
  // 验证收货地址
  if (!address.value) {
    uni.showToast({
      title: '请选择收货地址',
      icon: 'none'
    });
    return;
  }
  
  // 模拟提交订单
  uni.showLoading({
    title: '提交订单中...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    
    // 模拟订单ID
    const orderId = 'ORDER_' + Date.now();
    
    // 跳转到支付页面
    uni.navigateTo({
      url: `/subPackages/activity-showcase/pages/payment/index?orderId=${orderId}&amount=${getTotalAmount()}`
    });
  }, 1500);
};

// 初始化订单数据
const initOrderData = (fromCart) => {
  // 模拟订单数据
  orderShops.value = [
    {
      id: 'shop1',
      name: 'Apple授权专卖店',
      logo: 'https://via.placeholder.com/100',
      deliveryType: 'express',
      deliveryFee: 10,
      remark: '',
      coupon: {
        id: 'coupon1',
        name: '满5000减300',
        amount: 300
      },
      items: [
        {
          id: '1',
          name: 'iPhone 14 Pro 深空黑 256G',
          specs: '颜色：深空黑；内存：256G',
          price: 7999,
          quantity: 1,
          image: 'https://via.placeholder.com/300x300'
        }
      ]
    },
    {
      id: 'shop2',
      name: '华为官方旗舰店',
      logo: 'https://via.placeholder.com/100',
      deliveryType: 'express',
      deliveryFee: 0,
      remark: '',
      coupon: null,
      items: [
        {
          id: '3',
          name: '华为Mate 50 Pro 曜金黑 512G',
          specs: '颜色：曜金黑；内存：512G',
          price: 6999,
          quantity: 1,
          image: 'https://via.placeholder.com/300x300'
        }
      ]
    }
  ];
};

onMounted(() => {
  const query = uni.getSystemInfoSync().platform === 'devtools' 
    ? { from: 'cart' } // 开发环境模拟数据
    : uni.$u.route.query;
    
  // 从购物车进入或从商品详情进入
  const fromCart = query.from === 'cart';
  initOrderData(fromCart);
});
</script>

<style lang="scss" scoped>
.order-confirm-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F2F2F7;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
  
  .navbar-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 4px 6px rgba(255,59,105,0.15);
  }
  
  .navbar-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 30rpx;
    padding-top: var(--status-bar-height, 25px);
    box-sizing: border-box;
  }
  
  .navbar-left, .navbar-right {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80rpx;
    height: 80rpx;
  }
  
  .navbar-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #FFFFFF;
    letter-spacing: 0.5px;
  }
  
  .icon {
    width: 48rpx;
    height: 48rpx;
  }
}

/* 内容区域 */
.content-scroll {
  flex: 1;
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
  margin-bottom: 100rpx; /* 底部结算栏高度 */
}

/* 收货地址 */
.address-section {
  background-color: #FFFFFF;
  margin-bottom: 20rpx;
  position: relative;
  
  .address-content {
    display: flex;
    align-items: center;
    padding: 30rpx;
    
    .address-info {
      flex: 1;
      
      .user-info {
        margin-bottom: 10rpx;
        
        .user-name {
          font-size: 32rpx;
          font-weight: 600;
          color: #333333;
          margin-right: 20rpx;
        }
        
        .user-phone {
          font-size: 28rpx;
          color: #666666;
        }
      }
      
      .address-detail {
        font-size: 28rpx;
        color: #666666;
        line-height: 1.4;
      }
    }
    
    .arrow-icon {
      width: 32rpx;
      height: 32rpx;
      color: #999999;
    }
  }
  
  .address-empty {
    padding: 30rpx;
    
    .add-address {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      text {
        font-size: 28rpx;
        color: #666666;
      }
      
      .arrow-icon {
        width: 32rpx;
        height: 32rpx;
        color: #999999;
      }
    }
  }
  
  .address-divider {
    height: 10rpx;
    width: 100%;
    overflow: hidden;
    
    .divider-image {
      width: 100%;
      height: 10rpx;
    }
  }
}

/* 商品列表 */
.products-section {
  margin-bottom: 20rpx;
}

.shop-group {
  background-color: #FFFFFF;
  margin-bottom: 20rpx;
  
  .shop-header {
    display: flex;
    align-items: center;
    padding: 20rpx 30rpx;
    border-bottom: 1rpx solid #F2F2F7;
    
    .shop-logo {
      width: 40rpx;
      height: 40rpx;
      border-radius: 50%;
    }
    
    .shop-name {
      font-size: 28rpx;
      color: #333333;
      margin-left: 10rpx;
      font-weight: 500;
    }
  }
  
  .product-list {
    padding: 0 30rpx;
  }
  
  .product-item {
    display: flex;
    padding: 30rpx 0;
    border-bottom: 1rpx solid #F2F2F7;
    
    &:last-child {
      border-bottom: none;
    }
    
    .product-image {
      width: 160rpx;
      height: 160rpx;
      border-radius: 8rpx;
    }
    
    .product-info {
      flex: 1;
      margin-left: 20rpx;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      
      .product-name {
        font-size: 28rpx;
        color: #333333;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
      }
      
      .product-specs {
        font-size: 24rpx;
        color: #999999;
        margin-top: 10rpx;
      }
      
      .product-price-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: auto;
        
        .product-price {
          font-size: 28rpx;
          color: #FF3B69;
          font-weight: 600;
        }
        
        .product-quantity {
          font-size: 28rpx;
          color: #999999;
        }
      }
    }
  }
  
  .delivery-section, .remark-section, .coupon-section {
    padding: 20rpx 30rpx;
    border-top: 1rpx solid #F2F2F7;
    
    .section-title {
      font-size: 28rpx;
      color: #333333;
      margin-bottom: 20rpx;
    }
  }
  
  .delivery-options {
    display: flex;
    
    .delivery-option {
      padding: 10rpx 20rpx;
      border-radius: 30rpx;
      font-size: 24rpx;
      color: #666666;
      background-color: #F2F2F7;
      margin-right: 20rpx;
      
      &.active {
        color: #FFFFFF;
        background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
      }
    }
  }
  
  .remark-input {
    height: 80rpx;
    background-color: #F2F2F7;
    border-radius: 8rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    color: #333333;
  }
  
  .coupon-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    text {
      font-size: 28rpx;
      color: #FF3B69;
    }
    
    .arrow-icon {
      width: 32rpx;
      height: 32rpx;
      color: #999999;
    }
  }
  
  .shop-subtotal {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 30rpx;
    border-top: 1rpx solid #F2F2F7;
    
    text {
      font-size: 28rpx;
      color: #333333;
      
      &:last-child {
        font-weight: 600;
      }
    }
  }
}

/* 支付方式 */
.payment-section {
  background-color: #FFFFFF;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  
  .section-title {
    font-size: 28rpx;
    color: #333333;
    margin-bottom: 20rpx;
  }
  
  .payment-options {
    .payment-option {
      display: flex;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #F2F2F7;
      
      &:last-child {
        border-bottom: none;
      }
      
      .payment-icon {
        width: 48rpx;
        height: 48rpx;
        margin-right: 20rpx;
      }
      
      text {
        flex: 1;
        font-size: 28rpx;
        color: #333333;
      }
      
      .checkbox {
        width: 36rpx;
        height: 36rpx;
        border-radius: 50%;
        background-color: #FF3B69;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .check-icon {
          color: #FFFFFF;
        }
      }
    }
  }
}

/* 订单金额 */
.amount-section {
  background-color: #FFFFFF;
  padding: 20rpx 30rpx;
  
  .amount-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10rpx 0;
    
    text {
      font-size: 28rpx;
      color: #333333;
    }
  }
}

/* 底部结算栏 */
.checkout-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  padding-bottom: env(safe-area-inset-bottom); /* 适配 iPhone X 以上的底部安全区域 */
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 99;
  
  .total-amount {
    font-size: 28rpx;
    color: #333333;
    
    .price {
      font-size: 32rpx;
      color: #FF3B69;
      font-weight: 600;
    }
  }
  
  .submit-btn {
    padding: 16rpx 40rpx;
    background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
    color: #FFFFFF;
    font-size: 28rpx;
    border-radius: 40rpx;
  }
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 34px; /* iOS 安全区域高度 */
}
</style> 