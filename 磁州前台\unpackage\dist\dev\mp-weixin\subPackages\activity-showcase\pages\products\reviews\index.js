"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const productId = common_vendor.ref("");
    const product = common_vendor.ref({
      id: "",
      name: "商品加载中...",
      price: 0,
      image: "https://via.placeholder.com/300x300"
    });
    const reviewTags = common_vendor.ref([
      { id: "all", name: "全部", count: 235 },
      { id: "positive", name: "好评", count: 220 },
      { id: "neutral", name: "中评", count: 10 },
      { id: "negative", name: "差评", count: 5 },
      { id: "hasImage", name: "有图", count: 120 },
      { id: "hasVideo", name: "有视频", count: 30 }
    ]);
    const activeTag = common_vendor.ref("all");
    const reviews = common_vendor.ref([]);
    const totalReviews = common_vendor.computed(() => {
      var _a;
      return ((_a = reviewTags.value.find((tag) => tag.id === "all")) == null ? void 0 : _a.count) || 0;
    });
    const positiveRate = common_vendor.computed(() => {
      var _a;
      const positive = ((_a = reviewTags.value.find((tag) => tag.id === "positive")) == null ? void 0 : _a.count) || 0;
      const total = totalReviews.value;
      return total > 0 ? Math.round(positive / total * 100) : 100;
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const selectTag = (tagId) => {
      activeTag.value = tagId;
      loadReviews();
    };
    const toggleLike = (review) => {
      review.isLiked = !review.isLiked;
      if (review.isLiked) {
        review.likes = (review.likes || 0) + 1;
      } else {
        review.likes = Math.max(0, (review.likes || 0) - 1);
      }
    };
    const previewImage = (images, current) => {
      common_vendor.index.previewImage({
        urls: images,
        current: images[current]
      });
    };
    const loadMore = () => {
      common_vendor.index.showToast({
        title: "已加载全部评价",
        icon: "none"
      });
    };
    const loadReviews = () => {
      reviews.value = [
        {
          id: "1",
          userName: "用户***123",
          userAvatar: "https://via.placeholder.com/100",
          rating: 5,
          content: "手机质量很好，拍照效果出色，电池续航也不错，总体来说非常满意！屏幕显示效果特别好，色彩鲜艳，动态岛设计很有创意。",
          time: "2023-09-15",
          specs: "颜色：深空黑；内存：256G",
          images: [
            "https://via.placeholder.com/300x300",
            "https://via.placeholder.com/300x300",
            "https://via.placeholder.com/300x300"
          ],
          likes: 12,
          isLiked: false
        },
        {
          id: "2",
          userName: "张**",
          userAvatar: "https://via.placeholder.com/100",
          rating: 4,
          content: "手机整体不错，就是电池续航一般，重度使用一天需要充两次电。相机效果很棒，尤其是夜景模式表现优秀。",
          time: "2023-09-10",
          specs: "颜色：银色；内存：256G",
          images: [
            "https://via.placeholder.com/300x300",
            "https://via.placeholder.com/300x300"
          ],
          shopReply: "感谢您的评价！关于电池续航问题，建议您检查是否有后台应用过度耗电，或者可以尝试更新到最新系统版本，这可能会优化电池性能。如有其他问题，欢迎随时联系我们的客服。",
          likes: 8,
          isLiked: false
        },
        {
          id: "3",
          userName: "李**",
          userAvatar: "https://via.placeholder.com/100",
          rating: 5,
          content: "物流超快，第二天就收到了。包装很好，手机外观漂亮，系统流畅，总之很满意的一次购物体验！",
          time: "2023-09-05",
          specs: "颜色：紫色；内存：512G",
          likes: 5,
          isLiked: false
        },
        {
          id: "4",
          userName: "王**",
          userAvatar: "https://via.placeholder.com/100",
          rating: 3,
          content: "手机还行吧，但感觉不值这个价，性能提升不是很明显，还不如用旧手机多用几年。",
          time: "2023-09-01",
          specs: "颜色：深空黑；内存：128G",
          shopReply: "感谢您的反馈。我们的新款手机在处理器、相机系统和屏幕显示等方面都有显著提升。如果您有任何使用问题，欢迎联系我们的售后服务团队，我们将竭诚为您服务。",
          likes: 2,
          isLiked: false
        }
      ];
    };
    const getProductInfo = (id) => {
      setTimeout(() => {
        product.value = {
          id,
          name: "iPhone 14 Pro 深空黑 256G",
          price: 7999,
          image: "https://via.placeholder.com/300x300"
        };
      }, 500);
    };
    common_vendor.onMounted(() => {
      const query = common_vendor.index.getSystemInfoSync().platform === "devtools" ? { id: "1" } : common_vendor.index.$u.route.query;
      if (query.id) {
        productId.value = query.id;
        getProductInfo(query.id);
      }
      loadReviews();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          d: "M19 12H5M12 19l-7-7 7-7",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        b: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        c: common_vendor.o(goBack),
        d: product.value.image,
        e: common_vendor.t(product.value.name),
        f: common_vendor.t(product.value.price.toFixed(2)),
        g: common_vendor.t(totalReviews.value),
        h: common_vendor.t(positiveRate.value),
        i: common_vendor.f(reviewTags.value, (tag, k0, i0) => {
          return {
            a: common_vendor.t(tag.name),
            b: common_vendor.t(tag.count),
            c: activeTag.value === tag.id ? 1 : "",
            d: tag.id,
            e: common_vendor.o(($event) => selectTag(tag.id), tag.id)
          };
        }),
        j: reviews.value.length > 0
      }, reviews.value.length > 0 ? {
        k: common_vendor.f(reviews.value, (review, index, i0) => {
          return common_vendor.e({
            a: review.userAvatar,
            b: common_vendor.t(review.userName),
            c: common_vendor.t(review.time),
            d: common_vendor.f(5, (i, k1, i1) => {
              return {
                a: i,
                b: i <= review.rating ? 1 : ""
              };
            }),
            e: common_vendor.t(review.content),
            f: review.images && review.images.length > 0
          }, review.images && review.images.length > 0 ? {
            g: common_vendor.f(review.images, (image, imgIndex, i1) => {
              return {
                a: imgIndex,
                b: image,
                c: common_vendor.o(($event) => previewImage(review.images, imgIndex), imgIndex)
              };
            })
          } : {}, {
            h: review.specs
          }, review.specs ? {
            i: common_vendor.t(review.specs)
          } : {}, {
            j: review.shopReply
          }, review.shopReply ? {
            k: common_vendor.t(review.shopReply)
          } : {}, {
            l: "1398ae02-3-" + i0 + "," + ("1398ae02-2-" + i0),
            m: review.isLiked ? 1 : "",
            n: "1398ae02-2-" + i0,
            o: common_vendor.t(review.likes || 0),
            p: common_vendor.o(($event) => toggleLike(review), index),
            q: index
          });
        }),
        l: common_vendor.p({
          d: "M14 9V5a3 3 0 00-3-3l-4 9v11h11.28a2 2 0 002-1.7l1.38-9a2 2 0 00-2-2.3H14zM7 22H4a2 2 0 01-2-2v-7a2 2 0 012-2h3",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        m: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        })
      } : {
        n: common_assets._imports_0$64
      }, {
        o: common_vendor.o(loadMore)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-1398ae02"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/products/reviews/index.js.map
