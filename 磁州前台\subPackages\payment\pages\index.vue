<template>
  <view class="wallet-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">我的钱包</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 账户余额卡片 -->
    <view class="balance-card" :style="{ marginTop: (navbarHeight + 10) + 'px' }">
      <view class="balance-title">账户余额 (元)</view>
      <view class="balance-amount">{{ balanceInfo.amount.toFixed(2) }}</view>
      <view class="balance-buttons">
        <button class="balance-btn withdraw-btn" @click="navigateToWithdraw">提现</button>
        <button class="balance-btn recharge-btn" @click="navigateToRecharge">充值</button>
      </view>
    </view>
    
    <!-- 钱包功能区 -->
    <view class="wallet-functions">
      <view class="function-item" @click="navigateTo('/subPackages/payment/pages/detail')">
        <view class="function-left">
          <image class="function-icon" src="/static/images/tabbar/钱包明细.png"></image>
          <text class="function-name">钱包明细</text>
        </view>
        <image class="arrow-icon" src="/static/images/tabbar/arrow-up.png"></image>
      </view>
      
      <view class="function-item" @click="navigateTo('/subPackages/payment/pages/bills')">
        <view class="function-left">
          <image class="function-icon" src="/static/images/tabbar/收支记录.png"></image>
          <text class="function-name">收支记录</text>
        </view>
        <image class="arrow-icon" src="/static/images/tabbar/arrow-up.png"></image>
      </view>
      
      <view class="function-item" @click="navigateTo('/subPackages/payment/pages/bank')">
        <view class="function-left">
          <image class="function-icon" src="/static/images/tabbar/银行卡.png"></image>
          <text class="function-name">银行卡管理</text>
        </view>
        <image class="arrow-icon" src="/static/images/tabbar/arrow-up.png"></image>
      </view>
    </view>
    
    <!-- 交易记录 -->
    <view class="transaction-section">
      <view class="section-header">
        <text class="section-title">近期交易</text>
        <text class="section-more" @click="navigateTo('/subPackages/payment/pages/bills')">查看更多</text>
      </view>
      
      <view class="transaction-list">
        <view v-if="transactions.length > 0">
          <view class="transaction-item" v-for="(item, index) in transactions" :key="index">
            <view class="transaction-left">
              <view class="transaction-title">{{ item.title }}</view>
              <view class="transaction-time">{{ item.time }}</view>
            </view>
            <view class="transaction-amount" :class="{'income': item.type === 'income', 'expense': item.type === 'expense'}">
              {{ item.type === 'income' ? '+' : '-' }}{{ item.amount.toFixed(2) }}
            </view>
          </view>
        </view>
        <view v-else class="empty-view">
          <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
          <view class="empty-text">暂无交易记录</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { smartNavigate } from '@/utils/navigation.js';

// 响应式状态
const statusBarHeight = ref(20);
const navbarHeight = ref(64);
const balanceInfo = ref({
  amount: 0.00,
  frozenAmount: 0.00
});
const transactions = ref([
  {
    id: 'tx001',
    title: '充值',
    time: '2023-11-05 14:30',
    amount: 100.00,
    type: 'income'
  },
  {
    id: 'tx002',
    title: '服务支付',
    time: '2023-11-03 09:15',
    amount: 35.00,
    type: 'expense'
  },
  {
    id: 'tx003',
    title: '提现',
    time: '2023-10-28 16:22',
    amount: 50.00,
    type: 'expense'
  }
]);

// 方法
// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 页面跳转
const navigateTo = (url) => {
  smartNavigate(url).catch(err => {
    console.error('页面跳转失败:', err);
  });
};

// 跳转到提现页面
const navigateToWithdraw = () => {
  navigateTo('/subPackages/payment/pages/withdraw');
};

// 跳转到充值页面
const navigateToRecharge = () => {
  navigateTo('/subPackages/payment/pages/recharge');
};

// 获取钱包余额
const getWalletBalance = () => {
  // 这里应该是从API获取钱包余额
  // 模拟API请求
  setTimeout(() => {
    balanceInfo.value = {
      amount: 158.50,
      frozenAmount: 0.00
    };
  }, 500);
};

// 生命周期钩子
onMounted(() => {
  // 获取状态栏高度
  const sysInfo = uni.getSystemInfoSync();
  statusBarHeight.value = sysInfo.statusBarHeight;
  navbarHeight.value = statusBarHeight.value + 44;
  
  // 获取钱包余额
  getWalletBalance();
});
</script>

<style>
.wallet-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
}

.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.navbar-right {
  width: 80rpx;
}

/* 余额卡片 */
.balance-card {
  background: linear-gradient(to right, #0052CC, #0066FF);
  margin: 0 30rpx;
  padding: a0rpx;
  border-radius: 20rpx;
  color: #fff;
  overflow: hidden;
  box-shadow: 0 8rpx 20rpx rgba(0, 82, 204, 0.2);
}

.balance-title {
  font-size: 28rpx;
  opacity: 0.9;
  margin: 30rpx 0 20rpx 40rpx;
}

.balance-amount {
  font-size: 60rpx;
  font-weight: bold;
  margin: 0 0 40rpx 40rpx;
}

.balance-buttons {
  display: flex;
  justify-content: space-around;
  margin: 30rpx 40rpx 40rpx;
}

.balance-btn {
  width: 240rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  border-radius: 40rpx;
  margin: 0;
}

.withdraw-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.recharge-btn {
  background-color: #fff;
  color: #0052CC;
}

/* 钱包功能区 */
.wallet-functions {
  background-color: #fff;
  margin: 30rpx;
  border-radius: 20rpx;
  padding: 20rpx 0;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}

.function-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.function-item:last-child {
  border-bottom: none;
}

.function-left {
  display: flex;
  align-items: center;
}

.function-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
  background-color: #f8f9fc;
  border-radius: 20rpx;
  padding: 10rpx;
}

.function-name {
  font-size: 28rpx;
  color: #333;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  transform: rotate(90deg);
  opacity: 0.5;
}

/* 交易记录区域 */
.transaction-section {
  background-color: #fff;
  margin: 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.section-more {
  font-size: 24rpx;
  color: #0052CC;
}

.transaction-list {
  min-height: 300rpx;
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-left {
  display: flex;
  flex-direction: column;
}

.transaction-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.transaction-time {
  font-size: 24rpx;
  color: #999;
}

.transaction-amount {
  font-size: 32rpx;
  font-weight: 500;
}

.income {
  color: #07c160;
}

.expense {
  color: #f56c6c;
}

/* 空状态 */
.empty-view {
  padding: 60rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style> 
