"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  __name: "verify",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const isAgreed = common_vendor.ref(false);
    const formData = common_vendor.reactive({
      shopName: "",
      legalPerson: "",
      address: "",
      contactPhone: "",
      businessScope: "",
      licenseImage: "",
      idCardFrontImage: "",
      idCardBackImage: "",
      storefrontImage: "",
      qualificationImage: ""
    });
    const isFormValid = common_vendor.computed(() => {
      return isAgreed.value && formData.shopName && formData.legalPerson && formData.address && formData.contactPhone && formData.businessScope && formData.licenseImage && formData.idCardFrontImage && formData.idCardBackImage && formData.storefrontImage;
    });
    common_vendor.onLoad(() => {
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight;
      loadMerchantInfo();
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const loadMerchantInfo = () => {
      const merchantInfo = common_vendor.index.getStorageSync("lastMerchantData");
      if (merchantInfo) {
        formData.shopName = merchantInfo.shopName || "";
        formData.address = merchantInfo.address || "";
        formData.contactPhone = merchantInfo.contactPhone || "";
      }
    };
    const chooseImage = (type) => {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          common_vendor.index.showLoading({
            title: "处理中...",
            mask: true
          });
          addWatermark(tempFilePath, type);
        }
      });
    };
    const addWatermark = (imagePath, type) => {
      const watermarkText = "只用于磁州生活网认证";
      common_vendor.index.getImageInfo({
        src: imagePath,
        success: (imageInfo) => {
          const canvasWidth = imageInfo.width;
          const canvasHeight = imageInfo.height;
          const ctx = common_vendor.index.createCanvasContext("watermarkCanvas");
          common_vendor.index.createSelectorQuery().select("#watermarkCanvas");
          ctx.clearRect(0, 0, canvasWidth, canvasHeight);
          ctx.drawImage(imagePath, 0, 0, canvasWidth, canvasHeight);
          ctx.setFontSize(20);
          ctx.setFillStyle("rgba(0, 0, 0, 0.3)");
          ctx.setGlobalAlpha(0.5);
          ctx.rotate(-20 * Math.PI / 180);
          const textWidth = ctx.measureText(watermarkText).width;
          for (let y = -canvasHeight; y < canvasHeight * 2; y += 100) {
            for (let x = -canvasWidth; x < canvasWidth * 2; x += textWidth + 80) {
              ctx.fillText(watermarkText, x, y);
            }
          }
          ctx.draw(false, () => {
            common_vendor.index.canvasToTempFilePath({
              canvasId: "watermarkCanvas",
              success: (res) => {
                formData[type + "Image"] = res.tempFilePath;
                common_vendor.index.hideLoading();
              },
              fail: (err) => {
                common_vendor.index.__f__("error", "at pages/business/verify.vue:281", "Canvas to temp file path failed:", err);
                common_vendor.index.hideLoading();
                common_vendor.index.showToast({ title: "图片处理失败", icon: "none" });
              }
            });
          });
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/business/verify.vue:289", "Get image info failed:", err);
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({ title: "无法获取图片信息", icon: "none" });
        }
      });
    };
    const checkboxChange = (e) => {
      isAgreed.value = e.detail.value.includes("agree");
    };
    const showAgreement = () => {
      common_vendor.index.showModal({
        title: "商家认证服务协议",
        content: "这里是详细的商家认证服务协议内容...（内容待填充）",
        showCancel: false,
        confirmText: "我已阅读"
      });
    };
    const submitVerify = () => {
      if (!isFormValid.value) {
        common_vendor.index.showToast({
          title: "请填写完整的必填信息并同意协议",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "提交中...",
        mask: true
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "提交成功！待审核",
          icon: "success"
        });
        common_vendor.index.__f__("log", "at pages/business/verify.vue:332", "提交的表单数据:", formData);
        setTimeout(() => {
          common_vendor.index.redirectTo({
            url: "/pages/business/success?from=verify"
          });
        }, 1500);
      }, 2e3);
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: statusBarHeight.value + "px",
        b: common_assets._imports_0$13,
        c: common_vendor.o(goBack),
        d: common_assets._imports_1$7,
        e: formData.shopName,
        f: common_vendor.o(($event) => formData.shopName = $event.detail.value),
        g: formData.legalPerson,
        h: common_vendor.o(($event) => formData.legalPerson = $event.detail.value),
        i: formData.address,
        j: common_vendor.o(($event) => formData.address = $event.detail.value),
        k: formData.contactPhone,
        l: common_vendor.o(($event) => formData.contactPhone = $event.detail.value),
        m: formData.businessScope,
        n: common_vendor.o(($event) => formData.businessScope = $event.detail.value),
        o: !formData.licenseImage
      }, !formData.licenseImage ? {
        p: common_assets._imports_2$7
      } : {
        q: formData.licenseImage
      }, {
        r: common_vendor.o(($event) => chooseImage("license")),
        s: !formData.idCardFrontImage
      }, !formData.idCardFrontImage ? {
        t: common_assets._imports_2$7
      } : {
        v: formData.idCardFrontImage
      }, {
        w: common_vendor.o(($event) => chooseImage("idCardFront")),
        x: !formData.idCardBackImage
      }, !formData.idCardBackImage ? {
        y: common_assets._imports_2$7
      } : {
        z: formData.idCardBackImage
      }, {
        A: common_vendor.o(($event) => chooseImage("idCardBack")),
        B: !formData.storefrontImage
      }, !formData.storefrontImage ? {
        C: common_assets._imports_2$7
      } : {
        D: formData.storefrontImage
      }, {
        E: common_vendor.o(($event) => chooseImage("storefront")),
        F: !formData.qualificationImage
      }, !formData.qualificationImage ? {
        G: common_assets._imports_2$7
      } : {
        H: formData.qualificationImage
      }, {
        I: common_vendor.o(($event) => chooseImage("qualification")),
        J: isAgreed.value,
        K: common_vendor.o(showAgreement),
        L: common_vendor.o(checkboxChange),
        M: !isFormValid.value,
        N: !isFormValid.value ? 1 : "",
        O: common_vendor.o(submitVerify)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/business/verify.js.map
