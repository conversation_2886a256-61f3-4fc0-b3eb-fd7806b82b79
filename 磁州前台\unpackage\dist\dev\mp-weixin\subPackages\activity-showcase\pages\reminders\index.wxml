<view class="reminders-container data-v-f3ba5f86"><view class="custom-navbar data-v-f3ba5f86"><view class="navbar-bg data-v-f3ba5f86"></view><view class="navbar-content data-v-f3ba5f86"><view class="back-btn data-v-f3ba5f86" bindtap="{{b}}"><image src="{{a}}" mode="aspectFit" class="back-icon data-v-f3ba5f86"></image></view><view class="navbar-title data-v-f3ba5f86">活动提醒</view><view class="navbar-right data-v-f3ba5f86"><view class="settings-btn data-v-f3ba5f86" bindtap="{{f}}"><svg wx:if="{{e}}" u-s="{{['d']}}" class="icon data-v-f3ba5f86" u-i="f3ba5f86-0" bind:__l="__l" u-p="{{e}}"><circle wx:if="{{c}}" class="data-v-f3ba5f86" u-i="f3ba5f86-1,f3ba5f86-0" bind:__l="__l" u-p="{{c}}"></circle><path wx:if="{{d}}" class="data-v-f3ba5f86" u-i="f3ba5f86-2,f3ba5f86-0" bind:__l="__l" u-p="{{d}}"></path></svg></view></view></view></view><view class="reminder-tabs data-v-f3ba5f86"><view wx:for="{{g}}" wx:for-item="tab" wx:key="c" class="{{['tab-item', 'data-v-f3ba5f86', tab.d && 'active']}}" bindtap="{{tab.e}}"><text class="tab-text data-v-f3ba5f86">{{tab.a}}</text><view wx:if="{{tab.b}}" class="tab-indicator data-v-f3ba5f86" style="{{'background:' + 'linear-gradient(90deg, #34C759 0%, #7ED321 100%)'}}"></view></view></view><swiper class="reminders-swiper data-v-f3ba5f86" current="{{k}}" bindchange="{{l}}"><swiper-item wx:for="{{h}}" wx:for-item="tab" wx:key="h" class="data-v-f3ba5f86"><scroll-view class="tab-content data-v-f3ba5f86" scroll-y refresher-enabled refresher-triggered="{{j}}" bindrefresherrefresh="{{tab.f}}" bindscrolltolower="{{tab.g}}"><view class="reminders-list data-v-f3ba5f86"><view wx:for="{{tab.a}}" wx:for-item="reminder" wx:key="t" class="{{['reminder-card', 'data-v-f3ba5f86', reminder.v && 'read']}}"><view class="reminder-icon data-v-f3ba5f86" style="{{'background:' + reminder.d}}"><svg wx:if="{{i}}" u-s="{{['d']}}" class="icon data-v-f3ba5f86" u-i="{{reminder.c}}" bind:__l="__l" u-p="{{i}}"><path wx:if="{{reminder.b}}" class="data-v-f3ba5f86" u-i="{{reminder.a}}" bind:__l="__l" u-p="{{reminder.b}}"></path></svg></view><view class="reminder-content data-v-f3ba5f86" bindtap="{{reminder.s}}"><view class="reminder-header data-v-f3ba5f86"><text class="reminder-title data-v-f3ba5f86">{{reminder.e}}</text><text class="reminder-time data-v-f3ba5f86">{{reminder.f}}</text></view><text class="reminder-desc data-v-f3ba5f86">{{reminder.g}}</text><view wx:if="{{reminder.h}}" class="activity-info data-v-f3ba5f86"><image src="{{reminder.i}}" class="activity-image data-v-f3ba5f86" mode="aspectFill"></image><view class="activity-details data-v-f3ba5f86"><text class="activity-name data-v-f3ba5f86">{{reminder.j}}</text><view class="activity-meta data-v-f3ba5f86"><text class="activity-date data-v-f3ba5f86">{{reminder.k}}</text><text class="activity-location data-v-f3ba5f86">{{reminder.l}}</text></view></view></view><view class="reminder-actions data-v-f3ba5f86"><view class="action-btn data-v-f3ba5f86" style="{{'background:' + reminder.n + ';' + ('color:' + '#FFFFFF')}}" catchtap="{{reminder.o}}">{{reminder.m}}</view><view wx:if="{{reminder.p}}" class="action-btn secondary data-v-f3ba5f86" catchtap="{{reminder.q}}"> 标记为已读 </view><view wx:else class="action-btn secondary data-v-f3ba5f86" catchtap="{{reminder.r}}"> 删除 </view></view></view></view></view><view wx:if="{{tab.b}}" class="empty-state data-v-f3ba5f86"><image class="empty-image data-v-f3ba5f86" src="{{tab.c}}"></image><text class="empty-text data-v-f3ba5f86">{{tab.d}}</text><view class="action-btn data-v-f3ba5f86" bindtap="{{tab.e}}" style="{{'background:' + 'linear-gradient(135deg, #34C759 0%, #7ED321 100%)' + ';' + ('border-radius:' + '35px') + ';' + ('box-shadow:' + '0 5px 15px rgba(52,199,89,0.3)')}}"><text class="data-v-f3ba5f86">去参与活动</text></view></view></scroll-view></swiper-item></swiper><uni-popup wx:if="{{I}}" class="r data-v-f3ba5f86" u-s="{{['d']}}" u-r="settingsPopup" u-i="f3ba5f86-5" bind:__l="__l" u-p="{{I}}"><view class="settings-popup data-v-f3ba5f86"><view class="settings-header data-v-f3ba5f86"><text class="settings-title data-v-f3ba5f86">提醒设置</text><view class="settings-close data-v-f3ba5f86" bindtap="{{p}}"><svg wx:if="{{o}}" u-s="{{['d']}}" class="icon data-v-f3ba5f86" u-i="f3ba5f86-6,f3ba5f86-5" bind:__l="__l" u-p="{{o}}"><line wx:if="{{m}}" class="data-v-f3ba5f86" u-i="f3ba5f86-7,f3ba5f86-6" bind:__l="__l" u-p="{{m}}"></line><line wx:if="{{n}}" class="data-v-f3ba5f86" u-i="f3ba5f86-8,f3ba5f86-6" bind:__l="__l" u-p="{{n}}"></line></svg></view></view><view class="settings-content data-v-f3ba5f86"><view class="settings-section data-v-f3ba5f86"><text class="section-title data-v-f3ba5f86">通知设置</text><view class="settings-item data-v-f3ba5f86"><text class="item-label data-v-f3ba5f86">活动开始提醒</text><switch class="data-v-f3ba5f86" checked="{{q}}" bindchange="{{r}}" color="#34C759"/></view><view class="settings-item data-v-f3ba5f86"><text class="item-label data-v-f3ba5f86">活动变更提醒</text><switch class="data-v-f3ba5f86" checked="{{s}}" bindchange="{{t}}" color="#34C759"/></view><view class="settings-item data-v-f3ba5f86"><text class="item-label data-v-f3ba5f86">报名成功提醒</text><switch class="data-v-f3ba5f86" checked="{{v}}" bindchange="{{w}}" color="#34C759"/></view><view class="settings-item data-v-f3ba5f86"><text class="item-label data-v-f3ba5f86">活动取消提醒</text><switch class="data-v-f3ba5f86" checked="{{x}}" bindchange="{{y}}" color="#34C759"/></view></view><view class="settings-section data-v-f3ba5f86"><text class="section-title data-v-f3ba5f86">提醒时间</text><view class="settings-item data-v-f3ba5f86"><text class="item-label data-v-f3ba5f86">提前提醒时间</text><picker class="data-v-f3ba5f86" mode="selector" range="{{C}}" value="{{D}}" bindchange="{{E}}"><view class="picker-value data-v-f3ba5f86"><text class="data-v-f3ba5f86">{{z}}</text><svg wx:if="{{B}}" u-s="{{['d']}}" class="icon data-v-f3ba5f86" u-i="f3ba5f86-9,f3ba5f86-5" bind:__l="__l" u-p="{{B}}"><path wx:if="{{A}}" class="data-v-f3ba5f86" u-i="f3ba5f86-10,f3ba5f86-9" bind:__l="__l" u-p="{{A}}"></path></svg></view></picker></view></view><view class="settings-section data-v-f3ba5f86"><text class="section-title data-v-f3ba5f86">清除记录</text><view class="clear-options data-v-f3ba5f86"><view class="clear-btn data-v-f3ba5f86" bindtap="{{F}}"><text class="data-v-f3ba5f86">清除已读提醒</text></view><view class="clear-btn danger data-v-f3ba5f86" bindtap="{{G}}"><text class="data-v-f3ba5f86">清除全部提醒</text></view></view></view></view></view></uni-popup></view>