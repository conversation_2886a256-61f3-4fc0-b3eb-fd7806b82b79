<template>
  <view class="promotions-page">
    <!-- 顶部导航栏 -->
    <view class="navbar">
      <view class="navbar-left">
        <view class="back-button" @tap="goBack">
          <svg class="back-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 18L9 12L15 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </view>
      </view>
      <view class="navbar-title">
        <text class="title-main">活动管理</text>
      </view>
      <view class="navbar-right">
        <view class="add-button" @tap="createPromotion">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 5V19M5 12H19" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </view>
      </view>
    </view>

    <!-- 内容滚动区域 -->
    <scroll-view class="content-container" scroll-y refresher-enabled @refresherrefresh="onRefresh" :refresher-triggered="isRefreshing">
      <!-- 活动分类标签页 -->
      <view class="tab-container">
        <view 
          v-for="(tab, index) in tabs" 
          :key="index" 
          class="tab-item" 
          :class="{ active: currentTab === tab.value }"
          @tap="switchTab(tab.value)"
        >
          {{ tab.label }}
        </view>
      </view>
    
      <!-- 活动列表 -->
      <view class="promotions-list" v-if="filteredPromotions.length > 0">
        <view 
          v-for="(promotion, index) in filteredPromotions" 
          :key="promotion.id"
          class="promotion-card"
          :class="{ expired: promotion.status === 'expired' }"
          @tap="navigateToDetail(promotion.id)"
        >
          <!-- 活动状态标签 -->
          <view class="status-tag" :class="getStatusClass(promotion.status)">
            {{ getStatusText(promotion.status) }}
          </view>
          
          <!-- 活动封面图 -->
          <view class="promotion-banner">
            <image class="banner-image" :src="promotion.banner" mode="aspectFill"></image>
          </view>
          
          <!-- 活动信息 -->
          <view class="promotion-content">
            <text class="promotion-title">{{ promotion.title }}</text>
            
            <view class="promotion-info">
              <view class="info-item">
                <svg class="info-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8 2V6M16 2V6M3 10H21M5 4H19C20.1046 4 21 4.89543 21 6V20C21 21.1046 20.1046 22 19 22H5C3.89543 22 3 21.1046 3 20V6C3 4.89543 3.89543 4 5 4Z" stroke="#8E8E93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <text class="info-text">{{ promotion.dateRange }}</text>
              </view>
              
              <view class="info-item">
                <svg class="info-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M16 21V19C16 16.7909 14.2091 15 12 15H5C2.79086 15 1 16.7909 1 19V21M20 8V16M16 12H24M12 7C12 9.20914 10.2091 11 8 11C5.79086 11 4 9.20914 4 7C4 4.79086 5.79086 3 8 3C10.2091 3 12 4.79086 12 7Z" stroke="#8E8E93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <text class="info-text">已参与 {{ promotion.participants }}</text>
              </view>
            </view>
            
            <!-- 活动描述 -->
            <view class="promotion-desc">
              <text class="desc-text">{{ promotion.description }}</text>
            </view>
            
            <!-- 活动操作按钮 -->
            <view class="promotion-actions">
              <view class="action-button edit" @tap.stop="editPromotion(promotion.id)">
                <svg class="action-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13M18.5 2.5C18.8978 2.10217 19.4374 1.87868 20 1.87868C20.5626 1.87868 21.1022 2.10217 21.5 2.5C21.8978 2.89782 22.1213 3.43739 22.1213 4C22.1213 4.56261 21.8978 5.10217 21.5 5.5L12 15L8 16L9 12L18.5 2.5Z" stroke="#5E5CE6" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <text class="action-text">编辑</text>
              </view>
              
              <view class="action-button" 
                :class="promotion.status === 'active' ? 'pause' : 'resume'"
                @tap.stop="togglePromotionStatus(promotion)"
              >
                <svg v-if="promotion.status === 'active'" class="action-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M10 9V15M14 9V15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#FF9F0A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <svg v-else class="action-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M10 8.08899L15.8382 11.9996L10 15.911V8.08899Z" stroke="#30D158" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#30D158" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <text class="action-text">{{ promotion.status === 'active' ? '暂停' : '恢复' }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-else>
        <view class="empty-icon">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="24" height="24" rx="12" fill="#F5F5F7"/>
            <path d="M8 11.5C8 11.5 9.5 13 12 13C14.5 13 16 11.5 16 11.5M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#8E8E93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </view>
        <text class="empty-title">暂无活动</text>
        <text class="empty-desc">您还没有创建任何营销活动，点击右上角"+"创建</text>
        <view class="empty-button" @tap="createPromotion">
          <text class="button-text">创建新活动</text>
        </view>
      </view>
      
      <!-- 浮动按钮 -->
      <view class="floating-button" @tap="createPromotion">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 5V19M5 12H19" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';

// 状态变量
const isRefreshing = ref(false);
const currentTab = ref('all');

// 标签页
const tabs = [
  { label: '全部', value: 'all' },
  { label: '进行中', value: 'active' },
  { label: '未开始', value: 'upcoming' },
  { label: '已结束', value: 'expired' }
];

// 营销活动数据
const promotions = reactive([
  {
    id: '1',
    title: '618狂欢节大促',
    description: '全场商品低至5折，限时特惠，错过等一年！',
    banner: '/static/mock/promotion1.jpg',
    dateRange: '2023-06-01 至 2023-06-18',
    status: 'upcoming',
    participants: 0
  },
  {
    id: '2',
    title: '新品上市 买二送一',
    description: '夏季新品上市，买二送一，更有机会抽取千元大奖！',
    banner: '/static/mock/promotion2.jpg',
    dateRange: '2023-05-15 至 2023-05-30',
    status: 'active',
    participants: 156
  },
  {
    id: '3',
    title: '会员专享折扣日',
    description: '每月15日会员专享额外95折优惠，多买多省！',
    banner: '/static/mock/promotion3.jpg',
    dateRange: '2023-05-15 至 2023-05-15',
    status: 'active',
    participants: 89
  },
  {
    id: '4',
    title: '五一劳动节促销',
    description: '五一假期特惠，满199减50，满399减100！',
    banner: '/static/mock/promotion4.jpg',
    dateRange: '2023-04-29 至 2023-05-03',
    status: 'expired',
    participants: 324
  }
]);

// 过滤后的活动列表
const filteredPromotions = computed(() => {
  if (currentTab.value === 'all') {
    return promotions;
  } else {
    return promotions.filter(item => item.status === currentTab.value);
  }
});

// 获取状态类名
const getStatusClass = (status) => {
  switch (status) {
    case 'active':
      return 'status-active';
    case 'upcoming':
      return 'status-upcoming';
    case 'expired':
      return 'status-expired';
    default:
      return '';
  }
};

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'active':
      return '进行中';
    case 'upcoming':
      return '未开始';
    case 'expired':
      return '已结束';
    default:
      return '';
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 创建新活动
const createPromotion = () => {
  uni.navigateTo({
    url: '/subPackages/merchant-admin/pages/promotions/create'
  });
};

// 切换标签页
const switchTab = (tab) => {
  currentTab.value = tab;
};

// 跳转到活动详情
const navigateToDetail = (id) => {
  uni.navigateTo({
    url: `/subPackages/merchant-admin/pages/promotions/detail?id=${id}`
  });
};

// 编辑活动
const editPromotion = (id) => {
  uni.navigateTo({
    url: `/subPackages/merchant-admin/pages/promotions/edit?id=${id}`
  });
};

// 切换活动状态
const togglePromotionStatus = (promotion) => {
  if (promotion.status === 'active') {
    promotion.status = 'paused';
    uni.showToast({
      title: '已暂停活动',
      icon: 'none'
    });
  } else if (promotion.status === 'paused') {
    promotion.status = 'active';
    uni.showToast({
      title: '已恢复活动',
      icon: 'none'
    });
  } else if (promotion.status === 'upcoming') {
    promotion.status = 'active';
    uni.showToast({
      title: '已提前开始活动',
      icon: 'none'
    });
  }
};

// 下拉刷新
const onRefresh = () => {
  isRefreshing.value = true;
  
  // 模拟数据刷新
  setTimeout(() => {
    isRefreshing.value = false;
    uni.showToast({
      title: '数据已更新',
      icon: 'none'
    });
  }, 1500);
};

// 生命周期
onMounted(() => {
  // 可以在这里加载初始数据
});
</script>

<style>
/* 页面根样式 */
.promotions-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f7;
  position: relative;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF9F0A, #FF3B30);
  color: white;
  padding: 48px 20px 16px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.navbar-left {
  display: flex;
  align-items: center;
  width: 40px;
}

.back-button {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24px;
  height: 24px;
}

.navbar-title {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.title-main {
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
  color: white;
}

.navbar-right {
  width: 40px;
  display: flex;
  justify-content: flex-end;
}

.add-button {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 内容区域 */
.content-container {
  flex: 1;
  padding: 20px;
  box-sizing: border-box;
  background-color: #f5f5f7;
  position: relative;
}

/* 标签页 */
.tab-container {
  display: flex;
  background-color: white;
  border-radius: 12px;
  margin-bottom: 20px;
  padding: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 8px 0;
  font-size: 14px;
  color: #8e8e93;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.tab-item.active {
  background-color: #FF9F0A;
  color: white;
}

/* 活动列表 */
.promotions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.promotion-card {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  position: relative;
}

.promotion-card.expired {
  opacity: 0.7;
}

/* 状态标签 */
.status-tag {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  z-index: 1;
}

.status-active {
  background-color: #30D158;
}

.status-upcoming {
  background-color: #5E5CE6;
}

.status-expired {
  background-color: #8E8E93;
}

/* 活动banner */
.promotion-banner {
  width: 100%;
  height: 160px;
  position: relative;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 活动内容 */
.promotion-content {
  padding: 16px;
}

.promotion-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 12px;
}

.promotion-info {
  display: flex;
  margin-bottom: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
}

.info-icon {
  margin-right: 4px;
}

.info-text {
  font-size: 14px;
  color: #8e8e93;
}

.promotion-desc {
  margin-bottom: 16px;
}

.desc-text {
  font-size: 14px;
  color: #3a3a3c;
  line-height: 1.5;
}

/* 活动操作按钮 */
.promotion-actions {
  display: flex;
  gap: 12px;
  margin-top: 12px;
}

.action-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  background-color: #f5f5f7;
  color: #5E5CE6;
}

.action-button.edit {
  border: 1px solid #5E5CE6;
}

.action-button.pause {
  border: 1px solid #FF9F0A;
  color: #FF9F0A;
}

.action-button.resume {
  border: 1px solid #30D158;
  color: #30D158;
}

.action-icon {
  margin-right: 8px;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 20px;
}

.empty-icon {
  margin-bottom: 20px;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: #8e8e93;
  text-align: center;
  margin-bottom: 24px;
}

.empty-button {
  padding: 12px 24px;
  background-color: #FF9F0A;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(255, 159, 10, 0.3);
}

/* 浮动按钮 */
.floating-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(135deg, #FF9F0A, #FF3B30);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(255, 159, 10, 0.4);
  z-index: 99;
}
</style> 