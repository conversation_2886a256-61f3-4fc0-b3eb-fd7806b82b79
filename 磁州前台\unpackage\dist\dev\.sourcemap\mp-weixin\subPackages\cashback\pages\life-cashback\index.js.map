{"version": 3, "file": "index.js", "sources": ["subPackages/cashback/pages/life-cashback/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcY2FzaGJhY2tccGFnZXNcbGlmZS1jYXNoYmFja1xpbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"life-cashback-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <custom-navbar title=\"生活返现\" :show-back=\"true\"></custom-navbar>\r\n    \r\n    <!-- 内容区域 -->\r\n    <view class=\"content-container\">\r\n      <!-- 生活服务分类 -->\r\n      <view class=\"category-section\">\r\n        <view class=\"category-grid\">\r\n          <!-- 外卖红包 -->\r\n          <view class=\"category-item\" @tap=\"navigateToService('takeout')\">\r\n            <view class=\"item-icon-container\" style=\"background-color: #FFE8E0;\">\r\n              <svg class=\"item-icon\" viewBox=\"0 0 24 24\" width=\"28\" height=\"28\">\r\n                <path fill=\"#FF6B6B\" d=\"M15.5,21L14,8H16.23L15.1,3.46L16.84,3L18.09,8H22L20.5,21H15.5M5,11H10A3,3 0 0,1 13,14H2A3,3 0 0,1 5,11M13,18A3,3 0 0,1 10,21H5A3,3 0 0,1 2,18H13M3,15H8L9.5,16.5L11,15H12A1,1 0 0,1 13,16A1,1 0 0,1 12,17H3A1,1 0 0,1 2,16A1,1 0 0,1 3,15Z\" />\r\n              </svg>\r\n            </view>\r\n            <text class=\"item-title\">领外卖红包</text>\r\n            <text class=\"item-desc\">最高返2%-5%</text>\r\n          </view>\r\n          \r\n          <!-- 打车红包 -->\r\n          <view class=\"category-item\" @tap=\"navigateToService('taxi')\">\r\n            <view class=\"item-icon-container\" style=\"background-color: #FFF2D6;\">\r\n              <svg class=\"item-icon\" viewBox=\"0 0 24 24\" width=\"28\" height=\"28\">\r\n                <path fill=\"#FFA726\" d=\"M5,11L6.5,6.5H17.5L19,11M17.5,16A1.5,1.5 0 0,1 16,14.5A1.5,1.5 0 0,1 17.5,13A1.5,1.5 0 0,1 19,14.5A1.5,1.5 0 0,1 17.5,16M6.5,16A1.5,1.5 0 0,1 5,14.5A1.5,1.5 0 0,1 6.5,13A1.5,1.5 0 0,1 8,14.5A1.5,1.5 0 0,1 6.5,16M18.92,6C18.72,5.42 18.16,5 17.5,5H6.5C5.84,5 5.28,5.42 5.08,6L3,12V20A1,1 0 0,0 4,21H5A1,1 0 0,0 6,20V19H18V20A1,1 0 0,0 19,21H20A1,1 0 0,0 21,20V12L18.92,6Z\" />\r\n              </svg>\r\n            </view>\r\n            <text class=\"item-title\">领打车红包</text>\r\n            <text class=\"item-desc\">最高返2.4%-5%</text>\r\n          </view>\r\n          \r\n          <!-- 电影票 -->\r\n          <view class=\"category-item\" @tap=\"navigateToService('movie')\">\r\n            <view class=\"item-icon-container\" style=\"background-color: #FFE0EC;\">\r\n              <svg class=\"item-icon\" viewBox=\"0 0 24 24\" width=\"28\" height=\"28\">\r\n                <path fill=\"#E91E63\" d=\"M18,9H16V7H18M18,13H16V11H18M18,17H16V15H18M8,9H6V7H8M8,13H6V11H8M8,17H6V15H8M18,3V5H16V3H8V5H6V3H4V21H6V19H8V21H16V19H18V21H20V3H18Z\" />\r\n              </svg>\r\n            </view>\r\n            <text class=\"item-title\">电影票8折起</text>\r\n            <text class=\"item-desc\">返10%</text>\r\n          </view>\r\n          \r\n          <!-- 快递 -->\r\n          <view class=\"category-item\" @tap=\"navigateToService('express')\">\r\n            <view class=\"item-icon-container\" style=\"background-color: #E3F1FF;\">\r\n              <svg class=\"item-icon\" viewBox=\"0 0 24 24\" width=\"28\" height=\"28\">\r\n                <path fill=\"#2196F3\" d=\"M3,14H5V20H19V14H21V21A1,1 0 0,1 20,22H4A1,1 0 0,1 3,21V14M17,4H7V2H17V4M17.5,5L12,10.5L6.5,5H17.5M20,6.4L17.9,8.5L15.5,6.1L16.9,4.7L20,7.8V6.4M5.93,4.7L7.33,6.1L4.93,8.5L2.83,6.4V7.8L5.93,4.7Z\" />\r\n              </svg>\r\n            </view>\r\n            <text class=\"item-title\">寄快递返现</text>\r\n            <text class=\"item-desc\">返15%</text>\r\n          </view>\r\n          \r\n          <!-- 酒店 -->\r\n          <view class=\"category-item\" @tap=\"navigateToService('hotel')\">\r\n            <view class=\"item-icon-container\" style=\"background-color: #E8F5E9;\">\r\n              <svg class=\"item-icon\" viewBox=\"0 0 24 24\" width=\"28\" height=\"28\">\r\n                <path fill=\"#4CAF50\" d=\"M19,7H11V14H3V5H1V20H3V17H21V20H23V11A4,4 0 0,0 19,7M7,13A3,3 0 0,0 10,10A3,3 0 0,0 7,7A3,3 0 0,0 4,10A3,3 0 0,0 7,13Z\" />\r\n              </svg>\r\n            </view>\r\n            <text class=\"item-title\">酒店订房</text>\r\n            <text class=\"item-desc\">返6%</text>\r\n          </view>\r\n          \r\n          <!-- 机票 -->\r\n          <view class=\"category-item\" @tap=\"navigateToService('flight')\">\r\n            <view class=\"item-icon-container\" style=\"background-color: #E0F7FA;\">\r\n              <svg class=\"item-icon\" viewBox=\"0 0 24 24\" width=\"28\" height=\"28\">\r\n                <path fill=\"#00BCD4\" d=\"M21,16V14L13,9V3.5A1.5,1.5 0 0,0 11.5,2A1.5,1.5 0 0,0 10,3.5V9L2,14V16L10,13.5V19L8,20.5V22L11.5,21L15,22V20.5L13,19V13.5L21,16Z\" />\r\n              </svg>\r\n            </view>\r\n            <text class=\"item-title\">机票预订</text>\r\n            <text class=\"item-desc\">返3%</text>\r\n          </view>\r\n          \r\n          <!-- 火车票 -->\r\n          <view class=\"category-item\" @tap=\"navigateToService('train')\">\r\n            <view class=\"item-icon-container\" style=\"background-color: #FFF3E0;\">\r\n              <svg class=\"item-icon\" viewBox=\"0 0 24 24\" width=\"28\" height=\"28\">\r\n                <path fill=\"#FF9800\" d=\"M18,10H6V5H18M12,17C10.89,17 10,16.1 10,15C10,13.89 10.89,13 12,13A2,2 0 0,1 14,15A2,2 0 0,1 12,17M4,15.5A3.5,3.5 0 0,0 7.5,19L6,20.5V21H18V20.5L16.5,19A3.5,3.5 0 0,0 20,15.5V5C20,1.5 16.42,1 12,1C7.58,1 4,1.5 4,5V15.5Z\" />\r\n              </svg>\r\n            </view>\r\n            <text class=\"item-title\">火车票</text>\r\n            <text class=\"item-desc\">返2%</text>\r\n          </view>\r\n          \r\n          <!-- 会员充值 -->\r\n          <view class=\"category-item\" @tap=\"navigateToService('vip')\">\r\n            <view class=\"item-icon-container\" style=\"background-color: #F3E5F5;\">\r\n              <svg class=\"item-icon\" viewBox=\"0 0 24 24\" width=\"28\" height=\"28\">\r\n                <path fill=\"#9C27B0\" d=\"M12,8H4A2,2 0 0,0 2,10V14A2,2 0 0,0 4,16H5V20A1,1 0 0,0 6,21H8A1,1 0 0,0 9,20V16H12L17,20V4L12,8M21.5,12C21.5,13.71 20.54,15.26 19,16V8C20.53,8.75 21.5,10.3 21.5,12Z\" />\r\n              </svg>\r\n            </view>\r\n            <text class=\"item-title\">会员充值</text>\r\n            <text class=\"item-desc\">3.6元起</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 优惠券区域 -->\r\n      <view class=\"coupon-section\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">热门优惠券</text>\r\n          <view class=\"section-more\" @tap=\"goToCoupons\">\r\n            <text>更多</text>\r\n            <svg class=\"arrow-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n              <path fill=\"#999999\" d=\"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z\" />\r\n            </svg>\r\n          </view>\r\n        </view>\r\n        \r\n        <scroll-view scroll-x class=\"coupons-scroll\" show-scrollbar=\"false\">\r\n          <view class=\"coupons-container\">\r\n            <!-- 优惠券1 -->\r\n            <view class=\"coupon-item\" @tap=\"navigateToService('coupon', 1)\">\r\n              <view class=\"coupon-content\">\r\n                <text class=\"coupon-platform\">淘宝-搜了么</text>\r\n                <text class=\"coupon-desc\">免费奶茶喝到爽</text>\r\n                <text class=\"coupon-subdesc\">下单再返3元</text>\r\n              </view>\r\n              <view class=\"coupon-price\">\r\n                <text class=\"price-value\">15元</text>\r\n                <text class=\"price-tag\">券</text>\r\n              </view>\r\n            </view>\r\n            \r\n            <!-- 优惠券2 -->\r\n            <view class=\"coupon-item\" @tap=\"navigateToService('coupon', 2)\">\r\n              <view class=\"coupon-content\">\r\n                <text class=\"coupon-platform\">美团外卖</text>\r\n                <text class=\"coupon-desc\">无门槛红包</text>\r\n                <text class=\"coupon-subdesc\">满20元可用</text>\r\n              </view>\r\n              <view class=\"coupon-price\">\r\n                <text class=\"price-value\">10元</text>\r\n                <text class=\"price-tag\">券</text>\r\n              </view>\r\n            </view>\r\n            \r\n            <!-- 优惠券3 -->\r\n            <view class=\"coupon-item\" @tap=\"navigateToService('coupon', 3)\">\r\n              <view class=\"coupon-content\">\r\n                <text class=\"coupon-platform\">滴滴出行</text>\r\n                <text class=\"coupon-desc\">打车立减</text>\r\n                <text class=\"coupon-subdesc\">全国通用</text>\r\n              </view>\r\n              <view class=\"coupon-price\">\r\n                <text class=\"price-value\">8元</text>\r\n                <text class=\"price-tag\">券</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </scroll-view>\r\n      </view>\r\n      \r\n      <!-- 热门活动 -->\r\n      <view class=\"activity-section\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">热门活动</text>\r\n        </view>\r\n        \r\n        <view class=\"activity-list\">\r\n          <!-- 活动1 -->\r\n          <view class=\"activity-item\" @tap=\"navigateToActivity(1)\">\r\n            <image class=\"activity-image\" src=\"/static/images/cashback/activity-1.png\" mode=\"aspectFill\"></image>\r\n            <view class=\"activity-info\">\r\n              <text class=\"activity-title\">外卖周末狂欢</text>\r\n              <text class=\"activity-desc\">周末下单满减+返利双重优惠</text>\r\n              <view class=\"activity-tags\">\r\n                <text class=\"tag\">满减</text>\r\n                <text class=\"tag\">返利</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 活动2 -->\r\n          <view class=\"activity-item\" @tap=\"navigateToActivity(2)\">\r\n            <image class=\"activity-image\" src=\"/static/images/cashback/activity-2.png\" mode=\"aspectFill\"></image>\r\n            <view class=\"activity-info\">\r\n              <text class=\"activity-title\">电影节优惠季</text>\r\n              <text class=\"activity-desc\">购票低至5折，再享10%返利</text>\r\n              <view class=\"activity-tags\">\r\n                <text class=\"tag\">折扣</text>\r\n                <text class=\"tag\">返利</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport CustomNavbar from '../../components/CustomNavbar.vue';\r\n\r\nexport default {\r\n  components: {\r\n    CustomNavbar\r\n  },\r\n  data() {\r\n    return {\r\n      \r\n    };\r\n  },\r\n  onLoad() {\r\n    // 设置页面不显示系统导航栏\r\n    uni.setNavigationBarColor({\r\n      frontColor: '#ffffff',\r\n      backgroundColor: '#9C27B0'\r\n    });\r\n  },\r\n  methods: {\r\n    navigateToService(type, id = 0) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/cashback/pages/life-service/index?type=${type}${id ? '&id=' + id : ''}`\r\n      });\r\n    },\r\n    goToCoupons() {\r\n      uni.showToast({\r\n        title: '优惠券功能正在开发中',\r\n        icon: 'none',\r\n        duration: 2000\r\n      });\r\n    },\r\n    navigateToActivity(id) {\r\n      uni.showToast({\r\n        title: '活动详情功能正在开发中',\r\n        icon: 'none',\r\n        duration: 2000\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.life-cashback-container {\r\n  min-height: 100vh;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.content-container {\r\n  padding-top: calc(var(--status-bar-height) + 44px);\r\n  padding-bottom: 20px;\r\n}\r\n\r\n.category-section {\r\n  margin: 16px;\r\n  background-color: #FFFFFF;\r\n  border-radius: 16px;\r\n  padding: 16px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.category-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(4, 1fr);\r\n  gap: 16px;\r\n}\r\n\r\n.category-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  \r\n  .item-icon-container {\r\n    width: 52px;\r\n    height: 52px;\r\n    border-radius: 12px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-bottom: 8px;\r\n  }\r\n  \r\n  .item-title {\r\n    font-size: 14px;\r\n    color: #333333;\r\n    margin-bottom: 4px;\r\n    text-align: center;\r\n  }\r\n  \r\n  .item-desc {\r\n    font-size: 12px;\r\n    color: #9C27B0;\r\n    text-align: center;\r\n  }\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  \r\n  .section-title {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #333333;\r\n  }\r\n  \r\n  .section-more {\r\n    display: flex;\r\n    align-items: center;\r\n    color: #999999;\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n.coupon-section {\r\n  margin: 16px;\r\n  background-color: #FFFFFF;\r\n  border-radius: 16px;\r\n  padding: 16px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.coupons-scroll {\r\n  width: 100%;\r\n  white-space: nowrap;\r\n}\r\n\r\n.coupons-container {\r\n  display: inline-flex;\r\n  padding-bottom: 8px;\r\n}\r\n\r\n.coupon-item {\r\n  width: 220px;\r\n  height: 100px;\r\n  background: linear-gradient(135deg, #F5F0FF 0%, #EDE7F6 100%);\r\n  border-radius: 12px;\r\n  padding: 16px;\r\n  margin-right: 12px;\r\n  display: flex;\r\n  position: relative;\r\n  overflow: hidden;\r\n  \r\n  &:last-child {\r\n    margin-right: 0;\r\n  }\r\n  \r\n  &::after {\r\n    content: '';\r\n    position: absolute;\r\n    right: -10px;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    width: 20px;\r\n    height: 20px;\r\n    border-radius: 50%;\r\n    background-color: #FFFFFF;\r\n    box-shadow: inset 0 0 0 2px #F5F0FF;\r\n  }\r\n  \r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    left: -10px;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    width: 20px;\r\n    height: 20px;\r\n    border-radius: 50%;\r\n    background-color: #FFFFFF;\r\n    box-shadow: inset 0 0 0 2px #F5F0FF;\r\n  }\r\n  \r\n  .coupon-content {\r\n    flex: 1;\r\n    \r\n    .coupon-platform {\r\n      font-size: 14px;\r\n      color: #333333;\r\n      margin-bottom: 6px;\r\n      display: block;\r\n    }\r\n    \r\n    .coupon-desc {\r\n      font-size: 16px;\r\n      color: #9C27B0;\r\n      font-weight: 600;\r\n      margin-bottom: 4px;\r\n      display: block;\r\n    }\r\n    \r\n    .coupon-subdesc {\r\n      font-size: 12px;\r\n      color: #666666;\r\n      display: block;\r\n    }\r\n  }\r\n  \r\n  .coupon-price {\r\n    background-color: #FF5252;\r\n    border-radius: 20px;\r\n    padding: 4px 8px;\r\n    height: fit-content;\r\n    display: flex;\r\n    align-items: center;\r\n    \r\n    .price-value {\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n      color: #FFFFFF;\r\n    }\r\n    \r\n    .price-tag {\r\n      font-size: 12px;\r\n      color: #FFFFFF;\r\n      background-color: rgba(255, 255, 255, 0.3);\r\n      border-radius: 10px;\r\n      padding: 0 4px;\r\n      margin-left: 2px;\r\n    }\r\n  }\r\n}\r\n\r\n.activity-section {\r\n  margin: 16px;\r\n  background-color: #FFFFFF;\r\n  border-radius: 16px;\r\n  padding: 16px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.activity-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.activity-item {\r\n  display: flex;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  background-color: #F9F9F9;\r\n  \r\n  .activity-image {\r\n    width: 100px;\r\n    height: 100px;\r\n    object-fit: cover;\r\n  }\r\n  \r\n  .activity-info {\r\n    flex: 1;\r\n    padding: 12px;\r\n    \r\n    .activity-title {\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n      color: #333333;\r\n      margin-bottom: 6px;\r\n      display: block;\r\n    }\r\n    \r\n    .activity-desc {\r\n      font-size: 14px;\r\n      color: #666666;\r\n      margin-bottom: 8px;\r\n      display: block;\r\n    }\r\n    \r\n    .activity-tags {\r\n      display: flex;\r\n      gap: 8px;\r\n      \r\n      .tag {\r\n        font-size: 12px;\r\n        color: #9C27B0;\r\n        background-color: rgba(156, 39, 176, 0.1);\r\n        border-radius: 10px;\r\n        padding: 2px 8px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/cashback/pages/life-cashback/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAmMA,MAAK,eAAgB,MAAW;AAEhC,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;EAGR;AAAA,EACD,SAAS;AAEPA,kBAAAA,MAAI,sBAAsB;AAAA,MACxB,YAAY;AAAA,MACZ,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACF;AAAA,EACD,SAAS;AAAA,IACP,kBAAkB,MAAM,KAAK,GAAG;AAC9BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,uDAAuD,IAAI,GAAG,KAAK,SAAS,KAAK,EAAE;AAAA,MAC1F,CAAC;AAAA,IACF;AAAA,IACD,cAAc;AACZA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACZ,CAAC;AAAA,IACF;AAAA,IACD,mBAAmB,IAAI;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzOA,GAAG,WAAW,eAAe;"}