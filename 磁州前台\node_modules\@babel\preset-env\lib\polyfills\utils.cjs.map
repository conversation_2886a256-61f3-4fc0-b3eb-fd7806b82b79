{"version": 3, "names": ["exports", "getImportSource", "node", "specifiers", "length", "source", "value", "getRequireSource", "type", "expression", "callee", "name", "arguments", "isPolyfillSource"], "sources": ["../../src/polyfills/utils.cjs"], "sourcesContent": ["// TODO(Babel 8) Remove this file\nif (process.env.BABEL_8_BREAKING) {\n  throw new Error(\n    \"Internal Babel error: This file should only be loaded in Babel 7\",\n  );\n}\n\nexports.getImportSource = function ({ node }) {\n  if (node.specifiers.length === 0) return node.source.value;\n};\n\nexports.getRequireSource = function ({ node }) {\n  if (node.type !== \"ExpressionStatement\") return;\n  const { expression } = node;\n  if (\n    expression.type === \"CallExpression\" &&\n    expression.callee.type === \"Identifier\" &&\n    expression.callee.name === \"require\" &&\n    expression.arguments.length === 1 &&\n    expression.arguments[0].type === \"StringLiteral\"\n  ) {\n    return expression.arguments[0].value;\n  }\n};\n\nexports.isPolyfillSource = function (source) {\n  return source === \"@babel/polyfill\" || source === \"core-js\";\n};\n"], "mappings": ";AAOAA,OAAO,CAACC,eAAe,GAAG,UAAU;EAAEC;AAAK,CAAC,EAAE;EAC5C,IAAIA,IAAI,CAACC,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE,OAAOF,IAAI,CAACG,MAAM,CAACC,KAAK;AAC5D,CAAC;AAEDN,OAAO,CAACO,gBAAgB,GAAG,UAAU;EAAEL;AAAK,CAAC,EAAE;EAC7C,IAAIA,IAAI,CAACM,IAAI,KAAK,qBAAqB,EAAE;EACzC,MAAM;IAAEC;EAAW,CAAC,GAAGP,IAAI;EAC3B,IACEO,UAAU,CAACD,IAAI,KAAK,gBAAgB,IACpCC,UAAU,CAACC,MAAM,CAACF,IAAI,KAAK,YAAY,IACvCC,UAAU,CAACC,MAAM,CAACC,IAAI,KAAK,SAAS,IACpCF,UAAU,CAACG,SAAS,CAACR,MAAM,KAAK,CAAC,IACjCK,UAAU,CAACG,SAAS,CAAC,CAAC,CAAC,CAACJ,IAAI,KAAK,eAAe,EAChD;IACA,OAAOC,UAAU,CAACG,SAAS,CAAC,CAAC,CAAC,CAACN,KAAK;EACtC;AACF,CAAC;AAEDN,OAAO,CAACa,gBAAgB,GAAG,UAAUR,MAAM,EAAE;EAC3C,OAAOA,MAAM,KAAK,iBAAiB,IAAIA,MAAM,KAAK,SAAS;AAC7D,CAAC", "ignoreList": []}