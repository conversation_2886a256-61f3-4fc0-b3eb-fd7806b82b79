"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  __name: "partner-fans",
  setup(__props) {
    const totalFans = common_vendor.ref(128);
    const newFans = common_vendor.ref(5);
    const activeFans = common_vendor.ref(42);
    const currentTab = common_vendor.ref("all");
    const searchKeyword = common_vendor.ref("");
    const fansList = common_vendor.ref([]);
    const page = common_vendor.ref(1);
    common_vendor.ref(10);
    const hasMore = common_vendor.ref(true);
    const isLoading = common_vendor.ref(false);
    const showShareModal = common_vendor.ref(false);
    const filteredFans = common_vendor.computed(() => {
      let result = [...fansList.value];
      if (currentTab.value === "new") {
        result = result.filter((item) => isToday(item.joinTime));
      } else if (currentTab.value === "active") {
        result = result.filter((item) => item.activeLevel >= 3);
      } else if (currentTab.value === "valuable") {
        result = result.filter((item) => item.contribution > 0);
      }
      if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase();
        result = result.filter(
          (item) => item.nickname.toLowerCase().includes(keyword)
        );
      }
      return result;
    });
    const emptyText = common_vendor.computed(() => {
      if (searchKeyword.value) {
        return "没有找到相关粉丝";
      } else if (currentTab.value === "new") {
        return "今日暂无新增粉丝";
      } else if (currentTab.value === "active") {
        return "暂无活跃粉丝";
      } else if (currentTab.value === "valuable") {
        return "暂无消费粉丝";
      } else {
        return "暂无粉丝，去邀请好友吧";
      }
    });
    common_vendor.onMounted(() => {
      loadData();
    });
    const switchTab = (tab) => {
      currentTab.value = tab;
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const searchFans = () => {
      page.value = 1;
      hasMore.value = true;
      loadData();
    };
    const clearSearch = () => {
      searchKeyword.value = "";
      searchFans();
    };
    const loadData = () => {
      isLoading.value = true;
      setTimeout(() => {
        const mockData = generateMockFans();
        if (page.value === 1) {
          fansList.value = mockData;
        } else {
          fansList.value = [...fansList.value, ...mockData];
        }
        hasMore.value = page.value < 3;
        isLoading.value = false;
      }, 500);
    };
    const loadMore = () => {
      if (isLoading.value || !hasMore.value)
        return;
      page.value++;
      loadData();
    };
    const sendMessage = (fan) => {
      common_vendor.index.navigateTo({
        url: `/pages/message/chat?userId=${fan.id}&nickname=${fan.nickname}`
      });
    };
    const shareToFriends = () => {
      showShareModal.value = true;
    };
    const closeShareModal = () => {
      showShareModal.value = false;
    };
    const shareToWechat = () => {
      common_vendor.index.showToast({
        title: "已分享到微信",
        icon: "success"
      });
      closeShareModal();
    };
    const shareToMoments = () => {
      common_vendor.index.showToast({
        title: "已分享到朋友圈",
        icon: "success"
      });
      closeShareModal();
    };
    const navigateToPoster = () => {
      common_vendor.index.navigateTo({
        url: "/pages/my/partner-poster"
      });
      closeShareModal();
    };
    const copyLink = () => {
      common_vendor.index.setClipboardData({
        data: "https://example.com/register?ref=" + Math.random().toString(36).slice(2),
        success: () => {
          common_vendor.index.showToast({
            title: "链接已复制",
            icon: "success"
          });
          closeShareModal();
        }
      });
    };
    const formatDate = (dateStr) => {
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      return `${year}-${month}-${day}`;
    };
    const isToday = (dateStr) => {
      const today = /* @__PURE__ */ new Date();
      const date = new Date(dateStr);
      return today.getFullYear() === date.getFullYear() && today.getMonth() === date.getMonth() && today.getDate() === date.getDate();
    };
    const generateMockFans = () => {
      const mockFans = [];
      const nicknames = ["奔跑的小猪", "小小科技迷", "梦想家", "爱笑的眼睛", "春风十里", "南方姑娘", "山川湖海", "时光漫步"];
      const avatars = [
        "/static/images/avatar-1.png",
        "/static/images/avatar-2.png",
        "/static/images/avatar-3.png",
        "/static/images/avatar-4.png",
        "/static/images/avatar-5.png",
        "/static/images/avatar-6.png"
      ];
      for (let i = 0; i < 10; i++) {
        const now = /* @__PURE__ */ new Date();
        const randomDays = Math.floor(Math.random() * 30);
        const joinTime = new Date(now.getTime() - randomDays * 24 * 60 * 60 * 1e3);
        const activeLevel = Math.floor(Math.random() * 5) + 1;
        const contribution = randomDays === 0 ? 0 : Math.floor(Math.random() * 1e3);
        const isVip = Math.random() > 0.7;
        mockFans.push({
          id: "user_" + Date.now() + i,
          nickname: nicknames[Math.floor(Math.random() * nicknames.length)],
          avatar: avatars[Math.floor(Math.random() * avatars.length)],
          joinTime,
          activeLevel,
          contribution,
          isVip
        });
      }
      return mockFans;
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: common_vendor.t(totalFans.value),
        d: common_vendor.t(newFans.value),
        e: common_vendor.t(activeFans.value),
        f: currentTab.value === "all" ? 1 : "",
        g: common_vendor.o(($event) => switchTab("all")),
        h: currentTab.value === "new" ? 1 : "",
        i: common_vendor.o(($event) => switchTab("new")),
        j: currentTab.value === "active" ? 1 : "",
        k: common_vendor.o(($event) => switchTab("active")),
        l: currentTab.value === "valuable" ? 1 : "",
        m: common_vendor.o(($event) => switchTab("valuable")),
        n: common_vendor.o(searchFans),
        o: searchKeyword.value,
        p: common_vendor.o(($event) => searchKeyword.value = $event.detail.value),
        q: searchKeyword.value
      }, searchKeyword.value ? {
        r: common_vendor.o(clearSearch)
      } : {}, {
        s: filteredFans.value.length > 0
      }, filteredFans.value.length > 0 ? {
        t: common_vendor.f(filteredFans.value, (item, index, i0) => {
          return common_vendor.e({
            a: item.avatar,
            b: item.isVip
          }, item.isVip ? {} : {}, {
            c: common_vendor.t(item.nickname),
            d: isToday(item.joinTime)
          }, isToday(item.joinTime) ? {} : {}, {
            e: common_vendor.t(formatDate(item.joinTime)),
            f: common_vendor.t(item.contribution),
            g: common_vendor.o(($event) => sendMessage(item), index),
            h: index
          });
        })
      } : {
        v: common_assets._imports_1$20,
        w: common_vendor.t(emptyText.value),
        x: common_vendor.o(shareToFriends)
      }, {
        y: filteredFans.value.length > 0 && hasMore.value
      }, filteredFans.value.length > 0 && hasMore.value ? common_vendor.e({
        z: !isLoading.value
      }, !isLoading.value ? {
        A: common_vendor.o(loadMore)
      } : {}) : {}, {
        B: filteredFans.value.length > 0 && !hasMore.value
      }, filteredFans.value.length > 0 && !hasMore.value ? {} : {}, {
        C: showShareModal.value
      }, showShareModal.value ? {
        D: common_vendor.o(closeShareModal),
        E: common_assets._imports_2$19,
        F: common_vendor.o(shareToWechat),
        G: common_assets._imports_3$15,
        H: common_vendor.o(shareToMoments),
        I: common_assets._imports_4$10,
        J: common_vendor.o(navigateToPoster),
        K: common_assets._imports_5$10,
        L: common_vendor.o(copyLink),
        M: common_vendor.o(closeShareModal)
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-c08f443b"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/partner/pages/partner-fans.js.map
