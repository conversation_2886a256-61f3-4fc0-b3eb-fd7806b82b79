{"version": 3, "file": "detail.js", "sources": ["subPackages/news/pages/detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbmV3c1xwYWdlc1xkZXRhaWwudnVl"], "sourcesContent": ["<template>\n\t<view class=\"detail-container\">\n\t\t<!-- 顶部区域 -->\n\t\t<view class=\"detail-header card-style\">\n\t\t\t<view class=\"header-top\">\n\t\t\t\t<view class=\"category-badge\">{{newsInfo.category}}</view>\n\t\t\t<text class=\"detail-title\">{{newsInfo.title}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"detail-meta\">\n\t\t\t\t<view class=\"meta-item source-item\">\n\t\t\t\t\t<image src=\"/static/images/tabbar/作者.png\" class=\"meta-icon\"></image>\n\t\t\t\t\t<text>{{newsInfo.source}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"meta-item time-item\">\n\t\t\t\t\t<image src=\"/static/images/tabbar/时间.png\" class=\"meta-icon\"></image>\n\t\t\t\t\t<text>{{newsInfo.time}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"meta-item view-count\">\n\t\t\t\t\t<image src=\"/static/images/tabbar/浏览.png\" class=\"meta-icon\"></image>\n\t\t\t\t\t<text>{{newsInfo.views}}浏览</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 添加真正的悬浮导航键到页面根元素 -->\n\t\t<view class=\"info-nav-btn\" @click=\"navigateTo('/pages/index/index')\">\n\t\t\t<view class=\"info-hot-tag\">火热</view>\n\t\t\t<view class=\"info-btn-content\">\n\t\t\t\t<view class=\"info-icon-group\">\n\t\t\t\t\t<view class=\"info-icon-item\"></view>\n\t\t\t\t\t<view class=\"info-icon-item\"></view>\n\t\t\t\t\t<view class=\"info-icon-item\"></view>\n\t\t\t\t\t<view class=\"info-icon-item\"></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-nav-text\">便民</view>\n\t\t\t\t<view class=\"info-nav-text\">信息</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 添加悬浮推广按钮 -->\n\t\t<FloatPromotionButton \n\t\t\t@click=\"showPromotionTool\" \n\t\t\t:position=\"{right: '30rpx', bottom: '280rpx'}\"\n\t\t\tsize=\"90rpx\"\n\t\t/>\n\t\t\n\t\t<!-- 内容区域 -->\n\t\t<view class=\"detail-content card-style\">\n\t\t\t<!-- 主图区域 -->\n\t\t\t<view class=\"content-image-container\" v-if=\"newsInfo.image\">\n\t\t\t\t<image :src=\"newsInfo.image\" class=\"content-image\" mode=\"widthFix\"></image>\n\t\t\t\t<view class=\"image-caption\" v-if=\"newsInfo.imageCaption\">{{newsInfo.imageCaption}}</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 正文区域 -->\n\t\t\t<view class=\"text-container\">\n\t\t\t<text class=\"content-text\">{{newsInfo.content}}</text>\n\t\t</view>\n\t\t\n\t\t\t<!-- 来源标记 -->\n\t\t\t<view class=\"content-footer\">\n\t\t\t\t<text class=\"content-source\">作者：{{newsInfo.source}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 互动区域 -->\n\t\t<view class=\"detail-stats card-style\">\n\t\t\t<view class=\"stat-item\" @click=\"toggleLike\">\n\t\t\t\t<image :src=\"newsInfo.isLiked ? '/static/images/tabbar/已点赞选中.png' : '/static/images/tabbar/点赞.png'\" class=\"stat-icon\"></image>\n\t\t\t\t<text class=\"stat-text\" :class=\"{ 'active': newsInfo.isLiked }\">{{newsInfo.likes}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"stat-item\" @click=\"scrollToComment\">\n\t\t\t\t<image src=\"/static/images/tabbar/评论.png\" class=\"stat-icon\"></image>\n\t\t\t\t<text class=\"stat-text\">{{newsInfo.comments}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"stat-item\" @click=\"toggleCollect\">\n\t\t\t\t<image :src=\"newsInfo.isCollected ? '/static/images/tabbar/已收藏选中.png' : '/static/images/tabbar/收藏.png'\" class=\"stat-icon\"></image>\n\t\t\t\t<text class=\"stat-text\" :class=\"{ 'active': newsInfo.isCollected }\">收藏</text>\n\t\t\t</view>\n\t\t\t<button class=\"share-btn stat-item\" open-type=\"share\" @click=\"beforeShare\">\n\t\t\t\t<image src=\"/static/images/tabbar/分享.png\" class=\"stat-icon\"></image>\n\t\t\t\t<text class=\"stat-text\">分享</text>\n\t\t\t</button>\n\t\t</view>\n\t\t\n\t\t<!-- 评论区 -->\n\t\t<view class=\"comment-section card-style\" id=\"comment-section\" v-if=\"newsInfo.commentList && newsInfo.commentList.length > 0\">\n\t\t\t<view class=\"section-title\">\n\t\t\t\t<text class=\"title-text\">热门评论</text>\n\t\t\t\t<text class=\"comment-count\">({{newsInfo.comments}})</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 评论列表 -->\n\t\t\t<view class=\"comment-list\">\n\t\t\t\t<view class=\"comment-item\" v-for=\"(comment, index) in newsInfo.commentList\" :key=\"index\">\n\t\t\t\t\t<image :src=\"comment.avatar\" class=\"comment-avatar\" mode=\"aspectFill\"></image>\n\t\t\t\t\t<view class=\"comment-content\">\n\t\t\t\t\t\t<!-- 评论头部 -->\n\t\t\t\t\t\t<view class=\"comment-header\">\n\t\t\t\t\t\t<text class=\"comment-name\">{{comment.name}}</text>\n\t\t\t\t\t\t\t<!-- 徽章 -->\n\t\t\t\t\t\t\t<view class=\"user-badge\" v-if=\"comment.isOfficial\">官方</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 评论正文 -->\n\t\t\t\t\t\t<text class=\"comment-text\">{{comment.content}}</text>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 评论底部 -->\n\t\t\t\t\t\t<view class=\"comment-footer\">\n\t\t\t\t\t\t\t<text class=\"comment-time\">{{comment.time}}</text>\n\t\t\t\t\t\t\t<view class=\"comment-actions\">\n\t\t\t\t\t\t\t\t<view class=\"action-item reply-btn\" @click=\"showReplyInput(index, comment.name)\">\n\t\t\t\t\t\t\t\t\t<image src=\"/static/images/tabbar/回复.png\" class=\"action-icon\"></image>\n\t\t\t\t\t\t\t\t\t<text>回复</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"action-item like-btn\" @click=\"toggleCommentLike(index)\">\n\t\t\t\t\t\t\t\t\t<image :src=\"comment.isLiked ? '/static/images/tabbar/已点赞选中.png' : '/static/images/tabbar/点赞.png'\" class=\"action-icon\"></image>\n\t\t\t\t\t\t\t\t<text :class=\"{ 'active': comment.isLiked }\">{{comment.likes}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 回复列表 -->\n\t\t\t\t\t\t<view class=\"reply-list\" v-if=\"comment.replies && comment.replies.length > 0\">\n\t\t\t\t\t\t\t<view class=\"reply-item\" v-for=\"(reply, replyIndex) in comment.showAllReplies ? comment.replies : comment.replies.slice(0, 2)\" :key=\"replyIndex\" @click=\"showReplyInput(index, reply.name)\">\n\t\t\t\t\t\t\t\t<view class=\"reply-content\">\n\t\t\t\t\t\t\t\t\t<text class=\"reply-name\">{{reply.name}}</text>\n\t\t\t\t\t\t\t\t\t<view class=\"reply-badge\" v-if=\"reply.isOfficial\">官方</view>\n\t\t\t\t\t\t\t\t\t<text v-if=\"reply.replyTo\" class=\"reply-to\">回复</text>\n\t\t\t\t\t\t\t\t\t<text v-if=\"reply.replyTo\" class=\"reply-name\">@{{reply.replyTo}}</text>\n\t\t\t\t\t\t\t\t\t<text class=\"reply-text\">：{{reply.content}}</text>\n\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"reply-footer\">\n\t\t\t\t\t\t\t\t\t<text class=\"reply-time\">{{reply.time}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t\t\t\t\t\t<!-- 查看更多回复 -->\n\t\t\t\t\t\t\t<view class=\"view-more-replies\" v-if=\"comment.replies.length > 2 && !comment.showAllReplies\" @click.stop=\"toggleReplies(index)\">\n\t\t\t\t\t\t\t\t查看更多{{comment.replies.length - 2}}条回复 <text class=\"more-icon\">∨</text>\n\t\t</view>\n\t\t\t\t\t\t\t<view class=\"view-more-replies\" v-if=\"comment.replies.length > 2 && comment.showAllReplies\" @click.stop=\"toggleReplies(index)\">\n\t\t\t\t\t\t\t\t收起回复 <text class=\"more-icon\">∧</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 查看更多评论 -->\n\t\t\t<view class=\"view-more-comments\" @click=\"loadMoreComments\" v-if=\"hasMoreComments\">\n\t\t\t\t<text>查看更多评论</text>\n\t\t\t\t<image src=\"/static/images/tabbar/更多.png\" class=\"more-comments-icon\"></image>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 无评论提示 -->\n\t\t<view class=\"no-comment card-style\" v-else>\n\t\t\t<image src=\"/static/images/tabbar/暂无评论.png\" class=\"no-comment-icon\"></image>\n\t\t\t<text class=\"no-comment-text\">暂无评论，快来发表你的看法吧</text>\n\t\t</view>\n\t\t\n\t\t<!-- 评论输入框 -->\n\t\t<view class=\"comment-input-container card-style\">\n\t\t\t<view class=\"comment-input-wrapper\">\n\t\t\t\t<input \n\t\t\t\t\ttype=\"text\" \n\t\t\t\t\tclass=\"comment-input\" \n\t\t\t\t\t:placeholder=\"replyPlaceholder\" \n\t\t\t\t\tv-model=\"commentText\" \n\t\t\t\t\t:focus=\"inputFocus\"\n\t\t\t\t\tconfirm-type=\"send\"\n\t\t\t\t\t@confirm=\"submitComment\"\n\t\t\t\t\t@focus=\"onInputFocus\"\n\t\t\t\t\t@blur=\"onInputBlur\"\n\t\t\t\t/>\n\t\t\t\t<view class=\"send-btn\" :class=\"{ 'active': commentText.trim().length > 0 }\" @click=\"submitComment\">发送</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 磁县同城信息模块 -->\n\t\t<view class=\"related-info-section\">\n\t\t\t<view class=\"related-info-title\">\n\t\t\t\t<view class=\"premium-bar\"></view>\n\t\t\t\t<text class=\"related-title-text\">同城信息推荐</text>\n\t\t\t</view>\n\t\t\t<InfoList \n\t\t\t\t:allInfoList=\"filteredInfoList\" \n\t\t\t\t:toppedInfoList=\"filteredToppedInfoList\" \n\t\t\t\t:adBanner=\"adBanner\" \n\t\t\t\t@tab-change=\"handleInfoTabChange\" />\n\t\t</view>\n\t\t\n\t\t<!-- 底部导航栏 -->\n\t\t<view class=\"bottom-tabbar\">\n\t\t\t<view class=\"tabbar-item\" @click=\"navigateTo('/pages/index/index')\">\n\t\t\t\t<image class=\"tabbar-icon\" src=\"/static/images/tabbar/home-grey.png\" mode=\"aspectFit\"></image>\n\t\t\t\t<text class=\"tabbar-text\">首页</text>\n\t\t\t</view>\n\t\t\t<view class=\"tabbar-item\" @click=\"navigateTo('/pages/business/business')\">\n\t\t\t\t<image class=\"tabbar-icon\" src=\"/static/images/tabbar/shop-grey.png\" mode=\"aspectFit\"></image>\n\t\t\t\t<text class=\"tabbar-text\">商圈</text>\n\t\t\t</view>\n\t\t\t<view class=\"tabbar-item\" @click=\"navigateTo('/pages/publish/publish')\">\n\t\t\t\t<image class=\"tabbar-icon\" src=\"/static/images/tabbar/edit-grey.png\" mode=\"aspectFit\"></image>\n\t\t\t\t<text class=\"tabbar-text\">发布</text>\n\t\t\t</view>\n\t\t\t<view class=\"tabbar-item\" @click=\"navigateTo('/pages/group/group')\">\n\t\t\t\t<image class=\"tabbar-icon\" src=\"/static/images/tabbar/chat-grey.png\" mode=\"aspectFit\"></image>\n\t\t\t\t<text class=\"tabbar-text\">进群</text>\n\t\t\t</view>\n\t\t\t<view class=\"tabbar-item\" @click=\"navigateTo('/pages/my/my')\">\n\t\t\t\t<image class=\"tabbar-icon\" src=\"/static/images/tabbar/user-grey.png\" mode=\"aspectFit\"></image>\n\t\t\t\t<text class=\"tabbar-text\">我的</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 回到顶部按钮 -->\n\t\t<view class=\"back-to-top\" v-if=\"showBackToTop\" @click=\"scrollToTop\">\n\t\t\t<image src=\"/static/images/tabbar/arrow-up.png\" class=\"top-icon\"></image>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted, nextTick } from 'vue';\nimport InfoList from '@/components/index/InfoList.vue';\nimport FloatPromotionButton from '@/components/FloatPromotionButton.vue';\nimport mockApi from '@/mock/api';\nimport { infoCategories, visibleCategories } from '@/mock/info/categories';\nimport { infoListData } from '@/static/data/infoListData.js';\n\n\texport default {\n\tcomponents: {\n\t\tInfoList,\n\t\tFloatPromotionButton\n\t},\n  setup() {\n    // 响应式状态\n    const commentText = ref('');\n    const currentCommentIndex = ref(-1);\n    const replyToUser = ref('');\n    const inputBottom = ref(0);\n    const inputFocus = ref(false);\n    const showBackToTop = ref(false);\n    const hasMoreComments = ref(true);\n    const statusBarHeight = ref(20);\n    const newsId = ref(0);\n\t\t\t\n\t\t\t// 同城信息相关\n    const currentInfoTab = ref(0);\n    const isTabsFixed = ref(false);\n    const adBanner = ref({\n\t\t\t\timage: '/static/images/ad-banner.jpg',\n\t\t\t\turl: '/pages/activity/detail?id=3'\n    });\n    \n    // 新闻详情数据\n    const newsInfo = reactive({\n\t\t\t\t\tid: 1,\n      title: '',\n      time: '',\n      source: '',\n      category: '',\n      image: '',\n      imageCaption: '',\n\t\t\t\t\tisCollected: false,\n      content: '',\n      views: 0,\n      likes: 0,\n\t\t\t\t\tisLiked: false,\n      comments: 0,\n      commentList: []\n    });\n    \n    // 置顶和普通信息列表\n    const toppedInfoList = ref([]);\n    const allInfoList = ref([]);\n    \n    // 其他状态\n    const _tabsPlaceholderTop = ref(null);\n    const _currentScrollTop = ref(0);\n    \n    // 计算属性\n    const replyPlaceholder = computed(() => {\n      return replyToUser.value ? `回复 @${replyToUser.value}` : '写下你的评论...';\n    });\n    \n\t\t// 根据当前标签过滤信息列表\n    const filteredInfoList = computed(() => {\n\t\t\t// 如果是第一个标签（最新发布），返回所有信息\n      if (currentInfoTab.value === 0) {\n        return allInfoList.value;\n\t\t\t}\n\t\t\t\n\t\t\t// 否则，根据选中的分类标签过滤信息\n      const selectedCategory = infoCategories[currentInfoTab.value - 1];\n\t\t\t\n\t\t\t// 返回匹配分类的信息\n      return allInfoList.value.filter(item => item.category === selectedCategory);\n    });\n    \n\t\t// 根据当前标签过滤置顶信息列表\n    const filteredToppedInfoList = computed(() => {\n\t\t\t// 如果是第一个标签（最新发布），返回所有置顶信息\n      if (currentInfoTab.value === 0) {\n        return toppedInfoList.value;\n\t\t\t}\n\t\t\t\n\t\t\t// 否则，根据选中的分类标签过滤置顶信息\n      const selectedCategory = infoCategories[currentInfoTab.value - 1];\n\t\t\t\n\t\t\t// 返回匹配分类的置顶信息\n      return toppedInfoList.value.filter(item => item.category === selectedCategory);\n    });\n    \n    // 生命周期钩子 - 页面加载\n    function onPageLoad(options) {\n\t\t\t// 记录资讯ID\n\t\t\tif (options.id) {\n        newsId.value = options.id;\n        console.log('资讯ID:', newsId.value);\n\t\t\t}\n\t\t\t\n\t\t\t// 获取状态栏高度\n\t\t\tuni.getSystemInfo({\n\t\t\t\tsuccess: (res) => {\n          statusBarHeight.value = res.statusBarHeight;\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\t// 初始化页面数据\n      fetchNewsDetail(newsId.value);\n      fetchInfoData();\n\t\t\t\n\t\t\t// 获取标签位置\n\t\t\tsetTimeout(() => {\n        getTabsPosition();\n\t\t\t}, 500);\n    }\n    \n    // 获取新闻详情数据\n    function fetchNewsDetail(id) {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '加载中'\n\t\t\t\t});\n\t\t\t\t\n      // 使用模拟API获取新闻详情\n      mockApi.news.getDetail(id).then(data => {\n        // 将获取的数据复制到响应式对象中\n        Object.assign(newsInfo, data);\n\t\t\t\t\tuni.hideLoading();\n        \n        // 增加浏览量记录\n        recordView(id);\n      }).catch(err => {\n        uni.hideLoading();\n\t\t\t\t\t\t\t\tuni.showToast({\n          title: '获取资讯失败',\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t});\n        console.error('获取资讯失败:', err);\n      });\n    }\n    \n    // 获取信息数据\n    function fetchInfoData() {\n      // 从静态数据文件获取数据，与首页保持一致\n      \n      // 获取置顶信息\n      toppedInfoList.value = infoListData.toppedInfoList.map(item => {\n        // 确保每个项目都有必要的字段\n        return {\n          ...item,\n          images: item.images || [],\n          tags: item.tags || [],\n          views: item.views || 0,\n          likes: item.likes || 0,\n          comments: item.comments || 0\n        };\n      });\n      \n      // 获取普通信息\n      allInfoList.value = infoListData.allInfoList.map(item => {\n        // 确保每个项目都有必要的字段\n        return {\n          ...item,\n          images: item.images || [],\n          tags: item.tags || [],\n          views: item.views || 0,\n          likes: item.likes || 0,\n          comments: item.comments || 0\n        };\n      });\n      \n      // 获取广告横幅\n      adBanner.value = {\n        image: '/static/images/banner/ad-banner.jpg',\n        url: '/pages/ad/detail',\n        title: '广告横幅'\n      };\n    }\n\t\t\t\n\t\t\t// 记录浏览量\n    function recordView(id) {\n\t\t\t\tconsole.log('记录浏览量，ID:', id);\n      // 实际应用中，这里应该调用后端API记录浏览量\n    }\n\t\t\t\n\t\t\t// 点赞功能\n    function toggleLike() {\n\t\t\t\t// 添加触感反馈\n\t\t\t\tuni.vibrateShort({\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tconsole.log('振动成功');\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n      newsInfo.isLiked = !newsInfo.isLiked;\n      newsInfo.likes += newsInfo.isLiked ? 1 : -1;\n\t\t\t\t\n\t\t\t\t// 显示交互反馈\n\t\t\t\tuni.showToast({\n        title: newsInfo.isLiked ? '已点赞' : '已取消点赞',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 1500\n\t\t\t\t});\n    }\n\t\t\t\n\t\t\t// 收藏功能\n    function toggleCollect() {\n\t\t\t\t// 添加触感反馈\n\t\t\t\tuni.vibrateShort({\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tconsole.log('振动成功');\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n      newsInfo.isCollected = !newsInfo.isCollected;\n\t\t\t\t\n\t\t\t\t// 显示交互反馈\n\t\t\t\tuni.showToast({\n        title: newsInfo.isCollected ? '已收藏' : '已取消收藏',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 1500\n\t\t\t\t});\n    }\n\t\t\t\n\t\t\t// 分享功能\n    function beforeShare() {\n\t\t\t\t// 添加触感反馈\n\t\t\t\tuni.vibrateShort();\n\t\t\t\tconsole.log('准备分享...');\n\t\t\t\t// 可以在这里设置分享内容，但实际分享参数在onShareAppMessage中定义\n    }\n\t\t\t\n\t\t\t// 显示推广工具\n\t\t\tfunction showPromotionTool() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/subPackages/promotion/pages/promotion-tool?type=content&id=${newsId.value}`\n\t\t\t\t});\n    }\n\t\t\t\n\t\t\t// 滚动到评论区\n    function scrollToComment() {\n\t\t\t\tuni.createSelectorQuery()\n\t\t\t\t\t.select('#comment-section')\n\t\t\t\t\t.boundingClientRect(data => {\n\t\t\t\t\t\tif (data) {\n\t\t\t\t\t\t\tuni.pageScrollTo({\n\t\t\t\t\t\t\t\tscrollTop: data.top,\n\t\t\t\t\t\t\t\tduration: 300\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 如果没有评论，滚动到底部\n\t\t\t\t\t\t\tuni.pageScrollTo({\n\t\t\t\t\t\t\t\tscrollTop: 9999,\n\t\t\t\t\t\t\t\tduration: 300\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\t.exec();\n    }\n\t\t\t\n\t\t\t// 切换评论点赞状态\n    function toggleCommentLike(index) {\n\t\t\t\t// 添加触感反馈\n\t\t\t\tuni.vibrateShort({\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tconsole.log('振动成功');\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n      const comment = newsInfo.commentList[index];\n\t\t\t\tcomment.isLiked = !comment.isLiked;\n\t\t\t\tcomment.likes += comment.isLiked ? 1 : -1;\n    }\n\t\t\t\n\t\t\t// 显示回复输入框\n    function showReplyInput(commentIndex, userName) {\n      currentCommentIndex.value = commentIndex;\n      replyToUser.value = userName;\n      inputFocus.value = true;\n\t\t\t\t\n\t\t\t\t// 滚动到评论输入框\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.createSelectorQuery()\n\t\t\t\t\t\t.select('.comment-input-container')\n          .boundingClientRect(data => {\n\t\t\t\t\t\t\tif (data) {\n\t\t\t\t\t\t\t\tuni.pageScrollTo({\n\t\t\t\t\t\t\t\t\tscrollTop: data.top,\n\t\t\t\t\t\t\t\t\tduration: 300\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.exec();\n\t\t\t\t}, 200);\n    }\n\t\t\t\n\t\t\t// 重置回复状态\n    function resetReplyState() {\n      currentCommentIndex.value = -1;\n      replyToUser.value = '';\n      commentText.value = '';\n      inputFocus.value = false;\n    }\n\t\t\t\n\t\t\t// 提交评论或回复\n    function submitComment() {\n      if (!commentText.value.trim()) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入评论内容',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 添加触感反馈\n\t\t\t\tuni.vibrateShort({\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tconsole.log('振动成功');\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 如果是回复评论\n      if (currentCommentIndex.value >= 0 && replyToUser.value) {\n        const comment = newsInfo.commentList[currentCommentIndex.value];\n\t\t\t\t\t\n\t\t\t\t\t// 如果没有replies数组，创建一个\n\t\t\t\t\tif (!comment.replies) {\n\t\t\t\t\t\tcomment.replies = [];\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 添加回复\n\t\t\t\t\tcomment.replies.unshift({\n\t\t\t\t\t\tname: '游客',\n          content: commentText.value,\n\t\t\t\t\t\ttime: '刚刚',\n          replyTo: replyToUser.value,\n\t\t\t\t\t\tisOfficial: false\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 增加评论总数\n        newsInfo.comments++;\n\t\t\t\t\t\n\t\t\t\t\t// 自动展开回复\n\t\t\t\t\tcomment.showAllReplies = true;\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '回复成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 滚动到该评论\n\t\t\t\t\tsetTimeout(() => {\n          const commentSelector = `.comment-item:nth-child(${currentCommentIndex.value + 1})`;\n\t\t\t\t\t\tuni.createSelectorQuery()\n\t\t\t\t\t\t\t.select(commentSelector)\n            .boundingClientRect(data => {\n\t\t\t\t\t\t\t\tif (data) {\n\t\t\t\t\t\t\t\t\tuni.pageScrollTo({\n\t\t\t\t\t\t\t\t\t\tscrollTop: data.top,\n\t\t\t\t\t\t\t\t\t\tduration: 300\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t.exec();\n\t\t\t\t\t}, 300);\n\t\t\t\t} else {\n\t\t\t\t\t// 添加新评论\n        newsInfo.commentList.unshift({\n\t\t\t\t\tavatar: '/static/images/tabbar/user-blue.png',\n\t\t\t\t\tname: '游客',\n          content: commentText.value,\n\t\t\t\t\ttime: '刚刚',\n\t\t\t\t\tlikes: 0,\n\t\t\t\t\t\tisLiked: false,\n\t\t\t\t\t\tisOfficial: false,\n\t\t\t\t\t\tshowAllReplies: false,\n\t\t\t\t\t\treplies: []\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 增加评论总数\n        newsInfo.comments++;\n\t\t\t\t\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '评论成功',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 滚动到评论区顶部\n\t\t\t\t\tsetTimeout(() => {\n          scrollToComment();\n\t\t\t\t\t}, 300);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 重置状态\n      resetReplyState();\n    }\n\t\t\t\n\t\t\t// 切换回复列表展开/收起\n    function toggleReplies(index) {\n\t\t\t\t// 添加触感反馈\n\t\t\t\tuni.vibrateShort({\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tconsole.log('振动成功');\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n      newsInfo.commentList[index].showAllReplies = !newsInfo.commentList[index].showAllReplies;\n    }\n\t\t\t\n\t\t\t// 加载更多评论\n    function loadMoreComments() {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '加载中'\n\t\t\t\t});\n\t\t\t\t\n      // 使用模拟API获取更多评论\n      mockApi.news.getMoreComments().then(comments => {\n        // 添加到评论列表\n        newsInfo.commentList.push(...comments);\n\t\t\t\t\t\n\t\t\t\t\t// 测试数据，模拟没有更多评论了\n        hasMoreComments.value = false;\n\t\t\t\t\t\n        uni.hideLoading();\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '已加载全部评论',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n      }).catch(err => {\n        uni.hideLoading();\n        console.error('加载更多评论失败:', err);\n      });\n    }\n\t\t\t\n\t\t\t// 输入框获取焦点\n    function onInputFocus(e) {\n      inputBottom.value = e.detail.height || 0;\n    }\n\t\t\t\n\t\t\t// 输入框失去焦点\n    function onInputBlur() {\n      inputBottom.value = 0;\n    }\n\t\t\t\n\t\t\t// 页面导航\n    function navigateTo(url) {\n\t\t\t\tuni.switchTab({\n\t\t\t\t\turl: url,\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('导航失败:', err);\n\t\t\t\t\t\t// 如果不是tabBar页面，使用普通导航\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: url\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n    }\n\t\t\n    // 导航到信息详情页\n    function navigateToInfoDetail(item) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/publish/${item.pageType || 'info-detail'}?id=${item.id}`\n\t\t\t});\n    }\n\t\t\n\t\t// 处理信息标签切换\n    function handleInfoTabChange(tab) {\n\t\t\tconsole.log('切换到标签:', tab);\n      currentInfoTab.value = tab.index;\n    }\n    \n    // 获取标签位置\n    function getTabsPosition() {\n      const query = uni.createSelectorQuery();\n      query.select('#tabsContainer').boundingClientRect(data => {\n        if (data) {\n          const tabsPosition = data;\n          const tabsHeight = data.height || 0;\n        }\n      }).exec();\n    }\n    \n    // 获取标签占位符位置\n    function getTabsPlaceholderPosition() {\n      nextTick(() => {\n        const query = uni.createSelectorQuery();\n        query.select('#tabsPlaceholder').boundingClientRect(data => {\n          if (data) {\n            _tabsPlaceholderTop.value = (_currentScrollTop.value || 0) + data.top;\n            console.log('标签占位符位置:', _tabsPlaceholderTop.value);\n          }\n        }).exec();\n      });\n    }\n    \n    // 回到顶部\n    function scrollToTop() {\n      uni.pageScrollTo({\n        scrollTop: 0,\n        duration: 300\n      });\n    }\n    \n    // 页面滚动事件处理\n    function onPageScroll(e) {\n      // 检查是否显示回到顶部按钮\n      showBackToTop.value = e.scrollTop > 500;\n    }\n    \n    // 页面下拉刷新\n    function onPullDownRefresh() {\n      console.log('下拉刷新');\n      // 刷新当前资讯\n      fetchNewsDetail(newsId.value);\n      // 停止下拉刷新动画\n      setTimeout(() => {\n        uni.stopPullDownRefresh();\n      }, 1000);\n    }\n    \n    // 页面触底事件\n    function onReachBottom() {\n      console.log('页面触底');\n      if (hasMoreComments.value) {\n        loadMoreComments();\n      }\n    }\n    \n    // 分享到微信好友\n    function onShareAppMessage() {\n      return {\n        title: newsInfo.title,\n        path: `/pages/subPackages/news/detail/index?id=${newsId.value}`\n      };\n    }\n    \n    // 分享到朋友圈\n    function onShareTimeline() {\n      return {\n        title: newsInfo.title,\n        query: `id=${newsInfo.id}`,\n        path: `/pages/subPackages/news/detail/index?id=${newsId.value}`,\n        imageUrl: newsInfo.image\n      };\n    }\n    \n    return {\n      // 数据\n      commentText,\n      currentCommentIndex,\n      replyToUser,\n      inputBottom,\n      inputFocus,\n      showBackToTop,\n      hasMoreComments,\n      statusBarHeight,\n      newsId,\n      currentInfoTab,\n      isTabsFixed,\n      visibleCategories,\n      infoCategories,\n      adBanner,\n      newsInfo,\n      toppedInfoList,\n      allInfoList,\n      _tabsPlaceholderTop,\n      _currentScrollTop,\n      \n      // 计算属性\n      replyPlaceholder,\n      filteredInfoList,\n      filteredToppedInfoList,\n      \n      // 方法\n      onPageLoad,\n      fetchNewsDetail,\n      recordView,\n      toggleLike,\n      toggleCollect,\n      beforeShare,\n      scrollToComment,\n      toggleCommentLike,\n      showReplyInput,\n      resetReplyState,\n      submitComment,\n      toggleReplies,\n      loadMoreComments,\n      onInputFocus,\n      onInputBlur,\n      navigateTo,\n      navigateToInfoDetail,\n      handleInfoTabChange,\n      getTabsPosition,\n      getTabsPlaceholderPosition,\n      scrollToTop,\n      onPageScroll,\n      onPullDownRefresh,\n      onReachBottom,\n      onShareAppMessage,\n      onShareTimeline,\n      showPromotionTool\n    };\n  },\n  // uni-app 页面生命周期钩子\n  onLoad(options) {\n    this.onPageLoad(options);\n  },\n  onReady() {\n    console.log('页面渲染完成');\n  },\n  onShow() {\n    console.log('页面显示');\n  },\n  onHide() {\n    console.log('页面隐藏');\n  },\n  onUnload() {\n    console.log('页面卸载');\n  },\n  onPullDownRefresh() {\n    this.onPullDownRefresh();\n  },\n  onReachBottom() {\n    this.onReachBottom();\n  },\n  onPageScroll(e) {\n    this.onPageScroll(e);\n  },\n  onShareAppMessage() {\n    return this.onShareAppMessage();\n  },\n  onShareTimeline() {\n    return this.onShareTimeline();\n\t\t}\n\t}\n</script>\n\n<style>\n\t.detail-container {\n\t\tmin-height: 100vh;\n\t\tbackground-color: #f5f5f5;\n\t\tpadding-bottom: 120rpx; /* 为底部导航栏留出空间 */\n\t\tfont-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif;\n\t}\n\t\n\t/* 卡片通用样式 */\n\t.card-style {\n\t\tbackground: #fff;\n\t\tborder-radius: 16rpx;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n\t\tmargin: 20rpx 24rpx;\n\t\toverflow: hidden;\n\t\tposition: relative;\n\t}\n\t\n\t/* 相关信息区域样式 */\n\t.related-info-section {\n\t\tmargin: 20rpx 0 30rpx 0;\n\t\tbackground: #fff;\n\t}\n\t\n\t.related-info-title {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 24rpx;\n\t\tmargin: 0 24rpx;\n\t\tborder-bottom: 1rpx solid #f0f0f0;\n\t}\n\t\n\t.premium-bar {\n\t\twidth: 6rpx;\n\t\theight: 36rpx;\n\t\tbackground: linear-gradient(180deg, #0052CC, #1677FF);\n\t\tborder-radius: 3rpx;\n\t\tmargin-right: 16rpx;\n\t}\n\t\n\t.related-title-text {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t\tletter-spacing: 0.5rpx;\n\t}\n\t\n\t/* 顶部区域样式 */\n\t.detail-header {\n\t\tpadding: 30rpx;\n\t\tposition: relative;\n\t\toverflow: visible;\n\t}\n\t\n\t.header-top {\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.category-badge {\n\t\tdisplay: inline-block;\n\t\tfont-size: 22rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #0052CC;\n\t\tbackground: rgba(0, 82, 204, 0.1);\n\t\tpadding: 6rpx 18rpx;\n\t\tborder-radius: 20rpx;\n\t\tmargin-bottom: 16rpx;\n\t\tbox-shadow: 0 2rpx 6rpx rgba(0, 82, 204, 0.2);\n\t}\n\t\n\t.detail-title {\n\t\tfont-size: 42rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-bottom: 26rpx;\n\t\tline-height: 1.4;\n\t\tletter-spacing: 0.5rpx;\n\t}\n\t\n\t.detail-meta {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tflex-wrap: wrap;\n\t\tcolor: #888;\n\t\tborder-top: 1rpx solid rgba(0, 0, 0, 0.05);\n\t\tpadding-top: 20rpx;\n\t\tposition: relative;\n\t}\n\t\n\t.meta-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-right: 24rpx;\n\t\tfont-size: 24rpx;\n\t}\n\t\n\t.meta-icon {\n\t\twidth: 28rpx;\n\t\theight: 28rpx;\n\t\tmargin-right: 8rpx;\n\t\topacity: 0.8;\n\t}\n\t\n\t/* 导航按钮样式 - 便民信息 */\n\t.home-nav-btn {\n\t\tdisplay: none; /* 隐藏旧的导航按钮 */\n\t}\n\t\n\t/* 真正的悬浮导航键样式 */\n\t.info-nav-btn {\n\t\tposition: fixed;\n\t\tright: 30rpx;\n\t\ttop: 150rpx;\n\t\tbackground: #ffffff;\n\t\tborder-radius: 45rpx;\n\t\tbox-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);\n\t\tpadding: 0;\n\t\twidth: 80rpx;\n\t\theight: 160rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\toverflow: visible;\n\t\tz-index: 100;\n\t\ttransition: all 0.3s;\n\t}\n\t\n\t.info-nav-btn:active {\n\t\ttransform: scale(0.95);\n\t\tbox-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.15);\n\t}\n\t\n\t.info-hot-tag {\n\t\tbackground: #ff4d4f;\n\t\tcolor: #fff;\n\t\tfont-size: 16rpx;\n\t\tpadding: 4rpx 12rpx;\n\t\tposition: absolute;\n\t\ttop: -15rpx;\n\t\tright: -20rpx;\n\t\tborder-radius: 10rpx;\n\t\tfont-weight: bold;\n\t\ttransform: rotate(15deg);\n\t\tbox-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);\n\t\tz-index: 999;\n\t}\n\t\n\t.info-btn-content {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding-top: 22rpx;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\t\n\t.info-icon-group {\n\t\twidth: 44rpx;\n\t\theight: 44rpx;\n\t\tdisplay: grid;\n\t\tgrid-template-columns: 1fr 1fr;\n\t\tgrid-template-rows: 1fr 1fr;\n\t\tgap: 4rpx;\n\t\tmargin-bottom: 8rpx;\n\t}\n\t\n\t.info-icon-item {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground-color: #0052CC;\n\t\tborder-radius: 2rpx;\n\t}\n\t\n\t.info-icon-item:first-child {\n\t\tbackground-color: #ff4d4f;\n\t}\n\t\n\t.info-nav-text {\n\t\tfont-size: 20rpx;\n\t\tcolor: #333;\n\t\tfont-weight: 700;\n\t\tline-height: 1.2;\n\t\tmargin-bottom: 4rpx;\n\t}\n\t\n\t/* 内容区域样式 */\n\t.detail-content {\n\t\tpadding: 30rpx;\n\t}\n\t\n\t.content-image-container {\n\t\tposition: relative;\n\t\tmargin: 0 -30rpx 30rpx;\n\t\toverflow: hidden;\n\t}\n\t\n\t.content-image {\n\t\twidth: 100%;\n\t}\n\t\n\t.image-caption {\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbackground: linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0));\n\t\tcolor: #fff;\n\t\tfont-size: 24rpx;\n\t\tpadding: 24rpx 20rpx 16rpx;\n\t\ttext-align: center;\n\t}\n\t\n\t.text-container {\n\t\tmargin-bottom: 30rpx;\n\t}\n\t\n\t.content-text {\n\t\tfont-size: 32rpx;\n\t\tcolor: #333;\n\t\tline-height: 1.8;\n\t\twhite-space: pre-wrap;\n\t\tletter-spacing: 0.5rpx;\n\t}\n\t\n\t.content-footer {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\tpadding-top: 30rpx;\n\t\tborder-top: 1rpx dashed rgba(0, 0, 0, 0.1);\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t}\n\t\n\t.content-source {\n\t\tfont-style: italic;\n\t}\n\t\n\t.report-text {\n\t\tcolor: #666;\n\t\tbackground: rgba(0, 0, 0, 0.05);\n\t\tpadding: 6rpx 16rpx;\n\t\tborder-radius: 20rpx;\n\t}\n\t\n\t/* 互动区域样式 */\n\t.detail-stats {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-around;\n\t\tpadding: 20rpx 30rpx;\n\t}\n\t\n\t.stat-item {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tpadding: 14rpx 20rpx;\n\t\tflex: 1;\n\t\tposition: relative;\n\t\ttransition: transform 0.2s;\n\t}\n\t\n\t.stat-item:active {\n\t\ttransform: scale(0.95);\n\t}\n\t\n\t.stat-icon {\n\t\twidth: 48rpx;\n\t\theight: 48rpx;\n\t\tmargin-bottom: 8rpx;\n\t}\n\t\n\t.stat-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t\tfont-weight: 500;\n\t}\n\t\n\t.stat-text.active {\n\t\tcolor: #0052CC;\n\t\tfont-weight: 600;\n\t}\n\t\n\t.stat-text.active {\n\t\tcolor: #0052CC;\n\t\tfont-weight: 600;\n\t}\n\t\n\t.share-btn {\n\t\tbackground: transparent;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tpadding: 0;\n\t\tline-height: 1;\n\t\tborder: none;\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t\tfont-weight: 500;\n\t}\n\t\n\t.share-btn::after {\n\t\tdisplay: none;\n\t\tborder: none;\n\t\tbackground: none;\n\t}\n\t\n\t/* 评论区样式 */\n\t.comment-section {\n\t\tpadding: 30rpx;\n\t}\n\t\n\t.section-title {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 30rpx;\n\t\tposition: relative;\n\t\tpadding-left: 20rpx;\n\t}\n\t\n\t.title-text {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\t\n\t.comment-count {\n\t\tfont-size: 26rpx;\n\t\tcolor: #999;\n\t\tmargin-left: 10rpx;\n\t\tposition: relative;\n\t\ttop: 2rpx;\n\t}\n\t\n\t.comment-list {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: 30rpx;\n\t}\n\t\n\t.comment-item {\n\t\tdisplay: flex;\n\t\tpadding-bottom: 24rpx;\n\t\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.05);\n\t}\n\t\n\t.comment-item:last-child {\n\t\tpadding-bottom: 0;\n\t\tborder-bottom: none;\n\t}\n\t\n\t.comment-avatar {\n\t\twidth: 72rpx;\n\t\theight: 72rpx;\n\t\tborder-radius: 50%;\n\t\tmargin-right: 20rpx;\n\t\tbackground-color: #f5f5f5;\n\t\tborder: 1rpx solid #f0f0f0;\n\t\tflex-shrink: 0;\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n\t}\n\t\n\t.comment-content {\n\t\tflex: 1;\n\t\toverflow: hidden;\n\t}\n\t\n\t.comment-header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\t\n\t.comment-name {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-right: 12rpx;\n\t}\n\t\n\t.user-badge {\n\t\tbackground: linear-gradient(to right, #0052CC, #2196F3);\n\t\tcolor: #fff;\n\t\tfont-size: 20rpx;\n\t\tpadding: 2rpx 10rpx;\n\t\tborder-radius: 10rpx;\n\t\tfont-weight: normal;\n\t\tbox-shadow: 0 2rpx 6rpx rgba(0, 82, 204, 0.2);\n\t}\n\t\n\t.comment-text {\n\t\tfont-size: 30rpx;\n\t\tcolor: #333;\n\t\tline-height: 1.6;\n\t\tmargin: 12rpx 0;\n\t\tword-break: break-all;\n\t}\n\t\n\t.comment-footer {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tmargin: 8rpx 0;\n\t}\n\t\n\t.comment-time {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t}\n\t\n\t.comment-actions {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\t\n\t.action-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-left: 30rpx;\n\t\tpadding: 4rpx 8rpx;\n\t}\n\t\n\t.action-icon {\n\t\twidth: 30rpx;\n\t\theight: 30rpx;\n\t\tmargin-right: 8rpx;\n\t}\n\t\n\t.reply-btn text, .like-btn text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t}\n\t\n\t.like-btn text.active {\n\t\tcolor: #0052CC;\n\t}\n\t\n\t/* 回复区域 */\n\t.reply-list {\n\t\tmargin: 20rpx 0 10rpx 0;\n\t\tpadding: 20rpx;\n\t\tbackground-color: #f8f9fc;\n\t\tborder-radius: 12rpx;\n\t\tborder: 1rpx solid rgba(0, 0, 0, 0.03);\n\t}\n\t\n\t.reply-item {\n\t\tmargin-bottom: 16rpx;\n\t\tpadding-bottom: 16rpx;\n\t\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.05);\n\t}\n\t\n\t.reply-item:last-child {\n\t\tmargin-bottom: 0;\n\t\tpadding-bottom: 0;\n\t\tborder-bottom: none;\n\t}\n\t\n\t.reply-content {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\talign-items: center;\n\t}\n\t\n\t.reply-name {\n\t\tfont-size: 26rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #0052CC;\n\t}\n\t\n\t.reply-badge {\n\t\tbackground: linear-gradient(to right, #0052CC, #2196F3);\n\t\tcolor: #fff;\n\t\tfont-size: 18rpx;\n\t\tpadding: 0 8rpx;\n\t\tborder-radius: 8rpx;\n\t\tmargin: 0 6rpx;\n\t\tline-height: 1.5;\n\t}\n\t\n\t.reply-to {\n\t\tfont-size: 26rpx;\n\t\tcolor: #999;\n\t\tmargin: 0 6rpx;\n\t}\n\t\n\t.reply-text {\n\t\tfont-size: 26rpx;\n\t\tcolor: #333;\n\t\tline-height: 1.5;\n\t\tword-break: break-all;\n\t}\n\t\n\t.reply-footer {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\tmargin-top: 8rpx;\n\t}\n\t\n\t.reply-time {\n\t\tfont-size: 22rpx;\n\t\tcolor: #999;\n\t}\n\t\n\t.view-more-replies {\n\t\tfont-size: 24rpx;\n\t\tcolor: #0052CC;\n\t\ttext-align: center;\n\t\tpadding: 12rpx 0;\n\t\tmargin-top: 10rpx;\n\t\tbackground: rgba(0, 82, 204, 0.05);\n\t\tborder-radius: 8rpx;\n\t}\n\t\n\t.more-icon {\n\t\tdisplay: inline-block;\n\t\ttransform: scale(0.7);\n\t}\n\t\n\t.view-more-comments {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: 20rpx 0;\n\t\tmargin-top: 10rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #0052CC;\n\t\tbackground: rgba(0, 82, 204, 0.05);\n\t\tborder-radius: 8rpx;\n\t}\n\t\n\t.more-comments-icon {\n\t\twidth: 24rpx;\n\t\theight: 24rpx;\n\t\tmargin-left: 8rpx;\n\t}\n\t\n\t/* 无评论提示 */\n\t.no-comment {\n\t\tpadding: 60rpx 0;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\t\n\t.no-comment-icon {\n\t\twidth: 120rpx;\n\t\theight: 120rpx;\n\t\tmargin-bottom: 20rpx;\n\t\topacity: 0.6;\n\t}\n\t\n\t.no-comment-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999;\n\t}\n\t\n\t/* 评论输入框 */\n\t.comment-input-container {\n\t\tpadding: 20rpx;\n\t\tpadding-bottom: calc(20rpx + constant(safe-area-inset-bottom) / 2);\n\t\tpadding-bottom: calc(20rpx + env(safe-area-inset-bottom) / 2);\n\t\tmargin-bottom: 100rpx; /* 为底部导航栏留出空间 */\n\t}\n\t\n\t.comment-input-wrapper {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbackground-color: #f5f7fa;\n\t\tborder-radius: 36rpx;\n\t\tpadding: 6rpx 10rpx 6rpx 30rpx;\n\t\tbox-shadow: inset 0 2rpx 6rpx rgba(0, 0, 0, 0.05);\n\t}\n\t\n\t.comment-input {\n\t\tflex: 1;\n\t\theight: 68rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tbackground-color: transparent;\n\t}\n\t\n\t.send-btn {\n\t\theight: 60rpx;\n\t\tline-height: 60rpx;\n\t\tbackground-color: #eaecf0;\n\t\tcolor: #999;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 500;\n\t\ttext-align: center;\n\t\tborder-radius: 30rpx;\n\t\tpadding: 0 24rpx;\n\t\tmargin-left: 10rpx;\n\t\ttransition: all 0.2s;\n\t}\n\t\n\t.send-btn.active {\n\t\tbackground: linear-gradient(to right, #0052CC, #2196F3);\n\t\tcolor: #fff;\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 82, 204, 0.3);\n\t}\n\t\n\t/* 底部导航栏样式 */\n\t.bottom-tabbar {\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\theight: 100rpx;\n\t\tpadding-bottom: calc(constant(safe-area-inset-bottom) / 2);\n\t\tpadding-bottom: calc(env(safe-area-inset-bottom) / 2);\n\t\tbackground-color: #ffffff;\n\t\tdisplay: flex;\n\t\tjustify-content: space-around;\n\t\talign-items: center;\n\t\tbox-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.08);\n\t\tz-index: 100;\n\t\tborder-top: 1rpx solid rgba(0, 0, 0, 0.05);\n\t\ttransform: translateY(-5rpx); /* 向上移动一点距离 */\n\t}\n\t\n\t.tabbar-item {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tflex: 1;\n\t\theight: 100%;\n\t\tpadding: 10rpx 0;\n\t}\n\t\n\t.tabbar-icon {\n\t\twidth: 44rpx;\n\t\theight: 44rpx;\n\t\tmargin-bottom: 6rpx;\n\t}\n\t\n\t.tabbar-text {\n\t\tfont-size: 22rpx;\n\t\tcolor: #7A7E83;\n\t\tline-height: 1;\n\t}\n\t\n\t/* 回到顶部按钮 */\n\t.back-to-top {\n\t\tposition: fixed;\n\t\tright: 30rpx;\n\t\tbottom: 240rpx; /* 增加距离底部的高度 */\n\t\twidth: 50rpx;\n\t\theight: 50rpx;\n\t\tbackground: rgba(255, 255, 255, 0.95);\n\t\tborder-radius: 50%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);\n\t\tz-index: 90;\n\t\topacity: 0.9;\n\t\ttransition: all 0.3s;\n\t}\n\t\n\t.back-to-top:active {\n\t\ttransform: scale(0.9);\n\t}\n\t\n\t.top-icon {\n\t\twidth: 40rpx;\n\t\theight: 40rpx;\n\t}\n\t\n\t/* 全部信息模块样式 */\n\t.all-info {\n\t\tmargin-bottom: 30rpx;\n\t\tpadding: 0 0 22rpx 0;\n\t\tmargin-left: 25rpx;\n\t\tmargin-right: 25rpx;\n\t\toverflow: hidden;\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 16rpx;\n\t\tbox-shadow: 0 6rpx 15rpx rgba(0, 0, 0, 0.05);\n\t}\n\t\n\t.all-info-title-row {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin: 26rpx 26rpx 20rpx 26rpx;\n\t\tpadding-top: 10rpx;\n\t\tborder-bottom: 0.5px solid rgba(230, 233, 240, 0.8);\n\t\tpadding-bottom: 20rpx;\n\t}\n\t\n\t.all-info-icon {\n\t\twidth: 36rpx;\n\t\theight: 36rpx;\n\t\tmargin-right: 12rpx;\n\t\tvertical-align: middle;\n\t}\n\t\n\t.all-info-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t\tdisplay: inline-block;\n\t\tmargin: 0;\n\t\tletter-spacing: 0.5px;\n\t\ttext-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);\n\t}\n\t\n\t.all-info-tabs-container {\n\t\tposition: relative;\n\t\twidth: 100%;\n\t\tz-index: 10;\n\t\tpadding-top: 5rpx;\n\t\tpadding-bottom: 5rpx;\n\t}\n\t\n\t/* 分类标签 */\n\t.all-info-tabs {\n\t\tmargin: 0 15rpx 22rpx 15rpx;\n\t\tpadding: 0;\n\t\twhite-space: nowrap;\n\t\tdisplay: flex;\n\t\tflex-wrap: nowrap;\n\t\toverflow-x: auto;\n\t\t-webkit-overflow-scrolling: touch;\n\t\tscrollbar-width: none;\n\t\twidth: auto;\n\t\tbox-sizing: border-box;\n\t\tpadding-bottom: 15rpx;\n\t\tpadding-top: 8rpx;\n\t\tbackground-color: #ffffff;\n\t\tz-index: 100;\n\t\ttransition: all 0.3s ease;\n\t}\n\t\n\t/* 固定在顶部的标签样式 */\n\t.fixed-tabs {\n\t\tposition: fixed;\n\t\ttop: calc(var(--status-bar-height, 44px) + 44px); /* 确保在导航栏下方 */\n\t\tleft: 0;\n\t\tright: 0;\n\t\tmargin: 0;\n\t\tpadding: 15rpx 20rpx 15rpx 30rpx;\n\t\tz-index: 98; /* 确保在导航栏下方但高于其他内容 */\n\t\tbox-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);\n\t\tborder-bottom: 1px solid rgba(235, 238, 245, 0.8);\n\t\tbackground-color: rgba(255, 255, 255, 0.97);\n\t\tbackdrop-filter: blur(10px);\n\t\t-webkit-backdrop-filter: blur(10px);\n\t\tanimation: fadeInDown 0.3s ease;\n\t\twidth: 100%;\n\t\tbox-sizing: border-box;\n\t}\n\n\t/* 添加一个淡入动画 */\n\t@keyframes fadeInDown {\n\t\tfrom {\n\t\t\topacity: 0;\n\t\t\ttransform: translateY(-15px);\n\t\t}\n\t\tto {\n\t\t\topacity: 1;\n\t\t\ttransform: translateY(0);\n\t\t}\n\t}\n\n\t.all-info-tabs::-webkit-scrollbar {\n\t\tdisplay: none;\n\t}\n\t\n\t.all-info-tab {\n\t\tdisplay: inline-block;\n\t\tpadding: 12rpx 24rpx;\n\t\tmargin-right: 12rpx;\n\t\tfont-size: 26rpx;\n\t\tcolor: #555;\n\t\tborder-radius: 16rpx;\n\t\tbackground: none;\n\t\tvertical-align: middle;\n\t\tflex-shrink: 0;\n\t\tposition: relative;\n\t\tz-index: 1;\n\t\tline-height: 1.4;\n\t\ttransition: all 0.2s ease;\n\t}\n\t\n\t.all-info-tab:active {\n\t\topacity: 0.7;\n\t\ttransform: scale(0.96);\n\t\tbackground-color: rgba(0, 0, 0, 0.05);\n\t}\n\t\n\t.all-info-tabs .all-info-tab:last-child {\n\t\tmargin-right: 30rpx;\n\t}\n\t\n\t.all-info-tab.active {\n\t\tcolor: #1677ff;\n\t\tbackground: linear-gradient(to bottom, #e6f0ff, #d2e6ff);\n\t\tfont-weight: 500;\n\t\tbox-shadow: 0 2rpx 6rpx rgba(22, 119, 255, 0.15);\n\t\ttransform: translateY(-2rpx);\n\t\ttransition: all 0.3s ease;\n\t}\n\t\n\t.all-info-list {\n\t\tpadding: 0 22rpx;\n\t\toverflow: hidden;\n\t}\n\t\n\t.all-info-item {\n\t\tbackground: #ffffff;\n\t\tborder-radius: 12rpx;\n\t\tpadding: 26rpx 24rpx 22rpx 24rpx;\n\t\tmargin-bottom: 22rpx;\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);\n\t}\n\t\n\t.info-item-header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 12rpx;\n\t}\n\t\n\t.info-tag {\n\t\tfont-size: 22rpx;\n\t\tcolor: #1677ff;\n\t\tbackground: rgba(230, 240, 255, 0.7);\n\t\tpadding: 6rpx 16rpx;\n\t\tborder-radius: 6rpx;\n\t\tmargin-right: 12rpx;\n\t\tfont-weight: 500;\n\t\tbox-shadow: 0 1rpx 3rpx rgba(22, 119, 255, 0.1);\n\t}\n\t\n\t.info-time {\n\t\tfont-size: 22rpx;\n\t\tcolor: #999;\n\t}\n\t\n\t.info-content {\n\t\tfont-size: 26rpx;\n\t\tcolor: #333;\n\t\tmargin-bottom: 15rpx;\n\t\tline-height: 1.5;\n\t}\n\t\n\t.info-meta {\n\t\tfont-size: 22rpx;\n\t\tcolor: #999;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t}\n\t\n\t.info-views {\n\t\tdisplay: inline-block;\n\t}\n\t\n\t.topped-item {\n\t\tbackground: #fefefe;\n\t\tborder-left: 4rpx solid #ff6b6b;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.08);\n\t}\n\t\n\t.premium-topped {\n\t\tborder-left: 4rpx solid #ffab2b;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(255, 171, 43, 0.08);\n\t}\n\t\n\t.topped-tag {\n\t\tcolor: #ff6b6b;\n\t\tbackground: rgba(255, 107, 107, 0.1);\n\t\tbox-shadow: 0 1rpx 3rpx rgba(255, 107, 107, 0.1);\n\t}\n\t\n\t.topped-badge {\n\t\tcolor: #ff6b6b;\n\t\tbackground: rgba(255, 107, 107, 0.1);\n\t}\n\t\n\t.premium-badge {\n\t\tcolor: #ffab2b;\n\t\tbackground: rgba(255, 171, 43, 0.1);\n\t\tbox-shadow: 0 1rpx 3rpx rgba(255, 171, 43, 0.1);\n\t}\n\t\n\t.tabs-placeholder {\n\t\theight: 80rpx;\n\t\twidth: 100%;\n\t}\n\t\n\t.fade-in {\n\t\tanimation: fadeIn 0.5s ease;\n\t}\n\t\n\t@keyframes fadeIn {\n\t\tfrom {\n\t\t\topacity: 0;\n\t\t\ttransform: translateY(20rpx);\n\t\t}\n\t\tto {\n\t\t\topacity: 1;\n\t\t\ttransform: translateY(0);\n\t\t}\n\t}\n\t\n\t.ad-banner {\n\t\tmargin-bottom: 22rpx;\n\t\tborder-radius: 12rpx;\n\t\toverflow: hidden;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\n\t}\n\t\n\t.ad-banner-image {\n\t\twidth: 100%;\n\t\theight: 140rpx;\n\t\tborder-radius: 12rpx;\n\t}\n\t\n\t.tabs-placeholder-container {\n\t\theight: 80rpx;\n\t\twidth: 100%;\n\t}\n\t\n\t/* 红包相关样式 */\n\t.red-packet-item {\n\t\tbackground-color: #FFF9F9;\n\t\tborder-left: 4rpx solid #FF4D4F;\n\t}\n\t\n\t.red-packet-tag {\n\t\tbackground-color: #FF4D4F;\n\t\tcolor: #FFFFFF;\n\t}\n\t\n\t.red-packet-badge {\n\t\tbackground-color: #FF4D4F;\n\t\tcolor: #FFFFFF;\n\t\tfont-weight: bold;\n\t\tanimation: pulse 1.5s infinite;\n\t}\n\t\n\t@keyframes pulse {\n\t\t0% {\n\t\t\ttransform: scale(1);\n\t\t}\n\t\t50% {\n\t\t\ttransform: scale(1.05);\n\t\t}\n\t\t100% {\n\t\t\ttransform: scale(1);\n\t\t}\n\t}\n\t\n\t.info-meta {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t}\n\t\n\t.info-meta-left {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\t\n\t.red-packet-info {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbackground-color: #FFF0F0;\n\t\tpadding: 4rpx 12rpx;\n\t\tborder-radius: 20rpx;\n\t}\n\t\n\t.red-packet-icon {\n\t\twidth: 28rpx;\n\t\theight: 28rpx;\n\t\tmargin-right: 6rpx;\n\t}\n\t\n\t.red-packet-amount {\n\t\tcolor: #FF4D4F;\n\t\tfont-size: 24rpx;\n\t\tfont-weight: bold;\n\t\tmargin-right: 6rpx;\n\t}\n\t\n\t.red-packet-count {\n\t\tcolor: #FF4D4F;\n\t\tfont-size: 22rpx;\n\t}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/news/pages/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "mockApi", "visibleCategories", "infoCategories"], "mappings": ";;;;;;AAkOA,MAAA,WAAA,MAAA;AACA,MAAA,uBAAA,MAAA;;;IAOE;AAAA,IACA;AAAA;EAEA,QAAA;;AAGE,UAAA,sBAAAA,cAAAA,IAAA,EAAA;;;;;AAKA,UAAA,kBAAAA,kBAAA,IAAA;;AAEA,UAAA,SAAAA,kBAAA,CAAA;;;AAKA,UAAA,WAAAA,cAAAA,IAAA;AAAA,MACA,OAAA;AAAA,MACA,KAAA;AAAA,IACA,CAAA;;;MAKE,OAAA;AAAA,MACA,MAAA;AAAA,MACA,QAAA;AAAA;MAEA,OAAA;AAAA,MACA,cAAA;AAAA,MACD,aAAA;AAAA;MAEC,OAAA;AAAA,MACA,OAAA;AAAA;;;IAIF,CAAA;;;AAOA,UAAA,sBAAAA,kBAAA,IAAA;;AAIA,UAAA,mBAAAC,cAAAA,SAAA,MAAA;;IAEA,CAAA;AAGA,UAAA,mBAAAA,cAAAA,SAAA,MAAA;;;MAID;;AAMG,aAAA,YAAA,MAAA,OAAA,UAAA,KAAA,aAAA,gBAAA;AAAA,IACF,CAAA;AAGA,UAAA,yBAAAA,cAAAA,SAAA,MAAA;;;MAID;;AAMG,aAAA,eAAA,MAAA,OAAA,UAAA,KAAA,aAAA,gBAAA;AAAA,IACF,CAAA;;AAKD,UAAA,QAAA,IAAA;;AAEKC,sBAAA,MAAA,MAAA,OAAA,4CAAA,SAAA,OAAA,KAAA;AAAA,MACL;AAGAA,oBAAAA,MAAA,cAAA;AAAA,QACC,SAAA,CAAA,QAAA;AACM,0BAAA,QAAA,IAAA;AAAA,QACN;AAAA,MACD,CAAA;;AAIG;AAGH,iBAAA,MAAA;AACK;MACL,GAAA,GAAA;AAAA,IACC;;AAIAA,oBAAAA,MAAA,YAAA;AAAA;MAEA,CAAA;AAGEC,eAAA,QAAA,KAAA,UAAA,EAAA,EAAA,KAAA,UAAA;;AAGDD,sBAAA,MAAA,YAAA;AAGG,mBAAA,EAAA;AAAA,MACF,CAAA,EAAA,MAAA,SAAA;AACEA,sBAAA,MAAA,YAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACE,OAAA;AAAA;QAEF,CAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,4CAAA,WAAA,GAAA;AAAA,MACF,CAAA;AAAA,IACF;;;AASI,eAAA;AAAA,UACE,GAAA;AAAA;UAEA,MAAA,KAAA,QAAA,CAAA;AAAA,UACA,OAAA,KAAA,SAAA;AAAA,UACA,OAAA,KAAA,SAAA;AAAA;;MAGJ,CAAA;;AAKE,eAAA;AAAA,UACE,GAAA;AAAA;UAEA,MAAA,KAAA,QAAA,CAAA;AAAA,UACA,OAAA,KAAA,SAAA;AAAA,UACA,OAAA,KAAA,SAAA;AAAA;;MAGJ,CAAA;AAGA,eAAA,QAAA;AAAA,QACE,OAAA;AAAA;;;IAIJ;;AAIAA,oBAAA,MAAA,MAAA,OAAA,4CAAA,aAAA,EAAA;AAAA,IAEA;AAGA,aAAA,aAAA;AAEAA,oBAAAA,MAAA,aAAA;AAAA;AAEEA,wBAAAA,MAAA,MAAA,OAAA,4CAAA,MAAA;AAAA,QACD;AAAA,MACD,CAAA;AAEE,eAAA,UAAA,CAAA,SAAA;AACA,eAAA,SAAA,SAAA,UAAA,IAAA;AAGFA,oBAAAA,MAAA,UAAA;AAAA,QACI,OAAA,SAAA,UAAA,QAAA;AAAA;;MAGJ,CAAA;AAAA,IACA;;AAKAA,oBAAAA,MAAA,aAAA;AAAA;AAEEA,wBAAAA,MAAA,MAAA,OAAA,4CAAA,MAAA;AAAA,QACD;AAAA,MACD,CAAA;AAEE,eAAA,cAAA,CAAA,SAAA;AAGFA,oBAAAA,MAAA,UAAA;AAAA,QACI,OAAA,SAAA,cAAA,QAAA;AAAA;;MAGJ,CAAA;AAAA,IACA;AAGA,aAAA,cAAA;AAEAA,oBAAA,MAAA,aAAA;AACAA,oBAAAA,MAAA,MAAA,OAAA,4CAAA,SAAA;AAAA,IAEA;;AAIAA,oBAAAA,MAAA,WAAA;AAAA,QACC,KAAA,+DAAA,OAAA,KAAA;AAAA,MACD,CAAA;AAAA,IACA;;;AAOE,YAAA,MAAA;AACCA,wBAAAA,MAAA,aAAA;AAAA,YACC,WAAA,KAAA;AAAA;UAED,CAAA;AAAA;AAGAA,wBAAAA,MAAA,aAAA;AAAA,YACC,WAAA;AAAA;UAED,CAAA;AAAA,QACD;AAAA,SAED;IACD;AAGA,aAAA,kBAAA,OAAA;AAEAA,oBAAAA,MAAA,aAAA;AAAA;AAEEA,wBAAAA,MAAA,MAAA,OAAA,4CAAA,MAAA;AAAA,QACD;AAAA,MACD,CAAA;AAEE,YAAA,UAAA,SAAA,YAAA,KAAA;AACF,cAAA,UAAA,CAAA,QAAA;AACA,cAAA,SAAA,QAAA,UAAA,IAAA;AAAA,IACA;AAGA,aAAA,eAAA,cAAA,UAAA;AACE,0BAAA,QAAA;;AAEA,iBAAA,QAAA;AAGF,iBAAA,MAAA;kDAEE,OAAA,0BAAA;AAEC,cAAA,MAAA;AACCA,0BAAAA,MAAA,aAAA;AAAA,cACC,WAAA,KAAA;AAAA;YAED,CAAA;AAAA,UACD;AAAA,WAED;MACF,GAAA,GAAA;AAAA,IACA;;;AAKE,kBAAA,QAAA;AACA,kBAAA,QAAA;;IAEF;;;AAKCA,sBAAAA,MAAA,UAAA;AAAA,UACC,OAAA;AAAA;QAED,CAAA;AACA;AAAA,MACD;AAGAA,oBAAAA,MAAA,aAAA;AAAA;AAEEA,wBAAAA,MAAA,MAAA,OAAA,4CAAA,MAAA;AAAA,QACD;AAAA,MACD,CAAA;;;AAOC,YAAA,CAAA,QAAA,SAAA;AACC,kBAAA,UAAA;QACD;;UAIC,MAAA;AAAA;UAEA,MAAA;AAAA;UAEA,YAAA;AAAA,QACD,CAAA;AAGG,iBAAA;;AAKHA,sBAAAA,MAAA,UAAA;AAAA;;QAGA,CAAA;AAGA,mBAAA,MAAA;AACK,gBAAA,kBAAA,2BAAA,oBAAA,QAAA,CAAA;;AAIF,gBAAA,MAAA;AACCA,4BAAAA,MAAA,aAAA;AAAA,gBACC,WAAA,KAAA;AAAA;cAED,CAAA;AAAA,YACD;AAAA,aAED;QACF,GAAA,GAAA;AAAA;;UAIA,QAAA;AAAA,UACA,MAAA;AAAA;UAEA,MAAA;AAAA,UACA,OAAA;AAAA;UAEC,YAAA;AAAA,UACA,gBAAA;AAAA,UACA,SAAA,CAAA;AAAA,QACD,CAAA;AAGG,iBAAA;AAEJA,sBAAAA,MAAA,UAAA;AAAA;;QAGC,CAAA;AAGA,mBAAA,MAAA;AACK;QACL,GAAA,GAAA;AAAA,MACD;AAGE;IACF;;AAKAA,oBAAAA,MAAA,aAAA;AAAA;AAEEA,wBAAAA,MAAA,MAAA,OAAA,4CAAA,MAAA;AAAA,QACD;AAAA,MACD,CAAA;AAEE,eAAA,YAAA,KAAA,EAAA,iBAAA,CAAA,SAAA,YAAA,KAAA,EAAA;AAAA,IACF;;AAIAA,oBAAAA,MAAA,YAAA;AAAA;MAEA,CAAA;;AAKI,iBAAA,YAAA,KAAA,GAAA,QAAA;;AAKAA,sBAAA,MAAA,YAAA;AACHA,sBAAAA,MAAA,UAAA;AAAA,UACC,OAAA;AAAA;QAED,CAAA;AAAA,MACC,CAAA,EAAA,MAAA,SAAA;AACEA,sBAAA,MAAA,YAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,4CAAA,aAAA,GAAA;AAAA,MACF,CAAA;AAAA,IACF;;AAIE,kBAAA,QAAA,EAAA,OAAA,UAAA;AAAA,IACF;AAGA,aAAA,cAAA;AACE,kBAAA,QAAA;AAAA,IACF;;AAIAA,oBAAAA,MAAA,UAAA;AAAA,QACC;AAAA;AAECA,wBAAA,MAAA,MAAA,SAAA,4CAAA,SAAA,GAAA;AAEAA,wBAAAA,MAAA,WAAA;AAAA,YACC;AAAA,UACD,CAAA;AAAA,QACD;AAAA,MACD,CAAA;AAAA,IACA;AAGA,aAAA,qBAAA,MAAA;AACDA,oBAAAA,MAAA,WAAA;AAAA,QACC,KAAA,kBAAA,KAAA,YAAA,aAAA,OAAA,KAAA,EAAA;AAAA,MACD,CAAA;AAAA,IACC;AAGA,aAAA,oBAAA,KAAA;AACDA,oBAAA,MAAA,MAAA,OAAA,4CAAA,UAAA,GAAA;AACG,qBAAA,QAAA,IAAA;AAAA,IACF;;AAIE,YAAA,QAAAA,oBAAA;;AAEE,YAAA,MAAA;AAEE,eAAA,UAAA;AAAA,QACF;AAAA,MACF,CAAA,EAAA,KAAA;AAAA,IACF;AAGA,aAAA,6BAAA;;AAEI,cAAA,QAAAA,oBAAA;;AAEE,cAAA,MAAA;AACE,gCAAA,SAAA,kBAAA,SAAA,KAAA,KAAA;;UAEF;AAAA,QACF,CAAA,EAAA,KAAA;AAAA,MACF,CAAA;AAAA,IACF;AAGA,aAAA,cAAA;AACEA,oBAAAA,MAAA,aAAA;AAAA;;MAGA,CAAA;AAAA,IACF;;AAKE,oBAAA,QAAA,EAAA,YAAA;AAAA,IACF;;AAIEA,oBAAAA,MAAA,MAAA,OAAA,4CAAA,MAAA;;AAIA,iBAAA,MAAA;;MAEA,GAAA,GAAA;AAAA,IACF;;AAIEA,oBAAAA,MAAA,MAAA,OAAA,4CAAA,MAAA;;AAEE;MACF;AAAA,IACF;;AAIE,aAAA;AAAA,QACE,OAAA,SAAA;AAAA;;IAGJ;;AAIE,aAAA;AAAA,QACE,OAAA,SAAA;AAAA;QAEA,MAAA,2CAAA,OAAA,KAAA;AAAA;;IAGJ;AAEA,WAAA;AAAA;AAAA;MAGE;AAAA;;;;MAKA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;MAEA,mBAAAE,qBAAA;AAAA,MACA,gBAAAC,qBAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;MAEA;AAAA,MACA;AAAA;AAAA,MAGA;AAAA,MACA;AAAA;;;MAKA;AAAA;;;;MAKA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;;MAGA;AAAA;;;MAIA;AAAA,MACA;AAAA,MACA;AAAA;;;MAIA;AAAA;MAEA;AAAA,MACA;AAAA,MACA;AAAA;;;EAIJ,OAAA,SAAA;;;EAGA,UAAA;AACEH,kBAAAA,MAAA,MAAA,OAAA,4CAAA,QAAA;AAAA;EAEF,SAAA;AACEA,kBAAAA,MAAA,MAAA,OAAA,4CAAA,MAAA;AAAA;EAEF,SAAA;AACEA,kBAAAA,MAAA,MAAA,OAAA,4CAAA,MAAA;AAAA;;AAGAA,kBAAAA,MAAA,MAAA,OAAA,4CAAA,MAAA;AAAA;EAEF,oBAAA;;;EAGA,gBAAA;AACE,SAAA,cAAA;AAAA;EAEF,aAAA,GAAA;AACE,SAAA,aAAA,CAAA;AAAA;EAEF,oBAAA;AACE,WAAA,KAAA;;EAEF,kBAAA;;EAEA;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACz1BD,GAAG,WAAW,eAAe;"}