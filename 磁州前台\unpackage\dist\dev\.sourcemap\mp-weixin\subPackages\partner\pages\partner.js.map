{"version": 3, "file": "partner.js", "sources": ["subPackages/partner/pages/partner.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNccGFydG5lclxwYWdlc1xwYXJ0bmVyLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"partner-container\">\r\n\t\t<!-- 自定义导航栏 -->\r\n\t\t<view class=\"custom-navbar\">\r\n\t\t\t<view class=\"navbar-left\" @click=\"goBack\">\r\n\t\t\t\t<image src=\"/static/images/tabbar/最新返回键.png\" class=\"back-icon\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"navbar-title\">合伙人中心</view>\r\n\t\t\t<view class=\"navbar-right\">\r\n\t\t\t\t<!-- 预留位置与发布页面保持一致 -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 添加顶部安全区域 -->\r\n\t\t<view class=\"safe-area-top\"></view>\r\n\t\t\r\n\t\t<!-- 用户卡片 -->\r\n\t\t<view class=\"user-card\">\r\n\t\t\t<image class=\"partner-avatar\" :src=\"partnerInfo.avatar || '/static/images/avatar/default.png'\" mode=\"aspectFill\"></image>\r\n\t\t\t<view class=\"user-info\">\r\n\t\t\t\t<view class=\"nickname\">{{partnerInfo.nickname || '游客'}}</view>\r\n\t\t\t\t<view class=\"level-info\">\r\n\t\t\t\t\t<image class=\"level-icon\" :src=\"getLevelIcon()\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t<text class=\"level-name\">{{getLevelName()}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t<view class=\"progress-bar\">\r\n\t\t\t\t\t<view class=\"progress\" :style=\"{width: progressWidth + '%'}\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"partner-upgrade\" @click=\"navigateTo('/subPackages/partner/pages/partner-upgrade')\" v-if=\"canUpgrade\">\r\n\t\t\t\t<text class=\"upgrade-text\">升级</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t<!-- 收益数据卡片 -->\r\n\t\t<view class=\"partner-card income-card\">\r\n\t\t\t<view class=\"income-header\">\r\n\t\t\t\t<view class=\"title\">收益统计</view>\r\n\t\t\t\t<view class=\"total-income\">总收益 <text>¥{{partnerInfo.totalIncome}}</text></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"data-row\">\r\n\t\t\t\t<view class=\"data-item\">\r\n\t\t\t\t\t<view class=\"data-value\">¥{{incomeData.today}}</view>\r\n\t\t\t\t\t<view class=\"data-label\">今日收益</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"data-item\">\r\n\t\t\t\t\t<view class=\"data-value\">¥{{incomeData.yesterday}}</view>\r\n\t\t\t\t\t<view class=\"data-label\">昨日收益</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"data-item\">\r\n\t\t\t\t\t<view class=\"data-value\">¥{{incomeData.thisWeek}}</view>\r\n\t\t\t\t\t<view class=\"data-label\">本周收益</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"chart-container\">\r\n\t\t\t\t<view class=\"chart-bars\">\r\n\t\t\t\t\t<view class=\"chart-bar\" v-for=\"(item, index) in chartData\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"bar-value\">{{item.value}}元</view>\r\n\t\t\t\t\t\t<view class=\"bar-item\" :style=\"{height: item.height + 'rpx'}\"></view>\r\n\t\t\t\t\t\t<text class=\"bar-date\">{{item.date}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 功能卡片 -->\r\n\t\t<view class=\"partner-card function-card\">\r\n\t\t\t<view class=\"partner-header\">\r\n\t\t\t\t<view class=\"title\">功能区</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"function-list\">\r\n\t\t\t\t<view class=\"function-item\" @click=\"navigateTo('/subPackages/partner/pages/partner-income')\">\r\n\t\t\t\t\t<view class=\"icon-block\" style=\"background-color: #6A9DFF;\"></view>\r\n\t\t\t\t\t<view class=\"name\">收益明细</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t<view class=\"function-item\" @click=\"navigateTo('/subPackages/partner/pages/partner-fans')\">\r\n\t\t\t\t\t<view class=\"icon-block\" style=\"background-color: #FF7D54;\"></view>\r\n\t\t\t\t\t<view class=\"name\">我的粉丝</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"function-item\" @click=\"navigateTo('/subPackages/partner/pages/partner-withdraw')\">\r\n\t\t\t\t\t<view class=\"icon-block\" style=\"background-color: #5ACB95;\"></view>\r\n\t\t\t\t\t<view class=\"name\">提现</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t<view class=\"function-item\" @click=\"navigateTo('/subPackages/partner/pages/partner-poster')\">\r\n\t\t\t\t\t<view class=\"icon-block\" style=\"background-color: #FFA940;\"></view>\r\n\t\t\t\t\t<view class=\"name\">推广海报</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"function-item\" @click=\"navigateTo('/subPackages/partner/pages/partner-qrcode')\">\r\n\t\t\t\t\t<view class=\"icon-block\" style=\"background-color: #9254DE;\"></view>\r\n\t\t\t\t\t<view class=\"name\">我的二维码</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"function-item\" @click=\"navigateTo('/subPackages/partner/pages/partner-rules')\">\r\n\t\t\t\t\t<view class=\"icon-block\" style=\"background-color: #36CFC9;\"></view>\r\n\t\t\t\t\t<view class=\"name\">规则说明</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 等级权益卡片 -->\r\n\t\t<view class=\"partner-card level-card\">\r\n\t\t\t<view class=\"partner-header\">\r\n\t\t\t\t<view class=\"title\">等级权益</view>\r\n\t\t\t\t<view class=\"more\" @click=\"navigateTo('/subPackages/partner/pages/partner-levels')\">\r\n\t\t\t\t\t查看详情 <text class=\"cuIcon-right\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"level-benefits\">\r\n\t\t\t\t<view class=\"benefit-item\">\r\n\t\t\t\t\t<image class=\"icon\" src=\"/static/images/tabbar/commission.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t\t<view class=\"name\">一级佣金比例</view>\r\n\t\t\t\t\t\t<view class=\"desc\">直接推广用户下单获得佣金</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"rate\">{{getCommissionRate(1)}}%</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"benefit-item\">\r\n\t\t\t\t\t<image class=\"icon\" src=\"/static/images/tabbar/commission2.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t\t<view class=\"name\">二级佣金比例</view>\r\n\t\t\t\t\t\t<view class=\"desc\">间接推广用户下单获得佣金</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"rate\">{{getCommissionRate(2)}}%</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 推广数据卡片 -->\r\n\t\t<view class=\"partner-card promotion-card\">\r\n\t\t\t<view class=\"partner-header\">\r\n\t\t\t\t<view class=\"title\">推广数据</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"promotion-data\">\r\n\t\t\t\t<view class=\"data-item\">\r\n\t\t\t\t\t<view class=\"data-value\">{{promotionData.viewCount}}</view>\r\n\t\t\t\t\t<view class=\"data-label\">访问人数 <text class=\"cuIcon-info\"></text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"data-item\">\r\n\t\t\t\t\t<view class=\"data-value\">{{promotionData.registerCount}}</view>\r\n\t\t\t\t\t<view class=\"data-label\">注册人数 <text class=\"cuIcon-info\"></text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"data-item\">\r\n\t\t\t\t\t<view class=\"data-value\">{{promotionData.orderCount}}</view>\r\n\t\t\t\t\t<view class=\"data-label\">下单人数 <text class=\"cuIcon-info\"></text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"data-item\">\r\n\t\t\t\t\t<view class=\"data-value\">{{promotionData.conversionRate}}</view>\r\n\t\t\t\t\t<view class=\"data-label\">转化率 <text class=\"cuIcon-info\"></text></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, computed, onMounted } from 'vue';\r\nimport { getLocalUserInfo } from '@/utils/userProfile.js';\r\nimport { smartNavigate } from '@/utils/navigation.js';\r\n\r\n// 响应式状态\r\nconst partnerInfo = reactive({\r\n\tnickname: '',\r\n\tavatar: '',\r\n\tlevel: 1, // 1: 普通合伙人, 2: 银牌合伙人, 3: 金牌合伙人, 4: 钻石合伙人\r\n\ttotalIncome: '0.00',\r\n\tthisMonthIncome: '0.00',\r\n\tfansCount: '0'\r\n});\r\n\r\nconst incomeData = reactive({\r\n\ttoday: '0.00',\r\n\tyesterday: '0.00',\r\n\tthisWeek: '0.00',\r\n\tchartData: [] // 最近7天的收益数据\r\n});\r\n\r\nconst promotionData = reactive({\r\n\tviewCount: '0',\r\n\tregisterCount: '0',\r\n\torderCount: '0',\r\n\tconversionRate: '0%'\r\n});\r\n\r\nconst progressWidth = ref(25); // 等级进度条宽度百分比\r\n\r\n// 计算属性\r\nconst canUpgrade = computed(() => {\r\n\treturn partnerInfo.level < 4; // 钻石合伙人是最高级别\r\n});\r\n\r\nconst chartData = computed(() => {\r\n\t// 转换收益数据为图表数据\r\n\tconst data = incomeData.chartData || [];\r\n\t// 找出最大值，用于计算高度比例\r\n\tconst maxValue = Math.max(...data, 10); // 至少为10，避免全部为0的情况\r\n\t\r\n\t// 计算每天的数据\r\n\treturn [\r\n\t\t{ date: '周一', value: data[0] || 0, height: data[0] ? (data[0] / maxValue) * 150 : 0 },\r\n\t\t{ date: '周二', value: data[1] || 0, height: data[1] ? (data[1] / maxValue) * 150 : 0 },\r\n\t\t{ date: '周三', value: data[2] || 0, height: data[2] ? (data[2] / maxValue) * 150 : 0 },\r\n\t\t{ date: '周四', value: data[3] || 0, height: data[3] ? (data[3] / maxValue) * 150 : 0 },\r\n\t\t{ date: '周五', value: data[4] || 0, height: data[4] ? (data[4] / maxValue) * 150 : 0 },\r\n\t\t{ date: '周六', value: data[5] || 0, height: data[5] ? (data[5] / maxValue) * 150 : 0 },\r\n\t\t{ date: '周日', value: data[6] || 0, height: data[6] ? (data[6] / maxValue) * 150 : 0 }\r\n\t];\r\n});\r\n\r\n// 生命周期钩子\r\nonMounted(() => {\r\n\t// 获取用户信息\r\n\tgetUserInfo();\r\n\t// 获取合伙人信息\r\n\tgetPartnerInfo();\r\n\t// 获取收益数据\r\n\tgetIncomeData();\r\n\t// 获取推广数据\r\n\tgetPromotionData();\r\n});\r\n\r\n// 方法\r\n// 获取用户信息\r\nconst getUserInfo = () => {\r\n\tconst userInfo = getLocalUserInfo();\r\n\tif (userInfo) {\r\n\t\tpartnerInfo.nickname = userInfo.nickname;\r\n\t\tpartnerInfo.avatar = userInfo.avatar;\r\n\t}\r\n};\r\n\r\n// 获取合伙人信息\r\nconst getPartnerInfo = () => {\r\n\t// 模拟数据，实际应从API获取\r\n\tObject.assign(partnerInfo, {\r\n\t\tlevel: 2,\r\n\t\ttotalIncome: '1280.50',\r\n\t\tthisMonthIncome: '320.75',\r\n\t\tfansCount: '28'\r\n\t});\r\n\t\r\n\t// 计算进度条宽度\r\n\tprogressWidth.value = (partnerInfo.level / 4) * 100;\r\n};\r\n\r\n// 获取收益数据\r\nconst getIncomeData = () => {\r\n\t// 模拟数据，实际应从API获取\r\n\tObject.assign(incomeData, {\r\n\t\ttoday: '35.60',\r\n\t\tyesterday: '42.80',\r\n\t\tthisWeek: '198.30',\r\n\t\tchartData: [12.5, 18.3, 25.6, 42.8, 35.6, 10.5, 8.2]\r\n\t});\r\n};\r\n\r\n// 获取推广数据\r\nconst getPromotionData = () => {\r\n\t// 模拟数据，实际应从API获取\r\n\tObject.assign(promotionData, {\r\n\t\tviewCount: '156',\r\n\t\tregisterCount: '32',\r\n\t\torderCount: '18',\r\n\t\tconversionRate: '11.5%'\r\n\t});\r\n};\r\n\r\n// 获取等级图标\r\nconst getLevelIcon = () => {\r\n\tconst icons = {\r\n\t\t1: '/static/images/tabbar/partner-level-1.png',\r\n\t\t2: '/static/images/tabbar/partner-level-2.png',\r\n\t\t3: '/static/images/tabbar/partner-level-3.png',\r\n\t\t4: '/static/images/tabbar/partner-level-4.png'\r\n\t};\r\n\treturn icons[partnerInfo.level] || icons[1];\r\n};\r\n\r\n// 获取等级名称\r\nconst getLevelName = () => {\r\n\tconst names = {\r\n\t\t1: '普通合伙人',\r\n\t\t2: '银牌合伙人',\r\n\t\t3: '金牌合伙人',\r\n\t\t4: '钻石合伙人'\r\n\t};\r\n\treturn names[partnerInfo.level] || names[1];\r\n};\r\n\r\n// 获取佣金比例\r\nconst getCommissionRate = (level) => {\r\n\t// 根据合伙人等级返回不同的佣金比例\r\n\tconst commissionRates = {\r\n\t\t1: { 1: 5, 2: 2 },    // 普通合伙人：一级5%，二级2%\r\n\t\t2: { 1: 8, 2: 3 },    // 银牌合伙人：一级8%，二级3%\r\n\t\t3: { 1: 12, 2: 5 },   // 金牌合伙人：一级12%，二级5%\r\n\t\t4: { 1: 15, 2: 8 }    // 钻石合伙人：一级15%，二级8%\r\n\t};\r\n\t\r\n\treturn commissionRates[partnerInfo.level]?.[level] || 0;\r\n};\r\n\r\n// 页面跳转\r\nconst navigateTo = (url) => {\r\n\tsmartNavigate(url).catch(err => {\r\n\t\tconsole.error('智能导航失败:', err);\r\n\t});\r\n};\r\n\r\n// 返回键\r\nconst goBack = () => {\r\n\tuni.navigateBack({\r\n\t\tfail: () => {\r\n\t\t\t// 如果返回失败（无页面可返回），则跳转到首页\r\n\t\t\tuni.switchTab({\r\n\t\t\t\turl: '/pages/my/my'\r\n\t\t\t});\r\n\t\t}\r\n\t});\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.partner-container {\r\n\t\tpadding-bottom: 30rpx;\r\n\t\tmin-height: 100vh;\r\n\t\tbackground-color: #F6F8FB;\r\n\t}\r\n\t\r\n\t.safe-area {\r\n\t\theight: 120rpx;\r\n\t\twidth: 100%;\r\n\t}\r\n\t\r\n\t.safe-area-top {\r\n\t\theight: 180rpx;\r\n\t\twidth: 100%;\r\n\t}\r\n\t\r\n\t.partner-card {\r\n\t\tmargin: 30rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tbox-shadow: 0 8rpx 20rpx rgba(22, 119, 255, 0.08);\r\n\t\tborder: 1px solid rgba(22, 119, 255, 0.1);\r\n\t\ttransition: all 0.3s ease;\r\n\t\t\r\n\t\t&:hover, &:active {\r\n\t\t\ttransform: translateY(-4rpx);\r\n\t\t\tbox-shadow: 0 12rpx 24rpx rgba(22, 119, 255, 0.12);\r\n\t\t}\r\n\t}\r\n\t\r\n\t.partner-header {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tposition: relative;\r\n\t\t\r\n\t\t&::after {\r\n\t\t\tcontent: '';\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: -15rpx;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 60rpx;\r\n\t\t\theight: 4rpx;\r\n\t\t\tbackground: #1677FF;\r\n\t\t\tborder-radius: 2rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.title {\r\n\t\t\tfont-size: 34rpx;\r\n\t\t\tfont-weight: 700;\r\n\t\t\tcolor: #333333;\r\n\t\t}\r\n\t\t\r\n\t\t.more {\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #1677FF;\r\n\t\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\t\t\r\n\t\t\t.cuIcon-right {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tmargin-left: 4rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.user-card {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 40rpx 30rpx;\r\n\t\tbackground: linear-gradient(135deg, #64B5FF, #A0D2FF);\r\n\t\tmargin: 30rpx 30rpx 30rpx; /* 调整顶部边距 */\r\n\t\tborder-radius: 20rpx;\r\n\t\tcolor: #FFFFFF;\r\n\t\tbox-shadow: 0 10rpx 30rpx rgba(22, 119, 255, 0.3);\r\n\t\r\n\t.partner-avatar {\r\n\t\t\twidth: 120rpx;\r\n\t\t\theight: 120rpx;\r\n\t\t\tborder-radius: 60rpx;\r\n\t\t\tborder: 4rpx solid #FFFFFF;\r\n\t\t\tmargin-right: 20rpx;\r\n\t\t\tbox-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);\r\n\t\t\tbackground-color: #FFFFFF;\r\n\t\t}\r\n\t\t\r\n\t\t.user-info {\r\n\t\t\tflex: 1;\r\n\t\t\t\r\n\t\t\t.nickname {\r\n\t\t\t\tfont-size: 38rpx;\r\n\t\tfont-weight: 600;\r\n\t\tmargin-bottom: 12rpx;\r\n\t\t\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\t\r\n\t\t\t.level-info {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\t\r\n\t\t\t\t.level-icon {\r\n\t\t\t\t\twidth: 40rpx;\r\n\t\t\t\t\theight: 40rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.level-name {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.progress-bar {\r\n\t\t\t\theight: 12rpx;\r\n\t\t\t\tbackground-color: rgba(255, 255, 255, 0.3);\r\n\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\t\r\n\t\t\t\t.progress {\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t\tbackground-color: #FFFFFF;\r\n\t\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\t\ttransition: width 0.8s ease;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t}\r\n\t\r\n\t.partner-upgrade {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tpadding: 14rpx 28rpx;\r\n\t\t\tbackground: linear-gradient(90deg, #1677FF, #4F9DFF);\r\n\t\tborder-radius: 30rpx;\r\n\t\t\tmargin-left: 20rpx;\r\n\t\t\tbox-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);\r\n\t\t\ttransition: all 0.3s ease;\r\n\t\t\t\r\n\t\t\t&:active {\r\n\t\t\t\ttransform: scale(0.95);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.upgrade-text {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #FFFFFF;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-weight: 700;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.income-card {\r\n\t\tmargin-bottom: 30rpx;\r\n\t\t\r\n\t\t.income-header {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t\r\n\t\t\t.title {\r\n\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\tfont-weight: 700;\r\n\t\t\t\tcolor: #333333;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.total-income {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #666666;\r\n\t\t\t\t\r\n\t\t\t\ttext {\r\n\t\t\t\t\tcolor: #FF6B00;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\tmargin-left: 10rpx;\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.data-row {\r\n\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\r\n\t.data-item {\r\n\t\tflex: 1;\r\n\t\ttext-align: center;\r\n\t\tposition: relative;\r\n\t\t\t\t\r\n\t\t\t\t&::after {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tbottom: -15rpx;\r\n\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\ttransform: translateX(-50%);\r\n\t\t\t\t\twidth: 60%;\r\n\t\t\t\t\theight: 3rpx;\r\n\t\t\t\t\tbackground: linear-gradient(90deg, transparent, #1677FF, transparent);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.chart-container {\r\n\t\t\theight: 260rpx;\r\n\t\t\twidth: 100%;\r\n\t\t\tposition: relative;\r\n\t\t\tmargin-top: 20rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\t\tpadding: 20rpx 10rpx 0;\r\n\t\t}\r\n\t\t\r\n\t\t.chart-bars {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\t\talign-items: flex-end;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 200rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.chart-bar {\r\n\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\t\tjustify-content: flex-end;\r\n\t\t\tflex: 1;\r\n\t\theight: 100%;\r\n\t}\r\n\t\r\n\t\t.bar-value {\r\n\t\t\tfont-size: 20rpx;\r\n\t\t\tcolor: #666666;\r\n\t\t\tmargin-bottom: 4rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.bar-item {\r\n\t\t\twidth: 30rpx;\r\n\t\t\tbackground: linear-gradient(180deg, #1677FF, #64B5FF);\r\n\t\t\tborder-radius: 15rpx 15rpx 0 0;\r\n\t\t}\r\n\t\t\r\n\t\t.bar-date {\r\n\t\t\tmargin-top: 10rpx;\r\n\t\t\tfont-size: 22rpx;\r\n\t\t\tcolor: #999999;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.data-value {\r\n\t\tfont-size: 42rpx;\r\n\t\tfont-weight: 700 !important;\r\n\t\tcolor: #1677FF !important;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tletter-spacing: 1rpx;\r\n\t}\r\n\t\r\n\t.data-label {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #76ABFF !important;\r\n\t\tfont-weight: 400 !important;\r\n\t}\r\n\t\r\n\t.function-card {\r\n\t\t.function-list {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\r\n\t.function-item {\r\n\t\t\t\twidth: 33.33%;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tpadding: 30rpx 0;\r\n\t\t\t\ttransition: all 0.3s ease;\r\n\t\t\t\t\r\n\t\t\t\t&:active {\r\n\t\t\t\t\ttransform: scale(0.95);\r\n\t\t\t\t\tbackground-color: rgba(22, 119, 255, 0.05);\r\n\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.icon-block {\r\n\t\t\t\t\twidth: 90rpx;\r\n\t\t\t\t\theight: 90rpx;\r\n\t\t\t\t\tmargin-bottom: 16rpx;\r\n\t\t\t\t\ttransition: transform 0.3s ease;\r\n\t\t\t\t\tborder-radius: 24rpx;\r\n\t\t\t\t\tmargin: 0 auto 16rpx;\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t&:active .icon-block {\r\n\t\t\t\t\ttransform: scale(1.1);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.name {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.level-card {\r\n\t\t.level-benefits {\r\n\t\t\tmargin-top: 20rpx;\r\n\t\t\t\r\n\t\t\t.benefit-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\t\t\tpadding: 24rpx 0;\r\n\t\t\t\tborder-bottom: 1px solid #F5F5F5;\r\n\t\t\t\t\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tborder-bottom: none;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.icon {\r\n\t\t\t\t\twidth: 70rpx;\r\n\t\t\t\t\theight: 70rpx;\r\n\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\tbackground-color: rgba(22, 119, 255, 0.1);\r\n\t\t\t\t\tpadding: 12rpx;\r\n\t\tborder-radius: 50%;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.info {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.name {\r\n\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\t\r\n\t\t\t\t\t.desc {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.rate {\r\n\t\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\t\tfont-weight: 700;\r\n\t\t\t\t\tcolor: #FF6B00;\r\n\t\t\t\t\tbackground-color: rgba(255, 107, 0, 0.1);\r\n\t\t\t\t\tpadding: 8rpx 20rpx;\r\n\t\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.promotion-card {\r\n\t.promotion-data {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\r\n\t\t\t.data-item {\r\n\t\twidth: 50%;\r\n\t\t\t\tpadding: 24rpx 0;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\t\r\n\t\t\t\t&::after {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\t\t\t\tbottom: 10rpx;\r\n\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\ttransform: translateX(-50%);\r\n\t\t\t\t\twidth: 60%;\r\n\t\t\t\t\theight: 3rpx;\r\n\t\t\t\t\tbackground: linear-gradient(90deg, transparent, #1677FF, transparent);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.cuIcon-info {\r\n\t\t\t\t\tmargin-left: 6rpx;\r\n\t\t\t\t\tcolor: #76ABFF;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.summary-items {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tmargin: 0 -10rpx;\r\n\t\t\r\n\t\t.summary-item {\r\n\t\t\twidth: calc(50% - 20rpx);\r\n\t\t\tmargin: 10rpx;\r\n\t\t\tbackground-color: #FFFFFF;\r\n\t\t\tborder-radius: 16rpx;\r\n\t\t\tpadding: 24rpx;\r\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\r\n\t\t\t\r\n\t\t\t.item-title {\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #999999;\r\n\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.item-value {\r\n\t\t\t\tfont-size: 38rpx;\r\n\t\t\t\tfont-weight: 700;\r\n\t\t\t\tcolor: #1677FF;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t/* 增强文本样式 */\r\n\t.text-bold {\r\n\t\tfont-weight: 700 !important;\r\n\t}\r\n\t\r\n\t/* 导航栏样式 */\r\n\t.navbar {\r\n\t\tposition: relative;\r\n\t\tz-index: 1;\r\n\t\theight: 44px;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\t\r\n\t/* 自定义导航栏 */\r\n\t.custom-navbar {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\theight: 88rpx;\r\n\t\tpadding: 0 30rpx;\r\n\t\tpadding-top: 44px; /* 状态栏高度 */\r\n\t\tposition: fixed; /* 改为固定定位 */\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbackground-image: linear-gradient(135deg, #0066FF, #0052CC); /* 改为与发布页一致的渐变角度 */\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.15);\r\n\t\tz-index: 100; /* 提高z-index确保在最上层 */\r\n\t}\r\n\t\r\n\t.navbar-title {\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tcolor: #ffffff;\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: 700;\r\n\t\tfont-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\r\n\t\ttext-align: center;\r\n\t}\r\n\t\r\n\t.navbar-right {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t}\r\n\t\r\n\t.navbar-left {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tposition: relative;\r\n\t\tz-index: 20; /* 确保在标题上层，可以被点击 */\r\n\t}\r\n\t\r\n\t.back-icon {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/partner/pages/partner.vue'\nwx.createPage(MiniProgramPage)"], "names": ["reactive", "ref", "computed", "onMounted", "getLocalUserInfo", "smartNavigate", "uni"], "mappings": ";;;;;;;;AA+JA,UAAM,cAAcA,cAAAA,SAAS;AAAA,MAC5B,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,OAAO;AAAA;AAAA,MACP,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,WAAW;AAAA,IACZ,CAAC;AAED,UAAM,aAAaA,cAAAA,SAAS;AAAA,MAC3B,OAAO;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,WAAW,CAAE;AAAA;AAAA,IACd,CAAC;AAED,UAAM,gBAAgBA,cAAAA,SAAS;AAAA,MAC9B,WAAW;AAAA,MACX,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,gBAAgB;AAAA,IACjB,CAAC;AAED,UAAM,gBAAgBC,cAAAA,IAAI,EAAE;AAG5B,UAAM,aAAaC,cAAQ,SAAC,MAAM;AACjC,aAAO,YAAY,QAAQ;AAAA,IAC5B,CAAC;AAED,UAAM,YAAYA,cAAQ,SAAC,MAAM;AAEhC,YAAM,OAAO,WAAW,aAAa;AAErC,YAAM,WAAW,KAAK,IAAI,GAAG,MAAM,EAAE;AAGrC,aAAO;AAAA,QACN,EAAE,MAAM,MAAM,OAAO,KAAK,CAAC,KAAK,GAAG,QAAQ,KAAK,CAAC,IAAK,KAAK,CAAC,IAAI,WAAY,MAAM,EAAG;AAAA,QACrF,EAAE,MAAM,MAAM,OAAO,KAAK,CAAC,KAAK,GAAG,QAAQ,KAAK,CAAC,IAAK,KAAK,CAAC,IAAI,WAAY,MAAM,EAAG;AAAA,QACrF,EAAE,MAAM,MAAM,OAAO,KAAK,CAAC,KAAK,GAAG,QAAQ,KAAK,CAAC,IAAK,KAAK,CAAC,IAAI,WAAY,MAAM,EAAG;AAAA,QACrF,EAAE,MAAM,MAAM,OAAO,KAAK,CAAC,KAAK,GAAG,QAAQ,KAAK,CAAC,IAAK,KAAK,CAAC,IAAI,WAAY,MAAM,EAAG;AAAA,QACrF,EAAE,MAAM,MAAM,OAAO,KAAK,CAAC,KAAK,GAAG,QAAQ,KAAK,CAAC,IAAK,KAAK,CAAC,IAAI,WAAY,MAAM,EAAG;AAAA,QACrF,EAAE,MAAM,MAAM,OAAO,KAAK,CAAC,KAAK,GAAG,QAAQ,KAAK,CAAC,IAAK,KAAK,CAAC,IAAI,WAAY,MAAM,EAAG;AAAA,QACrF,EAAE,MAAM,MAAM,OAAO,KAAK,CAAC,KAAK,GAAG,QAAQ,KAAK,CAAC,IAAK,KAAK,CAAC,IAAI,WAAY,MAAM,EAAG;AAAA,MACvF;AAAA,IACA,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AAEf;AAEA;AAEA;AAEA;IACD,CAAC;AAID,UAAM,cAAc,MAAM;AACzB,YAAM,WAAWC,kBAAAA;AACjB,UAAI,UAAU;AACb,oBAAY,WAAW,SAAS;AAChC,oBAAY,SAAS,SAAS;AAAA,MAC9B;AAAA,IACF;AAGA,UAAM,iBAAiB,MAAM;AAE5B,aAAO,OAAO,aAAa;AAAA,QAC1B,OAAO;AAAA,QACP,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,WAAW;AAAA,MACb,CAAE;AAGD,oBAAc,QAAS,YAAY,QAAQ,IAAK;AAAA,IACjD;AAGA,UAAM,gBAAgB,MAAM;AAE3B,aAAO,OAAO,YAAY;AAAA,QACzB,OAAO;AAAA,QACP,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,GAAG;AAAA,MACrD,CAAE;AAAA,IACF;AAGA,UAAM,mBAAmB,MAAM;AAE9B,aAAO,OAAO,eAAe;AAAA,QAC5B,WAAW;AAAA,QACX,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,gBAAgB;AAAA,MAClB,CAAE;AAAA,IACF;AAGA,UAAM,eAAe,MAAM;AAC1B,YAAM,QAAQ;AAAA,QACb,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACC,aAAO,MAAM,YAAY,KAAK,KAAK,MAAM,CAAC;AAAA,IAC3C;AAGA,UAAM,eAAe,MAAM;AAC1B,YAAM,QAAQ;AAAA,QACb,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACC,aAAO,MAAM,YAAY,KAAK,KAAK,MAAM,CAAC;AAAA,IAC3C;AAGA,UAAM,oBAAoB,CAAC,UAAU;;AAEpC,YAAM,kBAAkB;AAAA,QACvB,GAAG,EAAE,GAAG,GAAG,GAAG,EAAG;AAAA;AAAA,QACjB,GAAG,EAAE,GAAG,GAAG,GAAG,EAAG;AAAA;AAAA,QACjB,GAAG,EAAE,GAAG,IAAI,GAAG,EAAG;AAAA;AAAA,QAClB,GAAG,EAAE,GAAG,IAAI,GAAG,EAAG;AAAA;AAAA,MACpB;AAEC,eAAO,qBAAgB,YAAY,KAAK,MAAjC,mBAAqC,WAAU;AAAA,IACvD;AAGA,UAAM,aAAa,CAAC,QAAQ;AAC3BC,uBAAAA,cAAc,GAAG,EAAE,MAAM,SAAO;AAC/BC,sBAAc,MAAA,MAAA,SAAA,gDAAA,WAAW,GAAG;AAAA,MAC9B,CAAE;AAAA,IACF;AAGA,UAAM,SAAS,MAAM;AACpBA,oBAAAA,MAAI,aAAa;AAAA,QAChB,MAAM,MAAM;AAEXA,wBAAAA,MAAI,UAAU;AAAA,YACb,KAAK;AAAA,UACT,CAAI;AAAA,QACD;AAAA,MACH,CAAE;AAAA,IACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5TA,GAAG,WAAW,eAAe;"}