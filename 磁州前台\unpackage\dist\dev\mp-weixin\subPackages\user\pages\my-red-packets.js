"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const utils_redPacket = require("../../../utils/redPacket.js");
const utils_format = require("../../../utils/format.js");
const _sfc_main = {
  __name: "my-red-packets",
  setup(__props) {
    const tabs = common_vendor.ref([
      { name: "收到的红包", type: "received" },
      { name: "发出的红包", type: "sent" }
    ]);
    const currentTab = common_vendor.ref(0);
    const redPacketList = common_vendor.ref([]);
    const page = common_vendor.ref(1);
    const pageSize = common_vendor.ref(20);
    const loading = common_vendor.ref(false);
    const hasMore = common_vendor.ref(true);
    const cache = common_vendor.reactive(/* @__PURE__ */ new Map());
    common_vendor.onMounted(() => {
      loadData();
    });
    const switchTab = (index) => {
      if (currentTab.value === index)
        return;
      currentTab.value = index;
      page.value = 1;
      redPacketList.value = [];
      hasMore.value = true;
      loadData();
    };
    const loadData = async () => {
      if (loading.value || !hasMore.value)
        return;
      loading.value = true;
      const type = tabs.value[currentTab.value].type;
      const cacheKey = `${type}_${page.value}`;
      try {
        if (cache.has(cacheKey)) {
          const cachedData = cache.get(cacheKey);
          redPacketList.value = [...redPacketList.value, ...cachedData.list];
          hasMore.value = cachedData.hasMore;
        } else {
          const res = await utils_redPacket.getMyRedPackets({
            type,
            page: page.value,
            pageSize: pageSize.value
          });
          redPacketList.value = [...redPacketList.value, ...res.list];
          hasMore.value = res.hasMore;
          cache.set(cacheKey, {
            list: res.list,
            hasMore: res.hasMore
          });
        }
        page.value++;
      } catch (err) {
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
      } finally {
        loading.value = false;
      }
    };
    const onRefresh = async () => {
      page.value = 1;
      redPacketList.value = [];
      hasMore.value = true;
      cache.clear();
      await loadData();
      common_vendor.index.stopPullDownRefresh();
    };
    const loadMore = () => {
      loadData();
    };
    const viewRedPacketDetail = (item) => {
      common_vendor.index.navigateTo({
        url: `/pages/red-packet/detail?id=${item.id}`
      });
    };
    const getStatusText = (status) => {
      const statusMap = {
        pending: "待领取",
        received: "已领取",
        expired: "已过期",
        completed: "已领完"
      };
      return statusMap[status] || status;
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.f(tabs.value, (tab, index, i0) => {
          return {
            a: common_vendor.t(tab.name),
            b: index,
            c: currentTab.value === index ? 1 : "",
            d: common_vendor.o(($event) => switchTab(index), index)
          };
        }),
        b: common_vendor.f(redPacketList.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.title),
            b: common_vendor.t(common_vendor.unref(utils_format.formatTime)(item.createTime)),
            c: item.type === "received"
          }, item.type === "received" ? {
            d: common_vendor.t(common_vendor.unref(utils_format.formatAmount)(item.amount))
          } : {
            e: common_vendor.t(common_vendor.unref(utils_format.formatAmount)(item.totalAmount))
          }, {
            f: common_vendor.t(getStatusText(item.status)),
            g: common_vendor.n(item.status),
            h: index,
            i: common_vendor.o(($event) => viewRedPacketDetail(item), index)
          });
        }),
        c: loading.value
      }, loading.value ? {} : {}, {
        d: !hasMore.value && redPacketList.value.length > 0
      }, !hasMore.value && redPacketList.value.length > 0 ? {} : {}, {
        e: !loading.value && redPacketList.value.length === 0
      }, !loading.value && redPacketList.value.length === 0 ? {
        f: common_assets._imports_0$21
      } : {}, {
        g: common_vendor.o(loadMore),
        h: common_vendor.o(onRefresh)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/user/pages/my-red-packets.js.map
