<view class="product-container"><view class="header-area"><view class="safe-area-top"></view><view class="custom-navbar"><view class="navbar-left" bindtap="{{b}}"><image src="{{a}}" class="back-icon" style="filter:brightness(0) invert(1)"></image></view><view class="navbar-title">商品管理</view><view class="navbar-right"><text class="add-product-text" bindtap="{{c}}">添加</text></view></view><scroll-view class="category-scroll" scroll-x show-scrollbar="false"><view class="category-tabs"><view class="{{['category-tab', d && 'active']}}" bindtap="{{e}}"> 全部 </view><view wx:for="{{f}}" wx:for-item="category" wx:key="c" class="{{['category-tab', category.b && 'active']}}" bindtap="{{category.d}}">{{category.a}}</view></view></scroll-view><view class="search-box"><image src="{{g}}" class="search-icon"></image><input type="text" class="search-input" placeholder="搜索商品名称/关键词" confirm-type="search" bindconfirm="{{h}}" value="{{i}}" bindinput="{{j}}"/><text wx:if="{{k}}" class="clear-icon" bindtap="{{l}}">×</text></view></view><view class="bg-decoration bg-circle-1"></view><view class="bg-decoration bg-circle-2"></view><view class="bg-decoration bg-circle-3"></view><scroll-view class="content-scroll" scroll-y bindscrolltolower="{{B}}" refresher-enabled refresher-triggered="{{C}}" bindrefresherrefresh="{{D}}"><view wx:if="{{m}}" class="search-result-tip"><text class="search-keyword">"{{n}}"</text><text class="search-count">搜索结果：{{o}}个商品</text></view><view wx:if="{{p}}" class="batch-toolbar"><view class="selection-info"> 已选择 <text class="selected-count">{{q}}</text> 项 </view><view class="batch-actions"><view class="batch-btn" bindtap="{{r}}"><text class="btn-text">上架</text></view><view class="batch-btn" bindtap="{{s}}"><text class="btn-text">下架</text></view><view class="batch-btn delete" bindtap="{{t}}"><text class="btn-text">删除</text></view></view></view><view class="product-list"><view wx:for="{{v}}" wx:for-item="product" wx:key="r" class="{{['product-item', product.q && 'selected']}}" bindtap="{{product.s}}" bindlongpress="{{product.t}}"><view wx:if="{{w}}" class="select-checkbox" catchtap="{{product.b}}"><view class="{{['checkbox', product.a && 'checked']}}"></view></view><view class="product-content"><image src="{{product.c}}" mode="aspectFill" class="product-image"></image><view class="product-info"><view class="product-name">{{product.d}}</view><view class="product-category">{{product.e}}</view><view class="product-price"><text class="price">¥{{product.f}}</text><text wx:if="{{product.g}}" class="original-price">¥{{product.h}}</text></view><view class="product-stats"><text class="sales-count">销量 {{product.i}}</text><text class="inventory">库存 {{product.j}}</text></view></view></view><view class="{{['product-status', product.l && 'status-off']}}">{{product.k}}</view><view class="quick-actions" catchtap="{{product.p}}"><view class="action-btn" bindtap="{{product.n}}">{{product.m}}</view><view class="action-btn edit" bindtap="{{product.o}}"> 编辑 </view></view></view></view><view wx:if="{{x}}" class="loading-more"><text class="loading-text">加载中...</text></view><view wx:if="{{y}}" class="no-data"><image src="{{z}}" class="no-data-icon"></image><text class="no-data-text">{{A}}</text></view><view class="safe-area-bottom"></view></scroll-view><view wx:if="{{E}}" class="float-actions"><view class="float-btn select-mode-btn" bindtap="{{F}}"><text class="float-btn-text">批量管理</text></view></view><view wx:if="{{G}}" class="modal-overlay"></view><view wx:if="{{H}}" class="delete-modal"><view class="modal-header"><text class="modal-title">删除商品</text></view><view class="modal-content"><view class="confirm-message">确定要删除所选的 {{I}} 个商品吗？</view><view class="confirm-warning">删除后将无法恢复，请谨慎操作</view></view><view class="modal-footer"><button class="modal-btn cancel" bindtap="{{J}}">取消</button><button class="modal-btn confirm delete" bindtap="{{K}}">删除</button></view></view></view>