/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.user-profile-card {
  width: 100%;
  background-color: #ffffff;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}
.user-profile-card .user-profile-content {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.user-profile-card .user-profile-content .user-avatar-container {
  position: relative;
  margin-right: 16px;
}
.user-profile-card .user-profile-content .user-avatar-container .user-avatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: #f5f5f5;
  border: 0.5px solid rgba(60, 60, 67, 0.1);
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
}
.user-profile-card .user-profile-content .user-info {
  flex: 1;
}
.user-profile-card .user-profile-content .user-info .user-name-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 6px;
}
.user-profile-card .user-profile-content .user-info .user-name-row .user-name {
  font-size: 18px;
  font-weight: 600;
  color: #1d1d1f;
  margin-right: 8px;
  letter-spacing: -0.2px;
}
.user-profile-card .user-profile-content .user-info .user-name-row .user-vip-tag {
  background-color: #007AFF;
  color: #ffffff;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
  letter-spacing: -0.1px;
}
.user-profile-card .user-profile-content .user-info .user-id-row {
  font-size: 13px;
  color: #86868b;
  margin-bottom: 6px;
  letter-spacing: -0.1px;
}
.user-profile-card .user-profile-content .user-info .user-bio {
  font-size: 14px;
  color: #515154;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: -0.1px;
  line-height: 1.4;
}
.user-profile-card .card-actions {
  display: flex;
  margin-top: 16px;
  justify-content: space-between;
}
.user-profile-card .card-actions .action-button {
  width: 48%;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  border-radius: 8px;
  padding: 0;
  margin: 0;
  line-height: 1;
  font-weight: 500;
  letter-spacing: -0.1px;
  transition: all 0.2s ease;
}
.user-profile-card .card-actions .action-button.edit-profile {
  background-color: #f5f5f7;
  color: #1d1d1f;
}
.user-profile-card .card-actions .action-button.edit-profile:active {
  background-color: #e5e5ea;
}
.user-profile-card .card-actions .action-button.data-analysis {
  background-color: #007AFF;
  color: #ffffff;
}
.user-profile-card .card-actions .action-button.data-analysis:active {
  background-color: #0071e3;
}