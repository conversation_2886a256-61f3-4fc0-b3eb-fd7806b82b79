/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.points-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF9500, #FF5E3A);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(255, 149, 0, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 通用部分样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.edit-btn, .add-btn {
  display: flex;
  align-items: center;
  background: #FF9500;
  border-radius: 15px;
  padding: 5px 12px;
  color: white;
}
.btn-text {
  font-size: 13px;
  margin-right: 5px;
}
.edit-icon {
  width: 14px;
  height: 14px;
  position: relative;
}
.edit-icon:before {
  content: "";
  position: absolute;
  width: 10px;
  height: 10px;
  border: 1.5px solid white;
  border-radius: 1px;
  transform: rotate(45deg);
  top: 1px;
  left: 1px;
}
.arrow-icon {
  width: 8px;
  height: 8px;
  border-top: 2px solid #fff;
  border-right: 2px solid #fff;
  transform: rotate(45deg);
}

/* 概览部分样式 */
.overview-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}
.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.date-picker {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
}
.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}
.date-icon {
  width: 8px;
  height: 8px;
  border-top: 2px solid #666;
  border-right: 2px solid #666;
  transform: rotate(135deg);
}

/* 统计卡片样式 */
.stats-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}
.stats-card {
  width: 50%;
  padding: 7.5px;
  box-sizing: border-box;
  position: relative;
}
.card-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  background: #F8FAFC;
  padding: 15px;
  border-radius: 10px;
  border-left: 3px solid #FF9500;
}
.card-label {
  font-size: 12px;
  color: #999;
  position: absolute;
  bottom: 20px;
  left: 25px;
}
.card-trend {
  position: absolute;
  bottom: 20px;
  right: 25px;
  display: flex;
  align-items: center;
  font-size: 12px;
}
.card-trend.up {
  color: #34C759;
}
.card-trend.down {
  color: #FF3B30;
}
.trend-arrow {
  width: 0;
  height: 0;
  margin-right: 3px;
}
.card-trend.up .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 6px solid #34C759;
}
.card-trend.down .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 6px solid #FF3B30;
}

/* 积分规则样式 */
.rules-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}
.rules-list {
  margin-top: 10px;
}
.rule-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}
.rule-item:last-child {
  border-bottom: none;
}
.rule-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}
.svg-icon {
  width: 24px;
  height: 24px;
}
.rule-icon.purchase {
  background: rgba(255, 149, 0, 0.1);
}
.rule-icon.checkin {
  background: rgba(52, 199, 89, 0.1);
}
.rule-icon.review {
  background: rgba(0, 122, 255, 0.1);
}
.rule-icon.share {
  background: rgba(255, 45, 85, 0.1);
}
.rule-content {
  flex: 1;
}
.rule-name {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 3px;
}
.rule-desc {
  font-size: 12px;
  color: #999;
}
.rule-value {
  margin-left: 10px;
}
.value-text {
  font-size: 14px;
  color: #FF9500;
  font-weight: 500;
}

/* 积分商品样式 */
.products-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}
.products-scroll {
  white-space: nowrap;
  margin: 0 -15px;
  padding: 0 15px;
}
.products-container {
  display: inline-flex;
  padding: 5px 0;
}
.product-card {
  width: 140px;
  border-radius: 12px;
  overflow: hidden;
  margin-right: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: white;
}
.product-image {
  width: 140px;
  height: 140px;
  background: #f5f5f5;
}
.product-info {
  padding: 10px;
  position: relative;
}
.product-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  display: block;
  white-space: normal;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.product-points {
  display: flex;
  align-items: center;
}
.points-value {
  font-size: 16px;
  font-weight: 600;
  color: #FF9500;
}
.points-label {
  font-size: 12px;
  color: #999;
  margin-left: 3px;
}
.product-status {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  color: white;
}
.product-status.hot {
  background: #FF3B30;
}
.product-status.new {
  background: #34C759;
}

/* 响应式调整 */
@media screen and (max-width: 375px) {
.stats-card {
    width: 100%;
}
}