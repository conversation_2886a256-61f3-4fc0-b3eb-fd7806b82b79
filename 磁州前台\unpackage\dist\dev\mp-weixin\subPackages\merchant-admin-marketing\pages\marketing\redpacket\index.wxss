/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.redpacket-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(255, 0, 0, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 通用部分样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.add-btn {
  display: flex;
  align-items: center;
  background: #FF3B30;
  border-radius: 15px;
  padding: 5px 12px;
  color: white;
}
.btn-text {
  font-size: 13px;
  margin-right: 5px;
}
.plus-icon-small {
  width: 12px;
  height: 12px;
  position: relative;
}
.plus-icon-small:before,
.plus-icon-small:after {
  content: "";
  position: absolute;
  background: white;
}
.plus-icon-small:before {
  width: 12px;
  height: 2px;
  top: 5px;
  left: 0;
}
.plus-icon-small:after {
  height: 12px;
  width: 2px;
  left: 5px;
  top: 0;
}

/* 概览部分样式 */
.overview-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}
.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.date-picker {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
}
.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}
.date-icon {
  width: 8px;
  height: 8px;
  border-top: 2px solid #666;
  border-right: 2px solid #666;
  transform: rotate(135deg);
}

/* 统计卡片样式 */
.stats-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}
.stats-card {
  width: 50%;
  padding: 7.5px;
  box-sizing: border-box;
  position: relative;
}
.card-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  background: #F8FAFC;
  padding: 15px;
  border-radius: 10px;
  border-left: 3px solid #FF3B30;
}
.card-label {
  font-size: 12px;
  color: #999;
  position: absolute;
  bottom: 20px;
  left: 25px;
}
.card-trend {
  position: absolute;
  bottom: 20px;
  right: 25px;
  display: flex;
  align-items: center;
  font-size: 12px;
}
.card-trend.up {
  color: #34C759;
}
.card-trend.down {
  color: #FF3B30;
}
.trend-arrow {
  width: 0;
  height: 0;
  margin-right: 3px;
}
.card-trend.up .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 6px solid #34C759;
}
.card-trend.down .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 6px solid #FF3B30;
}

/* 红包列表样式 */
.redpacket-list-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}
.tab-header {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 15px;
}
.tab-item {
  padding: 0 15px 10px;
  font-size: 14px;
  color: #666;
  position: relative;
}
.tab-item.active {
  color: #FF3B30;
  font-weight: 500;
}
.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 15px;
  right: 15px;
  height: 2px;
  background: #FF3B30;
  border-radius: 1px;
}
.redpacket-list {
  margin-top: 15px;
}
.redpacket-item {
  background: #FFFFFF;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 15px;
  overflow: hidden;
}
.redpacket-header {
  display: flex;
  justify-content: space-between;
  padding: 10px 15px;
  background: #FFF8F8;
  border-bottom: 1px solid #FFE8E8;
}
.redpacket-type {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}
.type-fixed {
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}
.type-random {
  background: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}
.redpacket-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}
.status-active {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}
.status-upcoming {
  background: rgba(0, 122, 255, 0.1);
  color: #007AFF;
}
.status-ended {
  background: rgba(142, 142, 147, 0.1);
  color: #8E8E93;
}
.status-draft {
  background: rgba(255, 204, 0, 0.1);
  color: #FFCC00;
}
.redpacket-content {
  display: flex;
  padding: 15px;
}
.redpacket-icon {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  margin-right: 15px;
  position: relative;
}
.redpacket-icon.fixed {
  background: linear-gradient(135deg, #FF5858, #FF0000);
}
.redpacket-icon.fixed::before,
.redpacket-icon.random::before {
  content: "¥";
  position: absolute;
  color: white;
  font-size: 24px;
  font-weight: bold;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.redpacket-icon.random {
  background: linear-gradient(135deg, #FF9500, #FF5E3A);
}
.redpacket-info {
  flex: 1;
}
.redpacket-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  display: block;
}
.redpacket-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 5px;
  display: block;
}
.redpacket-amount {
  font-size: 12px;
}
.amount-label {
  color: #999;
}
.amount-value {
  color: #FF3B30;
  font-weight: 500;
}
.redpacket-stats {
  padding: 0 15px 15px;
  border-bottom: 1px solid #f0f0f0;
}
.stat-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}
.stat-row:last-child {
  margin-bottom: 0;
}
.stat-label {
  font-size: 12px;
  color: #999;
}
.stat-value {
  font-size: 12px;
  color: #333;
  font-weight: 500;
}
.redpacket-actions {
  display: flex;
  padding: 10px 15px;
}
.action-btn {
  font-size: 12px;
  color: #FF3B30;
  background: rgba(255, 59, 48, 0.1);
  padding: 5px 10px;
  border-radius: 15px;
  margin-right: 10px;
}
.action-btn.delete {
  color: #FF3B30;
  background: rgba(255, 59, 48, 0.1);
}
.empty-state {
  padding: 30px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.empty-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 59, 48, 0.1);
  border-radius: 30px;
  margin-bottom: 15px;
  position: relative;
}
.empty-icon::before {
  content: "";
  width: 30px;
  height: 30px;
  border: 2px solid #FF3B30;
  border-radius: 15px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.empty-icon::after {
  content: "";
  width: 2px;
  height: 15px;
  background: #FF3B30;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
}
.empty-text {
  font-size: 14px;
  color: #999;
}

/* 营销工具样式 */
.tools-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}
.tools-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}
.tool-card {
  width: 25%;
  padding: 7.5px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.tool-icon {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}
.icon-image {
  width: 24px;
  height: 24px;
}
.tool-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 3px;
  text-align: center;
}
.tool-desc {
  font-size: 10px;
  color: #999;
  text-align: center;
  height: 28px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 红包模板样式 */
.templates-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}
.templates-scroll {
  white-space: nowrap;
  margin: 0 -15px;
  padding: 0 15px;
}
.templates-container {
  display: inline-flex;
  padding: 5px 0;
}
.template-card {
  width: 150px;
  border-radius: 12px;
  overflow: hidden;
  margin-right: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.template-preview {
  height: 100px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.template-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}
.template-name {
  color: white;
  font-size: 14px;
  font-weight: 600;
}
.template-footer {
  padding: 10px;
  background: white;
  position: relative;
}
.template-desc {
  font-size: 12px;
  color: #999;
  margin-bottom: 25px;
  display: block;
}
.template-use-btn {
  position: absolute;
  bottom: 10px;
  right: 10px;
  font-size: 12px;
  color: #FF3B30;
  background: rgba(255, 59, 48, 0.1);
  padding: 3px 8px;
  border-radius: 10px;
}

/* 营销指南样式 */
.guide-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}
.guide-list {
  margin-top: 10px;
}
.guide-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}
.guide-item:last-child {
  border-bottom: none;
}
.guide-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}
.guide-content {
  flex: 1;
}
.guide-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 3px;
}
.guide-desc {
  font-size: 12px;
  color: #999;
}
.guide-arrow {
  width: 8px;
  height: 8px;
  border-top: 1.5px solid #999;
  border-right: 1.5px solid #999;
  transform: rotate(45deg);
  margin-left: 15px;
}

/* 浮动操作按钮 */
.floating-action-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  box-shadow: 0 4px 15px rgba(255, 59, 48, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}
.fab-icon {
  font-size: 28px;
  color: #fff;
  font-weight: 300;
  line-height: 1;
  margin-top: -2px;
}

/* 响应式调整 */
@media screen and (max-width: 375px) {
.tool-card {
    width: 33.33%;
}
}
@media screen and (max-width: 320px) {
.stats-card {
    width: 100%;
}
.tool-card {
    width: 50%;
}
}