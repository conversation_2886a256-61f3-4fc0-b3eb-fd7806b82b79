<template>
  <view class="business-container">
    <!-- 分类选择器 -->
    <view class="category-selector">
      <scroll-view scroll-x="true" class="category-scroll">
        <view 
          v-for="(item, index) in categories" 
          :key="index" 
          class="category-item" 
          :class="{ active: currentCategory === item }"
          @tap="selectCategory(item)"
        >
          {{ item }}
        </view>
      </scroll-view>
    </view>
    
    <!-- 商店列表 -->
    <view class="shop-list">
      <view 
        v-for="shop in shopList" 
        :key="shop.id" 
        class="shop-item"
        @tap="goToDetail(shop.id)"
      >
        <image class="shop-image" :src="shop.images[0]" mode="aspectFill"></image>
        <view class="shop-info">
          <view class="shop-name">
            {{ shop.name }}
            <text v-if="shop.isOfficial" class="official-tag">官方</text>
          </view>
          <view class="shop-rating">
            <text class="rating-score">{{ shop.rating }}</text>
            <text class="rating-sales">月售{{ shop.sales }}单</text>
          </view>
          <view class="shop-tags">
            <text v-for="(tag, index) in shop.tags" :key="index" class="tag">{{ tag }}</text>
          </view>
          <view class="shop-address">
            <text>{{ shop.address }}</text>
            <text class="distance">{{ shop.distance }}km</text>
          </view>
          <view v-if="shop.promotion" class="shop-promotion">{{ shop.promotion }}</view>
        </view>
      </view>
    </view>
    
    <!-- 加载状态 -->
    <view v-if="loading" class="loading">
      <text>加载中...</text>
    </view>
    <view v-if="!loading && shopList.length === 0" class="empty">
      <text>暂无数据</text>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import api from '@/mock/api';

// 响应式数据
const categories = ref(['全部']);
const shopList = ref([]);
const currentCategory = ref('全部');
const loading = ref(false);

// 获取商圈分类
const fetchCategories = async () => {
  try {
    const result = await api.business.getCategories();
    categories.value = ['全部', ...result];
  } catch (error) {
    console.error('获取分类失败', error);
  }
};

// 获取商店列表
const fetchShopList = async (category) => {
  loading.value = true;
  try {
    const result = await api.business.getShopList(category);
    shopList.value = result;
  } catch (error) {
    console.error('获取商店列表失败', error);
  } finally {
    loading.value = false;
  }
};

// 选择分类
const selectCategory = (category) => {
  currentCategory.value = category;
};

// 跳转到商店详情页
const goToDetail = (id) => {
  uni.navigateTo({
    url: `/subPackages/business/pages/detail?id=${id}`
  });
};

// 监听分类变化
watch(currentCategory, (newCategory) => {
  fetchShopList(newCategory);
});

// 组件挂载时执行
onMounted(() => {
  fetchCategories();
  fetchShopList('全部');
});
</script>

<style scoped>
.business-container {
  padding: 0 20rpx;
}

.category-selector {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #fff;
  padding: 20rpx 0;
}

.category-scroll {
  white-space: nowrap;
}

.category-item {
  display: inline-block;
  padding: 15rpx 30rpx;
  margin-right: 20rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  background-color: #f5f5f5;
}

.category-item.active {
  background-color: #1989fa;
  color: #fff;
}

.shop-list {
  margin-top: 20rpx;
}

.shop-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.shop-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.shop-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.shop-name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.official-tag {
  font-size: 22rpx;
  color: #fff;
  background-color: #ff6b00;
  padding: 4rpx 10rpx;
  border-radius: 4rpx;
  margin-left: 10rpx;
}

.shop-rating {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.rating-score {
  color: #ff6b00;
  font-weight: bold;
  margin-right: 10rpx;
}

.rating-sales {
  color: #999;
  font-size: 24rpx;
}

.shop-tags {
  margin-bottom: 10rpx;
}

.tag {
  display: inline-block;
  font-size: 22rpx;
  color: #666;
  background-color: #f5f5f5;
  padding: 4rpx 10rpx;
  margin-right: 10rpx;
  border-radius: 4rpx;
}

.shop-address {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.distance {
  color: #999;
}

.shop-promotion {
  font-size: 24rpx;
  color: #ff6b00;
}

.loading, .empty {
  text-align: center;
  padding: 30rpx 0;
  color: #999;
}
</style> 