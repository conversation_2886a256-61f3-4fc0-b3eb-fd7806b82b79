{"version": 3, "file": "index.js", "sources": ["subPackages/merchant-admin-home/pages/settings/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4taG9tZVxwYWdlc1xzZXR0aW5nc1xpbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"settings-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">系统设置</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 账号安全模块 -->\r\n    <view class=\"settings-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">账号安全</text>\r\n      </view>\r\n      \r\n      <view class=\"settings-menu\">\r\n        <!-- 密码管理 -->\r\n        <view class=\"menu-item\" @click=\"navigateTo('./security/password')\">\r\n          <view class=\"item-left\">\r\n            <view class=\"item-icon password-icon\">\r\n              <svg viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n                <rect x=\"3\" y=\"11\" width=\"18\" height=\"11\" rx=\"2\" ry=\"2\"></rect>\r\n                <path d=\"M7 11V7a5 5 0 0110 0v4\"></path>\r\n              </svg>\r\n            </view>\r\n            <text class=\"item-name\">密码管理</text>\r\n          </view>\r\n          <view class=\"item-right\">\r\n            <text class=\"item-desc\">双因素认证</text>\r\n            <view class=\"arrow-icon\"></view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 登录设备管理 -->\r\n        <view class=\"menu-item\" @click=\"navigateTo('./security/devices')\">\r\n          <view class=\"item-left\">\r\n            <view class=\"item-icon device-icon\">\r\n              <svg viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n                <rect x=\"5\" y=\"2\" width=\"14\" height=\"20\" rx=\"2\" ry=\"2\"></rect>\r\n                <line x1=\"12\" y1=\"18\" x2=\"12\" y2=\"18\"></line>\r\n              </svg>\r\n            </view>\r\n            <text class=\"item-name\">登录设备管理</text>\r\n          </view>\r\n          <view class=\"item-right\">\r\n            <text class=\"item-desc\">3台设备</text>\r\n            <view class=\"arrow-icon\"></view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 操作日志查询 -->\r\n        <view class=\"menu-item\" @click=\"navigateTo('./security/logs')\">\r\n          <view class=\"item-left\">\r\n            <view class=\"item-icon log-icon\">\r\n              <svg viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n                <path d=\"M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z\"></path>\r\n                <polyline points=\"14 2 14 8 20 8\"></polyline>\r\n                <line x1=\"16\" y1=\"13\" x2=\"8\" y2=\"13\"></line>\r\n                <line x1=\"16\" y1=\"17\" x2=\"8\" y2=\"17\"></line>\r\n                <polyline points=\"10 9 9 9 8 9\"></polyline>\r\n              </svg>\r\n            </view>\r\n            <text class=\"item-name\">操作日志查询</text>\r\n          </view>\r\n          <view class=\"item-right\">\r\n            <view class=\"arrow-icon\"></view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 员工权限管理模块 -->\r\n    <view class=\"settings-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">员工权限管理</text>\r\n      </view>\r\n      \r\n      <view class=\"settings-menu\">\r\n        <!-- 角色设置 -->\r\n        <view class=\"menu-item\" @click=\"navigateTo('./staff/roles')\">\r\n          <view class=\"item-left\">\r\n            <view class=\"item-icon role-icon\">\r\n              <svg viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n                <path d=\"M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2\"></path>\r\n                <circle cx=\"9\" cy=\"7\" r=\"4\"></circle>\r\n                <path d=\"M23 21v-2a4 4 0 00-3-3.87\"></path>\r\n                <path d=\"M16 3.13a4 4 0 010 7.75\"></path>\r\n              </svg>\r\n            </view>\r\n            <text class=\"item-name\">角色设置</text>\r\n          </view>\r\n          <view class=\"item-right\">\r\n            <text class=\"item-desc\">管理员、运营、客服等</text>\r\n            <view class=\"arrow-icon\"></view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 权限配置 -->\r\n        <view class=\"menu-item\" @click=\"navigateTo('./staff/permissions')\">\r\n          <view class=\"item-left\">\r\n            <view class=\"item-icon permission-icon\">\r\n              <svg viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n                <path d=\"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z\"></path>\r\n              </svg>\r\n            </view>\r\n            <text class=\"item-name\">权限配置</text>\r\n          </view>\r\n          <view class=\"item-right\">\r\n            <text class=\"item-desc\">功能权限、数据权限</text>\r\n            <view class=\"arrow-icon\"></view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 操作用户 -->\r\n        <view class=\"menu-item\" @click=\"navigateTo('./staff/users')\">\r\n          <view class=\"item-left\">\r\n            <view class=\"item-icon user-icon\">\r\n              <svg viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n                <path d=\"M20 21v-2a4 4 0 00-4-4H8a4 4 0 00-4 4v2\"></path>\r\n                <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\r\n              </svg>\r\n            </view>\r\n            <text class=\"item-name\">操作用户</text>\r\n          </view>\r\n          <view class=\"item-right\">\r\n            <text class=\"item-desc\">关联操作记录与追踪</text>\r\n            <view class=\"arrow-icon\"></view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 通知设置模块 -->\r\n    <view class=\"settings-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">通知设置</text>\r\n      </view>\r\n      \r\n      <view class=\"settings-menu\">\r\n        <!-- 通知渠道设置 -->\r\n        <view class=\"menu-item\" @click=\"navigateTo('./notification/channels')\">\r\n          <view class=\"item-left\">\r\n            <view class=\"item-icon channel-icon\">\r\n              <svg viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n                <path d=\"M22 16.92v3a2 2 0 01-2.18 2 19.79 19.79 0 01-8.63-3.07 19.5 19.5 0 01-6-6 19.79 19.79 0 01-3.07-8.67A2 2 0 014.11 2h3a2 2 0 012 1.72 12.84 12.84 0 00.7 2.81 2 2 0 01-.45 2.11L8.09 9.91a16 16 0 006 6l1.27-1.27a2 2 0 012.11-.45 12.84 12.84 0 002.81.7A2 2 0 0122 16.92z\"></path>\r\n              </svg>\r\n            </view>\r\n            <text class=\"item-name\">通知渠道设置</text>\r\n          </view>\r\n          <view class=\"item-right\">\r\n            <text class=\"item-desc\">短信、微信、APP推送</text>\r\n            <view class=\"arrow-icon\"></view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 通知类型设置 -->\r\n        <view class=\"menu-item\" @click=\"navigateTo('./notification/types')\">\r\n          <view class=\"item-left\">\r\n            <view class=\"item-icon type-icon\">\r\n              <svg viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n                <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\r\n                <line x1=\"12\" y1=\"8\" x2=\"12\" y2=\"16\"></line>\r\n                <line x1=\"8\" y1=\"12\" x2=\"16\" y2=\"12\"></line>\r\n              </svg>\r\n            </view>\r\n            <text class=\"item-name\">通知类型设置</text>\r\n          </view>\r\n          <view class=\"item-right\">\r\n            <text class=\"item-desc\">订单、营销、系统</text>\r\n            <view class=\"arrow-icon\"></view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 消息模板管理 -->\r\n        <view class=\"menu-item\" @click=\"navigateTo('./notification/templates')\">\r\n          <view class=\"item-left\">\r\n            <view class=\"item-icon template-icon\">\r\n              <svg viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n                <path d=\"M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z\"></path>\r\n                <polyline points=\"14 2 14 8 20 8\"></polyline>\r\n                <line x1=\"16\" y1=\"13\" x2=\"8\" y2=\"13\"></line>\r\n                <line x1=\"16\" y1=\"17\" x2=\"8\" y2=\"17\"></line>\r\n                <polyline points=\"10 9 9 9 8 9\"></polyline>\r\n              </svg>\r\n            </view>\r\n            <text class=\"item-name\">消息模板管理</text>\r\n          </view>\r\n          <view class=\"item-right\">\r\n            <text class=\"item-desc\">自定义通知内容</text>\r\n            <view class=\"arrow-icon\"></view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 应用集成模块 -->\r\n    <view class=\"settings-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">应用集成</text>\r\n      </view>\r\n      \r\n      <view class=\"settings-menu\">\r\n        <!-- 打印机设置 -->\r\n        <view class=\"menu-item\" @click=\"navigateTo('./integration/printer')\">\r\n          <view class=\"item-left\">\r\n            <view class=\"item-icon printer-icon\">\r\n              <svg viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n                <polyline points=\"6 9 6 2 18 2 18 9\"></polyline>\r\n                <path d=\"M6 18H4a2 2 0 01-2-2v-5a2 2 0 012-2h16a2 2 0 012 2v5a2 2 0 01-2 2h-2\"></path>\r\n                <rect x=\"6\" y=\"14\" width=\"12\" height=\"8\"></rect>\r\n              </svg>\r\n            </view>\r\n            <text class=\"item-name\">打印机设置</text>\r\n          </view>\r\n          <view class=\"item-right\">\r\n            <text class=\"item-desc\">小票打印、发票打印</text>\r\n            <view class=\"arrow-icon\"></view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 物流对接 -->\r\n        <view class=\"menu-item\" @click=\"navigateTo('./integration/logistics')\">\r\n          <view class=\"item-left\">\r\n            <view class=\"item-icon logistics-icon\">\r\n              <svg viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n                <rect x=\"1\" y=\"3\" width=\"15\" height=\"13\"></rect>\r\n                <polygon points=\"16 8 20 8 23 11 23 16 16 16 16 8\"></polygon>\r\n                <circle cx=\"5.5\" cy=\"18.5\" r=\"2.5\"></circle>\r\n                <circle cx=\"18.5\" cy=\"18.5\" r=\"2.5\"></circle>\r\n              </svg>\r\n            </view>\r\n            <text class=\"item-name\">物流对接</text>\r\n          </view>\r\n          <view class=\"item-right\">\r\n            <text class=\"item-desc\">快递公司对接、运费模板</text>\r\n            <view class=\"arrow-icon\"></view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 支付设置 -->\r\n        <view class=\"menu-item\" @click=\"navigateTo('./integration/payment')\">\r\n          <view class=\"item-left\">\r\n            <view class=\"item-icon payment-icon\">\r\n              <svg viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n                <rect x=\"2\" y=\"4\" width=\"20\" height=\"16\" rx=\"2\" ry=\"2\"></rect>\r\n                <line x1=\"2\" y1=\"10\" x2=\"22\" y2=\"10\"></line>\r\n              </svg>\r\n            </view>\r\n            <text class=\"item-name\">支付设置</text>\r\n          </view>\r\n          <view class=\"item-right\">\r\n            <text class=\"item-desc\">支付渠道、结算周期</text>\r\n            <view class=\"arrow-icon\"></view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 第三方应用 -->\r\n        <view class=\"menu-item\" @click=\"navigateTo('./integration/apps')\">\r\n          <view class=\"item-left\">\r\n            <view class=\"item-icon app-icon\">\r\n              <svg viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n                <rect x=\"3\" y=\"3\" width=\"7\" height=\"7\"></rect>\r\n                <rect x=\"14\" y=\"3\" width=\"7\" height=\"7\"></rect>\r\n                <rect x=\"14\" y=\"14\" width=\"7\" height=\"7\"></rect>\r\n                <rect x=\"3\" y=\"14\" width=\"7\" height=\"7\"></rect>\r\n              </svg>\r\n            </view>\r\n            <text class=\"item-name\">第三方应用</text>\r\n          </view>\r\n          <view class=\"item-right\">\r\n            <text class=\"item-desc\">营销工具、数据分析</text>\r\n            <view class=\"arrow-icon\"></view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      \r\n    }\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    navigateTo(url) {\r\n      uni.navigateTo({ url });\r\n    },\r\n    showHelp() {\r\n      uni.showToast({\r\n        title: '帮助中心功能开发中',\r\n        icon: 'none'\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.settings-container {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n  padding-bottom: 20px;\r\n}\r\n\r\n.navbar {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 44px 16px 10px;\r\n  background: linear-gradient(135deg, #1677FF, #065DD2);\r\n  position: relative;\r\n  z-index: 100;\r\n}\r\n\r\n.navbar-back {\r\n  width: 40px;\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  color: #fff;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  flex: 1;\r\n  text-align: center;\r\n}\r\n\r\n.navbar-right {\r\n  width: 40px;\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.help-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #fff;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n}\r\n\r\n.settings-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header {\r\n  padding: 16px 16px 8px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.settings-menu {\r\n  background-color: #fff;\r\n}\r\n\r\n.menu-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.menu-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.item-left {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.item-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 12px;\r\n}\r\n\r\n.password-icon, .log-icon {\r\n  background-color: #e6f7ff;\r\n  color: #1677FF;\r\n}\r\n\r\n.device-icon, .role-icon {\r\n  background-color: #fff7e6;\r\n  color: #fa8c16;\r\n}\r\n\r\n.permission-icon, .user-icon {\r\n  background-color: #f6ffed;\r\n  color: #52c41a;\r\n}\r\n\r\n.channel-icon, .type-icon {\r\n  background-color: #fcf4ff;\r\n  color: #722ed1;\r\n}\r\n\r\n.template-icon, .printer-icon {\r\n  background-color: #fff2e8;\r\n  color: #fa541c;\r\n}\r\n\r\n.logistics-icon, .payment-icon {\r\n  background-color: #f0f5ff;\r\n  color: #2f54eb;\r\n}\r\n\r\n.app-icon {\r\n  background-color: #fff0f6;\r\n  color: #eb2f96;\r\n}\r\n\r\n.item-name {\r\n  font-size: 16px;\r\n  color: #333;\r\n}\r\n\r\n.item-right {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.item-desc {\r\n  font-size: 14px;\r\n  color: #999;\r\n  margin-right: 8px;\r\n}\r\n\r\n.arrow-icon {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-top: 1px solid #ccc;\r\n  border-right: 1px solid #ccc;\r\n  transform: rotate(45deg);\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-home/pages/settings/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA4RA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO,CAEP;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,WAAW,KAAK;AACdA,oBAAAA,MAAI,WAAW,EAAE,IAAE,CAAG;AAAA,IACvB;AAAA,IACD,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/SA,GAAG,WAAW,eAAe;"}