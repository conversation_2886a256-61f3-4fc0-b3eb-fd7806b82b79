/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.contact-history-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
}

/* 自定义导航栏 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #1677FF;
}
.header-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
}
.left-action, .right-action {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-icon {
  width: 24px;
  height: 24px;
}
.back-icon {
  width: 24px;
  height: 24px;
  /* 图标是黑色的，需要转为白色 */
  filter: brightness(0) invert(1);
}
.title-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 空状态 */
.empty-state {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.empty-text {
  font-size: 28rpx;
  color: #8E8E93;
}

/* 历史列表 */
.history-list {
  position: relative;
  padding: 20rpx 32rpx;
}
.history-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.driver-info {
  display: flex;
  flex-direction: column;
}
.driver-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 4rpx;
}
.driver-phone {
  font-size: 24rpx;
  color: #666666;
}
.contact-time {
  font-size: 24rpx;
  color: #8E8E93;
}

/* 行程信息 */
.trip-info {
  background-color: #F9F9F9;
  border-radius: 12rpx;
  padding: 16rpx;
  margin-bottom: 16rpx;
}
.route-info {
  margin-bottom: 12rpx;
}
.route-point {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}
.point {
  width: 16rpx;
  height: 16rpx;
  border-radius: 8rpx;
  margin-right: 12rpx;
}
.point.start {
  background-color: #34C759;
}
.point.end {
  background-color: #FF3B30;
}
.point-text {
  font-size: 26rpx;
  color: #333333;
}
.route-line {
  width: 2rpx;
  height: 30rpx;
  background-color: #DDDDDD;
  margin-left: 7rpx;
  margin-bottom: 12rpx;
}
.time-info {
  display: flex;
  justify-content: space-between;
}
.departure-date, .departure-time {
  font-size: 24rpx;
  color: #666666;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: flex-end;
}
.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  margin-left: 16rpx;
  font-size: 24rpx;
}
.action-btn.call {
  background-color: #F2F7FD;
  color: #0A84FF;
  border: 1px solid #0A84FF;
}
.action-btn.rate {
  background-color: #0A84FF;
  color: #FFFFFF;
}
.btn-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}
.action-btn.rate .btn-icon {
  filter: brightness(0) invert(1);
}
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  width: 100%;
}