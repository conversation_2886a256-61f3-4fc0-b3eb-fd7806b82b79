<template>
  <view class="product-detail-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="navigateBack">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M19 12H5M12 19l-7-7 7-7" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        <view class="navbar-title">商品详情</view>
        <view class="navbar-right">
          <view class="share-btn" @click="shareProduct">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <path d="M4 12v8a2 2 0 002 2h12a2 2 0 002-2v-8M16 6l-4-4-4 4M12 2v13" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 商品轮播图 -->
      <swiper class="product-swiper" indicator-dots autoplay circular>
        <swiper-item v-for="(img, index) in product.images" :key="index">
          <image class="swiper-image" :src="img" mode="aspectFill" @click="previewImage(product.images, index)"></image>
        </swiper-item>
      </swiper>

      <!-- 商品信息 -->
      <view class="product-info-card">
        <view class="price-row">
          <view class="price-box">
            <text class="price-symbol">¥</text>
            <text class="price-value">{{ product.price }}</text>
            <text class="original-price" v-if="product.originalPrice">¥{{ product.originalPrice }}</text>
          </view>
          <view class="sold-count">已售{{ product.sold }}件</view>
        </view>

        <view class="product-name">{{ product.name }}</view>
        
        <view class="tags-row" v-if="product.tags && product.tags.length">
          <view class="tag" v-for="(tag, index) in product.tags" :key="index">{{ tag }}</view>
        </view>
      </view>

      <!-- 商家信息 -->
      <view class="shop-info-card">
        <view class="shop-header" @click="viewShopDetail">
          <image class="shop-avatar" :src="product.shop.avatar" mode="aspectFill"></image>
          <view class="shop-detail">
            <text class="shop-name">{{ product.shop.name }}</text>
            <view class="shop-rating">
              <view v-for="i in 5" :key="i" class="star" :class="{ 'active': i <= product.shop.rating }">
                <svg viewBox="0 0 24 24" width="12" height="12">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" fill="currentColor" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
              </view>
              <text class="rating-value">{{ product.shop.rating }}</text>
            </view>
          </view>
          <view class="view-shop-btn">
            进店逛逛
            <svg viewBox="0 0 24 24" width="16" height="16">
              <path d="M9 18l6-6-6-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
      </view>

      <!-- 商品规格 -->
      <view class="specs-card">
        <view class="specs-item" @click="showSpecsSelector">
          <text class="specs-label">规格</text>
          <view class="specs-value">
            <text>{{ selectedSpecs || '请选择规格' }}</text>
            <svg viewBox="0 0 24 24" width="16" height="16">
              <path d="M9 18l6-6-6-6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
        
        <view class="specs-item" @click="showDeliveryInfo">
          <text class="specs-label">配送</text>
          <view class="specs-value">
            <text>{{ product.delivery }}</text>
            <svg viewBox="0 0 24 24" width="16" height="16">
              <path d="M9 18l6-6-6-6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
        
        <view class="specs-item" @click="showServiceInfo">
          <text class="specs-label">服务</text>
          <view class="specs-value">
            <view class="service-tags">
              <view class="service-tag" v-for="(service, index) in product.services" :key="index">
                <svg viewBox="0 0 24 24" width="14" height="14">
                  <path d="M20 6L9 17l-5-5" stroke="#07C160" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
                <text>{{ service }}</text>
              </view>
            </view>
            <svg viewBox="0 0 24 24" width="16" height="16">
              <path d="M9 18l6-6-6-6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
      </view>

      <!-- 商品详情 -->
      <view class="detail-card">
        <view class="detail-header">
          <text class="detail-title">商品详情</text>
        </view>
        <view class="detail-content">
          <rich-text :nodes="product.description"></rich-text>
          <view class="detail-images">
            <image 
              v-for="(img, index) in product.detailImages" 
              :key="index" 
              :src="img" 
              mode="widthFix" 
              class="detail-image"
            ></image>
          </view>
        </view>
      </view>

      <!-- 商品评价 -->
      <view class="reviews-card">
        <view class="reviews-header">
          <text class="reviews-title">商品评价({{ product.reviewCount }})</text>
          <view class="view-all" @click="viewAllReviews">
            查看全部
            <svg viewBox="0 0 24 24" width="16" height="16">
              <path d="M9 18l6-6-6-6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
        
        <view class="reviews-content">
          <view class="review-item" v-for="(review, index) in product.reviews" :key="index">
            <view class="review-header">
              <image class="reviewer-avatar" :src="review.avatar" mode="aspectFill"></image>
              <view class="reviewer-info">
                <text class="reviewer-name">{{ review.name }}</text>
                <view class="review-rating">
                  <view v-for="i in 5" :key="i" class="star" :class="{ 'active': i <= review.rating }">
                    <svg viewBox="0 0 24 24" width="12" height="12">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" fill="currentColor" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                  </view>
                  <text class="review-date">{{ review.date }}</text>
                </view>
              </view>
            </view>
            <view class="review-content">{{ review.content }}</view>
            <view class="review-images" v-if="review.images && review.images.length">
              <image 
                v-for="(img, imgIndex) in review.images" 
                :key="imgIndex" 
                :src="img" 
                mode="aspectFill"
                class="review-image"
                @click="previewImage(review.images, imgIndex)"
              ></image>
            </view>
            <view class="specs-info" v-if="review.specs">{{ review.specs }}</view>
          </view>
        </view>
      </view>

      <!-- 推荐商品 -->
      <view class="recommend-card">
        <view class="recommend-header">
          <text class="recommend-title">猜你喜欢</text>
        </view>
        <view class="recommend-list">
          <view 
            class="recommend-item" 
            v-for="(item, index) in recommendProducts" 
            :key="index"
            @click="viewProductDetail(item.id)"
          >
            <image class="recommend-image" :src="item.image" mode="aspectFill"></image>
            <view class="recommend-info">
              <text class="recommend-name">{{ item.name }}</text>
              <view class="recommend-price-row">
                <text class="recommend-price">¥{{ item.price }}</text>
                <text class="recommend-sold">已售{{ item.sold }}件</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="footer-bar">
      <view class="footer-left">
        <view class="action-btn" @click="navigateToShop">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M20 7h-7.05a5 5 0 00-9.9 0H3a1 1 0 00-1 1v1a1 1 0 001 1v8a3 3 0 003 3h8a3 3 0 003-3v-8a1 1 0 001-1V8a1 1 0 00-1-1zM10 20H6a1 1 0 01-1-1v-8h5v9zm8-1a1 1 0 01-1 1h-4v-9h5v8z" stroke="currentColor" fill="none" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <circle cx="10" cy="5" r="2" stroke="currentColor" fill="none" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
          </svg>
          <text>店铺</text>
        </view>
        <view class="action-btn" @click="toggleFavorite">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24" :class="{ 'active': isFavorite }">
            <path d="M20.84 4.61a5.5 5.5 0 00-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 00-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 000-7.78z" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
          <text>收藏</text>
        </view>
        <view class="action-btn" @click="contactService">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M21 11.5a8.38 8.38 0 01-.9 3.8 8.5 8.5 0 01-7.6 4.7 8.38 8.38 0 01-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 01-.9-3.8 8.5 8.5 0 014.7-7.6 8.38 8.38 0 013.8-.9h.5a8.48 8.48 0 018 8v.5z" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
          <text>客服</text>
        </view>
      </view>
      <view class="footer-right">
        <button class="cart-btn" @click="addToCart">加入购物车</button>
        <button class="buy-btn" @click="buyNow">立即购买</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 商品信息
const product = ref({
  id: '1',
  name: '磁州特产手工小酥肉',
  price: 68.00,
  originalPrice: 88.00,
  sold: 1024,
  tags: ['限时特惠', '包邮', '48小时发货'],
  delivery: '免运费',
  services: ['7天无理由退货', '正品保证', '极速退款'],
  description: '<p>磁州特产手工小酥肉，传统工艺制作，选用上等五花肉，经过腌制、裹粉、油炸等多道工序精心制作而成。肉质酥脆，口感香浓，回味无穷。</p>',
  detailImages: [
    '/static/images/products/detail1.jpg',
    '/static/images/products/detail2.jpg',
    '/static/images/products/detail3.jpg'
  ],
  images: [
    '/static/images/products/product1-1.jpg',
    '/static/images/products/product1-2.jpg',
    '/static/images/products/product1-3.jpg'
  ],
  specs: [
    {
      name: '规格',
      options: ['小份(250g)', '中份(500g)', '大份(1000g)']
    },
    {
      name: '口味',
      options: ['原味', '微辣', '麻辣']
    }
  ],
  reviewCount: 238,
  reviews: [
    {
      name: '张先生',
      avatar: '/static/images/avatars/user1.jpg',
      rating: 5,
      date: '2023-06-15',
      content: '味道非常好，包装也很精美，送货速度快，下次还会再买！',
      specs: '规格：中份(500g) 口味：原味',
      images: [
        '/static/images/reviews/review1-1.jpg',
        '/static/images/reviews/review1-2.jpg'
      ]
    },
    {
      name: '李女士',
      avatar: '/static/images/avatars/user2.jpg',
      rating: 4,
      date: '2023-06-10',
      content: '肉质很嫩，味道不错，就是有点咸，下次尝试微辣口味。',
      specs: '规格：小份(250g) 口味：原味',
      images: []
    }
  ],
  shop: {
    id: '101',
    name: '磁州好味食品专营店',
    avatar: '/static/images/shops/shop1.jpg',
    rating: 4.8
  }
});

// 推荐商品
const recommendProducts = ref([
  {
    id: '2',
    name: '磁州传统手工豆腐脑',
    price: 12.80,
    sold: 3256,
    image: '/static/images/products/product2.jpg'
  },
  {
    id: '3',
    name: '磁州特色糖醋里脊',
    price: 48.00,
    sold: 1856,
    image: '/static/images/products/product3.jpg'
  },
  {
    id: '4',
    name: '磁州农家小炒肉',
    price: 38.00,
    sold: 2048,
    image: '/static/images/products/product4.jpg'
  }
]);

// 收藏状态
const isFavorite = ref(false);

// 已选规格
const selectedSpecs = ref('');

// 返回上一页
const navigateBack = () => {
  uni.navigateBack();
};

// 分享商品
const shareProduct = () => {
  uni.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage', 'shareTimeline']
  });
};

// 预览图片
const previewImage = (images, current) => {
  uni.previewImage({
    urls: images,
    current: images[current]
  });
};

// 查看店铺详情
const viewShopDetail = () => {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/shops/detail/index?id=${product.value.shop.id}`
  });
};

// 查看商品详情
const viewProductDetail = (id) => {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/products/detail/index?id=${id}`
  });
};

// 查看全部评价
const viewAllReviews = () => {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/products/reviews?id=${product.value.id}`
  });
};

// 显示规格选择器
const showSpecsSelector = () => {
  uni.showToast({
    title: '正在加载规格选择器...',
    icon: 'none'
  });
  // 实际项目中这里应该显示规格选择弹窗
};

// 显示配送信息
const showDeliveryInfo = () => {
  uni.showModal({
    title: '配送信息',
    content: '本商品支持全国配送，订单满99元免运费，不满99元收取10元运费。预计3-5天送达。',
    showCancel: false
  });
};

// 显示服务信息
const showServiceInfo = () => {
  uni.showModal({
    title: '服务说明',
    content: '7天无理由退货：商品签收后7天内，在不影响二次销售的情况下可申请无理由退货。\n正品保证：所有商品均为正品，假一赔十。\n极速退款：审核通过后24小时内退款到账。',
    showCancel: false
  });
};

// 切换收藏状态
const toggleFavorite = () => {
  isFavorite.value = !isFavorite.value;
  uni.showToast({
    title: isFavorite.value ? '已收藏' : '已取消收藏',
    icon: 'none'
  });
};

// 联系客服
const contactService = () => {
  uni.showToast({
    title: '正在接入客服系统...',
    icon: 'none'
  });
};

// 导航到店铺
const navigateToShop = () => {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/shops/detail/index?id=${product.value.shop.id}`
  });
};

// 加入购物车
const addToCart = () => {
  if (!selectedSpecs.value) {
    showSpecsSelector();
    return;
  }
  
  uni.showToast({
    title: '已加入购物车',
    icon: 'success'
  });
};

// 立即购买
const buyNow = () => {
  if (!selectedSpecs.value) {
    showSpecsSelector();
    return;
  }
  
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/order/confirm?productId=${product.value.id}&specs=${encodeURIComponent(selectedSpecs.value)}`
  });
};

onMounted(() => {
  // 获取商品详情数据
  // 实际项目中这里应该调用API获取数据
});
</script>

<style lang="scss" scoped>
.product-detail-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F2F2F7;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  
  .navbar-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to right, #FF3B69, #FF7A9E);
  }
  
  .navbar-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 0 30rpx;
    padding-top: var(--status-bar-height, 25px);
    box-sizing: border-box;
  }
  
  .navbar-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #FFFFFF;
    letter-spacing: 0.5px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  
  .back-btn, .share-btn {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .back-btn {
    position: absolute;
    left: 30rpx;
  }
  
  .navbar-right {
    position: absolute;
    right: 30rpx;
  }
}

/* 内容区域 */
.content-scroll {
  flex: 1;
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
  margin-bottom: 120rpx; /* 底部操作栏高度 */
}

/* 商品轮播图 */
.product-swiper {
  width: 100%;
  height: 750rpx;
  
  .swiper-image {
    width: 100%;
    height: 100%;
  }
}

/* 商品信息卡片 */
.product-info-card {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
    
    .price-box {
      display: flex;
      align-items: baseline;
      
      .price-symbol {
        font-size: 28rpx;
        font-weight: 600;
        color: #FF3B69;
      }
      
      .price-value {
        font-size: 48rpx;
        font-weight: 600;
        color: #FF3B69;
        margin-right: 16rpx;
      }
      
      .original-price {
        font-size: 24rpx;
        color: #999999;
        text-decoration: line-through;
      }
    }
    
    .sold-count {
      font-size: 24rpx;
      color: #999999;
    }
  }
  
  .product-name {
    font-size: 36rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 16rpx;
    line-height: 1.4;
  }
  
  .tags-row {
    display: flex;
    flex-wrap: wrap;
    
    .tag {
      font-size: 24rpx;
      color: #FF3B69;
      background-color: rgba(255, 59, 105, 0.1);
      border-radius: 6rpx;
      padding: 4rpx 12rpx;
      margin-right: 16rpx;
      margin-bottom: 10rpx;
    }
  }
}

/* 商家信息卡片 */
.shop-info-card {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .shop-header {
    display: flex;
    align-items: center;
    
    .shop-avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      margin-right: 16rpx;
    }
    
    .shop-detail {
      flex: 1;
      
      .shop-name {
        font-size: 28rpx;
        font-weight: 500;
        color: #333333;
        margin-bottom: 6rpx;
      }
      
      .shop-rating {
        display: flex;
        align-items: center;
        
        .star {
          color: #CCCCCC;
          margin-right: 4rpx;
          
          &.active {
            color: #FFCC00;
          }
        }
        
        .rating-value {
          font-size: 24rpx;
          color: #999999;
          margin-left: 6rpx;
        }
      }
    }
    
    .view-shop-btn {
      font-size: 28rpx;
      color: #666666;
      display: flex;
      align-items: center;
      padding: 10rpx 20rpx;
      border: 1px solid #DDDDDD;
      border-radius: 30rpx;
    }
  }
}

/* 规格卡片 */
.specs-card {
  background-color: #FFFFFF;
  padding: 0 30rpx;
  margin-bottom: 20rpx;
  
  .specs-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 0;
    border-bottom: 1px solid #F2F2F7;
    
    &:last-child {
      border-bottom: none;
    }
    
    .specs-label {
      font-size: 28rpx;
      color: #999999;
    }
    
    .specs-value {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      font-size: 28rpx;
      color: #333333;
      
      .service-tags {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-end;
        max-width: 400rpx;
        
        .service-tag {
          display: flex;
          align-items: center;
          margin-left: 16rpx;
          margin-bottom: 6rpx;
          
          svg {
            margin-right: 4rpx;
          }
          
          text {
            font-size: 24rpx;
            color: #666666;
          }
        }
      }
      
      svg {
        margin-left: 10rpx;
      }
    }
  }
}

/* 商品详情卡片 */
.detail-card {
  background-color: #FFFFFF;
  margin-bottom: 20rpx;
  
  .detail-header {
    padding: 30rpx;
    border-bottom: 1px solid #F2F2F7;
    
    .detail-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }
  }
  
  .detail-content {
    padding: 30rpx;
    
    .detail-images {
      margin-top: 20rpx;
      
      .detail-image {
        width: 100%;
        margin-bottom: 20rpx;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

/* 评价卡片 */
.reviews-card {
  background-color: #FFFFFF;
  margin-bottom: 20rpx;
  
  .reviews-header {
    padding: 30rpx;
    border-bottom: 1px solid #F2F2F7;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .reviews-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }
    
    .view-all {
      font-size: 28rpx;
      color: #999999;
      display: flex;
      align-items: center;
    }
  }
  
  .reviews-content {
    padding: 0 30rpx;
    
    .review-item {
      padding: 30rpx 0;
      border-bottom: 1px solid #F2F2F7;
      
      &:last-child {
        border-bottom: none;
      }
      
      .review-header {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;
        
        .reviewer-avatar {
          width: 64rpx;
          height: 64rpx;
          border-radius: 50%;
          margin-right: 16rpx;
        }
        
        .reviewer-info {
          flex: 1;
          
          .reviewer-name {
            font-size: 28rpx;
            font-weight: 500;
            color: #333333;
            margin-bottom: 6rpx;
          }
          
          .review-rating {
            display: flex;
            align-items: center;
            
            .star {
              color: #CCCCCC;
              margin-right: 4rpx;
              
              &.active {
                color: #FFCC00;
              }
            }
            
            .review-date {
              font-size: 24rpx;
              color: #999999;
              margin-left: 10rpx;
            }
          }
        }
      }
      
      .review-content {
        font-size: 28rpx;
        color: #333333;
        line-height: 1.5;
        margin-bottom: 16rpx;
      }
      
      .review-images {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 16rpx;
        
        .review-image {
          width: 160rpx;
          height: 160rpx;
          margin-right: 10rpx;
          margin-bottom: 10rpx;
          border-radius: 8rpx;
        }
      }
      
      .specs-info {
        font-size: 24rpx;
        color: #999999;
      }
    }
  }
}

/* 推荐商品卡片 */
.recommend-card {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-bottom: 30rpx;
  
  .recommend-header {
    margin-bottom: 20rpx;
    
    .recommend-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }
  }
  
  .recommend-list {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    
    .recommend-item {
      width: 240rpx;
      margin-right: 20rpx;
      flex-shrink: 0;
      
      &:last-child {
        margin-right: 0;
      }
      
      .recommend-image {
        width: 240rpx;
        height: 240rpx;
        border-radius: 16rpx;
        margin-bottom: 10rpx;
      }
      
      .recommend-info {
        .recommend-name {
          font-size: 28rpx;
          color: #333333;
          margin-bottom: 6rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        .recommend-price-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .recommend-price {
            font-size: 28rpx;
            font-weight: 600;
            color: #FF3B69;
          }
          
          .recommend-sold {
            font-size: 24rpx;
            color: #999999;
          }
        }
      }
    }
  }
}

/* 底部操作栏 */
.footer-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
  z-index: 100;
  
  .footer-left {
    display: flex;
    
    .action-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 40rpx;
      
      .icon {
        color: #999999;
        margin-bottom: 6rpx;
        
        &.active {
          color: #FF3B69;
        }
      }
      
      text {
        font-size: 24rpx;
        color: #999999;
      }
    }
  }
  
  .footer-right {
    display: flex;
    
    .cart-btn, .buy-btn {
      height: 80rpx;
      line-height: 80rpx;
      border-radius: 40rpx;
      padding: 0 40rpx;
      font-size: 32rpx;
      font-weight: 500;
      border: none;
    }
    
    .cart-btn {
      background-color: #FFF0F5;
      color: #FF3B69;
      margin-right: 20rpx;
    }
    
    .buy-btn {
      background: linear-gradient(to right, #FF3B69, #FF7A9E);
      color: #FFFFFF;
    }
  }
}
</style>