{"version": 3, "file": "create.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/group/create.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xncm91cFxjcmVhdGUudnVl"], "sourcesContent": ["<template>\n  <view class=\"group-create-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">创建拼团活动</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 使用进度步骤组件 -->\n    <progress-steps \n      :steps=\"['选择商品', '拼团设置', '确认创建']\" \n      :current-step=\"currentStep\">\n    </progress-steps>\n    \n    <!-- 表单容器 -->\n    <scroll-view scroll-y class=\"form-scroll-view\" enable-back-to-top :scroll-into-view=\"scrollToId\">\n      <!-- 步骤1: 选择商品 -->\n      <view class=\"form-section\" v-if=\"currentStep === 1\" id=\"step1\">\n        <!-- 使用区块标题组件 -->\n        <section-header title=\"选择商品方式\"></section-header>\n        \n        <view class=\"selection-options\">\n          <view class=\"selection-option\" \n                :class=\"{ active: formData.productSelectionType === 'existing' }\"\n                @tap=\"formData.productSelectionType = 'existing'\">\n            <view class=\"option-radio\">\n              <view class=\"radio-inner\" v-if=\"formData.productSelectionType === 'existing'\"></view>\n            </view>\n            <view class=\"option-content\">\n              <text class=\"option-title\">选择现有商品</text>\n              <text class=\"option-desc\">从已有商品库中选择商品添加到拼团</text>\n            </view>\n          </view>\n          \n          <view class=\"selection-option\"\n                :class=\"{ active: formData.productSelectionType === 'new' }\"\n                @tap=\"formData.productSelectionType = 'new'\">\n            <view class=\"option-radio\">\n              <view class=\"radio-inner\" v-if=\"formData.productSelectionType === 'new'\"></view>\n            </view>\n            <view class=\"option-content\">\n              <text class=\"option-title\">新建拼团商品</text>\n              <text class=\"option-desc\">创建新商品并添加到拼团活动中</text>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 选择现有商品 -->\n        <view class=\"product-selection\" v-if=\"formData.productSelectionType === 'existing'\">\n          <view class=\"search-bar\">\n            <!-- 使用SVG图标组件 -->\n            <svg-icon name=\"search\" size=\"18\" color=\"#999\"></svg-icon>\n            <input class=\"search-input\" placeholder=\"搜索商品名称\" v-model=\"searchKeyword\" @input=\"searchProducts\"/>\n          </view>\n          \n          <view class=\"filter-options\">\n            <view class=\"filter-item\" @tap=\"toggleFilterDropdown('category')\">\n              <text>商品类别</text>\n              <svg-icon name=\"arrow-right\" size=\"14\" color=\"#999\"></svg-icon>\n            </view>\n            <view class=\"filter-item\" @tap=\"toggleFilterDropdown('price')\">\n              <text>价格区间</text>\n              <svg-icon name=\"arrow-right\" size=\"14\" color=\"#999\"></svg-icon>\n            </view>\n            <view class=\"filter-item\" @tap=\"toggleFilterDropdown('stock')\">\n              <text>库存状态</text>\n              <svg-icon name=\"arrow-right\" size=\"14\" color=\"#999\"></svg-icon>\n            </view>\n          </view>\n          \n          <!-- 商品列表 -->\n          <view class=\"product-list\">\n            <view class=\"product-item\" v-for=\"(product, index) in productList\" :key=\"index\" @tap=\"toggleProductSelection(product)\">\n              <view class=\"checkbox\" :class=\"{ checked: isProductSelected(product) }\">\n                <view class=\"checkbox-inner\" v-if=\"isProductSelected(product)\"></view>\n              </view>\n              <image class=\"product-img\" :src=\"product.image\" mode=\"aspectFill\"></image>\n              <view class=\"product-info\">\n                <text class=\"product-name\">{{product.name}}</text>\n                <view class=\"product-price\">\n                  <text class=\"price-current\">¥{{product.price}}</text>\n                  <text class=\"price-original\" v-if=\"product.originalPrice\">¥{{product.originalPrice}}</text>\n                </view>\n                <text class=\"product-stock\">库存: {{product.stock}}件</text>\n              </view>\n            </view>\n          </view>\n          \n          <!-- 已选商品 -->\n          <view class=\"selected-products\" v-if=\"formData.selectedProducts.length > 0\">\n            <view class=\"selected-header\">\n              <text class=\"selected-title\">已选商品({{formData.selectedProducts.length}})</text>\n              <text class=\"clear-all\" @tap=\"clearSelectedProducts\">清空</text>\n            </view>\n            <view class=\"selected-list\">\n              <view class=\"selected-item\" v-for=\"(product, index) in formData.selectedProducts\" :key=\"index\">\n                <image class=\"selected-img\" :src=\"product.image\" mode=\"aspectFill\"></image>\n                <view class=\"selected-info\">\n                  <text class=\"selected-name\">{{product.name}}</text>\n                  <text class=\"selected-price\">¥{{product.price}}</text>\n                </view>\n                <view class=\"remove-btn\" @tap.stop=\"removeProduct(index)\">×</view>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 新建商品 -->\n        <view class=\"create-product\" v-if=\"formData.productSelectionType === 'new'\">\n          <!-- 使用图标按钮组件 -->\n          <icon-button \n            icon=\"plus\" \n            text=\"新建商品\" \n            type=\"primary\" \n            size=\"large\"\n            @click=\"navigateToCreateProduct\">\n          </icon-button>\n          <text class=\"create-product-tip\">创建新商品后可自动关联到拼团活动</text>\n        </view>\n      </view>\n      \n      <!-- 步骤2: 拼团设置 -->\n      <view class=\"form-section\" v-if=\"currentStep === 2\" id=\"step2\">\n        <!-- 使用区块标题组件 -->\n        <section-header title=\"拼团活动设置\"></section-header>\n        \n        <view class=\"form-content\">\n          <!-- 使用表单组件 -->\n          <!-- 拼团活动名称 -->\n          <form-group label=\"活动名称\" :is-required=\"true\">\n            <input class=\"form-input\" type=\"text\" v-model=\"formData.groupName\" placeholder=\"请输入拼团活动名称\" maxlength=\"30\"/>\n            <text class=\"input-counter\">{{formData.groupName.length}}/30</text>\n          </form-group>\n          \n          <!-- 拼团类型选择 -->\n          <form-group label=\"拼团类型\" :is-required=\"true\" tip=\"选择普通拼团或套餐拼团\">\n            <view class=\"radio-group\">\n              <view class=\"radio-item\" \n                :class=\"{ active: formData.groupType === 'normal' }\" \n                @tap=\"formData.groupType = 'normal'\">\n                <view class=\"radio-dot\">\n                  <view class=\"radio-dot-inner\" v-if=\"formData.groupType === 'normal'\"></view>\n                </view>\n                <text class=\"radio-label\">普通拼团</text>\n              </view>\n              <view class=\"radio-item\" \n                :class=\"{ active: formData.groupType === 'package' }\" \n                @tap=\"formData.groupType = 'package'\">\n                <view class=\"radio-dot\">\n                  <view class=\"radio-dot-inner\" v-if=\"formData.groupType === 'package'\"></view>\n                </view>\n                <text class=\"radio-label\">套餐拼团</text>\n              </view>\n            </view>\n          </form-group>\n          \n          <!-- 套餐拼团设置 -->\n          <view class=\"package-settings\" v-if=\"formData.groupType === 'package'\">\n            <form-group label=\"套餐名称\" :is-required=\"true\">\n              <input class=\"form-input\" type=\"text\" v-model=\"formData.packageName\" placeholder=\"如：四菜一汤家庭套餐\" maxlength=\"30\"/>\n              <text class=\"input-counter\">{{formData.packageName.length}}/30</text>\n            </form-group>\n            \n            <form-group label=\"套餐描述\">\n              <textarea class=\"form-textarea\" v-model=\"formData.packageDescription\" placeholder=\"请输入套餐描述信息\" maxlength=\"100\"></textarea>\n              <text class=\"input-counter\">{{formData.packageDescription.length}}/100</text>\n            </form-group>\n            \n            <!-- 套餐商品列表 -->\n            <form-group label=\"套餐商品\" :is-required=\"true\" tip=\"添加组成套餐的商品\">\n              <view class=\"package-items\">\n                <view class=\"package-item\" v-for=\"(item, idx) in formData.packageItems\" :key=\"idx\">\n                  <view class=\"package-item-header\">\n                    <text class=\"package-item-title\">商品 {{idx + 1}}</text>\n                    <view class=\"package-item-remove\" @tap=\"removePackageItem(idx)\" v-if=\"formData.packageItems.length > 1\">×</view>\n                  </view>\n                  <view class=\"package-item-content\">\n                    <view class=\"package-item-row\">\n                      <text class=\"package-item-label\">商品名称:</text>\n                      <input class=\"package-item-input\" v-model=\"item.name\" placeholder=\"请输入商品名称\"/>\n                    </view>\n                    <view class=\"package-item-row\">\n                      <text class=\"package-item-label\">原价(元):</text>\n                      <input class=\"package-item-input\" type=\"digit\" v-model=\"item.originalPrice\" placeholder=\"0.00\"/>\n                    </view>\n                    <view class=\"package-item-row\">\n                      <text class=\"package-item-label\">数量:</text>\n                      <view class=\"number-picker small\">\n                        <view class=\"number-btn minus\" @tap=\"decrementItemQuantity(idx)\">-</view>\n                        <input class=\"number-input\" type=\"number\" v-model=\"item.quantity\" />\n                        <view class=\"number-btn plus\" @tap=\"incrementItemQuantity(idx)\">+</view>\n                      </view>\n                    </view>\n                  </view>\n                </view>\n                \n                <!-- 添加商品按钮 -->\n                <view class=\"add-package-item\" @tap=\"addPackageItem\">\n                  <view class=\"add-icon\">+</view>\n                  <text class=\"add-text\">添加商品</text>\n                </view>\n              </view>\n            </form-group>\n          </view>\n          \n          <!-- 市场价 -->\n          <form-group label=\"市场价\" :is-required=\"true\" tip=\"商品的市场参考价格\">\n            <view class=\"price-input-wrapper\">\n              <text class=\"price-symbol\">¥</text>\n              <input class=\"form-input price-input\" type=\"digit\" v-model=\"formData.marketPrice\" placeholder=\"请输入商品市场价\"/>\n            </view>\n          </form-group>\n          \n          <!-- 日常价 -->\n          <form-group label=\"日常价\" :is-required=\"true\" tip=\"店铺内商品平时销售的价格\">\n            <view class=\"price-input-wrapper\">\n              <text class=\"price-symbol\">¥</text>\n              <input class=\"form-input price-input\" type=\"digit\" v-model=\"formData.regularPrice\" placeholder=\"请输入商品日常价\"/>\n            </view>\n          </form-group>\n          \n          <!-- 拼团价格 -->\n          <form-group label=\"拼团价格\" :is-required=\"true\">\n            <view class=\"price-input-wrapper\">\n              <text class=\"price-symbol\">¥</text>\n              <input class=\"form-input price-input\" type=\"digit\" v-model=\"formData.groupPrice\" placeholder=\"请输入拼团价格\"/>\n            </view>\n          </form-group>\n          \n          <!-- 开启拼团按钮 -->\n          <form-group label=\"开启拼团\" tip=\"开启后需要拼团才能享受优惠价，关闭则无需拼团直接享受优惠\">\n            <view class=\"switch-container\">\n              <switch :checked=\"formData.requireGroup\" @change=\"e => formData.requireGroup = e.detail.value\" color=\"#9040FF\" />\n            </view>\n          </form-group>\n          \n          <!-- 成团人数 -->\n          <form-group label=\"成团人数\" :is-required=\"true\">\n            <view class=\"number-picker\">\n              <view class=\"number-btn minus\" @tap=\"decrementGroupSize\">-</view>\n              <input class=\"number-input\" type=\"number\" v-model=\"formData.minGroupSize\" />\n              <view class=\"number-btn plus\" @tap=\"incrementGroupSize\">+</view>\n            </view>\n          </form-group>\n          \n          <!-- 单人限购 -->\n          <form-group label=\"单人限购\">\n            <view class=\"number-picker\">\n              <view class=\"number-btn minus\" @tap=\"decrementLimitPerUser\">-</view>\n              <input class=\"number-input\" type=\"number\" v-model=\"formData.limitPerUser\" />\n              <view class=\"number-btn plus\" @tap=\"incrementLimitPerUser\">+</view>\n              <text class=\"unit-text\">件</text>\n            </view>\n          </form-group>\n          \n          <!-- 活动有效期 -->\n          <form-group label=\"活动有效期\" :is-required=\"true\">\n            <view class=\"radio-group\">\n              <view class=\"radio-item\" \n                :class=\"{ active: formData.validity.type === 'fixed' }\" \n                @tap=\"formData.validity.type = 'fixed'\">\n                <view class=\"radio-dot\">\n                  <view class=\"radio-dot-inner\" v-if=\"formData.validity.type === 'fixed'\"></view>\n                </view>\n                <text class=\"radio-label\">固定时间段</text>\n              </view>\n              <view class=\"radio-item\" \n                :class=\"{ active: formData.validity.type === 'dynamic' }\" \n                @tap=\"formData.validity.type = 'dynamic'\">\n                <view class=\"radio-dot\">\n                  <view class=\"radio-dot-inner\" v-if=\"formData.validity.type === 'dynamic'\"></view>\n                </view>\n                <text class=\"radio-label\">动态时间段</text>\n              </view>\n            </view>\n            \n            <!-- 固定时间段选择器 -->\n            <view class=\"datetime-picker\" v-if=\"formData.validity.type === 'fixed'\">\n              <picker mode=\"date\" :value=\"formData.validity.startDate\" @change=\"(e) => dateChange('start', e)\" class=\"picker-component\">\n                <view class=\"datetime-field\">\n                  <text class=\"datetime-placeholder\" v-if=\"!formData.validity.startDate\">开始日期</text>\n                  <text class=\"datetime-value\" v-else>{{formData.validity.startDate}}</text>\n                  <view class=\"datetime-icon\"></view>\n                </view>\n              </picker>\n              <picker mode=\"time\" :value=\"formData.validity.startTime\" @change=\"(e) => timeChange('start', e)\" class=\"picker-component\">\n                <view class=\"datetime-field\">\n                  <text class=\"datetime-placeholder\" v-if=\"!formData.validity.startTime\">开始时间</text>\n                  <text class=\"datetime-value\" v-else>{{formData.validity.startTime}}</text>\n                  <view class=\"datetime-icon\"></view>\n                </view>\n              </picker>\n              <text class=\"datetime-separator\">至</text>\n              <picker mode=\"date\" :value=\"formData.validity.endDate\" @change=\"(e) => dateChange('end', e)\" class=\"picker-component\">\n                <view class=\"datetime-field\">\n                  <text class=\"datetime-placeholder\" v-if=\"!formData.validity.endDate\">结束日期</text>\n                  <text class=\"datetime-value\" v-else>{{formData.validity.endDate}}</text>\n                  <view class=\"datetime-icon\"></view>\n                </view>\n              </picker>\n              <picker mode=\"time\" :value=\"formData.validity.endTime\" @change=\"(e) => timeChange('end', e)\" class=\"picker-component\">\n                <view class=\"datetime-field\">\n                  <text class=\"datetime-placeholder\" v-if=\"!formData.validity.endTime\">结束时间</text>\n                  <text class=\"datetime-value\" v-else>{{formData.validity.endTime}}</text>\n                  <view class=\"datetime-icon\"></view>\n                </view>\n              </picker>\n            </view>\n            \n            <!-- 动态时间段设置 -->\n            <view class=\"dynamic-days\" v-if=\"formData.validity.type === 'dynamic'\">\n              <view class=\"number-picker\">\n                <view class=\"number-btn minus\" @tap=\"decrementValidityDays\">-</view>\n                <input class=\"number-input\" type=\"number\" v-model=\"formData.validity.days\" />\n                <view class=\"number-btn plus\" @tap=\"incrementValidityDays\">+</view>\n                <text class=\"unit-text\">天</text>\n              </view>\n              <text class=\"form-tip\">拼团创建成功后，活动自动生效并持续指定天数</text>\n            </view>\n          </form-group>\n          \n          <!-- 成团时限 -->\n          <form-group label=\"成团时限\" :is-required=\"true\">\n            <view class=\"number-picker\">\n              <view class=\"number-btn minus\" @tap=\"decrementTimeLimit\">-</view>\n              <input class=\"number-input\" type=\"number\" v-model=\"formData.timeLimit\" />\n              <view class=\"number-btn plus\" @tap=\"incrementTimeLimit\">+</view>\n            </view>\n          </form-group>\n          \n          <!-- 支付和核销设置 -->\n          <form-group label=\"支付方式\" :is-required=\"true\">\n            <view class=\"radio-group\">\n              <view class=\"radio-item\" \n                :class=\"{ active: formData.paymentType === 'store' }\" \n                @tap=\"formData.paymentType = 'store'\">\n                <view class=\"radio-dot\">\n                  <view class=\"radio-dot-inner\" v-if=\"formData.paymentType === 'store'\"></view>\n                </view>\n                <text class=\"radio-label\">到店支付</text>\n              </view>\n              <view class=\"radio-item\" \n                :class=\"{ active: formData.paymentType === 'online' }\" \n                @tap=\"formData.paymentType = 'online'\">\n                <view class=\"radio-dot\">\n                  <view class=\"radio-dot-inner\" v-if=\"formData.paymentType === 'online'\"></view>\n                </view>\n                <text class=\"radio-label\">在线支付</text>\n              </view>\n            </view>\n          </form-group>\n          \n          <form-group label=\"核销方式\" :is-required=\"true\">\n            <view class=\"radio-group\">\n              <view class=\"radio-item\" \n                :class=\"{ active: formData.verifyType === 'store' }\" \n                @tap=\"formData.verifyType = 'store'\">\n                <view class=\"radio-dot\">\n                  <view class=\"radio-dot-inner\" v-if=\"formData.verifyType === 'store'\"></view>\n                </view>\n                <text class=\"radio-label\">到店核销</text>\n              </view>\n              <view class=\"radio-item\" \n                :class=\"{ active: formData.verifyType === 'online' }\" \n                @tap=\"formData.verifyType = 'online'\">\n                <view class=\"radio-dot\">\n                  <view class=\"radio-dot-inner\" v-if=\"formData.verifyType === 'online'\"></view>\n                </view>\n                <text class=\"radio-label\">在线核销</text>\n              </view>\n            </view>\n          </form-group>\n          \n          <form-group label=\"生成核销码\" :is-required=\"true\">\n            <view class=\"switch-container\">\n              <switch :checked=\"formData.verificationCode\" @change=\"e => formData.verificationCode = e.detail.value\" color=\"#9040FF\" />\n            </view>\n          </form-group>\n          \n          <form-group label=\"核销码有效期\" :is-required=\"true\">\n            <view class=\"number-picker\">\n              <view class=\"number-btn minus\" @tap=\"decrementVerificationExpiry\">-</view>\n              <input class=\"number-input\" type=\"number\" v-model=\"formData.verificationExpiry\" />\n              <view class=\"number-btn plus\" @tap=\"incrementVerificationExpiry\">+</view>\n              <text class=\"unit-text\">天</text>\n            </view>\n          </form-group>\n          \n          <form-group label=\"允许部分核销\">\n            <view class=\"switch-container\">\n              <switch :checked=\"formData.allowPartialVerification\" @change=\"e => formData.allowPartialVerification = e.detail.value\" color=\"#9040FF\" />\n            </view>\n          </form-group>\n          \n          <!-- 添加分销设置 -->\n          <form-group label=\"分销设置\" v-if=\"hasMerchantDistribution\">\n            <distribution-setting \n              :initial-settings=\"formData.distributionSettings\"\n              @update=\"updateDistributionSettings\"\n            />\n          </form-group>\n        </view>\n      </view>\n      \n      <!-- 步骤3: 确认创建 -->\n      <view class=\"form-section\" v-if=\"currentStep === 3\" id=\"step3\">\n        <!-- 使用区块标题组件 -->\n        <section-header title=\"确认拼团信息\"></section-header>\n        \n        <view class=\"form-content\">\n          <!-- 拼团信息预览 -->\n          <view class=\"preview-card\">\n            <view class=\"preview-header\">\n              <text class=\"preview-title\">{{formData.groupName || '拼团活动名称'}}</text>\n              <text class=\"preview-dates\">{{formData.startDate}} 至 {{formData.endDate}}</text>\n            </view>\n            \n            <view class=\"preview-content\">\n              <!-- 商品预览 -->\n              <view class=\"preview-products\">\n                <view \n                  class=\"preview-product-item\" \n                  v-for=\"(product, index) in formData.selectedProducts\" \n                  :key=\"index\"\n                >\n                  <image class=\"product-image\" :src=\"product.image\" mode=\"aspectFill\"></image>\n                  <view class=\"product-info\">\n                    <text class=\"product-name\">{{product.name}}</text>\n                    <view class=\"product-price\">\n                      <text class=\"group-price\">¥{{product.groupPrice || product.price}}</text>\n                      <text class=\"original-price\">¥{{product.originalPrice || product.price}}</text>\n                </view>\n              </view>\n                </view>\n              </view>\n              \n              <!-- 拼团规则 -->\n              <view class=\"group-rules\">\n                <view class=\"rule-item\">\n                  <text class=\"rule-label\">拼团人数:</text>\n                  <text class=\"rule-value\">{{formData.groupSize}}人</text>\n                </view>\n              <view class=\"rule-item\">\n                  <text class=\"rule-label\">拼团时限:</text>\n                  <text class=\"rule-value\">{{formData.groupDuration}}小时</text>\n              </view>\n              <view class=\"rule-item\">\n                  <text class=\"rule-label\">单人限购:</text>\n                  <text class=\"rule-value\">{{formData.purchaseLimit || '不限'}}</text>\n              </view>\n              <view class=\"rule-item\">\n                  <text class=\"rule-label\">活动库存:</text>\n                  <text class=\"rule-value\">{{formData.stockLimit || '不限'}}</text>\n              </view>\n              </view>\n            </view>\n          </view>\n          \n          <!-- 活动推广 -->\n          <form-group label=\"活动推广\" tip=\"发布、置顶或刷新活动，提升曝光率\">\n            <MarketingPromotionActions \n              :activity-type=\"'group'\"\n              :activity-id=\"tempGroupId\"\n              :publish-mode-only=\"true\"\n              :show-actions=\"['publish']\"\n              @action-completed=\"handlePromotionCompleted\"\n            />\n          </form-group>\n          \n          <!-- 提交按钮 -->\n          <view class=\"form-actions\">\n            <button class=\"btn-secondary\" @click=\"prevStep\">上一步</button>\n            <button class=\"btn-primary\" @click=\"submitGroupActivity\">创建拼团</button>\n          </view>\n        </view>\n      </view>\n    </scroll-view>\n    \n    <!-- 底部按钮区域 -->\n    <view class=\"footer-buttons\">\n      <button class=\"btn btn-secondary\" @tap=\"prevStep\" v-if=\"currentStep > 1\">上一步</button>\n      <button class=\"btn btn-primary\" @tap=\"nextStep\" v-if=\"currentStep < 3\">下一步</button>\n      <button class=\"btn btn-primary\" @tap=\"submitForm\" v-if=\"currentStep === 3\">创建活动</button>\n    </view>\n  </view>\n</template>\n\n<script>\n// 导入组件\nimport SectionHeader from '/subPackages/merchant-admin/components/ui/SectionHeader.vue';\nimport FormGroup from '/subPackages/merchant-admin/components/ui/FormGroup.vue';\nimport IconButton from '/subPackages/merchant-admin/components/ui/IconButton.vue';\nimport SvgIcon from '/subPackages/merchant-admin/components/ui/SvgIcon.vue';\nimport ProgressSteps from '/subPackages/merchant-admin/components/ui/ProgressSteps.vue';\nimport DistributionSetting from '../distribution/components/DistributionSetting.vue';\nimport distributionMixin from '/subPackages/merchant-admin-marketing/mixins/distributionMixin';\nimport MarketingPromotionActions from '/subPackages/merchant-admin-marketing/components/MarketingPromotionActions.vue';\n\nexport default {\n  components: {\n    SectionHeader,\n    FormGroup,\n    IconButton,\n    SvgIcon,\n    ProgressSteps,\n    DistributionSetting,\n    MarketingPromotionActions\n  },\n  mixins: [distributionMixin], // 使用分销混入\n  data() {\n    return {\n      currentStep: 1,\n      scrollToId: 'step1',\n      searchKeyword: '',\n      \n      // 表单数据\n      formData: {\n        // 步骤1：商品信息\n        productSelectionType: 'existing', // existing=选择已有商品, new=新建商品\n        selectedProducts: [], // 选择的商品列表\n        \n        // 步骤2：拼团设置\n        groupName: '', // 拼团活动名称\n        groupType: 'normal', // normal=普通拼团, package=套餐拼团\n        packageName: '', // 套餐名称\n        packageDescription: '', // 套餐描述\n        packageItems: [{ // 套餐商品列表\n          name: '',\n          originalPrice: '',\n          quantity: 1\n        }],\n        marketPrice: '', // 市场价\n        regularPrice: '', // 日常价\n        groupPrice: '', // 拼团价格\n        requireGroup: true, // 是否需要拼团才能享受优惠价\n        minGroupSize: 2, // 成团人数\n        limitPerUser: 1, // 单用户限购\n        validity: { // 有效期\n          type: 'fixed', // fixed=固定时间段, dynamic=动态时间段\n          startDate: '', // 开始日期\n          startTime: '', // 开始时间\n          endDate: '', // 结束日期\n          endTime: '', // 结束时间\n          days: 7 // 动态时间段的天数\n        },\n        timeLimit: 24, // 成团时限(小时)\n        rules: [], // 自定义规则\n        \n        // 支付和核销设置\n        paymentType: 'store', // online=在线支付, store=到店支付\n        verifyType: 'store', // online=在线核销, store=到店核销\n        verificationCode: true, // 是否生成核销码\n        verificationExpiry: 7, // 核销码有效期(天)\n        allowPartialVerification: false, // 是否允许部分核销\n        distributionSettings: {\n          enabled: false,\n          commissionMode: 'percentage',\n          commissions: {\n            level1: '',\n            level2: '',\n            level3: ''\n          },\n          enableLevel3: false\n        },\n        groupSize: 2, // 拼团人数\n        groupDuration: 24, // 拼团时限(小时)\n        purchaseLimit: '不限', // 单人限购\n        stockLimit: '不限' // 活动库存\n      },\n      \n      // 模拟商品列表\n      productList: [\n        {\n          id: 1,\n          name: 'iPhone 14 Pro Max',\n          price: '8999.00',\n          originalPrice: '9999.00',\n          stock: 120,\n          image: '/static/images/products/iphone.jpg'\n        },\n        {\n          id: 2,\n          name: 'Apple Watch Series 8',\n          price: '3099.00',\n          originalPrice: '3299.00',\n          stock: 85,\n          image: '/static/images/products/watch.jpg'\n        },\n        {\n          id: 3,\n          name: 'AirPods Pro 2代',\n          price: '1799.00',\n          originalPrice: '1999.00',\n          stock: 200,\n          image: '/static/images/products/airpods.jpg'\n        },\n        {\n          id: 4,\n          name: 'MacBook Air M2',\n          price: '7599.00',\n          originalPrice: '7999.00',\n          stock: 50,\n          image: '/static/images/products/macbook.jpg'\n        },\n        {\n          id: 5,\n          name: 'iPad Pro 2022',\n          price: '6299.00',\n          originalPrice: '6799.00',\n          stock: 75,\n          image: '/static/images/products/ipad.jpg'\n        }\n      ],\n      hasMerchantDistribution: false, // 商家是否开通分销功能\n      tempGroupId: 'temp-' + Date.now() // 临时ID，实际应该从后端获取\n    };\n  },\n  computed: {\n    progressPercentage() {\n      // 计算进度百分比\n      return (this.currentStep / 3) * 100;\n    }\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack();\n    },\n    showHelp() {\n      uni.showToast({\n        title: '拼团活动帮助',\n        icon: 'none'\n      });\n    },\n    prevStep() {\n      if (this.currentStep > 1) {\n        this.currentStep -= 1;\n        this.scrollToId = `step${this.currentStep}`;\n      }\n    },\n    nextStep() {\n      // 表单验证\n      if (this.currentStep === 1) {\n        // 验证是否选择了商品\n        if (this.formData.productSelectionType === 'existing' && this.formData.selectedProducts.length === 0) {\n          uni.showToast({\n            title: '请至少选择一个商品',\n            icon: 'none'\n          });\n          return;\n        }\n      } else if (this.currentStep === 2) {\n        // 验证拼团设置\n        if (!this.formData.groupName) {\n          uni.showToast({ title: '请输入活动名称', icon: 'none' });\n          return;\n        }\n        \n        // 套餐拼团特有验证\n        if (this.formData.groupType === 'package') {\n          if (!this.formData.packageName) {\n            uni.showToast({ title: '请输入套餐名称', icon: 'none' });\n            return;\n          }\n          \n          // 验证套餐商品\n          let hasEmptyItem = false;\n          this.formData.packageItems.forEach(item => {\n            if (!item.name || !item.originalPrice) {\n              hasEmptyItem = true;\n            }\n          });\n          \n          if (hasEmptyItem) {\n            uni.showToast({ title: '请完善套餐商品信息', icon: 'none' });\n            return;\n          }\n        }\n        \n        if (!this.formData.marketPrice) {\n          uni.showToast({ title: '请输入市场价', icon: 'none' });\n          return;\n        }\n        \n        if (!this.formData.regularPrice) {\n          uni.showToast({ title: '请输入日常价', icon: 'none' });\n          return;\n        }\n        \n        if (!this.formData.groupPrice) {\n          uni.showToast({ title: '请输入拼团价格', icon: 'none' });\n          return;\n        }\n        \n        // 检查价格大小关系\n        const marketPrice = parseFloat(this.formData.marketPrice);\n        const regularPrice = parseFloat(this.formData.regularPrice);\n        const groupPrice = parseFloat(this.formData.groupPrice);\n        \n        if (marketPrice <= regularPrice) {\n          uni.showToast({ title: '市场价应高于日常价', icon: 'none' });\n          return;\n        }\n        \n        if (regularPrice <= groupPrice) {\n          uni.showToast({ title: '日常价应高于拼团价', icon: 'none' });\n          return;\n        }\n        \n        if (this.formData.validity.type === 'fixed') {\n          if (!this.formData.validity.startDate || !this.formData.validity.endDate || !this.formData.validity.startTime || !this.formData.validity.endTime) {\n            uni.showToast({ title: '请选择活动有效期', icon: 'none' });\n            return;\n          }\n        }\n      }\n      \n      // 通过验证，进入下一步\n      if (this.currentStep < 3) {\n        this.currentStep += 1;\n        this.scrollToId = `step${this.currentStep}`;\n      }\n    },\n    submitForm() {\n      // 表单提交逻辑\n      uni.showLoading({\n        title: '创建中...'\n      });\n      \n      // 准备提交的数据\n      const submitData = {\n        // 基本信息\n        groupName: this.formData.groupName,\n        groupType: this.formData.groupType,\n        marketPrice: parseFloat(this.formData.marketPrice),\n        regularPrice: parseFloat(this.formData.regularPrice),\n        groupPrice: parseFloat(this.formData.groupPrice),\n        requireGroup: this.formData.requireGroup,\n        minGroupSize: this.formData.minGroupSize,\n        limitPerUser: this.formData.limitPerUser,\n        timeLimit: this.formData.timeLimit,\n        \n        // 有效期\n        validityType: this.formData.validity.type,\n        validityStartDate: this.formData.validity.startDate,\n        validityStartTime: this.formData.validity.startTime,\n        validityEndDate: this.formData.validity.endDate,\n        validityEndTime: this.formData.validity.endTime,\n        validityDays: this.formData.validity.days,\n        \n        // 支付和核销设置\n        paymentType: this.formData.paymentType,\n        verifyType: this.formData.verifyType,\n        verificationCode: this.formData.verificationCode,\n        verificationExpiry: this.formData.verificationExpiry,\n        allowPartialVerification: this.formData.allowPartialVerification\n      };\n      \n      // 根据拼团类型添加不同的数据\n      if (this.formData.groupType === 'normal') {\n        // 普通拼团 - 添加商品信息\n        submitData.products = this.formData.selectedProducts.map(product => ({\n          id: product.id,\n          name: product.name,\n          price: product.price,\n          originalPrice: product.originalPrice,\n          image: product.image,\n          stock: product.stock\n        }));\n      } else if (this.formData.groupType === 'package') {\n        // 套餐拼团 - 添加套餐信息\n        submitData.packageName = this.formData.packageName;\n        submitData.packageDescription = this.formData.packageDescription;\n        submitData.packageItems = this.formData.packageItems.map(item => ({\n          name: item.name,\n          originalPrice: parseFloat(item.originalPrice),\n          quantity: parseInt(item.quantity)\n        }));\n        submitData.totalOriginalPrice = parseFloat(this.calculateTotalOriginalPrice());\n        submitData.totalSavings = parseFloat(this.calculateSavings());\n        submitData.totalItems = this.getTotalItemQuantity();\n      }\n      \n      // 处理分销设置\n      if (this.hasMerchantDistribution && this.formData.distributionSettings.enabled) {\n        // 添加分销相关数据\n        submitData.distributionSettings = this.formData.distributionSettings;\n      }\n      \n      console.log('提交数据:', submitData);\n      \n      // 模拟API请求\n      setTimeout(() => {\n        uni.hideLoading();\n        uni.showToast({\n          title: '拼团活动创建成功！',\n          icon: 'success',\n          duration: 2000\n        });\n        \n        // 返回拼团管理页面\n        setTimeout(() => {\n          uni.navigateBack();\n        }, 2000);\n      }, 1500);\n    },\n    searchProducts() {\n      // 实现商品搜索逻辑\n      console.log('搜索商品:', this.searchKeyword);\n      // 这里可以添加实际的搜索逻辑\n    },\n    toggleFilterDropdown(filterType) {\n      // 打开筛选下拉列表\n      uni.showToast({\n        title: `打开${filterType}筛选`,\n        icon: 'none'\n      });\n    },\n    toggleProductSelection(product) {\n      // 切换商品选择状态\n      const index = this.formData.selectedProducts.findIndex(p => p.id === product.id);\n      if (index === -1) {\n        this.formData.selectedProducts.push(product);\n      } else {\n        this.formData.selectedProducts.splice(index, 1);\n      }\n    },\n    isProductSelected(product) {\n      // 检查商品是否已选择\n      return this.formData.selectedProducts.findIndex(p => p.id === product.id) !== -1;\n    },\n    removeProduct(index) {\n      // 从已选列表中移除商品\n      this.formData.selectedProducts.splice(index, 1);\n    },\n    clearSelectedProducts() {\n      // 清空已选商品列表\n      this.formData.selectedProducts = [];\n    },\n    navigateToCreateProduct() {\n      // 导航到创建商品页\n      uni.showToast({\n        title: '跳转到创建商品页面',\n        icon: 'none'\n      });\n    },\n    // 步骤2中的方法\n    decrementGroupSize() {\n      if (this.formData.minGroupSize > 2) {\n        this.formData.minGroupSize -= 1;\n      }\n    },\n    incrementGroupSize() {\n      if (this.formData.minGroupSize < 100) {\n        this.formData.minGroupSize += 1;\n      }\n    },\n    decrementLimitPerUser() {\n      if (this.formData.limitPerUser > 0) {\n        this.formData.limitPerUser -= 1;\n      }\n    },\n    incrementLimitPerUser() {\n      if (this.formData.limitPerUser < 999) {\n        this.formData.limitPerUser += 1;\n      }\n    },\n    // 套餐商品相关方法\n    addPackageItem() {\n      this.formData.packageItems.push({\n        name: '',\n        originalPrice: '',\n        quantity: 1\n      });\n    },\n    removePackageItem(index) {\n      if (this.formData.packageItems.length > 1) {\n        this.formData.packageItems.splice(index, 1);\n      }\n    },\n    decrementItemQuantity(index) {\n      if (this.formData.packageItems[index].quantity > 1) {\n        this.formData.packageItems[index].quantity -= 1;\n      }\n    },\n    incrementItemQuantity(index) {\n      if (this.formData.packageItems[index].quantity < 99) {\n        this.formData.packageItems[index].quantity += 1;\n      }\n    },\n    decrementValidityDays() {\n      if (this.formData.validity.days > 1) {\n        this.formData.validity.days -= 1;\n      }\n    },\n    incrementValidityDays() {\n      if (this.formData.validity.days < 90) {\n        this.formData.validity.days += 1;\n      }\n    },\n    decrementTimeLimit() {\n      if (this.formData.timeLimit > 1) {\n        this.formData.timeLimit -= 1;\n      }\n    },\n    incrementTimeLimit() {\n      if (this.formData.timeLimit < 72) {\n        this.formData.timeLimit += 1;\n      }\n    },\n    decrementVerificationExpiry() {\n      if (this.formData.verificationExpiry > 1) {\n        this.formData.verificationExpiry -= 1;\n      }\n    },\n    incrementVerificationExpiry() {\n      if (this.formData.verificationExpiry < 90) {\n        this.formData.verificationExpiry += 1;\n      }\n    },\n    dateChange(type, e) {\n      // 处理日期选择\n      this.formData.validity[`${type}Date`] = e.detail.value;\n    },\n    timeChange(type, e) {\n      // 处理时间选择\n      this.formData.validity[`${type}Time`] = e.detail.value;\n    },\n    // 套餐计算方法\n    getTotalItemQuantity() {\n      if (!this.formData.packageItems || this.formData.packageItems.length === 0) {\n        return 0;\n      }\n      return this.formData.packageItems.reduce((sum, item) => sum + parseInt(item.quantity || 0), 0);\n    },\n    calculateTotalOriginalPrice() {\n      if (!this.formData.packageItems || this.formData.packageItems.length === 0) {\n        return '0.00';\n      }\n      const total = this.formData.packageItems.reduce((sum, item) => {\n        const price = parseFloat(item.originalPrice || 0);\n        const quantity = parseInt(item.quantity || 0);\n        return sum + (price * quantity);\n      }, 0);\n      return total.toFixed(2);\n    },\n    calculateSavings() {\n      const totalOriginal = parseFloat(this.calculateTotalOriginalPrice());\n      const groupPrice = parseFloat(this.formData.groupPrice || 0);\n      const savings = totalOriginal - groupPrice;\n      return savings > 0 ? savings.toFixed(2) : '0.00';\n    },\n    // 更新分销设置\n    updateDistributionSettings(settings) {\n      this.formData.distributionSettings = settings;\n    },\n    \n    // 检查商家是否开通分销功能\n    checkMerchantDistribution() {\n      // 调用API检查商家是否开通分销功能\n      // 这里模拟API调用\n      setTimeout(() => {\n        this.hasMerchantDistribution = true;\n      }, 500);\n    },\n    \n    // 保存拼团活动\n    async saveGroupActivity() {\n      // ... existing code ...\n      \n      // 处理分销设置\n      if (this.hasMerchantDistribution && this.formData.distributionSettings.enabled) {\n        const success = await this.saveActivityDistributionSettings('group', groupId);\n        if (!success) {\n          return;\n        }\n      }\n      \n      // ... existing code ...\n    },\n    \n    // 处理推广操作完成事件\n    handlePromotionCompleted(data) {\n      console.log('推广操作完成:', data);\n      // 根据不同操作类型处理结果\n      if (data.action === 'publish') {\n        uni.showToast({\n          title: '发布成功',\n          icon: 'success'\n        });\n      } else if (data.action === 'top') {\n        uni.showToast({\n          title: '置顶成功',\n          icon: 'success'\n        });\n      } else if (data.action === 'refresh') {\n        uni.showToast({\n          title: '刷新成功',\n          icon: 'success'\n        });\n      }\n    }\n  },\n  mounted() {\n    // 检查商家是否开通分销功能\n    this.checkMerchantDistribution();\n  }\n};\n</script>\n\n<style lang=\"scss\">\n.group-create-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: 100px;\n  display: flex;\n  flex-direction: column;\n}\n\n/* 自定义导航栏 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #9040FF, #5E35B1);\n  color: #fff;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 20px;\n  height: 20px;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: bold;\n}\n\n/* 进度指示器 */\n.progress-container {\n  padding: 20px 15px;\n  background-color: #fff;\n  margin-bottom: 10px;\n  box-shadow: 0 1px 5px rgba(0,0,0,0.05);\n}\n\n.progress-bar {\n  height: 4px;\n  background-color: #EBEDF5;\n  border-radius: 2px;\n  margin-bottom: 15px;\n  position: relative;\n  overflow: hidden;\n}\n\n.progress-track {\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 100%;\n  width: 100%;\n  background-color: #EBEDF5;\n}\n\n.progress-fill {\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 100%;\n  background: linear-gradient(to right, #9040FF, #5E35B1);\n  border-radius: 2px;\n  transition: width 0.3s;\n}\n\n.progress-steps {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  position: relative;\n}\n\n.step {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  position: relative;\n  flex: 1;\n}\n\n.step-line {\n  height: 1px;\n  background-color: #EBEDF5;\n  flex: 1;\n  margin: 0 5px;\n  transition: background-color 0.3s;\n  \n  &.active {\n    background-color: #9040FF;\n  }\n}\n\n.step-dot {\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background-color: #EBEDF5;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 8px;\n  transition: all 0.3s;\n  \n  .check-icon {\n    width: 10px;\n    height: 6px;\n    border-left: 2px solid #fff;\n    border-bottom: 2px solid #fff;\n    transform: rotate(-45deg) translate(1px, -1px);\n  }\n}\n\n.step-label {\n  font-size: 12px;\n  color: #999;\n  white-space: nowrap;\n}\n\n.step.active {\n  .step-dot {\n    background-color: #9040FF;\n    color: #fff;\n  }\n  \n  .step-label {\n    color: #9040FF;\n    font-weight: 500;\n  }\n}\n\n.step.completed {\n  .step-dot {\n    background-color: #9040FF;\n  }\n  \n  .step-label {\n    color: #9040FF;\n  }\n}\n\n/* 表单容器 */\n.form-scroll-view {\n  flex: 1;\n  padding-bottom: 20px;\n}\n\n.form-section {\n  margin: 15px;\n  background-color: #fff;\n  border-radius: 12px;\n  box-shadow: 0 2px 10px rgba(0,0,0,0.05);\n  overflow: hidden;\n}\n\n.section-header {\n  padding: 15px;\n  border-bottom: 1px solid #F0F0F0;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n/* 选择方式 */\n.selection-options {\n  padding: 15px;\n}\n\n.selection-option {\n  display: flex;\n  align-items: center;\n  padding: 15px;\n  border-radius: 8px;\n  margin-bottom: 10px;\n  background-color: #F9F9F9;\n  transition: all 0.3s;\n  \n  &.active {\n    background-color: rgba(126, 48, 225, 0.05);\n    border-left: 3px solid #7E30E1;\n  }\n}\n\n.option-radio {\n  width: 22px;\n  height: 22px;\n  border-radius: 50%;\n  border: 2px solid #CCCCCC;\n  margin-right: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  \n  .radio-inner {\n    width: 12px;\n    height: 12px;\n    border-radius: 50%;\n    background-color: #7E30E1;\n  }\n}\n\n.selection-option.active .option-radio {\n  border-color: #7E30E1;\n}\n\n.option-content {\n  flex: 1;\n}\n\n.option-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 4px;\n  display: block;\n}\n\n.option-desc {\n  font-size: 12px;\n  color: #999;\n}\n\n/* 商品选择 */\n.product-selection {\n  padding: 15px;\n}\n\n.search-bar {\n  display: flex;\n  align-items: center;\n  background-color: #F5F5F5;\n  border-radius: 20px;\n  padding: 8px 15px;\n  margin-bottom: 15px;\n}\n\n.search-icon {\n  width: 14px;\n  height: 14px;\n  border: 2px solid #999;\n  border-radius: 50%;\n  position: relative;\n  margin-right: 8px;\n  \n  &:after {\n    content: '';\n    position: absolute;\n    width: 2px;\n    height: 6px;\n    background-color: #999;\n    bottom: -4px;\n    right: -2px;\n    transform: rotate(-45deg);\n  }\n}\n\n.search-input {\n  flex: 1;\n  height: 20px;\n  font-size: 14px;\n}\n\n.filter-options {\n  display: flex;\n  justify-content: space-around;\n  padding: 10px 0;\n  margin-bottom: 15px;\n  border-bottom: 1px solid #F0F0F0;\n}\n\n.filter-item {\n  display: flex;\n  align-items: center;\n  font-size: 12px;\n  color: #666;\n}\n\n.arrow-down {\n  width: 0;\n  height: 0;\n  border-left: 4px solid transparent;\n  border-right: 4px solid transparent;\n  border-top: 4px solid #666;\n  margin-left: 5px;\n}\n\n/* 商品列表 */\n.product-list {\n  margin-bottom: 20px;\n}\n\n.product-item {\n  display: flex;\n  padding: 15px 0;\n  border-bottom: 1px solid #F0F0F0;\n}\n\n.checkbox {\n  width: 22px;\n  height: 22px;\n  border: 2px solid #CCC;\n  border-radius: 4px;\n  margin-right: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  \n  &.checked {\n    background-color: #7E30E1;\n    border-color: #7E30E1;\n  }\n  \n  .checkbox-inner {\n    width: 10px;\n    height: 6px;\n    border-left: 2px solid #fff;\n    border-bottom: 2px solid #fff;\n    transform: rotate(-45deg) translate(1px, -1px);\n  }\n}\n\n.product-img {\n  width: 60px;\n  height: 60px;\n  border-radius: 4px;\n  margin-right: 10px;\n  background-color: #f5f5f5;\n  object-fit: cover;\n}\n\n.product-info {\n  flex: 1;\n}\n\n.product-name {\n  font-size: 14px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 6px;\n  display: block;\n}\n\n.product-price {\n  display: flex;\n  align-items: center;\n  margin-bottom: 4px;\n}\n\n.price-current {\n  font-size: 15px;\n  font-weight: 600;\n  color: #FF3B30;\n  margin-right: 5px;\n}\n\n.price-original {\n  font-size: 12px;\n  color: #999;\n  text-decoration: line-through;\n}\n\n.product-stock {\n  font-size: 12px;\n  color: #999;\n}\n\n/* 已选商品 */\n.selected-products {\n  margin-top: 20px;\n  padding-top: 15px;\n  border-top: 1px solid #F0F0F0;\n}\n\n.selected-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.selected-title {\n  font-size: 14px;\n  font-weight: 500;\n  color: #333;\n}\n\n.clear-all {\n  font-size: 12px;\n  color: #7E30E1;\n}\n\n.selected-list {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.selected-item {\n  width: calc(33.33% - 10px);\n  margin-right: 10px;\n  margin-bottom: 10px;\n  position: relative;\n  background-color: #F9F9F9;\n  border-radius: 8px;\n  padding: 8px;\n  overflow: hidden;\n}\n\n.selected-img {\n  width: 100%;\n  height: 70px;\n  border-radius: 4px;\n  margin-bottom: 5px;\n  object-fit: cover;\n}\n\n.selected-info {\n  padding-top: 5px;\n}\n\n.selected-name {\n  font-size: 12px;\n  color: #333;\n  display: block;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  margin-bottom: 4px;\n}\n\n.selected-price {\n  font-size: 12px;\n  color: #FF3B30;\n  font-weight: 500;\n}\n\n.remove-btn {\n  position: absolute;\n  top: 0;\n  right: 0;\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: rgba(0,0,0,0.3);\n  color: #fff;\n  font-size: 16px;\n  border-radius: 0 0 0 8px;\n}\n\n/* 新建商品 */\n.create-product {\n  padding: 30px 15px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.create-product-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 60%;\n  height: 44px;\n  border-radius: 22px;\n  background: linear-gradient(135deg, #34C759, #32ADE6);\n  color: #fff;\n  font-size: 16px;\n  font-weight: 500;\n  margin-bottom: 15px;\n  box-shadow: 0 5px 15px rgba(52, 199, 89, 0.2);\n}\n\n.btn-icon {\n  font-size: 20px;\n  margin-right: 5px;\n}\n\n.create-product-tip {\n  font-size: 12px;\n  color: #999;\n}\n\n/* 步骤2: 表单样式 */\n.form-content {\n  padding: 15px;\n}\n\n.form-group {\n  margin-bottom: 20px;\n}\n\n.form-label {\n  margin-bottom: 10px;\n}\n\n.label-text {\n  font-size: 14px;\n  font-weight: 500;\n  color: #333;\n  \n  &.required:after {\n    content: '*';\n    color: #FF3B30;\n    margin-left: 4px;\n  }\n}\n\n.form-field {\n  position: relative;\n}\n\n.form-input {\n  width: 100%;\n  height: 44px;\n  background-color: #F5F5F5;\n  border-radius: 8px;\n  padding: 0 15px;\n  font-size: 14px;\n  color: #333;\n}\n\n.input-counter {\n  position: absolute;\n  right: 15px;\n  top: 12px;\n  font-size: 12px;\n  color: #999;\n}\n\n.price-input-wrapper {\n  position: relative;\n}\n\n.price-symbol {\n  position: absolute;\n  left: 15px;\n  top: 12px;\n  font-size: 14px;\n  color: #333;\n}\n\n.price-input {\n  padding-left: 30px;\n}\n\n.form-tip {\n  font-size: 12px;\n  color: #999;\n  margin-top: 8px;\n  display: block;\n}\n\n.number-picker {\n  display: flex;\n  align-items: center;\n  height: 44px;\n}\n\n.number-btn {\n  width: 44px;\n  height: 44px;\n  background-color: #F5F5F5;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 20px;\n  font-weight: 600;\n  color: #999;\n  \n  &.minus {\n    border-radius: 8px 0 0 8px;\n  }\n  \n  &.plus {\n    border-radius: 0 8px 8px 0;\n  }\n}\n\n.number-input {\n  width: 60px;\n  height: 44px;\n  background-color: #F5F5F5;\n  text-align: center;\n  font-size: 16px;\n  color: #333;\n}\n\n.unit-text {\n  margin-left: 8px;\n  font-size: 14px;\n  color: #666;\n}\n\n.radio-group {\n  display: flex;\n  margin-bottom: 15px;\n}\n\n.radio-item {\n  display: flex;\n  align-items: center;\n  margin-right: 20px;\n  padding: 5px 0;\n}\n\n.radio-dot {\n  width: 18px;\n  height: 18px;\n  border-radius: 50%;\n  border: 2px solid #CCCCCC;\n  margin-right: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  \n  .radio-dot-inner {\n    width: 10px;\n    height: 10px;\n    border-radius: 50%;\n    background-color: #7E30E1;\n  }\n}\n\n.radio-item.active .radio-dot {\n  border-color: #7E30E1;\n}\n\n.radio-label {\n  font-size: 14px;\n  color: #333;\n}\n\n.datetime-picker {\n  display: flex;\n  flex-direction: column;\n  margin-bottom: 15px;\n}\n\n.datetime-field {\n  background: #F8FAFC;\n  border-radius: 4px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  padding: 0 12px;\n  margin-bottom: 8px;\n  position: relative;\n}\n\n.datetime-placeholder {\n  color: #999;\n  font-size: 14px;\n}\n\n.datetime-value {\n  color: #333;\n  font-size: 14px;\n}\n\n.datetime-icon {\n  width: 10px;\n  height: 10px;\n  border-top: 2px solid #999;\n  border-right: 2px solid #999;\n  transform: rotate(45deg);\n  position: absolute;\n  right: 12px;\n}\n\n.datetime-separator {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 5px 0;\n  color: #666;\n}\n\n.picker-component {\n  width: 100%;\n}\n\n/* 调整时间日期选择布局 */\n@media screen and (min-width: 375px) {\n  .datetime-picker {\n    flex-direction: row;\n    flex-wrap: wrap;\n  }\n  \n  .picker-component {\n    width: 48%;\n    margin-right: 2%;\n  }\n  \n  .datetime-separator {\n    display: flex;\n    width: 100%;\n    justify-content: center;\n    margin: 5px 0;\n  }\n}\n\n/* 步骤3：确认创建样式 */\n.confirm-content {\n  padding: 15px;\n}\n\n.activity-card {\n  border-radius: 12px;\n  overflow: hidden;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n}\n\n.activity-header {\n  position: relative;\n  height: 120px;\n}\n\n.activity-bg {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.activity-overlay {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.7));\n}\n\n.activity-info {\n  position: relative;\n  padding: 15px;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-end;\n}\n\n.activity-name {\n  color: #fff;\n  font-size: 18px;\n  font-weight: 600;\n  margin-bottom: 5px;\n  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);\n}\n\n.activity-time {\n  color: rgba(255, 255, 255, 0.9);\n  font-size: 12px;\n  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);\n}\n\n.activity-body {\n  padding: 15px;\n  background-color: #fff;\n  display: flex;\n}\n\n.product-preview {\n  width: 70px;\n  height: 70px;\n  position: relative;\n  border-radius: 8px;\n  overflow: hidden;\n  margin-right: 15px;\n}\n\n.product-cover {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.product-count {\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  padding: 2px 6px;\n  background-color: rgba(0, 0, 0, 0.6);\n  color: #fff;\n  font-size: 10px;\n  border-top-left-radius: 8px;\n}\n\n.activity-detail {\n  flex: 1;\n}\n\n.detail-item {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 8px;\n}\n\n.detail-label {\n  font-size: 13px;\n  color: #666;\n}\n\n.detail-value {\n  font-size: 13px;\n  color: #333;\n  font-weight: 500;\n  \n  &.price {\n    color: #FF3B30;\n  }\n}\n\n.confirm-products,\n.confirm-rules {\n  margin-bottom: 20px;\n}\n\n.confirm-title {\n  font-size: 15px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 12px;\n}\n\n.confirm-product-list {\n  background-color: #F9F9F9;\n  border-radius: 8px;\n}\n\n.confirm-product-item {\n  display: flex;\n  padding: 12px;\n  border-bottom: 1px solid #F0F0F0;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n}\n\n.confirm-product-img {\n  width: 50px;\n  height: 50px;\n  border-radius: 4px;\n  margin-right: 10px;\n}\n\n.confirm-product-info {\n  flex: 1;\n}\n\n.confirm-product-name {\n  font-size: 13px;\n  color: #333;\n  margin-bottom: 5px;\n  display: block;\n}\n\n.confirm-product-price {\n  display: flex;\n  flex-direction: column;\n}\n\n.confirm-price-market {\n  color: #999;\n  text-decoration: line-through;\n  font-size: 11px;\n  margin-right: 8px;\n  display: block;\n}\n\n.confirm-price-regular {\n  color: #FF6B00;\n  text-decoration: line-through;\n  font-size: 11px;\n  margin-right: 8px;\n  display: block;\n  margin-top: 2px;\n}\n\n.confirm-price-group {\n  font-size: 12px;\n  color: #666;\n  \n  .price-value {\n    color: #FF3B30;\n    font-weight: 500;\n  }\n}\n\n.rules-content {\n  background-color: #F9F9F9;\n  border-radius: 8px;\n  padding: 12px 15px;\n}\n\n.rule-item {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 10px;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n.rule-dot {\n  width: 6px;\n  height: 6px;\n  border-radius: 50%;\n  background-color: #7E30E1;\n  margin-top: 6px;\n  margin-right: 8px;\n  flex-shrink: 0;\n}\n\n.rule-text {\n  font-size: 13px;\n  color: #666;\n  flex: 1;\n  line-height: 1.5;\n}\n\n.preview-tip {\n  display: flex;\n  align-items: center;\n  background-color: rgba(126, 48, 225, 0.05);\n  border-radius: 8px;\n  padding: 12px;\n}\n\n.tip-icon {\n  width: 18px;\n  height: 18px;\n  border-radius: 50%;\n  background-color: #7E30E1;\n  color: #fff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  font-style: italic;\n  margin-right: 10px;\n}\n\n.tip-text {\n  font-size: 12px;\n  color: #7E30E1;\n  flex: 1;\n  line-height: 1.5;\n}\n\n/* 底部操作按钮 */\n.footer-buttons {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  padding: 10px 15px;\n  padding-bottom: calc(10px + env(safe-area-inset-bottom));\n  background-color: #fff;\n  display: flex;\n  justify-content: space-between;\n  box-shadow: 0 -2px 10px rgba(0,0,0,0.05);\n  z-index: 99;\n}\n\n.btn-primary {\n  background: linear-gradient(135deg, #9040FF, #5E35B1);\n  color: #fff;\n  border: none;\n  border-radius: 25px;\n  height: 50px;\n  line-height: 50px;\n  font-size: 16px;\n  font-weight: 500;\n  flex: 1;\n  margin-left: 10px;\n  box-shadow: 0 5px 15px rgba(126, 48, 225, 0.2);\n  transition: all 0.3s;\n  \n  &:active {\n    transform: translateY(2px);\n    box-shadow: 0 3px 8px rgba(126, 48, 225, 0.2);\n  }\n}\n\n.btn-secondary {\n  background-color: #F0F0F0;\n  color: #666;\n  border: none;\n  border-radius: 25px;\n  height: 50px;\n  line-height: 50px;\n  font-size: 16px;\n  font-weight: 500;\n  width: 120px;\n  transition: all 0.3s;\n  \n  &:active {\n    background-color: #E6E6E6;\n  }\n}\n\n/* 开关容器 */\n.switch-container {\n  display: flex;\n  align-items: center;\n  height: 40px;\n}\n\n.price-regular {\n  color: #FF6B00;\n  text-decoration: line-through;\n  font-size: 12px;\n  margin-right: 8px;\n}\n\n.price-market {\n  color: #999;\n  text-decoration: line-through;\n  font-size: 12px;\n  margin-right: 8px;\n}\n\n.confirm-price-market {\n  color: #999;\n  text-decoration: line-through;\n  font-size: 11px;\n  margin-right: 8px;\n  display: block;\n}\n\n.confirm-price-regular {\n  color: #FF6B00;\n  text-decoration: line-through;\n  font-size: 11px;\n  margin-right: 8px;\n  display: block;\n  margin-top: 2px;\n}\n\n/* 套餐拼团样式 */\n.package-settings {\n  margin-top: 15px;\n  padding: 15px;\n  background-color: #F8F9FC;\n  border-radius: 10px;\n}\n\n.form-textarea {\n  width: 100%;\n  height: 80px;\n  background: #FFFFFF;\n  border: 1px solid #E5E7EB;\n  border-radius: 8px;\n  padding: 10px;\n  font-size: 14px;\n  color: #333;\n}\n\n.package-items {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.package-item {\n  background: #FFFFFF;\n  border: 1px solid #E5E7EB;\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.package-item-header {\n  background: #F0F2F5;\n  padding: 10px 15px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.package-item-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n}\n\n.package-item-remove {\n  width: 20px;\n  height: 20px;\n  border-radius: 50%;\n  background: rgba(255, 59, 48, 0.1);\n  color: #FF3B30;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n  line-height: 1;\n}\n\n.package-item-content {\n  padding: 10px 15px;\n}\n\n.package-item-row {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.package-item-row:last-child {\n  margin-bottom: 0;\n}\n\n.package-item-label {\n  width: 80px;\n  font-size: 14px;\n  color: #666;\n}\n\n.package-item-input {\n  flex: 1;\n  height: 36px;\n  background: #F5F7FA;\n  border: 1px solid #E5E7EB;\n  border-radius: 6px;\n  padding: 0 10px;\n  font-size: 14px;\n  color: #333;\n}\n\n.add-package-item {\n  height: 44px;\n  background: rgba(144, 64, 255, 0.1);\n  border: 1px dashed #9040FF;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n}\n\n.add-icon {\n  width: 20px;\n  height: 20px;\n  border-radius: 50%;\n  background: #9040FF;\n  color: #FFFFFF;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n  margin-right: 8px;\n}\n\n.add-text {\n  font-size: 14px;\n  color: #9040FF;\n}\n\n.number-picker.small {\n  height: 32px;\n}\n\n.number-picker.small .number-btn {\n  width: 24px;\n  height: 24px;\n}\n\n.number-picker.small .number-input {\n  width: 40px;\n  height: 24px;\n  font-size: 13px;\n}\n\n/* 套餐预览样式 */\n.package-preview {\n  width: 100px;\n  height: 100px;\n  background: #F5F7FA;\n  border-radius: 8px;\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  margin-right: 15px;\n  overflow: hidden;\n}\n\n.package-badge {\n  position: absolute;\n  top: 0;\n  left: 0;\n  background: #9040FF;\n  color: #FFFFFF;\n  font-size: 10px;\n  padding: 2px 6px;\n  border-radius: 0 0 8px 0;\n}\n\n.package-name {\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n  text-align: center;\n  padding: 0 5px;\n}\n\n.package-items-preview {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.package-item-text {\n  font-size: 10px;\n  color: #666;\n  line-height: 1.4;\n}\n\n.confirm-package {\n  margin-top: 20px;\n  background: #FFFFFF;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n}\n\n.package-description {\n  font-size: 14px;\n  color: #666;\n  line-height: 1.5;\n  margin-bottom: 15px;\n  padding-bottom: 15px;\n  border-bottom: 1px dashed #E5E7EB;\n}\n\n.package-items-list {\n  margin-bottom: 15px;\n}\n\n.package-item-detail {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #F5F7FA;\n}\n\n.package-item-detail:last-child {\n  margin-bottom: 0;\n  padding-bottom: 0;\n  border-bottom: none;\n}\n\n.package-item-number {\n  width: 24px;\n  height: 24px;\n  background: #F0F2F5;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  font-weight: 600;\n  color: #666;\n  margin-right: 10px;\n}\n\n.package-item-info {\n  flex: 1;\n}\n\n.package-item-name {\n  font-size: 14px;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.package-item-meta {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.package-item-price {\n  font-size: 12px;\n  color: #FF3B30;\n}\n\n.package-item-quantity {\n  font-size: 12px;\n  color: #999;\n}\n\n.package-summary {\n  background: #F8FAFC;\n  border-radius: 8px;\n  padding: 10px 15px;\n}\n\n.package-summary-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.package-summary-row:last-child {\n  margin-bottom: 0;\n}\n\n.package-summary-row.highlight {\n  margin: 10px 0;\n  padding: 8px 0;\n  border-top: 1px dashed #E5E7EB;\n  border-bottom: 1px dashed #E5E7EB;\n}\n\n.summary-label {\n  font-size: 13px;\n  color: #666;\n}\n\n.summary-value {\n  font-size: 13px;\n  color: #333;\n  font-weight: 600;\n}\n\n.summary-value.save {\n  color: #FF3B30;\n}\n\n.confirm-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 15px;\n}\n\n/* 支付和核销设置 */\n.confirm-payment-verification {\n  margin-top: 20px;\n  background: #FFFFFF;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n}\n\n.setting-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.setting-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background-color: #7E30E1;\n  color: #FFFFFF;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 10px;\n}\n\n.setting-info {\n  flex: 1;\n}\n\n.setting-label {\n  font-size: 14px;\n  color: #333;\n}\n\n.setting-value {\n  font-size: 14px;\n  color: #666;\n}\n\n/* 预览卡片样式 */\n.preview-card {\n  background-color: #FFFFFF;\n  border-radius: 12rpx;\n  overflow: hidden;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n  margin-bottom: 30rpx;\n}\n\n.preview-header {\n  padding: 20rpx;\n  border-bottom: 1rpx solid #F0F0F0;\n}\n\n.preview-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333333;\n}\n\n.preview-dates {\n  font-size: 24rpx;\n  color: #999999;\n  margin-top: 8rpx;\n  display: block;\n}\n\n.preview-content {\n  padding: 20rpx;\n}\n\n.preview-products {\n  margin-bottom: 20rpx;\n}\n\n.preview-product-item {\n  display: flex;\n  padding: 16rpx 0;\n  border-bottom: 1rpx solid #F0F0F0;\n}\n\n.preview-product-item:last-child {\n  border-bottom: none;\n}\n\n.product-image {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 8rpx;\n  margin-right: 16rpx;\n}\n\n.product-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.product-name {\n  font-size: 28rpx;\n  color: #333333;\n  line-height: 1.4;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.product-price {\n  display: flex;\n  align-items: baseline;\n}\n\n.group-price {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #FF6B6B;\n}\n\n.original-price {\n  font-size: 24rpx;\n  color: #999999;\n  text-decoration: line-through;\n  margin-left: 12rpx;\n}\n\n.group-rules {\n  background-color: #F8F9FA;\n  border-radius: 8rpx;\n  padding: 16rpx;\n}\n\n.rule-item {\n  display: flex;\n  justify-content: space-between;\n  padding: 8rpx 0;\n}\n\n.rule-label {\n  font-size: 26rpx;\n  color: #666666;\n}\n\n.rule-value {\n  font-size: 26rpx;\n  color: #333333;\n  font-weight: 500;\n}\n\n/* 表单操作区 */\n.form-actions {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 40rpx;\n}\n\n.btn-secondary, .btn-primary {\n  flex: 1;\n  height: 80rpx;\n  border-radius: 40rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n  font-weight: 500;\n}\n\n.btn-secondary {\n  background-color: #F5F5F5;\n  color: #666666;\n  margin-right: 20rpx;\n}\n\n.btn-primary {\n  background: linear-gradient(135deg, #6B0FBE, #9013FE);\n  color: #FFFFFF;\n}\n</style>\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/group/create.vue'\nwx.createPage(MiniProgramPage)"], "names": ["distributionMixin", "uni"], "mappings": ";;;AA8eA,MAAK,gBAAiB,MAAW;AACjC,kBAAkB,MAAW;AAC7B,MAAK,aAAc,MAAW;AAC9B,gBAAgB,MAAW;AAC3B,MAAK,gBAAiB,MAAW;AACjC,MAAK,sBAAuB,MAAW;AAEvC,MAAK,4BAA6B,MAAW;AAE7C,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA,EACD,QAAQ,CAACA,4DAAAA,iBAAiB;AAAA;AAAA,EAC1B,OAAO;AACL,WAAO;AAAA,MACL,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,eAAe;AAAA;AAAA,MAGf,UAAU;AAAA;AAAA,QAER,sBAAsB;AAAA;AAAA,QACtB,kBAAkB,CAAE;AAAA;AAAA;AAAA,QAGpB,WAAW;AAAA;AAAA,QACX,WAAW;AAAA;AAAA,QACX,aAAa;AAAA;AAAA,QACb,oBAAoB;AAAA;AAAA,QACpB,cAAc,CAAC;AAAA;AAAA,UACb,MAAM;AAAA,UACN,eAAe;AAAA,UACf,UAAU;AAAA,QACZ,CAAC;AAAA,QACD,aAAa;AAAA;AAAA,QACb,cAAc;AAAA;AAAA,QACd,YAAY;AAAA;AAAA,QACZ,cAAc;AAAA;AAAA,QACd,cAAc;AAAA;AAAA,QACd,cAAc;AAAA;AAAA,QACd,UAAU;AAAA;AAAA,UACR,MAAM;AAAA;AAAA,UACN,WAAW;AAAA;AAAA,UACX,WAAW;AAAA;AAAA,UACX,SAAS;AAAA;AAAA,UACT,SAAS;AAAA;AAAA,UACT,MAAM;AAAA;AAAA,QACP;AAAA,QACD,WAAW;AAAA;AAAA,QACX,OAAO,CAAE;AAAA;AAAA;AAAA,QAGT,aAAa;AAAA;AAAA,QACb,YAAY;AAAA;AAAA,QACZ,kBAAkB;AAAA;AAAA,QAClB,oBAAoB;AAAA;AAAA,QACpB,0BAA0B;AAAA;AAAA,QAC1B,sBAAsB;AAAA,UACpB,SAAS;AAAA,UACT,gBAAgB;AAAA,UAChB,aAAa;AAAA,YACX,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,UACT;AAAA,UACD,cAAc;AAAA,QACf;AAAA,QACD,WAAW;AAAA;AAAA,QACX,eAAe;AAAA;AAAA,QACf,eAAe;AAAA;AAAA,QACf,YAAY;AAAA;AAAA,MACb;AAAA;AAAA,MAGD,aAAa;AAAA,QACX;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,eAAe;AAAA,UACf,OAAO;AAAA,UACP,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,eAAe;AAAA,UACf,OAAO;AAAA,UACP,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,eAAe;AAAA,UACf,OAAO;AAAA,UACP,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,eAAe;AAAA,UACf,OAAO;AAAA,UACP,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,eAAe;AAAA,UACf,OAAO;AAAA,UACP,OAAO;AAAA,QACT;AAAA,MACD;AAAA,MACD,yBAAyB;AAAA;AAAA,MACzB,aAAa,UAAU,KAAK,IAAM;AAAA;AAAA;EAErC;AAAA,EACD,UAAU;AAAA,IACR,qBAAqB;AAEnB,aAAQ,KAAK,cAAc,IAAK;AAAA,IAClC;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPC,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,WAAW;AACT,UAAI,KAAK,cAAc,GAAG;AACxB,aAAK,eAAe;AACpB,aAAK,aAAa,OAAO,KAAK,WAAW;AAAA,MAC3C;AAAA,IACD;AAAA,IACD,WAAW;AAET,UAAI,KAAK,gBAAgB,GAAG;AAE1B,YAAI,KAAK,SAAS,yBAAyB,cAAc,KAAK,SAAS,iBAAiB,WAAW,GAAG;AACpGA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AACD;AAAA,QACF;AAAA,iBACS,KAAK,gBAAgB,GAAG;AAEjC,YAAI,CAAC,KAAK,SAAS,WAAW;AAC5BA,wBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,OAAK,CAAG;AAChD;AAAA,QACF;AAGA,YAAI,KAAK,SAAS,cAAc,WAAW;AACzC,cAAI,CAAC,KAAK,SAAS,aAAa;AAC9BA,0BAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,OAAK,CAAG;AAChD;AAAA,UACF;AAGA,cAAI,eAAe;AACnB,eAAK,SAAS,aAAa,QAAQ,UAAQ;AACzC,gBAAI,CAAC,KAAK,QAAQ,CAAC,KAAK,eAAe;AACrC,6BAAe;AAAA,YACjB;AAAA,UACF,CAAC;AAED,cAAI,cAAc;AAChBA,0BAAG,MAAC,UAAU,EAAE,OAAO,aAAa,MAAM,OAAK,CAAG;AAClD;AAAA,UACF;AAAA,QACF;AAEA,YAAI,CAAC,KAAK,SAAS,aAAa;AAC9BA,wBAAG,MAAC,UAAU,EAAE,OAAO,UAAU,MAAM,OAAK,CAAG;AAC/C;AAAA,QACF;AAEA,YAAI,CAAC,KAAK,SAAS,cAAc;AAC/BA,wBAAG,MAAC,UAAU,EAAE,OAAO,UAAU,MAAM,OAAK,CAAG;AAC/C;AAAA,QACF;AAEA,YAAI,CAAC,KAAK,SAAS,YAAY;AAC7BA,wBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,OAAK,CAAG;AAChD;AAAA,QACF;AAGA,cAAM,cAAc,WAAW,KAAK,SAAS,WAAW;AACxD,cAAM,eAAe,WAAW,KAAK,SAAS,YAAY;AAC1D,cAAM,aAAa,WAAW,KAAK,SAAS,UAAU;AAEtD,YAAI,eAAe,cAAc;AAC/BA,wBAAG,MAAC,UAAU,EAAE,OAAO,aAAa,MAAM,OAAK,CAAG;AAClD;AAAA,QACF;AAEA,YAAI,gBAAgB,YAAY;AAC9BA,wBAAG,MAAC,UAAU,EAAE,OAAO,aAAa,MAAM,OAAK,CAAG;AAClD;AAAA,QACF;AAEA,YAAI,KAAK,SAAS,SAAS,SAAS,SAAS;AAC3C,cAAI,CAAC,KAAK,SAAS,SAAS,aAAa,CAAC,KAAK,SAAS,SAAS,WAAW,CAAC,KAAK,SAAS,SAAS,aAAa,CAAC,KAAK,SAAS,SAAS,SAAS;AAChJA,0BAAG,MAAC,UAAU,EAAE,OAAO,YAAY,MAAM,OAAK,CAAG;AACjD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAGA,UAAI,KAAK,cAAc,GAAG;AACxB,aAAK,eAAe;AACpB,aAAK,aAAa,OAAO,KAAK,WAAW;AAAA,MAC3C;AAAA,IACD;AAAA,IACD,aAAa;AAEXA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAGD,YAAM,aAAa;AAAA;AAAA,QAEjB,WAAW,KAAK,SAAS;AAAA,QACzB,WAAW,KAAK,SAAS;AAAA,QACzB,aAAa,WAAW,KAAK,SAAS,WAAW;AAAA,QACjD,cAAc,WAAW,KAAK,SAAS,YAAY;AAAA,QACnD,YAAY,WAAW,KAAK,SAAS,UAAU;AAAA,QAC/C,cAAc,KAAK,SAAS;AAAA,QAC5B,cAAc,KAAK,SAAS;AAAA,QAC5B,cAAc,KAAK,SAAS;AAAA,QAC5B,WAAW,KAAK,SAAS;AAAA;AAAA,QAGzB,cAAc,KAAK,SAAS,SAAS;AAAA,QACrC,mBAAmB,KAAK,SAAS,SAAS;AAAA,QAC1C,mBAAmB,KAAK,SAAS,SAAS;AAAA,QAC1C,iBAAiB,KAAK,SAAS,SAAS;AAAA,QACxC,iBAAiB,KAAK,SAAS,SAAS;AAAA,QACxC,cAAc,KAAK,SAAS,SAAS;AAAA;AAAA,QAGrC,aAAa,KAAK,SAAS;AAAA,QAC3B,YAAY,KAAK,SAAS;AAAA,QAC1B,kBAAkB,KAAK,SAAS;AAAA,QAChC,oBAAoB,KAAK,SAAS;AAAA,QAClC,0BAA0B,KAAK,SAAS;AAAA;AAI1C,UAAI,KAAK,SAAS,cAAc,UAAU;AAExC,mBAAW,WAAW,KAAK,SAAS,iBAAiB,IAAI,cAAY;AAAA,UACnE,IAAI,QAAQ;AAAA,UACZ,MAAM,QAAQ;AAAA,UACd,OAAO,QAAQ;AAAA,UACf,eAAe,QAAQ;AAAA,UACvB,OAAO,QAAQ;AAAA,UACf,OAAO,QAAQ;AAAA,QAChB,EAAC;AAAA,MACJ,WAAW,KAAK,SAAS,cAAc,WAAW;AAEhD,mBAAW,cAAc,KAAK,SAAS;AACvC,mBAAW,qBAAqB,KAAK,SAAS;AAC9C,mBAAW,eAAe,KAAK,SAAS,aAAa,IAAI,WAAS;AAAA,UAChE,MAAM,KAAK;AAAA,UACX,eAAe,WAAW,KAAK,aAAa;AAAA,UAC5C,UAAU,SAAS,KAAK,QAAQ;AAAA,QACjC,EAAC;AACF,mBAAW,qBAAqB,WAAW,KAAK,4BAA6B,CAAA;AAC7E,mBAAW,eAAe,WAAW,KAAK,iBAAkB,CAAA;AAC5D,mBAAW,aAAa,KAAK;MAC/B;AAGA,UAAI,KAAK,2BAA2B,KAAK,SAAS,qBAAqB,SAAS;AAE9E,mBAAW,uBAAuB,KAAK,SAAS;AAAA,MAClD;AAEAA,oBAAY,MAAA,MAAA,OAAA,gFAAA,SAAS,UAAU;AAG/B,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACZ,CAAC;AAGD,mBAAW,MAAM;AACfA,wBAAG,MAAC,aAAY;AAAA,QACjB,GAAE,GAAI;AAAA,MACR,GAAE,IAAI;AAAA,IACR;AAAA,IACD,iBAAiB;AAEfA,oBAAY,MAAA,MAAA,OAAA,gFAAA,SAAS,KAAK,aAAa;AAAA,IAExC;AAAA,IACD,qBAAqB,YAAY;AAE/BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,KAAK,UAAU;AAAA,QACtB,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,uBAAuB,SAAS;AAE9B,YAAM,QAAQ,KAAK,SAAS,iBAAiB,UAAU,OAAK,EAAE,OAAO,QAAQ,EAAE;AAC/E,UAAI,UAAU,IAAI;AAChB,aAAK,SAAS,iBAAiB,KAAK,OAAO;AAAA,aACtC;AACL,aAAK,SAAS,iBAAiB,OAAO,OAAO,CAAC;AAAA,MAChD;AAAA,IACD;AAAA,IACD,kBAAkB,SAAS;AAEzB,aAAO,KAAK,SAAS,iBAAiB,UAAU,OAAK,EAAE,OAAO,QAAQ,EAAE,MAAM;AAAA,IAC/E;AAAA,IACD,cAAc,OAAO;AAEnB,WAAK,SAAS,iBAAiB,OAAO,OAAO,CAAC;AAAA,IAC/C;AAAA,IACD,wBAAwB;AAEtB,WAAK,SAAS,mBAAmB;IAClC;AAAA,IACD,0BAA0B;AAExBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA;AAAA,IAED,qBAAqB;AACnB,UAAI,KAAK,SAAS,eAAe,GAAG;AAClC,aAAK,SAAS,gBAAgB;AAAA,MAChC;AAAA,IACD;AAAA,IACD,qBAAqB;AACnB,UAAI,KAAK,SAAS,eAAe,KAAK;AACpC,aAAK,SAAS,gBAAgB;AAAA,MAChC;AAAA,IACD;AAAA,IACD,wBAAwB;AACtB,UAAI,KAAK,SAAS,eAAe,GAAG;AAClC,aAAK,SAAS,gBAAgB;AAAA,MAChC;AAAA,IACD;AAAA,IACD,wBAAwB;AACtB,UAAI,KAAK,SAAS,eAAe,KAAK;AACpC,aAAK,SAAS,gBAAgB;AAAA,MAChC;AAAA,IACD;AAAA;AAAA,IAED,iBAAiB;AACf,WAAK,SAAS,aAAa,KAAK;AAAA,QAC9B,MAAM;AAAA,QACN,eAAe;AAAA,QACf,UAAU;AAAA,MACZ,CAAC;AAAA,IACF;AAAA,IACD,kBAAkB,OAAO;AACvB,UAAI,KAAK,SAAS,aAAa,SAAS,GAAG;AACzC,aAAK,SAAS,aAAa,OAAO,OAAO,CAAC;AAAA,MAC5C;AAAA,IACD;AAAA,IACD,sBAAsB,OAAO;AAC3B,UAAI,KAAK,SAAS,aAAa,KAAK,EAAE,WAAW,GAAG;AAClD,aAAK,SAAS,aAAa,KAAK,EAAE,YAAY;AAAA,MAChD;AAAA,IACD;AAAA,IACD,sBAAsB,OAAO;AAC3B,UAAI,KAAK,SAAS,aAAa,KAAK,EAAE,WAAW,IAAI;AACnD,aAAK,SAAS,aAAa,KAAK,EAAE,YAAY;AAAA,MAChD;AAAA,IACD;AAAA,IACD,wBAAwB;AACtB,UAAI,KAAK,SAAS,SAAS,OAAO,GAAG;AACnC,aAAK,SAAS,SAAS,QAAQ;AAAA,MACjC;AAAA,IACD;AAAA,IACD,wBAAwB;AACtB,UAAI,KAAK,SAAS,SAAS,OAAO,IAAI;AACpC,aAAK,SAAS,SAAS,QAAQ;AAAA,MACjC;AAAA,IACD;AAAA,IACD,qBAAqB;AACnB,UAAI,KAAK,SAAS,YAAY,GAAG;AAC/B,aAAK,SAAS,aAAa;AAAA,MAC7B;AAAA,IACD;AAAA,IACD,qBAAqB;AACnB,UAAI,KAAK,SAAS,YAAY,IAAI;AAChC,aAAK,SAAS,aAAa;AAAA,MAC7B;AAAA,IACD;AAAA,IACD,8BAA8B;AAC5B,UAAI,KAAK,SAAS,qBAAqB,GAAG;AACxC,aAAK,SAAS,sBAAsB;AAAA,MACtC;AAAA,IACD;AAAA,IACD,8BAA8B;AAC5B,UAAI,KAAK,SAAS,qBAAqB,IAAI;AACzC,aAAK,SAAS,sBAAsB;AAAA,MACtC;AAAA,IACD;AAAA,IACD,WAAW,MAAM,GAAG;AAElB,WAAK,SAAS,SAAS,GAAG,IAAI,MAAM,IAAI,EAAE,OAAO;AAAA,IAClD;AAAA,IACD,WAAW,MAAM,GAAG;AAElB,WAAK,SAAS,SAAS,GAAG,IAAI,MAAM,IAAI,EAAE,OAAO;AAAA,IAClD;AAAA;AAAA,IAED,uBAAuB;AACrB,UAAI,CAAC,KAAK,SAAS,gBAAgB,KAAK,SAAS,aAAa,WAAW,GAAG;AAC1E,eAAO;AAAA,MACT;AACA,aAAO,KAAK,SAAS,aAAa,OAAO,CAAC,KAAK,SAAS,MAAM,SAAS,KAAK,YAAY,CAAC,GAAG,CAAC;AAAA,IAC9F;AAAA,IACD,8BAA8B;AAC5B,UAAI,CAAC,KAAK,SAAS,gBAAgB,KAAK,SAAS,aAAa,WAAW,GAAG;AAC1E,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,KAAK,SAAS,aAAa,OAAO,CAAC,KAAK,SAAS;AAC7D,cAAM,QAAQ,WAAW,KAAK,iBAAiB,CAAC;AAChD,cAAM,WAAW,SAAS,KAAK,YAAY,CAAC;AAC5C,eAAO,MAAO,QAAQ;AAAA,MACvB,GAAE,CAAC;AACJ,aAAO,MAAM,QAAQ,CAAC;AAAA,IACvB;AAAA,IACD,mBAAmB;AACjB,YAAM,gBAAgB,WAAW,KAAK,4BAA6B,CAAA;AACnE,YAAM,aAAa,WAAW,KAAK,SAAS,cAAc,CAAC;AAC3D,YAAM,UAAU,gBAAgB;AAChC,aAAO,UAAU,IAAI,QAAQ,QAAQ,CAAC,IAAI;AAAA,IAC3C;AAAA;AAAA,IAED,2BAA2B,UAAU;AACnC,WAAK,SAAS,uBAAuB;AAAA,IACtC;AAAA;AAAA,IAGD,4BAA4B;AAG1B,iBAAW,MAAM;AACf,aAAK,0BAA0B;AAAA,MAChC,GAAE,GAAG;AAAA,IACP;AAAA;AAAA,IAGD,MAAM,oBAAoB;AAIxB,UAAI,KAAK,2BAA2B,KAAK,SAAS,qBAAqB,SAAS;AAC9E,cAAM,UAAU,MAAM,KAAK,iCAAiC,SAAS,OAAO;AAC5E,YAAI,CAAC,SAAS;AACZ;AAAA,QACF;AAAA,MACF;AAAA,IAGD;AAAA;AAAA,IAGD,yBAAyB,MAAM;AAC7BA,oBAAY,MAAA,MAAA,OAAA,gFAAA,WAAW,IAAI;AAE3B,UAAI,KAAK,WAAW,WAAW;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,iBACQ,KAAK,WAAW,OAAO;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH,WAAW,KAAK,WAAW,WAAW;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACD;AAAA,EACD,UAAU;AAER,SAAK,0BAAyB;AAAA,EAChC;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACl/BA,GAAG,WAAW,eAAe;"}