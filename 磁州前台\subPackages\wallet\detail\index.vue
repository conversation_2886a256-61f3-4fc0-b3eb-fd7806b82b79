<template>
  <view class="wallet-detail-container">
    <!-- 筛选区域 -->
    <view class="filter-section">
      <view class="date-filter">
        <picker mode="date" :value="startDate" :end="endDate" @change="onStartDateChange">
          <view class="date-item">
            <text class="date-label">开始日期</text>
            <text class="date-value">{{ startDate }}</text>
            <text class="date-icon">▼</text>
          </view>
        </picker>
        <text class="date-separator">至</text>
        <picker mode="date" :value="endDate" :start="startDate" :end="today" @change="onEndDateChange">
          <view class="date-item">
            <text class="date-label">结束日期</text>
            <text class="date-value">{{ endDate }}</text>
            <text class="date-icon">▼</text>
          </view>
        </picker>
      </view>
      
      <view class="type-filter">
        <view 
          v-for="(item, index) in transactionTypes" 
          :key="index"
          class="type-item"
          :class="{ active: selectedType === item.value }"
          @click="selectType(item.value)"
        >
          {{ item.label }}
        </view>
      </view>
    </view>
    
    <!-- 交易记录列表 -->
    <view class="transaction-list" v-if="transactions.length > 0">
      <view class="transaction-item" v-for="(item, index) in transactions" :key="index">
        <view class="transaction-icon" :class="item.type">
          <text class="iconfont" :class="getIconClass(item.type)"></text>
        </view>
        <view class="transaction-info">
          <view class="transaction-title">{{ item.title }}</view>
          <view class="transaction-time">{{ item.time }}</view>
        </view>
        <view class="transaction-amount" :class="{ 'income': item.amount > 0 }">
          {{ item.amount > 0 ? '+' : '' }}{{ item.amount.toFixed(2) }}
        </view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <image class="empty-icon" src="/static/images/tabbar/钱包.png" mode="aspectFit"></image>
      <text class="empty-text">暂无交易记录</text>
    </view>
    
    <!-- 加载更多 -->
    <view class="load-more" v-if="hasMore && transactions.length > 0">
      <text v-if="loading">加载中...</text>
      <text v-else @click="loadMore">点击加载更多</text>
    </view>
  </view>
</template>

<script>
import cashbackService from '@/utils/cashbackService';

export default {
  data() {
    const now = new Date();
    const today = now.toISOString().split('T')[0];
    const thirtyDaysAgo = new Date(now.setDate(now.getDate() - 30)).toISOString().split('T')[0];
    
    return {
      today,
      startDate: thirtyDaysAgo,
      endDate: today,
      selectedType: 'all',
      transactionTypes: [
        { label: '全部', value: 'all' },
        { label: '收入', value: 'income' },
        { label: '支出', value: 'expense' },
        { label: '返利', value: 'cashback' },
        { label: '提现', value: 'withdraw' }
      ],
      transactions: [],
      page: 1,
      limit: 10,
      hasMore: true,
      loading: false
    };
  },
  onLoad() {
    this.fetchTransactions();
  },
  onPullDownRefresh() {
    this.page = 1;
    this.transactions = [];
    this.hasMore = true;
    this.fetchTransactions().then(() => {
      uni.stopPullDownRefresh();
    });
  },
  methods: {
    async fetchTransactions() {
      if (this.loading || !this.hasMore) return;
      
      this.loading = true;
      
      try {
        const params = {
          page: this.page,
          limit: this.limit,
          startDate: this.startDate,
          endDate: this.endDate,
          type: this.selectedType === 'all' ? '' : this.selectedType
        };
        
        const res = await cashbackService.getWalletTransactions(params);
        
        if (res && res.list) {
          if (this.page === 1) {
            this.transactions = res.list;
          } else {
            this.transactions = [...this.transactions, ...res.list];
          }
          
          this.hasMore = res.list.length === this.limit;
          this.page++;
        } else {
          this.hasMore = false;
        }
      } catch (error) {
        uni.showToast({
          title: '获取交易记录失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },
    onStartDateChange(e) {
      this.startDate = e.detail.value;
      this.resetAndFetch();
    },
    onEndDateChange(e) {
      this.endDate = e.detail.value;
      this.resetAndFetch();
    },
    selectType(type) {
      if (this.selectedType === type) return;
      
      this.selectedType = type;
      this.resetAndFetch();
    },
    resetAndFetch() {
      this.page = 1;
      this.transactions = [];
      this.hasMore = true;
      this.fetchTransactions();
    },
    loadMore() {
      this.fetchTransactions();
    },
    getIconClass(type) {
      switch (type) {
        case 'income':
          return 'icon-income';
        case 'expense':
          return 'icon-expense';
        case 'cashback':
          return 'icon-cashback';
        case 'withdraw':
          return 'icon-withdraw';
        default:
          return 'icon-transaction';
      }
    }
  }
};
</script>

<style lang="scss">
.wallet-detail-container {
  min-height: 100vh;
  background-color: #F2F2F7;
  padding-bottom: 30rpx;
}

.filter-section {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.date-filter {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.date-item {
  flex: 1;
  background-color: #F2F2F7;
  border-radius: 10rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
}

.date-label {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 10rpx;
}

.date-value {
  font-size: 28rpx;
  color: #1C1C1E;
}

.date-icon {
  position: absolute;
  right: 20rpx;
  font-size: 24rpx;
  color: #8E8E93;
}

.date-separator {
  margin: 0 20rpx;
  color: #8E8E93;
}

.type-filter {
  display: flex;
  flex-wrap: wrap;
}

.type-item {
  padding: 10rpx 30rpx;
  background-color: #F2F2F7;
  border-radius: 30rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  font-size: 24rpx;
  color: #8E8E93;
}

.type-item.active {
  background-color: #007AFF;
  color: #FFFFFF;
}

.transaction-list {
  background-color: #FFFFFF;
}

.transaction-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #E5E5EA;
}

.transaction-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
}

.transaction-icon.income {
  background-color: rgba(52, 199, 89, 0.1);
}

.transaction-icon.expense {
  background-color: rgba(255, 59, 48, 0.1);
}

.transaction-icon.cashback {
  background-color: rgba(0, 122, 255, 0.1);
}

.transaction-icon.withdraw {
  background-color: rgba(255, 149, 0, 0.1);
}

.transaction-info {
  flex: 1;
}

.transaction-title {
  font-size: 28rpx;
  color: #1C1C1E;
  margin-bottom: 10rpx;
}

.transaction-time {
  font-size: 24rpx;
  color: #8E8E93;
}

.transaction-amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #FF3B30;
}

.transaction-amount.income {
  color: #34C759;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #8E8E93;
}

.load-more {
  text-align: center;
  padding: 30rpx;
  font-size: 28rpx;
  color: #8E8E93;
}
</style> 