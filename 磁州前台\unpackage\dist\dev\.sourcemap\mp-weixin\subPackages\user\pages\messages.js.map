{"version": 3, "file": "messages.js", "sources": ["subPackages/user/pages/messages.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcdXNlclxwYWdlc1xtZXNzYWdlcy52dWU"], "sourcesContent": ["<template>\n  <view class=\"messages-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"navbar-left\" @click=\"goBack\">\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\n      </view>\n      <view class=\"navbar-title\">我的消息</view>\n      <view class=\"navbar-right\" @click=\"clearAllMessages\">\n        <text class=\"clear-text\">清空</text>\n      </view>\n    </view>\n    \n    <!-- 消息分类选项卡 -->\n    <view class=\"tabs-container\" :style=\"{ top: navbarHeight + 'px' }\">\n      <view \n        class=\"tab-item\" \n        v-for=\"(tab, index) in tabs\" \n        :key=\"index\"\n        :class=\"{ active: currentTab === index }\"\n        @click=\"switchTab(index)\"\n      >\n        <text class=\"tab-text\">{{ tab.name }}</text>\n        <view class=\"badge\" v-if=\"tab.unread > 0\">{{ tab.unread > 99 ? '99+' : tab.unread }}</view>\n      </view>\n      <view class=\"tab-line\" :style=\"tabLineStyle\"></view>\n    </view>\n    \n    <!-- 内容区域 -->\n    <view class=\"content-area\" :style=\"{ paddingTop: (navbarHeight + tabsHeight) + 'px' }\">\n      <swiper class=\"content-swiper\" :current=\"currentTab\" @change=\"onSwiperChange\">\n        <!-- 系统消息 -->\n        <swiper-item>\n          <scroll-view scroll-y class=\"tab-scroll\" @scrolltolower=\"loadMore(0)\" refresher-enabled :refresher-triggered=\"refreshing[0]\" @refresherrefresh=\"onRefresh(0)\">\n            <view v-if=\"systemMessages.length > 0\" class=\"message-list\">\n              <view class=\"message-item\" v-for=\"(item, index) in systemMessages\" :key=\"index\" @click=\"viewSystemMessage(item)\">\n                <view class=\"message-left\">\n                  <image class=\"message-icon\" :src=\"item.icon || '/static/images/tabbar/系统通知.png'\" mode=\"aspectFill\"></image>\n                  <view class=\"unread-dot\" v-if=\"!item.isRead\"></view>\n                </view>\n                <view class=\"message-center\">\n                  <view class=\"message-title\">{{ item.title }}</view>\n                  <view class=\"message-content\">{{ item.content }}</view>\n                </view>\n                <view class=\"message-right\">\n                  <text class=\"message-time\">{{ item.time }}</text>\n                </view>\n              </view>\n            </view>\n            <view v-else class=\"empty-view\">\n              <image class=\"empty-icon\" src=\"/static/images/empty.png\" mode=\"aspectFit\"></image>\n              <view class=\"empty-text\">暂无系统消息</view>\n            </view>\n            <view v-if=\"systemMessages.length > 0 && !hasMore[0]\" class=\"list-bottom\">没有更多了</view>\n          </scroll-view>\n        </swiper-item>\n        \n        <!-- 互动消息 -->\n        <swiper-item>\n          <scroll-view scroll-y class=\"tab-scroll\" @scrolltolower=\"loadMore(1)\" refresher-enabled :refresher-triggered=\"refreshing[1]\" @refresherrefresh=\"onRefresh(1)\">\n            <view v-if=\"interactionMessages.length > 0\" class=\"message-list\">\n              <view class=\"message-item\" v-for=\"(item, index) in interactionMessages\" :key=\"index\" @click=\"viewInteractionMessage(item)\">\n                <view class=\"message-left\">\n                  <image class=\"message-avatar\" :src=\"item.userAvatar || '/static/images/default-avatar.png'\" mode=\"aspectFill\"></image>\n                  <view class=\"unread-dot\" v-if=\"!item.isRead\"></view>\n                </view>\n                <view class=\"message-center\">\n                  <view class=\"message-title\">\n                    <text class=\"user-name\">{{ item.userName }}</text>\n                    <text class=\"action-text\">{{ getActionText(item.action) }}</text>\n                  </view>\n                  <view class=\"message-content\">{{ item.content }}</view>\n                </view>\n                <view class=\"message-right\">\n                  <text class=\"message-time\">{{ item.time }}</text>\n                  <image v-if=\"item.contentImage\" class=\"content-preview\" :src=\"item.contentImage\" mode=\"aspectFill\"></image>\n                </view>\n              </view>\n            </view>\n            <view v-else class=\"empty-view\">\n              <image class=\"empty-icon\" src=\"/static/images/empty.png\" mode=\"aspectFit\"></image>\n              <view class=\"empty-text\">暂无互动消息</view>\n            </view>\n            <view v-if=\"interactionMessages.length > 0 && !hasMore[1]\" class=\"list-bottom\">没有更多了</view>\n          </scroll-view>\n        </swiper-item>\n        \n        <!-- 聊天消息 -->\n        <swiper-item>\n          <scroll-view scroll-y class=\"tab-scroll\" @scrolltolower=\"loadMore(2)\" refresher-enabled :refresher-triggered=\"refreshing[2]\" @refresherrefresh=\"onRefresh(2)\">\n            <view v-if=\"chatMessages.length > 0\" class=\"chat-list\">\n              <view class=\"chat-item\" v-for=\"(item, index) in chatMessages\" :key=\"index\" @click=\"navigateToChat(item)\">\n                <view class=\"chat-left\">\n                  <image class=\"chat-avatar\" :src=\"item.userAvatar || '/static/images/default-avatar.png'\" mode=\"aspectFill\"></image>\n                  <view class=\"chat-badge\" v-if=\"item.unreadCount > 0\">{{ item.unreadCount > 99 ? '99+' : item.unreadCount }}</view>\n                </view>\n                <view class=\"chat-center\">\n                  <view class=\"chat-name\">{{ item.userName }}</view>\n                  <view class=\"chat-last-message\">{{ item.lastMessage }}</view>\n                </view>\n                <view class=\"chat-right\">\n                  <text class=\"chat-time\">{{ item.time }}</text>\n                </view>\n              </view>\n            </view>\n            <view v-else class=\"empty-view\">\n              <image class=\"empty-icon\" src=\"/static/images/empty.png\" mode=\"aspectFit\"></image>\n              <view class=\"empty-text\">暂无聊天消息</view>\n            </view>\n            <view v-if=\"chatMessages.length > 0 && !hasMore[2]\" class=\"list-bottom\">没有更多了</view>\n          </scroll-view>\n        </swiper-item>\n      </swiper>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue';\nimport { smartNavigate } from '@/utils/navigation.js';\n\n// Vue 3 Composition API 代码开始\n// 状态栏和导航栏高度\nconst statusBarHeight = ref(20);\nconst navbarHeight = ref(64); // 导航栏高度\nconst tabsHeight = ref(44); // 选项卡高度\n\n// 选项卡数据\nconst tabs = ref([\n  { name: '系统', unread: 2 },\n  { name: '互动', unread: 5 },\n  { name: '聊天', unread: 3 }\n]);\nconst currentTab = ref(0);\n\n// 消息列表数据\nconst systemMessages = ref([]);\nconst interactionMessages = ref([]);\nconst chatMessages = ref([]);\n\n// 分页和加载状态\nconst page = ref([1, 1, 1]); // 当前页码\nconst pageSize = ref(10); // 每页显示数量\nconst hasMore = ref([true, true, true]); // 是否有更多数据\nconst refreshing = ref([false, false, false]); // 刷新状态\n\n// 计算属性：选项卡下划线样式\nconst tabLineStyle = computed(() => {\n  return {\n    transform: `translateX(${currentTab.value * 100}%)`\n  };\n});\n\n// 生命周期钩子\nonMounted(() => {\n  // 获取状态栏高度\n  const sysInfo = uni.getSystemInfoSync();\n  statusBarHeight.value = sysInfo.statusBarHeight;\n  navbarHeight.value = statusBarHeight.value + 44;\n  \n  // 加载初始数据\n  loadSystemMessages();\n});\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n\n// 切换选项卡\nconst switchTab = (index) => {\n  if (currentTab.value === index) return;\n  currentTab.value = index;\n  \n  // 加载对应选项卡的数据\n  switch (index) {\n    case 0:\n      if (systemMessages.value.length === 0) loadSystemMessages();\n      break;\n    case 1:\n      if (interactionMessages.value.length === 0) loadInteractionMessages();\n      break;\n    case 2:\n      if (chatMessages.value.length === 0) loadChatMessages();\n      break;\n  }\n};\n\n// 轮播图切换事件\nconst onSwiperChange = (e) => {\n  switchTab(e.detail.current);\n};\n\n// 清空所有消息\nconst clearAllMessages = () => {\n  uni.showModal({\n    title: '提示',\n    content: '确定要清空当前分类的所有消息吗？',\n    success: (res) => {\n      if (res.confirm) {\n        switch (currentTab.value) {\n          case 0:\n            systemMessages.value = [];\n            tabs.value[0].unread = 0;\n            break;\n          case 1:\n            interactionMessages.value = [];\n            tabs.value[1].unread = 0;\n            break;\n          case 2:\n            chatMessages.value = [];\n            tabs.value[2].unread = 0;\n            break;\n        }\n        \n        uni.showToast({\n          title: '清空成功',\n          icon: 'success'\n        });\n      }\n    }\n  });\n};\n\n// 获取互动动作文本\nconst getActionText = (action) => {\n  const actionMap = {\n    'like': '点赞了你的',\n    'comment': '评论了你的',\n    'collect': '收藏了你的',\n    'follow': '关注了你',\n    'mention': '提到了你'\n  };\n  return actionMap[action] || '';\n};\n\n// 加载系统消息\nconst loadSystemMessages = () => {\n  // 模拟请求延迟\n  setTimeout(() => {\n    // 模拟数据\n    const mockData = Array.from({ length: 10 }, (_, i) => ({\n      id: `system_${page.value[0]}_${i}`,\n      title: `系统通知 ${page.value[0]}_${i}`,\n      content: '您的帐号已完成实名认证，现在可以使用更多功能啦！',\n      time: getRandomTime(),\n      icon: '/static/images/tabbar/系统通知.png',\n      isRead: Math.random() > 0.3 // 70%概率已读\n    }));\n    \n    if (page.value[0] === 1) {\n      systemMessages.value = mockData;\n    } else {\n      systemMessages.value = [...systemMessages.value, ...mockData];\n    }\n    \n    // 模拟是否还有更多数据\n    hasMore.value[0] = page.value[0] < 3;\n    \n    // 关闭刷新状态\n    refreshing.value[0] = false;\n  }, 500);\n};\n\n// 加载互动消息\nconst loadInteractionMessages = () => {\n  // 模拟请求延迟\n  setTimeout(() => {\n    // 模拟数据\n    const actions = ['like', 'comment', 'collect', 'follow', 'mention'];\n    const mockData = Array.from({ length: 10 }, (_, i) => ({\n      id: `interaction_${page.value[1]}_${i}`,\n      userName: `用户${Math.floor(Math.random() * 1000)}`,\n      userAvatar: '/static/images/default-avatar.png',\n      action: actions[Math.floor(Math.random() * actions.length)],\n      content: '这是一条动态内容，描述具体的互动信息...',\n      contentImage: Math.random() > 0.5 ? '/static/images/service1.jpg' : '', // 50%概率有图片\n      time: getRandomTime(),\n      isRead: Math.random() > 0.5 // 50%概率已读\n    }));\n    \n    if (page.value[1] === 1) {\n      interactionMessages.value = mockData;\n    } else {\n      interactionMessages.value = [...interactionMessages.value, ...mockData];\n    }\n    \n    // 模拟是否还有更多数据\n    hasMore.value[1] = page.value[1] < 3;\n    \n    // 关闭刷新状态\n    refreshing.value[1] = false;\n  }, 500);\n};\n\n// 加载聊天消息\nconst loadChatMessages = () => {\n  // 模拟请求延迟\n  setTimeout(() => {\n    // 模拟数据\n    const mockData = Array.from({ length: 10 }, (_, i) => ({\n      id: `chat_${page.value[2]}_${i}`,\n      userName: `用户${Math.floor(Math.random() * 1000)}`,\n      userAvatar: '/static/images/default-avatar.png',\n      lastMessage: '您好，请问有什么可以帮助您的吗？',\n      time: getRandomTime(),\n      unreadCount: Math.floor(Math.random() * 10) // 0-9条未读消息\n    }));\n    \n    if (page.value[2] === 1) {\n      chatMessages.value = mockData;\n    } else {\n      chatMessages.value = [...chatMessages.value, ...mockData];\n    }\n    \n    // 模拟是否还有更多数据\n    hasMore.value[2] = page.value[2] < 3;\n    \n    // 关闭刷新状态\n    refreshing.value[2] = false;\n  }, 500);\n};\n\n// 生成随机时间\nconst getRandomTime = () => {\n  const today = new Date();\n  const randomDays = Math.floor(Math.random() * 7); // 0-6天前\n  const randomHours = Math.floor(Math.random() * 24); // 0-23小时\n  const randomMinutes = Math.floor(Math.random() * 60); // 0-59分钟\n  \n  const date = new Date(today);\n  date.setDate(date.getDate() - randomDays);\n  date.setHours(randomHours, randomMinutes);\n  \n  // 如果是今天，返回时间，否则返回日期\n  if (randomDays === 0) {\n    return `${randomHours.toString().padStart(2, '0')}:${randomMinutes.toString().padStart(2, '0')}`;\n  } else {\n    return `${date.getMonth() + 1}月${date.getDate()}日`;\n  }\n};\n\n// 加载更多数据\nconst loadMore = (tabIndex) => {\n  if (!hasMore.value[tabIndex] || refreshing.value[tabIndex]) return;\n  \n  page.value[tabIndex]++;\n  \n  switch (tabIndex) {\n    case 0:\n      loadSystemMessages();\n      break;\n    case 1:\n      loadInteractionMessages();\n      break;\n    case 2:\n      loadChatMessages();\n      break;\n  }\n};\n\n// 下拉刷新\nconst onRefresh = (tabIndex) => {\n  refreshing.value[tabIndex] = true;\n  page.value[tabIndex] = 1;\n  \n  switch (tabIndex) {\n    case 0:\n      loadSystemMessages();\n      break;\n    case 1:\n      loadInteractionMessages();\n      break;\n    case 2:\n      loadChatMessages();\n      break;\n  }\n};\n\n// 查看系统消息\nconst viewSystemMessage = (item) => {\n  // 标记为已读\n  if (!item.isRead) {\n    item.isRead = true;\n    updateUnreadCount(0);\n  }\n  \n  // 导航到消息详情页\n  smartNavigate(`/pages/my/message-detail?id=${item.id}&type=system`);\n};\n\n// 查看互动消息\nconst viewInteractionMessage = (item) => {\n  // 标记为已读\n  if (!item.isRead) {\n    item.isRead = true;\n    updateUnreadCount(1);\n  }\n  \n  // 根据不同类型的互动跳转到对应页面\n  if (item.action === 'follow') {\n    smartNavigate(`/pages/my/profile?userId=${item.userId}`);\n  } else {\n    smartNavigate(`/pages/publish/info-detail?id=${item.contentId}`);\n  }\n};\n\n// 跳转到聊天页面\nconst navigateToChat = (item) => {\n  // 重置未读数\n  item.unreadCount = 0;\n  updateUnreadCount(2);\n  \n  // 导航到聊天页面\n  smartNavigate(`/pages/message/chat?userId=${item.id}`);\n};\n\n// 更新未读消息数量\nconst updateUnreadCount = (tabIndex) => {\n  let count = 0;\n  \n  switch (tabIndex) {\n    case 0:\n      count = systemMessages.value.filter(item => !item.isRead).length;\n      break;\n    case 1:\n      count = interactionMessages.value.filter(item => !item.isRead).length;\n      break;\n    case 2:\n      count = chatMessages.value.reduce((sum, item) => sum + item.unreadCount, 0);\n      break;\n  }\n  \n  tabs.value[tabIndex].unread = count;\n};\n// Vue 3 Composition API 代码结束\n</script>\n\n<style>\n.messages-container {\n  min-height: 100vh;\n  background-color: #f5f7fa;\n}\n\n/* 自定义导航栏 */\n.custom-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 44px;\n  background-color: #0052CC;\n  color: #fff;\n  display: flex;\n  align-items: center;\n  padding: 0 15px;\n  z-index: 999;\n}\n\n.navbar-left {\n  width: 80rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n}\n\n.back-icon {\n  width: 40rpx;\n  height: 40rpx;\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 500;\n}\n\n.navbar-right {\n  width: 80rpx;\n  display: flex;\n  justify-content: flex-end;\n}\n\n.clear-text {\n  font-size: 14px;\n  color: #fff;\n}\n\n/* 选项卡 */\n.tabs-container {\n  position: fixed;\n  display: flex;\n  width: 100%;\n  background-color: #fff;\n  z-index: 100;\n  border-bottom: 1rpx solid #f0f0f0;\n  box-sizing: border-box;\n  height: 44px;\n}\n\n.tab-item {\n  flex: 1;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  box-sizing: border-box;\n}\n\n.tab-text {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 400;\n  transition: all 0.3s;\n}\n\n.tab-item.active .tab-text {\n  color: #0066FF;\n  font-weight: 500;\n}\n\n.badge {\n  position: absolute;\n  top: 8rpx;\n  right: 50%;\n  margin-right: -50rpx;\n  min-width: 32rpx;\n  height: 32rpx;\n  background-color: #ff5252;\n  color: #fff;\n  font-size: 20rpx;\n  border-radius: 16rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 8rpx;\n}\n\n.tab-line {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  height: 6rpx;\n  width: 33.3% !important;\n  background-color: #0066FF;\n  border-radius: 6rpx;\n  transition: transform 0.3s ease;\n  z-index: 101;\n  transform: translateX(0);\n}\n\n/* 内容区域 */\n.content-area {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.content-swiper {\n  width: 100%;\n  height: calc(100vh - 108px); /* 状态栏(~20px) + 导航栏(44px) + 选项卡(44px) */\n}\n\n.tab-scroll {\n  height: 100%;\n  box-sizing: border-box;\n}\n\n/* 消息列表 */\n.message-list, .chat-list {\n  padding: 0 30rpx;\n}\n\n.message-item, .chat-item {\n  display: flex;\n  padding: 25rpx 0;\n  border-bottom: 1rpx solid #f5f5f5;\n}\n\n.message-left, .chat-left {\n  position: relative;\n  margin-right: 20rpx;\n}\n\n.message-icon {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 20rpx;\n  background-color: #f5f7fa;\n}\n\n.message-avatar, .chat-avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 40rpx;\n  background-color: #f5f7fa;\n}\n\n.unread-dot {\n  position: absolute;\n  top: 0;\n  right: 0;\n  width: 16rpx;\n  height: 16rpx;\n  background-color: #ff5252;\n  border-radius: 8rpx;\n}\n\n.chat-badge {\n  position: absolute;\n  top: -8rpx;\n  right: -8rpx;\n  min-width: 32rpx;\n  height: 32rpx;\n  background-color: #ff5252;\n  color: #fff;\n  font-size: 20rpx;\n  border-radius: 16rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 8rpx;\n}\n\n.message-center, .chat-center {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  overflow: hidden;\n}\n\n.message-title, .chat-name {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n  margin-bottom: 10rpx;\n  display: flex;\n  align-items: center;\n}\n\n.user-name {\n  max-width: 200rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.action-text {\n  color: #666;\n  font-weight: 400;\n}\n\n.message-content, .chat-last-message {\n  font-size: 26rpx;\n  color: #999;\n  line-height: 1.4;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 1;\n  -webkit-box-orient: vertical;\n}\n\n.message-right, .chat-right {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n  justify-content: space-between;\n  margin-left: 20rpx;\n}\n\n.message-time, .chat-time {\n  font-size: 24rpx;\n  color: #999;\n  margin-bottom: 10rpx;\n}\n\n.content-preview {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 8rpx;\n  background-color: #f5f7fa;\n}\n\n/* 空状态 */\n.empty-view {\n  padding: 120rpx 0;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.empty-icon {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 20rpx;\n}\n\n.empty-text {\n  font-size: 28rpx;\n  color: #999;\n}\n\n/* 列表底部 */\n.list-bottom {\n  text-align: center;\n  font-size: 24rpx;\n  color: #999;\n  padding: 30rpx 0;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/user/pages/messages.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onMounted", "uni", "smartNavigate", "MiniProgramPage"], "mappings": ";;;;;;;AA2HA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,eAAeA,cAAAA,IAAI,EAAE;AAC3B,UAAM,aAAaA,cAAAA,IAAI,EAAE;AAGzB,UAAM,OAAOA,cAAAA,IAAI;AAAA,MACf,EAAE,MAAM,MAAM,QAAQ,EAAG;AAAA,MACzB,EAAE,MAAM,MAAM,QAAQ,EAAG;AAAA,MACzB,EAAE,MAAM,MAAM,QAAQ,EAAG;AAAA,IAC3B,CAAC;AACD,UAAM,aAAaA,cAAAA,IAAI,CAAC;AAGxB,UAAM,iBAAiBA,cAAAA,IAAI,CAAA,CAAE;AAC7B,UAAM,sBAAsBA,cAAAA,IAAI,CAAA,CAAE;AAClC,UAAM,eAAeA,cAAAA,IAAI,CAAA,CAAE;AAG3B,UAAM,OAAOA,cAAG,IAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AACTA,kBAAG,IAAC,EAAE;AACvB,UAAM,UAAUA,cAAG,IAAC,CAAC,MAAM,MAAM,IAAI,CAAC;AACtC,UAAM,aAAaA,cAAG,IAAC,CAAC,OAAO,OAAO,KAAK,CAAC;AAG5C,UAAM,eAAeC,cAAQ,SAAC,MAAM;AAClC,aAAO;AAAA,QACL,WAAW,cAAc,WAAW,QAAQ,GAAG;AAAA,MACnD;AAAA,IACA,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AAEd,YAAM,UAAUC,oBAAI;AACpB,sBAAgB,QAAQ,QAAQ;AAChC,mBAAa,QAAQ,gBAAgB,QAAQ;AAG7C;IACF,CAAC;AAGD,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,YAAY,CAAC,UAAU;AAC3B,UAAI,WAAW,UAAU;AAAO;AAChC,iBAAW,QAAQ;AAGnB,cAAQ,OAAK;AAAA,QACX,KAAK;AACH,cAAI,eAAe,MAAM,WAAW;AAAG,+BAAkB;AACzD;AAAA,QACF,KAAK;AACH,cAAI,oBAAoB,MAAM,WAAW;AAAG,oCAAuB;AACnE;AAAA,QACF,KAAK;AACH,cAAI,aAAa,MAAM,WAAW;AAAG,6BAAgB;AACrD;AAAA,MACH;AAAA,IACH;AAGA,UAAM,iBAAiB,CAAC,MAAM;AAC5B,gBAAU,EAAE,OAAO,OAAO;AAAA,IAC5B;AAGA,UAAM,mBAAmB,MAAM;AAC7BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,oBAAQ,WAAW,OAAK;AAAA,cACtB,KAAK;AACH,+BAAe,QAAQ;AACvB,qBAAK,MAAM,CAAC,EAAE,SAAS;AACvB;AAAA,cACF,KAAK;AACH,oCAAoB,QAAQ;AAC5B,qBAAK,MAAM,CAAC,EAAE,SAAS;AACvB;AAAA,cACF,KAAK;AACH,6BAAa,QAAQ;AACrB,qBAAK,MAAM,CAAC,EAAE,SAAS;AACvB;AAAA,YACH;AAEDA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,CAAC,WAAW;AAChC,YAAM,YAAY;AAAA,QAChB,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,MACf;AACE,aAAO,UAAU,MAAM,KAAK;AAAA,IAC9B;AAGA,UAAM,qBAAqB,MAAM;AAE/B,iBAAW,MAAM;AAEf,cAAM,WAAW,MAAM,KAAK,EAAE,QAAQ,GAAI,GAAE,CAAC,GAAG,OAAO;AAAA,UACrD,IAAI,UAAU,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC;AAAA,UAChC,OAAO,QAAQ,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC;AAAA,UACjC,SAAS;AAAA,UACT,MAAM,cAAe;AAAA,UACrB,MAAM;AAAA,UACN,QAAQ,KAAK,OAAM,IAAK;AAAA;AAAA,QACzB,EAAC;AAEF,YAAI,KAAK,MAAM,CAAC,MAAM,GAAG;AACvB,yBAAe,QAAQ;AAAA,QAC7B,OAAW;AACL,yBAAe,QAAQ,CAAC,GAAG,eAAe,OAAO,GAAG,QAAQ;AAAA,QAC7D;AAGD,gBAAQ,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI;AAGnC,mBAAW,MAAM,CAAC,IAAI;AAAA,MACvB,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,0BAA0B,MAAM;AAEpC,iBAAW,MAAM;AAEf,cAAM,UAAU,CAAC,QAAQ,WAAW,WAAW,UAAU,SAAS;AAClE,cAAM,WAAW,MAAM,KAAK,EAAE,QAAQ,GAAI,GAAE,CAAC,GAAG,OAAO;AAAA,UACrD,IAAI,eAAe,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC;AAAA,UACrC,UAAU,KAAK,KAAK,MAAM,KAAK,OAAM,IAAK,GAAI,CAAC;AAAA,UAC/C,YAAY;AAAA,UACZ,QAAQ,QAAQ,KAAK,MAAM,KAAK,OAAQ,IAAG,QAAQ,MAAM,CAAC;AAAA,UAC1D,SAAS;AAAA,UACT,cAAc,KAAK,OAAQ,IAAG,MAAM,gCAAgC;AAAA;AAAA,UACpE,MAAM,cAAe;AAAA,UACrB,QAAQ,KAAK,OAAM,IAAK;AAAA;AAAA,QACzB,EAAC;AAEF,YAAI,KAAK,MAAM,CAAC,MAAM,GAAG;AACvB,8BAAoB,QAAQ;AAAA,QAClC,OAAW;AACL,8BAAoB,QAAQ,CAAC,GAAG,oBAAoB,OAAO,GAAG,QAAQ;AAAA,QACvE;AAGD,gBAAQ,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI;AAGnC,mBAAW,MAAM,CAAC,IAAI;AAAA,MACvB,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,mBAAmB,MAAM;AAE7B,iBAAW,MAAM;AAEf,cAAM,WAAW,MAAM,KAAK,EAAE,QAAQ,GAAI,GAAE,CAAC,GAAG,OAAO;AAAA,UACrD,IAAI,QAAQ,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC;AAAA,UAC9B,UAAU,KAAK,KAAK,MAAM,KAAK,OAAM,IAAK,GAAI,CAAC;AAAA,UAC/C,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,MAAM,cAAe;AAAA,UACrB,aAAa,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE;AAAA;AAAA,QAC3C,EAAC;AAEF,YAAI,KAAK,MAAM,CAAC,MAAM,GAAG;AACvB,uBAAa,QAAQ;AAAA,QAC3B,OAAW;AACL,uBAAa,QAAQ,CAAC,GAAG,aAAa,OAAO,GAAG,QAAQ;AAAA,QACzD;AAGD,gBAAQ,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI;AAGnC,mBAAW,MAAM,CAAC,IAAI;AAAA,MACvB,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,gBAAgB,MAAM;AAC1B,YAAM,QAAQ,oBAAI;AAClB,YAAM,aAAa,KAAK,MAAM,KAAK,OAAM,IAAK,CAAC;AAC/C,YAAM,cAAc,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE;AACjD,YAAM,gBAAgB,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE;AAEnD,YAAM,OAAO,IAAI,KAAK,KAAK;AAC3B,WAAK,QAAQ,KAAK,QAAS,IAAG,UAAU;AACxC,WAAK,SAAS,aAAa,aAAa;AAGxC,UAAI,eAAe,GAAG;AACpB,eAAO,GAAG,YAAY,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC,IAAI,cAAc,SAAQ,EAAG,SAAS,GAAG,GAAG,CAAC;AAAA,MAClG,OAAS;AACL,eAAO,GAAG,KAAK,aAAa,CAAC,IAAI,KAAK,SAAS;AAAA,MAChD;AAAA,IACH;AAGA,UAAM,WAAW,CAAC,aAAa;AAC7B,UAAI,CAAC,QAAQ,MAAM,QAAQ,KAAK,WAAW,MAAM,QAAQ;AAAG;AAE5D,WAAK,MAAM,QAAQ;AAEnB,cAAQ,UAAQ;AAAA,QACd,KAAK;AACH;AACA;AAAA,QACF,KAAK;AACH;AACA;AAAA,QACF,KAAK;AACH;AACA;AAAA,MACH;AAAA,IACH;AAGA,UAAM,YAAY,CAAC,aAAa;AAC9B,iBAAW,MAAM,QAAQ,IAAI;AAC7B,WAAK,MAAM,QAAQ,IAAI;AAEvB,cAAQ,UAAQ;AAAA,QACd,KAAK;AACH;AACA;AAAA,QACF,KAAK;AACH;AACA;AAAA,QACF,KAAK;AACH;AACA;AAAA,MACH;AAAA,IACH;AAGA,UAAM,oBAAoB,CAAC,SAAS;AAElC,UAAI,CAAC,KAAK,QAAQ;AAChB,aAAK,SAAS;AACd,0BAAkB,CAAC;AAAA,MACpB;AAGDC,uBAAa,cAAC,+BAA+B,KAAK,EAAE,cAAc;AAAA,IACpE;AAGA,UAAM,yBAAyB,CAAC,SAAS;AAEvC,UAAI,CAAC,KAAK,QAAQ;AAChB,aAAK,SAAS;AACd,0BAAkB,CAAC;AAAA,MACpB;AAGD,UAAI,KAAK,WAAW,UAAU;AAC5BA,yBAAAA,cAAc,4BAA4B,KAAK,MAAM,EAAE;AAAA,MAC3D,OAAS;AACLA,yBAAAA,cAAc,iCAAiC,KAAK,SAAS,EAAE;AAAA,MAChE;AAAA,IACH;AAGA,UAAM,iBAAiB,CAAC,SAAS;AAE/B,WAAK,cAAc;AACnB,wBAAkB,CAAC;AAGnBA,uBAAAA,cAAc,8BAA8B,KAAK,EAAE,EAAE;AAAA,IACvD;AAGA,UAAM,oBAAoB,CAAC,aAAa;AACtC,UAAI,QAAQ;AAEZ,cAAQ,UAAQ;AAAA,QACd,KAAK;AACH,kBAAQ,eAAe,MAAM,OAAO,UAAQ,CAAC,KAAK,MAAM,EAAE;AAC1D;AAAA,QACF,KAAK;AACH,kBAAQ,oBAAoB,MAAM,OAAO,UAAQ,CAAC,KAAK,MAAM,EAAE;AAC/D;AAAA,QACF,KAAK;AACH,kBAAQ,aAAa,MAAM,OAAO,CAAC,KAAK,SAAS,MAAM,KAAK,aAAa,CAAC;AAC1E;AAAA,MACH;AAED,WAAK,MAAM,QAAQ,EAAE,SAAS;AAAA,IAChC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjbA,GAAG,WAAWC,SAAe;"}