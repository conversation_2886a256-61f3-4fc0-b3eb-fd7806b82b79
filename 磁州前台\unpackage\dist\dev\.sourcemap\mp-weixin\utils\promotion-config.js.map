{"version": 3, "file": "promotion-config.js", "sources": ["utils/promotion-config.js"], "sourcesContent": ["// 推广营销配置工具\n// 用于前台ConfigurablePremiumActions组件获取后台配置\n\n// 配置缓存\nlet configCache = null\nlet lastUpdateTime = 0\nconst CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存\n\n// 默认配置（后台服务不可用时的备用配置）\nconst defaultConfig = {\n  buttons: {\n    publish: {\n      ad: {\n        enabled: true,\n        title: '看广告发布',\n        description: '看一个广告免费发布一天',\n        carpoolDescription: '看一个广告发布一条信息',\n        icon: '/static/images/premium/ad-publish.png',\n        tag: '免费'\n      },\n      paid: {\n        enabled: true,\n        title: '付费发布',\n        description: '3天/1周/1个月任选',\n        carpoolDescription: '付费1元发布一条信息',\n        icon: '/static/images/premium/paid-publish.png',\n        tag: '付费'\n      }\n    },\n    top: {\n      ad: {\n        enabled: true,\n        title: '广告置顶',\n        description: '观看30秒广告获得2小时置顶',\n        duration: '2小时',\n        icon: '/static/images/premium/ad-top.png',\n        tag: '免费'\n      },\n      paid: {\n        enabled: true,\n        title: '付费置顶',\n        description: '3天/1周/1个月任选',\n        icon: '/static/images/premium/paid-top.png',\n        tag: '付费'\n      }\n    },\n    refresh: {\n      ad: {\n        enabled: true,\n        title: '广告刷新',\n        description: '观看15秒广告刷新一次',\n        icon: '/static/images/premium/ad-refresh.png',\n        tag: '免费'\n      },\n      paid: {\n        enabled: true,\n        title: '付费刷新',\n        description: '5次/10次/20次任选',\n        icon: '/static/images/premium/paid-refresh.png',\n        tag: '付费'\n      }\n    },\n    join: {\n      ad: {\n        enabled: true,\n        title: '看广告入驻',\n        description: '观看30秒广告获得1个月免费特权',\n        icon: '/static/images/premium/ad-join.png',\n        tag: '免费'\n      },\n      paid: {\n        enabled: true,\n        title: '付费入驻',\n        description: '1个月/3个月/1年任选',\n        icon: '/static/images/premium/paid-join.png',\n        tag: '付费'\n      }\n    },\n    renew: {\n      ad: {\n        enabled: true,\n        title: '看广告续费',\n        description: '观看30秒广告延长7天会员',\n        icon: '/static/images/premium/ad-renew.png',\n        tag: '免费'\n      },\n      paid: {\n        enabled: true,\n        title: '付费续费',\n        description: '1个月/3个月/6个月任选',\n        icon: '/static/images/premium/paid-renew.png',\n        tag: '付费'\n      }\n    }\n  },\n  pricing: {\n    publish: [\n      { duration: '3天', price: '2.8', recommended: false },\n      { duration: '1周', price: '5.8', recommended: true },\n      { duration: '1个月', price: '19.8', recommended: false }\n    ],\n    top: [\n      { duration: '3天', price: '2.8', recommended: false },\n      { duration: '1周', price: '5.8', recommended: true },\n      { duration: '1个月', price: '19.8', recommended: false }\n    ],\n    refresh: [\n      { duration: '5次', price: '1.8', recommended: false },\n      { duration: '10次', price: '2.8', recommended: true },\n      { duration: '20次', price: '4.8', recommended: false }\n    ],\n    join: [\n      { duration: '1个月', price: '99', recommended: false },\n      { duration: '3个月', price: '199', recommended: true },\n      { duration: '1年', price: '599', recommended: false }\n    ],\n    renew: [\n      { duration: '1个月', price: '99', recommended: false },\n      { duration: '3个月', price: '199', recommended: true },\n      { duration: '6个月', price: '359', recommended: false }\n    ]\n  },\n  toggles: {\n    global: {\n      enabled: true,\n      defaultShowMode: 'direct'\n    },\n    pages: {\n      publish: { enabled: true, adEnabled: true, paidEnabled: true, carpoolSpecial: true, priority: 'equal' },\n      top: { enabled: true, adEnabled: true, paidEnabled: true, supportedTypes: ['merchant_top', 'carpool_top', 'publish_top'] },\n      refresh: { enabled: true, adEnabled: true, paidEnabled: true },\n      join: { enabled: true, adEnabled: true, paidEnabled: true },\n      renew: { enabled: true, adEnabled: true, paidEnabled: true }\n    },\n    advanced: {\n      debugMode: false,\n      logging: true,\n      cacheConfig: true,\n      updateInterval: 300\n    }\n  },\n  adReward: {\n    adUnitId: 'adunit-xxxxxxxxx',\n    rewardRules: {\n      publish: { type: 'duration', value: 1, dailyLimit: 5 },\n      top: { type: 'duration', value: 2, dailyLimit: 3 },\n      refresh: { type: 'count', value: 1, dailyLimit: 10 },\n      join: { type: 'duration', value: 30, dailyLimit: 1 },\n      renew: { type: 'duration', value: 7, dailyLimit: 1 }\n    }\n  }\n}\n\n// 从后台获取配置\nconst fetchConfigFromBackend = async () => {\n  try {\n    // 这里应该是实际的后台API地址\n    const response = await uni.request({\n      url: 'https://your-backend-api.com/api/promotion/complete-config',\n      method: 'GET',\n      timeout: 5000\n    })\n    \n    if (response.statusCode === 200 && response.data) {\n      return response.data\n    } else {\n      throw new Error('获取配置失败')\n    }\n  } catch (error) {\n    console.warn('从后台获取推广配置失败，使用默认配置:', error)\n    return defaultConfig\n  }\n}\n\n// 获取推广配置\nexport const getPromotionConfig = async (forceRefresh = false) => {\n  const now = Date.now()\n  \n  // 检查缓存是否有效\n  if (!forceRefresh && configCache && (now - lastUpdateTime) < CACHE_DURATION) {\n    return configCache\n  }\n  \n  try {\n    // 从后台获取最新配置\n    const config = await fetchConfigFromBackend()\n    \n    // 更新缓存\n    configCache = config\n    lastUpdateTime = now\n    \n    // 保存到本地存储\n    try {\n      uni.setStorageSync('promotion_config', config)\n      uni.setStorageSync('promotion_config_time', now)\n    } catch (e) {\n      console.warn('保存配置到本地存储失败:', e)\n    }\n    \n    return config\n  } catch (error) {\n    console.error('获取推广配置失败:', error)\n    \n    // 尝试从本地存储获取\n    try {\n      const localConfig = uni.getStorageSync('promotion_config')\n      const localTime = uni.getStorageSync('promotion_config_time')\n      \n      if (localConfig && localTime && (now - localTime) < 24 * 60 * 60 * 1000) { // 24小时内的本地缓存\n        configCache = localConfig\n        return localConfig\n      }\n    } catch (e) {\n      console.warn('从本地存储获取配置失败:', e)\n    }\n    \n    // 返回默认配置\n    return defaultConfig\n  }\n}\n\n// 获取按钮配置\nexport const getButtonConfig = async (pageType, optionType) => {\n  const config = await getPromotionConfig()\n  \n  // 检查全局开关\n  if (!config.toggles.global.enabled) {\n    return null\n  }\n  \n  // 解析页面类型\n  let configKey = 'publish'\n  if (pageType.includes('top')) {\n    configKey = 'top'\n  } else if (pageType.includes('refresh')) {\n    configKey = 'refresh'\n  } else if (pageType.includes('join')) {\n    configKey = 'join'\n  } else if (pageType.includes('renew')) {\n    configKey = 'renew'\n  }\n  \n  // 检查页面开关\n  const pageToggle = config.toggles.pages[configKey]\n  if (!pageToggle || !pageToggle.enabled) {\n    return null\n  }\n  \n  // 检查选项开关\n  if (optionType === 'ad' && !pageToggle.adEnabled) {\n    return null\n  }\n  if (optionType === 'paid' && !pageToggle.paidEnabled) {\n    return null\n  }\n  \n  // 返回按钮配置\n  const buttonConfig = config.buttons[configKey]\n  if (!buttonConfig) {\n    return null\n  }\n  \n  return buttonConfig[optionType]\n}\n\n// 获取价格配置\nexport const getPricingConfig = async (action) => {\n  const config = await getPromotionConfig()\n  \n  // 映射操作到配置键\n  let configKey = 'publish'\n  if (action === 'top') {\n    configKey = 'top'\n  } else if (action === 'refresh') {\n    configKey = 'refresh'\n  } else if (action === 'join') {\n    configKey = 'join'\n  } else if (action === 'renew') {\n    configKey = 'renew'\n  }\n  \n  return config.pricing[configKey] || []\n}\n\n// 获取广告奖励配置\nexport const getAdRewardConfig = async (action) => {\n  const config = await getPromotionConfig()\n  \n  const rewardRule = config.adReward.rewardRules[action]\n  if (!rewardRule) {\n    return null\n  }\n  \n  return {\n    adUnitId: config.adReward.adUnitId,\n    rewardType: rewardRule.type,\n    rewardValue: rewardRule.value,\n    dailyLimit: rewardRule.dailyLimit\n  }\n}\n\n// 检查功能是否启用\nexport const isFeatureEnabled = async (pageType, optionType = null) => {\n  const config = await getPromotionConfig()\n  \n  // 检查全局开关\n  if (!config.toggles.global.enabled) {\n    return false\n  }\n  \n  // 解析页面类型\n  let configKey = 'publish'\n  if (pageType.includes('top')) {\n    configKey = 'top'\n  } else if (pageType.includes('refresh')) {\n    configKey = 'refresh'\n  } else if (pageType.includes('join')) {\n    configKey = 'join'\n  } else if (pageType.includes('renew')) {\n    configKey = 'renew'\n  }\n  \n  const pageToggle = config.toggles.pages[configKey]\n  if (!pageToggle || !pageToggle.enabled) {\n    return false\n  }\n  \n  // 如果指定了选项类型，检查选项开关\n  if (optionType === 'ad') {\n    return pageToggle.adEnabled\n  }\n  if (optionType === 'paid') {\n    return pageToggle.paidEnabled\n  }\n  \n  return true\n}\n\n// 记录用户操作日志\nexport const logUserAction = async (action, data) => {\n  const config = await getPromotionConfig()\n  \n  if (!config.toggles.advanced.logging) {\n    return\n  }\n  \n  try {\n    // 发送日志到后台\n    uni.request({\n      url: 'https://your-backend-api.com/api/promotion/log',\n      method: 'POST',\n      data: {\n        action,\n        data,\n        timestamp: Date.now(),\n        userAgent: navigator.userAgent || 'unknown'\n      }\n    })\n  } catch (error) {\n    console.warn('发送操作日志失败:', error)\n  }\n}\n\n// 清除配置缓存\nexport const clearConfigCache = () => {\n  configCache = null\n  lastUpdateTime = 0\n  \n  try {\n    uni.removeStorageSync('promotion_config')\n    uni.removeStorageSync('promotion_config_time')\n  } catch (e) {\n    console.warn('清除本地配置缓存失败:', e)\n  }\n}\n\n// 导出默认配置（用于调试）\nexport { defaultConfig }\n"], "names": ["uni"], "mappings": ";;AAIA,IAAI,cAAc;AAClB,IAAI,iBAAiB;AACrB,MAAM,iBAAiB,IAAI,KAAK;AAGhC,MAAM,gBAAgB;AAAA,EACpB,SAAS;AAAA,IACP,SAAS;AAAA,MACP,IAAI;AAAA,QACF,SAAS;AAAA,QACT,OAAO;AAAA,QACP,aAAa;AAAA,QACb,oBAAoB;AAAA,QACpB,MAAM;AAAA,QACN,KAAK;AAAA,MACN;AAAA,MACD,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,OAAO;AAAA,QACP,aAAa;AAAA,QACb,oBAAoB;AAAA,QACpB,MAAM;AAAA,QACN,KAAK;AAAA,MACN;AAAA,IACF;AAAA,IACD,KAAK;AAAA,MACH,IAAI;AAAA,QACF,SAAS;AAAA,QACT,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,MAAM;AAAA,QACN,KAAK;AAAA,MACN;AAAA,MACD,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,KAAK;AAAA,MACN;AAAA,IACF;AAAA,IACD,SAAS;AAAA,MACP,IAAI;AAAA,QACF,SAAS;AAAA,QACT,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,KAAK;AAAA,MACN;AAAA,MACD,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,KAAK;AAAA,MACN;AAAA,IACF;AAAA,IACD,MAAM;AAAA,MACJ,IAAI;AAAA,QACF,SAAS;AAAA,QACT,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,KAAK;AAAA,MACN;AAAA,MACD,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,KAAK;AAAA,MACN;AAAA,IACF;AAAA,IACD,OAAO;AAAA,MACL,IAAI;AAAA,QACF,SAAS;AAAA,QACT,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,KAAK;AAAA,MACN;AAAA,MACD,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,KAAK;AAAA,MACN;AAAA,IACF;AAAA,EACF;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AAAA,MACP,EAAE,UAAU,MAAM,OAAO,OAAO,aAAa,MAAO;AAAA,MACpD,EAAE,UAAU,MAAM,OAAO,OAAO,aAAa,KAAM;AAAA,MACnD,EAAE,UAAU,OAAO,OAAO,QAAQ,aAAa,MAAO;AAAA,IACvD;AAAA,IACD,KAAK;AAAA,MACH,EAAE,UAAU,MAAM,OAAO,OAAO,aAAa,MAAO;AAAA,MACpD,EAAE,UAAU,MAAM,OAAO,OAAO,aAAa,KAAM;AAAA,MACnD,EAAE,UAAU,OAAO,OAAO,QAAQ,aAAa,MAAO;AAAA,IACvD;AAAA,IACD,SAAS;AAAA,MACP,EAAE,UAAU,MAAM,OAAO,OAAO,aAAa,MAAO;AAAA,MACpD,EAAE,UAAU,OAAO,OAAO,OAAO,aAAa,KAAM;AAAA,MACpD,EAAE,UAAU,OAAO,OAAO,OAAO,aAAa,MAAO;AAAA,IACtD;AAAA,IACD,MAAM;AAAA,MACJ,EAAE,UAAU,OAAO,OAAO,MAAM,aAAa,MAAO;AAAA,MACpD,EAAE,UAAU,OAAO,OAAO,OAAO,aAAa,KAAM;AAAA,MACpD,EAAE,UAAU,MAAM,OAAO,OAAO,aAAa,MAAO;AAAA,IACrD;AAAA,IACD,OAAO;AAAA,MACL,EAAE,UAAU,OAAO,OAAO,MAAM,aAAa,MAAO;AAAA,MACpD,EAAE,UAAU,OAAO,OAAO,OAAO,aAAa,KAAM;AAAA,MACpD,EAAE,UAAU,OAAO,OAAO,OAAO,aAAa,MAAO;AAAA,IACtD;AAAA,EACF;AAAA,EACD,SAAS;AAAA,IACP,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,iBAAiB;AAAA,IAClB;AAAA,IACD,OAAO;AAAA,MACL,SAAS,EAAE,SAAS,MAAM,WAAW,MAAM,aAAa,MAAM,gBAAgB,MAAM,UAAU,QAAS;AAAA,MACvG,KAAK,EAAE,SAAS,MAAM,WAAW,MAAM,aAAa,MAAM,gBAAgB,CAAC,gBAAgB,eAAe,aAAa,EAAG;AAAA,MAC1H,SAAS,EAAE,SAAS,MAAM,WAAW,MAAM,aAAa,KAAM;AAAA,MAC9D,MAAM,EAAE,SAAS,MAAM,WAAW,MAAM,aAAa,KAAM;AAAA,MAC3D,OAAO,EAAE,SAAS,MAAM,WAAW,MAAM,aAAa,KAAM;AAAA,IAC7D;AAAA,IACD,UAAU;AAAA,MACR,WAAW;AAAA,MACX,SAAS;AAAA,MACT,aAAa;AAAA,MACb,gBAAgB;AAAA,IACjB;AAAA,EACF;AAAA,EACD,UAAU;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,MACX,SAAS,EAAE,MAAM,YAAY,OAAO,GAAG,YAAY,EAAG;AAAA,MACtD,KAAK,EAAE,MAAM,YAAY,OAAO,GAAG,YAAY,EAAG;AAAA,MAClD,SAAS,EAAE,MAAM,SAAS,OAAO,GAAG,YAAY,GAAI;AAAA,MACpD,MAAM,EAAE,MAAM,YAAY,OAAO,IAAI,YAAY,EAAG;AAAA,MACpD,OAAO,EAAE,MAAM,YAAY,OAAO,GAAG,YAAY,EAAG;AAAA,IACrD;AAAA,EACF;AACH;AAGA,MAAM,yBAAyB,YAAY;AACzC,MAAI;AAEF,UAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,MACjC,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,SAAS;AAAA,IACf,CAAK;AAED,QAAI,SAAS,eAAe,OAAO,SAAS,MAAM;AAChD,aAAO,SAAS;AAAA,IACtB,OAAW;AACL,YAAM,IAAI,MAAM,QAAQ;AAAA,IACzB;AAAA,EACF,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,QAAA,oCAAa,uBAAuB,KAAK;AACzC,WAAO;AAAA,EACR;AACH;AAGY,MAAC,qBAAqB,OAAO,eAAe,UAAU;AAChE,QAAM,MAAM,KAAK,IAAK;AAGtB,MAAI,CAAC,gBAAgB,eAAgB,MAAM,iBAAkB,gBAAgB;AAC3E,WAAO;AAAA,EACR;AAED,MAAI;AAEF,UAAM,SAAS,MAAM,uBAAwB;AAG7C,kBAAc;AACd,qBAAiB;AAGjB,QAAI;AACFA,0BAAI,eAAe,oBAAoB,MAAM;AAC7CA,0BAAI,eAAe,yBAAyB,GAAG;AAAA,IAChD,SAAQ,GAAG;AACVA,oBAAAA,MAAa,MAAA,QAAA,oCAAA,gBAAgB,CAAC;AAAA,IAC/B;AAED,WAAO;AAAA,EACR,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,SAAA,oCAAc,aAAa,KAAK;AAGhC,QAAI;AACF,YAAM,cAAcA,cAAAA,MAAI,eAAe,kBAAkB;AACzD,YAAM,YAAYA,cAAAA,MAAI,eAAe,uBAAuB;AAE5D,UAAI,eAAe,aAAc,MAAM,YAAa,KAAK,KAAK,KAAK,KAAM;AACvE,sBAAc;AACd,eAAO;AAAA,MACR;AAAA,IACF,SAAQ,GAAG;AACVA,oBAAAA,MAAa,MAAA,QAAA,oCAAA,gBAAgB,CAAC;AAAA,IAC/B;AAGD,WAAO;AAAA,EACR;AACH;AA+CY,MAAC,mBAAmB,OAAO,WAAW;AAChD,QAAM,SAAS,MAAM,mBAAoB;AAGzC,MAAI,YAAY;AAChB,MAAI,WAAW,OAAO;AACpB,gBAAY;AAAA,EAChB,WAAa,WAAW,WAAW;AAC/B,gBAAY;AAAA,EAChB,WAAa,WAAW,QAAQ;AAC5B,gBAAY;AAAA,EAChB,WAAa,WAAW,SAAS;AAC7B,gBAAY;AAAA,EACb;AAED,SAAO,OAAO,QAAQ,SAAS,KAAK,CAAE;AACxC;AAoBY,MAAC,mBAAmB,OAAO,UAAU,aAAa,SAAS;AACrE,QAAM,SAAS,MAAM,mBAAoB;AAGzC,MAAI,CAAC,OAAO,QAAQ,OAAO,SAAS;AAClC,WAAO;AAAA,EACR;AAGD,MAAI,YAAY;AAChB,MAAI,SAAS,SAAS,KAAK,GAAG;AAC5B,gBAAY;AAAA,EACb,WAAU,SAAS,SAAS,SAAS,GAAG;AACvC,gBAAY;AAAA,EACb,WAAU,SAAS,SAAS,MAAM,GAAG;AACpC,gBAAY;AAAA,EACb,WAAU,SAAS,SAAS,OAAO,GAAG;AACrC,gBAAY;AAAA,EACb;AAED,QAAM,aAAa,OAAO,QAAQ,MAAM,SAAS;AACjD,MAAI,CAAC,cAAc,CAAC,WAAW,SAAS;AACtC,WAAO;AAAA,EACR;AAGD,MAAI,eAAe,MAAM;AACvB,WAAO,WAAW;AAAA,EACnB;AACD,MAAI,eAAe,QAAQ;AACzB,WAAO,WAAW;AAAA,EACnB;AAED,SAAO;AACT;AAGY,MAAC,gBAAgB,OAAO,QAAQ,SAAS;AACnD,QAAM,SAAS,MAAM,mBAAoB;AAEzC,MAAI,CAAC,OAAO,QAAQ,SAAS,SAAS;AACpC;AAAA,EACD;AAED,MAAI;AAEFA,kBAAAA,MAAI,QAAQ;AAAA,MACV,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA,WAAW,KAAK,IAAK;AAAA,QACrB,WAAW,UAAU,aAAa;AAAA,MACnC;AAAA,IACP,CAAK;AAAA,EACF,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,QAAA,oCAAa,aAAa,KAAK;AAAA,EAChC;AACH;;;;;"}