"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const utils_distributionService = require("../../../utils/distributionService.js");
const _sfc_main = {
  __name: "commission",
  setup(__props) {
    const tabs = [
      { name: "全部", value: "all" },
      { name: "待结算", value: "pending" },
      { name: "可提现", value: "settled" },
      { name: "已提现", value: "withdrawn" }
    ];
    const activeTab = common_vendor.ref("all");
    const commissionRecords = common_vendor.ref([]);
    const commissionSummary = common_vendor.reactive({
      total: 0,
      pending: 0,
      settled: 0,
      withdrawn: 0
    });
    const pagination = common_vendor.reactive({
      page: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    });
    const hasMoreData = common_vendor.ref(false);
    const loading = common_vendor.ref(false);
    common_vendor.onMounted(async () => {
      await getCommissionRecords();
    });
    const getCommissionRecords = async (loadMore2 = false) => {
      if (loading.value)
        return;
      try {
        loading.value = true;
        const page = loadMore2 ? pagination.page + 1 : 1;
        const result = await utils_distributionService.distributionService.getCommissionRecords({
          page,
          pageSize: pagination.pageSize,
          status: activeTab.value
        });
        if (result) {
          if (loadMore2) {
            commissionRecords.value = [...commissionRecords.value, ...result.list];
          } else {
            commissionRecords.value = result.list;
          }
          pagination.page = page;
          pagination.total = result.pagination.total;
          pagination.totalPages = result.pagination.totalPages;
          hasMoreData.value = pagination.page < pagination.totalPages;
          if (result.summary) {
            Object.assign(commissionSummary, result.summary);
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/distribution/pages/commission.vue:179", "获取佣金记录失败", error);
        common_vendor.index.showToast({
          title: "获取佣金记录失败",
          icon: "none"
        });
      } finally {
        loading.value = false;
      }
    };
    const switchTab = (tab) => {
      if (activeTab.value === tab)
        return;
      activeTab.value = tab;
      getCommissionRecords();
    };
    const loadMore = () => {
      if (hasMoreData.value && !loading.value) {
        getCommissionRecords(true);
      }
    };
    const formatCommission = (amount) => {
      return utils_distributionService.distributionService.formatCommission(amount);
    };
    const formatTime = (time) => {
      if (!time)
        return "";
      const date = new Date(time);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")} ${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
    };
    const getStatusText = (status) => {
      switch (status) {
        case "pending":
          return "待结算";
        case "settled":
          return "可提现";
        case "withdrawn":
          return "已提现";
        default:
          return "未知状态";
      }
    };
    const getStatusPrefix = (status) => {
      return status === "pending" ? "+预计 " : "+";
    };
    const getStatusClass = (status) => {
      return {
        "pending": "status-pending",
        "settled": "status-settled",
        "withdrawn": "status-withdrawn"
      }[status] || "";
    };
    const getLevelText = (level) => {
      switch (level) {
        case 1:
          return "一级";
        case 2:
          return "二级";
        case 3:
          return "三级";
        default:
          return "";
      }
    };
    const navigateTo = (url) => {
      common_vendor.index.navigateTo({ url });
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const showHelp = () => {
      common_vendor.index.showModal({
        title: "佣金明细帮助",
        content: "佣金明细页面显示您的所有佣金记录，包括待结算、可提现和已提现的佣金。待结算佣金需要等待订单完成后才能提现。",
        showCancel: false
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(goBack),
        b: common_vendor.o(showHelp),
        c: common_vendor.t(formatCommission(commissionSummary.total)),
        d: common_vendor.t(formatCommission(commissionSummary.pending)),
        e: common_vendor.t(formatCommission(commissionSummary.settled)),
        f: common_vendor.t(formatCommission(commissionSummary.withdrawn)),
        g: common_vendor.o(($event) => navigateTo("/subPackages/distribution/pages/withdraw")),
        h: common_vendor.f(tabs, (tab, index, i0) => {
          return {
            a: common_vendor.t(tab.name),
            b: index,
            c: activeTab.value === tab.value ? 1 : "",
            d: common_vendor.o(($event) => switchTab(tab.value), index)
          };
        }),
        i: commissionRecords.value.length > 0
      }, commissionRecords.value.length > 0 ? {
        j: common_vendor.f(commissionRecords.value, (record, index, i0) => {
          return {
            a: common_vendor.t(record.productName),
            b: common_vendor.t(getStatusPrefix(record.status)),
            c: common_vendor.t(formatCommission(record.amount)),
            d: common_vendor.n(getStatusClass(record.status)),
            e: common_vendor.t(record.orderId),
            f: common_vendor.t(getLevelText(record.level)),
            g: common_vendor.t(formatTime(record.createdAt)),
            h: common_vendor.t(getStatusText(record.status)),
            i: index
          };
        })
      } : {
        k: common_assets._imports_0$18
      }, {
        l: hasMoreData.value && commissionRecords.value.length > 0
      }, hasMoreData.value && commissionRecords.value.length > 0 ? common_vendor.e({
        m: loading.value
      }, loading.value ? {} : {
        n: common_vendor.o(loadMore)
      }) : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/distribution/pages/commission.js.map
