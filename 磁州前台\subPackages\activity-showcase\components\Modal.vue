<template>
  <view class="modal-overlay" v-if="visible" :class="{ 'fade-in': visible }" @click="handleOverlayClick">
    <view 
      class="modal-container" 
      :class="[
        `position-${position}`, 
        `size-${size}`,
        { 'slide-in': visible, 'full-screen': fullScreen }
      ]"
      @click.stop
    >
      <!-- 模态框头部 -->
      <view class="modal-header" v-if="showHeader">
        <slot name="header">
          <view class="header-content">
            <text class="modal-title" v-if="title">{{ title }}</text>
            <view class="header-actions">
              <slot name="header-actions"></slot>
              <view class="close-btn" v-if="showCloseBtn" @click="handleClose">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </view>
            </view>
          </view>
        </slot>
      </view>
      
      <!-- 模态框主体 -->
      <view class="modal-body" :class="{ 'scrollable': scrollable }">
        <slot></slot>
      </view>
      
      <!-- 模态框底部 -->
      <view class="modal-footer" v-if="showFooter">
        <slot name="footer">
          <view class="footer-actions">
            <view class="action-btn cancel" v-if="showCancelBtn" @click="handleCancel">
              <text class="btn-text">{{ cancelText }}</text>
            </view>
            <view class="action-btn confirm" v-if="showConfirmBtn" @click="handleConfirm">
              <text class="btn-text">{{ confirmText }}</text>
            </view>
          </view>
        </slot>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'

// Props定义
const props = defineProps({
  // 是否显示
  visible: {
    type: Boolean,
    default: false
  },
  // 标题
  title: {
    type: String,
    default: ''
  },
  // 位置
  position: {
    type: String,
    default: 'center', // center, top, bottom
    validator: (value) => ['center', 'top', 'bottom'].includes(value)
  },
  // 尺寸
  size: {
    type: String,
    default: 'medium', // small, medium, large, auto
    validator: (value) => ['small', 'medium', 'large', 'auto'].includes(value)
  },
  // 是否全屏
  fullScreen: {
    type: Boolean,
    default: false
  },
  // 是否可滚动
  scrollable: {
    type: Boolean,
    default: true
  },
  // 是否显示头部
  showHeader: {
    type: Boolean,
    default: true
  },
  // 是否显示底部
  showFooter: {
    type: Boolean,
    default: false
  },
  // 是否显示关闭按钮
  showCloseBtn: {
    type: Boolean,
    default: true
  },
  // 是否显示取消按钮
  showCancelBtn: {
    type: Boolean,
    default: false
  },
  // 是否显示确认按钮
  showConfirmBtn: {
    type: Boolean,
    default: false
  },
  // 取消按钮文本
  cancelText: {
    type: String,
    default: '取消'
  },
  // 确认按钮文本
  confirmText: {
    type: String,
    default: '确认'
  },
  // 点击遮罩是否关闭
  closeOnOverlay: {
    type: Boolean,
    default: true
  },
  // 是否阻止滚动穿透
  preventScroll: {
    type: Boolean,
    default: true
  },
  // 层级
  zIndex: {
    type: Number,
    default: 1000
  }
})

// Emits定义
const emit = defineEmits([
  'update:visible',
  'close',
  'cancel',
  'confirm',
  'open',
  'opened',
  'closed'
])

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    emit('open')
    nextTick(() => {
      emit('opened')
    })
    
    // 阻止滚动穿透
    if (props.preventScroll) {
      document.body.style.overflow = 'hidden'
    }
  } else {
    emit('closed')
    
    // 恢复滚动
    if (props.preventScroll) {
      document.body.style.overflow = ''
    }
  }
})

// 方法
function handleOverlayClick() {
  if (props.closeOnOverlay) {
    handleClose()
  }
}

function handleClose() {
  emit('update:visible', false)
  emit('close')
}

function handleCancel() {
  emit('cancel')
  handleClose()
}

function handleConfirm() {
  emit('confirm')
}
</script>

<style scoped>
/* 模态框组件样式开始 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: v-bind(zIndex);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal-overlay.fade-in {
  opacity: 1;
}

.modal-container {
  background: white;
  border-radius: 16px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  transform: scale(0.9) translateY(20px);
  transition: all 0.3s ease;
}

.modal-container.slide-in {
  transform: scale(1) translateY(0);
}

/* 位置样式 */
.modal-container.position-top {
  align-self: flex-start;
  margin-top: 10vh;
  transform: translateY(-20px);
}

.modal-container.position-top.slide-in {
  transform: translateY(0);
}

.modal-container.position-bottom {
  align-self: flex-end;
  margin-bottom: 0;
  border-radius: 16px 16px 0 0;
  transform: translateY(100%);
}

.modal-container.position-bottom.slide-in {
  transform: translateY(0);
}

/* 尺寸样式 */
.modal-container.size-small {
  width: 300px;
  min-height: 200px;
}

.modal-container.size-medium {
  width: 400px;
  min-height: 300px;
}

.modal-container.size-large {
  width: 500px;
  min-height: 400px;
}

.modal-container.size-auto {
  width: auto;
  min-width: 280px;
  min-height: auto;
}

.modal-container.full-screen {
  width: 100vw;
  height: 100vh;
  max-width: 100vw;
  max-height: 100vh;
  border-radius: 0;
}

/* 头部样式 */
.modal-header {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.close-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: #f5f5f5;
  transition: all 0.3s ease;
}

.close-btn:active {
  background: #e9ecef;
  transform: scale(0.95);
}

.close-btn svg {
  color: #666;
}

/* 主体样式 */
.modal-body {
  padding: 20px;
  flex: 1;
  overflow: hidden;
}

.modal-body.scrollable {
  overflow-y: auto;
  max-height: 60vh;
}

.full-screen .modal-body.scrollable {
  max-height: calc(100vh - 140px);
}

/* 底部样式 */
.modal-footer {
  padding: 20px;
  border-top: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.footer-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.action-btn {
  flex: 1;
  padding: 12px 24px;
  border-radius: 8px;
  text-align: center;
  transition: all 0.3s ease;
  min-width: 80px;
}

.action-btn:active {
  transform: scale(0.98);
}

.action-btn.cancel {
  background: #f5f5f5;
  border: 1px solid #e0e0e0;
}

.action-btn.cancel:active {
  background: #e9ecef;
}

.action-btn.confirm {
  background: #2196F3;
}

.action-btn.confirm:active {
  background: #1976D2;
}

.btn-text {
  font-size: 16px;
  font-weight: 500;
}

.action-btn.cancel .btn-text {
  color: #666;
}

.action-btn.confirm .btn-text {
  color: white;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .modal-container {
    max-width: 95vw;
    max-height: 95vh;
  }
  
  .modal-container.size-small,
  .modal-container.size-medium,
  .modal-container.size-large {
    width: 90vw;
  }
  
  .modal-container.position-bottom {
    width: 100vw;
    max-width: 100vw;
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 16px;
  }
  
  .modal-title {
    font-size: 16px;
  }
  
  .footer-actions {
    flex-direction: column;
  }
  
  .action-btn {
    width: 100%;
  }
}

/* 滚动条样式 */
.modal-body::-webkit-scrollbar {
  width: 4px;
}

.modal-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.modal-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画关键帧 */
@keyframes modalFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalSlideIn {
  from {
    transform: scale(0.9) translateY(20px);
  }
  to {
    transform: scale(1) translateY(0);
  }
}

@keyframes modalSlideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes modalSlideDown {
  from {
    transform: translateY(-20px);
  }
  to {
    transform: translateY(0);
  }
}
/* 模态框组件样式结束 */
</style>
