
/* 满减活动卡片特有样式 */
.discount-card.data-v-ef845547 {
  /* 继承基础卡片样式 */
}

/* 满减特有信息区域 */
.discount-special.data-v-ef845547 {
  padding: 20rpx;
  background-color: rgba(90, 200, 250, 0.05);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
}

/* 满减规则区域 */
.discount-rules.data-v-ef845547 {
  margin-bottom: 20rpx;
}
.rules-header.data-v-ef845547 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.rules-title.data-v-ef845547 {
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
}
.merchant-count.data-v-ef845547 {
  font-size: 22rpx;
  color: #5ac8fa;
  background-color: rgba(90, 200, 250, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
}
.rules-list.data-v-ef845547 {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}
.rule-item.data-v-ef845547 {
  position: relative;
  background-color: rgba(90, 200, 250, 0.08);
  border-radius: 16rpx;
  padding: 12rpx 16rpx;
  border: 1rpx solid rgba(90, 200, 250, 0.2);
  transition: all 0.3s ease;
}
.best-rule.data-v-ef845547 {
  background-color: rgba(90, 200, 250, 0.15);
  border: 1rpx solid rgba(90, 200, 250, 0.4);
  transform: scale(1.02);
}
.rule-badge.data-v-ef845547 {
  position: absolute;
  top: -10rpx;
  right: 16rpx;
  background-color: #5ac8fa;
  color: #ffffff;
  font-size: 20rpx;
  font-weight: 600;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 8rpx rgba(90, 200, 250, 0.3);
}
.rule-content.data-v-ef845547 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.rule-text.data-v-ef845547 {
  font-size: 26rpx;
  color: #333333;
  font-weight: 600;
}
.rule-save.data-v-ef845547 {
  font-size: 22rpx;
  color: #5ac8fa;
}

/* 活动时间 */
.discount-period.data-v-ef845547 {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.period-icon.data-v-ef845547 {
  margin-right: 8rpx;
  color: #5ac8fa;
}
.period-text.data-v-ef845547 {
  font-size: 24rpx;
  color: #333333;
}

/* 使用范围 */
.discount-scope.data-v-ef845547 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.scope-icon.data-v-ef845547 {
  margin-right: 8rpx;
  color: #5ac8fa;
}
.scope-text.data-v-ef845547 {
  font-size: 24rpx;
  color: #333333;
}

/* 参与商家 */
.discount-merchants.data-v-ef845547 {
  margin-top: 16rpx;
  border-top: 1rpx dashed rgba(90, 200, 250, 0.3);
  padding-top: 16rpx;
}
.merchants-header.data-v-ef845547 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}
.merchants-title.data-v-ef845547 {
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
}
.merchants-count.data-v-ef845547 {
  font-size: 22rpx;
  color: #5ac8fa;
}
.merchants-list.data-v-ef845547 {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}
.merchant-item.data-v-ef845547 {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: calc((100% - 32rpx) / 3);
}
.merchant-logo.data-v-ef845547 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  border: 1rpx solid rgba(90, 200, 250, 0.2);
  margin-bottom: 8rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
}
.merchant-name.data-v-ef845547 {
  font-size: 22rpx;
  color: #333333;
  width: 100%;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.more-merchants.data-v-ef845547 {
  display: flex;
  justify-content: center;
  align-items: center;
  width: calc((100% - 32rpx) / 3);
  height: 110rpx;
}
.more-merchants text.data-v-ef845547 {
  font-size: 24rpx;
  color: #5ac8fa;
}
