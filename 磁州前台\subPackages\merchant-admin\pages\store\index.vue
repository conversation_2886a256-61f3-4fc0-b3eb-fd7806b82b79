<template>
  <view class="store-entry-container">
    <!-- 顶部导航栏 -->
    <view class="header-area">
      <!-- 顶部安全区域 -->
      <view class="safe-area-top"></view>
      
      <!-- 自定义导航栏 -->
      <view class="custom-navbar">
        <view class="navbar-left" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="back-icon" style="filter: brightness(0) invert(1);"></image>
        </view>
        <view class="navbar-title">店铺管理</view>
        <view class="navbar-right">
          <image src="/static/images/tabbar/更多.png" class="more-icon"></image>
        </view>
      </view>
    </view>
    
    <!-- 背景装饰 -->
    <view class="bg-decoration bg-circle-1"></view>
    <view class="bg-decoration bg-circle-2"></view>
    <view class="bg-decoration bg-circle-3"></view>
    
    <!-- 店铺信息卡片 -->
    <view class="store-info-card">
      <view class="store-header">
        <view class="store-avatar-wrapper">
          <image :src="storeInfo.avatar || '/static/images/default-store.png'" mode="aspectFill" class="store-avatar"></image>
          <view class="edit-avatar-btn" @click="editAvatar">
            <image src="/static/images/tabbar/编辑.png" mode="aspectFit" class="edit-icon"></image>
          </view>
        </view>
        <view class="store-info">
          <view class="store-name-wrapper">
            <text class="store-name">{{storeInfo.name}}</text>
            <view class="store-badge" v-if="storeInfo.isVerified">已认证</view>
          </view>
          <text class="store-desc">{{storeInfo.description || '暂无店铺介绍'}}</text>
          <view class="store-stats">
            <view class="stat-item">
              <text class="stat-value">{{storeInfo.rating}}</text>
              <text class="stat-label">评分</text>
            </view>
            <view class="stat-divider"></view>
            <view class="stat-item">
              <text class="stat-value">{{storeInfo.orderCount}}</text>
              <text class="stat-label">订单</text>
            </view>
            <view class="stat-divider"></view>
            <view class="stat-item">
              <text class="stat-value">{{storeInfo.visitorCount}}</text>
              <text class="stat-label">访客</text>
            </view>
          </view>
        </view>
        <view class="preview-btn" @click="previewStore">
          <text class="preview-text">预览</text>
        </view>
      </view>
      <view class="store-status-bar">
        <view class="status-indicator">
          <view class="status-dot" :class="{'active': storeInfo.status === 'open'}"></view>
          <text class="status-text">{{storeInfo.status === 'open' ? '营业中' : '休息中'}}</text>
        </view>
        <view class="operation-hours">
          <text>营业时间: {{storeInfo.operationHours}}</text>
        </view>
      </view>
    </view>
    
    <!-- 功能区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 基础信息配置 -->
      <view class="function-section">
        <view class="section-header">
          <text class="section-title">基础信息配置</text>
          <text class="section-subtitle">设置店铺基本信息以吸引更多顾客</text>
        </view>
        
        <view class="function-grid">
          <view class="function-item" @click="navigateTo('basicInfo')">
            <view class="icon-wrapper logo-bg">
              <image src="/static/images/tabbar/店铺信息.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">店铺信息</text>
            <text class="function-desc">LOGO、封面图、简介</text>
          </view>
          
          <view class="function-item" @click="navigateTo('operationInfo')">
            <view class="icon-wrapper operation-bg">
              <image src="/static/images/tabbar/营业信息.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">经营信息</text>
            <text class="function-desc">营业时间、配送范围</text>
          </view>
          
          <view class="function-item" @click="navigateTo('locationInfo')">
            <view class="icon-wrapper location-bg">
              <image src="/static/images/tabbar/位置信息.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">位置管理</text>
            <text class="function-desc">门店地址、导航设置</text>
          </view>
        </view>
      </view>
      
      <!-- 商品服务管理 -->
      <view class="function-section">
        <view class="section-header">
          <text class="section-title">商品服务管理</text>
          <text class="section-subtitle">管理您店铺提供的商品与服务</text>
        </view>
        
        <view class="function-grid">
          <view class="function-item" @click="navigateTo('categoryManage')">
            <view class="icon-wrapper category-bg">
              <image src="/static/images/tabbar/分类管理.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">分类管理</text>
            <text class="function-desc">自定义商品分类体系</text>
          </view>
          
          <view class="function-item" @click="navigateTo('productManage')">
            <view class="icon-wrapper product-bg">
              <image src="/static/images/tabbar/商品管理.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">商品管理</text>
            <text class="function-desc">批量上传、SKU管理</text>
          </view>
          
          <view class="function-item" @click="navigateTo('serviceManage')">
            <view class="icon-wrapper service-bg">
              <image src="/static/images/tabbar/服务管理.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">服务项目</text>
            <text class="function-desc">服务定价、服务说明</text>
          </view>
        </view>
      </view>
      
      <!-- 门店形象管理 -->
      <view class="function-section">
        <view class="section-header">
          <text class="section-title">门店形象管理</text>
          <text class="section-subtitle">打造专业店铺形象，提升用户信任度</text>
        </view>
        
        <view class="function-grid">
          <view class="function-item" @click="navigateTo('storeAlbum')">
            <view class="icon-wrapper album-bg">
              <image src="/static/images/tabbar/店铺相册.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">店铺相册</text>
            <text class="function-desc">环境展示、产品展示</text>
          </view>
          
          <view class="function-item" @click="navigateTo('videoManage')">
            <view class="icon-wrapper video-bg">
              <image src="/static/images/tabbar/视频展示.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">视频展示</text>
            <text class="function-desc">店铺宣传视频、服务展示</text>
          </view>
          
          <view class="function-item" @click="navigateTo('storeCulture')">
            <view class="icon-wrapper culture-bg">
              <image src="/static/images/tabbar/商家故事.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">商家故事</text>
            <text class="function-desc">品牌文化、特色介绍</text>
          </view>
        </view>
      </view>
      
      <!-- 认证与资质管理 -->
      <view class="function-section">
        <view class="section-header">
          <text class="section-title">认证与资质管理</text>
          <text class="section-subtitle">提升店铺可信度，获得更多顾客信任</text>
        </view>
        
        <view class="function-grid">
          <view class="function-item" @click="navigateTo('storeVerify')">
            <view class="icon-wrapper verify-bg">
              <image src="/static/images/tabbar/商家认证.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">商家认证</text>
            <text class="function-desc">企业/个体工商户认证</text>
          </view>
          
          <view class="function-item" @click="navigateTo('qualificationManage')">
            <view class="icon-wrapper qualification-bg">
              <image src="/static/images/tabbar/资质管理.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">资质管理</text>
            <text class="function-desc">经营许可证、行业资质</text>
          </view>
          
          <view class="function-item" @click="navigateTo('qualificationRemind')">
            <view class="icon-wrapper remind-bg">
              <image src="/static/images/tabbar/提醒管理.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">到期提醒</text>
            <text class="function-desc">资质到期提醒与续期</text>
          </view>
        </view>
      </view>
      
      <!-- 我的活动管理 -->
      <view class="function-section">
        <view class="section-header">
          <text class="section-title">我的活动管理</text>
          <text class="section-subtitle">管理您发布的营销活动</text>
        </view>
        
        <view class="function-grid">
          <view class="function-item" @click="navigateTo('activityList')">
            <view class="icon-wrapper activity-bg">
              <image src="/static/images/tabbar/活动列表.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">活动列表</text>
            <text class="function-desc">已发布活动状态管理</text>
          </view>
          
          <view class="function-item" @click="navigateTo('activityOperation')">
            <view class="icon-wrapper operation-bg">
              <image src="/static/images/tabbar/活动操作.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">活动操作</text>
            <text class="function-desc">续费、置顶、刷新</text>
          </view>
          
          <view class="function-item" @click="navigateTo('activityData')">
            <view class="icon-wrapper data-bg">
              <image src="/static/images/tabbar/活动数据.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">活动数据</text>
            <text class="function-desc">浏览量、转化率分析</text>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 添加底部导航栏，与商家中心页面保持一致 -->
    <view class="tab-bar">
      <view 
        class="tab-item" 
        v-for="(tab, index) in tabItems" 
        :key="index"
        :class="{ active: currentTab === tab.id }"
        @tap="switchTab(tab.id)">
        <view class="active-indicator" v-if="currentTab === tab.id"></view>
        <view class="tab-icon" :class="tab.icon"></view>
        <text class="tab-text">{{ tab.text }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      storeInfo: {
        avatar: '/static/images/store-avatar.png',
        name: '磁州小吃美食店',
        description: '专注本地特色小吃，用心制作每一道美食',
        rating: 4.8,
        orderCount: 2580,
        viewCount: 12560,
        followerCount: 680,
        labels: ['特色小吃', '本地美食', '堂食', '外卖', '预订'],
        status: 'open',
        operationHours: '09:00-21:00',
        isVerified: true
      },
      categories: [
        { id: 1, name: '招牌菜', count: 12 },
        { id: 2, name: '凉菜', count: 8 },
        { id: 3, name: '热菜', count: 15 },
        { id: 4, name: '主食', count: 6 },
        { id: 5, name: '汤类', count: 4 },
        { id: 6, name: '饮品', count: 7 }
      ],
      menuItems: [
        { id: 101, name: '铁板鱿鱼', price: 38, sales: 256, image: '/static/images/dish1.png', category: 1 },
        { id: 102, name: '爆炒腰花', price: 42, sales: 198, image: '/static/images/dish2.png', category: 1 },
        { id: 103, name: '剁椒鱼头', price: 58, sales: 172, image: '/static/images/dish3.png', category: 1 },
        { id: 104, name: '凉拌海蜇', price: 22, sales: 118, image: '/static/images/dish4.png', category: 2 },
        { id: 105, name: '手撕包菜', price: 18, sales: 167, image: '/static/images/dish5.png', category: 3 }
      ],
      currentTab: 1, // 当前选中的标签页（店铺管理）
      tabItems: [
        {
          id: 0,
          icon: 'dashboard',
          text: '商家中心',
          url: '/subPackages/merchant-admin-home/pages/merchant-home/index'
        },
        {
          id: 1,
          icon: 'store',
          text: '店铺管理',
          url: '/subPackages/merchant-admin/pages/store/index'
        },
        {
          id: 2,
          icon: 'marketing',
          text: '营销中心',
          url: '/subPackages/merchant-admin-marketing/pages/marketing/index'
        },
        {
          id: 3,
          icon: 'orders',
          text: '订单管理',
          url: '/subPackages/merchant-admin-order/pages/order/index'
        },
        {
          id: 'more',
          icon: 'more',
          text: '更多',
          url: ''
        }
      ]
    }
  },
  onLoad() {
    // 页面加载完成后的处理
    this.setStatusBarHeight();
  },
  methods: {
    // 设置状态栏高度
    setStatusBarHeight() {
      // 获取系统信息设置状态栏高度
      uni.getSystemInfo({
        success: (res) => {
          this.statusBarHeight = res.statusBarHeight;
          // 将状态栏高度设置为CSS变量
          if (typeof document !== 'undefined') {
            document.documentElement.style.setProperty('--status-bar-height', `${this.statusBarHeight}px`);
          }
        }
      });
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 编辑头像
    editAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          // 这里可以添加上传图片的逻辑
          this.storeInfo.avatar = res.tempFilePaths[0];
          uni.showToast({
            title: '头像已更新',
            icon: 'success'
          });
        }
      });
    },
    
    // 预览店铺
    previewStore() {
      uni.showToast({
        title: '店铺预览功能开发中',
        icon: 'none'
      });
    },
    
    // 导航到具体功能页面
    navigateTo(page) {
      // 根据不同的页面参数跳转到对应的页面
      const pageMap = {
        'basicInfo': '/subPackages/merchant-admin/pages/store/basic-info',
        'operationInfo': '/subPackages/merchant-admin/pages/store/operation-info',
        'locationInfo': '/subPackages/merchant-admin/pages/store/location-info',
        'categoryManage': '/subPackages/merchant-admin/pages/store/category',
        'productManage': '/subPackages/merchant-admin/pages/store/product',
        'serviceManage': '/subPackages/merchant-admin/pages/store/service',
        'storeAlbum': '/subPackages/merchant-admin/pages/store/album',
        'videoManage': '/subPackages/merchant-admin/pages/store/video',
        'storeCulture': '/subPackages/merchant-admin/pages/store/culture',
        'storeVerify': '/subPackages/merchant-admin/pages/store/verify',
        'qualificationManage': '/subPackages/merchant-admin/pages/store/qualification',
        'qualificationRemind': '/subPackages/merchant-admin/pages/store/remind',
        'activityList': '/subPackages/merchant-admin/pages/store/activity-list',
        'activityOperation': '/subPackages/merchant-admin/pages/store/activity-operation',
        'activityData': '/subPackages/merchant-admin/pages/store/activity-data'
      };
      
      const url = pageMap[page];
      if (url) {
        uni.navigateTo({ url });
      } else {
        uni.showToast({
          title: '功能开发中',
          icon: 'none'
        });
      }
    },
    
    // 切换底部导航栏
    switchTab(tabId) {
      // 处理"更多"选项
      if (tabId === 'more') {
        this.showMoreOptions();
        return;
      }
      
      if (tabId === this.currentTab) return;
      
      this.currentTab = tabId;
      
      // 特殊处理店铺管理标签
      if (tabId === 1) {
        // 当前已经在店铺管理页面，不需要跳转
        return;
      }
      
      // 使用redirectTo而不是navigateTo，避免堆栈过多
      uni.redirectTo({
        url: this.tabItems[tabId].url,
        fail: (err) => {
          console.error('redirectTo失败:', err);
          // 如果redirectTo失败，尝试使用switchTab
          uni.switchTab({
            url: this.tabItems[tabId].url,
            fail: (switchErr) => {
              console.error('switchTab也失败:', switchErr);
              uni.showToast({
                title: '页面跳转失败，请稍后再试',
                icon: 'none'
              });
            }
          });
        }
      });
    },
    
    // 显示更多选项弹出菜单
    showMoreOptions() {
      // 准备更多菜单中的选项
      const moreOptions = ['客户运营', '分析洞察', '系统设置'];
      
      uni.showActionSheet({
        itemList: moreOptions,
        success: (res) => {
          // 根据选择的选项进行跳转
          const routes = [
            '/subPackages/merchant-admin-customer/pages/customer/index',
            '/subPackages/merchant-admin/pages/settings/index'
          ];
          uni.navigateTo({
            url: routes[res.tapIndex]
          });
        }
      });
    }
  }
}
</script>

<style lang="scss">
.store-entry-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(110rpx + var(--status-bar-height, 20px));
}

/* 顶部导航栏样式 */
.header-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
  color: #fff;
}

.safe-area-top {
  height: var(--status-bar-height, 20px);
  width: 100%;
}

.custom-navbar {
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
}

.navbar-left, .navbar-right {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon, .more-icon {
  width: 48rpx;
  height: 48rpx;
}

/* 背景装饰样式 */
.bg-decoration {
  position: absolute;
  z-index: -1;
  border-radius: 50%;
  opacity: 0.07;
  background-color: #0A84FF;
}

.bg-circle-1 {
  top: -100rpx;
  right: -150rpx;
  width: 400rpx;
  height: 400rpx;
}

.bg-circle-2 {
  top: 10%;
  left: -200rpx;
  width: 500rpx;
  height: 500rpx;
}

.bg-circle-3 {
  bottom: 5%;
  right: -100rpx;
  width: 300rpx;
  height: 300rpx;
}

/* 店铺信息卡片样式 */
.store-info-card {
  margin: 30rpx;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.store-header {
  padding: 30rpx;
  display: flex;
  align-items: center;
  position: relative;
}

.store-avatar-wrapper {
  position: relative;
}

.store-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
  background-color: #f0f0f0;
  border: 4rpx solid #FFFFFF;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.edit-avatar-btn {
  position: absolute;
  right: -10rpx;
  bottom: -10rpx;
  width: 48rpx;
  height: 48rpx;
  background-color: #0A84FF;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-icon {
  width: 24rpx;
  height: 24rpx;
  filter: brightness(0) invert(1);
}

.store-info {
  flex: 1;
  padding: 0 20rpx;
}

.store-name-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.store-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 10rpx;
}

.store-badge {
  padding: 4rpx 12rpx;
  background-color: #34C759;
  color: #FFFFFF;
  font-size: 20rpx;
  border-radius: 10rpx;
}

.store-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
  height: 34rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.store-stats {
  display: flex;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.stat-label {
  font-size: 20rpx;
  color: #999;
}

.stat-divider {
  width: 2rpx;
  height: 30rpx;
  background-color: #E5E5EA;
  margin: 0 30rpx;
}

.preview-btn {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  background-color: #0A84FF;
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 8rpx 24rpx;
  border-radius: 30rpx;
}

.store-status-bar {
  padding: 20rpx 30rpx;
  border-top: 2rpx solid #F2F2F7;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 24rpx;
  color: #666;
}

.status-indicator {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #8E8E93;
  margin-right: 10rpx;
}

.status-dot.active {
  background-color: #34C759;
}

/* 内容滚动区域 */
.content-scroll {
  padding: 20rpx 30rpx;
  padding-bottom: calc(60px + env(safe-area-inset-bottom, 0px) + 120rpx);
  height: calc(100vh - 280rpx - var(--status-bar-height, 20px));
}

/* 功能区块样式 */
.function-section {
  margin-bottom: 40rpx;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.section-subtitle {
  font-size: 24rpx;
  color: #999;
  margin-top: 6rpx;
}

.function-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.function-item {
  width: calc(33.33% - 20rpx);
  margin: 10rpx;
  border-radius: 16rpx;
  overflow: hidden;
  background-color: #FFFFFF;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.03);
  padding: 20rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.function-item:active {
  transform: scale(0.95);
  opacity: 0.9;
}

.icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.icon-wrapper::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  border-radius: 50%;
  z-index: 1;
}

.function-icon {
  width: 40rpx;
  height: 40rpx;
  filter: brightness(0) invert(1);
  z-index: 2;
}

.function-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 6rpx;
}

.function-desc {
  font-size: 20rpx;
  color: #999;
  text-align: center;
  padding: 0 10rpx;
  height: 28rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

/* 图标背景颜色 */
.logo-bg {
  background: linear-gradient(135deg, #1976D2, #64B5F6);
}

.operation-bg {
  background: linear-gradient(135deg, #FF9966, #FF5E62);
}

.location-bg {
  background: linear-gradient(135deg, #38ADAE, #30CE9B);
}

.category-bg {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
}

.product-bg {
  background: linear-gradient(135deg, #36D1DC, #5B86E5);
}

.service-bg {
  background: linear-gradient(135deg, #FF6B6B, #F04172);
}

.album-bg {
  background: linear-gradient(135deg, #FDEB71, #F8D800);
}

.video-bg {
  background: linear-gradient(135deg, #8E2DE2, #4A00E0);
}

.culture-bg {
  background: linear-gradient(135deg, #795548, #A1887F);
}

.verify-bg {
  background: linear-gradient(135deg, #38ADAE, #30CE9B);
}

.qualification-bg {
  background: linear-gradient(135deg, #FF5858, #FF0000);
}

.remind-bg {
  background: linear-gradient(135deg, #4481EB, #04BEFE);
}

.activity-bg {
  background: linear-gradient(135deg, #FF6B6B, #F04172);
}

.data-bg {
  background: linear-gradient(135deg, #36D1DC, #5B86E5);
}

/* 适配暗黑模式 */
@media (prefers-color-scheme: dark) {
  .store-entry-container {
    background-color: #1C1C1E;
  }
  
  .store-info-card,
  .function-section,
  .function-item {
    background-color: #2C2C2E;
  }
  
  .store-name,
  .section-title,
  .function-name,
  .stat-value {
    color: #FFFFFF;
  }
  
  .store-desc,
  .section-subtitle,
  .function-desc,
  .stat-label,
  .operation-hours,
  .status-text {
    color: #8E8E93;
  }
  
  .store-status-bar {
    border-top-color: #3A3A3C;
  }
}

/* 底部导航栏样式 */
.tab-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  padding: 0 10px;
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #FFFFFF;
  border-top: 1px solid var(--border-light, #F0F0F0);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  flex-shrink: 0;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
  transition: all 0.2s ease;
  padding: 8px 0;
  margin: 0 8px;
}

.tab-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 3px;
  transition: all 0.25s cubic-bezier(0.3, 0.7, 0.4, 1.5);
  background-position: center;
  background-repeat: no-repeat;
  background-size: 22px;
  opacity: 0.7;
}

.tab-text {
  font-size: 10px;
  color: var(--text-tertiary, #999999);
  transition: color 0.2s ease, transform 0.25s ease;
  transform: scale(0.9);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  padding: 0 2px;
}

.active-indicator {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 16px;
  height: 3px;
  border-radius: 0 0 4px 4px;
  background: linear-gradient(90deg, var(--brand-primary, #0A84FF), var(--accent-purple, #5E5CE6));
}

.tab-item.active .tab-icon {
  transform: translateY(-2px);
  opacity: 1;
}

.tab-item.active .tab-text {
  color: var(--brand-primary, #0A84FF);
  font-weight: 500;
  transform: scale(1);
}

/* 导航图标样式 */
.tab-icon.dashboard {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z'/%3E%3C/svg%3E");
}

.tab-icon.store {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M20 4H4v2h16V4zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6h1zm-9 4H6v-4h6v4z'/%3E%3C/svg%3E");
}

.tab-icon.marketing {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z'/%3E%3C/svg%3E");
}

.tab-icon.orders {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
}

.tab-icon.customers {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z'/%3E%3C/svg%3E");
}

.tab-icon.analytics {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-5h2v5zm4 0h-2v-3h2v3zm0-5h-2V7h2v2zm4 5h-2V7h2v10z'/%3E%3C/svg%3E");
}

.tab-icon.settings {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z'/%3E%3C/svg%3E");
}

.tab-icon.more {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z'/%3E%3C/svg%3E");
}

@media screen and (max-width: 375px) {
  /* 在较小屏幕上优化导航栏 */
  .tab-text {
    font-size: 9px;
  }
  
  .tab-icon {
    margin-bottom: 2px;
    background-size: 20px;
  }
}

/* 在较宽屏幕上优化导航栏，确保元素不会挤压 */
@media screen and (min-width: 400px) {
  .tab-bar {
    padding: 0 10px;
  }
  
  .tab-item {
    margin: 0 5px;
  }
}

/* 添加底部安全区域 */
.safe-area-bottom {
  height: calc(60px + env(safe-area-inset-bottom));
}
</style> 