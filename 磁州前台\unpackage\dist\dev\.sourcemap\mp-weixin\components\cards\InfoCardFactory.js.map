{"version": 3, "file": "InfoCardFactory.js", "sources": ["components/cards/InfoCardFactory.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7AvY29tcG9uZW50cy9jYXJkcy9JbmZvQ2FyZEZhY3RvcnkudnVl"], "sourcesContent": ["<template>\r\n  <!-- 使用条件渲染替代动态组件 -->\r\n  <JobCard \r\n    v-if=\"cardType === 'JobCard'\" \r\n    :item=\"item\" \r\n  />\r\n  <HouseRentCard \r\n    v-else-if=\"cardType === 'HouseRentCard'\" \r\n    :item=\"item\" \r\n  />\r\n  <SecondHandCard \r\n    v-else-if=\"cardType === 'SecondHandCard'\" \r\n    :item=\"item\" \r\n  />\r\n  <BaseInfoCard \r\n    v-else \r\n    :item=\"item\" \r\n  />\r\n</template>\r\n\r\n<script setup>\r\nimport { computed } from 'vue';\r\nimport BaseInfoCard from './BaseInfoCard.vue';\r\nimport JobCard from './JobCard.vue';\r\nimport HouseRentCard from './HouseRentCard.vue';\r\nimport SecondHandCard from './SecondHandCard.vue';\r\n\r\nconst props = defineProps({\r\n  item: {\r\n    type: Object,\r\n    required: true\r\n  }\r\n});\r\n\r\nconst cardType = computed(() => {\r\n  const categoryMap = {\r\n    '招聘信息': 'JobCard',\r\n    '求职信息': 'JobCard',\r\n    '房屋出租': 'HouseRentCard',\r\n    '房屋出售': 'HouseRentCard',\r\n    '二手闲置': 'SecondHandCard',\r\n    '二手车辆': 'SecondHandCard',\r\n    '到家服务': 'BaseInfoCard',\r\n    '寻找服务': 'BaseInfoCard',\r\n    '生意转让': 'BaseInfoCard',\r\n    '宠物信息': 'BaseInfoCard',\r\n    '商家活动': 'BaseInfoCard',\r\n    '婚恋交友': 'BaseInfoCard',\r\n    '车辆服务': 'BaseInfoCard',\r\n    '磁州拼车': 'BaseInfoCard',\r\n    '教育培训': 'BaseInfoCard',\r\n    '其他服务': 'BaseInfoCard'\r\n  };\r\n  \r\n  // 根据分类返回对应的卡片组件类型\r\n  return categoryMap[props.item.category] || 'BaseInfoCard';\r\n});\r\n</script> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/components/cards/InfoCardFactory.vue'\nwx.createComponent(Component)"], "names": ["computed", "Component"], "mappings": ";;;;;AAsBA,MAAM,eAAe,MAAW;AAChC,MAAM,UAAU,MAAW;AAC3B,MAAM,gBAAgB,MAAW;AACjC,MAAM,iBAAiB,MAAW;;;;;;;;;;AAElC,UAAM,QAAQ;AAOd,UAAM,WAAWA,cAAQ,SAAC,MAAM;AAC9B,YAAM,cAAc;AAAA,QAClB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,MACZ;AAGE,aAAO,YAAY,MAAM,KAAK,QAAQ,KAAK;AAAA,IAC7C,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvDD,GAAG,gBAAgBC,SAAS;"}