"use strict";
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      currentDateRange: "2023-05-01 至 2023-05-31"
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    navigateTo(url) {
      common_vendor.index.navigateTo({ url });
    },
    showDatePicker() {
      common_vendor.index.showToast({
        title: "日期选择功能开发中",
        icon: "none"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.t($data.currentDateRange),
    c: common_vendor.o((...args) => $options.showDatePicker && $options.showDatePicker(...args)),
    d: common_vendor.o(($event) => $options.navigateTo("./sales")),
    e: common_vendor.o(($event) => $options.navigateTo("./sales/product")),
    f: common_vendor.o(($event) => $options.navigateTo("./sales/time")),
    g: common_vendor.o(($event) => $options.navigateTo("./sales/profit")),
    h: common_vendor.o(($event) => $options.navigateTo("./customer")),
    i: common_vendor.o(($event) => $options.navigateTo("./customer/new-old")),
    j: common_vendor.o(($event) => $options.navigateTo("./customer/lifecycle")),
    k: common_vendor.o(($event) => $options.navigateTo("./customer/value")),
    l: common_vendor.o(($event) => $options.navigateTo("./customer/churn")),
    m: common_vendor.o(($event) => $options.navigateTo("./marketing")),
    n: common_vendor.o(($event) => $options.navigateTo("./marketing/roi")),
    o: common_vendor.o(($event) => $options.navigateTo("./marketing/funnel")),
    p: common_vendor.o(($event) => $options.navigateTo("./marketing/sensitivity")),
    q: common_vendor.o(($event) => $options.navigateTo("./competition")),
    r: common_vendor.o(($event) => $options.navigateTo("./competition/industry")),
    s: common_vendor.o(($event) => $options.navigateTo("./competition/price")),
    t: common_vendor.o(($event) => $options.navigateTo("./competition/advantage"))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin/pages/analysis/index.js.map
