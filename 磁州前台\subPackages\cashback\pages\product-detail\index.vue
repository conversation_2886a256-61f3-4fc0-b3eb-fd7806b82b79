<template>
  <view class="product-detail-container">
    <!-- 自定义导航栏 -->
    <custom-navbar title="商品详情" :show-back="true"></custom-navbar>
    
    <!-- 内容区域 -->
    <view class="content-container">
      <!-- 商品信息 -->
      <view class="product-info-section">
        <image class="product-image" :src="product.image" mode="aspectFill"></image>
        <view class="product-info">
          <text class="product-title">{{ product.title }}</text>
          <view class="product-price-row">
            <view class="price-container">
              <text class="price-symbol">¥</text>
              <text class="price-value">{{ product.price }}</text>
            </view>
            <view class="cashback-tag">
              <text>返{{ product.cashback }}元</text>
            </view>
          </view>
          <view class="product-source">
            <image class="source-icon" :src="product.platformIcon" mode="aspectFit"></image>
            <text class="source-name">{{ product.platform }}</text>
          </view>
        </view>
      </view>
      
      <!-- 比价功能 -->
      <view class="price-compare-section">
        <view class="section-header">
          <text class="section-title">全网比价</text>
          <view class="sort-options">
            <text 
              class="sort-option" 
              :class="{'sort-option--active': sortBy === 'price'}"
              @tap="sortPrices('price')"
            >按价格</text>
            <text 
              class="sort-option" 
              :class="{'sort-option--active': sortBy === 'cashback'}"
              @tap="sortPrices('cashback')"
            >按返利</text>
          </view>
        </view>
        
        <view class="price-list">
          <view 
            class="price-item" 
            v-for="(item, index) in sortedPriceList" 
            :key="index"
            :class="{'price-item--best': item.isBest}"
            @tap="navigateToStore(item)"
          >
            <view class="price-item-left">
              <image class="platform-icon" :src="item.platformIcon" mode="aspectFit"></image>
              <view class="price-item-info">
                <text class="platform-name">{{ item.platform }}</text>
                <view class="price-tag" v-if="item.isBest">
                  <text>{{ item.bestTag }}</text>
                </view>
              </view>
            </view>
            <view class="price-item-right">
              <view class="price-container">
                <text class="price-value">¥{{ item.price }}</text>
              </view>
              <text class="cashback-value">返¥{{ item.cashback }}</text>
              <view class="go-button">
                <text>去购买</text>
                <svg class="arrow-icon" viewBox="0 0 24 24" width="16" height="16">
                  <path fill="#FFFFFF" d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
                </svg>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 商品详情 -->
      <view class="product-detail-section">
        <view class="section-header">
          <text class="section-title">商品详情</text>
        </view>
        <rich-text :nodes="product.description"></rich-text>
      </view>
    </view>
  </view>
</template>

<script>
import CustomNavbar from '../../components/CustomNavbar.vue';

export default {
  components: {
    CustomNavbar
  },
  data() {
    return {
      link: '',
      product: {
        id: 1,
        title: 'Apple iPhone 15 Pro Max (A2850) 256GB 原色钛金属',
        image: '/static/images/cashback/product-1.png',
        price: '9999.00',
        cashback: '300.00',
        platform: '京东',
        platformIcon: '/static/images/cashback/platform-jd.png',
        description: '<div><p>iPhone 15 Pro Max采用航空级钛金属材质，搭载A17 Pro芯片，4nm工艺制程，全新USB-C接口，支持USB 3传输速度，最高10Gbps...</p></div>'
      },
      priceList: [
        {
          platform: '京东',
          platformIcon: '/static/images/cashback/platform-jd.png',
          price: '9999.00',
          cashback: '300.00',
          isBest: true,
          bestTag: '返利最高',
          url: 'https://item.jd.com/example'
        },
        {
          platform: '天猫',
          platformIcon: '/static/images/cashback/platform-tmall.png',
          price: '9989.00',
          cashback: '280.00',
          isBest: true,
          bestTag: '价格最低',
          url: 'https://detail.tmall.com/example'
        },
        {
          platform: '苏宁',
          platformIcon: '/static/images/cashback/platform-suning.png',
          price: '10099.00',
          cashback: '290.00',
          isBest: false,
          bestTag: '',
          url: 'https://product.suning.com/example'
        },
        {
          platform: '拼多多',
          platformIcon: '/static/images/cashback/platform-pdd.png',
          price: '10199.00',
          cashback: '250.00',
          isBest: false,
          bestTag: '',
          url: 'https://mobile.yangkeduo.com/example'
        },
        {
          platform: '抖音',
          platformIcon: '/static/images/cashback/platform-douyin.png',
          price: '10299.00',
          cashback: '260.00',
          isBest: false,
          bestTag: '',
          url: 'https://haohuo.douyin.com/example'
        }
      ],
      sortBy: 'price' // 默认按价格排序
    };
  },
  computed: {
    sortedPriceList() {
      if (this.sortBy === 'price') {
        return [...this.priceList].sort((a, b) => parseFloat(a.price) - parseFloat(b.price));
      } else {
        return [...this.priceList].sort((a, b) => parseFloat(b.cashback) - parseFloat(a.cashback));
      }
    }
  },
  onLoad(options) {
    // 设置页面不显示系统导航栏
    uni.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#9C27B0'
    });
    
    if (options.link) {
      this.link = decodeURIComponent(options.link);
      this.fetchProductInfo();
    }
    
    if (options.id) {
      this.fetchProductById(options.id);
    }
  },
  methods: {
    // 获取商品信息
    fetchProductInfo() {
      // 这里应该发送请求到后端获取商品信息
      // 使用this.link作为参数
      uni.showLoading({
        title: '获取商品信息...'
      });
      
      // 模拟请求
      setTimeout(() => {
        uni.hideLoading();
        // 实际项目中应该用真实数据替换
        console.log('获取商品信息:', this.link);
      }, 1000);
    },
    
    // 根据ID获取商品
    fetchProductById(id) {
      // 这里应该发送请求到后端获取商品信息
      // 使用id作为参数
      uni.showLoading({
        title: '获取商品信息...'
      });
      
      // 模拟请求
      setTimeout(() => {
        uni.hideLoading();
        // 实际项目中应该用真实数据替换
        console.log('获取商品信息:', id);
      }, 1000);
    },
    
    // 排序价格列表
    sortPrices(type) {
      this.sortBy = type;
    },
    
    // 跳转到商店
    navigateToStore(item) {
      // 这里可以跳转到对应的电商平台
      // 实际项目中可能需要通过特殊方式打开
      uni.showModal({
        title: '跳转提示',
        content: `即将跳转到${item.platform}购买，确认继续吗？`,
        success: (res) => {
          if (res.confirm) {
            // 可以使用plus.runtime.openURL(item.url)在APP中打开外部链接
            // 或者使用其他方式处理
            uni.showToast({
              title: `正在跳转到${item.platform}`,
              icon: 'none'
            });
          }
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.product-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content-container {
  padding-top: calc(var(--status-bar-height) + 44px);
  padding-bottom: 20px;
}

.product-info-section {
  background-color: #FFFFFF;
  padding: 16px;
  
  .product-image {
    width: 100%;
    height: 300px;
    border-radius: 12px;
    margin-bottom: 16px;
  }
  
  .product-info {
    .product-title {
      font-size: 16px;
      color: #333333;
      line-height: 1.4;
      margin-bottom: 12px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    .product-price-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
      
      .price-container {
        display: flex;
        align-items: baseline;
        
        .price-symbol {
          font-size: 14px;
          color: #FF6B6B;
          margin-right: 2px;
        }
        
        .price-value {
          font-size: 20px;
          font-weight: 600;
          color: #FF6B6B;
        }
      }
      
      .cashback-tag {
        background-color: rgba(156, 39, 176, 0.1);
        padding: 4px 8px;
        border-radius: 4px;
        
        text {
          font-size: 12px;
          color: #9C27B0;
        }
      }
    }
    
    .product-source {
      display: flex;
      align-items: center;
      
      .source-icon {
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }
      
      .source-name {
        font-size: 12px;
        color: #999999;
      }
    }
  }
}

.price-compare-section {
  margin-top: 12px;
  background-color: #FFFFFF;
  padding: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333333;
  }
  
  .sort-options {
    display: flex;
    
    .sort-option {
      font-size: 14px;
      color: #999999;
      margin-left: 16px;
      position: relative;
      
      &--active {
        color: #9C27B0;
        
        &::after {
          content: '';
          position: absolute;
          bottom: -4px;
          left: 0;
          right: 0;
          height: 2px;
          background-color: #9C27B0;
          border-radius: 1px;
        }
      }
    }
  }
}

.price-list {
  .price-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #EEEEEE;
    
    &:last-child {
      border-bottom: none;
    }
    
    &--best {
      background-color: rgba(156, 39, 176, 0.05);
      border-radius: 8px;
      padding: 16px 8px;
      margin: 0 -8px;
    }
    
    .price-item-left {
      display: flex;
      align-items: center;
      
      .platform-icon {
        width: 24px;
        height: 24px;
        margin-right: 8px;
      }
      
      .price-item-info {
        .platform-name {
          font-size: 14px;
          color: #333333;
          margin-bottom: 4px;
        }
        
        .price-tag {
          display: inline-block;
          background-color: #9C27B0;
          border-radius: 2px;
          padding: 2px 4px;
          
          text {
            font-size: 10px;
            color: #FFFFFF;
          }
        }
      }
    }
    
    .price-item-right {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      
      .price-container {
        margin-bottom: 4px;
        
        .price-value {
          font-size: 16px;
          font-weight: 500;
          color: #FF6B6B;
        }
      }
      
      .cashback-value {
        font-size: 12px;
        color: #9C27B0;
        margin-bottom: 8px;
      }
      
      .go-button {
        display: flex;
        align-items: center;
        background-color: #9C27B0;
        border-radius: 12px;
        padding: 4px 10px;
        
        text {
          font-size: 12px;
          color: #FFFFFF;
        }
        
        .arrow-icon {
          margin-left: 2px;
        }
      }
    }
  }
}

.product-detail-section {
  margin-top: 12px;
  background-color: #FFFFFF;
  padding: 16px;
}
</style> 