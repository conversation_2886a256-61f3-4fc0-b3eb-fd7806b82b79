{"version": 3, "file": "create.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/coupon/create.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xjb3Vwb25cY3JlYXRlLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"coupon-create-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @tap=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">创建优惠券</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @tap=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 表单容器 -->\n    <scroll-view scroll-y class=\"form-container\">\n      <!-- 优惠券类型选择 -->\n      <view class=\"type-selection-section\">\n        <view class=\"type-option\" \n          v-for=\"(type, index) in couponTypes\" \n          :key=\"index\"\n          :class=\"{'active': selectedType === type.value}\"\n          @tap=\"selectCouponType(type.value)\">\n          <view class=\"type-icon\" :class=\"'bg-'+type.value\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n              <path d=\"M22 12c0-5.52-4.48-10-10-10S2 6.48 2 12s4.48 10 10 10 10-4.48 10-10z\"></path>\n              <path d=\"M8 14s1.5 2 4 2 4-2 4-2\"></path>\n              <line x1=\"9\" y1=\"9\" x2=\"9.01\" y2=\"9\"></line>\n              <line x1=\"15\" y1=\"9\" x2=\"15.01\" y2=\"9\"></line>\n            </svg>\n          </view>\n          <text class=\"type-name\">{{type.name}}</text>\n          <text class=\"type-desc\">{{type.description}}</text>\n          <view class=\"type-check\" v-if=\"selectedType === type.value\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n              <polyline points=\"20 6 9 17 4 12\"></polyline>\n            </svg>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 基本信息 -->\n      <view class=\"form-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">基本信息</text>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"item-label\">优惠券名称</text>\n          <input type=\"text\" class=\"item-input\" v-model=\"couponForm.title\" placeholder=\"请输入优惠券名称\" />\n        </view>\n        \n        <view class=\"form-item\" v-if=\"selectedType === 'discount'\">\n          <text class=\"item-label\">优惠金额</text>\n          <view class=\"amount-input-wrapper\">\n            <text class=\"amount-symbol\">¥</text>\n            <input type=\"digit\" class=\"amount-input\" v-model=\"couponForm.value\" placeholder=\"请输入优惠金额\" />\n          </view>\n        </view>\n        \n<view class=\"form-item\" v-if=\"selectedType === 'percent'\">\n  <text class=\"item-label\">折扣比例</text>\n  <view class=\"amount-input-wrapper\">\n    <input type=\"digit\" class=\"amount-input\" v-model=\"couponForm.discount\" placeholder=\"请输入折扣比例\" />\n    <text class=\"amount-unit\">折</text>\n  </view>\n</view>\n\n<view class=\"form-item\" v-if=\"selectedType !== 'free'\">\n  <text class=\"item-label\">使用门槛</text>\n  <view class=\"amount-input-wrapper\">\n    <text class=\"threshold-text\">满</text>\n    <input type=\"digit\" class=\"amount-input\" v-model=\"couponForm.minSpend\" placeholder=\"请输入最低消费金额\" />\n    <text class=\"threshold-text\">元可用</text>\n  </view>\n</view>\n      </view>\n      \n      <!-- 有效期设置 -->\n      <view class=\"form-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">有效期设置</text>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"item-label\">有效期类型</text>\n          <view class=\"validity-type-selector\">\n            <view \n              class=\"type-option\" \n              :class=\"{'active': validityType === 'fixed'}\"\n              @tap=\"setValidityType('fixed')\">\n              <view class=\"option-radio\">\n                <view class=\"radio-inner\" v-if=\"validityType === 'fixed'\"></view>\n              </view>\n              <text class=\"option-text\">固定日期</text>\n            </view>\n            \n            <view \n              class=\"type-option\" \n              :class=\"{'active': validityType === 'dynamic'}\"\n              @tap=\"setValidityType('dynamic')\">\n              <view class=\"option-radio\">\n                <view class=\"radio-inner\" v-if=\"validityType === 'dynamic'\"></view>\n              </view>\n              <text class=\"option-text\">领取后生效</text>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"form-item\" v-if=\"validityType === 'fixed'\">\n          <text class=\"item-label\">开始日期</text>\n          <view class=\"date-selector\" @tap=\"showStartDatePicker\">\n            <text class=\"date-value\">{{couponForm.startDate}}</text>\n            <view class=\"date-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                <rect x=\"3\" y=\"4\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"></rect>\n                <line x1=\"16\" y1=\"2\" x2=\"16\" y2=\"6\"></line>\n                <line x1=\"8\" y1=\"2\" x2=\"8\" y2=\"6\"></line>\n                <line x1=\"3\" y1=\"10\" x2=\"21\" y2=\"10\"></line>\n              </svg>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"form-item\" v-if=\"validityType === 'fixed'\">\n          <text class=\"item-label\">结束日期</text>\n          <view class=\"date-selector\" @tap=\"showEndDatePicker\">\n            <text class=\"date-value\">{{couponForm.expireDate}}</text>\n            <view class=\"date-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                <rect x=\"3\" y=\"4\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"></rect>\n                <line x1=\"16\" y1=\"2\" x2=\"16\" y2=\"6\"></line>\n                <line x1=\"8\" y1=\"2\" x2=\"8\" y2=\"6\"></line>\n                <line x1=\"3\" y1=\"10\" x2=\"21\" y2=\"10\"></line>\n              </svg>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"form-item\" v-if=\"validityType === 'dynamic'\">\n          <text class=\"item-label\">有效天数</text>\n          <view class=\"days-input-wrapper\">\n            <input type=\"number\" class=\"days-input\" v-model=\"couponForm.validDays\" placeholder=\"请输入有效天数\" />\n            <text class=\"days-text\">天</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 发放设置 -->\n      <view class=\"form-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">发放设置</text>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"item-label\">发放总量</text>\n          <view class=\"amount-input-wrapper\">\n            <input type=\"number\" class=\"amount-input\" v-model=\"couponForm.totalCount\" placeholder=\"请输入发放总量\" />\n            <text class=\"amount-unit\">张</text>\n          </view>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"item-label\">每人限领</text>\n          <view class=\"amount-input-wrapper\">\n            <input type=\"number\" class=\"amount-input\" v-model=\"couponForm.perPersonLimit\" placeholder=\"请输入每人限领数量\" />\n            <text class=\"amount-unit\">张</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 使用规则 -->\n      <view class=\"form-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">使用规则</text>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"item-label\">适用商品</text>\n          <view class=\"product-selector\" @tap=\"showProductSelector\">\n            <text class=\"selector-value\">{{couponForm.applicableProducts}}</text>\n            <view class=\"selector-arrow\"></view>\n          </view>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"item-label\">使用说明</text>\n          <textarea class=\"item-textarea\" v-model=\"couponForm.instructions\" placeholder=\"请输入使用说明\" />\n        </view>\n      </view>\n      \n      <!-- 分销设置 -->\n      <view class=\"form-section\" v-if=\"hasMerchantDistribution\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">分销设置</text>\n          <text class=\"section-subtitle\">设置优惠券分销佣金</text>\n        </view>\n        \n        <DistributionSetting \n          :settings=\"couponForm.distributionSettings\"\n          @update-settings=\"updateDistributionSettings\"\n        />\n      </view>\n      \n      <!-- 活动推广 -->\n      <view class=\"form-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">活动推广</text>\n          <text class=\"section-subtitle\">选择发布方式</text>\n        </view>\n        \n        <MarketingPromotionActions \n          :activity-type=\"'coupon'\"\n          :activity-id=\"tempCouponId\"\n          :publish-mode-only=\"true\"\n          :show-actions=\"['publish']\"\n          @action-completed=\"handlePromotionCompleted\"\n        />\n      </view>\n    </scroll-view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"bottom-action-bar\">\n      <view class=\"action-button preview\" @tap=\"previewCoupon\">\n        <text class=\"button-text\">预览</text>\n      </view>\n      <view class=\"action-button create\" @tap=\"createCoupon\">\n        <text class=\"button-text\">创建</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { ref, reactive, onMounted } from 'vue';\nimport MarketingPromotionActions from '/subPackages/merchant-admin-marketing/components/MarketingPromotionActions.vue';\nimport DistributionSetting from '/subPackages/merchant-admin-marketing/pages/marketing/distribution/components/DistributionSetting.vue';\n\nexport default {\n  name: 'CouponCreate',\n  components: {\n    MarketingPromotionActions,\n    DistributionSetting\n  },\n  setup() {\n    // 响应式状态\n    const selectedType = ref('discount');\n    const validityType = ref('fixed');\n    const tempCouponId = ref('temp-' + Date.now()); // 临时ID，实际应该从后端获取\n    const hasMerchantDistribution = ref(false); // 商家是否开通分销功能\n    \n    const couponTypes = [\n      {\n        value: 'discount',\n        name: '满减券',\n        description: '满足消费金额门槛后减免固定金额'\n      },\n      {\n        value: 'percent',\n        name: '折扣券',\n        description: '满足消费金额门槛后按比例折扣'\n      },\n      {\n        value: 'free',\n        name: '无门槛券',\n        description: '无消费金额门槛，直接减免固定金额'\n      }\n    ];\n    \n    const couponForm = reactive({\n      title: '',\n      value: '',\n      discount: '',\n      minSpend: '',\n      startDate: formatDate(new Date()),\n      expireDate: formatDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)), // 30天后\n      validDays: '30',\n      totalCount: '',\n      perPersonLimit: '1',\n      applicableProducts: '全部商品',\n      instructions: '',\n      \n      // 分销设置\n      distributionSettings: {\n        enabled: false,\n        commissionMode: 'percentage',\n        commissions: {\n          level1: '',\n          level2: '',\n          level3: ''\n        },\n        enableLevel3: false\n      }\n    });\n    \n    // 方法\n    function goBack() {\n      uni.navigateBack();\n    }\n    \n    function showHelp() {\n      uni.showModal({\n        title: '帮助信息',\n        content: '创建优惠券页面可以设置优惠券的各项参数，包括类型、金额、有效期等。创建后可在优惠券管理页面查看和管理。',\n        showCancel: false\n      });\n    }\n    \n    function selectCouponType(type) {\n      selectedType.value = type;\n    }\n    \n    function setValidityType(type) {\n      validityType.value = type;\n    }\n    \n    function showStartDatePicker() {\n      uni.showDatePicker({\n        current: couponForm.startDate,\n        success: (res) => {\n          couponForm.startDate = res.date;\n        }\n      });\n    }\n    \n    function showEndDatePicker() {\n      uni.showDatePicker({\n        current: couponForm.expireDate,\n        success: (res) => {\n          couponForm.expireDate = res.date;\n        }\n      });\n    }\n    \n    function showProductSelector() {\n      uni.showActionSheet({\n        itemList: ['全部商品', '指定商品', '指定分类'],\n        success: (res) => {\n          const options = ['全部商品', '指定商品', '指定分类'];\n          couponForm.applicableProducts = options[res.tapIndex];\n          \n          if (res.tapIndex > 0) {\n            // 如果选择了指定商品或分类，显示选择界面\n            uni.showToast({\n              title: '请在下一页选择',\n              icon: 'none'\n            });\n          }\n        }\n      });\n    }\n    \n    function previewCoupon() {\n      // 表单验证\n      if (!validateForm()) {\n        return;\n      }\n      \n      // 显示预览弹窗\n      uni.showToast({\n        title: '预览功能开发中',\n        icon: 'none'\n      });\n    }\n    \n    async function createCoupon() {\n      // 表单验证\n      if (!validateForm()) {\n        return;\n      }\n      \n      // 在实际应用中，这里应该调用API创建优惠券\n      \n      uni.showLoading({\n        title: '创建中...'\n      });\n      \n      // 保存分销设置\n      if (hasMerchantDistribution.value && couponForm.distributionSettings.enabled) {\n        const success = await saveDistributionSettings();\n        if (!success) {\n          uni.hideLoading();\n          uni.showToast({\n            title: '分销设置保存失败',\n            icon: 'none'\n          });\n          return;\n        }\n      }\n      \n      setTimeout(() => {\n        uni.hideLoading();\n        \n        uni.showToast({\n          title: '创建成功',\n          icon: 'success'\n        });\n        \n        setTimeout(() => {\n          uni.navigateBack();\n        }, 1500);\n      }, 1000);\n    }\n    \n    function validateForm() {\n      if (!couponForm.title) {\n        uni.showToast({\n          title: '请输入优惠券名称',\n          icon: 'none'\n        });\n        return false;\n      }\n      \n      if (selectedType.value === 'discount' && !couponForm.value) {\n        uni.showToast({\n          title: '请输入优惠金额',\n          icon: 'none'\n        });\n        return false;\n      }\n      \n      if (selectedType.value === 'percent' && !couponForm.discount) {\n        uni.showToast({\n          title: '请输入折扣比例',\n          icon: 'none'\n        });\n        return false;\n      }\n      \n      if (selectedType.value !== 'free' && !couponForm.minSpend) {\n        uni.showToast({\n          title: '请输入使用门槛',\n          icon: 'none'\n        });\n        return false;\n      }\n      \n      if (!couponForm.totalCount) {\n        uni.showToast({\n          title: '请输入发放总量',\n          icon: 'none'\n        });\n        return false;\n      }\n      \n      return true;\n    }\n    \n    function formatDate(date) {\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      return `${year}-${month}-${day}`;\n    }\n    \n    function loadTemplateData(templateId) {\n      // 在实际应用中，这里应该调用API获取模板数据\n      \n      // 模拟加载模板数据\n      uni.showLoading({\n        title: '加载模板...'\n      });\n      \n      setTimeout(() => {\n        // 模拟模板数据\n        Object.assign(couponForm, {\n          title: '模板优惠券',\n          value: '20',\n          minSpend: '200',\n          totalCount: '1000',\n          perPersonLimit: '2',\n          applicableProducts: '全部商品',\n          instructions: '模板优惠券使用说明'\n        });\n        \n        uni.hideLoading();\n      }, 500);\n    }\n    \n    // 更新分销设置\n    function updateDistributionSettings(settings) {\n      couponForm.distributionSettings = settings;\n    }\n    \n    // 检查商家是否开通分销功能\n    function checkMerchantDistribution() {\n      // 调用API检查商家是否开通分销功能\n      // 这里模拟API调用\n      setTimeout(() => {\n        hasMerchantDistribution.value = true;\n      }, 500);\n    }\n    \n    // 保存分销设置\n    async function saveDistributionSettings() {\n      // 模拟API调用\n      return new Promise((resolve) => {\n        if (hasMerchantDistribution.value && couponForm.distributionSettings.enabled) {\n          setTimeout(() => {\n            console.log('分销设置已保存');\n            resolve(true);\n          }, 500);\n        } else {\n          resolve(true);\n        }\n      });\n    }\n    \n    // 处理推广操作完成事件\n    function handlePromotionCompleted(data) {\n      console.log('推广操作完成:', data);\n      // 根据不同操作类型处理结果\n      if (data.action === 'publish') {\n        uni.showToast({\n          title: '发布成功',\n          icon: 'success'\n        });\n      }\n    }\n    \n    // 在挂载时检查分销功能\n    onMounted(() => {\n      // 可以在这里处理从模板创建的情况\n      const pages = getCurrentPages();\n      const currentPage = pages[pages.length - 1];\n      const options = currentPage.$page?.options || {};\n      \n      if (options.template) {\n        // 从模板创建，加载模板数据\n        loadTemplateData(options.template);\n      }\n      \n      // 检查商家是否开通分销功能\n      checkMerchantDistribution();\n    });\n    \n    return {\n      selectedType,\n      validityType,\n      couponTypes,\n      couponForm,\n      hasMerchantDistribution,\n      tempCouponId,\n      goBack,\n      showHelp,\n      selectCouponType,\n      setValidityType,\n      showStartDatePicker,\n      showEndDatePicker,\n      showProductSelector,\n      previewCoupon,\n      createCoupon,\n      updateDistributionSettings,\n      handlePromotionCompleted\n    };\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.coupon-create-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: 80px; /* 为底部操作栏留出空间 */\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #FF9966, #FF5E62);\n  color: #fff;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(255, 94, 98, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: bold;\n}\n\n/* 表单容器样式 */\n.form-container {\n  height: calc(100vh - 80px - 44px - 15px - 15px);\n}\n\n/* 优惠券类型选择样式 */\n.type-selection-section {\n  margin: 15px;\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.type-option {\n  width: calc(33.33% - 10px);\n  margin: 0 5px 10px;\n  background: #fff;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  position: relative;\n  transition: all 0.3s;\n}\n\n.type-option.active {\n  border: 2px solid #FF5E62;\n  padding: 13px;\n}\n\n.type-icon {\n  width: 50px;\n  height: 50px;\n  border-radius: 25px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 10px;\n  color: #fff;\n}\n\n.bg-discount {\n  background: linear-gradient(135deg, #FF9966, #FF5E62);\n}\n\n.bg-percent {\n  background: linear-gradient(135deg, #36D1DC, #5B86E5);\n}\n\n.bg-free {\n  background: linear-gradient(135deg, #11998e, #38ef7d);\n}\n\n.type-name {\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.type-desc {\n  font-size: 12px;\n  color: #999;\n  text-align: center;\n  line-height: 1.3;\n}\n\n.type-check {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  width: 20px;\n  height: 20px;\n  color: #FF5E62;\n}\n\n/* 表单部分样式 */\n.form-section {\n  margin: 15px;\n  background: #fff;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n}\n\n.section-header {\n  margin-bottom: 15px;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.section-subtitle {\n  font-size: 12px;\n  color: #999;\n  margin-top: 5px;\n}\n\n.form-item {\n  margin-bottom: 15px;\n}\n\n.form-item:last-child {\n  margin-bottom: 0;\n}\n\n.item-label {\n  font-size: 14px;\n  color: #666;\n  margin-bottom: 8px;\n  display: block;\n}\n\n.item-input {\n  width: 100%;\n  height: 44px;\n  background: #F5F7FA;\n  border-radius: 8px;\n  padding: 0 12px;\n  font-size: 14px;\n  color: #333;\n  box-sizing: border-box;\n}\n\n.amount-input-wrapper {\n  display: flex;\n  align-items: center;\n  background: #F5F7FA;\n  border-radius: 8px;\n  padding: 0 12px;\n  height: 44px;\n}\n\n.amount-symbol {\n  font-size: 14px;\n  color: #333;\n  margin-right: 5px;\n}\n\n.amount-input {\n  flex: 1;\n  height: 44px;\n  font-size: 14px;\n  color: #333;\n  background: transparent;\n}\n\n.amount-unit {\n  font-size: 14px;\n  color: #666;\n}\n\n.threshold-text {\n  font-size: 14px;\n  color: #666;\n  margin: 0 5px;\n}\n\n.validity-type-selector {\n  display: flex;\n}\n\n.type-option {\n  display: flex;\n  align-items: center;\n  margin-right: 20px;\n}\n\n.option-radio {\n  width: 18px;\n  height: 18px;\n  border-radius: 9px;\n  border: 1px solid #ddd;\n  margin-right: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.radio-inner {\n  width: 10px;\n  height: 10px;\n  border-radius: 5px;\n  background: #FF5E62;\n}\n\n.option-text {\n  font-size: 14px;\n  color: #333;\n}\n\n.type-option.active .option-text {\n  color: #FF5E62;\n}\n\n.date-selector {\n  width: 100%;\n  height: 44px;\n  background: #F5F7FA;\n  border-radius: 8px;\n  padding: 0 12px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.date-value {\n  font-size: 14px;\n  color: #333;\n}\n\n.date-icon {\n  width: 20px;\n  height: 20px;\n  color: #999;\n}\n\n.days-input-wrapper {\n  display: flex;\n  align-items: center;\n  background: #F5F7FA;\n  border-radius: 8px;\n  padding: 0 12px;\n  height: 44px;\n}\n\n.days-input {\n  flex: 1;\n  height: 44px;\n  font-size: 14px;\n  color: #333;\n  background: transparent;\n}\n\n.days-text {\n  font-size: 14px;\n  color: #666;\n}\n\n.product-selector {\n  width: 100%;\n  height: 44px;\n  background: #F5F7FA;\n  border-radius: 8px;\n  padding: 0 12px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.selector-value {\n  font-size: 14px;\n  color: #333;\n}\n\n.selector-arrow {\n  width: 10px;\n  height: 10px;\n  border-right: 2px solid #999;\n  border-bottom: 2px solid #999;\n  transform: rotate(45deg);\n}\n\n.item-textarea {\n  width: 100%;\n  height: 100px;\n  background: #F5F7FA;\n  border-radius: 8px;\n  padding: 12px;\n  font-size: 14px;\n  color: #333;\n  box-sizing: border-box;\n}\n\n/* 底部操作栏样式 */\n.bottom-action-bar {\n  position: fixed;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: #fff;\n  display: flex;\n  padding: 15px;\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);\n  z-index: 90;\n}\n\n.action-button {\n  flex: 1;\n  height: 44px;\n  border-radius: 22px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 0 5px;\n}\n\n.action-button.preview {\n  background: #F5F7FA;\n  border: 1px solid #FF5E62;\n}\n\n.action-button.create {\n  background: linear-gradient(135deg, #FF9966, #FF5E62);\n}\n\n.button-text {\n  font-size: 16px;\n  font-weight: 500;\n}\n\n.action-button.preview .button-text {\n  color: #FF5E62;\n}\n\n.action-button.create .button-text {\n  color: #fff;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/coupon/create.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "uni", "onMounted"], "mappings": ";;AA0OA,MAAK,4BAA6B,MAAW;AAC7C,MAAO,sBAAqB,MAAW;AAEvC,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,YAAY;AAAA,IACV;AAAA,IACA;AAAA,EACD;AAAA,EACD,QAAQ;AAEN,UAAM,eAAeA,kBAAI,UAAU;AACnC,UAAM,eAAeA,kBAAI,OAAO;AAChC,UAAM,eAAeA,cAAAA,IAAI,UAAU,KAAK,IAAK,CAAA;AAC7C,UAAM,0BAA0BA,kBAAI,KAAK;AAEzC,UAAM,cAAc;AAAA,MAClB;AAAA,QACE,OAAO;AAAA,QACP,MAAM;AAAA,QACN,aAAa;AAAA,MACd;AAAA,MACD;AAAA,QACE,OAAO;AAAA,QACP,MAAM;AAAA,QACN,aAAa;AAAA,MACd;AAAA,MACD;AAAA,QACE,OAAO;AAAA,QACP,MAAM;AAAA,QACN,aAAa;AAAA,MACf;AAAA;AAGF,UAAM,aAAaC,cAAAA,SAAS;AAAA,MAC1B,OAAO;AAAA,MACP,OAAO;AAAA,MACP,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,WAAW,oBAAI,MAAM;AAAA,MAChC,YAAY,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,CAAC;AAAA;AAAA,MACtE,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,MACpB,cAAc;AAAA;AAAA,MAGd,sBAAsB;AAAA,QACpB,SAAS;AAAA,QACT,gBAAgB;AAAA,QAChB,aAAa;AAAA,UACX,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,QACD,cAAc;AAAA,MAChB;AAAA,IACF,CAAC;AAGD,aAAS,SAAS;AAChBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAEA,aAAS,WAAW;AAClBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MACd,CAAC;AAAA,IACH;AAEA,aAAS,iBAAiB,MAAM;AAC9B,mBAAa,QAAQ;AAAA,IACvB;AAEA,aAAS,gBAAgB,MAAM;AAC7B,mBAAa,QAAQ;AAAA,IACvB;AAEA,aAAS,sBAAsB;AAC7BA,oBAAAA,MAAI,eAAe;AAAA,QACjB,SAAS,WAAW;AAAA,QACpB,SAAS,CAAC,QAAQ;AAChB,qBAAW,YAAY,IAAI;AAAA,QAC7B;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,oBAAoB;AAC3BA,oBAAAA,MAAI,eAAe;AAAA,QACjB,SAAS,WAAW;AAAA,QACpB,SAAS,CAAC,QAAQ;AAChB,qBAAW,aAAa,IAAI;AAAA,QAC9B;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,sBAAsB;AAC7BA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,QAAQ,QAAQ,MAAM;AAAA,QACjC,SAAS,CAAC,QAAQ;AAChB,gBAAM,UAAU,CAAC,QAAQ,QAAQ,MAAM;AACvC,qBAAW,qBAAqB,QAAQ,IAAI,QAAQ;AAEpD,cAAI,IAAI,WAAW,GAAG;AAEpBA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,gBAAgB;AAEvB,UAAI,CAAC,aAAY,GAAI;AACnB;AAAA,MACF;AAGAA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAEA,mBAAe,eAAe;AAE5B,UAAI,CAAC,aAAY,GAAI;AACnB;AAAA,MACF;AAIAA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAGD,UAAI,wBAAwB,SAAS,WAAW,qBAAqB,SAAS;AAC5E,cAAM,UAAU,MAAM;AACtB,YAAI,CAAC,SAAS;AACZA,wBAAG,MAAC,YAAW;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AACD;AAAA,QACF;AAAA,MACF;AAEA,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAEfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAED,mBAAW,MAAM;AACfA,wBAAG,MAAC,aAAY;AAAA,QACjB,GAAE,IAAI;AAAA,MACR,GAAE,GAAI;AAAA,IACT;AAEA,aAAS,eAAe;AACtB,UAAI,CAAC,WAAW,OAAO;AACrBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,aAAa,UAAU,cAAc,CAAC,WAAW,OAAO;AAC1DA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,aAAa,UAAU,aAAa,CAAC,WAAW,UAAU;AAC5DA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,aAAa,UAAU,UAAU,CAAC,WAAW,UAAU;AACzDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,WAAW,YAAY;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,WAAW,MAAM;AACxB,YAAM,OAAO,KAAK;AAClB,YAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,YAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAChC;AAEA,aAAS,iBAAiB,YAAY;AAIpCA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAED,iBAAW,MAAM;AAEf,eAAO,OAAO,YAAY;AAAA,UACxB,OAAO;AAAA,UACP,OAAO;AAAA,UACP,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,gBAAgB;AAAA,UAChB,oBAAoB;AAAA,UACpB,cAAc;AAAA,QAChB,CAAC;AAEDA,sBAAG,MAAC,YAAW;AAAA,MAChB,GAAE,GAAG;AAAA,IACR;AAGA,aAAS,2BAA2B,UAAU;AAC5C,iBAAW,uBAAuB;AAAA,IACpC;AAGA,aAAS,4BAA4B;AAGnC,iBAAW,MAAM;AACf,gCAAwB,QAAQ;AAAA,MACjC,GAAE,GAAG;AAAA,IACR;AAGA,mBAAe,2BAA2B;AAExC,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,YAAI,wBAAwB,SAAS,WAAW,qBAAqB,SAAS;AAC5E,qBAAW,MAAM;AACfA,0BAAAA,oGAAY,SAAS;AACrB,oBAAQ,IAAI;AAAA,UACb,GAAE,GAAG;AAAA,eACD;AACL,kBAAQ,IAAI;AAAA,QACd;AAAA,MACF,CAAC;AAAA,IACH;AAGA,aAAS,yBAAyB,MAAM;AACtCA,oBAAY,MAAA,MAAA,OAAA,iFAAA,WAAW,IAAI;AAE3B,UAAI,KAAK,WAAW,WAAW;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACF;AAGAC,kBAAAA,UAAU,MAAM;;AAEd,YAAM,QAAQ;AACd,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,YAAU,iBAAY,UAAZ,mBAAmB,YAAW,CAAA;AAE9C,UAAI,QAAQ,UAAU;AAEpB,yBAAiB,QAAQ,QAAQ;AAAA,MACnC;AAGA;IACF,CAAC;AAED,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;EAEJ;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1iBA,GAAG,WAAW,eAAe;"}