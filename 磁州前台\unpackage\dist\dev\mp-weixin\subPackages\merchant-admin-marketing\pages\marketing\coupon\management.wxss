/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-f35110dd, html.data-v-f35110dd, #app.data-v-f35110dd, .index-container.data-v-f35110dd {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.coupon-container.data-v-f35110dd {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar.data-v-f35110dd {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF9966, #FF5E62);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(255, 94, 98, 0.15);
}
.navbar-back.data-v-f35110dd {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon.data-v-f35110dd {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title.data-v-f35110dd {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right.data-v-f35110dd {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon.data-v-f35110dd {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 概览部分样式 */
.overview-section.data-v-f35110dd {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}
.overview-cards.data-v-f35110dd {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px;
}
.overview-card.data-v-f35110dd {
  width: 25%;
  padding: 0 5px;
  box-sizing: border-box;
}
.card-value.data-v-f35110dd {
  display: block;
  font-size: 18px;
  font-weight: bold;
  color: #FF5E62;
  text-align: center;
  margin-bottom: 5px;
}
.card-label.data-v-f35110dd {
  display: block;
  font-size: 12px;
  color: #666;
  text-align: center;
}

/* 操作栏样式 */
.action-bar.data-v-f35110dd {
  margin: 0 15px 15px;
  display: flex;
  align-items: center;
}
.search-box.data-v-f35110dd {
  flex: 1;
  height: 36px;
  background: #fff;
  border-radius: 18px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.search-icon.data-v-f35110dd {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  color: #999;
}
.icon-svg.data-v-f35110dd {
  width: 16px;
  height: 16px;
}
.search-input.data-v-f35110dd {
  flex: 1;
  height: 36px;
  font-size: 14px;
  color: #333;
  border: none;
  background: transparent;
}
.filter-btn.data-v-f35110dd, .sort-btn.data-v-f35110dd {
  height: 36px;
  padding: 0 12px;
  background: #fff;
  border-radius: 18px;
  margin-left: 10px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.btn-icon.data-v-f35110dd {
  width: 16px;
  height: 16px;
  margin-right: 5px;
  color: #666;
}
.btn-text.data-v-f35110dd {
  font-size: 14px;
  color: #666;
}

/* 优惠券列表样式 */
.coupon-list.data-v-f35110dd {
  padding: 0 15px;
  margin-bottom: 80px;
  /* 为悬浮按钮留出空间 */
}
.coupon-card.data-v-f35110dd {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
}
.coupon-card.hover.data-v-f35110dd {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}
.coupon-header.data-v-f35110dd {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}
.coupon-title.data-v-f35110dd {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.coupon-status.data-v-f35110dd {
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 12px;
  color: white;
}
.status-active.data-v-f35110dd {
  background: #34C759;
}
.status-expired.data-v-f35110dd {
  background: #8E8E93;
}
.status-upcoming.data-v-f35110dd {
  background: #FF9500;
}
.status-paused.data-v-f35110dd {
  background: #FF9500;
}
.coupon-value.data-v-f35110dd {
  display: flex;
  align-items: baseline;
  margin-bottom: 12px;
}
.discount-symbol.data-v-f35110dd {
  font-size: 16px;
  color: #FF5E62;
  margin-right: 2px;
}
.discount-amount.data-v-f35110dd {
  font-size: 24px;
  font-weight: bold;
  color: #FF5E62;
  margin-right: 8px;
}
.discount-condition.data-v-f35110dd {
  font-size: 12px;
  color: #666;
}
.coupon-info.data-v-f35110dd {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 15px;
}
.info-item.data-v-f35110dd {
  width: 50%;
  margin-bottom: 5px;
  display: flex;
}
.info-label.data-v-f35110dd {
  font-size: 12px;
  color: #999;
  margin-right: 5px;
}
.info-value.data-v-f35110dd {
  font-size: 12px;
  color: #333;
  font-weight: 500;
}
.coupon-actions.data-v-f35110dd {
  display: flex;
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}
.action-btn.data-v-f35110dd {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 5px 0;
}
.action-btn .btn-icon.data-v-f35110dd {
  width: 20px;
  height: 20px;
  margin-right: 0;
  margin-bottom: 3px;
}
.action-btn .btn-text.data-v-f35110dd {
  font-size: 12px;
}
.action-btn.edit.data-v-f35110dd {
  color: #007AFF;
}
.action-btn.pause.data-v-f35110dd {
  color: #FF9500;
}
.action-btn.activate.data-v-f35110dd {
  color: #34C759;
}
.action-btn.delete.data-v-f35110dd {
  color: #FF3B30;
}

/* 空状态样式 */
.empty-state.data-v-f35110dd {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}
.empty-icon.data-v-f35110dd {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background: rgba(255, 94, 98, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  color: #FF5E62;
}
.empty-text.data-v-f35110dd {
  font-size: 16px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}
.empty-subtext.data-v-f35110dd {
  font-size: 14px;
  color: #999;
}

/* 顶部操作区样式 */
.top-actions.data-v-f35110dd {
  padding: 15px;
  display: flex;
  justify-content: flex-end;
  background-color: #fff;
}

/* 悬浮操作按钮样式已移除，使用CreateButton组件 */