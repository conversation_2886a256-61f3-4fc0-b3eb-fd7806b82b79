<template>
  <view class="business-filter-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="back-btn" @click="navigateBack">
        <view class="back-icon"></view>
      </view>
      <view class="navbar-title">{{businessTitle}}</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-box">
        <image class="search-icon" src="/static/images/tabbar/放大镜.png"></image>
        <input class="search-input" type="text" v-model="searchKeyword" placeholder="搜索商家名称、商品或服务" confirm-type="search" @confirm="searchBusinesses" />
        <view class="search-btn" @click="searchBusinesses">搜索</view>
      </view>
    </view>
    
    <!-- 顶部一级分类标签栏 -->
    <scroll-view class="top-category-tabs" scroll-x :show-scrollbar="false">
      <view 
        class="top-category-item" 
        v-for="(item, index) in categories" 
        :key="index"
        :class="{'active-top-category': currentCategory === item.name}"
        @click="switchCategory(item)">
        {{item.name}}
      </view>
    </scroll-view>
    
    <!-- 子分类标签栏 -->
    <scroll-view class="subcategory-tabs" scroll-x :show-scrollbar="false" v-if="subcategoryList.length > 0">
      <view 
        class="subcategory-item" 
        v-for="(subcat, index) in subcategoryList" 
        :key="index"
        :class="{'active-subcategory': selectedSubcategory === subcat}"
        @click="selectSubcategory(subcat)">
        {{subcat}}
      </view>
    </scroll-view>
    
    <!-- 筛选条件栏 -->
    <view class="filter-section">
      <!-- 区域筛选 -->
      <view class="filter-item" @click="showAreaFilter = true">
        <text class="filter-text" :class="{ 'active-filter': selectedArea !== '全部区域' }">
          {{selectedArea}}
        </text>
        <view class="filter-arrow" :class="{ 'arrow-up': showAreaFilter }"></view>
      </view>
      
      <!-- 排序筛选 -->
      <view class="filter-item" @click="showSortFilter = true">
        <text class="filter-text" :class="{ 'active-filter': selectedSort !== '默认排序' }">
          {{selectedSort}}
        </text>
        <view class="filter-arrow" :class="{ 'arrow-up': showSortFilter }"></view>
      </view>
    </view>
    
    <!-- 已选筛选标签 -->
    <view class="selected-filters" v-if="hasActiveFilters">
      <scroll-view scroll-x class="filter-tags-scroll" show-scrollbar="false">
        <view class="filter-tags">
          <view class="filter-tag" v-if="currentCategory !== '全部'">
            {{currentCategory}} <text class="tag-close" @click="resetCategory">×</text>
          </view>
          <view class="filter-tag" v-if="selectedSubcategory !== '' && selectedSubcategory !== '全部'">
            {{selectedSubcategory}} <text class="tag-close" @click="resetSubcategory">×</text>
          </view>
          <view class="filter-tag" v-if="selectedArea !== '全部区域'">
            {{selectedArea}} <text class="tag-close" @click="resetArea">×</text>
          </view>
          <view class="filter-tag" v-if="selectedSort !== '默认排序'">
            {{selectedSort}} <text class="tag-close" @click="resetSort">×</text>
          </view>
          <view class="reset-all" @click="resetAllFilters">清除全部</view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 区域筛选弹出内容 -->
    <view class="filter-dropdown area-dropdown" v-if="showAreaFilter">
      <scroll-view scroll-y class="dropdown-scroll">
        <view class="dropdown-item" 
          v-for="(area, index) in areaList" 
          :key="index"
          :class="{ 'active-item': area === selectedArea }"
          @click="selectArea(area)">
          <text class="dropdown-item-text">{{area}}</text>
          <text class="dropdown-item-check" v-if="area === selectedArea">✓</text>
        </view>
      </scroll-view>
    </view>
    
    <!-- 排序筛选弹出内容 -->
    <view class="filter-dropdown sort-dropdown" v-if="showSortFilter">
      <view class="dropdown-item" 
        v-for="(sort, index) in sortList" 
        :key="index"
        :class="{ 'active-item': sort === selectedSort }"
        @click="selectSort(sort)">
        <text class="dropdown-item-text">{{sort}}</text>
        <text class="dropdown-item-check" v-if="sort === selectedSort">✓</text>
      </view>
    </view>
    
    <!-- 遮罩层 -->
    <view class="filter-mask" 
      v-if="showAreaFilter || showSortFilter"
      @click="closeAllFilters"></view>
    
    <!-- 内容列表 -->
    <scroll-view 
      scroll-y 
      class="business-list-container" 
      @scrolltolower="loadMore" 
      refresher-enabled 
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh">
      
      <!-- 数据统计提示 -->
      <view class="result-stats" v-if="businessList.length > 0">
        共找到 <text class="stats-number">{{businessList.length}}</text> 家商户
      </view>
      
      <view v-if="businessList.length > 0">
        <!-- 商家列表 -->
        <view 
          v-for="(item, index) in businessList" 
          :key="index" 
          class="business-item"
          @click="navigateToShopDetail(item.id)">
          <image class="business-logo" :src="item.logo" mode="aspectFill"></image>
          <view class="business-info">
            <view class="business-name-row">
              <text class="business-name">{{item.name}}</text>
              <text class="business-distance" v-if="item.distance">{{item.distance}}km</text>
            </view>
            <text class="business-desc">{{item.description}}</text>
            <view class="business-meta">
              <text class="business-category">{{item.category}}</text>
              <text class="business-subcategory" v-if="item.subcategory">{{item.subcategory}}</text>
              <text class="business-scale" v-if="item.scale">{{item.scale}}</text>
              <text class="business-area" v-if="item.area">{{item.area}}</text>
            </view>
          </view>
          <button class="follow-btn" @click.stop="followBusiness(item.id)">+ 关注</button>
        </view>
        
        <!-- 加载更多提示 -->
        <view class="loading-more" v-if="hasMore">
          <text class="loading-text">加载中...</text>
        </view>
        <view class="loading-more" v-else>
          <text class="loading-text">没有更多了</text>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view v-else class="empty-state">
        <image src="/static/images/empty.png" mode="aspectFit" class="empty-image"></image>
        <text class="empty-text">暂无相关商家</text>
        <view class="empty-tips">
          尝试调整筛选条件，或查看其他分类
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
// vue api
import { ref, computed, onMounted } from 'vue';
// uni-app api
import { onLoad, onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app';

// vue api 开始
// 状态栏高度
const statusBarHeight = ref(20);
// 页面标题
const businessTitle = ref('商家筛选');
// 搜索关键词
const searchKeyword = ref('');
// 当前选中的一级分类
const currentCategory = ref('全部');
// 当前选中的二级分类
const selectedSubcategory = ref('');
// 正在刷新
const refreshing = ref(false);
// 是否有更多数据
const hasMore = ref(true);
// 当前页码
const page = ref(1);

// 筛选弹窗显示状态
const showAreaFilter = ref(false);
const showSortFilter = ref(false);

// 当前选中的筛选条件
const selectedArea = ref('全部区域');
const selectedSort = ref('默认排序');

// 筛选选项列表
const areaList = ref(['全部区域', '城区', '磁州镇', '讲武城镇', '岳城镇', '观台镇', '白土镇', '黄沙镇']);
const categories = ref([
    { name: '全部', icon: '' },
    { name: '房产楼盘', icon: '/static/images/tabbar/房产楼盘.png' },
    { name: '美食小吃', icon: '/static/images/tabbar/美食小吃.png' },
    { name: '装修家居', icon: '/static/images/tabbar/装修家居.png' },
    { name: '母婴专区', icon: '/static/images/tabbar/母婴专区.png' },
    { name: '休闲娱乐', icon: '/static/images/tabbar/休闲娱乐.png' },
    { name: '到家服务', icon: '/static/images/tabbar/商到家服务.png' },
    { name: '开锁换锁', icon: '/static/images/tabbar/开锁换锁.png' },
    { name: '数码通讯', icon: '/static/images/tabbar/数码通讯.png' },
    { name: '车辆服务', icon: '/static/images/tabbar/商车辆服务.png' },
    { name: '教育培训', icon: '/static/images/tabbar/商教育培训.png' },
    { name: '婚纱摄影', icon: '/static/images/tabbar/婚纱摄影.png' },
    { name: '农林牧渔', icon: '/static/images/tabbar/农林牧渔.png' },
    { name: '广告传媒', icon: '/static/images/tabbar/广告传媒.png' },
    { name: '其他行业', icon: '/static/images/tabbar/其他.png' }
]);
const subcategoryMap = ref({
    '全部': [],
    '房产楼盘': ['全部', '新房', '二手房', '租房', '商铺', '办公室', '厂房', '别墅', '写字楼', '公寓'],
    '美食小吃': ['全部', '火锅', '烧烤', '蛋糕店', '小吃', '炒菜', '烤鱼', '面馆', '西餐', '日料', '快餐', '饮品店', '早餐', '烘焙'],
    '装修家居': ['全部', '全屋装修', '家具', '建材', '瓷砖', '橱柜', '卫浴', '灯饰', '窗帘', '地板', '五金', '电工', '油漆工', '水暖工'],
    '母婴专区': ['全部', '婴儿用品', '儿童玩具', '孕妇用品', '童装', '早教', '月嫂', '育儿', '孕产服务', '母婴食品'],
    '休闲娱乐': ['全部', 'KTV', '酒吧', '电影院', '咖啡厅', '茶馆', '网咖', '游乐园', '健身房', '美甲', '桌游', '棋牌室', '足浴'],
    '到家服务': ['全部', '保洁', '维修', '安装', '搬家', '洗衣', '家电维修', '管道疏通', '保姆', '月嫂', '跑腿', '家居维修'],
    '开锁换锁': ['全部', '开门锁', '开车锁', '换锁芯', '保险柜开锁', '智能锁安装', '防盗门维修', '配钥匙'],
    '数码通讯': ['全部', '手机', '电脑', '相机', '维修', '配件', '网络设备', '电子产品', '游戏设备', '办公设备'],
    '车辆服务': ['全部', '洗车', '美容', '维修', '保养', '补胎', '贴膜', '4S店', '汽车用品', '违章代办', '二手车', '汽车租赁'],
    '教育培训': ['全部', '幼儿教育', '小学辅导', '中学辅导', '高考培训', '语言培训', '音乐培训', '美术培训', '体育培训', '职业技能', '才艺培训'],
    '婚纱摄影': ['全部', '婚纱摄影', '婚礼策划', '婚车租赁', '婚礼司仪', '婚宴酒店', '婚礼跟拍', '婚戒定制', '婚纱礼服', '新娘跟妆'],
    '农林牧渔': ['全部', '农产品', '种植', '养殖', '农资', '花卉', '苗木', '水产', '畜牧', '农机', '农技服务'],
    '广告传媒': ['全部', '广告设计', '印刷', '展览展示', '标识标牌', '喷绘写真', '广告制作', '媒体投放', '摄影摄像', '网络推广'],
    '其他行业': ['全部', '劳务派遣', '招聘服务', '法律咨询', '财务服务', '物流快递', '公司注册', '保险服务', '翻译服务', '代理记账']
});
const subcategoryList = ref([]);
const sortList = ref(['默认排序', '热门优先', '距离最近', '最新加入', '评分最高']);

// 商家列表数据
const businessList = ref([
    {
        id: "1",
        logo: '/static/images/tabbar/入驻卡片.png',
        name: '五分利电器',
        description: '家电全网调货，全场特价，送货上门',
        category: '数码电器',
        subcategory: '电子产品',
        scale: '10-20人',
        area: '城区',
        distance: '1.2'
    },
    {
        id: "2",
        logo: '/static/images/tabbar/入驻卡片.png',
        name: '北方鑫雨装饰',
        description: '专业设计，精工细作，打造温馨家园',
        category: '装修家居',
        subcategory: '全屋装修',
        scale: '50-100人',
        area: '磁州镇',
        distance: '3.5'
    }
]);

// 计算属性，判断是否有活动的筛选条件
const hasActiveFilters = computed(() => {
    return currentCategory.value !== '全部' ||
           (selectedSubcategory.value !== '' && selectedSubcategory.value !== '全部') ||
           selectedArea.value !== '全部区域' ||
           selectedSort.value !== '默认排序';
});

// onMounted and onLoad
onMounted(() => {
    const sysInfo = uni.getSystemInfoSync();
    statusBarHeight.value = sysInfo.statusBarHeight || 20;
});

onLoad((options) => {
    const category = options.category;
    if (category) {
        const foundCategory = categories.value.find(c => c.name === category);
        if (foundCategory) {
            switchCategory(foundCategory);
        } else {
            // 如果路由参数中的分类不存在，则加载默认数据
            businessTitle.value = '全部商家';
            fetchBusinessData(true);
        }
    } else {
        // 如果没有路由参数，则加载默认数据
        businessTitle.value = '全部商家';
        fetchBusinessData(true);
    }
});

// 页面下拉刷新
onPullDownRefresh(() => {
    onRefresh();
});

// 页面上拉触底
onReachBottom(() => {
    loadMore();
});

// 方法
const navigateBack = () => {
    uni.navigateBack();
};

const searchBusinesses = () => {
    console.log('开始搜索:', searchKeyword.value);
    resetAndFetch();
};

const switchCategory = (item) => {
    if (currentCategory.value === item.name) return;

    currentCategory.value = item.name;
    businessTitle.value = item.name === '全部' ? '全部商家' : item.name;
    selectedSubcategory.value = ''; // 重置子分类
    
    // 更新子分类列表
    subcategoryList.value = subcategoryMap.value[item.name] || [];
    if (subcategoryList.value.length > 0) {
        selectedSubcategory.value = '全部';
    }
    
    resetAndFetch();
};

const selectSubcategory = (subcat) => {
    if (selectedSubcategory.value === subcat) return;
    selectedSubcategory.value = subcat;
    resetAndFetch();
};

const selectArea = (area) => {
    selectedArea.value = area;
    showAreaFilter.value = false;
    resetAndFetch();
};

const selectSort = (sort) => {
    selectedSort.value = sort;
    showSortFilter.value = false;
    resetAndFetch();
};

const closeAllFilters = () => {
    showAreaFilter.value = false;
    showSortFilter.value = false;
};

const resetCategory = () => {
    switchCategory({ name: '全部' });
};

const resetSubcategory = () => {
    selectedSubcategory.value = '全部';
    resetAndFetch();
};

const resetArea = () => {
    selectArea('全部区域');
};

const resetSort = () => {
    selectSort('默认排序');
};

const resetAllFilters = () => {
    currentCategory.value = '全部';
    businessTitle.value = '全部商家';
    selectedSubcategory.value = '';
    subcategoryList.value = [];
    selectedArea.value = '全部区域';
    selectedSort.value = '默认排序';
    searchKeyword.value = '';
    closeAllFilters();
    resetAndFetch();
};

const fetchBusinessData = (isRefresh = false) => {
    if (isRefresh) {
        page.value = 1;
        businessList.value = [];
        hasMore.value = true;
    }

    if (!hasMore.value) {
        if (refreshing.value) uni.stopPullDownRefresh();
        return;
    }
    
    console.log('Fetching data...', {
        page: page.value,
        keyword: searchKeyword.value,
        category: currentCategory.value,
        subcategory: selectedSubcategory.value,
        area: selectedArea.value,
        sort: selectedSort.value
    });
    
    // 模拟API请求
    setTimeout(() => {
        const mockData = [
            { id: "1", logo: '/static/images/tabbar/入驻卡片.png', name: '五分利电器', description: '家电全网调货，全场特价，送货上门', category: '数码电器', subcategory: '电子产品', scale: '10-20人', area: '城区', distance: '1.2' },
            { id: "2", logo: '/static/images/tabbar/入驻卡片.png', name: '北方鑫雨装饰', description: '专业设计，精工细作，打造温馨家园', category: '装修家居', subcategory: '全屋装修', scale: '50-100人', area: '磁州镇', distance: '3.5' },
            // 更多模拟数据...
        ];
        
        // 模拟分页
        if (page.value > 2) {
            businessList.value = businessList.value.concat([]); // 加载空数组
            hasMore.value = false;
        } else {
            businessList.value = isRefresh ? mockData : businessList.value.concat(mockData);
            hasMore.value = true;
        }

        page.value++;
        if (refreshing.value) {
            uni.stopPullDownRefresh();
            refreshing.value = false;
        }
    }, 1000);
};

const resetAndFetch = () => {
    fetchBusinessData(true);
};

const onRefresh = () => {
    if (refreshing.value) return;
    refreshing.value = true;
    fetchBusinessData(true);
};

const loadMore = () => {
    fetchBusinessData();
};

const navigateToShopDetail = (id) => {
    uni.navigateTo({
        url: `/pages/business/shop-detail?id=${id}`
    });
};

const followBusiness = (id) => {
    console.log('关注商家:', id);
    uni.showToast({
        title: '关注成功',
        icon: 'success'
    });
};
// vue api 结尾
</script>

<style scoped>
.business-filter-container {
  background-color: #f5f7fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Helvetica, Arial, sans-serif;
}

/* 导航栏样式 */
.custom-navbar {
  display: flex;
  align-items: center;
  height: 44px;
  position: relative;
  background-color: #fff;
  z-index: 99;
  border-bottom: 1px solid #f0f0f0;
}

.back-btn {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24px;
  height: 24px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M15 18l-6-6 6-6'/%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 700;
  color: #333;
}

.navbar-right {
  width: 44px;
  height: 44px;
}

/* 搜索框样式 */
.search-container {
  padding: 15rpx 30rpx;
  background-color: #fff;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 16rpx;
  padding: 10rpx 20rpx;
}

.search-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.search-input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
  color: #333;
}

.search-btn {
  padding: 6rpx 20rpx;
  background: linear-gradient(to right, #007AFF, #5AC8FA);
  color: #fff;
  font-size: 26rpx;
  border-radius: 12rpx;
  margin-left: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}

/* 分类标签样式 */
.top-category-tabs {
  white-space: nowrap;
  background-color: #fff;
  padding: 15rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.top-category-item {
  display: inline-block;
  padding: 15rpx 30rpx;
  font-size: 28rpx;
  color: #333;
  position: relative;
}

.active-top-category {
  color: #007AFF;
  font-weight: 500;
}

.active-top-category::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #007AFF;
  border-radius: 2rpx;
}

/* 子分类标签栏 */
.subcategory-tabs {
  white-space: nowrap;
  background-color: #f7f9ff;
  padding: 15rpx 0;
  border-bottom: 1px solid #e6eeff;
}

.subcategory-item {
  display: inline-block;
  padding: 10rpx 24rpx;
  font-size: 26rpx;
  color: #666;
  margin: 0 8rpx;
  border-radius: 24rpx;
  transition: all 0.3s ease;
}

.active-subcategory {
  color: #007AFF;
  font-weight: 500;
  background-color: rgba(0, 122, 255, 0.1);
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.1);
}

.active-subcategory::after {
  display: none;
}

/* 筛选条件样式 */
.filter-section {
  display: flex;
  padding: 20rpx 0;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  z-index: 10;
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.filter-text {
  font-size: 26rpx;
  color: #666;
}

.active-filter {
  color: #007AFF;
  font-weight: 500;
}

.filter-arrow {
  width: 0;
  height: 0;
  border-left: 6rpx solid transparent;
  border-right: 6rpx solid transparent;
  border-top: 6rpx solid #999;
  margin-left: 6rpx;
  transition: transform 0.3s;
}

.arrow-up {
  transform: rotate(180deg);
}

/* 已选筛选标签样式 */
.selected-filters {
  background-color: #fff;
  padding: 10rpx 20rpx;
  border-bottom: 1px solid #f0f0f0;
}

.filter-tags-scroll {
  white-space: nowrap;
}

.filter-tags {
  display: inline-flex;
  align-items: center;
}

.filter-tag {
  display: inline-block;
  padding: 6rpx 16rpx;
  background-color: rgba(0, 122, 255, 0.1);
  color: #007AFF;
  font-size: 24rpx;
  border-radius: 12rpx;
  margin-right: 15rpx;
}

.tag-close {
  display: inline-block;
  margin-left: 6rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.reset-all {
  display: inline-block;
  padding: 6rpx 16rpx;
  color: #999;
  font-size: 24rpx;
  border: 1px solid #eee;
  border-radius: 12rpx;
}

/* 筛选下拉内容样式 */
.filter-dropdown {
  position: absolute;
  top: 160rpx;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 11;
  max-height: 600rpx;
  overflow-y: auto;
}

.area-dropdown, .sort-dropdown {
  max-height: 400rpx;
}

.dropdown-item {
  padding: 24rpx 30rpx;
  font-size: 28rpx;
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f5f5f5;
}

.title-item {
  background-color: #f8f8f8;
  font-weight: 500;
}

.dropdown-title {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
}

.active-item {
  color: #007AFF;
}

.dropdown-item-check {
  color: #007AFF;
  font-weight: bold;
}

.dropdown-scroll {
  max-height: 550rpx;
}

/* 遮罩层样式 */
.filter-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 9;
}

/* 商家列表样式 */
.business-list-container {
  flex: 1;
  background-color: #f5f7fa;
}

.result-stats {
  padding: 20rpx 30rpx;
  font-size: 24rpx;
  color: #999;
}

.stats-number {
  color: #007AFF;
  font-weight: 500;
}

.business-item {
  margin: 20rpx 30rpx;
  background-color: #fff;
  border-radius: 24rpx;
  padding: 24rpx;
  display: flex;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.business-logo {
  width: 100rpx;
  height: 100rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}

.business-info {
  flex: 1;
  overflow: hidden;
}

.business-name-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.business-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.business-distance {
  font-size: 24rpx;
  color: #999;
}

.business-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.business-meta {
  display: flex;
  align-items: center;
}

.business-category, .business-subcategory, .business-scale, .business-area {
  font-size: 22rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  margin-right: 10rpx;
}

.business-subcategory {
  background-color: rgba(0, 122, 255, 0.1);
  color: #007AFF;
  border: 1px solid rgba(0, 122, 255, 0.1);
}

.follow-btn {
  position: absolute;
  right: 24rpx;
  bottom: 24rpx;
  background: linear-gradient(to right, #007AFF, #5AC8FA);
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 24rpx;
  border: none;
  line-height: 1.5;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}

/* 加载更多样式 */
.loading-more {
  text-align: center;
  padding: 30rpx 0;
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.empty-tips {
  font-size: 26rpx;
  color: #999;
}
</style> 