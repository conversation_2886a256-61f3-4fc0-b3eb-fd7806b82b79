{"version": 3, "file": "birthday-privilege.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/member/birthday-privilege.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xtZW1iZXJcYmlydGhkYXktcHJpdmlsZWdlLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"birthday-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">生日特权</text>\r\n      <view class=\"navbar-right\"></view>\r\n    </view>\r\n    \r\n    <!-- 生日特权内容 -->\r\n    <view class=\"privilege-content\">\r\n      <!-- 生日特权开关 -->\r\n      <view class=\"section-card\">\r\n        <view class=\"switch-item\">\r\n          <view class=\"switch-content\">\r\n            <text class=\"switch-title\">生日特权</text>\r\n            <text class=\"switch-desc\">开启后，会员在生日当月可享受专属特权</text>\r\n          </view>\r\n          <switch :checked=\"birthdaySettings.enabled\" @change=\"toggleBirthdayPrivilege\" color=\"#4A00E0\" />\r\n        </view>\r\n      </view>\r\n      \r\n      <block v-if=\"birthdaySettings.enabled\">\r\n        <!-- 生日特权设置 -->\r\n        <view class=\"section-card\">\r\n          <view class=\"section-title\">特权设置</view>\r\n          \r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">特权有效期</text>\r\n            <view class=\"radio-group\">\r\n              <view class=\"radio-item\" :class=\"{ active: birthdaySettings.validPeriod === 'day' }\" @click=\"setValidPeriod('day')\">\r\n                <text class=\"radio-text\">生日当天</text>\r\n              </view>\r\n              <view class=\"radio-item\" :class=\"{ active: birthdaySettings.validPeriod === 'week' }\" @click=\"setValidPeriod('week')\">\r\n                <text class=\"radio-text\">生日当周</text>\r\n              </view>\r\n              <view class=\"radio-item\" :class=\"{ active: birthdaySettings.validPeriod === 'month' }\" @click=\"setValidPeriod('month')\">\r\n                <text class=\"radio-text\">生日当月</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">提前通知</text>\r\n            <view class=\"form-input-group\">\r\n              <input type=\"number\" class=\"form-input\" v-model=\"birthdaySettings.notifyDays\" />\r\n              <text class=\"input-suffix\">天</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"form-item switch-item\">\r\n            <text class=\"form-label\">短信通知</text>\r\n            <switch :checked=\"birthdaySettings.smsNotify\" @change=\"toggleSmsNotify\" color=\"#4A00E0\" />\r\n          </view>\r\n          \r\n          <view class=\"form-item switch-item\">\r\n            <text class=\"form-label\">微信通知</text>\r\n            <switch :checked=\"birthdaySettings.wechatNotify\" @change=\"toggleWechatNotify\" color=\"#4A00E0\" />\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 生日礼包设置 -->\r\n        <view class=\"section-card\">\r\n          <view class=\"section-title\">生日礼包</view>\r\n          \r\n          <view class=\"privilege-list\">\r\n            <view class=\"privilege-item\" v-for=\"(privilege, index) in birthdayPrivileges\" :key=\"index\">\r\n              <view class=\"privilege-checkbox\" :class=\"{ checked: privilege.selected }\" @click=\"togglePrivilege(privilege)\">\r\n                <view class=\"checkbox-inner\" v-if=\"privilege.selected\"></view>\r\n              </view>\r\n              <view class=\"privilege-content\">\r\n                <text class=\"privilege-name\">{{privilege.name}}</text>\r\n                <text class=\"privilege-desc\">{{privilege.description}}</text>\r\n              </view>\r\n              <view class=\"privilege-config\" @click=\"configPrivilege(privilege)\">\r\n                <text class=\"config-text\">设置</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <button class=\"add-btn\" @click=\"addPrivilege\">添加生日特权</button>\r\n        </view>\r\n        \r\n        <!-- 适用会员等级 -->\r\n        <view class=\"section-card\">\r\n          <view class=\"section-title\">适用会员等级</view>\r\n          \r\n          <view class=\"level-list\">\r\n            <view class=\"level-item\" v-for=\"(level, index) in memberLevels\" :key=\"index\">\r\n              <view class=\"level-checkbox\" :class=\"{ checked: level.selected }\" @click=\"toggleLevel(level)\">\r\n                <view class=\"checkbox-inner\" v-if=\"level.selected\"></view>\r\n              </view>\r\n              <view class=\"level-content\">\r\n                <text class=\"level-name\">{{level.name}}</text>\r\n                <text class=\"level-desc\">{{level.memberCount}}名会员</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 生日祝福语 -->\r\n        <view class=\"section-card\">\r\n          <view class=\"section-title\">生日祝福语</view>\r\n          \r\n          <view class=\"form-item\">\r\n            <textarea class=\"form-textarea\" v-model=\"birthdaySettings.greetingText\" placeholder=\"请输入生日祝福语\" />\r\n          </view>\r\n          \r\n          <view class=\"greeting-preview\">\r\n            <view class=\"preview-title\">预览效果</view>\r\n            <view class=\"preview-card\">\r\n              <view class=\"preview-header\">\r\n                <image class=\"preview-logo\" src=\"/static/images/logo.png\" mode=\"aspectFit\"></image>\r\n                <text class=\"preview-shop-name\">磁州生活网</text>\r\n              </view>\r\n              <view class=\"preview-content\">\r\n                <text class=\"preview-greeting\">{{birthdaySettings.greetingText || '亲爱的会员，祝您生日快乐！我们为您准备了专属生日礼包，点击查看详情。'}}</text>\r\n              </view>\r\n              <view class=\"preview-footer\">\r\n                <text class=\"preview-btn\">查看生日礼包</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </block>\r\n    </view>\r\n    \r\n    <!-- 保存按钮 -->\r\n    <view class=\"bottom-bar\" v-if=\"birthdaySettings.enabled\">\r\n      <button class=\"save-btn\" @click=\"saveSettings\">保存设置</button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 生日特权设置\r\n      birthdaySettings: {\r\n        enabled: true,\r\n        validPeriod: 'month',\r\n        notifyDays: 3,\r\n        smsNotify: true,\r\n        wechatNotify: true,\r\n        greetingText: '亲爱的会员，祝您生日快乐！我们为您准备了专属生日礼包，点击查看详情。'\r\n      },\r\n      \r\n      // 生日特权列表\r\n      birthdayPrivileges: [\r\n        {\r\n          id: 1,\r\n          name: '生日优惠券',\r\n          description: '赠送生日专属优惠券',\r\n          selected: true\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '积分翻倍',\r\n          description: '生日期间消费积分翻倍',\r\n          selected: true\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '生日礼品',\r\n          description: '到店领取生日礼品',\r\n          selected: false\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '专属折扣',\r\n          description: '生日当天消费享受额外折扣',\r\n          selected: true\r\n        },\r\n        {\r\n          id: 5,\r\n          name: '免费配送',\r\n          description: '生日期间订单免费配送',\r\n          selected: false\r\n        }\r\n      ],\r\n      \r\n      // 会员等级\r\n      memberLevels: [\r\n        {\r\n          id: 1,\r\n          name: '普通会员',\r\n          memberCount: 2156,\r\n          selected: false\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '银卡会员',\r\n          memberCount: 864,\r\n          selected: true\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '金卡会员',\r\n          memberCount: 426,\r\n          selected: true\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '钻石会员',\r\n          memberCount: 116,\r\n          selected: true\r\n        }\r\n      ]\r\n    };\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    \r\n    toggleBirthdayPrivilege(e) {\r\n      this.birthdaySettings.enabled = e.detail.value;\r\n    },\r\n    \r\n    setValidPeriod(period) {\r\n      this.birthdaySettings.validPeriod = period;\r\n    },\r\n    \r\n    toggleSmsNotify(e) {\r\n      this.birthdaySettings.smsNotify = e.detail.value;\r\n    },\r\n    \r\n    toggleWechatNotify(e) {\r\n      this.birthdaySettings.wechatNotify = e.detail.value;\r\n    },\r\n    \r\n    togglePrivilege(privilege) {\r\n      const index = this.birthdayPrivileges.findIndex(item => item.id === privilege.id);\r\n      if (index !== -1) {\r\n        this.birthdayPrivileges[index].selected = !this.birthdayPrivileges[index].selected;\r\n      }\r\n    },\r\n    \r\n    configPrivilege(privilege) {\r\n      uni.showToast({\r\n        title: `${privilege.name}设置功能开发中`,\r\n        icon: 'none'\r\n      });\r\n    },\r\n    \r\n    addPrivilege() {\r\n      uni.showToast({\r\n        title: '添加特权功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    \r\n    toggleLevel(level) {\r\n      const index = this.memberLevels.findIndex(item => item.id === level.id);\r\n      if (index !== -1) {\r\n        this.memberLevels[index].selected = !this.memberLevels[index].selected;\r\n      }\r\n    },\r\n    \r\n    saveSettings() {\r\n      uni.showLoading({\r\n        title: '保存中...'\r\n      });\r\n      \r\n      setTimeout(() => {\r\n        uni.hideLoading();\r\n        uni.showToast({\r\n          title: '生日特权设置保存成功',\r\n          icon: 'success'\r\n        });\r\n      }, 1000);\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n/* 生日特权页面样式开始 */\r\n.birthday-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: 100rpx;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #8E2DE2, #4A00E0);\r\n  color: #fff;\r\n  padding: 44px 16px 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 2px 10px rgba(74, 0, 224, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.navbar-right {\r\n  width: 36px;\r\n  height: 36px;\r\n}\r\n\r\n/* 特权内容样式 */\r\n.privilege-content {\r\n  padding: 20rpx;\r\n}\r\n\r\n.section-card {\r\n  background: #fff;\r\n  border-radius: 12rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.section-title {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 20rpx;\r\n  padding-bottom: 15rpx;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n/* 开关样式 */\r\n.switch-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.switch-content {\r\n  flex: 1;\r\n}\r\n\r\n.switch-title {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 8rpx;\r\n  display: block;\r\n}\r\n\r\n.switch-desc {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n/* 表单样式 */\r\n.form-item {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.form-label {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  margin-bottom: 10rpx;\r\n  display: block;\r\n}\r\n\r\n.form-input-group {\r\n  display: flex;\r\n  align-items: center;\r\n  border: 1rpx solid #ddd;\r\n  border-radius: 8rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.form-input {\r\n  flex: 1;\r\n  height: 80rpx;\r\n  padding: 0 20rpx;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.input-suffix {\r\n  padding: 0 20rpx;\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  background: #f5f5f5;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n}\r\n\r\n.form-textarea {\r\n  width: 100%;\r\n  height: 160rpx;\r\n  border: 1rpx solid #ddd;\r\n  border-radius: 8rpx;\r\n  padding: 20rpx;\r\n  font-size: 28rpx;\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* 单选按钮组 */\r\n.radio-group {\r\n  display: flex;\r\n  gap: 20rpx;\r\n}\r\n\r\n.radio-item {\r\n  flex: 1;\r\n  height: 80rpx;\r\n  border: 1rpx solid #ddd;\r\n  border-radius: 8rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f9f9f9;\r\n}\r\n\r\n.radio-item.active {\r\n  background: rgba(74, 0, 224, 0.1);\r\n  border-color: #4A00E0;\r\n}\r\n\r\n.radio-text {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n}\r\n\r\n.radio-item.active .radio-text {\r\n  color: #4A00E0;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 特权列表样式 */\r\n.privilege-list {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.privilege-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20rpx 0;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.privilege-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.privilege-checkbox {\r\n  width: 36rpx;\r\n  height: 36rpx;\r\n  border: 1rpx solid #ddd;\r\n  border-radius: 50%;\r\n  margin-right: 20rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.privilege-checkbox.checked {\r\n  border-color: #4A00E0;\r\n  background: #4A00E0;\r\n}\r\n\r\n.checkbox-inner {\r\n  width: 18rpx;\r\n  height: 18rpx;\r\n  border-radius: 50%;\r\n  background: #fff;\r\n}\r\n\r\n.privilege-content {\r\n  flex: 1;\r\n}\r\n\r\n.privilege-name {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  margin-bottom: 8rpx;\r\n  display: block;\r\n}\r\n\r\n.privilege-desc {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.privilege-config {\r\n  padding: 6rpx 16rpx;\r\n  border-radius: 30rpx;\r\n  background: rgba(74, 0, 224, 0.1);\r\n}\r\n\r\n.config-text {\r\n  font-size: 24rpx;\r\n  color: #4A00E0;\r\n}\r\n\r\n/* 会员等级列表 */\r\n.level-list {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.level-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20rpx 0;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.level-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.level-checkbox {\r\n  width: 36rpx;\r\n  height: 36rpx;\r\n  border: 1rpx solid #ddd;\r\n  border-radius: 50%;\r\n  margin-right: 20rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.level-checkbox.checked {\r\n  border-color: #4A00E0;\r\n  background: #4A00E0;\r\n}\r\n\r\n.level-content {\r\n  flex: 1;\r\n}\r\n\r\n.level-name {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  margin-bottom: 8rpx;\r\n  display: block;\r\n}\r\n\r\n.level-desc {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n/* 祝福语预览 */\r\n.greeting-preview {\r\n  margin-top: 30rpx;\r\n}\r\n\r\n.preview-title {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  margin-bottom: 15rpx;\r\n}\r\n\r\n.preview-card {\r\n  border: 1rpx solid #f0f0f0;\r\n  border-radius: 12rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.preview-header {\r\n  background: #4A00E0;\r\n  padding: 20rpx;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.preview-logo {\r\n  width: 48rpx;\r\n  height: 48rpx;\r\n  border-radius: 8rpx;\r\n  margin-right: 15rpx;\r\n}\r\n\r\n.preview-shop-name {\r\n  font-size: 28rpx;\r\n  color: #fff;\r\n  font-weight: 600;\r\n}\r\n\r\n.preview-content {\r\n  padding: 30rpx;\r\n  background: #fff;\r\n}\r\n\r\n.preview-greeting {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  line-height: 1.6;\r\n}\r\n\r\n.preview-footer {\r\n  padding: 20rpx;\r\n  display: flex;\r\n  justify-content: center;\r\n  border-top: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.preview-btn {\r\n  font-size: 28rpx;\r\n  color: #4A00E0;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 添加按钮 */\r\n.add-btn {\r\n  background: #4A00E0;\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 40rpx;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n  font-size: 28rpx;\r\n  margin-top: 20rpx;\r\n}\r\n\r\n/* 底部保存栏 */\r\n.bottom-bar {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: #fff;\r\n  padding: 20rpx;\r\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.save-btn {\r\n  background: #4A00E0;\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 40rpx;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n  font-size: 28rpx;\r\n  width: 100%;\r\n}\r\n/* 生日特权页面样式结束 */\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/member/birthday-privilege.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAyIA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA;AAAA,MAEL,kBAAkB;AAAA,QAChB,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,cAAc;AAAA,QACd,cAAc;AAAA,MACf;AAAA;AAAA,MAGD,oBAAoB;AAAA,QAClB;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,QACX;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,QACX;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,QACX;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,QACX;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,QACZ;AAAA,MACD;AAAA;AAAA,MAGD,cAAc;AAAA,QACZ;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,QACX;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,QACX;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,QACX;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,QACZ;AAAA,MACF;AAAA;EAEH;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IAED,wBAAwB,GAAG;AACzB,WAAK,iBAAiB,UAAU,EAAE,OAAO;AAAA,IAC1C;AAAA,IAED,eAAe,QAAQ;AACrB,WAAK,iBAAiB,cAAc;AAAA,IACrC;AAAA,IAED,gBAAgB,GAAG;AACjB,WAAK,iBAAiB,YAAY,EAAE,OAAO;AAAA,IAC5C;AAAA,IAED,mBAAmB,GAAG;AACpB,WAAK,iBAAiB,eAAe,EAAE,OAAO;AAAA,IAC/C;AAAA,IAED,gBAAgB,WAAW;AACzB,YAAM,QAAQ,KAAK,mBAAmB,UAAU,UAAQ,KAAK,OAAO,UAAU,EAAE;AAChF,UAAI,UAAU,IAAI;AAChB,aAAK,mBAAmB,KAAK,EAAE,WAAW,CAAC,KAAK,mBAAmB,KAAK,EAAE;AAAA,MAC5E;AAAA,IACD;AAAA,IAED,gBAAgB,WAAW;AACzBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,GAAG,UAAU,IAAI;AAAA,QACxB,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IAED,eAAe;AACbA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IAED,YAAY,OAAO;AACjB,YAAM,QAAQ,KAAK,aAAa,UAAU,UAAQ,KAAK,OAAO,MAAM,EAAE;AACtE,UAAI,UAAU,IAAI;AAChB,aAAK,aAAa,KAAK,EAAE,WAAW,CAAC,KAAK,aAAa,KAAK,EAAE;AAAA,MAChE;AAAA,IACD;AAAA,IAED,eAAe;AACbA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACF,GAAE,GAAI;AAAA,IACT;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnRA,GAAG,WAAW,eAAe;"}