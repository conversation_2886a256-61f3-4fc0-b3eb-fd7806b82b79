@echo off
chcp 65001 >nul

echo 🛑 停止磁州生活网后台管理系统

REM 检查当前目录
if not exist "backend" (
    echo ❌ 请在项目根目录（cizhou-admin）下运行此脚本
    pause
    exit /b 1
)

REM 停止Docker服务
echo 📦 停止后端Docker服务...
cd backend

if exist "docker-compose.dev.yml" (
    docker-compose -f docker-compose.dev.yml down
    echo ✅ Docker服务已停止
) else (
    echo ⚠️  docker-compose.dev.yml文件不存在
)

REM 清理Docker资源（可选）
set /p cleanup="是否清理Docker数据卷？这将删除所有数据 (y/N): "
if /i "%cleanup%"=="y" (
    echo 🧹 清理Docker数据卷...
    docker-compose -f docker-compose.dev.yml down -v
    docker system prune -f
    echo ✅ Docker数据卷已清理
)

REM 停止Java进程
echo 🔍 检查Java进程...
tasklist /FI "IMAGENAME eq java.exe" /FO CSV | find "java.exe" >nul
if not errorlevel 1 (
    echo 🛑 停止Java进程...
    taskkill /F /IM java.exe >nul 2>&1
    echo ✅ Java进程已停止
) else (
    echo ✅ 没有发现运行中的Java进程
)

REM 停止Node.js进程
echo 🔍 检查Node.js进程...
tasklist /FI "IMAGENAME eq node.exe" /FO CSV | find "node.exe" >nul
if not errorlevel 1 (
    echo 🛑 停止Node.js进程...
    taskkill /F /IM node.exe >nul 2>&1
    echo ✅ Node.js进程已停止
) else (
    echo ✅ 没有发现运行中的Node.js进程
)

cd ..

echo.
echo 🎉 系统已完全停止！
echo.
echo 📋 已停止的服务：
echo    ✅ MySQL数据库
echo    ✅ Redis缓存
echo    ✅ Nacos注册中心
echo    ✅ 后端Java服务
echo    ✅ 前端Node.js服务
echo.
echo 💡 如需重新启动，请运行：
echo    start-all.bat

pause
