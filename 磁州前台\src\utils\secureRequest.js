/**
 * 安全的API请求工具
 */
import { encryptData, decryptData } from './securityUtils';

// 基础URL，应从环境变量获取
const BASE_URL = process.env.VUE_APP_API_BASE_URL || '';

// 请求拦截器
const requestInterceptor = (config) => {
  // 添加时间戳防止缓存
  const timestamp = new Date().getTime();
  
  // 获取token
  const token = uni.getStorageSync('token') || '';
  
  // 设置请求头
  config.header = {
    ...config.header,
    'Authorization': token ? `Bearer ${token}` : '',
    'X-Timestamp': timestamp,
    'X-Client-Version': process.env.VUE_APP_VERSION || '1.0.0',
    'Content-Type': 'application/json'
  };
  
  // 对敏感数据进行加密
  if (config.secureData && Object.keys(config.secureData).length > 0) {
    config.data = {
      ...config.data,
      securePayload: encryptData(config.secureData)
    };
    
    // 删除原始敏感数据
    delete config.secureData;
  }
  
  return config;
};

// 响应拦截器
const responseInterceptor = (response) => {
  // 检查是否有加密数据
  if (response.data && response.data.encryptedData) {
    try {
      // 解密数据
      response.data.decryptedData = decryptData(response.data.encryptedData);
      delete response.data.encryptedData;
    } catch (error) {
      console.error('数据解密失败', error);
    }
  }
  
  return response;
};

// 错误处理
const handleError = (error) => {
  // 处理不同类型的错误
  if (error.statusCode === 401) {
    // 未授权，清除登录状态并跳转登录页
    uni.removeStorageSync('token');
    uni.showToast({
      title: '登录已过期，请重新登录',
      icon: 'none'
    });
    
    setTimeout(() => {
      uni.navigateTo({
        url: '/pages/login/index'
      });
    }, 1500);
  } else if (error.statusCode === 403) {
    uni.showToast({
      title: '您没有权限执行此操作',
      icon: 'none'
    });
  } else {
    uni.showToast({
      title: error.errMsg || '请求失败',
      icon: 'none'
    });
  }
  
  return Promise.reject(error);
};

/**
 * 安全请求方法
 * @param {Object} options 请求配置
 * @returns {Promise} 请求Promise
 */
const secureRequest = (options = {}) => {
  // 应用请求拦截器
  const config = requestInterceptor(options);
  
  // 构建完整URL
  const url = /^(http|https):\/\//.test(config.url) 
    ? config.url 
    : `${BASE_URL}${config.url}`;
  
  return new Promise((resolve, reject) => {
    uni.request({
      ...config,
      url,
      success: (res) => {
        // 应用响应拦截器
        const response = responseInterceptor(res);
        
        // 处理业务状态码
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(response.data);
        } else {
          handleError(response).catch(reject);
        }
      },
      fail: (err) => {
        handleError(err).catch(reject);
      }
    });
  });
};

// 导出请求方法
export default {
  get: (url, data = {}, options = {}) => {
    return secureRequest({
      url,
      data,
      method: 'GET',
      ...options
    });
  },
  
  post: (url, data = {}, options = {}) => {
    return secureRequest({
      url,
      data,
      method: 'POST',
      ...options
    });
  },
  
  put: (url, data = {}, options = {}) => {
    return secureRequest({
      url,
      data,
      method: 'PUT',
      ...options
    });
  },
  
  delete: (url, data = {}, options = {}) => {
    return secureRequest({
      url,
      data,
      method: 'DELETE',
      ...options
    });
  }
}; 