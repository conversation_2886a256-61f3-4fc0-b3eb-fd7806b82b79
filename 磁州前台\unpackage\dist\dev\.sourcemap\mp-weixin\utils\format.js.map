{"version": 3, "file": "format.js", "sources": ["utils/format.js"], "sourcesContent": ["/**\r\n * 格式化工具函数\r\n */\r\n\r\n/**\r\n * 格式化时间\r\n * @param {string|number|Date} time 时间\r\n * @param {string} format 格式化模式\r\n * @returns {string} 格式化后的时间字符串\r\n */\r\nexport function formatTime(time, format = 'YYYY-MM-DD HH:mm:ss') {\r\n  if (!time) return '';\r\n  \r\n  const date = new Date(time);\r\n  if (isNaN(date.getTime())) return '';\r\n  \r\n  const year = date.getFullYear();\r\n  const month = date.getMonth() + 1;\r\n  const day = date.getDate();\r\n  const hour = date.getHours();\r\n  const minute = date.getMinutes();\r\n  const second = date.getSeconds();\r\n  \r\n  return format\r\n    .replace('YYYY', year)\r\n    .replace('MM', month.toString().padStart(2, '0'))\r\n    .replace('DD', day.toString().padStart(2, '0'))\r\n    .replace('HH', hour.toString().padStart(2, '0'))\r\n    .replace('mm', minute.toString().padStart(2, '0'))\r\n    .replace('ss', second.toString().padStart(2, '0'));\r\n}\r\n\r\n/**\r\n * 格式化金额\r\n * @param {number} amount 金额\r\n * @param {number} decimals 小数位数\r\n * @returns {string} 格式化后的金额字符串\r\n */\r\nexport function formatAmount(amount, decimals = 2) {\r\n  if (typeof amount !== 'number') return '0.00';\r\n  return amount.toFixed(decimals);\r\n}\r\n\r\n/**\r\n * 格式化剩余时间\r\n * @param {string|number|Date} endTime 结束时间\r\n * @returns {string} 格式化后的剩余时间字符串\r\n */\r\nexport function formatRemainTime(endTime) {\r\n  if (!endTime) return '';\r\n  \r\n  const end = new Date(endTime).getTime();\r\n  const now = Date.now();\r\n  const diff = end - now;\r\n  \r\n  if (diff <= 0) return '已结束';\r\n  \r\n  const days = Math.floor(diff / (1000 * 60 * 60 * 24));\r\n  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));\r\n  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\r\n  \r\n  if (days > 0) {\r\n    return `${days}天${hours}小时`;\r\n  } else if (hours > 0) {\r\n    return `${hours}小时${minutes}分钟`;\r\n  } else {\r\n    return `${minutes}分钟`;\r\n  }\r\n}\r\n\r\n/**\r\n * 格式化数字（添加千分位）\r\n * @param {number} num 数字\r\n * @returns {string} 格式化后的数字字符串\r\n */\r\nexport function formatNumber(num) {\r\n  if (typeof num !== 'number') return '0';\r\n  return num.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n}\r\n\r\n/**\r\n * 格式化文件大小\r\n * @param {number} bytes 字节数\r\n * @returns {string} 格式化后的文件大小字符串\r\n */\r\nexport function formatFileSize(bytes) {\r\n  if (bytes === 0) return '0 B';\r\n  \r\n  const k = 1024;\r\n  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n  \r\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n}\r\n\r\n/**\r\n * 格式化距离\r\n * @param {number} meters 距离（米）\r\n * @returns {string} 格式化后的距离字符串\r\n */\r\nexport function formatDistance(meters) {\r\n  if (typeof meters !== 'number') return '';\r\n  \r\n  if (meters < 1000) {\r\n    return `${Math.round(meters)}米`;\r\n  } else {\r\n    return `${(meters / 1000).toFixed(1)}公里`;\r\n  }\r\n} "], "names": [], "mappings": ";AAUO,SAAS,WAAW,MAAM,SAAS,uBAAuB;AAC/D,MAAI,CAAC;AAAM,WAAO;AAElB,QAAM,OAAO,IAAI,KAAK,IAAI;AAC1B,MAAI,MAAM,KAAK,QAAO,CAAE;AAAG,WAAO;AAElC,QAAM,OAAO,KAAK;AAClB,QAAM,QAAQ,KAAK,SAAQ,IAAK;AAChC,QAAM,MAAM,KAAK;AACjB,QAAM,OAAO,KAAK;AAClB,QAAM,SAAS,KAAK;AACpB,QAAM,SAAS,KAAK;AAEpB,SAAO,OACJ,QAAQ,QAAQ,IAAI,EACpB,QAAQ,MAAM,MAAM,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC,EAC/C,QAAQ,MAAM,IAAI,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC,EAC7C,QAAQ,MAAM,KAAK,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC,EAC9C,QAAQ,MAAM,OAAO,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC,EAChD,QAAQ,MAAM,OAAO,SAAQ,EAAG,SAAS,GAAG,GAAG,CAAC;AACrD;AAQO,SAAS,aAAa,QAAQ,WAAW,GAAG;AACjD,MAAI,OAAO,WAAW;AAAU,WAAO;AACvC,SAAO,OAAO,QAAQ,QAAQ;AAChC;AAkCO,SAAS,aAAa,KAAK;AAChC,MAAI,OAAO,QAAQ;AAAU,WAAO;AACpC,SAAO,IAAI,SAAU,EAAC,QAAQ,yBAAyB,GAAG;AAC5D;AAsBO,SAAS,eAAe,QAAQ;AACrC,MAAI,OAAO,WAAW;AAAU,WAAO;AAEvC,MAAI,SAAS,KAAM;AACjB,WAAO,GAAG,KAAK,MAAM,MAAM,CAAC;AAAA,EAChC,OAAS;AACL,WAAO,IAAI,SAAS,KAAM,QAAQ,CAAC,CAAC;AAAA,EACrC;AACH;;;;;"}