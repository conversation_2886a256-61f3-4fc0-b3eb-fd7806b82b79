<template>
  <view class="favorites-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">收藏/关注</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 顶部选项卡 -->
    <view class="tabs-container" :style="{ top: navbarHeight + 'px' }">
      <view 
        class="tab-item" 
        v-for="(tab, index) in tabs" 
        :key="index"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        <text class="tab-text">{{ tab.name }}</text>
      </view>
      <view class="tab-line" :style="tabLineStyle"></view>
    </view>
    
    <!-- 内容区域 -->
    <view class="content-area" :style="{ paddingTop: (navbarHeight + tabsHeight) + 'px' }">
      <swiper class="content-swiper" :current="currentTab" @change="onSwiperChange">
        <!-- 收藏列表 -->
        <swiper-item>
          <scroll-view scroll-y class="tab-scroll" @scrolltolower="loadMore(0)" refresher-enabled :refresher-triggered="refreshing[0]" @refresherrefresh="onRefresh(0)">
            <view v-if="collectList.length > 0" class="collect-list">
              <view class="collect-item" v-for="(item, index) in collectList" :key="index" @click="viewDetail(item)">
                <view class="collect-left">
                  <image class="collect-image" :src="item.image" mode="aspectFill"></image>
                </view>
                <view class="collect-right">
                  <view class="collect-title">{{ item.title }}</view>
                  <view class="collect-desc">{{ item.desc }}</view>
                  <view class="collect-meta">
                    <text class="collect-time">{{ item.time }}</text>
                    <text class="collect-type">{{ item.type }}</text>
                  </view>
                </view>
              </view>
            </view>
            <view v-else class="empty-view">
              <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
              <view class="empty-text">暂无收藏内容</view>
            </view>
            <view v-if="collectList.length > 0 && !hasMore[0]" class="list-bottom">没有更多了</view>
          </scroll-view>
        </swiper-item>
        
        <!-- 关注列表 -->
        <swiper-item>
          <scroll-view scroll-y class="tab-scroll" @scrolltolower="loadMore(1)" refresher-enabled :refresher-triggered="refreshing[1]" @refresherrefresh="onRefresh(1)">
            <view v-if="followList.length > 0" class="follow-list">
              <view class="follow-item" v-for="(item, index) in followList" :key="index" @click="viewUser(item)">
                <view class="follow-left">
                  <image class="follow-avatar" :src="item.avatar" mode="aspectFill"></image>
                </view>
                <view class="follow-middle">
                  <view class="follow-name">{{ item.name }}</view>
                  <view class="follow-info">{{ item.info }}</view>
                </view>
                <view class="follow-right">
                  <button class="follow-btn" @click.stop="toggleFollow(item)">
                    {{ item.isFollowed ? '已关注' : '关注' }}
                  </button>
                </view>
              </view>
            </view>
            <view v-else class="empty-view">
              <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
              <view class="empty-text">暂无关注用户</view>
            </view>
            <view v-if="followList.length > 0 && !hasMore[1]" class="list-bottom">没有更多了</view>
          </scroll-view>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 响应式数据
const statusBarHeight = ref(20);
const navbarHeight = ref(64); // 导航栏高度
const tabsHeight = ref(44); // 选项卡高度
const tabs = ref([
  { name: '收藏' },
  { name: '关注' }
]);
const currentTab = ref(0);
const collectList = ref([]);
const followList = ref([]);
const page = ref([1, 1]); // 当前页码
const pageSize = ref(10); // 每页显示数量
const hasMore = ref([true, true]); // 是否有更多数据
const refreshing = ref([false, false]); // 刷新状态

// 计算属性
const tabLineStyle = computed(() => {
  return {
    transform: `translateX(${currentTab.value * (100 / tabs.value.length)}%)`,
    width: `${100 / tabs.value.length}%`
  };
});

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 切换选项卡
const switchTab = (index) => {
  currentTab.value = index;
};

// 轮播图切换事件
const onSwiperChange = (e) => {
  currentTab.value = e.detail.current;
};

// 加载收藏列表
const loadCollectList = () => {
  // 模拟请求延迟
  setTimeout(() => {
    // 模拟数据
    const mockData = Array.from({ length: 10 }, (_, i) => ({
      id: `collect_${page.value[0]}_${i}`,
      title: `收藏内容标题 ${page.value[0]}_${i}`,
      desc: '这是收藏内容的简短描述，可能包含多行文本内容...',
      image: '/static/images/service1.jpg',
      time: '2023-10-15',
      type: ['商家', '服务', '商品', '资讯'][Math.floor(Math.random() * 4)]
    }));
    
    if (page.value[0] === 1) {
      collectList.value = mockData;
    } else {
      collectList.value = [...collectList.value, ...mockData];
    }
    
    // 模拟是否还有更多数据
    hasMore.value[0] = page.value[0] < 3;
    
    // 关闭刷新状态
    refreshing.value[0] = false;
  }, 500);
};

// 加载关注列表
const loadFollowList = () => {
  // 模拟请求延迟
  setTimeout(() => {
    // 模拟数据
    const mockData = Array.from({ length: 10 }, (_, i) => ({
      id: `follow_${page.value[1]}_${i}`,
      name: `用户名称 ${page.value[1]}_${i}`,
      avatar: '/static/images/avatar.png',
      info: '发布了20条内容，获得188人关注',
      isFollowed: true
    }));
    
    if (page.value[1] === 1) {
      followList.value = mockData;
    } else {
      followList.value = [...followList.value, ...mockData];
    }
    
    // 模拟是否还有更多数据
    hasMore.value[1] = page.value[1] < 3;
    
    // 关闭刷新状态
    refreshing.value[1] = false;
  }, 500);
};

// 加载更多数据
const loadMore = (tabIndex) => {
  if (!hasMore.value[tabIndex]) return;
  
  page.value[tabIndex]++;
  if (tabIndex === 0) {
    loadCollectList();
  } else {
    loadFollowList();
  }
};

// 下拉刷新
const onRefresh = (tabIndex) => {
  refreshing.value[tabIndex] = true;
  page.value[tabIndex] = 1;
  hasMore.value[tabIndex] = true;
  
  if (tabIndex === 0) {
    loadCollectList();
  } else {
    loadFollowList();
  }
};

// 查看收藏详情
const viewDetail = (item) => {
  uni.navigateTo({
    url: `/pages/detail/detail?id=${item.id}&type=${item.type}`
  });
};

// 查看用户主页
const viewUser = (item) => {
  uni.navigateTo({
    url: `/pages/user/profile?id=${item.id}`
  });
};

// 切换关注状态
const toggleFollow = (item) => {
  item.isFollowed = !item.isFollowed;
  uni.showToast({
    title: item.isFollowed ? '已关注' : '已取消关注',
    icon: 'none'
  });
};

// 生命周期钩子
onMounted(() => {
  // 获取状态栏高度
  const sysInfo = uni.getSystemInfoSync();
  statusBarHeight.value = sysInfo.statusBarHeight;
  navbarHeight.value = statusBarHeight.value + 44;
  
  // 加载初始数据
  loadCollectList();
  loadFollowList();
});
</script>

<style>
.favorites-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  position: relative;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  background-color: #0052CC;
  color: #fff;
  z-index: 100;
}

.navbar-left {
  width: 80rpx;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
}

.navbar-right {
  width: 80rpx;
  text-align: right;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 选项卡样式 */
.tabs-container {
  position: fixed;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  z-index: 99;
}

.tab-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.tab-text {
  font-size: 14px;
  color: #333;
  padding: 0 15px;
}

.tab-item.active .tab-text {
  color: #0052CC;
  font-weight: bold;
}

.tab-line {
  position: absolute;
  bottom: 0;
  height: 3px;
  background-color: #0052CC;
  border-radius: 2px;
  transition: transform 0.3s;
}

/* 内容区域 */
.content-area {
  position: relative;
  height: 100vh;
}

.content-swiper {
  height: 100%;
}

.tab-scroll {
  height: 100%;
}

/* 收藏列表 */
.collect-list {
  padding: 15px;
}

.collect-item {
  display: flex;
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.collect-left {
  width: 120rpx;
  height: 120rpx;
  margin-right: 15px;
}

.collect-image {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}

.collect-right {
  flex: 1;
}

.collect-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.collect-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.collect-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

/* 关注列表 */
.follow-list {
  padding: 15px;
}

.follow-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.follow-left {
  margin-right: 15px;
}

.follow-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.follow-middle {
  flex: 1;
}

.follow-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.follow-info {
  font-size: 13px;
  color: #999;
}

.follow-right {
  margin-left: 15px;
}

.follow-btn {
  min-width: 150rpx;
  height: 60rpx;
  line-height: 60rpx;
  padding: 0 15px;
  font-size: 13px;
  border-radius: 30rpx;
  background-color: #0052CC;
  color: #fff;
}

.follow-btn[disabled] {
  background-color: #eee;
  color: #999;
}

/* 空状态 */
.empty-view {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 100px;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 列表底部 */
.list-bottom {
  text-align: center;
  padding: 15px 0;
  font-size: 14px;
  color: #999;
}
</style>