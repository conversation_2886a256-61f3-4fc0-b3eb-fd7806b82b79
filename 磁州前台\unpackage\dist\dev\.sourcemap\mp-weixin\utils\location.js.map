{"version": 3, "file": "location.js", "sources": ["utils/location.js"], "sourcesContent": ["/**\n * 位置服务工具函数\n */\n\n// 默认位置（磁县）\nconst DEFAULT_LOCATION = {\n  latitude: 36.313076,\n  longitude: 114.347312,\n  province: '河北省',\n  city: '邯郸市',\n  district: '磁县',\n  address: '河北省邯郸市磁县',\n  location: '河北省 邯郸市 磁县'\n};\n\n// 获取用户位置信息 - 优化过的版本，避免重复请求授权\nexport const getUserLocation = (options = {}) => {\n  return new Promise((resolve, reject) => {\n    // 检查是否正在请求位置权限，避免重复请求\n    const isRequestingLocation = uni.getStorageSync('isRequestingLocation');\n    if (isRequestingLocation === 'true' || isRequestingLocation === true) {\n      console.log('已有位置请求正在进行，避免重复请求');\n      \n      // 使用默认位置并返回\n      if (options.useDefaultOnFail !== false) {\n        resolve({...DEFAULT_LOCATION, timestamp: Date.now()});\n      } else {\n        reject(new Error('已有位置请求正在进行'));\n      }\n      return;\n    }\n    \n    // 标记正在请求位置\n    uni.setStorageSync('isRequestingLocation', 'true');\n    \n    // 首先检查本地是否有存储的位置信息\n    const savedLocation = uni.getStorageSync('user_location');\n    if (savedLocation && !options.forceRefresh) {\n      console.log('使用本地存储的位置信息');\n      // 清除请求标记\n      uni.removeStorageSync('isRequestingLocation');\n      resolve(savedLocation);\n      return;\n    }\n    \n    // 检查是否已标记为处理过位置授权\n    const locationAuthChecked = uni.getStorageSync('locationAuthChecked');\n    \n    // 检查位置权限设置\n    uni.getSetting({\n      success: (res) => {\n        // 检查是否已授权位置\n        if (res.authSetting && res.authSetting['scope.userLocation']) {\n          // 用户已授权，直接获取位置\n          getLocationInfo(options, resolve, reject);\n        } else if (!locationAuthChecked) {\n          // 用户未授权，且未请求过权限，记录已请求\n          uni.setStorageSync('locationAuthChecked', true);\n          \n          // 请求位置权限\n          uni.authorize({\n            scope: 'scope.userLocation',\n            success: () => {\n              // 获取位置信息\n              getLocationInfo(options, resolve, reject);\n            },\n            fail: (err) => {\n              console.log('用户拒绝授权位置权限:', err);\n              // 清除请求标记\n              uni.removeStorageSync('isRequestingLocation');\n              // 使用默认位置\n              useDefaultLocation(options, resolve, reject);\n            }\n          });\n        } else {\n          // 已请求过权限但被拒绝，直接使用默认位置\n          console.log('用户之前已拒绝位置授权，使用默认位置');\n          // 清除请求标记\n          uni.removeStorageSync('isRequestingLocation');\n          useDefaultLocation(options, resolve, reject);\n        }\n      },\n      fail: (err) => {\n        console.error('获取设置失败:', err);\n        // 清除请求标记\n        uni.removeStorageSync('isRequestingLocation');\n        useDefaultLocation(options, resolve, reject);\n      }\n    });\n  });\n};\n\n// 内部函数：获取位置信息\nfunction getLocationInfo(options, resolve, reject) {\n    uni.getLocation({\n    type: options.type || 'gcj02',\n    altitude: options.altitude || false,\n    isHighAccuracy: options.isHighAccuracy || true,\n    highAccuracyExpireTime: options.timeout || 3000,\n      success: (res) => {\n        console.log('获取位置成功:', res);\n        \n        // 构建位置信息对象\n        const location = {\n          latitude: res.latitude,\n          longitude: res.longitude,\n        // 以下信息需要通过逆地理编码获取，这里使用默认值\n          province: DEFAULT_LOCATION.province,\n          city: DEFAULT_LOCATION.city,\n          district: DEFAULT_LOCATION.district,\n          address: DEFAULT_LOCATION.address,\n        location: DEFAULT_LOCATION.location,\n        timestamp: Date.now()\n        };\n        \n      // 保存位置信息到本地存储\n      uni.setStorageSync('user_location', location);\n      \n      // 清除请求标记\n      uni.removeStorageSync('isRequestingLocation');\n        \n        // 返回位置信息\n        resolve(location);\n      },\n      fail: (err) => {\n        console.error('获取位置失败:', err);\n      // 清除请求标记\n      uni.removeStorageSync('isRequestingLocation');\n      useDefaultLocation(options, resolve, reject);\n    }\n  });\n}\n        \n// 内部函数：使用默认位置\nfunction useDefaultLocation(options, resolve, reject) {\n        if (options.useDefaultOnFail !== false) {\n          console.log('使用默认位置');\n    \n    // 构建包含时间戳的默认位置\n    const defaultLocation = {\n      ...DEFAULT_LOCATION,\n      timestamp: Date.now()\n    };\n    \n    // 保存默认位置到本地存储\n    uni.setStorageSync('user_location', defaultLocation);\n    \n    resolve(defaultLocation);\n        } else {\n    reject(new Error('获取位置失败，且不允许使用默认位置'));\n        }\n      }\n\n// 计算两点之间的距离（单位：米）\nexport const calculateDistance = (lat1, lng1, lat2, lng2) => {\n  const radLat1 = (lat1 * Math.PI) / 180.0;\n  const radLat2 = (lat2 * Math.PI) / 180.0;\n  const a = radLat1 - radLat2;\n  const b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180.0;\n  \n  let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));\n  s = s * 6378.137; // 地球半径\n  s = Math.round(s * 10000) / 10; // 输出为米\n  \n  return s;\n};\n\n// 格式化距离显示\nexport const formatDistance = (distance) => {\n  if (distance < 1000) {\n    return `${distance}米`;\n  } else {\n    return `${(distance / 1000).toFixed(1)}公里`;\n  }\n}; \n\n// 全局定位工具方法\nexport function getLocationWithAuth(callback) {\n  const located = uni.getStorageSync('hasLocated');\n  if (located) {\n    callback && callback();\n  } else {\n    uni.getLocation({\n      type: 'wgs84',\n      success: (res) => {\n        uni.setStorageSync('hasLocated', true);\n        callback && callback();\n      },\n      fail: () => {\n        uni.showToast({ title: '需要定位权限', icon: 'none' });\n      }\n    });\n  }\n} "], "names": ["uni"], "mappings": ";;AAKA,MAAM,mBAAmB;AAAA,EACvB,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,MAAM;AAAA,EACN,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AACZ;AAGY,MAAC,kBAAkB,CAAC,UAAU,OAAO;AAC/C,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEtC,UAAM,uBAAuBA,cAAAA,MAAI,eAAe,sBAAsB;AACtE,QAAI,yBAAyB,UAAU,yBAAyB,MAAM;AACpEA,oBAAAA,MAAY,MAAA,OAAA,2BAAA,mBAAmB;AAG/B,UAAI,QAAQ,qBAAqB,OAAO;AACtC,gBAAQ,EAAC,GAAG,kBAAkB,WAAW,KAAK,IAAK,EAAA,CAAC;AAAA,MAC5D,OAAa;AACL,eAAO,IAAI,MAAM,YAAY,CAAC;AAAA,MAC/B;AACD;AAAA,IACD;AAGDA,kBAAAA,MAAI,eAAe,wBAAwB,MAAM;AAGjD,UAAM,gBAAgBA,cAAAA,MAAI,eAAe,eAAe;AACxD,QAAI,iBAAiB,CAAC,QAAQ,cAAc;AAC1CA,oBAAAA,MAAY,MAAA,OAAA,2BAAA,aAAa;AAEzBA,0BAAI,kBAAkB,sBAAsB;AAC5C,cAAQ,aAAa;AACrB;AAAA,IACD;AAGD,UAAM,sBAAsBA,cAAAA,MAAI,eAAe,qBAAqB;AAGpEA,kBAAAA,MAAI,WAAW;AAAA,MACb,SAAS,CAAC,QAAQ;AAEhB,YAAI,IAAI,eAAe,IAAI,YAAY,oBAAoB,GAAG;AAE5D,0BAAgB,SAAS,SAAS,MAAM;AAAA,QAClD,WAAmB,CAAC,qBAAqB;AAE/BA,wBAAAA,MAAI,eAAe,uBAAuB,IAAI;AAG9CA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS,MAAM;AAEb,8BAAgB,SAAS,SAAS,MAAM;AAAA,YACzC;AAAA,YACD,MAAM,CAAC,QAAQ;AACbA,0EAAY,eAAe,GAAG;AAE9BA,kCAAI,kBAAkB,sBAAsB;AAE5C,iCAAmB,SAAS,SAAS,MAAM;AAAA,YAC5C;AAAA,UACb,CAAW;AAAA,QACX,OAAe;AAELA,wBAAAA,MAAA,MAAA,OAAA,2BAAY,oBAAoB;AAEhCA,8BAAI,kBAAkB,sBAAsB;AAC5C,6BAAmB,SAAS,SAAS,MAAM;AAAA,QAC5C;AAAA,MACF;AAAA,MACD,MAAM,CAAC,QAAQ;AACbA,sBAAc,MAAA,MAAA,SAAA,2BAAA,WAAW,GAAG;AAE5BA,4BAAI,kBAAkB,sBAAsB;AAC5C,2BAAmB,SAAS,SAAS,MAAM;AAAA,MAC5C;AAAA,IACP,CAAK;AAAA,EACL,CAAG;AACH;AAGA,SAAS,gBAAgB,SAAS,SAAS,QAAQ;AAC/CA,gBAAAA,MAAI,YAAY;AAAA,IAChB,MAAM,QAAQ,QAAQ;AAAA,IACtB,UAAU,QAAQ,YAAY;AAAA,IAC9B,gBAAgB,QAAQ,kBAAkB;AAAA,IAC1C,wBAAwB,QAAQ,WAAW;AAAA,IACzC,SAAS,CAAC,QAAQ;AAChBA,oBAAY,MAAA,MAAA,OAAA,4BAAA,WAAW,GAAG;AAG1B,YAAM,WAAW;AAAA,QACf,UAAU,IAAI;AAAA,QACd,WAAW,IAAI;AAAA;AAAA,QAEf,UAAU,iBAAiB;AAAA,QAC3B,MAAM,iBAAiB;AAAA,QACvB,UAAU,iBAAiB;AAAA,QAC3B,SAAS,iBAAiB;AAAA,QAC5B,UAAU,iBAAiB;AAAA,QAC3B,WAAW,KAAK,IAAK;AAAA,MAC7B;AAGMA,oBAAAA,MAAI,eAAe,iBAAiB,QAAQ;AAG5CA,0BAAI,kBAAkB,sBAAsB;AAG1C,cAAQ,QAAQ;AAAA,IACjB;AAAA,IACD,MAAM,CAAC,QAAQ;AACbA,oBAAc,MAAA,MAAA,SAAA,4BAAA,WAAW,GAAG;AAE9BA,0BAAI,kBAAkB,sBAAsB;AAC5C,yBAAmB,SAAS,SAAS,MAAM;AAAA,IAC5C;AAAA,EACL,CAAG;AACH;AAGA,SAAS,mBAAmB,SAAS,SAAS,QAAQ;AAC9C,MAAI,QAAQ,qBAAqB,OAAO;AACtCA,kBAAAA,+CAAY,QAAQ;AAG1B,UAAM,kBAAkB;AAAA,MACtB,GAAG;AAAA,MACH,WAAW,KAAK,IAAK;AAAA,IAC3B;AAGIA,kBAAAA,MAAI,eAAe,iBAAiB,eAAe;AAEnD,YAAQ,eAAe;AAAA,EAC3B,OAAe;AACX,WAAO,IAAI,MAAM,mBAAmB,CAAC;AAAA,EAChC;AACT;;"}