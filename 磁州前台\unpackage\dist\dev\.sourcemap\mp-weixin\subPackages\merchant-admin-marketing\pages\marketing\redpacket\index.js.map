{"version": 3, "file": "index.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/redpacket/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xyZWRwYWNrZXRcaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"redpacket-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">红包营销</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 红包数据概览 -->\r\n    <view class=\"overview-section\">\r\n      <view class=\"overview-header\">\r\n        <text class=\"section-title\">红包数据概览</text>\r\n        <view class=\"date-picker\" @click=\"showDatePicker\">\r\n          <text class=\"date-text\">{{dateRange}}</text>\r\n          <view class=\"date-icon\"></view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"stats-cards\">\r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">{{redpacketData.totalCount}}</view>\r\n          <view class=\"card-label\">发放总数</view>\r\n          <view class=\"card-trend\" :class=\"redpacketData.countTrend\">\r\n            <view class=\"trend-arrow\"></view>\r\n            <text class=\"trend-value\">{{redpacketData.countGrowth}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">¥{{formatNumber(redpacketData.totalAmount)}}</view>\r\n          <view class=\"card-label\">红包总额</view>\r\n          <view class=\"card-trend\" :class=\"redpacketData.amountTrend\">\r\n            <view class=\"trend-arrow\"></view>\r\n            <text class=\"trend-value\">{{redpacketData.amountGrowth}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">{{redpacketData.receiveRate}}%</view>\r\n          <view class=\"card-label\">领取率</view>\r\n          <view class=\"card-trend\" :class=\"redpacketData.receiveRateTrend\">\r\n            <view class=\"trend-arrow\"></view>\r\n            <text class=\"trend-value\">{{redpacketData.receiveRateGrowth}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">{{redpacketData.conversionRate}}%</view>\r\n          <view class=\"card-label\">转化率</view>\r\n          <view class=\"card-trend\" :class=\"redpacketData.conversionTrend\">\r\n            <view class=\"trend-arrow\"></view>\r\n            <text class=\"trend-value\">{{redpacketData.conversionGrowth}}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 红包活动列表 -->\r\n    <view class=\"redpacket-list-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">红包活动</text>\r\n        <view class=\"add-btn\" @click=\"createRedpacket\">\r\n          <text class=\"btn-text\">创建红包</text>\r\n          <view class=\"plus-icon-small\"></view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"tab-header\">\r\n        <view \r\n          class=\"tab-item\" \r\n          v-for=\"(tab, index) in tabList\" \r\n          :key=\"index\"\r\n          :class=\"{ active: currentTab === index }\"\r\n          @tap=\"switchTab(index)\">\r\n          <text class=\"tab-text\">{{tab}}</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"redpacket-list\">\r\n        <view class=\"redpacket-item\" v-for=\"(item, index) in filteredRedpackets\" :key=\"index\" @click=\"viewRedpacketDetail(item)\">\r\n          <view class=\"redpacket-header\">\r\n            <view class=\"redpacket-type\" :class=\"'type-'+item.type\">{{item.typeText}}</view>\r\n            <view class=\"redpacket-status\" :class=\"'status-'+item.status\">{{item.statusText}}</view>\r\n          </view>\r\n          \r\n          <view class=\"redpacket-content\">\r\n            <view class=\"redpacket-icon\" :class=\"item.type\"></view>\r\n            <view class=\"redpacket-info\">\r\n              <text class=\"redpacket-name\">{{item.name}}</text>\r\n              <text class=\"redpacket-time\">{{item.timeRange}}</text>\r\n              <view class=\"redpacket-amount\">\r\n                <text class=\"amount-label\">红包金额: </text>\r\n                <text class=\"amount-value\" v-if=\"item.type === 'fixed'\">¥{{item.amount}}/个</text>\r\n                <text class=\"amount-value\" v-else-if=\"item.type === 'random'\">¥{{item.minAmount}}-{{item.maxAmount}}/个</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"redpacket-stats\">\r\n            <view class=\"stat-row\">\r\n              <text class=\"stat-label\">发放数量:</text>\r\n              <text class=\"stat-value\">{{item.sentCount}}/{{item.totalCount}}</text>\r\n            </view>\r\n            <view class=\"stat-row\">\r\n              <text class=\"stat-label\">领取数量:</text>\r\n              <text class=\"stat-value\">{{item.receivedCount}}</text>\r\n            </view>\r\n            <view class=\"stat-row\">\r\n              <text class=\"stat-label\">使用数量:</text>\r\n              <text class=\"stat-value\">{{item.usedCount}}</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"redpacket-actions\">\r\n            <view class=\"action-btn\" @click.stop=\"editRedpacket(item)\">编辑</view>\r\n            <view class=\"action-btn\" @click.stop=\"shareRedpacket(item)\" v-if=\"item.status === 'active'\">分享</view>\r\n            <view class=\"action-btn delete\" @click.stop=\"deleteRedpacket(item)\" v-if=\"item.status === 'draft' || item.status === 'ended'\">删除</view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"empty-state\" v-if=\"filteredRedpackets.length === 0\">\r\n          <view class=\"empty-icon\"></view>\r\n          <text class=\"empty-text\">暂无{{tabList[currentTab]}}红包活动</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 红包营销工具 -->\r\n    <view class=\"tools-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">红包营销工具</text>\r\n      </view>\r\n      \r\n      <view class=\"tools-grid\">\r\n        <view class=\"tool-card\" v-for=\"(tool, index) in marketingTools\" :key=\"index\" @click=\"useTool(tool)\">\r\n          <view class=\"tool-icon\" :style=\"{ background: tool.color }\">\r\n            <image class=\"icon-image\" :src=\"tool.icon\" mode=\"aspectFit\"></image>\r\n          </view>\r\n          <text class=\"tool-name\">{{tool.name}}</text>\r\n          <text class=\"tool-desc\">{{tool.description}}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 红包模板 -->\r\n    <view class=\"templates-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">红包模板</text>\r\n        <view class=\"add-btn\" @click=\"createTemplate\">\r\n          <text class=\"btn-text\">创建模板</text>\r\n          <view class=\"plus-icon-small\"></view>\r\n        </view>\r\n      </view>\r\n      \r\n      <scroll-view scroll-x class=\"templates-scroll\" show-scrollbar=\"false\">\r\n        <view class=\"templates-container\">\r\n          <view class=\"template-card\" v-for=\"(template, index) in redpacketTemplates\" :key=\"index\" @click=\"useTemplate(template)\">\r\n            <view class=\"template-preview\" :style=\"{ background: template.color }\">\r\n              <view class=\"template-icon\">\r\n                <image class=\"icon-image\" :src=\"template.icon\" mode=\"aspectFit\"></image>\r\n              </view>\r\n              <text class=\"template-name\">{{template.name}}</text>\r\n            </view>\r\n            <view class=\"template-footer\">\r\n              <text class=\"template-desc\">{{template.description}}</text>\r\n              <view class=\"template-use-btn\">使用</view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </scroll-view>\r\n    </view>\r\n    \r\n    <!-- 红包营销指南 -->\r\n    <view class=\"guide-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">红包营销指南</text>\r\n      </view>\r\n      \r\n      <view class=\"guide-list\">\r\n        <view class=\"guide-item\" v-for=\"(guide, index) in marketingGuides\" :key=\"index\" @click=\"viewGuide(guide)\">\r\n          <view class=\"guide-icon\" :style=\"{ background: guide.color }\">\r\n            <image class=\"icon-image\" :src=\"guide.icon\" mode=\"aspectFit\"></image>\r\n          </view>\r\n          <view class=\"guide-content\">\r\n            <text class=\"guide-title\">{{guide.title}}</text>\r\n            <text class=\"guide-desc\">{{guide.description}}</text>\r\n          </view>\r\n          <view class=\"guide-arrow\"></view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 浮动操作按钮 -->\r\n    <view class=\"floating-action-button\" @click=\"showActionMenu\">\r\n      <view class=\"fab-icon\">+</view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      dateRange: '2023-04-01 ~ 2023-04-30',\r\n      currentTab: 0,\r\n      tabList: ['全部', '进行中', '未开始', '已结束', '草稿'],\r\n      \r\n      // 红包数据概览\r\n      redpacketData: {\r\n        totalCount: 5862,\r\n        countTrend: 'up',\r\n        countGrowth: '12.5%',\r\n        \r\n        totalAmount: 58620.50,\r\n        amountTrend: 'up',\r\n        amountGrowth: '15.2%',\r\n        \r\n        receiveRate: 76.8,\r\n        receiveRateTrend: 'up',\r\n        receiveRateGrowth: '3.5%',\r\n        \r\n        conversionRate: 42.3,\r\n        conversionTrend: 'up',\r\n        conversionGrowth: '5.2%'\r\n      },\r\n      \r\n      // 红包活动列表\r\n      redpacketList: [\r\n        {\r\n          id: 1,\r\n          name: '新用户专享红包',\r\n          type: 'fixed',\r\n          typeText: '固定金额',\r\n          status: 'active',\r\n          statusText: '进行中',\r\n          timeRange: '2023-04-01 ~ 2023-04-30',\r\n          amount: 10.00,\r\n          totalCount: 1000,\r\n          sentCount: 568,\r\n          receivedCount: 452,\r\n          usedCount: 326\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '五一节日红包',\r\n          type: 'random',\r\n          typeText: '随机金额',\r\n          status: 'upcoming',\r\n          statusText: '未开始',\r\n          timeRange: '2023-05-01 ~ 2023-05-07',\r\n          minAmount: 5.00,\r\n          maxAmount: 50.00,\r\n          totalCount: 2000,\r\n          sentCount: 0,\r\n          receivedCount: 0,\r\n          usedCount: 0\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '会员专享红包',\r\n          type: 'fixed',\r\n          typeText: '固定金额',\r\n          status: 'active',\r\n          statusText: '进行中',\r\n          timeRange: '2023-04-15 ~ 2023-04-30',\r\n          amount: 20.00,\r\n          totalCount: 500,\r\n          sentCount: 286,\r\n          receivedCount: 215,\r\n          usedCount: 158\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '春季促销红包',\r\n          type: 'random',\r\n          typeText: '随机金额',\r\n          status: 'ended',\r\n          statusText: '已结束',\r\n          timeRange: '2023-03-01 ~ 2023-03-31',\r\n          minAmount: 10.00,\r\n          maxAmount: 100.00,\r\n          totalCount: 1000,\r\n          sentCount: 1000,\r\n          receivedCount: 876,\r\n          usedCount: 654\r\n        },\r\n        {\r\n          id: 5,\r\n          name: '周末专享红包',\r\n          type: 'fixed',\r\n          typeText: '固定金额',\r\n          status: 'draft',\r\n          statusText: '草稿',\r\n          timeRange: '未设置',\r\n          amount: 15.00,\r\n          totalCount: 800,\r\n          sentCount: 0,\r\n          receivedCount: 0,\r\n          usedCount: 0\r\n        }\r\n      ],\r\n      \r\n      // 红包营销工具\r\n      marketingTools: [\r\n        {\r\n          id: 1,\r\n          name: '红包群发',\r\n          description: '批量发送红包给用户',\r\n          icon: '/static/images/send-icon.png',\r\n          color: 'linear-gradient(135deg, #FF9A8B, #FF6B6B)'\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '红包雨',\r\n          description: '创建红包雨活动',\r\n          icon: '/static/images/rain-icon.png',\r\n          color: 'linear-gradient(135deg, #FFDB01, #FF9E01)'\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '裂变红包',\r\n          description: '创建裂变式红包',\r\n          icon: '/static/images/share-icon.png',\r\n          color: 'linear-gradient(135deg, #FF5E62, #FF9966)'\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '红包数据',\r\n          description: '红包数据分析',\r\n          icon: '/static/images/data-icon.png',\r\n          color: 'linear-gradient(135deg, #00F2FE, #4FACFE)'\r\n        }\r\n      ],\r\n      \r\n      // 红包模板\r\n      redpacketTemplates: [\r\n        {\r\n          id: 1,\r\n          name: '新年红包',\r\n          description: '新年主题红包模板',\r\n          icon: '/static/images/newyear-icon.png',\r\n          color: 'linear-gradient(135deg, #FF416C, #FF4B2B)'\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '生日红包',\r\n          description: '生日主题红包模板',\r\n          icon: '/static/images/birthday-icon.png',\r\n          color: 'linear-gradient(135deg, #F6D365, #FDA085)'\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '感恩红包',\r\n          description: '感恩主题红包模板',\r\n          icon: '/static/images/thanks-icon.png',\r\n          color: 'linear-gradient(135deg, #A18CD1, #FBC2EB)'\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '节日红包',\r\n          description: '节日主题红包模板',\r\n          icon: '/static/images/festival-icon.png',\r\n          color: 'linear-gradient(135deg, #FF9A9E, #FECFEF)'\r\n        }\r\n      ],\r\n      \r\n      // 营销指南\r\n      marketingGuides: [\r\n        {\r\n          id: 1,\r\n          title: '红包营销最佳实践',\r\n          description: '了解如何有效使用红包提高转化率',\r\n          icon: '/static/images/guide-icon.png',\r\n          color: 'linear-gradient(135deg, #84FAB0, #8FD3F4)'\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '节日红包攻略',\r\n          description: '节日期间红包营销策略指南',\r\n          icon: '/static/images/strategy-icon.png',\r\n          color: 'linear-gradient(135deg, #FCCF31, #F55555)'\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '裂变红包详解',\r\n          description: '如何利用裂变红包实现用户增长',\r\n          icon: '/static/images/growth-icon.png',\r\n          color: 'linear-gradient(135deg, #43E97B, #38F9D7)'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    filteredRedpackets() {\r\n      if (this.currentTab === 0) {\r\n        return this.redpacketList;\r\n      } else {\r\n        const statusMap = ['', 'active', 'upcoming', 'ended', 'draft'];\r\n        return this.redpacketList.filter(item => item.status === statusMap[this.currentTab]);\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    \r\n    showHelp() {\r\n      uni.showModal({\r\n        title: '红包营销帮助',\r\n        content: '红包营销是通过发放现金红包的方式吸引用户，提高用户活跃度和转化率的营销方式。',\r\n        showCancel: false\r\n      });\r\n    },\r\n    \r\n    formatNumber(number) {\r\n      return number.toFixed(2).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n    },\r\n    \r\n    showDatePicker() {\r\n      // 实现日期选择器\r\n      uni.showToast({\r\n        title: '日期选择功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    \r\n    switchTab(index) {\r\n      this.currentTab = index;\r\n    },\r\n    \r\n    createRedpacket() {\r\n      uni.navigateTo({\r\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/redpacket/create'\r\n      });\r\n    },\r\n    \r\n    viewRedpacketDetail(redpacket) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/redpacket/detail?id=${redpacket.id}`\r\n      });\r\n    },\r\n    \r\n    editRedpacket(redpacket) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/redpacket/edit?id=${redpacket.id}`\r\n      });\r\n    },\r\n    \r\n    shareRedpacket(redpacket) {\r\n      uni.showLoading({\r\n        title: '生成分享链接...'\r\n      });\r\n      \r\n      setTimeout(() => {\r\n        uni.hideLoading();\r\n        uni.showModal({\r\n          title: '分享红包',\r\n          content: '红包分享链接已生成，是否复制链接？',\r\n          confirmText: '复制',\r\n          success: (res) => {\r\n            if (res.confirm) {\r\n              uni.setClipboardData({\r\n                data: `https://example.com/redpacket/${redpacket.id}`,\r\n                success: () => {\r\n                  uni.showToast({\r\n                    title: '链接已复制',\r\n                    icon: 'success'\r\n                  });\r\n                }\r\n              });\r\n            }\r\n          }\r\n        });\r\n      }, 1000);\r\n    },\r\n    \r\n    deleteRedpacket(redpacket) {\r\n      uni.showModal({\r\n        title: '删除红包',\r\n        content: `确定要删除\"${redpacket.name}\"吗？`,\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            // 模拟删除操作\r\n            const index = this.redpacketList.findIndex(item => item.id === redpacket.id);\r\n            if (index !== -1) {\r\n              this.redpacketList.splice(index, 1);\r\n              uni.showToast({\r\n                title: '删除成功',\r\n                icon: 'success'\r\n              });\r\n            }\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n    useTool(tool) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/redpacket/tool?id=${tool.id}`\r\n      });\r\n    },\r\n    \r\n    createTemplate() {\r\n      uni.navigateTo({\r\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/redpacket/create-template'\r\n      });\r\n    },\r\n    \r\n    useTemplate(template) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/redpacket/create?template=${template.id}`\r\n      });\r\n    },\r\n    \r\n    viewGuide(guide) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/redpacket/guide?id=${guide.id}`\r\n      });\r\n    },\r\n    \r\n    showActionMenu() {\r\n      uni.showActionSheet({\r\n        itemList: ['创建红包', '红包数据分析', '导出红包数据', '红包营销设置'],\r\n        success: (res) => {\r\n          const actions = [\r\n            () => this.createRedpacket(),\r\n            () => this.analyzeData(),\r\n            () => this.exportData(),\r\n            () => this.manageSettings()\r\n          ];\r\n          \r\n          actions[res.tapIndex]();\r\n        }\r\n      });\r\n    },\r\n    \r\n    analyzeData() {\r\n      uni.navigateTo({\r\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/redpacket/data-analysis'\r\n      });\r\n    },\r\n    \r\n    exportData() {\r\n      uni.showLoading({\r\n        title: '导出中...'\r\n      });\r\n      \r\n      setTimeout(() => {\r\n        uni.hideLoading();\r\n        uni.showToast({\r\n          title: '数据导出成功',\r\n          icon: 'success'\r\n        });\r\n      }, 1500);\r\n    },\r\n    \r\n    manageSettings() {\r\n      uni.navigateTo({\r\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/redpacket/settings'\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.redpacket-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #FF5858, #FF0000);\r\n  color: #fff;\r\n  padding: 44px 16px 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 2px 10px rgba(255, 0, 0, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.navbar-right {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.help-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 通用部分样式 */\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.add-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  background: #FF3B30;\r\n  border-radius: 15px;\r\n  padding: 5px 12px;\r\n  color: white;\r\n}\r\n\r\n.btn-text {\r\n  font-size: 13px;\r\n  margin-right: 5px;\r\n}\r\n\r\n.plus-icon-small {\r\n  width: 12px;\r\n  height: 12px;\r\n  position: relative;\r\n}\r\n\r\n.plus-icon-small:before,\r\n.plus-icon-small:after {\r\n  content: '';\r\n  position: absolute;\r\n  background: white;\r\n}\r\n\r\n.plus-icon-small:before {\r\n  width: 12px;\r\n  height: 2px;\r\n  top: 5px;\r\n  left: 0;\r\n}\r\n\r\n.plus-icon-small:after {\r\n  height: 12px;\r\n  width: 2px;\r\n  left: 5px;\r\n  top: 0;\r\n}\r\n\r\n/* 概览部分样式 */\r\n.overview-section {\r\n  margin: 15px;\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  padding: 15px;\r\n}\r\n\r\n.overview-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.date-picker {\r\n  display: flex;\r\n  align-items: center;\r\n  background: #F5F7FA;\r\n  border-radius: 15px;\r\n  padding: 5px 10px;\r\n}\r\n\r\n.date-text {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-right: 5px;\r\n}\r\n\r\n.date-icon {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-top: 2px solid #666;\r\n  border-right: 2px solid #666;\r\n  transform: rotate(135deg);\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: 0 -7.5px;\r\n}\r\n\r\n.stats-card {\r\n  width: 50%;\r\n  padding: 7.5px;\r\n  box-sizing: border-box;\r\n  position: relative;\r\n}\r\n\r\n.card-value {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 5px;\r\n  background: #F8FAFC;\r\n  padding: 15px;\r\n  border-radius: 10px;\r\n  border-left: 3px solid #FF3B30;\r\n}\r\n\r\n.card-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n  position: absolute;\r\n  bottom: 20px;\r\n  left: 25px;\r\n}\r\n\r\n.card-trend {\r\n  position: absolute;\r\n  bottom: 20px;\r\n  right: 25px;\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 12px;\r\n}\r\n\r\n.card-trend.up {\r\n  color: #34C759;\r\n}\r\n\r\n.card-trend.down {\r\n  color: #FF3B30;\r\n}\r\n\r\n.trend-arrow {\r\n  width: 0;\r\n  height: 0;\r\n  margin-right: 3px;\r\n}\r\n\r\n.card-trend.up .trend-arrow {\r\n  border-left: 4px solid transparent;\r\n  border-right: 4px solid transparent;\r\n  border-bottom: 6px solid #34C759;\r\n}\r\n\r\n.card-trend.down .trend-arrow {\r\n  border-left: 4px solid transparent;\r\n  border-right: 4px solid transparent;\r\n  border-top: 6px solid #FF3B30;\r\n}\r\n\r\n/* 红包列表样式 */\r\n.redpacket-list-section {\r\n  margin: 15px;\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  padding: 15px;\r\n}\r\n\r\n.tab-header {\r\n  display: flex;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.tab-item {\r\n  padding: 0 15px 10px;\r\n  font-size: 14px;\r\n  color: #666;\r\n  position: relative;\r\n}\r\n\r\n.tab-item.active {\r\n  color: #FF3B30;\r\n  font-weight: 500;\r\n}\r\n\r\n.tab-item.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 15px;\r\n  right: 15px;\r\n  height: 2px;\r\n  background: #FF3B30;\r\n  border-radius: 1px;\r\n}\r\n\r\n.redpacket-list {\r\n  margin-top: 15px;\r\n}\r\n\r\n.redpacket-item {\r\n  background: #FFFFFF;\r\n  border-radius: 10px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n  margin-bottom: 15px;\r\n  overflow: hidden;\r\n}\r\n\r\n.redpacket-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  padding: 10px 15px;\r\n  background: #FFF8F8;\r\n  border-bottom: 1px solid #FFE8E8;\r\n}\r\n\r\n.redpacket-type {\r\n  font-size: 12px;\r\n  padding: 2px 8px;\r\n  border-radius: 10px;\r\n}\r\n\r\n.type-fixed {\r\n  background: rgba(255, 59, 48, 0.1);\r\n  color: #FF3B30;\r\n}\r\n\r\n.type-random {\r\n  background: rgba(255, 149, 0, 0.1);\r\n  color: #FF9500;\r\n}\r\n\r\n.redpacket-status {\r\n  font-size: 12px;\r\n  padding: 2px 8px;\r\n  border-radius: 10px;\r\n}\r\n\r\n.status-active {\r\n  background: rgba(52, 199, 89, 0.1);\r\n  color: #34C759;\r\n}\r\n\r\n.status-upcoming {\r\n  background: rgba(0, 122, 255, 0.1);\r\n  color: #007AFF;\r\n}\r\n\r\n.status-ended {\r\n  background: rgba(142, 142, 147, 0.1);\r\n  color: #8E8E93;\r\n}\r\n\r\n.status-draft {\r\n  background: rgba(255, 204, 0, 0.1);\r\n  color: #FFCC00;\r\n}\r\n\r\n.redpacket-content {\r\n  display: flex;\r\n  padding: 15px;\r\n}\r\n\r\n.redpacket-icon {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 25px;\r\n  margin-right: 15px;\r\n  position: relative;\r\n}\r\n\r\n.redpacket-icon.fixed {\r\n  background: linear-gradient(135deg, #FF5858, #FF0000);\r\n}\r\n\r\n.redpacket-icon.fixed::before,\r\n.redpacket-icon.random::before {\r\n  content: '¥';\r\n  position: absolute;\r\n  color: white;\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n}\r\n\r\n.redpacket-icon.random {\r\n  background: linear-gradient(135deg, #FF9500, #FF5E3A);\r\n}\r\n\r\n.redpacket-info {\r\n  flex: 1;\r\n}\r\n\r\n.redpacket-name {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 5px;\r\n  display: block;\r\n}\r\n\r\n.redpacket-time {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 5px;\r\n  display: block;\r\n}\r\n\r\n.redpacket-amount {\r\n  font-size: 12px;\r\n}\r\n\r\n.amount-label {\r\n  color: #999;\r\n}\r\n\r\n.amount-value {\r\n  color: #FF3B30;\r\n  font-weight: 500;\r\n}\r\n\r\n.redpacket-stats {\r\n  padding: 0 15px 15px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.stat-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.stat-row:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 12px;\r\n  color: #333;\r\n  font-weight: 500;\r\n}\r\n\r\n.redpacket-actions {\r\n  display: flex;\r\n  padding: 10px 15px;\r\n}\r\n\r\n.action-btn {\r\n  font-size: 12px;\r\n  color: #FF3B30;\r\n  background: rgba(255, 59, 48, 0.1);\r\n  padding: 5px 10px;\r\n  border-radius: 15px;\r\n  margin-right: 10px;\r\n}\r\n\r\n.action-btn.delete {\r\n  color: #FF3B30;\r\n  background: rgba(255, 59, 48, 0.1);\r\n}\r\n\r\n.empty-state {\r\n  padding: 30px 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.empty-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  background: rgba(255, 59, 48, 0.1);\r\n  border-radius: 30px;\r\n  margin-bottom: 15px;\r\n  position: relative;\r\n}\r\n\r\n.empty-icon::before {\r\n  content: '';\r\n  width: 30px;\r\n  height: 30px;\r\n  border: 2px solid #FF3B30;\r\n  border-radius: 15px;\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n}\r\n\r\n.empty-icon::after {\r\n  content: '';\r\n  width: 2px;\r\n  height: 15px;\r\n  background: #FF3B30;\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%) rotate(45deg);\r\n}\r\n\r\n.empty-text {\r\n  font-size: 14px;\r\n  color: #999;\r\n}\r\n\r\n/* 营销工具样式 */\r\n.tools-section {\r\n  margin: 15px;\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  padding: 15px;\r\n}\r\n\r\n.tools-grid {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: 0 -7.5px;\r\n}\r\n\r\n.tool-card {\r\n  width: 25%;\r\n  padding: 7.5px;\r\n  box-sizing: border-box;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.tool-icon {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 25px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.icon-image {\r\n  width: 24px;\r\n  height: 24px;\r\n}\r\n\r\n.tool-name {\r\n  font-size: 14px;\r\n  color: #333;\r\n  margin-bottom: 3px;\r\n  text-align: center;\r\n}\r\n\r\n.tool-desc {\r\n  font-size: 10px;\r\n  color: #999;\r\n  text-align: center;\r\n  height: 28px;\r\n  overflow: hidden;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n}\r\n\r\n/* 红包模板样式 */\r\n.templates-section {\r\n  margin: 15px;\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  padding: 15px;\r\n}\r\n\r\n.templates-scroll {\r\n  white-space: nowrap;\r\n  margin: 0 -15px;\r\n  padding: 0 15px;\r\n}\r\n\r\n.templates-container {\r\n  display: inline-flex;\r\n  padding: 5px 0;\r\n}\r\n\r\n.template-card {\r\n  width: 150px;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  margin-right: 15px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.template-preview {\r\n  height: 100px;\r\n  padding: 15px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.template-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.template-name {\r\n  color: white;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n}\r\n\r\n.template-footer {\r\n  padding: 10px;\r\n  background: white;\r\n  position: relative;\r\n}\r\n\r\n.template-desc {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 25px;\r\n  display: block;\r\n}\r\n\r\n.template-use-btn {\r\n  position: absolute;\r\n  bottom: 10px;\r\n  right: 10px;\r\n  font-size: 12px;\r\n  color: #FF3B30;\r\n  background: rgba(255, 59, 48, 0.1);\r\n  padding: 3px 8px;\r\n  border-radius: 10px;\r\n}\r\n\r\n/* 营销指南样式 */\r\n.guide-section {\r\n  margin: 15px;\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  padding: 15px;\r\n}\r\n\r\n.guide-list {\r\n  margin-top: 10px;\r\n}\r\n\r\n.guide-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 15px 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.guide-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.guide-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 15px;\r\n}\r\n\r\n.guide-content {\r\n  flex: 1;\r\n}\r\n\r\n.guide-title {\r\n  font-size: 15px;\r\n  font-weight: 500;\r\n  color: #333;\r\n  margin-bottom: 3px;\r\n}\r\n\r\n.guide-desc {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.guide-arrow {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-top: 1.5px solid #999;\r\n  border-right: 1.5px solid #999;\r\n  transform: rotate(45deg);\r\n  margin-left: 15px;\r\n}\r\n\r\n/* 浮动操作按钮 */\r\n.floating-action-button {\r\n  position: fixed;\r\n  bottom: 30px;\r\n  right: 30px;\r\n  width: 56px;\r\n  height: 56px;\r\n  border-radius: 28px;\r\n  background: linear-gradient(135deg, #FF5858, #FF0000);\r\n  box-shadow: 0 4px 15px rgba(255, 59, 48, 0.3);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 100;\r\n}\r\n\r\n.fab-icon {\r\n  font-size: 28px;\r\n  color: #fff;\r\n  font-weight: 300;\r\n  line-height: 1;\r\n  margin-top: -2px;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media screen and (max-width: 375px) {\r\n  .tool-card {\r\n    width: 33.33%;\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 320px) {\r\n  .stats-card {\r\n    width: 100%;\r\n  }\r\n  \r\n  .tool-card {\r\n    width: 50%;\r\n  }\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/redpacket/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA6MA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,SAAS,CAAC,MAAM,OAAO,OAAO,OAAO,IAAI;AAAA;AAAA,MAGzC,eAAe;AAAA,QACb,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,QAEb,aAAa;AAAA,QACb,aAAa;AAAA,QACb,cAAc;AAAA,QAEd,aAAa;AAAA,QACb,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QAEnB,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,MACnB;AAAA;AAAA,MAGD,eAAe;AAAA,QACb;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,eAAe;AAAA,UACf,WAAW;AAAA,QACZ;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,WAAW;AAAA,UACX,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,eAAe;AAAA,UACf,WAAW;AAAA,QACZ;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,eAAe;AAAA,UACf,WAAW;AAAA,QACZ;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,WAAW;AAAA,UACX,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,eAAe;AAAA,UACf,WAAW;AAAA,QACZ;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,eAAe;AAAA,UACf,WAAW;AAAA,QACb;AAAA,MACD;AAAA;AAAA,MAGD,gBAAgB;AAAA,QACd;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MACD;AAAA;AAAA,MAGD,oBAAoB;AAAA,QAClB;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MACD;AAAA;AAAA,MAGD,iBAAiB;AAAA,QACf;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,qBAAqB;AACnB,UAAI,KAAK,eAAe,GAAG;AACzB,eAAO,KAAK;AAAA,aACP;AACL,cAAM,YAAY,CAAC,IAAI,UAAU,YAAY,SAAS,OAAO;AAC7D,eAAO,KAAK,cAAc,OAAO,UAAQ,KAAK,WAAW,UAAU,KAAK,UAAU,CAAC;AAAA,MACrF;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IAED,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MACd,CAAC;AAAA,IACF;AAAA,IAED,aAAa,QAAQ;AACnB,aAAO,OAAO,QAAQ,CAAC,EAAE,QAAQ,yBAAyB,GAAG;AAAA,IAC9D;AAAA,IAED,iBAAiB;AAEfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IAED,UAAU,OAAO;AACf,WAAK,aAAa;AAAA,IACnB;AAAA,IAED,kBAAkB;AAChBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IAED,oBAAoB,WAAW;AAC7BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,6EAA6E,UAAU,EAAE;AAAA,MAChG,CAAC;AAAA,IACF;AAAA,IAED,cAAc,WAAW;AACvBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,2EAA2E,UAAU,EAAE;AAAA,MAC9F,CAAC;AAAA,IACF;AAAA,IAED,eAAe,WAAW;AACxBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,UACb,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AACfA,4BAAAA,MAAI,iBAAiB;AAAA,gBACnB,MAAM,iCAAiC,UAAU,EAAE;AAAA,gBACnD,SAAS,MAAM;AACbA,gCAAAA,MAAI,UAAU;AAAA,oBACZ,OAAO;AAAA,oBACP,MAAM;AAAA,kBACR,CAAC;AAAA,gBACH;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACF,GAAE,GAAI;AAAA,IACR;AAAA,IAED,gBAAgB,WAAW;AACzBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,SAAS,UAAU,IAAI;AAAA,QAChC,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEf,kBAAM,QAAQ,KAAK,cAAc,UAAU,UAAQ,KAAK,OAAO,UAAU,EAAE;AAC3E,gBAAI,UAAU,IAAI;AAChB,mBAAK,cAAc,OAAO,OAAO,CAAC;AAClCA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,QAAQ,MAAM;AACZA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,2EAA2E,KAAK,EAAE;AAAA,MACzF,CAAC;AAAA,IACF;AAAA,IAED,iBAAiB;AACfA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IAED,YAAY,UAAU;AACpBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,mFAAmF,SAAS,EAAE;AAAA,MACrG,CAAC;AAAA,IACF;AAAA,IAED,UAAU,OAAO;AACfA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4EAA4E,MAAM,EAAE;AAAA,MAC3F,CAAC;AAAA,IACF;AAAA,IAED,iBAAiB;AACfA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,QAAQ,UAAU,UAAU,QAAQ;AAAA,QAC/C,SAAS,CAAC,QAAQ;AAChB,gBAAM,UAAU;AAAA,YACd,MAAM,KAAK,gBAAiB;AAAA,YAC5B,MAAM,KAAK,YAAa;AAAA,YACxB,MAAM,KAAK,WAAY;AAAA,YACvB,MAAM,KAAK,eAAe;AAAA;AAG5B,kBAAQ,IAAI,QAAQ;QACtB;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,cAAc;AACZA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IAED,aAAa;AACXA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACF,GAAE,IAAI;AAAA,IACR;AAAA,IAED,iBAAiB;AACfA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvjBA,GAAG,WAAW,eAAe;"}