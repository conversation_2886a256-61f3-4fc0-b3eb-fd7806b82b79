<template>
  <view class="chat-container" :class="{'has-more-panel': showMorePanel}">
    <!-- 顶部安全区域 -->
    <view class="safe-area-top"></view>
    
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/最新返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">{{chatTitle}}</view>
      <view class="navbar-right">
        <!-- 预留位置 -->
      </view>
    </view>

    <!-- 聊天内容区域 -->
    <scroll-view 
      class="chat-content" 
      scroll-y 
      :scroll-into-view="scrollIntoView"
      @scrolltoupper="loadMoreMessages"
      upper-threshold="50"
      @tap="closeMorePanel"
    >
      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMoreMessages">
        <text v-if="!isLoading">加载更多</text>
        <text v-else>加载中...</text>
      </view>
      
      <!-- 消息列表 -->
      <view class="message-list">
        <block v-for="(item, index) in messages" :key="index">
          <!-- 时间分割线 -->
          <view class="time-divider" v-if="showTimeDivider(index)">
            <text>{{formatTime(item.time)}}</text>
          </view>
          
          <view 
            class="message-item" 
            :id="'msg-' + index"
            :class="{'self': item.isSelf}"
          >
            <!-- 头像 -->
            <view class="avatar-container" v-if="!item.isSelf">
              <image class="avatar" :src="item.avatar" mode="aspectFill"></image>
            </view>
            
            <!-- 消息气泡 -->
            <view class="message-bubble" :class="{'self': item.isSelf}">
              <!-- 普通文本消息 -->
              <text v-if="!item.type || item.type === 'text'" class="message-text">{{item.content}}</text>
              
              <!-- 定位消息 -->
              <view v-else-if="item.type === 'location'" class="location-message" @tap="viewLocation(item.locationData)">
                <view class="location-icon">
                  <image src="/static/images/tabbar/定位.png" mode="aspectFit"></image>
                </view>
                <view class="location-info">
                  <text class="location-name">{{item.locationData.name}}</text>
                  <text class="location-address">{{item.locationData.address}}</text>
                </view>
                <view class="location-arrow">
                  <text class="arrow-icon">></text>
                </view>
              </view>
              
              <!-- 图片消息 -->
              <view v-else-if="item.type === 'image'" class="image-message" @tap="previewImage(item.imageUrl)">
                <image :src="item.imageUrl" mode="widthFix" class="message-image"></image>
              </view>
            </view>
            
            <!-- 头像 -->
            <view class="avatar-container" v-if="item.isSelf">
              <image class="avatar" :src="userInfo.avatar" mode="aspectFill"></image>
            </view>
          </view>
        </block>
      </view>
    </scroll-view>

    <!-- 输入区域 -->
    <view class="input-area" :style="{ bottom: isKeyboardShow ? keyboardHeight + 'px' : '0' }">
      <view class="input-box">
        <textarea 
          class="input-field" 
          v-model="messageText" 
          placeholder="请输入消息..." 
          :adjust-position="true"
          :show-confirm-bar="false"
          :cursor-spacing="20"
          :maxlength="-1"
          @focus="onInputFocus"
          @blur="onInputBlur"
          @confirm="sendMessage"
          @input="onInput"
          :style="{ 'max-height': '120rpx' }"
        ></textarea>
      </view>
      <view class="action-btn" :class="{'has-text': messageText.trim().length > 0}" @tap="messageText.trim().length > 0 ? sendMessage() : toggleMorePanel()">
        <text v-if="messageText.trim().length > 0" class="send-text">发送</text>
        <image v-else src="/static/images/tabbar/添加.png" mode="aspectFit" class="add-icon"></image>
      </view>
    </view>
    
    <!-- 底部安全区域 -->
    <view class="safe-area-bottom"></view>
    
    <!-- 扩展功能面板（抽屉式） -->
    <view class="more-panel-mask" v-if="showMorePanel" @tap.stop="closeMorePanel"></view>
    <view class="more-panel-drawer" :class="{'show': showMorePanel}">
      <view class="more-panel-content">
        <view class="more-grid">
          <view class="more-item" @tap.stop="sendLocation">
            <view class="more-icon location-icon">
              <image src="/static/images/tabbar/定位.png" mode="aspectFit" class="panel-icon-image"></image>
            </view>
            <text class="more-text">位置</text>
          </view>
          <view class="more-item" @tap.stop="sendImage">
            <view class="more-icon image-icon">
              <image src="/static/images/tabbar/相册.png" mode="aspectFit" class="panel-icon-image"></image>
            </view>
            <text class="more-text">图片</text>
          </view>
          <view class="more-item" @tap.stop="sendFile">
            <view class="more-icon file-icon">
              <image src="/static/images/tabbar/文件.png" mode="aspectFit" class="panel-icon-image"></image>
            </view>
            <text class="more-text">文件</text>
          </view>
          <view class="more-item" @tap.stop="sendContact">
            <view class="more-icon contact-icon">
              <image src="/static/images/tabbar/联系人.png" mode="aspectFit" class="panel-icon-image"></image>
            </view>
            <text class="more-text">名片</text>
          </view>
        </view>
      </view>
      <view class="more-panel-handle" @tap.stop="closeMorePanel">
        <view class="handle-line"></view>
      </view>
    </view>
  </view>
</template>

<script>
import { getLocalUserInfo } from '@/utils/userProfile.js';

export default {
  data() {
    return {
      userId: '', // 聊天对象ID
      nickname: '', // 聊天对象昵称
      chatTitle: '私信', // 聊天标题
      messages: [], // 消息列表
      messageText: '', // 输入框文本
      scrollIntoView: '', // 滚动到指定位置
      hasMoreMessages: false, // 是否有更多历史消息
      isLoading: false, // 是否正在加载
      page: 1, // 当前页码
      userInfo: {
        avatar: '/static/images/default-avatar.png'
      }, // 当前用户信息
      isKeyboardShow: false,
      keyboardHeight: 0,
      showMorePanel: false, // 是否显示扩展功能面板
    }
  },
  onLoad(options) {
    // 获取参数
    this.userId = options.userId || '';
    this.nickname = options.nickname || '用户';
    this.chatTitle = this.nickname;
    
    // 设置导航栏样式
    uni.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#0A84FF'
    });
    
    // 获取当前用户信息
    this.getUserInfo();
    
    // 加载历史消息
    this.loadMessages();
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 获取当前用户信息
    getUserInfo() {
      const userInfo = getLocalUserInfo();
      if (userInfo) {
        this.userInfo = userInfo;
      }
    },
    
    // 加载消息
    loadMessages() {
      this.isLoading = true;
      
      // 模拟API请求延迟
      setTimeout(() => {
        // 模拟消息数据
        const mockMessages = this.generateMockMessages();
        
        if (this.page === 1) {
          this.messages = mockMessages;
          // 滚动到最新消息
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        } else {
          this.messages = [...mockMessages, ...this.messages];
        }
        
        this.hasMoreMessages = this.page < 3; // 模拟只有3页历史消息
        this.isLoading = false;
      }, 500);
    },
    
    // 加载更多历史消息
    loadMoreMessages() {
      if (this.isLoading || !this.hasMoreMessages) return;
      
      this.page++;
      this.loadMessages();
    },
    
    // 发送消息
    sendMessage() {
      const content = this.messageText.trim();
      if (!content) return;
      
      // 添加新消息
      const newMessage = {
        id: Date.now().toString(),
        content,
        time: new Date(),
        isSelf: true,
        avatar: this.userInfo.avatar
      };
      
      this.messages.push(newMessage);
      this.messageText = '';
      
      // 滚动到最新消息
      this.$nextTick(() => {
        this.scrollToBottom();
      });
      
      // 模拟对方回复
      setTimeout(() => {
        const replyMessage = {
          id: (Date.now() + 1).toString(),
          content: this.getRandomReply(),
          time: new Date(),
          isSelf: false,
          avatar: '/static/images/default-avatar.png'
        };
        
        this.messages.push(replyMessage);
        
        // 滚动到最新消息
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }, 1000);
    },
    
    // 滚动到底部
    scrollToBottom() {
      if (this.messages.length > 0) {
        this.scrollIntoView = 'msg-' + (this.messages.length - 1);
      }
    },
    
    // 输入框获取焦点
    onInputFocus(e) {
      this.isKeyboardShow = true;
      this.keyboardHeight = e.detail.height || 0;
      // 滚动到最新消息
      this.scrollToBottom();
    },
    
    // 输入框失去焦点
    onInputBlur() {
      this.isKeyboardShow = false;
      this.keyboardHeight = 0;
    },
    
    // 判断是否显示时间分割线
    showTimeDivider(index) {
      if (index === 0) return true;
      
      const currentMsg = this.messages[index];
      const prevMsg = this.messages[index - 1];
      
      // 如果与上一条消息时间相差超过5分钟，显示时间分割线
      return new Date(currentMsg.time) - new Date(prevMsg.time) > 5 * 60 * 1000;
    },
    
    // 格式化时间
    formatTime(time) {
      const date = new Date(time);
      const now = new Date();
      const isToday = date.toDateString() === now.toDateString();
      
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      
      if (isToday) {
        return `${hours}:${minutes}`;
      } else {
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${month}-${day} ${hours}:${minutes}`;
      }
    },
    
    // 生成模拟消息数据
    generateMockMessages() {
      const mockMessages = [];
      const count = 10; // 每页10条消息
      
      // 基准时间，根据页码设置
      const baseTime = new Date();
      baseTime.setHours(baseTime.getHours() - (this.page * 2));
      
      for (let i = 0; i < count; i++) {
        const isSelf = Math.random() > 0.5;
        const messageTime = new Date(baseTime);
        messageTime.setMinutes(messageTime.getMinutes() + i * 10);
        
        mockMessages.push({
          id: `msg_${this.page}_${i}`,
          content: this.getRandomMessage(isSelf),
          time: messageTime,
          isSelf,
          avatar: isSelf ? this.userInfo.avatar : '/static/images/default-avatar.png'
        });
      }
      
      return mockMessages;
    },
    
    // 获取随机消息内容
    getRandomMessage(isSelf) {
      const selfMessages = [
        '你好，最近怎么样？',
        '有时间一起出来喝杯咖啡吗？',
        '推荐一下附近有什么好吃的地方？',
        '最近有什么新活动吗？',
        '谢谢你的关注！'
      ];
      
      const otherMessages = [
        '你好，我很好，谢谢关心！',
        '最近有点忙，改天约时间吧',
        '附近新开了一家火锅店，味道不错',
        '周末有个音乐节活动，要一起去吗？',
        '不客气，我很喜欢你分享的内容'
      ];
      
      const messages = isSelf ? selfMessages : otherMessages;
      return messages[Math.floor(Math.random() * messages.length)];
    },
    
    // 获取随机回复
    getRandomReply() {
      const replies = [
        '好的，我知道了',
        '嗯嗯，没问题',
        '谢谢你告诉我这些',
        '我会考虑的',
        '有空再聊吧',
        '这个主意不错',
        '我同意你的看法',
        '需要我帮忙吗？',
        '我会尽快回复你的',
        '我明白你的意思了'
      ];
      
      return replies[Math.floor(Math.random() * replies.length)];
    },
    
    // 发送定位
    sendLocation() {
      // 关闭更多面板
      this.showMorePanel = false;
      
      uni.authorize({
        scope: 'scope.userLocation',
        success: () => {
          uni.chooseLocation({
            success: (res) => {
              if (res.name && res.address) {
                // 创建定位消息
                const locationMsg = {
                  id: Date.now().toString(),
                  type: 'location',
                  content: `[位置] ${res.name}`,
                  locationData: {
                    name: res.name,
                    address: res.address,
                    latitude: res.latitude,
                    longitude: res.longitude
                  },
                  time: new Date(),
                  isSelf: true,
                  avatar: this.userInfo.avatar
                };
                
                // 添加到消息列表
                this.messages.push(locationMsg);
                
                // 滚动到最新消息
                this.$nextTick(() => {
                  this.scrollToBottom();
                });
                
                // 模拟对方回复
                setTimeout(() => {
                  const replyMessage = {
                    id: (Date.now() + 1).toString(),
                    content: '我已收到您的位置信息',
                    time: new Date(),
                    isSelf: false,
                    avatar: '/static/images/default-avatar.png'
                  };
                  
                  this.messages.push(replyMessage);
                  
                  // 滚动到最新消息
                  this.$nextTick(() => {
                    this.scrollToBottom();
                  });
                }, 1000);
              }
            },
            fail: (err) => {
              console.error('选择位置失败', err);
              uni.showToast({
                title: '选择位置失败',
                icon: 'none'
              });
            }
          });
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '需要您授权获取位置信息，是否前往设置？',
            confirmText: '去设置',
            success: (res) => {
              if (res.confirm) {
                uni.openSetting();
              }
            }
          });
        }
      });
    },
    
    // 查看定位
    viewLocation(locationData) {
      uni.openLocation({
        latitude: locationData.latitude,
        longitude: locationData.longitude,
        name: locationData.name,
        address: locationData.address,
        success: () => {
          console.log('打开位置成功');
        },
        fail: (err) => {
          console.error('打开位置失败', err);
          uni.showToast({
            title: '打开位置失败',
            icon: 'none'
          });
        }
      });
    },
    
    // 输入变化
    onInput(e) {
      // 输入时自动关闭更多面板
      if (this.messageText.trim().length > 0 && this.showMorePanel) {
        this.showMorePanel = false;
      }
    },
    
    // 切换扩展功能面板
    toggleMorePanel() {
      if (this.messageText.trim().length > 0) {
        this.sendMessage();
      } else {
        // 切换面板状态
        if (!this.showMorePanel) {
          uni.hideKeyboard();
          // 延迟显示面板，确保键盘收起
          setTimeout(() => {
            this.showMorePanel = true;
            // 监听点击事件，点击内容区域关闭面板
            setTimeout(() => {
              uni.$once('chat-content-tap', () => {
                this.showMorePanel = false;
              });
            }, 300);
          }, 50);
        } else {
          this.showMorePanel = false;
        }
      }
    },
    
    // 发送图片
    sendImage() {
      uni.chooseImage({
        count: 1,
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          
          // 创建图片消息
          const imageMsg = {
            id: Date.now().toString(),
            type: 'image',
            content: '[图片]',
            imageUrl: tempFilePath,
            time: new Date(),
            isSelf: true,
            avatar: this.userInfo.avatar
          };
          
          // 添加到消息列表
          this.messages.push(imageMsg);
          
          // 关闭更多面板
          this.showMorePanel = false;
          
          // 滚动到最新消息
          this.$nextTick(() => {
            this.scrollToBottom();
          });
          
          // 模拟对方回复
          setTimeout(() => {
            const replyMessage = {
              id: (Date.now() + 1).toString(),
              content: '收到您发送的图片了',
              time: new Date(),
              isSelf: false,
              avatar: '/static/images/default-avatar.png'
            };
            
            this.messages.push(replyMessage);
            
            // 滚动到最新消息
            this.$nextTick(() => {
              this.scrollToBottom();
            });
          }, 1000);
        }
      });
    },
    
    // 发送文件
    sendFile() {
      // 提示功能开发中
      uni.showToast({
        title: '文件发送功能开发中',
        icon: 'none'
      });
      this.showMorePanel = false;
    },
    
    // 发送名片
    sendContact() {
      // 提示功能开发中
      uni.showToast({
        title: '名片发送功能开发中',
        icon: 'none'
      });
      this.showMorePanel = false;
    },
    
    // 预览图片
    previewImage(url) {
      uni.previewImage({
        urls: [url],
        current: url
      });
    },
    
    // 关闭扩展功能面板
    closeMorePanel() {
      if (this.showMorePanel) {
        this.showMorePanel = false;
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.chat-container {
  min-height: 100vh;
  background-color: #f9f9f9;
  background-image: linear-gradient(rgba(226, 226, 226, 0.3) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(226, 226, 226, 0.3) 1px, transparent 1px);
  background-size: 20px 20px;
  display: flex;
  flex-direction: column;
  padding-top: 0;
  box-sizing: border-box;
  position: relative;
  
  &.has-more-panel {
    padding-bottom: 400rpx;
  }
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(110rpx + var(--status-bar-height));
  padding-top: var(--status-bar-height);
  background: linear-gradient(135deg, #0A84FF, #0040DD);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--status-bar-height) 30rpx 0;
  box-sizing: border-box;
  width: 100%;
}

.navbar-title {
  position: absolute;
  left: 0;
  right: 0;
  color: #FFFFFF;
  font-size: 36rpx;
  font-weight: 700;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  letter-spacing: 1rpx;
}

.navbar-left, .navbar-right {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1000;
}

.back-icon {
  width: 44rpx;
  height: 44rpx;
  filter: brightness(0) invert(1);
}

.safe-area-top {
  height: var(--status-bar-height);
  width: 100%;
  background: linear-gradient(135deg, #0A84FF, #0040DD);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 998;
}

/* 聊天内容区域 */
.chat-content {
  height: 100vh;
  padding: 20rpx 30rpx;
  padding-top: calc(110rpx + var(--status-bar-height) + 20rpx);
  padding-bottom: calc(104rpx + 20rpx);
  box-sizing: border-box;
  width: 100%;
  background-color: #f9f9f9;
}

.load-more {
  text-align: center;
  font-size: 24rpx;
  color: #999999;
  padding: 20rpx 0;
}

.message-list {
  padding-bottom: 20rpx;
  width: 100%;
}

.message-item {
  display: flex;
  margin-bottom: 30rpx;
  width: 100%;
  animation: fadeIn 0.3s ease-out;
  
  &.self {
    flex-direction: row-reverse;
    justify-content: flex-start;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.time-divider {
  width: 100%;
  text-align: center;
  margin: 20rpx 0;
  position: relative;
  left: 0;
  right: 0;
  
  text {
    font-size: 24rpx;
    color: #8e8e93;
    background-color: rgba(142, 142, 147, 0.1);
    padding: 4rpx 12rpx;
    border-radius: 10rpx;
  }
}

.avatar-container {
  margin: 0 20rpx;
  flex-shrink: 0;
  width: 80rpx;
  height: 80rpx;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.message-bubble {
  max-width: 55%;
  padding: 20rpx 24rpx;
  border-radius: 20rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
  word-break: break-all;
  word-wrap: break-word;
  flex-shrink: 1;
  
  &.self {
    background-color: #007AFF;
    color: #ffffff;
    margin-right: 16rpx;
  }
  
  &::before {
    content: '';
    position: absolute;
    top: 20rpx;
    left: -16rpx;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 8rpx 16rpx 8rpx 0;
    border-color: transparent #ffffff transparent transparent;
  }
  
  &.self::before {
    left: auto;
    right: -16rpx;
    border-width: 8rpx 0 8rpx 16rpx;
    border-color: transparent transparent transparent #007AFF;
  }
}

.message-text {
  font-size: 32rpx;
  line-height: 1.5;
  word-break: break-all;
  word-wrap: break-word;
  white-space: normal;
  display: inline-block;
  max-width: 100%;
}

/* 图片消息样式 */
.image-message {
  padding: 0;
  border-radius: 20rpx;
  overflow: hidden;
}

.message-image {
  width: 400rpx;
  max-width: 400rpx;
  border-radius: 20rpx;
}

/* 定位消息样式 */
.location-message {
  display: flex;
  align-items: center;
  padding: 20rpx;
  width: 400rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  
  .location-icon {
    width: 48rpx;
    height: 48rpx;
    margin-right: 16rpx;
    
    image {
      width: 100%;
      height: 100%;
    }
  }
  
  .location-info {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  
  .location-name {
    font-size: 28rpx;
    font-weight: 600;
    margin-bottom: 8rpx;
    color: #333333;
  }
  
  .location-address {
    font-size: 24rpx;
    color: #8e8e93;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .location-arrow {
    width: 24rpx;
    height: 24rpx;
    margin-left: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .arrow-icon {
    font-size: 24rpx;
    color: #8e8e93;
  }
}

/* 输入区域 */
.input-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12rpx 20rpx;
  padding-bottom: calc(12rpx + env(safe-area-inset-bottom));
  background-color: rgba(249, 249, 249, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  z-index: 99;
}

.input-box {
  flex: 1;
  background-color: #f2f2f7;
  border-radius: 36rpx;
  padding: 10rpx 20rpx;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
  height: 80rpx;
  display: flex;
  align-items: center;
}

.input-field {
  width: 100%;
  height: 60rpx;
  font-size: 30rpx;
  line-height: 1.4;
}

.action-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f2f2f7;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  transition: all 0.15s ease;
  position: relative;
  
  &:active {
    transform: scale(0.9);
    background-color: #e0e0e0;
  }
  
  &.has-text {
    background-color: #0A84FF;
  }
}

.send-text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 500;
}

.add-icon {
  width: 30rpx;
  height: 30rpx;
}

.more-icon {
  width: 76rpx;
  height: 76rpx;
  border-radius: 16rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.panel-icon-image {
  width: 30rpx;
  height: 30rpx;
}

.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  width: 100%;
  background-color: #ffffff;
}

/* 抽屉式扩展功能面板 */
.more-panel-mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 90;
}

.more-panel-drawer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 300rpx;
  background-color: #ffffff;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  
  &.show {
    transform: translateY(0);
  }
}

.more-panel-content {
  padding: 20rpx;
  height: 100%;
  box-sizing: border-box;
}

.more-panel-handle {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.handle-line {
  width: 60rpx;
  height: 6rpx;
  background-color: #e0e0e0;
  border-radius: 3rpx;
}

.more-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx 0;
}

.more-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.more-text {
  font-size: 22rpx;
  color: #333333;
  font-weight: 400;
}

.location-icon {
  background-color: #07C160;
  
  image {
    filter: brightness(0) invert(1);
  }
}

.image-icon {
  background-color: #1677FF;
  
  image {
    filter: brightness(0) invert(1);
  }
}

.file-icon {
  background-color: #FA5151;
  
  image {
    filter: brightness(0) invert(1);
  }
}

.contact-icon {
  background-color: #6467F0;
  
  image {
    filter: brightness(0) invert(1);
  }
}
</style> 