<template>
  <view class="detail-container job-detail-container">
    <!-- 添加自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">招聘信息详情</view>
      <view class="navbar-right">
        <!-- 占位 -->
      </view>
    </view>
    
    <!-- 隐藏的分享按钮，用于自动触发 -->
    <button id="shareButton" class="hidden-share-btn" open-type="share"></button>
    
    <!-- 隐藏的Canvas用于绘制海报 -->
    <canvas canvas-id="posterCanvas" class="poster-canvas" style="width: 600px; height: 900px; position: fixed; top: -9999px; left: -9999px;"></canvas>
    
    <!-- 悬浮海报按钮 -->
    <view class="float-poster-btn" @click="generateShareImage">
      <image src="/static/images/tabbar/海报.png" class="poster-icon"></image>
      <text class="poster-text">海报</text>
    </view>
    
    <view class="detail-wrapper job-detail-wrapper">
      <!-- 顶部公司信息卡片 -->
      <view class="content-card company-card">
        <view class="section-title">公司信息</view>
        <view class="publisher-header">
          <view class="avatar-container">
            <image :src="jobData.company.logo" mode="aspectFill" class="avatar-image"></image>
          </view>
          <view class="publisher-info">
            <text class="publisher-name">{{jobData.company.name}}</text>
            <view class="publisher-meta">
              <text class="meta-text">{{jobData.company.type}}</text>
              <text class="meta-text">{{jobData.company.size}}</text>
              <view class="meta-text" v-if="jobData.company.isVerified">
                <text class="iconfont icon-verified"></text>
                <text>已认证</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 职位信息卡片 -->
      <view class="content-card job-card">
        <view class="job-header">
          <view class="title-row">
            <text class="main-title">{{jobData.title}}</text>
            <text class="price-text">{{jobData.salary}}</text>
          </view>
          <view class="meta-info">
            <view class="tag-group">
              <view class="info-tag" v-for="(tag, index) in jobData.tags" :key="index">{{tag}}</view>
            </view>
            <text class="publish-time">发布于 {{formatTime(jobData.publishTime)}}</text>
          </view>
        </view>
        
        <view class="basic-info">
          <view class="info-item">
            <text class="info-label">工作地点</text>
            <text class="info-value">{{jobData.location}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">工作经验</text>
            <text class="info-value">{{jobData.experience}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">学历要求</text>
            <text class="info-value">{{jobData.education}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">招聘人数</text>
            <text class="info-value">{{jobData.headcount}}人</text>
          </view>
        </view>
      </view>
      
      <!-- 职位描述 -->
      <view class="content-card description-card">
        <view class="section-title">职位描述</view>
        <view class="description-content">
          <rich-text :nodes="jobData.description" class="description-text"></rich-text>
        </view>
      </view>
      
      <!-- 岗位职责 -->
      <view class="content-card responsibility-card">
        <view class="section-title">岗位职责</view>
        <view class="job-responsibility">
          <view class="list-item" v-for="(item, index) in jobData.responsibilities" :key="index">
            <text class="list-dot">•</text>
            <text class="list-text">{{item}}</text>
          </view>
        </view>
      </view>
      
      <!-- 任职要求 -->
      <view class="content-card requirement-card">
        <view class="section-title">任职要求</view>
        <view class="job-requirement">
          <view class="list-item" v-for="(item, index) in jobData.requirements" :key="index">
            <text class="list-dot">•</text>
            <text class="list-text">{{item}}</text>
          </view>
        </view>
      </view>
      
      <!-- 福利待遇 -->
      <view class="content-card benefits-card">
        <view class="section-title">福利待遇</view>
        <view class="job-benefits">
          <view class="benefit-tag" v-for="(benefit, index) in jobData.benefits" :key="index">
            <text class="benefit-icon iconfont icon-benefit"></text>
            <text class="benefit-text">{{benefit}}</text>
          </view>
        </view>
      </view>
      
      <!-- 公司地址卡片 -->
      <view class="content-card location-card">
        <view class="section-title">工作地址</view>
        <view class="location-content" @click="openLocation">
          <text class="iconfont icon-location"></text>
          <text class="location-text">{{jobData.address}}</text>
          <text class="iconfont icon-right location-arrow"></text>
        </view>
        <view class="location-map">
          <image src="/static/images/map-preview.png" mode="aspectFill" class="map-preview"></image>
        </view>
      </view>
      
      <!-- 联系方式卡片 -->
      <view class="content-card contact-card">
        <view class="section-title">招聘方联系方式</view>
        <view class="contact-content">
          <view class="contact-item">
            <text class="contact-label">联系人</text>
            <text class="contact-value">{{jobData.contact.name}}</text>
          </view>
          <view class="contact-item">
            <text class="contact-label">电话</text>
            <text class="contact-value contact-phone" @click="callPhone">{{jobData.contact.phone}}</text>
          </view>
          <view class="contact-tips">
            <text class="tips-icon iconfont icon-info"></text>
            <text class="tips-text">请说明在"磁州生活网"看到的信息</text>
          </view>
        </view>
      </view>
      
      <!-- 红包区域 -->
      <view class="content-card red-packet-card" v-if="jobData.hasRedPacket">
        <view class="section-title">红包福利</view>
        <view class="red-packet-section">
          <view class="red-packet-container" @click="openRedPacket">
            <view class="red-packet-blur-bg"></view>
            <view class="red-packet-content">
              <view class="red-packet-left">
                <image class="red-packet-icon" src="/static/images/tabbar/抢红包.gif"></image>
              <view class="red-packet-info">
                <view class="red-packet-title">
                  {{jobData.redPacket.type === 'random' ? '随机金额红包' : '查看职位领红包'}}
                </view>
                <view class="red-packet-desc">
                  还剩{{jobData.redPacket.remain}}个，{{getRedPacketConditionText()}}
                  </view>
                </view>
              </view>
              <view class="red-packet-right">
                <view class="red-packet-amount"><text class="prefix">共</text> ¥{{jobData.redPacket.amount}}</view>
                <view class="grab-btn">立即领取</view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 相关招聘信息卡片 - 默认显示 -->
      <view class="content-card related-jobs-card">
        <!-- 标题栏 -->
        <view class="collapsible-header">
          <view class="section-title">相关招聘推荐</view>
        </view>
        
        <!-- 内容区 -->
        <view class="collapsible-content">
          <!-- 简洁的职位列表 -->
          <view class="related-jobs-list">
            <view class="related-job-item" 
                 v-for="(job, index) in relatedJobs.slice(0, 3)" 
                 :key="index" 
                 @click="navigateToJobDetail(job.id)">
              <view class="job-item-content">
                <view class="job-item-left">
                  <image class="company-logo" :src="job.companyLogo" mode="aspectFill"></image>
                </view>
                <view class="job-item-middle">
                  <text class="job-item-title">{{job.title}}</text>
                  <view class="job-item-company">{{job.companyName}}</view>
                  <view class="job-item-tags">
                    <text class="job-item-tag" v-for="(tag, tagIndex) in job.tags.slice(0, 2)" :key="tagIndex">{{tag}}</text>
                    <text class="job-item-tag-more" v-if="job.tags.length > 2">+{{job.tags.length - 2}}</text>
                  </view>
                </view>
                <view class="job-item-right">
                  <text class="job-item-salary">{{job.salary}}</text>
                </view>
              </view>
            </view>
            
            <!-- 暂无数据提示 -->
            <view class="empty-related-jobs" v-if="relatedJobs.length === 0">
              <image src="/static/images/empty.png" class="empty-image" mode="aspectFit"></image>
              <text class="empty-text">暂无相关招聘</text>
            </view>
          </view>
          
          <!-- 查看更多按钮 -->
          <view class="view-more-btn" v-if="relatedJobs.length > 0" @click.stop="navigateToJobList">
            <text class="view-more-text">查看更多招聘信息</text>
            <text class="view-more-icon iconfont icon-right"></text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="interaction-toolbar">
      <view class="toolbar-item" @click="goToHome">
        <image src="/static/images/tabbar/a首页.png" class="toolbar-icon"></image>
        <text class="toolbar-text">首页</text>
      </view>
      <view class="toolbar-item" :class="{'active-toolbar-item': isFollowed}" @click="toggleFollow">
        <image :src="isFollowed ? '/static/images/tabbar/已收藏选中.png' : '/static/images/tabbar/a关注.png'" class="toolbar-icon"></image>
        <text class="toolbar-text">{{isFollowed ? '已关注' : '关注'}}</text>
      </view>
      <button class="share-button toolbar-item" open-type="share">
        <image src="/static/images/tabbar/a分享.png" class="toolbar-icon"></image>
        <text class="toolbar-text">分享</text>
      </button>
      <view class="toolbar-item" @click="showCommentInput">
        <image src="/static/images/tabbar/a消息.png" class="toolbar-icon"></image>
        <text class="toolbar-text">评论</text>
      </view>
      <view class="toolbar-item call-button" @click="callPhone">
        <view class="call-button-content">
          <text class="call-text">打电话</text>
          <text class="call-subtitle">请说在磁州生活网看到的</text>
        </view>
      </view>
    </view>
    
    <!-- 红包弹窗 -->
    <view class="red-packet-popup" v-if="showRedPacket">
      <view class="red-packet-popup-wrapper" @click.stop>
        <view class="red-packet-task-modal" v-if="!redPacketGrabbed">
          <view class="red-packet-modal-header">
            <text class="red-packet-modal-title">完成任务领取红包</text>
            <text class="red-packet-modal-close" @click="closeRedPacket">×</text>
            </view>
          <view class="red-packet-modal-amount">¥{{jobData.redPacket.amount}}</view>
          
          <view class="red-packet-task-list">
            <view class="red-packet-task-item" :class="{'task-completed': isShared}" v-if="jobData.redPacket.conditions.includes('转发分享')">
              <view class="task-icon-container" :class="{'completed-icon-container': isShared}">
                <image class="task-icon" src="/static/images/tabbar/分享.png"></image>
            </view>
              <view class="task-info">
                <view class="task-title">转发分享</view>
                <view class="task-desc">分享给好友，{{shareViewCount}}/10人查看</view>
          </view>
              <button class="task-action-btn" :class="{'completed-btn': isShared}" open-type="share">
                {{isShared ? '已分享' : '去分享'}}
              </button>
          </view>
          </view>
          
          <view class="red-packet-get-btn" :class="{'red-packet-get-btn-active': canGrabRedPacket}" @click="grabRedPacket">
            {{canGrabRedPacket ? '立即领取' : '完成任务领取'}}
          </view>
          
          <view class="red-packet-tip">
            <text class="red-packet-tip-text">*完成以上任务即可领取红包</text>
          </view>
        </view>
        
        <!-- 抢到红包后的弹窗 -->
        <view class="red-packet-success-modal" v-if="redPacketGrabbed">
          <image class="success-bg" src="/static/images/tabbar/红包弹窗背景.png" mode="aspectFill"></image>
          <view class="success-content">
            <view class="success-title">恭喜您抢到红包</view>
            <view class="success-amount">¥{{grabbedAmount}}</view>
            <view class="success-desc">红包已存入您的钱包</view>
            <view class="success-btn-group">
              <view class="success-btn success-btn-wallet" @click="goToWallet">查看我的钱包</view>
              <view class="success-btn" @click="closeRedPacket">关闭</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 评论输入区域 -->
    <view class="comment-popup" v-if="showCommentArea">
      <view class="comment-mask" @click="showCommentArea = false"></view>
      <view class="comment-container">
        <view class="comment-header">
          <text class="comment-title">发表评论</text>
          <text class="comment-close" @click="showCommentArea = false">×</text>
        </view>
        <view class="comment-body">
          <textarea class="comment-textarea" v-model="commentContent" placeholder="说点什么吧..." maxlength="200" auto-focus auto-height></textarea>
          <view class="comment-footer">
            <text class="comment-count">{{commentContent.length}}/200</text>
            <button class="comment-submit" @click="submitComment">发布</button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from 'vue'

// 状态栏高度
const statusBarHeight = ref(20);

// 获取状态栏高度
onMounted(() => {
  try {
    const sysInfo = uni.getSystemInfoSync();
    statusBarHeight.value = sysInfo.statusBarHeight || 20;
  } catch (e) {
    console.error('获取状态栏高度失败', e);
  }
});

// 返回上一页
const goBack = () => {
  uni.navigateBack({
    fail: () => {
      uni.switchTab({
        url: '/pages/index/index'
      });
    }
  });
};

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  return `${date.getMonth() + 1}月${date.getDate()}日`;
};

// 响应式数据
const isCollected = ref(false);
const isFollowed = ref(false);
const isShared = ref(false);
const isCommented = ref(false);
const showRedPacket = ref(false);
const redPacketGrabbed = ref(false);
const grabbedAmount = ref('0.00');
const showCommentArea = ref(false);
const commentContent = ref('');
const shareViewCount = ref(0);

// 计算红包是否可以领取
const canGrabRedPacket = computed(() => {
  // 如果没有条件，直接可以领取
  if (!jobData.value.redPacket.conditions || jobData.value.redPacket.conditions.length === 0) {
    return true;
  }
  
  // 检查所有条件是否都满足
  let allConditionsMet = true;
  
  // 检查转发分享条件
  if (jobData.value.redPacket.conditions.includes('转发分享')) {
    if (!isShared.value || shareViewCount.value < 10) {
      allConditionsMet = false;
    }
  }
  
  // 检查关注店铺条件
  if (jobData.value.redPacket.conditions.includes('关注店铺')) {
    if (!isFollowed.value) {
      allConditionsMet = false;
    }
  }
  
  return allConditionsMet;
});

const jobData = ref({
  id: 'job12345',
  title: '前端开发工程师',
  salary: '8000-12000元/月',
  tags: ['五险一金', '双休', '包吃住'],
  publishTime: Date.now() - 86400000 * 2, // 2天前
  location: '磁县城区',
  experience: '1-3年',
  education: '大专及以上',
  headcount: 3,
  description: '负责公司产品的前端开发工作，包括但不限于PC端、移动端的页面开发和交互实现。与后端开发人员密切合作，确保前后端数据交互的顺畅和高效。参与产品需求分析和功能设计讨论，提供前端技术可行性建议。',
  responsibilities: [
    '负责公司Web前端页面的设计和开发',
    '根据产品需求，分析并给出最优的页面前端结构解决方案',
    '与后端工程师协作，完成数据交互、接口联调',
    '对现有项目进行性能优化，提升用户体验',
    '关注前端技术的发展，根据业务需求引入新技术'
  ],
  requirements: [
    '计算机相关专业，大专及以上学历',
    '1年以上前端开发经验，熟悉HTML5、CSS3、JavaScript',
    '熟悉Vue、React等主流前端框架至少一种',
    '了解响应式布局和移动端适配',
    '有良好的代码风格，重视代码质量',
    '具备良好的沟通能力和团队协作精神'
  ],
  benefits: [
    '五险一金',
    '双休',
    '带薪年假',
    '节日福利',
    '定期团建',
    '免费工作餐',
    '晋升空间'
  ],
  address: '河北省邯郸市磁县北关镇磁州大厦5层',
  contact: {
    name: '张经理',
    phone: '13912345678'
  },
  company: {
    name: '磁县科技有限公司',
    logo: '/static/images/company-logo.png',
    type: '互联网',
    size: '50-100人',
    isVerified: true
  },
  // 红包相关数据
  hasRedPacket: true,
  redPacket: {
    amount: '10.00',
    remain: 8,
    type: 'fixed', // fixed:固定金额 random:随机金额
    total: 20,
    validity: '2024-06-30',
    description: '感谢您查看我们的招聘信息',
    conditions: ['转发分享'] // 领取条件
  }
});

// 获取红包条件文本
const getRedPacketConditionText = () => {
  const conditions = jobData.value.redPacket.conditions || [];
  if (!conditions || conditions.length === 0) {
    return '投递简历再领一个';
  }
  
  // 最多显示两个条件，其余用"等"表示
  if (conditions.length === 1) {
    return `${conditions[0]}可再领`;
  } else if (conditions.length === 2) {
    return `${conditions[0]}、${conditions[1]}可再领`;
  } else {
    return `${conditions[0]}、${conditions[1]}等可再领`;
  }
};

// 方法
const toggleCollect = () => {
  isCollected.value = !isCollected.value;
  if (isCollected.value) {
    uni.showToast({
      title: '收藏成功',
      icon: 'success'
    });
  }
};

// 打开红包弹窗
const openRedPacket = () => {
  showRedPacket.value = true;
};

// 关闭红包弹窗
const closeRedPacket = () => {
  showRedPacket.value = false;
};

// 抢红包
const grabRedPacket = () => {
  // 检查条件是否满足
  if (!checkRedPacketConditions()) {
    return;
  }
  
  // 模拟抢红包过程
  uni.showLoading({
    title: '正在拆红包...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    redPacketGrabbed.value = true;
    
    // 根据红包类型生成金额
    if (jobData.value.redPacket.type === 'random') {
      // 随机金额红包，在红包金额的50%-150%之间随机
      const baseAmount = parseFloat(jobData.value.redPacket.amount);
      const min = baseAmount * 0.5;
      const max = baseAmount * 1.5;
      grabbedAmount.value = (min + Math.random() * (max - min)).toFixed(2);
    } else {
      // 固定金额红包
      grabbedAmount.value = jobData.value.redPacket.amount;
    }
    
    // 更新剩余数量
    jobData.value.redPacket.remain -= 1;
  }, 1000);
};

// 检查红包领取条件
const checkRedPacketConditions = () => {
  const conditions = jobData.value.redPacket.conditions || [];
  if (!conditions || conditions.length === 0) {
    return true; // 没有条件限制
  }

  // 获取用户信息
  const userInfo = uni.getStorageSync('userInfo');
  if (!userInfo) {
    uni.showModal({
      title: '提示',
      content: '请先登录后再领取红包',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pages/login/login'
          });
        }
      }
    });
    return false;
  }

  // 检查各项条件
  let hasUnmetConditions = false;
  let unmetConditionsText = '';

  // 检查是否转发
  if (conditions.includes('转发分享')) {
    const hasShared = uni.getStorageSync(`shared_${jobData.value.id}`) || false;
    const shareViewCount = uni.getStorageSync(`share_views_${jobData.value.id}`) || 0;
    
    if (!hasShared) {
      hasUnmetConditions = true;
      unmetConditionsText += '- 请先转发分享此招聘信息\n';
    } else if (shareViewCount < 10) {
      hasUnmetConditions = true;
      unmetConditionsText += `- 您的分享需要至少10人查看(当前${shareViewCount}人)\n`;
    }
  }

  // 如果有未满足的条件，显示提示
  if (hasUnmetConditions) {
    uni.showModal({
      title: '无法领取红包',
      content: `请先完成以下条件:\n${unmetConditionsText}`,
      showCancel: false
    });
    return false;
  }

  return true;
};

// 设置已分享标记(在分享成功回调中调用)
const markAsShared = () => {
  uni.setStorageSync(`shared_${jobData.value.id}`, true);
};

// 增加分享查看次数(在其他用户通过分享链接查看时调用)
const increaseShareViewCount = () => {
  const currentCount = uni.getStorageSync(`share_views_${jobData.value.id}`) || 0;
  uni.setStorageSync(`share_views_${jobData.value.id}`, currentCount + 1);
};

// 设置已评论标记
const markAsCommented = () => {
  uni.setStorageSync(`commented_${jobData.value.id}`, true);
};

// 设置已关注标记
const markAsFollowed = () => {
  uni.setStorageSync(`followed_${jobData.value.company.id}`, true);
};

// 前往钱包页面
const goToWallet = () => {
  uni.navigateTo({
    url: '/subPackages/payment/pages/wallet'
  });
};

const callPhone = () => {
  uni.makePhoneCall({
    phoneNumber: jobData.value.contact.phone,
    fail: () => {
      uni.showToast({
        title: '拨打电话失败',
        icon: 'none'
      });
    }
  });
};

const openLocation = () => {
  uni.showToast({
    title: '查看地图位置',
    icon: 'none'
  });
};

// 跳转到首页
const goToHome = () => {
  uni.switchTab({
    url: '/pages/index/index'
  });
};

// 打开私信聊天
const openChat = () => {
  if (!jobData.value.company || !jobData.value.company.id) {
    uni.showToast({
      title: '无法获取招聘方信息',
      icon: 'none'
    });
    return;
  }
  
  // 跳转到聊天页面
  uni.navigateTo({
    url: `/pages/chat/index?userId=${jobData.value.company.id || 'temp_id'}&username=${encodeURIComponent(jobData.value.company.name || '招聘方')}`
  });
};

// 海报相关数据
const posterImagePath = ref('');
const showPosterFlag = ref(false);

// 生成海报的方法
const generateShareImage = () => {
  uni.showLoading({
    title: '正在生成海报...',
    mask: true
  });
  
  // 创建海报数据对象
  const posterData = {
    title: jobData.value.title,
    salary: jobData.value.salary,
    company: jobData.value.company.name,
    address: jobData.value.address,
    phone: jobData.value.contact.phone,
    requirements: jobData.value.requirements ? jobData.value.requirements.substring(0, 60) + '...' : '',
    qrcode: '/static/images/tabbar/客服微信.png',
    logo: jobData.value.company.logo,
    bgImage: '/static/images/banner/banner-1.png'
  };
  
  // #ifdef H5
  // H5环境不支持canvas绘制图片保存，提示用户
  setTimeout(() => {
    uni.hideLoading();
    uni.showModal({
      title: '提示',
      content: 'H5环境暂不支持保存海报，请使用App或小程序',
      showCancel: false
    });
  }, 1000);
  return;
  // #endif
  
  // 绘制海报
  const ctx = uni.createCanvasContext('posterCanvas');
  
  // 绘制背景
  ctx.save();
  ctx.drawImage(posterData.bgImage, 0, 0, 600, 400);
  // 添加半透明蒙层
  ctx.setFillStyle('rgba(0, 0, 0, 0.35)');
  ctx.fillRect(0, 0, 600, 900);
  ctx.restore();
  
  // 绘制白色卡片背景
  ctx.save();
  ctx.setFillStyle('#ffffff');
  ctx.fillRect(30, 280, 540, 550);
  ctx.restore();
  
  // 绘制Logo
  ctx.save();
  ctx.beginPath();
  ctx.arc(300, 200, 80, 0, 2 * Math.PI);
  ctx.setFillStyle('#ffffff');
  ctx.fill();
  // 在圆形内绘制Logo
  ctx.clip();
  ctx.drawImage(posterData.logo, 220, 120, 160, 160);
  ctx.restore();
  
  // 绘制岗位名称
  ctx.setFillStyle('#333333');
  ctx.setFontSize(32);
  ctx.setTextAlign('center');
  ctx.fillText(posterData.title, 300, 350);
  
  // 绘制薪资
  ctx.setFillStyle('#FF6B6B');
  ctx.setFontSize(28);
  ctx.fillText(posterData.salary, 300, 400);
  
  // 分割线
  ctx.beginPath();
  ctx.setStrokeStyle('#eeeeee');
  ctx.setLineWidth(2);
  ctx.moveTo(100, 430);
  ctx.lineTo(500, 430);
  ctx.stroke();
  
  // 绘制公司名称
  ctx.setFillStyle('#666666');
  ctx.setFontSize(24);
  ctx.setTextAlign('left');
  ctx.fillText('公司: ' + posterData.company, 80, 480);
  
  // 绘制工作地点
  ctx.fillText('地址: ' + posterData.address, 80, 520);
  
  // A wrap text function
  const wrapText = (ctx, text, x, y, maxWidth, lineHeight) => {
    if (text.length === 0) return;
    
    const words = text.split('');
    let line = '';
    let testLine = '';
    let lineCount = 0;
    
    for (let n = 0; n < words.length; n++) {
      testLine += words[n];
      const metrics = ctx.measureText(testLine);
      const testWidth = metrics.width;
      
      if (testWidth > maxWidth && n > 0) {
        ctx.fillText(line, x, y + (lineCount * lineHeight));
        line = words[n];
        testLine = words[n];
        lineCount++;
        
        if (lineCount >= 3) {
          line += '...';
          ctx.fillText(line, x, y + (lineCount * lineHeight));
          break;
        }
      } else {
        line = testLine;
      }
    }
    
    if (lineCount < 3) {
      ctx.fillText(line, x, y + (lineCount * lineHeight));
    }
  };
  
  // 绘制岗位要求
  ctx.setFillStyle('#666666');
  ctx.fillText('岗位要求:', 80, 560);
  wrapText(ctx, posterData.requirements, 80, 600, 440, 35);
  
  // 绘制电话
  if (posterData.phone) {
    ctx.fillText('联系电话: ' + posterData.phone, 80, 680);
  }
  
  // 绘制小程序码
  ctx.drawImage(posterData.qrcode, 225, 720, 150, 150);
  
  // 提示文字
  ctx.setFillStyle('#999999');
  ctx.setFontSize(20);
  ctx.setTextAlign('center');
  ctx.fillText('长按识别二维码查看详情', 300, 880);
  
  // 应用平台Logo
  ctx.setFillStyle('#333333');
  ctx.setFontSize(24);
  ctx.fillText('磁县同城 - 招聘信息', 300, 840);
  
  // 绘制完成，输出图片
  ctx.draw(false, () => {
    setTimeout(() => {
      // 延迟确保canvas已完成渲染
      uni.canvasToTempFilePath({
        canvasId: 'posterCanvas',
        success: (res) => {
          uni.hideLoading();
          showPosterModal(res.tempFilePath);
        },
        fail: (err) => {
          console.error('生成海报失败', err);
          uni.hideLoading();
          uni.showToast({
            title: '生成海报失败',
            icon: 'none'
          });
        }
      });
    }, 800);
  });
};

// 显示海报预览和保存选项
const showPosterModal = (posterPath) => {
  posterImagePath.value = posterPath;
  showPosterFlag.value = true;
  
  uni.showModal({
    title: '海报已生成',
    content: '海报已生成，是否保存到相册？',
    confirmText: '保存',
    success: (res) => {
      if (res.confirm) {
        savePosterToAlbum(posterPath);
      } else {
        // 预览图片
        uni.previewImage({
          urls: [posterPath],
          current: posterPath
        });
      }
    }
  });
};

// 保存海报到相册
const savePosterToAlbum = (posterPath) => {
  uni.showLoading({
    title: '正在保存...'
  });
  
  uni.saveImageToPhotosAlbum({
    filePath: posterPath,
    success: () => {
      uni.hideLoading();
      uni.showToast({
        title: '已保存到相册',
        icon: 'success'
      });
    },
    fail: (err) => {
      uni.hideLoading();
      console.error('保存失败', err);
      
      if (err.errMsg.indexOf('auth deny') > -1) {
        uni.showModal({
          title: '提示',
          content: '保存失败，请授权相册权限后重试',
          confirmText: '去设置',
          success: (res) => {
            if (res.confirm) {
              uni.openSetting();
            }
          }
        });
      } else {
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }
    }
  });
};

// 相关职位列表功能
const relatedJobs = ref([]);

// 加载相关招聘信息
const loadRelatedJobs = () => {
  // 这里可以调用API获取数据
  // 实际项目中应该根据当前招聘信息的分类、标签等进行相关性匹配
  
  // 模拟数据
  setTimeout(() => {
    relatedJobs.value = [
      {
        id: 'job001',
        title: '销售经理',
        salary: '6000-8000',
        companyName: '磁州科技有限公司',
        companyLogo: '/static/images/tabbar/公司.png',
        tags: ['五险一金', '包吃住', '加班补助']
      },
      {
        id: 'job002',
        title: '前台文员',
        salary: '3500-4500',
        companyName: '磁州商贸有限公司',
        companyLogo: '/static/images/tabbar/企业.png',
        tags: ['五险', '双休', '有食堂']
      },
      {
        id: 'job003',
        title: '网络销售',
        salary: '4000-8000',
        companyName: '磁州网络科技公司',
        companyLogo: '/static/images/tabbar/网络.png',
        tags: ['高提成', '弹性工作', '有培训']
      }
    ];
  }, 500);
};

// 跳转到详情页
const navigateToJobDetail = (id) => {
  // 避免重复跳转当前页面
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};
  
  if (id === options.id) {
    return;
  }
  
  uni.navigateTo({
    url: `/pages/publish/job-detail?id=${id}`
  });
};

// 跳转到招聘列表页
const navigateToJobList = (e) => {
  if (e) e.stopPropagation();
  const jobCategory = jobData.value.tags?.[0] || '';
  uni.navigateTo({
    url: `/subPackages/service/pages/filter?type=job&title=${encodeURIComponent('招聘信息')}&category=${encodeURIComponent(jobCategory)}&active=job`
  });
};

// 生命周期钩子
onMounted(() => {
  // 修改页面标题
  uni.setNavigationBarTitle({
    title: '招聘详情'
  });
  
  // 获取路由参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};
  
  // 如果有ID参数，则根据ID获取详情
  if (options.id) {
    console.log('正在获取招聘详情，ID:', options.id);
    // 实际开发中这里应该调用API获取详情数据
    // fetchJobDetail(options.id).then(data => {
    //   jobData.value = data;
    // });
    
    // 获取各种状态
    isFollowed.value = uni.getStorageSync(`followed_${jobData.value.company.id}`) || false;
    isShared.value = uni.getStorageSync(`shared_${jobData.value.id}`) || false;
    isCommented.value = uni.getStorageSync(`commented_${jobData.value.id}`) || false;
    
    // 获取分享查看次数
    shareViewCount.value = uni.getStorageSync(`share_views_${jobData.value.id}`) || 0;
    
    // 如果是通过分享链接访问的，增加分享查看次数
    if (options.source === 'share') {
      increaseShareViewCount();
      shareViewCount.value += 1;
    }
  }
  
  // 加载相关职位数据
  loadRelatedJobs();
});

// 切换关注状态
const toggleFollow = () => {
  if (!checkLoginStatus()) {
    return;
  }
  
  isFollowed.value = !isFollowed.value;
  
  if (isFollowed.value) {
    // 关注成功
    markAsFollowed();
    uni.showToast({
      title: '关注成功',
      icon: 'success'
    });
  } else {
    // 取消关注
    uni.removeStorageSync(`followed_${jobData.value.company.id}`);
    uni.showToast({
      title: '已取消关注',
      icon: 'none'
    });
  }
};

// 显示评论输入框
const showCommentInput = () => {
  if (!checkLoginStatus()) {
    return;
  }
  
  showCommentArea.value = true;
};

// 提交评论
const submitComment = () => {
  if (commentContent.value.trim() === '') {
    uni.showToast({
      title: '评论内容不能为空',
      icon: 'none'
    });
    return;
  }
  
  uni.showLoading({
    title: '提交中...'
  });
  
  // 模拟提交评论
  setTimeout(() => {
    uni.hideLoading();
    
    // 标记为已评论
    markAsCommented();
    
    uni.showToast({
      title: '评论成功',
      icon: 'success'
    });
    
    // 清空评论内容并隐藏评论区
    commentContent.value = '';
    showCommentArea.value = false;
  }, 800);
};

// 检查登录状态
const checkLoginStatus = () => {
  const userInfo = uni.getStorageSync('userInfo');
  if (!userInfo) {
    uni.showModal({
      title: '提示',
      content: '请先登录后再操作',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pages/login/login'
          });
        }
      }
    });
    return false;
  }
  return true;
};

// 分享设置
const onShareAppMessage = (res) => {
  markAsShared(); // 标记为已分享
  
  return {
    title: jobData.value.title + ' - ' + jobData.value.salary,
    path: `/pages/publish/job-detail?id=${jobData.value.id}&source=share`,
    imageUrl: '/static/images/share-job-poster.png', // 分享图片
    success: function() {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      });
    }
  };
};
</script>

<style>
@import './common-detail-style.css';

/* 招聘详情页的特殊样式 */
.job-detail-container {
  /* 可以添加特定于招聘详情页的样式 */
}

/* 岗位职责和任职要求列表样式 */
.job-responsibility,
.job-requirement {
  padding: 16rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}

.list-item {
  display: flex;
  margin-bottom: 16rpx;
}

.list-item:last-child {
  margin-bottom: 0;
}

.list-dot {
  font-size: 28rpx;
  color: #1890ff;
  margin-right: 12rpx;
  line-height: 1.6;
}

.list-text {
  font-size: 28rpx;
  color: #555;
  line-height: 1.6;
  flex: 1;
}

/* 福利标签样式 */
.job-benefits {
  display: flex;
  flex-wrap: wrap;
  padding: 16rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}

.benefit-tag {
  display: flex;
  align-items: center;
  background-color: #f6ffed;
  border: 1rpx solid #b7eb8f;
  border-radius: 6rpx;
  padding: 8rpx 16rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}

.benefit-icon {
  font-size: 24rpx;
  color: #52c41a;
  margin-right: 8rpx;
}

.benefit-text {
  font-size: 24rpx;
  color: #52c41a;
}

/* 工作地址地图样式 */
.location-content {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
}

.location-text {
  flex: 1;
  font-size: 28rpx;
  color: #555;
  margin: 0 12rpx;
}

.location-arrow {
  font-size: 24rpx;
  color: #999;
}

.location-map {
  height: 240rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.map-preview {
  width: 100%;
  height: 100%;
}

/* 隐藏的分享按钮 */
.hidden-share-btn {
  position: fixed;
  width: 2rpx;
  height: 2rpx;
  opacity: 0;
  top: -999rpx;
  left: -999rpx;
  z-index: -1;
  overflow: hidden;
  padding: 0;
  margin: 0;
  border: none;
}

.hidden-share-btn::after {
  display: none;
}

/* 悬浮海报按钮 */
.float-poster-btn {
  position: fixed;
  right: 30rpx;
  bottom: 200rpx;
  width: 100rpx;
  height: 100rpx;
  background: rgba(240, 240, 240, 0.9);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.08);
  z-index: 90;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1rpx solid rgba(230, 230, 230, 0.6);
  transition: all 0.2s ease;
}

.float-poster-btn:active {
  transform: scale(0.95);
  box-shadow: 0 3rpx 10rpx rgba(0,0,0,0.08);
}

.poster-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 4rpx;
}

.poster-text {
  font-size: 20rpx;
  color: #444;
  line-height: 1;
}

.job-detail-wrapper {
  padding: 24rpx;
  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */
}

/* 相关招聘信息卡片样式 */
.related-jobs-card {
  margin-top: 12px;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 可折叠标题栏样式 */
.collapsible-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #fff;
  position: relative;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 内容区样式 */
.collapsible-content {
  padding: 0 16px 16px;
  overflow: hidden;
}

/* 相关职位列表样式 */
.related-jobs-list {
  margin-bottom: 12px;
}

.related-job-item {
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.related-job-item:last-child {
  border-bottom: none;
}

.job-item-content {
  display: flex;
  align-items: center;
}

.job-item-left {
  margin-right: 12px;
}

.company-logo {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #f5f7fa;
}

.job-item-middle {
  flex: 1;
  overflow: hidden;
}

.job-item-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.job-item-company {
  font-size: 13px;
  color: #666;
  margin-bottom: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.job-item-tags {
  display: flex;
  flex-wrap: wrap;
}

.job-item-tag {
  font-size: 11px;
  color: #0066ff;
  background-color: rgba(0, 102, 255, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  margin-right: 6px;
}

.job-item-tag-more {
  font-size: 11px;
  color: #999;
}

.job-item-right {
  min-width: 80px;
  text-align: right;
}

.job-item-salary {
  font-size: 15px;
  font-weight: 500;
  color: #ff5252;
}

/* 查看更多按钮样式 */
.view-more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  background-color: #f7f9fc;
  border-radius: 8px;
  margin-top: 8px;
}

.view-more-text {
  font-size: 14px;
  color: #0066ff;
}

.view-more-icon {
  margin-left: 4px;
  font-size: 12px;
  color: #0066ff;
}

/* 空数据提示样式 */
.empty-related-jobs {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 0;
}

.empty-image {
  width: 80px;
  height: 80px;
  margin-bottom: 12px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 红包区域样式 */
.red-packet-card {
  background-color: #fff;
  margin: 20rpx 20rpx 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.08);
}

.red-packet-section {
  padding: 16rpx;
}

.red-packet-container {
  position: relative;
  height: 160rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(255, 90, 95, 0.15);
}

.red-packet-blur-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
  background: linear-gradient(to left, #FF5A5F, #FF8A8E);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.red-packet-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 30rpx;
}

.red-packet-left {
  flex: 1;
  display: flex;
  align-items: center;
}

.red-packet-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.red-packet-info {
  flex: 1;
}

.red-packet-title {
  font-size: 32rpx;
  color: #FFFFFF;
  font-weight: bold;
  margin-bottom: 8rpx;
  text-shadow: none;
}

.red-packet-desc {
  font-size: 20rpx;
  color: #FFFFFF;
  font-weight: bold;
  text-shadow: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.red-packet-right {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.red-packet-amount {
  font-size: 36rpx;
  color: #FFFFFF;
  font-weight: bold;
  margin-bottom: 10rpx;
  text-shadow: none;
}

.red-packet-amount .prefix {
  font-size: 28rpx;
  margin-right: 4rpx;
}

.grab-btn {
  background-color: #FFFFFF;
  color: #F05A5F;
  font-size: 24rpx;
  padding: 12rpx 32rpx;
  border-radius: 30rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.red-packet-validity, 
.red-packet-description {
  margin-top: 12rpx;
  padding: 0 16rpx;
}

.validity-text, 
.description-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

.red-packet-description {
  margin-bottom: 10rpx;
}

/* 红包弹窗 */
.red-packet-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.red-packet-popup-wrapper {
  position: relative;
  width: 600rpx;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
}

.red-packet-task-modal {
  position: relative;
  width: 100%;
  padding: 40rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
}

.red-packet-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: calc(100% - 60rpx);
  padding: 0 0;
  margin-bottom: 30rpx;
  box-sizing: border-box;
}

.red-packet-modal-title {
  font-size: 36rpx;
  color: #333;
  font-weight: 600;
  flex-grow: 1;
  text-align: center;
  padding-right: 60rpx; /* 为关闭按钮留出空间 */
}

.red-packet-modal-close {
  font-size: 40rpx;
  color: #999;
  cursor: pointer;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.red-packet-modal-amount {
  font-size: 72rpx;
  color: #FF5A5F;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
  width: 100%; /* 确保金额文本占据全部宽度以便自身居中 */
}

.red-packet-task-list {
  background-color: #F8F8F8;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  width: calc(100% - 60rpx); /* 减去弹窗左右内边距 */
  box-sizing: border-box;
}

.red-packet-task-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #FFFFFF;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}

.red-packet-task-item:last-child {
  margin-bottom: 0;
}

.task-completed {
  background-color: #F6FFED;
  border: 1rpx solid #B7EB8F;
}

.task-icon-container {
  width: 48rpx;
  height: 48rpx;
  background-color: #FFECEC;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.completed-icon-container {
  background-color: #F6FFED;
}

.task-icon {
  width: 28rpx;
  height: 28rpx;
}

.task-info {
  flex: 1;
}

.task-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.task-desc {
  font-size: 24rpx;
  color: #999;
}

.task-action-btn {
  background-color: #FF5A5F;
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 12rpx 30rpx;
  border-radius: 30rpx;
  font-weight: 600;
  margin-left: 20rpx;
  border: none;
  outline: none;
  flex-shrink: 0;
  width: 140rpx; /* 设置固定宽度 */
  text-align: center;
}

.completed-btn {
  background-color: #52C41A;
}

.red-packet-get-btn {
  background-color: rgba(255, 90, 95, 0.3);
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 600;
  padding: 24rpx 0;
  border-radius: 16rpx;
  text-align: center;
  margin-bottom: 20rpx;
  transition: all 0.3s;
  width: calc(100% - 60rpx); /* 减去弹窗左右内边距 */
}

.red-packet-get-btn-active {
  background-color: #FF5A5F;
  box-shadow: 0 4rpx 12rpx rgba(255, 90, 95, 0.3);;
}

.red-packet-tip {
  text-align: center;
  width: 100%;
}

.red-packet-tip-text {
  font-size: 24rpx;
  color: #999;
}

.red-packet-success-modal {
  position: relative;
  overflow: hidden;
  padding: 40rpx;
  text-align: center;
}

.success-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

.success-content {
  position: relative;
  z-index: 1;
  padding: 30rpx 0;
}

.success-title {
  font-size: 40rpx;
  color: #FFD700;
  font-weight: bold;
  margin-bottom: 30rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.success-amount {
  font-size: 80rpx;
  color: #FFD700;
  font-weight: bold;
  margin-bottom: 30rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.success-desc {
  font-size: 28rpx;
  color: #FFFFFF;
  margin-bottom: 60rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.success-btn-group {
  display: flex;
  justify-content: center;
  gap: 30rpx;
}

.success-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: #FFFFFF;
  font-size: 28rpx;
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.success-btn-wallet {
  background-color: #FFD700;
  color: #333;
}

/* 评论输入区域样式 */
.comment-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.comment-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
}

.comment-container {
  position: relative;
  width: 80%;
  max-width: 600px;
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.comment-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.comment-close {
  font-size: 20px;
  color: #999;
  cursor: pointer;
}

.comment-body {
  margin-bottom: 20px;
}

.comment-textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 10px;
}

.comment-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comment-count {
  font-size: 14px;
  color: #999;
}

.comment-submit {
  background-color: #0066ff;
  color: #fff;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

/* 活跃工具栏项样式 */
.active-toolbar-item {
  color: #0066ff;
}

.active-toolbar-item .toolbar-text {
  color: #0066ff;
}

/* 自定义导航栏样式 */
.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}

.navbar-left {
  width: 60px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 20px;
  height: 20px;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 500;
  color: #fff;
}

.navbar-right {
  width: 60px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style> 