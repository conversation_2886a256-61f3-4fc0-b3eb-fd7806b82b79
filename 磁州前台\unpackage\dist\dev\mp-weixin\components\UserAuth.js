"use strict";
const common_vendor = require("../common/vendor.js");
const utils_userProfile = require("../utils/userProfile.js");
const _sfc_main = {
  __name: "UserAuth",
  props: {
    buttonText: {
      type: String,
      default: "获取头像昵称"
    },
    autoAuth: {
      type: Boolean,
      default: true
      // 默认开启自动授权
    }
  },
  emits: ["update:userInfo"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const mpWeixin = common_vendor.ref(false);
    const userInfoExists = common_vendor.ref(false);
    const showNicknamePopup = common_vendor.ref(false);
    const selectedAvatar = common_vendor.ref("");
    const nickname = common_vendor.ref("");
    const hasUserInfo = common_vendor.computed(() => {
      return userInfoExists.value;
    });
    const onChooseAvatar = (e) => {
      const avatarUrl = e.detail.avatarUrl;
      selectedAvatar.value = avatarUrl;
      common_vendor.index.uploadFile({
        url: "https://your-api-domain/api/upload/avatar",
        filePath: avatarUrl,
        name: "file",
        success: (uploadRes) => {
          showNicknamePopup.value = true;
        },
        fail: () => {
          showNicknamePopup.value = true;
        }
      });
    };
    const confirmNickname = async () => {
      if (!nickname.value.trim()) {
        common_vendor.index.showToast({
          title: "昵称不能为空",
          icon: "none"
        });
        return;
      }
      const updatedUser = await utils_userProfile.updateUserProfile({
        avatarUrl: selectedAvatar.value,
        nickName: nickname.value
      });
      if (updatedUser) {
        userInfoExists.value = true;
        showNicknamePopup.value = false;
        emit("update:userInfo", updatedUser);
        common_vendor.index.showToast({
          title: "设置成功",
          icon: "success"
        });
      } else {
        common_vendor.index.showToast({
          title: "设置失败，请重试",
          icon: "none"
        });
      }
    };
    const autoGetUserInfo = () => {
      if (!mpWeixin.value)
        return;
      if (userInfoExists.value)
        return;
      common_vendor.index.getUserProfile({
        desc: "用于完善用户资料",
        success: (res) => {
          nickname.value = res.userInfo.nickName || "用户" + Math.floor(Math.random() * 1e4);
          selectedAvatar.value = "/static/images/default-avatar.png";
          utils_userProfile.updateUserProfile({
            avatarUrl: selectedAvatar.value,
            nickName: nickname.value
          }).then((updatedUser) => {
            if (updatedUser) {
              userInfoExists.value = true;
              emit("update:userInfo", updatedUser);
              common_vendor.index.showToast({
                title: "已获取基本信息，点击更换头像",
                icon: "none",
                duration: 2e3
              });
            }
          });
        },
        fail: (err) => {
          common_vendor.index.__f__("log", "at components/UserAuth.vue:172", "获取用户信息失败", err);
          common_vendor.index.showToast({
            title: "请点击按钮授权获取头像昵称",
            icon: "none",
            duration: 2e3
          });
        }
      });
    };
    const cancelNickname = () => {
      showNicknamePopup.value = false;
      selectedAvatar.value = "";
      nickname.value = "";
    };
    const mockAuthorize = () => {
      common_vendor.index.showModal({
        title: "提示",
        content: "非微信小程序环境，点击确定模拟授权",
        success: async (res) => {
          if (res.confirm) {
            const updatedUser = await utils_userProfile.updateUserProfile({
              avatarUrl: "/static/images/default-avatar.png",
              nickName: "测试用户" + Math.floor(Math.random() * 1e3)
            });
            if (updatedUser) {
              userInfoExists.value = true;
              emit("update:userInfo", updatedUser);
            }
          }
        }
      });
    };
    common_vendor.onMounted(() => {
      mpWeixin.value = true;
      userInfoExists.value = utils_userProfile.hasUserInfo();
      if (userInfoExists.value) {
        emit("update:userInfo", utils_userProfile.getLocalUserInfo());
      } else if (props.autoAuth) {
        autoGetUserInfo();
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: mpWeixin.value
      }, mpWeixin.value ? common_vendor.e({
        b: !hasUserInfo.value
      }, !hasUserInfo.value ? {
        c: common_vendor.t(__props.buttonText),
        d: common_vendor.o(onChooseAvatar)
      } : {}) : common_vendor.e({
        e: !hasUserInfo.value
      }, !hasUserInfo.value ? {
        f: common_vendor.t(__props.buttonText),
        g: common_vendor.o(mockAuthorize)
      } : {}), {
        h: showNicknamePopup.value
      }, showNicknamePopup.value ? {
        i: nickname.value,
        j: common_vendor.o(($event) => nickname.value = $event.detail.value),
        k: common_vendor.o(cancelNickname),
        l: common_vendor.o(confirmNickname)
      } : {});
    };
  }
};
wx.createComponent(_sfc_main);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/UserAuth.js.map
