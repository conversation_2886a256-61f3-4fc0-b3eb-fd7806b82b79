{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/my/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcbXlcaW5kZXgudnVl"], "sourcesContent": ["<template>\n  <view class=\"activity-my-container\">\n    <!-- 自定义导航栏 - 强调返回到平台主\"我的\"页面 -->\n    <view class=\"custom-navbar\">\n      <view class=\"navbar-bg\"></view>\n      <view class=\"navbar-content\">\n        <view class=\"back-btn\" @click=\"backToMainMyPage\">\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n            <path d=\"M19 12H5M12 19l-7-7 7-7\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n          </svg>\n        </view>\n        <view class=\"navbar-title\">我的活动</view>\n        <view class=\"navbar-right\">\n          <view class=\"filter-btn\" @click=\"showActivityFilter\">\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n              <path d=\"M22 3H2l8 9.46V19l4 2v-8.54L22 3z\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n            </svg>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 内容区域 -->\n    <scroll-view \n      class=\"content-scroll\" \n      scroll-y=\"true\" \n      refresher-enabled=\"true\"\n      :refresher-triggered=\"isRefreshing\"\n      @refresherrefresh=\"onRefresh\"\n      @scrolltolower=\"loadMore\"\n    >\n      <!-- 用户信息模块 -->\n      <view class=\"user-profile-card\" :style=\"{\n        borderRadius: '35px',\n        boxShadow: '0 8px 20px rgba(0,0,0,0.08)',\n        background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',\n        padding: '30rpx',\n        marginBottom: '30rpx',\n        position: 'relative',\n        overflow: 'hidden'\n      }\">\n        <!-- 背景装饰 -->\n        <view class=\"profile-bg-decoration\" :style=\"{\n          position: 'absolute',\n          top: '-50rpx',\n          right: '-50rpx',\n          width: '300rpx',\n          height: '300rpx',\n          borderRadius: '50%',\n          background: 'rgba(255,255,255,0.1)',\n          zIndex: '1'\n        }\"></view>\n        <view class=\"profile-bg-decoration\" :style=\"{\n          position: 'absolute',\n          bottom: '-80rpx',\n          left: '-80rpx',\n          width: '250rpx',\n          height: '250rpx',\n          borderRadius: '50%',\n          background: 'rgba(255,255,255,0.08)',\n          zIndex: '1'\n        }\"></view>\n        \n        <!-- 用户基本信息 -->\n        <view class=\"user-basic-info\" :style=\"{\n          display: 'flex',\n          alignItems: 'center',\n          position: 'relative',\n          zIndex: '2'\n        }\">\n          <view class=\"avatar-container\" :style=\"{\n            position: 'relative',\n            marginRight: '20rpx'\n          }\">\n            <image \n              class=\"user-avatar\" \n              :src=\"userInfo.avatar || '/static/images/default-avatar.png'\" \n              mode=\"aspectFill\"\n              :style=\"{\n                width: '120rpx',\n                height: '120rpx',\n                borderRadius: '50%',\n                border: '4rpx solid rgba(255,255,255,0.8)',\n                boxShadow: '0 4rpx 10rpx rgba(0,0,0,0.1)'\n              }\"\n            ></image>\n            <view class=\"vip-badge\" v-if=\"userInfo.isVip\" :style=\"{\n              position: 'absolute',\n              bottom: '0',\n              right: '0',\n              background: '#FFD700',\n              color: '#8B4513',\n              fontSize: '20rpx',\n              fontWeight: 'bold',\n              padding: '4rpx 10rpx',\n              borderRadius: '10rpx',\n              transform: 'translateY(50%)',\n              boxShadow: '0 2rpx 5rpx rgba(0,0,0,0.2)'\n            }\">VIP</view>\n            </view>\n          \n          <view class=\"user-info\" :style=\"{\n            flex: '1'\n          }\">\n            <view class=\"user-name-row\" :style=\"{\n              display: 'flex',\n              alignItems: 'center',\n              marginBottom: '10rpx'\n            }\">\n              <text class=\"user-name\" :style=\"{\n                fontSize: '36rpx',\n                fontWeight: 'bold',\n                color: '#FFFFFF',\n                marginRight: '10rpx',\n                textShadow: '0 2rpx 4rpx rgba(0,0,0,0.1)'\n              }\">{{ userInfo.nickname || '游客' }}</text>\n              <view class=\"level-tag\" :style=\"{\n                fontSize: '22rpx',\n                color: '#FF3B69',\n                background: 'rgba(255,255,255,0.9)',\n                padding: '2rpx 10rpx',\n                borderRadius: '10rpx',\n                fontWeight: '500'\n              }\">Lv.{{ userInfo.level || 1 }}</view>\n          </view>\n            \n            <text class=\"user-id\" :style=\"{\n              fontSize: '24rpx',\n              color: 'rgba(255,255,255,0.8)',\n              marginBottom: '10rpx',\n              display: 'block'\n            }\">ID: {{ userInfo.id || '未设置' }}</text>\n            \n            <view class=\"user-points\" :style=\"{\n              display: 'flex',\n              alignItems: 'center'\n            }\">\n              <text class=\"points-label\" :style=\"{\n                fontSize: '24rpx',\n                color: 'rgba(255,255,255,0.9)'\n              }\">积分:</text>\n              <text class=\"points-value\" :style=\"{\n                fontSize: '28rpx',\n                fontWeight: '600',\n                color: '#FFFFFF',\n                marginLeft: '8rpx'\n              }\">{{ userInfo.points || 0 }}</text>\n          </view>\n        </view>\n        \n          <view class=\"profile-action\" @click=\"navigateTo('/subPackages/activity-showcase/pages/user-profile/index')\" :style=\"{\n            padding: '10rpx 30rpx',\n            background: 'rgba(255,255,255,0.2)',\n            borderRadius: '30rpx',\n            fontSize: '26rpx',\n            color: '#FFFFFF',\n            fontWeight: '500',\n            border: '1rpx solid rgba(255,255,255,0.3)'\n          }\">\n            编辑资料\n            </view>\n          </view>\n        \n        <!-- 用户数据统计 -->\n        <view class=\"user-stats\" :style=\"{\n          display: 'flex',\n          justifyContent: 'space-around',\n          marginTop: '30rpx',\n          position: 'relative',\n          zIndex: '2',\n          background: 'rgba(255,255,255,0.1)',\n          borderRadius: '20rpx',\n          padding: '20rpx 0'\n        }\">\n          <view class=\"stat-item\" @click=\"navigateTo('/subPackages/activity-showcase/pages/favorites/index')\">\n            <text class=\"stat-value\" :style=\"{\n              fontSize: '32rpx',\n              fontWeight: 'bold',\n              color: '#FFFFFF',\n              display: 'block',\n              textAlign: 'center'\n            }\">{{ userInfo.favoriteCount || 0 }}</text>\n            <text class=\"stat-label\" :style=\"{\n              fontSize: '24rpx',\n              color: 'rgba(255,255,255,0.8)',\n              display: 'block',\n              textAlign: 'center'\n            }\">收藏</text>\n          </view>\n          \n          <view class=\"stat-item\" @click=\"navigateTo('/subPackages/activity-showcase/pages/history/index')\">\n            <text class=\"stat-value\" :style=\"{\n              fontSize: '32rpx',\n              fontWeight: 'bold',\n              color: '#FFFFFF',\n              display: 'block',\n              textAlign: 'center'\n            }\">{{ userInfo.historyCount || 0 }}</text>\n            <text class=\"stat-label\" :style=\"{\n              fontSize: '24rpx',\n              color: 'rgba(255,255,255,0.8)',\n              display: 'block',\n              textAlign: 'center'\n            }\">浏览</text>\n        </view>\n        \n          <view class=\"stat-item\" @click=\"navigateTo('/subPackages/activity-showcase/pages/coupon/index')\">\n            <text class=\"stat-value\" :style=\"{\n              fontSize: '32rpx',\n              fontWeight: 'bold',\n              color: '#FFFFFF',\n              display: 'block',\n              textAlign: 'center'\n            }\">{{ userInfo.couponCount || 0 }}</text>\n            <text class=\"stat-label\" :style=\"{\n              fontSize: '24rpx',\n              color: 'rgba(255,255,255,0.8)',\n              display: 'block',\n              textAlign: 'center'\n            }\">优惠券</text>\n          </view>\n          \n          <view class=\"stat-item\" @click=\"navigateTo('/subPackages/activity-showcase/pages/orders/index')\">\n            <text class=\"stat-value\" :style=\"{\n              fontSize: '32rpx',\n              fontWeight: 'bold',\n              color: '#FFFFFF',\n              display: 'block',\n              textAlign: 'center'\n            }\">{{ userInfo.orderCount || 0 }}</text>\n            <text class=\"stat-label\" :style=\"{\n              fontSize: '24rpx',\n              color: 'rgba(255,255,255,0.8)',\n              display: 'block',\n              textAlign: 'center'\n            }\">订单</text>\n          </view>\n          </view>\n        </view>\n        \n      <!-- 统一活动中心 -->\n      <view class=\"activity-center-card\" :style=\"{\n        borderRadius: '35px',\n        boxShadow: '0 8px 20px rgba(255,59,105,0.15)',\n        background: '#FFFFFF',\n        padding: '30rpx',\n        marginBottom: '30rpx'\n      }\">\n        <view class=\"card-header\">\n          <text class=\"card-title\">活动中心</text>\n          <view class=\"header-right\" @click=\"navigateTo('/subPackages/activity-showcase/pages/activity-records/index')\">\n            <text class=\"view-all\">查看全部</text>\n              <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n              <path d=\"M9 18l6-6-6-6\" stroke=\"#FF3B69\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n              </svg>\n          </view>\n        </view>\n      </view>\n\n        <!-- 主标签容器 -->\n        <view class=\"main-tabs-container\">\n        <view \n          v-for=\"(tab, index) in tabs\" \n          :key=\"index\"\n            class=\"main-tab-item\"\n          :class=\"{ active: currentTab === index }\"\n          @click=\"switchTab(index)\"\n        >\n          <text class=\"tab-text\">{{ tab.name }}</text>\n          <view class=\"tab-indicator\" v-if=\"currentTab === index\" :style=\"{\n            background: 'linear-gradient(90deg, #FF3B69 0%, #FF7A9E 100%)'\n          }\"></view>\n        </view>\n      </view>\n        \n        <!-- 子标签容器 -->\n        <view class=\"sub-tabs-container\">\n          <view \n            v-for=\"(activityType, index) in activityTypes\" \n            :key=\"index\"\n            class=\"sub-tab-item\"\n            :class=\"{ active: currentActivityType === index }\"\n            @click=\"switchActivityType(index)\"\n          >\n            <text class=\"tab-text\">{{ activityType.name }}</text>\n            <view class=\"tab-indicator\" v-if=\"currentActivityType === index\" :style=\"{\n            background: 'linear-gradient(90deg, #FF3B69 0%, #FF7A9E 100%)'\n          }\"></view>\n        </view>\n      </view>\n\n      <!-- 活动列表区域 -->\n      <view class=\"activities-container\">\n        <swiper class=\"activities-swiper\" :current=\"currentTab\" @change=\"onSwiperChange\" :style=\"{ height: swiperHeight + 'px' }\">\n        <swiper-item v-for=\"(tab, tabIndex) in tabs\" :key=\"tabIndex\">\n            <scroll-view class=\"tab-content\" scroll-y :id=\"`tab-content-${tabIndex}`\">\n              <view class=\"activity-list\" :id=\"`activity-list-${tabIndex}`\">\n              <ActivityStatusCard \n                  v-for=\"activity in getFilteredActivities(tab.status, activityTypes[currentActivityType].type)\" \n                :key=\"activity.id\"\n                :activity=\"activity\"\n                  :showCountdown=\"activity.type === 'flash'\"\n                  :canCancel=\"activity.status === 'registered'\"\n                @click=\"viewActivityDetail(activity)\"\n                @share=\"shareActivity(activity)\"\n                @cancel=\"cancelActivity(activity)\"\n                  @favorite=\"activity.isFavorite = !activity.isFavorite\"\n              />\n              </view>\n              \n              <!-- 空状态 -->\n              <view class=\"empty-state\" v-if=\"getFilteredActivities(tab.status, activityTypes[currentActivityType].type).length === 0\">\n                <image class=\"empty-image\" :src=\"tab.emptyImage\"></image>\n                <text class=\"empty-text\">{{ tab.emptyText }}</text>\n                <view class=\"action-btn\" @click=\"navigateTo('/subPackages/activity-showcase/pages/index/index')\" :style=\"{\n                  background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',\n                  borderRadius: '35px',\n                  boxShadow: '0 5px 15px rgba(255,59,105,0.3)'\n                }\">\n                  <text>{{ tab.actionText }}</text>\n              </view>\n            </view>\n          </scroll-view>\n        </swiper-item>\n      </swiper>\n      </view>\n      \n      <!-- 订单管理卡片 -->\n      <view class=\"orders-card\" :style=\"{\n          borderRadius: '35px',\n        boxShadow: '0 8px 20px rgba(90,200,250,0.15)',\n        background: '#FFFFFF',\n        padding: '30rpx',\n        marginBottom: '30rpx'\n      }\">\n        <view class=\"card-header\">\n          <text class=\"card-title\">订单管理</text>\n          <view class=\"header-right\" @click=\"navigateTo('/subPackages/activity-showcase/pages/orders/index')\">\n            <text class=\"view-all\">查看全部</text>\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n              <path d=\"M9 18l6-6-6-6\" stroke=\"#5AC8FA\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n            </svg>\n          </view>\n        </view>\n        \n        <view class=\"order-status-grid\">\n          <view class=\"status-item\" @click=\"navigateTo('/subPackages/activity-showcase/pages/orders/index?status=pending_payment')\">\n            <view class=\"status-icon\" :style=\"{\n              background: 'rgba(90,200,250,0.1)',\n              borderRadius: '50%'\n            }\">\n              <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n                <rect x=\"2\" y=\"4\" width=\"20\" height=\"16\" rx=\"2\" stroke=\"#5AC8FA\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></rect>\n                <line x1=\"12\" y1=\"16\" x2=\"12\" y2=\"16.01\" stroke=\"#5AC8FA\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n                <path d=\"M8 12h8M8 8h8\" stroke=\"#5AC8FA\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n              </svg>\n            </view>\n            <text class=\"status-name\">待付款</text>\n            <view class=\"status-badge\" v-if=\"orderCounts.pendingPayment > 0\">{{ orderCounts.pendingPayment }}</view>\n          </view>\n          \n          <view class=\"status-item\" @click=\"navigateTo('/subPackages/activity-showcase/pages/orders/index?status=pending_delivery')\">\n            <view class=\"status-icon\" :style=\"{\n              background: 'rgba(90,200,250,0.1)',\n              borderRadius: '50%'\n            }\">\n              <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n                <rect x=\"2\" y=\"4\" width=\"20\" height=\"16\" rx=\"2\" stroke=\"#5AC8FA\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></rect>\n                <path d=\"M16 10V6M8 10V6M4 10h16\" stroke=\"#5AC8FA\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n              </svg>\n            </view>\n            <text class=\"status-name\">待发货</text>\n            <view class=\"status-badge\" v-if=\"orderCounts.pendingDelivery > 0\">{{ orderCounts.pendingDelivery }}</view>\n          </view>\n          \n          <view class=\"status-item\" @click=\"navigateTo('/subPackages/activity-showcase/pages/orders/index?status=pending_receipt')\">\n            <view class=\"status-icon\" :style=\"{\n              background: 'rgba(90,200,250,0.1)',\n              borderRadius: '50%'\n            }\">\n              <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n                <path d=\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0118 0z\" stroke=\"#5AC8FA\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                <circle cx=\"12\" cy=\"10\" r=\"3\" stroke=\"#5AC8FA\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n              </svg>\n            </view>\n            <text class=\"status-name\">待收货</text>\n            <view class=\"status-badge\" v-if=\"orderCounts.pendingReceipt > 0\">{{ orderCounts.pendingReceipt }}</view>\n          </view>\n          \n          <view class=\"status-item\" @click=\"navigateTo('/subPackages/activity-showcase/pages/orders/index?status=pending_review')\">\n            <view class=\"status-icon\" :style=\"{\n              background: 'rgba(90,200,250,0.1)',\n              borderRadius: '50%'\n            }\">\n              <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n                <path d=\"M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7\" stroke=\"#5AC8FA\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                <path d=\"M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z\" stroke=\"#5AC8FA\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n              </svg>\n            </view>\n            <text class=\"status-name\">待评价</text>\n            <view class=\"status-badge\" v-if=\"orderCounts.pendingReview > 0\">{{ orderCounts.pendingReview }}</view>\n          </view>\n          \n          <view class=\"status-item\" @click=\"navigateTo('/subPackages/activity-showcase/pages/orders/index?status=after_sale')\">\n            <view class=\"status-icon\" :style=\"{\n              background: 'rgba(90,200,250,0.1)',\n              borderRadius: '50%'\n            }\">\n              <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n                <path d=\"M16 15v4a2 2 0 01-2 2h-4a2 2 0 01-2-2v-4M8.929 9.571L12 12.643l3.071-3.072M12 3v9.643\" stroke=\"#5AC8FA\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n              </svg>\n            </view>\n            <text class=\"status-name\">售后/退款</text>\n            <view class=\"status-badge\" v-if=\"orderCounts.afterSale > 0\">{{ orderCounts.afterSale }}</view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 优惠券专区 -->\n      <view class=\"coupon-card\" :style=\"{\n          borderRadius: '35px',\n        boxShadow: '0 10px 25px rgba(255,149,0,0.12)',\n        background: 'linear-gradient(to bottom, #FFFFFF, #FFFAF2)',\n        padding: '30rpx',\n        marginBottom: '30rpx',\n        border: '1rpx solid rgba(255,149,0,0.1)'\n      }\">\n        <view class=\"card-header\">\n          <text class=\"card-title\">优惠券专区</text>\n          <view class=\"header-right\" @click=\"navigateTo('/subPackages/activity-showcase/pages/coupon/index')\">\n            <text class=\"view-all\">查看全部</text>\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n              <path d=\"M9 18l6-6-6-6\" stroke=\"#FF9500\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n            </svg>\n          </view>\n        </view>\n        \n        <view class=\"coupon-status-tabs\">\n          <view \n            v-for=\"(couponStatus, index) in couponStatuses\" \n            :key=\"index\"\n            class=\"coupon-status-tab\"\n            :class=\"{ active: currentCouponStatus === index }\"\n            @click=\"switchCouponStatus(index)\"\n          >\n            <text>{{ couponStatus.name }}</text>\n            <view class=\"tab-indicator\" v-if=\"currentCouponStatus === index\" :style=\"{\n              background: 'linear-gradient(90deg, #FF9500 0%, #FFCC00 100%)'\n            }\"></view>\n          </view>\n        </view>\n        \n        <view class=\"coupons-list\">\n          <view \n            v-for=\"(coupon, index) in getCouponsByStatus(couponStatuses[currentCouponStatus].status)\"\n            :key=\"coupon.id\"\n            class=\"coupon-item\"\n            :style=\"{\n              background: getCouponBackground(coupon.status)\n            }\"\n            @click=\"viewCouponDetail(coupon)\"\n          >\n            <view class=\"coupon-left\">\n              <view class=\"coupon-value\">\n                <text class=\"currency\">¥</text>\n                <text class=\"amount\">{{ coupon.value }}</text>\n              </view>\n              <text class=\"coupon-condition\">{{ coupon.condition }}</text>\n            </view>\n            <view class=\"coupon-divider\"></view>\n            <view class=\"coupon-right\">\n              <text class=\"coupon-name\">{{ coupon.name }}</text>\n              <text class=\"coupon-shop\">{{ coupon.shopName }}</text>\n              <text class=\"coupon-validity\">{{ coupon.validityPeriod }}</text>\n              <view \n                v-if=\"coupon.status === 'unused'\" \n                class=\"coupon-use-btn\" \n                :style=\"{ \n                  background: 'rgba(255,128,0,0.9)',\n                  border: '1rpx solid rgba(255,128,0,0.3)'\n                }\"\n                @click.stop=\"useCoupon(coupon)\"\n              >\n                <text>立即使用</text>\n              </view>\n              <view \n                v-else-if=\"coupon.status === 'used'\" \n                class=\"coupon-use-btn\" \n                :style=\"{ \n                  background: 'rgba(46,184,77,0.9)',\n                  border: '1rpx solid rgba(46,184,77,0.3)'\n                }\"\n              >\n                <text>已使用</text>\n              </view>\n              <view \n                v-else \n                class=\"coupon-use-btn\" \n                :style=\"{ \n                  background: 'rgba(142,142,147,0.9)',\n                  border: '1rpx solid rgba(142,142,147,0.3)'\n                }\"\n              >\n                <text>已过期</text>\n              </view>\n            </view>\n          </view>\n          \n          <!-- 空状态 -->\n          <view class=\"empty-state\" v-if=\"getCouponsByStatus(couponStatuses[currentCouponStatus].status).length === 0\">\n            <image class=\"empty-image\" :src=\"couponStatuses[currentCouponStatus].emptyImage\"></image>\n            <text class=\"empty-text\">{{ couponStatuses[currentCouponStatus].emptyText }}</text>\n            <view class=\"action-btn\" @click=\"navigateTo('/subPackages/activity-showcase/pages/coupon/index')\" :style=\"{\n              background: 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)',\n              borderRadius: '35px',\n              boxShadow: '0 5px 15px rgba(255,149,0,0.3)'\n            }\">\n              <text>去领取优惠券</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 活动工具箱 -->\n      <view class=\"tools-card\" :style=\"{\n        borderRadius: '35px',\n        boxShadow: '0 8px 20px rgba(88,86,214,0.15)',\n        background: '#FFFFFF',\n        padding: '30rpx',\n        marginBottom: '30rpx'\n      }\">\n        <view class=\"toolbox-header\">\n          <text class=\"toolbox-title\">活动工具</text>\n        </view>\n        <view class=\"tool-grid\">\n          <view class=\"tool-item\" @click=\"navigateTo('/subPackages/activity-showcase/pages/reminders/index')\">\n            <view class=\"tool-icon reminder-icon\" :style=\"{\n              background: 'linear-gradient(135deg, #FF9500 0%, #FF3B30 100%)',\n              borderRadius: '50%',\n              boxShadow: '0 5px 15px rgba(255,59,48,0.2)'\n            }\">\n              <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n                <path d=\"M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                <path d=\"M13.73 21a2 2 0 0 1-3.46 0\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n              </svg>\n            </view>\n            <text class=\"tool-name\">活动提醒</text>\n          </view>\n          \n          <view class=\"tool-item\" @click=\"navigateTo('/subPackages/activity-showcase/pages/share-records/index')\">\n            <view class=\"tool-icon share-icon\" :style=\"{\n              background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',\n              borderRadius: '50%',\n              boxShadow: '0 5px 15px rgba(255,59,105,0.2)'\n            }\">\n              <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n                <circle cx=\"18\" cy=\"5\" r=\"3\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n                <circle cx=\"6\" cy=\"12\" r=\"3\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n                <circle cx=\"18\" cy=\"19\" r=\"3\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n                <line x1=\"8.59\" y1=\"13.51\" x2=\"15.42\" y2=\"17.49\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n                <line x1=\"15.41\" y1=\"6.51\" x2=\"8.59\" y2=\"10.49\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n              </svg>\n            </view>\n            <text class=\"tool-name\">分享工具</text>\n          </view>\n          \n          <view class=\"tool-item\" @click=\"navigateTo('/subPackages/activity-showcase/pages/favorites/index')\">\n            <view class=\"tool-icon favorite-icon\" :style=\"{\n              background: 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)',\n              borderRadius: '50%',\n              boxShadow: '0 5px 15px rgba(255,149,0,0.2)'\n            }\">\n              <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n                <path d=\"M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n              </svg>\n            </view>\n            <text class=\"tool-name\">收藏夹</text>\n          </view>\n          \n          <view class=\"tool-item\" @click=\"navigateTo('/subPackages/activity-showcase/pages/history/index')\">\n            <view class=\"tool-icon history-icon\" :style=\"{\n              background: 'linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)',\n              borderRadius: '50%',\n              boxShadow: '0 5px 15px rgba(142,142,147,0.2)'\n            }\">\n              <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n                <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n                <polyline points=\"12 6 12 12 16 14\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></polyline>\n              </svg>\n            </view>\n            <text class=\"tool-name\">浏览记录</text>\n          </view>\n          \n          <view class=\"tool-item\" @click=\"navigateToMainSettings\">\n            <view class=\"tool-icon help-icon\" :style=\"{\n              background: 'linear-gradient(135deg, #34C759 0%, #30D158 100%)',\n              borderRadius: '50%',\n              boxShadow: '0 5px 15px rgba(52,199,89,0.2)'\n            }\">\n              <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n                <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n                <path d=\"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                <line x1=\"12\" y1=\"17\" x2=\"12.01\" y2=\"17\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n              </svg>\n            </view>\n            <text class=\"tool-name\">帮助</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 推荐活动模块 -->\n      <view class=\"recommended-card\" :style=\"{\n        borderRadius: '35px',\n        boxShadow: '0 8px 20px rgba(90,200,250,0.15)',\n        background: 'linear-gradient(to bottom, #FFFFFF, #F0F8FF)',\n        padding: '30rpx',\n        marginBottom: '30rpx',\n        border: '1rpx solid rgba(90,200,250,0.1)'\n      }\">\n        <view class=\"card-header\">\n          <text class=\"card-title\">为您推荐</text>\n          <view class=\"header-right\" @click=\"navigateTo('/subPackages/activity-showcase/pages/recommendations/index')\">\n            <text class=\"view-all\">查看全部</text>\n              <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n              <path d=\"M9 18l6-6-6-6\" stroke=\"#5AC8FA\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n              </svg>\n          </view>\n          </view>\n          \n        \n        <scroll-view class=\"recommendation-scroll\" scroll-x=\"true\" show-scrollbar=\"false\">\n          <view class=\"recommendation-list\">\n            <view \n              v-for=\"(activity, index) in recommendedActivities\" \n              :key=\"activity.id\"\n              class=\"recommendation-item\"\n              @click=\"viewActivityDetail(activity)\"\n              :style=\"{\n                background: getRecommendationBackground(activity.type)\n              }\"\n            >\n              <image class=\"recommendation-image\" :src=\"activity.coverImage\" mode=\"aspectFill\"></image>\n              <view class=\"recommendation-content\">\n                <view class=\"recommendation-tag\" :style=\"{\n                  background: getRecommendationTagBackground(activity.type)\n                }\">\n                  <text>{{ getActivityTypeText(activity.type) }}</text>\n            </view>\n                <text class=\"recommendation-title\">{{ activity.title }}</text>\n                <text class=\"recommendation-shop\">{{ activity.shopName }}</text>\n                <view class=\"recommendation-price-row\">\n                  <text class=\"current-price\">¥{{ activity.currentPrice }}</text>\n                  <text class=\"original-price\" v-if=\"activity.originalPrice\">¥{{ activity.originalPrice }}</text>\n          </view>\n                <view class=\"recommendation-stats\">\n                  <text class=\"participation-count\">{{ activity.participationCount }}人参与</text>\n            </view>\n          </view>\n            </view>\n          </view>\n        </scroll-view>\n        \n        <!-- 空状态 -->\n        <view class=\"empty-state\" v-if=\"recommendedActivities.length === 0\">\n          <image class=\"empty-image\" src=\"/static/images/empty/empty-recommendation.png\"></image>\n          <text class=\"empty-text\">暂无推荐活动</text>\n          <view class=\"action-btn\" @click=\"navigateTo('/subPackages/activity-showcase/pages/index/index')\" :style=\"{\n            background: 'linear-gradient(135deg, #5AC8FA 0%, #64D2FF 100%)',\n            borderRadius: '35px',\n            boxShadow: '0 5px 15px rgba(90,200,250,0.3)'\n          }\">\n            <text>去浏览更多活动</text>\n          </view>\n          </view>\n        </view>\n        \n      <!-- 底部安全区域 -->\n      <view class=\"safe-area-bottom\"></view>\n    </scroll-view>\n    \n    <!-- 底部导航栏 -->\n    <view class=\"tabbar\">\n          <view \n        class=\"tabbar-item\" \n        :class=\"{active: currentTabBar === 'home'}\" \n        @click=\"switchTabBar('home')\"\n        data-tab=\"home\"\n      >\n        <view class=\"tab-icon home\"></view>\n        <text class=\"tabbar-text\">首页</text>\n          </view>\n          <view \n        class=\"tabbar-item\" \n        :class=\"{active: currentTabBar === 'discover'}\" \n        @click=\"switchTabBar('discover')\"\n        data-tab=\"discover\"\n      >\n        <view class=\"tab-icon discover\"></view>\n        <text class=\"tabbar-text\">本地商城</text>\n              </view>\n          <view \n        class=\"tabbar-item\" \n        :class=\"{active: currentTabBar === 'distribution'}\" \n        @click=\"switchTabBar('distribution')\"\n        data-tab=\"distribution\"\n      >\n        <view class=\"tab-icon distribution\"></view>\n        <text class=\"tabbar-text\">分销中心</text>\n              </view>\n          <view \n        class=\"tabbar-item\" \n        :class=\"{active: currentTabBar === 'message'}\" \n        @click=\"switchTabBar('message')\"\n        data-tab=\"message\"\n      >\n        <view class=\"tab-icon message\">\n          <view class=\"badge\" v-if=\"unreadMessageCount > 0\">{{ unreadMessageCount > 99 ? '99+' : unreadMessageCount }}</view>\n          </view>\n        <text class=\"tabbar-text\">消息</text>\n        </view>\n          <view \n        class=\"tabbar-item\" \n        :class=\"{active: currentTabBar === 'my'}\" \n        @click=\"switchTabBar('my')\"\n        data-tab=\"my\"\n      >\n        <view class=\"tab-icon user\"></view>\n        <text class=\"tabbar-text\">我的</text>\n              </view>\n            </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted, computed } from 'vue';\nimport ActivityStatusCard from '../../components/activity/ActivityStatusCard.vue';\nimport { onLoad, onShow } from '@dcloudio/uni-app';\n\n// 下拉刷新状态\nconst isRefreshing = ref(false);\n\n// 当前选中的标签页\nconst currentTab = ref(0);\nconst currentActivityType = ref(0);\nconst currentCouponStatus = ref(0);\nconst currentTabBar = ref('my'); // 当前底部导航选中项\n\n// 未读消息数量\nconst unreadMessageCount = ref(3);\n\n// 用户信息数据\nconst userInfo = ref({\n  nickname: '张三',\n  avatar: 'https://via.placeholder.com/120',\n  id: '10086',\n  level: 5,\n  isVip: true,\n  points: 1280,\n  favoriteCount: 16,\n  historyCount: 42,\n  couponCount: 8,\n  orderCount: 12\n});\n\n// 标签页数据\nconst tabs = ref([\n  { name: '进行中', status: 'ongoing', emptyText: '暂无进行中的活动', actionText: '去参与活动', emptyImage: '/static/images/empty/empty-ongoing.png' },\n  { name: '已报名', status: 'registered', emptyText: '暂无已报名的活动', actionText: '去浏览活动', emptyImage: '/static/images/empty/empty-registered.png' },\n  { name: '已完成', status: 'completed', emptyText: '暂无已完成的活动', actionText: '去参与活动', emptyImage: '/static/images/empty/empty-completed.png' },\n  { name: '已收藏', status: 'favorite', emptyText: '暂无收藏的活动', actionText: '去浏览活动', emptyImage: '/static/images/empty/empty-favorite.png' }\n]);\n\n// 活动类型标签\nconst activityTypes = ref([\n  { name: '全部', type: 'all', emptyText: '暂无活动参与记录', emptyImage: '/static/images/empty/empty-activity-all.png' },\n  { name: '拼团', type: 'group', emptyText: '暂无拼团活动参与记录', emptyImage: '/static/images/empty/empty-activity-group.png' },\n  { name: '秒杀', type: 'flash', emptyText: '暂无秒杀活动参与记录', emptyImage: '/static/images/empty/empty-activity-flash.png' },\n  { name: '优惠券', type: 'coupon', emptyText: '暂无优惠券活动参与记录', emptyImage: '/static/images/empty/empty-activity-coupon.png' }\n]);\n\n// 优惠券状态标签\nconst couponStatuses = ref([\n  { name: '全部', status: 'all', emptyText: '暂无优惠券', emptyImage: '/static/images/empty/empty-coupon-all.png' },\n  { name: '未使用', status: 'unused', emptyText: '暂无未使用优惠券', emptyImage: '/static/images/empty/empty-coupon-unused.png' },\n  { name: '已使用', status: 'used', emptyText: '暂无已使用优惠券', emptyImage: '/static/images/empty/empty-coupon-used.png' },\n  { name: '已过期', status: 'expired', emptyText: '暂无已过期优惠券', emptyImage: '/static/images/empty/empty-coupon-expired.png' }\n]);\n\n// 订单统计数据\nconst orderCounts = ref({\n  pendingPayment: 3,\n  pendingDelivery: 2,\n  pendingReceipt: 1,\n  pendingReview: 5,\n  afterSale: 0\n});\n\n// 活动列表数据\nconst activities = ref([]);\n\n// 活动日历数据\nconst activityEvents = ref([\n  {\n    id: 1,\n    title: '限时秒杀：iPhone 14 Pro',\n    startTime: new Date().getTime(),\n    location: '线上活动',\n    type: 'flash'\n  },\n  {\n    id: 2,\n    title: '3人团：小米空气净化器',\n    startTime: new Date().getTime() + 2 * 60 * 60 * 1000, // 2小时后\n    location: '线上活动',\n    type: 'group'\n  },\n  {\n    id: 3,\n    title: '满300减50全场优惠',\n    startTime: new Date().getTime() + 1 * 24 * 60 * 60 * 1000, // 明天\n    location: '线上活动',\n    type: 'discount'\n  }\n]);\n\n// 轮播图高度\nconst swiperHeight = ref(600);\n\n// 返回主\"我的\"页面\nconst backToMainMyPage = () => {\n  uni.navigateBack();\n};\n\n// 显示活动筛选\nconst showActivityFilter = () => {\n  uni.showToast({\n    title: '筛选功能开发中',\n    icon: 'none'\n  });\n};\n\n// 切换标签页\nconst switchTab = (index) => {\n  currentTab.value = index;\n  updateSwiperHeight(index);\n};\n\n// 轮播图变化\nconst onSwiperChange = (e) => {\n  currentTab.value = e.detail.current;\n  updateSwiperHeight(e.detail.current);\n};\n\n// 切换活动类型\nconst switchActivityType = (index) => {\n  currentActivityType.value = index;\n  updateSwiperHeight(currentTab.value);\n};\n\n// 更新轮播图高度\nconst updateSwiperHeight = (tabIndex = 0) => {\n  setTimeout(() => {\n    const query = uni.createSelectorQuery();\n    query.select(`#activity-list-${tabIndex}`).boundingClientRect(data => {\n      if (data && data.height > 0) {\n        swiperHeight.value = Math.max(600, data.height + 100); // 设置最小高度为600px，添加额外空间\n      } else {\n        // 如果没有获取到高度，设置一个默认高度\n        const filteredActivities = getFilteredActivities(\n          tabs.value[tabIndex].status, \n          activityTypes.value[currentActivityType.value].type\n        );\n        \n        // 根据活动数量设置高度或显示空状态的高度\n        if (filteredActivities.length > 0) {\n          swiperHeight.value = filteredActivities.length * 400 + 100; // 每个卡片大约400px高\n        } else {\n          swiperHeight.value = 600; // 空状态的高度\n        }\n      }\n    }).exec();\n  }, 300);\n};\n\n// 根据状态获取活动列表\nconst getActivitiesByStatus = (status) => {\n  // 模拟数据\n  if (status === 'ongoing') {\n    return [\n      {\n        id: 1,\n        title: '限时秒杀：iPhone 14 Pro',\n        coverImage: 'https://via.placeholder.com/300x200',\n        shopName: 'Apple授权专卖店',\n        startTime: new Date().getTime() - 2 * 60 * 60 * 1000, // 2小时前\n        endTime: new Date().getTime() + 10 * 60 * 60 * 1000, // 10小时后\n        currentPrice: 6999,\n        originalPrice: 8999,\n        type: 'flash',\n        status: 'ongoing'\n      },\n      {\n        id: 2,\n        title: '3人团：小米空气净化器',\n        coverImage: 'https://via.placeholder.com/300x200',\n        shopName: '小米官方旗舰店',\n        startTime: new Date().getTime() - 5 * 60 * 60 * 1000, // 5小时前\n        endTime: new Date().getTime() + 24 * 60 * 60 * 1000, // 24小时后\n        currentPrice: 699,\n        originalPrice: 999,\n        type: 'group',\n        status: 'ongoing'\n      }\n    ];\n  } else if (status === 'registered') {\n    return [\n      {\n        id: 3,\n        title: '满300减50全场优惠',\n        coverImage: 'https://via.placeholder.com/300x200',\n        shopName: '京东自营',\n        startTime: new Date().getTime() + 1 * 24 * 60 * 60 * 1000, // 明天\n        endTime: new Date().getTime() + 8 * 24 * 60 * 60 * 1000, // 8天后\n        currentPrice: 300,\n        type: 'discount',\n        status: 'registered'\n      }\n    ];\n  } else if (status === 'completed') {\n    return [\n      {\n        id: 4,\n        title: '星巴克咖啡买一送一',\n        coverImage: 'https://via.placeholder.com/300x200',\n        shopName: '星巴克(万达广场店)',\n        startTime: new Date().getTime() - 10 * 24 * 60 * 60 * 1000, // 10天前\n        endTime: new Date().getTime() - 3 * 24 * 60 * 60 * 1000, // 3天前\n        currentPrice: 30,\n        type: 'coupon',\n        status: 'completed'\n      }\n    ];\n  } else if (status === 'favorite') {\n    return [\n      {\n        id: 5,\n        title: '华为P50 Pro限时特惠',\n        coverImage: 'https://via.placeholder.com/300x200',\n        shopName: '华为授权体验店',\n        startTime: new Date().getTime() - 1 * 24 * 60 * 60 * 1000, // 1天前\n        endTime: new Date().getTime() + 6 * 24 * 60 * 60 * 1000, // 6天后\n        currentPrice: 5299,\n        originalPrice: 6488,\n        type: 'flash',\n        status: 'favorite'\n      }\n    ];\n  }\n  \n  return [];\n};\n\n// 获取活动记录列表\nconst getActivityRecordsByType = (type) => {\n  if (type === 'all') {\n    return [\n      { id: 1, title: '限时秒杀：iPhone 14 Pro', coverImage: 'https://via.placeholder.com/300x200', shopName: 'Apple授权专卖店', startTime: new Date().getTime() - 2 * 60 * 60 * 1000, endTime: new Date().getTime() + 10 * 60 * 60 * 1000, status: 'ongoing' },\n      { id: 2, title: '3人团：小米空气净化器', coverImage: 'https://via.placeholder.com/300x200', shopName: '小米官方旗舰店', startTime: new Date().getTime() - 5 * 60 * 60 * 1000, endTime: new Date().getTime() + 24 * 60 * 60 * 1000, status: 'ongoing' },\n      { id: 3, title: '满300减50全场优惠', coverImage: 'https://via.placeholder.com/300x200', shopName: '京东自营', startTime: new Date().getTime() + 1 * 24 * 60 * 60 * 1000, endTime: new Date().getTime() + 8 * 24 * 60 * 60 * 1000, status: 'registered' }\n    ];\n  } else if (type === 'group') {\n    return [\n      { id: 1, title: '3人团：小米空气净化器', coverImage: 'https://via.placeholder.com/300x200', shopName: '小米官方旗舰店', startTime: new Date().getTime() - 2 * 60 * 60 * 1000, endTime: new Date().getTime() + 24 * 60 * 60 * 1000, status: 'ongoing' }\n    ];\n  } else if (type === 'flash') {\n    return [\n      { id: 1, title: '限时秒杀：iPhone 14 Pro', coverImage: 'https://via.placeholder.com/300x200', shopName: 'Apple授权专卖店', startTime: new Date().getTime() - 2 * 60 * 60 * 1000, endTime: new Date().getTime() + 10 * 60 * 60 * 1000, status: 'ongoing' }\n    ];\n  } else if (type === 'coupon') {\n    return [\n      { id: 1, title: '满300减50全场优惠', coverImage: 'https://via.placeholder.com/300x200', shopName: '京东自营', startTime: new Date().getTime() + 1 * 24 * 60 * 60 * 1000, endTime: new Date().getTime() + 8 * 24 * 60 * 60 * 1000, status: 'registered' }\n    ];\n  }\n  \n  return [];\n};\n\n// 获取优惠券列表\nconst getCouponsByStatus = (status) => {\n  if (status === 'all') {\n    return [\n      { id: 1, name: '满300减50全场优惠券', value: 50, condition: '满300元可用', validityPeriod: '2023-12-31', shopName: '京东自营', status: 'unused' },\n      { id: 2, name: '星巴克咖啡买一送一优惠券', value: 30, condition: '满30元可用', validityPeriod: '2023-11-30', shopName: '星巴克(万达广场店)', status: 'unused' },\n      { id: 3, name: '满100减10优惠券', value: 10, condition: '满100元可用', validityPeriod: '2023-12-15', shopName: 'Apple授权专卖店', status: 'used' }\n    ];\n  } else if (status === 'unused') {\n    return [\n      { id: 1, name: '满300减50全场优惠券', value: 50, condition: '满300元可用', validityPeriod: '2023-12-31', shopName: '京东自营', status: 'unused' },\n      { id: 2, name: '星巴克咖啡买一送一优惠券', value: 30, condition: '满30元可用', validityPeriod: '2023-11-30', shopName: '星巴克(万达广场店)', status: 'unused' }\n    ];\n  } else if (status === 'used') {\n    return [\n      { id: 1, name: '满100减10优惠券', value: 10, condition: '满100元可用', validityPeriod: '2023-12-15', shopName: 'Apple授权专卖店', status: 'used' }\n    ];\n  } else if (status === 'expired') {\n    return [\n      { id: 1, name: '满50减5优惠券', value: 5, condition: '满50元可用', validityPeriod: '2023-11-20', shopName: '小米官方旗舰店', status: 'expired' }\n    ];\n  }\n  \n  return [];\n};\n\n// 获取同时按状态和类型筛选的活动列表\nconst getFilteredActivities = (status, type) => {\n  // 先按状态筛选\n  const statusFiltered = getActivitiesByStatus(status);\n  \n  // 如果类型是\"全部\"，直接返回按状态筛选的结果\n  if (type === 'all') {\n    return statusFiltered;\n  }\n  \n  // 再按类型筛选\n  return statusFiltered.filter(activity => activity.type === type);\n};\n\n// 查看活动详情\nconst viewActivityDetail = (activity) => {\n  let url = '';\n  \n  switch(activity.type) {\n    case 'flash':\n      url = `/subPackages/activity-showcase/pages/flash-sale/detail?id=${activity.id}`;\n      break;\n    case 'group':\n      url = `/subPackages/activity-showcase/pages/group-buy/detail?id=${activity.id}`;\n      break;\n    case 'discount':\n      url = `/subPackages/activity-showcase/pages/discount/detail?id=${activity.id}`;\n      break;\n    case 'coupon':\n      url = `/subPackages/activity-showcase/pages/coupon/detail?id=${activity.id}`;\n      break;\n    default:\n      url = `/subPackages/activity-showcase/pages/detail/index?id=${activity.id}&type=${activity.type}`;\n  }\n  \n  navigateTo(url);\n};\n\n// 分享活动\nconst shareActivity = (activity) => {\n  uni.showShareMenu({\n    withShareTicket: true,\n    success() {\n      uni.showToast({\n        title: '分享成功',\n        icon: 'success'\n      });\n    }\n  });\n};\n\n// 取消活动\nconst cancelActivity = (activity) => {\n  uni.showModal({\n    title: '取消活动',\n    content: '确定要取消该活动吗？',\n    success: (res) => {\n      if (res.confirm) {\n        // 模拟取消活动\n        uni.showToast({\n          title: '已取消活动',\n          icon: 'success'\n        });\n      }\n    }\n  });\n};\n\n// 查看日期活动\nconst viewDateActivities = (date) => {\n  uni.showToast({\n    title: `查看${date}活动`,\n    icon: 'none'\n  });\n};\n\n// 切换优惠券状态\nconst switchCouponStatus = (index) => {\n  currentCouponStatus.value = index;\n};\n\n// 使用优惠券\nconst useCoupon = (coupon) => {\n  uni.showToast({\n    title: `已使用优惠券: ${coupon.name}`,\n    icon: 'success'\n  });\n};\n\n// 查看优惠券详情\nconst viewCouponDetail = (coupon) => {\n  uni.showToast({\n    title: `查看优惠券: ${coupon.name} 详情`,\n    icon: 'none'\n  });\n};\n\n// 格式化活动时间\nconst formatActivityTime = (startTime, endTime) => {\n  const startDate = new Date(startTime);\n  const endDate = new Date(endTime);\n  const startMonth = startDate.getMonth() + 1;\n  const startDay = startDate.getDate();\n  const endMonth = endDate.getMonth() + 1;\n  const endDay = endDate.getDate();\n  return `${startMonth}月${startDay}日 - ${endMonth}月${endDay}日`;\n};\n\n// 获取活动状态颜色\nconst getActivityStatusColor = (status) => {\n  switch (status) {\n    case 'ongoing':\n      return '#FF3B69'; // 进行中\n    case 'registered':\n      return '#FF9500'; // 已报名\n    case 'completed':\n      return '#34C759'; // 已完成\n    case 'favorite':\n      return '#FF3B69'; // 已收藏\n    default:\n      return '#8E8E93'; // 默认\n  }\n};\n\n// 获取活动状态背景色\nconst getActivityStatusBgColor = (status) => {\n  switch (status) {\n    case 'ongoing':\n      return 'rgba(255,59,105,0.1)'; // 进行中\n    case 'registered':\n      return 'rgba(255,149,0,0.1)'; // 已报名\n    case 'completed':\n      return 'rgba(52,199,89,0.1)'; // 已完成\n    case 'favorite':\n      return 'rgba(255,59,105,0.1)'; // 已收藏\n    default:\n      return 'rgba(142,142,147,0.1)'; // 默认\n  }\n};\n\n// 获取活动状态文本\nconst getActivityStatusText = (status) => {\n  switch (status) {\n    case 'ongoing':\n      return '进行中';\n    case 'registered':\n      return '已报名';\n    case 'completed':\n      return '已完成';\n    case 'favorite':\n      return '已收藏';\n    default:\n      return '未知';\n  }\n};\n\n// 获取优惠券背景色\nconst getCouponBackground = (status) => {\n  switch (status) {\n    case 'unused':\n      return 'linear-gradient(135deg, #FF8000 0%, #FFA500 80%, #FFBF00 100%)'; // 未使用，更深的橙色\n    case 'used':\n      return 'linear-gradient(135deg, #2EB84D 0%, #34C759 80%, #30D158 100%)'; // 已使用，更深的绿色\n    case 'expired':\n      return 'linear-gradient(135deg, #636366 0%, #8E8E93 80%, #AEAEB2 100%)'; // 已过期，更深的灰色\n    default:\n      return 'linear-gradient(135deg, #FF8000 0%, #FFA500 80%, #FFBF00 100%)'; // 默认\n  }\n};\n\n// 页面导航\nconst navigateTo = (url) => {\n  uni.navigateTo({ url });\n};\n\n// 导航到平台主设置页面\nconst navigateToMainSettings = () => {\n  uni.navigateTo({\n    url: '/subPackages/activity-showcase/pages/settings/index'\n  });\n};\n\n// 下拉刷新\nconst onRefresh = () => {\n  isRefreshing.value = true;\n  \n  // 模拟加载数据\n  setTimeout(() => {\n    // 加载数据...\n    isRefreshing.value = false;\n    \n    uni.showToast({\n      title: '刷新成功',\n      icon: 'success'\n    });\n  }, 1000);\n};\n\n// 加载更多\nconst loadMore = () => {\n  // 加载更多数据...\n  uni.showToast({\n    title: '已加载全部数据',\n    icon: 'none'\n  });\n};\n\n// 页面加载\nonMounted(() => {\n  // 初始化数据\n  activities.value = getActivitiesByStatus(tabs.value[currentTab.value].status);\n  \n  // 确保组件渲染完成后再计算高度\n  setTimeout(() => {\n    updateSwiperHeight(currentTab.value);\n  }, 500);\n});\n\nonShow(() => {\n  // 每次页面显示时更新数据\n  activities.value = getActivitiesByStatus(tabs.value[currentTab.value].status);\n  \n  // 确保组件渲染完成后再计算高度\n  setTimeout(() => {\n    updateSwiperHeight(currentTab.value);\n  }, 500);\n});\n\n// 切换底部导航栏\nconst switchTabBar = (tab) => {\n  if (currentTabBar.value === tab) return;\n      \n  currentTabBar.value = tab;\n      \n  // 根据选中的标签页进行相应的导航\n  switch(tab) {\n    case 'home':\n      uni.reLaunch({\n        url: '/subPackages/activity-showcase/pages/index/index',\n        fail: (err) => {\n          console.error('页面跳转失败:', err);\n          uni.showToast({\n            title: '页面跳转失败',\n            icon: 'none'\n          });\n        }\n      });\n      break;\n    case 'discover':\n      uni.navigateTo({\n        url: '/subPackages/activity-showcase/pages/discover/index',\n        fail: (err) => {\n          console.error('页面跳转失败:', err);\n          uni.showToast({\n            title: '页面跳转失败',\n            icon: 'none'\n          });\n        }\n      });\n      break;\n    case 'distribution':\n      uni.navigateTo({\n        url: '/subPackages/activity-showcase/pages/distribution/index',\n        fail: (err) => {\n          console.error('页面跳转失败:', err);\n          uni.showToast({\n            title: '页面跳转失败',\n            icon: 'none'\n          });\n        }\n      });\n      break;\n    case 'message':\n      uni.navigateTo({\n        url: '/subPackages/activity-showcase/pages/message/index',\n        fail: (err) => {\n          console.error('页面跳转失败:', err);\n          uni.showToast({\n            title: '页面跳转失败',\n            icon: 'none'\n          });\n        }\n      });\n      break;\n    case 'my':\n      // 已在我的页面，不需要导航\n      break;\n  }\n};\n\n// 推荐活动数据\nconst recommendedActivities = ref([\n  {\n    id: 101,\n    title: '3人拼团：戴森吹风机',\n    coverImage: 'https://via.placeholder.com/300x200',\n    shopName: '戴森官方旗舰店',\n    startTime: new Date().getTime(),\n    endTime: new Date().getTime() + 3 * 24 * 60 * 60 * 1000, // 3天后\n    currentPrice: 1999,\n    originalPrice: 2999,\n    type: 'group',\n    matchRate: 95,\n    participationCount: 1283\n  },\n  {\n    id: 102,\n    title: '限时秒杀：小米手环8',\n    coverImage: 'https://via.placeholder.com/300x200',\n    shopName: '小米官方旗舰店',\n    startTime: new Date().getTime(),\n    endTime: new Date().getTime() + 1 * 24 * 60 * 60 * 1000, // 1天后\n    currentPrice: 199,\n    originalPrice: 299,\n    type: 'flash',\n    matchRate: 87,\n    participationCount: 3521\n  },\n  {\n    id: 103,\n    title: '满200减50：星巴克咖啡',\n    coverImage: 'https://via.placeholder.com/300x200',\n    shopName: '星巴克(万达广场店)',\n    startTime: new Date().getTime(),\n    endTime: new Date().getTime() + 7 * 24 * 60 * 60 * 1000, // 7天后\n    currentPrice: 200,\n    type: 'discount',\n    matchRate: 82,\n    participationCount: 872\n  }\n]);\n\n// 获取活动类型文本\nconst getActivityTypeText = (type) => {\n  switch (type) {\n    case 'flash':\n      return '秒杀';\n    case 'group':\n      return '拼团';\n    case 'discount':\n      return '优惠';\n    case 'coupon':\n      return '券';\n    default:\n      return '活动';\n  }\n};\n\n// 获取推荐背景色\nconst getRecommendationBackground = (type) => {\n  switch (type) {\n    case 'flash':\n      return 'linear-gradient(to bottom right, #FFFFFF, #FFF5F5)';\n    case 'group':\n      return 'linear-gradient(to bottom right, #FFFFFF, #F5F5FF)';\n    case 'discount':\n      return 'linear-gradient(to bottom right, #FFFFFF, #F5FFFA)';\n    case 'coupon':\n      return 'linear-gradient(to bottom right, #FFFFFF, #FFFAF5)';\n    default:\n      return 'linear-gradient(to bottom right, #FFFFFF, #F8F8F8)';\n  }\n};\n\n// 获取推荐标签背景色\nconst getRecommendationTagBackground = (type) => {\n  switch (type) {\n    case 'flash':\n      return 'linear-gradient(135deg, #FF3B30 0%, #FF6E6E 100%)';\n    case 'group':\n      return 'linear-gradient(135deg, #5856D6 0%, #7A7AFF 100%)';\n    case 'discount':\n      return 'linear-gradient(135deg, #34C759 0%, #5EE077 100%)';\n    case 'coupon':\n      return 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)';\n    default:\n      return 'linear-gradient(135deg, #5AC8FA 0%, #64D2FF 100%)';\n  }\n};\n</script>\n\n<style scoped>\n.activity-my-container {\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  overflow: hidden;\n  background-color: #F2F2F7;\n}\n\n/* 自定义导航栏 */\n.custom-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: calc(var(--status-bar-height, 25px) + 62px);\n  width: 100%;\n  z-index: 100;\n}\n  \n  .navbar-bg {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);\n    backdrop-filter: blur(10px);\n    -webkit-backdrop-filter: blur(10px);\n    box-shadow: 0 4px 6px rgba(255,59,105,0.15);\n  }\n  \n  .navbar-content {\n    position: relative;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    height: 100%;\n    padding: 0 30rpx;\n    padding-top: var(--status-bar-height, 25px);\n    box-sizing: border-box;\n  }\n  \n  .navbar-title {\n    font-size: 36rpx;\n    font-weight: 600;\n    color: #FFFFFF;\n    letter-spacing: 0.5px;\n  }\n  \n  .back-btn, .filter-btn {\n    width: 80rpx;\n    height: 80rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n}\n    \n.back-btn .icon, .filter-btn .icon {\n      width: 48rpx;\n      height: 48rpx;\n}\n\n/* 内容区域 */\n.content-scroll {\n  flex: 1;\n  height: calc(100vh - var(--status-bar-height, 25px) - 62px);\n  margin-top: calc(var(--status-bar-height, 25px) + 62px);\n  padding: 30rpx;\n  box-sizing: border-box;\n}\n\n/* 底部安全区域 */\n.safe-area-bottom {\n  height: 100rpx; /* 底部安全区域高度，包括导航栏高度 */\n  padding-bottom: env(safe-area-inset-bottom);\n}\n\n/* 标签页容器 */\n.tabs-container {\n  display: flex;\n  justify-content: space-around;\n  padding: 20rpx 0;\n  margin-bottom: 20rpx;\n  border-radius: 35px 35px 0 0;\n}\n  \n  .tab-item {\n    flex: 1;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    position: relative;\n    padding: 10rpx 0;\n}\n    \n    .tab-text {\n      font-size: 28rpx;\n      color: #8E8E93;\n      transition: color 0.3s ease;\n    }\n    \n    .tab-indicator {\n      position: absolute;\n      bottom: -10rpx;\n      width: 40rpx;\n      height: 3px;\n      border-radius: 1.5px;\n      transition: all 0.3s ease;\n    }\n    \n.tab-item.active .tab-text {\n        color: #FF3B69;\n        font-weight: 500;\n}\n\n/* 活动容器 */\n.activities-container {\n  width: 100%;\n  position: relative;\n  margin-bottom: 30rpx;\n}\n\n/* 活动轮播区域 */\n.activities-swiper {\n  width: 100%;\n  margin-bottom: 30rpx;\n  height: 600px; /* 默认高度 */\n}\n  \n  .tab-content {\n    height: 100%;\n    overflow: visible;\n    padding-bottom: 20rpx;\n}\n    \n    .activity-list {\n      padding: 10rpx 0;\n      display: flex;\n      flex-direction: column;\n      width: 100%;\n    }\n\n/* 活动状态卡片 */\n.activity-status-card {\n  display: flex;\n  flex-direction: column;\n  margin-bottom: 30rpx;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.card-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333333;\n}\n\n.header-right {\n  display: flex;\n  align-items: center;\n}\n\n.view-all {\n  font-size: 28rpx;\n  color: #FF3B69;\n  margin-right: 5rpx;\n}\n\n.icon {\n  width: 16rpx;\n  height: 16rpx;\n}\n\n/* 空状态 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 100rpx 0;\n}\n  \n  .empty-image {\n    width: 200rpx;\n    height: 200rpx;\n    margin-bottom: 30rpx;\n  }\n  \n  .empty-text {\n    font-size: 28rpx;\n    color: #8E8E93;\n    margin-bottom: 30rpx;\n  }\n  \n  .action-btn {\n    padding: 16rpx 40rpx;\n    border-radius: 35px;\n    color: #FFFFFF;\n    font-size: 28rpx;\n}\n    \n.action-btn:active {\n      opacity: 0.9;\n      transform: scale(0.98);\n    }\n\n/* 订单状态网格 */\n.order-status-grid {\n  display: flex;\n  flex-wrap: wrap;\n  margin-top: 20rpx;\n}\n\n.status-item {\n  width: 20%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  position: relative;\n    margin-bottom: 20rpx;\n}\n\n.status-icon {\n  width: 80rpx;\n  height: 80rpx;\n      display: flex;\n      align-items: center;\n  justify-content: center;\n  margin-bottom: 10rpx;\n}\n\n.status-name {\n  font-size: 24rpx;\n        color: #333333;\n}\n\n.status-badge {\n  position: absolute;\n  top: -10rpx;\n  right: 10rpx;\n  min-width: 32rpx;\n  height: 32rpx;\n  border-radius: 16rpx;\n  background-color: #FF3B30;\n  color: #FFFFFF;\n  font-size: 20rpx;\n        display: flex;\n        justify-content: center;\n  align-items: center;\n  padding: 0 6rpx;\n  box-sizing: border-box;\n}\n\n/* 优惠券列表 */\n.coupon-list {\n  margin-top: 20rpx;\n}\n\n/* 主标签容器 */\n.main-tabs-container {\n  display: flex;\n  justify-content: space-around;\n  margin-bottom: 20rpx;\n  border-bottom: 1rpx solid #F2F2F7;\n}\n\n.main-tab-item {\n  flex: 1;\n        display: flex;\n  flex-direction: column;\n        align-items: center;\n  position: relative;\n  padding: 15rpx 0;\n}\n\n.main-tab-item .tab-text {\n  font-size: 28rpx;\n  color: #8E8E93;\n  transition: color 0.3s ease;\n}\n\n.main-tab-item.active .tab-text {\n  color: #FF3B69;\n  font-weight: 600;\n}\n\n/* 子标签容器 */\n.sub-tabs-container {\n    display: flex;\n  justify-content: flex-start;\n    margin-bottom: 20rpx;\n  overflow-x: auto;\n  white-space: nowrap;\n}\n\n.sub-tab-item {\n  display: inline-flex;\n  flex-direction: column;\n  align-items: center;\n      position: relative;\n  padding: 10rpx 30rpx;\n}\n\n.sub-tab-item .tab-text {\n  font-size: 26rpx;\n  color: #8E8E93;\n  transition: color 0.3s ease;\n}\n\n.sub-tab-item.active .tab-text {\n  color: #FF3B69;\n  font-weight: 500;\n}\n\n/* 底部导航栏 */\n.tabbar {\n  position: fixed;\n        bottom: 0;\n  left: 0;\n        right: 0;\n  height: 100rpx;\n  background: #FFFFFF;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n        display: flex;\n  justify-content: space-around;\n        align-items: center;\n  padding-bottom: env(safe-area-inset-bottom);\n  z-index: 99;\n  border-top: 1rpx solid #EEEEEE;\n}\n  \n.tabbar-item {\n  flex: 1;\n      display: flex;\n      flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  padding: 6px 0;\n  box-sizing: border-box;\n  position: relative;\n}\n    \n.tabbar-item:active {\n  transform: scale(0.9);\n}\n    \n.tabbar-item.active .tab-icon {\n  transform: translateY(-5rpx);\n}\n      \n.tabbar-item.active .tabbar-text {\n  color: #FF3B69;\n  font-weight: 600;\n  transform: translateY(-2rpx);\n}\n    \n.tab-icon {\n  width: 24px;\n  height: 24px;\n  margin-bottom: 4px;\n  color: #999999;\n    display: flex;\n  justify-content: center;\n      align-items: center;\n  background-size: contain;\n  background-position: center;\n  background-repeat: no-repeat;\n  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n}\n\n/* 首页图标 */\n.tab-icon.home {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E\");\n}\n\n.tabbar-item.active[data-tab=\"home\"] .tab-icon.home {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E\");\n}\n\n/* 发现图标 */\n.tab-icon.discover {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5.5-2.5l7.51-3.49L17.5 6.5 9.99 9.99 6.5 17.5zm5.5-6.6c.61 0 1.1.49 1.1 1.1s-.49 1.1-1.1 1.1-1.1-.49-1.1-1.1.49-1.1 1.1-1.1z'/%3E%3C/svg%3E\");\n}\n\n.tabbar-item.active[data-tab=\"discover\"] .tab-icon.discover {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5.5-2.5l7.51-3.49L17.5 6.5 9.99 9.99 6.5 17.5zm5.5-6.6c.61 0 1.1.49 1.1 1.1s-.49 1.1-1.1 1.1-1.1-.49-1.1-1.1.49-1.1 1.1-1.1z'/%3E%3C/svg%3E\");\n}\n\n/* 消息图标 */\n.tab-icon.message {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H5.17L4 17.17V4h16v12z'/%3E%3C/svg%3E\");\n}\n\n.tabbar-item.active[data-tab=\"message\"] .tab-icon.message {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E\");\n}\n\n/* 分销图标 */\n.tab-icon.distribution {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E\");\n}\n\n.tabbar-item.active[data-tab=\"distribution\"] .tab-icon.distribution {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E\");\n}\n\n/* 我的图标 */\n.tab-icon.user {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E\");\n}\n\n.tabbar-item.active[data-tab=\"my\"] .tab-icon.user {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E\");\n}\n\n.tabbar-item.active .tab-icon {\n  filter: drop-shadow(0 1px 2px rgba(255, 59, 105, 0.3));\n}\n      \n.badge {\n  position: absolute;\n  top: -8rpx;\n  right: -12rpx;\n  min-width: 32rpx;\n  height: 32rpx;\n  border-radius: 16rpx;\n  background: linear-gradient(135deg, #FF453A, #FF2D55);\n  color: #FFFFFF;\n  font-size: 18rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 0 6rpx;\n  box-sizing: border-box;\n  font-weight: 600;\n  box-shadow: 0 2rpx 6rpx rgba(255, 45, 85, 0.3);\n  border: 1rpx solid rgba(255, 255, 255, 0.8);\n  transform: scale(0.9);\n}\n    \n.tabbar-text {\n  font-size: 22rpx;\n      color: #8E8E93;\n  margin-top: 2rpx;\n  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n}\n    \n.tabbar-item::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  transform: translateX(-50%) scaleX(0);\n  width: 30rpx;\n  height: 4rpx;\n  background: #FF3B69;\n  border-radius: 2rpx;\n  transition: transform 0.3s ease;\n}\n    \n.tabbar-item.active::after {\n  transform: translateX(-50%) scaleX(1);\n}\n\n/* 卡片通用样式 */\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n/* 工具箱样式 */\n.tools-card {\n  border-radius: 35px;\n  box-shadow: 0 8px 20px rgba(88,86,214,0.15);\n  background: #FFFFFF;\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n}\n  \n  .toolbox-header {\n    margin-bottom: 20rpx;\n}\n    \n    .toolbox-title {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #333333;\n  }\n  \n  .tool-grid {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  grid-gap: 20rpx;\n  margin-top: 20rpx;\n}\n    \n    .tool-item {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n  margin-bottom: 10rpx;\n}\n      \n      .tool-icon {\n        width: 80rpx;\n        height: 80rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-bottom: 10rpx;\n  border-radius: 50%;\n      }\n      \n      .tool-name {\n        font-size: 24rpx;\n        color: #333333;\n  text-align: center;\n  width: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n/* 优惠券专区样式 */\n.coupon-card {\n  border-radius: 35px;\n  box-shadow: 0 10px 25px rgba(255,149,0,0.12);\n  background: linear-gradient(to bottom, #FFFFFF, #FFFAF2);\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n  border: 1rpx solid rgba(255,149,0,0.1);\n}\n\n.coupon-status-tabs {\n    display: flex;\n  justify-content: space-around;\n    margin-bottom: 20rpx;\n  border-bottom: 1rpx solid rgba(242,242,247,0.8);\n  padding-bottom: 5rpx;\n}\n\n.coupon-status-tab {\n  flex: 1;\n      display: flex;\n  flex-direction: column;\n      align-items: center;\n  position: relative;\n  padding: 15rpx 0;\n}\n\n.coupon-status-tab text {\n        font-size: 28rpx;\n  color: #8E8E93;\n  transition: color 0.3s ease;\n}\n\n.coupon-status-tab.active text {\n  color: #FF9500;\n  font-weight: 600;\n}\n\n.tab-indicator {\n  position: absolute;\n  bottom: -5rpx;\n  width: 40rpx;\n  height: 4rpx;\n  border-radius: 2rpx;\n}\n\n.coupons-list {\n    margin-top: 20rpx;\n}\n\n.coupon-item {\n  position: relative;\n  display: flex;\n  margin-bottom: 25rpx;\n  border-radius: 20rpx;\n  overflow: hidden;\n  box-shadow: 0 5rpx 15rpx rgba(0,0,0,0.08);\n  transition: transform 0.3s ease;\n}\n\n.coupon-item:active {\n  transform: scale(0.98);\n}\n\n.coupon-left {\n  width: 200rpx;\n  padding: 25rpx 20rpx;\n      display: flex;\n      flex-direction: column;\n  justify-content: center;\n      align-items: center;\n      position: relative;\n  overflow: hidden;\n}\n\n.coupon-left::before {\n  content: '';\n  position: absolute;\n  top: -50%;\n  left: -50%;\n  width: 200%;\n  height: 200%;\n  background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 60%);\n  z-index: 1;\n}\n\n.coupon-value {\n        display: flex;\n  align-items: baseline;\n  position: relative;\n  z-index: 2;\n}\n\n.currency {\n  font-size: 26rpx;\n  font-weight: 600;\n  color: #FFFFFF;\n  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);\n}\n\n.amount {\n  font-size: 52rpx;\n  font-weight: 700;\n  color: #FFFFFF;\n  line-height: 1;\n  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);\n}\n\n.coupon-condition {\n  font-size: 22rpx;\n  color: #FFFFFF;\n  margin-top: 10rpx;\n  position: relative;\n  z-index: 2;\n  text-shadow: 0 1rpx 3rpx rgba(0,0,0,0.2);\n  background-color: rgba(0,0,0,0.1);\n  padding: 4rpx 10rpx;\n  border-radius: 10rpx;\n}\n\n.coupon-divider {\n        position: absolute;\n  left: 200rpx;\n  top: 0;\n  bottom: 0;\n  width: 0;\n  border-left: 1px dashed rgba(255,255,255,0.5);\n  z-index: 2;\n}\n\n.coupon-divider::before, .coupon-divider::after {\n  content: '';\n  position: absolute;\n  left: -10rpx;\n  width: 20rpx;\n  height: 20rpx;\n  border-radius: 50%;\n  background-color: #F8F8F8;\n  box-shadow: inset 0 0 5rpx rgba(0,0,0,0.05);\n  z-index: 2;\n}\n\n.coupon-divider::before {\n  top: -10rpx;\n}\n\n.coupon-divider::after {\n  bottom: -10rpx;\n}\n\n.coupon-right {\n  flex: 1;\n  padding: 25rpx;\n  display: flex;\n  flex-direction: column;\n    justify-content: space-between;\n  background-color: #FFFFFF;\n}\n\n.coupon-name {\n  font-size: 28rpx;\n      font-weight: 600;\n      color: #333333;\n  margin-bottom: 10rpx;\n}\n\n.coupon-shop {\n  font-size: 24rpx;\n  color: #666666;\n  margin-bottom: 15rpx;\n}\n\n.coupon-validity {\n  font-size: 22rpx;\n  color: #999999;\n}\n\n.coupon-use-btn {\n  align-self: flex-end;\n      display: flex;\n  justify-content: center;\n      align-items: center;\n  height: 60rpx;\n  width: 160rpx;\n  border-radius: 30rpx;\n  background: rgba(255,149,0,0.9);\n  font-size: 26rpx;\n  font-weight: 500;\n  color: #FFFFFF;\n  border: 1rpx solid rgba(255,149,0,0.3);\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);\n  transition: all 0.3s ease;\n}\n\n.coupon-use-btn:active {\n  transform: scale(0.95);\n  background: rgba(255,149,0,1);\n}\n\n/* 活动日历样式 */\n.calendar-card {\n  border-radius: 35px;\n  box-shadow: 0 8px 20px rgba(52,199,89,0.15);\n  background: #FFFFFF;\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n}\n\n.calendar-grid {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n  margin-top: 20rpx;\n}\n\n.calendar-item {\n  width: 31%;\n  padding: 15rpx;\n  border-radius: 16rpx;\n  margin-bottom: 15rpx;\n  box-sizing: border-box;\n}\n\n.calendar-date {\n    display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 10rpx;\n}\n\n.date-day {\n  font-size: 24rpx;\n  font-weight: 600;\n  color: #333333;\n}\n\n.date-month {\n  font-size: 20rpx;\n  color: #666666;\n}\n\n.calendar-event {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n}\n\n.event-title {\n  font-size: 22rpx;\n  font-weight: 500;\n  color: #333333;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  width: 100%;\n  text-align: center;\n}\n\n.event-type {\n  font-size: 20rpx;\n  color: #666666;\n  margin-top: 5rpx;\n}\n\n/* 分销中心样式 */\n.distribution-card {\n  border-radius: 35px;\n  box-shadow: 0 8px 20px rgba(172,57,255,0.15);\n  background: linear-gradient(135deg, #FFFFFF 0%, #F9F5FF 100%);\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n  transform: perspective(1000px) rotateX(-2deg);\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n}\n\n.distributor-level {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  height: 40rpx;\n  padding: 0 15rpx;\n  background: linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%);\n  border-radius: 20rpx;\n  margin-left: 15rpx;\n}\n\n.level-text {\n  font-size: 22rpx;\n  color: #FFFFFF;\n  font-weight: 500;\n}\n\n.view-more-btn {\n    display: flex;\n  align-items: center;\n  padding: 10rpx 20rpx;\n  border-radius: 30rpx;\n}\n\n.view-more-btn text {\n  font-size: 24rpx;\n  color: #FFFFFF;\n  margin-right: 5rpx;\n}\n\n.distributor-info {\n      display: flex;\n  align-items: center;\n  margin: 20rpx 0;\n}\n\n.distributor-avatar {\n  width: 100rpx;\n  height: 100rpx;\n  border-radius: 50%;\n      overflow: hidden;\n      position: relative;\n  margin-right: 20rpx;\n}\n\n.distributor-avatar image {\n  width: 100%;\n  height: 100%;\n}\n\n.distributor-badge {\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  width: 30rpx;\n  height: 30rpx;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 2rpx solid #FFFFFF;\n}\n\n.distributor-details {\n        display: flex;\n        flex-direction: column;\n}\n\n.distributor-name {\n          font-size: 28rpx;\n          font-weight: 600;\n          color: #333333;\n          margin-bottom: 5rpx;\n        }\n\n.distributor-id {\n          font-size: 24rpx;\n  color: #666666;\n}\n\n.earnings-overview {\n  display: flex;\n  justify-content: space-between;\n  margin: 30rpx 0;\n}\n\n.earnings-item {\n  display: flex;\n  flex-direction: column;\n    align-items: center;\n}\n\n.earnings-value {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #333333;\n  margin-bottom: 5rpx;\n}\n\n.earnings-label {\n  font-size: 24rpx;\n  color: #666666;\n}\n\n.progress-section {\n  margin-top: 20rpx;\n}\n\n.progress-header {\n      display: flex;\n  justify-content: space-between;\n  margin-bottom: 10rpx;\n}\n\n.progress-title {\n  font-size: 26rpx;\n  color: #333333;\n}\n\n.progress-value {\n  font-size: 26rpx;\n  font-weight: 600;\n  color: #AC39FF;\n}\n\n.progress-bar-bg {\n  width: 100%;\n  height: 10rpx;\n  background: rgba(172,57,255,0.2);\n  border-radius: 10rpx;\n  overflow: hidden;\n  margin-bottom: 10rpx;\n}\n\n.progress-bar-fill {\n  height: 100%;\n  background: linear-gradient(90deg, #AC39FF 0%, #B87AFF 100%);\n  border-radius: 10rpx;\n}\n\n.progress-hint {\n  font-size: 22rpx;\n  color: #666666;\n}\n\n/* 用户信息模块 */\n.user-profile-card {\n  border-radius: 35px;\n  box-shadow: 0 8px 20px rgba(0,0,0,0.08);\n  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n      position: relative;\n  overflow: hidden;\n}\n\n/* 背景装饰 */\n.profile-bg-decoration {\n        position: absolute;\n  top: -50rpx;\n  right: -50rpx;\n  width: 300rpx;\n  height: 300rpx;\n  border-radius: 50%;\n  background: rgba(255,255,255,0.1);\n  z-index: 1;\n}\n\n.profile-bg-decoration:last-child {\n  position: absolute;\n  bottom: -80rpx;\n  left: -80rpx;\n  width: 250rpx;\n  height: 250rpx;\n  border-radius: 50%;\n  background: rgba(255,255,255,0.08);\n  z-index: 1;\n}\n\n/* 用户基本信息 */\n.user-basic-info {\n    display: flex;\n  align-items: center;\n      position: relative;\n  z-index: 2;\n}\n\n.avatar-container {\n        position: relative;\n  margin-right: 20rpx;\n}\n\n.user-avatar {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 50%;\n  border: 4rpx solid rgba(255,255,255,0.8);\n  box-shadow: 0 4rpx 10rpx rgba(0,0,0,0.1);\n}\n\n.vip-badge {\n          position: absolute;\n          bottom: 0;\n  right: 0;\n  background: #FFD700;\n  color: #8B4513;\n  font-size: 20rpx;\n  font-weight: bold;\n  padding: 4rpx 10rpx;\n  border-radius: 10rpx;\n  transform: translateY(50%);\n  box-shadow: 0 2rpx 5rpx rgba(0,0,0,0.2);\n}\n\n.user-info {\n  flex: 1;\n}\n\n.user-name-row {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10rpx;\n}\n\n.user-name {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #FFFFFF;\n  margin-right: 10rpx;\n  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);\n}\n\n.level-tag {\n  font-size: 22rpx;\n  color: #FF3B69;\n  background: rgba(255,255,255,0.9);\n  padding: 2rpx 10rpx;\n  border-radius: 10rpx;\n  font-weight: 500;\n}\n\n.user-id {\n          font-size: 24rpx;\n  color: rgba(255,255,255,0.8);\n  margin-bottom: 10rpx;\n  display: block;\n}\n\n.user-points {\n        display: flex;\n  align-items: center;\n}\n\n.points-label {\n          font-size: 24rpx;\n  color: rgba(255,255,255,0.9);\n        }\n\n.points-value {\n  font-size: 28rpx;\n  font-weight: 600;\n          color: #FFFFFF;\n  margin-left: 8rpx;\n}\n\n.profile-action {\n  padding: 10rpx 30rpx;\n  background: rgba(255,255,255,0.2);\n          border-radius: 30rpx;\n  font-size: 26rpx;\n  color: #FFFFFF;\n  font-weight: 500;\n  border: 1rpx solid rgba(255,255,255,0.3);\n}\n\n/* 用户数据统计 */\n.user-stats {\n  display: flex;\n  justify-content: space-around;\n  margin-top: 30rpx;\n  position: relative;\n  z-index: 2;\n  background: rgba(255,255,255,0.1);\n  border-radius: 20rpx;\n  padding: 20rpx 0;\n}\n\n.stat-item {\n  text-align: center;\n}\n\n.stat-value {\n  font-size: 32rpx;\n  font-weight: bold;\n    color: #FFFFFF;\n  display: block;\n}\n\n.stat-label {\n  font-size: 24rpx;\n  color: rgba(255,255,255,0.8);\n  display: block;\n}\n\n/* 推荐活动模块样式 */\n.recommended-card {\n  border-radius: 35px;\n  box-shadow: 0 8px 20px rgba(90,200,250,0.15);\n  background: linear-gradient(to bottom, #FFFFFF, #F0F8FF);\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n  border: 1rpx solid rgba(90,200,250,0.1);\n}\n\n.recommendation-info {\n  margin-bottom: 20rpx;\n}\n\n.recommendation-subtitle {\n  font-size: 26rpx;\n  color: #666666;\n}\n\n.recommendation-scroll {\n  width: 100%;\n  white-space: nowrap;\n}\n\n.recommendation-list {\n  display: flex;\n  padding: 10rpx 0;\n}\n\n.recommendation-item {\n  display: inline-flex;\n  flex-direction: column;\n  width: 300rpx;\n  margin-right: 20rpx;\n  border-radius: 20rpx;\n  overflow: hidden;\n  box-shadow: 0 5rpx 15rpx rgba(0,0,0,0.08);\n  transition: transform 0.3s ease;\n}\n\n.recommendation-item:active {\n  transform: scale(0.98);\n}\n\n.recommendation-image {\n  width: 100%;\n  height: 180rpx;\n  border-top-left-radius: 20rpx;\n  border-top-right-radius: 20rpx;\n}\n\n.recommendation-content {\n  padding: 15rpx;\n  position: relative;\n}\n\n.recommendation-tag {\n  position: absolute;\n  top: -15rpx;\n  left: 15rpx;\n  padding: 5rpx 15rpx;\n  border-radius: 10rpx;\n  font-size: 20rpx;\n  color: #FFFFFF;\n  font-weight: 500;\n  box-shadow: 0 2rpx 5rpx rgba(0,0,0,0.1);\n}\n\n.recommendation-title {\n  font-size: 26rpx;\n  font-weight: 600;\n  color: #333333;\n  margin-top: 20rpx;\n  margin-bottom: 10rpx;\n  white-space: normal;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  height: 72rpx;\n}\n\n.recommendation-shop {\n  font-size: 22rpx;\n  color: #666666;\n  margin-bottom: 10rpx;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.recommendation-price-row {\n  display: flex;\n  align-items: baseline;\n  margin-bottom: 10rpx;\n}\n\n.current-price {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #FF3B69;\n  margin-right: 10rpx;\n}\n\n.original-price {\n  font-size: 22rpx;\n  color: #999999;\n  text-decoration: line-through;\n}\n\n.recommendation-stats {\n  display: flex;\n  justify-content: space-between;\n  font-size: 20rpx;\n  color: #666666;\n}\n\n.match-rate {\n  color: #5AC8FA;\n  font-weight: 500;\n}\n\n.participation-count {\n  color: #999999;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/my/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "onMounted", "onShow"], "mappings": ";;;;;;;;;;;;;;;AA+tBA,MAAM,qBAAqB,MAAW;;;;AAItC,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAG9B,UAAM,aAAaA,cAAAA,IAAI,CAAC;AACxB,UAAM,sBAAsBA,cAAAA,IAAI,CAAC;AACjC,UAAM,sBAAsBA,cAAAA,IAAI,CAAC;AACjC,UAAM,gBAAgBA,cAAAA,IAAI,IAAI;AAG9B,UAAM,qBAAqBA,cAAAA,IAAI,CAAC;AAGhC,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACnB,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,eAAe;AAAA,MACf,cAAc;AAAA,MACd,aAAa;AAAA,MACb,YAAY;AAAA,IACd,CAAC;AAGD,UAAM,OAAOA,cAAAA,IAAI;AAAA,MACf,EAAE,MAAM,OAAO,QAAQ,WAAW,WAAW,YAAY,YAAY,SAAS,YAAY,yCAA0C;AAAA,MACpI,EAAE,MAAM,OAAO,QAAQ,cAAc,WAAW,YAAY,YAAY,SAAS,YAAY,4CAA6C;AAAA,MAC1I,EAAE,MAAM,OAAO,QAAQ,aAAa,WAAW,YAAY,YAAY,SAAS,YAAY,2CAA4C;AAAA,MACxI,EAAE,MAAM,OAAO,QAAQ,YAAY,WAAW,WAAW,YAAY,SAAS,YAAY,0CAA2C;AAAA,IACvI,CAAC;AAGD,UAAM,gBAAgBA,cAAAA,IAAI;AAAA,MACxB,EAAE,MAAM,MAAM,MAAM,OAAO,WAAW,YAAY,YAAY,8CAA+C;AAAA,MAC7G,EAAE,MAAM,MAAM,MAAM,SAAS,WAAW,cAAc,YAAY,gDAAiD;AAAA,MACnH,EAAE,MAAM,MAAM,MAAM,SAAS,WAAW,cAAc,YAAY,gDAAiD;AAAA,MACnH,EAAE,MAAM,OAAO,MAAM,UAAU,WAAW,eAAe,YAAY,iDAAkD;AAAA,IACzH,CAAC;AAGD,UAAM,iBAAiBA,cAAAA,IAAI;AAAA,MACzB,EAAE,MAAM,MAAM,QAAQ,OAAO,WAAW,SAAS,YAAY,4CAA6C;AAAA,MAC1G,EAAE,MAAM,OAAO,QAAQ,UAAU,WAAW,YAAY,YAAY,+CAAgD;AAAA,MACpH,EAAE,MAAM,OAAO,QAAQ,QAAQ,WAAW,YAAY,YAAY,6CAA8C;AAAA,MAChH,EAAE,MAAM,OAAO,QAAQ,WAAW,WAAW,YAAY,YAAY,gDAAiD;AAAA,IACxH,CAAC;AAGD,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,WAAW;AAAA,IACb,CAAC;AAGD,UAAM,aAAaA,cAAAA,IAAI,CAAA,CAAE;AAGFA,kBAAAA,IAAI;AAAA,MACzB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,YAAW,oBAAI,KAAM,GAAC,QAAS;AAAA,QAC/B,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,YAAW,oBAAI,QAAO,QAAS,IAAG,IAAI,KAAK,KAAK;AAAA;AAAA,QAChD,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,YAAW,oBAAI,KAAI,GAAG,QAAO,IAAK,IAAI,KAAK,KAAK,KAAK;AAAA;AAAA,QACrD,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACH,CAAC;AAGD,UAAM,eAAeA,cAAAA,IAAI,GAAG;AAG5B,UAAM,mBAAmB,MAAM;AAC7BC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,qBAAqB,MAAM;AAC/BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,YAAY,CAAC,UAAU;AAC3B,iBAAW,QAAQ;AACnB,yBAAmB,KAAK;AAAA,IAC1B;AAGA,UAAM,iBAAiB,CAAC,MAAM;AAC5B,iBAAW,QAAQ,EAAE,OAAO;AAC5B,yBAAmB,EAAE,OAAO,OAAO;AAAA,IACrC;AAGA,UAAM,qBAAqB,CAAC,UAAU;AACpC,0BAAoB,QAAQ;AAC5B,yBAAmB,WAAW,KAAK;AAAA,IACrC;AAGA,UAAM,qBAAqB,CAAC,WAAW,MAAM;AAC3C,iBAAW,MAAM;AACf,cAAM,QAAQA,oBAAI;AAClB,cAAM,OAAO,kBAAkB,QAAQ,EAAE,EAAE,mBAAmB,UAAQ;AACpE,cAAI,QAAQ,KAAK,SAAS,GAAG;AAC3B,yBAAa,QAAQ,KAAK,IAAI,KAAK,KAAK,SAAS,GAAG;AAAA,UAC5D,OAAa;AAEL,kBAAM,qBAAqB;AAAA,cACzB,KAAK,MAAM,QAAQ,EAAE;AAAA,cACrB,cAAc,MAAM,oBAAoB,KAAK,EAAE;AAAA,YACzD;AAGQ,gBAAI,mBAAmB,SAAS,GAAG;AACjC,2BAAa,QAAQ,mBAAmB,SAAS,MAAM;AAAA,YACjE,OAAe;AACL,2BAAa,QAAQ;AAAA,YACtB;AAAA,UACF;AAAA,QACP,CAAK,EAAE,KAAI;AAAA,MACR,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,wBAAwB,CAAC,WAAW;AAExC,UAAI,WAAW,WAAW;AACxB,eAAO;AAAA,UACL;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,YAAW,oBAAI,QAAO,QAAS,IAAG,IAAI,KAAK,KAAK;AAAA;AAAA,YAChD,UAAS,oBAAI,QAAO,QAAS,IAAG,KAAK,KAAK,KAAK;AAAA;AAAA,YAC/C,cAAc;AAAA,YACd,eAAe;AAAA,YACf,MAAM;AAAA,YACN,QAAQ;AAAA,UACT;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,YAAW,oBAAI,QAAO,QAAS,IAAG,IAAI,KAAK,KAAK;AAAA;AAAA,YAChD,UAAS,oBAAI,QAAO,QAAS,IAAG,KAAK,KAAK,KAAK;AAAA;AAAA,YAC/C,cAAc;AAAA,YACd,eAAe;AAAA,YACf,MAAM;AAAA,YACN,QAAQ;AAAA,UACT;AAAA,QACP;AAAA,MACA,WAAa,WAAW,cAAc;AAClC,eAAO;AAAA,UACL;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,YAAW,oBAAI,KAAI,GAAG,QAAO,IAAK,IAAI,KAAK,KAAK,KAAK;AAAA;AAAA,YACrD,UAAS,oBAAI,KAAI,GAAG,QAAO,IAAK,IAAI,KAAK,KAAK,KAAK;AAAA;AAAA,YACnD,cAAc;AAAA,YACd,MAAM;AAAA,YACN,QAAQ;AAAA,UACT;AAAA,QACP;AAAA,MACA,WAAa,WAAW,aAAa;AACjC,eAAO;AAAA,UACL;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,YAAW,oBAAI,KAAI,GAAG,QAAO,IAAK,KAAK,KAAK,KAAK,KAAK;AAAA;AAAA,YACtD,UAAS,oBAAI,KAAI,GAAG,QAAO,IAAK,IAAI,KAAK,KAAK,KAAK;AAAA;AAAA,YACnD,cAAc;AAAA,YACd,MAAM;AAAA,YACN,QAAQ;AAAA,UACT;AAAA,QACP;AAAA,MACA,WAAa,WAAW,YAAY;AAChC,eAAO;AAAA,UACL;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,YAAW,oBAAI,KAAI,GAAG,QAAO,IAAK,IAAI,KAAK,KAAK,KAAK;AAAA;AAAA,YACrD,UAAS,oBAAI,KAAI,GAAG,QAAO,IAAK,IAAI,KAAK,KAAK,KAAK;AAAA;AAAA,YACnD,cAAc;AAAA,YACd,eAAe;AAAA,YACf,MAAM;AAAA,YACN,QAAQ;AAAA,UACT;AAAA,QACP;AAAA,MACG;AAED,aAAO;IACT;AA4BA,UAAM,qBAAqB,CAAC,WAAW;AACrC,UAAI,WAAW,OAAO;AACpB,eAAO;AAAA,UACL,EAAE,IAAI,GAAG,MAAM,gBAAgB,OAAO,IAAI,WAAW,WAAW,gBAAgB,cAAc,UAAU,QAAQ,QAAQ,SAAU;AAAA,UAClI,EAAE,IAAI,GAAG,MAAM,gBAAgB,OAAO,IAAI,WAAW,UAAU,gBAAgB,cAAc,UAAU,cAAc,QAAQ,SAAU;AAAA,UACvI,EAAE,IAAI,GAAG,MAAM,cAAc,OAAO,IAAI,WAAW,WAAW,gBAAgB,cAAc,UAAU,cAAc,QAAQ,OAAQ;AAAA,QAC1I;AAAA,MACA,WAAa,WAAW,UAAU;AAC9B,eAAO;AAAA,UACL,EAAE,IAAI,GAAG,MAAM,gBAAgB,OAAO,IAAI,WAAW,WAAW,gBAAgB,cAAc,UAAU,QAAQ,QAAQ,SAAU;AAAA,UAClI,EAAE,IAAI,GAAG,MAAM,gBAAgB,OAAO,IAAI,WAAW,UAAU,gBAAgB,cAAc,UAAU,cAAc,QAAQ,SAAU;AAAA,QAC7I;AAAA,MACA,WAAa,WAAW,QAAQ;AAC5B,eAAO;AAAA,UACL,EAAE,IAAI,GAAG,MAAM,cAAc,OAAO,IAAI,WAAW,WAAW,gBAAgB,cAAc,UAAU,cAAc,QAAQ,OAAQ;AAAA,QAC1I;AAAA,MACA,WAAa,WAAW,WAAW;AAC/B,eAAO;AAAA,UACL,EAAE,IAAI,GAAG,MAAM,YAAY,OAAO,GAAG,WAAW,UAAU,gBAAgB,cAAc,UAAU,WAAW,QAAQ,UAAW;AAAA,QACtI;AAAA,MACG;AAED,aAAO;IACT;AAGA,UAAM,wBAAwB,CAAC,QAAQ,SAAS;AAE9C,YAAM,iBAAiB,sBAAsB,MAAM;AAGnD,UAAI,SAAS,OAAO;AAClB,eAAO;AAAA,MACR;AAGD,aAAO,eAAe,OAAO,cAAY,SAAS,SAAS,IAAI;AAAA,IACjE;AAGA,UAAM,qBAAqB,CAAC,aAAa;AACvC,UAAI,MAAM;AAEV,cAAO,SAAS,MAAI;AAAA,QAClB,KAAK;AACH,gBAAM,6DAA6D,SAAS,EAAE;AAC9E;AAAA,QACF,KAAK;AACH,gBAAM,4DAA4D,SAAS,EAAE;AAC7E;AAAA,QACF,KAAK;AACH,gBAAM,2DAA2D,SAAS,EAAE;AAC5E;AAAA,QACF,KAAK;AACH,gBAAM,yDAAyD,SAAS,EAAE;AAC1E;AAAA,QACF;AACE,gBAAM,wDAAwD,SAAS,EAAE,SAAS,SAAS,IAAI;AAAA,MAClG;AAED,iBAAW,GAAG;AAAA,IAChB;AAGA,UAAM,gBAAgB,CAAC,aAAa;AAClCA,oBAAAA,MAAI,cAAc;AAAA,QAChB,iBAAiB;AAAA,QACjB,UAAU;AACRA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,iBAAiB,CAAC,aAAa;AACnCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAWA,UAAM,qBAAqB,CAAC,UAAU;AACpC,0BAAoB,QAAQ;AAAA,IAC9B;AAGA,UAAM,YAAY,CAAC,WAAW;AAC5BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,WAAW,OAAO,IAAI;AAAA,QAC7B,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,mBAAmB,CAAC,WAAW;AACnCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,UAAU,OAAO,IAAI;AAAA,QAC5B,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AA8DA,UAAM,sBAAsB,CAAC,WAAW;AACtC,cAAQ,QAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,QAAQ;AAC1BA,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IACxB;AAGA,UAAM,yBAAyB,MAAM;AACnCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,YAAY,MAAM;AACtB,mBAAa,QAAQ;AAGrB,iBAAW,MAAM;AAEf,qBAAa,QAAQ;AAErBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF,GAAE,GAAI;AAAA,IACT;AAGA,UAAM,WAAW,MAAM;AAErBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGAC,kBAAAA,UAAU,MAAM;AAEd,iBAAW,QAAQ,sBAAsB,KAAK,MAAM,WAAW,KAAK,EAAE,MAAM;AAG5E,iBAAW,MAAM;AACf,2BAAmB,WAAW,KAAK;AAAA,MACpC,GAAE,GAAG;AAAA,IACR,CAAC;AAEDC,kBAAAA,OAAO,MAAM;AAEX,iBAAW,QAAQ,sBAAsB,KAAK,MAAM,WAAW,KAAK,EAAE,MAAM;AAG5E,iBAAW,MAAM;AACf,2BAAmB,WAAW,KAAK;AAAA,MACpC,GAAE,GAAG;AAAA,IACR,CAAC;AAGD,UAAM,eAAe,CAAC,QAAQ;AAC5B,UAAI,cAAc,UAAU;AAAK;AAEjC,oBAAc,QAAQ;AAGtB,cAAO,KAAG;AAAA,QACR,KAAK;AACHF,wBAAAA,MAAI,SAAS;AAAA,YACX,KAAK;AAAA,YACL,MAAM,CAAC,QAAQ;AACbA,4BAAA,MAAA,MAAA,SAAA,4DAAc,WAAW,GAAG;AAC5BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,YACL,MAAM,CAAC,QAAQ;AACbA,4BAAA,MAAA,MAAA,SAAA,4DAAc,WAAW,GAAG;AAC5BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,YACL,MAAM,CAAC,QAAQ;AACbA,4BAAA,MAAA,MAAA,SAAA,4DAAc,WAAW,GAAG;AAC5BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,YACL,MAAM,CAAC,QAAQ;AACbA,4BAAA,MAAA,MAAA,SAAA,4DAAc,WAAW,GAAG;AAC5BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AACD;AAAA,MAIH;AAAA,IACH;AAGA,UAAM,wBAAwBD,cAAAA,IAAI;AAAA,MAChC;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,YAAW,oBAAI,KAAM,GAAC,QAAS;AAAA,QAC/B,UAAS,oBAAI,KAAI,GAAG,QAAO,IAAK,IAAI,KAAK,KAAK,KAAK;AAAA;AAAA,QACnD,cAAc;AAAA,QACd,eAAe;AAAA,QACf,MAAM;AAAA,QACN,WAAW;AAAA,QACX,oBAAoB;AAAA,MACrB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,YAAW,oBAAI,KAAM,GAAC,QAAS;AAAA,QAC/B,UAAS,oBAAI,KAAI,GAAG,QAAO,IAAK,IAAI,KAAK,KAAK,KAAK;AAAA;AAAA,QACnD,cAAc;AAAA,QACd,eAAe;AAAA,QACf,MAAM;AAAA,QACN,WAAW;AAAA,QACX,oBAAoB;AAAA,MACrB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,YAAW,oBAAI,KAAM,GAAC,QAAS;AAAA,QAC/B,UAAS,oBAAI,KAAI,GAAG,QAAO,IAAK,IAAI,KAAK,KAAK,KAAK;AAAA;AAAA,QACnD,cAAc;AAAA,QACd,MAAM;AAAA,QACN,WAAW;AAAA,QACX,oBAAoB;AAAA,MACrB;AAAA,IACH,CAAC;AAGD,UAAM,sBAAsB,CAAC,SAAS;AACpC,cAAQ,MAAI;AAAA,QACV,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,UAAM,8BAA8B,CAAC,SAAS;AAC5C,cAAQ,MAAI;AAAA,QACV,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,UAAM,iCAAiC,CAAC,SAAS;AAC/C,cAAQ,MAAI;AAAA,QACV,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC92CA,GAAG,WAAW,eAAe;"}