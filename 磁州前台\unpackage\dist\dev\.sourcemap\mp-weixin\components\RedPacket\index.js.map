{"version": 3, "file": "index.js", "sources": ["components/RedPacket/RedPacketCard.vue", "components/RedPacket/RedPacketPopup.vue", "components/RedPacket/RedPacketCreator.vue", "components/RedPacket/RedPacketSelector.vue", "components/RedPacket/index.js"], "sourcesContent": ["<template>\r\n  <view class=\"red-packet-card\" :class=\"{ 'is-expired': isExpired }\">\r\n    <view class=\"card-content\" @click=\"handleClick\">\r\n      <!-- 红包封面 -->\r\n      <view class=\"card-cover\">\r\n        <image class=\"cover-bg\" src=\"/static/images/red-packet-cover.png\" mode=\"aspectFill\"></image>\r\n        <view class=\"cover-content\">\r\n          <view class=\"sender-info\">\r\n            <image class=\"avatar\" :src=\"redPacket.sender.avatar\" mode=\"aspectFill\"></image>\r\n            <text class=\"nickname\">{{ redPacket.sender.nickname }}</text>\r\n          </view>\r\n          \r\n          <view class=\"packet-info\">\r\n            <text class=\"title\">{{ redPacket.title }}</text>\r\n            <view class=\"amount\" v-if=\"!isExpired\">\r\n              <text class=\"label\">剩余</text>\r\n              <text class=\"value\">{{ formatAmount(redPacket.remainAmount) }}元</text>\r\n            </view>\r\n            <view class=\"amount expired\" v-else>\r\n              <text>已过期</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"progress-bar\">\r\n            <view \r\n              class=\"progress-inner\"\r\n              :style=\"{ width: (redPacket.receivedCount / redPacket.totalCount * 100) + '%' }\"\r\n            ></view>\r\n            <text class=\"progress-text\">{{ redPacket.receivedCount }}/{{ redPacket.totalCount }}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 红包操作区 -->\r\n      <view class=\"card-actions\">\r\n        <view class=\"action-info\">\r\n          <text class=\"time\">{{ formatRemainTime(redPacket.expireTime) }}</text>\r\n          <text class=\"type\">{{ getTypeText(redPacket.type) }}</text>\r\n        </view>\r\n        \r\n        <button \r\n          class=\"action-btn\"\r\n          :class=\"{ 'is-expired': isExpired }\"\r\n          @click.stop=\"handleGrab\"\r\n          :disabled=\"isExpired\"\r\n        >\r\n          {{ isExpired ? '已过期' : '抢红包' }}\r\n        </button>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 红包动画效果 -->\r\n    <view class=\"card-animation\" v-if=\"showAnimation\">\r\n      <view class=\"animation-content\">\r\n        <image class=\"animation-icon\" src=\"/static/images/red-packet-animation.png\" mode=\"aspectFit\"></image>\r\n        <text class=\"animation-text\">恭喜发财</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { formatAmount } from '@/utils/format.js';\r\nimport { RED_PACKET_TYPE } from '@/utils/redPacket.js';\r\nimport { showShareMenu } from '@/utils/share';\r\n\r\nexport default {\r\n  name: 'RedPacketCard',\r\n  \r\n  props: {\r\n    redPacket: {\r\n      type: Object,\r\n      required: true\r\n    }\r\n  },\r\n  \r\n  data() {\r\n    return {\r\n      showAnimation: false\r\n    };\r\n  },\r\n  \r\n  computed: {\r\n    // 是否已过期\r\n    isExpired() {\r\n      return Date.now() > this.redPacket.expireTime;\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    // 处理点击\r\n    handleClick() {\r\n      this.$emit('view', this.redPacket);\r\n    },\r\n    \r\n    // 处理抢红包\r\n    handleGrab() {\r\n      if (this.isExpired) return;\r\n      \r\n      // 显示动画\r\n      this.showAnimation = true;\r\n      setTimeout(() => {\r\n        this.showAnimation = false;\r\n        this.$emit('grab', this.redPacket);\r\n      }, 1000);\r\n    },\r\n    \r\n    // 格式化金额\r\n    formatAmount,\r\n    \r\n    // 格式化剩余时间\r\n    formatRemainTime(expireTime) {\r\n      const now = Date.now();\r\n      const remain = expireTime - now;\r\n      \r\n      if (remain <= 0) return '已过期';\r\n      \r\n      const hours = Math.floor(remain / (60 * 60 * 1000));\r\n      const minutes = Math.floor((remain % (60 * 60 * 1000)) / (60 * 1000));\r\n      \r\n      if (hours > 0) {\r\n        return `剩余${hours}小时${minutes}分钟`;\r\n      } else {\r\n        return `剩余${minutes}分钟`;\r\n      }\r\n    },\r\n    \r\n    // 获取红包类型文本\r\n    getTypeText(type) {\r\n      const typeMap = {\r\n        [RED_PACKET_TYPE.RANDOM]: '拼手气红包',\r\n        [RED_PACKET_TYPE.FIXED]: '普通红包'\r\n      };\r\n      return typeMap[type] || '红包';\r\n    },\r\n    \r\n    async handleShare() {\r\n      try {\r\n        await showShareMenu({\r\n          type: 'redPacket',\r\n          data: this.redPacket\r\n        });\r\n        uni.showToast({\r\n          title: '分享成功',\r\n          icon: 'success'\r\n        });\r\n      } catch (error) {\r\n        uni.showToast({\r\n          title: '分享失败',\r\n          icon: 'error'\r\n        });\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.red-packet-card {\r\n  position: relative;\r\n  margin: 20rpx;\r\n  border-radius: 12rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n  transition: transform 0.3s ease;\r\n  \r\n  &:active {\r\n    transform: scale(0.98);\r\n  }\r\n  \r\n  &.is-expired {\r\n    opacity: 0.8;\r\n  }\r\n  \r\n  .card-content {\r\n    background-color: #fff;\r\n    \r\n    .card-cover {\r\n      position: relative;\r\n      height: 300rpx;\r\n      \r\n      .cover-bg {\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n      \r\n      .cover-content {\r\n        position: relative;\r\n        z-index: 1;\r\n        height: 100%;\r\n        padding: 30rpx;\r\n        display: flex;\r\n        flex-direction: column;\r\n        \r\n        .sender-info {\r\n          display: flex;\r\n          align-items: center;\r\n          margin-bottom: 20rpx;\r\n          \r\n          .avatar {\r\n            width: 64rpx;\r\n            height: 64rpx;\r\n            border-radius: 50%;\r\n            border: 2rpx solid #fff;\r\n            margin-right: 16rpx;\r\n          }\r\n          \r\n          .nickname {\r\n            font-size: 28rpx;\r\n            color: #fff;\r\n            text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);\r\n          }\r\n        }\r\n        \r\n        .packet-info {\r\n          flex: 1;\r\n          display: flex;\r\n          flex-direction: column;\r\n          justify-content: center;\r\n          \r\n          .title {\r\n            font-size: 36rpx;\r\n            color: #fff;\r\n            font-weight: 500;\r\n            margin-bottom: 20rpx;\r\n            text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);\r\n          }\r\n          \r\n          .amount {\r\n            display: flex;\r\n            align-items: baseline;\r\n            margin-bottom: 20rpx;\r\n            \r\n            .label {\r\n              font-size: 28rpx;\r\n              color: #fff;\r\n              margin-right: 8rpx;\r\n              text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);\r\n            }\r\n            \r\n            .value {\r\n              font-size: 48rpx;\r\n              color: #fff;\r\n              font-weight: 500;\r\n              text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);\r\n            }\r\n            \r\n            &.expired {\r\n              font-size: 32rpx;\r\n              color: #fff;\r\n              text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);\r\n            }\r\n          }\r\n        }\r\n        \r\n        .progress-bar {\r\n          height: 4rpx;\r\n          background-color: rgba(255, 255, 255, 0.3);\r\n          border-radius: 2rpx;\r\n          position: relative;\r\n          \r\n          .progress-inner {\r\n            height: 100%;\r\n            background-color: #fff;\r\n            border-radius: 2rpx;\r\n            transition: width 0.3s ease;\r\n          }\r\n          \r\n          .progress-text {\r\n            position: absolute;\r\n            right: 0;\r\n            top: 8rpx;\r\n            font-size: 20rpx;\r\n            color: #fff;\r\n            text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);\r\n          }\r\n        }\r\n      }\r\n    }\r\n    \r\n    .card-actions {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 20rpx;\r\n      background-color: #fff;\r\n      \r\n      .action-info {\r\n        .time {\r\n          font-size: 24rpx;\r\n          color: #999;\r\n          margin-right: 16rpx;\r\n        }\r\n        \r\n        .type {\r\n          font-size: 24rpx;\r\n          color: #666;\r\n        }\r\n      }\r\n      \r\n      .action-btn {\r\n        font-size: 28rpx;\r\n        color: #fff;\r\n        background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);\r\n        border-radius: 32rpx;\r\n        padding: 12rpx 32rpx;\r\n        line-height: 1.5;\r\n        \r\n        &.is-expired {\r\n          background: #f5f5f5;\r\n          color: #999;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .card-animation {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background-color: rgba(0, 0, 0, 0.6);\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    animation: fadeIn 0.3s ease;\r\n    \r\n    .animation-content {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      animation: scaleIn 0.3s ease;\r\n      \r\n      .animation-icon {\r\n        width: 120rpx;\r\n        height: 120rpx;\r\n        margin-bottom: 20rpx;\r\n        animation: rotate 1s linear infinite;\r\n      }\r\n      \r\n      .animation-text {\r\n        font-size: 36rpx;\r\n        color: #fff;\r\n        font-weight: 500;\r\n        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n@keyframes scaleIn {\r\n  from {\r\n    transform: scale(0.8);\r\n  }\r\n  to {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n@keyframes rotate {\r\n  from {\r\n    transform: rotate(0deg);\r\n  }\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n.action-area {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: 20rpx;\r\n}\r\n\r\n.grab-btn, .share-btn {\r\n  flex: 1;\r\n  height: 60rpx;\r\n  line-height: 60rpx;\r\n  text-align: center;\r\n  border-radius: 30rpx;\r\n  font-size: 28rpx;\r\n  margin: 0 10rpx;\r\n}\r\n\r\n.grab-btn {\r\n  background: linear-gradient(45deg, #ff4d4f, #ff7875);\r\n  color: #fff;\r\n}\r\n\r\n.grab-btn[disabled] {\r\n  background: #ccc;\r\n  color: #fff;\r\n}\r\n\r\n.share-btn {\r\n  background: #f5f5f5;\r\n  color: #666;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.share-btn .iconfont {\r\n  margin-right: 6rpx;\r\n  font-size: 24rpx;\r\n}\r\n</style> ", "<template>\r\n  <view class=\"red-packet-popup\" :class=\"{ show: show }\">\r\n    <view class=\"mask\" @click=\"handleClose\"></view>\r\n    \r\n    <!-- 红包主体内容 -->\r\n    <view class=\"red-packet-container\" :class=\"{ 'animation-scale': show }\">\r\n      <!-- 关闭按钮 -->\r\n      <view class=\"close-btn\" @click=\"handleClose\">\r\n        <text class=\"icon\">×</text>\r\n      </view>\r\n      \r\n      <!-- 展示不同状态的红包 -->\r\n      <block v-if=\"currentStep === 'grab'\">\r\n        <view class=\"red-packet-grab\">\r\n          <view class=\"red-packet-top\">\r\n            <view class=\"packet-header\">\r\n              <image :src=\"redPacket.sender?.avatar || '/static/images/default-avatar.png'\" class=\"sender-avatar\"></image>\r\n              <view class=\"packet-info\">\r\n                <text class=\"sender-name\">{{ redPacket.sender?.nickname || '用户' }}的红包</text>\r\n                <text class=\"packet-title\">{{ redPacket.title || '恭喜发财，大吉大利' }}</text>\r\n              </view>\r\n            </view>\r\n            \r\n            <view class=\"packet-envelope\" @click=\"openRedPacket\">\r\n              <image src=\"/static/images/red-packet-envelope.png\" class=\"envelope-image\"></image>\r\n              <view class=\"open-text\">点击拆开</view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </block>\r\n      \r\n      <block v-else-if=\"currentStep === 'opening'\">\r\n        <view class=\"red-packet-opening\">\r\n          <view class=\"opening-animation\">\r\n            <image src=\"/static/images/red-packet-opening.gif\" mode=\"aspectFit\"></image>\r\n          </view>\r\n          <text class=\"opening-text\">正在拆红包...</text>\r\n        </view>\r\n      </block>\r\n      \r\n      <block v-else-if=\"currentStep === 'result'\">\r\n        <view class=\"red-packet-result\">\r\n          <view class=\"result-header\">\r\n            <image :src=\"redPacket.sender?.avatar || '/static/images/default-avatar.png'\" class=\"sender-avatar\"></image>\r\n            <view class=\"result-info\">\r\n              <text class=\"packet-desc\">{{ redPacket.sender?.nickname || '用户' }}的红包</text>\r\n              <text class=\"packet-title\">{{ redPacket.title || '恭喜发财，大吉大利' }}</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"result-content\">\r\n            <view v-if=\"grabResult.status === 'success'\" class=\"result-success\">\r\n              <image src=\"/static/images/red-packet-success.png\" class=\"result-icon\"></image>\r\n              <text class=\"amount-text\">{{ formatAmount(grabResult.amount) }}</text>\r\n              <text class=\"amount-unit\">元</text>\r\n              <text class=\"result-tips\">已存入钱包，可直接提现</text>\r\n            </view>\r\n            \r\n            <view v-else-if=\"grabResult.status === 'fail'\" class=\"result-fail\">\r\n              <image src=\"/static/images/red-packet-fail.png\" class=\"result-icon\"></image>\r\n              <text class=\"fail-text\">{{ grabResult.message }}</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"action-area\">\r\n            <button \r\n              class=\"action-btn check-wallet\"\r\n              v-if=\"grabResult.status === 'success'\"\r\n              @click=\"goToWallet\"\r\n            >查看钱包</button>\r\n            \r\n            <button \r\n              v-else\r\n              class=\"action-btn know\"\r\n              @click=\"handleClose\"\r\n            >我知道了</button>\r\n          </view>\r\n        </view>\r\n      </block>\r\n      \r\n      <block v-else-if=\"currentStep === 'detail'\">\r\n        <view class=\"red-packet-detail\">\r\n          <view class=\"detail-header\">\r\n            <image :src=\"redPacket.sender?.avatar || '/static/images/default-avatar.png'\" class=\"sender-avatar\"></image>\r\n            <view class=\"detail-info\">\r\n              <text class=\"packet-desc\">{{ redPacket.sender?.nickname || '用户' }}的红包</text>\r\n              <text class=\"packet-title\">{{ redPacket.title || '恭喜发财，大吉大利' }}</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"detail-summary\">\r\n            <text class=\"summary-text\">已领取 {{ redPacket.totalCount - redPacket.remainCount || 0 }}/{{ redPacket.totalCount || 0 }} 个，共 {{ formatAmount(redPacket.totalAmount - redPacket.remainAmount) }}/{{ formatAmount(redPacket.totalAmount) }} 元</text>\r\n          </view>\r\n          \r\n          <scroll-view \r\n            class=\"grab-records\"\r\n            scroll-y \r\n            enable-flex\r\n          >\r\n            <block v-if=\"records && records.length\">\r\n              <view\r\n                v-for=\"(record, index) in records\"\r\n                :key=\"index\"\r\n                class=\"record-item\"\r\n              >\r\n                <view class=\"record-user\">\r\n                  <image :src=\"record.avatar || '/static/images/default-avatar.png'\" class=\"user-avatar\"></image>\r\n                  <text class=\"user-name\">{{ record.nickname || '用户' }}</text>\r\n                </view>\r\n                \r\n                <view class=\"record-amount\">\r\n                  <text class=\"amount-value\">{{ formatAmount(record.amount) }}</text>\r\n                  <text class=\"amount-unit\">元</text>\r\n                  <text v-if=\"record.isBest\" class=\"best-tag\">手气最佳</text>\r\n                </view>\r\n                \r\n                <text class=\"record-time\">{{ formatTime(record.grabTime) }}</text>\r\n              </view>\r\n            </block>\r\n            \r\n            <view v-else class=\"no-records\">\r\n              <text>暂无领取记录</text>\r\n            </view>\r\n          </scroll-view>\r\n          \r\n          <view class=\"action-area\">\r\n            <button class=\"action-btn know\" @click=\"handleClose\">我知道了</button>\r\n          </view>\r\n        </view>\r\n      </block>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { formatRedPacketAmount } from '@/utils/redPacket.js';\r\nimport { grabRedPacket } from '@/utils/redPacket.js';\r\nimport { formatDate } from '@/utils/date.js';\r\n\r\nexport default {\r\n  name: 'RedPacketPopup',\r\n  props: {\r\n    // 是否显示弹窗\r\n    show: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 红包数据\r\n    redPacket: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 红包记录\r\n    records: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 初始步骤\r\n    initialStep: {\r\n      type: String,\r\n      default: 'grab' // grab, opening, result, detail\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      currentStep: 'grab',\r\n      grabResult: {\r\n        status: '',\r\n        amount: 0,\r\n        message: ''\r\n      }\r\n    };\r\n  },\r\n  watch: {\r\n    show(val) {\r\n      if (val) {\r\n        this.currentStep = this.initialStep;\r\n      }\r\n    },\r\n    initialStep(val) {\r\n      if (this.show) {\r\n        this.currentStep = val;\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    // 关闭弹窗\r\n    handleClose() {\r\n      this.$emit('close');\r\n    },\r\n    \r\n    // 打开红包\r\n    openRedPacket() {\r\n      this.currentStep = 'opening';\r\n      \r\n      // 调用抢红包接口\r\n      grabRedPacket(this.redPacket.id).then(res => {\r\n        setTimeout(() => {\r\n          this.grabResult = {\r\n            status: 'success',\r\n            amount: res.amount,\r\n            message: ''\r\n          };\r\n          this.currentStep = 'result';\r\n          this.$emit('grab-success', res);\r\n        }, 1500); // 延迟展示结果，增强体验\r\n      }).catch(err => {\r\n        setTimeout(() => {\r\n          this.grabResult = {\r\n            status: 'fail',\r\n            amount: 0,\r\n            message: err.message || '红包已抢完'\r\n          };\r\n          this.currentStep = 'result';\r\n        }, 1500);\r\n      });\r\n    },\r\n    \r\n    // 格式化金额\r\n    formatAmount(amount) {\r\n      return formatRedPacketAmount(amount);\r\n    },\r\n    \r\n    // 格式化时间\r\n    formatTime(timestamp) {\r\n      return formatDate(timestamp, 'MM-DD HH:mm');\r\n    },\r\n    \r\n    // 跳转到钱包\r\n    goToWallet() {\r\n      uni.navigateTo({\r\n        url: '/subPackages/payment/pages/wallet'\r\n      });\r\n      this.handleClose();\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.red-packet-popup {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 1000;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n  transition: all 0.3s;\r\n  \r\n  &.show {\r\n    opacity: 1;\r\n    visibility: visible;\r\n  }\r\n  \r\n  .mask {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background-color: rgba(0, 0, 0, 0.6);\r\n    backdrop-filter: blur(4px);\r\n  }\r\n  \r\n  .red-packet-container {\r\n    position: relative;\r\n    width: 650rpx;\r\n    max-height: 80vh;\r\n    z-index: 1001;\r\n    transform: scale(0.8);\r\n    transition: transform 0.3s;\r\n    \r\n    &.animation-scale {\r\n      transform: scale(1);\r\n    }\r\n    \r\n    .close-btn {\r\n      position: absolute;\r\n      top: -80rpx;\r\n      right: 0;\r\n      width: 60rpx;\r\n      height: 60rpx;\r\n      background-color: rgba(0, 0, 0, 0.3);\r\n      border-radius: 50%;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      z-index: 10;\r\n      \r\n      .icon {\r\n        color: #fff;\r\n        font-size: 40rpx;\r\n        line-height: 1;\r\n      }\r\n    }\r\n  }\r\n  \r\n  /* 抢红包阶段样式 */\r\n  .red-packet-grab {\r\n    width: 100%;\r\n    background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);\r\n    border-radius: 24rpx;\r\n    overflow: hidden;\r\n    box-shadow: 0 8rpx 30rpx rgba(255, 8, 68, 0.3);\r\n    \r\n    .red-packet-top {\r\n      padding: 40rpx 30rpx;\r\n      \r\n      .packet-header {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 40rpx;\r\n        \r\n        .sender-avatar {\r\n          width: 80rpx;\r\n          height: 80rpx;\r\n          border-radius: 50%;\r\n          border: 4rpx solid rgba(255, 255, 255, 0.3);\r\n          margin-right: 20rpx;\r\n        }\r\n        \r\n        .packet-info {\r\n          color: #fff;\r\n          \r\n          .sender-name {\r\n            display: block;\r\n            font-size: 28rpx;\r\n            margin-bottom: 8rpx;\r\n            opacity: 0.9;\r\n          }\r\n          \r\n          .packet-title {\r\n            display: block;\r\n            font-size: 36rpx;\r\n            font-weight: 600;\r\n          }\r\n        }\r\n      }\r\n      \r\n      .packet-envelope {\r\n        width: 100%;\r\n        height: 600rpx;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: center;\r\n        position: relative;\r\n        \r\n        .envelope-image {\r\n          width: 400rpx;\r\n          height: 400rpx;\r\n          transform: rotate(-5deg);\r\n        }\r\n        \r\n        .open-text {\r\n          position: absolute;\r\n          bottom: 40rpx;\r\n          color: #FFEE95;\r\n          font-size: 36rpx;\r\n          font-weight: 600;\r\n          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);\r\n          animation: pulse 1.5s infinite;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  /* 正在打开红包样式 */\r\n  .red-packet-opening {\r\n    width: 100%;\r\n    height: 750rpx;\r\n    background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);\r\n    border-radius: 24rpx;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    \r\n    .opening-animation {\r\n      width: 300rpx;\r\n      height: 300rpx;\r\n      margin-bottom: 40rpx;\r\n      \r\n      image {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n    \r\n    .opening-text {\r\n      color: #fff;\r\n      font-size: 36rpx;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n  \r\n  /* 红包结果样式 */\r\n  .red-packet-result {\r\n    width: 100%;\r\n    background-color: #fff;\r\n    border-radius: 24rpx;\r\n    overflow: hidden;\r\n    \r\n    .result-header {\r\n      padding: 40rpx 30rpx;\r\n      background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);\r\n      display: flex;\r\n      align-items: center;\r\n      \r\n      .sender-avatar {\r\n        width: 80rpx;\r\n        height: 80rpx;\r\n        border-radius: 50%;\r\n        border: 4rpx solid rgba(255, 255, 255, 0.3);\r\n        margin-right: 20rpx;\r\n      }\r\n      \r\n      .result-info {\r\n        color: #fff;\r\n        \r\n        .packet-desc {\r\n          display: block;\r\n          font-size: 28rpx;\r\n          margin-bottom: 8rpx;\r\n          opacity: 0.9;\r\n        }\r\n        \r\n        .packet-title {\r\n          display: block;\r\n          font-size: 32rpx;\r\n          font-weight: 600;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .result-content {\r\n      padding: 60rpx 30rpx;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      \r\n      .result-success {\r\n        text-align: center;\r\n        \r\n        .result-icon {\r\n          width: 120rpx;\r\n          height: 120rpx;\r\n          margin-bottom: 30rpx;\r\n        }\r\n        \r\n        .amount-text {\r\n          font-size: 80rpx;\r\n          font-weight: 600;\r\n          color: #FF3B30;\r\n          font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;\r\n        }\r\n        \r\n        .amount-unit {\r\n          font-size: 36rpx;\r\n          color: #FF3B30;\r\n          margin-left: 8rpx;\r\n        }\r\n        \r\n        .result-tips {\r\n          display: block;\r\n          margin-top: 24rpx;\r\n          font-size: 28rpx;\r\n          color: #999;\r\n        }\r\n      }\r\n      \r\n      .result-fail {\r\n        text-align: center;\r\n        \r\n        .result-icon {\r\n          width: 120rpx;\r\n          height: 120rpx;\r\n          margin-bottom: 30rpx;\r\n        }\r\n        \r\n        .fail-text {\r\n          font-size: 32rpx;\r\n          color: #666;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .action-area {\r\n      padding: 0 30rpx 40rpx;\r\n      display: flex;\r\n      justify-content: center;\r\n      \r\n      .action-btn {\r\n        width: 320rpx;\r\n        height: 80rpx;\r\n        line-height: 80rpx;\r\n        border-radius: 40rpx;\r\n        font-size: 30rpx;\r\n        font-weight: 500;\r\n        \r\n        &.check-wallet {\r\n          background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);\r\n          color: #fff;\r\n        }\r\n        \r\n        &.know {\r\n          background-color: #F5F5F5;\r\n          color: #666;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  /* 红包详情样式 */\r\n  .red-packet-detail {\r\n    width: 100%;\r\n    background-color: #fff;\r\n    border-radius: 24rpx;\r\n    overflow: hidden;\r\n    max-height: 80vh;\r\n    display: flex;\r\n    flex-direction: column;\r\n    \r\n    .detail-header {\r\n      padding: 40rpx 30rpx;\r\n      background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);\r\n      display: flex;\r\n      align-items: center;\r\n      \r\n      .sender-avatar {\r\n        width: 80rpx;\r\n        height: 80rpx;\r\n        border-radius: 50%;\r\n        border: 4rpx solid rgba(255, 255, 255, 0.3);\r\n        margin-right: 20rpx;\r\n      }\r\n      \r\n      .detail-info {\r\n        color: #fff;\r\n        \r\n        .packet-desc {\r\n          display: block;\r\n          font-size: 28rpx;\r\n          margin-bottom: 8rpx;\r\n          opacity: 0.9;\r\n        }\r\n        \r\n        .packet-title {\r\n          display: block;\r\n          font-size: 32rpx;\r\n          font-weight: 600;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .detail-summary {\r\n      padding: 24rpx 30rpx;\r\n      border-bottom: 1px solid #F0F0F0;\r\n      \r\n      .summary-text {\r\n        font-size: 28rpx;\r\n        color: #666;\r\n      }\r\n    }\r\n    \r\n    .grab-records {\r\n      flex: 1;\r\n      max-height: 600rpx;\r\n      padding: 0 30rpx;\r\n      \r\n      .record-item {\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 24rpx 0;\r\n        border-bottom: 1px solid #F5F5F5;\r\n        \r\n        &:last-child {\r\n          border-bottom: none;\r\n        }\r\n        \r\n        .record-user {\r\n          display: flex;\r\n          align-items: center;\r\n          flex: 1;\r\n          \r\n          .user-avatar {\r\n            width: 64rpx;\r\n            height: 64rpx;\r\n            border-radius: 50%;\r\n            margin-right: 16rpx;\r\n          }\r\n          \r\n          .user-name {\r\n            font-size: 28rpx;\r\n            color: #333;\r\n            max-width: 200rpx;\r\n            white-space: nowrap;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n          }\r\n        }\r\n        \r\n        .record-amount {\r\n          display: flex;\r\n          align-items: center;\r\n          \r\n          .amount-value {\r\n            font-size: 36rpx;\r\n            color: #FF3B30;\r\n            font-weight: 600;\r\n            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;\r\n          }\r\n          \r\n          .amount-unit {\r\n            font-size: 24rpx;\r\n            color: #FF3B30;\r\n            margin-left: 4rpx;\r\n          }\r\n          \r\n          .best-tag {\r\n            margin-left: 12rpx;\r\n            font-size: 20rpx;\r\n            color: #fff;\r\n            background-color: #FF9500;\r\n            padding: 4rpx 8rpx;\r\n            border-radius: 6rpx;\r\n          }\r\n        }\r\n        \r\n        .record-time {\r\n          font-size: 24rpx;\r\n          color: #999;\r\n          margin-left: 24rpx;\r\n        }\r\n      }\r\n      \r\n      .no-records {\r\n        padding: 80rpx 0;\r\n        text-align: center;\r\n        color: #999;\r\n        font-size: 28rpx;\r\n      }\r\n    }\r\n    \r\n    .action-area {\r\n      padding: 24rpx 30rpx;\r\n      display: flex;\r\n      justify-content: center;\r\n      border-top: 1px solid #F0F0F0;\r\n      \r\n      .action-btn {\r\n        width: 320rpx;\r\n        height: 80rpx;\r\n        line-height: 80rpx;\r\n        border-radius: 40rpx;\r\n        font-size: 30rpx;\r\n        font-weight: 500;\r\n        \r\n        &.know {\r\n          background-color: #F5F5F5;\r\n          color: #666;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 红包动画 */\r\n@keyframes pulse {\r\n  0% {\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    transform: scale(1.1);\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n  }\r\n}\r\n</style> ", "<template>\r\n  <view class=\"red-packet-creator\">\r\n    <!-- 创建红包表单 -->\r\n    <view class=\"creator-form\">\r\n      <!-- 红包类型选择 -->\r\n      <view class=\"form-item\">\r\n        <view class=\"form-label\">红包类型</view>\r\n        <view class=\"type-selector\">\r\n          <view \r\n            v-for=\"(type, index) in packetTypes\" \r\n            :key=\"index\"\r\n            class=\"type-item\"\r\n            :class=\"{ active: formData.type === type.value }\"\r\n            @click=\"formData.type = type.value\"\r\n          >\r\n            <image :src=\"type.icon\" class=\"type-icon\"></image>\r\n            <text class=\"type-name\">{{ type.name }}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 红包金额 -->\r\n      <view class=\"form-item\">\r\n        <view class=\"form-label\">红包金额</view>\r\n        <view class=\"amount-input-container\">\r\n          <input \r\n            class=\"amount-input\" \r\n            type=\"digit\" \r\n            v-model=\"formData.amount\"\r\n            placeholder=\"输入红包金额\"\r\n            @input=\"validateAmount\"\r\n          />\r\n          <text class=\"amount-unit\">元</text>\r\n        </view>\r\n        <text class=\"available-balance\">当前余额：{{ formatAmount(userBalance) }}元</text>\r\n      </view>\r\n      \r\n      <!-- 红包数量 -->\r\n      <view class=\"form-item\" v-if=\"formData.type === 1\">\r\n        <view class=\"form-label\">红包个数</view>\r\n        <view class=\"count-selector\">\r\n          <button class=\"count-btn minus\" @click=\"decreaseCount\">-</button>\r\n          <input \r\n            class=\"count-input\" \r\n            type=\"number\" \r\n            v-model=\"formData.count\"\r\n            @input=\"validateCount\"\r\n          />\r\n          <button class=\"count-btn plus\" @click=\"increaseCount\">+</button>\r\n        </view>\r\n        <text class=\"amount-tips\">每人最多可得 {{ formatAmount(maxAmountPerPerson) }}元</text>\r\n      </view>\r\n      \r\n      <!-- 红包个数 - 固定红包模式 -->\r\n      <view class=\"form-item\" v-else-if=\"formData.type === 2\">\r\n        <view class=\"form-label\">红包个数</view>\r\n        <view class=\"count-selector\">\r\n          <button class=\"count-btn minus\" @click=\"decreaseCount\">-</button>\r\n          <input \r\n            class=\"count-input\" \r\n            type=\"number\" \r\n            v-model=\"formData.count\"\r\n            @input=\"validateCount\"\r\n          />\r\n          <button class=\"count-btn plus\" @click=\"increaseCount\">+</button>\r\n        </view>\r\n        <text class=\"amount-tips\">每人固定可得 {{ formatAmount(fixedAmountPerPerson) }}元</text>\r\n      </view>\r\n      \r\n      <!-- 红包祝福语 -->\r\n      <view class=\"form-item\">\r\n        <view class=\"form-label\">祝福语</view>\r\n        <view class=\"blessing-input-container\">\r\n          <input \r\n            class=\"blessing-input\" \r\n            type=\"text\" \r\n            v-model=\"formData.blessing\"\r\n            placeholder=\"恭喜发财，大吉大利\"\r\n            maxlength=\"30\"\r\n          />\r\n          <text class=\"blessing-count\">{{ formData.blessing.length }}/30</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 过期时间设置 -->\r\n      <view class=\"form-item\">\r\n        <view class=\"form-label\">过期时间</view>\r\n        <view class=\"expire-selector\">\r\n          <view \r\n            v-for=\"(expire, index) in expireOptions\" \r\n            :key=\"index\"\r\n            class=\"expire-item\"\r\n            :class=\"{ active: formData.expireHours === expire.value }\"\r\n            @click=\"formData.expireHours = expire.value\"\r\n          >\r\n            <text>{{ expire.label }}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 提交按钮 -->\r\n    <view class=\"action-area\">\r\n      <view class=\"balance-warning\" v-if=\"insufficientBalance\">\r\n        <text class=\"warning-text\">余额不足，请先充值</text>\r\n      </view>\r\n      \r\n      <button \r\n        class=\"submit-btn\" \r\n        :disabled=\"!canSubmit || loading\" \r\n        :loading=\"loading\"\r\n        @click=\"handleSubmit\"\r\n      >\r\n        {{ loading ? '创建中...' : '塞入红包' }}\r\n      </button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { RED_PACKET_TYPE, createRedPacket, formatRedPacketAmount } from '@/utils/redPacket.js';\r\n\r\nexport default {\r\n  name: 'RedPacketCreator',\r\n  props: {\r\n    // 用户余额（单位：分）\r\n    userBalance: {\r\n      type: Number,\r\n      default: 0\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // 表单数据\r\n      formData: {\r\n        type: RED_PACKET_TYPE.RANDOM, // 默认为拼手气红包\r\n        amount: '', // 红包金额（元）\r\n        count: 3, // 红包个数\r\n        blessing: '恭喜发财，大吉大利', // 祝福语\r\n        expireHours: 24 // 过期时间（小时）\r\n      },\r\n      // 红包类型列表\r\n      packetTypes: [\r\n        {\r\n          name: '拼手气红包',\r\n          value: RED_PACKET_TYPE.RANDOM,\r\n          icon: '/static/images/random-packet-icon.png'\r\n        },\r\n        {\r\n          name: '普通红包',\r\n          value: RED_PACKET_TYPE.FIXED,\r\n          icon: '/static/images/fixed-packet-icon.png'\r\n        }\r\n      ],\r\n      // 过期时间选项\r\n      expireOptions: [\r\n        { label: '24小时', value: 24 },\r\n        { label: '48小时', value: 48 },\r\n        { label: '72小时', value: 72 }\r\n      ],\r\n      loading: false // 提交加载状态\r\n    };\r\n  },\r\n  computed: {\r\n    // 红包总金额（分）\r\n    totalAmountInCents() {\r\n      const amount = parseFloat(this.formData.amount || 0);\r\n      return Math.floor(amount * 100); // 转换为分\r\n    },\r\n    \r\n    // 拼手气红包每人最多可得金额\r\n    maxAmountPerPerson() {\r\n      if (!this.formData.count || this.formData.count <= 0) return 0;\r\n      // 拼手气红包理论上单个人可以得到接近总金额\r\n      return this.totalAmountInCents;\r\n    },\r\n    \r\n    // 普通红包每人固定金额\r\n    fixedAmountPerPerson() {\r\n      if (!this.formData.count || this.formData.count <= 0) return 0;\r\n      // 普通红包每人金额相等\r\n      return Math.floor(this.totalAmountInCents / this.formData.count);\r\n    },\r\n    \r\n    // 是否余额不足\r\n    insufficientBalance() {\r\n      return this.totalAmountInCents > this.userBalance;\r\n    },\r\n    \r\n    // 是否可以提交\r\n    canSubmit() {\r\n      return (\r\n        this.totalAmountInCents > 0 && \r\n        this.formData.count > 0 && \r\n        !this.insufficientBalance\r\n      );\r\n    }\r\n  },\r\n  methods: {\r\n    // 格式化金额\r\n    formatAmount(amount) {\r\n      return formatRedPacketAmount(amount);\r\n    },\r\n    \r\n    // 校验金额\r\n    validateAmount() {\r\n      if (this.formData.amount === '') return;\r\n      \r\n      let amount = parseFloat(this.formData.amount);\r\n      \r\n      // 限制最小金额为0.01元\r\n      if (amount < 0.01) {\r\n        amount = 0.01;\r\n      }\r\n      \r\n      // 限制最大金额\r\n      const maxAmount = this.formatAmount(this.userBalance);\r\n      if (amount > parseFloat(maxAmount)) {\r\n        amount = parseFloat(maxAmount);\r\n      }\r\n      \r\n      // 保留两位小数\r\n      this.formData.amount = amount.toFixed(2);\r\n    },\r\n    \r\n    // 校验红包个数\r\n    validateCount() {\r\n      if (!this.formData.count) {\r\n        this.formData.count = 1;\r\n        return;\r\n      }\r\n      \r\n      let count = parseInt(this.formData.count);\r\n      \r\n      // 至少1个红包\r\n      if (count < 1) {\r\n        count = 1;\r\n      }\r\n      \r\n      // 最多100个红包\r\n      if (count > 100) {\r\n        count = 100;\r\n      }\r\n      \r\n      this.formData.count = count;\r\n    },\r\n    \r\n    // 减少红包个数\r\n    decreaseCount() {\r\n      if (this.formData.count > 1) {\r\n        this.formData.count--;\r\n      }\r\n    },\r\n    \r\n    // 增加红包个数\r\n    increaseCount() {\r\n      if (this.formData.count < 100) {\r\n        this.formData.count++;\r\n      }\r\n    },\r\n    \r\n    // 提交创建红包\r\n    handleSubmit() {\r\n      if (!this.canSubmit || this.loading) return;\r\n      \r\n      this.loading = true;\r\n      \r\n      // 构建请求参数\r\n      const params = {\r\n        type: this.formData.type,\r\n        totalAmount: this.totalAmountInCents,\r\n        totalCount: this.formData.count,\r\n        title: this.formData.blessing,\r\n        expireTime: Date.now() + this.formData.expireHours * 60 * 60 * 1000\r\n      };\r\n      \r\n      // 调用创建红包接口\r\n      createRedPacket(params)\r\n        .then(res => {\r\n          // 创建成功，返回红包数据\r\n          this.$emit('create-success', res);\r\n          this.resetForm();\r\n        })\r\n        .catch(err => {\r\n          // 创建失败，提示错误\r\n          uni.showToast({\r\n            title: err.message || '创建红包失败',\r\n            icon: 'none'\r\n          });\r\n        })\r\n        .finally(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    \r\n    // 重置表单\r\n    resetForm() {\r\n      this.formData = {\r\n        type: RED_PACKET_TYPE.RANDOM,\r\n        amount: '',\r\n        count: 3,\r\n        blessing: '恭喜发财，大吉大利',\r\n        expireHours: 24\r\n      };\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.red-packet-creator {\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  padding: 30rpx;\r\n  \r\n  .creator-form {\r\n    .form-item {\r\n      margin-bottom: 30rpx;\r\n      \r\n      &:last-child {\r\n        margin-bottom: 40rpx;\r\n      }\r\n      \r\n      .form-label {\r\n        font-size: 28rpx;\r\n        color: #333;\r\n        margin-bottom: 20rpx;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n    \r\n    .type-selector {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      \r\n      .type-item {\r\n        flex: 1;\r\n        height: 110rpx;\r\n        background-color: #F8F8F8;\r\n        border-radius: 8rpx;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: center;\r\n        margin: 0 10rpx;\r\n        \r\n        &:first-child {\r\n          margin-left: 0;\r\n        }\r\n        \r\n        &:last-child {\r\n          margin-right: 0;\r\n        }\r\n        \r\n        &.active {\r\n          background: linear-gradient(135deg, #FFE4E1 0%, #FFF0F5 100%);\r\n          border: 1px solid #FF6347;\r\n        }\r\n        \r\n        .type-icon {\r\n          width: 48rpx;\r\n          height: 48rpx;\r\n          margin-bottom: 8rpx;\r\n        }\r\n        \r\n        .type-name {\r\n          font-size: 24rpx;\r\n          color: #333;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .amount-input-container {\r\n      position: relative;\r\n      height: 90rpx;\r\n      \r\n      .amount-input {\r\n        height: 100%;\r\n        background-color: #F8F8F8;\r\n        border-radius: 8rpx;\r\n        padding: 0 100rpx 0 30rpx;\r\n        font-size: 32rpx;\r\n        color: #333;\r\n      }\r\n      \r\n      .amount-unit {\r\n        position: absolute;\r\n        right: 30rpx;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        font-size: 28rpx;\r\n        color: #999;\r\n      }\r\n    }\r\n    \r\n    .available-balance {\r\n      font-size: 24rpx;\r\n      color: #999;\r\n      margin-top: 12rpx;\r\n      display: block;\r\n    }\r\n    \r\n    .count-selector {\r\n      display: flex;\r\n      align-items: center;\r\n      height: 90rpx;\r\n      \r\n      .count-btn {\r\n        width: 90rpx;\r\n        height: 90rpx;\r\n        background-color: #F8F8F8;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        font-size: 36rpx;\r\n        color: #333;\r\n        margin: 0;\r\n        padding: 0;\r\n        line-height: 1;\r\n        \r\n        &.minus {\r\n          border-radius: 8rpx 0 0 8rpx;\r\n        }\r\n        \r\n        &.plus {\r\n          border-radius: 0 8rpx 8rpx 0;\r\n        }\r\n      }\r\n      \r\n      .count-input {\r\n        flex: 1;\r\n        height: 100%;\r\n        background-color: #F8F8F8;\r\n        text-align: center;\r\n        font-size: 32rpx;\r\n        color: #333;\r\n        margin: 0 2rpx;\r\n      }\r\n    }\r\n    \r\n    .amount-tips {\r\n      font-size: 24rpx;\r\n      color: #FF6347;\r\n      margin-top: 12rpx;\r\n      display: block;\r\n    }\r\n    \r\n    .blessing-input-container {\r\n      position: relative;\r\n      height: 90rpx;\r\n      \r\n      .blessing-input {\r\n        height: 100%;\r\n        background-color: #F8F8F8;\r\n        border-radius: 8rpx;\r\n        padding: 0 80rpx 0 30rpx;\r\n        font-size: 28rpx;\r\n        color: #333;\r\n      }\r\n      \r\n      .blessing-count {\r\n        position: absolute;\r\n        right: 20rpx;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        font-size: 24rpx;\r\n        color: #999;\r\n      }\r\n    }\r\n    \r\n    .expire-selector {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      \r\n      .expire-item {\r\n        flex: 1;\r\n        height: 80rpx;\r\n        background-color: #F8F8F8;\r\n        border-radius: 8rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        margin: 0 10rpx;\r\n        font-size: 26rpx;\r\n        color: #333;\r\n        \r\n        &:first-child {\r\n          margin-left: 0;\r\n        }\r\n        \r\n        &:last-child {\r\n          margin-right: 0;\r\n        }\r\n        \r\n        &.active {\r\n          background: linear-gradient(135deg, #FFE4E1 0%, #FFF0F5 100%);\r\n          border: 1px solid #FF6347;\r\n          color: #FF6347;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .action-area {\r\n    .balance-warning {\r\n      text-align: center;\r\n      margin-bottom: 20rpx;\r\n      \r\n      .warning-text {\r\n        font-size: 26rpx;\r\n        color: #FF3B30;\r\n      }\r\n    }\r\n    \r\n    .submit-btn {\r\n      width: 100%;\r\n      height: 90rpx;\r\n      line-height: 90rpx;\r\n      background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);\r\n      border-radius: 45rpx;\r\n      color: #fff;\r\n      font-size: 32rpx;\r\n      font-weight: 500;\r\n      \r\n      &[disabled] {\r\n        opacity: 0.6;\r\n        background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);\r\n        color: #fff;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style> ", "<template>\r\n  <view class=\"red-packet-selector\">\r\n    <!-- 选择按钮 -->\r\n    <view class=\"selector-btn\" @click=\"showPopup = true\">\r\n      <view class=\"btn-icon\">\r\n        <image src=\"/static/images/red-packet-icon-small.png\" mode=\"aspectFit\"></image>\r\n      </view>\r\n      <view class=\"btn-text\">\r\n        <text v-if=\"!selectedRedPacket\">添加红包</text>\r\n        <text v-else>已添加红包 {{ formatAmount(selectedRedPacket.totalAmount) }}元</text>\r\n      </view>\r\n      <view class=\"btn-arrow\">\r\n        <text class=\"arrow\">›</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 红包弹窗 -->\r\n    <view class=\"selector-popup\" v-if=\"showPopup\">\r\n      <view class=\"popup-mask\" @click=\"showPopup = false\"></view>\r\n      \r\n      <view class=\"popup-content\">\r\n        <view class=\"popup-header\">\r\n          <text class=\"title\">添加红包</text>\r\n          <view class=\"close-btn\" @click=\"showPopup = false\">\r\n            <text class=\"close-icon\">×</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"popup-body\">\r\n          <!-- 红包创建表单 -->\r\n          <red-packet-creator \r\n            :userBalance=\"userBalance\" \r\n            @create-success=\"handleCreateSuccess\"\r\n          />\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport RedPacketCreator from './RedPacketCreator.vue';\r\nimport { formatRedPacketAmount } from '@/utils/redPacket.js';\r\n\r\nexport default {\r\n  name: 'RedPacketSelector',\r\n  components: {\r\n    RedPacketCreator\r\n  },\r\n  props: {\r\n    // 用户余额（单位：分）\r\n    userBalance: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    // 已选择的红包\r\n    value: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      showPopup: false,\r\n      selectedRedPacket: null\r\n    };\r\n  },\r\n  watch: {\r\n    value: {\r\n      handler(val) {\r\n        this.selectedRedPacket = val;\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  methods: {\r\n    // 格式化金额\r\n    formatAmount(amount) {\r\n      return formatRedPacketAmount(amount);\r\n    },\r\n    \r\n    // 创建红包成功\r\n    handleCreateSuccess(redPacket) {\r\n      this.selectedRedPacket = redPacket;\r\n      this.$emit('input', redPacket);\r\n      this.showPopup = false;\r\n      \r\n      // 提示创建成功\r\n      uni.showToast({\r\n        title: '红包添加成功',\r\n        icon: 'success'\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.red-packet-selector {\r\n  width: 100%;\r\n  \r\n  .selector-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    background-color: #FFF;\r\n    border-radius: 8rpx;\r\n    padding: 20rpx 24rpx;\r\n    margin-bottom: 20rpx;\r\n    \r\n    .btn-icon {\r\n      width: 48rpx;\r\n      height: 48rpx;\r\n      margin-right: 20rpx;\r\n      \r\n      image {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n    \r\n    .btn-text {\r\n      flex: 1;\r\n      font-size: 28rpx;\r\n      color: #333;\r\n    }\r\n    \r\n    .btn-arrow {\r\n      width: 40rpx;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      \r\n      .arrow {\r\n        font-size: 36rpx;\r\n        color: #999;\r\n        transform: rotate(90deg);\r\n      }\r\n    }\r\n  }\r\n  \r\n  .selector-popup {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    z-index: 999;\r\n    display: flex;\r\n    flex-direction: column;\r\n    \r\n    .popup-mask {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      background-color: rgba(0, 0, 0, 0.5);\r\n    }\r\n    \r\n    .popup-content {\r\n      position: absolute;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      background-color: #F8F8F8;\r\n      border-radius: 24rpx 24rpx 0 0;\r\n      padding-bottom: env(safe-area-inset-bottom);\r\n      overflow: hidden;\r\n      max-height: 90vh;\r\n      display: flex;\r\n      flex-direction: column;\r\n      \r\n      .popup-header {\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        height: 100rpx;\r\n        position: relative;\r\n        background-color: #FFF;\r\n        border-bottom: 1px solid #F0F0F0;\r\n        \r\n        .title {\r\n          font-size: 32rpx;\r\n          font-weight: 500;\r\n          color: #333;\r\n        }\r\n        \r\n        .close-btn {\r\n          position: absolute;\r\n          right: 20rpx;\r\n          top: 0;\r\n          bottom: 0;\r\n          width: 80rpx;\r\n          display: flex;\r\n          justify-content: center;\r\n          align-items: center;\r\n          \r\n          .close-icon {\r\n            font-size: 40rpx;\r\n            color: #999;\r\n          }\r\n        }\r\n      }\r\n      \r\n      .popup-body {\r\n        padding: 30rpx;\r\n        overflow-y: auto;\r\n        flex: 1;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style> ", "/**\n * 红包组件模块\n */\nimport RedPacketCard from './RedPacketCard.vue';\nimport RedPacketPopup from './RedPacketPopup.vue';\nimport RedPacketCreator from './RedPacketCreator.vue';\nimport RedPacketSelector from './RedPacketSelector.vue';\n\n// 安装方法\nconst install = function(Vue) {\n  if (install.installed) return;\n  install.installed = true;\n  \n  // 注册组件\n  Vue.component(RedPacketCard.name, RedPacketCard);\n  Vue.component(RedPacketPopup.name, RedPacketPopup);\n  Vue.component(RedPacketCreator.name, RedPacketCreator);\n  Vue.component(RedPacketSelector.name, RedPacketSelector);\n};\n\n// 自动安装\nif (typeof window !== 'undefined' && window.Vue) {\n  install(window.Vue);\n}\n\nexport {\n  RedPacketCard,\n  RedPacketPopup,\n  RedPacketCreator,\n  RedPacketSelector\n};\n\nexport default {\n  install,\n}; "], "names": ["_sfc_main", "formatAmount", "RED_PACKET_TYPE", "showShareMenu", "uni", "grabRedPacket", "formatRedPacketAmount", "formatDate", "createRedPacket", "RedPacketCreator"], "mappings": ";;;;;;;AAkEA,MAAKA,cAAU;AAAA,EACb,MAAM;AAAA,EAEN,OAAO;AAAA,IACL,WAAW;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,EACD;AAAA,EAED,OAAO;AACL,WAAO;AAAA,MACL,eAAe;AAAA;EAElB;AAAA,EAED,UAAU;AAAA;AAAA,IAER,YAAY;AACV,aAAO,KAAK,IAAG,IAAK,KAAK,UAAU;AAAA,IACrC;AAAA,EACD;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,cAAc;AACZ,WAAK,MAAM,QAAQ,KAAK,SAAS;AAAA,IAClC;AAAA;AAAA,IAGD,aAAa;AACX,UAAI,KAAK;AAAW;AAGpB,WAAK,gBAAgB;AACrB,iBAAW,MAAM;AACf,aAAK,gBAAgB;AACrB,aAAK,MAAM,QAAQ,KAAK,SAAS;AAAA,MAClC,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,kBAGDC,aAAY;AAAA;AAAA,IAGZ,iBAAiB,YAAY;AAC3B,YAAM,MAAM,KAAK;AACjB,YAAM,SAAS,aAAa;AAE5B,UAAI,UAAU;AAAG,eAAO;AAExB,YAAM,QAAQ,KAAK,MAAM,UAAU,KAAK,KAAK,IAAK;AAClD,YAAM,UAAU,KAAK,MAAO,UAAU,KAAK,KAAK,QAAU,KAAK,IAAK;AAEpE,UAAI,QAAQ,GAAG;AACb,eAAO,KAAK,KAAK,KAAK,OAAO;AAAA,aACxB;AACL,eAAO,KAAK,OAAO;AAAA,MACrB;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,MAAM;AAChB,YAAM,UAAU;AAAA,QACd,CAACC,gBAAAA,gBAAgB,MAAM,GAAG;AAAA,QAC1B,CAACA,gBAAAA,gBAAgB,KAAK,GAAG;AAAA;AAE3B,aAAO,QAAQ,IAAI,KAAK;AAAA,IACzB;AAAA,IAED,MAAM,cAAc;AAClB,UAAI;AACF,cAAMC,0BAAc;AAAA,UAClB,MAAM;AAAA,UACN,MAAM,KAAK;AAAA,QACb,CAAC;AACDC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACD,SAAO,OAAO;AACdA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACfA,MAAKJ,cAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA;AAAA,IAEL,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA;AAAA,IAED,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS,OAAO,CAAA;AAAA,IACjB;AAAA;AAAA,IAED,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAM,CAAC;AAAA,IACjB;AAAA;AAAA,IAED,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA;AAAA,IACX;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,aAAa;AAAA,MACb,YAAY;AAAA,QACV,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA;EAEH;AAAA,EACD,OAAO;AAAA,IACL,KAAK,KAAK;AACR,UAAI,KAAK;AACP,aAAK,cAAc,KAAK;AAAA,MAC1B;AAAA,IACD;AAAA,IACD,YAAY,KAAK;AACf,UAAI,KAAK,MAAM;AACb,aAAK,cAAc;AAAA,MACrB;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,cAAc;AACZ,WAAK,MAAM,OAAO;AAAA,IACnB;AAAA;AAAA,IAGD,gBAAgB;AACd,WAAK,cAAc;AAGnBK,sBAAa,cAAC,KAAK,UAAU,EAAE,EAAE,KAAK,SAAO;AAC3C,mBAAW,MAAM;AACf,eAAK,aAAa;AAAA,YAChB,QAAQ;AAAA,YACR,QAAQ,IAAI;AAAA,YACZ,SAAS;AAAA;AAEX,eAAK,cAAc;AACnB,eAAK,MAAM,gBAAgB,GAAG;AAAA,QAC/B,GAAE,IAAI;AAAA,MACT,CAAC,EAAE,MAAM,SAAO;AACd,mBAAW,MAAM;AACf,eAAK,aAAa;AAAA,YAChB,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,SAAS,IAAI,WAAW;AAAA;AAE1B,eAAK,cAAc;AAAA,QACpB,GAAE,IAAI;AAAA,MACT,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,aAAa,QAAQ;AACnB,aAAOC,gBAAAA,sBAAsB,MAAM;AAAA,IACpC;AAAA;AAAA,IAGD,WAAW,WAAW;AACpB,aAAOC,WAAU,WAAC,WAAW,aAAa;AAAA,IAC3C;AAAA;AAAA,IAGD,aAAa;AACXH,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AACD,WAAK,YAAW;AAAA,IAClB;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClHA,MAAKJ,cAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA;AAAA,IAEL,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA;AAAA,MAEL,UAAU;AAAA,QACR,MAAME,gBAAe,gBAAC;AAAA;AAAA,QACtB,QAAQ;AAAA;AAAA,QACR,OAAO;AAAA;AAAA,QACP,UAAU;AAAA;AAAA,QACV,aAAa;AAAA;AAAA,MACd;AAAA;AAAA,MAED,aAAa;AAAA,QACX;AAAA,UACE,MAAM;AAAA,UACN,OAAOA,gBAAe,gBAAC;AAAA,UACvB,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,OAAOA,gBAAe,gBAAC;AAAA,UACvB,MAAM;AAAA,QACR;AAAA,MACD;AAAA;AAAA,MAED,eAAe;AAAA,QACb,EAAE,OAAO,QAAQ,OAAO,GAAI;AAAA,QAC5B,EAAE,OAAO,QAAQ,OAAO,GAAI;AAAA,QAC5B,EAAE,OAAO,QAAQ,OAAO,GAAG;AAAA,MAC5B;AAAA,MACD,SAAS;AAAA;AAAA;EAEZ;AAAA,EACD,UAAU;AAAA;AAAA,IAER,qBAAqB;AACnB,YAAM,SAAS,WAAW,KAAK,SAAS,UAAU,CAAC;AACnD,aAAO,KAAK,MAAM,SAAS,GAAG;AAAA,IAC/B;AAAA;AAAA,IAGD,qBAAqB;AACnB,UAAI,CAAC,KAAK,SAAS,SAAS,KAAK,SAAS,SAAS;AAAG,eAAO;AAE7D,aAAO,KAAK;AAAA,IACb;AAAA;AAAA,IAGD,uBAAuB;AACrB,UAAI,CAAC,KAAK,SAAS,SAAS,KAAK,SAAS,SAAS;AAAG,eAAO;AAE7D,aAAO,KAAK,MAAM,KAAK,qBAAqB,KAAK,SAAS,KAAK;AAAA,IAChE;AAAA;AAAA,IAGD,sBAAsB;AACpB,aAAO,KAAK,qBAAqB,KAAK;AAAA,IACvC;AAAA;AAAA,IAGD,YAAY;AACV,aACE,KAAK,qBAAqB,KAC1B,KAAK,SAAS,QAAQ,KACtB,CAAC,KAAK;AAAA,IAEV;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,aAAa,QAAQ;AACnB,aAAOI,gBAAAA,sBAAsB,MAAM;AAAA,IACpC;AAAA;AAAA,IAGD,iBAAiB;AACf,UAAI,KAAK,SAAS,WAAW;AAAI;AAEjC,UAAI,SAAS,WAAW,KAAK,SAAS,MAAM;AAG5C,UAAI,SAAS,MAAM;AACjB,iBAAS;AAAA,MACX;AAGA,YAAM,YAAY,KAAK,aAAa,KAAK,WAAW;AACpD,UAAI,SAAS,WAAW,SAAS,GAAG;AAClC,iBAAS,WAAW,SAAS;AAAA,MAC/B;AAGA,WAAK,SAAS,SAAS,OAAO,QAAQ,CAAC;AAAA,IACxC;AAAA;AAAA,IAGD,gBAAgB;AACd,UAAI,CAAC,KAAK,SAAS,OAAO;AACxB,aAAK,SAAS,QAAQ;AACtB;AAAA,MACF;AAEA,UAAI,QAAQ,SAAS,KAAK,SAAS,KAAK;AAGxC,UAAI,QAAQ,GAAG;AACb,gBAAQ;AAAA,MACV;AAGA,UAAI,QAAQ,KAAK;AACf,gBAAQ;AAAA,MACV;AAEA,WAAK,SAAS,QAAQ;AAAA,IACvB;AAAA;AAAA,IAGD,gBAAgB;AACd,UAAI,KAAK,SAAS,QAAQ,GAAG;AAC3B,aAAK,SAAS;AAAA,MAChB;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB;AACd,UAAI,KAAK,SAAS,QAAQ,KAAK;AAC7B,aAAK,SAAS;AAAA,MAChB;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACb,UAAI,CAAC,KAAK,aAAa,KAAK;AAAS;AAErC,WAAK,UAAU;AAGf,YAAM,SAAS;AAAA,QACb,MAAM,KAAK,SAAS;AAAA,QACpB,aAAa,KAAK;AAAA,QAClB,YAAY,KAAK,SAAS;AAAA,QAC1B,OAAO,KAAK,SAAS;AAAA,QACrB,YAAY,KAAK,QAAQ,KAAK,SAAS,cAAc,KAAK,KAAK;AAAA;AAIjEE,sBAAAA,gBAAgB,MAAM,EACnB,KAAK,SAAO;AAEX,aAAK,MAAM,kBAAkB,GAAG;AAChC,aAAK,UAAS;AAAA,OACf,EACA,MAAM,SAAO;AAEZJ,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,IAAI,WAAW;AAAA,UACtB,MAAM;AAAA,QACR,CAAC;AAAA,OACF,EACA,QAAQ,MAAM;AACb,aAAK,UAAU;AAAA,MACjB,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,YAAY;AACV,WAAK,WAAW;AAAA,QACd,MAAMF,gBAAe,gBAAC;AAAA,QACtB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,aAAa;AAAA;IAEjB;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzQA,yBAAyB,MAAW;AAGpC,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AAAA;AAAA,IAEL,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA;AAAA,IAED,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,mBAAmB;AAAA;EAEtB;AAAA,EACD,OAAO;AAAA,IACL,OAAO;AAAA,MACL,QAAQ,KAAK;AACX,aAAK,oBAAoB;AAAA,MAC1B;AAAA,MACD,WAAW;AAAA,IACb;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,aAAa,QAAQ;AACnB,aAAOI,gBAAAA,sBAAsB,MAAM;AAAA,IACpC;AAAA;AAAA,IAGD,oBAAoB,WAAW;AAC7B,WAAK,oBAAoB;AACzB,WAAK,MAAM,SAAS,SAAS;AAC7B,WAAK,YAAY;AAGjBF,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;ACrFA,MAAM,UAAU,SAAS,KAAK;AAC5B,MAAI,QAAQ;AAAW;AACvB,UAAQ,YAAY;AAGpB,MAAI,UAAU,cAAc,MAAM,aAAa;AAC/C,MAAI,UAAU,eAAe,MAAM,cAAc;AACjD,MAAI,UAAUK,UAAiB,MAAMA,SAAgB;AACrD,MAAI,UAAU,kBAAkB,MAAM,iBAAiB;AACzD;AAGA,IAAI,OAAO,WAAW,eAAe,OAAO,KAAK;AAC/C,UAAQ,OAAO,GAAG;AACpB;AASA,MAAe,sBAAA;AAAA,EACb;AACF;;;"}