<template>
  <view class="service-detail-container">
    <!-- 隐藏的分享按钮，用于自动触发 -->
    <button id="shareButton" class="hidden-share-btn" open-type="share"></button>
    
    <view class="service-detail-wrapper">
      <!-- 服务基本信息卡片 -->
      <view class="content-card service-info-card">
        <view class="service-header">
          <view class="service-title-row">
            <text class="service-title">{{serviceData.title}}</text>
            <text class="service-price">{{serviceData.price}}</text>
          </view>
          <view class="service-meta">
            <view class="service-tag-group">
              <view class="service-tag" v-for="(tag, index) in serviceData.tags" :key="index">{{tag}}</view>
            </view>
            <text class="service-publish-time">发布于 {{formatTime(serviceData.publishTime)}}</text>
          </view>
        </view>
        
        <!-- 服务图片轮播 -->
        <swiper class="service-swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500">
          <swiper-item v-for="(image, index) in serviceData.images" :key="index">
            <image :src="image" mode="aspectFill" class="service-image"></image>
          </swiper-item>
        </swiper>
        
        <!-- 基本信息 -->
        <view class="service-basic-info">
          <view class="info-item">
            <text class="info-label">服务类型</text>
            <text class="info-value">{{serviceData.type}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">服务范围</text>
            <text class="info-value">{{serviceData.area}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">服务时间</text>
            <text class="info-value">{{serviceData.time}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">服务方式</text>
            <text class="info-value">{{serviceData.method}}</text>
          </view>
        </view>
      </view>
      
      <!-- 服务内容 -->
      <view class="content-card service-content-card">
        <view class="section-title">服务内容</view>
        <view class="content-list">
          <view class="content-item" v-for="(item, index) in serviceData.contents" :key="index">
            <text class="content-label">{{item.label}}</text>
            <text class="content-value">{{item.value}}</text>
          </view>
        </view>
      </view>
      
      <!-- 服务流程 -->
      <view class="content-card service-process-card">
        <view class="section-title">服务流程</view>
        <view class="process-list">
          <view class="process-item" v-for="(item, index) in serviceData.process" :key="index">
            <view class="process-step">{{index + 1}}</view>
            <view class="process-info">
              <text class="process-title">{{item.title}}</text>
              <text class="process-desc">{{item.description}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 服务保障 -->
      <view class="content-card service-guarantee-card">
        <view class="section-title">服务保障</view>
        <view class="guarantee-list">
          <view class="guarantee-item" v-for="(item, index) in serviceData.guarantees" :key="index">
            <text class="guarantee-icon iconfont" :class="item.icon"></text>
            <view class="guarantee-info">
              <text class="guarantee-title">{{item.title}}</text>
              <text class="guarantee-desc">{{item.description}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 服务商信息 -->
      <view class="content-card provider-card">
        <view class="provider-header">
          <view class="provider-avatar">
            <image :src="serviceData.provider.avatar" mode="aspectFill"></image>
          </view>
          <view class="provider-info">
            <text class="provider-name">{{serviceData.provider.name}}</text>
            <view class="provider-meta">
              <text class="provider-type">{{serviceData.provider.type}}</text>
              <text class="provider-rating">信用等级 {{serviceData.provider.rating}}</text>
            </view>
          </view>
          <view class="provider-auth" v-if="serviceData.provider.isVerified">
            <text class="iconfont icon-verified"></text>
            <text class="auth-text">已认证</text>
          </view>
        </view>
      </view>
      
      <!-- 联系方式 -->
      <view class="content-card contact-card">
        <view class="contact-header">
          <text class="card-title">联系方式</text>
        </view>
        <view class="contact-content">
          <view class="contact-item">
            <text class="contact-label">联系人</text>
            <text class="contact-value">{{serviceData.contact.name}}</text>
          </view>
          <view class="contact-item">
            <text class="contact-label">电话</text>
            <text class="contact-value contact-phone" @click="callPhone">{{serviceData.contact.phone}}</text>
          </view>
          <view class="contact-tips">
            <text class="tips-icon iconfont icon-info"></text>
            <text class="tips-text">请说明在"磁州生活网"看到的信息</text>
          </view>
        </view>
      </view>
      
      <!-- 相关服务推荐 -->
      <view class="content-card related-services-card">
        <view class="section-title">相关服务推荐</view>
        <view class="related-services-content">
          <!-- 简洁的服务列表 -->
          <view class="related-services-list">
            <view class="related-service-item" 
                 v-for="(service, index) in relatedServices.slice(0, 3)" 
                 :key="index" 
                 @click="navigateToServiceDetail(service.id)">
              <view class="service-item-content">
                <view class="service-item-left">
                  <image class="provider-logo" :src="service.providerLogo" mode="aspectFill"></image>
                </view>
                <view class="service-item-middle">
                  <text class="service-item-title">{{service.title}}</text>
                  <view class="service-item-provider">{{service.providerName}}</view>
                  <view class="service-item-tags">
                    <text class="service-item-tag" v-for="(tag, tagIndex) in service.tags.slice(0, 2)" :key="tagIndex">{{tag}}</text>
                    <text class="service-item-tag-more" v-if="service.tags.length > 2">+{{service.tags.length - 2}}</text>
                  </view>
                </view>
                <view class="service-item-right">
                  <text class="service-item-price">{{service.price}}</text>
                </view>
              </view>
            </view>
            
            <!-- 暂无数据提示 -->
            <view class="empty-related-services" v-if="relatedServices.length === 0">
              <image src="/static/images/empty.png" class="empty-image" mode="aspectFit"></image>
              <text class="empty-text">暂无相关服务</text>
            </view>
          </view>
          
          <!-- 查看更多按钮 -->
          <view class="view-more-btn" v-if="relatedServices.length > 0" @click.stop="navigateToServiceList">
            <text class="view-more-text">查看更多服务信息</text>
            <text class="view-more-icon iconfont icon-right"></text>
          </view>
        </view>
      </view>
      
      <!-- 举报卡片 -->
      <report-card></report-card>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="interaction-toolbar">
      <view class="toolbar-item" @click="goToHome">
        <image src="/static/images/tabbar/a首页.png" class="toolbar-icon"></image>
        <text class="toolbar-text">首页</text>
      </view>
      <view class="toolbar-item" @click="toggleCollect">
        <image src="/static/images/tabbar/a收藏.png" class="toolbar-icon"></image>
        <text class="toolbar-text">收藏</text>
      </view>
      <button class="share-button toolbar-item" open-type="share">
        <image src="/static/images/tabbar/a分享.png" class="toolbar-icon"></image>
        <text class="toolbar-text">分享</text>
      </button>
      <view class="toolbar-item" @click="openChat">
        <image src="/static/images/tabbar/a消息.png" class="toolbar-icon"></image>
        <text class="toolbar-text">私信</text>
      </view>
      <view class="toolbar-item call-button" @click="callPhone">
        <view class="call-button-content">
          <text class="call-text">打电话</text>
          <text class="call-subtitle">请说在磁州生活网看到的</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  return `${date.getMonth() + 1}月${date.getDate()}日`;
};

// 响应式数据
const isCollected = ref(false);

// 相关服务列表
const relatedServices = ref([]);

// 加载相关服务信息
const loadRelatedServices = () => {
  // 这里可以调用API获取数据
  // 实际项目中应该根据当前服务的分类、标签等进行相关性匹配
  
  // 模拟数据
  setTimeout(() => {
    relatedServices.value = [
      {
        id: 'service001',
        title: '专业汽车钣金喷漆',
        price: '300元起',
        providerName: '磁县汽修中心',
        providerLogo: '/static/images/tabbar/汽车维修.png',
        tags: ['原厂漆料', '工期短', '修复如新']
      },
      {
        id: 'service002',
        title: '汽车轮胎更换',
        price: '400元/套起',
        providerName: '安途汽车服务',
        providerLogo: '/static/images/tabbar/轮胎.png',
        tags: ['正品轮胎', '免费安装', '送气门嘴']
      },
      {
        id: 'service003',
        title: '汽车空调清洗',
        price: '120元起',
        providerName: '车享家汽修',
        providerLogo: '/static/images/tabbar/空调.png',
        tags: ['除臭除菌', '上门服务', '养护保养']
      }
    ];
  }, 500);
};

// 跳转到服务详情页
const navigateToServiceDetail = (id) => {
  // 避免重复跳转当前页面
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};
  
  if (id === options.id) {
    return;
  }
  
  uni.navigateTo({
    url: `/pages/publish/vehicle-service-detail?id=${id}`
  });
};

// 跳转到车辆服务列表页
const navigateToServiceList = (e) => {
  if (e) e.stopPropagation();
  // 获取服务类型作为分类参数
  const serviceCategory = serviceData.value.tags?.[0] || '';
  uni.navigateTo({ 
    url: `/subPackages/service/pages/filter?type=car&title=${encodeURIComponent('车辆服务')}&category=${encodeURIComponent(serviceCategory)}&active=car` 
  });
};

const serviceData = ref({
  id: 'service12345',
  title: '专业汽车维修保养',
  price: '200元起',
  tags: ['专业维修', '上门服务', '质保一年'],
  publishTime: Date.now() - 86400000 * 2, // 2天前
  images: [
    '/static/images/service1.jpg',
    '/static/images/service2.jpg',
    '/static/images/service3.jpg'
  ],
  type: '汽车维修',
  area: '磁县全城',
  time: '8:00-20:00',
  method: '上门服务',
  contents: [
    { label: '维修项目', value: '发动机维修、变速箱维修、电路维修等' },
    { label: '保养项目', value: '机油更换、滤芯更换、轮胎保养等' },
    { label: '服务承诺', value: '专业技师、原厂配件、质保一年' },
    { label: '服务说明', value: '提供免费检测、上门服务、24小时救援' }
  ],
  process: [
    {
      title: '预约服务',
      description: '电话或在线预约，确定服务时间和地点'
    },
    {
      title: '上门检测',
      description: '专业技师上门进行车辆检测'
    },
    {
      title: '维修保养',
      description: '使用原厂配件进行维修保养'
    },
    {
      title: '质量保证',
      description: '提供一年质保，确保服务质量'
    }
  ],
  guarantees: [
    {
      icon: 'icon-quality',
      title: '质量保证',
      description: '使用原厂配件，质保一年'
    },
    {
      icon: 'icon-professional',
      title: '专业技师',
      description: '10年以上维修经验'
    },
    {
      icon: 'icon-service',
      title: '服务保障',
      description: '24小时救援服务'
    }
  ],
  provider: {
    name: '张师傅汽修',
    avatar: '/static/images/avatar.png',
    type: '商家',
    rating: 'A+',
    isVerified: true
  },
  contact: {
    name: '张师傅',
    phone: '13912345678'
  }
});

// 方法
const toggleCollect = () => {
  isCollected.value = !isCollected.value;
  if (isCollected.value) {
    uni.showToast({
      title: '收藏成功',
      icon: 'success'
    });
  }
};

const showShareOptions = () => {
  uni.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage', 'shareTimeline']
  });
};

const callPhone = () => {
  uni.makePhoneCall({
    phoneNumber: serviceData.value.contact.phone,
    fail: () => {
      uni.showToast({
        title: '拨打电话失败',
        icon: 'none'
      });
    }
  });
};

// 跳转到首页
const goToHome = () => {
  uni.switchTab({
    url: '/pages/index/index'
  });
};

// 打开私信聊天
const openChat = () => {
  if (!serviceData.value.provider || !serviceData.value.provider.id) {
    uni.showToast({
      title: '无法获取服务商信息',
      icon: 'none'
    });
    return;
  }
  
  // 跳转到聊天页面
  uni.navigateTo({
    url: `/pages/chat/index?userId=${serviceData.value.provider.id}&username=${encodeURIComponent(serviceData.value.provider.name || '服务商')}`
  });
};

// 生命周期钩子
onMounted(() => {
  // 修改页面标题
  uni.setNavigationBarTitle({
    title: '车辆服务详情'
  });
  
  // 加载相关服务推荐
  loadRelatedServices();
});
</script>

<style>
.service-detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 110rpx;
  padding-top: 150rpx; /* 增加顶部内边距，为固定导航栏留出空间 */
}

.service-detail-wrapper {
  padding: 24rpx;
  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */
}

.content-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 服务基本信息卡片 */
.service-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.service-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.service-price {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff4d4f;
}

.service-meta {
  margin-bottom: 24rpx;
}

.service-tag-group {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12rpx;
}

.service-tag {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
  margin-bottom: 12rpx;
}

.service-publish-time {
  font-size: 24rpx;
  color: #999;
}

/* 轮播图 */
.service-swiper {
  height: 400rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
}

.service-image {
  width: 100%;
  height: 100%;
}

/* 基本信息 */
.service-basic-info {
  display: flex;
  flex-wrap: wrap;
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}

.info-item {
  width: 50%;
  padding: 12rpx 24rpx;
  box-sizing: border-box;
}

.info-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 服务内容 */
.content-list {
  display: flex;
  flex-direction: column;
}

.content-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.content-item:last-child {
  border-bottom: none;
}

.content-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}

.content-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 服务流程 */
.process-list {
  display: flex;
  flex-direction: column;
}

.process-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.process-item:last-child {
  border-bottom: none;
}

.process-step {
  width: 40rpx;
  height: 40rpx;
  background-color: #1890ff;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.process-info {
  flex: 1;
}

.process-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.process-desc {
  font-size: 26rpx;
  color: #666;
}

/* 服务保障 */
.guarantee-list {
  display: flex;
  flex-direction: column;
}

.guarantee-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.guarantee-item:last-child {
  border-bottom: none;
}

.guarantee-icon {
  font-size: 40rpx;
  color: #1890ff;
  margin-right: 20rpx;
}

.guarantee-info {
  flex: 1;
}

.guarantee-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.guarantee-desc {
  font-size: 26rpx;
  color: #666;
}

/* 服务商信息 */
.provider-header {
  display: flex;
  align-items: center;
}

.provider-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
}

.provider-avatar image {
  width: 100%;
  height: 100%;
}

.provider-info {
  flex: 1;
}

.provider-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.provider-meta {
  display: flex;
  align-items: center;
}

.provider-type, .provider-rating {
  font-size: 24rpx;
  color: #666;
  margin-right: 16rpx;
}

/* 底部互动工具栏 */
.interaction-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10rpx 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  height: 120rpx;
  z-index: 100;
}

.toolbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6rpx 0;
  margin: 0 4rpx;
}

.toolbar-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}

.toolbar-text {
  font-size: 22rpx;
  color: #666;
}

.share-button {
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
  line-height: normal;
  border-radius: 0;
  flex: 1;
}

.share-button::after {
  display: none;
}

.call-button {
  flex: 3; /* 占据更多空间，原来是2，现在改为3让按钮更长 */
  background: linear-gradient(135deg, #0052CC, #0066FF);
  height: 90rpx;
  margin: 0 0 0 10rpx;
  border-radius: 45rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
}

.call-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.call-text {
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  line-height: 1.2;
}

.call-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 20rpx;
  line-height: 1.2;
}

/* 隐藏原来的底部操作栏 */
.action-bar {
  display: none;
}

/* 隐藏的分享按钮 */
.hidden-share-btn {
  position: absolute;
  top: -9999rpx;
  left: -9999rpx;
  width: 0;
  height: 0;
  padding: 0;
  margin: 0;
  opacity: 0;
}

.tips-text {
  font-size: 24rpx;
  color: #ff9800;
}

/* 相关服务推荐样式 */
.related-services-card {
  margin-top: 12px;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.related-services-content {
  padding: 0 16px 16px;
  overflow: hidden;
}

/* 相关服务列表样式 */
.related-services-list {
  margin-bottom: 12px;
}

.related-service-item {
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.related-service-item:last-child {
  border-bottom: none;
}

.service-item-content {
  display: flex;
  align-items: center;
}

.service-item-left {
  margin-right: 12px;
}

.provider-logo {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #f5f7fa;
}

.service-item-middle {
  flex: 1;
  overflow: hidden;
}

.service-item-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.service-item-provider {
  font-size: 13px;
  color: #666;
  margin-bottom: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.service-item-tags {
  display: flex;
  flex-wrap: wrap;
}

.service-item-tag {
  font-size: 11px;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  margin-right: 6px;
}

.service-item-tag-more {
  font-size: 11px;
  color: #999;
}

.service-item-right {
  min-width: 80px;
  text-align: right;
}

.service-item-price {
  font-size: 15px;
  font-weight: 500;
  color: #ff5252;
}

/* 查看更多按钮样式 */
.view-more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  background-color: #f7f9fc;
  border-radius: 8px;
  margin-top: 8px;
}

.view-more-text {
  font-size: 14px;
  color: #1890ff;
}

.view-more-icon {
  margin-left: 4px;
  font-size: 12px;
  color: #1890ff;
}

/* 空数据提示样式 */
.empty-related-services {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 0;
}

.empty-image {
  width: 80px;
  height: 80px;
  margin-bottom: 12px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 隐藏的分享按钮 */
.hidden-share-btn {
  position: fixed;
  width: 2rpx;
  height: 2rpx;
  opacity: 0;
  top: -999rpx;
  left: -999rpx;
  z-index: -1;
  overflow: hidden;
  padding: 0;
  margin: 0;
  border: none;
}

.hidden-share-btn::after {
  display: none;
}

.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}
</style> 