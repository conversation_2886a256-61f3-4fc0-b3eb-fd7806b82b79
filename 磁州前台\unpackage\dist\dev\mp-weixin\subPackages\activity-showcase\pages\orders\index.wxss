
.orders-container.data-v-1e56167c {
  min-height: 100vh;
  background-color: #F5F5F5;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.custom-navbar.data-v-1e56167c {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
}
.navbar-bg.data-v-1e56167c {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #5AC8FA 0%, #90E0FF 100%);
}
.navbar-content.data-v-1e56167c {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 103rpx; /* 原来是98rpx，增加5rpx */
  padding: calc(var(--status-bar-height) + 8rpx) 30rpx 0; /* 向下移动8rpx */
}
.back-btn.data-v-1e56167c, .search-btn.data-v-1e56167c {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.navbar-title.data-v-1e56167c {
  font-size: 36rpx;
  font-weight: bold;
  color: #FFFFFF;
}
.navbar-right.data-v-1e56167c {
  display: flex;
  align-items: center;
}

/* 标签栏样式 */
.order-tabs.data-v-1e56167c {
  display: flex;
  background: #FFFFFF;
  padding: 0 20rpx;
  margin-top: calc(var(--status-bar-height) + 111rpx); /* 原来是106rpx，增加5rpx */
  border-bottom: 1rpx solid #EEEEEE;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}
.tab-item.data-v-1e56167c {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  position: relative;
}
.tab-text.data-v-1e56167c {
  font-size: 28rpx;
  color: #333333;
  padding: 0 10rpx;
}
.tab-item.active .tab-text.data-v-1e56167c {
  color: #5AC8FA;
  font-weight: 500;
}
.tab-indicator.data-v-1e56167c {
  position: absolute;
  bottom: 0;
  width: 40rpx;
  height: 6rpx;
  border-radius: 3rpx;
}

/* 订单列表样式 */
.orders-swiper.data-v-1e56167c {
  height: calc(100vh - var(--status-bar-height) - 111rpx - 70rpx); /* 原来是106rpx，增加5rpx */
}
.tab-content.data-v-1e56167c {
  height: 100%;
  padding: 20rpx;
}
.order-list.data-v-1e56167c {
  padding-bottom: 30rpx;
}
.order-card.data-v-1e56167c {
  background: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}
.order-header.data-v-1e56167c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #F5F5F5;
}
.shop-info.data-v-1e56167c {
  display: flex;
  align-items: center;
}
.shop-icon.data-v-1e56167c {
  margin-right: 10rpx;
}
.shop-name.data-v-1e56167c {
  font-size: 28rpx;
  color: #333333;
  margin-right: 10rpx;
}
.arrow-icon.data-v-1e56167c {
  margin-left: 10rpx;
}
.order-status.data-v-1e56167c {
  font-size: 26rpx;
  font-weight: 500;
}
.order-content.data-v-1e56167c {
  padding: 20rpx 30rpx;
}
.order-item.data-v-1e56167c {
  display: flex;
  margin-bottom: 20rpx;
}
.order-item.data-v-1e56167c:last-child {
  margin-bottom: 0;
}
.item-image.data-v-1e56167c {
  width: 140rpx;
  height: 140rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
}
.item-info.data-v-1e56167c {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.item-name.data-v-1e56167c {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.item-spec.data-v-1e56167c {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 10rpx;
}
.item-price-qty.data-v-1e56167c {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.item-price.data-v-1e56167c {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}
.item-qty.data-v-1e56167c {
  font-size: 24rpx;
  color: #999999;
}
.order-footer.data-v-1e56167c {
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #F5F5F5;
}
.order-total.data-v-1e56167c {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 20rpx;
}
.total-text.data-v-1e56167c {
  font-size: 24rpx;
  color: #999999;
  margin-right: 10rpx;
}
.total-price.data-v-1e56167c {
  font-size: 28rpx;
  color: #333333;
  font-weight: bold;
}
.order-actions.data-v-1e56167c {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.action-btn.data-v-1e56167c {
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  margin-left: 20rpx;
}
.action-btn.cancel.data-v-1e56167c {
  border: 1rpx solid #DDDDDD;
  color: #666666;
}
.action-btn.primary.data-v-1e56167c {
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
}

/* 空状态样式 */
.empty-state.data-v-1e56167c {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-image.data-v-1e56167c {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-text.data-v-1e56167c {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 30rpx;
}
.empty-state .action-btn.data-v-1e56167c {
  padding: 15rpx 60rpx;
  font-size: 28rpx;
  color: #FFFFFF;
}
