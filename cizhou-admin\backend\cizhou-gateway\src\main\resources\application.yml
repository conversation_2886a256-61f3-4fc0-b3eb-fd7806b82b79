server:
  port: 8080

spring:
  application:
    name: cizhou-gateway
  
  profiles:
    active: dev
  
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: cizhou-admin
        group: DEFAULT_GROUP
      config:
        server-addr: localhost:8848
        namespace: cizhou-admin
        group: DEFAULT_GROUP
        file-extension: yml
        shared-configs:
          - data-id: common.yml
            group: DEFAULT_GROUP
            refresh: true
    
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      
      routes:
        # 认证服务
        - id: cizhou-auth
          uri: lb://cizhou-auth
          predicates:
            - Path=/auth/**
          filters:
            - StripPrefix=1
        
        # 用户服务
        - id: cizhou-user-service
          uri: lb://cizhou-user-service
          predicates:
            - Path=/user/**
          filters:
            - StripPrefix=1
            - name: AuthFilter
        
        # 商家服务
        - id: cizhou-merchant-service
          uri: lb://cizhou-merchant-service
          predicates:
            - Path=/merchant/**
          filters:
            - StripPrefix=1
            - name: AuthFilter
        
        # 内容服务
        - id: cizhou-content-service
          uri: lb://cizhou-content-service
          predicates:
            - Path=/content/**
          filters:
            - StripPrefix=1
            - name: AuthFilter
        
        # 订单服务
        - id: cizhou-order-service
          uri: lb://cizhou-order-service
          predicates:
            - Path=/order/**
          filters:
            - StripPrefix=1
            - name: AuthFilter
        
        # 营销服务
        - id: cizhou-marketing-service
          uri: lb://cizhou-marketing-service
          predicates:
            - Path=/marketing/**
          filters:
            - StripPrefix=1
            - name: AuthFilter
        
        # 数据服务
        - id: cizhou-data-service
          uri: lb://cizhou-data-service
          predicates:
            - Path=/data/**
          filters:
            - StripPrefix=1
            - name: AuthFilter
        
        # 通知服务
        - id: cizhou-notification-service
          uri: lb://cizhou-notification-service
          predicates:
            - Path=/notification/**
          filters:
            - StripPrefix=1
            - name: AuthFilter
      
      globalcors:
        cors-configurations:
          '[/**]':
            allowed-origins: "*"
            allowed-methods: "*"
            allowed-headers: "*"
            allow-credentials: true
            max-age: 3600
  
  redis:
    host: localhost
    port: 6379
    password: cizhou123456
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms

# JWT配置
jwt:
  secret: cizhou-admin-secret-key-2024-very-long-secret
  expiration: 86400000
  refresh-expiration: 604800000
  issuer: cizhou-admin

# 网关配置
gateway:
  # 白名单路径（不需要认证）
  whitelist:
    - /auth/login
    - /auth/logout
    - /auth/captcha
    - /auth/refresh-token
    - /actuator/**
    - /doc.html
    - /swagger-ui/**
    - /v3/api-docs/**
    - /webjars/**
  
  # 限流配置
  rate-limit:
    enabled: true
    default-capacity: 100
    default-tokens: 10
    default-seconds: 1

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always

# 日志配置
logging:
  level:
    com.cizhou.gateway: DEBUG
    org.springframework.cloud.gateway: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{50} - %msg%n"
