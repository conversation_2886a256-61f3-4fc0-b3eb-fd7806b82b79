{"version": 3, "file": "expired-list.js", "sources": ["carpool-package/pages/carpool/my/expired-list.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/Y2FycG9vbC1wYWNrYWdlXHBhZ2VzXGNhcnBvb2xcbXlcZXhwaXJlZC1saXN0LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"expired-list-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\">\r\n      <view class=\"navbar-content\">\r\n        <view class=\"navbar-left\" @click=\"goBack\">\r\n          <image src=\"/static/images/icons/back-white.png\" mode=\"aspectFit\" class=\"back-icon\"></image>\r\n        </view>\r\n        <view class=\"navbar-title\">已过期</view>\r\n        <view class=\"navbar-right\">\r\n          <text class=\"clear-text\" @click=\"showClearConfirm\">清空</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 内容区域 -->\r\n    <scroll-view class=\"scrollable-content\" scroll-y @scrolltolower=\"loadMore\" refresher-enabled @refresherrefresh=\"onRefresh\" :refresher-triggered=\"isRefreshing\">\r\n      <!-- 过期内容列表 -->\r\n      <view class=\"card-list\">\r\n        <view class=\"card-item\" v-for=\"(item, index) in expiredList\" :key=\"item.id\">\r\n          <!-- 卡片头部 -->\r\n          <view class=\"card-header\">\r\n            <view class=\"header-left\">\r\n              <text class=\"card-type\">{{item.type}}</text>\r\n              <text class=\"publish-time\">{{item.publishTime}}</text>\r\n            </view>\r\n            <view class=\"header-right\">\r\n              <text class=\"status-tag status-expired\">已过期</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 卡片内容 -->\r\n          <view class=\"card-content\">\r\n            <view class=\"route-info\">\r\n              <view class=\"route-points\">\r\n                <view class=\"start-point\">\r\n                  <view class=\"point-marker start\"></view>\r\n                  <text class=\"point-text\">{{item.startPoint}}</text>\r\n                </view>\r\n                <view class=\"route-line\"></view>\r\n                <view class=\"end-point\">\r\n                  <view class=\"point-marker end\"></view>\r\n                  <text class=\"point-text\">{{item.endPoint}}</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"trip-info\">\r\n                <view class=\"info-item\">\r\n                  <image src=\"/static/images/icons/calendar.png\" mode=\"aspectFit\" class=\"info-icon\"></image>\r\n                  <text class=\"info-text\">{{item.departureTime}}</text>\r\n                </view>\r\n                <view class=\"info-item\">\r\n                  <image src=\"/static/images/icons/people.png\" mode=\"aspectFit\" class=\"info-icon\"></image>\r\n                  <text class=\"info-text\">{{item.seatCount}}个座位</text>\r\n                </view>\r\n                <view class=\"info-item\" v-if=\"item.price\">\r\n                  <image src=\"/static/images/icons/price.png\" mode=\"aspectFit\" class=\"info-icon\"></image>\r\n                  <text class=\"info-text price\">¥{{item.price}}/人</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n            \r\n            <!-- 过期信息 -->\r\n            <view class=\"expired-info\">\r\n              <image src=\"/static/images/icons/time-expired.png\" mode=\"aspectFit\" class=\"expired-icon\"></image>\r\n              <text class=\"expired-text\">信息已于 {{item.expiredTime}} 过期</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 卡片底部按钮 -->\r\n          <view class=\"card-actions\">\r\n            <button class=\"action-button outline\" @click=\"deleteItem(item)\">删除</button>\r\n            <button class=\"action-button primary\" @click=\"republishItem(item)\">重新发布</button>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 无数据提示 -->\r\n      <view class=\"empty-state\" v-if=\"expiredList.length === 0 && !isLoading\">\r\n        <image src=\"/static/images/empty/no-expired.png\" mode=\"aspectFit\" class=\"empty-image\"></image>\r\n        <text class=\"empty-text\">暂无过期信息</text>\r\n      </view>\r\n      \r\n      <!-- 加载状态 -->\r\n      <view class=\"loading-state\" v-if=\"isLoading && !isRefreshing\">\r\n        <text class=\"loading-text\">加载中...</text>\r\n      </view>\r\n      \r\n      <!-- 到底提示 -->\r\n      <view class=\"list-bottom\" v-if=\"expiredList.length > 0 && !hasMore\">\r\n        <text class=\"bottom-text\">— 已经到底啦 —</text>\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue';\r\n\r\n// 数据\r\nconst expiredList = ref([]);\r\nconst page = ref(1);\r\nconst pageSize = ref(10);\r\nconst hasMore = ref(true);\r\nconst isLoading = ref(false);\r\nconst isRefreshing = ref(false);\r\n\r\n// 页面加载时执行\r\nonMounted(() => {\r\n  loadData();\r\n});\r\n\r\n// 加载数据\r\nconst loadData = () => {\r\n  if (isLoading.value) return;\r\n  isLoading.value = true;\r\n  \r\n  // 模拟数据加载\r\n  setTimeout(() => {\r\n    // 模拟数据\r\n    const mockData = [\r\n      {\r\n        id: '3001',\r\n        type: '长途拼车',\r\n        publishTime: '2023-10-10 16:30',\r\n        expiredTime: '2023-10-12 16:30',\r\n        startPoint: '磁县政府',\r\n        endPoint: '石家庄火车站',\r\n        departureTime: '2023-10-12 10:30',\r\n        seatCount: 3,\r\n        price: 50\r\n      },\r\n      {\r\n        id: '3002',\r\n        type: '上下班拼车',\r\n        publishTime: '2023-10-05 14:15',\r\n        expiredTime: '2023-10-09 14:15',\r\n        startPoint: '磁县老城区',\r\n        endPoint: '邯郸科技学院',\r\n        departureTime: '2023-10-09 07:30',\r\n        seatCount: 4,\r\n        price: 12\r\n      },\r\n      {\r\n        id: '3003',\r\n        type: '短途拼车',\r\n        publishTime: '2023-09-30 11:40',\r\n        expiredTime: '2023-10-02 11:40',\r\n        startPoint: '磁县体育场',\r\n        endPoint: '磁县汽车站',\r\n        departureTime: '2023-10-01 16:00',\r\n        seatCount: 2,\r\n        price: 5\r\n      }\r\n    ];\r\n    \r\n    if (page.value === 1) {\r\n      expiredList.value = mockData;\r\n    } else {\r\n      expiredList.value = [...expiredList.value, ...mockData];\r\n    }\r\n    \r\n    // 模拟没有更多数据\r\n    if (page.value >= 2) {\r\n      hasMore.value = false;\r\n    }\r\n    \r\n    isLoading.value = false;\r\n    isRefreshing.value = false;\r\n  }, 1000);\r\n};\r\n\r\n// 加载更多\r\nconst loadMore = () => {\r\n  if (!hasMore.value || isLoading.value) return;\r\n  page.value++;\r\n  loadData();\r\n};\r\n\r\n// 下拉刷新\r\nconst onRefresh = () => {\r\n  isRefreshing.value = true;\r\n  page.value = 1;\r\n  hasMore.value = true;\r\n  loadData();\r\n};\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 删除信息\r\nconst deleteItem = (item) => {\r\n  uni.showModal({\r\n    title: '提示',\r\n    content: '确定要删除此条信息吗？',\r\n    success: (res) => {\r\n      if (res.confirm) {\r\n        // 模拟删除操作\r\n        expiredList.value = expiredList.value.filter(i => i.id !== item.id);\r\n        uni.showToast({\r\n          title: '删除成功',\r\n          icon: 'success'\r\n        });\r\n      }\r\n    }\r\n  });\r\n};\r\n\r\n// 重新发布\r\nconst republishItem = (item) => {\r\n  uni.navigateTo({\r\n    url: `/carpool-package/pages/carpool/publish/index?id=${item.id}&type=republish`\r\n  });\r\n};\r\n\r\n// 显示清空确认\r\nconst showClearConfirm = () => {\r\n  if (expiredList.value.length === 0) {\r\n    uni.showToast({\r\n      title: '暂无数据可清空',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n  \r\n  uni.showModal({\r\n    title: '提示',\r\n    content: '确定要清空所有过期信息吗？此操作不可恢复。',\r\n    success: (res) => {\r\n      if (res.confirm) {\r\n        clearAll();\r\n      }\r\n    }\r\n  });\r\n};\r\n\r\n// 清空所有\r\nconst clearAll = () => {\r\n  expiredList.value = [];\r\n  uni.showToast({\r\n    title: '已清空',\r\n    icon: 'success'\r\n  });\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.expired-list-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.custom-navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 44px;\r\n  padding-top: var(--status-bar-height);\r\n  background: linear-gradient(135deg, #9E9E9E, #757575);\r\n  z-index: 100;\r\n}\r\n\r\n.navbar-content {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  height: 44px;\r\n  padding: 0 15px;\r\n}\r\n\r\n.navbar-left {\r\n  width: 50px;\r\n}\r\n\r\n.back-icon {\r\n  width: 20px;\r\n  height: 20px;\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  color: #FFFFFF;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n}\r\n\r\n.navbar-right {\r\n  width: 50px;\r\n  text-align: right;\r\n}\r\n\r\n.clear-text {\r\n  font-size: 14px;\r\n  color: #FFFFFF;\r\n}\r\n\r\n/* 内容区域 */\r\n.scrollable-content {\r\n  flex: 1;\r\n  margin-top: calc(44px + var(--status-bar-height));\r\n  padding: 12px;\r\n}\r\n\r\n/* 卡片列表 */\r\n.card-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.card-item {\r\n  background-color: #FFFFFF;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\r\n}\r\n\r\n/* 卡片头部 */\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 16px;\r\n  background-color: #F8FAFB;\r\n  border-bottom: 1px solid #EEEEEE;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.card-type {\r\n  font-size: 15px;\r\n  font-weight: 500;\r\n  color: #333333;\r\n}\r\n\r\n.publish-time {\r\n  font-size: 12px;\r\n  color: #999999;\r\n}\r\n\r\n.status-tag {\r\n  font-size: 12px;\r\n  padding: 2px 8px;\r\n  border-radius: 10px;\r\n}\r\n\r\n.status-expired {\r\n  background-color: rgba(153, 153, 153, 0.1);\r\n  color: #999999;\r\n}\r\n\r\n/* 卡片内容 */\r\n.card-content {\r\n  padding: 16px;\r\n}\r\n\r\n.route-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16px;\r\n}\r\n\r\n.route-points {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 6px;\r\n}\r\n\r\n.start-point, .end-point {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.point-marker {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-radius: 50%;\r\n}\r\n\r\n.start {\r\n  background-color: #1677FF;\r\n}\r\n\r\n.end {\r\n  background-color: #FF5722;\r\n}\r\n\r\n.route-line {\r\n  width: 2px;\r\n  height: 20px;\r\n  background-color: #DDDDDD;\r\n  margin-left: 5px;\r\n}\r\n\r\n.point-text {\r\n  font-size: 16px;\r\n  color: #333333;\r\n}\r\n\r\n.trip-info {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.info-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n}\r\n\r\n.info-text {\r\n  font-size: 14px;\r\n  color: #666666;\r\n}\r\n\r\n.price {\r\n  color: #FF5722;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 过期信息 */\r\n.expired-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-top: 16px;\r\n  padding-top: 16px;\r\n  border-top: 1px dashed #EEEEEE;\r\n}\r\n\r\n.expired-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n}\r\n\r\n.expired-text {\r\n  font-size: 12px;\r\n  color: #999999;\r\n}\r\n\r\n/* 卡片底部按钮 */\r\n.card-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  padding: 20px 24px;\r\n  gap: 20px;\r\n  border-top: 1px solid #EEEEEE;\r\n}\r\n\r\n.action-button {\r\n  padding: 10px 20px;\r\n  font-size: 16px;\r\n  height: 44px;\r\n  min-width: 90px;\r\n  border-radius: 6px;\r\n  background-color: transparent;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.action-button.outline {\r\n  color: #666666;\r\n  border: 1px solid #DDDDDD;\r\n}\r\n\r\n.action-button.primary {\r\n  color: #FFFFFF;\r\n  background-color: #1677FF;\r\n  border: 1px solid #1677FF;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 40px 0;\r\n}\r\n\r\n.empty-image {\r\n  width: 120px;\r\n  height: 120px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 16px;\r\n  color: #999999;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n/* 加载状态 */\r\n.loading-state {\r\n  padding: 16px 0;\r\n  text-align: center;\r\n}\r\n\r\n.loading-text {\r\n  font-size: 14px;\r\n  color: #999999;\r\n}\r\n\r\n/* 列表底部 */\r\n.list-bottom {\r\n  padding: 16px 0;\r\n  text-align: center;\r\n}\r\n\r\n.bottom-text {\r\n  font-size: 14px;\r\n  color: #999999;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/carpool-package/pages/carpool/my/expired-list.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni"], "mappings": ";;;;;;AAmGA,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAC1B,UAAM,OAAOA,cAAAA,IAAI,CAAC;AACDA,kBAAG,IAAC,EAAE;AACvB,UAAM,UAAUA,cAAAA,IAAI,IAAI;AACxB,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAC3B,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAG9BC,kBAAAA,UAAU,MAAM;AACd;IACF,CAAC;AAGD,UAAM,WAAW,MAAM;AACrB,UAAI,UAAU;AAAO;AACrB,gBAAU,QAAQ;AAGlB,iBAAW,MAAM;AAEf,cAAM,WAAW;AAAA,UACf;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,aAAa;AAAA,YACb,aAAa;AAAA,YACb,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,eAAe;AAAA,YACf,WAAW;AAAA,YACX,OAAO;AAAA,UACR;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,aAAa;AAAA,YACb,aAAa;AAAA,YACb,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,eAAe;AAAA,YACf,WAAW;AAAA,YACX,OAAO;AAAA,UACR;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,aAAa;AAAA,YACb,aAAa;AAAA,YACb,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,eAAe;AAAA,YACf,WAAW;AAAA,YACX,OAAO;AAAA,UACR;AAAA,QACP;AAEI,YAAI,KAAK,UAAU,GAAG;AACpB,sBAAY,QAAQ;AAAA,QAC1B,OAAW;AACL,sBAAY,QAAQ,CAAC,GAAG,YAAY,OAAO,GAAG,QAAQ;AAAA,QACvD;AAGD,YAAI,KAAK,SAAS,GAAG;AACnB,kBAAQ,QAAQ;AAAA,QACjB;AAED,kBAAU,QAAQ;AAClB,qBAAa,QAAQ;AAAA,MACtB,GAAE,GAAI;AAAA,IACT;AAGA,UAAM,WAAW,MAAM;AACrB,UAAI,CAAC,QAAQ,SAAS,UAAU;AAAO;AACvC,WAAK;AACL;IACF;AAGA,UAAM,YAAY,MAAM;AACtB,mBAAa,QAAQ;AACrB,WAAK,QAAQ;AACb,cAAQ,QAAQ;AAChB;IACF;AAGA,UAAM,SAAS,MAAM;AACnBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,aAAa,CAAC,SAAS;AAC3BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEf,wBAAY,QAAQ,YAAY,MAAM,OAAO,OAAK,EAAE,OAAO,KAAK,EAAE;AAClEA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,CAAC,SAAS;AAC9BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,mDAAmD,KAAK,EAAE;AAAA,MACnE,CAAG;AAAA,IACH;AAGA,UAAM,mBAAmB,MAAM;AAC7B,UAAI,YAAY,MAAM,WAAW,GAAG;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAEDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf;UACD;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrB,kBAAY,QAAQ;AACpBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnPA,GAAG,WAAW,eAAe;"}