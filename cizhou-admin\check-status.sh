#!/bin/bash

echo "🔍 磁州生活网后台管理系统状态检查"
echo "========================================"

# 检查Docker服务状态
echo "📦 Docker服务状态："
if docker info > /dev/null 2>&1; then
    echo "   ✅ Docker运行正常"
    
    # 检查各个容器状态
    echo ""
    echo "🐳 容器状态："
    
    containers=("cizhou-mysql-dev" "cizhou-redis-dev" "cizhou-nacos-dev")
    for container in "${containers[@]}"; do
        if docker ps --format "table {{.Names}}" | grep -q "$container"; then
            status=$(docker inspect --format='{{.State.Status}}' "$container" 2>/dev/null)
            if [ "$status" = "running" ]; then
                echo "   ✅ $container: 运行中"
            else
                echo "   ❌ $container: $status"
            fi
        else
            echo "   ❌ $container: 未运行"
        fi
    done
else
    echo "   ❌ Docker未运行"
fi

echo ""
echo "🌐 服务连接状态："

# 检查MySQL连接
if curl -s --connect-timeout 3 telnet://localhost:3306 > /dev/null 2>&1; then
    echo "   ✅ MySQL (3306): 可连接"
else
    echo "   ❌ MySQL (3306): 无法连接"
fi

# 检查Redis连接
if curl -s --connect-timeout 3 telnet://localhost:6379 > /dev/null 2>&1; then
    echo "   ✅ Redis (6379): 可连接"
else
    echo "   ❌ Redis (6379): 无法连接"
fi

# 检查Nacos连接
if curl -s --connect-timeout 3 http://localhost:8848/nacos > /dev/null 2>&1; then
    echo "   ✅ Nacos (8848): 可连接"
else
    echo "   ❌ Nacos (8848): 无法连接"
fi

# 检查后端服务
echo ""
echo "⚙️  后端服务状态："

# 检查网关服务
if curl -s --connect-timeout 3 http://localhost:8080/actuator/health > /dev/null 2>&1; then
    echo "   ✅ API网关 (8080): 运行正常"
else
    echo "   ❌ API网关 (8080): 无法访问"
fi

# 检查认证服务
if curl -s --connect-timeout 3 http://localhost:8081/actuator/health > /dev/null 2>&1; then
    echo "   ✅ 认证服务 (8081): 运行正常"
else
    echo "   ❌ 认证服务 (8081): 无法访问"
fi

# 检查前端服务
echo ""
echo "🎨 前端服务状态："

if curl -s --connect-timeout 3 http://localhost:3000 > /dev/null 2>&1; then
    echo "   ✅ 前端服务 (3000): 运行正常"
else
    echo "   ❌ 前端服务 (3000): 无法访问"
fi

# 检查进程状态
echo ""
echo "🔄 进程状态："

# 检查Java进程
JAVA_COUNT=$(ps aux | grep java | grep cizhou | grep -v grep | wc -l)
echo "   Java进程: $JAVA_COUNT 个"

# 检查Node.js进程
NODE_COUNT=$(ps aux | grep node | grep -E "(vite|dev)" | grep -v grep | wc -l)
echo "   Node.js进程: $NODE_COUNT 个"

# 系统资源使用情况
echo ""
echo "💻 系统资源："

# 内存使用情况
if command -v free &> /dev/null; then
    MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.1f%%", $3/$2 * 100.0}')
    echo "   内存使用: $MEMORY_USAGE"
fi

# 磁盘使用情况
if command -v df &> /dev/null; then
    DISK_USAGE=$(df -h / | awk 'NR==2{print $5}')
    echo "   磁盘使用: $DISK_USAGE"
fi

echo ""
echo "========================================"
echo "📋 快速访问链接："
echo "   前端管理后台: http://localhost:3000"
echo "   后端API文档:  http://localhost:8080/doc.html"
echo "   Nacos控制台:  http://localhost:8848/nacos"
echo ""
echo "🔑 默认账号: admin / admin123"
