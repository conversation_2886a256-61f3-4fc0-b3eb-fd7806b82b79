{"version": 3, "file": "detail-with-rewards.js", "sources": ["subPackages/activity/pages/detail-with-rewards.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHlccGFnZXNcZGV0YWlsLXdpdGgtcmV3YXJkcy52dWU"], "sourcesContent": ["<template>\n  <view class=\"activity-detail-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"navbar-left\" @click=\"goBack\">\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\n      </view>\n      <view class=\"navbar-title\">活动详情</view>\n      <view class=\"navbar-right\"></view>\n    </view>\n    \n    <!-- 活动封面 -->\n    <view class=\"activity-cover\" :style=\"{ paddingTop: navbarHeight + 'px' }\">\n      <image class=\"cover-image\" :src=\"activityInfo.coverImage\" mode=\"aspectFill\"></image>\n      <view class=\"activity-status\" :class=\"getStatusClass(activityInfo.status)\">\n        {{getStatusText(activityInfo.status)}}\n      </view>\n    </view>\n    \n    <!-- 活动信息卡片 -->\n    <view class=\"activity-card\">\n      <view class=\"activity-title\">{{activityInfo.title}}</view>\n      <view class=\"activity-meta\">\n        <view class=\"meta-item\">\n          <image class=\"meta-icon\" src=\"/static/images/tabbar/时间.png\"></image>\n          <text class=\"meta-text\">{{activityInfo.time}}</text>\n        </view>\n        <view class=\"meta-item\">\n          <image class=\"meta-icon\" src=\"/static/images/tabbar/地址.png\"></image>\n          <text class=\"meta-text\">{{activityInfo.location}}</text>\n        </view>\n        <view class=\"meta-item\">\n          <image class=\"meta-icon\" src=\"/static/images/tabbar/人数.png\"></image>\n          <text class=\"meta-text\">{{activityInfo.participants}}人已参与</text>\n        </view>\n        <view class=\"meta-item\">\n          <image class=\"meta-icon\" src=\"/static/images/tabbar/商家.png\"></image>\n          <text class=\"meta-text\">{{activityInfo.organizer}}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 我的奖励 -->\n    <view class=\"rewards-card\" v-if=\"userRewards.length > 0\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">我的奖励</text>\n        <text class=\"section-more\" @click=\"navigateTo('/pages/user-center/activity-rewards')\">查看全部</text>\n      </view>\n      \n      <view class=\"rewards-list\">\n        <view class=\"reward-item\" v-for=\"(item, index) in userRewards\" :key=\"index\" @click=\"viewRewardDetail(item)\">\n          <view class=\"reward-left\">\n            <image class=\"reward-icon\" :src=\"getRewardTypeIcon(item.type)\"></image>\n          </view>\n          <view class=\"reward-info\">\n            <text class=\"reward-name\">{{item.name}}</text>\n            <text class=\"reward-desc\">{{item.description}}</text>\n            <text class=\"reward-time\">{{item.time}}</text>\n          </view>\n          <view class=\"reward-right\">\n            <text class=\"reward-amount\" v-if=\"item.type === 'coupon' || item.type === 'redPacket'\">¥{{item.amount}}</text>\n            <text class=\"reward-amount\" v-else-if=\"item.type === 'points'\">+{{item.amount}}</text>\n            <text class=\"reward-status\" :class=\"{'status-used': item.status === 'used', 'status-expired': item.status === 'expired'}\">\n              {{getRewardStatusText(item.status)}}\n            </text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 活动详情 -->\n    <view class=\"detail-card\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">活动详情</text>\n      </view>\n      <rich-text class=\"detail-content\" :nodes=\"activityInfo.content\"></rich-text>\n    </view>\n    \n    <!-- 活动规则 -->\n    <view class=\"rules-card\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">活动规则</text>\n      </view>\n      <view class=\"rules-content\">\n        <view class=\"rule-item\" v-for=\"(rule, index) in activityInfo.rules\" :key=\"index\">\n          <text class=\"rule-number\">{{index + 1}}</text>\n          <text class=\"rule-text\">{{rule}}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 相关推荐 -->\n    <view class=\"related-card\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">相关推荐</text>\n        <text class=\"section-more\" @click=\"navigateTo('/subPackages/activity/pages/list')\">更多活动</text>\n      </view>\n      \n      <scroll-view scroll-x class=\"related-scroll\">\n        <view class=\"related-list\">\n          <view class=\"related-item\" v-for=\"(item, index) in relatedActivities\" :key=\"index\" @click=\"navigateTo('/subPackages/activity/pages/detail?id=' + item.id)\">\n            <image class=\"related-image\" :src=\"item.image\" mode=\"aspectFill\"></image>\n            <view class=\"related-info\">\n              <text class=\"related-title\">{{item.title}}</text>\n              <text class=\"related-time\">{{item.time}}</text>\n            </view>\n          </view>\n        </view>\n      </scroll-view>\n    </view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"footer-bar\">\n      <view class=\"footer-left\">\n        <button class=\"share-btn\" open-type=\"share\">\n          <image class=\"share-icon\" src=\"/static/images/tabbar/分享.png\"></image>\n          <text class=\"share-text\">分享</text>\n        </button>\n      </view>\n      <view class=\"footer-right\">\n        <view class=\"action-btn primary-btn\" v-if=\"activityInfo.status === 'ongoing'\" @click=\"joinActivity\">\n          立即参与\n        </view>\n        <view class=\"action-btn disabled-btn\" v-else-if=\"activityInfo.status === 'upcoming'\">\n          即将开始\n        </view>\n        <view class=\"action-btn disabled-btn\" v-else-if=\"activityInfo.status === 'ended'\">\n          活动已结束\n        </view>\n        <view class=\"action-btn secondary-btn\" @click=\"navigateTo('/pages/merchant-center/merchant')\">\n          商家入口\n        </view>\n      </view>\n    </view>\n    \n    <!-- 参与成功弹窗 -->\n    <view class=\"success-popup\" v-if=\"showSuccessPopup\" @click=\"closeSuccessPopup\">\n      <view class=\"success-content\" @click.stop>\n        <view class=\"success-header\">\n          <text class=\"success-title\">参与成功</text>\n          <view class=\"close-btn\" @click=\"closeSuccessPopup\">×</view>\n        </view>\n        <view class=\"success-body\">\n          <image class=\"success-icon\" src=\"/static/images/success.png\"></image>\n          <text class=\"success-message\">恭喜您成功参与活动</text>\n          <text class=\"success-desc\">您获得了以下奖励</text>\n          \n          <view class=\"popup-rewards\">\n            <view class=\"popup-reward-item\" v-for=\"(item, index) in newRewards\" :key=\"index\">\n              <image class=\"popup-reward-icon\" :src=\"getRewardTypeIcon(item.type)\"></image>\n              <view class=\"popup-reward-info\">\n                <text class=\"popup-reward-name\">{{item.name}}</text>\n                <text class=\"popup-reward-desc\">{{item.description}}</text>\n              </view>\n              <text class=\"popup-reward-amount\" v-if=\"item.type === 'coupon' || item.type === 'redPacket'\">¥{{item.amount}}</text>\n              <text class=\"popup-reward-amount\" v-else-if=\"item.type === 'points'\">+{{item.amount}}</text>\n            </view>\n          </view>\n        </view>\n        <view class=\"success-footer\">\n          <view class=\"success-btn\" @click=\"navigateTo('/pages/user-center/my-benefits')\">\n            查看我的福利\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      statusBarHeight: 20,\n      navbarHeight: 64,\n      activityId: '',\n      activityInfo: {\n        id: '1',\n        title: '夏日狂欢购物节',\n        coverImage: '/static/images/activity/shopping.jpg',\n        time: '2023-05-20 至 2023-05-30',\n        location: '磁州商业广场',\n        participants: 328,\n        organizer: '磁州商业联盟',\n        status: 'ongoing', // upcoming, ongoing, ended\n        content: '<p style=\"text-indent:2em;\">夏日狂欢购物节盛大开启！活动期间，参与商家推出多重优惠，满100减50，多买多送。</p><p style=\"text-indent:2em;\">同时，现场设有抽奖环节，有机会赢取iPhone、平板电脑等大奖。</p><p style=\"text-indent:2em;\">此外，消费满额即可获得精美礼品一份，数量有限，先到先得。</p><p style=\"text-indent:2em;\">活动详情请咨询各参与商家。</p>',\n        rules: [\n          '活动时间：2023年5月20日至5月30日',\n          '参与方式：在活动期间到店消费即可参与',\n          '奖励发放：系统自动发放至\"我的福利\"中',\n          '活动规则最终解释权归商家所有'\n        ]\n      },\n      userRewards: [\n        {\n          id: '1',\n          type: 'coupon',\n          name: '满100减20优惠券',\n          description: '适用于全部商家',\n          amount: 20,\n          time: '2023-05-20 获得',\n          status: 'available'\n        },\n        {\n          id: '2',\n          type: 'redPacket',\n          name: '5元现金红包',\n          description: '可直接提现至微信钱包',\n          amount: 5,\n          time: '2023-05-20 获得',\n          status: 'available'\n        }\n      ],\n      relatedActivities: [\n        {\n          id: '2',\n          title: '美食品鉴会',\n          time: '05-25 19:00',\n          image: '/static/images/activity/food.jpg'\n        },\n        {\n          id: '3',\n          title: '亲子嘉年华',\n          time: '05-28 全天',\n          image: '/static/images/activity/family.jpg'\n        },\n        {\n          id: '4',\n          title: '文化艺术节',\n          time: '06-01 至 06-05',\n          image: '/static/images/activity/art.jpg'\n        }\n      ],\n      showSuccessPopup: false,\n      newRewards: []\n    }\n  },\n  onLoad(options) {\n    // 获取状态栏高度\n    const sysInfo = uni.getSystemInfoSync();\n    this.statusBarHeight = sysInfo.statusBarHeight;\n    this.navbarHeight = this.statusBarHeight + 44;\n    \n    // 获取活动ID\n    if (options.id) {\n      this.activityId = options.id;\n      this.loadActivityDetail();\n    }\n  },\n  methods: {\n    // 返回上一页\n    goBack() {\n      uni.navigateBack();\n    },\n    \n    // 页面跳转\n    navigateTo(url) {\n      uni.navigateTo({\n        url: url\n      });\n    },\n    \n    // 加载活动详情\n    loadActivityDetail() {\n      // 这里应该是实际的API调用，获取活动详情\n      console.log('加载活动ID:', this.activityId);\n      // 当前使用模拟数据\n    },\n    \n    // 获取活动状态样式类\n    getStatusClass(status) {\n      switch (status) {\n        case 'upcoming':\n          return 'status-upcoming';\n        case 'ongoing':\n          return 'status-ongoing';\n        case 'ended':\n          return 'status-ended';\n        default:\n          return '';\n      }\n    },\n    \n    // 获取活动状态文本\n    getStatusText(status) {\n      switch (status) {\n        case 'upcoming':\n          return '即将开始';\n        case 'ongoing':\n          return '进行中';\n        case 'ended':\n          return '已结束';\n        default:\n          return '未知';\n      }\n    },\n    \n    // 获取奖励类型图标\n    getRewardTypeIcon(type) {\n      switch (type) {\n        case 'coupon':\n          return '/static/images/tabbar/卡券.png';\n        case 'redPacket':\n          return '/static/images/tabbar/我的红包.png';\n        case 'points':\n          return '/static/images/tabbar/每日签到.png';\n        case 'gift':\n          return '/static/images/tabbar/礼品.png';\n        default:\n          return '/static/images/tabbar/活动.png';\n      }\n    },\n    \n    // 获取奖励状态文本\n    getRewardStatusText(status) {\n      switch (status) {\n        case 'available':\n          return '可使用';\n        case 'used':\n          return '已使用';\n        case 'expired':\n          return '已过期';\n        default:\n          return '未知';\n      }\n    },\n    \n    // 查看奖励详情\n    viewRewardDetail(item) {\n      uni.showToast({\n        title: '查看详情: ' + item.name,\n        icon: 'none'\n      });\n    },\n    \n    // 参与活动\n    joinActivity() {\n      // 模拟参与活动\n      setTimeout(() => {\n        // 模拟获得的新奖励\n        this.newRewards = [\n          {\n            id: '3',\n            type: 'coupon',\n            name: '满200减50优惠券',\n            description: '限时特惠',\n            amount: 50\n          },\n          {\n            id: '4',\n            type: 'points',\n            name: '活动积分',\n            description: '可在积分商城兑换礼品',\n            amount: 100\n          }\n        ];\n        \n        // 显示成功弹窗\n        this.showSuccessPopup = true;\n        \n        // 将新奖励添加到用户奖励列表\n        this.userRewards = [...this.newRewards.map(item => ({\n          ...item,\n          time: '刚刚获得',\n          status: 'available'\n        })), ...this.userRewards];\n      }, 1000);\n    },\n    \n    // 关闭成功弹窗\n    closeSuccessPopup() {\n      this.showSuccessPopup = false;\n    }\n  },\n  // 分享功能\n  onShareAppMessage() {\n    return {\n      title: this.activityInfo.title,\n      path: '/subPackages/activity/pages/detail-with-rewards?id=' + this.activityId,\n      imageUrl: this.activityInfo.coverImage\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.activity-detail-container {\n  min-height: 100vh;\n  background-color: #f5f7fa;\n  padding-bottom: 100rpx;\n}\n\n/* 导航栏样式 */\n.custom-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 15px;\n  background: transparent;\n  color: #fff;\n  z-index: 100;\n}\n\n.navbar-left {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 36rpx;\n  height: 36rpx;\n}\n\n.navbar-title {\n  font-size: 18px;\n  font-weight: 500;\n}\n\n.navbar-right {\n  width: 60rpx;\n}\n\n/* 活动封面 */\n.activity-cover {\n  position: relative;\n  width: 100%;\n  height: 400rpx;\n}\n\n.cover-image {\n  width: 100%;\n  height: 100%;\n}\n\n.activity-status {\n  position: absolute;\n  top: 20rpx;\n  right: 20rpx;\n  padding: 6rpx 20rpx;\n  border-radius: 30rpx;\n  font-size: 24rpx;\n  color: #fff;\n}\n\n.status-upcoming {\n  background-color: #faad14;\n}\n\n.status-ongoing {\n  background-color: #52c41a;\n}\n\n.status-ended {\n  background-color: #999;\n}\n\n/* 活动信息卡片 */\n.activity-card {\n  margin: -40rpx 20rpx 20rpx;\n  padding: 30rpx;\n  background-color: #fff;\n  border-radius: 16rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n  position: relative;\n  z-index: 10;\n}\n\n.activity-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20rpx;\n}\n\n.activity-meta {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.meta-item {\n  display: flex;\n  align-items: center;\n  margin-right: 30rpx;\n  margin-bottom: 16rpx;\n  width: calc(50% - 30rpx);\n}\n\n.meta-icon {\n  width: 32rpx;\n  height: 32rpx;\n  margin-right: 10rpx;\n}\n\n.meta-text {\n  font-size: 26rpx;\n  color: #666;\n}\n\n/* 我的奖励 */\n.rewards-card {\n  margin: 20rpx;\n  padding: 30rpx;\n  background-color: #fff;\n  border-radius: 16rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.section-more {\n  font-size: 24rpx;\n  color: #3a7afe;\n}\n\n.rewards-list {\n  margin-top: 20rpx;\n}\n\n.reward-item {\n  display: flex;\n  align-items: center;\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.reward-item:last-child {\n  border-bottom: none;\n}\n\n.reward-left {\n  margin-right: 20rpx;\n}\n\n.reward-icon {\n  width: 80rpx;\n  height: 80rpx;\n}\n\n.reward-info {\n  flex: 1;\n}\n\n.reward-name {\n  font-size: 28rpx;\n  color: #333;\n  margin-bottom: 6rpx;\n  display: block;\n}\n\n.reward-desc {\n  font-size: 24rpx;\n  color: #666;\n  margin-bottom: 6rpx;\n  display: block;\n}\n\n.reward-time {\n  font-size: 22rpx;\n  color: #999;\n  display: block;\n}\n\n.reward-right {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n}\n\n.reward-amount {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #ff4d4f;\n  margin-bottom: 6rpx;\n}\n\n.reward-status {\n  font-size: 24rpx;\n  color: #3a7afe;\n  padding: 4rpx 12rpx;\n  background-color: #f0f5ff;\n  border-radius: 6rpx;\n}\n\n.status-used {\n  color: #52c41a;\n  background-color: #f6ffed;\n}\n\n.status-expired {\n  color: #999;\n  background-color: #f5f5f5;\n}\n\n/* 活动详情 */\n.detail-card {\n  margin: 20rpx;\n  padding: 30rpx;\n  background-color: #fff;\n  border-radius: 16rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.detail-content {\n  margin-top: 20rpx;\n  font-size: 28rpx;\n  color: #333;\n  line-height: 1.6;\n}\n\n/* 活动规则 */\n.rules-card {\n  margin: 20rpx;\n  padding: 30rpx;\n  background-color: #fff;\n  border-radius: 16rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.rules-content {\n  margin-top: 20rpx;\n}\n\n.rule-item {\n  display: flex;\n  margin-bottom: 16rpx;\n}\n\n.rule-number {\n  width: 40rpx;\n  height: 40rpx;\n  background-color: #f0f5ff;\n  color: #3a7afe;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 24rpx;\n  margin-right: 16rpx;\n  flex-shrink: 0;\n}\n\n.rule-text {\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.5;\n  flex: 1;\n}\n\n/* 相关推荐 */\n.related-card {\n  margin: 20rpx;\n  padding: 30rpx;\n  background-color: #fff;\n  border-radius: 16rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.related-scroll {\n  width: 100%;\n  white-space: nowrap;\n}\n\n.related-list {\n  display: flex;\n  padding: 10rpx 0;\n}\n\n.related-item {\n  width: 300rpx;\n  margin-right: 20rpx;\n  border-radius: 12rpx;\n  overflow: hidden;\n  background-color: #fff;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\n  display: inline-block;\n}\n\n.related-image {\n  width: 100%;\n  height: 180rpx;\n}\n\n.related-info {\n  padding: 16rpx;\n}\n\n.related-title {\n  font-size: 26rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8rpx;\n  display: block;\n  white-space: normal;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n}\n\n.related-time {\n  font-size: 22rpx;\n  color: #999;\n}\n\n/* 底部操作栏 */\n.footer-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 100rpx;\n  background-color: #fff;\n  display: flex;\n  align-items: center;\n  padding: 0 20rpx;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  z-index: 99;\n}\n\n.footer-left {\n  display: flex;\n  align-items: center;\n}\n\n.share-btn {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  background-color: transparent;\n  padding: 0;\n  margin: 0;\n  line-height: 1;\n  border: none;\n  outline: none;\n}\n\n.share-btn::after {\n  border: none;\n}\n\n.share-icon {\n  width: 40rpx;\n  height: 40rpx;\n  margin-bottom: 6rpx;\n}\n\n.share-text {\n  font-size: 22rpx;\n  color: #666;\n}\n\n.footer-right {\n  flex: 1;\n  display: flex;\n  justify-content: flex-end;\n}\n\n.action-btn {\n  height: 70rpx;\n  border-radius: 35rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 30rpx;\n  margin-left: 20rpx;\n  font-size: 28rpx;\n}\n\n.primary-btn {\n  background: linear-gradient(135deg, #3a7afe, #6ca6ff);\n  color: #fff;\n}\n\n.secondary-btn {\n  background-color: #f0f5ff;\n  color: #3a7afe;\n}\n\n.disabled-btn {\n  background-color: #f5f5f5;\n  color: #999;\n}\n\n/* 参与成功弹窗 */\n.success-popup {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 999;\n}\n\n.success-content {\n  width: 600rpx;\n  background-color: #fff;\n  border-radius: 16rpx;\n  overflow: hidden;\n}\n\n.success-header {\n  position: relative;\n  padding: 30rpx;\n  text-align: center;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.success-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.close-btn {\n  position: absolute;\n  right: 30rpx;\n  top: 30rpx;\n  font-size: 40rpx;\n  color: #999;\n  line-height: 1;\n}\n\n.success-body {\n  padding: 30rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.success-icon {\n  width: 120rpx;\n  height: 120rpx;\n  margin-bottom: 20rpx;\n}\n\n.success-message {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 10rpx;\n}\n\n.success-desc {\n  font-size: 26rpx;\n  color: #666;\n  margin-bottom: 30rpx;\n}\n\n.popup-rewards {\n  width: 100%;\n  margin-top: 20rpx;\n}\n\n.popup-reward-item {\n  display: flex;\n  align-items: center;\n  padding: 20rpx;\n  background-color: #f9f9f9;\n  border-radius: 12rpx;\n  margin-bottom: 20rpx;\n}\n\n.popup-reward-icon {\n  width: 60rpx;\n  height: 60rpx;\n  margin-right: 16rpx;\n}\n\n.popup-reward-info {\n  flex: 1;\n}\n\n.popup-reward-name {\n  font-size: 28rpx;\n  color: #333;\n  margin-bottom: 6rpx;\n  display: block;\n}\n\n.popup-reward-desc {\n  font-size: 24rpx;\n  color: #666;\n  display: block;\n}\n\n.popup-reward-amount {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #ff4d4f;\n}\n\n.success-footer {\n  padding: 20rpx 30rpx 30rpx;\n}\n\n.success-btn {\n  height: 80rpx;\n  background: linear-gradient(135deg, #3a7afe, #6ca6ff);\n  color: #fff;\n  border-radius: 40rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n}\n</style>\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity/pages/detail-with-rewards.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AA0KA,MAAA,YAAA;AAAA,EACE,OAAA;AACE,WAAA;AAAA,MACE,iBAAA;AAAA,MACA,cAAA;AAAA;;QAGE,IAAA;AAAA,QACA,OAAA;AAAA;;QAGA,UAAA;AAAA,QACA,cAAA;AAAA,QACA,WAAA;AAAA,QACA,QAAA;AAAA;AAAA;QAEA,OAAA;AAAA;UAEE;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA;;QAGA;AAAA,UACE,IAAA;AAAA;UAEA,MAAA;AAAA,UACA,aAAA;AAAA,UACA,QAAA;AAAA,UACA,MAAA;AAAA,UACA,QAAA;AAAA;QAEF;AAAA,UACE,IAAA;AAAA,UACA,MAAA;AAAA;;UAGA,QAAA;AAAA,UACA,MAAA;AAAA,UACA,QAAA;AAAA,QACF;AAAA;MAEF,mBAAA;AAAA,QACE;AAAA,UACE,IAAA;AAAA;UAEA,MAAA;AAAA,UACA,OAAA;AAAA;QAEF;AAAA,UACE,IAAA;AAAA;UAEA,MAAA;AAAA,UACA,OAAA;AAAA;QAEF;AAAA,UACE,IAAA;AAAA;UAEA,MAAA;AAAA,UACA,OAAA;AAAA,QACF;AAAA;;;IAIJ;AAAA;EAEF,OAAA,SAAA;AAEE,UAAA,UAAAA,oBAAA;AACA,SAAA,kBAAA,QAAA;AACA,SAAA,eAAA,KAAA,kBAAA;AAGA,QAAA,QAAA,IAAA;;;IAGA;AAAA;EAEF,SAAA;AAAA;AAAA,IAEE,SAAA;AACEA,oBAAA,MAAA,aAAA;AAAA;;IAIF,WAAA,KAAA;AACEA,oBAAAA,MAAA,WAAA;AAAA,QACE;AAAA,MACF,CAAA;AAAA;;IAIF,qBAAA;;;;;AAQE,cAAA,QAAA;AAAA,QACE,KAAA;;;;;AAKE,iBAAA;AAAA,QACF;AACE,iBAAA;AAAA,MACJ;AAAA;;IAIF,cAAA,QAAA;AACE,cAAA,QAAA;AAAA,QACE,KAAA;;;;;;QAMA;;MAEF;AAAA;;;;;AAOI,iBAAA;AAAA,QACF,KAAA;AACE,iBAAA;AAAA;AAEA,iBAAA;AAAA;AAEA,iBAAA;AAAA,QACF;AACE,iBAAA;AAAA,MACJ;AAAA;;;AAKA,cAAA,QAAA;AAAA,QACE,KAAA;;;;;;QAMA;;MAEF;AAAA;;;AAKAA,oBAAAA,MAAA,UAAA;AAAA;;MAGA,CAAA;AAAA;;IAIF,eAAA;AAEE,iBAAA,MAAA;AAEE,aAAA,aAAA;AAAA,UACE;AAAA,YACE,IAAA;AAAA;YAEA,MAAA;AAAA,YACA,aAAA;AAAA,YACA,QAAA;AAAA;UAEF;AAAA,YACE,IAAA;AAAA;;;YAIA,QAAA;AAAA,UACF;AAAA;;;UAQA,GAAA;AAAA;UAEA,QAAA;AAAA;MAEJ,GAAA,GAAA;AAAA;;IAIF,oBAAA;;IAEA;AAAA;;EAGF,oBAAA;AACE,WAAA;AAAA;MAEE,MAAA,wDAAA,KAAA;AAAA,MACA,UAAA,KAAA,aAAA;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7XA,GAAG,WAAW,eAAe;"}