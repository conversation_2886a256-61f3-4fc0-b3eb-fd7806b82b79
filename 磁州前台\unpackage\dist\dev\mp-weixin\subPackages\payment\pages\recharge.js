"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  __name: "recharge",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const navbarHeight = common_vendor.ref(64);
    const amount = common_vendor.ref("");
    const selectedPayment = common_vendor.ref("wxpay");
    const quickAmounts = common_vendor.ref([10, 50, 100, 200, 500]);
    const paymentMethods = common_vendor.ref([
      {
        id: "wxpay",
        name: "微信支付",
        icon: "/static/images/payment/wxpay.png"
      },
      {
        id: "alipay",
        name: "支付宝支付",
        icon: "/static/images/payment/alipay.png"
      }
    ]);
    const canRecharge = common_vendor.computed(() => {
      const amountNum = parseFloat(amount.value);
      return amountNum >= 1 && !isNaN(amountNum);
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const handleAmountInput = (e) => {
      const value = e.detail.value;
      if (value.indexOf(".") !== -1) {
        const decimal = value.split(".")[1];
        if (decimal.length > 2) {
          amount.value = parseFloat(value).toFixed(2);
        }
      }
    };
    const selectAmount = (amountValue) => {
      amount.value = amountValue.toString();
    };
    const selectPayment = (paymentId) => {
      selectedPayment.value = paymentId;
    };
    const submitRecharge = () => {
      if (!canRecharge.value) {
        return;
      }
      common_vendor.index.showLoading({
        title: "请求中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        if (selectedPayment.value === "wxpay") {
          common_vendor.index.showModal({
            title: "微信支付",
            content: `确认支付¥${amount.value}？`,
            success: (res) => {
              if (res.confirm) {
                processPayment();
              }
            }
          });
        } else if (selectedPayment.value === "alipay") {
          common_vendor.index.showModal({
            title: "支付宝支付",
            content: `确认支付¥${amount.value}？`,
            success: (res) => {
              if (res.confirm) {
                processPayment();
              }
            }
          });
        }
      }, 500);
    };
    const processPayment = () => {
      common_vendor.index.showLoading({
        title: "支付中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "充值成功",
          icon: "success",
          duration: 2e3,
          success: () => {
            setTimeout(() => {
              common_vendor.index.navigateBack();
            }, 2e3);
          }
        });
      }, 1500);
    };
    common_vendor.onMounted(() => {
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight;
      navbarHeight.value = statusBarHeight.value + 44;
    });
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0$5,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_vendor.o([($event) => amount.value = $event.detail.value, handleAmountInput]),
        e: amount.value,
        f: common_vendor.f(quickAmounts.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item),
            b: index,
            c: amount.value === item.toString() ? 1 : "",
            d: common_vendor.o(($event) => selectAmount(item), index)
          };
        }),
        g: navbarHeight.value + 10 + "px",
        h: common_vendor.f(paymentMethods.value, (item, index, i0) => {
          return common_vendor.e({
            a: item.icon,
            b: common_vendor.t(item.name),
            c: selectedPayment.value === item.id
          }, selectedPayment.value === item.id ? {
            d: common_assets._imports_1$6
          } : {}, {
            e: index,
            f: selectedPayment.value === item.id ? 1 : "",
            g: common_vendor.o(($event) => selectPayment(item.id), index)
          });
        }),
        i: !canRecharge.value,
        j: !canRecharge.value ? 1 : "",
        k: common_vendor.o(submitRecharge)
      };
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/payment/pages/recharge.js.map
