<template>
  <view class="group-orders-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">团购订单</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 状态筛选 -->
    <view class="filter-tabs" :style="{ marginTop: (statusBarHeight + 44) + 'px' }">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index" 
        class="tab-item" 
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        <text>{{ tab.name }}</text>
      </view>
    </view>
    
    <!-- 订单列表 -->
    <scroll-view 
      scroll-y 
      class="orders-list" 
      @scrolltolower="loadMore" 
      refresher-enabled 
      :refresher-triggered="refreshing" 
      @refresherrefresh="refresh"
    >
      <view v-if="ordersList.length > 0">
        <view class="order-item" v-for="item in ordersList" :key="item.id" @click="viewOrderDetail(item)">
          <view class="order-header">
            <view class="shop-info">
              <image class="shop-avatar" :src="item.shopAvatar" mode="aspectFill"></image>
              <text class="shop-name">{{ item.shopName }}</text>
              <image class="shop-arrow" src="/static/images/tabbar/右箭头.png" mode="aspectFit"></image>
            </view>
            <view class="order-status" :class="'status-' + item.status">{{ getStatusText(item.status) }}</view>
          </view>
          
          <view class="order-content">
            <image class="goods-image" :src="item.goodsImage" mode="aspectFill"></image>
            <view class="goods-info">
              <view class="goods-name">{{ item.goodsName }}</view>
              <view class="goods-spec" v-if="item.goodsSpec">{{ item.goodsSpec }}</view>
              <view class="goods-price-count">
                <text class="goods-price">¥{{ item.price }}</text>
                <text class="goods-count">x{{ item.count }}</text>
              </view>
            </view>
          </view>
          
          <view class="order-footer">
            <view class="order-time">下单时间：{{ item.createTime }}</view>
            <view class="order-total">
              <text>共{{ item.count }}件商品</text>
              <text class="total-price">实付：<text class="price-num">¥{{ item.totalAmount }}</text></text>
            </view>
          </view>
          
          <view class="order-actions">
            <view class="action-btn cancel-btn" v-if="item.status === 1" @click.stop="cancelOrder(item)">取消订单</view>
            <view class="action-btn pay-btn" v-if="item.status === 1" @click.stop="payOrder(item)">立即付款</view>
            <view class="action-btn" v-if="item.status === 2" @click.stop="checkDelivery(item)">查看配送</view>
            <view class="action-btn" v-if="item.status === 3" @click.stop="confirmReceive(item)">确认收货</view>
            <view class="action-btn" v-if="item.status === 4" @click.stop="writeReview(item)">评价</view>
            <view class="action-btn" v-if="item.status === 4 || item.status === 5" @click.stop="deleteOrder(item)">删除订单</view>
            <view class="action-btn contact-btn" @click.stop="contactShop(item)">联系商家</view>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="ordersList.length === 0 && !loading">
        <image src="/static/images/tabbar/空状态.png" class="empty-icon"></image>
        <view class="empty-text">暂无团购订单</view>
        <view class="empty-subtext">去看看有哪些优惠的团购活动吧</view>
        <view class="empty-btn" @click="goToGroupBuy">浏览团购</view>
      </view>
      
      <!-- 加载状态 -->
      <view class="loading-state" v-if="loading && !refreshing">
        <view class="loading-icon"></view>
        <text>加载中...</text>
      </view>
      
      <!-- 加载完成 -->
      <view class="load-all" v-if="loadAll && ordersList.length > 0">
        <text>已加载全部订单</text>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// Vue 3 Composition API 代码开始
// 状态栏高度
const statusBarHeight = ref(20);

// 当前选中的选项卡
const currentTab = ref(0);

// 选项卡数据
const tabs = ref([
  { name: '全部' },
  { name: '待付款' },
  { name: '待发货' },
  { name: '待收货' },
  { name: '已完成' }
]);

// 订单列表数据
const ordersList = ref([]);

// 分页相关
const page = ref(1);
const limit = ref(10);
const loading = ref(false);
const refreshing = ref(false);
const loadAll = ref(false);

// 生命周期钩子
onMounted(() => {
  // 获取状态栏高度
  const sysInfo = uni.getSystemInfoSync();
  statusBarHeight.value = sysInfo.statusBarHeight;
  
  // 加载订单数据
  loadOrders();
});

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 切换选项卡
const switchTab = (index) => {
  if (currentTab.value !== index) {
    currentTab.value = index;
    refresh();
  }
};

// 获取状态文字
const getStatusText = (status) => {
  switch (status) {
    case 1: return '待付款';
    case 2: return '待发货';
    case 3: return '待收货';
    case 4: return '已完成';
    case 5: return '已取消';
    default: return '未知状态';
  }
};

// 加载订单数据
const loadOrders = () => {
  if (loading.value || loadAll.value) return;
  
  loading.value = true;
  
  // 模拟请求
  setTimeout(() => {
    // 构造请求参数
    const params = {
      page: page.value,
      limit: limit.value,
      status: currentTab.value === 0 ? '' : currentTab.value
    };
    
    // 模拟数据
    const mockData = getMockOrdersData();
    let filteredData = mockData;
    
    // 根据状态筛选
    if (currentTab.value > 0) {
      filteredData = mockData.filter(item => item.status === currentTab.value);
    }
    
    // 更新数据
    if (page.value === 1) {
      ordersList.value = filteredData;
    } else {
      ordersList.value = [...ordersList.value, ...filteredData];
    }
    
    // 判断是否加载完全部
    loadAll.value = true; // 模拟数据全部加载完
    
    loading.value = false;
    refreshing.value = false;
    
    // 页码+1
    if (!loadAll.value) {
      page.value++;
    }
  }, 800);
};

// 刷新
const refresh = () => {
  refreshing.value = true;
  page.value = 1;
  loadAll.value = false;
  loadOrders();
};

// 加载更多
const loadMore = () => {
  loadOrders();
};

// 查看订单详情
const viewOrderDetail = (order) => {
  uni.navigateTo({
    url: `/pages/groupbuy/order-detail?id=${order.id}`
  });
};

// 取消订单
const cancelOrder = (order) => {
  uni.showModal({
    title: '提示',
    content: '确定要取消该订单吗？',
    success: (res) => {
      if (res.confirm) {
        uni.showLoading({
          title: '处理中...'
        });
        
        // 模拟请求
        setTimeout(() => {
          uni.hideLoading();
          
          // 更新本地数据
          const index = ordersList.value.findIndex(item => item.id === order.id);
          if (index !== -1) {
            ordersList.value[index].status = 5; // 已取消
          }
          
          uni.showToast({
            title: '订单已取消',
            icon: 'success'
          });
        }, 500);
      }
    }
  });
};

// 支付订单
const payOrder = (order) => {
  uni.navigateTo({
    url: `/pages/pay/index?orderId=${order.id}&amount=${order.totalAmount}`
  });
};

// 查看配送
const checkDelivery = (order) => {
  uni.showToast({
    title: '暂无配送信息',
    icon: 'none'
  });
};

// 确认收货
const confirmReceive = (order) => {
  uni.showModal({
    title: '提示',
    content: '确认已收到商品吗？',
    success: (res) => {
      if (res.confirm) {
        uni.showLoading({
          title: '处理中...'
        });
        
        // 模拟请求
        setTimeout(() => {
          uni.hideLoading();
          
          // 更新本地数据
          const index = ordersList.value.findIndex(item => item.id === order.id);
          if (index !== -1) {
            ordersList.value[index].status = 4; // 已完成
          }
          
          uni.showToast({
            title: '确认收货成功',
            icon: 'success'
          });
        }, 500);
      }
    }
  });
};

// 评价
const writeReview = (order) => {
  uni.navigateTo({
    url: `/pages/review/write?orderId=${order.id}`
  });
};

// 删除订单
const deleteOrder = (order) => {
  uni.showModal({
    title: '提示',
    content: '确定要删除该订单吗？',
    success: (res) => {
      if (res.confirm) {
        uni.showLoading({
          title: '处理中...'
        });
        
        // 模拟请求
        setTimeout(() => {
          uni.hideLoading();
          
          // 从列表中移除
          const index = ordersList.value.findIndex(item => item.id === order.id);
          if (index !== -1) {
            ordersList.value.splice(index, 1);
          }
          
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          });
        }, 500);
      }
    }
  });
};

// 联系商家
const contactShop = (order) => {
  uni.makePhoneCall({
    phoneNumber: order.shopPhone || '10086',
    fail: () => {
      uni.showToast({
        title: '拨打电话失败',
        icon: 'none'
      });
    }
  });
};

// 前往团购页面
const goToGroupBuy = () => {
  uni.navigateTo({
    url: '/subPackages/activity-showcase/pages/group-buy/index'
  });
};

// 模拟订单数据
const getMockOrdersData = () => {
  return [
    {
      id: '1001',
      shopName: '磁州烧饼店',
      shopAvatar: '/static/images/shop/shop1.jpg',
      shopPhone: '13812345678',
      goodsName: '正宗磁州烧饼 买二送一',
      goodsImage: '/static/images/product/food1.jpg',
      goodsSpec: '原味 3个装',
      price: 19.9,
      count: 1,
      totalAmount: 19.9,
      status: 1, // 待付款
      createTime: '2023-09-15 14:30'
    },
    {
      id: '1002',
      shopName: '水果鲜生',
      shopAvatar: '/static/images/shop/shop2.jpg',
      shopPhone: '13998765432',
      goodsName: '精品水果礼盒 新鲜当季水果',
      goodsImage: '/static/images/product/food2.jpg',
      goodsSpec: '精品混合装 3kg',
      price: 59.9,
      count: 1,
      totalAmount: 59.9,
      status: 2, // 待发货
      createTime: '2023-09-14 10:15'
    },
    {
      id: '1003',
      shopName: '老北京小吃',
      shopAvatar: '/static/images/shop/shop3.jpg',
      shopPhone: '13756781234',
      goodsName: '手工制作老北京糖葫芦',
      goodsImage: '/static/images/product/food3.jpg',
      goodsSpec: '山楂味 10串',
      price: 15.9,
      count: 2,
      totalAmount: 31.8,
      status: 3, // 待收货
      createTime: '2023-09-13 16:45'
    },
    {
      id: '1004',
      shopName: '磁州特色小吃',
      shopAvatar: '/static/images/shop/shop4.jpg',
      shopPhone: '13612345678',
      goodsName: '特色小吃套餐 多种口味',
      goodsImage: '/static/images/product/food4.jpg',
      goodsSpec: '经典6件套',
      price: 39.9,
      count: 1,
      totalAmount: 39.9,
      status: 4, // 已完成
      createTime: '2023-09-10 09:20'
    },
    {
      id: '1005',
      shopName: '磁州烧饼店',
      shopAvatar: '/static/images/shop/shop1.jpg',
      shopPhone: '13812345678',
      goodsName: '正宗磁州烧饼 买二送一',
      goodsImage: '/static/images/product/food1.jpg',
      goodsSpec: '芝麻味 3个装',
      price: 21.9,
      count: 1,
      totalAmount: 21.9,
      status: 5, // 已取消
      createTime: '2023-09-08 11:30'
    }
  ];
};
// Vue 3 Composition API 代码结束
</script>

<style lang="scss">
.group-orders-container {
  min-height: 100vh;
  background-color: #F5F7FA;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background: linear-gradient(135deg, #1677FF, #0052CC);
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 100;
}

.navbar-left {
  width: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 20px;
  height: 20px;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.navbar-right {
  width: 40px;
}

/* 状态筛选 */
.filter-tabs {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #F0F0F0;
  z-index: 99;
}

.tab-item {
  flex: 1;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #1677FF;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background-color: #1677FF;
  border-radius: 2px;
}

/* 订单列表 */
.orders-list {
  position: absolute;
  top: 88px;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 10px;
}

.order-item {
  background-color: #FFFFFF;
  border-radius: 12px;
  margin-bottom: 15px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid #F5F5F5;
}

.shop-info {
  display: flex;
  align-items: center;
}

.shop-avatar {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  margin-right: 8px;
}

.shop-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-right: 5px;
}

.shop-arrow {
  width: 14px;
  height: 14px;
}

.order-status {
  font-size: 14px;
  font-weight: 500;
}

.status-1 {
  color: #FF9500; /* 待付款 */
}

.status-2 {
  color: #007AFF; /* 待发货 */
}

.status-3 {
  color: #34C759; /* 待收货 */
}

.status-4 {
  color: #8E8E93; /* 已完成 */
}

.status-5 {
  color: #8E8E93; /* 已取消 */
}

.order-content {
  display: flex;
  padding: 15px 0;
  border-bottom: 1px solid #F5F5F5;
}

.goods-image {
  width: 80px;
  height: 80px;
  border-radius: 6px;
  margin-right: 12px;
}

.goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.goods-name {
  font-size: 15px;
  color: #333;
  font-weight: 500;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.goods-spec {
  font-size: 13px;
  color: #999;
  margin-bottom: 6px;
}

.goods-price-count {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-price {
  font-size: 15px;
  color: #FF3B30;
  font-weight: 500;
}

.goods-count {
  font-size: 13px;
  color: #999;
}

.order-footer {
  padding: 12px 0;
  display: flex;
  flex-direction: column;
}

.order-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}

.order-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  color: #666;
}

.total-price {
  font-size: 13px;
  color: #333;
}

.price-num {
  font-size: 16px;
  color: #FF3B30;
  font-weight: 500;
}

.order-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
  flex-wrap: wrap;
}

.action-btn {
  height: 30px;
  padding: 0 12px;
  border-radius: 15px;
  font-size: 13px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
  background-color: #F5F5F5;
  color: #666;
  border: 1px solid #E5E5E5;
}

.cancel-btn {
  color: #666;
}

.pay-btn {
  background-color: #1677FF;
  color: #FFF;
  border: none;
}

.contact-btn {
  border: 1px solid #1677FF;
  color: #1677FF;
  background-color: #FFF;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 100px;
}

.empty-icon {
  width: 120px;
  height: 120px;
  margin-bottom: 15px;
}

.empty-text {
  font-size: 16px;
  color: #999;
  margin-bottom: 8px;
}

.empty-subtext {
  font-size: 14px;
  color: #AAAAAA;
  margin-bottom: 20px;
}

.empty-btn {
  width: 120px;
  height: 40px;
  background: linear-gradient(135deg, #1677FF, #0052CC);
  color: #FFF;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

/* 加载状态 */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  color: #999;
  font-size: 14px;
}

.loading-icon {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #1677FF;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 加载完成 */
.load-all {
  text-align: center;
  padding: 15px 0;
  color: #999;
  font-size: 13px;
}
</style> 
