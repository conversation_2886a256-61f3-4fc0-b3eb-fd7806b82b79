"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      flashOn: false,
      showVerificationPopup: false,
      verificationData: {
        type: "",
        name: "",
        user: "",
        code: "",
        expiry: ""
      }
    };
  },
  onLoad() {
    setTimeout(() => {
      this.handleScanResult({
        type: "拼团活动",
        name: "双人下午茶套餐拼团",
        user: "张三 (138****8888)",
        code: "GP20230618001",
        expiry: "2023-06-25 23:59:59"
      });
    }, 3e3);
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    showHelp() {
      common_vendor.index.showToast({
        title: "扫码核销帮助",
        icon: "none"
      });
    },
    handleCameraError(e) {
      common_vendor.index.showToast({
        title: "摄像头启动失败，请检查权限设置",
        icon: "none"
      });
      common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/verification/scan.vue:127", "相机错误:", e);
    },
    toggleFlash() {
      this.flashOn = !this.flashOn;
      common_vendor.index.showToast({
        title: this.flashOn ? "闪光灯已开启" : "闪光灯已关闭",
        icon: "none"
      });
    },
    navigateToManual() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/verification/manual"
      });
    },
    handleScanResult(result) {
      this.verificationData = result;
      this.showVerificationPopup = true;
    },
    closePopup() {
      this.showVerificationPopup = false;
    },
    confirmVerification() {
      common_vendor.index.showLoading({
        title: "核销中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "核销成功",
          icon: "success"
        });
        this.closePopup();
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      }, 1e3);
    }
  }
};
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: common_vendor.o((...args) => $options.handleCameraError && $options.handleCameraError(...args)),
    d: common_assets._imports_0$45,
    e: common_vendor.p({
      d: "M9 18h6M12 6V2M7.5 10.5L5 8M16.5 10.5L19 8M12 18a6 6 0 0 0 0-12 6 6 0 0 0 0 12z"
    }),
    f: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      width: "24",
      height: "24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    g: common_vendor.o((...args) => $options.toggleFlash && $options.toggleFlash(...args)),
    h: common_vendor.p({
      d: "M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"
    }),
    i: common_vendor.p({
      d: "M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"
    }),
    j: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      width: "24",
      height: "24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    k: common_vendor.o((...args) => $options.navigateToManual && $options.navigateToManual(...args)),
    l: $data.showVerificationPopup
  }, $data.showVerificationPopup ? {
    m: common_vendor.o((...args) => $options.closePopup && $options.closePopup(...args)),
    n: common_vendor.t($data.verificationData.type),
    o: common_vendor.t($data.verificationData.name),
    p: common_vendor.t($data.verificationData.user),
    q: common_vendor.t($data.verificationData.code),
    r: common_vendor.t($data.verificationData.expiry),
    s: common_vendor.o((...args) => $options.closePopup && $options.closePopup(...args)),
    t: common_vendor.o((...args) => $options.confirmVerification && $options.confirmVerification(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/verification/scan.js.map
