/**
 * 位置服务工具函数
 */

// 默认位置（磁县）
const DEFAULT_LOCATION = {
  latitude: 36.313076,
  longitude: 114.347312,
  province: '河北省',
  city: '邯郸市',
  district: '磁县',
  address: '河北省邯郸市磁县',
  location: '河北省 邯郸市 磁县'
};

// 获取用户位置信息 - 优化过的版本，避免重复请求授权
export const getUserLocation = (options = {}) => {
  return new Promise((resolve, reject) => {
    // 检查是否正在请求位置权限，避免重复请求
    const isRequestingLocation = uni.getStorageSync('isRequestingLocation');
    if (isRequestingLocation === 'true' || isRequestingLocation === true) {
      console.log('已有位置请求正在进行，避免重复请求');
      
      // 使用默认位置并返回
      if (options.useDefaultOnFail !== false) {
        resolve({...DEFAULT_LOCATION, timestamp: Date.now()});
      } else {
        reject(new Error('已有位置请求正在进行'));
      }
      return;
    }
    
    // 标记正在请求位置
    uni.setStorageSync('isRequestingLocation', 'true');
    
    // 首先检查本地是否有存储的位置信息
    const savedLocation = uni.getStorageSync('user_location');
    if (savedLocation && !options.forceRefresh) {
      console.log('使用本地存储的位置信息');
      // 清除请求标记
      uni.removeStorageSync('isRequestingLocation');
      resolve(savedLocation);
      return;
    }
    
    // 检查是否已标记为处理过位置授权
    const locationAuthChecked = uni.getStorageSync('locationAuthChecked');
    
    // 检查位置权限设置
    uni.getSetting({
      success: (res) => {
        // 检查是否已授权位置
        if (res.authSetting && res.authSetting['scope.userLocation']) {
          // 用户已授权，直接获取位置
          getLocationInfo(options, resolve, reject);
        } else if (!locationAuthChecked) {
          // 用户未授权，且未请求过权限，记录已请求
          uni.setStorageSync('locationAuthChecked', true);
          
          // 请求位置权限
          uni.authorize({
            scope: 'scope.userLocation',
            success: () => {
              // 获取位置信息
              getLocationInfo(options, resolve, reject);
            },
            fail: (err) => {
              console.log('用户拒绝授权位置权限:', err);
              // 清除请求标记
              uni.removeStorageSync('isRequestingLocation');
              // 使用默认位置
              useDefaultLocation(options, resolve, reject);
            }
          });
        } else {
          // 已请求过权限但被拒绝，直接使用默认位置
          console.log('用户之前已拒绝位置授权，使用默认位置');
          // 清除请求标记
          uni.removeStorageSync('isRequestingLocation');
          useDefaultLocation(options, resolve, reject);
        }
      },
      fail: (err) => {
        console.error('获取设置失败:', err);
        // 清除请求标记
        uni.removeStorageSync('isRequestingLocation');
        useDefaultLocation(options, resolve, reject);
      }
    });
  });
};

// 内部函数：获取位置信息
function getLocationInfo(options, resolve, reject) {
    uni.getLocation({
    type: options.type || 'gcj02',
    altitude: options.altitude || false,
    isHighAccuracy: options.isHighAccuracy || true,
    highAccuracyExpireTime: options.timeout || 3000,
      success: (res) => {
        console.log('获取位置成功:', res);
        
        // 构建位置信息对象
        const location = {
          latitude: res.latitude,
          longitude: res.longitude,
        // 以下信息需要通过逆地理编码获取，这里使用默认值
          province: DEFAULT_LOCATION.province,
          city: DEFAULT_LOCATION.city,
          district: DEFAULT_LOCATION.district,
          address: DEFAULT_LOCATION.address,
        location: DEFAULT_LOCATION.location,
        timestamp: Date.now()
        };
        
      // 保存位置信息到本地存储
      uni.setStorageSync('user_location', location);
      
      // 清除请求标记
      uni.removeStorageSync('isRequestingLocation');
        
        // 返回位置信息
        resolve(location);
      },
      fail: (err) => {
        console.error('获取位置失败:', err);
      // 清除请求标记
      uni.removeStorageSync('isRequestingLocation');
      useDefaultLocation(options, resolve, reject);
    }
  });
}
        
// 内部函数：使用默认位置
function useDefaultLocation(options, resolve, reject) {
        if (options.useDefaultOnFail !== false) {
          console.log('使用默认位置');
    
    // 构建包含时间戳的默认位置
    const defaultLocation = {
      ...DEFAULT_LOCATION,
      timestamp: Date.now()
    };
    
    // 保存默认位置到本地存储
    uni.setStorageSync('user_location', defaultLocation);
    
    resolve(defaultLocation);
        } else {
    reject(new Error('获取位置失败，且不允许使用默认位置'));
        }
      }

// 计算两点之间的距离（单位：米）
export const calculateDistance = (lat1, lng1, lat2, lng2) => {
  const radLat1 = (lat1 * Math.PI) / 180.0;
  const radLat2 = (lat2 * Math.PI) / 180.0;
  const a = radLat1 - radLat2;
  const b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180.0;
  
  let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
  s = s * 6378.137; // 地球半径
  s = Math.round(s * 10000) / 10; // 输出为米
  
  return s;
};

// 格式化距离显示
export const formatDistance = (distance) => {
  if (distance < 1000) {
    return `${distance}米`;
  } else {
    return `${(distance / 1000).toFixed(1)}公里`;
  }
}; 

// 全局定位工具方法
export function getLocationWithAuth(callback) {
  const located = uni.getStorageSync('hasLocated');
  if (located) {
    callback && callback();
  } else {
    uni.getLocation({
      type: 'wgs84',
      success: (res) => {
        uni.setStorageSync('hasLocated', true);
        callback && callback();
      },
      fail: () => {
        uni.showToast({ title: '需要定位权限', icon: 'none' });
      }
    });
  }
} 