{"version": 3, "file": "GroupBuyCard.js", "sources": ["subPackages/activity-showcase/components/activity/GroupBuyCard.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7Avc3ViUGFja2FnZXMvYWN0aXZpdHktc2hvd2Nhc2UvY29tcG9uZW50cy9hY3Rpdml0eS9Hcm91cEJ1eUNhcmQudnVl"], "sourcesContent": ["<template>\r\n  <!-- 拼团活动卡片 - 苹果风格设计 -->\r\n  <view class=\"group-buy-card\">\r\n    <!-- 使用基础活动卡片 -->\r\n    <ActivityCard \r\n      :item=\"item\" \r\n      @favorite=\"$emit('favorite', item.id)\"\r\n      @action=\"$emit('action', { id: item.id, type: item.type, status: item.status })\"\r\n    >\r\n      <!-- 拼团特有信息插槽 -->\r\n      <template #special-info>\r\n        <view class=\"group-buy-special\">\r\n          <!-- 价格区域 -->\r\n          <view class=\"price-section\">\r\n            <view class=\"current-price\">\r\n              <text class=\"price-symbol\">¥</text>\r\n              <text class=\"price-value\">{{item.groupPrice}}</text>\r\n            </view>\r\n            <view class=\"original-price\">\r\n              <text class=\"price-label\">原价</text>\r\n              <text class=\"price-through\">¥{{item.originalPrice}}</text>\r\n            </view>\r\n            <view class=\"discount-tag\">\r\n              <text class=\"discount-value\">{{discountPercent}}折</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 拼团进度 -->\r\n          <view class=\"group-progress\">\r\n            <view class=\"progress-header\">\r\n              <text class=\"progress-title\">拼团进度</text>\r\n              <text class=\"progress-status\">{{item.currentGroupMembers}}/{{item.groupSize}}人</text>\r\n            </view>\r\n            <view class=\"progress-bar\">\r\n              <view class=\"progress-inner\" :style=\"{width: progressWidth + '%'}\"></view>\r\n            </view>\r\n            <view class=\"progress-tip\" v-if=\"item.status === 'ongoing'\">\r\n              <text class=\"tip-text\">还差{{remainCount}}人成团，快邀请好友吧！</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 拼团成员头像 -->\r\n          <view class=\"group-members\" v-if=\"item.participants && item.participants.length > 0\">\r\n            <view class=\"members-title\">参团好友</view>\r\n            <view class=\"members-avatars\">\r\n              <view \r\n                class=\"member-avatar-wrapper\" \r\n                v-for=\"(member, index) in displayMembers\" \r\n                :key=\"index\"\r\n              >\r\n                <image :src=\"member.avatar\" class=\"member-avatar\" mode=\"aspectFill\"></image>\r\n                <view class=\"team-leader-badge\" v-if=\"index === 0\">团长</view>\r\n              </view>\r\n              <view class=\"more-members\" v-if=\"item.participants.length > maxDisplayMembers\">\r\n                <text>+{{item.participants.length - maxDisplayMembers}}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </template>\r\n    </ActivityCard>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { computed } from 'vue';\r\nimport ActivityCard from './ActivityCard.vue';\r\n\r\nconst props = defineProps({\r\n  item: {\r\n    type: Object,\r\n    required: true\r\n  }\r\n});\r\n\r\n// 最大显示成员数\r\nconst maxDisplayMembers = 4;\r\n\r\n// 计算折扣百分比\r\nconst discountPercent = computed(() => {\r\n  if (!props.item.groupPrice || !props.item.originalPrice) return '';\r\n  return Math.floor((props.item.groupPrice / props.item.originalPrice) * 10);\r\n});\r\n\r\n// 计算进度条宽度\r\nconst progressWidth = computed(() => {\r\n  if (!props.item.currentGroupMembers || !props.item.groupSize) return 0;\r\n  return (props.item.currentGroupMembers / props.item.groupSize) * 100;\r\n});\r\n\r\n// 计算剩余所需人数\r\nconst remainCount = computed(() => {\r\n  if (!props.item.currentGroupMembers || !props.item.groupSize) return 0;\r\n  return props.item.groupSize - props.item.currentGroupMembers;\r\n});\r\n\r\n// 显示的成员头像\r\nconst displayMembers = computed(() => {\r\n  if (!props.item.participants) return [];\r\n  return props.item.participants.slice(0, maxDisplayMembers);\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n/* 拼团活动卡片特有样式 */\r\n.group-buy-card {\r\n  /* 继承基础卡片样式 */\r\n}\r\n\r\n/* 拼团特有信息区域 */\r\n.group-buy-special {\r\n  padding: 20rpx;\r\n  background-color: rgba(52, 199, 89, 0.05);\r\n  border-radius: 20rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n/* 价格区域 */\r\n.price-section {\r\n  display: flex;\r\n  align-items: baseline;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.current-price {\r\n  display: flex;\r\n  align-items: baseline;\r\n  color: #34c759;\r\n  margin-right: 16rpx;\r\n}\r\n\r\n.price-symbol {\r\n  font-size: 24rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.price-value {\r\n  font-size: 40rpx;\r\n  font-weight: 700;\r\n}\r\n\r\n.original-price {\r\n  display: flex;\r\n  align-items: baseline;\r\n  margin-right: 16rpx;\r\n}\r\n\r\n.price-label {\r\n  font-size: 22rpx;\r\n  color: #8e8e93;\r\n  margin-right: 4rpx;\r\n}\r\n\r\n.price-through {\r\n  font-size: 24rpx;\r\n  color: #8e8e93;\r\n  text-decoration: line-through;\r\n}\r\n\r\n.discount-tag {\r\n  padding: 4rpx 10rpx;\r\n  background-color: rgba(52, 199, 89, 0.1);\r\n  border-radius: 10rpx;\r\n}\r\n\r\n.discount-value {\r\n  font-size: 22rpx;\r\n  color: #34c759;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 拼团进度 */\r\n.group-progress {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.progress-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.progress-title {\r\n  font-size: 24rpx;\r\n  color: #000000;\r\n  font-weight: 500;\r\n}\r\n\r\n.progress-status {\r\n  font-size: 24rpx;\r\n  color: #34c759;\r\n  font-weight: 500;\r\n}\r\n\r\n.progress-bar {\r\n  height: 10rpx;\r\n  background-color: rgba(52, 199, 89, 0.1);\r\n  border-radius: 5rpx;\r\n  overflow: hidden;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.progress-inner {\r\n  height: 100%;\r\n  background-color: #34c759;\r\n  border-radius: 5rpx;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.progress-tip {\r\n  margin-top: 8rpx;\r\n}\r\n\r\n.tip-text {\r\n  font-size: 22rpx;\r\n  color: #ff9500;\r\n}\r\n\r\n/* 拼团成员 */\r\n.group-members {\r\n  margin-top: 16rpx;\r\n}\r\n\r\n.members-title {\r\n  font-size: 24rpx;\r\n  color: #000000;\r\n  font-weight: 500;\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n.members-avatars {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.member-avatar-wrapper {\r\n  position: relative;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.member-avatar {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  border-radius: 30rpx;\r\n  border: 2rpx solid #ffffff;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.team-leader-badge {\r\n  position: absolute;\r\n  bottom: -10rpx;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  background-color: #ff9500;\r\n  color: #ffffff;\r\n  font-size: 18rpx;\r\n  padding: 2rpx 8rpx;\r\n  border-radius: 10rpx;\r\n  white-space: nowrap;\r\n}\r\n\r\n.more-members {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  border-radius: 30rpx;\r\n  background-color: #f2f2f7;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.more-members text {\r\n  font-size: 20rpx;\r\n  color: #8e8e93;\r\n}\r\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/components/activity/GroupBuyCard.vue'\nwx.createComponent(Component)"], "names": ["computed"], "mappings": ";;;;;AAkEA,MAAM,eAAe,MAAW;AAUhC,MAAM,oBAAoB;;;;;;;;;;AAR1B,UAAM,QAAQ;AAWd,UAAM,kBAAkBA,cAAQ,SAAC,MAAM;AACrC,UAAI,CAAC,MAAM,KAAK,cAAc,CAAC,MAAM,KAAK;AAAe,eAAO;AAChE,aAAO,KAAK,MAAO,MAAM,KAAK,aAAa,MAAM,KAAK,gBAAiB,EAAE;AAAA,IAC3E,CAAC;AAGD,UAAM,gBAAgBA,cAAQ,SAAC,MAAM;AACnC,UAAI,CAAC,MAAM,KAAK,uBAAuB,CAAC,MAAM,KAAK;AAAW,eAAO;AACrE,aAAQ,MAAM,KAAK,sBAAsB,MAAM,KAAK,YAAa;AAAA,IACnE,CAAC;AAGD,UAAM,cAAcA,cAAQ,SAAC,MAAM;AACjC,UAAI,CAAC,MAAM,KAAK,uBAAuB,CAAC,MAAM,KAAK;AAAW,eAAO;AACrE,aAAO,MAAM,KAAK,YAAY,MAAM,KAAK;AAAA,IAC3C,CAAC;AAGD,UAAM,iBAAiBA,cAAQ,SAAC,MAAM;AACpC,UAAI,CAAC,MAAM,KAAK;AAAc,eAAO,CAAA;AACrC,aAAO,MAAM,KAAK,aAAa,MAAM,GAAG,iBAAiB;AAAA,IAC3D,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnGD,GAAG,gBAAgB,SAAS;"}