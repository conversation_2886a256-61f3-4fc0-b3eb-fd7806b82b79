<template>
  <view class="favorites-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      height: 'calc(var(--status-bar-height, 25px) + 62px)',
      width: '100%',
      zIndex: 100
    }">
      <view class="navbar-bg" :style="{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',
        boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
      }"></view>
      
      <view class="navbar-content" :style="{
        position: 'relative',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        height: '100%',
        padding: '0 30rpx',
        paddingTop: 'var(--status-bar-height, 25px)',
        boxSizing: 'border-box'
      }">
        <view class="back-btn" @click="goBack" :style="{
          width: '80rpx',
          height: '80rpx',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }">
          <image src="/static/images/tabbar/最新返回键.png" mode="aspectFit" :style="{
            width: '40rpx',
            height: '40rpx'
          }"></image>
        </view>
        
        <text class="navbar-title" :style="{
          fontSize: '36rpx',
          fontWeight: '600',
          color: '#FFFFFF'
        }">我的收藏</text>
        
        <!-- 占位元素，保持布局平衡 -->
        <view style="width: 80rpx;"></view>
      </view>
    </view>
    
    <!-- 内容区域 -->
    <view class="content-area" :style="{
      paddingTop: 'calc(var(--status-bar-height, 25px) + 62px)',
      paddingBottom: isEditMode ? '180rpx' : '0',
      minHeight: '100vh',
      boxSizing: 'border-box'
    }">
      <!-- 分类标签栏和编辑按钮 -->
      <view :style="{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '20rpx 30rpx',
        background: '#FFFFFF',
        marginBottom: '20rpx',
        boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
      }">
        <scroll-view 
          scroll-x 
          class="category-tabs" 
          :style="{
            whiteSpace: 'nowrap',
            flex: 1,
            overflow: 'hidden'
          }"
        >
          <view 
            v-for="(category, index) in categories" 
            :key="index"
            class="category-item"
            :class="{ active: currentCategory === index }"
            @click="switchCategory(index)"
            :style="{
              display: 'inline-block',
              padding: '10rpx 30rpx',
              marginRight: '20rpx',
              borderRadius: '30rpx',
              fontSize: '28rpx',
              color: currentCategory === index ? '#FFFFFF' : '#666666',
              background: currentCategory === index ? 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)' : '#F2F2F7',
              boxShadow: currentCategory === index ? '0 4px 10px rgba(255,59,105,0.2)' : 'none'
            }"
          >
            {{ category.name }}
          </view>
        </scroll-view>
        
        <!-- 编辑按钮移到这里 -->
        <view class="edit-btn" @click="toggleEditMode" :style="{
          marginLeft: '20rpx',
          padding: '10rpx 30rpx',
          borderRadius: '30rpx',
          fontSize: '28rpx',
          fontWeight: '500',
          background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',
          color: '#FFFFFF',
          boxShadow: '0 4px 10px rgba(255,59,105,0.2)'
        }">
          {{ isEditMode ? '完成' : '编辑' }}
        </view>
      </view>
      
      <!-- 收藏列表 -->
      <view class="favorites-list" :style="{
        padding: '0 20rpx'
      }">
        <view 
          v-for="(item, index) in filteredFavorites" 
          :key="index"
          class="favorite-item"
          :style="{
            background: '#FFFFFF',
            borderRadius: '20rpx',
            marginBottom: '20rpx',
            position: 'relative',
            overflow: 'hidden',
            boxShadow: '0 4px 10px rgba(0,0,0,0.05)'
          }"
        >
          <!-- 选择框 -->
          <view 
            v-if="isEditMode" 
            class="select-box" 
            @click.stop="toggleSelect(item)"
            :style="{
              position: 'absolute',
              top: '20rpx',
              left: '20rpx',
              width: '40rpx',
              height: '40rpx',
              borderRadius: '50%',
              border: item.selected ? '0' : '2rpx solid #CCCCCC',
              background: item.selected ? '#FF3B69' : '#FFFFFF',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: '10'
            }"
          >
            <svg v-if="item.selected" class="icon" viewBox="0 0 24 24" width="16" height="16">
              <path d="M5 12l5 5L20 7" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
          
          <!-- 内容区域 -->
          <view class="item-content" @click="viewDetail(item)" :style="{
            display: 'flex',
            padding: '20rpx'
          }">
            <image 
              :src="item.image" 
              mode="aspectFill" 
              :style="{
                width: '200rpx',
                height: '200rpx',
                borderRadius: '10rpx',
                marginRight: '20rpx'
              }"
            ></image>
            
            <view class="item-info" :style="{
              flex: '1',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'space-between'
            }">
              <view class="item-top">
                <text class="item-title" :style="{
                  fontSize: '28rpx',
                  fontWeight: '500',
                  color: '#333333',
                  marginBottom: '10rpx',
                  display: 'block',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  display: '-webkit-box',
                  '-webkit-line-clamp': '2',
                  '-webkit-box-orient': 'vertical',
                  lineHeight: '1.4'
                }">{{ item.title }}</text>
                
                <text class="item-shop" :style="{
                  fontSize: '24rpx',
                  color: '#999999',
                  marginBottom: '10rpx',
                  display: 'block'
                }">{{ item.shop }}</text>
              </view>
              
              <view class="item-bottom" :style="{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'flex-end'
              }">
                <view class="price-info">
                  <view class="current-price" :style="{
                    display: 'flex',
                    alignItems: 'baseline'
                  }">
                    <text :style="{
                      fontSize: '24rpx',
                      color: '#FF3B69',
                      marginRight: '5rpx'
                    }">¥</text>
                    <text :style="{
                      fontSize: '32rpx',
                      fontWeight: '600',
                      color: '#FF3B69'
                    }">{{ item.price }}</text>
                  </view>
                  
                  <text v-if="item.originalPrice" class="original-price" :style="{
                    fontSize: '24rpx',
                    color: '#999999',
                    textDecoration: 'line-through'
                  }">¥{{ item.originalPrice }}</text>
                </view>
                
                <view class="item-tag" :style="{
                  padding: '6rpx 16rpx',
                  borderRadius: '20rpx',
                  background: getTagBackground(item.type),
                  color: '#FFFFFF',
                  fontSize: '22rpx',
                  fontWeight: '500'
                }">
                  {{ getTagText(item.type) }}
                </view>
              </view>
            </view>
          </view>
          
          <!-- 收藏时间 -->
          <view class="favorite-time" :style="{
            padding: '10rpx 20rpx',
            borderTop: '1rpx solid #F2F2F7',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }">
            <text :style="{
              fontSize: '24rpx',
              color: '#999999'
            }">收藏于 {{ item.favoriteTime }}</text>
            
            <view class="action-btn" @click.stop="removeFavorite(item)" :style="{
              display: 'flex',
              alignItems: 'center'
            }">
              <svg class="icon" viewBox="0 0 24 24" width="16" height="16" :style="{ marginRight: '5rpx' }">
                <path d="M19 21l-7-5-7 5V5a2 2 0 012-2h10a2 2 0 012 2v16z" fill="#FF3B69" stroke="#FF3B69" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
              <text :style="{
                fontSize: '24rpx',
                color: '#FF3B69'
              }">取消收藏</text>
            </view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view v-if="filteredFavorites.length === 0" class="empty-state" :style="{
          padding: '100rpx 0',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center'
        }">
          <image src="/static/images/empty/empty-favorites.png" mode="aspectFit" :style="{
            width: '200rpx',
            height: '200rpx',
            marginBottom: '20rpx'
          }"></image>
          <text :style="{
            fontSize: '28rpx',
            color: '#999999',
            marginBottom: '30rpx'
          }">暂无收藏内容</text>
          
          <view class="action-btn" @click="goExplore" :style="{
            padding: '16rpx 40rpx',
            borderRadius: '35px',
            background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',
            color: '#FFFFFF',
            fontSize: '28rpx',
            boxShadow: '0 5px 15px rgba(255,59,105,0.3)'
          }">
            去探索
          </view>
        </view>
        
        <!-- 加载更多 -->
        <view v-if="filteredFavorites.length > 0 && !noMore" class="load-more" :style="{
          textAlign: 'center',
          padding: '30rpx 0'
        }">
          <text v-if="loading" :style="{
            fontSize: '26rpx',
            color: '#999999'
          }">加载中...</text>
          <text v-else @click="loadMore" :style="{
            fontSize: '26rpx',
            color: '#FF3B69'
          }">加载更多</text>
        </view>
        
        <!-- 没有更多数据 -->
        <view v-if="filteredFavorites.length > 0 && noMore" class="no-more" :style="{
          textAlign: 'center',
          padding: '30rpx 0'
        }">
          <text :style="{
            fontSize: '26rpx',
            color: '#999999'
          }">没有更多收藏了</text>
        </view>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view v-if="isEditMode" class="bottom-action-bar" :style="{
      position: 'fixed',
      bottom: 0,
      left: 0,
      right: 0,
      height: '120rpx',
      background: '#FFFFFF',
      boxShadow: '0 -2px 10px rgba(0,0,0,0.05)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: '0 30rpx',
      paddingBottom: 'env(safe-area-inset-bottom)',
      zIndex: 100
    }">
      <view class="select-all-btn" @click="toggleSelectAll" :style="{
        display: 'flex',
        alignItems: 'center'
      }">
        <view class="select-box" :style="{
          width: '40rpx',
          height: '40rpx',
          borderRadius: '50%',
          border: isAllSelected ? '0' : '2rpx solid #CCCCCC',
          background: isAllSelected ? '#FF3B69' : '#FFFFFF',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginRight: '10rpx'
        }">
          <svg v-if="isAllSelected" class="icon" viewBox="0 0 24 24" width="16" height="16">
            <path d="M5 12l5 5L20 7" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        <text :style="{
          fontSize: '28rpx',
          color: '#333333'
        }">全选</text>
      </view>
      
      <view 
        class="delete-btn" 
        @click="batchDelete"
        :style="{
          padding: '16rpx 40rpx',
          borderRadius: '35px',
          background: selectedCount > 0 ? 'linear-gradient(135deg, #FF3B30 0%, #FF5E3A 100%)' : '#CCCCCC',
          color: '#FFFFFF',
          fontSize: '28rpx',
          boxShadow: selectedCount > 0 ? '0 5px 15px rgba(255,59,48,0.3)' : 'none'
        }"
      >
        删除({{ selectedCount }})
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';

// 编辑模式
const isEditMode = ref(false);

// 分类数据
const categories = ref([
  { id: 0, name: '全部' },
  { id: 1, name: '活动' },
  { id: 2, name: '商品' },
  { id: 3, name: '优惠券' },
  { id: 4, name: '拼团' },
  { id: 5, name: '秒杀' }
]);
const currentCategory = ref(0);

// 加载状态
const loading = ref(false);
const noMore = ref(false);

// 收藏数据
const favorites = ref([
  {
    id: 1,
    title: 'Apple iPhone 14 Pro Max 256GB 暗夜紫 移动联通电信5G双卡双待手机',
    image: 'https://via.placeholder.com/200',
    shop: 'Apple官方旗舰店',
    price: '8999.00',
    originalPrice: '9999.00',
    type: 'product',
    favoriteTime: '2023-05-15 14:30',
    selected: false
  },
  {
    id: 2,
    title: '618年中大促全场低至5折起',
    image: 'https://via.placeholder.com/200',
    shop: '京东自营',
    price: '0.00',
    originalPrice: '',
    type: 'activity',
    favoriteTime: '2023-05-14 09:15',
    selected: false
  },
  {
    id: 3,
    title: '满300减50全场优惠券',
    image: 'https://via.placeholder.com/200',
    shop: '京东自营',
    price: '50.00',
    originalPrice: '',
    type: 'coupon',
    favoriteTime: '2023-05-10 16:42',
    selected: false
  },
  {
    id: 4,
    title: '小米12S Ultra 12GB+256GB 丹青黑 骁龙8+旗舰处理器 徕卡专业光学镜头',
    image: 'https://via.placeholder.com/200',
    shop: '小米官方旗舰店',
    price: '5999.00',
    originalPrice: '6999.00',
    type: 'product',
    favoriteTime: '2023-05-08 11:23',
    selected: false
  },
  {
    id: 5,
    title: '3人团：小米空气净化器',
    image: 'https://via.placeholder.com/200',
    shop: '小米官方旗舰店',
    price: '699.00',
    originalPrice: '999.00',
    type: 'group',
    favoriteTime: '2023-05-07 18:05',
    selected: false
  },
  {
    id: 6,
    title: '限时秒杀：iPhone 14 Pro',
    image: 'https://via.placeholder.com/200',
    shop: 'Apple授权专卖店',
    price: '6999.00',
    originalPrice: '8999.00',
    type: 'flash',
    favoriteTime: '2023-05-05 10:30',
    selected: false
  }
]);

// 过滤后的收藏列表
const filteredFavorites = computed(() => {
  if (currentCategory.value === 0) {
    return favorites.value;
  } else {
    const categoryMap = {
      1: 'activity',
      2: 'product',
      3: 'coupon',
      4: 'group',
      5: 'flash'
    };
    return favorites.value.filter(item => item.type === categoryMap[currentCategory.value]);
  }
});

// 选中的数量
const selectedCount = computed(() => {
  return favorites.value.filter(item => item.selected).length;
});

// 是否全选
const isAllSelected = computed(() => {
  return favorites.value.length > 0 && favorites.value.every(item => item.selected);
});

// 返回上一页
function goBack() {
  uni.navigateBack();
}

// 切换编辑模式
function toggleEditMode() {
  isEditMode.value = !isEditMode.value;
  
  // 退出编辑模式时，取消所有选择
  if (!isEditMode.value) {
    favorites.value.forEach(item => {
      item.selected = false;
    });
  }
}

// 切换分类
function switchCategory(index) {
  currentCategory.value = index;
}

// 查看详情
function viewDetail(item) {
  // 如果在编辑模式下，点击项目会选中/取消选中
  if (isEditMode.value) {
    toggleSelect(item);
    return;
  }
  
  // 根据不同类型跳转到不同页面
  let url = '';
  
  switch(item.type) {
    case 'product':
      url = `/subPackages/activity-showcase/pages/detail/index?id=${item.id}&type=product`;
      break;
    case 'activity':
      url = `/subPackages/activity-showcase/pages/detail/index?id=${item.id}&type=activity`;
      break;
    case 'coupon':
      url = `/subPackages/activity-showcase/pages/coupon/detail?id=${item.id}`;
      break;
    case 'group':
      url = `/subPackages/activity-showcase/pages/group-buy/detail?id=${item.id}`;
      break;
    case 'flash':
      url = `/subPackages/activity-showcase/pages/flash-sale/detail?id=${item.id}`;
      break;
    default:
      url = `/subPackages/activity-showcase/pages/detail/index?id=${item.id}`;
  }
  
  uni.navigateTo({ url });
}

// 切换选中状态
function toggleSelect(item) {
  item.selected = !item.selected;
}

// 切换全选
function toggleSelectAll() {
  const newState = !isAllSelected.value;
  favorites.value.forEach(item => {
    item.selected = newState;
  });
}

// 批量删除
function batchDelete() {
  if (selectedCount.value === 0) {
    uni.showToast({
      title: '请先选择要删除的收藏',
      icon: 'none'
    });
    return;
  }
  
  uni.showModal({
    title: '删除收藏',
    content: `确定要删除选中的${selectedCount.value}个收藏吗？`,
    success: (res) => {
      if (res.confirm) {
        // 删除选中的收藏
        favorites.value = favorites.value.filter(item => !item.selected);
        
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        });
        
        // 如果删除后没有收藏了，退出编辑模式
        if (favorites.value.length === 0) {
          isEditMode.value = false;
        }
      }
    }
  });
}

// 取消收藏
function removeFavorite(item) {
  uni.showModal({
    title: '取消收藏',
    content: '确定要取消收藏该内容吗？',
    success: (res) => {
      if (res.confirm) {
        // 从收藏列表中移除
        const index = favorites.value.findIndex(fav => fav.id === item.id);
        if (index !== -1) {
          favorites.value.splice(index, 1);
        }
        
        uni.showToast({
          title: '已取消收藏',
          icon: 'success'
        });
      }
    }
  });
}

// 前往探索
function goExplore() {
  uni.switchTab({
    url: '/pages/index/index'
  });
}

// 加载更多
function loadMore() {
  if (loading.value || noMore.value) return;
  
  loading.value = true;
  
  // 模拟加载更多数据
  setTimeout(() => {
    // 这里应该调用API获取更多数据
    // 模拟没有更多数据
    noMore.value = true;
    loading.value = false;
  }, 1500);
}

// 获取标签背景色
function getTagBackground(type) {
  switch (type) {
    case 'product':
      return 'linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%)';
    case 'activity':
      return 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)';
    case 'coupon':
      return 'linear-gradient(135deg, #FF3B30 0%, #FF5E3A 100%)';
    case 'group':
      return 'linear-gradient(135deg, #34C759 0%, #30D158 100%)';
    case 'flash':
      return 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)';
    default:
      return 'linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)';
  }
}

// 获取标签文本
function getTagText(type) {
  switch (type) {
    case 'product':
      return '商品';
    case 'activity':
      return '活动';
    case 'coupon':
      return '优惠券';
    case 'group':
      return '拼团';
    case 'flash':
      return '秒杀';
    default:
      return '其他';
  }
}
</script>

<style scoped>
.favorites-container {
  min-height: 100vh;
  background-color: #F8F8F8;
}

.back-btn:active, .edit-btn:active, .category-item:active, .action-btn:active {
  opacity: 0.8;
}

.favorite-item {
  transition: transform 0.3s ease;
}

.favorite-item:active {
  transform: scale(0.98);
}
</style> 