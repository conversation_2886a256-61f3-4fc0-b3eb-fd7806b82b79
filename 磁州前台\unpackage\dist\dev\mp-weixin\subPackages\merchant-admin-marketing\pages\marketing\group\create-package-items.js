"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      packageItems: [],
      showModal: false,
      isEditing: false,
      editIndex: -1,
      currentItem: {
        name: "",
        quantity: 1,
        unit: "份",
        price: "",
        description: ""
      },
      groupPrice: "0.00"
    };
  },
  onLoad() {
    try {
      const savedItems = common_vendor.index.getStorageSync("packageItems");
      if (savedItems) {
        this.packageItems = JSON.parse(savedItems);
      }
      const priceInfo = common_vendor.index.getStorageSync("packagePriceInfo");
      if (priceInfo) {
        const parsedPriceInfo = JSON.parse(priceInfo);
        this.groupPrice = parsedPriceInfo.groupPrice || "0.00";
      }
    } catch (e) {
      common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/group/create-package-items.vue:181", "读取本地存储失败:", e);
    }
  },
  methods: {
    goBack() {
      this.saveData();
      common_vendor.index.navigateBack();
    },
    nextStep() {
      if (this.packageItems.length === 0) {
        common_vendor.index.showToast({
          title: "请至少添加一个套餐项",
          icon: "none"
        });
        return;
      }
      this.saveData();
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-confirm"
      });
    },
    saveData() {
      try {
        common_vendor.index.setStorageSync("packageItems", JSON.stringify(this.packageItems));
      } catch (e) {
        common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/group/create-package-items.vue:211", "保存数据失败:", e);
      }
    },
    calculateTotalValue() {
      let total = 0;
      this.packageItems.forEach((item) => {
        total += parseFloat(item.price) * parseFloat(item.quantity);
      });
      return total.toFixed(2);
    },
    calculateDiscount() {
      const totalValue = parseFloat(this.calculateTotalValue());
      if (totalValue <= 0)
        return "10.0";
      const groupPrice = parseFloat(this.groupPrice);
      const discount = groupPrice / totalValue * 10;
      return discount.toFixed(1);
    },
    showAddItemModal() {
      this.isEditing = false;
      this.editIndex = -1;
      this.currentItem = {
        name: "",
        quantity: 1,
        unit: "份",
        price: "",
        description: ""
      };
      this.showModal = true;
    },
    editItem(index) {
      this.isEditing = true;
      this.editIndex = index;
      this.currentItem = JSON.parse(JSON.stringify(this.packageItems[index]));
      this.showModal = true;
    },
    deleteItem(index) {
      common_vendor.index.showModal({
        title: "确认删除",
        content: "确定要删除这个套餐项吗？",
        success: (res) => {
          if (res.confirm) {
            this.packageItems.splice(index, 1);
          }
        }
      });
    },
    hideModal() {
      this.showModal = false;
    },
    confirmItem() {
      if (!this.currentItem.name) {
        common_vendor.index.showToast({
          title: "请输入套餐项名称",
          icon: "none"
        });
        return;
      }
      if (!this.currentItem.quantity || parseFloat(this.currentItem.quantity) <= 0) {
        common_vendor.index.showToast({
          title: "请输入有效数量",
          icon: "none"
        });
        return;
      }
      if (!this.currentItem.unit) {
        common_vendor.index.showToast({
          title: "请输入单位",
          icon: "none"
        });
        return;
      }
      if (!this.currentItem.price || parseFloat(this.currentItem.price) <= 0) {
        common_vendor.index.showToast({
          title: "请输入有效原价",
          icon: "none"
        });
        return;
      }
      if (this.isEditing) {
        this.packageItems[this.editIndex] = JSON.parse(JSON.stringify(this.currentItem));
      } else {
        this.packageItems.push(JSON.parse(JSON.stringify(this.currentItem)));
      }
      this.hideModal();
    },
    showHelp() {
      common_vendor.index.showToast({
        title: "帮助信息",
        icon: "none"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: common_vendor.f($data.packageItems, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(index + 1),
        b: common_vendor.o(($event) => $options.editItem(index), index),
        c: common_vendor.o(($event) => $options.deleteItem(index), index),
        d: common_vendor.t(item.name),
        e: common_vendor.t(item.quantity),
        f: common_vendor.t(item.unit),
        g: common_vendor.t(item.price),
        h: item.description
      }, item.description ? {
        i: common_vendor.t(item.description)
      } : {}, {
        j: index
      });
    }),
    d: common_vendor.o((...args) => $options.showAddItemModal && $options.showAddItemModal(...args)),
    e: common_vendor.t($data.packageItems.length),
    f: common_vendor.t($options.calculateTotalValue()),
    g: common_vendor.t($data.groupPrice),
    h: common_vendor.t($options.calculateDiscount()),
    i: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    j: common_vendor.o((...args) => $options.nextStep && $options.nextStep(...args)),
    k: $data.showModal
  }, $data.showModal ? {
    l: common_vendor.o((...args) => $options.hideModal && $options.hideModal(...args)),
    m: common_vendor.t($data.isEditing ? "编辑套餐项" : "添加套餐项"),
    n: common_vendor.o((...args) => $options.hideModal && $options.hideModal(...args)),
    o: $data.currentItem.name,
    p: common_vendor.o(($event) => $data.currentItem.name = $event.detail.value),
    q: $data.currentItem.quantity,
    r: common_vendor.o(($event) => $data.currentItem.quantity = $event.detail.value),
    s: $data.currentItem.unit,
    t: common_vendor.o(($event) => $data.currentItem.unit = $event.detail.value),
    v: $data.currentItem.price,
    w: common_vendor.o(($event) => $data.currentItem.price = $event.detail.value),
    x: $data.currentItem.description,
    y: common_vendor.o(($event) => $data.currentItem.description = $event.detail.value),
    z: common_vendor.o((...args) => $options.hideModal && $options.hideModal(...args)),
    A: common_vendor.o((...args) => $options.confirmItem && $options.confirmItem(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-items.js.map
