#!/usr/bin/env node
"use strict";module.exports=function(e){var t={};function r(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,r),o.l=!0,o.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)r.d(n,o,function(t){return e[t]}.bind(null,o));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=84)}([function(e,t){e.exports=require("path")},function(e,t){e.exports=require("fs")},function(e,t,r){var n=r(65),o="function"==typeof Symbol&&"symbol"==typeof Symbol("foo"),i=Object.prototype.toString,a=Array.prototype.concat,s=Object.defineProperty,c=s&&function(){var e={};try{for(var t in s(e,"x",{enumerable:!1,value:e}),e)return!1;return e.x===e}catch(e){return!1}}(),l=function(e,t,r,n){var o;t in e&&("function"!=typeof(o=n)||"[object Function]"!==i.call(o)||!n())||(c?s(e,t,{configurable:!0,enumerable:!1,value:r,writable:!0}):e[t]=r)},u=function(e,t){var r=arguments.length>2?arguments[2]:{},i=n(t);o&&(i=a.call(i,Object.getOwnPropertySymbols(t)));for(var s=0;s<i.length;s+=1)l(e,i[s],t[i[s]],r[i[s]])};u.supportsDescriptors=!!c,e.exports=u},function(e,t,r){var n=r(4);e.exports=n.call(Function.call,Object.prototype.hasOwnProperty)},function(e,t,r){var n=r(67);e.exports=Function.prototype.bind||n},function(e,t){e.exports=require("os")},function(e,t,r){const n=r(0),o=r(1),i=r(12),a=r(50),s=r(7),c=r(54),l=e=>new Promise(t=>{i.exec(e,{stdio:[0,"pipe","ignore"]},(e,r)=>{t((e?"":r.toString()||"").trim())})}),u=function(e){const t=Object.values(Array.prototype.slice.call(arguments).slice(1));(process.env.ENVINFO_DEBUG||"").toLowerCase()===e&&console.log(e,JSON.stringify(t))},p=e=>new Promise(t=>{o.stat(e,r=>t(r?null:e))}),f=e=>new Promise(t=>{e||t(null),o.readFile(e,"utf8",(e,r)=>t(r||null))}),h=e=>p(e).then(f).then(e=>e?JSON.parse(e):null),d=/\d+\.[\d+|.]+/g,m=e=>{u("trace","findDarwinApplication",e);const t=`mdfind "kMDItemCFBundleIdentifier=='${e}'"`;return u("trace",t),l(t).then(e=>e.replace(/(\s)/g,"\\ "))},y=(e,t)=>{var r=(t||["CFBundleShortVersionString"]).map(function(e){return"-c Print:"+e});return["/usr/libexec/PlistBuddy"].concat(r).concat([e]).join(" ")},g=(e,t)=>{const r=[];let n=null;for(;null!==(n=e.exec(t));)r.push(n);return r};e.exports={run:l,log:u,fileExists:p,readFile:f,requireJson:h,versionRegex:d,findDarwinApplication:m,generatePlistBuddyCommand:y,matchAll:g,parseSDKManagerOutput:e=>{const t=e.split("Available")[0];return{apiLevels:g(c.androidAPILevels,t).map(e=>e[1]),buildTools:g(c.androidBuildTools,t).map(e=>e[1]),systemImages:g(c.androidSystemImages,t).map(e=>e[1].split("|").map(e=>e.trim())).map(e=>e[0].split(";")[0]+" | "+e[2].split(" System Image")[0])}},isObject:e=>"object"==typeof e&&!Array.isArray(e),noop:e=>e,pipe:e=>t=>e.reduce((e,t)=>t(e),t),browserBundleIdentifiers:{Chrome:"com.google.Chrome","Chrome Canary":"com.google.Chrome.canary",Firefox:"org.mozilla.firefox","Firefox Developer Edition":"org.mozilla.firefoxdeveloperedition","Firefox Nightly":"org.mozilla.nightly",Safari:"com.apple.Safari","Safari Technology Preview":"com.apple.SafariTechnologyPreview"},ideBundleIdentifiers:{Atom:"com.github.atom",IntelliJ:"com.jetbrains.intellij",PhpStorm:"com.jetbrains.PhpStorm","Sublime Text":"com.sublimetext.3",WebStorm:"com.jetbrains.WebStorm"},runSync:e=>(i.execSync(e,{stdio:[0,"pipe","ignore"]}).toString()||"").trim(),which:e=>new Promise(t=>a(e,(e,r)=>t(r))),getDarwinApplicationVersion:e=>{var t;return u("trace","getDarwinApplicationVersion",e),t="darwin"!==process.platform?"N/A":m(e).then(e=>l(y(n.join(e,"Contents","Info.plist"),["CFBundleShortVersionString"]))),Promise.resolve(t)},uniq:e=>Array.from(new Set(e)),toReadableBytes:e=>{const t=Math.floor(Math.log(e)/Math.log(1024));return e?(e/Math.pow(1024,t)).toFixed(2)+" "+["B","KB","MB","GB","TB","PB"][t]:"0 Bytes"},omit:(e,t)=>Object.keys(e).filter(e=>t.indexOf(e)<0).reduce((t,r)=>Object.assign(t,{[r]:e[r]}),{}),pick:(e,t)=>Object.keys(e).filter(e=>t.indexOf(e)>=0).reduce((t,r)=>Object.assign(t,{[r]:e[r]}),{}),getPackageJsonByName:e=>h(n.join(process.cwd(),"node_modules",e,"package.json")),getPackageJsonByPath:e=>h(n.join(process.cwd(),e)),getPackageJsonByFullPath:e=>(u("trace","getPackageJsonByFullPath",e),h(e)),getAllPackageJsonPaths:e=>(u("trace","getAllPackageJsonPaths",e),new Promise(t=>{const r=(e,r)=>t(r||[]);return s(e?n.join("node_modules",e,"package.json"):n.join("node_modules","**","package.json"),r)})),sortObject:e=>Object.keys(e).sort().reduce((t,r)=>(t[r]=e[r],t),{}),findVersion:(e,t,r)=>{const n=r||0,o=t||d,i=e.match(o);return i?i[n]:e},condensePath:e=>(e||"").replace(process.env.HOME,"~"),determineFound:(e,t,r)=>(u("trace","determineFound",e,t,r),"N/A"===t||"N/A"===t&&"N/A"===r?Promise.resolve([e,"N/A"]):t?r?Promise.resolve([e,t,r]):Promise.resolve([e,t]):Promise.resolve([e,"Not Found"]))}},function(e,t,r){e.exports=b;var n=r(1),o=r(13),i=r(8),a=(i.Minimatch,r(45)),s=r(47).EventEmitter,c=r(0),l=r(14),u=r(10),p=r(48),f=r(15),h=(f.alphasort,f.alphasorti,f.setopts),d=f.ownProp,m=r(49),y=(r(9),f.childrenIgnored),g=f.isIgnored,v=r(17);function b(e,t,r){if("function"==typeof t&&(r=t,t={}),t||(t={}),t.sync){if(r)throw new TypeError("callback provided to sync glob");return p(e,t)}return new S(e,t,r)}b.sync=p;var w=b.GlobSync=p.GlobSync;function S(e,t,r){if("function"==typeof t&&(r=t,t=null),t&&t.sync){if(r)throw new TypeError("callback provided to sync glob");return new w(e,t)}if(!(this instanceof S))return new S(e,t,r);h(this,e,t),this._didRealPath=!1;var n=this.minimatch.set.length;this.matches=new Array(n),"function"==typeof r&&(r=v(r),this.on("error",r),this.on("end",function(e){r(null,e)}));var o=this;if(this._processing=0,this._emitQueue=[],this._processQueue=[],this.paused=!1,this.noprocess)return this;if(0===n)return s();for(var i=!0,a=0;a<n;a++)this._process(this.minimatch.set[a],a,!1,s);function s(){--o._processing,o._processing<=0&&(i?process.nextTick(function(){o._finish()}):o._finish())}i=!1}b.glob=b,b.hasMagic=function(e,t){var r=function(e,t){if(null===t||"object"!=typeof t)return e;for(var r=Object.keys(t),n=r.length;n--;)e[r[n]]=t[r[n]];return e}({},t);r.noprocess=!0;var n=new S(e,r).minimatch.set;if(!e)return!1;if(n.length>1)return!0;for(var o=0;o<n[0].length;o++)if("string"!=typeof n[0][o])return!0;return!1},b.Glob=S,a(S,s),S.prototype._finish=function(){if(l(this instanceof S),!this.aborted){if(this.realpath&&!this._didRealpath)return this._realpath();f.finish(this),this.emit("end",this.found)}},S.prototype._realpath=function(){if(!this._didRealpath){this._didRealpath=!0;var e=this.matches.length;if(0===e)return this._finish();for(var t=this,r=0;r<this.matches.length;r++)this._realpathSet(r,n)}function n(){0==--e&&t._finish()}},S.prototype._realpathSet=function(e,t){var r=this.matches[e];if(!r)return t();var n=Object.keys(r),i=this,a=n.length;if(0===a)return t();var s=this.matches[e]=Object.create(null);n.forEach(function(r,n){r=i._makeAbs(r),o.realpath(r,i.realpathCache,function(n,o){n?"stat"===n.syscall?s[r]=!0:i.emit("error",n):s[o]=!0,0==--a&&(i.matches[e]=s,t())})})},S.prototype._mark=function(e){return f.mark(this,e)},S.prototype._makeAbs=function(e){return f.makeAbs(this,e)},S.prototype.abort=function(){this.aborted=!0,this.emit("abort")},S.prototype.pause=function(){this.paused||(this.paused=!0,this.emit("pause"))},S.prototype.resume=function(){if(this.paused){if(this.emit("resume"),this.paused=!1,this._emitQueue.length){var e=this._emitQueue.slice(0);this._emitQueue.length=0;for(var t=0;t<e.length;t++){var r=e[t];this._emitMatch(r[0],r[1])}}if(this._processQueue.length){var n=this._processQueue.slice(0);this._processQueue.length=0;for(t=0;t<n.length;t++){var o=n[t];this._processing--,this._process(o[0],o[1],o[2],o[3])}}}},S.prototype._process=function(e,t,r,n){if(l(this instanceof S),l("function"==typeof n),!this.aborted)if(this._processing++,this.paused)this._processQueue.push([e,t,r,n]);else{for(var o,a=0;"string"==typeof e[a];)a++;switch(a){case e.length:return void this._processSimple(e.join("/"),t,n);case 0:o=null;break;default:o=e.slice(0,a).join("/")}var s,c=e.slice(a);null===o?s=".":u(o)||u(e.join("/"))?(o&&u(o)||(o="/"+o),s=o):s=o;var p=this._makeAbs(s);if(y(this,s))return n();c[0]===i.GLOBSTAR?this._processGlobStar(o,s,p,c,t,r,n):this._processReaddir(o,s,p,c,t,r,n)}},S.prototype._processReaddir=function(e,t,r,n,o,i,a){var s=this;this._readdir(r,i,function(c,l){return s._processReaddir2(e,t,r,n,o,i,l,a)})},S.prototype._processReaddir2=function(e,t,r,n,o,i,a,s){if(!a)return s();for(var l=n[0],u=!!this.minimatch.negate,p=l._glob,f=this.dot||"."===p.charAt(0),h=[],d=0;d<a.length;d++){if("."!==(y=a[d]).charAt(0)||f)(u&&!e?!y.match(l):y.match(l))&&h.push(y)}var m=h.length;if(0===m)return s();if(1===n.length&&!this.mark&&!this.stat){this.matches[o]||(this.matches[o]=Object.create(null));for(d=0;d<m;d++){var y=h[d];e&&(y="/"!==e?e+"/"+y:e+y),"/"!==y.charAt(0)||this.nomount||(y=c.join(this.root,y)),this._emitMatch(o,y)}return s()}n.shift();for(d=0;d<m;d++){y=h[d];e&&(y="/"!==e?e+"/"+y:e+y),this._process([y].concat(n),o,i,s)}s()},S.prototype._emitMatch=function(e,t){if(!this.aborted&&!g(this,t))if(this.paused)this._emitQueue.push([e,t]);else{var r=u(t)?t:this._makeAbs(t);if(this.mark&&(t=this._mark(t)),this.absolute&&(t=r),!this.matches[e][t]){if(this.nodir){var n=this.cache[r];if("DIR"===n||Array.isArray(n))return}this.matches[e][t]=!0;var o=this.statCache[r];o&&this.emit("stat",t,o),this.emit("match",t)}}},S.prototype._readdirInGlobStar=function(e,t){if(!this.aborted){if(this.follow)return this._readdir(e,!1,t);var r=this,o=m("lstat\0"+e,function(n,o){if(n&&"ENOENT"===n.code)return t();var i=o&&o.isSymbolicLink();r.symlinks[e]=i,i||!o||o.isDirectory()?r._readdir(e,!1,t):(r.cache[e]="FILE",t())});o&&n.lstat(e,o)}},S.prototype._readdir=function(e,t,r){if(!this.aborted&&(r=m("readdir\0"+e+"\0"+t,r))){if(t&&!d(this.symlinks,e))return this._readdirInGlobStar(e,r);if(d(this.cache,e)){var o=this.cache[e];if(!o||"FILE"===o)return r();if(Array.isArray(o))return r(null,o)}n.readdir(e,function(e,t,r){return function(n,o){n?e._readdirError(t,n,r):e._readdirEntries(t,o,r)}}(this,e,r))}},S.prototype._readdirEntries=function(e,t,r){if(!this.aborted){if(!this.mark&&!this.stat)for(var n=0;n<t.length;n++){var o=t[n];o="/"===e?e+o:e+"/"+o,this.cache[o]=!0}return this.cache[e]=t,r(null,t)}},S.prototype._readdirError=function(e,t,r){if(!this.aborted){switch(t.code){case"ENOTSUP":case"ENOTDIR":var n=this._makeAbs(e);if(this.cache[n]="FILE",n===this.cwdAbs){var o=new Error(t.code+" invalid cwd "+this.cwd);o.path=this.cwd,o.code=t.code,this.emit("error",o),this.abort()}break;case"ENOENT":case"ELOOP":case"ENAMETOOLONG":case"UNKNOWN":this.cache[this._makeAbs(e)]=!1;break;default:this.cache[this._makeAbs(e)]=!1,this.strict&&(this.emit("error",t),this.abort()),this.silent||console.error("glob error",t)}return r()}},S.prototype._processGlobStar=function(e,t,r,n,o,i,a){var s=this;this._readdir(r,i,function(c,l){s._processGlobStar2(e,t,r,n,o,i,l,a)})},S.prototype._processGlobStar2=function(e,t,r,n,o,i,a,s){if(!a)return s();var c=n.slice(1),l=e?[e]:[],u=l.concat(c);this._process(u,o,!1,s);var p=this.symlinks[r],f=a.length;if(p&&i)return s();for(var h=0;h<f;h++){if("."!==a[h].charAt(0)||this.dot){var d=l.concat(a[h],c);this._process(d,o,!0,s);var m=l.concat(a[h],n);this._process(m,o,!0,s)}}s()},S.prototype._processSimple=function(e,t,r){var n=this;this._stat(e,function(o,i){n._processSimple2(e,t,o,i,r)})},S.prototype._processSimple2=function(e,t,r,n,o){if(this.matches[t]||(this.matches[t]=Object.create(null)),!n)return o();if(e&&u(e)&&!this.nomount){var i=/[\/\\]$/.test(e);"/"===e.charAt(0)?e=c.join(this.root,e):(e=c.resolve(this.root,e),i&&(e+="/"))}"win32"===process.platform&&(e=e.replace(/\\/g,"/")),this._emitMatch(t,e),o()},S.prototype._stat=function(e,t){var r=this._makeAbs(e),o="/"===e.slice(-1);if(e.length>this.maxLength)return t();if(!this.stat&&d(this.cache,r)){var i=this.cache[r];if(Array.isArray(i)&&(i="DIR"),!o||"DIR"===i)return t(null,i);if(o&&"FILE"===i)return t()}var a=this.statCache[r];if(void 0!==a){if(!1===a)return t(null,a);var s=a.isDirectory()?"DIR":"FILE";return o&&"FILE"===s?t():t(null,s,a)}var c=this,l=m("stat\0"+r,function(o,i){if(i&&i.isSymbolicLink())return n.stat(r,function(n,o){n?c._stat2(e,r,null,i,t):c._stat2(e,r,n,o,t)});c._stat2(e,r,o,i,t)});l&&n.lstat(r,l)},S.prototype._stat2=function(e,t,r,n,o){if(r&&("ENOENT"===r.code||"ENOTDIR"===r.code))return this.statCache[t]=!1,o();var i="/"===e.slice(-1);if(this.statCache[t]=n,"/"===t.slice(-1)&&n&&!n.isDirectory())return o(null,!1,n);var a=!0;return n&&(a=n.isDirectory()?"DIR":"FILE"),this.cache[t]=this.cache[t]||a,i&&"FILE"===a?o():o(null,a,n)}},function(e,t,r){e.exports=d,d.Minimatch=m;var n={sep:"/"};try{n=r(0)}catch(e){}var o=d.GLOBSTAR=m.GLOBSTAR={},i=r(42),a={"!":{open:"(?:(?!(?:",close:"))[^/]*?)"},"?":{open:"(?:",close:")?"},"+":{open:"(?:",close:")+"},"*":{open:"(?:",close:")*"},"@":{open:"(?:",close:")"}},s="[^/]",c=s+"*?",l="(?:(?!(?:\\/|^)(?:\\.{1,2})($|\\/)).)*?",u="(?:(?!(?:\\/|^)\\.).)*?",p="().*{}+?[]^$\\!".split("").reduce(function(e,t){return e[t]=!0,e},{});var f=/\/+/;function h(e,t){e=e||{},t=t||{};var r={};return Object.keys(t).forEach(function(e){r[e]=t[e]}),Object.keys(e).forEach(function(t){r[t]=e[t]}),r}function d(e,t,r){if("string"!=typeof t)throw new TypeError("glob pattern string required");return r||(r={}),!(!r.nocomment&&"#"===t.charAt(0))&&(""===t.trim()?""===e:new m(t,r).match(e))}function m(e,t){if(!(this instanceof m))return new m(e,t);if("string"!=typeof e)throw new TypeError("glob pattern string required");t||(t={}),e=e.trim(),"/"!==n.sep&&(e=e.split(n.sep).join("/")),this.options=t,this.set=[],this.pattern=e,this.regexp=null,this.negate=!1,this.comment=!1,this.empty=!1,this.make()}function y(e,t){if(t||(t=this instanceof m?this.options:{}),void 0===(e=void 0===e?this.pattern:e))throw new TypeError("undefined pattern");return t.nobrace||!e.match(/\{.*\}/)?[e]:i(e)}d.filter=function(e,t){return t=t||{},function(r,n,o){return d(r,e,t)}},d.defaults=function(e){if(!e||!Object.keys(e).length)return d;var t=d,r=function(r,n,o){return t.minimatch(r,n,h(e,o))};return r.Minimatch=function(r,n){return new t.Minimatch(r,h(e,n))},r},m.defaults=function(e){return e&&Object.keys(e).length?d.defaults(e).Minimatch:m},m.prototype.debug=function(){},m.prototype.make=function(){if(this._made)return;var e=this.pattern,t=this.options;if(!t.nocomment&&"#"===e.charAt(0))return void(this.comment=!0);if(!e)return void(this.empty=!0);this.parseNegate();var r=this.globSet=this.braceExpand();t.debug&&(this.debug=console.error);this.debug(this.pattern,r),r=this.globParts=r.map(function(e){return e.split(f)}),this.debug(this.pattern,r),r=r.map(function(e,t,r){return e.map(this.parse,this)},this),this.debug(this.pattern,r),r=r.filter(function(e){return-1===e.indexOf(!1)}),this.debug(this.pattern,r),this.set=r},m.prototype.parseNegate=function(){var e=this.pattern,t=!1,r=this.options,n=0;if(r.nonegate)return;for(var o=0,i=e.length;o<i&&"!"===e.charAt(o);o++)t=!t,n++;n&&(this.pattern=e.substr(n));this.negate=t},d.braceExpand=function(e,t){return y(e,t)},m.prototype.braceExpand=y,m.prototype.parse=function(e,t){if(e.length>65536)throw new TypeError("pattern is too long");var r=this.options;if(!r.noglobstar&&"**"===e)return o;if(""===e)return"";var n,i="",l=!!r.nocase,u=!1,f=[],h=[],d=!1,m=-1,y=-1,v="."===e.charAt(0)?"":r.dot?"(?!(?:^|\\/)\\.{1,2}(?:$|\\/))":"(?!\\.)",b=this;function w(){if(n){switch(n){case"*":i+=c,l=!0;break;case"?":i+=s,l=!0;break;default:i+="\\"+n}b.debug("clearStateChar %j %j",n,i),n=!1}}for(var S,P=0,j=e.length;P<j&&(S=e.charAt(P));P++)if(this.debug("%s\t%s %s %j",e,P,i,S),u&&p[S])i+="\\"+S,u=!1;else switch(S){case"/":return!1;case"\\":w(),u=!0;continue;case"?":case"*":case"+":case"@":case"!":if(this.debug("%s\t%s %s %j <-- stateChar",e,P,i,S),d){this.debug("  in class"),"!"===S&&P===y+1&&(S="^"),i+=S;continue}b.debug("call clearStateChar %j",n),w(),n=S,r.noext&&w();continue;case"(":if(d){i+="(";continue}if(!n){i+="\\(";continue}f.push({type:n,start:P-1,reStart:i.length,open:a[n].open,close:a[n].close}),i+="!"===n?"(?:(?!(?:":"(?:",this.debug("plType %j %j",n,i),n=!1;continue;case")":if(d||!f.length){i+="\\)";continue}w(),l=!0;var O=f.pop();i+=O.close,"!"===O.type&&h.push(O),O.reEnd=i.length;continue;case"|":if(d||!f.length||u){i+="\\|",u=!1;continue}w(),i+="|";continue;case"[":if(w(),d){i+="\\"+S;continue}d=!0,y=P,m=i.length,i+=S;continue;case"]":if(P===y+1||!d){i+="\\"+S,u=!1;continue}if(d){var I=e.substring(y+1,P);try{RegExp("["+I+"]")}catch(e){var A=this.parse(I,g);i=i.substr(0,m)+"\\["+A[0]+"\\]",l=l||A[1],d=!1;continue}}l=!0,d=!1,i+=S;continue;default:w(),u?u=!1:!p[S]||"^"===S&&d||(i+="\\"),i+=S}d&&(I=e.substr(y+1),A=this.parse(I,g),i=i.substr(0,m)+"\\["+A[0],l=l||A[1]);for(O=f.pop();O;O=f.pop()){var x=i.slice(O.reStart+O.open.length);this.debug("setting tail",i,O),x=x.replace(/((?:\\{2}){0,64})(\\?)\|/g,function(e,t,r){return r||(r="\\"),t+t+r+"|"}),this.debug("tail=%j\n   %s",x,x,O,i);var E="*"===O.type?c:"?"===O.type?s:"\\"+O.type;l=!0,i=i.slice(0,O.reStart)+E+"\\("+x}w(),u&&(i+="\\\\");var k=!1;switch(i.charAt(0)){case".":case"[":case"(":k=!0}for(var $=h.length-1;$>-1;$--){var T=h[$],F=i.slice(0,T.reStart),C=i.slice(T.reStart,T.reEnd-8),N=i.slice(T.reEnd-8,T.reEnd),_=i.slice(T.reEnd);N+=_;var D=F.split("(").length-1,M=_;for(P=0;P<D;P++)M=M.replace(/\)[+*?]?/,"");var B="";""===(_=M)&&t!==g&&(B="$");var V=F+C+_+B+N;i=V}""!==i&&l&&(i="(?=.)"+i);k&&(i=v+i);if(t===g)return[i,l];if(!l)return e.replace(/\\(.)/g,"$1");var R=r.nocase?"i":"";try{var G=new RegExp("^"+i+"$",R)}catch(e){return new RegExp("$.")}return G._glob=e,G._src=i,G};var g={};d.makeRe=function(e,t){return new m(e,t||{}).makeRe()},m.prototype.makeRe=function(){if(this.regexp||!1===this.regexp)return this.regexp;var e=this.set;if(!e.length)return this.regexp=!1,this.regexp;var t=this.options,r=t.noglobstar?c:t.dot?l:u,n=t.nocase?"i":"",i=e.map(function(e){return e.map(function(e){return e===o?r:"string"==typeof e?e.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&"):e._src}).join("\\/")}).join("|");i="^(?:"+i+")$",this.negate&&(i="^(?!"+i+").*$");try{this.regexp=new RegExp(i,n)}catch(e){this.regexp=!1}return this.regexp},d.match=function(e,t,r){var n=new m(t,r=r||{});return e=e.filter(function(e){return n.match(e)}),n.options.nonull&&!e.length&&e.push(t),e},m.prototype.match=function(e,t){if(this.debug("match",e,this.pattern),this.comment)return!1;if(this.empty)return""===e;if("/"===e&&t)return!0;var r=this.options;"/"!==n.sep&&(e=e.split(n.sep).join("/"));e=e.split(f),this.debug(this.pattern,"split",e);var o,i,a=this.set;for(this.debug(this.pattern,"set",a),i=e.length-1;i>=0&&!(o=e[i]);i--);for(i=0;i<a.length;i++){var s=a[i],c=e;r.matchBase&&1===s.length&&(c=[o]);var l=this.matchOne(c,s,t);if(l)return!!r.flipNegate||!this.negate}return!r.flipNegate&&this.negate},m.prototype.matchOne=function(e,t,r){var n=this.options;this.debug("matchOne",{this:this,file:e,pattern:t}),this.debug("matchOne",e.length,t.length);for(var i=0,a=0,s=e.length,c=t.length;i<s&&a<c;i++,a++){this.debug("matchOne loop");var l,u=t[a],p=e[i];if(this.debug(t,u,p),!1===u)return!1;if(u===o){this.debug("GLOBSTAR",[t,u,p]);var f=i,h=a+1;if(h===c){for(this.debug("** at the end");i<s;i++)if("."===e[i]||".."===e[i]||!n.dot&&"."===e[i].charAt(0))return!1;return!0}for(;f<s;){var d=e[f];if(this.debug("\nglobstar while",e,f,t,h,d),this.matchOne(e.slice(f),t.slice(h),r))return this.debug("globstar found match!",f,s,d),!0;if("."===d||".."===d||!n.dot&&"."===d.charAt(0)){this.debug("dot detected!",e,f,t,h);break}this.debug("globstar swallow a segment, and continue"),f++}return!(!r||(this.debug("\n>>> no match, partial?",e,f,t,h),f!==s))}if("string"==typeof u?(l=n.nocase?p.toLowerCase()===u.toLowerCase():p===u,this.debug("string match",u,p,l)):(l=p.match(u),this.debug("pattern match",u,p,l)),!l)return!1}if(i===s&&a===c)return!0;if(i===s)return r;if(a===c)return i===s-1&&""===e[i];throw new Error("wtf?")}},function(e,t){e.exports=require("util")},function(e,t,r){function n(e){return"/"===e.charAt(0)}function o(e){var t=/^([a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/]+[^\\\/]+)?([\\\/])?([\s\S]*?)$/.exec(e),r=t[1]||"",n=Boolean(r&&":"!==r.charAt(1));return Boolean(t[2]||n)}e.exports="win32"===process.platform?o:n,e.exports.posix=n,e.exports.win32=o},function(e,t,r){var n=Function.prototype.toString,o=/^\s*class\b/,i=function(e){try{var t=n.call(e);return o.test(t)}catch(e){return!1}},a=Object.prototype.toString,s="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag;e.exports=function(e){if(!e)return!1;if("function"!=typeof e&&"object"!=typeof e)return!1;if("function"==typeof e&&!e.prototype)return!0;if(s)return function(e){try{return!i(e)&&(n.call(e),!0)}catch(e){return!1}}(e);if(i(e))return!1;var t=a.call(e);return"[object Function]"===t||"[object GeneratorFunction]"===t}},function(e,t){e.exports=require("child_process")},function(e,t,r){e.exports=u,u.realpath=u,u.sync=p,u.realpathSync=p,u.monkeypatch=function(){n.realpath=u,n.realpathSync=p},u.unmonkeypatch=function(){n.realpath=o,n.realpathSync=i};var n=r(1),o=n.realpath,i=n.realpathSync,a=process.version,s=/^v[0-5]\./.test(a),c=r(41);function l(e){return e&&"realpath"===e.syscall&&("ELOOP"===e.code||"ENOMEM"===e.code||"ENAMETOOLONG"===e.code)}function u(e,t,r){if(s)return o(e,t,r);"function"==typeof t&&(r=t,t=null),o(e,t,function(n,o){l(n)?c.realpath(e,t,r):r(n,o)})}function p(e,t){if(s)return i(e,t);try{return i(e,t)}catch(r){if(l(r))return c.realpathSync(e,t);throw r}}},function(e,t){e.exports=require("assert")},function(e,t,r){function n(e,t){return Object.prototype.hasOwnProperty.call(e,t)}t.alphasort=l,t.alphasorti=c,t.setopts=function(e,t,r){r||(r={});if(r.matchBase&&-1===t.indexOf("/")){if(r.noglobstar)throw new Error("base matching requires globstar");t="**/"+t}e.silent=!!r.silent,e.pattern=t,e.strict=!1!==r.strict,e.realpath=!!r.realpath,e.realpathCache=r.realpathCache||Object.create(null),e.follow=!!r.follow,e.dot=!!r.dot,e.mark=!!r.mark,e.nodir=!!r.nodir,e.nodir&&(e.mark=!0);e.sync=!!r.sync,e.nounique=!!r.nounique,e.nonull=!!r.nonull,e.nosort=!!r.nosort,e.nocase=!!r.nocase,e.stat=!!r.stat,e.noprocess=!!r.noprocess,e.absolute=!!r.absolute,e.maxLength=r.maxLength||1/0,e.cache=r.cache||Object.create(null),e.statCache=r.statCache||Object.create(null),e.symlinks=r.symlinks||Object.create(null),function(e,t){e.ignore=t.ignore||[],Array.isArray(e.ignore)||(e.ignore=[e.ignore]);e.ignore.length&&(e.ignore=e.ignore.map(u))}(e,r),e.changedCwd=!1;var i=process.cwd();n(r,"cwd")?(e.cwd=o.resolve(r.cwd),e.changedCwd=e.cwd!==i):e.cwd=i;e.root=r.root||o.resolve(e.cwd,"/"),e.root=o.resolve(e.root),"win32"===process.platform&&(e.root=e.root.replace(/\\/g,"/"));e.cwdAbs=a(e.cwd)?e.cwd:p(e,e.cwd),"win32"===process.platform&&(e.cwdAbs=e.cwdAbs.replace(/\\/g,"/"));e.nomount=!!r.nomount,r.nonegate=!0,r.nocomment=!0,e.minimatch=new s(t,r),e.options=e.minimatch.options},t.ownProp=n,t.makeAbs=p,t.finish=function(e){for(var t=e.nounique,r=t?[]:Object.create(null),n=0,o=e.matches.length;n<o;n++){var i=e.matches[n];if(i&&0!==Object.keys(i).length){var a=Object.keys(i);t?r.push.apply(r,a):a.forEach(function(e){r[e]=!0})}else if(e.nonull){var s=e.minimatch.globSet[n];t?r.push(s):r[s]=!0}}t||(r=Object.keys(r));e.nosort||(r=r.sort(e.nocase?c:l));if(e.mark){for(var n=0;n<r.length;n++)r[n]=e._mark(r[n]);e.nodir&&(r=r.filter(function(t){var r=!/\/$/.test(t),n=e.cache[t]||e.cache[p(e,t)];return r&&n&&(r="DIR"!==n&&!Array.isArray(n)),r}))}e.ignore.length&&(r=r.filter(function(t){return!f(e,t)}));e.found=r},t.mark=function(e,t){var r=p(e,t),n=e.cache[r],o=t;if(n){var i="DIR"===n||Array.isArray(n),a="/"===t.slice(-1);if(i&&!a?o+="/":!i&&a&&(o=o.slice(0,-1)),o!==t){var s=p(e,o);e.statCache[s]=e.statCache[r],e.cache[s]=e.cache[r]}}return o},t.isIgnored=f,t.childrenIgnored=function(e,t){return!!e.ignore.length&&e.ignore.some(function(e){return!(!e.gmatcher||!e.gmatcher.match(t))})};var o=r(0),i=r(8),a=r(10),s=i.Minimatch;function c(e,t){return e.toLowerCase().localeCompare(t.toLowerCase())}function l(e,t){return e.localeCompare(t)}function u(e){var t=null;if("/**"===e.slice(-3)){var r=e.replace(/(\/\*\*)+$/,"");t=new s(r,{dot:!0})}return{matcher:new s(e,{dot:!0}),gmatcher:t}}function p(e,t){var r=t;return r="/"===t.charAt(0)?o.join(e.root,t):a(t)||""===t?t:e.changedCwd?o.resolve(e.cwd,t):o.resolve(t),"win32"===process.platform&&(r=r.replace(/\\/g,"/")),r}function f(e,t){return!!e.ignore.length&&e.ignore.some(function(e){return e.matcher.match(t)||!(!e.gmatcher||!e.gmatcher.match(t))})}},function(e,t){e.exports=function e(t,r){if(t&&r)return e(t)(r);if("function"!=typeof t)throw new TypeError("need wrapper function");Object.keys(t).forEach(function(e){n[e]=t[e]});return n;function n(){for(var e=new Array(arguments.length),r=0;r<e.length;r++)e[r]=arguments[r];var n=t.apply(this,e),o=e[e.length-1];return"function"==typeof n&&n!==o&&Object.keys(o).forEach(function(e){n[e]=o[e]}),n}}},function(e,t,r){var n=r(16);function o(e){var t=function(){return t.called?t.value:(t.called=!0,t.value=e.apply(this,arguments))};return t.called=!1,t}function i(e){var t=function(){if(t.called)throw new Error(t.onceError);return t.called=!0,t.value=e.apply(this,arguments)},r=e.name||"Function wrapped with `once`";return t.onceError=r+" shouldn't be called more than once",t.called=!1,t}e.exports=n(o),e.exports.strict=n(i),o.proto=o(function(){Object.defineProperty(Function.prototype,"once",{value:function(){return o(this)},configurable:!0}),Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return i(this)},configurable:!0})})},function(e,t,r){e.exports=r(19)},function(e,t,r){var n=r(3),o=r(68),i=r(21),a=i("%TypeError%"),s=i("%SyntaxError%"),c=i("%Array%"),l=i("%String%"),u=i("%Object%"),p=i("%Number%"),f=i("%Symbol%",!0),h=i("%RegExp%"),d=!!f,m=r(22),y=r(23),g=p.MAX_SAFE_INTEGER||Math.pow(2,53)-1,v=r(24),b=r(25),w=r(26),S=r(74),P=parseInt,j=r(4),O=j.call(Function.call,c.prototype.slice),I=j.call(Function.call,l.prototype.slice),A=j.call(Function.call,h.prototype.test,/^0b[01]+$/i),x=j.call(Function.call,h.prototype.test,/^0o[0-7]+$/i),E=j.call(Function.call,h.prototype.exec),k=new h("["+["","​","￾"].join("")+"]","g"),$=j.call(Function.call,h.prototype.test,k),T=j.call(Function.call,h.prototype.test,/^[-+]0x[0-9a-f]+$/i),F=j.call(Function.call,l.prototype.charCodeAt),C=j.call(Function.call,Object.prototype.toString),N=Math.floor,_=Math.abs,D=Object.create,M=u.getOwnPropertyDescriptor,B=u.isExtensible,V=["\t\n\v\f\r   ᠎    ","         　\u2028","\u2029\ufeff"].join(""),R=new RegExp("(^["+V+"]+)|(["+V+"]+$)","g"),G=j.call(Function.call,l.prototype.replace),L=r(75),U=r(77),W=v(v({},L),{Call:function(e,t){var r=arguments.length>2?arguments[2]:[];if(!this.IsCallable(e))throw new a(e+" is not a function");return e.apply(t,r)},ToPrimitive:o,ToNumber:function(e){var t=S(e)?e:o(e,p);if("symbol"==typeof t)throw new a("Cannot convert a Symbol value to a number");if("string"==typeof t){if(A(t))return this.ToNumber(P(I(t,2),2));if(x(t))return this.ToNumber(P(I(t,2),8));if($(t)||T(t))return NaN;var r=function(e){return G(e,R,"")}(t);if(r!==t)return this.ToNumber(r)}return p(t)},ToInt16:function(e){var t=this.ToUint16(e);return t>=32768?t-65536:t},ToInt8:function(e){var t=this.ToUint8(e);return t>=128?t-256:t},ToUint8:function(e){var t=this.ToNumber(e);if(m(t)||0===t||!y(t))return 0;var r=b(t)*N(_(t));return w(r,256)},ToUint8Clamp:function(e){var t=this.ToNumber(e);if(m(t)||t<=0)return 0;if(t>=255)return 255;var r=N(e);return r+.5<t?r+1:t<r+.5?r:r%2!=0?r+1:r},ToString:function(e){if("symbol"==typeof e)throw new a("Cannot convert a Symbol value to a string");return l(e)},ToObject:function(e){return this.RequireObjectCoercible(e),u(e)},ToPropertyKey:function(e){var t=this.ToPrimitive(e,l);return"symbol"==typeof t?t:this.ToString(t)},ToLength:function(e){var t=this.ToInteger(e);return t<=0?0:t>g?g:t},CanonicalNumericIndexString:function(e){if("[object String]"!==C(e))throw new a("must be a string");if("-0"===e)return-0;var t=this.ToNumber(e);return this.SameValue(this.ToString(t),e)?t:void 0},RequireObjectCoercible:L.CheckObjectCoercible,IsArray:c.isArray||function(e){return"[object Array]"===C(e)},IsConstructor:function(e){return"function"==typeof e&&!!e.prototype},IsExtensible:Object.preventExtensions?function(e){return!S(e)&&B(e)}:function(e){return!0},IsInteger:function(e){if("number"!=typeof e||m(e)||!y(e))return!1;var t=_(e);return N(t)===t},IsPropertyKey:function(e){return"string"==typeof e||"symbol"==typeof e},IsRegExp:function(e){if(!e||"object"!=typeof e)return!1;if(d){var t=e[f.match];if(void 0!==t)return L.ToBoolean(t)}return U(e)},SameValueZero:function(e,t){return e===t||m(e)&&m(t)},GetV:function(e,t){if(!this.IsPropertyKey(t))throw new a("Assertion failed: IsPropertyKey(P) is not true");return this.ToObject(e)[t]},GetMethod:function(e,t){if(!this.IsPropertyKey(t))throw new a("Assertion failed: IsPropertyKey(P) is not true");var r=this.GetV(e,t);if(null!=r){if(!this.IsCallable(r))throw new a(t+"is not a function");return r}},Get:function(e,t){if("Object"!==this.Type(e))throw new a("Assertion failed: Type(O) is not Object");if(!this.IsPropertyKey(t))throw new a("Assertion failed: IsPropertyKey(P) is not true");return e[t]},Type:function(e){return"symbol"==typeof e?"Symbol":L.Type(e)},SpeciesConstructor:function(e,t){if("Object"!==this.Type(e))throw new a("Assertion failed: Type(O) is not Object");var r=e.constructor;if(void 0===r)return t;if("Object"!==this.Type(r))throw new a("O.constructor is not an Object");var n=d&&f.species?r[f.species]:void 0;if(null==n)return t;if(this.IsConstructor(n))return n;throw new a("no constructor found")},CompletePropertyDescriptor:function(e){if(!this.IsPropertyDescriptor(e))throw new a("Desc must be a Property Descriptor");return this.IsGenericDescriptor(e)||this.IsDataDescriptor(e)?(n(e,"[[Value]]")||(e["[[Value]]"]=void 0),n(e,"[[Writable]]")||(e["[[Writable]]"]=!1)):(n(e,"[[Get]]")||(e["[[Get]]"]=void 0),n(e,"[[Set]]")||(e["[[Set]]"]=void 0)),n(e,"[[Enumerable]]")||(e["[[Enumerable]]"]=!1),n(e,"[[Configurable]]")||(e["[[Configurable]]"]=!1),e},Set:function(e,t,r,n){if("Object"!==this.Type(e))throw new a("O must be an Object");if(!this.IsPropertyKey(t))throw new a("P must be a Property Key");if("Boolean"!==this.Type(n))throw new a("Throw must be a Boolean");if(n)return e[t]=r,!0;try{e[t]=r}catch(e){return!1}},HasOwnProperty:function(e,t){if("Object"!==this.Type(e))throw new a("O must be an Object");if(!this.IsPropertyKey(t))throw new a("P must be a Property Key");return n(e,t)},HasProperty:function(e,t){if("Object"!==this.Type(e))throw new a("O must be an Object");if(!this.IsPropertyKey(t))throw new a("P must be a Property Key");return t in e},IsConcatSpreadable:function(e){if("Object"!==this.Type(e))return!1;if(d&&"symbol"==typeof f.isConcatSpreadable){var t=this.Get(e,Symbol.isConcatSpreadable);if(void 0!==t)return this.ToBoolean(t)}return this.IsArray(e)},Invoke:function(e,t){if(!this.IsPropertyKey(t))throw new a("P must be a Property Key");var r=O(arguments,2),n=this.GetV(e,t);return this.Call(n,e,r)},GetIterator:function(e,t){if(!d)throw new SyntaxError("ES.GetIterator depends on native iterator support.");var r=t;arguments.length<2&&(r=this.GetMethod(e,f.iterator));var n=this.Call(r,e);if("Object"!==this.Type(n))throw new a("iterator must return an object");return n},IteratorNext:function(e,t){var r=this.Invoke(e,"next",arguments.length<2?[]:[t]);if("Object"!==this.Type(r))throw new a("iterator next must return an object");return r},IteratorComplete:function(e){if("Object"!==this.Type(e))throw new a("Assertion failed: Type(iterResult) is not Object");return this.ToBoolean(this.Get(e,"done"))},IteratorValue:function(e){if("Object"!==this.Type(e))throw new a("Assertion failed: Type(iterResult) is not Object");return this.Get(e,"value")},IteratorStep:function(e){var t=this.IteratorNext(e);return!0!==this.IteratorComplete(t)&&t},IteratorClose:function(e,t){if("Object"!==this.Type(e))throw new a("Assertion failed: Type(iterator) is not Object");if(!this.IsCallable(t))throw new a("Assertion failed: completion is not a thunk for a Completion Record");var r,n=t,o=this.GetMethod(e,"return");if(void 0===o)return n();try{var i=this.Call(o,e,[])}catch(e){throw r=n(),n=null,e}if(r=n(),n=null,"Object"!==this.Type(i))throw new a("iterator .return must return an object");return r},CreateIterResultObject:function(e,t){if("Boolean"!==this.Type(t))throw new a("Assertion failed: Type(done) is not Boolean");return{value:e,done:t}},RegExpExec:function(e,t){if("Object"!==this.Type(e))throw new a("R must be an Object");if("String"!==this.Type(t))throw new a("S must be a String");var r=this.Get(e,"exec");if(this.IsCallable(r)){var n=this.Call(r,e,[t]);if(null===n||"Object"===this.Type(n))return n;throw new a('"exec" method must return `null` or an Object')}return E(e,t)},ArraySpeciesCreate:function(e,t){if(!this.IsInteger(t)||t<0)throw new a("Assertion failed: length must be an integer >= 0");var r,n=0===t?0:t;if(this.IsArray(e)&&(r=this.Get(e,"constructor"),"Object"===this.Type(r)&&d&&f.species&&null===(r=this.Get(r,f.species))&&(r=void 0)),void 0===r)return c(n);if(!this.IsConstructor(r))throw new a("C must be a constructor");return new r(n)},CreateDataProperty:function(e,t,r){if("Object"!==this.Type(e))throw new a("Assertion failed: Type(O) is not Object");if(!this.IsPropertyKey(t))throw new a("Assertion failed: IsPropertyKey(P) is not true");var n=M(e,t),o=n||"function"!=typeof B||B(e);if(n&&(!n.writable||!n.configurable)||!o)return!1;var i={configurable:!0,enumerable:!0,value:r,writable:!0};return Object.defineProperty(e,t,i),!0},CreateDataPropertyOrThrow:function(e,t,r){if("Object"!==this.Type(e))throw new a("Assertion failed: Type(O) is not Object");if(!this.IsPropertyKey(t))throw new a("Assertion failed: IsPropertyKey(P) is not true");var n=this.CreateDataProperty(e,t,r);if(!n)throw new a("unable to create data property");return n},ObjectCreate:function(e,t){if(null!==e&&"Object"!==this.Type(e))throw new a("Assertion failed: proto must be null or an object");if((arguments.length<2?[]:t).length>0)throw new s("es-abstract does not yet support internal slots");if(null===e&&!D)throw new s("native Object.create support is required to create null objects");return D(e)},AdvanceStringIndex:function(e,t,r){if("String"!==this.Type(e))throw new a("S must be a String");if(!this.IsInteger(t)||t<0||t>g)throw new a("Assertion failed: length must be an integer >= 0 and <= 2**53");if("Boolean"!==this.Type(r))throw new a("Assertion failed: unicode must be a Boolean");if(!r)return t+1;if(t+1>=e.length)return t+1;var n=F(e,t);if(n<55296||n>56319)return t+1;var o=F(e,t+1);return o<56320||o>57343?t+1:t+2}});delete W.CheckObjectCoercible,e.exports=W},function(e,t){e.exports=function(e){return null===e||"function"!=typeof e&&"object"!=typeof e}},function(e,t,r){var n=Object.getOwnPropertyDescriptor?function(){return Object.getOwnPropertyDescriptor(arguments,"callee").get}():function(){throw new TypeError},o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator,i=Object.getPrototypeOf||function(e){return e.__proto__},a=void 0,s="undefined"==typeof Uint8Array?void 0:i(Uint8Array),c={"$ %Array%":Array,"$ %ArrayBuffer%":"undefined"==typeof ArrayBuffer?void 0:ArrayBuffer,"$ %ArrayBufferPrototype%":"undefined"==typeof ArrayBuffer?void 0:ArrayBuffer.prototype,"$ %ArrayIteratorPrototype%":o?i([][Symbol.iterator]()):void 0,"$ %ArrayPrototype%":Array.prototype,"$ %ArrayProto_entries%":Array.prototype.entries,"$ %ArrayProto_forEach%":Array.prototype.forEach,"$ %ArrayProto_keys%":Array.prototype.keys,"$ %ArrayProto_values%":Array.prototype.values,"$ %AsyncFromSyncIteratorPrototype%":void 0,"$ %AsyncFunction%":void 0,"$ %AsyncFunctionPrototype%":void 0,"$ %AsyncGenerator%":void 0,"$ %AsyncGeneratorFunction%":void 0,"$ %AsyncGeneratorPrototype%":void 0,"$ %AsyncIteratorPrototype%":a&&o&&Symbol.asyncIterator?a[Symbol.asyncIterator]():void 0,"$ %Atomics%":"undefined"==typeof Atomics?void 0:Atomics,"$ %Boolean%":Boolean,"$ %BooleanPrototype%":Boolean.prototype,"$ %DataView%":"undefined"==typeof DataView?void 0:DataView,"$ %DataViewPrototype%":"undefined"==typeof DataView?void 0:DataView.prototype,"$ %Date%":Date,"$ %DatePrototype%":Date.prototype,"$ %decodeURI%":decodeURI,"$ %decodeURIComponent%":decodeURIComponent,"$ %encodeURI%":encodeURI,"$ %encodeURIComponent%":encodeURIComponent,"$ %Error%":Error,"$ %ErrorPrototype%":Error.prototype,"$ %eval%":eval,"$ %EvalError%":EvalError,"$ %EvalErrorPrototype%":EvalError.prototype,"$ %Float32Array%":"undefined"==typeof Float32Array?void 0:Float32Array,"$ %Float32ArrayPrototype%":"undefined"==typeof Float32Array?void 0:Float32Array.prototype,"$ %Float64Array%":"undefined"==typeof Float64Array?void 0:Float64Array,"$ %Float64ArrayPrototype%":"undefined"==typeof Float64Array?void 0:Float64Array.prototype,"$ %Function%":Function,"$ %FunctionPrototype%":Function.prototype,"$ %Generator%":void 0,"$ %GeneratorFunction%":void 0,"$ %GeneratorPrototype%":void 0,"$ %Int8Array%":"undefined"==typeof Int8Array?void 0:Int8Array,"$ %Int8ArrayPrototype%":"undefined"==typeof Int8Array?void 0:Int8Array.prototype,"$ %Int16Array%":"undefined"==typeof Int16Array?void 0:Int16Array,"$ %Int16ArrayPrototype%":"undefined"==typeof Int16Array?void 0:Int8Array.prototype,"$ %Int32Array%":"undefined"==typeof Int32Array?void 0:Int32Array,"$ %Int32ArrayPrototype%":"undefined"==typeof Int32Array?void 0:Int32Array.prototype,"$ %isFinite%":isFinite,"$ %isNaN%":isNaN,"$ %IteratorPrototype%":o?i(i([][Symbol.iterator]())):void 0,"$ %JSON%":JSON,"$ %JSONParse%":JSON.parse,"$ %Map%":"undefined"==typeof Map?void 0:Map,"$ %MapIteratorPrototype%":"undefined"!=typeof Map&&o?i((new Map)[Symbol.iterator]()):void 0,"$ %MapPrototype%":"undefined"==typeof Map?void 0:Map.prototype,"$ %Math%":Math,"$ %Number%":Number,"$ %NumberPrototype%":Number.prototype,"$ %Object%":Object,"$ %ObjectPrototype%":Object.prototype,"$ %ObjProto_toString%":Object.prototype.toString,"$ %ObjProto_valueOf%":Object.prototype.valueOf,"$ %parseFloat%":parseFloat,"$ %parseInt%":parseInt,"$ %Promise%":"undefined"==typeof Promise?void 0:Promise,"$ %PromisePrototype%":"undefined"==typeof Promise?void 0:Promise.prototype,"$ %PromiseProto_then%":"undefined"==typeof Promise?void 0:Promise.prototype.then,"$ %Promise_all%":"undefined"==typeof Promise?void 0:Promise.all,"$ %Promise_reject%":"undefined"==typeof Promise?void 0:Promise.reject,"$ %Promise_resolve%":"undefined"==typeof Promise?void 0:Promise.resolve,"$ %Proxy%":"undefined"==typeof Proxy?void 0:Proxy,"$ %RangeError%":RangeError,"$ %RangeErrorPrototype%":RangeError.prototype,"$ %ReferenceError%":ReferenceError,"$ %ReferenceErrorPrototype%":ReferenceError.prototype,"$ %Reflect%":"undefined"==typeof Reflect?void 0:Reflect,"$ %RegExp%":RegExp,"$ %RegExpPrototype%":RegExp.prototype,"$ %Set%":"undefined"==typeof Set?void 0:Set,"$ %SetIteratorPrototype%":"undefined"!=typeof Set&&o?i((new Set)[Symbol.iterator]()):void 0,"$ %SetPrototype%":"undefined"==typeof Set?void 0:Set.prototype,"$ %SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?void 0:SharedArrayBuffer,"$ %SharedArrayBufferPrototype%":"undefined"==typeof SharedArrayBuffer?void 0:SharedArrayBuffer.prototype,"$ %String%":String,"$ %StringIteratorPrototype%":o?i(""[Symbol.iterator]()):void 0,"$ %StringPrototype%":String.prototype,"$ %Symbol%":o?Symbol:void 0,"$ %SymbolPrototype%":o?Symbol.prototype:void 0,"$ %SyntaxError%":SyntaxError,"$ %SyntaxErrorPrototype%":SyntaxError.prototype,"$ %ThrowTypeError%":n,"$ %TypedArray%":s,"$ %TypedArrayPrototype%":s?s.prototype:void 0,"$ %TypeError%":TypeError,"$ %TypeErrorPrototype%":TypeError.prototype,"$ %Uint8Array%":"undefined"==typeof Uint8Array?void 0:Uint8Array,"$ %Uint8ArrayPrototype%":"undefined"==typeof Uint8Array?void 0:Uint8Array.prototype,"$ %Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?void 0:Uint8ClampedArray,"$ %Uint8ClampedArrayPrototype%":"undefined"==typeof Uint8ClampedArray?void 0:Uint8ClampedArray.prototype,"$ %Uint16Array%":"undefined"==typeof Uint16Array?void 0:Uint16Array,"$ %Uint16ArrayPrototype%":"undefined"==typeof Uint16Array?void 0:Uint16Array.prototype,"$ %Uint32Array%":"undefined"==typeof Uint32Array?void 0:Uint32Array,"$ %Uint32ArrayPrototype%":"undefined"==typeof Uint32Array?void 0:Uint32Array.prototype,"$ %URIError%":URIError,"$ %URIErrorPrototype%":URIError.prototype,"$ %WeakMap%":"undefined"==typeof WeakMap?void 0:WeakMap,"$ %WeakMapPrototype%":"undefined"==typeof WeakMap?void 0:WeakMap.prototype,"$ %WeakSet%":"undefined"==typeof WeakSet?void 0:WeakSet,"$ %WeakSetPrototype%":"undefined"==typeof WeakSet?void 0:WeakSet.prototype};e.exports=function(e,t){if(arguments.length>1&&"boolean"!=typeof t)throw new TypeError('"allowMissing" argument must be a boolean');var r="$ "+e;if(!(r in c))throw new SyntaxError("intrinsic "+e+" does not exist!");if(void 0===c[r]&&!t)throw new TypeError("intrinsic "+e+" exists, but is not available. Please file an issue!");return c[r]}},function(e,t){e.exports=Number.isNaN||function(e){return e!=e}},function(e,t){var r=Number.isNaN||function(e){return e!=e};e.exports=Number.isFinite||function(e){return"number"==typeof e&&!r(e)&&e!==1/0&&e!==-1/0}},function(e,t,r){var n=r(4).call(Function.call,Object.prototype.hasOwnProperty),o=Object.assign;e.exports=function(e,t){if(o)return o(e,t);for(var r in t)n(t,r)&&(e[r]=t[r]);return e}},function(e,t){e.exports=function(e){return e>=0?1:-1}},function(e,t){e.exports=function(e,t){var r=e%t;return Math.floor(r>=0?r:r+t)}},function(e,t,r){var n=r(18),o=Number.isNaN||function(e){return e!=e},i=Number.isFinite||function(e){return"number"==typeof e&&global.isFinite(e)},a=Array.prototype.indexOf;e.exports=function(e){var t=arguments.length>1?n.ToInteger(arguments[1]):0;if(a&&!o(e)&&i(t)&&void 0!==e)return a.apply(this,arguments)>-1;var r=n.ToObject(this),s=n.ToLength(r.length);if(0===s)return!1;for(var c=t>=0?t:Math.max(0,s+t);c<s;){if(n.SameValueZero(e,r[c]))return!0;c+=1}return!1}},function(e,t,r){var n=r(27);e.exports=function(){return Array.prototype.includes||n}},function(e,t,r){var n=r(30),o=r(3),i=r(4).call(Function.call,Object.prototype.propertyIsEnumerable);e.exports=function(e){var t=n.RequireObjectCoercible(e),r=[];for(var a in t)o(t,a)&&i(t,a)&&r.push([a,t[a]]);return r}},function(e,t,r){e.exports=r(80)},function(e,t,r){var n=r(29);e.exports=function(){return"function"==typeof Object.entries?Object.entries:n}},function(e,t,r){var n=r(30),o=r(3),i=r(4).call(Function.call,Object.prototype.propertyIsEnumerable);e.exports=function(e){var t=n.RequireObjectCoercible(e),r=[];for(var a in t)o(t,a)&&i(t,a)&&r.push(t[a]);return r}},function(e,t,r){var n=r(32);e.exports=function(){return"function"==typeof Object.values?Object.values:n}},function(e,t,r){const n=r(35),o=r(55),i=r(63),a=r(6),s=r(64),c=r(79),l=r(82);function u(e,t){(t=t||{}).clipboard&&console.log("\n*** Clipboard option removed - use clipboardy or clipboard-cli directly ***\n");const r=Object.keys(e).length>0?e:i.defaults,a=Object.entries(r).reduce((e,r)=>{const o=r[0],i=r[1],a=n[`get${o}`];return a?(i&&e.push(a(i,t)),e):e=e.concat((i||[]).map(e=>{const t=n[`get${e.replace(/\s/g,"")}Info`];return t?t():Promise.resolve(["Unknown"])}))},[]);return Promise.all(a).then(e=>{const r=e.reduce((e,t)=>(t&&t[0]&&Object.assign(e,{[t[0]]:t}),e),{});return function(e,t){const r=(()=>t.json?o.json:t.markdown?o.markdown:o.yaml)();return t.console&&console.log(r(e,Object.assign({},t,{console:!0}))),r(e,Object.assign({},t,{console:!1}))}(Object.entries(i.defaults).reduce((e,t)=>{const n=t[0],o=t[1];return r[n]?Object.assign(e,{[n]:r[n][1]}):Object.assign(e,{[n]:(o||[]).reduce((e,t)=>r[t]?(r[t].shift(),1===r[t].length?Object.assign(e,{[t]:r[t][0]}):Object.assign(e,{[t]:{version:r[t][0],path:r[t][1]}})):e,{})})},{}),t)})}Array.prototype.includes||s.shim(),Object.entries||c.shim(),Object.values||l.shim(),e.exports={cli:function(e){if(e.all)return u(Object.assign({},i.defaults,{npmPackages:!0,npmGlobalPackages:!0}),e);if(e.raw)return u(JSON.parse(e.raw),e);if(e.helper){const t=n[`get${e.helper}`]||n[`get${e.helper}Info`]||n[e.helper];return t?t().then(console.log):console.error("Not Found")}const t=(e,t)=>e.toLowerCase().includes(t.toLowerCase()),r=Object.keys(e).filter(e=>Object.keys(i.defaults).some(r=>t(r,e))),o=Object.entries(i.defaults).reduce((n,o)=>r.some(e=>t(e,o[0]))?Object.assign(n,{[o[0]]:o[1]||e[o[0]]}):n,{});return e.preset?(i[e.preset]||console.error(`\nNo "${e.preset}" preset found.`),u(Object.assign({},a.omit(i[e.preset],["options"]),o),Object.assign({},i[e.preset].options,a.pick(e,["duplicates","fullTree","json","markdown","console"])))):u(o,e)},helpers:n,main:u,run:function(e,t){return"string"==typeof e.preset?u(i[e.preset],t):u(e,t)}}},function(e,t,r){const n=r(5),o=r(36),i=r(0),a=r(40),s=r(6),c="N/A",l="darwin"===process.platform,u="linux"===process.platform,p=process.platform.startsWith("win");e.exports=Object.assign({},s,a,{getiOSSDKInfo:()=>l?s.run("xcodebuild -showsdks").then(e=>e.match(/[\w]+\s[\d|.]+/g)).then(s.uniq).then(e=>e.length?["iOS SDK",{Platforms:e}]:["iOS SDK","Not Found"]):Promise.resolve(["iOS SDK",c]),getAndroidSDKInfo:()=>s.run(process.env.ANDROID_HOME?"$ANDROID_HOME/tools/bin/sdkmanager --list":"sdkmanager --list").then(e=>!e&&l?s.run("~/Library/Android/sdk/tools/bin/sdkmanager --list"):e).then(e=>{const t=s.parseSDKManagerOutput(e);return t.buildTools.length||t.apiLevels.length||t.systemImages.length?Promise.resolve(["Android SDK",{"API Levels":t.apiLevels||"Not Found","Build Tools":t.buildTools||"Not Found","System Images":t.systemImages||"Not Found"}]):Promise.resolve(["Android SDK","Not Found"])}),getAndroidStudioInfo:()=>{let e;return l?e=s.run(s.generatePlistBuddyCommand(i.join("/","Applications","Android\\ Studio.app","Contents","Info.plist"),["CFBundleShortVersionString","CFBundleVersion"])).then(e=>e.split("\n").join(" ")):u?e=Promise.all([s.run('cat /opt/android-studio/bin/studio.sh | grep "$Home/.AndroidStudio" | head -1').then(s.findVersion),s.run("cat /opt/android-studio/build.txt")]).then(e=>{return`${e[0]} ${e[1]}`.trim()||"Not Found"}):p&&(e=Promise.all([s.run('wmic datafile where name="C:\\\\Program Files\\\\Android\\\\Android Studio\\\\bin\\\\studio.exe" get Version').then(e=>e.replace(/(\r\n|\n|\r)/gm,"")),s.run('type "C:\\\\Program Files\\\\Android\\\\Android Studio\\\\build.txt"').then(e=>e.replace(/(\r\n|\n|\r)/gm,""))]).then(e=>{return`${e[0]} ${e[1]}`.trim()||"Not Found"})),e.then(e=>s.determineFound("Android Studio",e))},getAtomInfo:()=>(s.log("trace","getAtomInfo"),Promise.all([s.getDarwinApplicationVersion(s.ideBundleIdentifiers.Atom),c]).then(e=>s.determineFound("Atom",e[0],e[1]))),getMySQLInfo:()=>(s.log("trace","getMySQLInfo"),Promise.all([s.run("mysql --version").then(e=>`${s.findVersion(e,null,1)}${e.includes("MariaDB")?" (MariaDB)":""}`),s.which("mysql")]).then(e=>s.determineFound("MySQL",e[0],e[1]))),getMongoDBInfo:()=>(s.log("trace","getMongoDBInfo"),Promise.all([s.run("mongo --version").then(s.findVersion),s.which("mongo")]).then(e=>s.determineFound("MongoDB",e[0],e[1]))),getSQLiteInfo:()=>(s.log("trace","getSQLiteInfo"),Promise.all([s.run("sqlite3 --version").then(s.findVersion),s.which("sqlite3")]).then(e=>s.determineFound("SQLite",e[0],e[1]))),getPostgreSQLInfo:()=>(s.log("trace","getPostgreSQLInfo"),Promise.all([s.run("postgres --version").then(s.findVersion),s.which("postgres")]).then(e=>s.determineFound("PostgreSQL",e[0],e[1]))),getCPUInfo:()=>{let e;s.log("trace","getCPUInfo");try{const t=n.cpus();e="("+t.length+") "+n.arch()+" "+t[0].model}catch(t){e="Unknown"}return Promise.all(["CPU",e])},getBashInfo:()=>(s.log("trace","getBashInfo"),Promise.all([s.run("bash --version").then(s.findVersion),s.which("bash")]).then(e=>s.determineFound("Bash",e[0],e[1]))),getPerlInfo:()=>(s.log("trace","getPerlInfo"),Promise.all([s.run("perl -v").then(s.findVersion),s.which("perl")]).then(e=>s.determineFound("Perl",e[0],e[1]))),getPHPInfo:()=>(s.log("trace","getPHPInfo"),Promise.all([s.run("php -v").then(s.findVersion),s.which("php")]).then(e=>s.determineFound("PHP",e[0],e[1]))),getParallelsInfo:()=>(s.log("trace","getParallelsInfo"),Promise.all([s.run("prlctl --version").then(s.findVersion),s.which("prlctl")]).then(e=>s.determineFound("Parallels",e[0],e[1]))),getDockerInfo:()=>(s.log("trace","getDockerInfo"),Promise.all([s.run("docker --version").then(s.findVersion),s.which("docker")]).then(e=>s.determineFound("Docker",e[0],e[1]))),getElixirInfo:()=>(s.log("trace","getElixirInfo"),Promise.all([s.run("elixir --version").then(e=>s.findVersion(e,/[Elixir]+\s([\d+.[\d+|.]+)/,1)),s.which("elixir")]).then(e=>Promise.resolve(s.determineFound("Elixir",e[0],e[1])))),getMemoryInfo:()=>(s.log("trace","getMemoryInfo"),Promise.all(["Memory",`${s.toReadableBytes(n.freemem())} / ${s.toReadableBytes(n.totalmem())}`])),getSublimeTextInfo:()=>(s.log("trace","getSublimeTextInfo"),Promise.all([s.run("subl --version").then(e=>s.findVersion(e,/\d+/)),s.which("subl")]).then(e=>""===e[0]&&l?(s.log("trace","getSublimeTextInfo using plist"),Promise.all([s.getDarwinApplicationVersion(s.ideBundleIdentifiers["Sublime Text"]),c])):e).then(e=>s.determineFound("Sublime Text",e[0],e[1]))),getHomeBrewInfo:()=>{return s.log("trace","getHomeBrewInfo"),l?Promise.all(["Homebrew",s.run("brew --version").then(s.findVersion),s.which("brew")]):Promise.all(["Homebrew",c])},getGoInfo:()=>(s.log("trace","getGoInfo"),Promise.all([s.run("go version").then(s.findVersion),s.which("go")]).then(e=>s.determineFound("Go",e[0],e[1]))),getRubyInfo:()=>(s.log("trace","getRubyInfo"),Promise.all([s.run("ruby -v").then(s.findVersion),s.which("ruby")]).then(e=>s.determineFound("Ruby",e[0],e[1]))),getNodeInfo:()=>(s.log("trace","getNodeInfo"),Promise.all([p?s.run("node -v").then(s.findVersion):s.which("node").then(e=>e?s.run(e+" -v"):Promise.resolve("")).then(s.findVersion),s.which("node").then(s.condensePath)]).then(e=>s.determineFound("Node",e[0],e[1]))),getnpmInfo:()=>(s.log("trace","getnpmInfo"),Promise.all([s.run("npm -v"),s.which("npm").then(s.condensePath)]).then(e=>s.determineFound("npm",e[0],e[1]))),getShellInfo:()=>{if(s.log("trace","getShellInfo",process.env),l||u){const e=process.env.SHELL||s.runSync("getent passwd $LOGNAME | cut -d: -f7 | head -1");return Promise.all([s.run(`${e} --version`).then(s.findVersion),s.which(e)]).then(e=>s.determineFound("Shell",e[0]||"Unknown",e[1]))}return Promise.resolve(["Shell",c])},getOSInfo:()=>{let e;return s.log("trace","getOSInfo"),(e=l?s.run("sw_vers -productVersion "):u?s.run("cat /etc/os-release").then(e=>{const t=(e||"").match(/NAME="(.+)"/),r=(e||"").match(/VERSION="(.+)"/)||[];return`${t[1]} ${r[1]}`.trim()||""}):Promise.resolve()).then(e=>{let t=o(n.platform(),n.release());return e&&(t+=` ${e}`),["OS",t]})},getContainerInfo:()=>(s.log("trace","getContainerInfo"),u?Promise.all([s.fileExists("/.dockerenv"),s.readFile("/proc/self/cgroup")]).then(e=>(s.log("trace","getContainerInfoThen",e),Promise.resolve(["Container",e[0]||e[1]?"Yes":c]))).catch(e=>s.log("trace","getContainerInfoCatch",e)):Promise.resolve(["Container",c])),getWatchmanInfo:()=>(s.log("trace","getWatchmanInfo"),Promise.all([s.which("watchman").then(e=>e?s.run(e+" -v"):void 0),s.which("watchman")]).then(e=>s.determineFound("Watchman",e[0],e[1]))),getVSCodeInfo:()=>(s.log("trace","getVSCodeInfo"),Promise.all([s.run("code --version").then(s.findVersion),s.which("code")]).then(e=>s.determineFound("VSCode",e[0],e[1]))),getIntelliJInfo:()=>(s.log("trace","getIntelliJInfo"),s.getDarwinApplicationVersion(s.ideBundleIdentifiers.IntelliJ).then(e=>s.determineFound("IntelliJ",e))),getPhpStormInfo:()=>(s.log("trace","getPhpStormInfo"),s.getDarwinApplicationVersion(s.ideBundleIdentifiers.PhpStorm).then(e=>s.determineFound("PhpStorm",e))),getWebStormInfo:()=>(s.log("trace","getWebStormInfo"),s.getDarwinApplicationVersion(s.ideBundleIdentifiers.WebStorm).then(e=>s.determineFound("WebStorm",e))),getVirtualBoxInfo:()=>(s.log("trace","getVirtualBoxInfo"),Promise.all([s.run("vboxmanage --version").then(s.findVersion),s.which("vboxmanage")]).then(e=>s.determineFound("VirtualBox",e[0],e[1]))),getVMwareFusionInfo:()=>(s.log("trace","getVMwareFusionInfo"),s.getDarwinApplicationVersion("com.vmware.fusion").then(e=>s.determineFound("VMWare Fusion",e,c))),getPythonInfo:()=>(s.log("trace","getPythonInfo"),Promise.all([s.run("python -V 2>&1").then(s.findVersion),s.run("which python")]).then(e=>s.determineFound("Python",e[0],e[1]))),getXcodeInfo:()=>(s.log("trace","getXcodeInfo"),l?Promise.all([s.which("xcodebuild").then(e=>s.run(e+" -version")).then(e=>`${s.findVersion(e)}/${e.split("Build version ")[1]}`),s.which("xcodebuild")]).then(e=>s.determineFound("Xcode",e[0],e[1])):Promise.resolve(["Xcode",c])),getYarnInfo:()=>(s.log("trace","getYarnInfo"),Promise.all([s.run("yarn -v"),s.which("yarn").then(s.condensePath)]).then(e=>s.determineFound("Yarn",e[0],e[1]))),getEdgeInfo:()=>{let e;return s.log("trace","getEdgeInfo"),(e=p&&"10"===n.release().split(".")[0]?s.run("powershell get-appxpackage Microsoft.MicrosoftEdge").then(s.findVersion):Promise.resolve(c)).then(e=>s.determineFound("Edge",e,c))},getInternetExplorerInfo:()=>{let e;if(s.log("trace","getInternetExplorerInfo"),p){const t=[process.env.SYSTEMDRIVE||"C:","Program Files","Internet Explorer","iexplore.exe"].join("\\\\");e=s.run(`wmic datafile where "name='${t}'" get Version`).then(s.findVersion)}else e=Promise.resolve(c);return e.then(e=>s.determineFound("Internet Explorer",e,c))},getChromeInfo:()=>{let e;return s.log("trace","getChromeInfo"),(e=u?s.run("google-chrome --version").then(e=>e.replace(/^.* ([^ ]*)/g,"$1")):l?s.getDarwinApplicationVersion(s.browserBundleIdentifiers.Chrome).then(s.findVersion):Promise.resolve(c)).then(e=>s.determineFound("Chrome",e,c))},getChromeCanaryInfo:()=>{return s.log("trace","getChromeCanaryInfo"),s.getDarwinApplicationVersion(s.browserBundleIdentifiers["Chrome Canary"]).then(e=>s.determineFound("Chrome Canary",e,c))},getFirefoxDeveloperEditionInfo:()=>{return s.log("trace","getFirefoxDeveloperEditionInfo"),s.getDarwinApplicationVersion(s.browserBundleIdentifiers["Firefox Developer Edition"]).then(e=>s.determineFound("Firefox Developer Edition",e,c))},getSafariTechnologyPreviewInfo:()=>{return s.log("trace","getSafariTechnologyPreviewInfo"),s.getDarwinApplicationVersion(s.browserBundleIdentifiers["Safari Technology Preview"]).then(e=>s.determineFound("Safari Technology Preview",e,c))},getSafariInfo:()=>{return s.log("trace","getSafariInfo"),s.getDarwinApplicationVersion(s.browserBundleIdentifiers.Safari).then(e=>s.determineFound("Safari",e,c))},getFirefoxInfo:()=>{let e;return s.log("trace","getFirefoxInfo"),(e=u?s.run("firefox --version").then(e=>e.replace(/^.* ([^ ]*)/g,"$1")):l?s.getDarwinApplicationVersion(s.browserBundleIdentifiers.Firefox):Promise.resolve(c)).then(e=>s.determineFound("Firefox",e,c))},getFirefoxNightlyInfo:()=>{let e;return s.log("trace","getFirefoxNightlyInfo"),(e=u?s.run("firefox-trunk --version").then(e=>e.replace(/^.* ([^ ]*)/g,"$1")):l?s.getDarwinApplicationVersion(s.browserBundleIdentifiers["Firefox Nightly"]):Promise.resolve(c)).then(e=>s.determineFound("Firefox Nightly",e,c))},getGitInfo:()=>(s.log("trace","getGitInfo"),l||u?Promise.all([s.run("git --version").then(s.findVersion),s.run("which git")]).then(e=>s.determineFound("Git",e[0],e[1])):Promise.resolve(["Git",c])),getMakeInfo:()=>(s.log("trace","getMakeInfo"),l||u?Promise.all([s.run("make --version").then(s.findVersion),s.run("which make")]).then(e=>s.determineFound("Make",e[0],e[1])):Promise.resolve(["Make",c])),getCMakeInfo:()=>(s.log("trace","getCMakeInfo"),l||u?Promise.all([s.run("cmake --version").then(s.findVersion),s.run("which cmake")]).then(e=>s.determineFound("CMake",e[0],e[1])):Promise.resolve(["CMake",c])),getGCCInfo:()=>(s.log("trace","getGCCInfo"),l||u?Promise.all([s.run("gcc -v 2>&1").then(s.findVersion),s.run("which gcc")]).then(e=>s.determineFound("GCC",e[0],e[1])):Promise.resolve(["GCC",c])),getNanoInfo:()=>(s.log("trace","getNanoInfo"),l||u?Promise.all([s.run("nano --version").then(s.findVersion),s.run("which nano")]).then(e=>s.determineFound("Nano",e[0],e[1])):Promise.resolve(["Nano",c])),getEmacsInfo:()=>(s.log("trace","getEmacsInfo"),l||u?Promise.all([s.run("emacs --version").then(s.findVersion),s.run("which emacs")]).then(e=>s.determineFound("Emacs",e[0],e[1])):Promise.resolve(["Emacs",c])),getVimInfo:()=>(s.log("trace","getVimInfo"),l||u?Promise.all([s.run("vim --version").then(s.findVersion),s.run("which vim")]).then(e=>s.determineFound("Vim",e[0],e[1])):Promise.resolve(["Vim",c])),getNvimInfo:()=>(s.log("trace","getNvimInfo"),l||u?Promise.all([s.run("nvim --version").then(s.findVersion),s.run("which nvim")]).then(e=>s.determineFound("Nvim",e[0],e[1])):Promise.resolve(["Vim",c])),getRustInfo:()=>(s.log("trace","getRustInfo"),l||u?Promise.all([s.run("rustup --version").then(s.findVersion),s.run("which rustup")]).then(e=>s.determineFound("Rust",e[0],e[1])):Promise.resolve(["Rust",c])),getScalaInfo:()=>(s.log("trace","getScalaInfo"),l||u?Promise.all([s.run("scalac -version").then(s.findVersion),s.run("which scalac")]).then(e=>s.determineFound("Scala",e[0],e[1])):Promise.resolve(["Scala",c])),getJavaInfo:()=>(s.log("trace","getJavaInfo"),l||u?Promise.all([s.run("javac -version 2>&1").then(s.findVersion),s.run("which javac")]).then(e=>s.determineFound("Java",e[0],e[1])):Promise.resolve(["Java",c])),getApacheInfo:()=>(s.log("trace","getApacheInfo"),l||u?Promise.all([s.run("apachectl -v").then(s.findVersion),s.run("which apachectl")]).then(e=>s.determineFound("Apache",e[0],e[1])):Promise.resolve(["Apache",c])),getNginxInfo:()=>(s.log("trace","getNginxInfo"),l||u?Promise.all([s.run("nginx -v 2>&1").then(s.findVersion),s.run("which nginx")]).then(e=>s.determineFound("Nginx",e[0],e[1])):Promise.resolve(["Nginx",c]))})},function(e,t,r){var n=r(5),o=r(37),i=r(38);e.exports=function(e,t){if(!e&&t)throw new Error("You can't specify a `release` without specifying `platform`");var r;return e=e||n.platform(),t=t||n.release(),"darwin"===e?(Number(t.split(".")[0])>15?"macOS":"OS X")+((r=o(t).name)?" "+r:""):"linux"===e?"Linux"+((r=t.replace(/^(\d+\.\d+).*/,"$1"))?" "+r:""):"win32"===e?"Windows"+((r=i(t))?" "+r:""):e}},function(e,t,r){var n=r(5),o={17:"High Sierra",16:"Sierra",15:"El Capitan",14:"Yosemite",13:"Mavericks",12:"Mountain Lion",11:"Lion",10:"Snow Leopard",9:"Leopard",8:"Tiger",7:"Panther",6:"Jaguar",5:"Puma"};e.exports=function(e){return e=(e||n.release()).split(".")[0],{name:o[e],version:"10."+(Number(e)-4)}}},function(e,t,r){var n=r(5),o=r(39),i={"10.0":"10",6.3:"8.1",6.2:"8",6.1:"7","6.0":"Vista",5.1:"XP","5.0":"2000",4.9:"ME",4.1:"98","4.0":"95"};e.exports=function(e){var t=/\d+\.\d+/,a=t.exec(e||n.release());if(!e&&"win32"===process.platform&&o.satisfies(process.version,">=0.12.0 <3.1.0"))try{a=t.exec(String(r(12).execSync("ver.exe",{timeout:2e3})))}catch(e){}if(e&&!a)throw new Error("`release` argument doesn't match `n.n`");return i[(a||[])[0]]}},function(e,t){var r;t=e.exports=q,r="object"==typeof process&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?function(){var e=Array.prototype.slice.call(arguments,0);e.unshift("SEMVER"),console.log.apply(console,e)}:function(){},t.SEMVER_SPEC_VERSION="2.0.0";var n=256,o=Number.MAX_SAFE_INTEGER||9007199254740991,i=t.re=[],a=t.src=[],s=0,c=s++;a[c]="0|[1-9]\\d*";var l=s++;a[l]="[0-9]+";var u=s++;a[u]="\\d*[a-zA-Z-][a-zA-Z0-9-]*";var p=s++;a[p]="("+a[c]+")\\.("+a[c]+")\\.("+a[c]+")";var f=s++;a[f]="("+a[l]+")\\.("+a[l]+")\\.("+a[l]+")";var h=s++;a[h]="(?:"+a[c]+"|"+a[u]+")";var d=s++;a[d]="(?:"+a[l]+"|"+a[u]+")";var m=s++;a[m]="(?:-("+a[h]+"(?:\\."+a[h]+")*))";var y=s++;a[y]="(?:-?("+a[d]+"(?:\\."+a[d]+")*))";var g=s++;a[g]="[0-9A-Za-z-]+";var v=s++;a[v]="(?:\\+("+a[g]+"(?:\\."+a[g]+")*))";var b=s++,w="v?"+a[p]+a[m]+"?"+a[v]+"?";a[b]="^"+w+"$";var S="[v=\\s]*"+a[f]+a[y]+"?"+a[v]+"?",P=s++;a[P]="^"+S+"$";var j=s++;a[j]="((?:<|>)?=?)";var O=s++;a[O]=a[l]+"|x|X|\\*";var I=s++;a[I]=a[c]+"|x|X|\\*";var A=s++;a[A]="[v=\\s]*("+a[I]+")(?:\\.("+a[I]+")(?:\\.("+a[I]+")(?:"+a[m]+")?"+a[v]+"?)?)?";var x=s++;a[x]="[v=\\s]*("+a[O]+")(?:\\.("+a[O]+")(?:\\.("+a[O]+")(?:"+a[y]+")?"+a[v]+"?)?)?";var E=s++;a[E]="^"+a[j]+"\\s*"+a[A]+"$";var k=s++;a[k]="^"+a[j]+"\\s*"+a[x]+"$";var $=s++;a[$]="(?:^|[^\\d])(\\d{1,16})(?:\\.(\\d{1,16}))?(?:\\.(\\d{1,16}))?(?:$|[^\\d])";var T=s++;a[T]="(?:~>?)";var F=s++;a[F]="(\\s*)"+a[T]+"\\s+",i[F]=new RegExp(a[F],"g");var C=s++;a[C]="^"+a[T]+a[A]+"$";var N=s++;a[N]="^"+a[T]+a[x]+"$";var _=s++;a[_]="(?:\\^)";var D=s++;a[D]="(\\s*)"+a[_]+"\\s+",i[D]=new RegExp(a[D],"g");var M=s++;a[M]="^"+a[_]+a[A]+"$";var B=s++;a[B]="^"+a[_]+a[x]+"$";var V=s++;a[V]="^"+a[j]+"\\s*("+S+")$|^$";var R=s++;a[R]="^"+a[j]+"\\s*("+w+")$|^$";var G=s++;a[G]="(\\s*)"+a[j]+"\\s*("+S+"|"+a[A]+")",i[G]=new RegExp(a[G],"g");var L=s++;a[L]="^\\s*("+a[A]+")\\s+-\\s+("+a[A]+")\\s*$";var U=s++;a[U]="^\\s*("+a[x]+")\\s+-\\s+("+a[x]+")\\s*$";var W=s++;a[W]="(<|>)?=?\\s*\\*";for(var K=0;K<35;K++)r(K,a[K]),i[K]||(i[K]=new RegExp(a[K]));function J(e,t){if(t&&"object"==typeof t||(t={loose:!!t,includePrerelease:!1}),e instanceof q)return e;if("string"!=typeof e)return null;if(e.length>n)return null;if(!(t.loose?i[P]:i[b]).test(e))return null;try{return new q(e,t)}catch(e){return null}}function q(e,t){if(t&&"object"==typeof t||(t={loose:!!t,includePrerelease:!1}),e instanceof q){if(e.loose===t.loose)return e;e=e.version}else if("string"!=typeof e)throw new TypeError("Invalid Version: "+e);if(e.length>n)throw new TypeError("version is longer than "+n+" characters");if(!(this instanceof q))return new q(e,t);r("SemVer",e,t),this.options=t,this.loose=!!t.loose;var a=e.trim().match(t.loose?i[P]:i[b]);if(!a)throw new TypeError("Invalid Version: "+e);if(this.raw=e,this.major=+a[1],this.minor=+a[2],this.patch=+a[3],this.major>o||this.major<0)throw new TypeError("Invalid major version");if(this.minor>o||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>o||this.patch<0)throw new TypeError("Invalid patch version");a[4]?this.prerelease=a[4].split(".").map(function(e){if(/^[0-9]+$/.test(e)){var t=+e;if(t>=0&&t<o)return t}return e}):this.prerelease=[],this.build=a[5]?a[5].split("."):[],this.format()}t.parse=J,t.valid=function(e,t){var r=J(e,t);return r?r.version:null},t.clean=function(e,t){var r=J(e.trim().replace(/^[=v]+/,""),t);return r?r.version:null},t.SemVer=q,q.prototype.format=function(){return this.version=this.major+"."+this.minor+"."+this.patch,this.prerelease.length&&(this.version+="-"+this.prerelease.join(".")),this.version},q.prototype.toString=function(){return this.version},q.prototype.compare=function(e){return r("SemVer.compare",this.version,this.options,e),e instanceof q||(e=new q(e,this.options)),this.compareMain(e)||this.comparePre(e)},q.prototype.compareMain=function(e){return e instanceof q||(e=new q(e,this.options)),Q(this.major,e.major)||Q(this.minor,e.minor)||Q(this.patch,e.patch)},q.prototype.comparePre=function(e){if(e instanceof q||(e=new q(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;var t=0;do{var n=this.prerelease[t],o=e.prerelease[t];if(r("prerelease compare",t,n,o),void 0===n&&void 0===o)return 0;if(void 0===o)return 1;if(void 0===n)return-1;if(n!==o)return Q(n,o)}while(++t)},q.prototype.inc=function(e,t){switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t),this.inc("pre",t);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",t),this.inc("pre",t);break;case"major":0===this.minor&&0===this.patch&&0!==this.prerelease.length||this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":0===this.patch&&0!==this.prerelease.length||this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":if(0===this.prerelease.length)this.prerelease=[0];else{for(var r=this.prerelease.length;--r>=0;)"number"==typeof this.prerelease[r]&&(this.prerelease[r]++,r=-2);-1===r&&this.prerelease.push(0)}t&&(this.prerelease[0]===t?isNaN(this.prerelease[1])&&(this.prerelease=[t,0]):this.prerelease=[t,0]);break;default:throw new Error("invalid increment argument: "+e)}return this.format(),this.raw=this.version,this},t.inc=function(e,t,r,n){"string"==typeof r&&(n=r,r=void 0);try{return new q(e,r).inc(t,n).version}catch(e){return null}},t.diff=function(e,t){if(Z(e,t))return null;var r=J(e),n=J(t);if(r.prerelease.length||n.prerelease.length){for(var o in r)if(("major"===o||"minor"===o||"patch"===o)&&r[o]!==n[o])return"pre"+o;return"prerelease"}for(var o in r)if(("major"===o||"minor"===o||"patch"===o)&&r[o]!==n[o])return o},t.compareIdentifiers=Q;var H=/^[0-9]+$/;function Q(e,t){var r=H.test(e),n=H.test(t);return r&&n&&(e=+e,t=+t),r&&!n?-1:n&&!r?1:e<t?-1:e>t?1:0}function Y(e,t,r){return new q(e,r).compare(new q(t,r))}function X(e,t,r){return Y(e,t,r)>0}function z(e,t,r){return Y(e,t,r)<0}function Z(e,t,r){return 0===Y(e,t,r)}function ee(e,t,r){return 0!==Y(e,t,r)}function te(e,t,r){return Y(e,t,r)>=0}function re(e,t,r){return Y(e,t,r)<=0}function ne(e,t,r,n){var o;switch(t){case"===":"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),o=e===r;break;case"!==":"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),o=e!==r;break;case"":case"=":case"==":o=Z(e,r,n);break;case"!=":o=ee(e,r,n);break;case">":o=X(e,r,n);break;case">=":o=te(e,r,n);break;case"<":o=z(e,r,n);break;case"<=":o=re(e,r,n);break;default:throw new TypeError("Invalid operator: "+t)}return o}function oe(e,t){if(t&&"object"==typeof t||(t={loose:!!t,includePrerelease:!1}),e instanceof oe){if(e.loose===!!t.loose)return e;e=e.value}if(!(this instanceof oe))return new oe(e,t);r("comparator",e,t),this.options=t,this.loose=!!t.loose,this.parse(e),this.semver===ie?this.value="":this.value=this.operator+this.semver.version,r("comp",this)}t.rcompareIdentifiers=function(e,t){return Q(t,e)},t.major=function(e,t){return new q(e,t).major},t.minor=function(e,t){return new q(e,t).minor},t.patch=function(e,t){return new q(e,t).patch},t.compare=Y,t.compareLoose=function(e,t){return Y(e,t,!0)},t.rcompare=function(e,t,r){return Y(t,e,r)},t.sort=function(e,r){return e.sort(function(e,n){return t.compare(e,n,r)})},t.rsort=function(e,r){return e.sort(function(e,n){return t.rcompare(e,n,r)})},t.gt=X,t.lt=z,t.eq=Z,t.neq=ee,t.gte=te,t.lte=re,t.cmp=ne,t.Comparator=oe;var ie={};function ae(e,t){if(t&&"object"==typeof t||(t={loose:!!t,includePrerelease:!1}),e instanceof ae)return e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease?e:new ae(e.raw,t);if(e instanceof oe)return new ae(e.value,t);if(!(this instanceof ae))return new ae(e,t);if(this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease,this.raw=e,this.set=e.split(/\s*\|\|\s*/).map(function(e){return this.parseRange(e.trim())},this).filter(function(e){return e.length}),!this.set.length)throw new TypeError("Invalid SemVer Range: "+e);this.format()}function se(e){return!e||"x"===e.toLowerCase()||"*"===e}function ce(e,t,r,n,o,i,a,s,c,l,u,p,f){return((t=se(r)?"":se(n)?">="+r+".0.0":se(o)?">="+r+"."+n+".0":">="+t)+" "+(s=se(c)?"":se(l)?"<"+(+c+1)+".0.0":se(u)?"<"+c+"."+(+l+1)+".0":p?"<="+c+"."+l+"."+u+"-"+p:"<="+s)).trim()}function le(e,t,n){for(var o=0;o<e.length;o++)if(!e[o].test(t))return!1;if(n||(n={}),t.prerelease.length&&!n.includePrerelease){for(o=0;o<e.length;o++)if(r(e[o].semver),e[o].semver!==ie&&e[o].semver.prerelease.length>0){var i=e[o].semver;if(i.major===t.major&&i.minor===t.minor&&i.patch===t.patch)return!0}return!1}return!0}function ue(e,t,r){try{t=new ae(t,r)}catch(e){return!1}return t.test(e)}function pe(e,t,r,n){var o,i,a,s,c;switch(e=new q(e,n),t=new ae(t,n),r){case">":o=X,i=re,a=z,s=">",c=">=";break;case"<":o=z,i=te,a=X,s="<",c="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(ue(e,t,n))return!1;for(var l=0;l<t.set.length;++l){var u=t.set[l],p=null,f=null;if(u.forEach(function(e){e.semver===ie&&(e=new oe(">=0.0.0")),p=p||e,f=f||e,o(e.semver,p.semver,n)?p=e:a(e.semver,f.semver,n)&&(f=e)}),p.operator===s||p.operator===c)return!1;if((!f.operator||f.operator===s)&&i(e,f.semver))return!1;if(f.operator===c&&a(e,f.semver))return!1}return!0}oe.prototype.parse=function(e){var t=this.options.loose?i[V]:i[R],r=e.match(t);if(!r)throw new TypeError("Invalid comparator: "+e);this.operator=r[1],"="===this.operator&&(this.operator=""),r[2]?this.semver=new q(r[2],this.options.loose):this.semver=ie},oe.prototype.toString=function(){return this.value},oe.prototype.test=function(e){return r("Comparator.test",e,this.options.loose),this.semver===ie||("string"==typeof e&&(e=new q(e,this.options)),ne(e,this.operator,this.semver,this.options))},oe.prototype.intersects=function(e,t){if(!(e instanceof oe))throw new TypeError("a Comparator is required");var r;if(t&&"object"==typeof t||(t={loose:!!t,includePrerelease:!1}),""===this.operator)return r=new ae(e.value,t),ue(this.value,r,t);if(""===e.operator)return r=new ae(this.value,t),ue(e.semver,r,t);var n=!(">="!==this.operator&&">"!==this.operator||">="!==e.operator&&">"!==e.operator),o=!("<="!==this.operator&&"<"!==this.operator||"<="!==e.operator&&"<"!==e.operator),i=this.semver.version===e.semver.version,a=!(">="!==this.operator&&"<="!==this.operator||">="!==e.operator&&"<="!==e.operator),s=ne(this.semver,"<",e.semver,t)&&(">="===this.operator||">"===this.operator)&&("<="===e.operator||"<"===e.operator),c=ne(this.semver,">",e.semver,t)&&("<="===this.operator||"<"===this.operator)&&(">="===e.operator||">"===e.operator);return n||o||i&&a||s||c},t.Range=ae,ae.prototype.format=function(){return this.range=this.set.map(function(e){return e.join(" ").trim()}).join("||").trim(),this.range},ae.prototype.toString=function(){return this.range},ae.prototype.parseRange=function(e){var t=this.options.loose;e=e.trim();var n=t?i[U]:i[L];e=e.replace(n,ce),r("hyphen replace",e),e=e.replace(i[G],"$1$2$3"),r("comparator trim",e,i[G]),e=(e=(e=e.replace(i[F],"$1~")).replace(i[D],"$1^")).split(/\s+/).join(" ");var o=t?i[V]:i[R],a=e.split(" ").map(function(e){return function(e,t){return r("comp",e,t),e=function(e,t){return e.trim().split(/\s+/).map(function(e){return function(e,t){r("caret",e,t),t&&"object"==typeof t||(t={loose:!!t,includePrerelease:!1});var n=t.loose?i[B]:i[M];return e.replace(n,function(t,n,o,i,a){var s;return r("caret",e,t,n,o,i,a),se(n)?s="":se(o)?s=">="+n+".0.0 <"+(+n+1)+".0.0":se(i)?s="0"===n?">="+n+"."+o+".0 <"+n+"."+(+o+1)+".0":">="+n+"."+o+".0 <"+(+n+1)+".0.0":a?(r("replaceCaret pr",a),"-"!==a.charAt(0)&&(a="-"+a),s="0"===n?"0"===o?">="+n+"."+o+"."+i+a+" <"+n+"."+o+"."+(+i+1):">="+n+"."+o+"."+i+a+" <"+n+"."+(+o+1)+".0":">="+n+"."+o+"."+i+a+" <"+(+n+1)+".0.0"):(r("no pr"),s="0"===n?"0"===o?">="+n+"."+o+"."+i+" <"+n+"."+o+"."+(+i+1):">="+n+"."+o+"."+i+" <"+n+"."+(+o+1)+".0":">="+n+"."+o+"."+i+" <"+(+n+1)+".0.0"),r("caret return",s),s})}(e,t)}).join(" ")}(e,t),r("caret",e),e=function(e,t){return e.trim().split(/\s+/).map(function(e){return function(e,t){t&&"object"==typeof t||(t={loose:!!t,includePrerelease:!1});var n=t.loose?i[N]:i[C];return e.replace(n,function(t,n,o,i,a){var s;return r("tilde",e,t,n,o,i,a),se(n)?s="":se(o)?s=">="+n+".0.0 <"+(+n+1)+".0.0":se(i)?s=">="+n+"."+o+".0 <"+n+"."+(+o+1)+".0":a?(r("replaceTilde pr",a),"-"!==a.charAt(0)&&(a="-"+a),s=">="+n+"."+o+"."+i+a+" <"+n+"."+(+o+1)+".0"):s=">="+n+"."+o+"."+i+" <"+n+"."+(+o+1)+".0",r("tilde return",s),s})}(e,t)}).join(" ")}(e,t),r("tildes",e),e=function(e,t){return r("replaceXRanges",e,t),e.split(/\s+/).map(function(e){return function(e,t){e=e.trim(),t&&"object"==typeof t||(t={loose:!!t,includePrerelease:!1});var n=t.loose?i[k]:i[E];return e.replace(n,function(t,n,o,i,a,s){r("xRange",e,t,n,o,i,a,s);var c=se(o),l=c||se(i),u=l||se(a),p=u;return"="===n&&p&&(n=""),c?t=">"===n||"<"===n?"<0.0.0":"*":n&&p?(l&&(i=0),u&&(a=0),">"===n?(n=">=",l?(o=+o+1,i=0,a=0):u&&(i=+i+1,a=0)):"<="===n&&(n="<",l?o=+o+1:i=+i+1),t=n+o+"."+i+"."+a):l?t=">="+o+".0.0 <"+(+o+1)+".0.0":u&&(t=">="+o+"."+i+".0 <"+o+"."+(+i+1)+".0"),r("xRange return",t),t})}(e,t)}).join(" ")}(e,t),r("xrange",e),e=function(e,t){return r("replaceStars",e,t),e.trim().replace(i[W],"")}(e,t),r("stars",e),e}(e,this.options)},this).join(" ").split(/\s+/);return this.options.loose&&(a=a.filter(function(e){return!!e.match(o)})),a=a.map(function(e){return new oe(e,this.options)},this)},ae.prototype.intersects=function(e,t){if(!(e instanceof ae))throw new TypeError("a Range is required");return this.set.some(function(r){return r.every(function(r){return e.set.some(function(e){return e.every(function(e){return r.intersects(e,t)})})})})},t.toComparators=function(e,t){return new ae(e,t).set.map(function(e){return e.map(function(e){return e.value}).join(" ").trim().split(" ")})},ae.prototype.test=function(e){if(!e)return!1;"string"==typeof e&&(e=new q(e,this.options));for(var t=0;t<this.set.length;t++)if(le(this.set[t],e,this.options))return!0;return!1},t.satisfies=ue,t.maxSatisfying=function(e,t,r){var n=null,o=null;try{var i=new ae(t,r)}catch(e){return null}return e.forEach(function(e){i.test(e)&&(n&&-1!==o.compare(e)||(o=new q(n=e,r)))}),n},t.minSatisfying=function(e,t,r){var n=null,o=null;try{var i=new ae(t,r)}catch(e){return null}return e.forEach(function(e){i.test(e)&&(n&&1!==o.compare(e)||(o=new q(n=e,r)))}),n},t.validRange=function(e,t){try{return new ae(e,t).range||"*"}catch(e){return null}},t.ltr=function(e,t,r){return pe(e,t,"<",r)},t.gtr=function(e,t,r){return pe(e,t,">",r)},t.outside=pe,t.prerelease=function(e,t){var r=J(e,t);return r&&r.prerelease.length?r.prerelease:null},t.intersects=function(e,t,r){return e=new ae(e,r),t=new ae(t,r),e.intersects(t)},t.coerce=function(e){if(e instanceof q)return e;if("string"!=typeof e)return null;var t=e.match(i[$]);return null==t?null:J((t[1]||"0")+"."+(t[2]||"0")+"."+(t[3]||"0"))}},function(e,t,r){const n=r(7),o=r(0),i=r(6),a=e=>{const t=e.split("node_modules/"),r=t[t.length-1];return"@"===r.charAt(0)?[r.split("/")[0],r.split("/")[1]].join("/"):r.split("/")[0]};e.exports={getnpmPackages:function(e,t){i.log("trace","getnpmPackages"),t||(t={});let r=null,n=null;return"string"==typeof e&&(e.includes("*")||e.includes("?")||e.includes("+")||e.includes("!")?r=e:e=e.split(",")),Promise.all(["npmPackages",i.getPackageJsonByPath("package.json").then(e=>Object.assign({},(e||{}).devDependencies||{},(e||{}).dependencies||{})).then(e=>(n=e,t.fullTree||t.duplicates||r?i.getAllPackageJsonPaths(r):Promise.resolve(Object.keys(e||[]).map(e=>o.join("node_modules",e,"package.json"))))).then(o=>!r&&"boolean"!=typeof e||t.fullTree?Array.isArray(e)?Promise.resolve((o||[]).filter(t=>e.includes(a(t)))):Promise.resolve(o):Promise.resolve((o||[]).filter(e=>Object.keys(n||[]).includes(a(e))))).then(e=>Promise.all([e,Promise.all(e.map(e=>i.getPackageJsonByPath(e)))])).then(e=>{const r=e[0];return e[1].reduce((e,o,a)=>o&&o.name?(e[o.name]||(e[o.name]={}),t.duplicates&&e[o.name].installed&&e[o.name].installed!==o.version&&i.uniq(e[o.name].duplicates=(e[o.name].duplicates||[]).concat(o.version)),1===(r[a].match(/node_modules/g)||[]).length&&(e[o.name].installed=o.version),n[o.name]&&(e[o.name].wanted=n[o.name]),e):e,{})}).then(r=>(t.showNotFound&&Array.isArray(e)&&e.forEach(e=>{r[e]||(r[e]="Not Found")}),r)).then(e=>i.sortObject(e))])},getnpmGlobalPackages:function(e,t){i.log("trace","getnpmGlobalPackages",e);let r=null;return"string"==typeof e?e.includes("*")||e.includes("?")||e.includes("+")||e.includes("!")?r=e:e=e.split(","):Array.isArray(e)||(e=!0),Promise.all(["npmGlobalPackages",i.run("npm get prefix --global").then(e=>new Promise((t,i)=>n(o.join(e,"lib","node_modules",r||"{*,@*/*}","package.json"),(e,r)=>{e||t(r),i(e)}))).then(t=>Promise.all(t.filter(t=>"boolean"==typeof e||null!==r||e.includes(a(t))).map(e=>i.getPackageJsonByFullPath(e)))).then(e=>e.reduce((e,t)=>t?Object.assign(e,{[t.name]:t.version}):e,{})).then(r=>(t.showNotFound&&Array.isArray(e)&&e.forEach(e=>{r[e]||(r[e]="Not Found")}),r))])}}},function(e,t,r){var n=r(0),o="win32"===process.platform,i=r(1),a=process.env.NODE_DEBUG&&/fs/.test(process.env.NODE_DEBUG);function s(e){return"function"==typeof e?e:function(){var e;if(a){var t=new Error;e=function(e){e&&(t.message=e.message,r(e=t))}}else e=r;return e;function r(e){if(e){if(process.throwDeprecation)throw e;if(!process.noDeprecation){var t="fs: missing callback "+(e.stack||e.message);process.traceDeprecation?console.trace(t):console.error(t)}}}}()}n.normalize;if(o)var c=/(.*?)(?:[\/\\]+|$)/g;else c=/(.*?)(?:[\/]+|$)/g;if(o)var l=/^(?:[a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/][^\\\/]+)?[\\\/]*/;else l=/^[\/]*/;t.realpathSync=function(e,t){if(e=n.resolve(e),t&&Object.prototype.hasOwnProperty.call(t,e))return t[e];var r,a,s,u,p=e,f={},h={};function d(){var t=l.exec(e);r=t[0].length,a=t[0],s=t[0],u="",o&&!h[s]&&(i.lstatSync(s),h[s]=!0)}for(d();r<e.length;){c.lastIndex=r;var m=c.exec(e);if(u=a,a+=m[0],s=u+m[1],r=c.lastIndex,!(h[s]||t&&t[s]===s)){var y;if(t&&Object.prototype.hasOwnProperty.call(t,s))y=t[s];else{var g=i.lstatSync(s);if(!g.isSymbolicLink()){h[s]=!0,t&&(t[s]=s);continue}var v=null;if(!o){var b=g.dev.toString(32)+":"+g.ino.toString(32);f.hasOwnProperty(b)&&(v=f[b])}null===v&&(i.statSync(s),v=i.readlinkSync(s)),y=n.resolve(u,v),t&&(t[s]=y),o||(f[b]=v)}e=n.resolve(y,e.slice(r)),d()}}return t&&(t[p]=e),e},t.realpath=function(e,t,r){if("function"!=typeof r&&(r=s(t),t=null),e=n.resolve(e),t&&Object.prototype.hasOwnProperty.call(t,e))return process.nextTick(r.bind(null,null,t[e]));var a,u,p,f,h=e,d={},m={};function y(){var t=l.exec(e);a=t[0].length,u=t[0],p=t[0],f="",o&&!m[p]?i.lstat(p,function(e){if(e)return r(e);m[p]=!0,g()}):process.nextTick(g)}function g(){if(a>=e.length)return t&&(t[h]=e),r(null,e);c.lastIndex=a;var n=c.exec(e);return f=u,u+=n[0],p=f+n[1],a=c.lastIndex,m[p]||t&&t[p]===p?process.nextTick(g):t&&Object.prototype.hasOwnProperty.call(t,p)?w(t[p]):i.lstat(p,v)}function v(e,n){if(e)return r(e);if(!n.isSymbolicLink())return m[p]=!0,t&&(t[p]=p),process.nextTick(g);if(!o){var a=n.dev.toString(32)+":"+n.ino.toString(32);if(d.hasOwnProperty(a))return b(null,d[a],p)}i.stat(p,function(e){if(e)return r(e);i.readlink(p,function(e,t){o||(d[a]=t),b(e,t)})})}function b(e,o,i){if(e)return r(e);var a=n.resolve(f,o);t&&(t[i]=a),w(a)}function w(t){e=n.resolve(t,e.slice(a)),y()}y()}},function(e,t,r){var n=r(43),o=r(44);e.exports=function(e){if(!e)return[];"{}"===e.substr(0,2)&&(e="\\{\\}"+e.substr(2));return function e(t,r){var i=[];var a=o("{","}",t);if(!a||/\$$/.test(a.pre))return[t];var c=/^-?\d+\.\.-?\d+(?:\.\.-?\d+)?$/.test(a.body);var l=/^[a-zA-Z]\.\.[a-zA-Z](?:\.\.-?\d+)?$/.test(a.body);var p=c||l;var y=a.body.indexOf(",")>=0;if(!p&&!y)return a.post.match(/,.*\}/)?(t=a.pre+"{"+a.body+s+a.post,e(t)):[t];var g;if(p)g=a.body.split(/\.\./);else if(1===(g=function e(t){if(!t)return[""];var r=[];var n=o("{","}",t);if(!n)return t.split(",");var i=n.pre;var a=n.body;var s=n.post;var c=i.split(",");c[c.length-1]+="{"+a+"}";var l=e(s);s.length&&(c[c.length-1]+=l.shift(),c.push.apply(c,l));r.push.apply(r,c);return r}(a.body)).length&&1===(g=e(g[0],!1).map(f)).length){var v=a.post.length?e(a.post,!1):[""];return v.map(function(e){return a.pre+g[0]+e})}var b=a.pre;var v=a.post.length?e(a.post,!1):[""];var w;if(p){var S=u(g[0]),P=u(g[1]),j=Math.max(g[0].length,g[1].length),O=3==g.length?Math.abs(u(g[2])):1,I=d,A=P<S;A&&(O*=-1,I=m);var x=g.some(h);w=[];for(var E=S;I(E,P);E+=O){var k;if(l)"\\"===(k=String.fromCharCode(E))&&(k="");else if(k=String(E),x){var $=j-k.length;if($>0){var T=new Array($+1).join("0");k=E<0?"-"+T+k.slice(1):T+k}}w.push(k)}}else w=n(g,function(t){return e(t,!1)});for(var F=0;F<w.length;F++)for(var C=0;C<v.length;C++){var N=b+w[F]+v[C];(!r||p||N)&&i.push(N)}return i}(function(e){return e.split("\\\\").join(i).split("\\{").join(a).split("\\}").join(s).split("\\,").join(c).split("\\.").join(l)}(e),!0).map(p)};var i="\0SLASH"+Math.random()+"\0",a="\0OPEN"+Math.random()+"\0",s="\0CLOSE"+Math.random()+"\0",c="\0COMMA"+Math.random()+"\0",l="\0PERIOD"+Math.random()+"\0";function u(e){return parseInt(e,10)==e?parseInt(e,10):e.charCodeAt(0)}function p(e){return e.split(i).join("\\").split(a).join("{").split(s).join("}").split(c).join(",").split(l).join(".")}function f(e){return"{"+e+"}"}function h(e){return/^-?0\d/.test(e)}function d(e,t){return e<=t}function m(e,t){return e>=t}},function(e,t){e.exports=function(e,t){for(var n=[],o=0;o<e.length;o++){var i=t(e[o],o);r(i)?n.push.apply(n,i):n.push(i)}return n};var r=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)}},function(e,t,r){function n(e,t,r){e instanceof RegExp&&(e=o(e,r)),t instanceof RegExp&&(t=o(t,r));var n=i(e,t,r);return n&&{start:n[0],end:n[1],pre:r.slice(0,n[0]),body:r.slice(n[0]+e.length,n[1]),post:r.slice(n[1]+t.length)}}function o(e,t){var r=t.match(e);return r?r[0]:null}function i(e,t,r){var n,o,i,a,s,c=r.indexOf(e),l=r.indexOf(t,c+1),u=c;if(c>=0&&l>0){for(n=[],i=r.length;u>=0&&!s;)u==c?(n.push(u),c=r.indexOf(e,u+1)):1==n.length?s=[n.pop(),l]:((o=n.pop())<i&&(i=o,a=l),l=r.indexOf(t,u+1)),u=c<l&&c>=0?c:l;n.length&&(s=[i,a])}return s}e.exports=n,n.range=i},function(e,t,r){try{var n=r(9);if("function"!=typeof n.inherits)throw"";e.exports=n.inherits}catch(t){e.exports=r(46)}},function(e,t){"function"==typeof Object.create?e.exports=function(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}:e.exports=function(e,t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}},function(e,t){e.exports=require("events")},function(e,t,r){e.exports=d,d.GlobSync=m;var n=r(1),o=r(13),i=r(8),a=(i.Minimatch,r(7).Glob,r(9),r(0)),s=r(14),c=r(10),l=r(15),u=(l.alphasort,l.alphasorti,l.setopts),p=l.ownProp,f=l.childrenIgnored,h=l.isIgnored;function d(e,t){if("function"==typeof t||3===arguments.length)throw new TypeError("callback provided to sync glob\nSee: https://github.com/isaacs/node-glob/issues/167");return new m(e,t).found}function m(e,t){if(!e)throw new Error("must provide pattern");if("function"==typeof t||3===arguments.length)throw new TypeError("callback provided to sync glob\nSee: https://github.com/isaacs/node-glob/issues/167");if(!(this instanceof m))return new m(e,t);if(u(this,e,t),this.noprocess)return this;var r=this.minimatch.set.length;this.matches=new Array(r);for(var n=0;n<r;n++)this._process(this.minimatch.set[n],n,!1);this._finish()}m.prototype._finish=function(){if(s(this instanceof m),this.realpath){var e=this;this.matches.forEach(function(t,r){var n=e.matches[r]=Object.create(null);for(var i in t)try{i=e._makeAbs(i),n[o.realpathSync(i,e.realpathCache)]=!0}catch(t){if("stat"!==t.syscall)throw t;n[e._makeAbs(i)]=!0}})}l.finish(this)},m.prototype._process=function(e,t,r){s(this instanceof m);for(var n,o=0;"string"==typeof e[o];)o++;switch(o){case e.length:return void this._processSimple(e.join("/"),t);case 0:n=null;break;default:n=e.slice(0,o).join("/")}var a,l=e.slice(o);null===n?a=".":c(n)||c(e.join("/"))?(n&&c(n)||(n="/"+n),a=n):a=n;var u=this._makeAbs(a);f(this,a)||(l[0]===i.GLOBSTAR?this._processGlobStar(n,a,u,l,t,r):this._processReaddir(n,a,u,l,t,r))},m.prototype._processReaddir=function(e,t,r,n,o,i){var s=this._readdir(r,i);if(s){for(var c=n[0],l=!!this.minimatch.negate,u=c._glob,p=this.dot||"."===u.charAt(0),f=[],h=0;h<s.length;h++){if("."!==(y=s[h]).charAt(0)||p)(l&&!e?!y.match(c):y.match(c))&&f.push(y)}var d=f.length;if(0!==d)if(1!==n.length||this.mark||this.stat){n.shift();for(h=0;h<d;h++){var m;y=f[h];m=e?[e,y]:[y],this._process(m.concat(n),o,i)}}else{this.matches[o]||(this.matches[o]=Object.create(null));for(var h=0;h<d;h++){var y=f[h];e&&(y="/"!==e.slice(-1)?e+"/"+y:e+y),"/"!==y.charAt(0)||this.nomount||(y=a.join(this.root,y)),this._emitMatch(o,y)}}}},m.prototype._emitMatch=function(e,t){if(!h(this,t)){var r=this._makeAbs(t);if(this.mark&&(t=this._mark(t)),this.absolute&&(t=r),!this.matches[e][t]){if(this.nodir){var n=this.cache[r];if("DIR"===n||Array.isArray(n))return}this.matches[e][t]=!0,this.stat&&this._stat(t)}}},m.prototype._readdirInGlobStar=function(e){if(this.follow)return this._readdir(e,!1);var t,r;try{r=n.lstatSync(e)}catch(e){if("ENOENT"===e.code)return null}var o=r&&r.isSymbolicLink();return this.symlinks[e]=o,o||!r||r.isDirectory()?t=this._readdir(e,!1):this.cache[e]="FILE",t},m.prototype._readdir=function(e,t){if(t&&!p(this.symlinks,e))return this._readdirInGlobStar(e);if(p(this.cache,e)){var r=this.cache[e];if(!r||"FILE"===r)return null;if(Array.isArray(r))return r}try{return this._readdirEntries(e,n.readdirSync(e))}catch(t){return this._readdirError(e,t),null}},m.prototype._readdirEntries=function(e,t){if(!this.mark&&!this.stat)for(var r=0;r<t.length;r++){var n=t[r];n="/"===e?e+n:e+"/"+n,this.cache[n]=!0}return this.cache[e]=t,t},m.prototype._readdirError=function(e,t){switch(t.code){case"ENOTSUP":case"ENOTDIR":var r=this._makeAbs(e);if(this.cache[r]="FILE",r===this.cwdAbs){var n=new Error(t.code+" invalid cwd "+this.cwd);throw n.path=this.cwd,n.code=t.code,n}break;case"ENOENT":case"ELOOP":case"ENAMETOOLONG":case"UNKNOWN":this.cache[this._makeAbs(e)]=!1;break;default:if(this.cache[this._makeAbs(e)]=!1,this.strict)throw t;this.silent||console.error("glob error",t)}},m.prototype._processGlobStar=function(e,t,r,n,o,i){var a=this._readdir(r,i);if(a){var s=n.slice(1),c=e?[e]:[],l=c.concat(s);this._process(l,o,!1);var u=a.length;if(!this.symlinks[r]||!i)for(var p=0;p<u;p++){if("."!==a[p].charAt(0)||this.dot){var f=c.concat(a[p],s);this._process(f,o,!0);var h=c.concat(a[p],n);this._process(h,o,!0)}}}},m.prototype._processSimple=function(e,t){var r=this._stat(e);if(this.matches[t]||(this.matches[t]=Object.create(null)),r){if(e&&c(e)&&!this.nomount){var n=/[\/\\]$/.test(e);"/"===e.charAt(0)?e=a.join(this.root,e):(e=a.resolve(this.root,e),n&&(e+="/"))}"win32"===process.platform&&(e=e.replace(/\\/g,"/")),this._emitMatch(t,e)}},m.prototype._stat=function(e){var t=this._makeAbs(e),r="/"===e.slice(-1);if(e.length>this.maxLength)return!1;if(!this.stat&&p(this.cache,t)){var o=this.cache[t];if(Array.isArray(o)&&(o="DIR"),!r||"DIR"===o)return o;if(r&&"FILE"===o)return!1}var i=this.statCache[t];if(!i){var a;try{a=n.lstatSync(t)}catch(e){if(e&&("ENOENT"===e.code||"ENOTDIR"===e.code))return this.statCache[t]=!1,!1}if(a&&a.isSymbolicLink())try{i=n.statSync(t)}catch(e){i=a}else i=a}this.statCache[t]=i;o=!0;return i&&(o=i.isDirectory()?"DIR":"FILE"),this.cache[t]=this.cache[t]||o,(!r||"FILE"!==o)&&o},m.prototype._mark=function(e){return l.mark(this,e)},m.prototype._makeAbs=function(e){return l.makeAbs(this,e)}},function(e,t,r){var n=r(16),o=Object.create(null),i=r(17);e.exports=n(function(e,t){return o[e]?(o[e].push(t),null):(o[e]=[t],function(e){return i(function t(){var r=o[e],n=r.length,i=function(e){for(var t=e.length,r=[],n=0;n<t;n++)r[n]=e[n];return r}(arguments);try{for(var a=0;a<n;a++)r[a].apply(null,i)}finally{r.length>n?(r.splice(0,n),process.nextTick(function(){t.apply(null,i)})):delete o[e]}})}(e))})},function(e,t,r){e.exports=l,l.sync=function(e,t){for(var r=c(e,t=t||{}),n=r.env,i=r.ext,l=r.extExe,u=[],p=0,f=n.length;p<f;p++){var h=n[p];'"'===h.charAt(0)&&'"'===h.slice(-1)&&(h=h.slice(1,-1));var d=o.join(h,e);!h&&/^\.[\\\/]/.test(e)&&(d=e.slice(0,2)+d);for(var m=0,y=i.length;m<y;m++){var g=d+i[m];try{if(a.sync(g,{pathExt:l})){if(!t.all)return g;u.push(g)}}catch(e){}}}if(t.all&&u.length)return u;if(t.nothrow)return null;throw s(e)};var n="win32"===process.platform||"cygwin"===process.env.OSTYPE||"msys"===process.env.OSTYPE,o=r(0),i=n?";":":",a=r(51);function s(e){var t=new Error("not found: "+e);return t.code="ENOENT",t}function c(e,t){var r=t.colon||i,o=t.path||process.env.PATH||"",a=[""];o=o.split(r);var s="";return n&&(o.unshift(process.cwd()),a=(s=t.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM").split(r),-1!==e.indexOf(".")&&""!==a[0]&&a.unshift("")),(e.match(/\//)||n&&e.match(/\\/))&&(o=[""]),{env:o,ext:a,extExe:s}}function l(e,t,r){"function"==typeof t&&(r=t,t={});var n=c(e,t),i=n.env,l=n.ext,u=n.extExe,p=[];!function n(c,f){if(c===f)return t.all&&p.length?r(null,p):r(s(e));var h=i[c];'"'===h.charAt(0)&&'"'===h.slice(-1)&&(h=h.slice(1,-1));var d=o.join(h,e);!h&&/^\.[\\\/]/.test(e)&&(d=e.slice(0,2)+d),function e(o,i){if(o===i)return n(c+1,f);var s=l[o];a(d+s,{pathExt:u},function(n,a){if(!n&&a){if(!t.all)return r(null,d+s);p.push(d+s)}return e(o+1,i)})}(0,l.length)}(0,i.length)}},function(e,t,r){var n;r(1);function o(e,t,r){if("function"==typeof t&&(r=t,t={}),!r){if("function"!=typeof Promise)throw new TypeError("callback not provided");return new Promise(function(r,n){o(e,t||{},function(e,t){e?n(e):r(t)})})}n(e,t||{},function(e,n){e&&("EACCES"===e.code||t&&t.ignoreErrors)&&(e=null,n=!1),r(e,n)})}n="win32"===process.platform||global.TESTING_WINDOWS?r(52):r(53),e.exports=o,o.sync=function(e,t){try{return n.sync(e,t||{})}catch(e){if(t&&t.ignoreErrors||"EACCES"===e.code)return!1;throw e}}},function(e,t,r){e.exports=i,i.sync=function(e,t){return o(n.statSync(e),e,t)};var n=r(1);function o(e,t,r){return!(!e.isSymbolicLink()&&!e.isFile())&&function(e,t){var r=void 0!==t.pathExt?t.pathExt:process.env.PATHEXT;if(!r)return!0;if(-1!==(r=r.split(";")).indexOf(""))return!0;for(var n=0;n<r.length;n++){var o=r[n].toLowerCase();if(o&&e.substr(-o.length).toLowerCase()===o)return!0}return!1}(t,r)}function i(e,t,r){n.stat(e,function(n,i){r(n,!n&&o(i,e,t))})}},function(e,t,r){e.exports=o,o.sync=function(e,t){return i(n.statSync(e),t)};var n=r(1);function o(e,t,r){n.stat(e,function(e,n){r(e,!e&&i(n,t))})}function i(e,t){return e.isFile()&&function(e,t){var r=e.mode,n=e.uid,o=e.gid,i=void 0!==t.uid?t.uid:process.getuid&&process.getuid(),a=void 0!==t.gid?t.gid:process.getgid&&process.getgid(),s=parseInt("100",8),c=parseInt("010",8),l=parseInt("001",8),u=s|c;return r&l||r&c&&o===a||r&s&&n===i||r&u&&0===i}(e,t)}},function(e,t){e.exports={androidSystemImages:/system-images;([\S \t]+)/g,androidAPILevels:/platforms;android-(\d+)[\S\s]/g,androidBuildTools:/build-tools;([\d|.]+)[\S\s]/g}},function(e,t,r){const n=r(56),o=r(6);function i(e,t){return o.log("trace","clean",e),Object.keys(e).reduce((r,n)=>!t.showNotFound&&"Not Found"===e[n]||"N/A"===e[n]||void 0===e[n]||0===Object.keys(e[n]).length?r:o.isObject(e[n])?Object.values(e[n]).every(e=>"N/A"===e||!t.showNotFound&&"Not Found"===e)?r:Object.assign(r,{[n]:i(e[n],t)}):Object.assign(r,{[n]:e[n]}),{})}function a(e,t){o.log("trace","formatHeaders"),t||(t={type:"underline"});const r={underline:["[4m","[0m"]};return e.slice().split("\n").map(e=>{if(":"===e.slice("-1")){const n=e.match(/^[\s]*/g)[0];return`${n}${r[t.type][0]}${e.slice(n.length)}${r[t.type][1]}`}return e}).join("\n")}function s(e){return o.log("trace","formatPackages"),e.npmPackages?Object.assign(e,{npmPackages:Object.entries(e.npmPackages||{}).reduce((e,t)=>{const r=t[0],n=t[1];if("Not Found"===n)return Object.assign(e,{[r]:n});const o=n.wanted?`${n.wanted} =>`:"",i=Array.isArray(n.installed)?n.installed.join(", "):n.installed,a=n.duplicates?`(${n.duplicates.join(", ")})`:"";return Object.assign(e,{[r]:`${o} ${i} ${a}`})},{})}):e}function c(e,t,r){return r||(r={emptyMessage:"None"}),Array.isArray(t)&&(t=t.length>0?t.join(", "):r.emptyMessage),{[e]:t}}function l(e){return o.log("trace","serializeArrays"),function e(t,r){return Object.entries(t).reduce((t,n)=>{const i=n[0],a=n[1];return o.isObject(a)?Object.assign(t,{[i]:e(a,r)}):Object.assign(t,r(i,a))},{})}(e,c)}function u(e){return o.log("trace","serializeVersionsAndPaths"),Object.entries(e).reduce((e,t)=>Object.assign(e,{[t[0]]:Object.entries(t[1]).reduce((e,t)=>{const r=t[0],n=t[1];return n.version?Object.assign(e,{[r]:[n.version,n.path].filter(Boolean).join(" - ")}):Object.assign(e,{[r]:[n][0]})},{})},{}),{})}function p(e){return n(e,{indent:"  ",prefix:"\n",postfix:"\n"})}function f(e){return e.slice().split("\n").map(e=>{if(""!==e){const t=":"===e.slice("-1"),r=e.search(/\S|$/);return t?`${"#".repeat(r/2+1)} `+e.slice(r):" - "+e.slice(r)}return""}).join("\n")}function h(e,t){return t||(t={indent:"  "}),JSON.stringify(e,null,t.indent)}e.exports={json:function(e,t){return o.log("trace","formatToJson"),t||(t={}),e=o.pipe([()=>i(e,t),t.title?e=>({[t.title]:e}):o.noop,h])(e),e=t.console?`\n${e}\n`:e},markdown:function(e,t){return o.log("trace","formatToMarkdown"),o.pipe([()=>i(e,t),s,l,u,p,f,t.title?e=>`\n# ${t.title}${e}`:o.noop])(e,t)},yaml:function(e,t){return o.log("trace","formatToYaml",t),o.pipe([()=>i(e,t),s,l,u,t.title?e=>({[t.title]:e}):o.noop,p,t.console?a:o.noop])(e,t)}}},function(e,t,r){var n=r(57),o=r(58),i=r(62),a=["object","array"];e.exports=function(e,t){var r=o(t),s=r.colors,c=r.prefix,l=r.postfix,u=r.dateToString,p=r.errorToString,f=r.indent;function h(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(0===Object.keys(e).length)return" {}";var o="\n",s=i(t,f);return Object.keys(e).forEach(function(c){var l=e[c],u=n(l),p=i(r,"  "),f=-1!==a.indexOf(u)?"":" ",h=m(u,l,t+1,r);o+=`${p}${s}${c}:${f}${h}\n`}),o.substring(0,o.length-1)}function d(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(0===e.length)return" []";var o="\n",a=i(t,f);return e.forEach(function(e){var s=n(e),c=i(r,"  "),l=m(s,e,t,r+1).toString().trimLeft();o+=`${c}${a}- ${l}\n`}),o.substring(0,o.length-1)}function m(e,t,r,n){switch(e){case"array":return d(t,r,n);case"object":return h(t,r,n);case"string":return s.string(t);case"symbol":return s.symbol(t.toString());case"number":return s.number(t);case"boolean":return s.boolean(t);case"null":return s.null("null");case"undefined":return s.undefined("undefined");case"date":return s.date(u(t));case"error":return s.error(p(t));default:return t&&t.toString?t.toString():Object.prototype.toString.call(t)}}var y="";return"object"===n(e)&&Object.keys(e).length>0?y=h(e):"array"===n(e)&&e.length>0&&(y=d(e)),0===y.length?"":`${c}${y.slice(1)}${l}`}},function(e,t,r){e.exports=function(e){return Array.isArray(e)?"array":e instanceof Date?"date":e instanceof Error?"error":null===e?"null":"object"==typeof e&&"[object Object]"===Object.prototype.toString.call(e)?"object":typeof e}},function(e,t,r){var n=r(59),o=r(60),i=r(61),a=" ",s="\n",c="";function l(e,t){return void 0===e?t:e}e.exports=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{indent:l(e.indent,a),prefix:l(e.prefix,s),postfix:l(e.postfix,c),errorToString:e.errorToString||n,dateToString:e.dateToString||o,colors:Object.assign({},i,e.colors)}}},function(e,t,r){e.exports=function(e){return Error.prototype.toString.call(e)}},function(e,t,r){e.exports=function(e){return`new Date(${Date.prototype.toISOString.call(e)})`}},function(e,t,r){function n(e){return e}e.exports={date:n,error:n,symbol:n,string:n,number:n,boolean:n,null:n,undefined:n}},function(e,t,r){e.exports=function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"  ",r="",n=0;n<e;n+=1)r+=t;return r}},function(e,t){e.exports={defaults:{System:["OS","CPU","Memory","Container","Shell"],Binaries:["Node","Yarn","npm","Watchman"],Utilities:["CMake","Make","GCC","Git"],Servers:["Apache","Nginx"],Virtualization:["Docker","Parallels","VirtualBox","VMware Fusion"],SDKs:["iOS SDK","Android SDK"],IDEs:["Android Studio","Atom","Emacs","IntelliJ","NVim","Nano","PhpStorm","Sublime Text","VSCode","Vim","WebStorm","Xcode"],Languages:["Bash","Go","Elixir","Java","Perl","PHP","Python","Ruby","Rust","Scala"],Databases:["MongoDB","MySQL","PostgreSQL","SQLite"],Browsers:["Chrome","Chrome Canary","Edge","Firefox","Firefox Developer Edition","Firefox Nightly","Internet Explorer","Safari","Safari Technology Preview"],npmPackages:null,npmGlobalPackages:null},jest:{System:["OS","CPU"],Binaries:["Node","Yarn","npm"],npmPackages:["jest"]},"react-native":{System:["OS","CPU"],Binaries:["Node","Yarn","npm","Watchman"],SDKs:["iOS SDK","Android SDK"],IDEs:["Android Studio","Xcode"],npmPackages:["react","react-native"],npmGlobalPackages:["react-native-cli"]},webpack:{System:["OS","CPU"],Binaries:["Node","Yarn","npm"],npmPackages:"*webpack*",npmGlobalPackages:["webpack","webpack-cli"]},"styled-components":{System:["OS","CPU"],Binaries:["Node","Yarn","npm"],Browsers:["Chrome","Firefox","Safari"],npmPackages:"*styled-components*"},"create-react-app":{System:["OS","CPU"],Binaries:["Node","npm","Yarn"],Browsers:["Chrome","Edge","Internet Explorer","Firefox","Safari"],npmPackages:["react","react-dom","react-scripts"],npmGlobalPackages:["create-react-app"],options:{duplicates:!0,showNotFound:!0}},apollo:{System:["OS"],Binaries:["Node","npm","Yarn"],Browsers:["Chrome","Edge","Firefox","Safari"],npmPackages:"*apollo*",npmGlobalPackages:"*apollo*"},"react-native-web":{System:["OS","CPU"],Binaries:["Node","npm","Yarn"],Browsers:["Chrome","Edge","Internet Explorer","Firefox","Safari"],npmPackages:["react","react-native-web"],options:{showNotFound:!0}}}},function(e,t,r){var n=r(2),o=r(18),i=r(27),a=r(28),s=a(),c=r(78),l=Array.prototype.slice,u=function(e,t){return o.RequireObjectCoercible(e),s.apply(e,l.call(arguments,1))};n(u,{getPolyfill:a,implementation:i,shim:c}),e.exports=u},function(e,t,r){var n=Object.prototype.hasOwnProperty,o=Object.prototype.toString,i=Array.prototype.slice,a=r(66),s=Object.prototype.propertyIsEnumerable,c=!s.call({toString:null},"toString"),l=s.call(function(){},"prototype"),u=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],p=function(e){var t=e.constructor;return t&&t.prototype===e},f={$applicationCache:!0,$console:!0,$external:!0,$frame:!0,$frameElement:!0,$frames:!0,$innerHeight:!0,$innerWidth:!0,$outerHeight:!0,$outerWidth:!0,$pageXOffset:!0,$pageYOffset:!0,$parent:!0,$scrollLeft:!0,$scrollTop:!0,$scrollX:!0,$scrollY:!0,$self:!0,$webkitIndexedDB:!0,$webkitStorageInfo:!0,$window:!0},h=function(){if("undefined"==typeof window)return!1;for(var e in window)try{if(!f["$"+e]&&n.call(window,e)&&null!==window[e]&&"object"==typeof window[e])try{p(window[e])}catch(e){return!0}}catch(e){return!0}return!1}(),d=function(e){var t=null!==e&&"object"==typeof e,r="[object Function]"===o.call(e),i=a(e),s=t&&"[object String]"===o.call(e),f=[];if(!t&&!r&&!i)throw new TypeError("Object.keys called on a non-object");var d=l&&r;if(s&&e.length>0&&!n.call(e,0))for(var m=0;m<e.length;++m)f.push(String(m));if(i&&e.length>0)for(var y=0;y<e.length;++y)f.push(String(y));else for(var g in e)d&&"prototype"===g||!n.call(e,g)||f.push(String(g));if(c)for(var v=function(e){if("undefined"==typeof window||!h)return p(e);try{return p(e)}catch(e){return!1}}(e),b=0;b<u.length;++b)v&&"constructor"===u[b]||!n.call(e,u[b])||f.push(u[b]);return f};d.shim=function(){if(Object.keys){if(!function(){return 2===(Object.keys(arguments)||"").length}(1,2)){var e=Object.keys;Object.keys=function(t){return a(t)?e(i.call(t)):e(t)}}}else Object.keys=d;return Object.keys||d},e.exports=d},function(e,t,r){var n=Object.prototype.toString;e.exports=function(e){var t=n.call(e),r="[object Arguments]"===t;return r||(r="[object Array]"!==t&&null!==e&&"object"==typeof e&&"number"==typeof e.length&&e.length>=0&&"[object Function]"===n.call(e.callee)),r}},function(e,t,r){var n=Array.prototype.slice,o=Object.prototype.toString;e.exports=function(e){var t=this;if("function"!=typeof t||"[object Function]"!==o.call(t))throw new TypeError("Function.prototype.bind called on incompatible "+t);for(var r,i=n.call(arguments,1),a=Math.max(0,t.length-i.length),s=[],c=0;c<a;c++)s.push("$"+c);if(r=Function("binder","return function ("+s.join(",")+"){ return binder.apply(this,arguments); }")(function(){if(this instanceof r){var o=t.apply(this,i.concat(n.call(arguments)));return Object(o)===o?o:this}return t.apply(e,i.concat(n.call(arguments)))}),t.prototype){var l=function(){};l.prototype=t.prototype,r.prototype=new l,l.prototype=null}return r}},function(e,t,r){e.exports=r(69)},function(e,t,r){var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator,o=r(20),i=r(11),a=r(70),s=r(71);e.exports=function(e){if(o(e))return e;var t,r="default";if(arguments.length>1&&(arguments[1]===String?r="string":arguments[1]===Number&&(r="number")),n&&(Symbol.toPrimitive?t=function(e,t){var r=e[t];if(null!=r){if(!i(r))throw new TypeError(r+" returned for property "+t+" of object "+e+" is not a function");return r}}(e,Symbol.toPrimitive):s(e)&&(t=Symbol.prototype.valueOf)),void 0!==t){var c=t.call(e,r);if(o(c))return c;throw new TypeError("unable to convert exotic object to primitive")}return"default"===r&&(a(e)||s(e))&&(r="string"),function(e,t){if(null==e)throw new TypeError("Cannot call method on "+e);if("string"!=typeof t||"number"!==t&&"string"!==t)throw new TypeError('hint must be "string" or "number"');var r,n,a,s="string"===t?["toString","valueOf"]:["valueOf","toString"];for(a=0;a<s.length;++a)if(r=e[s[a]],i(r)&&(n=r.call(e),o(n)))return n;throw new TypeError("No default value")}(e,"default"===r?"number":r)}},function(e,t,r){var n=Date.prototype.getDay,o=Object.prototype.toString,i="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag;e.exports=function(e){return"object"==typeof e&&null!==e&&(i?function(e){try{return n.call(e),!0}catch(e){return!1}}(e):"[object Date]"===o.call(e))}},function(e,t,r){var n=Object.prototype.toString;if(r(72)()){var o=Symbol.prototype.toString,i=/^Symbol\(.*\)$/;e.exports=function(e){if("symbol"==typeof e)return!0;if("[object Symbol]"!==n.call(e))return!1;try{return function(e){return"symbol"==typeof e.valueOf()&&i.test(o.call(e))}(e)}catch(e){return!1}}}else e.exports=function(e){return!1}},function(e,t,r){var n=global.Symbol,o=r(73);e.exports=function(){return"function"==typeof n&&("function"==typeof Symbol&&("symbol"==typeof n("foo")&&("symbol"==typeof Symbol("bar")&&o())))}},function(e,t,r){e.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),r=Object(t);if("string"==typeof t)return!1;if("[object Symbol]"!==Object.prototype.toString.call(t))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(t in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var n=Object.getOwnPropertySymbols(e);if(1!==n.length||n[0]!==t)return!1;if(!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var o=Object.getOwnPropertyDescriptor(e,t);if(42!==o.value||!0!==o.enumerable)return!1}return!0}},function(e,t){e.exports=function(e){return null===e||"function"!=typeof e&&"object"!=typeof e}},function(e,t,r){var n=r(21),o=n("%Object%"),i=n("%TypeError%"),a=n("%String%"),s=r(22),c=r(23),l=r(25),u=r(26),p=r(11),f=r(76),h=r(3),d={ToPrimitive:f,ToBoolean:function(e){return!!e},ToNumber:function(e){return+e},ToInteger:function(e){var t=this.ToNumber(e);return s(t)?0:0!==t&&c(t)?l(t)*Math.floor(Math.abs(t)):t},ToInt32:function(e){return this.ToNumber(e)>>0},ToUint32:function(e){return this.ToNumber(e)>>>0},ToUint16:function(e){var t=this.ToNumber(e);if(s(t)||0===t||!c(t))return 0;var r=l(t)*Math.floor(Math.abs(t));return u(r,65536)},ToString:function(e){return a(e)},ToObject:function(e){return this.CheckObjectCoercible(e),o(e)},CheckObjectCoercible:function(e,t){if(null==e)throw new i(t||"Cannot call method on "+e);return e},IsCallable:p,SameValue:function(e,t){return e===t?0!==e||1/e==1/t:s(e)&&s(t)},Type:function(e){return null===e?"Null":void 0===e?"Undefined":"function"==typeof e||"object"==typeof e?"Object":"number"==typeof e?"Number":"boolean"==typeof e?"Boolean":"string"==typeof e?"String":void 0},IsPropertyDescriptor:function(e){if("Object"!==this.Type(e))return!1;var t={"[[Configurable]]":!0,"[[Enumerable]]":!0,"[[Get]]":!0,"[[Set]]":!0,"[[Value]]":!0,"[[Writable]]":!0};for(var r in e)if(h(e,r)&&!t[r])return!1;var n=h(e,"[[Value]]"),o=h(e,"[[Get]]")||h(e,"[[Set]]");if(n&&o)throw new i("Property Descriptors may not be both accessor and data descriptors");return!0},IsAccessorDescriptor:function(e){if(void 0===e)return!1;if(!this.IsPropertyDescriptor(e))throw new i("Desc must be a Property Descriptor");return!(!h(e,"[[Get]]")&&!h(e,"[[Set]]"))},IsDataDescriptor:function(e){if(void 0===e)return!1;if(!this.IsPropertyDescriptor(e))throw new i("Desc must be a Property Descriptor");return!(!h(e,"[[Value]]")&&!h(e,"[[Writable]]"))},IsGenericDescriptor:function(e){if(void 0===e)return!1;if(!this.IsPropertyDescriptor(e))throw new i("Desc must be a Property Descriptor");return!this.IsAccessorDescriptor(e)&&!this.IsDataDescriptor(e)},FromPropertyDescriptor:function(e){if(void 0===e)return e;if(!this.IsPropertyDescriptor(e))throw new i("Desc must be a Property Descriptor");if(this.IsDataDescriptor(e))return{value:e["[[Value]]"],writable:!!e["[[Writable]]"],enumerable:!!e["[[Enumerable]]"],configurable:!!e["[[Configurable]]"]};if(this.IsAccessorDescriptor(e))return{get:e["[[Get]]"],set:e["[[Set]]"],enumerable:!!e["[[Enumerable]]"],configurable:!!e["[[Configurable]]"]};throw new i("FromPropertyDescriptor must be called with a fully populated Property Descriptor")},ToPropertyDescriptor:function(e){if("Object"!==this.Type(e))throw new i("ToPropertyDescriptor requires an object");var t={};if(h(e,"enumerable")&&(t["[[Enumerable]]"]=this.ToBoolean(e.enumerable)),h(e,"configurable")&&(t["[[Configurable]]"]=this.ToBoolean(e.configurable)),h(e,"value")&&(t["[[Value]]"]=e.value),h(e,"writable")&&(t["[[Writable]]"]=this.ToBoolean(e.writable)),h(e,"get")){var r=e.get;if(void 0!==r&&!this.IsCallable(r))throw new TypeError("getter must be a function");t["[[Get]]"]=r}if(h(e,"set")){var n=e.set;if(void 0!==n&&!this.IsCallable(n))throw new i("setter must be a function");t["[[Set]]"]=n}if((h(t,"[[Get]]")||h(t,"[[Set]]"))&&(h(t,"[[Value]]")||h(t,"[[Writable]]")))throw new i("Invalid property descriptor. Cannot both specify accessors and a value or writable attribute");return t}};e.exports=d},function(e,t,r){var n=Object.prototype.toString,o=r(20),i=r(11),a=function(e){var t;if((t=arguments.length>1?arguments[1]:"[object Date]"===n.call(e)?String:Number)===String||t===Number){var r,a,s=t===String?["toString","valueOf"]:["valueOf","toString"];for(a=0;a<s.length;++a)if(i(e[s[a]])&&(r=e[s[a]](),o(r)))return r;throw new TypeError("No default value")}throw new TypeError("invalid [[DefaultValue]] hint supplied")};e.exports=function(e){return o(e)?e:arguments.length>1?a(e,arguments[1]):a(e)}},function(e,t,r){var n=r(3),o=RegExp.prototype.exec,i=Object.getOwnPropertyDescriptor,a=Object.prototype.toString,s="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag;e.exports=function(e){if(!e||"object"!=typeof e)return!1;if(!s)return"[object RegExp]"===a.call(e);var t=i(e,"lastIndex");return!(!t||!n(t,"value"))&&function(e){try{var t=e.lastIndex;return e.lastIndex=0,o.call(e),!0}catch(e){return!1}finally{e.lastIndex=t}}(e)}},function(e,t,r){var n=r(2),o=r(28);e.exports=function(){var e=o();return n(Array.prototype,{includes:e},{includes:function(){return Array.prototype.includes!==e}}),e}},function(e,t,r){var n=r(2),o=r(29),i=r(31),a=r(81),s=i();n(s,{getPolyfill:i,implementation:o,shim:a}),e.exports=s},function(e,t,r){var n=r(19),o=r(24),i=o(o({},n),{SameValueNonNumber:function(e,t){if("number"==typeof e||typeof e!=typeof t)throw new TypeError("SameValueNonNumber requires two non-number values of the same type.");return this.SameValue(e,t)}});e.exports=i},function(e,t,r){var n=r(31),o=r(2);e.exports=function(){var e=n();return o(Object,{entries:e},{entries:function(){return Object.entries!==e}}),e}},function(e,t,r){var n=r(2),o=r(32),i=r(33),a=r(83),s=i();n(s,{getPolyfill:i,implementation:o,shim:a}),e.exports=s},function(e,t,r){var n=r(33),o=r(2);e.exports=function(){var e=n();return o(Object,{values:e},{values:function(){return Object.values!==e}}),e}},function(e,t,r){const n=r(34),o=r(85)(process.argv.slice(2));o.console=!0,o.help||o._.indexOf("help")>-1?(console.log("\n  ,,',                                  ,,             ,,,,,,           ,',,\n ,,,                                                  ,,,                  ,,,\n ,,       ,,,,,    ,,,,,,   ,,,     ,,  ,,  .,,,,,,   ,,,,,,,   ,,,,,       ,,\n ,,     ,,    ,,  ,,,   ,,,  ,,    ,,,  ,,  ,,,   ,,, ,,      ,,,   ,,,     ,,\n ,,,   ,,     .,, ,,,    ,,  ,,,   ,,   ,,  ,,,    ,, ,,     ,,      ,,     ,,,\n ,,    ,,,,,,,,,, ,,,    ,,   ,,  ,,    ,,  ,,,    ,, ,,     ,,      ,,     ,,\n ,,    ,,,        ,,,    ,,    ,,,,,    ,,  ,,,    ,, ,,     ,,,    ,,,     ,,\n ,,      ,,,,,,,  ,,,    ,,     ,,,     ,,  ,,,    ,, ,,       ,,,,,,,      ,,\n ,,,                                                                       ,,,\n  ,,,'                                                                  ',,,\n\n  VERSION: 6.0.0\n\n  USAGE:\n\n    `envinfo` || `npx envinfo`\n\n  OPTIONS:\n\n    --system               Print general system info such as OS, CPU, Memory and Shell\n    --browsers             Get version numbers of installed web browsers\n    --SDKs                 Get platforms, build tools and SDKs of iOS and Android\n    --IDEs                 Get version numbers of installed IDEs\n    --languages            Get version numbers of installed languages such as Java, Python, PHP, etc\n    --binaries             Get version numbers of node, npm, watchman, etc\n    --npmPackages          Get version numbers of locally installed npm packages - glob, string, or comma delimited list\n    --npmGlobalPackages    Get version numbers of globally installed npm packages\n\n    --duplicates           Mark duplicate npm packages inside parentheses eg. (2.1.4)\n    --fullTree             Traverse entire node_modules dependency tree, not just top level\n\n    --markdown             Print output in markdown format\n    --json                 Print output in JSON format\n    --console              Print to console (defaults to on for CLI usage, off for programmatic usage)\n    --showNotFound         Don't filter out values marked 'Not Found'\n    --title                Give your report a top level title ie 'Environment Report'\n\n    --clipboard            *Removed - use clipboardy or clipboard-cli directly*\n  "),process.exit(0)):(o.version||o.v||o._.indexOf("version")>-1)&&(console.log("6.0.0"),process.exit(0)),n.cli(o)},function(e,t){function r(e){return"number"==typeof e||(!!/^0x[0-9a-f]+$/i.test(e)||/^[-+]?(?:\d+(?:\.\d*)?|\.\d+)(e[-+]?\d+)?$/.test(e))}e.exports=function(e,t){t||(t={});var n={bools:{},strings:{},unknownFn:null};"function"==typeof t.unknown&&(n.unknownFn=t.unknown),"boolean"==typeof t.boolean&&t.boolean?n.allBools=!0:[].concat(t.boolean).filter(Boolean).forEach(function(e){n.bools[e]=!0});var o={};Object.keys(t.alias||{}).forEach(function(e){o[e]=[].concat(t.alias[e]),o[e].forEach(function(t){o[t]=[e].concat(o[e].filter(function(e){return t!==e}))})}),[].concat(t.string).filter(Boolean).forEach(function(e){n.strings[e]=!0,o[e]&&(n.strings[o[e]]=!0)});var i=t.default||{},a={_:[]};Object.keys(n.bools).forEach(function(e){c(e,void 0!==i[e]&&i[e])});var s=[];function c(e,t,i){if(!i||!n.unknownFn||function(e,t){return n.allBools&&/^--[^=]+$/.test(t)||n.strings[e]||n.bools[e]||o[e]}(e,i)||!1!==n.unknownFn(i)){var s=!n.strings[e]&&r(t)?Number(t):t;l(a,e.split("."),s),(o[e]||[]).forEach(function(e){l(a,e.split("."),s)})}}function l(e,t,r){var o=e;t.slice(0,-1).forEach(function(e){void 0===o[e]&&(o[e]={}),o=o[e]});var i=t[t.length-1];void 0===o[i]||n.bools[i]||"boolean"==typeof o[i]?o[i]=r:Array.isArray(o[i])?o[i].push(r):o[i]=[o[i],r]}function u(e){return o[e].some(function(e){return n.bools[e]})}-1!==e.indexOf("--")&&(s=e.slice(e.indexOf("--")+1),e=e.slice(0,e.indexOf("--")));for(var p=0;p<e.length;p++){var f=e[p];if(/^--.+=/.test(f)){var h=f.match(/^--([^=]+)=([\s\S]*)$/),d=h[1],m=h[2];n.bools[d]&&(m="false"!==m),c(d,m,f)}else if(/^--no-.+/.test(f)){c(d=f.match(/^--no-(.+)/)[1],!1,f)}else if(/^--.+/.test(f)){d=f.match(/^--(.+)/)[1];void 0===(b=e[p+1])||/^-/.test(b)||n.bools[d]||n.allBools||o[d]&&u(d)?/^(true|false)$/.test(b)?(c(d,"true"===b,f),p++):c(d,!n.strings[d]||"",f):(c(d,b,f),p++)}else if(/^-[^-]+/.test(f)){for(var y=f.slice(1,-1).split(""),g=!1,v=0;v<y.length;v++){var b;if("-"!==(b=f.slice(v+2))){if(/[A-Za-z]/.test(y[v])&&/=/.test(b)){c(y[v],b.split("=")[1],f),g=!0;break}if(/[A-Za-z]/.test(y[v])&&/-?\d+(\.\d*)?(e-?\d+)?$/.test(b)){c(y[v],b,f),g=!0;break}if(y[v+1]&&y[v+1].match(/\W/)){c(y[v],f.slice(v+2),f),g=!0;break}c(y[v],!n.strings[y[v]]||"",f)}else c(y[v],b,f)}d=f.slice(-1)[0];g||"-"===d||(!e[p+1]||/^(-|--)[^-]/.test(e[p+1])||n.bools[d]||o[d]&&u(d)?e[p+1]&&/true|false/.test(e[p+1])?(c(d,"true"===e[p+1],f),p++):c(d,!n.strings[d]||"",f):(c(d,e[p+1],f),p++))}else if(n.unknownFn&&!1===n.unknownFn(f)||a._.push(n.strings._||!r(f)?f:Number(f)),t.stopEarly){a._.push.apply(a._,e.slice(p+1));break}}return Object.keys(i).forEach(function(e){var t,r,n;t=a,r=e.split("."),n=t,r.slice(0,-1).forEach(function(e){n=n[e]||{}}),r[r.length-1]in n||(l(a,e.split("."),i[e]),(o[e]||[]).forEach(function(t){l(a,t.split("."),i[e])}))}),t["--"]?(a["--"]=new Array,s.forEach(function(e){a["--"].push(e)})):s.forEach(function(e){a._.push(e)}),a}}]);