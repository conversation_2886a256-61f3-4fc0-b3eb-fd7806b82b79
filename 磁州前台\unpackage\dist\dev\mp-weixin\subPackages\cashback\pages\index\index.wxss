/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-3d430b98, html.data-v-3d430b98, #app.data-v-3d430b98, .index-container.data-v-3d430b98 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.cashback-container.data-v-3d430b98 {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.content-container.data-v-3d430b98 {
  padding-top: calc(var(--status-bar-height) + 44px);
  padding-bottom: 20px;
}
.search-section.data-v-3d430b98 {
  padding: 16px;
  background: linear-gradient(135deg, #AB47BC 0%, #9C27B0 100%);
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  padding-top: 24px;
}
.search-bar.data-v-3d430b98 {
  display: flex;
  align-items: center;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 10px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.search-bar .search-icon.data-v-3d430b98 {
  margin-right: 8px;
}
.search-bar .search-placeholder.data-v-3d430b98 {
  color: #999999;
  font-size: 14px;
}

/* 商品链接粘贴模块 */
.paste-link-section.data-v-3d430b98 {
  margin: 16px;
  padding: 16px;
  background-color: #FFFFFF;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
}
.paste-link-section.data-v-3d430b98::after {
  content: "";
  display: var(--009dcc79);
  position: absolute;
  top: 0;
  right: 0;
  width: 60px;
  height: 60px;
  background-color: #9C27B0;
  border-radius: 0 16px 0 60px;
  z-index: 1;
}
.paste-link-section.data-v-3d430b98::before {
  content: "已检测";
  display: var(--009dcc79);
  position: absolute;
  top: 12px;
  right: 8px;
  color: #FFFFFF;
  font-size: 12px;
  font-weight: 500;
  transform: rotate(45deg);
  z-index: 2;
}
.paste-link-info.data-v-3d430b98 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}
.paste-link-info .paste-link-title.data-v-3d430b98 {
  font-size: 14px;
  color: #333333;
}
.paste-link-info .paste-link-title .highlight.data-v-3d430b98 {
  color: #9C27B0;
  font-weight: 500;
}
.paste-link-info .paste-link-tutorial.data-v-3d430b98 {
  font-size: 14px;
  color: #9C27B0;
  background-color: rgba(156, 39, 176, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
}
.paste-link-input.data-v-3d430b98 {
  background-color: #F5F5F5;
  border-radius: 12px;
  padding: 12px;
  margin-bottom: 12px;
}
.paste-link-input .input-placeholder.data-v-3d430b98 {
  font-size: 14px;
  color: #999999;
  display: block;
  margin-bottom: 4px;
}
.paste-link-input .example-link.data-v-3d430b98 {
  display: flex;
}
.paste-link-input .example-link .example-text.data-v-3d430b98 {
  font-size: 12px;
  color: #999999;
  margin-right: 4px;
}
.paste-link-input .example-link .example-url.data-v-3d430b98 {
  font-size: 12px;
  color: #999999;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.platform-icons.data-v-3d430b98 {
  display: flex;
  margin-bottom: 12px;
  margin-top: 12px;
  justify-content: center;
}
.platform-icons .platform-icon.data-v-3d430b98 {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 8px;
  margin-left: 8px;
}
.paste-button.data-v-3d430b98 {
  width: 100%;
  height: 44px;
  background-color: #9C27B0;
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 500;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  position: relative;
  overflow: hidden;
}
.paste-button.data-v-3d430b98::after {
  content: "";
  display: var(--009dcc79);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  animation: shimmer-3d430b98 1.5s infinite;
}
@keyframes shimmer-3d430b98 {
0% {
    transform: translateX(-100%);
}
100% {
    transform: translateX(100%);
}
}
.platforms-section.data-v-3d430b98 {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.section-header.data-v-3d430b98 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.section-header .section-title.data-v-3d430b98 {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}
.section-header .section-more.data-v-3d430b98 {
  display: flex;
  align-items: center;
  color: #999999;
  font-size: 14px;
}
.platforms-scroll.data-v-3d430b98 {
  width: 100%;
  white-space: nowrap;
}
.platforms-container.data-v-3d430b98 {
  display: inline-flex;
  padding-bottom: 8px;
}
.platform-item.data-v-3d430b98 {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 24px;
}
.platform-item.data-v-3d430b98:last-child {
  margin-right: 0;
}
.platform-item .platform-icon.data-v-3d430b98 {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  margin-bottom: 8px;
}
.platform-item .platform-name.data-v-3d430b98 {
  font-size: 12px;
  color: #333333;
}
.coupon-section.data-v-3d430b98 {
  margin: 16px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.coupon-section .coupon-banner.data-v-3d430b98 {
  width: 100%;
  border-radius: 16px;
}
.products-section.data-v-3d430b98 {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.products-grid.data-v-3d430b98 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

/* 生活返利模块 */
.life-cashback-section.data-v-3d430b98 {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.life-cashback-grid.data-v-3d430b98 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: auto auto;
  gap: 12px;
}
.life-cashback-item.data-v-3d430b98 {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  position: relative;
  border: 1px solid #F0F0F0;
}
.life-cashback-item .item-icon-container.data-v-3d430b98 {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}
.life-cashback-item .item-content.data-v-3d430b98 {
  display: flex;
  flex-direction: column;
}
.life-cashback-item .item-content .item-title.data-v-3d430b98 {
  font-size: 14px;
  color: #333333;
  margin-bottom: 4px;
}
.life-cashback-item .item-content .item-desc.data-v-3d430b98 {
  font-size: 12px;
  color: #9C27B0;
}
.life-cashback-item.special-item.data-v-3d430b98 {
  grid-column: span 2;
  background-color: #F5F0FF;
  padding: 12px 16px;
  flex-direction: row;
  align-items: center;
  position: relative;
  overflow: hidden;
}
.life-cashback-item.special-item .special-content.data-v-3d430b98 {
  flex: 1;
}
.life-cashback-item.special-item .special-content .special-title.data-v-3d430b98 {
  font-size: 14px;
  color: #333333;
  margin-bottom: 4px;
}
.life-cashback-item.special-item .special-content .special-desc.data-v-3d430b98 {
  font-size: 14px;
  color: #9C27B0;
  font-weight: 500;
  margin-bottom: 2px;
}
.life-cashback-item.special-item .special-content .special-subdesc.data-v-3d430b98 {
  font-size: 12px;
  color: #666666;
}
.life-cashback-item.special-item .special-price.data-v-3d430b98 {
  background-color: #FF5252;
  border-radius: 20px;
  padding: 4px 8px;
  display: flex;
  align-items: center;
  margin-right: 12px;
}
.life-cashback-item.special-item .special-price .price-value.data-v-3d430b98 {
  font-size: 16px;
  font-weight: 600;
  color: #FFFFFF;
}
.life-cashback-item.special-item .special-price .price-tag.data-v-3d430b98 {
  font-size: 12px;
  color: #FFFFFF;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  padding: 0 4px;
  margin-left: 2px;
}
.life-cashback-item.special-item .special-image.data-v-3d430b98 {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 60px;
  height: 60px;
}