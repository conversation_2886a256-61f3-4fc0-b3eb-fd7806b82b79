<view class="discount-container"><view class="navbar"><view class="navbar-back" bindtap="{{a}}"><view class="back-icon"></view></view><text class="navbar-title">满减活动管理</text><view class="navbar-right"><view class="help-icon">?</view></view></view><view class="top-actions"><create-button wx:if="{{c}}" bindclick="{{b}}" u-i="16c40466-0" bind:__l="__l" u-p="{{c}}"/></view><view class="overview-section"><view class="overview-cards"><view class="overview-card"><text class="card-value">{{d}}</text><text class="card-label">活动总数</text></view><view class="overview-card"><text class="card-value">{{e}}</text><text class="card-label">进行中</text></view><view class="overview-card"><text class="card-value">{{f}}</text><text class="card-label">参与订单</text></view><view class="overview-card"><text class="card-value">¥{{g}}</text><text class="card-label">总优惠额</text></view></view></view><view class="action-bar"><view class="search-box"><view class="search-icon"><view class="icon-svg"><svg wx:if="{{j}}" u-s="{{['d']}}" u-i="16c40466-1" bind:__l="__l" u-p="{{j}}"><circle wx:if="{{h}}" u-i="16c40466-2,16c40466-1" bind:__l="__l" u-p="{{h}}"></circle><line wx:if="{{i}}" u-i="16c40466-3,16c40466-1" bind:__l="__l" u-p="{{i}}"></line></svg></view></view><input type="text" class="search-input" placeholder="搜索满减活动" bindinput="{{k}}" value="{{l}}"/></view><view class="filter-btn" bindtap="{{o}}"><view class="btn-icon"><svg wx:if="{{n}}" u-s="{{['d']}}" u-i="16c40466-4" bind:__l="__l" u-p="{{n}}"><polygon wx:if="{{m}}" u-i="16c40466-5,16c40466-4" bind:__l="__l" u-p="{{m}}"></polygon></svg></view><text class="btn-text">筛选</text></view></view><view class="discount-list"><view wx:for="{{p}}" wx:for-item="item" wx:key="F" bindtap="{{item.G}}" class="{{['discount-item', item.H && 'hover']}}" bindmouseenter="{{item.I}}" bindmouseleave="{{item.J}}"><view class="{{['discount-status', item.b]}}">{{item.a}}</view><view class="discount-content"><text class="discount-title">{{item.c}}</text><view class="discount-rules"><view wx:for="{{item.d}}" wx:for-item="rule" wx:key="c" class="rule-item"><text class="rule-text">满{{rule.a}}减{{rule.b}}</text></view></view><view class="discount-info"><view class="info-item"><text class="info-label">活动时间:</text><text class="info-value">{{item.e}}</text></view><view class="info-item"><text class="info-label">使用次数:</text><text class="info-value">{{item.f}}次</text></view><view class="info-item"><text class="info-label">优惠金额:</text><text class="info-value">¥{{item.g}}</text></view></view></view><view class="discount-actions"><view class="action-btn edit" catchtap="{{item.j}}"><view class="btn-icon"><svg wx:if="{{r}}" u-s="{{['d']}}" u-i="{{item.i}}" bind:__l="__l" u-p="{{r}}"><path wx:if="{{q}}" u-i="{{item.h}}" bind:__l="__l" u-p="{{q}}"></path></svg></view><text class="btn-text">编辑</text></view><view class="{{['action-btn', item.x]}}" catchtap="{{item.y}}"><view class="btn-icon"><svg wx:if="{{item.k}}" u-s="{{['d']}}" u-i="{{item.p}}" bind:__l="__l" u-p="{{item.q}}"><rect wx:if="{{item.m}}" u-i="{{item.l}}" bind:__l="__l" u-p="{{item.m}}"></rect><rect wx:if="{{item.o}}" u-i="{{item.n}}" bind:__l="__l" u-p="{{item.o}}"></rect></svg><svg wx:else u-s="{{['d']}}" u-i="{{item.t}}" bind:__l="__l" u-p="{{item.v||''}}"><polygon wx:if="{{item.s}}" u-i="{{item.r}}" bind:__l="__l" u-p="{{item.s}}"></polygon></svg></view><text class="btn-text">{{item.w}}</text></view><view class="action-btn delete" catchtap="{{item.E}}"><view class="btn-icon"><svg wx:if="{{x}}" u-s="{{['d']}}" u-i="{{item.D}}" bind:__l="__l" u-p="{{x}}"><polyline wx:if="{{s}}" u-i="{{item.z}}" bind:__l="__l" u-p="{{s}}"></polyline><path wx:if="{{t}}" u-i="{{item.A}}" bind:__l="__l" u-p="{{t}}"></path><line wx:if="{{v}}" u-i="{{item.B}}" bind:__l="__l" u-p="{{v}}"></line><line wx:if="{{w}}" u-i="{{item.C}}" bind:__l="__l" u-p="{{w}}"></line></svg></view><text class="btn-text">删除</text></view></view></view><view wx:if="{{y}}" class="empty-state"><view class="empty-icon"><svg wx:if="{{B}}" u-s="{{['d']}}" u-i="16c40466-18" bind:__l="__l" u-p="{{B}}"><polyline wx:if="{{z}}" u-i="16c40466-19,16c40466-18" bind:__l="__l" u-p="{{z}}"></polyline><path wx:if="{{A}}" u-i="16c40466-20,16c40466-18" bind:__l="__l" u-p="{{A}}"></path></svg></view><text class="empty-text">暂无满减活动</text><text class="empty-subtext">点击下方按钮创建新的满减活动</text></view></view></view>