{"version": 3, "file": "package-detail.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/group/package-detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xncm91cFxwYWNrYWdlLWRldGFpbC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"package-detail-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">套餐详情</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"more-icon\" @click=\"showMoreOptions\">···</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 页面内容 -->\r\n    <scroll-view scroll-y class=\"page-content\">\r\n      <!-- 套餐基本信息 -->\r\n      <view class=\"package-header\">\r\n        <view class=\"package-title-row\">\r\n          <text class=\"package-name\">{{packageDetail.name}}</text>\r\n          <view class=\"package-status\" :class=\"packageDetail.statusClass\">{{packageDetail.statusText}}</view>\r\n        </view>\r\n        <text class=\"package-desc\">{{packageDetail.description}}</text>\r\n        <view class=\"package-meta\">\r\n          <view class=\"meta-item\">\r\n            <text class=\"meta-label\">创建时间：</text>\r\n            <text class=\"meta-value\">{{packageDetail.createTime}}</text>\r\n          </view>\r\n          <view class=\"meta-item\">\r\n            <text class=\"meta-label\">活动时间：</text>\r\n            <text class=\"meta-value\">{{packageDetail.activityTime}}</text>\r\n          </view>\r\n          <view class=\"meta-item\">\r\n            <text class=\"meta-label\">成团人数：</text>\r\n            <text class=\"meta-value\">{{packageDetail.groupSize}}人</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 套餐价格信息 -->\r\n      <view class=\"price-card\">\r\n        <view class=\"price-row\">\r\n          <text class=\"price-label\">市场价</text>\r\n          <text class=\"price-value original\">¥{{packageDetail.originalPrice}}</text>\r\n        </view>\r\n        <view class=\"price-row\">\r\n          <text class=\"price-label\">拼团价</text>\r\n          <text class=\"price-value group\">¥{{packageDetail.groupPrice}}</text>\r\n        </view>\r\n        <view class=\"price-row\">\r\n          <text class=\"price-label\">节省金额</text>\r\n          <text class=\"price-value save\">¥{{packageDetail.savingsAmount}}</text>\r\n        </view>\r\n        <view class=\"price-row\">\r\n          <text class=\"price-label\">折扣率</text>\r\n          <text class=\"price-value discount\">{{packageDetail.discountRate}}折</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 套餐商品列表 -->\r\n      <view class=\"items-card\">\r\n        <view class=\"card-header\">\r\n          <text class=\"card-title\">套餐商品</text>\r\n        </view>\r\n        <view class=\"items-list\">\r\n          <view class=\"item-row\" v-for=\"(item, index) in packageDetail.items\" :key=\"index\">\r\n            <view class=\"item-number\">{{index + 1}}</view>\r\n            <view class=\"item-info\">\r\n              <text class=\"item-name\">{{item.name}}</text>\r\n              <view class=\"item-meta\">\r\n                <text class=\"item-price\">¥{{item.price}}</text>\r\n                <text class=\"item-quantity\">x {{item.quantity}}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <view class=\"items-summary\">\r\n          <text class=\"summary-text\">共 {{packageDetail.totalItems}} 件商品，原价总计 ¥{{packageDetail.originalPrice}}</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 销售数据 -->\r\n      <view class=\"sales-card\">\r\n        <view class=\"card-header\">\r\n          <text class=\"card-title\">销售数据</text>\r\n          <view class=\"date-picker\" @click=\"showDatePicker\">\r\n            <text class=\"date-text\">{{dateRange}}</text>\r\n            <view class=\"date-icon\"></view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"sales-stats\">\r\n          <view class=\"stat-box\">\r\n            <text class=\"stat-value\">{{packageDetail.salesCount}}</text>\r\n            <text class=\"stat-label\">总销量</text>\r\n          </view>\r\n          <view class=\"stat-box\">\r\n            <text class=\"stat-value\">{{packageDetail.viewCount}}</text>\r\n            <text class=\"stat-label\">浏览量</text>\r\n          </view>\r\n          <view class=\"stat-box\">\r\n            <text class=\"stat-value\">{{packageDetail.conversionRate}}%</text>\r\n            <text class=\"stat-label\">转化率</text>\r\n          </view>\r\n          <view class=\"stat-box\">\r\n            <text class=\"stat-value\">¥{{formatNumber(packageDetail.totalRevenue)}}</text>\r\n            <text class=\"stat-label\">总收入</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 销售趋势图表 -->\r\n        <view class=\"sales-chart\">\r\n          <view class=\"chart-header\">\r\n            <text class=\"chart-title\">销售趋势</text>\r\n          </view>\r\n          <view class=\"chart-container\">\r\n            <!-- 这里可以放置实际的图表组件 -->\r\n            <view class=\"chart-placeholder\">\r\n              <text class=\"placeholder-text\">销售趋势图表</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 拼团记录 -->\r\n      <view class=\"groups-card\">\r\n        <view class=\"card-header\">\r\n          <text class=\"card-title\">拼团记录</text>\r\n          <text class=\"view-all\" @click=\"viewAllGroups\">查看全部</text>\r\n        </view>\r\n        \r\n        <view class=\"groups-list\">\r\n          <view class=\"group-item\" v-for=\"(group, index) in packageDetail.recentGroups\" :key=\"index\">\r\n            <view class=\"group-info\">\r\n              <view class=\"group-leader\">\r\n                <image class=\"leader-avatar\" :src=\"group.leaderAvatar\" mode=\"aspectFill\"></image>\r\n                <text class=\"leader-name\">{{group.leaderName}}</text>\r\n              </view>\r\n              <view class=\"group-status\" :class=\"group.statusClass\">{{group.statusText}}</view>\r\n            </view>\r\n            <view class=\"group-progress\">\r\n              <view class=\"progress-text\">\r\n                <text>已参与：{{group.currentMembers}}/{{group.requiredMembers}}人</text>\r\n                <text>{{group.progressPercent}}%</text>\r\n              </view>\r\n              <view class=\"progress-bar\">\r\n                <view class=\"progress-fill\" :style=\"{ width: group.progressPercent + '%' }\"></view>\r\n              </view>\r\n            </view>\r\n            <view class=\"group-time\">\r\n              <text class=\"time-label\">{{group.timeType}}：</text>\r\n              <text class=\"time-value\">{{group.timeValue}}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n    \r\n    <!-- 底部操作栏 -->\r\n    <view class=\"footer-actions\">\r\n      <view class=\"action-button edit\" @click=\"editPackage\">\r\n        <view class=\"button-icon edit-icon\"></view>\r\n        <text class=\"button-text\">编辑套餐</text>\r\n      </view>\r\n      <view class=\"action-button share\" @click=\"sharePackage\">\r\n        <view class=\"button-icon share-icon\"></view>\r\n        <text class=\"button-text\">分享套餐</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      packageId: null,\r\n      dateRange: '最近7天',\r\n      \r\n      // 套餐详情数据\r\n      packageDetail: {\r\n        id: 1,\r\n        name: '四菜一汤家庭套餐',\r\n        description: '适合3-4人用餐，经典家庭聚餐套餐，荤素搭配，营养均衡',\r\n        createTime: '2023-04-10 14:30',\r\n        activityTime: '2023-04-10 ~ 2023-05-10',\r\n        statusText: '进行中',\r\n        statusClass: 'active',\r\n        groupSize: 3,\r\n        \r\n        originalPrice: '168.00',\r\n        groupPrice: '99.00',\r\n        savingsAmount: '69.00',\r\n        discountRate: '5.9',\r\n        \r\n        items: [\r\n          { name: '红烧肉', price: '48.00', quantity: 1 },\r\n          { name: '糖醋排骨', price: '42.00', quantity: 1 },\r\n          { name: '鱼香肉丝', price: '38.00', quantity: 1 },\r\n          { name: '清炒时蔬', price: '22.00', quantity: 1 },\r\n          { name: '紫菜蛋花汤', price: '18.00', quantity: 1 }\r\n        ],\r\n        totalItems: 5,\r\n        \r\n        salesCount: 56,\r\n        viewCount: 1280,\r\n        conversionRate: 4.3,\r\n        totalRevenue: 5544.00,\r\n        \r\n        recentGroups: [\r\n          {\r\n            id: 1,\r\n            leaderName: '张先生',\r\n            leaderAvatar: '/static/images/avatars/user1.jpg',\r\n            requiredMembers: 3,\r\n            currentMembers: 2,\r\n            progressPercent: 66,\r\n            timeType: '剩余时间',\r\n            timeValue: '12小时23分',\r\n            statusText: '拼团中',\r\n            statusClass: 'active'\r\n          },\r\n          {\r\n            id: 2,\r\n            leaderName: '李女士',\r\n            leaderAvatar: '/static/images/avatars/user2.jpg',\r\n            requiredMembers: 3,\r\n            currentMembers: 3,\r\n            progressPercent: 100,\r\n            timeType: '成团时间',\r\n            timeValue: '2023-04-15 18:30',\r\n            statusText: '已成团',\r\n            statusClass: 'success'\r\n          },\r\n          {\r\n            id: 3,\r\n            leaderName: '王先生',\r\n            leaderAvatar: '/static/images/avatars/user3.jpg',\r\n            requiredMembers: 3,\r\n            currentMembers: 1,\r\n            progressPercent: 33,\r\n            timeType: '失败时间',\r\n            timeValue: '2023-04-14 10:15',\r\n            statusText: '已失败',\r\n            statusClass: 'failed'\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    if (options.id) {\r\n      this.packageId = options.id;\r\n      // 这里可以添加实际的数据加载逻辑\r\n      this.loadPackageDetail();\r\n    }\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    showMoreOptions() {\r\n      uni.showActionSheet({\r\n        itemList: ['复制套餐', '下架套餐', '删除套餐'],\r\n        success: (res) => {\r\n          switch(res.tapIndex) {\r\n            case 0:\r\n              this.copyPackage();\r\n              break;\r\n            case 1:\r\n              this.deactivatePackage();\r\n              break;\r\n            case 2:\r\n              this.confirmDeletePackage();\r\n              break;\r\n          }\r\n        }\r\n      });\r\n    },\r\n    loadPackageDetail() {\r\n      // 实际项目中应该从服务器加载数据\r\n      console.log('加载套餐详情，ID:', this.packageId);\r\n    },\r\n    formatNumber(num) {\r\n      return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });\r\n    },\r\n    showDatePicker() {\r\n      uni.showToast({\r\n        title: '日期选择功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    viewAllGroups() {\r\n      uni.navigateTo({\r\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/group/package-groups?id=${this.packageId}`\r\n      });\r\n    },\r\n    editPackage() {\r\n      uni.navigateTo({\r\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/group/create?id=${this.packageId}&type=package&edit=true`\r\n      });\r\n    },\r\n    sharePackage() {\r\n      uni.showToast({\r\n        title: '分享功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    copyPackage() {\r\n      uni.showToast({\r\n        title: '复制功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    deactivatePackage() {\r\n      uni.showModal({\r\n        title: '确认下架',\r\n        content: '确定要下架该套餐吗？下架后用户将无法购买。',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            uni.showToast({\r\n              title: '下架成功',\r\n              icon: 'success'\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    confirmDeletePackage() {\r\n      uni.showModal({\r\n        title: '确认删除',\r\n        content: '确定要删除该套餐吗？此操作不可恢复。',\r\n        confirmText: '删除',\r\n        confirmColor: '#FF3B30',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            uni.showToast({\r\n              title: '删除成功',\r\n              icon: 'success'\r\n            });\r\n            setTimeout(() => {\r\n              uni.navigateBack();\r\n            }, 1500);\r\n          }\r\n        }\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.package-detail-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: calc(80px + env(safe-area-inset-bottom));\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #9040FF, #5E35B1);\r\n  color: #fff;\r\n  padding: 44px 16px 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.navbar-right {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.more-icon {\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n  transform: rotate(90deg);\r\n}\r\n\r\n.page-content {\r\n  height: calc(100vh - 59px - 80px - env(safe-area-inset-bottom));\r\n}\r\n\r\n/* 套餐头部信息 */\r\n.package-header {\r\n  margin: 15px;\r\n  padding: 15px;\r\n  background: #FFFFFF;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.package-title-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.package-name {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.package-status {\r\n  font-size: 12px;\r\n  padding: 2px 8px;\r\n  border-radius: 10px;\r\n}\r\n\r\n.package-status.active {\r\n  background: rgba(52, 199, 89, 0.1);\r\n  color: #34C759;\r\n}\r\n\r\n.package-status.inactive {\r\n  background: rgba(255, 59, 48, 0.1);\r\n  color: #FF3B30;\r\n}\r\n\r\n.package-desc {\r\n  font-size: 14px;\r\n  color: #666;\r\n  line-height: 1.5;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.package-meta {\r\n  border-top: 1px dashed #E5E7EB;\r\n  padding-top: 15px;\r\n}\r\n\r\n.meta-item {\r\n  display: flex;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.meta-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.meta-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n  width: 80px;\r\n}\r\n\r\n.meta-value {\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n/* 价格卡片 */\r\n.price-card {\r\n  margin: 15px;\r\n  padding: 15px;\r\n  background: #FFFFFF;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.price-row {\r\n  width: 50%;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.price-row:nth-last-child(-n+2) {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.price-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n  display: block;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.price-value {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.price-value.original {\r\n  text-decoration: line-through;\r\n  color: #999;\r\n}\r\n\r\n.price-value.group {\r\n  color: #FF3B30;\r\n}\r\n\r\n.price-value.save {\r\n  color: #FF9500;\r\n}\r\n\r\n.price-value.discount {\r\n  color: #34C759;\r\n}\r\n\r\n/* 商品列表卡片 */\r\n.items-card {\r\n  margin: 15px;\r\n  background: #FFFFFF;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  overflow: hidden;\r\n}\r\n\r\n.card-header {\r\n  padding: 15px;\r\n  border-bottom: 1px solid #F5F7FA;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.card-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.items-list {\r\n  padding: 0 15px;\r\n}\r\n\r\n.item-row {\r\n  display: flex;\r\n  padding: 15px 0;\r\n  border-bottom: 1px solid #F5F7FA;\r\n}\r\n\r\n.item-row:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.item-number {\r\n  width: 24px;\r\n  height: 24px;\r\n  background: #F0F2F5;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  color: #666;\r\n  margin-right: 10px;\r\n}\r\n\r\n.item-info {\r\n  flex: 1;\r\n}\r\n\r\n.item-name {\r\n  font-size: 14px;\r\n  color: #333;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.item-meta {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.item-price {\r\n  font-size: 12px;\r\n  color: #FF3B30;\r\n}\r\n\r\n.item-quantity {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.items-summary {\r\n  padding: 15px;\r\n  background: #F8FAFC;\r\n  border-top: 1px solid #F5F7FA;\r\n}\r\n\r\n.summary-text {\r\n  font-size: 12px;\r\n  color: #666;\r\n  text-align: center;\r\n}\r\n\r\n/* 销售数据卡片 */\r\n.sales-card {\r\n  margin: 15px;\r\n  background: #FFFFFF;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  overflow: hidden;\r\n}\r\n\r\n.date-picker {\r\n  display: flex;\r\n  align-items: center;\r\n  background: #F5F7FA;\r\n  border-radius: 15px;\r\n  padding: 5px 10px;\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n.date-icon {\r\n  width: 0;\r\n  height: 0;\r\n  border-left: 4px solid transparent;\r\n  border-right: 4px solid transparent;\r\n  border-top: 5px solid #666;\r\n  margin-left: 5px;\r\n}\r\n\r\n.sales-stats {\r\n  display: flex;\r\n  padding: 15px;\r\n  border-bottom: 1px solid #F5F7FA;\r\n}\r\n\r\n.stat-box {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.sales-chart {\r\n  padding: 15px;\r\n}\r\n\r\n.chart-header {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.chart-title {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.chart-container {\r\n  height: 200px;\r\n}\r\n\r\n.chart-placeholder {\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #F8FAFC;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.placeholder-text {\r\n  font-size: 14px;\r\n  color: #999;\r\n}\r\n\r\n/* 拼团记录卡片 */\r\n.groups-card {\r\n  margin: 15px;\r\n  background: #FFFFFF;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  overflow: hidden;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.view-all {\r\n  font-size: 14px;\r\n  color: #9040FF;\r\n}\r\n\r\n.groups-list {\r\n  padding: 0 15px;\r\n}\r\n\r\n.group-item {\r\n  padding: 15px 0;\r\n  border-bottom: 1px solid #F5F7FA;\r\n}\r\n\r\n.group-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.group-info {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.group-leader {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.leader-avatar {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 50%;\r\n  margin-right: 8px;\r\n}\r\n\r\n.leader-name {\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.group-status {\r\n  font-size: 12px;\r\n  padding: 2px 8px;\r\n  border-radius: 10px;\r\n}\r\n\r\n.group-status.active {\r\n  background: rgba(52, 199, 89, 0.1);\r\n  color: #34C759;\r\n}\r\n\r\n.group-status.success {\r\n  background: rgba(0, 122, 255, 0.1);\r\n  color: #007AFF;\r\n}\r\n\r\n.group-status.failed {\r\n  background: rgba(255, 59, 48, 0.1);\r\n  color: #FF3B30;\r\n}\r\n\r\n.group-progress {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.progress-text {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.progress-bar {\r\n  height: 4px;\r\n  background-color: #EBEDF5;\r\n  border-radius: 2px;\r\n  overflow: hidden;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, #9040FF, #5E35B1);\r\n  border-radius: 2px;\r\n}\r\n\r\n.group-time {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.time-value {\r\n  color: #666;\r\n}\r\n\r\n/* 底部操作栏 */\r\n.footer-actions {\r\n  position: fixed;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  height: 80px;\r\n  background: #FFFFFF;\r\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-around;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n  z-index: 10;\r\n}\r\n\r\n.action-button {\r\n  flex: 1;\r\n  height: 50px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.button-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.edit-icon {\r\n  background: rgba(0, 122, 255, 0.1);\r\n  position: relative;\r\n}\r\n\r\n.edit-icon:before {\r\n  content: '';\r\n  width: 12px;\r\n  height: 12px;\r\n  border: 1px solid #007AFF;\r\n  border-radius: 2px;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.share-icon {\r\n  background: rgba(52, 199, 89, 0.1);\r\n  position: relative;\r\n}\r\n\r\n.share-icon:before {\r\n  content: '';\r\n  width: 12px;\r\n  height: 6px;\r\n  border-left: 1px solid #34C759;\r\n  border-right: 1px solid #34C759;\r\n  border-bottom: 1px solid #34C759;\r\n}\r\n\r\n.button-text {\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/group/package-detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA4KA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,WAAW;AAAA;AAAA,MAGX,eAAe;AAAA,QACb,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,WAAW;AAAA,QAEX,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,cAAc;AAAA,QAEd,OAAO;AAAA,UACL,EAAE,MAAM,OAAO,OAAO,SAAS,UAAU,EAAG;AAAA,UAC5C,EAAE,MAAM,QAAQ,OAAO,SAAS,UAAU,EAAG;AAAA,UAC7C,EAAE,MAAM,QAAQ,OAAO,SAAS,UAAU,EAAG;AAAA,UAC7C,EAAE,MAAM,QAAQ,OAAO,SAAS,UAAU,EAAG;AAAA,UAC7C,EAAE,MAAM,SAAS,OAAO,SAAS,UAAU,EAAE;AAAA,QAC9C;AAAA,QACD,YAAY;AAAA,QAEZ,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,cAAc;AAAA,QAEd,cAAc;AAAA,UACZ;AAAA,YACE,IAAI;AAAA,YACJ,YAAY;AAAA,YACZ,cAAc;AAAA,YACd,iBAAiB;AAAA,YACjB,gBAAgB;AAAA,YAChB,iBAAiB;AAAA,YACjB,UAAU;AAAA,YACV,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,aAAa;AAAA,UACd;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,YAAY;AAAA,YACZ,cAAc;AAAA,YACd,iBAAiB;AAAA,YACjB,gBAAgB;AAAA,YAChB,iBAAiB;AAAA,YACjB,UAAU;AAAA,YACV,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,aAAa;AAAA,UACd;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,YAAY;AAAA,YACZ,cAAc;AAAA,YACd,iBAAiB;AAAA,YACjB,gBAAgB;AAAA,YAChB,iBAAiB;AAAA,YACjB,UAAU;AAAA,YACV,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,aAAa;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,OAAO,SAAS;AACd,QAAI,QAAQ,IAAI;AACd,WAAK,YAAY,QAAQ;AAEzB,WAAK,kBAAiB;AAAA,IACxB;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,kBAAkB;AAChBA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,QAAQ,QAAQ,MAAM;AAAA,QACjC,SAAS,CAAC,QAAQ;AAChB,kBAAO,IAAI,UAAQ;AAAA,YACjB,KAAK;AACH,mBAAK,YAAW;AAChB;AAAA,YACF,KAAK;AACH,mBAAK,kBAAiB;AACtB;AAAA,YACF,KAAK;AACH,mBAAK,qBAAoB;AACzB;AAAA,UACJ;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,oBAAoB;AAElBA,+HAAY,cAAc,KAAK,SAAS;AAAA,IACzC;AAAA,IACD,aAAa,KAAK;AAChB,aAAO,IAAI,eAAe,SAAS,EAAE,uBAAuB,GAAG,uBAAuB,EAAA,CAAG;AAAA,IAC1F;AAAA,IACD,iBAAiB;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,gBAAgB;AACdA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,iFAAiF,KAAK,SAAS;AAAA,MACtG,CAAC;AAAA,IACF;AAAA,IACD,cAAc;AACZA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,yEAAyE,KAAK,SAAS;AAAA,MAC9F,CAAC;AAAA,IACF;AAAA,IACD,eAAe;AACbA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,cAAc;AACZA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,oBAAoB;AAClBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,uBAAuB;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,cAAc;AAAA,QACd,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AACD,uBAAW,MAAM;AACfA,4BAAG,MAAC,aAAY;AAAA,YACjB,GAAE,IAAI;AAAA,UACT;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1VA,GAAG,WAAW,eAAe;"}