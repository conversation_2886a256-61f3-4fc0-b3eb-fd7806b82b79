
.call-history-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  position: relative;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  background-color: #0052CC;
  color: #fff;
  z-index: 100;
}
.navbar-left {
  width: 80rpx;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
}
.navbar-right {
  width: 80rpx;
  text-align: right;
}
.back-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 选项卡样式 */
.tabs-container {
  position: fixed;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  z-index: 99;
}
.tab-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
.tab-text {
  font-size: 14px;
  color: #333;
  padding: 0 15px;
}
.tab-item.active .tab-text {
  color: #0052CC;
  font-weight: bold;
}
.tab-line {
  position: absolute;
  bottom: 0;
  height: 3px;
  background-color: #0052CC;
  border-radius: 2px;
  transition: transform 0.3s;
}

/* 内容区域 */
.content-area {
  position: relative;
  height: 100vh;
}
.content-swiper {
  height: 100%;
}
.tab-scroll {
  height: 100%;
}

/* 通话记录列表 */
.call-list {
  padding: 15px;
}
.call-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.call-left {
  margin-right: 15px;
}
.call-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}
.call-middle {
  flex: 1;
}
.call-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}
.call-info {
  font-size: 13px;
  color: #999;
}
.call-type {
  margin-right: 10px;
  color: #666;
}
.call-desc {
  color: #999;
}
.call-right {
  text-align: right;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.call-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 10px;
}
.call-again {
  width: 60rpx;
  height: 60rpx;
  background-color: #e6f7ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.call-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 未接电话样式 */
.call-missed {
  color: #ff4d4f !important;
}

/* 空状态 */
.empty-view {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 100px;
}
.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20px;
}
.empty-text {
  font-size: 14px;
  color: #999;
}

/* 列表底部 */
.list-bottom {
  text-align: center;
  padding: 15px 0;
  font-size: 14px;
  color: #999;
}
