<template>
  <view class="promotion-tool-wrapper">
    <ConfigurablePremiumActions
      :pageType="pageType"
      :showMode="showMode"
      :itemData="itemData"
      @action-completed="handleActionCompleted"
      @action-cancelled="handleActionCancelled"
    />
  </view>
</template>

<script setup>
import { computed } from 'vue';
import ConfigurablePremiumActions from '@/components/premium/ConfigurablePremiumActions.vue';

// 定义组件属性
const props = defineProps({
  // 页面类型
  pageType: {
    type: String,
    default: 'publish',
    validator: (value) => [
      'publish', 'merchant_join', 'merchant_renew', 'merchant_top', 'merchant_refresh',
      'carpool_top', 'carpool_refresh', 'product_top', 'product_refresh'
    ].includes(value)
  },
  // 显示模式
  showMode: {
    type: String,
    default: 'button',
    validator: (value) => ['button', 'direct', 'selection'].includes(value)
  },
  // 项目数据
  itemData: {
    type: Object,
    default: () => ({
      id: '',
      title: '推广',
      description: '提升曝光度'
    })
  }
});

// 定义事件
const emit = defineEmits(['action-completed', 'action-cancelled']);

// 处理操作完成
const handleActionCompleted = (result) => {
  console.log('推广操作完成:', result);
  emit('action-completed', result);
};

// 处理操作取消
const handleActionCancelled = (result) => {
  console.log('推广操作取消:', result);
  emit('action-cancelled', result);
};
</script>

<style lang="scss" scoped>
.promotion-tool-wrapper {
  width: 100%;
}



</style> 