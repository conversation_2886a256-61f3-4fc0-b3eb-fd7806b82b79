<template>
  <view class="service-container">
    <!-- 顶部导航栏 -->
    <view class="header-area">
      <view class="safe-area-top"></view>
      <view class="custom-navbar">
        <view class="navbar-left" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="back-icon" style="filter: brightness(0) invert(1);"></image>
        </view>
        <view class="navbar-title">服务项目</view>
        <view class="navbar-right"></view>
      </view>
    </view>
    
    <!-- 背景装饰 -->
    <view class="bg-decoration bg-circle-1"></view>
    <view class="bg-decoration bg-circle-2"></view>
    <view class="bg-decoration bg-circle-3"></view>
    
    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 添加服务按钮 -->
      <view class="add-service-btn" @click="showAddServiceModal">
        <text class="add-icon">+</text>
        <text class="add-text">添加服务项目</text>
      </view>
      
      <!-- 服务列表 -->
      <view class="service-list" v-if="serviceList.length > 0">
        <view class="service-item" v-for="(item, index) in serviceList" :key="index">
          <view class="service-header">
            <view class="service-title-wrapper">
              <text class="service-title">{{item.name}}</text>
              <view class="service-status" :class="{ 'active': item.status === 'active' }">
                {{item.status === 'active' ? '上架中' : '已下架'}}
              </view>
            </view>
            <view class="service-price">
              <text class="price-value">¥{{item.price}}</text>
              <text class="price-unit">/{{item.unit}}</text>
            </view>
          </view>
          
          <view class="service-desc">{{item.description}}</view>
          
          <view class="service-tags" v-if="item.tags && item.tags.length > 0">
            <view class="tag-item" v-for="(tag, tagIndex) in item.tags" :key="tagIndex">
              {{tag}}
            </view>
          </view>
          
          <view class="service-cover">
            <image :src="item.coverImage" mode="aspectFill" class="cover-img"></image>
          </view>
          
          <view class="service-footer">
            <view class="service-stats">
              <view class="stat-item">
                <text class="stat-label">浏览</text>
                <text class="stat-value">{{item.views}}</text>
              </view>
              <view class="stat-item">
                <text class="stat-label">销量</text>
                <text class="stat-value">{{item.sales}}</text>
              </view>
              <view class="stat-item">
                <text class="stat-label">评分</text>
                <text class="stat-value">{{item.rating}}</text>
              </view>
            </view>
            
            <view class="service-actions">
              <view class="action-btn edit" @click="editService(index)">
                <image src="/static/images/tabbar/编辑.png" class="action-icon"></image>
                <text class="action-text">编辑</text>
              </view>
              <view class="action-btn" :class="item.status === 'active' ? 'disable' : 'enable'" @click="toggleServiceStatus(index)">
                <image :src="item.status === 'active' ? '/static/images/tabbar/下架.png' : '/static/images/tabbar/上架.png'" class="action-icon"></image>
                <text class="action-text">{{item.status === 'active' ? '下架' : '上架'}}</text>
              </view>
              <view class="action-btn delete" @click="deleteService(index)">
                <image src="/static/images/tabbar/删除.png" class="action-icon"></image>
                <text class="action-text">删除</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-else>
        <image src="/static/images/tabbar/无数据.png" class="empty-icon"></image>
        <text class="empty-text">暂无服务项目，请点击添加</text>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
    
    <!-- 添加/编辑服务弹窗 -->
    <view class="modal-overlay" v-if="showModal" @click="cancelModal"></view>
    <view class="service-modal" v-if="showModal">
      <view class="modal-header">
        <text class="modal-title">{{isEdit ? '编辑服务' : '添加服务'}}</text>
        <view class="close-btn" @click="cancelModal">×</view>
      </view>
      
      <scroll-view class="modal-body" scroll-y>
        <view class="form-item">
          <text class="form-label required">服务名称</text>
          <input type="text" class="form-input" v-model="formData.name" placeholder="请输入服务名称" />
        </view>
        
        <view class="form-item">
          <text class="form-label required">服务价格</text>
          <view class="price-input-group">
            <text class="price-prefix">¥</text>
            <input type="digit" class="form-input price-input" v-model="formData.price" placeholder="请输入价格" />
            <text class="price-delimiter">/</text>
            <input type="text" class="form-input unit-input" v-model="formData.unit" placeholder="单位" />
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">服务标签</text>
          <view class="tags-input">
            <view class="tag-item editable" v-for="(tag, index) in formData.tags" :key="index">
              {{tag}}
              <text class="remove-tag" @click="removeTag(index)">×</text>
            </view>
            <input type="text" class="tag-input" v-model="newTag" placeholder="添加标签" @confirm="addTag" />
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label required">服务封面</text>
          <view class="upload-area" @click="uploadCoverImage" v-if="!formData.coverImage">
            <text class="upload-icon">+</text>
            <text class="upload-text">上传封面图</text>
          </view>
          <view class="image-preview" v-else>
            <image :src="formData.coverImage" mode="aspectFill" class="preview-image"></image>
            <view class="preview-overlay">
              <view class="preview-action" @click.stop="uploadCoverImage">更换</view>
              <view class="preview-action delete" @click.stop="removeCoverImage">删除</view>
            </view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label required">服务描述</text>
          <textarea class="form-textarea" v-model="formData.description" placeholder="请输入服务介绍和详情描述"></textarea>
        </view>
      </scroll-view>
      
      <view class="modal-footer">
        <button class="modal-btn cancel" @click="cancelModal">取消</button>
        <button class="modal-btn confirm" @click="saveService" :disabled="!canSubmit">保存</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20,
      showModal: false,
      isEdit: false,
      editIndex: -1,
      newTag: '',
      serviceList: [
        {
          name: '上门保洁服务',
          price: '80',
          unit: '小时',
          tags: ['家居清洁', '开荒保洁', '深度清洁'],
          coverImage: '/static/images/service-1.jpg',
          description: '专业的保洁团队，配备先进清洁设备，提供家居日常保洁、开荒保洁、深度保洁等服务，让您的家居焕然一新。',
          status: 'active',
          views: 328,
          sales: 45,
          rating: 4.8
        },
        {
          name: '招牌菜制作体验',
          price: '168',
          unit: '人次',
          tags: ['美食体验', '技术学习'],
          coverImage: '/static/images/service-2.jpg',
          description: '由我店资深厨师一对一教学，传授招牌菜制作技巧，配备专业厨具和食材，体验结束后可带走成品。',
          status: 'inactive',
          views: 156,
          sales: 23,
          rating: 4.9
        }
      ],
      formData: {
        name: '',
        price: '',
        unit: '',
        tags: [],
        coverImage: '',
        description: '',
        status: 'active'
      }
    }
  },
  computed: {
    canSubmit() {
      return this.formData.name && this.formData.price && 
             this.formData.unit && this.formData.coverImage && 
             this.formData.description;
    }
  },
  onLoad() {
    this.setStatusBarHeight();
  },
  methods: {
    setStatusBarHeight() {
      uni.getSystemInfo({
        success: (res) => {
          this.statusBarHeight = res.statusBarHeight;
          if (typeof document !== 'undefined') {
            document.documentElement.style.setProperty('--status-bar-height', `${this.statusBarHeight}px`);
          }
        }
      });
    },
    goBack() {
      uni.navigateBack();
    },
    showAddServiceModal() {
      this.isEdit = false;
      this.formData = {
        name: '',
        price: '',
        unit: '次',
        tags: [],
        coverImage: '',
        description: '',
        status: 'active'
      };
      this.showModal = true;
    },
    editService(index) {
      this.isEdit = true;
      this.editIndex = index;
      const service = this.serviceList[index];
      this.formData = {
        name: service.name,
        price: service.price,
        unit: service.unit,
        tags: [...service.tags],
        coverImage: service.coverImage,
        description: service.description,
        status: service.status
      };
      this.showModal = true;
    },
    toggleServiceStatus(index) {
      const service = this.serviceList[index];
      service.status = service.status === 'active' ? 'inactive' : 'active';
      
      uni.showToast({
        title: service.status === 'active' ? '服务已上架' : '服务已下架',
        icon: 'none'
      });
    },
    deleteService(index) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除该服务项目吗？',
        success: (res) => {
          if (res.confirm) {
            this.serviceList.splice(index, 1);
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });
          }
        }
      });
    },
    cancelModal() {
      this.showModal = false;
    },
    addTag() {
      if (this.newTag && this.newTag.trim() && this.formData.tags.length < 5) {
        this.formData.tags.push(this.newTag.trim());
        this.newTag = '';
      } else if (this.formData.tags.length >= 5) {
        uni.showToast({
          title: '最多添加5个标签',
          icon: 'none'
        });
      }
    },
    removeTag(index) {
      this.formData.tags.splice(index, 1);
    },
    uploadCoverImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.formData.coverImage = res.tempFilePaths[0];
        }
      });
    },
    removeCoverImage() {
      this.formData.coverImage = '';
    },
    saveService() {
      if (!this.canSubmit) {
        uni.showToast({
          title: '请填写完整服务信息',
          icon: 'none'
        });
        return;
      }
      
      const serviceData = {
        name: this.formData.name,
        price: this.formData.price,
        unit: this.formData.unit,
        tags: [...this.formData.tags],
        coverImage: this.formData.coverImage,
        description: this.formData.description,
        status: this.formData.status,
        views: this.isEdit ? this.serviceList[this.editIndex].views : 0,
        sales: this.isEdit ? this.serviceList[this.editIndex].sales : 0,
        rating: this.isEdit ? this.serviceList[this.editIndex].rating : 5.0
      };
      
      if (this.isEdit) {
        this.serviceList.splice(this.editIndex, 1, serviceData);
      } else {
        this.serviceList.push(serviceData);
      }
      
      this.showModal = false;
      
      uni.showToast({
        title: this.isEdit ? '修改成功' : '添加成功',
        icon: 'success'
      });
    }
  }
}
</script>

<style lang="scss">
.service-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(110rpx + var(--status-bar-height, 20px));
}

/* 顶部导航栏样式 */
.header-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
  color: #fff;
}

.safe-area-top {
  height: var(--status-bar-height, 20px);
  width: 100%;
}

.custom-navbar {
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
}

.navbar-left, .navbar-right {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 48rpx;
  height: 48rpx;
}

/* 背景装饰样式 */
.bg-decoration {
  position: absolute;
  z-index: -1;
  border-radius: 50%;
  opacity: 0.07;
  background-color: #0A84FF;
}

.bg-circle-1 {
  top: -100rpx;
  right: -150rpx;
  width: 400rpx;
  height: 400rpx;
}

.bg-circle-2 {
  top: 20%;
  left: -200rpx;
  width: 500rpx;
  height: 500rpx;
}

.bg-circle-3 {
  bottom: 10%;
  right: -100rpx;
  width: 300rpx;
  height: 300rpx;
}

.content-scroll {
  padding: 30rpx;
}

/* 添加服务按钮 */
.add-service-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0A84FF, #0055FF);
  color: #FFFFFF;
  height: 100rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 10rpx rgba(10, 132, 255, 0.3);
}

.add-icon {
  font-size: 40rpx;
  margin-right: 10rpx;
}

.add-text {
  font-size: 32rpx;
  font-weight: 500;
}

/* 服务列表 */
.service-list {
  margin-top: 10rpx;
}

.service-item {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.service-title-wrapper {
  display: flex;
  align-items: center;
}

.service-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 16rpx;
}

.service-status {
  padding: 4rpx 12rpx;
  font-size: 22rpx;
  border-radius: 6rpx;
  background-color: #F2F2F7;
  color: #8E8E93;
}

.service-status.active {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.service-price {
  display: flex;
  align-items: flex-end;
}

.price-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #FF3B30;
}

.price-unit {
  font-size: 24rpx;
  color: #666;
  margin-left: 4rpx;
  margin-bottom: 4rpx;
}

.service-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

.service-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.tag-item {
  background-color: #F5F8FC;
  color: #0A84FF;
  padding: 6rpx 16rpx;
  font-size: 24rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}

.service-cover {
  width: 100%;
  height: 340rpx;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.cover-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.service-footer {
  display: flex;
  flex-direction: column;
}

.service-stats {
  display: flex;
  border-bottom: 2rpx solid #F2F2F7;
  padding-bottom: 20rpx;
  margin-bottom: 20rpx;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 6rpx;
}

.stat-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.service-actions {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  margin-left: 20rpx;
}

.action-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.action-text {
  font-size: 24rpx;
}

.action-btn.edit {
  background-color: rgba(10, 132, 255, 0.1);
  color: #0A84FF;
}

.action-btn.enable {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.action-btn.disable {
  background-color: rgba(142, 142, 147, 0.1);
  color: #8E8E93;
}

.action-btn.delete {
  background-color: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 40rpx;
}

/* 模态弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
}

.service-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-height: 90vh;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 30rpx;
  text-align: center;
  position: relative;
  border-bottom: 2rpx solid #F2F2F7;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  font-size: 36rpx;
  color: #999;
}

.modal-body {
  padding: 30rpx;
  max-height: 70vh;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.required::after {
  content: '*';
  color: #FF3B30;
  margin-left: 6rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background-color: #F5F8FC;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
}

.price-input-group {
  display: flex;
  align-items: center;
  background-color: #F5F8FC;
  border-radius: 10rpx;
  height: 80rpx;
  padding: 0 20rpx;
}

.price-prefix {
  font-size: 28rpx;
  color: #333;
  margin-right: 10rpx;
}

.price-input {
  flex: 1;
  background-color: transparent;
  height: 80rpx;
  padding: 0;
}

.price-delimiter {
  margin: 0 10rpx;
  color: #333;
}

.unit-input {
  width: 120rpx;
  background-color: transparent;
  height: 80rpx;
  padding: 0;
}

.tags-input {
  display: flex;
  flex-wrap: wrap;
  background-color: #F5F8FC;
  border-radius: 10rpx;
  padding: 10rpx;
  min-height: 80rpx;
}

.tag-item.editable {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.remove-tag {
  margin-left: 6rpx;
  color: #666;
  font-size: 24rpx;
}

.tag-input {
  height: 60rpx;
  flex: 1;
  min-width: 150rpx;
  font-size: 28rpx;
  color: #333;
  background-color: transparent;
}

.upload-area {
  width: 100%;
  height: 300rpx;
  background-color: #F5F8FC;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #CCCCCC;
}

.upload-icon {
  font-size: 48rpx;
  color: #0A84FF;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 28rpx;
  color: #666;
}

.image-preview {
  width: 100%;
  height: 300rpx;
  position: relative;
  border-radius: 10rpx;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-overlay {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  padding: 10rpx;
}

.preview-action {
  background-color: rgba(10, 132, 255, 0.7);
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 6rpx;
  margin-left: 10rpx;
}

.preview-action.delete {
  background-color: rgba(255, 59, 48, 0.7);
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  background-color: #F5F8FC;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
}

.modal-footer {
  display: flex;
  border-top: 2rpx solid #F2F2F7;
}

.modal-btn {
  flex: 1;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  background: none;
}

.modal-btn::after {
  border: none;
}

.cancel {
  color: #999;
  border-right: 1px solid #F2F2F7;
}

.confirm {
  color: #0A84FF;
  font-weight: 500;
}

.confirm[disabled] {
  color: #CCCCCC;
}

/* 适配暗黑模式 */
@media (prefers-color-scheme: dark) {
  .service-container {
    background-color: #1C1C1E;
  }
  
  .service-item,
  .service-modal {
    background-color: #2C2C2E;
  }
  
  .service-title,
  .modal-title,
  .form-label,
  .stat-value,
  .price-prefix,
  .price-value,
  .price-delimiter {
    color: #FFFFFF;
  }
  
  .service-desc,
  .stat-label,
  .price-unit {
    color: #A8A8A8;
  }
  
  .tag-item {
    background-color: #3A3A3C;
  }
  
  .service-stats {
    border-color: #3A3A3C;
  }
  
  .form-input,
  .price-input-group,
  .tags-input,
  .upload-area,
  .form-textarea {
    background-color: #3A3A3C;
    color: #FFFFFF;
  }
  
  .upload-text {
    color: #A8A8A8;
  }
  
  .modal-header,
  .modal-footer {
    border-color: #3A3A3C;
  }
}
</style> 