<template>
  <view class="products-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">可分销商品</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input-wrap">
        <view class="search-icon"></view>
        <input 
          class="search-input" 
          type="text" 
          v-model="searchKeyword" 
          placeholder="搜索商品" 
          confirm-type="search"
          @confirm="searchProducts"
        />
        <view class="clear-icon" v-if="searchKeyword" @click="clearSearch"></view>
      </view>
      <view class="search-btn" @click="searchProducts">搜索</view>
    </view>
    
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view 
        v-for="(filter, index) in filters" 
        :key="index" 
        class="filter-item" 
        :class="{ 'active': activeFilter === filter.value }"
        @click="switchFilter(filter.value)"
      >
        <text>{{filter.name}}</text>
        <view class="sort-icon" v-if="filter.sortable"></view>
      </view>
    </view>
    
    <!-- 商品列表 -->
    <view class="products-list" v-if="products.length > 0">
      <view 
        v-for="(product, index) in products" 
        :key="index" 
        class="product-card"
        @click="navigateToProduct(product)"
      >
        <image class="product-image" :src="product.image" mode="aspectFill"></image>
        <view class="product-info">
          <text class="product-name">{{product.name}}</text>
          <view class="product-meta">
            <text class="product-price">¥{{product.price}}</text>
            <text class="product-sales">已售{{product.sales}}件</text>
          </view>
          <view class="product-commission">
            <text class="commission-rate">佣金比例 {{product.commissionRate}}%</text>
            <text class="commission-value">预计佣金 ¥{{product.commission}}</text>
          </view>
          <button class="promote-btn" @click.stop="promoteProduct(product)">立即推广</button>
        </view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <image class="empty-image" src="/static/images/empty-products.png" mode="aspectFit"></image>
      <text class="empty-text">暂无可分销商品</text>
    </view>
    
    <!-- 加载更多 -->
    <view class="load-more" v-if="hasMoreData && products.length > 0">
      <text v-if="loading">加载中...</text>
      <text v-else @click="loadMore">点击加载更多</text>
    </view>
    
    <!-- 推广弹窗 -->
    <view class="promotion-modal" v-if="showPromotionModal">
      <view class="modal-mask" @click="closePromotionModal"></view>
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">选择推广方式</text>
          <view class="close-icon" @click="closePromotionModal"></view>
        </view>
        
        <view class="promotion-options">
          <view class="promotion-option" @click="navigateTo('/subPackages/promotion/pages/promotion-tool', selectedProduct)">
            <view class="option-icon poster"></view>
            <text class="option-name">生成海报</text>
          </view>
          
          <view class="promotion-option" @click="navigateTo('/subPackages/promotion/pages/qrcode', selectedProduct)">
            <view class="option-icon qrcode"></view>
            <text class="option-name">生成二维码</text>
          </view>
          
          <view class="promotion-option" @click="copyPromotionLink">
            <view class="option-icon link"></view>
            <text class="option-name">复制链接</text>
          </view>
          
          <view class="promotion-option" @click="shareToFriends">
            <view class="option-icon share"></view>
            <text class="option-name">分享好友</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import distributionService from '@/utils/distributionService';

// 搜索关键词
const searchKeyword = ref('');

// 筛选选项
const filters = [
  { name: '综合排序', value: 'default' },
  { name: '佣金优先', value: 'commission', sortable: true },
  { name: '销量优先', value: 'sales', sortable: true },
  { name: '价格优先', value: 'price', sortable: true }
];

// 当前选中的筛选
const activeFilter = ref('default');

// 商品列表
const products = ref([]);

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0
});

// 是否有更多数据
const hasMoreData = ref(false);

// 是否正在加载
const loading = ref(false);

// 推广弹窗
const showPromotionModal = ref(false);

// 选中的商品
const selectedProduct = ref(null);

// 页面加载
onMounted(async () => {
  // 获取商品列表
  await getProducts();
});

// 获取商品列表
const getProducts = async (loadMore = false) => {
  if (loading.value) return;
  
  try {
    loading.value = true;
    
    const page = loadMore ? pagination.page + 1 : 1;
    
    const result = await distributionService.getDistributableProducts({
      page,
      pageSize: pagination.pageSize,
      sortBy: activeFilter.value === 'default' ? 'commission' : activeFilter.value,
      keyword: searchKeyword.value
    });
    
    if (result) {
      // 更新商品列表
      if (loadMore) {
        products.value = [...products.value, ...result.list];
      } else {
        products.value = result.list;
      }
      
      // 更新分页信息
      pagination.page = page;
      pagination.total = result.pagination.total;
      pagination.totalPages = result.pagination.totalPages;
      
      // 更新是否有更多数据
      hasMoreData.value = pagination.page < pagination.totalPages;
    }
  } catch (error) {
    console.error('获取商品列表失败', error);
    uni.showToast({
      title: '获取商品列表失败',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
};

// 搜索商品
const searchProducts = () => {
  getProducts();
};

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = '';
  getProducts();
};

// 切换筛选
const switchFilter = (filter) => {
  if (activeFilter.value === filter) return;
  
  activeFilter.value = filter;
  getProducts();
};

// 加载更多
const loadMore = () => {
  if (hasMoreData.value && !loading.value) {
    getProducts(true);
  }
};

// 推广商品
const promoteProduct = (product) => {
  selectedProduct.value = product;
  showPromotionModal.value = true;
};

// 关闭推广弹窗
const closePromotionModal = () => {
  showPromotionModal.value = false;
};

// 复制推广链接
const copyPromotionLink = async () => {
  if (!selectedProduct.value) return;
  
  try {
    const result = await distributionService.generatePromotionLink({
      type: 'product',
      id: selectedProduct.value.id,
      distributorId: 'DIS1001' // 模拟数据，实际应从用户系统获取
    });
    
    if (result && result.url) {
      uni.setClipboardData({
        data: result.url,
        success: () => {
          uni.showToast({
            title: '链接已复制',
            icon: 'success'
          });
          closePromotionModal();
        }
      });
    }
  } catch (error) {
    console.error('生成推广链接失败', error);
    uni.showToast({
      title: '生成推广链接失败',
      icon: 'none'
    });
  }
};

// 分享给好友
const shareToFriends = () => {
  if (!selectedProduct.value) return;
  
  // 调用分享API
  uni.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage', 'shareTimeline']
  });
  
  // 模拟分享成功
  setTimeout(() => {
    closePromotionModal();
  }, 500);
};

// 导航到商品详情
const navigateToProduct = (product) => {
  uni.navigateTo({
    url: `/pages/product/detail?id=${product.id}&isDistribution=true`
  });
};

// 页面导航
const navigateTo = (url, product) => {
  if (!product) {
    uni.navigateTo({ url });
    return;
  }
  
  // 携带商品信息
  uni.navigateTo({
    url: `${url}?type=product&id=${product.id}&title=${encodeURIComponent(product.name)}&image=${encodeURIComponent(product.image)}`
  });
  
  // 关闭弹窗
  closePromotionModal();
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 显示帮助
const showHelp = () => {
  uni.showModal({
    title: '可分销商品帮助',
    content: '可分销商品是平台允许分销员推广并获得佣金的商品。您可以选择商品进行推广，通过分享获得佣金。',
    showCancel: false
  });
};
</script>

<style lang="scss">
.products-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 88rpx 32rpx 30rpx;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);
}

.navbar-back {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24rpx;
  height: 24rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.navbar-right {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: #FFFFFF;
}

.search-input-wrap {
  flex: 1;
  height: 72rpx;
  background: #F5F7FA;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  margin-right: 20rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  background-color: #6B0FBE;
  border-radius: 40rpx;
  margin-right: 10rpx;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: #333;
}

.clear-icon {
  width: 32rpx;
  height: 32rpx;
  background-color: #cccccc;
  border-radius: 40rpx;
}

.search-btn {
  font-size: 28rpx;
  color: #6B0FBE;
}

/* 筛选栏 */
.filter-bar {
  display: flex;
  background: #FFFFFF;
  padding: 0 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.filter-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.filter-item.active {
  color: #6B0FBE;
  font-weight: 600;
}

.sort-icon {
  width: 16rpx;
  height: 24rpx;
  position: relative;
  margin-left: 8rpx;
}

.sort-icon::before,
.sort-icon::after {
  content: '';
  position: absolute;
  left: 0;
  width: 0;
  height: 0;
}

.sort-icon::before {
  top: 4rpx;
  border-left: 8rpx solid transparent;
  border-right: 8rpx solid transparent;
  border-bottom: 8rpx solid #999;
}

.sort-icon::after {
  bottom: 4rpx;
  border-left: 8rpx solid transparent;
  border-right: 8rpx solid transparent;
  border-top: 8rpx solid #999;
}

/* 商品列表 */
.products-list {
  margin: 0 30rpx;
}

.product-card {
  background: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.product-image {
  width: 100%;
  height: 400rpx;
  background-color: #f5f5f5;
}

.product-info {
  padding: 20rpx;
}

.product-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.product-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF5722;
}

.product-sales {
  font-size: 24rpx;
  color: #999;
}

.product-commission {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.commission-rate {
  font-size: 24rpx;
  color: #666;
}

.commission-value {
  font-size: 24rpx;
  color: #6B0FBE;
  font-weight: 600;
}

.promote-btn {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  padding: 12rpx 0;
  line-height: 1.5;
  width: 100%;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 26rpx;
  color: #666;
}

/* 推广弹窗 */
.promotion-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  border-radius: 40rpx 40rpx 0 0;
  padding: 30rpx;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-icon {
  width: 40rpx;
  height: 40rpx;
  background-color: #999;
  border-radius: 40rpx;
}

.promotion-options {
  display: flex;
  justify-content: space-around;
  padding: 30rpx 0;
}

.promotion-option {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.option-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
  border-radius: 40rpx;
}

.option-icon.poster {
  background-color: #FF9500;
}

.option-icon.qrcode {
  background-color: #34C759;
}

.option-icon.link {
  background-color: #1677FF;
}

.option-icon.share {
  background-color: #6B0FBE;
}

.option-name {
  font-size: 26rpx;
  color: #333;
}
</style> 