<template>
  <view class="detail-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-left" @click="goBack">
        <image class="back-icon" src="/static/icons/back-white.svg"></image>
      </view>
      <view class="navbar-title">失物招领详情</view>
      <view class="navbar-right">
        <image class="share-icon" src="/static/icons/share.svg" @click="shareInfo"></image>
      </view>
    </view>
    
    <!-- 详情内容 -->
    <view class="detail-wrapper">
      <!-- 基本信息卡片 -->
      <view class="content-card">
        <view class="title-row">
          <text class="main-title">{{lostFoundData.title}}</text>
          <text class="status-tag" :class="lostFoundData.type === 'lost' ? 'lost-tag' : 'found-tag'">
            {{lostFoundData.type === 'lost' ? '寻物启事' : '招领启事'}}
          </text>
        </view>
        
        <view class="info-row">
          <view class="info-item">
            <text class="info-label">物品类型</text>
            <text class="info-value">{{lostFoundData.itemType}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">{{lostFoundData.type === 'lost' ? '丢失时间' : '拾获时间'}}</text>
            <text class="info-value">{{lostFoundData.time}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">{{lostFoundData.type === 'lost' ? '丢失地点' : '拾获地点'}}</text>
            <text class="info-value">{{lostFoundData.location}}</text>
          </view>
        </view>
      </view>
      
      <!-- 物品图片 -->
      <view class="content-card" v-if="lostFoundData.images && lostFoundData.images.length > 0">
        <view class="section-title">物品图片</view>
        <view class="image-grid">
          <image 
            v-for="(img, index) in lostFoundData.images" 
            :key="index" 
            :src="img" 
            class="item-image"
            @click="previewImage(index)"
          ></image>
        </view>
      </view>
      
      <!-- 详细描述 -->
      <view class="content-card">
        <view class="section-title">详细描述</view>
        <view class="description-content">
          {{lostFoundData.description}}
        </view>
      </view>
      
      <!-- 联系方式 -->
      <view class="content-card">
        <view class="section-title">联系方式</view>
        <view class="contact-info">
          <view class="contact-item">
            <text class="contact-label">联系人</text>
            <text class="contact-value">{{lostFoundData.contactName}}</text>
          </view>
          <view class="contact-item">
            <text class="contact-label">联系电话</text>
            <text class="contact-value">{{lostFoundData.contactPhone}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-action-bar">
      <view class="action-btn collect-btn" @click="toggleCollect">
        <image class="action-icon" :src="isCollected ? '/static/icons/star-filled.svg' : '/static/icons/star.svg'"></image>
        <text>{{isCollected ? '已收藏' : '收藏'}}</text>
      </view>
      <view class="action-btn contact-btn" @click="contactPerson">联系Ta</view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { onLoad } from '@dcloudio/uni-app';

// --- 响应式状态 ---
const lostFoundId = ref(null);
const isCollected = ref(false);
const lostFoundData = reactive({
  id: 1,
  type: 'lost', // 'lost' 或 'found'
  title: '寻找丢失的黑色钱包',
  itemType: '钱包/证件',
  time: '2023-05-15 14:30',
  location: '市民广场公交站',
  description: '今天下午在市民广场公交站等车时不慎丢失一个黑色长款钱包，内有身份证、银行卡等重要证件。如有拾到，请联系我，必有酬谢！',
  contactName: '张先生',
  contactPhone: '13812345678',
  images: [
    '/static/demo/lost1.jpg',
    '/static/demo/lost2.jpg'
  ],
  publishTime: '2023-05-15 16:45',
  views: 128
});

// --- 方法 ---

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 预览图片
const previewImage = (index) => {
  uni.previewImage({
    current: index,
    urls: lostFoundData.images
  });
};

// 分享信息
const shareInfo = () => {
  uni.share({
    provider: "weixin",
    scene: "WXSceneSession",
    type: 0,
    title: lostFoundData.title,
    summary: lostFoundData.description.substring(0, 40) + '...',
    imageUrl: lostFoundData.images[0],
    success: function (res) {
      console.log("success:" + JSON.stringify(res));
    },
    fail: function (err) {
      console.log("fail:" + JSON.stringify(err));
    }
  });
};

// 切换收藏状态
const toggleCollect = () => {
  isCollected.value = !isCollected.value;
  uni.showToast({
    title: isCollected.value ? '收藏成功' : '已取消收藏',
    icon: 'success'
  });
};

// 联系人
const contactPerson = () => {
  uni.makePhoneCall({
    phoneNumber: lostFoundData.contactPhone
  });
};

// --- 生命周期 ---
onLoad((options) => {
  if (options.id) {
    lostFoundId.value = options.id;
    // 实际项目中应该根据ID从服务器获取数据
    // getLostFoundDetail(lostFoundId.value);
  }
});

</script>

<style lang="scss">
.detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 110rpx;
  padding-top: 150rpx; /* 增加顶部内边距，为固定导航栏留出空间 */
}

/* 自定义导航栏 */
.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}

.navbar-left {
  position: absolute;
  left: 30rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  color: #FFFFFF;
  font-size: 36rpx;
  font-weight: 600;
  text-align: center;
  flex: 1;
}

.navbar-right {
  position: absolute;
  right: 30rpx;
}

.share-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 详情包装器 */
.detail-wrapper {
  padding: 24rpx;
  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */
}

/* 内容卡片通用样式 */
.content-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

/* 标题行 */
.title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.main-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.status-tag {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  color: #fff;
}

.lost-tag {
  background-color: #ff6b6b;
}

.found-tag {
  background-color: #4caf50;
}

/* 信息行 */
.info-row {
  margin-top: 20rpx;
}

.info-item {
  display: flex;
  margin-bottom: 16rpx;
}

.info-label {
  width: 160rpx;
  color: #666;
  font-size: 28rpx;
}

.info-value {
  flex: 1;
  color: #333;
  font-size: 28rpx;
}

/* 章节标题 */
.section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 6rpx;
  height: 32rpx;
  width: 8rpx;
  background: linear-gradient(to bottom, #0066FF, #0052CC);
  border-radius: 4rpx;
}

/* 图片网格 */
.image-grid {
  display: flex;
  flex-wrap: wrap;
}

.item-image {
  width: 210rpx;
  height: 210rpx;
  margin-right: 15rpx;
  margin-bottom: 15rpx;
  border-radius: 8rpx;
  background-color: #f5f7fa;
}

/* 描述内容 */
.description-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

/* 联系信息 */
.contact-info {
  margin-top: 10rpx;
}

.contact-item {
  display: flex;
  margin-bottom: 16rpx;
}

.contact-label {
  width: 160rpx;
  color: #666;
  font-size: 28rpx;
}

.contact-value {
  flex: 1;
  color: #333;
  font-size: 28rpx;
}

/* 底部操作栏 */
.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 90;
}

.action-btn {
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 30rpx;
}

.collect-btn {
  width: 180rpx;
  margin-right: 20rpx;
  color: #666;
  background-color: #f5f7fa;
}

.action-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 8rpx;
}

.contact-btn {
  flex: 1;
  background: linear-gradient(135deg, #0066FF, #0052CC);
  color: #fff;
}
</style> 