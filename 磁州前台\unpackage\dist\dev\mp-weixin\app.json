{"pages": ["pages/index/index", "pages/test-navigation", "pages/redirect-fix", "pages/publish/info-detail", "pages/publish/job-detail", "pages/publish/home-service-detail", "pages/publish/business-transfer-detail", "pages/publish/find-service-detail", "pages/publish/job-seeking-detail", "pages/publish/house-rent-detail", "pages/publish/house-sale-detail", "pages/publish/car-detail", "pages/publish/pet-detail", "pages/publish/second-hand-detail", "pages/publish/dating-detail", "pages/publish/merchant-activity-detail", "pages/publish/carpool-detail", "pages/publish/education-detail", "pages/carpool-entry/index", "pages/business/business", "pages/publish/publish", "pages/business/join", "pages/business/success", "pages/business/verify", "pages/business/verify-success", "pages/business/shop-detail", "pages/business/filter", "pages/group/group", "pages/my/my", "pages/my/publish", "pages/my/refresh-package", "pages/location/select", "pages/error/index", "pages/user/my-red-packets", "pages/user/group-orders", "pages/red-packet/detail", "pages/red-packet/list", "pages/publish/detail", "pages/test/shop-test", "pages/message/chat", "pages/user-center/profile", "pages/user-center/activities", "pages/publish/success", "pages/wallet/index"], "subPackages": [{"root": "subPackages/partner", "pages": ["pages/partner", "pages/partner-levels", "pages/partner-poster", "pages/partner-fans", "pages/partner-team", "pages/partner-income", "pages/partner-withdraw", "pages/partner-qrcode", "pages/partner-rules"]}, {"root": "subPackages/payment", "pages": ["pages/index", "pages/success", "pages/wallet", "pages/withdraw", "pages/recharge", "pages/detail", "pages/bills", "pages/bank"]}, {"root": "subPackages/activity", "pages": ["pages/square", "pages/detail-with-rewards", "pages/city-events", "pages/detail", "pages/list"]}, {"root": "subPackages/service", "pages": ["pages/filter", "pages/list", "pages/category", "pages/home-service-list"]}, {"root": "subPackages/user", "pages": ["pages/group-orders", "pages/my-red-packets", "pages/profile", "pages/my-benefits", "pages/messages", "pages/history", "pages/call-history", "pages/favorites"]}, {"root": "carpool-package", "pages": ["pages/carpool-main/index", "pages/carpool/groups/index", "pages/carpool/groups/create", "pages/carpool/my/index", "pages/carpool/detail/index", "pages/carpool/my/message-center", "pages/carpool/my/driver-verification", "pages/carpool/my/feedback", "pages/carpool/my/trip-records", "pages/carpool/my/driver-ratings", "pages/carpool/my/driver-profile", "pages/carpool/my/contact-history", "pages/carpool/my/published-list", "pages/carpool/my/pending-list", "pages/carpool/my/expired-list", "pages/carpool/my/favorites", "pages/carpool/publish/people-to-car", "pages/carpool/publish/car-to-people", "pages/carpool/publish/goods-to-car", "pages/carpool/publish/car-to-goods", "pages/carpool/publish/success", "pages/carpool/premium/top", "pages/carpool/my/create-rating"]}, {"root": "subPackages/news", "pages": ["pages/list", "pages/detail"]}, {"root": "subPackages/checkin", "pages": ["pages/daily-checkin", "pages/points", "pages/points-detail", "pages/points-rank", "pages/points-mall", "pages/exchange-history"]}, {"root": "subPackages/franchise", "pages": ["pages/index", "pages/application-form", "pages/agreement"]}, {"root": "subPackages/merchant-admin-home", "pages": ["pages/merchant", "pages/merchant-home/index", "pages/settings/index"]}, {"root": "subPackages/merchant-admin-marketing", "pages": ["pages/marketing/index", "pages/marketing/coupon/detail", "pages/marketing/coupon/edit", "pages/marketing/coupon/create", "pages/marketing/coupon/records", "pages/marketing/coupon/management", "pages/marketing/discount/management", "pages/marketing/discount/detail", "pages/marketing/discount/edit", "pages/marketing/discount/create", "pages/marketing/group/management", "pages/marketing/group/create", "pages/marketing/group/package-management", "pages/marketing/group/package-detail", "pages/marketing/group/create-package-type", "pages/marketing/group/create-package-info", "pages/marketing/group/create-package-price", "pages/marketing/group/create-package-items", "pages/marketing/group/create-package-confirm", "pages/marketing/flash/management", "pages/marketing/flash/detail", "pages/marketing/flash/edit", "pages/marketing/flash/create", "pages/marketing/points/management", "pages/marketing/points/create", "pages/marketing/points/rules", "pages/marketing/create-coupon", "pages/marketing/redpacket/index", "pages/marketing/redpacket/create", "pages/marketing/redpacket/edit", "pages/marketing/redpacket/detail", "pages/marketing/redpacket/data-analysis", "pages/marketing/redpacket/guide", "pages/marketing/redpacket/settings", "pages/marketing/redpacket/mass-sending", "pages/marketing/redpacket/red-rain", "pages/marketing/redpacket/fission", "pages/marketing/redpacket/create-template", "pages/marketing/redpacket/tool", "pages/marketing/redpacket/festival-redpacket-strategy", "pages/marketing/redpacket/redpacket-marketing-overview", "pages/marketing/redpacket/fission-redpacket-guide", "pages/marketing/ai/trend-analysis", "pages/marketing/distribution/index", "pages/marketing/distribution/qrcode", "pages/marketing/distribution/product-cards", "pages/marketing/distribution/channels", "pages/marketing/distribution/reports", "pages/marketing/distribution/promotion-tool", "pages/marketing/distribution/create-activity", "pages/marketing/distribution/pay-commission", "pages/marketing/distribution/add-distributor", "pages/marketing/distribution/agreement", "pages/marketing/distribution/commission-rules", "pages/marketing/distribution/withdrawal", "pages/marketing/distribution/levels", "pages/marketing/distribution/conditions", "pages/marketing/distribution/distributor-detail", "pages/marketing/distribution/distributors", "pages/marketing/verification/index", "pages/marketing/verification/scan", "pages/marketing/verification/manual", "pages/marketing/verification/records", "pages/marketing/member/member-level", "pages/marketing/member/member-privilege", "pages/marketing/member/tool", "pages/marketing/member/points-rule", "pages/marketing/member/growth-rule", "pages/marketing/member/birthday-privilege", "pages/marketing/member/member-agreement", "pages/marketing/member/add-level", "pages/marketing/member/add-privilege", "pages/marketing/member/member-discount", "pages/marketing/member/points-acceleration", "pages/marketing/member/free-shipping", "pages/marketing/member/birthday-gift", "pages/marketing/member/vip-service", "pages/marketing/member/points", "pages/marketing/member/tasks", "pages/marketing/member/coupons", "pages/marketing/member/activities"]}, {"root": "subPackages/merchant-admin-order", "pages": ["pages/order/index", "pages/orders/list", "pages/orders/detail", "pages/orders/after-sale"]}, {"root": "subPackages/merchant-admin-customer", "pages": ["pages/customer/index", "pages/marketing/distribution/index", "pages/marketing/distribution/distributors"]}, {"root": "subPackages/merchant-admin", "pages": ["pages/store/index", "pages/analysis/index", "pages/settings/index", "pages/activity/index", "pages/products/create", "pages/store/product"]}, {"root": "subPackages/promotion", "pages": ["pages/promotion-tool", "pages/qrcode"]}, {"root": "subPackages/distribution", "pages": ["pages/index", "pages/apply", "pages/products", "pages/team", "pages/commission", "pages/withdraw", "pages/promotion", "pages/merchant-apply"]}, {"root": "subPackages/cashback", "pages": ["pages/index/index", "pages/search/index", "pages/platforms/index", "pages/category/index", "pages/product-detail/index", "pages/life-cashback/index", "pages/life-service/index"]}, {"root": "subPackages/activity-showcase", "pages": ["pages/index/index", "pages/flash-sale/index", "pages/group-buy/index", "pages/discount/index", "pages/coupon/index", "pages/detail/index", "pages/flash-sale/detail", "pages/coupon/detail", "pages/discount/detail", "pages/my/index", "pages/discover/index", "pages/message/index", "pages/message/detail", "pages/search/index", "pages/distribution/index", "pages/distribution/orders/index", "pages/distribution/team/index", "pages/distribution/earnings/index", "pages/distribution/withdraw/index", "pages/distribution/records/index", "pages/distribution/poster/index", "pages/distribution/products/index", "pages/orders/index", "pages/activity-records/index", "pages/reminders/index", "pages/share-records/index", "pages/shops/index", "pages/products/hot/index", "pages/promotion-center/index", "pages/shops/detail", "pages/user-profile/index", "pages/favorites/index", "pages/history/index", "pages/cart/index", "pages/products/reviews/index", "pages/orders/confirm/index"]}], "window": {"navigationBarTextStyle": "white", "navigationBarTitleText": "磁州生活网", "navigationBarBackgroundColor": "#0052CC", "backgroundColor": "#F8F8F8"}, "tabBar": {"color": "#7A7E83", "selectedColor": "#0052CC", "backgroundColor": "#ffffff", "borderStyle": "black", "list": [{"pagePath": "pages/index/index", "text": "首页", "iconPath": "static/images/tabbar/home-grey.png", "selectedIconPath": "static/images/tabbar/home-blue.png"}, {"pagePath": "pages/business/business", "text": "商圈", "iconPath": "static/images/tabbar/shop-grey.png", "selectedIconPath": "static/images/tabbar/shop-blue.png"}, {"pagePath": "pages/publish/publish", "text": "发布", "iconPath": "static/images/tabbar/edit-grey.png", "selectedIconPath": "static/images/tabbar/edit-blue.png"}, {"pagePath": "pages/carpool-entry/index", "text": "拼车", "iconPath": "static/images/tabbar/拼车.png", "selectedIconPath": "static/images/tabbar/拼车选中.png"}, {"pagePath": "pages/my/my", "text": "我的", "iconPath": "static/images/tabbar/user-grey.png", "selectedIconPath": "static/images/tabbar/user-blue.png"}]}, "preloadRule": {"pages/index/index": {"network": "all", "packages": ["subPackages/partner", "subPackages/payment", "subPackages/activity", "subPackages/service", "subPackages/user", "subPackages/checkin", "subPackages/franchise", "subPackages/merchant-admin-home", "subPackages/merchant-admin-marketing", "subPackages/merchant-admin-customer", "subPackages/merchant-admin", "carpool-package", "subPackages/distribution", "subPackages/cashback", "subPackages/activity-showcase"]}, "pages/my/my": {"network": "all", "packages": ["subPackages/activity-showcase"]}}, "networkTimeout": {"request": 60000, "connectSocket": 60000, "uploadFile": 60000, "downloadFile": 60000}, "requiredPrivateInfos": ["getLocation"], "permission": {"scope.userLocation": {"desc": "获取您的位置信息用于为您提供更好的同城服务"}}, "lazyCodeLoading": "requiredComponents", "usingComponents": {"cu-custom": "/components/cu-custom"}}