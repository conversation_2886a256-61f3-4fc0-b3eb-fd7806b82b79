{"version": 3, "file": "category.js", "sources": ["subPackages/service/pages/category.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcc2VydmljZVxwYWdlc1xjYXRlZ29yeS52dWU"], "sourcesContent": ["<template>\n  <view class=\"service-category-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"back-btn\" @click=\"navigateBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <view class=\"navbar-title\">选择服务类型</view>\n      <view class=\"navbar-right\"></view>\n    </view>\n    \n    <!-- 提示文本 -->\n    <view class=\"service-tip\">\n      <text class=\"tip-text\">请选择服务类型</text>\n      <text class=\"tip-desc\">选择您需要的服务类型，查看相关信息</text>\n    </view>\n    \n    <!-- 服务类型列表 -->\n    <view class=\"service-grid\">\n      <!-- 家政服务 -->\n      <view class=\"service-item\" @click=\"selectServiceType('家政服务', 'home_cleaning')\">\n        <view class=\"service-icon-wrapper\">\n          <image class=\"service-icon\" src=\"/static/images/tabbar/123/家政服务.png\" mode=\"aspectFit\"></image>\n        </view>\n        <text class=\"service-name\">家政服务</text>\n      </view>\n      \n      <!-- 维修改造 -->\n      <view class=\"service-item\" @click=\"selectServiceType('维修改造', 'repair')\">\n        <view class=\"service-icon-wrapper\">\n          <image class=\"service-icon\" src=\"/static/images/tabbar/123/维修改造.png\" mode=\"aspectFit\"></image>\n        </view>\n        <text class=\"service-name\">维修改造</text>\n      </view>\n      \n      <!-- 上门安装 -->\n      <view class=\"service-item\" @click=\"selectServiceType('上门安装', 'installation')\">\n        <view class=\"service-icon-wrapper\">\n          <image class=\"service-icon\" src=\"/static/images/tabbar/123/上门安装.png\" mode=\"aspectFit\"></image>\n        </view>\n        <text class=\"service-name\">上门安装</text>\n      </view>\n      \n      <!-- 开锁换锁 -->\n      <view class=\"service-item\" @click=\"selectServiceType('开锁换锁', 'locksmith')\">\n        <view class=\"service-icon-wrapper\">\n          <image class=\"service-icon\" src=\"/static/images/tabbar/123/开锁换锁.png\" mode=\"aspectFit\"></image>\n        </view>\n        <text class=\"service-name\">开锁换锁</text>\n      </view>\n      \n      <!-- 搬家拉货 -->\n      <view class=\"service-item\" @click=\"selectServiceType('搬家拉货', 'moving')\">\n        <view class=\"service-icon-wrapper\">\n          <image class=\"service-icon\" src=\"/static/images/tabbar/123/搬家拉货.png\" mode=\"aspectFit\"></image>\n        </view>\n        <text class=\"service-name\">搬家拉货</text>\n      </view>\n      \n      <!-- 上门美容 -->\n      <view class=\"service-item\" @click=\"selectServiceType('上门美容', 'beauty')\">\n        <view class=\"service-icon-wrapper\">\n          <image class=\"service-icon\" src=\"/static/images/tabbar/123/上门美容.png\" mode=\"aspectFit\"></image>\n        </view>\n        <text class=\"service-name\">上门美容</text>\n      </view>\n      \n      <!-- 上门家教 -->\n      <view class=\"service-item\" @click=\"selectServiceType('上门家教', 'tutor')\">\n        <view class=\"service-icon-wrapper\">\n          <image class=\"service-icon\" src=\"/static/images/tabbar/123/上门家教.png\" mode=\"aspectFit\"></image>\n        </view>\n        <text class=\"service-name\">上门家教</text>\n      </view>\n      \n      <!-- 宠物服务 -->\n      <view class=\"service-item\" @click=\"selectServiceType('宠物服务', 'pet_service')\">\n        <view class=\"service-icon-wrapper\">\n          <image class=\"service-icon\" src=\"/static/images/tabbar/123/宠物服务.png\" mode=\"aspectFit\"></image>\n        </view>\n        <text class=\"service-name\">宠物服务</text>\n      </view>\n      \n      <!-- 上门疏通 -->\n      <view class=\"service-item\" @click=\"selectServiceType('上门疏通', 'plumbing')\">\n        <view class=\"service-icon-wrapper\">\n          <image class=\"service-icon\" src=\"/static/images/tabbar/123/上门疏通.png\" mode=\"aspectFit\"></image>\n        </view>\n        <text class=\"service-name\">上门疏通</text>\n      </view>\n      \n      <!-- 其他类型 -->\n      <view class=\"service-item\" @click=\"selectServiceType('其他类型', 'other')\">\n        <view class=\"service-icon-wrapper\">\n          <image class=\"service-icon\" src=\"/static/images/tabbar/123/其他类型.png\" mode=\"aspectFit\"></image>\n        </view>\n        <text class=\"service-name\">其他类型</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue';\n\n// 响应式状态\nconst statusBarHeight = ref(20);\n\n// 方法\n// 返回上一页\nconst navigateBack = () => {\n  uni.navigateBack();\n};\n\n// 选择服务类型\nconst selectServiceType = (name, type) => {\n  uni.navigateTo({\n    url: `/subPackages/service/pages/home-service-list?subType=${type}&subName=${encodeURIComponent(name)}`\n  });\n};\n\n// 生命周期钩子\nonMounted(() => {\n  // 获取状态栏高度\n  const sysInfo = uni.getSystemInfoSync();\n  statusBarHeight.value = sysInfo.statusBarHeight;\n});\n</script>\n\n<style>\n.service-category-container {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  padding-bottom: 30rpx;\n}\n\n.custom-navbar {\n  display: flex;\n  align-items: center;\n  height: 44px;\n  background-color: #1677FF;\n  padding-left: 10px;\n  padding-right: 10px;\n  position: relative;\n  z-index: 100;\n}\n\n.back-btn {\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-top: 2px solid #fff;\n  border-left: 2px solid #fff;\n  transform: rotate(-45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  color: #fff;\n  font-size: 18px;\n  font-weight: 500;\n}\n\n.navbar-right {\n  width: 40px;\n  height: 40px;\n}\n\n.service-tip {\n  padding: 30rpx;\n  background-color: #fff;\n}\n\n.tip-text {\n  font-size: 36rpx;\n  font-weight: 500;\n  color: #333;\n  display: block;\n  margin-bottom: 10rpx;\n}\n\n.tip-desc {\n  font-size: 28rpx;\n  color: #999;\n}\n\n.service-grid {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 20rpx;\n  background-color: #fff;\n  margin-top: 20rpx;\n}\n\n.service-item {\n  width: 20%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 40rpx;\n}\n\n.service-icon-wrapper {\n  width: 140rpx;\n  height: 140rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.service-icon {\n  width: 86rpx;\n  height: 86rpx;\n  object-fit: contain;\n}\n\n.service-name {\n  font-size: 26rpx;\n  color: #333;\n  text-align: center;\n}\n</style> \n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/service/pages/category.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "onMounted", "MiniProgramPage"], "mappings": ";;;;;;AA0GA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAI9B,UAAM,eAAe,MAAM;AACzBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,oBAAoB,CAAC,MAAM,SAAS;AACxCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,wDAAwD,IAAI,YAAY,mBAAmB,IAAI,CAAC;AAAA,MACzG,CAAG;AAAA,IACH;AAGAC,kBAAAA,UAAU,MAAM;AAEd,YAAM,UAAUD,oBAAI;AACpB,sBAAgB,QAAQ,QAAQ;AAAA,IAClC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7HD,GAAG,WAAWE,SAAe;"}