{"name": "cizhou-admin-frontend", "version": "1.0.0", "description": "磁州生活网后台管理系统前端", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "lint:style": "stylelint **/*.{css,scss,vue} --fix", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "js-cookie": "^3.0.5", "crypto-js": "^4.2.0", "file-saver": "^2.0.5", "xlsx": "^0.18.5"}, "devDependencies": {"@types/node": "^20.10.5", "@types/lodash-es": "^4.17.12", "@types/nprogress": "^0.2.3", "@types/js-cookie": "^3.0.6", "@types/crypto-js": "^4.2.1", "@types/file-saver": "^2.0.7", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.3", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "jsdom": "^23.0.1", "postcss": "^8.4.32", "prettier": "^3.1.1", "stylelint": "^16.1.0", "stylelint-config-standard": "^36.0.0", "stylelint-config-standard-vue": "^1.0.0", "tailwindcss": "^3.4.0", "typescript": "^5.3.3", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.10", "vitest": "^1.1.0", "vue-tsc": "^1.8.25"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}