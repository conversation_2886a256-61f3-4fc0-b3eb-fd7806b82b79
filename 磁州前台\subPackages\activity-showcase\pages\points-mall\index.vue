<template>
  <view class="points-mall-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="15 18 9 12 15 6"></polyline>
          </svg>
        </view>
        <view class="navbar-title">积分商城</view>
        <view class="navbar-right">
          <view class="points-info" @click="showPointsDetail">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polygon points="12 2 15.09 8.26 22 9 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9 8.91 8.26 12 2"></polygon>
            </svg>
            <text class="points-text">{{ userPoints }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y @scrolltolower="loadMore">
      <!-- 积分余额卡片 -->
      <view class="points-balance-card">
        <view class="balance-header">
          <view class="balance-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polygon points="12 2 15.09 8.26 22 9 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9 8.91 8.26 12 2"></polygon>
            </svg>
          </view>
          <view class="balance-info">
            <text class="balance-title">我的积分</text>
            <text class="balance-amount">{{ userPoints }}</text>
          </view>
          <view class="balance-actions">
            <view class="action-btn" @click="navigateTo('/subPackages/activity-showcase/pages/checkin/index')">
              <text class="action-text">签到赚积分</text>
            </view>
          </view>
        </view>
        <view class="balance-tips">
          <text class="tips-text">积分可用于兑换商品，完成任务获得更多积分</text>
        </view>
      </view>

      <!-- 分类导航 -->
      <view class="category-nav">
        <scroll-view class="category-scroll" scroll-x show-scrollbar="false">
          <view 
            class="category-item" 
            v-for="(category, index) in categories" 
            :key="index"
            :class="{ active: currentCategory === category.id }"
            @click="switchCategory(category.id)"
          >
            <view class="category-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path v-if="category.type === 'all'" d="M3 3h18v18H3zM12 8v8M8 12h8"></path>
                <path v-else-if="category.type === 'virtual'" d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                <path v-else-if="category.type === 'physical'" d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8z"></path>
                <circle v-else cx="12" cy="12" r="10"></circle>
              </svg>
            </view>
            <text class="category-name">{{ category.name }}</text>
          </view>
        </scroll-view>
      </view>

      <!-- 商品列表 -->
      <view class="products-grid">
        <view 
          class="product-item" 
          v-for="(product, index) in filteredProducts" 
          :key="product.id"
          @click="navigateToProductDetail(product)"
        >
          <view class="product-image-container">
            <image class="product-image" :src="product.image" mode="aspectFill" :lazy-load="true"></image>
            <view class="product-badge" v-if="product.badge">
              <text class="badge-text">{{ product.badge }}</text>
            </view>
          </view>
          <view class="product-info">
            <text class="product-name">{{ product.name }}</text>
            <text class="product-desc">{{ product.description }}</text>
            <view class="product-price">
              <view class="points-price">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polygon points="12 2 15.09 8.26 22 9 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9 8.91 8.26 12 2"></polygon>
                </svg>
                <text class="points-text">{{ product.points }}</text>
              </view>
              <text class="original-price" v-if="product.originalPrice">¥{{ product.originalPrice }}</text>
            </view>
            <view class="product-stock">
              <text class="stock-text">库存：{{ product.stock }}</text>
            </view>
          </view>
          <view class="exchange-btn" @click.stop="exchangeProduct(product)">
            <text class="exchange-text">立即兑换</text>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="loading-more" v-if="loading">
        <text>加载中...</text>
      </view>
      
      <!-- 没有更多数据 -->
      <view class="no-more" v-if="noMore && filteredProducts.length > 0">
        <text>已经到底啦~</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="filteredProducts.length === 0 && !loading">
        <view class="empty-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M16 16s-1.5-2-4-2-4 2-4 2"></path>
            <line x1="9" y1="9" x2="9.01" y2="9"></line>
            <line x1="15" y1="9" x2="15.01" y2="9"></line>
          </svg>
        </view>
        <text class="empty-text">暂无商品</text>
        <text class="empty-desc">该分类下暂时没有可兑换的商品</text>
      </view>

      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>

    <!-- 积分详情弹窗 -->
    <view class="points-modal" v-if="showPointsModal" @click="hidePointsDetail">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">积分明细</text>
          <view class="close-btn" @click="hidePointsDetail">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </view>
        </view>
        <view class="modal-body">
          <view class="points-summary">
            <view class="summary-item">
              <text class="summary-label">总积分</text>
              <text class="summary-value">{{ userPoints }}</text>
            </view>
            <view class="summary-item">
              <text class="summary-label">今日获得</text>
              <text class="summary-value">+{{ todayPoints }}</text>
            </view>
            <view class="summary-item">
              <text class="summary-label">本月获得</text>
              <text class="summary-value">+{{ monthPoints }}</text>
            </view>
          </view>
          <view class="points-actions">
            <view class="action-item" @click="navigateTo('/subPackages/activity-showcase/pages/checkin/index')">
              <text class="action-title">每日签到</text>
              <text class="action-desc">每天签到获得积分</text>
            </view>
            <view class="action-item" @click="navigateTo('/subPackages/activity-showcase/pages/invite/index')">
              <text class="action-title">邀请好友</text>
              <text class="action-desc">邀请好友获得积分奖励</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const userPoints = ref(1580)
const todayPoints = ref(20)
const monthPoints = ref(350)
const currentCategory = ref('all')
const loading = ref(false)
const noMore = ref(false)
const showPointsModal = ref(false)

// 分类数据
const categories = ref([
  { id: 'all', name: '全部', type: 'all' },
  { id: 'virtual', name: '虚拟商品', type: 'virtual' },
  { id: 'physical', name: '实物商品', type: 'physical' },
  { id: 'coupon', name: '优惠券', type: 'coupon' }
])

// 商品数据
const products = ref([
  {
    id: 1,
    name: '10元优惠券',
    description: '全场通用，满50可用',
    image: '/static/images/points/coupon-10.jpg',
    points: 100,
    originalPrice: 10,
    stock: 999,
    category: 'coupon',
    badge: '热门'
  },
  {
    id: 2,
    name: '20元优惠券',
    description: '全场通用，满100可用',
    image: '/static/images/points/coupon-20.jpg',
    points: 180,
    originalPrice: 20,
    stock: 888,
    category: 'coupon'
  },
  {
    id: 3,
    name: '会员月卡',
    description: '享受会员专属权益',
    image: '/static/images/points/vip-month.jpg',
    points: 500,
    originalPrice: 30,
    stock: 100,
    category: 'virtual',
    badge: '推荐'
  },
  {
    id: 4,
    name: '精美水杯',
    description: '品牌定制，质量保证',
    image: '/static/images/points/cup.jpg',
    points: 800,
    originalPrice: 50,
    stock: 50,
    category: 'physical'
  },
  {
    id: 5,
    name: '蓝牙耳机',
    description: '高品质音效，舒适佩戴',
    image: '/static/images/points/earphone.jpg',
    points: 1500,
    originalPrice: 99,
    stock: 20,
    category: 'physical',
    badge: '限量'
  }
])

// 计算属性 - 过滤商品
const filteredProducts = computed(() => {
  if (currentCategory.value === 'all') {
    return products.value
  }
  return products.value.filter(product => product.category === currentCategory.value)
})

// 页面加载
onMounted(() => {
  console.log('积分商城页面加载')
  loadUserPoints()
})

// 方法
function goBack() {
  uni.navigateBack()
}

function navigateTo(url) {
  uni.navigateTo({ url })
}

function switchCategory(categoryId) {
  currentCategory.value = categoryId
}

function showPointsDetail() {
  showPointsModal.value = true
}

function hidePointsDetail() {
  showPointsModal.value = false
}

function navigateToProductDetail(product) {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/products/detail/index?id=${product.id}&type=points`
  })
}

function exchangeProduct(product) {
  if (userPoints.value < product.points) {
    uni.showToast({
      title: '积分不足',
      icon: 'none'
    })
    return
  }
  
  uni.showModal({
    title: '确认兑换',
    content: `确定要用${product.points}积分兑换${product.name}吗？`,
    success: (res) => {
      if (res.confirm) {
        // 执行兑换逻辑
        userPoints.value -= product.points
        uni.showToast({
          title: '兑换成功',
          icon: 'success'
        })
      }
    }
  })
}

function loadUserPoints() {
  // 模拟加载用户积分
  setTimeout(() => {
    userPoints.value = 1580
    todayPoints.value = 20
    monthPoints.value = 350
  }, 500)
}

function loadMore() {
  if (loading.value || noMore.value) return
  
  loading.value = true
  
  // 模拟加载更多数据
  setTimeout(() => {
    // 这里可以添加更多商品数据
    noMore.value = true
    loading.value = false
  }, 1000)
}
</script>

<style scoped>
/* 积分商城样式开始 */
.points-mall-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
}

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 107, 53, 0.95);
  backdrop-filter: blur(10px);
  padding-top: var(--status-bar-height, 44px);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
}

.back-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.2);
}

.back-btn svg {
  color: white;
}

.navbar-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
}

.navbar-right {
  display: flex;
  align-items: center;
}

.points-info {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  gap: 4px;
}

.points-info svg {
  color: #FFD700;
}

.points-text {
  font-size: 14px;
  font-weight: 600;
  color: white;
}

/* 内容区域样式 */
.content-scroll {
  padding-top: calc(var(--status-bar-height, 44px) + 44px);
  height: 100vh;
}

/* 积分余额卡片样式 */
.points-balance-card {
  margin: 20px 16px;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.balance-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
}

.balance-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-radius: 24px;
}

.balance-icon svg {
  color: white;
}

.balance-info {
  flex: 1;
}

.balance-title {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.balance-amount {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #FF6B35;
}

.action-btn {
  padding: 8px 16px;
  background: linear-gradient(135deg, #FF6B35, #F7931E);
  border-radius: 20px;
}

.action-text {
  font-size: 12px;
  color: white;
  font-weight: 500;
}

.balance-tips {
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.tips-text {
  font-size: 12px;
  color: #999;
}

/* 分类导航样式 */
.category-nav {
  margin: 0 16px 20px;
}

.category-scroll {
  white-space: nowrap;
}

.category-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 16px;
  margin-right: 8px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  min-width: 80px;
  transition: all 0.3s ease;
}

.category-item.active {
  background: white;
  box-shadow: 0 2px 12px rgba(255, 107, 53, 0.3);
}

.category-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6px;
}

.category-item.active .category-icon svg {
  color: #FF6B35;
}

.category-icon svg {
  color: #666;
}

.category-name {
  font-size: 12px;
  color: #333;
  font-weight: 500;
}

/* 商品网格样式 */
.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  padding: 0 16px;
}

.product-item {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.product-item:active {
  transform: scale(0.98);
}

.product-image-container {
  position: relative;
  width: 100%;
  height: 120px;
}

.product-image {
  width: 100%;
  height: 100%;
}

.product-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  padding: 2px 8px;
  background: #FF6B35;
  border-radius: 10px;
}

.badge-text {
  font-size: 10px;
  color: white;
  font-weight: 500;
}

.product-info {
  padding: 12px;
}

.product-name {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-desc {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
}

.points-price {
  display: flex;
  align-items: center;
  gap: 4px;
}

.points-price svg {
  color: #FFD700;
}

.points-price .points-text {
  font-size: 16px;
  font-weight: 700;
  color: #FF6B35;
}

.original-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

.product-stock {
  margin-bottom: 12px;
}

.stock-text {
  font-size: 12px;
  color: #666;
}

.exchange-btn {
  width: 100%;
  padding: 8px;
  background: linear-gradient(135deg, #FF6B35, #F7931E);
  border-radius: 8px;
  text-align: center;
}

.exchange-text {
  font-size: 14px;
  color: white;
  font-weight: 500;
}

/* 加载状态样式 */
.loading-more, .no-more {
  text-align: center;
  padding: 20px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 20px;
}

.empty-icon {
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-icon svg {
  color: white;
}

.empty-text {
  font-size: 16px;
  color: white;
  font-weight: 500;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

/* 积分详情弹窗样式 */
.points-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 400px;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: #f5f5f5;
}

.close-btn svg {
  color: #666;
}

.modal-body {
  padding: 20px;
}

.points-summary {
  margin-bottom: 24px;
}

.summary-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-label {
  font-size: 14px;
  color: #666;
}

.summary-value {
  font-size: 16px;
  font-weight: 600;
  color: #FF6B35;
}

.points-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-item {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.action-title {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.action-desc {
  display: block;
  font-size: 12px;
  color: #666;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  background: transparent;
}
/* 积分商城样式结束 */
</style>
