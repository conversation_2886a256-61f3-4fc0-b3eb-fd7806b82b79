"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  __name: "CityNews",
  setup(__props) {
    const noticeList = common_vendor.ref([
      "磁县人民政府关于加强城市管理的通知",
      "2023年磁县春节期间活动安排",
      "磁县城区道路施工公告",
      "关于开展全民健康体检的通知",
      "磁县文化中心活动预告"
    ]);
    function navigateToMore() {
      common_vendor.index.navigateTo({
        url: "/subPackages/news/pages/list",
        fail: () => {
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "none"
          });
        }
      });
    }
    function navigateToDetail(index) {
      common_vendor.index.navigateTo({
        url: `/subPackages/news/pages/detail?id=${index + 1}`,
        fail: () => {
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "none"
          });
        }
      });
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(navigateToMore),
        b: common_assets._imports_0$9,
        c: common_vendor.f(noticeList.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item),
            b: index,
            c: common_vendor.o(($event) => navigateToDetail(index), index)
          };
        })
      };
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-1c538d68"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/index/CityNews.js.map
