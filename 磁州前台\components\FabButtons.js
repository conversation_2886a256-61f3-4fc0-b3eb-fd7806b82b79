Component({
  properties: {
    pageName: {
      type: String,
      value: ''
    },
    pageInfo: {
      type: Object,
      value: {}
    }
  },
  data: {
    isOpen: false
  },
  methods: {
    toggleMenu() {
      this.setData({
        isOpen: !this.data.isOpen
      });
    },
    handleShare() {
      const { pageInfo } = this.properties;
      
      if (pageInfo && pageInfo.title) {
        wx.showShareMenu({
          withShareTicket: true,
          menus: ['shareAppMessage', 'shareTimeline']
        });
      } else {
        wx.showToast({
          title: '分享信息不完整',
          icon: 'none'
        });
      }
      
      this.setData({
        isOpen: false
      });
    }
  }
}) 