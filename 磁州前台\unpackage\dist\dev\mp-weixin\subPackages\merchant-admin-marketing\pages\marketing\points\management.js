"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
if (!Math) {
  CreateButton();
}
const CreateButton = () => "../../../components/CreateButton.js";
const _sfc_main = {
  __name: "management",
  setup(__props) {
    const pointsData = common_vendor.reactive({
      totalPoints: "125,680",
      usedPoints: "87,456",
      activeUsers: "1,254",
      conversionRate: "68.5"
    });
    const activeFilter = common_vendor.ref("all");
    const filterLabels = {
      all: "全部",
      active: "进行中",
      draft: "草稿",
      ended: "已结束"
    };
    const pointsItems = common_vendor.ref([
      {
        id: 1,
        name: "精美保温杯",
        points: 2e3,
        stock: 100,
        redeemed: 35,
        views: 1240,
        image: "/static/images/points-item1.jpg",
        timeRange: "2023-05-01 ~ 2023-12-31",
        status: "active",
        statusText: "进行中",
        statusClass: "status-active"
      },
      {
        id: 2,
        name: "无线蓝牙耳机",
        points: 5e3,
        stock: 50,
        redeemed: 12,
        views: 876,
        image: "/static/images/points-item2.jpg",
        timeRange: "2023-05-15 ~ 2023-12-31",
        status: "active",
        statusText: "进行中",
        statusClass: "status-active"
      },
      {
        id: 3,
        name: "会员月卡",
        points: 1e3,
        stock: 999,
        redeemed: 128,
        views: 3560,
        image: "/static/images/points-item3.jpg",
        timeRange: "2023-04-01 ~ 2023-12-31",
        status: "active",
        statusText: "进行中",
        statusClass: "status-active"
      },
      {
        id: 4,
        name: "限定礼盒套装",
        points: 8e3,
        stock: 20,
        redeemed: 0,
        views: 0,
        image: "/static/images/points-item4.jpg",
        timeRange: "未发布",
        status: "draft",
        statusText: "草稿",
        statusClass: "status-draft"
      },
      {
        id: 5,
        name: "春节限定福袋",
        points: 3e3,
        stock: 200,
        redeemed: 200,
        views: 4280,
        image: "/static/images/points-item5.jpg",
        timeRange: "2023-01-15 ~ 2023-02-15",
        status: "ended",
        statusText: "已结束",
        statusClass: "status-ended"
      }
    ]);
    const filteredItems = common_vendor.computed(() => {
      if (activeFilter.value === "all") {
        return pointsItems.value;
      }
      return pointsItems.value.filter((item) => item.status === activeFilter.value);
    });
    const setFilter = (filter) => {
      activeFilter.value = filter;
    };
    const viewItemDetail = (item) => {
      common_vendor.index.showToast({
        title: `查看商品: ${item.name}`,
        icon: "none"
      });
    };
    const editItem = (item) => {
      common_vendor.index.showToast({
        title: `编辑商品: ${item.name}`,
        icon: "none"
      });
    };
    const deleteItem = (item) => {
      common_vendor.index.showModal({
        title: "确认删除",
        content: `确定要删除"${item.name}"吗？`,
        success: (res) => {
          if (res.confirm) {
            const index = pointsItems.value.findIndex((i) => i.id === item.id);
            if (index !== -1) {
              pointsItems.value.splice(index, 1);
              common_vendor.index.showToast({
                title: "删除成功",
                icon: "success"
              });
            }
          }
        }
      });
    };
    const pointsRules = common_vendor.ref([
      {
        id: 1,
        type: "purchase",
        title: "商品购买",
        description: "每消费1元获得1积分",
        points: 1
      },
      {
        id: 2,
        type: "checkin",
        title: "每日签到",
        description: "每日签到获得积分奖励",
        points: 10
      },
      {
        id: 3,
        type: "share",
        title: "分享商品",
        description: "分享商品到社交媒体获得积分",
        points: 5
      }
    ]);
    const editRules = () => {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/points/rules"
      });
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const createPointsItem = () => {
      common_vendor.index.showToast({
        title: "创建新的积分商品",
        icon: "none",
        duration: 2e3
      });
      setTimeout(() => {
        common_vendor.index.navigateTo({
          url: "/subPackages/merchant-admin-marketing/pages/marketing/points/create"
        });
      }, 500);
    };
    common_vendor.onMounted(() => {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-marketing/pages/marketing/points/management.vue:366", "积分商城管理页面已加载");
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          d: "M15 18L9 12L15 6",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        b: common_vendor.p({
          width: "24",
          height: "24",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        c: common_vendor.o(goBack),
        d: common_vendor.o(createPointsItem),
        e: common_vendor.p({
          text: "创建积分商品",
          theme: "points"
        }),
        f: common_vendor.t(pointsData.totalPoints),
        g: common_vendor.t(pointsData.usedPoints),
        h: common_vendor.t(pointsData.activeUsers),
        i: common_vendor.t(pointsData.conversionRate),
        j: activeFilter.value === "all" ? 1 : "",
        k: common_vendor.o(($event) => setFilter("all")),
        l: activeFilter.value === "active" ? 1 : "",
        m: common_vendor.o(($event) => setFilter("active")),
        n: activeFilter.value === "draft" ? 1 : "",
        o: common_vendor.o(($event) => setFilter("draft")),
        p: activeFilter.value === "ended" ? 1 : "",
        q: common_vendor.o(($event) => setFilter("ended")),
        r: common_vendor.f(filteredItems.value, (item, index, i0) => {
          return {
            a: item.image,
            b: common_vendor.t(item.statusText),
            c: common_vendor.n(item.statusClass),
            d: common_vendor.t(item.name),
            e: common_vendor.t(item.points),
            f: common_vendor.t(item.stock),
            g: common_vendor.t(item.redeemed),
            h: common_vendor.t(item.views),
            i: common_vendor.t(item.timeRange),
            j: "6796c6eb-4-" + i0 + "," + ("6796c6eb-3-" + i0),
            k: "6796c6eb-5-" + i0 + "," + ("6796c6eb-3-" + i0),
            l: "6796c6eb-3-" + i0,
            m: common_vendor.o(($event) => editItem(item), index),
            n: "6796c6eb-7-" + i0 + "," + ("6796c6eb-6-" + i0),
            o: "6796c6eb-8-" + i0 + "," + ("6796c6eb-6-" + i0),
            p: "6796c6eb-6-" + i0,
            q: common_vendor.o(($event) => deleteItem(item), index),
            r: index,
            s: common_vendor.o(($event) => viewItemDetail(item), index)
          };
        }),
        s: common_vendor.p({
          d: "M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13",
          stroke: "#5E5CE6",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        t: common_vendor.p({
          d: "M18.5 2.50001C18.8978 2.10219 19.4374 1.87869 20 1.87869C20.5626 1.87869 21.1022 2.10219 21.5 2.50001C21.8978 2.89784 22.1213 3.4374 22.1213 4.00001C22.1213 4.56262 21.8978 5.10219 21.5 5.50001L12 15L8 16L9 12L18.5 2.50001Z",
          stroke: "#5E5CE6",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        v: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        w: common_vendor.p({
          d: "M3 6H5H21",
          stroke: "#FF3B30",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        x: common_vendor.p({
          d: "M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z",
          stroke: "#FF3B30",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        y: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        z: filteredItems.value.length === 0
      }, filteredItems.value.length === 0 ? {
        A: common_assets._imports_0$34,
        B: common_vendor.t(filterLabels[activeFilter.value])
      } : {}, {
        C: common_vendor.p({
          d: "M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13",
          stroke: "#5E5CE6",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        D: common_vendor.p({
          d: "M18.5 2.50001C18.8978 2.10219 19.4374 1.87869 20 1.87869C20.5626 1.87869 21.1022 2.10219 21.5 2.50001C21.8978 2.89784 22.1213 3.4374 22.1213 4.00001C22.1213 4.56262 21.8978 5.10219 21.5 5.50001L12 15L8 16L9 12L18.5 2.50001Z",
          stroke: "#5E5CE6",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        E: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        F: common_vendor.o(editRules),
        G: common_vendor.f(pointsRules.value, (rule, index, i0) => {
          return common_vendor.e({
            a: rule.type === "purchase"
          }, rule.type === "purchase" ? {
            b: "6796c6eb-13-" + i0 + "," + ("6796c6eb-12-" + i0),
            c: common_vendor.p({
              d: "M6 2L3 6V20C3 20.5304 3.21071 21.0391 3.58579 21.4142C3.96086 21.7893 4.46957 22 5 22H19C19.5304 22 20.0391 21.7893 20.4142 21.4142C20.7893 21.0391 21 20.5304 21 20V6L18 2H6Z",
              stroke: "currentColor",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            d: "6796c6eb-14-" + i0 + "," + ("6796c6eb-12-" + i0),
            e: common_vendor.p({
              d: "M3 6H21",
              stroke: "currentColor",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            f: "6796c6eb-15-" + i0 + "," + ("6796c6eb-12-" + i0),
            g: common_vendor.p({
              d: "M16 10C16 11.0609 15.5786 12.0783 14.8284 12.8284C14.0783 13.5786 13.0609 14 12 14C10.9391 14 9.92172 13.5786 9.17157 12.8284C8.42143 12.0783 8 11.0609 8 10",
              stroke: "currentColor",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            h: "6796c6eb-12-" + i0,
            i: common_vendor.p({
              width: "20",
              height: "20",
              viewBox: "0 0 24 24",
              fill: "none",
              xmlns: "http://www.w3.org/2000/svg"
            })
          } : {}, {
            j: rule.type === "checkin"
          }, rule.type === "checkin" ? {
            k: "6796c6eb-17-" + i0 + "," + ("6796c6eb-16-" + i0),
            l: common_vendor.p({
              d: "M9 11L12 14L22 4",
              stroke: "currentColor",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            m: "6796c6eb-18-" + i0 + "," + ("6796c6eb-16-" + i0),
            n: common_vendor.p({
              d: "M21 12V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H16",
              stroke: "currentColor",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            o: "6796c6eb-16-" + i0,
            p: common_vendor.p({
              width: "20",
              height: "20",
              viewBox: "0 0 24 24",
              fill: "none",
              xmlns: "http://www.w3.org/2000/svg"
            })
          } : {}, {
            q: rule.type === "share"
          }, rule.type === "share" ? {
            r: "6796c6eb-20-" + i0 + "," + ("6796c6eb-19-" + i0),
            s: common_vendor.p({
              d: "M18 8C19.6569 8 21 6.65685 21 5C21 3.34315 19.6569 2 18 2C16.3431 2 15 3.34315 15 5C15 6.65685 16.3431 8 18 8Z",
              stroke: "currentColor",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            t: "6796c6eb-21-" + i0 + "," + ("6796c6eb-19-" + i0),
            v: common_vendor.p({
              d: "M6 15C7.65685 15 9 13.6569 9 12C9 10.3431 7.65685 9 6 9C4.34315 9 3 10.3431 3 12C3 13.6569 4.34315 15 6 15Z",
              stroke: "currentColor",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            w: "6796c6eb-22-" + i0 + "," + ("6796c6eb-19-" + i0),
            x: common_vendor.p({
              d: "M18 22C19.6569 22 21 20.6569 21 19C21 17.3431 19.6569 16 18 16C16.3431 16 15 17.3431 15 19C15 20.6569 16.3431 22 18 22Z",
              stroke: "currentColor",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            y: "6796c6eb-23-" + i0 + "," + ("6796c6eb-19-" + i0),
            z: common_vendor.p({
              d: "M8.59 13.51L15.42 17.49",
              stroke: "currentColor",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            A: "6796c6eb-24-" + i0 + "," + ("6796c6eb-19-" + i0),
            B: common_vendor.p({
              d: "M15.41 6.51L8.59 10.49",
              stroke: "currentColor",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            C: "6796c6eb-19-" + i0,
            D: common_vendor.p({
              width: "20",
              height: "20",
              viewBox: "0 0 24 24",
              fill: "none",
              xmlns: "http://www.w3.org/2000/svg"
            })
          } : {}, {
            E: common_vendor.n(rule.type),
            F: common_vendor.t(rule.title),
            G: common_vendor.t(rule.description),
            H: common_vendor.t(rule.points),
            I: index
          });
        })
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/points/management.js.map
