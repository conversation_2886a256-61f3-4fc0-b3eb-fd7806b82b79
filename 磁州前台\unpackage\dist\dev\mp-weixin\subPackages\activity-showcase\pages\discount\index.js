"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      refreshing: false,
      loading: false,
      noMore: false,
      currentTabIndex: 0,
      tabs: [
        { name: "全部" },
        { name: "餐饮美食" },
        { name: "休闲娱乐" },
        { name: "生活服务" }
      ],
      discountItems: [
        {
          id: 1,
          merchantName: "星巴克咖啡",
          logo: "https://via.placeholder.com/100x100",
          tag: "热门",
          rules: [
            { text: "满50减10", highlight: false },
            { text: "满100减30", highlight: true },
            { text: "满200减60", highlight: false }
          ],
          startTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1e3).toISOString(),
          endTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1e3).toISOString()
        },
        {
          id: 2,
          merchantName: "肯德基",
          logo: "https://via.placeholder.com/100x100",
          tag: "限时",
          rules: [
            { text: "满59减15", highlight: false },
            { text: "满99减30", highlight: true },
            { text: "满199减60", highlight: false }
          ],
          startTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1e3).toISOString(),
          endTime: new Date(Date.now() + 15 * 24 * 60 * 60 * 1e3).toISOString()
        },
        {
          id: 3,
          merchantName: "海底捞火锅",
          logo: "https://via.placeholder.com/100x100",
          tag: "爆款",
          rules: [
            { text: "满200减30", highlight: false },
            { text: "满400减80", highlight: true },
            { text: "满600减150", highlight: false }
          ],
          startTime: new Date(Date.now() - 10 * 24 * 60 * 60 * 1e3).toISOString(),
          endTime: new Date(Date.now() + 20 * 24 * 60 * 60 * 1e3).toISOString()
        },
        {
          id: 4,
          merchantName: "喜茶",
          logo: "https://via.placeholder.com/100x100",
          tag: "新店",
          rules: [
            { text: "满40减8", highlight: false },
            { text: "满80减20", highlight: true },
            { text: "满120减35", highlight: false }
          ],
          startTime: new Date(Date.now() - 5 * 24 * 60 * 60 * 1e3).toISOString(),
          endTime: new Date(Date.now() + 25 * 24 * 60 * 60 * 1e3).toISOString()
        },
        {
          id: 5,
          merchantName: "优衣库",
          logo: "https://via.placeholder.com/100x100",
          tag: "折扣",
          rules: [
            { text: "满300减50", highlight: false },
            { text: "满500减100", highlight: true },
            { text: "满800减200", highlight: false }
          ],
          startTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1e3).toISOString(),
          endTime: new Date(Date.now() + 10 * 24 * 60 * 60 * 1e3).toISOString()
        }
      ]
    };
  },
  onLoad() {
    this.fetchData();
  },
  methods: {
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 切换选项卡
    switchTab(index) {
      this.currentTabIndex = index;
      this.fetchData();
    },
    // 下拉刷新
    onRefresh() {
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".content-scroll").boundingClientRect((data) => {
        if (data && data.top <= 5) {
          this.refreshing = true;
          setTimeout(() => {
            this.fetchData();
            this.refreshing = false;
            common_vendor.index.showToast({
              title: "刷新成功",
              icon: "none"
            });
          }, 1500);
        } else {
          this.refreshing = false;
        }
      }).exec();
    },
    // 加载更多
    loadMore() {
      if (this.loading || this.noMore)
        return;
      this.loading = true;
      setTimeout(() => {
        const moreItems = [
          {
            id: 6,
            merchantName: "必胜客",
            logo: "https://via.placeholder.com/100x100",
            tag: "人气",
            rules: [
              { text: "满100减20", highlight: false },
              { text: "满200减50", highlight: true },
              { text: "满300减80", highlight: false }
            ],
            startTime: new Date(Date.now() - 4 * 24 * 60 * 60 * 1e3).toISOString(),
            endTime: new Date(Date.now() + 18 * 24 * 60 * 60 * 1e3).toISOString()
          },
          {
            id: 7,
            merchantName: "屈臣氏",
            logo: "https://via.placeholder.com/100x100",
            tag: "特惠",
            rules: [
              { text: "满99减20", highlight: false },
              { text: "满199减50", highlight: true },
              { text: "满299减100", highlight: false }
            ],
            startTime: new Date(Date.now() - 6 * 24 * 60 * 60 * 1e3).toISOString(),
            endTime: new Date(Date.now() + 14 * 24 * 60 * 60 * 1e3).toISOString()
          }
        ];
        this.discountItems = [...this.discountItems, ...moreItems];
        this.noMore = true;
        this.loading = false;
      }, 1500);
    },
    // 获取数据
    fetchData() {
    },
    // 导航到详情页
    navigateToDetail(id) {
      common_vendor.index.navigateTo({
        url: `/subPackages/activity-showcase/pages/discount/detail?id=${id}`
      });
    },
    // 获取活动时间文本
    getTimeText(startTime, endTime) {
      const start = new Date(startTime);
      const end = new Date(endTime);
      const startMonth = start.getMonth() + 1;
      const startDay = start.getDate();
      const endMonth = end.getMonth() + 1;
      const endDay = end.getDate();
      return `${startMonth}.${startDay}-${endMonth}.${endDay}`;
    }
  }
};
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$7,
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: common_vendor.p({
      d: "M512 421.490332L331.349941 240.840273c-24.988383-24.988383-65.35828-24.988383-90.346664 0-24.988383 24.988383-24.988383 65.35828 0 90.346664L421.653336 512 240.840273 692.812059c-24.988383 24.988383-24.988383 65.35828 0 90.346664 24.988383 24.988383 65.35828 24.988383 90.346664 0L512 602.509668l180.650059 180.650059c24.988383 24.988383 65.35828 24.988383 90.346664 0 24.988383-24.988383 24.988383-65.35828 0-90.346664L602.346664 512l180.813063-180.812059c24.988383-24.988383 24.988383-65.35828 0-90.346664-24.988383-24.988383-65.35828-24.988383-90.346664 0L512 421.490332z",
      fill: "#FFFFFF"
    }),
    d: common_vendor.p({
      viewBox: "0 0 1024 1024",
      xmlns: "http://www.w3.org/2000/svg",
      width: "22",
      height: "22"
    }),
    e: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    f: common_vendor.f($data.tabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab.name),
        b: index,
        c: $data.currentTabIndex === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    }),
    g: common_vendor.f($data.discountItems, (item, index, i0) => {
      return common_vendor.e({
        a: item.logo,
        b: common_vendor.t(item.merchantName),
        c: item.tag
      }, item.tag ? {
        d: common_vendor.t(item.tag)
      } : {}, {
        e: common_vendor.f(item.rules, (rule, ruleIndex, i1) => {
          return {
            a: common_vendor.t(rule.text),
            b: ruleIndex,
            c: rule.highlight ? 1 : ""
          };
        }),
        f: common_vendor.t($options.getTimeText(item.startTime, item.endTime)),
        g: index,
        h: common_vendor.o(($event) => $options.navigateToDetail(item.id), index)
      });
    }),
    h: $data.loading
  }, $data.loading ? {} : {}, {
    i: $data.noMore
  }, $data.noMore ? {} : {}, {
    j: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-bb3ebeba"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/discount/index.js.map
