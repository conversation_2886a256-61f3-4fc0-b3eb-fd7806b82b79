{"name": "weex-template-compiler", "version": "2.6.10-weex.1", "description": "Weex template compiler for Vue 2.0", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue.git"}, "keywords": ["vue", "compiler"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue/issues"}, "homepage": "https://github.com/vuejs/vue/tree/dev/packages/weex-template-compiler#readme", "dependencies": {"acorn": "^5.2.1", "escodegen": "^1.8.1", "he": "^1.1.0"}}