@echo off

echo.
echo ========================================
echo   磁州生活网基础启动脚本
echo ========================================
echo.

echo 第一步：启动 Docker 服务
echo.

cd backend

echo 启动 MySQL...
docker run -d --name cizhou-mysql -p 3306:3306 -e MYSQL_ROOT_PASSWORD=cizhou123456 -e MYSQL_DATABASE=cizhou_admin mysql:8.0

echo 启动 Redis...
docker run -d --name cizhou-redis -p 6379:6379 redis:7.0-alpine redis-server --requirepass cizhou123456

echo 启动 Nacos...
docker run -d --name cizhou-nacos -p 8848:8848 -e MODE=standalone nacos/nacos-server:v2.3.0

echo.
echo 等待服务启动...
timeout /t 30

echo.
echo 初始化数据库...
docker exec -i cizhou-mysql mysql -uroot -pcizhou123456 -e "CREATE DATABASE IF NOT EXISTS cizhou_admin;"

echo.
echo ========================================
echo 基础服务启动完成
echo ========================================
echo.

echo 服务地址：
echo   MySQL:  localhost:3306 (root/cizhou123456)
echo   Redis:  localhost:6379 (cizhou123456)
echo   Nacos:  http://localhost:8848/nacos (nacos/nacos)
echo.

echo 下一步：
echo   1. 在 IDE 中启动 cizhou-auth 服务
echo   2. 在 IDE 中启动 cizhou-gateway 服务
echo   3. 运行 frontend\start-dev.bat 启动前端
echo.

pause
