"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      priceInfo: {
        marketPrice: "",
        regularPrice: "",
        groupPrice: "",
        limitPerUser: 1,
        stock: 100
      }
    };
  },
  onLoad() {
    try {
      const savedPriceInfo = common_vendor.index.getStorageSync("packagePriceInfo");
      if (savedPriceInfo) {
        this.priceInfo = JSON.parse(savedPriceInfo);
      }
    } catch (e) {
      common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/group/create-package-price.vue:140", "读取本地存储失败:", e);
    }
  },
  methods: {
    goBack() {
      this.saveData();
      common_vendor.index.navigateBack();
    },
    nextStep() {
      if (!this.priceInfo.marketPrice) {
        common_vendor.index.showToast({
          title: "请输入市场价",
          icon: "none"
        });
        return;
      }
      if (!this.priceInfo.regularPrice) {
        common_vendor.index.showToast({
          title: "请输入日常价",
          icon: "none"
        });
        return;
      }
      if (!this.priceInfo.groupPrice) {
        common_vendor.index.showToast({
          title: "请输入拼团价",
          icon: "none"
        });
        return;
      }
      const marketPrice = parseFloat(this.priceInfo.marketPrice);
      const regularPrice = parseFloat(this.priceInfo.regularPrice);
      const groupPrice = parseFloat(this.priceInfo.groupPrice);
      if (marketPrice <= regularPrice) {
        common_vendor.index.showToast({
          title: "市场价应高于日常价",
          icon: "none"
        });
        return;
      }
      if (regularPrice <= groupPrice) {
        common_vendor.index.showToast({
          title: "日常价应高于拼团价",
          icon: "none"
        });
        return;
      }
      this.saveData();
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-items"
      });
    },
    saveData() {
      try {
        common_vendor.index.setStorageSync("packagePriceInfo", JSON.stringify(this.priceInfo));
      } catch (e) {
        common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/group/create-package-price.vue:207", "保存数据失败:", e);
      }
    },
    formatPrice(price) {
      if (!price)
        return "0.00";
      return parseFloat(price).toFixed(2);
    },
    calculateSavings() {
      const marketPrice = parseFloat(this.priceInfo.marketPrice) || 0;
      const groupPrice = parseFloat(this.priceInfo.groupPrice) || 0;
      const savings = marketPrice - groupPrice;
      return savings > 0 ? savings.toFixed(2) : "0.00";
    },
    calculateDiscount() {
      const marketPrice = parseFloat(this.priceInfo.marketPrice) || 1;
      const groupPrice = parseFloat(this.priceInfo.groupPrice) || 0;
      const discount = groupPrice / marketPrice * 10;
      return discount.toFixed(1);
    },
    decrementLimitPerUser() {
      if (this.priceInfo.limitPerUser > 0) {
        this.priceInfo.limitPerUser--;
      }
    },
    incrementLimitPerUser() {
      if (this.priceInfo.limitPerUser < 99) {
        this.priceInfo.limitPerUser++;
      }
    },
    decrementStock() {
      if (this.priceInfo.stock > 1) {
        this.priceInfo.stock--;
      }
    },
    incrementStock() {
      if (this.priceInfo.stock < 9999) {
        this.priceInfo.stock++;
      }
    },
    showHelp() {
      common_vendor.index.showToast({
        title: "帮助信息",
        icon: "none"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: $data.priceInfo.marketPrice,
    d: common_vendor.o(($event) => $data.priceInfo.marketPrice = $event.detail.value),
    e: $data.priceInfo.regularPrice,
    f: common_vendor.o(($event) => $data.priceInfo.regularPrice = $event.detail.value),
    g: $data.priceInfo.groupPrice,
    h: common_vendor.o(($event) => $data.priceInfo.groupPrice = $event.detail.value),
    i: common_vendor.o((...args) => $options.decrementLimitPerUser && $options.decrementLimitPerUser(...args)),
    j: $data.priceInfo.limitPerUser,
    k: common_vendor.o(($event) => $data.priceInfo.limitPerUser = $event.detail.value),
    l: common_vendor.o((...args) => $options.incrementLimitPerUser && $options.incrementLimitPerUser(...args)),
    m: common_vendor.o((...args) => $options.decrementStock && $options.decrementStock(...args)),
    n: $data.priceInfo.stock,
    o: common_vendor.o(($event) => $data.priceInfo.stock = $event.detail.value),
    p: common_vendor.o((...args) => $options.incrementStock && $options.incrementStock(...args)),
    q: common_vendor.t($options.formatPrice($data.priceInfo.marketPrice)),
    r: common_vendor.t($options.formatPrice($data.priceInfo.regularPrice)),
    s: common_vendor.t($options.formatPrice($data.priceInfo.groupPrice)),
    t: common_vendor.t($options.calculateSavings()),
    v: common_vendor.t($options.calculateDiscount()),
    w: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    x: common_vendor.o((...args) => $options.nextStep && $options.nextStep(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-price.js.map
