<template>
  <view class="after-sale-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">售后服务中心</text>
      <view class="navbar-right">
        <view class="help-icon">?</view>
      </view>
    </view>
    
    <!-- 售后服务Tab -->
    <view class="tab-container">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index"
        class="tab-item"
        :class="{'active': currentTab === tab.value}"
        @click="switchTab(tab.value)">
        <text class="tab-name">{{tab.name}}</text>
        <text v-if="tab.count > 0" class="tab-badge">{{tab.count}}</text>
      </view>
    </view>
    
    <!-- 退款申请列表 -->
    <view v-if="currentTab === 'refund'" class="content-main">
      <view 
        v-for="(item, index) in refundList" 
        :key="index"
        class="refund-item"
        @click="viewRefundDetail(item.id)">
        <view class="refund-header">
          <text class="refund-number">退款编号: {{item.refundNo}}</text>
          <text class="refund-status" :class="'status-' + item.status">{{getRefundStatusText(item.status)}}</text>
        </view>
        <view class="order-info">
          <text class="order-number">订单号: {{item.orderNo}}</text>
          <text class="refund-time">申请时间: {{item.createTime}}</text>
        </view>
        <view class="refund-info">
          <view class="customer-info">
            <image class="customer-avatar" :src="item.customerAvatar" mode="aspectFill"></image>
            <text class="customer-name">{{item.customerName}}</text>
          </view>
          <view class="refund-amount">
            <text class="label">退款金额:</text>
            <text class="amount">¥{{item.amount}}</text>
          </view>
        </view>
        <view class="refund-reason">
          <text class="label">退款原因:</text>
          <text class="reason">{{item.reason}}</text>
        </view>
        <view class="action-buttons">
          <view v-if="item.status === 'pending'" class="action-btn primary" @click.stop="approveRefund(item.id)">同意退款</view>
          <view v-if="item.status === 'pending'" class="action-btn reject" @click.stop="rejectRefund(item.id)">拒绝退款</view>
          <view v-if="['approved', 'rejected'].includes(item.status)" class="action-btn" @click.stop="contactCustomer(item.customerId)">联系客户</view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view v-if="refundList.length === 0" class="empty-state">
        <view class="empty-icon">📭</view>
        <text class="empty-text">暂无退款申请</text>
      </view>
    </view>
    
    <!-- 投诉处理列表 -->
    <view v-if="currentTab === 'complaint'" class="content-main">
      <view 
        v-for="(item, index) in complaintList" 
        :key="index"
        class="complaint-item"
        @click="viewComplaintDetail(item.id)">
        <view class="complaint-header">
          <text class="complaint-title">{{item.title}}</text>
          <text class="complaint-status" :class="'status-' + item.status">{{getComplaintStatusText(item.status)}}</text>
        </view>
        <view class="order-info">
          <text class="order-number">订单号: {{item.orderNo}}</text>
          <text class="complaint-time">投诉时间: {{item.createTime}}</text>
        </view>
        <view class="complaint-content">
          <text class="complaint-text">{{item.content}}</text>
        </view>
        <view class="action-buttons">
          <view v-if="item.status === 'pending'" class="action-btn primary" @click.stop="handleComplaint(item.id)">处理投诉</view>
          <view class="action-btn" @click.stop="contactCustomer(item.customerId)">联系客户</view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view v-if="complaintList.length === 0" class="empty-state">
        <view class="empty-icon">📭</view>
        <text class="empty-text">暂无客户投诉</text>
      </view>
    </view>
    
    <!-- 评价管理列表 -->
    <view v-if="currentTab === 'review'" class="content-main">
      <view 
        v-for="(item, index) in reviewList" 
        :key="index"
        class="review-item">
        <view class="review-header">
          <view class="customer-info">
            <image class="customer-avatar" :src="item.customerAvatar" mode="aspectFill"></image>
            <text class="customer-name">{{item.customerName}}</text>
          </view>
          <view class="rating">
            <text v-for="i in 5" :key="i" class="star" :class="{'filled': i <= item.rating}">★</text>
            <text class="rating-text">{{item.rating}}.0</text>
          </view>
        </view>
        <view class="review-content">
          <text class="review-text">{{item.content}}</text>
          <view v-if="item.images && item.images.length > 0" class="review-images">
            <image 
              v-for="(img, imgIndex) in item.images" 
              :key="imgIndex"
              :src="img"
              mode="aspectFill"
              class="review-image"
              @click="previewImage(item.images, imgIndex)"></image>
          </view>
        </view>
        <view class="review-footer">
          <text class="review-time">{{item.createTime}}</text>
          <text class="order-number">订单号: {{item.orderNo}}</text>
        </view>
        <view class="reply-section">
          <view v-if="item.reply" class="merchant-reply">
            <text class="reply-label">商家回复:</text>
            <text class="reply-content">{{item.reply}}</text>
            <text class="reply-time">{{item.replyTime}}</text>
          </view>
          <view v-else class="reply-form">
            <input 
              class="reply-input" 
              placeholder="回复该评价..." 
              v-model="replyContent[item.id]"
              @confirm="submitReply(item.id)" />
            <view class="reply-btn" @click="submitReply(item.id)">回复</view>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view v-if="reviewList.length === 0" class="empty-state">
        <view class="empty-icon">📭</view>
        <text class="empty-text">暂无客户评价</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentTab: 'refund', // refund, complaint, review
      tabs: [
        { name: '退款管理', value: 'refund', count: 2 },
        { name: '投诉处理', value: 'complaint', count: 1 },
        { name: '评价管理', value: 'review', count: 0 }
      ],
      refundList: [
        {
          id: '4001',
          refundNo: 'RF20230510001',
          orderNo: 'CZ20230501001',
          status: 'pending', // pending, approved, rejected, completed
          createTime: '2023-05-10 14:30',
          customerName: '张三',
          customerId: '10086',
          customerAvatar: '/static/images/default-avatar.png',
          amount: '128.00',
          reason: '商品质量不符合预期，水果有部分已经变质。'
        },
        {
          id: '4002',
          refundNo: 'RF20230511002',
          orderNo: 'CZ20230502003',
          status: 'approved',
          createTime: '2023-05-11 09:15',
          customerName: '李四',
          customerId: '10087',
          customerAvatar: '/static/images/default-avatar.png',
          amount: '89.90',
          reason: '配送时间过长，蔬菜已经不新鲜。'
        }
      ],
      complaintList: [
        {
          id: '5001',
          title: '配送员服务态度差',
          orderNo: 'CZ20230503005',
          status: 'pending', // pending, processing, resolved
          createTime: '2023-05-12 16:45',
          customerId: '10088',
          content: '配送员态度非常恶劣，送货上门后直接扔在门口就走了，敲门也不回应。'
        }
      ],
      reviewList: [
        {
          id: '6001',
          customerName: '王五',
          customerAvatar: '/static/images/default-avatar.png',
          rating: 4,
          content: '水果非常新鲜，包装也很精美，就是价格稍微贵了点。下次还会购买。',
          images: [
            '/static/images/review-1.jpg',
            '/static/images/review-2.jpg'
          ],
          createTime: '2023-05-13 10:20',
          orderNo: 'CZ20230505008',
          reply: null,
          replyTime: null
        },
        {
          id: '6002',
          customerName: '赵六',
          customerAvatar: '/static/images/default-avatar.png',
          rating: 5,
          content: '蔬菜很新鲜，配送速度也很快，非常满意！',
          images: [],
          createTime: '2023-05-14 15:30',
          orderNo: 'CZ20230506010',
          reply: '感谢您的支持，我们会继续努力提供优质服务！',
          replyTime: '2023-05-14 16:05'
        },
        {
          id: '6003',
          customerName: '钱七',
          customerAvatar: '/static/images/default-avatar.png',
          rating: 2,
          content: '送来的水果有几个已经坏了，很失望。',
          images: [
            '/static/images/review-3.jpg'
          ],
          createTime: '2023-05-15 09:40',
          orderNo: 'CZ20230507012',
          reply: null,
          replyTime: null
        }
      ],
      replyContent: {}
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    switchTab(tab) {
      this.currentTab = tab;
    },
    viewRefundDetail(id) {
      uni.showToast({
        title: '查看退款详情功能开发中',
        icon: 'none'
      });
    },
    approveRefund(id) {
      uni.showModal({
        title: '确认退款',
        content: '确认同意该退款申请？',
        success: (res) => {
          if (res.confirm) {
            // 更新退款状态
            const index = this.refundList.findIndex(item => item.id === id);
            if (index !== -1) {
              this.refundList[index].status = 'approved';
              uni.showToast({
                title: '已同意退款',
                icon: 'success'
              });
            }
          }
        }
      });
    },
    rejectRefund(id) {
      uni.showModal({
        title: '拒绝退款',
        content: '确认拒绝该退款申请？请确保已与客户沟通。',
        success: (res) => {
          if (res.confirm) {
            // 更新退款状态
            const index = this.refundList.findIndex(item => item.id === id);
            if (index !== -1) {
              this.refundList[index].status = 'rejected';
              uni.showToast({
                title: '已拒绝退款',
                icon: 'success'
              });
            }
          }
        }
      });
    },
    viewComplaintDetail(id) {
      uni.showToast({
        title: '查看投诉详情功能开发中',
        icon: 'none'
      });
    },
    handleComplaint(id) {
      uni.showModal({
        title: '处理投诉',
        content: '确认开始处理该投诉？',
        success: (res) => {
          if (res.confirm) {
            // 更新投诉状态
            const index = this.complaintList.findIndex(item => item.id === id);
            if (index !== -1) {
              this.complaintList[index].status = 'processing';
              uni.showToast({
                title: '已开始处理投诉',
                icon: 'success'
              });
            }
          }
        }
      });
    },
    contactCustomer(customerId) {
      uni.showActionSheet({
        itemList: ['拨打电话', '发送消息'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 拨打电话
            uni.showToast({
              title: '拨打电话功能开发中',
              icon: 'none'
            });
          } else if (res.tapIndex === 1) {
            // 发送消息
            uni.showToast({
              title: '消息功能开发中',
              icon: 'none'
            });
          }
        }
      });
    },
    previewImage(images, current) {
      uni.previewImage({
        urls: images,
        current: images[current]
      });
    },
    submitReply(reviewId) {
      if (!this.replyContent[reviewId]) {
        uni.showToast({
          title: '请输入回复内容',
          icon: 'none'
        });
        return;
      }
      
      // 更新评价回复
      const index = this.reviewList.findIndex(item => item.id === reviewId);
      if (index !== -1) {
        this.reviewList[index].reply = this.replyContent[reviewId];
        this.reviewList[index].replyTime = this.getCurrentTime();
        this.replyContent[reviewId] = '';
        
        uni.showToast({
          title: '回复成功',
          icon: 'success'
        });
      }
    },
    getCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hour = String(now.getHours()).padStart(2, '0');
      const minute = String(now.getMinutes()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hour}:${minute}`;
    },
    getRefundStatusText(status) {
      const texts = {
        pending: '待处理',
        approved: '已同意',
        rejected: '已拒绝',
        completed: '已完成'
      };
      return texts[status] || '未知状态';
    },
    getComplaintStatusText(status) {
      const texts = {
        pending: '待处理',
        processing: '处理中',
        resolved: '已解决'
      };
      return texts[status] || '未知状态';
    }
  }
}
</script>

<style>
.after-sale-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}

.tab-container {
  display: flex;
  background-color: #fff;
  padding: 0 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.tab-item {
  padding: 16px 0;
  margin-right: 24px;
  position: relative;
  display: flex;
  align-items: center;
}

.tab-item.active {
  color: #1677FF;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background-color: #1677FF;
  border-radius: 3px 3px 0 0;
}

.tab-badge {
  min-width: 16px;
  height: 16px;
  border-radius: 8px;
  background-color: #ff4d4f;
  color: #fff;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 4px;
  padding: 0 4px;
}

.content-main {
  padding: 16px;
}

.refund-item, .complaint-item, .review-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.refund-header, .complaint-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.refund-number, .complaint-title {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.refund-status, .complaint-status {
  font-size: 14px;
  font-weight: 500;
}

.status-pending {
  color: #FF9800;
}

.status-approved, .status-processing {
  color: #2196F3;
}

.status-completed, .status-resolved {
  color: #4CAF50;
}

.status-rejected {
  color: #F44336;
}

.order-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 14px;
  color: #666;
}

.refund-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.customer-info {
  display: flex;
  align-items: center;
}

.customer-avatar {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  margin-right: 8px;
}

.customer-name {
  font-size: 14px;
  color: #333;
}

.refund-amount {
  display: flex;
  align-items: center;
}

.label {
  font-size: 14px;
  color: #666;
  margin-right: 4px;
}

.amount {
  font-size: 16px;
  color: #ff6a00;
  font-weight: 600;
}

.refund-reason {
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.reason {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  margin-left: 12px;
  background-color: #f0f0f0;
  color: #333;
}

.action-btn.primary {
  background-color: #1677FF;
  color: #fff;
}

.action-btn.reject {
  background-color: #ff4d4f;
  color: #fff;
}

.complaint-content {
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.complaint-text {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.rating {
  display: flex;
  align-items: center;
}

.star {
  color: #ddd;
  font-size: 16px;
}

.star.filled {
  color: #FFAB00;
}

.rating-text {
  font-size: 14px;
  color: #666;
  margin-left: 4px;
}

.review-content {
  margin-bottom: 12px;
}

.review-text {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8px;
}

.review-images {
  display: flex;
  flex-wrap: wrap;
}

.review-image {
  width: 80px;
  height: 80px;
  margin-right: 8px;
  margin-bottom: 8px;
  border-radius: 4px;
}

.review-footer {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
  margin-bottom: 12px;
}

.reply-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

.merchant-reply {
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 4px;
}

.reply-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  margin-bottom: 4px;
  display: block;
}

.reply-content {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}

.reply-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  display: block;
  text-align: right;
}

.reply-form {
  display: flex;
}

.reply-input {
  flex: 1;
  height: 36px;
  border: 1px solid #ddd;
  border-radius: 18px;
  padding: 0 12px;
  font-size: 14px;
}

.reply-btn {
  width: 60px;
  height: 36px;
  background-color: #1677FF;
  color: #fff;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  font-size: 14px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 0;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  color: #999;
}
</style> 