<template>
  <view class="share-container">
    <view class="share-header">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/最新返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">分享</view>
    </view>
    
    <view class="share-content">
      <view class="share-card">
        <view class="share-title">{{shareData.title || '分享内容'}}</view>
        <view class="share-desc">{{shareData.desc || ''}}</view>
        <image class="share-image" :src="shareData.imageUrl || '/static/images/share-cover.png'" mode="aspectFill"></image>
      </view>
      
      <view class="share-options">
        <view class="share-option" @click="shareToFriend">
          <image src="/static/images/tabbar/a分享.png" class="option-icon"></image>
          <text class="option-text">微信好友</text>
        </view>
        <view class="share-option" @click="shareToTimeline">
          <image src="/static/images/tabbar/转发.png" class="option-icon"></image>
          <text class="option-text">朋友圈</text>
        </view>
        <view class="share-option" @click="copyLink">
          <image src="/static/images/tabbar/阿分享.png" class="option-icon"></image>
          <text class="option-text">复制链接</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      shareData: {
        title: '',
        path: '',
        imageUrl: '',
        desc: ''
      }
    }
  },
  onLoad() {
    // 获取页面参数
    const eventChannel = this.getOpenerEventChannel();
    if (eventChannel) {
      eventChannel.on('shareData', (data) => {
        this.shareData = data;
      });
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 分享给微信好友
    shareToFriend() {
      // 使用小程序原生分享API
      uni.share({
        provider: 'weixin',
        scene: 'WXSceneSession',
        type: 0,
        title: this.shareData.title,
        summary: this.shareData.desc,
        imageUrl: this.shareData.imageUrl,
        href: this.shareData.path,
        success: (res) => {
          uni.showToast({
            title: '分享成功',
            icon: 'success'
          });
          setTimeout(() => {
            this.goBack();
          }, 1500);
        },
        fail: (err) => {
          console.error('分享失败', err);
          // 如果分享失败，提供复制链接的选项
          this.fallbackShare();
        }
      });
    },
    
    // 分享到朋友圈
    shareToTimeline() {
      // 使用小程序原生分享API
      uni.share({
        provider: 'weixin',
        scene: 'WXSceneTimeline',
        type: 0,
        title: this.shareData.title,
        summary: this.shareData.desc,
        imageUrl: this.shareData.imageUrl,
        href: this.shareData.path,
        success: (res) => {
          uni.showToast({
            title: '分享成功',
            icon: 'success'
          });
          setTimeout(() => {
            this.goBack();
          }, 1500);
        },
        fail: (err) => {
          console.error('分享失败', err);
          // 如果分享失败，提供复制链接的选项
          this.fallbackShare();
        }
      });
    },
    
    // 复制链接
    copyLink() {
      const link = `https://your-domain.com${this.shareData.path}`;
      
      uni.setClipboardData({
        data: link,
        success: () => {
          uni.showToast({
            title: '链接已复制',
            icon: 'success'
          });
        }
      });
    },
    
    // 备用分享方案
    fallbackShare() {
      uni.showModal({
        title: '分享提示',
        content: '当前环境不支持直接分享，是否复制分享内容？',
        confirmText: '复制',
        success: (res) => {
          if (res.confirm) {
            const shareText = `${this.shareData.title} ${this.shareData.desc || ''}`;
            uni.setClipboardData({
              data: shareText,
              success: () => {
                uni.showToast({
                  title: '已复制分享内容',
                  icon: 'success'
                });
              }
            });
          }
        }
      });
    }
  }
}
</script>

<style lang="scss">
.share-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 90rpx;
}

.share-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 90rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.share-content {
  padding: 30rpx;
}

.share-card {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.share-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
}

.share-desc {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 20rpx;
}

.share-image {
  width: 100%;
  height: 300rpx;
  border-radius: 12rpx;
}

.share-options {
  display: flex;
  justify-content: space-around;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.option-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 16rpx;
}

.option-text {
  font-size: 28rpx;
  color: #333333;
}
</style> 