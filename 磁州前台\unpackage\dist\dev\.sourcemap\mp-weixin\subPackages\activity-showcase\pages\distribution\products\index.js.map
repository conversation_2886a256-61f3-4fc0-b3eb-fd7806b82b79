{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/distribution/products/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcZGlzdHJpYnV0aW9uXHByb2R1Y3RzXGluZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"products-container\">\r\n    <!-- 搜索栏 -->\r\n    <view class=\"search-bar\" :style=\"{\r\n      padding: '20rpx 30rpx',\r\n      background: '#FFFFFF',\r\n      position: 'sticky',\r\n      top: 0,\r\n      zIndex: 100,\r\n      display: 'flex',\r\n      alignItems: 'center',\r\n      boxShadow: '0 2px 10px rgba(0,0,0,0.05)'\r\n    }\">\r\n      <view class=\"search-input-wrapper\" :style=\"{\r\n        flex: 1,\r\n        height: '70rpx',\r\n        borderRadius: '35rpx',\r\n        background: '#F2F2F7',\r\n        display: 'flex',\r\n        alignItems: 'center',\r\n        paddingLeft: '20rpx'\r\n      }\">\r\n        <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\">\r\n          <circle cx=\"11\" cy=\"11\" r=\"8\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\r\n          <path d=\"M21 21l-4.35-4.35\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n        </svg>\r\n        <input \r\n          type=\"text\" \r\n          v-model=\"searchKeyword\" \r\n          placeholder=\"搜索商品\" \r\n          :style=\"{\r\n            flex: 1,\r\n            height: '100%',\r\n            border: 'none',\r\n            background: 'transparent',\r\n            marginLeft: '10rpx',\r\n            fontSize: '28rpx'\r\n          }\"\r\n          @confirm=\"searchProducts\"\r\n        />\r\n      </view>\r\n      \r\n      <view class=\"filter-btn\" @click=\"toggleFilterPanel\" :style=\"{\r\n        display: 'flex',\r\n        alignItems: 'center',\r\n        marginLeft: '20rpx',\r\n        padding: '0 10rpx'\r\n      }\">\r\n        <text :style=\"{\r\n          fontSize: '28rpx',\r\n          color: '#666666',\r\n          marginRight: '5rpx'\r\n        }\">筛选</text>\r\n        <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\">\r\n          <path d=\"M22 3H2l8 9.46V19l4 2v-8.54L22 3z\" stroke=\"#666666\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n        </svg>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 分类标签栏 -->\r\n    <scroll-view \r\n      scroll-x \r\n      class=\"category-tabs\" \r\n      :style=\"{\r\n        whiteSpace: 'nowrap',\r\n        padding: '20rpx 30rpx',\r\n        background: '#FFFFFF',\r\n        marginBottom: '20rpx',\r\n        boxShadow: '0 2px 10px rgba(0,0,0,0.05)'\r\n      }\"\r\n    >\r\n      <view \r\n        v-for=\"(category, index) in categories\" \r\n        :key=\"index\"\r\n        class=\"category-item\"\r\n        :class=\"{ active: currentCategory === index }\"\r\n        @click=\"switchCategory(index)\"\r\n        :style=\"{\r\n          display: 'inline-block',\r\n          padding: '10rpx 30rpx',\r\n          marginRight: '20rpx',\r\n          borderRadius: '30rpx',\r\n          fontSize: '28rpx',\r\n          color: currentCategory === index ? '#FFFFFF' : '#666666',\r\n          background: currentCategory === index ? 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)' : '#F2F2F7',\r\n          boxShadow: currentCategory === index ? '0 4px 10px rgba(172,57,255,0.2)' : 'none'\r\n        }\"\r\n      >\r\n        {{ category.name }}\r\n      </view>\r\n    </scroll-view>\r\n    \r\n    <!-- 排序栏 -->\r\n    <view class=\"sort-bar\" :style=\"{\r\n      display: 'flex',\r\n      padding: '20rpx 30rpx',\r\n      background: '#FFFFFF',\r\n      marginBottom: '20rpx',\r\n      boxShadow: '0 2px 10px rgba(0,0,0,0.05)'\r\n    }\">\r\n      <view \r\n        v-for=\"(option, index) in sortOptions\" \r\n        :key=\"index\"\r\n        class=\"sort-option\"\r\n        :class=\"{ active: currentSort === index }\"\r\n        @click=\"switchSort(index)\"\r\n        :style=\"{\r\n          flex: 1,\r\n          textAlign: 'center',\r\n          fontSize: '26rpx',\r\n          color: currentSort === index ? '#AC39FF' : '#666666',\r\n          fontWeight: currentSort === index ? '500' : '400',\r\n          position: 'relative',\r\n          paddingBottom: '10rpx'\r\n        }\"\r\n      >\r\n        {{ option.name }}\r\n        <view v-if=\"currentSort === index\" class=\"sort-indicator\" :style=\"{\r\n          position: 'absolute',\r\n          bottom: 0,\r\n          left: '50%',\r\n          transform: 'translateX(-50%)',\r\n          width: '40rpx',\r\n          height: '4rpx',\r\n          background: 'linear-gradient(90deg, #AC39FF 0%, #B87AFF 100%)',\r\n          borderRadius: '2rpx'\r\n        }\"></view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 商品列表 -->\r\n    <view class=\"products-list\" :style=\"{\r\n      padding: '0 20rpx'\r\n    }\">\r\n      <view class=\"products-grid\" :style=\"{\r\n        display: 'grid',\r\n        gridTemplateColumns: 'repeat(2, 1fr)',\r\n        gap: '20rpx'\r\n      }\">\r\n        <view \r\n          v-for=\"(product, index) in filteredProducts\" \r\n          :key=\"index\"\r\n          class=\"product-item\"\r\n          @click=\"viewProductDetail(product)\"\r\n          :style=\"{\r\n            background: '#FFFFFF',\r\n            borderRadius: '20rpx',\r\n            overflow: 'hidden',\r\n            boxShadow: '0 4px 10px rgba(0,0,0,0.05)'\r\n          }\"\r\n        >\r\n          <view class=\"product-image-wrapper\" :style=\"{\r\n            position: 'relative',\r\n            paddingTop: '100%'\r\n          }\">\r\n            <image \r\n              :src=\"product.image\" \r\n              mode=\"aspectFill\" \r\n              :style=\"{\r\n                position: 'absolute',\r\n                top: 0,\r\n                left: 0,\r\n                width: '100%',\r\n                height: '100%'\r\n              }\"\r\n            ></image>\r\n            \r\n            <view v-if=\"product.tag\" class=\"product-tag\" :style=\"{\r\n              position: 'absolute',\r\n              top: '10rpx',\r\n              left: '10rpx',\r\n              padding: '5rpx 15rpx',\r\n              borderRadius: '20rpx',\r\n              background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',\r\n              color: '#FFFFFF',\r\n              fontSize: '22rpx',\r\n              fontWeight: '500'\r\n            }\">\r\n              {{ product.tag }}\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"product-info\" :style=\"{\r\n            padding: '20rpx'\r\n          }\">\r\n            <text class=\"product-name\" :style=\"{\r\n              fontSize: '28rpx',\r\n              fontWeight: '500',\r\n              color: '#333333',\r\n              marginBottom: '10rpx',\r\n              display: 'block',\r\n              overflow: 'hidden',\r\n              textOverflow: 'ellipsis',\r\n              display: '-webkit-box',\r\n              '-webkit-line-clamp': '2',\r\n              '-webkit-box-orient': 'vertical',\r\n              lineHeight: '1.4'\r\n            }\">{{ product.name }}</text>\r\n            \r\n            <view class=\"product-price-row\" :style=\"{\r\n              display: 'flex',\r\n              alignItems: 'baseline',\r\n              marginBottom: '10rpx'\r\n            }\">\r\n              <text :style=\"{\r\n                fontSize: '24rpx',\r\n                color: '#FF3B69',\r\n                marginRight: '5rpx'\r\n              }\">¥</text>\r\n              <text :style=\"{\r\n                fontSize: '32rpx',\r\n                fontWeight: '600',\r\n                color: '#FF3B69',\r\n                marginRight: '10rpx'\r\n              }\">{{ product.price }}</text>\r\n              <text :style=\"{\r\n                fontSize: '24rpx',\r\n                color: '#999999',\r\n                textDecoration: 'line-through'\r\n              }\">¥{{ product.originalPrice }}</text>\r\n            </view>\r\n            \r\n            <view class=\"product-commission-row\" :style=\"{\r\n              display: 'flex',\r\n              justifyContent: 'space-between',\r\n              alignItems: 'center'\r\n            }\">\r\n              <view class=\"commission-info\" :style=\"{\r\n                display: 'flex',\r\n                alignItems: 'center'\r\n              }\">\r\n                <text :style=\"{\r\n                  fontSize: '24rpx',\r\n                  color: '#AC39FF',\r\n                  marginRight: '5rpx'\r\n                }\">佣金:</text>\r\n                <text :style=\"{\r\n                  fontSize: '24rpx',\r\n                  fontWeight: '600',\r\n                  color: '#AC39FF'\r\n                }\">¥{{ product.commission }}</text>\r\n              </view>\r\n              \r\n              <text :style=\"{\r\n                fontSize: '22rpx',\r\n                color: '#999999'\r\n              }\">已售{{ product.soldCount }}</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"promote-btn\" @click.stop=\"createPoster(product)\" :style=\"{\r\n            position: 'absolute',\r\n            bottom: '20rpx',\r\n            right: '20rpx',\r\n            padding: '8rpx 20rpx',\r\n            borderRadius: '30rpx',\r\n            background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)',\r\n            color: '#FFFFFF',\r\n            fontSize: '24rpx',\r\n            fontWeight: '500',\r\n            boxShadow: '0 4px 10px rgba(172,57,255,0.2)'\r\n          }\">\r\n            推广\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 空状态 -->\r\n      <view v-if=\"filteredProducts.length === 0\" class=\"empty-state\" :style=\"{\r\n        padding: '100rpx 0',\r\n        display: 'flex',\r\n        flexDirection: 'column',\r\n        alignItems: 'center'\r\n      }\">\r\n        <image src=\"/static/images/empty/empty-products.png\" mode=\"aspectFit\" :style=\"{\r\n          width: '200rpx',\r\n          height: '200rpx',\r\n          marginBottom: '20rpx'\r\n        }\"></image>\r\n        <text :style=\"{\r\n          fontSize: '28rpx',\r\n          color: '#999999'\r\n        }\">暂无相关商品</text>\r\n      </view>\r\n      \r\n      <!-- 加载更多 -->\r\n      <view v-if=\"filteredProducts.length > 0\" class=\"load-more\" :style=\"{\r\n        textAlign: 'center',\r\n        padding: '30rpx 0'\r\n      }\">\r\n        <text v-if=\"loading\" :style=\"{\r\n          fontSize: '26rpx',\r\n          color: '#999999'\r\n        }\">加载中...</text>\r\n        <text v-else-if=\"noMore\" :style=\"{\r\n          fontSize: '26rpx',\r\n          color: '#999999'\r\n        }\">没有更多商品了</text>\r\n        <text v-else @click=\"loadMore\" :style=\"{\r\n          fontSize: '26rpx',\r\n          color: '#AC39FF'\r\n        }\">加载更多</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 筛选面板 -->\r\n    <view v-if=\"showFilterPanel\" class=\"filter-panel-mask\" @click=\"toggleFilterPanel\" :style=\"{\r\n      position: 'fixed',\r\n      top: 0,\r\n      left: 0,\r\n      right: 0,\r\n      bottom: 0,\r\n      background: 'rgba(0,0,0,0.5)',\r\n      zIndex: 999\r\n    }\"></view>\r\n    \r\n    <view v-if=\"showFilterPanel\" class=\"filter-panel\" :style=\"{\r\n      position: 'fixed',\r\n      top: 0,\r\n      right: 0,\r\n      width: '70%',\r\n      height: '100%',\r\n      background: '#FFFFFF',\r\n      zIndex: 1000,\r\n      boxShadow: '-5px 0 15px rgba(0,0,0,0.1)',\r\n      display: 'flex',\r\n      flexDirection: 'column'\r\n    }\">\r\n      <view class=\"filter-header\" :style=\"{\r\n        padding: '30rpx',\r\n        borderBottom: '1rpx solid #EFEFEF',\r\n        display: 'flex',\r\n        justifyContent: 'space-between',\r\n        alignItems: 'center'\r\n      }\">\r\n        <text :style=\"{\r\n          fontSize: '32rpx',\r\n          fontWeight: '600',\r\n          color: '#333333'\r\n        }\">筛选条件</text>\r\n        \r\n        <view class=\"close-btn\" @click=\"toggleFilterPanel\" :style=\"{\r\n          width: '60rpx',\r\n          height: '60rpx',\r\n          borderRadius: '50%',\r\n          background: '#F2F2F7',\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'center'\r\n        }\">\r\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\">\r\n            <path d=\"M18 6L6 18M6 6l12 12\" stroke=\"#666666\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n          </svg>\r\n        </view>\r\n      </view>\r\n      \r\n      <scroll-view scroll-y class=\"filter-content\" :style=\"{\r\n        flex: 1,\r\n        padding: '30rpx'\r\n      }\">\r\n        <!-- 价格区间 -->\r\n        <view class=\"filter-section\" :style=\"{\r\n          marginBottom: '30rpx'\r\n        }\">\r\n          <text class=\"section-title\" :style=\"{\r\n            fontSize: '28rpx',\r\n            fontWeight: '500',\r\n            color: '#333333',\r\n            marginBottom: '20rpx',\r\n            display: 'block'\r\n          }\">价格区间</text>\r\n          \r\n          <view class=\"price-range\" :style=\"{\r\n            display: 'flex',\r\n            alignItems: 'center'\r\n          }\">\r\n            <input \r\n              type=\"digit\" \r\n              v-model=\"minPrice\" \r\n              placeholder=\"最低价\" \r\n              :style=\"{\r\n                width: '150rpx',\r\n                height: '70rpx',\r\n                background: '#F2F2F7',\r\n                borderRadius: '10rpx',\r\n                padding: '0 20rpx',\r\n                fontSize: '26rpx'\r\n              }\"\r\n            />\r\n            <text :style=\"{\r\n              margin: '0 20rpx',\r\n              color: '#999999'\r\n            }\">-</text>\r\n            <input \r\n              type=\"digit\" \r\n              v-model=\"maxPrice\" \r\n              placeholder=\"最高价\" \r\n              :style=\"{\r\n                width: '150rpx',\r\n                height: '70rpx',\r\n                background: '#F2F2F7',\r\n                borderRadius: '10rpx',\r\n                padding: '0 20rpx',\r\n                fontSize: '26rpx'\r\n              }\"\r\n            />\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 佣金比例 -->\r\n        <view class=\"filter-section\" :style=\"{\r\n          marginBottom: '30rpx'\r\n        }\">\r\n          <text class=\"section-title\" :style=\"{\r\n            fontSize: '28rpx',\r\n            fontWeight: '500',\r\n            color: '#333333',\r\n            marginBottom: '20rpx',\r\n            display: 'block'\r\n          }\">佣金比例</text>\r\n          \r\n          <view class=\"commission-options\" :style=\"{\r\n            display: 'flex',\r\n            flexWrap: 'wrap'\r\n          }\">\r\n            <view \r\n              v-for=\"(option, index) in commissionOptions\" \r\n              :key=\"index\"\r\n              class=\"commission-option\"\r\n              :class=\"{ active: selectedCommission === index }\"\r\n              @click=\"selectCommission(index)\"\r\n              :style=\"{\r\n                padding: '10rpx 30rpx',\r\n                borderRadius: '30rpx',\r\n                background: selectedCommission === index ? 'rgba(172,57,255,0.1)' : '#F2F2F7',\r\n                border: selectedCommission === index ? '1rpx solid #AC39FF' : '1rpx solid transparent',\r\n                color: selectedCommission === index ? '#AC39FF' : '#666666',\r\n                fontSize: '26rpx',\r\n                marginRight: '20rpx',\r\n                marginBottom: '20rpx'\r\n              }\"\r\n            >\r\n              {{ option }}\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 商品标签 -->\r\n        <view class=\"filter-section\" :style=\"{\r\n          marginBottom: '30rpx'\r\n        }\">\r\n          <text class=\"section-title\" :style=\"{\r\n            fontSize: '28rpx',\r\n            fontWeight: '500',\r\n            color: '#333333',\r\n            marginBottom: '20rpx',\r\n            display: 'block'\r\n          }\">商品标签</text>\r\n          \r\n          <view class=\"tag-options\" :style=\"{\r\n            display: 'flex',\r\n            flexWrap: 'wrap'\r\n          }\">\r\n            <view \r\n              v-for=\"(tag, index) in tagOptions\" \r\n              :key=\"index\"\r\n              class=\"tag-option\"\r\n              :class=\"{ active: selectedTags.includes(tag) }\"\r\n              @click=\"toggleTag(tag)\"\r\n              :style=\"{\r\n                padding: '10rpx 30rpx',\r\n                borderRadius: '30rpx',\r\n                background: selectedTags.includes(tag) ? 'rgba(172,57,255,0.1)' : '#F2F2F7',\r\n                border: selectedTags.includes(tag) ? '1rpx solid #AC39FF' : '1rpx solid transparent',\r\n                color: selectedTags.includes(tag) ? '#AC39FF' : '#666666',\r\n                fontSize: '26rpx',\r\n                marginRight: '20rpx',\r\n                marginBottom: '20rpx'\r\n              }\"\r\n            >\r\n              {{ tag }}\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </scroll-view>\r\n      \r\n      <view class=\"filter-footer\" :style=\"{\r\n        padding: '30rpx',\r\n        borderTop: '1rpx solid #EFEFEF',\r\n        display: 'flex'\r\n      }\">\r\n        <view class=\"reset-btn\" @click=\"resetFilter\" :style=\"{\r\n          flex: 1,\r\n          height: '80rpx',\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          borderRadius: '40rpx',\r\n          border: '1rpx solid #EFEFEF',\r\n          color: '#666666',\r\n          fontSize: '28rpx',\r\n          marginRight: '20rpx'\r\n        }\">\r\n          重置\r\n        </view>\r\n        \r\n        <view class=\"apply-btn\" @click=\"applyFilter\" :style=\"{\r\n          flex: 1,\r\n          height: '80rpx',\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          borderRadius: '40rpx',\r\n          background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)',\r\n          color: '#FFFFFF',\r\n          fontSize: '28rpx',\r\n          boxShadow: '0 5px 15px rgba(172,57,255,0.3)'\r\n        }\">\r\n          确定\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 底部安全区域 -->\r\n    <view class=\"safe-area-bottom\" :style=\"{\r\n      height: '100rpx',\r\n      paddingBottom: 'env(safe-area-inset-bottom)'\r\n    }\"></view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed } from 'vue';\r\n\r\n// 搜索关键词\r\nconst searchKeyword = ref('');\r\n\r\n// 分类数据\r\nconst categories = ref([\r\n  { id: 0, name: '全部' },\r\n  { id: 1, name: '数码' },\r\n  { id: 2, name: '家电' },\r\n  { id: 3, name: '服装' },\r\n  { id: 4, name: '美妆' },\r\n  { id: 5, name: '食品' },\r\n  { id: 6, name: '家居' },\r\n  { id: 7, name: '母婴' }\r\n]);\r\nconst currentCategory = ref(0);\r\n\r\n// 排序选项\r\nconst sortOptions = ref([\r\n  { name: '综合排序', field: 'default', order: 'desc' },\r\n  { name: '销量优先', field: 'soldCount', order: 'desc' },\r\n  { name: '价格升序', field: 'price', order: 'asc' },\r\n  { name: '价格降序', field: 'price', order: 'desc' },\r\n  { name: '佣金比例', field: 'commissionRate', order: 'desc' }\r\n]);\r\nconst currentSort = ref(0);\r\n\r\n// 筛选面板\r\nconst showFilterPanel = ref(false);\r\nconst minPrice = ref('');\r\nconst maxPrice = ref('');\r\nconst commissionOptions = ref(['5%以上', '10%以上', '15%以上', '20%以上']);\r\nconst selectedCommission = ref(-1);\r\nconst tagOptions = ref(['热销', '新品', '限时', '包邮', '满减', '秒杀']);\r\nconst selectedTags = ref([]);\r\n\r\n// 加载状态\r\nconst loading = ref(false);\r\nconst noMore = ref(false);\r\n\r\n// 商品数据\r\nconst products = ref([\r\n  {\r\n    id: 1,\r\n    name: 'Apple iPhone 14 Pro Max 256GB 暗夜紫 移动联通电信5G双卡双待手机',\r\n    image: 'https://via.placeholder.com/300',\r\n    price: '8999.00',\r\n    originalPrice: '9999.00',\r\n    commission: '300.00',\r\n    commissionRate: '3%',\r\n    soldCount: 1253,\r\n    category: 1,\r\n    tag: '热销'\r\n  },\r\n  {\r\n    id: 2,\r\n    name: '小米12S Ultra 12GB+256GB 丹青黑 骁龙8+旗舰处理器 徕卡专业光学镜头',\r\n    image: 'https://via.placeholder.com/300',\r\n    price: '5999.00',\r\n    originalPrice: '6999.00',\r\n    commission: '200.00',\r\n    commissionRate: '3.3%',\r\n    soldCount: 985,\r\n    category: 1,\r\n    tag: '新品'\r\n  },\r\n  {\r\n    id: 3,\r\n    name: '华为Mate 50 Pro 8GB+256GB 曜金黑 超光变XMAGE影像 北斗卫星消息',\r\n    image: 'https://via.placeholder.com/300',\r\n    price: '6799.00',\r\n    originalPrice: '7299.00',\r\n    commission: '250.00',\r\n    commissionRate: '3.7%',\r\n    soldCount: 756,\r\n    category: 1,\r\n    tag: '限时'\r\n  },\r\n  {\r\n    id: 4,\r\n    name: '戴森(Dyson) 吸尘器 V12 Detect Slim 手持无线吸尘器',\r\n    image: 'https://via.placeholder.com/300',\r\n    price: '4290.00',\r\n    originalPrice: '4990.00',\r\n    commission: '215.00',\r\n    commissionRate: '5%',\r\n    soldCount: 432,\r\n    category: 2,\r\n    tag: '热销'\r\n  },\r\n  {\r\n    id: 5,\r\n    name: '美的(Midea) 新风空调 1.5匹 壁挂式 智能家电 KFR-35GW/N8XHA1',\r\n    image: 'https://via.placeholder.com/300',\r\n    price: '3299.00',\r\n    originalPrice: '3899.00',\r\n    commission: '165.00',\r\n    commissionRate: '5%',\r\n    soldCount: 321,\r\n    category: 2,\r\n    tag: '满减'\r\n  },\r\n  {\r\n    id: 6,\r\n    name: 'NIKE 耐克 AIR FORCE 1 男子运动鞋 休闲板鞋',\r\n    image: 'https://via.placeholder.com/300',\r\n    price: '799.00',\r\n    originalPrice: '899.00',\r\n    commission: '80.00',\r\n    commissionRate: '10%',\r\n    soldCount: 1876,\r\n    category: 3,\r\n    tag: '包邮'\r\n  }\r\n]);\r\n\r\n// 过滤后的商品列表\r\nconst filteredProducts = computed(() => {\r\n  let result = [...products.value];\r\n  \r\n  // 根据分类筛选\r\n  if (currentCategory.value !== 0) {\r\n    result = result.filter(item => item.category === categories.value[currentCategory.value].id);\r\n  }\r\n  \r\n  // 根据搜索关键词筛选\r\n  if (searchKeyword.value) {\r\n    const keyword = searchKeyword.value.toLowerCase();\r\n    result = result.filter(item => item.name.toLowerCase().includes(keyword));\r\n  }\r\n  \r\n  // 根据排序选项排序\r\n  const { field, order } = sortOptions.value[currentSort.value];\r\n  \r\n  if (field !== 'default') {\r\n    result.sort((a, b) => {\r\n      let comparison = 0;\r\n      \r\n      if (field === 'price' || field === 'commissionRate' || field === 'soldCount') {\r\n        // 数值型字段，需要转换为数字\r\n        const aValue = field === 'commissionRate' \r\n          ? parseFloat(a[field].replace('%', '')) \r\n          : parseFloat(a[field]);\r\n        const bValue = field === 'commissionRate' \r\n          ? parseFloat(b[field].replace('%', '')) \r\n          : parseFloat(b[field]);\r\n        \r\n        comparison = aValue - bValue;\r\n      } else {\r\n        // 字符串型字段\r\n        comparison = a[field].localeCompare(b[field]);\r\n      }\r\n      \r\n      return order === 'asc' ? comparison : -comparison;\r\n    });\r\n  }\r\n  \r\n  return result;\r\n});\r\n\r\n// 搜索商品\r\nfunction searchProducts() {\r\n  // 这里可以添加搜索逻辑\r\n  uni.showToast({\r\n    title: '搜索: ' + searchKeyword.value,\r\n    icon: 'none'\r\n  });\r\n}\r\n\r\n// 切换分类\r\nfunction switchCategory(index) {\r\n  currentCategory.value = index;\r\n}\r\n\r\n// 切换排序方式\r\nfunction switchSort(index) {\r\n  currentSort.value = index;\r\n}\r\n\r\n// 切换筛选面板\r\nfunction toggleFilterPanel() {\r\n  showFilterPanel.value = !showFilterPanel.value;\r\n}\r\n\r\n// 选择佣金比例\r\nfunction selectCommission(index) {\r\n  selectedCommission.value = selectedCommission.value === index ? -1 : index;\r\n}\r\n\r\n// 切换标签选择\r\nfunction toggleTag(tag) {\r\n  const index = selectedTags.value.indexOf(tag);\r\n  if (index === -1) {\r\n    selectedTags.value.push(tag);\r\n  } else {\r\n    selectedTags.value.splice(index, 1);\r\n  }\r\n}\r\n\r\n// 重置筛选条件\r\nfunction resetFilter() {\r\n  minPrice.value = '';\r\n  maxPrice.value = '';\r\n  selectedCommission.value = -1;\r\n  selectedTags.value = [];\r\n}\r\n\r\n// 应用筛选条件\r\nfunction applyFilter() {\r\n  // 这里可以添加应用筛选的逻辑\r\n  showFilterPanel.value = false;\r\n  \r\n  uni.showToast({\r\n    title: '筛选条件已应用',\r\n    icon: 'success'\r\n  });\r\n}\r\n\r\n// 查看商品详情\r\nfunction viewProductDetail(product) {\r\n  uni.navigateTo({\r\n    url: `/subPackages/activity-showcase/pages/detail/index?id=${product.id}&source=distribution`\r\n  });\r\n}\r\n\r\n// 创建推广海报\r\nfunction createPoster(product) {\r\n  uni.navigateTo({\r\n    url: `/subPackages/activity-showcase/pages/distribution/poster/index?id=${product.id}`\r\n  });\r\n}\r\n\r\n// 加载更多\r\nfunction loadMore() {\r\n  if (loading.value || noMore.value) return;\r\n  \r\n  loading.value = true;\r\n  \r\n  // 模拟加载更多数据\r\n  setTimeout(() => {\r\n    // 这里应该调用API获取更多数据\r\n    // 模拟没有更多数据\r\n    noMore.value = true;\r\n    loading.value = false;\r\n  }, 1500);\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.products-container {\r\n  background-color: #F8F8F8;\r\n  min-height: 100vh;\r\n}\r\n\r\n.category-item:active, .promote-btn:active, .reset-btn:active, .apply-btn:active {\r\n  opacity: 0.9;\r\n  transform: scale(0.98);\r\n}\r\n\r\n.product-item {\r\n  position: relative;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.product-item:active {\r\n  transform: scale(0.98);\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/distribution/products/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni"], "mappings": ";;;;;;;;;;;;AAuhBA,UAAM,gBAAgBA,cAAAA,IAAI,EAAE;AAG5B,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,MACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,MACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,MACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,MACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,MACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,MACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,MACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,IACvB,CAAC;AACD,UAAM,kBAAkBA,cAAAA,IAAI,CAAC;AAG7B,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB,EAAE,MAAM,QAAQ,OAAO,WAAW,OAAO,OAAQ;AAAA,MACjD,EAAE,MAAM,QAAQ,OAAO,aAAa,OAAO,OAAQ;AAAA,MACnD,EAAE,MAAM,QAAQ,OAAO,SAAS,OAAO,MAAO;AAAA,MAC9C,EAAE,MAAM,QAAQ,OAAO,SAAS,OAAO,OAAQ;AAAA,MAC/C,EAAE,MAAM,QAAQ,OAAO,kBAAkB,OAAO,OAAQ;AAAA,IAC1D,CAAC;AACD,UAAM,cAAcA,cAAAA,IAAI,CAAC;AAGzB,UAAM,kBAAkBA,cAAAA,IAAI,KAAK;AACjC,UAAM,WAAWA,cAAAA,IAAI,EAAE;AACvB,UAAM,WAAWA,cAAAA,IAAI,EAAE;AACvB,UAAM,oBAAoBA,cAAG,IAAC,CAAC,QAAQ,SAAS,SAAS,OAAO,CAAC;AACjE,UAAM,qBAAqBA,cAAAA,IAAI,EAAE;AACjC,UAAM,aAAaA,cAAAA,IAAI,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,CAAC;AAC3D,UAAM,eAAeA,cAAAA,IAAI,CAAA,CAAE;AAG3B,UAAM,UAAUA,cAAAA,IAAI,KAAK;AACzB,UAAM,SAASA,cAAAA,IAAI,KAAK;AAGxB,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACnB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,gBAAgB;AAAA,QAChB,WAAW;AAAA,QACX,UAAU;AAAA,QACV,KAAK;AAAA,MACN;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,gBAAgB;AAAA,QAChB,WAAW;AAAA,QACX,UAAU;AAAA,QACV,KAAK;AAAA,MACN;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,gBAAgB;AAAA,QAChB,WAAW;AAAA,QACX,UAAU;AAAA,QACV,KAAK;AAAA,MACN;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,gBAAgB;AAAA,QAChB,WAAW;AAAA,QACX,UAAU;AAAA,QACV,KAAK;AAAA,MACN;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,gBAAgB;AAAA,QAChB,WAAW;AAAA,QACX,UAAU;AAAA,QACV,KAAK;AAAA,MACN;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,gBAAgB;AAAA,QAChB,WAAW;AAAA,QACX,UAAU;AAAA,QACV,KAAK;AAAA,MACN;AAAA,IACH,CAAC;AAGD,UAAM,mBAAmBC,cAAQ,SAAC,MAAM;AACtC,UAAI,SAAS,CAAC,GAAG,SAAS,KAAK;AAG/B,UAAI,gBAAgB,UAAU,GAAG;AAC/B,iBAAS,OAAO,OAAO,UAAQ,KAAK,aAAa,WAAW,MAAM,gBAAgB,KAAK,EAAE,EAAE;AAAA,MAC5F;AAGD,UAAI,cAAc,OAAO;AACvB,cAAM,UAAU,cAAc,MAAM,YAAW;AAC/C,iBAAS,OAAO,OAAO,UAAQ,KAAK,KAAK,cAAc,SAAS,OAAO,CAAC;AAAA,MACzE;AAGD,YAAM,EAAE,OAAO,MAAO,IAAG,YAAY,MAAM,YAAY,KAAK;AAE5D,UAAI,UAAU,WAAW;AACvB,eAAO,KAAK,CAAC,GAAG,MAAM;AACpB,cAAI,aAAa;AAEjB,cAAI,UAAU,WAAW,UAAU,oBAAoB,UAAU,aAAa;AAE5E,kBAAM,SAAS,UAAU,mBACrB,WAAW,EAAE,KAAK,EAAE,QAAQ,KAAK,EAAE,CAAC,IACpC,WAAW,EAAE,KAAK,CAAC;AACvB,kBAAM,SAAS,UAAU,mBACrB,WAAW,EAAE,KAAK,EAAE,QAAQ,KAAK,EAAE,CAAC,IACpC,WAAW,EAAE,KAAK,CAAC;AAEvB,yBAAa,SAAS;AAAA,UAC9B,OAAa;AAEL,yBAAa,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,CAAC;AAAA,UAC7C;AAED,iBAAO,UAAU,QAAQ,aAAa,CAAC;AAAA,QAC7C,CAAK;AAAA,MACF;AAED,aAAO;AAAA,IACT,CAAC;AAGD,aAAS,iBAAiB;AAExBC,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,SAAS,cAAc;AAAA,QAC9B,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,aAAS,eAAe,OAAO;AAC7B,sBAAgB,QAAQ;AAAA,IAC1B;AAGA,aAAS,WAAW,OAAO;AACzB,kBAAY,QAAQ;AAAA,IACtB;AAGA,aAAS,oBAAoB;AAC3B,sBAAgB,QAAQ,CAAC,gBAAgB;AAAA,IAC3C;AAGA,aAAS,iBAAiB,OAAO;AAC/B,yBAAmB,QAAQ,mBAAmB,UAAU,QAAQ,KAAK;AAAA,IACvE;AAGA,aAAS,UAAU,KAAK;AACtB,YAAM,QAAQ,aAAa,MAAM,QAAQ,GAAG;AAC5C,UAAI,UAAU,IAAI;AAChB,qBAAa,MAAM,KAAK,GAAG;AAAA,MAC/B,OAAS;AACL,qBAAa,MAAM,OAAO,OAAO,CAAC;AAAA,MACnC;AAAA,IACH;AAGA,aAAS,cAAc;AACrB,eAAS,QAAQ;AACjB,eAAS,QAAQ;AACjB,yBAAmB,QAAQ;AAC3B,mBAAa,QAAQ;IACvB;AAGA,aAAS,cAAc;AAErB,sBAAgB,QAAQ;AAExBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,aAAS,kBAAkB,SAAS;AAClCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,wDAAwD,QAAQ,EAAE;AAAA,MAC3E,CAAG;AAAA,IACH;AAGA,aAAS,aAAa,SAAS;AAC7BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,qEAAqE,QAAQ,EAAE;AAAA,MACxF,CAAG;AAAA,IACH;AAGA,aAAS,WAAW;AAClB,UAAI,QAAQ,SAAS,OAAO;AAAO;AAEnC,cAAQ,QAAQ;AAGhB,iBAAW,MAAM;AAGf,eAAO,QAAQ;AACf,gBAAQ,QAAQ;AAAA,MACjB,GAAE,IAAI;AAAA,IACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzwBA,GAAG,WAAW,eAAe;"}