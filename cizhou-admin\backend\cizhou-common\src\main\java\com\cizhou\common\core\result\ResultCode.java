package com.cizhou.common.core.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态码枚举
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    // 成功
    SUCCESS(200, "操作成功"),

    // 客户端错误 4xx
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未认证"),
    FORBIDDEN(403, "无权限访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    UNPROCESSABLE_ENTITY(422, "请求参数验证失败"),
    TOO_MANY_REQUESTS(429, "请求过于频繁"),

    // 服务端错误 5xx
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    BAD_GATEWAY(502, "网关错误"),
    SERVICE_UNAVAILABLE(503, "服务暂时不可用"),
    GATEWAY_TIMEOUT(504, "网关超时"),

    // 业务错误 1xxx
    BUSINESS_ERROR(1000, "业务处理失败"),
    
    // 用户相关错误 11xx
    USER_NOT_FOUND(1101, "用户不存在"),
    USER_DISABLED(1102, "用户已被禁用"),
    USER_LOCKED(1103, "用户已被锁定"),
    USERNAME_EXISTS(1104, "用户名已存在"),
    PHONE_EXISTS(1105, "手机号已存在"),
    EMAIL_EXISTS(1106, "邮箱已存在"),
    PASSWORD_ERROR(1107, "密码错误"),
    OLD_PASSWORD_ERROR(1108, "原密码错误"),
    
    // 认证相关错误 12xx
    TOKEN_INVALID(1201, "Token无效"),
    TOKEN_EXPIRED(1202, "Token已过期"),
    TOKEN_MISSING(1203, "Token缺失"),
    REFRESH_TOKEN_INVALID(1204, "刷新Token无效"),
    CAPTCHA_ERROR(1205, "验证码错误"),
    CAPTCHA_EXPIRED(1206, "验证码已过期"),
    LOGIN_FAILED(1207, "登录失败"),
    LOGOUT_FAILED(1208, "登出失败"),
    
    // 权限相关错误 13xx
    PERMISSION_DENIED(1301, "权限不足"),
    ROLE_NOT_FOUND(1302, "角色不存在"),
    PERMISSION_NOT_FOUND(1303, "权限不存在"),
    ROLE_IN_USE(1304, "角色正在使用中"),
    PERMISSION_IN_USE(1305, "权限正在使用中"),
    
    // 商家相关错误 14xx
    MERCHANT_NOT_FOUND(1401, "商家不存在"),
    MERCHANT_DISABLED(1402, "商家已被禁用"),
    MERCHANT_AUDIT_PENDING(1403, "商家审核中"),
    MERCHANT_AUDIT_REJECTED(1404, "商家审核被拒绝"),
    BUSINESS_LICENSE_EXISTS(1405, "营业执照号已存在"),
    
    // 内容相关错误 15xx
    CONTENT_NOT_FOUND(1501, "内容不存在"),
    CONTENT_DISABLED(1502, "内容已被禁用"),
    CONTENT_AUDIT_PENDING(1503, "内容审核中"),
    CONTENT_AUDIT_REJECTED(1504, "内容审核被拒绝"),
    CATEGORY_NOT_FOUND(1505, "分类不存在"),
    CATEGORY_IN_USE(1506, "分类正在使用中"),
    
    // 订单相关错误 16xx
    ORDER_NOT_FOUND(1601, "订单不存在"),
    ORDER_STATUS_ERROR(1602, "订单状态错误"),
    ORDER_CANNOT_CANCEL(1603, "订单无法取消"),
    ORDER_CANNOT_REFUND(1604, "订单无法退款"),
    PAYMENT_FAILED(1605, "支付失败"),
    REFUND_FAILED(1606, "退款失败"),
    
    // 营销相关错误 17xx
    ACTIVITY_NOT_FOUND(1701, "活动不存在"),
    ACTIVITY_NOT_STARTED(1702, "活动未开始"),
    ACTIVITY_ENDED(1703, "活动已结束"),
    COUPON_NOT_FOUND(1704, "优惠券不存在"),
    COUPON_EXPIRED(1705, "优惠券已过期"),
    COUPON_USED(1706, "优惠券已使用"),
    COUPON_NOT_AVAILABLE(1707, "优惠券不可用"),
    
    // 文件相关错误 18xx
    FILE_UPLOAD_FAILED(1801, "文件上传失败"),
    FILE_TYPE_NOT_SUPPORTED(1802, "文件类型不支持"),
    FILE_SIZE_EXCEEDED(1803, "文件大小超出限制"),
    FILE_NOT_FOUND(1804, "文件不存在"),
    
    // 数据相关错误 19xx
    DATA_NOT_FOUND(1901, "数据不存在"),
    DATA_DUPLICATE(1902, "数据重复"),
    DATA_INTEGRITY_VIOLATION(1903, "数据完整性约束违反"),
    DATA_ACCESS_ERROR(1904, "数据访问错误"),
    
    // 系统相关错误 20xx
    SYSTEM_BUSY(2001, "系统繁忙，请稍后重试"),
    SYSTEM_MAINTENANCE(2002, "系统维护中"),
    CONFIG_ERROR(2003, "系统配置错误"),
    THIRD_PARTY_SERVICE_ERROR(2004, "第三方服务错误"),
    NETWORK_ERROR(2005, "网络错误"),
    DATABASE_ERROR(2006, "数据库错误"),
    CACHE_ERROR(2007, "缓存错误"),
    MQ_ERROR(2008, "消息队列错误");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 消息
     */
    private final String message;

    /**
     * 根据状态码获取枚举
     */
    public static ResultCode getByCode(Integer code) {
        for (ResultCode resultCode : values()) {
            if (resultCode.getCode().equals(code)) {
                return resultCode;
            }
        }
        return INTERNAL_SERVER_ERROR;
    }

    /**
     * 判断是否为成功状态码
     */
    public static boolean isSuccess(Integer code) {
        return SUCCESS.getCode().equals(code);
    }

    /**
     * 判断是否为客户端错误
     */
    public static boolean isClientError(Integer code) {
        return code >= 400 && code < 500;
    }

    /**
     * 判断是否为服务端错误
     */
    public static boolean isServerError(Integer code) {
        return code >= 500 && code < 600;
    }

    /**
     * 判断是否为业务错误
     */
    public static boolean isBusinessError(Integer code) {
        return code >= 1000 && code < 3000;
    }
}
