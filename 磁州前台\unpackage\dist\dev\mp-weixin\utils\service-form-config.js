"use strict";
const generalHomeServiceForm = {
  sections: [
    {
      label: "服务基本信息",
      fields: [
        { type: "input", label: "服务标题", name: "title", required: true, placeholder: "请简要描述您提供的服务" },
        { type: "input", label: "价格范围", name: "price", required: true, placeholder: "例如：50-100元/次" },
        { type: "input", label: "服务时间", name: "serviceTime", required: true, placeholder: "例如：周一至周日 8:00-20:00" },
        { type: "location", label: "服务区域", name: "serviceArea", required: true },
        { type: "upload", label: "服务展示图片", name: "images", required: true, tip: "请上传清晰的服务相关图片，最多5张" },
        { type: "textarea", label: "服务详情", name: "description", required: true, placeholder: "请详细描述您提供的服务内容、服务流程等信息" }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", label: "联系人", name: "contactName", required: true },
        { type: "input", label: "联系电话", name: "contactPhone", required: true, inputType: "number" },
        { type: "input", label: "微信号", name: "wechat", required: false }
      ]
    }
  ]
};
const homeCleaningForm = {
  sections: [
    {
      label: "服务基本信息",
      fields: [
        { type: "input", label: "服务标题", name: "title", required: true, placeholder: "请简要描述您提供的家政保洁服务" },
        { type: "input", label: "价格范围", name: "price", required: true, placeholder: "例如：50-100元/小时" },
        { type: "input", label: "服务时间", name: "serviceTime", required: true, placeholder: "例如：周一至周日 8:00-20:00" },
        { type: "location", label: "服务区域", name: "serviceArea", required: true },
        { type: "tags", label: "服务类型", name: "serviceTags", required: true, options: ["日常保洁", "深度保洁", "开荒保洁", "家电清洗", "玻璃清洗", "地板打蜡", "沙发清洗", "厨房清洁"], maxCount: 5 },
        { type: "upload", label: "服务展示图片", name: "images", required: true, tip: "请上传清晰的服务相关图片，最多5张" },
        { type: "textarea", label: "服务详情", name: "description", required: true, placeholder: "请详细描述您提供的保洁服务内容、服务流程、使用的清洁用品等信息" }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", label: "联系人", name: "contactName", required: true },
        { type: "input", label: "联系电话", name: "contactPhone", required: true, inputType: "number" },
        { type: "input", label: "微信号", name: "wechat", required: false }
      ]
    }
  ]
};
const repairForm = {
  sections: [
    {
      label: "服务基本信息",
      fields: [
        { type: "input", label: "服务标题", name: "title", required: true, placeholder: "请简要描述您提供的维修改造服务" },
        { type: "input", label: "价格范围", name: "price", required: true, placeholder: "例如：上门费20元起，具体价格面议" },
        { type: "input", label: "服务时间", name: "serviceTime", required: true, placeholder: "例如：周一至周日 8:00-20:00" },
        { type: "location", label: "服务区域", name: "serviceArea", required: true },
        { type: "tags", label: "维修类型", name: "repairTags", required: true, options: ["水电维修", "木工维修", "家具维修", "电器维修", "管道疏通", "墙面翻新", "门窗维修", "其他维修"], maxCount: 5 },
        { type: "upload", label: "服务展示图片", name: "images", required: true, tip: "请上传清晰的服务相关图片或维修案例，最多5张" },
        { type: "textarea", label: "服务详情", name: "description", required: true, placeholder: "请详细描述您提供的维修服务内容、服务流程、维修经验等信息" }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", label: "联系人", name: "contactName", required: true },
        { type: "input", label: "联系电话", name: "contactPhone", required: true, inputType: "number" },
        { type: "input", label: "微信号", name: "wechat", required: false }
      ]
    }
  ]
};
const installationForm = {
  sections: [
    {
      label: "服务基本信息",
      fields: [
        { type: "input", label: "服务标题", name: "title", required: true, placeholder: "请简要描述您提供的安装服务" },
        { type: "input", label: "价格范围", name: "price", required: true, placeholder: "例如：根据安装物品不同，价格不同" },
        { type: "input", label: "服务时间", name: "serviceTime", required: true, placeholder: "例如：周一至周日 8:00-20:00" },
        { type: "location", label: "服务区域", name: "serviceArea", required: true },
        { type: "tags", label: "安装类型", name: "installationTags", required: true, options: ["家具安装", "灯具安装", "电器安装", "窗帘安装", "洁具安装", "净水器安装", "智能锁安装", "其他安装"], maxCount: 5 },
        { type: "upload", label: "服务展示图片", name: "images", required: true, tip: "请上传清晰的服务相关图片，最多5张" },
        { type: "textarea", label: "服务详情", name: "description", required: true, placeholder: "请详细描述您提供的安装服务内容、安装流程、经验等信息" }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", label: "联系人", name: "contactName", required: true },
        { type: "input", label: "联系电话", name: "contactPhone", required: true, inputType: "number" },
        { type: "input", label: "微信号", name: "wechat", required: false }
      ]
    }
  ]
};
const locksmithForm = {
  sections: [
    {
      label: "服务基本信息",
      fields: [
        { type: "input", label: "服务标题", name: "title", required: true, placeholder: "请简要描述您提供的开锁换锁服务" },
        { type: "input", label: "价格范围", name: "price", required: true, placeholder: "例如：开锁30元起，换锁费用另计" },
        { type: "input", label: "服务时间", name: "serviceTime", required: true, placeholder: "例如：24小时服务" },
        { type: "location", label: "服务区域", name: "serviceArea", required: true },
        { type: "tags", label: "服务类型", name: "locksmithTags", required: true, options: ["门锁开锁", "保险柜开锁", "汽车开锁", "锁具更换", "防盗锁安装", "智能锁安装", "锁具维修", "其他服务"], maxCount: 5 },
        { type: "upload", label: "服务展示图片", name: "images", required: true, tip: "请上传清晰的服务相关图片，最多5张" },
        { type: "textarea", label: "服务详情", name: "description", required: true, placeholder: "请详细描述您提供的开锁换锁服务内容、服务流程、资质证明等信息" }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", label: "联系人", name: "contactName", required: true },
        { type: "input", label: "联系电话", name: "contactPhone", required: true, inputType: "number" },
        { type: "input", label: "微信号", name: "wechat", required: false }
      ]
    }
  ]
};
const movingForm = {
  sections: [
    {
      label: "服务基本信息",
      fields: [
        { type: "input", label: "服务标题", name: "title", required: true, placeholder: "请简要描述您提供的搬家拉货服务" },
        { type: "input", label: "价格范围", name: "price", required: true, placeholder: "例如：小型搬家200元起，大型搬家面议" },
        { type: "input", label: "服务时间", name: "serviceTime", required: true, placeholder: "例如：周一至周日 6:00-22:00" },
        { type: "location", label: "服务区域", name: "serviceArea", required: true },
        { type: "tags", label: "服务类型", name: "movingTags", required: true, options: ["居民搬家", "小型搬运", "长途搬家", "办公室搬迁", "家具拆装", "大件运输", "货物运输", "其他服务"], maxCount: 5 },
        { type: "upload", label: "服务展示图片", name: "images", required: true, tip: "请上传清晰的服务相关图片，最多5张" },
        { type: "textarea", label: "服务详情", name: "description", required: true, placeholder: "请详细描述您提供的搬家拉货服务内容、车辆信息、服务流程等信息" }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", label: "联系人", name: "contactName", required: true },
        { type: "input", label: "联系电话", name: "contactPhone", required: true, inputType: "number" },
        { type: "input", label: "微信号", name: "wechat", required: false }
      ]
    }
  ]
};
const beautyServiceForm = {
  sections: [
    {
      label: "服务基本信息",
      fields: [
        { type: "input", label: "服务标题", name: "title", required: true, placeholder: "请简要描述您提供的上门美容服务" },
        { type: "input", label: "价格范围", name: "price", required: true, placeholder: "例如：面部护理100元起，美甲80元起" },
        { type: "input", label: "服务时间", name: "serviceTime", required: true, placeholder: "例如：周一至周日 9:00-21:00" },
        { type: "location", label: "服务区域", name: "serviceArea", required: true },
        { type: "tags", label: "服务类型", name: "beautyTags", required: true, options: ["面部护理", "美甲服务", "美睫服务", "化妆服务", "头发护理", "身体按摩", "中医美容", "其他美容"], maxCount: 5 },
        { type: "upload", label: "服务展示图片", name: "images", required: true, tip: "请上传清晰的服务相关图片或案例，最多5张" },
        { type: "textarea", label: "服务详情", name: "description", required: true, placeholder: "请详细描述您提供的美容服务内容、使用的产品、服务流程、资质证明等信息" }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", label: "联系人", name: "contactName", required: true },
        { type: "input", label: "联系电话", name: "contactPhone", required: true, inputType: "number" },
        { type: "input", label: "微信号", name: "wechat", required: false }
      ]
    }
  ]
};
const tutorForm = {
  sections: [
    {
      label: "服务基本信息",
      fields: [
        { type: "input", label: "服务标题", name: "title", required: true, placeholder: "请简要描述您提供的上门家教服务" },
        { type: "input", label: "价格范围", name: "price", required: true, placeholder: "例如：小学课程80元/小时，初中课程100元/小时" },
        { type: "input", label: "服务时间", name: "serviceTime", required: true, placeholder: "例如：周一至周五18:00-21:00，周末全天" },
        { type: "location", label: "服务区域", name: "serviceArea", required: true },
        { type: "tags", label: "教学科目", name: "tutorTags", required: true, options: ["语文", "数学", "英语", "物理", "化学", "生物", "历史", "地理", "政治", "音乐", "美术", "体育", "编程", "其他"], maxCount: 6 },
        { type: "upload", label: "相关证件或照片", name: "images", required: true, tip: "请上传学历证明、教师资格证等相关证件照片，最多5张" },
        { type: "textarea", label: "教学经验", name: "description", required: true, placeholder: "请详细描述您的教学经验、教育背景、授课方法等信息" }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", label: "联系人", name: "contactName", required: true },
        { type: "input", label: "联系电话", name: "contactPhone", required: true, inputType: "number" },
        { type: "input", label: "微信号", name: "wechat", required: false }
      ]
    }
  ]
};
const petServiceForm = {
  sections: [
    {
      label: "服务基本信息",
      fields: [
        { type: "input", label: "服务标题", name: "title", required: true, placeholder: "请简要描述您提供的宠物服务" },
        { type: "input", label: "价格范围", name: "price", required: true, placeholder: "例如：宠物洗澡50元起，宠物寄养100元/天" },
        { type: "input", label: "服务时间", name: "serviceTime", required: true, placeholder: "例如：周一至周日 9:00-20:00" },
        { type: "location", label: "服务区域", name: "serviceArea", required: true },
        { type: "tags", label: "服务类型", name: "petServiceTags", required: true, options: ["宠物洗护", "宠物美容", "宠物寄养", "宠物训练", "宠物医疗", "宠物接送", "上门遛狗", "其他服务"], maxCount: 5 },
        { type: "upload", label: "服务展示图片", name: "images", required: true, tip: "请上传清晰的服务相关图片或案例，最多5张" },
        { type: "textarea", label: "服务详情", name: "description", required: true, placeholder: "请详细描述您提供的宠物服务内容、服务流程、服务环境、资质证明等信息" }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", label: "联系人", name: "contactName", required: true },
        { type: "input", label: "联系电话", name: "contactPhone", required: true, inputType: "number" },
        { type: "input", label: "微信号", name: "wechat", required: false }
      ]
    }
  ]
};
const plumbingForm = {
  sections: [
    {
      label: "服务基本信息",
      fields: [
        { type: "input", label: "服务标题", name: "title", required: true, placeholder: "请简要描述您提供的上门疏通服务" },
        { type: "input", label: "价格范围", name: "price", required: true, placeholder: "例如：普通疏通50元起，疑难管道疏通面议" },
        { type: "input", label: "服务时间", name: "serviceTime", required: true, placeholder: "例如：24小时服务" },
        { type: "location", label: "服务区域", name: "serviceArea", required: true },
        { type: "tags", label: "疏通类型", name: "plumbingTags", required: true, options: ["马桶疏通", "地漏疏通", "厨房下水道", "洗菜池疏通", "浴缸疏通", "管道清洗", "化粪池清理", "其他疏通"], maxCount: 5 },
        { type: "upload", label: "服务展示图片", name: "images", required: true, tip: "请上传清晰的服务相关图片或案例，最多5张" },
        { type: "textarea", label: "服务详情", name: "description", required: true, placeholder: "请详细描述您提供的疏通服务内容、服务流程、使用的工具、服务经验等信息" }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", label: "联系人", name: "contactName", required: true },
        { type: "input", label: "联系电话", name: "contactPhone", required: true, inputType: "number" },
        { type: "input", label: "微信号", name: "wechat", required: false }
      ]
    }
  ]
};
const otherServiceForm = {
  sections: [
    {
      label: "服务基本信息",
      fields: [
        { type: "input", label: "服务标题", name: "title", required: true, placeholder: "请简要描述您提供的服务" },
        { type: "input", label: "服务类型", name: "serviceType", required: true, placeholder: "请填写您的服务类型" },
        { type: "input", label: "价格范围", name: "price", required: true, placeholder: "例如：服务价格面议" },
        { type: "input", label: "服务时间", name: "serviceTime", required: true, placeholder: "例如：周一至周日 9:00-18:00" },
        { type: "location", label: "服务区域", name: "serviceArea", required: true },
        { type: "upload", label: "服务展示图片", name: "images", required: true, tip: "请上传清晰的服务相关图片，最多5张" },
        { type: "textarea", label: "服务详情", name: "description", required: true, placeholder: "请详细描述您提供的服务内容、服务流程、专业资质等信息" }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", label: "联系人", name: "contactName", required: true },
        { type: "input", label: "联系电话", name: "contactPhone", required: true, inputType: "number" },
        { type: "input", label: "微信号", name: "wechat", required: false }
      ]
    }
  ]
};
const datingForm = {
  sections: [
    {
      label: "基本信息",
      fields: [
        { type: "input", name: "nickname", label: "昵称", required: true, placeholder: "请输入您的昵称" },
        { type: "gender", name: "gender", label: "性别", required: true },
        { type: "input", name: "age", label: "年龄", required: true, inputType: "number", placeholder: "请输入您的实际年龄" },
        { type: "input", name: "height", label: "身高", required: true, inputType: "number", placeholder: "请输入身高(cm)", suffix: "cm" },
        { type: "picker", name: "education", label: "学历", required: true, options: ["初中及以下", "高中/中专", "大专", "本科", "硕士", "博士及以上"] },
        { type: "picker", name: "maritalStatus", label: "婚姻状况", required: true, options: ["未婚", "离异", "丧偶"] },
        { type: "input", name: "occupation", label: "职业", required: true, placeholder: "请输入您的职业" },
        { type: "input", name: "income", label: "月收入", required: true, placeholder: "如：5000-8000、8000-12000等" }
      ]
    },
    {
      label: "个人介绍",
      fields: [
        { type: "textarea", name: "selfIntro", label: "自我介绍", required: true, placeholder: "请简要介绍自己的性格、爱好、生活方式等" },
        { type: "textarea", name: "expectation", label: "择偶要求", required: true, placeholder: "请描述您期望的另一半的条件、性格等" },
        { type: "upload", name: "images", label: "个人照片", required: true, tip: "请上传清晰的本人近照，最多5张" },
        { type: "tags", name: "interests", label: "兴趣爱好", options: ["旅游", "美食", "运动", "阅读", "电影", "音乐", "摄影", "书法", "绘画", "舞蹈", "游戏", "其他"], maxCount: 5 }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", name: "contact", label: "联系人", required: true, placeholder: "请输入联系人姓名" },
        { type: "input", name: "phone", label: "手机号", required: true, inputType: "number", placeholder: "请输入有效的手机号码" },
        { type: "sendCode", name: "code", label: "验证码", required: true },
        { type: "input", name: "wechat", label: "微信号", placeholder: "方便联系的微信号" }
      ]
    }
  ]
};
const merchantActivityForm = {
  sections: [
    {
      label: "活动基本信息",
      fields: [
        { type: "input", name: "title", label: "活动标题", required: true, placeholder: "请输入活动标题，简明扼要" },
        { type: "input", name: "merchantName", label: "商家名称", required: true, placeholder: "请输入商家全称" },
        { type: "picker", name: "activityType", label: "活动类型", required: true, options: ["优惠折扣", "满减活动", "买赠活动", "限时特价", "新品上市", "会员专享", "节日活动", "其他"] },
        { type: "date", name: "startDate", label: "开始日期", required: true },
        { type: "date", name: "endDate", label: "结束日期", required: true },
        { type: "location", name: "address", label: "活动地点", required: true, placeholder: "请输入详细的商家地址" }
      ]
    },
    {
      label: "活动详情",
      fields: [
        { type: "textarea", name: "description", label: "活动描述", required: true, placeholder: "请详细描述活动内容、优惠力度、参与方式等" },
        { type: "textarea", name: "rules", label: "活动规则", required: true, placeholder: "请说明活动的规则、限制条件等" },
        { type: "upload", name: "images", label: "活动图片", required: true, tip: "请上传活动海报、商品图片等，最多5张" },
        { type: "tags", name: "tags", label: "活动标签", options: ["超值优惠", "限时抢购", "新店开业", "周年庆", "节日特惠", "会员专享", "秒杀活动", "团购优惠"], maxCount: 3 }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", name: "contact", label: "联系人", required: true, placeholder: "请输入活动负责人姓名" },
        { type: "input", name: "phone", label: "联系电话", required: true, inputType: "number", placeholder: "请输入有效的联系电话" },
        { type: "sendCode", name: "code", label: "验证码", required: true },
        { type: "input", name: "wechat", label: "微信号", placeholder: "方便客户咨询的微信号" }
      ]
    }
  ]
};
const businessTransferForm = {
  sections: [
    {
      label: "转让信息",
      fields: [
        { type: "input", label: "标题", name: "title", required: true, placeholder: "请简要描述您要转让的店铺/摊位" },
        { type: "picker", label: "转让类型", name: "transferType", required: true, options: ["店铺转让", "摊位转让", "柜台转让", "档口转让", "其他"] },
        { type: "input", label: "经营类型", name: "businessType", required: true, placeholder: "例如：餐饮、服装、美容美发" },
        { type: "location", label: "店铺地址", name: "location", required: true },
        { type: "input", label: "店面面积", name: "area", required: false, placeholder: "请输入店面面积", suffix: "㎡" },
        { type: "input", label: "月租金", name: "rent", required: true, placeholder: "请输入月租金", suffix: "元/月" },
        { type: "input", label: "转让费", name: "transferFee", required: true, placeholder: "请输入转让费", suffix: "元" },
        { type: "input", label: "剩余租期", name: "leaseTime", required: false, placeholder: "例如：3年、10个月" },
        { type: "upload", label: "店铺照片", name: "images", required: true, tip: "请上传清晰的店铺内外部照片，最多9张" },
        { type: "textarea", label: "详细描述", name: "description", required: true, placeholder: "请详细描述店铺的装修情况、客流量、经营状况、转让原因等信息" }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", label: "联系人", name: "contactName", required: true },
        { type: "input", label: "联系电话", name: "contactPhone", required: true, inputType: "number" },
        { type: "input", label: "微信号", name: "wechat", required: false }
      ]
    }
  ]
};
const educationForm = {
  sections: [
    {
      label: "课程信息",
      fields: [
        { type: "input", label: "标题", name: "title", required: true, placeholder: "请简要描述您的课程或培训项目" },
        { type: "picker", label: "教育类型", name: "educationType", required: true, options: ["学科辅导", "兴趣培训", "技能培训", "语言培训", "职业培训", "资格证书", "其他教育"] },
        { type: "input", label: "适合对象", name: "targetStudents", required: true, placeholder: "例如：小学生、职场人士、零基础学员" },
        { type: "input", label: "课程价格", name: "price", required: true, placeholder: "例如：3980元/期、198元/课时" },
        { type: "input", label: "课程时长", name: "duration", required: true, placeholder: "例如：3个月、16课时" },
        { type: "input", label: "上课时间", name: "classTime", required: true, placeholder: "例如：周末班、晚上7-9点" },
        { type: "location", label: "上课地点", name: "location", required: true },
        { type: "upload", label: "相关图片", name: "images", required: true, tip: "请上传课程、教师或场地照片，最多5张" },
        { type: "textarea", label: "课程详情", name: "description", required: true, placeholder: "请详细描述课程内容、教学特点、师资力量、学习效果等信息" }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", label: "联系人", name: "contactName", required: true },
        { type: "input", label: "联系电话", name: "contactPhone", required: true, inputType: "number" },
        { type: "input", label: "微信号", name: "wechat", required: false }
      ]
    }
  ]
};
const defaultForm = {
  sections: [
    {
      label: "基本信息",
      fields: [
        { type: "input", label: "标题", name: "title", required: true, placeholder: "请输入标题" },
        { type: "upload", label: "相关图片", name: "images", required: true, tip: "请上传相关图片，最多5张" },
        { type: "textarea", label: "详细描述", name: "description", required: true, placeholder: "请详细描述您要发布的信息内容" }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", label: "联系人", name: "contactName", required: true },
        { type: "input", label: "联系电话", name: "contactPhone", required: true, inputType: "number" },
        { type: "input", label: "微信号", name: "wechat", required: false }
      ]
    }
  ]
};
const petForm = {
  sections: [
    {
      label: "宠物信息",
      fields: [
        { type: "input", label: "标题", name: "title", required: true, placeholder: "请简要描述您的宠物" },
        { type: "picker", label: "宠物类型", name: "petType", required: true, options: ["狗狗", "猫咪", "兔子", "鼠类", "鸟类", "鱼类", "龟类", "其他"] },
        { type: "input", label: "品种", name: "breed", required: true, placeholder: "例如：金毛、英短、荷兰垂耳兔" },
        { type: "picker", label: "性别", name: "gender", required: true, options: ["公", "母", "未知"] },
        { type: "input", label: "年龄", name: "age", required: true, placeholder: "例如：2岁、3个月" },
        { type: "picker", label: "是否纯种", name: "isPurebred", required: false, options: ["是", "否", "不确定"] },
        { type: "picker", label: "疫苗情况", name: "vaccineStatus", required: false, options: ["已接种", "部分接种", "未接种"] },
        { type: "picker", label: "驱虫情况", name: "dewormingStatus", required: false, options: ["已驱虫", "未驱虫"] },
        { type: "picker", label: "绝育情况", name: "neuterStatus", required: false, options: ["已绝育", "未绝育"] },
        { type: "input", label: "价格", name: "price", required: true, placeholder: "例如：2000元、赠送" },
        { type: "upload", label: "宠物照片", name: "images", required: true, tip: "请上传清晰的宠物照片，最多9张" },
        { type: "textarea", label: "宠物描述", name: "description", required: true, placeholder: "请详细描述宠物的性格、习惯、喂养要求等信息" }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", label: "联系人", name: "contactName", required: true },
        { type: "input", label: "联系电话", name: "contactPhone", required: true, inputType: "number" },
        { type: "input", label: "微信号", name: "wechat", required: false }
      ]
    }
  ]
};
const otherHomeServiceForm = {
  sections: [
    {
      label: "服务基本信息",
      fields: [
        { type: "input", label: "服务标题", name: "title", required: true, placeholder: "请简要描述您提供的服务" },
        { type: "input", label: "价格范围", name: "price", required: true, placeholder: "例如：50-100元/次" },
        { type: "input", label: "服务时间", name: "serviceTime", required: true, placeholder: "例如：周一至周日 8:00-20:00" },
        { type: "location", label: "服务区域", name: "serviceArea", required: true },
        { type: "upload", label: "服务展示图片", name: "images", required: true, tip: "请上传清晰的服务相关图片，最多5张" },
        { type: "textarea", label: "服务详情", name: "description", required: true, placeholder: "请详细描述您提供的服务内容、服务流程等信息" }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", label: "联系人", name: "contactName", required: true },
        { type: "input", label: "联系电话", name: "contactPhone", required: true, inputType: "number" },
        { type: "input", label: "微信号", name: "wechat", required: false }
      ]
    }
  ]
};
const findServiceForm = {
  sections: [
    {
      label: "需求基本信息",
      fields: [
        { type: "input", label: "需求标题", name: "title", required: true, placeholder: "请简要描述您需要的服务" },
        { type: "input", label: "预算范围", name: "budget", required: true, placeholder: "例如：100-200元" },
        { type: "location", label: "服务地点", name: "serviceLocation", required: true },
        { type: "input", label: "期望时间", name: "expectedTime", required: true, placeholder: "例如：周末或工作日晚上" },
        { type: "textarea", label: "需求详情", name: "description", required: true, placeholder: "请详细描述您的需求，越详细越容易找到合适的服务" },
        { type: "upload", label: "相关图片", name: "images", required: false, tip: "可上传与需求相关的参考图片，最多5张" }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", label: "联系人", name: "contactName", required: true },
        { type: "input", label: "联系电话", name: "contactPhone", required: true, inputType: "number" },
        { type: "input", label: "微信号", name: "wechat", required: false }
      ]
    }
  ]
};
const recruitmentForm = {
  sections: [
    {
      label: "职位信息",
      fields: [
        { type: "input", label: "职位名称", name: "jobTitle", required: true, placeholder: "请输入招聘的职位名称" },
        { type: "input", label: "公司名称", name: "companyName", required: true, placeholder: "请输入公司全称" },
        { type: "input", label: "薪资范围", name: "salary", required: true, placeholder: "例如：4000-6000元/月" },
        { type: "picker", label: "工作性质", name: "jobNature", required: true, options: ["全职", "兼职", "实习", "临时工"] },
        { type: "picker", label: "学历要求", name: "education", required: true, options: ["不限", "初中及以下", "高中/中专", "大专", "本科", "硕士", "博士及以上"] },
        { type: "input", label: "工作经验", name: "experience", required: true, placeholder: "例如：1-3年、经验不限" },
        { type: "location", label: "工作地点", name: "workLocation", required: true },
        { type: "textarea", label: "职位描述", name: "jobDescription", required: true, placeholder: "请详细描述职位职责、要求等信息" },
        { type: "textarea", label: "公司介绍", name: "companyDescription", required: false, placeholder: "请简要介绍公司情况、福利等" }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", label: "联系人", name: "contactName", required: true },
        { type: "input", label: "联系电话", name: "contactPhone", required: true, inputType: "number" },
        { type: "input", label: "电子邮箱", name: "email", required: false },
        { type: "input", label: "微信号", name: "wechat", required: false }
      ]
    }
  ]
};
const carServiceForm = {
  sections: [
    {
      label: "服务基本信息",
      fields: [
        { type: "input", label: "服务标题", name: "title", required: true, placeholder: "请简要描述您提供的汽车服务" },
        { type: "picker", label: "服务类型", name: "serviceType", required: true, options: ["洗车美容", "维修保养", "汽车改装", "汽车救援", "驾驶培训", "年检代办", "其他服务"] },
        { type: "input", label: "价格范围", name: "price", required: true, placeholder: "例如：洗车30元起，保养200元起" },
        { type: "input", label: "服务时间", name: "serviceTime", required: true, placeholder: "例如：周一至周日 8:00-20:00" },
        { type: "location", label: "服务地点", name: "serviceLocation", required: true },
        { type: "upload", label: "服务展示图片", name: "images", required: true, tip: "请上传清晰的服务相关图片或店面照片，最多5张" },
        { type: "textarea", label: "服务详情", name: "description", required: true, placeholder: "请详细描述您提供的汽车服务内容、服务流程、技术水平等信息" }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", label: "联系人", name: "contactName", required: true },
        { type: "input", label: "联系电话", name: "contactPhone", required: true, inputType: "number" },
        { type: "input", label: "微信号", name: "wechat", required: false }
      ]
    }
  ]
};
const secondHandForm = {
  sections: [
    {
      label: "商品信息",
      fields: [
        { type: "input", label: "标题", name: "title", required: true, placeholder: "请简要描述您的商品" },
        { type: "picker", label: "商品类别", name: "category", required: true, options: ["手机数码", "家用电器", "电脑办公", "家具家居", "服装鞋帽", "母婴用品", "运动户外", "图书音像", "其他物品"] },
        { type: "input", label: "品牌型号", name: "brand", required: false, placeholder: "例如：iPhone 13、华为P50" },
        { type: "input", label: "新旧程度", name: "condition", required: true, placeholder: "例如：9成新、全新未拆封" },
        { type: "input", label: "使用时长", name: "usageTime", required: false, placeholder: "例如：使用3个月、1年" },
        { type: "input", label: "原价", name: "originalPrice", required: false, placeholder: "购买时的价格", suffix: "元" },
        { type: "input", label: "售价", name: "price", required: true, placeholder: "请输入出售价格", suffix: "元" },
        { type: "location", label: "交易地点", name: "location", required: true },
        { type: "picker", label: "交易方式", name: "tradeMethod", required: true, options: ["线下自提", "快递邮寄", "同城配送", "均可"] },
        { type: "upload", label: "商品照片", name: "images", required: true, tip: "请上传清晰的商品照片，包括外观和细节，最多9张" },
        { type: "textarea", label: "商品描述", name: "description", required: true, placeholder: "请详细描述商品的功能、配置、包装、瑕疵等情况" }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", label: "联系人", name: "contactName", required: true },
        { type: "input", label: "联系电话", name: "contactPhone", required: true, inputType: "number" },
        { type: "input", label: "微信号", name: "wechat", required: false }
      ]
    }
  ]
};
const carpoolForm = {
  sections: [
    {
      label: "拼车信息",
      fields: [
        { type: "input", label: "标题", name: "title", required: true, placeholder: "请简要描述您的拼车需求" },
        { type: "picker", label: "拼车类型", name: "carpoolType", required: true, options: ["人找车", "车找人", "货找车", "车找货"] },
        { type: "location", label: "出发地", name: "startLocation", required: true },
        { type: "location", label: "目的地", name: "endLocation", required: true },
        { type: "input", label: "出发时间", name: "departureTime", required: true, placeholder: "例如：6月1日上午10点" },
        { type: "input", label: "座位数/人数", name: "seats", required: false, placeholder: "例如：3个座位、需要2人" },
        { type: "input", label: "车型信息", name: "carInfo", required: false, placeholder: "例如：大众朗逸、白色轿车" },
        { type: "input", label: "费用说明", name: "price", required: true, placeholder: "例如：AA分担、每人50元" },
        { type: "textarea", label: "其他说明", name: "description", required: false, placeholder: "请补充其他关于拼车的要求或说明" }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", label: "联系人", name: "contactName", required: true },
        { type: "input", label: "联系电话", name: "contactPhone", required: true, inputType: "number" },
        { type: "input", label: "微信号", name: "wechat", required: false }
      ]
    }
  ]
};
const jobWantedForm = {
  sections: [
    {
      label: "个人信息",
      fields: [
        { type: "input", label: "姓名", name: "name", required: true },
        { type: "picker", label: "性别", name: "gender", required: true, options: ["男", "女"] },
        { type: "input", label: "年龄", name: "age", required: true, inputType: "number" },
        { type: "picker", label: "学历", name: "education", required: true, options: ["初中及以下", "高中/中专", "大专", "本科", "硕士", "博士及以上"] },
        { type: "input", label: "工作经验", name: "experience", required: true, placeholder: "例如：3年销售经验" }
      ]
    },
    {
      label: "求职意向",
      fields: [
        { type: "input", label: "期望职位", name: "desiredPosition", required: true },
        { type: "input", label: "期望薪资", name: "expectedSalary", required: true, placeholder: "例如：5000-8000元/月" },
        { type: "picker", label: "工作性质", name: "jobNature", required: true, options: ["全职", "兼职", "实习", "临时工"] },
        { type: "location", label: "期望地点", name: "desiredLocation", required: false },
        { type: "textarea", label: "个人简介", name: "introduction", required: true, placeholder: "请简要介绍您的工作经历、专业技能等" },
        { type: "upload", label: "个人照片", name: "images", required: false, tip: "可上传个人照片或作品，最多3张" }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", label: "联系电话", name: "contactPhone", required: true, inputType: "number" },
        { type: "input", label: "微信号", name: "wechat", required: false }
      ]
    }
  ]
};
const houseRentForm = {
  sections: [
    {
      label: "房源信息",
      fields: [
        { type: "input", label: "标题", name: "title", required: true, placeholder: "请简要描述房源特点" },
        { type: "picker", label: "房源类型", name: "houseType", required: true, options: ["整租", "合租", "短租"] },
        { type: "input", label: "房屋户型", name: "layout", required: true, placeholder: "例如：2室1厅1卫" },
        { type: "input", label: "面积", name: "area", required: true, placeholder: "请输入房屋面积", suffix: "㎡" },
        { type: "input", label: "楼层", name: "floor", required: true, placeholder: "例如：6/18层" },
        { type: "input", label: "朝向", name: "orientation", required: false, placeholder: "例如：南北通透" },
        { type: "input", label: "小区名称", name: "community", required: true },
        { type: "location", label: "房屋地址", name: "location", required: true },
        { type: "input", label: "租金", name: "price", required: true, placeholder: "请输入月租金", suffix: "元/月" },
        { type: "tags", label: "房屋配置", name: "facilities", required: false, options: ["床", "衣柜", "空调", "冰箱", "洗衣机", "电视", "热水器", "宽带", "天然气", "暖气", "卫生间", "厨房", "阳台"], maxCount: 10 },
        { type: "upload", label: "房屋照片", name: "images", required: true, tip: "请上传清晰的房屋照片，最多9张" },
        { type: "textarea", label: "房源描述", name: "description", required: true, placeholder: "请详细描述房源情况、交通、周边配套等信息" }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", label: "联系人", name: "contactName", required: true },
        { type: "input", label: "联系电话", name: "contactPhone", required: true, inputType: "number" },
        { type: "input", label: "微信号", name: "wechat", required: false }
      ]
    }
  ]
};
const houseSellForm = {
  sections: [
    {
      label: "房源信息",
      fields: [
        { type: "input", label: "标题", name: "title", required: true, placeholder: "请简要描述房源特点" },
        { type: "input", label: "房屋户型", name: "layout", required: true, placeholder: "例如：3室2厅2卫" },
        { type: "input", label: "面积", name: "area", required: true, placeholder: "请输入房屋面积", suffix: "㎡" },
        { type: "input", label: "楼层", name: "floor", required: true, placeholder: "例如：8/25层" },
        { type: "input", label: "朝向", name: "orientation", required: false, placeholder: "例如：南北通透" },
        { type: "input", label: "装修情况", name: "decoration", required: true, placeholder: "例如：精装修、简装、毛坯" },
        { type: "input", label: "建造年代", name: "buildYear", required: false, placeholder: "例如：2015年建" },
        { type: "input", label: "小区名称", name: "community", required: true },
        { type: "location", label: "房屋地址", name: "location", required: true },
        { type: "input", label: "售价", name: "price", required: true, placeholder: "请输入总价", suffix: "万元" },
        { type: "input", label: "单价", name: "unitPrice", required: false, placeholder: "每平米单价", suffix: "元/㎡" },
        { type: "picker", label: "房屋性质", name: "propertyType", required: true, options: ["商品房", "经济适用房", "回迁房", "公寓", "别墅", "商住两用"] },
        { type: "picker", label: "产权年限", name: "ownership", required: false, options: ["70年", "50年", "40年", "永久"] },
        { type: "tags", label: "房屋特色", name: "features", required: false, options: ["学区房", "地铁房", "电梯房", "南北通透", "采光好", "河景房", "公园周边", "带花园", "带车位", "低总价"], maxCount: 6 },
        { type: "upload", label: "房屋照片", name: "images", required: true, tip: "请上传清晰的房屋照片，最多9张" },
        { type: "textarea", label: "房源描述", name: "description", required: true, placeholder: "请详细描述房源情况、交通、周边配套等信息" }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", label: "联系人", name: "contactName", required: true },
        { type: "input", label: "联系电话", name: "contactPhone", required: true, inputType: "number" },
        { type: "input", label: "微信号", name: "wechat", required: false }
      ]
    }
  ]
};
const usedCarForm = {
  sections: [
    {
      label: "车辆信息",
      fields: [
        { type: "input", label: "标题", name: "title", required: true, placeholder: "请简要描述您的车辆" },
        { type: "input", label: "品牌车型", name: "carModel", required: true, placeholder: "例如：大众朗逸、本田雅阁" },
        { type: "input", label: "上牌时间", name: "registrationTime", required: true, placeholder: "例如：2018年5月" },
        { type: "input", label: "行驶里程", name: "mileage", required: true, placeholder: "请输入行驶里程", suffix: "万公里" },
        { type: "input", label: "排量", name: "displacement", required: false, placeholder: "例如：1.6L、2.0T" },
        { type: "picker", label: "变速箱", name: "transmission", required: false, options: ["手动", "自动", "手自一体", "CVT", "双离合"] },
        { type: "picker", label: "排放标准", name: "emissionStandard", required: false, options: ["国五", "国六", "欧五", "欧六"] },
        { type: "picker", label: "车辆类型", name: "carType", required: true, options: ["轿车", "SUV", "跑车", "MPV", "面包车", "皮卡", "货车"] },
        { type: "input", label: "车身颜色", name: "color", required: false, placeholder: "例如：白色、黑色" },
        { type: "picker", label: "过户次数", name: "transferCount", required: false, options: ["0次(新车)", "1次", "2次", "3次及以上"] },
        { type: "input", label: "年检到期", name: "inspectionExpiry", required: false, placeholder: "例如：2023年6月" },
        { type: "input", label: "保险到期", name: "insuranceExpiry", required: false, placeholder: "例如：2023年8月" },
        { type: "input", label: "售价", name: "price", required: true, placeholder: "请输入售价", suffix: "万元" },
        { type: "upload", label: "车辆照片", name: "images", required: true, tip: "请上传清晰的车辆内外照片，至少3张，最多9张" },
        { type: "textarea", label: "车况描述", name: "description", required: true, placeholder: "请详细描述车辆的使用情况、维修保养记录、车况等信息" }
      ]
    },
    {
      label: "联系方式",
      fields: [
        { type: "input", label: "联系人", name: "contactName", required: true },
        { type: "input", label: "联系电话", name: "contactPhone", required: true, inputType: "number" },
        { type: "input", label: "微信号", name: "wechat", required: false }
      ]
    }
  ]
};
const serviceFormConfig = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  beautyServiceForm,
  businessTransferForm,
  carServiceForm,
  carpoolForm,
  datingForm,
  defaultForm,
  educationForm,
  findServiceForm,
  generalHomeServiceForm,
  homeCleaningForm,
  houseRentForm,
  houseSellForm,
  installationForm,
  jobWantedForm,
  locksmithForm,
  merchantActivityForm,
  movingForm,
  otherHomeServiceForm,
  otherServiceForm,
  petForm,
  petServiceForm,
  plumbingForm,
  recruitmentForm,
  repairForm,
  secondHandForm,
  tutorForm,
  usedCarForm
}, Symbol.toStringTag, { value: "Module" }));
exports.businessTransferForm = businessTransferForm;
exports.carServiceForm = carServiceForm;
exports.carpoolForm = carpoolForm;
exports.datingForm = datingForm;
exports.defaultForm = defaultForm;
exports.educationForm = educationForm;
exports.findServiceForm = findServiceForm;
exports.houseRentForm = houseRentForm;
exports.houseSellForm = houseSellForm;
exports.jobWantedForm = jobWantedForm;
exports.merchantActivityForm = merchantActivityForm;
exports.otherHomeServiceForm = otherHomeServiceForm;
exports.petForm = petForm;
exports.recruitmentForm = recruitmentForm;
exports.secondHandForm = secondHandForm;
exports.serviceFormConfig = serviceFormConfig;
exports.usedCarForm = usedCarForm;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/service-form-config.js.map
