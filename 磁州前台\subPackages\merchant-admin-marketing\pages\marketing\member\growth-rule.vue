<template>
  <view class="growth-rule-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">成长值规则</text>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 成长值规则内容 -->
    <view class="rule-content">
      <!-- 成长值获取规则 -->
      <view class="section-card">
        <view class="section-title">成长值获取规则</view>
        
        <view class="rule-form">
          <view class="form-item">
            <text class="form-label">消费成长值比例</text>
            <view class="form-input-group">
              <input type="number" class="form-input" v-model="growthRules.consumptionRatio" />
              <text class="input-suffix">成长值/元</text>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">连续签到奖励</text>
            <view class="form-input-group">
              <input type="number" class="form-input" v-model="growthRules.checkInGrowth" />
              <text class="input-suffix">成长值/天</text>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">完善资料奖励</text>
            <view class="form-input-group">
              <input type="number" class="form-input" v-model="growthRules.profileGrowth" />
              <text class="input-suffix">成长值</text>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">首次购买奖励</text>
            <view class="form-input-group">
              <input type="number" class="form-input" v-model="growthRules.firstPurchaseGrowth" />
              <text class="input-suffix">成长值</text>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">邀请新用户奖励</text>
            <view class="form-input-group">
              <input type="number" class="form-input" v-model="growthRules.inviteGrowth" />
              <text class="input-suffix">成长值/人</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 成长值等级对应 -->
      <view class="section-card">
        <view class="section-title">成长值等级对应</view>
        
        <view class="level-list">
          <view class="level-item" v-for="(level, index) in growthLevels" :key="index">
            <view class="level-header">
              <text class="level-name">{{level.name}}</text>
              <text class="level-growth">{{level.minGrowth}}-{{level.maxGrowth}}成长值</text>
            </view>
            <view class="level-benefits">
              <text class="benefit-item" v-for="(benefit, bIndex) in level.benefits" :key="bIndex">{{benefit}}</text>
            </view>
            <view class="level-actions">
              <text class="action-btn edit" @click="editLevel(level)">编辑</text>
              <text class="action-btn delete" v-if="index > 0" @click="deleteLevel(level)">删除</text>
            </view>
          </view>
        </view>
        
        <button class="add-btn" @click="addLevel">添加成长值等级</button>
      </view>
      
      <!-- 成长值规则说明 -->
      <view class="section-card">
        <view class="section-title">成长值规则说明</view>
        
        <view class="form-item">
          <text class="form-label">规则说明</text>
          <textarea class="form-textarea" v-model="growthRules.description" placeholder="请输入成长值规则说明" />
        </view>
        
        <view class="form-item switch-item">
          <text class="form-label">成长值可视化</text>
          <switch :checked="growthRules.isVisible" @change="toggleVisibility" color="#4A00E0" />
        </view>
        
        <view class="form-item switch-item">
          <text class="form-label">等级保护机制</text>
          <switch :checked="growthRules.levelProtection" @change="toggleLevelProtection" color="#4A00E0" />
        </view>
        
        <view class="form-item" v-if="growthRules.levelProtection">
          <text class="form-label">保护期限</text>
          <view class="form-input-group">
            <input type="number" class="form-input" v-model="growthRules.protectionDays" />
            <text class="input-suffix">天</text>
          </view>
        </view>
      </view>
      
      <!-- 成长值任务 -->
      <view class="section-card">
        <view class="section-title">成长值任务</view>
        
        <view class="task-list">
          <view class="task-item" v-for="(task, index) in growthTasks" :key="index">
            <view class="task-info">
              <text class="task-name">{{task.name}}</text>
              <text class="task-reward">奖励: {{task.reward}}成长值</text>
            </view>
            <view class="task-status">
              <switch :checked="task.enabled" @change="(e) => toggleTask(task, e)" color="#4A00E0" />
            </view>
          </view>
        </view>
        
        <button class="add-btn" @click="addTask">添加成长值任务</button>
      </view>
    </view>
    
    <!-- 保存按钮 -->
    <view class="bottom-bar">
      <button class="save-btn" @click="saveRules">保存规则</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 成长值规则
      growthRules: {
        consumptionRatio: 5,
        checkInGrowth: 2,
        profileGrowth: 50,
        firstPurchaseGrowth: 100,
        inviteGrowth: 30,
        
        description: '成长值是会员等级的重要依据，通过消费、签到、完成任务等方式获取成长值，成长值越高，会员等级越高，享受的权益越多。',
        isVisible: true,
        levelProtection: true,
        protectionDays: 30
      },
      
      // 成长值等级
      growthLevels: [
        {
          id: 1,
          name: '普通会员',
          minGrowth: 0,
          maxGrowth: 999,
          benefits: ['基础会员权益']
        },
        {
          id: 2,
          name: '银卡会员',
          minGrowth: 1000,
          maxGrowth: 4999,
          benefits: ['9.8折优惠', '生日礼包']
        },
        {
          id: 3,
          name: '金卡会员',
          minGrowth: 5000,
          maxGrowth: 19999,
          benefits: ['9.5折优惠', '生日礼包', '专属客服']
        },
        {
          id: 4,
          name: '钻石会员',
          minGrowth: 20000,
          maxGrowth: 99999,
          benefits: ['9折优惠', '生日礼包', '专属客服', '免费配送']
        }
      ],
      
      // 成长值任务
      growthTasks: [
        {
          id: 1,
          name: '连续签到7天',
          reward: 50,
          enabled: true
        },
        {
          id: 2,
          name: '完善个人资料',
          reward: 50,
          enabled: true
        },
        {
          id: 3,
          name: '绑定手机号',
          reward: 30,
          enabled: true
        },
        {
          id: 4,
          name: '首次购买',
          reward: 100,
          enabled: true
        },
        {
          id: 5,
          name: '邀请好友注册',
          reward: 30,
          enabled: true
        }
      ]
    };
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    
    toggleVisibility(e) {
      this.growthRules.isVisible = e.detail.value;
    },
    
    toggleLevelProtection(e) {
      this.growthRules.levelProtection = e.detail.value;
    },
    
    editLevel(level) {
      uni.showToast({
        title: '编辑等级功能开发中',
        icon: 'none'
      });
    },
    
    deleteLevel(level) {
      uni.showModal({
        title: '删除确认',
        content: `确定要删除"${level.name}"等级吗？`,
        success: (res) => {
          if (res.confirm) {
            const index = this.growthLevels.findIndex(item => item.id === level.id);
            if (index !== -1) {
              this.growthLevels.splice(index, 1);
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              });
            }
          }
        }
      });
    },
    
    addLevel() {
      uni.showToast({
        title: '添加等级功能开发中',
        icon: 'none'
      });
    },
    
    toggleTask(task, e) {
      const index = this.growthTasks.findIndex(item => item.id === task.id);
      if (index !== -1) {
        this.growthTasks[index].enabled = e.detail.value;
      }
      
      uni.showToast({
        title: e.detail.value ? `${task.name}已启用` : `${task.name}已禁用`,
        icon: 'none'
      });
    },
    
    addTask() {
      uni.showToast({
        title: '添加任务功能开发中',
        icon: 'none'
      });
    },
    
    saveRules() {
      uni.showLoading({
        title: '保存中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '成长值规则保存成功',
          icon: 'success'
        });
      }, 1000);
    }
  }
}
</script>

<style>
/* 成长值规则页面样式开始 */
.growth-rule-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 100rpx;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #8E2DE2, #4A00E0);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(74, 0, 224, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
}

/* 规则内容样式 */
.rule-content {
  padding: 20rpx;
}

.section-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 表单样式 */
.form-item {
  margin-bottom: 20rpx;
}

.form-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.form-input-group {
  display: flex;
  align-items: center;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  overflow: hidden;
}

.form-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.input-suffix {
  padding: 0 20rpx;
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  height: 80rpx;
  line-height: 80rpx;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 成长值等级样式 */
.level-list {
  margin-bottom: 20rpx;
}

.level-item {
  background: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
}

.level-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.level-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.level-growth {
  font-size: 24rpx;
  color: #4A00E0;
  background: rgba(74, 0, 224, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.level-benefits {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-bottom: 15rpx;
}

.benefit-item {
  font-size: 24rpx;
  color: #666;
  background: #f0f0f0;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.level-actions {
  display: flex;
  gap: 15rpx;
  justify-content: flex-end;
}

.action-btn {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
}

.action-btn.edit {
  background: rgba(74, 0, 224, 0.1);
  color: #4A00E0;
}

.action-btn.delete {
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}

/* 任务列表样式 */
.task-list {
  margin-bottom: 20rpx;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.task-item:last-child {
  border-bottom: none;
}

.task-info {
  flex: 1;
}

.task-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.task-reward {
  font-size: 24rpx;
  color: #FF9500;
}

/* 添加按钮 */
.add-btn {
  background: #4A00E0;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  margin-top: 20rpx;
}

/* 底部保存栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.save-btn {
  background: #4A00E0;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  width: 100%;
}
/* 成长值规则页面样式结束 */
</style> 