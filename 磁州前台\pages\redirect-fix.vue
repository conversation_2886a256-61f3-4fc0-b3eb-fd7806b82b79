<template>
  <view class="redirect-container">
    <text>加载中...</text>
  </view>
</template>

<script>
export default {
  onLoad(options) {
    // 获取所有传递过来的参数
    console.log('接收到的跳转参数:', JSON.stringify(options));
    
    const params = Object.keys(options)
      .map(key => `${key}=${encodeURIComponent(options[key])}`)
      .join('&');
    
    // 获取页面类型
    const pageType = options.pageType || 'info-detail';
    
    // 直接跳转，不使用setTimeout
    // 尝试使用原始的详情页路径
    let url = `/pages/publish/${pageType}?${params}`;
    console.log('尝试跳转到原始页面:', url);
    
    uni.navigateTo({
      url: url,
      success: () => {
        console.log('跳转成功');
      },
      fail: (err) => {
        console.error('跳转原始页面失败', err);
        
        // 返回首页并显示错误提示
        uni.showToast({
          title: '页面跳转失败，请稍后重试',
          icon: 'none',
          duration: 1000
        });
      }
    });
  }
}
</script>

<style>
.redirect-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}
</style> 