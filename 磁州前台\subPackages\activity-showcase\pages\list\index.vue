<template>
  <view class="activities-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M19 12H5M12 19l-7-7 7-7" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        <view class="navbar-title">活动列表</view>
        <view class="navbar-right">
          <view class="search-btn" @click="navigateTo('/subPackages/activity-showcase/pages/search/index?type=activities')">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <circle cx="11" cy="11" r="8" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
              <path d="M21 21l-4.35-4.35" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
      </view>
    </view>

    <!-- 搜索和筛选区域 -->
    <view class="search-filter-bar">
      <view class="search-box" @click="navigateTo('/subPackages/activity-showcase/pages/search/index?type=activities')">
        <svg class="search-icon" viewBox="0 0 24 24" width="20" height="20">
          <circle cx="11" cy="11" r="8" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
          <path d="M21 21l-4.35-4.35" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>
        <text class="search-placeholder">搜索活动名称、类型</text>
      </view>
      
      <view class="filter-options">
        <view 
          class="filter-option"
          :class="{ active: currentFilter === 'latest' }"
          @click="setFilter('latest')"
        >
          <text>最新</text>
        </view>
        
        <view 
          class="filter-option"
          :class="{ active: currentFilter === 'popular' }"
          @click="setFilter('popular')"
        >
          <text>热门</text>
        </view>
        
        <view 
          class="filter-option"
          :class="{ active: currentFilter === 'nearby' }"
          @click="setFilter('nearby')"
        >
          <text>附近</text>
        </view>
        
        <view 
          class="filter-option"
          @click="showFilter"
        >
          <text>筛选</text>
          <svg class="filter-icon" viewBox="0 0 24 24" width="16" height="16">
            <path d="M22 3H2l8 9.46V19l4 2v-8.54L22 3z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
      </view>
    </view>

    <!-- 活动类型标签栏 -->
    <scroll-view class="category-scroll" scroll-x>
      <view class="category-list">
        <view 
          v-for="(category, index) in activityCategories" 
          :key="index"
          class="category-item"
          :class="{ active: currentCategory === index }"
          @click="switchCategory(index)"
        >
          <text>{{ category.name }}</text>
        </view>
      </view>
    </scroll-view>

    <!-- 活动列表区域 -->
    <scroll-view 
      class="activities-scroll" 
      scroll-y 
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="loadMore"
    >
      <view class="activities-list">
        <view 
          v-for="activity in getFilteredActivities()" 
          :key="activity.id"
          class="activity-card"
          @click="viewActivityDetail(activity)"
        >
          <!-- 活动图片 -->
          <image :src="activity.image" class="activity-image" mode="aspectFill"></image>
          
          <!-- 活动状态标签 -->
          <view class="status-tag" :style="{
            background: getStatusBackground(activity.status)
          }">
            {{ getStatusText(activity.status) }}
          </view>
          
          <!-- 活动信息 -->
          <view class="activity-info">
            <text class="activity-title">{{ activity.title }}</text>
            
            <view class="activity-meta">
              <view class="meta-item">
                <svg class="meta-icon" viewBox="0 0 24 24" width="16" height="16">
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></rect>
                  <line x1="16" y1="2" x2="16" y2="6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                  <line x1="8" y1="2" x2="8" y2="6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                  <line x1="3" y1="10" x2="21" y2="10" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                </svg>
                <text class="meta-text">{{ activity.date }}</text>
              </view>
              
              <view class="meta-item">
                <svg class="meta-icon" viewBox="0 0 24 24" width="16" height="16">
                  <circle cx="12" cy="12" r="10" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                  <polyline points="12 6 12 12 16 14" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></polyline>
                </svg>
                <text class="meta-text">{{ activity.time }}</text>
              </view>
              
              <view class="meta-item">
                <svg class="meta-icon" viewBox="0 0 24 24" width="16" height="16">
                  <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0118 0z" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                  <circle cx="12" cy="10" r="3" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                </svg>
                <text class="meta-text">{{ activity.location }}</text>
              </view>
            </view>
            
            <view class="activity-bottom">
              <view class="price-participants">
                <view class="activity-price" v-if="activity.price > 0">
                  <text class="price-symbol">¥</text>
                  <text class="price-value">{{ activity.price.toFixed(2) }}</text>
                  <text class="price-original" v-if="activity.originalPrice">¥{{ activity.originalPrice.toFixed(2) }}</text>
                </view>
                <text class="activity-free" v-else>免费</text>
                
                <view class="participants">
                  <view class="avatar-group">
                    <image 
                      v-for="(avatar, avatarIndex) in activity.participants.slice(0, 3)" 
                      :key="avatarIndex"
                      :src="avatar"
                      class="participant-avatar"
                    ></image>
                  </view>
                  <text class="participant-count">{{ activity.participants.length }}人参与</text>
                </view>
              </view>
              
              <view 
                class="action-btn"
                :style="{
                  background: getActionBtnBackground(activity.status)
                }"
              >
                {{ getActionBtnText(activity.status) }}
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多提示 -->
      <view class="loading-more" v-if="isLoadingMore">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
    </scroll-view>

    <!-- 筛选弹窗 -->
    <uni-popup ref="filterPopup" type="bottom">
      <view class="filter-popup">
        <view class="filter-header">
          <text class="filter-title">筛选</text>
          <view class="filter-close" @click="closeFilter">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <line x1="18" y1="6" x2="6" y2="18" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
              <line x1="6" y1="6" x2="18" y2="18" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
            </svg>
          </view>
        </view>
        
        <view class="filter-content">
          <!-- 活动状态筛选 -->
          <view class="filter-section">
            <text class="section-title">活动状态</text>
            <view class="filter-options">
              <view 
                v-for="(option, index) in statusOptions" 
                :key="index"
                class="filter-option"
                :class="{ active: selectedStatus === index }"
                @click="selectStatus(index)"
              >
                {{ option.label }}
              </view>
            </view>
          </view>
          
          <!-- 活动时间筛选 -->
          <view class="filter-section">
            <text class="section-title">活动时间</text>
            <view class="filter-options">
              <view 
                v-for="(option, index) in timeOptions" 
                :key="index"
                class="filter-option"
                :class="{ active: selectedTimeOption === index }"
                @click="selectTimeOption(index)"
              >
                {{ option.label }}
              </view>
            </view>
          </view>
          
          <!-- 价格筛选 -->
          <view class="filter-section">
            <text class="section-title">价格范围</text>
            <view class="filter-options">
              <view 
                v-for="(option, index) in priceOptions" 
                :key="index"
                class="filter-option"
                :class="{ active: selectedPriceOption === index }"
                @click="selectPriceOption(index)"
              >
                {{ option.label }}
              </view>
            </view>
          </view>
          
          <!-- 特色服务筛选 -->
          <view class="filter-section">
            <text class="section-title">特色服务</text>
            <view class="filter-options">
              <view 
                v-for="(option, index) in featureOptions" 
                :key="index"
                class="filter-option"
                :class="{ active: selectedFeatures.includes(index) }"
                @click="toggleFeature(index)"
              >
                {{ option.label }}
              </view>
            </view>
          </view>
        </view>
        
        <view class="filter-footer">
          <view class="filter-reset" @click="resetFilters">
            <text>重置</text>
          </view>
          <view class="filter-apply" @click="applyFilter">
            <text>确定</text>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>
<script setup>
import { ref, computed, onMounted } from 'vue';

// 页面状态
const currentCategory = ref(0);
const currentFilter = ref('latest');
const isRefreshing = ref(false);
const isLoadingMore = ref(false);
const activitiesList = ref([]);
const filterPopup = ref(null);

// 筛选选项
const selectedStatus = ref(0);
const selectedTimeOption = ref(0);
const selectedPriceOption = ref(0);
const selectedFeatures = ref([]);

// 活动分类
const activityCategories = [
  { name: '全部', value: 'all' },
  { name: '文化活动', value: 'culture' },
  { name: '体育赛事', value: 'sports' },
  { name: '亲子活动', value: 'family' },
  { name: '公益活动', value: 'charity' },
  { name: '户外拓展', value: 'outdoor' },
  { name: '音乐演出', value: 'music' },
  { name: '展览讲座', value: 'exhibition' }
];

// 状态选项
const statusOptions = [
  { label: '全部', value: 'all' },
  { label: '未开始', value: 'upcoming' },
  { label: '进行中', value: 'ongoing' },
  { label: '已结束', value: 'ended' }
];

// 时间选项
const timeOptions = [
  { label: '全部时间', value: 'all' },
  { label: '今天', value: 'today' },
  { label: '明天', value: 'tomorrow' },
  { label: '本周', value: 'this_week' },
  { label: '本月', value: 'this_month' }
];

// 价格选项
const priceOptions = [
  { label: '全部价格', value: 'all' },
  { label: '免费', value: 'free' },
  { label: '¥1-50', value: '1-50' },
  { label: '¥51-100', value: '51-100' },
  { label: '¥100以上', value: '100+' }
];

// 特色服务选项
const featureOptions = [
  { label: '提供餐饮', value: 'food' },
  { label: '亲子友好', value: 'family_friendly' },
  { label: '无障碍设施', value: 'accessibility' },
  { label: '免费停车', value: 'free_parking' },
  { label: '提供证书', value: 'certificate' }
];

// 模拟数据
const mockActivities = [
  {
    id: '1001',
    title: '磁州文化节',
    image: '/static/demo/activity1.jpg',
    status: 'upcoming',
    date: '2024-06-15',
    time: '09:00-18:00',
    location: '磁州文化广场',
    price: 0,
    originalPrice: 0,
    category: 'culture',
    participants: [
      '/static/demo/avatar1.png',
      '/static/demo/avatar2.png',
      '/static/demo/avatar3.png',
      '/static/demo/avatar4.png',
      '/static/demo/avatar5.png'
    ],
    features: ['food', 'family_friendly', 'free_parking']
  },
  {
    id: '1002',
    title: '亲子户外拓展活动',
    image: '/static/demo/activity2.jpg',
    status: 'ongoing',
    date: '2024-05-28',
    time: '14:00-17:00',
    location: '磁州森林公园',
    price: 128,
    originalPrice: 168,
    category: 'family',
    participants: [
      '/static/demo/avatar1.png',
      '/static/demo/avatar3.png',
      '/static/demo/avatar5.png'
    ],
    features: ['family_friendly', 'free_parking']
  },
  {
    id: '1003',
    title: '社区篮球赛',
    image: '/static/demo/activity3.jpg',
    status: 'upcoming',
    date: '2024-06-05',
    time: '10:00-12:00',
    location: '磁州体育中心',
    price: 50,
    originalPrice: 80,
    category: 'sports',
    participants: [
      '/static/demo/avatar2.png',
      '/static/demo/avatar4.png',
      '/static/demo/avatar5.png',
      '/static/demo/avatar1.png'
    ],
    features: ['food', 'free_parking', 'certificate']
  },
  {
    id: '1004',
    title: '传统文化体验课',
    image: '/static/demo/activity4.jpg',
    status: 'ended',
    date: '2024-05-15',
    time: '15:00-17:00',
    location: '磁州文化馆',
    price: 88,
    originalPrice: 120,
    category: 'culture',
    participants: [
      '/static/demo/avatar3.png',
      '/static/demo/avatar1.png'
    ],
    features: ['family_friendly', 'accessibility', 'certificate']
  },
  {
    id: '1005',
    title: '环保公益行动',
    image: '/static/demo/activity5.jpg',
    status: 'upcoming',
    date: '2024-06-10',
    time: '09:00-12:00',
    location: '磁州河畔',
    price: 0,
    originalPrice: 0,
    category: 'charity',
    participants: [
      '/static/demo/avatar1.png',
      '/static/demo/avatar2.png',
      '/static/demo/avatar3.png',
      '/static/demo/avatar4.png',
      '/static/demo/avatar5.png',
      '/static/demo/avatar1.png',
      '/static/demo/avatar2.png'
    ],
    features: ['food', 'certificate']
  }
];

// 生命周期
onMounted(() => {
  loadActivities();
});

// 方法
const loadActivities = () => {
  // 模拟加载数据
  activitiesList.value = mockActivities;
};

const getFilteredActivities = () => {
  let result = [...activitiesList.value];
  
  // 应用分类筛选
  if (currentCategory.value !== 0) {
    const category = activityCategories[currentCategory.value].value;
    result = result.filter(activity => activity.category === category);
  }
  
  // 应用排序
  switch (currentFilter.value) {
    case 'latest':
      result.sort((a, b) => new Date(b.date) - new Date(a.date));
      break;
    case 'popular':
      result.sort((a, b) => b.participants.length - a.participants.length);
      break;
    case 'nearby':
      // 假设这里有基于位置的排序逻辑
      break;
  }
  
  return result;
};

const switchCategory = (index) => {
  currentCategory.value = index;
};

const setFilter = (filter) => {
  currentFilter.value = filter;
};

const onRefresh = () => {
  isRefreshing.value = true;
  setTimeout(() => {
    loadActivities();
    isRefreshing.value = false;
  }, 1000);
};

const loadMore = () => {
  // 模拟加载更多
  if (activitiesList.value.length >= 10) return;
  
  isLoadingMore.value = true;
  setTimeout(() => {
    // 模拟添加更多数据
    const newActivities = [
      {
        id: '1006',
        title: '磁州音乐节',
        image: '/static/demo/activity6.jpg',
        status: 'upcoming',
        date: '2024-06-20',
        time: '19:00-22:00',
        location: '磁州广场',
        price: 120,
        originalPrice: 180,
        category: 'music',
        participants: [
          '/static/demo/avatar1.png',
          '/static/demo/avatar2.png',
          '/static/demo/avatar3.png',
          '/static/demo/avatar4.png'
        ],
        features: ['food', 'free_parking']
      },
      {
        id: '1007',
        title: '陶艺展览',
        image: '/static/demo/activity7.jpg',
        status: 'upcoming',
        date: '2024-06-18',
        time: '10:00-17:00',
        location: '磁州艺术馆',
        price: 60,
        originalPrice: 80,
        category: 'exhibition',
        participants: [
          '/static/demo/avatar5.png',
          '/static/demo/avatar1.png',
          '/static/demo/avatar3.png'
        ],
        features: ['accessibility', 'family_friendly']
      }
    ];
    
    activitiesList.value = [...activitiesList.value, ...newActivities];
    isLoadingMore.value = false;
  }, 1500);
};

const getStatusText = (status) => {
  const statusMap = {
    'upcoming': '未开始',
    'ongoing': '进行中',
    'ended': '已结束'
  };
  return statusMap[status] || '未知状态';
};

const getStatusBackground = (status) => {
  const bgMap = {
    'upcoming': 'rgba(255, 149, 0, 0.8)',
    'ongoing': 'rgba(52, 199, 89, 0.8)',
    'ended': 'rgba(142, 142, 147, 0.8)'
  };
  return bgMap[status] || 'rgba(142, 142, 147, 0.8)';
};

const getActionBtnText = (status) => {
  const textMap = {
    'upcoming': '立即报名',
    'ongoing': '查看详情',
    'ended': '活动回顾'
  };
  return textMap[status] || '查看详情';
};

const getActionBtnBackground = (status) => {
  const bgMap = {
    'upcoming': 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',
    'ongoing': 'linear-gradient(135deg, #34C759 0%, #7ED321 100%)',
    'ended': 'linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)'
  };
  return bgMap[status] || 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)';
};

const viewActivityDetail = (activity) => {
  navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${activity.id}`);
};

const showFilter = () => {
  filterPopup.value.open();
};

const closeFilter = () => {
  filterPopup.value.close();
};

const selectStatus = (index) => {
  selectedStatus.value = index;
};

const selectTimeOption = (index) => {
  selectedTimeOption.value = index;
};

const selectPriceOption = (index) => {
  selectedPriceOption.value = index;
};

const toggleFeature = (index) => {
  const position = selectedFeatures.value.indexOf(index);
  if (position !== -1) {
    selectedFeatures.value.splice(position, 1);
  } else {
    selectedFeatures.value.push(index);
  }
};

const resetFilters = () => {
  selectedStatus.value = 0;
  selectedTimeOption.value = 0;
  selectedPriceOption.value = 0;
  selectedFeatures.value = [];
};

const applyFilter = () => {
  // 应用筛选
  console.log('应用筛选', {
    status: statusOptions[selectedStatus.value].value,
    time: timeOptions[selectedTimeOption.value].value,
    price: priceOptions[selectedPriceOption.value].value,
    features: selectedFeatures.value.map(index => featureOptions[index].value)
  });
  
  // 模拟筛选结果
  uni.showToast({
    title: '筛选已应用',
    icon: 'success'
  });
  
  closeFilter();
};

const goBack = () => {
  uni.navigateBack();
};

const navigateTo = (url) => {
  uni.navigateTo({ url });
};
</script>

<style scoped>
.activities-container {
  min-height: 100vh;
  background-color: #F5F5F5;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
}

.navbar-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
}

.navbar-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 90rpx;
  padding: var(--status-bar-height) 30rpx 0;
}

.back-btn, .search-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #FFFFFF;
}

.navbar-right {
  display: flex;
  align-items: center;
}

/* 搜索和筛选栏样式 */
.search-filter-bar {
  display: flex;
  flex-direction: column;
  background: #FFFFFF;
  padding: 20rpx;
  margin-top: calc(var(--status-bar-height) + 90rpx);
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.search-box {
  display: flex;
  align-items: center;
  height: 70rpx;
  background: #F5F5F5;
  border-radius: 35rpx;
  padding: 0 30rpx;
  margin-bottom: 20rpx;
}

.search-icon {
  margin-right: 10rpx;
}

.search-placeholder {
  font-size: 28rpx;
  color: #999999;
}

.filter-options {
  display: flex;
  justify-content: space-around;
}

.filter-option {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666666;
  padding: 10rpx 0;
}

.filter-option.active {
  color: #FF3B69;
  font-weight: 500;
}

.filter-icon {
  margin-left: 6rpx;
}

/* 分类标签栏样式 */
.category-scroll {
  background: #FFFFFF;
  padding: 20rpx 0;
  white-space: nowrap;
  border-bottom: 1rpx solid #EEEEEE;
}

.category-list {
  display: flex;
  padding: 0 20rpx;
}

.category-item {
  display: inline-block;
  padding: 10rpx 30rpx;
  margin-right: 20rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  color: #666666;
  background: #F5F5F5;
}

.category-item.active {
  background: rgba(255, 59, 105, 0.1);
  color: #FF3B69;
  border: 1rpx solid rgba(255, 59, 105, 0.3);
}

/* 活动列表样式 */
.activities-scroll {
  height: calc(100vh - var(--status-bar-height) - 90rpx - 180rpx - 80rpx);
}

.activities-list {
  padding: 20rpx;
}

.activity-card {
  background: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
  position: relative;
}

.activity-image {
  width: 100%;
  height: 300rpx;
  object-fit: cover;
}

.status-tag {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #FFFFFF;
}

.activity-info {
  padding: 20rpx 30rpx;
}

.activity-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.activity-meta {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  margin-bottom: 10rpx;
}

.meta-icon {
  margin-right: 6rpx;
}

.meta-text {
  font-size: 24rpx;
  color: #999999;
}

.activity-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-participants {
  display: flex;
  flex-direction: column;
}

.activity-price {
  display: flex;
  align-items: baseline;
  margin-bottom: 10rpx;
}

.price-symbol {
  font-size: 24rpx;
  color: #FF3B69;
  margin-right: 2rpx;
}

.price-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #FF3B69;
}

.price-original {
  font-size: 24rpx;
  color: #999999;
  text-decoration: line-through;
  margin-left: 10rpx;
}

.activity-free {
  font-size: 28rpx;
  color: #34C759;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.participants {
  display: flex;
  align-items: center;
}

.avatar-group {
  display: flex;
  margin-right: 10rpx;
}

.participant-avatar {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #FFFFFF;
  margin-left: -10rpx;
}

.participant-avatar:first-child {
  margin-left: 0;
}

.participant-count {
  font-size: 24rpx;
  color: #999999;
}

.action-btn {
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  color: #FFFFFF;
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
}

/* 加载更多样式 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #EEEEEE;
  border-top-color: #FF3B69;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10rpx;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 28rpx;
  color: #999999;
}

/* 筛选弹窗样式 */
.filter-popup {
  background: #FFFFFF;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
  padding: 30rpx;
  max-height: 70vh;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.filter-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.filter-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-content {
  max-height: calc(70vh - 180rpx);
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
}

.filter-option {
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #666666;
  background: #F5F5F5;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.filter-option.active {
  background: rgba(255, 59, 105, 0.1);
  color: #FF3B69;
  border: 1rpx solid rgba(255, 59, 105, 0.3);
}

.filter-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #F0F0F0;
}

.filter-reset, .filter-apply {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.filter-reset {
  background: #F5F5F5;
  color: #666666;
  margin-right: 20rpx;
}

.filter-apply {
  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
  color: #FFFFFF;
  box-shadow: 0 4rpx 8rpx rgba(255, 59, 105, 0.2);
}
</style>