"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  __name: "list",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const serviceType = common_vendor.ref("");
    const serviceTitle = common_vendor.ref("服务列表");
    const currentTab = common_vendor.ref(0);
    const currentSubTab = common_vendor.ref(0);
    const sortBy = common_vendor.ref("default");
    const isRefreshing = common_vendor.ref(false);
    const hasMore = common_vendor.ref(true);
    const page = common_vendor.ref(1);
    const limit = common_vendor.ref(10);
    const serviceList = common_vendor.ref([]);
    const showSubCategories = common_vendor.ref(false);
    const subCategories = common_vendor.ref([]);
    const showSubSubCategories = common_vendor.ref(false);
    const subSubCategories = common_vendor.ref([]);
    const showSearchBox = common_vendor.ref(false);
    const searchKeyword = common_vendor.ref("");
    const selectedArea = common_vendor.ref("全部区域");
    const areaList = common_vendor.ref(["全部区域", "城区", "磁州镇", "讲武城镇", "岳城镇", "观台镇", "白土镇", "黄沙镇"]);
    const showAreaFilter = common_vendor.ref(false);
    const showSortFilter = common_vendor.ref(false);
    const sortList = common_vendor.ref(["最新", "最热", "附近"]);
    const areaDropdownTop = common_vendor.ref(44);
    const sortDropdownTop = common_vendor.ref(44);
    common_vendor.ref(null);
    common_vendor.ref(null);
    const setServiceTitle = (type) => {
      const titleMap = {
        "home": "到家服务",
        "find": "寻找服务",
        "business": "生意转让",
        "job": "招聘信息",
        "resume": "求职信息",
        "house_rent": "房屋出租",
        "house_sell": "房屋出售",
        "second_car": "二手车辆",
        "pet": "宠物信息",
        "dating": "婚恋交友",
        "merchant_activity": "商家活动",
        "car": "车辆服务",
        "second_hand": "二手闲置",
        "carpool": "磁州拼车",
        "education": "教育培训",
        "other": "其他服务"
      };
      serviceTitle.value = titleMap[type] || "服务列表";
    };
    const switchTab = (index) => {
      if (currentTab.value !== index) {
        currentTab.value = index;
        const selectedType = subCategories.value[index].type;
        serviceType.value = selectedType;
        setServiceTitle(selectedType);
        loadSecondLevelCategories(selectedType);
        page.value = 1;
        serviceList.value = [];
        hasMore.value = true;
        loadServiceList();
      }
    };
    const switchSubTab = (index) => {
      if (currentSubTab.value !== index) {
        currentSubTab.value = index;
        page.value = 1;
        serviceList.value = [];
        hasMore.value = true;
        loadServiceList();
      }
    };
    const toggleSort = (sort) => {
      if (sort === "default") {
        showSortFilter.value = !showSortFilter.value;
        showAreaFilter.value = false;
        if (showSortFilter.value) {
          common_vendor.nextTick$1(() => {
            const query = common_vendor.index.createSelectorQuery();
            query.select(".filter-container").boundingClientRect((container) => {
              if (container) {
                sortDropdownTop.value = container.height + container.top;
              }
            }).exec();
          });
        }
      } else {
        sortBy.value = sort;
        page.value = 1;
        serviceList.value = [];
        hasMore.value = true;
        loadServiceList();
      }
    };
    const selectSort = (sort) => {
      const previousSort = sortBy.value;
      if (sort === "最新") {
        sortBy.value = "default";
      } else {
        sortBy.value = sort;
      }
      showSortFilter.value = false;
      if (previousSort !== sortBy.value) {
        page.value = 1;
        serviceList.value = [];
        hasMore.value = true;
        loadServiceList();
      }
    };
    const loadServiceList = () => {
      common_vendor.index.showLoading({
        title: "加载中"
      });
      const subCategoryType = showSubSubCategories.value && currentSubTab.value > 0 ? subSubCategories.value[currentSubTab.value].type : "all";
      setTimeout(() => {
        try {
          const allPublishedInfo = common_vendor.index.getStorageSync("publishedInfo") || [];
          let filteredList = [];
          switch (serviceType.value) {
            case "home":
              if (subCategoryType === "all") {
                filteredList = allPublishedInfo.filter(
                  (item) => item.type === "home_service"
                );
              } else {
                filteredList = allPublishedInfo.filter(
                  (item) => item.type === "home_service" && item.serviceType === subCategoryType
                );
              }
              break;
            case "find":
              if (subCategoryType === "all") {
                filteredList = allPublishedInfo.filter(
                  (item) => item.type === "find_service"
                );
              } else {
                filteredList = allPublishedInfo.filter(
                  (item) => item.type === "find_service" && item.findCategory === subCategoryType
                );
              }
              break;
            case "business":
              if (subCategoryType === "all") {
                filteredList = allPublishedInfo.filter(
                  (item) => item.type === "business_transfer"
                );
              } else {
                filteredList = allPublishedInfo.filter(
                  (item) => item.type === "business_transfer" && item.businessCategory === subCategoryType
                );
              }
              break;
            case "job":
              if (subCategoryType === "all") {
                filteredList = allPublishedInfo.filter(
                  (item) => item.type === "hire"
                );
              } else {
                filteredList = allPublishedInfo.filter(
                  (item) => item.type === "hire" && item.jobCategory === subCategoryType
                );
              }
              break;
            case "resume":
              if (subCategoryType === "all") {
                filteredList = allPublishedInfo.filter(
                  (item) => item.type === "job_wanted"
                );
              } else {
                filteredList = allPublishedInfo.filter(
                  (item) => item.type === "job_wanted" && item.resumeCategory === subCategoryType
                );
              }
              break;
            case "car":
              if (subCategoryType === "all") {
                filteredList = allPublishedInfo.filter(
                  (item) => item.type === "car_service"
                );
              } else {
                filteredList = allPublishedInfo.filter(
                  (item) => item.type === "car_service" && item.carServiceType === subCategoryType
                );
              }
              break;
            case "second_hand":
              if (subCategoryType === "all") {
                filteredList = allPublishedInfo.filter(
                  (item) => item.type === "second_hand"
                );
              } else {
                filteredList = allPublishedInfo.filter(
                  (item) => item.type === "second_hand" && item.secondHandCategory === subCategoryType
                );
              }
              break;
            case "carpool":
              if (subCategoryType === "all") {
                filteredList = allPublishedInfo.filter(
                  (item) => item.type === "carpool"
                );
              } else {
                filteredList = allPublishedInfo.filter(
                  (item) => item.type === "carpool" && item.carpoolType === subCategoryType
                );
              }
              break;
            case "education":
              if (subCategoryType === "all") {
                filteredList = allPublishedInfo.filter(
                  (item) => item.type === "education"
                );
              } else {
                filteredList = allPublishedInfo.filter(
                  (item) => item.type === "education" && item.educationType === subCategoryType
                );
              }
              break;
            case "dating":
              if (subCategoryType === "all") {
                filteredList = allPublishedInfo.filter(
                  (item) => item.type === "dating"
                );
              } else {
                filteredList = allPublishedInfo.filter(
                  (item) => item.type === "dating" && item.datingCategory === subCategoryType
                );
              }
              break;
            case "merchant_activity":
              if (subCategoryType === "all") {
                filteredList = allPublishedInfo.filter(
                  (item) => item.type === "merchant_activity"
                );
              } else {
                filteredList = allPublishedInfo.filter(
                  (item) => item.type === "merchant_activity" && item.activityCategory === subCategoryType
                );
              }
              break;
            case "other":
              if (subCategoryType === "all") {
                filteredList = allPublishedInfo.filter(
                  (item) => item.type === "other_service"
                );
              } else {
                filteredList = allPublishedInfo.filter(
                  (item) => item.type === "other_service" && item.otherServiceType === subCategoryType
                );
              }
              break;
            case "house_rent":
              filteredList = allPublishedInfo.filter(
                (item) => item.type === "house_rent"
              );
              break;
            case "house_sell":
              filteredList = allPublishedInfo.filter(
                (item) => item.type === "house_sell"
              );
              break;
            case "second_car":
              filteredList = allPublishedInfo.filter(
                (item) => item.type === "used_car"
              );
              break;
            case "pet":
              filteredList = allPublishedInfo.filter(
                (item) => item.type === "pet"
              );
              break;
            default:
              filteredList = allPublishedInfo;
          }
          if (sortBy.value === "default") {
            filteredList.sort((a, b) => new Date(b.publishTime || 0) - new Date(a.publishTime || 0));
          } else if (sortBy.value === "最热") {
            filteredList.sort((a, b) => (b.views || 0) - (a.views || 0));
          } else if (sortBy.value === "附近") {
            filteredList.sort((a, b) => {
              if (a.distance && b.distance) {
                return (a.distance || 999999) - (b.distance || 999999);
              }
              if (a.distance)
                return -1;
              if (b.distance)
                return 1;
              return 0;
            });
          }
          const start = (page.value - 1) * limit.value;
          const end = page.value * limit.value;
          const pageData = filteredList.slice(start, end);
          if (pageData.length > 0) {
            serviceList.value = [...serviceList.value, ...pageData];
            hasMore.value = filteredList.length > serviceList.value.length;
          } else {
            hasMore.value = false;
          }
          if (serviceList.value.length === 0) {
            addSampleData();
          }
        } catch (e) {
          common_vendor.index.__f__("error", "at subPackages/service/pages/list.vue:585", "加载服务列表失败", e);
          addSampleData();
        }
        common_vendor.index.hideLoading();
        if (isRefreshing.value) {
          isRefreshing.value = false;
        }
      }, 800);
    };
    const addSampleData = () => {
      switch (serviceType.value) {
        case "home":
          serviceList.value.push({
            id: "sample-home-1",
            category: "家政服务",
            content: "专业家政保洁，开荒保洁，日常保洁，家电清洗，擦玻璃等服务",
            time: "2024-05-01 10:30",
            views: 128,
            images: ["/static/images/banner/banner-1.png"],
            type: "home_service",
            pageType: "home-service-detail"
          });
          serviceList.value.push({
            id: "sample-home-2",
            category: "维修改造",
            content: "专业水电安装维修，管道疏通，灯具安装，墙面翻新",
            time: "2024-05-01 09:45",
            views: 156,
            images: ["/static/images/banner/banner-2.png"],
            type: "home_service",
            pageType: "home-service-detail"
          });
          serviceList.value.push({
            id: "sample-home-3",
            category: "开锁换锁",
            content: "专业开锁换锁，汽车开锁，保险柜开锁，安装智能锁",
            time: "2024-05-01 08:20",
            views: 89,
            type: "home_service",
            pageType: "home-service-detail"
          });
          break;
        case "business":
          serviceList.value.push({
            id: "sample-business-1",
            category: "餐饮店铺",
            subcategory: "中餐店",
            content: "黄金地段餐饮店整体转让，客流稳定，接手即可盈利",
            time: "2024-05-01 14:30",
            views: 198,
            images: ["/static/images/banner/banner-3.png"],
            type: "business_transfer",
            pageType: "business-transfer-detail"
          });
          serviceList.value.push({
            id: "sample-business-2",
            category: "零售店铺",
            subcategory: "服装店",
            content: "县城中心奶茶店转让，设备齐全，接手即可营业",
            time: "2024-05-01 15:20",
            views: 156,
            type: "business_transfer",
            pageType: "business-transfer-detail"
          });
          serviceList.value.push({
            id: "sample-business-3",
            category: "美容美发",
            content: "美容美发店转让，位置好，客源稳定，接手即可营业",
            time: "2024-05-01 16:10",
            views: 142,
            type: "business_transfer",
            pageType: "business-transfer-detail"
          });
          break;
        case "resume":
          serviceList.value.push({
            id: "sample-resume-1",
            category: "求职信息",
            content: "会计专业毕业，有初级会计证书，两年工作经验，求职会计相关工作",
            time: "2024-05-01 15:30",
            views: 95,
            type: "job_wanted",
            pageType: "job-seeking-detail"
          });
          serviceList.value.push({
            id: "sample-resume-2",
            category: "求职信息",
            content: "有多年销售经验，善于沟通，能吃苦耐劳，求职销售相关工作",
            time: "2024-05-01 14:45",
            views: 112,
            type: "job_wanted",
            pageType: "job-seeking-detail"
          });
          break;
        case "find":
          serviceList.value.push({
            id: "sample-find-1",
            category: "维修服务",
            subcategory: "水电维修",
            content: "找水电工维修厨房水管漏水，今天急修",
            time: "2024-05-01 11:30",
            views: 75,
            type: "find_service",
            pageType: "find-service-detail"
          });
          serviceList.value.push({
            id: "sample-find-2",
            category: "家政服务",
            subcategory: "保洁服务",
            content: "寻找家政阿姨，每周定期打扫家庭卫生",
            time: "2024-05-01 10:45",
            views: 86,
            type: "find_service",
            pageType: "find-service-detail"
          });
          serviceList.value.push({
            id: "sample-find-3",
            category: "安装服务",
            subcategory: "家具安装",
            content: "寻找搬家服务，三室两厅搬家，本周六上午",
            time: "2024-05-01 09:15",
            views: 92,
            type: "find_service",
            pageType: "find-service-detail"
          });
          serviceList.value.push({
            id: "sample-find-4",
            category: "教育服务",
            content: "寻找高中数学家教，高三学生，一对一辅导",
            time: "2024-05-01 08:45",
            views: 68,
            type: "find_service",
            pageType: "find-service-detail"
          });
          break;
        case "dating":
          serviceList.value.push({
            id: "sample-dating-1",
            category: "男士征婚",
            content: "35岁，身高178cm，本科学历，公务员，诚心寻找一位温柔贤惠的女士共度余生",
            time: "2024-05-01 09:30",
            views: 156,
            type: "dating",
            pageType: "dating-detail"
          });
          serviceList.value.push({
            id: "sample-dating-2",
            category: "女士征婚",
            content: "32岁，身高165cm，硕士学历，教师，希望找一位成熟稳重、有责任心的男士",
            time: "2024-05-01 10:15",
            views: 188,
            type: "dating",
            pageType: "dating-detail"
          });
          serviceList.value.push({
            id: "sample-dating-3",
            category: "相亲活动",
            content: "本周六下午2点，城区文化广场举办大型相亲交友活动，单身男女免费参加",
            time: "2024-05-01 11:20",
            views: 235,
            images: ["/static/images/banner/banner-3.jpg"],
            type: "dating",
            pageType: "dating-detail"
          });
          break;
        case "merchant_activity":
          serviceList.value.push({
            id: "sample-merchant-1",
            category: "促销活动",
            content: "五一大促销，全场商品5折起，部分商品买一送一，活动时间5月1日至5月5日",
            time: "2024-05-01 08:30",
            views: 245,
            images: ["/static/images/banner/banner-1.png"],
            type: "merchant_activity",
            pageType: "merchant-activity-detail"
          });
          serviceList.value.push({
            id: "sample-merchant-2",
            category: "新店开业",
            content: "新店开业大酬宾，前100名顾客免费赠送精美礼品一份，消费满100元送50元代金券",
            time: "2024-05-01 09:45",
            views: 198,
            images: ["/static/images/banner/banner-2.png"],
            type: "merchant_activity",
            pageType: "merchant-activity-detail"
          });
          serviceList.value.push({
            id: "sample-merchant-3",
            category: "满减活动",
            content: "本周末满减活动，满100减30，满200减80，满300减150，多买多省",
            time: "2024-05-01 10:30",
            views: 176,
            type: "merchant_activity",
            pageType: "merchant-activity-detail"
          });
          break;
        case "job":
          serviceList.value.push({
            id: "sample-job-1",
            category: "销售",
            content: "招聘销售人员3名，底薪3000+提成，要求有销售经验",
            time: "2024-05-01 09:15",
            views: 210,
            type: "hire",
            pageType: "job-detail"
          });
          serviceList.value.push({
            id: "sample-job-2",
            category: "服务员",
            content: "餐厅招聘服务员2名，有经验优先，包吃住",
            time: "2024-05-01 11:30",
            views: 150,
            type: "hire",
            pageType: "job-detail"
          });
          serviceList.value.push({
            id: "sample-job-3",
            category: "司机",
            content: "招聘专职司机，C1驾照，有3年以上驾龄，底薪4500",
            time: "2024-05-01 08:45",
            views: 180,
            type: "hire",
            pageType: "job-detail"
          });
          break;
        case "car":
          serviceList.value.push({
            id: "sample-car-1",
            category: "汽车维修",
            content: "专业汽车维修，各种故障检测维修，价格合理",
            time: "2024-05-01 10:15",
            views: 95,
            type: "car_service",
            pageType: "car-service-detail"
          });
          serviceList.value.push({
            id: "sample-car-2",
            category: "汽车保养",
            content: "汽车保养套餐，包含机油机滤更换，全车检查等服务",
            time: "2024-05-01 09:30",
            views: 120,
            type: "car_service",
            pageType: "car-service-detail"
          });
          break;
        case "second_hand":
          serviceList.value.push({
            id: "sample-second-hand-1",
            category: "手机数码",
            content: "出售二手iPhone 13，128G，成色95新，有发票保修",
            time: "2024-05-01 14:20",
            views: 230,
            images: ["/static/images/banner/banner-3.png"],
            type: "second_hand",
            pageType: "info-detail"
          });
          serviceList.value.push({
            id: "sample-second-hand-2",
            category: "家具家居",
            content: "搬家出售九成新沙发一套，茶几，餐桌等家具",
            time: "2024-05-01 13:15",
            views: 185,
            type: "second_hand",
            pageType: "info-detail"
          });
          break;
        case "education":
          serviceList.value.push({
            id: "sample-education-1",
            category: "小学辅导",
            content: "小学一对一辅导，语数英全科，有丰富教学经验",
            time: "2024-05-01 16:30",
            views: 145,
            type: "education",
            pageType: "info-detail"
          });
          serviceList.value.push({
            id: "sample-education-2",
            category: "英语培训",
            content: "英语口语培训，外教一对一，提高口语能力",
            time: "2024-05-01 15:20",
            views: 168,
            type: "education",
            pageType: "info-detail"
          });
          break;
        case "carpool":
          serviceList.value.push({
            id: "sample-carpool-1",
            category: "上下班拼车",
            content: "工作日上下班拼车，磁州到市区，早7点发车，晚6点返回",
            time: "2024-05-01 18:30",
            views: 112,
            type: "carpool",
            pageType: "info-detail"
          });
          serviceList.value.push({
            id: "sample-carpool-2",
            category: "城际拼车",
            content: "周末磁州到石家庄拼车，周六早出发，周日晚返回",
            time: "2024-05-01 17:15",
            views: 135,
            type: "carpool",
            pageType: "info-detail"
          });
          break;
        case "house_rent":
          serviceList.value.push({
            id: "sample-house-rent-1",
            category: "整租",
            content: "城区两室一厅出租，精装修，家电齐全，拎包入住",
            time: "2024-05-01 14:30",
            views: 220,
            images: ["/static/images/banner/banner-2.png"],
            type: "house_rent",
            pageType: "info-detail"
          });
          serviceList.value.push({
            id: "sample-house-rent-2",
            category: "合租",
            content: "城区三室一厅合租，主卧带阳台，家电齐全，拎包入住",
            time: "2024-05-01 13:20",
            views: 175,
            type: "house_rent",
            pageType: "info-detail"
          });
          break;
        case "house_sell":
          serviceList.value.push({
            id: "sample-house-sell-1",
            category: "商品房",
            content: "城区新小区三室两厅出售，110平米，精装修，南北通透",
            time: "2024-05-01 15:30",
            views: 245,
            images: ["/static/images/banner/banner-3.png"],
            type: "house_sell",
            pageType: "info-detail"
          });
          serviceList.value.push({
            id: "sample-house-sell-2",
            category: "二手房",
            content: "老城区两室一厅出售，70平米，简装修，交通便利",
            time: "2024-05-01 14:15",
            views: 198,
            type: "house_sell",
            pageType: "info-detail"
          });
          break;
        case "second_car":
          serviceList.value.push({
            id: "sample-second-car-1",
            category: "轿车",
            content: "2020年大众朗逸，1.5L自动挡，行驶3万公里，车况良好",
            time: "2024-05-01 11:30",
            views: 210,
            images: ["/static/images/banner/banner-1.png"],
            type: "used_car",
            pageType: "info-detail"
          });
          serviceList.value.push({
            id: "sample-second-car-2",
            category: "SUV",
            content: "2019年本田CR-V，2.0L自动挡，行驶5万公里，车况良好",
            time: "2024-05-01 10:15",
            views: 185,
            type: "used_car",
            pageType: "info-detail"
          });
          break;
        case "pet":
          serviceList.value.push({
            id: "sample-pet-1",
            category: "狗狗",
            content: "出售纯种金毛幼犬，2个月大，已打疫苗，健康活泼",
            time: "2024-05-01 13:30",
            views: 165,
            images: ["/static/images/banner/banner-3.png"],
            type: "pet",
            pageType: "info-detail"
          });
          serviceList.value.push({
            id: "sample-pet-2",
            category: "猫咪",
            content: "出售英短蓝猫，3个月大，已打疫苗，粘人可爱",
            time: "2024-05-01 12:15",
            views: 145,
            type: "pet",
            pageType: "info-detail"
          });
          break;
        case "other":
          serviceList.value.push({
            id: "sample-other-1",
            category: "法律服务",
            content: "专业律师咨询服务，合同审核，法律纠纷解决",
            time: "2024-05-01 11:45",
            views: 78,
            type: "other_service",
            pageType: "info-detail"
          });
          serviceList.value.push({
            id: "sample-other-2",
            category: "设计服务",
            content: "平面设计，logo设计，海报设计，价格合理",
            time: "2024-05-01 10:50",
            views: 92,
            type: "other_service",
            pageType: "info-detail"
          });
          break;
        default:
          serviceList.value.push({
            id: "sample-default-1",
            category: "服务信息",
            content: "这是一条示例服务信息",
            time: "2024-05-01 12:00",
            views: 50,
            type: serviceType.value,
            pageType: "info-detail"
          });
      }
      hasMore.value = serviceList.value.length >= 5;
    };
    const onRefresh = () => {
      isRefreshing.value = true;
      page.value = 1;
      serviceList.value = [];
      hasMore.value = true;
      loadServiceList();
    };
    const loadMore = () => {
      if (hasMore.value) {
        page.value++;
        loadServiceList();
      }
    };
    const navigateToDetail = (item) => {
      let url = "";
      if (item.pageType) {
        if (item.type === "home_service" && item.serviceType) {
          url = `/pages/publish/${item.pageType}?id=${item.id}&type=${item.serviceType}`;
        } else {
          url = `/pages/publish/${item.pageType}?id=${item.id}`;
        }
      } else {
        switch (serviceType.value) {
          case "home":
            let subType = "home_cleaning";
            if (currentTab.value > 0 && subCategories.value[currentTab.value]) {
              subType = subCategories.value[currentTab.value].type;
            }
            url = `/pages/publish/home-service-detail?id=${item.id}&type=${item.serviceType || subType}`;
            break;
          case "find":
            url = `/pages/publish/find-service-detail?id=${item.id}`;
            break;
          case "business":
            url = `/pages/publish/business-transfer-detail?id=${item.id}`;
            break;
          case "job":
            url = `/pages/publish/job-detail?id=${item.id}`;
            break;
          case "resume":
            url = `/pages/publish/job-seeking-detail?id=${item.id}`;
            break;
          case "dating":
            url = `/pages/publish/dating-detail?id=${item.id}`;
            break;
          case "merchant_activity":
            url = `/pages/publish/merchant-activity-detail?id=${item.id}`;
            break;
          case "car":
            url = `/pages/publish/car-service-detail?id=${item.id}`;
            break;
          case "second_hand":
            url = `/pages/publish/second-hand-detail?id=${item.id}`;
            break;
          case "carpool":
            url = `/pages/publish/carpool-detail?id=${item.id}`;
            break;
          case "education":
            url = `/pages/publish/education-detail?id=${item.id}`;
            break;
          default:
            url = `/pages/publish/info-detail?id=${item.id}&type=${serviceType.value}`;
        }
      }
      common_vendor.index.__f__("log", "at subPackages/service/pages/list.vue:1159", "跳转到详情页：", url);
      common_vendor.index.navigateTo({
        url
      });
    };
    const navigateToPublish = () => {
      if (serviceType.value === "home") {
        common_vendor.index.navigateTo({
          url: "/pages/publish/service-category"
        });
      } else {
        const typeMap = {
          "find": "find_service",
          "business": "business_transfer",
          "job": "hire",
          "resume": "job_wanted",
          "house_rent": "house_rent",
          "house_sell": "house_sell",
          "second_car": "used_car",
          "pet": "pet",
          "dating": "dating",
          "merchant_activity": "merchant_activity",
          "car": "car_service",
          "second_hand": "second_hand",
          "carpool": "carpool",
          "education": "education",
          "other": "other_service"
        };
        const publishType = typeMap[serviceType.value] || "other_service";
        const publishName = serviceTitle.value;
        common_vendor.index.navigateTo({
          url: `/pages/publish/detail?type=${publishType}&name=${encodeURIComponent(publishName)}&categoryType=${publishType}&categoryName=${encodeURIComponent(publishName)}`
        });
      }
    };
    const navigateBack = () => {
      common_vendor.index.navigateBack();
    };
    const toggleAreaFilter = () => {
      showAreaFilter.value = !showAreaFilter.value;
      showSortFilter.value = false;
      if (showAreaFilter.value) {
        common_vendor.nextTick$1(() => {
          const query = common_vendor.index.createSelectorQuery();
          query.select(".filter-container").boundingClientRect((container) => {
            if (container) {
              areaDropdownTop.value = container.height + container.top;
            }
          }).exec();
        });
      }
    };
    const selectArea = (area) => {
      if (selectedArea.value !== area) {
        selectedArea.value = area;
        page.value = 1;
        serviceList.value = [];
        hasMore.value = true;
        loadServiceList();
      }
      showAreaFilter.value = false;
    };
    const closeAllFilters = () => {
      showAreaFilter.value = false;
      showSortFilter.value = false;
    };
    const applyKeywordSearch = () => {
      common_vendor.index.__f__("log", "at subPackages/service/pages/list.vue:1260", "应用关键词搜索：", searchKeyword.value);
    };
    const resetFilters = () => {
      showAreaFilter.value = false;
      showSortFilter.value = false;
      page.value = 1;
      serviceList.value = [];
      hasMore.value = true;
      loadServiceList();
    };
    common_vendor.onMounted(() => {
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight;
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      if (options.type) {
        serviceType.value = options.type;
        setServiceTitle(options.type);
        showSubCategories.value = true;
        subCategories.value = [
          { name: "到家服务", type: "home" },
          { name: "寻找服务", type: "find" },
          { name: "生意转让", type: "business" },
          { name: "招聘信息", type: "job" },
          { name: "求职信息", type: "resume" },
          { name: "房屋出租", type: "house_rent" },
          { name: "房屋出售", type: "house_sell" },
          { name: "二手车辆", type: "second_car" },
          { name: "宠物信息", type: "pet" },
          { name: "商家活动", type: "merchant_activity" },
          { name: "婚恋交友", type: "dating" },
          { name: "车辆服务", type: "car" },
          { name: "二手闲置", type: "second_hand" },
          { name: "磁州拼车", type: "carpool" },
          { name: "教育培训", type: "education" },
          { name: "其他服务", type: "other" }
        ];
        const currentTypeIndex = subCategories.value.findIndex((item) => item.type === options.type);
        if (currentTypeIndex > -1) {
          currentTab.value = currentTypeIndex;
        }
        loadSecondLevelCategories(options.type);
        switch (options.type) {
          case "home":
          case "find":
          case "business":
          case "job":
          case "resume":
          case "dating":
          case "merchant_activity":
            showSearchBox.value = true;
            break;
          default:
            showSearchBox.value = false;
        }
      }
      loadServiceList();
    });
    const loadSecondLevelCategories = (type) => {
      switch (type) {
        case "home":
          subSubCategories.value = [
            { name: "全部", type: "all" },
            { name: "家政服务", type: "home_cleaning" },
            { name: "维修改造", type: "repair" },
            { name: "上门安装", type: "installation" },
            { name: "开锁换锁", type: "locksmith" },
            { name: "搬家拉货", type: "moving" },
            { name: "上门美容", type: "beauty" },
            { name: "上门家教", type: "tutor" },
            { name: "宠物服务", type: "pet_service" },
            { name: "上门疏通", type: "plumbing" },
            { name: "其他类型", type: "other" }
          ];
          showSubSubCategories.value = true;
          break;
        case "find":
          subSubCategories.value = [
            { name: "全部服务", type: "all" },
            { name: "家政服务", type: "home_service" },
            { name: "维修服务", type: "repair" },
            { name: "安装服务", type: "installation" },
            { name: "搬家服务", type: "moving" },
            { name: "美容服务", type: "beauty" },
            { name: "教育服务", type: "education" },
            { name: "其他服务", type: "other" }
          ];
          showSubSubCategories.value = true;
          break;
        case "business":
          subSubCategories.value = [
            { name: "全部分类", type: "all" },
            { name: "餐饮店铺", type: "restaurant" },
            { name: "零售店铺", type: "retail" },
            { name: "美容美发", type: "beauty_salon" },
            { name: "服装店铺", type: "clothing" },
            { name: "便利超市", type: "convenience" },
            { name: "其他店铺", type: "other" }
          ];
          showSubSubCategories.value = true;
          break;
        case "job":
          subSubCategories.value = [
            { name: "全部分类", type: "all" },
            { name: "销售", type: "sales" },
            { name: "服务员", type: "waiter" },
            { name: "技工", type: "technician" },
            { name: "司机", type: "driver" },
            { name: "厨师", type: "chef" },
            { name: "会计", type: "accountant" },
            { name: "文员", type: "clerk" },
            { name: "保安", type: "security" },
            { name: "其他", type: "other" }
          ];
          showSubSubCategories.value = true;
          break;
        case "resume":
          subSubCategories.value = [
            { name: "全部分类", type: "all" },
            { name: "销售类", type: "sales" },
            { name: "技术类", type: "tech" },
            { name: "服务类", type: "service" },
            { name: "行政类", type: "admin" },
            { name: "教育类", type: "education" },
            { name: "其他类", type: "other" }
          ];
          showSubSubCategories.value = true;
          break;
        case "dating":
          subSubCategories.value = [
            { name: "全部分类", type: "all" },
            { name: "男士征婚", type: "male_dating" },
            { name: "女士征婚", type: "female_dating" },
            { name: "恋爱交友", type: "friendship" },
            { name: "相亲活动", type: "matchmaking" },
            { name: "婚恋平台", type: "dating_platform" },
            { name: "其他", type: "other" }
          ];
          showSubSubCategories.value = true;
          break;
        case "merchant_activity":
          subSubCategories.value = [
            { name: "全部分类", type: "all" },
            { name: "促销活动", type: "promotion" },
            { name: "新店开业", type: "opening" },
            { name: "优惠券", type: "coupon" },
            { name: "满减活动", type: "discount" },
            { name: "限时特价", type: "special_price" },
            { name: "积分活动", type: "points" },
            { name: "其他活动", type: "other" }
          ];
          showSubSubCategories.value = true;
          break;
        case "car":
          subSubCategories.value = [
            { name: "全部分类", type: "all" },
            { name: "汽车维修", type: "repair" },
            { name: "汽车保养", type: "maintenance" },
            { name: "汽车美容", type: "beauty" },
            { name: "汽车改装", type: "modification" },
            { name: "汽车救援", type: "rescue" },
            { name: "其他", type: "other" }
          ];
          showSubSubCategories.value = true;
          break;
        case "second_hand":
          subSubCategories.value = [
            { name: "全部分类", type: "all" },
            { name: "手机数码", type: "digital" },
            { name: "家用电器", type: "appliance" },
            { name: "家具家居", type: "furniture" },
            { name: "服装鞋帽", type: "clothing" },
            { name: "母婴用品", type: "baby" },
            { name: "运动户外", type: "sports" },
            { name: "图书音像", type: "books" },
            { name: "其他", type: "other" }
          ];
          showSubSubCategories.value = true;
          break;
        case "carpool":
          subSubCategories.value = [
            { name: "全部分类", type: "all" },
            { name: "上下班拼车", type: "commute" },
            { name: "城际拼车", type: "intercity" },
            { name: "回乡拼车", type: "hometown" },
            { name: "其他", type: "other" }
          ];
          showSubSubCategories.value = true;
          break;
        case "education":
          subSubCategories.value = [
            { name: "全部分类", type: "all" },
            { name: "学前教育", type: "preschool" },
            { name: "小学辅导", type: "primary" },
            { name: "中学辅导", type: "secondary" },
            { name: "高考辅导", type: "college_entrance" },
            { name: "英语培训", type: "english" },
            { name: "音乐培训", type: "music" },
            { name: "美术培训", type: "art" },
            { name: "体育培训", type: "sports" },
            { name: "职业技能", type: "vocational" },
            { name: "其他", type: "other" }
          ];
          showSubSubCategories.value = true;
          break;
        case "other":
          subSubCategories.value = [
            { name: "全部分类", type: "all" },
            { name: "法律服务", type: "legal" },
            { name: "金融服务", type: "financial" },
            { name: "设计服务", type: "design" },
            { name: "网络服务", type: "internet" },
            { name: "翻译服务", type: "translation" },
            { name: "婚庆服务", type: "wedding" },
            { name: "摄影服务", type: "photography" },
            { name: "其他", type: "other" }
          ];
          showSubSubCategories.value = true;
          break;
        default:
          subSubCategories.value = [];
          showSubSubCategories.value = false;
      }
      currentSubTab.value = 0;
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(navigateBack),
        b: common_vendor.t(serviceTitle.value),
        c: statusBarHeight.value + "px",
        d: showSearchBox.value
      }, showSearchBox.value ? common_vendor.e({
        e: common_assets._imports_0$10,
        f: common_vendor.o(applyKeywordSearch),
        g: searchKeyword.value,
        h: common_vendor.o(($event) => searchKeyword.value = $event.detail.value),
        i: searchKeyword.value
      }, searchKeyword.value ? {
        j: common_vendor.o(($event) => searchKeyword.value = "")
      } : {}) : {}, {
        k: showSubCategories.value
      }, showSubCategories.value ? {
        l: common_vendor.f(subCategories.value, (tab, index, i0) => {
          return {
            a: common_vendor.t(tab.name),
            b: index,
            c: currentTab.value === index ? 1 : "",
            d: common_vendor.o(($event) => switchTab(index), index)
          };
        })
      } : {}, {
        m: showSubSubCategories.value && subSubCategories.value.length > 0
      }, showSubSubCategories.value && subSubCategories.value.length > 0 ? {
        n: common_vendor.f(subSubCategories.value, (subTab, index, i0) => {
          return {
            a: common_vendor.t(subTab.name),
            b: index,
            c: currentSubTab.value === index ? 1 : "",
            d: common_vendor.o(($event) => switchSubTab(index), index)
          };
        })
      } : {}, {
        o: common_vendor.t(selectedArea.value),
        p: selectedArea.value !== "全部区域" ? 1 : "",
        q: showAreaFilter.value ? 1 : "",
        r: common_vendor.o(toggleAreaFilter),
        s: common_vendor.t(sortBy.value === "default" ? "最新" : sortBy.value),
        t: sortBy.value !== "default" ? 1 : "",
        v: showSortFilter.value ? 1 : "",
        w: common_vendor.o(($event) => toggleSort("default")),
        x: showAreaFilter.value
      }, showAreaFilter.value ? {
        y: common_vendor.f(areaList.value, (area, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(area),
            b: area === selectedArea.value
          }, area === selectedArea.value ? {} : {}, {
            c: index,
            d: area === selectedArea.value ? 1 : "",
            e: common_vendor.o(($event) => selectArea(area), index)
          });
        }),
        z: areaDropdownTop.value + "px"
      } : {}, {
        A: showSortFilter.value
      }, showSortFilter.value ? {
        B: common_vendor.f(sortList.value, (sort, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(sort),
            b: sortBy.value === "default" && sort === "最新" || sort === sortBy.value
          }, sortBy.value === "default" && sort === "最新" || sort === sortBy.value ? {} : {}, {
            c: index,
            d: sortBy.value === "default" && sort === "最新" || sort === sortBy.value ? 1 : "",
            e: common_vendor.o(($event) => selectSort(sort), index)
          });
        }),
        C: sortDropdownTop.value + "px"
      } : {}, {
        D: showAreaFilter.value || showSortFilter.value
      }, showAreaFilter.value || showSortFilter.value ? {
        E: common_vendor.o(closeAllFilters)
      } : {}, {
        F: serviceList.value.length > 0
      }, serviceList.value.length > 0 ? {
        G: common_vendor.f(serviceList.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.category),
            b: item.subcategory
          }, item.subcategory ? {
            c: common_vendor.t(item.subcategory)
          } : {}, {
            d: common_vendor.t(item.area || "全城"),
            e: common_vendor.t(item.content),
            f: item.price
          }, item.price ? {
            g: common_vendor.t(item.price)
          } : {}, {
            h: item.images && item.images.length > 0
          }, item.images && item.images.length > 0 ? {
            i: common_vendor.f(item.images.slice(0, 3), (img, imgIndex, i1) => {
              return {
                a: imgIndex,
                b: img
              };
            }),
            j: item.images.length === 1 ? 1 : ""
          } : {}, {
            k: common_vendor.t(item.views || 0),
            l: common_vendor.t(item.time),
            m: index,
            n: common_vendor.o(($event) => navigateToDetail(item), index)
          });
        })
      } : {
        H: common_assets._imports_1$3,
        I: common_vendor.o(resetFilters)
      }, {
        J: serviceList.value.length > 0 && hasMore.value
      }, serviceList.value.length > 0 && hasMore.value ? {} : {}, {
        K: serviceList.value.length > 0 && !hasMore.value
      }, serviceList.value.length > 0 && !hasMore.value ? {} : {}, {
        L: common_vendor.o(loadMore),
        M: isRefreshing.value,
        N: common_vendor.o(onRefresh),
        O: common_vendor.o(navigateToPublish),
        P: serviceType.value
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-09770677"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/service/pages/list.js.map
