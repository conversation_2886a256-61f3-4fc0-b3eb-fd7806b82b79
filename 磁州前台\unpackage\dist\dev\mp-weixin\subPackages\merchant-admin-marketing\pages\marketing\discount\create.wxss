/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.discount-create-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 80px;
  /* 为底部按钮留出空间 */
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FDEB71, #F8D800);
  color: #333;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(248, 216, 0, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #333;
  border-bottom: 2px solid #333;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  padding: 0 10px;
}
.save-btn {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

/* 表单样式 */
.create-form {
  padding: 15px;
}
.form-section {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.section-desc {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
.add-rule {
  font-size: 14px;
  color: #F8D800;
}
.form-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}
.form-item:last-child {
  border-bottom: none;
}
.item-label {
  width: 80px;
  font-size: 14px;
  color: #666;
}
.item-input {
  flex: 1;
  height: 24px;
  font-size: 14px;
  color: #333;
}
.input-count {
  font-size: 12px;
  color: #999;
  margin-left: 10px;
}
.date-picker {
  flex: 1;
  height: 36px;
  background: #f5f5f5;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
}
.date-text {
  font-size: 14px;
  color: #333;
}
.date-separator {
  margin: 0 10px;
  color: #999;
}
.status-switch {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.status-text {
  font-size: 14px;
  color: #333;
  margin-right: 10px;
}

/* 规则样式 */
.rule-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  background: #f9f9f9;
  border-radius: 8px;
  padding: 12px;
}
.rule-inputs {
  flex: 1;
  display: flex;
  align-items: center;
}
.rule-input-group {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 6px;
  padding: 8px 12px;
  border: 1px solid #eee;
}
.input-prefix {
  font-size: 14px;
  color: #666;
  margin-right: 5px;
}
.rule-input {
  width: 80px;
  font-size: 14px;
  color: #333;
  text-align: center;
}
.input-suffix {
  font-size: 14px;
  color: #666;
  margin-left: 5px;
}
.rule-separator {
  margin: 0 10px;
  color: #666;
  font-size: 14px;
}
.rule-delete {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
}
.delete-icon {
  font-size: 18px;
  color: #999;
}
.empty-rules {
  padding: 30px 0;
  display: flex;
  justify-content: center;
}
.empty-text {
  font-size: 14px;
  color: #999;
}

/* 使用设置样式 */
.item-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.item-value {
  font-size: 14px;
  color: #333;
  margin-right: 5px;
}
.item-arrow {
  font-size: 14px;
  color: #999;
}
.limit-input-group {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.limit-input {
  width: 60px;
  font-size: 14px;
  color: #333;
  text-align: right;
}
.textarea-item {
  flex-direction: column;
  align-items: flex-start;
}
.item-textarea {
  width: 100%;
  height: 100px;
  font-size: 14px;
  color: #333;
  background: #f9f9f9;
  border-radius: 8px;
  padding: 10px;
  margin-top: 10px;
  box-sizing: border-box;
}
.textarea-count {
  align-self: flex-end;
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

/* 分销设置样式 */
.form-section .distribution-setting {
  margin-top: 0;
  padding: 0;
}

/* 营销建议样式 */
.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}
.tip-item:last-child {
  margin-bottom: 0;
}
.tip-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  margin-top: 2px;
  color: #F8D800;
}
.tip-text {
  flex: 1;
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}

/* 活动推广样式 */
.form-section .marketing-promotion-container {
  margin-top: 10px;
}

/* 底部保存按钮样式 */
.bottom-save-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 15px;
  background: #fff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 90;
}
.save-button {
  width: 100%;
  height: 44px;
  background: linear-gradient(135deg, #FDEB71, #F8D800);
  color: #333;
  font-size: 16px;
  font-weight: 500;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}