<template>
  <view class="location-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">位置管理</text>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 表单区域 -->
    <view class="form-container">
      <!-- 门店地址 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">门店地址</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">省市区</text>
          <view class="region-picker" @click="showRegionPicker">
            <text class="picker-value">{{formData.province}} {{formData.city}} {{formData.district}}</text>
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">详细地址</text>
          <textarea class="address-input" v-model="formData.address" placeholder="请输入详细地址，如道路、门牌号、小区、楼栋号、单元室等" />
        </view>
        
        <view class="form-item">
          <text class="form-label">门店坐标</text>
          <view class="map-container">
            <image src="/static/images/shop/map-placeholder.jpg" mode="aspectFill" class="map-image"></image>
            <view class="map-marker">
              <svg viewBox="0 0 24 24" width="24" height="24" fill="#1677FF" stroke="none">
                <path d="M12 0c-5.522 0-10 4.395-10 9.815 0 5.505 4.375 9.268 10 14.185 5.625-4.917 10-8.68 10-14.185 0-5.42-4.478-9.815-10-9.815zm0 6c2.209 0 4 1.791 4 4s-1.791 4-4 4-4-1.791-4-4 1.791-4 4-4z"></path>
              </svg>
            </view>
            <view class="map-actions">
              <button class="map-button" @click="chooseLocation">重新定位</button>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 导航设置 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">导航设置</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">导航提示语</text>
          <input type="text" v-model="formData.navTips" placeholder="如：进门右转，电梯到3楼" />
        </view>
        
        <view class="form-item">
          <text class="form-label">支持的导航应用</text>
          <view class="nav-apps">
            <view 
              v-for="(app, index) in navApps" 
              :key="index"
              :class="['nav-app-item', {'active': formData.enabledNavApps.includes(app.id)}]"
              @click="toggleNavApp(app.id)">
              <image :src="app.icon" mode="aspectFit" class="app-icon"></image>
              <text class="app-name">{{app.name}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 服务范围 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">服务范围</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">服务半径</text>
          <view class="radius-slider">
            <slider 
              :value="formData.serviceRadius" 
              :min="1" 
              :max="20" 
              :step="0.5"
              show-value 
              @change="changeRadius" 
              activeColor="#1677FF" />
            <text class="radius-unit">公里</text>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">服务区域预览</text>
          <view class="map-container">
            <image src="/static/images/shop/service-area-map.jpg" mode="aspectFill" class="map-image"></image>
            <view class="service-circle"></view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">特殊服务区域</text>
          <view class="special-areas">
            <view class="area-item" v-for="(area, index) in formData.specialAreas" :key="index">
              <text class="area-name">{{area.name}}</text>
              <view class="area-actions">
                <text class="edit-btn" @click="editSpecialArea(index)">编辑</text>
                <text class="delete-btn" @click="deleteSpecialArea(index)">删除</text>
              </view>
            </view>
            <view class="add-area-btn" @click="addSpecialArea">
              <svg viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
              <text>添加特殊服务区域</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 营业状态 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">营业状态</text>
        </view>
        
        <view class="form-item">
          <view class="switch-item">
            <text>显示门店在线状态</text>
            <switch :checked="formData.showOnlineStatus" @change="toggleOnlineStatus" color="#1677FF" />
          </view>
        </view>
        
        <view class="form-item" v-if="formData.showOnlineStatus">
          <view class="switch-item">
            <text>当前营业状态</text>
            <switch :checked="formData.isOpen" @change="toggleOpenStatus" color="#1677FF" />
          </view>
          <text class="status-text">{{formData.isOpen ? '营业中' : '休息中'}}</text>
        </view>
      </view>
    </view>
    
    <!-- 底部保存按钮 -->
    <view class="bottom-bar">
      <button class="save-button" @click="saveLocation">保存</button>
    </view>
    
    <!-- 省市区选择器弹窗 -->
    <uni-popup ref="regionPicker" type="bottom">
      <view class="picker-container">
        <view class="picker-header">
          <text class="cancel-btn" @click="cancelRegionPicker">取消</text>
          <text class="picker-title">选择省市区</text>
          <text class="confirm-btn" @click="confirmRegionPicker">确定</text>
        </view>
        <picker-view :value="regionValue" @change="regionChange" class="picker-view">
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in provinces" :key="index">{{item}}</view>
          </picker-view-column>
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in cities" :key="index">{{item}}</view>
          </picker-view-column>
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in districts" :key="index">{{item}}</view>
          </picker-view-column>
        </picker-view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        province: '河北省',
        city: '邯郸市',
        district: '磁县',
        address: '磁州镇光明路28号',
        longitude: 114.374,
        latitude: 36.374,
        navTips: '进门右转，电梯到3楼',
        enabledNavApps: ['amap', 'baidu', 'tencent'],
        serviceRadius: 5,
        specialAreas: [
          { name: '磁县城区', radius: 10 },
          { name: '邯郸市区', radius: 15 }
        ],
        showOnlineStatus: true,
        isOpen: true
      },
      navApps: [
        { id: 'amap', name: '高德地图', icon: '/static/images/shop/amap.png' },
        { id: 'baidu', name: '百度地图', icon: '/static/images/shop/baidu.png' },
        { id: 'tencent', name: '腾讯地图', icon: '/static/images/shop/tencent.png' },
        { id: 'apple', name: '苹果地图', icon: '/static/images/shop/apple.png' }
      ],
      // 省市区数据
      provinces: ['河北省', '山东省', '河南省'],
      cities: ['邯郸市', '石家庄市', '保定市'],
      districts: ['磁县', '丛台区', '复兴区', '邯山区'],
      regionValue: [0, 0, 0],
      tempRegion: {
        province: '河北省',
        city: '邯郸市',
        district: '磁县'
      }
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showRegionPicker() {
      this.$refs.regionPicker.open();
    },
    regionChange(e) {
      const values = e.detail.value;
      this.regionValue = values;
      
      this.tempRegion = {
        province: this.provinces[values[0]],
        city: this.cities[values[1]],
        district: this.districts[values[2]]
      };
    },
    confirmRegionPicker() {
      this.formData.province = this.tempRegion.province;
      this.formData.city = this.tempRegion.city;
      this.formData.district = this.tempRegion.district;
      this.$refs.regionPicker.close();
    },
    cancelRegionPicker() {
      this.$refs.regionPicker.close();
    },
    chooseLocation() {
      uni.showToast({
        title: '地图选点功能开发中',
        icon: 'none'
      });
    },
    toggleNavApp(appId) {
      const index = this.formData.enabledNavApps.indexOf(appId);
      if (index > -1) {
        this.formData.enabledNavApps.splice(index, 1);
      } else {
        this.formData.enabledNavApps.push(appId);
      }
    },
    changeRadius(e) {
      this.formData.serviceRadius = e.detail.value;
    },
    addSpecialArea() {
      uni.showToast({
        title: '添加特殊区域功能开发中',
        icon: 'none'
      });
    },
    editSpecialArea(index) {
      uni.showToast({
        title: '编辑特殊区域功能开发中',
        icon: 'none'
      });
    },
    deleteSpecialArea(index) {
      uni.showModal({
        title: '提示',
        content: '确定要删除该特殊服务区域吗？',
        success: (res) => {
          if (res.confirm) {
            this.formData.specialAreas.splice(index, 1);
            uni.showToast({
              title: '已删除',
              icon: 'success'
            });
          }
        }
      });
    },
    toggleOnlineStatus(e) {
      this.formData.showOnlineStatus = e.detail.value;
    },
    toggleOpenStatus(e) {
      this.formData.isOpen = e.detail.value;
    },
    saveLocation() {
      // 表单验证
      if (!this.formData.address) {
        uni.showToast({
          title: '请输入详细地址',
          icon: 'none'
        });
        return;
      }
      
      // 模拟保存
      uni.showLoading({
        title: '保存中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        });
        
        // 返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }, 1000);
    }
  }
}
</script>

<style>
.location-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 100px;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
}

.form-container {
  padding: 16px;
}

.form-section {
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
}

.form-item {
  margin-bottom: 16px;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  display: block;
}

.region-picker {
  height: 44px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 0 12px;
  background-color: #f9f9f9;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.picker-value {
  font-size: 14px;
  color: #333;
}

.arrow-icon {
  width: 8px;
  height: 8px;
  border-top: 1px solid #999;
  border-right: 1px solid #999;
  transform: rotate(135deg);
}

.address-input {
  width: 100%;
  height: 80px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 12px;
  font-size: 14px;
  background-color: #f9f9f9;
}

input {
  width: 100%;
  height: 44px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
  background-color: #f9f9f9;
}

.map-container {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.map-image {
  width: 100%;
  height: 100%;
}

.map-marker {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.map-actions {
  position: absolute;
  bottom: 12px;
  right: 12px;
}

.map-button {
  height: 32px;
  padding: 0 12px;
  background-color: #fff;
  border-radius: 16px;
  font-size: 12px;
  color: #1677FF;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.nav-apps {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.nav-app-item {
  width: 72px;
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.5;
}

.nav-app-item.active {
  opacity: 1;
}

.app-icon {
  width: 40px;
  height: 40px;
  margin-bottom: 4px;
}

.app-name {
  font-size: 12px;
  color: #666;
}

.radius-slider {
  display: flex;
  align-items: center;
}

.radius-unit {
  font-size: 14px;
  color: #666;
  margin-left: 8px;
}

.service-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120px;
  height: 120px;
  border-radius: 60px;
  border: 2px solid #1677FF;
  background-color: rgba(22, 119, 255, 0.1);
  transform: translate(-50%, -50%);
}

.special-areas {
  
}

.area-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.area-item:last-child {
  border-bottom: none;
}

.area-name {
  font-size: 14px;
  color: #333;
}

.area-actions {
  display: flex;
  gap: 16px;
}

.edit-btn {
  font-size: 12px;
  color: #1677FF;
}

.delete-btn {
  font-size: 12px;
  color: #ff4d4f;
}

.add-area-btn {
  display: flex;
  align-items: center;
  color: #1677FF;
  font-size: 14px;
  margin-top: 12px;
}

.add-area-btn svg {
  margin-right: 4px;
}

.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.switch-item text {
  font-size: 14px;
  color: #333;
}

.status-text {
  font-size: 12px;
  color: #52c41a;
  margin-top: 8px;
  display: block;
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background-color: #fff;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.save-button {
  width: 100%;
  height: 44px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  color: #fff;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 省市区选择器样式 */
.picker-container {
  background-color: #fff;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.cancel-btn {
  font-size: 14px;
  color: #999;
}

.picker-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.confirm-btn {
  font-size: 14px;
  color: #1677FF;
}

.picker-view {
  height: 240px;
  width: 100%;
}

.picker-item {
  line-height: 40px;
  text-align: center;
  font-size: 14px;
}
</style> 