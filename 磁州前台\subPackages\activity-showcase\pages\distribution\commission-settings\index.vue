<template>
  <view class="commission-settings-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg" :style="{
        background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)'
      }"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="navigateBack">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M19 12H5M12 19l-7-7 7-7" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        <view class="navbar-title">佣金设置</view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view 
      class="content-scroll" 
      scroll-y="true"
      :scroll-anchoring="true"
      :enhanced="true"
      :bounces="true"
      :show-scrollbar="false"
    >
      <!-- 提示信息 -->
      <view class="tips-card" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: '#FFF9E6',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <view class="tips-header">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
            <circle cx="12" cy="12" r="10" stroke="#FF9500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
            <line x1="12" y1="8" x2="12" y2="12" stroke="#FF9500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
            <line x1="12" y1="16" x2="12.01" y2="16" stroke="#FF9500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
          </svg>
          <text class="tips-title">佣金设置说明</text>
        </view>
        <text class="tips-content">佣金设置将影响您的分销推广收益，请根据您的实际情况进行合理设置。不同等级的分销员可设置的佣金上限不同，请参考等级规则。</text>
      </view>
      
      <!-- 佣金比例设置 -->
      <view class="commission-card" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: '#FFFFFF',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <view class="card-title">佣金比例设置</view>
        
        <view class="commission-form">
          <view class="form-item">
            <text class="form-label">一级佣金比例</text>
            <view class="form-input">
              <input 
                type="digit" 
                v-model="commissionSettings.firstLevel" 
                placeholder="请输入" 
                :maxlength="5"
                @input="validateFirstLevel"
              />
              <text class="input-suffix">%</text>
            </view>
          </view>
          <text class="form-hint">当前等级可设置最高{{ maxCommissionRates.firstLevel }}%</text>
          
          <view class="form-item">
            <text class="form-label">二级佣金比例</text>
            <view class="form-input">
              <input 
                type="digit" 
                v-model="commissionSettings.secondLevel" 
                placeholder="请输入" 
                :maxlength="5"
                @input="validateSecondLevel"
              />
              <text class="input-suffix">%</text>
            </view>
          </view>
          <text class="form-hint">当前等级可设置最高{{ maxCommissionRates.secondLevel }}%</text>
          
          <view class="form-item" v-if="userInfo.level === '高级分销商'">
            <text class="form-label">团队佣金比例</text>
            <view class="form-input">
              <input 
                type="digit" 
                v-model="commissionSettings.teamLevel" 
                placeholder="请输入" 
                :maxlength="5"
                @input="validateTeamLevel"
              />
              <text class="input-suffix">%</text>
            </view>
          </view>
          <text class="form-hint" v-if="userInfo.level === '高级分销商'">当前等级可设置最高{{ maxCommissionRates.teamLevel }}%</text>
        </view>
      </view>
      
      <!-- 佣金设置预览 -->
      <view class="preview-card" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: '#FFFFFF',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <view class="card-title">佣金预览</view>
        
        <view class="preview-content">
          <view class="preview-item">
            <text class="preview-label">商品价格</text>
            <view class="preview-input">
              <input 
                type="digit" 
                v-model="previewPrice" 
                placeholder="请输入" 
                :maxlength="10"
              />
              <text class="input-suffix">元</text>
            </view>
          </view>
          
          <view class="preview-result">
            <view class="result-item">
              <text class="result-label">一级佣金</text>
              <text class="result-value">¥{{ calculateFirstLevelCommission }}</text>
            </view>
            <view class="result-item">
              <text class="result-label">二级佣金</text>
              <text class="result-value">¥{{ calculateSecondLevelCommission }}</text>
            </view>
            <view class="result-item" v-if="userInfo.level === '高级分销商'">
              <text class="result-label">团队佣金</text>
              <text class="result-value">¥{{ calculateTeamLevelCommission }}</text>
            </view>
            <view class="result-item total">
              <text class="result-label">预计总收益</text>
              <text class="result-value">¥{{ calculateTotalCommission }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 佣金结算设置 -->
      <view class="settlement-card" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: '#FFFFFF',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <view class="card-title">结算设置</view>
        
        <view class="settlement-form">
          <view class="form-item">
            <text class="form-label">结算周期</text>
            <view class="form-select" @click="showSettlementCyclePicker">
              <text>{{ settlementCycleOptions[commissionSettings.settlementCycle] }}</text>
              <svg class="arrow-icon" viewBox="0 0 24 24" width="16" height="16">
                <path d="M6 9l6 6 6-6" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">最低提现金额</text>
            <view class="form-input">
              <input 
                type="digit" 
                v-model="commissionSettings.minWithdraw" 
                placeholder="请输入" 
                :maxlength="10"
              />
              <text class="input-suffix">元</text>
            </view>
          </view>
          
          <view class="form-item switch-item">
            <text class="form-label">开启自动结算</text>
            <switch 
              :checked="commissionSettings.autoSettlement" 
              color="#AC39FF"
              @change="toggleAutoSettlement"
            />
          </view>
          <text class="form-hint">开启后，系统将按照结算周期自动结算佣金</text>
          
          <view class="form-item switch-item">
            <text class="form-label">佣金到期提醒</text>
            <switch 
              :checked="commissionSettings.expirationReminder" 
              color="#AC39FF"
              @change="toggleExpirationReminder"
            />
          </view>
          <text class="form-hint">开启后，佣金即将到期时系统将发送提醒通知</text>
        </view>
      </view>
      
      <!-- 保存按钮 -->
      <view class="save-btn" @click="saveSettings" :style="{
        background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)',
        borderRadius: '35px',
        marginBottom: '30rpx'
      }">
        <text>保存设置</text>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
    
    <!-- 结算周期选择器 -->
    <view class="picker-mask" v-if="showPicker" @click="hidePicker"></view>
    <view class="picker-container" v-if="showPicker" :style="{
      borderTopLeftRadius: '35px',
      borderTopRightRadius: '35px',
      background: '#FFFFFF'
    }">
      <view class="picker-header">
        <text class="cancel-btn" @click="hidePicker">取消</text>
        <text class="picker-title">选择结算周期</text>
        <text class="confirm-btn" @click="confirmSettlementCycle">确定</text>
      </view>
      <picker-view
        class="picker-view"
        :indicator-style="'height: 80rpx;'"
        :value="[commissionSettings.settlementCycle]"
        @change="onSettlementCycleChange"
      >
        <picker-view-column>
          <view class="picker-item" v-for="(item, index) in settlementCycleOptions" :key="index">
            <text>{{ item }}</text>
          </view>
        </picker-view-column>
      </picker-view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 用户信息
const userInfo = ref({
  level: '中级分销商'
});

// 最大佣金比例
const maxCommissionRates = ref({
  firstLevel: 8,
  secondLevel: 3,
  teamLevel: 2
});

// 佣金设置
const commissionSettings = ref({
  firstLevel: '5',
  secondLevel: '2',
  teamLevel: '1',
  settlementCycle: 0, // 0: 7天, 1: 15天, 2: 30天
  minWithdraw: '10',
  autoSettlement: true,
  expirationReminder: true
});

// 结算周期选项
const settlementCycleOptions = ref([
  '7天',
  '15天',
  '30天'
]);

// 预览价格
const previewPrice = ref('100');

// 选择器相关
const showPicker = ref(false);
const tempSettlementCycle = ref(0);

// 计算一级佣金
const calculateFirstLevelCommission = computed(() => {
  const price = parseFloat(previewPrice.value) || 0;
  const rate = parseFloat(commissionSettings.value.firstLevel) || 0;
  return (price * rate / 100).toFixed(2);
});

// 计算二级佣金
const calculateSecondLevelCommission = computed(() => {
  const price = parseFloat(previewPrice.value) || 0;
  const rate = parseFloat(commissionSettings.value.secondLevel) || 0;
  return (price * rate / 100).toFixed(2);
});

// 计算团队佣金
const calculateTeamLevelCommission = computed(() => {
  const price = parseFloat(previewPrice.value) || 0;
  const rate = parseFloat(commissionSettings.value.teamLevel) || 0;
  return (price * rate / 100).toFixed(2);
});

// 计算总佣金
const calculateTotalCommission = computed(() => {
  const firstLevel = parseFloat(calculateFirstLevelCommission.value) || 0;
  const secondLevel = parseFloat(calculateSecondLevelCommission.value) || 0;
  const teamLevel = userInfo.value.level === '高级分销商' ? (parseFloat(calculateTeamLevelCommission.value) || 0) : 0;
  return (firstLevel + secondLevel + teamLevel).toFixed(2);
});

// 返回上一页
const navigateBack = () => {
  uni.navigateBack();
};

// 验证一级佣金比例
const validateFirstLevel = () => {
  let value = parseFloat(commissionSettings.value.firstLevel);
  if (isNaN(value)) {
    commissionSettings.value.firstLevel = '';
    return;
  }
  
  if (value > maxCommissionRates.value.firstLevel) {
    commissionSettings.value.firstLevel = maxCommissionRates.value.firstLevel.toString();
    uni.showToast({
      title: `一级佣金比例不能超过${maxCommissionRates.value.firstLevel}%`,
      icon: 'none'
    });
  }
};

// 验证二级佣金比例
const validateSecondLevel = () => {
  let value = parseFloat(commissionSettings.value.secondLevel);
  if (isNaN(value)) {
    commissionSettings.value.secondLevel = '';
    return;
  }
  
  if (value > maxCommissionRates.value.secondLevel) {
    commissionSettings.value.secondLevel = maxCommissionRates.value.secondLevel.toString();
    uni.showToast({
      title: `二级佣金比例不能超过${maxCommissionRates.value.secondLevel}%`,
      icon: 'none'
    });
  }
};

// 验证团队佣金比例
const validateTeamLevel = () => {
  let value = parseFloat(commissionSettings.value.teamLevel);
  if (isNaN(value)) {
    commissionSettings.value.teamLevel = '';
    return;
  }
  
  if (value > maxCommissionRates.value.teamLevel) {
    commissionSettings.value.teamLevel = maxCommissionRates.value.teamLevel.toString();
    uni.showToast({
      title: `团队佣金比例不能超过${maxCommissionRates.value.teamLevel}%`,
      icon: 'none'
    });
  }
};

// 切换自动结算
const toggleAutoSettlement = (e) => {
  commissionSettings.value.autoSettlement = e.detail.value;
};

// 切换佣金到期提醒
const toggleExpirationReminder = (e) => {
  commissionSettings.value.expirationReminder = e.detail.value;
};

// 显示结算周期选择器
const showSettlementCyclePicker = () => {
  tempSettlementCycle.value = commissionSettings.value.settlementCycle;
  showPicker.value = true;
};

// 隐藏选择器
const hidePicker = () => {
  showPicker.value = false;
};

// 结算周期变更
const onSettlementCycleChange = (e) => {
  tempSettlementCycle.value = e.detail.value[0];
};

// 确认结算周期
const confirmSettlementCycle = () => {
  commissionSettings.value.settlementCycle = tempSettlementCycle.value;
  showPicker.value = false;
};

// 保存设置
const saveSettings = () => {
  // 验证表单
  if (!commissionSettings.value.firstLevel) {
    uni.showToast({
      title: '请输入一级佣金比例',
      icon: 'none'
    });
    return;
  }
  
  if (!commissionSettings.value.secondLevel) {
    uni.showToast({
      title: '请输入二级佣金比例',
      icon: 'none'
    });
    return;
  }
  
  if (userInfo.value.level === '高级分销商' && !commissionSettings.value.teamLevel) {
    uni.showToast({
      title: '请输入团队佣金比例',
      icon: 'none'
    });
    return;
  }
  
  if (!commissionSettings.value.minWithdraw) {
    uni.showToast({
      title: '请输入最低提现金额',
      icon: 'none'
    });
    return;
  }
  
  // 保存设置
  uni.showLoading({
    title: '保存中...'
  });
  
  // 模拟保存过程
  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    });
  }, 1000);
};

// 页面加载
onMounted(() => {
  // 初始化数据，这里应该从API获取用户当前的佣金设置
  // 示例中使用模拟数据
  
  // 根据用户等级设置最大佣金比例
  if (userInfo.value.level === '初级分销商') {
    maxCommissionRates.value = {
      firstLevel: 5,
      secondLevel: 2,
      teamLevel: 0
    };
  } else if (userInfo.value.level === '中级分销商') {
    maxCommissionRates.value = {
      firstLevel: 8,
      secondLevel: 3,
      teamLevel: 0
    };
  } else if (userInfo.value.level === '高级分销商') {
    maxCommissionRates.value = {
      firstLevel: 12,
      secondLevel: 5,
      teamLevel: 2
    };
  }
});
</script>

<style lang="scss" scoped>
.commission-settings-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F8F8FA;
  position: relative;
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  
  .navbar-bg {
    height: 180rpx;
    width: 100%;
  }
  
  .navbar-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 180rpx;
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    padding-top: var(--status-bar-height);
    
    .back-btn {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .navbar-title {
      flex: 1;
      text-align: center;
      font-size: 36rpx;
      font-weight: 600;
      color: #FFFFFF;
    }
  }
}

.content-scroll {
  flex: 1;
  padding: 30rpx;
  padding-top: calc(180rpx + 30rpx);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 30rpx;
}

.tips-card {
  .tips-header {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
    
    .tips-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #FF9500;
      margin-left: 10rpx;
    }
  }
  
  .tips-content {
    font-size: 26rpx;
    color: #FF9500;
    line-height: 1.5;
  }
}

.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  
  .form-label {
    font-size: 28rpx;
    color: #333333;
  }
  
  .form-input {
    width: 200rpx;
    height: 80rpx;
    border: 1px solid #EEEEEE;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    padding: 0 20rpx;
    
    input {
      flex: 1;
      height: 100%;
      text-align: right;
      font-size: 28rpx;
      color: #333333;
    }
    
    .input-suffix {
      font-size: 28rpx;
      color: #666666;
      margin-left: 10rpx;
    }
  }
  
  .form-select {
    width: 200rpx;
    height: 80rpx;
    border: 1px solid #EEEEEE;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20rpx;
    
    text {
      font-size: 28rpx;
      color: #333333;
    }
  }
  
  &.switch-item {
    margin-top: 30rpx;
  }
}

.form-hint {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 30rpx;
  padding-left: 30rpx;
}

.preview-content {
  .preview-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;
    
    .preview-label {
      font-size: 28rpx;
      color: #333333;
    }
    
    .preview-input {
      width: 200rpx;
      height: 80rpx;
      border: 1px solid #EEEEEE;
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      padding: 0 20rpx;
      
      input {
        flex: 1;
        height: 100%;
        text-align: right;
        font-size: 28rpx;
        color: #333333;
      }
      
      .input-suffix {
        font-size: 28rpx;
        color: #666666;
        margin-left: 10rpx;
      }
    }
  }
  
  .preview-result {
    background-color: #F9F5FF;
    border-radius: 16rpx;
    padding: 20rpx;
    
    .result-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16rpx;
      
      .result-label {
        font-size: 28rpx;
        color: #666666;
      }
      
      .result-value {
        font-size: 28rpx;
        color: #333333;
      }
      
      &.total {
        margin-top: 20rpx;
        border-top: 1px solid rgba(172,57,255,0.2);
        padding-top: 16rpx;
        
        .result-label {
          font-size: 28rpx;
          font-weight: 600;
          color: #333333;
        }
        
        .result-value {
          font-size: 28rpx;
          font-weight: 600;
          color: #FF3B69;
        }
      }
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.save-btn {
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  
  text {
    font-size: 32rpx;
    color: #FFFFFF;
    font-weight: 600;
  }
}

.picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
}

.picker-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1001;
  
  .picker-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100rpx;
    padding: 0 30rpx;
    border-bottom: 1px solid #EEEEEE;
    
    .cancel-btn {
      font-size: 28rpx;
      color: #999999;
    }
    
    .picker-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }
    
    .confirm-btn {
      font-size: 28rpx;
      color: #AC39FF;
      font-weight: 600;
    }
  }
  
  .picker-view {
    width: 100%;
    height: 400rpx;
    
    .picker-item {
      display: flex;
      align-items: center;
      justify-content: center;
      
      text {
        font-size: 28rpx;
        color: #333333;
      }
    }
  }
}

.safe-area-bottom {
  height: 50rpx;
}

/* 适配小屏幕手机 */
@media screen and (max-width: 375px) {
  .card-title {
    font-size: 28rpx;
  }
  
  .form-item {
    .form-label {
      font-size: 24rpx;
    }
    
    .form-input, .form-select {
      input, text {
        font-size: 24rpx;
      }
    }
  }
}
</style> 