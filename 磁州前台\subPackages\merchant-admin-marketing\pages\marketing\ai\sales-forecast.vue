<template>
  <view class="sales-forecast-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">销售预测模型</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 页面内容 -->
    <scroll-view scroll-y class="page-content">
      <!-- 预测概览卡片 -->
      <view class="forecast-card">
        <view class="card-header">
          <text class="card-title">销售预测概览</text>
          <view class="date-filter">
            <text class="date-text">未来30天</text>
            <view class="filter-icon"></view>
          </view>
        </view>
        <view class="forecast-metrics">
          <view class="metric-item">
            <text class="metric-value">￥286,500</text>
            <text class="metric-label">预计销售额</text>
            <text class="metric-growth positive">↑12.8%</text>
          </view>
          <view class="metric-item">
            <text class="metric-value">9,240</text>
            <text class="metric-label">预计订单数</text>
            <text class="metric-growth positive">↑7.5%</text>
          </view>
          <view class="metric-item">
            <text class="metric-value">￥31</text>
            <text class="metric-label">预计客单价</text>
            <text class="metric-growth positive">↑4.2%</text>
          </view>
        </view>
      </view>
      
      <!-- 销售趋势图 -->
      <view class="trend-card">
        <view class="card-header">
          <text class="card-title">销售趋势预测</text>
          <view class="period-tabs">
            <text 
              v-for="(tab, index) in periodTabs" 
              :key="index" 
              :class="['tab-item', currentPeriodTab === index ? 'active' : '']"
              @click="switchPeriodTab(index)">
              {{tab}}
            </text>
          </view>
        </view>
        
        <view class="forecast-chart">
          <!-- SVG图表实现 -->
          <svg class="trend-chart-svg" viewBox="0 0 400 200" xmlns="http://www.w3.org/2000/svg">
            <!-- 网格线 -->
            <line x1="50" y1="20" x2="50" y2="180" stroke="#eaeaea" stroke-width="1" />
            <line x1="120" y1="20" x2="120" y2="180" stroke="#eaeaea" stroke-width="1" />
            <line x1="190" y1="20" x2="190" y2="180" stroke="#eaeaea" stroke-width="1" />
            <line x1="260" y1="20" x2="260" y2="180" stroke="#eaeaea" stroke-width="1" />
            <line x1="330" y1="20" x2="330" y2="180" stroke="#eaeaea" stroke-width="1" />
            <line x1="30" y1="40" x2="370" y2="40" stroke="#eaeaea" stroke-width="1" />
            <line x1="30" y1="80" x2="370" y2="80" stroke="#eaeaea" stroke-width="1" />
            <line x1="30" y1="120" x2="370" y2="120" stroke="#eaeaea" stroke-width="1" />
            <line x1="30" y1="160" x2="370" y2="160" stroke="#eaeaea" stroke-width="1" />
            
            <!-- 历史销售线 -->
            <path 
              d="M50,120 Q85,110 120,100 T190,70 T260,90"
              fill="none" 
              stroke="#007AFF" 
              stroke-width="2"
            />
            <circle cx="50" cy="120" r="4" fill="#007AFF" />
            <circle cx="120" cy="100" r="4" fill="#007AFF" />
            <circle cx="190" cy="70" r="4" fill="#007AFF" />
            <circle cx="260" cy="90" r="4" fill="#007AFF" />
            
            <!-- 预测销售线 -->
            <path 
              d="M260,90 Q295,60 330,50"
              fill="none" 
              stroke="#FF9500" 
              stroke-width="2" 
              stroke-dasharray="5,5"
            />
            <circle cx="330" cy="50" r="4" fill="#FF9500" />
            
            <!-- 预测区间 -->
            <path 
              d="M260,100 Q295,75 330,65 L330,40 Q295,50 260,80 Z" 
              fill="rgba(255, 149, 0, 0.1)" 
            />
            
            <!-- X轴标签 -->
            <text x="50" y="190" font-size="10" fill="#999" text-anchor="middle">上月</text>
            <text x="120" y="190" font-size="10" fill="#999" text-anchor="middle">本月初</text>
            <text x="190" y="190" font-size="10" fill="#999" text-anchor="middle">本月中</text>
            <text x="260" y="190" font-size="10" fill="#999" text-anchor="middle">当前</text>
            <text x="330" y="190" font-size="10" fill="#999" text-anchor="middle">预测</text>
            
            <!-- 图例 -->
            <rect x="30" y="210" width="10" height="2" fill="#007AFF" />
            <text x="45" y="214" font-size="10" fill="#666">历史销售</text>
            <rect x="100" y="210" width="10" height="2" fill="#FF9500" stroke-dasharray="5,5" />
            <text x="115" y="214" font-size="10" fill="#666">预测销售</text>
            <rect x="180" y="208" width="10" height="6" fill="rgba(255, 149, 0, 0.1)" />
            <text x="195" y="214" font-size="10" fill="#666">预测区间</text>
          </svg>
        </view>
      </view>
      
      <!-- 增长点分析 -->
      <view class="growth-card">
        <view class="card-header">
          <text class="card-title">主要增长点</text>
          <view class="ai-badge">AI</view>
        </view>
        
        <view class="growth-list">
          <view class="growth-item">
            <view class="growth-icon product"></view>
            <view class="growth-content">
              <text class="growth-title">热销产品</text>
              <text class="growth-value">美妆护肤类 +18.2%</text>
              <text class="growth-desc">根据季节变化和历史数据，预计美妆护肤类产品将迎来销售高峰</text>
            </view>
          </view>
          
          <view class="growth-item">
            <view class="growth-icon customer"></view>
            <view class="growth-content">
              <text class="growth-title">客户群体</text>
              <text class="growth-value">25-34岁女性 +15.4%</text>
              <text class="growth-desc">25-34岁女性消费增长明显，推荐增加此客群营销投入</text>
            </view>
          </view>
          
          <view class="growth-item">
            <view class="growth-icon channel"></view>
            <view class="growth-content">
              <text class="growth-title">销售渠道</text>
              <text class="growth-value">社交平台引流 +23.5%</text>
              <text class="growth-desc">来自社交媒体的流量转化率提升，建议增加社媒投放</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 产品销量预测 -->
      <view class="product-forecast-card">
        <view class="card-header">
          <text class="card-title">产品销量预测</text>
          <view class="category-filter">
            <text class="filter-text">全部分类</text>
            <view class="filter-icon"></view>
          </view>
        </view>
        
        <view class="product-forecast-list">
          <view class="product-item" v-for="(product, index) in products" :key="index">
            <image class="product-image" :src="product.image" mode="aspectFill"></image>
            <view class="product-info">
              <text class="product-name">{{product.name}}</text>
              <view class="forecast-data">
                <view class="forecast-bar-container">
                  <view class="forecast-bar-current" :style="{width: product.currentPercent + '%'}"></view>
                  <view class="forecast-bar-predicted" :style="{width: product.growthPercent + '%'}"></view>
                </view>
                <view class="forecast-numbers">
                  <text class="current-number">{{product.current}}</text>
                  <text class="predicted-number">{{product.predicted}}</text>
                  <text class="growth-rate" :class="{'positive': product.growth > 0, 'negative': product.growth < 0}">
                    {{product.growth > 0 ? '+' : ''}}{{product.growth}}%
                  </text>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <view class="view-more" @click="viewAllProducts">
          查看更多产品预测 <text class="arrow-right">→</text>
        </view>
      </view>
      
      <!-- 优化建议 -->
      <view class="recommendations-card">
        <view class="card-header">
          <text class="card-title">销售优化建议</text>
          <view class="ai-badge">AI</view>
        </view>
        
        <view class="recommendation-list">
          <view class="recommendation-item" v-for="(rec, index) in recommendations" :key="index">
            <view class="recommendation-icon" :class="rec.type"></view>
            <view class="recommendation-content">
              <text class="recommendation-title">{{rec.title}}</text>
              <text class="recommendation-desc">{{rec.description}}</text>
              <view class="recommendation-impact">
                <text class="impact-label">预计影响</text>
                <view class="impact-stars">
                  <view v-for="n in 5" :key="n" :class="['star', n <= rec.impact ? 'filled' : '']"></view>
                </view>
              </view>
              <view class="recommendation-actions">
                <text class="action apply" @click="applyRecommendation(index)">应用</text>
                <text class="action details" @click="viewRecommendationDetails(index)">详情</text>
                <text class="action ignore" @click="ignoreRecommendation(index)">忽略</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 底部空间 -->
      <view style="height: 20px;"></view>
    </scroll-view>
    
    <!-- 底部导航栏 -->
    <tab-bar active-tab="marketing" @tab-change="handleTabChange"></tab-bar>
  </view>
</template>

<script>
import TabBar from '../../../../../components/TabBar.vue'

export default {
  components: {
    TabBar
  },
  data() {
    return {
      periodTabs: ['7天', '30天', '90天', '180天'],
      currentPeriodTab: 1,
      products: [
        {
          name: '高保湿精华霜 50ml',
          image: '/static/images/product-1.png',
          current: 1280,
          predicted: 1520,
          growth: 18.8,
          currentPercent: 65,
          growthPercent: 12
        },
        {
          name: '小黑瓶精华液 30ml',
          image: '/static/images/product-2.png',
          current: 860,
          predicted: 950,
          growth: 10.5,
          currentPercent: 45,
          growthPercent: 5
        },
        {
          name: '清爽控油洁面乳 150ml',
          image: '/static/images/product-3.png',
          current: 1560,
          predicted: 1450,
          growth: -7.1,
          currentPercent: 80,
          growthPercent: -5
        },
        {
          name: '玫瑰保湿面膜 25ml*6片',
          image: '/static/images/product-4.png',
          current: 680,
          predicted: 820,
          growth: 20.6,
          currentPercent: 35,
          growthPercent: 7
        }
      ],
      recommendations: [
        {
          title: '调整库存',
          description: '基于销售预测，建议增加"高保湿精华霜"和"玫瑰保湿面膜"的备货量，预计可提高满足率和销售额',
          type: 'stock',
          impact: 4
        },
        {
          title: '营销活动优化',
          description: '针对25-34岁女性人群，推出"美妆护肤季"营销活动，预计提升转化率15%',
          type: 'marketing',
          impact: 5
        },
        {
          title: '价格策略调整',
          description: '建议对"清爽控油洁面乳"进行限时促销活动，提高产品竞争力',
          type: 'price',
          impact: 3
        }
      ]
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showHelp() {
      uni.showToast({
        title: '销售预测帮助',
        icon: 'none'
      });
    },
    switchPeriodTab(index) {
      this.currentPeriodTab = index;
    },
    viewAllProducts() {
      uni.showToast({
        title: '查看所有产品预测',
        icon: 'none'
      });
    },
    applyRecommendation(index) {
      uni.showToast({
        title: `已应用建议：${this.recommendations[index].title}`,
        icon: 'success'
      });
    },
    viewRecommendationDetails(index) {
      uni.showToast({
        title: `查看详情：${this.recommendations[index].title}`,
        icon: 'none'
      });
    },
    ignoreRecommendation(index) {
      uni.showToast({
        title: `已忽略建议：${this.recommendations[index].title}`,
        icon: 'none'
      });
    },
    handleTabChange(tabId) {
      // 处理底部标签页切换事件
      console.log('切换到标签:', tabId);
    }
  }
}
</script>

<style lang="scss">
.sales-forecast-container {
  min-height: 100vh;
  background-color: #F5F7FA;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #6366F1, #8B5CF6);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(99, 102, 241, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

.page-content {
  height: calc(100vh - 77px);
  box-sizing: border-box;
  padding: 15px;
}

/* 卡片通用样式 */
.forecast-card, .trend-card, .growth-card, .product-forecast-card, .recommendations-card {
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 15px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.date-filter, .category-filter {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
}

.date-text, .filter-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.filter-icon {
  width: 8px;
  height: 8px;
  border-top: 1.5px solid #666;
  border-right: 1.5px solid #666;
  transform: rotate(135deg);
}

/* 预测度量指标样式 */
.forecast-metrics {
  display: flex;
  justify-content: space-between;
}

.metric-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  position: relative;
}

.metric-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 10%;
  height: 80%;
  width: 1px;
  background-color: #F0F0F0;
}

.metric-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.metric-growth {
  font-size: 12px;
  font-weight: 500;
}

.metric-growth.positive {
  color: #34C759;
}

.metric-growth.negative {
  color: #FF3B30;
}

/* 趋势图表样式 */
.period-tabs {
  display: flex;
}

.tab-item {
  padding: 5px 10px;
  font-size: 12px;
  color: #666;
  border-radius: 15px;
  margin-left: 5px;
}

.tab-item.active {
  background: #6366F1;
  color: white;
}

.forecast-chart {
  height: 250px;
}

.trend-chart-svg {
  width: 100%;
  height: 100%;
}

/* 增长点分析样式 */
.ai-badge {
  display: inline-block;
  background: linear-gradient(135deg, #6366F1, #8B5CF6);
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 3px 6px;
  border-radius: 8px;
  margin-left: 8px;
}

.growth-list {
  margin-top: 10px;
}

.growth-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  background: #F8FAFC;
  border-radius: 10px;
  margin-bottom: 10px;
}

.growth-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 15px;
  flex-shrink: 0;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  color: white;
}

.growth-icon.product {
  background: linear-gradient(135deg, #6366F1, #8B5CF6);
}

.growth-icon.product::before {
  content: '📦';
}

.growth-icon.customer {
  background: linear-gradient(135deg, #EC4899, #F472B6);
}

.growth-icon.customer::before {
  content: '👤';
}

.growth-icon.channel {
  background: linear-gradient(135deg, #10B981, #34D399);
}

.growth-icon.channel::before {
  content: '📱';
}

.growth-content {
  flex: 1;
}

.growth-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.growth-value {
  font-size: 15px;
  font-weight: 600;
  color: #6366F1;
  margin-bottom: 6px;
}

.growth-desc {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}

/* 产品销量预测样式 */
.product-forecast-list {
  margin-bottom: 15px;
}

.product-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #F0F0F0;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  margin-right: 15px;
  background: #F5F7FA;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.forecast-bar-container {
  height: 8px;
  background-color: #F0F0F0;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  margin-bottom: 8px;
}

.forecast-bar-current {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background-color: #007AFF;
  border-radius: 4px;
}

.forecast-bar-predicted {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background-color: rgba(99, 102, 241, 0.5);
  border-radius: 4px;
  transform: translateX(100%);
}

.forecast-numbers {
  display: flex;
  justify-content: space-between;
}

.current-number {
  font-size: 12px;
  color: #666;
}

.predicted-number {
  font-size: 12px;
  font-weight: 600;
  color: #6366F1;
}

.growth-rate {
  font-size: 12px;
  font-weight: 600;
}

.growth-rate.positive {
  color: #34C759;
}

.growth-rate.negative {
  color: #FF3B30;
}

.view-more {
  text-align: center;
  font-size: 14px;
  color: #6366F1;
  padding: 10px;
}

.arrow-right {
  font-size: 16px;
}

/* 推荐建议样式 */
.recommendation-list {
  margin-top: 10px;
}

.recommendation-item {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  background: #F8FAFC;
  border-radius: 10px;
  margin-bottom: 12px;
}

.recommendation-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 15px;
  flex-shrink: 0;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  color: white;
}

.recommendation-icon.stock {
  background: linear-gradient(135deg, #10B981, #34D399);
}

.recommendation-icon.stock::before {
  content: '📊';
}

.recommendation-icon.marketing {
  background: linear-gradient(135deg, #6366F1, #8B5CF6);
}

.recommendation-icon.marketing::before {
  content: '🎯';
}

.recommendation-icon.price {
  background: linear-gradient(135deg, #F59E0B, #FBBF24);
}

.recommendation-icon.price::before {
  content: '💰';
}

.recommendation-content {
  flex: 1;
}

.recommendation-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.recommendation-desc {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 10px;
}

.recommendation-impact {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.impact-label {
  font-size: 12px;
  color: #666;
  margin-right: 10px;
}

.impact-stars {
  display: flex;
}

.star {
  width: 12px;
  height: 12px;
  margin-right: 4px;
  position: relative;
}

.star::before {
  content: '☆';
  position: absolute;
  color: #C7C7CC;
}

.star.filled::before {
  content: '★';
  color: #F59E0B;
}

.recommendation-actions {
  display: flex;
  justify-content: flex-end;
}

.action {
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 12px;
  margin-left: 8px;
}

.action.apply {
  background: #6366F1;
  color: white;
}

.action.details {
  background: #F0F0F0;
  color: #666;
}

.action.ignore {
  background: #F0F0F0;
  color: #666;
}
</style> 