"use strict";
const common_vendor = require("../../../../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
const _sfc_main = {
  __name: "ProductCard",
  props: {
    product: {
      type: Object,
      required: true
    },
    showShop: {
      type: Boolean,
      default: true
    },
    showActionBtn: {
      type: Boolean,
      default: true
    },
    cardStyle: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ["click", "addToCart"],
  setup(__props) {
    const getLabelColor = (type) => {
      switch (type) {
        case "discount":
          return "rgba(255,59,105,0.1)";
        case "coupon":
          return "rgba(255,149,0,0.1)";
        case "new":
          return "rgba(52,199,89,0.1)";
        case "hot":
          return "rgba(255,59,48,0.1)";
        default:
          return "rgba(142,142,147,0.1)";
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: __props.product.coverImage,
        b: __props.product.tag
      }, __props.product.tag ? {
        c: common_vendor.t(__props.product.tag)
      } : {}, {
        d: common_vendor.t(__props.product.title),
        e: __props.showShop
      }, __props.showShop ? common_vendor.e({
        f: __props.product.shopLogo,
        g: common_vendor.t(__props.product.shopName),
        h: __props.product.distance
      }, __props.product.distance ? {
        i: common_vendor.t(__props.product.distance)
      } : {}) : {}, {
        j: common_vendor.t(__props.product.price),
        k: __props.product.originalPrice
      }, __props.product.originalPrice ? {
        l: common_vendor.t(__props.product.originalPrice)
      } : {}, {
        m: __props.product.soldCount
      }, __props.product.soldCount ? {
        n: common_vendor.t(__props.product.soldCount)
      } : {}, {
        o: __props.product.labels && __props.product.labels.length > 0
      }, __props.product.labels && __props.product.labels.length > 0 ? {
        p: common_vendor.f(__props.product.labels, (label, index, i0) => {
          return {
            a: common_vendor.t(label.text),
            b: index,
            c: getLabelColor(label.type)
          };
        })
      } : {}, {
        q: __props.showActionBtn
      }, __props.showActionBtn ? {
        r: common_vendor.p({
          d: "M12 9v6m-3-3h6",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        s: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        t: common_vendor.o(($event) => _ctx.$emit("addToCart", __props.product))
      } : {}, {
        v: common_vendor.s(__props.cardStyle),
        w: common_vendor.o(($event) => _ctx.$emit("click", __props.product))
      });
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-fee967c6"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/components/mall/ProductCard.js.map
