"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const _sfc_main = {
  __name: "favorites",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const favoritesList = common_vendor.ref([]);
    const page = common_vendor.ref(1);
    common_vendor.ref(10);
    const hasMore = common_vendor.ref(true);
    const isLoading = common_vendor.ref(false);
    const isRefreshing = common_vendor.ref(false);
    const isEditMode = common_vendor.ref(false);
    const selectedItems = common_vendor.ref([]);
    const isAllSelected = common_vendor.computed(() => {
      return favoritesList.value.length > 0 && selectedItems.value.length === favoritesList.value.length;
    });
    common_vendor.onMounted(() => {
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight || 20;
      loadData();
    });
    const loadData = () => {
      if (isLoading.value)
        return;
      isLoading.value = true;
      setTimeout(() => {
        const mockData = [
          {
            id: "4001",
            type: "长途拼车",
            publishTime: "2023-10-15 16:30",
            status: "active",
            startPoint: "磁县政府",
            endPoint: "石家庄火车站",
            departureTime: "明天 10:30",
            seatCount: 3,
            price: 50,
            userName: "张先生",
            userAvatar: "/static/images/avatar/user1.png",
            viewCount: 128,
            messageCount: 5
          },
          {
            id: "4002",
            type: "上下班拼车",
            publishTime: "2023-10-14 14:15",
            status: "active",
            startPoint: "磁县老城区",
            endPoint: "邯郸科技学院",
            departureTime: "工作日 07:30",
            seatCount: 4,
            price: 12,
            userName: "李女士",
            userAvatar: "/static/images/avatar/user2.png",
            viewCount: 86,
            messageCount: 2
          },
          {
            id: "4003",
            type: "短途拼车",
            publishTime: "2023-10-13 11:40",
            status: "expired",
            startPoint: "磁县体育场",
            endPoint: "磁县汽车站",
            departureTime: "今天 16:00",
            seatCount: 2,
            price: 5,
            userName: "王先生",
            userAvatar: "/static/images/avatar/user3.png",
            viewCount: 56,
            messageCount: 0
          }
        ];
        if (page.value === 1) {
          favoritesList.value = mockData;
        } else {
          favoritesList.value = [...favoritesList.value, ...mockData];
        }
        if (page.value >= 2) {
          hasMore.value = false;
        }
        isLoading.value = false;
        isRefreshing.value = false;
      }, 1e3);
    };
    const loadMore = () => {
      if (!hasMore.value || isLoading.value)
        return;
      page.value++;
      loadData();
    };
    const onRefresh = () => {
      isRefreshing.value = true;
      page.value = 1;
      hasMore.value = true;
      loadData();
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const goBrowse = () => {
      common_vendor.index.navigateTo({
        url: "/carpool-package/pages/carpool-main/index"
      });
    };
    const viewDetail = (item) => {
      if (isEditMode.value) {
        toggleSelectItem(item);
        return;
      }
      common_vendor.index.navigateTo({
        url: `/carpool-package/pages/carpool/detail/index?id=${item.id}`
      });
    };
    const cancelFavorite = (item) => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要取消收藏此条信息吗？",
        success: (res) => {
          if (res.confirm) {
            favoritesList.value = favoritesList.value.filter((i) => i.id !== item.id);
            common_vendor.index.showToast({
              title: "已取消收藏",
              icon: "success"
            });
          }
        }
      });
    };
    const toggleEditMode = () => {
      isEditMode.value = !isEditMode.value;
      if (!isEditMode.value) {
        selectedItems.value = [];
      }
    };
    const toggleSelectItem = (item) => {
      const index = selectedItems.value.indexOf(item.id);
      if (index === -1) {
        selectedItems.value.push(item.id);
      } else {
        selectedItems.value.splice(index, 1);
      }
    };
    const toggleSelectAll = () => {
      if (isAllSelected.value) {
        selectedItems.value = [];
      } else {
        selectedItems.value = favoritesList.value.map((item) => item.id);
      }
    };
    const batchCancel = () => {
      if (selectedItems.value.length === 0)
        return;
      common_vendor.index.showModal({
        title: "提示",
        content: `确定要取消收藏这 ${selectedItems.value.length} 条信息吗？`,
        success: (res) => {
          if (res.confirm) {
            favoritesList.value = favoritesList.value.filter((item) => !selectedItems.value.includes(item.id));
            selectedItems.value = [];
            common_vendor.index.showToast({
              title: "已取消收藏",
              icon: "success"
            });
          }
        }
      });
    };
    const getStatusText = (status) => {
      const statusMap = {
        active: "可乘坐",
        pending: "审核中",
        expired: "已过期"
      };
      return statusMap[status] || status;
    };
    const getStatusClass = (status) => {
      const classMap = {
        active: "status-active",
        pending: "status-pending",
        expired: "status-expired"
      };
      return classMap[status] || "";
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_vendor.f(favoritesList.value, (item, index, i0) => {
          return common_vendor.e(isEditMode.value ? common_vendor.e({
            a: selectedItems.value.includes(item.id)
          }, selectedItems.value.includes(item.id) ? {} : {}, {
            b: selectedItems.value.includes(item.id) ? 1 : "",
            c: common_vendor.o(($event) => toggleSelectItem(item), item.id)
          }) : {}, {
            d: item.userAvatar,
            e: common_vendor.t(item.userName),
            f: common_vendor.t(item.publishTime),
            g: common_vendor.t(item.type),
            h: common_vendor.t(item.startPoint),
            i: common_vendor.t(item.endPoint),
            j: common_vendor.t(item.departureTime),
            k: common_vendor.t(item.seatCount),
            l: item.price
          }, item.price ? {
            m: common_assets._imports_3$27,
            n: common_vendor.t(item.price)
          } : {}, {
            o: common_vendor.t(item.viewCount),
            p: common_vendor.t(item.messageCount)
          }, !isEditMode.value ? {
            q: common_vendor.o(($event) => cancelFavorite(item), item.id)
          } : {}, {
            r: common_vendor.t(getStatusText(item.status)),
            s: common_vendor.n(getStatusClass(item.status)),
            t: common_vendor.o(($event) => viewDetail(item), item.id),
            v: item.id
          });
        }),
        e: isEditMode.value,
        f: common_assets._imports_1$33,
        g: common_assets._imports_2$29,
        h: common_assets._imports_4$23,
        i: common_assets._imports_5$21,
        j: !isEditMode.value,
        k: favoritesList.value.length === 0 && !isLoading.value
      }, favoritesList.value.length === 0 && !isLoading.value ? {
        l: common_assets._imports_6$18,
        m: common_vendor.o(goBrowse)
      } : {}, {
        n: isLoading.value && !isRefreshing.value
      }, isLoading.value && !isRefreshing.value ? {} : {}, {
        o: favoritesList.value.length > 0 && !hasMore.value
      }, favoritesList.value.length > 0 && !hasMore.value ? {} : {}, {
        p: common_vendor.o(loadMore),
        q: common_vendor.o(onRefresh),
        r: isRefreshing.value,
        s: isEditMode.value
      }, isEditMode.value ? common_vendor.e({
        t: isAllSelected.value
      }, isAllSelected.value ? {} : {}, {
        v: isAllSelected.value ? 1 : "",
        w: common_vendor.o(toggleSelectAll),
        x: common_vendor.t(selectedItems.value.length),
        y: selectedItems.value.length === 0,
        z: common_vendor.o(batchCancel)
      }) : {}, {
        A: common_vendor.t(isEditMode.value ? "完成" : "编辑"),
        B: common_vendor.o(toggleEditMode)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/carpool-package/pages/carpool/my/favorites.js.map
