<!-- 添加会员等级页面开始 -->
<template>
  <view class="add-level-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">添加会员等级</text>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 表单内容 -->
    <view class="form-content">
      <!-- 基本信息 -->
      <view class="section-card">
        <view class="section-title">基本信息</view>
        
        <view class="form-item">
          <text class="form-label">等级名称</text>
          <input class="form-input" v-model="levelForm.name" placeholder="请输入等级名称" maxlength="10" />
        </view>
        
        <view class="form-item">
          <text class="form-label">等级图标</text>
          <view class="icon-upload" @click="chooseIcon">
            <image class="preview-icon" v-if="levelForm.icon" :src="levelForm.icon" mode="aspectFit"></image>
            <view class="upload-placeholder" v-else>
              <text class="upload-icon">+</text>
              <text class="upload-text">上传图标</text>
            </view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">等级颜色</text>
          <view class="color-picker">
            <view class="color-option" 
                  v-for="(color, index) in colorOptions" 
                  :key="index" 
                  :style="{ background: color.value }"
                  :class="{ active: levelForm.color === color.value }"
                  @click="selectColor(color.value)">
            </view>
          </view>
        </view>
      </view>
      
      <!-- 升级条件 -->
      <view class="section-card">
        <view class="section-title">升级条件</view>
        
        <view class="form-item">
          <text class="form-label">条件类型</text>
          <view class="radio-group">
            <view class="radio-item" :class="{ active: levelForm.conditionType === 'consumption' }" @click="setConditionType('consumption')">
              <text class="radio-text">累计消费金额</text>
            </view>
            <view class="radio-item" :class="{ active: levelForm.conditionType === 'growth' }" @click="setConditionType('growth')">
              <text class="radio-text">成长值</text>
            </view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">{{ levelForm.conditionType === 'consumption' ? '消费金额' : '成长值' }}</text>
          <view class="form-input-group">
            <input type="number" class="form-input" v-model="levelForm.conditionValue" :placeholder="`请输入${levelForm.conditionType === 'consumption' ? '消费金额' : '成长值'}`" />
            <text class="input-suffix">{{ levelForm.conditionType === 'consumption' ? '元' : '分' }}</text>
          </view>
        </view>
        
        <view class="form-item switch-item">
          <text class="form-label">自动升级</text>
          <switch :checked="levelForm.autoUpgrade" @change="toggleAutoUpgrade" color="#4A00E0" />
        </view>
        
        <view class="form-item switch-item">
          <text class="form-label">等级保护</text>
          <switch :checked="levelForm.levelProtection" @change="toggleLevelProtection" color="#4A00E0" />
        </view>
        
        <view class="form-item" v-if="levelForm.levelProtection">
          <text class="form-label">保护期限</text>
          <view class="form-input-group">
            <input type="number" class="form-input" v-model="levelForm.protectionDays" placeholder="请输入保护天数" />
            <text class="input-suffix">天</text>
          </view>
        </view>
      </view>
      
      <!-- 等级权益 -->
      <view class="section-card">
        <view class="section-title">等级权益</view>
        
        <view class="form-item">
          <text class="form-label">折扣比例</text>
          <view class="form-input-group">
            <input type="digit" class="form-input" v-model="levelForm.discount" placeholder="请输入折扣比例" />
            <text class="input-suffix">折</text>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">积分倍率</text>
          <view class="form-input-group">
            <input type="digit" class="form-input" v-model="levelForm.pointsRatio" placeholder="请输入积分倍率" />
            <text class="input-suffix">倍</text>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">特权选择</text>
          <view class="privilege-list">
            <view class="privilege-item" v-for="(privilege, index) in privileges" :key="index">
              <view class="privilege-checkbox" :class="{ checked: privilege.selected }" @click="togglePrivilege(privilege)">
                <view class="checkbox-inner" v-if="privilege.selected"></view>
              </view>
              <text class="privilege-name">{{privilege.name}}</text>
            </view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">自定义权益</text>
          <view class="custom-privileges">
            <view class="custom-privilege-item" v-for="(item, index) in levelForm.customPrivileges" :key="index">
              <input class="custom-privilege-input" v-model="levelForm.customPrivileges[index]" placeholder="请输入权益内容" />
              <view class="delete-btn" @click="deleteCustomPrivilege(index)">×</view>
            </view>
            <view class="add-custom-btn" @click="addCustomPrivilege">
              <text class="add-custom-text">+ 添加自定义权益</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部按钮 -->
    <view class="bottom-bar">
      <button class="cancel-btn" @click="goBack">取消</button>
      <button class="save-btn" @click="saveLevel">保存</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 表单数据
      levelForm: {
        name: '',
        icon: '',
        color: 'linear-gradient(135deg, #8E2DE2, #4A00E0)',
        conditionType: 'consumption',
        conditionValue: '',
        autoUpgrade: true,
        levelProtection: false,
        protectionDays: 30,
        discount: '9.5',
        pointsRatio: '1.2',
        customPrivileges: ['']
      },
      
      // 颜色选项
      colorOptions: [
        { name: '紫色', value: 'linear-gradient(135deg, #8E2DE2, #4A00E0)' },
        { name: '金色', value: 'linear-gradient(135deg, #FFC837, #FF8008)' },
        { name: '银色', value: 'linear-gradient(135deg, #D4D4D8, #A1A1AA)' },
        { name: '蓝色', value: 'linear-gradient(135deg, #2196F3, #0D47A1)' },
        { name: '红色', value: 'linear-gradient(135deg, #FF5E3A, #FF2A68)' },
        { name: '绿色', value: 'linear-gradient(135deg, #43E97B, #38F9D7)' }
      ],
      
      // 特权列表
      privileges: [
        { id: 1, name: '会员折扣', selected: true },
        { id: 2, name: '积分加速', selected: true },
        { id: 3, name: '免费配送', selected: false },
        { id: 4, name: '生日礼包', selected: true },
        { id: 5, name: '专属客服', selected: false },
        { id: 6, name: '优先发货', selected: false }
      ]
    };
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    
    chooseIcon() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.levelForm.icon = res.tempFilePaths[0];
        }
      });
    },
    
    selectColor(color) {
      this.levelForm.color = color;
    },
    
    setConditionType(type) {
      this.levelForm.conditionType = type;
    },
    
    toggleAutoUpgrade(e) {
      this.levelForm.autoUpgrade = e.detail.value;
    },
    
    toggleLevelProtection(e) {
      this.levelForm.levelProtection = e.detail.value;
    },
    
    togglePrivilege(privilege) {
      const index = this.privileges.findIndex(item => item.id === privilege.id);
      if (index !== -1) {
        this.privileges[index].selected = !this.privileges[index].selected;
      }
    },
    
    addCustomPrivilege() {
      this.levelForm.customPrivileges.push('');
    },
    
    deleteCustomPrivilege(index) {
      if (this.levelForm.customPrivileges.length > 1) {
        this.levelForm.customPrivileges.splice(index, 1);
      } else {
        this.levelForm.customPrivileges = [''];
      }
    },
    
    validateForm() {
      if (!this.levelForm.name) {
        uni.showToast({
          title: '请输入等级名称',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.levelForm.icon) {
        uni.showToast({
          title: '请上传等级图标',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.levelForm.conditionValue) {
        uni.showToast({
          title: `请输入${this.levelForm.conditionType === 'consumption' ? '消费金额' : '成长值'}`,
          icon: 'none'
        });
        return false;
      }
      
      return true;
    },
    
    saveLevel() {
      if (!this.validateForm()) return;
      
      uni.showLoading({
        title: '保存中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '会员等级添加成功',
          icon: 'success'
        });
        
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }, 1000);
    }
  }
}
</script>

<style>
/* 添加会员等级页面样式开始 */
.add-level-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 120rpx;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #8E2DE2, #4A00E0);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(74, 0, 224, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
}

/* 表单内容样式 */
.form-content {
  padding: 20rpx;
}

.section-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 表单项样式 */
.form-item {
  margin-bottom: 20rpx;
}

.form-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.form-input-group {
  display: flex;
  align-items: center;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  overflow: hidden;
}

.form-input-group .form-input {
  flex: 1;
  border: none;
}

.input-suffix {
  padding: 0 20rpx;
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  height: 80rpx;
  line-height: 80rpx;
}

/* 图标上传 */
.icon-upload {
  width: 120rpx;
  height: 120rpx;
  border: 1rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f9f9f9;
}

.preview-icon {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.upload-text {
  font-size: 20rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 颜色选择器 */
.color-picker {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.color-option {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  border: 2rpx solid transparent;
}

.color-option.active {
  border-color: #333;
  transform: scale(1.1);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 单选按钮组 */
.radio-group {
  display: flex;
  gap: 20rpx;
}

.radio-item {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9f9f9;
}

.radio-item.active {
  background: rgba(74, 0, 224, 0.1);
  border-color: #4A00E0;
}

.radio-text {
  font-size: 26rpx;
  color: #666;
}

.radio-item.active .radio-text {
  color: #4A00E0;
  font-weight: 600;
}

/* 开关项 */
.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 特权列表 */
.privilege-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-top: 10rpx;
}

.privilege-item {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  background: #f9f9f9;
  border-radius: 8rpx;
}

.privilege-checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 1rpx solid #ddd;
  border-radius: 50%;
  margin-right: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.privilege-checkbox.checked {
  border-color: #4A00E0;
  background: #4A00E0;
}

.checkbox-inner {
  width: 18rpx;
  height: 18rpx;
  border-radius: 50%;
  background: #fff;
}

.privilege-name {
  font-size: 26rpx;
  color: #666;
}

/* 自定义权益 */
.custom-privileges {
  margin-top: 10rpx;
}

.custom-privilege-item {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.custom-privilege-input {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.delete-btn {
  width: 60rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
}

.add-custom-btn {
  height: 80rpx;
  border: 1rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9f9f9;
}

.add-custom-text {
  font-size: 26rpx;
  color: #4A00E0;
}

/* 底部按钮栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.cancel-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.save-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background: #4A00E0;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
}
/* 添加会员等级页面样式结束 */
</style>
<!-- 添加会员等级页面结束 --> 