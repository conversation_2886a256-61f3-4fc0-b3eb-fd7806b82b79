"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const api_index = require("../../../../api/index.js");
if (!Math) {
  (CarpoolNav + ConfigurablePremiumActions)();
}
const CarpoolNav = () => "../../../../components/carpool-nav.js";
const ConfigurablePremiumActions = () => "../../../../components/premium/ConfigurablePremiumActions.js";
const _sfc_main = {
  __name: "people-to-car",
  setup(__props) {
    const formData = common_vendor.ref({
      startPoint: "",
      endPoint: "",
      departureDate: "",
      departureTime: "",
      passengers: "",
      contactName: "",
      contactPhone: "",
      remark: "",
      agreement: false,
      viaPoints: []
    });
    const passengerOptions = common_vendor.ref(["1人", "2人", "3人", "4人", "5人", "6人"]);
    const passengerIndex = common_vendor.ref(0);
    const publishMode = common_vendor.ref("ad");
    const selectedOption = common_vendor.ref(null);
    common_vendor.onMounted((options) => {
      if (options && options.mode) {
        publishMode.value = options.mode;
      }
    });
    const chooseLocation = (type, index) => {
      common_vendor.index.chooseLocation({
        success: (res) => {
          if (type === "start") {
            formData.value.startPoint = res.name;
          } else if (type === "end") {
            formData.value.endPoint = res.name;
          } else if (type === "via") {
            if (index !== void 0) {
              formData.value.viaPoints[index] = res.name;
            } else {
              formData.value.viaPoints.push(res.name);
            }
          }
        }
      });
    };
    const onDateChange = (e) => {
      formData.value.departureDate = e.detail.value;
    };
    const onTimeChange = (e) => {
      formData.value.departureTime = e.detail.value;
    };
    const onPassengerChange = (e) => {
      passengerIndex.value = e.detail.value;
      formData.value.passengers = passengerOptions.value[passengerIndex.value];
    };
    const onAgreementChange = (e) => {
      formData.value.agreement = e.detail.value.length > 0;
    };
    const viewAgreement = () => {
      common_vendor.index.navigateTo({
        url: "/pages/carpool/agreement"
      });
    };
    const submitForm = () => {
      if (!formData.value.startPoint) {
        showToast("请输入出发地");
        return;
      }
      if (!formData.value.endPoint) {
        showToast("请输入目的地");
        return;
      }
      if (!formData.value.departureDate) {
        showToast("请选择出发日期");
        return;
      }
      if (!formData.value.departureTime) {
        showToast("请选择出发时间");
        return;
      }
      if (!formData.value.passengers) {
        showToast("请选择乘车人数");
        return;
      }
      if (!formData.value.contactPhone) {
        showToast("请输入手机号码");
        return;
      }
      if (!/^1\d{10}$/.test(formData.value.contactPhone)) {
        showToast("手机号码格式不正确");
        return;
      }
      if (!selectedOption.value) {
        showToast("请选择发布方式");
        return;
      }
      submitToServer();
    };
    const submitToServer = async () => {
      try {
        common_vendor.index.showLoading({
          title: "发布中..."
        });
        const formDataToSubmit = {
          type: "people-to-car",
          departure: formData.value.startPoint,
          destination: formData.value.endPoint,
          departure_time: `${formData.value.date} ${formData.value.time}`,
          passenger_count: parseInt(passengerOptions.value[passengerIndex.value]),
          price: parseFloat(formData.value.price) || 0,
          contact: formData.value.contact,
          description: formData.value.remark,
          via_points: formData.value.viaPoints.filter((point) => point.trim() !== ""),
          publish_mode: publishMode.value
        };
        common_vendor.index.__f__("log", "at carpool-package/pages/carpool/publish/people-to-car.vue:340", "提交的表单数据：", formDataToSubmit);
        const result = await api_index.api.carpool.publish(formDataToSubmit);
        if (result.success) {
          common_vendor.index.navigateTo({
            url: `/carpool-package/pages/carpool/publish/success?id=${result.data.id}&type=people-to-car&mode=${publishMode.value}`
          });
        } else {
          common_vendor.index.showToast({
            title: result.message || "发布失败，请重试",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at carpool-package/pages/carpool/publish/people-to-car.vue:358", "发布拼车信息失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    };
    const showToast = (title) => {
      common_vendor.index.showToast({
        title,
        icon: "none"
      });
    };
    const addViaPoint = () => {
      formData.value.viaPoints.push("");
    };
    const removeViaPoint = (index) => {
      formData.value.viaPoints.splice(index, 1);
    };
    const handleActionCompleted = (actionType, result) => {
      selectedOption.value = result;
      if (actionType === "ad") {
        publishMode.value = "ad";
      } else if (actionType === "paid") {
        publishMode.value = "premium";
      }
      common_vendor.index.__f__("log", "at carpool-package/pages/carpool/publish/people-to-car.vue:402", "选择的推广选项:", actionType, result);
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          title: "发布人找车信息"
        }),
        b: formData.value.startPoint,
        c: common_vendor.o(($event) => formData.value.startPoint = $event.detail.value),
        d: common_assets._imports_2$36,
        e: common_vendor.o(($event) => chooseLocation("start")),
        f: formData.value.endPoint,
        g: common_vendor.o(($event) => formData.value.endPoint = $event.detail.value),
        h: common_assets._imports_2$36,
        i: common_vendor.o(($event) => chooseLocation("end")),
        j: common_vendor.f(formData.value.viaPoints, (point, index, i0) => {
          return {
            a: formData.value.viaPoints[index],
            b: common_vendor.o(($event) => formData.value.viaPoints[index] = $event.detail.value, index),
            c: common_vendor.o(($event) => chooseLocation("via", index), index),
            d: common_vendor.o(($event) => removeViaPoint(index), index),
            e: index
          };
        }),
        k: common_assets._imports_2$36,
        l: formData.value.viaPoints.length < 3
      }, formData.value.viaPoints.length < 3 ? {
        m: common_vendor.o(addViaPoint)
      } : {}, {
        n: common_vendor.t(formData.value.departureDate || "请选择出发日期"),
        o: common_assets._imports_0$27,
        p: formData.value.departureDate,
        q: common_vendor.o(onDateChange),
        r: common_vendor.t(formData.value.departureTime || "请选择出发时间"),
        s: common_assets._imports_0$27,
        t: formData.value.departureTime,
        v: common_vendor.o(onTimeChange),
        w: common_vendor.t(formData.value.passengers || "请选择乘车人数"),
        x: common_assets._imports_0$27,
        y: passengerOptions.value,
        z: passengerIndex.value,
        A: common_vendor.o(onPassengerChange),
        B: formData.value.contactName,
        C: common_vendor.o(($event) => formData.value.contactName = $event.detail.value),
        D: formData.value.contactPhone,
        E: common_vendor.o(($event) => formData.value.contactPhone = $event.detail.value),
        F: formData.value.remark,
        G: common_vendor.o(($event) => formData.value.remark = $event.detail.value),
        H: common_vendor.t(formData.value.remark.length),
        I: formData.value.agreement,
        J: common_vendor.o(viewAgreement),
        K: common_vendor.o(onAgreementChange),
        L: common_vendor.o(handleActionCompleted),
        M: common_vendor.p({
          pageType: "publish",
          showMode: "direct"
        }),
        N: !formData.value.agreement,
        O: common_vendor.o(submitForm)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/carpool-package/pages/carpool/publish/people-to-car.js.map
