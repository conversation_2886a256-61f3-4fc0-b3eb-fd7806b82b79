<template>
  <view class="wallet-container">
    <!-- 自定义导航栏 -->
    <custom-navbar title="我的钱包" :show-back="true"></custom-navbar>
    
    <!-- 内容区域 -->
    <view class="content-container">
      <!-- 钱包卡片 -->
      <view class="wallet-card">
        <view class="wallet-header">
          <text class="wallet-title">可提现金额</text>
          <view class="wallet-amount">
            <text class="currency">¥</text>
            <text class="amount">{{ walletInfo.balance }}</text>
          </view>
        </view>
        <view class="wallet-actions">
          <button class="action-button" @tap="navigateToWithdraw">
            <text>提现</text>
          </button>
          <button class="action-button action-button--outline" @tap="navigateToWithdrawRecords">
            <text>提现记录</text>
          </button>
        </view>
      </view>
      
      <!-- 收益统计 -->
      <view class="stats-section">
        <view class="stats-item">
          <text class="stats-value">{{ walletInfo.totalIncome }}</text>
          <text class="stats-label">累计返利(元)</text>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <text class="stats-value">{{ walletInfo.pendingAmount }}</text>
          <text class="stats-label">待结算(元)</text>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <text class="stats-value">{{ withdrawnAmount }}</text>
          <text class="stats-label">已提现(元)</text>
        </view>
      </view>
      
      <!-- 返利记录 -->
      <view class="records-section">
        <view class="section-header">
          <text class="section-title">返利记录</text>
          <view class="section-more" @tap="navigateToCashbackRecords">
            <text>全部</text>
            <svg class="arrow-icon" viewBox="0 0 24 24" width="16" height="16">
              <path fill="#999999" d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
            </svg>
          </view>
        </view>
        
        <view class="records-list">
          <view class="record-empty" v-if="transactions.length === 0">
            <image class="empty-icon" src="/static/images/cashback/empty-records.png" mode="aspectFit"></image>
            <text>暂无返利记录</text>
          </view>
          <view class="record-item" v-for="(transaction, index) in transactions" :key="index">
            <view class="record-info">
              <text class="record-title">{{ transaction.title }}</text>
              <text class="record-time">{{ transaction.time }}</text>
            </view>
            <view class="record-amount" :class="{'record-amount--pending': transaction.status === 'pending'}">
              <text>{{ transaction.status === 'pending' ? '预估 ' : '+' }}{{ transaction.amount }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import CustomNavbar from '../../components/CustomNavbar.vue';
import { getWalletInfo, getWalletTransactions } from '@/services/cashbackService.js';

export default {
  components: {
    CustomNavbar
  },
  data() {
    return {
      walletInfo: {
        balance: '1,234.56',
        totalIncome: '3,456.78',
        pendingAmount: '789.12'
      },
      withdrawnAmount: '1,433.10',
      transactions: [
        {
          title: 'Apple iPhone 15 Pro Max',
          time: '2023-11-15 14:30',
          amount: '300.00',
          status: 'success'
        },
        {
          title: 'HUAWEI Mate 60 Pro',
          time: '2023-11-10 09:45',
          amount: '200.00',
          status: 'pending'
        },
        {
          title: '小米14 Ultra',
          time: '2023-11-05 16:20',
          amount: '240.00',
          status: 'success'
        }
      ]
    };
  },
  onLoad() {
    // 设置页面不显示系统导航栏
    uni.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#9C27B0'
    });
    
    // 加载钱包信息和交易记录
    this.loadWalletInfo();
    this.loadTransactions();
  },
  methods: {
    // 加载钱包信息
    async loadWalletInfo() {
      try {
        const result = await getWalletInfo();
        if (result && result.code === 0) {
          this.walletInfo = result.data;
        } else {
          uni.showToast({
            title: result.message || '加载钱包信息失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('加载钱包信息失败:', error);
      }
    },
    
    // 加载交易记录
    async loadTransactions() {
      try {
        const params = {
          page: 1,
          pageSize: 5
        };
        
        const result = await getWalletTransactions(params);
        if (result && result.code === 0) {
          this.transactions = result.data.transactions || [];
        }
      } catch (error) {
        console.error('加载交易记录失败:', error);
      }
    },
    
    // 导航到提现页面
    navigateToWithdraw() {
      uni.navigateTo({
        url: '/subPackages/cashback/pages/withdraw/index'
      });
    },
    
    // 导航到提现记录页面
    navigateToWithdrawRecords() {
      uni.navigateTo({
        url: '/subPackages/cashback/pages/withdraw-records/index'
      });
    },
    
    // 导航到返利记录页面
    navigateToCashbackRecords() {
      uni.navigateTo({
        url: '/subPackages/cashback/pages/cashback-records/index'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.wallet-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content-container {
  padding-top: calc(var(--status-bar-height) + 44px);
  padding-bottom: 20px;
}

.wallet-card {
  margin: 16px;
  padding: 24px;
  background: linear-gradient(135deg, #AB47BC 0%, #9C27B0 100%);
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(156, 39, 176, 0.2);
  
  .wallet-header {
    margin-bottom: 24px;
  }
  
  .wallet-title {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 8px;
    display: block;
  }
  
  .wallet-amount {
    display: flex;
    align-items: baseline;
    
    .currency {
      font-size: 20px;
      color: #FFFFFF;
      margin-right: 4px;
    }
    
    .amount {
      font-size: 36px;
      font-weight: 600;
      color: #FFFFFF;
    }
  }
  
  .wallet-actions {
    display: flex;
    gap: 16px;
    
    .action-button {
      flex: 1;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 20px;
      background-color: #FFFFFF;
      border: none;
      padding: 0;
      
      text {
        font-size: 14px;
        font-weight: 500;
        color: #9C27B0;
      }
      
      &--outline {
        background-color: transparent;
        border: 1px solid #FFFFFF;
        
        text {
          color: #FFFFFF;
        }
      }
    }
  }
}

.stats-section {
  margin: 16px;
  padding: 16px;
  background-color: #FFFFFF;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  
  .stats-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .stats-value {
      font-size: 18px;
      font-weight: 600;
      color: #333333;
      margin-bottom: 4px;
    }
    
    .stats-label {
      font-size: 12px;
      color: #999999;
    }
  }
  
  .stats-divider {
    width: 1px;
    height: 30px;
    background-color: #EEEEEE;
  }
}

.records-section {
  margin: 16px;
  padding: 16px;
  background-color: #FFFFFF;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333333;
  }
  
  .section-more {
    display: flex;
    align-items: center;
    
    text {
      font-size: 14px;
      color: #999999;
    }
  }
}

.records-list {
  .record-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 32px 0;
    
    .empty-icon {
      width: 80px;
      height: 80px;
      margin-bottom: 16px;
    }
    
    text {
      font-size: 14px;
      color: #999999;
    }
  }
  
  .record-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #EEEEEE;
    
    &:last-child {
      border-bottom: none;
    }
    
    .record-info {
      flex: 1;
      
      .record-title {
        font-size: 14px;
        color: #333333;
        margin-bottom: 4px;
        display: block;
      }
      
      .record-time {
        font-size: 12px;
        color: #999999;
      }
    }
    
    .record-amount {
      font-size: 16px;
      font-weight: 500;
      color: #FF6B6B;
      
      &--pending {
        color: #FFA726;
      }
    }
  }
}
</style> 