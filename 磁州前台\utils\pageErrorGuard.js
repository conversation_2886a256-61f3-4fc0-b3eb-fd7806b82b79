/**
 * 页面错误守卫工具
 * 用于在页面生命周期和方法中捕获并处理错误
 */
import { handleError } from './errorHandler'

/**
 * 创建页面错误守卫混入
 * @param {Object} options 配置选项
 * @returns {Object} Vue Mixin对象
 */
export function createPageErrorGuard(options = {}) {
  const {
    enableGlobalGuard = true,    // 是否启用全局守卫
    logErrors = true,            // 是否记录错误日志
    showErrorNotify = true,      // 是否显示错误通知
    errorPage = '/pages/error/index' // 严重错误时跳转的页面
  } = options
  
  // 生命周期钩子列表
  const lifecycleHooks = [
    'onLoad',
    'onShow',
    'onReady',
    'onPullDownRefresh',
    'onReachBottom',
    'onTabItemTap',
    'onNavigationBarButtonTap',
    'onBackPress',
    'onNavigationBarSearchInputChanged',
    'onNavigationBarSearchInputConfirmed',
    'onNavigationBarSearchInputClicked'
  ]
  
  // 页面方法安全包装器
  const wrapMethod = (fn, methodName) => {
    if (typeof fn !== 'function') return fn
    
    return async function(...args) {
      try {
        return await fn.apply(this, args)
      } catch (error) {
        console.error(`[页面方法错误] ${methodName}:`, error)
        
        // 处理错误
        handleError(error, {
          showToast: showErrorNotify,
          silent: !logErrors
        })
        
        // 对于导航和交互类方法的错误，可能需要特殊处理
        if (methodName.includes('Navigate') || methodName.includes('Tap')) {
          // 阻止默认行为或提供后备操作
          console.log('导航或交互方法发生错误，已阻止默认行为')
        }
        
        // 返回安全的默认值以避免界面崩溃
        return null
      }
    }
  }
  
  // 生命周期方法包装器
  const wrapLifecycle = (fn, hookName) => {
    if (typeof fn !== 'function') return fn
    
    return async function(...args) {
      try {
        return await fn.apply(this, args)
      } catch (error) {
        console.error(`[页面生命周期错误] ${hookName}:`, error)
        
        // 处理错误
        const handledError = handleError(error, {
          showToast: showErrorNotify && hookName !== 'onLoad', // onLoad错误可能需要特殊处理
          silent: !logErrors
        })
        
        // onLoad错误可能意味着页面无法正常初始化，考虑跳转到错误页面
        if (hookName === 'onLoad' && handledError.type !== 'network') {
          console.error('页面加载失败，准备跳转到错误页面')
          
          // 延迟跳转以确保当前页面已经准备好
          setTimeout(() => {
            uni.navigateTo({
              url: `${errorPage}?error=${encodeURIComponent(handledError.message)}&from=${encodeURIComponent(this.route || 'unknown')}`
            })
          }, 100)
        }
      }
    }
  }
  
  // 创建混入对象
  const mixin = {
    // 注入错误处理工具函数
    created() {
      this.$handleError = handleError
      
      // 安全调用API的辅助方法
      this.$safeCall = async (promise, options = {}) => {
        try {
          return await promise
        } catch (error) {
          handleError(error, options)
          return options.defaultValue || null
        }
      }
    },
    
    // 包装页面方法
    mounted() {
      if (!enableGlobalGuard) return
      
      // 遍历组件方法，包装非生命周期方法
      for (const key in this) {
        // 跳过生命周期钩子和内部属性
        if (lifecycleHooks.includes(key) || key.startsWith('_') || key.startsWith('$')) {
          continue
        }
        
        const prop = this[key]
        // 只包装方法
        if (typeof prop === 'function' && !prop._errorGuarded) {
          const original = prop
          const wrapped = wrapMethod(original, key)
          wrapped._errorGuarded = true
          this[key] = wrapped
        }
      }
    }
  }
  
  // 添加生命周期钩子包装
  lifecycleHooks.forEach(hook => {
    const originalHook = mixin[hook]
    
    mixin[hook] = function(...args) {
      // 如果已有定义，先包装原有的钩子
      if (originalHook) {
        return wrapLifecycle(originalHook, hook).apply(this, args)
      }
      
      // 查找组件实例中的钩子
      const instanceHook = this.$options[hook]
      if (instanceHook) {
        // 如果有多个钩子（数组），全部包装
        if (Array.isArray(instanceHook)) {
          return Promise.all(
            instanceHook.map(h => wrapLifecycle(h, hook).apply(this, args))
          )
        }
        
        // 单个钩子直接包装
        return wrapLifecycle(instanceHook, hook).apply(this, args)
      }
    }
  })
  
  return mixin
}

// 导出默认的页面错误守卫混入
export default createPageErrorGuard() 