<template>
  <view class="flash-detail-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <image class="back-icon" src="/static/images/tabbar/最新返回键.png" mode="aspectFit"></image>
        </view>
        <view class="navbar-title">秒杀详情</view>
        <view class="navbar-right"></view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container" v-if="loading">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <block v-else>
      <!-- 商品轮播图 -->
      <swiper class="product-swiper" 
        :indicator-dots="true" 
        indicator-color="rgba(255,255,255,0.4)"
        indicator-active-color="#FFFFFF"
        :autoplay="true" 
        :interval="4000" 
        :duration="400"
        :style="{ marginTop: navbarHeight + 'px' }">
        <swiper-item v-for="(item, index) in product.images" :key="index">
          <image :src="item" mode="aspectFill" class="swiper-image"></image>
        </swiper-item>
      </swiper>

      <!-- 商品基本信息卡片 -->
      <view class="product-info-card">
        <!-- 倒计时区域 -->
        <view class="countdown-section">
          <view class="countdown-label">距结束</view>
          <view class="countdown-timer">
            <text class="time-block">{{ countdown.hours }}</text>
            <text class="time-colon">:</text>
            <text class="time-block">{{ countdown.minutes }}</text>
            <text class="time-colon">:</text>
            <text class="time-block">{{ countdown.seconds }}</text>
          </view>
        </view>
        
        <!-- 价格区域 -->
        <view class="price-section">
          <view class="price-main">
            <text class="price-symbol">¥</text>
            <text class="price-value">{{ product.flashPrice }}</text>
            <view class="price-tag">{{ discountPercent }}折</view>
          </view>
          <view class="original-price">¥{{ product.originalPrice }}</view>
        </view>
        
        <!-- 商品标题 -->
        <view class="product-title-row">
          <view class="flash-tag">秒杀</view>
          <text class="product-title">{{ product.title }}</text>
        </view>
        
        <!-- 销量和库存 -->
        <view class="sales-stock">
          <text class="sales-count">已售{{ product.soldCount }}件</text>
          <text class="stock-count">剩余{{ product.stock }}件</text>
        </view>
        
        <!-- 进度条 -->
        <view class="progress-section">
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: progressWidth }"></view>
          </view>
          <view class="progress-text">
            <text>已抢{{ product.soldCount }}件，限量{{ product.totalCount }}件</text>
          </view>
        </view>
        
        <!-- 分销组件 - 醒目位置 -->
        <distribution-section 
          :itemId="id"
          itemType="flash"
          :itemTitle="product.title"
          :itemPrice="product.flashPrice"
          :commissionRate="product.commissionRate || 15"
        />
      </view>
      
      <!-- 商家信息卡片 -->
      <view class="shop-card" @click="goToShop">
        <view class="shop-info">
          <image :src="product.shopLogo" class="shop-logo"></image>
          <view class="shop-details">
            <text class="shop-name">{{ product.shopName }}</text>
            <view class="shop-rating">
              <view class="rating-stars">
                <image v-for="i in 5" :key="i" 
                  :src="i <= Math.floor(product.shopRating) ? 
                    '/static/images/tabbar/星星-选中.png' : 
                    '/static/images/tabbar/星星-未选.png'" 
                  class="star-icon"></image>
              </view>
              <text class="rating-score">{{ product.shopRating }}</text>
            </view>
          </view>
        </view>
        <view class="shop-action">
          <text>进店</text>
          <text class="arrow-icon">›</text>
        </view>
      </view>
      
      <!-- 商品详情卡片 -->
      <view class="detail-card">
        <view class="detail-header">
          <text class="detail-title">商品详情</text>
        </view>
        <view class="detail-content">
          <text class="detail-text">{{ product.description }}</text>
          <image v-for="(img, index) in product.detailImages" 
            :key="index" 
            :src="img" 
            mode="widthFix" 
            class="detail-image"></image>
        </view>
      </view>
      
      <!-- 底部购买栏 -->
      <view class="bottom-bar">
        <view class="bottom-left">
          <view class="action-btn" @click="toggleFavorite">
            <image :src="isFavorite ? '/static/images/tabbar/收藏-选中.png' : '/static/images/tabbar/收藏.png'" class="action-icon"></image>
            <text>收藏</text>
          </view>
          <view class="action-btn" @click="contactService">
            <image src="/static/images/tabbar/客服.png" class="action-icon"></image>
            <text>客服</text>
          </view>
          <view class="action-btn" @click="share">
            <image src="/static/images/tabbar/分享.png" class="action-icon"></image>
            <text>分享</text>
          </view>
        </view>
        <view class="buy-buttons">
          <view class="buy-btn flash-buy" @click="buyNow">
            <text class="buy-price">¥{{ product.flashPrice }}</text>
            <text class="buy-label">立即抢购</text>
          </view>
        </view>
      </view>
    </block>
  </view>
</template>

<script>
import DistributionSection from '@/components/distribution-section.vue';

export default {
  components: {
    DistributionSection
  },
  data() {
    return {
      id: null,
      statusBarHeight: 20,
      navbarHeight: 82,
      loading: true,
      product: {},
      countdown: {
        hours: '00',
        minutes: '00',
        seconds: '00'
      },
      progressWidth: '0%',
      isFavorite: false,
      timer: null // 用于存储定时器ID
    }
  },
  computed: {
    discountPercent() {
      if (!this.product.flashPrice || !this.product.originalPrice) return '';
      const discount = (parseFloat(this.product.flashPrice) / parseFloat(this.product.originalPrice)) * 10;
      return discount.toFixed(1);
    }
  },
  onLoad(options) {
    if (options && options.id) {
      this.id = options.id;
    }
    
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 62; // 状态栏 + 标题栏高度
    
    // 模拟加载数据
    setTimeout(() => {
      this.loadProductDetail();
    }, 500);
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 加载秒杀商品详情
    loadProductDetail() {
      // 模拟API加载数据
      this.loading = true;
      
      // 在实际应用中，这里应该是从API获取数据
      setTimeout(() => {
        // 模拟数据
        this.product = {
          id: this.id || 1,
          title: 'iPhone 13 Pro Max限时秒杀',
          images: [
            '/static/demo/product1.jpg',
            '/static/demo/product2.jpg',
            '/static/demo/product3.jpg'
          ],
          detailImages: [
            '/static/demo/detail1.jpg',
            '/static/demo/detail2.jpg'
          ],
          flashPrice: '7999',
          originalPrice: '8999',
          soldCount: 156,
          totalCount: 200,
          startTime: new Date(),
          endTime: new Date(Date.now() + 24 * 60 * 60 * 1000),
          description: '苹果最新旗舰手机限时特惠，A15仿生芯片，超视网膜XDR显示屏，专业级摄像系统，超瓷晶面板，IP68级防水，5G网络。',
          shopName: '官方旗舰店',
          shopLogo: '/static/demo/shop-logo.png',
          shopRating: 4.9,
          stock: 44,
          commissionRate: 15
        };
        
        this.loading = false;
        this.startCountdown();
        this.calculateProgress();
      }, 1000);
    },
    
    // 开始倒计时
    startCountdown() {
      const endTime = this.product.endTime;
      const updateCountdown = () => {
        const now = new Date();
        const diff = endTime - now;
        
        if (diff <= 0) {
          // 秒杀已结束
          this.countdown = {
            hours: '00',
            minutes: '00',
            seconds: '00'
          };
          clearInterval(this.timer);
          return;
        }
        
        // 计算小时、分钟和秒
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((diff % (1000 * 60)) / 1000);
        
        // 格式化时间
        this.countdown = {
          hours: hours.toString().padStart(2, '0'),
          minutes: minutes.toString().padStart(2, '0'),
          seconds: seconds.toString().padStart(2, '0')
        };
      };
      
      // 立即更新一次
      updateCountdown();
      
      // 每秒更新一次
      this.timer = setInterval(updateCountdown, 1000);
    },
    
    // 计算进度条宽度
    calculateProgress() {
      const soldPercent = (this.product.soldCount / this.product.totalCount) * 100;
      this.progressWidth = soldPercent + '%';
    },
    
    // 跳转到店铺
    goToShop() {
      uni.navigateTo({
        url: `/pages/shop/detail?id=${this.product.shopId || 1}`
      });
    },
    
    // 联系客服
    contactService() {
      uni.showToast({
        title: '正在连接客服...',
        icon: 'none'
      });
    },
    
    // 切换收藏状态
    toggleFavorite() {
      this.isFavorite = !this.isFavorite;
      uni.showToast({
        title: this.isFavorite ? '收藏成功' : '已取消收藏',
        icon: 'success'
      });
    },
    
    // 立即购买
    buyNow() {
      uni.showLoading({
        title: '正在下单'
      });
      
      // 模拟API请求
      setTimeout(() => {
        uni.hideLoading();
        
        // 跳转到支付页面
        uni.navigateTo({
          url: `/pages/pay/index?amount=${this.product.flashPrice}&title=${encodeURIComponent(this.product.title)}`
        });
      }, 800);
    },
    
    // 分享
    share() {
      uni.showToast({
        title: '分享功能开发中',
        icon: 'none'
      });
    }
  },
  onUnload() {
    // 清除定时器
    if (this.timer) {
      clearInterval(this.timer);
    }
  }
};
</script>

<style lang="scss" scoped>
.flash-detail-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 120rpx; /* 为底部购买栏留出空间 */
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
  
  .navbar-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
  }
  
  .navbar-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding-top: var(--status-bar-height, 25px);
    padding-left: 30rpx;
    padding-right: 30rpx;
    box-sizing: border-box;
  }
  
  .back-btn {
    width: 40rpx;
    height: 40rpx;
    margin-right: 10rpx;
  }
  
  .back-icon {
    width: 100%;
    height: 100%;
  }
  
  .navbar-title {
    font-size: 18px;
    font-weight: 600;
    color: #FFFFFF;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  
  .loading-spinner {
    width: 80rpx;
    height: 80rpx;
    border: 6rpx solid #f3f3f3;
    border-top: 6rpx solid #FF3B30;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }
  
  .loading-text {
    font-size: 28rpx;
    color: #8E8E93;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 商品轮播图 */
.product-swiper {
  width: 100%;
  height: 750rpx; /* 轮播图高度 */
  border-bottom-left-radius: 35rpx;
  border-bottom-right-radius: 35rpx;
  overflow: hidden;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.15);
  margin-bottom: 30rpx;
  
  .swiper-image {
    width: 100%;
    height: 100%;
  }
}

/* 商品基本信息卡片 */
.product-info-card {
  background-color: #FFFFFF;
  border-radius: 35rpx;
  padding: 30rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);
  margin: 0 30rpx 30rpx 30rpx;
  transform: translateZ(0);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:active {
    transform: translateY(5rpx);
    box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.06);
  }

  .countdown-section {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    .countdown-label {
      font-size: 28rpx;
      font-weight: 600;
      color: #333333;
      margin-right: 15rpx;
    }

    .countdown-timer {
      display: flex;
      align-items: center;
    }

    .time-block {
      background-color: #FF3B30;
      border-radius: 8rpx;
      padding: 8rpx 12rpx;
      font-size: 28rpx;
      font-weight: 700;
      color: #FFFFFF;
      box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.3);
    }

    .time-colon {
      font-size: 28rpx;
      font-weight: 700;
      color: #333333;
      margin: 0 8rpx;
    }
  }

  .price-section {
    display: flex;
    align-items: baseline;
    margin-bottom: 20rpx;

    .price-main {
      display: flex;
      align-items: baseline;
      margin-right: 15rpx;
    }

    .price-symbol {
      font-size: 36rpx;
      font-weight: 700;
      color: #FF3B30;
      margin-right: 5rpx;
    }

    .price-value {
      font-size: 56rpx;
      font-weight: 800;
      color: #FF3B30;
      text-shadow: 0 2rpx 4rpx rgba(255, 59, 48, 0.2);
    }

    .price-tag {
      background: linear-gradient(135deg, #FF3B30 0%, #FF6B6B 100%);
      color: #FFFFFF;
      font-size: 24rpx;
      font-weight: 600;
      padding: 6rpx 12rpx;
      border-radius: 8rpx;
      margin-left: 15rpx;
      box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.2);
    }

    .original-price {
      font-size: 28rpx;
      color: #8E8E93;
      text-decoration: line-through;
    }
  }

  .product-title-row {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    .flash-tag {
      background: linear-gradient(135deg, #FF3B30 0%, #FF6B6B 100%);
      color: #FFFFFF;
      font-size: 24rpx;
      font-weight: 600;
      padding: 6rpx 12rpx;
      border-radius: 8rpx;
      margin-right: 15rpx;
      box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.2);
    }

    .product-title {
      font-size: 36rpx;
      font-weight: 700;
      color: #333333;
      flex: 1;
      line-height: 1.4;
    }
  }

  .sales-stock {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20rpx;
    font-size: 26rpx;
    color: #8E8E93;
  }

  .progress-section {
    margin-top: 20rpx;
    margin-bottom: 30rpx;

    .progress-bar {
      width: 100%;
      height: 12rpx;
      background-color: #E0E0E0;
      border-radius: 6rpx;
      overflow: hidden;
      margin-bottom: 10rpx;
      box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(to right, #FF3B30, #FF6B6B);
      border-radius: 6rpx;
      box-shadow: 0 2rpx 4rpx rgba(255, 59, 48, 0.2);
    }

    .progress-text {
      font-size: 24rpx;
      color: #8E8E93;
      text-align: right;
    }
  }
}

/* 商家信息卡片 */
.shop-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #FFFFFF;
  border-radius: 35rpx;
  padding: 25rpx 30rpx;
  margin: 0 30rpx 30rpx 30rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);
  transform: translateZ(0);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:active {
    transform: translateY(5rpx);
    box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.06);
  }
  
  .shop-info {
    display: flex;
    align-items: center;
  }
  
  .shop-logo {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    margin-right: 20rpx;
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  }
  
  .shop-details {
    display: flex;
    flex-direction: column;
  }
  
  .shop-name {
    font-size: 30rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 10rpx;
  }
  
  .shop-rating {
    display: flex;
    align-items: center;
  }
  
  .rating-stars {
    display: flex;
    margin-right: 10rpx;
  }
  
  .star-icon {
    width: 24rpx;
    height: 24rpx;
    margin-right: 4rpx;
  }
  
  .rating-score {
    font-size: 24rpx;
    color: #FF9500;
    font-weight: 600;
  }
  
  .shop-action {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    color: #8E8E93;
    
    .arrow-icon {
      font-size: 32rpx;
      margin-left: 5rpx;
    }
  }
}

/* 商品详情卡片 */
.detail-card {
  background-color: #FFFFFF;
  border-radius: 35rpx;
  padding: 30rpx;
  margin: 0 30rpx 30rpx 30rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);
  
  .detail-header {
    margin-bottom: 20rpx;
    border-bottom: 1rpx solid #EFEFEF;
    padding-bottom: 20rpx;
  }
  
  .detail-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
  }
  
  .detail-content {
    display: flex;
    flex-direction: column;
  }
  
  .detail-text {
    font-size: 28rpx;
    color: #666666;
    line-height: 1.6;
    margin-bottom: 20rpx;
  }
  
  .detail-image {
    width: 100%;
    margin-bottom: 20rpx;
    border-radius: 20rpx;
    box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.05);
  }
}

/* 底部购买栏 */
.bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100rpx;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.05);
  z-index: 99;
  
  .bottom-left {
    display: flex;
    align-items: center;
  }
  
  .action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 30rpx;
    
    .action-icon {
      width: 40rpx;
      height: 40rpx;
      margin-bottom: 5rpx;
    }
    
    text {
      font-size: 22rpx;
      color: #8E8E93;
    }
  }
  
  .buy-buttons {
    display: flex;
  }
  
  .buy-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 240rpx;
    height: 80rpx;
    border-radius: 40rpx;
    box-shadow: 0 8rpx 16rpx rgba(255, 59, 48, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    
    &:active {
      transform: translateY(5rpx);
      box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.15);
    }
  }
  
  .flash-buy {
    background: linear-gradient(135deg, #FF3B30 0%, #FF6B6B 100%);
    
    .buy-price {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: 2rpx;
    }
    
    .buy-label {
      font-size: 28rpx;
      font-weight: 600;
      color: #FFFFFF;
    }
  }
}
</style> 