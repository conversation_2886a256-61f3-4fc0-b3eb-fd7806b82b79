"use strict";
const common_vendor = require("../../../../../../common/vendor.js");
const _sfc_main = {
  name: "DistributionSetting",
  props: {
    // 初始分销设置
    initialSettings: {
      type: Object,
      default: () => ({
        enabled: false,
        commissionMode: "percentage",
        // 'percentage' 或 'fixed'
        commissions: {
          level1: "",
          level2: "",
          level3: ""
        },
        enableLevel3: false
      })
    },
    // 是否禁用编辑
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      distributionEnabled: this.initialSettings.enabled,
      commissionMode: this.initialSettings.commissionMode,
      level1Commission: this.initialSettings.commissions.level1,
      level2Commission: this.initialSettings.commissions.level2,
      level3Commission: this.initialSettings.commissions.level3,
      enableLevel3: this.initialSettings.enableLevel3
    };
  },
  watch: {
    initialSettings: {
      handler(newVal) {
        this.distributionEnabled = newVal.enabled;
        this.commissionMode = newVal.commissionMode;
        this.level1Commission = newVal.commissions.level1;
        this.level2Commission = newVal.commissions.level2;
        this.level3Commission = newVal.commissions.level3;
        this.enableLevel3 = newVal.enableLevel3;
      },
      deep: true
    }
  },
  methods: {
    // 切换是否启用分销
    toggleDistribution(e) {
      this.distributionEnabled = e.detail.value;
      this.emitUpdate();
    },
    // 更新佣金模式
    updateCommissionMode(mode) {
      if (this.disabled)
        return;
      this.commissionMode = mode;
      this.emitUpdate();
    },
    // 更新佣金值
    updateCommission(level, e) {
      if (this.disabled)
        return;
      this[`${level}Commission`] = e.detail.value;
      this.emitUpdate();
    },
    // 切换是否启用三级分销
    toggleLevel3(e) {
      if (this.disabled)
        return;
      this.enableLevel3 = e.detail.value;
      this.emitUpdate();
    },
    // 向父组件发送更新事件
    emitUpdate() {
      this.$emit("update", {
        enabled: this.distributionEnabled,
        commissionMode: this.commissionMode,
        commissions: {
          level1: this.level1Commission,
          level2: this.level2Commission,
          level3: this.level3Commission
        },
        enableLevel3: this.enableLevel3
      });
    }
  }
};
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_circle + _component_svg)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.distributionEnabled,
    b: common_vendor.o((...args) => $options.toggleDistribution && $options.toggleDistribution(...args)),
    c: $data.distributionEnabled
  }, $data.distributionEnabled ? common_vendor.e({
    d: common_vendor.p({
      d: "M19 5L5 19"
    }),
    e: common_vendor.p({
      cx: "6.5",
      cy: "6.5",
      r: "2.5"
    }),
    f: common_vendor.p({
      cx: "17.5",
      cy: "17.5",
      r: "2.5"
    }),
    g: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      width: "24",
      height: "24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    h: $data.commissionMode === "percentage" ? 1 : "",
    i: common_vendor.o(($event) => $options.updateCommissionMode("percentage")),
    j: common_vendor.p({
      d: "M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H7"
    }),
    k: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      width: "24",
      height: "24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    l: $data.commissionMode === "fixed" ? 1 : "",
    m: common_vendor.o(($event) => $options.updateCommissionMode("fixed")),
    n: $data.commissionMode === "percentage" ? "佣金比例" : "固定金额",
    o: common_vendor.o([($event) => $data.level1Commission = $event.detail.value, ($event) => $options.updateCommission("level1", $event)]),
    p: $data.level1Commission,
    q: common_vendor.t($data.commissionMode === "percentage" ? "%" : "元"),
    r: $data.commissionMode === "percentage" ? "佣金比例" : "固定金额",
    s: common_vendor.o([($event) => $data.level2Commission = $event.detail.value, ($event) => $options.updateCommission("level2", $event)]),
    t: $data.level2Commission,
    v: common_vendor.t($data.commissionMode === "percentage" ? "%" : "元"),
    w: $data.enableLevel3
  }, $data.enableLevel3 ? {
    x: $data.commissionMode === "percentage" ? "佣金比例" : "固定金额",
    y: common_vendor.o([($event) => $data.level3Commission = $event.detail.value, ($event) => $options.updateCommission("level3", $event)]),
    z: $data.level3Commission,
    A: common_vendor.t($data.commissionMode === "percentage" ? "%" : "元")
  } : {}, {
    B: $data.enableLevel3,
    C: common_vendor.o((...args) => $options.toggleLevel3 && $options.toggleLevel3(...args)),
    D: common_vendor.p({
      cx: "12",
      cy: "12",
      r: "10"
    }),
    E: common_vendor.p({
      d: "M12 8v4M12 16h.01"
    }),
    F: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      width: "16",
      height: "16",
      fill: "none",
      stroke: "#6B0FBE",
      ["stroke-width"]: "2"
    }),
    G: common_vendor.t($data.commissionMode === "percentage" ? "佣金比例总和建议不超过30%，以保证合理利润" : "佣金金额总和建议不超过商品价格的30%")
  }) : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/distribution/components/DistributionSetting.js.map
