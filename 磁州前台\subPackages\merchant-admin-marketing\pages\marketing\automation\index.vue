<!-- 自动化营销页面开始 -->
<template>
  <view class="automation-page">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">营销自动化</text>
      <view class="navbar-right">
        <!-- 已移除帮助按钮 -->
      </view>
    </view>
    
    <view class="content">
      <view class="header-card">
        <view class="title">营销自动化</view>
        <view class="description">设置智能化的营销触发规则，提高用户留存与转化</view>
        <view class="data-overview">
          <view class="data-item">
            <text class="value">{{ ruleList.length }}</text>
            <text class="label">已创建规则</text>
          </view>
          <view class="data-item">
            <text class="value">{{ activeRuleCount }}</text>
            <text class="label">运行中规则</text>
          </view>
          <view class="data-item">
            <text class="value">{{ totalTriggerCount }}</text>
            <text class="label">已触发次数</text>
          </view>
        </view>
      </view>
      
      <!-- 功能导航 -->
      <view class="feature-nav">
        <view class="feature-item" @click="handleCreateRule">
          <view class="icon-box">
            <u-icon name="plus" color="#6236FF" size="24"></u-icon>
          </view>
          <text>新建规则</text>
        </view>
        <view class="feature-item" @click="showTriggerLog">
          <view class="icon-box">
            <u-icon name="file-text" color="#6236FF" size="24"></u-icon>
          </view>
          <text>触发记录</text>
        </view>
        <view class="feature-item" @click="showRuleAnalysis">
          <view class="icon-box">
            <u-icon name="bar-chart" color="#6236FF" size="24"></u-icon>
          </view>
          <text>数据分析</text>
        </view>
        <view class="feature-item" @click="showRuleTemplate">
          <view class="icon-box">
            <u-icon name="grid" color="#6236FF" size="24"></u-icon>
          </view>
          <text>规则模板</text>
        </view>
      </view>
      
      <!-- 筛选区域 -->
      <view class="filter-section" v-if="ruleList.length > 0">
        <view class="filter-item" :class="{ active: currentTab === 'all' }" @click="switchTab('all')">全部</view>
        <view class="filter-item" :class="{ active: currentTab === 'active' }" @click="switchTab('active')">运行中</view>
        <view class="filter-item" :class="{ active: currentTab === 'paused' }" @click="switchTab('paused')">已暂停</view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="filteredRuleList.length === 0">
        <image src="/static/images/empty.png" mode="aspectFit" class="empty-image"></image>
        <view class="empty-text">暂无自动化规则</view>
        <view class="empty-subtext">创建规则后将自动执行营销动作</view>
        <u-button type="primary" text="创建自动化规则" @click="handleCreateRule" :customStyle="{ marginTop: '30rpx' }"></u-button>
      </view>
      
      <!-- 规则列表 -->
      <view class="rule-list" v-else>
        <view class="rule-item" v-for="(item, index) in filteredRuleList" :key="index">
          <view class="rule-header">
            <view class="left">
              <text class="rule-name">{{ item.name }}</text>
              <view :class="['status-tag', item.status === 1 ? 'active' : 'paused']">
                {{ item.status === 1 ? '运行中' : '已暂停' }}
              </view>
            </view>
            <view class="right">
              <u-switch v-model="item.status" @change="(value) => toggleRuleStatus(item, value)" 
                activeColor="#6236FF" size="22"></u-switch>
            </view>
          </view>
          
          <view class="rule-content">
            <view class="trigger-section">
              <view class="section-title">
                <u-icon name="calendar" color="#6236FF" size="16"></u-icon>
                <text>触发条件</text>
              </view>
              <view class="trigger-detail">{{ getTriggerText(item.trigger) }}</view>
            </view>
            
            <view class="action-section">
              <view class="section-title">
                <u-icon name="play-right" color="#6236FF" size="16"></u-icon>
                <text>执行动作</text>
              </view>
              <view class="action-detail">{{ getActionText(item.action) }}</view>
            </view>
            
            <view class="rule-footer">
              <view class="stats">
                <text>已触发: {{ item.triggerCount || 0 }}次</text>
                <text class="dot">•</text>
                <text>创建时间: {{ formatDate(item.createTime) }}</text>
              </view>
              
              <view class="operations">
                <view class="op-btn" @click="editRule(item)">
                  <u-icon name="edit-pen" color="#6236FF" size="16"></u-icon>
                  <text>编辑</text>
                </view>
                <view class="op-btn" @click="viewRuleDetail(item)">
                  <u-icon name="eye" color="#6236FF" size="16"></u-icon>
                  <text>查看</text>
                </view>
                <view class="op-btn" @click="deleteRule(item)">
                  <u-icon name="trash" color="#FF4D4F" size="16"></u-icon>
                  <text class="delete">删除</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 确认删除弹窗 -->
    <u-modal
      :show="showDeleteModal"
      title="删除规则"
      content="确定要删除该自动化规则吗？删除后将无法恢复。"
      @confirm="confirmDelete"
      @cancel="cancelDelete"
      confirmText="确认删除"
      cancelText="取消"
      confirmColor="#FF4D4F"
    ></u-modal>
  </view>
</template>

<script>
export default {
  data() {
    return {
      ruleList: [], // 自动化规则列表
      currentTab: 'all', // 当前选中的tab: all, active, paused
      showDeleteModal: false, // 是否显示删除确认弹窗
      currentDeleteItem: null, // 当前要删除的规则
      activeRuleCount: 0, // 活跃规则数量
      totalTriggerCount: 0, // 总触发次数
    }
  },
  computed: {
    // 根据选中的tab筛选规则列表
    filteredRuleList() {
      if (this.currentTab === 'all') {
        return this.ruleList
      } else if (this.currentTab === 'active') {
        return this.ruleList.filter(item => item.status === 1)
      } else {
        return this.ruleList.filter(item => item.status === 0)
      }
    }
  },
  onLoad() {
    this.loadRuleList()
  },
  methods: {
    // 加载规则列表
    loadRuleList() {
      // 模拟数据，实际开发中应该替换为API调用
      setTimeout(() => {
        this.ruleList = [
          {
            id: 1,
            name: '会员注册赠送优惠券',
            status: 1, // 1: 运行中, 0: 已暂停
            trigger: {
              type: 'register',
              conditions: []
            },
            action: {
              type: 'coupon',
              couponId: 10,
              couponName: '新人专享优惠券'
            },
            triggerCount: 258,
            createTime: '2023-11-20 10:30:25'
          },
          {
            id: 2,
            name: '用户下单未支付提醒',
            status: 1,
            trigger: {
              type: 'unpaidOrder',
              timeDelay: 30, // 30分钟
              conditions: []
            },
            action: {
              type: 'message',
              messageType: 'sms',
              templateId: 'SMS_001'
            },
            triggerCount: 126,
            createTime: '2023-11-15 16:45:10'
          },
          {
            id: 3,
            name: '会员生日赠送积分',
            status: 0,
            trigger: {
              type: 'birthday',
              daysBefore: 0,
              conditions: [
                { field: 'memberLevel', operator: 'gte', value: 2 }
              ]
            },
            action: {
              type: 'points',
              points: 500
            },
            triggerCount: 45,
            createTime: '2023-10-30 09:15:36'
          }
        ]
        
        // 计算活跃规则数量和总触发次数
        this.activeRuleCount = this.ruleList.filter(item => item.status === 1).length
        this.totalTriggerCount = this.ruleList.reduce((total, item) => total + (item.triggerCount || 0), 0)
      }, 500)
    },
    
    // 切换tab
    switchTab(tab) {
      this.currentTab = tab
    },
    
    // 创建新规则
    handleCreateRule() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin/pages/marketing/automation/create-rule'
      })
    },
    
    // 查看触发记录
    showTriggerLog() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin/pages/marketing/automation/trigger-log'
      })
    },
    
    // 查看数据分析
    showRuleAnalysis() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin/pages/marketing/automation/analysis'
      })
    },
    
    // 查看规则模板
    showRuleTemplate() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin/pages/marketing/automation/templates'
      })
    },
    
    // 切换规则状态
    toggleRuleStatus(item, value) {
      // 更新规则状态
      item.status = value ? 1 : 0
      // 实际开发中应该调用API更新状态
      uni.showToast({
        title: value ? '规则已启用' : '规则已暂停',
        icon: 'none'
      })
      
      // 重新计算活跃规则数量
      this.activeRuleCount = this.ruleList.filter(item => item.status === 1).length
    },
    
    // 编辑规则
    editRule(item) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin/pages/marketing/automation/edit-rule?id=${item.id}`
      })
    },
    
    // 查看规则详情
    viewRuleDetail(item) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin/pages/marketing/automation/rule-detail?id=${item.id}`
      })
    },
    
    // 删除规则
    deleteRule(item) {
      this.currentDeleteItem = item
      this.showDeleteModal = true
    },
    
    // 确认删除
    confirmDelete() {
      if (!this.currentDeleteItem) return
      
      // 从列表中移除该规则
      const index = this.ruleList.findIndex(item => item.id === this.currentDeleteItem.id)
      if (index !== -1) {
        this.ruleList.splice(index, 1)
      }
      
      // 实际开发中应该调用API删除规则
      uni.showToast({
        title: '规则已删除',
        icon: 'none'
      })
      
      // 重新计算活跃规则数量和总触发次数
      this.activeRuleCount = this.ruleList.filter(item => item.status === 1).length
      this.totalTriggerCount = this.ruleList.reduce((total, item) => total + (item.triggerCount || 0), 0)
      
      this.showDeleteModal = false
      this.currentDeleteItem = null
    },
    
    // 取消删除
    cancelDelete() {
      this.showDeleteModal = false
      this.currentDeleteItem = null
    },
    
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return ''
      // 简单处理，只显示日期部分
      return dateStr.split(' ')[0]
    },
    
    // 获取触发条件文本描述
    getTriggerText(trigger) {
      if (!trigger) return '未设置触发条件'
      
      switch (trigger.type) {
        case 'register':
          return '用户完成注册'
        case 'unpaidOrder':
          return `用户下单未支付 ${trigger.timeDelay} 分钟后`
        case 'birthday':
          return trigger.daysBefore > 0 
            ? `用户生日前 ${trigger.daysBefore} 天` 
            : '用户生日当天'
        default:
          return '未知触发条件'
      }
    },
    
    // 获取执行动作文本描述
    getActionText(action) {
      if (!action) return '未设置执行动作'
      
      switch (action.type) {
        case 'coupon':
          return `发送优惠券: ${action.couponName || action.couponId}`
        case 'message':
          return `发送${action.messageType === 'sms' ? '短信' : '消息'}通知`
        case 'points':
          return `赠送 ${action.points} 积分`
        default:
          return '未知执行动作'
      }
    },
    
    // 自定义返回按钮
    goBack() {
      uni.navigateBack()
    },
    
    // 显示帮助信息
    showHelp() {
      uni.showToast({
        title: '营销自动化帮助',
        icon: 'none'
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.automation-page {
  min-height: 100vh;
  background-color: #f5f6fa;
  
  .navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30rpx;
    height: 180rpx; /* 固定高度包含状态栏和关闭按钮, +20rpx (10px) */
    padding-top: var(--status-bar-height);
    background: #6236FF;
    position: relative;
    width: 100%;
    box-sizing: border-box;
    z-index: 999;
    
    .navbar-back {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      
      .back-icon {
        width: 20rpx;
        height: 20rpx;
        border-top: 3rpx solid #ffffff;
        border-left: 3rpx solid #ffffff;
        transform: rotate(-45deg);
      }
    }
    
    .navbar-title {
      font-size: 36rpx;
      font-weight: 500;
      color: #ffffff;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, 30%); /* 向下调整以对齐关闭键, 适应更高的导航栏 */
      white-space: nowrap;
    }
    
    .navbar-right {
      width: 60rpx;
      display: flex;
      justify-content: flex-end;
      
      .help-icon {
        width: 40rpx;
        height: 40rpx;
        border-radius: 50%;
        border: 2rpx solid #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ffffff;
        font-size: 28rpx;
      }
    }
  }
  
  .content {
    padding: 30rpx;
    padding-top: 15rpx;
  }
  
  .header-card {
    background: linear-gradient(135deg, #6236FF 0%, #9370FF 100%);
    border-radius: 20rpx;
    padding: 40rpx 30rpx;
    color: #ffffff;
    margin-bottom: 30rpx;
    box-shadow: 0 10rpx 20rpx rgba(98, 54, 255, 0.1);
    
    .title {
      font-size: 36rpx;
      font-weight: bold;
      margin-bottom: 10rpx;
    }
    
    .description {
      font-size: 26rpx;
      opacity: 0.8;
      margin-bottom: 40rpx;
    }
    
    .data-overview {
      display: flex;
      justify-content: space-between;
      
      .data-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        
        .value {
          font-size: 42rpx;
          font-weight: bold;
          margin-bottom: 8rpx;
        }
        
        .label {
          font-size: 24rpx;
          opacity: 0.8;
        }
      }
    }
  }
  
  .feature-nav {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30rpx;
    
    .feature-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      background-color: #ffffff;
      border-radius: 16rpx;
      padding: 20rpx 0;
      width: 22%;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
      
      .icon-box {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        background-color: rgba(98, 54, 255, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10rpx;
      }
      
      text {
        font-size: 24rpx;
        color: #333333;
      }
    }
  }
  
  .filter-section {
    display: flex;
    background-color: #ffffff;
    border-radius: 10rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
    
    .filter-item {
      flex: 1;
      text-align: center;
      padding: 20rpx 0;
      font-size: 28rpx;
      color: #666666;
      position: relative;
      
      &.active {
        color: #6236FF;
        font-weight: 500;
        
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 40rpx;
          height: 4rpx;
          background-color: #6236FF;
          border-radius: 2rpx;
        }
      }
    }
  }
  
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;
    background-color: #ffffff;
    border-radius: 20rpx;
    
    .empty-image {
      width: 200rpx;
      height: 200rpx;
      margin-bottom: 30rpx;
    }
    
    .empty-text {
      font-size: 30rpx;
      color: #333333;
      font-weight: 500;
      margin-bottom: 10rpx;
    }
    
    .empty-subtext {
      font-size: 26rpx;
      color: #999999;
      margin-bottom: 30rpx;
    }
  }
  
  .rule-list {
    .rule-item {
      background-color: #ffffff;
      border-radius: 20rpx;
      margin-bottom: 20rpx;
      overflow: hidden;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
      
      .rule-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30rpx;
        border-bottom: 2rpx solid #f5f5f5;
        
        .left {
          display: flex;
          align-items: center;
          
          .rule-name {
            font-size: 30rpx;
            color: #333333;
            font-weight: 500;
            margin-right: 15rpx;
          }
          
          .status-tag {
            font-size: 22rpx;
            padding: 4rpx 12rpx;
            border-radius: 20rpx;
            
            &.active {
              background-color: rgba(98, 54, 255, 0.1);
              color: #6236FF;
            }
            
            &.paused {
              background-color: rgba(150, 150, 150, 0.1);
              color: #999999;
            }
          }
        }
      }
      
      .rule-content {
        padding: 20rpx 30rpx 30rpx;
        
        .trigger-section, .action-section {
          margin-bottom: 20rpx;
          
          .section-title {
            display: flex;
            align-items: center;
            margin-bottom: 10rpx;
            
            text {
              font-size: 26rpx;
              color: #666666;
              margin-left: 10rpx;
            }
          }
          
          .trigger-detail, .action-detail {
            font-size: 28rpx;
            color: #333333;
            padding-left: 36rpx;
          }
        }
        
        .rule-footer {
          border-top: 2rpx solid #f5f5f5;
          padding-top: 20rpx;
          margin-top: 20rpx;
          
          .stats {
            display: flex;
            align-items: center;
            font-size: 24rpx;
            color: #999999;
            margin-bottom: 20rpx;
            
            .dot {
              margin: 0 10rpx;
            }
          }
          
          .operations {
            display: flex;
            
            .op-btn {
              display: flex;
              align-items: center;
              margin-right: 30rpx;
              
              text {
                font-size: 24rpx;
                color: #666666;
                margin-left: 6rpx;
                
                &.delete {
                  color: #FF4D4F;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
<!-- 自动化营销页面结束 --> 