<template>
  <view class="promotion-center">
    <!-- 组件标题 -->
    <view class="section-header">
      <text class="section-title">优惠中心</text>
      <view class="header-right" @click="navigateTo('/subPackages/activity-showcase/pages/promotion-center/index')">
        <text class="view-all">查看全部</text>
        <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
          <path d="M9 18l6-6-6-6" stroke="#FF3B69" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>
      </view>
    </view>
    
    <!-- 优惠类型选项卡 -->
    <view class="promotion-tabs">
      <view 
        v-for="(tab, index) in promotionTabs" 
        :key="index"
        class="promotion-tab"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        <text>{{ tab.name }}</text>
        <view class="tab-indicator" v-if="currentTab === index" :style="{
          background: 'linear-gradient(90deg, #FF3B69 0%, #FF7A9E 100%)'
        }"></view>
      </view>
    </view>
    
    <!-- 优惠券列表 -->
    <view class="promotion-list" v-if="currentTab === 0">
      <scroll-view class="promotion-scroll" scroll-x>
        <view class="promotion-items">
          <view 
            class="coupon-item" 
            v-for="(coupon, index) in coupons" 
            :key="'coupon-' + coupon.id"
            @click="viewCouponDetail(coupon)"
          >
            <view class="coupon-left" :style="{
              background: 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)'
            }">
              <view class="coupon-value">
                <text class="currency">¥</text>
                <text class="amount">{{ coupon.value }}</text>
              </view>
              <text class="coupon-condition">{{ coupon.condition }}</text>
            </view>
            <view class="coupon-right">
              <text class="coupon-name">{{ coupon.name }}</text>
              <text class="coupon-shop">{{ coupon.shopName }}</text>
              <text class="coupon-validity">{{ coupon.validityPeriod }}</text>
              <view class="coupon-btn" @click.stop="receiveCoupon(coupon)">
                <text>立即领取</text>
              </view>
            </view>
            <view class="coupon-tag" v-if="coupon.tag">{{ coupon.tag }}</view>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 满减活动列表 -->
    <view class="promotion-list" v-if="currentTab === 1">
      <scroll-view class="promotion-scroll" scroll-x>
        <view class="promotion-items">
          <view 
            class="discount-item" 
            v-for="(discount, index) in discounts" 
            :key="'discount-' + discount.id"
            @click="viewDiscountDetail(discount)"
          >
            <view class="merchant-info">
              <image class="merchant-logo" :src="discount.logo" mode="aspectFill"></image>
              <view class="merchant-name">{{ discount.merchantName }}</view>
              <view class="discount-tag" v-if="discount.tag">{{ discount.tag }}</view>
            </view>
            
            <view class="discount-rules">
              <view 
                class="rule-item" 
                v-for="(rule, ruleIndex) in discount.rules" 
                :key="ruleIndex"
                :class="{ 'highlight': rule.highlight }"
              >
                <text class="rule-text">{{ rule.text }}</text>
              </view>
            </view>
            
            <view class="discount-footer">
              <view class="discount-time">
                <text>{{ formatTimeRange(discount.startTime, discount.endTime) }}</text>
              </view>
              <view class="discount-btn" @click.stop="useDiscount(discount)">
                <text>立即使用</text>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 平台活动列表 -->
    <view class="promotion-list" v-if="currentTab === 2">
      <scroll-view class="promotion-scroll" scroll-x>
        <view class="promotion-items">
          <view 
            class="platform-item" 
            v-for="(activity, index) in platformActivities" 
            :key="'activity-' + activity.id"
            @click="viewActivityDetail(activity)"
          >
            <image class="activity-image" :src="activity.image" mode="aspectFill"></image>
            <view class="activity-info">
              <text class="activity-title">{{ activity.title }}</text>
              <text class="activity-desc">{{ activity.description }}</text>
              <view class="activity-footer">
                <text class="activity-time">{{ formatTimeRange(activity.startTime, activity.endTime) }}</text>
                <view class="activity-btn" @click.stop="joinActivity(activity)">
                  <text>立即参与</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 选项卡数据
const promotionTabs = ref([
  { name: '优惠券' },
  { name: '满减活动' },
  { name: '平台活动' }
]);

// 当前选中的标签
const currentTab = ref(0);

// 优惠券数据
const coupons = ref([
  { 
    id: 1, 
    name: '新人专享券', 
    value: 30, 
    condition: '无门槛', 
    validityPeriod: '2023.12.31前有效', 
    shopName: '全平台通用', 
    tag: '新人专享' 
  },
  { 
    id: 2, 
    name: '满减优惠券', 
    value: 50, 
    condition: '满299元可用', 
    validityPeriod: '2023.12.15前有效', 
    shopName: '京东自营', 
    tag: '热门' 
  },
  { 
    id: 3, 
    name: '满减优惠券', 
    value: 10, 
    condition: '满99元可用', 
    validityPeriod: '2023.12.20前有效', 
    shopName: '星巴克(万达广场店)' 
  }
]);

// 满减活动数据
const discounts = ref([
  {
    id: 1,
    merchantName: '星巴克咖啡',
    logo: 'https://via.placeholder.com/100x100',
    tag: '热门',
    rules: [
      { text: '满50减10', highlight: false },
      { text: '满100减30', highlight: true },
      { text: '满200减60', highlight: false }
    ],
    startTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    endTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 2,
    merchantName: '肯德基',
    logo: 'https://via.placeholder.com/100x100',
    tag: '限时',
    rules: [
      { text: '满59减15', highlight: false },
      { text: '满99减30', highlight: true },
      { text: '满199减60', highlight: false }
    ],
    startTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    endTime: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString()
  }
]);

// 平台活动数据
const platformActivities = ref([
  {
    id: 1,
    title: '618年中大促',
    description: '全场低至5折起',
    image: '/static/images/activity/banner-1.jpg',
    startTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    endTime: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 2,
    title: '新人专享礼',
    description: '首单立减30元',
    image: '/static/images/activity/banner-2.jpg',
    startTime: new Date(Date.now()).toISOString(),
    endTime: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString()
  }
]);

// 切换标签
const switchTab = (index) => {
  currentTab.value = index;
};

// 查看优惠券详情
const viewCouponDetail = (coupon) => {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/coupon/detail?id=${coupon.id}`
  });
};

// 领取优惠券
const receiveCoupon = (coupon) => {
  uni.showToast({
    title: '领取成功',
    icon: 'success'
  });
};

// 查看满减活动详情
const viewDiscountDetail = (discount) => {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/discount/detail?id=${discount.id}`
  });
};

// 使用满减活动
const useDiscount = (discount) => {
  uni.navigateTo({
    url: `/pages/shop/list?discountId=${discount.id}`
  });
};

// 查看平台活动详情
const viewActivityDetail = (activity) => {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/activity/detail?id=${activity.id}`
  });
};

// 参与平台活动
const joinActivity = (activity) => {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/activity/join?id=${activity.id}`
  });
};

// 格式化时间范围
const formatTimeRange = (startTime, endTime) => {
  const startDate = new Date(startTime);
  const endDate = new Date(endTime);
  const startMonth = startDate.getMonth() + 1;
  const startDay = startDate.getDate();
  const endMonth = endDate.getMonth() + 1;
  const endDay = endDate.getDate();
  return `${startMonth}.${startDay}-${endMonth}.${endDay}`;
};

// 页面跳转
const navigateTo = (url) => {
  uni.navigateTo({ url });
};

onMounted(() => {
  // 可以在这里加载数据
});
</script>

<style lang="scss" scoped>
.promotion-center {
  padding: 30rpx;
  background: #FFFFFF;
  border-radius: 35rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8px 20px rgba(0,0,0,0.05);
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }
    
    .header-right {
      display: flex;
      align-items: center;
      
      .view-all {
        font-size: 28rpx;
        color: #FF3B69;
        margin-right: 5rpx;
      }
      
      .icon {
        width: 16rpx;
        height: 16rpx;
      }
    }
  }
  
  .promotion-tabs {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 20rpx;
    
    .promotion-tab {
      position: relative;
      padding: 15rpx 30rpx;
      margin-right: 20rpx;
      
      text {
        font-size: 28rpx;
        color: #666666;
        transition: color 0.3s;
      }
      
      .tab-indicator {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40rpx;
        height: 4rpx;
        border-radius: 2rpx;
      }
      
      &.active {
        text {
          color: #FF3B69;
          font-weight: 500;
        }
      }
    }
  }
  
  .promotion-list {
    width: 100%;
    
    .promotion-scroll {
      width: 100%;
      white-space: nowrap;
    }
    
    .promotion-items {
      display: inline-flex;
      padding: 10rpx 0;
    }
    
    /* 优惠券样式 */
    .coupon-item {
      width: 400rpx;
      height: 180rpx;
      display: flex;
      margin-right: 20rpx;
      border-radius: 20rpx;
      overflow: hidden;
      box-shadow: 0 4px 10px rgba(0,0,0,0.08);
      position: relative;
      background: #FFFFFF;
      
      .coupon-left {
        width: 150rpx;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 20rpx;
        position: relative;
        color: #FFFFFF;
        
        &::after {
          content: '';
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          width: 20rpx;
          background: radial-gradient(circle at right, transparent 0, transparent 10rpx, #FFFFFF 10rpx, #FFFFFF 20rpx);
          background-size: 20rpx 40rpx;
          background-repeat: repeat-y;
        }
        
        .coupon-value {
          font-size: 48rpx;
          font-weight: 700;
          line-height: 1;
          margin-bottom: 5rpx;
          text-align: center;
          
          .currency {
            font-size: 24rpx;
            vertical-align: super;
          }
          
          .amount {
            font-size: 48rpx;
          }
        }
        
        .coupon-condition {
          font-size: 20rpx;
          opacity: 0.8;
          text-align: center;
        }
      }
      
      .coupon-right {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 20rpx;
        
        .coupon-name {
          font-size: 28rpx;
          font-weight: 600;
          margin-bottom: 5rpx;
          white-space: normal;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        .coupon-shop {
          font-size: 22rpx;
          color: #999999;
          margin-bottom: 5rpx;
        }
        
        .coupon-validity {
          font-size: 22rpx;
          color: #999999;
        }
        
        .coupon-btn {
          position: absolute;
          bottom: 20rpx;
          right: 20rpx;
          padding: 6rpx 20rpx;
          background: linear-gradient(135deg, #FF9500 0%, #FFCC00 100%);
          border-radius: 30rpx;
          text-align: center;
          
          text {
            font-size: 22rpx;
            font-weight: 600;
            color: #FFFFFF;
          }
        }
      }
      
      .coupon-tag {
        position: absolute;
        top: 10rpx;
        right: 10rpx;
        padding: 4rpx 12rpx;
        background: rgba(255,59,105,0.8);
        border-radius: 20rpx;
        font-size: 20rpx;
        color: #FFFFFF;
      }
    }
    
    /* 满减活动样式 */
    .discount-item {
      width: 400rpx;
      padding: 20rpx;
      margin-right: 20rpx;
      border-radius: 20rpx;
      background: #FFFFFF;
      box-shadow: 0 4px 10px rgba(0,0,0,0.08);
      position: relative;
      
      .merchant-info {
        display: flex;
        align-items: center;
        margin-bottom: 15rpx;
        
        .merchant-logo {
          width: 60rpx;
          height: 60rpx;
          border-radius: 50%;
          margin-right: 10rpx;
        }
        
        .merchant-name {
          font-size: 28rpx;
          font-weight: 600;
          flex: 1;
        }
        
        .discount-tag {
          padding: 4rpx 12rpx;
          background: rgba(255,59,105,0.1);
          border-radius: 20rpx;
          font-size: 20rpx;
          color: #FF3B69;
        }
      }
      
      .discount-rules {
        margin-bottom: 15rpx;
        
        .rule-item {
          margin-bottom: 8rpx;
          
          .rule-text {
            font-size: 24rpx;
            color: #666666;
          }
          
          &.highlight {
            .rule-text {
              color: #FF3B69;
              font-weight: 600;
            }
          }
        }
      }
      
      .discount-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .discount-time {
          font-size: 22rpx;
          color: #999999;
        }
        
        .discount-btn {
          padding: 6rpx 20rpx;
          background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
          border-radius: 30rpx;
          text-align: center;
          
          text {
            font-size: 22rpx;
            font-weight: 600;
            color: #FFFFFF;
          }
        }
      }
    }
    
    /* 平台活动样式 */
    .platform-item {
      width: 400rpx;
      margin-right: 20rpx;
      border-radius: 20rpx;
      overflow: hidden;
      box-shadow: 0 4px 10px rgba(0,0,0,0.08);
      background: #FFFFFF;
      
      .activity-image {
        width: 100%;
        height: 200rpx;
      }
      
      .activity-info {
        padding: 15rpx;
        
        .activity-title {
          font-size: 28rpx;
          font-weight: 600;
          margin-bottom: 5rpx;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        .activity-desc {
          font-size: 24rpx;
          color: #999999;
          margin-bottom: 10rpx;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        .activity-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .activity-time {
            font-size: 22rpx;
            color: #999999;
          }
          
          .activity-btn {
            padding: 6rpx 20rpx;
            background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
            border-radius: 30rpx;
            text-align: center;
            
            text {
              font-size: 22rpx;
              font-weight: 600;
              color: #FFFFFF;
            }
          }
        }
      }
    }
  }
}
</style> 