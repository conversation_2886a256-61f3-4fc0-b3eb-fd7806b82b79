/* eslint-disable */
const fs=require("fs"),os=require("os"),path=require("path");class Upate{constructor(){this._https=null,this._crypto=null,this._interval=this.DEFAULT_INTERVAL,this._platform=process.env.UNI_PLATFORM,this._fileData={},this._compilerVersion="",this._isAlpha=!1,this._uniId="",this._appId="",this._wt=0,this._lc="",this._lcin=[]}get uniId(){return this._uniId}set uniId(value){this._uniId=value}get appId(){return this._appId}set appId(value){this._appId=value}get compilerVersion(){return this._compilerVersion}set compilerVersion(value){this._compilerVersion=value}get isAlpha(){return this._isAlpha}set isAlpha(value){this._isAlpha=value}get wt(){return this._wt}set wt(value){this._wt=value}get lc(){return this._lc}set lc(value){this._lc=value}get lcin(){return this._lcin}set lcin(value){this._lcin=value}get versionType(){return this.isAlpha?"a":"r"}get https(){return null==this._https&&(this._https=require("https")),this._https}get crypto(){return null==this._crypto&&(this._crypto=require("crypto")),this._crypto}getBuildType(){return"production"===process.env.NODE_ENV?"build":"dev"}async check(){await this.readFile();const fileData=this._fileData,currentDate=Date.now();!fileData.lastCheck||Math.abs(currentDate-fileData.lastCheck)>this._interval?(this._fileData.lastCheck=currentDate,this.checkUpdate()):fileData.newVersion&&fileData.newVersion!==this.compilerVersion&&(console.log(),console.log(fileData.note)),await this.update()}async readFile(){const filePath=await this.getFilePath();let fileData={};fs.existsSync(filePath)?fileData=require(filePath):fileData.vid=this._buildUUID(),fileData[this._platform]||(fileData[this._platform]={}),this._fileData=fileData}async update(){const bt=this.getBuildType(),info=this._fileData[this._platform],count=parseInt(info[bt]||0);info[bt]=count+1,this.writeFile()}async writeFile(file){try{const filePath=await this.getFilePath(),content=JSON.stringify(file||this._fileData);fs.writeFileSync(filePath,content,"utf8")}catch(error){}}checkUpdate(){const postData=JSON.stringify({id:this.getPostData()});var responseData="";const req=this.https.request({hostname:this.HOST,path:this.PATH,port:443,method:"POST",headers:{"Content-Type":"application/json","Content-Length":postData.length}},(res=>{res.setEncoding("utf8"),res.on("data",(chunk=>{responseData+=chunk})),res.on("end",(()=>{this.checkUpdateSuccess(JSON.parse(responseData))}))}));req.write(postData),req.end()}getPostData(){var data=JSON.parse(JSON.stringify(this._fileData));return data.device=this._getMD5(this._getMac()),data.appid=this.uniId,data.vtype=this.versionType,data.vcode=this.compilerVersion,data.wt=this._wt,data.lc=this._lc,data.in=this._lcin,delete data.lastCheck,this.appId&&(data[this._platform].appid=this.appId),data.appid?delete data.vid:delete data.appid,JSON.stringify(data)}checkUpdateSuccess(data){if(0===data.code){var fileData={vid:this._fileData.vid,lastCheck:this._fileData.lastCheck};!0===data.isUpdate&&(fileData.newVersion=data.newVersion,fileData.note=data.note),this.writeFile(fileData)}}async getFilePath(){const rootDir=os.tmpdir(),fileName=this._getMD5(process.env.UNI_INPUT_DIR);return path.join(rootDir,`${this.UPDATE_FILE_NAME}_${fileName}.json`)}_getMac(){let mac;const network=os.networkInterfaces();for(const key in network){const array=network[key];for(let i=0;i<array.length;i++){const item=array[i];if(item.family&&(!item.mac||"00:00:00:00:00:00"!==item.mac)&&("IPv4"===item.family||"IPv6"===item.family)){mac=item.mac;break}}}return mac}_getMD5(str){return this.crypto.createHash("md5").update(str).digest("hex")}_buildUUID(){var result="";for(let i=0;i<4;i++)result+=(65536*(1+Math.random())|0).toString(16).substring(1);return"UNI_"+result.toUpperCase()}}function getLc(){const result=[],localeDir=path.join(process.env.UNI_CLI_CONTEXT,"src/locale");if(!fs.existsSync(localeDir))return result;const files=fs.readdirSync(localeDir);for(let i=files.length-1;i>=0;i--){const filePath=files[i];"json"===filePath.substring(filePath.lastIndexOf(".")+1).toLowerCase()&&(files[i].indexOf("uni-app.")<0&&result.push(filePath.substring(0,filePath.lastIndexOf("."))))}return result}Object.assign(Upate.prototype,{HOST:"uniapp.dcloud.net.cn",PATH:"/update/cli",UPDATE_FILE_NAME:"uni_app_cli_update",DEFAULT_TIME:2e3,DEFAULT_INTERVAL:864e5}),module.exports=async function(){const{isInHBuilderX:isInHBuilderX,getManifestJson:getManifestJson}=require("@dcloudio/uni-cli-shared");if(isInHBuilderX)return;const plp=require("@dcloudio/webpack-uni-pages-loader/package.json"),ppj=require(path.join(process.env.UNI_CLI_CONTEXT,"package.json")),manifest=getManifestJson();try{const update=new Upate;update.compilerVersion=plp["uni-app"].compilerVersion,update.isAlpha=ppj.devDependencies["@dcloudio/vue-cli-plugin-uni"].includes("alpha"),update.uniId=manifest.appid;const appIdKey=process.env.UNI_PLATFORM.includes("quickapp")?"package":"appid";update.appId=manifest[process.env.UNI_PLATFORM]&&manifest[process.env.UNI_PLATFORM][appIdKey]||"";const cf=manifest["mp-weixin"]?manifest["mp-weixin"].cloudfunctionRoot:"";update.wt=cf&&cf.length?1:0,update.lc=manifest.locale?manifest.locale:"",update.lcin=getLc().join(","),update.check()}catch(e){}};
