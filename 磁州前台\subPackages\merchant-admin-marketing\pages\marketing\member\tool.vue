<!-- 会员工具页面开始 -->
<template>
  <view class="tool-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">{{ toolInfo.name || '会员工具' }}</text>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 工具内容 -->
    <view class="tool-content">
      <template v-if="toolInfo">
        <view class="tool-header">
          <view class="tool-icon" :style="{ background: toolInfo.color }">
            <image class="icon-image" :src="toolInfo.icon" mode="aspectFit"></image>
          </view>
          <view class="tool-info">
            <text class="tool-name">{{ toolInfo.name }}</text>
            <text class="tool-desc">{{ toolInfo.description }}</text>
          </view>
        </view>
        
        <!-- 工具功能区 -->
        <view class="tool-function">
          <template v-if="toolId === '1'">
            <!-- 会员积分工具 -->
            <view class="section-card">
              <view class="section-title">积分概览</view>
              <view class="stats-row">
                <view class="stat-item">
                  <text class="stat-value">256,890</text>
                  <text class="stat-label">总积分发放</text>
                </view>
                <view class="stat-item">
                  <text class="stat-value">128,450</text>
                  <text class="stat-label">总积分使用</text>
                </view>
                <view class="stat-item">
                  <text class="stat-value">128,440</text>
                  <text class="stat-label">剩余积分</text>
                </view>
              </view>
            </view>
            
            <view class="section-card">
              <view class="section-title">积分规则设置</view>
              <view class="form-item">
                <text class="form-label">消费积分比例</text>
                <view class="form-input-group">
                  <input type="number" class="form-input" v-model="pointsSettings.ratio" />
                  <text class="input-suffix">积分/元</text>
                </view>
              </view>
              <view class="form-item">
                <text class="form-label">积分有效期</text>
                <view class="form-input-group">
                  <input type="number" class="form-input" v-model="pointsSettings.validity" />
                  <text class="input-suffix">个月</text>
                </view>
              </view>
              <view class="form-item">
                <text class="form-label">积分抵扣比例</text>
                <view class="form-input-group">
                  <input type="number" class="form-input" v-model="pointsSettings.discount" />
                  <text class="input-suffix">积分/元</text>
                </view>
              </view>
              <button class="save-btn" @click="saveSettings">保存设置</button>
            </view>
          </template>
          
          <template v-else-if="toolId === '2'">
            <!-- 会员任务工具 -->
            <view class="section-card">
              <view class="section-title">任务列表</view>
              <view class="task-list">
                <view class="task-item" v-for="(task, index) in tasks" :key="index">
                  <view class="task-info">
                    <text class="task-name">{{ task.name }}</text>
                    <text class="task-reward">奖励: {{ task.reward }}</text>
                  </view>
                  <switch :checked="task.enabled" @change="(e) => toggleTask(task, e)" color="#4A00E0" />
                </view>
              </view>
              <button class="add-btn" @click="addTask">添加任务</button>
            </view>
          </template>
          
          <template v-else-if="toolId === '3'">
            <!-- 会员卡券工具 -->
            <view class="section-card">
              <view class="section-title">卡券列表</view>
              <view class="coupon-list">
                <view class="coupon-item" v-for="(coupon, index) in coupons" :key="index">
                  <view class="coupon-info">
                    <text class="coupon-name">{{ coupon.name }}</text>
                    <text class="coupon-value">{{ coupon.value }}</text>
                    <text class="coupon-validity">有效期: {{ coupon.validity }}</text>
                  </view>
                  <view class="coupon-actions">
                    <text class="action-btn edit" @click="editCoupon(coupon)">编辑</text>
                    <text class="action-btn delete" @click="deleteCoupon(coupon)">删除</text>
                  </view>
                </view>
              </view>
              <button class="add-btn" @click="addCoupon">添加卡券</button>
            </view>
          </template>
          
          <template v-else-if="toolId === '4'">
            <!-- 会员活动工具 -->
            <view class="section-card">
              <view class="section-title">活动列表</view>
              <view class="activity-list">
                <view class="activity-item" v-for="(activity, index) in activities" :key="index">
                  <view class="activity-info">
                    <text class="activity-name">{{ activity.name }}</text>
                    <text class="activity-time">{{ activity.startTime }} - {{ activity.endTime }}</text>
                    <text class="activity-status" :class="activity.status">{{ activity.statusText }}</text>
                  </view>
                  <view class="activity-actions">
                    <text class="action-btn edit" @click="editActivity(activity)">编辑</text>
                    <text class="action-btn delete" @click="deleteActivity(activity)">删除</text>
                  </view>
                </view>
              </view>
              <button class="add-btn" @click="addActivity">添加活动</button>
            </view>
          </template>
          
          <template v-else>
            <view class="empty-tip">
              <image class="empty-icon" src="/static/images/empty-data.svg"></image>
              <text class="empty-text">暂无工具内容</text>
            </view>
          </template>
        </view>
      </template>
      
      <template v-else>
        <view class="loading-container">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载中...</text>
        </view>
      </template>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      toolId: '',
      toolInfo: null,
      
      // 积分设置
      pointsSettings: {
        ratio: 10,
        validity: 12,
        discount: 100
      },
      
      // 任务列表
      tasks: [
        {
          id: 1,
          name: '每日签到',
          reward: '10积分',
          enabled: true
        },
        {
          id: 2,
          name: '分享商品',
          reward: '20积分',
          enabled: true
        },
        {
          id: 3,
          name: '评价订单',
          reward: '30积分',
          enabled: true
        },
        {
          id: 4,
          name: '完成首单',
          reward: '50积分',
          enabled: true
        }
      ],
      
      // 卡券列表
      coupons: [
        {
          id: 1,
          name: '新人优惠券',
          value: '满100减10',
          validity: '领取后30天内有效'
        },
        {
          id: 2,
          name: '会员生日券',
          value: '满200减30',
          validity: '生日当月有效'
        },
        {
          id: 3,
          name: '节日特惠券',
          value: '满300减50',
          validity: '2023-05-01至2023-05-07'
        }
      ],
      
      // 活动列表
      activities: [
        {
          id: 1,
          name: '会员日特惠',
          startTime: '2023-05-20',
          endTime: '2023-05-21',
          status: 'upcoming',
          statusText: '即将开始'
        },
        {
          id: 2,
          name: '积分翻倍周',
          startTime: '2023-04-15',
          endTime: '2023-04-21',
          status: 'active',
          statusText: '进行中'
        },
        {
          id: 3,
          name: '春季促销',
          startTime: '2023-03-01',
          endTime: '2023-03-15',
          status: 'ended',
          statusText: '已结束'
        }
      ]
    };
  },
  onLoad(options) {
    if (options.id) {
      this.toolId = options.id;
      this.fetchToolInfo();
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    
    fetchToolInfo() {
      // 模拟从服务器获取工具信息
      const toolsData = [
        {
          id: '1',
          name: '会员积分',
          description: '管理会员积分规则',
          icon: '/static/images/points-icon.png',
          color: 'linear-gradient(135deg, #FF9500, #FF5E3A)'
        },
        {
          id: '2',
          name: '会员任务',
          description: '设置会员任务和奖励',
          icon: '/static/images/task-icon.png',
          color: 'linear-gradient(135deg, #F6D365, #FDA085)'
        },
        {
          id: '3',
          name: '会员卡券',
          description: '发放会员专属卡券',
          icon: '/static/images/coupon-icon.png',
          color: 'linear-gradient(135deg, #FF6FD8, #3813C2)'
        },
        {
          id: '4',
          name: '会员活动',
          description: '创建会员专属活动',
          icon: '/static/images/activity-icon.png',
          color: 'linear-gradient(135deg, #43E97B, #38F9D7)'
        }
      ];
      
      this.toolInfo = toolsData.find(tool => tool.id === this.toolId) || null;
      
      if (!this.toolInfo) {
        uni.showToast({
          title: '工具信息不存在',
          icon: 'none'
        });
      }
    },
    
    saveSettings() {
      uni.showLoading({
        title: '保存中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '设置保存成功',
          icon: 'success'
        });
      }, 1000);
    },
    
    toggleTask(task, e) {
      const index = this.tasks.findIndex(item => item.id === task.id);
      if (index !== -1) {
        this.tasks[index].enabled = e.detail.value;
      }
      
      uni.showToast({
        title: e.detail.value ? `${task.name}已启用` : `${task.name}已禁用`,
        icon: 'none'
      });
    },
    
    addTask() {
      uni.showToast({
        title: '添加任务功能开发中',
        icon: 'none'
      });
    },
    
    editCoupon(coupon) {
      uni.showToast({
        title: '编辑卡券功能开发中',
        icon: 'none'
      });
    },
    
    deleteCoupon(coupon) {
      uni.showModal({
        title: '删除确认',
        content: `确定要删除"${coupon.name}"吗？`,
        success: (res) => {
          if (res.confirm) {
            const index = this.coupons.findIndex(item => item.id === coupon.id);
            if (index !== -1) {
              this.coupons.splice(index, 1);
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              });
            }
          }
        }
      });
    },
    
    addCoupon() {
      uni.showToast({
        title: '添加卡券功能开发中',
        icon: 'none'
      });
    },
    
    editActivity(activity) {
      uni.showToast({
        title: '编辑活动功能开发中',
        icon: 'none'
      });
    },
    
    deleteActivity(activity) {
      uni.showModal({
        title: '删除确认',
        content: `确定要删除"${activity.name}"吗？`,
        success: (res) => {
          if (res.confirm) {
            const index = this.activities.findIndex(item => item.id === activity.id);
            if (index !== -1) {
              this.activities.splice(index, 1);
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              });
            }
          }
        }
      });
    },
    
    addActivity() {
      uni.showToast({
        title: '添加活动功能开发中',
        icon: 'none'
      });
    }
  }
}
</script>

<style>
/* 工具页面样式开始 */
.tool-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #8E2DE2, #4A00E0);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(74, 0, 224, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
}

/* 工具内容样式 */
.tool-content {
  padding: 20rpx;
}

.tool-header {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tool-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.icon-image {
  width: 50rpx;
  height: 50rpx;
}

.tool-info {
  flex: 1;
}

.tool-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.tool-desc {
  font-size: 24rpx;
  color: #999;
}

.section-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 积分概览 */
.stats-row {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

/* 表单样式 */
.form-item {
  margin-bottom: 20rpx;
}

.form-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.form-input-group {
  display: flex;
  align-items: center;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  overflow: hidden;
}

.form-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.input-suffix {
  padding: 0 20rpx;
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  height: 80rpx;
  line-height: 80rpx;
}

.save-btn {
  background: #4A00E0;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  margin-top: 30rpx;
}

/* 任务列表 */
.task-list {
  margin-bottom: 20rpx;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.task-item:last-child {
  border-bottom: none;
}

.task-info {
  flex: 1;
}

.task-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.task-reward {
  font-size: 24rpx;
  color: #FF9500;
}

/* 卡券列表 */
.coupon-list {
  margin-bottom: 20rpx;
}

.coupon-item {
  background: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.coupon-info {
  flex: 1;
}

.coupon-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.coupon-value {
  font-size: 26rpx;
  color: #FF6B22;
  font-weight: 600;
  margin-bottom: 8rpx;
  display: block;
}

.coupon-validity {
  font-size: 24rpx;
  color: #999;
}

.coupon-actions {
  display: flex;
  gap: 15rpx;
}

.action-btn {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
}

.action-btn.edit {
  background: rgba(74, 0, 224, 0.1);
  color: #4A00E0;
}

.action-btn.delete {
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}

/* 活动列表 */
.activity-list {
  margin-bottom: 20rpx;
}

.activity-item {
  background: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.activity-info {
  flex: 1;
}

.activity-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.activity-time {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}

.activity-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  display: inline-block;
}

.activity-status.upcoming {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.activity-status.active {
  background: rgba(0, 122, 255, 0.1);
  color: #007AFF;
}

.activity-status.ended {
  background: rgba(142, 142, 147, 0.1);
  color: #8E8E93;
}

.activity-actions {
  display: flex;
  gap: 15rpx;
}

/* 添加按钮 */
.add-btn {
  background: #4A00E0;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  margin-top: 20rpx;
}

/* 空状态 */
.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(74, 0, 224, 0.1);
  border-left: 4rpx solid #4A00E0;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}
/* 工具页面样式结束 */
</style>
<!-- 会员工具页面结束 --> 