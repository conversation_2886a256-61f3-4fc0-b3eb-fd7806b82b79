<template>
  <view class="wallet-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px', background: navbarColor }">
      <view class="navbar-left" @click="goBack">
        <image :src="backIconPath" class="back-icon"></image>
      </view>
      <view class="navbar-title">我的钱包</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 账户余额卡片 -->
    <view class="balance-card" :style="{ marginTop: (navbarHeight + 10) + 'px', background: cardGradient }">
      <view class="balance-title">账户余额 (元)</view>
      <view class="balance-amount">{{ balanceInfo.amount.toFixed(2) }}</view>
      <view class="balance-buttons">
        <button class="balance-btn withdraw-btn" @click="navigateToWithdraw">提现</button>
        <button class="balance-btn recharge-btn" @click="navigateToRecharge">充值</button>
      </view>
    </view>
    
    <!-- 钱包功能区 -->
    <view class="wallet-functions">
      <view class="function-item" @click="navigateToDetail">
        <view class="function-left">
          <view class="function-icon-block" style="background-color: #6A9DFF;"></view>
          <text class="function-name">钱包明细</text>
        </view>
        <image class="arrow-icon" src="/static/images/tabbar/arrow-up.png"></image>
      </view>
      
      <view class="function-item" @click="navigateToBills">
        <view class="function-left">
          <view class="function-icon-block" style="background-color: #FF7D54;"></view>
          <text class="function-name">收支记录</text>
        </view>
        <image class="arrow-icon" src="/static/images/tabbar/arrow-up.png"></image>
      </view>
      
      <view class="function-item" @click="navigateToBank">
        <view class="function-left">
          <view class="function-icon-block" style="background-color: #5ACB95;"></view>
          <text class="function-name">银行卡管理</text>
        </view>
        <image class="arrow-icon" src="/static/images/tabbar/arrow-up.png"></image>
      </view>
    </view>
    
    <!-- 交易记录 -->
    <view class="transaction-section">
      <view class="section-header">
        <text class="section-title">近期交易</text>
        <text class="section-more" @click="navigateToBills">查看更多</text>
      </view>
      
      <view class="transaction-list">
        <view v-if="transactions.length > 0">
          <view class="transaction-item" v-for="(item, index) in transactions" :key="index">
            <view class="transaction-left">
              <view class="transaction-title">{{ item.title }}</view>
              <view class="transaction-time">{{ item.time }}</view>
            </view>
            <view class="transaction-amount" :class="{'income': item.type === 'income', 'expense': item.type === 'expense'}">
              {{ item.type === 'income' ? '+' : '-' }}{{ item.amount.toFixed(2) }}
            </view>
          </view>
        </view>
        <view v-else class="empty-view">
          <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
          <view class="empty-text">暂无交易记录</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { smartNavigate } from '@/utils/navigation.js';

// 接收页面类型参数，用于适配不同场景
const props = defineProps({
  // 场景类型: 'main'(主钱包), 'carpool'(拼车钱包)
  scene: {
    type: String,
    default: 'main'
  },
  // 自定义路径前缀，用于导航
  pathPrefix: {
    type: String,
    default: '/subPackages/payment/pages'
  }
});

// 响应式状态
const statusBarHeight = ref(20);
const navbarHeight = ref(64);
const balanceInfo = ref({
  amount: 0.00,
  frozenAmount: 0.00
});
const transactions = ref([]);

// 根据场景设置不同的背景和图标
const cardGradient = props.scene === 'carpool' 
  ? 'linear-gradient(to right, #7ABAFF, #B0D7FF)' 
  : 'linear-gradient(to right, #7AA6FF, #A6C7FF)';

// 设置导航栏颜色
const navbarColor = 'linear-gradient(135deg, #0066FF, #0052CC)'; // 与"我的"页面保持一致的渐变色
  
const backIconPath = props.scene === 'carpool'
  ? '/static/images/tabbar/最新返回键.png'
  : '/static/images/tabbar/返回键.png';

// 页面路径映射
const getPagePath = (page) => {
  return `${props.pathPrefix}/${page}`;
};

// 方法
// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 页面跳转
const navigateTo = (url) => {
  smartNavigate(url).catch(err => {
    console.error('页面跳转失败:', err);
  });
};

// 跳转到提现页面
const navigateToWithdraw = () => {
  navigateTo(getPagePath('withdraw'));
};

// 跳转到充值页面
const navigateToRecharge = () => {
  navigateTo(getPagePath('recharge'));
};

// 跳转到明细页面
const navigateToDetail = () => {
  navigateTo(getPagePath('detail'));
};

// 跳转到账单页面
const navigateToBills = () => {
  navigateTo(getPagePath('bills'));
};

// 跳转到银行卡页面
const navigateToBank = () => {
  navigateTo(getPagePath('bank'));
};

// 获取钱包余额
const getWalletBalance = () => {
  // 这里应该是从API获取钱包余额
  // 模拟API请求
  setTimeout(() => {
    balanceInfo.value = {
      amount: 158.50,
      frozenAmount: 0.00
    };
  }, 500);
};

// 获取交易记录
const getTransactions = () => {
  // 模拟交易记录数据
  setTimeout(() => {
    transactions.value = [
      {
        id: 'tx001',
        title: '充值',
        time: '2023-11-05 14:30',
        amount: 100.00,
        type: 'income'
      },
      {
        id: 'tx002',
        title: '服务支付',
        time: '2023-11-03 09:15',
        amount: 35.00,
        type: 'expense'
      },
      {
        id: 'tx003',
        title: '提现',
        time: '2023-10-28 16:22',
        amount: 50.00,
        type: 'expense'
      }
    ];
  }, 500);
};

// 生命周期钩子
onMounted(() => {
  // 获取状态栏高度
  const sysInfo = uni.getSystemInfoSync();
  statusBarHeight.value = sysInfo.statusBarHeight;
  navbarHeight.value = statusBarHeight.value + 44;
  
  // 获取钱包余额和交易记录
  getWalletBalance();
  getTransactions();
});
</script>

<style>
.wallet-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
  background: linear-gradient(135deg, #0066FF, #0052CC); /* 默认使用与"我的"页面一致的渐变色 */
}

.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.navbar-right {
  width: 80rpx;
}

/* 余额卡片 */
.balance-card {
  margin: 0 30rpx;
  padding: 40rpx;
  border-radius: 30rpx;
  color: #fff;
  overflow: hidden;
  box-shadow: 0 8rpx 20rpx rgba(0, 82, 204, 0.15);
}

.balance-title {
  font-size: 28rpx;
  opacity: 0.9;
  margin: 0 0 20rpx 0;
}

.balance-amount {
  font-size: 60rpx;
  font-weight: bold;
  margin: 0 0 40rpx 0;
}

.balance-buttons {
  display: flex;
  justify-content: space-around;
  margin: 30rpx 0 0 0;
}

.balance-btn {
  width: 240rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  border-radius: 40rpx;
  margin: 0;
}

.withdraw-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.recharge-btn {
  background-color: #fff;
  color: #0052CC;
}

/* 钱包功能区 */
.wallet-functions {
  margin: 30rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.function-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.function-item:last-child {
  border-bottom: none;
}

.function-left {
  display: flex;
  align-items: center;
}

.function-icon-block {
  width: 44rpx;
  height: 44rpx;
  margin-right: 20rpx;
  border-radius: 10rpx;
}

.function-name {
  font-size: 28rpx;
  color: #333;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  transform: rotate(90deg);
}

/* 交易记录区域 */
.transaction-section {
  margin: 30rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  padding-bottom: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.section-more {
  font-size: 24rpx;
  color: #666;
}

.transaction-list {
  padding: 0 30rpx;
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-left {
  display: flex;
  flex-direction: column;
}

.transaction-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.transaction-time {
  font-size: 24rpx;
  color: #999;
}

.transaction-amount {
  font-size: 32rpx;
  font-weight: 500;
}

.income {
  color: #ff6b00;
}

.expense {
  color: #333;
}

/* 空状态 */
.empty-view {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style> 