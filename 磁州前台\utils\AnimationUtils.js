/**
 * 动画工具类
 * 提供常用的动画效果函数
 */

/**
 * 创建弹性动画
 * @param {Object} element - 动画元素
 * @param {Object} options - 动画选项
 * @returns {Object} - 动画对象
 */
export const createBounceAnimation = (element, options = {}) => {
  const {
    scale = 1.2,
    duration = 300
  } = options;
  
  const animation = uni.createAnimation({
    duration,
    timingFunction: 'ease'
  });
  
  animation.scale(scale).step();
  animation.scale(1).step();
  
  return animation;
};

/**
 * 创建渐入动画
 * @param {Object} element - 动画元素
 * @param {Object} options - 动画选项
 * @returns {Object} - 动画对象
 */
export const createFadeInAnimation = (element, options = {}) => {
  const {
    duration = 500,
    delay = 0
  } = options;
  
  const animation = uni.createAnimation({
    duration,
    timingFunction: 'ease',
    delay
  });
  
  animation.opacity(0).step({ duration: 0 });
  animation.opacity(1).step();
  
  return animation;
};

/**
 * 创建渐出动画
 * @param {Object} element - 动画元素
 * @param {Object} options - 动画选项
 * @returns {Object} - 动画对象
 */
export const createFadeOutAnimation = (element, options = {}) => {
  const {
    duration = 500
  } = options;
  
  const animation = uni.createAnimation({
    duration,
    timingFunction: 'ease'
  });
  
  animation.opacity(0).step();
  
  return animation;
};

/**
 * 创建滑入动画
 * @param {Object} element - 动画元素
 * @param {Object} options - 动画选项
 * @returns {Object} - 动画对象
 */
export const createSlideInAnimation = (element, options = {}) => {
  const {
    direction = 'bottom',
    duration = 500,
    distance = 100
  } = options;
  
  const animation = uni.createAnimation({
    duration,
    timingFunction: 'ease-out'
  });
  
  // 初始位置
  if (direction === 'bottom') {
    animation.translateY(distance).opacity(0).step({ duration: 0 });
    animation.translateY(0).opacity(1).step();
  } else if (direction === 'top') {
    animation.translateY(-distance).opacity(0).step({ duration: 0 });
    animation.translateY(0).opacity(1).step();
  } else if (direction === 'left') {
    animation.translateX(-distance).opacity(0).step({ duration: 0 });
    animation.translateX(0).opacity(1).step();
  } else if (direction === 'right') {
    animation.translateX(distance).opacity(0).step({ duration: 0 });
    animation.translateX(0).opacity(1).step();
  }
  
  return animation;
};

/**
 * 创建滑出动画
 * @param {Object} element - 动画元素
 * @param {Object} options - 动画选项
 * @returns {Object} - 动画对象
 */
export const createSlideOutAnimation = (element, options = {}) => {
  const {
    direction = 'bottom',
    duration = 500,
    distance = 100
  } = options;
  
  const animation = uni.createAnimation({
    duration,
    timingFunction: 'ease-in'
  });
  
  if (direction === 'bottom') {
    animation.translateY(distance).opacity(0).step();
  } else if (direction === 'top') {
    animation.translateY(-distance).opacity(0).step();
  } else if (direction === 'left') {
    animation.translateX(-distance).opacity(0).step();
  } else if (direction === 'right') {
    animation.translateX(distance).opacity(0).step();
  }
  
  return animation;
};

/**
 * 创建脉冲动画
 * @param {Object} element - 动画元素
 * @param {Object} options - 动画选项
 * @returns {Object} - 动画对象
 */
export const createPulseAnimation = (element, options = {}) => {
  const {
    scale = 1.1,
    duration = 600,
    times = 1
  } = options;
  
  const animation = uni.createAnimation({
    duration: duration / 2,
    timingFunction: 'ease'
  });
  
  for (let i = 0; i < times; i++) {
    animation.scale(scale).step();
    animation.scale(1).step();
  }
  
  return animation;
};

/**
 * 创建摇晃动画
 * @param {Object} element - 动画元素
 * @param {Object} options - 动画选项
 * @returns {Object} - 动画对象
 */
export const createShakeAnimation = (element, options = {}) => {
  const {
    angle = 5,
    duration = 500,
    times = 2
  } = options;
  
  const animation = uni.createAnimation({
    duration: duration / (times * 2),
    timingFunction: 'ease'
  });
  
  for (let i = 0; i < times; i++) {
    animation.rotate(angle).step();
    animation.rotate(-angle).step();
  }
  
  animation.rotate(0).step();
  
  return animation;
}; 