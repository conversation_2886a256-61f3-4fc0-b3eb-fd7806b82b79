"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_userProfile = require("../../utils/userProfile.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      userId: "",
      // 聊天对象ID
      nickname: "",
      // 聊天对象昵称
      chatTitle: "私信",
      // 聊天标题
      messages: [],
      // 消息列表
      messageText: "",
      // 输入框文本
      scrollIntoView: "",
      // 滚动到指定位置
      hasMoreMessages: false,
      // 是否有更多历史消息
      isLoading: false,
      // 是否正在加载
      page: 1,
      // 当前页码
      userInfo: {
        avatar: "/static/images/default-avatar.png"
      },
      // 当前用户信息
      isKeyboardShow: false,
      keyboardHeight: 0,
      showMorePanel: false
      // 是否显示扩展功能面板
    };
  },
  onLoad(options) {
    this.userId = options.userId || "";
    this.nickname = options.nickname || "用户";
    this.chatTitle = this.nickname;
    common_vendor.index.setNavigationBarColor({
      frontColor: "#ffffff",
      backgroundColor: "#0A84FF"
    });
    this.getUserInfo();
    this.loadMessages();
  },
  methods: {
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 获取当前用户信息
    getUserInfo() {
      const userInfo = utils_userProfile.getLocalUserInfo();
      if (userInfo) {
        this.userInfo = userInfo;
      }
    },
    // 加载消息
    loadMessages() {
      this.isLoading = true;
      setTimeout(() => {
        const mockMessages = this.generateMockMessages();
        if (this.page === 1) {
          this.messages = mockMessages;
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        } else {
          this.messages = [...mockMessages, ...this.messages];
        }
        this.hasMoreMessages = this.page < 3;
        this.isLoading = false;
      }, 500);
    },
    // 加载更多历史消息
    loadMoreMessages() {
      if (this.isLoading || !this.hasMoreMessages)
        return;
      this.page++;
      this.loadMessages();
    },
    // 发送消息
    sendMessage() {
      const content = this.messageText.trim();
      if (!content)
        return;
      const newMessage = {
        id: Date.now().toString(),
        content,
        time: /* @__PURE__ */ new Date(),
        isSelf: true,
        avatar: this.userInfo.avatar
      };
      this.messages.push(newMessage);
      this.messageText = "";
      this.$nextTick(() => {
        this.scrollToBottom();
      });
      setTimeout(() => {
        const replyMessage = {
          id: (Date.now() + 1).toString(),
          content: this.getRandomReply(),
          time: /* @__PURE__ */ new Date(),
          isSelf: false,
          avatar: "/static/images/default-avatar.png"
        };
        this.messages.push(replyMessage);
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }, 1e3);
    },
    // 滚动到底部
    scrollToBottom() {
      if (this.messages.length > 0) {
        this.scrollIntoView = "msg-" + (this.messages.length - 1);
      }
    },
    // 输入框获取焦点
    onInputFocus(e) {
      this.isKeyboardShow = true;
      this.keyboardHeight = e.detail.height || 0;
      this.scrollToBottom();
    },
    // 输入框失去焦点
    onInputBlur() {
      this.isKeyboardShow = false;
      this.keyboardHeight = 0;
    },
    // 判断是否显示时间分割线
    showTimeDivider(index) {
      if (index === 0)
        return true;
      const currentMsg = this.messages[index];
      const prevMsg = this.messages[index - 1];
      return new Date(currentMsg.time) - new Date(prevMsg.time) > 5 * 60 * 1e3;
    },
    // 格式化时间
    formatTime(time) {
      const date = new Date(time);
      const now = /* @__PURE__ */ new Date();
      const isToday = date.toDateString() === now.toDateString();
      const hours = date.getHours().toString().padStart(2, "0");
      const minutes = date.getMinutes().toString().padStart(2, "0");
      if (isToday) {
        return `${hours}:${minutes}`;
      } else {
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        const day = date.getDate().toString().padStart(2, "0");
        return `${month}-${day} ${hours}:${minutes}`;
      }
    },
    // 生成模拟消息数据
    generateMockMessages() {
      const mockMessages = [];
      const count = 10;
      const baseTime = /* @__PURE__ */ new Date();
      baseTime.setHours(baseTime.getHours() - this.page * 2);
      for (let i = 0; i < count; i++) {
        const isSelf = Math.random() > 0.5;
        const messageTime = new Date(baseTime);
        messageTime.setMinutes(messageTime.getMinutes() + i * 10);
        mockMessages.push({
          id: `msg_${this.page}_${i}`,
          content: this.getRandomMessage(isSelf),
          time: messageTime,
          isSelf,
          avatar: isSelf ? this.userInfo.avatar : "/static/images/default-avatar.png"
        });
      }
      return mockMessages;
    },
    // 获取随机消息内容
    getRandomMessage(isSelf) {
      const selfMessages = [
        "你好，最近怎么样？",
        "有时间一起出来喝杯咖啡吗？",
        "推荐一下附近有什么好吃的地方？",
        "最近有什么新活动吗？",
        "谢谢你的关注！"
      ];
      const otherMessages = [
        "你好，我很好，谢谢关心！",
        "最近有点忙，改天约时间吧",
        "附近新开了一家火锅店，味道不错",
        "周末有个音乐节活动，要一起去吗？",
        "不客气，我很喜欢你分享的内容"
      ];
      const messages = isSelf ? selfMessages : otherMessages;
      return messages[Math.floor(Math.random() * messages.length)];
    },
    // 获取随机回复
    getRandomReply() {
      const replies = [
        "好的，我知道了",
        "嗯嗯，没问题",
        "谢谢你告诉我这些",
        "我会考虑的",
        "有空再聊吧",
        "这个主意不错",
        "我同意你的看法",
        "需要我帮忙吗？",
        "我会尽快回复你的",
        "我明白你的意思了"
      ];
      return replies[Math.floor(Math.random() * replies.length)];
    },
    // 发送定位
    sendLocation() {
      this.showMorePanel = false;
      common_vendor.index.authorize({
        scope: "scope.userLocation",
        success: () => {
          common_vendor.index.chooseLocation({
            success: (res) => {
              if (res.name && res.address) {
                const locationMsg = {
                  id: Date.now().toString(),
                  type: "location",
                  content: `[位置] ${res.name}`,
                  locationData: {
                    name: res.name,
                    address: res.address,
                    latitude: res.latitude,
                    longitude: res.longitude
                  },
                  time: /* @__PURE__ */ new Date(),
                  isSelf: true,
                  avatar: this.userInfo.avatar
                };
                this.messages.push(locationMsg);
                this.$nextTick(() => {
                  this.scrollToBottom();
                });
                setTimeout(() => {
                  const replyMessage = {
                    id: (Date.now() + 1).toString(),
                    content: "我已收到您的位置信息",
                    time: /* @__PURE__ */ new Date(),
                    isSelf: false,
                    avatar: "/static/images/default-avatar.png"
                  };
                  this.messages.push(replyMessage);
                  this.$nextTick(() => {
                    this.scrollToBottom();
                  });
                }, 1e3);
              }
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/message/chat.vue:448", "选择位置失败", err);
              common_vendor.index.showToast({
                title: "选择位置失败",
                icon: "none"
              });
            }
          });
        },
        fail: () => {
          common_vendor.index.showModal({
            title: "提示",
            content: "需要您授权获取位置信息，是否前往设置？",
            confirmText: "去设置",
            success: (res) => {
              if (res.confirm) {
                common_vendor.index.openSetting();
              }
            }
          });
        }
      });
    },
    // 查看定位
    viewLocation(locationData) {
      common_vendor.index.openLocation({
        latitude: locationData.latitude,
        longitude: locationData.longitude,
        name: locationData.name,
        address: locationData.address,
        success: () => {
          common_vendor.index.__f__("log", "at pages/message/chat.vue:479", "打开位置成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/message/chat.vue:482", "打开位置失败", err);
          common_vendor.index.showToast({
            title: "打开位置失败",
            icon: "none"
          });
        }
      });
    },
    // 输入变化
    onInput(e) {
      if (this.messageText.trim().length > 0 && this.showMorePanel) {
        this.showMorePanel = false;
      }
    },
    // 切换扩展功能面板
    toggleMorePanel() {
      if (this.messageText.trim().length > 0) {
        this.sendMessage();
      } else {
        if (!this.showMorePanel) {
          common_vendor.index.hideKeyboard();
          setTimeout(() => {
            this.showMorePanel = true;
            setTimeout(() => {
              common_vendor.index.$once("chat-content-tap", () => {
                this.showMorePanel = false;
              });
            }, 300);
          }, 50);
        } else {
          this.showMorePanel = false;
        }
      }
    },
    // 发送图片
    sendImage() {
      common_vendor.index.chooseImage({
        count: 1,
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          const imageMsg = {
            id: Date.now().toString(),
            type: "image",
            content: "[图片]",
            imageUrl: tempFilePath,
            time: /* @__PURE__ */ new Date(),
            isSelf: true,
            avatar: this.userInfo.avatar
          };
          this.messages.push(imageMsg);
          this.showMorePanel = false;
          this.$nextTick(() => {
            this.scrollToBottom();
          });
          setTimeout(() => {
            const replyMessage = {
              id: (Date.now() + 1).toString(),
              content: "收到您发送的图片了",
              time: /* @__PURE__ */ new Date(),
              isSelf: false,
              avatar: "/static/images/default-avatar.png"
            };
            this.messages.push(replyMessage);
            this.$nextTick(() => {
              this.scrollToBottom();
            });
          }, 1e3);
        }
      });
    },
    // 发送文件
    sendFile() {
      common_vendor.index.showToast({
        title: "文件发送功能开发中",
        icon: "none"
      });
      this.showMorePanel = false;
    },
    // 发送名片
    sendContact() {
      common_vendor.index.showToast({
        title: "名片发送功能开发中",
        icon: "none"
      });
      this.showMorePanel = false;
    },
    // 预览图片
    previewImage(url) {
      common_vendor.index.previewImage({
        urls: [url],
        current: url
      });
    },
    // 关闭扩展功能面板
    closeMorePanel() {
      if (this.showMorePanel) {
        this.showMorePanel = false;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$7,
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: common_vendor.t($data.chatTitle),
    d: $data.hasMoreMessages
  }, $data.hasMoreMessages ? common_vendor.e({
    e: !$data.isLoading
  }, !$data.isLoading ? {} : {}) : {}, {
    f: common_vendor.f($data.messages, (item, index, i0) => {
      return common_vendor.e({
        a: $options.showTimeDivider(index)
      }, $options.showTimeDivider(index) ? {
        b: common_vendor.t($options.formatTime(item.time))
      } : {}, {
        c: !item.isSelf
      }, !item.isSelf ? {
        d: item.avatar
      } : {}, {
        e: !item.type || item.type === "text"
      }, !item.type || item.type === "text" ? {
        f: common_vendor.t(item.content)
      } : item.type === "location" ? {
        h: common_assets._imports_3$4,
        i: common_vendor.t(item.locationData.name),
        j: common_vendor.t(item.locationData.address),
        k: common_vendor.o(($event) => $options.viewLocation(item.locationData), index)
      } : item.type === "image" ? {
        m: item.imageUrl,
        n: common_vendor.o(($event) => $options.previewImage(item.imageUrl), index)
      } : {}, {
        g: item.type === "location",
        l: item.type === "image",
        o: item.isSelf ? 1 : "",
        p: item.isSelf
      }, item.isSelf ? {
        q: $data.userInfo.avatar
      } : {}, {
        r: "msg-" + index,
        s: item.isSelf ? 1 : "",
        t: index
      });
    }),
    g: $data.scrollIntoView,
    h: common_vendor.o((...args) => $options.loadMoreMessages && $options.loadMoreMessages(...args)),
    i: common_vendor.o((...args) => $options.closeMorePanel && $options.closeMorePanel(...args)),
    j: -1,
    k: common_vendor.o((...args) => $options.onInputFocus && $options.onInputFocus(...args)),
    l: common_vendor.o((...args) => $options.onInputBlur && $options.onInputBlur(...args)),
    m: common_vendor.o((...args) => $options.sendMessage && $options.sendMessage(...args)),
    n: common_vendor.o([($event) => $data.messageText = $event.detail.value, (...args) => $options.onInput && $options.onInput(...args)]),
    o: $data.messageText,
    p: $data.messageText.trim().length > 0
  }, $data.messageText.trim().length > 0 ? {} : {
    q: common_assets._imports_2$12
  }, {
    r: $data.messageText.trim().length > 0 ? 1 : "",
    s: common_vendor.o(($event) => $data.messageText.trim().length > 0 ? $options.sendMessage() : $options.toggleMorePanel()),
    t: $data.isKeyboardShow ? $data.keyboardHeight + "px" : "0",
    v: $data.showMorePanel
  }, $data.showMorePanel ? {
    w: common_vendor.o((...args) => $options.closeMorePanel && $options.closeMorePanel(...args))
  } : {}, {
    x: common_assets._imports_3$4,
    y: common_vendor.o((...args) => $options.sendLocation && $options.sendLocation(...args)),
    z: common_assets._imports_3$11,
    A: common_vendor.o((...args) => $options.sendImage && $options.sendImage(...args)),
    B: common_assets._imports_4$7,
    C: common_vendor.o((...args) => $options.sendFile && $options.sendFile(...args)),
    D: common_assets._imports_5$6,
    E: common_vendor.o((...args) => $options.sendContact && $options.sendContact(...args)),
    F: common_vendor.o((...args) => $options.closeMorePanel && $options.closeMorePanel(...args)),
    G: $data.showMorePanel ? 1 : "",
    H: $data.showMorePanel ? 1 : ""
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-013fa921"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/message/chat.js.map
