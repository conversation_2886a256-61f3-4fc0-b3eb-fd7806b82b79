"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      packageId: null,
      dateRange: "最近7天",
      // 套餐详情数据
      packageDetail: {
        id: 1,
        name: "四菜一汤家庭套餐",
        description: "适合3-4人用餐，经典家庭聚餐套餐，荤素搭配，营养均衡",
        createTime: "2023-04-10 14:30",
        activityTime: "2023-04-10 ~ 2023-05-10",
        statusText: "进行中",
        statusClass: "active",
        groupSize: 3,
        originalPrice: "168.00",
        groupPrice: "99.00",
        savingsAmount: "69.00",
        discountRate: "5.9",
        items: [
          { name: "红烧肉", price: "48.00", quantity: 1 },
          { name: "糖醋排骨", price: "42.00", quantity: 1 },
          { name: "鱼香肉丝", price: "38.00", quantity: 1 },
          { name: "清炒时蔬", price: "22.00", quantity: 1 },
          { name: "紫菜蛋花汤", price: "18.00", quantity: 1 }
        ],
        totalItems: 5,
        salesCount: 56,
        viewCount: 1280,
        conversionRate: 4.3,
        totalRevenue: 5544,
        recentGroups: [
          {
            id: 1,
            leaderName: "张先生",
            leaderAvatar: "/static/images/avatars/user1.jpg",
            requiredMembers: 3,
            currentMembers: 2,
            progressPercent: 66,
            timeType: "剩余时间",
            timeValue: "12小时23分",
            statusText: "拼团中",
            statusClass: "active"
          },
          {
            id: 2,
            leaderName: "李女士",
            leaderAvatar: "/static/images/avatars/user2.jpg",
            requiredMembers: 3,
            currentMembers: 3,
            progressPercent: 100,
            timeType: "成团时间",
            timeValue: "2023-04-15 18:30",
            statusText: "已成团",
            statusClass: "success"
          },
          {
            id: 3,
            leaderName: "王先生",
            leaderAvatar: "/static/images/avatars/user3.jpg",
            requiredMembers: 3,
            currentMembers: 1,
            progressPercent: 33,
            timeType: "失败时间",
            timeValue: "2023-04-14 10:15",
            statusText: "已失败",
            statusClass: "failed"
          }
        ]
      }
    };
  },
  onLoad(options) {
    if (options.id) {
      this.packageId = options.id;
      this.loadPackageDetail();
    }
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    showMoreOptions() {
      common_vendor.index.showActionSheet({
        itemList: ["复制套餐", "下架套餐", "删除套餐"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.copyPackage();
              break;
            case 1:
              this.deactivatePackage();
              break;
            case 2:
              this.confirmDeletePackage();
              break;
          }
        }
      });
    },
    loadPackageDetail() {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-marketing/pages/marketing/group/package-detail.vue:281", "加载套餐详情，ID:", this.packageId);
    },
    formatNumber(num) {
      return num.toLocaleString("zh-CN", { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    },
    showDatePicker() {
      common_vendor.index.showToast({
        title: "日期选择功能开发中",
        icon: "none"
      });
    },
    viewAllGroups() {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/group/package-groups?id=${this.packageId}`
      });
    },
    editPackage() {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/group/create?id=${this.packageId}&type=package&edit=true`
      });
    },
    sharePackage() {
      common_vendor.index.showToast({
        title: "分享功能开发中",
        icon: "none"
      });
    },
    copyPackage() {
      common_vendor.index.showToast({
        title: "复制功能开发中",
        icon: "none"
      });
    },
    deactivatePackage() {
      common_vendor.index.showModal({
        title: "确认下架",
        content: "确定要下架该套餐吗？下架后用户将无法购买。",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "下架成功",
              icon: "success"
            });
          }
        }
      });
    },
    confirmDeletePackage() {
      common_vendor.index.showModal({
        title: "确认删除",
        content: "确定要删除该套餐吗？此操作不可恢复。",
        confirmText: "删除",
        confirmColor: "#FF3B30",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "删除成功",
              icon: "success"
            });
            setTimeout(() => {
              common_vendor.index.navigateBack();
            }, 1500);
          }
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showMoreOptions && $options.showMoreOptions(...args)),
    c: common_vendor.t($data.packageDetail.name),
    d: common_vendor.t($data.packageDetail.statusText),
    e: common_vendor.n($data.packageDetail.statusClass),
    f: common_vendor.t($data.packageDetail.description),
    g: common_vendor.t($data.packageDetail.createTime),
    h: common_vendor.t($data.packageDetail.activityTime),
    i: common_vendor.t($data.packageDetail.groupSize),
    j: common_vendor.t($data.packageDetail.originalPrice),
    k: common_vendor.t($data.packageDetail.groupPrice),
    l: common_vendor.t($data.packageDetail.savingsAmount),
    m: common_vendor.t($data.packageDetail.discountRate),
    n: common_vendor.f($data.packageDetail.items, (item, index, i0) => {
      return {
        a: common_vendor.t(index + 1),
        b: common_vendor.t(item.name),
        c: common_vendor.t(item.price),
        d: common_vendor.t(item.quantity),
        e: index
      };
    }),
    o: common_vendor.t($data.packageDetail.totalItems),
    p: common_vendor.t($data.packageDetail.originalPrice),
    q: common_vendor.t($data.dateRange),
    r: common_vendor.o((...args) => $options.showDatePicker && $options.showDatePicker(...args)),
    s: common_vendor.t($data.packageDetail.salesCount),
    t: common_vendor.t($data.packageDetail.viewCount),
    v: common_vendor.t($data.packageDetail.conversionRate),
    w: common_vendor.t($options.formatNumber($data.packageDetail.totalRevenue)),
    x: common_vendor.o((...args) => $options.viewAllGroups && $options.viewAllGroups(...args)),
    y: common_vendor.f($data.packageDetail.recentGroups, (group, index, i0) => {
      return {
        a: group.leaderAvatar,
        b: common_vendor.t(group.leaderName),
        c: common_vendor.t(group.statusText),
        d: common_vendor.n(group.statusClass),
        e: common_vendor.t(group.currentMembers),
        f: common_vendor.t(group.requiredMembers),
        g: common_vendor.t(group.progressPercent),
        h: group.progressPercent + "%",
        i: common_vendor.t(group.timeType),
        j: common_vendor.t(group.timeValue),
        k: index
      };
    }),
    z: common_vendor.o((...args) => $options.editPackage && $options.editPackage(...args)),
    A: common_vendor.o((...args) => $options.sharePackage && $options.sharePackage(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/group/package-detail.js.map
