"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_rect = common_vendor.resolveComponent("rect");
  const _component_circle = common_vendor.resolveComponent("circle");
  (_component_path + _component_polyline + _component_svg + _component_rect + _component_circle)();
}
if (!Math) {
  BaseInfoCard();
}
const BaseInfoCard = () => "./BaseInfoCard.js";
const _sfc_main = {
  __name: "JobCard",
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  setup(__props) {
    const props = __props;
    const filteredBenefits = common_vendor.computed(() => {
      if (!props.item.benefits || !props.item.benefits.length)
        return [];
      if (!props.item.tags || !props.item.tags.length)
        return props.item.benefits;
      const tagsLowerCase = props.item.tags.map((tag) => tag.toLowerCase());
      return props.item.benefits.filter(
        (benefit) => !tagsLowerCase.includes(benefit.toLowerCase())
      );
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: __props.item.company
      }, __props.item.company ? common_vendor.e({
        b: __props.item.companyLogo
      }, __props.item.companyLogo ? {
        c: __props.item.companyLogo
      } : {}, {
        d: common_vendor.t(__props.item.company),
        e: __props.item.companySize
      }, __props.item.companySize ? {
        f: common_vendor.t(__props.item.companySize)
      } : {}, {
        g: __props.item.companyType
      }, __props.item.companyType ? {
        h: common_vendor.t(__props.item.companyType)
      } : {}, {
        i: __props.item.isVerified
      }, __props.item.isVerified ? {
        j: common_vendor.p({
          d: "M22 11.08V12a10 10 0 1 1-5.93-9.14"
        }),
        k: common_vendor.p({
          points: "22 4 12 14.01 9 11.01"
        }),
        l: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "#007AFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        })
      } : {}) : {}, {
        m: __props.item.education
      }, __props.item.education ? {
        n: common_vendor.p({
          d: "M22 10v6M2 10l10-5 10 5-10 5z"
        }),
        o: common_vendor.p({
          d: "M6 12v5c3 3 9 3 12 0v-5"
        }),
        p: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "14",
          height: "14",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        q: common_vendor.t(__props.item.education)
      } : {}, {
        r: __props.item.experience
      }, __props.item.experience ? {
        s: common_vendor.p({
          x: "2",
          y: "7",
          width: "20",
          height: "14",
          rx: "2",
          ry: "2"
        }),
        t: common_vendor.p({
          d: "M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"
        }),
        v: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "14",
          height: "14",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        w: common_vendor.t(__props.item.experience)
      } : {}, {
        x: __props.item.jobType
      }, __props.item.jobType ? {
        y: common_vendor.p({
          cx: "12",
          cy: "12",
          r: "10"
        }),
        z: common_vendor.p({
          points: "12 6 12 12 16 14"
        }),
        A: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "14",
          height: "14",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        B: common_vendor.t(__props.item.jobType)
      } : {}, {
        C: filteredBenefits.value.length
      }, filteredBenefits.value.length ? {
        D: common_vendor.f(filteredBenefits.value, (benefit, index, i0) => {
          return {
            a: common_vendor.t(benefit),
            b: index
          };
        })
      } : {}, {
        E: common_vendor.p({
          item: __props.item
        })
      });
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ea7d2210"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/cards/JobCard.js.map
