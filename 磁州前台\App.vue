<script>
// import { initLocationService } from './utils/locationService.js';
import { getLocalUserInfo, fetchUserInfo } from './utils/userProfile.js';
import { setupGlobalErrorHandlers } from './utils/errorHandler.js';
import { initLocationService } from './utils/locationService.js';

// 初始化全局错误处理
const { reportError } = setupGlobalErrorHandlers();

export default {
	globalData: {
		userInfo: null,
		locationInfo: null,
		// 添加错误处理相关全局数据
		errorHandling: {
			enabled: true,
			reportErrors: true,
			lastError: null
		},
		// 添加版本信息
		version: '1.0.0',
		// 返利系统配置
		cashback: {
			enabled: true,
		version: '1.0.0'
		}
	},
	onLaunch: function() {
		console.log('App Launch');
		// 检查应用版本
		this.checkVersion();
		// 检查网络状态
		this.checkNetworkStatus();
		// 初始化错误处理
		this.initErrorHandler();
		
		// 设置全局错误捕获
		this.setupErrorCatching();
		
		// 检查位置权限状态
		this.checkLocationPermission();
		
		// 初始化位置服务
		this.initLocation();
		
		// 初始化用户信息
		this.initUserInfo();
		
		// 处理剪贴板权限和提示问题
		this.handleClipboardPermission();
		
		const located = uni.getStorageSync('hasLocated');
		if (!located) {
			uni.getLocation({
				type: 'wgs84',
				success: (res) => {
					uni.setStorageSync('hasLocated', true);
					// 可在此存储定位信息
				},
				fail: () => {
					// 用户拒绝授权，不做处理
				}
			});
		}
	},
	onShow: function() {
		console.log('App Show');
	},
	onHide: function() {
		console.log('App Hide');
	},
	onError: function(err) {
		// 处理全局JS错误
		console.error('应用发生全局错误:', err);
		
		// 记录错误
		this.globalData.errorHandling.lastError = {
			message: err,
			timestamp: Date.now(),
			type: 'js_error'
		};
		
		// 上报错误
		if (this.globalData.errorHandling.reportErrors) {
			reportError({
				error: err,
				type: 'js_error',
				timestamp: Date.now()
			});
		}
	},
	onUnhandledRejection: function(e) {
		// 未捕获的Promise错误
		console.error('未处理的Promise错误:', e.reason);
		
		// 检查是否是导航错误
		if (e.reason && e.reason.errMsg && e.reason.errMsg.includes('navigateTo:fail')) {
			// 导航错误的静默处理，避免重复显示错误
			console.warn('导航错误被全局拦截:', e.reason.errMsg);
			
			// 页面不存在错误不需要上报和显示给用户，因为具体调用页面已处理
			if (e.reason.errMsg.includes('is not found')) {
				return;
			}
		}
		
		// 记录错误
		this.globalData.errorHandling.lastError = {
			message: e.reason?.message || e.reason,
			stack: e.reason?.stack,
			timestamp: Date.now(),
			type: 'promise_error'
		};
		
		// 上报错误
		if (this.globalData.errorHandling.reportErrors) {
			reportError({
				error: e.reason,
				type: 'promise_error',
				timestamp: Date.now()
			});
		}
	},
	methods: {
		// 检查应用版本
		checkVersion() {
			// 修复getApp().globalData可能为undefined的问题
			try {
				// 直接使用this.globalData替代getApp().globalData
				console.log('当前应用版本:', this.globalData.version);
				
				// 这里可以添加版本检查逻辑
				// 例如与服务器版本比对，提示用户更新等
			} catch (error) {
				console.error('版本检查出错:', error);
			}
		},
		
		// 检查网络状态
		checkNetworkStatus() {
			uni.getNetworkType({
				success: function(res) {
					if (res.networkType === 'none') {
						uni.showToast({
							title: '当前无网络连接',
							icon: 'none',
							duration: 2000
						});
					}
				}
			});
			
			// 监听网络状态变化
			uni.onNetworkStatusChange(function(res) {
				if (!res.isConnected) {
					uni.showToast({
						title: '网络连接已断开',
						icon: 'none',
						duration: 2000
					});
				} else {
					uni.showToast({
						title: '网络已连接',
						icon: 'none',
						duration: 2000
					});
				}
			});
		},
		
		// 初始化全局错误处理
		initErrorHandler() {
			// 在微信小程序环境中没有window对象，需要检查环境
			const isH5 = typeof window !== 'undefined';
			
			// 只在H5环境下添加这些事件监听器
			if (isH5) {
				// 捕获App内的未处理Promise异常
				window.addEventListener('unhandledrejection', event => {
					console.error('Unhandled Promise Rejection:', event.reason);
					// 阻止默认处理
					event.preventDefault();
				});
				
				// 捕获全局JS错误
				window.onerror = function(msg, url, line, col, error) {
					console.error('Global error:', {
						message: msg,
						url: url,
						line: line,
						column: col,
						error: error
					});
					
					// 不显示给用户，避免影响体验
					return true; // 阻止默认错误提示
				};
			}
			
			// 小程序特有的错误处理
			// 小程序环境下使用uni.onError捕获全局错误
			uni.onError(err => {
				console.error('小程序全局错误:', err);
				
				// 上报错误
				if (this.globalData.errorHandling.reportErrors) {
					reportError({
						error: err,
						type: 'mp_error',
						timestamp: Date.now()
					});
				}
			});
		},
		
		// 设置全局错误捕获
		setupErrorCatching() {
			// 拦截网络请求失败的情况
			const originalRequest = uni.request;
			uni.request = (options = {}) => {
				const original = options.fail;
				options.fail = (err) => {
					// 如果网络错误，进行自定义处理
					console.error('网络请求错误:', err);
					
					// 尝试恢复网络
					if (err.errMsg && err.errMsg.includes('fail')) {
						// 检查网络状态
						uni.getNetworkType({
							success: (res) => {
								if (res.networkType === 'none') {
									uni.showToast({
										title: '网络连接已断开，请检查网络设置',
										icon: 'none',
										duration: 2000
									});
								}
							}
						});
					}
					
					// 调用原始fail回调
					if (typeof original === 'function') {
						original(err);
					}
				};
				return originalRequest(options);
			};
				
			// 监听网络状态变化
			uni.onNetworkStatusChange((res) => {
				if (!res.isConnected) {
					// 网络断开时提示用户
					uni.showToast({
						title: '网络已断开连接',
						icon: 'none',
						duration: 2000
					});
				} else {
					// 网络恢复时可以刷新数据
					console.log('网络已恢复:', res.networkType);
			}
			});
		},
		
		// 初始化用户信息
		async initUserInfo() {
			try {
				// 1. 先从本地获取用户信息
				let userInfo = getLocalUserInfo();
				
				// 2. 如果本地没有用户信息，尝试从服务器获取
				if (!userInfo) {
					// 实际项目中，这里应该先登录获取token，然后获取用户信息
					// 这里简化处理，直接调用获取用户信息接口
					userInfo = await fetchUserInfo();
				}
				
				// 3. 保存到全局数据
				this.globalData.userInfo = userInfo;
				console.log('初始化用户信息成功:', userInfo);
			} catch (error) {
				console.error('初始化用户信息出错:', error);
				
				// 使用默认用户信息
				this.globalData.userInfo = {
					userId: 'guest',
					avatar: '/static/images/default-avatar.png',
					nickname: '访客用户',
					level: 0,
					points: 0,
					isVip: false
				};
				
				// 上报错误
				reportError({
					error: error,
					type: 'user_info_error',
					timestamp: Date.now()
				});
			}
		},
		
		// 初始化位置服务
		async initLocation() {
			try {
				console.log('应用初始化位置服务');
				
				// 检查是否首次使用应用
				const isFirstLaunch = !uni.getStorageSync('app_launched_before');
				
				if (isFirstLaunch) {
					// 标记应用已启动过
					uni.setStorageSync('app_launched_before', true);
					
					// 直接请求位置权限，不再显示提示框
					this.requestLocationPermission();
				} else {
					// 非首次启动，使用已保存的位置信息
					const savedLocation = uni.getStorageSync('user_location');
					if (savedLocation) {
						this.globalData.locationInfo = savedLocation;
					} else {
						// 如果没有保存的位置信息，尝试获取位置
						this.requestLocationPermission();
					}
				}
			} catch (error) {
				console.error('初始化位置服务出错:', error);
				// 出错时使用默认位置
				this.useDefaultLocation();
				
				// 上报错误
				reportError({
					error: error,
					type: 'location_service_error',
					timestamp: Date.now()
				});
			}
		},
		
		// 检查位置权限状态
		checkLocationPermission() {
			// 检查是否已经提示过位置权限
			uni.getStorage({
				key: 'locationAuthChecked',
				success: () => {
					console.log('已经请求过位置权限，不再自动请求');
				},
				fail: () => {
					// 第一次使用，请求位置权限
					this.requestLocationPermission();
				}
			});
		},
		
		// 请求位置权限
		requestLocationPermission() {
			// 检查是否已经请求过位置权限
			const locationAuthChecked = uni.getStorageSync('locationAuthChecked');
			if (locationAuthChecked) {
				console.log('已经请求过位置权限，不再自动请求');
				// 检查是否已有位置信息
				const savedLocation = uni.getStorageSync('user_location');
				if (savedLocation) {
					// 有保存的位置信息，直接使用
					this.globalData.locationInfo = savedLocation;
					console.log('使用已保存的位置信息');
				} else {
					// 尝试重新获取位置，但不再弹出授权框
					this.getUserLocation();
				}
				return;
			}
			
			// 标记已请求位置权限，避免重复请求
			this.setLocationChecked();
			
			uni.getSetting({
				success: (res) => {
					// 检查是否已授权位置
					if (res.authSetting && !res.authSetting['scope.userLocation']) {
						// 请求位置授权，仅请求一次
						uni.authorize({
							scope: 'scope.userLocation',
							success: () => {
								// 获取位置信息
								this.getUserLocation();
							},
							fail: () => {
								console.log('用户拒绝了位置授权请求');
								// 使用默认位置
								this.useDefaultLocation();
							}
						});
					} else {
						// 已有权限
						this.getUserLocation();
					}
				},
				fail: (err) => {
					console.error('获取授权设置失败:', err);
					// 使用默认位置
					this.useDefaultLocation();
				}
			});
		},
		
		// 标记已经请求过位置权限
		setLocationChecked() {
			uni.setStorage({
				key: 'locationAuthChecked',
				data: true,
				success: () => {
					console.log('已记录位置权限请求状态');
				}
			});
		},
		
		// 获取用户位置
		getUserLocation() {
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					// 保存位置信息
					const location = {
						latitude: res.latitude,
						longitude: res.longitude,
						timestamp: Date.now()
					};
					
					uni.setStorage({
						key: 'userLocation',
						data: location,
						success: () => {
							console.log('已保存用户位置信息');
							// 发布位置更新事件，通知需要的页面
							uni.$emit('locationUpdated', location);
						}
					});
				},
				fail: (err) => {
					console.log('获取位置失败:', err);
				}
			});
		},
		
		// 从经纬度获取地址信息
		getAddressFromLocation(latitude, longitude) {
			// 调用地图API获取地址信息
			// 这里使用uni-app提供的uni.request进行网络请求
			// 实际项目中可能需要使用专门的地图SDK或服务
			
			// 示例：使用默认实现
			const locationInfo = {
				latitude: latitude,
				longitude: longitude,
				// 以下信息通常需要通过地图API获取
				province: '正在获取...',
				city: '正在获取...',
				district: '正在获取...',
				address: '正在获取详细地址...',
				location: '正在获取位置信息...'
			};
			
			// 保存位置信息
			this.globalData.locationInfo = locationInfo;
			uni.setStorageSync('user_location', locationInfo);
			
			// 使用高德地图、百度地图或腾讯地图API进行逆地理编码
			// 这里仅使用简单示例，实际项目中需替换为真实API调用
			setTimeout(() => {
				// 模拟异步获取地址信息
				const updatedLocation = {
					...locationInfo,
					province: '河北省',
					city: '邯郸市',
					district: this.getDistrictFromCoords(latitude, longitude),
					address: '河北省邯郸市' + this.getDistrictFromCoords(latitude, longitude),
					location: '河北省 邯郸市 ' + this.getDistrictFromCoords(latitude, longitude)
				};
				
				// 更新位置信息
				this.globalData.locationInfo = updatedLocation;
				uni.setStorageSync('user_location', updatedLocation);
				
				// 触发位置更新事件
				uni.$emit('location_updated', updatedLocation);
			}, 1000);
		},
		
		// 根据坐标简单判断区县（示例实现）
		getDistrictFromCoords(latitude, longitude) {
			// 实际项目中应该使用地图API进行逆地理编码
			// 这里仅做简单示例
			if (latitude > 36.3 && latitude < 36.4 && longitude > 114.3 && longitude < 114.4) {
				return '磁县';
			} else if (latitude > 36.5 && latitude < 36.7) {
				return '丛台区';
			} else if (longitude > 114.4 && longitude < 114.6) {
				return '复兴区';
			} else {
				return '邯山区';
			}
		},
		
		// 使用默认位置
		useDefaultLocation() {
			const defaultLocation = {
				latitude: 36.313076,
				longitude: 114.347312,
				province: '河北省',
				city: '邯郸市',
				district: '磁县',
				address: '河北省邯郸市磁县',
				location: '河北省 邯郸市 磁县'
			};
			
			// 保存默认位置信息
			this.globalData.locationInfo = defaultLocation;
			uni.setStorageSync('user_location', defaultLocation);
			
			// 初始化位置服务
			initLocationService();
		},
		
		// 更新全局用户信息
		updateUserInfo(userInfo) {
			this.globalData.userInfo = userInfo;
		},
		
		// 处理剪贴板权限和提示问题
		handleClipboardPermission() {
			// #ifdef APP-PLUS
			try {
				// 延迟执行，等待应用完全初始化
				setTimeout(() => {
					// 尝试使用原生API处理剪贴板
					if (plus && plus.pasteboard) {
						// 先读取一次剪贴板内容获取权限
						plus.pasteboard.getClipboard();
						
						// Android平台处理
						if (plus.os.name.toLowerCase() === 'android') {
							// 使用Android原生API禁用剪贴板通知
							const Context = plus.android.importClass('android.content.Context');
							const activity = plus.android.runtimeMainActivity();
							const clipboard = activity.getSystemService(Context.CLIPBOARD_SERVICE);
							
							if (clipboard) {
								// 尝试移除剪贴板监听器
								if (clipboard.removePrimaryClipChangedListener) {
									const PrimaryClipChangedListener = plus.android.implements('android.content.ClipboardManager$OnPrimaryClipChangedListener', {
										onPrimaryClipChanged: function() {}
									});
									clipboard.removePrimaryClipChangedListener(PrimaryClipChangedListener);
								}
								
								// 尝试清空剪贴板通知
								try {
									const NotificationManager = activity.getSystemService(Context.NOTIFICATION_SERVICE);
									if (NotificationManager && NotificationManager.cancelAll) {
										NotificationManager.cancelAll();
									}
								} catch (e) {
									console.error('清空通知失败:', e);
								}
							}
							
							// 尝试使用反射禁用剪贴板通知
							try {
								const Class = plus.android.importClass('java.lang.Class');
								const ClipboardManagerClass = Class.forName('android.content.ClipboardManager');
								const methods = ClipboardManagerClass.getDeclaredMethods();
								
								for (let i = 0; i < methods.length; i++) {
									const method = methods[i];
									if (method.getName().indexOf('notify') !== -1 || 
										method.getName().indexOf('Notification') !== -1) {
										method.setAccessible(true);
										method.invoke(clipboard, false);
									}
								}
							} catch (e) {
								console.error('反射禁用剪贴板通知失败:', e);
							}
						} 
						// iOS平台处理
						else if (plus.os.name.toLowerCase() === 'ios') {
							// iOS可能需要特殊处理
							// 尝试使用原生方法
							try {
								const UIPasteboard = plus.ios.importClass("UIPasteboard");
								const generalPasteboard = UIPasteboard.generalPasteboard();
								// 读取一次获取权限
								generalPasteboard.string();
							} catch (e) {
								console.error('iOS剪贴板处理失败:', e);
							}
						}
					}
				}, 1000);
			} catch (e) {
				console.error('剪贴板权限处理失败:', e);
			}
			// #endif
		}
	}
}
</script>

<style>
	/*每个页面公共css */
	@import url('/static/iconfont.css');
	@import url('/static/styles/partner-styles.css');
	
	page {
		background-color: #f5f7fa;
		font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
		color: #333;
		font-size: 28rpx;
		line-height: 1.5;
	}
	
	/* 通用卡片样式 */
	.card {
		background-color: #fff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		margin-bottom: 20rpx;
		padding: 30rpx;
	}
	
	/* 主题色 */
	.primary-color {
		color: #0066FF;
	}
	
	.primary-bg {
		background-color: #0066FF;
	}
	
	/* 辅助色 */
	.success-color {
		color: #67c23a;
	}
	
	.warning-color {
		color: #e6a23c;
	}
	
	.danger-color {
		color: #f56c6c;
	}
	
	.info-color {
		color: #909399;
	}
</style>
