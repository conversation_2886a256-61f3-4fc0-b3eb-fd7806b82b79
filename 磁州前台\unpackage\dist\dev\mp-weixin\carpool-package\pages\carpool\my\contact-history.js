"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const _sfc_main = {
  __name: "contact-history",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const contactHistory = common_vendor.ref([]);
    const ratingsHistory = common_vendor.ref([]);
    common_vendor.onMounted(() => {
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight || 20;
      loadContactHistory();
      loadRatingsHistory();
      if (contactHistory.value.length === 0) {
        contactHistory.value = [
          {
            driverId: "1001",
            carpoolId: "2001",
            phoneNumber: "13812345678",
            startLocation: "磁县政府",
            endLocation: "邯郸火车站",
            departureDate: "2023-10-20",
            departureTime: "09:30",
            contactTime: (/* @__PURE__ */ new Date()).toISOString()
          },
          {
            driverId: "1002",
            carpoolId: "2002",
            phoneNumber: "13987654321",
            startLocation: "磁县老城区",
            endLocation: "邯郸科技学院",
            departureDate: "2023-10-21",
            departureTime: "14:00",
            contactTime: new Date(Date.now() - 864e5).toISOString()
            // 昨天
          }
        ];
      }
    });
    common_vendor.onShow(() => {
      loadContactHistory();
      loadRatingsHistory();
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const loadContactHistory = () => {
      const history = common_vendor.index.getStorageSync("contactHistory") || [];
      contactHistory.value = history.sort((a, b) => {
        return new Date(b.contactTime) - new Date(a.contactTime);
      });
    };
    const loadRatingsHistory = () => {
      ratingsHistory.value = common_vendor.index.getStorageSync("ratingsHistory") || [];
    };
    const getDriverName = (item) => {
      const driversInfo = common_vendor.index.getStorageSync("driversInfo") || {};
      if (driversInfo[item.driverId]) {
        return driversInfo[item.driverId].name;
      }
      return "司机" + item.phoneNumber.substr(-4);
    };
    const formatPhone = (phone) => {
      if (!phone)
        return "";
      return phone.replace(/(\d{3})(\d{4})(\d{4})/, "$1 $2 $3");
    };
    const formatDate = (dateString) => {
      const date = new Date(dateString);
      const now = /* @__PURE__ */ new Date();
      const diffDays = Math.floor((now - date) / (1e3 * 60 * 60 * 24));
      if (diffDays === 0) {
        const hours = date.getHours().toString().padStart(2, "0");
        const minutes = date.getMinutes().toString().padStart(2, "0");
        return `今天 ${hours}:${minutes}`;
      } else if (diffDays === 1) {
        return "昨天";
      } else if (diffDays < 7) {
        return `${diffDays}天前`;
      } else {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        const day = date.getDate().toString().padStart(2, "0");
        return `${year}-${month}-${day}`;
      }
    };
    const callDriver = (item) => {
      const contactHistoryData = common_vendor.index.getStorageSync("contactHistory") || [];
      const index = contactHistoryData.findIndex(
        (contact) => contact.driverId === item.driverId && contact.carpoolId === item.carpoolId
      );
      if (index !== -1) {
        contactHistoryData[index].contactTime = (/* @__PURE__ */ new Date()).toISOString();
        common_vendor.index.setStorageSync("contactHistory", contactHistoryData);
      }
      common_vendor.index.makePhoneCall({
        phoneNumber: item.phoneNumber,
        fail: (err) => {
          common_vendor.index.__f__("error", "at carpool-package/pages/carpool/my/contact-history.vue:198", "拨打电话失败", err);
          common_vendor.index.showToast({
            title: "拨打电话失败",
            icon: "none"
          });
        }
      });
    };
    const rateDriver = (item) => {
      common_vendor.index.__f__("log", "at carpool-package/pages/carpool/my/contact-history.vue:209", "点击评价司机按钮", item);
      if (!item.driverId || !item.phoneNumber) {
        common_vendor.index.showToast({
          title: "缺少司机信息，无法评价",
          icon: "none"
        });
        return;
      }
      const url = `/carpool-package/pages/carpool/my/create-rating?driverId=${item.driverId}&phoneNumber=${item.phoneNumber}&carpoolId=${item.carpoolId}&startLocation=${encodeURIComponent(item.startLocation)}&endLocation=${encodeURIComponent(item.endLocation)}&departureTime=${item.departureTime}&departureDate=${item.departureDate}`;
      common_vendor.index.__f__("log", "at carpool-package/pages/carpool/my/contact-history.vue:221", "尝试跳转到路径:", url);
      common_vendor.index.navigateTo({
        url,
        success: (res) => {
          common_vendor.index.__f__("log", "at carpool-package/pages/carpool/my/contact-history.vue:226", "导航到评价页面成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at carpool-package/pages/carpool/my/contact-history.vue:229", "导航到评价页面失败", err);
          const alternativeUrl = `../create-rating?driverId=${item.driverId}&phoneNumber=${item.phoneNumber}&carpoolId=${item.carpoolId}&startLocation=${encodeURIComponent(item.startLocation)}&endLocation=${encodeURIComponent(item.endLocation)}&departureTime=${item.departureTime}&departureDate=${item.departureDate}`;
          common_vendor.index.__f__("log", "at carpool-package/pages/carpool/my/contact-history.vue:232", "尝试备选路径:", alternativeUrl);
          common_vendor.index.navigateTo({
            url: alternativeUrl,
            success: () => common_vendor.index.__f__("log", "at carpool-package/pages/carpool/my/contact-history.vue:236", "使用备选路径成功导航"),
            fail: (error) => {
              common_vendor.index.__f__("error", "at carpool-package/pages/carpool/my/contact-history.vue:238", "备选路径也失败", error);
              common_vendor.index.showToast({
                title: "跳转评价页面失败",
                icon: "none"
              });
            }
          });
        }
      });
    };
    const hasRated = (item) => {
      return ratingsHistory.value.some(
        (rating) => rating.driverId === item.driverId && rating.carpoolId === item.carpoolId
      );
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: contactHistory.value.length === 0
      }, contactHistory.value.length === 0 ? {
        e: common_assets._imports_1$39
      } : {}, {
        f: common_vendor.f(contactHistory.value, (item, index, i0) => {
          return {
            a: common_vendor.t(getDriverName(item)),
            b: common_vendor.t(formatPhone(item.phoneNumber)),
            c: common_vendor.t(formatDate(item.contactTime)),
            d: common_vendor.t(item.startLocation),
            e: common_vendor.t(item.endLocation),
            f: common_vendor.t(item.departureDate),
            g: common_vendor.t(item.departureTime),
            h: common_vendor.o(($event) => callDriver(item), index),
            i: common_vendor.t(hasRated(item) ? "修改评价" : "评价司机"),
            j: common_vendor.o(($event) => rateDriver(item), index),
            k: index
          };
        }),
        g: common_assets._imports_2$35,
        h: common_assets._imports_3$20,
        i: statusBarHeight.value + 44 + 20 + "px"
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/carpool-package/pages/carpool/my/contact-history.js.map
