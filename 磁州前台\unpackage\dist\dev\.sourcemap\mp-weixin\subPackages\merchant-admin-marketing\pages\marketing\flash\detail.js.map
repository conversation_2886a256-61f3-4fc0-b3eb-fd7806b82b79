{"version": 3, "file": "detail.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/flash/detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xmbGFzaFxkZXRhaWwudnVl"], "sourcesContent": ["<!-- 秒杀活动详情页面 (detail.vue) -->\n<template>\n  <view class=\"flash-detail-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-left\">\n        <view class=\"back-button\" @tap=\"goBack\">\n          <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M15 18L9 12L15 6\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n        </view>\n      </view>\n      <view class=\"navbar-title\">\n        <text class=\"title-text\">秒杀活动详情</text>\n      </view>\n      <view class=\"navbar-right\">\n        <view class=\"more-button\" @tap=\"showMoreOptions\">\n          <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <circle cx=\"12\" cy=\"6\" r=\"2\" fill=\"white\"/>\n            <circle cx=\"12\" cy=\"12\" r=\"2\" fill=\"white\"/>\n            <circle cx=\"12\" cy=\"18\" r=\"2\" fill=\"white\"/>\n          </svg>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 页面内容区域 -->\n    <scroll-view scroll-y class=\"content-area\">\n      <!-- 商品信息区域 -->\n      <view class=\"product-section\">\n        <swiper class=\"product-swiper\" indicator-dots autoplay circular>\n          <swiper-item v-for=\"(image, index) in flashSaleData.images\" :key=\"index\">\n            <image class=\"product-image\" :src=\"image\" mode=\"aspectFill\"></image>\n          </swiper-item>\n        </swiper>\n        \n        <view class=\"product-info\">\n          <view class=\"status-tag\" :class=\"flashSaleData.statusClass\">{{flashSaleData.statusText}}</view>\n          <view class=\"product-name\">{{flashSaleData.name}}</view>\n          <view class=\"product-price\">\n            <text class=\"price-now\">¥{{flashSaleData.flashPrice}}</text>\n            <text class=\"price-original\">¥{{flashSaleData.originalPrice}}</text>\n            <text class=\"discount-tag\">{{flashSaleData.discountText}}</text>\n          </view>\n          \n          <view class=\"countdown-section\" v-if=\"flashSaleData.status !== 'ended'\">\n            <view class=\"countdown-label\">{{countdownLabel}}</view>\n            <view class=\"countdown-timer\">\n              <text class=\"time-block\">{{countdown.days}}</text>\n              <text class=\"time-separator\">天</text>\n              <text class=\"time-block\">{{countdown.hours}}</text>\n              <text class=\"time-separator\">:</text>\n              <text class=\"time-block\">{{countdown.minutes}}</text>\n              <text class=\"time-separator\">:</text>\n              <text class=\"time-block\">{{countdown.seconds}}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 销售数据区域 -->\n      <view class=\"sales-section\">\n        <view class=\"sales-header\">\n          <text class=\"section-title\">销售数据</text>\n          <view class=\"refresh-button\" @tap=\"refreshData\">\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M23 4V10H17\" stroke=\"#FF7600\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M1 20V14H7\" stroke=\"#FF7600\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M3.51 9.00001C4.01717 7.56645 4.87913 6.2765 6.01547 5.27651C7.1518 4.27653 8.52547 3.60084 10 3.31677C11.4745 3.03271 13.0047 3.15505 14.4111 3.67167C15.8175 4.18829 17.0512 5.07723 17.99 6.24001L23 10M1 14L6.01 17.76C6.94879 18.9228 8.18246 19.8117 9.58886 20.3283C10.9953 20.845 12.5255 20.9673 14 20.6832C15.4745 20.3992 16.8482 19.7235 17.9845 18.7235C19.1209 17.7235 19.9828 16.4336 20.49 15\" stroke=\"#FF7600\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            </svg>\n            <text class=\"refresh-text\">刷新</text>\n          </view>\n        </view>\n        \n        <view class=\"sales-stats\">\n          <view class=\"stat-item\">\n            <view class=\"stat-value\">{{flashSaleData.viewCount}}</view>\n            <view class=\"stat-label\">浏览量</view>\n          </view>\n          <view class=\"stat-item\">\n            <view class=\"stat-value\">{{flashSaleData.stockSold}}</view>\n            <view class=\"stat-label\">已售数量</view>\n          </view>\n          <view class=\"stat-item\">\n            <view class=\"stat-value\">{{flashSaleData.stockRemain}}</view>\n            <view class=\"stat-label\">剩余库存</view>\n          </view>\n          <view class=\"stat-item\">\n            <view class=\"stat-value\">{{flashSaleData.conversionRate}}%</view>\n            <view class=\"stat-label\">转化率</view>\n          </view>\n        </view>\n        \n        <view class=\"progress-bar\">\n          <view class=\"progress-inner\" :style=\"{ width: flashSaleData.progressPercent + '%' }\"></view>\n        </view>\n        <view class=\"progress-text\">\n          <text>已售{{flashSaleData.progressPercent}}%</text>\n          <text>剩余{{flashSaleData.stockRemain}}件</text>\n        </view>\n      </view>\n      \n      <!-- 活动详情区域 -->\n      <view class=\"detail-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">活动详情</text>\n        </view>\n        \n        <view class=\"detail-items\">\n          <view class=\"detail-item\">\n            <view class=\"item-label\">活动时间</view>\n            <view class=\"item-value\">{{flashSaleData.timeRange}}</view>\n          </view>\n          <view class=\"detail-item\">\n            <view class=\"item-label\">活动库存</view>\n            <view class=\"item-value\">{{flashSaleData.stockTotal}}件</view>\n          </view>\n          <view class=\"detail-item\">\n            <view class=\"item-label\">限购数量</view>\n            <view class=\"item-value\">{{flashSaleData.purchaseLimit > 0 ? flashSaleData.purchaseLimit + '件/人' : '不限购'}}</view>\n          </view>\n          <view class=\"detail-item\">\n            <view class=\"item-label\">活动规则</view>\n            <view class=\"item-value\">{{flashSaleData.rules}}</view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 商品详情区域 -->\n      <view class=\"product-detail-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">商品详情</text>\n        </view>\n        \n        <rich-text class=\"product-description\" :nodes=\"flashSaleData.description\"></rich-text>\n        \n        <view class=\"product-images\">\n          <image \n            v-for=\"(image, index) in flashSaleData.detailImages\" \n            :key=\"index\" \n            class=\"detail-image\" \n            :src=\"image\" \n            mode=\"widthFix\"\n          ></image>\n        </view>\n      </view>\n      \n      <!-- 订单记录区域 -->\n      <view class=\"orders-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">订单记录</text>\n          <view class=\"view-all\" @tap=\"viewAllOrders\">\n            <text class=\"view-all-text\">查看全部</text>\n            <view class=\"arrow-icon\"></view>\n          </view>\n        </view>\n        \n        <view class=\"orders-list\" v-if=\"flashSaleData.orders.length > 0\">\n          <view class=\"order-item\" v-for=\"(order, index) in flashSaleData.orders\" :key=\"index\">\n            <view class=\"order-user\">\n              <image class=\"user-avatar\" :src=\"order.userAvatar\" mode=\"aspectFill\"></image>\n              <text class=\"user-name\">{{order.userName}}</text>\n            </view>\n            <view class=\"order-info\">\n              <text class=\"order-quantity\">{{order.quantity}}件</text>\n              <text class=\"order-time\">{{order.time}}</text>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"empty-orders\" v-else>\n          <image class=\"empty-image\" src=\"/static/images/empty-orders.png\" mode=\"aspectFit\"></image>\n          <text class=\"empty-text\">暂无订单记录</text>\n        </view>\n      </view>\n      \n      <!-- 底部空间 -->\n      <view class=\"bottom-space\"></view>\n    </scroll-view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"bottom-bar\">\n      <view class=\"action-buttons\">\n        <view class=\"action-button edit\" @tap=\"editFlashSale\">\n          <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13\" stroke=\"#5E5CE6\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <path d=\"M18.5 2.50001C18.8978 2.10219 19.4374 1.87869 20 1.87869C20.5626 1.87869 21.1022 2.10219 21.5 2.50001C21.8978 2.89784 22.1213 3.4374 22.1213 4.00001C22.1213 4.56262 21.8978 5.10219 21.5 5.50001L12 15L8 16L9 12L18.5 2.50001Z\" stroke=\"#5E5CE6\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n          <text class=\"button-text\">编辑</text>\n        </view>\n        <view class=\"action-button share\" @tap=\"shareFlashSale\">\n          <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M18 8C19.6569 8 21 6.65685 21 5C21 3.34315 19.6569 2 18 2C16.3431 2 15 3.34315 15 5C15 6.65685 16.3431 8 18 8Z\" stroke=\"#34C759\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <path d=\"M6 15C7.65685 15 9 13.6569 9 12C9 10.3431 7.65685 9 6 9C4.34315 9 3 10.3431 3 12C3 13.6569 4.34315 15 6 15Z\" stroke=\"#34C759\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <path d=\"M18 22C19.6569 22 21 20.6569 21 19C21 17.3431 19.6569 16 18 16C16.3431 16 15 17.3431 15 19C15 20.6569 16.3431 22 18 22Z\" stroke=\"#34C759\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <path d=\"M8.59 13.51L15.42 17.49\" stroke=\"#34C759\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <path d=\"M15.41 6.51L8.59 10.49\" stroke=\"#34C759\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n          <text class=\"button-text\">分享</text>\n        </view>\n        <view class=\"action-button\" :class=\"flashSaleData.status === 'active' ? 'pause' : 'activate'\" @tap=\"toggleStatus\">\n          <svg v-if=\"flashSaleData.status === 'active'\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <rect x=\"6\" y=\"4\" width=\"4\" height=\"16\" rx=\"1\" stroke=\"#FF9500\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <rect x=\"14\" y=\"4\" width=\"4\" height=\"16\" rx=\"1\" stroke=\"#FF9500\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n          <svg v-else width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M5 3L19 12L5 21V3Z\" stroke=\"#34C759\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n          <text class=\"button-text\">{{flashSaleData.status === 'active' ? '暂停' : '启用'}}</text>\n        </view>\n        <view class=\"action-button delete\" @tap=\"deleteFlashSale\">\n          <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M3 6H5H21\" stroke=\"#FF3B30\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <path d=\"M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z\" stroke=\"#FF3B30\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n          <text class=\"button-text\">删除</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 更多选项弹窗 -->\n    <view class=\"more-options-popup\" v-if=\"showOptions\" @tap=\"hideMoreOptions\">\n      <view class=\"popup-mask\"></view>\n      <view class=\"popup-content\" @tap.stop>\n        <view class=\"popup-option\" @tap=\"viewQrCode\">\n          <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <rect x=\"3\" y=\"3\" width=\"7\" height=\"7\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <rect x=\"14\" y=\"3\" width=\"7\" height=\"7\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <rect x=\"3\" y=\"14\" width=\"7\" height=\"7\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <rect x=\"14\" y=\"14\" width=\"7\" height=\"7\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n          <text>查看二维码</text>\n        </view>\n        <view class=\"popup-option\" @tap=\"viewDataAnalysis\">\n          <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M18 20V10\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <path d=\"M12 20V4\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <path d=\"M6 20V14\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n          <text>数据分析</text>\n        </view>\n        <view class=\"popup-option\" @tap=\"copyLink\">\n          <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M10 13C10.4295 13.5741 10.9774 14.0492 11.6066 14.3929C12.2357 14.7367 12.9315 14.9411 13.6467 14.9923C14.3618 15.0435 15.0796 14.9404 15.7513 14.6898C16.4231 14.4392 17.0331 14.0471 17.54 13.54L20.54 10.54C21.4508 9.59699 21.9548 8.33397 21.9434 7.02299C21.932 5.71201 21.4061 4.45794 20.4791 3.5309C19.5521 2.60386 18.298 2.07802 16.987 2.06663C15.676 2.05523 14.413 2.55921 13.47 3.47L11.75 5.18\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <path d=\"M14 11C13.5705 10.4259 13.0226 9.95083 12.3934 9.60706C11.7642 9.2633 11.0685 9.05889 10.3533 9.00768C9.63816 8.95646 8.92037 9.05964 8.24861 9.31023C7.57685 9.56082 6.96684 9.95294 6.45996 10.46L3.45996 13.46C2.54917 14.403 2.04519 15.666 2.05659 16.977C2.06798 18.288 2.59382 19.5421 3.52086 20.4691C4.4479 21.3961 5.70197 21.922 7.01295 21.9334C8.32393 21.9448 9.58694 21.4408 10.53 20.53L12.24 18.82\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n          <text>复制链接</text>\n        </view>\n        <view class=\"popup-option\" @tap=\"exportData\">\n          <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <path d=\"M7 10L12 15L17 10\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <path d=\"M12 15V3\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n          <text>导出数据</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, computed, onMounted } from 'vue';\n\n// 页面参数\nconst flashId = ref(null);\n\n// 弹窗控制\nconst showOptions = ref(false);\n\n// 秒杀活动数据\nconst flashSaleData = reactive({\n  id: 1,\n  name: '夏季清凉风扇特惠',\n  flashPrice: 59.9,\n  originalPrice: 129.9,\n  discountText: '4.6折',\n  stockTotal: 100,\n  stockSold: 86,\n  stockRemain: 14,\n  viewCount: 1564,\n  conversionRate: 5.5,\n  progressPercent: 86,\n  images: [\n    '/static/images/flash-item1.jpg',\n    '/static/images/flash-item1-2.jpg',\n    '/static/images/flash-item1-3.jpg'\n  ],\n  detailImages: [\n    '/static/images/flash-detail1.jpg',\n    '/static/images/flash-detail2.jpg',\n    '/static/images/flash-detail3.jpg'\n  ],\n  timeRange: '2023-07-01 12:00 ~ 14:00',\n  status: 'active',\n  statusText: '进行中',\n  statusClass: 'status-active',\n  purchaseLimit: 2,\n  rules: '活动期间每人限购2件，秒杀商品不支持退换货，数量有限，先到先得',\n  description: '<p>这是一款高品质的夏季清凉风扇，采用先进技术，风力强劲，静音设计，让您的夏天更加舒适。</p><p>规格参数：</p><ul><li>功率：30W</li><li>风速：3档可调</li><li>电池容量：4000mAh</li><li>续航时间：4-8小时</li><li>材质：环保ABS</li></ul>',\n  orders: [\n    {\n      userName: '张先生',\n      userAvatar: '/static/images/avatar1.jpg',\n      quantity: 1,\n      time: '2023-07-01 12:05'\n    },\n    {\n      userName: '李女士',\n      userAvatar: '/static/images/avatar2.jpg',\n      quantity: 2,\n      time: '2023-07-01 12:08'\n    },\n    {\n      userName: '王先生',\n      userAvatar: '/static/images/avatar3.jpg',\n      quantity: 1,\n      time: '2023-07-01 12:15'\n    }\n  ]\n});\n\n// 倒计时数据\nconst countdown = reactive({\n  days: '00',\n  hours: '01',\n  minutes: '25',\n  seconds: '36'\n});\n\n// 倒计时标签\nconst countdownLabel = computed(() => {\n  if (flashSaleData.status === 'upcoming') {\n    return '距开始还剩';\n  } else if (flashSaleData.status === 'active') {\n    return '距结束还剩';\n  }\n  return '';\n});\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n\n// 显示更多选项\nconst showMoreOptions = () => {\n  showOptions.value = true;\n};\n\n// 隐藏更多选项\nconst hideMoreOptions = () => {\n  showOptions.value = false;\n};\n\n// 刷新数据\nconst refreshData = () => {\n  uni.showLoading({\n    title: '刷新中...'\n  });\n  \n  setTimeout(() => {\n    uni.hideLoading();\n    uni.showToast({\n      title: '数据已更新',\n      icon: 'success'\n    });\n  }, 1000);\n};\n\n// 编辑秒杀活动\nconst editFlashSale = () => {\n  uni.navigateTo({\n    url: `/subPackages/merchant-admin-marketing/pages/marketing/flash/edit?id=${flashSaleData.id}`\n  });\n};\n\n// 分享秒杀活动\nconst shareFlashSale = () => {\n  uni.showToast({\n    title: '生成分享链接中...',\n    icon: 'loading',\n    duration: 1500\n  });\n  \n  setTimeout(() => {\n    uni.showModal({\n      title: '分享秒杀活动',\n      content: `活动\"${flashSaleData.name}\"的分享链接已创建，可发送给客户或分享到社交媒体`,\n      confirmText: '复制链接',\n      cancelText: '取消',\n      success: (res) => {\n        if (res.confirm) {\n          uni.setClipboardData({\n            data: `https://example.com/flash-sale/${flashSaleData.id}`,\n            success: () => {\n              uni.showToast({\n                title: '链接已复制',\n                icon: 'success'\n              });\n            }\n          });\n        }\n      }\n    });\n  }, 1500);\n};\n\n// 切换活动状态\nconst toggleStatus = () => {\n  const action = flashSaleData.status === 'active' ? '暂停' : '启用';\n  \n  uni.showModal({\n    title: `确认${action}`,\n    content: `确定要${action}该秒杀活动吗？`,\n    confirmText: '确定',\n    cancelText: '取消',\n    success: (res) => {\n      if (res.confirm) {\n        if (flashSaleData.status === 'active') {\n          flashSaleData.status = 'upcoming';\n          flashSaleData.statusText = '未开始';\n          flashSaleData.statusClass = 'status-upcoming';\n        } else {\n          flashSaleData.status = 'active';\n          flashSaleData.statusText = '进行中';\n          flashSaleData.statusClass = 'status-active';\n        }\n        \n        uni.showToast({\n          title: `${action}成功`,\n          icon: 'success'\n        });\n      }\n    }\n  });\n};\n\n// 删除秒杀活动\nconst deleteFlashSale = () => {\n  uni.showModal({\n    title: '确认删除',\n    content: `确定要删除\"${flashSaleData.name}\"秒杀活动吗？一旦删除将无法恢复。`,\n    confirmText: '删除',\n    confirmColor: '#FF3B30',\n    cancelText: '取消',\n    success: (res) => {\n      if (res.confirm) {\n        uni.showToast({\n          title: '删除成功',\n          icon: 'success',\n          duration: 1500,\n          success: () => {\n            setTimeout(() => {\n              uni.navigateBack();\n            }, 1500);\n          }\n        });\n      }\n    }\n  });\n};\n\n// 查看全部订单\nconst viewAllOrders = () => {\n  uni.navigateTo({\n    url: `/subPackages/merchant-admin-marketing/pages/marketing/flash/orders?id=${flashSaleData.id}`\n  });\n};\n\n// 查看二维码\nconst viewQrCode = () => {\n  hideMoreOptions();\n  uni.showLoading({\n    title: '生成二维码...'\n  });\n  \n  setTimeout(() => {\n    uni.hideLoading();\n    uni.previewImage({\n      urls: ['/static/images/flash-qrcode.png']\n    });\n  }, 1000);\n};\n\n// 查看数据分析\nconst viewDataAnalysis = () => {\n  hideMoreOptions();\n  uni.navigateTo({\n    url: `/subPackages/merchant-admin-marketing/pages/marketing/flash/analysis?id=${flashSaleData.id}`\n  });\n};\n\n// 复制链接\nconst copyLink = () => {\n  hideMoreOptions();\n  uni.setClipboardData({\n    data: `https://example.com/flash-sale/${flashSaleData.id}`,\n    success: () => {\n      uni.showToast({\n        title: '链接已复制',\n        icon: 'success'\n      });\n    }\n  });\n};\n\n// 导出数据\nconst exportData = () => {\n  hideMoreOptions();\n  uni.showLoading({\n    title: '导出中...'\n  });\n  \n  setTimeout(() => {\n    uni.hideLoading();\n    uni.showToast({\n      title: '数据已导出',\n      icon: 'success'\n    });\n  }, 1500);\n};\n\n// 页面加载\nonMounted(() => {\n  // 获取页面参数\n  const eventChannel = getOpenerEventChannel();\n  const query = uni.getSystemInfoSync().platform === 'h5' ? location.href.split('?')[1] : '';\n  \n  if (query) {\n    const params = query.split('&').reduce((res, item) => {\n      const [key, value] = item.split('=');\n      res[key] = value;\n      return res;\n    }, {});\n    \n    flashId.value = params.id;\n  } else {\n    const pages = getCurrentPages();\n    const currentPage = pages[pages.length - 1];\n    \n    if (currentPage && currentPage.options) {\n      flashId.value = currentPage.options.id;\n    }\n  }\n  \n  // 加载秒杀活动数据\n  loadFlashSaleData();\n  \n  // 启动倒计时\n  startCountdown();\n});\n\n// 加载秒杀活动数据\nconst loadFlashSaleData = () => {\n  // 实际应用中应该从API获取数据\n  console.log('加载秒杀活动数据, ID:', flashId.value);\n  \n  // 模拟API请求\n  uni.showLoading({\n    title: '加载中...'\n  });\n  \n  setTimeout(() => {\n    uni.hideLoading();\n    // 实际应用中这里应该使用API返回的数据更新flashSaleData\n  }, 500);\n};\n\n// 启动倒计时\nconst startCountdown = () => {\n  // 设置目标时间（示例：当前时间加上1小时30分钟）\n  const targetTime = new Date();\n  if (flashSaleData.status === 'upcoming') {\n    // 未开始，倒计时到开始时间\n    targetTime.setHours(targetTime.getHours() + 2);\n  } else if (flashSaleData.status === 'active') {\n    // 进行中，倒计时到结束时间\n    targetTime.setHours(targetTime.getHours() + 1);\n    targetTime.setMinutes(targetTime.getMinutes() + 30);\n  }\n  \n  // 更新倒计时\n  const updateCountdown = () => {\n    const now = new Date();\n    const diff = targetTime - now;\n    \n    if (diff <= 0) {\n      // 倒计时结束\n      countdown.days = '00';\n      countdown.hours = '00';\n      countdown.minutes = '00';\n      countdown.seconds = '00';\n      \n      // 更新活动状态\n      if (flashSaleData.status === 'upcoming') {\n        flashSaleData.status = 'active';\n        flashSaleData.statusText = '进行中';\n        flashSaleData.statusClass = 'status-active';\n        // 重新启动倒计时到结束时间\n        startCountdown();\n      } else if (flashSaleData.status === 'active') {\n        flashSaleData.status = 'ended';\n        flashSaleData.statusText = '已结束';\n        flashSaleData.statusClass = 'status-ended';\n      }\n      \n      return;\n    }\n    \n    // 计算天、时、分、秒\n    const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));\n    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n    const seconds = Math.floor((diff % (1000 * 60)) / 1000);\n    \n    // 更新倒计时数据\n    countdown.days = days.toString().padStart(2, '0');\n    countdown.hours = hours.toString().padStart(2, '0');\n    countdown.minutes = minutes.toString().padStart(2, '0');\n    countdown.seconds = seconds.toString().padStart(2, '0');\n    \n    // 继续倒计时\n    setTimeout(updateCountdown, 1000);\n  };\n  \n  // 开始倒计时\n  updateCountdown();\n};\n</script>\n\n<style lang=\"scss\">\n/* 页面容器 */\n.flash-detail-container {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  position: relative;\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #FF7600, #FF3C00);\n  color: white;\n  padding: 48px 20px 16px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 8px rgba(255, 120, 0, 0.15);\n}\n\n.navbar-left {\n  width: 40px;\n}\n\n.back-button {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n}\n\n.title-text {\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.navbar-right {\n  width: 40px;\n  display: flex;\n  justify-content: flex-end;\n}\n\n.more-button {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* 内容区域 */\n.content-area {\n  flex: 1;\n  box-sizing: border-box;\n  height: calc(100vh - 80px - 60px);\n}\n\n/* 商品信息区域 */\n.product-section {\n  background: #FFFFFF;\n  margin-bottom: 12px;\n}\n\n.product-swiper {\n  width: 100%;\n  height: 300px;\n}\n\n.product-image {\n  width: 100%;\n  height: 100%;\n}\n\n.product-info {\n  padding: 16px;\n  position: relative;\n}\n\n.status-tag {\n  position: absolute;\n  top: -20px;\n  right: 16px;\n  padding: 4px 12px;\n  border-radius: 16px;\n  font-size: 12px;\n  color: white;\n  z-index: 10;\n}\n\n.status-active {\n  background: #34C759;\n}\n\n.status-upcoming {\n  background: #007AFF;\n}\n\n.status-ended {\n  background: #8E8E93;\n}\n\n.product-name {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333333;\n  margin-bottom: 12px;\n  line-height: 1.4;\n}\n\n.product-price {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.price-now {\n  font-size: 24px;\n  font-weight: 700;\n  color: #FF3B30;\n  margin-right: 8px;\n}\n\n.price-original {\n  font-size: 16px;\n  color: #999999;\n  text-decoration: line-through;\n  margin-right: 8px;\n}\n\n.discount-tag {\n  background: rgba(255, 59, 48, 0.1);\n  color: #FF3B30;\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-size: 12px;\n}\n\n.countdown-section {\n  background: #FFF8F0;\n  border-radius: 8px;\n  padding: 12px;\n  margin-top: 8px;\n}\n\n.countdown-label {\n  font-size: 14px;\n  color: #FF7600;\n  margin-bottom: 8px;\n}\n\n.countdown-timer {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.time-block {\n  background: #FF7600;\n  color: white;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 16px;\n  font-weight: 600;\n  min-width: 32px;\n  text-align: center;\n}\n\n.time-separator {\n  margin: 0 4px;\n  color: #FF7600;\n  font-weight: 600;\n}\n\n/* 销售数据区域 */\n.sales-section {\n  background: #FFFFFF;\n  padding: 16px;\n  margin-bottom: 12px;\n}\n\n.sales-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.section-title {\n  font-size: 17px;\n  font-weight: 600;\n  color: #333333;\n}\n\n.refresh-button {\n  display: flex;\n  align-items: center;\n  color: #FF7600;\n}\n\n.refresh-text {\n  font-size: 14px;\n  margin-left: 4px;\n}\n\n.sales-stats {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 16px;\n}\n\n.stat-item {\n  text-align: center;\n  flex: 1;\n}\n\n.stat-value {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333333;\n  margin-bottom: 4px;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #999999;\n}\n\n.progress-bar {\n  height: 8px;\n  background: #F0F0F0;\n  border-radius: 4px;\n  overflow: hidden;\n  margin-bottom: 8px;\n}\n\n.progress-inner {\n  height: 100%;\n  background: linear-gradient(90deg, #FF7600, #FF3C00);\n  border-radius: 4px;\n}\n\n.progress-text {\n  display: flex;\n  justify-content: space-between;\n  font-size: 12px;\n  color: #666666;\n}\n\n/* 活动详情区域 */\n.detail-section {\n  background: #FFFFFF;\n  padding: 16px;\n  margin-bottom: 12px;\n}\n\n.section-header {\n  margin-bottom: 16px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.detail-items {\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.detail-item {\n  display: flex;\n  padding: 12px 0;\n  border-bottom: 1px solid #F5F5F5;\n}\n\n.detail-item:last-child {\n  border-bottom: none;\n}\n\n.item-label {\n  width: 80px;\n  font-size: 14px;\n  color: #666666;\n}\n\n.item-value {\n  flex: 1;\n  font-size: 14px;\n  color: #333333;\n  line-height: 1.5;\n}\n\n/* 商品详情区域 */\n.product-detail-section {\n  background: #FFFFFF;\n  padding: 16px;\n  margin-bottom: 12px;\n}\n\n.product-description {\n  font-size: 14px;\n  color: #333333;\n  line-height: 1.6;\n  margin-bottom: 16px;\n}\n\n.product-images {\n  width: 100%;\n}\n\n.detail-image {\n  width: 100%;\n  margin-bottom: 12px;\n}\n\n/* 订单记录区域 */\n.orders-section {\n  background: #FFFFFF;\n  padding: 16px;\n  margin-bottom: 12px;\n}\n\n.view-all {\n  display: flex;\n  align-items: center;\n  color: #666666;\n}\n\n.view-all-text {\n  font-size: 14px;\n}\n\n.arrow-icon {\n  width: 0;\n  height: 0;\n  border-left: 5px solid transparent;\n  border-right: 5px solid transparent;\n  border-top: 5px solid #666666;\n  transform: rotate(-90deg);\n  margin-left: 4px;\n}\n\n.orders-list {\n  margin-top: 8px;\n}\n\n.order-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid #F5F5F5;\n}\n\n.order-item:last-child {\n  border-bottom: none;\n}\n\n.order-user {\n  display: flex;\n  align-items: center;\n}\n\n.user-avatar {\n  width: 36px;\n  height: 36px;\n  border-radius: 18px;\n  margin-right: 8px;\n}\n\n.user-name {\n  font-size: 14px;\n  color: #333333;\n}\n\n.order-info {\n  text-align: right;\n}\n\n.order-quantity {\n  font-size: 14px;\n  color: #333333;\n  margin-bottom: 4px;\n}\n\n.order-time {\n  font-size: 12px;\n  color: #999999;\n}\n\n.empty-orders {\n  padding: 24px 0;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.empty-image {\n  width: 80px;\n  height: 80px;\n  margin-bottom: 12px;\n}\n\n.empty-text {\n  font-size: 14px;\n  color: #999999;\n}\n\n/* 底部空间 */\n.bottom-space {\n  height: 20px;\n}\n\n/* 底部操作栏 */\n.bottom-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: #FFFFFF;\n  padding: 10px 16px;\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);\n  z-index: 90;\n}\n\n.action-buttons {\n  display: flex;\n  justify-content: space-around;\n}\n\n.action-button {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 5px 0;\n}\n\n.button-text {\n  font-size: 12px;\n  margin-top: 4px;\n}\n\n.action-button.edit {\n  color: #5E5CE6;\n}\n\n.action-button.share {\n  color: #34C759;\n}\n\n.action-button.pause {\n  color: #FF9500;\n}\n\n.action-button.activate {\n  color: #34C759;\n}\n\n.action-button.delete {\n  color: #FF3B30;\n}\n\n/* 更多选项弹窗 */\n.more-options-popup {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 999;\n}\n\n.popup-mask {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.6);\n}\n\n.popup-content {\n  position: absolute;\n  top: 80px;\n  right: 16px;\n  background: #FFFFFF;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n  width: 160px;\n}\n\n.popup-option {\n  display: flex;\n  align-items: center;\n  padding: 12px 16px;\n  font-size: 14px;\n  color: #333333;\n}\n\n.popup-option:active {\n  background: #F5F5F5;\n}\n\n.popup-option svg {\n  margin-right: 8px;\n}\n\n@media screen and (min-width: 768px) {\n  .product-swiper {\n    height: 400px;\n  }\n  \n  .action-buttons {\n    max-width: 600px;\n    margin: 0 auto;\n  }\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/flash/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "computed", "uni", "onMounted", "MiniProgramPage"], "mappings": ";;;;;;;;;;;;;AAyQA,UAAM,UAAUA,cAAAA,IAAI,IAAI;AAGxB,UAAM,cAAcA,cAAAA,IAAI,KAAK;AAG7B,UAAM,gBAAgBC,cAAAA,SAAS;AAAA,MAC7B,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,aAAa;AAAA,MACb,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,QAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,MACD,cAAc;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,MACD,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,eAAe;AAAA,MACf,OAAO;AAAA,MACP,aAAa;AAAA,MACb,QAAQ;AAAA,QACN;AAAA,UACE,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,MAAM;AAAA,QACP;AAAA,MACF;AAAA,IACH,CAAC;AAGD,UAAM,YAAYA,cAAAA,SAAS;AAAA,MACzB,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AAGD,UAAM,iBAAiBC,cAAQ,SAAC,MAAM;AACpC,UAAI,cAAc,WAAW,YAAY;AACvC,eAAO;AAAA,MACX,WAAa,cAAc,WAAW,UAAU;AAC5C,eAAO;AAAA,MACR;AACD,aAAO;AAAA,IACT,CAAC;AAGD,UAAM,SAAS,MAAM;AACnBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,kBAAkB,MAAM;AAC5B,kBAAY,QAAQ;AAAA,IACtB;AAGA,UAAM,kBAAkB,MAAM;AAC5B,kBAAY,QAAQ;AAAA,IACtB;AAGA,UAAM,cAAc,MAAM;AACxBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF,GAAE,GAAI;AAAA,IACT;AAGA,UAAM,gBAAgB,MAAM;AAC1BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,uEAAuE,cAAc,EAAE;AAAA,MAChG,CAAG;AAAA,IACH;AAGA,UAAM,iBAAiB,MAAM;AAC3BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACd,CAAG;AAED,iBAAW,MAAM;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS,MAAM,cAAc,IAAI;AAAA,UACjC,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AACfA,4BAAAA,MAAI,iBAAiB;AAAA,gBACnB,MAAM,kCAAkC,cAAc,EAAE;AAAA,gBACxD,SAAS,MAAM;AACbA,gCAAAA,MAAI,UAAU;AAAA,oBACZ,OAAO;AAAA,oBACP,MAAM;AAAA,kBACtB,CAAe;AAAA,gBACF;AAAA,cACb,CAAW;AAAA,YACF;AAAA,UACF;AAAA,QACP,CAAK;AAAA,MACF,GAAE,IAAI;AAAA,IACT;AAGA,UAAM,eAAe,MAAM;AACzB,YAAM,SAAS,cAAc,WAAW,WAAW,OAAO;AAE1DA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,KAAK,MAAM;AAAA,QAClB,SAAS,MAAM,MAAM;AAAA,QACrB,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,gBAAI,cAAc,WAAW,UAAU;AACrC,4BAAc,SAAS;AACvB,4BAAc,aAAa;AAC3B,4BAAc,cAAc;AAAA,YACtC,OAAe;AACL,4BAAc,SAAS;AACvB,4BAAc,aAAa;AAC3B,4BAAc,cAAc;AAAA,YAC7B;AAEDA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO,GAAG,MAAM;AAAA,cAChB,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,kBAAkB,MAAM;AAC5BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,SAAS,cAAc,IAAI;AAAA,QACpC,aAAa;AAAA,QACb,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,cACV,SAAS,MAAM;AACb,2BAAW,MAAM;AACfA,gCAAG,MAAC,aAAY;AAAA,gBACjB,GAAE,IAAI;AAAA,cACR;AAAA,YACX,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,MAAM;AAC1BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,yEAAyE,cAAc,EAAE;AAAA,MAClG,CAAG;AAAA,IACH;AAGA,UAAM,aAAa,MAAM;AACvB;AACAA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,aAAa;AAAA,UACf,MAAM,CAAC,iCAAiC;AAAA,QAC9C,CAAK;AAAA,MACF,GAAE,GAAI;AAAA,IACT;AAGA,UAAM,mBAAmB,MAAM;AAC7B;AACAA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,2EAA2E,cAAc,EAAE;AAAA,MACpG,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrB;AACAA,oBAAAA,MAAI,iBAAiB;AAAA,QACnB,MAAM,kCAAkC,cAAc,EAAE;AAAA,QACxD,SAAS,MAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,aAAa,MAAM;AACvB;AACAA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF,GAAE,IAAI;AAAA,IACT;AAGAC,kBAAAA,UAAU,MAAM;AAEO,4BAAwB;AAC7C,YAAM,QAAQD,cAAG,MAAC,kBAAmB,EAAC,aAAa,OAAO,SAAS,KAAK,MAAM,GAAG,EAAE,CAAC,IAAI;AAExF,UAAI,OAAO;AACT,cAAM,SAAS,MAAM,MAAM,GAAG,EAAE,OAAO,CAAC,KAAK,SAAS;AACpD,gBAAM,CAAC,KAAK,KAAK,IAAI,KAAK,MAAM,GAAG;AACnC,cAAI,GAAG,IAAI;AACX,iBAAO;AAAA,QACR,GAAE,CAAE,CAAA;AAEL,gBAAQ,QAAQ,OAAO;AAAA,MAC3B,OAAS;AACL,cAAM,QAAQ;AACd,cAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAE1C,YAAI,eAAe,YAAY,SAAS;AACtC,kBAAQ,QAAQ,YAAY,QAAQ;AAAA,QACrC;AAAA,MACF;AAGD;AAGA;IACF,CAAC;AAGD,UAAM,oBAAoB,MAAM;AAE9BA,oBAAA,MAAA,MAAA,OAAA,gFAAY,iBAAiB,QAAQ,KAAK;AAG1CA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAAA,MAEhB,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,iBAAiB,MAAM;AAE3B,YAAM,aAAa,oBAAI;AACvB,UAAI,cAAc,WAAW,YAAY;AAEvC,mBAAW,SAAS,WAAW,SAAU,IAAG,CAAC;AAAA,MACjD,WAAa,cAAc,WAAW,UAAU;AAE5C,mBAAW,SAAS,WAAW,SAAU,IAAG,CAAC;AAC7C,mBAAW,WAAW,WAAW,WAAY,IAAG,EAAE;AAAA,MACnD;AAGD,YAAM,kBAAkB,MAAM;AAC5B,cAAM,MAAM,oBAAI;AAChB,cAAM,OAAO,aAAa;AAE1B,YAAI,QAAQ,GAAG;AAEb,oBAAU,OAAO;AACjB,oBAAU,QAAQ;AAClB,oBAAU,UAAU;AACpB,oBAAU,UAAU;AAGpB,cAAI,cAAc,WAAW,YAAY;AACvC,0BAAc,SAAS;AACvB,0BAAc,aAAa;AAC3B,0BAAc,cAAc;AAE5B;UACR,WAAiB,cAAc,WAAW,UAAU;AAC5C,0BAAc,SAAS;AACvB,0BAAc,aAAa;AAC3B,0BAAc,cAAc;AAAA,UAC7B;AAED;AAAA,QACD;AAGD,cAAM,OAAO,KAAK,MAAM,QAAQ,MAAO,KAAK,KAAK,GAAG;AACpD,cAAM,QAAQ,KAAK,MAAO,QAAQ,MAAO,KAAK,KAAK,OAAQ,MAAO,KAAK,GAAG;AAC1E,cAAM,UAAU,KAAK,MAAO,QAAQ,MAAO,KAAK,OAAQ,MAAO,GAAG;AAClE,cAAM,UAAU,KAAK,MAAO,QAAQ,MAAO,MAAO,GAAI;AAGtD,kBAAU,OAAO,KAAK,SAAU,EAAC,SAAS,GAAG,GAAG;AAChD,kBAAU,QAAQ,MAAM,SAAU,EAAC,SAAS,GAAG,GAAG;AAClD,kBAAU,UAAU,QAAQ,SAAU,EAAC,SAAS,GAAG,GAAG;AACtD,kBAAU,UAAU,QAAQ,SAAU,EAAC,SAAS,GAAG,GAAG;AAGtD,mBAAW,iBAAiB,GAAI;AAAA,MACpC;AAGE;IACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnnBA,GAAG,WAAWE,SAAe;"}