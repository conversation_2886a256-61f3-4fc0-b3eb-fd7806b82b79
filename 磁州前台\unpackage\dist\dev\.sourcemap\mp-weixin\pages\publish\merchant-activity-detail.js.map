{"version": 3, "file": "merchant-activity-detail.js", "sources": ["pages/publish/merchant-activity-detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHVibGlzaC9tZXJjaGFudC1hY3Rpdml0eS1kZXRhaWwudnVl"], "sourcesContent": ["<template>\n  <view class=\"detail-container activity-detail-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"navbar-left\" @click=\"goBack\">\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\n      </view>\n      <view class=\"navbar-title\">商家活动详情</view>\n      <view class=\"navbar-right\">\n        <!-- 占位 -->\n      </view>\n    </view>\n    \n    <!-- 隐藏的分享按钮，用于自动触发 -->\n    <button id=\"shareButton\" class=\"hidden-share-btn\" open-type=\"share\"></button>\n    \n    <!-- 隐藏的Canvas用于绘制海报 -->\n    <canvas canvas-id=\"posterCanvas\" class=\"poster-canvas\" style=\"width: 600px; height: 900px; position: fixed; top: -9999px; left: -9999px;\"></canvas>\n    \n    <!-- 悬浮海报按钮 -->\n    <view class=\"float-poster-btn\" @click=\"generateShareImage\">\n      <image src=\"/static/images/tabbar/海报.png\" class=\"poster-icon\"></image>\n      <text class=\"poster-text\">海报</text>\n    </view>\n    \n    <view class=\"detail-wrapper activity-detail-wrapper\">\n      <!-- 活动封面 -->\n      <view class=\"content-card cover-card\">\n        <swiper class=\"cover-swiper\" indicator-dots autoplay circular>\n          <swiper-item v-for=\"(image, index) in activityData.images\" :key=\"index\">\n            <image :src=\"image\" mode=\"aspectFill\" class=\"cover-image\" @click=\"previewImage(index)\"></image>\n          </swiper-item>\n        </swiper>\n      </view>\n      \n      <!-- 活动信息卡片 -->\n      <view class=\"content-card activity-card\">\n        <view class=\"activity-header\">\n          <view class=\"title-row\">\n            <text class=\"main-title\">{{activityData.title}}</text>\n            <view class=\"activity-status\" :class=\"getActivityStatusClass()\">{{getActivityStatusText()}}</view>\n          </view>\n          <view class=\"meta-info\">\n            <view class=\"tag-group\">\n              <view class=\"info-tag\" v-for=\"(tag, index) in activityData.tags\" :key=\"index\">{{tag}}</view>\n            </view>\n            <text class=\"publish-time\">发布于 {{formatTime(activityData.publishTime)}}</text>\n          </view>\n        </view>\n        \n        <view class=\"basic-info\">\n          <view class=\"info-item\">\n            <text class=\"info-label\">活动类型</text>\n            <text class=\"info-value\">{{activityData.activityType}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">活动时间</text>\n            <text class=\"info-value\">{{activityData.startDate}} 至 {{activityData.endDate}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">商家名称</text>\n            <text class=\"info-value\">{{activityData.merchantName}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 活动描述 -->\n      <view class=\"content-card description-card\">\n        <view class=\"section-title\">活动描述</view>\n        <view class=\"description-content\">\n          <rich-text :nodes=\"activityData.description\" class=\"description-text\"></rich-text>\n        </view>\n      </view>\n      \n      <!-- 活动规则 -->\n      <view class=\"content-card rules-card\">\n        <view class=\"section-title\">活动规则</view>\n        <view class=\"rules-content\">\n          <rich-text :nodes=\"activityData.rules\" class=\"rules-text\"></rich-text>\n        </view>\n      </view>\n      \n      <!-- 商家地址卡片 -->\n      <view class=\"content-card location-card\">\n        <view class=\"section-title\">活动地点</view>\n        <view class=\"location-content\" @click=\"openLocation\">\n          <text class=\"iconfont icon-location\"></text>\n          <text class=\"location-text\">{{activityData.address}}</text>\n          <text class=\"iconfont icon-right location-arrow\"></text>\n        </view>\n        <view class=\"location-map\">\n          <image src=\"/static/images/map-preview.png\" mode=\"aspectFill\" class=\"map-preview\"></image>\n        </view>\n      </view>\n      \n      <!-- 联系方式卡片 -->\n      <view class=\"content-card contact-card\">\n        <view class=\"section-title\">商家联系方式</view>\n        <view class=\"contact-content\">\n          <view class=\"contact-item\">\n            <text class=\"contact-label\">联系人</text>\n            <text class=\"contact-value\">{{activityData.contact}}</text>\n          </view>\n          <view class=\"contact-item\">\n            <text class=\"contact-label\">电话</text>\n            <text class=\"contact-value contact-phone\" @click=\"callPhone\">{{activityData.phone}}</text>\n          </view>\n          <view class=\"contact-tips\">\n            <text class=\"tips-icon iconfont icon-info\"></text>\n            <text class=\"tips-text\">请说明在\"磁州生活网\"看到的信息</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 红包区域 -->\n      <view class=\"content-card red-packet-card\" v-if=\"activityData.hasRedPacket\">\n        <view class=\"section-title\">活动红包</view>\n        <view class=\"red-packet-section\">\n          <view class=\"red-packet-container\" @click=\"openRedPacket\">\n            <view class=\"red-packet-blur-bg\"></view>\n            <view class=\"red-packet-content\">\n              <view class=\"red-packet-left\">\n                <image class=\"red-packet-icon\" src=\"/static/images/tabbar/抢红包.gif\"></image>\n              <view class=\"red-packet-info\">\n                <view class=\"red-packet-title\">\n                  {{activityData.redPacket.type === 'random' ? '随机金额红包' : '查看活动领红包'}}\n                </view>\n                <view class=\"red-packet-desc\">\n                  还剩{{activityData.redPacket.remain}}个，{{getRedPacketConditionText()}}\n                  </view>\n                </view>\n              </view>\n              <view class=\"red-packet-right\">\n                <view class=\"red-packet-amount\"><text class=\"prefix\">共</text> ¥{{activityData.redPacket.amount}}</view>\n                <view class=\"grab-btn\">立即领取</view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 举报卡片 -->\n      <report-card></report-card>\n      \n      <!-- 相关活动推荐卡片 -->\n      <view class=\"content-card related-activities-card\">\n        <!-- 标题栏 -->\n        <view class=\"collapsible-header\">\n          <view class=\"section-title\">相关活动推荐</view>\n        </view>\n        \n        <!-- 内容区 -->\n        <view class=\"collapsible-content\">\n          <!-- 简洁的活动列表 -->\n          <view class=\"related-activities-list\">\n            <view class=\"related-activity-item\" \n                 v-for=\"(activity, index) in relatedActivities.slice(0, 3)\" \n                 :key=\"index\" \n                 @click=\"navigateToActivityDetail(activity.id)\">\n              <view class=\"related-activity-left\">\n                <image :src=\"activity.image\" mode=\"aspectFill\" class=\"related-activity-image\"></image>\n              </view>\n              <view class=\"related-activity-info\">\n                <view class=\"related-activity-title\">{{activity.title}}</view>\n                <view class=\"related-activity-meta\">\n                  <text class=\"related-meta-text\">{{activity.merchantName}}</text>\n                  <text class=\"related-meta-text\">{{activity.activityType}}</text>\n                </view>\n                <view class=\"related-activity-date\">{{activity.startDate}} 至 {{activity.endDate}}</view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"footer-action-bar\">\n      <view class=\"action-item collect-action\" @click=\"toggleCollect\">\n        <text class=\"action-icon\" :class=\"{'icon-collected': isCollected, 'icon-collect': !isCollected}\"></text>\n        <text class=\"action-text\">{{isCollected ? '已收藏' : '收藏'}}</text>\n      </view>\n      <view class=\"action-item share-action\" @click=\"shareToFriend\">\n        <text class=\"action-icon icon-share\"></text>\n        <text class=\"action-text\">分享</text>\n      </view>\n      <view class=\"action-item contact-action\" @click=\"contactMerchant\">\n        <text class=\"action-text\">联系商家</text>\n      </view>\n    </view>\n    \n    <!-- 海报弹窗 -->\n    <view class=\"poster-modal\" v-if=\"showPosterModal\" @click=\"closePosterModal\">\n      <view class=\"poster-container\" @click.stop>\n        <view class=\"poster-header\">\n          <text class=\"poster-modal-title\">分享海报</text>\n          <text class=\"close-icon\" @click=\"closePosterModal\">×</text>\n        </view>\n        <view class=\"poster-image-container\">\n          <image :src=\"posterUrl\" mode=\"widthFix\" class=\"poster-preview\"></image>\n        </view>\n        <view class=\"poster-footer\">\n          <button class=\"poster-btn save-btn\" @click=\"savePoster\">保存到相册</button>\n          <button class=\"poster-btn share-btn\" open-type=\"share\">分享给朋友</button>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, onMounted } from 'vue';\nimport { onLoad } from '@dcloudio/uni-app';\nimport { formatTime } from '../../utils/date.js';\nimport ReportCard from '../../components/ReportCard.vue';\n\n// --- 响应式状态 ---\nconst statusBarHeight = ref(20);\nconst activityId = ref('');\nconst isCollected = ref(false);\nconst showPosterModal = ref(false);\nconst posterUrl = ref('');\n\nconst activityData = reactive({\n        id: '1',\n        title: '周年庆典大优惠',\n        activityType: '优惠折扣',\n        merchantName: '品味餐厅',\n        startDate: '2023-06-01',\n        endDate: '2023-06-30',\n        address: '河北省邯郸市磁县北顺城街32号',\n        description: '品味餐厅两周年庆典，全场菜品8折，满200减30，满300减50，满500减100！还有精美礼品等你来拿！',\n        rules: '1. 活动时间：2023年6月1日至6月30日\\n2. 折扣与满减不可同时使用\\n3. 特价菜品除外\\n4. 每桌最多减免100元\\n5. 节假日通用',\n        tags: ['周年庆', '满减优惠', '有赠品'],\n        images: [\n          '/static/images/default-image.png',\n          '/static/images/default-image.png',\n          '/static/images/default-image.png'\n        ],\n        contact: '李经理',\n        phone: '13800138000',\n        wechat: 'restaurant123',\n        publishTime: new Date().getTime() - 86400000, // 一天前\n        hasRedPacket: true,\n        redPacket: {\n          type: 'random',\n          amount: 20,\n          remain: 10,\n          total: 20\n        }\n});\n\nconst relatedActivities = reactive([\n        {\n          id: '2',\n          title: '新店开业大酬宾',\n          merchantName: '鲜果超市',\n          activityType: '新店开业',\n          startDate: '2023-06-05',\n          endDate: '2023-06-15',\n          image: '/static/images/default-image.png'\n        },\n        {\n          id: '3',\n          title: '夏季特惠活动',\n          merchantName: '时尚服饰',\n          activityType: '限时特价',\n          startDate: '2023-06-10',\n          endDate: '2023-06-20',\n          image: '/static/images/default-image.png'\n        },\n        {\n          id: '4',\n          title: '会员专享日',\n          merchantName: '健身中心',\n          activityType: '会员专享',\n          startDate: '2023-06-15',\n          endDate: '2023-06-16',\n          image: '/static/images/default-image.png'\n        }\n]);\n\n\n// --- 方法 ---\n\nconst goBack = () => uni.navigateBack();\n\nconst previewImage = (index) => {\n  uni.previewImage({\n    current: index,\n    urls: activityData.images,\n  });\n};\n\nconst getActivityStatusText = () => {\n      const now = new Date();\n  const start = new Date(activityData.startDate);\n  const end = new Date(activityData.endDate);\n  if (now < start) return '未开始';\n  if (now > end) return '已结束';\n  return '进行中';\n};\n\nconst getActivityStatusClass = () => {\n  const status = getActivityStatusText();\n  if (status === '进行中') return 'status-ongoing';\n  if (status === '未开始') return 'status-not-started';\n        return 'status-ended';\n};\n\nconst openLocation = () => {\n      uni.openLocation({\n    latitude: 36.36, // 示例纬度\n    longitude: 114.23, // 示例经度\n    address: activityData.address,\n    name: activityData.merchantName,\n      });\n};\n\nconst callPhone = () => {\n  uni.makePhoneCall({ phoneNumber: activityData.phone });\n};\n\nconst openRedPacket = () => {\n  uni.showToast({ title: '红包功能正在开发中', icon: 'none' });\n};\n\nconst getRedPacketConditionText = () => \"点击领取\";\n\nconst navigateToActivityDetail = (id) => {\n  uni.navigateTo({ url: `/pages/publish/merchant-activity-detail?id=${id}` });\n};\n\nconst toggleCollect = () => {\n  isCollected.value = !isCollected.value;\n  uni.showToast({ title: isCollected.value ? '收藏成功' : '取消收藏' });\n};\n\nconst shareToFriend = () => {\n  uni.share({\n    provider: 'weixin',\n    scene: 'WXSceneSession',\n    type: 0,\n    title: activityData.title,\n    summary: activityData.description,\n    imageUrl: activityData.images[0],\n  });\n};\n\nconst contactMerchant = () => {\n  uni.showActionSheet({\n    itemList: ['拨打电话', '添加微信'],\n    success: (res) => {\n      if (res.tapIndex === 0) callPhone();\n      if (res.tapIndex === 1) {\n        uni.setClipboardData({\n          data: activityData.wechat,\n          success: () => uni.showToast({ title: '微信号已复制' }),\n        });\n      }\n    },\n  });\n};\n\nconst generateShareImage = () => {\n  uni.showLoading({ title: '海报生成中...' });\n  const ctx = uni.createCanvasContext('posterCanvas');\n  // ... (省略Canvas绘制逻辑)\n  // 此处应有完整的绘制海报的逻辑\n      setTimeout(() => {\n     // 模拟生成成功\n    posterUrl.value = '/static/images/default-image.png'; // 假设这是生成的海报\n    showPosterModal.value = true;\n        uni.hideLoading();\n      }, 1000);\n};\n\nconst closePosterModal = () => {\n  showPosterModal.value = false;\n};\n\nconst savePoster = () => {\n  uni.saveImageToPhotosAlbum({\n    filePath: posterUrl.value,\n    success: () => uni.showToast({ title: '保存成功' }),\n    fail: () => uni.showToast({ title: '保存失败', icon: 'error' }),\n  });\n};\n\n\n// --- 生命周期 ---\nonLoad((options) => {\n  activityId.value = options.id || '';\n  // fetchActivityData(activityId.value);\n});\n\nonMounted(() => {\n  const systemInfo = uni.getSystemInfoSync();\n  statusBarHeight.value = systemInfo.statusBarHeight || 20;\n});\n\n</script>\n\n<style lang=\"scss\">\n.detail-container {\n  min-height: 100vh;\n  background-color: #f5f7fa;\n  padding-bottom: 120rpx; /* 为底部操作栏留出空间 */\n}\n\n/* 自定义导航栏 */\n.custom-navbar {\n  background: linear-gradient(135deg, #0066FF, #0052CC);\n  height: 88rpx;\n  padding-top: 44px; /* 状态栏高度 */\n  display: flex;\n  align-items: center;\n  position: fixed; /* 改为固定定位 */\n  top: 0;\n  left: 0;\n  right: 0;\n  padding-bottom: 10rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n  z-index: 100; /* 提高z-index确保在最上层 */\n}\n\n.navbar-left {\n  position: absolute;\n  left: 30rpx;\n  display: flex;\n  align-items: center;\n}\n\n.back-icon {\n  width: 40rpx;\n  height: 40rpx;\n}\n\n.navbar-title {\n  color: #FFFFFF;\n  font-size: 36rpx;\n  font-weight: 600;\n  flex: 1;\n  text-align: center;\n}\n\n.navbar-right {\n  position: absolute;\n  right: 30rpx;\n}\n\n/* 详情包装器 */\n.detail-wrapper {\n  padding: 24rpx;\n  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */\n}\n\n/* 内容卡片通用样式 */\n.content-card {\n  background-color: #FFFFFF;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);\n}\n\n/* 章节标题 */\n.section-title {\n  font-size: 32rpx;\n  color: #333;\n  font-weight: 600;\n  margin-bottom: 20rpx;\n  position: relative;\n  padding-left: 20rpx;\n}\n\n.section-title::before {\n  content: \"\";\n  position: absolute;\n  left: 0;\n  top: 6rpx;\n  height: 32rpx;\n  width: 8rpx;\n  background: linear-gradient(to bottom, #3846cd, #5868e0);\n  border-radius: 4rpx;\n}\n\n/* 活动封面 */\n.cover-card {\n  padding: 0;\n  overflow: hidden;\n}\n\n.cover-swiper {\n  height: 400rpx;\n  width: 100%;\n}\n\n.cover-image {\n  width: 100%;\n  height: 100%;\n}\n\n/* 活动信息卡片 */\n.activity-header {\n  margin-bottom: 20rpx;\n}\n\n.title-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.main-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n  flex: 1;\n}\n\n.activity-status {\n  font-size: 24rpx;\n  padding: 4rpx 16rpx;\n  border-radius: 20rpx;\n  margin-left: 10rpx;\n}\n\n.status-upcoming {\n  background-color: #e6f7ff;\n  color: #1890ff;\n}\n\n.status-ongoing {\n  background-color: #f6ffed;\n  color: #52c41a;\n}\n\n.status-ended {\n  background-color: #f5f5f5;\n  color: #999;\n}\n\n.meta-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.tag-group {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.info-tag {\n  font-size: 22rpx;\n  color: #666;\n  background-color: #f5f7fa;\n  padding: 4rpx 16rpx;\n  border-radius: 20rpx;\n  margin-right: 10rpx;\n  margin-bottom: 10rpx;\n}\n\n.publish-time {\n  font-size: 22rpx;\n  color: #999;\n}\n\n/* 基本信息列表 */\n.basic-info {\n  border-top: 1rpx solid #f0f0f0;\n  padding-top: 20rpx;\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  padding: 16rpx 0;\n  border-bottom: 1rpx solid #f9f9f9;\n}\n\n.info-label {\n  color: #666;\n  font-size: 28rpx;\n}\n\n.info-value {\n  color: #333;\n  font-size: 28rpx;\n  font-weight: 500;\n}\n\n/* 描述和规则 */\n.description-content, .rules-content {\n  color: #333;\n  font-size: 28rpx;\n  line-height: 1.6;\n}\n\n/* 位置卡片 */\n.location-content {\n  display: flex;\n  align-items: center;\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #f9f9f9;\n}\n\n.location-text {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333;\n  margin: 0 10rpx;\n}\n\n.location-arrow {\n  color: #999;\n  font-size: 24rpx;\n}\n\n.location-map {\n  margin-top: 20rpx;\n  height: 200rpx;\n  border-radius: 8rpx;\n  overflow: hidden;\n}\n\n.map-preview {\n  width: 100%;\n  height: 100%;\n}\n\n/* 联系方式卡片 */\n.contact-content {\n  padding: 10rpx 0;\n}\n\n.contact-item {\n  display: flex;\n  justify-content: space-between;\n  padding: 16rpx 0;\n  border-bottom: 1rpx solid #f9f9f9;\n}\n\n.contact-label {\n  color: #666;\n  font-size: 28rpx;\n}\n\n.contact-value {\n  color: #333;\n  font-size: 28rpx;\n}\n\n.contact-phone {\n  color: #3846cd;\n}\n\n.contact-tips {\n  display: flex;\n  align-items: center;\n  margin-top: 20rpx;\n}\n\n.tips-icon {\n  color: #3846cd;\n  font-size: 28rpx;\n  margin-right: 10rpx;\n}\n\n.tips-text {\n  color: #999;\n  font-size: 24rpx;\n}\n\n/* 红包卡片 */\n.red-packet-section {\n  padding: 10rpx 0;\n}\n\n.red-packet-container {\n  background: linear-gradient(135deg, #FF9B9B, #FF6B6B);\n  border-radius: 12rpx;\n  padding: 4rpx;\n  position: relative;\n  overflow: hidden;\n}\n\n.red-packet-blur-bg {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: url(\"../../../static/images/tabbar/红包背景.png\") no-repeat center/cover;\n  opacity: 0.1;\n}\n\n.red-packet-content {\n  background: #FFF;\n  border-radius: 8rpx;\n  padding: 20rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  position: relative;\n  z-index: 1;\n}\n\n.red-packet-left {\n  display: flex;\n  align-items: center;\n}\n\n.red-packet-icon {\n  width: 60rpx;\n  height: 60rpx;\n  margin-right: 16rpx;\n}\n\n.red-packet-info {\n  display: flex;\n  flex-direction: column;\n}\n\n.red-packet-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #FF4D4F;\n  margin-bottom: 6rpx;\n}\n\n.red-packet-desc {\n  font-size: 22rpx;\n  color: #999;\n}\n\n.red-packet-right {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n}\n\n.red-packet-amount {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #FF4D4F;\n  margin-bottom: 10rpx;\n}\n\n.prefix {\n  font-size: 22rpx;\n  font-weight: normal;\n}\n\n.grab-btn {\n  background: linear-gradient(135deg, #FF9B9B, #FF6B6B);\n  color: #FFF;\n  font-size: 24rpx;\n  padding: 6rpx 20rpx;\n  border-radius: 20rpx;\n}\n\n/* 相关活动推荐 */\n.related-activities-list {\n  padding: 10rpx 0;\n}\n\n.related-activity-item {\n  display: flex;\n  align-items: center;\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #f9f9f9;\n}\n\n.related-activity-item:last-child {\n  border-bottom: none;\n}\n\n.related-activity-left {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 8rpx;\n  overflow: hidden;\n  margin-right: 20rpx;\n}\n\n.related-activity-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.related-activity-info {\n  flex: 1;\n}\n\n.related-activity-title {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8rpx;\n  line-height: 1.4;\n}\n\n.related-activity-meta {\n  display: flex;\n  margin-bottom: 6rpx;\n}\n\n.related-meta-text {\n  font-size: 24rpx;\n  color: #666;\n  margin-right: 16rpx;\n}\n\n.related-activity-date {\n  font-size: 22rpx;\n  color: #999;\n}\n\n/* 底部操作栏 */\n.footer-action-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 100rpx;\n  background: #FFF;\n  display: flex;\n  align-items: center;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  z-index: 90;\n  padding-bottom: env(safe-area-inset-bottom);\n}\n\n.action-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n}\n\n.action-icon {\n  font-size: 40rpx;\n  color: #999;\n  margin-bottom: 6rpx;\n}\n\n.icon-collected {\n  color: #3846cd;\n}\n\n.action-text {\n  font-size: 24rpx;\n  color: #666;\n}\n\n.contact-action {\n  flex: 2;\n  background: linear-gradient(135deg, #3846cd, #5868e0);\n  height: 70rpx;\n  border-radius: 35rpx;\n  margin: 0 20rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.contact-action .action-text {\n  color: #FFF;\n  font-size: 28rpx;\n  font-weight: 500;\n}\n\n/* 海报弹窗 */\n.poster-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.6);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 999;\n}\n\n.poster-container {\n  width: 80%;\n  background: #FFF;\n  border-radius: 16rpx;\n  overflow: hidden;\n}\n\n.poster-header {\n  padding: 20rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.poster-modal-title {\n  font-size: 32rpx;\n  font-weight: 500;\n  color: #333;\n}\n\n.close-icon {\n  font-size: 40rpx;\n  color: #999;\n  padding: 10rpx;\n}\n\n.poster-image-container {\n  padding: 30rpx;\n  display: flex;\n  justify-content: center;\n}\n\n.poster-preview {\n  width: 100%;\n  border-radius: 8rpx;\n}\n\n.poster-footer {\n  display: flex;\n  padding: 20rpx;\n  border-top: 1rpx solid #f0f0f0;\n}\n\n.poster-btn {\n  flex: 1;\n  height: 80rpx;\n  line-height: 80rpx;\n  text-align: center;\n  border-radius: 40rpx;\n  margin: 0 10rpx;\n  font-size: 28rpx;\n}\n\n.save-btn {\n  background: #f5f7fa;\n  color: #666;\n}\n\n.share-btn {\n  background: linear-gradient(135deg, #3846cd, #5868e0);\n  color: #FFF;\n}\n\n/* 悬浮海报按钮 */\n.float-poster-btn {\n  position: fixed;\n  right: 30rpx;\n  bottom: 140rpx;\n  width: 100rpx;\n  height: 100rpx;\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 50%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);\n  z-index: 80;\n}\n\n.poster-icon {\n  width: 40rpx;\n  height: 40rpx;\n  margin-bottom: 4rpx;\n}\n\n.poster-text {\n  font-size: 20rpx;\n  color: #666;\n}\n\n/* 隐藏的分享按钮 */\n.hidden-share-btn {\n  position: absolute;\n  opacity: 0;\n  width: 0;\n  height: 0;\n}\n\n/* 海报Canvas */\n.poster-canvas {\n  position: fixed;\n  top: -9999px;\n  left: -9999px;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/publish/merchant-activity-detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "uni", "onLoad", "onMounted", "MiniProgramPage"], "mappings": ";;;;;;;AAsNA,MAAM,aAAa,MAAW;;;;AAG9B,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,aAAaA,cAAAA,IAAI,EAAE;AACzB,UAAM,cAAcA,cAAAA,IAAI,KAAK;AAC7B,UAAM,kBAAkBA,cAAAA,IAAI,KAAK;AACjC,UAAM,YAAYA,cAAAA,IAAI,EAAE;AAExB,UAAM,eAAeC,cAAAA,SAAS;AAAA,MACtB,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,cAAc;AAAA,MACd,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,OAAO;AAAA,MACP,MAAM,CAAC,OAAO,QAAQ,KAAK;AAAA,MAC3B,QAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,MACD,SAAS;AAAA,MACT,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,cAAa,oBAAI,QAAO,QAAS,IAAG;AAAA;AAAA,MACpC,cAAc;AAAA,MACd,WAAW;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,MACR;AAAA,IACT,CAAC;AAED,UAAM,oBAAoBA,cAAAA,SAAS;AAAA,MAC3B;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,cAAc;AAAA,QACd,cAAc;AAAA,QACd,WAAW;AAAA,QACX,SAAS;AAAA,QACT,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,cAAc;AAAA,QACd,cAAc;AAAA,QACd,WAAW;AAAA,QACX,SAAS;AAAA,QACT,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,cAAc;AAAA,QACd,cAAc;AAAA,QACd,WAAW;AAAA,QACX,SAAS;AAAA,QACT,OAAO;AAAA,MACR;AAAA,IACT,CAAC;AAKD,UAAM,SAAS,MAAMC,oBAAI;AAEzB,UAAM,eAAe,CAAC,UAAU;AAC9BA,oBAAAA,MAAI,aAAa;AAAA,QACf,SAAS;AAAA,QACT,MAAM,aAAa;AAAA,MACvB,CAAG;AAAA,IACH;AAEA,UAAM,wBAAwB,MAAM;AAC9B,YAAM,MAAM,oBAAI;AACpB,YAAM,QAAQ,IAAI,KAAK,aAAa,SAAS;AAC7C,YAAM,MAAM,IAAI,KAAK,aAAa,OAAO;AACzC,UAAI,MAAM;AAAO,eAAO;AACxB,UAAI,MAAM;AAAK,eAAO;AACtB,aAAO;AAAA,IACT;AAEA,UAAM,yBAAyB,MAAM;AACnC,YAAM,SAAS;AACf,UAAI,WAAW;AAAO,eAAO;AAC7B,UAAI,WAAW;AAAO,eAAO;AACvB,aAAO;AAAA,IACf;AAEA,UAAM,eAAe,MAAM;AACrBA,oBAAAA,MAAI,aAAa;AAAA,QACnB,UAAU;AAAA;AAAA,QACV,WAAW;AAAA;AAAA,QACX,SAAS,aAAa;AAAA,QACtB,MAAM,aAAa;AAAA,MACvB,CAAO;AAAA,IACP;AAEA,UAAM,YAAY,MAAM;AACtBA,oBAAG,MAAC,cAAc,EAAE,aAAa,aAAa,MAAO,CAAA;AAAA,IACvD;AAEA,UAAM,gBAAgB,MAAM;AAC1BA,oBAAG,MAAC,UAAU,EAAE,OAAO,aAAa,MAAM,OAAM,CAAE;AAAA,IACpD;AAEA,UAAM,4BAA4B,MAAM;AAExC,UAAM,2BAA2B,CAAC,OAAO;AACvCA,oBAAG,MAAC,WAAW,EAAE,KAAK,8CAA8C,EAAE,GAAE,CAAE;AAAA,IAC5E;AAEA,UAAM,gBAAgB,MAAM;AAC1B,kBAAY,QAAQ,CAAC,YAAY;AACjCA,0BAAI,UAAU,EAAE,OAAO,YAAY,QAAQ,SAAS,OAAM,CAAE;AAAA,IAC9D;AAEA,UAAM,gBAAgB,MAAM;AAC1BA,oBAAAA,MAAI,MAAM;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO,aAAa;AAAA,QACpB,SAAS,aAAa;AAAA,QACtB,UAAU,aAAa,OAAO,CAAC;AAAA,MACnC,CAAG;AAAA,IACH;AAEA,UAAM,kBAAkB,MAAM;AAC5BA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,QAAQ,MAAM;AAAA,QACzB,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,aAAa;AAAG,sBAAS;AACjC,cAAI,IAAI,aAAa,GAAG;AACtBA,0BAAAA,MAAI,iBAAiB;AAAA,cACnB,MAAM,aAAa;AAAA,cACnB,SAAS,MAAMA,cAAAA,MAAI,UAAU,EAAE,OAAO,SAAQ,CAAE;AAAA,YAC1D,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,qBAAqB,MAAM;AAC/BA,oBAAAA,MAAI,YAAY,EAAE,OAAO,WAAY,CAAA;AACzBA,oBAAG,MAAC,oBAAoB,cAAc;AAG9C,iBAAW,MAAM;AAEnB,kBAAU,QAAQ;AAClB,wBAAgB,QAAQ;AACpBA,sBAAG,MAAC,YAAW;AAAA,MAChB,GAAE,GAAI;AAAA,IACb;AAEA,UAAM,mBAAmB,MAAM;AAC7B,sBAAgB,QAAQ;AAAA,IAC1B;AAEA,UAAM,aAAa,MAAM;AACvBA,oBAAAA,MAAI,uBAAuB;AAAA,QACzB,UAAU,UAAU;AAAA,QACpB,SAAS,MAAMA,cAAAA,MAAI,UAAU,EAAE,OAAO,OAAM,CAAE;AAAA,QAC9C,MAAM,MAAMA,cAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,SAAS;AAAA,MAC9D,CAAG;AAAA,IACH;AAIAC,kBAAM,OAAC,CAAC,YAAY;AAClB,iBAAW,QAAQ,QAAQ,MAAM;AAAA,IAEnC,CAAC;AAEDC,kBAAAA,UAAU,MAAM;AACd,YAAM,aAAaF,oBAAI;AACvB,sBAAgB,QAAQ,WAAW,mBAAmB;AAAA,IACxD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9YD,GAAG,WAAWG,SAAe;"}