<template>
  <view class="product-publish-container">
    <!-- 状态栏占位 -->
    <view class="status-bar-placeholder"></view>
    
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-left" @tap="goBack">
        <view class="back-button">
          <text class="icon-back">←</text>
        </view>
      </view>
      <view class="navbar-title">
        <text class="title-text">发布商品</text>
      </view>
      <view class="navbar-right">
        <view class="help-button" @tap="showHelp">
          <text class="icon-help">?</text>
        </view>
      </view>
    </view>
    
    <!-- 主内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 商品基本信息 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">基本信息</text>
          <text class="section-subtitle">填写商品的基本信息</text>
        </view>
        
        <!-- 商品图片上传 -->
        <view class="image-upload-area">
          <view class="upload-title">
            <text class="required">*</text>
            <text>商品图片</text>
            <text class="upload-desc">最多9张，建议尺寸800x800</text>
          </view>
          
          <view class="image-grid">
            <view v-for="(image, index) in productForm.images" :key="index" class="image-item">
              <image :src="image" mode="aspectFill" class="preview-image"></image>
              <view class="delete-btn" @tap="removeImage(index)">×</view>
            </view>
            
            <view v-if="productForm.images.length < 9" class="upload-item" @tap="chooseImage">
              <text class="upload-icon">+</text>
              <text class="upload-text">上传图片</text>
            </view>
          </view>
        </view>
        
        <!-- 商品名称 -->
        <view class="form-item">
          <view class="form-label">
            <text class="required">*</text>
            <text>商品名称</text>
          </view>
          <view class="form-input-wrap">
            <input 
              class="form-input" 
              type="text" 
              v-model="productForm.name" 
              placeholder="请输入商品名称（2-40字）" 
              maxlength="40"
            />
            <text class="input-counter">{{productForm.name.length}}/40</text>
          </view>
        </view>
        
        <!-- 商品分类 -->
        <view class="form-item">
          <view class="form-label">
            <text class="required">*</text>
            <text>商品分类</text>
          </view>
          <view class="form-input-wrap selector" @tap="showCategoryPicker">
            <text class="selector-text" :class="{ placeholder: !productForm.category }">
              {{ productForm.category || '请选择商品分类' }}
            </text>
            <text class="selector-arrow">›</text>
          </view>
        </view>
        
        <!-- 价格区域 -->
        <view class="price-row">
          <!-- 商品价格 -->
          <view class="form-item price-item">
            <view class="form-label">
              <text class="required">*</text>
              <text>售价</text>
            </view>
            <view class="form-input-wrap price-input">
              <text class="price-symbol">¥</text>
              <input 
                class="form-input" 
                type="digit" 
                v-model="productForm.price" 
                placeholder="0.00" 
              />
            </view>
          </view>
          
          <!-- 原价 -->
          <view class="form-item price-item">
            <view class="form-label">
              <text>原价</text>
              <text class="label-tip">(选填)</text>
            </view>
            <view class="form-input-wrap price-input">
              <text class="price-symbol">¥</text>
              <input 
                class="form-input" 
                type="digit" 
                v-model="productForm.originalPrice" 
                placeholder="0.00" 
              />
            </view>
          </view>
        </view>
        
        <!-- 库存 -->
        <view class="form-item">
          <view class="form-label">
            <text class="required">*</text>
            <text>库存数量</text>
          </view>
          <view class="form-input-wrap stock-input">
            <view class="stock-btn minus" @tap="decreaseStock" :class="{ disabled: productForm.stock <= 0 }">-</view>
            <input 
              class="form-input stock-value" 
              type="number" 
              v-model="productForm.stock" 
            />
            <view class="stock-btn plus" @tap="increaseStock">+</view>
          </view>
        </view>
      </view>
      
      <!-- 商品详情 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">商品详情</text>
          <text class="section-subtitle">详细描述您的商品特点和卖点</text>
        </view>
        
        <!-- 商品描述 -->
        <view class="form-item">
          <view class="form-label">
            <text class="required">*</text>
            <text>商品描述</text>
          </view>
          <view class="form-input-wrap textarea-wrap">
            <textarea 
              class="form-textarea" 
              v-model="productForm.description" 
              placeholder="请详细描述商品的特点、用途、材质等信息" 
              maxlength="500"
            ></textarea>
            <text class="input-counter textarea-counter">{{productForm.description ? productForm.description.length : 0}}/500</text>
          </view>
        </view>
        
        <!-- 规格参数 -->
        <view class="form-item">
          <view class="form-label specs-header">
            <view class="specs-title">
              <text>规格参数</text>
              <text class="label-tip">(选填)</text>
            </view>
            <view class="add-spec-btn" @tap="addSpec">
              <text class="add-spec-icon">+</text>
              <text class="add-spec-text">添加规格</text>
            </view>
          </view>
          
          <view class="specs-list">
            <view v-for="(spec, index) in productForm.specs" :key="index" class="spec-item">
              <view class="spec-inputs">
                <input 
                  class="spec-key" 
                  type="text" 
                  v-model="spec.key" 
                  placeholder="规格名称" 
                />
                <text class="spec-separator">:</text>
                <input 
                  class="spec-value" 
                  type="text" 
                  v-model="spec.value" 
                  placeholder="规格值" 
                />
              </view>
              <view class="delete-spec-btn" @tap="removeSpec(index)">×</view>
            </view>
            
            <view v-if="productForm.specs.length === 0" class="empty-specs">
              <text>点击"添加规格"按钮添加商品规格参数</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 营销设置 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">营销设置</text>
          <text class="section-subtitle">设置商品的营销相关信息</text>
        </view>
        
        <!-- 营销标签 -->
        <view class="form-item">
          <view class="form-label">
            <text>营销标签</text>
            <text class="label-tip">(选填)</text>
          </view>
          
          <view class="tags-container">
            <view 
              v-for="(tag, index) in marketingTags" 
              :key="index" 
              class="tag-item" 
              :class="{ active: selectedTags.includes(tag.id) }"
              @tap="toggleTag(tag.id)"
            >
              <text>{{tag.name}}</text>
            </view>
          </view>
        </view>
        
        <!-- 限购设置 -->
        <view class="form-item">
          <view class="form-label">
            <text>限购数量</text>
            <text class="label-tip">(选填，0表示不限购)</text>
          </view>
          <view class="form-input-wrap">
            <input 
              class="form-input" 
              type="number" 
              v-model="productForm.purchaseLimit" 
              placeholder="0" 
            />
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-action-bar">
      <button class="action-button save-draft" @tap="saveDraft">保存草稿</button>
      <button class="action-button publish" @tap="publishProduct">立即发布</button>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue';

// 商品表单数据
const productForm = reactive({
  name: '',
  category: '',
  price: '',
  originalPrice: '',
  stock: 0,
  images: [],
  description: '',
  specs: [],
  purchaseLimit: 0
});

// 营销标签
const marketingTags = reactive([
  { id: 1, name: '新品' },
  { id: 2, name: '热卖' },
  { id: 3, name: '限时' },
  { id: 4, name: '促销' },
  { id: 5, name: '包邮' },
  { id: 6, name: '品质保障' }
]);

// 已选标签
const selectedTags = reactive([]);

// 方法
const goBack = () => {
  uni.navigateBack();
};

const showHelp = () => {
  uni.showToast({
    title: '帮助中心即将上线',
    icon: 'none'
  });
};

// 图片上传
const chooseImage = () => {
  uni.chooseImage({
    count: 9 - productForm.images.length,
    sizeType: ['original', 'compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      productForm.images = [...productForm.images, ...res.tempFilePaths];
    }
  });
};

const removeImage = (index) => {
  productForm.images.splice(index, 1);
};

// 分类选择
const showCategoryPicker = () => {
  uni.showActionSheet({
    itemList: ['食品饮料', '服装服饰', '美妆个护', '家居家纺', '数码电器', '其他分类'],
    success: (res) => {
      const categories = ['食品饮料', '服装服饰', '美妆个护', '家居家纺', '数码电器', '其他分类'];
      productForm.category = categories[res.tapIndex];
    }
  });
};

// 库存操作
const increaseStock = () => {
  productForm.stock++;
};

const decreaseStock = () => {
  if (productForm.stock > 0) {
    productForm.stock--;
  }
};

const saveDraft = () => {
  uni.showToast({
    title: '已保存草稿',
    icon: 'success'
  });
};

// 规格参数操作
const addSpec = () => {
  if (productForm.specs.length >= 10) {
    return uni.showToast({
      title: '最多添加10个规格参数',
      icon: 'none'
    });
  }
  productForm.specs.push({ key: '', value: '' });
};

const removeSpec = (index) => {
  productForm.specs.splice(index, 1);
};

// 标签操作
const toggleTag = (tagId) => {
  const index = selectedTags.indexOf(tagId);
  if (index > -1) {
    selectedTags.splice(index, 1);
  } else {
    if (selectedTags.length >= 3) {
      return uni.showToast({
        title: '最多选择3个标签',
        icon: 'none'
      });
    }
    selectedTags.push(tagId);
  }
};

const publishProduct = () => {
  // 表单验证
  if (!productForm.name) {
    return uni.showToast({ title: '请输入商品名称', icon: 'none' });
  }
  if (!productForm.category) {
    return uni.showToast({ title: '请选择商品分类', icon: 'none' });
  }
  if (!productForm.price) {
    return uni.showToast({ title: '请输入商品价格', icon: 'none' });
  }
  if (productForm.images.length === 0) {
    return uni.showToast({ title: '请至少上传一张商品图片', icon: 'none' });
  }
  if (!productForm.description) {
    return uni.showToast({ title: '请输入商品描述', icon: 'none' });
  }
  
  // 收集所有数据
  const productData = {
    ...productForm,
    tags: selectedTags.map(id => {
      const tag = marketingTags.find(t => t.id === id);
      return tag ? tag.name : '';
    }).filter(Boolean)
  };
  
  console.log('商品数据:', productData);
  
  uni.showLoading({ title: '正在发布...' });
  
  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({ title: '发布成功', icon: 'success' });
    
    // 延迟返回上一页
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }, 2000);
};
</script>

<style>
/* 全局样式 */
.product-publish-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
  position: relative;
}

/* 状态栏占位 */
.status-bar-placeholder {
  height: var(--status-bar-height, 20px);
  width: 100%;
  background: linear-gradient(135deg, #3A86FF, #5E60CE);
}

/* 导航栏样式 */
.custom-navbar {
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 16px;
  background: linear-gradient(135deg, #3A86FF, #5E60CE);
  position: relative;
}

.navbar-left, .navbar-right {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-title {
  flex: 1;
  text-align: center;
}

.title-text {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
}

.back-button, .help-button {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-back, .icon-help {
  color: #FFFFFF;
  font-size: 18px;
}

/* 内容区域 */
.content-scroll {
  flex: 1;
  padding: 16px;
}

/* 表单区域 */
.form-section {
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 6px;
  display: block;
}

.section-subtitle {
  font-size: 14px;
  color: #999;
}

/* 图片上传区域 */
.image-upload-area {
  margin-bottom: 20px;
}

.upload-title {
  margin-bottom: 12px;
  font-size: 15px;
  color: #333;
}

.required {
  color: #FF3B30;
  margin-right: 4px;
}

.upload-desc {
  font-size: 12px;
  color: #999;
  margin-left: 8px;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -6px;
}

.image-item, .upload-item {
  width: calc(33.33% - 12px);
  margin: 6px;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
}

.image-item::before, .upload-item::before {
  content: "";
  display: block;
  padding-top: 100%;
}

.preview-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-btn {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: rgba(0, 0, 0, 0.6);
  color: #FFF;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  z-index: 1;
}

.upload-item {
  background-color: #F5F7FA;
  border: 1px dashed #DDD;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  font-size: 24px;
  color: #999;
  margin-bottom: 4px;
}

.upload-text {
  font-size: 12px;
  color: #999;
}

/* 表单项 */
.form-item {
  margin-bottom: 20px;
}

.form-label {
  font-size: 15px;
  color: #333;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.label-tip {
  font-size: 12px;
  color: #999;
  margin-left: 4px;
}

.form-input-wrap {
  background-color: #F5F7FA;
  border-radius: 12px;
  padding: 0 16px;
  height: 48px;
  display: flex;
  align-items: center;
  position: relative;
}

.form-input {
  flex: 1;
  height: 100%;
  font-size: 15px;
}

.input-counter {
  font-size: 12px;
  color: #999;
}

/* 选择器 */
.selector {
  justify-content: space-between;
}

.selector-text {
  font-size: 15px;
  color: #333;
}

.placeholder {
  color: #999;
}

.selector-arrow {
  font-size: 18px;
  color: #999;
  transform: rotate(90deg);
}

/* 价格区域 */
.price-row {
  display: flex;
  margin: 0 -8px;
  margin-bottom: 20px;
}

.price-item {
  flex: 1;
  padding: 0 8px;
  margin-bottom: 0;
}

.price-input {
  position: relative;
}

.price-symbol {
  font-size: 15px;
  color: #333;
  margin-right: 4px;
}

/* 库存控制 */
.stock-input {
  display: flex;
  align-items: center;
  padding: 0;
}

.stock-btn {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #3A86FF;
  background-color: rgba(58, 134, 255, 0.1);
}

.stock-btn.disabled {
  color: #CCC;
}

.stock-value {
  flex: 1;
  text-align: center;
  font-size: 15px;
}

/* 底部操作栏 */
.bottom-action-bar {
  display: flex;
  padding: 16px;
  background-color: #FFFFFF;
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.05);
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.action-button {
  flex: 1;
  height: 50px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 8px;
  border: none;
}

.save-draft {
  background-color: #E0E7FF;
  color: #4F46E5;
}

.publish {
  background: linear-gradient(135deg, #3A86FF, #5E60CE);
  color: #FFFFFF;
}

/* 文本域样式 */
.textarea-wrap {
  height: auto;
  padding: 12px 16px;
  min-height: 120px;
}

.form-textarea {
  width: 100%;
  height: 100px;
  font-size: 15px;
  line-height: 1.5;
}

.textarea-counter {
  position: absolute;
  bottom: 12px;
  right: 16px;
}

/* 规格参数样式 */
.specs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.specs-title {
  display: flex;
  align-items: center;
}

.add-spec-btn {
  display: flex;
  align-items: center;
  background-color: rgba(58, 134, 255, 0.1);
  padding: 6px 12px;
  border-radius: 16px;
}

.add-spec-icon {
  font-size: 16px;
  color: #3A86FF;
  margin-right: 4px;
}

.add-spec-text {
  font-size: 14px;
  color: #3A86FF;
}

.specs-list {
  background-color: #F5F7FA;
  border-radius: 12px;
  padding: 8px;
}

.spec-item {
  display: flex;
  align-items: center;
  background-color: #FFFFFF;
  border-radius: 8px;
  padding: 8px;
  margin-bottom: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.spec-item:last-child {
  margin-bottom: 0;
}

.spec-inputs {
  flex: 1;
  display: flex;
  align-items: center;
}

.spec-key {
  width: 40%;
  font-size: 14px;
  padding: 6px 0;
}

.spec-separator {
  margin: 0 8px;
  color: #999;
}

.spec-value {
  flex: 1;
  font-size: 14px;
  padding: 6px 0;
}

.delete-spec-btn {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

.empty-specs {
  padding: 16px;
  text-align: center;
  color: #999;
  font-size: 14px;
}

/* 标签样式 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -6px;
}

.tag-item {
  margin: 6px;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  background-color: #F5F7FA;
  color: #666;
  transition: all 0.2s;
}

.tag-item:nth-child(1) {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.tag-item:nth-child(2) {
  background-color: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}

.tag-item:nth-child(3) {
  background-color: rgba(255, 45, 85, 0.1);
  color: #FF2D55;
}

.tag-item:nth-child(4) {
  background-color: rgba(175, 82, 222, 0.1);
  color: #AF52DE;
}

.tag-item:nth-child(5) {
  background-color: rgba(90, 200, 250, 0.1);
  color: #5AC8FA;
}

.tag-item:nth-child(6) {
  background-color: rgba(0, 122, 255, 0.1);
  color: #007AFF;
}

.tag-item.active {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tag-item.active:nth-child(1) {
  background-color: #34C759;
  color: #FFFFFF;
}

.tag-item.active:nth-child(2) {
  background-color: #FF9500;
  color: #FFFFFF;
}

.tag-item.active:nth-child(3) {
  background-color: #FF2D55;
  color: #FFFFFF;
}

.tag-item.active:nth-child(4) {
  background-color: #AF52DE;
  color: #FFFFFF;
}

.tag-item.active:nth-child(5) {
  background-color: #5AC8FA;
  color: #FFFFFF;
}

.tag-item.active:nth-child(6) {
  background-color: #007AFF;
  color: #FFFFFF;
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .product-publish-container {
    background-color: #1C1C1E;
  }
  
  .form-section {
    background-color: #2C2C2E;
  }
  
  .section-title {
    color: #FFFFFF;
  }
  
  .form-label {
    color: #FFFFFF;
  }
  
  .form-input-wrap {
    background-color: #3A3A3C;
  }
  
  .form-input, .form-textarea {
    color: #FFFFFF;
  }
  
  .upload-item {
    background-color: #3A3A3C;
    border-color: #666;
  }
  
  .spec-item {
    background-color: #3A3A3C;
  }
  
  .specs-list {
    background-color: #2C2C2E;
  }
  
  .bottom-action-bar {
    background-color: #2C2C2E;
  }
}
</style>
