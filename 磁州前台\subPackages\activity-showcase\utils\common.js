/**
 * 磁州前台通用工具函数
 * 提供常用的工具方法、数据处理、格式化等功能
 */

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} wait 等待时间（毫秒）
 * @param {boolean} immediate 是否立即执行
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait = 300, immediate = false) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      timeout = null
      if (!immediate) func.apply(this, args)
    }
    const callNow = immediate && !timeout
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    if (callNow) func.apply(this, args)
  }
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} limit 时间间隔（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit = 300) {
  let inThrottle
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 深拷贝对象
 * @param {any} obj 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

/**
 * 生成唯一ID
 * @param {string} prefix 前缀
 * @returns {string} 唯一ID
 */
export function generateId(prefix = 'id') {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @param {number} decimals 小数位数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

/**
 * 格式化数字
 * @param {number} num 数字
 * @param {number} precision 精度
 * @returns {string} 格式化后的数字
 */
export function formatNumber(num, precision = 2) {
  if (num >= 1e9) {
    return (num / 1e9).toFixed(precision) + 'B'
  } else if (num >= 1e6) {
    return (num / 1e6).toFixed(precision) + 'M'
  } else if (num >= 1e3) {
    return (num / 1e3).toFixed(precision) + 'K'
  }
  return num.toString()
}

/**
 * 格式化价格
 * @param {number} price 价格
 * @param {string} currency 货币符号
 * @param {number} decimals 小数位数
 * @returns {string} 格式化后的价格
 */
export function formatPrice(price, currency = '¥', decimals = 2) {
  if (typeof price !== 'number') return currency + '0.00'
  return currency + price.toFixed(decimals)
}

/**
 * 格式化时间
 * @param {Date|string|number} date 日期
 * @param {string} format 格式
 * @returns {string} 格式化后的时间
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 获取相对时间
 * @param {Date|string|number} date 日期
 * @returns {string} 相对时间描述
 */
export function getRelativeTime(date) {
  const now = new Date()
  const target = new Date(date)
  const diff = now.getTime() - target.getTime()
  
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const week = 7 * day
  const month = 30 * day
  const year = 365 * day
  
  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return Math.floor(diff / minute) + '分钟前'
  } else if (diff < day) {
    return Math.floor(diff / hour) + '小时前'
  } else if (diff < week) {
    return Math.floor(diff / day) + '天前'
  } else if (diff < month) {
    return Math.floor(diff / week) + '周前'
  } else if (diff < year) {
    return Math.floor(diff / month) + '个月前'
  } else {
    return Math.floor(diff / year) + '年前'
  }
}

/**
 * 验证手机号
 * @param {string} phone 手机号
 * @returns {boolean} 是否有效
 */
export function validatePhone(phone) {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 验证邮箱
 * @param {string} email 邮箱
 * @returns {boolean} 是否有效
 */
export function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 验证身份证号
 * @param {string} idCard 身份证号
 * @returns {boolean} 是否有效
 */
export function validateIdCard(idCard) {
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  return idCardRegex.test(idCard)
}

/**
 * 隐藏手机号中间四位
 * @param {string} phone 手机号
 * @returns {string} 隐藏后的手机号
 */
export function hidePhone(phone) {
  if (!phone || phone.length !== 11) return phone
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

/**
 * 隐藏邮箱用户名
 * @param {string} email 邮箱
 * @returns {string} 隐藏后的邮箱
 */
export function hideEmail(email) {
  if (!email || !email.includes('@')) return email
  const [username, domain] = email.split('@')
  if (username.length <= 2) return email
  const hiddenUsername = username.charAt(0) + '*'.repeat(username.length - 2) + username.charAt(username.length - 1)
  return hiddenUsername + '@' + domain
}

/**
 * 获取URL参数
 * @param {string} name 参数名
 * @param {string} url URL地址
 * @returns {string|null} 参数值
 */
export function getUrlParam(name, url = window.location.href) {
  const regex = new RegExp('[?&]' + name + '=([^&#]*)', 'i')
  const results = regex.exec(url)
  return results ? decodeURIComponent(results[1]) : null
}

/**
 * 构建URL参数
 * @param {Object} params 参数对象
 * @returns {string} URL参数字符串
 */
export function buildUrlParams(params) {
  const searchParams = new URLSearchParams()
  Object.keys(params).forEach(key => {
    if (params[key] !== null && params[key] !== undefined) {
      searchParams.append(key, params[key])
    }
  })
  return searchParams.toString()
}

/**
 * 数组去重
 * @param {Array} arr 数组
 * @param {string} key 去重的键名（对象数组）
 * @returns {Array} 去重后的数组
 */
export function uniqueArray(arr, key = null) {
  if (!Array.isArray(arr)) return []
  
  if (key) {
    const seen = new Set()
    return arr.filter(item => {
      const value = item[key]
      if (seen.has(value)) {
        return false
      }
      seen.add(value)
      return true
    })
  }
  
  return [...new Set(arr)]
}

/**
 * 数组分组
 * @param {Array} arr 数组
 * @param {string|Function} key 分组键或函数
 * @returns {Object} 分组后的对象
 */
export function groupBy(arr, key) {
  if (!Array.isArray(arr)) return {}
  
  return arr.reduce((groups, item) => {
    const groupKey = typeof key === 'function' ? key(item) : item[key]
    if (!groups[groupKey]) {
      groups[groupKey] = []
    }
    groups[groupKey].push(item)
    return groups
  }, {})
}

/**
 * 数组排序
 * @param {Array} arr 数组
 * @param {string} key 排序键
 * @param {string} order 排序方向 asc|desc
 * @returns {Array} 排序后的数组
 */
export function sortArray(arr, key, order = 'asc') {
  if (!Array.isArray(arr)) return []
  
  return arr.sort((a, b) => {
    const aVal = a[key]
    const bVal = b[key]
    
    if (aVal < bVal) {
      return order === 'asc' ? -1 : 1
    }
    if (aVal > bVal) {
      return order === 'asc' ? 1 : -1
    }
    return 0
  })
}

/**
 * 随机打乱数组
 * @param {Array} arr 数组
 * @returns {Array} 打乱后的数组
 */
export function shuffleArray(arr) {
  if (!Array.isArray(arr)) return []
  
  const shuffled = [...arr]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}

/**
 * 获取随机数
 * @param {number} min 最小值
 * @param {number} max 最大值
 * @returns {number} 随机数
 */
export function getRandomNumber(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

/**
 * 获取随机字符串
 * @param {number} length 长度
 * @param {string} chars 字符集
 * @returns {string} 随机字符串
 */
export function getRandomString(length = 8, chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 颜色转换
 * @param {string} color 颜色值
 * @param {string} format 目标格式 hex|rgb|hsl
 * @returns {string} 转换后的颜色值
 */
export function convertColor(color, format = 'hex') {
  // 简化实现，实际项目中可以使用专门的颜色处理库
  if (format === 'hex' && color.startsWith('rgb')) {
    const rgb = color.match(/\d+/g)
    if (rgb && rgb.length >= 3) {
      const hex = rgb.slice(0, 3).map(x => {
        const hex = parseInt(x).toString(16)
        return hex.length === 1 ? '0' + hex : hex
      }).join('')
      return '#' + hex
    }
  }
  return color
}

/**
 * 本地存储封装
 */
export const storage = {
  set(key, value, expire = null) {
    const data = {
      value,
      expire: expire ? Date.now() + expire : null
    }
    try {
      uni.setStorageSync(key, JSON.stringify(data))
    } catch (e) {
      console.error('存储数据失败:', e)
    }
  },
  
  get(key, defaultValue = null) {
    try {
      const data = uni.getStorageSync(key)
      if (!data) return defaultValue
      
      const parsed = JSON.parse(data)
      if (parsed.expire && Date.now() > parsed.expire) {
        this.remove(key)
        return defaultValue
      }
      
      return parsed.value
    } catch (e) {
      console.error('读取数据失败:', e)
      return defaultValue
    }
  },
  
  remove(key) {
    try {
      uni.removeStorageSync(key)
    } catch (e) {
      console.error('删除数据失败:', e)
    }
  },
  
  clear() {
    try {
      uni.clearStorageSync()
    } catch (e) {
      console.error('清空数据失败:', e)
    }
  }
}

/**
 * 平台检测
 */
export const platform = {
  isWeChat: () => {
    // #ifdef MP-WEIXIN
    return true
    // #endif
    return false
  },
  
  isAlipay: () => {
    // #ifdef MP-ALIPAY
    return true
    // #endif
    return false
  },
  
  isH5: () => {
    // #ifdef H5
    return true
    // #endif
    return false
  },
  
  isApp: () => {
    // #ifdef APP-PLUS
    return true
    // #endif
    return false
  },
  
  isIOS: () => {
    const system = uni.getSystemInfoSync()
    return system.platform === 'ios'
  },
  
  isAndroid: () => {
    const system = uni.getSystemInfoSync()
    return system.platform === 'android'
  }
}

/**
 * 默认导出所有工具函数
 */
export default {
  debounce,
  throttle,
  deepClone,
  generateId,
  formatFileSize,
  formatNumber,
  formatPrice,
  formatDate,
  getRelativeTime,
  validatePhone,
  validateEmail,
  validateIdCard,
  hidePhone,
  hideEmail,
  getUrlParam,
  buildUrlParams,
  uniqueArray,
  groupBy,
  sortArray,
  shuffleArray,
  getRandomNumber,
  getRandomString,
  convertColor,
  storage,
  platform
}
