/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-0ba2294a, html.data-v-0ba2294a, #app.data-v-0ba2294a, .index-container.data-v-0ba2294a {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.withdraw-container.data-v-0ba2294a {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
  padding-top: calc(44px + var(--status-bar-height));
}
.custom-navbar.data-v-0ba2294a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  padding: 0 30rpx;
  padding-top: 44px;
  /* 状态栏高度 */
  position: fixed;
  /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  background-image: linear-gradient(135deg, #0066FF, #0052CC);
  /* 改为与发布页一致的渐变角度 */
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.15);
  z-index: 100;
  /* 提高z-index确保在最上层 */
}
.navbar-left.data-v-0ba2294a {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 20;
  /* 确保在标题上层，可以被点击 */
}
.back-icon.data-v-0ba2294a {
  width: 100%;
  height: 100%;
}
.navbar-title.data-v-0ba2294a {
  position: absolute;
  left: 0;
  right: 0;
  font-size: 36rpx;
  font-weight: 700;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  text-align: center;
}
.navbar-right.data-v-0ba2294a {
  width: 40rpx;
  height: 40rpx;
}
.safe-area-top.data-v-0ba2294a {
  height: var(--status-bar-height);
  width: 100%;
  background-image: linear-gradient(135deg, #0066FF, #0052CC);
}
.balance-card.data-v-0ba2294a {
  margin: 30rpx;
  background: linear-gradient(135deg, #1677FF, #0E5FD8);
  border-radius: 16rpx;
  padding: 40rpx;
  color: #ffffff;
  box-shadow: 0 10rpx 20rpx rgba(22, 119, 255, 0.2);
}
.balance-title.data-v-0ba2294a {
  font-size: 28rpx;
  opacity: 0.9;
}
.balance-amount.data-v-0ba2294a {
  font-size: 60rpx;
  font-weight: bold;
  margin: 20rpx 0 30rpx;
}
.balance-detail.data-v-0ba2294a {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 20rpx;
}
.detail-item.data-v-0ba2294a {
  display: flex;
  flex-direction: column;
  font-size: 24rpx;
}
.detail-item text.data-v-0ba2294a:first-child {
  opacity: 0.8;
  margin-bottom: 8rpx;
}
.detail-item text.data-v-0ba2294a:last-child {
  font-size: 28rpx;
  font-weight: 500;
}
.withdraw-form.data-v-0ba2294a {
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.form-title.data-v-0ba2294a, .method-title.data-v-0ba2294a {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
  color: #333333;
}
.amount-input.data-v-0ba2294a {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #eeeeee;
  padding-bottom: 20rpx;
  margin-bottom: 10rpx;
}
.currency.data-v-0ba2294a {
  font-size: 50rpx;
  font-weight: bold;
  margin-right: 10rpx;
  color: #333333;
}
.amount-input input.data-v-0ba2294a {
  font-size: 50rpx;
  flex: 1;
}
.amount-tips.data-v-0ba2294a {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 30rpx;
}
.all-btn.data-v-0ba2294a {
  color: #1677FF;
}
.actual-amount.data-v-0ba2294a {
  background-color: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
  display: flex;
  justify-content: space-between;
  font-size: 28rpx;
  margin-bottom: 30rpx;
}
.actual-amount .amount.data-v-0ba2294a {
  color: #1677FF;
  font-weight: 500;
}
.withdraw-method.data-v-0ba2294a {
  margin-top: 30rpx;
}
.method-list.data-v-0ba2294a {
  display: flex;
  justify-content: space-between;
}
.method-item.data-v-0ba2294a {
  width: 30%;
  height: 150rpx;
  border: 1px solid #eeeeee;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}
.method-item image.data-v-0ba2294a {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}
.method-item text.data-v-0ba2294a {
  font-size: 26rpx;
  color: #666666;
}
.method-item.active.data-v-0ba2294a {
  border-color: #1677FF;
  background-color: rgba(22, 119, 255, 0.05);
}
.method-item .check-icon.data-v-0ba2294a {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: #1677FF;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  border-top-left-radius: 12rpx;
}
.account-input.data-v-0ba2294a {
  margin-top: 30rpx;
}
.input-item.data-v-0ba2294a {
  margin-bottom: 20rpx;
}
.input-item .label.data-v-0ba2294a {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 10rpx;
  display: block;
}
.input-item input.data-v-0ba2294a, .input-item .picker.data-v-0ba2294a {
  height: 80rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  width: 100%;
  box-sizing: border-box;
}
.input-item .picker.data-v-0ba2294a {
  display: flex;
  align-items: center;
  color: #333333;
}
.withdraw-btn.data-v-0ba2294a {
  background: linear-gradient(135deg, #1677FF, #0E5FD8);
  color: #ffffff;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  margin-top: 40rpx;
  font-weight: 500;
  box-shadow: 0 10rpx 20rpx rgba(22, 119, 255, 0.2);
}
.withdraw-btn.disabled.data-v-0ba2294a {
  background: linear-gradient(135deg, #cccccc, #999999);
  box-shadow: none;
}
.withdraw-rules.data-v-0ba2294a {
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.rules-title.data-v-0ba2294a {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
  color: #333333;
}
.rules-content.data-v-0ba2294a {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.8;
}
.rule-item.data-v-0ba2294a {
  margin-bottom: 10rpx;
}
.withdraw-history.data-v-0ba2294a {
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.history-title.data-v-0ba2294a {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
  color: #333333;
}
.history-list.data-v-0ba2294a {
  max-height: 400rpx;
  overflow-y: auto;
}
.history-item.data-v-0ba2294a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
}
.history-item.data-v-0ba2294a:last-child {
  border-bottom: none;
}
.history-left .history-amount.data-v-0ba2294a {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8rpx;
}
.history-left .history-time.data-v-0ba2294a {
  font-size: 24rpx;
  color: #999999;
}
.history-status.data-v-0ba2294a {
  font-size: 26rpx;
}
.history-status.status-success.data-v-0ba2294a {
  color: #07c160;
}
.history-status.status-pending.data-v-0ba2294a, .history-status.status-processing.data-v-0ba2294a {
  color: #1677FF;
}
.history-status.status-failed.data-v-0ba2294a {
  color: #ff4d4f;
}
.no-record.data-v-0ba2294a {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}
.no-record image.data-v-0ba2294a {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.no-record text.data-v-0ba2294a {
  font-size: 28rpx;
  color: #999999;
}
.view-more.data-v-0ba2294a {
  text-align: center;
  font-size: 28rpx;
  color: #1677FF;
  margin-top: 20rpx;
}
.withdraw-modal.data-v-0ba2294a {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
}
.withdraw-modal .modal-mask.data-v-0ba2294a {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}
.withdraw-modal .modal-content.data-v-0ba2294a {
  width: 600rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  position: relative;
  z-index: 1000;
}
.withdraw-modal .modal-title.data-v-0ba2294a {
  font-size: 32rpx;
  font-weight: 500;
  text-align: center;
  margin-bottom: 30rpx;
}
.withdraw-modal .modal-info.data-v-0ba2294a {
  margin-bottom: 30rpx;
}
.withdraw-modal .modal-info .info-item.data-v-0ba2294a {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
  font-size: 28rpx;
}
.withdraw-modal .modal-info .info-item .label.data-v-0ba2294a {
  color: #666666;
}
.withdraw-modal .modal-info .info-item .value.data-v-0ba2294a {
  color: #333333;
  font-weight: 500;
}
.withdraw-modal .modal-info .info-item.highlight .label.data-v-0ba2294a, .withdraw-modal .modal-info .info-item.highlight .value.data-v-0ba2294a {
  color: #1677FF;
  font-weight: bold;
}
.withdraw-modal .modal-btns.data-v-0ba2294a {
  display: flex;
}
.withdraw-modal .modal-btns button.data-v-0ba2294a {
  flex: 1;
  height: 80rpx;
  font-size: 30rpx;
  border-radius: 40rpx;
  margin: 0 10rpx;
}
.withdraw-modal .modal-btns .cancel-btn.data-v-0ba2294a {
  background-color: #f5f5f5;
  color: #666666;
}
.withdraw-modal .modal-btns .confirm-btn.data-v-0ba2294a {
  background: linear-gradient(135deg, #1677FF, #0E5FD8);
  color: #ffffff;
  font-weight: 500;
}
.withdraw-modal .success-modal.data-v-0ba2294a {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 50rpx 40rpx;
}
.withdraw-modal .success-icon.data-v-0ba2294a {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}
.withdraw-modal .success-title.data-v-0ba2294a {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}
.withdraw-modal .success-desc.data-v-0ba2294a {
  font-size: 26rpx;
  color: #666666;
  text-align: center;
  margin-bottom: 30rpx;
}
.withdraw-modal .success-btn.data-v-0ba2294a {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #1677FF, #0E5FD8);
  color: #ffffff;
  font-size: 30rpx;
  border-radius: 40rpx;
  font-weight: 500;
}