<view class="activities-container"><view class="navbar"><view class="navbar-back" bindtap="{{a}}"><view class="back-icon"></view></view><text class="navbar-title">会员活动</text><view class="navbar-right"><view class="help-icon" bindtap="{{b}}">?</view></view></view><view class="overview-section"><view class="overview-header"><text class="section-title">活动概览</text><view class="date-picker" bindtap="{{d}}"><text class="date-text">{{c}}</text><view class="date-icon"></view></view></view><view class="stats-cards"><view class="stats-card"><view class="card-value">{{e}}</view><view class="card-label">总活动数</view></view><view class="stats-card"><view class="card-value">{{f}}</view><view class="card-label">进行中活动</view></view><view class="stats-card"><view class="card-value">{{g}}</view><view class="card-label">参与人次</view><view class="{{['card-trend', i]}}"><view class="trend-arrow"></view><text class="trend-value">{{h}}</text></view></view><view class="stats-card"><view class="card-value">¥{{j}}</view><view class="card-label">活动收入</view><view class="{{['card-trend', l]}}"><view class="trend-arrow"></view><text class="trend-value">{{k}}</text></view></view></view></view><view class="tabs-section"><scroll-view scroll-x class="tabs-scroll" show-scrollbar="false"><view class="tabs"><view wx:for="{{m}}" wx:for-item="tab" wx:key="b" class="{{['tab-item', tab.c && 'active']}}" bindtap="{{tab.d}}">{{tab.a}}</view></view></scroll-view></view><view class="activities-section"><view class="section-header"><text class="section-title">{{n}}</text><view class="add-btn" bindtap="{{o}}"><text class="btn-text">添加活动</text><view class="plus-icon"></view></view></view><view class="activities-list"><view wx:for="{{p}}" wx:for-item="activity" wx:key="v" class="activity-item" bindtap="{{activity.w}}"><image class="activity-image" src="{{activity.a}}" mode="aspectFill"></image><view class="{{['activity-overlay', activity.b]}}"></view><view class="{{['activity-status-badge', activity.d]}}">{{activity.c}}</view><view class="activity-content"><view class="activity-name">{{activity.e}}</view><view class="activity-period">{{activity.f}}</view><view class="activity-meta"><view class="meta-item"><svg wx:if="{{r}}" u-s="{{['d']}}" class="svg-icon" u-i="{{activity.h}}" bind:__l="__l" u-p="{{r}}"><path wx:if="{{q}}" u-i="{{activity.g}}" bind:__l="__l" u-p="{{q}}"/></svg><text>{{activity.i}}人参与</text></view><view class="meta-item"><svg wx:if="{{t}}" u-s="{{['d']}}" class="svg-icon" u-i="{{activity.k}}" bind:__l="__l" u-p="{{t}}"><path wx:if="{{s}}" u-i="{{activity.j}}" bind:__l="__l" u-p="{{s}}"/></svg><text>{{activity.l}}</text></view></view></view><view class="activity-actions"><view class="action-btn stats" catchtap="{{activity.o}}"><svg wx:if="{{w}}" u-s="{{['d']}}" class="svg-icon" u-i="{{activity.n}}" bind:__l="__l" u-p="{{w}}"><path wx:if="{{v}}" u-i="{{activity.m}}" bind:__l="__l" u-p="{{v}}"/></svg><text>统计</text></view><view class="action-btn share" catchtap="{{activity.r}}"><svg wx:if="{{y}}" u-s="{{['d']}}" class="svg-icon" u-i="{{activity.q}}" bind:__l="__l" u-p="{{y}}"><path wx:if="{{x}}" u-i="{{activity.p}}" bind:__l="__l" u-p="{{x}}"/></svg><text>分享</text></view><view class="action-btn toggle"><switch checked="{{activity.s}}" bindchange="{{activity.t}}" color="#43E97B"/></view></view></view></view></view><view class="floating-action-button" bindtap="{{z}}"><view class="fab-icon">+</view></view></view>