"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_services_ActivityService = require("../../../../common/services/ActivityService.js");
const common_assets = require("../../../../common/assets.js");
const CountdownTimer = () => "../../components/CountdownTimer.js";
const _sfc_main = {
  components: {
    CountdownTimer
  },
  data() {
    return {
      refreshing: false,
      loading: false,
      noMore: false,
      scrollTop: 0,
      currentRoundIndex: 0,
      rounds: [
        {
          time: "08:00",
          status: "已开抢",
          label: "08:00场",
          endTime: new Date((/* @__PURE__ */ new Date()).setHours(10, 0, 0, 0)).toISOString()
        },
        {
          time: "10:00",
          status: "抢购中",
          label: "10:00场",
          endTime: new Date((/* @__PURE__ */ new Date()).setHours(12, 0, 0, 0)).toISOString()
        },
        {
          time: "12:00",
          status: "即将开始",
          label: "12:00场",
          endTime: new Date((/* @__PURE__ */ new Date()).setHours(14, 0, 0, 0)).toISOString()
        },
        {
          time: "14:00",
          status: "即将开始",
          label: "14:00场",
          endTime: new Date((/* @__PURE__ */ new Date()).setHours(16, 0, 0, 0)).toISOString()
        },
        {
          time: "16:00",
          status: "即将开始",
          label: "16:00场",
          endTime: new Date((/* @__PURE__ */ new Date()).setHours(18, 0, 0, 0)).toISOString()
        },
        {
          time: "18:00",
          status: "即将开始",
          label: "18:00场",
          endTime: new Date((/* @__PURE__ */ new Date()).setHours(20, 0, 0, 0)).toISOString()
        },
        {
          time: "20:00",
          status: "即将开始",
          label: "20:00场",
          endTime: new Date((/* @__PURE__ */ new Date()).setHours(22, 0, 0, 0)).toISOString()
        }
      ],
      products: [],
      page: 1,
      pageSize: 10
    };
  },
  computed: {
    currentRound() {
      return this.rounds[this.currentRoundIndex];
    }
  },
  onLoad() {
    this.fetchData();
  },
  methods: {
    // 监听滚动事件
    handleScroll(e) {
      this.scrollTop = e.detail.scrollTop;
    },
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 切换场次
    switchRound(index) {
      this.currentRoundIndex = index;
      this.products = [];
      this.page = 1;
      this.noMore = false;
      this.fetchData();
    },
    // 倒计时结束
    onCountdownEnd() {
      common_vendor.index.showToast({
        title: "当前场次已结束",
        icon: "none"
      });
      if (this.currentRoundIndex < this.rounds.length - 1) {
        this.currentRoundIndex++;
        this.products = [];
        this.page = 1;
        this.noMore = false;
        this.fetchData();
      }
    },
    // 加载更多
    loadMore() {
      if (this.loading || this.noMore)
        return;
      this.loading = true;
      this.page++;
      this.fetchMoreData();
    },
    // 获取数据
    fetchData() {
      this.loading = true;
      try {
        const flashSaleActivities = common_services_ActivityService.ActivityService.getActivities({
          type: common_services_ActivityService.ActivityType.FLASH,
          onlyPublished: true,
          sortByEndTime: true,
          page: this.page,
          pageSize: this.pageSize
        });
        if (flashSaleActivities && flashSaleActivities.length > 0) {
          this.products = flashSaleActivities;
          this.loading = false;
        } else {
          this.products = [];
          this.noMore = true;
          this.loading = false;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/activity-showcase/pages/flash-sale/index.vue:263", "获取秒杀活动数据失败:", error);
        this.loading = false;
        common_vendor.index.showToast({
          title: "获取数据失败",
          icon: "none"
        });
      }
    },
    // 获取更多数据
    fetchMoreData() {
      try {
        const moreActivities = common_services_ActivityService.ActivityService.getActivities({
          type: common_services_ActivityService.ActivityType.FLASH,
          onlyPublished: true,
          sortByEndTime: true,
          page: this.page,
          pageSize: this.pageSize
        });
        if (moreActivities && moreActivities.length > 0) {
          this.products = [...this.products, ...moreActivities];
          this.loading = false;
        } else {
          this.noMore = true;
          this.loading = false;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/activity-showcase/pages/flash-sale/index.vue:293", "获取更多秒杀活动数据失败:", error);
        this.loading = false;
        common_vendor.index.showToast({
          title: "获取更多数据失败",
          icon: "none"
        });
      }
    },
    // 导航到详情页
    navigateToDetail(id) {
      common_vendor.index.navigateTo({
        url: `/subPackages/activity-showcase/pages/flash-sale/detail?id=${id}`
      });
    }
  }
};
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_countdown_timer = common_vendor.resolveComponent("countdown-timer");
  (_component_path + _component_svg + _component_countdown_timer)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$7,
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: common_vendor.p({
      d: "M512 421.490332L331.349941 240.840273c-24.988383-24.988383-65.35828-24.988383-90.346664 0-24.988383 24.988383-24.988383 65.35828 0 90.346664L421.653336 512 240.840273 692.812059c-24.988383 24.988383-24.988383 65.35828 0 90.346664 24.988383 24.988383 65.35828 24.988383 90.346664 0L512 602.509668l180.650059 180.650059c24.988383 24.988383 65.35828 24.988383 90.346664 0 24.988383-24.988383 24.988383-65.35828 0-90.346664L602.346664 512l180.813063-180.812059c24.988383-24.988383 24.988383-65.35828 0-90.346664-24.988383-24.988383-65.35828-24.988383-90.346664 0L512 421.490332z",
      fill: "#FFFFFF"
    }),
    d: common_vendor.p({
      viewBox: "0 0 1024 1024",
      xmlns: "http://www.w3.org/2000/svg",
      width: "22",
      height: "22"
    }),
    e: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    f: common_vendor.t($options.currentRound.label),
    g: common_vendor.t($options.currentRound.status),
    h: common_vendor.o($options.onCountdownEnd),
    i: common_vendor.p({
      ["end-time"]: $options.currentRound.endTime,
      type: "flash"
    }),
    j: common_vendor.f($data.rounds, (round, index, i0) => {
      return {
        a: common_vendor.t(round.time),
        b: common_vendor.t(round.status),
        c: index,
        d: $data.currentRoundIndex === index ? 1 : "",
        e: common_vendor.o(($event) => $options.switchRound(index), index)
      };
    }),
    k: common_vendor.f($data.products, (product, index, i0) => {
      return common_vendor.e({
        a: product.coverImage || product.image,
        b: product.tag
      }, product.tag ? {
        c: common_vendor.t(product.tag)
      } : {}, {
        d: common_vendor.t(product.name || product.title),
        e: common_vendor.t(product.description),
        f: common_vendor.t(product.flashPrice || product.currentPrice),
        g: common_vendor.t(product.originalPrice),
        h: common_vendor.t(product.soldCount),
        i: common_vendor.t(product.stockTotal || product.totalCount),
        j: `${Math.min(100, product.soldCount / (product.stockTotal || product.totalCount) * 100)}%`,
        k: index,
        l: common_vendor.o(($event) => $options.navigateToDetail(product.id), index)
      });
    }),
    l: $data.loading
  }, $data.loading ? {} : {}, {
    m: $data.noMore
  }, $data.noMore ? {} : {}, {
    n: $data.products.length === 0 && !$data.loading
  }, $data.products.length === 0 && !$data.loading ? {
    o: common_assets._imports_1$40
  } : {}, {
    p: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args)),
    q: common_vendor.o((...args) => $options.handleScroll && $options.handleScroll(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-cd88ae3b"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/flash-sale/index.js.map
