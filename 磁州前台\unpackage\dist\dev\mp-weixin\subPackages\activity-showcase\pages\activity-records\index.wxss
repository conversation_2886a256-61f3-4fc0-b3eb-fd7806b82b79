
.activity-records-container.data-v-2492d9f5 {
  min-height: 100vh;
  background-color: #F5F5F5;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.custom-navbar.data-v-2492d9f5 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
}
.navbar-bg.data-v-2492d9f5 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
}
.navbar-content.data-v-2492d9f5 {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 107rpx; /* 原来是102rpx，增加5rpx */
  padding: var(--status-bar-height) 30rpx 0;
}
.back-btn.data-v-2492d9f5, .filter-btn.data-v-2492d9f5 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon.data-v-2492d9f5 {
  width: 36rpx;
  height: 36rpx;
}
.navbar-title.data-v-2492d9f5 {
  font-size: 36rpx;
  font-weight: bold;
  color: #FFFFFF;
}
.navbar-right.data-v-2492d9f5 {
  display: flex;
  align-items: center;
}

/* 标签栏样式 */
.activity-tabs.data-v-2492d9f5 {
  display: flex;
  background: #FFFFFF;
  padding: 0 20rpx;
  margin-top: calc(var(--status-bar-height) + 107rpx); /* 原来是102rpx，增加5rpx */
  border-bottom: 1rpx solid #EEEEEE;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}
.tab-item.data-v-2492d9f5 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  position: relative;
}
.tab-text.data-v-2492d9f5 {
  font-size: 28rpx;
  color: #333333;
  padding: 0 10rpx;
}
.tab-item.active .tab-text.data-v-2492d9f5 {
  color: #FF3B69;
  font-weight: 500;
}
.tab-indicator.data-v-2492d9f5 {
  position: absolute;
  bottom: 0;
  width: 40rpx;
  height: 6rpx;
  border-radius: 3rpx;
}

/* 活动记录列表样式 */
.records-swiper.data-v-2492d9f5 {
  height: calc(100vh - var(--status-bar-height) - 107rpx - 70rpx); /* 原来是102rpx，增加5rpx */
}
.tab-content.data-v-2492d9f5 {
  height: 100%;
  padding: 20rpx;
}
.records-list.data-v-2492d9f5 {
  padding-bottom: 30rpx;
}
.record-card.data-v-2492d9f5 {
  background: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
  position: relative;
}
.record-image.data-v-2492d9f5 {
  width: 100%;
  height: 300rpx;
  object-fit: cover;
}
.status-tag.data-v-2492d9f5 {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  font-weight: 500;
}
.record-info.data-v-2492d9f5 {
  padding: 20rpx 30rpx;
}
.record-title.data-v-2492d9f5 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.record-meta.data-v-2492d9f5 {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}
.meta-item.data-v-2492d9f5 {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  margin-bottom: 10rpx;
}
.meta-icon.data-v-2492d9f5 {
  margin-right: 6rpx;
}
.meta-text.data-v-2492d9f5 {
  font-size: 24rpx;
  color: #999999;
}
.record-bottom.data-v-2492d9f5 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10rpx;
}
.participants.data-v-2492d9f5 {
  display: flex;
  align-items: center;
}
.avatar-group.data-v-2492d9f5 {
  display: flex;
  margin-right: 10rpx;
}
.participant-avatar.data-v-2492d9f5 {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  border: 2rpx solid #FFFFFF;
  margin-left: -10rpx;
}
.participant-avatar.data-v-2492d9f5:first-child {
  margin-left: 0;
}
.avatar-more.data-v-2492d9f5 {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background: #F0F0F0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #666666;
  margin-left: -10rpx;
  border: 2rpx solid #FFFFFF;
}
.participant-count.data-v-2492d9f5 {
  font-size: 24rpx;
  color: #666666;
}
.record-actions.data-v-2492d9f5 {
  display: flex;
  align-items: center;
}
.action-btn.data-v-2492d9f5 {
	  display: flex;
	  align-items: center;
	  justify-content: center;
	  padding: 10rpx 20rpx;
	  border-radius: 30rpx;
	  font-size: 24rpx;
	  margin-left: 15rpx;
}
.action-btn.share.data-v-2492d9f5 {
	  border: 1rpx solid #DDDDDD;
	  color: #666666;
}
.action-icon.data-v-2492d9f5 {
	  margin-right: 6rpx;
}
.action-btn.primary.data-v-2492d9f5 {
	  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
}
	
	/* 空状态样式 */
.empty-state.data-v-2492d9f5 {
	  display: flex;
	  flex-direction: column;
	  align-items: center;
	  justify-content: center;
	  padding: 100rpx 0;
}
.empty-image.data-v-2492d9f5 {
	  width: 200rpx;
	  height: 200rpx;
	  margin-bottom: 30rpx;
}
.empty-text.data-v-2492d9f5 {
	  font-size: 28rpx;
	  color: #999999;
	  margin-bottom: 30rpx;
}
.empty-state .action-btn.data-v-2492d9f5 {
	  padding: 15rpx 60rpx;
	  font-size: 28rpx;
	  color: #FFFFFF;
}
	
	/* 筛选弹窗样式 */
.filter-popup.data-v-2492d9f5 {
	  background: #FFFFFF;
	  border-top-left-radius: 30rpx;
	  border-top-right-radius: 30rpx;
	  padding: 30rpx;
	  max-height: 70vh;
}
.filter-header.data-v-2492d9f5 {
	  display: flex;
	  justify-content: space-between;
	  align-items: center;
	  margin-bottom: 30rpx;
}
.filter-title.data-v-2492d9f5 {
	  font-size: 32rpx;
	  font-weight: bold;
	  color: #333333;
}
.filter-close.data-v-2492d9f5 {
	  width: 60rpx;
	  height: 60rpx;
	  display: flex;
	  align-items: center;
	  justify-content: center;
}
.filter-content.data-v-2492d9f5 {
	  max-height: calc(70vh - 180rpx);
	  overflow-y: auto;
}
.filter-section.data-v-2492d9f5 {
	  margin-bottom: 30rpx;
}
.section-title.data-v-2492d9f5 {
	  font-size: 28rpx;
	  color: #333333;
	  font-weight: 500;
	  margin-bottom: 20rpx;
}
.filter-options.data-v-2492d9f5 {
	  display: flex;
	  flex-wrap: wrap;
}
.filter-option.data-v-2492d9f5 {
	  padding: 10rpx 30rpx;
	  border-radius: 30rpx;
	  font-size: 26rpx;
	  color: #666666;
	  background: #F5F5F5;
	  margin-right: 20rpx;
	  margin-bottom: 20rpx;
}
.filter-option.active.data-v-2492d9f5 {
	  background: rgba(255, 59, 105, 0.1);
	  color: #FF3B69;
	  border: 1rpx solid rgba(255, 59, 105, 0.3);
}
.filter-footer.data-v-2492d9f5 {
	  display: flex;
	  justify-content: space-between;
	  margin-top: 30rpx;
	  padding-top: 20rpx;
	  border-top: 1rpx solid #F0F0F0;
}
.filter-reset.data-v-2492d9f5, .filter-apply.data-v-2492d9f5 {
	  flex: 1;
	  height: 80rpx;
	  display: flex;
	  align-items: center;
	  justify-content: center;
	  border-radius: 40rpx;
	  font-size: 28rpx;
}
.filter-reset.data-v-2492d9f5 {
	  background: #F5F5F5;
	  color: #666666;
	  margin-right: 20rpx;
}
.filter-apply.data-v-2492d9f5 {
	  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
	  color: #FFFFFF;
	  box-shadow: 0 4rpx 8rpx rgba(255, 59, 105, 0.2);
}
	