/**
 * 增强版微信小程序激励广告服务
 * 支持商家入驻、发布、置顶、续费、刷新等完整功能
 * 基于微信小程序激励广告API：https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/ad/rewarded-video-ad.html
 */

import request from '@/utils/request';

class EnhancedRewardedAdService {
  constructor() {
    this.videoAd = null;
    this.currentRewardType = null;
    this.currentParams = {};
    this.adConfig = {
      adUnitId: 'adunit-9699c56c26082b54', // 默认广告单元ID
      testMode: false,
      minWatchPercentage: 80,
      cooldownTime: 300 // 5分钟冷却时间
    };
    this.lastAdTime = 0;
  }

  /**
   * 初始化激励视频广告
   */
  initRewardedVideoAd() {
    // 检查微信小程序环境
    if (typeof wx === 'undefined' || !wx.createRewardedVideoAd) {
      console.warn('当前环境不支持激励视频广告');
      return false;
    }

    try {
      // 销毁旧的广告实例
      if (this.videoAd) {
        this.videoAd.destroy();
      }

      this.videoAd = wx.createRewardedVideoAd({
        adUnitId: this.adConfig.adUnitId
      });

      // 监听广告加载成功
      this.videoAd.onLoad(() => {
        console.log('激励视频广告加载成功');
      });

      // 监听广告加载失败
      this.videoAd.onError((err) => {
        console.error('激励视频广告加载失败', err);
        this.handleAdError(err);
      });

      // 监听广告关闭
      this.videoAd.onClose((res) => {
        this.handleAdClose(res);
      });

      return true;
    } catch (error) {
      console.error('初始化激励视频广告失败', error);
      return false;
    }
  }

  /**
   * 处理广告关闭事件
   */
  handleAdClose(res) {
    if (res && res.isEnded || res === undefined) {
      // 广告播放完成，发放奖励
      console.log('激励视频广告完成，发放奖励');
      this.grantReward();
    } else {
      // 播放中途退出，不发放奖励
      console.log('激励视频广告未完成');
      uni.showToast({
        title: '请观看完整广告才能获得奖励',
        icon: 'none',
        duration: 2000
      });
      
      // 触发广告取消事件
      uni.$emit('adCancelled', {
        type: this.currentRewardType,
        params: this.currentParams
      });
    }
  }

  /**
   * 处理广告错误
   */
  handleAdError(err) {
    let message = '广告加载失败';
    
    switch (err.errCode) {
      case 1000:
        message = '后端接口调用失败';
        break;
      case 1001:
        message = '参数错误';
        break;
      case 1002:
        message = '广告单元无效';
        break;
      case 1003:
        message = '内部错误';
        break;
      case 1004:
        message = '无合适的广告';
        break;
      case 1005:
        message = '广告组件审核中';
        break;
      case 1006:
        message = '广告组件被驳回';
        break;
      case 1007:
        message = '广告组件被封禁';
        break;
      case 1008:
        message = '广告单元已关闭';
        break;
      default:
        message = `广告错误: ${err.errMsg}`;
    }
    
    uni.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  }

  /**
   * 检查冷却时间
   */
  checkCooldown() {
    const now = Date.now();
    const timeDiff = now - this.lastAdTime;
    
    if (timeDiff < this.adConfig.cooldownTime * 1000) {
      const remainingTime = Math.ceil((this.adConfig.cooldownTime * 1000 - timeDiff) / 1000);
      uni.showToast({
        title: `请等待${remainingTime}秒后再试`,
        icon: 'none'
      });
      return false;
    }
    
    return true;
  }

  /**
   * 显示激励视频广告
   * @param {string} rewardType - 奖励类型
   * @param {Object} params - 参数对象
   */
  async showRewardedVideoAd(rewardType, params = {}) {
    // 检查冷却时间
    if (!this.checkCooldown()) {
      return false;
    }

    this.currentRewardType = rewardType;
    this.currentParams = params;

    // 检查是否已初始化
    if (!this.videoAd) {
      const initSuccess = this.initRewardedVideoAd();
      if (!initSuccess) {
        uni.showToast({
          title: '广告功能不可用',
          icon: 'none'
        });
        return false;
      }
    }

    try {
      // 显示广告
      await this.videoAd.show();
      this.lastAdTime = Date.now();
      return true;
    } catch (error) {
      console.error('显示激励视频广告失败', error);
      
      // 失败重试
      try {
        await this.videoAd.load();
        await this.videoAd.show();
        this.lastAdTime = Date.now();
        return true;
      } catch (retryError) {
        console.error('重试显示激励视频广告失败', retryError);
        this.handleAdError(retryError);
        return false;
      }
    }
  }

  /**
   * 商家入驻看广告
   */
  async watchAdForJoin(params = {}) {
    const result = await uni.showModal({
      title: '看广告免费入驻',
      content: '观看完整广告可获得1个月免费入驻特权，确定要观看吗？',
      confirmText: '观看广告',
      cancelText: '取消'
    });

    if (!result.confirm) {
      return false;
    }

    return await this.showRewardedVideoAd('merchant_join', params);
  }

  /**
   * 商家发布看广告
   */
  async watchAdForPublish(params = {}) {
    const result = await uni.showModal({
      title: '看广告免费发布',
      content: '观看完整广告可免费发布商家信息，确定要观看吗？',
      confirmText: '观看广告',
      cancelText: '取消'
    });

    if (!result.confirm) {
      return false;
    }

    return await this.showRewardedVideoAd('merchant_publish', params);
  }

  /**
   * 商家置顶看广告
   */
  async watchAdForTop(params = {}) {
    const result = await uni.showModal({
      title: '看广告置顶',
      content: '观看完整广告可免费置顶信息2小时，确定要观看吗？',
      confirmText: '观看广告',
      cancelText: '取消'
    });

    if (!result.confirm) {
      return false;
    }

    return await this.showRewardedVideoAd('merchant_top', params);
  }

  /**
   * 商家续费看广告
   */
  async watchAdForRenew(params = {}) {
    const result = await uni.showModal({
      title: '看广告续费',
      content: '观看完整广告可延长7天入驻期限，确定要观看吗？',
      confirmText: '观看广告',
      cancelText: '取消'
    });

    if (!result.confirm) {
      return false;
    }

    return await this.showRewardedVideoAd('merchant_renew', params);
  }

  /**
   * 商家刷新看广告
   */
  async watchAdForRefresh(params = {}) {
    const result = await uni.showModal({
      title: '看广告刷新',
      content: '观看完整广告可刷新信息到最新，确定要观看吗？',
      confirmText: '观看广告',
      cancelText: '取消'
    });

    if (!result.confirm) {
      return false;
    }

    return await this.showRewardedVideoAd('merchant_refresh', params);
  }

  /**
   * 发放奖励
   */
  async grantReward() {
    try {
      // 获取用户信息
      const userInfo = uni.getStorageSync('userInfo');
      if (!userInfo || !userInfo.id) {
        uni.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }

      // 构建请求数据
      const requestData = {
        reward_type: this.currentRewardType,
        user_id: userInfo.id,
        ad_unit_id: this.adConfig.adUnitId,
        timestamp: Date.now(),
        ...this.currentParams
      };

      // 根据奖励类型调用不同的API
      const apiUrl = this.getApiUrl(this.currentRewardType);
      
      // 调用后台API发放奖励
      const response = await request.post(apiUrl, requestData);

      if (response.data && response.data.success) {
        // 奖励发放成功
        const rewardData = response.data.data;
        this.showRewardSuccess(rewardData);
        
        // 触发奖励成功事件
        uni.$emit('rewardGranted', {
          type: this.currentRewardType,
          data: rewardData,
          params: this.currentParams
        });
      } else {
        // 奖励发放失败
        uni.showToast({
          title: response.data?.message || '奖励发放失败',
          icon: 'none'
        });
      }

    } catch (error) {
      console.error('发放奖励失败', error);
      uni.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    }
  }

  /**
   * 获取API地址
   */
  getApiUrl(rewardType) {
    const apiMap = {
      'merchant_join': '/api/merchant/ads/join',
      'merchant_publish': '/api/merchant/ads/publish',
      'merchant_top': '/api/merchant/ads/top',
      'merchant_renew': '/api/merchant/ads/renew',
      'merchant_refresh': '/api/merchant/ads/refresh'
    };
    
    return apiMap[rewardType] || '/api/merchant/ads/reward';
  }

  /**
   * 显示奖励成功提示
   */
  showRewardSuccess(rewardData) {
    const messageMap = {
      'merchant_join': '商家入驻成功！获得1个月免费特权',
      'merchant_publish': '信息发布成功！',
      'merchant_top': '信息置顶成功！置顶2小时',
      'merchant_renew': `续费成功！延长${rewardData.days || 7}天`,
      'merchant_refresh': '信息刷新成功！'
    };
    
    const message = messageMap[rewardData.type] || '奖励发放成功';
    
    uni.showToast({
      title: message,
      icon: 'success',
      duration: 2000
    });
  }

  /**
   * 更新广告配置
   */
  updateConfig(config) {
    this.adConfig = { ...this.adConfig, ...config };
    
    // 如果广告单元ID改变，需要重新初始化
    if (config.adUnitId && config.adUnitId !== this.adConfig.adUnitId) {
      this.initRewardedVideoAd();
    }
  }

  /**
   * 销毁广告实例
   */
  destroy() {
    if (this.videoAd) {
      this.videoAd.destroy();
      this.videoAd = null;
    }
  }
}

// 创建单例实例
const enhancedRewardedAdService = new EnhancedRewardedAdService();

export default enhancedRewardedAdService;
