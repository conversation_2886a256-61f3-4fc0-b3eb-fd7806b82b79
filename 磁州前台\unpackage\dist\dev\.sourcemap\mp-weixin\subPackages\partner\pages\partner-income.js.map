{"version": 3, "file": "partner-income.js", "sources": ["subPackages/partner/pages/partner-income.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNccGFydG5lclxwYWdlc1xwYXJ0bmVyLWluY29tZS52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"income-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\">\r\n      <view class=\"navbar-left\" @click=\"goBack\">\r\n        <image src=\"/static/images/tabbar/最新返回键.png\" class=\"back-icon\"></image>\r\n      </view>\r\n      <view class=\"navbar-title\">收益明细</view>\r\n      <view class=\"navbar-right\">\r\n        <!-- 预留位置与发布页面保持一致 -->\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 添加顶部安全区域 -->\r\n    <view class=\"safe-area-top\"></view>\r\n\r\n    <!-- 收益总览 -->\r\n    <view class=\"income-overview\">\r\n      <view class=\"overview-header\">\r\n        <text class=\"overview-title\">总收益(元)</text>\r\n        <picker mode=\"date\" fields=\"month\" :value=\"currentMonth\" @change=\"monthChange\">\r\n          <view class=\"month-picker\">\r\n            {{formattedMonth}}\r\n            <text class=\"cuIcon-calendar\"></text>\r\n          </view>\r\n        </picker>\r\n      </view>\r\n      <view class=\"overview-amount\">{{totalIncome}}</view>\r\n      <view class=\"overview-stats\">\r\n        <view class=\"stats-item\">\r\n          <text class=\"stats-value\">{{firstLevelIncome}}</text>\r\n          <text class=\"stats-label\">一级收益</text>\r\n        </view>\r\n        <view class=\"stats-divider\"></view>\r\n        <view class=\"stats-item\">\r\n          <text class=\"stats-value\">{{secondLevelIncome}}</text>\r\n          <text class=\"stats-label\">二级收益</text>\r\n        </view>\r\n        <view class=\"stats-divider\"></view>\r\n        <view class=\"stats-item\">\r\n          <text class=\"stats-value\">{{incomeOrders}}</text>\r\n          <text class=\"stats-label\">订单数</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 收益图表 -->\r\n    <view class=\"income-chart\">\r\n      <view class=\"chart-header\">\r\n        <text class=\"chart-title\">收益趋势</text>\r\n        <view class=\"chart-legend\">\r\n          <view class=\"legend-item\">\r\n            <view class=\"legend-color level-one\"></view>\r\n            <text>一级收益</text>\r\n          </view>\r\n          <view class=\"legend-item\">\r\n            <view class=\"legend-color level-two\"></view>\r\n            <text>二级收益</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      <view class=\"chart-content\">\r\n        <!-- 简化版图表，实际项目中可以使用图表组件 -->\r\n        <view class=\"chart-bars\">\r\n          <view class=\"chart-bar\" v-for=\"(item, index) in chartData\" :key=\"index\">\r\n            <view class=\"bar-item level-two\" :style=\"{height: item.level2 + 'rpx'}\"></view>\r\n            <view class=\"bar-item level-one\" :style=\"{height: item.level1 + 'rpx'}\"></view>\r\n            <text class=\"bar-date\">{{item.date}}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 筛选区域 -->\r\n    <view class=\"filter-section\">\r\n      <view class=\"filter-tabs\">\r\n        <view \r\n          class=\"tab-item\" \r\n          :class=\"{'active': currentTab === 'all'}\"\r\n          @tap=\"switchTab('all')\"\r\n        >全部</view>\r\n        <view \r\n          class=\"tab-item\" \r\n          :class=\"{'active': currentTab === 'level1'}\"\r\n          @tap=\"switchTab('level1')\"\r\n        >一级收益</view>\r\n        <view \r\n          class=\"tab-item\" \r\n          :class=\"{'active': currentTab === 'level2'}\"\r\n          @tap=\"switchTab('level2')\"\r\n        >二级收益</view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 收益明细列表 -->\r\n    <view class=\"income-list\" v-if=\"filteredIncome.length > 0\">\r\n      <view class=\"income-item\" v-for=\"(item, index) in filteredIncome\" :key=\"index\">\r\n        <view class=\"item-left\">\r\n          <text class=\"item-title\">{{item.title}}</text>\r\n          <text class=\"item-desc\">{{item.desc}}</text>\r\n          <text class=\"item-time\">{{item.time}}</text>\r\n        </view>\r\n        <view class=\"item-right\">\r\n          <text class=\"item-amount\" :class=\"{'level-one-text': item.level === 1, 'level-two-text': item.level === 2}\">+{{item.amount}}</text>\r\n          <text class=\"item-level\">{{item.level === 1 ? '一级收益' : '二级收益'}}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 空状态 -->\r\n    <view class=\"empty-state\" v-else>\r\n      <image src=\"/static/images/no-income.png\" mode=\"aspectFit\"></image>\r\n      <text>暂无收益记录</text>\r\n      <button class=\"share-btn\" @tap=\"navigateToPoster\">去推广赚佣金</button>\r\n    </view>\r\n\r\n    <!-- 加载更多 -->\r\n    <view class=\"load-more\" v-if=\"filteredIncome.length > 0 && hasMore\">\r\n      <text @tap=\"loadMore\" v-if=\"!isLoading\">加载更多</text>\r\n      <text v-else>加载中...</text>\r\n    </view>\r\n\r\n    <view class=\"load-end\" v-if=\"filteredIncome.length > 0 && !hasMore\">\r\n      <text>- 已经到底了 -</text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, computed, onMounted } from 'vue';\r\n\r\n// 响应式数据\r\nconst currentMonth = ref('2023-11'); // 当前选择的月份\r\nconst totalIncome = ref('3628.50'); // 总收益\r\nconst firstLevelIncome = ref('2745.20'); // 一级收益\r\nconst secondLevelIncome = ref('883.30'); // 二级收益\r\nconst incomeOrders = ref(56); // 订单数\r\nconst currentTab = ref('all'); // 当前选中的标签\r\nconst incomeList = ref([]); // 收益列表\r\nconst page = ref(1); // 当前页码\r\nconst pageSize = ref(10); // 每页条数\r\nconst hasMore = ref(true); // 是否有更多数据\r\nconst isLoading = ref(false); // 是否正在加载\r\nconst chartData = ref([\r\n  { date: '11-01', level1: 80, level2: 20 },\r\n  { date: '11-05', level1: 120, level2: 50 },\r\n  { date: '11-10', level1: 60, level2: 30 },\r\n  { date: '11-15', level1: 100, level2: 40 },\r\n  { date: '11-20', level1: 150, level2: 60 },\r\n  { date: '11-25', level1: 90, level2: 35 },\r\n  { date: '11-30', level1: 110, level2: 45 }\r\n]);\r\n\r\n// 计算属性\r\n// 格式化月份显示\r\nconst formattedMonth = computed(() => {\r\n  const [year, month] = currentMonth.value.split('-');\r\n  return `${year}年${parseInt(month)}月`;\r\n});\r\n\r\n// 过滤后的收益列表\r\nconst filteredIncome = computed(() => {\r\n  if (currentTab.value === 'all') {\r\n    return incomeList.value;\r\n  } else if (currentTab.value === 'level1') {\r\n    return incomeList.value.filter(item => item.level === 1);\r\n  } else {\r\n    return incomeList.value.filter(item => item.level === 2);\r\n  }\r\n});\r\n\r\n// 方法\r\n// 月份变化\r\nconst monthChange = (e) => {\r\n  currentMonth.value = e.detail.value;\r\n  page.value = 1;\r\n  hasMore.value = true;\r\n  incomeList.value = [];\r\n  loadData();\r\n  \r\n  // 更新图表数据\r\n  updateChartData();\r\n};\r\n\r\n// 切换标签\r\nconst switchTab = (tab) => {\r\n  currentTab.value = tab;\r\n};\r\n\r\n// 加载数据\r\nconst loadData = () => {\r\n  isLoading.value = true;\r\n  \r\n  // 模拟API请求\r\n  setTimeout(() => {\r\n    // 模拟数据\r\n    const mockData = generateMockIncome();\r\n    \r\n    if (page.value === 1) {\r\n      incomeList.value = mockData;\r\n    } else {\r\n      incomeList.value = [...incomeList.value, ...mockData];\r\n    }\r\n    \r\n    // 判断是否还有更多数据\r\n    hasMore.value = page.value < 3; // 模拟只有3页数据\r\n    \r\n    isLoading.value = false;\r\n  }, 500);\r\n};\r\n\r\n// 加载更多\r\nconst loadMore = () => {\r\n  if (isLoading.value || !hasMore.value) return;\r\n  \r\n  page.value++;\r\n  loadData();\r\n};\r\n\r\n// 更新图表数据\r\nconst updateChartData = () => {\r\n  // 在实际项目中，这里会根据选择的月份从服务器获取数据\r\n  // 这里只是简单模拟\r\n  const [year, month] = currentMonth.value.split('-');\r\n  const daysInMonth = new Date(parseInt(year), parseInt(month), 0).getDate();\r\n  \r\n  chartData.value = [];\r\n  \r\n  // 生成7个数据点\r\n  const step = Math.floor(daysInMonth / 6);\r\n  for (let i = 0; i < 7; i++) {\r\n    const day = i * step + 1;\r\n    chartData.value.push({\r\n      date: `${month}-${day < 10 ? '0' + day : day}`,\r\n      level1: Math.floor(Math.random() * 100) + 50,\r\n      level2: Math.floor(Math.random() * 50) + 10\r\n    });\r\n  }\r\n};\r\n\r\n// 跳转到海报页面\r\nconst navigateToPoster = () => {\r\n  uni.navigateTo({\r\n    url: '/pages/my/partner-poster'\r\n  });\r\n};\r\n\r\n// 生成模拟收益数据\r\nconst generateMockIncome = () => {\r\n  const mockIncome = [];\r\n  const titles = ['商品订单佣金', '会员充值佣金', '服务预约佣金', '活动报名佣金'];\r\n  const names = ['张先生', '李女士', '王先生', '赵女士', '刘先生', '陈女士', '杨先生', '周女士'];\r\n  \r\n  // 随机生成10条记录\r\n  for (let i = 0; i < 10; i++) {\r\n    const level = Math.random() > 0.7 ? 2 : 1; // 70%是一级收益，30%是二级收益\r\n    const title = titles[Math.floor(Math.random() * titles.length)];\r\n    const name = names[Math.floor(Math.random() * names.length)];\r\n    \r\n    // 随机金额，一级收益通常比二级高\r\n    const amount = level === 1 \r\n      ? (Math.floor(Math.random() * 100) + 30).toFixed(2)\r\n      : (Math.floor(Math.random() * 50) + 10).toFixed(2);\r\n    \r\n    // 随机时间，本月内\r\n    const [year, month] = currentMonth.value.split('-');\r\n    const day = Math.floor(Math.random() * 28) + 1;\r\n    const hour = Math.floor(Math.random() * 24);\r\n    const minute = Math.floor(Math.random() * 60);\r\n    const time = `${year}-${month}-${day < 10 ? '0' + day : day} ${hour < 10 ? '0' + hour : hour}:${minute < 10 ? '0' + minute : minute}`;\r\n    \r\n    mockIncome.push({\r\n      id: 'income_' + Date.now() + i,\r\n      title,\r\n      desc: `来自${name}的订单`,\r\n      time,\r\n      amount,\r\n      level\r\n    });\r\n  }\r\n  \r\n  // 按时间倒序排序\r\n  mockIncome.sort((a, b) => new Date(b.time) - new Date(a.time));\r\n  \r\n  return mockIncome;\r\n};\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 生命周期钩子 - 页面加载\r\nonMounted(() => {\r\n  // 初始化加载数据\r\n  loadData();\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.income-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: 30rpx;\r\n  padding-top: calc(44px + var(--status-bar-height));\r\n}\r\n\r\n.income-overview {\r\n  margin: 30rpx;\r\n  background: linear-gradient(135deg, #1677FF, #0E5FD8);\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n  color: #ffffff;\r\n  box-shadow: 0 10rpx 20rpx rgba(22, 119, 255, 0.2);\r\n}\r\n\r\n.overview-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.overview-title {\r\n  font-size: 28rpx;\r\n  opacity: 0.9;\r\n}\r\n\r\n.month-picker {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 24rpx;\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  padding: 8rpx 16rpx;\r\n  border-radius: 20rpx;\r\n  \r\n  .cuIcon-calendar {\r\n    margin-left: 10rpx;\r\n  }\r\n}\r\n\r\n.overview-amount {\r\n  font-size: 60rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.overview-stats {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  border-top: 1px solid rgba(255, 255, 255, 0.2);\r\n  padding-top: 20rpx;\r\n}\r\n\r\n.stats-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  flex: 1;\r\n}\r\n\r\n.stats-value {\r\n  font-size: 32rpx;\r\n  font-weight: 500;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.stats-label {\r\n  font-size: 24rpx;\r\n  opacity: 0.8;\r\n}\r\n\r\n.stats-divider {\r\n  width: 1px;\r\n  height: 60rpx;\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.income-chart {\r\n  margin: 30rpx;\r\n  background-color: #ffffff;\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.chart-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.chart-title {\r\n  font-size: 30rpx;\r\n  font-weight: 500;\r\n  color: #333333;\r\n}\r\n\r\n.chart-legend {\r\n  display: flex;\r\n}\r\n\r\n.legend-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-left: 20rpx;\r\n  font-size: 24rpx;\r\n  color: #666666;\r\n}\r\n\r\n.legend-color {\r\n  width: 20rpx;\r\n  height: 10rpx;\r\n  margin-right: 8rpx;\r\n  \r\n  &.level-one {\r\n    background-color: #1677FF;\r\n  }\r\n  \r\n  &.level-two {\r\n    background-color: #36CBCB;\r\n  }\r\n}\r\n\r\n.chart-content {\r\n  height: 400rpx;\r\n  display: flex;\r\n  align-items: flex-end;\r\n}\r\n\r\n.chart-bars {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-end;\r\n  width: 100%;\r\n  height: 300rpx;\r\n}\r\n\r\n.chart-bar {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  width: 40rpx;\r\n}\r\n\r\n.bar-item {\r\n  width: 40rpx;\r\n  border-radius: 4rpx 4rpx 0 0;\r\n  \r\n  &.level-one {\r\n    background-color: #1677FF;\r\n  }\r\n  \r\n  &.level-two {\r\n    background-color: #36CBCB;\r\n  }\r\n}\r\n\r\n.bar-date {\r\n  margin-top: 10rpx;\r\n  font-size: 22rpx;\r\n  color: #999999;\r\n}\r\n\r\n.filter-section {\r\n  margin: 30rpx;\r\n  background-color: #ffffff;\r\n  border-radius: 16rpx;\r\n  padding: 0 20rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.filter-tabs {\r\n  display: flex;\r\n}\r\n\r\n.tab-item {\r\n  padding: 20rpx 30rpx;\r\n  font-size: 28rpx;\r\n  color: #666666;\r\n  position: relative;\r\n  \r\n  &.active {\r\n    color: #1677FF;\r\n    font-weight: 500;\r\n    \r\n    &::after {\r\n      content: '';\r\n      position: absolute;\r\n      left: 50%;\r\n      bottom: 0;\r\n      transform: translateX(-50%);\r\n      width: 40rpx;\r\n      height: 4rpx;\r\n      background-color: #1677FF;\r\n      border-radius: 2rpx;\r\n    }\r\n  }\r\n}\r\n\r\n.income-list {\r\n  padding: 0 30rpx;\r\n}\r\n\r\n.income-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  padding: 30rpx;\r\n  background-color: #ffffff;\r\n  border-radius: 16rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.item-left {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.item-title {\r\n  font-size: 30rpx;\r\n  font-weight: 500;\r\n  color: #333333;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.item-desc {\r\n  font-size: 26rpx;\r\n  color: #666666;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.item-time {\r\n  font-size: 24rpx;\r\n  color: #999999;\r\n}\r\n\r\n.item-right {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-end;\r\n  justify-content: center;\r\n}\r\n\r\n.item-amount {\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 10rpx;\r\n  \r\n  &.level-one-text {\r\n    color: #1677FF;\r\n  }\r\n  \r\n  &.level-two-text {\r\n    color: #36CBCB;\r\n  }\r\n}\r\n\r\n.item-level {\r\n  font-size: 24rpx;\r\n  color: #999999;\r\n  padding: 4rpx 12rpx;\r\n  background-color: #f5f5f5;\r\n  border-radius: 20rpx;\r\n}\r\n\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 100rpx 0;\r\n  \r\n  image {\r\n    width: 240rpx;\r\n    height: 240rpx;\r\n    margin-bottom: 30rpx;\r\n  }\r\n  \r\n  text {\r\n    font-size: 28rpx;\r\n    color: #999999;\r\n    margin-bottom: 40rpx;\r\n  }\r\n  \r\n  .share-btn {\r\n    width: 300rpx;\r\n    height: 80rpx;\r\n    line-height: 80rpx;\r\n    background: linear-gradient(135deg, #1677FF, #0E5FD8);\r\n    color: #ffffff;\r\n    font-size: 28rpx;\r\n    border-radius: 40rpx;\r\n  }\r\n}\r\n\r\n.load-more, .load-end {\r\n  text-align: center;\r\n  font-size: 26rpx;\r\n  color: #999999;\r\n  padding: 30rpx 0;\r\n}\r\n\r\n/* 自定义导航栏样式 */\r\n.custom-navbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  height: 88rpx;\r\n  padding: 0 30rpx;\r\n  padding-top: 44px; /* 状态栏高度 */\r\n  position: fixed; /* 改为固定定位 */\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background-image: linear-gradient(135deg, #0066FF, #0052CC); /* 改为与发布页一致的渐变角度 */\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.15);\r\n  z-index: 100; /* 提高z-index确保在最上层 */\r\n}\r\n\r\n.navbar-title {\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  color: #ffffff;\r\n  font-size: 36rpx;\r\n  font-weight: 700;\r\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\r\n  text-align: center;\r\n}\r\n\r\n.navbar-right {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n}\r\n\r\n.navbar-left {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n  z-index: 20; /* 确保在标题上层，可以被点击 */\r\n}\r\n\r\n.back-icon {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.safe-area-top {\r\n  height: var(--status-bar-height);\r\n  width: 100%;\r\n  background-image: linear-gradient(135deg, #0066FF, #0052CC);\r\n}\r\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/partner/pages/partner-income.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "onMounted"], "mappings": ";;;;;;AAoIA,UAAM,eAAeA,cAAAA,IAAI,SAAS;AAClC,UAAM,cAAcA,cAAAA,IAAI,SAAS;AACjC,UAAM,mBAAmBA,cAAAA,IAAI,SAAS;AACtC,UAAM,oBAAoBA,cAAAA,IAAI,QAAQ;AACtC,UAAM,eAAeA,cAAAA,IAAI,EAAE;AAC3B,UAAM,aAAaA,cAAAA,IAAI,KAAK;AAC5B,UAAM,aAAaA,cAAAA,IAAI,CAAA,CAAE;AACzB,UAAM,OAAOA,cAAAA,IAAI,CAAC;AACDA,kBAAG,IAAC,EAAE;AACvB,UAAM,UAAUA,cAAAA,IAAI,IAAI;AACxB,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAC3B,UAAM,YAAYA,cAAAA,IAAI;AAAA,MACpB,EAAE,MAAM,SAAS,QAAQ,IAAI,QAAQ,GAAI;AAAA,MACzC,EAAE,MAAM,SAAS,QAAQ,KAAK,QAAQ,GAAI;AAAA,MAC1C,EAAE,MAAM,SAAS,QAAQ,IAAI,QAAQ,GAAI;AAAA,MACzC,EAAE,MAAM,SAAS,QAAQ,KAAK,QAAQ,GAAI;AAAA,MAC1C,EAAE,MAAM,SAAS,QAAQ,KAAK,QAAQ,GAAI;AAAA,MAC1C,EAAE,MAAM,SAAS,QAAQ,IAAI,QAAQ,GAAI;AAAA,MACzC,EAAE,MAAM,SAAS,QAAQ,KAAK,QAAQ,GAAI;AAAA,IAC5C,CAAC;AAID,UAAM,iBAAiBC,cAAQ,SAAC,MAAM;AACpC,YAAM,CAAC,MAAM,KAAK,IAAI,aAAa,MAAM,MAAM,GAAG;AAClD,aAAO,GAAG,IAAI,IAAI,SAAS,KAAK,CAAC;AAAA,IACnC,CAAC;AAGD,UAAM,iBAAiBA,cAAQ,SAAC,MAAM;AACpC,UAAI,WAAW,UAAU,OAAO;AAC9B,eAAO,WAAW;AAAA,MACtB,WAAa,WAAW,UAAU,UAAU;AACxC,eAAO,WAAW,MAAM,OAAO,UAAQ,KAAK,UAAU,CAAC;AAAA,MAC3D,OAAS;AACL,eAAO,WAAW,MAAM,OAAO,UAAQ,KAAK,UAAU,CAAC;AAAA,MACxD;AAAA,IACH,CAAC;AAID,UAAM,cAAc,CAAC,MAAM;AACzB,mBAAa,QAAQ,EAAE,OAAO;AAC9B,WAAK,QAAQ;AACb,cAAQ,QAAQ;AAChB,iBAAW,QAAQ;AACnB;AAGA;IACF;AAGA,UAAM,YAAY,CAAC,QAAQ;AACzB,iBAAW,QAAQ;AAAA,IACrB;AAGA,UAAM,WAAW,MAAM;AACrB,gBAAU,QAAQ;AAGlB,iBAAW,MAAM;AAEf,cAAM,WAAW;AAEjB,YAAI,KAAK,UAAU,GAAG;AACpB,qBAAW,QAAQ;AAAA,QACzB,OAAW;AACL,qBAAW,QAAQ,CAAC,GAAG,WAAW,OAAO,GAAG,QAAQ;AAAA,QACrD;AAGD,gBAAQ,QAAQ,KAAK,QAAQ;AAE7B,kBAAU,QAAQ;AAAA,MACnB,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,WAAW,MAAM;AACrB,UAAI,UAAU,SAAS,CAAC,QAAQ;AAAO;AAEvC,WAAK;AACL;IACF;AAGA,UAAM,kBAAkB,MAAM;AAG5B,YAAM,CAAC,MAAM,KAAK,IAAI,aAAa,MAAM,MAAM,GAAG;AAClD,YAAM,cAAc,IAAI,KAAK,SAAS,IAAI,GAAG,SAAS,KAAK,GAAG,CAAC,EAAE,QAAO;AAExE,gBAAU,QAAQ;AAGlB,YAAM,OAAO,KAAK,MAAM,cAAc,CAAC;AACvC,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAM,MAAM,IAAI,OAAO;AACvB,kBAAU,MAAM,KAAK;AAAA,UACnB,MAAM,GAAG,KAAK,IAAI,MAAM,KAAK,MAAM,MAAM,GAAG;AAAA,UAC5C,QAAQ,KAAK,MAAM,KAAK,OAAQ,IAAG,GAAG,IAAI;AAAA,UAC1C,QAAQ,KAAK,MAAM,KAAK,OAAQ,IAAG,EAAE,IAAI;AAAA,QAC/C,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,mBAAmB,MAAM;AAC7BC,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,qBAAqB,MAAM;AAC/B,YAAM,aAAa,CAAA;AACnB,YAAM,SAAS,CAAC,UAAU,UAAU,UAAU,QAAQ;AACtD,YAAM,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAGrE,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,cAAM,QAAQ,KAAK,OAAQ,IAAG,MAAM,IAAI;AACxC,cAAM,QAAQ,OAAO,KAAK,MAAM,KAAK,WAAW,OAAO,MAAM,CAAC;AAC9D,cAAM,OAAO,MAAM,KAAK,MAAM,KAAK,WAAW,MAAM,MAAM,CAAC;AAG3D,cAAM,SAAS,UAAU,KACpB,KAAK,MAAM,KAAK,OAAM,IAAK,GAAG,IAAI,IAAI,QAAQ,CAAC,KAC/C,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE,IAAI,IAAI,QAAQ,CAAC;AAGnD,cAAM,CAAC,MAAM,KAAK,IAAI,aAAa,MAAM,MAAM,GAAG;AAClD,cAAM,MAAM,KAAK,MAAM,KAAK,WAAW,EAAE,IAAI;AAC7C,cAAM,OAAO,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE;AAC1C,cAAM,SAAS,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE;AAC5C,cAAM,OAAO,GAAG,IAAI,IAAI,KAAK,IAAI,MAAM,KAAK,MAAM,MAAM,GAAG,IAAI,OAAO,KAAK,MAAM,OAAO,IAAI,IAAI,SAAS,KAAK,MAAM,SAAS,MAAM;AAEnI,mBAAW,KAAK;AAAA,UACd,IAAI,YAAY,KAAK,IAAK,IAAG;AAAA,UAC7B;AAAA,UACA,MAAM,KAAK,IAAI;AAAA,UACf;AAAA,UACA;AAAA,UACA;AAAA,QACN,CAAK;AAAA,MACF;AAGD,iBAAW,KAAK,CAAC,GAAG,MAAM,IAAI,KAAK,EAAE,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,CAAC;AAE7D,aAAO;AAAA,IACT;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGAC,kBAAAA,UAAU,MAAM;AAEd;IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvSD,GAAG,WAAW,eAAe;"}