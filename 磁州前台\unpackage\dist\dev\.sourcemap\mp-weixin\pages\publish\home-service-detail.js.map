{"version": 3, "file": "home-service-detail.js", "sources": ["pages/publish/home-service-detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHVibGlzaC9ob21lLXNlcnZpY2UtZGV0YWlsLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"service-detail-container\">\n    <!-- 添加自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"navbar-left\" @click=\"goBack\">\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\n      </view>\n      <view class=\"navbar-title\">到家服务详情</view>\n      <view class=\"navbar-right\">\n        <!-- 占位 -->\n      </view>\n    </view>\n    \n    <!-- 隐藏的分享按钮，用于自动触发 -->\n    <button id=\"shareButton\" class=\"hidden-share-btn\" open-type=\"share\"></button>\n    \n    <!-- 隐藏的Canvas用于绘制海报 -->\n    <canvas canvas-id=\"posterCanvas\" class=\"poster-canvas\" style=\"width: 600px; height: 900px; position: fixed; top: -9999px; left: -9999px;\"></canvas>\n    \n    <!-- 悬浮海报按钮 -->\n    <view class=\"float-poster-btn\" @click=\"generateShareImage\">\n      <image src=\"/static/images/tabbar/海报.png\" class=\"poster-icon\"></image>\n      <text class=\"poster-text\">海报</text>\n    </view>\n    \n    <!-- 举报按钮 -->\n    \n    <view class=\"service-detail-wrapper\">\n      <!-- 服务基本信息卡片 -->\n      <view class=\"content-card service-info-card\">\n        <view class=\"service-header\">\n          <view class=\"service-title-row\">\n            <text class=\"service-title\">{{serviceData.title}}</text>\n            <text class=\"service-price\">{{serviceData.price}}</text>\n          </view>\n          <view class=\"service-meta\">\n            <view class=\"service-tag-group\">\n              <view class=\"service-tag\" v-for=\"(tag, index) in serviceData.tags\" :key=\"index\">{{tag}}</view>\n            </view>\n            <text class=\"service-publish-time\">发布于 {{formatTime(serviceData.publishTime)}}</text>\n          </view>\n        </view>\n        \n        <!-- 服务图片轮播 -->\n        <swiper class=\"service-swiper\" :indicator-dots=\"true\" :autoplay=\"true\" :interval=\"3000\" :duration=\"500\">\n          <swiper-item v-for=\"(image, index) in serviceData.images\" :key=\"index\">\n            <image :src=\"image\" mode=\"aspectFill\" class=\"service-image\"></image>\n          </swiper-item>\n        </swiper>\n        \n        <!-- 基本信息 -->\n        <view class=\"service-basic-info\">\n          <view class=\"info-item\">\n            <text class=\"info-label\">服务类型</text>\n            <text class=\"info-value\">{{serviceData.type}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">服务范围</text>\n            <text class=\"info-value\">{{serviceData.area}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">服务时间</text>\n            <text class=\"info-value\">{{serviceData.time}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">服务方式</text>\n            <text class=\"info-value\">{{serviceData.method}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 服务内容 -->\n      <view class=\"content-card service-content-card\">\n        <view class=\"section-title\">服务内容</view>\n        <view class=\"content-list\">\n          <view class=\"content-item\" v-for=\"(item, index) in serviceData.contents\" :key=\"index\">\n            <text class=\"content-icon iconfont\" :class=\"item.icon\"></text>\n            <text class=\"content-text\">{{item.name}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 服务说明 -->\n      <view class=\"content-card service-desc-card\">\n        <view class=\"section-title\">服务说明</view>\n        <view class=\"desc-content\">\n          <rich-text :nodes=\"serviceData.description\"></rich-text>\n        </view>\n      </view>\n      \n      <!-- 服务保障 -->\n      <view class=\"content-card service-guarantee-card\">\n        <view class=\"section-title\">服务保障</view>\n        <view class=\"guarantee-list\">\n          <view class=\"guarantee-item\" v-for=\"(item, index) in serviceData.guarantees\" :key=\"index\">\n            <text class=\"guarantee-icon iconfont\" :class=\"item.icon\"></text>\n            <view class=\"guarantee-info\">\n              <text class=\"guarantee-title\">{{item.title}}</text>\n              <text class=\"guarantee-desc\">{{item.desc}}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 服务商信息 -->\n      <view class=\"content-card provider-card\">\n        <view class=\"section-title\">服务商信息</view>\n        <view class=\"provider-header\">\n          <view class=\"provider-avatar\">\n            <image :src=\"serviceData.provider.avatar\" mode=\"aspectFill\"></image>\n          </view>\n          <view class=\"provider-info\">\n            <text class=\"provider-name\">{{serviceData.provider.name}}</text>\n            <view class=\"provider-meta\">\n              <text class=\"provider-type\">{{serviceData.provider.type}}</text>\n              <text class=\"provider-rating\">好评率 {{serviceData.provider.rating}}%</text>\n            </view>\n          </view>\n          <view class=\"provider-auth\" v-if=\"serviceData.provider.isVerified\">\n            <text class=\"iconfont icon-verified\"></text>\n            <text class=\"auth-text\">已认证</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 联系方式 -->\n      <view class=\"content-card contact-card\">\n        <view class=\"section-title\">联系方式</view>\n        <view class=\"contact-content\">\n          <view class=\"contact-item\">\n            <text class=\"contact-label\">联系人</text>\n            <text class=\"contact-value\">{{serviceData.contact.name}}</text>\n          </view>\n          <view class=\"contact-item\">\n            <text class=\"contact-label\">电话</text>\n            <text class=\"contact-value contact-phone\" @click=\"callPhone\">{{serviceData.contact.phone}}</text>\n          </view>\n          <view class=\"contact-tips\">\n            <text class=\"tips-icon iconfont icon-info\"></text>\n            <text class=\"tips-text\">请说明在\"磁州生活网\"看到的信息</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 举报卡片 -->\n      <view class=\"content-card report-card\" @click=\"showReportOptions\">\n        <view class=\"report-content\">\n          <image src=\"/static/images/tabbar/举报.png\" class=\"report-icon\"></image>\n          <view class=\"report-text-container\">\n            <view class=\"report-title\">如遇无效、虚假信息，请立即举报</view>\n            <view class=\"report-subtitle\">平台将尽快核实处理</view>\n          </view>\n          <view class=\"report-arrow\">\n            <view class=\"arrow-icon\"></view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 相关服务推荐 -->\n      <view class=\"content-card related-services-card\">\n        <view class=\"section-title\">相关服务推荐</view>\n        <view class=\"related-services-content\">\n          <!-- 简洁的服务列表 -->\n          <view class=\"related-services-list\">\n            <view class=\"related-service-item\" \n                 v-for=\"(service, index) in relatedServices.slice(0, 3)\" \n                 :key=\"index\" \n                 @click=\"navigateToServiceDetail(service.id, service.type)\">\n              <view class=\"service-item-content\">\n                <view class=\"service-item-left\">\n                  <image class=\"provider-logo\" :src=\"service.providerLogo\" mode=\"aspectFill\"></image>\n                </view>\n                <view class=\"service-item-middle\">\n                  <text class=\"service-item-title\">{{service.title}}</text>\n                  <view class=\"service-item-provider\">{{service.providerName}}</view>\n                  <view class=\"service-item-tags\">\n                    <text class=\"service-item-tag\" v-for=\"(tag, tagIndex) in service.tags.slice(0, 2)\" :key=\"tagIndex\">{{tag}}</text>\n                    <text class=\"service-item-tag-more\" v-if=\"service.tags.length > 2\">+{{service.tags.length - 2}}</text>\n                  </view>\n                </view>\n                <view class=\"service-item-right\">\n                  <text class=\"service-item-price\">{{service.price}}</text>\n                </view>\n              </view>\n            </view>\n            \n            <!-- 暂无数据提示 -->\n            <view class=\"empty-related-services\" v-if=\"relatedServices.length === 0\">\n              <image src=\"/static/images/empty.png\" class=\"empty-image\" mode=\"aspectFit\"></image>\n              <text class=\"empty-text\">暂无相关服务</text>\n            </view>\n          </view>\n          \n          <!-- 查看更多按钮 -->\n          <view class=\"view-more-btn\" v-if=\"relatedServices.length > 0\" @click.stop=\"navigateToServiceList\">\n            <text class=\"view-more-text\">查看更多服务信息</text>\n            <text class=\"view-more-icon iconfont icon-right\"></text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"interaction-toolbar\">\n      <view class=\"toolbar-item\" @click=\"goToHome\">\n        <image src=\"/static/images/tabbar/a首页.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">首页</text>\n      </view>\n      <view class=\"toolbar-item\" @click=\"toggleCollect\">\n        <image src=\"/static/images/tabbar/a收藏.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">收藏</text>\n      </view>\n      <button class=\"share-button toolbar-item\" open-type=\"share\">\n        <image src=\"/static/images/tabbar/a分享.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">分享</text>\n      </button>\n      <view class=\"toolbar-item\" @click=\"openChat\">\n        <image src=\"/static/images/tabbar/a消息.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">私信</text>\n      </view>\n      <view class=\"toolbar-item call-button\" @click=\"callPhone\">\n        <view class=\"call-button-content\">\n          <text class=\"call-text\">打电话</text>\n          <text class=\"call-subtitle\">请说在磁州生活网看到的</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport ReportCard from '@/components/ReportCard.vue';\n\n// 状态栏高度\nconst statusBarHeight = ref(20);\n\n// 获取状态栏高度\nonMounted(() => {\n  try {\n    const sysInfo = uni.getSystemInfoSync();\n    statusBarHeight.value = sysInfo.statusBarHeight || 20;\n  } catch (e) {\n    console.error('获取状态栏高度失败', e);\n  }\n});\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack({\n    fail: () => {\n      uni.switchTab({\n        url: '/pages/index/index'\n      });\n    }\n  });\n};\n\n// 格式化时间\nconst formatTime = (timestamp) => {\n  const date = new Date(timestamp);\n  return `${date.getMonth() + 1}月${date.getDate()}日`;\n};\n\n// 响应式数据\nconst isCollected = ref(false);\nconst posterImagePath = ref('');\nconst showPosterFlag = ref(false);\n\n// 相关服务列表\nconst relatedServices = ref([]);\n\n// 加载相关服务信息\nconst loadRelatedServices = () => {\n  // 获取当前服务类型的代码\n  const currentType = serviceData.value.type;\n  const currentTypeCode = getSubTypeCode(currentType);\n  \n  // 根据当前服务类型加载相关服务\n  setTimeout(() => {\n    // 根据服务类型设置不同的推荐服务\n    switch(currentTypeCode) {\n      case 'locksmith': // 开锁换锁\n        relatedServices.value = [\n          {\n            id: 'locksmith001',\n            title: '24小时紧急开锁',\n            price: '60元起',\n            providerName: '安全开锁中心',\n            providerLogo: '/static/images/tabbar/开锁.png',\n            tags: ['24小时服务', '快速上门', '技术精湛'],\n            type: 'locksmith'\n          },\n          {\n            id: 'locksmith002',\n            title: '智能锁安装服务',\n            price: '150元起',\n            providerName: '智能家居安装',\n            providerLogo: '/static/images/tabbar/智能锁.png',\n            tags: ['指纹锁', '密码锁', '刷卡锁'],\n            type: 'locksmith'\n          },\n          {\n            id: 'locksmith003',\n            title: '保险柜开锁服务',\n            price: '200元起',\n            providerName: '专业开锁技师',\n            providerLogo: '/static/images/tabbar/保险柜.png',\n            tags: ['保险柜', '保险箱', '密码箱'],\n            type: 'locksmith'\n          }\n        ];\n        break;\n      case 'installation': // 上门安装\n        relatedServices.value = [\n          {\n            id: 'installation001',\n            title: '家具组装服务',\n            price: '80元起',\n            providerName: '家具安装中心',\n            providerLogo: '/static/images/tabbar/家具.png',\n            tags: ['专业工具', '经验丰富', '安装保障'],\n            type: 'installation'\n          },\n          {\n            id: 'installation002',\n            title: '电器安装服务',\n            price: '100元起',\n            providerName: '电器安装专家',\n            providerLogo: '/static/images/tabbar/电器.png',\n            tags: ['空调安装', '电视安装', '洗衣机安装'],\n            type: 'installation'\n          },\n          {\n            id: 'installation003',\n            title: '灯具安装服务',\n            price: '60元起',\n            providerName: '灯具安装师傅',\n            providerLogo: '/static/images/tabbar/灯具.png',\n            tags: ['吊灯安装', '射灯安装', '壁灯安装'],\n            type: 'installation'\n          }\n        ];\n        break;\n      case 'home_cleaning': // 家政保洁\n      default:\n    relatedServices.value = [\n      {\n        id: 'service001',\n        title: '专业擦玻璃服务',\n        price: '40元/平方',\n        providerName: '阿姨到家家政',\n        providerLogo: '/static/images/tabbar/清洁.png',\n            tags: ['高空作业', '安全保障', '专业设备'],\n            type: 'home_cleaning'\n      },\n      {\n        id: 'service002',\n        title: '空调深度清洗',\n        price: '100元/台起',\n        providerName: '磁县家电清洗中心',\n        providerLogo: '/static/images/tabbar/空调.png',\n            tags: ['上门服务', '专业工具', '彻底除菌'],\n            type: 'home_cleaning'\n      },\n      {\n        id: 'service003',\n        title: '深度除螨服务',\n        price: '288元起',\n        providerName: '优家净家政',\n        providerLogo: '/static/images/tabbar/除螨.png',\n            tags: ['沙发床垫', '紫外除螨', '深度杀菌'],\n            type: 'home_cleaning'\n      }\n    ];\n        break;\n    }\n  }, 500);\n};\n\n// 跳转到服务详情页\nconst navigateToServiceDetail = (id, type) => {\n  // 避免重复跳转当前页面\n  const pages = getCurrentPages();\n  const currentPage = pages[pages.length - 1];\n  const options = currentPage.$page?.options || {};\n  \n  if (id === options.id) {\n    return;\n  }\n  \n  // 如果没有传入服务类型，使用当前服务类型\n  const serviceType = type || getSubTypeCode(serviceData.value.type);\n  \n  uni.navigateTo({\n    url: `/pages/publish/home-service-detail?id=${id}&type=${serviceType}`\n  });\n};\n\n// 跳转到服务列表页\nconst navigateToServiceList = (e) => {\n  if (e) e.stopPropagation();\n  // 获取当前服务类型作为筛选条件\n  const serviceCategory = serviceData.value.type || '';\n  const serviceTypeCode = getSubTypeCode(serviceCategory);\n  \n  // 跳转到到家服务列表页，并传递正确的子分类参数\n  uni.navigateTo({\n    url: `/pages/service/home-service-list?subType=${serviceTypeCode}&subName=${encodeURIComponent(serviceCategory)}` \n  });\n};\n\n// 根据分类名称获取相应的类型编码\nconst getSubTypeCode = (categoryName) => {\n  const categoryMap = {\n    '家政保洁': 'home_cleaning',\n    '家政服务': 'home_cleaning',\n    '维修改造': 'repair',\n    '上门安装': 'installation',\n    '开锁换锁': 'locksmith',\n    '搬家拉货': 'moving',\n    '上门美容': 'beauty',\n    '上门家教': 'tutor',\n    '宠物服务': 'pet_service',\n    '上门疏通': 'plumbing'\n  };\n  \n  return categoryMap[categoryName] || 'home_cleaning';\n};\n\nconst serviceData = ref({\n  id: 'service12345',\n  title: '专业家政保洁服务',\n  price: '50-120元/小时起',\n  tags: ['专业保洁', '上门服务', '可预约', '环保用品', '全城服务'],\n  publishTime: Date.now() - 86400000 * 2, // 2天前\n  images: [\n    '/static/images/service1.jpg',\n    '/static/images/service2.jpg',\n    '/static/images/service3.jpg',\n    '/static/images/service4.jpg',\n    '/static/images/service5.jpg'\n  ],\n  type: '家政保洁',\n  area: '磁县城区及周边10公里',\n  time: '8:00-20:00（节假日不休）',\n  method: '上门服务，提前预约',\n  contents: [\n    { name: '日常保洁（擦拭、除尘、吸尘、拖地等）', icon: 'icon-clean' },\n    { name: '深度保洁（角落清洁、污渍处理等）', icon: 'icon-deep-clean' },\n    { name: '开荒保洁（新房入住前全面清洁）', icon: 'icon-new-clean' },\n    { name: '家电清洗（空调、冰箱、洗衣机等）', icon: 'icon-appliance' },\n    { name: '玻璃清洗（内外玻璃、高层玻璃）', icon: 'icon-window' },\n    { name: '地板打蜡（木地板、瓷砖等）', icon: 'icon-floor' },\n    { name: '沙发清洗（布艺、真皮沙发）', icon: 'icon-sofa' },\n    { name: '厨房深度清洁（油烟机、灶台等）', icon: 'icon-kitchen' }\n  ],\n  description: '<p>磁县家政服务中心提供专业的家政保洁服务，服务范围包括但不限于：</p><ul><li>日常保洁：室内清洁、地面清洁、家具清洁、卫生间清洁等</li><li>深度保洁：死角清洁、顽固污渍处理、除菌消毒等</li><li>开荒保洁：新房入住前的全面清洁，包括装修后遗留物清理</li><li>家电清洗：专业拆卸清洗空调、冰箱、洗衣机、热水器等</li><li>玻璃清洗：内外玻璃、门窗、高层落地窗等</li><li>地板打蜡：各类地板专业打蜡、抛光、养护</li></ul><p>我们的服务特点：</p><ul><li>专业团队：全部员工经过专业培训，持证上岗</li><li>专业设备：使用先进清洁设备，提高清洁效率和质量</li><li>环保材料：使用环保清洁用品，安全健康无污染</li><li>灵活预约：支持线上预约，可提前1-7天预约服务</li><li>售后保障：服务不满意可重做，损坏物品照价赔偿</li></ul>',\n  guarantees: [\n    {\n      icon: 'icon-quality',\n      title: '品质保障',\n      desc: '专业保洁人员，持证上岗，统一着装，规范操作'\n    },\n    {\n      icon: 'icon-safe',\n      title: '安全保障',\n      desc: '员工背景调查，使用环保清洁用品，损坏赔付'\n    },\n    {\n      icon: 'icon-time',\n      title: '准时服务',\n      desc: '准时上门，不迟到，如有延误提前通知'\n    },\n    {\n      icon: 'icon-satisfaction',\n      title: '满意保障',\n      desc: '服务不满意可重做，服务质量有问题可投诉退款'\n    },\n    {\n      icon: 'icon-privacy',\n      title: '隐私保障',\n      desc: '保护客户隐私，不拍摄客户家庭环境'\n    }\n  ],\n  provider: {\n    name: '磁县家政服务中心',\n    avatar: '/static/images/avatar.png',\n    type: '专业机构（5年经验）',\n    rating: 98,\n    isVerified: true\n  },\n  contact: {\n    name: '王经理（服务总监）',\n    phone: '13912345678'\n  }\n});\n\n// 收藏方法\nconst toggleCollect = () => {\n  isCollected.value = !isCollected.value;\n  if (isCollected.value) {\n    uni.showToast({\n      title: '收藏成功',\n      icon: 'success'\n    });\n  }\n};\n\n// 分享方法\nconst showShareOptions = () => {\n  uni.showShareMenu({\n    withShareTicket: true,\n    menus: ['shareAppMessage', 'shareTimeline']\n  });\n};\n\n// 拨打电话方法\nconst callPhone = () => {\n  uni.makePhoneCall({\n    phoneNumber: serviceData.value.contact.phone,\n    fail: () => {\n      uni.showToast({\n        title: '拨打电话失败',\n        icon: 'none'\n      });\n    }\n  });\n};\n\n// 跳转到首页\nconst goToHome = () => {\n  uni.switchTab({\n    url: '/pages/index/index'\n  });\n};\n\n// 打开私信聊天\nconst openChat = () => {\n  // 检查是否已登录\n  const userInfo = uni.getStorageSync('userInfo');\n  if (!userInfo) {\n    uni.showModal({\n      title: '提示',\n      content: '发送私信需要先登录，是否前往登录？',\n      success: (res) => {\n        if (res.confirm) {\n          uni.navigateTo({\n            url: '/pages/login/login?redirect=' + encodeURIComponent(`/pages/publish/home-service-detail?id=${serviceData.value.id}`)\n          });\n        }\n      }\n    });\n    return;\n  }\n  \n  // 跳转到聊天页面\n  uni.navigateTo({\n    url: `/pages/chat/chat?targetId=${serviceData.value.provider.id}&targetName=${serviceData.value.provider.name}&targetAvatar=${encodeURIComponent(serviceData.value.provider.avatar)}`\n  });\n};\n\n// 生成海报的方法\nconst generateShareImage = () => {\n  uni.showLoading({\n    title: '正在生成海报...',\n    mask: true\n  });\n  \n  // 创建海报数据对象\n  const posterData = {\n    title: serviceData.value.title,\n    price: serviceData.value.price,\n    type: serviceData.value.type,\n    address: serviceData.value.area,\n    phone: serviceData.value.contact ? serviceData.value.contact.phone : '',\n    description: serviceData.value.description ? serviceData.value.description.substring(0, 60) + '...' : '',\n    qrcode: '/static/images/tabbar/客服微信.png',\n    logo: '/static/images/tabbar/家政服务.png',\n    bgImage: serviceData.value.cover || '/static/images/banner/banner-1.png'\n  };\n  \n  // #ifdef H5\n  // H5环境不支持canvas绘制图片保存，提示用户\n  setTimeout(() => {\n    uni.hideLoading();\n    uni.showModal({\n      title: '提示',\n      content: 'H5环境暂不支持保存海报，请使用App或小程序',\n      showCancel: false\n    });\n  }, 1000);\n  return;\n  // #endif\n  \n  // 绘制海报\n  const ctx = uni.createCanvasContext('posterCanvas');\n  \n  // 绘制背景\n  ctx.save();\n  ctx.drawImage(posterData.bgImage, 0, 0, 600, 400);\n  // 添加半透明蒙层\n  ctx.setFillStyle('rgba(0, 0, 0, 0.35)');\n  ctx.fillRect(0, 0, 600, 900);\n  ctx.restore();\n  \n  // 绘制白色卡片背景\n  ctx.save();\n  ctx.setFillStyle('#ffffff');\n  ctx.fillRect(30, 280, 540, 550);\n  ctx.restore();\n  \n  // 绘制Logo\n  ctx.save();\n  ctx.beginPath();\n  ctx.arc(300, 200, 80, 0, 2 * Math.PI);\n  ctx.setFillStyle('#ffffff');\n  ctx.fill();\n  // 在圆形内绘制Logo\n  ctx.clip();\n  ctx.drawImage(posterData.logo, 220, 120, 160, 160);\n  ctx.restore();\n  \n  // 绘制服务标题\n  ctx.setFillStyle('#333333');\n  ctx.setFontSize(32);\n  ctx.setTextAlign('center');\n  ctx.fillText(posterData.title, 300, 350);\n  \n  // 绘制价格\n  ctx.setFillStyle('#FF6B6B');\n  ctx.setFontSize(28);\n  ctx.fillText(posterData.price, 300, 400);\n  \n  // 分割线\n  ctx.beginPath();\n  ctx.setStrokeStyle('#eeeeee');\n  ctx.setLineWidth(2);\n  ctx.moveTo(100, 430);\n  ctx.lineTo(500, 430);\n  ctx.stroke();\n  \n  // 绘制服务类型\n  ctx.setFillStyle('#666666');\n  ctx.setFontSize(24);\n  ctx.setTextAlign('left');\n  ctx.fillText('服务类型: ' + posterData.type, 80, 480);\n  \n  // 绘制服务区域\n  ctx.fillText('服务区域: ' + posterData.address, 80, 520);\n  \n  // A wrap text function\n  const wrapText = (ctx, text, x, y, maxWidth, lineHeight) => {\n    if (text.length === 0) return;\n    \n    const words = text.split('');\n    let line = '';\n    let testLine = '';\n    let lineCount = 0;\n    \n    for (let n = 0; n < words.length; n++) {\n      testLine += words[n];\n      const metrics = ctx.measureText(testLine);\n      const testWidth = metrics.width;\n      \n      if (testWidth > maxWidth && n > 0) {\n        ctx.fillText(line, x, y + (lineCount * lineHeight));\n        line = words[n];\n        testLine = words[n];\n        lineCount++;\n        \n        if (lineCount >= 3) {\n          line += '...';\n          ctx.fillText(line, x, y + (lineCount * lineHeight));\n          break;\n        }\n      } else {\n        line = testLine;\n      }\n    }\n    \n    if (lineCount < 3) {\n      ctx.fillText(line, x, y + (lineCount * lineHeight));\n    }\n  };\n  \n  // 绘制简介\n  ctx.setFillStyle('#666666');\n  wrapText(ctx, posterData.description, 80, 560, 440, 35);\n  \n  // 绘制电话\n  if (posterData.phone) {\n    ctx.fillText('联系电话: ' + posterData.phone, 80, 680);\n  }\n  \n  // 绘制小程序码\n  ctx.drawImage(posterData.qrcode, 225, 720, 150, 150);\n  \n  // 提示文字\n  ctx.setFillStyle('#999999');\n  ctx.setFontSize(20);\n  ctx.setTextAlign('center');\n  ctx.fillText('长按识别二维码查看详情', 300, 880);\n  \n  // 应用平台Logo\n  ctx.setFillStyle('#333333');\n  ctx.setFontSize(24);\n  ctx.fillText('磁县同城 - 家政服务', 300, 840);\n  \n  // 绘制完成，输出图片\n  ctx.draw(false, () => {\n    setTimeout(() => {\n      // 延迟确保canvas已完成渲染\n      uni.canvasToTempFilePath({\n        canvasId: 'posterCanvas',\n        success: (res) => {\n          uni.hideLoading();\n          showPosterModal(res.tempFilePath);\n        },\n        fail: (err) => {\n          console.error('生成海报失败', err);\n          uni.hideLoading();\n          uni.showToast({\n            title: '生成海报失败',\n            icon: 'none'\n          });\n        }\n      });\n    }, 800);\n  });\n};\n\n// 显示海报预览和保存选项\nconst showPosterModal = (posterPath) => {\n  posterImagePath.value = posterPath;\n  showPosterFlag.value = true;\n  \n  uni.showModal({\n    title: '海报已生成',\n    content: '海报已生成，是否保存到相册？',\n    confirmText: '保存',\n    success: (res) => {\n      if (res.confirm) {\n        savePosterToAlbum(posterPath);\n      } else {\n        // 预览图片\n        uni.previewImage({\n          urls: [posterPath],\n          current: posterPath\n        });\n      }\n    }\n  });\n};\n\n// 保存海报到相册\nconst savePosterToAlbum = (posterPath) => {\n  uni.showLoading({\n    title: '正在保存...'\n  });\n  \n  uni.saveImageToPhotosAlbum({\n    filePath: posterPath,\n    success: () => {\n      uni.hideLoading();\n      uni.showToast({\n        title: '已保存到相册',\n        icon: 'success'\n      });\n    },\n    fail: (err) => {\n      uni.hideLoading();\n      console.error('保存失败', err);\n      \n      if (err.errMsg.indexOf('auth deny') > -1) {\n        uni.showModal({\n          title: '提示',\n          content: '保存失败，请授权相册权限后重试',\n          confirmText: '去设置',\n          success: (res) => {\n            if (res.confirm) {\n              uni.openSetting();\n            }\n          }\n        });\n      } else {\n        uni.showToast({\n          title: '保存失败',\n          icon: 'none'\n        });\n      }\n    }\n  });\n};\n\n// 举报相关\nconst showReportOptions = () => {\n  uni.showActionSheet({\n    itemList: ['虚假信息', '违法内容', '色情内容', '侵权投诉', '诱导欺骗', '其他问题'],\n    success: (res) => {\n      const reportReasonIndex = res.tapIndex;\n      const reasons = ['虚假信息', '违法内容', '色情内容', '侵权投诉', '诱导欺骗', '其他问题'];\n      showReportInputDialog(reasons[reportReasonIndex]);\n    }\n  });\n};\n\nconst showReportInputDialog = (reason) => {\n  // 检查是否登录\n  const hasLogin = uni.getStorageSync('token') || false;\n  if (!hasLogin) {\n    uni.showModal({\n      title: '提示',\n      content: '请先登录后再进行举报',\n      confirmText: '去登录',\n      success: (res) => {\n        if (res.confirm) {\n          uni.navigateTo({\n            url: '/pages/login/login'\n          });\n        }\n      }\n    });\n    return;\n  }\n  \n  uni.showModal({\n    title: '举报内容',\n    content: `您选择的举报原因是: ${reason}，请确认是否提交举报？`,\n    confirmText: '确认举报',\n    success: (res) => {\n      if (res.confirm) {\n        submitReport(reason, '');\n      }\n    }\n  });\n};\n\nconst submitReport = (reason, content) => {\n  uni.showLoading({\n    title: '提交中...'\n  });\n  \n  // 这里模拟提交举报信息到服务器\n    setTimeout(() => {\n    uni.hideLoading();\n    \n    uni.showToast({\n      title: '举报成功',\n      icon: 'success'\n    });\n    \n    // 实际开发中，这里应该调用API提交举报信息\n    // const reportData = {\n    //   type: 'homeService',\n    //   id: serviceData.value.id,\n    //   reason: reason,\n    //   content: content\n    // };\n    // submitReportAPI(reportData).then(() => {\n    //   uni.showToast({\n    //     title: '举报成功',\n    //     icon: 'success'\n    //   });\n    // });\n    }, 1500);\n};\n\n// 生命周期钩子\nonMounted(() => {\n  // 获取路由参数\n  const pages = getCurrentPages();\n  const currentPage = pages[pages.length - 1];\n  const options = currentPage.options || {};\n  \n  // 获取服务ID和类型\n  const id = options.id || '';\n  const serviceType = options.type || '';\n  \n  // 加载服务数据\n  loadServiceData(id, serviceType);\n  \n  // 加载相关服务推荐\n  loadRelatedServices();\n});\n\n// 加载服务数据方法\nconst loadServiceData = (id, serviceType) => {\n  // 根据不同服务类型加载不同数据\n  console.log('加载服务类型:', serviceType, '服务ID:', id || '默认服务');\n  \n  switch(serviceType) {\n    case 'installation': // 上门安装\n      loadInstallationData(id);\n      break;\n    case 'locksmith': // 开锁换锁\n      loadLocksmithData(id);\n      break;\n    case 'repair': // 维修改造\n      loadRepairData(id);\n      break;\n    case 'moving': // 搬家拉货\n      loadMovingData(id);\n      break;\n    case 'beauty': // 上门美容\n      loadBeautyData(id);\n      break;\n    case 'tutor': // 上门家教\n      loadTutorData(id);\n      break;\n    case 'pet_service': // 宠物服务\n      loadPetServiceData(id);\n      break;\n    case 'plumbing': // 上门疏通\n      loadPlumbingData(id);\n      break;\n    case 'home_cleaning': // 家政保洁\n    default:\n      // 使用默认的家政保洁数据\n      // 不需要修改serviceData.value，因为它已经默认是家政保洁数据\n      break;\n  }\n  \n  // 修改页面标题\n  uni.setNavigationBarTitle({\n    title: serviceData.value.title || '服务详情'\n  });\n};\n\n// 加载上门安装数据\nconst loadInstallationData = (id) => {\n  serviceData.value = {\n    id: id || 'installation12345',\n    title: '专业上门安装服务',\n    price: '60-200元/次起',\n    tags: ['专业工具', '上门服务', '可预约', '全城服务'],\n    publishTime: Date.now() - 86400000 * 3, // 3天前\n    images: [\n      '/static/images/service2.jpg',\n      '/static/images/service3.jpg',\n      '/static/images/service1.jpg'\n    ],\n    type: '上门安装',\n    area: '磁县城区及周边15公里',\n    time: '8:00-21:00（节假日照常服务）',\n    method: '上门服务，提前预约',\n    contents: [\n      { name: '家具安装（床、柜、桌椅等）', icon: 'icon-furniture' },\n      { name: '电器安装（电视、空调等）', icon: 'icon-appliance' },\n      { name: '灯具安装（吊灯、壁灯等）', icon: 'icon-light' },\n      { name: '卫浴安装（花洒、马桶等）', icon: 'icon-bathroom' },\n      { name: '门窗安装（室内门、推拉门等）', icon: 'icon-door' },\n      { name: '五金安装（挂钩、置物架等）', icon: 'icon-hardware' }\n    ],\n    description: '<p>磁县专业上门安装服务，提供各类家具、电器、灯具等安装服务：</p><ul><li>家具安装：床、衣柜、书柜、桌椅、沙发等</li><li>电器安装：电视机、空调、洗衣机、热水器等</li><li>灯具安装：吊灯、吸顶灯、壁灯、射灯等</li><li>卫浴安装：花洒、马桶、浴室柜、浴缸等</li><li>门窗安装：室内门、推拉门、折叠门等</li><li>五金安装：窗帘杆、挂钩、置物架等</li></ul><p>服务特点：</p><ul><li>专业师傅：经验丰富，技术精湛</li><li>专业工具：配备各类专业安装工具</li><li>上门服务：直接到府上安装，省时省力</li><li>质量保障：安装完成后检查确认，确保质量</li></ul>',\n    guarantees: [\n      {\n        icon: 'icon-quality',\n        title: '品质保障',\n        desc: '专业安装师傅，持证上岗，经验丰富'\n      },\n      {\n        icon: 'icon-safe',\n        title: '安全保障',\n        desc: '师傅背景调查，安装规范，损坏赔付'\n      },\n      {\n        icon: 'icon-time',\n        title: '准时服务',\n        desc: '准时上门，不迟到，如有延误提前通知'\n      }\n    ],\n    provider: {\n      name: '磁县家居安装服务中心',\n      avatar: '/static/images/avatar.png',\n      type: '专业机构（6年经验）',\n      rating: 97,\n      isVerified: true\n    },\n    contact: {\n      name: '李师傅（安装主管）',\n      phone: '13987654321'\n    }\n  };\n};\n\n// 加载开锁换锁数据\nconst loadLocksmithData = (id) => {\n  serviceData.value = {\n    id: id || 'locksmith12345',\n    title: '专业开锁换锁服务',\n    price: '60-180元/次起',\n    tags: ['24小时服务', '快速上门', '专业技师', '全城服务'],\n    publishTime: Date.now() - 86400000 * 1, // 1天前\n    images: [\n      '/static/images/service3.jpg',\n      '/static/images/service1.jpg',\n      '/static/images/service2.jpg'\n    ],\n    type: '开锁换锁',\n    area: '磁县城区及周边20公里',\n    time: '全天24小时（节假日不休）',\n    method: '上门服务，随叫随到',\n    contents: [\n      { name: '开锁服务（防盗门、保险柜等）', icon: 'icon-unlock' },\n      { name: '换锁服务（门锁、抽屉锁等）', icon: 'icon-lock' },\n      { name: '修锁服务（锁芯修复等）', icon: 'icon-repair' },\n      { name: '配钥匙（各类钥匙配制）', icon: 'icon-key' },\n      { name: '安装锁具（新锁安装）', icon: 'icon-install' },\n      { name: '智能锁安装（指纹锁、密码锁）', icon: 'icon-smart' }\n    ],\n    description: '<p>磁县专业开锁换锁服务，提供24小时上门服务：</p><ul><li>开锁服务：防盗门、木门、保险柜、抽屉、汽车等各类锁具开启</li><li>换锁服务：更换各类门锁、抽屉锁、保险柜锁等</li><li>修锁服务：修复锁芯、门锁调整等</li><li>配钥匙：各类钥匙复制、配制</li><li>安装锁具：新锁具安装、加装等</li><li>智能锁安装：指纹锁、密码锁、刷卡锁等智能锁安装</li></ul><p>服务特点：</p><ul><li>24小时服务：随时响应您的需求</li><li>快速上门：接单后30分钟内到达</li><li>专业技师：持证上岗，技术精湛</li><li>合理收费：透明价格，无隐形消费</li></ul>',\n    guarantees: [\n      {\n        icon: 'icon-quality',\n        title: '品质保障',\n        desc: '专业开锁师傅，持证上岗，经验丰富'\n      },\n      {\n        icon: 'icon-safe',\n        title: '安全保障',\n        desc: '师傅身份验证，开锁需验证身份'\n      },\n      {\n        icon: 'icon-time',\n        title: '快速响应',\n        desc: '30分钟内上门，紧急情况优先处理'\n      }\n    ],\n    provider: {\n      name: '磁县安全开锁服务中心',\n      avatar: '/static/images/avatar.png',\n      type: '专业机构（8年经验）',\n      rating: 98,\n      isVerified: true\n    },\n    contact: {\n      name: '王师傅（开锁技师）',\n      phone: '13876543210'\n    }\n  };\n};\n\n// 加载维修改造数据\nconst loadRepairData = (id) => {\n  serviceData.value = {\n    id: id || 'repair12345',\n    title: '专业维修改造服务',\n    price: '80-300元/次起',\n    tags: ['专业维修', '上门服务', '可预约', '全城服务'],\n    publishTime: Date.now() - 86400000 * 2, // 2天前\n    images: [\n      '/static/images/service1.jpg',\n      '/static/images/service2.jpg',\n      '/static/images/service3.jpg'\n    ],\n    type: '维修改造',\n    area: '磁县城区及周边15公里',\n    time: '8:00-20:00（节假日照常服务）',\n    method: '上门服务，提前预约',\n    contents: [\n      { name: '水电维修（水管、电路等）', icon: 'icon-water-electric' },\n      { name: '家具维修（桌椅、柜子等）', icon: 'icon-furniture' },\n      { name: '家电维修（冰箱、洗衣机等）', icon: 'icon-appliance' },\n      { name: '门窗维修（门锁、合页等）', icon: 'icon-door' },\n      { name: '墙面维修（墙漆、裂缝等）', icon: 'icon-wall' },\n      { name: '小型改造（隔断、架子等）', icon: 'icon-renovation' }\n    ],\n    description: '<p>磁县专业维修改造服务，解决您家中各类维修需求：</p><ul><li>水电维修：水管漏水、电路故障、开关插座更换等</li><li>家具维修：桌椅修复、柜门调整、五金更换等</li><li>家电维修：常见家电故障排除、清洗保养等</li><li>门窗维修：门锁更换、合页调整、密封条更换等</li><li>墙面维修：墙漆修补、裂缝处理、壁纸修复等</li><li>小型改造：简易隔断、置物架安装、小型装修等</li></ul><p>服务特点：</p><ul><li>专业师傅：经验丰富，技术全面</li><li>快速响应：当天预约，快速上门</li><li>合理收费：先检查后报价，认可后施工</li><li>质量保障：维修后保修期内免费返修</li></ul>',\n    guarantees: [\n      {\n        icon: 'icon-quality',\n        title: '品质保障',\n        desc: '专业维修师傅，技术全面，经验丰富'\n      },\n      {\n        icon: 'icon-safe',\n        title: '安全保障',\n        desc: '规范操作，确保安全，损坏赔付'\n      },\n      {\n        icon: 'icon-time',\n        title: '保修服务',\n        desc: '维修后提供保修期，期内免费返修'\n      }\n    ],\n    provider: {\n      name: '磁县万能维修中心',\n      avatar: '/static/images/avatar.png',\n      type: '专业机构（7年经验）',\n      rating: 96,\n      isVerified: true\n    },\n    contact: {\n      name: '张师傅（维修主管）',\n      phone: '13765432109'\n    }\n  };\n};\n\n// 其他服务类型的数据加载方法可以根据需要添加\nconst loadMovingData = (id) => {\n  // 搬家拉货数据\n  serviceData.value = {\n    id: id || 'moving12345',\n    title: '专业搬家拉货服务',\n    price: '100-500元/次起',\n    tags: ['专业搬运', '全城服务', '可预约', '价格合理'],\n    type: '搬家拉货',\n    // ...其他搬家拉货相关数据\n  };\n};\n\nconst loadBeautyData = (id) => {\n  // 上门美容数据\n  serviceData.value = {\n    id: id || 'beauty12345',\n    title: '专业上门美容服务',\n    price: '80-300元/次起',\n    tags: ['专业美容', '上门服务', '可预约', '全城服务'],\n    type: '上门美容',\n    // ...其他上门美容相关数据\n  };\n};\n\nconst loadTutorData = (id) => {\n  // 上门家教数据\n  serviceData.value = {\n    id: id || 'tutor12345',\n    title: '专业上门家教服务',\n    price: '100-200元/小时起',\n    tags: ['专业教师', '上门授课', '可预约', '全城服务'],\n    type: '上门家教',\n    // ...其他上门家教相关数据\n  };\n};\n\nconst loadPetServiceData = (id) => {\n  // 宠物服务数据\n  serviceData.value = {\n    id: id || 'pet_service12345',\n    title: '专业宠物上门服务',\n    price: '80-300元/次起',\n    tags: ['宠物美容', '宠物医疗', '上门服务', '全城服务'],\n    type: '宠物服务',\n    // ...其他宠物服务相关数据\n  };\n};\n\nconst loadPlumbingData = (id) => {\n  // 上门疏通数据\n  serviceData.value = {\n    id: id || 'plumbing12345',\n    title: '专业上门疏通服务',\n    price: '80-200元/次起',\n    tags: ['管道疏通', '马桶疏通', '上门服务', '全城服务'],\n    type: '上门疏通',\n    // ...其他上门疏通相关数据\n  };\n};\n</script>\n\n<style>\n.service-detail-container {\n  min-height: 100vh;\n  background-color: #f5f7fa;\n  padding-bottom: 150rpx;\n  padding-top: 0; /* 移除顶部内边距，由导航栏控制 */\n}\n\n/* 自定义导航栏样式 */\n.custom-navbar {\n  background: linear-gradient(135deg, #0066FF, #0052CC);\n  height: 88rpx;\n  padding-top: 44px; /* 状态栏高度 */\n  display: flex;\n  align-items: center;\n  position: fixed; /* 改为固定定位 */\n  top: 0;\n  left: 0;\n  right: 0;\n  padding-bottom: 10rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n  z-index: 100; /* 提高z-index确保在最上层 */\n}\n\n.navbar-left {\n  width: 60px;\n  display: flex;\n  align-items: center;\n}\n\n.back-icon {\n  width: 20px;\n  height: 20px;\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 17px;\n  font-weight: 500;\n  color: #fff;\n}\n\n.navbar-right {\n  width: 60px;\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n}\n\n.service-detail-wrapper {\n  padding: 24rpx;\n  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */\n}\n\n/* 内容卡片基础样式 */\n.content-card {\n  background-color: #fff;\n  border-radius: 16rpx;\n  margin-bottom: 24rpx;\n  padding: 30rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n}\n\n/* 服务基本信息卡片 */\n.service-title-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.service-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.service-price {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #ff4d4f;\n}\n\n.service-meta {\n  margin-bottom: 24rpx;\n}\n\n.service-tag-group {\n  display: flex;\n  flex-wrap: wrap;\n  margin-bottom: 12rpx;\n}\n\n.service-tag {\n  font-size: 24rpx;\n  color: #1890ff;\n  background-color: rgba(24, 144, 255, 0.1);\n  padding: 4rpx 16rpx;\n  border-radius: 6rpx;\n  margin-right: 16rpx;\n  margin-bottom: 12rpx;\n}\n\n.service-publish-time {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 轮播图 */\n.service-swiper {\n  height: 400rpx;\n  border-radius: 12rpx;\n  overflow: hidden;\n  margin-bottom: 24rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\n}\n\n.service-image {\n  width: 100%;\n  height: 100%;\n}\n\n/* 基本信息 */\n.service-basic-info {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 24rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n  border: 1px solid rgba(24, 144, 255, 0.1);\n}\n\n.info-item {\n  width: 50%;\n  padding: 12rpx 24rpx;\n  box-sizing: border-box;\n}\n\n.info-label {\n  font-size: 26rpx;\n  color: #999;\n  margin-bottom: 8rpx;\n  display: block;\n}\n\n.info-value {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n/* 区块标题优化 - 添加蓝色竖线 */\n.section-title {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 20rpx;\n  position: relative;\n  padding-left: 16rpx;\n}\n\n.section-title::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 6rpx;\n  width: 6rpx;\n  height: 28rpx;\n  background-color: #1890ff;\n  border-radius: 3rpx;\n}\n\n/* 服务内容 */\n.content-list {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -12rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n  padding: 16rpx;\n}\n\n.content-item {\n  width: 33.33%;\n  padding: 12rpx;\n  box-sizing: border-box;\n  display: flex;\n  align-items: center;\n}\n\n.content-icon {\n  font-size: 32rpx;\n  color: #1890ff;\n  margin-right: 8rpx;\n}\n\n.content-text {\n  font-size: 26rpx;\n  color: #666;\n}\n\n/* 服务说明 */\n.desc-content {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.6;\n  padding: 20rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n  border: 1px solid rgba(24, 144, 255, 0.1);\n}\n\n/* 服务保障 */\n.guarantee-list {\n  display: flex;\n  flex-direction: column;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n  padding: 16rpx;\n}\n\n.guarantee-item {\n  display: flex;\n  align-items: flex-start;\n  padding: 16rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.guarantee-item:last-child {\n  border-bottom: none;\n}\n\n.guarantee-icon {\n  font-size: 40rpx;\n  color: #1890ff;\n  margin-right: 16rpx;\n}\n\n.guarantee-info {\n  flex: 1;\n}\n\n.guarantee-title {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.guarantee-desc {\n  font-size: 26rpx;\n  color: #666;\n}\n\n/* 服务商信息 */\n.provider-header {\n  display: flex;\n  align-items: center;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n  padding: 20rpx;\n}\n\n.provider-avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  overflow: hidden;\n  margin-right: 20rpx;\n  border: 2rpx solid #e8e8e8;\n}\n\n.provider-avatar image {\n  width: 100%;\n  height: 100%;\n}\n\n.provider-info {\n  flex: 1;\n}\n\n.provider-name {\n  font-size: 30rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.provider-meta {\n  display: flex;\n  align-items: center;\n}\n\n.provider-type, .provider-rating {\n  font-size: 24rpx;\n  color: #666;\n  margin-right: 16rpx;\n}\n\n/* 联系方式样式 - 电话显示为绿色，提示为黄色 */\n.contact-content {\n  padding: 20rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n}\n\n.contact-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.contact-label {\n  width: 120rpx;\n  font-size: 28rpx;\n  color: #888;\n}\n\n.contact-value {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333;\n}\n\n.contact-phone {\n  color: #52c41a; /* 修改为绿色 */\n  font-weight: 500;\n}\n\n.contact-tips {\n  display: flex;\n  align-items: center;\n  margin-top: 16rpx;\n  padding: 10rpx 16rpx;\n  background-color: rgba(255, 152, 0, 0.1);\n  border-radius: 8rpx;\n}\n\n.tips-icon {\n  font-size: 24rpx;\n  color: #ff9800; /* 黄色 */\n  margin-right: 8rpx;\n}\n\n.tips-text {\n  font-size: 24rpx;\n  color: #ff9800; /* 黄色 */\n}\n\n/* 举报卡片样式 */\n.report-card {\n  margin: 0 auto 24rpx;\n  padding: 0;\n  overflow: hidden;\n}\n\n.report-content {\n  display: flex;\n  align-items: center;\n  padding: 24rpx 30rpx;\n  background-color: #fff;\n  position: relative;\n}\n\n.report-icon {\n  width: 48rpx;\n  height: 48rpx;\n  margin-right: 20rpx;\n}\n\n.report-text-container {\n  flex: 1;\n}\n\n.report-title {\n  font-size: 28rpx;\n  color: #333;\n  line-height: 1.4;\n}\n\n.report-subtitle {\n  font-size: 24rpx;\n  color: #999;\n  margin-top: 4rpx;\n  line-height: 1.4;\n}\n\n.report-arrow {\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n  width: 40rpx;\n}\n\n.arrow-icon {\n  width: 16rpx;\n  height: 16rpx;\n  border-top: 2rpx solid #999;\n  border-right: 2rpx solid #999;\n  transform: rotate(45deg);\n}\n\n.arrow-icon::after {\n  content: '';\n  display: inline-block;\n  width: 16rpx;\n  height: 16rpx;\n  border-top: 2rpx solid #999;\n  border-right: 2rpx solid #999;\n  transform: rotate(45deg);\n}\n\n.report-content:active {\n  background-color: #f9f9f9;\n}\n\n/* 底部互动工具栏 */\n.interaction-toolbar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background-color: #fff;\n  padding: 10rpx 10rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-top: 1rpx solid #f0f0f0;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  height: 120rpx;\n  z-index: 100;\n}\n\n.toolbar-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 6rpx 0;\n  margin: 0 4rpx;\n}\n\n.toolbar-icon {\n  width: 44rpx;\n  height: 44rpx;\n  margin-bottom: 6rpx;\n}\n\n.toolbar-text {\n  font-size: 22rpx;\n  color: #666;\n}\n\n.share-button {\n  background: transparent;\n  border: none;\n  margin: 0;\n  padding: 0;\n  line-height: normal;\n  border-radius: 0;\n  flex: 1;\n}\n\n.share-button::after {\n  display: none;\n}\n\n.call-button {\n  flex: 3; /* 占据更多空间，原来是2，现在改为3让按钮更长 */\n  background: linear-gradient(135deg, #0052CC, #0066FF);\n  height: 90rpx;\n  margin: 0 0 0 10rpx;\n  border-radius: 45rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n}\n\n.call-button-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n}\n\n.call-text {\n  color: #fff;\n  font-size: 30rpx;\n  font-weight: bold;\n  line-height: 1.2;\n}\n\n.call-subtitle {\n  color: rgba(255, 255, 255, 0.9);\n  font-size: 20rpx;\n  line-height: 1.2;\n}\n\n/* 隐藏原来的底部操作栏 */\n.action-bar {\n  display: none;\n}\n\n/* 隐藏的分享按钮 */\n.hidden-share-btn {\n  position: absolute;\n  top: -9999rpx;\n  left: -9999rpx;\n  width: 0;\n  height: 0;\n  padding: 0;\n  margin: 0;\n  opacity: 0;\n}\n\n/* 悬浮海报按钮 */\n.float-poster-btn {\n  position: fixed;\n  right: 30rpx;\n  bottom: 200rpx;\n  width: 100rpx;\n  height: 100rpx;\n  background: rgba(240, 240, 240, 0.9);\n  border-radius: 50%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.08);\n  z-index: 90;\n  backdrop-filter: blur(10px);\n  -webkit-backdrop-filter: blur(10px);\n  border: 1rpx solid rgba(230, 230, 230, 0.6);\n  transition: all 0.2s ease;\n}\n\n.float-poster-btn:active {\n  transform: scale(0.95);\n  box-shadow: 0 3rpx 10rpx rgba(0,0,0,0.08);\n}\n\n.poster-icon {\n  width: 40rpx;\n  height: 40rpx;\n  margin-bottom: 4rpx;\n}\n\n.poster-text {\n  font-size: 20rpx;\n  color: #444;\n  line-height: 1;\n}\n\n/* 相关服务推荐样式 */\n.related-services-card {\n  margin-top: 12px;\n  background-color: #fff;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\n}\n\n.related-services-content {\n  padding: 0 16px 16px;\n  overflow: hidden;\n}\n\n/* 相关服务列表样式 */\n.related-services-list {\n  margin-bottom: 12px;\n}\n\n.related-service-item {\n  padding: 12px 0;\n  border-bottom: 1px solid #f5f5f5;\n}\n\n.related-service-item:last-child {\n  border-bottom: none;\n}\n\n.service-item-content {\n  display: flex;\n  align-items: center;\n}\n\n.service-item-left {\n  margin-right: 12px;\n}\n\n.provider-logo {\n  width: 40px;\n  height: 40px;\n  border-radius: 8px;\n  background-color: #f5f7fa;\n}\n\n.service-item-middle {\n  flex: 1;\n  overflow: hidden;\n}\n\n.service-item-title {\n  font-size: 15px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 4px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.service-item-provider {\n  font-size: 13px;\n  color: #666;\n  margin-bottom: 6px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.service-item-tags {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.service-item-tag {\n  font-size: 11px;\n  color: #1890ff;\n  background-color: rgba(24, 144, 255, 0.1);\n  padding: 2px 6px;\n  border-radius: 4px;\n  margin-right: 6px;\n}\n\n.service-item-tag-more {\n  font-size: 11px;\n  color: #999;\n}\n\n.service-item-right {\n  min-width: 80px;\n  text-align: right;\n}\n\n.service-item-price {\n  font-size: 15px;\n  font-weight: 500;\n  color: #ff5252;\n}\n\n/* 查看更多按钮样式 */\n.view-more-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 44px;\n  background-color: #f7f9fc;\n  border-radius: 8px;\n  margin-top: 8px;\n}\n\n.view-more-text {\n  font-size: 14px;\n  color: #1890ff;\n}\n\n.view-more-icon {\n  margin-left: 4px;\n  font-size: 12px;\n  color: #1890ff;\n}\n\n/* 空数据提示样式 */\n.empty-related-services {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 24px 0;\n}\n\n.empty-image {\n  width: 80px;\n  height: 80px;\n  margin-bottom: 12px;\n}\n\n.empty-text {\n  font-size: 14px;\n  color: #999;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/publish/home-service-detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni", "ctx", "MiniProgramPage"], "mappings": ";;;;;;AA2OA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAG9BC,kBAAAA,UAAU,MAAM;AACd,UAAI;AACF,cAAM,UAAUC,oBAAI;AACpB,wBAAgB,QAAQ,QAAQ,mBAAmB;AAAA,MACpD,SAAQ,GAAG;AACVA,2FAAc,aAAa,CAAC;AAAA,MAC7B;AAAA,IACH,CAAC;AAGD,UAAM,SAAS,MAAM;AACnBA,oBAAAA,MAAI,aAAa;AAAA,QACf,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,KAAK;AAAA,UACb,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,cAAc;AAChC,YAAM,OAAO,IAAI,KAAK,SAAS;AAC/B,aAAO,GAAG,KAAK,aAAa,CAAC,IAAI,KAAK,SAAS;AAAA,IACjD;AAGA,UAAM,cAAcF,cAAAA,IAAI,KAAK;AAC7B,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,iBAAiBA,cAAAA,IAAI,KAAK;AAGhC,UAAM,kBAAkBA,cAAAA,IAAI,CAAA,CAAE;AAG9B,UAAM,sBAAsB,MAAM;AAEhC,YAAM,cAAc,YAAY,MAAM;AACtC,YAAM,kBAAkB,eAAe,WAAW;AAGlD,iBAAW,MAAM;AAEf,gBAAO,iBAAe;AAAA,UACpB,KAAK;AACH,4BAAgB,QAAQ;AAAA,cACtB;AAAA,gBACE,IAAI;AAAA,gBACJ,OAAO;AAAA,gBACP,OAAO;AAAA,gBACP,cAAc;AAAA,gBACd,cAAc;AAAA,gBACd,MAAM,CAAC,UAAU,QAAQ,MAAM;AAAA,gBAC/B,MAAM;AAAA,cACP;AAAA,cACD;AAAA,gBACE,IAAI;AAAA,gBACJ,OAAO;AAAA,gBACP,OAAO;AAAA,gBACP,cAAc;AAAA,gBACd,cAAc;AAAA,gBACd,MAAM,CAAC,OAAO,OAAO,KAAK;AAAA,gBAC1B,MAAM;AAAA,cACP;AAAA,cACD;AAAA,gBACE,IAAI;AAAA,gBACJ,OAAO;AAAA,gBACP,OAAO;AAAA,gBACP,cAAc;AAAA,gBACd,cAAc;AAAA,gBACd,MAAM,CAAC,OAAO,OAAO,KAAK;AAAA,gBAC1B,MAAM;AAAA,cACP;AAAA,YACX;AACQ;AAAA,UACF,KAAK;AACH,4BAAgB,QAAQ;AAAA,cACtB;AAAA,gBACE,IAAI;AAAA,gBACJ,OAAO;AAAA,gBACP,OAAO;AAAA,gBACP,cAAc;AAAA,gBACd,cAAc;AAAA,gBACd,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,gBAC7B,MAAM;AAAA,cACP;AAAA,cACD;AAAA,gBACE,IAAI;AAAA,gBACJ,OAAO;AAAA,gBACP,OAAO;AAAA,gBACP,cAAc;AAAA,gBACd,cAAc;AAAA,gBACd,MAAM,CAAC,QAAQ,QAAQ,OAAO;AAAA,gBAC9B,MAAM;AAAA,cACP;AAAA,cACD;AAAA,gBACE,IAAI;AAAA,gBACJ,OAAO;AAAA,gBACP,OAAO;AAAA,gBACP,cAAc;AAAA,gBACd,cAAc;AAAA,gBACd,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,gBAC7B,MAAM;AAAA,cACP;AAAA,YACX;AACQ;AAAA,UACF,KAAK;AAAA,UACL;AACF,4BAAgB,QAAQ;AAAA,cACtB;AAAA,gBACE,IAAI;AAAA,gBACJ,OAAO;AAAA,gBACP,OAAO;AAAA,gBACP,cAAc;AAAA,gBACd,cAAc;AAAA,gBACV,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,gBAC7B,MAAM;AAAA,cACX;AAAA,cACD;AAAA,gBACE,IAAI;AAAA,gBACJ,OAAO;AAAA,gBACP,OAAO;AAAA,gBACP,cAAc;AAAA,gBACd,cAAc;AAAA,gBACV,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,gBAC7B,MAAM;AAAA,cACX;AAAA,cACD;AAAA,gBACE,IAAI;AAAA,gBACJ,OAAO;AAAA,gBACP,OAAO;AAAA,gBACP,cAAc;AAAA,gBACd,cAAc;AAAA,gBACV,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,gBAC7B,MAAM;AAAA,cACX;AAAA,YACP;AACQ;AAAA,QACH;AAAA,MACF,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,0BAA0B,CAAC,IAAI,SAAS;;AAE5C,YAAM,QAAQ;AACd,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,YAAU,iBAAY,UAAZ,mBAAmB,YAAW,CAAA;AAE9C,UAAI,OAAO,QAAQ,IAAI;AACrB;AAAA,MACD;AAGD,YAAM,cAAc,QAAQ,eAAe,YAAY,MAAM,IAAI;AAEjEE,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,yCAAyC,EAAE,SAAS,WAAW;AAAA,MACxE,CAAG;AAAA,IACH;AAGA,UAAM,wBAAwB,CAAC,MAAM;AACnC,UAAI;AAAG,UAAE;AAET,YAAM,kBAAkB,YAAY,MAAM,QAAQ;AAClD,YAAM,kBAAkB,eAAe,eAAe;AAGtDA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4CAA4C,eAAe,YAAY,mBAAmB,eAAe,CAAC;AAAA,MACnH,CAAG;AAAA,IACH;AAGA,UAAM,iBAAiB,CAAC,iBAAiB;AACvC,YAAM,cAAc;AAAA,QAClB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,MACZ;AAEE,aAAO,YAAY,YAAY,KAAK;AAAA,IACtC;AAEA,UAAM,cAAcF,cAAAA,IAAI;AAAA,MACtB,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM,CAAC,QAAQ,QAAQ,OAAO,QAAQ,MAAM;AAAA,MAC5C,aAAa,KAAK,IAAK,IAAG,QAAW;AAAA;AAAA,MACrC,QAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,UAAU;AAAA,QACR,EAAE,MAAM,sBAAsB,MAAM,aAAc;AAAA,QAClD,EAAE,MAAM,oBAAoB,MAAM,kBAAmB;AAAA,QACrD,EAAE,MAAM,mBAAmB,MAAM,iBAAkB;AAAA,QACnD,EAAE,MAAM,oBAAoB,MAAM,iBAAkB;AAAA,QACpD,EAAE,MAAM,mBAAmB,MAAM,cAAe;AAAA,QAChD,EAAE,MAAM,iBAAiB,MAAM,aAAc;AAAA,QAC7C,EAAE,MAAM,iBAAiB,MAAM,YAAa;AAAA,QAC5C,EAAE,MAAM,mBAAmB,MAAM,eAAgB;AAAA,MAClD;AAAA,MACD,aAAa;AAAA,MACb,YAAY;AAAA,QACV;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,QACP;AAAA,MACF;AAAA,MACD,UAAU;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,YAAY;AAAA,MACb;AAAA,MACD,SAAS;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACH,CAAC;AAGD,UAAM,gBAAgB,MAAM;AAC1B,kBAAY,QAAQ,CAAC,YAAY;AACjC,UAAI,YAAY,OAAO;AACrBE,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAWA,UAAM,YAAY,MAAM;AACtBA,oBAAAA,MAAI,cAAc;AAAA,QAChB,aAAa,YAAY,MAAM,QAAQ;AAAA,QACvC,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AAErB,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,UAAI,CAAC,UAAU;AACbA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AACfA,4BAAAA,MAAI,WAAW;AAAA,gBACb,KAAK,iCAAiC,mBAAmB,yCAAyC,YAAY,MAAM,EAAE,EAAE;AAAA,cACpI,CAAW;AAAA,YACF;AAAA,UACF;AAAA,QACP,CAAK;AACD;AAAA,MACD;AAGDA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,6BAA6B,YAAY,MAAM,SAAS,EAAE,eAAe,YAAY,MAAM,SAAS,IAAI,iBAAiB,mBAAmB,YAAY,MAAM,SAAS,MAAM,CAAC;AAAA,MACvL,CAAG;AAAA,IACH;AAGA,UAAM,qBAAqB,MAAM;AAC/BA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAGD,YAAM,aAAa;AAAA,QACjB,OAAO,YAAY,MAAM;AAAA,QACzB,OAAO,YAAY,MAAM;AAAA,QACzB,MAAM,YAAY,MAAM;AAAA,QACxB,SAAS,YAAY,MAAM;AAAA,QAC3B,OAAO,YAAY,MAAM,UAAU,YAAY,MAAM,QAAQ,QAAQ;AAAA,QACrE,aAAa,YAAY,MAAM,cAAc,YAAY,MAAM,YAAY,UAAU,GAAG,EAAE,IAAI,QAAQ;AAAA,QACtG,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,SAAS,YAAY,MAAM,SAAS;AAAA,MACxC;AAgBE,YAAM,MAAMA,cAAAA,MAAI,oBAAoB,cAAc;AAGlD,UAAI,KAAI;AACR,UAAI,UAAU,WAAW,SAAS,GAAG,GAAG,KAAK,GAAG;AAEhD,UAAI,aAAa,qBAAqB;AACtC,UAAI,SAAS,GAAG,GAAG,KAAK,GAAG;AAC3B,UAAI,QAAO;AAGX,UAAI,KAAI;AACR,UAAI,aAAa,SAAS;AAC1B,UAAI,SAAS,IAAI,KAAK,KAAK,GAAG;AAC9B,UAAI,QAAO;AAGX,UAAI,KAAI;AACR,UAAI,UAAS;AACb,UAAI,IAAI,KAAK,KAAK,IAAI,GAAG,IAAI,KAAK,EAAE;AACpC,UAAI,aAAa,SAAS;AAC1B,UAAI,KAAI;AAER,UAAI,KAAI;AACR,UAAI,UAAU,WAAW,MAAM,KAAK,KAAK,KAAK,GAAG;AACjD,UAAI,QAAO;AAGX,UAAI,aAAa,SAAS;AAC1B,UAAI,YAAY,EAAE;AAClB,UAAI,aAAa,QAAQ;AACzB,UAAI,SAAS,WAAW,OAAO,KAAK,GAAG;AAGvC,UAAI,aAAa,SAAS;AAC1B,UAAI,YAAY,EAAE;AAClB,UAAI,SAAS,WAAW,OAAO,KAAK,GAAG;AAGvC,UAAI,UAAS;AACb,UAAI,eAAe,SAAS;AAC5B,UAAI,aAAa,CAAC;AAClB,UAAI,OAAO,KAAK,GAAG;AACnB,UAAI,OAAO,KAAK,GAAG;AACnB,UAAI,OAAM;AAGV,UAAI,aAAa,SAAS;AAC1B,UAAI,YAAY,EAAE;AAClB,UAAI,aAAa,MAAM;AACvB,UAAI,SAAS,WAAW,WAAW,MAAM,IAAI,GAAG;AAGhD,UAAI,SAAS,WAAW,WAAW,SAAS,IAAI,GAAG;AAGnD,YAAM,WAAW,CAACC,MAAK,MAAM,GAAG,GAAG,UAAU,eAAe;AAC1D,YAAI,KAAK,WAAW;AAAG;AAEvB,cAAM,QAAQ,KAAK,MAAM,EAAE;AAC3B,YAAI,OAAO;AACX,YAAI,WAAW;AACf,YAAI,YAAY;AAEhB,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,sBAAY,MAAM,CAAC;AACnB,gBAAM,UAAUA,KAAI,YAAY,QAAQ;AACxC,gBAAM,YAAY,QAAQ;AAE1B,cAAI,YAAY,YAAY,IAAI,GAAG;AACjC,YAAAA,KAAI,SAAS,MAAM,GAAG,IAAK,YAAY,UAAW;AAClD,mBAAO,MAAM,CAAC;AACd,uBAAW,MAAM,CAAC;AAClB;AAEA,gBAAI,aAAa,GAAG;AAClB,sBAAQ;AACR,cAAAA,KAAI,SAAS,MAAM,GAAG,IAAK,YAAY,UAAW;AAClD;AAAA,YACD;AAAA,UACT,OAAa;AACL,mBAAO;AAAA,UACR;AAAA,QACF;AAED,YAAI,YAAY,GAAG;AACjB,UAAAA,KAAI,SAAS,MAAM,GAAG,IAAK,YAAY,UAAW;AAAA,QACnD;AAAA,MACL;AAGE,UAAI,aAAa,SAAS;AAC1B,eAAS,KAAK,WAAW,aAAa,IAAI,KAAK,KAAK,EAAE;AAGtD,UAAI,WAAW,OAAO;AACpB,YAAI,SAAS,WAAW,WAAW,OAAO,IAAI,GAAG;AAAA,MAClD;AAGD,UAAI,UAAU,WAAW,QAAQ,KAAK,KAAK,KAAK,GAAG;AAGnD,UAAI,aAAa,SAAS;AAC1B,UAAI,YAAY,EAAE;AAClB,UAAI,aAAa,QAAQ;AACzB,UAAI,SAAS,eAAe,KAAK,GAAG;AAGpC,UAAI,aAAa,SAAS;AAC1B,UAAI,YAAY,EAAE;AAClB,UAAI,SAAS,eAAe,KAAK,GAAG;AAGpC,UAAI,KAAK,OAAO,MAAM;AACpB,mBAAW,MAAM;AAEfD,wBAAAA,MAAI,qBAAqB;AAAA,YACvB,UAAU;AAAA,YACV,SAAS,CAAC,QAAQ;AAChBA,4BAAG,MAAC,YAAW;AACf,8BAAgB,IAAI,YAAY;AAAA,YACjC;AAAA,YACD,MAAM,CAAC,QAAQ;AACbA,4BAAA,MAAA,MAAA,SAAA,gDAAc,UAAU,GAAG;AAC3BA,4BAAG,MAAC,YAAW;AACfA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AAAA,QACF,GAAE,GAAG;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,kBAAkB,CAAC,eAAe;AACtC,sBAAgB,QAAQ;AACxB,qBAAe,QAAQ;AAEvBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,8BAAkB,UAAU;AAAA,UACpC,OAAa;AAELA,0BAAAA,MAAI,aAAa;AAAA,cACf,MAAM,CAAC,UAAU;AAAA,cACjB,SAAS;AAAA,YACnB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,CAAC,eAAe;AACxCA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAEDA,oBAAAA,MAAI,uBAAuB;AAAA,QACzB,UAAU;AAAA,QACV,SAAS,MAAM;AACbA,wBAAG,MAAC,YAAW;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAG,MAAC,YAAW;AACfA,wBAAA,MAAA,MAAA,SAAA,gDAAc,QAAQ,GAAG;AAEzB,cAAI,IAAI,OAAO,QAAQ,WAAW,IAAI,IAAI;AACxCA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,SAAS;AAAA,cACT,aAAa;AAAA,cACb,SAAS,CAAC,QAAQ;AAChB,oBAAI,IAAI,SAAS;AACfA,gCAAG,MAAC,YAAW;AAAA,gBAChB;AAAA,cACF;AAAA,YACX,CAAS;AAAA,UACT,OAAa;AACLA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,MAAM;AAC9BA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QACzD,SAAS,CAAC,QAAQ;AAChB,gBAAM,oBAAoB,IAAI;AAC9B,gBAAM,UAAU,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAC/D,gCAAsB,QAAQ,iBAAiB,CAAC;AAAA,QACjD;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,wBAAwB,CAAC,WAAW;AAExC,YAAM,WAAWA,cAAG,MAAC,eAAe,OAAO,KAAK;AAChD,UAAI,CAAC,UAAU;AACbA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,UACb,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AACfA,4BAAAA,MAAI,WAAW;AAAA,gBACb,KAAK;AAAA,cACjB,CAAW;AAAA,YACF;AAAA,UACF;AAAA,QACP,CAAK;AACD;AAAA,MACD;AAEDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,cAAc,MAAM;AAAA,QAC7B,aAAa;AAAA,QACb,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,yBAAuB;AAAA,UACxB;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,eAAe,CAAC,QAAQ,YAAY;AACxCA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAGC,iBAAW,MAAM;AACjBA,sBAAG,MAAC,YAAW;AAEfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MAeA,GAAE,IAAI;AAAA,IACX;AAGAD,kBAAAA,UAAU,MAAM;AAEd,YAAM,QAAQ;AACd,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,UAAU,YAAY,WAAW;AAGvC,YAAM,KAAK,QAAQ,MAAM;AACzB,YAAM,cAAc,QAAQ,QAAQ;AAGpC,sBAAgB,IAAI,WAAW;AAG/B;IACF,CAAC;AAGD,UAAM,kBAAkB,CAAC,IAAI,gBAAgB;AAE3CC,uFAAY,WAAW,aAAa,SAAS,MAAM,MAAM;AAEzD,cAAO,aAAW;AAAA,QAChB,KAAK;AACH,+BAAqB,EAAE;AACvB;AAAA,QACF,KAAK;AACH,4BAAkB,EAAE;AACpB;AAAA,QACF,KAAK;AACH,yBAAe,EAAE;AACjB;AAAA,QACF,KAAK;AACH,yBAAe,EAAE;AACjB;AAAA,QACF,KAAK;AACH,yBAAe,EAAE;AACjB;AAAA,QACF,KAAK;AACH,wBAAc,EAAE;AAChB;AAAA,QACF,KAAK;AACH,6BAAmB,EAAE;AACrB;AAAA,QACF,KAAK;AACH,2BAAiB,EAAE;AACnB;AAAA,MAMH;AAGDA,oBAAAA,MAAI,sBAAsB;AAAA,QACxB,OAAO,YAAY,MAAM,SAAS;AAAA,MACtC,CAAG;AAAA,IACH;AAGA,UAAM,uBAAuB,CAAC,OAAO;AACnC,kBAAY,QAAQ;AAAA,QAClB,IAAI,MAAM;AAAA,QACV,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM,CAAC,QAAQ,QAAQ,OAAO,MAAM;AAAA,QACpC,aAAa,KAAK,IAAK,IAAG,QAAW;AAAA;AAAA,QACrC,QAAQ;AAAA,UACN;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,QACD,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,UAAU;AAAA,UACR,EAAE,MAAM,iBAAiB,MAAM,iBAAkB;AAAA,UACjD,EAAE,MAAM,gBAAgB,MAAM,iBAAkB;AAAA,UAChD,EAAE,MAAM,gBAAgB,MAAM,aAAc;AAAA,UAC5C,EAAE,MAAM,gBAAgB,MAAM,gBAAiB;AAAA,UAC/C,EAAE,MAAM,kBAAkB,MAAM,YAAa;AAAA,UAC7C,EAAE,MAAM,iBAAiB,MAAM,gBAAiB;AAAA,QACjD;AAAA,QACD,aAAa;AAAA,QACb,YAAY;AAAA,UACV;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,UACP;AAAA,UACD;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,UACP;AAAA,UACD;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,UACP;AAAA,QACF;AAAA,QACD,UAAU;AAAA,UACR,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,YAAY;AAAA,QACb;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACL;AAAA,IACA;AAGA,UAAM,oBAAoB,CAAC,OAAO;AAChC,kBAAY,QAAQ;AAAA,QAClB,IAAI,MAAM;AAAA,QACV,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM,CAAC,UAAU,QAAQ,QAAQ,MAAM;AAAA,QACvC,aAAa,KAAK,IAAK,IAAG,QAAW;AAAA;AAAA,QACrC,QAAQ;AAAA,UACN;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,QACD,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,UAAU;AAAA,UACR,EAAE,MAAM,kBAAkB,MAAM,cAAe;AAAA,UAC/C,EAAE,MAAM,iBAAiB,MAAM,YAAa;AAAA,UAC5C,EAAE,MAAM,eAAe,MAAM,cAAe;AAAA,UAC5C,EAAE,MAAM,eAAe,MAAM,WAAY;AAAA,UACzC,EAAE,MAAM,cAAc,MAAM,eAAgB;AAAA,UAC5C,EAAE,MAAM,kBAAkB,MAAM,aAAc;AAAA,QAC/C;AAAA,QACD,aAAa;AAAA,QACb,YAAY;AAAA,UACV;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,UACP;AAAA,UACD;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,UACP;AAAA,UACD;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,UACP;AAAA,QACF;AAAA,QACD,UAAU;AAAA,UACR,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,YAAY;AAAA,QACb;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACL;AAAA,IACA;AAGA,UAAM,iBAAiB,CAAC,OAAO;AAC7B,kBAAY,QAAQ;AAAA,QAClB,IAAI,MAAM;AAAA,QACV,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM,CAAC,QAAQ,QAAQ,OAAO,MAAM;AAAA,QACpC,aAAa,KAAK,IAAK,IAAG,QAAW;AAAA;AAAA,QACrC,QAAQ;AAAA,UACN;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,QACD,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,UAAU;AAAA,UACR,EAAE,MAAM,gBAAgB,MAAM,sBAAuB;AAAA,UACrD,EAAE,MAAM,gBAAgB,MAAM,iBAAkB;AAAA,UAChD,EAAE,MAAM,iBAAiB,MAAM,iBAAkB;AAAA,UACjD,EAAE,MAAM,gBAAgB,MAAM,YAAa;AAAA,UAC3C,EAAE,MAAM,gBAAgB,MAAM,YAAa;AAAA,UAC3C,EAAE,MAAM,gBAAgB,MAAM,kBAAmB;AAAA,QAClD;AAAA,QACD,aAAa;AAAA,QACb,YAAY;AAAA,UACV;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,UACP;AAAA,UACD;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,UACP;AAAA,UACD;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,UACP;AAAA,QACF;AAAA,QACD,UAAU;AAAA,UACR,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,YAAY;AAAA,QACb;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACL;AAAA,IACA;AAGA,UAAM,iBAAiB,CAAC,OAAO;AAE7B,kBAAY,QAAQ;AAAA,QAClB,IAAI,MAAM;AAAA,QACV,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM,CAAC,QAAQ,QAAQ,OAAO,MAAM;AAAA,QACpC,MAAM;AAAA;AAAA,MAEV;AAAA,IACA;AAEA,UAAM,iBAAiB,CAAC,OAAO;AAE7B,kBAAY,QAAQ;AAAA,QAClB,IAAI,MAAM;AAAA,QACV,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM,CAAC,QAAQ,QAAQ,OAAO,MAAM;AAAA,QACpC,MAAM;AAAA;AAAA,MAEV;AAAA,IACA;AAEA,UAAM,gBAAgB,CAAC,OAAO;AAE5B,kBAAY,QAAQ;AAAA,QAClB,IAAI,MAAM;AAAA,QACV,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM,CAAC,QAAQ,QAAQ,OAAO,MAAM;AAAA,QACpC,MAAM;AAAA;AAAA,MAEV;AAAA,IACA;AAEA,UAAM,qBAAqB,CAAC,OAAO;AAEjC,kBAAY,QAAQ;AAAA,QAClB,IAAI,MAAM;AAAA,QACV,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM,CAAC,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QACrC,MAAM;AAAA;AAAA,MAEV;AAAA,IACA;AAEA,UAAM,mBAAmB,CAAC,OAAO;AAE/B,kBAAY,QAAQ;AAAA,QAClB,IAAI,MAAM;AAAA,QACV,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM,CAAC,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QACrC,MAAM;AAAA;AAAA,MAEV;AAAA,IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtoCA,GAAG,WAAWE,SAAe;"}