<!-- 创建中 -->
<template>
  <view class="create-package-items-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">拼团活动</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 步骤指示器 -->
    <view class="step-indicator">
      <view class="step-progress">
        <view class="step-progress-bar" style="width: 80%"></view>
      </view>
      <view class="step-text">步骤 4/5</view>
    </view>
    
    <!-- 页面内容 -->
    <scroll-view scroll-y class="page-content">
      <view class="page-title">添加套餐内容</view>
      <view class="page-subtitle">请添加团购套餐包含的商品或服务</view>
      
      <!-- 套餐内容列表 -->
      <view class="package-items">
        <view v-for="(item, index) in packageItems" :key="index" class="package-item">
          <view class="item-header">
            <text class="item-title">套餐项 {{index + 1}}</text>
            <view class="item-actions">
              <view class="action-btn edit" @tap="editItem(index)">编辑</view>
              <view class="action-btn delete" @tap="deleteItem(index)">删除</view>
            </view>
          </view>
          
          <view class="item-content">
            <view class="item-info">
              <text class="info-label">名称:</text>
              <text class="info-value">{{item.name}}</text>
            </view>
            
            <view class="item-info">
              <text class="info-label">数量:</text>
              <text class="info-value">{{item.quantity}} {{item.unit}}</text>
            </view>
            
            <view class="item-info">
              <text class="info-label">原价:</text>
              <text class="info-value">¥{{item.price}}</text>
            </view>
            
            <view class="item-info" v-if="item.description">
              <text class="info-label">描述:</text>
              <text class="info-value">{{item.description}}</text>
            </view>
          </view>
        </view>
        
        <!-- 添加按钮 -->
        <view class="add-item-btn" @tap="showAddItemModal">
          <view class="add-icon">+</view>
          <text class="add-text">添加套餐项</text>
        </view>
      </view>
      
      <!-- 套餐总价值预览 -->
      <view class="total-value-preview">
        <view class="preview-header">套餐总价值</view>
        
        <view class="preview-content">
          <view class="preview-item">
            <text class="preview-label">套餐项数量</text>
            <text class="preview-value">{{packageItems.length}}项</text>
          </view>
          
          <view class="preview-item">
            <text class="preview-label">套餐原价总值</text>
            <text class="preview-value">¥{{calculateTotalValue()}}</text>
          </view>
          
          <view class="preview-item">
            <text class="preview-label">拼团价</text>
            <text class="preview-value group-price">¥{{groupPrice}}</text>
          </view>
          
          <view class="preview-item">
            <text class="preview-label">折扣率</text>
            <text class="preview-value discount">{{calculateDiscount()}}折</text>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部按钮 -->
    <view class="footer-buttons">
      <button class="btn btn-secondary" @click="goBack">上一步</button>
      <button class="btn btn-primary" @click="nextStep">下一步</button>
    </view>
    
    <!-- 添加/编辑套餐项弹窗 -->
    <view class="modal" v-if="showModal">
      <view class="modal-mask" @tap="hideModal"></view>
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">{{isEditing ? '编辑套餐项' : '添加套餐项'}}</text>
          <view class="modal-close" @tap="hideModal">×</view>
        </view>
        
        <view class="modal-body">
          <view class="form-item">
            <text class="form-label">名称 <text class="required">*</text></text>
            <input class="form-input" type="text" v-model="currentItem.name" placeholder="请输入套餐项名称" maxlength="30" />
          </view>
          
          <view class="form-item">
            <text class="form-label">数量 <text class="required">*</text></text>
            <view class="quantity-input">
              <input class="form-input" type="number" v-model="currentItem.quantity" placeholder="请输入数量" />
              <input class="unit-input" type="text" v-model="currentItem.unit" placeholder="单位" maxlength="4" />
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">原价 <text class="required">*</text></text>
            <view class="price-input-wrapper">
              <text class="price-symbol">¥</text>
              <input class="form-input" type="digit" v-model="currentItem.price" placeholder="请输入原价" />
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">描述</text>
            <textarea class="form-textarea" v-model="currentItem.description" placeholder="请输入套餐项描述" maxlength="100"></textarea>
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="modal-btn cancel" @tap="hideModal">取消</button>
          <button class="modal-btn confirm" @tap="confirmItem">确定</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      packageItems: [],
      showModal: false,
      isEditing: false,
      editIndex: -1,
      currentItem: {
        name: '',
        quantity: 1,
        unit: '份',
        price: '',
        description: ''
      },
      groupPrice: '0.00'
    }
  },
  onLoad() {
    // 尝试从本地存储获取之前保存的数据
    try {
      const savedItems = uni.getStorageSync('packageItems');
      if (savedItems) {
        this.packageItems = JSON.parse(savedItems);
      }
      
      const priceInfo = uni.getStorageSync('packagePriceInfo');
      if (priceInfo) {
        const parsedPriceInfo = JSON.parse(priceInfo);
        this.groupPrice = parsedPriceInfo.groupPrice || '0.00';
      }
    } catch (e) {
      console.error('读取本地存储失败:', e);
    }
  },
  methods: {
    goBack() {
      // 保存当前页面数据
      this.saveData();
      uni.navigateBack();
    },
    nextStep() {
      // 表单验证
      if (this.packageItems.length === 0) {
        uni.showToast({
          title: '请至少添加一个套餐项',
          icon: 'none'
        });
        return;
      }
      
      // 保存数据并跳转到下一步
      this.saveData();
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-confirm'
      });
    },
    saveData() {
      // 保存当前页面数据到本地存储
      try {
        uni.setStorageSync('packageItems', JSON.stringify(this.packageItems));
      } catch (e) {
        console.error('保存数据失败:', e);
      }
    },
    calculateTotalValue() {
      let total = 0;
      this.packageItems.forEach(item => {
        total += parseFloat(item.price) * parseFloat(item.quantity);
      });
      return total.toFixed(2);
    },
    calculateDiscount() {
      const totalValue = parseFloat(this.calculateTotalValue());
      if (totalValue <= 0) return '10.0';
      
      const groupPrice = parseFloat(this.groupPrice);
      const discount = (groupPrice / totalValue) * 10;
      return discount.toFixed(1);
    },
    showAddItemModal() {
      this.isEditing = false;
      this.editIndex = -1;
      this.currentItem = {
        name: '',
        quantity: 1,
        unit: '份',
        price: '',
        description: ''
      };
      this.showModal = true;
    },
    editItem(index) {
      this.isEditing = true;
      this.editIndex = index;
      this.currentItem = JSON.parse(JSON.stringify(this.packageItems[index]));
      this.showModal = true;
    },
    deleteItem(index) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这个套餐项吗？',
        success: (res) => {
          if (res.confirm) {
            this.packageItems.splice(index, 1);
          }
        }
      });
    },
    hideModal() {
      this.showModal = false;
    },
    confirmItem() {
      // 表单验证
      if (!this.currentItem.name) {
        uni.showToast({
          title: '请输入套餐项名称',
          icon: 'none'
        });
        return;
      }
      
      if (!this.currentItem.quantity || parseFloat(this.currentItem.quantity) <= 0) {
        uni.showToast({
          title: '请输入有效数量',
          icon: 'none'
        });
        return;
      }
      
      if (!this.currentItem.unit) {
        uni.showToast({
          title: '请输入单位',
          icon: 'none'
        });
        return;
      }
      
      if (!this.currentItem.price || parseFloat(this.currentItem.price) <= 0) {
        uni.showToast({
          title: '请输入有效原价',
          icon: 'none'
        });
        return;
      }
      
      if (this.isEditing) {
        // 编辑现有项
        this.packageItems[this.editIndex] = JSON.parse(JSON.stringify(this.currentItem));
      } else {
        // 添加新项
        this.packageItems.push(JSON.parse(JSON.stringify(this.currentItem)));
      }
      
      this.hideModal();
    },
    showHelp() {
      uni.showToast({
        title: '帮助信息',
        icon: 'none'
      });
    }
  }
}
</script>

<style lang="scss">
.create-package-items-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  display: flex;
  flex-direction: column;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #fff;
}

/* 步骤指示器 */
.step-indicator {
  padding: 15px;
  background: #FFFFFF;
}

.step-progress {
  height: 4px;
  background-color: #EBEDF5;
  border-radius: 2px;
  margin-bottom: 5px;
  position: relative;
}

.step-progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #9040FF, #5E35B1);
  border-radius: 2px;
}

.step-text {
  font-size: 12px;
  color: #999;
  text-align: right;
}

/* 页面内容 */
.page-content {
  flex: 1;
  padding: 20px 15px;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.page-subtitle {
  font-size: 14px;
  color: #999;
  margin-bottom: 20px;
}

/* 套餐项样式 */
.package-items {
  margin-bottom: 20px;
}

.package-item {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px dashed #EBEDF5;
}

.item-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.item-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  padding: 5px 10px;
  font-size: 12px;
  border-radius: 15px;
}

.action-btn.edit {
  background: #F0F7FF;
  color: #007AFF;
}

.action-btn.delete {
  background: #FFF0F0;
  color: #FF3B30;
}

.item-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.item-info {
  display: flex;
  align-items: flex-start;
}

.info-label {
  width: 60px;
  font-size: 14px;
  color: #666;
}

.info-value {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.add-item-btn {
  background: #FFFFFF;
  border: 1px dashed #CCCCCC;
  border-radius: 12px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.add-icon {
  width: 30px;
  height: 30px;
  background: #F5F7FA;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #9040FF;
  margin-bottom: 5px;
}

.add-text {
  font-size: 14px;
  color: #9040FF;
}

/* 总价值预览 */
.total-value-preview {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.preview-header {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px dashed #EBEDF5;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-label {
  font-size: 14px;
  color: #666;
}

.preview-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.preview-value.group-price {
  color: #FF3B30;
}

.preview-value.discount {
  color: #34C759;
}

/* 底部按钮 */
.footer-buttons {
  padding: 15px;
  background: #FFFFFF;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  gap: 15px;
}

.btn {
  flex: 1;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #FFFFFF;
}

.btn-secondary {
  background: #F5F7FA;
  color: #666;
  border: 1px solid #EBEDF5;
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #FFFFFF;
  border-radius: 20px 20px 0 0;
  padding: 20px;
  transform: translateY(0);
  transition: transform 0.3s;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #999;
}

.modal-body {
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  gap: 15px;
}

.modal-btn {
  flex: 1;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  border: none;
}

.modal-btn.cancel {
  background: #F5F7FA;
  color: #666;
  border: 1px solid #EBEDF5;
}

.modal-btn.confirm {
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #FFFFFF;
}

/* 表单样式 */
.form-item {
  margin-bottom: 20px;
}

.form-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

.required {
  color: #FF3B30;
}

.form-input {
  width: 100%;
  height: 45px;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 14px;
  color: #333;
  border: 1px solid #EBEDF5;
}

.form-textarea {
  width: 100%;
  height: 100px;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  color: #333;
  border: 1px solid #EBEDF5;
}

.quantity-input {
  display: flex;
  gap: 10px;
}

.unit-input {
  width: 80px;
  height: 45px;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 14px;
  color: #333;
  border: 1px solid #EBEDF5;
}

.price-input-wrapper {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 8px;
  border: 1px solid #EBEDF5;
  padding: 0 12px;
}

.price-symbol {
  font-size: 14px;
  color: #333;
  margin-right: 5px;
}
</style>
