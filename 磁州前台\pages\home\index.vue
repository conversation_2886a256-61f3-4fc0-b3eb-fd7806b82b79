// 引入红包相关组件
import { RedPacketPopup } from '@/components/RedPacket';
import { getRedPacketDetail, getHotRedPackets } from '@/utils/redPacket.js';
import { formatTime, formatAmount } from '@/utils/format.js';

export default {
  components: {
    RedPacketPopup
  },
  data() {
    return {
      currentRedPacket: null, // 当前选中的红包
      showRedPacketPopup: false, // 是否显示红包弹窗
      redPacketRecords: [], // 红包领取记录
      redPacketPopupStep: 'grab', // 红包弹窗步骤
      hotRedPackets: [],
    };
  },
  created() {
    this.loadHotRedPackets();
  },
  methods: {
    // 加载热门红包
    async loadHotRedPackets() {
      try {
        const res = await getHotRedPackets();
        this.hotRedPackets = res.list;
      } catch (err) {
        console.error('加载热门红包失败:', err);
      }
    },
    
    // 处理从子组件传递的红包抢取事件
    handleRedPacketGrab(redPacket) {
      // 设置当前红包
      this.currentRedPacket = redPacket;
      this.redPacketPopupStep = 'grab';
      this.showRedPacketPopup = true;
    },
    
    // 处理从子组件传递的红包查看事件
    handleRedPacketView(redPacket) {
      // 设置当前红包
      this.currentRedPacket = redPacket;
      this.redPacketPopupStep = 'detail';
      this.showRedPacketPopup = true;
      
      // 获取红包记录
      // 这里需要调用获取红包记录的接口
      this.redPacketRecords = []; // 模拟数据
    },
    
    // 抢红包成功
    handleGrabSuccess(result) {
      // 显示抢到的金额
      uni.showToast({
        title: `抢到${formatAmount(result.amount)}元红包`,
        icon: 'none'
      });
      
      // 刷新页面数据
      this.refreshData();
    },
    
    // 处理红包点击
    handleRedPacketClick(item) {
      this.currentRedPacket = item;
      this.redPacketPopupStep = 'detail';
      this.showRedPacketPopup = true;
      
      // 获取红包记录
      this.fetchRedPacketRecords(item.id);
    },
    
    // 获取红包记录
    async fetchRedPacketRecords(id) {
      try {
        const res = await getRedPacketDetail(id);
        this.redPacketRecords = res.records || [];
      } catch (err) {
        console.error('获取红包记录失败:', err);
      }
    },
    
    // 跳转到红包列表
    navigateToRedPacketList() {
      uni.navigateTo({
        url: '/pages/red-packet/list'
      });
    },
    
    // 格式化剩余时间
    formatRemainTime(expireTime) {
      const now = Date.now();
      const remain = expireTime - now;
      
      if (remain <= 0) return '已过期';
      
      const hours = Math.floor(remain / (60 * 60 * 1000));
      const minutes = Math.floor((remain % (60 * 60 * 1000)) / (60 * 1000));
      
      if (hours > 0) {
        return `剩余${hours}小时${minutes}分钟`;
      } else {
        return `剩余${minutes}分钟`;
      }
    },
    
    // 格式化时间
    formatTime,
    
    // 格式化金额
    formatAmount
  }
} 