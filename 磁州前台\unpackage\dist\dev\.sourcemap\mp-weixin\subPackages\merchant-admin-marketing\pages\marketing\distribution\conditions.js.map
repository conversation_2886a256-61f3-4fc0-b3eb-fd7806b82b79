{"version": 3, "file": "conditions.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/distribution/conditions.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xkaXN0cmlidXRpb25cY29uZGl0aW9ucy52dWU"], "sourcesContent": ["<template>\n  <view class=\"conditions-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">成为分销员条件</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 条件选择卡片 -->\n    <view class=\"conditions-card\">\n      <view class=\"condition-item\" @click=\"selectCondition('none')\">\n        <view class=\"radio-button\" :class=\"{ active: selectedCondition === 'none' }\">\n          <view class=\"radio-inner\" v-if=\"selectedCondition === 'none'\"></view>\n        </view>\n        <view class=\"condition-content\">\n          <text class=\"condition-title\">无条件</text>\n          <text class=\"condition-desc\">任何用户都可以成为分销员</text>\n        </view>\n      </view>\n      \n      <view class=\"condition-item\" @click=\"selectCondition('apply')\">\n        <view class=\"radio-button\" :class=\"{ active: selectedCondition === 'apply' }\">\n          <view class=\"radio-inner\" v-if=\"selectedCondition === 'apply'\"></view>\n        </view>\n        <view class=\"condition-content\">\n          <text class=\"condition-title\">申请成为分销员</text>\n          <text class=\"condition-desc\">用户需要提交申请，审核通过后成为分销员</text>\n        </view>\n      </view>\n      \n      <view class=\"condition-item\" @click=\"selectCondition('purchase')\">\n        <view class=\"radio-button\" :class=\"{ active: selectedCondition === 'purchase' }\">\n          <view class=\"radio-inner\" v-if=\"selectedCondition === 'purchase'\"></view>\n        </view>\n        <view class=\"condition-content\">\n          <text class=\"condition-title\">购买商品并申请</text>\n          <text class=\"condition-desc\">用户需要购买指定商品并提交申请</text>\n        </view>\n      </view>\n      \n      <view class=\"condition-item\" @click=\"selectCondition('invite')\">\n        <view class=\"radio-button\" :class=\"{ active: selectedCondition === 'invite' }\">\n          <view class=\"radio-inner\" v-if=\"selectedCondition === 'invite'\"></view>\n        </view>\n        <view class=\"condition-content\">\n          <text class=\"condition-title\">邀请成为分销员</text>\n          <text class=\"condition-desc\">现有分销员邀请新用户才能成为分销员</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 附加条件卡片 -->\n    <view class=\"additional-card\" v-if=\"selectedCondition === 'purchase'\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">购买商品设置</text>\n      </view>\n      \n      <view class=\"form-item\">\n        <text class=\"form-label\">购买金额要求</text>\n        <view class=\"form-input-group\">\n          <text class=\"input-prefix\">¥</text>\n          <input type=\"digit\" v-model=\"purchaseAmount\" class=\"form-input\" placeholder=\"请输入金额\" />\n        </view>\n      </view>\n      \n      <view class=\"form-item\">\n        <text class=\"form-label\">指定商品</text>\n        <view class=\"form-switch\">\n          <switch :checked=\"specificProducts\" @change=\"toggleSpecificProducts\" color=\"#6B0FBE\" />\n        </view>\n      </view>\n      \n      <view class=\"product-list\" v-if=\"specificProducts\">\n        <view class=\"product-item\" v-for=\"(product, index) in selectedProducts\" :key=\"index\">\n          <image class=\"product-image\" :src=\"product.image\" mode=\"aspectFill\"></image>\n          <view class=\"product-info\">\n            <text class=\"product-name\">{{product.name}}</text>\n            <text class=\"product-price\">¥{{product.price}}</text>\n          </view>\n          <view class=\"product-remove\" @click=\"removeProduct(index)\">×</view>\n        </view>\n        \n        <view class=\"add-product\" @click=\"addProduct\">\n          <view class=\"add-icon\">+</view>\n          <text class=\"add-text\">添加商品</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 审核设置卡片 -->\n    <view class=\"approval-card\" v-if=\"selectedCondition === 'apply' || selectedCondition === 'purchase'\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">审核设置</text>\n      </view>\n      \n      <view class=\"form-item\">\n        <text class=\"form-label\">需要审核</text>\n        <view class=\"form-switch\">\n          <switch :checked=\"requireApproval\" @change=\"toggleRequireApproval\" color=\"#6B0FBE\" />\n        </view>\n      </view>\n      \n      <view class=\"form-item\" v-if=\"requireApproval\">\n        <text class=\"form-label\">自动通过审核</text>\n        <view class=\"form-switch\">\n          <switch :checked=\"autoApprove\" @change=\"toggleAutoApprove\" color=\"#6B0FBE\" />\n        </view>\n      </view>\n    </view>\n    \n    <!-- 保存按钮 -->\n    <view class=\"button-container\">\n      <button class=\"save-button\" @click=\"saveSettings\">保存设置</button>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue';\n\n// 选中的条件\nconst selectedCondition = ref('purchase');\n\n// 购买金额\nconst purchaseAmount = ref('100');\n\n// 是否指定商品\nconst specificProducts = ref(true);\n\n// 是否需要审核\nconst requireApproval = ref(true);\n\n// 是否自动通过审核\nconst autoApprove = ref(false);\n\n// 选中的商品\nconst selectedProducts = ref([\n  {\n    id: '1',\n    name: '分销员入门套餐',\n    price: '99.00',\n    image: '/static/images/products/product-1.jpg'\n  },\n  {\n    id: '2',\n    name: '分销员高级套餐',\n    price: '199.00',\n    image: '/static/images/products/product-2.jpg'\n  }\n]);\n\n// 页面加载\nonMounted(() => {\n  // 获取分销员条件设置\n  getConditionSettings();\n});\n\n// 获取分销员条件设置\nconst getConditionSettings = () => {\n  // 这里应该从API获取设置\n  // 暂时使用模拟数据\n};\n\n// 选择条件\nconst selectCondition = (condition) => {\n  selectedCondition.value = condition;\n};\n\n// 切换是否指定商品\nconst toggleSpecificProducts = (e) => {\n  specificProducts.value = e.detail.value;\n};\n\n// 切换是否需要审核\nconst toggleRequireApproval = (e) => {\n  requireApproval.value = e.detail.value;\n  \n  // 如果不需要审核，则不自动通过\n  if (!requireApproval.value) {\n    autoApprove.value = false;\n  }\n};\n\n// 切换是否自动通过审核\nconst toggleAutoApprove = (e) => {\n  autoApprove.value = e.detail.value;\n};\n\n// 添加商品\nconst addProduct = () => {\n  uni.showToast({\n    title: '添加商品功能开发中',\n    icon: 'none'\n  });\n};\n\n// 移除商品\nconst removeProduct = (index) => {\n  selectedProducts.value.splice(index, 1);\n};\n\n// 保存设置\nconst saveSettings = () => {\n  // 这里应该调用API保存设置\n  \n  uni.showLoading({\n    title: '保存中...'\n  });\n  \n  setTimeout(() => {\n    uni.hideLoading();\n    uni.showToast({\n      title: '保存成功',\n      icon: 'success'\n    });\n    \n    // 返回上一页\n    setTimeout(() => {\n      uni.navigateBack();\n    }, 1500);\n  }, 1000);\n};\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n\n// 显示帮助\nconst showHelp = () => {\n  uni.showModal({\n    title: '分销员条件帮助',\n    content: '您可以设置用户成为分销员的条件，包括无条件、申请、购买商品和邀请等方式。',\n    showCancel: false\n  });\n};\n</script>\n\n<style lang=\"scss\">\n.conditions-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: env(safe-area-inset-bottom);\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\n  color: #fff;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: bold;\n}\n\n/* 条件卡片样式 */\n.conditions-card {\n  margin: 16px;\n  background-color: #FFFFFF;\n  border-radius: 20px;\n  overflow: hidden;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);\n  padding: 8px 0;\n}\n\n.condition-item {\n  display: flex;\n  align-items: center;\n  padding: 16px;\n  border-bottom: 1px solid #F0F0F0;\n}\n\n.condition-item:last-child {\n  border-bottom: none;\n}\n\n.radio-button {\n  width: 20px;\n  height: 20px;\n  border-radius: 10px;\n  border: 2px solid #CCCCCC;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 12px;\n  flex-shrink: 0;\n}\n\n.radio-button.active {\n  border-color: #6B0FBE;\n}\n\n.radio-inner {\n  width: 10px;\n  height: 10px;\n  border-radius: 5px;\n  background-color: #6B0FBE;\n}\n\n.condition-content {\n  flex: 1;\n}\n\n.condition-title {\n  font-size: 15px;\n  color: #333;\n  margin-bottom: 4px;\n}\n\n.condition-desc {\n  font-size: 12px;\n  color: #999;\n}\n\n/* 附加条件卡片样式 */\n.additional-card, .approval-card {\n  margin: 16px;\n  background-color: #FFFFFF;\n  border-radius: 20px;\n  overflow: hidden;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);\n}\n\n.card-header {\n  padding: 16px;\n  border-bottom: 1px solid #F0F0F0;\n}\n\n.card-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.form-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px;\n  border-bottom: 1px solid #F0F0F0;\n}\n\n.form-item:last-child {\n  border-bottom: none;\n}\n\n.form-label {\n  font-size: 15px;\n  color: #333;\n}\n\n.form-input-group {\n  display: flex;\n  align-items: center;\n  background-color: #F5F7FA;\n  border-radius: 8px;\n  padding: 0 12px;\n  height: 36px;\n}\n\n.input-prefix {\n  font-size: 15px;\n  color: #333;\n  margin-right: 4px;\n}\n\n.form-input {\n  height: 36px;\n  width: 80px;\n  font-size: 15px;\n  color: #333;\n  text-align: right;\n}\n\n.form-switch {\n  height: 36px;\n  display: flex;\n  align-items: center;\n}\n\n/* 商品列表样式 */\n.product-list {\n  padding: 16px;\n}\n\n.product-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 12px;\n  background-color: #F5F7FA;\n  border-radius: 12px;\n  padding: 10px;\n}\n\n.product-image {\n  width: 50px;\n  height: 50px;\n  border-radius: 8px;\n  margin-right: 12px;\n}\n\n.product-info {\n  flex: 1;\n}\n\n.product-name {\n  font-size: 14px;\n  color: #333;\n  margin-bottom: 4px;\n}\n\n.product-price {\n  font-size: 13px;\n  color: #FF3B30;\n}\n\n.product-remove {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background-color: rgba(255, 59, 48, 0.1);\n  color: #FF3B30;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n}\n\n.add-product {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 12px;\n  border: 1px dashed #CCCCCC;\n  border-radius: 12px;\n}\n\n.add-icon {\n  width: 20px;\n  height: 20px;\n  border-radius: 10px;\n  background-color: #6B0FBE;\n  color: #FFFFFF;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 8px;\n  font-size: 16px;\n}\n\n.add-text {\n  font-size: 14px;\n  color: #6B0FBE;\n}\n\n/* 按钮样式 */\n.button-container {\n  margin: 24px 16px;\n}\n\n.save-button {\n  height: 44px;\n  border-radius: 22px;\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\n  color: #FFFFFF;\n  font-size: 16px;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: none;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/distribution/conditions.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni", "MiniProgramPage"], "mappings": ";;;;;AA8HA,UAAM,oBAAoBA,cAAAA,IAAI,UAAU;AAGxC,UAAM,iBAAiBA,cAAAA,IAAI,KAAK;AAGhC,UAAM,mBAAmBA,cAAAA,IAAI,IAAI;AAGjC,UAAM,kBAAkBA,cAAAA,IAAI,IAAI;AAGhC,UAAM,cAAcA,cAAAA,IAAI,KAAK;AAG7B,UAAM,mBAAmBA,cAAAA,IAAI;AAAA,MAC3B;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,MACR;AAAA,IACH,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AAEd;IACF,CAAC;AAGD,UAAM,uBAAuB,MAAM;AAAA,IAGnC;AAGA,UAAM,kBAAkB,CAAC,cAAc;AACrC,wBAAkB,QAAQ;AAAA,IAC5B;AAGA,UAAM,yBAAyB,CAAC,MAAM;AACpC,uBAAiB,QAAQ,EAAE,OAAO;AAAA,IACpC;AAGA,UAAM,wBAAwB,CAAC,MAAM;AACnC,sBAAgB,QAAQ,EAAE,OAAO;AAGjC,UAAI,CAAC,gBAAgB,OAAO;AAC1B,oBAAY,QAAQ;AAAA,MACrB;AAAA,IACH;AAGA,UAAM,oBAAoB,CAAC,MAAM;AAC/B,kBAAY,QAAQ,EAAE,OAAO;AAAA,IAC/B;AAGA,UAAM,aAAa,MAAM;AACvBC,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,CAAC,UAAU;AAC/B,uBAAiB,MAAM,OAAO,OAAO,CAAC;AAAA,IACxC;AAGA,UAAM,eAAe,MAAM;AAGzBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAGD,mBAAW,MAAM;AACfA,wBAAG,MAAC,aAAY;AAAA,QACjB,GAAE,IAAI;AAAA,MACR,GAAE,GAAI;AAAA,IACT;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MAChB,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/OA,GAAG,WAAWC,SAAe;"}