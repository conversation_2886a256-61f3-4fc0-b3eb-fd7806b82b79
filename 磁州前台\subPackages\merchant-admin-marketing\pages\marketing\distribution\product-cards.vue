<template>
  <view class="product-cards-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">商品推广卡片</text>
      <view class="navbar-right">
        <view class="history-icon" @click="showHistory">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <polyline points="12 6 12 12 16 14" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </view>
      </view>
    </view>
    
    <!-- 商品选择 -->
    <view class="product-selection-section">
      <view class="section-header">
        <text class="section-title">选择推广商品</text>
        <view class="search-btn" @click="showSearch">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M21 21L16.65 16.65" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <text>搜索商品</text>
        </view>
      </view>
      
      <scroll-view class="product-scroll" scroll-y>
        <view class="product-list">
          <view 
            v-for="(product, index) in products" 
            :key="index" 
            class="product-item"
            :class="{ selected: selectedProduct === product }"
            @click="selectProduct(product)"
          >
            <view class="select-indicator" v-if="selectedProduct === product">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 6L9 17L4 12" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </view>
            <image class="product-image" :src="product.image" mode="aspectFill"></image>
            <view class="product-info">
              <text class="product-name">{{product.name}}</text>
              <view class="product-price-row">
                <text class="product-price">¥{{product.price}}</text>
                <text class="product-commission">佣金: ¥{{product.commission}}</text>
              </view>
              <view class="product-sales">已售 {{product.sold}} | 库存 {{product.stock}}</view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 卡片预览 -->
    <view class="card-preview-section" v-if="selectedProduct">
      <view class="section-header">
        <text class="section-title">卡片预览</text>
        <view class="template-selector" @click="showTemplates">
          <text>{{currentTemplate.name}}</text>
          <view class="selector-arrow"></view>
        </view>
      </view>
      
      <view class="card-preview" :class="currentTemplate.value">
        <view class="preview-card">
          <image class="card-product-image" :src="selectedProduct.image" mode="aspectFill"></image>
          
          <view class="card-content">
            <text class="card-product-name">{{selectedProduct.name}}</text>
            
            <view class="card-price-row">
              <text class="card-price">¥{{selectedProduct.price}}</text>
              <text class="card-original-price" v-if="selectedProduct.originalPrice">¥{{selectedProduct.originalPrice}}</text>
            </view>
            
            <view class="card-footer">
              <view class="card-shop-info">
                <image class="card-shop-logo" :src="shopInfo.logo" mode="aspectFill"></image>
                <text class="card-shop-name">{{shopInfo.name}}</text>
              </view>
              
              <view class="card-qrcode-hint">
                <view class="card-qrcode-placeholder"></view>
                <text class="card-scan-text">扫码购买</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 自定义选项 -->
      <view class="customization-options">
        <view class="option-group">
          <text class="option-title">卡片样式</text>
          <view class="style-options">
            <view 
              v-for="(style, index) in cardStyles" 
              :key="index" 
              class="style-option" 
              :class="{ active: currentStyle === style.value }"
              @click="selectStyle(style.value)"
            >
              <view class="style-color" :style="{ backgroundColor: style.color }"></view>
              <text class="style-name">{{style.name}}</text>
            </view>
          </view>
        </view>
        
        <view class="option-group">
          <view class="toggle-option">
            <text class="toggle-label">显示原价</text>
            <switch :checked="showOriginalPrice" @change="toggleOriginalPrice" color="#6B0FBE" />
          </view>
          
          <view class="toggle-option">
            <text class="toggle-label">添加促销标签</text>
            <switch :checked="showPromoTag" @change="togglePromoTag" color="#6B0FBE" />
          </view>
          
          <view class="toggle-option" v-if="showPromoTag">
            <text class="toggle-label">促销标签文字</text>
            <input 
              class="tag-input" 
              type="text" 
              v-model="promoTagText" 
              placeholder="限时优惠" 
              maxlength="6"
            />
          </view>
        </view>
      </view>
      
      <!-- 生成按钮 -->
      <view class="generate-button-container">
        <button class="generate-button" @click="generateCard">
          <text>生成推广卡片</text>
        </button>
      </view>
    </view>
    
    <!-- 空状态提示 -->
    <view class="empty-state" v-if="!selectedProduct">
      <image class="empty-image" src="/static/images/empty-state.png" mode="aspectFit"></image>
      <text class="empty-text">请选择一个商品来创建推广卡片</text>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue';

// 店铺信息
const shopInfo = reactive({
  name: '磁州同城生活',
  logo: '/static/images/logo.png'
});

// 商品列表
const products = reactive([
  {
    id: 1,
    name: 'Apple iPhone 14 Pro Max 256GB 暗夜紫',
    image: '/static/images/products/iphone.jpg',
    price: 8999.00,
    originalPrice: 9599.00,
    commission: 450.00,
    sold: 86,
    stock: 100
  },
  {
    id: 2,
    name: 'Apple Watch Series 8 GPS 45mm 午夜色',
    image: '/static/images/products/watch.jpg',
    price: 3299.00,
    originalPrice: 3499.00,
    commission: 165.00,
    sold: 72,
    stock: 100
  },
  {
    id: 3,
    name: 'Apple AirPods Pro 2 主动降噪无线耳机',
    image: '/static/images/products/airpods.jpg',
    price: 1999.00,
    originalPrice: 2199.00,
    commission: 100.00,
    sold: 94,
    stock: 100
  },
  {
    id: 4,
    name: 'Apple MacBook Air M2 芯片 13.6英寸',
    image: '/static/images/products/macbook.jpg',
    price: 7999.00,
    originalPrice: 8499.00,
    commission: 400.00,
    sold: 58,
    stock: 100
  },
  {
    id: 5,
    name: 'Apple iPad Air 5 10.9英寸 Wi-Fi版',
    image: '/static/images/products/ipad.jpg',
    price: 4799.00,
    originalPrice: 4999.00,
    commission: 240.00,
    sold: 65,
    stock: 100
  }
]);

// 卡片模板
const templates = [
  { name: '横版卡片', value: 'horizontal' },
  { name: '竖版卡片', value: 'vertical' },
  { name: '简约卡片', value: 'minimal' }
];

// 卡片样式
const cardStyles = [
  { name: '紫色', value: 'purple', color: '#6B0FBE' },
  { name: '蓝色', value: 'blue', color: '#007AFF' },
  { name: '绿色', value: 'green', color: '#34C759' },
  { name: '红色', value: 'red', color: '#FF3B30' },
  { name: '橙色', value: 'orange', color: '#FF9500' }
];

// 状态变量
const selectedProduct = ref(null);
const currentTemplate = ref(templates[0]);
const currentStyle = ref('purple');
const showOriginalPrice = ref(true);
const showPromoTag = ref(false);
const promoTagText = ref('限时优惠');

// 方法
const goBack = () => {
  uni.navigateBack();
};

const showHistory = () => {
  uni.navigateTo({
    url: '/subPackages/merchant-admin-marketing/pages/distribution/product-cards/history'
  });
};

const showSearch = () => {
  uni.showToast({
    title: '搜索功能开发中',
    icon: 'none'
  });
};

const selectProduct = (product) => {
  selectedProduct.value = product;
};

const showTemplates = () => {
  uni.showActionSheet({
    itemList: templates.map(item => item.name),
    success: (res) => {
      currentTemplate.value = templates[res.tapIndex];
    }
  });
};

const selectStyle = (style) => {
  currentStyle.value = style;
};

const toggleOriginalPrice = (e) => {
  showOriginalPrice.value = e.detail.value;
};

const togglePromoTag = (e) => {
  showPromoTag.value = e.detail.value;
};

const generateCard = () => {
  uni.showLoading({
    title: '生成中...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    uni.showModal({
      title: '卡片生成成功',
      content: '推广卡片已生成，您可以保存到相册或直接分享',
      confirmText: '分享',
      cancelText: '保存',
      success: (res) => {
        if (res.confirm) {
          uni.showShareMenu({
            withShareTicket: true,
            menus: ['shareAppMessage', 'shareTimeline']
          });
        } else {
          uni.showToast({
            title: '已保存到相册',
            icon: 'success'
          });
        }
      }
    });
  }, 1500);
};
</script>

<style lang="scss">
.product-cards-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.history-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 商品选择部分 */
.product-selection-section {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.search-btn {
  display: flex;
  align-items: center;
  background-color: rgba(107, 15, 190, 0.05);
  border-radius: 16px;
  padding: 6px 12px;
}

.search-btn text {
  font-size: 14px;
  color: #6B0FBE;
  margin-left: 6px;
}

.product-scroll {
  max-height: 320px;
}

.product-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.product-item {
  display: flex;
  background-color: #F8F9FC;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.product-item.selected {
  border-color: #6B0FBE;
  background-color: rgba(107, 15, 190, 0.05);
}

.select-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: #6B0FBE;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.product-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
}

.product-info {
  flex: 1;
  padding: 10px;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-price-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
}

.product-price {
  font-size: 14px;
  color: #FF3B30;
  font-weight: 500;
}

.product-commission {
  font-size: 12px;
  color: #6B0FBE;
}

.product-sales {
  font-size: 12px;
  color: #999;
}

/* 卡片预览部分 */
.card-preview-section {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
}

.template-selector {
  display: flex;
  align-items: center;
  background-color: rgba(107, 15, 190, 0.05);
  border-radius: 16px;
  padding: 6px 12px;
}

.template-selector text {
  font-size: 14px;
  color: #6B0FBE;
}

.selector-arrow {
  width: 8px;
  height: 8px;
  border-right: 2px solid #6B0FBE;
  border-bottom: 2px solid #6B0FBE;
  transform: rotate(45deg);
  margin-left: 8px;
}

.card-preview {
  margin: 20px 0;
  display: flex;
  justify-content: center;
}

.preview-card {
  width: 300px;
  background-color: #FFFFFF;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

.card-product-image {
  width: 100%;
  height: 180px;
  object-fit: cover;
}

.card-content {
  padding: 16px;
}

.card-product-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-price-row {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.card-price {
  font-size: 18px;
  color: #FF3B30;
  font-weight: 600;
  margin-right: 8px;
}

.card-original-price {
  font-size: 14px;
  color: #999;
  text-decoration: line-through;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-shop-info {
  display: flex;
  align-items: center;
}

.card-shop-logo {
  width: 20px;
  height: 20px;
  border-radius: 10px;
  margin-right: 8px;
}

.card-shop-name {
  font-size: 12px;
  color: #666;
}

.card-qrcode-hint {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.card-qrcode-placeholder {
  width: 40px;
  height: 40px;
  background-color: #F0F0F0;
  border-radius: 4px;
  margin-bottom: 4px;
  position: relative;
}

.card-qrcode-placeholder::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background-color: #CCCCCC;
  border-radius: 2px;
}

.card-scan-text {
  font-size: 10px;
  color: #999;
}

/* 自定义选项 */
.customization-options {
  margin-bottom: 20px;
}

.option-group {
  margin-bottom: 20px;
}

.option-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  display: block;
}

.style-options {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.style-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 60px;
}

.style-color {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  margin-bottom: 6px;
  transition: all 0.3s ease;
}

.style-name {
  font-size: 12px;
  color: #666;
}

.style-option.active .style-color {
  transform: scale(1.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.style-option.active .style-name {
  color: #6B0FBE;
  font-weight: 500;
}

.toggle-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #F0F0F0;
}

.toggle-option:last-child {
  border-bottom: none;
}

.toggle-label {
  font-size: 14px;
  color: #333;
}

.tag-input {
  width: 120px;
  height: 36px;
  background-color: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 14px;
  color: #333;
  text-align: right;
}

/* 生成按钮 */
.generate-button-container {
  padding: 0 20px;
}

.generate-button {
  width: 100%;
  height: 48px;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  border-radius: 24px;
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  box-shadow: 0 4px 12px rgba(107, 15, 190, 0.2);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 20px;
}

.empty-text {
  font-size: 14px;
  color: #999;
  text-align: center;
}

/* 卡片模板样式 */
.horizontal .preview-card {
  display: flex;
  width: 300px;
  height: 120px;
}

.horizontal .card-product-image {
  width: 120px;
  height: 120px;
}

.horizontal .card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.horizontal .card-product-name {
  font-size: 14px;
  margin-bottom: 6px;
  -webkit-line-clamp: 1;
}

.horizontal .card-price-row {
  margin-bottom: 8px;
}

.horizontal .card-price {
  font-size: 16px;
}

.horizontal .card-footer {
  margin-top: auto;
}

.vertical .preview-card {
  width: 240px;
}

.minimal .preview-card {
  width: 280px;
  border: 1px solid #EEEEEE;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);
}

.minimal .card-product-image {
  height: 160px;
}

.minimal .card-content {
  padding: 12px;
}

.minimal .card-product-name {
  font-size: 14px;
  margin-bottom: 8px;
}

.minimal .card-price {
  font-size: 16px;
}
</style> 