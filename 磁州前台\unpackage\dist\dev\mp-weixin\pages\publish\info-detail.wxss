
/* 全局样式 */
.post-detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 110rpx;
  padding-top: 150rpx; /* 增加顶部内边距，为固定导航栏留出空间 */
}
.post-detail-wrapper {
  max-width: 750rpx;
  margin: 0 auto;
  padding: 24rpx;
  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */
}

/* 通用卡片样式 */
.content-card {
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
  padding: 30rpx;
}
.card-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

/* 信息主体卡片 */
.main-info {
  margin-top: 0; /* 调整顶部边距，因为现在使用系统导航栏 */
}
.post-header {
  margin-bottom: 30rpx;
}
.post-title {
  font-size: 38rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.4;
}
.post-meta {
  display: flex;
  flex-wrap: wrap;
  color: #8c8c8c;
  font-size: 26rpx;
  gap: 20rpx;
  align-items: center;
}
.post-category {
  color: #0052CC;
  background-color: rgba(0, 82, 204, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 8rpx;
}
.post-gallery {
  margin: 30rpx 0;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.08);
}
.swiper {
  height: 400rpx;
  border-radius: 12rpx;
}
.carousel-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}
.single-image image {
  width: 100%;
  border-radius: 12rpx;
}
.divider {
  height: 2rpx;
  background-color: #f0f0f0;
  margin: 30rpx 0;
}
.post-content {
  font-size: 30rpx;
  line-height: 1.8;
  color: #444;
  margin-bottom: 30rpx;
}
.post-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 20rpx;
}
.tag {
  margin-right: 0;
}

/* 发布者信息卡片 */
.publisher-header {
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 20rpx;
}
.publisher-content {
  display: flex;
  align-items: center;
}
.publisher-avatar {
  margin-right: 20rpx;
}
.avatar-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}
.publisher-details {
  flex-grow: 1;
}
.publisher-name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  color: #333;
}
.publisher-stats {
  display: flex;
  gap: 40rpx;
}
.stat-item {
  text-align: center;
}
.stat-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}
.stat-label {
  font-size: 24rpx;
  color: #8c8c8c;
}
.contact-btn {
  padding: 10rpx 30rpx;
  border-radius: 40rpx;
}

/* 位置信息卡片样式 */
.location-info {
  margin-bottom: 20rpx;
}
.location-content {
  display: flex;
  align-items: center;
  padding: 15rpx 0;
}
.location-content .iconfont {
  font-size: 32rpx;
  color: #0052CC;
  margin-right: 10rpx;
}
.map-preview {
  width: 100%;
  border-radius: 10rpx;
  margin-top: 10rpx;
}

/* 评论区样式 */
.comment-header {
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 20rpx;
}
.comment-list {
  margin-bottom: 30rpx;
}
.comment-empty {
  text-align: center;
  color: #999;
  padding: 40rpx 0;
}
.comment-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}
.comment-user {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.comment-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 15rpx;
}
.comment-user-info {
  flex: 1;
}
.comment-username {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}
.comment-time {
  font-size: 24rpx;
  color: #999;
  margin-left: 15rpx;
}
.comment-content {
  font-size: 28rpx;
  color: #444;
  line-height: 1.5;
  padding-left: 75rpx;
}
.comment-input-area {
  display: flex;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}
.comment-input {
  flex: 1;
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 35rpx;
  padding: 0 20rpx;
  margin-right: 20rpx;
}
.comment-submit {
  border-radius: 35rpx;
}

/* 适配样式 */
@media screen and (max-width: 375px) {
.post-detail-wrapper {
    padding: 0 20rpx;
}
.post-title {
    font-size: 34rpx;
}
.content-card {
    padding: 20rpx;
}
.related-item {
    width: 200rpx;
}
}

/* 底部互动工具栏 */
.interaction-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10rpx 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  height: 120rpx;
  z-index: 100;
}
.toolbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6rpx 0;
  margin: 0 4rpx;
}
.toolbar-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}
.toolbar-text {
  font-size: 22rpx;
  color: #666;
}
.share-button {
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
  line-height: normal;
  border-radius: 0;
  flex: 1;
}
.share-button::after {
  display: none;
}
.call-button {
  flex: 3; /* 占据更多空间，原来是2，现在改为3让按钮更长 */
  background: linear-gradient(135deg, #0052CC, #0066FF);
  height: 90rpx;
  margin: 0 0 0 10rpx;
  border-radius: 45rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
}
.call-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.call-text {
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  line-height: 1.2;
}
.call-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 20rpx;
  line-height: 1.2;
}

/* 隐藏的分享按钮 */
.hidden-share-btn {
  position: fixed;
  width: 2rpx;
  height: 2rpx;
  opacity: 0;
  top: -999rpx;
  left: -999rpx;
  z-index: -1;
  overflow: hidden;
  padding: 0;
  margin: 0;
  border: none;
}
.hidden-share-btn::after {
  display: none;
}

/* 添加分享按钮高亮样式 */
.share-button-highlight {
  animation: shareButtonPulse 0.8s infinite alternate;
  background-color: rgba(0, 102, 255, 0.1);
  border-radius: 8rpx;
}
@keyframes shareButtonPulse {
from { transform: scale(1);
}
to { transform: scale(1.1); background-color: rgba(0, 102, 255, 0.2);
}
}

/* 分享引导浮层 */
.share-guide-layer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
}
.guide-content {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  width: 80%;
  max-width: 600rpx;
  text-align: center;
  position: relative;
}
.guide-close {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #333;
  transition: transform 0.3s;
}
.guide-close:active {
  transform: scale(0.9);
}
.guide-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.guide-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}
.guide-tips {
  font-size: 26rpx;
  color: #ff6600;
  font-weight: bold;
  margin-bottom: 30rpx;
}

/* 分享按钮样式 */
.guide-share-btn {
  background-color: #007AFF;
  color: #FFFFFF !important;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 40rpx;
  padding: 20rpx 0;
  width: 80%;
  margin: 30rpx auto 10rpx;
  box-shadow: 0 6rpx 15rpx rgba(0, 122, 255, 0.3);
  border: none;
  line-height: 1.5;
}
.guide-share-btn::after {
  display: none;
}

/* 相关信息推荐样式 */
.related-posts-card {
  margin-top: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}
.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 16rpx;
}
.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 6rpx;
  height: 28rpx;
  background-color: #0052CC;
  border-radius: 3rpx;
}
.related-posts-content {
  padding: 0 16px 16px;
  overflow: hidden;
}

/* 相关信息列表样式 */
.related-posts-list {
  margin-bottom: 12px;
}
.related-post-item {
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}
.related-post-item:last-child {
  border-bottom: none;
}
.post-item-content {
  display: flex;
  align-items: center;
}
.post-item-left {
  margin-right: 12px;
}
.post-image {
  width: 80px;
  height: 60px;
  border-radius: 8px;
  background-color: #f5f7fa;
}
.post-item-middle {
  flex: 1;
  overflow: hidden;
}
.post-item-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.post-item-category {
  font-size: 13px;
  color: #0052CC;
  background-color: rgba(0, 82, 204, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
  display: inline-block;
  margin-bottom: 6px;
}
.post-item-meta {
  display: flex;
  font-size: 12px;
  color: #999;
}
.post-item-time {
  margin-right: 16px;
}

/* 查看更多按钮样式 */
.view-more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  background-color: #f7f9fc;
  border-radius: 8px;
  margin-top: 8px;
}
.view-more-text {
  font-size: 14px;
  color: #0052CC;
}
.view-more-icon {
  margin-left: 4px;
  font-size: 12px;
  color: #0052CC;
}

/* 空数据提示样式 */
.empty-related-posts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 0;
}
.empty-image {
  width: 80px;
  height: 80px;
  margin-bottom: 12px;
}
.empty-text {
  font-size: 14px;
  color: #999;
}

/* 红包相关样式 */
.red-packet-details {
  background-color: #FFF5F5;
  border-radius: 16rpx;
  padding: 20rpx;
  margin: 20rpx 0;
}
.red-packet-amount,
.red-packet-usage,
.red-packet-method {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #FFE6E6;
}
.amount-label,
.usage-label,
.method-label {
  color: #666;
  font-size: 28rpx;
}
.amount-value,
.usage-value,
.method-value {
  color: #FF4D4F;
  font-size: 32rpx;
  font-weight: bold;
}
.red-packet-receive {
  flex: 1;
  background-color: #FF4D4F;
  color: white;
  text-align: center;
  padding: 20rpx;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 8rpx;
  margin-top: 10rpx;
}
.red-packet-card {
  border-left: 6rpx solid #FF4D4F;
}

/* 自定义导航栏样式 */
.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}
.navbar-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #fff;
  margin: 0 auto;
}
