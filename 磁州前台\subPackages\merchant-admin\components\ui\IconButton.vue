<template>
  <view class="icon-button" :class="[type, size]" @tap="onClick">
    <view class="icon-container">
      <svg-icon :name="icon" :size="iconSize" :color="iconColor" />
    </view>
    <text v-if="text" class="button-text">{{ text }}</text>
    <slot></slot>
  </view>
</template>

<script>
import SvgIcon from './SvgIcon.vue';

export default {
  name: 'IconButton',
  components: {
    SvgIcon
  },
  props: {
    icon: {
      type: String,
      required: true
    },
    text: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'default', // default, primary, success, warning, danger
      validator: value => ['default', 'primary', 'success', 'warning', 'danger'].includes(value)
    },
    size: {
      type: String,
      default: 'medium', // small, medium, large
      validator: value => ['small', 'medium', 'large'].includes(value)
    }
  },
  computed: {
    iconSize() {
      const sizes = {
        small: 16,
        medium: 20,
        large: 24
      };
      return sizes[this.size] || 20;
    },
    iconColor() {
      if (this.type === 'default') return '#666';
      return 'white';
    }
  },
  methods: {
    onClick() {
      this.$emit('click');
    }
  }
}
</script>

<style lang="scss">
.icon-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s;
  
  &.small {
    padding: 6px 12px;
    font-size: 12px;
  }
  
  &.medium {
    padding: 8px 16px;
    font-size: 14px;
  }
  
  &.large {
    padding: 10px 20px;
    font-size: 16px;
  }
  
  &.default {
    background-color: #F5F5F5;
    color: #333;
    
    &:active {
      background-color: #E5E5E5;
    }
  }
  
  &.primary {
    background-color: #0A84FF;
    color: white;
    
    &:active {
      background-color: #0071E3;
    }
  }
  
  &.success {
    background-color: #34C759;
    color: white;
    
    &:active {
      background-color: #30B352;
    }
  }
  
  &.warning {
    background-color: #FF9500;
    color: white;
    
    &:active {
      background-color: #E68600;
    }
  }
  
  &.danger {
    background-color: #FF3B30;
    color: white;
    
    &:active {
      background-color: #E63028;
    }
  }
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 6px;
}

.button-text {
  font-weight: 500;
}
</style> 