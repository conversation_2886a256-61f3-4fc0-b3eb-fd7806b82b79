"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  __name: "find-service-detail",
  setup(__props) {
    const formatTime = (timestamp) => {
      const date = new Date(timestamp);
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    };
    const isCollected = common_vendor.ref(false);
    const serviceData = common_vendor.ref({
      id: "find12345",
      title: "寻找专业水电维修师傅",
      budget: "200-500元（视情况而定）",
      tags: ["水电维修", "急聘", "可议价", "今日内", "有保修"],
      publishTime: Date.now() - 864e5 * 0.5,
      // 12小时前
      description: "家里厨房水管突然漏水，现已临时关闭总阀门。需要专业的水电维修师傅上门检查维修，要求经验丰富，能快速诊断问题并解决。因情况紧急，希望能今天下午或晚上来处理。",
      type: "水电维修",
      area: "磁县城区-县政府附近",
      time: "今天（越快越好）",
      method: "上门服务",
      details: [
        { label: "维修项目", value: "厨房水管漏水" },
        { label: "具体位置", value: "厨房水槽下方管道" },
        { label: "漏水情况", value: "渗水明显，已关闭总阀门" },
        { label: "紧急程度", value: "非常紧急（影响正常用水）" },
        { label: "服务要求", value: "持证上岗，经验丰富，维修彻底" },
        { label: "预算说明", value: "基础检查+维修费用，耗材另计" }
      ],
      notes: "<p>补充说明：</p><ul><li>需要师傅自带齐全工具，我家没有维修工具</li><li>希望能提供材料发票和维修保修服务</li><li>最好能提前电话联系确认到达时间</li><li>我家是6楼，有电梯</li><li>预算可根据实际情况适当调整，主要求修复彻底不返工</li></ul>",
      publisher: {
        name: "张先生",
        avatar: "/static/images/avatar.png",
        type: "个人用户",
        rating: "A+（优质用户）",
        isVerified: true
      },
      contact: {
        name: "张先生",
        phone: "13912345678"
      }
    });
    const similarServices = common_vendor.ref([
      {
        id: "find001",
        title: "急寻专业空调维修师傅上门检修",
        budget: "100-300元",
        type: "空调维修",
        area: "磁县城区"
      },
      {
        id: "find002",
        title: "找专业小时工打扫新房",
        budget: "80元/小时",
        type: "保洁服务",
        area: "磁县城区"
      },
      {
        id: "find003",
        title: "寻找搬家工人帮忙搬家",
        budget: "300元",
        type: "搬家服务",
        area: "磁县城区"
      },
      {
        id: "find004",
        title: "找厨师上门做一天席面",
        budget: "800元/天",
        type: "厨师服务",
        area: "磁县城区"
      }
    ]);
    const relatedServices = common_vendor.ref([]);
    const loadRelatedServices = () => {
      setTimeout(() => {
        relatedServices.value = [
          {
            id: "service001",
            title: "专业水电维修师傅",
            providerName: "老王维修店",
            providerLogo: "/static/images/tabbar/公司.png",
            tags: ["水电维修", "上门服务", "5年经验"],
            price: "上门费50元起"
          },
          {
            id: "service002",
            title: "水管安装疏通修理",
            providerName: "王师傅维修",
            providerLogo: "/static/images/tabbar/企业.png",
            tags: ["管道疏通", "安装维修", "快速响应"],
            price: "定价优惠"
          },
          {
            id: "service003",
            title: "全能水电维修服务",
            providerName: "张师傅维修",
            providerLogo: "/static/images/tabbar/个人.png",
            tags: ["专业水电", "随叫随到", "保修一年"],
            price: "面议"
          }
        ];
      }, 500);
    };
    const posterImagePath = common_vendor.ref("");
    const showPosterFlag = common_vendor.ref(false);
    const toggleCollect = () => {
      isCollected.value = !isCollected.value;
      if (isCollected.value) {
        common_vendor.index.showToast({
          title: "收藏成功",
          icon: "success"
        });
      }
    };
    const callPhone = () => {
      common_vendor.index.makePhoneCall({
        phoneNumber: serviceData.value.contact.phone,
        fail: () => {
          common_vendor.index.showToast({
            title: "拨打电话失败",
            icon: "none"
          });
        }
      });
    };
    const navigateToService = (id) => {
      common_vendor.index.navigateTo({
        url: `/pages/publish/find-service-detail?id=${id}`
      });
    };
    const goToHome = () => {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    };
    const openChat = () => {
      if (!serviceData.value.publisher || !serviceData.value.publisher.id) {
        common_vendor.index.showToast({
          title: "无法获取发布者信息",
          icon: "none"
        });
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/chat/index?userId=${serviceData.value.publisher.id}&username=${encodeURIComponent(serviceData.value.publisher.name || "发布者")}`
      });
    };
    const loadServiceData = (id) => {
      if (id && id.startsWith("temp_")) {
        const publishDataList = common_vendor.index.getStorageSync("publishDataList") || [];
        const publishData = publishDataList.find((item) => item.id === id);
        if (publishData) {
          updateServiceDataFromPublish(publishData);
        } else {
          common_vendor.index.showToast({
            title: "数据加载失败",
            icon: "none"
          });
        }
      } else {
        common_vendor.index.__f__("log", "at pages/publish/find-service-detail.vue:379", "获取服务ID:", id);
      }
    };
    const updateServiceDataFromPublish = (publishData) => {
      serviceData.value = {
        ...serviceData.value,
        id: publishData.id,
        title: publishData.title || "寻找服务",
        budget: publishData.budget || "预算待定",
        publishTime: publishData.publishTime || Date.now(),
        description: publishData.demandDescription || publishData.description || serviceData.value.description,
        type: publishData.serviceType || "服务类型",
        area: publishData.serviceArea || "磁县城区",
        time: publishData.urgency === "非常紧急" ? "今天（越快越好）" : "近期",
        method: "上门服务"
      };
      if (publishData.contact && publishData.phone) {
        serviceData.value.contact = {
          name: publishData.contact,
          phone: publishData.phone
        };
        serviceData.value.publisher = {
          name: publishData.contact,
          avatar: "/static/images/avatar.png",
          type: "个人用户",
          rating: "A（普通用户）",
          isVerified: true
        };
      }
      if (publishData.requirements && publishData.requirements.length > 0) {
        serviceData.value.tags = publishData.requirements;
      } else if (publishData.tags && publishData.tags.length > 0) {
        serviceData.value.tags = publishData.tags;
      }
      const newDetails = [];
      if (publishData.serviceType) {
        newDetails.push({ label: "服务类型", value: publishData.serviceType });
      }
      if (publishData.serviceArea) {
        newDetails.push({ label: "服务区域", value: publishData.serviceArea });
      }
      if (publishData.urgency) {
        newDetails.push({ label: "紧急程度", value: publishData.urgency });
      }
      if (publishData.budget) {
        newDetails.push({ label: "预算", value: publishData.budget });
      }
      if (publishData.images && publishData.images.length > 0) {
        newDetails.push({ label: "相关图片", value: `已上传${publishData.images.length}张图片` });
      }
      if (newDetails.length > 0) {
        serviceData.value.details = newDetails;
      }
      if (publishData.demandDescription) {
        serviceData.value.notes = `<p>需求详情：</p><p>${publishData.demandDescription}</p>`;
      }
    };
    const generateShareImage = () => {
      common_vendor.index.showLoading({
        title: "正在生成海报...",
        mask: true
      });
      const posterData = {
        title: serviceData.value.title,
        budget: serviceData.value.budget,
        type: serviceData.value.type,
        address: serviceData.value.area,
        phone: serviceData.value.publisher ? serviceData.value.publisher.phone : "",
        description: serviceData.value.description ? serviceData.value.description.substring(0, 60) + "..." : "",
        qrcode: "/static/images/tabbar/客服微信.png",
        logo: "/static/images/tabbar/商家入驻.png",
        bgImage: "/static/images/banner/banner-1.png"
      };
      const ctx = common_vendor.index.createCanvasContext("posterCanvas");
      ctx.save();
      ctx.drawImage(posterData.bgImage, 0, 0, 600, 400);
      ctx.setFillStyle("rgba(0, 0, 0, 0.35)");
      ctx.fillRect(0, 0, 600, 900);
      ctx.restore();
      ctx.save();
      ctx.setFillStyle("#ffffff");
      ctx.fillRect(30, 280, 540, 550);
      ctx.restore();
      ctx.save();
      ctx.beginPath();
      ctx.arc(300, 200, 80, 0, 2 * Math.PI);
      ctx.setFillStyle("#ffffff");
      ctx.fill();
      ctx.clip();
      ctx.drawImage(posterData.logo, 220, 120, 160, 160);
      ctx.restore();
      ctx.setFillStyle("#333333");
      ctx.setFontSize(32);
      ctx.setTextAlign("center");
      ctx.fillText(posterData.title, 300, 350);
      ctx.setFillStyle("#FF6B6B");
      ctx.setFontSize(28);
      ctx.fillText(posterData.budget, 300, 400);
      ctx.beginPath();
      ctx.setStrokeStyle("#eeeeee");
      ctx.setLineWidth(2);
      ctx.moveTo(100, 430);
      ctx.lineTo(500, 430);
      ctx.stroke();
      ctx.setFillStyle("#666666");
      ctx.setFontSize(24);
      ctx.setTextAlign("left");
      ctx.fillText("服务类型: " + posterData.type, 80, 480);
      ctx.fillText("服务区域: " + posterData.address, 80, 520);
      const wrapText = (ctx2, text, x, y, maxWidth, lineHeight) => {
        if (text.length === 0)
          return;
        const words = text.split("");
        let line = "";
        let testLine = "";
        let lineCount = 0;
        for (let n = 0; n < words.length; n++) {
          testLine += words[n];
          const metrics = ctx2.measureText(testLine);
          const testWidth = metrics.width;
          if (testWidth > maxWidth && n > 0) {
            ctx2.fillText(line, x, y + lineCount * lineHeight);
            line = words[n];
            testLine = words[n];
            lineCount++;
            if (lineCount >= 3) {
              line += "...";
              ctx2.fillText(line, x, y + lineCount * lineHeight);
              break;
            }
          } else {
            line = testLine;
          }
        }
        if (lineCount < 3) {
          ctx2.fillText(line, x, y + lineCount * lineHeight);
        }
      };
      ctx.setFillStyle("#666666");
      wrapText(ctx, posterData.description, 80, 560, 440, 35);
      if (posterData.phone) {
        ctx.fillText("联系电话: " + posterData.phone, 80, 680);
      }
      ctx.drawImage(posterData.qrcode, 225, 720, 150, 150);
      ctx.setFillStyle("#999999");
      ctx.setFontSize(20);
      ctx.setTextAlign("center");
      ctx.fillText("长按识别二维码查看详情", 300, 880);
      ctx.setFillStyle("#333333");
      ctx.setFontSize(24);
      ctx.fillText("磁县同城 - 生活服务", 300, 840);
      ctx.draw(false, () => {
        setTimeout(() => {
          common_vendor.index.canvasToTempFilePath({
            canvasId: "posterCanvas",
            success: (res) => {
              common_vendor.index.hideLoading();
              showPosterModal(res.tempFilePath);
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/publish/find-service-detail.vue:618", "生成海报失败", err);
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "生成海报失败",
                icon: "none"
              });
            }
          });
        }, 800);
      });
    };
    const showPosterModal = (posterPath) => {
      posterImagePath.value = posterPath;
      showPosterFlag.value = true;
      common_vendor.index.showModal({
        title: "海报已生成",
        content: "海报已生成，是否保存到相册？",
        confirmText: "保存",
        success: (res) => {
          if (res.confirm) {
            savePosterToAlbum(posterPath);
          } else {
            common_vendor.index.previewImage({
              urls: [posterPath],
              current: posterPath
            });
          }
        }
      });
    };
    const savePosterToAlbum = (posterPath) => {
      common_vendor.index.showLoading({
        title: "正在保存..."
      });
      common_vendor.index.saveImageToPhotosAlbum({
        filePath: posterPath,
        success: () => {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "已保存到相册",
            icon: "success"
          });
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at pages/publish/find-service-detail.vue:670", "保存失败", err);
          if (err.errMsg.indexOf("auth deny") > -1) {
            common_vendor.index.showModal({
              title: "提示",
              content: "保存失败，请授权相册权限后重试",
              confirmText: "去设置",
              success: (res) => {
                if (res.confirm) {
                  common_vendor.index.openSetting();
                }
              }
            });
          } else {
            common_vendor.index.showToast({
              title: "保存失败",
              icon: "none"
            });
          }
        }
      });
    };
    common_vendor.onMounted(() => {
      common_vendor.index.setNavigationBarTitle({
        title: "需求详情"
      });
      common_vendor.index.setNavigationBarColor({
        frontColor: "#000000",
        backgroundColor: "#ffffff"
      });
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      if (options.id) {
        loadServiceData(options.id);
      }
      loadRelatedServices();
      if (options.fromPublish) {
        setTimeout(() => {
          const shareButton = document.getElementById("shareButton");
          if (shareButton) {
            shareButton.click();
          }
        }, 1500);
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_10,
        b: common_vendor.o(generateShareImage),
        c: common_vendor.t(serviceData.value.title),
        d: common_vendor.t(serviceData.value.budget),
        e: common_vendor.f(serviceData.value.tags, (tag, index, i0) => {
          return {
            a: common_vendor.t(tag),
            b: index
          };
        }),
        f: common_vendor.t(formatTime(serviceData.value.publishTime)),
        g: common_vendor.t(serviceData.value.description),
        h: common_vendor.t(serviceData.value.type),
        i: common_vendor.t(serviceData.value.area),
        j: common_vendor.t(serviceData.value.time),
        k: common_vendor.t(serviceData.value.method),
        l: common_vendor.f(serviceData.value.details, (item, index, i0) => {
          return {
            a: common_vendor.t(item.label),
            b: common_vendor.t(item.value),
            c: index
          };
        }),
        m: serviceData.value.notes,
        n: serviceData.value.publisher.avatar,
        o: common_vendor.t(serviceData.value.publisher.name),
        p: common_vendor.t(serviceData.value.publisher.type),
        q: common_vendor.t(serviceData.value.publisher.rating),
        r: serviceData.value.publisher.isVerified
      }, serviceData.value.publisher.isVerified ? {} : {}, {
        s: common_vendor.t(serviceData.value.contact.name),
        t: common_vendor.t(serviceData.value.contact.phone),
        v: common_vendor.o(callPhone),
        w: common_vendor.f(similarServices.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.title),
            b: common_vendor.t(item.budget),
            c: common_vendor.t(item.type),
            d: common_vendor.t(item.area),
            e: index,
            f: common_vendor.o(($event) => navigateToService(item.id), index)
          };
        }),
        x: common_assets._imports_12,
        y: common_vendor.o(goToHome),
        z: common_assets._imports_3$2,
        A: common_vendor.o(toggleCollect),
        B: common_assets._imports_3$3,
        C: common_assets._imports_14,
        D: common_vendor.o(openChat),
        E: common_vendor.o(callPhone)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/publish/find-service-detail.js.map
