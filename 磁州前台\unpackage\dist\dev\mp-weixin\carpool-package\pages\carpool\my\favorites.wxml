<view class="favorites-container"><view class="custom-header" style="{{'padding-top:' + c}}"><view class="header-content"><view class="left-action" bindtap="{{b}}"><image src="{{a}}" class="action-icon back-icon"></image></view><view class="title-area"><text class="page-title">我的收藏</text></view><view class="right-action"></view></view></view><scroll-view class="scrollable-content" scroll-y enable-back-to-top="true" bindscrolltolower="{{p}}" refresher-enabled bindrefresherrefresh="{{q}}" refresher-triggered="{{r}}"><view class="card-container"><view wx:for="{{d}}" wx:for-item="item" wx:key="v" class="card-item"><view wx:if="{{e}}" class="select-area" catchtap="{{item.c}}"><view class="{{['checkbox', item.b && 'checked']}}"><view wx:if="{{item.a}}" class="checkbox-inner"></view></view></view><view class="card-content" bindtap="{{item.t}}"><view class="user-info"><image src="{{item.d}}" mode="aspectFill" class="user-avatar"></image><view class="user-details"><text class="user-name">{{item.e}}</text><view class="user-meta"><text class="publish-time">{{item.f}}</text><text class="trip-type">{{item.g}}</text></view></view></view><view class="route-info"><view class="route-points"><view class="start-point"><view class="point-marker start"></view><text class="point-text">{{item.h}}</text></view><view class="route-line"></view><view class="end-point"><view class="point-marker end"></view><text class="point-text">{{item.i}}</text></view></view><view class="trip-info"><view class="info-item"><image src="{{f}}" mode="aspectFit" class="info-icon"></image><text class="info-text">{{item.j}}</text></view><view class="info-item"><image src="{{g}}" mode="aspectFit" class="info-icon"></image><text class="info-text">{{item.k}}个座位</text></view><view wx:if="{{item.l}}" class="info-item"><image src="{{item.m}}" mode="aspectFit" class="info-icon"></image><text class="info-text price">¥{{item.n}}/人</text></view></view></view><view class="card-footer"><view class="meta-info"><view class="views"><image src="{{h}}" mode="aspectFit" class="meta-icon"></image><text class="meta-text">{{item.o}}</text></view><view class="messages"><image src="{{i}}" mode="aspectFit" class="meta-icon"></image><text class="meta-text">{{item.p}}</text></view></view><view class="action-area"><view wx:if="{{j}}" class="cancel-btn" catchtap="{{item.q}}"> 取消收藏 </view><view class="{{['status-tag', item.s]}}">{{item.r}}</view></view></view></view></view></view><view wx:if="{{k}}" class="empty-state"><image src="{{l}}" mode="aspectFit" class="empty-image"></image><text class="empty-text">暂无收藏信息</text><button class="browse-button" bindtap="{{m}}">去浏览</button></view><view wx:if="{{n}}" class="loading-state"><text class="loading-text">加载中...</text></view><view wx:if="{{o}}" class="list-bottom"><text class="bottom-text">— 已经到底啦 —</text></view></scroll-view><view wx:if="{{s}}" class="batch-action-bar"><view class="select-all" bindtap="{{w}}"><view class="{{['checkbox', v && 'checked']}}"><view wx:if="{{t}}" class="checkbox-inner"></view></view><text class="select-text">全选</text></view><button class="batch-button" disabled="{{y}}" bindtap="{{z}}">取消收藏({{x}})</button></view><view class="float-edit-btn" bindtap="{{B}}"><text>{{A}}</text></view></view>