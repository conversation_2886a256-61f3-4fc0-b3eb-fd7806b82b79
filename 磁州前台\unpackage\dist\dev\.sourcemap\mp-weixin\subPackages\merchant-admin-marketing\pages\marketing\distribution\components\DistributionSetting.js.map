{"version": 3, "file": "DistributionSetting.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/distribution/components/DistributionSetting.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7Avc3ViUGFja2FnZXMvbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nL3BhZ2VzL21hcmtldGluZy9kaXN0cmlidXRpb24vY29tcG9uZW50cy9EaXN0cmlidXRpb25TZXR0aW5nLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"distribution-setting-container\">\r\n    <view class=\"section-header\">\r\n      <text class=\"section-title\">分销设置</text>\r\n      <text class=\"section-subtitle\">设置此活动的分销规则和佣金</text>\r\n    </view>\r\n    \r\n    <!-- 启用分销开关 -->\r\n    <view class=\"switch-item\">\r\n      <view class=\"switch-label\">\r\n        <text class=\"label-text\">启用分销</text>\r\n        <text class=\"label-desc\">开启后，分销员可推广此活动获得佣金</text>\r\n      </view>\r\n      <switch \r\n        :checked=\"distributionEnabled\" \r\n        color=\"#6B0FBE\" \r\n        @change=\"toggleDistribution\"\r\n      />\r\n    </view>\r\n    \r\n    <!-- 分销设置内容 -->\r\n    <view class=\"distribution-content\" v-if=\"distributionEnabled\">\r\n      <!-- 佣金模式选择 -->\r\n      <view class=\"mode-options\">\r\n        <view \r\n          class=\"mode-option\" \r\n          :class=\"{ 'active': commissionMode === 'percentage' }\"\r\n          @click=\"updateCommissionMode('percentage')\"\r\n        >\r\n          <view class=\"option-icon percentage\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n              <path d=\"M19 5L5 19\" />\r\n              <circle cx=\"6.5\" cy=\"6.5\" r=\"2.5\" />\r\n              <circle cx=\"17.5\" cy=\"17.5\" r=\"2.5\" />\r\n            </svg>\r\n          </view>\r\n          <view class=\"option-content\">\r\n            <text class=\"option-title\">按比例计算</text>\r\n            <text class=\"option-desc\">按商品售价的百分比计算佣金</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view \r\n          class=\"mode-option\" \r\n          :class=\"{ 'active': commissionMode === 'fixed' }\"\r\n          @click=\"updateCommissionMode('fixed')\"\r\n        >\r\n          <view class=\"option-icon fixed\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n              <path d=\"M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H7\" />\r\n            </svg>\r\n          </view>\r\n          <view class=\"option-content\">\r\n            <text class=\"option-title\">固定金额</text>\r\n            <text class=\"option-desc\">每件商品固定金额佣金</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 佣金设置 -->\r\n      <view class=\"commission-levels\">\r\n        <view class=\"level-item\">\r\n          <view class=\"level-header\">\r\n            <view class=\"level-icon level1\"></view>\r\n            <text class=\"level-name\">一级分销</text>\r\n          </view>\r\n          <view class=\"level-input-wrap\">\r\n            <input \r\n              class=\"level-input\" \r\n              type=\"digit\" \r\n              v-model=\"level1Commission\" \r\n              :placeholder=\"commissionMode === 'percentage' ? '佣金比例' : '固定金额'\"\r\n              @input=\"updateCommission('level1', $event)\"\r\n            />\r\n            <text class=\"input-unit\">{{commissionMode === 'percentage' ? '%' : '元'}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"level-item\">\r\n          <view class=\"level-header\">\r\n            <view class=\"level-icon level2\"></view>\r\n            <text class=\"level-name\">二级分销</text>\r\n          </view>\r\n          <view class=\"level-input-wrap\">\r\n            <input \r\n              class=\"level-input\" \r\n              type=\"digit\" \r\n              v-model=\"level2Commission\" \r\n              :placeholder=\"commissionMode === 'percentage' ? '佣金比例' : '固定金额'\"\r\n              @input=\"updateCommission('level2', $event)\"\r\n            />\r\n            <text class=\"input-unit\">{{commissionMode === 'percentage' ? '%' : '元'}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"level-item\" v-if=\"enableLevel3\">\r\n          <view class=\"level-header\">\r\n            <view class=\"level-icon level3\"></view>\r\n            <text class=\"level-name\">三级分销</text>\r\n          </view>\r\n          <view class=\"level-input-wrap\">\r\n            <input \r\n              class=\"level-input\" \r\n              type=\"digit\" \r\n              v-model=\"level3Commission\" \r\n              :placeholder=\"commissionMode === 'percentage' ? '佣金比例' : '固定金额'\"\r\n              @input=\"updateCommission('level3', $event)\"\r\n            />\r\n            <text class=\"input-unit\">{{commissionMode === 'percentage' ? '%' : '元'}}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 三级分销开关 -->\r\n      <view class=\"switch-item\">\r\n        <view class=\"switch-label\">\r\n          <text class=\"label-text\">启用三级分销</text>\r\n        </view>\r\n        <switch \r\n          :checked=\"enableLevel3\" \r\n          color=\"#6B0FBE\" \r\n          @change=\"toggleLevel3\"\r\n        />\r\n      </view>\r\n      \r\n      <!-- 佣金提示 -->\r\n      <view class=\"commission-tips\">\r\n        <view class=\"tip-icon\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\" fill=\"none\" stroke=\"#6B0FBE\" stroke-width=\"2\">\r\n            <circle cx=\"12\" cy=\"12\" r=\"10\" />\r\n            <path d=\"M12 8v4M12 16h.01\" />\r\n          </svg>\r\n        </view>\r\n        <text class=\"tip-text\">{{commissionMode === 'percentage' ? '佣金比例总和建议不超过30%，以保证合理利润' : '佣金金额总和建议不超过商品价格的30%'}}</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'DistributionSetting',\r\n  props: {\r\n    // 初始分销设置\r\n    initialSettings: {\r\n      type: Object,\r\n      default: () => ({\r\n        enabled: false,\r\n        commissionMode: 'percentage', // 'percentage' 或 'fixed'\r\n        commissions: {\r\n          level1: '',\r\n          level2: '',\r\n          level3: ''\r\n        },\r\n        enableLevel3: false\r\n      })\r\n    },\r\n    // 是否禁用编辑\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      distributionEnabled: this.initialSettings.enabled,\r\n      commissionMode: this.initialSettings.commissionMode,\r\n      level1Commission: this.initialSettings.commissions.level1,\r\n      level2Commission: this.initialSettings.commissions.level2,\r\n      level3Commission: this.initialSettings.commissions.level3,\r\n      enableLevel3: this.initialSettings.enableLevel3\r\n    }\r\n  },\r\n  watch: {\r\n    initialSettings: {\r\n      handler(newVal) {\r\n        this.distributionEnabled = newVal.enabled;\r\n        this.commissionMode = newVal.commissionMode;\r\n        this.level1Commission = newVal.commissions.level1;\r\n        this.level2Commission = newVal.commissions.level2;\r\n        this.level3Commission = newVal.commissions.level3;\r\n        this.enableLevel3 = newVal.enableLevel3;\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  methods: {\r\n    // 切换是否启用分销\r\n    toggleDistribution(e) {\r\n      this.distributionEnabled = e.detail.value;\r\n      this.emitUpdate();\r\n    },\r\n    \r\n    // 更新佣金模式\r\n    updateCommissionMode(mode) {\r\n      if (this.disabled) return;\r\n      this.commissionMode = mode;\r\n      this.emitUpdate();\r\n    },\r\n    \r\n    // 更新佣金值\r\n    updateCommission(level, e) {\r\n      if (this.disabled) return;\r\n      this[`${level}Commission`] = e.detail.value;\r\n      this.emitUpdate();\r\n    },\r\n    \r\n    // 切换是否启用三级分销\r\n    toggleLevel3(e) {\r\n      if (this.disabled) return;\r\n      this.enableLevel3 = e.detail.value;\r\n      this.emitUpdate();\r\n    },\r\n    \r\n    // 向父组件发送更新事件\r\n    emitUpdate() {\r\n      this.$emit('update', {\r\n        enabled: this.distributionEnabled,\r\n        commissionMode: this.commissionMode,\r\n        commissions: {\r\n          level1: this.level1Commission,\r\n          level2: this.level2Commission,\r\n          level3: this.level3Commission\r\n        },\r\n        enableLevel3: this.enableLevel3\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.distribution-setting-container {\r\n  background-color: #FFFFFF;\r\n  border-radius: 12px;\r\n  padding: 16px;\r\n  margin-bottom: 16px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.section-header {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333333;\r\n}\r\n\r\n.section-subtitle {\r\n  font-size: 12px;\r\n  color: #999999;\r\n  margin-top: 4px;\r\n}\r\n\r\n.switch-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 0;\r\n  border-bottom: 1px solid #F5F5F5;\r\n}\r\n\r\n.switch-label {\r\n  flex: 1;\r\n}\r\n\r\n.label-text {\r\n  font-size: 14px;\r\n  color: #333333;\r\n}\r\n\r\n.label-desc {\r\n  font-size: 12px;\r\n  color: #999999;\r\n  margin-top: 4px;\r\n  display: block;\r\n}\r\n\r\n.distribution-content {\r\n  margin-top: 16px;\r\n}\r\n\r\n.mode-options {\r\n  display: flex;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.mode-option {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px;\r\n  border: 1px solid #EEEEEE;\r\n  border-radius: 8px;\r\n  margin-right: 12px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.mode-option:last-child {\r\n  margin-right: 0;\r\n}\r\n\r\n.mode-option.active {\r\n  border-color: #6B0FBE;\r\n  background-color: rgba(107, 15, 190, 0.05);\r\n}\r\n\r\n.option-icon {\r\n  width: 36px;\r\n  height: 36px;\r\n  border-radius: 18px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 12px;\r\n}\r\n\r\n.option-icon.percentage {\r\n  background-color: rgba(107, 15, 190, 0.1);\r\n  color: #6B0FBE;\r\n}\r\n\r\n.option-icon.fixed {\r\n  background-color: rgba(107, 15, 190, 0.1);\r\n  color: #6B0FBE;\r\n}\r\n\r\n.option-content {\r\n  flex: 1;\r\n}\r\n\r\n.option-title {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #333333;\r\n}\r\n\r\n.option-desc {\r\n  font-size: 12px;\r\n  color: #999999;\r\n  margin-top: 4px;\r\n}\r\n\r\n.commission-levels {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.level-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 0;\r\n  border-bottom: 1px solid #F5F5F5;\r\n}\r\n\r\n.level-header {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.level-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 8px;\r\n  position: relative;\r\n}\r\n\r\n.level-icon.level1 {\r\n  background-color: #FF9500;\r\n}\r\n\r\n.level-icon.level1::after {\r\n  content: '1';\r\n  color: #FFFFFF;\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n}\r\n\r\n.level-icon.level2 {\r\n  background-color: #34C759;\r\n}\r\n\r\n.level-icon.level2::after {\r\n  content: '2';\r\n  color: #FFFFFF;\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n}\r\n\r\n.level-icon.level3 {\r\n  background-color: #007AFF;\r\n}\r\n\r\n.level-icon.level3::after {\r\n  content: '3';\r\n  color: #FFFFFF;\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n}\r\n\r\n.level-name {\r\n  font-size: 14px;\r\n  color: #333333;\r\n}\r\n\r\n.level-input-wrap {\r\n  display: flex;\r\n  align-items: center;\r\n  background-color: #F5F5F5;\r\n  border-radius: 4px;\r\n  padding: 0 12px;\r\n  height: 36px;\r\n}\r\n\r\n.level-input {\r\n  width: 80px;\r\n  height: 36px;\r\n  font-size: 14px;\r\n  text-align: right;\r\n}\r\n\r\n.input-unit {\r\n  font-size: 14px;\r\n  color: #999999;\r\n  margin-left: 4px;\r\n}\r\n\r\n.commission-tips {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  padding: 12px;\r\n  background-color: rgba(107, 15, 190, 0.05);\r\n  border-radius: 8px;\r\n  margin-top: 16px;\r\n}\r\n\r\n.tip-icon {\r\n  margin-right: 8px;\r\n  margin-top: 2px;\r\n}\r\n\r\n.tip-text {\r\n  font-size: 12px;\r\n  color: #666666;\r\n  line-height: 1.5;\r\n  flex: 1;\r\n}\r\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/distribution/components/DistributionSetting.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;AA4IA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA;AAAA,IAEL,iBAAiB;AAAA,MACf,MAAM;AAAA,MACN,SAAS,OAAO;AAAA,QACd,SAAS;AAAA,QACT,gBAAgB;AAAA;AAAA,QAChB,aAAa;AAAA,UACX,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,QACD,cAAc;AAAA;IAEjB;AAAA;AAAA,IAED,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,qBAAqB,KAAK,gBAAgB;AAAA,MAC1C,gBAAgB,KAAK,gBAAgB;AAAA,MACrC,kBAAkB,KAAK,gBAAgB,YAAY;AAAA,MACnD,kBAAkB,KAAK,gBAAgB,YAAY;AAAA,MACnD,kBAAkB,KAAK,gBAAgB,YAAY;AAAA,MACnD,cAAc,KAAK,gBAAgB;AAAA,IACrC;AAAA,EACD;AAAA,EACD,OAAO;AAAA,IACL,iBAAiB;AAAA,MACf,QAAQ,QAAQ;AACd,aAAK,sBAAsB,OAAO;AAClC,aAAK,iBAAiB,OAAO;AAC7B,aAAK,mBAAmB,OAAO,YAAY;AAC3C,aAAK,mBAAmB,OAAO,YAAY;AAC3C,aAAK,mBAAmB,OAAO,YAAY;AAC3C,aAAK,eAAe,OAAO;AAAA,MAC5B;AAAA,MACD,MAAM;AAAA,IACR;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,mBAAmB,GAAG;AACpB,WAAK,sBAAsB,EAAE,OAAO;AACpC,WAAK,WAAU;AAAA,IAChB;AAAA;AAAA,IAGD,qBAAqB,MAAM;AACzB,UAAI,KAAK;AAAU;AACnB,WAAK,iBAAiB;AACtB,WAAK,WAAU;AAAA,IAChB;AAAA;AAAA,IAGD,iBAAiB,OAAO,GAAG;AACzB,UAAI,KAAK;AAAU;AACnB,WAAK,GAAG,KAAK,YAAY,IAAI,EAAE,OAAO;AACtC,WAAK,WAAU;AAAA,IAChB;AAAA;AAAA,IAGD,aAAa,GAAG;AACd,UAAI,KAAK;AAAU;AACnB,WAAK,eAAe,EAAE,OAAO;AAC7B,WAAK,WAAU;AAAA,IAChB;AAAA;AAAA,IAGD,aAAa;AACX,WAAK,MAAM,UAAU;AAAA,QACnB,SAAS,KAAK;AAAA,QACd,gBAAgB,KAAK;AAAA,QACrB,aAAa;AAAA,UACX,QAAQ,KAAK;AAAA,UACb,QAAQ,KAAK;AAAA,UACb,QAAQ,KAAK;AAAA,QACd;AAAA,QACD,cAAc,KAAK;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnOA,GAAG,gBAAgB,SAAS;"}