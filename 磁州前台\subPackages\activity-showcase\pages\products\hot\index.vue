<template>
  <view class="product-detail-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="navigateBack">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M19 12H5M12 19l-7-7 7-7" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        <view class="navbar-title">商品详情</view>
        <view class="navbar-right">
          <view class="share-btn" @click="shareProduct">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <path d="M4 12v8a2 2 0 002 2h12a2 2 0 002-2v-8M16 6l-4-4-4 4M12 2v13" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y :scroll-with-animation="true">
      <!-- 商品轮播图 -->
      <swiper class="product-swiper" indicator-dots autoplay circular
        :indicator-color="'rgba(255, 255, 255, 0.6)'"
        :indicator-active-color="'#ffffff'"
        :interval="5000"
        :duration="500"
      >
        <swiper-item v-for="(image, index) in product.images" :key="index">
          <view class="swiper-item">
            <image class="product-image" :src="image" mode="aspectFill"></image>
          </view>
        </swiper-item>
      </swiper>

      <!-- 价格信息 -->
      <view class="price-section">
        <view class="price-row">
          <view class="current-price">¥{{ product.price.toFixed(2) }}</view>
          <view class="original-price">¥{{ product.originalPrice.toFixed(2) }}</view>
          <view class="discount-badge">{{ discount }}折</view>
        </view>
        <view class="sales-info">已售 {{ product.soldCount }} | 库存 {{ product.stock }}</view>
      </view>

      <!-- 商品标题 -->
      <view class="product-title-section">
        <view class="product-title">{{ product.title }}</view>
        <view class="collect-btn" @click="toggleFavorite">
          <view class="collect-icon" :class="{ active: isFavorite }">
            <svg viewBox="0 0 24 24" width="22" height="22">
              <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"
                :fill="isFavorite ? '#FF3B69' : 'none'" 
                :stroke="isFavorite ? '#FF3B69' : '#8E8E93'" 
                stroke-width="2" />
            </svg>
          </view>
          <text>{{ isFavorite ? '已收藏' : '收藏' }}</text>
        </view>
      </view>

      <!-- 商品标签 -->
      <view class="tags-section">
        <view class="tag-item" v-for="(tag, index) in product.labels" :key="index">
          <view class="tag-badge" :class="tag.type">{{ tag.text }}</view>
        </view>
      </view>

      <!-- 促销信息 -->
      <view class="promotion-section" v-if="product.promotions && product.promotions.length > 0">
        <view class="section-title">促销信息</view>
        <view class="promotion-list">
          <view class="promotion-item" v-for="(promo, index) in product.promotions" :key="index">
            <view class="promo-badge" :class="promo.type">{{ promo.type === 'coupon' ? '券' : '减' }}</view>
            <text class="promo-text">{{ promo.text }}</text>
          </view>
        </view>
      </view>

      <!-- 选择规格 -->
      <view class="spec-section" @click="openSpecSelector">
        <view class="section-title">选择规格</view>
        <view class="spec-content">
          <text class="spec-text">{{ selectedSpec || '请选择规格数量' }}</text>
          <svg class="arrow-icon" viewBox="0 0 24 24" width="16" height="16">
            <path d="M9 18l6-6-6-6" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
      </view>

      <!-- 店铺信息 -->
      <view class="shop-section" @click="navigateToShop">
        <image class="shop-logo" :src="product.shopLogo" mode="aspectFill"></image>
        <view class="shop-info">
          <view class="shop-name">{{ product.shopName }}</view>
          <view class="shop-rating">
            <view class="rating-stars">
              <view class="star" v-for="i in 5" :key="i">
                <svg viewBox="0 0 24 24" width="14" height="14">
                  <path d="M12 2l2.4 7.2H22l-6 4.8 2.4 7.2-6-4.8-6 4.8 2.4-7.2-6-4.8h7.6z" :fill="i <= shop.rating ? '#FF9500' : '#E0E0E0'" />
                </svg>
              </view>
            </view>
            <text class="rating-text">{{ shop.rating.toFixed(1) }}</text>
          </view>
        </view>
        <view class="enter-shop-btn">
          <text>进店</text>
          <svg class="arrow-icon" viewBox="0 0 24 24" width="14" height="14">
            <path d="M9 18l6-6-6-6" stroke="#FF3B69" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
      </view>

      <!-- 商品详情 -->
      <view class="detail-section">
        <view class="detail-title">商品详情</view>
        <view class="detail-content">
          <rich-text :nodes="product.description"></rich-text>
          <view class="detail-images">
            <image 
              v-for="(image, index) in product.detailImages" 
              :key="index"
              :src="image"
              mode="widthFix"
              class="detail-image"
            ></image>
          </view>
        </view>
      </view>

      <!-- 商品评价 -->
      <view class="reviews-section">
        <view class="section-header">
          <view class="section-title">商品评价</view>
          <view class="view-all" @click="navigateToReviews">
            <text>查看全部</text>
            <svg class="arrow-icon" viewBox="0 0 24 24" width="14" height="14">
              <path d="M9 18l6-6-6-6" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
        <view class="reviews-list" v-if="reviews.length > 0">
          <view class="review-item" v-for="(review, index) in reviews.slice(0, 2)" :key="index">
            <view class="review-header">
              <image class="user-avatar" :src="review.userAvatar" mode="aspectFill"></image>
              <view class="review-user-info">
                <view class="user-name">{{ review.userName }}</view>
                <view class="review-rating">
                  <view class="star" v-for="i in 5" :key="i">
                    <svg viewBox="0 0 24 24" width="12" height="12">
                      <path d="M12 2l2.4 7.2H22l-6 4.8 2.4 7.2-6-4.8-6 4.8 2.4-7.2-6-4.8h7.6z" :fill="i <= review.rating ? '#FF9500' : '#E0E0E0'" />
                    </svg>
                  </view>
                </view>
              </view>
              <view class="review-time">{{ review.time }}</view>
            </view>
            <view class="review-content">{{ review.content }}</view>
            <view class="review-images" v-if="review.images && review.images.length > 0">
              <image 
                v-for="(img, imgIndex) in review.images" 
                :key="imgIndex"
                :src="img"
                mode="aspectFill"
                class="review-image"
                @click="previewImage(review.images, imgIndex)"
              ></image>
            </view>
          </view>
        </view>
        <view class="empty-reviews" v-else>
          <text>暂无评价</text>
        </view>
      </view>

      <!-- 相关推荐 -->
      <view class="recommend-section">
        <view class="section-title">相关推荐</view>
        <scroll-view class="recommend-scroll" scroll-x show-scrollbar="false">
          <view class="recommend-list">
            <view class="recommend-item" v-for="(item, index) in relatedProducts" :key="index" @click="viewProductDetail(item)">
              <image class="recommend-image" :src="item.coverImage" mode="aspectFill"></image>
              <view class="recommend-info">
                <text class="recommend-title">{{ item.title }}</text>
                <view class="recommend-price">¥{{ item.price.toFixed(2) }}</view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="bottom-left">
        <view class="action-btn shop-btn" @click="navigateToShop">
          <svg viewBox="0 0 24 24" width="20" height="20">
            <path d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none" />
            <path d="M9 22V12h6v10" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none" />
          </svg>
          <text>店铺</text>
        </view>
        <view class="action-btn cart-btn" @click="navigateToCart">
          <svg viewBox="0 0 24 24" width="20" height="20">
            <path d="M9 22a1 1 0 100-2 1 1 0 000 2zM20 22a1 1 0 100-2 1 1 0 000 2zM1 1h4l2.68 13.39a2 2 0 002 1.61h9.72a2 2 0 002-1.61L23 6H6" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none" />
          </svg>
          <text>购物车</text>
          <view class="cart-badge" v-if="cartCount > 0">{{ cartCount }}</view>
        </view>
      </view>
      <view class="bottom-right">
        <view class="add-to-cart-btn" @click="addToCart">加入购物车</view>
        <view class="buy-now-btn" @click="buyNow">立即购买</view>
      </view>
    </view>

    <!-- 规格选择弹窗 -->
    <view class="spec-popup" v-if="showSpecSelector" @click.stop="closeSpecSelector">
      <view class="spec-popup-content" @click.stop>
        <view class="spec-popup-header">
          <image class="spec-product-image" :src="product.coverImage" mode="aspectFill"></image>
          <view class="spec-product-info">
            <view class="spec-product-price">¥{{ selectedSku ? selectedSku.price.toFixed(2) : product.price.toFixed(2) }}</view>
            <view class="spec-product-stock">库存: {{ selectedSku ? selectedSku.stock : product.stock }}</view>
            <view class="spec-selected">{{ getSelectedSpecText() }}</view>
          </view>
          <view class="spec-close-btn" @click="closeSpecSelector">
            <svg viewBox="0 0 24 24" width="20" height="20">
              <path d="M18 6L6 18M6 6l12 12" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
          </view>
        </view>
        
        <view class="spec-options">
          <view class="spec-option-group" v-for="(group, groupIndex) in product.specs" :key="groupIndex">
            <view class="spec-option-title">{{ group.title }}</view>
            <view class="spec-option-list">
              <view 
                class="spec-option-item" 
                v-for="(option, optionIndex) in group.options" 
                :key="optionIndex"
                :class="{ active: selectedSpecMap[group.title] === option, disabled: !isOptionAvailable(group.title, option) }"
                @click="selectSpec(group.title, option)"
              >
                {{ option }}
              </view>
            </view>
          </view>
        </view>
        
        <view class="quantity-selector">
          <view class="quantity-title">数量</view>
          <view class="quantity-control">
            <view class="quantity-btn minus" :class="{ disabled: quantity <= 1 }" @click="decreaseQuantity">
              <svg viewBox="0 0 24 24" width="20" height="20">
                <path d="M5 12h14" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </view>
            <input class="quantity-input" type="number" v-model.number="quantity" />
            <view class="quantity-btn plus" :class="{ disabled: quantity >= maxQuantity }" @click="increaseQuantity">
              <svg viewBox="0 0 24 24" width="20" height="20">
                <path d="M12 5v14M5 12h14" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </view>
          </view>
        </view>
        
        <view class="spec-popup-footer">
          <view class="add-to-cart-btn" @click="confirmAddToCart">加入购物车</view>
          <view class="buy-now-btn" @click="confirmBuyNow">立即购买</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 商品数据
const product = ref({
  id: 1,
  title: 'iPhone 14 Pro 深空黑 256G',
  price: 7999,
  originalPrice: 8999,
  stock: 986,
  soldCount: 235,
  coverImage: 'https://via.placeholder.com/300x300',
  images: [
    'https://via.placeholder.com/750x750',
    'https://via.placeholder.com/750x750',
    'https://via.placeholder.com/750x750'
  ],
  detailImages: [
    'https://via.placeholder.com/750x1500',
    'https://via.placeholder.com/750x1200'
  ],
  shopName: 'Apple授权专卖店',
  shopLogo: 'https://via.placeholder.com/100',
  description: '<p>iPhone 14 Pro 搭载 A16 仿生芯片，超强性能与电池续航，支持 5G 网络。</p><p>4800万像素主摄 + 1200万像素超广角 + 1200万像素长焦，带来专业级摄影体验。</p>',
  labels: [
    { type: 'discount', text: '满3000减300' },
    { type: 'new', text: '新品' }
  ],
  promotions: [
    { type: 'discount', text: '满3000减300' },
    { type: 'coupon', text: '新用户立减200元' }
  ],
  specs: [
    {
      title: '颜色',
      options: ['深空黑', '银色', '金色', '暗紫色']
    },
    {
      title: '容量',
      options: ['128GB', '256GB', '512GB', '1TB']
    }
  ],
  skus: [
    { specs: { '颜色': '深空黑', '容量': '256GB' }, price: 7999, stock: 986 },
    { specs: { '颜色': '深空黑', '容量': '512GB' }, price: 9999, stock: 568 },
    { specs: { '颜色': '银色', '容量': '256GB' }, price: 7999, stock: 756 },
    { specs: { '颜色': '银色', '容量': '512GB' }, price: 9999, stock: 325 },
    { specs: { '颜色': '金色', '容量': '256GB' }, price: 7999, stock: 421 },
    { specs: { '颜色': '金色', '容量': '512GB' }, price: 9999, stock: 132 },
    { specs: { '颜色': '暗紫色', '容量': '256GB' }, price: 7999, stock: 675 },
    { specs: { '颜色': '暗紫色', '容量': '512GB' }, price: 9999, stock: 289 }
  ]
});

// 店铺数据
const shop = ref({
  id: 1,
  name: 'Apple授权专卖店',
  logo: 'https://via.placeholder.com/100',
  rating: 4.8,
  productCount: 128,
  followersCount: 5689
});

// 评价数据
const reviews = ref([
  {
    id: 1,
    userName: '用户123456',
    userAvatar: 'https://via.placeholder.com/50',
    rating: 5,
    content: '手机非常好用，外观漂亮，系统流畅，续航也很棒，相机拍照效果很赞，总体来说非常满意！',
    time: '2023-07-15',
    images: [
      'https://via.placeholder.com/200x200',
      'https://via.placeholder.com/200x200',
      'https://via.placeholder.com/200x200'
    ]
  },
  {
    id: 2,
    userName: '磁县用户',
    userAvatar: 'https://via.placeholder.com/50',
    rating: 4,
    content: '手机整体不错，就是价格有点贵，但质量和体验确实很好，值得购买。',
    time: '2023-07-10',
    images: []
  }
]);

// 相关推荐
const relatedProducts = ref([
  {
    id: 2,
    title: 'iPhone 14 银色 128G',
    coverImage: 'https://via.placeholder.com/300x300',
    price: 6999,
    originalPrice: 7599
  },
  {
    id: 3,
    title: 'iPhone 14 Pro Max 暗紫色 256G',
    coverImage: 'https://via.placeholder.com/300x300',
    price: 8999,
    originalPrice: 9999
  },
  {
    id: 4,
    title: 'AirPods Pro 2代',
    coverImage: 'https://via.placeholder.com/300x300',
    price: 1999,
    originalPrice: 2199
  },
  {
    id: 5,
    title: 'Apple Watch Series 8',
    coverImage: 'https://via.placeholder.com/300x300',
    price: 2999,
    originalPrice: 3299
  }
]);

// 页面状态
const isFavorite = ref(false);
const cartCount = ref(5);
const showSpecSelector = ref(false);
const selectedSpecMap = ref({});
const quantity = ref(1);
const maxQuantity = ref(10);

// 计算折扣
const discount = computed(() => {
  return Math.floor((product.value.price / product.value.originalPrice) * 10);
});

// 选中的规格文本
const selectedSpec = computed(() => {
  const specs = Object.entries(selectedSpecMap.value).map(([key, value]) => `${key}:${value}`);
  return specs.length > 0 ? specs.join(', ') : '';
});

// 根据选中的规格获取SKU
const selectedSku = computed(() => {
  const specs = selectedSpecMap.value;
  const specKeys = Object.keys(specs);
  
  // 检查是否所有规格都已选择
  if (specKeys.length !== product.value.specs.length) {
    return null;
  }
  
  // 查找匹配的SKU
  return product.value.skus.find(sku => {
    return specKeys.every(key => sku.specs[key] === specs[key]);
  });
});

// 选择规格
function selectSpec(title, option) {
  if (!isOptionAvailable(title, option)) return;
  
  selectedSpecMap.value = {
    ...selectedSpecMap.value,
    [title]: option
  };
}

// 判断规格选项是否可选
function isOptionAvailable(title, option) {
  // 复制当前已选规格
  const currentSpecs = { ...selectedSpecMap.value, [title]: option };
  const selectedKeys = Object.keys(currentSpecs);
  
  // 如果不是全部规格都已选择，则判断是否有对应的SKU
  if (selectedKeys.length < product.value.specs.length) {
    return product.value.skus.some(sku => {
      return selectedKeys.every(key => sku.specs[key] === currentSpecs[key]);
    });
  }
  
  // 如果全部规格都已选择，则判断是否有库存
  const matchedSku = product.value.skus.find(sku => {
    return selectedKeys.every(key => sku.specs[key] === currentSpecs[key]);
  });
  
  return matchedSku && matchedSku.stock > 0;
}

// 获取已选规格文本
function getSelectedSpecText() {
  const specs = Object.entries(selectedSpecMap.value);
  if (specs.length === 0) {
    return '请选择规格';
  }
  return specs.map(([key, value]) => `已选: ${key} ${value}`).join(', ');
}

// 增加数量
function increaseQuantity() {
  if (quantity.value < maxQuantity.value) {
    quantity.value++;
  }
}

// 减少数量
function decreaseQuantity() {
  if (quantity.value > 1) {
    quantity.value--;
  }
}

// 打开规格选择器
function openSpecSelector() {
  showSpecSelector.value = true;
}

// 关闭规格选择器
function closeSpecSelector() {
  showSpecSelector.value = false;
}

// 添加到购物车
function addToCart() {
  openSpecSelector();
}

// 确认添加到购物车
function confirmAddToCart() {
  if (!selectedSku.value) {
    uni.showToast({
      title: '请选择完整规格',
      icon: 'none'
    });
    return;
  }
  
  cartCount.value += quantity.value;
  
  uni.showToast({
    title: '已添加到购物车',
    icon: 'success'
  });
  
  closeSpecSelector();
}

// 立即购买
function buyNow() {
  openSpecSelector();
}

// 确认立即购买
function confirmBuyNow() {
  if (!selectedSku.value) {
    uni.showToast({
      title: '请选择完整规格',
      icon: 'none'
    });
    return;
  }
  
  navigateTo('/subPackages/activity-showcase/pages/orders/confirm?productId=' + product.value.id);
  closeSpecSelector();
}

// 切换收藏状态
function toggleFavorite() {
  isFavorite.value = !isFavorite.value;
  uni.showToast({
    title: isFavorite.value ? '已收藏' : '已取消收藏',
    icon: 'none'
  });
}

// 分享商品
function shareProduct() {
  uni.showShareMenu({
    withShareTicket: true,
    success() {
      console.log('打开分享菜单成功');
    },
    fail() {
      uni.showToast({
        title: '分享功能开发中',
        icon: 'none'
      });
    }
  });
}

// 预览图片
function previewImage(images, current) {
  uni.previewImage({
    urls: images,
    current: current
  });
}

// 导航到店铺
function navigateToShop() {
  navigateTo(`/subPackages/activity-showcase/pages/shops/detail?id=${shop.value.id}`);
}

// 导航到购物车
function navigateToCart() {
  navigateTo('/subPackages/activity-showcase/pages/cart/index');
}

// 导航到评价列表
function navigateToReviews() {
  navigateTo(`/subPackages/activity-showcase/pages/products/reviews?id=${product.value.id}`);
}

// 查看商品详情
function viewProductDetail(product) {
  navigateTo(`/subPackages/activity-showcase/pages/products/detail?id=${product.id}`);
}

// 导航通用方法
function navigateTo(url) {
  uni.navigateTo({
    url,
    fail: (err) => {
      console.error('导航失败:', err);
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    }
  });
}

// 返回上一页
function navigateBack() {
  uni.navigateBack();
}

// 页面加载
onMounted(() => {
  // 从路由参数获取商品ID
  const productId = Number(getApp().globalData.route.query.id) || 1;
  console.log('加载商品详情:', productId);
  
  // 这里可以根据ID请求商品数据
  // 目前使用模拟数据
});
</script>

<style lang="scss" scoped>
.product-detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F2F2F7;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
  
  .navbar-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 4px 6px rgba(255,59,105,0.15);
  }
  
  .navbar-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 0 30rpx;
    padding-top: var(--status-bar-height, 25px);
    box-sizing: border-box;
  }
  
  .navbar-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #FFFFFF;
    letter-spacing: 0.5px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  
  .back-btn, .share-btn {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .back-btn {
    position: absolute;
    left: 30rpx;
  }
  
  .navbar-right {
    position: absolute;
    right: 30rpx;
  }
}

/* 内容区域 */
.content-scroll {
  flex: 1;
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
  margin-bottom: 120rpx; /* 底部操作栏高度 */
}

/* 商品轮播图 */
.product-swiper {
  width: 100%;
  height: 750rpx;
  
  .swiper-item {
    width: 100%;
    height: 100%;
  }
  
  .product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

/* 价格信息 */
.price-section {
  background-color: #FFFFFF;
  padding: 30rpx;
  
  .price-row {
    display: flex;
    align-items: center;
  }
  
  .current-price {
    font-size: 48rpx;
    font-weight: 600;
    color: #FF3B69;
  }
  
  .original-price {
    font-size: 28rpx;
    color: #8E8E93;
    text-decoration: line-through;
    margin-left: 16rpx;
  }
  
  .discount-badge {
    margin-left: 16rpx;
    background-color: rgba(255, 59, 105, 0.1);
    color: #FF3B69;
    font-size: 24rpx;
    padding: 4rpx 12rpx;
    border-radius: 16rpx;
  }
  
  .sales-info {
    font-size: 24rpx;
    color: #8E8E93;
    margin-top: 12rpx;
  }
}

/* 商品标题 */
.product-title-section {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-top: 2rpx;
  display: flex;
  justify-content: space-between;
  
  .product-title {
    flex: 1;
    font-size: 32rpx;
    font-weight: 500;
    color: #333333;
    line-height: 1.5;
  }
  
  .collect-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-left: 30rpx;
    
    .collect-icon {
      margin-bottom: 6rpx;
      
      &.active {
        animation: heartBeat 0.5s ease;
      }
    }
    
    text {
      font-size: 22rpx;
      color: #8E8E93;
    }
  }
}

@keyframes heartBeat {
  0% { transform: scale(1); }
  14% { transform: scale(1.3); }
  28% { transform: scale(1); }
  42% { transform: scale(1.3); }
  70% { transform: scale(1); }
}

/* 商品标签 */
.tags-section {
  background-color: #FFFFFF;
  padding: 0 30rpx 30rpx;
  display: flex;
  flex-wrap: wrap;
  
  .tag-item {
    margin-right: 16rpx;
    margin-bottom: 16rpx;
    
    .tag-badge {
      display: inline-block;
      padding: 6rpx 16rpx;
      border-radius: 6rpx;
      font-size: 24rpx;
      
      &.discount {
        background-color: rgba(255, 149, 0, 0.1);
        color: #FF9500;
      }
      
      &.new {
        background-color: rgba(90, 200, 250, 0.1);
        color: #5AC8FA;
      }
      
      &.hot {
        background-color: rgba(255, 59, 48, 0.1);
        color: #FF3B30;
      }
      
      &.coupon {
        background-color: rgba(255, 45, 85, 0.1);
        color: #FF2D55;
      }
    }
  }
}

/* 促销信息 */
.promotion-section {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-top: 20rpx;
  
  .section-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #333333;
    margin-bottom: 20rpx;
  }
  
  .promotion-list {
    .promotion-item {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;
      
      .promo-badge {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40rpx;
        height: 40rpx;
        border-radius: 8rpx;
        font-size: 22rpx;
        color: #FFFFFF;
        margin-right: 16rpx;
        
        &.discount {
          background-color: #FF9500;
        }
        
        &.coupon {
          background-color: #FF2D55;
        }
      }
      
      .promo-text {
        font-size: 26rpx;
        color: #333333;
      }
    }
  }
}

/* 选择规格 */
.spec-section {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-top: 2rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .section-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #333333;
  }
  
  .spec-content {
    display: flex;
    align-items: center;
    
    .spec-text {
      font-size: 26rpx;
      color: #8E8E93;
      margin-right: 10rpx;
    }
  }
}

/* 店铺信息 */
.shop-section {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-top: 20rpx;
  display: flex;
  align-items: center;
  
  .shop-logo {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    margin-right: 20rpx;
  }
  
  .shop-info {
    flex: 1;
    
    .shop-name {
      font-size: 28rpx;
      font-weight: 500;
      color: #333333;
      margin-bottom: 8rpx;
    }
    
    .shop-rating {
      display: flex;
      align-items: center;
      
      .rating-stars {
        display: flex;
        margin-right: 10rpx;
      }
      
      .rating-text {
        font-size: 24rpx;
        color: #FF9500;
      }
    }
  }
  
  .enter-shop-btn {
    display: flex;
    align-items: center;
    padding: 10rpx 20rpx;
    background-color: rgba(255, 59, 105, 0.1);
    border-radius: 30rpx;
    
    text {
      font-size: 24rpx;
      color: #FF3B69;
      margin-right: 6rpx;
    }
  }
}

/* 商品详情 */
.detail-section {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-top: 20rpx;
  
  .detail-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #333333;
    margin-bottom: 20rpx;
    position: relative;
    padding-left: 20rpx;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 8rpx;
      width: 8rpx;
      height: 28rpx;
      background: linear-gradient(to bottom, #FF3B69, #FF7A9E);
      border-radius: 4rpx;
    }
  }
  
  .detail-content {
    font-size: 26rpx;
    color: #333333;
    line-height: 1.6;
  }
  
  .detail-images {
    margin-top: 20rpx;
    
    .detail-image {
      width: 100%;
      height: auto;
      margin-bottom: 20rpx;
      border-radius: 12rpx;
    }
  }
}

/* 商品评价 */
.reviews-section {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-top: 20rpx;
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .section-title {
      font-size: 28rpx;
      font-weight: 500;
      color: #333333;
      position: relative;
      padding-left: 20rpx;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 8rpx;
        width: 8rpx;
        height: 28rpx;
        background: linear-gradient(to bottom, #FF3B69, #FF7A9E);
        border-radius: 4rpx;
      }
    }
    
    .view-all {
      display: flex;
      align-items: center;
      
      text {
        font-size: 24rpx;
        color: #8E8E93;
        margin-right: 6rpx;
      }
    }
  }
  
  .reviews-list {
    .review-item {
      padding-bottom: 20rpx;
      border-bottom: 1px solid #F2F2F7;
      margin-bottom: 20rpx;
      
      &:last-child {
        margin-bottom: 0;
        border-bottom: none;
      }
      
      .review-header {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;
        
        .user-avatar {
          width: 60rpx;
          height: 60rpx;
          border-radius: 50%;
          margin-right: 16rpx;
        }
        
        .review-user-info {
          flex: 1;
          
          .user-name {
            font-size: 26rpx;
            color: #333333;
            margin-bottom: 6rpx;
          }
          
          .review-rating {
            display: flex;
          }
        }
        
        .review-time {
          font-size: 22rpx;
          color: #8E8E93;
        }
      }
      
      .review-content {
        font-size: 26rpx;
        color: #333333;
        line-height: 1.5;
        margin-bottom: 16rpx;
      }
      
      .review-images {
        display: flex;
        flex-wrap: wrap;
        
        .review-image {
          width: 160rpx;
          height: 160rpx;
          border-radius: 8rpx;
          margin-right: 16rpx;
          margin-bottom: 16rpx;
        }
      }
    }
  }
  
  .empty-reviews {
    padding: 60rpx 0;
    text-align: center;
    
    text {
      font-size: 28rpx;
      color: #8E8E93;
    }
  }
}

/* 相关推荐 */
.recommend-section {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-top: 20rpx;
  
  .section-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #333333;
    margin-bottom: 20rpx;
    position: relative;
    padding-left: 20rpx;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 8rpx;
      width: 8rpx;
      height: 28rpx;
      background: linear-gradient(to bottom, #FF3B69, #FF7A9E);
      border-radius: 4rpx;
    }
  }
  
  .recommend-scroll {
    white-space: nowrap;
  }
  
  .recommend-list {
    display: inline-flex;
    padding: 10rpx 0;
  }
  
  .recommend-item {
    width: 240rpx;
    margin-right: 20rpx;
    
    .recommend-image {
      width: 240rpx;
      height: 240rpx;
      border-radius: 12rpx;
      margin-bottom: 12rpx;
    }
    
    .recommend-info {
      .recommend-title {
        font-size: 26rpx;
        color: #333333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 8rpx;
      }
      
      .recommend-price {
        font-size: 26rpx;
        font-weight: 500;
        color: #FF3B69;
      }
    }
  }
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 34px; /* iOS 安全区域高度 */
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background-color: #FFFFFF;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 90;
  
  .bottom-left {
    display: flex;
    flex: 1;
    
    .action-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100rpx;
      position: relative;
      
      svg {
        margin-bottom: 6rpx;
      }
      
      text {
        font-size: 22rpx;
        color: #8E8E93;
      }
      
      .cart-badge {
        position: absolute;
        top: 0;
        right: 10rpx;
        min-width: 32rpx;
        height: 32rpx;
        border-radius: 16rpx;
        background: #FF3B30;
        color: #FFFFFF;
        font-size: 20rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 6rpx;
        box-sizing: border-box;
      }
    }
  }
  
  .bottom-right {
    flex: 2;
    display: flex;
    
    .add-to-cart-btn, .buy-now-btn {
      flex: 1;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      font-weight: 500;
    }
    
    .add-to-cart-btn {
      background-color: rgba(255, 59, 105, 0.1);
      color: #FF3B69;
      border-radius: 40rpx 0 0 40rpx;
    }
    
    .buy-now-btn {
      background: linear-gradient(135deg, #FF3B69, #FF7A9E);
      color: #FFFFFF;
      border-radius: 0 40rpx 40rpx 0;
    }
  }
}

/* 规格选择弹窗 */
.spec-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  
  .spec-popup-content {
    background-color: #FFFFFF;
    border-radius: 30rpx 30rpx 0 0;
    padding: 30rpx;
    animation: slideUp 0.3s ease;
    max-height: 80vh;
    overflow-y: auto;
  }
  
  .spec-popup-header {
    display: flex;
    margin-bottom: 30rpx;
    position: relative;
    
    .spec-product-image {
      width: 160rpx;
      height: 160rpx;
      border-radius: 12rpx;
      margin-right: 20rpx;
    }
    
    .spec-product-info {
      flex: 1;
      
      .spec-product-price {
        font-size: 36rpx;
        font-weight: 600;
        color: #FF3B69;
        margin-bottom: 10rpx;
      }
      
      .spec-product-stock {
        font-size: 24rpx;
        color: #8E8E93;
        margin-bottom: 10rpx;
      }
      
      .spec-selected {
        font-size: 24rpx;
        color: #333333;
      }
    }
    
    .spec-close-btn {
      position: absolute;
      top: 0;
      right: 0;
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  
  .spec-options {
    .spec-option-group {
      margin-bottom: 30rpx;
      
      .spec-option-title {
        font-size: 28rpx;
        font-weight: 500;
        color: #333333;
        margin-bottom: 20rpx;
      }
      
      .spec-option-list {
        display: flex;
        flex-wrap: wrap;
        
        .spec-option-item {
          padding: 12rpx 24rpx;
          border-radius: 8rpx;
          background-color: #F2F2F7;
          font-size: 26rpx;
          color: #333333;
          margin-right: 20rpx;
          margin-bottom: 20rpx;
          
          &.active {
            background-color: rgba(255, 59, 105, 0.1);
            color: #FF3B69;
            border: 1px solid #FF3B69;
          }
          
          &.disabled {
            color: #C8C8C8;
            background-color: #F8F8F8;
            pointer-events: none;
          }
        }
      }
    }
  }
  
  .quantity-selector {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;
    
    .quantity-title {
      font-size: 28rpx;
      font-weight: 500;
      color: #333333;
    }
    
    .quantity-control {
      display: flex;
      align-items: center;
      
      .quantity-btn {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #E0E0E0;
        
        &.minus {
          border-radius: 8rpx 0 0 8rpx;
        }
        
        &.plus {
          border-radius: 0 8rpx 8rpx 0;
        }
        
        &.disabled {
          color: #C8C8C8;
          background-color: #F8F8F8;
          pointer-events: none;
        }
      }
      
      .quantity-input {
        width: 80rpx;
        height: 60rpx;
        border-top: 1px solid #E0E0E0;
        border-bottom: 1px solid #E0E0E0;
        text-align: center;
        font-size: 28rpx;
      }
    }
  }
  
  .spec-popup-footer {
    display: flex;
    
    .add-to-cart-btn, .buy-now-btn {
      flex: 1;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      font-weight: 500;
    }
    
    .add-to-cart-btn {
      background-color: rgba(255, 59, 105, 0.1);
      color: #FF3B69;
      border-radius: 40rpx 0 0 40rpx;
    }
    
    .buy-now-btn {
      background: linear-gradient(135deg, #FF3B69, #FF7A9E);
      color: #FFFFFF;
      border-radius: 0 40rpx 40rpx 0;
    }
  }
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}
</style>