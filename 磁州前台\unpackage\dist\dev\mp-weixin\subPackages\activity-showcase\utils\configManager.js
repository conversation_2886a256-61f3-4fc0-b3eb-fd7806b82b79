"use strict";
const common_vendor = require("../../../common/vendor.js");
const DEFAULT_CONFIG = {
  structure: {
    navbar: {
      enabled: true,
      title: "活动中心",
      backgroundColor: "#ffffff",
      textColor: "#333333",
      showBackButton: true,
      showCloseButton: false,
      showSearchButton: true,
      showShareButton: true,
      style: "default",
      height: 60,
      borderRadius: 0,
      shadow: "light",
      position: "fixed",
      zIndex: 1e3,
      animation: "fadeIn"
    },
    banner: {
      enabled: true,
      autoplay: true,
      interval: 5e3,
      duration: 500,
      circular: true,
      showIndicators: true,
      indicatorColor: "rgba(255, 255, 255, 0.6)",
      indicatorActiveColor: "#ffffff",
      indicatorPosition: "bottom-center",
      height: 200,
      borderRadius: 8,
      margin: 16,
      padding: 0,
      aspectRatio: "16:9",
      objectFit: "cover",
      lazyLoad: true,
      preloadImages: 2,
      touchable: true
    }
  },
  display: {
    cardStyle: {
      layout: "card",
      width: "auto",
      height: "auto",
      aspectRatio: "16:9",
      backgroundColor: "#ffffff",
      borderColor: "#e5e7eb",
      borderWidth: 1,
      borderRadius: 8,
      shadow: "light",
      padding: 16,
      margin: 8,
      overflow: "hidden",
      hoverEffect: true,
      hoverScale: 1.05,
      clickEffect: true,
      animationDuration: 300
    },
    imageDisplay: {
      enabled: true,
      width: "100%",
      height: "auto",
      aspectRatio: "16:9",
      objectFit: "cover",
      borderRadius: 8,
      border: "none",
      opacity: 1,
      brightness: 1,
      contrast: 1,
      saturation: 1,
      blur: 0,
      quality: 0.8,
      lazyLoad: true,
      placeholder: "",
      errorImage: "",
      loadingImage: "",
      preload: false,
      cache: true
    }
  },
  interaction: {},
  theme: {},
  data: {},
  system: {}
};
class ConfigManager {
  constructor() {
    this.config = DEFAULT_CONFIG;
    this.isLoaded = false;
    this.listeners = [];
  }
  /**
   * 初始化配置管理器
   */
  async init() {
    try {
      await this.loadConfig();
      this.applyConfig();
      this.isLoaded = true;
      common_vendor.index.__f__("log", "at subPackages/activity-showcase/utils/configManager.js:109", "活动展示配置加载成功");
    } catch (error) {
      common_vendor.index.__f__("warn", "at subPackages/activity-showcase/utils/configManager.js:111", "配置加载失败，使用默认配置:", error);
      this.config = DEFAULT_CONFIG;
      this.applyConfig();
      this.isLoaded = true;
    }
  }
  /**
   * 从本地文件加载配置
   */
  async loadConfig() {
    try {
      const response = await common_vendor.index.request({
        url: "/subPackages/activity-showcase/config/display-config.json",
        method: "GET"
      });
      if (response.statusCode === 200 && response.data) {
        this.config = { ...DEFAULT_CONFIG, ...response.data };
        return;
      }
    } catch (error) {
      common_vendor.index.__f__("log", "at subPackages/activity-showcase/utils/configManager.js:134", "本地配置文件不存在，尝试从后台API获取");
    }
    try {
      const response = await common_vendor.index.request({
        url: "http://localhost:8080/api/v1/activity-display/config",
        method: "GET",
        header: {
          "Content-Type": "application/json"
        }
      });
      if (response.statusCode === 200 && response.data && response.data.data) {
        this.config = { ...DEFAULT_CONFIG, ...response.data.data };
      }
    } catch (error) {
      common_vendor.index.__f__("warn", "at subPackages/activity-showcase/utils/configManager.js:151", "从后台API获取配置失败:", error);
      throw error;
    }
  }
  /**
   * 应用配置到页面
   */
  applyConfig() {
    this.applyNavbarConfig();
    this.applyBannerConfig();
    this.applyCardStyleConfig();
    this.applyImageDisplayConfig();
    this.notifyListeners();
  }
  /**
   * 应用导航栏配置
   */
  applyNavbarConfig() {
    var _a;
    const navbarConfig = (_a = this.config.structure) == null ? void 0 : _a.navbar;
    if (!navbarConfig || !navbarConfig.enabled)
      return;
    const root = document.documentElement;
    if (root) {
      root.style.setProperty("--navbar-bg-color", navbarConfig.backgroundColor);
      root.style.setProperty("--navbar-text-color", navbarConfig.textColor);
      root.style.setProperty("--navbar-height", `${navbarConfig.height}px`);
      root.style.setProperty("--navbar-border-radius", `${navbarConfig.borderRadius}px`);
      root.style.setProperty("--navbar-z-index", navbarConfig.zIndex);
    }
  }
  /**
   * 应用轮播图配置
   */
  applyBannerConfig() {
    var _a;
    const bannerConfig = (_a = this.config.structure) == null ? void 0 : _a.banner;
    if (!bannerConfig || !bannerConfig.enabled)
      return;
    const root = document.documentElement;
    if (root) {
      root.style.setProperty("--banner-height", `${bannerConfig.height}px`);
      root.style.setProperty("--banner-border-radius", `${bannerConfig.borderRadius}px`);
      root.style.setProperty("--banner-margin", `${bannerConfig.margin}px`);
      root.style.setProperty("--banner-padding", `${bannerConfig.padding}px`);
      root.style.setProperty("--banner-indicator-color", bannerConfig.indicatorColor);
      root.style.setProperty("--banner-indicator-active-color", bannerConfig.indicatorActiveColor);
    }
  }
  /**
   * 应用卡片样式配置
   */
  applyCardStyleConfig() {
    var _a;
    const cardConfig = (_a = this.config.display) == null ? void 0 : _a.cardStyle;
    if (!cardConfig)
      return;
    const root = document.documentElement;
    if (root) {
      root.style.setProperty("--card-bg-color", cardConfig.backgroundColor);
      root.style.setProperty("--card-border-color", cardConfig.borderColor);
      root.style.setProperty("--card-border-width", `${cardConfig.borderWidth}px`);
      root.style.setProperty("--card-border-radius", `${cardConfig.borderRadius}px`);
      root.style.setProperty("--card-padding", `${cardConfig.padding}px`);
      root.style.setProperty("--card-margin", `${cardConfig.margin}px`);
      root.style.setProperty("--card-hover-scale", cardConfig.hoverScale);
      root.style.setProperty("--card-animation-duration", `${cardConfig.animationDuration}ms`);
    }
  }
  /**
   * 应用图片显示配置
   */
  applyImageDisplayConfig() {
    var _a;
    const imageConfig = (_a = this.config.display) == null ? void 0 : _a.imageDisplay;
    if (!imageConfig || !imageConfig.enabled)
      return;
    const root = document.documentElement;
    if (root) {
      root.style.setProperty("--image-border-radius", `${imageConfig.borderRadius}px`);
      root.style.setProperty("--image-opacity", imageConfig.opacity);
      root.style.setProperty("--image-brightness", imageConfig.brightness);
      root.style.setProperty("--image-contrast", imageConfig.contrast);
      root.style.setProperty("--image-saturation", imageConfig.saturation);
      root.style.setProperty("--image-blur", `${imageConfig.blur}px`);
    }
  }
  /**
   * 获取配置值
   */
  getConfig(path) {
    const keys = path.split(".");
    let value = this.config;
    for (const key of keys) {
      if (value && typeof value === "object" && key in value) {
        value = value[key];
      } else {
        return void 0;
      }
    }
    return value;
  }
  /**
   * 检查功能是否启用
   */
  isEnabled(path) {
    const config = this.getConfig(path);
    return config && config.enabled === true;
  }
  /**
   * 添加配置变更监听器
   */
  addListener(callback) {
    this.listeners.push(callback);
  }
  /**
   * 移除配置变更监听器
   */
  removeListener(callback) {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }
  /**
   * 通知所有监听器
   */
  notifyListeners() {
    this.listeners.forEach((callback) => {
      try {
        callback(this.config);
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/activity-showcase/utils/configManager.js:301", "配置监听器执行失败:", error);
      }
    });
  }
  /**
   * 重新加载配置
   */
  async reload() {
    await this.loadConfig();
    this.applyConfig();
  }
  /**
   * 获取完整配置
   */
  getAllConfig() {
    return this.config;
  }
  /**
   * 检查配置是否已加载
   */
  isConfigLoaded() {
    return this.isLoaded;
  }
}
const configManager = new ConfigManager();
if (typeof getApp === "function") {
  const app = getApp();
  app.globalData = app.globalData || {};
  app.globalData.configManager = configManager;
}
exports.configManager = configManager;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/utils/configManager.js.map
