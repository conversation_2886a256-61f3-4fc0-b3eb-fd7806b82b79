{"version": 3, "file": "join.js", "sources": ["pages/business/join.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYnVzaW5lc3Mvam9pbi52dWU"], "sourcesContent": ["<template>\n\t<view class=\"join-page\">\n\t\t<!-- 高斯模糊背景 -->\n\t\t<view class=\"blur-bg\">\n\t\t\t<image src=\"/static/images/banner/banner-3.jpg\" mode=\"aspectFill\" class=\"bg-image\"></image>\n\t\t</view>\n\t\t\n\t\t<!-- 状态栏占位 -->\n\t\t<view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\n\t\t\n\t\t<!-- 导航栏 -->\n\t\t<view class=\"navbar\">\n\t\t\t<view class=\"navbar-left\" @click=\"goBack\">\n\t\t\t\t<image src=\"/static/images/tabbar/返回.png\" class=\"back-icon-img\"></image>\n\t\t\t</view>\n\t\t\t<text class=\"navbar-title\">商家入驻</text>\n\t\t\t<view class=\"navbar-right\"></view>\n\t\t</view>\n\t\t\n\t\t<!-- 内容区 -->\n\t\t<view class=\"content\">\n\t\t\t<!-- 入驻说明卡片 -->\n\t\t\t<view class=\"intro-card glass-card\">\n\t\t\t\t<view class=\"intro-header\">\n\t\t\t\t\t<view class=\"intro-icon-wrap\">\n\t\t\t\t\t\t<image src=\"/static/images/tabbar/入驻卡片.png\" class=\"intro-icon\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"intro-titles\">\n\t\t\t\t\t\t<text class=\"intro-title\">商家免费入驻</text>\n\t\t\t\t\t\t<view class=\"subtitle-wrapper\">\n\t\t\t\t\t\t\t<text class=\"intro-subtitle\">获取更多商业机会和客户资源</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"intro-stats\">\n\t\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t\t<text class=\"stat-num\">123.4万</text>\n\t\t\t\t\t\t<text class=\"stat-label\">月曝光量</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"stat-divider\"></view>\n\t\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t\t<text class=\"stat-num\">567</text>\n\t\t\t\t\t\t<text class=\"stat-label\">已入驻商家</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"stat-divider\"></view>\n\t\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t\t<text class=\"stat-num\">89%</text>\n\t\t\t\t\t\t<text class=\"stat-label\">转化率</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 表单区域 -->\n\t\t\t<view class=\"form-section glass-card\">\n\t\t\t\t<text class=\"form-title\">基本信息</text>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">店铺名称</text>\n\t\t\t\t\t<input type=\"text\" class=\"form-input\" placeholder=\"请输入商家名称\" v-model=\"formData.shopName\" />\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">详细地址</text>\n\t\t\t\t\t<view class=\"address-wrap\">\n\t\t\t\t\t\t<input type=\"text\" class=\"form-input address-input\" placeholder=\"请输入店铺地址\" v-model=\"formData.address\" />\n\t\t\t\t\t\t<view class=\"location-btn\" @click.stop=\"getLocation\">\n\t\t\t\t\t\t\t<image src=\"/static/images/tabbar/定位.png\" class=\"location-icon-img\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"location-tips\" v-if=\"formData.address\">\n\t\t\t\t\t\t<text class=\"location-text\">磁县正义路</text>\n\t\t\t\t\t\t<text class=\"location-btn-text\" @click=\"getLocation\">定位</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">所属行业</text>\n\t\t\t\t\t<view class=\"form-select\" @click=\"showCategoryPickerFn\">\n\t\t\t\t\t\t<text :class=\"{'placeholder': !formData.category}\">{{formData.category || '点击选择所属行业'}}</text>\n\t\t\t\t\t\t<image src=\"/static/images/tabbar/箭头.png\" class=\"select-arrow-img\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">商家规模</text>\n\t\t\t\t\t<view class=\"form-select\" @click=\"showScalePickerFn\">\n\t\t\t\t\t\t<text :class=\"{'placeholder': !formData.scale}\">{{formData.scale || '点击选择商家规模人数'}}</text>\n\t\t\t\t\t\t<image src=\"/static/images/tabbar/箭头.png\" class=\"select-arrow-img\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">营业时间</text>\n\t\t\t\t\t<view class=\"form-select\" @click=\"showTimePickerFn\">\n\t\t\t\t\t\t<text :class=\"{'placeholder': !formData.businessTime}\">{{formData.businessTime || '点击选择营业时间'}}</text>\n\t\t\t\t\t\t<image src=\"/static/images/tabbar/箭头.png\" class=\"select-arrow-img\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">联系电话</text>\n\t\t\t\t\t<input type=\"number\" class=\"form-input\" placeholder=\"请输入联系电话\" v-model=\"formData.contactPhone\" />\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">商家介绍</text>\n\t\t\t\t\t<textarea class=\"form-textarea\" placeholder=\"请填写商家介绍内容\" v-model=\"formData.description\"></textarea>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 图片上传区域 -->\n\t\t\t<view class=\"form-section glass-card\">\n\t\t\t\t<text class=\"form-title\">商家图片</text>\n\t\t\t\t<view class=\"upload-box\" @click=\"uploadImage('shopImage')\">\n\t\t\t\t\t<view class=\"upload-placeholder\">\n\t\t\t\t\t\t<text class=\"plus-icon\">+</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"form-section glass-card\">\n\t\t\t\t<text class=\"form-title\">商家logo</text>\n\t\t\t\t<view class=\"upload-box\" @click=\"uploadImage('logo')\">\n\t\t\t\t\t<view class=\"upload-placeholder\">\n\t\t\t\t\t\t<text class=\"plus-icon\">+</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"upload-desc-container\">\n\t\t\t\t\t\t<text class=\"upload-desc\">请上传商家logo</text>\n\t\t\t\t\t\t<text class=\"upload-desc\">或门头照片</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"form-section glass-card\">\n\t\t\t\t<text class=\"form-title\">客服微信二维码</text>\n\t\t\t\t<view class=\"upload-box\" @click=\"uploadImage('qrcode')\">\n\t\t\t\t\t<view class=\"upload-placeholder\">\n\t\t\t\t\t\t<text class=\"plus-icon\">+</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"upload-desc-container\">\n\t\t\t\t\t\t<text class=\"upload-desc\">请上传客服</text>\n\t\t\t\t\t\t<text class=\"upload-desc\">微信二维码</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"form-section glass-card\">\n\t\t\t\t<text class=\"form-title\">商家相册</text>\n\t\t\t\t<view class=\"upload-box\" @click=\"uploadImage('album')\">\n\t\t\t\t\t<view class=\"upload-placeholder\">\n\t\t\t\t\t\t<text class=\"plus-icon\">+</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"upload-desc\">最多上传10张照片</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 商家入驻推广操作 -->\n\t\t\t<view class=\"form-section glass-card\">\n\t\t\t\t<text class=\"form-title\">商家入驻</text>\n\t\t\t\t<ConfigurablePremiumActions\n\t\t\t\t\tpageType=\"merchant_join\"\n\t\t\t\t\tshowMode=\"selection\"\n\t\t\t\t\t:itemData=\"merchantJoinData\"\n\t\t\t\t\t@action-completed=\"handleJoinCompleted\"\n\t\t\t\t\t@action-cancelled=\"handleJoinCancelled\"\n\t\t\t\t/>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 付费入驻版本弹窗 -->\n\t\t\t<view class=\"popup-mask\" v-if=\"showVersionModal\" @click=\"hideVersionModal\"></view>\n\t\t\t<view class=\"popup-content version-popup\" v-if=\"showVersionModal\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<text class=\"popup-title\">选择付费入驻版本</text>\n\t\t\t\t\t<view class=\"popup-close\" @click=\"hideVersionModal\">\n\t\t\t\t\t\t<image src=\"/static/images/tabbar/关闭.png\" class=\"close-icon-img\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<scroll-view class=\"version-list\" scroll-y>\n\t\t\t\t\t<!-- 基础版 -->\n\t\t\t\t\t<view class=\"version-card apple-style\" :class=\"{'active': formData.version === 'basic'}\" @click=\"selectVersion('basic')\">\n\t\t\t\t\t\t<view class=\"version-card-bg basic-gradient\">\n\t\t\t\t\t\t\t<view class=\"version-pill\">基础版</view>\n\t\t\t\t\t\t\t<view class=\"version-content\">\n\t\t\t\t\t\t\t\t<view class=\"version-price-area\">\n\t\t\t\t\t\t\t\t\t<text class=\"version-price-label\">¥</text>\n\t\t\t\t\t\t\t\t\t<text class=\"version-price-value\">49.9</text>\n\t\t\t\t\t\t\t\t\t<text class=\"version-price-cycle\">/年</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"version-desc\">适合个体商户</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<view class=\"version-features\">\n\t\t\t\t\t\t\t\t\t<view class=\"version-feature-item\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"feature-dot\"></view>\n\t\t\t\t\t\t\t\t\t\t<text class=\"feature-text\">商品发布数量：最多20个</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"version-feature-item\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"feature-dot\"></view>\n\t\t\t\t\t\t\t\t\t\t<text class=\"feature-text\">店铺展示位置：普通位置</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"version-feature-item\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"feature-dot\"></view>\n\t\t\t\t\t\t\t\t\t\t<text class=\"feature-text\">客户数据分析：基础版</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"version-feature-item\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"feature-dot\"></view>\n\t\t\t\t\t\t\t\t\t\t<text class=\"feature-text\">免费获赠一次店铺推广</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<view class=\"version-select-btn\" :class=\"{'selected': formData.version === 'basic'}\">\n\t\t\t\t\t\t\t\t\t<text>{{ formData.version === 'basic' ? '已选择' : '选择此版本' }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 高级版 -->\n\t\t\t\t\t<view class=\"version-card apple-style\" :class=\"{'active': formData.version === 'premium'}\" @click=\"selectVersion('premium')\">\n\t\t\t\t\t\t<view class=\"version-card-bg premium-gradient\">\n\t\t\t\t\t\t\t<view class=\"version-badge\">\n\t\t\t\t\t\t\t\t<text class=\"badge-text\">热门</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"version-pill\">高级版</view>\n\t\t\t\t\t\t\t<view class=\"version-content\">\n\t\t\t\t\t\t\t\t<view class=\"version-price-area\">\n\t\t\t\t\t\t\t\t\t<text class=\"version-price-label\">¥</text>\n\t\t\t\t\t\t\t\t\t<text class=\"version-price-value\">149.9</text>\n\t\t\t\t\t\t\t\t\t<text class=\"version-price-cycle\">/年</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"version-desc\">性价比最高</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<view class=\"version-features\">\n\t\t\t\t\t\t\t\t\t<view class=\"version-feature-item\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"feature-dot\"></view>\n\t\t\t\t\t\t\t\t\t\t<text class=\"feature-text\">商品发布数量：最多50个</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"version-feature-item\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"feature-dot\"></view>\n\t\t\t\t\t\t\t\t\t\t<text class=\"feature-text\">店铺展示位置：优先位置</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"version-feature-item\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"feature-dot\"></view>\n\t\t\t\t\t\t\t\t\t\t<text class=\"feature-text\">客户数据分析：专业版</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"version-feature-item\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"feature-dot\"></view>\n\t\t\t\t\t\t\t\t\t\t<text class=\"feature-text\">赠送3次店铺推广和置顶</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"version-feature-item\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"feature-dot\"></view>\n\t\t\t\t\t\t\t\t\t\t<text class=\"feature-text\">商品视频展示功能</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<view class=\"version-select-btn premium-select\" :class=\"{'selected': formData.version === 'premium'}\">\n\t\t\t\t\t\t\t\t\t<text>{{ formData.version === 'premium' ? '已选择' : '选择此版本' }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 尊贵版 -->\n\t\t\t\t\t<view class=\"version-card apple-style\" :class=\"{'active': formData.version === 'deluxe'}\" @click=\"selectVersion('deluxe')\">\n\t\t\t\t\t\t<view class=\"version-card-bg deluxe-gradient\">\n\t\t\t\t\t\t\t<view class=\"version-pill\">尊贵版</view>\n\t\t\t\t\t\t\t<view class=\"version-content\">\n\t\t\t\t\t\t\t\t<view class=\"version-price-area\">\n\t\t\t\t\t\t\t\t\t<text class=\"version-price-label\">¥</text>\n\t\t\t\t\t\t\t\t\t<text class=\"version-price-value\">299.9</text>\n\t\t\t\t\t\t\t\t\t<text class=\"version-price-cycle\">/年</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"version-desc\">全功能无限制</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<view class=\"version-features\">\n\t\t\t\t\t\t\t\t\t<view class=\"version-feature-item\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"feature-dot\"></view>\n\t\t\t\t\t\t\t\t\t\t<text class=\"feature-text\">商品发布数量：无限制</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"version-feature-item\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"feature-dot\"></view>\n\t\t\t\t\t\t\t\t\t\t<text class=\"feature-text\">店铺展示位置：最佳位置</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"version-feature-item\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"feature-dot\"></view>\n\t\t\t\t\t\t\t\t\t\t<text class=\"feature-text\">客户数据分析：高级版</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"version-feature-item\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"feature-dot\"></view>\n\t\t\t\t\t\t\t\t\t\t<text class=\"feature-text\">免费获赠整年店铺推广</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"version-feature-item\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"feature-dot\"></view>\n\t\t\t\t\t\t\t\t\t\t<text class=\"feature-text\">优先客服一对一服务</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"version-feature-item\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"feature-dot\"></view>\n\t\t\t\t\t\t\t\t\t\t<text class=\"feature-text\">专属VIP店铺标识</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<view class=\"version-select-btn deluxe-select\" :class=\"{'selected': formData.version === 'deluxe'}\">\n\t\t\t\t\t\t\t\t\t<text>{{ formData.version === 'deluxe' ? '已选择' : '选择此版本' }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t\t\n\t\t\t\t<view class=\"version-popup-footer\">\n\t\t\t\t\t<button class=\"confirm-version-btn\" @click=\"confirmVersion\">确认选择</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 协议同意 -->\n\t\t\t<view class=\"agreement-section\">\n\t\t\t\t<view class=\"agreement-checkbox\" @click=\"toggleAgreement\">\n\t\t\t\t\t<view class=\"checkbox\" :class=\"{'checked': formData.agreed}\"></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"agreement-text\">我已阅读同意<text class=\"agreement-link\">《服务协议》</text>和<text class=\"agreement-link\">《隐私政策》</text></view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 提交按钮 -->\n\t\t\t<view class=\"submit-section\">\n\t\t\t\t<button class=\"submit-btn\" @click=\"submitForm()\">确认入驻</button>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 分类选择弹出层 -->\n\t\t<view class=\"popup-mask\" v-if=\"showCategoryPicker || showScalePicker || showTimePicker\" @click=\"hideAllModals\"></view>\n\t\t\n\t\t<!-- 分类选择 -->\n\t\t<view class=\"popup-content\" v-if=\"showCategoryPicker\">\n\t\t\t<view class=\"popup-header\">\n\t\t\t\t<text class=\"popup-title\">选择经营类目</text>\n\t\t\t\t<view class=\"popup-close\" @click=\"hideCategoryPicker\">\n\t\t\t\t\t<image src=\"/static/images/tabbar/关闭.png\" class=\"close-icon-img\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<scroll-view class=\"category-list\" scroll-y>\n\t\t\t\t<view \n\t\t\t\t\tclass=\"category-item\" \n\t\t\t\t\tv-for=\"(item, index) in categories\" \n\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t@click=\"selectCategory(item)\"\n\t\t\t\t\t:class=\"{'active': formData.category === item}\"\n\t\t\t\t>\n\t\t\t\t\t<text>{{item}}</text>\n\t\t\t\t\t<image src=\"/static/images/tabbar/选中.png\" class=\"check-icon-img\" v-if=\"formData.category === item\"></image>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t</view>\n\t\t\n\t\t<!-- 规模选择 -->\n\t\t<view class=\"popup-content\" v-if=\"showScalePicker\">\n\t\t\t<view class=\"popup-header\">\n\t\t\t\t<text class=\"popup-title\">选择商家规模</text>\n\t\t\t\t<view class=\"popup-close\" @click=\"hideScalePicker\">\n\t\t\t\t\t<image src=\"/static/images/tabbar/关闭.png\" class=\"close-icon-img\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<scroll-view class=\"category-list\" scroll-y>\n\t\t\t\t<view \n\t\t\t\t\tclass=\"category-item\" \n\t\t\t\t\tv-for=\"(item, index) in scales\" \n\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t@click=\"selectScale(item)\"\n\t\t\t\t\t:class=\"{'active': formData.scale === item}\"\n\t\t\t\t>\n\t\t\t\t\t<text>{{item}}</text>\n\t\t\t\t\t<image src=\"/static/images/tabbar/选中.png\" class=\"check-icon-img\" v-if=\"formData.scale === item\"></image>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t</view>\n\t\t\n\t\t<!-- 时间选择 -->\n\t\t<view class=\"popup-content\" v-if=\"showTimePicker\">\n\t\t\t<view class=\"popup-header\">\n\t\t\t\t<text class=\"popup-title\">选择营业时间</text>\n\t\t\t\t<view class=\"popup-close\" @click=\"hideTimePicker\">\n\t\t\t\t\t<image src=\"/static/images/tabbar/关闭.png\" class=\"close-icon-img\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<scroll-view class=\"category-list\" scroll-y>\n\t\t\t\t<view \n\t\t\t\t\tclass=\"category-item\" \n\t\t\t\t\tv-for=\"(item, index) in businessTimes\" \n\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t@click=\"selectTime(item)\"\n\t\t\t\t\t:class=\"{'active': formData.businessTime === item}\"\n\t\t\t\t>\n\t\t\t\t\t<text>{{item}}</text>\n\t\t\t\t\t<image src=\"/static/images/tabbar/选中.png\" class=\"check-icon-img\" v-if=\"formData.businessTime === item\"></image>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t</view>\n\t</view>\n</template>\n\n<script setup>\nimport { ref, reactive, computed, onMounted } from 'vue';\nimport enhancedRewardedAdService from '@/services/enhancedRewardedAdService.js';\nimport ConfigurablePremiumActions from '@/components/premium/ConfigurablePremiumActions.vue';\n\n// --- 响应式状态 ---\nconst statusBarHeight = ref(20);\nconst showCategoryPicker = ref(false);\nconst showScalePicker = ref(false);\nconst showTimePicker = ref(false);\nconst showVersionModal = ref(false);\n\nconst formData = reactive({\n\t\t\t\t\tshopName: '',\n\t\t\t\t\taddress: '',\n  category: '',\n\t\t\t\t\tscale: '',\n\t\t\t\t\tbusinessTime: '',\n  contactPhone: '',\n  description: '',\n  shopImage: null,\n  logo: null,\n  qrcode: null,\n  album: [],\n\t\t\t\t\tjoinMethod: 'free',\n  version: 'premium',\n\t\t\t\t\tagreed: false\n});\n\n// 广告统计数据\nconst adStats = ref({\n\tjoin: {\n\t\tremaining: 1,\n\t\tlimit: 1,\n\t\tlastWatchTime: null\n\t}\n});\n\nconst categories = reactive(['美食小吃', '休闲娱乐', '装修家居', '母婴专区', '房产楼盘', '到家服务', '车辆服务', '教育培训', '其他行业']);\nconst scales = reactive(['1-10人', '11-50人', '51-100人', '100-200人', '200人以上']);\nconst businessTimes = reactive(['08:00-18:00', '09:00-21:00', '24小时营业', '自定义']);\nconst versions = reactive([\n  { \n    key: 'basic', \n    name: '基础版', \n    price: '49.9', \n    desc: '适合个体商户',\n    features: [\n        '商品发布数量：最多20个',\n        '店铺展示位置：普通位置',\n        '客户数据分析：基础版',\n        '免费获赠一次店铺推广'\n    ] \n  },\n  { \n    key: 'premium', \n    name: '高级版', \n    price: '149.9', \n    desc: '性价比最高',\n    features: [\n        '商品发布数量：最多50个',\n        '店铺展示位置：优先位置',\n        '客户数据分析：专业版',\n        '赠送3次店铺推广和置顶',\n        '商品视频展示功能'\n    ] \n  },\n  { \n    key: 'deluxe', \n    name: '尊贵版', \n    price: '299.9', \n    desc: '全功能无限制',\n    features: [\n        '商品发布数量：无限制',\n        '店铺展示位置：最佳位置',\n        '客户数据分析：高级版',\n        '免费获赠整年店铺推广',\n        '优先客服一对一服务',\n        '专属VIP店铺标识'\n    ] \n  }\n]);\n\n// --- 计算属性 ---\nconst versionName = computed(() => versions.find(v => v.key === formData.version)?.name || '');\nconst versionPrice = computed(() => versions.find(v => v.key === formData.version)?.price || 0);\n\nconst getPlanBadgeClass = computed(() => {\n\tswitch(formData.version) {\n\t\tcase 'premium': return 'premium-badge';\n\t\tcase 'deluxe': return 'deluxe-badge';\n\t\tdefault: return 'basic-badge';\n\t}\n});\nconst getVersionMainFeature = computed(() => versions.find(v => v.key === formData.version)?.features[0] || '');\nconst getVersionSecondFeature = computed(() => versions.find(v => v.key === formData.version)?.features[1] || '');\n\n// 商家入驻数据\nconst merchantJoinData = reactive({\n\tid: 'merchant_join',\n\ttitle: '商家入驻',\n\tdescription: '加入我们的商家平台，获得更多商业机会'\n});\n\n// --- 方法 ---\nconst goBack = () => uni.navigateBack();\n\nconst getLocation = () => {\n\tuni.chooseLocation({\n\t\tsuccess: (res) => {\n\t\t\tformData.address = res.address + (res.name || '');\n\t\t}\n\t});\n};\n\nconst hideAllModals = () => {\n    showCategoryPicker.value = false;\n    showScalePicker.value = false;\n    showTimePicker.value = false;\n};\n\n// 分类选择器\nconst showCategoryPickerFn = () => { showCategoryPicker.value = true; };\nconst hideCategoryPicker = () => { showCategoryPicker.value = false; };\nconst selectCategory = (item) => {\n    formData.category = item;\n    hideCategoryPicker();\n};\n\n// 规模选择器\nconst showScalePickerFn = () => { showScalePicker.value = true; };\nconst hideScalePicker = () => { showScalePicker.value = false; };\nconst selectScale = (item) => {\n    formData.scale = item;\n    hideScalePicker();\n};\n\n// 时间选择器\nconst showTimePickerFn = () => { showTimePicker.value = true; };\nconst hideTimePicker = () => { showTimePicker.value = false; };\nconst selectTime = (item) => {\n    formData.businessTime = item;\n    hideTimePicker();\n};\n\nconst uploadImage = (type) => {\n\t\t\t\tuni.chooseImage({\n\t\tcount: type === 'album' ? (10 - formData.album.length) : 1,\n        sizeType: ['original', 'compressed'],\n\t\t\t\t\tsourceType: ['album', 'camera'],\n\t\t\t\t\tsuccess: (res) => {\n\t\t\tif(type === 'album') {\n\t\t\t\tformData.album.push(...res.tempFilePaths);\n\t\t\t\t\t\t\t} else {\n\t\t\t\tformData[type] = res.tempFilePaths[0];\n\t\t\t}\n\t\t}\n\t});\n};\n\nconst selectJoinMethod = (method) => {\n\tformData.joinMethod = method;\n};\n\n\n\nconst selectVersion = (versionKey) => {\n\tformData.version = versionKey;\n};\n\nconst confirmVersion = () => {\n    showVersionModal.value = false;\n};\n\nconst hideVersionModal = () => {\n    showVersionModal.value = false;\n}\n\nconst toggleAgreement = () => {\n    formData.agreed = !formData.agreed;\n};\n\nconst validateForm = (isFree) => {\n    if(!formData.shopName) { uni.showToast({ title: '请输入店铺名称', icon: 'none' }); return false; }\n\tif(!formData.address) { uni.showToast({ title: '请输入店铺地址', icon: 'none' }); return false; }\n\tif(!formData.category) { uni.showToast({ title: '请选择所属行业', icon: 'none' }); return false; }\n\tif(!formData.contactPhone) { uni.showToast({ title: '请输入联系电话', icon: 'none' }); return false; }\n    if(!formData.agreed) { uni.showToast({ title: '请阅读并同意服务协议', icon: 'none' }); return false; }\n\n    if (!isFree && formData.joinMethod === 'paid' && !formData.version) {\n        uni.showToast({ title: '请选择一个付费版本', icon: 'none' });\n        return false;\n    }\n    return true;\n};\n\n// 广告成功回调\nconst handleAdSuccess = (result) => {\n\tconsole.log('广告观看成功:', result);\n\tif (result.type === 'join') {\n\t\tuni.showToast({\n\t\t\ttitle: '观看成功！获得1个月免费特权',\n\t\t\ticon: 'success',\n\t\t\tduration: 2500\n\t\t});\n\t\t// 提交表单\n\t\tsubmitForm(true);\n\t}\n};\n\n// 广告失败回调\nconst handleAdFailed = (type) => {\n\tconsole.log('广告观看失败:', type);\n\tuni.showToast({\n\t\ttitle: '广告播放失败，请重试',\n\t\ticon: 'none'\n\t});\n};\n\n// 广告取消回调\nconst handleAdCancelled = (type) => {\n\tconsole.log('用户取消观看广告:', type);\n};\n\n// 处理入驻完成\nconst handleJoinCompleted = (result) => {\n\tconsole.log('入驻完成:', result);\n\n\tif (result.type === 'ad') {\n\t\t// 看广告入驻成功\n\t\tuni.showModal({\n\t\t\ttitle: '入驻成功',\n\t\t\tcontent: `恭喜您成功入驻！获得${result.data?.days || 30}天免费特权`,\n\t\t\tshowCancel: false,\n\t\t\tconfirmText: '知道了',\n\t\t\tsuccess: () => {\n\t\t\t\tuni.redirectTo({\n\t\t\t\t\turl: '/pages/business/success?shopId=test&type=ad'\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\t} else if (result.type === 'payment') {\n\t\t// 付费入驻成功\n\t\tuni.showToast({\n\t\t\ttitle: '入驻成功',\n\t\t\ticon: 'success'\n\t\t});\n\t\tuni.redirectTo({\n\t\t\turl: '/pages/business/success?shopId=test&type=payment'\n\t\t});\n\t}\n};\n\n// 处理入驻取消\nconst handleJoinCancelled = (result) => {\n\tconsole.log('入驻取消:', result);\n\tif (result.type === 'ad') {\n\t\tuni.showToast({\n\t\t\ttitle: '已取消观看广告',\n\t\t\ticon: 'none'\n\t\t});\n\t} else if (result.type === 'payment') {\n\t\tuni.showToast({\n\t\t\ttitle: '已取消支付',\n\t\t\ticon: 'none'\n\t\t});\n\t}\n};\n\nconst submitForm = (fromAd = false) => {\n    if (!validateForm(fromAd)) return;\n\n\tconsole.log('提交的表单数据:', JSON.parse(JSON.stringify(formData)));\n\n\tuni.showLoading({ title: '提交中...' });\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.hideLoading();\n\t\tuni.showToast({ title: '提交成功，等待审核', icon: 'success' });\n\t\tuni.navigateTo({ url: '/pages/business/success?shopId=test' });\n\t\t\t\t}, 1500);\n};\n\n// --- 广告相关方法 ---\nconst handleWatchAdJoin = async () => {\n\t// 检查剩余次数\n\tif (adStats.value.join.remaining <= 0) {\n\t\tuni.showToast({\n\t\t\ttitle: '今日入驻次数已用完',\n\t\t\ticon: 'none'\n\t\t});\n\t\treturn;\n\t}\n\n\ttry {\n\t\t// 验证表单\n\t\tif (!validateForm(true)) {\n\t\t\treturn;\n\t\t}\n\n\t\t// 调用增强版广告服务\n\t\tconst success = await enhancedRewardedAdService.watchAdForJoin({\n\t\t\tshop_name: formData.shopName,\n\t\t\tcategory: formData.category,\n\t\t\taddress: formData.address,\n\t\t\tcontact_phone: formData.contactPhone,\n\t\t\tbusiness_time: formData.businessTime,\n\t\t\tscale: formData.scale,\n\t\t\tdescription: formData.description,\n\t\t\tversion: formData.version\n\t\t});\n\n\t\tif (success) {\n\t\t\t// 监听奖励发放成功事件\n\t\t\tuni.$on('rewardGranted', handleAdRewardGranted);\n\t\t}\n\t} catch (error) {\n\t\tconsole.error('看广告入驻失败', error);\n\t\tuni.showToast({\n\t\t\ttitle: '操作失败，请重试',\n\t\t\ticon: 'none'\n\t\t});\n\t}\n};\n\nconst handleAdRewardGranted = (data) => {\n\tif (data.type === 'merchant_join') {\n\t\t// 入驻成功，更新统计数据\n\t\tadStats.value.join.remaining--;\n\t\tadStats.value.join.lastWatchTime = new Date();\n\n\t\t// 显示成功提示\n\t\tuni.showModal({\n\t\t\ttitle: '入驻成功',\n\t\t\tcontent: `恭喜您成功入驻！获得${data.data.days}天免费特权，有效期至${data.data.expire_date}`,\n\t\t\tshowCancel: false,\n\t\t\tconfirmText: '知道了',\n\t\t\tsuccess: () => {\n\t\t\t\t// 跳转到商家后台\n\t\t\t\tuni.redirectTo({\n\t\t\t\t\turl: '/subPackages/merchant-admin/pages/dashboard'\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\n\t\t// 移除事件监听\n\t\tuni.$off('rewardGranted', handleAdRewardGranted);\n\t}\n};\n\nconst loadAdStats = async () => {\n\ttry {\n\t\t// 这里应该从后台API获取用户的广告观看统计\n\t\t// const response = await request.get('/api/merchant/ads/stats', {\n\t\t//   params: { ad_type: 'merchant_join', date: new Date().toISOString().split('T')[0] }\n\t\t// });\n\n\t\t// 暂时使用默认数据\n\t\tadStats.value.join = {\n\t\t\tremaining: 1,\n\t\t\tlimit: 1,\n\t\t\tlastWatchTime: null\n\t\t};\n\t} catch (error) {\n\t\tconsole.error('加载广告统计失败', error);\n\t}\n};\n\n// --- 生命周期 ---\nonMounted(() => {\n\tconst systemInfo = uni.getSystemInfoSync();\n\tstatusBarHeight.value = systemInfo.statusBarHeight || 20;\n\n\t// 加载广告统计数据\n\tloadAdStats();\n});\n</script>\n\n<style>\n\t/* 页面样式 */\n\t.join-page {\n\t\tmin-height: 100vh;\n\t\tbackground-color: #f8f9fc;\n\t\tposition: relative;\n\t}\n\t\n\t/* 高斯模糊背景 */\n\t.blur-bg {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\theight: 450rpx;\n\t\tz-index: 0;\n\t\toverflow: hidden;\n\t}\n\t\n\t.bg-image {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tfilter: blur(20px);\n\t\ttransform: scale(1.1);\n\t\topacity: 0.8;\n\t}\n\t\n\t.blur-bg::after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\tright: 0;\n\t\ttop: 0;\n\t\tbottom: 0;\n\t\tbackground: linear-gradient(to bottom, \n\t\t\t#0052CC 0%, \n\t\t\t#0066FF 50%,\n\t\t\t#f8f9fc 100%\n\t\t);\n\t}\n\t\n\t/* 导航栏 */\n\t.status-bar, .navbar {\n\t\tposition: relative;\n\t\tz-index: 10;\n\t}\n\t\n\t.navbar {\n\t\theight: 44px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 0 30rpx;\n\t}\n\t\n\t.navbar-left, .navbar-right {\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\t\n\t.back-icon-img {\n\t\twidth: 32rpx;\n\t\theight: 32rpx;\n\t}\n\t\n\t.navbar-title {\n\t\tcolor: #ffffff;\n\t\tfont-size: 34rpx;\n\t\tfont-weight: 500;\n\t\tletter-spacing: 1rpx;\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);\n\t}\n\t\n\t/* 内容区 */\n\t.content {\n\t\tposition: relative;\n\t\tz-index: 5;\n\t\tpadding: 30rpx;\n\t\tpadding-bottom: 80rpx;\n\t}\n\t\n\t/* 玻璃拟态卡片通用样式 */\n\t.glass-card {\n\t\tbackground-color: rgba(255, 255, 255, 0.92);\n\t\tbackdrop-filter: blur(16px);\n\t\tborder-radius: 20rpx;\n\t\tpadding: 32rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);\n\t}\n\t\n\t/* 入驻说明卡片 */\n\t.intro-card {\n\t\tpadding: 36rpx;\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t\tborder-radius: 24rpx;\n\t\tbackground: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 249, 255, 0.95));\n\t\tbox-shadow: \n\t\t\t0 10rpx 30rpx rgba(22, 119, 255, 0.15), \n\t\t\t0 6rpx 12rpx rgba(0, 0, 0, 0.1),\n\t\t\t0 1rpx 0 rgba(255, 255, 255, 1) inset,\n\t\t\t0 -20rpx 60rpx rgba(255, 255, 255, 0.5) inset;\n\t\tborder: 1px solid rgba(255, 255, 255, 0.7);\n\t\ttransform: translateY(-8rpx);\n\t\ttransition: all 0.3s ease;\n\t}\n\t\n\t.intro-card::after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\theight: 100rpx;\n\t\tbackground: linear-gradient(to bottom, rgba(255, 255, 255, 0.5), transparent);\n\t\tpointer-events: none;\n\t}\n\t\n\t.intro-header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 36rpx;\n\t}\n\t\n\t.intro-icon-wrap {\n\t\twidth: 90rpx;\n\t\theight: 90rpx;\n\t\tborder-radius: 20rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-right: 24rpx;\n\t\tbackground: linear-gradient(135deg, #1677ff, #0052cc);\n\t\tbox-shadow: 0 4rpx 12rpx rgba(22, 119, 255, 0.2);\n\t}\n\t\n\t.intro-icon {\n\t\twidth: 50rpx;\n\t\theight: 50rpx;\n\t\tfilter: brightness(0) invert(1);\n\t}\n\t\n\t.intro-titles {\n\t\tflex: 1;\n\t}\n\t\n\t.subtitle-wrapper {\n\t\tdisplay: block;\n\t\twidth: 100%;\n\t}\n\t\n\t.intro-title {\n\t\tfont-size: 34rpx;\n\t\tcolor: #333;\n\t\tfont-weight: 600;\n\t\tmargin-bottom: 12rpx;\n\t\tletter-spacing: 1px;\n\t}\n\t\n\t.intro-subtitle {\n\t\tfont-size: 22rpx;\n\t\tcolor: #666;\n\t\tletter-spacing: 0.5px;\n\t\tline-height: 1.3;\n\t\tdisplay: block;\n\t}\n\t\n\t.intro-stats {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tmargin-top: 24rpx;\n\t\tbackground-color: rgba(248, 249, 252, 0.8);\n\t\tborder-radius: 16rpx;\n\t\tpadding: 24rpx 20rpx;\n\t\tbox-shadow: \n\t\t\t0 4rpx 12rpx rgba(0, 0, 0, 0.05),\n\t\t\t0 1rpx 0 rgba(255, 255, 255, 1) inset;\n\t\tborder: 1px solid rgba(255, 255, 255, 0.7);\n\t}\n\t\n\t.stat-item {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tpadding: 0 10rpx;\n\t}\n\t\n\t.stat-num {\n\t\tfont-size: 32rpx;\n\t\tcolor: #1677ff;\n\t\tfont-weight: 600;\n\t\tmargin-bottom: 10rpx;\n\t\ttext-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);\n\t}\n\t\n\t.stat-label {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t\tletter-spacing: 1px;\n\t}\n\t\n\t.stat-divider {\n\t\twidth: 2px;\n\t\theight: 44rpx;\n\t\tbackground-color: rgba(0, 0, 0, 0.08);\n\t\tmargin: 0 15rpx;\n\t}\n\t\n\t/* 表单样式 */\n\t.form-section {\n\t\tmargin-bottom: 30rpx;\n\t}\n\t\n\t.form-title {\n\t\tfont-size: 30rpx;\n\t\tcolor: #333;\n\t\tfont-weight: 600;\n\t\tmargin-bottom: 24rpx;\n\t\tdisplay: block;\n\t}\n\t\n\t.form-item {\n\t\tmargin-bottom: 24rpx;\n\t}\n\t\n\t.form-label {\n\t\tfont-size: 26rpx;\n\t\tcolor: #666;\n\t\tmargin-bottom: 12rpx;\n\t\tdisplay: block;\n\t}\n\t\n\t.form-input {\n\t\theight: 80rpx;\n\t\tbackground-color: rgba(248, 249, 252, 0.6);\n\t\tborder-radius: 12rpx;\n\t\tpadding: 0 24rpx;\n\t\tfont-size: 26rpx;\n\t\tcolor: #333;\n\t\tborder: 1px solid rgba(0, 0, 0, 0.05);\n\t}\n\t\n\t.address-wrap {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t}\n\t\n\t.address-input {\n\t\tpadding-right: 90rpx;\n\t\tflex: 1;\n\t}\n\t\n\t.location-btn {\n\t\tposition: absolute;\n\t\tright: 0;\n\t\ttop: 0;\n\t\theight: 80rpx;\n\t\twidth: 80rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground: linear-gradient(135deg, #1677ff, #0052cc);\n\t\tborder-radius: 0 12rpx 12rpx 0;\n\t\tz-index: 5;\n\t}\n\t\n\t.location-icon-img {\n\t\twidth: 32rpx;\n\t\theight: 32rpx;\n\t\tfilter: brightness(0) invert(1);\n\t}\n\t\n\t.form-select {\n\t\theight: 80rpx;\n\t\tbackground-color: rgba(248, 249, 252, 0.6);\n\t\tborder-radius: 12rpx;\n\t\tpadding: 0 24rpx;\n\t\tfont-size: 26rpx;\n\t\tcolor: #333;\n\t\tborder: 1px solid rgba(0, 0, 0, 0.05);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t}\n\t\n\t.placeholder {\n\t\tcolor: #999;\n\t}\n\t\n\t.select-arrow-img {\n\t\twidth: 24rpx;\n\t\theight: 24rpx;\n\t\topacity: 0.6;\n\t}\n\t\n\t.form-textarea {\n\t\theight: 160rpx;\n\t\tbackground-color: rgba(248, 249, 252, 0.6);\n\t\tborder-radius: 12rpx;\n\t\tpadding: 24rpx;\n\t\tfont-size: 26rpx;\n\t\tcolor: #333;\n\t\tborder: 1px solid rgba(0, 0, 0, 0.05);\n\t\twidth: 100%;\n\t\tbox-sizing: border-box;\n\t}\n\t\n\t/* 入驻优势 */\n\t.benefits-section {\n\t\tmargin-bottom: 30rpx;\n\t}\n\t\n\t.benefits-title {\n\t\tfont-size: 30rpx;\n\t\tcolor: #333;\n\t\tfont-weight: 600;\n\t\tmargin-bottom: 24rpx;\n\t\tdisplay: block;\n\t}\n\t\n\t.benefits-list {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\t\n\t.benefit-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 20rpx;\n\t\tbackground-color: rgba(248, 249, 252, 0.6);\n\t\tborder-radius: 12rpx;\n\t\tpadding: 16rpx 20rpx;\n\t}\n\t\n\t.benefit-item:last-child {\n\t\tmargin-bottom: 0;\n\t}\n\t\n\t.benefit-icon-wrap {\n\t\twidth: 64rpx;\n\t\theight: 64rpx;\n\t\tborder-radius: 16rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground: rgba(22, 119, 255, 0.1);\n\t\tmargin-right: 16rpx;\n\t}\n\t\n\t.benefit-icon-img {\n\t\twidth: 32rpx;\n\t\theight: 32rpx;\n\t}\n\t\n\t.benefit-content {\n\t\tflex: 1;\n\t}\n\t\n\t.benefit-name {\n\t\tfont-size: 26rpx;\n\t\tcolor: #333;\n\t\tfont-weight: 600;\n\t\tmargin-bottom: 4rpx;\n\t}\n\t\n\t.benefit-desc {\n\t\tfont-size: 22rpx;\n\t\tcolor: #666;\n\t}\n\t\n\t/* 提交按钮 */\n\t.submit-section {\n\t\tmargin-top: 40rpx;\n\t\tmargin-bottom: 60rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t}\n\t\n\t.submit-btn {\n\t\twidth: 90%;\n\t\theight: 88rpx;\n\t\tbackground: linear-gradient(135deg, #1677ff, #0052cc);\n\t\tborder-radius: 44rpx;\n\t\tcolor: #ffffff;\n\t\tfont-size: 30rpx;\n\t\tfont-weight: 600;\n\t\tmargin-bottom: 20rpx;\n\t\tbox-shadow: 0 8rpx 16rpx rgba(22, 119, 255, 0.2);\n\t}\n\t\n\t.agreement-section {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmargin-top: 30rpx;\n\t}\n\n\t.agreement-checkbox {\n\t\twidth: 32rpx;\n\t\theight: 32rpx;\n\t\tborder: 1px solid #ccc;\n\t\tborder-radius: 50%;\n\t\tmargin-right: 12rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\t\n\t.checkbox.checked {\n\t\tbackground-color: #1677ff;\n\t\tborder-color: #1677ff;\n\t}\n\t\n\t.agreement-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t}\n\t\n\t.agreement-link {\n\t\tcolor: #1677ff;\n\t}\n\t\n\t/* 弹窗样式 */\n\t.popup-mask {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\n\t\tz-index: 100;\n\t\tbackdrop-filter: blur(3px);\n\t\t-webkit-backdrop-filter: blur(3px);\n\t}\n\t\n\t.popup-content {\n\t\tposition: fixed;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground-color: #fff;\n\t\tz-index: 101;\n\t\tborder-radius: 24rpx 24rpx 0 0;\n\t\tpadding-bottom: env(safe-area-inset-bottom);\n\t\tmax-height: 70vh;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tbox-shadow: 0 -10rpx 30rpx rgba(0, 0, 0, 0.1);\n\t\tanimation: slideUp 0.3s ease-out;\n\t}\n\t\n\t@keyframes slideUp {\n\t\tfrom {\n\t\t\ttransform: translateY(100%);\n\t\t}\n\t\tto {\n\t\t\ttransform: translateY(0);\n\t\t}\n\t}\n\t\n\t.popup-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 24rpx 30rpx;\n\t\tborder-bottom: 1px solid rgba(0, 0, 0, 0.05);\n\t}\n\t\n\t.popup-title {\n\t\tfont-size: 30rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t}\n\t\n\t.popup-close {\n\t\twidth: 56rpx;\n\t\theight: 56rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder-radius: 50%;\n\t\tbackground-color: rgba(0, 0, 0, 0.05);\n\t}\n\t\n\t.close-icon-img {\n\t\twidth: 28rpx;\n\t\theight: 28rpx;\n\t\topacity: 0.6;\n\t}\n\t\n\t.category-list {\n\t\tmax-height: calc(70vh - 90rpx);\n\t\tpadding: 0 30rpx;\n\t}\n\t\n\t.category-list .category-item {\n\t\theight: 100rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tborder-bottom: 1px solid rgba(0, 0, 0, 0.05);\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t}\n\t\n\t.category-list .category-item.active {\n\t\tcolor: #1677ff;\n\t}\n\t\n\t.check-icon-img {\n\t\twidth: 36rpx;\n\t\theight: 36rpx;\n\t}\n\t\n\t.location-tips {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-top: 8rpx;\n\t\tpadding: 0 8rpx;\n\t}\n\t\n\t.location-text {\n\t\tfont-size: 22rpx;\n\t\tcolor: #0052cc;\n\t}\n\t\n\t.location-btn-text {\n\t\tfont-size: 22rpx;\n\t\tcolor: #0052cc;\n\t\tpadding: 4rpx 12rpx;\n\t\tbackground-color: rgba(0, 82, 204, 0.05);\n\t\tborder-radius: 10rpx;\n\t}\n\t\n\t/* 上传图片样式 */\n\t.upload-box {\n\t\twidth: 200rpx;\n\t\theight: 200rpx;\n\t\tbackground-color: rgba(248, 249, 252, 0.6);\n\t\tborder-radius: 12rpx;\n\t\tborder: 1px dashed rgba(0, 0, 0, 0.1);\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-top: 16rpx;\n\t}\n\t\n\t.upload-placeholder {\n\t\twidth: 80rpx;\n\t\theight: 80rpx;\n\t\tbackground-color: rgba(0, 82, 204, 0.05);\n\t\tborder-radius: 40rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-bottom: 12rpx;\n\t}\n\t\n\t.plus-icon {\n\t\tfont-size: 44rpx;\n\t\tcolor: #1677ff;\n\t\tfont-weight: 200;\n\t}\n\t\n\t.upload-desc-container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tmargin-top: 8rpx;\n\t}\n\t\n\t.upload-desc {\n\t\tfont-size: 22rpx;\n\t\tcolor: #999;\n\t\tline-height: 32rpx;\n\t\ttext-align: center;\n\t}\n\t\n\t/* 入驻方式选择 */\n\t.join-methods {\n\t\tmargin-top: 20rpx;\n\t}\n\t\n\t.join-option {\n\t\tdisplay: flex;\n\t\tpadding: 20rpx;\n\t\tborder-radius: 16rpx;\n\t\tbackground-color: rgba(255, 255, 255, 0.5);\n\t\tborder: 1px solid rgba(0, 0, 0, 0.05);\n\t\tmargin-bottom: 20rpx;\n\t\ttransition: all 0.3s ease;\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t}\n\t\n\t.join-option.active {\n\t\tbackground-color: rgba(22, 119, 255, 0.05);\n\t\tborder: 1px solid rgba(22, 119, 255, 0.3);\n\t\ttransform: translateY(-4rpx);\n\t\tbox-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.06);\n\t}\n\t\n\t.option-radio {\n\t\twidth: 36rpx;\n\t\theight: 36rpx;\n\t\tborder-radius: 50%;\n\t\tborder: 2px solid #e0e0e0;\n\t\tmargin-right: 20rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\t\n\t.join-option.active .option-radio {\n\t\tborder-color: #1677ff;\n\t}\n\t\n\t.radio-dot {\n\t\twidth: 20rpx;\n\t\theight: 20rpx;\n\t\tborder-radius: 50%;\n\t\tbackground-color: #1677ff;\n\t}\n\t\n\t.option-content {\n\t\tflex: 1;\n\t}\n\t\n\t.option-title-row {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 8rpx;\n\t}\n\t\n\t.option-icon {\n\t\twidth: 36rpx;\n\t\theight: 36rpx;\n\t\tmargin-right: 10rpx;\n\t}\n\t\n\t.option-title {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t\tmargin-right: 10rpx;\n\t}\n\t\n\t.option-desc {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t}\n\t\n\t.option-badge {\n\t\tpadding: 2rpx 12rpx;\n\t\tbackground-color: #e6f7ff;\n\t\tborder-radius: 8rpx;\n\t\tfont-size: 20rpx;\n\t\tcolor: #1677ff;\n\t}\n\t\n\t.premium-badge {\n\t\tbackground-color: #fff7e6;\n\t\tcolor: #fa8c16;\n\t}\n\t\n\t/* 特权列表 */\n\t.privilege-list {\n\t\tmargin-top: 30rpx;\n\t\tpadding: 24rpx;\n\t\tbackground-color: rgba(248, 249, 252, 0.6);\n\t\tborder-radius: 16rpx;\n\t\tborder: 1px dashed rgba(22, 119, 255, 0.3);\n\t}\n\t\n\t.privilege-title {\n\t\tfont-size: 26rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t\tmargin-bottom: 16rpx;\n\t}\n\t\n\t.privilege-items {\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.privilege-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 12rpx;\n\t}\n\t\n\t.privilege-dot {\n\t\twidth: 8rpx;\n\t\theight: 8rpx;\n\t\tborder-radius: 4rpx;\n\t\tbackground-color: #1677ff;\n\t\tmargin-right: 12rpx;\n\t}\n\t\n\t.privilege-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t}\n\n\t/* 增强版广告按钮样式 */\n\t.enhanced-ad-section {\n\t\tmargin-top: 30rpx;\n\t}\n\n\t.ad-button-wrapper {\n\t\tbackground: linear-gradient(135deg, #FF6B35, #FF8A50);\n\t\tborder-radius: 20rpx;\n\t\tpadding: 4rpx;\n\t\tbox-shadow: 0 8rpx 24rpx rgba(255, 107, 53, 0.3);\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.ad-button-wrapper:active {\n\t\ttransform: scale(0.98);\n\t\tbox-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.4);\n\t}\n\n\t.ad-button-content {\n\t\tbackground: white;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 24rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t}\n\n\t.ad-button-content::before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground: linear-gradient(135deg, rgba(255, 107, 53, 0.05), rgba(255, 138, 80, 0.05));\n\t\tpointer-events: none;\n\t}\n\n\t.ad-icon-wrapper {\n\t\tposition: relative;\n\t\tmargin-right: 20rpx;\n\t}\n\n\t.ad-icon {\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tborder-radius: 12rpx;\n\t}\n\n\t.free-badge {\n\t\tposition: absolute;\n\t\ttop: -8rpx;\n\t\tright: -8rpx;\n\t\tbackground: linear-gradient(45deg, #52C41A, #73D13D);\n\t\tcolor: white;\n\t\tfont-size: 20rpx;\n\t\tpadding: 4rpx 8rpx;\n\t\tborder-radius: 8rpx;\n\t\tfont-weight: 600;\n\t\tbox-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.3);\n\t}\n\n\t.ad-text-content {\n\t\tflex: 1;\n\t}\n\n\t.ad-title {\n\t\tdisplay: block;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t\tmargin-bottom: 8rpx;\n\t}\n\n\t.ad-description {\n\t\tdisplay: block;\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t}\n\n\t.ad-arrow {\n\t\tmargin-left: 20rpx;\n\t}\n\n\t.arrow-icon {\n\t\tfont-size: 32rpx;\n\t\tcolor: #FF6B35;\n\t\tfont-weight: bold;\n\t}\n\n\t.ad-tips {\n\t\tmargin-top: 16rpx;\n\t\ttext-align: center;\n\t}\n\n\t.tips-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t}\n\n\t.watch-ad-button {\n\t\tbackground: linear-gradient(135deg, #1677ff, #0052cc);\n\t\tborder-radius: 44rpx;\n\t\theight: 80rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #fff;\n\t\tfont-weight: 500;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-top: 20rpx;\n\t\tbox-shadow: 0 8rpx 16rpx rgba(22, 119, 255, 0.2);\n\t}\n\t\n\t.btn-icon {\n\t\twidth: 36rpx;\n\t\theight: 36rpx;\n\t\tmargin-right: 10rpx;\n\t}\n\t\n\t/* 已选版本展示 */\n\t.selected-plan {\n\t\tmargin-top: 20rpx;\n\t\tpadding: 24rpx;\n\t\tborder-radius: 20rpx;\n\t\tbackground-color: #f8f9fc;\n\t\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n\t\twidth: 92%; /* 从88%增加到92% */\n\t\tmargin-left: auto;\n\t\tmargin-right: auto;\n\t\tborder: 1px solid rgba(230, 235, 245, 0.8);\n\t}\n\t\n\t.apple-card {\n\t\tbackground-color: rgba(255, 255, 255, 0.85);\n\t\tbackdrop-filter: blur(10px);\n\t\tbox-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);\n\t\tborder: 1px solid rgba(255, 255, 255, 0.5);\n\t}\n\t\n\t.plan-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 14rpx; /* 减小底部间距 */\n\t}\n\t\n\t.plan-badge {\n\t\tpadding: 6rpx 18rpx;\n\t\tborder-radius: 14rpx;\n\t\tfont-size: 22rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #fff;\n\t}\n\t\n\t.basic-badge {\n\t\tbackground-color: rgba(33, 150, 243, 0.8);\n\t\tcolor: #ffffff;\n\t\tborder: 1px solid rgba(33, 150, 243, 0.2);\n\t}\n\t\n\t.premium-badge {\n\t\tbackground-color: rgba(255, 152, 0, 0.8);\n\t\tcolor: #ffffff;\n\t\tborder: 1px solid rgba(255, 152, 0, 0.2);\n\t}\n\t\n\t.deluxe-badge {\n\t\tbackground-color: rgba(156, 39, 176, 0.8);\n\t\tcolor: #ffffff;\n\t\tborder: 1px solid rgba(156, 39, 176, 0.2);\n\t}\n\t\n\t.plan-price-row {\n\t\tdisplay: flex;\n\t\talign-items: baseline;\n\t\tmargin-bottom: 12rpx;\n\t}\n\t\n\t.plan-price-label {\n\t\tfont-size: 24rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #333;\n\t}\n\t\n\t.plan-price-value {\n\t\tfont-size: 40rpx; /* 减小字体大小 */\n\t\tfont-weight: 700;\n\t\tcolor: #333;\n\t\tmargin: 0 4rpx;\n\t}\n\t\n\t.plan-price-cycle {\n\t\tfont-size: 22rpx;\n\t\tcolor: #666;\n\t}\n\t\n\t.change-plan {\n\t\tfont-size: 24rpx;\n\t\tcolor: #007aff;\n\t\tpadding: 6rpx 12rpx;\n\t\tbackground-color: rgba(0, 122, 255, 0.1);\n\t\tborder-radius: 12rpx;\n\t}\n\t\n\t.plan-divider {\n\t\theight: 1px;\n\t\tbackground-color: rgba(0, 0, 0, 0.05);\n\t\tmargin: 12rpx 0 16rpx;\n\t}\n\t\n\t.plan-features {\n\t\tmargin-bottom: 16rpx;\n\t}\n\t\n\t.plan-feature {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 10rpx;\n\t\tfont-size: 24rpx;\n\t}\n\t\n\t.feature-dot {\n\t\twidth: 8rpx;\n\t\theight: 8rpx;\n\t\tborder-radius: 50%;\n\t\tbackground-color: #1677ff;\n\t\tmargin-right: 10rpx;\n\t}\n\t\n\t.feature-text {\n\t\tcolor: #666;\n\t\tfont-size: 24rpx;\n\t}\n\t\n\t.select-other-btn {\n\t\tmargin-top: 10rpx;\n\t\tbackground-color: rgba(0, 122, 255, 0.08);\n\t\tborder-radius: 14rpx;\n\t\theight: 64rpx;\n\t\tline-height: 64rpx;\n\t\tcolor: #007aff;\n\t\tfont-size: 24rpx;\n\t\tfont-weight: 500;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder: 1px solid rgba(0, 122, 255, 0.2);\n\t}\n\t\n\t/* 付费入驻版本弹窗 */\n\t.version-popup {\n\t\tpadding-bottom: 100rpx;\n\t\tmax-height: 75vh; /* 减小最大高度 */\n\t\tborder-radius: 30rpx 30rpx 0 0;\n\t\tbox-shadow: 0 -10rpx 30rpx rgba(0, 0, 0, 0.1);\n\t\toverflow: hidden;\n\t\tbackground-color: #f5f5f7;\n\t}\n\t\n\t.version-list {\n\t\tpadding: 20rpx 10rpx; /* 调整左右内边距 */\n\t\tmax-height: 50vh;\n\t\toverflow-y: auto;\n\t\t-webkit-overflow-scrolling: touch;\n\t}\n\t\n\t.version-card {\n\t\tmargin-bottom: 24rpx; /* 减小底部间距 */\n\t\tborder-radius: 24rpx;\n\t\tborder: none;\n\t\toverflow: hidden;\n\t\ttransition: all 0.3s ease;\n\t\tposition: relative;\n\t\tbox-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.06);\n\t\twidth: 85%; /* 从75%增加到85% */\n\t\tmargin-left: auto;\n\t\tmargin-right: auto;\n\t\ttransform: scale(0.98); /* 略微调整默认缩放 */\n\t}\n\t\n\t.version-card.active {\n\t\ttransform: scale(1);\n\t\tbox-shadow: 0 12rpx 30rpx rgba(0, 0, 0, 0.12);\n\t}\n\t\n\t.apple-style {\n\t\tbackground-color: transparent;\n\t}\n\t\n\t.version-card-bg {\n\t\tposition: relative;\n\t\tpadding: 24rpx; /* 从28rpx减小到24rpx */\n\t\tborder-radius: 24rpx;\n\t\toverflow: hidden;\n\t\theight: 100%;\n\t}\n\t\n\t.basic-gradient {\n\t\tbackground: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);\n\t\tborder: 1px solid rgba(33, 150, 243, 0.2);\n\t}\n\t\n\t.premium-gradient {\n\t\tbackground: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);\n\t\tborder: 1px solid rgba(255, 193, 7, 0.2);\n\t}\n\t\n\t.deluxe-gradient {\n\t\tbackground: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);\n\t\tborder: 1px solid rgba(156, 39, 176, 0.2);\n\t}\n\t\n\t.version-badge {\n\t\tposition: absolute;\n\t\ttop: 16rpx;\n\t\tright: 16rpx;\n\t\tbackground: linear-gradient(135deg, #ff9a00, #ff6a00);\n\t\tcolor: white;\n\t\tfont-size: 20rpx;\n\t\tfont-weight: 600;\n\t\tpadding: 4rpx 16rpx;\n\t\tborder-radius: 16rpx;\n\t\tbox-shadow: 0 2rpx 8rpx rgba(255, 106, 0, 0.3);\n\t}\n\t\n\t.badge-text {\n\t\tletter-spacing: 1px;\n\t}\n\t\n\t.version-pill {\n\t\tdisplay: inline-block;\n\t\tmargin-bottom: 24rpx;\n\t\tpadding: 6rpx 20rpx;\n\t\tbackground-color: rgba(255, 255, 255, 0.5);\n\t\tborder-radius: 20rpx;\n\t\tfont-size: 24rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t\tbackdrop-filter: blur(10rpx);\n\t\tborder: 1px solid rgba(255, 255, 255, 0.7);\n\t}\n\t\n\t.version-content {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\t\n\t.version-price-area {\n\t\tmargin: 6rpx 0 12rpx; /* 减小上下间距 */\n\t}\n\t\n\t.version-price-label {\n\t\tfont-size: 26rpx;\n\t\tcolor: #333;\n\t\tfont-weight: 600;\n\t}\n\t\n\t.version-price-value {\n\t\tfont-size: 40rpx; /* 减小字体大小 */\n\t\tcolor: #333;\n\t\tfont-weight: 700;\n\t}\n\t\n\t.version-price-cycle {\n\t\tfont-size: 22rpx;\n\t\tcolor: #666;\n\t\tfont-weight: 400;\n\t\tmargin-left: 2rpx;\n\t}\n\t\n\t.version-desc {\n\t\tfont-size: 22rpx;\n\t\tcolor: #666;\n\t\tmargin-bottom: 16rpx; /* 减小底部间距 */\n\t}\n\t\n\t.version-features {\n\t\tbackground-color: rgba(255, 255, 255, 0.6);\n\t\tborder-radius: 16rpx;\n\t\tpadding: 16rpx 18rpx; /* 调整内边距 */\n\t\tmargin-bottom: 20rpx;\n\t\tbackdrop-filter: blur(8rpx);\n\t\tborder: 1px solid rgba(255, 255, 255, 0.8);\n\t}\n\t\n\t.version-feature-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 10rpx; /* 减小底部间距 */\n\t\tfont-size: 22rpx; /* 减小字体大小 */\n\t}\n\t\n\t.feature-dot {\n\t\twidth: 6rpx;\n\t\theight: 6rpx;\n\t\tborder-radius: 3rpx;\n\t\tbackground-color: #333;\n\t\tmargin-right: 10rpx;\n\t}\n\t\n\t.feature-text {\n\t\tfont-size: 22rpx;\n\t\tcolor: #333;\n\t\tline-height: 1.4;\n\t}\n\t\n\t.version-select-btn {\n\t\twidth: 100%;\n\t\theight: 72rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder-radius: 36rpx;\n\t\tbackground: rgba(0, 122, 255, 0.9);\n\t\tcolor: #fff;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 500;\n\t\tbox-shadow: 0 6rpx 12rpx rgba(0, 122, 255, 0.2);\n\t\ttransition: all 0.3s ease;\n\t}\n\t\n\t.version-select-btn.selected {\n\t\tbackground: rgba(0, 98, 204, 0.9);\n\t}\n\t\n\t.premium-select {\n\t\tbackground: rgba(255, 149, 0, 0.9);\n\t\tbox-shadow: 0 6rpx 12rpx rgba(255, 149, 0, 0.2);\n\t}\n\t\n\t.premium-select.selected {\n\t\tbackground: rgba(230, 134, 0, 0.9);\n\t}\n\t\n\t.deluxe-select {\n\t\tbackground: rgba(175, 82, 222, 0.9);\n\t\tbox-shadow: 0 6rpx 12rpx rgba(175, 82, 222, 0.2);\n\t}\n\t\n\t.deluxe-select.selected {\n\t\tbackground: rgba(150, 70, 190, 0.9);\n\t}\n\t\n\t.version-popup-footer {\n\t\tposition: fixed;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground-color: rgba(255, 255, 255, 0.95);\n\t\tbackdrop-filter: blur(10px);\n\t\tpadding: 24rpx 32rpx;\n\t\tborder-top: 1px solid rgba(0, 0, 0, 0.05);\n\t}\n\t\n\t.confirm-version-btn {\n\t\twidth: 100%;\n\t\theight: 88rpx;\n\t\tbackground: linear-gradient(135deg, #007AFF, #0062CC);\n\t\tborder-radius: 44rpx;\n\t\tcolor: #ffffff;\n\t\tfont-size: 30rpx;\n\t\tfont-weight: 600;\n\t\tbox-shadow: 0 8rpx 16rpx rgba(0, 122, 255, 0.2);\n\t}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/business/join.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "computed", "uni", "onMounted", "MiniProgramPage"], "mappings": ";;;;;;AAkZA,MAAM,6BAA6B,MAAW;;;;AAG9C,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,qBAAqBA,cAAAA,IAAI,KAAK;AACpC,UAAM,kBAAkBA,cAAAA,IAAI,KAAK;AACjC,UAAM,iBAAiBA,cAAAA,IAAI,KAAK;AAChC,UAAM,mBAAmBA,cAAAA,IAAI,KAAK;AAElC,UAAM,WAAWC,cAAAA,SAAS;AAAA,MACrB,UAAU;AAAA,MACV,SAAS;AAAA,MACZ,UAAU;AAAA,MACP,OAAO;AAAA,MACP,cAAc;AAAA,MACjB,cAAc;AAAA,MACd,aAAa;AAAA,MACb,WAAW;AAAA,MACX,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,OAAO,CAAE;AAAA,MACN,YAAY;AAAA,MACf,SAAS;AAAA,MACN,QAAQ;AAAA,IACb,CAAC;AAGD,UAAM,UAAUD,cAAAA,IAAI;AAAA,MACnB,MAAM;AAAA,QACL,WAAW;AAAA,QACX,OAAO;AAAA,QACP,eAAe;AAAA,MACf;AAAA,IACF,CAAC;AAED,UAAM,aAAaC,cAAAA,SAAS,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,CAAC;AACpG,UAAM,SAASA,cAAAA,SAAS,CAAC,SAAS,UAAU,WAAW,YAAY,QAAQ,CAAC;AAC5E,UAAM,gBAAgBA,cAAQ,SAAC,CAAC,eAAe,eAAe,UAAU,KAAK,CAAC;AAC9E,UAAM,WAAWA,cAAAA,SAAS;AAAA,MACxB;AAAA,QACE,KAAK;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,UACN;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACH;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,UACN;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACH;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,UACN;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACH;AAAA,MACF;AAAA,IACH,CAAC;AAGmBC,kBAAQ,SAAC;;AAAM,6BAAS,KAAK,OAAK,EAAE,QAAQ,SAAS,OAAO,MAA7C,mBAAgD,SAAQ;AAAA,KAAE;AACxEA,kBAAQ,SAAC;;AAAM,6BAAS,KAAK,OAAK,EAAE,QAAQ,SAAS,OAAO,MAA7C,mBAAgD,UAAS;AAAA,KAAC;AAEpEA,kBAAAA,SAAS,MAAM;AACxC,cAAO,SAAS,SAAO;AAAA,QACtB,KAAK;AAAW,iBAAO;AAAA,QACvB,KAAK;AAAU,iBAAO;AAAA,QACtB;AAAS,iBAAO;AAAA,MAChB;AAAA,IACF,CAAC;AAC6BA,kBAAQ,SAAC;;AAAM,6BAAS,KAAK,OAAK,EAAE,QAAQ,SAAS,OAAO,MAA7C,mBAAgD,SAAS,OAAM;AAAA,KAAE;AAC9EA,kBAAQ,SAAC;;AAAM,6BAAS,KAAK,OAAK,EAAE,QAAQ,SAAS,OAAO,MAA7C,mBAAgD,SAAS,OAAM;AAAA,KAAE;AAGhH,UAAM,mBAAmBD,cAAAA,SAAS;AAAA,MACjC,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,aAAa;AAAA,IACd,CAAC;AAGD,UAAM,SAAS,MAAME,oBAAI;AAEzB,UAAM,cAAc,MAAM;AACzBA,oBAAAA,MAAI,eAAe;AAAA,QAClB,SAAS,CAAC,QAAQ;AACjB,mBAAS,UAAU,IAAI,WAAW,IAAI,QAAQ;AAAA,QAC9C;AAAA,MACH,CAAE;AAAA,IACF;AAEA,UAAM,gBAAgB,MAAM;AACxB,yBAAmB,QAAQ;AAC3B,sBAAgB,QAAQ;AACxB,qBAAe,QAAQ;AAAA,IAC3B;AAGA,UAAM,uBAAuB,MAAM;AAAE,yBAAmB,QAAQ;AAAA,IAAK;AACrE,UAAM,qBAAqB,MAAM;AAAE,yBAAmB,QAAQ;AAAA,IAAM;AACpE,UAAM,iBAAiB,CAAC,SAAS;AAC7B,eAAS,WAAW;AACpB;IACJ;AAGA,UAAM,oBAAoB,MAAM;AAAE,sBAAgB,QAAQ;AAAA,IAAK;AAC/D,UAAM,kBAAkB,MAAM;AAAE,sBAAgB,QAAQ;AAAA,IAAM;AAC9D,UAAM,cAAc,CAAC,SAAS;AAC1B,eAAS,QAAQ;AACjB;IACJ;AAGA,UAAM,mBAAmB,MAAM;AAAE,qBAAe,QAAQ;AAAA,IAAK;AAC7D,UAAM,iBAAiB,MAAM;AAAE,qBAAe,QAAQ;AAAA,IAAM;AAC5D,UAAM,aAAa,CAAC,SAAS;AACzB,eAAS,eAAe;AACxB;IACJ;AAEA,UAAM,cAAc,CAAC,SAAS;AAC1BA,oBAAAA,MAAI,YAAY;AAAA,QAClB,OAAO,SAAS,UAAW,KAAK,SAAS,MAAM,SAAU;AAAA,QACnD,UAAU,CAAC,YAAY,YAAY;AAAA,QACtC,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AACpB,cAAG,SAAS,SAAS;AACpB,qBAAS,MAAM,KAAK,GAAG,IAAI,aAAa;AAAA,UAC5C,OAAc;AACV,qBAAS,IAAI,IAAI,IAAI,cAAc,CAAC;AAAA,UACpC;AAAA,QACD;AAAA,MACH,CAAE;AAAA,IACF;AAQA,UAAM,gBAAgB,CAAC,eAAe;AACrC,eAAS,UAAU;AAAA,IACpB;AAEA,UAAM,iBAAiB,MAAM;AACzB,uBAAiB,QAAQ;AAAA,IAC7B;AAEA,UAAM,mBAAmB,MAAM;AAC3B,uBAAiB,QAAQ;AAAA,IAC7B;AAEA,UAAM,kBAAkB,MAAM;AAC1B,eAAS,SAAS,CAAC,SAAS;AAAA,IAChC;AAEA,UAAM,eAAe,CAAC,WAAW;AAC7B,UAAG,CAAC,SAAS,UAAU;AAAEA,sBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,OAAM,CAAE;AAAG,eAAO;AAAA,MAAQ;AAC9F,UAAG,CAAC,SAAS,SAAS;AAAEA,sBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,OAAM,CAAE;AAAG,eAAO;AAAA,MAAQ;AAC1F,UAAG,CAAC,SAAS,UAAU;AAAEA,sBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,OAAM,CAAE;AAAG,eAAO;AAAA,MAAQ;AAC3F,UAAG,CAAC,SAAS,cAAc;AAAEA,sBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,OAAM,CAAE;AAAG,eAAO;AAAA,MAAQ;AAC5F,UAAG,CAAC,SAAS,QAAQ;AAAEA,sBAAG,MAAC,UAAU,EAAE,OAAO,cAAc,MAAM,OAAM,CAAE;AAAG,eAAO;AAAA,MAAQ;AAE5F,UAAI,CAAC,UAAU,SAAS,eAAe,UAAU,CAAC,SAAS,SAAS;AAChEA,sBAAG,MAAC,UAAU,EAAE,OAAO,aAAa,MAAM,OAAM,CAAE;AAClD,eAAO;AAAA,MACV;AACD,aAAO;AAAA,IACX;AA+BA,UAAM,sBAAsB,CAAC,WAAW;;AACvCA,yEAAY,SAAS,MAAM;AAE3B,UAAI,OAAO,SAAS,MAAM;AAEzBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,SAAS,eAAa,YAAO,SAAP,mBAAa,SAAQ,EAAE;AAAA,UAC7C,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,SAAS,MAAM;AACdA,0BAAAA,MAAI,WAAW;AAAA,cACd,KAAK;AAAA,YACV,CAAK;AAAA,UACD;AAAA,QACJ,CAAG;AAAA,MACH,WAAY,OAAO,SAAS,WAAW;AAErCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AACDA,sBAAAA,MAAI,WAAW;AAAA,UACd,KAAK;AAAA,QACR,CAAG;AAAA,MACD;AAAA,IACF;AAGA,UAAM,sBAAsB,CAAC,WAAW;AACvCA,yEAAY,SAAS,MAAM;AAC3B,UAAI,OAAO,SAAS,MAAM;AACzBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACH,WAAY,OAAO,SAAS,WAAW;AACrCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACD;AAAA,IACF;AAEA,UAAM,aAAa,CAAC,SAAS,UAAU;AACnC,UAAI,CAAC,aAAa,MAAM;AAAG;AAE9BA,oBAAAA,MAAA,MAAA,OAAA,kCAAY,YAAY,KAAK,MAAM,KAAK,UAAU,QAAQ,CAAC,CAAC;AAE5DA,oBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AAChC,iBAAW,MAAM;AAChBA,sBAAG,MAAC,YAAW;AAClBA,sBAAG,MAAC,UAAU,EAAE,OAAO,aAAa,MAAM,UAAS,CAAE;AACrDA,sBAAAA,MAAI,WAAW,EAAE,KAAK,sCAAuC,CAAA;AAAA,MAC1D,GAAE,IAAI;AAAA,IACX;AAqEA,UAAM,cAAc,YAAY;AAC/B,UAAI;AAOH,gBAAQ,MAAM,OAAO;AAAA,UACpB,WAAW;AAAA,UACX,OAAO;AAAA,UACP,eAAe;AAAA,QAClB;AAAA,MACE,SAAQ,OAAO;AACfA,6EAAc,YAAY,KAAK;AAAA,MAC/B;AAAA,IACF;AAGAC,kBAAAA,UAAU,MAAM;AACf,YAAM,aAAaD,oBAAI;AACvB,sBAAgB,QAAQ,WAAW,mBAAmB;AAGtD;IACD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpwBD,GAAG,WAAWE,SAAe;"}