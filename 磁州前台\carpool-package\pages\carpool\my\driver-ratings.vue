<template>
  <view class="ratings-container">
    <!-- 自定义导航栏 -->
    <view class="custom-header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="left-action" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="action-icon back-icon"></image>
        </view>
        <view class="title-area">
          <text class="page-title">我的评价</text>
        </view>
        <view class="right-action">
          <!-- 预留位置 -->
        </view>
      </view>
    </view>
    
    <!-- 评分统计 -->
    <view class="rating-summary" :style="{ marginTop: (statusBarHeight + 44) + 'px' }">
      <view class="summary-left">
        <view class="average-score">{{averageScore}}</view>
        <view class="total-ratings">共{{totalRatings}}条评价</view>
      </view>
      <view class="summary-right">
        <view class="star-row">
          <text class="star-level">5星</text>
          <view class="progress-bar">
            <view class="progress-fill" :style="{width: fiveStarPercent + '%'}"></view>
          </view>
          <text class="star-count">{{fiveStarCount}}</text>
        </view>
        <view class="star-row">
          <text class="star-level">4星</text>
          <view class="progress-bar">
            <view class="progress-fill" :style="{width: fourStarPercent + '%'}"></view>
          </view>
          <text class="star-count">{{fourStarCount}}</text>
        </view>
        <view class="star-row">
          <text class="star-level">3星</text>
          <view class="progress-bar">
            <view class="progress-fill" :style="{width: threeStarPercent + '%'}"></view>
          </view>
          <text class="star-count">{{threeStarCount}}</text>
        </view>
        <view class="star-row">
          <text class="star-level">2星</text>
          <view class="progress-bar">
            <view class="progress-fill" :style="{width: twoStarPercent + '%'}"></view>
          </view>
          <text class="star-count">{{twoStarCount}}</text>
        </view>
        <view class="star-row">
          <text class="star-level">1星</text>
          <view class="progress-bar">
            <view class="progress-fill" :style="{width: oneStarPercent + '%'}"></view>
          </view>
          <text class="star-count">{{oneStarCount}}</text>
        </view>
      </view>
    </view>
    
    <!-- 标签统计 -->
    <view class="tags-summary">
      <view class="section-title">评价标签</view>
      <view class="tags-container">
        <view class="tag-item" v-for="(tag, index) in topTags" :key="index">
          <text class="tag-text">{{tag.name}}</text>
          <text class="tag-count">{{tag.count}}</text>
        </view>
      </view>
    </view>
    
    <!-- 筛选选项 -->
    <view class="filter-options">
      <view class="filter-tabs">
        <view 
          class="tab-item" 
          :class="{ active: activeFilter === 'all' }"
          @click="setFilter('all')"
        >全部</view>
        <view 
          class="tab-item" 
          :class="{ active: activeFilter === 'good' }"
          @click="setFilter('good')"
        >好评</view>
        <view 
          class="tab-item" 
          :class="{ active: activeFilter === 'medium' }"
          @click="setFilter('medium')"
        >中评</view>
        <view 
          class="tab-item" 
          :class="{ active: activeFilter === 'bad' }"
          @click="setFilter('bad')"
        >差评</view>
      </view>
    </view>
    
    <!-- 评价列表 -->
    <view class="ratings-list">
      <view class="empty-state" v-if="filteredRatings.length === 0">
        <image src="/static/images/icons/empty-ratings.png" class="empty-icon"></image>
        <text class="empty-text">暂无{{filterText}}评价</text>
      </view>
      
      <view class="rating-item" v-for="(item, index) in filteredRatings" :key="index">
        <view class="rating-header">
          <view class="user-info" v-if="!item.isAnonymous">
            <image :src="item.userAvatar" class="user-avatar"></image>
            <text class="user-name">{{item.userName}}</text>
          </view>
          <view class="user-info" v-else>
            <image src="/static/images/avatar/anonymous.png" class="user-avatar"></image>
            <text class="user-name">匿名用户</text>
          </view>
          <view class="rating-score">
            <view class="star-display">
              <image 
                v-for="i in 5" 
                :key="i" 
                :src="i <= item.rating ? '/static/images/icons/star-filled.png' : '/static/images/icons/star-empty.png'" 
                class="star-icon-small"
              ></image>
            </view>
            <text class="rating-date">{{formatDate(item.createTime)}}</text>
          </view>
        </view>
        
        <view class="rating-tags" v-if="item.tags && item.tags.length > 0">
          <view class="tag-pill" v-for="(tag, tagIndex) in item.tags" :key="tagIndex">
            {{tag}}
          </view>
        </view>
        
        <view class="rating-comment" v-if="item.comment">
          {{item.comment}}
        </view>
        
        <view class="trip-info">
          <text class="trip-route">{{item.startLocation}} → {{item.endLocation}}</text>
          <text class="trip-time">{{item.departureDate}} {{item.departureTime}}</text>
        </view>
      </view>
    </view>
    
    <!-- 底部安全区域 -->
    <view class="safe-area-bottom"></view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 状态栏高度
const statusBarHeight = ref(20);

// 评价数据
const ratings = ref([]);
const activeFilter = ref('all');
const averageScore = ref(4.8);
const totalRatings = ref(0);
const fiveStarCount = ref(0);
const fourStarCount = ref(0);
const threeStarCount = ref(0);
const twoStarCount = ref(0);
const oneStarCount = ref(0);
const topTags = ref([]);

// 计算属性
const fiveStarPercent = computed(() => {
  return totalRatings.value > 0 ? (fiveStarCount.value / totalRatings.value) * 100 : 0;
});

const fourStarPercent = computed(() => {
  return totalRatings.value > 0 ? (fourStarCount.value / totalRatings.value) * 100 : 0;
});

const threeStarPercent = computed(() => {
  return totalRatings.value > 0 ? (threeStarCount.value / totalRatings.value) * 100 : 0;
});

const twoStarPercent = computed(() => {
  return totalRatings.value > 0 ? (twoStarCount.value / totalRatings.value) * 100 : 0;
});

const oneStarPercent = computed(() => {
  return totalRatings.value > 0 ? (oneStarCount.value / totalRatings.value) * 100 : 0;
});

const filteredRatings = computed(() => {
  if (activeFilter.value === 'all') {
    return ratings.value;
  } else if (activeFilter.value === 'good') {
    return ratings.value.filter(item => item.rating >= 4);
  } else if (activeFilter.value === 'medium') {
    return ratings.value.filter(item => item.rating === 3);
  } else if (activeFilter.value === 'bad') {
    return ratings.value.filter(item => item.rating <= 2);
      }
  return ratings.value;
});

const filterText = computed(() => {
      const filterMap = {
        all: '',
        good: '好',
        medium: '中',
        bad: '差'
      };
  return filterMap[activeFilter.value];
});

// 页面加载
onMounted(() => {
  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight || 20;
  
  // 加载评价数据
  loadRatings();
});

    // 返回上一页
const goBack = () => {
      uni.navigateBack();
};
    
    // 加载评价数据
const loadRatings = () => {
      // 实际应用中应该从API获取数据
      // 这里使用模拟数据
      uni.showLoading({
        title: '加载中...'
      });
      
      setTimeout(() => {
        // 模拟数据
    ratings.value = [
          {
            id: 'R001',
            userName: '李先生',
            userAvatar: '/static/images/avatar/user3.png',
            rating: 5,
            tags: ['准时出发', '路线合理', '态度友好'],
            comment: '司机师傅非常准时，提前到达上车地点等候，路线选择也很合理，避开了拥堵路段，全程行车平稳，服务态度也很好！',
            isAnonymous: false,
            createTime: '2023-06-18T09:30:00',
            startLocation: '磁县火车站',
            endLocation: '邯郸东站',
            departureDate: '2023-06-15',
            departureTime: '14:30'
          },
          {
            id: 'R002',
            userName: '王女士',
            userAvatar: '/static/images/avatar/user4.png',
            rating: 4,
            tags: ['驾驶平稳', '车内整洁'],
            comment: '车内很干净，司机开车也很稳，就是上车地点稍微有点难找。',
            isAnonymous: false,
            createTime: '2023-06-16T15:45:00',
            startLocation: '磁县人民医院',
            endLocation: '邯郸市区',
            departureDate: '2023-06-16',
            departureTime: '10:00'
          },
          {
            id: 'R003',
            userName: '',
            userAvatar: '',
            rating: 5,
            tags: ['价格合理', '态度友好', '准时出发'],
            comment: '价格很合理，比其他拼车便宜，司机人也很好，会主动帮忙搬行李。',
            isAnonymous: true,
            createTime: '2023-06-14T18:20:00',
            startLocation: '磁县商业街',
            endLocation: '邯郸高铁站',
            departureDate: '2023-06-14',
            departureTime: '16:30'
          },
          {
            id: 'R004',
            userName: '张先生',
            userAvatar: '/static/images/avatar/user2.png',
            rating: 3,
            tags: ['价格合理'],
            comment: '一般，没什么特别的，价格还可以。',
            isAnonymous: false,
            createTime: '2023-06-12T11:05:00',
            startLocation: '磁县第一中学',
            endLocation: '邯郸火车站',
            departureDate: '2023-06-12',
            departureTime: '09:00'
          },
          {
            id: 'R005',
            userName: '赵女士',
            userAvatar: '/static/images/avatar/user5.png',
            rating: 2,
            tags: [],
            comment: '迟到了15分钟，态度也不是很好，车内有异味。',
            isAnonymous: false,
            createTime: '2023-06-10T20:15:00',
            startLocation: '磁县政府',
            endLocation: '邯郸市区',
            departureDate: '2023-06-10',
            departureTime: '18:30'
          }
        ];
        
        // 计算统计数据
    calculateStatistics();
        
        uni.hideLoading();
      }, 1000);
};
    
    // 计算统计数据
const calculateStatistics = () => {
      // 计算总数和各星级数量
  totalRatings.value = ratings.value.length;
  fiveStarCount.value = ratings.value.filter(item => item.rating === 5).length;
  fourStarCount.value = ratings.value.filter(item => item.rating === 4).length;
  threeStarCount.value = ratings.value.filter(item => item.rating === 3).length;
  twoStarCount.value = ratings.value.filter(item => item.rating === 2).length;
  oneStarCount.value = ratings.value.filter(item => item.rating === 1).length;
      
      // 计算平均分
  if (totalRatings.value > 0) {
    const totalScore = ratings.value.reduce((sum, item) => sum + item.rating, 0);
    averageScore.value = (totalScore / totalRatings.value).toFixed(1);
      }
      
      // 计算标签统计
      const tagMap = {};
  ratings.value.forEach(item => {
        if (item.tags && item.tags.length > 0) {
          item.tags.forEach(tag => {
            if (tagMap[tag]) {
              tagMap[tag]++;
            } else {
              tagMap[tag] = 1;
            }
          });
        }
      });
      
      // 转换为数组并排序
      const tagArray = Object.keys(tagMap).map(key => ({
        name: key,
        count: tagMap[key]
      }));
      
      tagArray.sort((a, b) => b.count - a.count);
  topTags.value = tagArray.slice(0, 6); // 取前6个标签
};
    
    // 设置筛选条件
const setFilter = (filter) => {
  activeFilter.value = filter;
};
    
    // 格式化日期
const formatDate = (dateString) => {
      const date = new Date(dateString);
      const now = new Date();
      const diffDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));
      
      if (diffDays === 0) {
        return '今天';
      } else if (diffDays === 1) {
        return '昨天';
      } else if (diffDays < 7) {
        return `${diffDays}天前`;
      } else {
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();
        return `${year}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`;
      }
};
</script>

<style lang="scss">
.ratings-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(90rpx + var(--status-bar-height, 40px));
  padding-bottom: 40rpx;
}

/* 自定义导航栏 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
}

.header-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
}

.left-action, .right-action {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  width: 24px;
  height: 24px;
}

.back-icon {
  width: 24px;
  height: 24px;
  /* 图标是黑色的，需要转为白色 */
  filter: brightness(0) invert(1);
}

.title-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 评分统计 */
.rating-summary {
  margin: 20rpx 32rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.summary-left {
  width: 30%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-right: 1px solid #F2F2F7;
  padding-right: 24rpx;
}

.average-score {
  font-size: 48rpx;
  font-weight: 600;
  color: #FF9F0A;
  margin-bottom: 8rpx;
}

.total-ratings {
  font-size: 24rpx;
  color: #8E8E93;
}

.summary-right {
  flex: 1;
  padding-left: 24rpx;
}

.star-row {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.star-level {
  width: 60rpx;
  font-size: 24rpx;
  color: #8E8E93;
}

.progress-bar {
  flex: 1;
  height: 16rpx;
  background-color: #F2F2F7;
  border-radius: 8rpx;
  margin: 0 16rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #FF9F0A;
  border-radius: 8rpx;
}

.star-count {
  width: 40rpx;
  font-size: 24rpx;
  color: #8E8E93;
  text-align: right;
}

/* 标签统计 */
.tags-summary {
  margin: 0 32rpx 32rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
}

.tag-item {
  padding: 10rpx 20rpx;
  background-color: #F5F5F5;
  border-radius: 30rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
}

.tag-text {
  font-size: 26rpx;
  color: #333333;
}

.tag-count {
  font-size: 24rpx;
  color: #FF9F0A;
  margin-left: 8rpx;
}

/* 筛选选项 */
.filter-options {
  margin: 0 32rpx 20rpx;
}

.filter-tabs {
  display: flex;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.tab-item {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666666;
  position: relative;
}

.tab-item.active {
  color: #0A84FF;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 25%;
  width: 50%;
  height: 4rpx;
  background-color: #0A84FF;
  border-radius: 2rpx;
}

/* 评价列表 */
.ratings-list {
  margin: 0 32rpx;
}

.empty-state {
  padding: 60rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #8E8E93;
}

.rating-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.rating-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  margin-right: 12rpx;
}

.user-name {
  font-size: 28rpx;
  color: #333333;
}

.rating-score {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.star-display {
  display: flex;
  margin-bottom: 6rpx;
}

.star-icon-small {
  width: 24rpx;
  height: 24rpx;
  margin-left: 4rpx;
}

.rating-date {
  font-size: 22rpx;
  color: #8E8E93;
}

.rating-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}

.tag-pill {
  padding: 6rpx 16rpx;
  background-color: #F5F5F5;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #666666;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}

.rating-comment {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.trip-info {
  padding-top: 16rpx;
  border-top: 1px solid #F2F2F7;
  display: flex;
  justify-content: space-between;
}

.trip-route {
  font-size: 24rpx;
  color: #666666;
}

.trip-time {
  font-size: 24rpx;
  color: #8E8E93;
}

.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  width: 100%;
}
</style> 