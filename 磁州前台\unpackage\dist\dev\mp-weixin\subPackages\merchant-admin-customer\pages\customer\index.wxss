/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 页面容器 */
.customer-management-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  display: flex;
  flex-direction: column;
}

/* 导航栏 */
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}
.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}
.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}

/* 客户数据概览 */
.data-overview {
  background: #fff;
  border-radius: 10px;
  margin: 15px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}
.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.overview-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.date-selector {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
}
.selector-icon {
  width: 12px;
  height: 12px;
  margin-left: 5px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23666666"><path d="M7 10l5 5 5-5z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}
.metric-cards {
  display: flex;
  flex-wrap: wrap;
  margin: -5px;
}
.metric-card {
  width: calc(50% - 10px);
  background: #F8FAFC;
  border-radius: 10px;
  padding: 15px;
  margin: 5px;
  display: flex;
  align-items: center;
}
.metric-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.metric-icon.customers {
  background: linear-gradient(135deg, #3498DB, #2980B9);
}
.metric-icon.new {
  background: linear-gradient(135deg, #2ECC71, #27AE60);
}
.metric-icon.active {
  background: linear-gradient(135deg, #F39C12, #E67E22);
}
.metric-icon.repurchase {
  background: linear-gradient(135deg, #9B59B6, #8E44AD);
}
.metric-data {
  flex: 1;
}
.metric-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  display: block;
}
.metric-label {
  font-size: 12px;
  color: #999;
}
.metric-trend {
  display: flex;
  align-items: center;
  font-size: 12px;
  margin-left: 5px;
}
.metric-trend.up {
  color: #52C41A;
}
.metric-trend.down {
  color: #FF4D4F;
}
.trend-icon {
  width: 12px;
  height: 12px;
  margin-right: 2px;
}
.metric-trend.up .trend-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2352C41A"><path d="M7 14l5-5 5 5z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}
.metric-trend.down .trend-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FF4D4F"><path d="M7 10l5 5 5-5z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

/* 通用区块样式 */
.customer-segments, .lifecycle-section, .rfm-section, .tag-section, .interaction-section {
  background: #fff;
  border-radius: 10px;
  margin: 15px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.section-action {
  font-size: 14px;
  color: #1677FF;
}

/* 客户分群标签页 */
.segment-tabs {
  display: flex;
  overflow-x: auto;
  white-space: nowrap;
  margin-bottom: 15px;
  scrollbar-width: none;
  /* Firefox */
}
.segment-tabs::-webkit-scrollbar {
  display: none;
  /* Chrome/Safari/Opera */
}
.segment-tab {
  padding: 8px 16px;
  margin-right: 10px;
  font-size: 14px;
  color: #666;
  background: #F5F7FA;
  border-radius: 16px;
  flex-shrink: 0;
}
.segment-tab.active {
  background: #F0F7FF;
  color: #1677FF;
  font-weight: 500;
}
.segment-info-card {
  background: #F8FAFC;
  border-radius: 10px;
  padding: 15px;
}
.segment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.segment-title-container {
  display: flex;
  align-items: center;
}
.segment-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-right: 8px;
}
.segment-tag {
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
}
.segment-tag.high-value {
  background: #E6F7FF;
  color: #1677FF;
}
.segment-tag.active {
  background: #F6FFED;
  color: #52C41A;
}
.segment-tag.new {
  background: #FCF5EB;
  color: #FA8C16;
}
.segment-tag.risk {
  background: #FFF1F0;
  color: #FF4D4F;
}
.segment-tag.low {
  background: #F5F5F5;
  color: #999;
}
.segment-action {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #1677FF;
}
.action-icon {
  width: 12px;
  height: 12px;
  margin-left: 5px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231677FF"><path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}
.segment-metrics {
  display: flex;
  margin-bottom: 15px;
}
.segment-metric {
  flex: 1;
  text-align: center;
  padding: 0 10px;
}
.segment-metric:not(:last-child) {
  border-right: 1px solid #EAEAEA;
}
.segment-chart {
  height: 200px;
  margin-bottom: 15px;
  position: relative;
}
.chart-canvas {
  width: 100%;
  height: 100%;
}
.chart-title {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 12px;
  color: #999;
}
.segment-traits {
  margin-bottom: 15px;
}
.traits-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
  display: block;
}
.traits-tags {
  display: flex;
  flex-wrap: wrap;
}
.trait-tag {
  padding: 6px 12px;
  background: #F5F7FA;
  border-radius: 16px;
  font-size: 12px;
  color: #666;
  margin: 0 8px 8px 0;
}
.segment-actions {
  display: flex;
}
.action-btn {
  flex: 1;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  margin: 0 5px;
}
.action-btn.detail {
  background: #F0F7FF;
  color: #1677FF;
}
.action-btn.export {
  background: #F5F5F5;
  color: #666;
}

/* 客户生命周期 */
.lifecycle-card {
  background: #F8FAFC;
  border-radius: 10px;
  padding: 15px;
}
.lifecycle-chart {
  margin-bottom: 15px;
}
.lifecycle-image {
  width: 100%;
  height: auto;
  max-height: 180px;
  border-radius: 8px;
}
.lifecycle-metrics {
  display: flex;
  flex-wrap: wrap;
  margin: -5px;
}
.lifecycle-stage {
  width: calc(50% - 10px);
  margin: 5px;
  background: #fff;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
}
.stage-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.stage-icon {
  width: 16px;
  height: 16px;
  border-radius: 8px;
  margin-right: 8px;
}
.stage-icon.new {
  background: #52C41A;
}
.stage-icon.growing {
  background: #1677FF;
}
.stage-icon.mature {
  background: #722ED1;
}
.stage-icon.risk {
  background: #FF4D4F;
}
.stage-name {
  font-size: 14px;
  color: #666;
}
.stage-data {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.stage-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.stage-trend {
  display: flex;
  align-items: center;
  font-size: 12px;
}
.stage-trend.up {
  color: #52C41A;
}
.stage-trend.down {
  color: #FF4D4F;
}
.trend-value {
  font-size: 12px;
}
.lifecycle-insights {
  margin-top: 15px;
  padding: 10px;
  background: #F0F7FF;
  border-radius: 8px;
  display: flex;
  align-items: flex-start;
}
.insight-icon {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  flex-shrink: 0;
  margin-top: 2px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231677FF"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 15c-.55 0-1-.45-1-1v-4c0-.55.45-1 1-1s1 .45 1 1v4c0 .55-.45 1-1 1zm1-8h-2V7h2v2z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}
.insight-text {
  font-size: 14px;
  color: #1677FF;
  line-height: 1.5;
}

/* RFM 价值矩阵 */
.rfm-card {
  background: #F8FAFC;
  border-radius: 10px;
  padding: 15px;
}
.rfm-matrix {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}
.rfm-row {
  display: flex;
  height: 80px;
  margin-bottom: 10px;
}
.rfm-row:last-child {
  margin-bottom: 0;
}
.rfm-cell {
  flex: 1;
  margin: 0 5px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.rfm-cell.high {
  background: linear-gradient(135deg, #E6F7FF, #D4EFFF);
}
.rfm-cell.medium {
  background: linear-gradient(135deg, #F0F7FF, #E8F4FF);
}
.rfm-cell.low {
  background: linear-gradient(135deg, #F9F9F9, #F5F5F5);
}
.rfm-cell.potential {
  background: linear-gradient(135deg, #FCF5EB, #FAEBD7);
}
.cell-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}
.cell-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.rfm-legend {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
}
.legend-title {
  font-size: 14px;
  color: #666;
  margin-right: 10px;
}
.legend-item {
  display: flex;
  align-items: center;
  margin-right: 15px;
  margin-bottom: 5px;
}
.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 3px;
  margin-right: 5px;
}
.legend-color.high {
  background: #1677FF;
}
.legend-color.medium {
  background: #52C41A;
}
.legend-color.low {
  background: #BFBFBF;
}
.legend-color.potential {
  background: #FA8C16;
}
.legend-text {
  font-size: 12px;
  color: #666;
}
.rfm-desc {
  background: #fff;
  border-radius: 8px;
  padding: 10px;
}
.desc-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}
.desc-text {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
}

/* 客户标签管理 */
.tag-card {
  background: #F8FAFC;
  border-radius: 10px;
  padding: 15px;
}
.tag-categories {
  display: flex;
  overflow-x: auto;
  white-space: nowrap;
  margin-bottom: 15px;
  scrollbar-width: none;
}
.tag-categories::-webkit-scrollbar {
  display: none;
}
.tag-category {
  padding: 8px 16px;
  margin-right: 10px;
  font-size: 14px;
  color: #666;
  background: #fff;
  border-radius: 16px;
  flex-shrink: 0;
}
.tag-category.active {
  background: #1677FF;
  color: #fff;
}
.tag-cloud {
  display: flex;
  flex-wrap: wrap;
  padding: 10px;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 15px;
}
.tag-item {
  padding: 6px 12px;
  background: #F5F7FA;
  border-radius: 16px;
  margin: 5px;
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
}
.tag-item.xsmall {
  font-size: 10px;
}
.tag-item.small {
  font-size: 12px;
}
.tag-item.medium {
  font-size: 14px;
}
.tag-item.large {
  font-size: 16px;
  font-weight: 500;
}
.tag-item.xlarge {
  font-size: 18px;
  font-weight: 600;
}
.tag-count {
  font-size: 10px;
  color: #999;
  margin-left: 5px;
}
.tag-footer {
  display: flex;
  justify-content: center;
}
.create-tag-btn {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: #1677FF;
  border-radius: 16px;
  color: #fff;
}
.btn-icon {
  width: 16px;
  height: 16px;
  margin-right: 5px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}
.btn-text {
  font-size: 14px;
  font-weight: 500;
}

/* 客户互动 */
.message-stats {
  display: flex;
  margin-bottom: 15px;
}
.message-stat-item {
  flex: 1;
  text-align: center;
}
.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 5px;
}
.stat-label {
  font-size: 12px;
  color: #999;
}
.recent-messages {
  background: #F8FAFC;
  border-radius: 10px;
  padding: 15px;
}
.message-item {
  display: flex;
  align-items: center;
  padding: 10px;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 10px;
  position: relative;
}
.customer-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 10px;
}
.message-content {
  flex: 1;
  min-width: 0;
}
.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}
.customer-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}
.message-time {
  font-size: 12px;
  color: #999;
}
.message-preview {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}
.message-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  min-width: 18px;
  height: 18px;
  border-radius: 9px;
  background: #FF4D4F;
  color: #fff;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6px;
}
.interaction-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}
.action-btn {
  flex: 1;
  height: 40px;
  margin: 0 5px;
  background: #fff;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-icon {
  width: 16px;
  height: 16px;
  margin-right: 5px;
}
.btn-icon.message {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231677FF"><path d="M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}
.btn-icon.notify {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231677FF"><path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}
.btn-icon.survey {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231677FF"><path d="M11 7h2v2h-2zm0 4h2v6h-2zm1-9C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}
.btn-text {
  font-size: 14px;
  color: #1677FF;
}