"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      dateRange: "2023-04-01 ~ 2023-04-30",
      currentTab: 0,
      tabList: ["全部", "进行中", "已完成", "草稿"],
      // 群发数据概览
      massData: {
        totalCount: 42,
        countTrend: "up",
        countGrowth: "15.3%",
        totalUsers: 3568,
        usersTrend: "up",
        usersGrowth: "23.7%",
        totalAmount: 8765.5,
        amountTrend: "up",
        amountGrowth: "18.2%",
        conversionRate: 32.6,
        conversionTrend: "up",
        conversionGrowth: "5.8%"
      },
      // 群发记录列表
      massList: [
        {
          id: 1,
          title: "新用户欢迎红包",
          type: "fixed",
          status: "completed",
          statusText: "已完成",
          sendTime: "2023-04-15 14:30",
          amount: 10,
          targetText: "新注册用户",
          sentCount: 568,
          receivedCount: 452,
          usedCount: 326
        },
        {
          id: 2,
          title: "五一节日红包",
          type: "random",
          status: "processing",
          statusText: "进行中",
          sendTime: "2023-05-01 10:00",
          minAmount: 5,
          maxAmount: 50,
          targetText: "全部会员",
          sentCount: 1200,
          receivedCount: 876,
          usedCount: 543
        },
        {
          id: 3,
          title: "老用户回馈红包",
          type: "fixed",
          status: "draft",
          statusText: "草稿",
          sendTime: "未发送",
          amount: 15,
          targetText: "三个月未消费用户",
          sentCount: 0,
          receivedCount: 0,
          usedCount: 0
        }
      ],
      // 群发攻略
      strategies: [
        {
          id: 1,
          title: "如何提高红包领取率",
          description: "了解用户行为，精准设置红包金额和发放时间",
          color: "#FF6B6B",
          icon: "/static/images/redpacket/strategy-icon-1.png"
        },
        {
          id: 2,
          title: "会员分层群发策略",
          description: "根据会员等级和消费习惯制定差异化红包策略",
          color: "#4ECDC4",
          icon: "/static/images/redpacket/strategy-icon-2.png"
        },
        {
          id: 3,
          title: "节假日红包营销攻略",
          description: "把握节假日营销节点，提升红包营销效果",
          color: "#FFD166",
          icon: "/static/images/redpacket/strategy-icon-3.png"
        }
      ]
    };
  },
  computed: {
    filteredMassList() {
      if (this.currentTab === 0) {
        return this.massList;
      } else {
        const statusMap = {
          1: "processing",
          2: "completed",
          3: "draft"
        };
        return this.massList.filter((item) => item.status === statusMap[this.currentTab]);
      }
    }
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    showHelp() {
      common_vendor.index.showModal({
        title: "红包群发帮助",
        content: "红包群发功能可以帮助您向指定用户群体批量发送红包，提升用户活跃度和转化率。",
        showCancel: false
      });
    },
    showDatePicker() {
      common_vendor.index.showToast({
        title: "日期选择功能开发中",
        icon: "none"
      });
    },
    formatNumber(num) {
      return num.toFixed(2);
    },
    switchTab(index) {
      this.currentTab = index;
    },
    createMassSending() {
      common_vendor.index.showToast({
        title: "创建群发功能开发中",
        icon: "none"
      });
    },
    viewMassDetail(item) {
      common_vendor.index.showToast({
        title: "查看详情功能开发中",
        icon: "none"
      });
    },
    repeatMass(item) {
      common_vendor.index.showToast({
        title: "再次发送功能开发中",
        icon: "none"
      });
    },
    deleteMass(item) {
      common_vendor.index.showModal({
        title: "删除确认",
        content: `确定要删除"${item.title}"吗？`,
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "删除成功",
              icon: "success"
            });
          }
        }
      });
    },
    viewStrategy(strategy) {
      common_vendor.index.showToast({
        title: "攻略详情功能开发中",
        icon: "none"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: common_vendor.t($data.dateRange),
    d: common_vendor.o((...args) => $options.showDatePicker && $options.showDatePicker(...args)),
    e: common_vendor.t($data.massData.totalCount),
    f: common_vendor.t($data.massData.countGrowth),
    g: common_vendor.n($data.massData.countTrend),
    h: common_vendor.t($data.massData.totalUsers),
    i: common_vendor.t($data.massData.usersGrowth),
    j: common_vendor.n($data.massData.usersTrend),
    k: common_vendor.t($options.formatNumber($data.massData.totalAmount)),
    l: common_vendor.t($data.massData.amountGrowth),
    m: common_vendor.n($data.massData.amountTrend),
    n: common_vendor.t($data.massData.conversionRate),
    o: common_vendor.t($data.massData.conversionGrowth),
    p: common_vendor.n($data.massData.conversionTrend),
    q: common_vendor.o((...args) => $options.createMassSending && $options.createMassSending(...args)),
    r: common_vendor.f($data.tabList, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab),
        b: index,
        c: $data.currentTab === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    }),
    s: common_vendor.f($options.filteredMassList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.title),
        b: common_vendor.t(item.statusText),
        c: common_vendor.n("status-" + item.status),
        d: common_vendor.n(item.type),
        e: common_vendor.t(item.sendTime),
        f: item.type === "fixed"
      }, item.type === "fixed" ? {
        g: common_vendor.t(item.amount)
      } : item.type === "random" ? {
        i: common_vendor.t(item.minAmount),
        j: common_vendor.t(item.maxAmount)
      } : {}, {
        h: item.type === "random",
        k: common_vendor.t(item.targetText),
        l: common_vendor.t(item.sentCount),
        m: common_vendor.t(item.receivedCount),
        n: common_vendor.t(item.usedCount),
        o: common_vendor.o(($event) => $options.viewMassDetail(item), index),
        p: item.status === "completed"
      }, item.status === "completed" ? {
        q: common_vendor.o(($event) => $options.repeatMass(item), index)
      } : {}, {
        r: item.status === "draft"
      }, item.status === "draft" ? {
        s: common_vendor.o(($event) => $options.deleteMass(item), index)
      } : {}, {
        t: index,
        v: common_vendor.o(($event) => $options.viewMassDetail(item), index)
      });
    }),
    t: $options.filteredMassList.length === 0
  }, $options.filteredMassList.length === 0 ? {
    v: common_vendor.t($data.tabList[$data.currentTab])
  } : {}, {
    w: common_vendor.f($data.strategies, (strategy, index, i0) => {
      return {
        a: strategy.icon,
        b: strategy.color,
        c: common_vendor.t(strategy.title),
        d: common_vendor.t(strategy.description),
        e: index,
        f: common_vendor.o(($event) => $options.viewStrategy(strategy), index)
      };
    }),
    x: common_vendor.o((...args) => $options.createMassSending && $options.createMassSending(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/redpacket/mass-sending.js.map
