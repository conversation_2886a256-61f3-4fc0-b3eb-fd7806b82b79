{"version": 3, "file": "contentApi.js", "sources": ["api/contentApi.js"], "sourcesContent": ["/**\n * 内容管理API服务\n * 包括新闻、信息、商家等内容的API调用\n */\n\nimport request from '@/utils/request';\n\n// 内容API服务类\nclass ContentApiService {\n\n  /**\n   * 获取新闻分类列表\n   * @returns {Promise} 分类列表\n   */\n  async getNewsCategories() {\n    try {\n      const response = await request.get('/content/news/categories');\n      \n      return {\n        success: true,\n        data: response.data || []\n      };\n    } catch (error) {\n      console.error('获取新闻分类失败:', error);\n      return {\n        success: false,\n        data: [],\n        message: error.message || '获取分类失败'\n      };\n    }\n  }\n\n  /**\n   * 获取新闻列表\n   * @param {Object} params - 查询参数\n   * @param {number} params.page - 页码\n   * @param {number} params.limit - 每页数量\n   * @param {number} params.category_id - 分类ID\n   * @returns {Promise} 新闻列表\n   */\n  async getNewsList(params = {}) {\n    try {\n      const queryParams = {\n        page: params.page || 1,\n        limit: params.limit || 10,\n        category_id: params.category_id || null\n      };\n\n      const response = await request.get('/content/news', { params: queryParams });\n      \n      return {\n        success: true,\n        data: response.data || [],\n        total: response.total || 0,\n        page: response.page || 1,\n        limit: response.limit || 10\n      };\n    } catch (error) {\n      console.error('获取新闻列表失败:', error);\n      return {\n        success: false,\n        data: [],\n        message: error.message || '获取新闻失败'\n      };\n    }\n  }\n\n  /**\n   * 获取新闻详情\n   * @param {number} newsId - 新闻ID\n   * @returns {Promise} 新闻详情\n   */\n  async getNewsDetail(newsId) {\n    try {\n      const response = await request.get(`/content/news/${newsId}`);\n      \n      return {\n        success: true,\n        data: response.data\n      };\n    } catch (error) {\n      console.error('获取新闻详情失败:', error);\n      return {\n        success: false,\n        message: error.message || '获取新闻详情失败'\n      };\n    }\n  }\n\n  /**\n   * 获取信息分类列表\n   * @returns {Promise} 分类列表\n   */\n  async getInfoCategories() {\n    try {\n      const response = await request.get('/content/info/categories');\n      \n      return {\n        success: true,\n        data: response.data || []\n      };\n    } catch (error) {\n      console.error('获取信息分类失败:', error);\n      return {\n        success: false,\n        data: [],\n        message: error.message || '获取分类失败'\n      };\n    }\n  }\n\n  /**\n   * 获取信息列表\n   * @param {Object} params - 查询参数\n   * @param {number} params.page - 页码\n   * @param {number} params.limit - 每页数量\n   * @param {number} params.category_id - 分类ID\n   * @param {string} params.type - 信息类型 (all, topped, normal)\n   * @returns {Promise} 信息列表\n   */\n  async getInfoList(params = {}) {\n    try {\n      const queryParams = {\n        page: params.page || 1,\n        limit: params.limit || 10,\n        category_id: params.category_id || null,\n        type: params.type || 'all'\n      };\n\n      const response = await request.get('/content/info', { params: queryParams });\n      \n      return {\n        success: true,\n        data: response.data || [],\n        total: response.total || 0,\n        page: response.page || 1,\n        limit: response.limit || 10\n      };\n    } catch (error) {\n      console.error('获取信息列表失败:', error);\n      return {\n        success: false,\n        data: [],\n        message: error.message || '获取信息失败'\n      };\n    }\n  }\n\n  /**\n   * 获取置顶信息列表\n   * @param {Object} params - 查询参数\n   * @returns {Promise} 置顶信息列表\n   */\n  async getToppedInfo(params = {}) {\n    return await this.getInfoList({ ...params, type: 'topped' });\n  }\n\n  /**\n   * 获取信息详情\n   * @param {number} infoId - 信息ID\n   * @returns {Promise} 信息详情\n   */\n  async getInfoDetail(infoId) {\n    try {\n      const response = await request.get(`/content/info/${infoId}`);\n      \n      return {\n        success: true,\n        data: response.data\n      };\n    } catch (error) {\n      console.error('获取信息详情失败:', error);\n      return {\n        success: false,\n        message: error.message || '获取信息详情失败'\n      };\n    }\n  }\n\n  /**\n   * 发布信息\n   * @param {Object} infoData - 信息数据\n   * @returns {Promise} 发布结果\n   */\n  async publishInfo(infoData) {\n    try {\n      const response = await request.post('/content/info', infoData);\n      \n      return {\n        success: true,\n        data: response.data,\n        message: '发布成功'\n      };\n    } catch (error) {\n      console.error('发布信息失败:', error);\n      return {\n        success: false,\n        message: error.message || '发布失败，请重试'\n      };\n    }\n  }\n\n  /**\n   * 获取商家分类列表\n   * @returns {Promise} 分类列表\n   */\n  async getBusinessCategories() {\n    try {\n      const response = await request.get('/content/business/categories');\n      \n      return {\n        success: true,\n        data: response.data || []\n      };\n    } catch (error) {\n      console.error('获取商家分类失败:', error);\n      return {\n        success: false,\n        data: [],\n        message: error.message || '获取分类失败'\n      };\n    }\n  }\n\n  /**\n   * 获取商家列表\n   * @param {Object} params - 查询参数\n   * @param {number} params.page - 页码\n   * @param {number} params.limit - 每页数量\n   * @param {number} params.category_id - 分类ID\n   * @returns {Promise} 商家列表\n   */\n  async getBusinessList(params = {}) {\n    try {\n      const queryParams = {\n        page: params.page || 1,\n        limit: params.limit || 10,\n        category_id: params.category_id || null\n      };\n\n      const response = await request.get('/content/business', { params: queryParams });\n      \n      return {\n        success: true,\n        data: response.data || [],\n        total: response.total || 0,\n        page: response.page || 1,\n        limit: response.limit || 10\n      };\n    } catch (error) {\n      console.error('获取商家列表失败:', error);\n      return {\n        success: false,\n        data: [],\n        message: error.message || '获取商家失败'\n      };\n    }\n  }\n\n  /**\n   * 获取商家详情\n   * @param {number} businessId - 商家ID\n   * @returns {Promise} 商家详情\n   */\n  async getBusinessDetail(businessId) {\n    try {\n      const response = await request.get(`/content/business/${businessId}`);\n      \n      return {\n        success: true,\n        data: response.data\n      };\n    } catch (error) {\n      console.error('获取商家详情失败:', error);\n      return {\n        success: false,\n        message: error.message || '获取商家详情失败'\n      };\n    }\n  }\n\n  /**\n   * 上传图片\n   * @param {string} filePath - 图片路径\n   * @param {string} type - 上传类型 (news, info, business, avatar)\n   * @returns {Promise} 上传结果\n   */\n  async uploadImage(filePath, type = 'info') {\n    try {\n      return new Promise((resolve, reject) => {\n        uni.uploadFile({\n          url: `${request.defaults.baseURL}/content/upload`,\n          filePath: filePath,\n          name: 'file',\n          formData: {\n            type: type\n          },\n          header: {\n            'Authorization': `Bearer ${uni.getStorageSync('token')}`\n          },\n          success: (res) => {\n            try {\n              const data = JSON.parse(res.data);\n              if (data.success) {\n                resolve({\n                  success: true,\n                  data: data.data,\n                  url: data.data.url\n                });\n              } else {\n                reject(new Error(data.message || '上传失败'));\n              }\n            } catch (error) {\n              reject(new Error('上传响应解析失败'));\n            }\n          },\n          fail: (error) => {\n            reject(new Error(error.errMsg || '上传失败'));\n          }\n        });\n      });\n    } catch (error) {\n      console.error('上传图片失败:', error);\n      return {\n        success: false,\n        message: error.message || '上传失败'\n      };\n    }\n  }\n\n  /**\n   * 搜索内容\n   * @param {Object} params - 搜索参数\n   * @param {string} params.keyword - 搜索关键词\n   * @param {string} params.type - 搜索类型 (news, info, business, all)\n   * @param {number} params.page - 页码\n   * @param {number} params.limit - 每页数量\n   * @returns {Promise} 搜索结果\n   */\n  async searchContent(params) {\n    try {\n      const queryParams = {\n        keyword: params.keyword,\n        type: params.type || 'all',\n        page: params.page || 1,\n        limit: params.limit || 10\n      };\n\n      const response = await request.get('/content/search', { params: queryParams });\n      \n      return {\n        success: true,\n        data: response.data || [],\n        total: response.total || 0,\n        page: response.page || 1,\n        limit: response.limit || 10\n      };\n    } catch (error) {\n      console.error('搜索内容失败:', error);\n      return {\n        success: false,\n        data: [],\n        message: error.message || '搜索失败'\n      };\n    }\n  }\n}\n\n// 创建单例实例\nconst contentApi = new ContentApiService();\n\nexport default contentApi;\n"], "names": ["request", "uni"], "mappings": ";;;AAQA,MAAM,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMtB,MAAM,oBAAoB;AACxB,QAAI;AACF,YAAM,WAAW,MAAMA,cAAAA,QAAQ,IAAI,0BAA0B;AAE7D,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,SAAS,QAAQ,CAAE;AAAA,MACjC;AAAA,IACK,SAAQ,OAAO;AACdC,oEAAc,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,CAAE;AAAA,QACR,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUD,MAAM,YAAY,SAAS,IAAI;AAC7B,QAAI;AACF,YAAM,cAAc;AAAA,QAClB,MAAM,OAAO,QAAQ;AAAA,QACrB,OAAO,OAAO,SAAS;AAAA,QACvB,aAAa,OAAO,eAAe;AAAA,MAC3C;AAEM,YAAM,WAAW,MAAMD,cAAAA,QAAQ,IAAI,iBAAiB,EAAE,QAAQ,YAAW,CAAE;AAE3E,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,SAAS,QAAQ,CAAE;AAAA,QACzB,OAAO,SAAS,SAAS;AAAA,QACzB,MAAM,SAAS,QAAQ;AAAA,QACvB,OAAO,SAAS,SAAS;AAAA,MACjC;AAAA,IACK,SAAQ,OAAO;AACdC,oEAAc,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,CAAE;AAAA,QACR,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,cAAc,QAAQ;AAC1B,QAAI;AACF,YAAM,WAAW,MAAMD,sBAAQ,IAAI,iBAAiB,MAAM,EAAE;AAE5D,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,SAAS;AAAA,MACvB;AAAA,IACK,SAAQ,OAAO;AACdC,oEAAc,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,oBAAoB;AACxB,QAAI;AACF,YAAM,WAAW,MAAMD,cAAAA,QAAQ,IAAI,0BAA0B;AAE7D,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,SAAS,QAAQ,CAAE;AAAA,MACjC;AAAA,IACK,SAAQ,OAAO;AACdC,qEAAc,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,CAAE;AAAA,QACR,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWD,MAAM,YAAY,SAAS,IAAI;AAC7B,QAAI;AACF,YAAM,cAAc;AAAA,QAClB,MAAM,OAAO,QAAQ;AAAA,QACrB,OAAO,OAAO,SAAS;AAAA,QACvB,aAAa,OAAO,eAAe;AAAA,QACnC,MAAM,OAAO,QAAQ;AAAA,MAC7B;AAEM,YAAM,WAAW,MAAMD,cAAAA,QAAQ,IAAI,iBAAiB,EAAE,QAAQ,YAAW,CAAE;AAE3E,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,SAAS,QAAQ,CAAE;AAAA,QACzB,OAAO,SAAS,SAAS;AAAA,QACzB,MAAM,SAAS,QAAQ;AAAA,QACvB,OAAO,SAAS,SAAS;AAAA,MACjC;AAAA,IACK,SAAQ,OAAO;AACdC,qEAAc,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,CAAE;AAAA,QACR,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,cAAc,SAAS,IAAI;AAC/B,WAAO,MAAM,KAAK,YAAY,EAAE,GAAG,QAAQ,MAAM,SAAQ,CAAE;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,cAAc,QAAQ;AAC1B,QAAI;AACF,YAAM,WAAW,MAAMD,sBAAQ,IAAI,iBAAiB,MAAM,EAAE;AAE5D,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,SAAS;AAAA,MACvB;AAAA,IACK,SAAQ,OAAO;AACdC,qEAAc,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,YAAY,UAAU;AAC1B,QAAI;AACF,YAAM,WAAW,MAAMD,cAAO,QAAC,KAAK,iBAAiB,QAAQ;AAE7D,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,SAAS;AAAA,QACf,SAAS;AAAA,MACjB;AAAA,IACK,SAAQ,OAAO;AACdC,oBAAc,MAAA,MAAA,SAAA,4BAAA,WAAW,KAAK;AAC9B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,wBAAwB;AAC5B,QAAI;AACF,YAAM,WAAW,MAAMD,cAAAA,QAAQ,IAAI,8BAA8B;AAEjE,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,SAAS,QAAQ,CAAE;AAAA,MACjC;AAAA,IACK,SAAQ,OAAO;AACdC,qEAAc,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,CAAE;AAAA,QACR,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUD,MAAM,gBAAgB,SAAS,IAAI;AACjC,QAAI;AACF,YAAM,cAAc;AAAA,QAClB,MAAM,OAAO,QAAQ;AAAA,QACrB,OAAO,OAAO,SAAS;AAAA,QACvB,aAAa,OAAO,eAAe;AAAA,MAC3C;AAEM,YAAM,WAAW,MAAMD,cAAAA,QAAQ,IAAI,qBAAqB,EAAE,QAAQ,YAAW,CAAE;AAE/E,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,SAAS,QAAQ,CAAE;AAAA,QACzB,OAAO,SAAS,SAAS;AAAA,QACzB,MAAM,SAAS,QAAQ;AAAA,QACvB,OAAO,SAAS,SAAS;AAAA,MACjC;AAAA,IACK,SAAQ,OAAO;AACdC,qEAAc,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,CAAE;AAAA,QACR,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,kBAAkB,YAAY;AAClC,QAAI;AACF,YAAM,WAAW,MAAMD,sBAAQ,IAAI,qBAAqB,UAAU,EAAE;AAEpE,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,SAAS;AAAA,MACvB;AAAA,IACK,SAAQ,OAAO;AACdC,qEAAc,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,MAAM,YAAY,UAAU,OAAO,QAAQ;AACzC,QAAI;AACF,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,GAAGD,cAAAA,QAAQ,SAAS,OAAO;AAAA,UAChC;AAAA,UACA,MAAM;AAAA,UACN,UAAU;AAAA,YACR;AAAA,UACD;AAAA,UACD,QAAQ;AAAA,YACN,iBAAiB,UAAUC,cAAAA,MAAI,eAAe,OAAO,CAAC;AAAA,UACvD;AAAA,UACD,SAAS,CAAC,QAAQ;AAChB,gBAAI;AACF,oBAAM,OAAO,KAAK,MAAM,IAAI,IAAI;AAChC,kBAAI,KAAK,SAAS;AAChB,wBAAQ;AAAA,kBACN,SAAS;AAAA,kBACT,MAAM,KAAK;AAAA,kBACX,KAAK,KAAK,KAAK;AAAA,gBACjC,CAAiB;AAAA,cACjB,OAAqB;AACL,uBAAO,IAAI,MAAM,KAAK,WAAW,MAAM,CAAC;AAAA,cACzC;AAAA,YACF,SAAQ,OAAO;AACd,qBAAO,IAAI,MAAM,UAAU,CAAC;AAAA,YAC7B;AAAA,UACF;AAAA,UACD,MAAM,CAAC,UAAU;AACf,mBAAO,IAAI,MAAM,MAAM,UAAU,MAAM,CAAC;AAAA,UACzC;AAAA,QACX,CAAS;AAAA,MACT,CAAO;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,4BAAA,WAAW,KAAK;AAC9B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWD,MAAM,cAAc,QAAQ;AAC1B,QAAI;AACF,YAAM,cAAc;AAAA,QAClB,SAAS,OAAO;AAAA,QAChB,MAAM,OAAO,QAAQ;AAAA,QACrB,MAAM,OAAO,QAAQ;AAAA,QACrB,OAAO,OAAO,SAAS;AAAA,MAC/B;AAEM,YAAM,WAAW,MAAMD,cAAAA,QAAQ,IAAI,mBAAmB,EAAE,QAAQ,YAAW,CAAE;AAE7E,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,SAAS,QAAQ,CAAE;AAAA,QACzB,OAAO,SAAS,SAAS;AAAA,QACzB,MAAM,SAAS,QAAQ;AAAA,QACvB,OAAO,SAAS,SAAS;AAAA,MACjC;AAAA,IACK,SAAQ,OAAO;AACdC,oBAAc,MAAA,MAAA,SAAA,4BAAA,WAAW,KAAK;AAC9B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,CAAE;AAAA,QACR,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AACH;AAGK,MAAC,aAAa,IAAI,kBAAiB;;"}