{"name": "typescript", "author": "Microsoft Corp.", "homepage": "https://www.typescriptlang.org/", "version": "3.7.5", "license": "Apache-2.0", "description": "TypeScript is a language for application scale JavaScript development", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript"], "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "repository": {"type": "git", "url": "https://github.com/Microsoft/TypeScript.git"}, "main": "./lib/typescript.js", "typings": "./lib/typescript.d.ts", "bin": {"tsc": "./bin/tsc", "tsserver": "./bin/tsserver"}, "engines": {"node": ">=4.2.0"}, "devDependencies": {"@octokit/rest": "latest", "@types/browserify": "latest", "@types/chai": "latest", "@types/convert-source-map": "latest", "@types/glob": "latest", "@types/gulp": "^4.0.5", "@types/gulp-concat": "latest", "@types/gulp-newer": "latest", "@types/gulp-rename": "0.0.33", "@types/gulp-sourcemaps": "0.0.32", "@types/jake": "latest", "@types/merge2": "latest", "@types/microsoft__typescript-etw": "latest", "@types/minimatch": "latest", "@types/minimist": "latest", "@types/mkdirp": "latest", "@types/mocha": "latest", "@types/ms": "latest", "@types/node": "latest", "@types/node-fetch": "^2.3.4", "@types/q": "latest", "@types/source-map-support": "latest", "@types/through2": "latest", "@types/travis-fold": "latest", "@types/xml2js": "^0.4.0", "@typescript-eslint/eslint-plugin": "2.3.2", "@typescript-eslint/experimental-utils": "2.3.2", "@typescript-eslint/parser": "2.3.2", "async": "latest", "azure-devops-node-api": "^8.0.0", "browser-resolve": "^1.11.2", "browserify": "latest", "chai": "latest", "chalk": "latest", "convert-source-map": "latest", "del": "5.1.0", "eslint": "6.5.1", "eslint-formatter-autolinkable-stylish": "1.0.3", "eslint-plugin-import": "2.18.2", "eslint-plugin-jsdoc": "15.9.9", "eslint-plugin-no-null": "1.0.2", "fancy-log": "latest", "fs-extra": "^6.0.1", "glob": "latest", "gulp": "^4.0.0", "gulp-concat": "latest", "gulp-insert": "latest", "gulp-newer": "latest", "gulp-rename": "latest", "gulp-sourcemaps": "latest", "istanbul": "latest", "merge2": "latest", "minimist": "latest", "mkdirp": "latest", "mocha": "latest", "mocha-fivemat-progress-reporter": "latest", "ms": "latest", "node-fetch": "^2.6.0", "plugin-error": "latest", "pretty-hrtime": "^1.0.3", "prex": "^0.4.3", "q": "latest", "remove-internal": "^2.9.2", "simple-git": "^1.113.0", "source-map-support": "latest", "through2": "latest", "travis-fold": "latest", "typescript": "next", "vinyl": "latest", "vinyl-sourcemaps-apply": "latest", "xml2js": "^0.4.19"}, "scripts": {"prepare": "gulp build-eslint-rules", "pretest": "gulp tests", "test": "gulp runtests-parallel --light=false", "test:eslint-rules": "gulp run-eslint-rules-tests", "build": "npm run build:compiler && npm run build:tests", "build:compiler": "gulp local", "build:tests": "gulp tests", "start": "node lib/tsc", "clean": "gulp clean", "gulp": "gulp", "jake": "gulp", "lint": "gulp lint", "lint:ci": "gulp lint --ci", "lint:compiler": "gulp lint-compiler", "lint:scripts": "gulp lint-scripts", "setup-hooks": "node scripts/link-hooks.js", "update-costly-tests": "node scripts/costly-tests.js"}, "browser": {"fs": false, "os": false, "path": false, "crypto": false, "buffer": false, "@microsoft/typescript-etw": false, "source-map-support": false, "inspector": false}, "dependencies": {}}