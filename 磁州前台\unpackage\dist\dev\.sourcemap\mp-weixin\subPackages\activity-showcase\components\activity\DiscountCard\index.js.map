{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/components/activity/DiscountCard/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7Avc3ViUGFja2FnZXMvYWN0aXZpdHktc2hvd2Nhc2UvY29tcG9uZW50cy9hY3Rpdml0eS9EaXNjb3VudENhcmQvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <!-- 满减活动卡片 - 苹果风格设计 -->\r\n  <view class=\"discount-card\">\r\n    <!-- 使用基础活动卡片 -->\r\n    <ActivityCard \r\n      :item=\"item\" \r\n      @favorite=\"$emit('favorite', item.id)\"\r\n      @action=\"$emit('action', { id: item.id, type: item.type, status: item.status })\"\r\n    >\r\n      <!-- 满减特有信息插槽 -->\r\n      <template #special-info>\r\n        <view class=\"discount-special\">\r\n          <!-- 满减规则区域 -->\r\n          <view class=\"discount-rules\">\r\n            <view class=\"rules-header\">\r\n              <view class=\"rules-title\">满减规则</view>\r\n              <view class=\"merchant-count\" v-if=\"item.merchantCount\">\r\n                <text>{{item.merchantCount}}家商家参与</text>\r\n              </view>\r\n            </view>\r\n            \r\n            <view class=\"rules-list\">\r\n              <view \r\n                class=\"rule-item\" \r\n                v-for=\"(rule, index) in item.discountRules\" \r\n                :key=\"index\"\r\n                :class=\"{'best-rule': index === item.discountRules.length - 1}\"\r\n              >\r\n                <view class=\"rule-badge\" v-if=\"index === item.discountRules.length - 1\">\r\n                  <text>最优</text>\r\n                </view>\r\n                <view class=\"rule-content\">\r\n                  <text class=\"rule-text\">满{{rule.threshold}}减{{rule.discount}}</text>\r\n                  <text class=\"rule-save\">立省{{rule.discount}}元</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 活动时间 -->\r\n          <view class=\"discount-period\" v-if=\"item.startDate && item.endDate\">\r\n            <view class=\"period-icon\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\r\n                <rect x=\"3\" y=\"4\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"></rect>\r\n                <line x1=\"16\" y1=\"2\" x2=\"16\" y2=\"6\"></line>\r\n                <line x1=\"8\" y1=\"2\" x2=\"8\" y2=\"6\"></line>\r\n                <line x1=\"3\" y1=\"10\" x2=\"21\" y2=\"10\"></line>\r\n              </svg>\r\n            </view>\r\n            <text class=\"period-text\">活动时间: {{item.startDate}} - {{item.endDate}}</text>\r\n          </view>\r\n          \r\n          <!-- 使用范围 -->\r\n          <view class=\"discount-scope\" v-if=\"item.scope\">\r\n            <view class=\"scope-icon\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\r\n                <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path>\r\n                <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\r\n              </svg>\r\n            </view>\r\n            <text class=\"scope-text\">{{item.scope}}</text>\r\n          </view>\r\n          \r\n          <!-- 参与商家 -->\r\n          <view class=\"discount-merchants\" v-if=\"item.merchants && item.merchants.length > 0\">\r\n            <view class=\"merchants-header\">\r\n              <text class=\"merchants-title\">参与商家</text>\r\n              <text class=\"merchants-count\">共{{item.merchants.length}}家</text>\r\n            </view>\r\n            <view class=\"merchants-list\">\r\n              <view \r\n                class=\"merchant-item\" \r\n                v-for=\"(merchant, index) in displayMerchants\" \r\n                :key=\"index\"\r\n              >\r\n                <image \r\n                  :src=\"merchant.logo\" \r\n                  class=\"merchant-logo\" \r\n                  mode=\"aspectFill\"\r\n                ></image>\r\n                <text class=\"merchant-name\">{{merchant.name}}</text>\r\n              </view>\r\n              <view class=\"more-merchants\" v-if=\"item.merchants.length > maxDisplayMerchants\">\r\n                <text>更多 ></text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </template>\r\n    </ActivityCard>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { computed } from 'vue';\r\nimport ActivityCard from '../ActivityCard.vue';\r\n\r\nconst props = defineProps({\r\n  item: {\r\n    type: Object,\r\n    required: true\r\n  }\r\n});\r\n\r\n// 最大显示商家数量\r\nconst maxDisplayMerchants = 3;\r\n\r\n// 显示的商家\r\nconst displayMerchants = computed(() => {\r\n  if (!props.item.merchants || !props.item.merchants.length) return [];\r\n  return props.item.merchants.slice(0, maxDisplayMerchants);\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n/* 满减活动卡片特有样式 */\r\n.discount-card {\r\n  /* 继承基础卡片样式 */\r\n}\r\n\r\n/* 满减特有信息区域 */\r\n.discount-special {\r\n  padding: 20rpx;\r\n  background-color: rgba(90, 200, 250, 0.05);\r\n  border-radius: 20rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n/* 满减规则区域 */\r\n.discount-rules {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.rules-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.rules-title {\r\n  font-size: 26rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n}\r\n\r\n.merchant-count {\r\n  font-size: 22rpx;\r\n  color: #5ac8fa;\r\n  background-color: rgba(90, 200, 250, 0.1);\r\n  padding: 4rpx 12rpx;\r\n  border-radius: 16rpx;\r\n}\r\n\r\n.rules-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12rpx;\r\n}\r\n\r\n.rule-item {\r\n  position: relative;\r\n  background-color: rgba(90, 200, 250, 0.08);\r\n  border-radius: 16rpx;\r\n  padding: 12rpx 16rpx;\r\n  border: 1rpx solid rgba(90, 200, 250, 0.2);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.best-rule {\r\n  background-color: rgba(90, 200, 250, 0.15);\r\n  border: 1rpx solid rgba(90, 200, 250, 0.4);\r\n  transform: scale(1.02);\r\n}\r\n\r\n.rule-badge {\r\n  position: absolute;\r\n  top: -10rpx;\r\n  right: 16rpx;\r\n  background-color: #5ac8fa;\r\n  color: #ffffff;\r\n  font-size: 20rpx;\r\n  font-weight: 600;\r\n  padding: 4rpx 12rpx;\r\n  border-radius: 10rpx;\r\n  box-shadow: 0 4rpx 8rpx rgba(90, 200, 250, 0.3);\r\n}\r\n\r\n.rule-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.rule-text {\r\n  font-size: 26rpx;\r\n  color: #333333;\r\n  font-weight: 600;\r\n}\r\n\r\n.rule-save {\r\n  font-size: 22rpx;\r\n  color: #5ac8fa;\r\n}\r\n\r\n/* 活动时间 */\r\n.discount-period {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.period-icon {\r\n  margin-right: 8rpx;\r\n  color: #5ac8fa;\r\n}\r\n\r\n.period-text {\r\n  font-size: 24rpx;\r\n  color: #333333;\r\n}\r\n\r\n/* 使用范围 */\r\n.discount-scope {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.scope-icon {\r\n  margin-right: 8rpx;\r\n  color: #5ac8fa;\r\n}\r\n\r\n.scope-text {\r\n  font-size: 24rpx;\r\n  color: #333333;\r\n}\r\n\r\n/* 参与商家 */\r\n.discount-merchants {\r\n  margin-top: 16rpx;\r\n  border-top: 1rpx dashed rgba(90, 200, 250, 0.3);\r\n  padding-top: 16rpx;\r\n}\r\n\r\n.merchants-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.merchants-title {\r\n  font-size: 26rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n}\r\n\r\n.merchants-count {\r\n  font-size: 22rpx;\r\n  color: #5ac8fa;\r\n}\r\n\r\n.merchants-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16rpx;\r\n}\r\n\r\n.merchant-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  width: calc((100% - 32rpx) / 3);\r\n}\r\n\r\n.merchant-logo {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  border-radius: 40rpx;\r\n  border: 1rpx solid rgba(90, 200, 250, 0.2);\r\n  margin-bottom: 8rpx;\r\n  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.merchant-name {\r\n  font-size: 22rpx;\r\n  color: #333333;\r\n  width: 100%;\r\n  text-align: center;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.more-merchants {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: calc((100% - 32rpx) / 3);\r\n  height: 110rpx;\r\n}\r\n\r\n.more-merchants text {\r\n  font-size: 24rpx;\r\n  color: #5ac8fa;\r\n}\r\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/components/activity/DiscountCard/index.vue'\nwx.createComponent(Component)"], "names": ["computed"], "mappings": ";;;;;;;;;;;;;AA+FA,MAAM,eAAe,MAAW;AAUhC,MAAM,sBAAsB;;;;;;;;;;AAR5B,UAAM,QAAQ;AAWd,UAAM,mBAAmBA,cAAQ,SAAC,MAAM;AACtC,UAAI,CAAC,MAAM,KAAK,aAAa,CAAC,MAAM,KAAK,UAAU;AAAQ,eAAO;AAClE,aAAO,MAAM,KAAK,UAAU,MAAM,GAAG,mBAAmB;AAAA,IAC1D,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9GD,GAAG,gBAAgB,SAAS;"}