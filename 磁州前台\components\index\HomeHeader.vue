<template>
  <!-- 首页顶部导航 -->
  <view class="header-container">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 导航栏 -->
    <view class="nav-bar" :style="{ opacity: navOpacity }">
      <!-- 左侧位置 -->
      <view class="location-btn" @click="openLocationPicker">
        <text class="location-icon uni-icon-location"></text>
        <text class="location-text">{{ locationName }}</text>
        <text class="location-arrow uni-icon-arrowdown"></text>
      </view>
      
      <!-- 中间搜索框 -->
      <view class="search-box" @click="navigateToSearch">
        <text class="search-icon uni-icon-search"></text>
        <text class="search-placeholder">搜索商家、服务、资讯</text>
      </view>
      
      <!-- 右侧消息 -->
      <view class="message-btn" @click="navigateToMessage">
        <text class="message-icon uni-icon-notification"></text>
        <view class="message-badge" v-if="unreadCount > 0">{{ unreadCount > 99 ? '99+' : unreadCount }}</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'HomeHeader',
  props: {
    navOpacity: {
      type: Number,
      default: 0.85
    },
    locationName: {
      type: String,
      default: '磁县'
    },
    unreadCount: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      statusBarHeight: 20
    };
  },
  created() {
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight || 20;
  },
  methods: {
    // 打开位置选择器
    openLocationPicker() {
      this.$emit('location-click');
    },
    
    // 跳转到搜索页面
    navigateToSearch() {
      uni.navigateTo({
        url: '/pages/search/index',
        fail: () => {
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    },
    
    // 跳转到消息页面
    navigateToMessage() {
      uni.navigateTo({
        url: '/pages/message/index',
        fail: () => {
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.header-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.status-bar {
  width: 100%;
  background-color: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 90rpx;
  padding: 0 20rpx;
  background-color: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.location-btn {
  display: flex;
  align-items: center;
  padding: 10rpx 16rpx;
  border-radius: 30rpx;
  background-color: rgba(255, 255, 255, 0.6);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.location-btn:active {
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(0.96);
}

.location-icon {
  font-size: 28rpx;
  color: #007AFF;
  margin-right: 6rpx;
}

.location-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  max-width: 120rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.location-arrow {
  font-size: 24rpx;
  color: #999;
  margin-left: 4rpx;
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  height: 60rpx;
  background-color: rgba(242, 242, 247, 0.8);
  border-radius: 30rpx;
  margin: 0 20rpx;
  padding: 0 20rpx;
  transition: all 0.2s ease;
}

.search-box:active {
  background-color: rgba(242, 242, 247, 1);
  transform: scale(0.98);
}

.search-icon {
  font-size: 28rpx;
  color: #999;
  margin-right: 10rpx;
}

.search-placeholder {
  font-size: 26rpx;
  color: #999;
}

.message-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70rpx;
  height: 70rpx;
  position: relative;
}

.message-icon {
  font-size: 40rpx;
  color: #333;
}

.message-badge {
  position: absolute;
  top: 0;
  right: 0;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background-color: #FF3B30;
  color: #fff;
  font-size: 20rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6rpx;
  box-sizing: border-box;
  border: 2rpx solid #fff;
  transform: translate(30%, -30%);
}
</style> 