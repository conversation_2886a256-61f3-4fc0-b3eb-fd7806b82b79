/*
    Name:     <PERSON><PERSON> (light)
    Author:   <PERSON>
    License:  Creative Commons Attribution-ShareAlike 4.0 Unported License
    URL:      https://github.com/idleberg/<PERSON><PERSON>-highlight.js
*/

/* <PERSON><PERSON> Comment */
.hljs-comment,
.hljs-quote {
  color: #a57a4c;
}

/* <PERSON><PERSON> Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-meta {
  color: #dc3958;
}

/* <PERSON>bie Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-deletion,
.hljs-link {
  color: #f79a32;
}

/* <PERSON><PERSON> Yellow */
.hljs-title,
.hljs-section,
.hljs-attribute {
  color: #f06431;
}

/* <PERSON><PERSON> */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
  color: #889b4a;
}

/* <PERSON><PERSON> Purple */
.hljs-keyword,
.hljs-selector-tag,
.hljs-function {
  color: #98676a;
}

.hljs {
  display: block;
  overflow-x: auto;
  background: #fbebd4;
  color: #84613d;
  padding: 0.5em;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}
