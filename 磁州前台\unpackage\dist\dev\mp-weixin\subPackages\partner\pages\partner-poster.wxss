/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-f8ca8baa, html.data-v-f8ca8baa, #app.data-v-f8ca8baa, .index-container.data-v-f8ca8baa {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.poster-container.data-v-f8ca8baa {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
  padding-top: calc(44px + var(--status-bar-height));
}

/* 自定义导航栏 */
.custom-navbar.data-v-f8ca8baa {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  padding: 0 30rpx;
  padding-top: 44px;
  /* 状态栏高度 */
  position: fixed;
  /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  background-image: linear-gradient(135deg, #0066FF, #0052CC);
  /* 改为与发布页一致的渐变角度 */
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.15);
  z-index: 100;
  /* 提高z-index确保在最上层 */
}
.navbar-title.data-v-f8ca8baa {
  position: absolute;
  left: 0;
  right: 0;
  font-size: 36rpx;
  font-weight: 700;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  text-align: center;
}
.navbar-right.data-v-f8ca8baa {
  width: 40rpx;
  height: 40rpx;
}
.navbar-left.data-v-f8ca8baa {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 20;
  /* 确保在标题上层，可以被点击 */
}
.back-icon.data-v-f8ca8baa {
  width: 100%;
  height: 100%;
}
.safe-area-top.data-v-f8ca8baa {
  height: var(--status-bar-height);
  width: 100%;
  background-image: linear-gradient(135deg, #0066FF, #0052CC);
}

/* 海报预览区域 */
.poster-preview.data-v-f8ca8baa {
  margin: 30rpx;
  border-radius: 20rpx;
  background-color: #fff;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
}
.preview-header.data-v-f8ca8baa {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.preview-title.data-v-f8ca8baa {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.template-switch.data-v-f8ca8baa {
  display: flex;
  align-items: center;
  background-color: #E6F4FF;
  border-radius: 30rpx;
  padding: 12rpx 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(22, 119, 255, 0.1);
  transition: all 0.3s;
}
.template-switch.data-v-f8ca8baa:active {
  transform: scale(0.95);
  background-color: #D1EBFF;
}
.switch-text.data-v-f8ca8baa {
  font-size: 24rpx;
  color: #1677FF;
  font-weight: 500;
}
.switch-icon.data-v-f8ca8baa {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}
.poster-card.data-v-f8ca8baa {
  position: relative;
  width: 100%;
  padding: 30rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
}
.poster-image.data-v-f8ca8baa {
  width: 80%;
  display: block;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  min-height: 400rpx;
  object-fit: contain;
}
.poster-loading.data-v-f8ca8baa {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
}
.loading-spinner.data-v-f8ca8baa {
  width: 80rpx;
  height: 80rpx;
  border: 8rpx solid #f3f3f3;
  border-top: 8rpx solid #1677FF;
  border-radius: 50%;
  animation: spin-f8ca8baa 1s linear infinite;
  margin-bottom: 20rpx;
}
@keyframes spin-f8ca8baa {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.loading-text.data-v-f8ca8baa {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

/* 新的美化后的按钮组样式 */
.new-poster-actions.data-v-f8ca8baa {
  display: flex;
  justify-content: space-around;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  margin-top: 20rpx;
}
.action-button.data-v-f8ca8baa {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: transparent;
  padding: 0;
  line-height: normal;
  width: 33%;
  height: auto;
}
.action-button.data-v-f8ca8baa::after {
  border: none;
}
.button-icon-wrap.data-v-f8ca8baa {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f0f5ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  transition: all 0.3s;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.save-icon-bg.data-v-f8ca8baa {
  background-color: #e6f7ff;
}
.share-icon-bg.data-v-f8ca8baa {
  background-color: #f6ffed;
}
.button-icon.data-v-f8ca8baa {
  width: 40rpx;
  height: 40rpx;
}
.button-text.data-v-f8ca8baa {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}
.refresh-button:active .button-icon-wrap.data-v-f8ca8baa {
  transform: rotate(180deg);
}
.save-button:active .button-icon-wrap.data-v-f8ca8baa,
.share-button:active .button-icon-wrap.data-v-f8ca8baa {
  transform: scale(0.9);
}

/* 统计卡片 */
.stats-card.data-v-f8ca8baa {
  margin: 30rpx;
  border-radius: 20rpx;
  background-color: #fff;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
}
.stats-header.data-v-f8ca8baa {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.stats-title.data-v-f8ca8baa {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.stats-content.data-v-f8ca8baa {
  display: flex;
  padding: 30rpx 0;
}
.stats-item.data-v-f8ca8baa {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stats-value.data-v-f8ca8baa {
  font-size: 36rpx;
  font-weight: 600;
  color: #1677FF;
  margin-bottom: 10rpx;
}
.stats-label.data-v-f8ca8baa {
  font-size: 24rpx;
  color: #999999;
}
.stats-divider.data-v-f8ca8baa {
  width: 1rpx;
  height: 60rpx;
  background-color: #f0f0f0;
  align-self: center;
}

/* 佣金卡片 */
.commission-card.data-v-f8ca8baa {
  margin: 30rpx;
  border-radius: 20rpx;
  background-color: #fff;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
}
.commission-header.data-v-f8ca8baa {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.commission-title.data-v-f8ca8baa {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.commission-content.data-v-f8ca8baa {
  padding: 20rpx 30rpx;
}
.commission-item.data-v-f8ca8baa {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.commission-item.data-v-f8ca8baa:last-child {
  border-bottom: none;
}
.commission-info.data-v-f8ca8baa {
  flex: 1;
}
.commission-label.data-v-f8ca8baa {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8rpx;
}
.commission-desc.data-v-f8ca8baa {
  font-size: 24rpx;
  color: #999999;
}
.commission-value.data-v-f8ca8baa {
  font-size: 40rpx;
  font-weight: 700;
  color: #FF6B00;
  background-color: rgba(255, 107, 0, 0.1);
  padding: 10rpx 24rpx;
  border-radius: 30rpx;
}
.level-tag.data-v-f8ca8baa {
  font-size: 24rpx;
  font-weight: 500;
  color: #FF6B00;
  background-color: rgba(255, 107, 0, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}
.level-benefits.data-v-f8ca8baa {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
}
.benefit-item.data-v-f8ca8baa {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.benefit-icon-wrap.data-v-f8ca8baa {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10rpx;
}
.benefit-text.data-v-f8ca8baa {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
}
.upgrade-tips.data-v-f8ca8baa {
  margin-top: 10rpx;
  text-align: right;
}
.tips-text.data-v-f8ca8baa {
  font-size: 24rpx;
  color: #999999;
}
.upgrade-btn.data-v-f8ca8baa {
  display: flex;
  align-items: center;
  background-color: #E6F4FF;
  border-radius: 30rpx;
  padding: 8rpx 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(22, 119, 255, 0.1);
  transition: all 0.3s;
}
.upgrade-btn.data-v-f8ca8baa:active {
  transform: scale(0.95);
}
.upgrade-icon.data-v-f8ca8baa {
  width: 20rpx;
  height: 20rpx;
  margin-left: 8rpx;
}

/* 步骤卡片 */
.steps-card.data-v-f8ca8baa {
  margin: 30rpx;
  border-radius: 20rpx;
  background-color: #fff;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
}
.steps-header.data-v-f8ca8baa {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.steps-title.data-v-f8ca8baa {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.steps-content.data-v-f8ca8baa {
  padding: 30rpx;
}
.step-item.data-v-f8ca8baa {
  display: flex;
  align-items: center;
}
.step-icon-wrap.data-v-f8ca8baa {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
  flex-shrink: 0;
}
.step1-bg.data-v-f8ca8baa {
  background-color: rgba(22, 119, 255, 0.1);
}
.step2-bg.data-v-f8ca8baa {
  background-color: rgba(255, 149, 0, 0.1);
}
.step3-bg.data-v-f8ca8baa {
  background-color: rgba(52, 199, 89, 0.1);
}
.step4-bg.data-v-f8ca8baa {
  background-color: rgba(255, 59, 48, 0.1);
}
.step-icon.data-v-f8ca8baa {
  width: 40rpx;
  height: 40rpx;
}
.step-info.data-v-f8ca8baa {
  flex: 1;
}
.step-name.data-v-f8ca8baa {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8rpx;
}
.step-desc.data-v-f8ca8baa {
  font-size: 26rpx;
  color: #999999;
}
.step-connector.data-v-f8ca8baa {
  width: 2rpx;
  height: 40rpx;
  background-color: #E6E6E6;
  margin-left: 40rpx;
  margin-bottom: 10rpx;
  margin-top: 10rpx;
}

/* 规则卡片 */
.rules-card.data-v-f8ca8baa {
  margin: 30rpx;
  border-radius: 20rpx;
  background-color: #fff;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
}
.rules-header.data-v-f8ca8baa {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
}
.rules-title.data-v-f8ca8baa {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-right: 16rpx;
}
.rules-tag.data-v-f8ca8baa {
  font-size: 20rpx;
  color: #FF6B00;
  background-color: rgba(255, 107, 0, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}
.rules-content.data-v-f8ca8baa {
  padding: 20rpx 30rpx;
}
.rule-item.data-v-f8ca8baa {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}
.rule-item.data-v-f8ca8baa:last-child {
  margin-bottom: 0;
}
.rule-dot.data-v-f8ca8baa {
  width: 12rpx;
  height: 12rpx;
  background-color: #1677FF;
  border-radius: 50%;
  margin-top: 12rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}
.silver-dot.data-v-f8ca8baa {
  background-color: #C0C0C0;
}
.gold-dot.data-v-f8ca8baa {
  background-color: #FFD700;
}
.diamond-dot.data-v-f8ca8baa {
  background-color: #B9F2FF;
  box-shadow: 0 0 5rpx #B9F2FF;
}
.rule-text.data-v-f8ca8baa {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.6;
  flex: 1;
}
.upgrade-action.data-v-f8ca8baa {
  padding: 20rpx 30rpx 30rpx;
  display: flex;
  justify-content: center;
}
.upgrade-now-btn.data-v-f8ca8baa {
  width: 80%;
  height: 80rpx;
  line-height: 80rpx;
  background-image: linear-gradient(135deg, #FF9500, #FF6B00);
  color: #fff;
  font-size: 30rpx;
  font-weight: 500;
  border-radius: 40rpx;
  box-shadow: 0 6rpx 12rpx rgba(255, 107, 0, 0.2);
  transition: all 0.3s;
}
.upgrade-now-btn.data-v-f8ca8baa:active {
  transform: scale(0.98);
  box-shadow: 0 3rpx 6rpx rgba(255, 107, 0, 0.3);
}

/* 模板选择弹窗 */
.template-modal.data-v-f8ca8baa, .share-modal.data-v-f8ca8baa {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: flex-end;
  z-index: 999;
}
.template-modal.data-v-f8ca8baa {
  align-items: center;
}
.share-content.data-v-f8ca8baa {
  width: 100%;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
  animation: slideUp-f8ca8baa 0.3s ease-out;
}
@keyframes slideUp-f8ca8baa {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
.share-header.data-v-f8ca8baa {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.share-title.data-v-f8ca8baa {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.close-btn.data-v-f8ca8baa {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
  padding: 10rpx;
}
.share-options.data-v-f8ca8baa {
  display: flex;
  flex-wrap: wrap;
  padding: 40rpx 20rpx 20rpx;
}
.share-option.data-v-f8ca8baa {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}
.share-icon-wrap.data-v-f8ca8baa {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  transition: all 0.3s;
}
.wechat-bg.data-v-f8ca8baa {
  background-color: #95EC69;
}
.moments-bg.data-v-f8ca8baa {
  background-color: #FFD666;
}
.qq-bg.data-v-f8ca8baa {
  background-color: #91D5FF;
}
.weibo-bg.data-v-f8ca8baa {
  background-color: #FFA39E;
}
.share-option:active .share-icon-wrap.data-v-f8ca8baa {
  transform: scale(0.9);
}
.share-icon.data-v-f8ca8baa {
  width: 50rpx;
  height: 50rpx;
}
.share-name.data-v-f8ca8baa {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
}
.cancel-btn.data-v-f8ca8baa {
  width: 100%;
  height: 100rpx;
  line-height: 100rpx;
  background-color: #fff;
  color: #333333;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 0;
  border-top: 10rpx solid #f5f5f7;
}

/* 底部操作按钮 */
.bottom-actions.data-v-f8ca8baa {
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.generate-btn.data-v-f8ca8baa {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-image: linear-gradient(135deg, #0066FF, #0052CC);
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 45rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 82, 204, 0.2);
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-icon.data-v-f8ca8baa {
  width: 36rpx;
  height: 36rpx;
  margin-right: 12rpx;
}
.btn-text.data-v-f8ca8baa {
  font-weight: 600;
  letter-spacing: 2rpx;
}
.generate-btn.data-v-f8ca8baa::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(120deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0) 100%);
  opacity: 0.6;
  transition: all 0.8s;
}
.generate-btn.data-v-f8ca8baa:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 8rpx rgba(0, 82, 204, 0.3);
}
.generate-btn.data-v-f8ca8baa:active::after {
  left: 100%;
}

/* 模板选择弹窗 */
.modal-content.data-v-f8ca8baa {
  width: 80%;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  animation: fadeIn-f8ca8baa 0.3s ease-out;
}
@keyframes fadeIn-f8ca8baa {
from {
    opacity: 0;
    transform: scale(0.9);
}
to {
    opacity: 1;
    transform: scale(1);
}
}
.modal-header.data-v-f8ca8baa {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.modal-title.data-v-f8ca8baa {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.template-list.data-v-f8ca8baa {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
  max-height: 600rpx;
}
.template-item.data-v-f8ca8baa {
  position: relative;
  width: calc(50% - 20rpx);
  margin: 10rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  background-color: #f5f7fa;
  transition: all 0.3s;
}
.template-item.data-v-f8ca8baa:active {
  transform: scale(0.98);
}
.template-item.active.data-v-f8ca8baa {
  box-shadow: 0 4rpx 16rpx rgba(22, 119, 255, 0.3);
}
.template-image.data-v-f8ca8baa {
  width: 100%;
  height: 300rpx;
  display: block;
  object-fit: contain;
}
.template-mask.data-v-f8ca8baa {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(22, 119, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}
.check-icon.data-v-f8ca8baa {
  width: 60rpx;
  height: 60rpx;
}
.confirm-btn.data-v-f8ca8baa {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #1677FF;
  color: #fff;
  font-size: 30rpx;
  font-weight: 500;
  border-radius: 0;
}