<view class="page-root"><view class="nav-bg" style="{{'height:' + a}}"></view><view class="navbar-content" style="{{'top:' + d + ';' + ('height:' + '44px')}}"><view class="navbar-left" bindtap="{{c}}"><image class="back-icon" src="{{b}}" mode="aspectFit"></image></view><text class="navbar-title">积分商城</text><view class="navbar-right"></view></view><view class="points-mall-container" style="{{'padding-top:' + E}}"><view class="points-balance-card"><view class="balance-container"><view class="balance-label">我的积分</view><view class="balance-value">{{e}}</view></view><view class="balance-actions-vertical"><view class="balance-detail" bindtap="{{f}}">积分明细</view><view class="history-btn" bindtap="{{g}}">兑换记录</view></view></view><view class="category-tabs"><scroll-view scroll-x class="tabs-scroll" show-scrollbar="false"><view wx:for="{{h}}" wx:for-item="category" wx:key="c" class="{{['tab-item', category.b && 'active']}}" bindtap="{{category.d}}">{{category.a}}</view></scroll-view></view><scroll-view scroll-y class="products-container" refresher-enabled bindrefresherrefresh="{{l}}" refresher-triggered="isRefreshing"><view class="product-grid"><view wx:for="{{i}}" wx:for-item="product" wx:key="i" class="product-item" bindtap="{{product.j}}"><image class="product-image" src="{{product.a}}" mode="aspectFill"></image><view class="product-info"><view class="product-name">{{product.b}}</view><view class="product-description">{{product.c}}</view><view class="product-bottom"><view class="product-points">{{product.d}}积分</view><view class="{{['exchange-btn', product.f && 'disabled']}}">{{product.e}}</view></view></view><view wx:if="{{product.g}}" class="product-tag">{{product.h}}</view></view></view><view wx:if="{{j}}" class="empty-state"><image class="empty-icon" src="{{k}}"></image><view class="empty-text">暂无商品，敬请期待</view></view></scroll-view><view wx:if="{{m}}" class="product-detail-popup" bindtap="{{D}}"><view class="popup-content"><view class="popup-close" bindtap="{{n}}">×</view><image class="popup-image" src="{{o}}" mode="aspectFill"></image><view class="popup-info"><view class="popup-name">{{p}}</view><view class="popup-description">{{q}}</view><view class="popup-points">{{r}} 积分</view><view wx:if="{{s}}" class="popup-rules"><view class="rules-title">兑换规则</view><view class="rules-content">{{t}}</view></view><view wx:if="{{v}}" class="popup-stock"><text class="stock-label">库存:</text><text class="stock-value">{{w}}</text></view><view wx:if="{{x}}" class="popup-validity"><text class="validity-label">有效期:</text><text class="validity-value">{{y}}</text></view><button class="{{['popup-btn', A && 'disabled']}}" disabled="{{B}}" bindtap="{{C}}">{{z}}</button></view></view></view></view></view>