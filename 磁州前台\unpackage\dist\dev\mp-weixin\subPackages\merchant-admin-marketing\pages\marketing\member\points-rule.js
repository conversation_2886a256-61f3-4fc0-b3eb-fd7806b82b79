"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      // 积分规则
      pointsRules: {
        consumptionRatio: 10,
        checkInPoints: 5,
        reviewPoints: 10,
        sharePoints: 5,
        registerPoints: 100,
        redemptionRatio: 100,
        minRedemption: 100,
        maxRedemptionRatio: 30,
        validity: 12,
        expirationReminder: true,
        reminderDays: 30
      },
      // 积分等级
      pointsLevels: [
        {
          id: 1,
          name: "普通会员",
          minPoints: 0,
          maxPoints: 999,
          benefits: ["基础积分兑换"]
        },
        {
          id: 2,
          name: "银卡会员",
          minPoints: 1e3,
          maxPoints: 4999,
          benefits: ["积分兑换9.5折", "生日双倍积分"]
        },
        {
          id: 3,
          name: "金卡会员",
          minPoints: 5e3,
          maxPoints: 19999,
          benefits: ["积分兑换9折", "生日双倍积分", "专属积分活动"]
        },
        {
          id: 4,
          name: "钻石会员",
          minPoints: 2e4,
          maxPoints: 99999,
          benefits: ["积分兑换8.5折", "生日三倍积分", "专属积分活动", "积分不过期"]
        }
      ]
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    toggleExpirationReminder(e) {
      this.pointsRules.expirationReminder = e.detail.value;
    },
    editLevel(level) {
      common_vendor.index.showToast({
        title: "编辑等级功能开发中",
        icon: "none"
      });
    },
    deleteLevel(level) {
      common_vendor.index.showModal({
        title: "删除确认",
        content: `确定要删除"${level.name}"等级吗？`,
        success: (res) => {
          if (res.confirm) {
            const index = this.pointsLevels.findIndex((item) => item.id === level.id);
            if (index !== -1) {
              this.pointsLevels.splice(index, 1);
              common_vendor.index.showToast({
                title: "删除成功",
                icon: "success"
              });
            }
          }
        }
      });
    },
    addLevel() {
      common_vendor.index.showToast({
        title: "添加等级功能开发中",
        icon: "none"
      });
    },
    saveRules() {
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "积分规则保存成功",
          icon: "success"
        });
      }, 1e3);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: $data.pointsRules.consumptionRatio,
    c: common_vendor.o(($event) => $data.pointsRules.consumptionRatio = $event.detail.value),
    d: $data.pointsRules.checkInPoints,
    e: common_vendor.o(($event) => $data.pointsRules.checkInPoints = $event.detail.value),
    f: $data.pointsRules.reviewPoints,
    g: common_vendor.o(($event) => $data.pointsRules.reviewPoints = $event.detail.value),
    h: $data.pointsRules.sharePoints,
    i: common_vendor.o(($event) => $data.pointsRules.sharePoints = $event.detail.value),
    j: $data.pointsRules.registerPoints,
    k: common_vendor.o(($event) => $data.pointsRules.registerPoints = $event.detail.value),
    l: $data.pointsRules.redemptionRatio,
    m: common_vendor.o(($event) => $data.pointsRules.redemptionRatio = $event.detail.value),
    n: $data.pointsRules.minRedemption,
    o: common_vendor.o(($event) => $data.pointsRules.minRedemption = $event.detail.value),
    p: $data.pointsRules.maxRedemptionRatio,
    q: common_vendor.o(($event) => $data.pointsRules.maxRedemptionRatio = $event.detail.value),
    r: $data.pointsRules.validity,
    s: common_vendor.o(($event) => $data.pointsRules.validity = $event.detail.value),
    t: $data.pointsRules.expirationReminder,
    v: common_vendor.o((...args) => $options.toggleExpirationReminder && $options.toggleExpirationReminder(...args)),
    w: $data.pointsRules.expirationReminder
  }, $data.pointsRules.expirationReminder ? {
    x: $data.pointsRules.reminderDays,
    y: common_vendor.o(($event) => $data.pointsRules.reminderDays = $event.detail.value)
  } : {}, {
    z: common_vendor.f($data.pointsLevels, (level, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(level.name),
        b: common_vendor.t(level.minPoints),
        c: common_vendor.t(level.maxPoints),
        d: common_vendor.f(level.benefits, (benefit, bIndex, i1) => {
          return {
            a: common_vendor.t(benefit),
            b: bIndex
          };
        }),
        e: common_vendor.o(($event) => $options.editLevel(level), index),
        f: index > 0
      }, index > 0 ? {
        g: common_vendor.o(($event) => $options.deleteLevel(level), index)
      } : {}, {
        h: index
      });
    }),
    A: common_vendor.o((...args) => $options.addLevel && $options.addLevel(...args)),
    B: common_vendor.o((...args) => $options.saveRules && $options.saveRules(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/member/points-rule.js.map
