<template>
	<view class="page-root">
		<view class="nav-bg" :style="{ height: (statusBarHeight + 44) + 'px' }"></view>
		<view class="navbar-content" :style="{ top: statusBarHeight + 'px', height: '44px' }">
			<view class="navbar-left" @click="goBack">
				<image class="back-icon" src="/static/images/tabbar/最新返回键.png" mode="aspectFit"></image>
			</view>
			<text class="navbar-title">积分商城</text>
			<view class="navbar-right"></view>
		</view>
		<view class="points-mall-container" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
		<!-- 积分余额卡片 -->
		<view class="points-balance-card">
			<view class="balance-container">
				<view class="balance-label">我的积分</view>
				<view class="balance-value">{{ userPoints }}</view>
			</view>
				<view class="balance-actions-vertical">
					<view class="balance-detail" @click="navigateTo('/subPackages/checkin/pages/points-detail')">积分明细</view>
					<view class="history-btn" @click="navigateTo('/subPackages/checkin/pages/exchange-history')">兑换记录</view>
			</view>
		</view>
		
		<!-- 商品分类标签 -->
		<view class="category-tabs">
			<scroll-view scroll-x class="tabs-scroll" show-scrollbar="false">
				<view 
					class="tab-item" 
					:class="{ active: currentCategory === category.id }"
					v-for="(category, index) in categories" 
					:key="index"
					@click="switchCategory(index)"
				>
					{{ category.name }}
				</view>
			</scroll-view>
		</view>
		
		<!-- 商品列表 -->
		<scroll-view scroll-y class="products-container" refresher-enabled @refresherrefresh="refreshProducts" refresher-triggered="isRefreshing">
			<view class="product-grid">
				<view class="product-item" v-for="(product, index) in filteredProducts" :key="index" @click="showProductDetail(product)">
					<image class="product-image" :src="product.image" mode="aspectFill"></image>
					<view class="product-info">
						<view class="product-name">{{ product.name }}</view>
						<view class="product-description">{{ product.description }}</view>
						<view class="product-bottom">
							<view class="product-points">{{ product.points }}积分</view>
							<view class="exchange-btn" :class="{ 'disabled': product.points > userPoints }">
								{{ product.points > userPoints ? '积分不足' : '立即兑换' }}
							</view>
						</view>
					</view>
					<view class="product-tag" v-if="product.tag">{{ product.tag }}</view>
				</view>
			</view>
			
			<!-- 空状态提示 -->
			<view class="empty-state" v-if="filteredProducts.length === 0">
				<image class="empty-icon" src="/static/images/tabbar/empty.png"></image>
				<view class="empty-text">暂无商品，敬请期待</view>
			</view>
		</scroll-view>
		
		<!-- 商品详情弹窗 -->
		<view class="product-detail-popup" v-if="showPopup" @click.self="showPopup = false">
			<view class="popup-content">
				<view class="popup-close" @click="showPopup = false">×</view>
				<image class="popup-image" :src="selectedProduct.image" mode="aspectFill"></image>
				<view class="popup-info">
					<view class="popup-name">{{ selectedProduct.name }}</view>
					<view class="popup-description">{{ selectedProduct.description }}</view>
					<view class="popup-points">{{ selectedProduct.points }} 积分</view>
					
					<view class="popup-rules" v-if="selectedProduct.rules">
						<view class="rules-title">兑换规则</view>
						<view class="rules-content">{{ selectedProduct.rules }}</view>
					</view>
					
					<view class="popup-stock" v-if="selectedProduct.stock">
						<text class="stock-label">库存:</text>
						<text class="stock-value">{{ selectedProduct.stock }}</text>
					</view>
					
					<view class="popup-validity" v-if="selectedProduct.validity">
						<text class="validity-label">有效期:</text>
						<text class="validity-value">{{ selectedProduct.validity }}</text>
					</view>
					
					<button class="popup-btn" 
						:class="{ 'disabled': selectedProduct.points > userPoints }"
						:disabled="selectedProduct.points > userPoints"
						@click="exchangeProduct">
						{{ selectedProduct.points > userPoints ? '积分不足' : '立即兑换' }}
					</button>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// Vue3迁移代码开始
// 响应式状态
const statusBarHeight = ref(20);
const userPoints = ref(1280);
const currentCategory = ref(0);
const isRefreshing = ref(false);
const showPopup = ref(false);
const selectedProduct = ref({});

// 常量数据
const categories = [
	{ id: 0, name: '全部' },
	{ id: 3, name: '虚拟商品' },
	{ id: 2, name: '实物商品' },
	{ id: 4, name: '限时兑换' },
	{ id: 1, name: '优惠券' }
];

// 商品数据
const products = ref([
	{
		id: 1,
		name: '5元通用券',
		description: '可用于平台内任意商家消费抵扣',
		points: 100,
		image: '/static/images/banner/coupon-1.png',
		category: 1,
		validity: '兑换后30天内有效',
		stock: 999,
		rules: '单笔消费满20元可用，每人每月限兑换5张',
		tag: '热门'
	},
	{
		id: 2,
		name: '10元外卖券',
		description: '仅限平台认证餐饮商家使用',
		points: 180,
		image: '/static/images/banner/coupon-2.png',
		category: 1,
		validity: '兑换后30天内有效',
		stock: 500,
		rules: '单笔消费满30元可用，每人每月限兑换3张'
	},
	{
		id: 3,
		name: '精美保温杯',
		description: '304不锈钢内胆，24小时保温',
		points: 1200,
		image: '/static/images/banner/product-1.png',
		category: 2,
		stock: 50,
		rules: '兑换后7天内发货，快递费用由平台承担'
	},
	{
		id: 4,
		name: '信息置顶服务',
		description: '发布信息优先展示24小时',
		points: 300,
		image: '/static/images/banner/service-1.png',
		category: 3,
		validity: '兑换后7天内有效',
		rules: '每个信息仅可使用一次，有效期内未使用将自动失效'
	},
	{
		id: 5,
		name: '会员月卡',
		description: '30天会员特权，免费发布信息',
		points: 500,
		image: '/static/images/banner/vip-1.png',
		category: 3,
		validity: '激活后30天内有效',
		rules: '包含会员所有特权，可与其他优惠共享使用',
		tag: '超值'
	},
	{
		id: 6,
		name: '限时抢购券',
		description: '周末专享，满100减50',
		points: 250,
		image: '/static/images/banner/coupon-3.png',
		category: 4,
		validity: '本周六、日两天有效',
		stock: 100,
		rules: '限指定商家使用，详见券面说明',
		tag: '限时'
	},
	{
		id: 7,
		name: '信息置顶卡（7天）',
		description: '让您的信息在同城列表置顶展示7天，曝光翻倍！',
		points: 500,
		image: '/static/images/banner/top-card-placeholder.png',
		category: 3,
		validity: '兑换后7天内有效',
		rules: '仅限同城信息使用，每条信息每月限用一次',
		tag: '虚拟'
	},
	{
		id: 8,
		name: 'VIP会员月卡',
		description: '享受专属标识、免费发布、优先审核等特权30天',
		points: 1200,
		image: '/static/images/banner/vip-card-placeholder.png',
		category: 3,
		validity: '激活后30天内有效',
		rules: '会员期间享受所有VIP特权',
		tag: '虚拟'
	},
	{
		id: 9,
		name: '积分抽奖券',
		description: '可参与平台积分抽奖活动，赢取实物大奖',
		points: 100,
		image: '/static/images/banner/lottery-placeholder.png',
		category: 3,
		validity: '兑换后30天内有效',
		rules: '每次抽奖消耗1张抽奖券',
		tag: '虚拟'
	},
	{
		id: 10,
		name: '本地活动报名券',
		description: '免费报名本地线下活动、沙龙、讲座等',
		points: 80,
		image: '/static/images/banner/event-ticket-placeholder.png',
		category: 3,
		validity: '兑换后30天内有效',
		rules: '部分活动需提前预约，具体以活动规则为准',
		tag: '虚拟'
	},
	{
		id: 11,
		name: '专属头像框',
		description: '兑换后可装饰个人头像，彰显个性',
		points: 60,
		image: '/static/images/banner/avatar-frame-placeholder.png',
		category: 3,
		validity: '永久有效',
		rules: '可在个人中心-头像设置中更换',
		tag: '虚拟'
	},
	{
		id: 12,
		name: '话费充值10元',
		description: '兑换后系统将自动为您绑定的手机号充值',
		points: 1000,
		image: '/static/images/banner/phone-recharge.png',
		category: 3,
		validity: '兑换后立即到账',
		rules: '仅限本人实名认证手机号使用，充值成功后不予退还',
		tag: '热门'
	},
	{
		id: 13,
		name: '话费充值30元',
		description: '兑换后系统将自动为您绑定的手机号充值',
		points: 2500,
		image: '/static/images/banner/phone-recharge.png',
		category: 3,
		validity: '兑换后立即到账',
		rules: '仅限本人实名认证手机号使用，充值成功后不予退还',
		tag: '超值'
	},
	{
		id: 14,
		name: '话费充值100元',
		description: '兑换后系统将自动为您绑定的手机号充值',
		points: 8000,
		image: '/static/images/banner/phone-recharge.png',
		category: 3,
		validity: '兑换后立即到账',
		rules: '仅限本人实名认证手机号使用，充值成功后不予退还',
		tag: '限量'
	}
]);

// 计算属性 - 根据当前分类筛选商品
const filteredProducts = computed(() => {
	if (currentCategory.value === 0) {
		// 全部分类下，按照以下顺序排序：话费充值、其他虚拟商品、实物商品、优惠券
		return [...products.value].sort((a, b) => {
			// 话费充值（虚拟商品且名称包含"话费"）排在最前面
			if (a.name.includes('话费') && !b.name.includes('话费')) return -1;
			if (!a.name.includes('话费') && b.name.includes('话费')) return 1;
			
			// 同为话费充值商品，按面额从小到大排序
			if (a.name.includes('话费') && b.name.includes('话费')) {
				const aValue = parseInt(a.name.match(/\d+/)[0]);
				const bValue = parseInt(b.name.match(/\d+/)[0]);
				return aValue - bValue;
			}
			
			// 非话费商品，按分类排序
			if (a.category !== b.category) {
				// 其他虚拟商品排第二
				if (a.category === 3) return -1;
				if (b.category === 3) return 1;
				
				// 实物商品排第三
				if (a.category === 2) return -1;
				if (b.category === 2) return 1;
				
				// 限时兑换排第四
				if (a.category === 4) return -1;
				if (b.category === 4) return 1;
				
				// 优惠券排最后
				return a.category - b.category;
			}
			
			// 同类型内部按ID排序
			return a.id - b.id;
		});
	} else {
		return products.value.filter(product => product.category === currentCategory.value);
	}
});

// 生命周期钩子
onMounted(() => {
	// 获取状态栏高度
	const sysInfo = uni.getSystemInfoSync();
	statusBarHeight.value = sysInfo.statusBarHeight || 20;
	
	// 获取用户积分
	getUserPoints();
	
	// 确保初始分类为"全部"
	currentCategory.value = 0;
});

// 方法
// 获取用户积分数据
function getUserPoints() {
	// 实际应用中应调用API获取
	// 示例使用模拟数据
	setTimeout(() => {
		// userPoints.value = 1280;
	}, 500);
}

// 切换商品分类
function switchCategory(index) {
	currentCategory.value = categories[index].id;
}

// 刷新商品列表
function refreshProducts(e) {
	isRefreshing.value = true;
	
	// 模拟刷新操作
	setTimeout(() => {
		isRefreshing.value = false;
		uni.showToast({
			title: '刷新成功',
			icon: 'none'
		});
	}, 1500);
}

// 显示商品详情
function showProductDetail(product) {
	selectedProduct.value = product;
	showPopup.value = true;
}

// 兑换商品
function exchangeProduct() {
	if (selectedProduct.value.points > userPoints.value) {
		uni.showToast({
			title: '积分不足',
			icon: 'none'
		});
		return;
	}
	
	uni.showModal({
		title: '确认兑换',
		content: `确定使用${selectedProduct.value.points}积分兑换"${selectedProduct.value.name}"吗？`,
		success: (res) => {
			if (res.confirm) {
				uni.showLoading({
					title: '兑换中...'
				});
				
				// 模拟兑换操作
				setTimeout(() => {
					userPoints.value -= selectedProduct.value.points;
					uni.hideLoading();
					showPopup.value = false;
					
					uni.showToast({
						title: '兑换成功',
						icon: 'success'
					});
				}, 1500);
			}
		}
	});
}

// 页面导航
function navigateTo(url) {
	uni.navigateTo({
		url: url
	});
}

// 返回上一页
function goBack() {
	uni.navigateBack();
}
// Vue3迁移代码结束
</script>

<style lang="scss">
.page-root {
	position: relative;
	min-height: 100vh;
	background: #f6faff;
}
.nav-bg {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	background: #1677FF;
	z-index: 100;
	width: 100%;
}
.navbar-content {
	position: fixed;
	left: 0;
	right: 0;
	z-index: 101;
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: transparent;
	width: 100%;
}
.navbar-left, .navbar-right {
	width: 44px;
	height: 44px;
	display: flex;
	align-items: center;
	justify-content: center;
}
.back-icon {
	width: 24px;
	height: 24px;
	display: block;
	background: none;
	border-radius: 0;
	margin: 0 auto;
}
.navbar-title {
	flex: 1;
	text-align: center;
	font-size: 18px;
	font-weight: 600;
	color: #fff;
	line-height: 44px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.history-btn {
	margin-top: 0;
	padding: 3px 18px;
	border: 1.5px solid #1677FF;
	color: #1677FF;
	border-radius: 16px;
	font-size: 13px;
	font-weight: 500;
	background: #fff;
	transition: background 0.2s, color 0.2s;
	line-height: 20px;
	display: inline-flex;
	align-items: center;
	justify-content: center;
}
.history-btn:active {
	background: #1677FF;
	color: #fff;
}
.points-mall-container {
	min-height: 100vh;
	position: relative;
	box-sizing: border-box;
	background: #f6faff;
}

/* 状态栏样式 */
.status-bar {
	background-color: #ffffff;
	width: 100%;
	position: fixed;
	top: 0;
	left: 0;
	z-index: 100;
}

/* 积分余额卡片 */
.points-balance-card {
	max-width: 320px;
	margin: 18px auto 0 auto;
	background: linear-gradient(135deg, #3a86ff, #1a56cc);
	border-radius: 24rpx;
	padding: 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	color: #fff;
	box-shadow: 0 10rpx 30rpx rgba(58, 134, 255, 0.2);
}

.balance-container {
	display: flex;
	flex-direction: column;
}

.balance-label {
	font-size: 26rpx;
	opacity: 0.9;
	margin-bottom: 8rpx;
}

.balance-value {
	font-size: 48rpx;
	font-weight: 700;
	font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
}

.balance-actions-vertical {
	display: flex;
	flex-direction: column;
	gap: 10px;
	align-items: flex-end;
}

.balance-detail {
	width: 92px;
	padding: 6px 0;
	background: linear-gradient(90deg, #1677FF 0%, #007aff 100%);
	color: #fff;
	border-radius: 18px;
	font-size: 14px;
	font-weight: 500;
	text-align: center;
	line-height: 22px;
	cursor: pointer;
	box-shadow: 0 2px 8px rgba(22,119,255,0.08);
}

.history-btn {
	width: 92px;
	padding: 6px 0;
	border: 1.5px solid #1677FF;
	color: #1677FF;
	border-radius: 18px;
	font-size: 14px;
	font-weight: 500;
	background: #fff;
	text-align: center;
	line-height: 22px;
	cursor: pointer;
	transition: background 0.2s, color 0.2s;
}

.history-btn:active {
	background: #1677FF;
	color: #fff;
}

/* 分类标签 */
.category-tabs {
	background-color: #fff;
	padding: 20rpx 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.03);
}

.tabs-scroll {
	white-space: nowrap;
	width: 100%;
}

.tab-item {
	display: inline-block;
	padding: 14rpx 30rpx;
	font-size: 28rpx;
	color: #666;
	margin-right: 20rpx;
	border-radius: 40rpx;
	transition: all 0.3s;
	background-color: #f5f5f5;
}

.tab-item.active {
	background: linear-gradient(135deg, #3a86ff, #1a56cc);
	color: #fff;
	font-weight: 500;
	box-shadow: 0 5rpx 15rpx rgba(58, 134, 255, 0.2);
}

/* 商品列表 */
.products-container {
	padding: 0;
}

.product-grid {
	display: grid;
	grid-template-columns: repeat(2, 160px);
	justify-content: center;
	gap: 16px;
	padding-bottom: 30rpx;
}

.product-item {
	width: 160px;
	margin: 0;
	background-color: #fff;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.05);
	position: relative;
}

.product-image {
	width: 100%;
	height: 240rpx;
	object-fit: cover;
}

.product-info {
	padding: 20rpx;
}

.product-name {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 10rpx;
}

.product-description {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 24rpx;
	height: 68rpx;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
	text-overflow: ellipsis;
}

.product-bottom {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.product-points {
	font-size: 26rpx;
	color: #ff6b6b;
	font-weight: 600;
}

.exchange-btn {
	background-color: rgba(58, 134, 255, 0.1);
	color: #3a86ff;
	padding: 6rpx 16rpx;
	border-radius: 30rpx;
	font-size: 22rpx;
	font-weight: 500;
}

.exchange-btn.disabled {
	background-color: rgba(0, 0, 0, 0.05);
	color: #999;
}

.product-tag {
	position: absolute;
	top: 16rpx;
	right: 16rpx;
	background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
	color: #fff;
	padding: 6rpx 16rpx;
	border-radius: 20rpx 0 20rpx 0;
	font-size: 22rpx;
	font-weight: 500;
	box-shadow: 0 4rpx 8rpx rgba(255, 107, 107, 0.2);
}

/* 空状态 */
.empty-state {
	padding: 100rpx 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.empty-icon {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 30rpx;
	opacity: 0.6;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

/* 商品详情弹窗 */
.product-detail-popup {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 999;
}

.popup-content {
	width: 85%;
	max-width: 650rpx;
	background-color: #fff;
	border-radius: 24rpx;
	overflow: hidden;
	position: relative;
	max-height: 85vh;
	display: flex;
	flex-direction: column;
}

.popup-close {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background-color: rgba(0, 0, 0, 0.3);
	color: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 40rpx;
	z-index: 10;
}

.popup-image {
	width: 100%;
	height: 350rpx;
	object-fit: cover;
}

.popup-info {
	padding: 30rpx;
}

.popup-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 10rpx;
}

.popup-description {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 20rpx;
	line-height: 1.6;
}

.popup-points {
	font-size: 36rpx;
	color: #ff6b6b;
	font-weight: 600;
	margin-bottom: 24rpx;
}

.popup-rules {
	background-color: #f9f9f9;
	padding: 20rpx;
	border-radius: 16rpx;
	margin-bottom: 24rpx;
}

.rules-title {
	font-size: 26rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 10rpx;
}

.rules-content {
	font-size: 24rpx;
	color: #666;
	line-height: 1.6;
}

.popup-stock, .popup-validity {
	display: flex;
	font-size: 26rpx;
	margin-bottom: 16rpx;
}

.stock-label, .validity-label {
	color: #999;
	margin-right: 10rpx;
}

.stock-value, .validity-value {
	color: #333;
}

.popup-btn {
	width: 100%;
	height: 80rpx;
	background: linear-gradient(135deg, #3a86ff, #1a56cc);
	color: #fff;
	border-radius: 40rpx;
	font-size: 28rpx;
	font-weight: 500;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 30rpx;
	box-shadow: 0 10rpx 20rpx rgba(58, 134, 255, 0.2);
}

.popup-btn.disabled {
	background: #ccc;
	box-shadow: none;
}

.mall-history-row {
	display: flex;
	justify-content: flex-end;
	padding: 0 30rpx 10px 30rpx;
	background: transparent;
}
</style> 