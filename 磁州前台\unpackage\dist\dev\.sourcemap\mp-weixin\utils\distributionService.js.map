{"version": 3, "file": "distributionService.js", "sources": ["utils/distributionService.js"], "sourcesContent": ["/**\n * 分销系统服务\n * 提供分销相关的API和功能\n */\n\nimport request from '@/utils/request';\nimport { formatNumber } from './format';\nimport { getToken } from '@/utils/auth';\nimport store from '@/store';\n\n/**\n * 分销系统服务类\n */\nclass DistributionService {\n  /**\n   * 检查用户是否是分销员\n   * @param {Object} options 选项\n   * @param {String} options.userId 用户ID\n   * @returns {Promise<Boolean>} 是否是分销员\n   */\n  async isDistributor(options = {}) {\n    try {\n      // 这里应该调用后端API\n      // 暂时使用模拟数据\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          // 模拟数据：用户ID为1001、1002、1003的是分销员\n          const isDistributor = ['1001', '1002', '1003'].includes(options.userId);\n          resolve(isDistributor);\n        }, 300);\n      });\n    } catch (error) {\n      console.error('检查分销员状态失败', error);\n      return false;\n    }\n  }\n\n  /**\n   * 获取分销员信息\n   * @param {Object} options 选项\n   * @param {String} options.userId 用户ID\n   * @returns {Promise<Object>} 分销员信息\n   */\n  async getDistributorInfo(options = {}) {\n    try {\n      // 这里应该调用后端API\n      // 暂时使用模拟数据\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          // 模拟数据\n          resolve({\n            id: options.userId,\n            level: '黄金分销员',\n            levelValue: 2,\n            totalCommission: 1256.8,\n            availableCommission: 458.2,\n            pendingCommission: 320.5,\n            withdrawnCommission: 478.1,\n            totalOrders: 32,\n            teamCount: 5,\n            createdAt: '2023-05-15',\n            inviteCode: 'DIS' + options.userId,\n            status: 'active'\n          });\n        }, 300);\n      });\n    } catch (error) {\n      console.error('获取分销员信息失败', error);\n      return null;\n    }\n  }\n\n  /**\n   * 获取分销中心数据\n   */\n  async getDistributionCenter() {\n    return await request({\n      url: '/distribution/center',\n      method: 'GET',\n    });\n  }\n\n  /**\n   * 申请成为分销员\n   * @param {Object} data 申请数据\n   */\n  async applyDistributor(data) {\n    return await request({\n      url: '/distribution/apply',\n      method: 'POST',\n      data\n    });\n  }\n\n  /**\n   * 获取分销员申请状态\n   */\n  async getApplyStatus() {\n    return await request({\n      url: '/distribution/apply/status',\n      method: 'GET',\n    });\n  }\n\n  /**\n   * 获取佣金明细列表\n   * @param {Object} params 查询参数\n   */\n  async getCommissionList(params) {\n    return await request({\n      url: '/distribution/commission/list',\n      method: 'GET',\n      params\n    });\n  }\n\n  /**\n   * 获取佣金统计数据\n   */\n  async getCommissionStats() {\n    return await request({\n      url: '/distribution/commission/stats',\n      method: 'GET',\n    });\n  }\n\n  /**\n   * 获取推广工具列表\n   */\n  async getPromotionTools() {\n    return await request({\n      url: '/distribution/promotion/tools',\n      method: 'GET',\n    });\n  }\n\n  /**\n   * 获取分销团队数据\n   * @param {Object} params 查询参数\n   */\n  async getTeamMembers(params) {\n    return await request({\n      url: '/distribution/team/members',\n      method: 'GET',\n      params\n    });\n  }\n\n  /**\n   * 获取团队统计数据\n   */\n  async getTeamStats() {\n    return await request({\n      url: '/distribution/team/stats',\n      method: 'GET',\n    });\n  }\n\n  /**\n   * 获取可分销商品列表\n   * @param {Object} params 查询参数\n   */\n  async getDistributableProducts(params) {\n    return await request({\n      url: '/distribution/products',\n      method: 'GET',\n      params\n    });\n  }\n\n  /**\n   * 提交佣金提现申请\n   * @param {Object} data 提现数据\n   */\n  async withdrawCommission(data) {\n    return await request({\n      url: '/distribution/withdraw',\n      method: 'POST',\n      data\n    });\n  }\n\n  /**\n   * 获取提现记录列表\n   * @param {Object} params 查询参数\n   */\n  async getWithdrawRecords(params) {\n    return await request({\n      url: '/distribution/withdraw/records',\n      method: 'GET',\n      params\n    });\n  }\n\n  /**\n   * 生成商品推广码\n   * @param {Object} data 商品数据\n   */\n  async generateProductCode(data) {\n    return await request({\n      url: '/distribution/promotion/code',\n      method: 'POST',\n      data\n    });\n  }\n\n  /**\n   * 获取分销配置\n   */\n  async getDistributionConfig() {\n    return await request({\n      url: '/distribution/config',\n      method: 'GET',\n    });\n  }\n\n  /**\n   * 获取分销海报\n   * @param {Object} params 海报参数\n   */\n  async getDistributionPoster(params) {\n    return await request({\n      url: '/distribution/promotion/poster',\n      method: 'GET',\n      params\n    });\n  }\n  \n  /**\n   * 获取带有分销参数的商品链接\n   * @param {Object} data 商品数据\n   */\n  async getProductShareLink(data) {\n    return await request({\n      url: '/distribution/promotion/link',\n      method: 'POST',\n      data\n    });\n  }\n  \n  /**\n   * 获取商家列表（支持分销）\n   * @param {Object} params 查询参数\n   */\n  async getMerchantsWithDistribution(params) {\n    return await request({\n      url: '/distribution/merchants',\n      method: 'GET',\n      params\n    });\n  }\n  \n  /**\n   * 申请成为商家专属分销员\n   * @param {Object} data 申请数据\n   */\n  async applyMerchantDistributor(data) {\n    return await request({\n      url: '/distribution/merchant/apply',\n      method: 'POST',\n      data\n    });\n  }\n  \n  /**\n   * 获取分销等级设置\n   */\n  async getDistributionLevels() {\n    return await request({\n      url: '/distribution/levels',\n      method: 'GET',\n    });\n  }\n  \n  /**\n   * 保存分销等级设置\n   * @param {Object} data 等级设置数据\n   */\n  async saveDistributionLevels(data) {\n    return await request({\n      url: '/distribution/levels',\n      method: 'POST',\n      data\n    });\n  }\n  \n  /**\n   * 获取佣金规则设置\n   */\n  async getCommissionRules() {\n    return await request({\n      url: '/distribution/commission/rules',\n      method: 'GET',\n    });\n  }\n  \n  /**\n   * 保存佣金规则设置\n   * @param {Object} data 佣金规则数据\n   */\n  async saveCommissionRules(data) {\n    return await request({\n      url: '/distribution/commission/rules',\n      method: 'POST',\n      data\n    });\n  }\n  \n  /**\n   * 获取分销员列表（管理端）\n   * @param {Object} params 查询参数\n   */\n  async getDistributorsList(params) {\n    return await request({\n      url: '/distribution/admin/distributors',\n      method: 'GET',\n      params\n    });\n  }\n  \n  /**\n   * 审核分销员申请（管理端）\n   * @param {Object} data 审核数据\n   */\n  async reviewDistributorApplication(data) {\n    return await request({\n      url: '/distribution/admin/review',\n      method: 'POST',\n      data\n    });\n  }\n  \n  /**\n   * 设置分销员等级（管理端）\n   * @param {Object} data 等级数据\n   */\n  async setDistributorLevel(data) {\n    return await request({\n      url: '/distribution/admin/set-level',\n      method: 'POST',\n      data\n    });\n  }\n  \n  /**\n   * 禁用/启用分销员（管理端）\n   * @param {Object} data 状态数据\n   */\n  async toggleDistributorStatus(data) {\n    return await request({\n      url: '/distribution/admin/toggle-status',\n      method: 'POST',\n      data\n    });\n  }\n  \n  /**\n   * 获取分销订单列表（管理端）\n   * @param {Object} params 查询参数\n   */\n  async getDistributionOrders(params) {\n    return await request({\n      url: '/distribution/admin/orders',\n      method: 'GET',\n      params\n    });\n  }\n  \n  /**\n   * 手动调整佣金（管理端）\n   * @param {Object} data 调整数据\n   */\n  async adjustCommission(data) {\n    return await request({\n      url: '/distribution/admin/adjust-commission',\n      method: 'POST',\n      data\n    });\n  }\n  \n  /**\n   * 审核提现申请（管理端）\n   * @param {Object} data 审核数据\n   */\n  async reviewWithdrawal(data) {\n    return await request({\n      url: '/distribution/admin/review-withdrawal',\n      method: 'POST',\n      data\n    });\n  }\n  \n  /**\n   * 获取分销统计数据（管理端）\n   * @param {Object} params 查询参数\n   */\n  async getDistributionStats(params) {\n    return await request({\n      url: '/distribution/admin/stats',\n      method: 'GET',\n      params\n    });\n  }\n  \n  /**\n   * 获取分销商品设置列表（管理端）\n   * @param {Object} params 查询参数\n   */\n  async getDistributionProductSettings(params) {\n    return await request({\n      url: '/distribution/admin/product-settings',\n      method: 'GET',\n      params\n    });\n  }\n  \n  /**\n   * 设置商品分销参数（管理端）\n   * @param {Object} data 商品分销数据\n   */\n  async setProductDistribution(data) {\n    return await request({\n      url: '/distribution/admin/set-product',\n      method: 'POST',\n      data\n    });\n  }\n  \n  /**\n   * 批量设置商品分销参数（管理端）\n   * @param {Object} data 批量设置数据\n   */\n  async batchSetProductDistribution(data) {\n    return await request({\n      url: '/distribution/admin/batch-set-products',\n      method: 'POST',\n      data\n    });\n  }\n\n  /**\n   * 获取分销条件\n   * @returns {Promise<Object>} 分销条件\n   */\n  async getDistributionConditions() {\n    try {\n      // 这里应该调用后端API\n      // 暂时使用模拟数据\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          resolve({\n            requirePurchase: true,\n            minimumPurchase: 100,\n            requireApproval: true,\n            autoApprove: false,\n            needRealName: true,\n            needMobile: true,\n            description: '成为分销员需要购买任意商品并提交申请，审核通过后即可成为分销员。'\n          });\n        }, 300);\n      });\n    } catch (error) {\n      console.error('获取分销条件失败', error);\n      return null;\n    }\n  }\n\n  /**\n   * 获取佣金明细\n   * @param {Object} options 选项\n   * @param {Number} options.page 页码\n   * @param {Number} options.pageSize 每页数量\n   * @param {String} options.status 状态筛选\n   * @returns {Promise<Object>} 佣金明细和分页信息\n   */\n  async getCommissionRecords(options = {}) {\n    try {\n      const { page = 1, pageSize = 10, status = 'all' } = options;\n      \n      // 这里应该调用后端API\n      // 暂时使用模拟数据\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          // 模拟数据\n          const statuses = ['pending', 'settled', 'withdrawn'];\n          const records = Array(30).fill().map((_, index) => {\n            const amount = Math.floor(Math.random() * 100) + 10;\n            const recordStatus = statuses[Math.floor(Math.random() * statuses.length)];\n            \n            return {\n              id: 'C' + (10000 + index),\n              orderId: 'O' + (20000 + index),\n              productName: `商品${index % 10 + 1}`,\n              amount,\n              status: recordStatus,\n              createdAt: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString(),\n              settledAt: recordStatus === 'pending' ? null : new Date(Date.now() - Math.floor(Math.random() * 20) * 24 * 60 * 60 * 1000).toISOString(),\n              withdrawnAt: recordStatus === 'withdrawn' ? new Date(Date.now() - Math.floor(Math.random() * 10) * 24 * 60 * 60 * 1000).toISOString() : null,\n              level: Math.floor(Math.random() * 3) + 1\n            };\n          });\n          \n          // 状态筛选\n          let filteredRecords = records;\n          if (status !== 'all') {\n            filteredRecords = records.filter(record => record.status === status);\n          }\n          \n          // 分页\n          const start = (page - 1) * pageSize;\n          const end = start + pageSize;\n          const paginatedRecords = filteredRecords.slice(start, end);\n          \n          resolve({\n            list: paginatedRecords,\n            pagination: {\n              total: filteredRecords.length,\n              page,\n              pageSize,\n              totalPages: Math.ceil(filteredRecords.length / pageSize)\n            },\n            summary: {\n              total: records.reduce((sum, record) => sum + record.amount, 0),\n              pending: records.filter(record => record.status === 'pending').reduce((sum, record) => sum + record.amount, 0),\n              settled: records.filter(record => record.status === 'settled').reduce((sum, record) => sum + record.amount, 0),\n              withdrawn: records.filter(record => record.status === 'withdrawn').reduce((sum, record) => sum + record.amount, 0)\n            }\n          });\n        }, 500);\n      });\n    } catch (error) {\n      console.error('获取佣金明细失败', error);\n      return {\n        list: [],\n        pagination: {\n          total: 0,\n          page: options.page || 1,\n          pageSize: options.pageSize || 10,\n          totalPages: 0\n        },\n        summary: {\n          total: 0,\n          pending: 0,\n          settled: 0,\n          withdrawn: 0\n        }\n      };\n    }\n  }\n\n  /**\n   * 获取团队成员\n   * @param {Object} options 选项\n   * @param {Number} options.page 页码\n   * @param {Number} options.pageSize 每页数量\n   * @param {Number} options.level 团队层级\n   * @returns {Promise<Object>} 团队成员和分页信息\n   */\n  async getTeamMembers(options = {}) {\n    try {\n      const { page = 1, pageSize = 10, level = 1 } = options;\n      \n      // 这里应该调用后端API\n      // 暂时使用模拟数据\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          // 模拟数据\n          const members = Array(level === 1 ? 15 : 8).fill().map((_, index) => ({\n            id: 'U' + (10000 + index),\n            nickname: `用户${index + 1}`,\n            avatar: `/static/images/avatars/avatar-${(index % 8) + 1}.jpg`,\n            level: Math.floor(Math.random() * 3) + 1,\n            levelName: ['普通分销员', '黄金分销员', '钻石分销员'][Math.floor(Math.random() * 3)],\n            joinTime: new Date(Date.now() - Math.floor(Math.random() * 90) * 24 * 60 * 60 * 1000).toISOString(),\n            contribution: Math.floor(Math.random() * 2000) + 100,\n            orderCount: Math.floor(Math.random() * 20) + 1\n          }));\n          \n          // 分页\n          const start = (page - 1) * pageSize;\n          const end = start + pageSize;\n          const paginatedMembers = members.slice(start, end);\n          \n          resolve({\n            list: paginatedMembers,\n            pagination: {\n              total: members.length,\n              page,\n              pageSize,\n              totalPages: Math.ceil(members.length / pageSize)\n            },\n            summary: {\n              level1Count: 15,\n              level2Count: 8,\n              totalContribution: members.reduce((sum, member) => sum + member.contribution, 0)\n            }\n          });\n        }, 500);\n      });\n    } catch (error) {\n      console.error('获取团队成员失败', error);\n      return {\n        list: [],\n        pagination: {\n          total: 0,\n          page: options.page || 1,\n          pageSize: options.pageSize || 10,\n          totalPages: 0\n        },\n        summary: {\n          level1Count: 0,\n          level2Count: 0,\n          totalContribution: 0\n        }\n      };\n    }\n  }\n\n  /**\n   * 获取提现设置\n   * @returns {Promise<Object>} 提现设置\n   */\n  async getWithdrawalSettings() {\n    try {\n      // 这里应该调用后端API\n      // 暂时使用模拟数据\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          resolve({\n            minAmount: 50,\n            maxAmount: 5000,\n            feeRate: 0.01,\n            withdrawalCycle: 'weekly',\n            withdrawalDays: [1, 4], // 周一和周四可提现\n            autoApprove: false,\n            needRealName: true,\n            withdrawalMethods: ['wechat', 'alipay', 'bank']\n          });\n        }, 300);\n      });\n    } catch (error) {\n      console.error('获取提现设置失败', error);\n      return null;\n    }\n  }\n\n  /**\n   * 申请提现\n   * @param {Object} data 提现数据\n   * @returns {Promise<Object>} 提现结果\n   */\n  async applyWithdrawal(data) {\n    try {\n      // 这里应该调用后端API\n      // 暂时使用模拟数据\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          if (data.amount < 50) {\n            resolve({\n              success: false,\n              message: '提现金额不能小于50元'\n            });\n            return;\n          }\n          \n          resolve({\n            success: true,\n            message: '提现申请已提交，等待审核',\n            data: {\n              withdrawalId: 'W' + Date.now(),\n              amount: data.amount,\n              fee: data.amount * 0.01,\n              actualAmount: data.amount * 0.99,\n              status: 'pending',\n              createdAt: new Date().toISOString()\n            }\n          });\n        }, 500);\n      });\n    } catch (error) {\n      console.error('申请提现失败', error);\n      return {\n        success: false,\n        message: '提现申请失败，请稍后再试',\n        error\n      };\n    }\n  }\n\n  /**\n   * 获取分销协议\n   * @returns {Promise<String>} 分销协议内容\n   */\n  async getDistributionAgreement() {\n    try {\n      // 这里应该调用后端API\n      // 暂时使用模拟数据\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          resolve(`# 磁州生活网分销员协议\n\n## 一、总则\n\n1.1 本协议是磁州生活网平台与分销员之间关于分销合作的约定。\n1.2 成为分销员即表示您已阅读并同意本协议的全部条款。\n\n## 二、分销员资格\n\n2.1 分销员需年满18周岁，具有完全民事行为能力。\n2.2 分销员需遵守国家法律法规和平台规则。\n2.3 分销员资格需经平台审核通过。\n\n## 三、分销规则\n\n3.1 分销员可获得其推广成交订单的佣金。\n3.2 佣金比例根据分销员等级和商品设置而定。\n3.3 佣金将在订单完成并结算后计入分销员账户。\n\n## 四、提现规则\n\n4.1 分销员可申请将账户佣金提现至指定账户。\n4.2 提现金额不低于平台设定的最低提现额度。\n4.3 提现需完成实名认证和绑定提现账户。\n\n## 五、禁止行为\n\n5.1 禁止虚假交易、刷单等作弊行为。\n5.2 禁止使用不当手段获取分销资格或佣金。\n5.3 禁止发布违法违规信息进行推广。\n\n## 六、违约责任\n\n6.1 若分销员违反本协议，平台有权取消其分销资格。\n6.2 因违规行为获得的佣金，平台有权追回。\n6.3 造成平台损失的，分销员需承担赔偿责任。\n\n## 七、协议修改\n\n7.1 平台有权根据需要修改本协议内容。\n7.2 协议修改后，平台将通过适当方式通知分销员。\n7.3 修改后的协议自公布之日起生效。\n\n## 八、协议终止\n\n8.1 分销员可主动申请终止分销合作。\n8.2 平台有权根据规则终止与分销员的合作。\n8.3 协议终止后，未结算的佣金将按规则处理。\n\n## 九、其他条款\n\n9.1 本协议解释权归磁州生活网平台所有。\n9.2 本协议自分销员资格审核通过之日起生效。`);\n        }, 300);\n      });\n    } catch (error) {\n      console.error('获取分销协议失败', error);\n      return '';\n    }\n  }\n\n  /**\n   * 生成推广链接\n   * @param {Object} options 选项\n   * @param {String} options.type 推广类型\n   * @param {String} options.id 推广对象ID\n   * @param {String} options.distributorId 分销员ID\n   * @returns {Promise<Object>} 推广链接信息\n   */\n  async generatePromotionLink(options) {\n    try {\n      const { type, id, distributorId } = options;\n      \n      // 这里应该调用后端API\n      // 暂时使用模拟数据\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const baseUrl = 'https://cizhoulife.com';\n          let path = '';\n          \n          switch (type) {\n            case 'product':\n              path = `/pages/product/detail?id=${id}&distributor=${distributorId}`;\n              break;\n            case 'merchant':\n              path = `/pages/merchant/detail?id=${id}&distributor=${distributorId}`;\n              break;\n            case 'activity':\n              path = `/pages/activity/detail?id=${id}&distributor=${distributorId}`;\n              break;\n            default:\n              path = `/?distributor=${distributorId}`;\n          }\n          \n          resolve({\n            url: baseUrl + path,\n            shortUrl: `https://czl.cn/${type.charAt(0)}${id.substring(0, 4)}`,\n            qrCodeUrl: `/static/images/qrcode-placeholder.png`,\n            distributorId,\n            type,\n            id\n          });\n        }, 300);\n      });\n    } catch (error) {\n      console.error('生成推广链接失败', error);\n      return null;\n    }\n  }\n  \n  /**\n   * 格式化佣金金额\n   * @param {Number} amount 金额\n   * @returns {String} 格式化后的金额\n   */\n  formatCommission(amount) {\n    return formatNumber(amount, 2);\n  }\n}\n\n// 创建单例\nconst distributionService = new DistributionService();\n\nexport default distributionService; "], "names": ["uni", "request", "formatNumber"], "mappings": ";;;;;AAaA,MAAM,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOxB,MAAM,cAAc,UAAU,IAAI;AAChC,QAAI;AAGF,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,mBAAW,MAAM;AAEf,gBAAM,gBAAgB,CAAC,QAAQ,QAAQ,MAAM,EAAE,SAAS,QAAQ,MAAM;AACtE,kBAAQ,aAAa;AAAA,QACtB,GAAE,GAAG;AAAA,MACd,CAAO;AAAA,IACF,SAAQ,OAAO;AACdA,+EAAc,aAAa,KAAK;AAChC,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,MAAM,mBAAmB,UAAU,IAAI;AACrC,QAAI;AAGF,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,mBAAW,MAAM;AAEf,kBAAQ;AAAA,YACN,IAAI,QAAQ;AAAA,YACZ,OAAO;AAAA,YACP,YAAY;AAAA,YACZ,iBAAiB;AAAA,YACjB,qBAAqB;AAAA,YACrB,mBAAmB;AAAA,YACnB,qBAAqB;AAAA,YACrB,aAAa;AAAA,YACb,WAAW;AAAA,YACX,WAAW;AAAA,YACX,YAAY,QAAQ,QAAQ;AAAA,YAC5B,QAAQ;AAAA,UACpB,CAAW;AAAA,QACF,GAAE,GAAG;AAAA,MACd,CAAO;AAAA,IACF,SAAQ,OAAO;AACdA,+EAAc,aAAa,KAAK;AAChC,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,wBAAwB;AAC5B,WAAO,MAAMC,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,iBAAiB,MAAM;AAC3B,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,iBAAiB;AACrB,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,kBAAkB,QAAQ;AAC9B,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,qBAAqB;AACzB,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,oBAAoB;AACxB,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,eAAe,QAAQ;AAC3B,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,eAAe;AACnB,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,yBAAyB,QAAQ;AACrC,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,mBAAmB,MAAM;AAC7B,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,mBAAmB,QAAQ;AAC/B,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,oBAAoB,MAAM;AAC9B,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,wBAAwB;AAC5B,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,sBAAsB,QAAQ;AAClC,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,oBAAoB,MAAM;AAC9B,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,6BAA6B,QAAQ;AACzC,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,yBAAyB,MAAM;AACnC,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,wBAAwB;AAC5B,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,uBAAuB,MAAM;AACjC,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,qBAAqB;AACzB,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,oBAAoB,MAAM;AAC9B,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,oBAAoB,QAAQ;AAChC,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,6BAA6B,MAAM;AACvC,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,oBAAoB,MAAM;AAC9B,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,wBAAwB,MAAM;AAClC,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,sBAAsB,QAAQ;AAClC,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,iBAAiB,MAAM;AAC3B,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,iBAAiB,MAAM;AAC3B,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,qBAAqB,QAAQ;AACjC,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,+BAA+B,QAAQ;AAC3C,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,uBAAuB,MAAM;AACjC,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,4BAA4B,MAAM;AACtC,WAAO,MAAMA,cAAAA,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,4BAA4B;AAChC,QAAI;AAGF,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,mBAAW,MAAM;AACf,kBAAQ;AAAA,YACN,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,aAAa;AAAA,YACb,cAAc;AAAA,YACd,YAAY;AAAA,YACZ,aAAa;AAAA,UACzB,CAAW;AAAA,QACF,GAAE,GAAG;AAAA,MACd,CAAO;AAAA,IACF,SAAQ,OAAO;AACdD,gFAAc,YAAY,KAAK;AAC/B,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUD,MAAM,qBAAqB,UAAU,IAAI;AACvC,QAAI;AACF,YAAM,EAAE,OAAO,GAAG,WAAW,IAAI,SAAS,MAAO,IAAG;AAIpD,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,mBAAW,MAAM;AAEf,gBAAM,WAAW,CAAC,WAAW,WAAW,WAAW;AACnD,gBAAM,UAAU,MAAM,EAAE,EAAE,KAAI,EAAG,IAAI,CAAC,GAAG,UAAU;AACjD,kBAAM,SAAS,KAAK,MAAM,KAAK,WAAW,GAAG,IAAI;AACjD,kBAAM,eAAe,SAAS,KAAK,MAAM,KAAK,WAAW,SAAS,MAAM,CAAC;AAEzE,mBAAO;AAAA,cACL,IAAI,OAAO,MAAQ;AAAA,cACnB,SAAS,OAAO,MAAQ;AAAA,cACxB,aAAa,KAAK,QAAQ,KAAK,CAAC;AAAA,cAChC;AAAA,cACA,QAAQ;AAAA,cACR,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,cACpG,WAAW,iBAAiB,YAAY,OAAO,IAAI,KAAK,KAAK,IAAK,IAAG,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,cACxI,aAAa,iBAAiB,cAAc,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,MAAM,KAAK,WAAW,EAAE,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAW,IAAK;AAAA,cACxI,OAAO,KAAK,MAAM,KAAK,OAAQ,IAAG,CAAC,IAAI;AAAA,YACrD;AAAA,UACA,CAAW;AAGD,cAAI,kBAAkB;AACtB,cAAI,WAAW,OAAO;AACpB,8BAAkB,QAAQ,OAAO,YAAU,OAAO,WAAW,MAAM;AAAA,UACpE;AAGD,gBAAM,SAAS,OAAO,KAAK;AAC3B,gBAAM,MAAM,QAAQ;AACpB,gBAAM,mBAAmB,gBAAgB,MAAM,OAAO,GAAG;AAEzD,kBAAQ;AAAA,YACN,MAAM;AAAA,YACN,YAAY;AAAA,cACV,OAAO,gBAAgB;AAAA,cACvB;AAAA,cACA;AAAA,cACA,YAAY,KAAK,KAAK,gBAAgB,SAAS,QAAQ;AAAA,YACxD;AAAA,YACD,SAAS;AAAA,cACP,OAAO,QAAQ,OAAO,CAAC,KAAK,WAAW,MAAM,OAAO,QAAQ,CAAC;AAAA,cAC7D,SAAS,QAAQ,OAAO,YAAU,OAAO,WAAW,SAAS,EAAE,OAAO,CAAC,KAAK,WAAW,MAAM,OAAO,QAAQ,CAAC;AAAA,cAC7G,SAAS,QAAQ,OAAO,YAAU,OAAO,WAAW,SAAS,EAAE,OAAO,CAAC,KAAK,WAAW,MAAM,OAAO,QAAQ,CAAC;AAAA,cAC7G,WAAW,QAAQ,OAAO,YAAU,OAAO,WAAW,WAAW,EAAE,OAAO,CAAC,KAAK,WAAW,MAAM,OAAO,QAAQ,CAAC;AAAA,YAClH;AAAA,UACb,CAAW;AAAA,QACF,GAAE,GAAG;AAAA,MACd,CAAO;AAAA,IACF,SAAQ,OAAO;AACdA,gFAAc,YAAY,KAAK;AAC/B,aAAO;AAAA,QACL,MAAM,CAAE;AAAA,QACR,YAAY;AAAA,UACV,OAAO;AAAA,UACP,MAAM,QAAQ,QAAQ;AAAA,UACtB,UAAU,QAAQ,YAAY;AAAA,UAC9B,YAAY;AAAA,QACb;AAAA,QACD,SAAS;AAAA,UACP,OAAO;AAAA,UACP,SAAS;AAAA,UACT,SAAS;AAAA,UACT,WAAW;AAAA,QACZ;AAAA,MACT;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUD,MAAM,eAAe,UAAU,IAAI;AACjC,QAAI;AACF,YAAM,EAAE,OAAO,GAAG,WAAW,IAAI,QAAQ,EAAG,IAAG;AAI/C,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,mBAAW,MAAM;AAEf,gBAAM,UAAU,MAAM,UAAU,IAAI,KAAK,CAAC,EAAE,KAAI,EAAG,IAAI,CAAC,GAAG,WAAW;AAAA,YACpE,IAAI,OAAO,MAAQ;AAAA,YACnB,UAAU,KAAK,QAAQ,CAAC;AAAA,YACxB,QAAQ,iCAAkC,QAAQ,IAAK,CAAC;AAAA,YACxD,OAAO,KAAK,MAAM,KAAK,OAAQ,IAAG,CAAC,IAAI;AAAA,YACvC,WAAW,CAAC,SAAS,SAAS,OAAO,EAAE,KAAK,MAAM,KAAK,OAAQ,IAAG,CAAC,CAAC;AAAA,YACpE,UAAU,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,YACnG,cAAc,KAAK,MAAM,KAAK,OAAQ,IAAG,GAAI,IAAI;AAAA,YACjD,YAAY,KAAK,MAAM,KAAK,OAAQ,IAAG,EAAE,IAAI;AAAA,UAC9C,EAAC;AAGF,gBAAM,SAAS,OAAO,KAAK;AAC3B,gBAAM,MAAM,QAAQ;AACpB,gBAAM,mBAAmB,QAAQ,MAAM,OAAO,GAAG;AAEjD,kBAAQ;AAAA,YACN,MAAM;AAAA,YACN,YAAY;AAAA,cACV,OAAO,QAAQ;AAAA,cACf;AAAA,cACA;AAAA,cACA,YAAY,KAAK,KAAK,QAAQ,SAAS,QAAQ;AAAA,YAChD;AAAA,YACD,SAAS;AAAA,cACP,aAAa;AAAA,cACb,aAAa;AAAA,cACb,mBAAmB,QAAQ,OAAO,CAAC,KAAK,WAAW,MAAM,OAAO,cAAc,CAAC;AAAA,YAChF;AAAA,UACb,CAAW;AAAA,QACF,GAAE,GAAG;AAAA,MACd,CAAO;AAAA,IACF,SAAQ,OAAO;AACdA,gFAAc,YAAY,KAAK;AAC/B,aAAO;AAAA,QACL,MAAM,CAAE;AAAA,QACR,YAAY;AAAA,UACV,OAAO;AAAA,UACP,MAAM,QAAQ,QAAQ;AAAA,UACtB,UAAU,QAAQ,YAAY;AAAA,UAC9B,YAAY;AAAA,QACb;AAAA,QACD,SAAS;AAAA,UACP,aAAa;AAAA,UACb,aAAa;AAAA,UACb,mBAAmB;AAAA,QACpB;AAAA,MACT;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,wBAAwB;AAC5B,QAAI;AAGF,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,mBAAW,MAAM;AACf,kBAAQ;AAAA,YACN,WAAW;AAAA,YACX,WAAW;AAAA,YACX,SAAS;AAAA,YACT,iBAAiB;AAAA,YACjB,gBAAgB,CAAC,GAAG,CAAC;AAAA;AAAA,YACrB,aAAa;AAAA,YACb,cAAc;AAAA,YACd,mBAAmB,CAAC,UAAU,UAAU,MAAM;AAAA,UAC1D,CAAW;AAAA,QACF,GAAE,GAAG;AAAA,MACd,CAAO;AAAA,IACF,SAAQ,OAAO;AACdA,gFAAc,YAAY,KAAK;AAC/B,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,gBAAgB,MAAM;AAC1B,QAAI;AAGF,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,mBAAW,MAAM;AACf,cAAI,KAAK,SAAS,IAAI;AACpB,oBAAQ;AAAA,cACN,SAAS;AAAA,cACT,SAAS;AAAA,YACvB,CAAa;AACD;AAAA,UACD;AAED,kBAAQ;AAAA,YACN,SAAS;AAAA,YACT,SAAS;AAAA,YACT,MAAM;AAAA,cACJ,cAAc,MAAM,KAAK,IAAK;AAAA,cAC9B,QAAQ,KAAK;AAAA,cACb,KAAK,KAAK,SAAS;AAAA,cACnB,cAAc,KAAK,SAAS;AAAA,cAC5B,QAAQ;AAAA,cACR,YAAW,oBAAI,KAAM,GAAC,YAAa;AAAA,YACpC;AAAA,UACb,CAAW;AAAA,QACF,GAAE,GAAG;AAAA,MACd,CAAO;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,uCAAA,UAAU,KAAK;AAC7B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS;AAAA,QACT;AAAA,MACR;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,2BAA2B;AAC/B,QAAI;AAGF,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,mBAAW,MAAM;AACf,kBAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBAoDM;AAAA,QACf,GAAE,GAAG;AAAA,MACd,CAAO;AAAA,IACF,SAAQ,OAAO;AACdA,gFAAc,YAAY,KAAK;AAC/B,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUD,MAAM,sBAAsB,SAAS;AACnC,QAAI;AACF,YAAM,EAAE,MAAM,IAAI,cAAa,IAAK;AAIpC,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,mBAAW,MAAM;AACf,gBAAM,UAAU;AAChB,cAAI,OAAO;AAEX,kBAAQ,MAAI;AAAA,YACV,KAAK;AACH,qBAAO,4BAA4B,EAAE,gBAAgB,aAAa;AAClE;AAAA,YACF,KAAK;AACH,qBAAO,6BAA6B,EAAE,gBAAgB,aAAa;AACnE;AAAA,YACF,KAAK;AACH,qBAAO,6BAA6B,EAAE,gBAAgB,aAAa;AACnE;AAAA,YACF;AACE,qBAAO,iBAAiB,aAAa;AAAA,UACxC;AAED,kBAAQ;AAAA,YACN,KAAK,UAAU;AAAA,YACf,UAAU,kBAAkB,KAAK,OAAO,CAAC,CAAC,GAAG,GAAG,UAAU,GAAG,CAAC,CAAC;AAAA,YAC/D,WAAW;AAAA,YACX;AAAA,YACA;AAAA,YACA;AAAA,UACZ,CAAW;AAAA,QACF,GAAE,GAAG;AAAA,MACd,CAAO;AAAA,IACF,SAAQ,OAAO;AACdA,gFAAc,YAAY,KAAK;AAC/B,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,iBAAiB,QAAQ;AACvB,WAAOE,aAAAA,aAAa,MAAS;AAAA,EAC9B;AACH;AAGK,MAAC,sBAAsB,IAAI,oBAAmB;;"}