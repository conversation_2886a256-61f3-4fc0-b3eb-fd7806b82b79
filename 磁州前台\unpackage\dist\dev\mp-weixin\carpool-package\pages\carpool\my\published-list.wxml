<view class="published-list-container"><view class="custom-header" style="{{'padding-top:' + e}}"><view class="header-content"><view class="left-action" bindtap="{{b}}"><image src="{{a}}" class="action-icon back-icon"></image></view><view class="title-area"><text class="page-title">我的发布</text></view><view class="right-action" bindtap="{{d}}"><image src="{{c}}" class="plus-icon"></image></view></view></view><scroll-view class="status-tabs" scroll-x="true" show-scrollbar="false" style="{{'top:' + g}}"><view class="tabs-container"><view wx:for="{{f}}" wx:for-item="tab" wx:key="e" class="{{['tab-item', tab.f && 'active']}}" bindtap="{{tab.g}}"><text class="tab-text">{{tab.a}}</text><text wx:if="{{tab.b}}" class="tab-count">({{tab.c}})</text><view wx:if="{{tab.d}}" class="tab-line"></view></view></view></scroll-view><scroll-view class="list-scroll" scroll-y refresher-enabled refresher-triggered="{{q}}" bindrefresherrefresh="{{r}}" bindscrolltolower="{{s}}" style="{{'margin-top:' + t + ';' + ('height:' + v)}}"><view wx:if="{{h}}" class="trip-list"><view wx:for="{{i}}" wx:for-item="item" wx:key="l" class="{{['trip-card', item.m && 'is-topped']}}" bindtap="{{item.n}}"><view class="trip-route"><view class="route-points"><view class="route-line-container"><view class="point-marker start"></view><view class="route-line"></view><view class="point-marker end"></view></view><view class="route-text"><view class="start-point"><text class="point-text">{{item.a}}</text></view><view class="end-point"><text class="point-text">{{item.b}}</text></view></view></view><view class="trip-meta"><view class="meta-left"><text class="time-text">{{item.c}}</text><text class="seat-text">{{item.d}}座</text><view class="{{['status-badge', item.f]}}">{{item.e}}</view><view wx:if="{{item.g}}" class="top-badge"><text>置顶</text></view></view><view class="meta-right"><text class="price-value">¥{{item.h}}</text></view></view></view><view class="action-bar"><button class="action-btn" catchtap="{{item.i}}">编辑</button><button class="action-btn" catchtap="{{item.j}}">推广</button><button class="action-btn" catchtap="{{item.k}}">转发</button></view></view></view><view wx:if="{{j}}" class="empty-state"><image src="{{k}}" class="empty-image"></image><text class="empty-text">暂无{{l}}的行程</text><button class="publish-btn" bindtap="{{n}}"><text>发布新行程</text><image src="{{m}}" class="arrow-icon"></image></button></view><view wx:if="{{o}}" class="loading-more"><view class="loading-spinner"></view><text>加载中...</text></view><view wx:if="{{p}}" class="no-more"><text>没有更多了</text></view></scroll-view></view><view wx:if="{{w}}" class="popup-mask" bindtap="{{E}}"><view class="action-sheet" catchtap="{{D}}"><view class="sheet-header"><text class="sheet-title">更多操作</text><view class="close-btn" bindtap="{{y}}"><image src="{{x}}" class="close-icon"></image></view></view><view class="sheet-content"><view class="option-item" bindtap="{{z}}"><view class="option-left"><view class="option-info"><text class="option-title">修改内容</text></view></view></view><view class="option-item" bindtap="{{A}}"><view class="option-left"><view class="option-info"><text class="option-title">删除内容</text></view></view></view><view class="option-item" bindtap="{{B}}"><view class="option-left"><view class="option-info"><text class="option-title">下架内容</text></view></view></view></view><view class="sheet-footer"><button class="cancel-btn" bindtap="{{C}}">取消</button></view></view></view><view wx:if="{{F}}" class="popup-mask" bindtap="{{T}}"><view class="action-sheet promotion-sheet" catchtap="{{S}}"><view class="sheet-header"><text class="sheet-title">推广选项</text><view class="close-btn" bindtap="{{H}}"><image src="{{G}}" class="close-icon"></image></view></view><view class="promotion-content"><view class="promotion-card" bindtap="{{L}}"><view class="promotion-icon"><svg wx:if="{{K}}" u-s="{{['d']}}" class="icon" u-i="6569bfe6-0" bind:__l="__l" u-p="{{K}}"><path wx:if="{{I}}" u-i="6569bfe6-1,6569bfe6-0" bind:__l="__l" u-p="{{I}}"></path><path wx:if="{{J}}" u-i="6569bfe6-2,6569bfe6-0" bind:__l="__l" u-p="{{J}}"></path></svg></view><view class="promotion-info"><view class="promotion-title">置顶信息</view><view class="promotion-desc">提升10倍曝光率，获得更多关注</view><view class="promotion-tag"><view class="tag-item hot">热门</view></view></view><view class="promotion-action"><view class="action-btn">立即置顶</view></view></view><view class="promotion-card" bindtap="{{Q}}"><view class="promotion-icon refresh-icon"><svg wx:if="{{P}}" u-s="{{['d']}}" class="icon" u-i="6569bfe6-3" bind:__l="__l" u-p="{{P}}"><path wx:if="{{M}}" u-i="6569bfe6-4,6569bfe6-3" bind:__l="__l" u-p="{{M}}"></path><path wx:if="{{N}}" u-i="6569bfe6-5,6569bfe6-3" bind:__l="__l" u-p="{{N}}"></path><path wx:if="{{O}}" u-i="6569bfe6-6,6569bfe6-3" bind:__l="__l" u-p="{{O}}"></path></svg></view><view class="promotion-info"><view class="promotion-title">刷新信息</view><view class="promotion-desc">刷新后置顶信息排名立刻升到置顶第一位！未置顶的会升到未置顶第一位！</view></view><view class="promotion-action"><view class="action-btn refresh-btn">立即刷新</view></view></view></view><view class="sheet-footer"><button class="cancel-btn" bindtap="{{R}}">取消</button></view></view></view><view wx:if="{{U}}" class="popup-mask" bindtap="{{ab}}"><view class="action-sheet promotion-sheet" catchtap="{{aa}}"><view class="sheet-header"><text class="sheet-title">{{V}}</text><view class="close-btn" bindtap="{{X}}"><image src="{{W}}" class="close-icon"></image></view></view><view class="unified-promotion-container"><configurable-premium-actions wx:if="{{Z}}" bindactionCompleted="{{Y}}" class="compact-card" u-i="6569bfe6-7" bind:__l="__l" u-p="{{Z}}"/></view></view></view>