"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const _sfc_main = {
  __name: "feedback",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const feedbackTypes = common_vendor.ref(["功能建议", "信息错误", "拼车纠纷", "账号问题", "其他问题"]);
    const selectedType = common_vendor.ref(0);
    const feedbackContent = common_vendor.ref("");
    const uploadedImages = common_vendor.ref([]);
    const contactInfo = common_vendor.ref("");
    const showHistoryDetail = common_vendor.ref(false);
    const currentHistory = common_vendor.ref({});
    const showSuccessPopup = common_vendor.ref(false);
    const feedbackHistory = common_vendor.ref([
      {
        id: "6001",
        type: 0,
        content: "建议在拼车页面增加路线规划功能，方便用户查看路线详情。",
        time: "2023-10-10 14:30",
        status: "replied",
        reply: "感谢您的建议！我们正在开发路线规划功能，预计将在下个版本中上线。",
        replyTime: "2023-10-11 09:15"
      },
      {
        id: "6002",
        type: 3,
        content: '无法绑定手机号，提示"验证码错误"，但我确认验证码输入正确。',
        time: "2023-09-25 16:45",
        status: "processing",
        images: ["/static/images/feedback/error.jpg"]
      }
    ]);
    const isFormValid = common_vendor.computed(() => {
      return feedbackContent.value.trim().length >= 5;
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const selectType = (index) => {
      selectedType.value = index;
    };
    const chooseImage = () => {
      if (uploadedImages.value.length >= 3) {
        common_vendor.index.showToast({
          title: "最多上传3张图片",
          icon: "none"
        });
        return;
      }
      common_vendor.index.chooseImage({
        count: 3 - uploadedImages.value.length,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFiles = res.tempFiles;
          let isValid = true;
          tempFiles.forEach((file) => {
            if (file.size > 5 * 1024 * 1024) {
              isValid = false;
              common_vendor.index.showToast({
                title: "图片大小不能超过5MB",
                icon: "none"
              });
              return;
            }
          });
          if (isValid) {
            const tempFilePaths = res.tempFilePaths;
            uploadedImages.value = [...uploadedImages.value, ...tempFilePaths];
            if (uploadedImages.value.length > 3) {
              uploadedImages.value = uploadedImages.value.slice(0, 3);
            }
          }
        }
      });
    };
    const deleteImage = (index) => {
      uploadedImages.value.splice(index, 1);
    };
    const submitFeedback = () => {
      if (!isFormValid.value)
        return;
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        showSuccessPopup.value = true;
        feedbackContent.value = "";
        uploadedImages.value = [];
      }, 1500);
    };
    const closeSuccessPopup = () => {
      showSuccessPopup.value = false;
      const newFeedback = {
        id: Date.now().toString(),
        type: selectedType.value,
        content: feedbackContent.value,
        time: formatTime(/* @__PURE__ */ new Date()),
        status: "pending",
        images: uploadedImages.value
      };
      feedbackHistory.value.unshift(newFeedback);
    };
    const viewHistoryDetail = (item) => {
      currentHistory.value = item;
      showHistoryDetail.value = true;
    };
    const closeHistoryDetail = () => {
      showHistoryDetail.value = false;
    };
    const previewImage = (current, urls) => {
      common_vendor.index.previewImage({
        current,
        urls
      });
    };
    const getStatusText = (status) => {
      const statusMap = {
        pending: "待处理",
        processing: "处理中",
        replied: "已回复",
        closed: "已关闭"
      };
      return statusMap[status] || status;
    };
    const getStatusClass = (status) => {
      const classMap = {
        pending: "status-pending",
        processing: "status-processing",
        replied: "status-replied",
        closed: "status-closed"
      };
      return classMap[status] || "";
    };
    const formatTime = (date) => {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      const hours = date.getHours().toString().padStart(2, "0");
      const minutes = date.getMinutes().toString().padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    };
    common_vendor.onMounted(() => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = systemInfo.statusBarHeight || 20;
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_vendor.f(feedbackTypes.value, (type, index, i0) => {
          return {
            a: common_vendor.t(type),
            b: index,
            c: selectedType.value === index ? 1 : "",
            d: common_vendor.o(($event) => selectType(index), index)
          };
        }),
        e: feedbackContent.value,
        f: common_vendor.o(($event) => feedbackContent.value = $event.detail.value),
        g: common_vendor.t(feedbackContent.value.length),
        h: common_vendor.f(uploadedImages.value, (image, index, i0) => {
          return {
            a: image,
            b: common_vendor.o(($event) => deleteImage(index), index),
            c: index
          };
        }),
        i: uploadedImages.value.length < 3
      }, uploadedImages.value.length < 3 ? {
        j: common_assets._imports_1$35,
        k: common_vendor.o(chooseImage)
      } : {}, {
        l: contactInfo.value,
        m: common_vendor.o(($event) => contactInfo.value = $event.detail.value),
        n: !isFormValid.value,
        o: !isFormValid.value ? 1 : "",
        p: common_vendor.o(submitFeedback),
        q: feedbackHistory.value.length > 0
      }, feedbackHistory.value.length > 0 ? {
        r: common_vendor.f(feedbackHistory.value, (item, index, i0) => {
          return {
            a: common_vendor.t(feedbackTypes.value[item.type]),
            b: common_vendor.t(item.time),
            c: common_vendor.t(item.content),
            d: common_vendor.t(getStatusText(item.status)),
            e: common_vendor.n(getStatusClass(item.status)),
            f: index,
            g: common_vendor.o(($event) => viewHistoryDetail(item), index)
          };
        }),
        s: common_assets._imports_2$32
      } : {}, {
        t: statusBarHeight.value + 44 + "px",
        v: showHistoryDetail.value
      }, showHistoryDetail.value ? {
        w: common_vendor.o(closeHistoryDetail)
      } : {}, {
        x: showHistoryDetail.value
      }, showHistoryDetail.value ? common_vendor.e({
        y: common_vendor.o(closeHistoryDetail),
        z: common_vendor.t(feedbackTypes.value[currentHistory.value.type]),
        A: common_vendor.t(currentHistory.value.time),
        B: common_vendor.t(getStatusText(currentHistory.value.status)),
        C: common_vendor.n(getStatusClass(currentHistory.value.status)),
        D: common_vendor.t(currentHistory.value.content),
        E: currentHistory.value.images && currentHistory.value.images.length > 0
      }, currentHistory.value.images && currentHistory.value.images.length > 0 ? {
        F: common_vendor.f(currentHistory.value.images, (img, index, i0) => {
          return {
            a: index,
            b: img,
            c: common_vendor.o(($event) => previewImage(img, currentHistory.value.images), index)
          };
        })
      } : {}, {
        G: currentHistory.value.reply
      }, currentHistory.value.reply ? {
        H: common_vendor.t(currentHistory.value.replyTime),
        I: common_vendor.t(currentHistory.value.reply)
      } : {}) : {}, {
        J: showSuccessPopup.value
      }, showSuccessPopup.value ? {} : {}, {
        K: showSuccessPopup.value
      }, showSuccessPopup.value ? {
        L: common_assets._imports_3$30,
        M: common_vendor.o(closeSuccessPopup)
      } : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/carpool-package/pages/carpool/my/feedback.js.map
