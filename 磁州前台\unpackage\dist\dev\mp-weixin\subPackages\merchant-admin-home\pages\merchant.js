"use strict";
const common_vendor = require("../../../common/vendor.js");
const utils_navigation = require("../../../utils/navigation.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      statusBarHeight: 20,
      navbarHeight: 64,
      merchantInfo: {
        logo: "",
        name: "",
        verified: false,
        merchantId: ""
      },
      statsData: {
        views: 0,
        leads: 0,
        income: "0.00"
      },
      pendingItems: [],
      merchantCategory: ""
    };
  },
  onLoad() {
    const sysInfo = common_vendor.index.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 44;
    this.loadMerchantInfo();
    this.loadStatsData();
    this.loadPendingItems();
  },
  onShareAppMessage() {
    return {
      title: "商家后台 - 专业管理工具，助力商家成长",
      path: "/pages/my/merchant"
    };
  },
  methods: {
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 跳转到设置页面
    navigateToSettings() {
      utils_navigation.smartNavigate("/subPackages/merchant-plugin/pages/settings").catch((err) => {
        common_vendor.index.__f__("error", "at subPackages/merchant-admin-home/pages/merchant.vue:365", "跳转到设置页面失败:", err);
      });
    },
    // 页面跳转
    navigateTo(url) {
      utils_navigation.smartNavigate(url).catch((err) => {
        common_vendor.index.__f__("error", "at subPackages/merchant-admin-home/pages/merchant.vue:373", "页面跳转失败:", err);
      });
    },
    // 跳转到商家认证页面
    navigateToVerify() {
      utils_navigation.smartNavigate("/subPackages/merchant-plugin/pages/verify");
    },
    // 跳转到商家资料编辑页面
    navigateToMerchantEdit() {
      utils_navigation.smartNavigate("/subPackages/merchant-plugin/pages/profile");
    },
    // 处理待办事项
    handlePendingItem(item) {
      const urlMap = {
        "order": "/subPackages/merchant-plugin/pages/orders",
        "comment": "/subPackages/merchant-plugin/pages/reviews",
        "finance": "/subPackages/merchant-plugin/pages/finance",
        "approval": "/subPackages/merchant-plugin/pages/approval"
      };
      if (urlMap[item.type]) {
        this.navigateTo(urlMap[item.type]);
      }
    },
    // 加载商家信息
    loadMerchantInfo() {
      setTimeout(() => {
        const verified = Math.random() > 0.3;
        this.merchantInfo = {
          logo: verified ? "/static/images/shop-logo.png" : "",
          name: verified ? "磁州特色小吃店" : "",
          verified,
          merchantId: "M" + Math.floor(Math.random() * 1e6).toString().padStart(6, "0")
        };
      }, 500);
    },
    // 加载数据统计
    loadStatsData() {
      setTimeout(() => {
        this.statsData = {
          views: Math.floor(Math.random() * 500),
          leads: Math.floor(Math.random() * 100),
          income: (Math.random() * 1e3).toFixed(2)
        };
      }, 600);
    },
    // 加载待处理事项
    loadPendingItems() {
      setTimeout(() => {
        const havePendingItems = Math.random() > 0.3;
        if (havePendingItems) {
          this.pendingItems = [
            {
              type: "service",
              count: Math.floor(Math.random() * 10) + 1,
              title: "待处理服务",
              desc: "有新的服务预约需要确认",
              url: "/subPackages/merchant-plugin/pages/service-orders"
            },
            {
              type: "comment",
              count: Math.floor(Math.random() * 5) + 1,
              title: "新评价待回复",
              desc: "有客户的评价需要回复",
              url: "/subPackages/merchant-plugin/pages/reviews"
            },
            {
              type: "finance",
              count: Math.floor(Math.random() * 3) + 1,
              title: "待结算账单",
              desc: "有账单需要确认结算",
              url: "/subPackages/merchant-plugin/pages/finance"
            },
            {
              type: "info",
              count: Math.floor(Math.random() * 2) + 1,
              title: "信息即将到期",
              desc: "有信息需要更新或续期",
              url: "/subPackages/merchant-plugin/pages/info-manage"
            }
          ];
          const count = Math.floor(Math.random() * 3) + 1;
          this.pendingItems = this.pendingItems.slice(0, count);
        } else {
          this.pendingItems = [];
        }
      }, 700);
    },
    // 获取商家类目标题
    getCategoryTitle(category) {
      const categoryTitles = {
        "food": "美食小吃",
        "house": "房产中介",
        "decoration": "装修家居",
        "service": "到家服务"
      };
      return categoryTitles[category] || "未分类商家";
    },
    // 跳转到个人资料页面
    goToProfile() {
      common_vendor.index.navigateTo({
        url: "/pages/user-center/profile",
        fail: (err) => {
          common_vendor.index.__f__("error", "at subPackages/merchant-admin-home/pages/merchant.vue:494", "跳转到个人资料页面失败:", err);
          common_vendor.index.showToast({
            title: "跳转失败，请稍后再试",
            icon: "none"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$5,
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: common_assets._imports_1$46,
    d: common_vendor.o((...args) => $options.navigateToSettings && $options.navigateToSettings(...args)),
    e: $data.statusBarHeight + "px",
    f: $data.merchantInfo.logo || "/static/images/default-shop.png",
    g: common_vendor.t($data.merchantInfo.name || "未认证商家"),
    h: $data.merchantInfo.verified ? "/static/images/tabbar/已认证.png" : "/static/images/tabbar/未认证.png",
    i: common_vendor.t($data.merchantInfo.verified ? "已认证" : "未认证"),
    j: $data.merchantInfo.verified ? 1 : "",
    k: !$data.merchantInfo.verified
  }, !$data.merchantInfo.verified ? {
    l: common_vendor.o((...args) => $options.navigateToVerify && $options.navigateToVerify(...args))
  } : {
    m: common_vendor.o((...args) => $options.navigateToMerchantEdit && $options.navigateToMerchantEdit(...args))
  }, {
    n: common_vendor.t($data.statsData.views),
    o: common_vendor.t($data.statsData.leads),
    p: common_vendor.t($data.statsData.income),
    q: $data.navbarHeight + 10 + "px",
    r: common_assets._imports_2$40,
    s: common_vendor.o((...args) => $options.goToProfile && $options.goToProfile(...args)),
    t: common_assets._imports_3$9,
    v: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-plugin/pages/info-publish")),
    w: common_assets._imports_4$25,
    x: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-plugin/pages/info-manage")),
    y: common_assets._imports_5$3,
    z: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-admin/pages/products/create")),
    A: common_assets._imports_6$19,
    B: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-plugin/pages/reviews")),
    C: common_assets._imports_7$2,
    D: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-admin-marketing/pages/marketing/index")),
    E: common_assets._imports_8$8,
    F: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-plugin/pages/coupon")),
    G: common_assets._imports_9$10,
    H: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-plugin/pages/red-packet-manage")),
    I: common_assets._imports_10,
    J: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-plugin/pages/poster")),
    K: common_assets._imports_11$3,
    L: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-plugin/pages/analysis")),
    M: common_assets._imports_12$5,
    N: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-plugin/pages/finance")),
    O: common_assets._imports_13$3,
    P: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-plugin/pages/visitor")),
    Q: common_assets._imports_14$5,
    R: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-plugin/pages/leads")),
    S: common_assets._imports_15$2,
    T: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-admin-order/pages/orders/list")),
    U: common_assets._imports_16$2,
    V: common_vendor.o(() => {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/verification/index"
      });
    }),
    W: common_assets._imports_17$2,
    X: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-plugin/pages/customer")),
    Y: common_assets._imports_1$46,
    Z: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-plugin/pages/settings")),
    aa: $data.merchantCategory
  }, $data.merchantCategory ? common_vendor.e({
    ab: common_vendor.t($options.getCategoryTitle($data.merchantCategory)),
    ac: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-plugin/pages/industry-features")),
    ad: $data.merchantCategory === "food"
  }, $data.merchantCategory === "food" ? {
    ae: common_assets._imports_18,
    af: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-plugin/pages/food/menu"))
  } : $data.merchantCategory === "house" ? {
    ah: common_assets._imports_19$1,
    ai: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-plugin/pages/house/listing"))
  } : $data.merchantCategory === "decoration" ? {
    ak: common_assets._imports_20,
    al: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-plugin/pages/decoration/case"))
  } : $data.merchantCategory === "service" ? {
    an: common_assets._imports_21,
    ao: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-plugin/pages/services"))
  } : {}, {
    ag: $data.merchantCategory === "house",
    aj: $data.merchantCategory === "decoration",
    am: $data.merchantCategory === "service"
  }) : {}, {
    ap: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-plugin/pages/todo")),
    aq: $data.pendingItems.length > 0
  }, $data.pendingItems.length > 0 ? {
    ar: common_vendor.f($data.pendingItems, (item, index, i0) => {
      return {
        a: common_vendor.t(item.count),
        b: common_vendor.n("badge-" + item.type),
        c: common_vendor.t(item.title),
        d: common_vendor.t(item.desc),
        e: index,
        f: common_vendor.o(($event) => $options.handlePendingItem(item), index)
      };
    })
  } : {
    as: common_assets._imports_22
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
_sfc_main.__runtimeHooks = 2;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-home/pages/merchant.js.map
