<template>
  <view class="points-create-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-left">
        <view class="back-button" @tap="goBack">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 18L9 12L15 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </view>
      </view>
      <view class="navbar-title">
        <text class="title-text">创建积分商品</text>
      </view>
      <view class="navbar-right">
        <!-- 占位 -->
      </view>
    </view>
    
    <!-- 页面内容区域 -->
    <scroll-view scroll-y class="content-area">
      <!-- 表单区域 -->
      <view class="form-section">
        <!-- 基本信息 -->
        <view class="form-group">
          <view class="form-title">基本信息</view>
          
          <view class="form-item">
            <text class="form-label">商品名称</text>
            <input class="form-input" v-model="formData.name" placeholder="请输入积分商品名称" />
          </view>
          
          <view class="form-item">
            <text class="form-label">所需积分</text>
            <input class="form-input" v-model="formData.points" type="number" placeholder="请输入兑换所需积分" />
          </view>
          
          <view class="form-item">
            <text class="form-label">库存数量</text>
            <input class="form-input" v-model="formData.stock" type="number" placeholder="请输入商品库存" />
          </view>
        </view>
        
        <!-- 商品图片 -->
        <view class="form-group">
          <view class="form-title">商品图片</view>
          <view class="image-uploader">
            <view class="upload-box" @tap="chooseImage" v-if="!formData.image">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 5V19M5 12H19" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <text class="upload-text">上传图片</text>
            </view>
            <view class="image-preview" v-else>
              <image class="preview-image" :src="formData.image" mode="aspectFill"></image>
              <view class="delete-image" @tap="deleteImage">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M18 6L6 18M6 6L18 18" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 有效期 -->
        <view class="form-group">
          <view class="form-title">有效期</view>
          
          <view class="form-item">
            <text class="form-label">开始时间</text>
            <picker mode="date" :value="formData.startDate" @change="onStartDateChange" class="date-picker">
              <view class="picker-value">{{formData.startDate || '请选择开始日期'}}</view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="form-label">结束时间</text>
            <picker mode="date" :value="formData.endDate" @change="onEndDateChange" class="date-picker">
              <view class="picker-value">{{formData.endDate || '请选择结束日期'}}</view>
            </picker>
          </view>
        </view>
        
        <!-- 商品描述 -->
        <view class="form-group">
          <view class="form-title">商品描述</view>
          <textarea class="form-textarea" v-model="formData.description" placeholder="请输入商品描述信息" />
        </view>
        
        <!-- 兑换规则 -->
        <view class="form-group">
          <view class="form-title">兑换规则</view>
          <textarea class="form-textarea" v-model="formData.rules" placeholder="请输入兑换规则" />
        </view>
      </view>
      
      <!-- 底部空间 -->
      <view class="bottom-space"></view>
    </scroll-view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="action-button save-draft" @tap="saveDraft">保存草稿</view>
      <view class="action-button publish" @tap="publishItem">立即发布</view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue';

// 表单数据
const formData = reactive({
  name: '',
  points: '',
  stock: '',
  image: '',
  startDate: '',
  endDate: '',
  description: '',
  rules: ''
});

// 选择图片
const chooseImage = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      formData.image = res.tempFilePaths[0];
    }
  });
};

// 删除图片
const deleteImage = () => {
  formData.image = '';
};

// 日期选择
const onStartDateChange = (e) => {
  formData.startDate = e.detail.value;
};

const onEndDateChange = (e) => {
  formData.endDate = e.detail.value;
};

// 保存草稿
const saveDraft = () => {
  if (!formData.name) {
    uni.showToast({
      title: '请输入商品名称',
      icon: 'none'
    });
    return;
  }
  
  uni.showToast({
    title: '草稿保存成功',
    icon: 'success'
  });
  
  setTimeout(() => {
    goBack();
  }, 1500);
};

// 发布商品
const publishItem = () => {
  // 表单验证
  if (!formData.name) {
    uni.showToast({
      title: '请输入商品名称',
      icon: 'none'
    });
    return;
  }
  
  if (!formData.points) {
    uni.showToast({
      title: '请输入所需积分',
      icon: 'none'
    });
    return;
  }
  
  if (!formData.stock) {
    uni.showToast({
      title: '请输入库存数量',
      icon: 'none'
    });
    return;
  }
  
  if (!formData.image) {
    uni.showToast({
      title: '请上传商品图片',
      icon: 'none'
    });
    return;
  }
  
  if (!formData.startDate || !formData.endDate) {
    uni.showToast({
      title: '请设置有效期',
      icon: 'none'
    });
    return;
  }
  
  // 提交表单
  uni.showLoading({
    title: '正在发布...'
  });
  
  // 模拟API请求
  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: '发布成功',
      icon: 'success'
    });
    
    setTimeout(() => {
      goBack();
    }, 1500);
  }, 2000);
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss">
/* 页面容器 */
.points-create-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
  position: relative;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  color: white;
  padding: 48px 20px 16px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(255, 120, 0, 0.15);
}

.navbar-left {
  width: 40px;
}

.back-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-title {
  flex: 1;
  text-align: center;
}

.title-text {
  font-size: 18px;
  font-weight: 600;
}

.navbar-right {
  width: 40px;
}

/* 内容区域 */
.content-area {
  flex: 1;
  padding: 16px;
  box-sizing: border-box;
  height: calc(100vh - 140px); /* 减去导航栏和底部操作栏的高度 */
}

/* 表单区域 */
.form-section {
  margin-bottom: 20px;
}

.form-group {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.form-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16px;
}

.form-item {
  margin-bottom: 16px;
}

.form-label {
  font-size: 14px;
  color: #666666;
  display: block;
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  height: 44px;
  background-color: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  box-sizing: border-box;
  font-size: 14px;
  color: #333333;
}

.form-textarea {
  width: 100%;
  height: 120px;
  background-color: #F5F7FA;
  border-radius: 8px;
  padding: 12px;
  box-sizing: border-box;
  font-size: 14px;
  color: #333333;
}

/* 图片上传 */
.image-uploader {
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-box {
  width: 120px;
  height: 120px;
  background-color: #F5F7FA;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-text {
  font-size: 14px;
  color: #999999;
  margin-top: 8px;
}

.image-preview {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-image {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 日期选择器 */
.date-picker {
  width: 100%;
  height: 44px;
  background-color: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.picker-value {
  font-size: 14px;
  color: #333333;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 60px;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 99;
}

.action-button {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
}

.save-draft {
  background-color: #F5F7FA;
  color: #666666;
  margin-right: 12px;
}

.publish {
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  color: white;
}

/* 底部空间 */
.bottom-space {
  height: 60px;
}
</style> 