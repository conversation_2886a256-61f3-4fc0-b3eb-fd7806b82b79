"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
if (!Math) {
  ConfigurablePremiumActions();
}
const ConfigurablePremiumActions = () => "../../../../components/premium/ConfigurablePremiumActions.js";
const _sfc_main = {
  __name: "published-list",
  setup(__props, { expose: __expose }) {
    const statusBarHeight = common_vendor.ref(20);
    const statusTabs = common_vendor.ref([
      { label: "全部", value: "all", count: 0 },
      { label: "待出行", value: "pending", count: 0 },
      { label: "进行中", value: "ongoing", count: 0 },
      { label: "已完成", value: "completed", count: 0 },
      { label: "已取消", value: "canceled", count: 0 }
    ]);
    const currentStatus = common_vendor.ref("all");
    const tripList = common_vendor.ref([]);
    const page = common_vendor.ref(1);
    common_vendor.ref(10);
    const hasMore = common_vendor.ref(true);
    const isLoading = common_vendor.ref(false);
    const isRefreshing = common_vendor.ref(false);
    const currentItem = common_vendor.ref(null);
    const showMorePopup = common_vendor.ref(false);
    const showTopPopup = common_vendor.ref(false);
    const activePromotionType = common_vendor.ref("");
    const userRefreshCount = common_vendor.ref(0);
    const showPromotionOptionsPopup = common_vendor.ref(false);
    common_vendor.onMounted(() => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = systemInfo.statusBarHeight;
      const app = getApp();
      if (app.globalData) {
        app.globalData.statusBarHeight = statusBarHeight.value;
      }
      loadTripList();
      updateStatusCount();
    });
    common_vendor.nextTick$1(() => {
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const publishNewTrip = () => {
      common_vendor.index.navigateTo({
        url: "/carpool-package/pages/carpool/publish/index"
      });
    };
    const switchStatus = (status) => {
      if (currentStatus.value === status)
        return;
      currentStatus.value = status;
      page.value = 1;
      tripList.value = [];
      hasMore.value = true;
      loadTripList();
    };
    const loadTripList = () => {
      if (isLoading.value)
        return;
      isLoading.value = true;
      setTimeout(() => {
        const mockData = getMockData();
        if (page.value === 1) {
          tripList.value = mockData;
        } else {
          tripList.value = [...tripList.value, ...mockData];
        }
        hasMore.value = page.value < 3;
        isLoading.value = false;
        isRefreshing.value = false;
      }, 1e3);
    };
    const getMockData = () => {
      return [
        {
          id: "1001",
          status: "pending",
          startPoint: "磁县火车站",
          endPoint: "邯郸东站",
          departureTime: "2024-01-20 14:30",
          totalSeats: 4,
          price: 35,
          orders: [
            {
              userAvatar: "/static/images/avatar/user1.png",
              userName: "张先生",
              seats: 2,
              status: "confirmed"
            }
          ]
        },
        {
          id: "1002",
          status: "ongoing",
          startPoint: "磁县汽车站",
          endPoint: "邯郸市区",
          departureTime: "2024-01-19 09:30",
          totalSeats: 3,
          price: 25,
          orders: [
            {
              userAvatar: "/static/images/avatar/user2.png",
              userName: "李女士",
              seats: 1,
              status: "completed"
            }
          ]
        }
      ];
    };
    const updateStatusCount = () => {
      statusTabs.value = statusTabs.value.map((tab) => ({
        ...tab,
        count: tab.value === "all" ? 8 : 2
      }));
    };
    const getStatusText = (status) => {
      const statusMap = {
        all: "全部",
        pending: "待出行",
        ongoing: "进行中",
        completed: "已完成",
        canceled: "已取消"
      };
      return statusMap[status] || status;
    };
    const onRefresh = () => {
      isRefreshing.value = true;
      page.value = 1;
      loadTripList();
    };
    const loadMore = () => {
      if (hasMore.value && !isLoading.value) {
        page.value++;
        loadTripList();
      }
    };
    const viewTripDetail = (item) => {
      common_vendor.index.navigateTo({
        url: `/carpool-package/pages/carpool/trip-detail/index?id=${item.id}`
      });
    };
    const shareTrip = (item) => {
      common_vendor.index.showShareMenu({
        withShareTicket: true,
        menus: ["shareAppMessage", "shareTimeline"]
      });
    };
    const closeMorePopup = () => {
      showMorePopup.value = false;
    };
    const editContent = (item) => {
      if (item) {
        showMorePopup.value = false;
        common_vendor.index.navigateTo({
          url: `/carpool-package/pages/carpool/edit-trip/index?id=${item.id}`
        });
      }
    };
    const deleteContent = () => {
      common_vendor.index.showModal({
        title: "删除内容",
        content: "确定要删除该行程吗？删除后无法恢复",
        success: (res) => {
          if (res.confirm) {
            showMorePopup.value = false;
            common_vendor.index.showToast({
              title: "删除成功",
              icon: "success"
            });
            tripList.value = tripList.value.filter((i) => i.id !== currentItem.value.id);
          }
        }
      });
    };
    const unpublishContent = () => {
      common_vendor.index.showModal({
        title: "下架内容",
        content: "确定要下架该行程吗？下架后可在已下架列表中重新上架",
        success: (res) => {
          if (res.confirm) {
            showMorePopup.value = false;
            common_vendor.index.showToast({
              title: "下架成功",
              icon: "success"
            });
            if (currentItem.value) {
              currentItem.value.status = "unpublished";
              if (currentStatus.value !== "all") {
                tripList.value = tripList.value.filter((i) => i.id !== currentItem.value.id);
              }
            }
          }
        }
      });
    };
    const showTopOptions = (item) => {
      currentItem.value = item;
      activePromotionType.value = "";
      showTopPopup.value = true;
    };
    const selectPromotionOption = (type) => {
      activePromotionType.value = type;
      showTopPopup.value = false;
      showPromotionOptionsPopup.value = true;
    };
    const closeTopPopup = () => {
      showTopPopup.value = false;
      activePromotionType.value = "";
    };
    const closePromotionOptionsPopup = () => {
      showPromotionOptionsPopup.value = false;
      activePromotionType.value = "";
    };
    const handlePromotionCompleted = (eventType, data) => {
      showPromotionOptionsPopup.value = false;
      if (data.action === "top") {
        if (currentItem.value) {
          currentItem.value.isTopPinned = true;
          if (data.option && data.option.duration) {
            currentItem.value.topPinnedDays = parseInt(data.option.duration) || 1;
          }
        }
        common_vendor.index.showToast({
          title: "置顶成功",
          icon: "success"
        });
      } else if (data.action === "refresh") {
        if (eventType === "useRefreshCount") {
          userRefreshCount.value = data.remainingCount || 0;
        }
        common_vendor.index.showToast({
          title: "刷新成功",
          icon: "success"
        });
        loadTripList();
      }
      if (eventType === "refreshPackage") {
        userRefreshCount.value = data.totalCount || 0;
        common_vendor.index.showToast({
          title: `购买${data.count}次成功`,
          icon: "success"
        });
      }
    };
    __expose({
      loadTripList,
      switchStatus,
      publishNewTrip
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: common_assets._imports_1$41,
        d: common_vendor.o(publishNewTrip),
        e: statusBarHeight.value + "px",
        f: common_vendor.f(statusTabs.value, (tab, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(tab.label),
            b: tab.count > 0
          }, tab.count > 0 ? {
            c: common_vendor.t(tab.count)
          } : {}, {
            d: currentStatus.value === tab.value
          }, currentStatus.value === tab.value ? {} : {}, {
            e: index,
            f: currentStatus.value === tab.value ? 1 : "",
            g: common_vendor.o(($event) => switchStatus(tab.value), index)
          });
        }),
        g: statusBarHeight.value + 44 + "px",
        h: tripList.value.length > 0
      }, tripList.value.length > 0 ? {
        i: common_vendor.f(tripList.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.startPoint),
            b: common_vendor.t(item.endPoint),
            c: common_vendor.t(item.departureTime),
            d: common_vendor.t(item.totalSeats),
            e: common_vendor.t(getStatusText(item.status)),
            f: common_vendor.n(item.status),
            g: item.isTopPinned
          }, item.isTopPinned ? {} : {}, {
            h: common_vendor.t(item.price),
            i: common_vendor.o(($event) => editContent(item), item.id),
            j: common_vendor.o(($event) => showTopOptions(item), item.id),
            k: common_vendor.o(($event) => shareTrip(), item.id),
            l: item.id,
            m: item.isTopPinned ? 1 : "",
            n: common_vendor.o(($event) => viewTripDetail(item), item.id)
          });
        })
      } : {}, {
        j: tripList.value.length === 0
      }, tripList.value.length === 0 ? {
        k: common_assets._imports_1$40,
        l: common_vendor.t(getStatusText(currentStatus.value)),
        m: common_assets._imports_3$32,
        n: common_vendor.o(publishNewTrip)
      } : {}, {
        o: tripList.value.length > 0 && isLoading.value
      }, tripList.value.length > 0 && isLoading.value ? {} : {}, {
        p: tripList.value.length > 0 && !hasMore.value && !isLoading.value
      }, tripList.value.length > 0 && !hasMore.value && !isLoading.value ? {} : {}, {
        q: isRefreshing.value,
        r: common_vendor.o(onRefresh),
        s: common_vendor.o(loadMore),
        t: statusBarHeight.value + 88 + "px",
        v: `calc(100vh - ${statusBarHeight.value + 88}px)`,
        w: showMorePopup.value
      }, showMorePopup.value ? {
        x: common_assets._imports_4$20,
        y: common_vendor.o(closeMorePopup),
        z: common_vendor.o(($event) => editContent(currentItem.value)),
        A: common_vendor.o(deleteContent),
        B: common_vendor.o(unpublishContent),
        C: common_vendor.o(closeMorePopup),
        D: common_vendor.o(() => {
        }),
        E: common_vendor.o(closeMorePopup)
      } : {}, {
        F: showTopPopup.value
      }, showTopPopup.value ? {
        G: common_assets._imports_4$20,
        H: common_vendor.o(closeTopPopup),
        I: common_vendor.p({
          d: "M857.088 224.256H166.912c-17.92 0-33.792 7.68-45.056 20.48-11.264 12.288-16.896 28.16-15.872 45.056l28.16 462.848c1.024 25.088 20.992 44.544 46.08 44.544H774.144c25.088 0 45.056-20.48 46.08-44.544l28.672-462.848c1.024-16.896-4.608-32.768-15.872-45.056-11.264-13.312-27.136-20.48-45.056-20.48z m-319.488 419.84h-51.2l25.088-227.84h1.024l25.088 227.84z m275.968 44.032c0 4.096-3.072 7.68-7.68 7.68H218.112c-4.096 0-7.68-3.584-7.68-7.68l-23.04-379.904c0-2.56 1.024-5.12 2.56-6.656 2.048-2.048 4.096-3.072 6.656-3.072h628.224c2.56 0 5.12 1.024 6.656 3.072 2.048 2.048 2.56 4.096 2.56 6.656l-20.48 379.904z",
          fill: "#1296db",
          ["p-id"]: "2272"
        }),
        J: common_vendor.p({
          d: "M512 143.872c10.24 0 18.944-7.168 21.504-17.408 0.512-3.072 0.512-5.632 0-8.704l-21.504-71.68-21.504 71.68c-0.512 3.072-0.512 5.632 0 8.704 2.56 10.24 11.264 17.408 21.504 17.408zM355.84 183.808c7.68-7.68 9.216-19.456 4.096-28.16-1.536-2.56-3.584-4.608-5.632-6.144L295.936 108.032l28.672 65.536c1.536 2.56 3.584 4.608 5.632 6.144 8.704 5.12 20.48 3.584 28.16-4.096 0-0.512-2.56-1.024-2.56-1.536zM206.336 276.992c3.072-10.752-1.536-22.528-11.264-27.648-3.072-1.536-5.632-2.048-8.704-2.56l-73.216-10.752 64.512 35.84c2.56 1.536 5.632 2.048 8.704 2.56 10.752 3.072 17.92 1.536 19.456 2.56h0.512zM819.2 236.032c-3.072 0.512-5.632 1.024-8.704 2.56-9.728 5.12-14.336 16.896-11.264 27.648l-0.512-0.512c1.536-1.024 8.704-0.512 19.456-2.56 3.072-0.512 5.632-1.024 8.704-2.56l64.512-35.84-73.216 10.752h1.024zM663.552 149.504c-2.048 1.536-4.096 3.584-5.632 6.144-5.12 9.216-3.584 20.992 4.096 28.16v0.512c7.68 7.68 19.456 9.216 28.16 4.096 2.048-1.536 4.096-3.584 5.632-6.144l28.672-65.536-58.368 41.472-2.56-8.704z",
          fill: "#1296db",
          ["p-id"]: "2273"
        }),
        K: common_vendor.p({
          t: "1692585872514",
          viewBox: "0 0 1024 1024",
          version: "1.1",
          xmlns: "http://www.w3.org/2000/svg",
          ["p-id"]: "2271",
          width: "32",
          height: "32"
        }),
        L: common_vendor.o(($event) => selectPromotionOption("top")),
        M: common_vendor.p({
          d: "M512 981.333333C252.8 981.333333 42.666667 771.2 42.666667 512S252.8 42.666667 512 42.666667s469.333333 210.133333 469.333333 469.333333-210.133333 469.333333-469.333333 469.333333z m0-853.333333C276.48 128 128 276.48 128 512s148.48 384 384 384 384-148.48 384-384S747.52 128 512 128z",
          fill: "#6366f1",
          ["p-id"]: "3296"
        }),
        N: common_vendor.p({
          d: "M512 725.333333c-117.76 0-213.333333-95.573333-213.333333-213.333333s95.573333-213.333333 213.333333-213.333333 213.333333 95.573333 213.333333 213.333333-95.573333 213.333333-213.333333 213.333333z m0-341.333333c-70.653333 0-128 57.346667-128 128s57.346667 128 128 128 128-57.346667 128-128-57.346667-128-128-128z",
          fill: "#6366f1",
          ["p-id"]: "3297"
        }),
        O: common_vendor.p({
          d: "M512 896c-23.466667 0-42.666667-19.2-42.666667-42.666667v-85.333333c0-23.466667 19.2-42.666667 42.666667-42.666667s42.666667 19.2 42.666667 42.666667v85.333333c0 23.466667-19.2 42.666667-42.666667 42.666667zM512 298.666667c-23.466667 0-42.666667-19.2-42.666667-42.666667V170.666667c0-23.466667 19.2-42.666667 42.666667-42.666667s42.666667 19.2 42.666667 42.666667v85.333333c0 23.466667-19.2 42.666667-42.666667 42.666667zM768 640h85.333333c23.466667 0 42.666667-19.2 42.666667-42.666667s-19.2-42.666667-42.666667-42.666666h-85.333333c-23.466667 0-42.666667 19.2-42.666667 42.666666s19.2 42.666667 42.666667 42.666667zM170.666667 640h85.333333c23.466667 0 42.666667-19.2 42.666667-42.666667s-19.2-42.666667-42.666667-42.666666H170.666667c-23.466667 0-42.666667 19.2-42.666667 42.666666s19.2 42.666667 42.666667 42.666667z",
          fill: "#6366f1",
          ["p-id"]: "3298"
        }),
        P: common_vendor.p({
          t: "1692586074385",
          viewBox: "0 0 1024 1024",
          version: "1.1",
          xmlns: "http://www.w3.org/2000/svg",
          ["p-id"]: "3295",
          width: "32",
          height: "32"
        }),
        Q: common_vendor.o(($event) => selectPromotionOption("refresh")),
        R: common_vendor.o(closeTopPopup),
        S: common_vendor.o(() => {
        }),
        T: common_vendor.o(closeTopPopup)
      } : {}, {
        U: showPromotionOptionsPopup.value
      }, showPromotionOptionsPopup.value ? {
        V: common_vendor.t(activePromotionType.value === "top" ? "置顶方式" : activePromotionType.value === "refresh" ? "刷新方式" : "推广方式"),
        W: common_assets._imports_4$20,
        X: common_vendor.o(closePromotionOptionsPopup),
        Y: common_vendor.o(handlePromotionCompleted),
        Z: common_vendor.p({
          ["info-id"]: currentItem.value ? currentItem.value.id : "",
          ["page-type"]: activePromotionType.value,
          ["show-mode"]: "direct",
          ["refresh-count"]: userRefreshCount.value
        }),
        aa: common_vendor.o(() => {
        }),
        ab: common_vendor.o(closePromotionOptionsPopup)
      } : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/carpool-package/pages/carpool/my/published-list.js.map
