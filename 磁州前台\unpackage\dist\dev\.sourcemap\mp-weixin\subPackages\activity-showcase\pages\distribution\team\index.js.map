{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/distribution/team/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcZGlzdHJpYnV0aW9uXHRlYW1caW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"team-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\">\r\n      <view class=\"navbar-bg\"></view>\r\n      <view class=\"navbar-content\">\r\n        <view class=\"back-btn\" @click=\"navigateBack\">\r\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n            <path d=\"M19 12H5M12 19l-7-7 7-7\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n          </svg>\r\n        </view>\r\n        <view class=\"navbar-title\">我的团队</view>\r\n        <view class=\"navbar-right\">\r\n          <view class=\"invite-btn\" @click=\"showInviteQrcode\">\r\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\">\r\n              <path d=\"M12 5v14M5 12h14\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            </svg>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 内容区域 -->\r\n    <view class=\"content\">\r\n      <!-- 团队统计卡片 -->\r\n      <view class=\"stats-card\" :style=\"{\r\n        borderRadius: '35px',\r\n        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',\r\n        background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)',\r\n        padding: '30rpx',\r\n        marginBottom: '30rpx'\r\n      }\">\r\n        <view class=\"stats-grid\">\r\n          <view class=\"stat-item\">\r\n            <text class=\"stat-number\">{{ teamStats.totalMembers }}</text>\r\n            <text class=\"stat-label\">团队总人数</text>\r\n          </view>\r\n          <view class=\"stat-item\">\r\n            <text class=\"stat-number\">{{ teamStats.directMembers }}</text>\r\n            <text class=\"stat-label\">直属成员</text>\r\n          </view>\r\n          <view class=\"stat-item\">\r\n            <text class=\"stat-number\">{{ teamStats.indirectMembers }}</text>\r\n            <text class=\"stat-label\">间接成员</text>\r\n          </view>\r\n          <view class=\"stat-item\">\r\n            <text class=\"stat-number\">{{ teamStats.newMembers }}</text>\r\n            <text class=\"stat-label\">本月新增</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 团队分类标签 -->\r\n      <view class=\"filter-tabs\" :style=\"{\r\n        borderRadius: '35px',\r\n        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',\r\n        background: '#FFFFFF',\r\n        padding: '20rpx 30rpx',\r\n        marginBottom: '30rpx'\r\n      }\">\r\n        <scroll-view class=\"tabs-scroll\" scroll-x>\r\n          <view class=\"tabs-container\">\r\n            <view \r\n              v-for=\"(tab, index) in teamTabs\" \r\n              :key=\"index\"\r\n              class=\"tab-item\"\r\n              :class=\"{ active: currentTab === index }\"\r\n              @click=\"switchTab(index)\"\r\n            >\r\n              <text>{{ tab.name }}</text>\r\n              <view class=\"tab-indicator\" v-if=\"currentTab === index\"></view>\r\n            </view>\r\n          </view>\r\n        </scroll-view>\r\n      </view>\r\n      \r\n      <!-- 团队成员列表 -->\r\n      <view class=\"members-list\">\r\n        <view \r\n          v-for=\"member in filteredMembers\"\r\n          :key=\"member.id\"\r\n          class=\"member-card\"\r\n          :style=\"{\r\n            borderRadius: '35px',\r\n            boxShadow: '0 8px 20px rgba(172,57,255,0.15)',\r\n            background: '#FFFFFF',\r\n            padding: '30rpx',\r\n            marginBottom: '30rpx'\r\n          }\"\r\n          @click=\"viewMemberDetail(member)\"\r\n        >\r\n          <!-- 成员头部信息 -->\r\n          <view class=\"member-header\">\r\n            <view class=\"member-avatar-section\">\r\n              <image class=\"member-avatar\" :src=\"member.avatar\" mode=\"aspectFill\"></image>\r\n              <view class=\"online-status\" v-if=\"member.isOnline\"></view>\r\n            </view>\r\n            <view class=\"member-basic-info\">\r\n              <view class=\"member-name-level\">\r\n                <text class=\"member-name\">{{ member.name }}</text>\r\n                <view class=\"member-level\" :style=\"{\r\n                  background: getLevelColor(member.level),\r\n                  borderRadius: '20rpx',\r\n                  padding: '6rpx 12rpx'\r\n                }\">\r\n                  <text style=\"color: #FFFFFF; font-size: 20rpx;\">{{ member.level }}</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"member-meta\">\r\n                <text class=\"join-time\">{{ member.joinTime }} 加入</text>\r\n                <text class=\"member-id\">ID: {{ member.id }}</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"member-actions\">\r\n              <svg class=\"arrow-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n                <path d=\"M9 18l6-6-6-6\" stroke=\"#C7C7CC\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 成员数据统计 -->\r\n          <view class=\"member-stats\">\r\n            <view class=\"stat-row\">\r\n              <view class=\"stat-item\">\r\n                <text class=\"stat-value\">{{ member.orderCount }}</text>\r\n                <text class=\"stat-label\">订单数</text>\r\n              </view>\r\n              <view class=\"stat-item\">\r\n                <text class=\"stat-value\">¥{{ member.contribution }}</text>\r\n                <text class=\"stat-label\">贡献佣金</text>\r\n              </view>\r\n              <view class=\"stat-item\">\r\n                <text class=\"stat-value\">{{ member.teamSize }}</text>\r\n                <text class=\"stat-label\">下级团队</text>\r\n              </view>\r\n              <view class=\"stat-item\">\r\n                <text class=\"stat-value\">{{ member.monthlyOrders }}</text>\r\n                <text class=\"stat-label\">本月订单</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 成员操作按钮 -->\r\n          <view class=\"member-actions-bar\" v-if=\"member.type === 'direct'\">\r\n            <view class=\"action-btn secondary\" @click.stop=\"sendMessage(member)\">\r\n              <svg class=\"btn-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n                <path d=\"M21 15a2 2 0 01-2 2H7l-4 4V5a2 2 0 012-2h14a2 2 0 012 2z\" stroke=\"#AC39FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n              <text>私信</text>\r\n            </view>\r\n            <view class=\"action-btn primary\" @click.stop=\"viewTeamDetail(member)\">\r\n              <svg class=\"btn-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n                <path d=\"M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2M9 11a4 4 0 100-8 4 4 0 000 8zM23 21v-2a4 4 0 00-3-3.87M16 3.13a4 4 0 010 7.75\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n              <text>查看团队</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 空状态 -->\r\n        <view class=\"empty-state\" v-if=\"filteredMembers.length === 0\">\r\n          <view class=\"empty-content\" :style=\"{\r\n            borderRadius: '35px',\r\n            background: '#FFFFFF',\r\n            padding: '80rpx 40rpx',\r\n            textAlign: 'center'\r\n          }\">\r\n            <svg class=\"empty-icon\" viewBox=\"0 0 24 24\" width=\"80\" height=\"80\">\r\n              <path d=\"M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2M9 11a4 4 0 100-8 4 4 0 000 8zM23 21v-2a4 4 0 00-3-3.87M16 3.13a4 4 0 010 7.75\" stroke=\"#C7C7CC\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            </svg>\r\n            <text class=\"empty-text\">暂无{{ teamTabs[currentTab].name }}</text>\r\n            <view class=\"empty-action\" @click=\"showInviteQrcode\" :style=\"{\r\n              background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)',\r\n              borderRadius: '30rpx',\r\n              padding: '16rpx 32rpx',\r\n              marginTop: '40rpx'\r\n            }\">\r\n              <text style=\"color: #FFFFFF;\">邀请好友加入</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 底部安全区域 -->\r\n      <view class=\"safe-area-bottom\"></view>\r\n    </view>\r\n    \r\n    <!-- 邀请二维码弹窗 -->\r\n    <view class=\"invite-modal\" v-if=\"showInviteModal\" @click=\"hideInviteQrcode\">\r\n      <view class=\"modal-content\" @click.stop>\r\n        <view class=\"modal-header\">\r\n          <text class=\"modal-title\">邀请好友加入团队</text>\r\n          <view class=\"close-btn\" @click=\"hideInviteQrcode\">\r\n            <svg viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n              <path d=\"M18 6L6 18M6 6l12 12\" stroke=\"#8E8E93\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            </svg>\r\n          </view>\r\n        </view>\r\n        <view class=\"qrcode-section\">\r\n          <image class=\"qrcode-image\" src=\"https://via.placeholder.com/200\" mode=\"aspectFit\"></image>\r\n          <text class=\"qrcode-tip\">扫描二维码或分享链接邀请好友</text>\r\n        </view>\r\n        <view class=\"share-actions\">\r\n          <view class=\"share-btn\" @click=\"shareToWechat\">\r\n            <text>分享到微信</text>\r\n          </view>\r\n          <view class=\"share-btn\" @click=\"copyInviteLink\">\r\n            <text>复制邀请链接</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue';\r\n\r\n// 页面状态\r\nconst currentTab = ref(0);\r\nconst showInviteModal = ref(false);\r\n\r\n// 团队统计\r\nconst teamStats = ref({\r\n  totalMembers: 28,\r\n  directMembers: 12,\r\n  indirectMembers: 16,\r\n  newMembers: 5\r\n});\r\n\r\n// 团队分类标签\r\nconst teamTabs = ref([\r\n  { name: '全部成员', type: 'all' },\r\n  { name: '直属成员', type: 'direct' },\r\n  { name: '间接成员', type: 'indirect' },\r\n  { name: '本月新增', type: 'new' }\r\n]);\r\n\r\n// 团队成员列表\r\nconst memberList = ref([\r\n  {\r\n    id: 'M001',\r\n    name: '李小明',\r\n    avatar: 'https://via.placeholder.com/80',\r\n    level: '高级分销商',\r\n    joinTime: '2023-12-15',\r\n    type: 'direct',\r\n    isOnline: true,\r\n    orderCount: 45,\r\n    contribution: 1234.56,\r\n    teamSize: 8,\r\n    monthlyOrders: 12,\r\n    isNew: false\r\n  },\r\n  {\r\n    id: 'M002',\r\n    name: '王小红',\r\n    avatar: 'https://via.placeholder.com/80',\r\n    level: '中级分销商',\r\n    joinTime: '2024-01-08',\r\n    type: 'direct',\r\n    isOnline: false,\r\n    orderCount: 28,\r\n    contribution: 856.78,\r\n    teamSize: 3,\r\n    monthlyOrders: 8,\r\n    isNew: true\r\n  },\r\n  {\r\n    id: 'M003',\r\n    name: '张小华',\r\n    avatar: 'https://via.placeholder.com/80',\r\n    level: '初级分销商',\r\n    joinTime: '2024-01-12',\r\n    type: 'indirect',\r\n    isOnline: true,\r\n    orderCount: 15,\r\n    contribution: 345.67,\r\n    teamSize: 0,\r\n    monthlyOrders: 6,\r\n    isNew: true\r\n  },\r\n  {\r\n    id: 'M004',\r\n    name: '刘小强',\r\n    avatar: 'https://via.placeholder.com/80',\r\n    level: '中级分销商',\r\n    joinTime: '2023-11-20',\r\n    type: 'direct',\r\n    isOnline: false,\r\n    orderCount: 32,\r\n    contribution: 967.89,\r\n    teamSize: 5,\r\n    monthlyOrders: 10,\r\n    isNew: false\r\n  }\r\n]);\r\n\r\n// 计算属性\r\nconst filteredMembers = computed(() => {\r\n  const type = teamTabs.value[currentTab.value].type;\r\n  switch(type) {\r\n    case 'all':\r\n      return memberList.value;\r\n    case 'direct':\r\n      return memberList.value.filter(member => member.type === 'direct');\r\n    case 'indirect':\r\n      return memberList.value.filter(member => member.type === 'indirect');\r\n    case 'new':\r\n      return memberList.value.filter(member => member.isNew);\r\n    default:\r\n      return memberList.value;\r\n  }\r\n});\r\n\r\n// 方法\r\nconst navigateBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\nconst switchTab = (index) => {\r\n  currentTab.value = index;\r\n};\r\n\r\nconst getLevelColor = (level) => {\r\n  switch(level) {\r\n    case '高级分销商': return 'linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%)';\r\n    case '中级分销商': return 'linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%)';\r\n    case '初级分销商': return 'linear-gradient(135deg, #45B7D1 0%, #96C93D 100%)';\r\n    default: return 'linear-gradient(135deg, #8E8E93 0%, #A8A8A8 100%)';\r\n  }\r\n};\r\n\r\nconst viewMemberDetail = (member) => {\r\n  uni.navigateTo({\r\n    url: `/subPackages/activity-showcase/pages/distribution/member-detail/index?id=${member.id}`\r\n  });\r\n};\r\n\r\nconst sendMessage = (member) => {\r\n  uni.showToast({\r\n    title: `向${member.name}发送私信`,\r\n    icon: 'none'\r\n  });\r\n};\r\n\r\nconst viewTeamDetail = (member) => {\r\n  uni.navigateTo({\r\n    url: `/subPackages/activity-showcase/pages/distribution/team/detail/index?id=${member.id}`\r\n  });\r\n};\r\n\r\nconst showInviteQrcode = () => {\r\n  showInviteModal.value = true;\r\n};\r\n\r\nconst hideInviteQrcode = () => {\r\n  showInviteModal.value = false;\r\n};\r\n\r\nconst shareToWechat = () => {\r\n  uni.showToast({\r\n    title: '分享到微信功能开发中',\r\n    icon: 'none'\r\n  });\r\n};\r\n\r\nconst copyInviteLink = () => {\r\n  uni.setClipboardData({\r\n    data: 'https://example.com/invite?code=ABC123',\r\n    success: () => {\r\n      uni.showToast({\r\n        title: '邀请链接已复制',\r\n        icon: 'success'\r\n      });\r\n    }\r\n  });\r\n};\r\n\r\nonMounted(() => {\r\n  // 页面加载时的初始化操作\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.team-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100vh;\r\n  background: linear-gradient(180deg, #F2F2F7 0%, #E8E8ED 100%);\r\n}\r\n\r\n.custom-navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: calc(var(--status-bar-height, 25px) + 62px);\r\n  width: 100%;\r\n  z-index: 100;\r\n  \r\n  .navbar-bg {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%);\r\n    backdrop-filter: blur(10px);\r\n    -webkit-backdrop-filter: blur(10px);\r\n    box-shadow: 0 4px 6px rgba(172,57,255,0.15);\r\n  }\r\n  \r\n  .navbar-content {\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: 100%;\r\n    padding: 0 30rpx;\r\n    padding-top: var(--status-bar-height, 25px);\r\n    box-sizing: border-box;\r\n  }\r\n  \r\n  .navbar-title {\r\n    font-size: 36rpx;\r\n    font-weight: 600;\r\n    color: #FFFFFF;\r\n    letter-spacing: 0.5px;\r\n  }\r\n  \r\n  .back-btn, .invite-btn {\r\n    width: 80rpx;\r\n    height: 80rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    border-radius: 50%;\r\n    background: rgba(255, 255, 255, 0.2);\r\n    \r\n    .icon {\r\n      width: 48rpx;\r\n      height: 48rpx;\r\n    }\r\n  }\r\n  \r\n  .navbar-right {\r\n    width: 80rpx;\r\n    display: flex;\r\n    justify-content: flex-end;\r\n  }\r\n}\r\n\r\n.content {\r\n  margin-top: calc(var(--status-bar-height, 25px) + 62px);\r\n  padding: 30rpx;\r\n  flex: 1;\r\n}\r\n\r\n.stats-card {\r\n  .stats-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(4, 1fr);\r\n    gap: 20rpx;\r\n    \r\n    .stat-item {\r\n      text-align: center;\r\n      \r\n      .stat-number {\r\n        display: block;\r\n        font-size: 32rpx;\r\n        font-weight: 600;\r\n        color: #FFFFFF;\r\n        margin-bottom: 8rpx;\r\n      }\r\n      \r\n      .stat-label {\r\n        font-size: 24rpx;\r\n        color: rgba(255, 255, 255, 0.8);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.filter-tabs {\r\n  .tabs-scroll {\r\n    width: 100%;\r\n    \r\n    .tabs-container {\r\n      display: flex;\r\n      white-space: nowrap;\r\n      \r\n      .tab-item {\r\n        position: relative;\r\n        padding: 20rpx 30rpx;\r\n        margin-right: 20rpx;\r\n        \r\n        text {\r\n          font-size: 28rpx;\r\n          color: #8E8E93;\r\n          transition: color 0.3s ease;\r\n        }\r\n        \r\n        .tab-indicator {\r\n          position: absolute;\r\n          bottom: 10rpx;\r\n          left: 50%;\r\n          transform: translateX(-50%);\r\n          width: 40rpx;\r\n          height: 4rpx;\r\n          border-radius: 2rpx;\r\n          background: linear-gradient(90deg, #AC39FF 0%, #B87AFF 100%);\r\n        }\r\n        \r\n        &.active text {\r\n          color: #AC39FF;\r\n          font-weight: 500;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.member-card {\r\n  .member-header {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 20rpx;\r\n    \r\n    .member-avatar-section {\r\n      position: relative;\r\n      margin-right: 20rpx;\r\n      \r\n      .member-avatar {\r\n        width: 100rpx;\r\n        height: 100rpx;\r\n        border-radius: 50%;\r\n        border: 4rpx solid #F0F0F0;\r\n      }\r\n      \r\n      .online-status {\r\n        position: absolute;\r\n        bottom: 8rpx;\r\n        right: 8rpx;\r\n        width: 20rpx;\r\n        height: 20rpx;\r\n        background: #34C759;\r\n        border-radius: 50%;\r\n        border: 3rpx solid #FFFFFF;\r\n      }\r\n    }\r\n    \r\n    .member-basic-info {\r\n      flex: 1;\r\n      \r\n      .member-name-level {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 12rpx;\r\n        \r\n        .member-name {\r\n          font-size: 32rpx;\r\n          font-weight: 600;\r\n          color: #333333;\r\n          margin-right: 16rpx;\r\n        }\r\n      }\r\n      \r\n      .member-meta {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 20rpx;\r\n        \r\n        .join-time, .member-id {\r\n          font-size: 24rpx;\r\n          color: #8E8E93;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .member-actions {\r\n      .arrow-icon {\r\n        width: 32rpx;\r\n        height: 32rpx;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .member-stats {\r\n    .stat-row {\r\n      display: grid;\r\n      grid-template-columns: repeat(4, 1fr);\r\n      gap: 20rpx;\r\n      padding: 20rpx 0;\r\n      border-top: 1rpx solid #F0F0F0;\r\n      border-bottom: 1rpx solid #F0F0F0;\r\n      margin-bottom: 20rpx;\r\n      \r\n      .stat-item {\r\n        text-align: center;\r\n        \r\n        .stat-value {\r\n          display: block;\r\n          font-size: 28rpx;\r\n          font-weight: 600;\r\n          color: #AC39FF;\r\n          margin-bottom: 8rpx;\r\n        }\r\n        \r\n        .stat-label {\r\n          font-size: 22rpx;\r\n          color: #8E8E93;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .member-actions-bar {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    gap: 20rpx;\r\n    \r\n    .action-btn {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8rpx;\r\n      padding: 12rpx 20rpx;\r\n      border-radius: 25rpx;\r\n      \r\n      .btn-icon {\r\n        width: 32rpx;\r\n        height: 32rpx;\r\n      }\r\n      \r\n      text {\r\n        font-size: 26rpx;\r\n        font-weight: 500;\r\n      }\r\n      \r\n      &.primary {\r\n        background: linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%);\r\n        \r\n        text {\r\n          color: #FFFFFF;\r\n        }\r\n      }\r\n      \r\n      &.secondary {\r\n        background: transparent;\r\n        border: 2rpx solid #AC39FF;\r\n        \r\n        text {\r\n          color: #AC39FF;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.empty-state {\r\n  .empty-content {\r\n    .empty-icon {\r\n      margin-bottom: 30rpx;\r\n    }\r\n    \r\n    .empty-text {\r\n      display: block;\r\n      font-size: 28rpx;\r\n      color: #8E8E93;\r\n      margin-bottom: 20rpx;\r\n    }\r\n    \r\n    .empty-action text {\r\n      font-size: 28rpx;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n}\r\n\r\n.invite-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 1000;\r\n  \r\n  .modal-content {\r\n    background: #FFFFFF;\r\n    border-radius: 35px;\r\n    padding: 40rpx;\r\n    margin: 40rpx;\r\n    max-width: 600rpx;\r\n    width: 100%;\r\n    \r\n    .modal-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 40rpx;\r\n      \r\n      .modal-title {\r\n        font-size: 32rpx;\r\n        font-weight: 600;\r\n        color: #333333;\r\n      }\r\n      \r\n      .close-btn {\r\n        width: 60rpx;\r\n        height: 60rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n      }\r\n    }\r\n    \r\n    .qrcode-section {\r\n      text-align: center;\r\n      margin-bottom: 40rpx;\r\n      \r\n      .qrcode-image {\r\n        width: 400rpx;\r\n        height: 400rpx;\r\n        border-radius: 20rpx;\r\n        margin-bottom: 20rpx;\r\n      }\r\n      \r\n      .qrcode-tip {\r\n        font-size: 26rpx;\r\n        color: #8E8E93;\r\n      }\r\n    }\r\n    \r\n    .share-actions {\r\n      display: flex;\r\n      gap: 20rpx;\r\n      \r\n      .share-btn {\r\n        flex: 1;\r\n        text-align: center;\r\n        padding: 20rpx;\r\n        background: linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%);\r\n        border-radius: 30rpx;\r\n        \r\n        text {\r\n          color: #FFFFFF;\r\n          font-size: 28rpx;\r\n          font-weight: 500;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.safe-area-bottom {\r\n  height: env(safe-area-inset-bottom);\r\n}\r\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/distribution/team/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "onMounted"], "mappings": ";;;;;;;;;;AA2NA,UAAM,aAAaA,cAAAA,IAAI,CAAC;AACxB,UAAM,kBAAkBA,cAAAA,IAAI,KAAK;AAGjC,UAAM,YAAYA,cAAAA,IAAI;AAAA,MACpB,cAAc;AAAA,MACd,eAAe;AAAA,MACf,iBAAiB;AAAA,MACjB,YAAY;AAAA,IACd,CAAC;AAGD,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACnB,EAAE,MAAM,QAAQ,MAAM,MAAO;AAAA,MAC7B,EAAE,MAAM,QAAQ,MAAM,SAAU;AAAA,MAChC,EAAE,MAAM,QAAQ,MAAM,WAAY;AAAA,MAClC,EAAE,MAAM,QAAQ,MAAM,MAAO;AAAA,IAC/B,CAAC;AAGD,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACrB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,MAAM;AAAA,QACN,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,UAAU;AAAA,QACV,eAAe;AAAA,QACf,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,MAAM;AAAA,QACN,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,UAAU;AAAA,QACV,eAAe;AAAA,QACf,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,MAAM;AAAA,QACN,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,UAAU;AAAA,QACV,eAAe;AAAA,QACf,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,MAAM;AAAA,QACN,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,UAAU;AAAA,QACV,eAAe;AAAA,QACf,OAAO;AAAA,MACR;AAAA,IACH,CAAC;AAGD,UAAM,kBAAkBC,cAAQ,SAAC,MAAM;AACrC,YAAM,OAAO,SAAS,MAAM,WAAW,KAAK,EAAE;AAC9C,cAAO,MAAI;AAAA,QACT,KAAK;AACH,iBAAO,WAAW;AAAA,QACpB,KAAK;AACH,iBAAO,WAAW,MAAM,OAAO,YAAU,OAAO,SAAS,QAAQ;AAAA,QACnE,KAAK;AACH,iBAAO,WAAW,MAAM,OAAO,YAAU,OAAO,SAAS,UAAU;AAAA,QACrE,KAAK;AACH,iBAAO,WAAW,MAAM,OAAO,YAAU,OAAO,KAAK;AAAA,QACvD;AACE,iBAAO,WAAW;AAAA,MACrB;AAAA,IACH,CAAC;AAGD,UAAM,eAAe,MAAM;AACzBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAEA,UAAM,YAAY,CAAC,UAAU;AAC3B,iBAAW,QAAQ;AAAA,IACrB;AAEA,UAAM,gBAAgB,CAAC,UAAU;AAC/B,cAAO,OAAK;AAAA,QACV,KAAK;AAAS,iBAAO;AAAA,QACrB,KAAK;AAAS,iBAAO;AAAA,QACrB,KAAK;AAAS,iBAAO;AAAA,QACrB;AAAS,iBAAO;AAAA,MACjB;AAAA,IACH;AAEA,UAAM,mBAAmB,CAAC,WAAW;AACnCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4EAA4E,OAAO,EAAE;AAAA,MAC9F,CAAG;AAAA,IACH;AAEA,UAAM,cAAc,CAAC,WAAW;AAC9BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,IAAI,OAAO,IAAI;AAAA,QACtB,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAEA,UAAM,iBAAiB,CAAC,WAAW;AACjCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,0EAA0E,OAAO,EAAE;AAAA,MAC5F,CAAG;AAAA,IACH;AAEA,UAAM,mBAAmB,MAAM;AAC7B,sBAAgB,QAAQ;AAAA,IAC1B;AAEA,UAAM,mBAAmB,MAAM;AAC7B,sBAAgB,QAAQ;AAAA,IAC1B;AAEA,UAAM,gBAAgB,MAAM;AAC1BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAEA,UAAM,iBAAiB,MAAM;AAC3BA,oBAAAA,MAAI,iBAAiB;AAAA,QACnB,MAAM;AAAA,QACN,SAAS,MAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAEAC,kBAAAA,UAAU,MAAM;AAAA,IAEhB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5XD,GAAG,WAAW,eAAe;"}