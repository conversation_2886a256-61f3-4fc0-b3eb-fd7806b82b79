"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const subPackages_merchantAdminMarketing_mixins_distributionMixin = require("../../../mixins/distributionMixin.js");
const SectionHeader = () => "../../../../merchant-admin/components/ui/SectionHeader.js";
const FormGroup = () => "../../../../merchant-admin/components/ui/FormGroup.js";
const IconButton = () => "../../../../merchant-admin/components/ui/IconButton.js";
const SvgIcon = () => "../../../../merchant-admin/components/ui/SvgIcon.js";
const ProgressSteps = () => "../../../../merchant-admin/components/ui/ProgressSteps.js";
const DistributionSetting = () => "../distribution/components/DistributionSetting.js";
const MarketingPromotionActions = () => "../../../components/MarketingPromotionActions.js";
const _sfc_main = {
  components: {
    SectionHeader,
    FormGroup,
    IconButton,
    SvgIcon,
    ProgressSteps,
    DistributionSetting,
    MarketingPromotionActions
  },
  mixins: [subPackages_merchantAdminMarketing_mixins_distributionMixin.distributionMixin],
  // 使用分销混入
  data() {
    return {
      currentStep: 1,
      scrollToId: "step1",
      searchKeyword: "",
      // 表单数据
      formData: {
        // 步骤1：商品信息
        productSelectionType: "existing",
        // existing=选择已有商品, new=新建商品
        selectedProducts: [],
        // 选择的商品列表
        // 步骤2：拼团设置
        groupName: "",
        // 拼团活动名称
        groupType: "normal",
        // normal=普通拼团, package=套餐拼团
        packageName: "",
        // 套餐名称
        packageDescription: "",
        // 套餐描述
        packageItems: [{
          // 套餐商品列表
          name: "",
          originalPrice: "",
          quantity: 1
        }],
        marketPrice: "",
        // 市场价
        regularPrice: "",
        // 日常价
        groupPrice: "",
        // 拼团价格
        requireGroup: true,
        // 是否需要拼团才能享受优惠价
        minGroupSize: 2,
        // 成团人数
        limitPerUser: 1,
        // 单用户限购
        validity: {
          // 有效期
          type: "fixed",
          // fixed=固定时间段, dynamic=动态时间段
          startDate: "",
          // 开始日期
          startTime: "",
          // 开始时间
          endDate: "",
          // 结束日期
          endTime: "",
          // 结束时间
          days: 7
          // 动态时间段的天数
        },
        timeLimit: 24,
        // 成团时限(小时)
        rules: [],
        // 自定义规则
        // 支付和核销设置
        paymentType: "store",
        // online=在线支付, store=到店支付
        verifyType: "store",
        // online=在线核销, store=到店核销
        verificationCode: true,
        // 是否生成核销码
        verificationExpiry: 7,
        // 核销码有效期(天)
        allowPartialVerification: false,
        // 是否允许部分核销
        distributionSettings: {
          enabled: false,
          commissionMode: "percentage",
          commissions: {
            level1: "",
            level2: "",
            level3: ""
          },
          enableLevel3: false
        },
        groupSize: 2,
        // 拼团人数
        groupDuration: 24,
        // 拼团时限(小时)
        purchaseLimit: "不限",
        // 单人限购
        stockLimit: "不限"
        // 活动库存
      },
      // 模拟商品列表
      productList: [
        {
          id: 1,
          name: "iPhone 14 Pro Max",
          price: "8999.00",
          originalPrice: "9999.00",
          stock: 120,
          image: "/static/images/products/iphone.jpg"
        },
        {
          id: 2,
          name: "Apple Watch Series 8",
          price: "3099.00",
          originalPrice: "3299.00",
          stock: 85,
          image: "/static/images/products/watch.jpg"
        },
        {
          id: 3,
          name: "AirPods Pro 2代",
          price: "1799.00",
          originalPrice: "1999.00",
          stock: 200,
          image: "/static/images/products/airpods.jpg"
        },
        {
          id: 4,
          name: "MacBook Air M2",
          price: "7599.00",
          originalPrice: "7999.00",
          stock: 50,
          image: "/static/images/products/macbook.jpg"
        },
        {
          id: 5,
          name: "iPad Pro 2022",
          price: "6299.00",
          originalPrice: "6799.00",
          stock: 75,
          image: "/static/images/products/ipad.jpg"
        }
      ],
      hasMerchantDistribution: false,
      // 商家是否开通分销功能
      tempGroupId: "temp-" + Date.now()
      // 临时ID，实际应该从后端获取
    };
  },
  computed: {
    progressPercentage() {
      return this.currentStep / 3 * 100;
    }
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    showHelp() {
      common_vendor.index.showToast({
        title: "拼团活动帮助",
        icon: "none"
      });
    },
    prevStep() {
      if (this.currentStep > 1) {
        this.currentStep -= 1;
        this.scrollToId = `step${this.currentStep}`;
      }
    },
    nextStep() {
      if (this.currentStep === 1) {
        if (this.formData.productSelectionType === "existing" && this.formData.selectedProducts.length === 0) {
          common_vendor.index.showToast({
            title: "请至少选择一个商品",
            icon: "none"
          });
          return;
        }
      } else if (this.currentStep === 2) {
        if (!this.formData.groupName) {
          common_vendor.index.showToast({ title: "请输入活动名称", icon: "none" });
          return;
        }
        if (this.formData.groupType === "package") {
          if (!this.formData.packageName) {
            common_vendor.index.showToast({ title: "请输入套餐名称", icon: "none" });
            return;
          }
          let hasEmptyItem = false;
          this.formData.packageItems.forEach((item) => {
            if (!item.name || !item.originalPrice) {
              hasEmptyItem = true;
            }
          });
          if (hasEmptyItem) {
            common_vendor.index.showToast({ title: "请完善套餐商品信息", icon: "none" });
            return;
          }
        }
        if (!this.formData.marketPrice) {
          common_vendor.index.showToast({ title: "请输入市场价", icon: "none" });
          return;
        }
        if (!this.formData.regularPrice) {
          common_vendor.index.showToast({ title: "请输入日常价", icon: "none" });
          return;
        }
        if (!this.formData.groupPrice) {
          common_vendor.index.showToast({ title: "请输入拼团价格", icon: "none" });
          return;
        }
        const marketPrice = parseFloat(this.formData.marketPrice);
        const regularPrice = parseFloat(this.formData.regularPrice);
        const groupPrice = parseFloat(this.formData.groupPrice);
        if (marketPrice <= regularPrice) {
          common_vendor.index.showToast({ title: "市场价应高于日常价", icon: "none" });
          return;
        }
        if (regularPrice <= groupPrice) {
          common_vendor.index.showToast({ title: "日常价应高于拼团价", icon: "none" });
          return;
        }
        if (this.formData.validity.type === "fixed") {
          if (!this.formData.validity.startDate || !this.formData.validity.endDate || !this.formData.validity.startTime || !this.formData.validity.endTime) {
            common_vendor.index.showToast({ title: "请选择活动有效期", icon: "none" });
            return;
          }
        }
      }
      if (this.currentStep < 3) {
        this.currentStep += 1;
        this.scrollToId = `step${this.currentStep}`;
      }
    },
    submitForm() {
      common_vendor.index.showLoading({
        title: "创建中..."
      });
      const submitData = {
        // 基本信息
        groupName: this.formData.groupName,
        groupType: this.formData.groupType,
        marketPrice: parseFloat(this.formData.marketPrice),
        regularPrice: parseFloat(this.formData.regularPrice),
        groupPrice: parseFloat(this.formData.groupPrice),
        requireGroup: this.formData.requireGroup,
        minGroupSize: this.formData.minGroupSize,
        limitPerUser: this.formData.limitPerUser,
        timeLimit: this.formData.timeLimit,
        // 有效期
        validityType: this.formData.validity.type,
        validityStartDate: this.formData.validity.startDate,
        validityStartTime: this.formData.validity.startTime,
        validityEndDate: this.formData.validity.endDate,
        validityEndTime: this.formData.validity.endTime,
        validityDays: this.formData.validity.days,
        // 支付和核销设置
        paymentType: this.formData.paymentType,
        verifyType: this.formData.verifyType,
        verificationCode: this.formData.verificationCode,
        verificationExpiry: this.formData.verificationExpiry,
        allowPartialVerification: this.formData.allowPartialVerification
      };
      if (this.formData.groupType === "normal") {
        submitData.products = this.formData.selectedProducts.map((product) => ({
          id: product.id,
          name: product.name,
          price: product.price,
          originalPrice: product.originalPrice,
          image: product.image,
          stock: product.stock
        }));
      } else if (this.formData.groupType === "package") {
        submitData.packageName = this.formData.packageName;
        submitData.packageDescription = this.formData.packageDescription;
        submitData.packageItems = this.formData.packageItems.map((item) => ({
          name: item.name,
          originalPrice: parseFloat(item.originalPrice),
          quantity: parseInt(item.quantity)
        }));
        submitData.totalOriginalPrice = parseFloat(this.calculateTotalOriginalPrice());
        submitData.totalSavings = parseFloat(this.calculateSavings());
        submitData.totalItems = this.getTotalItemQuantity();
      }
      if (this.hasMerchantDistribution && this.formData.distributionSettings.enabled) {
        submitData.distributionSettings = this.formData.distributionSettings;
      }
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-marketing/pages/marketing/group/create.vue:794", "提交数据:", submitData);
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "拼团活动创建成功！",
          icon: "success",
          duration: 2e3
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 2e3);
      }, 1500);
    },
    searchProducts() {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-marketing/pages/marketing/group/create.vue:813", "搜索商品:", this.searchKeyword);
    },
    toggleFilterDropdown(filterType) {
      common_vendor.index.showToast({
        title: `打开${filterType}筛选`,
        icon: "none"
      });
    },
    toggleProductSelection(product) {
      const index = this.formData.selectedProducts.findIndex((p) => p.id === product.id);
      if (index === -1) {
        this.formData.selectedProducts.push(product);
      } else {
        this.formData.selectedProducts.splice(index, 1);
      }
    },
    isProductSelected(product) {
      return this.formData.selectedProducts.findIndex((p) => p.id === product.id) !== -1;
    },
    removeProduct(index) {
      this.formData.selectedProducts.splice(index, 1);
    },
    clearSelectedProducts() {
      this.formData.selectedProducts = [];
    },
    navigateToCreateProduct() {
      common_vendor.index.showToast({
        title: "跳转到创建商品页面",
        icon: "none"
      });
    },
    // 步骤2中的方法
    decrementGroupSize() {
      if (this.formData.minGroupSize > 2) {
        this.formData.minGroupSize -= 1;
      }
    },
    incrementGroupSize() {
      if (this.formData.minGroupSize < 100) {
        this.formData.minGroupSize += 1;
      }
    },
    decrementLimitPerUser() {
      if (this.formData.limitPerUser > 0) {
        this.formData.limitPerUser -= 1;
      }
    },
    incrementLimitPerUser() {
      if (this.formData.limitPerUser < 999) {
        this.formData.limitPerUser += 1;
      }
    },
    // 套餐商品相关方法
    addPackageItem() {
      this.formData.packageItems.push({
        name: "",
        originalPrice: "",
        quantity: 1
      });
    },
    removePackageItem(index) {
      if (this.formData.packageItems.length > 1) {
        this.formData.packageItems.splice(index, 1);
      }
    },
    decrementItemQuantity(index) {
      if (this.formData.packageItems[index].quantity > 1) {
        this.formData.packageItems[index].quantity -= 1;
      }
    },
    incrementItemQuantity(index) {
      if (this.formData.packageItems[index].quantity < 99) {
        this.formData.packageItems[index].quantity += 1;
      }
    },
    decrementValidityDays() {
      if (this.formData.validity.days > 1) {
        this.formData.validity.days -= 1;
      }
    },
    incrementValidityDays() {
      if (this.formData.validity.days < 90) {
        this.formData.validity.days += 1;
      }
    },
    decrementTimeLimit() {
      if (this.formData.timeLimit > 1) {
        this.formData.timeLimit -= 1;
      }
    },
    incrementTimeLimit() {
      if (this.formData.timeLimit < 72) {
        this.formData.timeLimit += 1;
      }
    },
    decrementVerificationExpiry() {
      if (this.formData.verificationExpiry > 1) {
        this.formData.verificationExpiry -= 1;
      }
    },
    incrementVerificationExpiry() {
      if (this.formData.verificationExpiry < 90) {
        this.formData.verificationExpiry += 1;
      }
    },
    dateChange(type, e) {
      this.formData.validity[`${type}Date`] = e.detail.value;
    },
    timeChange(type, e) {
      this.formData.validity[`${type}Time`] = e.detail.value;
    },
    // 套餐计算方法
    getTotalItemQuantity() {
      if (!this.formData.packageItems || this.formData.packageItems.length === 0) {
        return 0;
      }
      return this.formData.packageItems.reduce((sum, item) => sum + parseInt(item.quantity || 0), 0);
    },
    calculateTotalOriginalPrice() {
      if (!this.formData.packageItems || this.formData.packageItems.length === 0) {
        return "0.00";
      }
      const total = this.formData.packageItems.reduce((sum, item) => {
        const price = parseFloat(item.originalPrice || 0);
        const quantity = parseInt(item.quantity || 0);
        return sum + price * quantity;
      }, 0);
      return total.toFixed(2);
    },
    calculateSavings() {
      const totalOriginal = parseFloat(this.calculateTotalOriginalPrice());
      const groupPrice = parseFloat(this.formData.groupPrice || 0);
      const savings = totalOriginal - groupPrice;
      return savings > 0 ? savings.toFixed(2) : "0.00";
    },
    // 更新分销设置
    updateDistributionSettings(settings) {
      this.formData.distributionSettings = settings;
    },
    // 检查商家是否开通分销功能
    checkMerchantDistribution() {
      setTimeout(() => {
        this.hasMerchantDistribution = true;
      }, 500);
    },
    // 保存拼团活动
    async saveGroupActivity() {
      if (this.hasMerchantDistribution && this.formData.distributionSettings.enabled) {
        const success = await this.saveActivityDistributionSettings("group", groupId);
        if (!success) {
          return;
        }
      }
    },
    // 处理推广操作完成事件
    handlePromotionCompleted(data) {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-marketing/pages/marketing/group/create.vue:988", "推广操作完成:", data);
      if (data.action === "publish") {
        common_vendor.index.showToast({
          title: "发布成功",
          icon: "success"
        });
      } else if (data.action === "top") {
        common_vendor.index.showToast({
          title: "置顶成功",
          icon: "success"
        });
      } else if (data.action === "refresh") {
        common_vendor.index.showToast({
          title: "刷新成功",
          icon: "success"
        });
      }
    }
  },
  mounted() {
    this.checkMerchantDistribution();
  }
};
if (!Array) {
  const _component_progress_steps = common_vendor.resolveComponent("progress-steps");
  const _component_section_header = common_vendor.resolveComponent("section-header");
  const _component_svg_icon = common_vendor.resolveComponent("svg-icon");
  const _component_icon_button = common_vendor.resolveComponent("icon-button");
  const _component_form_group = common_vendor.resolveComponent("form-group");
  const _component_distribution_setting = common_vendor.resolveComponent("distribution-setting");
  const _component_MarketingPromotionActions = common_vendor.resolveComponent("MarketingPromotionActions");
  (_component_progress_steps + _component_section_header + _component_svg_icon + _component_icon_button + _component_form_group + _component_distribution_setting + _component_MarketingPromotionActions)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: common_vendor.p({
      steps: ["选择商品", "拼团设置", "确认创建"],
      ["current-step"]: $data.currentStep
    }),
    d: $data.currentStep === 1
  }, $data.currentStep === 1 ? common_vendor.e({
    e: common_vendor.p({
      title: "选择商品方式"
    }),
    f: $data.formData.productSelectionType === "existing"
  }, $data.formData.productSelectionType === "existing" ? {} : {}, {
    g: $data.formData.productSelectionType === "existing" ? 1 : "",
    h: common_vendor.o(($event) => $data.formData.productSelectionType = "existing"),
    i: $data.formData.productSelectionType === "new"
  }, $data.formData.productSelectionType === "new" ? {} : {}, {
    j: $data.formData.productSelectionType === "new" ? 1 : "",
    k: common_vendor.o(($event) => $data.formData.productSelectionType = "new"),
    l: $data.formData.productSelectionType === "existing"
  }, $data.formData.productSelectionType === "existing" ? common_vendor.e({
    m: common_vendor.p({
      name: "search",
      size: "18",
      color: "#999"
    }),
    n: common_vendor.o([($event) => $data.searchKeyword = $event.detail.value, (...args) => $options.searchProducts && $options.searchProducts(...args)]),
    o: $data.searchKeyword,
    p: common_vendor.p({
      name: "arrow-right",
      size: "14",
      color: "#999"
    }),
    q: common_vendor.o(($event) => $options.toggleFilterDropdown("category")),
    r: common_vendor.p({
      name: "arrow-right",
      size: "14",
      color: "#999"
    }),
    s: common_vendor.o(($event) => $options.toggleFilterDropdown("price")),
    t: common_vendor.p({
      name: "arrow-right",
      size: "14",
      color: "#999"
    }),
    v: common_vendor.o(($event) => $options.toggleFilterDropdown("stock")),
    w: common_vendor.f($data.productList, (product, index, i0) => {
      return common_vendor.e({
        a: $options.isProductSelected(product)
      }, $options.isProductSelected(product) ? {} : {}, {
        b: $options.isProductSelected(product) ? 1 : "",
        c: product.image,
        d: common_vendor.t(product.name),
        e: common_vendor.t(product.price),
        f: product.originalPrice
      }, product.originalPrice ? {
        g: common_vendor.t(product.originalPrice)
      } : {}, {
        h: common_vendor.t(product.stock),
        i: index,
        j: common_vendor.o(($event) => $options.toggleProductSelection(product), index)
      });
    }),
    x: $data.formData.selectedProducts.length > 0
  }, $data.formData.selectedProducts.length > 0 ? {
    y: common_vendor.t($data.formData.selectedProducts.length),
    z: common_vendor.o((...args) => $options.clearSelectedProducts && $options.clearSelectedProducts(...args)),
    A: common_vendor.f($data.formData.selectedProducts, (product, index, i0) => {
      return {
        a: product.image,
        b: common_vendor.t(product.name),
        c: common_vendor.t(product.price),
        d: common_vendor.o(($event) => $options.removeProduct(index), index),
        e: index
      };
    })
  } : {}) : {}, {
    B: $data.formData.productSelectionType === "new"
  }, $data.formData.productSelectionType === "new" ? {
    C: common_vendor.o($options.navigateToCreateProduct),
    D: common_vendor.p({
      icon: "plus",
      text: "新建商品",
      type: "primary",
      size: "large"
    })
  } : {}) : {}, {
    E: $data.currentStep === 2
  }, $data.currentStep === 2 ? common_vendor.e({
    F: common_vendor.p({
      title: "拼团活动设置"
    }),
    G: $data.formData.groupName,
    H: common_vendor.o(($event) => $data.formData.groupName = $event.detail.value),
    I: common_vendor.t($data.formData.groupName.length),
    J: common_vendor.p({
      label: "活动名称",
      ["is-required"]: true
    }),
    K: $data.formData.groupType === "normal"
  }, $data.formData.groupType === "normal" ? {} : {}, {
    L: $data.formData.groupType === "normal" ? 1 : "",
    M: common_vendor.o(($event) => $data.formData.groupType = "normal"),
    N: $data.formData.groupType === "package"
  }, $data.formData.groupType === "package" ? {} : {}, {
    O: $data.formData.groupType === "package" ? 1 : "",
    P: common_vendor.o(($event) => $data.formData.groupType = "package"),
    Q: common_vendor.p({
      label: "拼团类型",
      ["is-required"]: true,
      tip: "选择普通拼团或套餐拼团"
    }),
    R: $data.formData.groupType === "package"
  }, $data.formData.groupType === "package" ? {
    S: $data.formData.packageName,
    T: common_vendor.o(($event) => $data.formData.packageName = $event.detail.value),
    U: common_vendor.t($data.formData.packageName.length),
    V: common_vendor.p({
      label: "套餐名称",
      ["is-required"]: true
    }),
    W: $data.formData.packageDescription,
    X: common_vendor.o(($event) => $data.formData.packageDescription = $event.detail.value),
    Y: common_vendor.t($data.formData.packageDescription.length),
    Z: common_vendor.p({
      label: "套餐描述"
    }),
    aa: common_vendor.f($data.formData.packageItems, (item, idx, i0) => {
      return common_vendor.e({
        a: common_vendor.t(idx + 1)
      }, $data.formData.packageItems.length > 1 ? {
        b: common_vendor.o(($event) => $options.removePackageItem(idx), idx)
      } : {}, {
        c: item.name,
        d: common_vendor.o(($event) => item.name = $event.detail.value, idx),
        e: item.originalPrice,
        f: common_vendor.o(($event) => item.originalPrice = $event.detail.value, idx),
        g: common_vendor.o(($event) => $options.decrementItemQuantity(idx), idx),
        h: item.quantity,
        i: common_vendor.o(($event) => item.quantity = $event.detail.value, idx),
        j: common_vendor.o(($event) => $options.incrementItemQuantity(idx), idx),
        k: idx
      });
    }),
    ab: $data.formData.packageItems.length > 1,
    ac: common_vendor.o((...args) => $options.addPackageItem && $options.addPackageItem(...args)),
    ad: common_vendor.p({
      label: "套餐商品",
      ["is-required"]: true,
      tip: "添加组成套餐的商品"
    })
  } : {}, {
    ae: $data.formData.marketPrice,
    af: common_vendor.o(($event) => $data.formData.marketPrice = $event.detail.value),
    ag: common_vendor.p({
      label: "市场价",
      ["is-required"]: true,
      tip: "商品的市场参考价格"
    }),
    ah: $data.formData.regularPrice,
    ai: common_vendor.o(($event) => $data.formData.regularPrice = $event.detail.value),
    aj: common_vendor.p({
      label: "日常价",
      ["is-required"]: true,
      tip: "店铺内商品平时销售的价格"
    }),
    ak: $data.formData.groupPrice,
    al: common_vendor.o(($event) => $data.formData.groupPrice = $event.detail.value),
    am: common_vendor.p({
      label: "拼团价格",
      ["is-required"]: true
    }),
    an: $data.formData.requireGroup,
    ao: common_vendor.o((e) => $data.formData.requireGroup = e.detail.value),
    ap: common_vendor.p({
      label: "开启拼团",
      tip: "开启后需要拼团才能享受优惠价，关闭则无需拼团直接享受优惠"
    }),
    aq: common_vendor.o((...args) => $options.decrementGroupSize && $options.decrementGroupSize(...args)),
    ar: $data.formData.minGroupSize,
    as: common_vendor.o(($event) => $data.formData.minGroupSize = $event.detail.value),
    at: common_vendor.o((...args) => $options.incrementGroupSize && $options.incrementGroupSize(...args)),
    av: common_vendor.p({
      label: "成团人数",
      ["is-required"]: true
    }),
    aw: common_vendor.o((...args) => $options.decrementLimitPerUser && $options.decrementLimitPerUser(...args)),
    ax: $data.formData.limitPerUser,
    ay: common_vendor.o(($event) => $data.formData.limitPerUser = $event.detail.value),
    az: common_vendor.o((...args) => $options.incrementLimitPerUser && $options.incrementLimitPerUser(...args)),
    aA: common_vendor.p({
      label: "单人限购"
    }),
    aB: $data.formData.validity.type === "fixed"
  }, $data.formData.validity.type === "fixed" ? {} : {}, {
    aC: $data.formData.validity.type === "fixed" ? 1 : "",
    aD: common_vendor.o(($event) => $data.formData.validity.type = "fixed"),
    aE: $data.formData.validity.type === "dynamic"
  }, $data.formData.validity.type === "dynamic" ? {} : {}, {
    aF: $data.formData.validity.type === "dynamic" ? 1 : "",
    aG: common_vendor.o(($event) => $data.formData.validity.type = "dynamic"),
    aH: $data.formData.validity.type === "fixed"
  }, $data.formData.validity.type === "fixed" ? common_vendor.e({
    aI: !$data.formData.validity.startDate
  }, !$data.formData.validity.startDate ? {} : {
    aJ: common_vendor.t($data.formData.validity.startDate)
  }, {
    aK: $data.formData.validity.startDate,
    aL: common_vendor.o((e) => $options.dateChange("start", e)),
    aM: !$data.formData.validity.startTime
  }, !$data.formData.validity.startTime ? {} : {
    aN: common_vendor.t($data.formData.validity.startTime)
  }, {
    aO: $data.formData.validity.startTime,
    aP: common_vendor.o((e) => $options.timeChange("start", e)),
    aQ: !$data.formData.validity.endDate
  }, !$data.formData.validity.endDate ? {} : {
    aR: common_vendor.t($data.formData.validity.endDate)
  }, {
    aS: $data.formData.validity.endDate,
    aT: common_vendor.o((e) => $options.dateChange("end", e)),
    aU: !$data.formData.validity.endTime
  }, !$data.formData.validity.endTime ? {} : {
    aV: common_vendor.t($data.formData.validity.endTime)
  }, {
    aW: $data.formData.validity.endTime,
    aX: common_vendor.o((e) => $options.timeChange("end", e))
  }) : {}, {
    aY: $data.formData.validity.type === "dynamic"
  }, $data.formData.validity.type === "dynamic" ? {
    aZ: common_vendor.o((...args) => $options.decrementValidityDays && $options.decrementValidityDays(...args)),
    ba: $data.formData.validity.days,
    bb: common_vendor.o(($event) => $data.formData.validity.days = $event.detail.value),
    bc: common_vendor.o((...args) => $options.incrementValidityDays && $options.incrementValidityDays(...args))
  } : {}, {
    bd: common_vendor.p({
      label: "活动有效期",
      ["is-required"]: true
    }),
    be: common_vendor.o((...args) => $options.decrementTimeLimit && $options.decrementTimeLimit(...args)),
    bf: $data.formData.timeLimit,
    bg: common_vendor.o(($event) => $data.formData.timeLimit = $event.detail.value),
    bh: common_vendor.o((...args) => $options.incrementTimeLimit && $options.incrementTimeLimit(...args)),
    bi: common_vendor.p({
      label: "成团时限",
      ["is-required"]: true
    }),
    bj: $data.formData.paymentType === "store"
  }, $data.formData.paymentType === "store" ? {} : {}, {
    bk: $data.formData.paymentType === "store" ? 1 : "",
    bl: common_vendor.o(($event) => $data.formData.paymentType = "store"),
    bm: $data.formData.paymentType === "online"
  }, $data.formData.paymentType === "online" ? {} : {}, {
    bn: $data.formData.paymentType === "online" ? 1 : "",
    bo: common_vendor.o(($event) => $data.formData.paymentType = "online"),
    bp: common_vendor.p({
      label: "支付方式",
      ["is-required"]: true
    }),
    bq: $data.formData.verifyType === "store"
  }, $data.formData.verifyType === "store" ? {} : {}, {
    br: $data.formData.verifyType === "store" ? 1 : "",
    bs: common_vendor.o(($event) => $data.formData.verifyType = "store"),
    bt: $data.formData.verifyType === "online"
  }, $data.formData.verifyType === "online" ? {} : {}, {
    bv: $data.formData.verifyType === "online" ? 1 : "",
    bw: common_vendor.o(($event) => $data.formData.verifyType = "online"),
    bx: common_vendor.p({
      label: "核销方式",
      ["is-required"]: true
    }),
    by: $data.formData.verificationCode,
    bz: common_vendor.o((e) => $data.formData.verificationCode = e.detail.value),
    bA: common_vendor.p({
      label: "生成核销码",
      ["is-required"]: true
    }),
    bB: common_vendor.o((...args) => $options.decrementVerificationExpiry && $options.decrementVerificationExpiry(...args)),
    bC: $data.formData.verificationExpiry,
    bD: common_vendor.o(($event) => $data.formData.verificationExpiry = $event.detail.value),
    bE: common_vendor.o((...args) => $options.incrementVerificationExpiry && $options.incrementVerificationExpiry(...args)),
    bF: common_vendor.p({
      label: "核销码有效期",
      ["is-required"]: true
    }),
    bG: $data.formData.allowPartialVerification,
    bH: common_vendor.o((e) => $data.formData.allowPartialVerification = e.detail.value),
    bI: common_vendor.p({
      label: "允许部分核销"
    }),
    bJ: $data.hasMerchantDistribution
  }, $data.hasMerchantDistribution ? {
    bK: common_vendor.o($options.updateDistributionSettings),
    bL: common_vendor.p({
      ["initial-settings"]: $data.formData.distributionSettings
    }),
    bM: common_vendor.p({
      label: "分销设置"
    })
  } : {}) : {}, {
    bN: $data.currentStep === 3
  }, $data.currentStep === 3 ? {
    bO: common_vendor.p({
      title: "确认拼团信息"
    }),
    bP: common_vendor.t($data.formData.groupName || "拼团活动名称"),
    bQ: common_vendor.t($data.formData.startDate),
    bR: common_vendor.t($data.formData.endDate),
    bS: common_vendor.f($data.formData.selectedProducts, (product, index, i0) => {
      return {
        a: product.image,
        b: common_vendor.t(product.name),
        c: common_vendor.t(product.groupPrice || product.price),
        d: common_vendor.t(product.originalPrice || product.price),
        e: index
      };
    }),
    bT: common_vendor.t($data.formData.groupSize),
    bU: common_vendor.t($data.formData.groupDuration),
    bV: common_vendor.t($data.formData.purchaseLimit || "不限"),
    bW: common_vendor.t($data.formData.stockLimit || "不限"),
    bX: common_vendor.o($options.handlePromotionCompleted),
    bY: common_vendor.p({
      ["activity-type"]: "group",
      ["activity-id"]: $data.tempGroupId,
      ["publish-mode-only"]: true,
      ["show-actions"]: ["publish"]
    }),
    bZ: common_vendor.p({
      label: "活动推广",
      tip: "发布、置顶或刷新活动，提升曝光率"
    }),
    ca: common_vendor.o((...args) => $options.prevStep && $options.prevStep(...args)),
    cb: common_vendor.o((...args) => _ctx.submitGroupActivity && _ctx.submitGroupActivity(...args))
  } : {}, {
    cc: $data.scrollToId,
    cd: $data.currentStep > 1
  }, $data.currentStep > 1 ? {
    ce: common_vendor.o((...args) => $options.prevStep && $options.prevStep(...args))
  } : {}, {
    cf: $data.currentStep < 3
  }, $data.currentStep < 3 ? {
    cg: common_vendor.o((...args) => $options.nextStep && $options.nextStep(...args))
  } : {}, {
    ch: $data.currentStep === 3
  }, $data.currentStep === 3 ? {
    ci: common_vendor.o((...args) => $options.submitForm && $options.submitForm(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/group/create.js.map
