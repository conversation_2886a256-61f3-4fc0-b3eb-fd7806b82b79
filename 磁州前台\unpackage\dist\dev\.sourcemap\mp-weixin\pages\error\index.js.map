{"version": 3, "file": "index.js", "sources": ["pages/error/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZXJyb3IvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"error-page\">\r\n\t\t<!-- 头部背景 -->\r\n\t\t<view class=\"error-header-bg\"></view>\r\n\t\t\r\n\t\t<!-- 错误图标 -->\r\n\t\t<view class=\"error-icon-container\">\r\n\t\t\t<image class=\"error-icon\" src=\"/static/images/error.png\" mode=\"aspectFit\"></image>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 错误信息 -->\r\n\t\t<view class=\"error-content\">\r\n\t\t\t<text class=\"error-title\">{{errorTitle}}</text>\r\n\t\t\t<text class=\"error-message\">{{errorMessage}}</text>\r\n\t\t\t<text class=\"error-details\" v-if=\"showDetails\">{{errorDetails}}</text>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 操作按钮 -->\r\n\t\t<view class=\"error-actions\">\r\n\t\t\t<button class=\"action-btn primary\" @click=\"goHome\">返回首页</button>\r\n\t\t\t<button class=\"action-btn secondary\" @click=\"goBack\">返回上页</button>\r\n\t\t\t<button class=\"action-btn tertiary\" @click=\"toggleDetails\">\r\n\t\t\t\t{{showDetails ? '隐藏' : '显示'}}详情\r\n\t\t\t</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\terrorCode: '',\r\n\t\t\terrorMessage: '抱歉，应用遇到了一些问题',\r\n\t\t\terrorDetails: '',\r\n\t\t\tfromPage: '',\r\n\t\t\tshowDetails: false,\r\n\t\t\terrorType: 'general' // general, network, permission, auth\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\terrorTitle() {\r\n\t\t\tconst titles = {\r\n\t\t\t\tgeneral: '应用错误',\r\n\t\t\t\tnetwork: '网络错误',\r\n\t\t\t\tpermission: '权限错误',\r\n\t\t\t\tauth: '授权错误'\r\n\t\t\t}\r\n\t\t\treturn titles[this.errorType] || '应用错误'\r\n\t\t}\r\n\t},\r\n\tonLoad(options) {\r\n\t\t// 获取错误信息\r\n\t\tif (options.error) {\r\n\t\t\tthis.errorMessage = decodeURIComponent(options.error) || this.errorMessage\r\n\t\t}\r\n\t\t\r\n\t\tif (options.details) {\r\n\t\t\tthis.errorDetails = decodeURIComponent(options.details)\r\n\t\t}\r\n\t\t\r\n\t\tif (options.from) {\r\n\t\t\tthis.fromPage = decodeURIComponent(options.from)\r\n\t\t}\r\n\t\t\r\n\t\tif (options.type) {\r\n\t\t\tthis.errorType = options.type\r\n\t\t}\r\n\t\t\r\n\t\tif (options.code) {\r\n\t\t\tthis.errorCode = options.code\r\n\t\t}\r\n\t\t\r\n\t\t// 记录错误日志\r\n\t\tconsole.error('Error page loaded:', {\r\n\t\t\tmessage: this.errorMessage,\r\n\t\t\tdetails: this.errorDetails,\r\n\t\t\tfrom: this.fromPage,\r\n\t\t\ttype: this.errorType,\r\n\t\t\tcode: this.errorCode\r\n\t\t})\r\n\t},\r\n\tmethods: {\r\n\t\t// 返回首页\r\n\t\tgoHome() {\r\n\t\t\tuni.switchTab({\r\n\t\t\t\turl: '/pages/index/index'\r\n\t\t\t})\r\n\t\t},\r\n\t\t\r\n\t\t// 返回上一页\r\n\t\tgoBack() {\r\n\t\t\t// 如果有来源页面且不是当前错误页面，则返回\r\n\t\t\tif (this.fromPage && this.fromPage !== 'pages/error/index') {\r\n\t\t\t\t// 尝试返回到来源页面\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 检查是否可以直接跳转\r\n\t\t\t\t\tif (this.fromPage.startsWith('/pages/')) {\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: this.fromPage,\r\n\t\t\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\t\t\t// 如果跳转失败，直接返回上一页\r\n\t\t\t\t\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\t\t\t\t\tdelta: 1\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 默认返回上一页\r\n\t\t\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\t\t\tdelta: 1\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\t// 出错时返回首页\r\n\t\t\t\t\tthis.goHome()\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\t// 没有来源页面时，返回上一页\r\n\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\tdelta: 1,\r\n\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\t// 如果无法返回上一页，则回到首页\r\n\t\t\t\t\t\tthis.goHome()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 切换显示详情\r\n\t\ttoggleDetails() {\r\n\t\t\tthis.showDetails = !this.showDetails\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\n.error-page {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tmin-height: 100vh;\r\n\tbackground-color: #f5f7fa;\r\n\tposition: relative;\r\n\tpadding: 0 30rpx;\r\n}\r\n\r\n.error-header-bg {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 40vh;\r\n\tbackground: linear-gradient(to bottom, #0066FF, #409EFF);\r\n\tborder-radius: 0 0 50rpx 50rpx;\r\n\tz-index: 0;\r\n}\r\n\r\n.error-icon-container {\r\n\tmargin-top: 15vh;\r\n\tz-index: 1;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\twidth: 240rpx;\r\n\theight: 240rpx;\r\n\tbackground-color: rgba(255, 255, 255, 0.9);\r\n\tborder-radius: 50%;\r\n\tbox-shadow: 0 8rpx 24rpx rgba(0, 102, 255, 0.15);\r\n}\r\n\r\n.error-icon {\r\n\twidth: 160rpx;\r\n\theight: 160rpx;\r\n}\r\n\r\n.error-content {\r\n\twidth: 100%;\r\n\tpadding: 40rpx;\r\n\tmargin-top: 60rpx;\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 24rpx;\r\n\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\r\n\tz-index: 1;\r\n\ttext-align: center;\r\n}\r\n\r\n.error-title {\r\n\tfont-size: 40rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333333;\r\n\tmargin-bottom: 20rpx;\r\n\tdisplay: block;\r\n}\r\n\r\n.error-message {\r\n\tfont-size: 30rpx;\r\n\tcolor: #666666;\r\n\tline-height: 1.5;\r\n\tmargin-bottom: 20rpx;\r\n\tdisplay: block;\r\n}\r\n\r\n.error-details {\r\n\tfont-size: 26rpx;\r\n\tcolor: #999999;\r\n\tbackground-color: #f7f7f7;\r\n\tpadding: 20rpx;\r\n\tborder-radius: 12rpx;\r\n\ttext-align: left;\r\n\tword-break: break-word;\r\n\tmargin-top: 20rpx;\r\n\tdisplay: block;\r\n\tmax-height: 300rpx;\r\n\toverflow-y: auto;\r\n}\r\n\r\n.error-actions {\r\n\twidth: 100%;\r\n\tmargin-top: 40rpx;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tz-index: 1;\r\n}\r\n\r\n.action-btn {\r\n\twidth: 80%;\r\n\tmargin-bottom: 20rpx;\r\n\theight: 80rpx;\r\n\tline-height: 80rpx;\r\n\tfont-size: 30rpx;\r\n\tborder-radius: 40rpx;\r\n}\r\n\r\n.primary {\r\n\tbackground-color: #0066FF;\r\n\tcolor: #FFFFFF;\r\n}\r\n\r\n.secondary {\r\n\tbackground-color: #FFFFFF;\r\n\tcolor: #0066FF;\r\n\tborder: 1rpx solid #0066FF;\r\n}\r\n\r\n.tertiary {\r\n\tbackground-color: transparent;\r\n\tcolor: #666666;\r\n\tfont-size: 26rpx;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/error/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AA6BA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,WAAW;AAAA,MACX,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU;AAAA,MACV,aAAa;AAAA,MACb,WAAW;AAAA;AAAA,IACZ;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,aAAa;AACZ,YAAM,SAAS;AAAA,QACd,SAAS;AAAA,QACT,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,MAAM;AAAA,MACP;AACA,aAAO,OAAO,KAAK,SAAS,KAAK;AAAA,IAClC;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AAEf,QAAI,QAAQ,OAAO;AAClB,WAAK,eAAe,mBAAmB,QAAQ,KAAK,KAAK,KAAK;AAAA,IAC/D;AAEA,QAAI,QAAQ,SAAS;AACpB,WAAK,eAAe,mBAAmB,QAAQ,OAAO;AAAA,IACvD;AAEA,QAAI,QAAQ,MAAM;AACjB,WAAK,WAAW,mBAAmB,QAAQ,IAAI;AAAA,IAChD;AAEA,QAAI,QAAQ,MAAM;AACjB,WAAK,YAAY,QAAQ;AAAA,IAC1B;AAEA,QAAI,QAAQ,MAAM;AACjB,WAAK,YAAY,QAAQ;AAAA,IAC1B;AAGAA,kBAAAA,MAAc,MAAA,SAAA,+BAAA,sBAAsB;AAAA,MACnC,SAAS,KAAK;AAAA,MACd,SAAS,KAAK;AAAA,MACd,MAAM,KAAK;AAAA,MACX,MAAM,KAAK;AAAA,MACX,MAAM,KAAK;AAAA,KACX;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAER,SAAS;AACRA,oBAAAA,MAAI,UAAU;AAAA,QACb,KAAK;AAAA,OACL;AAAA,IACD;AAAA;AAAA,IAGD,SAAS;AAER,UAAI,KAAK,YAAY,KAAK,aAAa,qBAAqB;AAE3D,YAAI;AAEH,cAAI,KAAK,SAAS,WAAW,SAAS,GAAG;AACxCA,0BAAAA,MAAI,WAAW;AAAA,cACd,KAAK,KAAK;AAAA,cACV,MAAM,MAAM;AAEXA,8BAAAA,MAAI,aAAa;AAAA,kBAChB,OAAO;AAAA,iBACP;AAAA,cACF;AAAA,aACA;AAAA,iBACK;AAENA,0BAAAA,MAAI,aAAa;AAAA,cAChB,OAAO;AAAA,aACP;AAAA,UACF;AAAA,QACD,SAAS,GAAG;AAEX,eAAK,OAAO;AAAA,QACb;AAAA,aACM;AAENA,sBAAAA,MAAI,aAAa;AAAA,UAChB,OAAO;AAAA,UACP,MAAM,MAAM;AAEX,iBAAK,OAAO;AAAA,UACb;AAAA,SACA;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,gBAAgB;AACf,WAAK,cAAc,CAAC,KAAK;AAAA,IAC1B;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;ACrIA,GAAG,WAAW,eAAe;"}