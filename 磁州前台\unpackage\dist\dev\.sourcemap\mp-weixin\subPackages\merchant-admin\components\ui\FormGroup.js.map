{"version": 3, "file": "FormGroup.js", "sources": ["subPackages/merchant-admin/components/ui/FormGroup.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7Avc3ViUGFja2FnZXMvbWVyY2hhbnQtYWRtaW4vY29tcG9uZW50cy91aS9Gb3JtR3JvdXAudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"form-group\">\r\n    <view class=\"form-label\">\r\n      <text class=\"label-text\" :class=\"{ required: isRequired }\">{{ label }}</text>\r\n    </view>\r\n    <view class=\"form-field\">\r\n      <slot></slot>\r\n      <text class=\"form-tip\" v-if=\"tip\">{{ tip }}</text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'FormGroup',\r\n  props: {\r\n    label: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    isRequired: {\r\n      type: <PERSON>olean,\r\n      default: false\r\n    },\r\n    tip: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.form-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.form-label {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.label-text {\r\n  font-size: 15px;\r\n  font-weight: 500;\r\n  color: #333;\r\n  \r\n  &.required::after {\r\n    content: ' *';\r\n    color: #FF3B30;\r\n  }\r\n}\r\n\r\n.form-field {\r\n  position: relative;\r\n}\r\n\r\n.form-tip {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-top: 4px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 基础表单输入框样式 */\r\n.form-input {\r\n  width: 100%;\r\n  height: 44px;\r\n  background-color: #F8FAFC;\r\n  border-radius: 8px;\r\n  padding: 0 15px;\r\n  font-size: 15px;\r\n  color: #333;\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* 价格输入框样式 */\r\n.price-input-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  background-color: #F8FAFC;\r\n  border-radius: 8px;\r\n  padding: 0 15px;\r\n  height: 44px;\r\n}\r\n\r\n.price-symbol {\r\n  font-size: 16px;\r\n  color: #333;\r\n  margin-right: 5px;\r\n}\r\n\r\n.price-input {\r\n  flex: 1;\r\n  height: 44px;\r\n  font-size: 15px;\r\n  background-color: transparent;\r\n  padding: 0;\r\n}\r\n\r\n/* 数字选择器样式 */\r\n.number-picker {\r\n  display: flex;\r\n  align-items: center;\r\n  height: 44px;\r\n}\r\n\r\n.number-btn {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 4px;\r\n  background-color: #F0F0F0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: #666;\r\n}\r\n\r\n.number-input {\r\n  width: 60px;\r\n  height: 32px;\r\n  text-align: center;\r\n  margin: 0 8px;\r\n  background-color: #F8FAFC;\r\n  border-radius: 4px;\r\n  font-size: 15px;\r\n}\r\n\r\n.unit-text {\r\n  margin-left: 8px;\r\n  color: #666;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 单选按钮组样式 */\r\n.radio-group {\r\n  display: flex;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.radio-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-right: 24px;\r\n  \r\n  &.active .radio-label {\r\n    color: #0A84FF;\r\n  }\r\n}\r\n\r\n.radio-dot {\r\n  width: 18px;\r\n  height: 18px;\r\n  border-radius: 50%;\r\n  border: 1px solid #D1D1D6;\r\n  margin-right: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  \r\n  .active & {\r\n    border-color: #0A84FF;\r\n  }\r\n}\r\n\r\n.radio-dot-inner {\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 50%;\r\n  background-color: #0A84FF;\r\n}\r\n\r\n.radio-label {\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin/components/ui/FormGroup.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;AAaA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,IACX;AAAA,IACD,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,KAAK;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AACF;;;;;;;;;;;AC5BA,GAAG,gBAAgB,SAAS;"}