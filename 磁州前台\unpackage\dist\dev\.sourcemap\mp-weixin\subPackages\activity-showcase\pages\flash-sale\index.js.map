{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/flash-sale/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcZmxhc2gtc2FsZVxpbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"flash-sale-page\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\">\r\n      <view class=\"navbar-bg\"></view>\r\n      <view class=\"navbar-content\">\r\n        <view class=\"back-btn\" @click=\"goBack\">\r\n          <image class=\"back-icon\" src=\"/static/images/tabbar/最新返回键.png\" mode=\"aspectFit\"></image>\r\n        </view>\r\n        <view class=\"navbar-title\">限时秒杀</view>\r\n        <view class=\"navbar-right\">\r\n          <view class=\"close-btn\" @click=\"goBack\">\r\n            <svg class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" width=\"22\" height=\"22\">\r\n              <path d=\"M512 421.490332L331.349941 240.840273c-24.988383-24.988383-65.35828-24.988383-90.346664 0-24.988383 24.988383-24.988383 65.35828 0 90.346664L421.653336 512 240.840273 692.812059c-24.988383 24.988383-24.988383 65.35828 0 90.346664 24.988383 24.988383 65.35828 24.988383 90.346664 0L512 602.509668l180.650059 180.650059c24.988383 24.988383 65.35828 24.988383 90.346664 0 24.988383-24.988383 24.988383-65.35828 0-90.346664L602.346664 512l180.813063-180.812059c24.988383-24.988383 24.988383-65.35828 0-90.346664-24.988383-24.988383-65.35828-24.988383-90.346664 0L512 421.490332z\" fill=\"#FFFFFF\"></path>\r\n            </svg>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 倒计时区域 -->\r\n    <view class=\"countdown-section\">\r\n      <view class=\"countdown-header\">\r\n        <view class=\"current-round\">\r\n          <text class=\"round-label\">{{ currentRound.label }}</text>\r\n          <text class=\"round-status\">{{ currentRound.status }}</text>\r\n        </view>\r\n        <view class=\"countdown-wrapper\">\r\n          <countdown-timer \r\n            :end-time=\"currentRound.endTime\" \r\n            type=\"flash\"\r\n            @countdown-end=\"onCountdownEnd\"\r\n          ></countdown-timer>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 场次选择 -->\r\n      <scroll-view class=\"round-scroll\" scroll-x show-scrollbar=\"false\">\r\n        <view class=\"round-list\">\r\n          <view \r\n            class=\"round-item\" \r\n            v-for=\"(round, index) in rounds\" \r\n            :key=\"index\"\r\n            :class=\"{ active: currentRoundIndex === index }\"\r\n            @click=\"switchRound(index)\"\r\n          >\r\n            <text class=\"round-time\">{{ round.time }}</text>\r\n            <text class=\"round-status\">{{ round.status }}</text>\r\n          </view>\r\n        </view>\r\n      </scroll-view>\r\n    </view>\r\n    \r\n    <!-- 商品列表 -->\r\n    <scroll-view class=\"content-scroll\" scroll-y\r\n      @scrolltolower=\"loadMore\"\r\n      @scroll=\"handleScroll\">\r\n      <view class=\"products-list\">\r\n        <view \r\n          class=\"product-item\" \r\n          v-for=\"(product, index) in products\" \r\n          :key=\"index\"\r\n          @click=\"navigateToDetail(product.id)\"\r\n        >\r\n          <view class=\"product-image-container\">\r\n            <image class=\"product-image\" :src=\"product.coverImage || product.image\" mode=\"aspectFill\"></image>\r\n            <view class=\"product-tag\" v-if=\"product.tag\">{{ product.tag }}</view>\r\n          </view>\r\n          <view class=\"product-info\">\r\n            <view class=\"product-title\">{{ product.name || product.title }}</view>\r\n            <view class=\"product-desc\">{{ product.description }}</view>\r\n            <view class=\"product-price-row\">\r\n              <view class=\"current-price\">\r\n                <text class=\"price-symbol\">¥</text>\r\n                <text class=\"price-value\">{{ product.flashPrice || product.currentPrice }}</text>\r\n              </view>\r\n              <view class=\"original-price\">¥{{ product.originalPrice }}</view>\r\n            </view>\r\n            <view class=\"product-progress\">\r\n              <view class=\"progress-text\">\r\n                <text>已售{{ product.soldCount }}件</text>\r\n                <text>共{{ product.stockTotal || product.totalCount }}件</text>\r\n              </view>\r\n              <view class=\"progress-bar\">\r\n                <view \r\n                  class=\"progress-filled\" \r\n                  :style=\"{width: `${Math.min(100, (product.soldCount / (product.stockTotal || product.totalCount)) * 100)}%`}\"\r\n                ></view>\r\n              </view>\r\n            </view>\r\n            <view class=\"product-btn\">\r\n              <text>立即抢购</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 加载更多提示 -->\r\n      <view class=\"loading-more\" v-if=\"loading\">\r\n        <text>加载中...</text>\r\n      </view>\r\n      \r\n      <!-- 到底了提示 -->\r\n      <view class=\"no-more\" v-if=\"noMore\">\r\n        <text>已经到底啦~</text>\r\n      </view>\r\n      \r\n      <!-- 空状态 -->\r\n      <view class=\"empty-state\" v-if=\"products.length === 0 && !loading\">\r\n        <image class=\"empty-image\" src=\"/static/images/empty-state.png\" mode=\"aspectFit\"></image>\r\n        <text class=\"empty-text\">暂无秒杀活动</text>\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport CountdownTimer from '../../components/CountdownTimer.vue'\r\nimport { ActivityService, ActivityType } from '@/common/services/ActivityService.js'\r\n\r\nexport default {\r\n  components: {\r\n    CountdownTimer\r\n  },\r\n  data() {\r\n    return {\r\n      refreshing: false,\r\n      loading: false,\r\n      noMore: false,\r\n      scrollTop: 0,\r\n      currentRoundIndex: 0,\r\n      rounds: [\r\n        { \r\n          time: '08:00', \r\n          status: '已开抢', \r\n          label: '08:00场',\r\n          endTime: new Date(new Date().setHours(10, 0, 0, 0)).toISOString()\r\n        },\r\n        { \r\n          time: '10:00', \r\n          status: '抢购中', \r\n          label: '10:00场',\r\n          endTime: new Date(new Date().setHours(12, 0, 0, 0)).toISOString()\r\n        },\r\n        { \r\n          time: '12:00', \r\n          status: '即将开始', \r\n          label: '12:00场',\r\n          endTime: new Date(new Date().setHours(14, 0, 0, 0)).toISOString()\r\n        },\r\n        { \r\n          time: '14:00', \r\n          status: '即将开始', \r\n          label: '14:00场',\r\n          endTime: new Date(new Date().setHours(16, 0, 0, 0)).toISOString()\r\n        },\r\n        { \r\n          time: '16:00', \r\n          status: '即将开始', \r\n          label: '16:00场',\r\n          endTime: new Date(new Date().setHours(18, 0, 0, 0)).toISOString()\r\n        },\r\n        { \r\n          time: '18:00', \r\n          status: '即将开始', \r\n          label: '18:00场',\r\n          endTime: new Date(new Date().setHours(20, 0, 0, 0)).toISOString()\r\n        },\r\n        { \r\n          time: '20:00', \r\n          status: '即将开始', \r\n          label: '20:00场',\r\n          endTime: new Date(new Date().setHours(22, 0, 0, 0)).toISOString()\r\n        }\r\n      ],\r\n      products: [],\r\n      page: 1,\r\n      pageSize: 10\r\n    }\r\n  },\r\n  computed: {\r\n    currentRound() {\r\n      return this.rounds[this.currentRoundIndex]\r\n    }\r\n  },\r\n  onLoad() {\r\n    // 页面加载时获取数据\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n    // 监听滚动事件\r\n    handleScroll(e) {\r\n      this.scrollTop = e.detail.scrollTop;\r\n    },\r\n    \r\n    // 返回上一页\r\n    goBack() {\r\n      uni.navigateBack()\r\n    },\r\n    \r\n    // 切换场次\r\n    switchRound(index) {\r\n      this.currentRoundIndex = index\r\n      this.products = [] // 清空当前产品列表\r\n      this.page = 1 // 重置页码\r\n      this.noMore = false // 重置加载状态\r\n      this.fetchData() // 获取新场次的数据\r\n    },\r\n    \r\n    // 倒计时结束\r\n    onCountdownEnd() {\r\n      // 更新状态或获取下一场数据\r\n      uni.showToast({\r\n        title: '当前场次已结束',\r\n        icon: 'none'\r\n      })\r\n      \r\n      // 自动切换到下一场\r\n      if (this.currentRoundIndex < this.rounds.length - 1) {\r\n        this.currentRoundIndex++\r\n        this.products = [] // 清空当前产品列表\r\n        this.page = 1 // 重置页码\r\n        this.noMore = false // 重置加载状态\r\n        this.fetchData() // 获取新场次的数据\r\n      }\r\n    },\r\n    \r\n    // 加载更多\r\n    loadMore() {\r\n      if (this.loading || this.noMore) return\r\n      \r\n      this.loading = true\r\n      this.page++\r\n      \r\n      // 获取更多数据\r\n      this.fetchMoreData()\r\n    },\r\n    \r\n    // 获取数据\r\n    fetchData() {\r\n      this.loading = true\r\n      \r\n      try {\r\n        // 使用ActivityService获取秒杀活动数据\r\n        const flashSaleActivities = ActivityService.getActivities({\r\n          type: ActivityType.FLASH,\r\n          onlyPublished: true,\r\n          sortByEndTime: true,\r\n          page: this.page,\r\n          pageSize: this.pageSize\r\n        });\r\n        \r\n        if (flashSaleActivities && flashSaleActivities.length > 0) {\r\n          this.products = flashSaleActivities;\r\n          this.loading = false;\r\n        } else {\r\n          // 如果没有数据，可以使用默认数据或显示空状态\r\n          this.products = [];\r\n          this.noMore = true;\r\n          this.loading = false;\r\n        }\r\n      } catch (error) {\r\n        console.error('获取秒杀活动数据失败:', error);\r\n        this.loading = false;\r\n        uni.showToast({\r\n          title: '获取数据失败',\r\n          icon: 'none'\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 获取更多数据\r\n    fetchMoreData() {\r\n      try {\r\n        // 使用ActivityService获取更多秒杀活动数据\r\n        const moreActivities = ActivityService.getActivities({\r\n          type: ActivityType.FLASH,\r\n          onlyPublished: true,\r\n          sortByEndTime: true,\r\n          page: this.page,\r\n          pageSize: this.pageSize\r\n        });\r\n        \r\n        if (moreActivities && moreActivities.length > 0) {\r\n          this.products = [...this.products, ...moreActivities];\r\n          this.loading = false;\r\n        } else {\r\n          // 没有更多数据\r\n          this.noMore = true;\r\n          this.loading = false;\r\n        }\r\n      } catch (error) {\r\n        console.error('获取更多秒杀活动数据失败:', error);\r\n        this.loading = false;\r\n        uni.showToast({\r\n          title: '获取更多数据失败',\r\n          icon: 'none'\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 导航到详情页\r\n    navigateToDetail(id) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/activity-showcase/pages/flash-sale/detail?id=${id}`\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.flash-sale-page {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  background-color: #F2F2F7;\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.custom-navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: calc(var(--status-bar-height, 25px) + 62px);\r\n  width: 100%;\r\n  z-index: 100;\r\n  \r\n  .navbar-bg {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);\r\n  }\r\n  \r\n  .navbar-content {\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: 100%;\r\n    padding-top: var(--status-bar-height, 25px);\r\n    padding-left: 30rpx;\r\n    padding-right: 30rpx;\r\n    box-sizing: border-box;\r\n    \r\n    .back-btn {\r\n      width: 40rpx;\r\n      height: 40rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n    }\r\n    \r\n    .back-icon {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n    \r\n    .navbar-title {\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n      color: #FFFFFF;\r\n      position: absolute;\r\n      left: 50%;\r\n      transform: translateX(-50%);\r\n    }\r\n    \r\n    .navbar-right {\r\n      width: 80rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: flex-end;\r\n      \r\n      .close-btn {\r\n        width: 64rpx;\r\n        height: 64rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 倒计时区域 */\r\n.countdown-section {\r\n  margin-top: calc(var(--status-bar-height, 25px) + 62px);\r\n  background-color: #FFFFFF;\r\n  padding: 20rpx 30rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);\r\n  \r\n  .countdown-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 20rpx;\r\n    \r\n    .current-round {\r\n      .round-label {\r\n        font-size: 32rpx;\r\n        font-weight: 600;\r\n        color: #333333;\r\n        margin-right: 12rpx;\r\n      }\r\n      \r\n      .round-status {\r\n        font-size: 24rpx;\r\n        color: #FF3B30;\r\n        background-color: rgba(255, 59, 48, 0.1);\r\n        padding: 4rpx 12rpx;\r\n        border-radius: 12rpx;\r\n      }\r\n    }\r\n    \r\n    .countdown-wrapper {\r\n      // 样式由组件自身提供\r\n    }\r\n  }\r\n  \r\n  .round-scroll {\r\n    width: 100%;\r\n    white-space: nowrap;\r\n    \r\n    .round-list {\r\n      display: inline-flex;\r\n      padding: 10rpx 0;\r\n      \r\n      .round-item {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        padding: 16rpx 30rpx;\r\n        margin-right: 20rpx;\r\n        border-radius: 16rpx;\r\n        background-color: #F2F2F7;\r\n        \r\n        &.active {\r\n          background: linear-gradient(135deg, #FF5A5F 0%, #FF3B30 100%);\r\n          \r\n          .round-time, .round-status {\r\n            color: #FFFFFF;\r\n          }\r\n        }\r\n        \r\n        .round-time {\r\n          font-size: 28rpx;\r\n          font-weight: 600;\r\n          color: #333333;\r\n          margin-bottom: 6rpx;\r\n        }\r\n        \r\n        .round-status {\r\n          font-size: 22rpx;\r\n          color: #666666;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 商品列表 */\r\n.content-scroll {\r\n  flex: 1;\r\n  width: 100%;\r\n}\r\n\r\n.products-list {\r\n  padding: 20rpx;\r\n}\r\n\r\n.product-item {\r\n  display: flex;\r\n  margin-bottom: 20rpx;\r\n  padding: 20rpx;\r\n  background-color: #FFFFFF;\r\n  border-radius: 16rpx;\r\n  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);\r\n  \r\n  .product-image-container {\r\n    position: relative;\r\n    width: 200rpx;\r\n    height: 200rpx;\r\n    margin-right: 20rpx;\r\n    \r\n    .product-image {\r\n      width: 100%;\r\n      height: 100%;\r\n      border-radius: 12rpx;\r\n    }\r\n    \r\n    .product-tag {\r\n      position: absolute;\r\n      top: 10rpx;\r\n      left: 10rpx;\r\n      padding: 4rpx 12rpx;\r\n      font-size: 20rpx;\r\n      color: #FFFFFF;\r\n      border-radius: 10rpx;\r\n      background: linear-gradient(135deg, #FF5A5F 0%, #FF3B30 100%);\r\n    }\r\n  }\r\n  \r\n  .product-info {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    \r\n    .product-title {\r\n      font-size: 28rpx;\r\n      font-weight: 600;\r\n      color: #333333;\r\n      margin-bottom: 8rpx;\r\n    }\r\n    \r\n    .product-desc {\r\n      font-size: 24rpx;\r\n      color: #666666;\r\n      margin-bottom: 12rpx;\r\n      display: -webkit-box;\r\n      -webkit-line-clamp: 2;\r\n      -webkit-box-orient: vertical;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n    }\r\n    \r\n    .product-price-row {\r\n      display: flex;\r\n      align-items: baseline;\r\n      margin-bottom: 12rpx;\r\n      \r\n      .current-price {\r\n        color: #FF3B30;\r\n        font-weight: 600;\r\n        \r\n        .price-symbol {\r\n          font-size: 24rpx;\r\n        }\r\n        \r\n        .price-value {\r\n          font-size: 32rpx;\r\n        }\r\n      }\r\n      \r\n      .original-price {\r\n        margin-left: 12rpx;\r\n        font-size: 24rpx;\r\n        color: #999999;\r\n        text-decoration: line-through;\r\n      }\r\n    }\r\n    \r\n    .product-progress {\r\n      margin-bottom: 16rpx;\r\n      \r\n      .progress-text {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        font-size: 22rpx;\r\n        color: #666666;\r\n        margin-bottom: 8rpx;\r\n      }\r\n      \r\n      .progress-bar {\r\n        height: 8rpx;\r\n        background-color: #F2F2F7;\r\n        border-radius: 4rpx;\r\n        overflow: hidden;\r\n        \r\n        .progress-filled {\r\n          height: 100%;\r\n          background: linear-gradient(90deg, #FF9500 0%, #FF3B30 100%);\r\n          border-radius: 4rpx;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .product-btn {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      height: 60rpx;\r\n      border-radius: 30rpx;\r\n      background: linear-gradient(135deg, #FF5A5F 0%, #FF3B30 100%);\r\n      font-size: 26rpx;\r\n      font-weight: 500;\r\n      color: #FFFFFF;\r\n    }\r\n  }\r\n}\r\n\r\n/* 加载更多和到底了提示 */\r\n.loading-more, .no-more {\r\n  text-align: center;\r\n  padding: 20rpx 0;\r\n  font-size: 24rpx;\r\n  color: #999999;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 100rpx 0;\r\n  \r\n  .empty-image {\r\n    width: 200rpx;\r\n    height: 200rpx;\r\n    margin-bottom: 20rpx;\r\n  }\r\n  \r\n  .empty-text {\r\n    font-size: 28rpx;\r\n    color: #999999;\r\n  }\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/flash-sale/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "ActivityService", "ActivityType"], "mappings": ";;;;AAqHA,MAAK,iBAAkB,MAAW;AAGlC,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,mBAAmB;AAAA,MACnB,QAAQ;AAAA,QACN;AAAA,UACE,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,SAAS,IAAI,MAAK,oBAAI,KAAM,GAAC,SAAS,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE,YAAY;AAAA,QACjE;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,SAAS,IAAI,MAAK,oBAAI,KAAM,GAAC,SAAS,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE,YAAY;AAAA,QACjE;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,SAAS,IAAI,MAAK,oBAAI,KAAM,GAAC,SAAS,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE,YAAY;AAAA,QACjE;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,SAAS,IAAI,MAAK,oBAAI,KAAM,GAAC,SAAS,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE,YAAY;AAAA,QACjE;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,SAAS,IAAI,MAAK,oBAAI,KAAM,GAAC,SAAS,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE,YAAY;AAAA,QACjE;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,SAAS,IAAI,MAAK,oBAAI,KAAM,GAAC,SAAS,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE,YAAY;AAAA,QACjE;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,SAAS,IAAI,MAAK,oBAAI,KAAM,GAAC,SAAS,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE,YAAY;AAAA,QAClE;AAAA,MACD;AAAA,MACD,UAAU,CAAE;AAAA,MACZ,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,eAAe;AACb,aAAO,KAAK,OAAO,KAAK,iBAAiB;AAAA,IAC3C;AAAA,EACD;AAAA,EACD,SAAS;AAEP,SAAK,UAAU;AAAA,EAChB;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,aAAa,GAAG;AACd,WAAK,YAAY,EAAE,OAAO;AAAA,IAC3B;AAAA;AAAA,IAGD,SAAS;AACPA,oBAAAA,MAAI,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,YAAY,OAAO;AACjB,WAAK,oBAAoB;AACzB,WAAK,WAAW,CAAG;AACnB,WAAK,OAAO;AACZ,WAAK,SAAS;AACd,WAAK,UAAY;AAAA,IAClB;AAAA;AAAA,IAGD,iBAAiB;AAEfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,OACP;AAGD,UAAI,KAAK,oBAAoB,KAAK,OAAO,SAAS,GAAG;AACnD,aAAK;AACL,aAAK,WAAW,CAAG;AACnB,aAAK,OAAO;AACZ,aAAK,SAAS;AACd,aAAK,UAAY;AAAA,MACnB;AAAA,IACD;AAAA;AAAA,IAGD,WAAW;AACT,UAAI,KAAK,WAAW,KAAK;AAAQ;AAEjC,WAAK,UAAU;AACf,WAAK;AAGL,WAAK,cAAc;AAAA,IACpB;AAAA;AAAA,IAGD,YAAY;AACV,WAAK,UAAU;AAEf,UAAI;AAEF,cAAM,sBAAsBC,gCAAe,gBAAC,cAAc;AAAA,UACxD,MAAMC,gCAAY,aAAC;AAAA,UACnB,eAAe;AAAA,UACf,eAAe;AAAA,UACf,MAAM,KAAK;AAAA,UACX,UAAU,KAAK;AAAA,QACjB,CAAC;AAED,YAAI,uBAAuB,oBAAoB,SAAS,GAAG;AACzD,eAAK,WAAW;AAChB,eAAK,UAAU;AAAA,eACV;AAEL,eAAK,WAAW;AAChB,eAAK,SAAS;AACd,eAAK,UAAU;AAAA,QACjB;AAAA,MACA,SAAO,OAAO;AACdF,sBAAA,MAAA,MAAA,SAAA,mEAAc,eAAe,KAAK;AAClC,aAAK,UAAU;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB;AACd,UAAI;AAEF,cAAM,iBAAiBC,gCAAe,gBAAC,cAAc;AAAA,UACnD,MAAMC,gCAAY,aAAC;AAAA,UACnB,eAAe;AAAA,UACf,eAAe;AAAA,UACf,MAAM,KAAK;AAAA,UACX,UAAU,KAAK;AAAA,QACjB,CAAC;AAED,YAAI,kBAAkB,eAAe,SAAS,GAAG;AAC/C,eAAK,WAAW,CAAC,GAAG,KAAK,UAAU,GAAG,cAAc;AACpD,eAAK,UAAU;AAAA,eACV;AAEL,eAAK,SAAS;AACd,eAAK,UAAU;AAAA,QACjB;AAAA,MACA,SAAO,OAAO;AACdF,sBAAc,MAAA,MAAA,SAAA,mEAAA,iBAAiB,KAAK;AACpC,aAAK,UAAU;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB,IAAI;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,6DAA6D,EAAE;AAAA,OACrE;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnTA,GAAG,WAAW,eAAe;"}