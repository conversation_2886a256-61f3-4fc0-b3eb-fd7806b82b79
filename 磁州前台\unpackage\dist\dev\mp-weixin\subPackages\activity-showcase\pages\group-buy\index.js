"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      refreshing: false,
      loading: false,
      noMore: false,
      currentTabIndex: 0,
      isHomePage: false,
      tabs: [
        { name: "全部拼团" },
        { name: "我的拼团" }
      ],
      banners: [
        { image: "https://via.placeholder.com/750x300", link: "" },
        { image: "https://via.placeholder.com/750x300", link: "" },
        { image: "https://via.placeholder.com/750x300", link: "" }
      ],
      categories: [
        { name: "美食", icon: "https://via.placeholder.com/60x60", id: "food" },
        { name: "电器", icon: "https://via.placeholder.com/60x60", id: "appliance" },
        { name: "服饰", icon: "https://via.placeholder.com/60x60", id: "clothing" },
        { name: "美妆", icon: "https://via.placeholder.com/60x60", id: "beauty" },
        { name: "家居", icon: "https://via.placeholder.com/60x60", id: "home" }
      ],
      limitedTimeItems: [
        {
          id: 1,
          image: "https://via.placeholder.com/200x200",
          title: "高端智能扫地机器人",
          marketPrice: "3999",
          dailyPrice: "2999",
          groupPrice: "1999",
          groupCount: 3
        },
        {
          id: 2,
          image: "https://via.placeholder.com/200x200",
          title: "九阳豆浆机",
          marketPrice: "599",
          dailyPrice: "499",
          groupPrice: "299",
          groupCount: 2
        },
        {
          id: 3,
          image: "https://via.placeholder.com/200x200",
          title: "小米空气净化器",
          marketPrice: "999",
          dailyPrice: "899",
          groupPrice: "699",
          groupCount: 3
        }
      ],
      limitedTimeCountdown: "23:59:59",
      groupItems: [
        {
          id: 2,
          image: "https://via.placeholder.com/300x300",
          title: "九阳豆浆机",
          description: "破壁免滤双预约多功能",
          groupPrice: "299",
          dailyPrice: "499",
          marketPrice: "599",
          tag: "热门",
          groupCount: 2,
          joinCount: 156,
          userAvatars: [
            "https://via.placeholder.com/50x50",
            "https://via.placeholder.com/50x50",
            "https://via.placeholder.com/50x50",
            "https://via.placeholder.com/50x50"
          ],
          endTime: new Date(Date.now() + 3 * 24 * 60 * 60 * 1e3).toISOString()
        },
        {
          id: 3,
          image: "https://via.placeholder.com/300x300",
          title: "小米空气净化器",
          description: "除菌除醛除异味",
          groupPrice: "699",
          dailyPrice: "899",
          marketPrice: "999",
          tag: "爆款",
          groupCount: 3,
          joinCount: 78,
          userAvatars: [
            "https://via.placeholder.com/50x50",
            "https://via.placeholder.com/50x50"
          ],
          endTime: new Date(Date.now() + 2 * 24 * 60 * 60 * 1e3).toISOString()
        },
        {
          id: 4,
          image: "https://via.placeholder.com/300x300",
          title: "华为智能手表",
          description: "心率监测，睡眠分析，运动追踪",
          groupPrice: "899",
          dailyPrice: "1099",
          marketPrice: "1299",
          tag: "新品",
          groupCount: 2,
          joinCount: 45,
          userAvatars: [
            "https://via.placeholder.com/50x50",
            "https://via.placeholder.com/50x50",
            "https://via.placeholder.com/50x50"
          ],
          endTime: new Date(Date.now() + 4 * 24 * 60 * 60 * 1e3).toISOString()
        },
        {
          id: 5,
          image: "https://via.placeholder.com/300x300",
          title: "飞利浦电动牙刷",
          description: "声波震动，智能提醒，长效续航",
          groupPrice: "399",
          dailyPrice: "599",
          marketPrice: "699",
          tag: "限量",
          groupCount: 5,
          joinCount: 230,
          userAvatars: [
            "https://via.placeholder.com/50x50",
            "https://via.placeholder.com/50x50"
          ],
          endTime: new Date(Date.now() + 1 * 24 * 60 * 60 * 1e3).toISOString()
        }
      ],
      myGroups: [
        {
          id: 6,
          image: "https://via.placeholder.com/300x300",
          title: "小米空气净化器",
          description: "除菌除醛除异味",
          groupPrice: "699",
          dailyPrice: "899",
          marketPrice: "999",
          groupCount: 3,
          joinCount: 2,
          status: "pending",
          endTime: new Date(Date.now() + 12 * 60 * 60 * 1e3).toISOString()
        },
        {
          id: 7,
          image: "https://via.placeholder.com/300x300",
          title: "华为智能手表",
          description: "心率监测，睡眠分析，运动追踪",
          groupPrice: "899",
          dailyPrice: "1099",
          marketPrice: "1299",
          groupCount: 2,
          joinCount: 2,
          status: "success",
          endTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1e3).toISOString()
        },
        {
          id: 8,
          image: "https://via.placeholder.com/300x300",
          title: "飞利浦电动牙刷",
          description: "声波震动，智能提醒，长效续航",
          groupPrice: "399",
          dailyPrice: "599",
          marketPrice: "699",
          groupCount: 5,
          joinCount: 3,
          status: "failed",
          endTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1e3).toISOString()
        }
      ]
    };
  },
  computed: {
    tabLineStyle() {
      const width = 100 / this.tabs.length;
      const left = this.currentTabIndex * width;
      return {
        width: width + "%",
        transform: `translateX(${left * 100}%)`
      };
    }
  },
  onLoad() {
    this.fetchData();
    this.startCountdown();
    const pages = getCurrentPages();
    if (pages.length === 1) {
      this.isHomePage = true;
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 切换选项卡
    switchTab(index) {
      this.currentTabIndex = index;
      this.fetchData();
    },
    // 加载更多
    loadMore() {
      if (this.loading || this.noMore)
        return;
      this.loading = true;
      setTimeout(() => {
        if (this.currentTabIndex === 0) {
          const moreItems = [
            {
              id: 9,
              image: "https://via.placeholder.com/300x300",
              title: "德尔玛加湿器",
              description: "大容量，静音设计，智能恒湿",
              groupPrice: "199",
              dailyPrice: "249",
              marketPrice: "299",
              tag: "特惠",
              groupCount: 2,
              joinCount: 67,
              userAvatars: [
                "https://via.placeholder.com/50x50",
                "https://via.placeholder.com/50x50"
              ],
              endTime: new Date(Date.now() + 5 * 24 * 60 * 60 * 1e3).toISOString()
            },
            {
              id: 10,
              image: "https://via.placeholder.com/300x300",
              title: "小熊酸奶机",
              description: "家用全自动，陶瓷内胆",
              groupPrice: "159",
              dailyPrice: "199",
              marketPrice: "259",
              tag: "热卖",
              groupCount: 3,
              joinCount: 120,
              userAvatars: [
                "https://via.placeholder.com/50x50"
              ],
              endTime: new Date(Date.now() + 3 * 24 * 60 * 60 * 1e3).toISOString()
            }
          ];
          this.groupItems = [...this.groupItems, ...moreItems];
        }
        this.noMore = true;
        this.loading = false;
      }, 1500);
    },
    // 获取数据
    fetchData() {
    },
    // 导航到详情页
    navigateToDetail(id) {
      common_vendor.index.navigateTo({
        url: `/subPackages/activity-showcase/pages/detail/index?id=${id}&type=group`
      });
    },
    // 导航到搜索页
    navigateToSearch() {
      common_vendor.index.navigateTo({
        url: "/subPackages/activity-showcase/pages/search/index"
      });
    },
    // 按分类筛选
    filterByCategory(category) {
      common_vendor.index.showToast({
        title: `已选择${category.name}分类`,
        icon: "none"
      });
    },
    // 启动倒计时
    startCountdown() {
      let hours = 23;
      let minutes = 59;
      let seconds = 59;
      setInterval(() => {
        seconds--;
        if (seconds < 0) {
          seconds = 59;
          minutes--;
          if (minutes < 0) {
            minutes = 59;
            hours--;
            if (hours < 0) {
              hours = 23;
            }
          }
        }
        this.limitedTimeCountdown = `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
      }, 1e3);
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        "pending": "拼团中",
        "success": "拼团成功",
        "failed": "拼团失败"
      };
      return statusMap[status] || "";
    },
    // 分享拼团
    shareGroup(id) {
      common_vendor.index.showShareMenu({
        withShareTicket: true,
        menus: ["shareAppMessage", "shareTimeline"]
      });
    },
    // 滚动到顶部
    scrollToTop() {
      common_vendor.index.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });
    },
    // 导航到首页
    navigateToHome() {
      if (!this.isHomePage) {
        common_vendor.index.switchTab({
          url: "/pages/index/index"
        });
      }
    },
    // 导航到分类页
    navigateToCategory() {
      common_vendor.index.switchTab({
        url: "/pages/category/index"
      });
    },
    // 导航到购物车
    navigateToCart() {
      common_vendor.index.switchTab({
        url: "/pages/cart/index"
      });
    },
    // 导航到我的页面
    navigateToMine() {
      common_vendor.index.switchTab({
        url: "/pages/mine/index"
      });
    }
  }
};
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$7,
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: common_vendor.p({
      d: "M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6c3.2 3.2 8.4 3.2 11.6 0l43.6-43.5c3.2-3.2 3.2-8.4 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z",
      fill: "#FFFFFF"
    }),
    d: common_vendor.p({
      viewBox: "0 0 1024 1024",
      xmlns: "http://www.w3.org/2000/svg",
      width: "22",
      height: "22"
    }),
    e: common_vendor.o((...args) => $options.navigateToSearch && $options.navigateToSearch(...args)),
    f: common_vendor.p({
      d: "M512 421.490332L331.349941 240.840273c-24.988383-24.988383-65.35828-24.988383-90.346664 0-24.988383 24.988383-24.988383 65.35828 0 90.346664L421.653336 512 240.840273 692.812059c-24.988383 24.988383-24.988383 65.35828 0 90.346664 24.988383 24.988383 65.35828 24.988383 90.346664 0L512 602.509668l180.650059 180.650059c24.988383 24.988383 65.35828 24.988383 90.346664 0 24.988383-24.988383 24.988383-65.35828 0-90.346664L602.346664 512l180.813063-180.812059c24.988383-24.988383 24.988383-65.35828 0-90.346664-24.988383-24.988383-65.35828-24.988383-90.346664 0L512 421.490332z",
      fill: "#FFFFFF"
    }),
    g: common_vendor.p({
      viewBox: "0 0 1024 1024",
      xmlns: "http://www.w3.org/2000/svg",
      width: "22",
      height: "22"
    }),
    h: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    i: common_vendor.f($data.tabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab.name),
        b: index,
        c: $data.currentTabIndex === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    }),
    j: common_vendor.s($options.tabLineStyle),
    k: $data.currentTabIndex === 0
  }, $data.currentTabIndex === 0 ? {
    l: common_vendor.f($data.banners, (banner, index, i0) => {
      return {
        a: banner.image,
        b: index
      };
    })
  } : {}, {
    m: $data.currentTabIndex === 0
  }, $data.currentTabIndex === 0 ? {
    n: common_vendor.f($data.categories, (category, index, i0) => {
      return {
        a: category.icon,
        b: common_vendor.t(category.name),
        c: index,
        d: common_vendor.o(($event) => $options.filterByCategory(category), index)
      };
    })
  } : {}, {
    o: $data.currentTabIndex === 0
  }, $data.currentTabIndex === 0 ? {
    p: common_vendor.t($data.limitedTimeCountdown),
    q: common_vendor.f($data.limitedTimeItems, (item, index, i0) => {
      return {
        a: item.image,
        b: common_vendor.t(item.title),
        c: common_vendor.t(item.groupPrice),
        d: common_vendor.t(item.dailyPrice),
        e: common_vendor.t(item.marketPrice),
        f: common_vendor.t(item.groupCount),
        g: index,
        h: common_vendor.o(($event) => $options.navigateToDetail(item.id), index)
      };
    })
  } : {}, {
    r: $data.currentTabIndex === 0
  }, $data.currentTabIndex === 0 ? {} : {}, {
    s: common_vendor.f($data.groupItems, (item, index, i0) => {
      return common_vendor.e({
        a: item.image,
        b: item.tag
      }, item.tag ? {
        c: common_vendor.t(item.tag)
      } : {}, {
        d: common_vendor.t(item.title),
        e: common_vendor.t(item.description),
        f: common_vendor.t(item.groupPrice),
        g: common_vendor.t(item.dailyPrice),
        h: common_vendor.t(item.marketPrice),
        i: common_vendor.t((item.marketPrice - item.groupPrice).toFixed(2)),
        j: common_vendor.f(item.userAvatars.slice(0, 3), (avatar, avatarIndex, i1) => {
          return {
            a: avatarIndex,
            b: avatar
          };
        }),
        k: item.joinCount > 3
      }, item.joinCount > 3 ? {
        l: common_vendor.t(item.joinCount - 3)
      } : {}, {
        m: common_vendor.t(item.joinCount),
        n: common_vendor.t(item.groupCount),
        o: common_vendor.t(item.groupPrice),
        p: index,
        q: common_vendor.o(($event) => $options.navigateToDetail(item.id), index)
      });
    }),
    t: $data.currentTabIndex === 1
  }, $data.currentTabIndex === 1 ? common_vendor.e({
    v: $data.myGroups.length === 0
  }, $data.myGroups.length === 0 ? {
    w: common_assets._imports_1$52,
    x: common_vendor.o(($event) => $options.switchTab(0))
  } : {
    y: common_vendor.f($data.myGroups, (item, index, i0) => {
      return common_vendor.e({
        a: item.image,
        b: common_vendor.t($options.getStatusText(item.status)),
        c: common_vendor.n(item.status),
        d: common_vendor.t(item.title),
        e: common_vendor.t(item.groupPrice),
        f: common_vendor.t(item.dailyPrice),
        g: common_vendor.t($options.getStatusText(item.status)),
        h: common_vendor.n(item.status),
        i: item.status === "pending"
      }, item.status === "pending" ? {
        j: common_vendor.t(item.groupCount - item.joinCount)
      } : {}, {
        k: item.status === "pending"
      }, item.status === "pending" ? {
        l: common_vendor.o(($event) => $options.shareGroup(item.id), index)
      } : {}, {
        m: index,
        n: common_vendor.o(($event) => $options.navigateToDetail(item.id), index)
      });
    })
  }) : {}, {
    z: $data.loading
  }, $data.loading ? {} : {}, {
    A: $data.noMore
  }, $data.noMore ? {} : {}, {
    B: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args)),
    C: common_vendor.p({
      d: "M512 352L188 676c-12.3 12.3-32.2 12.3-44.5 0-12.3-12.3-12.3-32.2 0-44.5l346-346c6.2-6.2 14.3-9.3 22.3-9.3s16.1 3.1 22.3 9.3l346 346c12.3 12.3 12.3 32.2 0 44.5-12.3 12.3-32.2 12.3-44.5 0L512 352z",
      fill: "#FFFFFF"
    }),
    D: common_vendor.p({
      viewBox: "0 0 1024 1024",
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20"
    }),
    E: common_vendor.o((...args) => $options.scrollToTop && $options.scrollToTop(...args)),
    F: common_vendor.p({
      d: "M512 128L128 447.936V896h255.936V640H640v256h255.936V447.936L512 128z",
      fill: $data.isHomePage ? "#FF2C54" : "#666666"
    }),
    G: common_vendor.p({
      viewBox: "0 0 1024 1024",
      xmlns: "http://www.w3.org/2000/svg",
      width: "24",
      height: "24"
    }),
    H: $data.isHomePage ? 1 : "",
    I: common_vendor.o((...args) => $options.navigateToHome && $options.navigateToHome(...args)),
    J: common_vendor.p({
      d: "M320 320H192V192h128v128zm256 0H448V192h128v128zm256 0H704V192h128v128zM320 576H192V448h128v128zm256 0H448V448h128v128zm256 0H704V448h128v128zM320 832H192V704h128v128zm256 0H448V704h128v128zm256 0H704V704h128v128z",
      fill: "#666666"
    }),
    K: common_vendor.p({
      viewBox: "0 0 1024 1024",
      xmlns: "http://www.w3.org/2000/svg",
      width: "24",
      height: "24"
    }),
    L: common_vendor.o((...args) => $options.navigateToCategory && $options.navigateToCategory(...args)),
    M: common_vendor.p({
      d: "M922.9 701.9H327.4l29.9-60.9 496.8-.9c16.8 0 31.2-12 34.2-28.6l68.8-385.1c1.8-10.1-.9-20.5-7.5-28.4a34.99 34.99 0 0 0-26.6-12.5l-632-2.1-5.4-25.4c-3.4-16.2-18-28-34.6-28H96.5a35.3 35.3 0 1 0 0 70.6h125.9L246 312l58.1 281.3-74.8 122.1a34.96 34.96 0 0 0-3 36.8c6 11.9 18.1 19.4 31.5 19.4h62.8a102.43 102.43 0 0 0-20.6 61.7c0 56.6 46 102.6 102.6 102.6s102.6-46 102.6-102.6c0-22.3-7.4-44-20.6-61.7h161.1a102.43 102.43 0 0 0-20.6 61.7c0 56.6 46 102.6 102.6 102.6s102.6-46 102.6-102.6c0-22.3-7.4-44-20.6-61.7H923c19.4 0 35.3-15.8 35.3-35.3a35.42 35.42 0 0 0-35.4-35.2zM305.7 253l575.8 1.9-56.4 315.8-452.3.8L305.7 253zm96.9 612.7c-17.4 0-31.6-14.2-31.6-31.6 0-17.4 14.2-31.6 31.6-31.6s31.6 14.2 31.6 31.6a31.6 31.6 0 0 1-31.6 31.6zm325.1 0c-17.4 0-31.6-14.2-31.6-31.6 0-17.4 14.2-31.6 31.6-31.6s31.6 14.2 31.6 31.6a31.6 31.6 0 0 1-31.6 31.6z",
      fill: "#666666"
    }),
    N: common_vendor.p({
      viewBox: "0 0 1024 1024",
      xmlns: "http://www.w3.org/2000/svg",
      width: "24",
      height: "24"
    }),
    O: common_vendor.o((...args) => $options.navigateToCart && $options.navigateToCart(...args)),
    P: common_vendor.p({
      d: "M858.5 763.6a374 374 0 0 0-80.6-119.5 375.63 375.63 0 0 0-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 0 0-80.6 119.5A371.7 371.7 0 0 0 136 901.8a8 8 0 0 0 8 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 0 0 8-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z",
      fill: "#666666"
    }),
    Q: common_vendor.p({
      viewBox: "0 0 1024 1024",
      xmlns: "http://www.w3.org/2000/svg",
      width: "24",
      height: "24"
    }),
    R: common_vendor.o((...args) => $options.navigateToMine && $options.navigateToMine(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-f45a6859"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/group-buy/index.js.map
