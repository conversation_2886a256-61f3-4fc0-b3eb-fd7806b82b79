/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-1baa414b, html.data-v-1baa414b, #app.data-v-1baa414b, .index-container.data-v-1baa414b {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.rules-container.data-v-1baa414b {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 30rpx;
  padding-top: calc(44px + var(--status-bar-height));
}

/* 自定义导航栏 */
.custom-navbar.data-v-1baa414b {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  padding: 0 30rpx;
  padding-top: 44px;
  /* 状态栏高度 */
  position: fixed;
  /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  background-image: linear-gradient(135deg, #0066FF, #0052CC);
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.15);
  z-index: 100;
  /* 提高z-index确保在最上层 */
}
.navbar-title.data-v-1baa414b {
  position: absolute;
  left: 0;
  right: 0;
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 700;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  text-align: center;
}
.navbar-left.data-v-1baa414b {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 20;
  /* 确保在标题上层，可以被点击 */
}
.back-icon.data-v-1baa414b {
  width: 100%;
  height: 100%;
}
.safe-area-top.data-v-1baa414b {
  height: var(--status-bar-height);
  width: 100%;
  background-image: linear-gradient(135deg, #0066FF, #0052CC);
}

/* 规则内容 */
.rules-content.data-v-1baa414b {
  padding: 20rpx 30rpx;
}
.rule-section.data-v-1baa414b {
  margin-bottom: 40rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);
}
.section-header.data-v-1baa414b {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  position: relative;
}
.section-header.data-v-1baa414b::after {
  content: "";
  position: absolute;
  bottom: -15rpx;
  left: 0;
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(to right, #0066FF, #36CBCB);
  border-radius: 2rpx;
}
.section-icon.data-v-1baa414b {
  width: 40rpx;
  height: 40rpx;
  margin-right: 15rpx;
}
.section-title.data-v-1baa414b {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.section-content.data-v-1baa414b {
  padding: 10rpx 0;
}

/* 等级项样式 */
.level-item.data-v-1baa414b {
  margin-bottom: 30rpx;
  padding-bottom: 30rpx;
  border-bottom: 1px dashed #e0e0e0;
}
.level-item.data-v-1baa414b:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}
.level-header.data-v-1baa414b {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.level-badge.data-v-1baa414b {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 24rpx;
  font-weight: 600;
  margin-right: 15rpx;
}
.level-badge.level-1.data-v-1baa414b {
  background: linear-gradient(135deg, #1677FF, #0E5FD8);
}
.level-badge.level-2.data-v-1baa414b {
  background: linear-gradient(135deg, #C0C0C0, #A9A9A9);
}
.level-badge.level-3.data-v-1baa414b {
  background: linear-gradient(135deg, #FFD700, #FFA500);
}
.level-badge.level-4.data-v-1baa414b {
  background: linear-gradient(135deg, #B9F2FF, #00BFFF);
}
.level-name.data-v-1baa414b {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
}
.level-desc.data-v-1baa414b {
  padding-left: 55rpx;
}
.desc-item.data-v-1baa414b {
  display: flex;
  margin-bottom: 10rpx;
}
.desc-item.data-v-1baa414b:last-child {
  margin-bottom: 0;
}
.desc-label.data-v-1baa414b {
  font-size: 28rpx;
  color: #666666;
  width: 150rpx;
  flex-shrink: 0;
}
.desc-value.data-v-1baa414b {
  font-size: 28rpx;
  color: #333333;
  flex: 1;
}

/* 规则项样式 */
.rule-item.data-v-1baa414b {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}
.rule-item.data-v-1baa414b:last-child {
  margin-bottom: 0;
}
.rule-dot.data-v-1baa414b {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #0066FF;
  margin-top: 14rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}
.rule-text.data-v-1baa414b {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  flex: 1;
}

/* 底部安全区域 */
.safe-area-bottom.data-v-1baa414b {
  height: env(safe-area-inset-bottom);
  width: 100%;
}