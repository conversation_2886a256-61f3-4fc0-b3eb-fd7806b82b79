{"version": 3, "file": "ActivityService.js", "sources": ["common/services/ActivityService.js"], "sourcesContent": ["/**\r\n * 活动服务 - 用于营销中心和活动中心之间的数据互通\r\n */\r\n\r\n// 模拟活动数据存储\r\nlet activities = [];\r\n\r\n// 活动类型枚举\r\nconst ActivityType = {\r\n  FLASH: 'flash',\r\n  GROUP: 'group',\r\n  DISCOUNT: 'discount',\r\n  COUPON: 'coupon'\r\n};\r\n\r\n// 活动状态枚举\r\nconst ActivityStatus = {\r\n  DRAFT: 'draft',      // 草稿\r\n  PUBLISHED: 'published', // 已发布\r\n  ONGOING: 'ongoing',  // 进行中\r\n  ENDED: 'ended',      // 已结束\r\n  CANCELLED: 'cancelled' // 已取消\r\n};\r\n\r\n/**\r\n * 活动服务类\r\n */\r\nclass ActivityService {\r\n  /**\r\n   * 创建活动\r\n   * @param {Object} activityData 活动数据\r\n   * @returns {Object} 创建的活动对象\r\n   */\r\n  static createActivity(activityData) {\r\n    const id = `activity_${Date.now()}_${Math.floor(Math.random() * 1000)}`;\r\n    const activity = {\r\n      id,\r\n      ...activityData,\r\n      createTime: new Date().toISOString(),\r\n      updateTime: new Date().toISOString(),\r\n      status: ActivityStatus.DRAFT\r\n    };\r\n    \r\n    activities.push(activity);\r\n    return activity;\r\n  }\r\n  \r\n  /**\r\n   * 发布活动\r\n   * @param {String} id 活动ID\r\n   * @param {Object} publishOptions 发布选项\r\n   * @returns {Object} 更新后的活动对象\r\n   */\r\n  static publishActivity(id, publishOptions = {}) {\r\n    const activity = activities.find(a => a.id === id);\r\n    if (!activity) return null;\r\n    \r\n    activity.status = ActivityStatus.PUBLISHED;\r\n    activity.updateTime = new Date().toISOString();\r\n    activity.publishTime = new Date().toISOString();\r\n    activity.publishOptions = publishOptions;\r\n    \r\n    return activity;\r\n  }\r\n  \r\n  /**\r\n   * 获取活动列表\r\n   * @param {Object} filters 过滤条件\r\n   * @returns {Array} 活动列表\r\n   */\r\n  static getActivities(filters = {}) {\r\n    let result = [...activities];\r\n    \r\n    // 按类型过滤\r\n    if (filters.type) {\r\n      result = result.filter(a => a.type === filters.type);\r\n    }\r\n    \r\n    // 按状态过滤\r\n    if (filters.status) {\r\n      result = result.filter(a => a.status === filters.status);\r\n    }\r\n    \r\n    // 只获取已发布的活动\r\n    if (filters.onlyPublished) {\r\n      result = result.filter(a => a.status === ActivityStatus.PUBLISHED);\r\n    }\r\n    \r\n    // 按商家ID过滤\r\n    if (filters.merchantId) {\r\n      result = result.filter(a => a.merchantId === filters.merchantId);\r\n    }\r\n    \r\n    // 按结束时间排序\r\n    if (filters.sortByEndTime) {\r\n      result.sort((a, b) => new Date(a.endTime) - new Date(b.endTime));\r\n    }\r\n    \r\n    // 分页\r\n    if (filters.page && filters.pageSize) {\r\n      const start = (filters.page - 1) * filters.pageSize;\r\n      const end = start + filters.pageSize;\r\n      result = result.slice(start, end);\r\n    }\r\n    \r\n    return result;\r\n  }\r\n  \r\n  /**\r\n   * 获取单个活动\r\n   * @param {String} id 活动ID\r\n   * @returns {Object} 活动对象\r\n   */\r\n  static getActivity(id) {\r\n    return activities.find(a => a.id === id) || null;\r\n  }\r\n  \r\n  /**\r\n   * 更新活动\r\n   * @param {String} id 活动ID\r\n   * @param {Object} updateData 更新数据\r\n   * @returns {Object} 更新后的活动对象\r\n   */\r\n  static updateActivity(id, updateData) {\r\n    const index = activities.findIndex(a => a.id === id);\r\n    if (index === -1) return null;\r\n    \r\n    activities[index] = {\r\n      ...activities[index],\r\n      ...updateData,\r\n      updateTime: new Date().toISOString()\r\n    };\r\n    \r\n    return activities[index];\r\n  }\r\n  \r\n  /**\r\n   * 删除活动\r\n   * @param {String} id 活动ID\r\n   * @returns {Boolean} 是否删除成功\r\n   */\r\n  static deleteActivity(id) {\r\n    const index = activities.findIndex(a => a.id === id);\r\n    if (index === -1) return false;\r\n    \r\n    activities.splice(index, 1);\r\n    return true;\r\n  }\r\n  \r\n  /**\r\n   * 取消活动\r\n   * @param {String} id 活动ID\r\n   * @returns {Object} 更新后的活动对象\r\n   */\r\n  static cancelActivity(id) {\r\n    return this.updateActivity(id, { status: ActivityStatus.CANCELLED });\r\n  }\r\n  \r\n  /**\r\n   * 获取活动统计信息\r\n   * @returns {Object} 统计信息\r\n   */\r\n  static getActivityStats() {\r\n    return {\r\n      total: activities.length,\r\n      published: activities.filter(a => a.status === ActivityStatus.PUBLISHED).length,\r\n      ongoing: activities.filter(a => a.status === ActivityStatus.ONGOING).length,\r\n      ended: activities.filter(a => a.status === ActivityStatus.ENDED).length\r\n    };\r\n  }\r\n  \r\n  /**\r\n   * 检查活动状态并更新\r\n   * 用于定期检查活动是否开始或结束\r\n   */\r\n  static checkAndUpdateActivityStatus() {\r\n    const now = new Date();\r\n    \r\n    activities.forEach(activity => {\r\n      const startTime = new Date(activity.startTime);\r\n      const endTime = new Date(activity.endTime);\r\n      \r\n      if (activity.status === ActivityStatus.PUBLISHED && now >= startTime && now < endTime) {\r\n        activity.status = ActivityStatus.ONGOING;\r\n        activity.updateTime = new Date().toISOString();\r\n      } else if ((activity.status === ActivityStatus.PUBLISHED || activity.status === ActivityStatus.ONGOING) && now >= endTime) {\r\n        activity.status = ActivityStatus.ENDED;\r\n        activity.updateTime = new Date().toISOString();\r\n      }\r\n    });\r\n  }\r\n  \r\n  /**\r\n   * 模拟加载初始数据\r\n   * 实际项目中应该从API获取\r\n   */\r\n  static loadInitialData() {\r\n    // 这里可以从API加载数据\r\n    // 目前使用模拟数据\r\n    const mockActivities = [\r\n      {\r\n        id: 'flash_001',\r\n        type: ActivityType.FLASH,\r\n        name: 'iPhone 13 Pro Max限时秒杀',\r\n        description: '苹果最新旗舰手机限时特惠',\r\n        coverImage: 'https://via.placeholder.com/300x300',\r\n        images: ['https://via.placeholder.com/300x300'],\r\n        originalPrice: '8999',\r\n        flashPrice: '7999',\r\n        stockTotal: 200,\r\n        soldCount: 156,\r\n        startTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),\r\n        endTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),\r\n        status: ActivityStatus.ONGOING,\r\n        merchantId: 'merchant_001',\r\n        createTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),\r\n        updateTime: new Date().toISOString(),\r\n        publishTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),\r\n        tag: '热门'\r\n      },\r\n      {\r\n        id: 'group_001',\r\n        type: ActivityType.GROUP,\r\n        name: '九阳豆浆机拼团',\r\n        description: '破壁免滤双预约多功能',\r\n        coverImage: 'https://via.placeholder.com/300x300',\r\n        images: ['https://via.placeholder.com/300x300'],\r\n        originalPrice: '599',\r\n        flashPrice: '299',\r\n        stockTotal: 100,\r\n        soldCount: 56,\r\n        startTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),\r\n        endTime: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),\r\n        status: ActivityStatus.ONGOING,\r\n        merchantId: 'merchant_002',\r\n        createTime: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),\r\n        updateTime: new Date().toISOString(),\r\n        publishTime: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),\r\n        tag: '2人团'\r\n      }\r\n    ];\r\n    \r\n    activities = mockActivities;\r\n  }\r\n}\r\n\r\n// 初始化时加载数据\r\nActivityService.loadInitialData();\r\n\r\n// 导出\r\nexport {\r\n  ActivityService,\r\n  ActivityType,\r\n  ActivityStatus\r\n}; "], "names": [], "mappings": ";AAKA,IAAI,aAAa,CAAA;AAGZ,MAAC,eAAe;AAAA,EACnB,OAAO;AAAA,EACP,OAAO;AAAA,EACP,UAAU;AAAA,EACV,QAAQ;AACV;AAGA,MAAM,iBAAiB;AAAA,EACrB,OAAO;AAAA;AAAA,EACP,WAAW;AAAA;AAAA,EACX,SAAS;AAAA;AAAA,EACT,OAAO;AAAA;AAAA,EACP,WAAW;AAAA;AACb;AAKA,MAAM,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpB,OAAO,eAAe,cAAc;AAClC,UAAM,KAAK,YAAY,KAAK,IAAK,CAAA,IAAI,KAAK,MAAM,KAAK,OAAQ,IAAG,GAAI,CAAC;AACrE,UAAM,WAAW;AAAA,MACf;AAAA,MACA,GAAG;AAAA,MACH,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA,MACpC,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA,MACpC,QAAQ,eAAe;AAAA,IAC7B;AAEI,eAAW,KAAK,QAAQ;AACxB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,OAAO,gBAAgB,IAAI,iBAAiB,IAAI;AAC9C,UAAM,WAAW,WAAW,KAAK,OAAK,EAAE,OAAO,EAAE;AACjD,QAAI,CAAC;AAAU,aAAO;AAEtB,aAAS,SAAS,eAAe;AACjC,aAAS,cAAa,oBAAI,KAAM,GAAC,YAAW;AAC5C,aAAS,eAAc,oBAAI,KAAM,GAAC,YAAW;AAC7C,aAAS,iBAAiB;AAE1B,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,OAAO,cAAc,UAAU,IAAI;AACjC,QAAI,SAAS,CAAC,GAAG,UAAU;AAG3B,QAAI,QAAQ,MAAM;AAChB,eAAS,OAAO,OAAO,OAAK,EAAE,SAAS,QAAQ,IAAI;AAAA,IACpD;AAGD,QAAI,QAAQ,QAAQ;AAClB,eAAS,OAAO,OAAO,OAAK,EAAE,WAAW,QAAQ,MAAM;AAAA,IACxD;AAGD,QAAI,QAAQ,eAAe;AACzB,eAAS,OAAO,OAAO,OAAK,EAAE,WAAW,eAAe,SAAS;AAAA,IAClE;AAGD,QAAI,QAAQ,YAAY;AACtB,eAAS,OAAO,OAAO,OAAK,EAAE,eAAe,QAAQ,UAAU;AAAA,IAChE;AAGD,QAAI,QAAQ,eAAe;AACzB,aAAO,KAAK,CAAC,GAAG,MAAM,IAAI,KAAK,EAAE,OAAO,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC;AAAA,IAChE;AAGD,QAAI,QAAQ,QAAQ,QAAQ,UAAU;AACpC,YAAM,SAAS,QAAQ,OAAO,KAAK,QAAQ;AAC3C,YAAM,MAAM,QAAQ,QAAQ;AAC5B,eAAS,OAAO,MAAM,OAAO,GAAG;AAAA,IACjC;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,OAAO,YAAY,IAAI;AACrB,WAAO,WAAW,KAAK,OAAK,EAAE,OAAO,EAAE,KAAK;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,OAAO,eAAe,IAAI,YAAY;AACpC,UAAM,QAAQ,WAAW,UAAU,OAAK,EAAE,OAAO,EAAE;AACnD,QAAI,UAAU;AAAI,aAAO;AAEzB,eAAW,KAAK,IAAI;AAAA,MAClB,GAAG,WAAW,KAAK;AAAA,MACnB,GAAG;AAAA,MACH,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA,IAC1C;AAEI,WAAO,WAAW,KAAK;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,OAAO,eAAe,IAAI;AACxB,UAAM,QAAQ,WAAW,UAAU,OAAK,EAAE,OAAO,EAAE;AACnD,QAAI,UAAU;AAAI,aAAO;AAEzB,eAAW,OAAO,OAAO,CAAC;AAC1B,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,OAAO,eAAe,IAAI;AACxB,WAAO,KAAK,eAAe,IAAI,EAAE,QAAQ,eAAe,UAAS,CAAE;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,OAAO,mBAAmB;AACxB,WAAO;AAAA,MACL,OAAO,WAAW;AAAA,MAClB,WAAW,WAAW,OAAO,OAAK,EAAE,WAAW,eAAe,SAAS,EAAE;AAAA,MACzE,SAAS,WAAW,OAAO,OAAK,EAAE,WAAW,eAAe,OAAO,EAAE;AAAA,MACrE,OAAO,WAAW,OAAO,OAAK,EAAE,WAAW,eAAe,KAAK,EAAE;AAAA,IACvE;AAAA,EACG;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,OAAO,+BAA+B;AACpC,UAAM,MAAM,oBAAI;AAEhB,eAAW,QAAQ,cAAY;AAC7B,YAAM,YAAY,IAAI,KAAK,SAAS,SAAS;AAC7C,YAAM,UAAU,IAAI,KAAK,SAAS,OAAO;AAEzC,UAAI,SAAS,WAAW,eAAe,aAAa,OAAO,aAAa,MAAM,SAAS;AACrF,iBAAS,SAAS,eAAe;AACjC,iBAAS,cAAa,oBAAI,KAAM,GAAC,YAAW;AAAA,MAC7C,YAAW,SAAS,WAAW,eAAe,aAAa,SAAS,WAAW,eAAe,YAAY,OAAO,SAAS;AACzH,iBAAS,SAAS,eAAe;AACjC,iBAAS,cAAa,oBAAI,KAAM,GAAC,YAAW;AAAA,MAC7C;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,OAAO,kBAAkB;AAGvB,UAAM,iBAAiB;AAAA,MACrB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM,aAAa;AAAA,QACnB,MAAM;AAAA,QACN,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ,CAAC,qCAAqC;AAAA,QAC9C,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,QACvE,SAAS,IAAI,KAAK,KAAK,IAAK,IAAG,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,QACjE,QAAQ,eAAe;AAAA,QACvB,YAAY;AAAA,QACZ,YAAY,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,QACxE,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA,QACpC,aAAa,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,QACzE,KAAK;AAAA,MACN;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM,aAAa;AAAA,QACnB,MAAM;AAAA,QACN,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ,CAAC,qCAAqC;AAAA,QAC9C,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,QACvE,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,QACrE,QAAQ,eAAe;AAAA,QACvB,YAAY;AAAA,QACZ,YAAY,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,QACxE,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA,QACpC,aAAa,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,QACzE,KAAK;AAAA,MACN;AAAA,IACP;AAEI,iBAAa;AAAA,EACd;AACH;AAGA,gBAAgB,gBAAiB;;;"}