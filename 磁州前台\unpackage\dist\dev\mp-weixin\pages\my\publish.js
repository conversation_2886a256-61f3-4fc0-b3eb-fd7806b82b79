"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  enableShareAppMessage: true,
  enableShareTimeline: true,
  data() {
    return {
      statusBarHeight: 0,
      tabs: ["全部", "审核中", "已发布", "已过期"],
      currentTab: 0,
      infoList: [
        {
          id: 1,
          title: "招聘服务员",
          content: "本店诚聘服务员2名，要求形象好，有经验者优先，薪资面议",
          date: "2024-01-15",
          views: 156,
          likes: 23,
          status: "passed",
          // 审核通过
          statusText: "审核通过"
        },
        {
          id: 2,
          title: "出售二手电动车",
          content: "9成新电动车转让，行驶3000公里，价格实惠",
          date: "2024-01-14",
          views: 89,
          likes: 12,
          status: "reviewing",
          // 审核中
          statusText: "审核中"
        },
        {
          id: 3,
          title: "求租门面房",
          content: "求租商业街店铺，面积50-100平米，价格合适即可",
          date: "2024-01-13",
          views: 245,
          likes: 35,
          status: "passed",
          // 审核通过
          statusText: "审核通过"
        }
      ],
      topCardVisible: false,
      currentItem: null,
      topOptionsVisible: false,
      selectedOption: 0,
      topOptions: [
        { days: 3, price: 2.8 },
        { days: 7, price: 5.8 },
        { days: 30, price: 19.8 }
      ],
      adTipsVisible: false,
      adWatchingVisible: false,
      adCountdown: 30,
      adMinTime: 10,
      topResultVisible: false,
      topSuccess: false,
      topResultText: "",
      userRefreshCount: 2,
      // 用户剩余刷新次数
      customModal: null,
      // 添加自定义弹窗数据
      promotionType: "top",
      // 'top', 'refresh', 'renew'
      promotionTypeText: "置顶"
      // 对应的中文文本
    };
  },
  onLoad() {
    const sysInfo = common_vendor.index.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
  },
  computed: {
    filteredList() {
      if (this.currentTab === 0) {
        return this.infoList;
      } else if (this.currentTab === 1) {
        return this.infoList.filter((item) => item.status === "reviewing");
      } else if (this.currentTab === 2) {
        return this.infoList.filter((item) => item.status === "passed");
      } else if (this.currentTab === 3) {
        return this.infoList.filter((item) => item.status === "expired");
      }
      return [];
    }
  },
  methods: {
    goBack() {
      common_vendor.index.__f__("log", "at pages/my/publish.vue:279", "返回按钮被点击");
      common_vendor.index.navigateBack({
        fail: function() {
          common_vendor.index.switchTab({
            url: "/pages/index/index"
          });
        }
      });
    },
    switchTab(index) {
      this.currentTab = index;
    },
    getStatusClass(status) {
      switch (status) {
        case "reviewing":
          return "status-reviewing";
        case "passed":
          return "status-passed";
        case "expired":
          return "status-expired";
        default:
          return "";
      }
    },
    handleTop(item) {
      this.currentItem = item;
      this.topCardVisible = true;
    },
    hideTopCard() {
      this.topCardVisible = false;
      this.topOptionsVisible = false;
    },
    showTopOptions() {
      this.topOptionsVisible = true;
    },
    hideTopOptions() {
      this.topOptionsVisible = false;
    },
    selectOption(index) {
      this.selectedOption = index;
      const option = this.topOptions[index];
      const orderInfo = {
        orderType: this.promotionType,
        days: option.days,
        amount: option.price,
        infoId: this.currentItem.id,
        userId: common_vendor.index.getStorageSync("userId")
      };
      common_vendor.index.navigateTo({
        url: `/pages/pay/index?orderInfo=${encodeURIComponent(JSON.stringify(orderInfo))}`
      });
      this.hideTopCard();
    },
    showAdTips() {
      this.adTipsVisible = true;
    },
    hideAdTips() {
      this.adTipsVisible = false;
    },
    startWatchAd() {
      this.adTipsVisible = false;
      this.adWatchingVisible = true;
      this.adCountdown = 30;
      this.adTimer = setInterval(() => {
        this.adCountdown--;
        if (this.adCountdown <= 0) {
          clearInterval(this.adTimer);
          this.handleAdComplete(true);
        }
      }, 1e3);
    },
    closeAd() {
      if (this.adCountdown <= 30 - this.adMinTime) {
        clearInterval(this.adTimer);
        this.adTimer = null;
        this.handleAdComplete(true);
      } else {
        common_vendor.index.showToast({
          title: `请观看至少${this.adMinTime}秒广告`,
          icon: "none",
          duration: 1500
        });
      }
    },
    handleAdComplete(success) {
      this.adWatchingVisible = false;
      if (success) {
        try {
          common_vendor.index.showLoading({ title: "处理中..." });
          const infoId = this.currentItem.id;
          setTimeout(() => {
            common_vendor.index.hideLoading();
            this.topResultVisible = true;
            this.topSuccess = true;
            this.topResultText = `${this.promotionTypeText}成功！信息已${this.promotionTypeText}1天`;
            setTimeout(() => {
              this.topResultVisible = false;
              this.hideTopCard();
            }, 2e3);
          }, 1e3);
        } catch (error) {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at pages/my/publish.vue:402", "免费操作请求失败", error);
          common_vendor.index.showToast({
            title: "网络错误，请稍后再试",
            icon: "none"
          });
        }
      } else {
        this.topResultVisible = true;
        this.topSuccess = false;
        this.topResultText = `${this.promotionTypeText}失败！请观看至少10秒广告`;
        setTimeout(() => {
          this.topResultVisible = false;
        }, 2e3);
      }
    },
    handleRefresh(item) {
      if (this.userRefreshCount <= 0) {
        this.showDirectPayRefresh(item);
        return;
      }
      common_vendor.index.showModal({
        title: "刷新发布",
        content: `您当前有${this.userRefreshCount}次刷新机会，是否使用1次刷新该发布？`,
        confirmText: "确认使用",
        cancelText: "购买套餐",
        success: (res) => {
          if (res.confirm) {
            this.userRefreshCount -= 1;
            common_vendor.index.showLoading({ title: "刷新中..." });
            setTimeout(() => {
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "刷新成功",
                icon: "success"
              });
            }, 1e3);
          } else {
            common_vendor.index.navigateTo({
              url: "/pages/services/refresh-package"
            });
          }
        }
      });
    },
    // 修改showDirectPayRefresh方法
    showDirectPayRefresh(item) {
      this.customModal = {
        show: true,
        title: "付费刷新",
        type: "发布",
        cancelText: "购买套餐",
        buttonText: "立即刷新",
        item
      };
    },
    // 关闭自定义弹窗
    closeCustomModal() {
      this.customModal = null;
    },
    // 处理自定义弹窗取消
    handleCustomModalCancel() {
      this.closeCustomModal();
      common_vendor.index.navigateTo({
        url: "/pages/services/refresh-package"
      });
    },
    // 处理自定义弹窗确认
    handleCustomModalConfirm() {
      this.closeCustomModal();
      common_vendor.index.showLoading({ title: "支付中..." });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "刷新成功",
          icon: "success"
        });
      }, 1500);
    },
    handleEdit(item) {
      common_vendor.index.showActionSheet({
        itemList: ["修改内容", "删除内容", item.status === "passed" ? "下架内容" : "上架内容"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.modifyPublish(item);
              break;
            case 1:
              this.confirmDelete(item);
              break;
            case 2:
              this.togglePublishStatus(item);
              break;
          }
        }
      });
    },
    modifyPublish(item) {
      common_vendor.index.navigateTo({
        url: `/pages/publish/detail?id=${item.id}&edit=1`
      });
    },
    togglePublishStatus(item) {
      const action = item.status === "passed" ? "下架" : "上架";
      common_vendor.index.showModal({
        title: `${action}内容`,
        content: `确定要${action}该发布内容吗？`,
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({ title: "处理中..." });
            setTimeout(() => {
              if (item.status === "passed") {
                item.status = "expired";
                item.statusText = "已下架";
              } else {
                item.status = "passed";
                item.statusText = "审核通过";
              }
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: `${action}成功`,
                icon: "success"
              });
            }, 1e3);
          }
        }
      });
    },
    promotePublish(item) {
      this.currentItem = item;
      common_vendor.index.showActionSheet({
        itemList: ["置顶信息", "刷新信息", "续费信息"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.promotionType = "top";
              this.promotionTypeText = "置顶";
              this.topCardVisible = true;
              break;
            case 1:
              common_vendor.index.navigateTo({
                url: `/pages/services/refresh-info?id=${item.id}`
              });
              break;
            case 2:
              common_vendor.index.navigateTo({
                url: `/pages/services/renew-info?id=${item.id}`
              });
              break;
          }
        }
      });
    },
    handleShare(item) {
      common_vendor.index.showShareMenu({
        withShareTicket: true,
        menus: ["shareAppMessage", "shareTimeline"]
      });
    },
    handleMore(item) {
      this.showMoreActions(item);
    },
    showMoreActions(item) {
      common_vendor.index.showActionSheet({
        itemList: ["编辑信息", "删除信息", "举报", "复制链接", "收藏", "取消置顶"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.handleEdit(item);
              break;
            case 1:
              this.confirmDelete(item);
              break;
            case 2:
              common_vendor.index.showToast({
                title: "举报成功",
                icon: "success"
              });
              break;
            case 3:
              common_vendor.index.setClipboardData({
                data: `http://example.com/info/${item.id}`,
                success: () => {
                  common_vendor.index.showToast({
                    title: "链接已复制",
                    icon: "success"
                  });
                }
              });
              break;
            case 4:
              common_vendor.index.showToast({
                title: "收藏成功",
                icon: "success"
              });
              break;
            case 5:
              common_vendor.index.showToast({
                title: "已取消置顶",
                icon: "success"
              });
              break;
          }
        }
      });
    },
    confirmDelete(item) {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要删除该信息吗？",
        success: (res) => {
          if (res.confirm) {
            this.infoList = this.infoList.filter((info) => info.id !== item.id);
            common_vendor.index.showToast({
              title: "删除成功",
              icon: "success"
            });
          }
        }
      });
    },
    onShareAppMessage(res) {
      if (res.from === "button") {
        const item = res.target.dataset.item;
        if (item) {
          return {
            title: item.title || "我的发布内容分享",
            path: `/pages/publish/detail?id=${item.id}`,
            imageUrl: item.image || "/static/images/default-activity.png"
          };
        }
      }
      return {
        title: "我的发布",
        path: "/pages/my/publish",
        imageUrl: "/static/images/default-share.png"
      };
    },
    goToDetail(item) {
      common_vendor.index.navigateTo({
        url: `/pages/publish/detail?id=${item.id}`
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$7,
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: common_vendor.s(`padding-top: ${$data.statusBarHeight}px; height: ${$data.statusBarHeight + 88}rpx;`),
    d: common_vendor.f($data.tabs, (tab, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(tab),
        b: $data.currentTab === index
      }, $data.currentTab === index ? {} : {}, {
        c: index,
        d: $data.currentTab === index ? 1 : "",
        e: common_vendor.o(($event) => $options.switchTab(index), index)
      });
    }),
    e: common_vendor.f($options.filteredList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.title),
        b: common_vendor.t(item.statusText),
        c: common_vendor.n($options.getStatusClass(item.status)),
        d: common_vendor.t(item.content),
        e: common_vendor.t(item.date),
        f: common_vendor.t(item.views),
        g: common_vendor.t(item.likes),
        h: common_vendor.o(($event) => $options.handleEdit(item), index),
        i: common_vendor.o(($event) => $options.promotePublish(item), index),
        j: JSON.stringify(item),
        k: common_vendor.o(() => {
        }, index),
        l: index,
        m: common_vendor.o(($event) => $options.goToDetail(item), index)
      };
    }),
    f: $options.filteredList.length === 0
  }, $options.filteredList.length === 0 ? {
    g: common_assets._imports_0$18
  } : {}, {
    h: $data.topCardVisible
  }, $data.topCardVisible ? common_vendor.e({
    i: common_vendor.t($data.promotionTypeText),
    j: common_vendor.t($data.promotionTypeText),
    k: common_vendor.o((...args) => $options.showAdTips && $options.showAdTips(...args)),
    l: common_vendor.t($data.promotionTypeText),
    m: common_vendor.o((...args) => $options.showTopOptions && $options.showTopOptions(...args)),
    n: $data.topOptionsVisible
  }, $data.topOptionsVisible ? {
    o: common_vendor.t($data.promotionTypeText),
    p: $data.selectedOption === 0 ? 1 : "",
    q: common_vendor.o(($event) => $options.selectOption(0)),
    r: $data.selectedOption === 1 ? 1 : "",
    s: common_vendor.o(($event) => $options.selectOption(1)),
    t: $data.selectedOption === 2 ? 1 : "",
    v: common_vendor.o(($event) => $options.selectOption(2))
  } : {}, {
    w: common_vendor.o(() => {
    }),
    x: common_vendor.o((...args) => $options.hideTopCard && $options.hideTopCard(...args))
  }) : {}, {
    y: $data.adTipsVisible
  }, $data.adTipsVisible ? {
    z: common_vendor.o((...args) => $options.hideAdTips && $options.hideAdTips(...args)),
    A: common_vendor.o((...args) => $options.startWatchAd && $options.startWatchAd(...args)),
    B: common_vendor.o(() => {
    }),
    C: common_vendor.o((...args) => $options.hideAdTips && $options.hideAdTips(...args))
  } : {}, {
    D: $data.adWatchingVisible
  }, $data.adWatchingVisible ? {
    E: common_vendor.t($data.adCountdown),
    F: common_assets._imports_2$9,
    G: common_vendor.t($data.adCountdown <= $data.adMinTime ? "关闭广告" : `请观看${$data.adMinTime}秒 (${$data.adCountdown})`),
    H: $data.adCountdown <= $data.adMinTime ? 1 : "",
    I: common_vendor.o((...args) => $options.closeAd && $options.closeAd(...args))
  } : {}, {
    J: $data.topResultVisible
  }, $data.topResultVisible ? {
    K: $data.topSuccess ? "/static/images/pay/success.png" : "/static/images/pay/fail.png",
    L: common_vendor.t($data.topResultText)
  } : {}, {
    M: $data.customModal && $data.customModal.show
  }, $data.customModal && $data.customModal.show ? {
    N: common_vendor.o((...args) => $options.closeCustomModal && $options.closeCustomModal(...args)),
    O: common_vendor.t($data.customModal.title),
    P: common_vendor.t($data.customModal.type),
    Q: common_assets._imports_6$5,
    R: common_vendor.t($data.customModal.cancelText || "购买套餐"),
    S: common_vendor.o((...args) => $options.handleCustomModalCancel && $options.handleCustomModalCancel(...args)),
    T: common_vendor.t($data.customModal.buttonText || "立即刷新"),
    U: common_vendor.o((...args) => $options.handleCustomModalConfirm && $options.handleCustomModalConfirm(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-5405ed41"]]);
_sfc_main.__runtimeHooks = 2;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/publish.js.map
