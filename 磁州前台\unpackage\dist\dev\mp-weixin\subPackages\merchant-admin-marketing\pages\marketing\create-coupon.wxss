/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.coupon-create-container {
  min-height: 100vh;
  background-color: #F5F7FA;
}

/* 导航栏样式 */
.custom-navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  position: relative;
  z-index: 100;
}
.navbar-left {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}
.back-arrow {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}
.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.help-btn {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}

/* 进度指示器样式 */
.progress-container {
  background: #fff;
  padding: 20px 15px 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}
.progress-bar {
  height: 4px;
  background: #EAEAEA;
  border-radius: 2px;
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
}
.progress-track {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: #EAEAEA;
}
.progress-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: #FF7600;
  transition: width 0.3s ease;
}
.progress-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 8px;
}
.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 60px;
}
.step-dot {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: #EAEAEA;
  color: #999;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  border: 2px solid transparent;
  position: relative;
  transition: all 0.3s ease;
}
.step.active .step-dot {
  background: #FF7600;
  color: #fff;
}
.step.completed .step-dot {
  background: #FF7600;
  border-color: rgba(255, 118, 0, 0.2);
}
.check-icon {
  width: 10px;
  height: 6px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(-45deg);
  margin-top: -2px;
}
.step-label {
  font-size: 12px;
  color: #999;
}
.step.active .step-label {
  color: #333;
  font-weight: 500;
}
.step-line {
  flex: 1;
  height: 1px;
  background: #EAEAEA;
  margin: 0 5px;
  margin-top: -18px;
  transition: background 0.3s ease;
}
.step-line.active {
  background: #FF7600;
}

/* 表单容器样式 */
.form-scroll-view {
  height: calc(100vh - 180px);
  /* 减去导航栏和进度条的高度 */
}
.form-section {
  padding: 15px;
  margin-bottom: 60px;
  /* 为底部按钮留出空间 */
}
.section-header {
  margin-bottom: 20px;
}
.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}
.section-subtitle {
  font-size: 14px;
  color: #666;
}

/* 优惠券预览样式 */
.coupon-preview-wrapper {
  margin-bottom: 30px;
}
.preview-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
  display: block;
}
.coupon-preview {
  width: 100%;
  height: 160px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  position: relative;
  display: flex;
  flex-direction: column;
}
.coupon-preview-content {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.coupon-name {
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
}
.coupon-value-container {
  display: flex;
  align-items: flex-end;
  margin-top: 10px;
  height: 50px;
}
.coupon-value-symbol {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 5px;
}
.coupon-value {
  font-size: 40px;
  font-weight: 700;
  color: #fff;
  line-height: 1;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}
.coupon-desc {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 10px;
}
.coupon-dates {
  display: flex;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
}
.coupon-dash-border {
  height: 4px;
  background-image: linear-gradient(to right, rgba(255, 255, 255, 0.7) 50%, transparent 50%);
  background-size: 16px 2px;
  background-repeat: repeat-x;
}
.coupon-extra {
  height: 36px;
  background: rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}
.coupon-limit {
  font-size: 12px;
  color: #fff;
}

/* 表单项样式 */
.form-block {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}
.form-item {
  margin-bottom: 20px;
}
.form-item:last-child {
  margin-bottom: 0;
}
.form-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 8px;
  display: block;
}
.form-label.required::before {
  content: "*";
  color: #FF3B30;
  margin-right: 4px;
}
.form-input {
  width: 100%;
  height: 44px;
  background: #F8FAFC;
  border: 1px solid #EAEAEA;
  border-radius: 8px;
  padding: 0 12px;
  color: #333;
  font-size: 14px;
  box-sizing: border-box;
  transition: all 0.3s;
}
.form-input:focus {
  border-color: #FF7600;
  background: #FFF;
  box-shadow: 0 0 0 2px rgba(255, 118, 0, 0.1);
}
.input-limit {
  font-size: 12px;
  color: #999;
  text-align: right;
  margin-top: 4px;
  display: block;
}
.form-textarea {
  width: 100%;
  height: 80px;
  background: #F8FAFC;
  border: 1px solid #EAEAEA;
  border-radius: 8px;
  padding: 12px;
  color: #333;
  font-size: 14px;
  box-sizing: border-box;
  transition: all 0.3s;
}
.form-textarea:focus {
  border-color: #FF7600;
  background: #FFF;
  box-shadow: 0 0 0 2px rgba(255, 118, 0, 0.1);
}

/* 单选按钮组样式 */
.radio-group {
  display: flex;
  margin-top: 5px;
}
.radio-item {
  display: flex;
  align-items: center;
  margin-right: 30px;
  cursor: pointer;
}
.radio-dot {
  width: 18px;
  height: 18px;
  border-radius: 9px;
  border: 2px solid #CCCCCC;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  transition: all 0.2s;
}
.radio-dot-inner {
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background: #FF7600;
}
.radio-item.active .radio-dot {
  border-color: #FF7600;
}
.radio-label {
  font-size: 14px;
  color: #333;
}

/* 金额输入框样式 */
.value-input-wrap {
  position: relative;
  width: 100%;
}
.value-input {
  padding-right: 40px;
}
.value-unit {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  color: #666;
}

/* 颜色选择器样式 */
.color-picker {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}
.color-option {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.2s;
}
.color-option.active {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
.color-check {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.check-mark {
  width: 10px;
  height: 6px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(-45deg);
  margin-top: -2px;
}

/* 底部按钮样式 */
.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 70px;
  background: #FFFFFF;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 15px;
  padding-bottom: env(safe-area-inset-bottom);
}
.btn {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  margin: 0 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.2s;
}
.btn:active {
  transform: scale(0.97);
  opacity: 0.9;
}
.btn.primary {
  background: linear-gradient(135deg, #FF7600, #FF4D00);
  color: #fff;
}
.btn.secondary {
  background: #F5F5F5;
  color: #666;
}

/* 使用规则样式 - 步骤2 */
.sub-section {
  margin-top: 25px;
  margin-bottom: 20px;
}
.sub-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15px;
}
.switch-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.switch-label {
  font-size: 14px;
  color: #333;
}

/* 发放设置样式 - 步骤3 */
.issue-methods {
  margin-top: 10px;
}
.issue-method {
  display: flex;
  align-items: center;
  background: #F8FAFC;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
  transition: all 0.3s ease;
  position: relative;
}
.issue-method.active {
  background: #FFF9F5;
  border: 1px solid #FF7600;
}
.method-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
}
.method-icon.manual {
  background: linear-gradient(135deg, #36D1DC, #5B86E5);
}
.method-icon.auto {
  background: linear-gradient(135deg, #FF9966, #FF5E62);
}
.method-icon.admin {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
}
.method-icon.share {
  background: linear-gradient(135deg, #FDEB71, #F8D800);
}
.method-icon::before {
  font-size: 20px;
  color: #fff;
  font-weight: 600;
}
.method-icon.manual::before {
  content: "券";
}
.method-icon.auto::before {
  content: "自";
}
.method-icon.admin::before {
  content: "商";
}
.method-icon.share::before {
  content: "享";
}
.method-info {
  flex: 1;
}
.method-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
  display: block;
}
.method-desc {
  font-size: 12px;
  color: #999;
}
.method-check {
  width: 22px;
  height: 22px;
  border-radius: 11px;
  background: #FF7600;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 分销设置样式 */
.distribution-setting {
  margin-top: 20px;
  padding: 15px;
  background: #F8FAFC;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}
.distribution-setting .section-header {
  margin-bottom: 15px;
}
.distribution-setting .section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}
.distribution-setting .section-subtitle {
  font-size: 13px;
  color: #666;
}
.distribution-setting .form-block {
  padding: 0;
  box-shadow: none;
}
.distribution-setting .form-item {
  margin-bottom: 15px;
}
.distribution-setting .form-item:last-child {
  margin-bottom: 0;
}
.distribution-setting .form-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 8px;
  display: block;
}
.distribution-setting .form-label.required::before {
  content: "";
  margin-right: 0;
}
.distribution-setting .form-input {
  width: 100%;
  height: 44px;
  background: #FFF;
  border: 1px solid #EAEAEA;
  border-radius: 8px;
  padding: 0 12px;
  color: #333;
  font-size: 14px;
  box-sizing: border-box;
  transition: all 0.3s;
}
.distribution-setting .form-input:focus {
  border-color: #FF7600;
  background: #FFF;
  box-shadow: 0 0 0 2px rgba(255, 118, 0, 0.1);
}
.distribution-setting .input-limit {
  font-size: 12px;
  color: #999;
  text-align: right;
  margin-top: 4px;
  display: block;
}
.distribution-setting .form-textarea {
  width: 100%;
  height: 80px;
  background: #FFF;
  border: 1px solid #EAEAEA;
  border-radius: 8px;
  padding: 12px;
  color: #333;
  font-size: 14px;
  box-sizing: border-box;
  transition: all 0.3s;
}
.distribution-setting .form-textarea:focus {
  border-color: #FF7600;
  background: #FFF;
  box-shadow: 0 0 0 2px rgba(255, 118, 0, 0.1);
}
.distribution-setting .radio-group {
  margin-top: 0;
}
.distribution-setting .radio-item {
  margin-right: 20px;
}
.distribution-setting .radio-dot {
  width: 16px;
  height: 16px;
  border-radius: 8px;
  border: 1px solid #CCCCCC;
  margin-right: 6px;
}
.distribution-setting .radio-dot-inner {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background: #FF7600;
}
.distribution-setting .radio-item.active .radio-dot {
  border-color: #FF7600;
}
.distribution-setting .radio-label {
  font-size: 14px;
  color: #333;
}
.distribution-setting .value-input-wrap {
  position: relative;
  width: 100%;
}
.distribution-setting .value-input {
  padding-right: 30px;
}
.distribution-setting .value-unit {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  color: #666;
}
.distribution-setting .color-picker {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}
.distribution-setting .color-option {
  width: 36px;
  height: 36px;
  border-radius: 7px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}
.distribution-setting .color-option.active {
  transform: scale(1.05);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}
.distribution-setting .color-check {
  width: 16px;
  height: 16px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
}
.distribution-setting .check-mark {
  width: 8px;
  height: 5px;
  border-left: 1.5px solid #fff;
  border-bottom: 1.5px solid #fff;
  transform: rotate(-45deg);
  margin-top: -1.5px;
}

/* 确认页样式 - 步骤4 */
.summary-card {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  margin-bottom: 15px;
}
.summary-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  padding-bottom: 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}
.summary-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}
.summary-item:last-child {
  margin-bottom: 0;
}
.summary-label {
  font-size: 14px;
  color: #666;
}
.summary-value {
  font-size: 14px;
  color: #333;
  text-align: right;
}

/* 新增样式 */
.preview-card {
  margin-bottom: 20px;
}
.block-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}
.form-actions {
  display: flex;
  justify-content: space-between;
  padding: 0 15px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 70px;
  background: #FFFFFF;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
}
.btn-prev, .btn-create {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  margin: 0 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.2s;
}
.btn-prev {
  background: #F5F5F5;
  color: #666;
}
.btn-create {
  background: linear-gradient(135deg, #FF7600, #FF4D00);
  color: #fff;
}
.btn-prev:active, .btn-create:active {
  transform: scale(0.97);
  opacity: 0.9;
}