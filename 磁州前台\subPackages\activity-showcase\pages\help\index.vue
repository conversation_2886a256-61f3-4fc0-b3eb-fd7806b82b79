<template>
  <view class="help-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="15 18 9 12 15 6"></polyline>
          </svg>
        </view>
        <view class="navbar-title">帮助中心</view>
        <view class="navbar-right">
          <view class="search-btn" @click="showSearchModal">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="M21 21l-4.35-4.35"></path>
            </svg>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 搜索框 -->
      <view class="search-section">
        <view class="search-box" @click="showSearchModal">
          <view class="search-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="M21 21l-4.35-4.35"></path>
            </svg>
          </view>
          <text class="search-placeholder">搜索帮助内容</text>
        </view>
      </view>

      <!-- 热门问题 -->
      <view class="hot-questions">
        <view class="section-header">
          <text class="section-title">热门问题</text>
          <text class="section-desc">用户最关心的问题</text>
        </view>

        <view class="questions-list">
          <view 
            class="question-item" 
            v-for="(question, index) in hotQuestions" 
            :key="index"
            @click="navigateToQuestion(question)"
          >
            <view class="question-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                <line x1="12" y1="17" x2="12.01" y2="17"></line>
              </svg>
            </view>
            <text class="question-text">{{ question.title }}</text>
            <view class="question-arrow">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </view>
          </view>
        </view>
      </view>

      <!-- 帮助分类 -->
      <view class="help-categories">
        <view class="section-header">
          <text class="section-title">帮助分类</text>
          <text class="section-desc">按类别查找帮助内容</text>
        </view>

        <view class="categories-grid">
          <view 
            class="category-item" 
            v-for="(category, index) in helpCategories" 
            :key="index"
            @click="navigateToCategory(category)"
          >
            <view class="category-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path v-if="category.type === 'order'" d="M9 22H15C20 22 22 20 22 15V9C22 4 20 2 15 2H9C4 2 2 4 2 9V15C2 20 4 22 9 22Z"></path>
                <path v-else-if="category.type === 'payment'" d="M21 4H3C2.45 4 2 4.45 2 5V19C2 19.55 2.45 20 3 20H21C21.55 20 22 19.55 22 19V5C22 4.45 21.55 4 21 4Z"></path>
                <path v-else-if="category.type === 'account'" d="M20 21V19C20 16.79 18.21 15 16 15H8C5.79 15 4 16.79 4 19V21M16 7C16 9.21 14.21 11 12 11S8 9.21 8 7S9.79 3 12 3S16 4.79 16 7Z"></path>
                <path v-else-if="category.type === 'product'" d="M21 16V8C21 6.9 20.1 6 19 6H5C3.9 6 3 6.9 3 8V16C3 17.1 3.9 18 5 18H19C20.1 18 21 17.1 21 16Z"></path>
                <circle v-else cx="12" cy="12" r="10"></circle>
              </svg>
            </view>
            <text class="category-name">{{ category.name }}</text>
            <text class="category-count">{{ category.count }}个问题</text>
          </view>
        </view>
      </view>

      <!-- 联系客服 -->
      <view class="contact-service">
        <view class="section-header">
          <text class="section-title">联系客服</text>
          <text class="section-desc">多种方式联系我们</text>
        </view>

        <view class="contact-methods">
          <view 
            class="contact-item" 
            v-for="(method, index) in contactMethods" 
            :key="index"
            @click="handleContact(method)"
          >
            <view class="contact-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path v-if="method.type === 'chat'" d="M21 11.5A8.38 8.38 0 0 1 20.1 15.3A8.5 8.5 0 0 1 12.6 20A8.38 8.38 0 0 1 8.8 19.1L3 21L4.9 15.2A8.38 8.38 0 0 1 4 11.5A8.5 8.5 0 0 1 8.7 3.9A8.38 8.38 0 0 1 12.5 3H13A8.48 8.48 0 0 1 21 11V11.5Z"></path>
                <path v-else-if="method.type === 'phone'" d="M22 16.92V18C22 18.55 21.55 19 21 19C9.4 19 0 9.6 0 -2C0 -2.55 0.45 -3 1 -3H2.08C2.6 -3 3.02 -2.64 3.07 -2.12L3.4 0.88C3.44 1.35 3.23 1.8 2.84 2.07L1.37 3.17C2.93 6.34 5.66 9.07 8.83 10.63L9.93 9.16C10.2 8.77 10.65 8.56 11.12 8.6L14.12 8.93C14.64 8.98 15 9.4 15 9.92V11C15 11.55 14.55 12 14 12C12.4 12 11 10.6 11 9"></path>
                <path v-else-if="method.type === 'email'" d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z M22 6L12 13L2 6"></path>
                <circle v-else cx="12" cy="12" r="10"></circle>
              </svg>
            </view>
            <view class="contact-content">
              <text class="contact-title">{{ method.title }}</text>
              <text class="contact-desc">{{ method.description }}</text>
              <text class="contact-time" v-if="method.time">{{ method.time }}</text>
            </view>
            <view class="contact-status" v-if="method.status">
              <view class="status-dot" :class="method.status"></view>
              <text class="status-text">{{ getStatusText(method.status) }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 使用指南 -->
      <view class="user-guide">
        <view class="section-header">
          <text class="section-title">使用指南</text>
          <text class="section-desc">快速上手使用教程</text>
        </view>

        <view class="guide-list">
          <view 
            class="guide-item" 
            v-for="(guide, index) in userGuides" 
            :key="index"
            @click="navigateToGuide(guide)"
          >
            <view class="guide-image">
              <image class="guide-img" :src="guide.image" mode="aspectFill"></image>
            </view>
            <view class="guide-content">
              <text class="guide-title">{{ guide.title }}</text>
              <text class="guide-desc">{{ guide.description }}</text>
              <view class="guide-meta">
                <text class="guide-views">{{ guide.views }}次阅读</text>
                <text class="guide-time">{{ guide.updateTime }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 意见反馈 -->
      <view class="feedback-section">
        <view class="feedback-card">
          <view class="feedback-header">
            <view class="feedback-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 11.5A8.38 8.38 0 0 1 20.1 15.3A8.5 8.5 0 0 1 12.6 20A8.38 8.38 0 0 1 8.8 19.1L3 21L4.9 15.2A8.38 8.38 0 0 1 4 11.5A8.5 8.5 0 0 1 8.7 3.9A8.38 8.38 0 0 1 12.5 3H13A8.48 8.48 0 0 1 21 11V11.5Z"></path>
              </svg>
            </view>
            <view class="feedback-info">
              <text class="feedback-title">意见反馈</text>
              <text class="feedback-desc">您的建议是我们改进的动力</text>
            </view>
          </view>
          <view class="feedback-btn" @click="navigateToFeedback">
            <text class="btn-text">提交反馈</text>
          </view>
        </view>
      </view>

      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>

    <!-- 搜索弹窗 -->
    <view class="search-modal" v-if="showSearch" @click="hideSearchModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">搜索帮助</text>
          <view class="close-btn" @click="hideSearchModal">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </view>
        </view>
        <view class="modal-body">
          <view class="search-input-container">
            <input 
              class="search-input" 
              v-model="searchKeyword" 
              placeholder="请输入关键词搜索"
              @input="handleSearch"
              focus
            />
            <view class="search-btn-modal" @click="performSearch">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="M21 21l-4.35-4.35"></path>
              </svg>
            </view>
          </view>
          
          <view class="search-suggestions" v-if="searchSuggestions.length > 0">
            <text class="suggestions-title">搜索建议</text>
            <view class="suggestions-list">
              <view 
                class="suggestion-item" 
                v-for="(suggestion, index) in searchSuggestions" 
                :key="index"
                @click="selectSuggestion(suggestion)"
              >
                <text class="suggestion-text">{{ suggestion }}</text>
              </view>
            </view>
          </view>

          <view class="search-history" v-if="searchHistory.length > 0">
            <view class="history-header">
              <text class="history-title">搜索历史</text>
              <view class="clear-history" @click="clearHistory">
                <text class="clear-text">清空</text>
              </view>
            </view>
            <view class="history-list">
              <view 
                class="history-item" 
                v-for="(item, index) in searchHistory" 
                :key="index"
                @click="selectSuggestion(item)"
              >
                <text class="history-text">{{ item }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 响应式数据
const showSearch = ref(false)
const searchKeyword = ref('')
const searchSuggestions = ref([])
const searchHistory = ref(['如何下单', '支付方式', '退款流程'])

// 热门问题
const hotQuestions = ref([
  { title: '如何下单购买商品？', id: 1 },
  { title: '支付失败怎么办？', id: 2 },
  { title: '如何申请退款？', id: 3 },
  { title: '订单状态如何查看？', id: 4 },
  { title: '如何联系客服？', id: 5 }
])

// 帮助分类
const helpCategories = ref([
  { name: '订单问题', type: 'order', count: 15 },
  { name: '支付问题', type: 'payment', count: 12 },
  { name: '账户问题', type: 'account', count: 8 },
  { name: '商品问题', type: 'product', count: 10 }
])

// 联系方式
const contactMethods = ref([
  {
    title: '在线客服',
    description: '实时在线解答问题',
    type: 'chat',
    time: '9:00-21:00',
    status: 'online'
  },
  {
    title: '客服热线',
    description: '************',
    type: 'phone',
    time: '9:00-18:00',
    status: 'offline'
  },
  {
    title: '邮件咨询',
    description: '<EMAIL>',
    type: 'email',
    time: '24小时内回复'
  }
])

// 使用指南
const userGuides = ref([
  {
    title: '新手购物指南',
    description: '从注册到下单的完整流程',
    image: '/static/images/help/guide1.jpg',
    views: 1250,
    updateTime: '2024-01-15'
  },
  {
    title: '支付方式说明',
    description: '支持的支付方式及操作步骤',
    image: '/static/images/help/guide2.jpg',
    views: 980,
    updateTime: '2024-01-10'
  },
  {
    title: '售后服务政策',
    description: '退换货流程及注意事项',
    image: '/static/images/help/guide3.jpg',
    views: 756,
    updateTime: '2024-01-08'
  }
])

// 页面加载
onMounted(() => {
  console.log('帮助中心页面加载')
  loadHelpData()
})

// 方法
function goBack() {
  uni.navigateBack()
}

function showSearchModal() {
  showSearch.value = true
}

function hideSearchModal() {
  showSearch.value = false
  searchKeyword.value = ''
  searchSuggestions.value = []
}

function handleSearch() {
  if (searchKeyword.value.trim()) {
    // 模拟搜索建议
    searchSuggestions.value = [
      '如何' + searchKeyword.value,
      searchKeyword.value + '问题',
      searchKeyword.value + '流程'
    ]
  } else {
    searchSuggestions.value = []
  }
}

function performSearch() {
  if (searchKeyword.value.trim()) {
    // 添加到搜索历史
    if (!searchHistory.value.includes(searchKeyword.value)) {
      searchHistory.value.unshift(searchKeyword.value)
      if (searchHistory.value.length > 10) {
        searchHistory.value.pop()
      }
    }
    
    // 执行搜索
    uni.navigateTo({
      url: `/pages/help/search?keyword=${encodeURIComponent(searchKeyword.value)}`
    })
    
    hideSearchModal()
  }
}

function selectSuggestion(suggestion) {
  searchKeyword.value = suggestion
  performSearch()
}

function clearHistory() {
  searchHistory.value = []
}

function navigateToQuestion(question) {
  uni.navigateTo({
    url: `/pages/help/question?id=${question.id}`
  })
}

function navigateToCategory(category) {
  uni.navigateTo({
    url: `/pages/help/category?type=${category.type}`
  })
}

function handleContact(method) {
  switch(method.type) {
    case 'chat':
      if (method.status === 'online') {
        uni.navigateTo({
          url: '/pages/service/chat'
        })
      } else {
        uni.showToast({
          title: '客服不在线',
          icon: 'none'
        })
      }
      break
    case 'phone':
      uni.makePhoneCall({
        phoneNumber: '************'
      })
      break
    case 'email':
      uni.setClipboardData({
        data: '<EMAIL>',
        success: () => {
          uni.showToast({
            title: '邮箱已复制',
            icon: 'success'
          })
        }
      })
      break
  }
}

function getStatusText(status) {
  switch(status) {
    case 'online':
      return '在线'
    case 'offline':
      return '离线'
    default:
      return ''
  }
}

function navigateToGuide(guide) {
  uni.navigateTo({
    url: `/pages/help/guide?title=${encodeURIComponent(guide.title)}`
  })
}

function navigateToFeedback() {
  uni.navigateTo({
    url: '/pages/help/feedback'
  })
}

function loadHelpData() {
  // 模拟加载帮助数据
  setTimeout(() => {
    console.log('帮助数据加载完成')
  }, 500)
}
</script>

<style scoped>
/* 帮助中心样式开始 */
.help-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #607D8B 0%, #455A64 100%);
}

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(96, 125, 139, 0.95);
  backdrop-filter: blur(10px);
  padding-top: var(--status-bar-height, 44px);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
}

.back-btn, .search-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.2);
}

.back-btn svg, .search-btn svg {
  color: white;
}

.navbar-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
}

/* 内容区域样式 */
.content-scroll {
  padding-top: calc(var(--status-bar-height, 44px) + 44px);
  height: 100vh;
}

/* 搜索区域样式 */
.search-section {
  padding: 20px 16px;
}

.search-box {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24px;
}

.search-icon svg {
  color: #666;
}

.search-placeholder {
  font-size: 16px;
  color: #999;
}

/* 热门问题样式 */
.hot-questions {
  margin: 0 16px 20px;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.section-desc {
  display: block;
  font-size: 14px;
  color: #666;
}

.questions-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.question-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.question-item:active {
  background: #e9ecef;
}

.question-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #607D8B;
  border-radius: 16px;
}

.question-icon svg {
  color: white;
}

.question-text {
  flex: 1;
  font-size: 16px;
  color: #333;
}

.question-arrow svg {
  color: #999;
}

/* 帮助分类样式 */
.help-categories {
  margin: 0 16px 20px;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  text-align: center;
  transition: all 0.3s ease;
}

.category-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.category-icon {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #607D8B;
  border-radius: 28px;
  margin-bottom: 12px;
}

.category-icon svg {
  color: white;
}

.category-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.category-count {
  font-size: 12px;
  color: #666;
}

/* 联系客服样式 */
.contact-service {
  margin: 0 16px 20px;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.contact-item:active {
  background: #e9ecef;
}

.contact-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #607D8B;
  border-radius: 20px;
}

.contact-icon svg {
  color: white;
}

.contact-content {
  flex: 1;
}

.contact-title {
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.contact-desc {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 2px;
}

.contact-time {
  display: block;
  font-size: 12px;
  color: #999;
}

.contact-status {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background: #ccc;
}

.status-dot.online {
  background: #4CAF50;
}

.status-dot.offline {
  background: #F44336;
}

.status-text {
  font-size: 12px;
  color: #666;
}

/* 使用指南样式 */
.user-guide {
  margin: 0 16px 20px;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.guide-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.guide-item {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.guide-item:active {
  background: #e9ecef;
}

.guide-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
}

.guide-img {
  width: 100%;
  height: 100%;
}

.guide-content {
  flex: 1;
}

.guide-title {
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
}

.guide-desc {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.guide-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.guide-views, .guide-time {
  font-size: 12px;
  color: #999;
}

/* 意见反馈样式 */
.feedback-section {
  margin: 0 16px 20px;
}

.feedback-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.feedback-header {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.feedback-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #607D8B;
  border-radius: 24px;
}

.feedback-icon svg {
  color: white;
}

.feedback-info {
  flex: 1;
}

.feedback-title {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.feedback-desc {
  display: block;
  font-size: 14px;
  color: #666;
}

.feedback-btn {
  padding: 10px 20px;
  background: #607D8B;
  border-radius: 20px;
}

.btn-text {
  font-size: 14px;
  color: white;
  font-weight: 500;
}

/* 搜索弹窗样式 */
.search-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  z-index: 2000;
  padding-top: 100px;
}

.modal-content {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 400px;
  max-height: 70vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: #f5f5f5;
}

.close-btn svg {
  color: #666;
}

.modal-body {
  padding: 20px;
  max-height: 50vh;
  overflow-y: auto;
}

.search-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f5f5f5;
  border-radius: 24px;
  margin-bottom: 20px;
}

.search-input {
  flex: 1;
  font-size: 16px;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
}

.search-btn-modal {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #607D8B;
  border-radius: 16px;
}

.search-btn-modal svg {
  color: white;
}

.search-suggestions, .search-history {
  margin-bottom: 20px;
}

.suggestions-title, .history-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.history-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.clear-history {
  padding: 4px 8px;
  background: #f5f5f5;
  border-radius: 12px;
}

.clear-text {
  font-size: 12px;
  color: #666;
}

.suggestions-list, .history-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.suggestion-item, .history-item {
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.suggestion-item:active, .history-item:active {
  background: #e9ecef;
}

.suggestion-text, .history-text {
  font-size: 14px;
  color: #333;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  background: transparent;
}
/* 帮助中心样式结束 */
</style>
