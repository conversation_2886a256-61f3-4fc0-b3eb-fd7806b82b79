"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const _sfc_main = {
  __name: "pending-list",
  setup(__props, { expose: __expose }) {
    const pendingList = common_vendor.ref([]);
    const page = common_vendor.ref(1);
    common_vendor.ref(10);
    const hasMore = common_vendor.ref(true);
    const isLoading = common_vendor.ref(false);
    const isRefreshing = common_vendor.ref(false);
    const showAcceleratePopup = common_vendor.ref(false);
    const accelerateOption = common_vendor.ref("pay");
    const currentItem = common_vendor.ref(null);
    const loadData = () => {
      if (isLoading.value)
        return;
      isLoading.value = true;
      setTimeout(() => {
        const mockData = [
          {
            id: "2001",
            type: "长途拼车",
            publishTime: "2023-10-15 16:30",
            startPoint: "磁县政府",
            endPoint: "石家庄火车站",
            departureTime: "明天 10:30",
            seatCount: 3,
            price: 50,
            auditProgress: 45,
            estimatedTime: "30分钟"
          },
          {
            id: "2002",
            type: "上下班拼车",
            publishTime: "2023-10-15 14:15",
            startPoint: "磁县老城区",
            endPoint: "邯郸科技学院",
            departureTime: "工作日 07:30",
            seatCount: 4,
            price: 12,
            auditProgress: 25,
            estimatedTime: "1小时"
          },
          {
            id: "2003",
            type: "短途拼车",
            publishTime: "2023-10-15 11:40",
            startPoint: "磁县体育场",
            endPoint: "磁县汽车站",
            departureTime: "今天 16:00",
            seatCount: 2,
            price: 5,
            auditProgress: 75,
            estimatedTime: "10分钟"
          }
        ];
        if (page.value === 1) {
          pendingList.value = mockData;
        } else {
          pendingList.value = [...pendingList.value, ...mockData];
        }
        if (page.value >= 2) {
          hasMore.value = false;
        }
        isLoading.value = false;
        isRefreshing.value = false;
      }, 1e3);
    };
    const loadMore = () => {
      if (!hasMore.value || isLoading.value)
        return;
      page.value++;
      loadData();
    };
    const onRefresh = () => {
      isRefreshing.value = true;
      page.value = 1;
      hasMore.value = true;
      loadData();
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const goPublish = () => {
      common_vendor.index.navigateTo({
        url: "/carpool-package/pages/carpool/publish/index"
      });
    };
    const deleteItem = (item) => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要删除此条信息吗？",
        success: (res) => {
          if (res.confirm) {
            pendingList.value = pendingList.value.filter((i) => i.id !== item.id);
            common_vendor.index.showToast({
              title: "删除成功",
              icon: "success"
            });
          }
        }
      });
    };
    const editItem = (item) => {
      common_vendor.index.navigateTo({
        url: `/carpool-package/pages/carpool/publish/index?id=${item.id}&type=edit`
      });
    };
    const accelerateAudit = (item) => {
      currentItem.value = item;
      showAcceleratePopup.value = true;
    };
    const closePopup = () => {
      showAcceleratePopup.value = false;
    };
    const selectAccelerateOption = (option) => {
      accelerateOption.value = option;
    };
    const confirmAccelerate = () => {
      if (accelerateOption.value === "pay") {
        common_vendor.index.showLoading({
          title: "支付中..."
        });
        setTimeout(() => {
          common_vendor.index.hideLoading();
          handleAccelerateSuccess();
        }, 1500);
      } else {
        common_vendor.index.showLoading({
          title: "加载广告中..."
        });
        setTimeout(() => {
          common_vendor.index.hideLoading();
          common_vendor.index.showModal({
            title: "广告观看完成",
            content: "感谢您观看广告，您的审核已加速处理！",
            showCancel: false,
            success: () => {
              handleAccelerateSuccess();
            }
          });
        }, 1500);
      }
    };
    const handleAccelerateSuccess = () => {
      if (!currentItem.value)
        return;
      pendingList.value = pendingList.value.map((item) => {
        if (item.id === currentItem.value.id) {
          return {
            ...item,
            auditProgress: 90,
            estimatedTime: "5分钟"
          };
        }
        return item;
      });
      common_vendor.index.showToast({
        title: "加速成功",
        icon: "success"
      });
      closePopup();
    };
    common_vendor.onMounted(() => {
      loadData();
    });
    __expose({
      loadData,
      goPublish
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$28,
        b: common_vendor.o(goBack),
        c: common_vendor.f(pendingList.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.type),
            b: common_vendor.t(item.publishTime),
            c: common_vendor.t(item.startPoint),
            d: common_vendor.t(item.endPoint),
            e: common_vendor.t(item.departureTime),
            f: common_vendor.t(item.seatCount),
            g: item.price
          }, item.price ? {
            h: common_assets._imports_3$27,
            i: common_vendor.t(item.price)
          } : {}, {
            j: item.auditProgress + "%",
            k: common_vendor.t(item.estimatedTime),
            l: common_vendor.o(($event) => deleteItem(item), item.id),
            m: common_vendor.o(($event) => editItem(item), item.id),
            n: common_vendor.o(($event) => accelerateAudit(item), item.id),
            o: item.id
          });
        }),
        d: common_assets._imports_1$33,
        e: common_assets._imports_2$29,
        f: pendingList.value.length === 0 && !isLoading.value
      }, pendingList.value.length === 0 && !isLoading.value ? {
        g: common_assets._imports_4$21,
        h: common_vendor.o(goPublish)
      } : {}, {
        i: isLoading.value && !isRefreshing.value
      }, isLoading.value && !isRefreshing.value ? {} : {}, {
        j: pendingList.value.length > 0 && !hasMore.value
      }, pendingList.value.length > 0 && !hasMore.value ? {} : {}, {
        k: common_vendor.o(loadMore),
        l: common_vendor.o(onRefresh),
        m: isRefreshing.value,
        n: showAcceleratePopup.value
      }, showAcceleratePopup.value ? {
        o: common_vendor.o(closePopup)
      } : {}, {
        p: showAcceleratePopup.value
      }, showAcceleratePopup.value ? {
        q: common_vendor.o(closePopup),
        r: common_assets._imports_5$19,
        s: accelerateOption.value === "pay",
        t: common_vendor.o(($event) => selectAccelerateOption("pay")),
        v: common_assets._imports_6$17,
        w: accelerateOption.value === "ad",
        x: common_vendor.o(($event) => selectAccelerateOption("ad")),
        y: common_vendor.o(closePopup),
        z: common_vendor.o(confirmAccelerate)
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-e7b40776"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/carpool-package/pages/carpool/my/pending-list.js.map
