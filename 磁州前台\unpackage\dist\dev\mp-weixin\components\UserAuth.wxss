/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.user-auth {
  width: 100%;
}
.auth-btn {
  background-color: #07c160;
  color: #ffffff;
  border-radius: 44rpx;
  font-size: 28rpx;
  height: 88rpx;
  line-height: 88rpx;
  width: 100%;
  text-align: center;
  padding: 0;
  margin: 0;
  border: none;
}
.auth-btn::after {
  border: none;
}
.auth-btn-text {
  font-size: 28rpx;
  font-weight: 500;
}
.nickname-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.nickname-container {
  width: 560rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
}
.nickname-header {
  padding: 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #eee;
}
.nickname-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.nickname-content {
  padding: 30rpx;
}
.nickname-tip {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 20rpx;
}
.nickname-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.nickname-footer {
  display: flex;
  border-top: 1rpx solid #eee;
}
.cancel-btn, .confirm-btn {
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-size: 30rpx;
  margin: 0;
  padding: 0;
  border-radius: 0;
  background-color: #fff;
}
.cancel-btn {
  color: #666;
  border-right: 1rpx solid #eee;
}
.confirm-btn {
  color: #07c160;
  font-weight: 500;
}
.cancel-btn::after, .confirm-btn::after {
  border: none;
}