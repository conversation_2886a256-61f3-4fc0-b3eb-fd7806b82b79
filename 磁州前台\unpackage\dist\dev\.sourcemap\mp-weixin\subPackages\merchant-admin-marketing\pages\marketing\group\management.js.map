{"version": 3, "file": "management.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/group/management.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xncm91cFxtYW5hZ2VtZW50LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"group-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">拼团活动</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 顶部操作区 -->\n    <view class=\"top-actions\">\n      <CreateButton text=\"创建拼团\" theme=\"group\" @click=\"createNewGroup\" />\n    </view>\n    \n    <!-- 拼团系统开关 -->\n    <view class=\"system-switch\">\n      <view class=\"switch-content\">\n        <text class=\"switch-title\">拼团系统</text>\n        <text class=\"switch-desc\">开启后，用户可以参与商品拼团活动</text>\n      </view>\n      <switch :checked=\"groupSystemEnabled\" @change=\"toggleGroupSystem\" color=\"#7E30E1\" />\n    </view>\n    \n    <!-- 拼团数据概览 -->\n    <view class=\"overview-section\">\n      <view class=\"overview-header\">\n        <text class=\"section-title\">拼团数据概览</text>\n        <view class=\"date-picker\" @click=\"showDatePicker\">\n          <text class=\"date-text\">{{dateRange}}</text>\n          <view class=\"date-icon\"></view>\n        </view>\n      </view>\n      \n      <view class=\"stats-cards\">\n        <view class=\"stats-card\">\n          <view class=\"card-icon groups\">\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\" stroke=\"#9040FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <circle cx=\"9\" cy=\"7\" r=\"4\" stroke=\"#9040FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M23 21v-2a4 4 0 0 0-3-3.87\" stroke=\"#9040FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M16 3.13a4 4 0 0 1 0 7.75\" stroke=\"#9040FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            </svg>\n          </view>\n          <view class=\"card-value\">{{groupData.totalGroups}}</view>\n          <view class=\"card-label\">拼团总数</view>\n          <view class=\"card-trend\" :class=\"groupData.groupsTrend\">\n            <view class=\"trend-arrow\"></view>\n            <text class=\"trend-value\">{{groupData.groupsGrowth}}</text>\n          </view>\n        </view>\n        \n        <view class=\"stats-card\">\n          <view class=\"card-icon rate\">\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M22 11.08V12a10 10 0 1 1-5.93-9.14\" stroke=\"#34C759\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M22 4 12 14.01l-3-3\" stroke=\"#34C759\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            </svg>\n          </view>\n          <view class=\"card-value\">{{groupData.successRate}}%</view>\n          <view class=\"card-label\">成团率</view>\n          <view class=\"card-trend\" :class=\"groupData.successRateTrend\">\n            <view class=\"trend-arrow\"></view>\n            <text class=\"trend-value\">{{groupData.successRateGrowth}}</text>\n          </view>\n        </view>\n        \n        <view class=\"stats-card\">\n          <view class=\"card-icon revenue\">\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M12 1v22M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\" stroke=\"#FF9500\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            </svg>\n          </view>\n          <view class=\"card-value\">¥{{formatNumber(groupData.totalRevenue)}}</view>\n          <view class=\"card-label\">拼团收入</view>\n          <view class=\"card-trend\" :class=\"groupData.revenueTrend\">\n            <view class=\"trend-arrow\"></view>\n            <text class=\"trend-value\">{{groupData.revenueGrowth}}</text>\n          </view>\n        </view>\n        \n        <view class=\"stats-card\">\n          <view class=\"card-icon participants\">\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\" stroke=\"#007AFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <circle cx=\"9\" cy=\"7\" r=\"4\" stroke=\"#007AFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            </svg>\n          </view>\n          <view class=\"card-value\">{{groupData.participantsCount}}</view>\n          <view class=\"card-label\">参与人数</view>\n          <view class=\"card-trend\" :class=\"groupData.participantsTrend\">\n            <view class=\"trend-arrow\"></view>\n            <text class=\"trend-value\">{{groupData.participantsGrowth}}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 进行中的拼团活动 -->\n    <view class=\"active-groups-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">进行中的拼团</text>\n        <text class=\"view-all\" @click=\"viewAllGroups\">查看全部</text>\n      </view>\n      \n      <view class=\"group-list\">\n        <view class=\"group-item\" v-for=\"(group, index) in activeGroups\" :key=\"index\" @click=\"viewGroupDetail(group)\">\n          <image class=\"group-image\" :src=\"group.image\" mode=\"aspectFill\"></image>\n          <view class=\"group-content\">\n            <view class=\"group-title-row\">\n              <text class=\"group-name\">{{group.name}}</text>\n              <view class=\"group-status\" :class=\"group.statusClass\">{{group.statusText}}</view>\n            </view>\n            <view class=\"group-info\">\n              <view class=\"info-item\">\n                <text class=\"info-label\">原价：</text>\n                <text class=\"info-value original-price\">¥{{group.originalPrice}}</text>\n              </view>\n              <view class=\"info-item\">\n                <text class=\"info-label\">拼团价：</text>\n                <text class=\"info-value group-price\">¥{{group.groupPrice}}</text>\n              </view>\n              <view class=\"info-item\">\n                <text class=\"info-label\">成团人数：</text>\n                <text class=\"info-value\">{{group.requiredMembers}}人</text>\n              </view>\n              <view class=\"info-item\">\n                <text class=\"info-label\">剩余时间：</text>\n                <text class=\"info-value time-left\">{{group.timeLeft}}</text>\n              </view>\n            </view>\n            <view class=\"group-progress\">\n              <view class=\"progress-text\">\n                <text>已参与：{{group.currentMembers}}/{{group.requiredMembers}}人</text>\n                <text>{{group.progressPercent}}%</text>\n              </view>\n              <view class=\"progress-bar\">\n                <view class=\"progress-fill\" :style=\"{ width: group.progressPercent + '%' }\"></view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 拼团设置 -->\n    <view class=\"settings-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">拼团设置</text>\n      </view>\n      \n      <view class=\"settings-list\">\n        <view class=\"settings-item\" @click=\"navigateToSetting('rules')\">\n          <view class=\"item-left\">\n            <view class=\"item-icon rules\"></view>\n            <text class=\"item-title\">拼团规则</text>\n          </view>\n          <view class=\"item-right\">\n            <text class=\"item-value\">{{groupSettings.rulesCount}}项规则</text>\n            <view class=\"item-arrow\"></view>\n          </view>\n        </view>\n        \n        <view class=\"settings-item\" @click=\"navigateToSetting('time')\">\n          <view class=\"item-left\">\n            <view class=\"item-icon time\"></view>\n            <text class=\"item-title\">拼团时间</text>\n          </view>\n          <view class=\"item-right\">\n            <text class=\"item-value\">{{groupSettings.timeLimit}}小时</text>\n            <view class=\"item-arrow\"></view>\n          </view>\n        </view>\n        \n        <view class=\"settings-item\" @click=\"navigateToSetting('discount')\">\n          <view class=\"item-left\">\n            <view class=\"item-icon discount\"></view>\n            <text class=\"item-title\">拼团折扣</text>\n          </view>\n          <view class=\"item-right\">\n            <text class=\"item-value\">{{groupSettings.discountRange}}</text>\n            <view class=\"item-arrow\"></view>\n          </view>\n        </view>\n        \n        <view class=\"settings-item\" @click=\"navigateToSetting('notification')\">\n          <view class=\"item-left\">\n            <view class=\"item-icon notification\"></view>\n            <text class=\"item-title\">通知设置</text>\n          </view>\n          <view class=\"item-right\">\n            <text class=\"item-value\">{{groupSettings.notificationEnabled ? '已开启' : '未开启'}}</text>\n            <view class=\"item-arrow\"></view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 拼团工具 -->\n    <view class=\"tools-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">拼团工具</text>\n      </view>\n      \n      <view class=\"tools-grid\">\n        <view class=\"tool-card\" v-for=\"(tool, index) in groupTools\" :key=\"index\" @click=\"useTool(tool)\">\n          <view class=\"tool-icon\" :style=\"{ background: tool.color }\">\n            <view class=\"tool-icon-svg\" v-html=\"tool.svg\"></view>\n          </view>\n          <text class=\"tool-name\">{{tool.name}}</text>\n          <text class=\"tool-desc\">{{tool.description}}</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport CreateButton from '/subPackages/merchant-admin-marketing/components/CreateButton.vue';\n\nexport default {\n  components: {\n    CreateButton\n  },\n  data() {\n    return {\n      groupSystemEnabled: true,\n      dateRange: '2023-04-01 ~ 2023-04-30',\n      \n      // 拼团数据\n      groupData: {\n        totalGroups: 24,\n        groupsTrend: 'up',\n        groupsGrowth: '12%',\n        \n        successRate: 85,\n        successRateTrend: 'up',\n        successRateGrowth: '5%',\n        \n        totalRevenue: 12580.50,\n        revenueTrend: 'up',\n        revenueGrowth: '18%',\n        \n        participantsCount: 156,\n        participantsTrend: 'up',\n        participantsGrowth: '15%'\n      },\n      \n      // 进行中的拼团\n      activeGroups: [\n        {\n          id: 1,\n          name: 'iPhone 14 Pro Max',\n          image: '/static/images/products/iphone.jpg',\n          originalPrice: '9999.00',\n          groupPrice: '8799.00',\n          requiredMembers: 3,\n          currentMembers: 2,\n          progressPercent: 66,\n          timeLeft: '12小时23分',\n          statusText: '进行中',\n          statusClass: 'active'\n        },\n        {\n          id: 2,\n          name: 'MacBook Air M2',\n          image: '/static/images/products/macbook.jpg',\n          originalPrice: '7999.00',\n          groupPrice: '7299.00',\n          requiredMembers: 5,\n          currentMembers: 3,\n          progressPercent: 60,\n          timeLeft: '5小时47分',\n          statusText: '即将结束',\n          statusClass: 'ending'\n        },\n        {\n          id: 3,\n          name: '四菜一汤家庭套餐',\n          image: '/static/images/products/food-package.jpg',\n          originalPrice: '168.00',\n          groupPrice: '99.00',\n          requiredMembers: 2,\n          currentMembers: 1,\n          progressPercent: 50,\n          timeLeft: '23小时59分',\n          statusText: '套餐拼团',\n          statusClass: 'package'\n        }\n      ],\n      \n      // 拼团设置\n      groupSettings: {\n        rulesCount: 5,\n        timeLimit: 24,\n        discountRange: '7折-9折',\n        notificationEnabled: true\n      },\n      \n      // 拼团工具\n      groupTools: [\n        {\n          name: '创建拼团',\n          description: '创建新的拼团活动',\n          color: '#34C759',\n          svg: '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-4.41 0-8 3.59-8 8s3.59 8 8 8 8-3.59 8-8-3.59-8-8-8zm4 10c0 2.21-1.79 4-4 4s-4-1.79-4-4 1.79-4 4-4 4 1.79 4 4zm0-6c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4z\"/></svg>'\n        },\n        {\n          name: '拼团管理',\n          description: '管理拼团活动',\n          color: '#FF3B30',\n          svg: '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-4.41 0-8 3.59-8 8s3.59 8 8 8 8-3.59 8-8-3.59-8-8-8zm4 10c0 2.21-1.79 4-4 4s-4-1.79-4-4 1.79-4 4-4 4 1.79 4 4zm0-6c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4z\"/></svg>'\n        },\n        {\n          name: '数据统计',\n          description: '查看拼团相关数据',\n          color: '#007AFF',\n          svg: '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z\"/></svg>'\n        },\n        {\n          name: '团购套餐',\n          description: '管理团购套餐',\n          color: '#FF9500',\n          svg: '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3\"></path><rect x=\"9\" y=\"9\" width=\"6\" height=\"6\"></rect></svg>'\n        }\n      ]\n    }\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack();\n    },\n    showHelp() {\n      uni.showToast({\n        title: '拼团活动帮助',\n        icon: 'none'\n      });\n    },\n    toggleGroupSystem(e) {\n      this.groupSystemEnabled = e.detail.value;\n      uni.showToast({\n        title: this.groupSystemEnabled ? '已开启拼团系统' : '已关闭拼团系统',\n        icon: 'none'\n      });\n    },\n    showDatePicker() {\n      // 显示日期选择器\n      uni.showToast({\n        title: '日期选择功能开发中',\n        icon: 'none'\n      });\n    },\n    formatNumber(num) {\n      return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });\n    },\n    viewAllGroups() {\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/group/list'\n      });\n    },\n    viewGroupDetail(group) {\n      uni.navigateTo({\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/group/detail?id=${group.id}`\n      });\n    },\n    navigateToSetting(setting) {\n      // 实现导航到设置页面的逻辑\n      uni.showToast({\n        title: `导航到${setting}设置页面`,\n        icon: 'none'\n      });\n    },\n    useTool(tool) {\n      // 实现使用工具的逻辑\n      if (tool.name === '创建拼团') {\n        uni.navigateTo({\n          url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create'\n        });\n      } else if (tool.name === '拼团管理') {\n        uni.navigateTo({\n          url: '/subPackages/merchant-admin-marketing/pages/marketing/group/list'\n        });\n      } else if (tool.name === '数据统计') {\n        uni.navigateTo({\n          url: '/subPackages/merchant-admin-marketing/pages/marketing/group/statistics'\n        });\n      } else if (tool.name === '团购套餐') {\n        uni.navigateTo({\n          url: '/subPackages/merchant-admin-marketing/pages/marketing/group/package-management'\n        });\n      } else {\n        uni.showToast({\n          title: `使用${tool.name}工具`,\n          icon: 'none'\n        });\n      }\n    },\n    createNewGroup() {\n      // 跳转到创建拼团活动页面\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create'\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.group-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: 30px;\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #9040FF, #5E35B1);\n  color: #fff;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: bold;\n}\n\n/* 顶部操作区样式 */\n.top-actions {\n  position: sticky;\n  top: 100px; /* 确保在导航栏下方 */\n  left: 0;\n  right: 0;\n  background: #FFFFFF;\n  padding: 15px 16px;\n  display: flex;\n  justify-content: flex-end;\n  z-index: 90;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n}\n\n/* 系统开关样式 */\n.system-switch {\n  margin: 15px;\n  padding: 15px;\n  background: #FFFFFF;\n  border-radius: 12px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.switch-content {\n  flex: 1;\n}\n\n.switch-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.switch-desc {\n  font-size: 12px;\n  color: #999;\n}\n\n/* 概览部分样式 */\n.overview-section {\n  margin: 15px;\n  padding: 15px;\n  background: #FFFFFF;\n  border-radius: 16px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);\n  border: 1px solid rgba(0, 0, 0, 0.03);\n}\n\n.overview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  position: relative;\n  padding-left: 8px;\n}\n\n.section-title::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 3px;\n  height: 16px;\n  background: linear-gradient(to bottom, #9040FF, #5E35B1);\n  border-radius: 3px;\n}\n\n.date-picker {\n  display: flex;\n  align-items: center;\n  background: rgba(144, 64, 255, 0.08);\n  border-radius: 20px;\n  padding: 6px 12px;\n  transition: all 0.3s ease;\n}\n\n.date-picker:hover {\n  background: rgba(144, 64, 255, 0.12);\n}\n\n.date-text {\n  font-size: 12px;\n  color: #9040FF;\n  margin-right: 6px;\n  font-weight: 500;\n}\n\n.date-icon {\n  width: 6px;\n  height: 6px;\n  border-top: 1.5px solid #9040FF;\n  border-right: 1.5px solid #9040FF;\n  transform: rotate(135deg);\n}\n\n/* 数据卡片样式 */\n.stats-cards {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 10px;\n  margin-top: 10px;\n}\n\n.stats-card {\n  background: #F8FAFC;\n  border-radius: 12px;\n  padding: 12px;\n  position: relative;\n  border: 1px solid rgba(0, 0, 0, 0.03);\n  display: flex;\n  flex-direction: column;\n  transition: all 0.3s ease;\n}\n\n.stats-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\n}\n\n.card-value {\n  font-size: 20px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 4px;\n}\n\n.card-label {\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 4px;\n}\n\n.card-trend {\n  display: flex;\n  align-items: center;\n  font-size: 11px;\n  margin-top: auto;\n}\n\n.card-trend.up {\n  color: #34C759;\n}\n\n.card-trend.down {\n  color: #FF3B30;\n}\n\n.trend-arrow {\n  width: 0;\n  height: 0;\n  margin-right: 4px;\n}\n\n.up .trend-arrow {\n  border-left: 3px solid transparent;\n  border-right: 3px solid transparent;\n  border-bottom: 5px solid #34C759;\n}\n\n.down .trend-arrow {\n  border-left: 3px solid transparent;\n  border-right: 3px solid transparent;\n  border-top: 5px solid #FF3B30;\n}\n\n.card-icon {\n  width: 28px;\n  height: 28px;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 8px;\n}\n\n.card-icon.groups {\n  background-color: rgba(144, 64, 255, 0.1);\n}\n\n.card-icon.rate {\n  background-color: rgba(52, 199, 89, 0.1);\n}\n\n.card-icon.revenue {\n  background-color: rgba(255, 149, 0, 0.1);\n}\n\n.card-icon.participants {\n  background-color: rgba(0, 122, 255, 0.1);\n}\n\n/* 进行中的拼团活动样式 */\n.active-groups-section {\n  margin: 15px;\n  padding: 15px;\n  background: #FFFFFF;\n  border-radius: 12px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.view-all {\n  font-size: 14px;\n  color: #9040FF;\n}\n\n.group-list {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.group-item {\n  display: flex;\n  background: #F8FAFC;\n  border-radius: 10px;\n  overflow: hidden;\n  position: relative;\n}\n\n.group-image {\n  width: 100px;\n  height: 100px;\n  object-fit: cover;\n}\n\n.group-content {\n  flex: 1;\n  padding: 10px;\n}\n\n.group-title-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 5px;\n}\n\n.group-name {\n  font-size: 15px;\n  font-weight: 600;\n  color: #333;\n  margin-right: 60px;\n}\n\n.group-status {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  font-size: 12px;\n  padding: 2px 8px;\n  border-radius: 10px;\n}\n\n.group-status.active {\n  background: rgba(52, 199, 89, 0.1);\n  color: #34C759;\n}\n\n.group-status.ending {\n  background: rgba(255, 149, 0, 0.1);\n  color: #FF9500;\n}\n\n.group-status.package {\n  background: rgba(144, 64, 255, 0.1);\n  color: #9040FF;\n}\n\n.group-info {\n  margin-bottom: 10px;\n}\n\n.info-item {\n  display: flex;\n  font-size: 12px;\n  margin-bottom: 3px;\n}\n\n.info-label {\n  color: #999;\n  width: 65px;\n}\n\n.info-value {\n  color: #666;\n}\n\n.original-price {\n  text-decoration: line-through;\n  color: #999;\n}\n\n.group-price {\n  color: #FF3B30;\n  font-weight: 600;\n}\n\n.time-left {\n  color: #FF9500;\n}\n\n.group-progress {\n  margin-top: 5px;\n}\n\n.progress-text {\n  display: flex;\n  justify-content: space-between;\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 5px;\n}\n\n.progress-bar {\n  height: 4px;\n  background-color: #EBEDF5;\n  border-radius: 2px;\n  overflow: hidden;\n}\n\n.progress-fill {\n  height: 100%;\n  background: linear-gradient(90deg, #9040FF, #5E35B1);\n  border-radius: 2px;\n}\n\n/* 拼团设置样式 */\n.settings-section {\n  margin: 15px;\n  padding: 15px;\n  background: #FFFFFF;\n  border-radius: 12px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n}\n\n.section-header {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 15px;\n}\n\n.settings-list {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -7.5px;\n}\n\n.settings-item {\n  width: 50%;\n  padding: 7.5px;\n  box-sizing: border-box;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 7.5px;\n}\n\n.item-left {\n  display: flex;\n  align-items: center;\n}\n\n.item-icon {\n  width: 24px;\n  height: 24px;\n  margin-right: 10px;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.item-icon.rules {\n  background-color: rgba(126, 48, 225, 0.1);\n  position: relative;\n}\n\n.item-icon.rules::before {\n  content: '';\n  width: 12px;\n  height: 12px;\n  border: 2px solid #7E30E1;\n  border-radius: 2px;\n}\n\n.item-icon.time {\n  background-color: rgba(52, 199, 89, 0.1);\n  position: relative;\n}\n\n.item-icon.time::before {\n  content: '';\n  width: 12px;\n  height: 12px;\n  border: 2px solid #34C759;\n  border-radius: 6px;\n}\n\n.item-icon.discount {\n  background-color: rgba(255, 59, 48, 0.1);\n  position: relative;\n}\n\n.item-icon.discount::before {\n  content: '%';\n  color: #FF3B30;\n  font-size: 14px;\n  font-weight: bold;\n}\n\n.item-icon.notification {\n  background-color: rgba(0, 122, 255, 0.1);\n  position: relative;\n}\n\n.item-icon.notification::before {\n  content: '';\n  width: 12px;\n  height: 12px;\n  border: 2px solid #007AFF;\n  border-radius: 6px;\n  position: relative;\n}\n\n.item-icon.notification::after {\n  content: '';\n  position: absolute;\n  width: 6px;\n  height: 6px;\n  background: #007AFF;\n  border-radius: 3px;\n  top: 9px;\n  left: 9px;\n}\n\n.item-title {\n  font-size: 12px;\n  color: #333;\n}\n\n.item-right {\n  display: flex;\n  align-items: center;\n}\n\n.item-value {\n  font-size: 12px;\n  color: #333;\n  margin-right: 10px;\n}\n\n.item-arrow {\n  width: 8px;\n  height: 8px;\n  border-top: 2px solid #333;\n  border-right: 2px solid #333;\n  transform: rotate(45deg);\n}\n\n/* 拼团工具样式 */\n.tools-section {\n  margin: 15px;\n  padding: 15px 12px;\n  background: #FFFFFF;\n  border-radius: 12px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n}\n\n.tools-grid {\n  display: flex;\n  justify-content: space-between;\n  margin: 0 -2px;\n}\n\n.tool-card {\n  flex: 1;\n  padding: 4px 2px;\n  box-sizing: border-box;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 0;\n}\n\n.tool-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 10px;\n  background-color: #7E30E1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 8px;\n}\n\n.tool-icon-svg {\n  width: 20px;\n  height: 20px;\n}\n\n.tool-name {\n  font-size: 12px;\n  font-weight: 600;\n  color: #333;\n  white-space: nowrap;\n  margin-bottom: 4px;\n}\n\n.tool-desc {\n  font-size: 10px;\n  color: #999;\n  text-align: center;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  max-width: 90px;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/group/management.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA6NA,MAAO,eAAc,MAAW;AAEhC,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,oBAAoB;AAAA,MACpB,WAAW;AAAA;AAAA,MAGX,WAAW;AAAA,QACT,aAAa;AAAA,QACb,aAAa;AAAA,QACb,cAAc;AAAA,QAEd,aAAa;AAAA,QACb,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QAEnB,cAAc;AAAA,QACd,cAAc;AAAA,QACd,eAAe;AAAA,QAEf,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,oBAAoB;AAAA,MACrB;AAAA;AAAA,MAGD,cAAc;AAAA,QACZ;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,eAAe;AAAA,UACf,YAAY;AAAA,UACZ,iBAAiB;AAAA,UACjB,gBAAgB;AAAA,UAChB,iBAAiB;AAAA,UACjB,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,eAAe;AAAA,UACf,YAAY;AAAA,UACZ,iBAAiB;AAAA,UACjB,gBAAgB;AAAA,UAChB,iBAAiB;AAAA,UACjB,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,eAAe;AAAA,UACf,YAAY;AAAA,UACZ,iBAAiB;AAAA,UACjB,gBAAgB;AAAA,UAChB,iBAAiB;AAAA,UACjB,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,aAAa;AAAA,QACf;AAAA,MACD;AAAA;AAAA,MAGD,eAAe;AAAA,QACb,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,eAAe;AAAA,QACf,qBAAqB;AAAA,MACtB;AAAA;AAAA,MAGD,YAAY;AAAA,QACV;AAAA,UACE,MAAM;AAAA,UACN,aAAa;AAAA,UACb,OAAO;AAAA,UACP,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,aAAa;AAAA,UACb,OAAO;AAAA,UACP,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,aAAa;AAAA,UACb,OAAO;AAAA,UACP,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,aAAa;AAAA,UACb,OAAO;AAAA,UACP,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,kBAAkB,GAAG;AACnB,WAAK,qBAAqB,EAAE,OAAO;AACnCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,KAAK,qBAAqB,YAAY;AAAA,QAC7C,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,iBAAiB;AAEfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,aAAa,KAAK;AAChB,aAAO,IAAI,eAAe,SAAS,EAAE,uBAAuB,GAAG,uBAAuB,EAAA,CAAG;AAAA,IAC1F;AAAA,IACD,gBAAgB;AACdA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IACD,gBAAgB,OAAO;AACrBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,yEAAyE,MAAM,EAAE;AAAA,MACxF,CAAC;AAAA,IACF;AAAA,IACD,kBAAkB,SAAS;AAEzBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,MAAM,OAAO;AAAA,QACpB,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,QAAQ,MAAM;AAEZ,UAAI,KAAK,SAAS,QAAQ;AACxBA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK;AAAA,QACP,CAAC;AAAA,iBACQ,KAAK,SAAS,QAAQ;AAC/BA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK;AAAA,QACP,CAAC;AAAA,iBACQ,KAAK,SAAS,QAAQ;AAC/BA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK;AAAA,QACP,CAAC;AAAA,iBACQ,KAAK,SAAS,QAAQ;AAC/BA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK;AAAA,QACP,CAAC;AAAA,aACI;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,KAAK,KAAK,IAAI;AAAA,UACrB,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACD;AAAA,IACD,iBAAiB;AAEfA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtZA,GAAG,WAAW,eAAe;"}