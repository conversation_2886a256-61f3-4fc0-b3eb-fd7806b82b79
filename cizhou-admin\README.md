# 🏢 磁州生活网后台管理系统

## 📋 项目概述

磁州生活网后台管理系统是一个企业级的微服务架构管理平台，用于管理磁州生活网前台的所有业务数据和运营活动。

### 🎯 项目特色

- **企业级架构**: 微服务 + 云原生设计
- **高性能**: 支持高并发，响应时间 < 200ms
- **高可用**: 99.9%+ 系统可用性
- **安全可靠**: OAuth2 + JWT + RBAC权限控制
- **智能化**: AI辅助开发，代码质量保证

### 🛠️ 技术栈

#### 前端技术栈
- **框架**: Vue 3.4+ + TypeScript 5.0+
- **构建工具**: Vite 5.0+
- **UI组件**: Element Plus 2.4+
- **状态管理**: Pinia 2.0+
- **路由管理**: Vue Router 4.0+
- **样式框架**: Tailwind CSS 3.0+
- **图表库**: ECharts 5.0+

#### 后端技术栈
- **框架**: Spring Boot 3.2+ + Spring Cloud 2023.0+
- **安全框架**: Spring Security 6.0+
- **数据访问**: MyBatis-Plus 3.5+
- **数据库**: MySQL 8.0+ + Redis 7.0+ + MongoDB 7.0+
- **消息队列**: RabbitMQ 3.12+
- **服务注册**: Nacos 2.3+
- **API网关**: Spring Cloud Gateway

#### 基础设施
- **容器化**: Docker 24.0+ + Kubernetes 1.28+
- **监控**: Prometheus + Grafana + ELK Stack
- **CI/CD**: GitLab CI/CD + Harbor
- **云服务**: 阿里云/腾讯云

### 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                        用户层                                │
├─────────────────────────────────────────────────────────────┤
│                     Web浏览器                               │
├─────────────────────────────────────────────────────────────┤
│                      接入层                                  │
│              Nginx负载均衡 + API网关                         │
├─────────────────────────────────────────────────────────────┤
│                      应用层                                  │
│  用户服务 │ 商家服务 │ 订单服务 │ 内容服务 │ 营销服务 │ 数据服务  │
├─────────────────────────────────────────────────────────────┤
│                      数据层                                  │
│           MySQL主库 │ Redis缓存 │ MongoDB文档库              │
└─────────────────────────────────────────────────────────────┘
```

### 📊 业务模块

#### 核心管理模块
- **用户管理**: C端用户管理、管理员管理、权限管理
- **商家管理**: 商家入驻、审核、运营管理
- **内容管理**: 信息发布、内容审核、分类管理
- **订单管理**: 订单处理、支付管理、退款管理

#### 业务运营模块
- **营销管理**: 活动管理、优惠券管理、推广工具
- **数据分析**: 实时统计、报表分析、决策支持
- **系统管理**: 系统配置、日志管理、监控告警

### 🚀 快速开始

#### 环境要求
- Node.js 18+
- Java 17+
- MySQL 8.0+
- Redis 7.0+
- Docker 24.0+

#### 前端启动
```bash
cd cizhou-admin-frontend
npm install
npm run dev
```

#### 后端启动
```bash
cd cizhou-admin-backend
mvn clean install
mvn spring-boot:run
```

#### Docker启动
```bash
docker-compose up -d
```

### 📈 性能指标

- **响应时间**: API接口 < 200ms (P95)
- **并发能力**: 支持1000+并发用户
- **可用性**: 99.9%+系统可用性
- **吞吐量**: 10000+ QPS

### 🔒 安全特性

- **身份认证**: OAuth2 + JWT Token
- **权限控制**: RBAC细粒度权限
- **数据加密**: 传输和存储加密
- **安全审计**: 完整操作日志
- **防护机制**: XSS、CSRF、SQL注入防护

### 📝 开发规范

#### 代码质量
- 测试覆盖率 > 90%
- ESLint + Prettier 代码规范
- SonarQube 质量检查
- 零安全漏洞容忍

#### Git规范
- 分支管理: GitFlow
- 提交规范: Conventional Commits
- 代码审查: Pull Request
- 自动化测试: CI/CD

### 📚 文档

- [系统架构设计](./docs/architecture.md)
- [API接口文档](./docs/api.md)
- [数据库设计](./docs/database.md)
- [部署指南](./docs/deployment.md)
- [开发指南](./docs/development.md)

### 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

### 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

### 📞 联系我们

- 项目地址: [GitHub](https://github.com/cizhou/admin-system)
- 问题反馈: [Issues](https://github.com/cizhou/admin-system/issues)
- 邮箱: <EMAIL>

---

**磁州生活网后台管理系统** - 企业级微服务管理平台 🚀
