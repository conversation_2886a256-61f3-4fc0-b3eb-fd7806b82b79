/**
 * 安全工具类 - 用于处理敏感信息
 */

import CryptoJS from 'crypto-js';

// 加密密钥，实际应用中应从环境变量或安全存储获取
const SECRET_KEY = process.env.VUE_APP_SECRET_KEY || 'default-key-for-development-only';

/**
 * 加密数据
 * @param {any} data 需要加密的数据
 * @returns {string} 加密后的字符串
 */
export const encryptData = (data) => {
  const jsonStr = JSON.stringify(data);
  return CryptoJS.AES.encrypt(jsonStr, SECRET_KEY).toString();
};

/**
 * 解密数据
 * @param {string} encryptedStr 加密的字符串
 * @returns {any} 解密后的数据
 */
export const decryptData = (encryptedStr) => {
  try {
    const bytes = CryptoJS.AES.decrypt(encryptedStr, SECRET_KEY);
    const decryptedStr = bytes.toString(CryptoJS.enc.Utf8);
    return JSON.parse(decryptedStr);
  } catch (error) {
    console.error('解密失败', error);
    return null;
  }
};

/**
 * 安全存储数据到本地
 * @param {string} key 存储键名
 * @param {any} value 存储的值
 */
export const secureStorage = {
  set: (key, value) => {
    const encryptedValue = encryptData(value);
    uni.setStorageSync(key, encryptedValue);
  },
  
  get: (key) => {
    const encryptedValue = uni.getStorageSync(key);
    if (!encryptedValue) return null;
    return decryptData(encryptedValue);
  },
  
  remove: (key) => {
    uni.removeStorageSync(key);
  }
};

/**
 * 掩码处理敏感信息
 * @param {string} text 需要处理的文本
 * @param {number} visibleStart 开始可见的字符数
 * @param {number} visibleEnd 结尾可见的字符数
 * @returns {string} 掩码处理后的文本
 */
export const maskSensitiveInfo = (text, visibleStart = 3, visibleEnd = 4) => {
  if (!text) return '';
  if (text.length <= visibleStart + visibleEnd) {
    return '*'.repeat(text.length);
  }
  
  const start = text.substring(0, visibleStart);
  const end = text.substring(text.length - visibleEnd);
  const masked = '*'.repeat(text.length - visibleStart - visibleEnd);
  
  return start + masked + end;
};

export default {
  encryptData,
  decryptData,
  secureStorage,
  maskSensitiveInfo
}; 