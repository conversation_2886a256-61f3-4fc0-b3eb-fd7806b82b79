{"version": 3, "file": "list.js", "sources": ["pages/red-packet/list.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcmVkLXBhY2tldC9saXN0LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"red-packet-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\">\r\n      <view class=\"navbar-left\" @click=\"goBack\">\r\n        <text class=\"iconfont icon-back\"></text>\r\n      </view>\r\n      <view class=\"navbar-title\">抢红包</view>\r\n      <view class=\"navbar-right\">\r\n        <text class=\"iconfont icon-filter\" @click=\"showFilter\"></text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 顶部统计信息 -->\r\n    <view class=\"stats-section\">\r\n      <view class=\"stats-card\">\r\n        <view class=\"stats-value\">{{totalAmount}}元</view>\r\n        <view class=\"stats-label\">累计发放</view>\r\n      </view>\r\n      <view class=\"stats-card\">\r\n        <view class=\"stats-value\">{{myAmount}}元</view>\r\n        <view class=\"stats-label\">我的收益</view>\r\n      </view>\r\n      <view class=\"stats-card\">\r\n        <view class=\"stats-value\">{{todayCount}}个</view>\r\n        <view class=\"stats-label\">今日红包</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 红包类型选择 -->\r\n    <view class=\"filter-tabs\">\r\n      <view \r\n        class=\"filter-tab\" \r\n        :class=\"{'active': activeTab === 'all'}\"\r\n        @click=\"switchTab('all')\">\r\n        全部红包\r\n      </view>\r\n      <view \r\n        class=\"filter-tab\" \r\n        :class=\"{'active': activeTab === 'merchant'}\"\r\n        @click=\"switchTab('merchant')\">\r\n        商家红包\r\n      </view>\r\n      <view \r\n        class=\"filter-tab\" \r\n        :class=\"{'active': activeTab === 'platform'}\"\r\n        @click=\"switchTab('platform')\">\r\n        平台红包\r\n      </view>\r\n      <view \r\n        class=\"filter-tab\" \r\n        :class=\"{'active': activeTab === 'info'}\"\r\n        @click=\"switchTab('info')\">\r\n        信息红包\r\n      </view>\r\n      <view \r\n        class=\"filter-tab\" \r\n        :class=\"{'active': activeTab === 'nearby'}\"\r\n        @click=\"switchTab('nearby')\">\r\n        附近红包\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 红包列表 -->\r\n    <scroll-view \r\n      scroll-y \r\n      class=\"red-packet-list\"\r\n      @scrolltolower=\"loadMore\"\r\n      :refresher-enabled=\"true\"\r\n      :refresher-triggered=\"isRefreshing\"\r\n      @refresherrefresh=\"onRefresh\">\r\n      \r\n      <view v-if=\"loading && !redPackets.length\" class=\"loading-placeholder\">\r\n        <uni-load-more status=\"loading\" :contentText=\"loadingText\"></uni-load-more>\r\n      </view>\r\n      \r\n      <view v-else-if=\"!redPackets.length\" class=\"empty-state\">\r\n        <image class=\"empty-image\" src=\"/static/images/empty-redpacket.png\" mode=\"aspectFit\"></image>\r\n        <text class=\"empty-text\">暂无红包，下拉刷新试试</text>\r\n      </view>\r\n      \r\n      <view v-else>\r\n        <view \r\n          class=\"red-packet-item\" \r\n          :class=\"{'info-red-packet-item': item.isInfoRedPacket}\"\r\n          v-for=\"(item, index) in redPackets\" \r\n          :key=\"item.id\"\r\n          @click=\"navigateToDetail(item)\">\r\n          \r\n          <!-- 商家/用户信息 -->\r\n          <view class=\"merchant-info\">\r\n            <image class=\"merchant-avatar\" :src=\"item.merchantAvatar\" mode=\"aspectFill\"></image>\r\n            <view class=\"merchant-details\">\r\n              <text class=\"merchant-name\">{{item.merchantName}}</text>\r\n              <text class=\"publish-time\">{{item.isInfoRedPacket ? item.time : formatTime(item.publishTime)}}</text>\r\n            </view>\r\n            <view class=\"info-type-tag\" v-if=\"item.isInfoRedPacket\">{{item.category}}</view>\r\n          </view>\r\n          \r\n          <!-- 内容区域 - 针对信息红包简化 -->\r\n          <view class=\"content-section\" :class=\"{'info-content-section': item.isInfoRedPacket}\">\r\n            <!-- 信息红包 -->\r\n            <template v-if=\"item.isInfoRedPacket\">\r\n              <text class=\"info-title\">{{item.content}}</text>\r\n              \r\n              <!-- 红包信息简化展示 -->\r\n              <view class=\"info-red-packet-bar\">\r\n                <view class=\"red-packet-icon-mini\">\r\n                  <image src=\"/static/images/tabbar/抢红包.gif\" mode=\"aspectFit\"></image>\r\n                </view>\r\n                <text class=\"red-packet-amount-mini\">¥{{item.redPacketAmount}} · 剩{{item.redPacketRemain}}个</text>\r\n                <view class=\"grab-button-mini\">抢</view>\r\n              </view>\r\n            </template>\r\n            \r\n            <!-- 普通红包保持原样 -->\r\n            <template v-else>\r\n              <view class=\"info-content\">\r\n                <view class=\"title-row\">\r\n                  <text class=\"title\">{{item.title}}</text>\r\n                </view>\r\n                <text class=\"description\">{{item.description}}</text>\r\n                \r\n                <view class=\"image-list\" v-if=\"item.images && item.images.length\">\r\n                  <image \r\n                    v-for=\"(img, imgIndex) in item.images.slice(0, 3)\" \r\n                    :key=\"imgIndex\"\r\n                    :src=\"img\"\r\n                    mode=\"aspectFill\"\r\n                    class=\"content-image\"\r\n                    @click.stop=\"previewImage(item.images, imgIndex)\"></image>\r\n                  <view class=\"image-count\" v-if=\"item.images.length > 3\">+{{item.images.length - 3}}</view>\r\n                </view>\r\n              </view>\r\n              \r\n              <view class=\"red-packet-info\">\r\n                <view class=\"red-packet-icon\">\r\n                  <image src=\"/static/images/red-packet-icon.png\" mode=\"aspectFit\"></image>\r\n                </view>\r\n                <view class=\"red-packet-details\">\r\n                  <text class=\"packet-amount\">¥{{item.amount}}</text>\r\n                  <text class=\"packet-type\">{{getPacketTypeText(item.packetType)}}</text>\r\n                </view>\r\n                <view class=\"packet-status\" :class=\"getStatusClass(item.status)\">\r\n                  <text>{{getStatusText(item.status)}}</text>\r\n                </view>\r\n              </view>\r\n            </template>\r\n          </view>\r\n          \r\n          <!-- 底部操作栏 - 仅为普通红包显示 -->\r\n          <view class=\"action-bar\" v-if=\"!item.isInfoRedPacket\">\r\n            <view class=\"action-stats\">\r\n              <text class=\"stat-item\">\r\n                <text class=\"iconfont icon-view\"></text>\r\n                {{item.viewCount}}\r\n              </text>\r\n              <text class=\"stat-item\">\r\n                <text class=\"iconfont icon-comment\"></text>\r\n                {{item.commentCount}}\r\n              </text>\r\n              <text class=\"stat-item\">\r\n                <text class=\"iconfont icon-like\"></text>\r\n                {{item.likeCount}}\r\n              </text>\r\n            </view>\r\n            <view class=\"action-buttons\">\r\n              <button \r\n                class=\"grab-btn\"\r\n                :class=\"{'disabled': item.status !== 0 || item.hasGrabbed}\"\r\n                @click.stop=\"grabRedPacket(item, index)\">\r\n                {{item.hasGrabbed ? '已领取' : (item.status === 0 ? '抢红包' : '已结束')}}\r\n              </button>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <uni-load-more :status=\"loadMoreStatus\" :contentText=\"loadMoreText\"></uni-load-more>\r\n      </view>\r\n    </scroll-view>\r\n    \r\n    <!-- 底部发布按钮 -->\r\n    <view class=\"publish-btn\" @click=\"navigateToPublish\">\r\n      <text class=\"iconfont icon-add\"></text>\r\n      <text>发布红包</text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { formatTime, formatDistance } from '@/utils/format';\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      activeTab: 'all',\r\n      redPackets: [],\r\n      loading: true,\r\n      isRefreshing: false,\r\n      loadMoreStatus: 'more', // more, loading, noMore\r\n      page: 1,\r\n      pageSize: 10,\r\n      totalAmount: '0.00',\r\n      myAmount: '0.00',\r\n      todayCount: 0,\r\n      loadingText: {\r\n        contentdown: '上拉加载更多',\r\n        contentrefresh: '加载中...',\r\n        contentnomore: '没有更多了'\r\n      },\r\n      loadMoreText: {\r\n        contentdown: '上拉加载更多',\r\n        contentrefresh: '加载中...',\r\n        contentnomore: '没有更多了'\r\n      }\r\n    };\r\n  },\r\n  \r\n  onLoad(options) {\r\n    // 如果有tab参数，则切换到对应标签\r\n    if (options && options.tab) {\r\n      this.activeTab = options.tab;\r\n    }\r\n    \r\n    this.loadRedPackets();\r\n    this.loadStats();\r\n  },\r\n  \r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    \r\n    showFilter() {\r\n      uni.showToast({\r\n        title: '筛选功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    \r\n    switchTab(tab) {\r\n      if (this.activeTab === tab) return;\r\n      \r\n      this.activeTab = tab;\r\n      this.redPackets = [];\r\n      this.page = 1;\r\n      this.loadRedPackets();\r\n    },\r\n    \r\n    async loadStats() {\r\n      try {\r\n        // 模拟数据，实际项目中应该从API获取\r\n        this.totalAmount = '8,888.88';\r\n        this.myAmount = '128.88';\r\n        this.todayCount = 56;\r\n      } catch (error) {\r\n        console.error('加载统计数据失败', error);\r\n      }\r\n    },\r\n    \r\n    async loadRedPackets(isRefresh = false) {\r\n      if (isRefresh) {\r\n        this.page = 1;\r\n      }\r\n      \r\n      if (this.page === 1) {\r\n        this.loading = true;\r\n      } else {\r\n        this.loadMoreStatus = 'loading';\r\n      }\r\n      \r\n      try {\r\n        // 模拟API请求，实际项目中应该调用真实API\r\n        await new Promise(resolve => setTimeout(resolve, 1000));\r\n        \r\n        // 模拟数据\r\n        const mockData = this.getMockData();\r\n        \r\n        if (this.page === 1) {\r\n          this.redPackets = mockData;\r\n        } else {\r\n          this.redPackets = [...this.redPackets, ...mockData];\r\n        }\r\n        \r\n        // 模拟没有更多数据\r\n        if (this.page >= 3) {\r\n          this.loadMoreStatus = 'noMore';\r\n        } else {\r\n          this.loadMoreStatus = 'more';\r\n          this.page++;\r\n        }\r\n      } catch (error) {\r\n        console.error('加载红包列表失败', error);\r\n        uni.showToast({\r\n          title: '加载失败，请重试',\r\n          icon: 'none'\r\n        });\r\n      } finally {\r\n        this.loading = false;\r\n        if (isRefresh) {\r\n          this.isRefreshing = false;\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 获取模拟数据\r\n    getMockData() {\r\n      const types = ['normal', 'lucky', 'fixed'];\r\n      const statuses = [0, 0, 0, 0, 1, 2]; // 大部分是进行中的红包\r\n      \r\n      // 根据当前选中的标签筛选数据\r\n      let mockData = [];\r\n      \r\n      if (this.activeTab === 'info') {\r\n        // 信息红包数据\r\n        mockData = Array.from({ length: 10 }, (_, i) => {\r\n          const infoType = this.getRandomInfoType();\r\n          const title = this.getRandomInfoTitle();\r\n          const description = this.getRandomInfoDescription();\r\n          \r\n          return {\r\n            id: `info_rp_${this.page}_${i}`,\r\n            merchantId: `user_${i}`,\r\n            merchantName: `${this.getRandomUserName()}`,\r\n            merchantAvatar: `/static/images/avatar_${(i % 5) + 1}.png`,\r\n            title: title,\r\n            description: description,\r\n            images: this.getRandomImages(i),\r\n            amount: (Math.random() * 50 + 5).toFixed(2),\r\n            packetType: types[Math.floor(Math.random() * types.length)],\r\n            status: statuses[Math.floor(Math.random() * statuses.length)],\r\n            publishTime: new Date(Date.now() - Math.random() * 86400000 * 7).getTime(),\r\n            distance: Math.random() * 5000,\r\n            viewCount: Math.floor(Math.random() * 1000),\r\n            commentCount: Math.floor(Math.random() * 50),\r\n            likeCount: Math.floor(Math.random() * 100),\r\n            hasGrabbed: Math.random() > 0.7,\r\n            infoType: infoType, // 信息类型\r\n            isInfoRedPacket: true, // 标记为信息红包\r\n            \r\n            // 添加与首页信息一致的字段\r\n            category: infoType,\r\n            content: title,\r\n            time: formatTime(new Date(Date.now() - Math.random() * 86400000 * 7).getTime(), 'YYYY-MM-DD HH:mm'),\r\n            views: Math.floor(Math.random() * 1000),\r\n            hasRedPacket: true,\r\n            redPacketAmount: (Math.random() * 50 + 5).toFixed(2),\r\n            redPacketType: types[Math.floor(Math.random() * types.length)],\r\n            redPacketCount: Math.floor(Math.random() * 50) + 10,\r\n            redPacketRemain: Math.floor(Math.random() * 10) + 1\r\n          };\r\n        });\r\n      } else {\r\n        // 原有的红包数据\r\n        mockData = Array.from({ length: 10 }, (_, i) => ({\r\n          id: `rp_${this.page}_${i}`,\r\n          merchantId: `m_${i}`,\r\n          merchantName: `${this.getRandomBusinessName()}`,\r\n          merchantAvatar: `/static/images/avatar_${(i % 5) + 1}.png`,\r\n          title: this.getRandomTitle(),\r\n          description: this.getRandomDescription(),\r\n          images: this.getRandomImages(i),\r\n          amount: (Math.random() * 100 + 5).toFixed(2),\r\n          packetType: types[Math.floor(Math.random() * types.length)],\r\n          status: statuses[Math.floor(Math.random() * statuses.length)],\r\n          publishTime: new Date(Date.now() - Math.random() * 86400000 * 7).getTime(),\r\n          distance: Math.random() * 5000,\r\n          viewCount: Math.floor(Math.random() * 1000),\r\n          commentCount: Math.floor(Math.random() * 50),\r\n          likeCount: Math.floor(Math.random() * 100),\r\n          hasGrabbed: Math.random() > 0.7,\r\n          isInfoRedPacket: false // 标记为非信息红包\r\n        }));\r\n      }\r\n      \r\n      return mockData;\r\n    },\r\n    \r\n    getRandomBusinessName() {\r\n      const names = [\r\n        '品味咖啡馆', '悦享美食城', '鲜果工坊', '时尚服饰店',\r\n        '健康生活馆', '数码科技店', '宠物乐园', '美丽花坊',\r\n        '家居生活馆', '创意礼品店'\r\n      ];\r\n      return names[Math.floor(Math.random() * names.length)];\r\n    },\r\n    \r\n    getRandomTitle() {\r\n      const titles = [\r\n        '开业大酬宾，红包雨来袭！',\r\n        '周年庆典，感恩回馈！',\r\n        '新品上市，抢先体验！',\r\n        '限时特惠，错过等一年！',\r\n        '会员专享，双倍红包！',\r\n        '春节特辑，财神送礼！',\r\n        '夏日清凉，冰爽红包！',\r\n        '金秋送爽，丰收好礼！',\r\n        '冬季温暖，暖心红包！',\r\n        '五一劳动节，犒劳自己！'\r\n      ];\r\n      return titles[Math.floor(Math.random() * titles.length)];\r\n    },\r\n    \r\n    getRandomDescription() {\r\n      const descriptions = [\r\n        '店庆活动期间，凡是到店消费满100元即可参与抽红包活动，最高可得88元现金红包！',\r\n        '为回馈新老顾客，本店特推出线上抢红包活动，抢到红包可直接抵扣消费金额！',\r\n        '关注我们的公众号，参与互动即可获得神秘红包，数量有限，先到先得！',\r\n        '新店开业，发放100个幸运红包，金额随机，最高可得200元！',\r\n        '五星好评送红包，晒图送红包，分享朋友圈再送红包，红包不停！',\r\n        '年终促销，下单立减，还有机会获得平台补贴的超级红包！'\r\n      ];\r\n      return descriptions[Math.floor(Math.random() * descriptions.length)];\r\n    },\r\n    \r\n    getRandomImages(seed) {\r\n      const count = Math.floor(Math.random() * 5) + 1;\r\n      return Array.from({ length: count }, (_, i) => \r\n        `/static/images/sample_${((seed + i) % 8) + 1}.jpg`\r\n      );\r\n    },\r\n    \r\n    formatTime(timestamp) {\r\n      return formatTime(timestamp);\r\n    },\r\n    \r\n    formatDistance(meters) {\r\n      return formatDistance(meters);\r\n    },\r\n    \r\n    getPacketTypeText(type) {\r\n      const typeMap = {\r\n        'normal': '普通红包',\r\n        'lucky': '拼手气红包',\r\n        'fixed': '固定金额'\r\n      };\r\n      return typeMap[type] || '红包';\r\n    },\r\n    \r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        0: '进行中',\r\n        1: '已领完',\r\n        2: '已过期'\r\n      };\r\n      return statusMap[status] || '未知状态';\r\n    },\r\n    \r\n    getStatusClass(status) {\r\n      const classMap = {\r\n        0: 'status-active',\r\n        1: 'status-finished',\r\n        2: 'status-expired'\r\n      };\r\n      return classMap[status] || '';\r\n    },\r\n    \r\n    navigateToDetail(item) {\r\n      // 如果是信息红包，使用与首页相同的导航逻辑\r\n      if (item.isInfoRedPacket) {\r\n        try {\r\n          // 准备参数\r\n          let params = {\r\n            id: encodeURIComponent(item.id),\r\n            category: encodeURIComponent(item.infoType),\r\n            content: encodeURIComponent(item.description || '')\r\n          };\r\n          \r\n          // 根据信息类型选择对应的详情页\r\n          const detailPageMap = {\r\n            '到家服务': 'home-service-detail',\r\n            '寻找服务': 'find-service-detail',\r\n            '生意转让': 'business-transfer-detail',\r\n            '商业转让': 'business-transfer-detail',\r\n            '招聘信息': 'job-detail',\r\n            '求职信息': 'job-seeking-detail',\r\n            '房屋出租': 'house-rent-detail',\r\n            '房屋出售': 'house-sale-detail',\r\n            '二手车辆': 'car-detail',\r\n            '宠物信息': 'pet-detail',\r\n            '车辆服务': 'vehicle-service-detail',\r\n            '二手闲置': 'second-hand-detail',\r\n            '磁州拼车': 'carpool-detail',\r\n            '教育培训': 'education-detail',\r\n            '其他服务': 'info-detail'\r\n          };\r\n          \r\n          const detailPage = detailPageMap[item.infoType] || 'info-detail';\r\n          const url = `/pages/publish/${detailPage}?${Object.entries(params)\r\n            .map(([key, value]) => `${key}=${value}`)\r\n            .join('&')}`;\r\n          \r\n          uni.navigateTo({\r\n            url: url,\r\n            success: () => {\r\n              console.log('成功跳转到信息详情页:', url);\r\n            },\r\n            fail: (e) => {\r\n              console.error('跳转信息详情页失败', e);\r\n              uni.showToast({\r\n                title: '跳转失败，请重试',\r\n                icon: 'none'\r\n              });\r\n            }\r\n          });\r\n        } catch (error) {\r\n          console.error('导航异常', error);\r\n          uni.showToast({\r\n            title: '页面跳转出错',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      } else {\r\n        // 普通红包跳转到红包详情页\r\n        uni.navigateTo({\r\n          url: `/pages/red-packet/detail?id=${item.id}`\r\n        });\r\n      }\r\n    },\r\n    \r\n    navigateToPublish() {\r\n      uni.navigateTo({\r\n        url: '/pages/publish/publish?type=redpacket'\r\n      });\r\n    },\r\n    \r\n    async grabRedPacket(item, index) {\r\n      if (item.status !== 0 || item.hasGrabbed) return;\r\n      \r\n      try {\r\n        // 模拟抢红包请求\r\n        await new Promise(resolve => setTimeout(resolve, 800));\r\n        \r\n        // 随机决定是否抢到\r\n        const success = Math.random() > 0.3;\r\n        \r\n        if (success) {\r\n          const amount = (Math.random() * 10 + 0.5).toFixed(2);\r\n          \r\n          uni.showModal({\r\n            title: '恭喜您',\r\n            content: `抢到了${amount}元红包`,\r\n            showCancel: false,\r\n            success: () => {\r\n              // 更新红包状态\r\n              this.redPackets[index].hasGrabbed = true;\r\n              \r\n              // 更新我的收益\r\n              this.myAmount = (parseFloat(this.myAmount) + parseFloat(amount)).toFixed(2);\r\n            }\r\n          });\r\n        } else {\r\n          uni.showToast({\r\n            title: '手慢了，红包被抢光了',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error('抢红包失败', error);\r\n        uni.showToast({\r\n          title: '网络异常，请重试',\r\n          icon: 'none'\r\n        });\r\n      }\r\n    },\r\n    \r\n    previewImage(images, current) {\r\n      uni.previewImage({\r\n        urls: images,\r\n        current: images[current]\r\n      });\r\n    },\r\n    \r\n    loadMore() {\r\n      if (this.loadMoreStatus !== 'more') return;\r\n      this.loadRedPackets();\r\n    },\r\n    \r\n    onRefresh() {\r\n      this.isRefreshing = true;\r\n      this.loadRedPackets(true);\r\n    },\r\n    \r\n    getRandomUserName() {\r\n      const names = [\r\n        '张先生', '李女士', '王师傅', '赵经理', \r\n        '刘老师', '陈总', '杨女士', '周先生',\r\n        '吴小姐', '郑经理'\r\n      ];\r\n      return names[Math.floor(Math.random() * names.length)];\r\n    },\r\n    \r\n    getRandomInfoTitle() {\r\n      const titles = [\r\n        '急招送餐员，日结工资+红包奖励',\r\n        '招聘销售人员，底薪3500+提成+红包',\r\n        '高薪诚聘厨师，包吃住，红包福利',\r\n        '招聘保洁人员，待遇优厚，红包补贴',\r\n        '诚聘前台接待，形象气质佳，红包奖励',\r\n        '招聘美容师，有经验者优先，红包补助',\r\n        '招聘仓库管理员，有红包福利',\r\n        '诚聘电工，持证上岗，红包奖励',\r\n        '招聘会计，经验丰富，红包补贴',\r\n        '招聘司机，C1以上，红包奖励'\r\n      ];\r\n      return titles[Math.floor(Math.random() * titles.length)];\r\n    },\r\n    \r\n    getRandomInfoDescription() {\r\n      const descriptions = [\r\n        '工作地点：市中心，工作时间：9:00-18:00，周末双休，节假日三倍工资，面试通过即可领取入职红包！',\r\n        '有责任心，能吃苦耐劳，有相关工作经验者优先，完成业绩有额外红包奖励！',\r\n        '要求有相关工作经验，能够独立完成工作任务，有团队合作精神，推荐成功有红包奖励！',\r\n        '工作轻松，环境优雅，五险一金，带薪年假，节日福利，红包不断！',\r\n        '要求：形象气质佳，沟通能力强，有相关工作经验，入职即发红包！',\r\n        '薪资待遇：3500-6000元/月，提供住宿，有红包奖励计划！',\r\n        '招聘岗位多个，薪资面议，有意者带上简历前来面试，红包等你拿！',\r\n        '有相关工作经验者优先，能够适应倒班，有责任心，推荐成功有红包！',\r\n        '要求会基本办公软件操作，有相关工作经验，红包福利多多！',\r\n        '工作稳定，待遇优厚，有晋升空间，入职就有红包奖励！'\r\n      ];\r\n      return descriptions[Math.floor(Math.random() * descriptions.length)];\r\n    },\r\n    \r\n    getRandomInfoType() {\r\n      const types = [\r\n        '招聘信息', '求职信息', '房屋出租', '二手闲置',\r\n        '生意转让', '车辆服务', '到家服务', '教育培训'\r\n      ];\r\n      return types[Math.floor(Math.random() * types.length)];\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.red-packet-container {\r\n  min-height: 100vh;\r\n  background-color: #f7f7f7;\r\n  position: relative;\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.custom-navbar {\r\n  height: 90rpx;\r\n  padding: var(--status-bar-height) 30rpx 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  background: linear-gradient(135deg, #FF6B6B, #FF8E53);\r\n  color: #fff;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 100;\r\n  \r\n  .navbar-left, .navbar-right {\r\n    width: 60rpx;\r\n    height: 60rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    \r\n    .iconfont {\r\n      font-size: 40rpx;\r\n    }\r\n  }\r\n  \r\n  .navbar-title {\r\n    font-size: 36rpx;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n/* 统计信息区域 */\r\n.stats-section {\r\n  padding: 30rpx;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  background: linear-gradient(135deg, #FF6B6B, #FF8E53);\r\n  color: #fff;\r\n  border-bottom-left-radius: 30rpx;\r\n  border-bottom-right-radius: 30rpx;\r\n  box-shadow: 0 6rpx 16rpx rgba(255, 107, 107, 0.2);\r\n  \r\n  .stats-card {\r\n    text-align: center;\r\n    flex: 1;\r\n    \r\n    .stats-value {\r\n      font-size: 40rpx;\r\n      font-weight: bold;\r\n      margin-bottom: 10rpx;\r\n      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\r\n    }\r\n    \r\n    .stats-label {\r\n      font-size: 24rpx;\r\n      opacity: 0.9;\r\n    }\r\n  }\r\n}\r\n\r\n/* 筛选标签 */\r\n.filter-tabs {\r\n  display: flex;\r\n  background-color: #fff;\r\n  padding: 20rpx 30rpx;\r\n  margin: 20rpx;\r\n  border-radius: 16rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n  \r\n  .filter-tab {\r\n    flex: 1;\r\n    text-align: center;\r\n    font-size: 28rpx;\r\n    color: #666;\r\n    padding: 16rpx 0;\r\n    position: relative;\r\n    transition: all 0.3s;\r\n    \r\n    &.active {\r\n      color: #FF6B6B;\r\n      font-weight: 500;\r\n      \r\n      &::after {\r\n        content: '';\r\n        position: absolute;\r\n        bottom: 0;\r\n        left: 50%;\r\n        transform: translateX(-50%);\r\n        width: 40rpx;\r\n        height: 6rpx;\r\n        background-color: #FF6B6B;\r\n        border-radius: 3rpx;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 红包列表 */\r\n.red-packet-list {\r\n  height: calc(100vh - 90rpx - var(--status-bar-height) - 170rpx - 100rpx);\r\n  padding: 0 20rpx;\r\n}\r\n\r\n.loading-placeholder, .empty-state {\r\n  padding: 100rpx 0;\r\n  text-align: center;\r\n  \r\n  .empty-image {\r\n    width: 200rpx;\r\n    height: 200rpx;\r\n    margin-bottom: 30rpx;\r\n  }\r\n  \r\n  .empty-text {\r\n    color: #999;\r\n    font-size: 28rpx;\r\n  }\r\n}\r\n\r\n.red-packet-item {\r\n  background-color: #fff;\r\n  border-radius: 16rpx;\r\n  margin-bottom: 20rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n  \r\n  .merchant-info {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 20rpx;\r\n    \r\n    .merchant-avatar {\r\n      width: 80rpx;\r\n      height: 80rpx;\r\n      border-radius: 50%;\r\n      margin-right: 20rpx;\r\n    }\r\n    \r\n    .merchant-details {\r\n      flex: 1;\r\n      \r\n      .merchant-name {\r\n        font-size: 30rpx;\r\n        font-weight: 500;\r\n        color: #333;\r\n        margin-bottom: 6rpx;\r\n      }\r\n      \r\n      .publish-time {\r\n        font-size: 24rpx;\r\n        color: #999;\r\n      }\r\n    }\r\n    \r\n    .info-type-tag {\r\n      font-size: 22rpx;\r\n      color: #fff;\r\n      background-color: #1677FF;\r\n      padding: 4rpx 12rpx;\r\n      border-radius: 6rpx;\r\n    }\r\n  }\r\n  \r\n  .content-section {\r\n    margin-bottom: 20rpx;\r\n    \r\n    .info-content {\r\n      margin-bottom: 30rpx;\r\n      \r\n      .title-row {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 16rpx;\r\n        \r\n        .title {\r\n          font-size: 32rpx;\r\n          font-weight: 500;\r\n          color: #333;\r\n          line-height: 1.4;\r\n          flex: 1;\r\n        }\r\n      }\r\n      \r\n      .description {\r\n        font-size: 28rpx;\r\n        color: #666;\r\n        line-height: 1.5;\r\n        margin-bottom: 20rpx;\r\n      }\r\n      \r\n      .image-list {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        margin: 0 -5rpx;\r\n        \r\n        .content-image {\r\n          width: calc(33.33% - 10rpx);\r\n          height: 200rpx;\r\n          margin: 5rpx;\r\n          border-radius: 8rpx;\r\n          background-color: #f5f5f5;\r\n        }\r\n        \r\n        .image-count {\r\n          position: absolute;\r\n          right: 20rpx;\r\n          bottom: 20rpx;\r\n          background-color: rgba(0, 0, 0, 0.5);\r\n          color: #fff;\r\n          font-size: 24rpx;\r\n          padding: 6rpx 12rpx;\r\n          border-radius: 20rpx;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .red-packet-info {\r\n      display: flex;\r\n      align-items: center;\r\n      background-color: #FFF7F7;\r\n      padding: 20rpx;\r\n      border-radius: 12rpx;\r\n      border: 1rpx solid #FFE0E0;\r\n      \r\n      .red-packet-icon {\r\n        width: 80rpx;\r\n        height: 80rpx;\r\n        margin-right: 20rpx;\r\n        \r\n        image {\r\n          width: 100%;\r\n          height: 100%;\r\n        }\r\n      }\r\n      \r\n      .red-packet-details {\r\n        flex: 1;\r\n        \r\n        .packet-amount {\r\n          font-size: 36rpx;\r\n          color: #FF4D4F;\r\n          font-weight: bold;\r\n          margin-bottom: 6rpx;\r\n        }\r\n        \r\n        .packet-type {\r\n          font-size: 24rpx;\r\n          color: #FF7875;\r\n        }\r\n      }\r\n      \r\n      .packet-status {\r\n        padding: 8rpx 16rpx;\r\n        border-radius: 20rpx;\r\n        font-size: 24rpx;\r\n        \r\n        &.status-active {\r\n          background-color: #FF4D4F;\r\n          color: #fff;\r\n        }\r\n        \r\n        &.status-finished {\r\n          background-color: #999;\r\n          color: #fff;\r\n        }\r\n        \r\n        &.status-expired {\r\n          background-color: #ccc;\r\n          color: #fff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .action-bar {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding-top: 20rpx;\r\n    border-top: 1rpx solid #f5f5f5;\r\n    \r\n    .action-stats {\r\n      display: flex;\r\n      \r\n      .stat-item {\r\n        margin-right: 30rpx;\r\n        font-size: 24rpx;\r\n        color: #999;\r\n        display: flex;\r\n        align-items: center;\r\n        \r\n        .iconfont {\r\n          margin-right: 6rpx;\r\n          font-size: 28rpx;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .action-buttons {\r\n      .grab-btn {\r\n        background: linear-gradient(135deg, #FF4D4F, #FF7875);\r\n        color: #fff;\r\n        font-size: 26rpx;\r\n        padding: 10rpx 30rpx;\r\n        border-radius: 30rpx;\r\n        border: none;\r\n        box-shadow: 0 4rpx 8rpx rgba(255, 77, 79, 0.2);\r\n        \r\n        &.disabled {\r\n          background: #ccc;\r\n          box-shadow: none;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 发布按钮 */\r\n.publish-btn {\r\n  position: fixed;\r\n  bottom: 40rpx;\r\n  right: 40rpx;\r\n  width: 180rpx;\r\n  height: 80rpx;\r\n  background: linear-gradient(135deg, #FF4D4F, #FF7875);\r\n  color: #fff;\r\n  border-radius: 40rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 28rpx;\r\n  box-shadow: 0 6rpx 16rpx rgba(255, 77, 79, 0.3);\r\n  z-index: 99;\r\n  \r\n  .iconfont {\r\n    margin-right: 10rpx;\r\n    font-size: 32rpx;\r\n  }\r\n}\r\n\r\n/* 信息红包特有样式 - 精简版 */\r\n.info-red-packet-item {\r\n  border-left: 4rpx solid #FF4D4F;\r\n  padding: 20rpx;\r\n}\r\n\r\n.info-content-section {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.info-title {\r\n  font-size: 30rpx;\r\n  font-weight: 500;\r\n  color: #333;\r\n  line-height: 1.4;\r\n  margin-bottom: 16rpx;\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-line-clamp: 2;\r\n  overflow: hidden;\r\n}\r\n\r\n.info-type-tag {\r\n  font-size: 22rpx;\r\n  color: #fff;\r\n  background-color: #1677FF;\r\n  padding: 4rpx 12rpx;\r\n  border-radius: 6rpx;\r\n}\r\n\r\n.info-red-packet-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-top: 16rpx;\r\n  \r\n  .red-packet-icon-mini {\r\n    width: 40rpx;\r\n    height: 40rpx;\r\n    margin-right: 10rpx;\r\n    \r\n    image {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  \r\n  .red-packet-amount-mini {\r\n    flex: 1;\r\n    font-size: 26rpx;\r\n    color: #FF4D4F;\r\n  }\r\n  \r\n  .grab-button-mini {\r\n    background: linear-gradient(135deg, #FF4D4F, #FF7875);\r\n    color: #fff;\r\n    font-size: 24rpx;\r\n    width: 50rpx;\r\n    height: 50rpx;\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    box-shadow: 0 2rpx 6rpx rgba(255, 77, 79, 0.2);\r\n  }\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/red-packet/list.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "formatTime", "formatDistance"], "mappings": ";;;;AAgMA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,YAAY,CAAE;AAAA,MACd,SAAS;AAAA,MACT,cAAc;AAAA,MACd,gBAAgB;AAAA;AAAA,MAChB,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,aAAa;AAAA,QACX,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,eAAe;AAAA,MAChB;AAAA,MACD,cAAc;AAAA,QACZ,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,eAAe;AAAA,MACjB;AAAA;EAEH;AAAA,EAED,OAAO,SAAS;AAEd,QAAI,WAAW,QAAQ,KAAK;AAC1B,WAAK,YAAY,QAAQ;AAAA,IAC3B;AAEA,SAAK,eAAc;AACnB,SAAK,UAAS;AAAA,EACf;AAAA,EAED,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IAED,aAAa;AACXA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IAED,UAAU,KAAK;AACb,UAAI,KAAK,cAAc;AAAK;AAE5B,WAAK,YAAY;AACjB,WAAK,aAAa;AAClB,WAAK,OAAO;AACZ,WAAK,eAAc;AAAA,IACpB;AAAA,IAED,MAAM,YAAY;AAChB,UAAI;AAEF,aAAK,cAAc;AACnB,aAAK,WAAW;AAChB,aAAK,aAAa;AAAA,MAClB,SAAO,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,oCAAA,YAAY,KAAK;AAAA,MACjC;AAAA,IACD;AAAA,IAED,MAAM,eAAe,YAAY,OAAO;AACtC,UAAI,WAAW;AACb,aAAK,OAAO;AAAA,MACd;AAEA,UAAI,KAAK,SAAS,GAAG;AACnB,aAAK,UAAU;AAAA,aACV;AACL,aAAK,iBAAiB;AAAA,MACxB;AAEA,UAAI;AAEF,cAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAI,CAAC;AAGtD,cAAM,WAAW,KAAK;AAEtB,YAAI,KAAK,SAAS,GAAG;AACnB,eAAK,aAAa;AAAA,eACb;AACL,eAAK,aAAa,CAAC,GAAG,KAAK,YAAY,GAAG,QAAQ;AAAA,QACpD;AAGA,YAAI,KAAK,QAAQ,GAAG;AAClB,eAAK,iBAAiB;AAAA,eACjB;AACL,eAAK,iBAAiB;AACtB,eAAK;AAAA,QACP;AAAA,MACA,SAAO,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,oCAAA,YAAY,KAAK;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH,UAAU;AACR,aAAK,UAAU;AACf,YAAI,WAAW;AACb,eAAK,eAAe;AAAA,QACtB;AAAA,MACF;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AACZ,YAAM,QAAQ,CAAC,UAAU,SAAS,OAAO;AACzC,YAAM,WAAW,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAGlC,UAAI,WAAW,CAAA;AAEf,UAAI,KAAK,cAAc,QAAQ;AAE7B,mBAAW,MAAM,KAAK,EAAE,QAAQ,MAAM,CAAC,GAAG,MAAM;AAC9C,gBAAM,WAAW,KAAK;AACtB,gBAAM,QAAQ,KAAK;AACnB,gBAAM,cAAc,KAAK;AAEzB,iBAAO;AAAA,YACL,IAAI,WAAW,KAAK,IAAI,IAAI,CAAC;AAAA,YAC7B,YAAY,QAAQ,CAAC;AAAA,YACrB,cAAc,GAAG,KAAK,kBAAmB,CAAA;AAAA,YACzC,gBAAgB,yBAA0B,IAAI,IAAK,CAAC;AAAA,YACpD;AAAA,YACA;AAAA,YACA,QAAQ,KAAK,gBAAgB,CAAC;AAAA,YAC9B,SAAS,KAAK,OAAM,IAAK,KAAK,GAAG,QAAQ,CAAC;AAAA,YAC1C,YAAY,MAAM,KAAK,MAAM,KAAK,OAAS,IAAE,MAAM,MAAM,CAAC;AAAA,YAC1D,QAAQ,SAAS,KAAK,MAAM,KAAK,OAAO,IAAI,SAAS,MAAM,CAAC;AAAA,YAC5D,aAAa,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,WAAW,QAAW,CAAC,EAAE,QAAS;AAAA,YAC1E,UAAU,KAAK,OAAM,IAAK;AAAA,YAC1B,WAAW,KAAK,MAAM,KAAK,OAAM,IAAK,GAAI;AAAA,YAC1C,cAAc,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE;AAAA,YAC3C,WAAW,KAAK,MAAM,KAAK,OAAM,IAAK,GAAG;AAAA,YACzC,YAAY,KAAK,OAAM,IAAK;AAAA,YAC5B;AAAA;AAAA,YACA,iBAAiB;AAAA;AAAA;AAAA,YAGjB,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAMC,aAAAA,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,OAAM,IAAK,QAAW,CAAC,EAAE,QAAO,GAAI,kBAAkB;AAAA,YAClG,OAAO,KAAK,MAAM,KAAK,OAAM,IAAK,GAAI;AAAA,YACtC,cAAc;AAAA,YACd,kBAAkB,KAAK,OAAM,IAAK,KAAK,GAAG,QAAQ,CAAC;AAAA,YACnD,eAAe,MAAM,KAAK,MAAM,KAAK,OAAO,IAAI,MAAM,MAAM,CAAC;AAAA,YAC7D,gBAAgB,KAAK,MAAM,KAAK,WAAW,EAAE,IAAI;AAAA,YACjD,iBAAiB,KAAK,MAAM,KAAK,OAAO,IAAI,EAAE,IAAI;AAAA;QAEtD,CAAC;AAAA,aACI;AAEL,mBAAW,MAAM,KAAK,EAAE,QAAQ,MAAM,CAAC,GAAG,OAAO;AAAA,UAC/C,IAAI,MAAM,KAAK,IAAI,IAAI,CAAC;AAAA,UACxB,YAAY,KAAK,CAAC;AAAA,UAClB,cAAc,GAAG,KAAK,sBAAuB,CAAA;AAAA,UAC7C,gBAAgB,yBAA0B,IAAI,IAAK,CAAC;AAAA,UACpD,OAAO,KAAK,eAAgB;AAAA,UAC5B,aAAa,KAAK,qBAAsB;AAAA,UACxC,QAAQ,KAAK,gBAAgB,CAAC;AAAA,UAC9B,SAAS,KAAK,OAAM,IAAK,MAAM,GAAG,QAAQ,CAAC;AAAA,UAC3C,YAAY,MAAM,KAAK,MAAM,KAAK,OAAS,IAAE,MAAM,MAAM,CAAC;AAAA,UAC1D,QAAQ,SAAS,KAAK,MAAM,KAAK,OAAO,IAAI,SAAS,MAAM,CAAC;AAAA,UAC5D,aAAa,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,WAAW,QAAW,CAAC,EAAE,QAAS;AAAA,UAC1E,UAAU,KAAK,OAAM,IAAK;AAAA,UAC1B,WAAW,KAAK,MAAM,KAAK,OAAM,IAAK,GAAI;AAAA,UAC1C,cAAc,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE;AAAA,UAC3C,WAAW,KAAK,MAAM,KAAK,OAAM,IAAK,GAAG;AAAA,UACzC,YAAY,KAAK,OAAM,IAAK;AAAA,UAC5B,iBAAiB;AAAA;AAAA,QAClB,EAAC;AAAA,MACJ;AAEA,aAAO;AAAA,IACR;AAAA,IAED,wBAAwB;AACtB,YAAM,QAAQ;AAAA,QACZ;AAAA,QAAS;AAAA,QAAS;AAAA,QAAQ;AAAA,QAC1B;AAAA,QAAS;AAAA,QAAS;AAAA,QAAQ;AAAA,QAC1B;AAAA,QAAS;AAAA;AAEX,aAAO,MAAM,KAAK,MAAM,KAAK,WAAW,MAAM,MAAM,CAAC;AAAA,IACtD;AAAA,IAED,iBAAiB;AACf,YAAM,SAAS;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAEF,aAAO,OAAO,KAAK,MAAM,KAAK,WAAW,OAAO,MAAM,CAAC;AAAA,IACxD;AAAA,IAED,uBAAuB;AACrB,YAAM,eAAe;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAEF,aAAO,aAAa,KAAK,MAAM,KAAK,WAAW,aAAa,MAAM,CAAC;AAAA,IACpE;AAAA,IAED,gBAAgB,MAAM;AACpB,YAAM,QAAQ,KAAK,MAAM,KAAK,WAAW,CAAC,IAAI;AAC9C,aAAO,MAAM;AAAA,QAAK,EAAE,QAAQ,MAAO;AAAA,QAAE,CAAC,GAAG,MACvC,0BAA2B,OAAO,KAAK,IAAK,CAAC;AAAA;IAEhD;AAAA,IAED,WAAW,WAAW;AACpB,aAAOA,aAAAA,WAAW,SAAS;AAAA,IAC5B;AAAA,IAED,eAAe,QAAQ;AACrB,aAAOC,aAAAA,eAAe,MAAM;AAAA,IAC7B;AAAA,IAED,kBAAkB,MAAM;AACtB,YAAM,UAAU;AAAA,QACd,UAAU;AAAA,QACV,SAAS;AAAA,QACT,SAAS;AAAA;AAEX,aAAO,QAAQ,IAAI,KAAK;AAAA,IACzB;AAAA,IAED,cAAc,QAAQ;AACpB,YAAM,YAAY;AAAA,QAChB,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA;AAEL,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA,IAED,eAAe,QAAQ;AACrB,YAAM,WAAW;AAAA,QACf,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA;AAEL,aAAO,SAAS,MAAM,KAAK;AAAA,IAC5B;AAAA,IAED,iBAAiB,MAAM;AAErB,UAAI,KAAK,iBAAiB;AACxB,YAAI;AAEF,cAAI,SAAS;AAAA,YACX,IAAI,mBAAmB,KAAK,EAAE;AAAA,YAC9B,UAAU,mBAAmB,KAAK,QAAQ;AAAA,YAC1C,SAAS,mBAAmB,KAAK,eAAe,EAAE;AAAA;AAIpD,gBAAM,gBAAgB;AAAA,YACpB,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA;AAGV,gBAAM,aAAa,cAAc,KAAK,QAAQ,KAAK;AACnD,gBAAM,MAAM,kBAAkB,UAAU,IAAI,OAAO,QAAQ,MAAM,EAC9D,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,GAAG,GAAG,IAAI,KAAK,EAAE,EACvC,KAAK,GAAG,CAAC;AAEZF,wBAAAA,MAAI,WAAW;AAAA,YACb;AAAA,YACA,SAAS,MAAM;AACbA,4BAAA,MAAA,MAAA,OAAA,oCAAY,eAAe,GAAG;AAAA,YAC/B;AAAA,YACD,MAAM,CAAC,MAAM;AACXA,4BAAc,MAAA,MAAA,SAAA,oCAAA,aAAa,CAAC;AAC5BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACD,SAAO,OAAO;AACdA,wBAAA,MAAA,MAAA,SAAA,oCAAc,QAAQ,KAAK;AAC3BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,aACK;AAELA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,+BAA+B,KAAK,EAAE;AAAA,QAC7C,CAAC;AAAA,MACH;AAAA,IACD;AAAA,IAED,oBAAoB;AAClBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IAED,MAAM,cAAc,MAAM,OAAO;AAC/B,UAAI,KAAK,WAAW,KAAK,KAAK;AAAY;AAE1C,UAAI;AAEF,cAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;AAGrD,cAAM,UAAU,KAAK,OAAM,IAAK;AAEhC,YAAI,SAAS;AACX,gBAAM,UAAU,KAAK,OAAM,IAAK,KAAK,KAAK,QAAQ,CAAC;AAEnDA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS,MAAM,MAAM;AAAA,YACrB,YAAY;AAAA,YACZ,SAAS,MAAM;AAEb,mBAAK,WAAW,KAAK,EAAE,aAAa;AAGpC,mBAAK,YAAY,WAAW,KAAK,QAAQ,IAAI,WAAW,MAAM,GAAG,QAAQ,CAAC;AAAA,YAC5E;AAAA,UACF,CAAC;AAAA,eACI;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,oCAAc,SAAS,KAAK;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACD;AAAA,IAED,aAAa,QAAQ,SAAS;AAC5BA,oBAAAA,MAAI,aAAa;AAAA,QACf,MAAM;AAAA,QACN,SAAS,OAAO,OAAO;AAAA,MACzB,CAAC;AAAA,IACF;AAAA,IAED,WAAW;AACT,UAAI,KAAK,mBAAmB;AAAQ;AACpC,WAAK,eAAc;AAAA,IACpB;AAAA,IAED,YAAY;AACV,WAAK,eAAe;AACpB,WAAK,eAAe,IAAI;AAAA,IACzB;AAAA,IAED,oBAAoB;AAClB,YAAM,QAAQ;AAAA,QACZ;AAAA,QAAO;AAAA,QAAO;AAAA,QAAO;AAAA,QACrB;AAAA,QAAO;AAAA,QAAM;AAAA,QAAO;AAAA,QACpB;AAAA,QAAO;AAAA;AAET,aAAO,MAAM,KAAK,MAAM,KAAK,WAAW,MAAM,MAAM,CAAC;AAAA,IACtD;AAAA,IAED,qBAAqB;AACnB,YAAM,SAAS;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAEF,aAAO,OAAO,KAAK,MAAM,KAAK,WAAW,OAAO,MAAM,CAAC;AAAA,IACxD;AAAA,IAED,2BAA2B;AACzB,YAAM,eAAe;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAEF,aAAO,aAAa,KAAK,MAAM,KAAK,WAAW,aAAa,MAAM,CAAC;AAAA,IACpE;AAAA,IAED,oBAAoB;AAClB,YAAM,QAAQ;AAAA,QACZ;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAQ;AAAA,QACxB;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAQ;AAAA;AAE1B,aAAO,MAAM,KAAK,MAAM,KAAK,WAAW,MAAM,MAAM,CAAC;AAAA,IACvD;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvnBA,GAAG,WAAW,eAAe;"}