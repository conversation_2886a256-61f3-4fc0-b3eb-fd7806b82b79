<template>
  <view class="routes-container">
    <!-- 顶部导航栏 -->
    <view class="header">
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
      <view class="navbar">
        <view class="navbar-left" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="back-icon"></image>
        </view>
        <view class="navbar-title">热门路线</view>
        <view class="navbar-right"></view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content">
      <!-- 搜索框 -->
      <view class="search-box">
        <input type="text" placeholder="搜索出发地/目的地" v-model="searchKeyword" class="search-input" />
        <button class="search-btn" @click="searchRoutes">搜索</button>
      </view>

      <!-- 热门路线列表 -->
      <view class="routes-list">
        <view class="route-item" v-for="(route, index) in filteredRoutes" :key="index" @click="selectRoute(route)">
          <view class="route-info">
            <view class="route-path">
              <text class="start-point">{{route.start}}</text>
              <text class="route-arrow">→</text>
              <text class="end-point">{{route.end}}</text>
            </view>
            <view class="route-count">
              <text>{{route.count}}条信息</text>
            </view>
          </view>
          <view class="route-actions">
            <button class="action-btn">查看</button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 状态栏高度
const statusBarHeight = ref(20);
// 搜索关键词
const searchKeyword = ref('');
// 路线数据
const routes = ref([
  { id: 1, start: '磁县', end: '邯郸', count: 128 },
  { id: 2, start: '邯郸', end: '磁县', count: 112 },
  { id: 3, start: '邯郸', end: '北京', count: 86 },
  { id: 4, start: '北京', end: '邯郸', count: 72 },
  { id: 5, start: '邯郸', end: '石家庄', count: 65 },
  { id: 6, start: '石家庄', end: '邯郸', count: 53 },
  { id: 7, start: '磁县', end: '石家庄', count: 48 },
  { id: 8, start: '石家庄', end: '磁县', count: 42 },
  { id: 9, start: '邯郸', end: '保定', count: 38 },
  { id: 10, start: '保定', end: '邯郸', count: 35 }
]);

// 计算属性：过滤后的路线
const filteredRoutes = computed(() => {
  if (!searchKeyword.value) {
    return routes.value;
  }
  
  const keyword = searchKeyword.value.toLowerCase();
  return routes.value.filter(route => 
    route.start.toLowerCase().includes(keyword) || 
    route.end.toLowerCase().includes(keyword)
  );
});

// 生命周期钩子
onMounted(() => {
  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight || 20;
  
  // 加载热门路线数据
  loadRoutes();
});

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 加载热门路线数据
const loadRoutes = () => {
  // 这里应该是实际的API调用
  // 目前使用的是模拟数据，已经在ref中定义
};

// 搜索路线
const searchRoutes = () => {
  console.log('搜索路线:', searchKeyword.value);
  // 搜索逻辑已经在computed属性中实现
};

// 选择路线
const selectRoute = (route) => {
  console.log('选择路线:', route);
  uni.navigateTo({
    url: `/carpool-package/pages/carpool/list/index?start=${encodeURIComponent(route.start)}&end=${encodeURIComponent(route.end)}`
  });
};
</script>

<style>
.routes-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background-color: #0A84FF;
  color: #fff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.navbar {
  height: 44px;
  display: flex;
  align-items: center;
  padding: 0 15px;
}

.navbar-left {
  width: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 24px;
  height: 24px;
  filter: brightness(0) invert(1);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
}

.navbar-right {
  width: 40px;
}

.content {
  margin-top: 64px;
  padding: 15px;
  flex: 1;
}

.search-box {
  display: flex;
  background-color: #fff;
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 15px;
}

.search-input {
  flex: 1;
  height: 36px;
  font-size: 14px;
  padding: 0 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-right: 10px;
}

.search-btn {
  width: 80px;
  height: 36px;
  background-color: #0A84FF;
  color: #fff;
  font-size: 14px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.routes-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.route-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.route-info {
  flex: 1;
}

.route-path {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.start-point, .end-point {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.route-arrow {
  margin: 0 10px;
  color: #0A84FF;
}

.route-count {
  font-size: 12px;
  color: #999;
}

.route-actions {
  margin-left: 15px;
}

.action-btn {
  background-color: #0A84FF;
  color: #fff;
  font-size: 14px;
  padding: 6px 12px;
  border-radius: 4px;
  border: none;
}
</style> 