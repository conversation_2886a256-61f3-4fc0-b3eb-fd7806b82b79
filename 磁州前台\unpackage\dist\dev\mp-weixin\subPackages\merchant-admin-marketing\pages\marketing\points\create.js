"use strict";
const common_vendor = require("../../../../../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
const _sfc_main = {
  __name: "create",
  setup(__props) {
    const formData = common_vendor.reactive({
      name: "",
      points: "",
      stock: "",
      image: "",
      startDate: "",
      endDate: "",
      description: "",
      rules: ""
    });
    const chooseImage = () => {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          formData.image = res.tempFilePaths[0];
        }
      });
    };
    const deleteImage = () => {
      formData.image = "";
    };
    const onStartDateChange = (e) => {
      formData.startDate = e.detail.value;
    };
    const onEndDateChange = (e) => {
      formData.endDate = e.detail.value;
    };
    const saveDraft = () => {
      if (!formData.name) {
        common_vendor.index.showToast({
          title: "请输入商品名称",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showToast({
        title: "草稿保存成功",
        icon: "success"
      });
      setTimeout(() => {
        goBack();
      }, 1500);
    };
    const publishItem = () => {
      if (!formData.name) {
        common_vendor.index.showToast({
          title: "请输入商品名称",
          icon: "none"
        });
        return;
      }
      if (!formData.points) {
        common_vendor.index.showToast({
          title: "请输入所需积分",
          icon: "none"
        });
        return;
      }
      if (!formData.stock) {
        common_vendor.index.showToast({
          title: "请输入库存数量",
          icon: "none"
        });
        return;
      }
      if (!formData.image) {
        common_vendor.index.showToast({
          title: "请上传商品图片",
          icon: "none"
        });
        return;
      }
      if (!formData.startDate || !formData.endDate) {
        common_vendor.index.showToast({
          title: "请设置有效期",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "正在发布..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "发布成功",
          icon: "success"
        });
        setTimeout(() => {
          goBack();
        }, 1500);
      }, 2e3);
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          d: "M15 18L9 12L15 6",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        b: common_vendor.p({
          width: "24",
          height: "24",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        c: common_vendor.o(goBack),
        d: formData.name,
        e: common_vendor.o(($event) => formData.name = $event.detail.value),
        f: formData.points,
        g: common_vendor.o(($event) => formData.points = $event.detail.value),
        h: formData.stock,
        i: common_vendor.o(($event) => formData.stock = $event.detail.value),
        j: !formData.image
      }, !formData.image ? {
        k: common_vendor.p({
          d: "M12 5V19M5 12H19",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        l: common_vendor.p({
          width: "24",
          height: "24",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        m: common_vendor.o(chooseImage)
      } : {
        n: formData.image,
        o: common_vendor.p({
          d: "M18 6L6 18M6 6L18 18",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        p: common_vendor.p({
          width: "20",
          height: "20",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        q: common_vendor.o(deleteImage)
      }, {
        r: common_vendor.t(formData.startDate || "请选择开始日期"),
        s: formData.startDate,
        t: common_vendor.o(onStartDateChange),
        v: common_vendor.t(formData.endDate || "请选择结束日期"),
        w: formData.endDate,
        x: common_vendor.o(onEndDateChange),
        y: formData.description,
        z: common_vendor.o(($event) => formData.description = $event.detail.value),
        A: formData.rules,
        B: common_vendor.o(($event) => formData.rules = $event.detail.value),
        C: common_vendor.o(saveDraft),
        D: common_vendor.o(publishItem)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/points/create.js.map
