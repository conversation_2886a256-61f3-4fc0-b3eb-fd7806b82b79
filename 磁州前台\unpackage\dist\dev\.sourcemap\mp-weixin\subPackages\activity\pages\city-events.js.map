{"version": 3, "file": "city-events.js", "sources": ["subPackages/activity/pages/city-events.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHlccGFnZXNcY2l0eS1ldmVudHMudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"beautiful-cizhou\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\r\n      <view class=\"navbar-left\" @click=\"goBack\">\r\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\r\n      </view>\r\n      <view class=\"navbar-title\">最美磁州</view>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"share-icon\" @click=\"share\">\r\n          <image src=\"/static/images/tabbar/分享.png\" class=\"icon-image\"></image>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 页面内容区域 -->\r\n    <scroll-view class=\"content-scroll\" scroll-y refresher-enabled \r\n      :refresher-triggered=\"refreshing\" @refresherrefresh=\"onRefresh\"\r\n      :style=\"{ paddingTop: (statusBarHeight + 44) + 'px' }\">\r\n      \r\n      <!-- 顶部视觉冲击区 -->\r\n      <view class=\"hero-section\">\r\n        <swiper class=\"hero-swiper\" circular autoplay interval=\"4000\" duration=\"600\" indicator-dots indicator-color=\"rgba(255,255,255,0.4)\" indicator-active-color=\"#FFFFFF\">\r\n          <swiper-item v-for=\"(item, index) in heroImages\" :key=\"index\">\r\n            <image :src=\"item.image\" mode=\"aspectFill\" class=\"hero-image\"></image>\r\n            <view class=\"hero-caption\">\r\n              <text class=\"hero-title\">{{item.title}}</text>\r\n              <text class=\"hero-desc\">{{item.desc}}</text>\r\n            </view>\r\n          </swiper-item>\r\n        </swiper>\r\n      </view>\r\n      \r\n      <!-- 分类导航 -->\r\n      <view class=\"category-tabs\">\r\n        <scroll-view scroll-x class=\"tab-scroll\" show-scrollbar=\"false\">\r\n          <view \r\n            class=\"tab-item\" \r\n            v-for=\"(tab, index) in tabs\" \r\n            :key=\"index\"\r\n            :class=\"{ active: currentTab === index }\"\r\n            @click=\"switchTab(index)\"\r\n          >\r\n            <text class=\"tab-text\">{{ tab.name }}</text>\r\n          </view>\r\n        </scroll-view>\r\n      </view>\r\n      \r\n      <!-- 磁州特色区 -->\r\n      <view class=\"feature-section\">\r\n        <view class=\"section-header\">\r\n          <view class=\"section-title\">\r\n            <text class=\"title-text\">磁州特色</text>\r\n          </view>\r\n          <view class=\"section-more\" @click=\"navigateTo('/subPackages/activity/pages/features')\">\r\n            <text class=\"more-text\">更多</text>\r\n            <text class=\"more-icon\">></text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"feature-cards\">\r\n          <view class=\"feature-card\" v-for=\"(feature, index) in features\" :key=\"index\" @click=\"viewFeature(feature)\">\r\n            <image :src=\"feature.image\" mode=\"aspectFill\" class=\"feature-image\"></image>\r\n            <view class=\"feature-info\">\r\n              <text class=\"feature-name\">{{ feature.name }}</text>\r\n              <text class=\"feature-desc\">{{ feature.desc }}</text>\r\n              <view class=\"feature-tag\" v-if=\"feature.tag\">{{ feature.tag }}</view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 景点推荐 -->\r\n      <view class=\"attractions-section\">\r\n        <view class=\"section-header\">\r\n          <view class=\"section-title\">\r\n            <text class=\"title-text\">景点推荐</text>\r\n          </view>\r\n          <view class=\"section-more\" @click=\"navigateTo('/subPackages/activity/pages/attractions')\">\r\n            <text class=\"more-text\">全部</text>\r\n            <text class=\"more-icon\">></text>\r\n          </view>\r\n        </view>\r\n        \r\n        <scroll-view scroll-x class=\"attractions-scroll\" show-scrollbar=\"false\" enhanced>\r\n          <view class=\"attraction-item\" v-for=\"(item, index) in attractions\" :key=\"index\" @click=\"viewAttraction(item)\">\r\n            <image :src=\"item.image\" mode=\"aspectFill\" class=\"attraction-image\"></image>\r\n            <view class=\"attraction-info\">\r\n              <view class=\"attraction-name-row\">\r\n                <text class=\"attraction-name\">{{ item.name }}</text>\r\n                <view class=\"attraction-rating\">\r\n                  <text class=\"rating-value\">{{ item.rating }}</text>\r\n                  <image src=\"/static/images/icons/star.png\" class=\"rating-star\"></image>\r\n                </view>\r\n              </view>\r\n              <text class=\"attraction-address\">{{ item.address }}</text>\r\n              <view class=\"attraction-tags\">\r\n                <text class=\"tag-item\" v-for=\"(tag, tagIndex) in item.tags\" :key=\"tagIndex\">{{ tag }}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </scroll-view>\r\n      </view>\r\n      \r\n      <!-- 活动日历 -->\r\n      <view class=\"events-section\">\r\n        <view class=\"section-header\">\r\n          <view class=\"section-title\">\r\n            <text class=\"title-text\">本月活动</text>\r\n          </view>\r\n          <view class=\"section-more\" @click=\"navigateTo('/subPackages/activity/pages/events')\">\r\n            <text class=\"more-text\">日历</text>\r\n            <text class=\"more-icon\">></text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"events-list\">\r\n          <view class=\"event-item\" v-for=\"(event, index) in events\" :key=\"index\" @click=\"viewEvent(event)\">\r\n            <view class=\"event-date\">\r\n              <text class=\"event-month\">{{ event.month }}</text>\r\n              <text class=\"event-day\">{{ event.day }}</text>\r\n            </view>\r\n            <view class=\"event-content\">\r\n              <text class=\"event-title\">{{ event.title }}</text>\r\n              <text class=\"event-location\">{{ event.location }}</text>\r\n              <view class=\"event-status\" :class=\"{ 'status-ongoing': event.status === 'ongoing', 'status-upcoming': event.status === 'upcoming', 'status-ended': event.status === 'ended' }\">\r\n                {{ getStatusText(event.status) }}\r\n              </view>\r\n            </view>\r\n            <view class=\"event-action\">\r\n              <text class=\"action-text\">{{ event.actionText }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 本地文化 -->\r\n      <view class=\"culture-section\">\r\n        <view class=\"section-header\">\r\n          <view class=\"section-title\">\r\n            <text class=\"title-text\">本地文化</text>\r\n          </view>\r\n          <view class=\"section-more\" @click=\"navigateTo('/subPackages/activity/pages/culture')\">\r\n            <text class=\"more-text\">探索</text>\r\n            <text class=\"more-icon\">></text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"culture-cards\">\r\n          <view class=\"culture-card\" v-for=\"(item, index) in culturalItems\" :key=\"index\" @click=\"viewCulture(item)\">\r\n            <view class=\"culture-icon-bg\" :style=\"{ backgroundColor: item.bgColor }\">\r\n              <image :src=\"item.icon\" class=\"culture-icon\"></image>\r\n            </view>\r\n            <view class=\"culture-info\">\r\n              <text class=\"culture-name\">{{ item.name }}</text>\r\n              <text class=\"culture-desc\">{{ item.desc }}</text>\r\n            </view>\r\n            <view class=\"arrow-right\">\r\n              <text class=\"arrow-icon\">></text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 攻略推荐 -->\r\n      <view class=\"guides-section\">\r\n        <view class=\"section-header\">\r\n          <view class=\"section-title\">\r\n            <text class=\"title-text\">精选攻略</text>\r\n          </view>\r\n          <view class=\"section-more\" @click=\"navigateTo('/subPackages/activity/pages/guides')\">\r\n            <text class=\"more-text\">全部</text>\r\n            <text class=\"more-icon\">></text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"guides-list\">\r\n          <view class=\"guide-item\" v-for=\"(guide, index) in guides\" :key=\"index\" @click=\"viewGuide(guide)\">\r\n            <image :src=\"guide.image\" mode=\"aspectFill\" class=\"guide-image\"></image>\r\n            <view class=\"guide-content\">\r\n              <text class=\"guide-title\">{{ guide.title }}</text>\r\n              <view class=\"guide-info\">\r\n                <view class=\"guide-author\">\r\n                  <image :src=\"guide.authorAvatar\" class=\"author-avatar\"></image>\r\n                  <text class=\"author-name\">{{ guide.authorName }}</text>\r\n                </view>\r\n                <view class=\"guide-stats\">\r\n                  <view class=\"stat-item\">\r\n                    <image src=\"/static/images/icons/view.png\" class=\"stat-icon\"></image>\r\n                    <text class=\"stat-count\">{{ guide.views }}</text>\r\n                  </view>\r\n                  <view class=\"stat-item\">\r\n                    <image src=\"/static/images/icons/like.png\" class=\"stat-icon\"></image>\r\n                    <text class=\"stat-count\">{{ guide.likes }}</text>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      statusBarHeight: 20,\r\n      refreshing: false,\r\n      currentTab: 0,\r\n      heroImages: [\r\n        {\r\n          image: '/static/images/cizhou/hero1.jpg',\r\n          title: '磁州印象',\r\n          desc: '千年窑火不熄，匠心守艺传承'\r\n        },\r\n        {\r\n          image: '/static/images/cizhou/hero2.jpg',\r\n          title: '磁州风光',\r\n          desc: '山水相依，四季如画'\r\n        },\r\n        {\r\n          image: '/static/images/cizhou/hero3.jpg',\r\n          title: '人文磁州',\r\n          desc: '历史悠久，文化灿烂'\r\n        }\r\n      ],\r\n      tabs: [\r\n        { name: '景点', icon: 'attraction' },\r\n        { name: '美食', icon: 'food' },\r\n        { name: '文化', icon: 'culture' },\r\n        { name: '活动', icon: 'event' },\r\n        { name: '住宿', icon: 'hotel' },\r\n        { name: '购物', icon: 'shopping' }\r\n      ],\r\n      features: [\r\n        {\r\n          id: 1,\r\n          name: '磁州窑瓷器',\r\n          desc: '北方民窑之冠，千年窑火不熄',\r\n          image: '/static/images/cizhou/feature1.jpg',\r\n          tag: '非遗'\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '民间剪纸',\r\n          desc: '巧手剪就锦绣图，浓郁乡土风情',\r\n          image: '/static/images/cizhou/feature2.jpg',\r\n          tag: '传统'\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '地方戏曲',\r\n          desc: '磁州梆子，古老的民间艺术表演',\r\n          image: '/static/images/cizhou/feature3.jpg',\r\n          tag: '艺术'\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '特色美食',\r\n          desc: '老磁州炖菜，地道农家风味',\r\n          image: '/static/images/cizhou/feature4.jpg',\r\n          tag: '美食'\r\n        }\r\n      ],\r\n      attractions: [\r\n        {\r\n          id: 1,\r\n          name: '磁州窑博物馆',\r\n          rating: 4.8,\r\n          address: '磁县城南2公里处',\r\n          image: '/static/images/cizhou/attraction1.jpg',\r\n          tags: ['历史', '博物馆', '陶瓷']\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '朱山风景区',\r\n          rating: 4.6,\r\n          address: '磁县东北15公里',\r\n          image: '/static/images/cizhou/attraction2.jpg',\r\n          tags: ['自然', '山水', '远足']\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '古窑址公园',\r\n          rating: 4.5,\r\n          address: '磁县陶瓷产业园',\r\n          image: '/static/images/cizhou/attraction3.jpg',\r\n          tags: ['遗址', '公园', '文化']\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '古城墙遗址',\r\n          rating: 4.3,\r\n          address: '磁县老城区',\r\n          image: '/static/images/cizhou/attraction4.jpg',\r\n          tags: ['古迹', '历史', '文化']\r\n        }\r\n      ],\r\n      events: [\r\n        {\r\n          id: 1,\r\n          title: '磁州窑文化节',\r\n          month: '6月',\r\n          day: '15',\r\n          location: '磁州窑博物馆',\r\n          status: 'upcoming',\r\n          actionText: '预约'\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '民俗文化展演',\r\n          month: '6月',\r\n          day: '20',\r\n          location: '文化广场',\r\n          status: 'upcoming',\r\n          actionText: '预约'\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '磁州好礼品鉴会',\r\n          month: '6月',\r\n          day: '10',\r\n          location: '城市会展中心',\r\n          status: 'ongoing',\r\n          actionText: '参与'\r\n        },\r\n        {\r\n          id: 4,\r\n          title: '传统工艺体验日',\r\n          month: '6月',\r\n          day: '5',\r\n          location: '磁州文化馆',\r\n          status: 'ended',\r\n          actionText: '回顾'\r\n        }\r\n      ],\r\n      culturalItems: [\r\n        {\r\n          name: '磁州窑文化',\r\n          desc: '了解磁州窑的历史和文化',\r\n          icon: '/static/images/cizhou/culture1.jpg',\r\n          bgColor: '#FFD700'\r\n        },\r\n        {\r\n          name: '磁州剪纸艺术',\r\n          desc: '探索磁州剪纸的独特魅力',\r\n          icon: '/static/images/cizhou/culture2.jpg',\r\n          bgColor: '#FF69B4'\r\n        },\r\n        {\r\n          name: '磁州梆子戏曲',\r\n          desc: '了解磁州梆子戏曲的历史和表演',\r\n          icon: '/static/images/cizhou/culture3.jpg',\r\n          bgColor: '#007AFF'\r\n        },\r\n        {\r\n          name: '磁州美食',\r\n          desc: '品尝磁州的传统美食',\r\n          icon: '/static/images/cizhou/culture4.jpg',\r\n          bgColor: '#FF4500'\r\n        }\r\n      ],\r\n      guides: [\r\n        {\r\n          title: '磁州一日游攻略',\r\n          image: '/static/images/cizhou/guide1.jpg',\r\n          authorName: '张三',\r\n          authorAvatar: '/static/images/cizhou/author1.jpg',\r\n          views: '1,234',\r\n          likes: '234'\r\n        },\r\n        {\r\n          title: '磁州文化深度游',\r\n          image: '/static/images/cizhou/guide2.jpg',\r\n          authorName: '李四',\r\n          authorAvatar: '/static/images/cizhou/author2.jpg',\r\n          views: '890',\r\n          likes: '123'\r\n        },\r\n        {\r\n          title: '磁州美食探索',\r\n          image: '/static/images/cizhou/guide3.jpg',\r\n          authorName: '王五',\r\n          authorAvatar: '/static/images/cizhou/author3.jpg',\r\n          views: '765',\r\n          likes: '98'\r\n        },\r\n        {\r\n          title: '磁州住宿体验',\r\n          image: '/static/images/cizhou/guide4.jpg',\r\n          authorName: '赵六',\r\n          authorAvatar: '/static/images/cizhou/author4.jpg',\r\n          views: '654',\r\n          likes: '76'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  onLoad() {\r\n    const sysInfo = uni.getSystemInfoSync();\r\n    this.statusBarHeight = sysInfo.statusBarHeight;\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    share() {\r\n      uni.showShareMenu({\r\n        withShareTicket: true,\r\n        menus: ['shareAppMessage', 'shareTimeline']\r\n      });\r\n    },\r\n    onRefresh() {\r\n      this.refreshing = true;\r\n      setTimeout(() => {\r\n        this.refreshing = false;\r\n      }, 1200);\r\n    },\r\n    switchTab(index) {\r\n      this.currentTab = index;\r\n      // 这里可以根据选中的标签加载不同的内容\r\n    },\r\n    navigateTo(url) {\r\n      uni.navigateTo({\r\n        url: url\r\n      });\r\n    },\r\n    viewFeature(feature) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/activity/pages/feature-detail?id=${feature.id}`\r\n      });\r\n    },\r\n    viewAttraction(attraction) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/activity/pages/attraction-detail?id=${attraction.id}`\r\n      });\r\n    },\r\n    viewEvent(event) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/activity/pages/event-detail?id=${event.id}`\r\n      });\r\n    },\r\n    viewCulture(item) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/activity/pages/culture-detail?name=${item.name}`\r\n      });\r\n    },\r\n    viewGuide(guide) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/activity/pages/guide-detail?title=${guide.title}`\r\n      });\r\n    },\r\n    getStatusText(status) {\r\n      switch(status) {\r\n        case 'ongoing': return '进行中';\r\n        case 'upcoming': return '即将开始';\r\n        case 'ended': return '已结束';\r\n        default: return '';\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.beautiful-cizhou {\r\n  position: relative;\r\n  height: 100vh;\r\n  background-color: #F7F7F7;\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.custom-navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 44px;\r\n  background: linear-gradient(135deg, #007AFF, #5AC8FA);\r\n  color: #FFFFFF;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0 16px;\r\n  z-index: 1000;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.navbar-left, .navbar-right {\r\n  width: 44px;\r\n  height: 44px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n}\r\n\r\n.navbar-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  text-align: center;\r\n  flex: 1;\r\n  margin: 0 50px; /* 添加左右外边距，为返回按钮留出空间 */\r\n}\r\n\r\n.back-icon, .icon-image {\r\n  width: 32px;\r\n  height: 32px;\r\n}\r\n\r\n/* 内容区域 */\r\n.content-scroll {\r\n  height: 100vh;\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* 顶部视觉区 */\r\n.hero-section {\r\n  width: 100%;\r\n  height: 420rpx;\r\n  position: relative;\r\n  margin-bottom: 24rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.hero-swiper {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.hero-image {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.hero-caption {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  padding: 30rpx 40rpx;\r\n  background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);\r\n}\r\n\r\n.hero-title {\r\n  font-size: 36rpx;\r\n  color: #FFFFFF;\r\n  font-weight: 600;\r\n  display: block;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.hero-desc {\r\n  font-size: 24rpx;\r\n  color: rgba(255,255,255,0.9);\r\n  display: block;\r\n}\r\n\r\n/* 分类标签 */\r\n.category-tabs {\r\n  background-color: #FFFFFF;\r\n  padding: 20rpx 0;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.tab-scroll {\r\n  white-space: nowrap;\r\n  width: 100%;\r\n}\r\n\r\n.tab-item {\r\n  display: inline-block;\r\n  padding: 16rpx 30rpx;\r\n  margin: 0 10rpx;\r\n  border-radius: 30rpx;\r\n  font-size: 28rpx;\r\n  color: #333333;\r\n  background-color: #F5F5F5;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.tab-item:first-child {\r\n  margin-left: 20rpx;\r\n}\r\n\r\n.tab-item:last-child {\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.tab-item.active {\r\n  background-color: #007AFF;\r\n  color: #FFFFFF;\r\n  box-shadow: 0 4rpx 10rpx rgba(0,122,255,0.3);\r\n}\r\n\r\n.tab-text {\r\n  font-weight: 500;\r\n}\r\n\r\n/* 通用板块样式 */\r\n.feature-section, .attractions-section, .events-section {\r\n  margin: 0 20rpx 30rpx;\r\n  background-color: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\r\n  overflow: hidden;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.title-text {\r\n  font-size: 34rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n  position: relative;\r\n  padding-left: 24rpx;\r\n}\r\n\r\n.title-text::before {\r\n  content: '';\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 8rpx;\r\n  height: 32rpx;\r\n  background-color: #007AFF;\r\n  border-radius: 4rpx;\r\n}\r\n\r\n.section-more {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.more-text {\r\n  font-size: 26rpx;\r\n  color: #007AFF;\r\n}\r\n\r\n.more-icon {\r\n  font-size: 26rpx;\r\n  color: #007AFF;\r\n  margin-left: 4rpx;\r\n}\r\n\r\n/* 特色板块 */\r\n.feature-cards {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 20rpx;\r\n}\r\n\r\n.feature-card {\r\n  border-radius: 16rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);\r\n  position: relative;\r\n}\r\n\r\n.feature-image {\r\n  width: 100%;\r\n  height: 180rpx;\r\n}\r\n\r\n.feature-info {\r\n  padding: 20rpx;\r\n  background-color: #FFFFFF;\r\n}\r\n\r\n.feature-name {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n  margin-bottom: 8rpx;\r\n  display: block;\r\n}\r\n\r\n.feature-desc {\r\n  font-size: 24rpx;\r\n  color: #666666;\r\n  display: block;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.feature-tag {\r\n  position: absolute;\r\n  top: 16rpx;\r\n  right: 16rpx;\r\n  padding: 6rpx 12rpx;\r\n  background-color: rgba(0,122,255,0.8);\r\n  color: #FFFFFF;\r\n  font-size: 22rpx;\r\n  border-radius: 20rpx;\r\n}\r\n\r\n/* 景点推荐 */\r\n.attractions-scroll {\r\n  width: 100%;\r\n  white-space: nowrap;\r\n}\r\n\r\n.attraction-item {\r\n  display: inline-block;\r\n  width: 400rpx;\r\n  margin-right: 20rpx;\r\n  border-radius: 16rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);\r\n  background-color: #FFFFFF;\r\n}\r\n\r\n.attraction-item:last-child {\r\n  margin-right: 0;\r\n}\r\n\r\n.attraction-image {\r\n  width: 100%;\r\n  height: 220rpx;\r\n}\r\n\r\n.attraction-info {\r\n  padding: 20rpx;\r\n}\r\n\r\n.attraction-name-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.attraction-name {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n}\r\n\r\n.attraction-rating {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.rating-value {\r\n  font-size: 24rpx;\r\n  color: #FF9500;\r\n  font-weight: 600;\r\n  margin-right: 4rpx;\r\n}\r\n\r\n.rating-star {\r\n  width: 24rpx;\r\n  height: 24rpx;\r\n}\r\n\r\n.attraction-address {\r\n  font-size: 24rpx;\r\n  color: #666666;\r\n  margin-bottom: 8rpx;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.attraction-tags {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.tag-item {\r\n  font-size: 20rpx;\r\n  color: #007AFF;\r\n  background-color: rgba(0,122,255,0.1);\r\n  padding: 4rpx 10rpx;\r\n  border-radius: 10rpx;\r\n  margin-right: 8rpx;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n/* 活动日历 */\r\n.events-list {\r\n  \r\n}\r\n\r\n.event-item {\r\n  display: flex;\r\n  padding: 20rpx 0;\r\n  border-bottom: 1px solid #F0F0F0;\r\n}\r\n\r\n.event-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.event-date {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 100rpx;\r\n  background-color: #F5F5F5;\r\n  border-radius: 12rpx;\r\n  padding: 16rpx 0;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.event-month {\r\n  font-size: 24rpx;\r\n  color: #666666;\r\n}\r\n\r\n.event-day {\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n}\r\n\r\n.event-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n}\r\n\r\n.event-title {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.event-location {\r\n  font-size: 24rpx;\r\n  color: #666666;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.event-status {\r\n  font-size: 22rpx;\r\n  padding: 4rpx 12rpx;\r\n  border-radius: 10rpx;\r\n  display: inline-block;\r\n}\r\n\r\n.status-ongoing {\r\n  background-color: rgba(40,205,65,0.1);\r\n  color: #28CD41;\r\n}\r\n\r\n.status-upcoming {\r\n  background-color: rgba(0,122,255,0.1);\r\n  color: #007AFF;\r\n}\r\n\r\n.status-ended {\r\n  background-color: rgba(142,142,147,0.1);\r\n  color: #8E8E93;\r\n}\r\n\r\n.event-action {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 100rpx;\r\n}\r\n\r\n.action-text {\r\n  font-size: 26rpx;\r\n  color: #007AFF;\r\n}\r\n\r\n/* 本地文化 */\r\n.culture-section {\r\n  margin: 0 20rpx 30rpx;\r\n  background-color: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.culture-cards {\r\n  \r\n}\r\n\r\n.culture-card {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20rpx 0;\r\n  border-bottom: 1px solid #F0F0F0;\r\n}\r\n\r\n.culture-card:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.culture-icon-bg {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.culture-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n}\r\n\r\n.culture-info {\r\n  flex: 1;\r\n}\r\n\r\n.culture-name {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n  margin-bottom: 8rpx;\r\n  display: block;\r\n}\r\n\r\n.culture-desc {\r\n  font-size: 24rpx;\r\n  color: #666666;\r\n  display: block;\r\n}\r\n\r\n.arrow-right {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.arrow-icon {\r\n  font-size: 24rpx;\r\n  color: #999999;\r\n}\r\n\r\n/* 攻略推荐 */\r\n.guides-section {\r\n  margin: 0 20rpx 30rpx;\r\n  background-color: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.guides-list {\r\n  \r\n}\r\n\r\n.guide-item {\r\n  display: flex;\r\n  padding: 20rpx 0;\r\n  border-bottom: 1px solid #F0F0F0;\r\n}\r\n\r\n.guide-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.guide-image {\r\n  width: 200rpx;\r\n  height: 140rpx;\r\n  border-radius: 12rpx;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.guide-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n}\r\n\r\n.guide-title {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  line-height: 1.4;\r\n}\r\n\r\n.guide-info {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.guide-author {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.author-avatar {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  border-radius: 50%;\r\n  margin-right: 10rpx;\r\n}\r\n\r\n.author-name {\r\n  font-size: 24rpx;\r\n  color: #666666;\r\n}\r\n\r\n.guide-stats {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.stat-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-left: 20rpx;\r\n}\r\n\r\n.stat-icon {\r\n  width: 24rpx;\r\n  height: 24rpx;\r\n  margin-right: 6rpx;\r\n}\r\n\r\n.stat-count {\r\n  font-size: 22rpx;\r\n  color: #999999;\r\n}\r\n</style> \r\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity/pages/city-events.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AA+MA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,iBAAiB;AAAA,MACjB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,QACV;AAAA,UACE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,MAAM;AAAA,QACR;AAAA,MACD;AAAA,MACD,MAAM;AAAA,QACJ,EAAE,MAAM,MAAM,MAAM,aAAc;AAAA,QAClC,EAAE,MAAM,MAAM,MAAM,OAAQ;AAAA,QAC5B,EAAE,MAAM,MAAM,MAAM,UAAW;AAAA,QAC/B,EAAE,MAAM,MAAM,MAAM,QAAS;AAAA,QAC7B,EAAE,MAAM,MAAM,MAAM,QAAS;AAAA,QAC7B,EAAE,MAAM,MAAM,MAAM,WAAW;AAAA,MAChC;AAAA,MACD,UAAU;AAAA,QACR;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,KAAK;AAAA,QACP;AAAA,MACD;AAAA,MACD,aAAa;AAAA,QACX;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,OAAO;AAAA,UACP,MAAM,CAAC,MAAM,OAAO,IAAI;AAAA,QACzB;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,OAAO;AAAA,UACP,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,QACxB;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,OAAO;AAAA,UACP,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,QACxB;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,OAAO;AAAA,UACP,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,QACzB;AAAA,MACD;AAAA,MACD,QAAQ;AAAA,QACN;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,OAAO;AAAA,UACP,KAAK;AAAA,UACL,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,QACb;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,OAAO;AAAA,UACP,KAAK;AAAA,UACL,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,QACb;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,OAAO;AAAA,UACP,KAAK;AAAA,UACL,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,QACb;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,OAAO;AAAA,UACP,KAAK;AAAA,UACL,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,QACd;AAAA,MACD;AAAA,MACD,eAAe;AAAA,QACb;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACD;AAAA,MACD,QAAQ;AAAA,QACN;AAAA,UACE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,OAAO;AAAA,UACP,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,OAAO;AAAA,UACP,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,OAAO;AAAA,UACP,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,OAAO;AAAA,UACP,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AACP,UAAM,UAAUA,oBAAI;AACpB,SAAK,kBAAkB,QAAQ;AAAA,EAChC;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,QAAQ;AACNA,oBAAAA,MAAI,cAAc;AAAA,QAChB,iBAAiB;AAAA,QACjB,OAAO,CAAC,mBAAmB,eAAe;AAAA,MAC5C,CAAC;AAAA,IACF;AAAA,IACD,YAAY;AACV,WAAK,aAAa;AAClB,iBAAW,MAAM;AACf,aAAK,aAAa;AAAA,MACnB,GAAE,IAAI;AAAA,IACR;AAAA,IACD,UAAU,OAAO;AACf,WAAK,aAAa;AAAA,IAEnB;AAAA,IACD,WAAW,KAAK;AACdA,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,YAAY,SAAS;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,iDAAiD,QAAQ,EAAE;AAAA,MAClE,CAAC;AAAA,IACF;AAAA,IACD,eAAe,YAAY;AACzBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,oDAAoD,WAAW,EAAE;AAAA,MACxE,CAAC;AAAA,IACF;AAAA,IACD,UAAU,OAAO;AACfA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,+CAA+C,MAAM,EAAE;AAAA,MAC9D,CAAC;AAAA,IACF;AAAA,IACD,YAAY,MAAM;AAChBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,mDAAmD,KAAK,IAAI;AAAA,MACnE,CAAC;AAAA,IACF;AAAA,IACD,UAAU,OAAO;AACfA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,kDAAkD,MAAM,KAAK;AAAA,MACpE,CAAC;AAAA,IACF;AAAA,IACD,cAAc,QAAQ;AACpB,cAAO,QAAM;AAAA,QACX,KAAK;AAAW,iBAAO;AAAA,QACvB,KAAK;AAAY,iBAAO;AAAA,QACxB,KAAK;AAAS,iBAAO;AAAA,QACrB;AAAS,iBAAO;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChdA,GAAG,WAAW,eAAe;"}