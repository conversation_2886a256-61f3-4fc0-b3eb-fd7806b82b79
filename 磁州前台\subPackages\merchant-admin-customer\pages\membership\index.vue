<template>
  <view class="membership-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">会员体系管理</text>
      <view class="navbar-right">
        <view class="help-icon">?</view>
      </view>
    </view>
    
    <!-- 会员数据概览 -->
    <view class="overview-section">
      <view class="overview-card">
        <view class="overview-item">
          <text class="overview-value">{{memberData.totalMembers}}</text>
          <text class="overview-label">会员总数</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{memberData.activeMembers}}</text>
          <text class="overview-label">活跃会员</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{memberData.newMembers}}</text>
          <text class="overview-label">本月新增</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{memberData.conversionRate}}%</text>
          <text class="overview-label">会员转化率</text>
        </view>
      </view>
    </view>
    
    <!-- 会员等级分布 -->
    <view class="level-distribution">
      <view class="section-header">
        <text class="section-title">会员等级分布</text>
      </view>
      <view class="level-chart">
        <view 
          v-for="(level, index) in memberLevels" 
          :key="index"
          class="level-bar">
          <view class="level-info">
            <view class="level-icon" :style="{backgroundColor: level.color}">{{level.icon}}</view>
            <text class="level-name">{{level.name}}</text>
          </view>
          <view class="bar-wrapper">
            <view class="bar-fill" :style="{width: level.percentage + '%', backgroundColor: level.color}"></view>
            <text class="bar-value">{{level.count}}人 ({{level.percentage}}%)</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 会员体系管理模块 -->
    <view class="module-section">
      <view class="section-header">
        <text class="section-title">会员体系设置</text>
      </view>
      
      <view class="module-grid">
        <!-- 会员等级规则 -->
        <view class="module-card" @click="navigateTo('./level-rules')">
          <view class="card-icon level-icon">👑</view>
          <text class="card-name">会员等级规则</text>
          <text class="card-desc">设置会员等级与权益</text>
          <view class="arrow-icon">›</view>
        </view>
        
        <!-- 升级规则 -->
        <view class="module-card" @click="navigateTo('./upgrade-rules')">
          <view class="card-icon upgrade-icon">⬆️</view>
          <text class="card-name">升级规则配置</text>
          <text class="card-desc">消费金额、次数规则</text>
          <view class="arrow-icon">›</view>
        </view>
        
        <!-- 积分规则 -->
        <view class="module-card" @click="navigateTo('./points-rules')">
          <view class="card-icon points-icon">🎯</view>
          <text class="card-name">积分规则设置</text>
          <text class="card-desc">积分获取与使用规则</text>
          <view class="arrow-icon">›</view>
        </view>
        
        <!-- 权益配置 -->
        <view class="module-card" @click="navigateTo('./benefits')">
          <view class="card-icon benefits-icon">🎁</view>
          <text class="card-name">会员权益配置</text>
          <text class="card-desc">折扣、赠品、专属服务</text>
          <view class="arrow-icon">›</view>
        </view>
      </view>
    </view>
    
    <!-- 会员营销工具 -->
    <view class="tools-section">
      <view class="section-header">
        <text class="section-title">会员营销工具</text>
      </view>
      
      <view class="tools-list">
        <view class="tool-item" @click="navigateTo('./birthday')">
          <view class="tool-icon birthday-icon">🎂</view>
          <view class="tool-content">
            <text class="tool-name">生日关怀</text>
            <text class="tool-desc">自动发送生日祝福与优惠</text>
          </view>
          <view class="arrow-icon">›</view>
        </view>
        
        <view class="tool-item" @click="navigateTo('./inactive')">
          <view class="tool-icon inactive-icon">⏰</view>
          <view class="tool-content">
            <text class="tool-name">唤醒沉睡会员</text>
            <text class="tool-desc">针对长期未消费会员的营销</text>
          </view>
          <view class="arrow-icon">›</view>
        </view>
        
        <view class="tool-item" @click="navigateTo('./referral')">
          <view class="tool-icon referral-icon">👥</view>
          <view class="tool-content">
            <text class="tool-name">会员推荐计划</text>
            <text class="tool-desc">老带新奖励机制设置</text>
          </view>
          <view class="arrow-icon">›</view>
        </view>
        
        <view class="tool-item" @click="navigateTo('./exclusive')">
          <view class="tool-icon exclusive-icon">🔒</view>
          <view class="tool-content">
            <text class="tool-name">专属优惠活动</text>
            <text class="tool-desc">会员专享价格与活动</text>
          </view>
          <view class="arrow-icon">›</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      memberData: {
        totalMembers: '728',
        activeMembers: '356',
        newMembers: '42',
        conversionRate: '58'
      },
      memberLevels: [
        {
          name: '普通会员',
          icon: '🥉',
          color: '#CD7F32',
          count: 450,
          percentage: 62
        },
        {
          name: '银卡会员',
          icon: '🥈',
          color: '#C0C0C0',
          count: 180,
          percentage: 25
        },
        {
          name: '金卡会员',
          icon: '🥇',
          color: '#FFD700',
          count: 80,
          percentage: 11
        },
        {
          name: '钻石会员',
          icon: '💎',
          color: '#B9F2FF',
          count: 18,
          percentage: 2
        }
      ]
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    navigateTo(url) {
      uni.navigateTo({ url });
    }
  }
}
</script>

<style>
.membership-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 20px;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}

.overview-section {
  padding: 16px;
}

.overview-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.overview-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.overview-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 12px;
  color: #999;
}

.level-distribution {
  padding: 0 16px;
  margin-bottom: 20px;
}

.section-header {
  margin-bottom: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.level-chart {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.level-bar {
  margin-bottom: 16px;
}

.level-bar:last-child {
  margin-bottom: 0;
}

.level-info {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.level-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  margin-right: 8px;
}

.level-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.bar-wrapper {
  height: 20px;
  background-color: #f0f0f0;
  border-radius: 10px;
  position: relative;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  border-radius: 10px;
}

.bar-value {
  position: absolute;
  right: 12px;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #fff;
  text-shadow: 0 0 2px rgba(0,0,0,0.5);
}

.module-section {
  padding: 0 16px;
  margin-bottom: 20px;
}

.module-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.module-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-bottom: 12px;
}

.level-icon {
  background-color: #fff7e6;
  color: #fa8c16;
}

.upgrade-icon {
  background-color: #f6ffed;
  color: #52c41a;
}

.points-icon {
  background-color: #e6f7ff;
  color: #1677FF;
}

.benefits-icon {
  background-color: #fff1f0;
  color: #f5222d;
}

.card-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.card-desc {
  font-size: 12px;
  color: #999;
  text-align: center;
}

.arrow-icon {
  position: absolute;
  right: 12px;
  top: 12px;
  font-size: 20px;
  color: #ccc;
}

.tools-section {
  padding: 0 16px;
}

.tools-list {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  overflow: hidden;
}

.tool-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.tool-item:last-child {
  border-bottom: none;
}

.tool-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin-right: 12px;
}

.birthday-icon {
  background-color: #fff7e6;
  color: #fa8c16;
}

.inactive-icon {
  background-color: #e6f7ff;
  color: #1677FF;
}

.referral-icon {
  background-color: #f6ffed;
  color: #52c41a;
}

.exclusive-icon {
  background-color: #fff1f0;
  color: #f5222d;
}

.tool-content {
  flex: 1;
}

.tool-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.tool-desc {
  font-size: 12px;
  color: #999;
}
</style> 