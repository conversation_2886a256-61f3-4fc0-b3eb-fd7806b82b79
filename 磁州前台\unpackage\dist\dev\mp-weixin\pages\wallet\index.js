"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Math) {
  WalletPage();
}
const WalletPage = () => "../../components/wallet/WalletPage.js";
const _sfc_main = {
  __name: "index",
  setup(__props) {
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          scene: "main",
          ["path-prefix"]: "/pages/wallet"
        })
      };
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/wallet/index.js.map
