{"version": 3, "file": "list.js", "sources": ["subPackages/activity/pages/list.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHlccGFnZXNcbGlzdC52dWU"], "sourcesContent": ["<template>\n\t<view class=\"page-container\">\n\t\t<!-- 自定义导航栏 -->\n\t\t<view class=\"custom-navbar\">\n\t\t\t<view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\n\t\t\t<view class=\"navbar-content\">\n\t\t\t\t<view class=\"navbar-left\">\n\t\t\t\t\t<view class=\"back-button\" @tap=\"goBack\">\n\t\t\t\t\t\t<svg class=\"back-icon\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n\t\t\t\t\t\t\t<path d=\"M15 18L9 12L15 6\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t\t\t\t</svg>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"navbar-title\">\n\t\t\t\t\t<text class=\"title-text\">活动列表</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"navbar-right\">\n\t\t\t\t\t<view class=\"filter-button\" @tap=\"showFilterOptions\">\n\t\t\t\t\t\t<svg width=\"22\" height=\"22\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n\t\t\t\t\t\t\t<path d=\"M3 6H21M6 12H18M10 18H14\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t\t\t\t</svg>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 状态筛选 -->\n\t\t<view class=\"filter-tabs\">\n\t\t\t<view class=\"tab-item\" :class=\"{ active: currentStatus === 'all' }\" @tap=\"filterByStatus('all')\">\n\t\t\t\t<text class=\"tab-text\">全部</text>\n\t\t\t</view>\n\t\t\t<view class=\"tab-item\" :class=\"{ active: currentStatus === '进行中' }\" @tap=\"filterByStatus('进行中')\">\n\t\t\t\t<text class=\"tab-text\">进行中</text>\n\t\t\t</view>\n\t\t\t<view class=\"tab-item\" :class=\"{ active: currentStatus === '未开始' }\" @tap=\"filterByStatus('未开始')\">\n\t\t\t\t<text class=\"tab-text\">未开始</text>\n\t\t\t</view>\n\t\t\t<view class=\"tab-item\" :class=\"{ active: currentStatus === '已结束' }\" @tap=\"filterByStatus('已结束')\">\n\t\t\t\t<text class=\"tab-text\">已结束</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 活动列表 -->\n\t\t<scroll-view class=\"activity-list\" scroll-y=\"true\" @scrolltolower=\"loadMore\">\n\t\t\t<template v-if=\"filteredActivities.length === 0\">\n\t\t\t\t<view class=\"empty-state\">\n\t\t\t\t\t<svg class=\"empty-icon\" width=\"80\" height=\"80\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n\t\t\t\t\t\t<path d=\"M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t\t\t\t<path d=\"M9 9H9.01\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t\t\t\t<path d=\"M15 9H15.01\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t\t\t\t<path d=\"M8 14H16\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t\t\t</svg>\n\t\t\t\t\t<text class=\"empty-text\">暂无活动信息</text>\n\t\t\t\t</view>\n\t\t\t</template>\n\t\t\t\n\t\t\t<template v-else>\n\t\t\t\t<view \n\t\t\t\t\tclass=\"activity-card\" \n\t\t\t\t\tv-for=\"(activity, index) in filteredActivities\" \n\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t@tap=\"viewActivityDetail(activity.id)\"\n\t\t\t\t>\n\t\t\t\t\t<view class=\"card-image\" :style=\"{ backgroundImage: `url(${activity.coverImage})` }\"></view>\n\t\t\t\t\t<view class=\"card-content\">\n\t\t\t\t\t\t<text class=\"card-title\">{{ activity.title }}</text>\n\t\t\t\t\t\t<view class=\"card-info\">\n\t\t\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t\t\t<svg class=\"info-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n\t\t\t\t\t\t\t\t\t<path d=\"M12 8V12L15 15\" stroke=\"#666666\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t\t\t\t\t\t\t<path d=\"M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z\" stroke=\"#666666\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t<text class=\"info-text\">{{ formatDate(activity.startTime) }} ~ {{ formatDate(activity.endTime) }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t\t\t<svg class=\"info-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n\t\t\t\t\t\t\t\t\t<path d=\"M17 21V19C17 16.7909 15.2091 15 13 15H5C2.79086 15 1 16.7909 1 19V21\" stroke=\"#666666\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t\t\t\t\t\t\t<path d=\"M9 11C11.2091 11 13 9.20914 13 7C13 4.79086 11.2091 3 9 3C6.79086 3 5 4.79086 5 7C5 9.20914 6.79086 11 9 11Z\" stroke=\"#666666\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t\t\t\t\t\t\t<path d=\"M23 21V19C22.9986 17.1771 21.765 15.5857 20 15.13\" stroke=\"#666666\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t\t\t\t\t\t\t<path d=\"M16 3.13C17.7699 3.58317 19.0078 5.17799 19.0078 7.005C19.0078 8.83201 17.7699 10.4268 16 10.88\" stroke=\"#666666\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t<text class=\"info-text\">{{ activity.participantsCount || 0 }}人参与</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"card-status\" :class=\"{\n\t\t\t\t\t\t\t'status-ongoing': activity.status === '进行中',\n\t\t\t\t\t\t\t'status-upcoming': activity.status === '未开始',\n\t\t\t\t\t\t\t'status-ended': activity.status === '已结束'\n\t\t\t\t\t\t}\">\n\t\t\t\t\t\t\t{{ activity.status }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"card-actions\">\n\t\t\t\t\t\t\t<view class=\"action-button edit\" @tap.stop=\"editActivity(activity.id)\">\n\t\t\t\t\t\t\t\t<svg class=\"action-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n\t\t\t\t\t\t\t\t\t<path d=\"M11 4H4C2.89543 4 2 4.89543 2 6V20C2 21.1046 2.89543 22 4 22H18C19.1046 22 20 21.1046 20 20V13\" stroke=\"#007AFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t\t\t\t\t\t\t<path d=\"M18.5 2.5C19.3284 1.67157 20.6716 1.67157 21.5 2.5C22.3284 3.32843 22.3284 4.67157 21.5 5.5L12 15L8 16L9 12L18.5 2.5Z\" stroke=\"#007AFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t<text class=\"action-text\">编辑</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<view class=\"action-button promote\" @tap.stop=\"promoteActivity(activity.id)\">\n\t\t\t\t\t\t\t\t<svg class=\"action-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n\t\t\t\t\t\t\t\t\t<path d=\"M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15\" stroke=\"#FF9500\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t\t\t\t\t\t\t<path d=\"M7 10L12 15L17 10\" stroke=\"#FF9500\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t\t\t\t\t\t\t<path d=\"M12 15V3\" stroke=\"#FF9500\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t<text class=\"action-text\">推广</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<view class=\"action-button share\" @tap.stop=\"shareActivity(activity.id)\">\n\t\t\t\t\t\t\t\t<svg class=\"action-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n\t\t\t\t\t\t\t\t\t<path d=\"M18 8C19.6569 8 21 6.65685 21 5C21 3.34315 19.6569 2 18 2C16.3431 2 15 3.34315 15 5C15 6.65685 16.3431 8 18 8Z\" stroke=\"#34C759\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t\t\t\t\t\t\t<path d=\"M6 15C7.65685 15 9 13.6569 9 12C9 10.3431 7.65685 9 6 9C4.34315 9 3 10.3431 3 12C3 13.6569 4.34315 15 6 15Z\" stroke=\"#34C759\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t\t\t\t\t\t\t<path d=\"M18 22C19.6569 22 21 20.6569 21 19C21 17.3431 19.6569 16 18 16C16.3431 16 15 17.3431 15 19C15 20.6569 16.3431 22 18 22Z\" stroke=\"#34C759\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t\t\t\t\t\t\t<path d=\"M8.59 13.51L15.42 17.49\" stroke=\"#34C759\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t\t\t\t\t\t\t<path d=\"M15.41 6.51L8.59 10.49\" stroke=\"#34C759\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t<text class=\"action-text\">转发</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</template>\n\t\t\t\n\t\t\t<view class=\"loading-container\" v-if=\"loading\">\n\t\t\t\t<text class=\"loading-text\">加载中...</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"no-more\" v-if=\"!loading && !hasMore && filteredActivities.length > 0\">\n\t\t\t\t<text>没有更多数据了</text>\n\t\t\t</view>\n\t\t</scroll-view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t// 页面配置\n\tnavigationStyle: 'custom'\n}\n</script>\n\n<script setup>\nimport { ref, computed, onMounted, reactive } from 'vue';\nimport { onLoad, onShow } from '@dcloudio/uni-app';\n\n// 状态栏高度\nconst statusBarHeight = ref(20);\n\n// 刷新状态\nconst isRefreshing = ref(false);\n\n// 当前筛选状态\nconst currentStatus = ref('all');\n\n// 活动数据\nconst activityList = reactive([\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 1,\n\t\t\t\t\t\ttitle: '磁州美食节',\n\t\tcoverImage: '/static/images/banner/banner-1.png',\n\t\tstartTime: '2024-03-20',\n\t\tendTime: '2024-03-25',\n\t\t\t\t\t\tlocation: '磁县人民广场',\n\t\t\t\t\t\tdescription: '汇聚磁州各地特色美食，带您品尝地道磁州味道',\n\t\tstatus: '进行中',\n\t\tparticipantsCount: 128,\n\t\tviewsCount: 3560,\n\t\tcommentsCount: 42\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 2,\n\t\t\t\t\t\ttitle: '春季农产品展销会',\n\t\tcoverImage: '/static/images/banner/banner-2.png',\n\t\tstartTime: '2024-03-28',\n\t\tendTime: '2024-03-30',\n\t\t\t\t\t\tlocation: '磁县会展中心',\n\t\t\t\t\t\tdescription: '展示磁州特色农产品，促进农产品销售',\n\t\tstatus: '未开始',\n\t\tparticipantsCount: 89,\n\t\tviewsCount: 1253,\n\t\tcommentsCount: 17\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 3,\n\t\t\t\t\t\ttitle: '磁州文化节',\n\t\tcoverImage: '/static/images/banner/banner-3.jpg',\n\t\tstartTime: '2024-04-01',\n\t\tendTime: '2024-04-07',\n\t\t\t\t\t\tlocation: '磁州古镇',\n\t\t\t\t\t\tdescription: '传承磁州文化，弘扬地方特色',\n\t\tstatus: '未开始',\n\t\tparticipantsCount: 65,\n\t\tviewsCount: 980,\n\t\tcommentsCount: 8\n\t},\n\t{\n\t\tid: 4,\n\t\ttitle: '春季招聘会',\n\t\tcoverImage: '/static/images/banner/banner-4.jpg',\n\t\tstartTime: '2024-03-15',\n\t\tendTime: '2024-03-16',\n\t\tlocation: '磁县人才市场',\n\t\tdescription: '提供就业机会，促进人才交流',\n\t\tstatus: '已结束',\n\t\tparticipantsCount: 356,\n\t\tviewsCount: 4230,\n\t\tcommentsCount: 76\n\t}\n]);\n\n// 加载和分页状态\nconst loading = ref(false);\nconst hasMore = ref(false);\n\n// 格式化日期\nconst formatDate = (dateString) => {\n\tif (!dateString) return '';\n\tconst date = new Date(dateString);\n\treturn `${date.getMonth() + 1}.${date.getDate()}`;\n};\n\n// 计算筛选后的活动列表\nconst filteredActivities = computed(() => {\n\tif (currentStatus.value === 'all') {\n\t\treturn activityList;\n\t}\n\treturn activityList.filter(item => item.status === currentStatus.value);\n});\n\n// 页面加载\nonLoad(() => {\n\t// 隐藏系统导航栏\n\tuni.hideNavigationBar();\n});\n\n// 页面显示\nonShow(() => {\n\t// 确保每次显示时都隐藏系统导航栏\n\tuni.hideNavigationBar();\n});\n\n// 初始化\nonMounted(() => {\n\t// 获取状态栏高度\n\tuni.getSystemInfo({\n\t\tsuccess: (res) => {\n\t\t\tstatusBarHeight.value = res.statusBarHeight;\n\t\t}\n\t});\n});\n\n// 返回上一页\nconst goBack = () => {\n\tuni.navigateBack();\n};\n\n// 显示筛选选项\nconst showFilterOptions = () => {\n\tuni.showActionSheet({\n\t\titemList: ['按时间排序', '按热度排序', '按参与人数排序'],\n\t\tsuccess: (res) => {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '排序功能已选择',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t}\n\t});\n};\n\n// 筛选活动\nconst filterByStatus = (status) => {\n\tcurrentStatus.value = status;\n};\n\n// 加载更多\nconst loadMore = () => {\n\tif (loading.value || !hasMore.value) return;\n\tloading.value = true;\n\t\n\tsetTimeout(() => {\n\t\tloading.value = false;\n\t}, 1000);\n};\n\n// 查看活动详情\nconst viewActivityDetail = (id) => {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/subPackages/activity/pages/detail?id=${id}`\n\t});\n};\n\n// 编辑活动\nconst editActivity = (id) => {\n\tuni.navigateTo({\n\t\turl: `/subPackages/activity/pages/edit?id=${id}`\n\t});\n};\n\n// 推广活动\nconst promoteActivity = (id) => {\n\tuni.showActionSheet({\n\t\titemList: ['活动置顶', '首页推荐', '短信推广', '社群分享'],\n\t\tsuccess: (res) => {\n\t\t\tconst options = ['活动置顶', '首页推荐', '短信推广', '社群分享'];\n\t\t\tuni.showToast({\n\t\t\t\ttitle: `已选择${options[res.tapIndex]}`,\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t}\n\t});\n};\n\n// 分享活动\nconst shareActivity = (id) => {\n\tuni.showShareMenu({\n\t\twithShareTicket: true,\n\t\tmenus: ['shareAppMessage', 'shareTimeline']\n\t});\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n\tdisplay: flex;\n\tflex-direction: column;\n\twidth: 100%;\n\theight: 100vh;\n\t\tbackground-color: #f5f5f5;\n\tposition: relative;\n\toverflow: hidden;\n}\n\n/* 自定义导航栏 */\n.custom-navbar {\n\twidth: 100%;\n\tbackground: linear-gradient(to right, #3a8afe, #6f7bfd);\n\tz-index: 100;\n\tposition: relative;\n}\n\n.status-bar {\n\twidth: 100%;\n}\n\n.navbar-content {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\theight: 44px;\n\tpadding: 0 12px;\n}\n\n.navbar-left {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.back-button {\n\tpadding: 8px;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.back-icon {\n\twidth: 20px;\n\theight: 20px;\n}\n\n.navbar-title {\n\tflex: 1;\n\ttext-align: center;\n}\n\n.title-text {\n\tfont-size: 18px;\n\tfont-weight: 800;\n\tcolor: white;\n}\n\n.navbar-right {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.filter-button {\n\tpadding: 8px;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n/* 状态筛选 */\n.filter-tabs {\n\tdisplay: flex;\n\tbackground-color: white;\n\tborder-bottom: 1px solid #e0e0e0;\n\tz-index: 10;\n\tmargin-bottom: 16px;\n}\n\n.tab-item {\n\tflex: 1;\n\tpadding: 12px 0;\n\ttext-align: center;\n\tposition: relative;\n}\n\n.tab-text {\n\tfont-size: 14px;\n\tcolor: #666;\n}\n\n.tab-item.active .tab-text {\n\tcolor: #3a8afe;\n\tfont-weight: 500;\n}\n\n.tab-item.active::after {\n\tcontent: '';\n\tposition: absolute;\n\tbottom: 0;\n\tleft: 50%;\n\ttransform: translateX(-50%);\n\twidth: 20px;\n\theight: 3px;\n\tbackground-color: #3a8afe;\n\tborder-radius: 3px;\n}\n\n/* 活动列表 */\n\t.activity-list {\n\tflex: 1;\n\toverflow-y: auto;\n\tpadding: 0 16px;\n}\n\n.empty-state {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\theight: 100%;\n\tpadding: 32px;\n}\n\n.empty-icon {\n\twidth: 80px;\n\theight: 80px;\n\tmargin-bottom: 16px;\n}\n\n.empty-text {\n\tfont-size: 16px;\n\tcolor: #999;\n\ttext-align: center;\n}\n\n/* 活动卡片 */\n.activity-card {\n\tbackground-color: white;\n\tborder-radius: 20px;\n\tmargin-bottom: 20px;\n\tbox-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);\n\t\toverflow: hidden;\n\twidth: 90%;\n\tmargin-left: auto;\n\tmargin-right: auto;\n\ttransition: transform 0.2s, box-shadow 0.2s;\n}\n\n.activity-card:active {\n\ttransform: scale(0.98);\n\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.card-image {\n\t\twidth: 100%;\n\theight: 180px;\n\tbackground-size: cover;\n\tbackground-position: center;\n}\n\n.card-content {\n\tpadding: 18px;\n}\n\n.card-title {\n\tfont-size: 18px;\n\tfont-weight: 600;\n\t\tcolor: #333;\n\tmargin-bottom: 10px;\n\tline-height: 1.4;\n\ttext-align: center;\n\t}\n\t\n.card-info {\n\t\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tflex-wrap: wrap;\n\tmargin-bottom: 12px;\n}\n\n.info-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\tmargin: 0 10px 6px;\n}\n\n.info-icon {\n\twidth: 16px;\n\theight: 16px;\n\tmargin-right: 4px;\n}\n\n.info-text {\n\tfont-size: 13px;\n\t\tcolor: #666;\n\t}\n\t\n.card-status {\n\tdisplay: block;\n\tpadding: 6px 0;\n\tborder-radius: 16px;\n\tfont-size: 13px;\n\tfont-weight: 500;\n\ttext-align: center;\n\twidth: 100px;\n\tmargin: 0 auto 12px;\n}\n\n.status-ongoing {\n\tbackground-color: rgba(58, 138, 254, 0.1);\n\tcolor: #3a8afe;\n}\n\n.status-upcoming {\n\tbackground-color: rgba(111, 123, 253, 0.1);\n\tcolor: #6f7bfd;\n\t}\n\t\n\t.status-ended {\n\tbackground-color: rgba(128, 128, 128, 0.1);\n\tcolor: #808080;\n}\n\n.card-actions {\n\tdisplay: flex;\n\tjustify-content: space-around;\n\tmargin-top: 12px;\n\tborder-top: 1px solid #f0f0f0;\n\tpadding-top: 12px;\n}\n\n.action-button {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tpadding: 8px 20px;\n\tborder-radius: 16px;\n}\n\n.action-button:active {\n\tbackground-color: rgba(0, 0, 0, 0.05);\n}\n\n.action-icon {\n\twidth: 22px;\n\theight: 22px;\n\tmargin-bottom: 6px;\n}\n\n.action-text {\n\tfont-size: 12px;\n\tcolor: #666;\n}\n\n/* 加载状态 */\n.loading-container {\n\tdisplay: flex;\n\tjustify-content: center;\n\tpadding: 16px 0;\n}\n\n.loading-text {\n\tfont-size: 14px;\n\tcolor: #999;\n}\n\n/* 没有更多数据提示 */\n.no-more {\n\ttext-align: center;\n\tpadding: 16px 0;\n\tfont-size: 14px;\n\tcolor: #999;\n}\n\n/* 隐藏滚动条 */\n::-webkit-scrollbar {\n\tdisplay: none;\n\twidth: 0 !important;\n\theight: 0 !important;\n\t-webkit-appearance: none;\n\tbackground: transparent;\n\t}\n</style> \n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity/pages/list.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "computed", "onLoad", "uni", "onShow", "onMounted"], "mappings": ";;;;;;;AA0IC,oBAAe;AAAA;AAAA,EAEf,iBAAiB;AAClB;;;;AAQA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAGTA,kBAAG,IAAC,KAAK;AAG9B,UAAM,gBAAgBA,cAAAA,IAAI,KAAK;AAG/B,UAAM,eAAeC,cAAAA,SAAS;AAAA,MACzB;AAAA,QACC,IAAI;AAAA,QACJ,OAAO;AAAA,QACX,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,SAAS;AAAA,QACL,UAAU;AAAA,QACV,aAAa;AAAA,QACjB,QAAQ;AAAA,QACR,mBAAmB;AAAA,QACnB,YAAY;AAAA,QACZ,eAAe;AAAA,MACX;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,OAAO;AAAA,QACX,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,SAAS;AAAA,QACL,UAAU;AAAA,QACV,aAAa;AAAA,QACjB,QAAQ;AAAA,QACR,mBAAmB;AAAA,QACnB,YAAY;AAAA,QACZ,eAAe;AAAA,MACX;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,OAAO;AAAA,QACX,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,SAAS;AAAA,QACL,UAAU;AAAA,QACV,aAAa;AAAA,QACjB,QAAQ;AAAA,QACR,mBAAmB;AAAA,QACnB,YAAY;AAAA,QACZ,eAAe;AAAA,MACf;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,SAAS;AAAA,QACT,UAAU;AAAA,QACV,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,mBAAmB;AAAA,QACnB,YAAY;AAAA,QACZ,eAAe;AAAA,MACf;AAAA,IACF,CAAC;AAGD,UAAM,UAAUD,cAAAA,IAAI,KAAK;AACzB,UAAM,UAAUA,cAAAA,IAAI,KAAK;AAGzB,UAAM,aAAa,CAAC,eAAe;AAClC,UAAI,CAAC;AAAY,eAAO;AACxB,YAAM,OAAO,IAAI,KAAK,UAAU;AAChC,aAAO,GAAG,KAAK,aAAa,CAAC,IAAI,KAAK,QAAS,CAAA;AAAA,IAChD;AAGA,UAAM,qBAAqBE,cAAQ,SAAC,MAAM;AACzC,UAAI,cAAc,UAAU,OAAO;AAClC,eAAO;AAAA,MACP;AACD,aAAO,aAAa,OAAO,UAAQ,KAAK,WAAW,cAAc,KAAK;AAAA,IACvE,CAAC;AAGDC,kBAAAA,OAAO,MAAM;AAEZC,oBAAG,MAAC,kBAAiB;AAAA,IACtB,CAAC;AAGDC,kBAAAA,OAAO,MAAM;AAEZD,oBAAG,MAAC,kBAAiB;AAAA,IACtB,CAAC;AAGDE,kBAAAA,UAAU,MAAM;AAEfF,oBAAAA,MAAI,cAAc;AAAA,QACjB,SAAS,CAAC,QAAQ;AACjB,0BAAgB,QAAQ,IAAI;AAAA,QAC5B;AAAA,MACH,CAAE;AAAA,IACF,CAAC;AAGD,UAAM,SAAS,MAAM;AACpBA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAGA,UAAM,oBAAoB,MAAM;AAC/BA,oBAAAA,MAAI,gBAAgB;AAAA,QACnB,UAAU,CAAC,SAAS,SAAS,SAAS;AAAA,QACtC,SAAS,CAAC,QAAQ;AACjBA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAI;AAAA,QACD;AAAA,MACH,CAAE;AAAA,IACF;AAGA,UAAM,iBAAiB,CAAC,WAAW;AAClC,oBAAc,QAAQ;AAAA,IACvB;AAGA,UAAM,WAAW,MAAM;AACtB,UAAI,QAAQ,SAAS,CAAC,QAAQ;AAAO;AACrC,cAAQ,QAAQ;AAEhB,iBAAW,MAAM;AAChB,gBAAQ,QAAQ;AAAA,MAChB,GAAE,GAAI;AAAA,IACR;AAGA,UAAM,qBAAqB,CAAC,OAAO;AAC/BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,yCAAyC,EAAE;AAAA,MACrD,CAAE;AAAA,IACF;AAGA,UAAM,eAAe,CAAC,OAAO;AAC5BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,uCAAuC,EAAE;AAAA,MAChD,CAAE;AAAA,IACF;AAGA,UAAM,kBAAkB,CAAC,OAAO;AAC/BA,oBAAAA,MAAI,gBAAgB;AAAA,QACnB,UAAU,CAAC,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QACzC,SAAS,CAAC,QAAQ;AACjB,gBAAM,UAAU,CAAC,QAAQ,QAAQ,QAAQ,MAAM;AAC/CA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,MAAM,QAAQ,IAAI,QAAQ,CAAC;AAAA,YAClC,MAAM;AAAA,UACV,CAAI;AAAA,QACD;AAAA,MACH,CAAE;AAAA,IACF;AAGA,UAAM,gBAAgB,CAAC,OAAO;AAC7BA,oBAAAA,MAAI,cAAc;AAAA,QACjB,iBAAiB;AAAA,QACjB,OAAO,CAAC,mBAAmB,eAAe;AAAA,MAC5C,CAAE;AAAA,IACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChUA,GAAG,WAAW,eAAe;"}