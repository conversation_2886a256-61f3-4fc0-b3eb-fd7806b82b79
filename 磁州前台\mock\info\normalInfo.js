// 普通信息列表模拟数据
export const allInfoList = [
  { id: 'business-transfer-restaurant-1', category: '生意转让', content: '【红包】黄金地段餐饮店整体转让，地处商业中心，设备齐全可直接营业！', time: '2024-05-16 08:30', views: 186, pageType: 'business-transfer-detail', hasRedPacket: true, redPacketAmount: '15.88', redPacketType: 'fixed', redPacketCount: 30, redPacketRemain: 18 },
  { id: 'business-transfer-no-red-1', category: '生意转让', content: '县城中心奶茶店转让，客流稳定，接手即可盈利，因个人原因急转', time: '2024-05-16 10:15', views: 135, pageType: 'business-transfer-detail', hasRedPacket: false },
  { id: 'red-packet-service-1', category: '到家服务', content: '【红包】专业家庭保洁服务，首单立减20元，预约送10元现金红包！', time: '2024-05-15 09:30', views: 235, pageType: 'home-service-detail', hasRedPacket: true, redPacketAmount: '10.00', redPacketType: 'fixed', redPacketCount: 50, redPacketRemain: 32 },
  { id: 'service-no-red-1', category: '到家服务', content: '专业上门维修空调、冰箱、洗衣机等家电，技术精湛，价格公道', time: '2024-05-15 11:30', views: 176, pageType: 'home-service-detail', hasRedPacket: false },
  { id: 'red-packet-job-1', category: '招聘信息', content: '【红包】招聘销售经理5名，底薪5000+提成，咨询简历投递送随机红包！', time: '2024-05-14 15:15', views: 456, pageType: 'job-detail', hasRedPacket: true, redPacketAmount: '66.66', redPacketType: 'random', redPacketCount: 30, redPacketRemain: 12 },
  { id: 'job-no-red-1', category: '招聘信息', content: '急招会计1名，五险一金，双休，2年以上工作经验，薪资4500-6000', time: '2024-05-15 14:30', views: 208, pageType: 'job-detail', hasRedPacket: false },
  { id: 'job-seeking-no-red-1', category: '求职信息', content: '计算机专业应届毕业生求职，熟悉前端开发，有项目经验，可立即上岗', time: '2024-05-15 16:45', views: 125, pageType: 'job-seeking-detail', hasRedPacket: false },
  { id: 'red-packet-house-1', category: '房屋出租', content: '【红包】市中心精装两室一厅出租，家电齐全，看房送15元现金红包！', time: '2024-05-13 14:30', views: 289, pageType: 'house-rent-detail', hasRedPacket: true, redPacketAmount: '15.00', redPacketType: 'fixed', redPacketCount: 20, redPacketRemain: 8 },
  { id: 'house-rent-no-red-1', category: '房屋出租', content: '学区房两室一厅出租，家电家具齐全，拎包入住，交通便利', time: '2024-05-14 10:20', views: 197, pageType: 'house-rent-detail', hasRedPacket: false },
  { id: 'house-sale-no-red-1', category: '房屋出售', content: '县城南区新房，三室两厅，120平米，毛坯房，采光好，有车位', time: '2024-05-14 09:15', views: 268, pageType: 'house-sale-detail', hasRedPacket: false },
  { id: 'red-packet-car-1', category: '二手车辆', content: '【红包】2023款本田雅阁2.0L，准新车，行驶5000公里，查询车况送红包！', time: '2024-05-12 13:25', views: 376, pageType: 'car-detail', hasRedPacket: true, redPacketAmount: '20.00', redPacketType: 'fixed', redPacketCount: 25, redPacketRemain: 10 },
  { id: 'car-no-red-1', category: '二手车辆', content: '2020款大众朗逸，1.5L自动挡，行驶3万公里，无事故，一手车', time: '2024-05-14 13:40', views: 189, pageType: 'car-detail', hasRedPacket: false },
  { id: 'finding-service-1', category: '寻找服务', content: '寻找专业水电工，需要重新布线和改水管，有意者请联系', time: '2024-05-14 17:30', views: 142, pageType: 'find-service-detail', hasRedPacket: false },
  { id: 'pet-no-red-1', category: '宠物信息', content: '家养蓝猫幼崽出售，2个月大，已驱虫，疫苗已做，可上门看猫', time: '2024-05-14 15:20', views: 162, pageType: 'pet-detail', hasRedPacket: false },
  { id: 'merchant-activity-no-red-1', category: '商家活动', content: '新开张火锅店满减活动，满100减30，满200减80，还有精美礼品赠送', time: '2024-05-14 11:25', views: 248, pageType: 'merchant-activity-detail', hasRedPacket: false },
  { id: 'red-packet-vehicle-1', category: '车辆服务', content: '【红包】专业汽车美容贴膜，隐形车衣，预约试用送50元红包！', time: '2024-05-11 11:45', views: 198, pageType: 'vehicle-service-detail', hasRedPacket: true, redPacketAmount: '50.00', redPacketType: 'fixed', redPacketCount: 10, redPacketRemain: 5 },
  { id: 'vehicle-service-no-red-1', category: '车辆服务', content: '专业汽车保养，机油三滤更换，四轮定位，价格实惠，技术可靠', time: '2024-05-14 16:30', views: 156, pageType: 'vehicle-service-detail', hasRedPacket: false },
  { id: 'red-packet-second-hand-1', category: '二手闲置', content: '【红包】全新iPhone 15 Pro Max，黑色256G，抽奖送华为手环！', time: '2024-05-10 10:20', views: 468, pageType: 'second-hand-detail', hasRedPacket: true, redPacketAmount: '88.88', redPacketType: 'random', redPacketCount: 5, redPacketRemain: 2 },
  { id: 'second-hand-no-red-1', category: '二手闲置', content: '9成新MacBook Pro 2022款，M1芯片，16G内存，512G硬盘，原价12999', time: '2024-05-14 17:10', views: 215, pageType: 'second-hand-detail', hasRedPacket: false },
  { id: 'ride-share-no-red-1', category: '磁州拼车', content: '每天早上7点县城到邯郸拼车，轿车舒适，准时发车，长期有效', time: '2024-05-14 18:20', views: 183, pageType: 'carpool-detail', hasRedPacket: false },
  { id: 'education-no-red-1', category: '教育培训', content: '小学初中高中各科辅导，一对一定制教学计划，提分效果明显', time: '2024-05-14 19:10', views: 134, pageType: 'education-detail', hasRedPacket: false },
  { id: 'dating-red-1', category: '婚恋交友', content: '【红包】28岁女士，本科学历，身高165cm，温柔大方，期待遇见有缘人', time: '2024-05-16 09:15', views: 228, pageType: 'dating-detail', hasRedPacket: true, redPacketAmount: '18.88', redPacketType: 'fixed', redPacketCount: 20, redPacketRemain: 12 },
  { id: 'dating-no-red-1', category: '婚恋交友', content: '32岁男士，身高178cm，事业稳定，性格开朗，寻找志同道合的另一半', time: '2024-05-15 14:20', views: 176, pageType: 'dating-detail', hasRedPacket: false },
  { id: 'other-service-1', category: '其他服务', content: '提供专业文件翻译服务，英语、日语、韩语等多语种互译，价格合理', time: '2024-05-15 11:20', views: 158, pageType: 'other-service-detail', hasRedPacket: false }
];

// 模拟获取普通信息列表的API函数
export const fetchAllInfo = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(allInfoList);
    }, 300);
  });
};

// 根据分类过滤信息列表
export const fetchInfoByCategory = (category) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      if (!category || category === '全部') {
        resolve(allInfoList);
      } else {
        const filteredList = allInfoList.filter(item => item.category === category);
        resolve(filteredList);
      }
    }, 300);
  });
}; 