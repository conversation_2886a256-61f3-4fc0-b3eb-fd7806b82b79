<view class="activity-center data-v-26a06813"><view class="activity-header data-v-26a06813"><view class="title-container data-v-26a06813"><view class="title-bar data-v-26a06813"></view><text class="activity-title data-v-26a06813">同城活动中心</text><view class="activity-badge data-v-26a06813"><text class="badge-text data-v-26a06813">精选推荐</text></view></view><view class="more-btn data-v-26a06813" bindtap="{{c}}"><text class="more-text data-v-26a06813">更多</text><view class="more-icon data-v-26a06813"><svg wx:if="{{b}}" class="data-v-26a06813" u-s="{{['d']}}" u-i="26a06813-0" bind:__l="__l" u-p="{{b}}"><polyline wx:if="{{a}}" class="data-v-26a06813" u-i="26a06813-1,26a06813-0" bind:__l="__l" u-p="{{a}}"></polyline></svg></view></view></view><view class="activity-tabs-container data-v-26a06813"><scroll-view class="activity-tabs data-v-26a06813" scroll-x show-scrollbar="false" scroll-with-animation="{{true}}" enhanced="{{true}}" bounces="{{true}}"><view wx:for="{{d}}" wx:for-item="tab" wx:key="y" class="{{['activity-tab', 'data-v-26a06813', tab.z && 'active']}}" bindtap="{{tab.A}}"><view class="tab-content data-v-26a06813"><view wx:if="{{tab.a}}" class="tab-icon data-v-26a06813"><svg wx:if="{{tab.t}}" class="data-v-26a06813" u-s="{{['d']}}" u-i="{{tab.s}}" bind:__l="__l" u-p="{{tab.t}}"><path wx:if="{{tab.b}}" class="data-v-26a06813" u-i="{{tab.c}}" bind:__l="__l" u-p="{{tab.d}}"></path><path wx:elif="{{tab.e}}" class="data-v-26a06813" u-i="{{tab.f}}" bind:__l="__l" u-p="{{tab.g}}"></path><path wx:elif="{{tab.h}}" class="data-v-26a06813" u-i="{{tab.i}}" bind:__l="__l" u-p="{{tab.j}}"></path><path wx:elif="{{tab.k}}" class="data-v-26a06813" u-i="{{tab.l}}" bind:__l="__l" u-p="{{tab.m}}"></path><path wx:elif="{{tab.n}}" class="data-v-26a06813" u-i="{{tab.o}}" bind:__l="__l" u-p="{{tab.p}}"></path><circle wx:else class="data-v-26a06813" u-i="{{tab.q}}" bind:__l="__l" u-p="{{tab.r||''}}"></circle></svg></view><text class="tab-text data-v-26a06813">{{tab.v}}</text></view><view wx:if="{{tab.w}}" class="tab-line data-v-26a06813"></view><view wx:if="{{tab.x}}" class="tab-glow data-v-26a06813"></view></view></scroll-view></view><view class="activity-scroll-container data-v-26a06813"><view wx:if="{{e}}" class="carousel-controls data-v-26a06813"><view bindtap="{{h}}" class="{{['control-btn', 'prev-btn', 'data-v-26a06813', i && 'disabled']}}"><svg wx:if="{{g}}" class="data-v-26a06813" u-s="{{['d']}}" u-i="26a06813-9" bind:__l="__l" u-p="{{g}}"><polyline wx:if="{{f}}" class="data-v-26a06813" u-i="26a06813-10,26a06813-9" bind:__l="__l" u-p="{{f}}"></polyline></svg></view><view class="control-btn next-btn data-v-26a06813" bindtap="{{l}}"><svg wx:if="{{k}}" class="data-v-26a06813" u-s="{{['d']}}" u-i="26a06813-11" bind:__l="__l" u-p="{{k}}"><polyline wx:if="{{j}}" class="data-v-26a06813" u-i="26a06813-12,26a06813-11" bind:__l="__l" u-p="{{j}}"></polyline></svg></view></view><scroll-view class="activity-scroll data-v-26a06813" scroll-x show-scrollbar="false" scroll-with-animation="{{true}}" enhanced="{{true}}" bounces="{{true}}" scroll-left="{{p}}" ref="activityScrollRef" bindtouchstart="{{q}}" bindtouchend="{{r}}" bindscroll="{{s}}"><view class="activity-list data-v-26a06813" style="{{'padding-left:' + n + ';' + ('padding-right:' + o)}}"><view wx:for="{{m}}" wx:for-item="item" wx:key="f" class="activity-item data-v-26a06813" style="{{'transform:' + item.g + ';' + ('opacity:' + item.h)}}"><activity-card-factory wx:if="{{item.e}}" class="data-v-26a06813" bindnavigate="{{item.a}}" bindfavorite="{{item.b}}" bindaction="{{item.c}}" u-i="{{item.d}}" bind:__l="__l" u-p="{{item.e}}"/></view></view></scroll-view><view wx:if="{{t}}" class="carousel-indicators data-v-26a06813"><view wx:for="{{v}}" wx:for-item="_" wx:key="a" class="{{['indicator-dot', 'data-v-26a06813', _.b && 'active']}}" bindtap="{{_.c}}"></view></view></view></view>