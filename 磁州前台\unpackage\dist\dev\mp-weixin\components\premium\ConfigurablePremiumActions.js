"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const utils_promotionConfig = require("../../utils/promotion-config.js");
const _sfc_main = {
  __name: "ConfigurablePremiumActions",
  props: {
    pageType: {
      type: String,
      default: ""
    },
    showMode: {
      type: String,
      default: "direct"
    },
    itemData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ["action-completed", "action-cancelled"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const promotionConfig = common_vendor.ref(null);
    const configLoaded = common_vendor.ref(false);
    const isCarpool = common_vendor.computed(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const route = currentPage ? currentPage.route || "" : "";
      return route.includes("carpool");
    });
    const showPaymentModal = common_vendor.ref(false);
    const selectedDuration = common_vendor.ref("");
    const currentPaymentAction = common_vendor.ref("");
    const paymentModalConfig = common_vendor.ref({
      title: "",
      description: "",
      options: []
    });
    const selectDirectOption = (action, optionType) => {
      common_vendor.index.__f__("log", "at components/premium/ConfigurablePremiumActions.vue:326", "选择操作:", action, optionType);
      let option = null;
      if (action === "publish") {
        if (optionType === "ad") {
          if (isCarpool.value) {
            option = {
              title: "免费发布",
              subtitle: "观看15秒广告发布一条信息",
              price: "免费",
              icon: "/static/images/premium/ad-publish.png",
              type: "ad",
              duration: "一条信息"
            };
          } else {
            option = {
              title: "免费发布",
              subtitle: "观看15秒广告后发布",
              price: "免费",
              icon: "/static/images/premium/ad-publish.png",
              type: "ad",
              duration: "1天"
            };
          }
        }
      } else if (action === "top") {
        if (optionType === "ad") {
          option = {
            title: "广告置顶",
            subtitle: "观看30秒广告获得2小时置顶",
            price: "免费",
            icon: "/static/images/premium/ad-top.png",
            type: "ad",
            duration: "2小时"
          };
        }
      } else if (action === "refresh") {
        if (optionType === "ad") {
          option = {
            title: "广告刷新",
            subtitle: "观看15秒广告刷新一次",
            price: "免费",
            icon: "/static/images/premium/ad-refresh.png",
            type: "ad"
          };
        }
      } else if (action === "join") {
        if (optionType === "ad") {
          option = {
            title: "看广告入驻",
            subtitle: "观看30秒广告获得1个月免费特权",
            price: "免费",
            icon: "/static/images/premium/ad-join.png",
            type: "ad",
            duration: "1个月"
          };
        }
      } else if (action === "renew") {
        if (optionType === "ad") {
          option = {
            title: "看广告续费",
            subtitle: "观看30秒广告延长7天会员",
            price: "免费",
            icon: "/static/images/premium/ad-renew.png",
            type: "ad",
            duration: "7天"
          };
        }
      }
      if (optionType === "ad") {
        if (option) {
          showAd(option, action);
        }
      } else if (optionType === "paid") {
        openPaymentModal(action);
      }
    };
    const showAd = (option, action) => {
      common_vendor.index.showLoading({
        title: "正在加载广告..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "广告观看完成",
          icon: "success"
        });
        emit("action-completed", {
          action,
          type: "ad",
          data: option
        });
      }, 2e3);
    };
    const openPaymentModal = async (action) => {
      var _a;
      currentPaymentAction.value = action;
      selectedDuration.value = "";
      try {
        const pricingOptions = await utils_promotionConfig.getPricingConfig(action);
        let title = "选择时长";
        let description = "选择您需要的时长";
        if (action === "publish") {
          title = "选择发布时长";
          description = "选择您希望发布的时长";
        } else if (action === "top") {
          title = "选择置顶时长";
          description = "选择您希望置顶的时长";
        } else if (action === "refresh") {
          title = "选择刷新套餐";
          description = "选择您需要的刷新次数";
        } else if (action === "join") {
          title = "选择入驻时长";
          description = "选择您的商家会员时长";
        } else if (action === "renew") {
          title = "选择续费时长";
          description = "选择您要续费的时长";
        }
        paymentModalConfig.value = {
          title,
          description,
          options: pricingOptions || []
        };
        showPaymentModal.value = true;
        utils_promotionConfig.logUserAction("open_payment_modal", {
          action,
          pageType: props.pageType,
          itemId: (_a = props.itemData) == null ? void 0 : _a.id
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at components/premium/ConfigurablePremiumActions.vue:477", "获取价格配置失败:", error);
        common_vendor.index.showToast({
          title: "获取价格信息失败",
          icon: "none"
        });
      }
    };
    const selectDuration = (option) => {
      selectedDuration.value = option.duration;
    };
    const closePaymentModal = () => {
      showPaymentModal.value = false;
      selectedDuration.value = "";
      if (currentPaymentAction.value) {
        emit("action-cancelled", {
          action: currentPaymentAction.value,
          type: "payment",
          reason: "user_cancelled"
        });
      }
      currentPaymentAction.value = "";
    };
    const confirmPayment = () => {
      if (!selectedDuration.value) {
        common_vendor.index.showToast({
          title: "请选择时长",
          icon: "none"
        });
        return;
      }
      const selectedOption = paymentModalConfig.value.options.find(
        (option) => option.duration === selectedDuration.value
      );
      if (!selectedOption)
        return;
      showPaymentModal.value = false;
      common_vendor.index.showLoading({
        title: "正在支付..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "支付成功",
          icon: "success"
        });
        emit("action-completed", {
          action: currentPaymentAction.value,
          type: "payment",
          data: {
            title: `付费${currentPaymentAction.value === "publish" ? "发布" : currentPaymentAction.value === "top" ? "置顶" : currentPaymentAction.value === "refresh" ? "刷新" : currentPaymentAction.value === "join" ? "入驻" : "续费"}`,
            duration: selectedOption.duration,
            price: selectedOption.price,
            type: "payment"
          }
        });
        selectedDuration.value = "";
        currentPaymentAction.value = "";
      }, 1500);
    };
    const initConfig = async () => {
      try {
        const enabled = await utils_promotionConfig.isFeatureEnabled(props.pageType);
        if (!enabled) {
          common_vendor.index.__f__("log", "at components/premium/ConfigurablePremiumActions.vue:566", "推广功能已禁用:", props.pageType);
          return;
        }
        promotionConfig.value = await utils_promotionConfig.getPromotionConfig();
        configLoaded.value = true;
        common_vendor.index.__f__("log", "at components/premium/ConfigurablePremiumActions.vue:574", "推广配置加载完成:", promotionConfig.value);
      } catch (error) {
        common_vendor.index.__f__("error", "at components/premium/ConfigurablePremiumActions.vue:576", "初始化推广配置失败:", error);
        configLoaded.value = true;
      }
    };
    common_vendor.onMounted(() => {
      initConfig();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: __props.showMode === "direct"
      }, __props.showMode === "direct" ? common_vendor.e({
        b: __props.pageType === "publish"
      }, __props.pageType === "publish" ? common_vendor.e({
        c: common_assets._imports_0$67,
        d: isCarpool.value
      }, isCarpool.value ? {} : {}, {
        e: common_vendor.o(($event) => selectDirectOption("publish", "ad")),
        f: common_assets._imports_1$60,
        g: isCarpool.value
      }, isCarpool.value ? {} : {}, {
        h: common_vendor.o(($event) => selectDirectOption("publish", "paid"))
      }) : __props.pageType === "top" || __props.pageType === "merchant_top" || __props.pageType === "carpool_top" || __props.pageType === "publish_top" || __props.pageType.endsWith("_top") ? common_vendor.e({
        j: common_assets._imports_2$48,
        k: __props.pageType.startsWith("merchant")
      }, __props.pageType.startsWith("merchant") ? {} : {}, {
        l: common_vendor.o(($event) => selectDirectOption("top", "ad")),
        m: common_assets._imports_3$41,
        n: __props.pageType.startsWith("merchant")
      }, __props.pageType.startsWith("merchant") ? {} : {}, {
        o: common_vendor.o(($event) => selectDirectOption("top", "paid"))
      }) : __props.pageType === "refresh" || __props.pageType === "merchant_refresh" || __props.pageType === "carpool_refresh" || __props.pageType === "publish_refresh" || __props.pageType.endsWith("_refresh") ? common_vendor.e({
        q: common_assets._imports_4$30,
        r: __props.pageType.startsWith("merchant")
      }, __props.pageType.startsWith("merchant") ? {} : {}, {
        s: common_vendor.o(($event) => selectDirectOption("refresh", "ad")),
        t: common_assets._imports_5$27,
        v: __props.pageType.startsWith("merchant")
      }, __props.pageType.startsWith("merchant") ? {} : {}, {
        w: common_vendor.o(($event) => selectDirectOption("refresh", "paid"))
      }) : __props.pageType === "merchant_join" ? {
        y: common_assets._imports_6$23,
        z: common_vendor.o(($event) => selectDirectOption("join", "ad")),
        A: common_assets._imports_7$14,
        B: common_vendor.o(($event) => selectDirectOption("join", "paid"))
      } : __props.pageType === "merchant_renew" ? {
        D: common_assets._imports_8$11,
        E: common_vendor.o(($event) => selectDirectOption("renew", "ad")),
        F: common_assets._imports_9$13,
        G: common_vendor.o(($event) => selectDirectOption("renew", "paid"))
      } : {}, {
        i: __props.pageType === "top" || __props.pageType === "merchant_top" || __props.pageType === "carpool_top" || __props.pageType === "publish_top" || __props.pageType.endsWith("_top"),
        p: __props.pageType === "refresh" || __props.pageType === "merchant_refresh" || __props.pageType === "carpool_refresh" || __props.pageType === "publish_refresh" || __props.pageType.endsWith("_refresh"),
        x: __props.pageType === "merchant_join",
        C: __props.pageType === "merchant_renew"
      }) : {}, {
        H: showPaymentModal.value
      }, showPaymentModal.value ? {
        I: common_vendor.t(paymentModalConfig.value.title),
        J: common_vendor.o(closePaymentModal),
        K: common_vendor.t(paymentModalConfig.value.description),
        L: common_vendor.f(paymentModalConfig.value.options, (option, k0, i0) => {
          return common_vendor.e({
            a: common_vendor.t(option.duration),
            b: common_vendor.t(option.price),
            c: option.recommended
          }, option.recommended ? {} : {}, {
            d: selectedDuration.value === option.duration ? 1 : "",
            e: option.duration,
            f: common_vendor.o(($event) => selectDuration(option), option.duration)
          });
        }),
        M: common_vendor.o(closePaymentModal),
        N: common_vendor.o(confirmPayment),
        O: !selectedDuration.value ? 1 : "",
        P: common_vendor.o(() => {
        }),
        Q: common_vendor.o(closePaymentModal)
      } : {});
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-445b6c30"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/premium/ConfigurablePremiumActions.js.map
