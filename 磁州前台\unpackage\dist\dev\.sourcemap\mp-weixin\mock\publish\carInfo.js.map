{"version": 3, "file": "carInfo.js", "sources": ["mock/publish/carInfo.js"], "sourcesContent": ["// 二手车辆详情模拟数据\r\nexport const carDetail = {\r\n  id: 'car-001',\r\n  title: '2020年丰田卡罗拉1.8L自动挡',\r\n  price: 98000,\r\n  brand: '丰田',\r\n  model: '卡罗拉',\r\n  year: 2020,\r\n  mileage: 3.5, // 行驶里程，单位万公里\r\n  displacement: '1.8L',\r\n  transmission: '自动挡',\r\n  fuelType: '汽油',\r\n  color: '珍珠白',\r\n  licensePlate: '冀E', // 车牌归属地\r\n  licensedTime: '2020-05', // 上牌时间\r\n  annualInspection: '2024-05', // 年检到期时间\r\n  insurance: '2024-05', // 保险到期时间\r\n  transferTimes: 0, // 过户次数\r\n  condition: '九成新',\r\n  description: '个人一手车，无事故，定期4S店保养，车况极佳，内饰干净整洁，发动机变速箱工作正常，随时可以看车试驾。',\r\n  contact: {\r\n    name: '李先生',\r\n    phone: '139****5678',\r\n    wechat: 'lixiansheng123'\r\n  },\r\n  features: ['一手车', '无事故', '4S店保养', '新车质保', '可按揭'],\r\n  configuration: ['倒车影像', '定速巡航', '自动空调', '真皮座椅', '多功能方向盘', '蓝牙/车载电话'],\r\n  images: [\r\n    '/static/images/car/car-1.jpg',\r\n    '/static/images/car/car-2.jpg',\r\n    '/static/images/car/car-3.jpg',\r\n    '/static/images/car/car-4.jpg'\r\n  ],\r\n  location: {\r\n    latitude: 36.314736,\r\n    longitude: 114.711234,\r\n    address: '磁县城区幸福路456号'\r\n  },\r\n  publishTime: '2024-03-12 09:45:00',\r\n  views: 278,\r\n  likes: 18,\r\n  collections: 9,\r\n  status: 'active' // active, sold, expired\r\n};\r\n\r\n// 相关车辆模拟数据\r\nexport const relatedCars = [\r\n  {\r\n    id: 'car-002',\r\n    title: '2019年大众朗逸1.5L手动挡',\r\n    price: 75000,\r\n    brand: '大众',\r\n    model: '朗逸',\r\n    year: 2019,\r\n    mileage: 4.2,\r\n    displacement: '1.5L',\r\n    transmission: '手动挡',\r\n    condition: '八成新',\r\n    image: '/static/images/car/car-5.jpg'\r\n  },\r\n  {\r\n    id: 'car-003',\r\n    title: '2021年本田思域1.5T自动挡',\r\n    price: 128000,\r\n    brand: '本田',\r\n    model: '思域',\r\n    year: 2021,\r\n    mileage: 2.8,\r\n    displacement: '1.5T',\r\n    transmission: '自动挡',\r\n    condition: '九成新',\r\n    image: '/static/images/car/car-6.jpg'\r\n  },\r\n  {\r\n    id: 'car-004',\r\n    title: '2018年日产轩逸1.6L自动挡',\r\n    price: 68000,\r\n    brand: '日产',\r\n    model: '轩逸',\r\n    year: 2018,\r\n    mileage: 5.6,\r\n    displacement: '1.6L',\r\n    transmission: '自动挡',\r\n    condition: '七成新',\r\n    image: '/static/images/car/car-7.jpg'\r\n  }\r\n];\r\n\r\n// 获取二手车辆详情的API函数\r\nexport const fetchCarDetail = (id) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve(carDetail);\r\n    }, 500);\r\n  });\r\n};\r\n\r\n// 获取相关车辆的API函数\r\nexport const fetchRelatedCars = () => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve(relatedCars);\r\n    }, 500);\r\n  });\r\n}; "], "names": [], "mappings": ";AACO,MAAM,YAAY;AAAA,EACvB,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,MAAM;AAAA,EACN,SAAS;AAAA;AAAA,EACT,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,EACV,OAAO;AAAA,EACP,cAAc;AAAA;AAAA,EACd,cAAc;AAAA;AAAA,EACd,kBAAkB;AAAA;AAAA,EAClB,WAAW;AAAA;AAAA,EACX,eAAe;AAAA;AAAA,EACf,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,EACT;AAAA,EACD,UAAU,CAAC,OAAO,OAAO,SAAS,QAAQ,KAAK;AAAA,EAC/C,eAAe,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,UAAU,SAAS;AAAA,EACnE,QAAQ;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,UAAU;AAAA,IACV,WAAW;AAAA,IACX,SAAS;AAAA,EACV;AAAA,EACD,aAAa;AAAA,EACb,OAAO;AAAA,EACP,OAAO;AAAA,EACP,aAAa;AAAA,EACb,QAAQ;AAAA;AACV;AAGO,MAAM,cAAc;AAAA,EACzB;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,SAAS;AAAA,IACT,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,OAAO;AAAA,EACR;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,SAAS;AAAA,IACT,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,OAAO;AAAA,EACR;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,SAAS;AAAA,IACT,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,OAAO;AAAA,EACR;AACH;AAGY,MAAC,iBAAiB,CAAC,OAAO;AACpC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,cAAQ,SAAS;AAAA,IAClB,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,mBAAmB,MAAM;AACpC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,cAAQ,WAAW;AAAA,IACpB,GAAE,GAAG;AAAA,EACV,CAAG;AACH;;;"}