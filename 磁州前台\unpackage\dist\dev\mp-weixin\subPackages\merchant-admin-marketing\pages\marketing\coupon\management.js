"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const CreateButton = () => "../../../components/CreateButton.js";
const _sfc_main = {
  components: {
    CreateButton
  },
  setup() {
    const statistics = common_vendor.reactive({
      total: 35,
      active: 12,
      used: 258,
      conversion: 46.8
    });
    const coupons = common_vendor.ref([
      {
        id: 1,
        title: "新客专享优惠",
        status: "active",
        statusText: "进行中",
        value: 10,
        minSpend: 100,
        expireDate: "2023-05-15",
        usedCount: 234,
        totalCount: 500,
        conversionRate: 46.8
      },
      {
        id: 2,
        title: "满减优惠券",
        status: "active",
        statusText: "进行中",
        value: 20,
        minSpend: 200,
        expireDate: "2023-05-20",
        usedCount: 156,
        totalCount: 300,
        conversionRate: 52
      },
      {
        id: 3,
        title: "节日特别券",
        status: "upcoming",
        statusText: "未开始",
        value: 50,
        minSpend: 300,
        expireDate: "2023-06-10",
        usedCount: 0,
        totalCount: 200,
        conversionRate: 0
      },
      {
        id: 4,
        title: "会员专享券",
        status: "active",
        statusText: "进行中",
        value: 15,
        minSpend: 150,
        expireDate: "2023-05-25",
        usedCount: 89,
        totalCount: 200,
        conversionRate: 44.5
      },
      {
        id: 5,
        title: "周末特惠券",
        status: "expired",
        statusText: "已结束",
        value: 30,
        minSpend: 250,
        expireDate: "2023-04-30",
        usedCount: 178,
        totalCount: 300,
        conversionRate: 59.3
      }
    ]);
    const searchKeyword = common_vendor.ref("");
    const hoveredCoupon = common_vendor.ref(null);
    const displayCoupons = common_vendor.computed(() => {
      if (!searchKeyword.value) {
        return coupons.value;
      }
      const keyword = searchKeyword.value.toLowerCase();
      return coupons.value.filter((coupon) => {
        return coupon.title.toLowerCase().includes(keyword);
      });
    });
    function goBack() {
      common_vendor.index.navigateBack();
    }
    function onSearch(e) {
    }
    function showFilterOptions() {
      common_vendor.index.showActionSheet({
        itemList: ["全部", "进行中", "未开始", "已结束"],
        success: (res) => {
        }
      });
    }
    function showSortOptions() {
      common_vendor.index.showActionSheet({
        itemList: ["默认排序", "面额从高到低", "面额从低到高", "使用率从高到低", "使用率从低到高"],
        success: (res) => {
        }
      });
    }
    function viewCouponDetail(coupon) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/coupon/detail?id=${coupon.id}`,
        animationType: "slide-in-right"
      });
    }
    function editCoupon(coupon) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/coupon/edit?id=${coupon.id}`,
        animationType: "slide-in-right"
      });
    }
    function toggleCouponStatus(coupon) {
      const isActive = coupon.status === "active";
      const newStatus = isActive ? "paused" : "active";
      const statusText = isActive ? "已暂停" : "进行中";
      coupon.status = newStatus;
      coupon.statusText = statusText;
      common_vendor.index.showToast({
        title: isActive ? "已暂停优惠券" : "已启用优惠券",
        icon: "success"
      });
    }
    function confirmDeleteCoupon(coupon) {
      common_vendor.index.showModal({
        title: "确认删除",
        content: `确定要删除"${coupon.title}"吗？此操作无法撤销。`,
        confirmColor: "#FF3B30",
        success: (res) => {
          if (res.confirm) {
            deleteCoupon(coupon);
          }
        }
      });
    }
    function deleteCoupon(coupon) {
      const index = coupons.value.findIndex((item) => item.id === coupon.id);
      if (index > -1) {
        coupons.value.splice(index, 1);
      }
      common_vendor.index.showToast({
        title: "已删除优惠券",
        icon: "success"
      });
    }
    function createNewCoupon() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/coupon/create",
        animationType: "slide-in-right"
      });
    }
    function setHoveredCoupon(couponId) {
      hoveredCoupon.value = couponId;
    }
    function clearHoveredCoupon() {
      hoveredCoupon.value = null;
    }
    common_vendor.onMounted(() => {
    });
    return {
      statistics,
      displayCoupons,
      searchKeyword,
      hoveredCoupon,
      goBack,
      onSearch,
      showFilterOptions,
      showSortOptions,
      viewCouponDetail,
      editCoupon,
      toggleCouponStatus,
      confirmDeleteCoupon,
      createNewCoupon,
      setHoveredCoupon,
      clearHoveredCoupon
    };
  }
};
if (!Array) {
  const _component_CreateButton = common_vendor.resolveComponent("CreateButton");
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_polygon = common_vendor.resolveComponent("polygon");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_rect = common_vendor.resolveComponent("rect");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  (_component_CreateButton + _component_circle + _component_line + _component_svg + _component_polygon + _component_path + _component_rect + _component_polyline)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $setup.goBack && $setup.goBack(...args)),
    b: common_vendor.o($setup.createNewCoupon),
    c: common_vendor.p({
      text: "创建优惠券",
      theme: "coupon"
    }),
    d: common_vendor.t($setup.statistics.total),
    e: common_vendor.t($setup.statistics.active),
    f: common_vendor.t($setup.statistics.used),
    g: common_vendor.t($setup.statistics.conversion),
    h: common_vendor.p({
      cx: "11",
      cy: "11",
      r: "8"
    }),
    i: common_vendor.p({
      x1: "21",
      y1: "21",
      x2: "16.65",
      y2: "16.65"
    }),
    j: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    k: common_vendor.o([($event) => $setup.searchKeyword = $event.detail.value, (...args) => $setup.onSearch && $setup.onSearch(...args)]),
    l: $setup.searchKeyword,
    m: common_vendor.p({
      points: "22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"
    }),
    n: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    o: common_vendor.o((...args) => $setup.showFilterOptions && $setup.showFilterOptions(...args)),
    p: common_vendor.p({
      x1: "4",
      y1: "9",
      x2: "20",
      y2: "9"
    }),
    q: common_vendor.p({
      x1: "4",
      y1: "15",
      x2: "20",
      y2: "15"
    }),
    r: common_vendor.p({
      x1: "10",
      y1: "3",
      x2: "8",
      y2: "21"
    }),
    s: common_vendor.p({
      x1: "16",
      y1: "3",
      x2: "14",
      y2: "21"
    }),
    t: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    v: common_vendor.o((...args) => $setup.showSortOptions && $setup.showSortOptions(...args)),
    w: common_vendor.f($setup.displayCoupons, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.title),
        b: common_vendor.t(item.statusText),
        c: common_vendor.n("status-" + item.status),
        d: common_vendor.t(item.value),
        e: common_vendor.t(item.minSpend),
        f: common_vendor.t(item.expireDate),
        g: common_vendor.t(item.usedCount),
        h: common_vendor.t(item.totalCount),
        i: "f35110dd-12-" + i0 + "," + ("f35110dd-11-" + i0),
        j: "f35110dd-11-" + i0,
        k: common_vendor.o(($event) => $setup.editCoupon(item), index),
        l: item.status === "active"
      }, item.status === "active" ? {
        m: "f35110dd-14-" + i0 + "," + ("f35110dd-13-" + i0),
        n: common_vendor.p({
          x: "6",
          y: "4",
          width: "4",
          height: "16"
        }),
        o: "f35110dd-15-" + i0 + "," + ("f35110dd-13-" + i0),
        p: common_vendor.p({
          x: "14",
          y: "4",
          width: "4",
          height: "16"
        }),
        q: "f35110dd-13-" + i0,
        r: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2"
        })
      } : {
        s: "f35110dd-17-" + i0 + "," + ("f35110dd-16-" + i0),
        t: common_vendor.p({
          points: "5 3 19 12 5 21 5 3"
        }),
        v: "f35110dd-16-" + i0,
        w: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2"
        })
      }, {
        x: common_vendor.t(item.status === "active" ? "暂停" : "启用"),
        y: common_vendor.n(item.status === "active" ? "pause" : "activate"),
        z: common_vendor.o(($event) => $setup.toggleCouponStatus(item), index),
        A: "f35110dd-19-" + i0 + "," + ("f35110dd-18-" + i0),
        B: "f35110dd-20-" + i0 + "," + ("f35110dd-18-" + i0),
        C: "f35110dd-21-" + i0 + "," + ("f35110dd-18-" + i0),
        D: "f35110dd-22-" + i0 + "," + ("f35110dd-18-" + i0),
        E: "f35110dd-18-" + i0,
        F: common_vendor.o(($event) => $setup.confirmDeleteCoupon(item), index),
        G: index,
        H: common_vendor.o(($event) => $setup.viewCouponDetail(item), index),
        I: $setup.hoveredCoupon === item.id ? 1 : "",
        J: common_vendor.o(($event) => $setup.setHoveredCoupon(item.id), index),
        K: common_vendor.o(($event) => $setup.clearHoveredCoupon(), index)
      });
    }),
    x: common_vendor.p({
      d: "M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"
    }),
    y: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    z: common_vendor.p({
      points: "3 6 5 6 21 6"
    }),
    A: common_vendor.p({
      d: "M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"
    }),
    B: common_vendor.p({
      x1: "10",
      y1: "11",
      x2: "10",
      y2: "17"
    }),
    C: common_vendor.p({
      x1: "14",
      y1: "11",
      x2: "14",
      y2: "17"
    }),
    D: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    E: $setup.displayCoupons.length === 0
  }, $setup.displayCoupons.length === 0 ? {
    F: common_vendor.p({
      x: "2",
      y: "4",
      width: "20",
      height: "16",
      rx: "2",
      ry: "2"
    }),
    G: common_vendor.p({
      d: "M12 8v8"
    }),
    H: common_vendor.p({
      d: "M8 12h8"
    }),
    I: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-f35110dd"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/coupon/management.js.map
