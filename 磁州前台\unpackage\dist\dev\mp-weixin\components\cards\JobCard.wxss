
.job-details.data-v-ea7d2210 {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(60, 60, 67, 0.1);
}

/* 公司信息 */
.company-info.data-v-ea7d2210 {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  background: rgba(0, 0, 0, 0.02);
  padding: 16rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.company-logo.data-v-ea7d2210 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-right: 16rpx;
  background: #fff;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}
.company-logo-img.data-v-ea7d2210 {
  width: 100%;
  height: 100%;
}
.company-data.data-v-ea7d2210 {
  flex: 1;
}
.company-name.data-v-ea7d2210 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1c1c1e;
  margin-bottom: 6rpx;
}
.company-meta.data-v-ea7d2210 {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.company-size.data-v-ea7d2210, .company-type.data-v-ea7d2210 {
  font-size: 22rpx;
  color: #8e8e93;
}
.company-auth.data-v-ea7d2210 {
  display: flex;
  align-items: center;
  background: rgba(0, 122, 255, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
}
.auth-text.data-v-ea7d2210 {
  font-size: 22rpx;
  color: #007AFF;
  margin-left: 4rpx;
}

/* 职位要求 */
.job-requirements.data-v-ea7d2210 {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 16rpx;
}
.req-item.data-v-ea7d2210 {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.02);
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
}
.req-icon.data-v-ea7d2210 {
  margin-right: 8rpx;
  color: #8e8e93;
  display: flex;
  align-items: center;
  justify-content: center;
}
.education-icon.data-v-ea7d2210 {
  color: #5856D6;
}
.experience-icon.data-v-ea7d2210 {
  color: #FF9500;
}
.jobtype-icon.data-v-ea7d2210 {
  color: #34C759;
}
.req-text.data-v-ea7d2210 {
  font-size: 24rpx;
  color: #636366;
}

/* 福利标签 */
.job-benefits.data-v-ea7d2210 {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}
.benefit-tag.data-v-ea7d2210 {
  background: linear-gradient(135deg, rgba(0, 122, 255, 0.1), rgba(88, 86, 214, 0.1));
  border-radius: 16rpx;
  padding: 6rpx 16rpx;
  border: 1rpx solid rgba(0, 122, 255, 0.2);
}
.benefit-text.data-v-ea7d2210 {
  font-size: 22rpx;
  color: #007AFF;
  font-weight: 500;
}
