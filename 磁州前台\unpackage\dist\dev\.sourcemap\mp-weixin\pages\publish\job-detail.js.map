{"version": 3, "file": "job-detail.js", "sources": ["pages/publish/job-detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHVibGlzaC9qb2ItZGV0YWlsLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"detail-container job-detail-container\">\n    <!-- 添加自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"navbar-left\" @click=\"goBack\">\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\n      </view>\n      <view class=\"navbar-title\">招聘信息详情</view>\n      <view class=\"navbar-right\">\n        <!-- 占位 -->\n      </view>\n    </view>\n    \n    <!-- 隐藏的分享按钮，用于自动触发 -->\n    <button id=\"shareButton\" class=\"hidden-share-btn\" open-type=\"share\"></button>\n    \n    <!-- 隐藏的Canvas用于绘制海报 -->\n    <canvas canvas-id=\"posterCanvas\" class=\"poster-canvas\" style=\"width: 600px; height: 900px; position: fixed; top: -9999px; left: -9999px;\"></canvas>\n    \n    <!-- 悬浮海报按钮 -->\n    <view class=\"float-poster-btn\" @click=\"generateShareImage\">\n      <image src=\"/static/images/tabbar/海报.png\" class=\"poster-icon\"></image>\n      <text class=\"poster-text\">海报</text>\n    </view>\n    \n    <view class=\"detail-wrapper job-detail-wrapper\">\n      <!-- 顶部公司信息卡片 -->\n      <view class=\"content-card company-card\">\n        <view class=\"section-title\">公司信息</view>\n        <view class=\"publisher-header\">\n          <view class=\"avatar-container\">\n            <image :src=\"jobData.company.logo\" mode=\"aspectFill\" class=\"avatar-image\"></image>\n          </view>\n          <view class=\"publisher-info\">\n            <text class=\"publisher-name\">{{jobData.company.name}}</text>\n            <view class=\"publisher-meta\">\n              <text class=\"meta-text\">{{jobData.company.type}}</text>\n              <text class=\"meta-text\">{{jobData.company.size}}</text>\n              <view class=\"meta-text\" v-if=\"jobData.company.isVerified\">\n                <text class=\"iconfont icon-verified\"></text>\n                <text>已认证</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 职位信息卡片 -->\n      <view class=\"content-card job-card\">\n        <view class=\"job-header\">\n          <view class=\"title-row\">\n            <text class=\"main-title\">{{jobData.title}}</text>\n            <text class=\"price-text\">{{jobData.salary}}</text>\n          </view>\n          <view class=\"meta-info\">\n            <view class=\"tag-group\">\n              <view class=\"info-tag\" v-for=\"(tag, index) in jobData.tags\" :key=\"index\">{{tag}}</view>\n            </view>\n            <text class=\"publish-time\">发布于 {{formatTime(jobData.publishTime)}}</text>\n          </view>\n        </view>\n        \n        <view class=\"basic-info\">\n          <view class=\"info-item\">\n            <text class=\"info-label\">工作地点</text>\n            <text class=\"info-value\">{{jobData.location}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">工作经验</text>\n            <text class=\"info-value\">{{jobData.experience}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">学历要求</text>\n            <text class=\"info-value\">{{jobData.education}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">招聘人数</text>\n            <text class=\"info-value\">{{jobData.headcount}}人</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 职位描述 -->\n      <view class=\"content-card description-card\">\n        <view class=\"section-title\">职位描述</view>\n        <view class=\"description-content\">\n          <rich-text :nodes=\"jobData.description\" class=\"description-text\"></rich-text>\n        </view>\n      </view>\n      \n      <!-- 岗位职责 -->\n      <view class=\"content-card responsibility-card\">\n        <view class=\"section-title\">岗位职责</view>\n        <view class=\"job-responsibility\">\n          <view class=\"list-item\" v-for=\"(item, index) in jobData.responsibilities\" :key=\"index\">\n            <text class=\"list-dot\">•</text>\n            <text class=\"list-text\">{{item}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 任职要求 -->\n      <view class=\"content-card requirement-card\">\n        <view class=\"section-title\">任职要求</view>\n        <view class=\"job-requirement\">\n          <view class=\"list-item\" v-for=\"(item, index) in jobData.requirements\" :key=\"index\">\n            <text class=\"list-dot\">•</text>\n            <text class=\"list-text\">{{item}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 福利待遇 -->\n      <view class=\"content-card benefits-card\">\n        <view class=\"section-title\">福利待遇</view>\n        <view class=\"job-benefits\">\n          <view class=\"benefit-tag\" v-for=\"(benefit, index) in jobData.benefits\" :key=\"index\">\n            <text class=\"benefit-icon iconfont icon-benefit\"></text>\n            <text class=\"benefit-text\">{{benefit}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 公司地址卡片 -->\n      <view class=\"content-card location-card\">\n        <view class=\"section-title\">工作地址</view>\n        <view class=\"location-content\" @click=\"openLocation\">\n          <text class=\"iconfont icon-location\"></text>\n          <text class=\"location-text\">{{jobData.address}}</text>\n          <text class=\"iconfont icon-right location-arrow\"></text>\n        </view>\n        <view class=\"location-map\">\n          <image src=\"/static/images/map-preview.png\" mode=\"aspectFill\" class=\"map-preview\"></image>\n        </view>\n      </view>\n      \n      <!-- 联系方式卡片 -->\n      <view class=\"content-card contact-card\">\n        <view class=\"section-title\">招聘方联系方式</view>\n        <view class=\"contact-content\">\n          <view class=\"contact-item\">\n            <text class=\"contact-label\">联系人</text>\n            <text class=\"contact-value\">{{jobData.contact.name}}</text>\n          </view>\n          <view class=\"contact-item\">\n            <text class=\"contact-label\">电话</text>\n            <text class=\"contact-value contact-phone\" @click=\"callPhone\">{{jobData.contact.phone}}</text>\n          </view>\n          <view class=\"contact-tips\">\n            <text class=\"tips-icon iconfont icon-info\"></text>\n            <text class=\"tips-text\">请说明在\"磁州生活网\"看到的信息</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 红包区域 -->\n      <view class=\"content-card red-packet-card\" v-if=\"jobData.hasRedPacket\">\n        <view class=\"section-title\">红包福利</view>\n        <view class=\"red-packet-section\">\n          <view class=\"red-packet-container\" @click=\"openRedPacket\">\n            <view class=\"red-packet-blur-bg\"></view>\n            <view class=\"red-packet-content\">\n              <view class=\"red-packet-left\">\n                <image class=\"red-packet-icon\" src=\"/static/images/tabbar/抢红包.gif\"></image>\n              <view class=\"red-packet-info\">\n                <view class=\"red-packet-title\">\n                  {{jobData.redPacket.type === 'random' ? '随机金额红包' : '查看职位领红包'}}\n                </view>\n                <view class=\"red-packet-desc\">\n                  还剩{{jobData.redPacket.remain}}个，{{getRedPacketConditionText()}}\n                  </view>\n                </view>\n              </view>\n              <view class=\"red-packet-right\">\n                <view class=\"red-packet-amount\"><text class=\"prefix\">共</text> ¥{{jobData.redPacket.amount}}</view>\n                <view class=\"grab-btn\">立即领取</view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 相关招聘信息卡片 - 默认显示 -->\n      <view class=\"content-card related-jobs-card\">\n        <!-- 标题栏 -->\n        <view class=\"collapsible-header\">\n          <view class=\"section-title\">相关招聘推荐</view>\n        </view>\n        \n        <!-- 内容区 -->\n        <view class=\"collapsible-content\">\n          <!-- 简洁的职位列表 -->\n          <view class=\"related-jobs-list\">\n            <view class=\"related-job-item\" \n                 v-for=\"(job, index) in relatedJobs.slice(0, 3)\" \n                 :key=\"index\" \n                 @click=\"navigateToJobDetail(job.id)\">\n              <view class=\"job-item-content\">\n                <view class=\"job-item-left\">\n                  <image class=\"company-logo\" :src=\"job.companyLogo\" mode=\"aspectFill\"></image>\n                </view>\n                <view class=\"job-item-middle\">\n                  <text class=\"job-item-title\">{{job.title}}</text>\n                  <view class=\"job-item-company\">{{job.companyName}}</view>\n                  <view class=\"job-item-tags\">\n                    <text class=\"job-item-tag\" v-for=\"(tag, tagIndex) in job.tags.slice(0, 2)\" :key=\"tagIndex\">{{tag}}</text>\n                    <text class=\"job-item-tag-more\" v-if=\"job.tags.length > 2\">+{{job.tags.length - 2}}</text>\n                  </view>\n                </view>\n                <view class=\"job-item-right\">\n                  <text class=\"job-item-salary\">{{job.salary}}</text>\n                </view>\n              </view>\n            </view>\n            \n            <!-- 暂无数据提示 -->\n            <view class=\"empty-related-jobs\" v-if=\"relatedJobs.length === 0\">\n              <image src=\"/static/images/empty.png\" class=\"empty-image\" mode=\"aspectFit\"></image>\n              <text class=\"empty-text\">暂无相关招聘</text>\n            </view>\n          </view>\n          \n          <!-- 查看更多按钮 -->\n          <view class=\"view-more-btn\" v-if=\"relatedJobs.length > 0\" @click.stop=\"navigateToJobList\">\n            <text class=\"view-more-text\">查看更多招聘信息</text>\n            <text class=\"view-more-icon iconfont icon-right\"></text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"interaction-toolbar\">\n      <view class=\"toolbar-item\" @click=\"goToHome\">\n        <image src=\"/static/images/tabbar/a首页.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">首页</text>\n      </view>\n      <view class=\"toolbar-item\" :class=\"{'active-toolbar-item': isFollowed}\" @click=\"toggleFollow\">\n        <image :src=\"isFollowed ? '/static/images/tabbar/已收藏选中.png' : '/static/images/tabbar/a关注.png'\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">{{isFollowed ? '已关注' : '关注'}}</text>\n      </view>\n      <button class=\"share-button toolbar-item\" open-type=\"share\">\n        <image src=\"/static/images/tabbar/a分享.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">分享</text>\n      </button>\n      <view class=\"toolbar-item\" @click=\"showCommentInput\">\n        <image src=\"/static/images/tabbar/a消息.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">评论</text>\n      </view>\n      <view class=\"toolbar-item call-button\" @click=\"callPhone\">\n        <view class=\"call-button-content\">\n          <text class=\"call-text\">打电话</text>\n          <text class=\"call-subtitle\">请说在磁州生活网看到的</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 红包弹窗 -->\n    <view class=\"red-packet-popup\" v-if=\"showRedPacket\">\n      <view class=\"red-packet-popup-wrapper\" @click.stop>\n        <view class=\"red-packet-task-modal\" v-if=\"!redPacketGrabbed\">\n          <view class=\"red-packet-modal-header\">\n            <text class=\"red-packet-modal-title\">完成任务领取红包</text>\n            <text class=\"red-packet-modal-close\" @click=\"closeRedPacket\">×</text>\n            </view>\n          <view class=\"red-packet-modal-amount\">¥{{jobData.redPacket.amount}}</view>\n          \n          <view class=\"red-packet-task-list\">\n            <view class=\"red-packet-task-item\" :class=\"{'task-completed': isShared}\" v-if=\"jobData.redPacket.conditions.includes('转发分享')\">\n              <view class=\"task-icon-container\" :class=\"{'completed-icon-container': isShared}\">\n                <image class=\"task-icon\" src=\"/static/images/tabbar/分享.png\"></image>\n            </view>\n              <view class=\"task-info\">\n                <view class=\"task-title\">转发分享</view>\n                <view class=\"task-desc\">分享给好友，{{shareViewCount}}/10人查看</view>\n          </view>\n              <button class=\"task-action-btn\" :class=\"{'completed-btn': isShared}\" open-type=\"share\">\n                {{isShared ? '已分享' : '去分享'}}\n              </button>\n          </view>\n          </view>\n          \n          <view class=\"red-packet-get-btn\" :class=\"{'red-packet-get-btn-active': canGrabRedPacket}\" @click=\"grabRedPacket\">\n            {{canGrabRedPacket ? '立即领取' : '完成任务领取'}}\n          </view>\n          \n          <view class=\"red-packet-tip\">\n            <text class=\"red-packet-tip-text\">*完成以上任务即可领取红包</text>\n          </view>\n        </view>\n        \n        <!-- 抢到红包后的弹窗 -->\n        <view class=\"red-packet-success-modal\" v-if=\"redPacketGrabbed\">\n          <image class=\"success-bg\" src=\"/static/images/tabbar/红包弹窗背景.png\" mode=\"aspectFill\"></image>\n          <view class=\"success-content\">\n            <view class=\"success-title\">恭喜您抢到红包</view>\n            <view class=\"success-amount\">¥{{grabbedAmount}}</view>\n            <view class=\"success-desc\">红包已存入您的钱包</view>\n            <view class=\"success-btn-group\">\n              <view class=\"success-btn success-btn-wallet\" @click=\"goToWallet\">查看我的钱包</view>\n              <view class=\"success-btn\" @click=\"closeRedPacket\">关闭</view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 评论输入区域 -->\n    <view class=\"comment-popup\" v-if=\"showCommentArea\">\n      <view class=\"comment-mask\" @click=\"showCommentArea = false\"></view>\n      <view class=\"comment-container\">\n        <view class=\"comment-header\">\n          <text class=\"comment-title\">发表评论</text>\n          <text class=\"comment-close\" @click=\"showCommentArea = false\">×</text>\n        </view>\n        <view class=\"comment-body\">\n          <textarea class=\"comment-textarea\" v-model=\"commentContent\" placeholder=\"说点什么吧...\" maxlength=\"200\" auto-focus auto-height></textarea>\n          <view class=\"comment-footer\">\n            <text class=\"comment-count\">{{commentContent.length}}/200</text>\n            <button class=\"comment-submit\" @click=\"submitComment\">发布</button>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted, reactive, computed } from 'vue'\n\n// 状态栏高度\nconst statusBarHeight = ref(20);\n\n// 获取状态栏高度\nonMounted(() => {\n  try {\n    const sysInfo = uni.getSystemInfoSync();\n    statusBarHeight.value = sysInfo.statusBarHeight || 20;\n  } catch (e) {\n    console.error('获取状态栏高度失败', e);\n  }\n});\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack({\n    fail: () => {\n      uni.switchTab({\n        url: '/pages/index/index'\n      });\n    }\n  });\n};\n\n// 格式化时间\nconst formatTime = (timestamp) => {\n  const date = new Date(timestamp);\n  return `${date.getMonth() + 1}月${date.getDate()}日`;\n};\n\n// 响应式数据\nconst isCollected = ref(false);\nconst isFollowed = ref(false);\nconst isShared = ref(false);\nconst isCommented = ref(false);\nconst showRedPacket = ref(false);\nconst redPacketGrabbed = ref(false);\nconst grabbedAmount = ref('0.00');\nconst showCommentArea = ref(false);\nconst commentContent = ref('');\nconst shareViewCount = ref(0);\n\n// 计算红包是否可以领取\nconst canGrabRedPacket = computed(() => {\n  // 如果没有条件，直接可以领取\n  if (!jobData.value.redPacket.conditions || jobData.value.redPacket.conditions.length === 0) {\n    return true;\n  }\n  \n  // 检查所有条件是否都满足\n  let allConditionsMet = true;\n  \n  // 检查转发分享条件\n  if (jobData.value.redPacket.conditions.includes('转发分享')) {\n    if (!isShared.value || shareViewCount.value < 10) {\n      allConditionsMet = false;\n    }\n  }\n  \n  // 检查关注店铺条件\n  if (jobData.value.redPacket.conditions.includes('关注店铺')) {\n    if (!isFollowed.value) {\n      allConditionsMet = false;\n    }\n  }\n  \n  return allConditionsMet;\n});\n\nconst jobData = ref({\n  id: 'job12345',\n  title: '前端开发工程师',\n  salary: '8000-12000元/月',\n  tags: ['五险一金', '双休', '包吃住'],\n  publishTime: Date.now() - 86400000 * 2, // 2天前\n  location: '磁县城区',\n  experience: '1-3年',\n  education: '大专及以上',\n  headcount: 3,\n  description: '负责公司产品的前端开发工作，包括但不限于PC端、移动端的页面开发和交互实现。与后端开发人员密切合作，确保前后端数据交互的顺畅和高效。参与产品需求分析和功能设计讨论，提供前端技术可行性建议。',\n  responsibilities: [\n    '负责公司Web前端页面的设计和开发',\n    '根据产品需求，分析并给出最优的页面前端结构解决方案',\n    '与后端工程师协作，完成数据交互、接口联调',\n    '对现有项目进行性能优化，提升用户体验',\n    '关注前端技术的发展，根据业务需求引入新技术'\n  ],\n  requirements: [\n    '计算机相关专业，大专及以上学历',\n    '1年以上前端开发经验，熟悉HTML5、CSS3、JavaScript',\n    '熟悉Vue、React等主流前端框架至少一种',\n    '了解响应式布局和移动端适配',\n    '有良好的代码风格，重视代码质量',\n    '具备良好的沟通能力和团队协作精神'\n  ],\n  benefits: [\n    '五险一金',\n    '双休',\n    '带薪年假',\n    '节日福利',\n    '定期团建',\n    '免费工作餐',\n    '晋升空间'\n  ],\n  address: '河北省邯郸市磁县北关镇磁州大厦5层',\n  contact: {\n    name: '张经理',\n    phone: '13912345678'\n  },\n  company: {\n    name: '磁县科技有限公司',\n    logo: '/static/images/company-logo.png',\n    type: '互联网',\n    size: '50-100人',\n    isVerified: true\n  },\n  // 红包相关数据\n  hasRedPacket: true,\n  redPacket: {\n    amount: '10.00',\n    remain: 8,\n    type: 'fixed', // fixed:固定金额 random:随机金额\n    total: 20,\n    validity: '2024-06-30',\n    description: '感谢您查看我们的招聘信息',\n    conditions: ['转发分享'] // 领取条件\n  }\n});\n\n// 获取红包条件文本\nconst getRedPacketConditionText = () => {\n  const conditions = jobData.value.redPacket.conditions || [];\n  if (!conditions || conditions.length === 0) {\n    return '投递简历再领一个';\n  }\n  \n  // 最多显示两个条件，其余用\"等\"表示\n  if (conditions.length === 1) {\n    return `${conditions[0]}可再领`;\n  } else if (conditions.length === 2) {\n    return `${conditions[0]}、${conditions[1]}可再领`;\n  } else {\n    return `${conditions[0]}、${conditions[1]}等可再领`;\n  }\n};\n\n// 方法\nconst toggleCollect = () => {\n  isCollected.value = !isCollected.value;\n  if (isCollected.value) {\n    uni.showToast({\n      title: '收藏成功',\n      icon: 'success'\n    });\n  }\n};\n\n// 打开红包弹窗\nconst openRedPacket = () => {\n  showRedPacket.value = true;\n};\n\n// 关闭红包弹窗\nconst closeRedPacket = () => {\n  showRedPacket.value = false;\n};\n\n// 抢红包\nconst grabRedPacket = () => {\n  // 检查条件是否满足\n  if (!checkRedPacketConditions()) {\n    return;\n  }\n  \n  // 模拟抢红包过程\n  uni.showLoading({\n    title: '正在拆红包...'\n  });\n  \n  setTimeout(() => {\n    uni.hideLoading();\n    redPacketGrabbed.value = true;\n    \n    // 根据红包类型生成金额\n    if (jobData.value.redPacket.type === 'random') {\n      // 随机金额红包，在红包金额的50%-150%之间随机\n      const baseAmount = parseFloat(jobData.value.redPacket.amount);\n      const min = baseAmount * 0.5;\n      const max = baseAmount * 1.5;\n      grabbedAmount.value = (min + Math.random() * (max - min)).toFixed(2);\n    } else {\n      // 固定金额红包\n      grabbedAmount.value = jobData.value.redPacket.amount;\n    }\n    \n    // 更新剩余数量\n    jobData.value.redPacket.remain -= 1;\n  }, 1000);\n};\n\n// 检查红包领取条件\nconst checkRedPacketConditions = () => {\n  const conditions = jobData.value.redPacket.conditions || [];\n  if (!conditions || conditions.length === 0) {\n    return true; // 没有条件限制\n  }\n\n  // 获取用户信息\n  const userInfo = uni.getStorageSync('userInfo');\n  if (!userInfo) {\n    uni.showModal({\n      title: '提示',\n      content: '请先登录后再领取红包',\n      confirmText: '去登录',\n      success: (res) => {\n        if (res.confirm) {\n          uni.navigateTo({\n            url: '/pages/login/login'\n          });\n        }\n      }\n    });\n    return false;\n  }\n\n  // 检查各项条件\n  let hasUnmetConditions = false;\n  let unmetConditionsText = '';\n\n  // 检查是否转发\n  if (conditions.includes('转发分享')) {\n    const hasShared = uni.getStorageSync(`shared_${jobData.value.id}`) || false;\n    const shareViewCount = uni.getStorageSync(`share_views_${jobData.value.id}`) || 0;\n    \n    if (!hasShared) {\n      hasUnmetConditions = true;\n      unmetConditionsText += '- 请先转发分享此招聘信息\\n';\n    } else if (shareViewCount < 10) {\n      hasUnmetConditions = true;\n      unmetConditionsText += `- 您的分享需要至少10人查看(当前${shareViewCount}人)\\n`;\n    }\n  }\n\n  // 如果有未满足的条件，显示提示\n  if (hasUnmetConditions) {\n    uni.showModal({\n      title: '无法领取红包',\n      content: `请先完成以下条件:\\n${unmetConditionsText}`,\n      showCancel: false\n    });\n    return false;\n  }\n\n  return true;\n};\n\n// 设置已分享标记(在分享成功回调中调用)\nconst markAsShared = () => {\n  uni.setStorageSync(`shared_${jobData.value.id}`, true);\n};\n\n// 增加分享查看次数(在其他用户通过分享链接查看时调用)\nconst increaseShareViewCount = () => {\n  const currentCount = uni.getStorageSync(`share_views_${jobData.value.id}`) || 0;\n  uni.setStorageSync(`share_views_${jobData.value.id}`, currentCount + 1);\n};\n\n// 设置已评论标记\nconst markAsCommented = () => {\n  uni.setStorageSync(`commented_${jobData.value.id}`, true);\n};\n\n// 设置已关注标记\nconst markAsFollowed = () => {\n  uni.setStorageSync(`followed_${jobData.value.company.id}`, true);\n};\n\n// 前往钱包页面\nconst goToWallet = () => {\n  uni.navigateTo({\n    url: '/subPackages/payment/pages/wallet'\n  });\n};\n\nconst callPhone = () => {\n  uni.makePhoneCall({\n    phoneNumber: jobData.value.contact.phone,\n    fail: () => {\n      uni.showToast({\n        title: '拨打电话失败',\n        icon: 'none'\n      });\n    }\n  });\n};\n\nconst openLocation = () => {\n  uni.showToast({\n    title: '查看地图位置',\n    icon: 'none'\n  });\n};\n\n// 跳转到首页\nconst goToHome = () => {\n  uni.switchTab({\n    url: '/pages/index/index'\n  });\n};\n\n// 打开私信聊天\nconst openChat = () => {\n  if (!jobData.value.company || !jobData.value.company.id) {\n    uni.showToast({\n      title: '无法获取招聘方信息',\n      icon: 'none'\n    });\n    return;\n  }\n  \n  // 跳转到聊天页面\n  uni.navigateTo({\n    url: `/pages/chat/index?userId=${jobData.value.company.id || 'temp_id'}&username=${encodeURIComponent(jobData.value.company.name || '招聘方')}`\n  });\n};\n\n// 海报相关数据\nconst posterImagePath = ref('');\nconst showPosterFlag = ref(false);\n\n// 生成海报的方法\nconst generateShareImage = () => {\n  uni.showLoading({\n    title: '正在生成海报...',\n    mask: true\n  });\n  \n  // 创建海报数据对象\n  const posterData = {\n    title: jobData.value.title,\n    salary: jobData.value.salary,\n    company: jobData.value.company.name,\n    address: jobData.value.address,\n    phone: jobData.value.contact.phone,\n    requirements: jobData.value.requirements ? jobData.value.requirements.substring(0, 60) + '...' : '',\n    qrcode: '/static/images/tabbar/客服微信.png',\n    logo: jobData.value.company.logo,\n    bgImage: '/static/images/banner/banner-1.png'\n  };\n  \n  // #ifdef H5\n  // H5环境不支持canvas绘制图片保存，提示用户\n  setTimeout(() => {\n    uni.hideLoading();\n    uni.showModal({\n      title: '提示',\n      content: 'H5环境暂不支持保存海报，请使用App或小程序',\n      showCancel: false\n    });\n  }, 1000);\n  return;\n  // #endif\n  \n  // 绘制海报\n  const ctx = uni.createCanvasContext('posterCanvas');\n  \n  // 绘制背景\n  ctx.save();\n  ctx.drawImage(posterData.bgImage, 0, 0, 600, 400);\n  // 添加半透明蒙层\n  ctx.setFillStyle('rgba(0, 0, 0, 0.35)');\n  ctx.fillRect(0, 0, 600, 900);\n  ctx.restore();\n  \n  // 绘制白色卡片背景\n  ctx.save();\n  ctx.setFillStyle('#ffffff');\n  ctx.fillRect(30, 280, 540, 550);\n  ctx.restore();\n  \n  // 绘制Logo\n  ctx.save();\n  ctx.beginPath();\n  ctx.arc(300, 200, 80, 0, 2 * Math.PI);\n  ctx.setFillStyle('#ffffff');\n  ctx.fill();\n  // 在圆形内绘制Logo\n  ctx.clip();\n  ctx.drawImage(posterData.logo, 220, 120, 160, 160);\n  ctx.restore();\n  \n  // 绘制岗位名称\n  ctx.setFillStyle('#333333');\n  ctx.setFontSize(32);\n  ctx.setTextAlign('center');\n  ctx.fillText(posterData.title, 300, 350);\n  \n  // 绘制薪资\n  ctx.setFillStyle('#FF6B6B');\n  ctx.setFontSize(28);\n  ctx.fillText(posterData.salary, 300, 400);\n  \n  // 分割线\n  ctx.beginPath();\n  ctx.setStrokeStyle('#eeeeee');\n  ctx.setLineWidth(2);\n  ctx.moveTo(100, 430);\n  ctx.lineTo(500, 430);\n  ctx.stroke();\n  \n  // 绘制公司名称\n  ctx.setFillStyle('#666666');\n  ctx.setFontSize(24);\n  ctx.setTextAlign('left');\n  ctx.fillText('公司: ' + posterData.company, 80, 480);\n  \n  // 绘制工作地点\n  ctx.fillText('地址: ' + posterData.address, 80, 520);\n  \n  // A wrap text function\n  const wrapText = (ctx, text, x, y, maxWidth, lineHeight) => {\n    if (text.length === 0) return;\n    \n    const words = text.split('');\n    let line = '';\n    let testLine = '';\n    let lineCount = 0;\n    \n    for (let n = 0; n < words.length; n++) {\n      testLine += words[n];\n      const metrics = ctx.measureText(testLine);\n      const testWidth = metrics.width;\n      \n      if (testWidth > maxWidth && n > 0) {\n        ctx.fillText(line, x, y + (lineCount * lineHeight));\n        line = words[n];\n        testLine = words[n];\n        lineCount++;\n        \n        if (lineCount >= 3) {\n          line += '...';\n          ctx.fillText(line, x, y + (lineCount * lineHeight));\n          break;\n        }\n      } else {\n        line = testLine;\n      }\n    }\n    \n    if (lineCount < 3) {\n      ctx.fillText(line, x, y + (lineCount * lineHeight));\n    }\n  };\n  \n  // 绘制岗位要求\n  ctx.setFillStyle('#666666');\n  ctx.fillText('岗位要求:', 80, 560);\n  wrapText(ctx, posterData.requirements, 80, 600, 440, 35);\n  \n  // 绘制电话\n  if (posterData.phone) {\n    ctx.fillText('联系电话: ' + posterData.phone, 80, 680);\n  }\n  \n  // 绘制小程序码\n  ctx.drawImage(posterData.qrcode, 225, 720, 150, 150);\n  \n  // 提示文字\n  ctx.setFillStyle('#999999');\n  ctx.setFontSize(20);\n  ctx.setTextAlign('center');\n  ctx.fillText('长按识别二维码查看详情', 300, 880);\n  \n  // 应用平台Logo\n  ctx.setFillStyle('#333333');\n  ctx.setFontSize(24);\n  ctx.fillText('磁县同城 - 招聘信息', 300, 840);\n  \n  // 绘制完成，输出图片\n  ctx.draw(false, () => {\n    setTimeout(() => {\n      // 延迟确保canvas已完成渲染\n      uni.canvasToTempFilePath({\n        canvasId: 'posterCanvas',\n        success: (res) => {\n          uni.hideLoading();\n          showPosterModal(res.tempFilePath);\n        },\n        fail: (err) => {\n          console.error('生成海报失败', err);\n          uni.hideLoading();\n          uni.showToast({\n            title: '生成海报失败',\n            icon: 'none'\n          });\n        }\n      });\n    }, 800);\n  });\n};\n\n// 显示海报预览和保存选项\nconst showPosterModal = (posterPath) => {\n  posterImagePath.value = posterPath;\n  showPosterFlag.value = true;\n  \n  uni.showModal({\n    title: '海报已生成',\n    content: '海报已生成，是否保存到相册？',\n    confirmText: '保存',\n    success: (res) => {\n      if (res.confirm) {\n        savePosterToAlbum(posterPath);\n      } else {\n        // 预览图片\n        uni.previewImage({\n          urls: [posterPath],\n          current: posterPath\n        });\n      }\n    }\n  });\n};\n\n// 保存海报到相册\nconst savePosterToAlbum = (posterPath) => {\n  uni.showLoading({\n    title: '正在保存...'\n  });\n  \n  uni.saveImageToPhotosAlbum({\n    filePath: posterPath,\n    success: () => {\n      uni.hideLoading();\n      uni.showToast({\n        title: '已保存到相册',\n        icon: 'success'\n      });\n    },\n    fail: (err) => {\n      uni.hideLoading();\n      console.error('保存失败', err);\n      \n      if (err.errMsg.indexOf('auth deny') > -1) {\n        uni.showModal({\n          title: '提示',\n          content: '保存失败，请授权相册权限后重试',\n          confirmText: '去设置',\n          success: (res) => {\n            if (res.confirm) {\n              uni.openSetting();\n            }\n          }\n        });\n      } else {\n        uni.showToast({\n          title: '保存失败',\n          icon: 'none'\n        });\n      }\n    }\n  });\n};\n\n// 相关职位列表功能\nconst relatedJobs = ref([]);\n\n// 加载相关招聘信息\nconst loadRelatedJobs = () => {\n  // 这里可以调用API获取数据\n  // 实际项目中应该根据当前招聘信息的分类、标签等进行相关性匹配\n  \n  // 模拟数据\n  setTimeout(() => {\n    relatedJobs.value = [\n      {\n        id: 'job001',\n        title: '销售经理',\n        salary: '6000-8000',\n        companyName: '磁州科技有限公司',\n        companyLogo: '/static/images/tabbar/公司.png',\n        tags: ['五险一金', '包吃住', '加班补助']\n      },\n      {\n        id: 'job002',\n        title: '前台文员',\n        salary: '3500-4500',\n        companyName: '磁州商贸有限公司',\n        companyLogo: '/static/images/tabbar/企业.png',\n        tags: ['五险', '双休', '有食堂']\n      },\n      {\n        id: 'job003',\n        title: '网络销售',\n        salary: '4000-8000',\n        companyName: '磁州网络科技公司',\n        companyLogo: '/static/images/tabbar/网络.png',\n        tags: ['高提成', '弹性工作', '有培训']\n      }\n    ];\n  }, 500);\n};\n\n// 跳转到详情页\nconst navigateToJobDetail = (id) => {\n  // 避免重复跳转当前页面\n  const pages = getCurrentPages();\n  const currentPage = pages[pages.length - 1];\n  const options = currentPage.options || {};\n  \n  if (id === options.id) {\n    return;\n  }\n  \n  uni.navigateTo({\n    url: `/pages/publish/job-detail?id=${id}`\n  });\n};\n\n// 跳转到招聘列表页\nconst navigateToJobList = (e) => {\n  if (e) e.stopPropagation();\n  const jobCategory = jobData.value.tags?.[0] || '';\n  uni.navigateTo({\n    url: `/subPackages/service/pages/filter?type=job&title=${encodeURIComponent('招聘信息')}&category=${encodeURIComponent(jobCategory)}&active=job`\n  });\n};\n\n// 生命周期钩子\nonMounted(() => {\n  // 修改页面标题\n  uni.setNavigationBarTitle({\n    title: '招聘详情'\n  });\n  \n  // 获取路由参数\n  const pages = getCurrentPages();\n  const currentPage = pages[pages.length - 1];\n  const options = currentPage.options || {};\n  \n  // 如果有ID参数，则根据ID获取详情\n  if (options.id) {\n    console.log('正在获取招聘详情，ID:', options.id);\n    // 实际开发中这里应该调用API获取详情数据\n    // fetchJobDetail(options.id).then(data => {\n    //   jobData.value = data;\n    // });\n    \n    // 获取各种状态\n    isFollowed.value = uni.getStorageSync(`followed_${jobData.value.company.id}`) || false;\n    isShared.value = uni.getStorageSync(`shared_${jobData.value.id}`) || false;\n    isCommented.value = uni.getStorageSync(`commented_${jobData.value.id}`) || false;\n    \n    // 获取分享查看次数\n    shareViewCount.value = uni.getStorageSync(`share_views_${jobData.value.id}`) || 0;\n    \n    // 如果是通过分享链接访问的，增加分享查看次数\n    if (options.source === 'share') {\n      increaseShareViewCount();\n      shareViewCount.value += 1;\n    }\n  }\n  \n  // 加载相关职位数据\n  loadRelatedJobs();\n});\n\n// 切换关注状态\nconst toggleFollow = () => {\n  if (!checkLoginStatus()) {\n    return;\n  }\n  \n  isFollowed.value = !isFollowed.value;\n  \n  if (isFollowed.value) {\n    // 关注成功\n    markAsFollowed();\n    uni.showToast({\n      title: '关注成功',\n      icon: 'success'\n    });\n  } else {\n    // 取消关注\n    uni.removeStorageSync(`followed_${jobData.value.company.id}`);\n    uni.showToast({\n      title: '已取消关注',\n      icon: 'none'\n    });\n  }\n};\n\n// 显示评论输入框\nconst showCommentInput = () => {\n  if (!checkLoginStatus()) {\n    return;\n  }\n  \n  showCommentArea.value = true;\n};\n\n// 提交评论\nconst submitComment = () => {\n  if (commentContent.value.trim() === '') {\n    uni.showToast({\n      title: '评论内容不能为空',\n      icon: 'none'\n    });\n    return;\n  }\n  \n  uni.showLoading({\n    title: '提交中...'\n  });\n  \n  // 模拟提交评论\n  setTimeout(() => {\n    uni.hideLoading();\n    \n    // 标记为已评论\n    markAsCommented();\n    \n    uni.showToast({\n      title: '评论成功',\n      icon: 'success'\n    });\n    \n    // 清空评论内容并隐藏评论区\n    commentContent.value = '';\n    showCommentArea.value = false;\n  }, 800);\n};\n\n// 检查登录状态\nconst checkLoginStatus = () => {\n  const userInfo = uni.getStorageSync('userInfo');\n  if (!userInfo) {\n    uni.showModal({\n      title: '提示',\n      content: '请先登录后再操作',\n      confirmText: '去登录',\n      success: (res) => {\n        if (res.confirm) {\n          uni.navigateTo({\n            url: '/pages/login/login'\n          });\n        }\n      }\n    });\n    return false;\n  }\n  return true;\n};\n\n// 分享设置\nconst onShareAppMessage = (res) => {\n  markAsShared(); // 标记为已分享\n  \n  return {\n    title: jobData.value.title + ' - ' + jobData.value.salary,\n    path: `/pages/publish/job-detail?id=${jobData.value.id}&source=share`,\n    imageUrl: '/static/images/share-job-poster.png', // 分享图片\n    success: function() {\n      uni.showToast({\n        title: '分享成功',\n        icon: 'success'\n      });\n    }\n  };\n};\n</script>\n\n<style>\n@import './common-detail-style.css';\n\n/* 招聘详情页的特殊样式 */\n.job-detail-container {\n  /* 可以添加特定于招聘详情页的样式 */\n}\n\n/* 岗位职责和任职要求列表样式 */\n.job-responsibility,\n.job-requirement {\n  padding: 16rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n}\n\n.list-item {\n  display: flex;\n  margin-bottom: 16rpx;\n}\n\n.list-item:last-child {\n  margin-bottom: 0;\n}\n\n.list-dot {\n  font-size: 28rpx;\n  color: #1890ff;\n  margin-right: 12rpx;\n  line-height: 1.6;\n}\n\n.list-text {\n  font-size: 28rpx;\n  color: #555;\n  line-height: 1.6;\n  flex: 1;\n}\n\n/* 福利标签样式 */\n.job-benefits {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 16rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n}\n\n.benefit-tag {\n  display: flex;\n  align-items: center;\n  background-color: #f6ffed;\n  border: 1rpx solid #b7eb8f;\n  border-radius: 6rpx;\n  padding: 8rpx 16rpx;\n  margin-right: 16rpx;\n  margin-bottom: 16rpx;\n}\n\n.benefit-icon {\n  font-size: 24rpx;\n  color: #52c41a;\n  margin-right: 8rpx;\n}\n\n.benefit-text {\n  font-size: 24rpx;\n  color: #52c41a;\n}\n\n/* 工作地址地图样式 */\n.location-content {\n  display: flex;\n  align-items: center;\n  padding: 20rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n  margin-bottom: 24rpx;\n}\n\n.location-text {\n  flex: 1;\n  font-size: 28rpx;\n  color: #555;\n  margin: 0 12rpx;\n}\n\n.location-arrow {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.location-map {\n  height: 240rpx;\n  border-radius: 12rpx;\n  overflow: hidden;\n}\n\n.map-preview {\n  width: 100%;\n  height: 100%;\n}\n\n/* 隐藏的分享按钮 */\n.hidden-share-btn {\n  position: fixed;\n  width: 2rpx;\n  height: 2rpx;\n  opacity: 0;\n  top: -999rpx;\n  left: -999rpx;\n  z-index: -1;\n  overflow: hidden;\n  padding: 0;\n  margin: 0;\n  border: none;\n}\n\n.hidden-share-btn::after {\n  display: none;\n}\n\n/* 悬浮海报按钮 */\n.float-poster-btn {\n  position: fixed;\n  right: 30rpx;\n  bottom: 200rpx;\n  width: 100rpx;\n  height: 100rpx;\n  background: rgba(240, 240, 240, 0.9);\n  border-radius: 50%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.08);\n  z-index: 90;\n  backdrop-filter: blur(10px);\n  -webkit-backdrop-filter: blur(10px);\n  border: 1rpx solid rgba(230, 230, 230, 0.6);\n  transition: all 0.2s ease;\n}\n\n.float-poster-btn:active {\n  transform: scale(0.95);\n  box-shadow: 0 3rpx 10rpx rgba(0,0,0,0.08);\n}\n\n.poster-icon {\n  width: 40rpx;\n  height: 40rpx;\n  margin-bottom: 4rpx;\n}\n\n.poster-text {\n  font-size: 20rpx;\n  color: #444;\n  line-height: 1;\n}\n\n.job-detail-wrapper {\n  padding: 24rpx;\n  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */\n}\n\n/* 相关招聘信息卡片样式 */\n.related-jobs-card {\n  margin-top: 12px;\n  background-color: #fff;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\n}\n\n/* 可折叠标题栏样式 */\n.collapsible-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px;\n  background-color: #fff;\n  position: relative;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n/* 内容区样式 */\n.collapsible-content {\n  padding: 0 16px 16px;\n  overflow: hidden;\n}\n\n/* 相关职位列表样式 */\n.related-jobs-list {\n  margin-bottom: 12px;\n}\n\n.related-job-item {\n  padding: 12px 0;\n  border-bottom: 1px solid #f5f5f5;\n}\n\n.related-job-item:last-child {\n  border-bottom: none;\n}\n\n.job-item-content {\n  display: flex;\n  align-items: center;\n}\n\n.job-item-left {\n  margin-right: 12px;\n}\n\n.company-logo {\n  width: 40px;\n  height: 40px;\n  border-radius: 8px;\n  background-color: #f5f7fa;\n}\n\n.job-item-middle {\n  flex: 1;\n  overflow: hidden;\n}\n\n.job-item-title {\n  font-size: 15px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 4px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.job-item-company {\n  font-size: 13px;\n  color: #666;\n  margin-bottom: 6px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.job-item-tags {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.job-item-tag {\n  font-size: 11px;\n  color: #0066ff;\n  background-color: rgba(0, 102, 255, 0.1);\n  padding: 2px 6px;\n  border-radius: 4px;\n  margin-right: 6px;\n}\n\n.job-item-tag-more {\n  font-size: 11px;\n  color: #999;\n}\n\n.job-item-right {\n  min-width: 80px;\n  text-align: right;\n}\n\n.job-item-salary {\n  font-size: 15px;\n  font-weight: 500;\n  color: #ff5252;\n}\n\n/* 查看更多按钮样式 */\n.view-more-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 44px;\n  background-color: #f7f9fc;\n  border-radius: 8px;\n  margin-top: 8px;\n}\n\n.view-more-text {\n  font-size: 14px;\n  color: #0066ff;\n}\n\n.view-more-icon {\n  margin-left: 4px;\n  font-size: 12px;\n  color: #0066ff;\n}\n\n/* 空数据提示样式 */\n.empty-related-jobs {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 24px 0;\n}\n\n.empty-image {\n  width: 80px;\n  height: 80px;\n  margin-bottom: 12px;\n}\n\n.empty-text {\n  font-size: 14px;\n  color: #999;\n}\n\n/* 红包区域样式 */\n.red-packet-card {\n  background-color: #fff;\n  margin: 20rpx 20rpx 30rpx;\n  border-radius: 16rpx;\n  overflow: hidden;\n  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.08);\n}\n\n.red-packet-section {\n  padding: 16rpx;\n}\n\n.red-packet-container {\n  position: relative;\n  height: 160rpx;\n  border-radius: 16rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 16rpx rgba(255, 90, 95, 0.15);\n}\n\n.red-packet-blur-bg {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n  z-index: 1;\n  background: linear-gradient(to left, #FF5A5F, #FF8A8E);\n  backdrop-filter: blur(10px);\n  -webkit-backdrop-filter: blur(10px);\n}\n\n.red-packet-content {\n  position: relative;\n  z-index: 2;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 100%;\n  padding: 0 30rpx;\n}\n\n.red-packet-left {\n  flex: 1;\n  display: flex;\n  align-items: center;\n}\n\n.red-packet-icon {\n  width: 80rpx;\n  height: 80rpx;\n  margin-right: 20rpx;\n  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));\n}\n\n.red-packet-info {\n  flex: 1;\n}\n\n.red-packet-title {\n  font-size: 32rpx;\n  color: #FFFFFF;\n  font-weight: bold;\n  margin-bottom: 8rpx;\n  text-shadow: none;\n}\n\n.red-packet-desc {\n  font-size: 20rpx;\n  color: #FFFFFF;\n  font-weight: bold;\n  text-shadow: none;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.red-packet-right {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.red-packet-amount {\n  font-size: 36rpx;\n  color: #FFFFFF;\n  font-weight: bold;\n  margin-bottom: 10rpx;\n  text-shadow: none;\n}\n\n.red-packet-amount .prefix {\n  font-size: 28rpx;\n  margin-right: 4rpx;\n}\n\n.grab-btn {\n  background-color: #FFFFFF;\n  color: #F05A5F;\n  font-size: 24rpx;\n  padding: 12rpx 32rpx;\n  border-radius: 30rpx;\n  font-weight: bold;\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);\n}\n\n.red-packet-validity, \n.red-packet-description {\n  margin-top: 12rpx;\n  padding: 0 16rpx;\n}\n\n.validity-text, \n.description-text {\n  font-size: 24rpx;\n  color: #666;\n  line-height: 1.5;\n}\n\n.red-packet-description {\n  margin-bottom: 10rpx;\n}\n\n/* 红包弹窗 */\n.red-packet-popup {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  backdrop-filter: blur(5px);\n  -webkit-backdrop-filter: blur(5px);\n}\n\n.red-packet-popup-wrapper {\n  position: relative;\n  width: 600rpx;\n  background-color: #FFFFFF;\n  border-radius: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);\n}\n\n.red-packet-task-modal {\n  position: relative;\n  width: 100%;\n  padding: 40rpx 0;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  box-sizing: border-box;\n}\n\n.red-packet-modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: calc(100% - 60rpx);\n  padding: 0 0;\n  margin-bottom: 30rpx;\n  box-sizing: border-box;\n}\n\n.red-packet-modal-title {\n  font-size: 36rpx;\n  color: #333;\n  font-weight: 600;\n  flex-grow: 1;\n  text-align: center;\n  padding-right: 60rpx; /* 为关闭按钮留出空间 */\n}\n\n.red-packet-modal-close {\n  font-size: 40rpx;\n  color: #999;\n  cursor: pointer;\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n}\n\n.red-packet-modal-amount {\n  font-size: 72rpx;\n  color: #FF5A5F;\n  font-weight: bold;\n  text-align: center;\n  margin-bottom: 40rpx;\n  width: 100%; /* 确保金额文本占据全部宽度以便自身居中 */\n}\n\n.red-packet-task-list {\n  background-color: #F8F8F8;\n  border-radius: 16rpx;\n  padding: 20rpx;\n  margin-bottom: 30rpx;\n  width: calc(100% - 60rpx); /* 减去弹窗左右内边距 */\n  box-sizing: border-box;\n}\n\n.red-packet-task-item {\n  display: flex;\n  align-items: center;\n  padding: 20rpx;\n  background-color: #FFFFFF;\n  border-radius: 12rpx;\n  margin-bottom: 16rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n  box-sizing: border-box;\n}\n\n.red-packet-task-item:last-child {\n  margin-bottom: 0;\n}\n\n.task-completed {\n  background-color: #F6FFED;\n  border: 1rpx solid #B7EB8F;\n}\n\n.task-icon-container {\n  width: 48rpx;\n  height: 48rpx;\n  background-color: #FFECEC;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 20rpx;\n  flex-shrink: 0;\n}\n\n.completed-icon-container {\n  background-color: #F6FFED;\n}\n\n.task-icon {\n  width: 28rpx;\n  height: 28rpx;\n}\n\n.task-info {\n  flex: 1;\n}\n\n.task-title {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n  margin-bottom: 4rpx;\n}\n\n.task-desc {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.task-action-btn {\n  background-color: #FF5A5F;\n  color: #FFFFFF;\n  font-size: 24rpx;\n  padding: 12rpx 30rpx;\n  border-radius: 30rpx;\n  font-weight: 600;\n  margin-left: 20rpx;\n  border: none;\n  outline: none;\n  flex-shrink: 0;\n  width: 140rpx; /* 设置固定宽度 */\n  text-align: center;\n}\n\n.completed-btn {\n  background-color: #52C41A;\n}\n\n.red-packet-get-btn {\n  background-color: rgba(255, 90, 95, 0.3);\n  color: #FFFFFF;\n  font-size: 32rpx;\n  font-weight: 600;\n  padding: 24rpx 0;\n  border-radius: 16rpx;\n  text-align: center;\n  margin-bottom: 20rpx;\n  transition: all 0.3s;\n  width: calc(100% - 60rpx); /* 减去弹窗左右内边距 */\n}\n\n.red-packet-get-btn-active {\n  background-color: #FF5A5F;\n  box-shadow: 0 4rpx 12rpx rgba(255, 90, 95, 0.3);;\n}\n\n.red-packet-tip {\n  text-align: center;\n  width: 100%;\n}\n\n.red-packet-tip-text {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.red-packet-success-modal {\n  position: relative;\n  overflow: hidden;\n  padding: 40rpx;\n  text-align: center;\n}\n\n.success-bg {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n  z-index: -1;\n}\n\n.success-content {\n  position: relative;\n  z-index: 1;\n  padding: 30rpx 0;\n}\n\n.success-title {\n  font-size: 40rpx;\n  color: #FFD700;\n  font-weight: bold;\n  margin-bottom: 30rpx;\n  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);\n}\n\n.success-amount {\n  font-size: 80rpx;\n  color: #FFD700;\n  font-weight: bold;\n  margin-bottom: 30rpx;\n  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);\n}\n\n.success-desc {\n  font-size: 28rpx;\n  color: #FFFFFF;\n  margin-bottom: 60rpx;\n  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);\n}\n\n.success-btn-group {\n  display: flex;\n  justify-content: center;\n  gap: 30rpx;\n}\n\n.success-btn {\n  background-color: rgba(255, 255, 255, 0.2);\n  color: #FFFFFF;\n  font-size: 28rpx;\n  padding: 16rpx 40rpx;\n  border-radius: 40rpx;\n  backdrop-filter: blur(5px);\n  -webkit-backdrop-filter: blur(5px);\n}\n\n.success-btn-wallet {\n  background-color: #FFD700;\n  color: #333;\n}\n\n/* 评论输入区域样式 */\n.comment-popup {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n\n.comment-mask {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.7);\n}\n\n.comment-container {\n  position: relative;\n  width: 80%;\n  max-width: 600px;\n  background-color: #fff;\n  border-radius: 12px;\n  padding: 20px;\n}\n\n.comment-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.comment-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n}\n\n.comment-close {\n  font-size: 20px;\n  color: #999;\n  cursor: pointer;\n}\n\n.comment-body {\n  margin-bottom: 20px;\n}\n\n.comment-textarea {\n  width: 100%;\n  padding: 10px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  margin-bottom: 10px;\n}\n\n.comment-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.comment-count {\n  font-size: 14px;\n  color: #999;\n}\n\n.comment-submit {\n  background-color: #0066ff;\n  color: #fff;\n  padding: 10px 20px;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n/* 活跃工具栏项样式 */\n.active-toolbar-item {\n  color: #0066ff;\n}\n\n.active-toolbar-item .toolbar-text {\n  color: #0066ff;\n}\n\n/* 自定义导航栏样式 */\n.custom-navbar {\n  background: linear-gradient(135deg, #0066FF, #0052CC);\n  height: 88rpx;\n  padding-top: 44px; /* 状态栏高度 */\n  display: flex;\n  align-items: center;\n  position: fixed; /* 改为固定定位 */\n  top: 0;\n  left: 0;\n  right: 0;\n  padding-bottom: 10rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n  z-index: 100; /* 提高z-index确保在最上层 */\n}\n\n.navbar-left {\n  width: 60px;\n  display: flex;\n  align-items: center;\n}\n\n.back-icon {\n  width: 20px;\n  height: 20px;\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 17px;\n  font-weight: 500;\n  color: #fff;\n}\n\n.navbar-right {\n  width: 60px;\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/publish/job-detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni", "computed", "shareViewCount", "ctx", "MiniProgramPage"], "mappings": ";;;;;;AA2UA,UAAA,kBAAAA,cAAAA,IAAA,EAAA;AAGAC,kBAAAA,UAAA,MAAA;AACA,UAAA;AACA,cAAA,UAAAC,oBAAA;AACA,wBAAA,QAAA,QAAA,mBAAA;AAAA,MACA,SAAA,GAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,uCAAA,aAAA,CAAA;AAAA,MACA;AAAA,IACA,CAAA;AAGA,UAAA,SAAA,MAAA;AACAA,oBAAAA,MAAA,aAAA;AAAA,QACA,MAAA,MAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,KAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,aAAA,CAAA,cAAA;AACA,YAAA,OAAA,IAAA,KAAA,SAAA;AACA,aAAA,GAAA,KAAA,aAAA,CAAA,IAAA,KAAA,SAAA;AAAA,IACA;AAGAF,kBAAA,IAAA,KAAA;AACA,UAAA,aAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,WAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,cAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,gBAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,mBAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,gBAAAA,cAAAA,IAAA,MAAA;AACA,UAAA,kBAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,iBAAAA,cAAAA,IAAA,EAAA;AACA,UAAA,iBAAAA,cAAAA,IAAA,CAAA;AAGA,UAAA,mBAAAG,cAAA,SAAA,MAAA;AAEA,UAAA,CAAA,QAAA,MAAA,UAAA,cAAA,QAAA,MAAA,UAAA,WAAA,WAAA,GAAA;AACA,eAAA;AAAA,MACA;AAGA,UAAA,mBAAA;AAGA,UAAA,QAAA,MAAA,UAAA,WAAA,SAAA,MAAA,GAAA;AACA,YAAA,CAAA,SAAA,SAAA,eAAA,QAAA,IAAA;AACA,6BAAA;AAAA,QACA;AAAA,MACA;AAGA,UAAA,QAAA,MAAA,UAAA,WAAA,SAAA,MAAA,GAAA;AACA,YAAA,CAAA,WAAA,OAAA;AACA,6BAAA;AAAA,QACA;AAAA,MACA;AAEA,aAAA;AAAA,IACA,CAAA;AAEA,UAAA,UAAAH,cAAAA,IAAA;AAAA,MACA,IAAA;AAAA,MACA,OAAA;AAAA,MACA,QAAA;AAAA,MACA,MAAA,CAAA,QAAA,MAAA,KAAA;AAAA,MACA,aAAA,KAAA,IAAA,IAAA,QAAA;AAAA;AAAA,MACA,UAAA;AAAA,MACA,YAAA;AAAA,MACA,WAAA;AAAA,MACA,WAAA;AAAA,MACA,aAAA;AAAA,MACA,kBAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACA;AAAA,MACA,cAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACA;AAAA,MACA,UAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACA;AAAA,MACA,SAAA;AAAA,MACA,SAAA;AAAA,QACA,MAAA;AAAA,QACA,OAAA;AAAA,MACA;AAAA,MACA,SAAA;AAAA,QACA,MAAA;AAAA,QACA,MAAA;AAAA,QACA,MAAA;AAAA,QACA,MAAA;AAAA,QACA,YAAA;AAAA,MACA;AAAA;AAAA,MAEA,cAAA;AAAA,MACA,WAAA;AAAA,QACA,QAAA;AAAA,QACA,QAAA;AAAA,QACA,MAAA;AAAA;AAAA,QACA,OAAA;AAAA,QACA,UAAA;AAAA,QACA,aAAA;AAAA,QACA,YAAA,CAAA,MAAA;AAAA;AAAA,MACA;AAAA,IACA,CAAA;AAGA,UAAA,4BAAA,MAAA;AACA,YAAA,aAAA,QAAA,MAAA,UAAA,cAAA,CAAA;AACA,UAAA,CAAA,cAAA,WAAA,WAAA,GAAA;AACA,eAAA;AAAA,MACA;AAGA,UAAA,WAAA,WAAA,GAAA;AACA,eAAA,GAAA,WAAA,CAAA,CAAA;AAAA,MACA,WAAA,WAAA,WAAA,GAAA;AACA,eAAA,GAAA,WAAA,CAAA,CAAA,IAAA,WAAA,CAAA,CAAA;AAAA,MACA,OAAA;AACA,eAAA,GAAA,WAAA,CAAA,CAAA,IAAA,WAAA,CAAA,CAAA;AAAA,MACA;AAAA,IACA;AAcA,UAAA,gBAAA,MAAA;AACA,oBAAA,QAAA;AAAA,IACA;AAGA,UAAA,iBAAA,MAAA;AACA,oBAAA,QAAA;AAAA,IACA;AAGA,UAAA,gBAAA,MAAA;AAEA,UAAA,CAAA,yBAAA,GAAA;AACA;AAAA,MACA;AAGAE,oBAAAA,MAAA,YAAA;AAAA,QACA,OAAA;AAAA,MACA,CAAA;AAEA,iBAAA,MAAA;AACAA,sBAAA,MAAA,YAAA;AACA,yBAAA,QAAA;AAGA,YAAA,QAAA,MAAA,UAAA,SAAA,UAAA;AAEA,gBAAA,aAAA,WAAA,QAAA,MAAA,UAAA,MAAA;AACA,gBAAA,MAAA,aAAA;AACA,gBAAA,MAAA,aAAA;AACA,wBAAA,SAAA,MAAA,KAAA,YAAA,MAAA,MAAA,QAAA,CAAA;AAAA,QACA,OAAA;AAEA,wBAAA,QAAA,QAAA,MAAA,UAAA;AAAA,QACA;AAGA,gBAAA,MAAA,UAAA,UAAA;AAAA,MACA,GAAA,GAAA;AAAA,IACA;AAGA,UAAA,2BAAA,MAAA;AACA,YAAA,aAAA,QAAA,MAAA,UAAA,cAAA,CAAA;AACA,UAAA,CAAA,cAAA,WAAA,WAAA,GAAA;AACA,eAAA;AAAA,MACA;AAGA,YAAA,WAAAA,cAAAA,MAAA,eAAA,UAAA;AACA,UAAA,CAAA,UAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,SAAA;AAAA,UACA,aAAA;AAAA,UACA,SAAA,CAAA,QAAA;AACA,gBAAA,IAAA,SAAA;AACAA,4BAAAA,MAAA,WAAA;AAAA,gBACA,KAAA;AAAA,cACA,CAAA;AAAA,YACA;AAAA,UACA;AAAA,QACA,CAAA;AACA,eAAA;AAAA,MACA;AAGA,UAAA,qBAAA;AACA,UAAA,sBAAA;AAGA,UAAA,WAAA,SAAA,MAAA,GAAA;AACA,cAAA,YAAAA,cAAAA,MAAA,eAAA,UAAA,QAAA,MAAA,EAAA,EAAA,KAAA;AACA,cAAAE,kBAAAF,cAAAA,MAAA,eAAA,eAAA,QAAA,MAAA,EAAA,EAAA,KAAA;AAEA,YAAA,CAAA,WAAA;AACA,+BAAA;AACA,iCAAA;AAAA,QACA,WAAAE,kBAAA,IAAA;AACA,+BAAA;AACA,iCAAA,qBAAAA,eAAA;AAAA;AAAA,QACA;AAAA,MACA;AAGA,UAAA,oBAAA;AACAF,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,SAAA;AAAA,EAAA,mBAAA;AAAA,UACA,YAAA;AAAA,QACA,CAAA;AACA,eAAA;AAAA,MACA;AAEA,aAAA;AAAA,IACA;AAQA,UAAA,yBAAA,MAAA;AACA,YAAA,eAAAA,cAAAA,MAAA,eAAA,eAAA,QAAA,MAAA,EAAA,EAAA,KAAA;AACAA,0BAAA,eAAA,eAAA,QAAA,MAAA,EAAA,IAAA,eAAA,CAAA;AAAA,IACA;AAGA,UAAA,kBAAA,MAAA;AACAA,0BAAA,eAAA,aAAA,QAAA,MAAA,EAAA,IAAA,IAAA;AAAA,IACA;AAGA,UAAA,iBAAA,MAAA;AACAA,0BAAA,eAAA,YAAA,QAAA,MAAA,QAAA,EAAA,IAAA,IAAA;AAAA,IACA;AAGA,UAAA,aAAA,MAAA;AACAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA;AAAA,MACA,CAAA;AAAA,IACA;AAEA,UAAA,YAAA,MAAA;AACAA,oBAAAA,MAAA,cAAA;AAAA,QACA,aAAA,QAAA,MAAA,QAAA;AAAA,QACA,MAAA,MAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAEA,UAAA,eAAA,MAAA;AACAA,oBAAAA,MAAA,UAAA;AAAA,QACA,OAAA;AAAA,QACA,MAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,WAAA,MAAA;AACAA,oBAAAA,MAAA,UAAA;AAAA,QACA,KAAA;AAAA,MACA,CAAA;AAAA,IACA;AAmBA,UAAA,kBAAAF,cAAAA,IAAA,EAAA;AACA,UAAA,iBAAAA,cAAAA,IAAA,KAAA;AAGA,UAAA,qBAAA,MAAA;AACAE,oBAAAA,MAAA,YAAA;AAAA,QACA,OAAA;AAAA,QACA,MAAA;AAAA,MACA,CAAA;AAGA,YAAA,aAAA;AAAA,QACA,OAAA,QAAA,MAAA;AAAA,QACA,QAAA,QAAA,MAAA;AAAA,QACA,SAAA,QAAA,MAAA,QAAA;AAAA,QACA,SAAA,QAAA,MAAA;AAAA,QACA,OAAA,QAAA,MAAA,QAAA;AAAA,QACA,cAAA,QAAA,MAAA,eAAA,QAAA,MAAA,aAAA,UAAA,GAAA,EAAA,IAAA,QAAA;AAAA,QACA,QAAA;AAAA,QACA,MAAA,QAAA,MAAA,QAAA;AAAA,QACA,SAAA;AAAA,MACA;AAgBA,YAAA,MAAAA,cAAAA,MAAA,oBAAA,cAAA;AAGA,UAAA,KAAA;AACA,UAAA,UAAA,WAAA,SAAA,GAAA,GAAA,KAAA,GAAA;AAEA,UAAA,aAAA,qBAAA;AACA,UAAA,SAAA,GAAA,GAAA,KAAA,GAAA;AACA,UAAA,QAAA;AAGA,UAAA,KAAA;AACA,UAAA,aAAA,SAAA;AACA,UAAA,SAAA,IAAA,KAAA,KAAA,GAAA;AACA,UAAA,QAAA;AAGA,UAAA,KAAA;AACA,UAAA,UAAA;AACA,UAAA,IAAA,KAAA,KAAA,IAAA,GAAA,IAAA,KAAA,EAAA;AACA,UAAA,aAAA,SAAA;AACA,UAAA,KAAA;AAEA,UAAA,KAAA;AACA,UAAA,UAAA,WAAA,MAAA,KAAA,KAAA,KAAA,GAAA;AACA,UAAA,QAAA;AAGA,UAAA,aAAA,SAAA;AACA,UAAA,YAAA,EAAA;AACA,UAAA,aAAA,QAAA;AACA,UAAA,SAAA,WAAA,OAAA,KAAA,GAAA;AAGA,UAAA,aAAA,SAAA;AACA,UAAA,YAAA,EAAA;AACA,UAAA,SAAA,WAAA,QAAA,KAAA,GAAA;AAGA,UAAA,UAAA;AACA,UAAA,eAAA,SAAA;AACA,UAAA,aAAA,CAAA;AACA,UAAA,OAAA,KAAA,GAAA;AACA,UAAA,OAAA,KAAA,GAAA;AACA,UAAA,OAAA;AAGA,UAAA,aAAA,SAAA;AACA,UAAA,YAAA,EAAA;AACA,UAAA,aAAA,MAAA;AACA,UAAA,SAAA,SAAA,WAAA,SAAA,IAAA,GAAA;AAGA,UAAA,SAAA,SAAA,WAAA,SAAA,IAAA,GAAA;AAGA,YAAA,WAAA,CAAAG,MAAA,MAAA,GAAA,GAAA,UAAA,eAAA;AACA,YAAA,KAAA,WAAA;AAAA;AAEA,cAAA,QAAA,KAAA,MAAA,EAAA;AACA,YAAA,OAAA;AACA,YAAA,WAAA;AACA,YAAA,YAAA;AAEA,iBAAA,IAAA,GAAA,IAAA,MAAA,QAAA,KAAA;AACA,sBAAA,MAAA,CAAA;AACA,gBAAA,UAAAA,KAAA,YAAA,QAAA;AACA,gBAAA,YAAA,QAAA;AAEA,cAAA,YAAA,YAAA,IAAA,GAAA;AACA,YAAAA,KAAA,SAAA,MAAA,GAAA,IAAA,YAAA,UAAA;AACA,mBAAA,MAAA,CAAA;AACA,uBAAA,MAAA,CAAA;AACA;AAEA,gBAAA,aAAA,GAAA;AACA,sBAAA;AACA,cAAAA,KAAA,SAAA,MAAA,GAAA,IAAA,YAAA,UAAA;AACA;AAAA,YACA;AAAA,UACA,OAAA;AACA,mBAAA;AAAA,UACA;AAAA,QACA;AAEA,YAAA,YAAA,GAAA;AACA,UAAAA,KAAA,SAAA,MAAA,GAAA,IAAA,YAAA,UAAA;AAAA,QACA;AAAA,MACA;AAGA,UAAA,aAAA,SAAA;AACA,UAAA,SAAA,SAAA,IAAA,GAAA;AACA,eAAA,KAAA,WAAA,cAAA,IAAA,KAAA,KAAA,EAAA;AAGA,UAAA,WAAA,OAAA;AACA,YAAA,SAAA,WAAA,WAAA,OAAA,IAAA,GAAA;AAAA,MACA;AAGA,UAAA,UAAA,WAAA,QAAA,KAAA,KAAA,KAAA,GAAA;AAGA,UAAA,aAAA,SAAA;AACA,UAAA,YAAA,EAAA;AACA,UAAA,aAAA,QAAA;AACA,UAAA,SAAA,eAAA,KAAA,GAAA;AAGA,UAAA,aAAA,SAAA;AACA,UAAA,YAAA,EAAA;AACA,UAAA,SAAA,eAAA,KAAA,GAAA;AAGA,UAAA,KAAA,OAAA,MAAA;AACA,mBAAA,MAAA;AAEAH,wBAAAA,MAAA,qBAAA;AAAA,YACA,UAAA;AAAA,YACA,SAAA,CAAA,QAAA;AACAA,4BAAA,MAAA,YAAA;AACA,8BAAA,IAAA,YAAA;AAAA,YACA;AAAA,YACA,MAAA,CAAA,QAAA;AACAA,4BAAA,MAAA,MAAA,SAAA,uCAAA,UAAA,GAAA;AACAA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA,gBACA,OAAA;AAAA,gBACA,MAAA;AAAA,cACA,CAAA;AAAA,YACA;AAAA,UACA,CAAA;AAAA,QACA,GAAA,GAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,kBAAA,CAAA,eAAA;AACA,sBAAA,QAAA;AACA,qBAAA,QAAA;AAEAA,oBAAAA,MAAA,UAAA;AAAA,QACA,OAAA;AAAA,QACA,SAAA;AAAA,QACA,aAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACA,cAAA,IAAA,SAAA;AACA,8BAAA,UAAA;AAAA,UACA,OAAA;AAEAA,0BAAAA,MAAA,aAAA;AAAA,cACA,MAAA,CAAA,UAAA;AAAA,cACA,SAAA;AAAA,YACA,CAAA;AAAA,UACA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,oBAAA,CAAA,eAAA;AACAA,oBAAAA,MAAA,YAAA;AAAA,QACA,OAAA;AAAA,MACA,CAAA;AAEAA,oBAAAA,MAAA,uBAAA;AAAA,QACA,UAAA;AAAA,QACA,SAAA,MAAA;AACAA,wBAAA,MAAA,YAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,QACA,MAAA,CAAA,QAAA;AACAA,wBAAA,MAAA,YAAA;AACAA,wBAAA,MAAA,MAAA,SAAA,uCAAA,QAAA,GAAA;AAEA,cAAA,IAAA,OAAA,QAAA,WAAA,IAAA,IAAA;AACAA,0BAAAA,MAAA,UAAA;AAAA,cACA,OAAA;AAAA,cACA,SAAA;AAAA,cACA,aAAA;AAAA,cACA,SAAA,CAAA,QAAA;AACA,oBAAA,IAAA,SAAA;AACAA,gCAAA,MAAA,YAAA;AAAA,gBACA;AAAA,cACA;AAAA,YACA,CAAA;AAAA,UACA,OAAA;AACAA,0BAAAA,MAAA,UAAA;AAAA,cACA,OAAA;AAAA,cACA,MAAA;AAAA,YACA,CAAA;AAAA,UACA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,cAAAF,cAAAA,IAAA,CAAA,CAAA;AAGA,UAAA,kBAAA,MAAA;AAKA,iBAAA,MAAA;AACA,oBAAA,QAAA;AAAA,UACA;AAAA,YACA,IAAA;AAAA,YACA,OAAA;AAAA,YACA,QAAA;AAAA,YACA,aAAA;AAAA,YACA,aAAA;AAAA,YACA,MAAA,CAAA,QAAA,OAAA,MAAA;AAAA,UACA;AAAA,UACA;AAAA,YACA,IAAA;AAAA,YACA,OAAA;AAAA,YACA,QAAA;AAAA,YACA,aAAA;AAAA,YACA,aAAA;AAAA,YACA,MAAA,CAAA,MAAA,MAAA,KAAA;AAAA,UACA;AAAA,UACA;AAAA,YACA,IAAA;AAAA,YACA,OAAA;AAAA,YACA,QAAA;AAAA,YACA,aAAA;AAAA,YACA,aAAA;AAAA,YACA,MAAA,CAAA,OAAA,QAAA,KAAA;AAAA,UACA;AAAA,QACA;AAAA,MACA,GAAA,GAAA;AAAA,IACA;AAGA,UAAA,sBAAA,CAAA,OAAA;AAEA,YAAA,QAAA;AACA,YAAA,cAAA,MAAA,MAAA,SAAA,CAAA;AACA,YAAA,UAAA,YAAA,WAAA;AAEA,UAAA,OAAA,QAAA,IAAA;AACA;AAAA,MACA;AAEAE,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,gCAAA,EAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,oBAAA,CAAA,MAAA;;AACA,UAAA;AAAA,UAAA;AACA,YAAA,gBAAA,aAAA,MAAA,SAAA,mBAAA,OAAA;AACAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,oDAAA,mBAAA,MAAA,CAAA,aAAA,mBAAA,WAAA,CAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGAD,kBAAAA,UAAA,MAAA;AAEAC,oBAAAA,MAAA,sBAAA;AAAA,QACA,OAAA;AAAA,MACA,CAAA;AAGA,YAAA,QAAA;AACA,YAAA,cAAA,MAAA,MAAA,SAAA,CAAA;AACA,YAAA,UAAA,YAAA,WAAA;AAGA,UAAA,QAAA,IAAA;AACAA,sBAAA,MAAA,MAAA,OAAA,uCAAA,gBAAA,QAAA,EAAA;AAOA,mBAAA,QAAAA,oBAAA,eAAA,YAAA,QAAA,MAAA,QAAA,EAAA,EAAA,KAAA;AACA,iBAAA,QAAAA,oBAAA,eAAA,UAAA,QAAA,MAAA,EAAA,EAAA,KAAA;AACA,oBAAA,QAAAA,oBAAA,eAAA,aAAA,QAAA,MAAA,EAAA,EAAA,KAAA;AAGA,uBAAA,QAAAA,oBAAA,eAAA,eAAA,QAAA,MAAA,EAAA,EAAA,KAAA;AAGA,YAAA,QAAA,WAAA,SAAA;AACA;AACA,yBAAA,SAAA;AAAA,QACA;AAAA,MACA;AAGA;IACA,CAAA;AAGA,UAAA,eAAA,MAAA;AACA,UAAA,CAAA,iBAAA,GAAA;AACA;AAAA,MACA;AAEA,iBAAA,QAAA,CAAA,WAAA;AAEA,UAAA,WAAA,OAAA;AAEA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AAAA,MACA,OAAA;AAEAA,4BAAA,kBAAA,YAAA,QAAA,MAAA,QAAA,EAAA,EAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,mBAAA,MAAA;AACA,UAAA,CAAA,iBAAA,GAAA;AACA;AAAA,MACA;AAEA,sBAAA,QAAA;AAAA,IACA;AAGA,UAAA,gBAAA,MAAA;AACA,UAAA,eAAA,MAAA,KAAA,MAAA,IAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AACA;AAAA,MACA;AAEAA,oBAAAA,MAAA,YAAA;AAAA,QACA,OAAA;AAAA,MACA,CAAA;AAGA,iBAAA,MAAA;AACAA,sBAAA,MAAA,YAAA;AAGA;AAEAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AAGA,uBAAA,QAAA;AACA,wBAAA,QAAA;AAAA,MACA,GAAA,GAAA;AAAA,IACA;AAGA,UAAA,mBAAA,MAAA;AACA,YAAA,WAAAA,cAAAA,MAAA,eAAA,UAAA;AACA,UAAA,CAAA,UAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,SAAA;AAAA,UACA,aAAA;AAAA,UACA,SAAA,CAAA,QAAA;AACA,gBAAA,IAAA,SAAA;AACAA,4BAAAA,MAAA,WAAA;AAAA,gBACA,KAAA;AAAA,cACA,CAAA;AAAA,YACA;AAAA,UACA;AAAA,QACA,CAAA;AACA,eAAA;AAAA,MACA;AACA,aAAA;AAAA,IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzjCA,GAAG,WAAWI,SAAe;"}