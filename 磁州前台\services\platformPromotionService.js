import request from '@/utils/request';
import store from '@/store';

/**
 * 平台级推广服务
 * 提供全平台通用的推广能力和配置
 */
class PlatformPromotionService {
  /**
   * 获取全局推广配置
   * @returns {Promise} 全局配置信息
   */
  async getGlobalConfig() {
    try {
      const { data } = await request.get('/api/promotion/config');
      return data;
    } catch (error) {
      console.error('获取推广配置失败', error);
      return {
        enabled: true, // 默认启用
        enabledModules: ['product', 'service', 'carpool', 'secondhand', 'house', 'community', 'content', 'activity']
      };
    }
  }
  
  /**
   * 检查特定业务模块推广工具是否启用
   * @param {string} moduleType - 业务模块类型
   * @returns {Promise<boolean>} 是否启用
   */
  async isModuleEnabled(moduleType) {
    const config = await this.getGlobalConfig();
    return config.enabled && (config.enabledModules?.includes(moduleType) ?? false);
  }
  
  /**
   * 获取当前用户的推广权限
   * @returns {Promise<object>} 权限对象
   */
  async getUserPromotionPermissions() {
    try {
      // 先尝试从缓存获取
      if (store.state.promotion?.permissions) {
        return store.state.promotion.permissions;
      }
      
      // 从服务器获取
      const { data } = await request.get('/api/promotion/user/permissions');
      
      // 缓存到状态管理
      if (store.commit) {
        store.commit('promotion/SET_PERMISSIONS', data);
      }
      
      return data;
    } catch (error) {
      console.error('获取用户推广权限失败', error);
      // 返回默认权限
      return {
        canPromote: true, // 默认可以推广自己的内容
        canEarnCommission: false, // 默认不能赚取佣金
        allowedModules: ['product', 'service', 'carpool', 'secondhand', 'house', 'community']
      };
    }
  }
  
  /**
   * 获取内容的推广配置
   * @param {string} contentType - 内容类型
   * @param {string} contentId - 内容ID
   * @returns {Promise<object>} 推广配置
   */
  async getContentPromotionConfig(contentType, contentId) {
    try {
      const { data } = await request.get('/api/promotion/content/config', {
        params: { type: contentType, id: contentId }
      });
      return data;
    } catch (error) {
      console.error('获取内容推广配置失败', error);
      // 返回默认配置
      return {
        enabled: true,
        templates: [],
        commissionRate: 0
      };
    }
  }
  
  /**
   * 记录推广内容生成事件
   * @param {object} params - 推广参数
   */
  async logPromotionGeneration(params) {
    try {
      await request.post('/api/promotion/logs/generate', params);
    } catch (error) {
      console.error('记录推广生成失败', error);
    }
  }
  
  /**
   * 记录推广内容分享事件
   * @param {object} params - 分享参数
   */
  async logPromotionSharing(params) {
    try {
      await request.post('/api/promotion/logs/share', params);
    } catch (error) {
      console.error('记录推广分享失败', error);
    }
  }

  /**
   * 获取用户的推广数据统计
   * @returns {Promise<object>} 推广统计数据
   */
  async getUserPromotionStats() {
    try {
      const { data } = await request.get('/api/promotion/user/stats');
      return data;
    } catch (error) {
      console.error('获取用户推广统计失败', error);
      // 返回空数据
      return {
        totalShares: 0,
        totalViews: 0,
        totalConversions: 0,
        totalCommission: 0
      };
    }
  }

  /**
   * 获取推广模板列表
   * @param {string} type - 推广类型
   * @returns {Promise<Array>} 模板列表
   */
  async getPromotionTemplates(type) {
    try {
      const { data } = await request.get('/api/promotion/templates', {
        params: { type }
      });
      return data.list || [];
    } catch (error) {
      console.error('获取推广模板失败', error);
      // 返回空数组
      return [];
    }
  }

  /**
   * 验证内容推广资格
   * @param {string} type - 内容类型
   * @param {string} id - 内容ID
   * @returns {Promise<object>} 推广资格
   */
  async validatePromotionEligibility(type, id) {
    try {
      const { data } = await request.get('/api/promotion/validate', {
        params: { type, id }
      });
      return data;
    } catch (error) {
      console.error('验证推广资格失败', error);
      // 默认可以推广
      return {
        canPromote: true,
        reason: '',
        commissionInfo: null
      };
    }
  }
}

export default new PlatformPromotionService(); 