"use strict";
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      currentView: "list",
      // list, detail, calendar
      currentStatus: "all",
      viewOptions: [
        { name: "列表视图", value: "list", icon: "📋" },
        { name: "详情视图", value: "detail", icon: "📝" },
        { name: "日历视图", value: "calendar", icon: "📅" }
      ],
      orderStatuses: [
        { name: "全部", value: "all" },
        { name: "待处理", value: "pending" },
        { name: "处理中", value: "processing" },
        { name: "已完成", value: "completed" },
        { name: "已取消", value: "cancelled" },
        { name: "退款中", value: "refunding" }
      ],
      orders: [
        {
          id: "1001",
          orderNo: "CZ20230501001",
          status: "pending",
          customerName: "张三",
          createTime: "2023-05-01 10:30",
          totalAmount: "128.00"
        },
        {
          id: "1002",
          orderNo: "CZ20230501002",
          status: "processing",
          customerName: "李四",
          createTime: "2023-05-01 11:45",
          totalAmount: "256.50"
        },
        {
          id: "1003",
          orderNo: "CZ20230502003",
          status: "completed",
          customerName: "王五",
          createTime: "2023-05-02 09:15",
          totalAmount: "89.90"
        }
      ],
      currentTab: 3,
      // 当前是订单管理
      tabItems: [
        {
          id: 0,
          icon: "dashboard",
          text: "商家中心",
          url: "/subPackages/merchant-admin-home/pages/merchant-home/index"
        },
        {
          id: 1,
          icon: "store",
          text: "店铺管理",
          url: "/subPackages/merchant-admin/pages/store/index"
        },
        {
          id: 2,
          icon: "marketing",
          text: "营销中心",
          url: "/subPackages/merchant-admin-marketing/pages/marketing/index"
        },
        {
          id: 3,
          icon: "orders",
          text: "订单管理",
          url: "/subPackages/merchant-admin-order/pages/order/index"
        },
        {
          id: "more",
          icon: "more",
          text: "更多",
          url: ""
        }
      ]
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    switchView(view) {
      this.currentView = view;
    },
    filterByStatus(status) {
      this.currentStatus = status;
      this.loadOrders();
    },
    showFilterOptions() {
      common_vendor.index.showToast({
        title: "筛选功能开发中",
        icon: "none"
      });
    },
    loadOrders() {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-order/pages/order/index.vue:215", "加载订单数据，状态:", this.currentStatus);
    },
    handleOrder(orderId) {
      common_vendor.index.navigateTo({
        url: `./detail?id=${orderId}&action=process`
      });
    },
    contactCustomer(orderId) {
      common_vendor.index.showToast({
        title: "联系客户功能开发中",
        icon: "none"
      });
    },
    // 切换底部导航栏
    switchTab(tabId) {
      if (tabId === "more") {
        this.showMoreOptions();
        return;
      }
      if (tabId === this.currentTab)
        return;
      this.currentTab = tabId;
      if (tabId === 3) {
        return;
      }
      common_vendor.index.redirectTo({
        url: this.tabItems[tabId].url,
        fail: (err) => {
          common_vendor.index.__f__("error", "at subPackages/merchant-admin-order/pages/order/index.vue:250", "redirectTo失败:", err);
          common_vendor.index.switchTab({
            url: this.tabItems[tabId].url,
            fail: (switchErr) => {
              common_vendor.index.__f__("error", "at subPackages/merchant-admin-order/pages/order/index.vue:255", "switchTab也失败:", switchErr);
              common_vendor.index.showToast({
                title: "页面跳转失败，请稍后再试",
                icon: "none"
              });
            }
          });
        }
      });
    },
    // 显示更多选项弹出菜单
    showMoreOptions() {
      const moreOptions = ["客户运营", "分析洞察", "系统设置"];
      common_vendor.index.showActionSheet({
        itemList: moreOptions,
        success: (res) => {
          const routes = [
            "/subPackages/merchant-admin-customer/pages/customer/index",
            "/subPackages/merchant-admin/pages/settings/index"
          ];
          common_vendor.index.navigateTo({
            url: routes[res.tapIndex]
          });
        }
      });
    },
    getStatusColor(status) {
      const colors = {
        pending: "#FF9800",
        processing: "#2196F3",
        completed: "#4CAF50",
        cancelled: "#9E9E9E",
        refunding: "#F44336"
      };
      return colors[status] || "#333333";
    },
    getStatusText(status) {
      const texts = {
        pending: "待处理",
        processing: "处理中",
        completed: "已完成",
        cancelled: "已取消",
        refunding: "退款中"
      };
      return texts[status] || "未知状态";
    },
    navigateTo(url) {
      common_vendor.index.navigateTo({ url });
    }
  },
  onLoad() {
    this.loadOrders();
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.f($data.viewOptions, (item, index, i0) => {
      return {
        a: common_vendor.n("bg-" + item.value),
        b: common_vendor.t(item.name),
        c: index,
        d: $data.currentView === item.value ? 1 : "",
        e: common_vendor.o(($event) => $options.switchView(item.value), index)
      };
    }),
    c: common_vendor.f($data.orderStatuses, (status, index, i0) => {
      return {
        a: common_vendor.t(status.name),
        b: index,
        c: common_vendor.n({
          "active": $data.currentStatus === status.value
        }),
        d: common_vendor.o(($event) => $options.filterByStatus(status.value), index)
      };
    }),
    d: $data.orders.length > 0
  }, $data.orders.length > 0 ? common_vendor.e({
    e: $data.currentView === "list"
  }, $data.currentView === "list" ? {
    f: common_vendor.f($data.orders, (order, index, i0) => {
      return {
        a: common_vendor.t(order.orderNo),
        b: common_vendor.t($options.getStatusText(order.status)),
        c: common_vendor.n("status-" + order.status),
        d: common_vendor.t(order.customerName),
        e: common_vendor.t(order.createTime),
        f: common_vendor.t(order.totalAmount),
        g: common_vendor.o(($event) => $options.handleOrder(order.id), index),
        h: common_vendor.o(($event) => $options.contactCustomer(order.id), index),
        i: index,
        j: `./detail?id=${order.id}`
      };
    })
  } : $data.currentView === "detail" ? {
    h: common_vendor.o(($event) => $options.navigateTo("./detail"))
  } : $data.currentView === "calendar" ? {
    j: common_vendor.o(($event) => $options.navigateTo("./calendar"))
  } : {}, {
    g: $data.currentView === "detail",
    i: $data.currentView === "calendar"
  }) : {
    k: common_vendor.o((...args) => $options.loadOrders && $options.loadOrders(...args))
  }, {
    l: common_vendor.f($data.tabItems, (item, index, i0) => {
      return common_vendor.e({
        a: $data.currentTab === item.id
      }, $data.currentTab === item.id ? {} : {}, {
        b: common_vendor.n(item.icon),
        c: common_vendor.t(item.text),
        d: index,
        e: $data.currentTab === item.id ? 1 : "",
        f: common_vendor.o(($event) => $options.switchTab(item.id), index)
      });
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-order/pages/order/index.js.map
