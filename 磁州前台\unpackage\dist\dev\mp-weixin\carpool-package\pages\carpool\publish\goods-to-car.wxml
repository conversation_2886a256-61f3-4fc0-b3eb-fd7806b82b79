<view class="publish-container"><carpool-nav wx:if="{{a}}" u-i="8008127a-0" bind:__l="__l" u-p="{{a}}"></carpool-nav><view class="form-container"><view class="form-group"><view class="form-title">货物信息</view><view class="form-item"><text class="label">货物名称</text><input type="text" placeholder="请输入货物名称" value="{{b}}" bindinput="{{c}}"/></view><view class="form-item"><text class="label">货物类型</text><picker mode="selector" range="{{f}}" value="{{g}}" bindchange="{{h}}"><view class="picker-value"><text>{{d}}</text><image src="{{e}}" mode="aspectFit"></image></view></picker></view><view class="form-item"><text class="label">货物重量</text><view class="weight-input"><input type="digit" placeholder="请输入货物重量" value="{{i}}" bindinput="{{j}}"/><text class="unit">公斤</text></view></view><view class="form-item"><text class="label">货物体积</text><view class="volume-input"><input type="digit" placeholder="请输入货物体积" value="{{k}}" bindinput="{{l}}"/><text class="unit">立方米</text></view></view></view><view class="form-group"><view class="form-title">运输信息</view><view class="form-item"><text class="label">出发地</text><view class="input-wrapper"><input type="text" placeholder="请输入出发地" value="{{m}}" bindinput="{{n}}"/><view class="location-btn" bindtap="{{p}}"><image src="{{o}}" mode="aspectFit"></image></view></view></view><view class="form-item"><text class="label">目的地</text><view class="input-wrapper"><input type="text" placeholder="请输入目的地" value="{{q}}" bindinput="{{r}}"/><view class="location-btn" bindtap="{{t}}"><image src="{{s}}" mode="aspectFit"></image></view></view></view><view class="form-item"><text class="label">途径地点</text><view class="via-points-container"><view wx:for="{{v}}" wx:for-item="point" wx:key="e" class="via-point-item"><view class="input-wrapper"><input type="text" placeholder="请输入途径地点" value="{{point.a}}" bindinput="{{point.b}}"/><view class="location-btn" bindtap="{{point.c}}"><image src="{{w}}" mode="aspectFit"></image></view></view><view class="via-point-actions"><view class="via-point-delete" bindtap="{{point.d}}"><text class="delete-icon">×</text></view></view></view><view wx:if="{{x}}" class="add-via-point" bindtap="{{y}}"><text class="add-icon">+</text><text class="add-text">添加途径地点</text></view></view></view><view class="form-item"><text class="label">期望发货日期</text><picker mode="date" value="{{B}}" start="2023-01-01" end="2030-12-31" bindchange="{{C}}"><view class="picker-value"><text>{{z}}</text><image src="{{A}}" mode="aspectFit"></image></view></picker></view><view class="form-item"><text class="label">期望运费</text><view class="price-input"><input type="digit" placeholder="请输入期望运费（选填）" value="{{D}}" bindinput="{{E}}"/><text class="unit">元</text></view></view></view><view class="form-group"><view class="form-title">联系方式</view><view class="form-item"><text class="label">联系人</text><input type="text" placeholder="请输入联系人姓名（选填）" value="{{F}}" bindinput="{{G}}"/></view><view class="form-item"><text class="label">手机号码</text><input type="number" placeholder="请输入手机号码" maxlength="11" value="{{H}}" bindinput="{{I}}"/></view></view><view class="form-group"><view class="form-title">补充说明</view><view class="form-item"><block wx:if="{{r0}}"><textarea placeholder="请输入补充说明（选填）" maxlength="200" value="{{J}}" bindinput="{{K}}"/></block><view class="word-count">{{L}}/200</view></view></view></view><view class="submit-section"><view class="agreement"><checkbox-group bindchange="{{O}}"><label><checkbox checked="{{M}}" color="#0A84FF"/><text>我已阅读并同意</text><text class="link" catchtap="{{N}}">《拼车服务协议》</text></label></checkbox-group></view><view class="publish-options"><view class="options-title">选择发布方式</view><view class="{{['option-card', R && 'option-selected']}}" bindtap="{{S}}"><view class="option-icon ad-icon"><image src="{{P}}" mode="aspectFit"></image></view><view class="option-content"><view class="option-name"><text>免费发布</text><view class="option-tag recommend">推荐</view></view><view class="option-desc">观看15秒广告免费发布</view></view><view class="option-checkbox"><view wx:if="{{Q}}" class="checkbox-inner"></view></view></view><view class="{{['option-card', V && 'option-selected']}}" bindtap="{{W}}"><view class="option-icon premium-icon"><image src="{{T}}" mode="aspectFit"></image></view><view class="option-content"><view class="option-name"><text>置顶发布</text><view class="option-tag premium">高级</view></view><view class="option-desc">支付5元获得置顶特权</view></view><view class="option-checkbox"><view wx:if="{{U}}" class="checkbox-inner"></view></view></view></view><button class="submit-btn" disabled="{{X}}" bindtap="{{Y}}"> 发布信息 </button></view></view>