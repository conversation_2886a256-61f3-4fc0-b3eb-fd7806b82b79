/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.promotion-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 88rpx 32rpx 30rpx;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);
}
.navbar-back {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 24rpx;
  height: 24rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}
.navbar-right {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

/* 推广工具卡片 */
.tools-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.tool-card {
  width: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
  box-sizing: border-box;
  border-right: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
}
.tool-card:nth-child(2n) {
  border-right: none;
}
.tool-card:nth-child(3), .tool-card:nth-child(4) {
  border-bottom: none;
}
.tool-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
  border-radius: 40rpx;
}
.tool-icon.poster {
  background-color: #FF9500;
}
.tool-icon.qrcode {
  background-color: #409EFF;
}
.tool-icon.text {
  background-color: #67C23A;
}
.tool-icon.materials {
  background-color: #E6A23C;
}
.tool-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.tool-desc {
  font-size: 24rpx;
  color: #999;
}

/* 卡片通用样式 */
.link-card,
.guide-card,
.tips-card,
.data-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.refresh-btn {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #6B0FBE;
}
.refresh-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
  background-color: #6B0FBE;
  border-radius: 40rpx;
}
.view-all {
  font-size: 26rpx;
  color: #6B0FBE;
}

/* 推广链接 */
.link-content {
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 20rpx;
}
.link-url {
  font-size: 26rpx;
  color: #666;
  word-break: break-all;
  margin-bottom: 20rpx;
  display: block;
}
.link-actions {
  display: flex;
  justify-content: flex-end;
}
.action-btn {
  font-size: 24rpx;
  padding: 8rpx 30rpx;
  border-radius: 30rpx;
  margin-left: 20rpx;
  line-height: 1.5;
}
.action-btn.copy {
  background: #6B0FBE;
  color: #fff;
}
.action-btn.share {
  background: #FF9500;
  color: #fff;
}

/* 推广指南 */
.guide-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.guide-step {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.step-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
  border-radius: 40rpx;
}
.step-icon.step1 {
  background-color: #6B0FBE;
}
.step-icon.step2 {
  background-color: #409EFF;
}
.step-icon.step3 {
  background-color: #67C23A;
}
.step-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.step-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.step-desc {
  font-size: 24rpx;
  color: #999;
}
.step-arrow {
  width: 40rpx;
  height: 20rpx;
}

/* 推广技巧 */
.tips-list {
  margin-bottom: 20rpx;
}
.tip-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.tip-item:last-child {
  margin-bottom: 0;
}
.tip-icon {
  width: 16rpx;
  height: 16rpx;
  border-radius: 40rpx;
  background-color: #6B0FBE;
  margin-right: 16rpx;
  flex-shrink: 0;
}
.tip-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 推广数据 */
.data-grid {
  display: flex;
  flex-wrap: wrap;
}
.data-item {
  width: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}
.data-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.data-label {
  font-size: 24rpx;
  color: #999;
}