{"version": 3, "file": "management.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/points/management.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xwb2ludHNcbWFuYWdlbWVudC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"points-management-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-left\">\r\n        <view class=\"back-button\" @tap=\"goBack\">\r\n          <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n            <path d=\"M15 18L9 12L15 6\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n          </svg>\r\n        </view>\r\n      </view>\r\n      <view class=\"navbar-title\">\r\n        <text class=\"title-text\">积分商城管理</text>\r\n      </view>\r\n      <view class=\"navbar-right\">\r\n        <!-- 不再使用帮助按钮，保持与营销中心一致 -->\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 顶部操作区 -->\r\n    <view class=\"top-actions\">\r\n      <CreateButton text=\"创建积分商品\" theme=\"points\" @click=\"createPointsItem\" />\r\n    </view>\r\n    \r\n    <!-- 页面内容区域 -->\r\n    <scroll-view scroll-y class=\"content-area\">\r\n      <!-- 积分概览区域 -->\r\n      <view class=\"overview-section\">\r\n        <view class=\"overview-header\">\r\n          <text class=\"section-title\">积分概览</text>\r\n          <view class=\"date-picker\">\r\n            <text class=\"date-text\">近30天</text>\r\n            <view class=\"arrow-icon\"></view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"overview-cards\">\r\n          <view class=\"overview-card\">\r\n            <view class=\"card-value\">{{pointsData.totalPoints}}</view>\r\n            <view class=\"card-label\">累计发放积分</view>\r\n          </view>\r\n          <view class=\"overview-card\">\r\n            <view class=\"card-value\">{{pointsData.usedPoints}}</view>\r\n            <view class=\"card-label\">已使用积分</view>\r\n          </view>\r\n          <view class=\"overview-card\">\r\n            <view class=\"card-value\">{{pointsData.activeUsers}}</view>\r\n            <view class=\"card-label\">活跃用户数</view>\r\n          </view>\r\n          <view class=\"overview-card\">\r\n            <view class=\"card-value\">{{pointsData.conversionRate}}%</view>\r\n            <view class=\"card-label\">兑换转化率</view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 积分商品列表 -->\r\n      <view class=\"points-items-section\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">积分商品</text>\r\n          <view class=\"filter-buttons\">\r\n            <view class=\"filter-button\" :class=\"{ active: activeFilter === 'all' }\" @tap=\"setFilter('all')\">全部</view>\r\n            <view class=\"filter-button\" :class=\"{ active: activeFilter === 'active' }\" @tap=\"setFilter('active')\">进行中</view>\r\n            <view class=\"filter-button\" :class=\"{ active: activeFilter === 'draft' }\" @tap=\"setFilter('draft')\">草稿</view>\r\n            <view class=\"filter-button\" :class=\"{ active: activeFilter === 'ended' }\" @tap=\"setFilter('ended')\">已结束</view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 商品列表 -->\r\n        <view class=\"points-items-list\">\r\n          <view class=\"points-item\" v-for=\"(item, index) in filteredItems\" :key=\"index\" @tap=\"viewItemDetail(item)\">\r\n            <view class=\"item-image-container\">\r\n              <image class=\"item-image\" :src=\"item.image\" mode=\"aspectFill\"></image>\r\n              <view class=\"item-status\" :class=\"item.statusClass\">{{item.statusText}}</view>\r\n            </view>\r\n            <view class=\"item-content\">\r\n              <view class=\"item-name\">{{item.name}}</view>\r\n              <view class=\"item-points\">\r\n                <text class=\"points-value\">{{item.points}}</text>\r\n                <text class=\"points-label\">积分</text>\r\n              </view>\r\n              <view class=\"item-stats\">\r\n                <view class=\"stat-item\">\r\n                  <text class=\"stat-value\">{{item.stock}}</text>\r\n                  <text class=\"stat-label\">库存</text>\r\n                </view>\r\n                <view class=\"stat-item\">\r\n                  <text class=\"stat-value\">{{item.redeemed}}</text>\r\n                  <text class=\"stat-label\">已兑换</text>\r\n                </view>\r\n                <view class=\"stat-item\">\r\n                  <text class=\"stat-value\">{{item.views}}</text>\r\n                  <text class=\"stat-label\">浏览</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"item-time\">{{item.timeRange}}</view>\r\n            </view>\r\n            <view class=\"item-actions\">\r\n              <view class=\"action-button edit\" @tap.stop=\"editItem(item)\">\r\n                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <path d=\"M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13\" stroke=\"#5E5CE6\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                  <path d=\"M18.5 2.50001C18.8978 2.10219 19.4374 1.87869 20 1.87869C20.5626 1.87869 21.1022 2.10219 21.5 2.50001C21.8978 2.89784 22.1213 3.4374 22.1213 4.00001C22.1213 4.56262 21.8978 5.10219 21.5 5.50001L12 15L8 16L9 12L18.5 2.50001Z\" stroke=\"#5E5CE6\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                </svg>\r\n              </view>\r\n              <view class=\"action-button delete\" @tap.stop=\"deleteItem(item)\">\r\n                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <path d=\"M3 6H5H21\" stroke=\"#FF3B30\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                  <path d=\"M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z\" stroke=\"#FF3B30\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                </svg>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 空状态 -->\r\n        <view class=\"empty-state\" v-if=\"filteredItems.length === 0\">\r\n          <image class=\"empty-image\" src=\"/static/images/empty-points.png\" mode=\"aspectFit\"></image>\r\n          <text class=\"empty-text\">暂无{{filterLabels[activeFilter]}}积分商品</text>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 积分规则设置 -->\r\n      <view class=\"points-rules-section\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">积分规则设置</text>\r\n          <view class=\"edit-button\" @tap=\"editRules\">\r\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path d=\"M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13\" stroke=\"#5E5CE6\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n              <path d=\"M18.5 2.50001C18.8978 2.10219 19.4374 1.87869 20 1.87869C20.5626 1.87869 21.1022 2.10219 21.5 2.50001C21.8978 2.89784 22.1213 3.4374 22.1213 4.00001C22.1213 4.56262 21.8978 5.10219 21.5 5.50001L12 15L8 16L9 12L18.5 2.50001Z\" stroke=\"#5E5CE6\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n            </svg>\r\n            <text>编辑</text>\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"rules-list\">\r\n          <view class=\"rule-item\" v-for=\"(rule, index) in pointsRules\" :key=\"index\">\r\n            <view class=\"rule-icon\" :class=\"rule.type\">\r\n              <svg v-if=\"rule.type === 'purchase'\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path d=\"M6 2L3 6V20C3 20.5304 3.21071 21.0391 3.58579 21.4142C3.96086 21.7893 4.46957 22 5 22H19C19.5304 22 20.0391 21.7893 20.4142 21.4142C20.7893 21.0391 21 20.5304 21 20V6L18 2H6Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                <path d=\"M3 6H21\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                <path d=\"M16 10C16 11.0609 15.5786 12.0783 14.8284 12.8284C14.0783 13.5786 13.0609 14 12 14C10.9391 14 9.92172 13.5786 9.17157 12.8284C8.42143 12.0783 8 11.0609 8 10\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n              </svg>\r\n              <svg v-if=\"rule.type === 'checkin'\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path d=\"M9 11L12 14L22 4\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                <path d=\"M21 12V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H16\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n              </svg>\r\n              <svg v-if=\"rule.type === 'share'\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path d=\"M18 8C19.6569 8 21 6.65685 21 5C21 3.34315 19.6569 2 18 2C16.3431 2 15 3.34315 15 5C15 6.65685 16.3431 8 18 8Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                <path d=\"M6 15C7.65685 15 9 13.6569 9 12C9 10.3431 7.65685 9 6 9C4.34315 9 3 10.3431 3 12C3 13.6569 4.34315 15 6 15Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                <path d=\"M18 22C19.6569 22 21 20.6569 21 19C21 17.3431 19.6569 16 18 16C16.3431 16 15 17.3431 15 19C15 20.6569 16.3431 22 18 22Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                <path d=\"M8.59 13.51L15.42 17.49\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                <path d=\"M15.41 6.51L8.59 10.49\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n              </svg>\r\n            </view>\r\n            <view class=\"rule-content\">\r\n              <view class=\"rule-title\">{{rule.title}}</view>\r\n              <view class=\"rule-desc\">{{rule.description}}</view>\r\n            </view>\r\n            <view class=\"rule-points\">+{{rule.points}}</view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      \r\n      <!-- 底部空间 -->\r\n      <view class=\"bottom-space\"></view>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, computed, onMounted } from 'vue';\r\nimport CreateButton from '/subPackages/merchant-admin-marketing/components/CreateButton.vue';\r\n\r\n// 积分数据\r\nconst pointsData = reactive({\r\n  totalPoints: '125,680',\r\n  usedPoints: '87,456',\r\n  activeUsers: '1,254',\r\n  conversionRate: '68.5'\r\n});\r\n\r\n// 筛选相关\r\nconst activeFilter = ref('all');\r\nconst filterLabels = {\r\n  all: '全部',\r\n  active: '进行中',\r\n  draft: '草稿',\r\n  ended: '已结束'\r\n};\r\n\r\n// 积分商品数据\r\nconst pointsItems = ref([\r\n  {\r\n    id: 1,\r\n    name: '精美保温杯',\r\n    points: 2000,\r\n    stock: 100,\r\n    redeemed: 35,\r\n    views: 1240,\r\n    image: '/static/images/points-item1.jpg',\r\n    timeRange: '2023-05-01 ~ 2023-12-31',\r\n    status: 'active',\r\n    statusText: '进行中',\r\n    statusClass: 'status-active'\r\n  },\r\n  {\r\n    id: 2,\r\n    name: '无线蓝牙耳机',\r\n    points: 5000,\r\n    stock: 50,\r\n    redeemed: 12,\r\n    views: 876,\r\n    image: '/static/images/points-item2.jpg',\r\n    timeRange: '2023-05-15 ~ 2023-12-31',\r\n    status: 'active',\r\n    statusText: '进行中',\r\n    statusClass: 'status-active'\r\n  },\r\n  {\r\n    id: 3,\r\n    name: '会员月卡',\r\n    points: 1000,\r\n    stock: 999,\r\n    redeemed: 128,\r\n    views: 3560,\r\n    image: '/static/images/points-item3.jpg',\r\n    timeRange: '2023-04-01 ~ 2023-12-31',\r\n    status: 'active',\r\n    statusText: '进行中',\r\n    statusClass: 'status-active'\r\n  },\r\n  {\r\n    id: 4,\r\n    name: '限定礼盒套装',\r\n    points: 8000,\r\n    stock: 20,\r\n    redeemed: 0,\r\n    views: 0,\r\n    image: '/static/images/points-item4.jpg',\r\n    timeRange: '未发布',\r\n    status: 'draft',\r\n    statusText: '草稿',\r\n    statusClass: 'status-draft'\r\n  },\r\n  {\r\n    id: 5,\r\n    name: '春节限定福袋',\r\n    points: 3000,\r\n    stock: 200,\r\n    redeemed: 200,\r\n    views: 4280,\r\n    image: '/static/images/points-item5.jpg',\r\n    timeRange: '2023-01-15 ~ 2023-02-15',\r\n    status: 'ended',\r\n    statusText: '已结束',\r\n    statusClass: 'status-ended'\r\n  }\r\n]);\r\n\r\n// 根据筛选条件过滤商品\r\nconst filteredItems = computed(() => {\r\n  if (activeFilter.value === 'all') {\r\n    return pointsItems.value;\r\n  }\r\n  return pointsItems.value.filter(item => item.status === activeFilter.value);\r\n});\r\n\r\n// 设置筛选条件\r\nconst setFilter = (filter) => {\r\n  activeFilter.value = filter;\r\n};\r\n\r\n// 查看商品详情\r\nconst viewItemDetail = (item) => {\r\n  uni.showToast({\r\n    title: `查看商品: ${item.name}`,\r\n    icon: 'none'\r\n  });\r\n};\r\n\r\n// 编辑商品\r\nconst editItem = (item) => {\r\n  uni.showToast({\r\n    title: `编辑商品: ${item.name}`,\r\n    icon: 'none'\r\n  });\r\n};\r\n\r\n// 删除商品\r\nconst deleteItem = (item) => {\r\n  uni.showModal({\r\n    title: '确认删除',\r\n    content: `确定要删除\"${item.name}\"吗？`,\r\n    success: (res) => {\r\n      if (res.confirm) {\r\n        // 在实际应用中应该调用API删除商品\r\n        const index = pointsItems.value.findIndex(i => i.id === item.id);\r\n        if (index !== -1) {\r\n          pointsItems.value.splice(index, 1);\r\n          uni.showToast({\r\n            title: '删除成功',\r\n            icon: 'success'\r\n          });\r\n        }\r\n      }\r\n    }\r\n  });\r\n};\r\n\r\n// 积分规则数据\r\nconst pointsRules = ref([\r\n  {\r\n    id: 1,\r\n    type: 'purchase',\r\n    title: '商品购买',\r\n    description: '每消费1元获得1积分',\r\n    points: 1\r\n  },\r\n  {\r\n    id: 2,\r\n    type: 'checkin',\r\n    title: '每日签到',\r\n    description: '每日签到获得积分奖励',\r\n    points: 10\r\n  },\r\n  {\r\n    id: 3,\r\n    type: 'share',\r\n    title: '分享商品',\r\n    description: '分享商品到社交媒体获得积分',\r\n    points: 5\r\n  }\r\n]);\r\n\r\n// 编辑积分规则\r\nconst editRules = () => {\r\n  uni.navigateTo({\r\n    url: '/subPackages/merchant-admin-marketing/pages/marketing/points/rules'\r\n  });\r\n};\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 创建积分商品\r\nconst createPointsItem = () => {\r\n  uni.showToast({\r\n    title: '创建新的积分商品',\r\n    icon: 'none',\r\n    duration: 2000\r\n  });\r\n  \r\n  // 跳转到创建页面\r\n  setTimeout(() => {\r\n    uni.navigateTo({\r\n      url: '/subPackages/merchant-admin-marketing/pages/marketing/points/create'\r\n    });\r\n  }, 500);\r\n};\r\n\r\n// 页面加载\r\nonMounted(() => {\r\n  console.log('积分商城管理页面已加载');\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n/* 页面容器 */\r\n.points-management-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  position: relative;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #FF7600, #FF3C00);\r\n  color: white;\r\n  padding: 48px 20px 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 2px 8px rgba(255, 120, 0, 0.15);\r\n}\r\n\r\n.navbar-left {\r\n  width: 40px;\r\n}\r\n\r\n.back-button {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n}\r\n\r\n.title-text {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.navbar-right {\r\n  width: 40px;\r\n}\r\n\r\n/* 顶部操作区 */\r\n.top-actions {\r\n  padding: 16px;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n/* 内容区域 */\r\n.content-area {\r\n  flex: 1;\r\n  padding: 16px;\r\n  box-sizing: border-box;\r\n  height: calc(100vh - 80px);\r\n}\r\n\r\n/* 概览区块 */\r\n.overview-section {\r\n  background-color: #FFFFFF;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  padding: 20px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.overview-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333333;\r\n}\r\n\r\n.date-picker {\r\n  display: flex;\r\n  align-items: center;\r\n  background-color: #F5F7FA;\r\n  border-radius: 16px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n.date-text {\r\n  font-size: 12px;\r\n  color: #666666;\r\n}\r\n\r\n.arrow-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-right: 2px solid #999;\r\n  border-bottom: 2px solid #999;\r\n  margin-left: 4px;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.overview-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: 0 -10px;\r\n}\r\n\r\n.overview-card {\r\n  width: 50%;\r\n  padding: 10px;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.card-value {\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  color: #333333;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.card-label {\r\n  font-size: 14px;\r\n  color: #999999;\r\n}\r\n\r\n/* 积分商品列表 */\r\n.points-items-section {\r\n  background-color: #FFFFFF;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  margin-bottom: 16px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.filter-buttons {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.filter-button {\r\n  padding: 8px 16px;\r\n  border-radius: 16px;\r\n  background-color: #F5F7FA;\r\n  margin-left: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.filter-button.active {\r\n  background-color: #FF7600;\r\n  color: white;\r\n}\r\n\r\n.points-items-list {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.points-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 16px 0;\r\n  border-bottom: 1px solid #F0F0F0;\r\n  position: relative;\r\n}\r\n\r\n.item-image-container {\r\n  position: relative;\r\n  width: 80px;\r\n  height: 80px;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  margin-right: 16px;\r\n}\r\n\r\n.item-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.item-status {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  padding: 4px 8px;\r\n  font-size: 12px;\r\n  color: white;\r\n  z-index: 2;\r\n}\r\n\r\n.status-active {\r\n  background-color: #34C759;\r\n}\r\n\r\n.status-draft {\r\n  background-color: #999999;\r\n}\r\n\r\n.status-ended {\r\n  background-color: #FF3B30;\r\n}\r\n\r\n.item-content {\r\n  flex: 1;\r\n}\r\n\r\n.item-name {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333333;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.item-points {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.points-value {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #FF7600;\r\n  margin-right: 4px;\r\n}\r\n\r\n.points-label {\r\n  font-size: 14px;\r\n  color: #999999;\r\n}\r\n\r\n.item-stats {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.stat-item {\r\n  margin-right: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #333333;\r\n  margin-right: 4px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 12px;\r\n  color: #999999;\r\n}\r\n\r\n.item-time {\r\n  font-size: 12px;\r\n  color: #999999;\r\n}\r\n\r\n.item-actions {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.action-button {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-left: 8px;\r\n}\r\n\r\n.action-button.edit {\r\n  background-color: rgba(94, 92, 230, 0.1);\r\n}\r\n\r\n.action-button.delete {\r\n  background-color: rgba(255, 59, 48, 0.1);\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 40px 0;\r\n  text-align: center;\r\n}\r\n\r\n.empty-image {\r\n  width: 120px;\r\n  height: 120px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 14px;\r\n  color: #999999;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 积分规则 */\r\n.points-rules-section {\r\n  background-color: #FFFFFF;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  margin-bottom: 16px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.edit-button {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #5E5CE6;\r\n  font-size: 14px;\r\n}\r\n\r\n.edit-button text {\r\n  margin-left: 4px;\r\n}\r\n\r\n.rules-list {\r\n  margin-top: 16px;\r\n}\r\n\r\n.rule-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px 0;\r\n  border-bottom: 1px solid #F0F0F0;\r\n}\r\n\r\n.rule-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 12px;\r\n}\r\n\r\n.rule-icon.purchase {\r\n  background-color: rgba(25, 137, 250, 0.1);\r\n  color: #1989FA;\r\n}\r\n\r\n.rule-icon.checkin {\r\n  background-color: rgba(52, 199, 89, 0.1);\r\n  color: #34C759;\r\n}\r\n\r\n.rule-icon.share {\r\n  background-color: rgba(255, 149, 0, 0.1);\r\n  color: #FF9500;\r\n}\r\n\r\n.rule-content {\r\n  flex: 1;\r\n}\r\n\r\n.rule-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #333333;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.rule-desc {\r\n  font-size: 12px;\r\n  color: #999999;\r\n}\r\n\r\n.rule-points {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #FF7600;\r\n}\r\n\r\n/* 底部空间 */\r\n.bottom-space {\r\n  height: 20px;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/points/management.vue'\nwx.createPage(MiniProgramPage)"], "names": ["reactive", "ref", "computed", "uni", "onMounted", "MiniProgramPage"], "mappings": ";;;;;;;;;;;AA4KA,MAAM,eAAe,MAAW;;;;AAGhC,UAAM,aAAaA,cAAAA,SAAS;AAAA,MAC1B,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,gBAAgB;AAAA,IAClB,CAAC;AAGD,UAAM,eAAeC,cAAAA,IAAI,KAAK;AAC9B,UAAM,eAAe;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAGA,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,QACP,OAAO;AAAA,QACP,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,MACd;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,QACP,OAAO;AAAA,QACP,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,MACd;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,QACP,OAAO;AAAA,QACP,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,MACd;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,QACP,OAAO;AAAA,QACP,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,MACd;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,QACP,OAAO;AAAA,QACP,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,MACd;AAAA,IACH,CAAC;AAGD,UAAM,gBAAgBC,cAAQ,SAAC,MAAM;AACnC,UAAI,aAAa,UAAU,OAAO;AAChC,eAAO,YAAY;AAAA,MACpB;AACD,aAAO,YAAY,MAAM,OAAO,UAAQ,KAAK,WAAW,aAAa,KAAK;AAAA,IAC5E,CAAC;AAGD,UAAM,YAAY,CAAC,WAAW;AAC5B,mBAAa,QAAQ;AAAA,IACvB;AAGA,UAAM,iBAAiB,CAAC,SAAS;AAC/BC,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,SAAS,KAAK,IAAI;AAAA,QACzB,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,CAAC,SAAS;AACzBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,SAAS,KAAK,IAAI;AAAA,QACzB,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,SAAS;AAC3BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,SAAS,KAAK,IAAI;AAAA,QAC3B,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEf,kBAAM,QAAQ,YAAY,MAAM,UAAU,OAAK,EAAE,OAAO,KAAK,EAAE;AAC/D,gBAAI,UAAU,IAAI;AAChB,0BAAY,MAAM,OAAO,OAAO,CAAC;AACjCA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,cAAcF,cAAAA,IAAI;AAAA,MACtB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACH,CAAC;AAGD,UAAM,YAAY,MAAM;AACtBE,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,mBAAmB,MAAM;AAC7BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACd,CAAG;AAGD,iBAAW,MAAM;AACfA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK;AAAA,QACX,CAAK;AAAA,MACF,GAAE,GAAG;AAAA,IACR;AAGAC,kBAAAA,UAAU,MAAM;AACdD,oBAAAA,MAAY,MAAA,OAAA,qFAAA,aAAa;AAAA,IAC3B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7WD,GAAG,WAAWE,SAAe;"}