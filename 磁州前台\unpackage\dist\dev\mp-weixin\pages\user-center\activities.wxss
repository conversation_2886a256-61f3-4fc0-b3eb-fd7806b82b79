/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.activities-container {
  min-height: 100vh;
  background-color: #F5F5F7;
}
.content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 44px);
  /* 减去导航栏高度 */
}

/* 标签导航 */
.tab-nav {
  display: flex;
  background-color: #FFFFFF;
  height: 90rpx;
  border-bottom: 1rpx solid #F2F2F7;
}
.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
}
.tab-item.active {
  color: #007AFF;
  font-weight: 500;
}
.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #007AFF;
  border-radius: 2rpx;
}
.tab-item .tab-badge {
  min-width: 32rpx;
  height: 32rpx;
  padding: 0 6rpx;
  background-color: #FF3B30;
  color: #FFFFFF;
  font-size: 20rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8rpx;
}

/* 标签内容 */
.tab-content {
  flex: 1;
}
.scroll-view {
  height: 100%;
}

/* 活动列表 */
.activity-list {
  padding: 24rpx;
}
.activity-list .activity-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}
.activity-list .activity-image {
  width: 100%;
  height: 240rpx;
}
.activity-list .activity-info {
  padding: 20rpx;
}
.activity-list .activity-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}
.activity-list .activity-tag {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}
.activity-list .activity-tag.tag-groupon {
  background-color: #FFF3E0;
  color: #FF9500;
}
.activity-list .activity-tag.tag-redpacket {
  background-color: #FFEBEE;
  color: #FF3B30;
}
.activity-list .activity-tag.tag-seckill {
  background-color: #E8F5E9;
  color: #4CD964;
}
.activity-list .activity-tag.tag-coupon {
  background-color: #E3F2FD;
  color: #007AFF;
}
.activity-list .activity-status {
  font-size: 24rpx;
}
.activity-list .activity-status.status-ongoing {
  color: #4CD964;
}
.activity-list .activity-status.status-success {
  color: #007AFF;
}
.activity-list .activity-status.status-failed {
  color: #8E8E93;
}
.activity-list .activity-status.status-used {
  color: #8E8E93;
}
.activity-list .activity-status.status-unused {
  color: #FF9500;
}
.activity-list .activity-status.status-expired {
  color: #8E8E93;
}
.activity-list .activity-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}
.activity-list .activity-shop {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.activity-list .activity-shop .shop-logo {
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  margin-right: 12rpx;
}
.activity-list .activity-shop .shop-name {
  font-size: 26rpx;
  color: #666;
}
.activity-list .activity-time {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #8E8E93;
}
.activity-list .activity-progress {
  margin-bottom: 16rpx;
}
.activity-list .activity-progress .progress-text {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}
.activity-list .activity-progress .progress-bar {
  height: 16rpx;
  background-color: #F2F2F7;
  border-radius: 8rpx;
  overflow: hidden;
}
.activity-list .activity-progress .progress-inner {
  height: 100%;
  background-color: #FF9500;
  border-radius: 8rpx;
}
.activity-list .countdown {
  color: #FF3B30;
}
.activity-list .redpacket-amount {
  display: flex;
  align-items: baseline;
  margin-bottom: 16rpx;
}
.activity-list .redpacket-amount .amount-value {
  font-size: 32rpx;
  color: #FF3B30;
  font-weight: 600;
  margin-right: 12rpx;
}
.activity-list .redpacket-amount .amount-desc {
  font-size: 24rpx;
  color: #8E8E93;
}
.activity-list .validity {
  color: #FF9500;
}

/* 加载更多 */
.load-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 26rpx;
  color: #8E8E93;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-state .empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-state .empty-text {
  font-size: 28rpx;
  color: #8E8E93;
}