# 同城前端分包结构说明

## 分包目录结构

```
同城前端/
├── pages/           # 主包页面
│   ├── index/       # 首页
│   ├── business/    # 商圈
│   ├── my/          # 我的
│   └── ...
├── subPackages/     # 所有分包统一放在此目录下
│   ├── partner/     # 合伙人分包
│   │   ├── pages/   # 合伙人页面
│   │   └── components/  # 合伙人专用组件
│   ├── franchise/   # 区域加盟分包
│   │   ├── pages/   # 加盟页面
│   │   └── components/  # 加盟专用组件
│   └── ...
└── ...
```

## 分包优化说明

### 1. 统一分包目录结构

所有分包统一放在`subPackages`目录下，便于管理和维护。每个分包有自己的`pages`和`components`目录，保持结构清晰。

### 2. 分包配置

在`pages.json`中，使用`subPackages`配置分包：

```json
"subPackages": [
  {
    "root": "subPackages/partner",
    "pages": [
      {
        "path": "pages/partner",
        "style": {
          "navigationStyle": "custom",
          "navigationBarTitleText": "合伙人中心"
        }
      },
      // 其他页面配置...
    ]
  },
  {
    "root": "subPackages/franchise",
    "pages": [
      // 区域加盟相关页面配置...
    ]
  }
]
```

### 3. 预加载配置

在`pages.json`中，使用`preloadRule`配置预加载规则：

```json
"preloadRule": {
  "pages/my/my": {
    "network": "all",
    "packages": ["pages/user-center", "subPackages/partner", "subPackages/franchise"]
  }
}
```

### 4. 分包优势

- **减小主包体积**：将非核心功能拆分为分包，减小主包大小，提高首次启动速度
- **按需加载**：用户只有访问分包内的页面时，才会下载分包内容
- **结构清晰**：统一的目录结构，便于维护和扩展
- **开发解耦**：不同功能模块可以独立开发和测试 