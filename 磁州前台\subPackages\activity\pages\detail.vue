<template>
	<view class="detail-container">
		<image :src="activityInfo.image" class="detail-banner" mode="aspectFill"></image>
		
		<view class="detail-content card-section">
			<view class="detail-header">
				<text class="detail-title">{{activityInfo.title}}</text>
				<view class="detail-status" :class="activityInfo.status === '进行中' ? 'status-active' : 'status-ended'">
					{{activityInfo.status}}
				</view>
			</view>
			
			<view class="detail-meta">
				<view class="meta-item">
					<image src="/static/images/tabbar/时间.png" class="meta-icon"></image>
					<text class="meta-text">{{activityInfo.time}}</text>
				</view>
				<view class="meta-item">
					<image src="/static/images/tabbar/位置.png" class="meta-icon"></image>
					<text class="meta-text">{{activityInfo.location}}</text>
				</view>
			</view>
			
			<view class="detail-section">
				<text class="section-title">活动详情</text>
				<text class="section-content">{{activityInfo.description}}</text>
			</view>
			
			<view class="detail-section">
				<text class="section-title">活动规则</text>
				<view class="rule-list">
					<text class="rule-item" v-for="(rule, index) in activityInfo.rules" :key="index">{{index + 1}}. {{rule}}</text>
				</view>
			</view>
			
			<view class="detail-section">
				<text class="section-title">联系方式</text>
				<view class="contact-info">
					<view class="contact-item">
						<text class="contact-label">主办方：</text>
						<text class="contact-value">{{activityInfo.organizer}}</text>
					</view>
					<view class="contact-item">
						<text class="contact-label">电话：</text>
						<text class="contact-value">{{activityInfo.phone}}</text>
					</view>
				</view>
			</view>
		</view>
		
		<view class="action-bar">
			<button class="share-btn" @click="shareActivity">
				<image src="/static/images/tabbar/分享.png" class="btn-icon"></image>
				<text>分享活动</text>
			</button>
			<button class="join-btn" @click="joinActivity">
				<text>我要参加</text>
			</button>
		</view>
		<fab-buttons />
	</view>
</template>

<script>
	import FabButtons from '@/components/FabButtons.vue'
	export default {
		data() {
			return {
				activityInfo: {
					id: 1,
					title: '磁州美食节',
					image: '/static/images/banner/banner-1.png',
					time: '2024-03-20 至 2024-03-25',
					location: '磁县人民广场',
					description: '汇聚磁州各地特色美食，带您品尝地道磁州味道。活动期间设有美食品鉴、厨艺比拼、文化展示等多个环节，欢迎广大市民朋友参与。',
					status: '进行中',
					rules: [
						'活动时间：每日10:00-22:00',
						'需要提前在线报名或现场登记',
						'请遵守场地秩序，文明参与活动',
						'主办方保留活动最终解释权'
					],
					organizer: '磁县文化旅游局',
					phone: '0310-12345678'
				}
			}
		},
		onLoad(options) {
			// 实际应用中，这里应该根据options.id从服务器获取活动详情
			console.log('活动ID:', options.id)
		},
		methods: {
			shareActivity() {
				uni.showToast({
					title: '分享功能开发中',
					icon: 'none'
				})
			},
			joinActivity() {
				uni.showToast({
					title: '报名成功',
					icon: 'success'
				})
			}
		},
		components: { FabButtons }
	}
</script>

<style>
	.detail-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 120rpx;
	}
	
	.detail-banner {
		width: 100%;
		height: 400rpx;
	}
	
	.detail-content {
		margin: -50rpx 20rpx 0;
		padding: 30rpx;
		background: #fff;
		border-radius: 20rpx;
		position: relative;
		z-index: 1;
	}
	
	.detail-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 30rpx;
	}
	
	.detail-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		flex: 1;
		margin-right: 20rpx;
	}
	
	.detail-status {
		padding: 4rpx 16rpx;
		border-radius: 20rpx;
		font-size: 24rpx;
	}
	
	.status-active {
		background-color: #e6f7ff;
		color: #0052CC;
	}
	
	.status-ended {
		background-color: #f5f5f5;
		color: #999;
	}
	
	.detail-meta {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
		margin-bottom: 30rpx;
	}
	
	.meta-item {
		display: flex;
		align-items: center;
	}
	
	.meta-icon {
		width: 32rpx;
		height: 32rpx;
		margin-right: 12rpx;
	}
	
	.meta-text {
		font-size: 28rpx;
		color: #666;
	}
	
	.detail-section {
		margin-bottom: 30rpx;
	}
	
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 16rpx;
		display: block;
	}
	
	.section-content {
		font-size: 28rpx;
		color: #666;
		line-height: 1.6;
	}
	
	.rule-list {
		display: flex;
		flex-direction: column;
		gap: 12rpx;
	}
	
	.rule-item {
		font-size: 28rpx;
		color: #666;
		line-height: 1.5;
	}
	
	.contact-info {
		display: flex;
		flex-direction: column;
		gap: 12rpx;
	}
	
	.contact-item {
		display: flex;
		align-items: center;
	}
	
	.contact-label {
		font-size: 28rpx;
		color: #333;
		width: 140rpx;
	}
	
	.contact-value {
		font-size: 28rpx;
		color: #666;
	}
	
	.action-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 100rpx;
		background: #fff;
		display: flex;
		align-items: center;
		padding: 0 30rpx;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.share-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 60rpx;
		height: 60rpx;
		background: #f5f5f5;
		border: none;
		border-radius: 50%;
		margin-right: 20rpx;
	}
	
	.btn-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 0;
	}
	
	.join-btn {
		flex: 1;
		height: 80rpx;
		background: linear-gradient(to right, #0052CC, #2196F3);
		color: #fff;
		border: none;
		border-radius: 40rpx;
		font-size: 32rpx;
		font-weight: bold;
	}
	
	/* 继承首页的卡片样式 */
	.card-section {
		box-shadow: 0 8rpx 24rpx rgba(0,82,204,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
	}
</style> 
