{"_from": "semver@^6.3.0", "_id": "semver@6.3.0", "_inBundle": false, "_integrity": "sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==", "_location": "/sass-loader/semver", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "semver@^6.3.0", "name": "semver", "escapedName": "semver", "rawSpec": "^6.3.0", "saveSpec": null, "fetchSpec": "^6.3.0"}, "_requiredBy": ["/sass-loader"], "_resolved": "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz", "_shasum": "ee0a64c8af5e8ceea67687b133761e1becbd1d3d", "_spec": "semver@^6.3.0", "_where": "/Users/<USER>/Documents/DCloud/HbuilderX-plugins/alpha/uniapp-cli/node_modules/sass-loader", "bin": {"semver": "bin/semver.js"}, "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bundleDependencies": false, "deprecated": false, "description": "The semantic version parser used by npm.", "devDependencies": {"tap": "^14.3.1"}, "files": ["bin", "range.bnf", "semver.js"], "homepage": "https://github.com/npm/node-semver#readme", "license": "ISC", "main": "semver.js", "name": "semver", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "scripts": {"postpublish": "git push origin --follow-tags", "postversion": "npm publish", "preversion": "npm test", "test": "tap"}, "tap": {"check-coverage": true}, "version": "6.3.0"}