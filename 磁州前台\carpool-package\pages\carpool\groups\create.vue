<template>
  <view class="create-group-container">
    <!-- 自定义导航栏 -->
    <view style="position: fixed; top: 0; left: 0; right: 0; height: 90px; background: linear-gradient(135deg, #0A84FF, #0040DD); z-index: 100; padding-top: var(--status-bar-height, 40px);">
      <!-- 返回键 - 绝对定位 -->
      <view style="position: absolute; left: 8px; top: calc(var(--status-bar-height, 40px) + 10px); width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;" @click="goBack">
        <image src="/static/images/tabbar/最新返回键.png" style="width: 24px; height: 24px; filter: brightness(0) invert(1);"></image>
      </view>
      
      <!-- 标题 - 绝对定位 -->
      <view style="position: absolute; left: 0; right: 0; top: calc(var(--status-bar-height, 40px) + 15px); text-align: center;">
        <text style="font-size: 18px; color: white; font-weight: bold;">创建拼车群</text>
      </view>
    </view>
    
    <!-- 占位元素确保内容不被导航栏遮挡 -->
    <view style="height: calc(90px + var(--status-bar-height, 40px));"></view>
    
    <!-- 表单内容 -->
    <view class="form-container">
      <view class="form-item">
        <text class="form-label">群名称</text>
        <input class="form-input" type="text" v-model="groupInfo.name" placeholder="请输入群名称" />
      </view>
      
      <view class="form-item">
        <text class="form-label">群描述</text>
        <textarea class="form-textarea" v-model="groupInfo.description" placeholder="请输入群描述，如路线、拼车规则等" />
      </view>
      
      <view class="form-item">
        <text class="form-label">群标签</text>
        <view class="tags-container">
          <view 
            v-for="(tag, index) in tags" 
            :key="index" 
            class="tag-item"
            :class="{ 'selected': selectedTags.includes(tag) }"
            @click="toggleTag(tag)"
          >
            {{tag}}
          </view>
        </view>
      </view>
      
      <view class="form-item">
        <text class="form-label">拼车路线</text>
        <view class="location-selectors">
          <view class="location-input" @click="selectLocation('start')">
            <view class="location-icon start"></view>
            <text class="location-text">{{groupInfo.startLocation || '选择出发地'}}</text>
            <view class="location-arrow">
              <image src="/static/images/tabbar/arrow-right.png" style="width: 16px; height: 16px;"></image>
            </view>
          </view>
          
          <view class="location-divider"></view>
          
          <view class="location-input" @click="selectLocation('end')">
            <view class="location-icon end"></view>
            <text class="location-text">{{groupInfo.endLocation || '选择目的地'}}</text>
            <view class="location-arrow">
              <image src="/static/images/tabbar/arrow-right.png" style="width: 16px; height: 16px;"></image>
            </view>
          </view>
        </view>
      </view>
      
      <view class="form-item">
        <text class="form-label">群二维码</text>
        <view class="qrcode-uploader" @click="uploadQRCode">
          <image v-if="groupInfo.qrcode" :src="groupInfo.qrcode" mode="aspectFit" class="qrcode-preview"></image>
          <view v-else class="upload-placeholder">
            <image src="/static/images/tabbar/plus.png" style="width: 32px; height: 32px;"></image>
            <text>上传群二维码</text>
          </view>
        </view>
      </view>
      
      <view class="form-item">
        <text class="form-label">联系方式</text>
        <input class="form-input" type="text" v-model="groupInfo.contactInfo" placeholder="请输入联系方式（手机号/微信）" />
      </view>
      
      <view class="form-item privacy-section">
        <checkbox-group @change="privacyChanged">
          <label class="privacy-label">
            <checkbox :checked="agreePrivacy" color="#0A84FF" />
            <text>我已阅读并同意</text>
            <text class="privacy-link" @click="showPrivacyPolicy">《拼车群管理规范》</text>
          </label>
        </checkbox-group>
      </view>
      
      <view class="submit-button-area">
        <button class="submit-btn" :disabled="!canSubmit" @click="submitForm">创建拼车群</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 状态变量
const groupInfo = ref({
  name: '',
  description: '',
  startLocation: '',
  endLocation: '',
  qrcode: '',
  contactInfo: ''
});

const tags = ref(['上班族', '学生', '长途', '短途', '每日拼车', '周末拼车', '临时拼车', '商务出行']);
const selectedTags = ref([]);
const agreePrivacy = ref(false);
const isAdmin = ref(false);

// 计算属性：是否可以提交
const canSubmit = computed(() => {
  return groupInfo.value.name && 
         groupInfo.value.description && 
         groupInfo.value.startLocation && 
         groupInfo.value.endLocation && 
         groupInfo.value.qrcode && 
         groupInfo.value.contactInfo && 
         agreePrivacy.value;
});

// 生命周期钩子
onMounted(() => {
  // 检查用户是否为管理员
  const userInfo = uni.getStorageSync('userInfo') || {};
  isAdmin.value = !!userInfo.isAdmin;
  
  if (!isAdmin.value) {
    uni.showModal({
      title: '提示',
      content: '只有管理员可以创建拼车群',
      showCancel: false,
      success: () => {
        uni.navigateBack();
      }
    });
  }
});

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 切换标签选择
const toggleTag = (tag) => {
  if (selectedTags.value.includes(tag)) {
    selectedTags.value = selectedTags.value.filter(item => item !== tag);
  } else {
    if (selectedTags.value.length < 3) {
      selectedTags.value.push(tag);
    } else {
      uni.showToast({
        title: '最多选择3个标签',
        icon: 'none'
      });
    }
  }
};

// 选择位置
const selectLocation = (type) => {
  uni.chooseLocation({
    success: (res) => {
      if (type === 'start') {
        groupInfo.value.startLocation = res.name;
      } else {
        groupInfo.value.endLocation = res.name;
      }
    },
    fail: () => {
      uni.showToast({
        title: '选择位置失败',
        icon: 'none'
      });
    }
  });
};

// 上传群二维码
const uploadQRCode = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      // 上传图片到服务器
      uni.showLoading({
        title: '上传中...'
      });
      
      uni.uploadFile({
        url: 'https://api.example.com/upload', // 替换为实际的上传接口
        filePath: res.tempFilePaths[0],
        name: 'file',
        success: (uploadRes) => {
          try {
            const data = JSON.parse(uploadRes.data);
            if (data.code === 0) {
              groupInfo.value.qrcode = data.data.url;
            } else {
              uni.showToast({
                title: data.msg || '上传失败',
                icon: 'none'
              });
            }
          } catch (e) {
            uni.showToast({
              title: '上传失败',
              icon: 'none'
            });
          }
        },
        fail: () => {
          uni.showToast({
            title: '上传失败',
            icon: 'none'
          });
        },
        complete: () => {
          uni.hideLoading();
        }
      });
    }
  });
};

// 隐私政策变更
const privacyChanged = (e) => {
  agreePrivacy.value = e.detail.value.length > 0;
};

// 显示隐私政策
const showPrivacyPolicy = () => {
  uni.showModal({
    title: '拼车群管理规范',
    content: '1. 群内禁止发布违法信息\n2. 请文明用语，互相尊重\n3. 遵守交通规则，确保安全\n4. 请勿发布虚假信息\n5. 平台有权对违规内容进行处理',
    showCancel: false
  });
};

// 提交表单
const submitForm = () => {
  if (!canSubmit.value) {
    uni.showToast({
      title: '请完成所有必填项',
      icon: 'none'
    });
    return;
  }
  
  if (!isAdmin.value) {
    uni.showToast({
      title: '只有管理员可以创建拼车群',
      icon: 'none'
    });
    return;
  }
  
  // 构建提交数据
  const formData = {
    ...groupInfo.value,
    tags: selectedTags.value
  };
  
  console.log('提交表单数据:', formData);
  
  // 提交到服务器
  uni.showLoading({
    title: '创建中...'
  });
  
  uni.request({
    url: 'https://api.example.com/carpool/groups/create', // 替换为实际的创建接口
    method: 'POST',
    data: formData,
    header: {
      'content-type': 'application/json',
      'Authorization': uni.getStorageSync('token') || ''
    },
    success: (res) => {
      if (res.statusCode === 200 && res.data.code === 0) {
        uni.showToast({
          title: '创建成功',
          icon: 'success'
        });
        
        // 延迟返回
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      } else {
        uni.showToast({
          title: res.data.msg || '创建失败',
          icon: 'none'
        });
      }
    },
    fail: () => {
      uni.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      });
    },
    complete: () => {
      uni.hideLoading();
    }
  });
};
</script>

<style lang="scss">
.create-group-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  padding-bottom: 40rpx;
}

.form-container {
  padding: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.form-label {
  font-size: 28rpx;
  color: #333333;
  font-weight: 600;
  margin-bottom: 16rpx;
  display: block;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background-color: #F8F8F8;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
}

.form-textarea {
  width: 100%;
  height: 180rpx;
  background-color: #F8F8F8;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333333;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
}

.tag-item {
  padding: 10rpx 20rpx;
  background-color: #F2F2F2;
  border-radius: 30rpx;
  margin: 10rpx;
  font-size: 24rpx;
  color: #666666;
}

.tag-item.selected {
  background-color: #E6F2FF;
  color: #0A84FF;
  border: 1px solid #0A84FF;
}

.location-selectors {
  background-color: #F8F8F8;
  border-radius: 8rpx;
  overflow: hidden;
}

.location-input {
  display: flex;
  align-items: center;
  height: 80rpx;
  padding: 0 20rpx;
}

.location-icon {
  width: 16rpx;
  height: 16rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
}

.location-icon.start {
  background-color: #34C759;
}

.location-icon.end {
  background-color: #FF3B30;
}

.location-text {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.location-divider {
  height: 1px;
  background-color: #E5E5E5;
  margin-left: 40rpx;
}

.qrcode-uploader {
  width: 200rpx;
  height: 200rpx;
  border: 1px dashed #CCCCCC;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-placeholder text {
  font-size: 24rpx;
  color: #999999;
  margin-top: 10rpx;
}

.qrcode-preview {
  width: 100%;
  height: 100%;
}

.privacy-section {
  background-color: transparent;
  box-shadow: none;
  padding: 0;
}

.privacy-label {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666666;
}

.privacy-link {
  color: #0A84FF;
  margin-left: 4rpx;
}

.submit-button-area {
  padding: 30rpx;
  margin-top: 40rpx;
}

.submit-btn {
  width: 100%;
  height: 90rpx;
  background: linear-gradient(135deg, #0A84FF, #0066CC);
  border-radius: 45rpx;
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(10, 132, 255, 0.3);
}

.submit-btn[disabled] {
  background: linear-gradient(135deg, #B8B8B8, #999999);
  color: #FFFFFF;
  box-shadow: none;
}

.empty-tip {
  font-size: 28rpx;
  color: #999999;
  margin-top: 20rpx;
}
</style>
