<template>
  <view 
    class="info-item" 
    :class="{
      'topped-item': item.isTopped, 
      'paid-top': item.topType === 'paid',
      'ad-top': item.topType === 'ad'
    }" 
  >
    <!-- 置顶标识 -->
    <view class="top-indicator" v-if="item.isTopped">
      <view class="top-badge" :class="{'paid-badge': item.topType === 'paid', 'ad-badge': item.topType === 'ad'}">
        <view class="top-badge-icon">
          <svg v-if="item.topType === 'paid'" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M16 8l-8 8"></path>
            <path d="M8 8l8 8"></path>
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
          </svg>
        </view>
        <text class="top-badge-text">{{item.topType === 'paid' ? '付费置顶' : '广告置顶'}}</text>
      </view>
    </view>
    
    <!-- 信息状态标签 -->
    <view class="info-status" v-if="item.status">
      <text class="info-status-text">{{item.status}}</text>
    </view>
    
    <!-- 内容区 -->
    <view class="info-content">
      <!-- 头部信息 -->
      <view class="info-header">
        <view class="info-category-wrapper">
          <view class="info-category" :class="[item.isTopped ? 'topped-category' : '', getCategoryClass(item.category)]">
            <text class="category-text">{{item.category}}</text>
          </view>
          <view class="info-time">{{item.time}}</view>
        </view>
      </view>
      
      <!-- 主要内容 -->
      <view class="info-main">
        <view class="info-content-wrapper">
          <text class="info-text" :class="{'bold-text': item.isTopped}">{{item.content}}</text>
          
          <!-- 内容标签 -->
          <view class="info-tags" v-if="item.tags && item.tags.length">
            <view class="info-tag" v-for="(tag, tagIndex) in item.tags" :key="tagIndex">
              <text class="info-tag-text">#{{tag}}</text>
            </view>
          </view>
        </view>
        
        <!-- 图片预览 -->
        <view class="info-images-right" v-if="item.images && item.images.length > 0">
          <view class="info-image-container-right">
            <image :src="item.images[0]" class="info-image" mode="aspectFill"></image>
            <view class="image-count-right" v-if="item.images.length > 1">+{{item.images.length - 1}}</view>
          </view>
        </view>
      </view>
      
      <!-- 核心数据区域 - 根据不同类型显示不同数据 -->
      <view class="core-data-area" v-if="hasKeyData">
        <view class="core-data-item" v-if="item.price">
          <view class="core-data-icon price-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="12" y1="1" x2="12" y2="23"></line>
              <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
            </svg>
          </view>
          <text class="core-data-value price-value">¥{{item.price}}<text class="price-unit" v-if="item.priceUnit">{{item.priceUnit}}</text></text>
        </view>
        
        <view class="core-data-item" v-if="item.area">
          <view class="core-data-icon area-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <circle cx="8.5" cy="8.5" r="1.5"></circle>
              <polyline points="21 15 16 10 5 21"></polyline>
            </svg>
          </view>
          <text class="core-data-value">{{item.area}}㎡</text>
        </view>
        
        <view class="core-data-item" v-if="item.minSalary || item.maxSalary">
          <view class="core-data-icon salary-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
              <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
            </svg>
          </view>
          <text class="core-data-value salary-value">{{formatSalary(item.minSalary, item.maxSalary, item.salaryUnit)}}</text>
        </view>
        
        <view class="core-data-item" v-if="item.location">
          <view class="core-data-icon location-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
              <circle cx="12" cy="10" r="3"></circle>
            </svg>
          </view>
          <text class="core-data-value">{{item.location}}</text>
        </view>
      </view>
      
      <slot name="content"></slot>
      
      <!-- 底部信息 -->
      <view class="info-footer">
        <view class="info-user">
          <view class="user-avatar-container">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="user-avatar-svg">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
          </view>
          <text class="user-name">{{item.author || '匿名用户'}}</text>
        </view>
        
        <view class="info-stats">
          <view class="info-views">
            <view class="view-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
            </view>
            <text class="view-count">{{item.views || 0}}</text>
          </view>
          
          <!-- 红包信息 -->
          <view v-if="item.hasRedPacket" class="info-redpacket">
            <view class="redpacket-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="16" y1="2" x2="16" y2="6"></line>
                <line x1="8" y1="2" x2="8" y2="6"></line>
                <line x1="3" y1="10" x2="21" y2="10"></line>
              </svg>
            </view>
            <text class="redpacket-amount">¥{{item.redPacketAmount}}</text>
            <view class="redpacket-btn">
              <text>抢</text>
            </view>
          </view>
          
          <!-- 互动按钮组 -->
          <view class="info-actions">
            <view class="info-action">
              <view class="info-action-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                </svg>
              </view>
              <text class="info-action-text">{{item.likes || 0}}</text>
            </view>
            
            <view class="info-action">
              <view class="info-action-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
                </svg>
              </view>
              <text class="info-action-text">{{item.comments || 0}}</text>
            </view>
            
            <view class="info-action">
              <view class="info-action-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="18" cy="5" r="3"></circle>
                  <circle cx="6" cy="12" r="3"></circle>
                  <circle cx="18" cy="19" r="3"></circle>
                  <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
                  <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
                </svg>
              </view>
              <text class="info-action-text">分享</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  item: {
    type: Object,
    required: true
  }
});

const hasKeyData = computed(() => {
  const item = props.item;
  return item.price || item.area || (item.minSalary || item.maxSalary) || item.location;
});

function formatSalary(min, max, unit = '月') {
  if (!min && !max) return '面议';
  if (!max) return `${min}K/${unit}以上`;
  if (!min) return `${max}K/${unit}以内`;
  return `${min}-${max}K/${unit}`;
}

function getCategoryClass(category) {
  const categoryMap = {
    '招聘信息': 'category-job',
    '求职信息': 'category-resume',
    '房屋出租': 'category-house-rent',
    '房屋出售': 'category-house-sale',
    '二手闲置': 'category-secondhand',
    '二手车辆': 'category-used-car',
    '到家服务': 'category-home-service',
    '寻找服务': 'category-find-service',
    '生意转让': 'category-business',
    '宠物信息': 'category-pet',
    '商家活动': 'category-merchant',
    '婚恋交友': 'category-dating',
    '车辆服务': 'category-car-service',
    '磁州拼车': 'category-carpool',
    '教育培训': 'category-education',
    '其他服务': 'category-other'
  };
  
  return categoryMap[category] || 'category-other';
}

const emits = defineEmits(['like', 'comment', 'share']);
</script>

<style>
.info-item {
  margin-bottom: 20rpx;
  border-radius: 28rpx;
  background-color: #FFFFFF;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  position: relative;
  border: none;
  overflow: hidden;
  padding: 24rpx 20rpx;
}

.topped-item {
  background: linear-gradient(135deg, #FFFFFF, #FFF9F0);
  border-left: 6rpx solid #FF9500;
}

.paid-top {
  background: linear-gradient(135deg, #FFFFFF, #FFF9F0);
  border-left: 6rpx solid #FF9500;
}

.ad-top {
  background: linear-gradient(135deg, #FFFFFF, #F0F7FF);
  border-left: 6rpx solid #007AFF;
}

.top-indicator {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
}

.top-badge {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-bottom-left-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  transform: translateZ(0);
}

.paid-badge {
  background: linear-gradient(135deg, #FF9500, #FF3B30);
  box-shadow: 0 4rpx 12rpx rgba(255, 59, 48, 0.2);
}

.ad-badge {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}

.top-badge-icon {
  color: #FFFFFF;
  margin-right: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.top-badge-text {
  font-size: 20rpx;
  color: #FFFFFF;
  font-weight: 600;
}

.info-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.info-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.info-category-wrapper {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.info-category {
  background: linear-gradient(135deg, #0062CC, #0091E6);
  border-radius: 10rpx;
  padding: 4rpx 12rpx;
  border: 0.5rpx solid rgba(0, 98, 204, 0.2);
  box-shadow: 0 4rpx 8rpx rgba(0, 98, 204, 0.15);
  transform: translateZ(0);
  margin-right: 12rpx;
  display: inline-flex;
  align-items: center;
}

.category-job {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
}

.category-resume {
  background: linear-gradient(135deg, #5856D6, #AF52DE);
}

.category-house-rent {
  background: linear-gradient(135deg, #FF9500, #FF3B30);
}

.category-house-sale {
  background: linear-gradient(135deg, #FF2D55, #FF3B30);
}

.category-secondhand {
  background: linear-gradient(135deg, #34C759, #30B0C7);
}

.category-used-car {
  background: linear-gradient(135deg, #FF9500, #FFCC00);
}

.category-home-service {
  background: linear-gradient(135deg, #5AC8FA, #007AFF);
}

.category-find-service {
  background: linear-gradient(135deg, #AF52DE, #5856D6);
}

.category-business {
  background: linear-gradient(135deg, #FF2D55, #FF9500);
}

.category-pet {
  background: linear-gradient(135deg, #FFCC00, #FF9500);
}

.category-merchant {
  background: linear-gradient(135deg, #FF3B30, #FF2D55);
}

.category-dating {
  background: linear-gradient(135deg, #FF2D55, #AF52DE);
}

.category-car-service {
  background: linear-gradient(135deg, #34C759, #5AC8FA);
}

.category-carpool {
  background: linear-gradient(135deg, #007AFF, #5856D6);
}

.category-education {
  background: linear-gradient(135deg, #5856D6, #007AFF);
}

.category-other {
  background: linear-gradient(135deg, #8E8E93, #636366);
}

.category-text {
  font-size: 20rpx;
  color: #FFFFFF;
  font-weight: 600;
}

.info-time {
  font-size: 22rpx;
  color: #8E8E93;
  font-weight: 400;
  background-color: rgba(142, 142, 147, 0.08);
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
  margin-left: 8rpx;
}

.info-main {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  margin-top: 8rpx;
  margin-bottom: 12rpx;
  width: 100%;
}

.info-content-wrapper {
  flex: 1;
  overflow: hidden;
  min-width: 0;
}

.info-text {
  font-size: 28rpx;
  color: #1C1C1E;
  line-height: 1.4;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.bold-text {
  font-weight: 600;
  color: #000000;
}

.info-images-right {
  width: 160rpx;
  height: 160rpx;
  flex-shrink: 0;
  position: relative;
  margin-left: 10rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.info-image-container-right {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #f2f2f7;
  position: relative;
}

.info-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 12rpx;
}

.image-count-right {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 18rpx;
  padding: 2rpx 10rpx;
  border-radius: 8rpx;
  z-index: 2;
}

.info-tags {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -4rpx 10rpx;
}

.info-tag {
  background-color: rgba(0, 122, 255, 0.08);
  border-radius: 16rpx;
  padding: 4rpx 12rpx;
  margin: 4rpx;
}

.info-tag-text {
  font-size: 20rpx;
  color: #007AFF;
  font-weight: 500;
}

.info-status {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 16rpx;
  padding: 4rpx 12rpx;
  z-index: 2;
}

.info-status-text {
  font-size: 20rpx;
  color: #FFFFFF;
  font-weight: 600;
}

.core-data-area {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10rpx;
  margin-bottom: 12rpx;
  padding-top: 10rpx;
  border-top: 1rpx solid rgba(60, 60, 67, 0.1);
}

.core-data-item {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.02);
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  margin-right: 10rpx;
  margin-bottom: 8rpx;
}

.core-data-icon {
  color: #007AFF;
  margin-right: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background-color: rgba(0, 122, 255, 0.1);
}

.price-icon {
  background: linear-gradient(135deg, #FF9500, #FF3B30);
}

.area-icon {
  background: linear-gradient(135deg, #34C759, #30B0C7);
}

.salary-icon {
  background: linear-gradient(135deg, #FF3B30, #FF9500);
}

.location-icon {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
}

.core-data-value {
  font-size: 24rpx;
  color: #636366;
  font-weight: 500;
}

.price-value {
  color: #FF3B30;
  font-weight: 700;
}

.price-unit {
  font-size: 18rpx;
  color: #FF3B30;
  font-weight: 500;
  margin-left: 4rpx;
}

.salary-value {
  color: #FF3B30;
  font-weight: 700;
}

.info-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 10rpx;
  border-top: 1rpx solid rgba(60, 60, 67, 0.1);
  position: relative;
  z-index: 1;
}

.info-user {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.02);
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
}

.user-avatar-container {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  margin-right: 8rpx;
  border: 1rpx solid #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 122, 255, 0.1);
  overflow: hidden;
}

.user-avatar-svg {
  width: 20px;
  height: 20px;
  color: #007AFF;
}

.user-name {
  font-size: 22rpx;
  color: #636366;
  font-weight: 500;
}

.info-stats {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
}

.info-views {
  display: flex;
  align-items: center;
  margin-right: 12rpx;
  background: rgba(0, 0, 0, 0.02);
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
}

.view-icon {
  color: #636366;
  margin-right: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-count {
  font-size: 22rpx;
  color: #636366;
  font-weight: 500;
}

.info-redpacket {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #FF3B30, #FF9500);
  border-radius: 20rpx;
  padding: 6rpx 12rpx;
  margin-right: 12rpx;
}

.redpacket-icon {
  color: #FFFFFF;
  margin-right: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.redpacket-amount {
  font-size: 22rpx;
  color: #FFFFFF;
  font-weight: 700;
  margin-right: 6rpx;
}

.redpacket-btn {
  background-color: #FFFFFF;
  border-radius: 50%;
  width: 30rpx;
  height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.redpacket-btn text {
  font-size: 18rpx;
  color: #FF3B30;
  font-weight: 700;
}

.info-actions {
  display: flex;
  margin-left: auto;
}

.info-action {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6rpx 12rpx;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 20rpx;
  margin: 0 4rpx;
}

.info-action-icon {
  color: #8E8E93;
  margin-right: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.info-action-text {
  font-size: 22rpx;
  color: #8E8E93;
  font-weight: 500;
}
</style> 