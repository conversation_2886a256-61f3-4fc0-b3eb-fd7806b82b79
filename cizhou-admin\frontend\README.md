# 磁州生活网后台管理系统 - 前端

基于 Vue 3 + TypeScript + Element Plus 构建的企业级后台管理系统前端。

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- npm >= 8.0.0

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

### 运行测试

```bash
npm run test
```

### 代码检查

```bash
npm run lint
```

## 📁 项目结构

```
src/
├── api/                 # API接口
├── assets/             # 静态资源
├── components/         # 公共组件
├── directives/         # 自定义指令
├── layout/             # 布局组件
├── router/             # 路由配置
├── stores/             # 状态管理
├── styles/             # 样式文件
├── types/              # 类型定义
├── utils/              # 工具函数
├── views/              # 页面组件
├── App.vue             # 根组件
└── main.ts             # 入口文件
```

## 🛠️ 技术栈

- **Vue 3.4+** - 渐进式JavaScript框架
- **TypeScript 5.0+** - JavaScript的超集
- **Vite 5.0+** - 下一代前端构建工具
- **Element Plus 2.4+** - Vue 3组件库
- **Vue Router 4.0+** - Vue.js官方路由
- **Pinia 2.0+** - Vue状态管理
- **Axios** - HTTP客户端
- **ECharts 5.0+** - 数据可视化

## 📋 功能特性

- ✅ 基于Vue 3 Composition API
- ✅ TypeScript支持
- ✅ 响应式设计
- ✅ 暗色主题支持
- ✅ 国际化支持
- ✅ 权限控制
- ✅ 动态路由
- ✅ 组件懒加载
- ✅ 自动化测试
- ✅ ESLint + Prettier

## 🔧 开发规范

### 代码规范

项目使用 ESLint + Prettier 进行代码规范检查和格式化。

### 提交规范

使用 Conventional Commits 规范：

- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 组件开发

- 使用 Composition API
- 组件名使用 PascalCase
- 文件名使用 kebab-case
- 添加适当的类型定义

## 🌐 环境配置

### 开发环境

```bash
# .env.development
VITE_API_BASE_URL=http://localhost:8080
VITE_APP_DEBUG=true
```

### 生产环境

```bash
# .env.production
VITE_API_BASE_URL=https://api.cizhou.com
VITE_APP_DEBUG=false
```

## 📦 构建部署

### 构建

```bash
npm run build
```

构建产物在 `dist` 目录下。

### 部署

可以部署到任何静态文件服务器，如：

- Nginx
- Apache
- Vercel
- Netlify

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。
