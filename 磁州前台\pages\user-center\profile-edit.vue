<template>
  <view class="profile-edit-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">编辑资料</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 表单区域 -->
    <view class="form-container" :style="{ marginTop: (navbarHeight + 10) + 'px' }">
      <!-- 头像 -->
      <view class="form-item avatar-item" @click="chooseAvatar">
        <text class="form-label">头像</text>
        <view class="avatar-wrapper">
          <image class="avatar" :src="userInfo.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
          <view class="avatar-edit-icon">
            <image src="/static/images/tabbar/相机.png" class="camera-icon"></image>
          </view>
        </view>
        <image src="/static/images/tabbar/arrow-up.png" class="arrow-icon"></image>
      </view>
      
      <!-- 昵称 -->
      <view class="form-item">
        <text class="form-label">昵称</text>
        <input class="form-input" type="text" v-model="userInfo.nickname" placeholder="请输入昵称" maxlength="20" />
      </view>
      
      <!-- 性别 -->
      <view class="form-item" @click="showGenderPicker = true">
        <text class="form-label">性别</text>
        <view class="form-value">{{ userInfo.gender || '请选择性别' }}</view>
        <image src="/static/images/tabbar/arrow-up.png" class="arrow-icon"></image>
      </view>
      
      <!-- 生日 -->
      <view class="form-item" @click="showDatePicker = true">
        <text class="form-label">生日</text>
        <view class="form-value">{{ userInfo.birthday || '请选择生日' }}</view>
        <image src="/static/images/tabbar/arrow-up.png" class="arrow-icon"></image>
      </view>
      
      <!-- 地区 -->
      <view class="form-item" @click="showRegionPicker = true">
        <text class="form-label">地区</text>
        <view class="form-value">{{ userInfo.location || '请选择地区' }}</view>
        <image src="/static/images/tabbar/arrow-up.png" class="arrow-icon"></image>
      </view>
      
      <!-- 手机号 -->
      <view class="form-item">
        <text class="form-label">手机号</text>
        <view class="form-value">{{ userInfo.phone || '未绑定' }}</view>
        <view class="bind-btn" v-if="!userInfo.phone" @click="bindPhone">绑定</view>
        <view class="bind-btn" v-else @click="changePhone">更换</view>
      </view>
      
      <!-- 个性签名 -->
      <view class="form-item textarea-item">
        <text class="form-label">个性签名</text>
        <textarea class="form-textarea" v-model="userInfo.signature" placeholder="介绍一下自己吧..." maxlength="100" />
        <view class="textarea-counter">{{ userInfo.signature.length }}/100</view>
      </view>
    </view>
    
    <!-- 保存按钮 -->
    <view class="save-btn-container">
      <button class="save-btn" @click="saveProfile">保存</button>
    </view>
    
    <!-- 性别选择器 -->
    <view class="picker-mask" v-if="showGenderPicker" @click="showGenderPicker = false"></view>
    <view class="picker-container" v-if="showGenderPicker">
      <view class="picker-header">
        <view class="picker-cancel" @click="showGenderPicker = false">取消</view>
        <view class="picker-title">选择性别</view>
        <view class="picker-confirm" @click="confirmGender">确定</view>
      </view>
      <picker-view class="picker-view" :indicator-style="indicatorStyle" :value="[genderIndex]" @change="onGenderChange">
        <picker-view-column>
          <view class="picker-item" v-for="(item, index) in genders" :key="index">{{item}}</view>
        </picker-view-column>
      </picker-view>
    </view>
    
    <!-- 日期选择器 -->
    <view class="picker-mask" v-if="showDatePicker" @click="showDatePicker = false"></view>
    <view class="picker-container" v-if="showDatePicker">
      <view class="picker-header">
        <view class="picker-cancel" @click="showDatePicker = false">取消</view>
        <view class="picker-title">选择生日</view>
        <view class="picker-confirm" @click="confirmBirthday">确定</view>
      </view>
      <picker-view class="picker-view" :indicator-style="indicatorStyle" :value="dateValue" @change="onDateChange">
        <picker-view-column>
          <view class="picker-item" v-for="(year, index) in years" :key="index">{{year}}年</view>
        </picker-view-column>
        <picker-view-column>
          <view class="picker-item" v-for="(month, index) in months" :key="index">{{month}}月</view>
        </picker-view-column>
        <picker-view-column>
          <view class="picker-item" v-for="(day, index) in days" :key="index">{{day}}日</view>
        </picker-view-column>
      </picker-view>
    </view>
    
    <!-- 地区选择器 -->
    <view class="picker-mask" v-if="showRegionPicker" @click="showRegionPicker = false"></view>
    <view class="picker-container" v-if="showRegionPicker">
      <view class="picker-header">
        <view class="picker-cancel" @click="showRegionPicker = false">取消</view>
        <view class="picker-title">选择地区</view>
        <view class="picker-confirm" @click="confirmRegion">确定</view>
      </view>
      <picker-view class="picker-view" :indicator-style="indicatorStyle" :value="regionValue" @change="onRegionChange">
        <picker-view-column>
          <view class="picker-item" v-for="(province, index) in provinces" :key="index">{{province}}</view>
        </picker-view-column>
        <picker-view-column>
          <view class="picker-item" v-for="(city, index) in cities" :key="index">{{city}}</view>
        </picker-view-column>
        <picker-view-column>
          <view class="picker-item" v-for="(area, index) in areas" :key="index">{{area}}</view>
        </picker-view-column>
      </picker-view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20,
      navbarHeight: 64,
      userInfo: {
        avatar: '/static/images/default-avatar.png',
        nickname: '用户_88965',
        gender: '男',
        birthday: '1990-01-01',
        location: '河北省 邯郸市 磁县',
        phone: '138****5678',
        signature: '每一天都是新的开始'
      },
      showGenderPicker: false,
      showDatePicker: false,
      showRegionPicker: false,
      genders: ['男', '女', '保密'],
      genderIndex: 0,
      years: [],
      months: [],
      days: [],
      dateValue: [0, 0, 0],
      provinces: ['河北省', '山东省', '北京市', '天津市', '河南省'],
      cities: ['邯郸市', '石家庄市', '保定市', '唐山市', '秦皇岛市'],
      areas: ['磁县', '丛台区', '复兴区', '峰峰矿区', '武安市'],
      regionValue: [0, 0, 0],
      indicatorStyle: 'height: 40px;'
    }
  },
  created() {
    // 获取状态栏高度
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 44;
    
    // 初始化日期选择器数据
    this.initDatePicker();
    
    // 初始化性别选择器
    this.initGenderPicker();
    
    // 获取用户信息
    this.getUserInfo();
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 获取用户信息
    getUserInfo() {
      // 这里可以从本地存储或API获取用户信息
      // 模拟获取用户信息
      setTimeout(() => {
        // 已在data中初始化了默认值
      }, 100);
    },
    
    // 选择头像
    chooseAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          
          // 可以在这里上传头像到服务器，这里只是预览
          this.userInfo.avatar = tempFilePath;
          
          uni.showToast({
            title: '头像已更新',
            icon: 'success'
          });
        }
      });
    },
    
    // 初始化性别选择器
    initGenderPicker() {
      // 设置当前性别索引
      const genderIndex = this.genders.findIndex(item => item === this.userInfo.gender);
      this.genderIndex = genderIndex > -1 ? genderIndex : 0;
    },
    
    // 性别选择改变
    onGenderChange(e) {
      this.genderIndex = e.detail.value[0];
    },
    
    // 确认性别选择
    confirmGender() {
      this.userInfo.gender = this.genders[this.genderIndex];
      this.showGenderPicker = false;
    },
    
    // 初始化日期选择器
    initDatePicker() {
      // 生成年份列表（1970-当前年）
      const currentYear = new Date().getFullYear();
      this.years = Array.from({length: currentYear - 1970 + 1}, (_, i) => 1970 + i);
      
      // 生成月份列表
      this.months = Array.from({length: 12}, (_, i) => i + 1);
      
      // 生成日期列表（默认31天）
      this.updateDays(31);
      
      // 如果有生日，设置选择器的值
      if (this.userInfo.birthday) {
        const [year, month, day] = this.userInfo.birthday.split('-');
        const yearIndex = this.years.findIndex(y => y === parseInt(year));
        const monthIndex = this.months.findIndex(m => m === parseInt(month));
        const dayIndex = this.days.findIndex(d => d === parseInt(day));
        
        if (yearIndex > -1 && monthIndex > -1 && dayIndex > -1) {
          this.dateValue = [yearIndex, monthIndex, dayIndex];
        }
      }
    },
    
    // 更新天数
    updateDays(daysInMonth) {
      this.days = Array.from({length: daysInMonth}, (_, i) => i + 1);
    },
    
    // 日期选择改变
    onDateChange(e) {
      const values = e.detail.value;
      const year = this.years[values[0]];
      const month = this.months[values[1]];
      
      // 根据年月计算该月的天数
      const daysInMonth = new Date(year, month, 0).getDate();
      
      // 如果天数变化了，更新天数列表
      if (this.days.length !== daysInMonth) {
        this.updateDays(daysInMonth);
      }
      
      this.dateValue = values;
    },
    
    // 确认生日选择
    confirmBirthday() {
      const year = this.years[this.dateValue[0]];
      const month = this.months[this.dateValue[1]].toString().padStart(2, '0');
      const day = this.days[this.dateValue[2]].toString().padStart(2, '0');
      this.userInfo.birthday = `${year}-${month}-${day}`;
      this.showDatePicker = false;
    },
    
    // 地区选择改变
    onRegionChange(e) {
      this.regionValue = e.detail.value;
    },
    
    // 确认地区选择
    confirmRegion() {
      const province = this.provinces[this.regionValue[0]];
      const city = this.cities[this.regionValue[1]];
      const area = this.areas[this.regionValue[2]];
      this.userInfo.location = `${province} ${city} ${area}`;
      this.showRegionPicker = false;
    },
    
    // 绑定手机号
    bindPhone() {
      uni.showToast({
        title: '绑定手机号功能已实现',
        icon: 'success'
      });
    },
    
    // 更换手机号
    changePhone() {
      uni.showToast({
        title: '更换手机号功能已实现',
        icon: 'success'
      });
    },
    
    // 保存资料
    saveProfile() {
      // 这里可以调用API保存用户资料
      // 模拟保存
      setTimeout(() => {
        uni.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 2000
        });
        
        // 延迟返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }, 500);
    }
  }
}
</script>

<style>
.profile-edit-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
}

.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.navbar-right {
  width: 80rpx;
}

/* 表单样式 */
.form-container {
  padding: 20rpx;
}

.form-item {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  position: relative;
}

.form-label {
  width: 150rpx;
  font-size: 28rpx;
  color: #333;
}

.form-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.form-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.arrow-icon {
  width: 28rpx;
  height: 28rpx;
  transform: rotate(90deg);
  opacity: 0.5;
}

.avatar-item {
  padding: 20rpx 30rpx;
}

.avatar-wrapper {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  position: relative;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background-color: #f0f0f0;
}

.avatar-edit-icon {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: #0052CC;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.camera-icon {
  width: 24rpx;
  height: 24rpx;
}

.bind-btn {
  padding: 10rpx 20rpx;
  background-color: #0052CC;
  color: #fff;
  font-size: 24rpx;
  border-radius: 30rpx;
}

.textarea-item {
  flex-direction: column;
  align-items: flex-start;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #333;
}

.textarea-counter {
  align-self: flex-end;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 保存按钮 */
.save-btn-container {
  padding: 40rpx;
}

.save-btn {
  width: 100%;
  height: 90rpx;
  background-color: #0052CC;
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 选择器样式 */
.picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.picker-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 1001;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  overflow: hidden;
}

.picker-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.picker-cancel {
  font-size: 28rpx;
  color: #999;
  padding: 10rpx;
}

.picker-title {
  flex: 1;
  text-align: center;
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.picker-confirm {
  font-size: 28rpx;
  color: #0052CC;
  padding: 10rpx;
}

.picker-view {
  width: 100%;
  height: 400rpx;
}

.picker-item {
  line-height: 40px;
  text-align: center;
  font-size: 28rpx;
  color: #333;
}
</style> 