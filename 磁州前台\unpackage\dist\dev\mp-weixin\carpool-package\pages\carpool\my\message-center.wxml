<view class="message-center-container"><view class="custom-header" style="{{'padding-top:' + c}}"><view class="header-content"><view class="left-action" bindtap="{{b}}"><image src="{{a}}" class="action-icon back-icon"></image></view><view class="title-area"><text class="page-title">消息中心</text></view><view class="right-action"></view></view></view><view class="message-tabs"><view wx:for="{{d}}" wx:for-item="tab" wx:key="c" class="{{['tab-item', tab.d && 'active']}}" bindtap="{{tab.e}}"><text class="tab-text">{{tab.a}}</text><view wx:if="{{tab.b}}" class="active-line"></view></view></view><scroll-view class="scrollable-content" scroll-y bindscrolltolower="{{q}}" refresher-enabled bindrefresherrefresh="{{r}}" refresher-triggered="{{s}}"><view wx:if="{{e}}" class="message-list"><view wx:for="{{f}}" wx:for-item="item" wx:key="e" class="message-item system" bindtap="{{item.f}}"><view wx:if="{{item.a}}" class="unread-indicator"></view><view class="message-icon-wrapper"><image src="{{g}}" mode="aspectFit" class="message-icon"></image></view><view class="message-content"><view class="message-header"><text class="message-title">{{item.b}}</text><text class="message-time">{{item.c}}</text></view><text class="message-brief">{{item.d}}</text></view></view><view wx:if="{{h}}" class="empty-state"><image src="{{i}}" mode="aspectFit" class="empty-image"></image><text class="empty-text">暂无系统消息</text></view></view><view wx:if="{{j}}" class="message-list"><view wx:for="{{k}}" wx:for-item="item" wx:key="i" class="message-item carpool" bindtap="{{item.j}}"><view wx:if="{{item.a}}" class="unread-indicator"></view><view class="message-icon-wrapper"><image src="{{l}}" mode="aspectFit" class="message-icon"></image></view><view class="message-content"><view class="message-header"><text class="message-title">{{item.b}}</text><text class="message-time">{{item.c}}</text></view><text class="message-brief">{{item.d}}</text><view wx:if="{{item.e}}" class="carpool-info"><view class="route-brief"><text class="route-text">{{item.f}} → {{item.g}}</text><text class="departure-time">{{item.h}}</text></view></view></view></view><view wx:if="{{m}}" class="empty-state"><image src="{{n}}" mode="aspectFit" class="empty-image"></image><text class="empty-text">暂无拼车消息</text></view></view><view wx:if="{{o}}" class="loading-state"><text class="loading-text">加载中...</text></view><view wx:if="{{p}}" class="list-bottom"><text class="bottom-text">— 已经到底啦 —</text></view></scroll-view><view wx:if="{{t}}" class="float-clear-btn" bindtap="{{v}}"><text class="float-clear-text">清空消息</text></view><view wx:if="{{w}}" class="popup-mask" bindtap="{{x}}"></view><view wx:if="{{y}}" class="popup-container"><view class="popup-header"><text class="popup-title">{{z}}</text><view class="popup-close" bindtap="{{A}}">×</view></view><scroll-view class="popup-content" scroll-y><view class="message-time-display">{{B}}</view><view class="message-full-content"><text class="content-text">{{C}}</text></view><view wx:if="{{D}}" class="carpool-detail"><view class="detail-title">相关拼车信息</view><view class="route-info"><view class="route-points"><view class="start-point"><view class="point-marker start"></view><text class="point-text">{{E}}</text></view><view class="route-line"></view><view class="end-point"><view class="point-marker end"></view><text class="point-text">{{F}}</text></view></view><view class="trip-info"><view class="info-item"><image src="{{G}}" mode="aspectFit" class="info-icon"></image><text class="info-text">{{H}}</text></view><view class="info-item"><image src="{{I}}" mode="aspectFit" class="info-icon"></image><text class="info-text">{{J}}个座位</text></view><view wx:if="{{K}}" class="info-item"><image src="{{L}}" mode="aspectFit" class="info-icon"></image><text class="info-text price">¥{{M}}/人</text></view></view></view></view></scroll-view><view class="popup-footer"><button class="popup-button delete" bindtap="{{N}}">删除消息</button><button wx:if="{{O}}" class="popup-button confirm" bindtap="{{P}}">查看详情</button></view></view></view>