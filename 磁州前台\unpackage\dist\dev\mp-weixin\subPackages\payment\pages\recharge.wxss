
.recharge-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
}
.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}
.back-icon {
  width: 40rpx;
  height: 40rpx;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}
.navbar-right {
  width: 80rpx;
}

/* 充值金额区域 */
.recharge-section {
  background-color: #fff;
  margin: 30rpx;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}
.section-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}
.amount-input-area {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}
.currency-symbol {
  font-size: 50rpx;
  font-weight: bold;
  margin-right: 20rpx;
}
.amount-input {
  font-size: 60rpx;
  font-weight: bold;
  flex: 1;
}
.amount-quick-select {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.quick-amount-item {
  width: 120rpx;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  background-color: #f8f9fc;
  border-radius: 35rpx;
  margin-bottom: 20rpx;
  font-size: 26rpx;
  color: #333;
  border: 1px solid transparent;
}
.quick-amount-item.selected {
  background-color: #e6f0ff;
  color: #0052CC;
  border: 1px solid #0052CC;
}

/* 支付方式 */
.payment-method {
  background-color: #fff;
  margin: 30rpx;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}
.payment-options {
  margin-top: 20rpx;
}
.payment-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}
.payment-option:last-child {
  border-bottom: none;
}
.payment-left {
  display: flex;
  align-items: center;
}
.payment-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}
.payment-name {
  font-size: 28rpx;
}
.payment-check {
  width: 40rpx;
  height: 40rpx;
}
.check-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 充值说明 */
.recharge-notice {
  margin: 30rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}
.notice-title {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
}
.notice-item {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
  line-height: 1.5;
}

/* 底部按钮区域 */
.bottom-btn-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.recharge-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #0052CC;
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
}
.btn-disabled {
  background-color: #cccccc;
  color: #ffffff;
}
