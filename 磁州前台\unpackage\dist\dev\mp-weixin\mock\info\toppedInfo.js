"use strict";
const toppedInfoList = [
  {
    id: "topped-job-1",
    category: "招聘信息",
    content: "急招会计1名，五险一金，双休，2年以上工作经验，薪资4500-6000",
    time: "2024-05-15 14:30",
    views: 208,
    pageType: "job-detail",
    isTopped: true,
    topType: "paid",
    topExpiry: "2024-05-20"
  },
  {
    id: "topped-house-1",
    category: "房屋出租",
    content: "市中心精装两室一厅出租，家电齐全，拎包入住，交通便利，周边配套设施完善",
    time: "2024-05-14 10:20",
    views: 197,
    pageType: "house-rent-detail",
    isTopped: true,
    topType: "ad",
    topExpiry: "2024-05-22",
    images: ["/static/images/tabbar/wxacode.jpg", "/static/images/tabbar/wxacode.jpg"]
  },
  {
    id: "topped-service-1",
    category: "到家服务",
    content: "专业家政服务，保洁、月嫂、育儿嫂、老人陪护，持证上岗，服务优质",
    time: "2024-05-15 16:20",
    views: 178,
    pageType: "home-service-detail",
    isTopped: true,
    topType: "paid",
    topExpiry: "2024-05-25"
  },
  {
    id: "topped-business-1",
    category: "生意转让",
    content: "繁华商圈饭店转让，160平米，接手即可营业，因家中有事急转",
    time: "2024-05-16 09:15",
    views: 255,
    pageType: "business-transfer-detail",
    isTopped: true,
    topType: "paid",
    topExpiry: "2024-05-23"
  },
  {
    id: "topped-car-1",
    category: "二手车辆",
    content: "2022年丰田凯美瑞2.5L混动，行驶1.2万公里，无事故无泡水，可分期",
    time: "2024-05-14 11:30",
    views: 221,
    pageType: "car-detail",
    isTopped: true,
    topType: "ad",
    topExpiry: "2024-05-21",
    images: ["/static/images/tabbar/wxacode.jpg"]
  }
];
const fetchToppedInfo = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(toppedInfoList);
    }, 300);
  });
};
exports.fetchToppedInfo = fetchToppedInfo;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/mock/info/toppedInfo.js.map
