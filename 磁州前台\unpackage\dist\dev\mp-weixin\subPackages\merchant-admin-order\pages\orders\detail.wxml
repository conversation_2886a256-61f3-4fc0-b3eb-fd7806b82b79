<view class="order-detail-container"><view class="navbar"><view class="navbar-back" bindtap="{{a}}"><view class="back-icon"></view></view><text class="navbar-title">订单详情</text><view class="navbar-right"><view class="more-icon">⋮</view></view></view><view class="{{['status-card', e]}}"><view class="status-header"><text class="status-text">{{b}}</text><text class="status-desc">{{c}}</text></view><view class="status-timeline"><view wx:for="{{d}}" wx:for-item="step" wx:key="d" class="{{['timeline-item', step.e && 'completed', step.f && 'current']}}"><view class="timeline-dot"></view><view class="timeline-content"><text class="timeline-title">{{step.a}}</text><text class="timeline-time">{{step.b}}</text></view><view wx:if="{{step.c}}" class="timeline-line"></view></view></view></view><view class="info-card"><view class="card-title">基本信息</view><view class="info-item"><text class="info-label">订单编号</text><text class="info-value">{{f}}</text></view><view class="info-item"><text class="info-label">下单时间</text><text class="info-value">{{g}}</text></view><view class="info-item"><text class="info-label">支付方式</text><text class="info-value">{{h}}</text></view><view class="info-item"><text class="info-label">支付状态</text><text class="{{['info-value', j]}}">{{i}}</text></view></view><view class="info-card"><view class="card-title">客户信息</view><view class="customer-profile"><image class="customer-avatar" src="{{k}}" mode="aspectFill"></image><view class="customer-info"><text class="customer-name">{{l}}</text><text class="customer-id">ID: {{m}}</text></view><view class="contact-btn" bindtap="{{n}}">联系客户</view></view><view wx:if="{{o}}" class="address-info"><view class="address-title"><text class="address-icon">📍</text><text>收货地址</text></view><view class="address-content"><text class="address-name">{{p}} {{q}}</text><text class="address-detail">{{r}}{{s}}{{t}}{{v}}</text></view></view></view><view class="info-card"><view class="card-title">商品信息</view><view wx:for="{{w}}" wx:for-item="item" wx:key="f" class="product-item"><image class="product-image" src="{{item.a}}" mode="aspectFill"></image><view class="product-info"><text class="product-name">{{item.b}}</text><text class="product-spec">{{item.c}}</text><view class="product-price-wrap"><text class="product-price">¥{{item.d}}</text><text class="product-quantity">x{{item.e}}</text></view></view></view><view class="amount-info"><view class="amount-item"><text class="amount-label">商品总价</text><text class="amount-value">¥{{x}}</text></view><view class="amount-item"><text class="amount-label">配送费</text><text class="amount-value">¥{{y}}</text></view><view wx:if="{{z}}" class="amount-item discount"><text class="amount-label">优惠金额</text><text class="amount-value">-¥{{A}}</text></view><view class="amount-item total"><text class="amount-label">订单总价</text><text class="amount-value">¥{{B}}</text></view></view></view><view wx:if="{{C}}" class="info-card"><view class="card-title">订单备注</view><view class="remark-content">{{D}}</view></view><view class="info-card"><view class="card-title">订单日志</view><view wx:for="{{E}}" wx:for-item="log" wx:key="c" class="log-item"><text class="log-time">{{log.a}}</text><text class="log-content">{{log.b}}</text></view></view><view class="action-bar"><view wx:if="{{F}}" class="action-btn primary" bindtap="{{G}}">接单处理</view><view wx:if="{{H}}" class="action-btn primary" bindtap="{{I}}">完成订单</view><view wx:if="{{J}}" class="action-btn" bindtap="{{K}}">取消订单</view><view class="action-btn" bindtap="{{L}}">打印订单</view></view></view>