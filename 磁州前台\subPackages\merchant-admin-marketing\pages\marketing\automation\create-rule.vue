<!-- 创建自动化规则页面开始 -->
<template>
  <view class="create-rule-page">
    <u-navbar
      title="创建自动化规则"
      :border="false"
      :autoBack="false"
      bgColor="#6236FF"
      titleStyle="color: #ffffff; font-weight: 500;"
      leftIcon="arrow-left"
      leftIconColor="#ffffff"
      @leftClick="goBack"
    ></u-navbar>
    
    <view class="content">
      <view class="form-card">
        <view class="form-group">
          <view class="form-label">规则名称</view>
          <u-input
            v-model="ruleForm.name"
            placeholder="请输入规则名称"
            border="bottom"
            :clearable="true"
          ></u-input>
        </view>
        
        <view class="form-group">
          <view class="form-label">触发条件</view>
          <view class="trigger-selector" @click="showTriggerPicker = true">
            <view class="selector-value" :class="{ placeholder: !ruleForm.trigger.type }">
              {{ getTriggerText(ruleForm.trigger) || '请选择触发条件' }}
            </view>
            <u-icon name="arrow-right" color="#999999" size="24"></u-icon>
          </view>
        </view>
        
        <!-- 条件设置区域，根据选择的触发条件类型显示不同的设置项 -->
        <view class="trigger-settings" v-if="ruleForm.trigger.type">
          <!-- 用户注册触发 -->
          <view v-if="ruleForm.trigger.type === 'register'">
            <view class="settings-title">注册后立即触发</view>
            <view class="settings-desc">用户完成注册后立即执行后续动作</view>
          </view>
          
          <!-- 下单未支付触发 -->
          <view v-if="ruleForm.trigger.type === 'unpaidOrder'">
            <view class="settings-title">下单未支付提醒</view>
            <view class="form-row">
              <view class="label">触发延迟</view>
              <view class="value-selector">
                <u-number-box
                  v-model="ruleForm.trigger.timeDelay"
                  :min="5"
                  :max="120"
                  :step="5"
                ></u-number-box>
                <text class="unit">分钟</text>
              </view>
            </view>
          </view>
          
          <!-- 会员生日触发 -->
          <view v-if="ruleForm.trigger.type === 'birthday'">
            <view class="settings-title">会员生日提醒</view>
            <view class="form-row">
              <view class="label">提前天数</view>
              <view class="value-selector">
                <u-number-box
                  v-model="ruleForm.trigger.daysBefore"
                  :min="0"
                  :max="30"
                  :step="1"
                ></u-number-box>
                <text class="unit">天</text>
              </view>
            </view>
            <view class="form-row">
              <view class="label">会员等级</view>
              <view class="value-selector">
                <u-input
                  v-model="ruleForm.trigger.conditions[0].value"
                  type="number"
                  placeholder="最低等级"
                  border="surround"
                  :clearable="true"
                ></u-input>
              </view>
            </view>
          </view>
        </view>
        
        <view class="form-group">
          <view class="form-label">执行动作</view>
          <view class="action-selector" @click="showActionPicker = true">
            <view class="selector-value" :class="{ placeholder: !ruleForm.action.type }">
              {{ getActionText(ruleForm.action) || '请选择执行动作' }}
            </view>
            <u-icon name="arrow-right" color="#999999" size="24"></u-icon>
          </view>
        </view>
        
        <!-- 动作设置区域，根据选择的动作类型显示不同的设置项 -->
        <view class="action-settings" v-if="ruleForm.action.type">
          <!-- 发送优惠券 -->
          <view v-if="ruleForm.action.type === 'coupon'">
            <view class="settings-title">发送优惠券</view>
            <view class="coupon-selector" @click="selectCoupon">
              <view class="selector-value" :class="{ placeholder: !ruleForm.action.couponId }">
                {{ ruleForm.action.couponName || '请选择优惠券' }}
              </view>
              <u-icon name="arrow-right" color="#999999" size="24"></u-icon>
            </view>
          </view>
          
          <!-- 发送消息通知 -->
          <view v-if="ruleForm.action.type === 'message'">
            <view class="settings-title">发送消息通知</view>
            <view class="form-row">
              <view class="label">消息类型</view>
              <view class="value-selector">
                <u-radio-group v-model="ruleForm.action.messageType">
                  <u-radio label="短信通知" name="sms"></u-radio>
                  <u-radio label="站内消息" name="app"></u-radio>
                </u-radio-group>
              </view>
            </view>
            <view class="form-row" v-if="ruleForm.action.messageType === 'sms'">
              <view class="label">消息模板</view>
              <view class="template-selector" @click="selectSmsTemplate">
                <view class="selector-value" :class="{ placeholder: !ruleForm.action.templateId }">
                  {{ ruleForm.action.templateId || '请选择短信模板' }}
                </view>
                <u-icon name="arrow-right" color="#999999" size="24"></u-icon>
              </view>
            </view>
          </view>
          
          <!-- 赠送积分 -->
          <view v-if="ruleForm.action.type === 'points'">
            <view class="settings-title">赠送积分</view>
            <view class="form-row">
              <view class="label">积分数量</view>
              <view class="value-selector">
                <u-input
                  v-model="ruleForm.action.points"
                  type="number"
                  placeholder="请输入积分数量"
                  border="surround"
                  :clearable="true"
                ></u-input>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 提交按钮 -->
      <view class="submit-section">
        <u-button
          type="primary"
          text="保存规则"
          :disabled="!isFormValid"
          @click="saveRule"
          :customStyle="{ width: '100%' }"
        ></u-button>
      </view>
    </view>
    
    <!-- 触发条件选择器弹窗 -->
    <u-popup :show="showTriggerPicker" mode="bottom" @close="showTriggerPicker = false">
      <view class="picker-header">
        <text>选择触发条件</text>
        <u-icon name="close" color="#999999" size="28" @click="showTriggerPicker = false"></u-icon>
      </view>
      <view class="picker-content">
        <view
          class="picker-item"
          v-for="(item, index) in triggerOptions"
          :key="index"
          @click="selectTrigger(item)"
        >
          <view class="item-icon" :style="{ backgroundColor: item.bgColor }">
            <u-icon :name="item.icon" color="#ffffff" size="24"></u-icon>
          </view>
          <view class="item-info">
            <view class="item-name">{{ item.name }}</view>
            <view class="item-desc">{{ item.desc }}</view>
          </view>
          <u-icon name="checkmark" color="#6236FF" size="20" v-if="ruleForm.trigger.type === item.value"></u-icon>
        </view>
      </view>
    </u-popup>
    
    <!-- 执行动作选择器弹窗 -->
    <u-popup :show="showActionPicker" mode="bottom" @close="showActionPicker = false">
      <view class="picker-header">
        <text>选择执行动作</text>
        <u-icon name="close" color="#999999" size="28" @click="showActionPicker = false"></u-icon>
      </view>
      <view class="picker-content">
        <view
          class="picker-item"
          v-for="(item, index) in actionOptions"
          :key="index"
          @click="selectAction(item)"
        >
          <view class="item-icon" :style="{ backgroundColor: item.bgColor }">
            <u-icon :name="item.icon" color="#ffffff" size="24"></u-icon>
          </view>
          <view class="item-info">
            <view class="item-name">{{ item.name }}</view>
            <view class="item-desc">{{ item.desc }}</view>
          </view>
          <u-icon name="checkmark" color="#6236FF" size="20" v-if="ruleForm.action.type === item.value"></u-icon>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 规则表单数据
      ruleForm: {
        name: '',
        trigger: {
          type: '',
          timeDelay: 30,
          daysBefore: 0,
          conditions: [{ field: 'memberLevel', operator: 'gte', value: 2 }]
        },
        action: {
          type: '',
          couponId: '',
          couponName: '',
          messageType: 'sms',
          templateId: '',
          points: 100
        }
      },
      
      // 触发条件选择器
      showTriggerPicker: false,
      triggerOptions: [
        {
          name: '用户注册',
          value: 'register',
          desc: '用户完成注册后立即触发',
          icon: 'account',
          bgColor: '#6236FF'
        },
        {
          name: '下单未支付',
          value: 'unpaidOrder',
          desc: '用户下单一段时间后仍未支付时触发',
          icon: 'shopping-cart',
          bgColor: '#FF9500'
        },
        {
          name: '会员生日',
          value: 'birthday',
          desc: '会员生日当天或提前几天触发',
          icon: 'gift',
          bgColor: '#FF2D55'
        }
      ],
      
      // 执行动作选择器
      showActionPicker: false,
      actionOptions: [
        {
          name: '发送优惠券',
          value: 'coupon',
          desc: '向目标用户发送指定优惠券',
          icon: 'coupon',
          bgColor: '#6236FF'
        },
        {
          name: '发送消息',
          value: 'message',
          desc: '向目标用户发送短信或应用内消息',
          icon: 'message',
          bgColor: '#34C759'
        },
        {
          name: '赠送积分',
          value: 'points',
          desc: '向目标用户赠送指定数量积分',
          icon: 'star',
          bgColor: '#FF9500'
        }
      ]
    }
  },
  computed: {
    // 表单是否有效
    isFormValid() {
      return (
        this.ruleForm.name && 
        this.ruleForm.trigger.type && 
        this.ruleForm.action.type && 
        this.validateActionSettings()
      )
    }
  },
  methods: {
    // 选择触发条件
    selectTrigger(item) {
      // 如果选择不同的触发类型，重置触发条件相关设置
      if (this.ruleForm.trigger.type !== item.value) {
        this.ruleForm.trigger = {
          type: item.value,
          timeDelay: 30,
          daysBefore: 0,
          conditions: [{ field: 'memberLevel', operator: 'gte', value: 2 }]
        }
      }
      
      this.showTriggerPicker = false
    },
    
    // 选择执行动作
    selectAction(item) {
      // 如果选择不同的动作类型，重置动作相关设置
      if (this.ruleForm.action.type !== item.value) {
        this.ruleForm.action = {
          type: item.value,
          couponId: '',
          couponName: '',
          messageType: 'sms',
          templateId: '',
          points: 100
        }
      }
      
      this.showActionPicker = false
    },
    
    // 选择优惠券
    selectCoupon() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin/pages/marketing/coupon/select-coupon?mode=select',
        events: {
          // 监听选择优惠券页面返回的数据
          selectCoupon: (coupon) => {
            this.ruleForm.action.couponId = coupon.id
            this.ruleForm.action.couponName = coupon.name
          }
        }
      })
    },
    
    // 选择短信模板
    selectSmsTemplate() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin/pages/marketing/message/select-template?type=sms',
        events: {
          // 监听选择模板页面返回的数据
          selectTemplate: (template) => {
            this.ruleForm.action.templateId = template.id
          }
        }
      })
    },
    
    // 验证动作设置是否完整
    validateActionSettings() {
      if (this.ruleForm.action.type === 'coupon') {
        return !!this.ruleForm.action.couponId
      } else if (this.ruleForm.action.type === 'message' && this.ruleForm.action.messageType === 'sms') {
        return !!this.ruleForm.action.templateId
      } else if (this.ruleForm.action.type === 'points') {
        return !!this.ruleForm.action.points
      }
      
      return true
    },
    
    // 获取触发条件文本描述
    getTriggerText(trigger) {
      if (!trigger.type) return ''
      
      switch (trigger.type) {
        case 'register':
          return '用户完成注册'
        case 'unpaidOrder':
          return `用户下单未支付 ${trigger.timeDelay} 分钟后`
        case 'birthday':
          return trigger.daysBefore > 0 
            ? `用户生日前 ${trigger.daysBefore} 天` 
            : '用户生日当天'
        default:
          return '未知触发条件'
      }
    },
    
    // 获取执行动作文本描述
    getActionText(action) {
      if (!action.type) return ''
      
      switch (action.type) {
        case 'coupon':
          return action.couponName 
            ? `发送优惠券: ${action.couponName}` 
            : '发送优惠券'
        case 'message':
          return `发送${action.messageType === 'sms' ? '短信' : '消息'}通知`
        case 'points':
          return `赠送 ${action.points} 积分`
        default:
          return '未知执行动作'
      }
    },
    
    // 保存规则
    saveRule() {
      if (!this.isFormValid) return
      
      uni.showLoading({ title: '保存中...' })
      
      // 模拟API调用
      setTimeout(() => {
        uni.hideLoading()
        
        uni.showToast({
          title: '规则创建成功',
          icon: 'success'
        })
        
        // 返回列表页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }, 1000)
    },
    
    // 自定义返回按钮
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.create-rule-page {
  min-height: 100vh;
  background-color: #f5f6fa;
  
  .content {
    padding: 30rpx;
  }
  
  .form-card {
    background-color: #ffffff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    
    .form-group {
      margin-bottom: 40rpx;
      
      .form-label {
        font-size: 30rpx;
        color: #333333;
        font-weight: 500;
        margin-bottom: 20rpx;
      }
      
      .trigger-selector, .action-selector, .coupon-selector, .template-selector {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 90rpx;
        padding: 0 20rpx;
        background-color: #f5f6fa;
        border-radius: 8rpx;
        
        .selector-value {
          font-size: 28rpx;
          color: #333333;
          
          &.placeholder {
            color: #999999;
          }
        }
      }
    }
    
    .trigger-settings, .action-settings {
      background-color: #f9f9f9;
      border-radius: 12rpx;
      padding: 30rpx;
      margin-bottom: 40rpx;
      
      .settings-title {
        font-size: 28rpx;
        color: #333333;
        font-weight: 500;
        margin-bottom: 10rpx;
      }
      
      .settings-desc {
        font-size: 24rpx;
        color: #999999;
        margin-bottom: 20rpx;
      }
      
      .form-row {
        display: flex;
        align-items: center;
        margin-top: 20rpx;
        
        .label {
          width: 180rpx;
          font-size: 26rpx;
          color: #666666;
        }
        
        .value-selector {
          flex: 1;
          display: flex;
          align-items: center;
          
          .unit {
            margin-left: 10rpx;
            font-size: 26rpx;
            color: #666666;
          }
        }
      }
    }
  }
  
  .submit-section {
    padding: 30rpx 0;
  }
  
  .picker-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 2rpx solid #f5f5f5;
    
    text {
      font-size: 30rpx;
      color: #333333;
      font-weight: 500;
    }
  }
  
  .picker-content {
    max-height: 800rpx;
    overflow-y: auto;
    padding: 20rpx 30rpx;
    
    .picker-item {
      display: flex;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 2rpx solid #f5f5f5;
      
      &:last-child {
        border-bottom: none;
      }
      
      .item-icon {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20rpx;
      }
      
      .item-info {
        flex: 1;
        
        .item-name {
          font-size: 28rpx;
          color: #333333;
          font-weight: 500;
          margin-bottom: 6rpx;
        }
        
        .item-desc {
          font-size: 24rpx;
          color: #999999;
        }
      }
    }
  }
}
</style>
<!-- 创建自动化规则页面结束 -->
