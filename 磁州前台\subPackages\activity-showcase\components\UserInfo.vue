<template>
  <view class="user-info-container" :class="[`theme-${theme}`, { compact: isCompact }]">
    <!-- 用户头像和基本信息 -->
    <view class="user-basic-info">
      <view class="avatar-container" @click="handleAvatarClick">
        <image 
          class="user-avatar" 
          :src="userInfo.avatar || defaultAvatar" 
          mode="aspectFill"
          @error="handleAvatarError"
        ></image>
        <view class="avatar-badge" v-if="userInfo.vipLevel > 0">
          <text class="badge-text">VIP{{ userInfo.vipLevel }}</text>
        </view>
        <view class="online-status" v-if="showOnlineStatus" :class="{ online: userInfo.isOnline }"></view>
      </view>
      
      <view class="user-details">
        <view class="user-name-row">
          <text class="user-name">{{ userInfo.name || '未设置昵称' }}</text>
          <view class="user-badges" v-if="userInfo.badges && userInfo.badges.length > 0">
            <view 
              class="badge-item" 
              v-for="(badge, index) in userInfo.badges" 
              :key="index"
              :class="badge.type"
            >
              <text class="badge-text">{{ badge.text }}</text>
            </view>
          </view>
        </view>
        
        <text class="user-desc" v-if="userInfo.description">{{ userInfo.description }}</text>
        
        <view class="user-stats" v-if="showStats && userInfo.stats">
          <view class="stat-item" v-for="(stat, key) in userInfo.stats" :key="key">
            <text class="stat-value">{{ stat.value }}</text>
            <text class="stat-label">{{ stat.label }}</text>
          </view>
        </view>
      </view>
      
      <view class="user-actions" v-if="showActions">
        <view class="action-btn" @click="handleFollow" v-if="showFollowBtn">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
            <circle cx="8.5" cy="7" r="4"></circle>
            <line x1="20" y1="8" x2="20" y2="14"></line>
            <line x1="23" y1="11" x2="17" y2="11"></line>
          </svg>
          <text class="action-text">{{ userInfo.isFollowed ? '已关注' : '关注' }}</text>
        </view>
        
        <view class="action-btn" @click="handleMessage" v-if="showMessageBtn">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
          </svg>
          <text class="action-text">私信</text>
        </view>
      </view>
    </view>
    
    <!-- 扩展信息 -->
    <view class="user-extended-info" v-if="showExtendedInfo && userInfo.extendedInfo">
      <view class="info-section" v-for="(section, index) in userInfo.extendedInfo" :key="index">
        <text class="section-title">{{ section.title }}</text>
        <view class="section-content">
          <view 
            class="info-item" 
            v-for="(item, itemIndex) in section.items" 
            :key="itemIndex"
          >
            <text class="item-label">{{ item.label }}</text>
            <text class="item-value">{{ item.value }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'

// Props定义
const props = defineProps({
  // 用户信息对象
  userInfo: {
    type: Object,
    default: () => ({
      id: null,
      name: '',
      avatar: '',
      description: '',
      vipLevel: 0,
      isOnline: false,
      isFollowed: false,
      badges: [],
      stats: null,
      extendedInfo: null
    })
  },
  // 主题样式
  theme: {
    type: String,
    default: 'default', // default, dark, light, colorful
    validator: (value) => ['default', 'dark', 'light', 'colorful'].includes(value)
  },
  // 是否紧凑模式
  isCompact: {
    type: Boolean,
    default: false
  },
  // 是否显示在线状态
  showOnlineStatus: {
    type: Boolean,
    default: false
  },
  // 是否显示统计信息
  showStats: {
    type: Boolean,
    default: true
  },
  // 是否显示操作按钮
  showActions: {
    type: Boolean,
    default: true
  },
  // 是否显示关注按钮
  showFollowBtn: {
    type: Boolean,
    default: true
  },
  // 是否显示私信按钮
  showMessageBtn: {
    type: Boolean,
    default: true
  },
  // 是否显示扩展信息
  showExtendedInfo: {
    type: Boolean,
    default: false
  }
})

// Emits定义
const emit = defineEmits([
  'avatar-click',
  'follow',
  'message',
  'user-click'
])

// 响应式数据
const defaultAvatar = ref('/static/images/avatar/default.png')

// 方法
function handleAvatarClick() {
  emit('avatar-click', props.userInfo)
}

function handleAvatarError() {
  // 头像加载失败时的处理
  console.log('头像加载失败')
}

function handleFollow() {
  emit('follow', props.userInfo)
}

function handleMessage() {
  emit('message', props.userInfo)
}
</script>

<style scoped>
/* 用户信息组件样式开始 */
.user-info-container {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.user-info-container.compact {
  padding: 12px;
}

/* 主题样式 */
.user-info-container.theme-dark {
  background: #2c2c2c;
  color: white;
}

.user-info-container.theme-light {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
}

.user-info-container.theme-colorful {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

/* 用户基本信息 */
.user-basic-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.avatar-container {
  position: relative;
  flex-shrink: 0;
}

.user-avatar {
  width: 56px;
  height: 56px;
  border-radius: 28px;
  border: 2px solid #f0f0f0;
}

.compact .user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
}

.avatar-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  padding: 2px 6px;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-radius: 8px;
  border: 2px solid white;
}

.avatar-badge .badge-text {
  font-size: 10px;
  color: #333;
  font-weight: 600;
}

.online-status {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  border-radius: 6px;
  background: #ccc;
  border: 2px solid white;
}

.online-status.online {
  background: #4CAF50;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.theme-dark .user-name,
.theme-colorful .user-name {
  color: white;
}

.user-badges {
  display: flex;
  gap: 4px;
}

.badge-item {
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
}

.badge-item.vip {
  background: #FFD700;
  color: #333;
}

.badge-item.verified {
  background: #4CAF50;
  color: white;
}

.badge-item.premium {
  background: #E91E63;
  color: white;
}

.user-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.theme-dark .user-desc,
.theme-colorful .user-desc {
  color: rgba(255, 255, 255, 0.8);
}

.user-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.theme-dark .stat-value,
.theme-colorful .stat-value {
  color: white;
}

.stat-label {
  font-size: 10px;
  color: #666;
}

.theme-dark .stat-label,
.theme-colorful .stat-label {
  color: rgba(255, 255, 255, 0.7);
}

.user-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex-shrink: 0;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: #f5f5f5;
  border-radius: 16px;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.95);
}

.theme-dark .action-btn {
  background: rgba(255, 255, 255, 0.1);
}

.theme-colorful .action-btn {
  background: rgba(255, 255, 255, 0.2);
}

.action-btn svg {
  color: #666;
}

.theme-dark .action-btn svg,
.theme-colorful .action-btn svg {
  color: white;
}

.action-text {
  font-size: 12px;
  color: #666;
}

.theme-dark .action-text,
.theme-colorful .action-text {
  color: white;
}

/* 扩展信息 */
.user-extended-info {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.theme-dark .user-extended-info {
  border-top-color: rgba(255, 255, 255, 0.1);
}

.theme-colorful .user-extended-info {
  border-top-color: rgba(255, 255, 255, 0.2);
}

.info-section {
  margin-bottom: 12px;
}

.info-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.theme-dark .section-title,
.theme-colorful .section-title {
  color: white;
}

.section-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-label {
  font-size: 12px;
  color: #666;
}

.theme-dark .item-label,
.theme-colorful .item-label {
  color: rgba(255, 255, 255, 0.7);
}

.item-value {
  font-size: 12px;
  color: #333;
  font-weight: 500;
}

.theme-dark .item-value,
.theme-colorful .item-value {
  color: white;
}

/* 紧凑模式调整 */
.compact .user-basic-info {
  gap: 8px;
}

.compact .user-name {
  font-size: 14px;
}

.compact .user-desc {
  font-size: 11px;
}

.compact .stat-value {
  font-size: 12px;
}

.compact .stat-label {
  font-size: 9px;
}

.compact .action-btn {
  padding: 4px 8px;
}

.compact .action-text {
  font-size: 11px;
}
/* 用户信息组件样式结束 */
</style>
