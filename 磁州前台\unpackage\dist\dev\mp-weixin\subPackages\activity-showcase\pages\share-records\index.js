"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_rect = common_vendor.resolveComponent("rect");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_uni_popup = common_vendor.resolveComponent("uni-popup");
  (_component_path + _component_svg + _component_rect + _component_line + _component_circle + _component_uni_popup)();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const currentTab = common_vendor.ref(0);
    const isRefreshing = common_vendor.ref(false);
    const recordsList = common_vendor.ref([]);
    const filterPopup = common_vendor.ref(null);
    const shareStats = common_vendor.ref({
      totalShares: 56,
      totalViews: 2348,
      totalRegistrations: 32,
      totalCommission: "¥328.50"
    });
    const selectedTimeOption = common_vendor.ref(0);
    const selectedPlatformOptions = common_vendor.ref([0]);
    const selectedContentTypeOptions = common_vendor.ref([0]);
    const timeOptions = [
      { label: "全部时间", value: "all" },
      { label: "最近一周", value: "week" },
      { label: "最近一月", value: "month" },
      { label: "最近三月", value: "three_months" },
      { label: "自定义", value: "custom" }
    ];
    const platformOptions = [
      { label: "全部平台", value: "all" },
      { label: "微信", value: "wechat" },
      { label: "朋友圈", value: "moments" },
      { label: "微博", value: "weibo" },
      { label: "抖音", value: "douyin" },
      { label: "其他", value: "other" }
    ];
    const contentTypeOptions = [
      { label: "全部类型", value: "all" },
      { label: "活动", value: "activity" },
      { label: "产品", value: "product" },
      { label: "优惠券", value: "coupon" },
      { label: "海报", value: "poster" }
    ];
    const shareTabs = [
      { name: "全部", type: "all", emptyText: "暂无分享记录", emptyImage: "/static/images/empty-shares.png" },
      { name: "活动", type: "activity", emptyText: "暂无活动分享", emptyImage: "/static/images/empty-activity-shares.png" },
      { name: "产品", type: "product", emptyText: "暂无产品分享", emptyImage: "/static/images/empty-product-shares.png" },
      { name: "优惠券", type: "coupon", emptyText: "暂无优惠券分享", emptyImage: "/static/images/empty-coupon-shares.png" }
    ];
    const mockRecords = [
      {
        id: "1001",
        title: "磁州文化节",
        contentType: "activity",
        platform: "微信群",
        date: "2024-05-15",
        image: "/static/demo/activity1.jpg",
        views: 245,
        likes: 32,
        comments: 18,
        registrations: 12,
        commission: 120
      },
      {
        id: "1002",
        title: "亲子户外拓展活动",
        contentType: "activity",
        platform: "朋友圈",
        date: "2024-05-12",
        image: "/static/demo/activity2.jpg",
        views: 189,
        likes: 24,
        comments: 9,
        registrations: 8,
        commission: 80
      },
      {
        id: "1003",
        title: "磁州特产礼盒",
        contentType: "product",
        platform: "微信群",
        date: "2024-05-10",
        image: "/static/demo/product1.jpg",
        views: 156,
        likes: 18,
        comments: 7,
        registrations: 5,
        commission: 50
      },
      {
        id: "1004",
        title: "新人专享优惠券",
        contentType: "coupon",
        platform: "抖音",
        date: "2024-05-08",
        image: "/static/demo/coupon1.jpg",
        views: 324,
        likes: 45,
        comments: 12,
        registrations: 7,
        commission: 35
      },
      {
        id: "1005",
        title: "磁州文化体验套餐",
        contentType: "product",
        platform: "微博",
        date: "2024-05-05",
        image: "/static/demo/product2.jpg",
        views: 132,
        likes: 16,
        comments: 5,
        registrations: 0,
        commission: 0
      }
    ];
    common_vendor.onMounted(() => {
      loadRecords();
    });
    const loadRecords = () => {
      recordsList.value = mockRecords;
    };
    const getRecordsByType = (type) => {
      if (type === "all") {
        return recordsList.value;
      }
      return recordsList.value.filter((record) => record.contentType === type);
    };
    const switchTab = (index) => {
      currentTab.value = index;
    };
    const onSwiperChange = (e) => {
      currentTab.value = e.detail.current;
    };
    const onRefresh = () => {
      isRefreshing.value = true;
      setTimeout(() => {
        loadRecords();
        isRefreshing.value = false;
      }, 1e3);
    };
    const loadMore = () => {
      common_vendor.index.__f__("log", "at subPackages/activity-showcase/pages/share-records/index.vue:447", "加载更多记录");
    };
    const getTypeTagText = (type) => {
      const typeMap = {
        "activity": "活动",
        "product": "产品",
        "coupon": "优惠券",
        "poster": "海报"
      };
      return typeMap[type] || "未知类型";
    };
    const getTypeTagBackground = (type) => {
      const bgMap = {
        "activity": "rgba(255, 59, 105, 0.8)",
        "product": "rgba(90, 200, 250, 0.8)",
        "coupon": "rgba(255, 149, 0, 0.8)",
        "poster": "rgba(52, 199, 89, 0.8)"
      };
      return bgMap[type] || "rgba(142, 142, 147, 0.8)";
    };
    const viewRecordDetail = (record) => {
      switch (record.contentType) {
        case "activity":
          navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${record.id}`);
          break;
        case "product":
          navigateTo(`/subPackages/activity-showcase/pages/products/detail?id=${record.id}`);
          break;
        case "coupon":
          navigateTo(`/subPackages/activity-showcase/pages/coupon/detail?id=${record.id}`);
          break;
        default:
          navigateTo(`/subPackages/activity-showcase/pages/distribution/poster/index?id=${record.id}`);
      }
    };
    const shareAgain = (record) => {
      common_vendor.index.showShareMenu({
        withShareTicket: true,
        menus: ["shareAppMessage", "shareTimeline"]
      });
    };
    const viewAnalytics = (record) => {
      navigateTo(`/subPackages/activity-showcase/pages/share-analytics/index?id=${record.id}`);
    };
    const showFilter = () => {
      filterPopup.value.open();
    };
    const closeFilter = () => {
      filterPopup.value.close();
    };
    const selectTimeOption = (index) => {
      selectedTimeOption.value = index;
    };
    const togglePlatformOption = (index) => {
      const position = selectedPlatformOptions.value.indexOf(index);
      if (index === 0) {
        selectedPlatformOptions.value = [0];
      } else {
        if (selectedPlatformOptions.value.includes(0)) {
          selectedPlatformOptions.value = selectedPlatformOptions.value.filter((item) => item !== 0);
        }
        if (position !== -1) {
          selectedPlatformOptions.value.splice(position, 1);
          if (selectedPlatformOptions.value.length === 0) {
            selectedPlatformOptions.value = [0];
          }
        } else {
          selectedPlatformOptions.value.push(index);
        }
      }
    };
    const toggleContentTypeOption = (index) => {
      const position = selectedContentTypeOptions.value.indexOf(index);
      if (index === 0) {
        selectedContentTypeOptions.value = [0];
      } else {
        if (selectedContentTypeOptions.value.includes(0)) {
          selectedContentTypeOptions.value = selectedContentTypeOptions.value.filter((item) => item !== 0);
        }
        if (position !== -1) {
          selectedContentTypeOptions.value.splice(position, 1);
          if (selectedContentTypeOptions.value.length === 0) {
            selectedContentTypeOptions.value = [0];
          }
        } else {
          selectedContentTypeOptions.value.push(index);
        }
      }
    };
    const resetFilter = () => {
      selectedTimeOption.value = 0;
      selectedPlatformOptions.value = [0];
      selectedContentTypeOptions.value = [0];
    };
    const applyFilter = () => {
      common_vendor.index.__f__("log", "at subPackages/activity-showcase/pages/share-records/index.vue:565", "应用筛选", {
        time: timeOptions[selectedTimeOption.value].value,
        platforms: selectedPlatformOptions.value.map((index) => platformOptions[index].value),
        contentTypes: selectedContentTypeOptions.value.map((index) => contentTypeOptions[index].value)
      });
      common_vendor.index.showToast({
        title: "筛选已应用",
        icon: "success"
      });
      closeFilter();
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const navigateTo = (url) => {
      common_vendor.index.navigateTo({ url });
    };
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: common_vendor.p({
          d: "M22 3H2l8 9.46V19l4 2v-8.54L22 3z",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        d: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        e: common_vendor.o(showFilter),
        f: common_vendor.f(shareTabs, (tab, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(tab.name),
            b: currentTab.value === index
          }, currentTab.value === index ? {} : {}, {
            c: index,
            d: currentTab.value === index ? 1 : "",
            e: common_vendor.o(($event) => switchTab(index), index)
          });
        }),
        g: common_vendor.t(shareStats.value.totalShares),
        h: common_vendor.t(shareStats.value.totalViews),
        i: common_vendor.t(shareStats.value.totalRegistrations),
        j: common_vendor.t(shareStats.value.totalCommission),
        k: common_vendor.f(shareTabs, (tab, tabIndex, i0) => {
          return common_vendor.e({
            a: common_vendor.f(getRecordsByType(tab.type), (record, k1, i1) => {
              return {
                a: record.image,
                b: common_vendor.t(getTypeTagText(record.contentType)),
                c: getTypeTagBackground(record.contentType),
                d: common_vendor.t(record.title),
                e: "2b334a2a-3-" + i0 + "-" + i1 + "," + ("2b334a2a-2-" + i0 + "-" + i1),
                f: "2b334a2a-4-" + i0 + "-" + i1 + "," + ("2b334a2a-2-" + i0 + "-" + i1),
                g: "2b334a2a-5-" + i0 + "-" + i1 + "," + ("2b334a2a-2-" + i0 + "-" + i1),
                h: "2b334a2a-6-" + i0 + "-" + i1 + "," + ("2b334a2a-2-" + i0 + "-" + i1),
                i: "2b334a2a-2-" + i0 + "-" + i1,
                j: common_vendor.t(record.date),
                k: "2b334a2a-8-" + i0 + "-" + i1 + "," + ("2b334a2a-7-" + i0 + "-" + i1),
                l: "2b334a2a-9-" + i0 + "-" + i1 + "," + ("2b334a2a-7-" + i0 + "-" + i1),
                m: "2b334a2a-10-" + i0 + "-" + i1 + "," + ("2b334a2a-7-" + i0 + "-" + i1),
                n: "2b334a2a-7-" + i0 + "-" + i1,
                o: common_vendor.t(record.platform),
                p: "2b334a2a-12-" + i0 + "-" + i1 + "," + ("2b334a2a-11-" + i0 + "-" + i1),
                q: "2b334a2a-13-" + i0 + "-" + i1 + "," + ("2b334a2a-11-" + i0 + "-" + i1),
                r: "2b334a2a-11-" + i0 + "-" + i1,
                s: common_vendor.t(record.views),
                t: "2b334a2a-15-" + i0 + "-" + i1 + "," + ("2b334a2a-14-" + i0 + "-" + i1),
                v: "2b334a2a-14-" + i0 + "-" + i1,
                w: common_vendor.t(record.likes),
                x: "2b334a2a-17-" + i0 + "-" + i1 + "," + ("2b334a2a-16-" + i0 + "-" + i1),
                y: "2b334a2a-16-" + i0 + "-" + i1,
                z: common_vendor.t(record.comments),
                A: "2b334a2a-19-" + i0 + "-" + i1 + "," + ("2b334a2a-18-" + i0 + "-" + i1),
                B: "2b334a2a-20-" + i0 + "-" + i1 + "," + ("2b334a2a-18-" + i0 + "-" + i1),
                C: "2b334a2a-21-" + i0 + "-" + i1 + "," + ("2b334a2a-18-" + i0 + "-" + i1),
                D: "2b334a2a-22-" + i0 + "-" + i1 + "," + ("2b334a2a-18-" + i0 + "-" + i1),
                E: "2b334a2a-18-" + i0 + "-" + i1,
                F: common_vendor.t(record.registrations),
                G: "2b334a2a-24-" + i0 + "-" + i1 + "," + ("2b334a2a-23-" + i0 + "-" + i1),
                H: "2b334a2a-25-" + i0 + "-" + i1 + "," + ("2b334a2a-23-" + i0 + "-" + i1),
                I: "2b334a2a-23-" + i0 + "-" + i1,
                J: common_vendor.t(record.commission.toFixed(2)),
                K: "2b334a2a-27-" + i0 + "-" + i1 + "," + ("2b334a2a-26-" + i0 + "-" + i1),
                L: "2b334a2a-28-" + i0 + "-" + i1 + "," + ("2b334a2a-26-" + i0 + "-" + i1),
                M: "2b334a2a-29-" + i0 + "-" + i1 + "," + ("2b334a2a-26-" + i0 + "-" + i1),
                N: "2b334a2a-30-" + i0 + "-" + i1 + "," + ("2b334a2a-26-" + i0 + "-" + i1),
                O: "2b334a2a-31-" + i0 + "-" + i1 + "," + ("2b334a2a-26-" + i0 + "-" + i1),
                P: "2b334a2a-26-" + i0 + "-" + i1,
                Q: common_vendor.o(($event) => shareAgain(), record.id),
                R: "2b334a2a-33-" + i0 + "-" + i1 + "," + ("2b334a2a-32-" + i0 + "-" + i1),
                S: "2b334a2a-34-" + i0 + "-" + i1 + "," + ("2b334a2a-32-" + i0 + "-" + i1),
                T: "2b334a2a-35-" + i0 + "-" + i1 + "," + ("2b334a2a-32-" + i0 + "-" + i1),
                U: "2b334a2a-32-" + i0 + "-" + i1,
                V: common_vendor.o(($event) => viewAnalytics(record), record.id),
                W: record.id,
                X: common_vendor.o(($event) => viewRecordDetail(record), record.id)
              };
            }),
            b: getRecordsByType(tab.type).length === 0
          }, getRecordsByType(tab.type).length === 0 ? {
            c: tab.emptyImage || "/static/images/empty-shares.png",
            d: common_vendor.t(tab.emptyText || "暂无分享记录"),
            e: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/list/index"), tabIndex)
          } : {}, {
            f: common_vendor.o(onRefresh, tabIndex),
            g: common_vendor.o(loadMore, tabIndex),
            h: tabIndex
          });
        }),
        l: common_vendor.p({
          x: "3",
          y: "4",
          width: "18",
          height: "18",
          rx: "2",
          ry: "2",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        m: common_vendor.p({
          x1: "16",
          y1: "2",
          x2: "16",
          y2: "6",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        n: common_vendor.p({
          x1: "8",
          y1: "2",
          x2: "8",
          y2: "6",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        o: common_vendor.p({
          x1: "3",
          y1: "10",
          x2: "21",
          y2: "10",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        p: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        q: common_vendor.p({
          d: "M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        r: common_vendor.p({
          cx: "9",
          cy: "7",
          r: "4",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        s: common_vendor.p({
          d: "M23 21v-2a4 4 0 00-3-3.87m-4-12a4 4 0 010 7.75",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        t: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        v: common_vendor.p({
          d: "M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        w: common_vendor.p({
          cx: "12",
          cy: "12",
          r: "3",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        x: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        y: common_vendor.p({
          d: "M20.84 4.61a5.5 5.5 0 00-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 00-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 000-7.78z",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        z: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        A: common_vendor.p({
          d: "M21 11.5a8.38 8.38 0 01-.9 3.8 8.5 8.5 0 01-7.6 4.7 8.38 8.38 0 01-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 01-.9-3.8 8.5 8.5 0 014.7-7.6 8.38 8.38 0 013.8-.9h.5a8.48 8.48 0 018 8v.5z",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        B: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        C: common_vendor.p({
          d: "M16 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2",
          stroke: "#5856D6",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        D: common_vendor.p({
          cx: "8.5",
          cy: "7",
          r: "4",
          stroke: "#5856D6",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        E: common_vendor.p({
          x1: "20",
          y1: "8",
          x2: "20",
          y2: "14",
          stroke: "#5856D6",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        F: common_vendor.p({
          x1: "23",
          y1: "11",
          x2: "17",
          y2: "11",
          stroke: "#5856D6",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        G: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        H: common_vendor.p({
          x1: "12",
          y1: "1",
          x2: "12",
          y2: "23",
          stroke: "#5856D6",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        I: common_vendor.p({
          d: "M17 5H9.5a3.5 3.5 0 000 7h5a3.5 3.5 0 010 7H6",
          stroke: "#5856D6",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        J: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        K: common_vendor.p({
          cx: "18",
          cy: "5",
          r: "3",
          stroke: "#666666",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        L: common_vendor.p({
          cx: "6",
          cy: "12",
          r: "3",
          stroke: "#666666",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        M: common_vendor.p({
          cx: "18",
          cy: "19",
          r: "3",
          stroke: "#666666",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        N: common_vendor.p({
          x1: "8.59",
          y1: "13.51",
          x2: "15.42",
          y2: "17.49",
          stroke: "#666666",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        O: common_vendor.p({
          x1: "15.41",
          y1: "6.51",
          x2: "8.59",
          y2: "10.49",
          stroke: "#666666",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        P: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        Q: common_vendor.p({
          x1: "18",
          y1: "20",
          x2: "18",
          y2: "10",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        R: common_vendor.p({
          x1: "12",
          y1: "20",
          x2: "12",
          y2: "4",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        S: common_vendor.p({
          x1: "6",
          y1: "20",
          x2: "6",
          y2: "14",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        T: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        U: isRefreshing.value,
        V: currentTab.value,
        W: common_vendor.o(onSwiperChange),
        X: common_vendor.p({
          x1: "18",
          y1: "6",
          x2: "6",
          y2: "18",
          stroke: "#333333",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        Y: common_vendor.p({
          x1: "6",
          y1: "6",
          x2: "18",
          y2: "18",
          stroke: "#333333",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        Z: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        aa: common_vendor.o(closeFilter),
        ab: common_vendor.f(timeOptions, (option, index, i0) => {
          return {
            a: common_vendor.t(option.label),
            b: index,
            c: selectedTimeOption.value === index ? 1 : "",
            d: common_vendor.o(($event) => selectTimeOption(index), index)
          };
        }),
        ac: common_vendor.f(platformOptions, (option, index, i0) => {
          return {
            a: common_vendor.t(option.label),
            b: index,
            c: selectedPlatformOptions.value.includes(index) ? 1 : "",
            d: common_vendor.o(($event) => togglePlatformOption(index), index)
          };
        }),
        ad: common_vendor.f(contentTypeOptions, (option, index, i0) => {
          return {
            a: common_vendor.t(option.label),
            b: index,
            c: selectedContentTypeOptions.value.includes(index) ? 1 : "",
            d: common_vendor.o(($event) => toggleContentTypeOption(index), index)
          };
        }),
        ae: common_vendor.o(resetFilter),
        af: common_vendor.o(applyFilter),
        ag: common_vendor.sr(filterPopup, "2b334a2a-36", {
          "k": "filterPopup"
        }),
        ah: common_vendor.p({
          type: "bottom"
        })
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-2b334a2a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/share-records/index.js.map
