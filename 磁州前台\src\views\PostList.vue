<template>
  <div class="post-list-container">
    <!-- 头部搜索区域 -->
    <div class="search-header">
      <div class="search-wrapper">
        <div class="search-bar">
          <el-input
            v-model="searchQuery"
            placeholder="搜索信息"
            prefix-icon="el-icon-search"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #append>
              <el-button icon="el-icon-search" @click="handleSearch">搜索</el-button>
            </template>
          </el-input>
        </div>
        <div class="filter-options">
          <el-select v-model="selectedCategory" placeholder="所有类别" @change="handleCategoryChange">
            <el-option
              v-for="category in categories"
              :key="category.value"
              :label="category.label"
              :value="category.value"
            />
          </el-select>
          <el-select v-model="selectedSort" placeholder="排序方式" @change="handleSortChange">
            <el-option
              v-for="option in sortOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
          <el-button type="primary" icon="el-icon-plus" @click="createPost">发布信息</el-button>
        </div>
      </div>
    </div>

    <div class="main-content">
      <!-- 左侧分类导航 -->
      <div class="category-sidebar">
        <div class="sidebar-card">
          <h3 class="sidebar-title">信息分类</h3>
          <ul class="category-list">
            <li 
              v-for="category in categories" 
              :key="category.value"
              :class="{ active: selectedCategory === category.value }"
              @click="selectedCategory = category.value; handleCategoryChange()"
            >
              <i :class="category.icon"></i>
              <span>{{ category.label }}</span>
              <span class="category-count">{{ category.count }}</span>
            </li>
          </ul>
        </div>

        <div class="sidebar-card">
          <h3 class="sidebar-title">热门标签</h3>
          <div class="tag-cloud">
            <el-tag
              v-for="tag in hotTags"
              :key="tag.name"
              :type="tag.type"
              effect="plain"
              class="tag-item"
              @click="handleTagClick(tag.name)"
            >
              {{ tag.name }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 右侧信息列表 -->
      <div class="post-content">
        <!-- 顶部推荐信息 -->
        <div class="featured-posts" v-if="featuredPosts.length > 0">
          <h3 class="section-title">推荐信息</h3>
          <el-carousel :interval="5000" height="260px">
            <el-carousel-item v-for="post in featuredPosts" :key="post.id" @click="viewPost(post.id)">
              <div class="featured-post-card" :style="{ backgroundImage: `url(${post.coverImage})` }">
                <div class="featured-overlay">
                  <div class="featured-content">
                    <div class="featured-badge">推荐</div>
                    <h3 class="featured-title">{{ post.title }}</h3>
                    <p class="featured-desc">{{ post.description }}</p>
                  </div>
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>

        <!-- 信息列表 -->
        <div class="post-list-section">
          <h3 class="section-title">
            {{ selectedCategory ? getCategoryLabel(selectedCategory) : '所有信息' }}
            <span class="post-count">共{{ totalPosts }}条</span>
          </h3>
          
          <!-- 列表为空的提示 -->
          <div v-if="posts.length === 0" class="empty-list">
            <el-empty description="暂无相关信息" :image-size="200">
              <el-button type="primary" @click="createPost">发布信息</el-button>
            </el-empty>
          </div>
          
          <!-- 信息列表 -->
          <div v-else class="post-list">
            <div class="post-card" v-for="post in posts" :key="post.id" @click="viewPost(post.id)">
              <div class="post-image" v-if="post.coverImage">
                <img :src="post.coverImage" :alt="post.title">
                <div class="post-badge" v-if="post.badge">{{ post.badge }}</div>
              </div>
              <div class="post-info">
                <h3 class="post-title">{{ post.title }}</h3>
                <p class="post-desc">{{ post.description }}</p>
                <div class="post-meta">
                  <div class="post-category">
                    <el-tag size="mini" effect="plain">{{ post.category }}</el-tag>
                  </div>
                  <div class="post-stats">
                    <span class="post-price" v-if="post.price">¥{{ post.price }}</span>
                    <span class="post-views"><i class="el-icon-view"></i> {{ post.views }}</span>
                    <span class="post-time">{{ formatTime(post.createdAt) }}</span>
                  </div>
                </div>
                <div class="post-footer">
                  <div class="publisher-info">
                    <el-avatar :size="30" :src="post.publisher.avatar"></el-avatar>
                    <span class="publisher-name">{{ post.publisher.username }}</span>
                  </div>
                  <div class="action-buttons">
                    <el-button type="text" size="mini" icon="el-icon-share" @click.stop="sharePost(post.id)"></el-button>
                    <el-button type="text" size="mini" icon="el-icon-star-off" @click.stop="favoritePost(post.id)"></el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 分页 -->
          <div class="pagination-container" v-if="posts.length > 0">
            <el-pagination
              background
              layout="prev, pager, next, jumper"
              :total="totalPosts"
              :page-size="pageSize"
              :current-page="currentPage"
              @current-change="handlePageChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

const router = useRouter()

// 搜索相关
const searchQuery = ref('')
const selectedCategory = ref('')
const selectedSort = ref('newest')

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const totalPosts = ref(50)

// 分类选项
const categories = ref([
  { label: '房屋出租', value: 'house-rent', icon: 'el-icon-house', count: 128 },
  { label: '招聘求职', value: 'jobs', icon: 'el-icon-s-custom', count: 96 },
  { label: '二手交易', value: 'second-hand', icon: 'el-icon-shopping-bag', count: 245 },
  { label: '本地服务', value: 'local-service', icon: 'el-icon-service', count: 84 },
  { label: '交友活动', value: 'social', icon: 'el-icon-coffee-cup', count: 56 },
  { label: '宠物服务', value: 'pets', icon: 'el-icon-cherry', count: 38 }
])

// 排序选项
const sortOptions = ref([
  { label: '最新发布', value: 'newest' },
  { label: '价格从低到高', value: 'price-asc' },
  { label: '价格从高到低', value: 'price-desc' },
  { label: '浏览量最高', value: 'popular' }
])

// 热门标签
const hotTags = ref([
  { name: '精装修', type: '' },
  { name: '急招', type: 'warning' },
  { name: '全新', type: 'success' },
  { name: '包邮', type: 'info' },
  { name: '免费', type: 'danger' },
  { name: '周末', type: '' },
  { name: '兼职', type: 'warning' },
  { name: '附近', type: 'info' },
  { name: '学生', type: '' },
  { name: '家政', type: '' },
  { name: '健身', type: 'success' },
  { name: '培训', type: 'warning' }
])

// 推荐信息数据
const featuredPosts = ref([
  {
    id: 1,
    title: '精装三室两厅，近地铁站，拎包入住',
    description: '市中心精装三室，家电齐全，交通便利，适合一家人居住',
    coverImage: 'https://via.placeholder.com/800x400?text=Featured+1',
    price: '3800/月',
    category: '房屋出租'
  },
  {
    id: 2,
    title: '高薪招聘Java开发工程师，五险一金',
    description: '知名互联网公司招聘Java工程师，提供具有竞争力的薪资和福利',
    coverImage: 'https://via.placeholder.com/800x400?text=Featured+2',
    price: '15k-25k',
    category: '招聘求职'
  },
  {
    id: 3,
    title: '全新iPhone 13 Pro Max，256G，国行未拆封',
    description: '全新未拆封，购买后不喜欢，现低价转让，可当面交易',
    coverImage: 'https://via.placeholder.com/800x400?text=Featured+3',
    price: '7999',
    category: '二手交易'
  }
])

// 信息列表数据
const posts = ref([
  {
    id: 101,
    title: '地铁口两室一厅，精装修，家电齐全',
    description: '小区环境优美，地理位置好，出行方便，房屋南北通透，采光好',
    coverImage: 'https://via.placeholder.com/300x200?text=Post+1',
    price: '2800/月',
    category: '房屋出租',
    views: 568,
    createdAt: new Date(Date.now() - 3600000 * 5),
    badge: '精选',
    publisher: {
      username: '安心房产',
      avatar: 'https://via.placeholder.com/40?text=User1'
    }
  },
  {
    id: 102,
    title: '诚聘前端开发工程师，有Vue经验优先',
    description: '负责公司产品的前端开发，要求熟悉Vue、React等前端框架，有2年以上工作经验',
    coverImage: 'https://via.placeholder.com/300x200?text=Post+2',
    price: '12k-18k',
    category: '招聘求职',
    views: 325,
    createdAt: new Date(Date.now() - 3600000 * 12),
    publisher: {
      username: '科技招聘官',
      avatar: 'https://via.placeholder.com/40?text=User2'
    }
  },
  {
    id: 103,
    title: '9成新MacBook Pro 2021款',
    description: '去年购买，因换新电脑出售，M1芯片，16G内存，512G硬盘，性能强劲',
    coverImage: 'https://via.placeholder.com/300x200?text=Post+3',
    price: '8500',
    category: '二手交易',
    views: 724,
    createdAt: new Date(Date.now() - 3600000 * 24),
    badge: '急售',
    publisher: {
      username: '数码达人',
      avatar: 'https://via.placeholder.com/40?text=User3'
    }
  },
  {
    id: 104,
    title: '专业上门维修家电，价格实惠',
    description: '专业维修各类家电，包括空调、冰箱、洗衣机等，多年经验，服务周到',
    coverImage: 'https://via.placeholder.com/300x200?text=Post+4',
    price: '上门费50元起',
    category: '本地服务',
    views: 461,
    createdAt: new Date(Date.now() - 3600000 * 36),
    publisher: {
      username: '家电维修师',
      avatar: 'https://via.placeholder.com/40?text=User4'
    }
  },
  {
    id: 105,
    title: '周末爬山活动，结交新朋友',
    description: '本周六组织爬山活动，欢迎热爱户外运动的朋友一起参加，结交新朋友',
    coverImage: 'https://via.placeholder.com/300x200?text=Post+5',
    category: '交友活动',
    views: 298,
    createdAt: new Date(Date.now() - 3600000 * 48),
    publisher: {
      username: '户外俱乐部',
      avatar: 'https://via.placeholder.com/40?text=User5'
    }
  }
])

// 根据分类值获取分类标签
const getCategoryLabel = (value) => {
  const category = categories.value.find(item => item.value === value)
  return category ? category.label : '所有信息'
}

// 格式化时间
const formatTime = (time) => {
  return formatDistanceToNow(new Date(time), {
    addSuffix: true,
    locale: zhCN
  })
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  ElMessage.success(`搜索：${searchQuery.value}`)
  // 实际应用中需要调用API搜索
}

// 处理分类变更
const handleCategoryChange = () => {
  currentPage.value = 1
  ElMessage.success(`切换到分类：${getCategoryLabel(selectedCategory.value)}`)
  // 实际应用中需要调用API获取对应分类的信息
}

// 处理排序变更
const handleSortChange = () => {
  const sortLabel = sortOptions.value.find(item => item.value === selectedSort.value)?.label
  ElMessage.success(`排序方式：${sortLabel}`)
  // 实际应用中需要调用API按照指定排序获取信息
}

// 处理标签点击
const handleTagClick = (tag) => {
  searchQuery.value = tag
  handleSearch()
}

// 处理分页
const handlePageChange = (page) => {
  currentPage.value = page
  ElMessage.success(`加载第${page}页`)
  // 实际应用中需要调用API获取指定页的信息
}

// 查看信息详情
const viewPost = (id) => {
  router.push(`/post/${id}`)
}

// 发布信息
const createPost = () => {
  router.push('/post/create')
}

// 分享信息
const sharePost = (id) => {
  ElMessage.success(`分享信息：${id}`)
}

// 收藏信息
const favoritePost = (id) => {
  ElMessage.success(`收藏信息：${id}`)
}

onMounted(() => {
  // 获取信息列表数据
  console.log('加载信息列表数据')
})
</script>

<style scoped>
.post-list-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 搜索头部样式 */
.search-header {
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
  padding: 15px 0;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.search-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.search-bar {
  margin-bottom: 15px;
}

.filter-options {
  display: flex;
  justify-content: space-between;
  gap: 15px;
}

.filter-options .el-select {
  width: 150px;
}

/* 主内容区域样式 */
.main-content {
  max-width: 1200px;
  margin: 20px auto;
  display: flex;
  gap: 20px;
  padding: 0 20px;
}

/* 左侧边栏样式 */
.category-sidebar {
  width: 250px;
  flex-shrink: 0;
}

.sidebar-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 15px;
  margin-bottom: 20px;
}

.sidebar-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-top: 0;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.category-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.category-list li {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 5px;
}

.category-list li:hover {
  background-color: #f5f7fa;
}

.category-list li.active {
  background-color: #ecf5ff;
  color: #409EFF;
}

.category-list i {
  margin-right: 10px;
  font-size: 18px;
}

.category-count {
  margin-left: auto;
  background-color: #f0f0f0;
  border-radius: 10px;
  padding: 2px 8px;
  font-size: 12px;
}

.category-list li.active .category-count {
  background-color: #409EFF;
  color: #fff;
}

.tag-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  cursor: pointer;
  margin: 0;
}

/* 右侧内容区 */
.post-content {
  flex-grow: 1;
  min-width: 0; /* 防止弹性项目溢出 */
}

/* 推荐信息样式 */
.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-top: 0;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.post-count {
  font-size: 14px;
  color: #909399;
  font-weight: normal;
  margin-left: 10px;
}

.featured-posts {
  margin-bottom: 25px;
}

.featured-post-card {
  height: 100%;
  border-radius: 8px;
  background-size: cover;
  background-position: center;
  position: relative;
  cursor: pointer;
}

.featured-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  padding: 30px 20px 20px;
  border-radius: 0 0 8px 8px;
  color: #fff;
}

.featured-badge {
  position: absolute;
  top: -40px;
  right: 20px;
  background-color: #f56c6c;
  color: #fff;
  padding: 2px 10px;
  border-radius: 4px;
  font-size: 12px;
}

.featured-title {
  font-size: 22px;
  margin: 0 0 10px;
}

.featured-desc {
  margin: 0;
  opacity: 0.9;
}

/* 信息列表样式 */
.post-list-section {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 20px;
}

.empty-list {
  padding: 40px 0;
  text-align: center;
}

.post-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.post-card {
  display: flex;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
  overflow: hidden;
  transition: all 0.3s;
  cursor: pointer;
  border: 1px solid #f0f0f0;
}

.post-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border-color: #e0e0e0;
}

.post-image {
  width: 200px;
  height: 150px;
  flex-shrink: 0;
  position: relative;
}

.post-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.post-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(245, 108, 108, 0.9);
  color: #fff;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.post-info {
  flex-grow: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.post-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.post-desc {
  font-size: 14px;
  color: #606266;
  margin: 0 0 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.5;
  flex-grow: 1;
}

.post-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.post-stats {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 13px;
  color: #909399;
}

.post-price {
  color: #f56c6c;
  font-weight: 600;
}

.post-views i {
  margin-right: 3px;
}

.post-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 10px;
  border-top: 1px solid #f0f0f0;
}

.publisher-info {
  display: flex;
  align-items: center;
}

.publisher-name {
  margin-left: 8px;
  font-size: 13px;
  color: #606266;
}

.action-buttons {
  display: flex;
  gap: 5px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 25px;
  display: flex;
  justify-content: center;
}

/* 适配平板 */
@media (max-width: 992px) {
  .main-content {
    flex-direction: column;
  }
  
  .category-sidebar {
    width: 100%;
  }
  
  .post-image {
    width: 150px;
  }
}

/* 适配手机 */
@media (max-width: 600px) {
  .search-wrapper {
    padding: 0 15px;
  }
  
  .filter-options {
    flex-wrap: wrap;
  }
  
  .filter-options .el-select,
  .filter-options .el-button {
    width: 100%;
  }
  
  .post-card {
    flex-direction: column;
  }
  
  .post-image {
    width: 100%;
    height: 180px;
  }
  
  .featured-title {
    font-size: 18px;
  }
}
</style> 