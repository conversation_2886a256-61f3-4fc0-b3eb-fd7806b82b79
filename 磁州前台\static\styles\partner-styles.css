/* 合伙人系统通用样式 */

/* 颜色定义 */
:root {
  --primary-blue: #1677FF;
  --primary-dark-blue: #0052CC;
  --primary-light-blue: #4096FF;
  --secondary-blue: #E6F7FF;
  --light-blue-1: #F0F7FF;
  --light-blue-2: #D6E8FF;
  --text-dark: #333333;
  --text-regular: #666666;
  --text-light: #999999;
  --border-color: #E5E5E5;
  --background-grey: #F5F7FA;
  --success-color: #52C41A;
  --warning-color: #FAAD14;
  --error-color: #FF4D4F;
}

/* 渐变背景 */
.bg-gradient-blue {
  background-image: linear-gradient(135deg, var(--primary-blue), var(--primary-dark-blue));
  color: #FFFFFF;
}

/* 浅蓝色渐变背景 */
.bg-gradient-light-blue {
  background-image: linear-gradient(135deg, var(--light-blue-1), var(--light-blue-2));
  color: var(--text-dark);
}

/* 卡片样式 */
.partner-card {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
  overflow: hidden;
}

/* 浅蓝色渐变卡片 */
.partner-card-light-blue {
  background-image: linear-gradient(135deg, var(--light-blue-1), var(--light-blue-2));
  border-radius: 12rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
  overflow: hidden;
}

.partner-card-header {
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.partner-card-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-dark);
}

.partner-card-body {
  padding: 30rpx;
}

/* 按钮样式 */
.btn-primary {
  background-color: var(--primary-blue);
  color: #FFFFFF;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  padding: 16rpx 30rpx;
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-blue);
  border: 1rpx solid var(--primary-blue);
  border-radius: 8rpx;
  font-size: 28rpx;
  padding: 16rpx 30rpx;
}

/* 标签样式 */
.partner-tag {
  display: inline-block;
  padding: 6rpx 16rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
}

.tag-blue {
  background-color: var(--secondary-blue);
  color: var(--primary-blue);
}

.tag-success {
  background-color: #F6FFED;
  color: var(--success-color);
}

.tag-warning {
  background-color: #FFF7E6;
  color: var(--warning-color);
}

/* 列表样式 */
.partner-list-item {
  display: flex;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.partner-list-item:last-child {
  border-bottom: none;
}

/* 数据展示 */
.data-overview {
  display: flex;
  justify-content: space-between;
  text-align: center;
  padding: 20rpx 0;
}

.data-item {
  flex: 1;
}

.data-value {
  font-size: 36rpx;
  font-weight: 500;
  color: var(--text-dark);
  margin-bottom: 8rpx;
}

.data-label {
  font-size: 24rpx;
  color: var(--text-light);
}

/* 分割线 */
.divider {
  height: 1rpx;
  background-color: var(--border-color);
  margin: 20rpx 0;
}

.divider-vertical {
  width: 1rpx;
  height: 100%;
  background-color: var(--border-color);
}

/* 进度条 */
.progress-bar {
  height: 10rpx;
  background-color: var(--secondary-blue);
  border-radius: 10rpx;
  overflow: hidden;
}

.progress-bar-inner {
  height: 100%;
  background-color: var(--primary-blue);
  border-radius: 10rpx;
}

/* 文字颜色修复 - 解决白色背景上白色文字的问题 */
.text-on-white {
  color: var(--text-dark) !important;
}

.text-on-blue {
  color: #FFFFFF !important;
}

.text-on-light {
  color: var(--text-dark) !important;
}

/* 卡片内文字颜色 */
.partner-card .card-content {
  color: var(--text-regular);
}

/* 白色背景上的按钮文字 */
.white-bg-btn {
  background-color: var(--primary-blue);
  color: #FFFFFF;
}

/* 蓝色背景上的按钮文字 */
.blue-bg-btn {
  background-color: #FFFFFF;
  color: var(--primary-blue);
}

/* 确保弹窗中的文字可见 */
.modal-content {
  background-color: #FFFFFF;
}

.modal-content .modal-title {
  color: var(--text-dark);
}

.modal-content .modal-text {
  color: var(--text-regular);
}

/* 全局修复 - 确保所有页面中的文字都能看清 */
/* 白色背景上的文字 */
.card-header text, 
.card-title, 
.more-text,
.function-name,
.promotion-value,
.promotion-label,
.data-value,
.data-label,
.step-title,
.step-desc,
.rule-title,
.rule-content,
.rule-text,
.faq-question,
.faq-answer,
.upgrade-title,
.upgrade-desc,
.requirement-label,
.requirement-value,
.share-name,
.preview-title,
.guide-title,
.rules-title,
.data-title,
.table-cell,
.header-cell {
  color: var(--text-dark) !important;
}

/* 浅色文字 */
.data-label,
.more-text,
.step-desc,
.promotion-label {
  color: var(--text-light) !important;
}

/* 普通文字 */
.rule-text,
.faq-answer,
.rule-content {
  color: var(--text-regular) !important;
}

/* 蓝色背景上的文字 */
.partner-card,
.current-level-card,
.upgrade-confirm-btn,
.confirm-btn {
  color: #FFFFFF !important;
}

/* 蓝色文字 */
.switch-text,
.level-node.active .node-text {
  color: var(--primary-blue) !important;
}

/* 确保白色背景卡片中的内容可见 */
.privileges-card, 
.rules-card, 
.faq-card,
.promotion-guide, 
.commission-rules, 
.promotion-data,
.income-card, 
.function-card, 
.level-card, 
.promotion-card,
.poster-preview {
  background-color: #FFFFFF;
}

/* 确保模态框中的内容可见 */
.modal-content,
.share-content {
  background-color: #FFFFFF;
}

/* 确保按钮文字清晰可见 */
.btn-primary,
.upgrade-confirm-btn,
.confirm-btn {
  color: #FFFFFF !important;
}

.cancel-btn {
  color: var(--text-dark) !important;
}

/* 返回按钮增强样式 */
.cu-bar .action.back-action {
  min-width: 120rpx !important;
  height: 90rpx !important;
  padding: 0 30rpx !important;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  position: relative !important;
  z-index: 9999 !important;
}

.cu-bar .action.back-action::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 120rpx;
  height: 90rpx;
  background-color: transparent;
  z-index: 1;
}

.back-icon {
  width: 50rpx !important;
  height: 50rpx !important;
  margin-right: 10rpx !important;
  z-index: 2 !important;
}

/* 点击反馈样式 */
.back-hover {
  opacity: 0.8 !important;
  transform: scale(0.95) !important;
} 