{"version": 3, "file": "FeatureGrid.js", "sources": ["components/index/FeatureGrid.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7AvY29tcG9uZW50cy9pbmRleC9GZWF0dXJlR3JpZC52dWU"], "sourcesContent": ["<template>\n  <!-- 四宫格特色功能区 -->\n  <view class=\"feature-grid card-section fade-in\">\n    <view \n      class=\"feature-item\" \n      style=\"background-image: linear-gradient(to left, #EF8BA6 0%, #FFFFFF 100%);\" \n      @click=\"navigateTo('/pages/red-packet/list')\"\n    >\n      <image class=\"feature-icon\" src=\"/static/images/tabbar/抢红包.gif\"></image>\n      <view class=\"feature-content\">\n        <text class=\"feature-title\">抢红包</text>\n        <text class=\"feature-desc\">最高得99元</text>\n      </view>\n    </view>\n    \n    <view \n      class=\"feature-item\" \n      style=\"background-image: linear-gradient(to left, #6FA3D0 0%, #FFFFFF 100%);\" \n      @click=\"goToSignIn\"\n    >\n      <image class=\"feature-icon\" src=\"/static/images/tabbar/每日签到.gif\"></image>\n      <view class=\"feature-content\">\n        <text class=\"feature-title\">每日签到</text>\n        <text class=\"feature-desc\">连续领好礼</text>\n      </view>\n    </view>\n    \n    <view \n      class=\"feature-item\" \n      style=\"background-image: linear-gradient(to left, #AA7BA0 0%, #FFFFFF 100%);\" \n      @click=\"navigateTo('/subPackages/activity-showcase/pages/index/index')\"\n    >\n      <image class=\"feature-icon\" src=\"/static/images/tabbar/商家活动.gif\"></image>\n      <view class=\"feature-content\">\n        <text class=\"feature-title\">活动中心</text>\n        <text class=\"feature-desc promo-text\">推广赚佣金</text>\n      </view>\n    </view>\n    \n    <view \n      class=\"feature-item\" \n      style=\"background-image: linear-gradient(to left, #6EC3B3 0%, #FFFFFF 100%);\" \n      @click=\"navigateTo('/subPackages/activity/pages/city-events')\"\n    >\n      <image class=\"feature-icon\" src=\"/static/images/tabbar/同城活动.png\"></image>\n      <view class=\"feature-content\">\n        <text class=\"feature-title\">最美磁州</text>\n        <text class=\"feature-desc\">丰富多彩</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nfunction navigateTo(url) {\n  if (!url) return;\n  \n  uni.navigateTo({\n    url: url,\n    fail: (err) => {\n      console.error('页面跳转失败:', err);\n      // 尝试使用switchTab\n      uni.switchTab({\n        url: url,\n        fail: (err2) => {\n          console.error('switchTab也失败:', err2);\n          uni.showToast({\n            title: '页面跳转失败',\n            icon: 'none'\n          });\n        }\n      });\n    }\n  });\n}\n\nfunction goToSignIn() {\n  // 签到功能\n  uni.navigateTo({\n    url: '/subPackages/checkin/pages/points',\n    fail: (err) => {\n      console.error('签到页面跳转失败:', err);\n      uni.showToast({\n        title: '签到功能暂未开放',\n        icon: 'none'\n      });\n    }\n  });\n}\n</script>\n\n<style lang=\"scss\" scoped>\n/* 四宫格特色功能区样式 */\n.feature-grid {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n  padding: 30rpx 24rpx;\n  background-color: #FFFFFF;\n  border-radius: 32rpx;\n  margin: 0 25rpx 34rpx;\n  box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.08);\n  position: relative;\n  overflow: hidden;\n  z-index: 1;\n}\n\n.feature-item {\n  width: 48%;\n  height: 164rpx;\n  border-radius: 26rpx;\n  display: flex;\n  align-items: center;\n  padding: 20rpx;\n  box-sizing: border-box;\n  margin-bottom: 24rpx;\n  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n  border: none;\n}\n\n.feature-item:active {\n  transform: translateY(2rpx) scale(0.98);\n  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);\n}\n\n.feature-icon {\n  width: 80rpx;\n  height: 80rpx;\n  z-index: 2;\n  position: relative;\n  filter: drop-shadow(0 2rpx 3rpx rgba(0, 0, 0, 0.2));\n}\n\n.feature-content {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  margin-left: 20rpx;\n  z-index: 1;\n}\n\n.feature-title {\n  font-size: 32rpx;\n  font-weight: 800;\n  color: #333333;\n  margin-bottom: 6rpx;\n  text-shadow: none;\n}\n\n.feature-desc {\n  font-size: 24rpx;\n  font-weight: 600;\n  color: #666666;\n  text-shadow: none;\n}\n\n/* 推广赚佣金动态效果 */\n.promo-text {\n  position: relative;\n  animation: promo-pulse 1.5s infinite ease-in-out;\n  text-shadow: 0 0 5rpx rgba(170, 123, 160, 0.3);\n}\n\n@keyframes promo-pulse {\n  0% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.1);\n  }\n  100% {\n    transform: scale(1);\n  }\n}\n\n.card-section {\n  margin-bottom: 20rpx;\n}\n\n.fade-in {\n  animation: fadeIn 0.5s ease-in-out;\n}\n\n@keyframes fadeIn {\n  from { opacity: 0; transform: translateY(20rpx); }\n  to { opacity: 1; transform: translateY(0); }\n}\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/components/index/FeatureGrid.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;;;;;AAsDA,aAAS,WAAW,KAAK;AACvB,UAAI,CAAC;AAAK;AAEVA,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,QACA,MAAM,CAAC,QAAQ;AACbA,wBAAc,MAAA,MAAA,SAAA,0CAAA,WAAW,GAAG;AAE5BA,wBAAAA,MAAI,UAAU;AAAA,YACZ;AAAA,YACA,MAAM,CAAC,SAAS;AACdA,4BAAc,MAAA,MAAA,SAAA,0CAAA,iBAAiB,IAAI;AACnCA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAEA,aAAS,aAAa;AAEpBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL,MAAM,CAAC,QAAQ;AACbA,wBAAc,MAAA,MAAA,SAAA,0CAAA,aAAa,GAAG;AAC9BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;ACvFA,GAAG,gBAAgB,SAAS;"}