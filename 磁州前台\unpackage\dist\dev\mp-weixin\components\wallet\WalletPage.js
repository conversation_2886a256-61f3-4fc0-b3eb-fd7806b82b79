"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const utils_navigation = require("../../utils/navigation.js");
const navbarColor = "linear-gradient(135deg, #0066FF, #0052CC)";
const _sfc_main = {
  __name: "WalletPage",
  props: {
    // 场景类型: 'main'(主钱包), 'carpool'(拼车钱包)
    scene: {
      type: String,
      default: "main"
    },
    // 自定义路径前缀，用于导航
    pathPrefix: {
      type: String,
      default: "/subPackages/payment/pages"
    }
  },
  setup(__props) {
    const props = __props;
    const statusBarHeight = common_vendor.ref(20);
    const navbarHeight = common_vendor.ref(64);
    const balanceInfo = common_vendor.ref({
      amount: 0,
      frozenAmount: 0
    });
    const transactions = common_vendor.ref([]);
    const cardGradient = props.scene === "carpool" ? "linear-gradient(to right, #7ABAFF, #B0D7FF)" : "linear-gradient(to right, #7AA6FF, #A6C7FF)";
    const backIconPath = props.scene === "carpool" ? "/static/images/tabbar/最新返回键.png" : "/static/images/tabbar/返回键.png";
    const getPagePath = (page) => {
      return `${props.pathPrefix}/${page}`;
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const navigateTo = (url) => {
      utils_navigation.smartNavigate(url).catch((err) => {
        common_vendor.index.__f__("error", "at components/wallet/WalletPage.vue:130", "页面跳转失败:", err);
      });
    };
    const navigateToWithdraw = () => {
      navigateTo(getPagePath("withdraw"));
    };
    const navigateToRecharge = () => {
      navigateTo(getPagePath("recharge"));
    };
    const navigateToDetail = () => {
      navigateTo(getPagePath("detail"));
    };
    const navigateToBills = () => {
      navigateTo(getPagePath("bills"));
    };
    const navigateToBank = () => {
      navigateTo(getPagePath("bank"));
    };
    const getWalletBalance = () => {
      setTimeout(() => {
        balanceInfo.value = {
          amount: 158.5,
          frozenAmount: 0
        };
      }, 500);
    };
    const getTransactions = () => {
      setTimeout(() => {
        transactions.value = [
          {
            id: "tx001",
            title: "充值",
            time: "2023-11-05 14:30",
            amount: 100,
            type: "income"
          },
          {
            id: "tx002",
            title: "服务支付",
            time: "2023-11-03 09:15",
            amount: 35,
            type: "expense"
          },
          {
            id: "tx003",
            title: "提现",
            time: "2023-10-28 16:22",
            amount: 50,
            type: "expense"
          }
        ];
      }, 500);
    };
    common_vendor.onMounted(() => {
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight;
      navbarHeight.value = statusBarHeight.value + 44;
      getWalletBalance();
      getTransactions();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.unref(backIconPath),
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: navbarColor,
        e: common_vendor.t(balanceInfo.value.amount.toFixed(2)),
        f: common_vendor.o(navigateToWithdraw),
        g: common_vendor.o(navigateToRecharge),
        h: navbarHeight.value + 10 + "px",
        i: common_vendor.unref(cardGradient),
        j: common_assets._imports_0$14,
        k: common_vendor.o(navigateToDetail),
        l: common_assets._imports_0$14,
        m: common_vendor.o(navigateToBills),
        n: common_assets._imports_0$14,
        o: common_vendor.o(navigateToBank),
        p: common_vendor.o(navigateToBills),
        q: transactions.value.length > 0
      }, transactions.value.length > 0 ? {
        r: common_vendor.f(transactions.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.title),
            b: common_vendor.t(item.time),
            c: common_vendor.t(item.type === "income" ? "+" : "-"),
            d: common_vendor.t(item.amount.toFixed(2)),
            e: item.type === "income" ? 1 : "",
            f: item.type === "expense" ? 1 : "",
            g: index
          };
        })
      } : {
        s: common_assets._imports_1$3
      });
    };
  }
};
wx.createComponent(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/wallet/WalletPage.js.map
