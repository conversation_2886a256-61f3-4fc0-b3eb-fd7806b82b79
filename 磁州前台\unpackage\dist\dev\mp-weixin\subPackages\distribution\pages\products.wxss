/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.products-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 88rpx 32rpx 30rpx;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);
}
.navbar-back {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 24rpx;
  height: 24rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}
.navbar-right {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: #FFFFFF;
}
.search-input-wrap {
  flex: 1;
  height: 72rpx;
  background: #F5F7FA;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  margin-right: 20rpx;
}
.search-icon {
  width: 32rpx;
  height: 32rpx;
  background-color: #6B0FBE;
  border-radius: 40rpx;
  margin-right: 10rpx;
}
.search-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: #333;
}
.clear-icon {
  width: 32rpx;
  height: 32rpx;
  background-color: #cccccc;
  border-radius: 40rpx;
}
.search-btn {
  font-size: 28rpx;
  color: #6B0FBE;
}

/* 筛选栏 */
.filter-bar {
  display: flex;
  background: #FFFFFF;
  padding: 0 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.filter-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}
.filter-item.active {
  color: #6B0FBE;
  font-weight: 600;
}
.sort-icon {
  width: 16rpx;
  height: 24rpx;
  position: relative;
  margin-left: 8rpx;
}
.sort-icon::before,
.sort-icon::after {
  content: "";
  position: absolute;
  left: 0;
  width: 0;
  height: 0;
}
.sort-icon::before {
  top: 4rpx;
  border-left: 8rpx solid transparent;
  border-right: 8rpx solid transparent;
  border-bottom: 8rpx solid #999;
}
.sort-icon::after {
  bottom: 4rpx;
  border-left: 8rpx solid transparent;
  border-right: 8rpx solid transparent;
  border-top: 8rpx solid #999;
}

/* 商品列表 */
.products-list {
  margin: 0 30rpx;
}
.product-card {
  background: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.product-image {
  width: 100%;
  height: 400rpx;
  background-color: #f5f5f5;
}
.product-info {
  padding: 20rpx;
}
.product-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
}
.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.product-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF5722;
}
.product-sales {
  font-size: 24rpx;
  color: #999;
}
.product-commission {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.commission-rate {
  font-size: 24rpx;
  color: #666;
}
.commission-value {
  font-size: 24rpx;
  color: #6B0FBE;
  font-weight: 600;
}
.promote-btn {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  padding: 12rpx 0;
  line-height: 1.5;
  width: 100%;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 26rpx;
  color: #666;
}

/* 推广弹窗 */
.promotion-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}
.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  border-radius: 40rpx 40rpx 0 0;
  padding: 30rpx;
  animation: slideUp 0.3s ease-out;
}
@keyframes slideUp {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.close-icon {
  width: 40rpx;
  height: 40rpx;
  background-color: #999;
  border-radius: 40rpx;
}
.promotion-options {
  display: flex;
  justify-content: space-around;
  padding: 30rpx 0;
}
.promotion-option {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.option-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
  border-radius: 40rpx;
}
.option-icon.poster {
  background-color: #FF9500;
}
.option-icon.qrcode {
  background-color: #34C759;
}
.option-icon.link {
  background-color: #1677FF;
}
.option-icon.share {
  background-color: #6B0FBE;
}
.option-name {
  font-size: 26rpx;
  color: #333;
}