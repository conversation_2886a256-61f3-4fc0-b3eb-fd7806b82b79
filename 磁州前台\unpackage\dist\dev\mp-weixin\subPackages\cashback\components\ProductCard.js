"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  name: "ProductCard",
  props: {
    product: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      isFavorite: false
    };
  },
  created() {
    this.isFavorite = this.product.isFavorite || false;
  },
  methods: {
    // 导航到商品详情
    navigateToDetail() {
      common_vendor.index.navigateTo({
        url: `/subPackages/cashback/pages/detail/index?id=${this.product.id}`
      });
    },
    // 切换收藏状态
    toggleFavorite() {
      this.isFavorite = !this.isFavorite;
      this.$emit("favorite", {
        id: this.product.id,
        isFavorite: this.isFavorite
      });
    },
    // 格式化价格
    formatPrice(price) {
      return parseFloat(price).toFixed(2);
    },
    // 格式化销量
    formatSales(sales) {
      if (sales >= 1e4) {
        return (sales / 1e4).toFixed(1) + "万";
      }
      return sales;
    }
  }
};
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.product.image,
    b: $props.product.discount
  }, $props.product.discount ? {
    c: common_vendor.t($props.product.discount)
  } : {}, {
    d: $props.product.platformIcon
  }, $props.product.platformIcon ? {
    e: $props.product.platformIcon
  } : {}, {
    f: common_vendor.t($props.product.platformName),
    g: common_vendor.t($props.product.title),
    h: common_vendor.t($options.formatPrice($props.product.price)),
    i: common_vendor.t($props.product.cashbackRate),
    j: common_vendor.t($options.formatSales($props.product.sales)),
    k: $data.isFavorite
  }, $data.isFavorite ? {
    l: common_vendor.p({
      fill: "#FF6B6B",
      d: "M12,21.35L10.55,20.03C5.4,15.36 2,12.27 2,8.5C2,5.41 4.42,3 7.5,3C9.24,3 10.91,3.81 12,5.08C13.09,3.81 14.76,3 16.5,3C19.58,3 22,5.41 22,8.5C22,12.27 18.6,15.36 13.45,20.03L12,21.35Z"
    })
  } : {
    m: common_vendor.p({
      fill: "#999999",
      d: "M12.1,18.55L12,18.65L11.89,18.55C7.14,14.24 4,11.39 4,8.5C4,6.5 5.5,5 7.5,5C9.04,5 10.54,6 11.07,7.36H12.93C13.46,6 14.96,5 16.5,5C18.5,5 20,6.5 20,8.5C20,11.39 16.86,14.24 12.1,18.55M16.5,3C14.76,3 13.09,3.81 12,5.08C10.91,3.81 9.24,3 7.5,3C4.42,3 2,5.41 2,8.5C2,12.27 5.4,15.36 10.55,20.03L12,21.35L13.45,20.03C18.6,15.36 22,12.27 22,8.5C22,5.41 19.58,3 16.5,3Z"
    })
  }, {
    n: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "16",
      height: "16"
    }),
    o: common_vendor.o((...args) => $options.toggleFavorite && $options.toggleFavorite(...args)),
    p: common_vendor.o((...args) => $options.navigateToDetail && $options.navigateToDetail(...args))
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-be4dc338"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/cashback/components/ProductCard.js.map
