/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-d81dfc96, html.data-v-d81dfc96, #app.data-v-d81dfc96, .index-container.data-v-d81dfc96 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.benefits-container.data-v-d81dfc96 {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 20rpx;
}

/* 导航栏样式 */
.custom-navbar.data-v-d81dfc96 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  background: linear-gradient(135deg, #3a7afe, #6ca6ff);
  color: #fff;
  z-index: 100;
}
.navbar-left.data-v-d81dfc96 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon.data-v-d81dfc96 {
  width: 36rpx;
  height: 36rpx;
}
.navbar-title.data-v-d81dfc96 {
  font-size: 18px;
  font-weight: 500;
}
.navbar-right.data-v-d81dfc96 {
  width: 60rpx;
}

/* 福利概览卡片 */
.overview-card.data-v-d81dfc96 {
  margin: calc(44px + var(--status-bar-height)) 20rpx 20rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, #3a7afe, #6ca6ff);
  border-radius: 16rpx;
  color: #fff;
  box-shadow: 0 4rpx 20rpx rgba(106, 166, 255, 0.3);
}
.overview-header.data-v-d81dfc96 {
  margin-bottom: 30rpx;
}
.overview-title.data-v-d81dfc96 {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  display: block;
}
.overview-subtitle.data-v-d81dfc96 {
  font-size: 24rpx;
  opacity: 0.8;
}
.overview-content.data-v-d81dfc96 {
  display: flex;
  justify-content: space-between;
}
.overview-item.data-v-d81dfc96 {
  text-align: center;
  flex: 1;
}
.overview-number.data-v-d81dfc96 {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
}
.overview-label.data-v-d81dfc96 {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 福利分类导航 */
.benefits-nav.data-v-d81dfc96 {
  margin: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.nav-item.data-v-d81dfc96 {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}
.nav-item.data-v-d81dfc96:last-child {
  border-bottom: none;
}
.nav-icon-wrap.data-v-d81dfc96 {
  width: 80rpx;
  height: 80rpx;
  background-color: #f0f5ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  position: relative;
}
.nav-icon.data-v-d81dfc96 {
  width: 40rpx;
  height: 40rpx;
}
.nav-badge.data-v-d81dfc96 {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  background-color: #ff4d4f;
  color: #fff;
  font-size: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6rpx;
}
.nav-label.data-v-d81dfc96 {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  flex: 1;
}
.nav-desc.data-v-d81dfc96 {
  font-size: 24rpx;
  color: #999;
  margin-right: 20rpx;
}
.nav-arrow.data-v-d81dfc96 {
  width: 32rpx;
  height: 32rpx;
  transform: rotate(90deg);
}

/* 最近获得的福利 */
.recent-benefits.data-v-d81dfc96 {
  margin: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.section-header.data-v-d81dfc96 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.section-title.data-v-d81dfc96 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.section-more.data-v-d81dfc96 {
  font-size: 24rpx;
  color: #3a7afe;
}
.benefits-list.data-v-d81dfc96 {
  margin-top: 20rpx;
}
.benefit-item.data-v-d81dfc96 {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.benefit-item.data-v-d81dfc96:last-child {
  border-bottom: none;
}
.benefit-icon.data-v-d81dfc96 {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}
.benefit-info.data-v-d81dfc96 {
  flex: 1;
}
.benefit-name.data-v-d81dfc96 {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
  display: block;
}
.benefit-desc.data-v-d81dfc96 {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 6rpx;
  display: block;
}
.benefit-time.data-v-d81dfc96 {
  font-size: 22rpx;
  color: #999;
  display: block;
}
.benefit-status.data-v-d81dfc96 {
  font-size: 24rpx;
  color: #3a7afe;
  padding: 4rpx 12rpx;
  background-color: #f0f5ff;
  border-radius: 6rpx;
}
.status-expired.data-v-d81dfc96 {
  color: #999;
  background-color: #f5f5f5;
}

/* 推荐活动 */
.recommended-activities.data-v-d81dfc96 {
  margin: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.activities-scroll.data-v-d81dfc96 {
  width: 100%;
  white-space: nowrap;
}
.activities-list.data-v-d81dfc96 {
  display: flex;
  padding: 10rpx 0;
}
.activity-card.data-v-d81dfc96 {
  width: 400rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  display: inline-block;
}
.activity-image.data-v-d81dfc96 {
  width: 100%;
  height: 200rpx;
}
.activity-info.data-v-d81dfc96 {
  padding: 16rpx;
}
.activity-name.data-v-d81dfc96 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 6rpx;
  display: block;
  white-space: normal;
}
.activity-desc.data-v-d81dfc96 {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.activity-meta.data-v-d81dfc96 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.activity-time.data-v-d81dfc96 {
  font-size: 22rpx;
  color: #999;
}
.activity-tag.data-v-d81dfc96 {
  font-size: 22rpx;
  color: #3a7afe;
  background-color: #f0f5ff;
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
}

/* 空状态 */
.empty-view.data-v-d81dfc96 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}
.empty-icon.data-v-d81dfc96 {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}
.empty-text.data-v-d81dfc96 {
  font-size: 28rpx;
  color: #999;
}