"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const utils_navigation = require("../../../utils/navigation.js");
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const navbarHeight = common_vendor.ref(64);
    const balanceInfo = common_vendor.ref({
      amount: 0,
      frozenAmount: 0
    });
    const transactions = common_vendor.ref([
      {
        id: "tx001",
        title: "充值",
        time: "2023-11-05 14:30",
        amount: 100,
        type: "income"
      },
      {
        id: "tx002",
        title: "服务支付",
        time: "2023-11-03 09:15",
        amount: 35,
        type: "expense"
      },
      {
        id: "tx003",
        title: "提现",
        time: "2023-10-28 16:22",
        amount: 50,
        type: "expense"
      }
    ]);
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const navigateTo = (url) => {
      utils_navigation.smartNavigate(url).catch((err) => {
        common_vendor.index.__f__("error", "at subPackages/payment/pages/index.vue:121", "页面跳转失败:", err);
      });
    };
    const navigateToWithdraw = () => {
      navigateTo("/subPackages/payment/pages/withdraw");
    };
    const navigateToRecharge = () => {
      navigateTo("/subPackages/payment/pages/recharge");
    };
    const getWalletBalance = () => {
      setTimeout(() => {
        balanceInfo.value = {
          amount: 158.5,
          frozenAmount: 0
        };
      }, 500);
    };
    common_vendor.onMounted(() => {
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight;
      navbarHeight.value = statusBarHeight.value + 44;
      getWalletBalance();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$5,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_vendor.t(balanceInfo.value.amount.toFixed(2)),
        e: common_vendor.o(navigateToWithdraw),
        f: common_vendor.o(navigateToRecharge),
        g: navbarHeight.value + 10 + "px",
        h: common_assets._imports_1$25,
        i: common_assets._imports_0$14,
        j: common_vendor.o(($event) => navigateTo("/subPackages/payment/pages/detail")),
        k: common_assets._imports_3$17,
        l: common_assets._imports_0$14,
        m: common_vendor.o(($event) => navigateTo("/subPackages/payment/pages/bills")),
        n: common_assets._imports_4$13,
        o: common_assets._imports_0$14,
        p: common_vendor.o(($event) => navigateTo("/subPackages/payment/pages/bank")),
        q: common_vendor.o(($event) => navigateTo("/subPackages/payment/pages/bills")),
        r: transactions.value.length > 0
      }, transactions.value.length > 0 ? {
        s: common_vendor.f(transactions.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.title),
            b: common_vendor.t(item.time),
            c: common_vendor.t(item.type === "income" ? "+" : "-"),
            d: common_vendor.t(item.amount.toFixed(2)),
            e: item.type === "income" ? 1 : "",
            f: item.type === "expense" ? 1 : "",
            g: index
          };
        })
      } : {
        t: common_assets._imports_1$3
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/payment/pages/index.js.map
