/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-ec46b593, html.data-v-ec46b593, #app.data-v-ec46b593, .index-container.data-v-ec46b593 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.coupon-detail-container.data-v-ec46b593 {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 120rpx;
  /* 为底部按钮留出空间 */
}

/* 自定义导航栏 */
.custom-navbar.data-v-ec46b593 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
}
.custom-navbar .navbar-bg.data-v-ec46b593 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FF9500 0%, #FFCC00 100%);
}
.custom-navbar .navbar-content.data-v-ec46b593 {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding-top: var(--status-bar-height, 25px);
  padding-left: 30rpx;
  padding-right: 30rpx;
  box-sizing: border-box;
}
.custom-navbar .back-btn.data-v-ec46b593 {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}
.custom-navbar .back-icon.data-v-ec46b593 {
  width: 100%;
  height: 100%;
}
.custom-navbar .navbar-title.data-v-ec46b593 {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

/* 加载状态 */
.loading-container.data-v-ec46b593 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}
.loading-container .loading-spinner.data-v-ec46b593 {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #FF9500;
  border-radius: 50%;
  animation: spin-ec46b593 1s linear infinite;
  margin-bottom: 20rpx;
}
.loading-container .loading-text.data-v-ec46b593 {
  font-size: 28rpx;
  color: #8E8E93;
}
@keyframes spin-ec46b593 {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
/* 优惠券信息卡片 */
.coupon-card.data-v-ec46b593 {
  background: linear-gradient(135deg, #FF9500 0%, #FFCC00 100%);
  border-radius: 35rpx;
  padding: 40rpx 30rpx;
  margin: 30rpx;
  box-shadow: 0 15rpx 35rpx rgba(255, 149, 0, 0.2);
  position: relative;
  overflow: hidden;
  /* 分销入口 */
}
.coupon-card.data-v-ec46b593::before {
  content: "";
  position: absolute;
  top: -100rpx;
  right: -100rpx;
  width: 300rpx;
  height: 300rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}
.coupon-card.data-v-ec46b593::after {
  content: "";
  position: absolute;
  bottom: -80rpx;
  left: -80rpx;
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}
.coupon-card .coupon-header.data-v-ec46b593 {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}
.coupon-card .coupon-amount.data-v-ec46b593 {
  display: flex;
  align-items: baseline;
  color: #FFFFFF;
}
.coupon-card .coupon-amount .amount-symbol.data-v-ec46b593 {
  font-size: 40rpx;
  font-weight: 600;
  margin-right: 5rpx;
}
.coupon-card .coupon-amount .amount-value.data-v-ec46b593 {
  font-size: 100rpx;
  font-weight: 800;
  line-height: 1;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}
.coupon-card .coupon-condition.data-v-ec46b593 {
  font-size: 28rpx;
  color: #FFFFFF;
  font-weight: 600;
  margin-top: 10rpx;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
}
.coupon-card .coupon-info.data-v-ec46b593 {
  text-align: center;
}
.coupon-card .coupon-title.data-v-ec46b593 {
  font-size: 36rpx;
  font-weight: 700;
  color: #FFFFFF;
  margin-bottom: 10rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.coupon-card .coupon-time.data-v-ec46b593 {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
}
.coupon-card .distribution-section.data-v-ec46b593 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 30rpx;
  margin-top: 20rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #FFF9F9 0%, #FFF5F5 100%);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 149, 0, 0.1);
}
.coupon-card .distribution-section .distribution-left.data-v-ec46b593 {
  display: flex;
  align-items: center;
}
.coupon-card .distribution-section .distribution-icon.data-v-ec46b593 {
  width: 60rpx;
  height: 60rpx;
  margin-right: 15rpx;
}
.coupon-card .distribution-section .distribution-icon .icon-image.data-v-ec46b593 {
  width: 100%;
  height: 100%;
}
.coupon-card .distribution-section .distribution-info.data-v-ec46b593 {
  display: flex;
  flex-direction: column;
}
.coupon-card .distribution-section .distribution-title.data-v-ec46b593 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 5rpx;
}
.coupon-card .distribution-section .distribution-desc.data-v-ec46b593 {
  font-size: 24rpx;
  color: #FF9500;
  font-weight: 600;
}
.coupon-card .distribution-section .distribution-right.data-v-ec46b593 {
  display: flex;
  align-items: center;
}
.coupon-card .distribution-section .distribution-btn.data-v-ec46b593 {
  font-size: 26rpx;
  color: #FF9500;
  font-weight: 600;
}
.coupon-card .distribution-section .arrow-icon.data-v-ec46b593 {
  font-size: 32rpx;
  color: #FF9500;
  margin-left: 5rpx;
}
.coupon-card .coupon-tag.data-v-ec46b593 {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  background-color: #FF3B30;
  color: #FFFFFF;
  font-size: 24rpx;
  font-weight: 600;
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.3);
  transform: rotate(15deg);
}

/* 使用说明卡片 */
.usage-card.data-v-ec46b593 {
  background-color: #FFFFFF;
  border-radius: 35rpx;
  padding: 30rpx;
  margin: 0 30rpx 30rpx 30rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);
}
.usage-card .card-header.data-v-ec46b593 {
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #EFEFEF;
  padding-bottom: 20rpx;
}
.usage-card .card-title.data-v-ec46b593 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.usage-card .usage-content.data-v-ec46b593 {
  display: flex;
  flex-direction: column;
}
.usage-card .usage-item.data-v-ec46b593 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15rpx;
}
.usage-card .usage-item .item-dot.data-v-ec46b593 {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #FF9500;
  margin-top: 12rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}
.usage-card .usage-item .item-text.data-v-ec46b593 {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}

/* 适用商品卡片 */
.products-card.data-v-ec46b593 {
  background-color: #FFFFFF;
  border-radius: 35rpx;
  padding: 30rpx;
  margin: 0 30rpx 30rpx 30rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);
}
.products-card .card-header.data-v-ec46b593 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #EFEFEF;
  padding-bottom: 20rpx;
}
.products-card .card-header .card-more.data-v-ec46b593 {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #8E8E93;
}
.products-card .card-header .card-more .arrow-icon.data-v-ec46b593 {
  font-size: 30rpx;
  margin-left: 5rpx;
}
.products-card .product-list.data-v-ec46b593 {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}
.products-card .product-item.data-v-ec46b593 {
  width: calc(33.33% - 20rpx);
  margin: 10rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.05);
  background-color: #FFFFFF;
  transition: transform 0.3s ease;
}
.products-card .product-item.data-v-ec46b593:active {
  transform: scale(0.98);
}
.products-card .product-item .product-image.data-v-ec46b593 {
  width: 100%;
  height: 180rpx;
  object-fit: cover;
}
.products-card .product-item .product-info.data-v-ec46b593 {
  padding: 15rpx;
}
.products-card .product-item .product-name.data-v-ec46b593 {
  font-size: 24rpx;
  color: #333333;
  line-height: 1.4;
  height: 68rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.products-card .product-item .product-price.data-v-ec46b593 {
  display: flex;
  align-items: baseline;
  margin-top: 10rpx;
}
.products-card .product-item .product-price .price-symbol.data-v-ec46b593 {
  font-size: 22rpx;
  color: #FF9500;
}
.products-card .product-item .product-price .price-value.data-v-ec46b593 {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF9500;
}

/* 底部按钮 */
.bottom-bar.data-v-ec46b593 {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100rpx;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.05);
  z-index: 99;
}
.bottom-bar .action-btn.data-v-ec46b593 {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 40rpx;
}
.bottom-bar .action-btn .icon.data-v-ec46b593 {
  margin-bottom: 5rpx;
}
.bottom-bar .action-btn text.data-v-ec46b593 {
  font-size: 22rpx;
  color: #8E8E93;
}
.bottom-bar .get-btn.data-v-ec46b593 {
  flex: 1;
  height: 80rpx;
  background: linear-gradient(135deg, #FF9500 0%, #FFCC00 100%);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 600;
  color: #FFFFFF;
  box-shadow: 0 8rpx 16rpx rgba(255, 149, 0, 0.2);
  transition: transform 0.3s ease;
}
.bottom-bar .get-btn.data-v-ec46b593:active {
  transform: translateY(5rpx);
  box-shadow: 0 4rpx 8rpx rgba(255, 149, 0, 0.15);
}