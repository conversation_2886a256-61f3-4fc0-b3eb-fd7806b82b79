server:
  port: 8081

spring:
  application:
    name: cizhou-auth
  
  profiles:
    active: embedded
  
  # 使用内嵌H2数据库
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:cizhou_admin;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL
    username: sa
    password: 
    
  h2:
    console:
      enabled: true
      path: /h2-console
      settings:
        web-allow-others: true
  
  # 使用内存Redis（或者不使用Redis）
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 0
      timeout: 3000ms
      # 如果没有Redis，可以禁用
      repositories:
        enabled: false

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: isDeleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:mapper/**/*.xml

# JWT配置
jwt:
  secret: cizhou-admin-secret-key-2024-very-long-secret
  expiration: 86400000
  refresh-expiration: 604800000
  issuer: cizhou-admin

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,h2
  endpoint:
    health:
      show-details: always

# 日志配置
logging:
  level:
    com.cizhou.auth: DEBUG
    com.cizhou.auth.mapper: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{50} - %msg%n"
