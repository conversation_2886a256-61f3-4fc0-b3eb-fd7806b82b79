{"version": 3, "file": "UserProfileCard.js", "sources": ["components/UserProfileCard.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7AvY29tcG9uZW50cy9Vc2VyUHJvZmlsZUNhcmQudnVl"], "sourcesContent": ["<template>\n  <view class=\"user-profile-card\">\n    <view class=\"user-profile-content\">\n      <view class=\"user-avatar-container\">\n        <image class=\"user-avatar\" :src=\"userInfo.avatar || '/static/images/default-avatar.png'\" mode=\"aspectFill\"></image>\n      </view>\n      <view class=\"user-info\">\n        <view class=\"user-name-row\">\n          <text class=\"user-name\">{{userInfo.nickname || '磁州居民'}}</text>\n          <view class=\"user-vip-tag\">VIP{{userInfo.vipLevel || 3}}</view>\n        </view>\n        <view class=\"user-id-row\">ID: {{userInfo.userId || '88965'}} · {{userInfo.joinDate || '2023年6月'}}入住</view>\n        <view class=\"user-bio\">{{userInfo.bio || '热爱生活，热爱磁州'}}</view>\n      </view>\n    </view>\n    <view class=\"card-actions\">\n      <button class=\"action-button edit-profile\" @click=\"handleEditProfile\">\n        编辑资料\n      </button>\n      <button class=\"action-button data-analysis\" @click=\"handleDataAnalysis\">\n        数据分析\n      </button>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'UserProfileCard',\n  props: {\n    userInfo: {\n      type: Object,\n      default: () => ({\n        nickname: '',\n        avatar: '',\n        userId: '',\n        joinDate: '',\n        bio: '',\n        vipLevel: 0\n      })\n    }\n  },\n  methods: {\n    handleEditProfile() {\n      this.$emit('edit-profile');\n    },\n    handleDataAnalysis() {\n      this.$emit('data-analysis');\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.user-profile-card {\n  width: 100%;\n  background-color: #ffffff;\n  border-radius: 16px;\n  padding: 20px;\n  margin-bottom: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\n  font-family: -apple-system, BlinkMacSystemFont, \"SF Pro Display\", \"SF Pro Text\", \"Helvetica Neue\", Arial, sans-serif;\n  \n  .user-profile-content {\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n    \n    .user-avatar-container {\n      position: relative;\n      margin-right: 16px;\n      \n      .user-avatar {\n        width: 64px;\n        height: 64px;\n        border-radius: 50%;\n        background-color: #f5f5f5;\n        border: 0.5px solid rgba(60, 60, 67, 0.1);\n        box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);\n      }\n    }\n    \n    .user-info {\n      flex: 1;\n      \n      .user-name-row {\n        display: flex;\n        flex-direction: row;\n        align-items: center;\n        margin-bottom: 6px;\n        \n        .user-name {\n          font-size: 18px;\n          font-weight: 600;\n          color: #1d1d1f;\n          margin-right: 8px;\n          letter-spacing: -0.2px;\n        }\n        \n        .user-vip-tag {\n          background-color: #007AFF;\n          color: #ffffff;\n          font-size: 12px;\n          padding: 2px 6px;\n          border-radius: 4px;\n          font-weight: 500;\n          letter-spacing: -0.1px;\n        }\n      }\n      \n      .user-id-row {\n        font-size: 13px;\n        color: #86868b;\n        margin-bottom: 6px;\n        letter-spacing: -0.1px;\n      }\n      \n      .user-bio {\n        font-size: 14px;\n        color: #515154;\n        white-space: nowrap;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        letter-spacing: -0.1px;\n        line-height: 1.4;\n      }\n    }\n  }\n  \n  .card-actions {\n    display: flex;\n    margin-top: 16px;\n    justify-content: space-between;\n    \n    .action-button {\n      width: 48%;\n      height: 36px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 15px;\n      border-radius: 8px;\n      padding: 0;\n      margin: 0;\n      line-height: 1;\n      font-weight: 500;\n      letter-spacing: -0.1px;\n      transition: all 0.2s ease;\n      \n      &.edit-profile {\n        background-color: #f5f5f7;\n        color: #1d1d1f;\n        \n        &:active {\n          background-color: #e5e5ea;\n        }\n      }\n      \n      &.data-analysis {\n        background-color: #007AFF;\n        color: #ffffff;\n        \n        &:active {\n          background-color: #0071e3;\n        }\n      }\n    }\n  }\n}\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/components/UserProfileCard.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;AA2BA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,IACL,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS,OAAO;AAAA,QACd,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,KAAK;AAAA,QACL,UAAU;AAAA;IAEd;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,oBAAoB;AAClB,WAAK,MAAM,cAAc;AAAA,IAC1B;AAAA,IACD,qBAAqB;AACnB,WAAK,MAAM,eAAe;AAAA,IAC5B;AAAA,EACF;AACF;;;;;;;;;;;;;;ACjDA,GAAG,gBAAgB,SAAS;"}