<template>
  <view class="rank-page">
    <!-- 顶部导航栏 -->
    <view class="nav-bg" :style="{ height: (statusBarHeight + 44) + 'px' }"></view>
    <view class="navbar" :style="{ top: statusBarHeight + 'px', height: '44px' }">
      <view class="navbar-left" @click="goBack">
        <image class="back-icon" src="/static/images/tabbar/最新返回键.png" mode="aspectFit"></image>
      </view>
      <text class="navbar-title">积分排行榜</text>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 内容容器 -->
    <view class="content-container" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
      <!-- 标题与说明 -->
      <view class="header-section">
        <view class="title-container">
          <text class="main-title">本月积分TOP榜</text>
          <view class="subtitle-container">
            <view class="subtitle-indicator"></view>
            <text class="subtitle">每月1日自动结算，榜单实时更新</text>
          </view>
        </view>
      </view>
      
      <!-- 顶部三名 -->
      <view class="top-three-section">
        <!-- 第二名 -->
        <view class="podium-item second-place">
          <view class="crown silver"></view>
          <view class="avatar-container">
            <image class="avatar" :src="top3[1].avatar" mode="aspectFill"></image>
            <view class="rank-badge silver">2</view>
          </view>
          <view class="user-info">
            <text class="nickname">{{ top3[1].nickname }}</text>
            <text class="points">{{ formatPoints(top3[1].points) }}</text>
          </view>
        </view>
        
        <!-- 第一名 -->
        <view class="podium-item first-place">
          <view class="crown gold"></view>
          <view class="avatar-container">
            <image class="avatar" :src="top3[0].avatar" mode="aspectFill"></image>
            <view class="rank-badge gold">1</view>
          </view>
          <view class="user-info">
            <text class="nickname">{{ top3[0].nickname }}</text>
            <text class="points">{{ formatPoints(top3[0].points) }}</text>
          </view>
        </view>
        
        <!-- 第三名 -->
        <view class="podium-item third-place">
          <view class="crown bronze"></view>
          <view class="avatar-container">
            <image class="avatar" :src="top3[2].avatar" mode="aspectFill"></image>
            <view class="rank-badge bronze">3</view>
          </view>
          <view class="user-info">
            <text class="nickname">{{ top3[2].nickname }}</text>
            <text class="points">{{ formatPoints(top3[2].points) }}</text>
          </view>
        </view>
      </view>
      
      <!-- 排行榜列表 -->
      <view class="rank-list-container">
        <view class="rank-list-header">
          <text class="rank-header-text">排名</text>
          <text class="rank-header-text user-col">用户</text>
          <text class="rank-header-text score-col">积分</text>
        </view>
        
        <scroll-view scroll-y class="rank-list">
          <view class="rank-item" v-for="(user, idx) in restList" :key="user.id">
            <view class="rank-number">{{ idx + 4 }}</view>
            <view class="rank-user">
              <image class="rank-avatar" :src="user.avatar" mode="aspectFill"></image>
              <text class="rank-nickname">{{ user.nickname }}</text>
            </view>
            <view class="rank-score">{{ formatPoints(user.points) }}</view>
          </view>
          
          <!-- 没有更多数据提示 -->
          <view class="no-more">
            <view class="no-more-line"></view>
            <text class="no-more-text">仅显示前100名</text>
            <view class="no-more-line"></view>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// Vue3迁移代码开始
// 响应式状态
const statusBarHeight = ref(20);
const maxRankDisplay = ref(100); // 最多显示100名用户

// 排行榜数据
const rankList = ref([
  { id: 1, nickname: '小明', avatar: '/static/images/avatar/avatar1.png', points: 3280 },
  { id: 2, nickname: '小红', avatar: '/static/images/avatar/avatar2.png', points: 2990 },
  { id: 3, nickname: '小刚', avatar: '/static/images/avatar/avatar3.png', points: 2650 },
  { id: 4, nickname: '小美', avatar: '/static/images/avatar/avatar4.png', points: 2200 },
  { id: 5, nickname: '小李', avatar: '/static/images/avatar/avatar5.png', points: 2100 },
  { id: 6, nickname: '小王', avatar: '/static/images/avatar/avatar6.png', points: 2000 },
  { id: 7, nickname: '小陈', avatar: '/static/images/avatar/avatar7.png', points: 1880 },
  { id: 8, nickname: '小赵', avatar: '/static/images/avatar/avatar8.png', points: 1700 },
  { id: 9, nickname: '小孙', avatar: '/static/images/avatar/avatar9.png', points: 1600 },
  { id: 10, nickname: '小周', avatar: '/static/images/avatar/avatar10.png', points: 1500 }
]);

// 计算属性
const top3 = computed(() => {
  return rankList.value.slice(0, 3);
});

const restList = computed(() => {
  // 只返回前100名（排除前3名后最多显示97名）
  return rankList.value.slice(3, maxRankDisplay.value);
});

// 生命周期钩子
onMounted(() => {
  try {
    const sysInfo = uni.getSystemInfoSync();
    statusBarHeight.value = sysInfo.statusBarHeight || 20;
  } catch (e) {
    statusBarHeight.value = 20;
  }
  
  // 实际应用中，这里会从服务器获取排行榜数据
  fetchRankData();
});

// 方法
function goBack() {
  uni.navigateBack();
}

function formatPoints(points) {
  return points + '分';
}

// 获取排行榜数据
function fetchRankData() {
  // 模拟API请求，实际项目中应替换为真实API调用
  // uni.request({
  //   url: 'api/points/rank',
  //   success: (res) => {
  //     if(res.data && res.data.list) {
  //       // 限制最多显示100名
  //       rankList.value = res.data.list.slice(0, maxRankDisplay.value);
  //     }
  //   }
  // });
}
// Vue3迁移代码结束
</script>

<style lang="scss">
page {
  background-color: #FFFFFF;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', Arial, sans-serif;
  color: #333333;
}

.rank-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 导航栏样式 */
.nav-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #3a86ff;
  z-index: 100;
}

.navbar {
  position: fixed;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  z-index: 101;
}

.navbar-left, .navbar-right {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24px;
  height: 24px;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 600;
  color: #FFFFFF;
}

/* 内容样式 */
.content-container {
  flex: 1;
  margin: 0 auto;
  width: 100%;
  max-width: 500px;
  display: flex;
  flex-direction: column;
}

/* 标题部分 */
.header-section {
  padding: 24px 20px 20px;
}

.title-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.main-title {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin-bottom: 12px;
  letter-spacing: 0.5px;
}

.subtitle-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.subtitle-indicator {
  width: 4px;
  height: 12px;
  background: #3a86ff;
  border-radius: 2px;
}

.subtitle {
  font-size: 13px;
  color: #888;
  font-weight: 400;
}

/* 前三名部分 */
.top-three-section {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  padding: 10px 16px 30px;
  position: relative;
  max-width: 100%;
  box-sizing: border-box;
}

.podium-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  width: 33.33%;
  box-sizing: border-box;
}

.first-place {
  z-index: 3;
}

.second-place {
  z-index: 2;
  transform: translateY(18px);
  padding-right: 5px;
}

.third-place {
  z-index: 1;
  transform: translateY(36px);
  padding-left: 5px;
}

.crown {
  width: 30px;
  height: 20px;
  margin-bottom: -5px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.crown.gold {
  background-image: linear-gradient(45deg, #FFD700, #FFC107);
  clip-path: polygon(50% 0%, 75% 50%, 100% 50%, 75% 100%, 25% 100%, 0% 50%, 25% 50%);
  width: 36px;
  height: 25px;
}

.crown.silver {
  background-image: linear-gradient(45deg, #C0C0C0, #A9A9A9);
  clip-path: polygon(50% 20%, 70% 50%, 90% 50%, 70% 100%, 30% 100%, 10% 50%, 30% 50%);
}

.crown.bronze {
  background-image: linear-gradient(45deg, #CD7F32, #B87333);
  clip-path: polygon(50% 20%, 70% 50%, 90% 50%, 70% 100%, 30% 100%, 10% 50%, 30% 50%);
}

.avatar-container {
  position: relative;
  margin-bottom: 8px;
}

.avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 3px solid #FFFFFF;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.first-place .avatar {
  width: 80px;
  height: 80px;
  border: 4px solid #FFD700;
}

.second-place .avatar {
  border: 3px solid #C0C0C0;
}

.third-place .avatar {
  border: 3px solid #CD7F32;
}

.rank-badge {
  position: absolute;
  right: -2px;
  bottom: -2px;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 700;
  color: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.first-place .rank-badge {
  width: 26px;
  height: 26px;
  font-size: 14px;
}

.rank-badge.gold {
  background: linear-gradient(135deg, #FFD700, #FFC107);
}

.rank-badge.silver {
  background: linear-gradient(135deg, #C0C0C0, #A9A9A9);
}

.rank-badge.bronze {
  background: linear-gradient(135deg, #CD7F32, #B87333);
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.nickname {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  box-sizing: border-box;
  padding: 0 2px;
}

.first-place .nickname {
  font-size: 16px;
  font-weight: 600;
}

.points {
  font-size: 15px;
  font-weight: 700;
  color: #3a86ff;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  box-sizing: border-box;
  padding: 0 2px;
}

.first-place .points {
  font-size: 18px;
}

/* 排行榜列表 */
.rank-list-container {
  flex: 1;
  background: #FFFFFF;
  border-radius: 20px 20px 0 0;
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  padding: 0;
  margin-top: 16px;
  width: 100%;
}

.rank-list-header {
  display: flex;
  padding: 16px 20px;
  border-bottom: 1px solid #F5F5F7;
  box-sizing: border-box;
  width: 100%;
}

.rank-header-text {
  font-size: 13px;
  color: #999;
  font-weight: 500;
}

.rank-header-text:first-child {
  width: 40px;
  flex-shrink: 0;
}

.rank-header-text.user-col {
  flex: 1;
  padding-left: 52px; /* 头像宽度40px + 间距12px */
}

.rank-header-text.score-col {
  width: 80px;
  flex-shrink: 0;
  text-align: right;
}

.rank-list {
  height: 50vh;
  padding: 0 20px;
  box-sizing: border-box;
  padding-right: 25px; /* 增加右侧内边距，避免被滚动条遮挡 */
}

.rank-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #F5F5F7;
  width: 100%;
  box-sizing: border-box;
}

.rank-number {
  width: 40px;
  font-size: 17px;
  font-weight: 600;
  color: #666;
  flex-shrink: 0;
}

.rank-user {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  overflow: hidden;
}

.rank-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid #F5F5F7;
  flex-shrink: 0;
}

.rank-nickname {
  font-size: 15px;
  color: #333;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.rank-score {
  width: 80px;
  flex-shrink: 0;
  text-align: right;
  font-size: 17px;
  font-weight: 600;
  color: #3a86ff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 5px; /* 为积分数字添加右侧内边距 */
}

/* 没有更多提示 */
.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  gap: 10px;
}

.no-more-line {
  height: 1px;
  flex: 1;
  background: #EFEFEF;
  max-width: 60px;
}

.no-more-text {
  font-size: 12px;
  color: #999;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: #aaa;
}
</style>