{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/distribution/records/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcZGlzdHJpYnV0aW9uXHJlY29yZHNcaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"records-container\">\r\n    <!-- 筛选标签栏 -->\r\n    <view class=\"filter-tabs\" :style=\"{\r\n      display: 'flex',\r\n      backgroundColor: '#FFFFFF',\r\n      borderRadius: '35px',\r\n      padding: '10rpx',\r\n      marginBottom: '30rpx',\r\n      boxShadow: '0 4px 10px rgba(0,0,0,0.05)'\r\n    }\">\r\n      <view \r\n        v-for=\"(tab, index) in tabs\" \r\n        :key=\"index\"\r\n        class=\"tab-item\"\r\n        :class=\"{ active: currentTab === index }\"\r\n        @click=\"switchTab(index)\"\r\n        :style=\"{\r\n          flex: '1',\r\n          textAlign: 'center',\r\n          padding: '15rpx 0',\r\n          borderRadius: '30rpx',\r\n          fontSize: '28rpx',\r\n          fontWeight: currentTab === index ? '600' : '400',\r\n          color: currentTab === index ? '#FFFFFF' : '#666666',\r\n          background: currentTab === index ? 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)' : 'transparent',\r\n          transition: 'all 0.3s ease'\r\n        }\"\r\n      >\r\n        {{ tab.name }}\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 日期筛选 -->\r\n    <view class=\"date-filter\" :style=\"{\r\n      display: 'flex',\r\n      justifyContent: 'space-between',\r\n      alignItems: 'center',\r\n      padding: '20rpx 30rpx',\r\n      backgroundColor: '#FFFFFF',\r\n      borderRadius: '35px',\r\n      marginBottom: '30rpx',\r\n      boxShadow: '0 4px 10px rgba(0,0,0,0.05)'\r\n    }\">\r\n      <view class=\"date-selector\" @click=\"showDatePicker('start')\" :style=\"{\r\n        display: 'flex',\r\n        alignItems: 'center'\r\n      }\">\r\n        <text :style=\"{\r\n          fontSize: '26rpx',\r\n          color: '#666666',\r\n          marginRight: '10rpx'\r\n        }\">开始日期:</text>\r\n        <text :style=\"{\r\n          fontSize: '26rpx',\r\n          color: '#333333',\r\n          fontWeight: '500'\r\n        }\">{{ startDate }}</text>\r\n      </view>\r\n      \r\n      <text :style=\"{\r\n        fontSize: '26rpx',\r\n        color: '#999999'\r\n      }\">至</text>\r\n      \r\n      <view class=\"date-selector\" @click=\"showDatePicker('end')\" :style=\"{\r\n        display: 'flex',\r\n        alignItems: 'center'\r\n      }\">\r\n        <text :style=\"{\r\n          fontSize: '26rpx',\r\n          color: '#666666',\r\n          marginRight: '10rpx'\r\n        }\">结束日期:</text>\r\n        <text :style=\"{\r\n          fontSize: '26rpx',\r\n          color: '#333333',\r\n          fontWeight: '500'\r\n        }\">{{ endDate }}</text>\r\n      </view>\r\n      \r\n      <view class=\"filter-btn\" @click=\"applyFilter\" :style=\"{\r\n        padding: '10rpx 20rpx',\r\n        borderRadius: '30rpx',\r\n        background: 'rgba(172,57,255,0.1)',\r\n        color: '#AC39FF',\r\n        fontSize: '26rpx',\r\n        fontWeight: '500'\r\n      }\">\r\n        筛选\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 统计卡片 -->\r\n    <view class=\"statistics-card\" :style=\"{\r\n      borderRadius: '35px',\r\n      boxShadow: '0 8px 20px rgba(172,57,255,0.15)',\r\n      background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)',\r\n      padding: '30rpx',\r\n      marginBottom: '30rpx',\r\n      position: 'relative',\r\n      overflow: 'hidden'\r\n    }\">\r\n      <!-- 背景装饰 -->\r\n      <view class=\"bg-decoration\" :style=\"{\r\n        position: 'absolute',\r\n        top: '-50rpx',\r\n        right: '-50rpx',\r\n        width: '300rpx',\r\n        height: '300rpx',\r\n        borderRadius: '50%',\r\n        background: 'rgba(255,255,255,0.1)',\r\n        zIndex: '1'\r\n      }\"></view>\r\n      <view class=\"bg-decoration\" :style=\"{\r\n        position: 'absolute',\r\n        bottom: '-80rpx',\r\n        left: '-80rpx',\r\n        width: '250rpx',\r\n        height: '250rpx',\r\n        borderRadius: '50%',\r\n        background: 'rgba(255,255,255,0.08)',\r\n        zIndex: '1'\r\n      }\"></view>\r\n      \r\n      <!-- 统计信息 -->\r\n      <view class=\"statistics-content\" :style=\"{\r\n        position: 'relative',\r\n        zIndex: '2'\r\n      }\">\r\n        <view class=\"period-info\" :style=\"{\r\n          display: 'flex',\r\n          justifyContent: 'space-between',\r\n          alignItems: 'center',\r\n          marginBottom: '20rpx'\r\n        }\">\r\n          <text :style=\"{\r\n            fontSize: '26rpx',\r\n            color: 'rgba(255,255,255,0.9)'\r\n          }\">{{ filterPeriodText }}</text>\r\n          \r\n          <view class=\"quick-filter\" :style=\"{\r\n            display: 'flex'\r\n          }\">\r\n            <view \r\n              v-for=\"(period, index) in quickPeriods\" \r\n              :key=\"index\"\r\n              class=\"period-item\"\r\n              :class=\"{ active: currentPeriod === index }\"\r\n              @click=\"selectQuickPeriod(index)\"\r\n              :style=\"{\r\n                fontSize: '24rpx',\r\n                color: currentPeriod === index ? '#AC39FF' : 'rgba(255,255,255,0.9)',\r\n                padding: '6rpx 16rpx',\r\n                borderRadius: '20rpx',\r\n                marginLeft: '10rpx',\r\n                background: currentPeriod === index ? '#FFFFFF' : 'rgba(255,255,255,0.1)'\r\n              }\"\r\n            >\r\n              {{ period.name }}\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"total-amount\" :style=\"{\r\n          marginBottom: '20rpx'\r\n        }\">\r\n          <text :style=\"{\r\n            fontSize: '26rpx',\r\n            color: 'rgba(255,255,255,0.9)',\r\n            marginBottom: '10rpx',\r\n            display: 'block'\r\n          }\">{{ tabs[currentTab].label }}(元)</text>\r\n          <text :style=\"{\r\n            fontSize: '50rpx',\r\n            fontWeight: 'bold',\r\n            color: '#FFFFFF',\r\n            display: 'block',\r\n            textShadow: '0 2rpx 4rpx rgba(0,0,0,0.1)'\r\n          }\">{{ totalAmount }}</text>\r\n        </view>\r\n        \r\n        <view class=\"statistics-details\" :style=\"{\r\n          display: 'flex',\r\n          justifyContent: 'space-around',\r\n          background: 'rgba(255,255,255,0.1)',\r\n          borderRadius: '20rpx',\r\n          padding: '20rpx 0'\r\n        }\">\r\n          <view class=\"detail-item\">\r\n            <text class=\"detail-value\" :style=\"{\r\n              fontSize: '32rpx',\r\n              fontWeight: 'bold',\r\n              color: '#FFFFFF',\r\n              display: 'block',\r\n              textAlign: 'center'\r\n            }\">{{ recordCount }}</text>\r\n            <text class=\"detail-label\" :style=\"{\r\n              fontSize: '24rpx',\r\n              color: 'rgba(255,255,255,0.8)',\r\n              display: 'block',\r\n              textAlign: 'center'\r\n            }\">记录数</text>\r\n          </view>\r\n          \r\n          <view class=\"detail-item\">\r\n            <text class=\"detail-value\" :style=\"{\r\n              fontSize: '32rpx',\r\n              fontWeight: 'bold',\r\n              color: '#FFFFFF',\r\n              display: 'block',\r\n              textAlign: 'center'\r\n            }\">{{ avgAmount }}</text>\r\n            <text class=\"detail-label\" :style=\"{\r\n              fontSize: '24rpx',\r\n              color: 'rgba(255,255,255,0.8)',\r\n              display: 'block',\r\n              textAlign: 'center'\r\n            }\">平均金额</text>\r\n          </view>\r\n          \r\n          <view class=\"detail-item\">\r\n            <text class=\"detail-value\" :style=\"{\r\n              fontSize: '32rpx',\r\n              fontWeight: 'bold',\r\n              color: '#FFFFFF',\r\n              display: 'block',\r\n              textAlign: 'center'\r\n            }\">{{ maxAmount }}</text>\r\n            <text class=\"detail-label\" :style=\"{\r\n              fontSize: '24rpx',\r\n              color: 'rgba(255,255,255,0.8)',\r\n              display: 'block',\r\n              textAlign: 'center'\r\n            }\">最高金额</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 记录列表 -->\r\n    <view class=\"records-list\" :style=\"{\r\n      borderRadius: '35px',\r\n      boxShadow: '0 8px 20px rgba(0,0,0,0.08)',\r\n      background: '#FFFFFF',\r\n      padding: '30rpx',\r\n      marginBottom: '30rpx'\r\n    }\">\r\n      <view class=\"list-header\" :style=\"{\r\n        display: 'flex',\r\n        justifyContent: 'space-between',\r\n        alignItems: 'center',\r\n        marginBottom: '20rpx'\r\n      }\">\r\n        <text :style=\"{\r\n          fontSize: '32rpx',\r\n          fontWeight: '600',\r\n          color: '#333333'\r\n        }\">收益明细</text>\r\n        \r\n        <view class=\"sort-filter\" @click=\"toggleSortMenu\" :style=\"{\r\n          display: 'flex',\r\n          alignItems: 'center'\r\n        }\">\r\n          <text :style=\"{\r\n            fontSize: '26rpx',\r\n            color: '#666666',\r\n            marginRight: '5rpx'\r\n          }\">{{ currentSortOption.name }}</text>\r\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n            <path d=\"M6 9l6 6 6-6\" stroke=\"#666666\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n          </svg>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 排序菜单 -->\r\n      <view v-if=\"showSortMenu\" class=\"sort-menu\" :style=\"{\r\n        position: 'absolute',\r\n        right: '30rpx',\r\n        top: '260rpx',\r\n        background: '#FFFFFF',\r\n        borderRadius: '20rpx',\r\n        boxShadow: '0 5px 15px rgba(0,0,0,0.1)',\r\n        zIndex: '100',\r\n        width: '200rpx',\r\n        overflow: 'hidden'\r\n      }\">\r\n        <view \r\n          v-for=\"(option, index) in sortOptions\" \r\n          :key=\"index\"\r\n          class=\"sort-option\"\r\n          @click=\"selectSortOption(index)\"\r\n          :style=\"{\r\n            padding: '20rpx',\r\n            fontSize: '26rpx',\r\n            color: currentSortIndex === index ? '#AC39FF' : '#333333',\r\n            fontWeight: currentSortIndex === index ? '500' : '400',\r\n            borderBottom: index < sortOptions.length - 1 ? '1rpx solid #F2F2F7' : 'none',\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            justifyContent: 'space-between'\r\n          }\"\r\n        >\r\n          <text>{{ option.name }}</text>\r\n          <svg v-if=\"currentSortIndex === index\" class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n            <path d=\"M5 12l5 5L20 7\" stroke=\"#AC39FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n          </svg>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 记录项目 -->\r\n      <view class=\"records-items\">\r\n        <view \r\n          v-for=\"(record, index) in filteredRecords\" \r\n          :key=\"index\"\r\n          class=\"record-item\"\r\n          :style=\"{\r\n            padding: '20rpx 0',\r\n            borderBottom: index < filteredRecords.length - 1 ? '1rpx solid #F2F2F7' : 'none',\r\n            display: 'flex',\r\n            alignItems: 'center'\r\n          }\"\r\n        >\r\n          <view class=\"record-icon\" :style=\"{\r\n            width: '80rpx',\r\n            height: '80rpx',\r\n            borderRadius: '50%',\r\n            background: getRecordTypeBg(record.type),\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            justifyContent: 'center',\r\n            marginRight: '20rpx'\r\n          }\">\r\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n              <path :d=\"getRecordTypeIcon(record.type)\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            </svg>\r\n          </view>\r\n          \r\n          <view class=\"record-info\" :style=\"{ flex: '1' }\">\r\n            <view class=\"record-top\" :style=\"{\r\n              display: 'flex',\r\n              justifyContent: 'space-between',\r\n              marginBottom: '5rpx'\r\n            }\">\r\n              <text class=\"record-title\" :style=\"{\r\n                fontSize: '28rpx',\r\n                fontWeight: '500',\r\n                color: '#333333'\r\n              }\">{{ record.title }}</text>\r\n              \r\n              <text class=\"record-amount\" :style=\"{\r\n                fontSize: '28rpx',\r\n                fontWeight: '600',\r\n                color: record.type === 'withdraw' ? '#FF3B30' : '#34C759'\r\n              }\">{{ record.type === 'withdraw' ? '-' : '+' }}{{ record.amount }}</text>\r\n            </view>\r\n            \r\n            <view class=\"record-bottom\" :style=\"{\r\n              display: 'flex',\r\n              justifyContent: 'space-between'\r\n            }\">\r\n              <text class=\"record-source\" :style=\"{\r\n                fontSize: '24rpx',\r\n                color: '#999999'\r\n              }\">{{ record.source }}</text>\r\n              \r\n              <text class=\"record-time\" :style=\"{\r\n                fontSize: '24rpx',\r\n                color: '#999999'\r\n              }\">{{ record.time }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 空状态 -->\r\n        <view v-if=\"filteredRecords.length === 0\" class=\"empty-state\" :style=\"{\r\n          padding: '50rpx 0',\r\n          display: 'flex',\r\n          flexDirection: 'column',\r\n          alignItems: 'center'\r\n        }\">\r\n          <image src=\"/static/images/empty/empty-records.png\" mode=\"aspectFit\" :style=\"{\r\n            width: '200rpx',\r\n            height: '200rpx',\r\n            marginBottom: '20rpx'\r\n          }\"></image>\r\n          <text :style=\"{\r\n            fontSize: '26rpx',\r\n            color: '#999999'\r\n          }\">暂无收益记录</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 加载更多 -->\r\n      <view v-if=\"filteredRecords.length > 0\" class=\"load-more\" :style=\"{\r\n        textAlign: 'center',\r\n        padding: '30rpx 0'\r\n      }\">\r\n        <text v-if=\"loading\" :style=\"{\r\n          fontSize: '26rpx',\r\n          color: '#999999'\r\n        }\">加载中...</text>\r\n        <text v-else-if=\"noMore\" :style=\"{\r\n          fontSize: '26rpx',\r\n          color: '#999999'\r\n        }\">没有更多数据了</text>\r\n        <text v-else @click=\"loadMore\" :style=\"{\r\n          fontSize: '26rpx',\r\n          color: '#AC39FF'\r\n        }\">加载更多</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 底部安全区域 -->\r\n    <view class=\"safe-area-bottom\" :style=\"{\r\n      height: '100rpx',\r\n      paddingBottom: 'env(safe-area-inset-bottom)'\r\n    }\"></view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed } from 'vue';\r\n\r\n// 标签页数据\r\nconst tabs = ref([\r\n  { name: '全部', status: 'all', label: '总收益' },\r\n  { name: '收入', status: 'income', label: '总收入' },\r\n  { name: '提现', status: 'withdraw', label: '总提现' }\r\n]);\r\nconst currentTab = ref(0);\r\n\r\n// 日期筛选\r\nconst startDate = ref('2023-05-01');\r\nconst endDate = ref('2023-05-31');\r\nconst currentPickerType = ref('');\r\n\r\n// 快速时间段筛选\r\nconst quickPeriods = ref([\r\n  { name: '今天', days: 0 },\r\n  { name: '7天', days: 7 },\r\n  { name: '30天', days: 30 },\r\n  { name: '全部', days: -1 }\r\n]);\r\nconst currentPeriod = ref(2); // 默认选择30天\r\n\r\n// 排序选项\r\nconst sortOptions = ref([\r\n  { name: '时间降序', field: 'time', order: 'desc' },\r\n  { name: '时间升序', field: 'time', order: 'asc' },\r\n  { name: '金额降序', field: 'amount', order: 'desc' },\r\n  { name: '金额升序', field: 'amount', order: 'asc' }\r\n]);\r\nconst currentSortIndex = ref(0);\r\nconst currentSortOption = computed(() => sortOptions.value[currentSortIndex.value]);\r\nconst showSortMenu = ref(false);\r\n\r\n// 加载状态\r\nconst loading = ref(false);\r\nconst noMore = ref(false);\r\n\r\n// 收益记录数据\r\nconst records = ref([\r\n  {\r\n    id: 1,\r\n    title: '直接佣金',\r\n    amount: '10.50',\r\n    source: '订单号: 2023051500001',\r\n    time: '2023-05-15 14:30:25',\r\n    type: 'commission'\r\n  },\r\n  {\r\n    id: 2,\r\n    title: '团队佣金',\r\n    amount: '5.20',\r\n    source: '来自: 张小明',\r\n    time: '2023-05-14 09:15:36',\r\n    type: 'team'\r\n  },\r\n  {\r\n    id: 3,\r\n    title: '提现',\r\n    amount: '100.00',\r\n    source: '提现到微信零钱',\r\n    time: '2023-05-10 16:42:18',\r\n    type: 'withdraw'\r\n  },\r\n  {\r\n    id: 4,\r\n    title: '活动奖励',\r\n    amount: '20.00',\r\n    source: '新人推广活动',\r\n    time: '2023-05-08 11:23:45',\r\n    type: 'reward'\r\n  },\r\n  {\r\n    id: 5,\r\n    title: '直接佣金',\r\n    amount: '15.80',\r\n    source: '订单号: 2023050700002',\r\n    time: '2023-05-07 18:05:12',\r\n    type: 'commission'\r\n  }\r\n]);\r\n\r\n// 过滤后的记录\r\nconst filteredRecords = computed(() => {\r\n  // 根据标签过滤\r\n  let result = records.value;\r\n  \r\n  if (tabs.value[currentTab.value].status !== 'all') {\r\n    if (tabs.value[currentTab.value].status === 'income') {\r\n      result = result.filter(record => record.type !== 'withdraw');\r\n    } else {\r\n      result = result.filter(record => record.type === tabs.value[currentTab.value].status);\r\n    }\r\n  }\r\n  \r\n  // 根据日期过滤\r\n  const startTimestamp = new Date(startDate.value).getTime();\r\n  const endTimestamp = new Date(endDate.value).getTime() + 24 * 60 * 60 * 1000 - 1; // 结束日期的最后一毫秒\r\n  \r\n  result = result.filter(record => {\r\n    const recordTime = new Date(record.time).getTime();\r\n    return recordTime >= startTimestamp && recordTime <= endTimestamp;\r\n  });\r\n  \r\n  // 根据排序选项排序\r\n  const { field, order } = currentSortOption.value;\r\n  \r\n  result.sort((a, b) => {\r\n    let comparison = 0;\r\n    \r\n    if (field === 'time') {\r\n      comparison = new Date(a.time).getTime() - new Date(b.time).getTime();\r\n    } else if (field === 'amount') {\r\n      comparison = parseFloat(a.amount) - parseFloat(b.amount);\r\n    }\r\n    \r\n    return order === 'asc' ? comparison : -comparison;\r\n  });\r\n  \r\n  return result;\r\n});\r\n\r\n// 统计数据\r\nconst totalAmount = computed(() => {\r\n  let total = 0;\r\n  \r\n  filteredRecords.value.forEach(record => {\r\n    if (tabs.value[currentTab.value].status === 'all') {\r\n      if (record.type === 'withdraw') {\r\n        total -= parseFloat(record.amount);\r\n      } else {\r\n        total += parseFloat(record.amount);\r\n      }\r\n    } else if (tabs.value[currentTab.value].status === 'income') {\r\n      if (record.type !== 'withdraw') {\r\n        total += parseFloat(record.amount);\r\n      }\r\n    } else if (tabs.value[currentTab.value].status === 'withdraw') {\r\n      total += parseFloat(record.amount);\r\n    }\r\n  });\r\n  \r\n  return total.toFixed(2);\r\n});\r\n\r\nconst recordCount = computed(() => filteredRecords.value.length);\r\n\r\nconst avgAmount = computed(() => {\r\n  if (filteredRecords.value.length === 0) return '0.00';\r\n  \r\n  let total = 0;\r\n  filteredRecords.value.forEach(record => {\r\n    total += parseFloat(record.amount);\r\n  });\r\n  \r\n  return (total / filteredRecords.value.length).toFixed(2);\r\n});\r\n\r\nconst maxAmount = computed(() => {\r\n  if (filteredRecords.value.length === 0) return '0.00';\r\n  \r\n  let max = 0;\r\n  filteredRecords.value.forEach(record => {\r\n    const amount = parseFloat(record.amount);\r\n    if (amount > max) {\r\n      max = amount;\r\n    }\r\n  });\r\n  \r\n  return max.toFixed(2);\r\n});\r\n\r\n// 筛选周期文本\r\nconst filterPeriodText = computed(() => {\r\n  return `${startDate.value} 至 ${endDate.value}`;\r\n});\r\n\r\n// 切换标签页\r\nfunction switchTab(index) {\r\n  currentTab.value = index;\r\n}\r\n\r\n// 显示日期选择器\r\nfunction showDatePicker(type) {\r\n  currentPickerType.value = type;\r\n  \r\n  uni.showToast({\r\n    title: '日期选择功能开发中',\r\n    icon: 'none'\r\n  });\r\n  \r\n  // 实际开发中应该使用日期选择器组件\r\n  // uni.showDatePicker({\r\n  //   success: (res) => {\r\n  //     if (currentPickerType.value === 'start') {\r\n  //       startDate.value = res.date;\r\n  //     } else {\r\n  //       endDate.value = res.date;\r\n  //     }\r\n  //   }\r\n  // });\r\n}\r\n\r\n// 应用筛选\r\nfunction applyFilter() {\r\n  // 这里可以添加筛选逻辑\r\n  uni.showToast({\r\n    title: '筛选已应用',\r\n    icon: 'success'\r\n  });\r\n}\r\n\r\n// 选择快速时间段\r\nfunction selectQuickPeriod(index) {\r\n  currentPeriod.value = index;\r\n  \r\n  const today = new Date();\r\n  const days = quickPeriods.value[index].days;\r\n  \r\n  if (days === -1) {\r\n    // 全部\r\n    startDate.value = '2023-01-01';\r\n    endDate.value = formatDate(today);\r\n  } else if (days === 0) {\r\n    // 今天\r\n    startDate.value = formatDate(today);\r\n    endDate.value = formatDate(today);\r\n  } else {\r\n    // 其他天数\r\n    const pastDate = new Date();\r\n    pastDate.setDate(today.getDate() - days);\r\n    startDate.value = formatDate(pastDate);\r\n    endDate.value = formatDate(today);\r\n  }\r\n}\r\n\r\n// 格式化日期\r\nfunction formatDate(date) {\r\n  const year = date.getFullYear();\r\n  const month = String(date.getMonth() + 1).padStart(2, '0');\r\n  const day = String(date.getDate()).padStart(2, '0');\r\n  return `${year}-${month}-${day}`;\r\n}\r\n\r\n// 切换排序菜单\r\nfunction toggleSortMenu() {\r\n  showSortMenu.value = !showSortMenu.value;\r\n}\r\n\r\n// 选择排序选项\r\nfunction selectSortOption(index) {\r\n  currentSortIndex.value = index;\r\n  showSortMenu.value = false;\r\n}\r\n\r\n// 获取记录类型背景色\r\nfunction getRecordTypeBg(type) {\r\n  switch (type) {\r\n    case 'commission':\r\n      return 'linear-gradient(135deg, #34C759 0%, #30D158 100%)';\r\n    case 'team':\r\n      return 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)';\r\n    case 'withdraw':\r\n      return 'linear-gradient(135deg, #FF3B30 0%, #FF5E3A 100%)';\r\n    case 'reward':\r\n      return 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)';\r\n    default:\r\n      return 'linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)';\r\n  }\r\n}\r\n\r\n// 获取记录类型图标\r\nfunction getRecordTypeIcon(type) {\r\n  switch (type) {\r\n    case 'commission':\r\n      return 'M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6';\r\n    case 'team':\r\n      return 'M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2M9 11a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM23 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75';\r\n    case 'withdraw':\r\n      return 'M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4M17 8l-5-5-5 5M12 3v12';\r\n    case 'reward':\r\n      return 'M12 15c3 0 6-2 6-6s-3-6-6-6-6 2-6 6 3 6 6 6zM2.5 9h4M17.5 9h4M12 15v8M8 21h8';\r\n    default:\r\n      return 'M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z';\r\n  }\r\n}\r\n\r\n// 加载更多\r\nfunction loadMore() {\r\n  if (loading.value || noMore.value) return;\r\n  \r\n  loading.value = true;\r\n  \r\n  // 模拟加载更多数据\r\n  setTimeout(() => {\r\n    // 这里应该调用API获取更多数据\r\n    // 模拟没有更多数据\r\n    noMore.value = true;\r\n    loading.value = false;\r\n  }, 1500);\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.records-container {\r\n  padding: 30rpx;\r\n  background-color: #F2F2F7;\r\n  min-height: 100vh;\r\n  position: relative;\r\n}\r\n\r\n.filter-btn:active, .sort-option:active, .load-more text:active {\r\n  opacity: 0.8;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/distribution/records/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni"], "mappings": ";;;;;;;;;;;AAyaA,UAAM,OAAOA,cAAAA,IAAI;AAAA,MACf,EAAE,MAAM,MAAM,QAAQ,OAAO,OAAO,MAAO;AAAA,MAC3C,EAAE,MAAM,MAAM,QAAQ,UAAU,OAAO,MAAO;AAAA,MAC9C,EAAE,MAAM,MAAM,QAAQ,YAAY,OAAO,MAAO;AAAA,IAClD,CAAC;AACD,UAAM,aAAaA,cAAAA,IAAI,CAAC;AAGxB,UAAM,YAAYA,cAAAA,IAAI,YAAY;AAClC,UAAM,UAAUA,cAAAA,IAAI,YAAY;AAChC,UAAM,oBAAoBA,cAAAA,IAAI,EAAE;AAGhC,UAAM,eAAeA,cAAAA,IAAI;AAAA,MACvB,EAAE,MAAM,MAAM,MAAM,EAAG;AAAA,MACvB,EAAE,MAAM,MAAM,MAAM,EAAG;AAAA,MACvB,EAAE,MAAM,OAAO,MAAM,GAAI;AAAA,MACzB,EAAE,MAAM,MAAM,MAAM,GAAI;AAAA,IAC1B,CAAC;AACD,UAAM,gBAAgBA,cAAAA,IAAI,CAAC;AAG3B,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB,EAAE,MAAM,QAAQ,OAAO,QAAQ,OAAO,OAAQ;AAAA,MAC9C,EAAE,MAAM,QAAQ,OAAO,QAAQ,OAAO,MAAO;AAAA,MAC7C,EAAE,MAAM,QAAQ,OAAO,UAAU,OAAO,OAAQ;AAAA,MAChD,EAAE,MAAM,QAAQ,OAAO,UAAU,OAAO,MAAO;AAAA,IACjD,CAAC;AACD,UAAM,mBAAmBA,cAAAA,IAAI,CAAC;AAC9B,UAAM,oBAAoBC,cAAQ,SAAC,MAAM,YAAY,MAAM,iBAAiB,KAAK,CAAC;AAClF,UAAM,eAAeD,cAAAA,IAAI,KAAK;AAG9B,UAAM,UAAUA,cAAAA,IAAI,KAAK;AACzB,UAAM,SAASA,cAAAA,IAAI,KAAK;AAGxB,UAAM,UAAUA,cAAAA,IAAI;AAAA,MAClB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AAAA,IACH,CAAC;AAGD,UAAM,kBAAkBC,cAAQ,SAAC,MAAM;AAErC,UAAI,SAAS,QAAQ;AAErB,UAAI,KAAK,MAAM,WAAW,KAAK,EAAE,WAAW,OAAO;AACjD,YAAI,KAAK,MAAM,WAAW,KAAK,EAAE,WAAW,UAAU;AACpD,mBAAS,OAAO,OAAO,YAAU,OAAO,SAAS,UAAU;AAAA,QACjE,OAAW;AACL,mBAAS,OAAO,OAAO,YAAU,OAAO,SAAS,KAAK,MAAM,WAAW,KAAK,EAAE,MAAM;AAAA,QACrF;AAAA,MACF;AAGD,YAAM,iBAAiB,IAAI,KAAK,UAAU,KAAK,EAAE;AACjD,YAAM,eAAe,IAAI,KAAK,QAAQ,KAAK,EAAE,QAAS,IAAG,KAAK,KAAK,KAAK,MAAO;AAE/E,eAAS,OAAO,OAAO,YAAU;AAC/B,cAAM,aAAa,IAAI,KAAK,OAAO,IAAI,EAAE;AACzC,eAAO,cAAc,kBAAkB,cAAc;AAAA,MACzD,CAAG;AAGD,YAAM,EAAE,OAAO,UAAU,kBAAkB;AAE3C,aAAO,KAAK,CAAC,GAAG,MAAM;AACpB,YAAI,aAAa;AAEjB,YAAI,UAAU,QAAQ;AACpB,uBAAa,IAAI,KAAK,EAAE,IAAI,EAAE,YAAY,IAAI,KAAK,EAAE,IAAI,EAAE,QAAO;AAAA,QACxE,WAAe,UAAU,UAAU;AAC7B,uBAAa,WAAW,EAAE,MAAM,IAAI,WAAW,EAAE,MAAM;AAAA,QACxD;AAED,eAAO,UAAU,QAAQ,aAAa,CAAC;AAAA,MAC3C,CAAG;AAED,aAAO;AAAA,IACT,CAAC;AAGD,UAAM,cAAcA,cAAQ,SAAC,MAAM;AACjC,UAAI,QAAQ;AAEZ,sBAAgB,MAAM,QAAQ,YAAU;AACtC,YAAI,KAAK,MAAM,WAAW,KAAK,EAAE,WAAW,OAAO;AACjD,cAAI,OAAO,SAAS,YAAY;AAC9B,qBAAS,WAAW,OAAO,MAAM;AAAA,UACzC,OAAa;AACL,qBAAS,WAAW,OAAO,MAAM;AAAA,UAClC;AAAA,QACP,WAAe,KAAK,MAAM,WAAW,KAAK,EAAE,WAAW,UAAU;AAC3D,cAAI,OAAO,SAAS,YAAY;AAC9B,qBAAS,WAAW,OAAO,MAAM;AAAA,UAClC;AAAA,QACP,WAAe,KAAK,MAAM,WAAW,KAAK,EAAE,WAAW,YAAY;AAC7D,mBAAS,WAAW,OAAO,MAAM;AAAA,QAClC;AAAA,MACL,CAAG;AAED,aAAO,MAAM,QAAQ,CAAC;AAAA,IACxB,CAAC;AAED,UAAM,cAAcA,cAAAA,SAAS,MAAM,gBAAgB,MAAM,MAAM;AAE/D,UAAM,YAAYA,cAAQ,SAAC,MAAM;AAC/B,UAAI,gBAAgB,MAAM,WAAW;AAAG,eAAO;AAE/C,UAAI,QAAQ;AACZ,sBAAgB,MAAM,QAAQ,YAAU;AACtC,iBAAS,WAAW,OAAO,MAAM;AAAA,MACrC,CAAG;AAED,cAAQ,QAAQ,gBAAgB,MAAM,QAAQ,QAAQ,CAAC;AAAA,IACzD,CAAC;AAED,UAAM,YAAYA,cAAQ,SAAC,MAAM;AAC/B,UAAI,gBAAgB,MAAM,WAAW;AAAG,eAAO;AAE/C,UAAI,MAAM;AACV,sBAAgB,MAAM,QAAQ,YAAU;AACtC,cAAM,SAAS,WAAW,OAAO,MAAM;AACvC,YAAI,SAAS,KAAK;AAChB,gBAAM;AAAA,QACP;AAAA,MACL,CAAG;AAED,aAAO,IAAI,QAAQ,CAAC;AAAA,IACtB,CAAC;AAGD,UAAM,mBAAmBA,cAAQ,SAAC,MAAM;AACtC,aAAO,GAAG,UAAU,KAAK,MAAM,QAAQ,KAAK;AAAA,IAC9C,CAAC;AAGD,aAAS,UAAU,OAAO;AACxB,iBAAW,QAAQ;AAAA,IACrB;AAGA,aAAS,eAAe,MAAM;AAC5B,wBAAkB,QAAQ;AAE1BC,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IAYH;AAGA,aAAS,cAAc;AAErBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,aAAS,kBAAkB,OAAO;AAChC,oBAAc,QAAQ;AAEtB,YAAM,QAAQ,oBAAI;AAClB,YAAM,OAAO,aAAa,MAAM,KAAK,EAAE;AAEvC,UAAI,SAAS,IAAI;AAEf,kBAAU,QAAQ;AAClB,gBAAQ,QAAQ,WAAW,KAAK;AAAA,MACpC,WAAa,SAAS,GAAG;AAErB,kBAAU,QAAQ,WAAW,KAAK;AAClC,gBAAQ,QAAQ,WAAW,KAAK;AAAA,MACpC,OAAS;AAEL,cAAM,WAAW,oBAAI;AACrB,iBAAS,QAAQ,MAAM,QAAS,IAAG,IAAI;AACvC,kBAAU,QAAQ,WAAW,QAAQ;AACrC,gBAAQ,QAAQ,WAAW,KAAK;AAAA,MACjC;AAAA,IACH;AAGA,aAAS,WAAW,MAAM;AACxB,YAAM,OAAO,KAAK;AAClB,YAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,YAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAChC;AAGA,aAAS,iBAAiB;AACxB,mBAAa,QAAQ,CAAC,aAAa;AAAA,IACrC;AAGA,aAAS,iBAAiB,OAAO;AAC/B,uBAAiB,QAAQ;AACzB,mBAAa,QAAQ;AAAA,IACvB;AAGA,aAAS,gBAAgB,MAAM;AAC7B,cAAQ,MAAI;AAAA,QACV,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,aAAS,kBAAkB,MAAM;AAC/B,cAAQ,MAAI;AAAA,QACV,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,aAAS,WAAW;AAClB,UAAI,QAAQ,SAAS,OAAO;AAAO;AAEnC,cAAQ,QAAQ;AAGhB,iBAAW,MAAM;AAGf,eAAO,QAAQ;AACf,gBAAQ,QAAQ;AAAA,MACjB,GAAE,IAAI;AAAA,IACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACltBA,GAAG,WAAW,eAAe;"}