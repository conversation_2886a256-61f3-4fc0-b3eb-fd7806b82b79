<view class="qrcode-container"><view class="navbar"><view class="navbar-back" bindtap="{{a}}"><view class="back-icon"></view></view><text class="navbar-title">推广二维码</text><view class="navbar-right"><view class="help-icon" bindtap="{{b}}">?</view></view></view><view class="qrcode-preview-section"><view class="preview-card" style="{{'background-color:' + k}}"><view class="preview-header"><image class="store-logo" src="{{c}}" mode="aspectFill"></image><view class="store-info"><text class="store-name">{{d}}</text><text class="store-slogan">{{e}}</text></view></view><view class="qrcode-wrapper"><view class="qrcode-image-container"><image class="qrcode-image" src="{{f}}" mode="aspectFit"></image><view wx:if="{{g}}" class="qrcode-logo"><image src="{{h}}" mode="aspectFill"></image></view></view></view><view class="preview-footer"><text class="scan-tip">{{i}}</text><text class="distributor-id">推广员ID: {{j}}</text></view></view><view class="action-buttons"><button class="action-button save" bindtap="{{p}}"><view class="button-icon"><svg wx:if="{{o}}" u-s="{{['d']}}" u-i="4bf3f175-0" bind:__l="__l" u-p="{{o}}"><path wx:if="{{l}}" u-i="4bf3f175-1,4bf3f175-0" bind:__l="__l" u-p="{{l}}"/><path wx:if="{{m}}" u-i="4bf3f175-2,4bf3f175-0" bind:__l="__l" u-p="{{m}}"/><path wx:if="{{n}}" u-i="4bf3f175-3,4bf3f175-0" bind:__l="__l" u-p="{{n}}"/></svg></view><text>保存到相册</text></button><button class="action-button share" bindtap="{{x}}"><view class="button-icon"><svg wx:if="{{w}}" u-s="{{['d']}}" u-i="4bf3f175-4" bind:__l="__l" u-p="{{w}}"><circle wx:if="{{q}}" u-i="4bf3f175-5,4bf3f175-4" bind:__l="__l" u-p="{{q}}"/><circle wx:if="{{r}}" u-i="4bf3f175-6,4bf3f175-4" bind:__l="__l" u-p="{{r}}"/><circle wx:if="{{s}}" u-i="4bf3f175-7,4bf3f175-4" bind:__l="__l" u-p="{{s}}"/><line wx:if="{{t}}" u-i="4bf3f175-8,4bf3f175-4" bind:__l="__l" u-p="{{t}}"/><line wx:if="{{v}}" u-i="4bf3f175-9,4bf3f175-4" bind:__l="__l" u-p="{{v}}"/></svg></view><text>分享二维码</text></button></view></view><view class="customization-section"><view class="option-group"><text class="option-title">二维码类型</text><view class="type-options"><view wx:for="{{y}}" wx:for-item="type" wx:key="c" class="{{['type-option', type.d && 'active']}}" bindtap="{{type.e}}"><view class="option-icon"><rich-text nodes="{{type.a}}"/></view><text class="option-name">{{type.b}}</text></view></view></view><view class="option-group"><text class="option-title">主题颜色</text><view class="theme-options"><view wx:for="{{z}}" wx:for-item="theme" wx:key="f" class="{{['theme-option', theme.g && 'active']}}" style="{{'background-color:' + theme.h}}" bindtap="{{theme.i}}"><view wx:if="{{theme.a}}" class="theme-check"><svg wx:if="{{theme.e}}" u-s="{{['d']}}" u-i="{{theme.d}}" bind:__l="__l" u-p="{{theme.e}}"><path wx:if="{{theme.c}}" u-i="{{theme.b}}" bind:__l="__l" u-p="{{theme.c}}"/></svg></view></view></view></view><view class="option-group"><view class="toggle-option"><text class="toggle-label">显示店铺LOGO</text><switch checked="{{A}}" bindchange="{{B}}" color="#6B0FBE"/></view><view class="toggle-option"><text class="toggle-label">显示推广员ID</text><switch checked="{{C}}" bindchange="{{D}}" color="#6B0FBE"/></view></view><view class="option-group"><text class="option-title">自定义文案</text><input class="custom-input" type="text" placeholder="输入自定义文案（最多20字）" maxlength="20" value="{{E}}" bindinput="{{F}}"/></view></view><view class="history-section"><view class="section-header"><text class="section-title">历史记录</text><text class="clear-history" bindtap="{{G}}">清空</text></view><scroll-view class="history-scroll" scroll-x><view class="history-list"><view wx:for="{{H}}" wx:for-item="item" wx:key="c" class="history-item" bindtap="{{item.d}}"><image class="history-image" src="{{item.a}}" mode="aspectFit"></image><text class="history-date">{{item.b}}</text></view></view></scroll-view></view></view>