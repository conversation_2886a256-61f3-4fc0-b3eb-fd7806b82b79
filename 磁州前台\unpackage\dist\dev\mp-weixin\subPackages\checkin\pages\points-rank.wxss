/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
page {
  background-color: #FFFFFF;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
  color: #333333;
}
.rank-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 导航栏样式 */
.nav-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #3a86ff;
  z-index: 100;
}
.navbar {
  position: fixed;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  z-index: 101;
}
.navbar-left, .navbar-right {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 24px;
  height: 24px;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 600;
  color: #FFFFFF;
}

/* 内容样式 */
.content-container {
  flex: 1;
  margin: 0 auto;
  width: 100%;
  max-width: 500px;
  display: flex;
  flex-direction: column;
}

/* 标题部分 */
.header-section {
  padding: 24px 20px 20px;
}
.title-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.main-title {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin-bottom: 12px;
  letter-spacing: 0.5px;
}
.subtitle-container {
  display: flex;
  align-items: center;
  gap: 8px;
}
.subtitle-indicator {
  width: 4px;
  height: 12px;
  background: #3a86ff;
  border-radius: 2px;
}
.subtitle {
  font-size: 13px;
  color: #888;
  font-weight: 400;
}

/* 前三名部分 */
.top-three-section {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  padding: 10px 16px 30px;
  position: relative;
  max-width: 100%;
  box-sizing: border-box;
}
.podium-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  width: 33.33%;
  box-sizing: border-box;
}
.first-place {
  z-index: 3;
}
.second-place {
  z-index: 2;
  transform: translateY(18px);
  padding-right: 5px;
}
.third-place {
  z-index: 1;
  transform: translateY(36px);
  padding-left: 5px;
}
.crown {
  width: 30px;
  height: 20px;
  margin-bottom: -5px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}
.crown.gold {
  background-image: linear-gradient(45deg, #FFD700, #FFC107);
  -webkit-clip-path: polygon(50% 0%, 75% 50%, 100% 50%, 75% 100%, 25% 100%, 0% 50%, 25% 50%);
          clip-path: polygon(50% 0%, 75% 50%, 100% 50%, 75% 100%, 25% 100%, 0% 50%, 25% 50%);
  width: 36px;
  height: 25px;
}
.crown.silver {
  background-image: linear-gradient(45deg, #C0C0C0, #A9A9A9);
  -webkit-clip-path: polygon(50% 20%, 70% 50%, 90% 50%, 70% 100%, 30% 100%, 10% 50%, 30% 50%);
          clip-path: polygon(50% 20%, 70% 50%, 90% 50%, 70% 100%, 30% 100%, 10% 50%, 30% 50%);
}
.crown.bronze {
  background-image: linear-gradient(45deg, #CD7F32, #B87333);
  -webkit-clip-path: polygon(50% 20%, 70% 50%, 90% 50%, 70% 100%, 30% 100%, 10% 50%, 30% 50%);
          clip-path: polygon(50% 20%, 70% 50%, 90% 50%, 70% 100%, 30% 100%, 10% 50%, 30% 50%);
}
.avatar-container {
  position: relative;
  margin-bottom: 8px;
}
.avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 3px solid #FFFFFF;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}
.first-place .avatar {
  width: 80px;
  height: 80px;
  border: 4px solid #FFD700;
}
.second-place .avatar {
  border: 3px solid #C0C0C0;
}
.third-place .avatar {
  border: 3px solid #CD7F32;
}
.rank-badge {
  position: absolute;
  right: -2px;
  bottom: -2px;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 700;
  color: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}
.first-place .rank-badge {
  width: 26px;
  height: 26px;
  font-size: 14px;
}
.rank-badge.gold {
  background: linear-gradient(135deg, #FFD700, #FFC107);
}
.rank-badge.silver {
  background: linear-gradient(135deg, #C0C0C0, #A9A9A9);
}
.rank-badge.bronze {
  background: linear-gradient(135deg, #CD7F32, #B87333);
}
.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.nickname {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  box-sizing: border-box;
  padding: 0 2px;
}
.first-place .nickname {
  font-size: 16px;
  font-weight: 600;
}
.points {
  font-size: 15px;
  font-weight: 700;
  color: #3a86ff;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  box-sizing: border-box;
  padding: 0 2px;
}
.first-place .points {
  font-size: 18px;
}

/* 排行榜列表 */
.rank-list-container {
  flex: 1;
  background: #FFFFFF;
  border-radius: 20px 20px 0 0;
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  padding: 0;
  margin-top: 16px;
  width: 100%;
}
.rank-list-header {
  display: flex;
  padding: 16px 20px;
  border-bottom: 1px solid #F5F5F7;
  box-sizing: border-box;
  width: 100%;
}
.rank-header-text {
  font-size: 13px;
  color: #999;
  font-weight: 500;
}
.rank-header-text:first-child {
  width: 40px;
  flex-shrink: 0;
}
.rank-header-text.user-col {
  flex: 1;
  padding-left: 52px;
  /* 头像宽度40px + 间距12px */
}
.rank-header-text.score-col {
  width: 80px;
  flex-shrink: 0;
  text-align: right;
}
.rank-list {
  height: 50vh;
  padding: 0 20px;
  box-sizing: border-box;
  padding-right: 25px;
  /* 增加右侧内边距，避免被滚动条遮挡 */
}
.rank-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #F5F5F7;
  width: 100%;
  box-sizing: border-box;
}
.rank-number {
  width: 40px;
  font-size: 17px;
  font-weight: 600;
  color: #666;
  flex-shrink: 0;
}
.rank-user {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  overflow: hidden;
}
.rank-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid #F5F5F7;
  flex-shrink: 0;
}
.rank-nickname {
  font-size: 15px;
  color: #333;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}
.rank-score {
  width: 80px;
  flex-shrink: 0;
  text-align: right;
  font-size: 17px;
  font-weight: 600;
  color: #3a86ff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 5px;
  /* 为积分数字添加右侧内边距 */
}

/* 没有更多提示 */
.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  gap: 10px;
}
.no-more-line {
  height: 1px;
  flex: 1;
  background: #EFEFEF;
  max-width: 60px;
}
.no-more-text {
  font-size: 12px;
  color: #999;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
}
::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}
::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 2px;
}
::-webkit-scrollbar-thumb:hover {
  background: #aaa;
}