/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.withdraw-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 88rpx 32rpx 30rpx;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);
}
.navbar-back {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 24rpx;
  height: 24rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}
.navbar-right {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

/* 可提现金额卡片 */
.balance-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  text-align: center;
}
.balance-header {
  margin-bottom: 20rpx;
}
.balance-label {
  font-size: 28rpx;
  color: #666;
}
.balance-amount {
  margin-bottom: 20rpx;
}
.currency {
  font-size: 36rpx;
  font-weight: 600;
  color: #FF5722;
}
.amount {
  font-size: 60rpx;
  font-weight: 600;
  color: #FF5722;
}
.balance-tips {
  font-size: 24rpx;
  color: #999;
}

/* 表单卡片 */
.form-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.form-header {
  margin-bottom: 30rpx;
}
.form-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.form-content {
  margin-bottom: 30rpx;
}
.form-item {
  margin-bottom: 30rpx;
}
.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}
.amount-input-wrap {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 0 20rpx;
  height: 80rpx;
}
.currency-symbol {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 10rpx;
}
.amount-input {
  flex: 1;
  height: 100%;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.amount-tips {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #999;
}
.all-amount {
  color: #6B0FBE;
}
.form-input {
  width: 100%;
  height: 80rpx;
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}
.method-options {
  display: flex;
  margin: 0 -10rpx;
}
.method-option {
  flex: 1;
  margin: 0 10rpx;
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 2rpx solid transparent;
}
.method-option.active {
  border-color: #6B0FBE;
  background: rgba(107, 15, 190, 0.05);
}
.method-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
  border-radius: 40rpx;
}
.method-icon.wechat {
  background-color: #07C160;
}
.method-icon.alipay {
  background-color: #1677FF;
}
.method-icon.bank {
  background-color: #FF9500;
}
.method-name {
  font-size: 26rpx;
  color: #333;
}
.form-footer {
  padding-top: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}
.fee-info {
  display: flex;
  flex-direction: column;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 20rpx;
}
.submit-btn {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 32rpx;
  padding: 20rpx 0;
  line-height: 1.5;
  width: 100%;
}
.submit-btn.disabled {
  background: #cccccc;
  color: #ffffff;
}

/* 提现记录 */
.records-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.records-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.records-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.view-all {
  font-size: 26rpx;
  color: #6B0FBE;
}
.record-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.record-item:last-child {
  border-bottom: none;
}
.record-info {
  display: flex;
  flex-direction: column;
}
.record-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}
.record-type {
  font-size: 28rpx;
  color: #333;
}
.record-amount {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}
.record-sub {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.record-time {
  font-size: 24rpx;
  color: #999;
}
.record-status {
  font-size: 24rpx;
}
.record-status.status-pending {
  color: #FF9500;
}
.record-status.status-success {
  color: #34C759;
}
.record-status.status-failed {
  color: #FF3B30;
}
.empty-records {
  text-align: center;
  padding: 30rpx 0;
  font-size: 26rpx;
  color: #999;
}

/* 提现说明 */
.tips-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.tips-header {
  margin-bottom: 20rpx;
}
.tips-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.tips-content {
  margin-bottom: 20rpx;
}
.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}
.tip-item:last-child {
  margin-bottom: 0;
}
.tip-icon {
  width: 16rpx;
  height: 16rpx;
  border-radius: 8rpx;
  background-color: #6B0FBE;
  margin-right: 16rpx;
  margin-top: 10rpx;
  flex-shrink: 0;
}
.tip-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}