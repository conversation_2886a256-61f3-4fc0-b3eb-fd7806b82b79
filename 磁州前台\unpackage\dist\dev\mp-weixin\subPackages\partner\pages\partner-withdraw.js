"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  __name: "partner-withdraw",
  setup(__props) {
    const availableBalance = common_vendor.ref(2358.65);
    const totalIncome = common_vendor.ref(3500);
    const withdrawedAmount = common_vendor.ref(1e3);
    const freezeAmount = common_vendor.ref(141.35);
    const amount = common_vendor.ref("");
    const feeRate = common_vendor.ref(0.6);
    const selectedMethod = common_vendor.ref("wechat");
    const alipayAccount = common_vendor.ref("");
    const alipayName = common_vendor.ref("");
    const bankIndex = common_vendor.ref(0);
    const bankList = [
      "中国工商银行",
      "中国农业银行",
      "中国建设银行",
      "中国银行",
      "交通银行",
      "招商银行",
      "中信银行",
      "浦发银行",
      "民生银行",
      "华夏银行",
      "兴业银行",
      "平安银行"
    ];
    const bankCard = common_vendor.ref("");
    const bankOwner = common_vendor.ref("");
    const showConfirmModal = common_vendor.ref(false);
    const showSuccessModal = common_vendor.ref(false);
    const historyList = common_vendor.ref([
      { amount: 500, time: "2023-12-15 14:30:25", status: "success" },
      { amount: 300, time: "2023-11-20 10:15:36", status: "pending" },
      { amount: 200, time: "2023-10-05 16:45:12", status: "success" }
    ]);
    const statusText = {
      "success": "已到账",
      "pending": "审核中",
      "processing": "处理中",
      "failed": "提现失败"
    };
    const methodText = {
      "wechat": "微信",
      "alipay": "支付宝",
      "bank": "银行卡"
    };
    const actualAmount = common_vendor.computed(() => {
      if (!amount.value || isNaN(amount.value))
        return 0;
      let fee2 = amount.value * (feeRate.value / 100);
      return (amount.value - fee2).toFixed(2);
    });
    const fee = common_vendor.computed(() => {
      if (!amount.value || isNaN(amount.value))
        return 0;
      return (amount.value * (feeRate.value / 100)).toFixed(2);
    });
    const canWithdraw = common_vendor.computed(() => {
      if (!amount.value || isNaN(amount.value) || amount.value <= 0)
        return false;
      if (amount.value > availableBalance.value)
        return false;
      if (amount.value < 1)
        return false;
      if (selectedMethod.value === "alipay" && (!alipayAccount.value || !alipayName.value))
        return false;
      if (selectedMethod.value === "bank" && (!bankCard.value || !bankOwner.value))
        return false;
      return true;
    });
    const setMaxAmount = () => {
      amount.value = availableBalance.value.toString();
    };
    const selectMethod = (method) => {
      selectedMethod.value = method;
    };
    const bankChange = (e) => {
      bankIndex.value = e.detail.value;
    };
    const submitWithdraw = () => {
      if (!canWithdraw.value)
        return;
      showConfirmModal.value = true;
    };
    const cancelWithdraw = () => {
      showConfirmModal.value = false;
    };
    const confirmWithdraw = () => {
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        showConfirmModal.value = false;
        showSuccessModal.value = true;
        availableBalance.value -= Number(amount.value);
        withdrawedAmount.value += Number(amount.value);
        historyList.value.unshift({
          amount: Number(amount.value),
          time: formatDate(/* @__PURE__ */ new Date()),
          status: "pending"
        });
        amount.value = "";
        alipayAccount.value = "";
        alipayName.value = "";
        bankCard.value = "";
        bankOwner.value = "";
      }, 1500);
    };
    const closeSuccessModal = () => {
      showSuccessModal.value = false;
    };
    const navigateToWithdrawHistory = () => {
      common_vendor.index.navigateTo({
        url: "/pages/my/partner-withdraw-history"
      });
    };
    const formatDate = (date) => {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      const hour = date.getHours().toString().padStart(2, "0");
      const minute = date.getMinutes().toString().padStart(2, "0");
      const second = date.getSeconds().toString().padStart(2, "0");
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: common_vendor.t(availableBalance.value),
        d: common_vendor.t(totalIncome.value),
        e: common_vendor.t(withdrawedAmount.value),
        f: common_vendor.t(freezeAmount.value),
        g: amount.value,
        h: common_vendor.o(($event) => amount.value = $event.detail.value),
        i: common_vendor.t(feeRate.value),
        j: common_vendor.o(setMaxAmount),
        k: amount.value > 0
      }, amount.value > 0 ? {
        l: common_vendor.t(actualAmount.value)
      } : {}, {
        m: common_assets._imports_1$22,
        n: selectedMethod.value === "wechat"
      }, selectedMethod.value === "wechat" ? {} : {}, {
        o: selectedMethod.value === "wechat" ? 1 : "",
        p: common_vendor.o(($event) => selectMethod("wechat")),
        q: common_assets._imports_2$20,
        r: selectedMethod.value === "alipay"
      }, selectedMethod.value === "alipay" ? {} : {}, {
        s: selectedMethod.value === "alipay" ? 1 : "",
        t: common_vendor.o(($event) => selectMethod("alipay")),
        v: common_assets._imports_3$16,
        w: selectedMethod.value === "bank"
      }, selectedMethod.value === "bank" ? {} : {}, {
        x: selectedMethod.value === "bank" ? 1 : "",
        y: common_vendor.o(($event) => selectMethod("bank")),
        z: selectedMethod.value === "alipay"
      }, selectedMethod.value === "alipay" ? {
        A: alipayAccount.value,
        B: common_vendor.o(($event) => alipayAccount.value = $event.detail.value),
        C: alipayName.value,
        D: common_vendor.o(($event) => alipayName.value = $event.detail.value)
      } : {}, {
        E: selectedMethod.value === "bank"
      }, selectedMethod.value === "bank" ? {
        F: common_vendor.t(bankList[bankIndex.value] || "请选择开户银行"),
        G: common_vendor.o(bankChange),
        H: bankIndex.value,
        I: bankList,
        J: bankCard.value,
        K: common_vendor.o(($event) => bankCard.value = $event.detail.value),
        L: bankOwner.value,
        M: common_vendor.o(($event) => bankOwner.value = $event.detail.value)
      } : {}, {
        N: !canWithdraw.value,
        O: !canWithdraw.value ? 1 : "",
        P: common_vendor.o(submitWithdraw),
        Q: common_vendor.t(feeRate.value),
        R: historyList.value.length > 0
      }, historyList.value.length > 0 ? {
        S: common_vendor.f(historyList.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.amount),
            b: common_vendor.t(item.time),
            c: common_vendor.t(statusText[item.status]),
            d: common_vendor.n("status-" + item.status),
            e: index
          };
        })
      } : {
        T: common_assets._imports_4$11
      }, {
        U: historyList.value.length > 0
      }, historyList.value.length > 0 ? {
        V: common_vendor.o(navigateToWithdrawHistory)
      } : {}, {
        W: showConfirmModal.value
      }, showConfirmModal.value ? common_vendor.e({
        X: common_vendor.o(cancelWithdraw),
        Y: common_vendor.t(amount.value),
        Z: common_vendor.t(fee.value),
        aa: common_vendor.t(actualAmount.value),
        ab: common_vendor.t(methodText[selectedMethod.value]),
        ac: selectedMethod.value === "alipay"
      }, selectedMethod.value === "alipay" ? {
        ad: common_vendor.t(alipayAccount.value)
      } : {}, {
        ae: selectedMethod.value === "bank"
      }, selectedMethod.value === "bank" ? {
        af: common_vendor.t(bankList[bankIndex.value]),
        ag: common_vendor.t(bankCard.value)
      } : {}, {
        ah: common_vendor.o(cancelWithdraw),
        ai: common_vendor.o(confirmWithdraw)
      }) : {}, {
        aj: showSuccessModal.value
      }, showSuccessModal.value ? {
        ak: common_assets._imports_5$11,
        al: common_vendor.o(closeSuccessModal)
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-0ba2294a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/partner/pages/partner-withdraw.js.map
