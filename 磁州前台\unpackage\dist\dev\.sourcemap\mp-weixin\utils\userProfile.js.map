{"version": 3, "file": "userProfile.js", "sources": ["utils/userProfile.js"], "sourcesContent": ["/**\r\n * 用户信息管理模块\r\n */\r\n\r\n// 默认头像\r\nconst DEFAULT_AVATAR = '/static/images/default-avatar.png';\r\n// 默认昵称\r\nconst DEFAULT_NICKNAME = '同城用户';\r\n\r\n// 本地存储的用户信息的键名\r\nconst USER_INFO_KEY = 'user_info';\r\n\r\n/**\r\n * 获取本地存储的用户信息\r\n */\r\nexport const getLocalUserInfo = () => {\r\n  try {\r\n    return uni.getStorageSync(USER_INFO_KEY) || null;\r\n  } catch (e) {\r\n    console.error('获取用户信息失败', e);\r\n    return null;\r\n  }\r\n};\r\n\r\n/**\r\n * 保存用户信息到本地\r\n * @param {Object} userInfo - 用户信息对象\r\n */\r\nexport const saveUserInfo = (userInfo) => {\r\n  if (!userInfo) return;\r\n  \r\n  try {\r\n    // 合并基本信息\r\n    const savedInfo = {\r\n      ...getLocalUserInfo(),\r\n      ...userInfo,\r\n      // 添加更新时间\r\n      updateTime: new Date().getTime()\r\n    };\r\n    \r\n    uni.setStorageSync(USER_INFO_KEY, savedInfo);\r\n    return savedInfo;\r\n  } catch (e) {\r\n    console.error('保存用户信息失败', e);\r\n    return null;\r\n  }\r\n};\r\n\r\n/**\r\n * 从服务器获取用户信息\r\n * @param {String} token - 用户token\r\n */\r\nexport const fetchUserInfo = (token) => {\r\n  return new Promise((resolve, reject) => {\r\n    // 这里应该调用后端接口获取用户信息\r\n    // 示例中模拟请求\r\n    setTimeout(() => {\r\n      const userInfo = {\r\n        userId: 'user_' + Math.random().toString(36).substr(2, 9),\r\n        // 如果没有设置过头像和昵称，使用默认值\r\n        avatar: DEFAULT_AVATAR,\r\n        nickname: DEFAULT_NICKNAME,\r\n        // 其他信息\r\n        level: 1,\r\n        points: 0,\r\n        isVip: false\r\n      };\r\n      \r\n      // 保存到本地\r\n      saveUserInfo(userInfo);\r\n      resolve(userInfo);\r\n    }, 500);\r\n  });\r\n};\r\n\r\n/**\r\n * 更新本地用户的昵称和头像\r\n * @param {Object} profile - 包含昵称和头像的对象\r\n */\r\nexport const updateUserProfile = async (profile) => {\r\n  try {\r\n    // 获取当前用户信息\r\n    const currentUser = getLocalUserInfo();\r\n    \r\n    // 更新信息\r\n    const updatedUser = {\r\n      ...currentUser,\r\n      avatar: profile.avatarUrl || currentUser?.avatar || DEFAULT_AVATAR,\r\n      nickname: profile.nickName || currentUser?.nickname || DEFAULT_NICKNAME\r\n    };\r\n    \r\n    // 保存到本地\r\n    saveUserInfo(updatedUser);\r\n    \r\n    // 同步到服务器\r\n    // 这里应该调用服务器接口保存用户信息\r\n    // 下面是示例代码\r\n    /*\r\n    await uni.request({\r\n      url: 'https://your-api-domain/api/user/updateProfile',\r\n      method: 'POST',\r\n      data: {\r\n        avatar: updatedUser.avatar,\r\n        nickname: updatedUser.nickname\r\n      }\r\n    });\r\n    */\r\n    \r\n    return updatedUser;\r\n  } catch (e) {\r\n    console.error('更新用户资料失败', e);\r\n    return null;\r\n  }\r\n};\r\n\r\n/**\r\n * 获取默认的用户信息\r\n */\r\nexport const getDefaultUserInfo = () => {\r\n  return {\r\n    avatar: DEFAULT_AVATAR,\r\n    nickname: DEFAULT_NICKNAME\r\n  };\r\n};\r\n\r\n/**\r\n * 检查是否有用户信息\r\n */\r\nexport const hasUserInfo = () => {\r\n  const userInfo = getLocalUserInfo();\r\n  return !!(userInfo && (userInfo.nickname !== DEFAULT_NICKNAME || userInfo.avatar !== DEFAULT_AVATAR));\r\n};\r\n\r\n/**\r\n * 登出，清除用户信息\r\n */\r\nexport const logout = () => {\r\n  try {\r\n    uni.removeStorageSync(USER_INFO_KEY);\r\n    return true;\r\n  } catch (e) {\r\n    console.error('清除用户信息失败', e);\r\n    return false;\r\n  }\r\n}; "], "names": ["uni"], "mappings": ";;AAKA,MAAM,iBAAiB;AAEvB,MAAM,mBAAmB;AAGzB,MAAM,gBAAgB;AAKV,MAAC,mBAAmB,MAAM;AACpC,MAAI;AACF,WAAOA,oBAAI,eAAe,aAAa,KAAK;AAAA,EAC7C,SAAQ,GAAG;AACVA,qEAAc,YAAY,CAAC;AAC3B,WAAO;AAAA,EACR;AACH;AAMO,MAAM,eAAe,CAAC,aAAa;AACxC,MAAI,CAAC;AAAU;AAEf,MAAI;AAEF,UAAM,YAAY;AAAA,MAChB,GAAG,iBAAkB;AAAA,MACrB,GAAG;AAAA;AAAA,MAEH,aAAY,oBAAI,KAAM,GAAC,QAAS;AAAA,IACtC;AAEIA,kBAAAA,MAAI,eAAe,eAAe,SAAS;AAC3C,WAAO;AAAA,EACR,SAAQ,GAAG;AACVA,qEAAc,YAAY,CAAC;AAC3B,WAAO;AAAA,EACR;AACH;AAiCY,MAAC,oBAAoB,OAAO,YAAY;AAClD,MAAI;AAEF,UAAM,cAAc;AAGpB,UAAM,cAAc;AAAA,MAClB,GAAG;AAAA,MACH,QAAQ,QAAQ,cAAa,2CAAa,WAAU;AAAA,MACpD,UAAU,QAAQ,aAAY,2CAAa,aAAY;AAAA,IAC7D;AAGI,iBAAa,WAAW;AAgBxB,WAAO;AAAA,EACR,SAAQ,GAAG;AACVA,sEAAc,YAAY,CAAC;AAC3B,WAAO;AAAA,EACR;AACH;AAeY,MAAC,cAAc,MAAM;AAC/B,QAAM,WAAW;AACjB,SAAO,CAAC,EAAE,aAAa,SAAS,aAAa,oBAAoB,SAAS,WAAW;AACvF;;;;"}