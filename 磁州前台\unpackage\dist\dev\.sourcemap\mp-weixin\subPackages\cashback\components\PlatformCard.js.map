{"version": 3, "file": "PlatformCard.js", "sources": ["subPackages/cashback/components/PlatformCard.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7Avc3ViUGFja2FnZXMvY2FzaGJhY2svY29tcG9uZW50cy9QbGF0Zm9ybUNhcmQudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"platform-card\" @tap=\"navigateToPlatform\">\r\n    <view class=\"platform-logo-container\">\r\n      <image class=\"platform-logo\" :src=\"platform.logo\" mode=\"aspectFit\" />\r\n    </view>\r\n    <view class=\"platform-info\">\r\n      <view class=\"platform-name\">\r\n        <text>{{ platform.name }}</text>\r\n      </view>\r\n      <view class=\"platform-cashback\">\r\n        <text>最高返{{ platform.maxCashbackRate }}</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'PlatformCard',\r\n  props: {\r\n    platform: {\r\n      type: Object,\r\n      required: true\r\n    }\r\n  },\r\n  methods: {\r\n    navigateToPlatform() {\r\n      uni.navigateTo({\r\n        url: `/subPackages/cashback/pages/platform-detail/index?id=${this.platform.id}`\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.platform-card {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  background-color: #FFFFFF;\r\n  border-radius: 16px;\r\n  padding: 16px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\r\n  \r\n  .platform-logo-container {\r\n    width: 60px;\r\n    height: 60px;\r\n    border-radius: 16px;\r\n    overflow: hidden;\r\n    margin-bottom: 12px;\r\n    background-color: #F5F7FA;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    \r\n    .platform-logo {\r\n      width: 48px;\r\n      height: 48px;\r\n    }\r\n  }\r\n  \r\n  .platform-info {\r\n    width: 100%;\r\n    text-align: center;\r\n  }\r\n  \r\n  .platform-name {\r\n    margin-bottom: 6px;\r\n    \r\n    text {\r\n      font-size: 14px;\r\n      color: #333333;\r\n      font-weight: 500;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      white-space: nowrap;\r\n    }\r\n  }\r\n  \r\n  .platform-cashback {\r\n    padding: 2px 8px;\r\n    background-color: rgba(156, 39, 176, 0.1);\r\n    border-radius: 10px;\r\n    display: inline-block;\r\n    \r\n    text {\r\n      font-size: 12px;\r\n      color: #9C27B0;\r\n    }\r\n  }\r\n}\r\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/cashback/components/PlatformCard.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;AAiBA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,IACL,UAAU;AAAA,MACR,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,qBAAqB;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,wDAAwD,KAAK,SAAS,EAAE;AAAA,MAC/E,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;AC/BA,GAAG,gBAAgB,SAAS;"}