/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.success-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f9fc;
}

/* 顶部渐变背景 */
.top-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 30vh;
  background: linear-gradient(135deg, #0046B3, #1677FF);
  border-bottom-left-radius: 30px;
  border-bottom-right-radius: 30px;
  z-index: 0;
}

/* 导航栏 */
.navbar {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  position: relative;
  z-index: 1;
}
.navbar-left {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 24px;
  height: 24px;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #fff;
}
.navbar-right {
  width: 44px;
}

/* 内容区域 */
.content {
  flex: 1;
  position: relative;
  z-index: 1;
  padding: 0 20px 30px;
}

/* 成功信息 */
.success-info {
  margin-top: 20px;
  text-align: center;
  position: relative;
  z-index: 5;
}
.success-message-card {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 24px;
  padding: 16px 20px;
  margin-bottom: 20px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.05), 0 8px 16px rgba(0, 0, 0, 0.1), 0 12px 24px -10px rgba(0, 82, 204, 0.15);
  transform: perspective(1000px) translateZ(0) rotateX(0.5deg);
  transform-style: preserve-3d;
  border: 1px solid rgba(255, 255, 255, 0.7);
  transition: transform 0.3s ease;
  position: relative;
}
.success-message-card::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);
  border-radius: 16px;
  pointer-events: none;
}
.success-message-icon {
  width: 48px;
  height: 48px;
  margin-right: 16px;
  flex-shrink: 0;
}
.success-message-content {
  flex: 1;
  text-align: left;
}
.success-message-title {
  font-size: 18px;
  font-weight: bold;
  color: #0052CC;
  margin-bottom: 4px;
}
.success-message-subtitle {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}
.shop-info {
  background-color: #fff;
  border-radius: 24px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.05), 0 8px 20px rgba(0, 0, 0, 0.1), 0 15px 30px -12px rgba(0, 82, 204, 0.2);
  transform: perspective(1000px) translateZ(0) rotateX(1deg);
  transform-style: preserve-3d;
  border: 1px solid rgba(255, 255, 255, 0.7);
  transition: transform 0.3s ease;
}
.shop-info::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);
  border-radius: 16px;
  pointer-events: none;
}
.shop-info::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  box-shadow: inset 0 1px 1px rgba(255, 255, 255, 0.5);
  border-radius: 16px;
  pointer-events: none;
}
.shop-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #f0f4ff;
  margin-bottom: 12px;
  background-color: #f8f8f8;
  box-shadow: 0 4px 10px rgba(0, 82, 204, 0.15);
}
.shop-avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.shop-name {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}
.shop-verify-tip {
  display: flex;
  align-items: center;
  background-color: #f0f8ff;
  padding: 4px 12px;
  border-radius: 20px;
}
.verify-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}
.verify-text {
  font-size: 14px;
  color: #0052CC;
}
.verify-btn {
  font-size: 14px;
  color: #0052CC;
  margin-left: 4px;
  cursor: pointer;
}
.shop-verified {
  display: flex;
  align-items: center;
  background-color: #f0f8ff;
  padding: 4px 12px;
  border-radius: 20px;
}
.verified-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}
.verified-text {
  font-size: 14px;
  color: #0052CC;
}

/* 数据概览 */
.data-overview {
  margin-top: 20px;
}
.data-card {
  background-color: #fff;
  border-radius: 24px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  position: relative;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.05), 0 8px 16px rgba(0, 0, 0, 0.1), 0 12px 24px -10px rgba(0, 82, 204, 0.15);
  transform: perspective(1000px) translateZ(0) rotateX(0.5deg);
  transform-style: preserve-3d;
  border: 1px solid rgba(255, 255, 255, 0.7);
  transition: transform 0.3s ease;
}
.data-card::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);
  border-radius: 16px;
  pointer-events: none;
}
.data-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.data-num {
  font-size: 18px;
  font-weight: bold;
  color: #0052CC;
  margin-bottom: 4px;
}
.data-label {
  font-size: 12px;
  color: #666;
}
.data-divider {
  width: 1px;
  background-color: #eee;
  margin: 0 10px;
}

/* 功能入口 */
.function-section {
  margin-top: 24px;
}
.function-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
}
.function-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px;
}
.function-item {
  width: 25%;
  padding: 8px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.function-icon-bg {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}
.bg-blue {
  background-color: #e6f0ff;
}
.bg-orange {
  background-color: #fff2e6;
}
.bg-green {
  background-color: #e6fff0;
}
.bg-purple {
  background-color: #f0e6ff;
}
.function-icon {
  width: 28px;
  height: 28px;
}
.function-name {
  font-size: 14px;
  color: #333;
  text-align: center;
}

/* 操作按钮 */
.action-buttons {
  margin-top: 30px;
  display: flex;
  flex-direction: column;
  padding: 0 30rpx;
  margin-bottom: 40rpx;
}
.primary-btn {
  background: linear-gradient(to right, #007AFF, #5AC8FA);
  color: #FFFFFF;
  font-size: 32rpx;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}
.secondary-btn {
  background: rgba(0, 122, 255, 0.1);
  color: #007AFF;
  font-size: 32rpx;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  border: 1px solid rgba(0, 122, 255, 0.2);
}

/* 下一步提示 */
.next-steps {
  margin-top: 30px;
  background-color: #fff;
  border-radius: 24px;
  padding: 20px;
  position: relative;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.05), 0 8px 16px rgba(0, 0, 0, 0.08), 0 12px 20px -8px rgba(0, 82, 204, 0.15);
  transform: perspective(1000px) translateZ(0) rotateX(0.8deg);
  transform-style: preserve-3d;
  border: 1px solid rgba(255, 255, 255, 0.7);
  transition: transform 0.3s ease;
}
.next-steps::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);
  border-radius: 16px;
  pointer-events: none;
}
.next-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
}
.step-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}
.step-item:last-child {
  border-bottom: none;
}
.step-left {
  display: flex;
  align-items: center;
  flex: 1;
}
.step-num {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: #0052CC;
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}
.step-text {
  font-size: 14px;
  color: #333;
}
.step-right {
  display: flex;
  align-items: center;
}
.go-text {
  font-size: 14px;
  color: #0052CC;
  margin-right: 4px;
}
.go-icon {
  width: 16px;
  height: 16px;
  transform: rotate(90deg);
}

/* 联系客服 */
.contact-section {
  margin-top: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.contact-text {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
}
.contact-btn {
  width: 140px;
  height: 36px;
  line-height: 36px;
  background: linear-gradient(135deg, #2FB8FF, #0076FF);
  color: #fff;
  font-size: 14px;
  border-radius: 18px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 8px rgba(0, 118, 255, 0.25);
  position: relative;
  overflow: hidden;
}
.contact-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  border-radius: 18px;
}

/* 会员信息 */
.membership-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20rpx;
}
.membership-badge {
  padding: 6rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  color: #fff;
  background: linear-gradient(135deg, #1677ff, #0052cc);
  box-shadow: 0 4rpx 8rpx rgba(22, 119, 255, 0.2);
}
.free-badge {
  background: linear-gradient(135deg, #52c41a, #389e0d);
}
.basic-badge {
  background: linear-gradient(135deg, #1677ff, #0052cc);
}
.premium-badge {
  background: linear-gradient(135deg, #fa8c16, #d46b08);
}
.deluxe-badge {
  background: linear-gradient(135deg, #722ed1, #531dab);
}
.test-badge {
  background: linear-gradient(135deg, #ffa500, #ff8c00);
}
.test-mark {
  position: absolute;
  top: -6px;
  right: -10px;
  background-color: #ff9800;
  color: #fff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  transform: scale(0.85);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 2;
}
.membership-date {
  margin-top: 8rpx;
  font-size: 22rpx;
  color: #666;
}

/* 认证好处卡片 */
.verify-benefits {
  margin-top: 20px;
  background-color: #fff;
  border-radius: 24px;
  padding: 20px;
  position: relative;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.05), 0 8px 16px rgba(0, 0, 0, 0.08), 0 12px 20px -8px rgba(0, 82, 204, 0.15);
  transform: perspective(1000px) translateZ(0) rotateX(0.7deg);
  transform-style: preserve-3d;
  border: 1px solid rgba(255, 255, 255, 0.7);
  transition: transform 0.3s ease;
}
.verify-benefits::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);
  border-radius: 16px;
  pointer-events: none;
}
.verify-benefits-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
}
.verify-benefit-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}
.verify-benefits-list {
  margin-bottom: 16px;
}
.verify-benefit-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.benefit-dot {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background-color: #1677FF;
  margin-right: 8px;
}
.benefit-text {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}
.verify-now-btn {
  background: linear-gradient(135deg, #1677FF, #0052CC);
  color: #fff;
  font-size: 16px;
  height: 40px;
  border-radius: 20px;
  margin-top: 10px;
  box-shadow: 0 4px 8px rgba(0, 82, 204, 0.2);
}

/* 重新设计店铺推广样式 */
.promotion-section {
  padding: 0;
  margin: 30rpx 30rpx 40rpx;
}
.promotion-card {
  background-color: #fff;
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.card-header {
  padding: 16px 16px 12px;
  background-color: #FFFFFF;
}
.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
}
.card-subtitle {
  font-size: 12px;
  color: #666;
  display: block;
  margin-top: 4px;
}
.promotion-tabs {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background-color: #FFFFFF;
  padding: 0 30px;
}

/* 紧凑卡片样式 */
.compact-card {
  border-radius: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}
.compact-card .card-header {
  padding: 12px 16px 8px;
}
.compact-card .card-title {
  font-size: 15px;
}
.compact-card .card-subtitle {
  font-size: 11px;
  margin-top: 2px;
}
.compact-card .promotion-tabs {
  padding: 0 20px;
}
.compact-card .promotion-tab {
  padding: 8px 0;
}
.compact-card .promotion-content {
  padding: 12px;
}
.promotion-tab {
  flex: 1;
  text-align: center;
  padding: 12px 0;
  position: relative;
  transition: all 0.3s ease;
  max-width: 120px;
  margin: 0 auto;
}
.promotion-tab.active {
  position: relative;
  border-bottom: none;
}
.promotion-tab.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background: linear-gradient(90deg, rgba(22, 119, 255, 0.7), #1677FF, rgba(22, 119, 255, 0.7));
  border-radius: 3px 3px 0 0;
  box-shadow: 0 1px 3px rgba(22, 119, 255, 0.2);
}
.promotion-tab-text {
  font-size: 14px;
  font-weight: 500;
  color: #666;
  transition: all 0.3s;
  position: relative;
  display: inline-block;
}
.promotion-tab.active .promotion-tab-text {
  color: #1677FF;
  font-weight: 600;
}

/* 紧凑选项卡样式 */
.compact-card .promotion-tab {
  padding: 8px 0;
}
.compact-card .promotion-tab-text {
  font-size: 13px;
}
.compact-card .promotion-tab.active::after {
  width: 16px;
  height: 2px;
}
.promotion-content {
  padding: 16px;
  background-color: #FFFFFF;
}
.tab-content-animation {
  animation: fadeIn 0.3s ease;
}
@keyframes fadeIn {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}