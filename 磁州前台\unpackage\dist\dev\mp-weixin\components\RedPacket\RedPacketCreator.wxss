/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.red-packet-creator {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
}
.red-packet-creator .creator-form .form-item {
  margin-bottom: 30rpx;
}
.red-packet-creator .creator-form .form-item:last-child {
  margin-bottom: 40rpx;
}
.red-packet-creator .creator-form .form-item .form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}
.red-packet-creator .creator-form .type-selector {
  display: flex;
  justify-content: space-between;
}
.red-packet-creator .creator-form .type-selector .type-item {
  flex: 1;
  height: 110rpx;
  background-color: #F8F8F8;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 10rpx;
}
.red-packet-creator .creator-form .type-selector .type-item:first-child {
  margin-left: 0;
}
.red-packet-creator .creator-form .type-selector .type-item:last-child {
  margin-right: 0;
}
.red-packet-creator .creator-form .type-selector .type-item.active {
  background: linear-gradient(135deg, #FFE4E1 0%, #FFF0F5 100%);
  border: 1px solid #FF6347;
}
.red-packet-creator .creator-form .type-selector .type-item .type-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
}
.red-packet-creator .creator-form .type-selector .type-item .type-name {
  font-size: 24rpx;
  color: #333;
}
.red-packet-creator .creator-form .amount-input-container {
  position: relative;
  height: 90rpx;
}
.red-packet-creator .creator-form .amount-input-container .amount-input {
  height: 100%;
  background-color: #F8F8F8;
  border-radius: 8rpx;
  padding: 0 100rpx 0 30rpx;
  font-size: 32rpx;
  color: #333;
}
.red-packet-creator .creator-form .amount-input-container .amount-unit {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 28rpx;
  color: #999;
}
.red-packet-creator .creator-form .available-balance {
  font-size: 24rpx;
  color: #999;
  margin-top: 12rpx;
  display: block;
}
.red-packet-creator .creator-form .count-selector {
  display: flex;
  align-items: center;
  height: 90rpx;
}
.red-packet-creator .creator-form .count-selector .count-btn {
  width: 90rpx;
  height: 90rpx;
  background-color: #F8F8F8;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 36rpx;
  color: #333;
  margin: 0;
  padding: 0;
  line-height: 1;
}
.red-packet-creator .creator-form .count-selector .count-btn.minus {
  border-radius: 8rpx 0 0 8rpx;
}
.red-packet-creator .creator-form .count-selector .count-btn.plus {
  border-radius: 0 8rpx 8rpx 0;
}
.red-packet-creator .creator-form .count-selector .count-input {
  flex: 1;
  height: 100%;
  background-color: #F8F8F8;
  text-align: center;
  font-size: 32rpx;
  color: #333;
  margin: 0 2rpx;
}
.red-packet-creator .creator-form .amount-tips {
  font-size: 24rpx;
  color: #FF6347;
  margin-top: 12rpx;
  display: block;
}
.red-packet-creator .creator-form .blessing-input-container {
  position: relative;
  height: 90rpx;
}
.red-packet-creator .creator-form .blessing-input-container .blessing-input {
  height: 100%;
  background-color: #F8F8F8;
  border-radius: 8rpx;
  padding: 0 80rpx 0 30rpx;
  font-size: 28rpx;
  color: #333;
}
.red-packet-creator .creator-form .blessing-input-container .blessing-count {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #999;
}
.red-packet-creator .creator-form .expire-selector {
  display: flex;
  justify-content: space-between;
}
.red-packet-creator .creator-form .expire-selector .expire-item {
  flex: 1;
  height: 80rpx;
  background-color: #F8F8F8;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 10rpx;
  font-size: 26rpx;
  color: #333;
}
.red-packet-creator .creator-form .expire-selector .expire-item:first-child {
  margin-left: 0;
}
.red-packet-creator .creator-form .expire-selector .expire-item:last-child {
  margin-right: 0;
}
.red-packet-creator .creator-form .expire-selector .expire-item.active {
  background: linear-gradient(135deg, #FFE4E1 0%, #FFF0F5 100%);
  border: 1px solid #FF6347;
  color: #FF6347;
}
.red-packet-creator .action-area .balance-warning {
  text-align: center;
  margin-bottom: 20rpx;
}
.red-packet-creator .action-area .balance-warning .warning-text {
  font-size: 26rpx;
  color: #FF3B30;
}
.red-packet-creator .action-area .submit-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);
  border-radius: 45rpx;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
}
.red-packet-creator .action-area .submit-btn[disabled] {
  opacity: 0.6;
  background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);
  color: #fff;
}