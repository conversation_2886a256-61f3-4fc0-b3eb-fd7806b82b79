"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      dateRange: "2023-04-01 ~ 2023-04-30",
      currentTab: 0,
      tabList: ["全部", "进行中", "未开始", "已结束", "草稿"],
      // 裂变数据概览
      fissionData: {
        totalCount: 28,
        countTrend: "up",
        countGrowth: "18.5%",
        totalUsers: 5682,
        usersTrend: "up",
        usersGrowth: "32.7%",
        avgShare: 6.8,
        shareTrend: "up",
        shareGrowth: "12.3%",
        conversionRate: 38.6,
        conversionTrend: "up",
        conversionGrowth: "8.2%"
      },
      // 裂变活动列表
      fissionList: [
        {
          id: 1,
          title: "新用户拉新活动",
          status: "active",
          statusText: "进行中",
          timeRange: "2023-04-15 ~ 2023-05-15",
          shareCount: 3,
          rewardAmount: 15,
          amount: 5,
          participantCount: 1286,
          shareCount: 4752,
          newUserCount: 863
        },
        {
          id: 2,
          title: "五一节日裂变",
          status: "upcoming",
          statusText: "未开始",
          timeRange: "2023-05-01 ~ 2023-05-07",
          shareCount: 5,
          rewardAmount: 30,
          amount: 8,
          participantCount: 0,
          shareCount: 0,
          newUserCount: 0
        },
        {
          id: 3,
          title: "会员专享裂变",
          status: "draft",
          statusText: "草稿",
          timeRange: "未设置",
          shareCount: 2,
          rewardAmount: 10,
          amount: 5,
          participantCount: 0,
          shareCount: 0,
          newUserCount: 0
        },
        {
          id: 4,
          title: "春节裂变活动",
          status: "ended",
          statusText: "已结束",
          timeRange: "2023-01-20 ~ 2023-02-05",
          shareCount: 3,
          rewardAmount: 20,
          amount: 6,
          participantCount: 2358,
          shareCount: 8965,
          newUserCount: 1542
        }
      ],
      // 裂变模板
      fissionTemplates: [
        {
          id: 1,
          name: "拉新引流",
          description: "适合获取新用户的裂变活动",
          color: "#FF6B6B",
          icon: "/static/images/redpacket/fission-icon-1.png"
        },
        {
          id: 2,
          name: "会员专享",
          description: "提高会员活跃度和忠诚度",
          color: "#4ECDC4",
          icon: "/static/images/redpacket/fission-icon-2.png"
        },
        {
          id: 3,
          name: "节日狂欢",
          description: "节假日期间提升品牌曝光",
          color: "#FFD166",
          icon: "/static/images/redpacket/fission-icon-3.png"
        },
        {
          id: 4,
          name: "新品推广",
          description: "快速提升新品知名度",
          color: "#6A0572",
          icon: "/static/images/redpacket/fission-icon-4.png"
        }
      ],
      // 裂变攻略
      strategies: [
        {
          id: 1,
          title: "如何设计高转化裂变活动",
          description: "了解用户心理，设置合理的裂变门槛和奖励",
          color: "#FF6B6B",
          icon: "/static/images/redpacket/strategy-icon-1.png"
        },
        {
          id: 2,
          title: "裂变活动文案技巧",
          description: "吸引人的文案能大幅提升裂变效果",
          color: "#4ECDC4",
          icon: "/static/images/redpacket/strategy-icon-2.png"
        },
        {
          id: 3,
          title: "裂变红包数据分析指南",
          description: "通过数据分析优化裂变策略",
          color: "#FFD166",
          icon: "/static/images/redpacket/strategy-icon-3.png"
        }
      ]
    };
  },
  computed: {
    filteredFissionList() {
      if (this.currentTab === 0) {
        return this.fissionList;
      } else {
        const statusMap = {
          1: "active",
          2: "upcoming",
          3: "ended",
          4: "draft"
        };
        return this.fissionList.filter((item) => item.status === statusMap[this.currentTab]);
      }
    }
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    showHelp() {
      common_vendor.index.showModal({
        title: "裂变红包帮助",
        content: "裂变红包是一种通过用户分享传播来获取新用户的营销工具，用户分享给指定数量的好友后可获得奖励。",
        showCancel: false
      });
    },
    showDatePicker() {
      common_vendor.index.showToast({
        title: "日期选择功能开发中",
        icon: "none"
      });
    },
    switchTab(index) {
      this.currentTab = index;
    },
    createFission() {
      common_vendor.index.showToast({
        title: "创建裂变活动功能开发中",
        icon: "none"
      });
    },
    viewFissionDetail(item) {
      common_vendor.index.showToast({
        title: "查看详情功能开发中",
        icon: "none"
      });
    },
    editFission(item) {
      common_vendor.index.showToast({
        title: "编辑功能开发中",
        icon: "none"
      });
    },
    shareFission(item) {
      common_vendor.index.showToast({
        title: "分享功能开发中",
        icon: "none"
      });
    },
    deleteFission(item) {
      common_vendor.index.showModal({
        title: "删除确认",
        content: `确定要删除"${item.title}"吗？`,
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "删除成功",
              icon: "success"
            });
          }
        }
      });
    },
    useTemplate(template) {
      common_vendor.index.showToast({
        title: "使用模板功能开发中",
        icon: "none"
      });
    },
    viewStrategy(strategy) {
      common_vendor.index.showToast({
        title: "攻略详情功能开发中",
        icon: "none"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: common_vendor.t($data.dateRange),
    d: common_vendor.o((...args) => $options.showDatePicker && $options.showDatePicker(...args)),
    e: common_vendor.t($data.fissionData.totalCount),
    f: common_vendor.t($data.fissionData.countGrowth),
    g: common_vendor.n($data.fissionData.countTrend),
    h: common_vendor.t($data.fissionData.totalUsers),
    i: common_vendor.t($data.fissionData.usersGrowth),
    j: common_vendor.n($data.fissionData.usersTrend),
    k: common_vendor.t($data.fissionData.avgShare),
    l: common_vendor.t($data.fissionData.shareGrowth),
    m: common_vendor.n($data.fissionData.shareTrend),
    n: common_vendor.t($data.fissionData.conversionRate),
    o: common_vendor.t($data.fissionData.conversionGrowth),
    p: common_vendor.n($data.fissionData.conversionTrend),
    q: common_vendor.o((...args) => $options.createFission && $options.createFission(...args)),
    r: common_vendor.f($data.tabList, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab),
        b: index,
        c: $data.currentTab === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    }),
    s: common_vendor.f($options.filteredFissionList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.title),
        b: common_vendor.t(item.statusText),
        c: common_vendor.n("status-" + item.status),
        d: common_vendor.t(item.timeRange),
        e: common_vendor.t(item.shareCount),
        f: common_vendor.t(item.rewardAmount),
        g: common_vendor.t(item.amount),
        h: common_vendor.t(item.participantCount),
        i: common_vendor.t(item.shareCount),
        j: common_vendor.t(item.newUserCount),
        k: common_vendor.o(($event) => $options.viewFissionDetail(item), index),
        l: item.status === "draft"
      }, item.status === "draft" ? {
        m: common_vendor.o(($event) => $options.editFission(item), index)
      } : {}, {
        n: item.status === "active"
      }, item.status === "active" ? {
        o: common_vendor.o(($event) => $options.shareFission(item), index)
      } : {}, {
        p: item.status === "draft" || item.status === "ended"
      }, item.status === "draft" || item.status === "ended" ? {
        q: common_vendor.o(($event) => $options.deleteFission(item), index)
      } : {}, {
        r: index,
        s: common_vendor.o(($event) => $options.viewFissionDetail(item), index)
      });
    }),
    t: $options.filteredFissionList.length === 0
  }, $options.filteredFissionList.length === 0 ? {
    v: common_vendor.t($data.tabList[$data.currentTab])
  } : {}, {
    w: common_vendor.f($data.fissionTemplates, (template, index, i0) => {
      return {
        a: template.icon,
        b: common_vendor.t(template.name),
        c: template.color,
        d: common_vendor.t(template.description),
        e: index,
        f: common_vendor.o(($event) => $options.useTemplate(template), index)
      };
    }),
    x: common_vendor.f($data.strategies, (strategy, index, i0) => {
      return {
        a: strategy.icon,
        b: strategy.color,
        c: common_vendor.t(strategy.title),
        d: common_vendor.t(strategy.description),
        e: index,
        f: common_vendor.o(($event) => $options.viewStrategy(strategy), index)
      };
    }),
    y: common_vendor.o((...args) => $options.createFission && $options.createFission(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/redpacket/fission.js.map
