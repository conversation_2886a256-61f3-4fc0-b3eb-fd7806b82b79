"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      dateRange: "2023-04-01 ~ 2023-04-30",
      currentFilter: "全部套餐",
      // 套餐数据
      packageData: {
        totalPackages: 8,
        packagesTrend: "up",
        packagesGrowth: "25%",
        salesCount: 124,
        salesTrend: "up",
        salesGrowth: "18%",
        totalRevenue: 9860.5,
        revenueTrend: "up",
        revenueGrowth: "22%",
        conversionRate: 32,
        conversionTrend: "up",
        conversionGrowth: "8%"
      },
      // 套餐列表
      packagesList: [
        {
          id: 1,
          name: "四菜一汤家庭套餐",
          description: "适合3-4人用餐，经典家庭聚餐套餐",
          itemsText: "红烧肉、糖醋排骨、鱼香肉丝、清炒时蔬、紫菜蛋花汤",
          originalPrice: "168.00",
          groupPrice: "99.00",
          savingsAmount: "69.00",
          salesCount: 56,
          viewCount: 1280,
          conversionRate: 4.3,
          statusText: "进行中",
          statusClass: "active"
        },
        {
          id: 2,
          name: "双人浪漫晚餐套餐",
          description: "适合情侣约会，浪漫双人套餐",
          itemsText: "牛排2份、沙拉2份、意面2份、甜点2份、红酒1瓶",
          originalPrice: "298.00",
          groupPrice: "199.00",
          savingsAmount: "99.00",
          salesCount: 32,
          viewCount: 876,
          conversionRate: 3.6,
          statusText: "进行中",
          statusClass: "active"
        },
        {
          id: 3,
          name: "商务午餐套餐",
          description: "适合商务洽谈，高档商务套餐",
          itemsText: "前菜4份、主菜4份、甜点4份、饮料4份",
          originalPrice: "388.00",
          groupPrice: "288.00",
          savingsAmount: "100.00",
          salesCount: 18,
          viewCount: 456,
          conversionRate: 3.9,
          statusText: "已结束",
          statusClass: "inactive"
        }
      ],
      showStepWizard: false,
      createSteps: [
        { title: "选择套餐类型", description: "选择您要创建的团购套餐类型" },
        { title: "填写套餐信息", description: "填写团购套餐的基本信息" },
        { title: "设置套餐价格", description: "设置团购套餐的价格" },
        { title: "添加套餐内容", description: "添加团购套餐的内容" },
        { title: "完成创建", description: "完成团购套餐的创建" }
      ]
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    showHelp() {
      common_vendor.index.showToast({
        title: "团购套餐帮助",
        icon: "none"
      });
    },
    showDatePicker() {
      common_vendor.index.showToast({
        title: "日期选择功能开发中",
        icon: "none"
      });
    },
    formatNumber(num) {
      return num.toLocaleString("zh-CN", { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    },
    showFilterOptions() {
      common_vendor.index.showActionSheet({
        itemList: ["全部套餐", "进行中", "已结束", "销量优先", "转化率优先"],
        success: (res) => {
          const filters = ["全部套餐", "进行中", "已结束", "销量优先", "转化率优先"];
          this.currentFilter = filters[res.tapIndex];
        }
      });
    },
    viewPackageDetail(packageItem) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/group/package-detail?id=${packageItem.id}`
      });
    },
    editPackage(packageItem) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/group/create?id=${packageItem.id}&type=package&edit=true`
      });
    },
    sharePackage(packageItem) {
      common_vendor.index.showToast({
        title: "分享功能开发中",
        icon: "none"
      });
    },
    showMoreActions(packageItem) {
      common_vendor.index.showActionSheet({
        itemList: ["复制套餐", "下架套餐", "删除套餐"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              common_vendor.index.showToast({ title: "复制套餐功能开发中", icon: "none" });
              break;
            case 1:
              common_vendor.index.showToast({ title: "下架套餐功能开发中", icon: "none" });
              break;
            case 2:
              this.confirmDeletePackage(packageItem);
              break;
          }
        }
      });
    },
    confirmDeletePackage(packageItem) {
      common_vendor.index.showModal({
        title: "确认删除",
        content: `确定要删除"${packageItem.name}"套餐吗？此操作不可恢复。`,
        confirmText: "删除",
        confirmColor: "#FF3B30",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({ title: "删除成功", icon: "success" });
          }
        }
      });
    },
    showCreateSteps() {
      this.showStepWizard = true;
    },
    cancelWizard() {
      this.showStepWizard = false;
    },
    selectStep(step) {
      const index = this.createSteps.indexOf(step);
      this.showStepWizard = false;
      switch (index) {
        case 0:
          common_vendor.index.navigateTo({
            url: "/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-type"
          });
          break;
        case 1:
          common_vendor.index.navigateTo({
            url: "/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-info"
          });
          break;
        case 2:
          common_vendor.index.navigateTo({
            url: "/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-price"
          });
          break;
        case 3:
          common_vendor.index.navigateTo({
            url: "/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-items"
          });
          break;
        case 4:
          common_vendor.index.navigateTo({
            url: "/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-confirm"
          });
          break;
        default:
          common_vendor.index.navigateTo({
            url: "/subPackages/merchant-admin-marketing/pages/marketing/group/create?type=package"
          });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: common_vendor.t($data.dateRange),
    d: common_vendor.o((...args) => $options.showDatePicker && $options.showDatePicker(...args)),
    e: common_vendor.t($data.packageData.totalPackages),
    f: common_vendor.t($data.packageData.packagesGrowth),
    g: common_vendor.n($data.packageData.packagesTrend),
    h: common_vendor.t($data.packageData.salesCount),
    i: common_vendor.t($data.packageData.salesGrowth),
    j: common_vendor.n($data.packageData.salesTrend),
    k: common_vendor.t($options.formatNumber($data.packageData.totalRevenue)),
    l: common_vendor.t($data.packageData.revenueGrowth),
    m: common_vendor.n($data.packageData.revenueTrend),
    n: common_vendor.t($data.packageData.conversionRate),
    o: common_vendor.t($data.packageData.conversionGrowth),
    p: common_vendor.n($data.packageData.conversionTrend),
    q: common_vendor.t($data.currentFilter),
    r: common_vendor.o((...args) => $options.showFilterOptions && $options.showFilterOptions(...args)),
    s: common_vendor.f($data.packagesList, (packageItem, index, i0) => {
      return {
        a: common_vendor.t(packageItem.name),
        b: common_vendor.t(packageItem.statusText),
        c: common_vendor.n(packageItem.statusClass),
        d: common_vendor.t(packageItem.description),
        e: common_vendor.t(packageItem.itemsText),
        f: common_vendor.t(packageItem.originalPrice),
        g: common_vendor.t(packageItem.groupPrice),
        h: common_vendor.t(packageItem.savingsAmount),
        i: common_vendor.t(packageItem.salesCount),
        j: common_vendor.t(packageItem.viewCount),
        k: common_vendor.t(packageItem.conversionRate),
        l: common_vendor.o(($event) => $options.editPackage(packageItem), index),
        m: common_vendor.o(($event) => $options.sharePackage(packageItem), index),
        n: common_vendor.o(($event) => $options.showMoreActions(packageItem), index),
        o: index,
        p: common_vendor.o(($event) => $options.viewPackageDetail(packageItem), index)
      };
    }),
    t: $data.packagesList.length === 0
  }, $data.packagesList.length === 0 ? {
    v: common_assets._imports_0$31
  } : {}, {
    w: common_vendor.o((...args) => $options.showCreateSteps && $options.showCreateSteps(...args)),
    x: $data.showStepWizard
  }, $data.showStepWizard ? {
    y: common_vendor.o((...args) => $options.cancelWizard && $options.cancelWizard(...args)),
    z: common_vendor.o((...args) => $options.cancelWizard && $options.cancelWizard(...args)),
    A: common_vendor.f($data.createSteps, (step, index, i0) => {
      return {
        a: common_vendor.t(index + 1),
        b: common_vendor.t(step.title),
        c: common_vendor.t(step.description),
        d: index,
        e: common_vendor.o(($event) => $options.selectStep(step), index)
      };
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/group/package-management.js.map
