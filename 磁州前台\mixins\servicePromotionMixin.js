/**
 * 服务推广混入
 * 为本地服务详情页提供推广能力
 */
import basePromotionMixin from './basePromotionMixin';

export default {
  mixins: [basePromotionMixin],

  data() {
    return {
      // 设置页面类型为服务
      pageType: 'service'
    };
  },

  methods: {
    /**
     * 重写：判断当前用户是否是内容所有者
     */
    isContentOwner() {
      // 获取当前用户ID
      const currentUserId = this.$store?.state?.user?.userId || '';
      // 获取服务发布者ID
      const publisherId = this.serviceDetail?.userId || this.service?.userId || '';
      // 获取服务商家ID
      const merchantId = this.serviceDetail?.merchantId || this.service?.merchantId || '';

      // 判断当前用户是否是服务发布者或商家
      return (currentUserId && publisherId && currentUserId === publisherId) || 
             (currentUserId && merchantId && currentUserId === merchantId);
    },

    /**
     * 重写：判断当前内容是否支持佣金
     */
    isCommissionContent() {
      // 判断服务是否可分销
      const canDistribute = this.serviceDetail?.canDistribute || this.service?.canDistribute || false;
      // 是否有分销设置
      const hasDistributionSetting = !!(this.serviceDetail?.commissionRate || this.service?.commissionRate);

      return canDistribute || hasDistributionSetting;
    },

    /**
     * 重写：生成推广数据
     */
    generatePromotionData() {
      // 获取服务数据
      const service = this.serviceDetail || this.service || {};
      
      // 构建推广数据
      this.promotionData = {
        id: service.id || '',
        title: service.title || service.name || '',
        image: service.mainImage || service.image || service.images?.[0] || '',
        category: service.categoryName || service.category || '',
        price: service.price || 0,
        merchantName: service.merchantName || service.shopName || '',
        rating: service.rating || service.score || 5,
        // 如果有更多服务特定字段，可以在这里添加
      };
    },

    /**
     * 显示服务推广浮层
     */
    showServicePromotion() {
      // 如果没有推广权限，显示提示
      if (!this.hasPromotionPermission) {
        uni.showToast({
          title: '暂无推广权限',
          icon: 'none'
        });
        return;
      }

      // 打开推广工具
      this.openPromotionTools();
    }
  }
}; 