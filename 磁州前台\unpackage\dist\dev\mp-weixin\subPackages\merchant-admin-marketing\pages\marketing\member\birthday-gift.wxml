<view class="birthday-gift-container data-v-01e8e90e"><view class="page-header data-v-01e8e90e"><view class="title-section data-v-01e8e90e"><text class="page-title data-v-01e8e90e">生日礼包</text><text class="page-subtitle data-v-01e8e90e">管理会员生日当月专享礼包</text></view></view><view class="section-card data-v-01e8e90e"><view class="switch-item data-v-01e8e90e"><view class="switch-content data-v-01e8e90e"><text class="switch-title data-v-01e8e90e">生日特权</text><text class="switch-desc data-v-01e8e90e">开启后，会员在生日当月可享受专属礼包</text></view><switch class="data-v-01e8e90e" checked="{{a}}" bindchange="{{b}}" color="#4A00E0"/></view></view><block wx:if="{{c}}"><view class="section-card data-v-01e8e90e"><view class="section-title data-v-01e8e90e">特权设置</view><view class="form-item data-v-01e8e90e"><text class="form-label data-v-01e8e90e">特权有效期</text><view class="radio-group data-v-01e8e90e"><view class="{{['radio-item', 'data-v-01e8e90e', d && 'active']}}" bindtap="{{e}}"><text class="radio-text data-v-01e8e90e">生日当天</text></view><view class="{{['radio-item', 'data-v-01e8e90e', f && 'active']}}" bindtap="{{g}}"><text class="radio-text data-v-01e8e90e">生日当周</text></view><view class="{{['radio-item', 'data-v-01e8e90e', h && 'active']}}" bindtap="{{i}}"><text class="radio-text data-v-01e8e90e">生日当月</text></view></view></view><view class="form-item data-v-01e8e90e"><text class="form-label data-v-01e8e90e">提前通知</text><view class="form-input-group data-v-01e8e90e"><input type="number" class="form-input data-v-01e8e90e" value="{{j}}" bindinput="{{k}}"/><text class="input-suffix data-v-01e8e90e">天</text></view></view><view class="form-item switch-item data-v-01e8e90e"><text class="form-label data-v-01e8e90e">短信通知</text><switch class="data-v-01e8e90e" checked="{{l}}" bindchange="{{m}}" color="#4A00E0"/></view><view class="form-item switch-item data-v-01e8e90e"><text class="form-label data-v-01e8e90e">微信通知</text><switch class="data-v-01e8e90e" checked="{{n}}" bindchange="{{o}}" color="#4A00E0"/></view></view><view class="section-card data-v-01e8e90e"><view class="section-title data-v-01e8e90e">生日礼包</view><view class="privilege-list data-v-01e8e90e"><view wx:for="{{p}}" wx:for-item="privilege" wx:key="g" class="privilege-item data-v-01e8e90e"><view class="{{['privilege-checkbox', 'data-v-01e8e90e', privilege.b && 'checked']}}" bindtap="{{privilege.c}}"><view wx:if="{{privilege.a}}" class="checkbox-inner data-v-01e8e90e"></view></view><view class="privilege-content data-v-01e8e90e"><text class="privilege-name data-v-01e8e90e">{{privilege.d}}</text><text class="privilege-desc data-v-01e8e90e">{{privilege.e}}</text></view><view class="privilege-config data-v-01e8e90e" bindtap="{{privilege.f}}"><text class="config-text data-v-01e8e90e">设置</text></view></view></view><button class="add-btn data-v-01e8e90e" bindtap="{{q}}">添加生日特权</button></view><view class="section-card data-v-01e8e90e"><view class="section-title data-v-01e8e90e">适用会员等级</view><view class="level-list data-v-01e8e90e"><view wx:for="{{r}}" wx:for-item="level" wx:key="f" class="level-item data-v-01e8e90e"><view class="{{['level-checkbox', 'data-v-01e8e90e', level.b && 'checked']}}" bindtap="{{level.c}}"><view wx:if="{{level.a}}" class="checkbox-inner data-v-01e8e90e"></view></view><view class="level-content data-v-01e8e90e"><text class="level-name data-v-01e8e90e">{{level.d}}</text><text class="level-desc data-v-01e8e90e">{{level.e}}名会员</text></view></view></view></view><view class="section-card data-v-01e8e90e"><view class="section-title data-v-01e8e90e">生日祝福语</view><view class="form-item data-v-01e8e90e"><block wx:if="{{r0}}"><textarea class="form-textarea data-v-01e8e90e" placeholder="请输入生日祝福语" value="{{s}}" bindinput="{{t}}"/></block></view><view class="greeting-preview data-v-01e8e90e"><view class="preview-title data-v-01e8e90e">预览效果</view><view class="preview-card data-v-01e8e90e"><view class="preview-header data-v-01e8e90e"><image class="preview-logo data-v-01e8e90e" src="{{v}}" mode="aspectFit"></image><text class="preview-shop-name data-v-01e8e90e">磁州生活网</text></view><view class="preview-content data-v-01e8e90e"><text class="preview-greeting data-v-01e8e90e">{{w}}</text></view><view class="preview-footer data-v-01e8e90e"><text class="preview-btn data-v-01e8e90e">查看生日礼包</text></view></view></view></view><view class="bottom-bar data-v-01e8e90e"><button class="save-btn data-v-01e8e90e" bindtap="{{x}}">保存设置</button></view></block></view>