<template>
  <view class="channels-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">通知渠道设置</text>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 渠道列表 -->
    <view class="channels-list">
      <!-- 短信通知 -->
      <view class="channel-card">
        <view class="channel-header">
          <view class="channel-icon sms-icon">
            <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 15a2 2 0 01-2 2H7l-4 4V5a2 2 0 012-2h14a2 2 0 012 2z"></path>
            </svg>
          </view>
          <view class="channel-info">
            <text class="channel-name">短信通知</text>
            <text class="channel-desc">通过短信向用户发送通知</text>
          </view>
          <switch :checked="channels.sms.enabled" @change="toggleChannel('sms')" color="#1677FF" />
        </view>
        
        <view class="channel-content" v-if="channels.sms.enabled">
          <view class="form-item">
            <text class="form-label">服务商</text>
            <picker :range="smsProviders" @change="selectSmsProvider">
              <view class="picker-view">
                <text>{{channels.sms.provider}}</text>
                <view class="arrow-icon"></view>
              </view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="form-label">AccessKey</text>
            <input class="form-input" type="text" v-model="channels.sms.accessKey" placeholder="请输入AccessKey" />
          </view>
          
          <view class="form-item">
            <text class="form-label">SecretKey</text>
            <input class="form-input" type="password" password="true" v-model="channels.sms.secretKey" placeholder="请输入SecretKey" />
          </view>
          
          <view class="form-item">
            <text class="form-label">签名</text>
            <input class="form-input" type="text" v-model="channels.sms.signature" placeholder="请输入短信签名" />
          </view>
          
          <view class="form-item">
            <text class="form-label">日发送限额</text>
            <input class="form-input" type="number" v-model="channels.sms.dailyLimit" placeholder="请输入日发送限额" />
          </view>
          
          <button class="test-button" @click="testChannel('sms')">测试发送</button>
          <button class="save-button" @click="saveChannel('sms')">保存配置</button>
        </view>
      </view>
      
      <!-- 微信通知 -->
      <view class="channel-card">
        <view class="channel-header">
          <view class="channel-icon wechat-icon">
            <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M9 8a3 3 0 100-6 3 3 0 000 6z"></path>
              <path d="M15 8a3 3 0 100-6 3 3 0 000 6z"></path>
              <path d="M21 15c0 2.21-3.582 4-8 4s-8-1.79-8-4"></path>
              <path d="M3 15c0-2.21 3.582-4 8-4"></path>
            </svg>
          </view>
          <view class="channel-info">
            <text class="channel-name">微信通知</text>
            <text class="channel-desc">通过微信公众号/小程序向用户发送通知</text>
          </view>
          <switch :checked="channels.wechat.enabled" @change="toggleChannel('wechat')" color="#1677FF" />
        </view>
        
        <view class="channel-content" v-if="channels.wechat.enabled">
          <view class="form-item">
            <text class="form-label">通知类型</text>
            <radio-group @change="selectWechatType">
              <label class="radio-item">
                <radio value="mp" :checked="channels.wechat.type === 'mp'" color="#1677FF" />
                <text>公众号模板消息</text>
              </label>
              <label class="radio-item">
                <radio value="ma" :checked="channels.wechat.type === 'ma'" color="#1677FF" />
                <text>小程序订阅消息</text>
              </label>
            </radio-group>
          </view>
          
          <view class="form-item">
            <text class="form-label">AppID</text>
            <input class="form-input" type="text" v-model="channels.wechat.appId" placeholder="请输入AppID" />
          </view>
          
          <view class="form-item">
            <text class="form-label">AppSecret</text>
            <input class="form-input" type="password" password="true" v-model="channels.wechat.appSecret" placeholder="请输入AppSecret" />
          </view>
          
          <button class="test-button" @click="testChannel('wechat')">测试发送</button>
          <button class="save-button" @click="saveChannel('wechat')">保存配置</button>
        </view>
      </view>
      
      <!-- APP推送 -->
      <view class="channel-card">
        <view class="channel-header">
          <view class="channel-icon app-icon">
            <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
              <path d="M16 11.37A4 4 0 1112.63 8 4 4 0 0116 11.37z"></path>
              <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
            </svg>
          </view>
          <view class="channel-info">
            <text class="channel-name">APP推送</text>
            <text class="channel-desc">通过APP向用户发送推送通知</text>
          </view>
          <switch :checked="channels.app.enabled" @change="toggleChannel('app')" color="#1677FF" />
        </view>
        
        <view class="channel-content" v-if="channels.app.enabled">
          <view class="form-item">
            <text class="form-label">推送服务商</text>
            <picker :range="pushProviders" @change="selectPushProvider">
              <view class="picker-view">
                <text>{{channels.app.provider}}</text>
                <view class="arrow-icon"></view>
              </view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="form-label">AppKey</text>
            <input class="form-input" type="text" v-model="channels.app.appKey" placeholder="请输入AppKey" />
          </view>
          
          <view class="form-item">
            <text class="form-label">AppSecret</text>
            <input class="form-input" type="password" password="true" v-model="channels.app.appSecret" placeholder="请输入AppSecret" />
          </view>
          
          <view class="form-item">
            <text class="form-label">Android包名</text>
            <input class="form-input" type="text" v-model="channels.app.androidPackage" placeholder="请输入Android包名" />
          </view>
          
          <view class="form-item">
            <text class="form-label">iOS Bundle ID</text>
            <input class="form-input" type="text" v-model="channels.app.iosBundleId" placeholder="请输入iOS Bundle ID" />
          </view>
          
          <button class="test-button" @click="testChannel('app')">测试发送</button>
          <button class="save-button" @click="saveChannel('app')">保存配置</button>
        </view>
      </view>
      
      <!-- 邮件通知 -->
      <view class="channel-card">
        <view class="channel-header">
          <view class="channel-icon email-icon">
            <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
              <polyline points="22,6 12,13 2,6"></polyline>
            </svg>
          </view>
          <view class="channel-info">
            <text class="channel-name">邮件通知</text>
            <text class="channel-desc">通过邮件向用户发送通知</text>
          </view>
          <switch :checked="channels.email.enabled" @change="toggleChannel('email')" color="#1677FF" />
        </view>
        
        <view class="channel-content" v-if="channels.email.enabled">
          <view class="form-item">
            <text class="form-label">SMTP服务器</text>
            <input class="form-input" type="text" v-model="channels.email.smtpServer" placeholder="请输入SMTP服务器地址" />
          </view>
          
          <view class="form-item">
            <text class="form-label">SMTP端口</text>
            <input class="form-input" type="number" v-model="channels.email.smtpPort" placeholder="请输入SMTP端口" />
          </view>
          
          <view class="form-item">
            <text class="form-label">发件人邮箱</text>
            <input class="form-input" type="text" v-model="channels.email.sender" placeholder="请输入发件人邮箱" />
          </view>
          
          <view class="form-item">
            <text class="form-label">邮箱密码/授权码</text>
            <input class="form-input" type="password" password="true" v-model="channels.email.password" placeholder="请输入邮箱密码或授权码" />
          </view>
          
          <view class="form-item">
            <text class="form-label">SSL加密</text>
            <switch :checked="channels.email.ssl" @change="toggleEmailSsl" color="#1677FF" />
          </view>
          
          <button class="test-button" @click="testChannel('email')">测试发送</button>
          <button class="save-button" @click="saveChannel('email')">保存配置</button>
        </view>
      </view>
    </view>
    
    <!-- 测试发送弹窗 -->
    <uni-popup ref="testPopup" type="dialog">
      <uni-popup-dialog 
        title="测试发送" 
        :before-close="true" 
        @confirm="confirmTest" 
        @close="closeTestModal">
        <view class="test-form">
          <view class="form-item">
            <text class="form-label">接收对象</text>
            <input class="form-input" type="text" v-model="testForm.receiver" :placeholder="getReceiverPlaceholder()" />
          </view>
          
          <view class="form-item">
            <text class="form-label">测试内容</text>
            <textarea class="form-textarea" v-model="testForm.content" placeholder="请输入测试内容" />
          </view>
        </view>
      </uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      channels: {
        sms: {
          enabled: true,
          provider: '阿里云',
          accessKey: 'LTAI4G1234567890ABCD',
          secretKey: '******************************',
          signature: '磁州生活网',
          dailyLimit: 1000
        },
        wechat: {
          enabled: true,
          type: 'mp',
          appId: 'wx1234567890abcdef',
          appSecret: '******************************'
        },
        app: {
          enabled: true,
          provider: '个推',
          appKey: 'ABCDEFG1234567890',
          appSecret: '******************************',
          androidPackage: 'com.cizhou.merchant',
          iosBundleId: 'com.cizhou.merchant'
        },
        email: {
          enabled: false,
          smtpServer: 'smtp.exmail.qq.com',
          smtpPort: 465,
          sender: '<EMAIL>',
          password: '******************************',
          ssl: true
        }
      },
      smsProviders: ['阿里云', '腾讯云', '华为云', '七牛云'],
      pushProviders: ['个推', '极光推送', '小米推送', '华为推送'],
      testingChannel: '',
      testForm: {
        receiver: '',
        content: '这是一条测试通知，请忽略。'
      }
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    toggleChannel(channel) {
      this.channels[channel].enabled = !this.channels[channel].enabled;
    },
    selectSmsProvider(e) {
      this.channels.sms.provider = this.smsProviders[e.detail.value];
    },
    selectWechatType(e) {
      this.channels.wechat.type = e.detail.value;
    },
    selectPushProvider(e) {
      this.channels.app.provider = this.pushProviders[e.detail.value];
    },
    toggleEmailSsl(e) {
      this.channels.email.ssl = e.detail.value;
    },
    saveChannel(channel) {
      uni.showLoading({
        title: '保存中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '配置已保存',
          icon: 'success'
        });
      }, 1500);
    },
    testChannel(channel) {
      this.testingChannel = channel;
      this.testForm.receiver = '';
      this.testForm.content = '这是一条测试通知，请忽略。';
      this.$refs.testPopup.open();
    },
    getReceiverPlaceholder() {
      switch(this.testingChannel) {
        case 'sms':
          return '请输入手机号码';
        case 'wechat':
          return '请输入OpenID';
        case 'app':
          return '请输入设备ID';
        case 'email':
          return '请输入邮箱地址';
        default:
          return '请输入接收对象';
      }
    },
    confirmTest() {
      if (!this.testForm.receiver) {
        uni.showToast({
          title: '请输入接收对象',
          icon: 'none'
        });
        return;
      }
      
      uni.showLoading({
        title: '发送中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '测试消息已发送',
          icon: 'success'
        });
        this.$refs.testPopup.close();
      }, 1500);
    },
    closeTestModal() {
      this.$refs.testPopup.close();
    }
  }
}
</script>

<style>
.channels-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 20px;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
}

.channels-list {
  padding: 16px;
}

.channel-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.channel-header {
  display: flex;
  align-items: center;
}

.channel-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.sms-icon {
  background-color: #e6f7ff;
  color: #1677FF;
}

.wechat-icon {
  background-color: #f6ffed;
  color: #52c41a;
}

.app-icon {
  background-color: #fff7e6;
  color: #fa8c16;
}

.email-icon {
  background-color: #fcf4ff;
  color: #722ed1;
}

.channel-info {
  flex: 1;
}

.channel-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.channel-desc {
  font-size: 12px;
  color: #999;
}

.channel-content {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.form-item {
  margin-bottom: 16px;
}

.form-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  display: block;
}

.form-input {
  width: 100%;
  height: 40px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
  color: #333;
  background-color: #f9f9f9;
}

.form-textarea {
  width: 100%;
  height: 80px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  color: #333;
  background-color: #f9f9f9;
}

.picker-view {
  width: 100%;
  height: 40px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
  color: #333;
  background-color: #f9f9f9;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.arrow-icon {
  width: 8px;
  height: 8px;
  border-top: 1px solid #999;
  border-right: 1px solid #999;
  transform: rotate(135deg);
}

.radio-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.radio-item:last-child {
  margin-bottom: 0;
}

.radio-item text {
  margin-left: 8px;
  font-size: 14px;
  color: #333;
}

.test-button {
  width: 100%;
  height: 40px;
  background-color: #f0f0f0;
  color: #333;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}

.save-button {
  width: 100%;
  height: 40px;
  background-color: #1677FF;
  color: #fff;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.test-form {
  padding: 0 16px;
}
</style> 