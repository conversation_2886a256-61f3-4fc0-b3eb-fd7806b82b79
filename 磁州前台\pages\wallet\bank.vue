<template>
  <view class="bank-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">银行卡管理</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 银行卡列表 -->
    <view class="bank-card-list" :style="{ marginTop: (navbarHeight + 10) + 'px' }">
      <view v-if="bankCards.length > 0">
        <view 
          class="bank-card-item" 
          v-for="(card, index) in bankCards" 
          :key="index"
          :class="{'selected': selectedCardId === card.id}"
          @click="selectCard(card.id)"
        >
          <view class="card-header">
            <view class="bank-logo">
              <image :src="card.bankLogo" class="bank-logo-img"></image>
            </view>
            <view class="bank-name">{{ card.bankName }}</view>
            <view class="card-type">{{ card.cardType }}</view>
          </view>
          <view class="card-number">**** **** **** {{ card.cardNumberLast4 }}</view>
          <view class="card-footer">
            <view class="card-holder">{{ card.cardHolder }}</view>
            <view class="card-actions">
              <view class="default-tag" v-if="card.isDefault">默认</view>
              <view class="card-action" @click.stop="setAsDefault(card.id)" v-if="!card.isDefault">设为默认</view>
              <view class="card-action delete" @click.stop="deleteCard(card.id)">删除</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view v-else class="empty-view">
        <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
        <view class="empty-text">暂无银行卡</view>
      </view>
    </view>
    
    <!-- 添加银行卡按钮 -->
    <view class="add-card-btn" @click="showAddCardPopup">
      <image src="/static/images/tabbar/添加.png" class="add-icon"></image>
      <text>添加银行卡</text>
    </view>
    
    <!-- 添加银行卡弹窗 -->
    <view class="popup-mask" v-if="showPopup" @click="closePopup"></view>
    <view class="popup-content" v-if="showPopup">
      <view class="popup-header">
        <view class="popup-title">添加银行卡</view>
        <view class="popup-close" @click="closePopup">×</view>
      </view>
      <view class="popup-body">
        <view class="form-item">
          <view class="form-label">持卡人</view>
          <input class="form-input" v-model="newCard.cardHolder" placeholder="请输入持卡人姓名" />
        </view>
        <view class="form-item">
          <view class="form-label">卡号</view>
          <input class="form-input" v-model="newCard.cardNumber" placeholder="请输入银行卡号" type="number" maxlength="19" @input="formatCardNumber" />
        </view>
        <view class="form-item">
          <view class="form-label">开户银行</view>
          <picker class="form-picker" :range="bankList" range-key="name" @change="onBankChange">
            <view class="picker-value">{{ newCard.bankName || '请选择开户银行' }}</view>
          </picker>
        </view>
        <view class="form-item">
          <view class="form-label">手机号</view>
          <input class="form-input" v-model="newCard.phone" placeholder="请输入银行预留手机号" type="number" maxlength="11" />
        </view>
        <view class="form-checkbox">
          <checkbox :checked="newCard.isDefault" @click="newCard.isDefault = !newCard.isDefault" color="#0052CC" />
          <text class="checkbox-label">设为默认银行卡</text>
        </view>
      </view>
      <view class="popup-footer">
        <button class="cancel-btn" @click="closePopup">取消</button>
        <button class="confirm-btn" @click="addCard" :disabled="!canAddCard">确定</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20,
      navbarHeight: 64,
      bankCards: [
        {
          id: 'card001',
          bankName: '中国建设银行',
          bankLogo: '/static/images/banks/ccb.png',
          cardType: '储蓄卡',
          cardNumber: '6217 0012 3456 7890',
          cardNumberLast4: '7890',
          cardHolder: '张三',
          isDefault: true
        },
        {
          id: 'card002',
          bankName: '中国工商银行',
          bankLogo: '/static/images/banks/icbc.png',
          cardType: '储蓄卡',
          cardNumber: '6222 0212 3456 1234',
          cardNumberLast4: '1234',
          cardHolder: '张三',
          isDefault: false
        }
      ],
      selectedCardId: 'card001',
      showPopup: false,
      newCard: {
        cardHolder: '',
        cardNumber: '',
        bankName: '',
        bankId: '',
        phone: '',
        isDefault: false
      },
      bankList: [
        { id: 'icbc', name: '中国工商银行', logo: '/static/images/banks/icbc.png' },
        { id: 'abc', name: '中国农业银行', logo: '/static/images/banks/abc.png' },
        { id: 'boc', name: '中国银行', logo: '/static/images/banks/boc.png' },
        { id: 'ccb', name: '中国建设银行', logo: '/static/images/banks/ccb.png' },
        { id: 'psbc', name: '中国邮政储蓄银行', logo: '/static/images/banks/psbc.png' },
        { id: 'cmb', name: '招商银行', logo: '/static/images/banks/cmb.png' },
        { id: 'spdb', name: '浦发银行', logo: '/static/images/banks/spdb.png' },
        { id: 'cib', name: '兴业银行', logo: '/static/images/banks/cib.png' }
      ]
    }
  },
  computed: {
    canAddCard() {
      return this.newCard.cardHolder && 
             this.newCard.cardNumber && 
             this.newCard.cardNumber.length >= 16 &&
             this.newCard.bankName &&
             this.newCard.phone && 
             this.newCard.phone.length === 11;
    }
  },
  onLoad() {
    // 获取状态栏高度
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 44;
    
    // 获取银行卡列表
    this.getBankCards();
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 获取银行卡列表
    getBankCards() {
      // 这里应该是从API获取银行卡列表
      // 模拟API请求，数据已在data中初始化
    },
    
    // 选择银行卡
    selectCard(cardId) {
      this.selectedCardId = cardId;
    },
    
    // 设置默认银行卡
    setAsDefault(cardId) {
      uni.showLoading({
        title: '设置中...'
      });
      
      // 模拟API请求
      setTimeout(() => {
        uni.hideLoading();
        
        // 更新本地数据
        this.bankCards.forEach(card => {
          card.isDefault = card.id === cardId;
        });
        
        uni.showToast({
          title: '设置成功',
          icon: 'success'
        });
      }, 500);
    },
    
    // 删除银行卡
    deleteCard(cardId) {
      uni.showModal({
        title: '提示',
        content: '确定要删除此银行卡吗？',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '删除中...'
            });
            
            // 模拟API请求
            setTimeout(() => {
              uni.hideLoading();
              
              // 更新本地数据
              const index = this.bankCards.findIndex(card => card.id === cardId);
              if (index !== -1) {
                // 如果删除的是默认卡，则设置第一张卡为默认
                const isDefault = this.bankCards[index].isDefault;
                this.bankCards.splice(index, 1);
                
                if (isDefault && this.bankCards.length > 0) {
                  this.bankCards[0].isDefault = true;
                }
                
                // 更新选中的卡
                if (this.selectedCardId === cardId) {
                  this.selectedCardId = this.bankCards.length > 0 ? this.bankCards[0].id : '';
                }
              }
              
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              });
            }, 500);
          }
        }
      });
    },
    
    // 显示添加银行卡弹窗
    showAddCardPopup() {
      this.showPopup = true;
      this.resetNewCard();
    },
    
    // 关闭弹窗
    closePopup() {
      this.showPopup = false;
    },
    
    // 重置新卡信息
    resetNewCard() {
      this.newCard = {
        cardHolder: '',
        cardNumber: '',
        bankName: '',
        bankId: '',
        phone: '',
        isDefault: false
      };
    },
    
    // 格式化卡号输入
    formatCardNumber(e) {
      let value = e.detail.value.replace(/\s/g, '');
      let formattedValue = '';
      
      for (let i = 0; i < value.length; i++) {
        if (i > 0 && i % 4 === 0) {
          formattedValue += ' ';
        }
        formattedValue += value[i];
      }
      
      this.newCard.cardNumber = formattedValue;
    },
    
    // 银行选择变化
    onBankChange(e) {
      const index = e.detail.value;
      const selectedBank = this.bankList[index];
      this.newCard.bankName = selectedBank.name;
      this.newCard.bankId = selectedBank.id;
    },
    
    // 添加银行卡
    addCard() {
      if (!this.canAddCard) return;
      
      uni.showLoading({
        title: '添加中...'
      });
      
      // 模拟API请求
      setTimeout(() => {
        uni.hideLoading();
        
        // 生成新卡信息
        const cardNumber = this.newCard.cardNumber.replace(/\s/g, '');
        const cardNumberLast4 = cardNumber.slice(-4);
        const selectedBank = this.bankList.find(bank => bank.id === this.newCard.bankId);
        
        const newCardInfo = {
          id: 'card' + Date.now(),
          bankName: this.newCard.bankName,
          bankLogo: selectedBank ? selectedBank.logo : '/static/images/banks/default.png',
          cardType: '储蓄卡',
          cardNumber: this.newCard.cardNumber,
          cardNumberLast4: cardNumberLast4,
          cardHolder: this.newCard.cardHolder,
          isDefault: this.newCard.isDefault
        };
        
        // 如果设置为默认卡，更新其他卡的状态
        if (newCardInfo.isDefault) {
          this.bankCards.forEach(card => {
            card.isDefault = false;
          });
        }
        
        // 添加到列表
        this.bankCards.push(newCardInfo);
        this.selectedCardId = newCardInfo.id;
        
        // 关闭弹窗
        this.closePopup();
        
        uni.showToast({
          title: '添加成功',
          icon: 'success'
        });
      }, 800);
    }
  }
}
</script>

<style>
.bank-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 120rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
}

.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.navbar-right {
  width: 80rpx;
}

/* 银行卡列表 */
.bank-card-list {
  padding: 30rpx;
}

.bank-card-item {
  background: linear-gradient(to right, #0052CC, #0066FF);
  border-radius: 20rpx;
  padding: 40rpx;
  color: #fff;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 82, 204, 0.2);
  position: relative;
}

.bank-card-item.selected {
  box-shadow: 0 8rpx 30rpx rgba(0, 82, 204, 0.4);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.bank-logo {
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.bank-logo-img {
  width: 60rpx;
  height: 60rpx;
}

.bank-name {
  font-size: 32rpx;
  font-weight: 500;
  flex: 1;
}

.card-type {
  font-size: 24rpx;
  opacity: 0.8;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
}

.card-number {
  font-size: 40rpx;
  font-weight: bold;
  letter-spacing: 4rpx;
  margin-bottom: 40rpx;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-holder {
  font-size: 28rpx;
}

.card-actions {
  display: flex;
  align-items: center;
}

.default-tag {
  font-size: 24rpx;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
  margin-right: 20rpx;
}

.card-action {
  font-size: 24rpx;
  margin-left: 20rpx;
  opacity: 0.8;
}

.card-action.delete {
  color: #ffcccc;
}

/* 添加银行卡按钮 */
.add-card-btn {
  position: fixed;
  bottom: 30rpx;
  left: 30rpx;
  right: 30rpx;
  height: 90rpx;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 45rpx;
  font-size: 32rpx;
}

.add-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

/* 空状态 */
.empty-view {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 弹窗样式 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.popup-content {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  z-index: 1001;
  padding-bottom: env(safe-area-inset-bottom);
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 500;
}

.popup-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-body {
  padding: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.form-input {
  width: 100%;
  height: 90rpx;
  background-color: #f8f9fc;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.form-picker {
  width: 100%;
  height: 90rpx;
  background-color: #f8f9fc;
  border-radius: 10rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
}

.picker-value {
  font-size: 28rpx;
  color: #333;
}

.form-checkbox {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
}

.checkbox-label {
  font-size: 28rpx;
  margin-left: 10rpx;
}

.popup-footer {
  display: flex;
  padding: 30rpx;
  border-top: 1rpx solid #f5f5f5;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-size: 32rpx;
  border-radius: 45rpx;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
  margin-right: 20rpx;
}

.confirm-btn {
  background-color: #0052CC;
  color: #fff;
}

.confirm-btn[disabled] {
  background-color: #cccccc;
}
</style> 