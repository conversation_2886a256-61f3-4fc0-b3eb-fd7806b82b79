
.distribution-section {
  padding: 0 24rpx;
  margin: 30rpx 0;
  position: relative;
  z-index: 10;
}
.distribution-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(245,245,247,0.9) 100%);
  padding: 28rpx 30rpx;
  border-radius: 35rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08), 
              0 2rpx 4rpx rgba(0, 0, 0, 0.05),
              0 0 1rpx rgba(0, 0, 0, 0.15);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}
.distribution-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
  opacity: 0.5;
}
.distribution-left {
  display: flex;
  align-items: center;
}
.distribution-icon {
  width: 70rpx;
  height: 70rpx;
  border-radius: 22rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

/* 默认样式 - 紫色 */
.distribution-icon {
  background: linear-gradient(135deg, #6B0FBE 0%, #9013FE 100%);
}

/* 拼团活动 - 红色 */
.distribution-icon.type-group {
  background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
  box-shadow: 0 4rpx 12rpx rgba(255, 44, 84, 0.2);
}

/* 秒杀活动 - 橙色 */
.distribution-icon.type-flash {
  background: linear-gradient(135deg, #FF6B00 0%, #FF9500 100%);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 0, 0.2);
}

/* 优惠券 - 蓝色 */
.distribution-icon.type-coupon {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}

/* 满减活动 - 绿色 */
.distribution-icon.type-discount {
  background: linear-gradient(135deg, #34C759 0%, #30DB5B 100%);
  box-shadow: 0 4rpx 12rpx rgba(52, 199, 89, 0.2);
}
.icon-image {
  width: 40rpx;
  height: 40rpx;
  filter: brightness(0) invert(1);
}
.distribution-info {
  display: flex;
  flex-direction: column;
}
.distribution-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1D1D1F;
  margin-bottom: 8rpx;
  letter-spacing: 0.5rpx;
}
.distribution-desc {
  font-size: 26rpx;
  color: #FF6B00;
  font-weight: 500;
}
.distribution-right {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  border-radius: 30rpx;
}

/* 默认样式 - 紫色 */
.distribution-right {
  background: linear-gradient(135deg, #6B0FBE 0%, #9013FE 100%);
  box-shadow: 0 4rpx 12rpx rgba(107, 15, 190, 0.2);
}

/* 拼团活动 - 红色 */
.distribution-right.type-group {
  background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
  box-shadow: 0 4rpx 12rpx rgba(255, 44, 84, 0.2);
}

/* 秒杀活动 - 橙色 */
.distribution-right.type-flash {
  background: linear-gradient(135deg, #FF6B00 0%, #FF9500 100%);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 0, 0.2);
}

/* 优惠券 - 蓝色 */
.distribution-right.type-coupon {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}

/* 满减活动 - 绿色 */
.distribution-right.type-discount {
  background: linear-gradient(135deg, #34C759 0%, #30DB5B 100%);
  box-shadow: 0 4rpx 12rpx rgba(52, 199, 89, 0.2);
}
.distribution-btn {
  font-size: 28rpx;
  color: #FFFFFF;
  font-weight: 500;
}
.arrow-icon {
  display: flex;
  align-items: center;
  margin-left: 8rpx;
  color: #FFFFFF;
}
