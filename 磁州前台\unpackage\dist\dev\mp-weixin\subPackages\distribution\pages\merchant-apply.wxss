/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.merchant-apply-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 88rpx 32rpx 30rpx;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);
}
.navbar-back {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 24rpx;
  height: 24rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}
.navbar-right {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

/* 专属分销说明 */
.intro-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.intro-header {
  margin-bottom: 30rpx;
}
.intro-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
}
.benefits-list {
  margin-bottom: 20rpx;
}
.benefit-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.benefit-item:last-child {
  margin-bottom: 0;
}
.benefit-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  border-radius: 10rpx;
  position: relative;
}
.benefit-icon.higher {
  background-color: #FF9500;
}
.benefit-icon.higher::before {
  content: "";
  position: absolute;
  width: 50rpx;
  height: 50rpx;
  top: 15rpx;
  left: 15rpx;
  background-color: white;
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M16,6L18.29,8.29L13.41,13.17L9.41,9.17L2,16.59L3.41,18L9.41,12L13.41,16L19.71,9.71L22,12V6H16Z' fill='white'/%3E%3C/svg%3E");
  mask-repeat: no-repeat;
  mask-position: center;
  mask-size: contain;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M16,6L18.29,8.29L13.41,13.17L9.41,9.17L2,16.59L3.41,18L9.41,12L13.41,16L19.71,9.71L22,12V6H16Z' fill='white'/%3E%3C/svg%3E");
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  -webkit-mask-size: contain;
}
.benefit-icon.priority {
  background-color: #34C759;
}
.benefit-icon.priority::before {
  content: "";
  position: absolute;
  width: 50rpx;
  height: 50rpx;
  top: 15rpx;
  left: 15rpx;
  background-color: white;
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M13,19H14A1,1 0 0,1 15,20H22V22H15A1,1 0 0,1 14,23H10A1,1 0 0,1 9,22H2V20H9A1,1 0 0,1 10,19H11V17H4A1,1 0 0,1 3,16V12A1,1 0 0,1 4,11H20A1,1 0 0,1 21,12V16A1,1 0 0,1 20,17H13V19M4,3H20A1,1 0 0,1 21,4V8A1,1 0 0,1 20,9H4A1,1 0 0,1 3,8V4A1,1 0 0,1 4,3M9,7H10V5H9V7M9,15H10V13H9V15M5,5V7H7V5H5M5,13V15H7V13H5Z' fill='white'/%3E%3C/svg%3E");
  mask-repeat: no-repeat;
  mask-position: center;
  mask-size: contain;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M13,19H14A1,1 0 0,1 15,20H22V22H15A1,1 0 0,1 14,23H10A1,1 0 0,1 9,22H2V20H9A1,1 0 0,1 10,19H11V17H4A1,1 0 0,1 3,16V12A1,1 0 0,1 4,11H20A1,1 0 0,1 21,12V16A1,1 0 0,1 20,17H13V19M4,3H20A1,1 0 0,1 21,4V8A1,1 0 0,1 20,9H4A1,1 0 0,1 3,8V4A1,1 0 0,1 4,3M9,7H10V5H9V7M9,15H10V13H9V15M5,5V7H7V5H5M5,13V15H7V13H5Z' fill='white'/%3E%3C/svg%3E");
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  -webkit-mask-size: contain;
}
.benefit-icon.exclusive {
  background-color: #6B0FBE;
}
.benefit-icon.exclusive::before {
  content: "";
  position: absolute;
  width: 50rpx;
  height: 50rpx;
  top: 15rpx;
  left: 15rpx;
  background-color: white;
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12,8L10.67,8.09C9.81,7.07 7.4,4.5 5,4.5C5,4.5 3.03,7.46 4.96,11.41C4.41,12.24 4.07,12.67 4,13.66L2.07,13.95L2.28,14.93L4.04,14.67L4.18,15.38L2.61,16.32L3.08,17.21L4.53,16.32L5.5,17.94L4.8,19.15L5.74,19.85L6.75,18.22L8.07,18.22L8.07,20.39L9.05,20.39L9.16,18.22L10.41,18.22L11.3,19.85L12.23,19.15L11.45,17.86L12.47,16.32L13.93,17.21L14.39,16.32L12.83,15.38L12.96,14.67L14.73,14.93L14.92,13.95L13.03,13.66C12.96,12.67 12.59,12.24 12.04,11.41C13.97,7.46 12,4.5 12,4.5C9.6,4.5 7.18,7.07 6.32,8.09L5,8L5.01,9H6.34C6.63,9.32 7.3,10.13 7.46,10.13C7.61,10.13 8.12,9.44 8.46,9H12L12,8Z' fill='white'/%3E%3C/svg%3E");
  mask-repeat: no-repeat;
  mask-position: center;
  mask-size: contain;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12,8L10.67,8.09C9.81,7.07 7.4,4.5 5,4.5C5,4.5 3.03,7.46 4.96,11.41C4.41,12.24 4.07,12.67 4,13.66L2.07,13.95L2.28,14.93L4.04,14.67L4.18,15.38L2.61,16.32L3.08,17.21L4.53,16.32L5.5,17.94L4.8,19.15L5.74,19.85L6.75,18.22L8.07,18.22L8.07,20.39L9.05,20.39L9.16,18.22L10.41,18.22L11.3,19.85L12.23,19.15L11.45,17.86L12.47,16.32L13.93,17.21L14.39,16.32L12.83,15.38L12.96,14.67L14.73,14.93L14.92,13.95L13.03,13.66C12.96,12.67 12.59,12.24 12.04,11.41C13.97,7.46 12,4.5 12,4.5C9.6,4.5 7.18,7.07 6.32,8.09L5,8L5.01,9H6.34C6.63,9.32 7.3,10.13 7.46,10.13C7.61,10.13 8.12,9.44 8.46,9H12L12,8Z' fill='white'/%3E%3C/svg%3E");
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  -webkit-mask-size: contain;
}
.benefit-info {
  flex: 1;
}
.benefit-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.benefit-desc {
  font-size: 24rpx;
  color: #666;
}

/* 商家列表 */
.section-header {
  margin: 30rpx 30rpx 20rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: #FFFFFF;
  margin-bottom: 20rpx;
}
.search-input-wrap {
  flex: 1;
  height: 72rpx;
  background: #F5F7FA;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  margin-right: 20rpx;
}
.search-icon {
  width: 32rpx;
  height: 32rpx;
  background-color: #6B0FBE;
  border-radius: 50%;
  position: relative;
  margin-right: 10rpx;
}
.search-icon::before {
  content: "";
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  border: 2rpx solid white;
  border-radius: 50%;
  top: 6rpx;
  left: 6rpx;
}
.search-icon::after {
  content: "";
  position: absolute;
  width: 8rpx;
  height: 2rpx;
  background-color: white;
  transform: rotate(45deg);
  bottom: 8rpx;
  right: 6rpx;
}
.search-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: #333;
}
.clear-icon {
  width: 32rpx;
  height: 32rpx;
  background-color: #cccccc;
  border-radius: 50%;
  position: relative;
}
.clear-icon::before,
.clear-icon::after {
  content: "";
  position: absolute;
  width: 16rpx;
  height: 2rpx;
  background-color: white;
  top: 50%;
  left: 50%;
}
.clear-icon::before {
  transform: translate(-50%, -50%) rotate(45deg);
}
.clear-icon::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}
.search-btn {
  font-size: 28rpx;
  color: #6B0FBE;
}

/* 商家列表 */
.merchants-list {
  margin: 0 30rpx;
}
.merchant-card {
  background: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.merchant-info {
  padding: 30rpx;
  display: flex;
  align-items: center;
}
.merchant-logo {
  width: 100rpx;
  height: 100rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}
.merchant-details {
  flex: 1;
}
.merchant-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}
.merchant-meta {
  display: flex;
  align-items: center;
}
.merchant-category {
  font-size: 24rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
}
.merchant-commission {
  font-size: 24rpx;
  color: #6B0FBE;
}
.merchant-status {
  width: 120rpx;
  height: 60rpx;
  background: #6B0FBE;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #fff;
}
.merchant-status.applied {
  background: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 26rpx;
  color: #666;
}

/* 申请弹窗 */
.apply-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}
.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  border-radius: 40rpx 40rpx 0 0;
  padding: 30rpx;
  animation: slideUp 0.3s ease-out;
}
@keyframes slideUp {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.close-icon {
  width: 40rpx;
  height: 40rpx;
  position: relative;
}
.close-icon::before,
.close-icon::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 32rpx;
  height: 2rpx;
  background-color: #999;
}
.close-icon::before {
  transform: translate(-50%, -50%) rotate(45deg);
}
.close-icon::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}
.modal-merchant {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}
.modal-merchant .merchant-logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 16rpx;
  margin-right: 0;
}
.modal-merchant .merchant-name {
  margin-bottom: 0;
}
.form-content {
  margin-bottom: 30rpx;
}
.form-item {
  margin-bottom: 30rpx;
  position: relative;
}
.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}
.form-textarea {
  width: 100%;
  height: 200rpx;
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}
.textarea-counter {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  font-size: 24rpx;
  color: #999;
}
.form-input {
  width: 100%;
  height: 80rpx;
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}
.modal-footer {
  display: flex;
  justify-content: space-between;
}
.cancel-btn,
.submit-btn {
  width: 48%;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}
.cancel-btn {
  background: #F5F7FA;
  color: #666;
}
.submit-btn {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
}
.submit-btn.disabled {
  background: #cccccc;
  color: #ffffff;
}