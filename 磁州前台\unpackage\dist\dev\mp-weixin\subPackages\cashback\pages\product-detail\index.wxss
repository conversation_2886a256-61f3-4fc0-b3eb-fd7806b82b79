/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-ffbebc8a, html.data-v-ffbebc8a, #app.data-v-ffbebc8a, .index-container.data-v-ffbebc8a {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.product-detail-container.data-v-ffbebc8a {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.content-container.data-v-ffbebc8a {
  padding-top: calc(var(--status-bar-height) + 44px);
  padding-bottom: 20px;
}
.product-info-section.data-v-ffbebc8a {
  background-color: #FFFFFF;
  padding: 16px;
}
.product-info-section .product-image.data-v-ffbebc8a {
  width: 100%;
  height: 300px;
  border-radius: 12px;
  margin-bottom: 16px;
}
.product-info-section .product-info .product-title.data-v-ffbebc8a {
  font-size: 16px;
  color: #333333;
  line-height: 1.4;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.product-info-section .product-info .product-price-row.data-v-ffbebc8a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}
.product-info-section .product-info .product-price-row .price-container.data-v-ffbebc8a {
  display: flex;
  align-items: baseline;
}
.product-info-section .product-info .product-price-row .price-container .price-symbol.data-v-ffbebc8a {
  font-size: 14px;
  color: #FF6B6B;
  margin-right: 2px;
}
.product-info-section .product-info .product-price-row .price-container .price-value.data-v-ffbebc8a {
  font-size: 20px;
  font-weight: 600;
  color: #FF6B6B;
}
.product-info-section .product-info .product-price-row .cashback-tag.data-v-ffbebc8a {
  background-color: rgba(156, 39, 176, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
}
.product-info-section .product-info .product-price-row .cashback-tag text.data-v-ffbebc8a {
  font-size: 12px;
  color: #9C27B0;
}
.product-info-section .product-info .product-source.data-v-ffbebc8a {
  display: flex;
  align-items: center;
}
.product-info-section .product-info .product-source .source-icon.data-v-ffbebc8a {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}
.product-info-section .product-info .product-source .source-name.data-v-ffbebc8a {
  font-size: 12px;
  color: #999999;
}
.price-compare-section.data-v-ffbebc8a {
  margin-top: 12px;
  background-color: #FFFFFF;
  padding: 16px;
}
.section-header.data-v-ffbebc8a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.section-header .section-title.data-v-ffbebc8a {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
}
.section-header .sort-options.data-v-ffbebc8a {
  display: flex;
}
.section-header .sort-options .sort-option.data-v-ffbebc8a {
  font-size: 14px;
  color: #999999;
  margin-left: 16px;
  position: relative;
}
.section-header .sort-options .sort-option--active.data-v-ffbebc8a {
  color: #9C27B0;
}
.section-header .sort-options .sort-option--active.data-v-ffbebc8a::after {
  content: "";
  position: absolute;
  bottom: -4px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #9C27B0;
  border-radius: 1px;
}
.price-list .price-item.data-v-ffbebc8a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #EEEEEE;
}
.price-list .price-item.data-v-ffbebc8a:last-child {
  border-bottom: none;
}
.price-list .price-item--best.data-v-ffbebc8a {
  background-color: rgba(156, 39, 176, 0.05);
  border-radius: 8px;
  padding: 16px 8px;
  margin: 0 -8px;
}
.price-list .price-item .price-item-left.data-v-ffbebc8a {
  display: flex;
  align-items: center;
}
.price-list .price-item .price-item-left .platform-icon.data-v-ffbebc8a {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}
.price-list .price-item .price-item-left .price-item-info .platform-name.data-v-ffbebc8a {
  font-size: 14px;
  color: #333333;
  margin-bottom: 4px;
}
.price-list .price-item .price-item-left .price-item-info .price-tag.data-v-ffbebc8a {
  display: inline-block;
  background-color: #9C27B0;
  border-radius: 2px;
  padding: 2px 4px;
}
.price-list .price-item .price-item-left .price-item-info .price-tag text.data-v-ffbebc8a {
  font-size: 10px;
  color: #FFFFFF;
}
.price-list .price-item .price-item-right.data-v-ffbebc8a {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.price-list .price-item .price-item-right .price-container.data-v-ffbebc8a {
  margin-bottom: 4px;
}
.price-list .price-item .price-item-right .price-container .price-value.data-v-ffbebc8a {
  font-size: 16px;
  font-weight: 500;
  color: #FF6B6B;
}
.price-list .price-item .price-item-right .cashback-value.data-v-ffbebc8a {
  font-size: 12px;
  color: #9C27B0;
  margin-bottom: 8px;
}
.price-list .price-item .price-item-right .go-button.data-v-ffbebc8a {
  display: flex;
  align-items: center;
  background-color: #9C27B0;
  border-radius: 12px;
  padding: 4px 10px;
}
.price-list .price-item .price-item-right .go-button text.data-v-ffbebc8a {
  font-size: 12px;
  color: #FFFFFF;
}
.price-list .price-item .price-item-right .go-button .arrow-icon.data-v-ffbebc8a {
  margin-left: 2px;
}
.product-detail-section.data-v-ffbebc8a {
  margin-top: 12px;
  background-color: #FFFFFF;
  padding: 16px;
}