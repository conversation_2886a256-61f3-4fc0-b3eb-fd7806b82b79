@echo off
chcp 65001 >nul

echo 🚀 启动磁州生活网后台管理系统开发环境

REM 检查Docker是否运行
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker未运行，请先启动Docker
    pause
    exit /b 1
)

REM 启动基础服务
echo 📦 启动基础服务（MySQL、Redis、Nacos）...
docker-compose -f docker-compose.dev.yml up -d mysql redis nacos

REM 等待服务启动
echo ⏳ 等待服务启动...
timeout /t 30 /nobreak >nul

REM 初始化数据库
echo 📊 初始化数据库...
docker exec -i cizhou-mysql-dev mysql -uroot -pcizhou123456 < sql/init.sql

echo ✅ 开发环境启动完成！
echo.
echo 📋 服务信息：
echo    MySQL:    localhost:3306 (root/cizhou123456)
echo    Redis:    localhost:6379 (cizhou123456)
echo    Nacos:    http://localhost:8848 (nacos/nacos)
echo.
echo 🔧 下一步：
echo    1. 在IDE中启动 cizhou-auth 服务
echo    2. 在IDE中启动 cizhou-gateway 服务
echo    3. 访问前端页面进行登录测试
echo.
echo 👤 默认管理员账号：
echo    用户名: admin
echo    密码:   admin123

pause
