{"version": 3, "file": "create.js", "sources": ["subPackages/merchant-admin/pages/products/create.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW5ccGFnZXNccHJvZHVjdHNcY3JlYXRlLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"product-publish-container\">\r\n    <!-- 状态栏占位 -->\r\n    <view class=\"status-bar-placeholder\"></view>\r\n    \r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\">\r\n      <view class=\"navbar-left\" @tap=\"goBack\">\r\n        <view class=\"back-button\">\r\n          <text class=\"icon-back\">←</text>\r\n        </view>\r\n      </view>\r\n      <view class=\"navbar-title\">\r\n        <text class=\"title-text\">发布商品</text>\r\n      </view>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-button\" @tap=\"showHelp\">\r\n          <text class=\"icon-help\">?</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 主内容区域 -->\r\n    <scroll-view class=\"content-scroll\" scroll-y>\r\n      <!-- 商品基本信息 -->\r\n      <view class=\"form-section\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">基本信息</text>\r\n          <text class=\"section-subtitle\">填写商品的基本信息</text>\r\n        </view>\r\n        \r\n        <!-- 商品图片上传 -->\r\n        <view class=\"image-upload-area\">\r\n          <view class=\"upload-title\">\r\n            <text class=\"required\">*</text>\r\n            <text>商品图片</text>\r\n            <text class=\"upload-desc\">最多9张，建议尺寸800x800</text>\r\n          </view>\r\n          \r\n          <view class=\"image-grid\">\r\n            <view v-for=\"(image, index) in productForm.images\" :key=\"index\" class=\"image-item\">\r\n              <image :src=\"image\" mode=\"aspectFill\" class=\"preview-image\"></image>\r\n              <view class=\"delete-btn\" @tap=\"removeImage(index)\">×</view>\r\n            </view>\r\n            \r\n            <view v-if=\"productForm.images.length < 9\" class=\"upload-item\" @tap=\"chooseImage\">\r\n              <text class=\"upload-icon\">+</text>\r\n              <text class=\"upload-text\">上传图片</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 商品名称 -->\r\n        <view class=\"form-item\">\r\n          <view class=\"form-label\">\r\n            <text class=\"required\">*</text>\r\n            <text>商品名称</text>\r\n          </view>\r\n          <view class=\"form-input-wrap\">\r\n            <input \r\n              class=\"form-input\" \r\n              type=\"text\" \r\n              v-model=\"productForm.name\" \r\n              placeholder=\"请输入商品名称（2-40字）\" \r\n              maxlength=\"40\"\r\n            />\r\n            <text class=\"input-counter\">{{productForm.name.length}}/40</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 商品分类 -->\r\n        <view class=\"form-item\">\r\n          <view class=\"form-label\">\r\n            <text class=\"required\">*</text>\r\n            <text>商品分类</text>\r\n          </view>\r\n          <view class=\"form-input-wrap selector\" @tap=\"showCategoryPicker\">\r\n            <text class=\"selector-text\" :class=\"{ placeholder: !productForm.category }\">\r\n              {{ productForm.category || '请选择商品分类' }}\r\n            </text>\r\n            <text class=\"selector-arrow\">›</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 价格区域 -->\r\n        <view class=\"price-row\">\r\n          <!-- 商品价格 -->\r\n          <view class=\"form-item price-item\">\r\n            <view class=\"form-label\">\r\n              <text class=\"required\">*</text>\r\n              <text>售价</text>\r\n            </view>\r\n            <view class=\"form-input-wrap price-input\">\r\n              <text class=\"price-symbol\">¥</text>\r\n              <input \r\n                class=\"form-input\" \r\n                type=\"digit\" \r\n                v-model=\"productForm.price\" \r\n                placeholder=\"0.00\" \r\n              />\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 原价 -->\r\n          <view class=\"form-item price-item\">\r\n            <view class=\"form-label\">\r\n              <text>原价</text>\r\n              <text class=\"label-tip\">(选填)</text>\r\n            </view>\r\n            <view class=\"form-input-wrap price-input\">\r\n              <text class=\"price-symbol\">¥</text>\r\n              <input \r\n                class=\"form-input\" \r\n                type=\"digit\" \r\n                v-model=\"productForm.originalPrice\" \r\n                placeholder=\"0.00\" \r\n              />\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 库存 -->\r\n        <view class=\"form-item\">\r\n          <view class=\"form-label\">\r\n            <text class=\"required\">*</text>\r\n            <text>库存数量</text>\r\n          </view>\r\n          <view class=\"form-input-wrap stock-input\">\r\n            <view class=\"stock-btn minus\" @tap=\"decreaseStock\" :class=\"{ disabled: productForm.stock <= 0 }\">-</view>\r\n            <input \r\n              class=\"form-input stock-value\" \r\n              type=\"number\" \r\n              v-model=\"productForm.stock\" \r\n            />\r\n            <view class=\"stock-btn plus\" @tap=\"increaseStock\">+</view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 商品详情 -->\r\n      <view class=\"form-section\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">商品详情</text>\r\n          <text class=\"section-subtitle\">详细描述您的商品特点和卖点</text>\r\n        </view>\r\n        \r\n        <!-- 商品描述 -->\r\n        <view class=\"form-item\">\r\n          <view class=\"form-label\">\r\n            <text class=\"required\">*</text>\r\n            <text>商品描述</text>\r\n          </view>\r\n          <view class=\"form-input-wrap textarea-wrap\">\r\n            <textarea \r\n              class=\"form-textarea\" \r\n              v-model=\"productForm.description\" \r\n              placeholder=\"请详细描述商品的特点、用途、材质等信息\" \r\n              maxlength=\"500\"\r\n            ></textarea>\r\n            <text class=\"input-counter textarea-counter\">{{productForm.description ? productForm.description.length : 0}}/500</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 规格参数 -->\r\n        <view class=\"form-item\">\r\n          <view class=\"form-label specs-header\">\r\n            <view class=\"specs-title\">\r\n              <text>规格参数</text>\r\n              <text class=\"label-tip\">(选填)</text>\r\n            </view>\r\n            <view class=\"add-spec-btn\" @tap=\"addSpec\">\r\n              <text class=\"add-spec-icon\">+</text>\r\n              <text class=\"add-spec-text\">添加规格</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"specs-list\">\r\n            <view v-for=\"(spec, index) in productForm.specs\" :key=\"index\" class=\"spec-item\">\r\n              <view class=\"spec-inputs\">\r\n                <input \r\n                  class=\"spec-key\" \r\n                  type=\"text\" \r\n                  v-model=\"spec.key\" \r\n                  placeholder=\"规格名称\" \r\n                />\r\n                <text class=\"spec-separator\">:</text>\r\n                <input \r\n                  class=\"spec-value\" \r\n                  type=\"text\" \r\n                  v-model=\"spec.value\" \r\n                  placeholder=\"规格值\" \r\n                />\r\n              </view>\r\n              <view class=\"delete-spec-btn\" @tap=\"removeSpec(index)\">×</view>\r\n            </view>\r\n            \r\n            <view v-if=\"productForm.specs.length === 0\" class=\"empty-specs\">\r\n              <text>点击\"添加规格\"按钮添加商品规格参数</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 营销设置 -->\r\n      <view class=\"form-section\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">营销设置</text>\r\n          <text class=\"section-subtitle\">设置商品的营销相关信息</text>\r\n        </view>\r\n        \r\n        <!-- 营销标签 -->\r\n        <view class=\"form-item\">\r\n          <view class=\"form-label\">\r\n            <text>营销标签</text>\r\n            <text class=\"label-tip\">(选填)</text>\r\n          </view>\r\n          \r\n          <view class=\"tags-container\">\r\n            <view \r\n              v-for=\"(tag, index) in marketingTags\" \r\n              :key=\"index\" \r\n              class=\"tag-item\" \r\n              :class=\"{ active: selectedTags.includes(tag.id) }\"\r\n              @tap=\"toggleTag(tag.id)\"\r\n            >\r\n              <text>{{tag.name}}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 限购设置 -->\r\n        <view class=\"form-item\">\r\n          <view class=\"form-label\">\r\n            <text>限购数量</text>\r\n            <text class=\"label-tip\">(选填，0表示不限购)</text>\r\n          </view>\r\n          <view class=\"form-input-wrap\">\r\n            <input \r\n              class=\"form-input\" \r\n              type=\"number\" \r\n              v-model=\"productForm.purchaseLimit\" \r\n              placeholder=\"0\" \r\n            />\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n    \r\n    <!-- 底部操作栏 -->\r\n    <view class=\"bottom-action-bar\">\r\n      <button class=\"action-button save-draft\" @tap=\"saveDraft\">保存草稿</button>\r\n      <button class=\"action-button publish\" @tap=\"publishProduct\">立即发布</button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive } from 'vue';\r\n\r\n// 商品表单数据\r\nconst productForm = reactive({\r\n  name: '',\r\n  category: '',\r\n  price: '',\r\n  originalPrice: '',\r\n  stock: 0,\r\n  images: [],\r\n  description: '',\r\n  specs: [],\r\n  purchaseLimit: 0\r\n});\r\n\r\n// 营销标签\r\nconst marketingTags = reactive([\r\n  { id: 1, name: '新品' },\r\n  { id: 2, name: '热卖' },\r\n  { id: 3, name: '限时' },\r\n  { id: 4, name: '促销' },\r\n  { id: 5, name: '包邮' },\r\n  { id: 6, name: '品质保障' }\r\n]);\r\n\r\n// 已选标签\r\nconst selectedTags = reactive([]);\r\n\r\n// 方法\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\nconst showHelp = () => {\r\n  uni.showToast({\r\n    title: '帮助中心即将上线',\r\n    icon: 'none'\r\n  });\r\n};\r\n\r\n// 图片上传\r\nconst chooseImage = () => {\r\n  uni.chooseImage({\r\n    count: 9 - productForm.images.length,\r\n    sizeType: ['original', 'compressed'],\r\n    sourceType: ['album', 'camera'],\r\n    success: (res) => {\r\n      productForm.images = [...productForm.images, ...res.tempFilePaths];\r\n    }\r\n  });\r\n};\r\n\r\nconst removeImage = (index) => {\r\n  productForm.images.splice(index, 1);\r\n};\r\n\r\n// 分类选择\r\nconst showCategoryPicker = () => {\r\n  uni.showActionSheet({\r\n    itemList: ['食品饮料', '服装服饰', '美妆个护', '家居家纺', '数码电器', '其他分类'],\r\n    success: (res) => {\r\n      const categories = ['食品饮料', '服装服饰', '美妆个护', '家居家纺', '数码电器', '其他分类'];\r\n      productForm.category = categories[res.tapIndex];\r\n    }\r\n  });\r\n};\r\n\r\n// 库存操作\r\nconst increaseStock = () => {\r\n  productForm.stock++;\r\n};\r\n\r\nconst decreaseStock = () => {\r\n  if (productForm.stock > 0) {\r\n    productForm.stock--;\r\n  }\r\n};\r\n\r\nconst saveDraft = () => {\r\n  uni.showToast({\r\n    title: '已保存草稿',\r\n    icon: 'success'\r\n  });\r\n};\r\n\r\n// 规格参数操作\r\nconst addSpec = () => {\r\n  if (productForm.specs.length >= 10) {\r\n    return uni.showToast({\r\n      title: '最多添加10个规格参数',\r\n      icon: 'none'\r\n    });\r\n  }\r\n  productForm.specs.push({ key: '', value: '' });\r\n};\r\n\r\nconst removeSpec = (index) => {\r\n  productForm.specs.splice(index, 1);\r\n};\r\n\r\n// 标签操作\r\nconst toggleTag = (tagId) => {\r\n  const index = selectedTags.indexOf(tagId);\r\n  if (index > -1) {\r\n    selectedTags.splice(index, 1);\r\n  } else {\r\n    if (selectedTags.length >= 3) {\r\n      return uni.showToast({\r\n        title: '最多选择3个标签',\r\n        icon: 'none'\r\n      });\r\n    }\r\n    selectedTags.push(tagId);\r\n  }\r\n};\r\n\r\nconst publishProduct = () => {\r\n  // 表单验证\r\n  if (!productForm.name) {\r\n    return uni.showToast({ title: '请输入商品名称', icon: 'none' });\r\n  }\r\n  if (!productForm.category) {\r\n    return uni.showToast({ title: '请选择商品分类', icon: 'none' });\r\n  }\r\n  if (!productForm.price) {\r\n    return uni.showToast({ title: '请输入商品价格', icon: 'none' });\r\n  }\r\n  if (productForm.images.length === 0) {\r\n    return uni.showToast({ title: '请至少上传一张商品图片', icon: 'none' });\r\n  }\r\n  if (!productForm.description) {\r\n    return uni.showToast({ title: '请输入商品描述', icon: 'none' });\r\n  }\r\n  \r\n  // 收集所有数据\r\n  const productData = {\r\n    ...productForm,\r\n    tags: selectedTags.map(id => {\r\n      const tag = marketingTags.find(t => t.id === id);\r\n      return tag ? tag.name : '';\r\n    }).filter(Boolean)\r\n  };\r\n  \r\n  console.log('商品数据:', productData);\r\n  \r\n  uni.showLoading({ title: '正在发布...' });\r\n  \r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    uni.showToast({ title: '发布成功', icon: 'success' });\r\n    \r\n    // 延迟返回上一页\r\n    setTimeout(() => {\r\n      uni.navigateBack();\r\n    }, 1500);\r\n  }, 2000);\r\n};\r\n</script>\r\n\r\n<style>\r\n/* 全局样式 */\r\n.product-publish-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  position: relative;\r\n}\r\n\r\n/* 状态栏占位 */\r\n.status-bar-placeholder {\r\n  height: var(--status-bar-height, 20px);\r\n  width: 100%;\r\n  background: linear-gradient(135deg, #3A86FF, #5E60CE);\r\n}\r\n\r\n/* 导航栏样式 */\r\n.custom-navbar {\r\n  display: flex;\r\n  align-items: center;\r\n  height: 44px;\r\n  padding: 0 16px;\r\n  background: linear-gradient(135deg, #3A86FF, #5E60CE);\r\n  position: relative;\r\n}\r\n\r\n.navbar-left, .navbar-right {\r\n  width: 44px;\r\n  height: 44px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n}\r\n\r\n.title-text {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #FFFFFF;\r\n}\r\n\r\n.back-button, .help-button {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 16px;\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.icon-back, .icon-help {\r\n  color: #FFFFFF;\r\n  font-size: 18px;\r\n}\r\n\r\n/* 内容区域 */\r\n.content-scroll {\r\n  flex: 1;\r\n  padding: 16px;\r\n}\r\n\r\n/* 表单区域 */\r\n.form-section {\r\n  background-color: #FFFFFF;\r\n  border-radius: 16px;\r\n  padding: 20px;\r\n  margin-bottom: 16px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.section-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 6px;\r\n  display: block;\r\n}\r\n\r\n.section-subtitle {\r\n  font-size: 14px;\r\n  color: #999;\r\n}\r\n\r\n/* 图片上传区域 */\r\n.image-upload-area {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.upload-title {\r\n  margin-bottom: 12px;\r\n  font-size: 15px;\r\n  color: #333;\r\n}\r\n\r\n.required {\r\n  color: #FF3B30;\r\n  margin-right: 4px;\r\n}\r\n\r\n.upload-desc {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-left: 8px;\r\n}\r\n\r\n.image-grid {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: 0 -6px;\r\n}\r\n\r\n.image-item, .upload-item {\r\n  width: calc(33.33% - 12px);\r\n  margin: 6px;\r\n  position: relative;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.image-item::before, .upload-item::before {\r\n  content: \"\";\r\n  display: block;\r\n  padding-top: 100%;\r\n}\r\n\r\n.preview-image {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.delete-btn {\r\n  position: absolute;\r\n  top: 6px;\r\n  right: 6px;\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  background-color: rgba(0, 0, 0, 0.6);\r\n  color: #FFF;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 16px;\r\n  z-index: 1;\r\n}\r\n\r\n.upload-item {\r\n  background-color: #F5F7FA;\r\n  border: 1px dashed #DDD;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.upload-icon {\r\n  font-size: 24px;\r\n  color: #999;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.upload-text {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n/* 表单项 */\r\n.form-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.form-label {\r\n  font-size: 15px;\r\n  color: #333;\r\n  margin-bottom: 10px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.label-tip {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-left: 4px;\r\n}\r\n\r\n.form-input-wrap {\r\n  background-color: #F5F7FA;\r\n  border-radius: 12px;\r\n  padding: 0 16px;\r\n  height: 48px;\r\n  display: flex;\r\n  align-items: center;\r\n  position: relative;\r\n}\r\n\r\n.form-input {\r\n  flex: 1;\r\n  height: 100%;\r\n  font-size: 15px;\r\n}\r\n\r\n.input-counter {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n/* 选择器 */\r\n.selector {\r\n  justify-content: space-between;\r\n}\r\n\r\n.selector-text {\r\n  font-size: 15px;\r\n  color: #333;\r\n}\r\n\r\n.placeholder {\r\n  color: #999;\r\n}\r\n\r\n.selector-arrow {\r\n  font-size: 18px;\r\n  color: #999;\r\n  transform: rotate(90deg);\r\n}\r\n\r\n/* 价格区域 */\r\n.price-row {\r\n  display: flex;\r\n  margin: 0 -8px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.price-item {\r\n  flex: 1;\r\n  padding: 0 8px;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.price-input {\r\n  position: relative;\r\n}\r\n\r\n.price-symbol {\r\n  font-size: 15px;\r\n  color: #333;\r\n  margin-right: 4px;\r\n}\r\n\r\n/* 库存控制 */\r\n.stock-input {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0;\r\n}\r\n\r\n.stock-btn {\r\n  width: 48px;\r\n  height: 48px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 20px;\r\n  color: #3A86FF;\r\n  background-color: rgba(58, 134, 255, 0.1);\r\n}\r\n\r\n.stock-btn.disabled {\r\n  color: #CCC;\r\n}\r\n\r\n.stock-value {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 15px;\r\n}\r\n\r\n/* 底部操作栏 */\r\n.bottom-action-bar {\r\n  display: flex;\r\n  padding: 16px;\r\n  background-color: #FFFFFF;\r\n  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.05);\r\n  position: sticky;\r\n  bottom: 0;\r\n  z-index: 10;\r\n}\r\n\r\n.action-button {\r\n  flex: 1;\r\n  height: 50px;\r\n  border-radius: 25px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 0 8px;\r\n  border: none;\r\n}\r\n\r\n.save-draft {\r\n  background-color: #E0E7FF;\r\n  color: #4F46E5;\r\n}\r\n\r\n.publish {\r\n  background: linear-gradient(135deg, #3A86FF, #5E60CE);\r\n  color: #FFFFFF;\r\n}\r\n\r\n/* 文本域样式 */\r\n.textarea-wrap {\r\n  height: auto;\r\n  padding: 12px 16px;\r\n  min-height: 120px;\r\n}\r\n\r\n.form-textarea {\r\n  width: 100%;\r\n  height: 100px;\r\n  font-size: 15px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.textarea-counter {\r\n  position: absolute;\r\n  bottom: 12px;\r\n  right: 16px;\r\n}\r\n\r\n/* 规格参数样式 */\r\n.specs-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.specs-title {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.add-spec-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  background-color: rgba(58, 134, 255, 0.1);\r\n  padding: 6px 12px;\r\n  border-radius: 16px;\r\n}\r\n\r\n.add-spec-icon {\r\n  font-size: 16px;\r\n  color: #3A86FF;\r\n  margin-right: 4px;\r\n}\r\n\r\n.add-spec-text {\r\n  font-size: 14px;\r\n  color: #3A86FF;\r\n}\r\n\r\n.specs-list {\r\n  background-color: #F5F7FA;\r\n  border-radius: 12px;\r\n  padding: 8px;\r\n}\r\n\r\n.spec-item {\r\n  display: flex;\r\n  align-items: center;\r\n  background-color: #FFFFFF;\r\n  border-radius: 8px;\r\n  padding: 8px;\r\n  margin-bottom: 8px;\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.spec-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.spec-inputs {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.spec-key {\r\n  width: 40%;\r\n  font-size: 14px;\r\n  padding: 6px 0;\r\n}\r\n\r\n.spec-separator {\r\n  margin: 0 8px;\r\n  color: #999;\r\n}\r\n\r\n.spec-value {\r\n  flex: 1;\r\n  font-size: 14px;\r\n  padding: 6px 0;\r\n}\r\n\r\n.delete-spec-btn {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  background-color: rgba(255, 59, 48, 0.1);\r\n  color: #FF3B30;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-left: 8px;\r\n}\r\n\r\n.empty-specs {\r\n  padding: 16px;\r\n  text-align: center;\r\n  color: #999;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 标签样式 */\r\n.tags-container {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: 0 -6px;\r\n}\r\n\r\n.tag-item {\r\n  margin: 6px;\r\n  padding: 8px 16px;\r\n  border-radius: 20px;\r\n  font-size: 14px;\r\n  background-color: #F5F7FA;\r\n  color: #666;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.tag-item:nth-child(1) {\r\n  background-color: rgba(52, 199, 89, 0.1);\r\n  color: #34C759;\r\n}\r\n\r\n.tag-item:nth-child(2) {\r\n  background-color: rgba(255, 149, 0, 0.1);\r\n  color: #FF9500;\r\n}\r\n\r\n.tag-item:nth-child(3) {\r\n  background-color: rgba(255, 45, 85, 0.1);\r\n  color: #FF2D55;\r\n}\r\n\r\n.tag-item:nth-child(4) {\r\n  background-color: rgba(175, 82, 222, 0.1);\r\n  color: #AF52DE;\r\n}\r\n\r\n.tag-item:nth-child(5) {\r\n  background-color: rgba(90, 200, 250, 0.1);\r\n  color: #5AC8FA;\r\n}\r\n\r\n.tag-item:nth-child(6) {\r\n  background-color: rgba(0, 122, 255, 0.1);\r\n  color: #007AFF;\r\n}\r\n\r\n.tag-item.active {\r\n  transform: scale(1.05);\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.tag-item.active:nth-child(1) {\r\n  background-color: #34C759;\r\n  color: #FFFFFF;\r\n}\r\n\r\n.tag-item.active:nth-child(2) {\r\n  background-color: #FF9500;\r\n  color: #FFFFFF;\r\n}\r\n\r\n.tag-item.active:nth-child(3) {\r\n  background-color: #FF2D55;\r\n  color: #FFFFFF;\r\n}\r\n\r\n.tag-item.active:nth-child(4) {\r\n  background-color: #AF52DE;\r\n  color: #FFFFFF;\r\n}\r\n\r\n.tag-item.active:nth-child(5) {\r\n  background-color: #5AC8FA;\r\n  color: #FFFFFF;\r\n}\r\n\r\n.tag-item.active:nth-child(6) {\r\n  background-color: #007AFF;\r\n  color: #FFFFFF;\r\n}\r\n\r\n/* 暗黑模式适配 */\r\n@media (prefers-color-scheme: dark) {\r\n  .product-publish-container {\r\n    background-color: #1C1C1E;\r\n  }\r\n  \r\n  .form-section {\r\n    background-color: #2C2C2E;\r\n  }\r\n  \r\n  .section-title {\r\n    color: #FFFFFF;\r\n  }\r\n  \r\n  .form-label {\r\n    color: #FFFFFF;\r\n  }\r\n  \r\n  .form-input-wrap {\r\n    background-color: #3A3A3C;\r\n  }\r\n  \r\n  .form-input, .form-textarea {\r\n    color: #FFFFFF;\r\n  }\r\n  \r\n  .upload-item {\r\n    background-color: #3A3A3C;\r\n    border-color: #666;\r\n  }\r\n  \r\n  .spec-item {\r\n    background-color: #3A3A3C;\r\n  }\r\n  \r\n  .specs-list {\r\n    background-color: #2C2C2E;\r\n  }\r\n  \r\n  .bottom-action-bar {\r\n    background-color: #2C2C2E;\r\n  }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin/pages/products/create.vue'\nwx.createPage(MiniProgramPage)"], "names": ["reactive", "uni", "MiniProgramPage"], "mappings": ";;;;;AAoQA,UAAM,cAAcA,cAAAA,SAAS;AAAA,MAC3B,MAAM;AAAA,MACN,UAAU;AAAA,MACV,OAAO;AAAA,MACP,eAAe;AAAA,MACf,OAAO;AAAA,MACP,QAAQ,CAAE;AAAA,MACV,aAAa;AAAA,MACb,OAAO,CAAE;AAAA,MACT,eAAe;AAAA,IACjB,CAAC;AAGD,UAAM,gBAAgBA,cAAAA,SAAS;AAAA,MAC7B,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,MACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,MACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,MACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,MACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,MACrB,EAAE,IAAI,GAAG,MAAM,OAAQ;AAAA,IACzB,CAAC;AAGD,UAAM,eAAeA,cAAAA,SAAS,CAAA,CAAE;AAGhC,UAAM,SAAS,MAAM;AACnBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAEA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,cAAc,MAAM;AACxBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO,IAAI,YAAY,OAAO;AAAA,QAC9B,UAAU,CAAC,YAAY,YAAY;AAAA,QACnC,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAChB,sBAAY,SAAS,CAAC,GAAG,YAAY,QAAQ,GAAG,IAAI,aAAa;AAAA,QAClE;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,cAAc,CAAC,UAAU;AAC7B,kBAAY,OAAO,OAAO,OAAO,CAAC;AAAA,IACpC;AAGA,UAAM,qBAAqB,MAAM;AAC/BA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QACzD,SAAS,CAAC,QAAQ;AAChB,gBAAM,aAAa,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAClE,sBAAY,WAAW,WAAW,IAAI,QAAQ;AAAA,QAC/C;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,MAAM;AAC1B,kBAAY;AAAA,IACd;AAEA,UAAM,gBAAgB,MAAM;AAC1B,UAAI,YAAY,QAAQ,GAAG;AACzB,oBAAY;AAAA,MACb;AAAA,IACH;AAEA,UAAM,YAAY,MAAM;AACtBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,UAAU,MAAM;AACpB,UAAI,YAAY,MAAM,UAAU,IAAI;AAClC,eAAOA,cAAAA,MAAI,UAAU;AAAA,UACnB,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AACD,kBAAY,MAAM,KAAK,EAAE,KAAK,IAAI,OAAO,GAAE,CAAE;AAAA,IAC/C;AAEA,UAAM,aAAa,CAAC,UAAU;AAC5B,kBAAY,MAAM,OAAO,OAAO,CAAC;AAAA,IACnC;AAGA,UAAM,YAAY,CAAC,UAAU;AAC3B,YAAM,QAAQ,aAAa,QAAQ,KAAK;AACxC,UAAI,QAAQ,IAAI;AACd,qBAAa,OAAO,OAAO,CAAC;AAAA,MAChC,OAAS;AACL,YAAI,aAAa,UAAU,GAAG;AAC5B,iBAAOA,cAAAA,MAAI,UAAU;AAAA,YACnB,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AACD,qBAAa,KAAK,KAAK;AAAA,MACxB;AAAA,IACH;AAEA,UAAM,iBAAiB,MAAM;AAE3B,UAAI,CAAC,YAAY,MAAM;AACrB,eAAOA,cAAAA,MAAI,UAAU,EAAE,OAAO,WAAW,MAAM,OAAM,CAAE;AAAA,MACxD;AACD,UAAI,CAAC,YAAY,UAAU;AACzB,eAAOA,cAAAA,MAAI,UAAU,EAAE,OAAO,WAAW,MAAM,OAAM,CAAE;AAAA,MACxD;AACD,UAAI,CAAC,YAAY,OAAO;AACtB,eAAOA,cAAAA,MAAI,UAAU,EAAE,OAAO,WAAW,MAAM,OAAM,CAAE;AAAA,MACxD;AACD,UAAI,YAAY,OAAO,WAAW,GAAG;AACnC,eAAOA,cAAAA,MAAI,UAAU,EAAE,OAAO,eAAe,MAAM,OAAM,CAAE;AAAA,MAC5D;AACD,UAAI,CAAC,YAAY,aAAa;AAC5B,eAAOA,cAAAA,MAAI,UAAU,EAAE,OAAO,WAAW,MAAM,OAAM,CAAE;AAAA,MACxD;AAGD,YAAM,cAAc;AAAA,QAClB,GAAG;AAAA,QACH,MAAM,aAAa,IAAI,QAAM;AAC3B,gBAAM,MAAM,cAAc,KAAK,OAAK,EAAE,OAAO,EAAE;AAC/C,iBAAO,MAAM,IAAI,OAAO;AAAA,QAC9B,CAAK,EAAE,OAAO,OAAO;AAAA,MACrB;AAEEA,oBAAY,MAAA,MAAA,OAAA,+DAAA,SAAS,WAAW;AAEhCA,oBAAAA,MAAI,YAAY,EAAE,OAAO,UAAW,CAAA;AAEpC,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,UAAS,CAAE;AAGhD,mBAAW,MAAM;AACfA,wBAAG,MAAC,aAAY;AAAA,QACjB,GAAE,IAAI;AAAA,MACR,GAAE,GAAI;AAAA,IACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5ZA,GAAG,WAAWC,SAAe;"}