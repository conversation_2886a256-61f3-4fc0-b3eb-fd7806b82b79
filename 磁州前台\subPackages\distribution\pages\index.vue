<template>
  <view class="distribution-center">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">我要赚钱</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-info">
        <image class="user-avatar" :src="userInfo.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
        <view class="user-details">
          <text class="user-name">{{userInfo.nickname || '游客'}}</text>
          <view class="user-level">
            <text class="level-tag">{{distributorInfo.level || '普通用户'}}</text>
            <text class="invite-code" v-if="distributorInfo.inviteCode">邀请码: {{distributorInfo.inviteCode}}</text>
          </view>
        </view>
      </view>
      <view class="user-status" v-if="!isDistributor">
        <button class="apply-btn" @click="navigateTo('/subPackages/distribution/pages/apply')">申请成为分销员</button>
      </view>
    </view>
    
    <!-- 数据概览卡片 -->
    <view class="stats-card" v-if="isDistributor">
      <view class="stats-header">
        <text class="stats-title">收益概览</text>
        <view class="stats-action" @click="navigateTo('/subPackages/distribution/pages/commission')">
          <text class="action-text">佣金明细</text>
          <view class="action-arrow"></view>
        </view>
      </view>
      
      <view class="stats-content">
        <view class="stats-item main">
          <text class="item-value">¥{{formatCommission(distributorInfo.totalCommission)}}</text>
          <text class="item-label">累计佣金</text>
        </view>
        
        <view class="stats-row">
          <view class="stats-item">
            <text class="item-value">¥{{formatCommission(distributorInfo.availableCommission)}}</text>
            <text class="item-label">可提现</text>
          </view>
          
          <view class="stats-item">
            <text class="item-value">¥{{formatCommission(distributorInfo.pendingCommission)}}</text>
            <text class="item-label">待结算</text>
          </view>
          
          <view class="stats-item">
            <text class="item-value">¥{{formatCommission(distributorInfo.withdrawnCommission)}}</text>
            <text class="item-label">已提现</text>
          </view>
        </view>
      </view>
      
      <view class="stats-footer">
        <button class="withdraw-btn" @click="navigateTo('/subPackages/distribution/pages/withdraw')">立即提现</button>
      </view>
    </view>
    
    <!-- 功能菜单 -->
    <view class="menu-section" v-if="isDistributor">
      <view class="menu-grid">
        <view class="menu-item" @click="navigateTo('/subPackages/distribution/pages/products')">
          <view class="menu-icon products"></view>
          <text class="menu-name">分销商品</text>
        </view>
        
        <view class="menu-item" @click="navigateTo('/subPackages/distribution/pages/team')">
          <view class="menu-icon team"></view>
          <text class="menu-name">我的团队</text>
        </view>
        
        <view class="menu-item" @click="navigateTo('/subPackages/distribution/pages/promotion')">
          <view class="menu-icon promotion"></view>
          <text class="menu-name">推广工具</text>
        </view>
        
        <view class="menu-item" @click="navigateTo('/subPackages/distribution/pages/commission')">
          <view class="menu-icon commission"></view>
          <text class="menu-name">佣金明细</text>
        </view>
      </view>
    </view>
    
    <!-- 推荐商品卡片 -->
    <view class="products-section" v-if="isDistributor">
      <view class="section-header">
        <text class="section-title">推荐分销商品</text>
        <view class="section-action" @click="navigateTo('/subPackages/distribution/pages/products')">
          <text class="action-text">查看全部</text>
          <view class="action-arrow"></view>
        </view>
      </view>
      
      <view class="products-list">
        <view class="product-card" v-for="(product, index) in recommendedProducts" :key="index" @click="navigateToProduct(product)">
          <image class="product-image" :src="product.image" mode="aspectFill"></image>
          <view class="product-info">
            <text class="product-name">{{product.name}}</text>
            <view class="product-price-row">
              <text class="product-price">¥{{product.price}}</text>
              <text class="commission-tag">赚¥{{product.commission}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 分销员申请引导 -->
    <view class="apply-guide" v-if="!isDistributor">
      <view class="guide-header">
        <text class="guide-title">成为分销员的好处</text>
      </view>
      
      <view class="guide-content">
        <view class="benefit-item">
          <view class="benefit-icon income"></view>
          <view class="benefit-info">
            <text class="benefit-title">额外收入</text>
            <text class="benefit-desc">分享商品赚取佣金，轻松获得额外收入</text>
          </view>
        </view>
        
        <view class="benefit-item">
          <view class="benefit-icon tools"></view>
          <view class="benefit-info">
            <text class="benefit-title">专业工具</text>
            <text class="benefit-desc">获得专业推广工具，提高转化率</text>
          </view>
        </view>
        
        <view class="benefit-item">
          <view class="benefit-icon team"></view>
          <view class="benefit-info">
            <text class="benefit-title">团队收益</text>
            <text class="benefit-desc">发展下级分销员，获得团队佣金</text>
          </view>
        </view>
      </view>
      
      <view class="guide-footer">
        <button class="apply-btn large" @click="navigateTo('/subPackages/distribution/pages/apply')">立即申请成为分销员</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import distributionService from '@/utils/distributionService';

// 用户信息
const userInfo = reactive({
  userId: '1001', // 模拟数据，实际应从用户系统获取
  nickname: '张三',
  avatar: '/static/images/default-avatar.png'
});

// 分销员信息
const distributorInfo = reactive({
  level: '普通分销员',
  totalCommission: 0,
  availableCommission: 0,
  pendingCommission: 0,
  withdrawnCommission: 0,
  inviteCode: ''
});

// 是否是分销员
const isDistributor = ref(false);

// 推荐商品
const recommendedProducts = ref([]);

// 页面加载
onMounted(async () => {
  // 检查是否是分销员
  await checkDistributorStatus();
  
  // 获取推荐商品
  if (isDistributor.value) {
    await getRecommendedProducts();
  }
});

// 检查分销员状态
const checkDistributorStatus = async () => {
  try {
    // 检查是否是分销员
    isDistributor.value = await distributionService.isDistributor({
      userId: userInfo.userId
    });
    
    // 如果是分销员，获取分销员信息
    if (isDistributor.value) {
      const info = await distributionService.getDistributorInfo({
        userId: userInfo.userId
      });
      
      if (info) {
        Object.assign(distributorInfo, info);
      }
    }
  } catch (error) {
    console.error('检查分销员状态失败', error);
    uni.showToast({
      title: '获取分销员信息失败',
      icon: 'none'
    });
  }
};

// 获取推荐商品
const getRecommendedProducts = async () => {
  try {
    const result = await distributionService.getDistributableProducts({
      page: 1,
      pageSize: 4,
      sortBy: 'commission'
    });
    
    recommendedProducts.value = result.list || [];
  } catch (error) {
    console.error('获取推荐商品失败', error);
  }
};

// 格式化佣金
const formatCommission = (amount) => {
  return distributionService.formatCommission(amount);
};

// 页面导航
const navigateTo = (url) => {
  uni.navigateTo({ url });
};

// 导航到商品详情
const navigateToProduct = (product) => {
  uni.navigateTo({
    url: `/pages/product/detail?id=${product.id}&isDistribution=true`
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 显示帮助
const showHelp = () => {
  uni.showModal({
    title: '我要赚钱帮助',
    content: '在我要赚钱，您可以查看佣金收益、管理团队、使用推广工具以及提现佣金等。',
    showCancel: false
  });
};
</script>

<style lang="scss">
.distribution-center {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 88rpx 32rpx 30rpx;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);
}

.navbar-back {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24rpx;
  height: 24rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.navbar-right {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

/* 用户卡片样式 */
.user-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 20rpx;
  border: 2rpx solid #f0f0f0;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.user-level {
  display: flex;
  align-items: center;
}

.level-tag {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
}

.invite-code {
  font-size: 24rpx;
  color: #666;
}

.user-status {
  margin-top: 20rpx;
}

.apply-btn {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  padding: 12rpx 30rpx;
  line-height: 1.5;
}

.apply-btn.large {
  font-size: 32rpx;
  padding: 20rpx 40rpx;
  width: 80%;
}

/* 数据概览卡片 */
.stats-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.stats-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.stats-action {
  display: flex;
  align-items: center;
}

.action-text {
  font-size: 26rpx;
  color: #666;
}

.action-arrow {
  width: 16rpx;
  height: 16rpx;
  border-right: 2rpx solid #666;
  border-top: 2rpx solid #666;
  transform: rotate(45deg);
  margin-left: 10rpx;
}

.stats-content {
  margin-bottom: 30rpx;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.stats-item.main {
  margin-bottom: 30rpx;
}

.stats-row {
  display: flex;
  justify-content: space-between;
}

.item-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.stats-item.main .item-value {
  font-size: 48rpx;
  color: #6B0FBE;
}

.item-label {
  font-size: 24rpx;
  color: #666;
}

.stats-footer {
  display: flex;
  justify-content: center;
}

.withdraw-btn {
  background: linear-gradient(135deg, #FF9500, #FF5722);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  padding: 12rpx 60rpx;
  line-height: 1.5;
}

/* 功能菜单 */
.menu-section {
  margin: 30rpx;
}

.menu-grid {
  display: flex;
  flex-wrap: wrap;
  background: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.menu-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 0;
}

.menu-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
  border-radius: 40rpx;
}

.menu-icon.products {
  background-color: #FF9500;
}

.menu-icon.team {
  background-color: #409EFF;
}

.menu-icon.promotion {
  background-color: #67C23A;
}

.menu-icon.commission {
  background-color: #E6A23C;
}

.menu-name {
  font-size: 26rpx;
  color: #333;
}

/* 推荐商品 */
.products-section {
  margin: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.section-action {
  display: flex;
  align-items: center;
}

.products-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.product-card {
  width: calc(50% - 20rpx);
  margin: 10rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.product-image {
  width: 100%;
  height: 300rpx;
  background-color: #f5f5f5;
}

.product-info {
  padding: 20rpx;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF5722;
}

.commission-tag {
  font-size: 24rpx;
  color: #6B0FBE;
  background-color: rgba(107, 15, 190, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

/* 分销员申请引导 */
.apply-guide {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.guide-header {
  margin-bottom: 30rpx;
}

.guide-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
}

.guide-content {
  margin-bottom: 30rpx;
}

.benefit-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.benefit-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  border-radius: 40rpx;
}

.benefit-icon.income {
  background-color: #FF9500;
}

.benefit-icon.tools {
  background-color: #67C23A;
}

.benefit-icon.team {
  background-color: #409EFF;
}

.benefit-info {
  flex: 1;
}

.benefit-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.benefit-desc {
  font-size: 24rpx;
  color: #666;
}

.guide-footer {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;
}
</style> 