{"version": 3, "file": "detail.js", "sources": ["subPackages/activity-showcase/pages/message/detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcbWVzc2FnZVxkZXRhaWwudnVl"], "sourcesContent": ["<!-- 消息详情页面开始 -->\r\n<template>\r\n  <view class=\"message-detail page-container\">\r\n    <!-- 消息头部 -->\r\n    <view class=\"message-header card\">\r\n      <view class=\"title\">{{ message.title }}</view>\r\n      <view class=\"meta\">\r\n        <text class=\"time\">{{ formatTime(message.createTime) }}</text>\r\n        <text class=\"type\">{{ message.type }}</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 消息内容 -->\r\n    <view class=\"message-content card\">\r\n      <rich-text :nodes=\"message.content\"></rich-text>\r\n      <view v-if=\"message.images?.length\" class=\"image-list\">\r\n        <image \r\n          v-for=\"(img, index) in message.images\" \r\n          :key=\"index\"\r\n          :src=\"img\"\r\n          mode=\"widthFix\"\r\n          @click=\"previewImage(index)\"\r\n        />\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 相关操作 -->\r\n    <view class=\"message-actions card\">\r\n      <button class=\"btn-primary\" @click=\"handleAction\">\r\n        {{ message.actionText || '查看详情' }}\r\n      </button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, onMounted } from 'vue'\r\nimport { formatTime } from '@/utils/date'\r\n\r\n// 消息数据\r\nconst message = ref({\r\n  title: '',\r\n  content: '',\r\n  type: '',\r\n  createTime: new Date(),\r\n  images: [],\r\n  actionText: ''\r\n})\r\n\r\n// 获取消息详情\r\nconst getMessageDetail = async (id: string) => {\r\n  try {\r\n    // TODO: 调用接口获取消息详情\r\n    message.value = {\r\n      title: '活动通知',\r\n      content: '您关注的活动即将开始...',\r\n      type: '系统通知',\r\n      createTime: new Date(),\r\n      images: [],\r\n      actionText: '立即查看'\r\n    }\r\n  } catch (error) {\r\n    uni.__f__('error','at subPackages/activity-showcase/pages/message/detail.vue:63','获取消息详情失败：', error)\r\n  }\r\n}\r\n\r\n// 图片预览\r\nconst previewImage = (index: number) => {\r\n  uni.previewImage({\r\n    current: index,\r\n    urls: message.value.images\r\n  })\r\n}\r\n\r\n// 处理操作按钮点击\r\nconst handleAction = () => {\r\n  // TODO: 根据消息类型处理不同操作\r\n  uni.showToast({\r\n    title: '操作成功',\r\n    icon: 'success'\r\n  })\r\n}\r\n\r\nonMounted(() => {\r\n  const id = ''  // TODO: 从路由参数获取消息ID\r\n  getMessageDetail(id)\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.message-detail {\r\n  .message-header {\r\n    .title {\r\n      font-size: 36rpx;\r\n      font-weight: 600;\r\n      color: #333;\r\n      margin-bottom: 16rpx;\r\n    }\r\n    \r\n    .meta {\r\n      display: flex;\r\n      align-items: center;\r\n      font-size: 24rpx;\r\n      color: #999;\r\n      \r\n      .time {\r\n        margin-right: 16rpx;\r\n      }\r\n      \r\n      .type {\r\n        background: #f0f0f0;\r\n        padding: 4rpx 12rpx;\r\n        border-radius: 4rpx;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .message-content {\r\n    margin-top: 24rpx;\r\n    \r\n    :deep(rich-text) {\r\n      font-size: 28rpx;\r\n      line-height: 1.6;\r\n      color: #666;\r\n    }\r\n    \r\n    .image-list {\r\n      margin-top: 24rpx;\r\n      \r\n      image {\r\n        width: 100%;\r\n        border-radius: 8rpx;\r\n        margin-bottom: 16rpx;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .message-actions {\r\n    margin-top: 24rpx;\r\n    display: flex;\r\n    justify-content: center;\r\n  }\r\n}\r\n</style>\r\n<!-- 消息详情页面结束 --> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/message/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "onMounted"], "mappings": ";;;;;;AAwCA,UAAM,UAAUA,cAAAA,IAAI;AAAA,MAClB,OAAO;AAAA,MACP,SAAS;AAAA,MACT,MAAM;AAAA,MACN,gCAAgB,KAAK;AAAA,MACrB,QAAQ,CAAC;AAAA,MACT,YAAY;AAAA,IAAA,CACb;AAGK,UAAA,mBAAmB,OAAO,OAAe;AACzC,UAAA;AAEF,gBAAQ,QAAQ;AAAA,UACd,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,UACN,gCAAgB,KAAK;AAAA,UACrB,QAAQ,CAAC;AAAA,UACT,YAAY;AAAA,QAAA;AAAA,eAEP,OAAO;AACdC,sBAAA,MAAI,MAAM,SAAQ,gEAA+D,aAAa,KAAK;AAAA,MACrG;AAAA,IAAA;AAII,UAAA,eAAe,CAAC,UAAkB;AACtCA,oBAAAA,MAAI,aAAa;AAAA,QACf,SAAS;AAAA,QACT,MAAM,QAAQ,MAAM;AAAA,MAAA,CACrB;AAAA,IAAA;AAIH,UAAM,eAAe,MAAM;AAEzBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAAA,IAAA;AAGHC,kBAAAA,UAAU,MAAM;AAEd,uBAAmB;AAAA,IAAA,CACpB;;;;;;;;;;;;;;;;;;;;;;;;;ACrFD,GAAG,WAAW,eAAe;"}