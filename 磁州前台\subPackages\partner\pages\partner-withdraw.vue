<template>
  <view class="withdraw-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/最新返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">收益提现</view>
      <view class="navbar-right">
        <!-- 预留位置与发布页面保持一致 -->
      </view>
    </view>
    
    <!-- 添加顶部安全区域 -->
    <view class="safe-area-top"></view>
    
    <!-- 账户余额信息 -->
    <view class="balance-card">
      <view class="balance-title">可提现余额</view>
      <view class="balance-amount">¥ {{availableBalance}}</view>
      <view class="balance-detail">
        <view class="detail-item">
          <text>总收益</text>
          <text>¥ {{totalIncome}}</text>
        </view>
        <view class="detail-item">
          <text>已提现</text>
          <text>¥ {{withdrawedAmount}}</text>
        </view>
        <view class="detail-item">
          <text>冻结中</text>
          <text>¥ {{freezeAmount}}</text>
        </view>
      </view>
    </view>

    <!-- 提现表单 -->
    <view class="withdraw-form">
      <view class="form-title">提现金额</view>
      <view class="amount-input">
        <text class="currency">¥</text>
        <input type="digit" v-model="amount" placeholder="请输入提现金额" />
      </view>
      <view class="amount-tips">
        <text>提现金额 ≥ 1元，提现手续费率{{feeRate}}%</text>
        <text class="all-btn" @tap="setMaxAmount">全部提现</text>
      </view>

      <!-- 实际到账金额 -->
      <view class="actual-amount" v-if="amount > 0">
        <text>实际到账：</text>
        <text class="amount">¥ {{actualAmount}}</text>
      </view>

      <!-- 提现方式选择 -->
      <view class="withdraw-method">
        <view class="method-title">提现方式</view>
        <view class="method-list">
          <view 
            class="method-item" 
            :class="{'active': selectedMethod === 'wechat'}" 
            @tap="selectMethod('wechat')"
          >
            <image src="/static/images/wechat-pay.png" mode="aspectFit"></image>
            <text>微信</text>
            <view class="check-icon" v-if="selectedMethod === 'wechat'">✓</view>
          </view>
          <view 
            class="method-item" 
            :class="{'active': selectedMethod === 'alipay'}" 
            @tap="selectMethod('alipay')"
          >
            <image src="/static/images/alipay.png" mode="aspectFit"></image>
            <text>支付宝</text>
            <view class="check-icon" v-if="selectedMethod === 'alipay'">✓</view>
          </view>
          <view 
            class="method-item" 
            :class="{'active': selectedMethod === 'bank'}" 
            @tap="selectMethod('bank')"
          >
            <image src="/static/images/bank-card.png" mode="aspectFit"></image>
            <text>银行卡</text>
            <view class="check-icon" v-if="selectedMethod === 'bank'">✓</view>
          </view>
        </view>
      </view>

      <!-- 支付宝账号输入 -->
      <view class="account-input" v-if="selectedMethod === 'alipay'">
        <view class="input-item">
          <text class="label">支付宝账号</text>
          <input type="text" v-model="alipayAccount" placeholder="请输入支付宝账号" />
        </view>
        <view class="input-item">
          <text class="label">真实姓名</text>
          <input type="text" v-model="alipayName" placeholder="请输入真实姓名" />
        </view>
      </view>

      <!-- 银行卡信息输入 -->
      <view class="account-input" v-if="selectedMethod === 'bank'">
        <view class="input-item">
          <text class="label">开户银行</text>
          <picker @change="bankChange" :value="bankIndex" :range="bankList">
            <view class="picker">{{bankList[bankIndex] || '请选择开户银行'}}</view>
          </picker>
        </view>
        <view class="input-item">
          <text class="label">银行卡号</text>
          <input type="number" v-model="bankCard" placeholder="请输入银行卡号" />
        </view>
        <view class="input-item">
          <text class="label">开户人</text>
          <input type="text" v-model="bankOwner" placeholder="请输入开户人姓名" />
        </view>
      </view>

      <!-- 提交按钮 -->
      <button 
        class="withdraw-btn" 
        :disabled="!canWithdraw" 
        :class="{'disabled': !canWithdraw}"
        @tap="submitWithdraw"
      >
        确认提现
      </button>
    </view>

    <!-- 提现规则 -->
    <view class="withdraw-rules">
      <view class="rules-title">提现规则</view>
      <view class="rules-content">
        <view class="rule-item">1. 提现金额需≥1元，单笔提现上限为50000元</view>
        <view class="rule-item">2. 提现手续费率为{{feeRate}}%，从提现金额中扣除</view>
        <view class="rule-item">3. 提现申请提交后，平台将在1-3个工作日内审核</view>
        <view class="rule-item">4. 审核通过后，资金将在24小时内到账</view>
        <view class="rule-item">5. 请确保提供的账户信息准确无误，因信息错误导致的提现失败责任自负</view>
        <view class="rule-item">6. 如遇节假日，处理时间可能顺延</view>
        <view class="rule-item">7. 如有疑问，请联系客服</view>
      </view>
    </view>

    <!-- 提现记录 -->
    <view class="withdraw-history">
      <view class="history-title">最近提现记录</view>
      <view class="history-list" v-if="historyList.length > 0">
        <view class="history-item" v-for="(item, index) in historyList" :key="index">
          <view class="history-left">
            <view class="history-amount">¥ {{item.amount}}</view>
            <view class="history-time">{{item.time}}</view>
          </view>
          <view class="history-right">
            <view class="history-status" :class="'status-'+item.status">
              {{statusText[item.status]}}
            </view>
          </view>
        </view>
      </view>
      <view class="no-record" v-else>
        <image src="/static/images/no-record.png" mode="aspectFit"></image>
        <text>暂无提现记录</text>
      </view>
      <view class="view-more" @tap="navigateToWithdrawHistory" v-if="historyList.length > 0">
        查看更多记录
      </view>
    </view>

    <!-- 提现确认弹窗 -->
    <view class="withdraw-modal" v-if="showConfirmModal">
      <view class="modal-mask" @tap="cancelWithdraw"></view>
      <view class="modal-content">
        <view class="modal-title">确认提现</view>
        <view class="modal-info">
          <view class="info-item">
            <text class="label">提现金额</text>
            <text class="value">¥ {{amount}}</text>
          </view>
          <view class="info-item">
            <text class="label">手续费</text>
            <text class="value">¥ {{fee}}</text>
          </view>
          <view class="info-item highlight">
            <text class="label">实际到账</text>
            <text class="value">¥ {{actualAmount}}</text>
          </view>
          <view class="info-item">
            <text class="label">提现方式</text>
            <text class="value">{{methodText[selectedMethod]}}</text>
          </view>
          <view class="info-item" v-if="selectedMethod === 'alipay'">
            <text class="label">支付宝账号</text>
            <text class="value">{{alipayAccount}}</text>
          </view>
          <view class="info-item" v-if="selectedMethod === 'bank'">
            <text class="label">银行信息</text>
            <text class="value">{{bankList[bankIndex]}} ({{bankCard}})</text>
          </view>
        </view>
        <view class="modal-btns">
          <button class="cancel-btn" @tap="cancelWithdraw">取消</button>
          <button class="confirm-btn" @tap="confirmWithdraw">确认</button>
        </view>
      </view>
    </view>

    <!-- 提现成功弹窗 -->
    <view class="withdraw-modal" v-if="showSuccessModal">
      <view class="modal-mask"></view>
      <view class="modal-content success-modal">
        <image src="/static/images/success-icon.png" mode="aspectFit" class="success-icon"></image>
        <view class="success-title">提现申请已提交</view>
        <view class="success-desc">平台将在1-3个工作日内审核，请留意提现记录</view>
        <button class="success-btn" @tap="closeSuccessModal">我知道了</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';

// 响应式状态
const availableBalance = ref(2358.65); // 可提现余额
const totalIncome = ref(3500.00); // 总收益
const withdrawedAmount = ref(1000.00); // 已提现金额
const freezeAmount = ref(141.35); // 冻结金额
const amount = ref(''); // 提现金额
const feeRate = ref(0.6); // 手续费率
const selectedMethod = ref('wechat'); // 选中的提现方式
const alipayAccount = ref(''); // 支付宝账号
const alipayName = ref(''); // 支付宝实名
const bankIndex = ref(0); // 选中的银行索引
const bankList = [
  '中国工商银行', '中国农业银行', '中国建设银行', '中国银行', 
  '交通银行', '招商银行', '中信银行', '浦发银行', 
  '民生银行', '华夏银行', '兴业银行', '平安银行'
];
const bankCard = ref(''); // 银行卡号
const bankOwner = ref(''); // 开户人
const showConfirmModal = ref(false); // 是否显示确认弹窗
const showSuccessModal = ref(false); // 是否显示成功弹窗

const historyList = ref([
  { amount: 500.00, time: '2023-12-15 14:30:25', status: 'success' },
  { amount: 300.00, time: '2023-11-20 10:15:36', status: 'pending' },
  { amount: 200.00, time: '2023-10-05 16:45:12', status: 'success' }
]);

const statusText = {
  'success': '已到账',
  'pending': '审核中',
  'processing': '处理中',
  'failed': '提现失败'
};

const methodText = {
  'wechat': '微信',
  'alipay': '支付宝',
  'bank': '银行卡'
};

// 计算属性
// 计算实际到账金额
const actualAmount = computed(() => {
  if (!amount.value || isNaN(amount.value)) return 0;
  let fee = amount.value * (feeRate.value / 100);
  return (amount.value - fee).toFixed(2);
});

// 计算手续费
const fee = computed(() => {
  if (!amount.value || isNaN(amount.value)) return 0;
  return (amount.value * (feeRate.value / 100)).toFixed(2);
});

// 是否可以提现
const canWithdraw = computed(() => {
  if (!amount.value || isNaN(amount.value) || amount.value <= 0) return false;
  if (amount.value > availableBalance.value) return false;
  if (amount.value < 1) return false;
  
  if (selectedMethod.value === 'alipay' && (!alipayAccount.value || !alipayName.value)) return false;
  if (selectedMethod.value === 'bank' && (!bankCard.value || !bankOwner.value)) return false;
  
  return true;
});

// 方法
// 设置最大金额
const setMaxAmount = () => {
  amount.value = availableBalance.value.toString();
};

// 选择提现方式
const selectMethod = (method) => {
  selectedMethod.value = method;
};

// 银行选择变化
const bankChange = (e) => {
  bankIndex.value = e.detail.value;
};

// 提交提现
const submitWithdraw = () => {
  if (!canWithdraw.value) return;
  showConfirmModal.value = true;
};

// 取消提现
const cancelWithdraw = () => {
  showConfirmModal.value = false;
};

// 确认提现
const confirmWithdraw = () => {
  // 这里调用提现API
  uni.showLoading({
    title: '提交中...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    showConfirmModal.value = false;
    showSuccessModal.value = true;
    
    // 模拟提现成功后更新余额
    availableBalance.value -= Number(amount.value);
    withdrawedAmount.value += Number(amount.value);
    
    // 添加提现记录
    historyList.value.unshift({
      amount: Number(amount.value),
      time: formatDate(new Date()),
      status: 'pending'
    });
    
    // 重置表单
    amount.value = '';
    alipayAccount.value = '';
    alipayName.value = '';
    bankCard.value = '';
    bankOwner.value = '';
  }, 1500);
};

// 关闭成功弹窗
const closeSuccessModal = () => {
  showSuccessModal.value = false;
};

// 跳转到提现记录页面
const navigateToWithdrawHistory = () => {
  uni.navigateTo({
    url: '/pages/my/partner-withdraw-history'
  });
};

// 格式化日期
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hour = date.getHours().toString().padStart(2, '0');
  const minute = date.getMinutes().toString().padStart(2, '0');
  const second = date.getSeconds().toString().padStart(2, '0');
  
  return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.withdraw-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
  padding-top: calc(44px + var(--status-bar-height));
}

.custom-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  padding: 0 30rpx;
  padding-top: 44px; /* 状态栏高度 */
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  background-image: linear-gradient(135deg, #0066FF, #0052CC); /* 改为与发布页一致的渐变角度 */
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.15);
  z-index: 100; /* 提高z-index确保在最上层 */
}

.navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 20; /* 确保在标题上层，可以被点击 */
}

.back-icon {
  width: 100%;
  height: 100%;
}

.navbar-title {
  position: absolute;
  left: 0;
  right: 0;
  font-size: 36rpx;
  font-weight: 700;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  text-align: center;
}

.navbar-right {
  width: 40rpx;
  height: 40rpx;
}

.safe-area-top {
  height: var(--status-bar-height);
  width: 100%;
  background-image: linear-gradient(135deg, #0066FF, #0052CC);
}

.balance-card {
  margin: 30rpx;
  background: linear-gradient(135deg, #1677FF, #0E5FD8);
  border-radius: 16rpx;
  padding: 40rpx;
  color: #ffffff;
  box-shadow: 0 10rpx 20rpx rgba(22, 119, 255, 0.2);
}

.balance-title {
  font-size: 28rpx;
  opacity: 0.9;
}

.balance-amount {
  font-size: 60rpx;
  font-weight: bold;
  margin: 20rpx 0 30rpx;
}

.balance-detail {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 20rpx;
}

.detail-item {
  display: flex;
  flex-direction: column;
  font-size: 24rpx;
  
  text:first-child {
    opacity: 0.8;
    margin-bottom: 8rpx;
  }
  
  text:last-child {
    font-size: 28rpx;
    font-weight: 500;
  }
}

.withdraw-form {
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.form-title, .method-title {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
  color: #333333;
}

.amount-input {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #eeeeee;
  padding-bottom: 20rpx;
  margin-bottom: 10rpx;
}

.currency {
  font-size: 50rpx;
  font-weight: bold;
  margin-right: 10rpx;
  color: #333333;
}

.amount-input input {
  font-size: 50rpx;
  flex: 1;
}

.amount-tips {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 30rpx;
}

.all-btn {
  color: #1677FF;
}

.actual-amount {
  background-color: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
  display: flex;
  justify-content: space-between;
  font-size: 28rpx;
  margin-bottom: 30rpx;
}

.actual-amount .amount {
  color: #1677FF;
  font-weight: 500;
}

.withdraw-method {
  margin-top: 30rpx;
}

.method-list {
  display: flex;
  justify-content: space-between;
}

.method-item {
  width: 30%;
  height: 150rpx;
  border: 1px solid #eeeeee;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  
  image {
    width: 60rpx;
    height: 60rpx;
    margin-bottom: 10rpx;
  }
  
  text {
    font-size: 26rpx;
    color: #666666;
  }
  
  &.active {
    border-color: #1677FF;
    background-color: rgba(22, 119, 255, 0.05);
  }
  
  .check-icon {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 40rpx;
    height: 40rpx;
    background-color: #1677FF;
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24rpx;
    border-top-left-radius: 12rpx;
  }
}

.account-input {
  margin-top: 30rpx;
}

.input-item {
  margin-bottom: 20rpx;
  
  .label {
    font-size: 28rpx;
    color: #666666;
    margin-bottom: 10rpx;
    display: block;
  }
  
  input, .picker {
    height: 80rpx;
    background-color: #f8f8f8;
    border-radius: 8rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    width: 100%;
    box-sizing: border-box;
  }
  
  .picker {
    display: flex;
    align-items: center;
    color: #333333;
  }
}

.withdraw-btn {
  background: linear-gradient(135deg, #1677FF, #0E5FD8);
  color: #ffffff;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  margin-top: 40rpx;
  font-weight: 500;
  box-shadow: 0 10rpx 20rpx rgba(22, 119, 255, 0.2);
  
  &.disabled {
    background: linear-gradient(135deg, #cccccc, #999999);
    box-shadow: none;
  }
}

.withdraw-rules {
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.rules-title {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
  color: #333333;
}

.rules-content {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.8;
}

.rule-item {
  margin-bottom: 10rpx;
}

.withdraw-history {
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.history-title {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
  color: #333333;
}

.history-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.history-left {
  .history-amount {
    font-size: 30rpx;
    font-weight: 500;
    color: #333333;
    margin-bottom: 8rpx;
  }
  
  .history-time {
    font-size: 24rpx;
    color: #999999;
  }
}

.history-status {
  font-size: 26rpx;
  
  &.status-success {
    color: #07c160;
  }
  
  &.status-pending, &.status-processing {
    color: #1677FF;
  }
  
  &.status-failed {
    color: #ff4d4f;
  }
}

.no-record {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
  
  image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 20rpx;
  }
  
  text {
    font-size: 28rpx;
    color: #999999;
  }
}

.view-more {
  text-align: center;
  font-size: 28rpx;
  color: #1677FF;
  margin-top: 20rpx;
}

.withdraw-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
  
  .modal-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
  }
  
  .modal-content {
    width: 600rpx;
    background-color: #ffffff;
    border-radius: 16rpx;
    padding: 40rpx;
    position: relative;
    z-index: 1000;
  }
  
  .modal-title {
    font-size: 32rpx;
    font-weight: 500;
    text-align: center;
    margin-bottom: 30rpx;
  }
  
  .modal-info {
    margin-bottom: 30rpx;
    
    .info-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15rpx;
      font-size: 28rpx;
      
      .label {
        color: #666666;
      }
      
      .value {
        color: #333333;
        font-weight: 500;
      }
      
      &.highlight {
        .label, .value {
          color: #1677FF;
          font-weight: bold;
        }
      }
    }
  }
  
  .modal-btns {
    display: flex;
    
    button {
      flex: 1;
      height: 80rpx;
      font-size: 30rpx;
      border-radius: 40rpx;
      margin: 0 10rpx;
    }
    
    .cancel-btn {
      background-color: #f5f5f5;
      color: #666666;
    }
    
    .confirm-btn {
      background: linear-gradient(135deg, #1677FF, #0E5FD8);
      color: #ffffff;
      font-weight: 500;
    }
  }
  
  .success-modal {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 50rpx 40rpx;
  }
  
  .success-icon {
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 20rpx;
  }
  
  .success-title {
    font-size: 32rpx;
    font-weight: 500;
    margin-bottom: 10rpx;
  }
  
  .success-desc {
    font-size: 26rpx;
    color: #666666;
    text-align: center;
    margin-bottom: 30rpx;
  }
  
  .success-btn {
    width: 100%;
    height: 80rpx;
    background: linear-gradient(135deg, #1677FF, #0E5FD8);
    color: #ffffff;
    font-size: 30rpx;
    border-radius: 40rpx;
    font-weight: 500;
  }
}
</style>