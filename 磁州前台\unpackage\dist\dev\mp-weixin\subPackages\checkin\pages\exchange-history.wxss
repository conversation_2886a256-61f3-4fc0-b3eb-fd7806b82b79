/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.page-root {
  position: relative;
  min-height: 100vh;
  background: #f6faff;
}
.nav-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #1677FF;
  z-index: 100;
  width: 100%;
}
.navbar-content {
  position: fixed;
  left: 0;
  right: 0;
  z-index: 101;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: transparent;
  width: 100%;
}
.navbar-left, .navbar-right {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 24px;
  height: 24px;
  display: block;
  background: none;
  border-radius: 0;
  margin: 0 auto;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  line-height: 44px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.exchange-history-container {
  min-height: 100vh;
  position: relative;
  box-sizing: border-box;
  background: #f6faff;
}
.record-list {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.record-item {
  max-width: 300px;
  width: 100%;
  margin: 0 auto 16px auto;
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 6px 24px rgba(22, 119, 255, 0.12), 0 1.5px 6px rgba(0, 0, 0, 0.06);
  padding: 18px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  word-break: break-all;
  transition: box-shadow 0.3s;
}
.record-item:active, .record-item:focus, .record-item:hover {
  box-shadow: 0 12px 32px rgba(22, 119, 255, 0.18), 0 3px 12px rgba(0, 0, 0, 0.1);
}
.record-main {
  display: flex;
  align-items: center;
  flex: 1;
}
.record-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  object-fit: cover;
  background-color: #f5f5f5;
  margin-right: 20rpx;
}
.record-info {
  display: flex;
  flex-direction: column;
}
.record-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
}
.record-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}
.record-status {
  display: inline-block;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
}
.status-checking {
  background-color: rgba(153, 153, 153, 0.1);
  color: #999;
}
.status-shipping {
  background-color: rgba(83, 166, 255, 0.1);
  color: #3a86ff;
}
.status-shipped {
  background-color: rgba(255, 102, 0, 0.1);
  color: #ff6600;
}
.status-using {
  background-color: rgba(0, 191, 131, 0.1);
  color: #00bf83;
}
.status-used {
  background-color: rgba(153, 153, 153, 0.1);
  color: #999;
}
.status-expired {
  background-color: rgba(153, 153, 153, 0.1);
  color: #999;
}
.record-points {
  font-size: 28rpx;
  color: #ff6b6b;
  font-weight: 500;
}
.empty-state {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}
.detail-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}
.popup-content {
  width: 80%;
  max-width: 600rpx;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.15);
}
.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1px solid #f1f1f1;
}
.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.popup-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.popup-body {
  padding: 30rpx;
}
.detail-item {
  display: flex;
  align-items: center;
  padding-bottom: 30rpx;
  margin-bottom: 30rpx;
  border-bottom: 1px solid #f5f5f5;
}
.detail-image {
  width: 150rpx;
  height: 150rpx;
  border-radius: 12rpx;
  object-fit: cover;
  margin-right: 20rpx;
}
.detail-info {
  flex: 1;
}
.detail-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
}
.detail-points {
  font-size: 28rpx;
  color: #ff6b6b;
  font-weight: 500;
}
.detail-grid {
  display: flex;
  flex-wrap: wrap;
}
.grid-item {
  width: 100%;
  margin-bottom: 24rpx;
}
.grid-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
}
.grid-value {
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
}
.grid-value.status {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
}
.detail-actions {
  margin-top: 40rpx;
  display: flex;
  justify-content: center;
}
.action-btn {
  width: 80%;
  height: 80rpx;
  line-height: 80rpx;
  background: linear-gradient(135deg, #3a86ff, #1a56cc);
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: 500;
  border-radius: 40rpx;
  box-shadow: 0 10rpx 20rpx rgba(58, 134, 255, 0.2);
}
.confirm-btn {
  background: linear-gradient(135deg, #ff6600, #ff8533);
  box-shadow: 0 10rpx 20rpx rgba(255, 102, 0, 0.2);
}
.filter-tabs {
  margin: calc(var(--status-bar-height, 0) + 88rpx + 20rpx) 30rpx 30rpx;
  display: flex;
  background-color: #eaf3ff;
  border-radius: 16rpx;
  overflow: hidden;
  padding: 6rpx;
}
.tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s;
  border-radius: 12rpx;
}
.tab-item.active {
  background-color: #e6f0ff;
  color: #1677FF;
  font-weight: 600;
  box-shadow: 0 2rpx 8rpx rgba(22, 119, 255, 0.08);
  border: 1.5px solid #1677FF;
}
.tab-item:not(.active):hover {
  background: #f0f7ff;
}