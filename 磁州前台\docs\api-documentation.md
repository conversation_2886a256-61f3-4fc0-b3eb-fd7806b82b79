# 磁州生活网API接口文档

## 1. 用户认证模块

### 1.1 用户登录
- **接口地址**: `POST /api/auth/login`
- **请求参数**:
  ```json
  {
    "phone": "手机号",
    "code": "验证码"
  }
  ```
- **响应数据**:
  ```json
  {
    "code": 200,
    "data": {
      "token": "JWT令牌",
      "userInfo": {
        "id": "用户ID",
        "nickname": "昵称",
        "avatar": "头像"
      }
    }
  }
  ```

## 2. 推广工具模块

### 2.1 生成推广海报
- **接口地址**: `POST /api/promotion/poster`
- **请求参数**:
  ```json
  {
    "type": "business|product|carpool",
    "id": "内容ID",
    "template": "模板ID"
  }
  ```

// ... 更多接口定义