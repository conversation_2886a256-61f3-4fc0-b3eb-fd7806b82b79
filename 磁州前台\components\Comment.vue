// 在import区域添加内容审核工具
import { checkText } from '../utils/contentCheck.js';

// 修改提交评论方法
const submitComment = async () => {
  if (!commentContent.value.trim()) {
    ElMessage.warning('请输入评论内容');
    return;
  }

  try {
    submitting.value = true;
    
    // 内容审核
    const checkResult = await checkText(commentContent.value);
    
    // 审核未通过
    if (!checkResult.pass) {
      ElMessage.error(`评论审核失败：${checkResult.message}`);
      submitting.value = false;
      return;
    }
    
    // 审核通过，提交评论
    await axios.post('/api/comments', {
      content: commentContent.value,
      targetId: props.targetId,
      targetType: props.targetType
    });
    
    ElMessage.success('评论发表成功');
    commentContent.value = '';
    fetchComments();
  } catch (error) {
    ElMessage.error('评论发表失败');
    console.error('评论发表失败:', error);
  } finally {
    submitting.value = false;
  }
};

// 修改提交回复方法
const submitReply = async (comment) => {
  if (!replyContent.value.trim()) {
    ElMessage.warning('请输入回复内容');
    return;
  }

  try {
    submitting.value = true;
    
    // 内容审核
    const checkResult = await checkText(replyContent.value);
    
    // 审核未通过
    if (!checkResult.pass) {
      ElMessage.error(`回复审核失败：${checkResult.message}`);
      submitting.value = false;
      return;
    }
    
    // 审核通过，提交回复
    await axios.post('/api/comments', {
      content: replyContent.value,
      targetId: props.targetId,
      targetType: props.targetType,
      parentId: comment.id
    });
    
    ElMessage.success('回复发表成功');
    cancelReply();
    fetchComments();
  } catch (error) {
    ElMessage.error('回复发表失败');
    console.error('回复发表失败:', error);
  } finally {
    submitting.value = false;
  }
}; 