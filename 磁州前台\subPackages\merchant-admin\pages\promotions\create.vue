<template>
  <view class="create-promotion-page">
    <!-- 顶部导航栏 -->
    <view class="navbar">
      <view class="navbar-left">
        <view class="back-button" @tap="goBack">
          <svg class="back-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 18L9 12L15 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </view>
      </view>
      <view class="navbar-title">
        <text class="title-main">创建活动</text>
      </view>
      <view class="navbar-right">
        <view class="help-button" @tap="showHelp">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M9.08984 9.00008C9.32495 8.33175 9.789 7.76819 10.3998 7.40921C11.0106 7.05024 11.7287 6.91902 12.427 7.03879C13.1253 7.15857 13.7587 7.52161 14.2149 8.06361C14.6712 8.60561 14.9209 9.2916 14.9198 10.0001C14.9198 12.0001 11.9198 13.0001 11.9198 13.0001" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M12 17H12.01" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </view>
      </view>
    </view>

    <!-- 内容滚动区域 -->
    <scroll-view scroll-y class="content-container">
      <!-- 表单分组：基本信息 -->
      <view class="form-group">
        <view class="form-group-header">
          <text class="form-group-title">活动基本信息</text>
          <text class="form-group-subtitle">请填写活动的基本信息</text>
        </view>

        <!-- 活动名称 -->
        <view class="form-item">
          <text class="form-label">活动名称</text>
          <view class="form-input-wrapper">
            <input 
              class="form-input" 
              type="text" 
              v-model="promotionForm.title" 
              placeholder="请输入活动名称"
              maxlength="30" 
            />
            <text class="input-counter">{{promotionForm.title.length}}/30</text>
          </view>
        </view>

        <!-- 活动类型 -->
        <view class="form-item">
          <text class="form-label">活动类型</text>
          <view class="form-input-wrapper selector" @tap="showTypeSelector">
            <text class="selector-value" :class="{ 'placeholder': !promotionForm.type }">
              {{ promotionForm.type || '请选择活动类型' }}
            </text>
            <svg class="selector-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 18L15 12L9 6" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
        </view>

        <!-- 活动时间 -->
        <view class="form-item">
          <text class="form-label">活动时间</text>
          <view class="date-range">
            <view class="date-picker" @tap="showDatePicker('start')">
              <text class="date-text" :class="{ 'placeholder': !promotionForm.startDate }">
                {{ promotionForm.startDate || '开始日期' }}
              </text>
            </view>
            <text class="date-separator">至</text>
            <view class="date-picker" @tap="showDatePicker('end')">
              <text class="date-text" :class="{ 'placeholder': !promotionForm.endDate }">
                {{ promotionForm.endDate || '结束日期' }}
              </text>
            </view>
          </view>
        </view>

        <!-- 活动封面 -->
        <view class="form-item">
          <text class="form-label">活动封面</text>
          <view class="upload-container">
            <view class="upload-preview" v-if="promotionForm.banner">
              <image class="preview-image" :src="promotionForm.banner" mode="aspectFill"></image>
              <view class="image-delete" @tap="removeBanner">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M18 6L6 18M6 6L18 18" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </view>
            </view>
            <view class="upload-button" @tap="chooseBanner" v-else>
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M21 12V17C21 18.1046 20.1046 19 19 19H5C3.89543 19 3 18.1046 3 17V12M21 12V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V12M21 12L15.4829 8.30824C14.5652 7.68381 13.4348 7.68381 12.5171 8.30824L6.99999 12M16 16H16.01M16 11H16.01M9 19V19.01" stroke="#FF9F0A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <text class="upload-text">上传活动封面</text>
              <text class="upload-tip">建议尺寸：800x400px，JPG/PNG格式</text>
            </view>
          </view>
        </view>

        <!-- 活动描述 -->
        <view class="form-item">
          <text class="form-label">活动描述</text>
          <view class="form-input-wrapper textarea">
            <textarea 
              class="form-textarea" 
              v-model="promotionForm.description"
              placeholder="请输入活动描述，介绍活动内容、优惠力度等"
              maxlength="200"
              auto-height
            ></textarea>
            <text class="textarea-counter">{{promotionForm.description.length}}/200</text>
          </view>
        </view>
      </view>

      <!-- 表单分组：优惠设置 -->
      <view class="form-group">
        <view class="form-group-header">
          <text class="form-group-title">优惠设置</text>
          <text class="form-group-subtitle">请设置活动的优惠规则</text>
        </view>

        <!-- 优惠类型 -->
        <view class="form-item">
          <text class="form-label">优惠类型</text>
          <view class="discount-tabs">
            <view 
              v-for="(tab, index) in discountTypes" 
              :key="index"
              class="discount-tab"
              :class="{ 'active': promotionForm.discountType === tab.value }"
              @tap="selectDiscountType(tab.value)"
            >
              {{ tab.label }}
            </view>
          </view>
        </view>

        <!-- 折扣设置 - 根据不同的优惠类型展示不同的表单 -->
        <view class="discount-form">
          <!-- 满减优惠 -->
          <block v-if="promotionForm.discountType === 'fullReduction'">
            <view class="discount-row" v-for="(item, index) in promotionForm.fullReductions" :key="index">
              <view class="discount-inputs">
                <view class="input-group">
                  <text class="input-prefix">满</text>
                  <input 
                    class="form-input" 
                    type="digit" 
                    v-model="item.full" 
                    placeholder="金额"
                  />
                  <text class="input-suffix">元</text>
                </view>
                <text class="discount-separator">减</text>
                <view class="input-group">
                  <input 
                    class="form-input" 
                    type="digit" 
                    v-model="item.reduction" 
                    placeholder="金额"
                  />
                  <text class="input-suffix">元</text>
                </view>
              </view>
              <view class="discount-delete" @tap="removeDiscountItem(index)">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M18 6L6 18M6 6L18 18" stroke="#FF3B30" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </view>
            </view>
            <view class="add-discount-button" @tap="addFullReduction">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 5V19M5 12H19" stroke="#FF9F0A" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <text class="add-text">添加满减规则</text>
            </view>
          </block>

          <!-- 折扣优惠 -->
          <block v-if="promotionForm.discountType === 'percentOff'">
            <view class="form-item">
              <text class="form-label">折扣力度</text>
              <view class="form-input-wrapper">
                <input 
                  class="form-input" 
                  type="digit" 
                  v-model="promotionForm.percentOff" 
                  placeholder="例如：8.5表示8.5折"
                />
                <text class="input-suffix">折</text>
              </view>
            </view>
          </block>

          <!-- 买赠优惠 -->
          <block v-if="promotionForm.discountType === 'buyGift'">
            <view class="form-item">
              <text class="form-label">购买数量</text>
              <view class="form-input-wrapper">
                <input 
                  class="form-input" 
                  type="number" 
                  v-model="promotionForm.buyCount" 
                  placeholder="购买商品数量"
                />
                <text class="input-suffix">件</text>
              </view>
            </view>
            <view class="form-item">
              <text class="form-label">赠送数量</text>
              <view class="form-input-wrapper">
                <input 
                  class="form-input" 
                  type="number" 
                  v-model="promotionForm.giftCount" 
                  placeholder="赠送商品数量"
                />
                <text class="input-suffix">件</text>
              </view>
            </view>
          </block>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-bar">
        <button class="submit-button" @tap="submitForm">创建活动</button>
        <button class="save-draft-button" @tap="saveDraft">保存草稿</button>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue';

// 活动表单数据
const promotionForm = reactive({
  title: '',
  type: '',
  startDate: '',
  endDate: '',
  banner: '',
  description: '',
  discountType: 'fullReduction', // 默认选中满减
  fullReductions: [{ full: '', reduction: '' }],
  percentOff: '',
  buyCount: '',
  giftCount: ''
});

// 优惠类型选项
const discountTypes = [
  { label: '满减优惠', value: 'fullReduction' },
  { label: '折扣优惠', value: 'percentOff' },
  { label: '买赠活动', value: 'buyGift' }
];

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 显示帮助
const showHelp = () => {
  uni.showToast({
    title: '查看活动创建帮助',
    icon: 'none'
  });
};

// 选择活动类型
const showTypeSelector = () => {
  uni.showActionSheet({
    itemList: ['打折促销', '限时秒杀', '新品首发', '节日活动', '会员特惠'],
    success: (res) => {
      const types = ['打折促销', '限时秒杀', '新品首发', '节日活动', '会员特惠'];
      promotionForm.type = types[res.tapIndex];
    }
  });
};

// 显示日期选择器
const showDatePicker = (type) => {
  uni.showToast({
    title: `${type === 'start' ? '开始' : '结束'}日期选择器开发中`,
    icon: 'none'
  });
  
  // 这里应该调用日期选择器，暂时用模拟数据
  setTimeout(() => {
    const today = new Date();
    const month = today.getMonth() + 1;
    const day = today.getDate();
    
    if (type === 'start') {
      promotionForm.startDate = `2023-${month}-${day}`;
    } else {
      // 结束日期设为7天后
      const endDate = new Date(today);
      endDate.setDate(today.getDate() + 7);
      promotionForm.endDate = `2023-${endDate.getMonth() + 1}-${endDate.getDate()}`;
    }
  }, 500);
};

// 选择封面图片
const chooseBanner = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['original', 'compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      promotionForm.banner = res.tempFilePaths[0];
    }
  });
};

// 移除封面
const removeBanner = () => {
  promotionForm.banner = '';
};

// 选择折扣类型
const selectDiscountType = (type) => {
  promotionForm.discountType = type;
};

// 添加满减规则
const addFullReduction = () => {
  if (promotionForm.fullReductions.length < 5) {
    promotionForm.fullReductions.push({ full: '', reduction: '' });
  } else {
    uni.showToast({
      title: '最多添加5个满减规则',
      icon: 'none'
    });
  }
};

// 删除满减规则
const removeDiscountItem = (index) => {
  if (promotionForm.fullReductions.length > 1) {
    promotionForm.fullReductions.splice(index, 1);
  } else {
    uni.showToast({
      title: '至少保留一个满减规则',
      icon: 'none'
    });
  }
};

// 提交表单
const submitForm = () => {
  // 表单验证
  if (!promotionForm.title) {
    return uni.showToast({ title: '请输入活动名称', icon: 'none' });
  }
  if (!promotionForm.type) {
    return uni.showToast({ title: '请选择活动类型', icon: 'none' });
  }
  if (!promotionForm.startDate || !promotionForm.endDate) {
    return uni.showToast({ title: '请选择活动时间', icon: 'none' });
  }
  if (!promotionForm.banner) {
    return uni.showToast({ title: '请上传活动封面', icon: 'none' });
  }
  if (!promotionForm.description) {
    return uni.showToast({ title: '请输入活动描述', icon: 'none' });
  }

  // 提交表单逻辑
  uni.showLoading({ title: '正在创建...' });
  
  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({ title: '创建成功', icon: 'success' });
    
    // 延迟返回上一页
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }, 2000);
};

// 保存草稿
const saveDraft = () => {
  uni.showLoading({ title: '正在保存...' });
  
  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({ title: '已保存草稿', icon: 'success' });
  }, 1000);
};
</script>

<style>
/* 页面根样式 */
.create-promotion-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f7;
  position: relative;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF9F0A, #FF3B30);
  color: white;
  padding: 48px 20px 16px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.navbar-left {
  display: flex;
  align-items: center;
  width: 40px;
}

.back-button {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24px;
  height: 24px;
}

.navbar-title {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.title-main {
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
  color: white;
}

.navbar-right {
  width: 40px;
  display: flex;
  justify-content: flex-end;
}

.help-button {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 内容区域 */
.content-container {
  flex: 1;
  padding: 20px;
  box-sizing: border-box;
  background-color: #f5f5f7;
}

/* 表单组样式 */
.form-group {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.form-group-header {
  margin-bottom: 20px;
}

.form-group-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 8px;
  display: block;
}

.form-group-subtitle {
  font-size: 14px;
  color: #8e8e93;
  display: block;
}

/* 表单项样式 */
.form-item {
  margin-bottom: 20px;
}

.form-label {
  font-size: 16px;
  font-weight: 500;
  color: #3a3a3c;
  margin-bottom: 8px;
  display: block;
}

.form-input-wrapper {
  background-color: #f5f5f7;
  border-radius: 10px;
  height: 48px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  position: relative;
  border: 1px solid rgba(60, 60, 67, 0.1);
}

.form-input {
  flex: 1;
  height: 100%;
  font-size: 16px;
  color: #000000;
  padding: 0;
}

.input-counter {
  font-size: 12px;
  color: #8e8e93;
  margin-left: 8px;
}

.input-prefix, .input-suffix {
  font-size: 16px;
  color: #8e8e93;
}

.input-prefix {
  margin-right: 8px;
}

.input-suffix {
  margin-left: 8px;
}

/* 选择器样式 */
.form-input-wrapper.selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.selector-value {
  font-size: 16px;
  color: #000000;
}

.selector-value.placeholder {
  color: #8e8e93;
}

.selector-icon {
  margin-left: 10px;
  transition: transform 0.3s ease;
}

/* 日期选择器 */
.date-range {
  display: flex;
  align-items: center;
}

.date-picker {
  flex: 1;
  background-color: #f5f5f7;
  border-radius: 10px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(60, 60, 67, 0.1);
}

.date-text {
  font-size: 16px;
  color: #000000;
}

.date-text.placeholder {
  color: #8e8e93;
}

.date-separator {
  margin: 0 10px;
  font-size: 16px;
  color: #8e8e93;
}

/* 文本区域样式 */
.form-input-wrapper.textarea {
  height: auto;
  min-height: 120px;
  padding: 15px;
}

.form-textarea {
  width: 100%;
  min-height: 100px;
  font-size: 16px;
  color: #000000;
  line-height: 1.5;
}

.textarea-counter {
  position: absolute;
  bottom: 8px;
  right: 15px;
  font-size: 12px;
  color: #8e8e93;
}

/* 图片上传器样式 */
.upload-container {
  display: flex;
  flex-direction: column;
}

.upload-preview {
  width: 100%;
  height: 200px;
  position: relative;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 10px;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-delete {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 30px;
  height: 30px;
  border-radius: 15px;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-button {
  width: 100%;
  height: 200px;
  background-color: rgba(255, 159, 10, 0.1);
  border: 2px dashed rgba(255, 159, 10, 0.3);
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #FF9F0A;
}

.upload-text {
  font-size: 16px;
  font-weight: 500;
  margin: 12px 0 8px;
}

.upload-tip {
  font-size: 12px;
  color: #8e8e93;
}

/* 折扣标签页 */
.discount-tabs {
  display: flex;
  background-color: #f5f5f7;
  border-radius: 10px;
  padding: 4px;
  margin-bottom: 20px;
}

.discount-tab {
  flex: 1;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #8e8e93;
  border-radius: 8px;
  font-weight: 500;
}

.discount-tab.active {
  background-color: white;
  color: #FF9F0A;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* 折扣表单 */
.discount-form {
  margin-bottom: 20px;
}

.discount-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.discount-inputs {
  flex: 1;
  display: flex;
  align-items: center;
}

.input-group {
  flex: 1;
  background-color: #f5f5f7;
  border-radius: 10px;
  height: 48px;
  display: flex;
  align-items: center;
  padding: 0 12px;
  border: 1px solid rgba(60, 60, 67, 0.1);
}

.discount-separator {
  margin: 0 10px;
  font-size: 16px;
  color: #8e8e93;
}

.discount-delete {
  margin-left: 12px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FF3B30;
}

.add-discount-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 0;
  color: #FF9F0A;
  font-size: 14px;
  font-weight: 500;
}

.add-text {
  margin-left: 8px;
}

/* 提交按钮 */
.submit-bar {
  margin: 20px 0 40px;
  display: flex;
  flex-direction: column;
}

.submit-button {
  height: 50px;
  line-height: 50px;
  border-radius: 25px;
  background: linear-gradient(135deg, #FF9F0A, #FF3B30);
  color: white;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 12px;
  box-shadow: 0 4px 12px rgba(255, 159, 10, 0.3);
}

.save-draft-button {
  height: 50px;
  line-height: 50px;
  border-radius: 25px;
  background-color: transparent;
  border: 1px solid #FF9F0A;
  color: #FF9F0A;
  font-size: 16px;
  text-align: center;
}
</style> 