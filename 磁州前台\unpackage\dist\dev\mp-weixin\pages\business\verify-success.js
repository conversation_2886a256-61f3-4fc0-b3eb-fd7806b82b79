"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  __name: "verify-success",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const applyTime = common_vendor.ref("刚刚");
    common_vendor.onLoad(() => {
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight;
      loadApplyData();
    });
    const goBack = () => {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    };
    const loadApplyData = () => {
      const verifyApplyData = common_vendor.index.getStorageSync("verifyApplyData");
      if (verifyApplyData && verifyApplyData.applyTime) {
        applyTime.value = verifyApplyData.applyTime;
      }
    };
    const goToMerchantCenter = () => {
      common_vendor.index.navigateTo({
        url: "/pages/my/merchant?tab=verify"
      });
    };
    const goToHome = () => {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    };
    const contactCustomerService = () => {
      common_vendor.index.makePhoneCall({
        phoneNumber: "************",
        fail: () => {
          common_vendor.index.showToast({
            title: "拨打客服电话失败",
            icon: "none"
          });
        }
      });
    };
    return (_ctx, _cache) => {
      return {
        a: statusBarHeight.value + "px",
        b: common_assets._imports_0$13,
        c: common_vendor.o(goBack),
        d: common_assets._imports_1$8,
        e: common_vendor.t(applyTime.value),
        f: common_assets._imports_4$3,
        g: common_vendor.o(goToMerchantCenter),
        h: common_vendor.o(goToHome),
        i: common_vendor.o(contactCustomerService)
      };
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/business/verify-success.js.map
