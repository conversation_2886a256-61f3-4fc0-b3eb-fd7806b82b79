{"version": 3, "file": "driver-profile.js", "sources": ["carpool-package/pages/carpool/my/driver-profile.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/Y2FycG9vbC1wYWNrYWdlXHBhZ2VzXGNhcnBvb2xcbXlcZHJpdmVyLXByb2ZpbGUudnVl"], "sourcesContent": ["<template>\n  <view class=\"driver-profile-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-header\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"header-content\">\n        <view class=\"left-action\" @click=\"goBack\">\n          <image src=\"/static/images/tabbar/最新返回键.png\" class=\"action-icon back-icon\"></image>\n        </view>\n        <view class=\"title-area\">\n          <text class=\"page-title\">个人中心</text>\n        </view>\n        <view class=\"right-action\">\n        </view>\n      </view>\n    </view>\n    \n    <!-- 司机信息卡片 -->\n    <view class=\"driver-card\" :style=\"{ marginTop: (statusBarHeight + 44) + 'px' }\">\n      <view class=\"driver-info-section\">\n        <view class=\"driver-avatar-container\">\n          <image :src=\"driverInfo.avatar\" class=\"driver-avatar\"></image>\n          <view class=\"verified-badge\" v-if=\"driverInfo.isVerified\">\n            <image src=\"/static/images/icons/verified.png\" class=\"verified-icon\"></image>\n          </view>\n        </view>\n        \n        <view class=\"driver-basic-info\">\n          <view class=\"driver-name-wrap\">\n            <text class=\"driver-name\">{{driverInfo.name}}</text>\n            <view class=\"driver-level\">\n              <image src=\"/static/images/icons/crown.png\" class=\"level-icon\"></image>\n              <text class=\"level-text\">{{driverInfo.level}}</text>\n            </view>\n          </view>\n          \n          <view class=\"driver-id\">ID: {{driverInfo.id}}</view>\n          \n          <view class=\"driver-stats\">\n            <view class=\"stat-item\">\n              <text class=\"stat-value\">{{driverInfo.tripCount}}</text>\n              <text class=\"stat-label\">行程数</text>\n            </view>\n            <view class=\"stat-divider\"></view>\n            <view class=\"stat-item\">\n              <text class=\"stat-value\">{{driverInfo.serviceYears}}</text>\n              <text class=\"stat-label\">服务年限</text>\n            </view>\n            <view class=\"stat-divider\"></view>\n            <view class=\"stat-item\">\n              <text class=\"stat-value\">{{driverInfo.rating}}</text>\n              <text class=\"stat-label\">评分</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"driver-tags\">\n        <view class=\"tag-item\" v-for=\"(tag, index) in driverInfo.tags\" :key=\"index\">\n          <text class=\"tag-text\">{{tag}}</text>\n        </view>\n      </view>\n      \n      <view class=\"driver-intro\">\n        <text class=\"intro-title\">个人介绍</text>\n        <text class=\"intro-content\">{{driverInfo.introduction}}</text>\n      </view>\n    </view>\n    \n    <!-- 车辆信息 -->\n    <view class=\"section-card\">\n      <view class=\"section-header\">\n        <view class=\"section-title-wrap\">\n          <image src=\"/static/images/icons/car.png\" class=\"section-icon\"></image>\n          <text class=\"section-title\">车辆信息</text>\n        </view>\n        <view class=\"section-more\" @click=\"navigateToPage('vehicle-detail')\">\n          <text class=\"more-text\">查看详情</text>\n          <text class=\"more-icon\">&gt;</text>\n        </view>\n      </view>\n      \n      <view class=\"vehicle-info\">\n        <view class=\"vehicle-image-container\">\n          <image :src=\"driverInfo.vehicle.image\" class=\"vehicle-image\"></image>\n        </view>\n        \n        <view class=\"vehicle-details\">\n          <text class=\"vehicle-name\">{{driverInfo.vehicle.brand}} {{driverInfo.vehicle.model}}</text>\n          <text class=\"vehicle-plate\">{{driverInfo.vehicle.plateNumber}}</text>\n          <text class=\"vehicle-desc\">{{driverInfo.vehicle.color}} · {{driverInfo.vehicle.seats}}座</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 服务统计 -->\n    <view class=\"section-card\">\n      <view class=\"section-header\">\n        <view class=\"section-title-wrap\">\n          <image src=\"/static/images/icons/stats.png\" class=\"section-icon\"></image>\n          <text class=\"section-title\">服务统计</text>\n        </view>\n      </view>\n      \n      <view class=\"service-stats\">\n        <view class=\"stat-row\">\n          <view class=\"stat-label\">总行程数</view>\n          <view class=\"stat-value-large\">{{driverInfo.serviceStats.totalTrips}}</view>\n        </view>\n        \n        <view class=\"stat-row\">\n          <view class=\"stat-label\">总服务时长</view>\n          <view class=\"stat-value-large\">{{driverInfo.serviceStats.totalHours}}小时</view>\n        </view>\n        \n        <view class=\"stat-row\">\n          <view class=\"stat-label\">总行驶里程</view>\n          <view class=\"stat-value-large\">{{driverInfo.serviceStats.totalDistance}}公里</view>\n        </view>\n        \n        <view class=\"stat-row\">\n          <view class=\"stat-label\">好评率</view>\n          <view class=\"stat-value-large\">{{driverInfo.serviceStats.positiveRate}}%</view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 服务区域 -->\n    <view class=\"section-card\">\n      <view class=\"section-header\">\n        <view class=\"section-title-wrap\">\n          <image src=\"/static/images/icons/location.png\" class=\"section-icon\"></image>\n          <text class=\"section-title\">服务区域</text>\n        </view>\n      </view>\n      \n      <view class=\"service-areas\">\n        <view class=\"area-item\" v-for=\"(area, index) in driverInfo.serviceAreas\" :key=\"index\">\n          <view class=\"area-icon-wrap\">\n            <image src=\"/static/images/icons/area-pin.png\" class=\"area-icon\"></image>\n          </view>\n          <text class=\"area-name\">{{area}}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部安全区域 -->\n    <view class=\"safe-area-bottom\"></view>\n    \n    <!-- 底部联系按钮 -->\n    <view class=\"contact-bar\">\n      <button class=\"contact-btn primary\" @click=\"contactDriver\">联系司机</button>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue';\n\n// 状态栏高度\nconst statusBarHeight = ref(20);\n\n// 司机信息\nconst driverInfo = ref({\n  id: '**********',\n  name: '张先生',\n  avatar: '/static/images/avatar/user1.png',\n  isVerified: true,\n  level: '金牌司机',\n  tripCount: 386,\n  serviceYears: 2.5,\n  rating: 4.9,\n  tags: ['驾驶平稳', '很准时', '路线熟悉', '热心服务'],\n  introduction: '您好，我是一名有着5年驾龄的老司机，熟悉磁县及周边地区的道路情况。我的车辆定期保养，保证舒适安全的乘车体验。欢迎选择我的拼车服务，我会尽力为您提供最好的出行体验。',\n  vehicle: {\n    brand: '大众',\n    model: '帕萨特',\n    plateNumber: '冀E·12345',\n    color: '白色',\n    seats: 5,\n    image: '/static/images/vehicles/car1.jpg'\n  },\n  serviceStats: {\n    totalTrips: 386,\n    totalHours: 720,\n    totalDistance: 15680,\n    positiveRate: 98\n  },\n  serviceAreas: [\n    '磁县城区',\n    '邯郸市区',\n    '磁县-邯郸',\n    '磁县-石家庄',\n    '磁县-邢台'\n  ]\n});\n\n// 页面加载时执行\nonMounted(() => {\n  // 获取状态栏高度\n  const systemInfo = uni.getSystemInfoSync();\n  statusBarHeight.value = systemInfo.statusBarHeight || 20;\n  \n  loadDriverInfo();\n});\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n\n// 加载司机信息\nconst loadDriverInfo = () => {\n  // 实际应用中应该从API获取数据\n  console.log('加载司机信息');\n};\n\n// 分享功能已移除\n\n// 导航到页面\nconst navigateToPage = (page) => {\n  if (page === 'vehicle-detail') {\n    uni.navigateTo({\n      url: '/carpool-package/pages/carpool/my/vehicle-detail'\n    });\n  }\n};\n\n// 联系司机\nconst contactDriver = () => {\n  uni.showModal({\n    title: '联系司机',\n    content: '是否拨打司机电话？',\n    success: (res) => {\n      if (res.confirm) {\n        uni.makePhoneCall({\n          phoneNumber: '13812345678',\n          fail: () => {\n            uni.showToast({\n              title: '拨打电话失败',\n              icon: 'none'\n            });\n          }\n        });\n      }\n    }\n  });\n};\n\n// 暴露页面方法给页面实例\ndefineExpose({});\n</script>\n\n<style lang=\"scss\">\n/* 容器样式 */\n.driver-profile-container {\n  background-color: #F5F5F5;\n  min-height: 100vh;\n  padding-bottom: 100rpx;\n}\n\n/* 自定义导航栏 */\n.custom-header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background-color: #0A84FF;\n}\n\n.header-content {\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 12px;\n}\n\n.left-action, .right-action {\n  width: 44px;\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.action-icon {\n  width: 24px;\n  height: 24px;\n}\n\n.back-icon {\n  width: 24px;\n  height: 24px;\n  /* 图标是黑色的，需要转为白色 */\n  filter: brightness(0) invert(1);\n}\n\n.title-area {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.page-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #FFFFFF;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\n}\n\n/* 司机卡片 */\n.driver-card {\n  background-color: #FFFFFF;\n  border-radius: 20rpx;\n  margin: 20rpx;\n  padding: 24rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.driver-info-section {\n  display: flex;\n  margin-bottom: 24rpx;\n}\n\n.driver-avatar-container {\n  position: relative;\n  width: 120rpx;\n  height: 120rpx;\n  margin-right: 24rpx;\n}\n\n.driver-avatar {\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  border: 2rpx solid #F2F2F7;\n}\n\n.verified-badge {\n  position: absolute;\n  right: -6rpx;\n  bottom: -6rpx;\n  width: 36rpx;\n  height: 36rpx;\n  background-color: #FFFFFF;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);\n}\n\n.verified-icon {\n  width: 28rpx;\n  height: 28rpx;\n}\n\n.driver-basic-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.driver-name-wrap {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8rpx;\n}\n\n.driver-name {\n  font-size: 34rpx;\n  font-weight: 600;\n  color: #333333;\n  margin-right: 12rpx;\n}\n\n.driver-level {\n  display: flex;\n  align-items: center;\n  background-color: #FFF9E6;\n  padding: 4rpx 12rpx;\n  border-radius: 20rpx;\n}\n\n.level-icon {\n  width: 24rpx;\n  height: 24rpx;\n  margin-right: 4rpx;\n}\n\n.level-text {\n  font-size: 22rpx;\n  color: #FF9F0A;\n}\n\n.driver-id {\n  font-size: 24rpx;\n  color: #8E8E93;\n  margin-bottom: 16rpx;\n}\n\n.driver-stats {\n  display: flex;\n  align-items: center;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.stat-value {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #333333;\n  line-height: 1;\n}\n\n.stat-label {\n  font-size: 22rpx;\n  color: #8E8E93;\n  margin-top: 4rpx;\n}\n\n.stat-divider {\n  width: 1px;\n  height: 30rpx;\n  background-color: #DDDDDD;\n  margin: 0 24rpx;\n}\n\n.driver-tags {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12rpx;\n  margin-bottom: 24rpx;\n}\n\n.tag-item {\n  background-color: #F2F2F7;\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n}\n\n.tag-text {\n  font-size: 24rpx;\n  color: #666666;\n}\n\n.driver-intro {\n  border-top: 1px solid #F2F2F7;\n  padding-top: 20rpx;\n}\n\n.intro-title {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333333;\n  margin-bottom: 12rpx;\n  display: block;\n}\n\n.intro-content {\n  font-size: 26rpx;\n  color: #666666;\n  line-height: 1.6;\n}\n\n/* 通用卡片样式 */\n.section-card {\n  margin: 20rpx;\n  background-color: #FFFFFF;\n  border-radius: 16rpx;\n  padding: 24rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.section-title-wrap {\n  display: flex;\n  align-items: center;\n}\n\n.section-icon {\n  width: 32rpx;\n  height: 32rpx;\n  margin-right: 12rpx;\n}\n\n.section-title {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #333333;\n}\n\n.section-more {\n  display: flex;\n  align-items: center;\n}\n\n.more-text {\n  font-size: 26rpx;\n  color: #8E8E93;\n  margin-right: 4rpx;\n}\n\n.more-icon {\n  font-size: 26rpx;\n  color: #8E8E93;\n}\n\n/* 车辆信息样式 */\n.vehicle-info {\n  display: flex;\n  align-items: center;\n}\n\n.vehicle-image-container {\n  width: 180rpx;\n  height: 120rpx;\n  border-radius: 8rpx;\n  overflow: hidden;\n  margin-right: 20rpx;\n}\n\n.vehicle-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.vehicle-details {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.vehicle-name {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333333;\n  margin-bottom: 6rpx;\n}\n\n.vehicle-plate {\n  font-size: 26rpx;\n  color: #666666;\n  margin-bottom: 6rpx;\n}\n\n.vehicle-desc {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n/* 服务统计样式 */\n.service-stats {\n  display: flex;\n  flex-direction: column;\n}\n\n.stat-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16rpx 0;\n  border-bottom: 1px solid #F2F2F7;\n}\n\n.stat-row:last-child {\n  border-bottom: none;\n}\n\n.stat-label {\n  font-size: 28rpx;\n  color: #666666;\n}\n\n.stat-value-large {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #333333;\n}\n\n/* 服务区域样式 */\n.service-areas {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16rpx;\n}\n\n.area-item {\n  display: flex;\n  align-items: center;\n  background-color: #F2F2F7;\n  padding: 10rpx 16rpx;\n  border-radius: 20rpx;\n}\n\n.area-icon-wrap {\n  width: 28rpx;\n  height: 28rpx;\n  margin-right: 8rpx;\n}\n\n.area-icon {\n  width: 100%;\n  height: 100%;\n}\n\n.area-name {\n  font-size: 26rpx;\n  color: #666666;\n}\n\n/* 底部联系按钮 */\n.contact-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background-color: #FFFFFF;\n  padding: 20rpx;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  z-index: 99;\n}\n\n.contact-btn {\n  width: 100%;\n  height: 80rpx;\n  border-radius: 40rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 30rpx;\n  font-weight: 500;\n}\n\n.primary {\n  background: linear-gradient(135deg, #0A84FF, #0040DD);\n  color: #FFFFFF;\n}\n\n.safe-area-bottom {\n  height: env(safe-area-inset-bottom);\n  width: 100%;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/carpool-package/pages/carpool/my/driver-profile.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni", "MiniProgramPage"], "mappings": ";;;;;;AA+JA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAG9B,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACrB,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,WAAW;AAAA,MACX,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,MAAM,CAAC,QAAQ,OAAO,QAAQ,MAAM;AAAA,MACpC,cAAc;AAAA,MACd,SAAS;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,aAAa;AAAA,QACb,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,MACR;AAAA,MACD,cAAc;AAAA,QACZ,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,cAAc;AAAA,MACf;AAAA,MACD,cAAc;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,IACH,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AAEd,YAAM,aAAaC,oBAAI;AACvB,sBAAgB,QAAQ,WAAW,mBAAmB;AAEtD;IACF,CAAC;AAGD,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,iBAAiB,MAAM;AAE3BA,oBAAAA,MAAY,MAAA,OAAA,8DAAA,QAAQ;AAAA,IACtB;AAKA,UAAM,iBAAiB,CAAC,SAAS;AAC/B,UAAI,SAAS,kBAAkB;AAC7BA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK;AAAA,QACX,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,gBAAgB,MAAM;AAC1BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,cAAc;AAAA,cAChB,aAAa;AAAA,cACb,MAAM,MAAM;AACVA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACpB,CAAa;AAAA,cACF;AAAA,YACX,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,aAAa,CAAE,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxPf,GAAG,WAAWC,SAAe;"}