
.messages-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
}
.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}
.back-icon {
  width: 40rpx;
  height: 40rpx;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}
.navbar-right {
  width: 80rpx;
  display: flex;
  justify-content: flex-end;
}
.clear-text {
  font-size: 14px;
  color: #fff;
}

/* 选项卡 */
.tabs-container {
  position: fixed;
  display: flex;
  width: 100%;
  background-color: #fff;
  z-index: 100;
  border-bottom: 1rpx solid #f0f0f0;
  box-sizing: border-box;
  height: 44px;
}
.tab-item {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
}
.tab-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 400;
  transition: all 0.3s;
}
.tab-item.active .tab-text {
  color: #0066FF;
  font-weight: 500;
}
.badge {
  position: absolute;
  top: 8rpx;
  right: 50%;
  margin-right: -50rpx;
  min-width: 32rpx;
  height: 32rpx;
  background-color: #ff5252;
  color: #fff;
  font-size: 20rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
}
.tab-line {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 6rpx;
  width: 33.3% !important;
  background-color: #0066FF;
  border-radius: 6rpx;
  transition: transform 0.3s ease;
  z-index: 101;
  transform: translateX(0);
}

/* 内容区域 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.content-swiper {
  width: 100%;
  height: calc(100vh - 108px); /* 状态栏(~20px) + 导航栏(44px) + 选项卡(44px) */
}
.tab-scroll {
  height: 100%;
  box-sizing: border-box;
}

/* 消息列表 */
.message-list, .chat-list {
  padding: 0 30rpx;
}
.message-item, .chat-item {
  display: flex;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}
.message-left, .chat-left {
  position: relative;
  margin-right: 20rpx;
}
.message-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  background-color: #f5f7fa;
}
.message-avatar, .chat-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #f5f7fa;
}
.unread-dot {
  position: absolute;
  top: 0;
  right: 0;
  width: 16rpx;
  height: 16rpx;
  background-color: #ff5252;
  border-radius: 8rpx;
}
.chat-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 32rpx;
  height: 32rpx;
  background-color: #ff5252;
  color: #fff;
  font-size: 20rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
}
.message-center, .chat-center {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
}
.message-title, .chat-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
}
.user-name {
  max-width: 200rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.action-text {
  color: #666;
  font-weight: 400;
}
.message-content, .chat-last-message {
  font-size: 26rpx;
  color: #999;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.message-right, .chat-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  margin-left: 20rpx;
}
.message-time, .chat-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}
.content-preview {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  background-color: #f5f7fa;
}

/* 空状态 */
.empty-view {
  padding: 120rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 列表底部 */
.list-bottom {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  padding: 30rpx 0;
}
