
.beautiful-cizhou {
  position: relative;
  height: 100vh;
  background-color: #F7F7F7;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
.navbar-left, .navbar-right {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.navbar-title {
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  flex: 1;
  margin: 0 50px; /* 添加左右外边距，为返回按钮留出空间 */
}
.back-icon, .icon-image {
  width: 32px;
  height: 32px;
}

/* 内容区域 */
.content-scroll {
  height: 100vh;
  box-sizing: border-box;
}

/* 顶部视觉区 */
.hero-section {
  width: 100%;
  height: 420rpx;
  position: relative;
  margin-bottom: 24rpx;
  overflow: hidden;
}
.hero-swiper {
  width: 100%;
  height: 100%;
}
.hero-image {
  width: 100%;
  height: 100%;
}
.hero-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx 40rpx;
  background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
}
.hero-title {
  font-size: 36rpx;
  color: #FFFFFF;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}
.hero-desc {
  font-size: 24rpx;
  color: rgba(255,255,255,0.9);
  display: block;
}

/* 分类标签 */
.category-tabs {
  background-color: #FFFFFF;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}
.tab-scroll {
  white-space: nowrap;
  width: 100%;
}
.tab-item {
  display: inline-block;
  padding: 16rpx 30rpx;
  margin: 0 10rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #F5F5F5;
  transition: all 0.3s;
}
.tab-item:first-child {
  margin-left: 20rpx;
}
.tab-item:last-child {
  margin-right: 20rpx;
}
.tab-item.active {
  background-color: #007AFF;
  color: #FFFFFF;
  box-shadow: 0 4rpx 10rpx rgba(0,122,255,0.3);
}
.tab-text {
  font-weight: 500;
}

/* 通用板块样式 */
.feature-section, .attractions-section, .events-section {
  margin: 0 20rpx 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
  overflow: hidden;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.section-title {
  display: flex;
  align-items: center;
}
.title-text {
  font-size: 34rpx;
  font-weight: 600;
  color: #333333;
  position: relative;
  padding-left: 24rpx;
}
.title-text::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background-color: #007AFF;
  border-radius: 4rpx;
}
.section-more {
  display: flex;
  align-items: center;
}
.more-text {
  font-size: 26rpx;
  color: #007AFF;
}
.more-icon {
  font-size: 26rpx;
  color: #007AFF;
  margin-left: 4rpx;
}

/* 特色板块 */
.feature-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}
.feature-card {
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
  position: relative;
}
.feature-image {
  width: 100%;
  height: 180rpx;
}
.feature-info {
  padding: 20rpx;
  background-color: #FFFFFF;
}
.feature-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
  display: block;
}
.feature-desc {
  font-size: 24rpx;
  color: #666666;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.feature-tag {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  padding: 6rpx 12rpx;
  background-color: rgba(0,122,255,0.8);
  color: #FFFFFF;
  font-size: 22rpx;
  border-radius: 20rpx;
}

/* 景点推荐 */
.attractions-scroll {
  width: 100%;
  white-space: nowrap;
}
.attraction-item {
  display: inline-block;
  width: 400rpx;
  margin-right: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
  background-color: #FFFFFF;
}
.attraction-item:last-child {
  margin-right: 0;
}
.attraction-image {
  width: 100%;
  height: 220rpx;
}
.attraction-info {
  padding: 20rpx;
}
.attraction-name-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}
.attraction-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}
.attraction-rating {
  display: flex;
  align-items: center;
}
.rating-value {
  font-size: 24rpx;
  color: #FF9500;
  font-weight: 600;
  margin-right: 4rpx;
}
.rating-star {
  width: 24rpx;
  height: 24rpx;
}
.attraction-address {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.attraction-tags {
  display: flex;
  flex-wrap: wrap;
}
.tag-item {
  font-size: 20rpx;
  color: #007AFF;
  background-color: rgba(0,122,255,0.1);
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
  margin-right: 8rpx;
  margin-bottom: 8rpx;
}

/* 活动日历 */
.events-list {
}
.event-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1px solid #F0F0F0;
}
.event-item:last-child {
  border-bottom: none;
}
.event-date {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  background-color: #F5F5F5;
  border-radius: 12rpx;
  padding: 16rpx 0;
  margin-right: 20rpx;
}
.event-month {
  font-size: 24rpx;
  color: #666666;
}
.event-day {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}
.event-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.event-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}
.event-location {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
}
.event-status {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  display: inline-block;
}
.status-ongoing {
  background-color: rgba(40,205,65,0.1);
  color: #28CD41;
}
.status-upcoming {
  background-color: rgba(0,122,255,0.1);
  color: #007AFF;
}
.status-ended {
  background-color: rgba(142,142,147,0.1);
  color: #8E8E93;
}
.event-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
}
.action-text {
  font-size: 26rpx;
  color: #007AFF;
}

/* 本地文化 */
.culture-section {
  margin: 0 20rpx 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}
.culture-cards {
}
.culture-card {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #F0F0F0;
}
.culture-card:last-child {
  border-bottom: none;
}
.culture-icon-bg {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}
.culture-icon {
  width: 40rpx;
  height: 40rpx;
}
.culture-info {
  flex: 1;
}
.culture-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
  display: block;
}
.culture-desc {
  font-size: 24rpx;
  color: #666666;
  display: block;
}
.arrow-right {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.arrow-icon {
  font-size: 24rpx;
  color: #999999;
}

/* 攻略推荐 */
.guides-section {
  margin: 0 20rpx 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}
.guides-list {
}
.guide-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1px solid #F0F0F0;
}
.guide-item:last-child {
  border-bottom: none;
}
.guide-image {
  width: 200rpx;
  height: 140rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}
.guide-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.guide-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
}
.guide-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.guide-author {
  display: flex;
  align-items: center;
}
.author-avatar {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  margin-right: 10rpx;
}
.author-name {
  font-size: 24rpx;
  color: #666666;
}
.guide-stats {
  display: flex;
  align-items: center;
}
.stat-item {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
}
.stat-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 6rpx;
}
.stat-count {
  font-size: 22rpx;
  color: #999999;
}
