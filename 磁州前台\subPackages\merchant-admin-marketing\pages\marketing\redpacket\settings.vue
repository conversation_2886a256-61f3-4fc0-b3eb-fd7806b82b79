<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">红包营销设置</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 设置内容 -->
    <scroll-view scroll-y class="settings-content">
      <!-- 基础设置 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-title">基础设置</text>
        </view>
        
        <view class="settings-group">
          <view class="settings-item">
            <view class="item-left">
              <text class="item-label">自动审核红包</text>
            </view>
            <view class="item-right">
              <switch :checked="settings.autoApprove" @change="toggleAutoApprove" color="#FF4D4F" />
            </view>
          </view>
          <view class="settings-item">
            <view class="item-left">
              <text class="item-label">红包领取提醒</text>
              <text class="item-desc">用户领取红包时通知商家</text>
            </view>
            <view class="item-right">
              <switch :checked="settings.receiveNotification" @change="toggleReceiveNotification" color="#FF4D4F" />
            </view>
          </view>
          <view class="settings-item">
            <view class="item-left">
              <text class="item-label">红包到期提醒</text>
              <text class="item-desc">红包即将过期时提醒用户</text>
            </view>
            <view class="item-right">
              <switch :checked="settings.expiryNotification" @change="toggleExpiryNotification" color="#FF4D4F" />
            </view>
          </view>
        </view>
      </view>
      
      <!-- 红包限制 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-title">红包限制</text>
        </view>
        
        <view class="settings-group">
          <view class="settings-item">
            <view class="item-left">
              <text class="item-label">单个红包最高金额</text>
            </view>
            <view class="item-right">
              <text class="item-value">¥{{settings.maxAmount}}</text>
              <view class="arrow-icon"></view>
            </view>
          </view>
          <view class="settings-item" @click="showLimitPicker('dailyLimit')">
            <view class="item-left">
              <text class="item-label">每日发放上限</text>
              <text class="item-desc">每日最多发放红包数量</text>
            </view>
            <view class="item-right">
              <text class="item-value">{{settings.dailyLimit}}个</text>
              <view class="arrow-icon"></view>
            </view>
          </view>
          <view class="settings-item" @click="showLimitPicker('userLimit')">
            <view class="item-left">
              <text class="item-label">用户领取限制</text>
              <text class="item-desc">单个用户每日最多领取次数</text>
            </view>
            <view class="item-right">
              <text class="item-value">{{settings.userLimit}}次</text>
              <view class="arrow-icon"></view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 红包模板设置 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-title">红包模板设置</text>
        </view>
        
        <view class="settings-group">
          <view class="settings-item" @click="navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/redpacket/template')">
            <view class="item-left">
              <text class="item-label">红包模板管理</text>
              <text class="item-desc">创建和管理红包模板</text>
            </view>
            <view class="item-right">
              <text class="item-value">{{settings.templateCount}}个</text>
              <view class="arrow-icon"></view>
            </view>
          </view>
          <view class="settings-item">
            <view class="item-left">
              <text class="item-label">默认红包样式</text>
            </view>
            <view class="item-right">
              <view class="template-preview" :style="{ backgroundImage: 'url(' + settings.defaultTemplate.preview + ')' }"></view>
              <view class="arrow-icon"></view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 高级设置 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-title">高级设置</text>
        </view>
        
        <view class="settings-group">
          <view class="settings-item">
            <view class="item-left">
              <text class="item-label">风控设置</text>
              <text class="item-desc">设置风险控制规则</text>
            </view>
            <view class="item-right">
              <view class="arrow-icon"></view>
            </view>
          </view>
          <view class="settings-item">
            <view class="item-left">
              <text class="item-label">用户黑名单</text>
              <text class="item-desc">管理不可领取红包的用户</text>
            </view>
            <view class="item-right">
              <text class="item-value">{{settings.blacklistCount}}人</text>
              <view class="arrow-icon"></view>
            </view>
          </view>
          <view class="settings-item">
            <view class="item-left">
              <text class="item-label">数据导出</text>
              <text class="item-desc">导出红包营销数据</text>
            </view>
            <view class="item-right">
              <view class="arrow-icon"></view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 账户与安全 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-title">账户与安全</text>
        </view>
        
        <view class="settings-group">
          <view class="settings-item">
            <view class="item-left">
              <text class="item-label">支付密码</text>
              <text class="item-desc">设置发放红包时的支付密码</text>
            </view>
            <view class="item-right">
              <text class="item-value">已设置</text>
              <view class="arrow-icon"></view>
            </view>
          </view>
          <view class="settings-item">
            <view class="item-left">
              <text class="item-label">操作日志</text>
              <text class="item-desc">查看红包操作记录</text>
            </view>
            <view class="item-right">
              <view class="arrow-icon"></view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 清除缓存按钮 -->
      <view class="clear-cache-btn" @click="clearCache">
        <text class="btn-text">清除缓存</text>
      </view>
    </scroll-view>
    
    <!-- 数量选择弹窗 -->
    <view class="popup-mask" v-if="showLimitPopup" @click="cancelLimitPicker"></view>
    <view class="popup-content" v-if="showLimitPopup">
      <view class="popup-header">
        <text class="popup-title">{{currentLimitTitle}}</text>
        <view class="popup-close" @click="cancelLimitPicker">×</view>
      </view>
      <view class="limit-picker">
        <view class="limit-control" @click="decreaseLimit">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="5" y1="12" x2="19" y2="12"></line>
          </svg>
        </view>
        <text class="limit-value">{{tempLimitValue}}</text>
        <view class="limit-control" @click="increaseLimit">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="12" y1="5" x2="12" y2="19"></line>
            <line x1="5" y1="12" x2="19" y2="12"></line>
          </svg>
        </view>
      </view>
      <view class="popup-buttons">
        <button class="cancel-btn" @click="cancelLimitPicker">取消</button>
        <button class="confirm-btn" @click="confirmLimitPicker">确定</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      settings: {
        autoApprove: true,
        receiveNotification: true,
        expiryNotification: false,
        maxAmount: 200,
        dailyLimit: 100,
        userLimit: 3,
        templateCount: 5,
        defaultTemplate: {
          id: 1,
          name: '默认红包',
          preview: '/static/images/redpacket-template-default.png'
        },
        blacklistCount: 7
      },
      showLimitPopup: false,
      currentLimit: '',
      currentLimitTitle: '',
      tempLimitValue: 0
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showHelp() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/redpacket/guide'
      });
    },
    toggleAutoApprove(e) {
      this.settings.autoApprove = e.detail.value;
      this.saveSettings();
    },
    toggleReceiveNotification(e) {
      this.settings.receiveNotification = e.detail.value;
      this.saveSettings();
    },
    toggleExpiryNotification(e) {
      this.settings.expiryNotification = e.detail.value;
      this.saveSettings();
    },
    showLimitPicker(limitType) {
      this.currentLimit = limitType;
      this.tempLimitValue = this.settings[limitType];
      
      if (limitType === 'dailyLimit') {
        this.currentLimitTitle = '每日发放上限';
      } else if (limitType === 'userLimit') {
        this.currentLimitTitle = '用户领取限制';
      }
      
      this.showLimitPopup = true;
    },
    cancelLimitPicker() {
      this.showLimitPopup = false;
    },
    confirmLimitPicker() {
      this.settings[this.currentLimit] = this.tempLimitValue;
      this.saveSettings();
      this.showLimitPopup = false;
    },
    decreaseLimit() {
      if (this.tempLimitValue > 1) {
        this.tempLimitValue--;
      }
    },
    increaseLimit() {
      this.tempLimitValue++;
    },
    navigateTo(url) {
      uni.navigateTo({
        url: url
      });
    },
    clearCache() {
      uni.showLoading({
        title: '清除中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '缓存已清除',
          icon: 'success'
        });
      }, 1000);
    },
    saveSettings() {
      // 模拟保存设置
      console.log('保存设置', this.settings);
      uni.showToast({
        title: '设置已保存',
        icon: 'success',
        duration: 1000
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏样式 */
.navbar {
  display: flex;
  align-items: center;
  height: 44px;
  background-color: #fff;
  padding: 0 15px;
  position: relative;
}

.navbar-back {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-top: 2px solid #333;
  border-left: 2px solid #333;
  transform: rotate(-45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 600;
  color: #333;
}

.navbar-right {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  border: 1px solid #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #999;
}

/* 设置内容样式 */
.settings-content {
  flex: 1;
  padding: 15px;
}

.settings-section {
  margin-bottom: 15px;
}

.section-header {
  margin-bottom: 10px;
}

.section-title {
  font-size: 15px;
  font-weight: 600;
  color: #666;
}

.settings-group {
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
}

.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.settings-item:last-child {
  border-bottom: none;
}

.item-left {
  flex: 1;
}

.item-label {
  font-size: 15px;
  color: #333;
  display: block;
}

.item-desc {
  font-size: 12px;
  color: #999;
  display: block;
  margin-top: 4px;
}

.item-right {
  display: flex;
  align-items: center;
}

.item-value {
  font-size: 14px;
  color: #999;
  margin-right: 5px;
}

.arrow-icon {
  width: 8px;
  height: 8px;
  border-top: 1px solid #ccc;
  border-right: 1px solid #ccc;
  transform: rotate(45deg);
}

.template-preview {
  width: 40px;
  height: 60px;
  background-size: cover;
  background-position: center;
  border-radius: 4px;
  margin-right: 10px;
}

/* 清除缓存按钮 */
.clear-cache-btn {
  background-color: #fff;
  border-radius: 10px;
  padding: 15px;
  text-align: center;
  margin-top: 20px;
  margin-bottom: 30px;
}

.btn-text {
  font-size: 15px;
  color: #FF4D4F;
}

/* 弹窗样式 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 100;
}

.popup-content {
  position: fixed;
  left: 15px;
  right: 15px;
  bottom: 30px;
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
  z-index: 101;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.popup-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.popup-close {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #999;
}

.limit-picker {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.limit-control {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  border: 1px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

.limit-value {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 30px;
  min-width: 40px;
  text-align: center;
}

.popup-buttons {
  display: flex;
  justify-content: space-between;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  height: 40px;
  border-radius: 20px;
  font-size: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
  margin-right: 10px;
}

.confirm-btn {
  background-color: #FF4D4F;
  color: #fff;
  margin-left: 10px;
}
</style>