/**
 * 商家广告相关API
 * 处理商家看广告免费续费功能
 */

import request from '@/utils/request'

// 商家广告API
export const merchantAdApi = {
  /**
   * 商家观看广告续费
   * @param {Object} data - 续费数据
   * @param {string} data.merchant_id - 商家ID
   * @param {string} data.ad_type - 广告类型 'merchant_renew'
   * @param {number} data.watch_duration - 观看时长(秒)
   * @param {string} data.session_id - 会话ID
   */
  renewByAd(data) {
    return request.post('/api/merchant/ads/renew', data)
  },

  /**
   * 商家观看广告入驻
   * @param {Object} data - 入驻数据
   * @param {string} data.ad_type - 广告类型 'merchant_join'
   * @param {number} data.watch_duration - 观看时长(秒)
   * @param {string} data.session_id - 会话ID
   */
  joinByAd(data) {
    return request.post('/api/merchant/ads/join', data)
  },

  /**
   * 商家观看广告置顶信息
   * @param {Object} data - 置顶数据
   * @param {string} data.merchant_id - 商家ID
   * @param {string} data.info_id - 信息ID
   * @param {string} data.ad_type - 广告类型 'merchant_top'
   * @param {number} data.watch_duration - 观看时长(秒)
   * @param {string} data.session_id - 会话ID
   */
  topByAd(data) {
    return request.post('/api/merchant/ads/top', data)
  },

  /**
   * 商家观看广告刷新信息
   * @param {Object} data - 刷新数据
   * @param {string} data.merchant_id - 商家ID
   * @param {string} data.info_id - 信息ID
   * @param {string} data.ad_type - 广告类型 'merchant_refresh'
   * @param {number} data.watch_duration - 观看时长(秒)
   * @param {string} data.session_id - 会话ID
   */
  refreshByAd(data) {
    return request.post('/api/merchant/ads/refresh', data)
  },

  /**
   * 获取商家广告观看记录
   * @param {string} merchantId - 商家ID
   * @param {Object} params - 查询参数
   */
  getAdRecords(merchantId, params = {}) {
    return request.get(`/api/merchant/${merchantId}/ads/records`, { params })
  },

  /**
   * 检查商家今日广告观看次数
   * @param {string} merchantId - 商家ID
   * @param {string} adType - 广告类型
   */
  checkTodayAdCount(merchantId, adType = 'merchant_renew') {
    return request.get(`/api/merchant/${merchantId}/ads/check-today`, {
      params: { ad_type: adType }
    })
  },

  /**
   * 获取商家广告配置
   * @param {string} merchantId - 商家ID
   */
  getAdConfig(merchantId) {
    return request.get(`/api/merchant/${merchantId}/ads/config`)
  },

  /**
   * 获取商家入驻信息
   * @param {string} merchantId - 商家ID
   */
  getMerchantInfo(merchantId) {
    return request.get(`/api/merchant/${merchantId}/info`)
  },

  /**
   * 更新商家入驻到期时间
   * @param {string} merchantId - 商家ID
   * @param {Object} data - 更新数据
   * @param {number} data.extend_days - 延长天数
   * @param {string} data.reason - 延长原因
   */
  extendExpireDate(merchantId, data) {
    return request.post(`/api/merchant/${merchantId}/extend`, data)
  },

  /**
   * 获取商家VIP等级信息
   * @param {string} merchantId - 商家ID
   */
  getVipInfo(merchantId) {
    return request.get(`/api/merchant/${merchantId}/vip`)
  },

  /**
   * 获取商家广告统计数据
   * @param {string} merchantId - 商家ID
   * @param {Object} params - 查询参数
   */
  getAdStats(merchantId, params = {}) {
    return request.get(`/api/merchant/${merchantId}/ads/stats`, { params })
  }
}

export default merchantAdApi
