{"version": 3, "file": "CountdownTimer.js", "sources": ["subPackages/activity-showcase/components/CountdownTimer.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7Avc3ViUGFja2FnZXMvYWN0aXZpdHktc2hvd2Nhc2UvY29tcG9uZW50cy9Db3VudGRvd25UaW1lci52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"countdown-container\" :class=\"[`countdown-${type}`]\">\r\n    <view class=\"countdown-label\">{{ label }}</view>\r\n    <view class=\"countdown-timer\">\r\n      <view class=\"time-block\">\r\n        <view class=\"time-value\">{{ days }}</view>\r\n        <view class=\"time-unit\">天</view>\r\n      </view>\r\n      <view class=\"time-separator\">:</view>\r\n      <view class=\"time-block\">\r\n        <view class=\"time-value\">{{ hours }}</view>\r\n        <view class=\"time-unit\">时</view>\r\n      </view>\r\n      <view class=\"time-separator\">:</view>\r\n      <view class=\"time-block\">\r\n        <view class=\"time-value\">{{ minutes }}</view>\r\n        <view class=\"time-unit\">分</view>\r\n      </view>\r\n      <view class=\"time-separator\">:</view>\r\n      <view class=\"time-block\">\r\n        <view class=\"time-value\">{{ seconds }}</view>\r\n        <view class=\"time-unit\">秒</view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'CountdownTimer',\r\n  props: {\r\n    endTime: {\r\n      type: [String, Number, Date],\r\n      required: true\r\n    },\r\n    type: {\r\n      type: String,\r\n      default: 'default',\r\n      validator: (value) => {\r\n        return ['flash', 'group', 'discount', 'coupon', 'default'].includes(value)\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      days: '00',\r\n      hours: '00',\r\n      minutes: '00',\r\n      seconds: '00',\r\n      timer: null,\r\n      isEnded: false\r\n    }\r\n  },\r\n  computed: {\r\n    label() {\r\n      if (this.isEnded) {\r\n        return '活动已结束'\r\n      }\r\n      \r\n      const labelMap = {\r\n        flash: '距结束还剩',\r\n        group: '拼团倒计时',\r\n        discount: '优惠倒计时',\r\n        coupon: '领取倒计时',\r\n        default: '距结束还剩'\r\n      }\r\n      \r\n      return labelMap[this.type] || '距结束还剩'\r\n    }\r\n  },\r\n  mounted() {\r\n    this.startCountdown()\r\n  },\r\n  beforeUnmount() {\r\n    this.clearCountdown()\r\n  },\r\n  methods: {\r\n    startCountdown() {\r\n      this.calculateTime()\r\n      this.timer = setInterval(() => {\r\n        this.calculateTime()\r\n      }, 1000)\r\n    },\r\n    clearCountdown() {\r\n      if (this.timer) {\r\n        clearInterval(this.timer)\r\n        this.timer = null\r\n      }\r\n    },\r\n    calculateTime() {\r\n      const endTime = new Date(this.endTime).getTime()\r\n      const now = new Date().getTime()\r\n      const diff = endTime - now\r\n      \r\n      if (diff <= 0) {\r\n        this.days = '00'\r\n        this.hours = '00'\r\n        this.minutes = '00'\r\n        this.seconds = '00'\r\n        this.isEnded = true\r\n        this.clearCountdown()\r\n        this.$emit('countdown-end')\r\n        return\r\n      }\r\n      \r\n      // 计算天、时、分、秒\r\n      const days = Math.floor(diff / (1000 * 60 * 60 * 24))\r\n      const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))\r\n      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))\r\n      const seconds = Math.floor((diff % (1000 * 60)) / 1000)\r\n      \r\n      // 格式化为两位数\r\n      this.days = days < 10 ? `0${days}` : `${days}`\r\n      this.hours = hours < 10 ? `0${hours}` : `${hours}`\r\n      this.minutes = minutes < 10 ? `0${minutes}` : `${minutes}`\r\n      this.seconds = seconds < 10 ? `0${seconds}` : `${seconds}`\r\n      \r\n      // 添加动画效果，当秒数变化时\r\n      const secondsBlock = document.querySelector('.time-block:last-child .time-value')\r\n      if (secondsBlock) {\r\n        secondsBlock.classList.add('time-pulse')\r\n        setTimeout(() => {\r\n          secondsBlock.classList.remove('time-pulse')\r\n        }, 500)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.countdown-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  margin-bottom: 16rpx;\r\n  \r\n  .countdown-label {\r\n    font-size: 24rpx;\r\n    margin-bottom: 8rpx;\r\n  }\r\n  \r\n  .countdown-timer {\r\n    display: flex;\r\n    align-items: center;\r\n    \r\n    .time-block {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      \r\n      .time-value {\r\n        min-width: 50rpx;\r\n        height: 50rpx;\r\n        line-height: 50rpx;\r\n        text-align: center;\r\n        font-size: 28rpx;\r\n        font-weight: 600;\r\n        background-color: rgba(0, 0, 0, 0.6);\r\n        color: #fff;\r\n        border-radius: 35rpx;\r\n        padding: 0 8rpx;\r\n        transition: transform 0.3s ease;\r\n        \r\n        &.time-pulse {\r\n          animation: pulse 0.5s ease-in-out;\r\n        }\r\n      }\r\n      \r\n      .time-unit {\r\n        font-size: 20rpx;\r\n        color: #666;\r\n        margin-top: 4rpx;\r\n      }\r\n    }\r\n    \r\n    .time-separator {\r\n      margin: 0 4rpx;\r\n      color: #666;\r\n      font-weight: 600;\r\n      font-size: 28rpx;\r\n      padding-bottom: 24rpx;\r\n    }\r\n  }\r\n}\r\n\r\n.countdown-flash {\r\n  .countdown-label {\r\n    color: #FF3B30;\r\n  }\r\n  \r\n  .time-value {\r\n    background: linear-gradient(135deg, #FF5A5F 0%, #FF3B30 100%) !important;\r\n  }\r\n}\r\n\r\n.countdown-group {\r\n  .countdown-label {\r\n    color: #34C759;\r\n  }\r\n  \r\n  .time-value {\r\n    background: linear-gradient(135deg, #4CD964 0%, #34C759 100%) !important;\r\n  }\r\n}\r\n\r\n.countdown-discount {\r\n  .countdown-label {\r\n    color: #5856D6;\r\n  }\r\n  \r\n  .time-value {\r\n    background: linear-gradient(135deg, #5E5CE6 0%, #5856D6 100%) !important;\r\n  }\r\n}\r\n\r\n.countdown-coupon {\r\n  .countdown-label {\r\n    color: #FF9500;\r\n  }\r\n  \r\n  .time-value {\r\n    background: linear-gradient(135deg, #FFCC00 0%, #FF9500 100%) !important;\r\n  }\r\n}\r\n\r\n.countdown-default {\r\n  .countdown-label {\r\n    color: #007AFF;\r\n  }\r\n  \r\n  .time-value {\r\n    background: linear-gradient(135deg, #007AFF 0%, #0A84FF 100%) !important;\r\n  }\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    transform: scale(1.1);\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n  }\r\n}\r\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/components/CountdownTimer.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;AA4BA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,IACL,SAAS;AAAA,MACP,MAAM,CAAC,QAAQ,QAAQ,IAAI;AAAA,MAC3B,UAAU;AAAA,IACX;AAAA,IACD,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,MACT,WAAW,CAAC,UAAU;AACpB,eAAO,CAAC,SAAS,SAAS,YAAY,UAAU,SAAS,EAAE,SAAS,KAAK;AAAA,MAC3E;AAAA,IACF;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,MACP,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,QAAQ;AACN,UAAI,KAAK,SAAS;AAChB,eAAO;AAAA,MACT;AAEA,YAAM,WAAW;AAAA,QACf,OAAO;AAAA,QACP,OAAO;AAAA,QACP,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAEA,aAAO,SAAS,KAAK,IAAI,KAAK;AAAA,IAChC;AAAA,EACD;AAAA,EACD,UAAU;AACR,SAAK,eAAe;AAAA,EACrB;AAAA,EACD,gBAAgB;AACd,SAAK,eAAe;AAAA,EACrB;AAAA,EACD,SAAS;AAAA,IACP,iBAAiB;AACf,WAAK,cAAc;AACnB,WAAK,QAAQ,YAAY,MAAM;AAC7B,aAAK,cAAc;AAAA,MACpB,GAAE,GAAI;AAAA,IACR;AAAA,IACD,iBAAiB;AACf,UAAI,KAAK,OAAO;AACd,sBAAc,KAAK,KAAK;AACxB,aAAK,QAAQ;AAAA,MACf;AAAA,IACD;AAAA,IACD,gBAAgB;AACd,YAAM,UAAU,IAAI,KAAK,KAAK,OAAO,EAAE,QAAQ;AAC/C,YAAM,OAAM,oBAAI,KAAM,GAAC,QAAQ;AAC/B,YAAM,OAAO,UAAU;AAEvB,UAAI,QAAQ,GAAG;AACb,aAAK,OAAO;AACZ,aAAK,QAAQ;AACb,aAAK,UAAU;AACf,aAAK,UAAU;AACf,aAAK,UAAU;AACf,aAAK,eAAe;AACpB,aAAK,MAAM,eAAe;AAC1B;AAAA,MACF;AAGA,YAAM,OAAO,KAAK,MAAM,QAAQ,MAAO,KAAK,KAAK,GAAG;AACpD,YAAM,QAAQ,KAAK,MAAO,QAAQ,MAAO,KAAK,KAAK,OAAQ,MAAO,KAAK,GAAG;AAC1E,YAAM,UAAU,KAAK,MAAO,QAAQ,MAAO,KAAK,OAAQ,MAAO,GAAG;AAClE,YAAM,UAAU,KAAK,MAAO,QAAQ,MAAO,MAAO,GAAI;AAGtD,WAAK,OAAO,OAAO,KAAK,IAAI,IAAI,KAAK,GAAG,IAAI;AAC5C,WAAK,QAAQ,QAAQ,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK;AAChD,WAAK,UAAU,UAAU,KAAK,IAAI,OAAO,KAAK,GAAG,OAAO;AACxD,WAAK,UAAU,UAAU,KAAK,IAAI,OAAO,KAAK,GAAG,OAAO;AAGxD,YAAM,eAAe,SAAS,cAAc,oCAAoC;AAChF,UAAI,cAAc;AAChB,qBAAa,UAAU,IAAI,YAAY;AACvC,mBAAW,MAAM;AACf,uBAAa,UAAU,OAAO,YAAY;AAAA,QAC3C,GAAE,GAAG;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;AC9HA,GAAG,gBAAgB,SAAS;"}