{"version": 3, "file": "publishApi.js", "sources": ["api/publishApi.js"], "sourcesContent": ["import request from '../utils/request.js'\r\n\r\n// 发布信息相关API\r\nexport const publishApi = {\r\n  // 发布信息\r\n  publishPost: async (postData) => {\r\n    try {\r\n      const response = await request.post('/api/posts', postData)\r\n      return response.data\r\n    } catch (error) {\r\n      console.error('发布信息失败:', error)\r\n      throw error\r\n    }\r\n  },\r\n\r\n  // 获取信息列表\r\n  getPosts: async (params = {}) => {\r\n    try {\r\n      const response = await request.get('/api/posts', { params })\r\n      return response.data\r\n    } catch (error) {\r\n      console.error('获取信息列表失败:', error)\r\n      return { data: [], total: 0 }\r\n    }\r\n  },\r\n\r\n  // 获取信息详情\r\n  getPost: async (id) => {\r\n    try {\r\n      const response = await request.get(`/api/posts/${id}`)\r\n      return response.data\r\n    } catch (error) {\r\n      console.error('获取信息详情失败:', error)\r\n      throw error\r\n    }\r\n  },\r\n\r\n  // 更新信息\r\n  updatePost: async (id, postData) => {\r\n    try {\r\n      const response = await request.put(`/api/posts/${id}`, postData)\r\n      return response.data\r\n    } catch (error) {\r\n      console.error('更新信息失败:', error)\r\n      throw error\r\n    }\r\n  },\r\n\r\n  // 删除信息\r\n  deletePost: async (id) => {\r\n    try {\r\n      const response = await request.delete(`/api/posts/${id}`)\r\n      return response.data\r\n    } catch (error) {\r\n      console.error('删除信息失败:', error)\r\n      throw error\r\n    }\r\n  },\r\n\r\n  // 信息置顶\r\n  topPost: async (id, topData) => {\r\n    try {\r\n      const response = await request.post(`/api/posts/${id}/top`, topData)\r\n      return response.data\r\n    } catch (error) {\r\n      console.error('置顶失败:', error)\r\n      throw error\r\n    }\r\n  },\r\n\r\n  // 信息刷新\r\n  refreshPost: async (id) => {\r\n    try {\r\n      const response = await request.post(`/api/posts/${id}/refresh`)\r\n      return response.data\r\n    } catch (error) {\r\n      console.error('刷新失败:', error)\r\n      throw error\r\n    }\r\n  },\r\n\r\n  // 发布带红包\r\n  addRedPacket: async (id, redPacketData) => {\r\n    try {\r\n      const response = await request.post(`/api/posts/${id}/redpacket`, redPacketData)\r\n      return response.data\r\n    } catch (error) {\r\n      console.error('添加红包失败:', error)\r\n      throw error\r\n    }\r\n  },\r\n\r\n  // 领取红包\r\n  claimRedPacket: async (id, claimData) => {\r\n    try {\r\n      const response = await request.post(`/api/posts/${id}/redpacket/claim`, claimData)\r\n      return response.data\r\n    } catch (error) {\r\n      console.error('领取红包失败:', error)\r\n      throw error\r\n    }\r\n  },\r\n\r\n  // 获取配置\r\n  getConfigs: async (category = null) => {\r\n    try {\r\n      const params = category ? { category } : {}\r\n      const response = await request.get('/api/configs', { params })\r\n      return response.data\r\n    } catch (error) {\r\n      console.error('获取配置失败:', error)\r\n      return []\r\n    }\r\n  },\r\n\r\n  // 获取发布配置\r\n  getPublishConfigs: async () => {\r\n    try {\r\n      const response = await request.get('/api/configs/publish')\r\n      return response.data\r\n    } catch (error) {\r\n      console.error('获取发布配置失败:', error)\r\n      return {}\r\n    }\r\n  },\r\n\r\n  // 上传图片\r\n  uploadImage: async (file) => {\r\n    try {\r\n      const formData = new FormData()\r\n      formData.append('image', file)\r\n\r\n      const response = await request.post('/api/upload/image', formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data'\r\n        }\r\n      })\r\n      return response.data\r\n    } catch (error) {\r\n      console.error('上传图片失败:', error)\r\n      throw error\r\n    }\r\n  },\r\n\r\n  // 获取分类信息\r\n  getCategories: async () => {\r\n    try {\r\n      const response = await request.get('/api/categories')\r\n      return response.data\r\n    } catch (error) {\r\n      console.error('获取分类失败:', error)\r\n      return []\r\n    }\r\n  },\r\n\r\n  // 获取首页推荐信息\r\n  getHomePosts: async (params = {}) => {\r\n    try {\r\n      const response = await request.get('/api/posts/home', { params })\r\n      return response.data\r\n    } catch (error) {\r\n      console.error('获取首页信息失败:', error)\r\n      return []\r\n    }\r\n  },\r\n\r\n  // 获取分类信息\r\n  getCategoryPosts: async (category, params = {}) => {\r\n    try {\r\n      const response = await request.get(`/api/posts/category/${category}`, { params })\r\n      return response.data\r\n    } catch (error) {\r\n      console.error('获取分类信息失败:', error)\r\n      return []\r\n    }\r\n  },\r\n\r\n  // 获取置顶信息\r\n  getTopPosts: async (category = null) => {\r\n    try {\r\n      const params = { is_top: true, status: 'approved' }\r\n      if (category) params.category = category\r\n      const response = await request.get('/api/posts', { params })\r\n      return response.data\r\n    } catch (error) {\r\n      console.error('获取置顶信息失败:', error)\r\n      return []\r\n    }\r\n  },\r\n\r\n  // 搜索信息\r\n  searchPosts: async (keyword, params = {}) => {\r\n    try {\r\n      const searchParams = { ...params, keyword }\r\n      const response = await request.get('/api/posts/search', { params: searchParams })\r\n      return response.data\r\n    } catch (error) {\r\n      console.error('搜索信息失败:', error)\r\n      return { data: [], total: 0 }\r\n    }\r\n  },\r\n\r\n  // 获取我的发布\r\n  getMyPosts: async (params = {}) => {\r\n    try {\r\n      const response = await request.get('/api/posts/my', { params })\r\n      return response.data\r\n    } catch (error) {\r\n      console.error('获取我的发布失败:', error)\r\n      return { data: [], total: 0 }\r\n    }\r\n  },\r\n\r\n  // 获取信息统计\r\n  getPostStats: async (id) => {\r\n    try {\r\n      const response = await request.get(`/api/posts/${id}/stats`)\r\n      return response.data\r\n    } catch (error) {\r\n      console.error('获取信息统计失败:', error)\r\n      return {}\r\n    }\r\n  }\r\n}\r\n\r\n// 默认导出\r\nexport default publishApi"], "names": ["request", "uni"], "mappings": ";;;AAGY,MAAC,aAAa;AAAA;AAAA,EAExB,aAAa,OAAO,aAAa;AAC/B,QAAI;AACF,YAAM,WAAW,MAAMA,cAAAA,QAAQ,KAAK,cAAc,QAAQ;AAC1D,aAAO,SAAS;AAAA,IACjB,SAAQ,OAAO;AACdC,oBAAAA,MAAc,MAAA,SAAA,2BAAA,WAAW,KAAK;AAC9B,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,UAAU,OAAO,SAAS,OAAO;AAC/B,QAAI;AACF,YAAM,WAAW,MAAMD,cAAO,QAAC,IAAI,cAAc,EAAE,QAAQ;AAC3D,aAAO,SAAS;AAAA,IACjB,SAAQ,OAAO;AACdC,oBAAAA,gDAAc,aAAa,KAAK;AAChC,aAAO,EAAE,MAAM,IAAI,OAAO,EAAG;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA,EAGD,SAAS,OAAO,OAAO;AACrB,QAAI;AACF,YAAM,WAAW,MAAMD,sBAAQ,IAAI,cAAc,EAAE,EAAE;AACrD,aAAO,SAAS;AAAA,IACjB,SAAQ,OAAO;AACdC,oBAAAA,gDAAc,aAAa,KAAK;AAChC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,YAAY,OAAO,IAAI,aAAa;AAClC,QAAI;AACF,YAAM,WAAW,MAAMD,cAAAA,QAAQ,IAAI,cAAc,EAAE,IAAI,QAAQ;AAC/D,aAAO,SAAS;AAAA,IACjB,SAAQ,OAAO;AACdC,oBAAAA,MAAc,MAAA,SAAA,2BAAA,WAAW,KAAK;AAC9B,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,YAAY,OAAO,OAAO;AACxB,QAAI;AACF,YAAM,WAAW,MAAMD,sBAAQ,OAAO,cAAc,EAAE,EAAE;AACxD,aAAO,SAAS;AAAA,IACjB,SAAQ,OAAO;AACdC,oBAAAA,MAAc,MAAA,SAAA,2BAAA,WAAW,KAAK;AAC9B,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,SAAS,OAAO,IAAI,YAAY;AAC9B,QAAI;AACF,YAAM,WAAW,MAAMD,cAAAA,QAAQ,KAAK,cAAc,EAAE,QAAQ,OAAO;AACnE,aAAO,SAAS;AAAA,IACjB,SAAQ,OAAO;AACdC,oBAAAA,MAAc,MAAA,SAAA,2BAAA,SAAS,KAAK;AAC5B,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,aAAa,OAAO,OAAO;AACzB,QAAI;AACF,YAAM,WAAW,MAAMD,sBAAQ,KAAK,cAAc,EAAE,UAAU;AAC9D,aAAO,SAAS;AAAA,IACjB,SAAQ,OAAO;AACdC,oBAAAA,MAAc,MAAA,SAAA,2BAAA,SAAS,KAAK;AAC5B,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,cAAc,OAAO,IAAI,kBAAkB;AACzC,QAAI;AACF,YAAM,WAAW,MAAMD,cAAAA,QAAQ,KAAK,cAAc,EAAE,cAAc,aAAa;AAC/E,aAAO,SAAS;AAAA,IACjB,SAAQ,OAAO;AACdC,oBAAAA,MAAc,MAAA,SAAA,2BAAA,WAAW,KAAK;AAC9B,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,gBAAgB,OAAO,IAAI,cAAc;AACvC,QAAI;AACF,YAAM,WAAW,MAAMD,cAAAA,QAAQ,KAAK,cAAc,EAAE,oBAAoB,SAAS;AACjF,aAAO,SAAS;AAAA,IACjB,SAAQ,OAAO;AACdC,oBAAAA,MAAc,MAAA,SAAA,2BAAA,WAAW,KAAK;AAC9B,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,YAAY,OAAO,WAAW,SAAS;AACrC,QAAI;AACF,YAAM,SAAS,WAAW,EAAE,SAAU,IAAG,CAAE;AAC3C,YAAM,WAAW,MAAMD,cAAO,QAAC,IAAI,gBAAgB,EAAE,QAAQ;AAC7D,aAAO,SAAS;AAAA,IACjB,SAAQ,OAAO;AACdC,oBAAAA,MAAc,MAAA,SAAA,4BAAA,WAAW,KAAK;AAC9B,aAAO,CAAE;AAAA,IACV;AAAA,EACF;AAAA;AAAA,EAGD,mBAAmB,YAAY;AAC7B,QAAI;AACF,YAAM,WAAW,MAAMD,sBAAQ,IAAI,sBAAsB;AACzD,aAAO,SAAS;AAAA,IACjB,SAAQ,OAAO;AACdC,oBAAAA,iDAAc,aAAa,KAAK;AAChC,aAAO,CAAE;AAAA,IACV;AAAA,EACF;AAAA;AAAA,EAGD,aAAa,OAAO,SAAS;AAC3B,QAAI;AACF,YAAM,WAAW,IAAI,SAAU;AAC/B,eAAS,OAAO,SAAS,IAAI;AAE7B,YAAM,WAAW,MAAMD,cAAAA,QAAQ,KAAK,qBAAqB,UAAU;AAAA,QACjE,SAAS;AAAA,UACP,gBAAgB;AAAA,QACjB;AAAA,MACT,CAAO;AACD,aAAO,SAAS;AAAA,IACjB,SAAQ,OAAO;AACdC,oBAAAA,MAAc,MAAA,SAAA,4BAAA,WAAW,KAAK;AAC9B,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,eAAe,YAAY;AACzB,QAAI;AACF,YAAM,WAAW,MAAMD,sBAAQ,IAAI,iBAAiB;AACpD,aAAO,SAAS;AAAA,IACjB,SAAQ,OAAO;AACdC,oBAAAA,MAAc,MAAA,SAAA,4BAAA,WAAW,KAAK;AAC9B,aAAO,CAAE;AAAA,IACV;AAAA,EACF;AAAA;AAAA,EAGD,cAAc,OAAO,SAAS,OAAO;AACnC,QAAI;AACF,YAAM,WAAW,MAAMD,cAAO,QAAC,IAAI,mBAAmB,EAAE,QAAQ;AAChE,aAAO,SAAS;AAAA,IACjB,SAAQ,OAAO;AACdC,oBAAAA,iDAAc,aAAa,KAAK;AAChC,aAAO,CAAE;AAAA,IACV;AAAA,EACF;AAAA;AAAA,EAGD,kBAAkB,OAAO,UAAU,SAAS,OAAO;AACjD,QAAI;AACF,YAAM,WAAW,MAAMD,cAAO,QAAC,IAAI,uBAAuB,QAAQ,IAAI,EAAE,QAAQ;AAChF,aAAO,SAAS;AAAA,IACjB,SAAQ,OAAO;AACdC,oBAAAA,iDAAc,aAAa,KAAK;AAChC,aAAO,CAAE;AAAA,IACV;AAAA,EACF;AAAA;AAAA,EAGD,aAAa,OAAO,WAAW,SAAS;AACtC,QAAI;AACF,YAAM,SAAS,EAAE,QAAQ,MAAM,QAAQ,WAAY;AACnD,UAAI;AAAU,eAAO,WAAW;AAChC,YAAM,WAAW,MAAMD,cAAO,QAAC,IAAI,cAAc,EAAE,QAAQ;AAC3D,aAAO,SAAS;AAAA,IACjB,SAAQ,OAAO;AACdC,oBAAAA,iDAAc,aAAa,KAAK;AAChC,aAAO,CAAE;AAAA,IACV;AAAA,EACF;AAAA;AAAA,EAGD,aAAa,OAAO,SAAS,SAAS,OAAO;AAC3C,QAAI;AACF,YAAM,eAAe,EAAE,GAAG,QAAQ,QAAS;AAC3C,YAAM,WAAW,MAAMD,sBAAQ,IAAI,qBAAqB,EAAE,QAAQ,cAAc;AAChF,aAAO,SAAS;AAAA,IACjB,SAAQ,OAAO;AACdC,oBAAAA,MAAc,MAAA,SAAA,4BAAA,WAAW,KAAK;AAC9B,aAAO,EAAE,MAAM,IAAI,OAAO,EAAG;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA,EAGD,YAAY,OAAO,SAAS,OAAO;AACjC,QAAI;AACF,YAAM,WAAW,MAAMD,cAAO,QAAC,IAAI,iBAAiB,EAAE,QAAQ;AAC9D,aAAO,SAAS;AAAA,IACjB,SAAQ,OAAO;AACdC,oBAAAA,iDAAc,aAAa,KAAK;AAChC,aAAO,EAAE,MAAM,IAAI,OAAO,EAAG;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA,EAGD,cAAc,OAAO,OAAO;AAC1B,QAAI;AACF,YAAM,WAAW,MAAMD,sBAAQ,IAAI,cAAc,EAAE,QAAQ;AAC3D,aAAO,SAAS;AAAA,IACjB,SAAQ,OAAO;AACdC,oBAAAA,iDAAc,aAAa,KAAK;AAChC,aAAO,CAAE;AAAA,IACV;AAAA,EACF;AACH;;"}