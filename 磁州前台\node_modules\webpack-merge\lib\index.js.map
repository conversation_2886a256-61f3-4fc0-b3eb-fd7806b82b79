{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,iCAAsE;AAEtE,6CAAuC;AACvC,yDAAiD;AACjD,iCAQiB;AACjB,mCAA8B;AAsHrB,iBAtHF,gBAAM,CAsHE;AA/Gf,sCAAsC;AACtC,SAAgB,gBAAgB,CAAC,OAAiB;IAChD,OAAO;QAAC,oBAA8B;aAA9B,UAA8B,EAA9B,qBAA8B,EAA9B,IAA8B;YAA9B,+BAA8B;;QACpC,OAAA,kBAAS,yBAAC,EAAE,GAAK,UAAU,GAAE,qBAAU,CAAC,OAAO,CAAC;IAAhD,CAAiD,CAAC;AACtD,CAAC;AAHD,4CAGC;AAED,SAAS,KAAK;IAAC,iBAA2B;SAA3B,UAA2B,EAA3B,qBAA2B,EAA3B,IAA2B;QAA3B,4BAA2B;;IACxC,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;QACxB,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;YAC7B,OAAO,kBAAS,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,qBAAU,EAAE,CAAC,CAAC;SAChD;QAED,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;KACnB;IAED,OAAO,kBAAS,yBAAC,EAAE,GAAK,OAAO,GAAE,qBAAU,EAAE,IAAE;AACjD,CAAC;AAEY,QAAA,KAAK,GAAG,gBAAgB,CAAC;IACpC,cAAc,EAAE,UAAC,CAAM,EAAE,CAAM,EAAE,GAAQ;QACvC,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACvC,OAAO,kBAAS,CAAC,CAAC,EAAE,CAAC,EAAE,8BAAU,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;SACxD;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF,CAAC,CAAC;AAEH,8BAA8B;AACjB,QAAA,QAAQ,GAAG;IAAC,iBAA2B;SAA3B,UAA2B,EAA3B,qBAA2B,EAA3B,IAA2B;QAA3B,4BAA2B;;IAClD,OAAA,eAAM,CAAC,KAAK,wBAAI,OAAO,GAAE;AAAzB,CAAyB,CAAC;AAE5B,qDAAqD;AACrD,kDAAkD;AACrC,QAAA,QAAQ,GAAG,UAAC,KAA2B;IAA3B,sBAAA,EAAA,UAA2B;IAClD,OAAA,gBAAgB,CAAC;QACf,cAAc,EAAE,cAAc,CAAC,KAAK,CAAC;QACrC,eAAe,EAAE,eAAe,CAAC,KAAK,CAAC;KACxC,CAAC;AAHF,CAGE,CAAC;AACQ,QAAA,kBAAkB,GAAG,UAAC,KAA2B;IAA3B,sBAAA,EAAA,UAA2B;IAC5D,OAAA,gBAAgB,CAAC;QACf,cAAc,EAAE,UAAC,CAAC,EAAE,CAAC,EAAE,GAAQ;YAC7B,IAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAE3C,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE;gBAClB,QAAQ,KAAK,CAAC,GAAG,CAAC,EAAE;oBAClB,KAAK,SAAS;wBACZ,gBACK,uBAAc,CACf,CAAC,EACD,CAAC;wBACD,4BAA4B;wBAC5B,UAAC,OAAc,EAAE,QAAe;4BAC9B,OAAA,8BAAU,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC;wBAAzC,CAAyC,CAC5C,EACE,CAAC,EACJ;oBACJ,KAAK,SAAS;wBACZ,OAAO,CAAC,CAAC;oBACX;wBACE,SAAS;wBACT,OAAO,kBAAS,CAAC,CAAC,EAAE,CAAC,EAAE,8BAAU,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;iBAC7D;aACF;YAED,OAAO,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QAC1C,CAAC;QACD,eAAe,EAAE,eAAe,CAAC,KAAK,CAAC;KACxC,CAAC;AA5BF,CA4BE,CAAC;AAEL,SAAS,cAAc,CAAC,KAAsB;IAC5C,OAAO,UAAC,CAAM,EAAE,CAAM,EAAE,GAAQ;QAC9B,QAAQ,KAAK,CAAC,GAAG,CAAC,EAAE;YAClB,KAAK,qBAAa,CAAC,OAAO;gBACxB,gBAAW,CAAC,EAAK,CAAC,EAAE;YACtB,KAAK,qBAAa,CAAC,OAAO;gBACxB,OAAO,CAAC,CAAC;YACX,KAAK,qBAAa,CAAC,MAAM,CAAC;YAC1B;gBACE,SAAS;gBACT,OAAO,KAAK,CAAC;SAChB;IACH,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,eAAe,CAAC,KAAsB;IAC7C,OAAO,UAAC,CAAM,EAAE,CAAM,EAAE,GAAQ;QAC9B,QAAQ,KAAK,CAAC,GAAG,CAAC,EAAE;YAClB,KAAK,qBAAa,CAAC,OAAO;gBACxB,OAAO,kBAAS,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,qBAAU,EAAE,CAAC,CAAC;YAC3C,KAAK,qBAAa,CAAC,OAAO;gBACxB,OAAO,CAAC,CAAC;YACX,KAAK,qBAAa,CAAC,MAAM,CAAC;YAC1B;gBACE,OAAO,KAAK,CAAC;SAChB;IACH,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,MAAM,CAAC,GAAQ;IACtB,OAAO,CACL;QACE,iBAAS,CAAC,UAAU;QACpB,iBAAS,CAAC,OAAO;QACjB,iBAAS,CAAC,WAAW;QACrB,iBAAS,CAAC,KAAK;KAChB,CAAC,OAAO,CAAC,GAAgB,CAAC,IAAI,CAAC,CACjC,CAAC;AACJ,CAAC;AAED,kBAAe,KAAK,CAAC"}