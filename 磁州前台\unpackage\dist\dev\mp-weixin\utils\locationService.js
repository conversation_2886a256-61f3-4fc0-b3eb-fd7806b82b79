"use strict";
const common_vendor = require("../common/vendor.js");
const utils_permission = require("./permission.js");
const utils_location = require("./location.js");
let currentLocation = null;
const DEFAULT_LOCATION = {
  latitude: 36.313076,
  longitude: 114.347312,
  province: "河北省",
  city: "邯郸市",
  district: "磁县",
  address: "河北省邯郸市磁县",
  location: "河北省 邯郸市 磁县"
};
const getCurrentLocation = () => {
  common_vendor.index.__f__("warn", "at utils/locationService.js:49", "getCurrentLocation 方法已弃用，请使用 utils/location.js 中的 getUserLocation 方法替代");
  if (currentLocation) {
    return currentLocation;
  }
  const cachedLocation = common_vendor.index.getStorageSync("user_location");
  if (cachedLocation) {
    currentLocation = cachedLocation;
    return cachedLocation;
  }
  return DEFAULT_LOCATION;
};
const refreshLocation = async () => {
  common_vendor.index.__f__("warn", "at utils/locationService.js:70", "refreshLocation 方法已弃用，请使用 utils/location.js 中的 getUserLocation 方法替代");
  try {
    common_vendor.index.showToast({
      title: "更新位置...",
      icon: "loading",
      duration: 1e3,
      mask: false
    });
    const permissionStatus = await utils_permission.checkLocationPermission();
    if (permissionStatus === "authorized") {
      try {
        const location = await utils_location.getUserLocation({
          type: "gcj02",
          timeout: 3e3
        });
        const newLocation = {
          latitude: location.latitude,
          longitude: location.longitude,
          // 理想情况下应通过地图API获取以下信息
          // 这里简化处理，使用默认数据
          province: DEFAULT_LOCATION.province,
          city: DEFAULT_LOCATION.city,
          district: DEFAULT_LOCATION.district,
          address: DEFAULT_LOCATION.address,
          location: DEFAULT_LOCATION.location
        };
        currentLocation = newLocation;
        common_vendor.index.setStorageSync("user_location", newLocation);
        common_vendor.index.$emit("location_updated", newLocation);
        common_vendor.index.showToast({
          title: "位置已更新",
          icon: "success",
          duration: 1500
        });
        return newLocation;
      } catch (error) {
        common_vendor.index.__f__("error", "at utils/locationService.js:121", "获取位置失败:", error);
        const fallbackLocation = currentLocation || DEFAULT_LOCATION;
        return fallbackLocation;
      }
    } else {
      common_vendor.index.showModal({
        title: "位置权限未开启",
        content: "需要位置权限才能刷新位置，是否前往设置开启？",
        confirmText: "去设置",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            utils_permission.openSettings();
          }
        }
      });
      return currentLocation || DEFAULT_LOCATION;
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/locationService.js:147", "刷新位置信息失败:", error);
    common_vendor.index.showToast({
      title: "更新位置失败",
      icon: "none",
      duration: 1500
    });
    return currentLocation || DEFAULT_LOCATION;
  }
};
exports.getCurrentLocation = getCurrentLocation;
exports.refreshLocation = refreshLocation;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/locationService.js.map
