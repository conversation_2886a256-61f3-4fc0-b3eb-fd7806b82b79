{"version": 3, "file": "driver-verification.js", "sources": ["carpool-package/pages/carpool/my/driver-verification.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/Y2FycG9vbC1wYWNrYWdlXHBhZ2VzXGNhcnBvb2xcbXlcZHJpdmVyLXZlcmlmaWNhdGlvbi52dWU"], "sourcesContent": ["<template>\n  <view class=\"verification-container\">\n    <!-- 自定义导航栏 - 简化版本 -->\n    <view class=\"nav-bar\">\n      <view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\n      <view class=\"nav-content\" style=\"height: calc(50px - 4px);\">\n        <view class=\"back-btn\" @click=\"goBack\">\n          <image src=\"/static/images/tabbar/最新返回键.png\" class=\"back-icon\"></image>\n        </view>\n        <view class=\"nav-title\">司机认证</view>\n        <view class=\"right-placeholder\"></view>\n      </view>\n    </view>\n    \n    <!-- 认证状态卡片 -->\n    <view class=\"status-card\" v-if=\"verificationStatus !== 'none'\">\n      <view class=\"status-icon\" :class=\"verificationStatus\">\n        <image \n          :src=\"verificationStatus === 'pending' ? '/static/images/icons/pending.png' : \n                verificationStatus === 'verified' ? '/static/images/icons/verified.png' : \n                '/static/images/icons/rejected.png'\" \n          class=\"status-img\">\n        </image>\n      </view>\n      <view class=\"status-text\">\n        {{ verificationStatus === 'pending' ? '认证审核中' : \n           verificationStatus === 'verified' ? '认证已通过' : '认证未通过' }}\n      </view>\n      <view class=\"status-desc\">\n        {{ verificationStatus === 'pending' ? '您的认证申请正在审核中，请耐心等待' : \n           verificationStatus === 'verified' ? '您已通过司机认证，可以发布车找人/车找货信息' : \n           '认证未通过，原因：' + rejectReason }}\n      </view>\n      \n      <button class=\"reapply-btn\" v-if=\"verificationStatus === 'rejected'\" @click=\"resetForm\">重新申请</button>\n    </view>\n    \n    <!-- 认证表单 -->\n    <view class=\"verification-form\" v-if=\"verificationStatus === 'none' || (verificationStatus === 'rejected' && showForm)\">\n      <view class=\"form-title\">司机认证</view>\n      <view class=\"form-subtitle\">请填写真实信息，审核通过后可发布车找人/车找货信息</view>\n      \n      <view class=\"form-group\">\n        <view class=\"form-label\">真实姓名</view>\n        <input type=\"text\" class=\"form-input\" v-model=\"formData.realName\" placeholder=\"请输入真实姓名\" />\n      </view>\n      \n      <view class=\"form-group\">\n        <view class=\"form-label\">身份证号</view>\n        <input type=\"idcard\" class=\"form-input\" v-model=\"formData.idCard\" placeholder=\"请输入身份证号\" maxlength=\"18\" />\n      </view>\n      \n      <view class=\"form-group\">\n        <view class=\"form-label\">驾驶证号</view>\n        <input type=\"text\" class=\"form-input\" v-model=\"formData.driverLicense\" placeholder=\"请输入驾驶证号\" />\n      </view>\n      \n      <view class=\"form-group\">\n        <view class=\"form-label\">驾驶证有效期</view>\n        <view class=\"date-picker\" @click=\"openDatePicker\">\n          <text class=\"date-text\">{{ formData.licenseExpireDate || '请选择有效期' }}</text>\n          <image src=\"/static/images/icons/calendar.png\" class=\"date-icon\"></image>\n        </view>\n      </view>\n      \n      <view class=\"form-group\">\n        <view class=\"form-label\">车牌号</view>\n        <input type=\"text\" class=\"form-input\" v-model=\"formData.carNumber\" placeholder=\"请输入车牌号\" />\n      </view>\n      \n      <view class=\"form-group\">\n        <view class=\"form-label\">车辆品牌型号</view>\n        <input type=\"text\" class=\"form-input\" v-model=\"formData.carModel\" placeholder=\"例如：本田雅阁 2022款\" />\n      </view>\n      \n      <view class=\"form-group\">\n        <view class=\"form-label\">车辆颜色</view>\n        <input type=\"text\" class=\"form-input\" v-model=\"formData.carColor\" placeholder=\"请输入车辆颜色\" />\n      </view>\n      \n      <view class=\"form-group\">\n        <view class=\"form-label\">车辆注册日期</view>\n        <view class=\"date-picker\" @click=\"openRegDatePicker\">\n          <text class=\"date-text\">{{ formData.carRegDate || '请选择注册日期' }}</text>\n          <image src=\"/static/images/icons/calendar.png\" class=\"date-icon\"></image>\n        </view>\n      </view>\n      \n      <view class=\"upload-section\">\n        <view class=\"upload-title\">证件照片上传</view>\n        \n        <view class=\"upload-item\">\n          <view class=\"upload-label\">身份证正面照</view>\n          <view class=\"upload-box\" @click=\"chooseImage('idCardFront')\" v-if=\"!formData.idCardFront\">\n            <image src=\"/static/images/icons/upload.png\" class=\"upload-icon\"></image>\n            <text class=\"upload-text\">点击上传</text>\n          </view>\n          <view class=\"image-preview\" v-else>\n            <image :src=\"formData.idCardFront\" mode=\"aspectFill\" class=\"preview-image\"></image>\n            <view class=\"delete-btn\" @click.stop=\"deleteImage('idCardFront')\">\n              <image src=\"/static/images/icons/delete.png\" class=\"delete-icon\"></image>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"upload-item\">\n          <view class=\"upload-label\">身份证背面照</view>\n          <view class=\"upload-box\" @click=\"chooseImage('idCardBack')\" v-if=\"!formData.idCardBack\">\n            <image src=\"/static/images/icons/upload.png\" class=\"upload-icon\"></image>\n            <text class=\"upload-text\">点击上传</text>\n          </view>\n          <view class=\"image-preview\" v-else>\n            <image :src=\"formData.idCardBack\" mode=\"aspectFill\" class=\"preview-image\"></image>\n            <view class=\"delete-btn\" @click.stop=\"deleteImage('idCardBack')\">\n              <image src=\"/static/images/icons/delete.png\" class=\"delete-icon\"></image>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"upload-item\">\n          <view class=\"upload-label\">驾驶证照片</view>\n          <view class=\"upload-box\" @click=\"chooseImage('driverLicenseImg')\" v-if=\"!formData.driverLicenseImg\">\n            <image src=\"/static/images/icons/upload.png\" class=\"upload-icon\"></image>\n            <text class=\"upload-text\">点击上传</text>\n          </view>\n          <view class=\"image-preview\" v-else>\n            <image :src=\"formData.driverLicenseImg\" mode=\"aspectFill\" class=\"preview-image\"></image>\n            <view class=\"delete-btn\" @click.stop=\"deleteImage('driverLicenseImg')\">\n              <image src=\"/static/images/icons/delete.png\" class=\"delete-icon\"></image>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"upload-item\">\n          <view class=\"upload-label\">行驶证照片</view>\n          <view class=\"upload-box\" @click=\"chooseImage('vehicleLicenseImg')\" v-if=\"!formData.vehicleLicenseImg\">\n            <image src=\"/static/images/icons/upload.png\" class=\"upload-icon\"></image>\n            <text class=\"upload-text\">点击上传</text>\n          </view>\n          <view class=\"image-preview\" v-else>\n            <image :src=\"formData.vehicleLicenseImg\" mode=\"aspectFill\" class=\"preview-image\"></image>\n            <view class=\"delete-btn\" @click.stop=\"deleteImage('vehicleLicenseImg')\">\n              <image src=\"/static/images/icons/delete.png\" class=\"delete-icon\"></image>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"agreement\">\n        <checkbox-group @change=\"checkboxChange\">\n          <label class=\"checkbox-label\">\n            <checkbox :checked=\"formData.agreement\" color=\"#0A84FF\" style=\"transform:scale(0.7)\" />\n            <text class=\"agreement-text\">我已阅读并同意</text>\n            <text class=\"agreement-link\" @click=\"viewAgreement\">《司机认证服务协议》</text>\n          </label>\n        </checkbox-group>\n      </view>\n      \n      <button class=\"submit-btn\" :disabled=\"!isFormValid\" @click=\"submitVerification\">提交认证</button>\n    </view>\n    \n    <!-- 底部安全区域 -->\n    <view class=\"safe-area-bottom\"></view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue';\n\n// 认证状态\nconst verificationStatus = ref('none'); // 'none', 'pending', 'verified', 'rejected'\nconst rejectReason = ref('');\nconst showForm = ref(false);\n\n// 表单数据\nconst formData = ref({\n        realName: '',\n        idCard: '',\n        driverLicense: '',\n        licenseExpireDate: '',\n        carNumber: '',\n        carModel: '',\n        carColor: '',\n        carRegDate: '',\n        idCardFront: '',\n        idCardBack: '',\n        driverLicenseImg: '',\n        vehicleLicenseImg: '',\n        agreement: false\n});\n\n// 计算表单是否有效\nconst isFormValid = computed(() => {\n      return (\n    formData.value.realName &&\n    formData.value.idCard &&\n    formData.value.idCard.length === 18 &&\n    formData.value.driverLicense &&\n    formData.value.licenseExpireDate &&\n    formData.value.carNumber &&\n    formData.value.carModel &&\n    formData.value.carColor &&\n    formData.value.carRegDate &&\n    formData.value.idCardFront &&\n    formData.value.idCardBack &&\n    formData.value.driverLicenseImg &&\n    formData.value.vehicleLicenseImg &&\n    formData.value.agreement\n      );\n});\n\n// 页面加载时检查认证状态\nonMounted(() => {\n  checkVerificationStatus();\n});\n\n    // 返回上一页\nconst goBack = () => {\n      uni.navigateBack();\n};\n    \n    // 检查认证状态\nconst checkVerificationStatus = () => {\n      // 模拟API调用\n      // 实际应用中应该从服务器获取认证状态\n      setTimeout(() => {\n        // 模拟数据\n        // 可以根据需要更改为 'none', 'pending', 'verified', 'rejected'\n    verificationStatus.value = 'none';\n    rejectReason.value = '提供的证件信息不清晰，请重新上传清晰照片';\n      }, 500);\n};\n    \n    // 重置表单，重新申请\nconst resetForm = () => {\n  showForm.value = true;\n  formData.value = {\n        realName: '',\n        idCard: '',\n        driverLicense: '',\n        licenseExpireDate: '',\n        carNumber: '',\n        carModel: '',\n        carColor: '',\n        carRegDate: '',\n        idCardFront: '',\n        idCardBack: '',\n        driverLicenseImg: '',\n        vehicleLicenseImg: '',\n        agreement: false\n      };\n};\n    \n    // 打开日期选择器 - 驾驶证有效期\nconst openDatePicker = () => {\n      const now = new Date();\n      const year = now.getFullYear();\n      const month = now.getMonth() + 1;\n      const day = now.getDate();\n      \n      uni.showDatePicker({\n    date: formData.value.licenseExpireDate || `${year}-${month}-${day}`,\n        success: (res) => {\n      formData.value.licenseExpireDate = res.date;\n        }\n      });\n};\n    \n    // 打开日期选择器 - 车辆注册日期\nconst openRegDatePicker = () => {\n      const now = new Date();\n      const year = now.getFullYear();\n      const month = now.getMonth() + 1;\n      const day = now.getDate();\n      \n      uni.showDatePicker({\n    date: formData.value.carRegDate || `${year}-${month}-${day}`,\n        success: (res) => {\n      formData.value.carRegDate = res.date;\n        }\n      });\n};\n    \n    // 选择图片\nconst chooseImage = (field) => {\n      uni.chooseImage({\n        count: 1,\n        sizeType: ['compressed'],\n        sourceType: ['album', 'camera'],\n        success: (res) => {\n      formData.value[field] = res.tempFilePaths[0];\n        }\n      });\n};\n    \n    // 删除图片\nconst deleteImage = (field) => {\n  formData.value[field] = '';\n};\n    \n    // 复选框变化\nconst checkboxChange = (e) => {\n  formData.value.agreement = e.detail.value.length > 0;\n};\n    \n    // 查看协议\nconst viewAgreement = () => {\n      uni.showModal({\n        title: '司机认证服务协议',\n        content: '司机认证服务协议内容...',\n        showCancel: false\n      });\n};\n    \n    // 提交认证\nconst submitVerification = () => {\n  if (!isFormValid.value) {\n        uni.showToast({\n          title: '请完善所有必填信息',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      uni.showLoading({\n        title: '提交中...'\n      });\n      \n      // 模拟API调用\n      setTimeout(() => {\n        uni.hideLoading();\n        \n        // 模拟成功\n        uni.showToast({\n          title: '提交成功，等待审核',\n          icon: 'success'\n        });\n        \n        // 更新状态\n    verificationStatus.value = 'pending';\n    showForm.value = false;\n        \n        // 延迟返回\n        setTimeout(() => {\n          uni.navigateBack();\n        }, 1500);\n      }, 2000);\n};\n</script>\n\n<style lang=\"scss\">\n/* 新导航栏样式 */\n.nav-bar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  background-color: #1677FF;\n  z-index: 999;\n  /* 标题栏高度再减少5px */\n  height: calc(var(--status-bar-height) + 82px);\n  width: 100vw;\n}\n\n.status-bar {\n  width: 100%;\n}\n\n.nav-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 49px;\n  padding: 0 20px 8px; /* 左右内边距确保按钮与边缘有足够距离 */\n  margin-top: 10px; /* 增加顶部间距，使内容整体下移 */\n}\n\n.back-btn {\n  width: 44px;\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: flex-start; /* 左对齐 */\n  position: relative;\n  top: 29px; /* 再次下移3px */\n}\n\n.back-icon {\n  width: 24px;\n  height: 24px;\n  filter: brightness(0) invert(1);\n}\n\n.nav-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  color: #ffffff;\n  transform: translateX(8px) translateY(29px); /* 标题向右8px并下移29px */\n  /* 确保文本可见 */\n  display: block;\n  position: relative;\n  z-index: 1001;\n  text-shadow: 0 1px 1px rgba(0,0,0,0.2); /* 添加文字阴影提高可读性 */\n}\n\n.right-placeholder {\n  width: 44px;\n  height: 44px;\n  display: flex;\n  justify-content: flex-end; /* 右对齐 */\n  position: relative;\n  top: 29px; /* 与返回键保持一致的偏移 */\n}\n\n/* 主容器样式调整 */\n.verification-container {\n  min-height: 100vh;\n  background-color: #F5F8FC;\n  position: relative;\n  padding-top: calc(var(--status-bar-height) + 82px); /* 再次调整后的导航栏高度 */\n  padding-bottom: 40rpx;\n}\n\n/* 认证状态卡片 */\n.status-card {\n  margin: 30rpx;\n  background-color: #FFFFFF;\n  border-radius: 16rpx;\n  padding: 40rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.status-icon {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 20rpx;\n}\n\n.status-icon.pending {\n  background-color: #FF9F0A;\n}\n\n.status-icon.verified {\n  background-color: #34C759;\n}\n\n.status-icon.rejected {\n  background-color: #FF3B30;\n}\n\n.status-img {\n  width: 60rpx;\n  height: 60rpx;\n}\n\n.status-text {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333333;\n  margin-bottom: 16rpx;\n}\n\n.status-desc {\n  font-size: 28rpx;\n  color: #666666;\n  text-align: center;\n  line-height: 1.5;\n}\n\n.reapply-btn {\n  margin-top: 30rpx;\n  background-color: #0A84FF;\n  color: #FFFFFF;\n  font-size: 28rpx;\n  padding: 12rpx 40rpx;\n  border-radius: 30rpx;\n  line-height: 1.5;\n}\n\n/* 认证表单 */\n.verification-form {\n  margin: 30rpx;\n  background-color: #FFFFFF;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.form-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333333;\n  margin-bottom: 10rpx;\n}\n\n.form-subtitle {\n  font-size: 24rpx;\n  color: #8E8E93;\n  margin-bottom: 30rpx;\n}\n\n.form-group {\n  margin-bottom: 24rpx;\n}\n\n.form-label {\n  font-size: 28rpx;\n  color: #333333;\n  margin-bottom: 10rpx;\n}\n\n.form-input {\n  width: 100%;\n  height: 80rpx;\n  background-color: #F5F5F5;\n  border-radius: 8rpx;\n  padding: 0 20rpx;\n  font-size: 28rpx;\n  color: #333333;\n}\n\n.date-picker {\n  width: 100%;\n  height: 80rpx;\n  background-color: #F5F5F5;\n  border-radius: 8rpx;\n  padding: 0 20rpx;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.date-text {\n  font-size: 28rpx;\n  color: #333333;\n}\n\n.date-icon {\n  width: 32rpx;\n  height: 32rpx;\n}\n\n/* 上传部分 */\n.upload-section {\n  margin-top: 30rpx;\n  margin-bottom: 30rpx;\n}\n\n.upload-title {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #333333;\n  margin-bottom: 20rpx;\n}\n\n.upload-item {\n  margin-bottom: 24rpx;\n}\n\n.upload-label {\n  font-size: 28rpx;\n  color: #333333;\n  margin-bottom: 10rpx;\n}\n\n.upload-box {\n  width: 100%;\n  height: 200rpx;\n  background-color: #F5F5F5;\n  border-radius: 8rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.upload-icon {\n  width: 48rpx;\n  height: 48rpx;\n  margin-bottom: 10rpx;\n}\n\n.upload-text {\n  font-size: 26rpx;\n  color: #8E8E93;\n}\n\n.image-preview {\n  width: 100%;\n  height: 200rpx;\n  border-radius: 8rpx;\n  position: relative;\n  overflow: hidden;\n}\n\n.preview-image {\n  width: 100%;\n  height: 100%;\n}\n\n.delete-btn {\n  position: absolute;\n  top: 10rpx;\n  right: 10rpx;\n  width: 40rpx;\n  height: 40rpx;\n  background-color: rgba(0, 0, 0, 0.5);\n  border-radius: 20rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.delete-icon {\n  width: 24rpx;\n  height: 24rpx;\n}\n\n/* 协议 */\n.agreement {\n  margin-bottom: 30rpx;\n}\n\n.checkbox-label {\n  display: flex;\n  align-items: center;\n}\n\n.agreement-text {\n  font-size: 26rpx;\n  color: #666666;\n}\n\n.agreement-link {\n  font-size: 26rpx;\n  color: #0A84FF;\n}\n\n/* 提交按钮 */\n.submit-btn {\n  width: 100%;\n  height: 90rpx;\n  background: linear-gradient(135deg, #0A84FF, #0040DD);\n  color: #FFFFFF;\n  font-size: 32rpx;\n  font-weight: 500;\n  border-radius: 45rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.submit-btn[disabled] {\n  background: #CCCCCC;\n  color: #FFFFFF;\n}\n\n.safe-area-bottom {\n  height: env(safe-area-inset-bottom);\n  width: 100%;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/carpool-package/pages/carpool/my/driver-verification.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onMounted", "uni", "MiniProgramPage"], "mappings": ";;;;;;AA0KA,UAAM,qBAAqBA,cAAAA,IAAI,MAAM;AACrC,UAAM,eAAeA,cAAAA,IAAI,EAAE;AAC3B,UAAM,WAAWA,cAAAA,IAAI,KAAK;AAG1B,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACb,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,eAAe;AAAA,MACf,mBAAmB;AAAA,MACnB,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,MACnB,WAAW;AAAA,IACnB,CAAC;AAGD,UAAM,cAAcC,cAAQ,SAAC,MAAM;AAC7B,aACF,SAAS,MAAM,YACf,SAAS,MAAM,UACf,SAAS,MAAM,OAAO,WAAW,MACjC,SAAS,MAAM,iBACf,SAAS,MAAM,qBACf,SAAS,MAAM,aACf,SAAS,MAAM,YACf,SAAS,MAAM,YACf,SAAS,MAAM,cACf,SAAS,MAAM,eACf,SAAS,MAAM,cACf,SAAS,MAAM,oBACf,SAAS,MAAM,qBACf,SAAS,MAAM;AAAA,IAEnB,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AACd;IACF,CAAC;AAGD,UAAM,SAAS,MAAM;AACfC,oBAAG,MAAC,aAAY;AAAA,IACtB;AAGA,UAAM,0BAA0B,MAAM;AAGhC,iBAAW,MAAM;AAGnB,2BAAmB,QAAQ;AAC3B,qBAAa,QAAQ;AAAA,MAClB,GAAE,GAAG;AAAA,IACZ;AAGA,UAAM,YAAY,MAAM;AACtB,eAAS,QAAQ;AACjB,eAAS,QAAQ;AAAA,QACX,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,eAAe;AAAA,QACf,mBAAmB;AAAA,QACnB,WAAW;AAAA,QACX,UAAU;AAAA,QACV,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,WAAW;AAAA,MACnB;AAAA,IACA;AAGA,UAAM,iBAAiB,MAAM;AACvB,YAAM,MAAM,oBAAI;AAChB,YAAM,OAAO,IAAI;AACjB,YAAM,QAAQ,IAAI,SAAQ,IAAK;AAC/B,YAAM,MAAM,IAAI;AAEhBA,oBAAAA,MAAI,eAAe;AAAA,QACrB,MAAM,SAAS,MAAM,qBAAqB,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,QAC7D,SAAS,CAAC,QAAQ;AACpB,mBAAS,MAAM,oBAAoB,IAAI;AAAA,QACpC;AAAA,MACT,CAAO;AAAA,IACP;AAGA,UAAM,oBAAoB,MAAM;AAC1B,YAAM,MAAM,oBAAI;AAChB,YAAM,OAAO,IAAI;AACjB,YAAM,QAAQ,IAAI,SAAQ,IAAK;AAC/B,YAAM,MAAM,IAAI;AAEhBA,oBAAAA,MAAI,eAAe;AAAA,QACrB,MAAM,SAAS,MAAM,cAAc,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,QACtD,SAAS,CAAC,QAAQ;AACpB,mBAAS,MAAM,aAAa,IAAI;AAAA,QAC7B;AAAA,MACT,CAAO;AAAA,IACP;AAGA,UAAM,cAAc,CAAC,UAAU;AACzBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AACpB,mBAAS,MAAM,KAAK,IAAI,IAAI,cAAc,CAAC;AAAA,QACxC;AAAA,MACT,CAAO;AAAA,IACP;AAGA,UAAM,cAAc,CAAC,UAAU;AAC7B,eAAS,MAAM,KAAK,IAAI;AAAA,IAC1B;AAGA,UAAM,iBAAiB,CAAC,MAAM;AAC5B,eAAS,MAAM,YAAY,EAAE,OAAO,MAAM,SAAS;AAAA,IACrD;AAGA,UAAM,gBAAgB,MAAM;AACtBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MACpB,CAAO;AAAA,IACP;AAGA,UAAM,qBAAqB,MAAM;AAC/B,UAAI,CAAC,YAAY,OAAO;AAClBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAChB,CAAS;AACD;AAAA,MACD;AAEDA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACf,CAAO;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAChB,CAAS;AAGL,2BAAmB,QAAQ;AAC3B,iBAAS,QAAQ;AAGb,mBAAW,MAAM;AACfA,wBAAG,MAAC,aAAY;AAAA,QACjB,GAAE,IAAI;AAAA,MACR,GAAE,GAAI;AAAA,IACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1VA,GAAG,WAAWC,SAAe;"}