{"name": "@vue/babel-sugar-v-model", "version": "1.4.0", "description": "Babel syntactic sugar for v-model support in Vue JSX", "main": "dist/plugin.js", "repository": "https://github.com/vuejs/jsx/tree/master/packages/babel-sugar-v-model", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "private": false, "publishConfig": {"access": "public"}, "files": [], "scripts": {"pretest:snapshot": "yarn build:test", "test:snapshot": "nyc --reporter=html --reporter=text-summary ava -v test/snapshot.js", "build:dependency": "cd ../babel-plugin-transform-vue-jsx && yarn build", "pretest:functional": "yarn build:dependency && yarn build:test && nyc --reporter=html --reporter=text-summary babel test/functional.js --plugins ./dist/plugin.testing.js,../babel-plugin-transform-vue-jsx/dist/plugin.js,@babel/plugin-transform-arrow-functions --out-file test/functional-compiled.js", "test:functional": "ava -v test/functional-compiled.js", "build": "rollup -c", "build:test": "rollup -c rollup.config.testing.js", "test": "rm -rf coverage* && yarn test:snapshot && mv coverage coverage-snapshot && yarn test:functional && mv coverage coverage-functional", "prepublish": "yarn build"}, "devDependencies": {"@babel/cli": "^7.2.0", "@babel/core": "^7.2.0", "@babel/plugin-transform-arrow-functions": "^7.12.1", "@babel/preset-env": "^7.2.0", "@vue/test-utils": "1.0.0-beta.26", "ava": "^0.25.0", "jsdom": "^13.0.0", "jsdom-global": "^3.0.2", "nyc": "^13.1.0", "rollup": "^0.67.4", "rollup-plugin-babel": "4.0.3", "rollup-plugin-istanbul": "^2.0.1", "rollup-plugin-uglify-es": "^0.0.1", "vue": "^2.5.17", "vue-template-compiler": "^2.5.17"}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.2.0", "@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "@vue/babel-plugin-transform-vue-jsx": "^1.4.0", "camelcase": "^5.0.0", "html-tags": "^2.0.0", "svg-tags": "^1.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "nyc": {"exclude": ["dist", "test"]}, "gitHead": "6566e12067f5d6c02d3849b574a1b84de5634008"}