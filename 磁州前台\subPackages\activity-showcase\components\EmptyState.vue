<template>
  <view class="empty-state-container" :class="[`size-${size}`, `type-${type}`]">
    <!-- 图标区域 -->
    <view class="empty-icon" v-if="showIcon">
      <!-- 自定义图标 -->
      <slot name="icon" v-if="$slots.icon"></slot>
      
      <!-- 预设图标 -->
      <view class="preset-icon" v-else :class="iconType">
        <!-- 无数据图标 -->
        <svg v-if="iconType === 'no-data'" xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
          <polyline points="14,2 14,8 20,8"></polyline>
          <line x1="16" y1="13" x2="8" y2="13"></line>
          <line x1="16" y1="17" x2="8" y2="17"></line>
          <polyline points="10,9 9,9 8,9"></polyline>
        </svg>
        
        <!-- 搜索无结果图标 -->
        <svg v-else-if="iconType === 'no-search'" xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="11" cy="11" r="8"></circle>
          <path d="M21 21l-4.35-4.35"></path>
          <line x1="9" y1="9" x2="15" y2="15"></line>
          <line x1="15" y1="9" x2="9" y2="15"></line>
        </svg>
        
        <!-- 网络错误图标 -->
        <svg v-else-if="iconType === 'network'" xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="4.93" y1="4.93" x2="19.07" y2="19.07"></line>
        </svg>
        
        <!-- 购物车空图标 -->
        <svg v-else-if="iconType === 'cart'" xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="9" cy="21" r="1"></circle>
          <circle cx="20" cy="21" r="1"></circle>
          <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
          <line x1="7" y1="13" x2="17" y2="13"></line>
        </svg>
        
        <!-- 收藏空图标 -->
        <svg v-else-if="iconType === 'favorite'" xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
          <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
          <line x1="8" y1="12" x2="16" y2="12"></line>
        </svg>
        
        <!-- 订单空图标 -->
        <svg v-else-if="iconType === 'order'" xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
          <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
          <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
          <line x1="9" y1="14" x2="15" y2="14"></line>
        </svg>
        
        <!-- 消息空图标 -->
        <svg v-else-if="iconType === 'message'" xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
          <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
          <line x1="8" y1="12" x2="16" y2="12"></line>
        </svg>
        
        <!-- 默认图标 -->
        <svg v-else xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
          <line x1="9" y1="9" x2="9.01" y2="9"></line>
          <line x1="15" y1="9" x2="15.01" y2="9"></line>
        </svg>
      </view>
    </view>
    
    <!-- 文本内容 -->
    <view class="empty-content">
      <text class="empty-title" v-if="title">{{ title }}</text>
      <text class="empty-description" v-if="description">{{ description }}</text>
      
      <!-- 自定义内容插槽 -->
      <slot name="content"></slot>
    </view>
    
    <!-- 操作按钮 -->
    <view class="empty-actions" v-if="showActions">
      <!-- 主要操作按钮 -->
      <view class="action-btn primary" v-if="primaryAction" @click="handlePrimaryAction">
        <svg v-if="primaryAction.icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path :d="primaryAction.icon"></path>
        </svg>
        <text class="btn-text">{{ primaryAction.text }}</text>
      </view>
      
      <!-- 次要操作按钮 -->
      <view class="action-btn secondary" v-if="secondaryAction" @click="handleSecondaryAction">
        <svg v-if="secondaryAction.icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path :d="secondaryAction.icon"></path>
        </svg>
        <text class="btn-text">{{ secondaryAction.text }}</text>
      </view>
      
      <!-- 自定义操作插槽 -->
      <slot name="actions"></slot>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

// Props定义
const props = defineProps({
  // 空状态类型
  type: {
    type: String,
    default: 'default', // default, error, network, maintenance
    validator: (value) => ['default', 'error', 'network', 'maintenance'].includes(value)
  },
  // 尺寸
  size: {
    type: String,
    default: 'medium', // small, medium, large
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  // 图标类型
  iconType: {
    type: String,
    default: 'no-data', // no-data, no-search, network, cart, favorite, order, message
    validator: (value) => ['no-data', 'no-search', 'network', 'cart', 'favorite', 'order', 'message'].includes(value)
  },
  // 标题
  title: {
    type: String,
    default: ''
  },
  // 描述
  description: {
    type: String,
    default: ''
  },
  // 是否显示图标
  showIcon: {
    type: Boolean,
    default: true
  },
  // 是否显示操作按钮
  showActions: {
    type: Boolean,
    default: true
  },
  // 主要操作
  primaryAction: {
    type: Object,
    default: null
    // { text: '刷新', icon: 'refresh-cw', action: 'refresh' }
  },
  // 次要操作
  secondaryAction: {
    type: Object,
    default: null
    // { text: '返回首页', icon: 'home', action: 'home' }
  }
})

// Emits定义
const emit = defineEmits([
  'primary-action',
  'secondary-action'
])

// 方法
function handlePrimaryAction() {
  if (props.primaryAction && props.primaryAction.action) {
    emit('primary-action', props.primaryAction.action)
  }
}

function handleSecondaryAction() {
  if (props.secondaryAction && props.secondaryAction.action) {
    emit('secondary-action', props.secondaryAction.action)
  }
}
</script>

<style scoped>
/* 空状态组件样式开始 */
.empty-state-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-state-container.size-small {
  padding: 20px 16px;
}

.empty-state-container.size-large {
  padding: 60px 24px;
}

/* 类型样式 */
.empty-state-container.type-error {
  color: #F44336;
}

.empty-state-container.type-network {
  color: #FF9800;
}

.empty-state-container.type-maintenance {
  color: #9C27B0;
}

/* 图标样式 */
.empty-icon {
  margin-bottom: 20px;
  opacity: 0.6;
}

.size-small .empty-icon {
  margin-bottom: 16px;
}

.size-large .empty-icon {
  margin-bottom: 24px;
}

.preset-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.preset-icon svg {
  color: #ccc;
  transition: all 0.3s ease;
}

.size-small .preset-icon svg {
  width: 48px;
  height: 48px;
}

.size-large .preset-icon svg {
  width: 80px;
  height: 80px;
}

.type-error .preset-icon svg {
  color: #F44336;
}

.type-network .preset-icon svg {
  color: #FF9800;
}

.type-maintenance .preset-icon svg {
  color: #9C27B0;
}

/* 内容样式 */
.empty-content {
  margin-bottom: 24px;
}

.size-small .empty-content {
  margin-bottom: 20px;
}

.size-large .empty-content {
  margin-bottom: 28px;
}

.empty-title {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.4;
}

.size-small .empty-title {
  font-size: 16px;
}

.size-large .empty-title {
  font-size: 20px;
}

.empty-description {
  display: block;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  max-width: 300px;
}

.size-small .empty-description {
  font-size: 13px;
  max-width: 250px;
}

.size-large .empty-description {
  font-size: 15px;
  max-width: 350px;
}

/* 操作按钮样式 */
.empty-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 24px;
  transition: all 0.3s ease;
  min-width: 120px;
  justify-content: center;
}

.size-small .action-btn {
  padding: 10px 20px;
  min-width: 100px;
}

.size-large .action-btn {
  padding: 14px 28px;
  min-width: 140px;
}

.action-btn:active {
  transform: scale(0.98);
}

.action-btn.primary {
  background: #2196F3;
  color: white;
}

.action-btn.primary:active {
  background: #1976D2;
}

.action-btn.secondary {
  background: #f5f5f5;
  color: #666;
  border: 1px solid #e0e0e0;
}

.action-btn.secondary:active {
  background: #e9ecef;
}

.btn-text {
  font-size: 14px;
  font-weight: 500;
}

.size-small .btn-text {
  font-size: 13px;
}

.size-large .btn-text {
  font-size: 15px;
}

.action-btn svg {
  color: currentColor;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .empty-state-container {
    padding: 30px 16px;
  }
  
  .empty-actions {
    width: 100%;
  }
  
  .action-btn {
    width: 100%;
    max-width: 200px;
  }
}

/* 动画效果 */
.empty-state-container {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.empty-icon {
  animation: fadeIn 0.8s ease-out 0.2s both;
}

.empty-content {
  animation: fadeIn 0.8s ease-out 0.4s both;
}

.empty-actions {
  animation: fadeIn 0.8s ease-out 0.6s both;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
/* 空状态组件样式结束 */
</style>
