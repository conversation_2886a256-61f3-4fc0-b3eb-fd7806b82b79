{"version": 3, "file": "profile.js", "sources": ["mock/user/profile.js"], "sourcesContent": ["// 用户资料模拟数据\r\nexport const userProfile = {\r\n  id: 'user-001',\r\n  nickname: '磁州居民',\r\n  avatar: '/static/images/tabbar/user-blue.png',\r\n  gender: 1, // 1-男，2-女，0-未设置\r\n  phone: '139****5678',\r\n  email: '<EMAIL>',\r\n  birthday: '1990-01-01',\r\n  level: 2,\r\n  points: 520,\r\n  isVip: true,\r\n  vipExpiry: '2024-12-31',\r\n  registerTime: '2023-01-15',\r\n  location: '磁县',\r\n  address: '磁县城区幸福路123号',\r\n  signature: '热爱生活，热爱磁州',\r\n  follows: 28,\r\n  fans: 15,\r\n  likes: 120,\r\n  collections: 45,\r\n  publishCount: 32,\r\n  interests: ['美食', '旅游', '电影', '音乐']\r\n};\r\n\r\n// 用户统计数据\r\nexport const userStats = {\r\n  publishStats: {\r\n    total: 32,\r\n    infoCount: 15,\r\n    businessCount: 8,\r\n    jobCount: 5,\r\n    houseCount: 4\r\n  },\r\n  interactionStats: {\r\n    likes: 120,\r\n    comments: 85,\r\n    shares: 36,\r\n    collections: 45\r\n  },\r\n  activityStats: {\r\n    participated: 12,\r\n    organized: 3,\r\n    upcoming: 2\r\n  }\r\n};\r\n\r\n// 获取用户资料的API函数\r\nexport const fetchUserProfile = (userId) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve(userProfile);\r\n    }, 300);\r\n  });\r\n};\r\n\r\n// 获取用户统计数据的API函数\r\nexport const fetchUserStats = (userId) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve(userStats);\r\n    }, 300);\r\n  });\r\n};\r\n\r\n// 更新用户资料的API函数\r\nexport const updateUserProfile = (data) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      // 模拟更新成功\r\n      const updatedProfile = {\r\n        ...userProfile,\r\n        ...data\r\n      };\r\n      resolve({\r\n        success: true,\r\n        data: updatedProfile\r\n      });\r\n    }, 500);\r\n  });\r\n}; "], "names": [], "mappings": ";AACO,MAAM,cAAc;AAAA,EACzB,IAAI;AAAA,EACJ,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,UAAU;AAAA,EACV,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,WAAW;AAAA,EACX,cAAc;AAAA,EACd,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,SAAS;AAAA,EACT,MAAM;AAAA,EACN,OAAO;AAAA,EACP,aAAa;AAAA,EACb,cAAc;AAAA,EACd,WAAW,CAAC,MAAM,MAAM,MAAM,IAAI;AACpC;AAGO,MAAM,YAAY;AAAA,EACvB,cAAc;AAAA,IACZ,OAAO;AAAA,IACP,WAAW;AAAA,IACX,eAAe;AAAA,IACf,UAAU;AAAA,IACV,YAAY;AAAA,EACb;AAAA,EACD,kBAAkB;AAAA,IAChB,OAAO;AAAA,IACP,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,aAAa;AAAA,EACd;AAAA,EACD,eAAe;AAAA,IACb,cAAc;AAAA,IACd,WAAW;AAAA,IACX,UAAU;AAAA,EACX;AACH;AAGY,MAAC,mBAAmB,CAAC,WAAW;AAC1C,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,cAAQ,WAAW;AAAA,IACpB,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,iBAAiB,CAAC,WAAW;AACxC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,cAAQ,SAAS;AAAA,IAClB,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,oBAAoB,CAAC,SAAS;AACzC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AAEf,YAAM,iBAAiB;AAAA,QACrB,GAAG;AAAA,QACH,GAAG;AAAA,MACX;AACM,cAAQ;AAAA,QACN,SAAS;AAAA,QACT,MAAM;AAAA,MACd,CAAO;AAAA,IACF,GAAE,GAAG;AAAA,EACV,CAAG;AACH;;;;"}