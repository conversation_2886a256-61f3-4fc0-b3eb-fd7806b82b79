<template>
  <view class="store-entry-container">
    <!-- 顶部导航栏 -->
    <view class="header-area">
      <!-- 顶部安全区域 -->
      <view class="safe-area-top"></view>
      
      <!-- 自定义导航栏 -->
      <view class="custom-navbar">
        <view class="navbar-left" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="back-icon" style="filter: brightness(0) invert(1);"></image>
        </view>
        <view class="navbar-title">店铺管理</view>
        <view class="navbar-right">
          <image src="/static/images/tabbar/更多.png" class="more-icon"></image>
        </view>
      </view>
    </view>
    
    <!-- 背景装饰 -->
    <view class="bg-decoration bg-circle-1"></view>
    <view class="bg-decoration bg-circle-2"></view>
    <view class="bg-decoration bg-circle-3"></view>
    
    <!-- 店铺信息卡片 -->
    <view class="store-info-card">
      <view class="store-header">
        <view class="store-avatar-wrapper">
          <image :src="storeInfo.avatar || '/static/images/default-store.png'" mode="aspectFill" class="store-avatar"></image>
          <view class="edit-avatar-btn" @click="editAvatar">
            <image src="/static/images/tabbar/编辑.png" mode="aspectFit" class="edit-icon"></image>
          </view>
        </view>
        <view class="store-info">
          <view class="store-name-wrapper">
            <text class="store-name">{{storeInfo.name}}</text>
            <view class="store-badge" v-if="storeInfo.isVerified">已认证</view>
          </view>
          <text class="store-desc">{{storeInfo.description || '暂无店铺介绍'}}</text>
          <view class="store-stats">
            <view class="stat-item">
              <text class="stat-value">{{storeInfo.rating}}</text>
              <text class="stat-label">评分</text>
            </view>
            <view class="stat-divider"></view>
            <view class="stat-item">
              <text class="stat-value">{{storeInfo.orderCount}}</text>
              <text class="stat-label">订单</text>
            </view>
            <view class="stat-divider"></view>
            <view class="stat-item">
              <text class="stat-value">{{storeInfo.visitorCount}}</text>
              <text class="stat-label">访客</text>
            </view>
          </view>
        </view>
        <view class="preview-btn" @click="previewStore">
          <text class="preview-text">预览</text>
        </view>
      </view>
      <view class="store-status-bar">
        <view class="status-indicator">
          <view class="status-dot" :class="{'active': storeInfo.status === 'open'}"></view>
          <text class="status-text">{{storeInfo.status === 'open' ? '营业中' : '休息中'}}</text>
        </view>
        <view class="operation-hours">
          <text>营业时间: {{storeInfo.operationHours}}</text>
        </view>
      </view>
    </view>
    
    <!-- 功能区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 基础信息配置 -->
      <view class="function-section">
        <view class="section-header">
          <text class="section-title">基础信息配置</text>
          <text class="section-subtitle">设置店铺基本信息以吸引更多顾客</text>
        </view>
        
        <view class="function-grid">
          <view class="function-item" @click="navigateTo('basicInfo')">
            <view class="icon-wrapper logo-bg">
              <image src="/static/images/tabbar/店铺信息.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">店铺信息</text>
            <text class="function-desc">LOGO、封面图、简介</text>
          </view>
          
          <view class="function-item" @click="navigateTo('operationInfo')">
            <view class="icon-wrapper operation-bg">
              <image src="/static/images/tabbar/营业信息.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">经营信息</text>
            <text class="function-desc">营业时间、配送范围</text>
          </view>
          
          <view class="function-item" @click="navigateTo('locationInfo')">
            <view class="icon-wrapper location-bg">
              <image src="/static/images/tabbar/位置信息.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">位置管理</text>
            <text class="function-desc">门店地址、导航设置</text>
          </view>
        </view>
      </view>
      
      <!-- 商品服务管理 -->
      <view class="function-section">
        <view class="section-header">
          <text class="section-title">商品服务管理</text>
          <text class="section-subtitle">管理您店铺提供的商品与服务</text>
        </view>
        
        <view class="function-grid">
          <view class="function-item" @click="navigateTo('categoryManage')">
            <view class="icon-wrapper category-bg">
              <image src="/static/images/tabbar/分类管理.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">分类管理</text>
            <text class="function-desc">自定义商品分类体系</text>
          </view>
          
          <view class="function-item" @click="navigateTo('productManage')">
            <view class="icon-wrapper product-bg">
              <image src="/static/images/tabbar/商品管理.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">商品管理</text>
            <text class="function-desc">批量上传、SKU管理</text>
          </view>
          
          <view class="function-item" @click="navigateTo('serviceManage')">
            <view class="icon-wrapper service-bg">
              <image src="/static/images/tabbar/服务管理.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">服务项目</text>
            <text class="function-desc">服务定价、服务说明</text>
          </view>
        </view>
      </view>
      
      <!-- 门店形象管理 -->
      <view class="function-section">
        <view class="section-header">
          <text class="section-title">门店形象管理</text>
          <text class="section-subtitle">打造专业店铺形象，提升用户信任度</text>
        </view>
        
        <view class="function-grid">
          <view class="function-item" @click="navigateTo('storeAlbum')">
            <view class="icon-wrapper album-bg">
              <image src="/static/images/tabbar/店铺相册.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">店铺相册</text>
            <text class="function-desc">环境展示、产品展示</text>
          </view>
          
          <view class="function-item" @click="navigateTo('videoManage')">
            <view class="icon-wrapper video-bg">
              <image src="/static/images/tabbar/视频展示.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">视频展示</text>
            <text class="function-desc">店铺宣传视频、服务展示</text>
          </view>
          
          <view class="function-item" @click="navigateTo('storeCulture')">
            <view class="icon-wrapper culture-bg">
              <image src="/static/images/tabbar/商家故事.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">商家故事</text>
            <text class="function-desc">品牌文化、特色介绍</text>
          </view>
        </view>
      </view>
      
      <!-- 认证与资质管理 -->
      <view class="function-section">
        <view class="section-header">
          <text class="section-title">认证与资质管理</text>
          <text class="section-subtitle">提升店铺可信度，获得更多顾客信任</text>
        </view>
        
        <view class="function-grid">
          <view class="function-item" @click="navigateTo('storeVerify')">
            <view class="icon-wrapper verify-bg">
              <image src="/static/images/tabbar/商家认证.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">商家认证</text>
            <text class="function-desc">企业/个体工商户认证</text>
          </view>
          
          <view class="function-item" @click="navigateTo('qualificationManage')">
            <view class="icon-wrapper qualification-bg">
              <image src="/static/images/tabbar/资质管理.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">资质管理</text>
            <text class="function-desc">经营许可证、行业资质</text>
          </view>
          
          <view class="function-item" @click="navigateTo('qualificationRemind')">
            <view class="icon-wrapper remind-bg">
              <image src="/static/images/tabbar/提醒管理.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">到期提醒</text>
            <text class="function-desc">资质到期提醒与续期</text>
          </view>
        </view>
      </view>
      
      <!-- 我的活动管理 -->
      <view class="function-section">
        <view class="section-header">
          <text class="section-title">我的活动管理</text>
          <text class="section-subtitle">管理您发布的营销活动</text>
        </view>
        
        <view class="function-grid">
          <view class="function-item" @click="navigateTo('activityList')">
            <view class="icon-wrapper activity-bg">
              <image src="/static/images/tabbar/活动列表.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">活动列表</text>
            <text class="function-desc">已发布活动状态管理</text>
          </view>
          
          <view class="function-item" @click="navigateTo('activityOperation')">
            <view class="icon-wrapper operation-bg">
              <image src="/static/images/tabbar/活动操作.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">活动操作</text>
            <text class="function-desc">续费、置顶、刷新</text>
          </view>
          
          <view class="function-item" @click="navigateTo('activityData')">
            <view class="icon-wrapper data-bg">
              <image src="/static/images/tabbar/活动数据.png" mode="aspectFit" class="function-icon"></image>
            </view>
            <text class="function-name">活动数据</text>
            <text class="function-desc">浏览量、转化率分析</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      storeInfo: {
        avatar: '/static/images/store-avatar.png',
        name: '磁州小吃美食店',
        description: '专注本地特色小吃，用心制作每一道美食',
        rating: 4.8,
        orderCount: 3256,
        visitorCount: 12489,
        status: 'open',
        operationHours: '09:00-21:00',
        isVerified: true
      }
    }
  },
  onLoad() {
    // 页面加载完成后的处理
    this.setStatusBarHeight();
  },
  methods: {
    // 设置状态栏高度
    setStatusBarHeight() {
      // 获取系统信息设置状态栏高度
      uni.getSystemInfo({
        success: (res) => {
          this.statusBarHeight = res.statusBarHeight;
          // 将状态栏高度设置为CSS变量
          if (typeof document !== 'undefined') {
            document.documentElement.style.setProperty('--status-bar-height', `${this.statusBarHeight}px`);
          }
        }
      });
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 编辑头像
    editAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          // 这里可以添加上传图片的逻辑
          this.storeInfo.avatar = res.tempFilePaths[0];
          uni.showToast({
            title: '头像已更新',
            icon: 'success'
          });
        }
      });
    },
    
    // 预览店铺
    previewStore() {
      uni.showToast({
        title: '店铺预览功能开发中',
        icon: 'none'
      });
    },
    
    // 导航到具体功能页面
    navigateTo(page) {
      // 根据不同的页面参数跳转到对应的页面
      const pageMap = {
        'basicInfo': '/subPackages/merchant-admin/pages/store/basic-info',
        'operationInfo': '/subPackages/merchant-admin/pages/store/operation-info',
        'locationInfo': '/subPackages/merchant-admin/pages/store/location-info',
        'categoryManage': '/subPackages/merchant-admin/pages/store/category',
        'productManage': '/subPackages/merchant-admin/pages/store/product',
        'serviceManage': '/subPackages/merchant-admin/pages/store/service',
        'storeAlbum': '/subPackages/merchant-admin/pages/store/album',
        'videoManage': '/subPackages/merchant-admin/pages/store/video',
        'storeCulture': '/subPackages/merchant-admin/pages/store/culture',
        'storeVerify': '/subPackages/merchant-admin/pages/store/verify',
        'qualificationManage': '/subPackages/merchant-admin/pages/store/qualification',
        'qualificationRemind': '/subPackages/merchant-admin/pages/store/expiration',
        'activityList': '/subPackages/merchant-admin/pages/activity/index',
        'activityOperation': '/subPackages/merchant-admin/pages/activity/operation',
        'activityData': '/subPackages/merchant-admin/pages/activity/data'
      };
      
      const url = pageMap[page];
      if (url) {
        uni.navigateTo({ url });
      } else {
        uni.showToast({
          title: '功能开发中',
          icon: 'none'
        });
      }
    }
  }
}
</script>

<style lang="scss">
.store-entry-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(110rpx + var(--status-bar-height, 20px));
}

/* 顶部导航栏样式 */
.header-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
  color: #fff;
}

.safe-area-top {
  height: var(--status-bar-height, 20px);
  width: 100%;
}

.custom-navbar {
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
}

.navbar-left, .navbar-right {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon, .more-icon {
  width: 48rpx;
  height: 48rpx;
}

/* 背景装饰样式 */
.bg-decoration {
  position: absolute;
  z-index: -1;
  border-radius: 50%;
  opacity: 0.07;
  background-color: #0A84FF;
}

.bg-circle-1 {
  top: -100rpx;
  right: -150rpx;
  width: 400rpx;
  height: 400rpx;
}

.bg-circle-2 {
  top: 10%;
  left: -200rpx;
  width: 500rpx;
  height: 500rpx;
}

.bg-circle-3 {
  bottom: 5%;
  right: -100rpx;
  width: 300rpx;
  height: 300rpx;
}

/* 店铺信息卡片样式 */
.store-info-card {
  margin: 30rpx;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.store-header {
  padding: 30rpx;
  display: flex;
  align-items: center;
  position: relative;
}

.store-avatar-wrapper {
  position: relative;
}

.store-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
  background-color: #f0f0f0;
  border: 4rpx solid #FFFFFF;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.edit-avatar-btn {
  position: absolute;
  right: -10rpx;
  bottom: -10rpx;
  width: 48rpx;
  height: 48rpx;
  background-color: #0A84FF;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-icon {
  width: 24rpx;
  height: 24rpx;
  filter: brightness(0) invert(1);
}

.store-info {
  flex: 1;
  padding: 0 20rpx;
}

.store-name-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.store-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 10rpx;
}

.store-badge {
  padding: 4rpx 12rpx;
  background-color: #34C759;
  color: #FFFFFF;
  font-size: 20rpx;
  border-radius: 10rpx;
}

.store-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
  height: 34rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.store-stats {
  display: flex;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.stat-label {
  font-size: 20rpx;
  color: #999;
}

.stat-divider {
  width: 2rpx;
  height: 30rpx;
  background-color: #E5E5EA;
  margin: 0 30rpx;
}

.preview-btn {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  background-color: #0A84FF;
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 8rpx 24rpx;
  border-radius: 30rpx;
}

.store-status-bar {
  padding: 20rpx 30rpx;
  border-top: 2rpx solid #F2F2F7;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 24rpx;
  color: #666;
}

.status-indicator {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #8E8E93;
  margin-right: 10rpx;
}

.status-dot.active {
  background-color: #34C759;
}

/* 内容滚动区域 */
.content-scroll {
  padding: 20rpx 30rpx;
  height: calc(100vh - 280rpx - var(--status-bar-height, 20px));
}

/* 功能区块样式 */
.function-section {
  margin-bottom: 40rpx;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.section-subtitle {
  font-size: 24rpx;
  color: #999;
  margin-top: 6rpx;
}

.function-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.function-item {
  width: calc(33.33% - 20rpx);
  margin: 10rpx;
  border-radius: 16rpx;
  overflow: hidden;
  background-color: #FFFFFF;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.03);
  padding: 20rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.function-item:active {
  transform: scale(0.95);
  opacity: 0.9;
}

.icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.function-icon {
  width: 40rpx;
  height: 40rpx;
}

.function-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 6rpx;
}

.function-desc {
  font-size: 20rpx;
  color: #999;
  text-align: center;
  padding: 0 10rpx;
  height: 28rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

/* 图标背景颜色 */
.logo-bg {
  background-color: rgba(10, 132, 255, 0.1);
}

.operation-bg {
  background-color: rgba(255, 149, 0, 0.1);
}

.location-bg {
  background-color: rgba(52, 199, 89, 0.1);
}

.category-bg {
  background-color: rgba(175, 82, 222, 0.1);
}

.product-bg {
  background-color: rgba(90, 200, 250, 0.1);
}

.service-bg {
  background-color: rgba(255, 45, 85, 0.1);
}

.album-bg {
  background-color: rgba(88, 86, 214, 0.1);
}

.video-bg {
  background-color: rgba(255, 204, 0, 0.1);
}

.culture-bg {
  background-color: rgba(162, 132, 94, 0.1);
}

.verify-bg {
  background-color: rgba(76, 217, 100, 0.1);
}

.qualification-bg {
  background-color: rgba(255, 59, 48, 0.1);
}

.remind-bg {
  background-color: rgba(0, 122, 255, 0.1);
}

.activity-bg {
  background-color: rgba(255, 45, 85, 0.1);
}

.data-bg {
  background-color: rgba(64, 156, 255, 0.1);
}

/* 适配暗黑模式 */
@media (prefers-color-scheme: dark) {
  .store-entry-container {
    background-color: #1C1C1E;
  }
  
  .store-info-card,
  .function-section,
  .function-item {
    background-color: #2C2C2E;
  }
  
  .store-name,
  .section-title,
  .function-name,
  .stat-value {
    color: #FFFFFF;
  }
  
  .store-desc,
  .section-subtitle,
  .function-desc,
  .stat-label,
  .operation-hours,
  .status-text {
    color: #8E8E93;
  }
  
  .store-status-bar {
    border-top-color: #3A3A3C;
  }
}
</style> 