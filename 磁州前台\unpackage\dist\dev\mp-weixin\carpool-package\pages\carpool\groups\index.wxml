<view class="container"><view class="custom-header" style="{{'padding-top:' + e}}"><view class="header-content"><view class="left-action" bindtap="{{b}}"><image src="{{a}}" class="action-icon back-icon"></image></view><view class="title-area"><text class="page-title">拼车群</text></view><view class="right-action"><image src="{{c}}" class="action-icon" bindtap="{{d}}"></image></view></view></view><view wx:if="{{f}}" class="search-section"><view class="search-box"><image src="{{g}}" mode="aspectFit" class="search-icon"></image><input type="text" class="search-input" placeholder="搜索拼车群" confirm-type="search" bindconfirm="{{h}}" value="{{i}}" bindinput="{{j}}"/><view class="search-cancel" bindtap="{{k}}">取消</view></view></view><view class="tabs-section"><scroll-view scroll-x class="tabs-scroll" show-scrollbar="false"><view wx:for="{{l}}" wx:for-item="tab" wx:key="c" class="{{['tab-item', tab.d && 'active']}}" bindtap="{{tab.e}}"><text>{{tab.a}}</text><view wx:if="{{tab.b}}" class="tab-indicator"></view></view></scroll-view></view><scroll-view scroll-y class="groups-list" refresher-enabled refresher-triggered="{{s}}" bindrefresherrefresh="{{t}}" bindscrolltolower="{{v}}"><view wx:for="{{m}}" wx:for-item="group" wx:key="r" class="group-card" bindtap="{{group.s}}"><view class="group-header"><image src="{{group.a}}" mode="aspectFill" class="group-avatar"></image><view class="group-info"><view class="group-name">{{group.b}}</view><view class="group-meta"><text class="group-members">{{group.c}}人</text><text class="{{['group-type', group.e]}}">{{group.d}}</text></view></view><view class="group-join" catchtap="{{group.f}}"><text>查看</text></view></view><view class="group-content"><view class="group-description"><text wx:if="{{group.g}}">{{group.h}}</text><text wx:else>{{group.i}}</text><text wx:if="{{group.j}}" class="desc-toggle" catchtap="{{group.l}}">{{group.k}}</text></view><scroll-view class="group-tags-scroll" scroll-x enhanced show-scrollbar="false"><view class="group-tags"><view wx:for="{{group.m}}" wx:for-item="tag" wx:key="b" class="tag">{{tag.a}}</view></view></scroll-view></view><view class="group-footer"><view wx:if="{{group.n}}" class="route-info"><view class="route-icon"><image src="{{group.o}}" mode="aspectFit"></image></view><view class="route-text">{{group.p}}</view></view><view class="group-activity"><text>{{group.q}}</text></view></view></view><view wx:if="{{n}}" class="loading-section"><view class="loading-spinner"></view><text>加载中...</text></view><view wx:if="{{o}}" class="empty-section"><image src="{{p}}" mode="aspectFit"></image><text>暂无相关拼车群</text><button wx:if="{{q}}" class="create-btn" bindtap="{{r}}">创建拼车群</button><text wx:else class="empty-tip">拼车群由管理员创建</text></view></scroll-view><view wx:if="{{w}}" class="floating-btn" bindtap="{{y}}"><image src="{{x}}" mode="aspectFit"></image></view><view class="tabbar"><view class="{{['tabbar-item', B && 'active']}}" bindtap="{{C}}"><image src="{{z}}" mode="aspectFit" class="tabbar-icon"></image><text class="{{['tabbar-text', A && 'active-text']}}">同城</text></view><view class="{{['tabbar-item', F && 'active']}}" bindtap="{{G}}"><image src="{{D}}" mode="aspectFit" class="tabbar-icon"></image><text class="{{['tabbar-text', E && 'active-text']}}">拼车</text></view><view class="{{['tabbar-item', J && 'active']}}" bindtap="{{K}}"><image src="{{H}}" mode="aspectFit" class="tabbar-icon"></image><text class="{{['tabbar-text', I && 'active-text']}}">发布</text></view><view class="tabbar-item active"><image src="{{L}}" mode="aspectFit" class="tabbar-icon"></image><text class="tabbar-text active-text">拼车群</text></view><view class="{{['tabbar-item', O && 'active']}}" bindtap="{{P}}"><image src="{{M}}" mode="aspectFit" class="tabbar-icon"></image><text class="{{['tabbar-text', N && 'active-text']}}">我的</text></view></view><view wx:if="{{Q}}" class="qrcode-popup" catchtap="{{af}}"><view class="qrcode-container" catchtap="{{ae}}"><view class="qrcode-header"><text class="qrcode-title">{{R}}</text><view class="qrcode-close" bindtap="{{S}}">×</view></view><view wx:if="{{T}}" class="group-info-section"><view class="group-route"><view class="route-icon"><svg wx:if="{{V}}" u-s="{{['d']}}" u-i="67f6f9c1-0" bind:__l="__l" u-p="{{V}}"><path wx:if="{{U}}" u-i="67f6f9c1-1,67f6f9c1-0" bind:__l="__l" u-p="{{U}}"></path></svg></view><text class="route-text">{{W}}</text></view><view class="group-tags-display"><view wx:for="{{X}}" wx:for-item="tag" wx:key="b" class="tag-display">{{tag.a}}</view></view><view class="group-desc"><text>{{Y}}</text></view><view class="group-members"><text class="members-count">{{Z}}人</text><text class="activity-time">{{aa}}活跃</text></view></view><view class="qrcode-content"><image src="{{ab}}" mode="aspectFit" class="qrcode-image"></image><text class="qrcode-tips">扫描微信二维码加入拼车群</text></view><view class="qrcode-footer"><button class="qrcode-save-btn" bindtap="{{ac}}">保存二维码</button><button class="qrcode-share-btn" bindtap="{{ad}}">分享给好友</button></view></view></view><view class="safe-area-bottom"></view><view wx:if="{{ag}}" class="publish-popup" bindtap="{{ar}}"><view class="publish-card" catchtap="{{aq}}"><view class="publish-header"><text class="publish-title">选择发布类型</text><view class="close-btn" bindtap="{{ah}}"><text class="close-icon">×</text></view></view><view class="publish-options"><view class="publish-option" bindtap="{{aj}}"><view class="option-icon-wrapper people-car"><image src="{{ai}}" mode="aspectFit" class="option-icon"></image></view><text class="option-text">人找车</text></view><view class="publish-option" bindtap="{{al}}"><view class="option-icon-wrapper car-people"><image src="{{ak}}" mode="aspectFit" class="option-icon"></image></view><text class="option-text">车找人</text></view><view class="publish-option" bindtap="{{an}}"><view class="option-icon-wrapper goods-car"><image src="{{am}}" mode="aspectFit" class="option-icon"></image></view><text class="option-text">货找车</text></view><view class="publish-option" bindtap="{{ap}}"><view class="option-icon-wrapper car-goods"><image src="{{ao}}" mode="aspectFit" class="option-icon"></image></view><text class="option-text">车找货</text></view></view></view></view></view>