/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-601419d5, html.data-v-601419d5, #app.data-v-601419d5, .index-container.data-v-601419d5 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.page-container.data-v-601419d5 {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏样式 */
.navbar.data-v-601419d5 {
  display: flex;
  align-items: center;
  height: 44px;
  background-color: #fff;
  padding: 0 15px;
  position: relative;
}
.navbar-back.data-v-601419d5 {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon.data-v-601419d5 {
  width: 12px;
  height: 12px;
  border-top: 2px solid #333;
  border-left: 2px solid #333;
  transform: rotate(-45deg);
}
.navbar-title.data-v-601419d5 {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 600;
  color: #333;
}
.navbar-right.data-v-601419d5 {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.share-icon.data-v-601419d5 {
  color: #333;
}

/* 内容滚动区 */
.content-scroll.data-v-601419d5 {
  flex: 1;
}

/* 页面头部 */
.page-header.data-v-601419d5 {
  height: 180px;
  position: relative;
  overflow: hidden;
}
.header-bg.data-v-601419d5 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20px;
}
.header-title.data-v-601419d5 {
  font-size: 24px;
  font-weight: 700;
  color: #fff;
  margin-bottom: 10px;
}
.header-subtitle.data-v-601419d5 {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
}

/* 内容部分 */
.content-section.data-v-601419d5 {
  padding: 20px 15px;
  background-color: #fff;
  border-radius: 15px 15px 0 0;
  margin-top: -20px;
  position: relative;
  z-index: 1;
}
.section-title.data-v-601419d5 {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  margin-top: 25px;
}
.section-title.data-v-601419d5:first-child {
  margin-top: 0;
}
.title-icon.data-v-601419d5 {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}
.title-text.data-v-601419d5 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
.content-text.data-v-601419d5 {
  font-size: 15px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}
.content-image.data-v-601419d5 {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}
.content-image image.data-v-601419d5 {
  width: 100%;
}

/* 节日列表 */
.festival-list.data-v-601419d5 {
  margin-bottom: 20px;
}
.festival-item.data-v-601419d5 {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}
.festival-header.data-v-601419d5 {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.festival-icon.data-v-601419d5 {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}
.festival-icon text.data-v-601419d5 {
  font-size: 16px;
  font-weight: 600;
  color: #FF6B6B;
}
.festival-name.data-v-601419d5 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.festival-content.data-v-601419d5 {
  padding-left: 50px;
}
.festival-desc.data-v-601419d5 {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 10px;
}
.strategy-points.data-v-601419d5 {
  margin-top: 10px;
}
.strategy-point.data-v-601419d5 {
  display: flex;
  margin-bottom: 8px;
}
.point-dot.data-v-601419d5 {
  width: 6px;
  height: 6px;
  border-radius: 3px;
  background-color: #FF6B6B;
  margin-top: 6px;
  margin-right: 8px;
  flex-shrink: 0;
}
.point-text.data-v-601419d5 {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* 设计要点 */
.design-points.data-v-601419d5 {
  margin-bottom: 20px;
}
.design-point.data-v-601419d5 {
  margin-bottom: 15px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
}
.point-header.data-v-601419d5 {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.point-icon.data-v-601419d5 {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}
.point-title.data-v-601419d5 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.point-desc.data-v-601419d5 {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

/* 时间轴 */
.timing-chart.data-v-601419d5 {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}
.chart-header.data-v-601419d5 {
  margin-bottom: 15px;
}
.chart-title.data-v-601419d5 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.timeline.data-v-601419d5 {
  position: relative;
  padding-left: 20px;
}
.timeline.data-v-601419d5::before {
  content: "";
  position: absolute;
  top: 0;
  left: 4px;
  height: 100%;
  width: 2px;
  background-color: #FF6B6B;
}
.timeline-item.data-v-601419d5 {
  position: relative;
  padding-bottom: 20px;
}
.timeline-item.data-v-601419d5:last-child {
  padding-bottom: 0;
}
.timeline-point.data-v-601419d5 {
  position: absolute;
  left: -20px;
  top: 5px;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #FF6B6B;
}
.timeline-content.data-v-601419d5 {
  padding-left: 10px;
}
.timeline-title.data-v-601419d5 {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 5px;
}
.timeline-desc.data-v-601419d5 {
  font-size: 14px;
  color: #666;
}

/* 案例分析 */
.case-study.data-v-601419d5 {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}
.case-header.data-v-601419d5 {
  margin-bottom: 10px;
}
.case-title.data-v-601419d5 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.case-content.data-v-601419d5 {
  margin-bottom: 15px;
}
.case-desc.data-v-601419d5 {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}
.case-results.data-v-601419d5 {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #eee;
  padding-top: 15px;
}
.result-item.data-v-601419d5 {
  text-align: center;
  flex: 1;
}
.result-label.data-v-601419d5 {
  font-size: 12px;
  color: #999;
  display: block;
  margin-bottom: 5px;
}
.result-value.data-v-601419d5 {
  font-size: 18px;
  font-weight: 600;
  color: #FF6B6B;
}

/* 联系我们 */
.contact-section.data-v-601419d5 {
  margin: 0 15px 30px;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.contact-title.data-v-601419d5 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 10px;
}
.contact-desc.data-v-601419d5 {
  font-size: 14px;
  color: #666;
  display: block;
  margin-bottom: 15px;
}
.contact-btn.data-v-601419d5 {
  background-color: #FF6B6B;
  color: #fff;
  border: none;
  border-radius: 20px;
  padding: 8px 20px;
  font-size: 14px;
}