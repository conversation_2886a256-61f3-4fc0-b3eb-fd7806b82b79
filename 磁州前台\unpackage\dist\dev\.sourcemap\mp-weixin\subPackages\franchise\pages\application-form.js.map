{"version": 3, "file": "application-form.js", "sources": ["subPackages/franchise/pages/application-form.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcZnJhbmNoaXNlXHBhZ2VzXGFwcGxpY2F0aW9uLWZvcm0udnVl"], "sourcesContent": ["<template>\n  <view class=\"application-container\">\n    <!-- 自定义导航栏 -->\n    <cu-custom bgColor=\"bg-gradient-blue\" isBack>\n      <template #backText>返回</template>\n      <template #content>\n        <text class=\"text-center text-white text-bold\" style=\"font-weight: 700; font-size: 36rpx;\">区域加盟申请</text>\n      </template>\n    </cu-custom>\n    \n    <!-- 表单内容 -->\n    <view class=\"form-wrapper\">\n      <!-- 申请区域 -->\n      <view class=\"form-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">申请区域</text>\n        </view>\n        <view class=\"section-content\">\n          <view class=\"form-item\">\n            <view class=\"item-label required\">区域</view>\n            <view class=\"item-content\">\n              <text class=\"selected-region\">{{ selectedRegion }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 基本信息 -->\n      <view class=\"form-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">基本信息</text>\n        </view>\n        <view class=\"section-content\">\n          <view class=\"form-item\">\n            <view class=\"item-label required\">姓名</view>\n            <view class=\"item-content\">\n              <input \n                class=\"form-input\" \n                type=\"text\" \n                placeholder=\"请输入您的真实姓名\" \n                v-model=\"formData.name\" \n                @input=\"validateField('name')\"\n              />\n            </view>\n            <view class=\"error-message\" v-if=\"errors.name\">{{ errors.name }}</view>\n          </view>\n          \n          <view class=\"form-item\">\n            <view class=\"item-label required\">手机号码</view>\n            <view class=\"item-content\">\n              <input \n                class=\"form-input\" \n                type=\"number\" \n                placeholder=\"请输入您的手机号码\" \n                v-model=\"formData.phone\" \n                maxlength=\"11\"\n                @input=\"validateField('phone')\"\n              />\n            </view>\n            <view class=\"error-message\" v-if=\"errors.phone\">{{ errors.phone }}</view>\n          </view>\n          \n          <view class=\"form-item\">\n            <view class=\"item-label required\">微信号</view>\n            <view class=\"item-content\">\n              <input \n                class=\"form-input\" \n                type=\"text\" \n                placeholder=\"请输入您的微信号码\" \n                v-model=\"formData.wechat\"\n                @input=\"validateField('wechat')\"\n              />\n            </view>\n            <view class=\"error-message\" v-if=\"errors.wechat\">{{ errors.wechat }}</view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 公司信息 -->\n      <view class=\"form-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">公司信息</text>\n          <text class=\"section-subtitle\">(个人申请可填写个体工商户信息)</text>\n        </view>\n        <view class=\"section-content\">\n          <view class=\"form-item\">\n            <view class=\"item-label required\">公司名称</view>\n            <view class=\"item-content\">\n              <input \n                class=\"form-input\" \n                type=\"text\" \n                placeholder=\"请输入公司/个体工商户名称\" \n                v-model=\"formData.companyName\"\n                @input=\"validateField('companyName')\"\n              />\n            </view>\n            <view class=\"error-message\" v-if=\"errors.companyName\">{{ errors.companyName }}</view>\n          </view>\n          \n          <view class=\"form-item\">\n            <view class=\"item-label required\">营业执照</view>\n            <view class=\"item-content\">\n              <view class=\"upload-box\" @tap=\"chooseBusinessLicense\">\n                <image v-if=\"formData.businessLicense\" :src=\"formData.businessLicense\" mode=\"aspectFill\" class=\"license-image\"></image>\n                <view v-else class=\"upload-placeholder\">\n                  <text class=\"cuIcon-camerafill\"></text>\n                  <text class=\"upload-text\">上传营业执照照片</text>\n                </view>\n              </view>\n            </view>\n            <view class=\"error-message\" v-if=\"errors.businessLicense\">{{ errors.businessLicense }}</view>\n          </view>\n          \n          <view class=\"form-item\">\n            <view class=\"item-label\">公司地址</view>\n            <view class=\"item-content\">\n              <input \n                class=\"form-input\" \n                type=\"text\" \n                placeholder=\"请输入公司详细地址\" \n                v-model=\"formData.companyAddress\"\n              />\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 运营能力 -->\n      <view class=\"form-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">运营能力</text>\n        </view>\n        <view class=\"section-content\">\n          <view class=\"form-item\">\n            <view class=\"item-label required\">运营人数</view>\n            <view class=\"item-content\">\n              <picker \n                mode=\"selector\" \n                :range=\"staffOptions\" \n                @change=\"staffChange\" \n                class=\"selector-picker\"\n              >\n                <view class=\"picker-value\">\n                  <text>{{ formData.staffCount || '请选择运营团队人数' }}</text>\n                  <text class=\"cuIcon-unfold\"></text>\n                </view>\n              </picker>\n            </view>\n            <view class=\"error-message\" v-if=\"errors.staffCount\">{{ errors.staffCount }}</view>\n          </view>\n          \n          <view class=\"form-item\">\n            <view class=\"item-label required\">本地资源</view>\n            <view class=\"item-content\">\n              <view class=\"checkbox-group\">\n                <view \n                  class=\"checkbox-item\" \n                  v-for=\"(item, index) in resourceOptions\" \n                  :key=\"index\"\n                  @tap=\"toggleResource(item.value)\"\n                  :class=\"{'checked': formData.resources.includes(item.value)}\"\n                >\n                  <text class=\"checkbox-icon\" :class=\"formData.resources.includes(item.value) ? 'cuIcon-squarecheckfill text-blue' : 'cuIcon-square'\"></text>\n                  <text class=\"checkbox-label\">{{ item.label }}</text>\n                </view>\n              </view>\n            </view>\n            <view class=\"error-message\" v-if=\"errors.resources\">{{ errors.resources }}</view>\n          </view>\n          \n          <view class=\"form-item\">\n            <view class=\"item-label\">已有商家资源</view>\n            <view class=\"item-content\">\n              <textarea \n                class=\"form-textarea\" \n                placeholder=\"请描述您现有的商家资源情况（选填）\" \n                v-model=\"formData.merchantResources\"\n                maxlength=\"200\"\n              ></textarea>\n              <view class=\"textarea-counter\">{{ formData.merchantResources.length }}/200</view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 补充信息 -->\n      <view class=\"form-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">补充信息</text>\n        </view>\n        <view class=\"section-content\">\n          <view class=\"form-item\">\n            <view class=\"item-label\">加盟优势</view>\n            <view class=\"item-content\">\n              <textarea \n                class=\"form-textarea\" \n                placeholder=\"请描述您的加盟优势（选填）\" \n                v-model=\"formData.advantages\"\n                maxlength=\"500\"\n              ></textarea>\n              <view class=\"textarea-counter\">{{ formData.advantages.length }}/500</view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 协议同意 -->\n      <view class=\"agreement-box\">\n        <view class=\"agreement-checkbox\" @tap=\"toggleAgreement\">\n          <text class=\"checkbox-icon\" :class=\"formData.agreement ? 'cuIcon-squarecheckfill text-blue' : 'cuIcon-square'\"></text>\n        </view>\n        <view class=\"agreement-text\">\n          <text>我已阅读并同意</text>\n          <text class=\"agreement-link\" @tap=\"showAgreement\">《区域加盟合作协议》</text>\n        </view>\n      </view>\n      <view class=\"error-message centered\" v-if=\"errors.agreement\">{{ errors.agreement }}</view>\n      \n      <!-- 提交按钮 -->\n      <view class=\"submit-btn-wrapper\">\n        <button class=\"submit-btn\" @tap=\"submitApplication\" :disabled=\"submitting\">\n          <text v-if=\"!submitting\">提交申请</text>\n          <view v-else class=\"loading-icon\"></view>\n        </button>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, onMounted } from 'vue';\n\n// 响应式状态\nconst selectedRegion = ref('');\nconst formData = reactive({\n  name: '',\n  phone: '',\n  wechat: '',\n  companyName: '',\n  businessLicense: '',\n  companyAddress: '',\n  staffCount: '',\n  resources: [],\n  merchantResources: '',\n  advantages: '',\n  agreement: false\n});\nconst errors = reactive({});\nconst submitting = ref(false);\n\n// 选项数据\nconst staffOptions = ['1-3人', '4-10人', '11-20人', '20人以上'];\nconst resourceOptions = [\n  { label: '本地商家资源', value: 'merchant' },\n  { label: '媒体资源', value: 'media' },\n  { label: '政府资源', value: 'government' },\n  { label: '协会/商会资源', value: 'association' },\n  { label: '高校/培训机构资源', value: 'education' }\n];\n\n// 生命周期钩子\nonMounted(() => {\n  // 获取页面参数\n  const pages = getCurrentPages();\n  const currentPage = pages[pages.length - 1];\n  const options = currentPage.options || {};\n  \n  // 从URL参数获取区域信息\n  if (options.region) {\n    selectedRegion.value = decodeURIComponent(options.region);\n  }\n});\n\n// 表单字段验证\nconst validateField = (field) => {\n  switch (field) {\n    case 'name':\n      errors.name = formData.name ? '' : '请输入姓名';\n      break;\n    case 'phone':\n      if (!formData.phone) {\n        errors.phone = '请输入手机号码';\n      } else if (!/^1[3-9]\\d{9}$/.test(formData.phone)) {\n        errors.phone = '请输入正确的手机号码';\n      } else {\n        errors.phone = '';\n      }\n      break;\n    case 'wechat':\n      errors.wechat = formData.wechat ? '' : '请输入微信号';\n      break;\n    case 'companyName':\n      errors.companyName = formData.companyName ? '' : '请输入公司名称';\n      break;\n    case 'businessLicense':\n      errors.businessLicense = formData.businessLicense ? '' : '请上传营业执照';\n      break;\n    case 'staffCount':\n      errors.staffCount = formData.staffCount ? '' : '请选择运营人数';\n      break;\n    case 'resources':\n      errors.resources = formData.resources.length > 0 ? '' : '请至少选择一项本地资源';\n      break;\n    case 'agreement':\n      errors.agreement = formData.agreement ? '' : '请同意协议后提交';\n      break;\n  }\n};\n\n// 验证所有字段\nconst validateForm = () => {\n  validateField('name');\n  validateField('phone');\n  validateField('wechat');\n  validateField('companyName');\n  validateField('businessLicense');\n  validateField('staffCount');\n  validateField('resources');\n  validateField('agreement');\n  \n  // 检查是否有错误\n  for (const key in errors) {\n    if (errors[key]) {\n      return false;\n    }\n  }\n  return true;\n};\n\n// 选择运营人数\nconst staffChange = (e) => {\n  const index = e.detail.value;\n  formData.staffCount = staffOptions[index];\n  validateField('staffCount');\n};\n\n// 切换资源选择\nconst toggleResource = (value) => {\n  const index = formData.resources.indexOf(value);\n  if (index === -1) {\n    formData.resources.push(value);\n  } else {\n    formData.resources.splice(index, 1);\n  }\n  validateField('resources');\n};\n\n// 选择营业执照图片\nconst chooseBusinessLicense = () => {\n  uni.chooseImage({\n    count: 1,\n    sizeType: ['compressed'],\n    sourceType: ['album', 'camera'],\n    success: (res) => {\n      formData.businessLicense = res.tempFilePaths[0];\n      validateField('businessLicense');\n    }\n  });\n};\n\n// 切换协议同意状态\nconst toggleAgreement = () => {\n  formData.agreement = !formData.agreement;\n  validateField('agreement');\n};\n\n// 显示协议内容\nconst showAgreement = () => {\n  uni.navigateTo({\n    url: '/subPackages/franchise/pages/agreement'\n  });\n};\n\n// 提交申请\nconst submitApplication = () => {\n  if (submitting.value) return;\n  \n  if (!validateForm()) {\n    uni.showToast({\n      title: '请完善表单信息',\n      icon: 'none'\n    });\n    return;\n  }\n  \n  submitting.value = true;\n  \n  // 模拟API请求，实际应调用服务器接口\n  setTimeout(() => {\n    submitting.value = false;\n    \n    uni.showModal({\n      title: '申请提交成功',\n      content: '您的区域加盟申请已提交，我们将在3个工作日内审核并联系您。',\n      showCancel: false,\n      success: () => {\n        uni.navigateBack();\n      }\n    });\n  }, 1500);\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.application-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: 40rpx;\n}\n\n.form-wrapper {\n  padding: 30rpx;\n}\n\n.form-section {\n  background-color: #FFFFFF;\n  border-radius: 20rpx;\n  margin-bottom: 30rpx;\n  overflow: hidden;\n  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);\n}\n\n.section-header {\n  padding: 30rpx;\n  border-bottom: 1px solid #F0F0F0;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333333;\n  position: relative;\n  padding-left: 20rpx;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    left: 0;\n    top: 50%;\n    transform: translateY(-50%);\n    width: 6rpx;\n    height: 30rpx;\n    background-color: #1677FF;\n    border-radius: 3rpx;\n  }\n}\n\n.section-subtitle {\n  font-size: 24rpx;\n  color: #999999;\n  margin-left: 20rpx;\n}\n\n.section-content {\n  padding: 20rpx 30rpx;\n}\n\n.form-item {\n  margin-bottom: 30rpx;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n.item-label {\n  font-size: 28rpx;\n  color: #333333;\n  margin-bottom: 15rpx;\n  \n  &.required::before {\n    content: '*';\n    color: #FF3B30;\n    margin-right: 6rpx;\n  }\n}\n\n.form-input {\n  height: 90rpx;\n  line-height: 90rpx;\n  padding: 0 20rpx;\n  background-color: #F9FCFF;\n  border-radius: 8rpx;\n  border: 1px solid #E5E5E5;\n  font-size: 28rpx;\n  width: 100%;\n  box-sizing: border-box;\n}\n\n.form-textarea {\n  width: 100%;\n  height: 200rpx;\n  padding: 20rpx;\n  background-color: #F9FCFF;\n  border-radius: 8rpx;\n  border: 1px solid #E5E5E5;\n  font-size: 28rpx;\n  box-sizing: border-box;\n}\n\n.textarea-counter {\n  text-align: right;\n  font-size: 24rpx;\n  color: #999999;\n  margin-top: 10rpx;\n}\n\n.selected-region {\n  font-size: 28rpx;\n  color: #1677FF;\n  font-weight: 500;\n  background-color: rgba(22, 119, 255, 0.05);\n  padding: 15rpx 20rpx;\n  border-radius: 8rpx;\n  display: inline-block;\n}\n\n.upload-box {\n  width: 280rpx;\n  height: 400rpx;\n  border: 1px dashed #CCCCCC;\n  border-radius: 8rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n}\n\n.upload-placeholder {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  \n  .cuIcon-camerafill {\n    font-size: 60rpx;\n    color: #CCCCCC;\n    margin-bottom: 15rpx;\n  }\n  \n  .upload-text {\n    font-size: 24rpx;\n    color: #999999;\n  }\n}\n\n.license-image {\n  width: 100%;\n  height: 100%;\n}\n\n.selector-picker {\n  width: 100%;\n}\n\n.picker-value {\n  height: 90rpx;\n  line-height: 90rpx;\n  padding: 0 20rpx;\n  background-color: #F9FCFF;\n  border-radius: 8rpx;\n  border: 1px solid #E5E5E5;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 28rpx;\n  color: #333333;\n}\n\n.checkbox-group {\n  display: flex;\n  flex-direction: column;\n}\n\n.checkbox-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20rpx;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n  \n  &.checked {\n    .checkbox-label {\n      color: #1677FF;\n    }\n  }\n}\n\n.checkbox-icon {\n  font-size: 40rpx;\n  margin-right: 15rpx;\n}\n\n.checkbox-label {\n  font-size: 28rpx;\n  color: #333333;\n}\n\n.agreement-box {\n  display: flex;\n  align-items: center;\n  margin: 40rpx 0 20rpx;\n  padding: 0 10rpx;\n}\n\n.agreement-checkbox {\n  margin-right: 15rpx;\n  \n  .checkbox-icon {\n    font-size: 40rpx;\n  }\n}\n\n.agreement-text {\n  font-size: 26rpx;\n  color: #666666;\n}\n\n.agreement-link {\n  color: #1677FF;\n}\n\n.error-message {\n  font-size: 24rpx;\n  color: #FF3B30;\n  margin-top: 10rpx;\n  \n  &.centered {\n    text-align: center;\n  }\n}\n\n.submit-btn-wrapper {\n  margin: 60rpx 0 40rpx;\n  padding: 0 30rpx;\n}\n\n.submit-btn {\n  width: 100%;\n  height: 90rpx;\n  line-height: 90rpx;\n  background: linear-gradient(90deg, #1677FF, #4F9DFF);\n  color: #FFFFFF;\n  font-size: 32rpx;\n  font-weight: 500;\n  border-radius: 45rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  \n  &[disabled] {\n    background: linear-gradient(90deg, #CCCCCC, #E5E5E5);\n    color: #FFFFFF;\n    opacity: 1;\n  }\n}\n\n.loading-icon {\n  width: 40rpx;\n  height: 40rpx;\n  border: 4rpx solid #FFFFFF;\n  border-radius: 50%;\n  border-top-color: transparent;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/franchise/pages/application-form.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "onMounted", "uni"], "mappings": ";;;;;;;;;AAyOA,UAAM,iBAAiBA,cAAAA,IAAI,EAAE;AAC7B,UAAM,WAAWC,cAAAA,SAAS;AAAA,MACxB,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,WAAW,CAAE;AAAA,MACb,mBAAmB;AAAA,MACnB,YAAY;AAAA,MACZ,WAAW;AAAA,IACb,CAAC;AACD,UAAM,SAASA,cAAAA,SAAS,CAAA,CAAE;AAC1B,UAAM,aAAaD,cAAAA,IAAI,KAAK;AAG5B,UAAM,eAAe,CAAC,QAAQ,SAAS,UAAU,OAAO;AACxD,UAAM,kBAAkB;AAAA,MACtB,EAAE,OAAO,UAAU,OAAO,WAAY;AAAA,MACtC,EAAE,OAAO,QAAQ,OAAO,QAAS;AAAA,MACjC,EAAE,OAAO,QAAQ,OAAO,aAAc;AAAA,MACtC,EAAE,OAAO,WAAW,OAAO,cAAe;AAAA,MAC1C,EAAE,OAAO,aAAa,OAAO,YAAa;AAAA,IAC5C;AAGAE,kBAAAA,UAAU,MAAM;AAEd,YAAM,QAAQ;AACd,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,UAAU,YAAY,WAAW;AAGvC,UAAI,QAAQ,QAAQ;AAClB,uBAAe,QAAQ,mBAAmB,QAAQ,MAAM;AAAA,MACzD;AAAA,IACH,CAAC;AAGD,UAAM,gBAAgB,CAAC,UAAU;AAC/B,cAAQ,OAAK;AAAA,QACX,KAAK;AACH,iBAAO,OAAO,SAAS,OAAO,KAAK;AACnC;AAAA,QACF,KAAK;AACH,cAAI,CAAC,SAAS,OAAO;AACnB,mBAAO,QAAQ;AAAA,UAChB,WAAU,CAAC,gBAAgB,KAAK,SAAS,KAAK,GAAG;AAChD,mBAAO,QAAQ;AAAA,UACvB,OAAa;AACL,mBAAO,QAAQ;AAAA,UAChB;AACD;AAAA,QACF,KAAK;AACH,iBAAO,SAAS,SAAS,SAAS,KAAK;AACvC;AAAA,QACF,KAAK;AACH,iBAAO,cAAc,SAAS,cAAc,KAAK;AACjD;AAAA,QACF,KAAK;AACH,iBAAO,kBAAkB,SAAS,kBAAkB,KAAK;AACzD;AAAA,QACF,KAAK;AACH,iBAAO,aAAa,SAAS,aAAa,KAAK;AAC/C;AAAA,QACF,KAAK;AACH,iBAAO,YAAY,SAAS,UAAU,SAAS,IAAI,KAAK;AACxD;AAAA,QACF,KAAK;AACH,iBAAO,YAAY,SAAS,YAAY,KAAK;AAC7C;AAAA,MACH;AAAA,IACH;AAGA,UAAM,eAAe,MAAM;AACzB,oBAAc,MAAM;AACpB,oBAAc,OAAO;AACrB,oBAAc,QAAQ;AACtB,oBAAc,aAAa;AAC3B,oBAAc,iBAAiB;AAC/B,oBAAc,YAAY;AAC1B,oBAAc,WAAW;AACzB,oBAAc,WAAW;AAGzB,iBAAW,OAAO,QAAQ;AACxB,YAAI,OAAO,GAAG,GAAG;AACf,iBAAO;AAAA,QACR;AAAA,MACF;AACD,aAAO;AAAA,IACT;AAGA,UAAM,cAAc,CAAC,MAAM;AACzB,YAAM,QAAQ,EAAE,OAAO;AACvB,eAAS,aAAa,aAAa,KAAK;AACxC,oBAAc,YAAY;AAAA,IAC5B;AAGA,UAAM,iBAAiB,CAAC,UAAU;AAChC,YAAM,QAAQ,SAAS,UAAU,QAAQ,KAAK;AAC9C,UAAI,UAAU,IAAI;AAChB,iBAAS,UAAU,KAAK,KAAK;AAAA,MACjC,OAAS;AACL,iBAAS,UAAU,OAAO,OAAO,CAAC;AAAA,MACnC;AACD,oBAAc,WAAW;AAAA,IAC3B;AAGA,UAAM,wBAAwB,MAAM;AAClCC,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAChB,mBAAS,kBAAkB,IAAI,cAAc,CAAC;AAC9C,wBAAc,iBAAiB;AAAA,QAChC;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,kBAAkB,MAAM;AAC5B,eAAS,YAAY,CAAC,SAAS;AAC/B,oBAAc,WAAW;AAAA,IAC3B;AAGA,UAAM,gBAAgB,MAAM;AAC1BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,MAAM;AAC9B,UAAI,WAAW;AAAO;AAEtB,UAAI,CAAC,aAAY,GAAI;AACnBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,iBAAW,QAAQ;AAGnB,iBAAW,MAAM;AACf,mBAAW,QAAQ;AAEnBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,SAAS,MAAM;AACbA,0BAAG,MAAC,aAAY;AAAA,UACjB;AAAA,QACP,CAAK;AAAA,MACF,GAAE,IAAI;AAAA,IACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/YA,GAAG,WAAW,eAAe;"}