<template>
	<view class="poster-container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="navbar-left" @click="goBack">
				<image src="/static/images/tabbar/最新返回键.png" class="back-icon"></image>
			</view>
			<view class="navbar-title">推广海报</view>
			<view class="navbar-right">
				<!-- 预留位置与发布页面保持一致 -->
			</view>
		</view>
		
		<!-- 添加顶部安全区域 -->
		<view class="safe-area-top"></view>
		
		<!-- 海报预览区域 -->
		<view class="poster-preview">
			<view class="preview-header">
				<text class="preview-title">我的推广海报</text>
				<view class="template-switch" @click="showTemplateModal">
					<image class="switch-icon" src="/static/images/tabbar/template.png"></image>
					<text class="switch-text">切换模板</text>
				</view>
			</view>
			
			<view class="poster-card">
				<image class="poster-image" :src="currentPoster" mode="widthFix"></image>
				<view class="poster-loading" v-if="isGenerating">
					<view class="loading-spinner"></view>
					<text class="loading-text">海报生成中...</text>
				</view>
			</view>
			
			<!-- 美化后的按钮组 -->
			<view class="new-poster-actions">
				<button class="action-button refresh-button" @click="refreshPoster">
					<view class="button-icon-wrap">
						<image class="button-icon" src="/static/images/tabbar/refresh.png"></image>
					</view>
					<text class="button-text">重新生成</text>
				</button>
				
				<button class="action-button save-button" @click="savePoster">
					<view class="button-icon-wrap save-icon-bg">
						<image class="button-icon" src="/static/images/tabbar/download.png"></image>
					</view>
					<text class="button-text">保存到相册</text>
				</button>
				
				<button class="action-button share-button" @click="sharePoster">
					<view class="button-icon-wrap share-icon-bg">
						<image class="button-icon" src="/static/images/tabbar/share.png"></image>
					</view>
					<text class="button-text">立即分享</text>
				</button>
			</view>
		</view>
		
		<!-- 推广数据卡片 -->
		<view class="stats-card">
			<view class="stats-header">
				<text class="stats-title">今日推广数据</text>
			</view>
			<view class="stats-content">
				<view class="stats-item">
					<text class="stats-value">{{ promotionData.viewCount }}</text>
					<text class="stats-label">浏览量</text>
				</view>
				<view class="stats-divider"></view>
				<view class="stats-item">
					<text class="stats-value">{{ promotionData.registerCount }}</text>
					<text class="stats-label">新增用户</text>
				</view>
				<view class="stats-divider"></view>
				<view class="stats-item">
					<text class="stats-value">{{ promotionData.orderCount }}</text>
					<text class="stats-label">订单数</text>
				</view>
				<view class="stats-divider"></view>
				<view class="stats-item">
					<text class="stats-value">{{ promotionData.income }}</text>
					<text class="stats-label">收益</text>
				</view>
			</view>
		</view>
		
		<!-- 佣金规则卡片 -->
		<view class="commission-card">
			<view class="commission-header">
				<text class="commission-title">合伙人等级</text>
				<view class="upgrade-btn" @click="goToUpgrade">
					<text class="upgrade-text">立即升级</text>
					<image class="upgrade-icon" src="/static/images/tabbar/右箭头.png"></image>
				</view>
			</view>
			<view class="commission-content">
				<view class="commission-item">
					<view class="commission-info">
						<view class="commission-label">当前等级</view>
						<view class="commission-desc">{{ getLevelName(partnerInfo.level) }}</view>
					</view>
					<view class="commission-value level-tag">{{ partnerInfo.level }}级</view>
				</view>
				<view class="level-benefits">
					<view class="benefit-item">
						<view class="benefit-icon-wrap">
							<image class="benefit-icon" src="/static/images/tabbar/收益明细.png"></image>
						</view>
						<view class="benefit-text">一级佣金 {{ getCommissionRate(1) }}%</view>
					</view>
					<view class="benefit-item">
						<view class="benefit-icon-wrap">
							<image class="benefit-icon" src="/static/images/tabbar/我的粉丝.png"></image>
						</view>
						<view class="benefit-text">二级佣金 {{ getCommissionRate(2) }}%</view>
					</view>
				</view>
				<view class="upgrade-tips">
					<text class="tips-text">升级更高等级可获得更多佣金比例和特权</text>
				</view>
			</view>
		</view>
		
		<!-- 推广步骤 -->
		<view class="steps-card">
			<view class="steps-header">
				<text class="steps-title">推广步骤</text>
			</view>
			<view class="steps-content">
				<view class="step-item">
					<view class="step-icon-wrap step1-bg">
						<image class="step-icon" src="/static/images/tabbar/save.png"></image>
					</view>
					<view class="step-info">
						<view class="step-name">保存海报</view>
						<view class="step-desc">保存推广海报到手机相册</view>
					</view>
				</view>
				
				<view class="step-connector"></view>
				
				<view class="step-item">
					<view class="step-icon-wrap step2-bg">
						<image class="step-icon" src="/static/images/tabbar/share.png"></image>
					</view>
					<view class="step-info">
						<view class="step-name">分享海报</view>
						<view class="step-desc">分享海报到朋友圈或好友</view>
					</view>
				</view>
				
				<view class="step-connector"></view>
				
				<view class="step-item">
					<view class="step-icon-wrap step3-bg">
						<image class="step-icon" src="/static/images/tabbar/scan.png"></image>
					</view>
					<view class="step-info">
						<view class="step-name">好友扫码</view>
						<view class="step-desc">好友扫描海报二维码</view>
					</view>
				</view>
				
				<view class="step-connector"></view>
				
				<view class="step-item">
					<view class="step-icon-wrap step4-bg">
						<image class="step-icon" src="/static/images/tabbar/money.png"></image>
					</view>
					<view class="step-info">
						<view class="step-name">获得佣金</view>
						<view class="step-desc">好友消费后您获得佣金</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 推广规则 -->
		<view class="rules-card">
			<view class="rules-header">
				<text class="rules-title">合伙人升级规则</text>
				<view class="rules-tag">付费升级</view>
			</view>
			<view class="rules-content">
				<view class="rule-item">
					<view class="rule-dot"></view>
					<text class="rule-text">普通合伙人：免费，一级佣金{{ getCommissionRate(1, 1) }}%，二级佣金{{ getCommissionRate(2, 1) }}%</text>
				</view>
				
				<view class="rule-item">
					<view class="rule-dot silver-dot"></view>
					<text class="rule-text">银牌合伙人：￥198/年，一级佣金{{ getCommissionRate(1, 2) }}%，二级佣金{{ getCommissionRate(2, 2) }}%</text>
				</view>
				
				<view class="rule-item">
					<view class="rule-dot gold-dot"></view>
					<text class="rule-text">金牌合伙人：￥498/年，一级佣金{{ getCommissionRate(1, 3) }}%，二级佣金{{ getCommissionRate(2, 3) }}%</text>
				</view>
				
				<view class="rule-item">
					<view class="rule-dot diamond-dot"></view>
					<text class="rule-text">钻石合伙人：￥998/年，一级佣金{{ getCommissionRate(1, 4) }}%，二级佣金{{ getCommissionRate(2, 4) }}%，专属客服</text>
				</view>
			</view>
			
			<view class="upgrade-action">
				<button class="upgrade-now-btn" @click="goToUpgrade">立即升级</button>
			</view>
		</view>
		
		<!-- 模板选择弹窗 -->
		<view class="template-modal" v-if="showModal" @click="hideTemplateModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">选择海报模板</text>
					<view class="close-btn" @click="hideTemplateModal">×</view>
				</view>
				
				<scroll-view class="template-list" scroll-y>
					<view 
						class="template-item" 
						v-for="(template, index) in posterTemplates" 
						:key="index"
						:class="{ active: selectedTemplate === index }"
						@click="selectTemplate(index)"
					>
						<image class="template-image" :src="template" mode="aspectFill"></image>
						<view class="template-mask" v-if="selectedTemplate === index">
							<image class="check-icon" src="/static/images/tabbar/check.png"></image>
						</view>
					</view>
				</scroll-view>
				
				<button class="confirm-btn" @click="confirmTemplate">确认选择</button>
			</view>
		</view>
		
		<!-- 分享菜单 -->
		<view class="share-modal" v-if="showShareModal" @click="hideShareModal">
			<view class="share-content" @click.stop>
				<view class="share-header">
					<text class="share-title">分享到</text>
					<view class="close-btn" @click="hideShareModal">×</view>
				</view>
				
				<view class="share-options">
					<view class="share-option" @click="shareToWechat">
						<view class="share-icon-wrap wechat-bg">
							<image class="share-icon" src="/static/images/tabbar/wechat.png"></image>
						</view>
						<text class="share-name">微信</text>
					</view>
					
					<view class="share-option" @click="shareToMoments">
						<view class="share-icon-wrap moments-bg">
							<image class="share-icon" src="/static/images/tabbar/moments.png"></image>
						</view>
						<text class="share-name">朋友圈</text>
					</view>
					
					<view class="share-option" @click="shareToQQ">
						<view class="share-icon-wrap qq-bg">
							<image class="share-icon" src="/static/images/tabbar/qq.png"></image>
						</view>
						<text class="share-name">QQ</text>
					</view>
					
					<view class="share-option" @click="shareToWeibo">
						<view class="share-icon-wrap weibo-bg">
							<image class="share-icon" src="/static/images/tabbar/weibo.png"></image>
						</view>
						<text class="share-name">微博</text>
					</view>
				</view>
				
				<button class="cancel-btn" @click="hideShareModal">取消</button>
			</view>
		</view>
		
		<!-- 底部操作按钮 -->
		<view class="bottom-actions">
			<button class="generate-btn" @click="refreshPoster">
				<image class="btn-icon" src="/static/images/tabbar/refresh.png"></image>
				<text class="btn-text">重新生成海报</text>
			</button>
		</view>
	</view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { getLocalUserInfo } from '@/utils/userProfile.js';

// 响应式数据
const partnerInfo = reactive({
	level: 2, // 默认等级
	nickname: '',
	avatar: ''
});
const currentPoster = ref('/static/images/cizhou.png');
const posterTemplates = ref([
	'/static/images/cizhou.png',
	'/static/images/tabbar/推广海报.png',
	'/static/images/tabbar/我的二维码.png',
	'/static/images/tabbar/规则说明.png'
]);
const selectedTemplate = ref(0);
const isGenerating = ref(false);
const showModal = ref(false);
const showShareModal = ref(false);
const promotionData = reactive({
	viewCount: '0',
	registerCount: '0',
	orderCount: '0',
	income: '¥0.00'
});

// 获取用户信息
const getUserInfo = () => {
	const userInfo = getLocalUserInfo();
	if (userInfo) {
		partnerInfo.nickname = userInfo.nickname;
		partnerInfo.avatar = userInfo.avatar;
	}
};

// 返回上一页
const goBack = () => {
	uni.navigateBack();
};

// 获取合伙人信息
const getPartnerInfo = () => {
	// 模拟数据，实际应从API获取
	partnerInfo.level = 2;
};

// 获取推广数据
const getPromotionData = () => {
	// 模拟数据，实际应从API获取
	promotionData.viewCount = '28';
	promotionData.registerCount = '5';
	promotionData.orderCount = '3';
	promotionData.income = '¥35.60';
};

// 生成海报
const generatePoster = () => {
	isGenerating.value = true;
	
	// 模拟海报生成过程
	setTimeout(() => {
		// 确保使用当前选择的模板
		currentPoster.value = posterTemplates.value[selectedTemplate.value];
		isGenerating.value = false;
		
		// 显示生成成功提示
		uni.showToast({
			title: '海报生成成功',
			icon: 'success',
			duration: 1500
		});
	}, 1500);
	
	// 实际应调用后端API生成海报
	// const params = {
	//   templateId: selectedTemplate.value,
	//   userId: getApp().globalData.userInfo.userId,
	//   nickname: partnerInfo.nickname,
	//   avatar: partnerInfo.avatar,
	//   level: partnerInfo.level
	// };
	// 
	// uni.request({
	//   url: 'https://api.example.com/generate-poster',
	//   method: 'POST',
	//   data: params,
	//   success: (res) => {
	//     if (res.data.code === 0) {
	//       currentPoster.value = res.data.data.posterUrl;
	//     } else {
	//       uni.showToast({
	//         title: res.data.message || '海报生成失败',
	//         icon: 'none'
	//       });
	//     }
	//   },
	//   fail: () => {
	//     uni.showToast({
	//       title: '网络错误，请重试',
	//       icon: 'none'
	//     });
	//   },
	//   complete: () => {
	//     isGenerating.value = false;
	//   }
	// });
};

// 刷新海报
const refreshPoster = () => {
	generatePoster();
};

// 保存海报到相册
const savePoster = () => {
	uni.showLoading({
		title: '保存中...'
	});
	
	uni.downloadFile({
		url: currentPoster.value,
		success: (res) => {
			if (res.statusCode === 200) {
				uni.saveImageToPhotosAlbum({
					filePath: res.tempFilePath,
					success: () => {
						uni.showToast({
							title: '保存成功',
							icon: 'success'
						});
					},
					fail: (err) => {
						console.error('保存失败:', err);
						
						if (err.errMsg.indexOf('auth deny') !== -1) {
							uni.showModal({
								title: '提示',
								content: '请授权保存图片到相册',
								success: (res) => {
									if (res.confirm) {
										uni.openSetting();
									}
								}
							});
						} else {
							uni.showToast({
								title: '保存失败',
								icon: 'none'
							});
						}
					}
				});
			} else {
				uni.showToast({
					title: '图片下载失败',
					icon: 'none'
				});
			}
		},
		fail: () => {
			uni.showToast({
				title: '网络错误，请重试',
				icon: 'none'
			});
		},
		complete: () => {
			uni.hideLoading();
		}
	});
};

// 分享海报
const sharePoster = () => {
	showShareModal.value = true;
};

// 显示模板选择弹窗
const showTemplateModal = () => {
	showModal.value = true;
};

// 隐藏模板选择弹窗
const hideTemplateModal = () => {
	showModal.value = false;
};

// 选择模板
const selectTemplate = (index) => {
	selectedTemplate.value = index;
};

// 确认选择模板
const confirmTemplate = () => {
	hideTemplateModal();
	generatePoster();
};

// 隐藏分享菜单
const hideShareModal = () => {
	showShareModal.value = false;
};

// 分享到微信
const shareToWechat = () => {
	// 实际分享逻辑
	uni.showToast({
		title: '分享成功',
		icon: 'success'
	});
	hideShareModal();
};

// 分享到朋友圈
const shareToMoments = () => {
	// 实际分享逻辑
	uni.showToast({
		title: '分享成功',
		icon: 'success'
	});
	hideShareModal();
};

// 分享到QQ
const shareToQQ = () => {
	// 实际分享逻辑
	uni.showToast({
		title: '分享成功',
		icon: 'success'
	});
	hideShareModal();
};

// 分享到微博
const shareToWeibo = () => {
	// 实际分享逻辑
	uni.showToast({
		title: '分享成功',
		icon: 'success'
	});
	hideShareModal();
};

// 获取佣金比例
const getCommissionRate = (level, partnerLevel) => {
	// 根据合伙人等级返回不同的佣金比例
	const commissionRates = {
		1: { 1: 5, 2: 2 },    // 普通合伙人：一级5%，二级2%
		2: { 1: 8, 2: 3 },    // 银牌合伙人：一级8%，二级3%
		3: { 1: 12, 2: 5 },   // 金牌合伙人：一级12%，二级5%
		4: { 1: 15, 2: 8 }    // 钻石合伙人：一级15%，二级8%
	};
	
	// 如果指定了合伙人等级，使用指定的等级
	if (partnerLevel) {
		return commissionRates[partnerLevel]?.[level] || 0;
	}
	
	// 否则使用当前用户的合伙人等级
	return commissionRates[partnerInfo.level]?.[level] || 0;
};

// 获取等级名称
const getLevelName = (level) => {
	const levelNames = {
		1: '普通合伙人',
		2: '银牌合伙人',
		3: '金牌合伙人',
		4: '钻石合伙人'
	};
	return levelNames[level] || '未知等级';
};

// 跳转到升级页面
const goToUpgrade = () => {
	// 跳转到合伙人升级页面
	uni.navigateTo({
		url: '/pages/new-partner/partner-upgrade',
		success: () => {
			console.log('跳转到合伙人升级页面成功');
		},
		fail: (err) => {
			console.error('跳转失败:', err);
			uni.showToast({
				title: '页面跳转失败',
				icon: 'none'
			});
		}
	});
};

// 生命周期钩子
onMounted(() => {
	// 获取用户信息
	getUserInfo();
	// 获取合伙人信息
	getPartnerInfo();
	// 获取推广数据
	getPromotionData();
	// 生成海报
	generatePoster();
});
</script>

<style lang="scss" scoped>
	.poster-container {
		min-height: 100vh;
		background-color: #F5F7FA;
		padding-bottom: 30rpx;
		padding-top: calc(44px + var(--status-bar-height));
	}
	
	/* 自定义导航栏 */
	.custom-navbar {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 88rpx;
		padding: 0 30rpx;
		padding-top: 44px; /* 状态栏高度 */
		position: fixed; /* 改为固定定位 */
		top: 0;
		left: 0;
		right: 0;
		background-image: linear-gradient(135deg, #0066FF, #0052CC); /* 改为与发布页一致的渐变角度 */
		box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.15);
		z-index: 100; /* 提高z-index确保在最上层 */
	}
	
	.navbar-title {
		position: absolute;
		left: 0;
		right: 0;
		font-size: 36rpx;
		font-weight: 700;
		font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
		text-align: center;
	}
	
	.navbar-right {
		width: 40rpx;
		height: 40rpx;
	}
	
	.navbar-left {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		z-index: 20; /* 确保在标题上层，可以被点击 */
	}
	
	.back-icon {
		width: 100%;
		height: 100%;
	}
	
	.safe-area-top {
		height: var(--status-bar-height);
		width: 100%;
		background-image: linear-gradient(135deg, #0066FF, #0052CC);
	}
	
	/* 海报预览区域 */
	.poster-preview {
		margin: 30rpx;
		border-radius: 20rpx;
		background-color: #fff;
		overflow: hidden;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
	}
	
	.preview-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.preview-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
	}
	
	.template-switch {
		display: flex;
		align-items: center;
		background-color: #E6F4FF;
		border-radius: 30rpx;
		padding: 12rpx 24rpx;
		box-shadow: 0 4rpx 12rpx rgba(22, 119, 255, 0.1);
		transition: all 0.3s;
	}
	
	.template-switch:active {
		transform: scale(0.95);
		background-color: #D1EBFF;
	}
	
	.switch-text {
		font-size: 24rpx;
		color: #1677FF;
		font-weight: 500;
	}
	
	.switch-icon {
		width: 28rpx;
		height: 28rpx;
		margin-right: 8rpx;
	}
	
	.poster-card {
		position: relative;
		width: 100%;
		padding: 30rpx;
		box-sizing: border-box;
		display: flex;
		justify-content: center;
	}
	
	.poster-image {
		width: 80%;
		display: block;
		border-radius: 16rpx;
		box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
		min-height: 400rpx;
		object-fit: contain;
	}
	
	.poster-loading {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(255, 255, 255, 0.9);
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		border-radius: 16rpx;
	}
	
	.loading-spinner {
		width: 80rpx;
		height: 80rpx;
		border: 8rpx solid #f3f3f3;
		border-top: 8rpx solid #1677FF;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin-bottom: 20rpx;
	}
	
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
	
	.loading-text {
		font-size: 30rpx;
		color: #333;
		font-weight: 500;
	}
	
	/* 新的美化后的按钮组样式 */
	.new-poster-actions {
		display: flex;
		justify-content: space-around;
		padding: 30rpx;
		border-top: 1rpx solid #f0f0f0;
		margin-top: 20rpx;
	}
	
	.action-button {
		display: flex;
		flex-direction: column;
		align-items: center;
		background-color: transparent;
		padding: 0;
		line-height: normal;
		width: 33%;
		height: auto;
	}
	
	.action-button::after {
		border: none;
	}
	
	.button-icon-wrap {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		background-color: #f0f5ff;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 12rpx;
		transition: all 0.3s;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}
	
	.save-icon-bg {
		background-color: #e6f7ff;
	}
	
	.share-icon-bg {
		background-color: #f6ffed;
	}
	
	.button-icon {
		width: 40rpx;
		height: 40rpx;
	}
	
	.button-text {
		font-size: 26rpx;
		color: #333;
		font-weight: 500;
	}
	
	.refresh-button:active .button-icon-wrap {
		transform: rotate(180deg);
	}
	
	.save-button:active .button-icon-wrap, 
	.share-button:active .button-icon-wrap {
		transform: scale(0.9);
	}
	
	/* 统计卡片 */
	.stats-card {
		margin: 30rpx;
		border-radius: 20rpx;
		background-color: #fff;
		overflow: hidden;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
	}
	
	.stats-header {
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.stats-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
	}
	
	.stats-content {
		display: flex;
		padding: 30rpx 0;
	}
	
	.stats-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.stats-value {
		font-size: 36rpx;
		font-weight: 600;
		color: #1677FF;
		margin-bottom: 10rpx;
	}
	
	.stats-label {
		font-size: 24rpx;
		color: #999999;
	}
	
	.stats-divider {
		width: 1rpx;
		height: 60rpx;
		background-color: #f0f0f0;
		align-self: center;
	}
	
	/* 佣金卡片 */
	.commission-card {
		margin: 30rpx;
		border-radius: 20rpx;
		background-color: #fff;
		overflow: hidden;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
	}
	
	.commission-header {
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.commission-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
	}
	
	.commission-content {
		padding: 20rpx 30rpx;
	}
	
	.commission-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
		
		&:last-child {
			border-bottom: none;
		}
	}
	
	.commission-info {
		flex: 1;
	}
	
	.commission-label {
		font-size: 30rpx;
		font-weight: 500;
		color: #333333;
		margin-bottom: 8rpx;
	}
	
	.commission-desc {
		font-size: 24rpx;
		color: #999999;
	}
	
	.commission-value {
		font-size: 40rpx;
		font-weight: 700;
		color: #FF6B00;
		background-color: rgba(255, 107, 0, 0.1);
		padding: 10rpx 24rpx;
		border-radius: 30rpx;
	}
	
	.level-tag {
		font-size: 24rpx;
		font-weight: 500;
		color: #FF6B00;
		background-color: rgba(255, 107, 0, 0.1);
		padding: 4rpx 12rpx;
		border-radius: 8rpx;
	}
	
	.level-benefits {
		margin-top: 20rpx;
		margin-bottom: 20rpx;
	}
	
	.benefit-item {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;
	}
	
	.benefit-icon-wrap {
		width: 40rpx;
		height: 40rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 10rpx;
	}
	
	.benefit-text {
		font-size: 26rpx;
		color: #333333;
		font-weight: 500;
	}
	
	.upgrade-tips {
		margin-top: 10rpx;
		text-align: right;
	}
	
	.tips-text {
		font-size: 24rpx;
		color: #999999;
	}
	
	.upgrade-btn {
		display: flex;
		align-items: center;
		background-color: #E6F4FF;
		border-radius: 30rpx;
		padding: 8rpx 20rpx;
		box-shadow: 0 4rpx 12rpx rgba(22, 119, 255, 0.1);
		transition: all 0.3s;
	}
	
	.upgrade-btn:active {
		transform: scale(0.95);
	}
	
	.upgrade-icon {
		width: 20rpx;
		height: 20rpx;
		margin-left: 8rpx;
	}
	
	/* 步骤卡片 */
	.steps-card {
		margin: 30rpx;
		border-radius: 20rpx;
		background-color: #fff;
		overflow: hidden;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
	}
	
	.steps-header {
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.steps-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
	}
	
	.steps-content {
		padding: 30rpx;
	}
	
	.step-item {
		display: flex;
		align-items: center;
	}
	
	.step-icon-wrap {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 30rpx;
		flex-shrink: 0;
	}
	
	.step1-bg {
		background-color: rgba(22, 119, 255, 0.1);
	}
	
	.step2-bg {
		background-color: rgba(255, 149, 0, 0.1);
	}
	
	.step3-bg {
		background-color: rgba(52, 199, 89, 0.1);
	}
	
	.step4-bg {
		background-color: rgba(255, 59, 48, 0.1);
	}
	
	.step-icon {
		width: 40rpx;
		height: 40rpx;
	}
	
	.step-info {
		flex: 1;
	}
	
	.step-name {
		font-size: 30rpx;
		font-weight: 500;
		color: #333333;
		margin-bottom: 8rpx;
	}
	
	.step-desc {
		font-size: 26rpx;
		color: #999999;
	}
	
	.step-connector {
		width: 2rpx;
		height: 40rpx;
		background-color: #E6E6E6;
		margin-left: 40rpx;
		margin-bottom: 10rpx;
		margin-top: 10rpx;
	}
	
	/* 规则卡片 */
	.rules-card {
		margin: 30rpx;
		border-radius: 20rpx;
		background-color: #fff;
		overflow: hidden;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
	}
	
	.rules-header {
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		display: flex;
		align-items: center;
	}
	
	.rules-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
		margin-right: 16rpx;
	}
	
	.rules-tag {
		font-size: 20rpx;
		color: #FF6B00;
		background-color: rgba(255, 107, 0, 0.1);
		padding: 4rpx 12rpx;
		border-radius: 8rpx;
	}
	
	.rules-content {
		padding: 20rpx 30rpx;
	}
	
	.rule-item {
		display: flex;
		align-items: flex-start;
		margin-bottom: 20rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
	}
	
	.rule-dot {
		width: 12rpx;
		height: 12rpx;
		background-color: #1677FF;
		border-radius: 50%;
		margin-top: 12rpx;
		margin-right: 15rpx;
		flex-shrink: 0;
	}
	
	.silver-dot {
		background-color: #C0C0C0;
	}
	
	.gold-dot {
		background-color: #FFD700;
	}
	
	.diamond-dot {
		background-color: #B9F2FF;
		box-shadow: 0 0 5rpx #B9F2FF;
	}
	
	.rule-text {
		font-size: 26rpx;
		color: #666666;
		line-height: 1.6;
		flex: 1;
	}
	
	.upgrade-action {
		padding: 20rpx 30rpx 30rpx;
		display: flex;
		justify-content: center;
	}
	
	.upgrade-now-btn {
		width: 80%;
		height: 80rpx;
		line-height: 80rpx;
		background-image: linear-gradient(135deg, #FF9500, #FF6B00);
		color: #fff;
		font-size: 30rpx;
		font-weight: 500;
		border-radius: 40rpx;
		box-shadow: 0 6rpx 12rpx rgba(255, 107, 0, 0.2);
		transition: all 0.3s;
	}
	
	.upgrade-now-btn:active {
		transform: scale(0.98);
		box-shadow: 0 3rpx 6rpx rgba(255, 107, 0, 0.3);
	}
	
	/* 模板选择弹窗 */
	.template-modal, .share-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: flex-end;
		z-index: 999;
	}
	
	.template-modal {
		align-items: center;
	}
	
	.share-content {
		width: 100%;
		background-color: #fff;
		border-radius: 24rpx 24rpx 0 0;
		overflow: hidden;
		animation: slideUp 0.3s ease-out;
	}
	
	@keyframes slideUp {
		from {
			transform: translateY(100%);
		}
		to {
			transform: translateY(0);
		}
	}
	
	.share-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.share-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
	}
	
	.close-btn {
		font-size: 40rpx;
		color: #999;
		line-height: 1;
		padding: 10rpx;
	}
	
	.share-options {
		display: flex;
		flex-wrap: wrap;
		padding: 40rpx 20rpx 20rpx;
	}
	
	.share-option {
		width: 25%;
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 40rpx;
	}
	
	.share-icon-wrap {
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 16rpx;
		transition: all 0.3s;
	}
	
	.wechat-bg {
		background-color: #95EC69;
	}
	
	.moments-bg {
		background-color: #FFD666;
	}
	
	.qq-bg {
		background-color: #91D5FF;
	}
	
	.weibo-bg {
		background-color: #FFA39E;
	}
	
	.share-option:active .share-icon-wrap {
		transform: scale(0.9);
	}
	
	.share-icon {
		width: 50rpx;
		height: 50rpx;
	}
	
	.share-name {
		font-size: 26rpx;
		color: #333333;
		font-weight: 500;
	}
	
	.cancel-btn {
		width: 100%;
		height: 100rpx;
		line-height: 100rpx;
		background-color: #fff;
		color: #333333;
		font-size: 32rpx;
		font-weight: 500;
		border-radius: 0;
		border-top: 10rpx solid #f5f5f7;
	}
	
	/* 底部操作按钮 */
	.bottom-actions {
		padding: 30rpx;
		margin-bottom: 30rpx;
	}
	
	.generate-btn {
		width: 100%;
		height: 90rpx;
		line-height: 90rpx;
		background-image: linear-gradient(135deg, #0066FF, #0052CC);
		color: #fff;
		font-size: 32rpx;
		font-weight: 500;
		border-radius: 45rpx;
		box-shadow: 0 8rpx 16rpx rgba(0, 82, 204, 0.2);
		position: relative;
		overflow: hidden;
		transition: all 0.3s;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.btn-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 12rpx;
	}
	
	.btn-text {
		font-weight: 600;
		letter-spacing: 2rpx;
	}
	
	.generate-btn::after {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background-image: linear-gradient(120deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.5) 50%, rgba(255,255,255,0) 100%);
		opacity: 0.6;
		transition: all 0.8s;
	}
	
	.generate-btn:active {
		transform: scale(0.98);
		box-shadow: 0 4rpx 8rpx rgba(0, 82, 204, 0.3);
	}
	
	.generate-btn:active::after {
		left: 100%;
	}
	
	/* 模板选择弹窗 */
	.modal-content {
		width: 80%;
		background-color: #fff;
		border-radius: 20rpx;
		overflow: hidden;
		animation: fadeIn 0.3s ease-out;
	}
	
	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: scale(0.9);
		}
		to {
			opacity: 1;
			transform: scale(1);
		}
	}
	
	.modal-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.modal-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
	}
	
	.template-list {
		display: flex;
		flex-wrap: wrap;
		padding: 20rpx;
		max-height: 600rpx;
	}
	
	.template-item {
		position: relative;
		width: calc(50% - 20rpx);
		margin: 10rpx;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
		background-color: #f5f7fa;
		transition: all 0.3s;
	}
	
	.template-item:active {
		transform: scale(0.98);
	}
	
	.template-item.active {
		box-shadow: 0 4rpx 16rpx rgba(22, 119, 255, 0.3);
	}
	
	.template-image {
		width: 100%;
		height: 300rpx;
		display: block;
		object-fit: contain;
	}
	
	.template-mask {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(22, 119, 255, 0.3);
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.check-icon {
		width: 60rpx;
		height: 60rpx;
	}
	
	.confirm-btn {
		width: 100%;
		height: 90rpx;
		line-height: 90rpx;
		background-color: #1677FF;
		color: #fff;
		font-size: 30rpx;
		font-weight: 500;
		border-radius: 0;
	}
</style>