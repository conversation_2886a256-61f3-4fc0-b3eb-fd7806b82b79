<view class="page-container"><view class="navbar"><view class="navbar-back" bindtap="{{a}}"><view class="back-icon"></view></view><text class="navbar-title">裂变红包</text><view class="navbar-right"><view class="help-icon" bindtap="{{b}}">?</view></view></view><view class="overview-section"><view class="overview-header"><text class="section-title">裂变数据概览</text><view class="date-picker" bindtap="{{d}}"><text class="date-text">{{c}}</text><view class="date-icon"></view></view></view><view class="stats-cards"><view class="stats-card"><view class="card-value">{{e}}</view><view class="card-label">裂变活动数</view><view class="{{['card-trend', g]}}"><view class="trend-arrow"></view><text class="trend-value">{{f}}</text></view></view><view class="stats-card"><view class="card-value">{{h}}</view><view class="card-label">参与人数</view><view class="{{['card-trend', j]}}"><view class="trend-arrow"></view><text class="trend-value">{{i}}</text></view></view><view class="stats-card"><view class="card-value">{{k}}</view><view class="card-label">平均分享数</view><view class="{{['card-trend', m]}}"><view class="trend-arrow"></view><text class="trend-value">{{l}}</text></view></view><view class="stats-card"><view class="card-value">{{n}}%</view><view class="card-label">转化率</view><view class="{{['card-trend', p]}}"><view class="trend-arrow"></view><text class="trend-value">{{o}}</text></view></view></view></view><view class="fission-list-section"><view class="section-header"><text class="section-title">裂变活动</text><view class="add-btn" bindtap="{{q}}"><text class="btn-text">创建活动</text><view class="plus-icon-small"></view></view></view><view class="tab-header"><view wx:for="{{r}}" wx:for-item="tab" wx:key="b" class="{{['tab-item', tab.c && 'active']}}" bindtap="{{tab.d}}"><text class="tab-text">{{tab.a}}</text></view></view><view class="fission-list"><view wx:for="{{s}}" wx:for-item="item" wx:key="r" class="fission-item" bindtap="{{item.s}}"><view class="fission-header"><view class="fission-title">{{item.a}}</view><view class="{{['fission-status', item.c]}}">{{item.b}}</view></view><view class="fission-content"><view class="fission-icon"></view><view class="fission-info"><text class="fission-time">{{item.d}}</text><view class="fission-rules"><text class="rules-label">裂变规则: </text><text class="rules-value">分享{{item.e}}人可得{{item.f}}元</text></view><view class="fission-amount"><text class="amount-label">红包金额: </text><text class="amount-value">¥{{item.g}}/个</text></view></view></view><view class="fission-stats"><view class="stat-row"><text class="stat-label">参与人数:</text><text class="stat-value">{{item.h}}人</text></view><view class="stat-row"><text class="stat-label">分享次数:</text><text class="stat-value">{{item.i}}次</text></view><view class="stat-row"><text class="stat-label">新增用户:</text><text class="stat-value">{{item.j}}人</text></view></view><view class="fission-actions"><view class="action-btn" catchtap="{{item.k}}">详情</view><view wx:if="{{item.l}}" class="action-btn" catchtap="{{item.m}}">编辑</view><view wx:if="{{item.n}}" class="action-btn" catchtap="{{item.o}}">分享</view><view wx:if="{{item.p}}" class="action-btn delete" catchtap="{{item.q}}">删除</view></view></view><view wx:if="{{t}}" class="empty-state"><view class="empty-icon"></view><text class="empty-text">暂无{{v}}裂变活动</text></view></view></view><view class="templates-section"><view class="section-header"><text class="section-title">裂变模板</text></view><scroll-view scroll-x class="templates-scroll" show-scrollbar="false"><view class="templates-container"><view wx:for="{{w}}" wx:for-item="template" wx:key="e" class="template-card" bindtap="{{template.f}}"><view class="template-preview" style="{{'background:' + template.c}}"><view class="template-icon"><image class="icon-image" src="{{template.a}}" mode="aspectFit"></image></view><text class="template-name">{{template.b}}</text></view><view class="template-footer"><text class="template-desc">{{template.d}}</text><view class="template-use-btn">使用</view></view></view></view></scroll-view></view><view class="strategy-section"><view class="section-header"><text class="section-title">裂变攻略</text></view><view class="strategy-list"><view wx:for="{{x}}" wx:for-item="strategy" wx:key="e" class="strategy-item" bindtap="{{strategy.f}}"><view class="strategy-icon" style="{{'background:' + strategy.b}}"><image class="icon-image" src="{{strategy.a}}" mode="aspectFit"></image></view><view class="strategy-content"><text class="strategy-title">{{strategy.c}}</text><text class="strategy-desc">{{strategy.d}}</text></view><view class="strategy-arrow"></view></view></view></view><view class="floating-action-button" bindtap="{{y}}"><view class="fab-icon">+</view></view></view>