<template>
  <view class="distributors-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">分销员管理</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 搜索筛选区 -->
    <view class="search-filter">
      <view class="search-bar">
        <view class="search-input-wrap">
          <view class="search-icon"></view>
          <input 
            class="search-input" 
            type="text" 
            v-model="searchParams.keyword" 
            placeholder="搜索分销员姓名/手机号" 
            confirm-type="search"
            @confirm="searchDistributors"
          />
          <view class="clear-icon" v-if="searchParams.keyword" @click="clearSearch"></view>
        </view>
        <view class="search-btn" @click="searchDistributors">搜索</view>
      </view>
      
      <view class="filter-options">
        <view class="filter-item">
          <picker 
            mode="selector" 
            :range="statusOptions" 
            range-key="name"
            :value="statusIndex"
            @change="onStatusChange"
          >
            <view class="picker-value">
              <text>{{statusOptions[statusIndex].name}}</text>
              <view class="arrow-icon"></view>
            </view>
          </picker>
        </view>
        
        <view class="filter-item">
          <picker 
            mode="selector" 
            :range="levelOptions" 
            range-key="name"
            :value="levelIndex"
            @change="onLevelChange"
          >
            <view class="picker-value">
              <text>{{levelOptions[levelIndex].name}}</text>
              <view class="arrow-icon"></view>
            </view>
          </picker>
        </view>
        
        <view class="filter-item">
          <picker 
            mode="selector" 
            :range="sortOptions" 
            range-key="name"
            :value="sortIndex"
            @change="onSortChange"
          >
            <view class="picker-value">
              <text>{{sortOptions[sortIndex].name}}</text>
              <view class="arrow-icon"></view>
            </view>
          </picker>
        </view>
      </view>
    </view>
    
    <!-- 内容区域 -->
    <view class="content-area">
      <!-- 分销员列表 -->
      <view class="distributors-list" v-if="distributors.length > 0">
        <view 
          v-for="(item, index) in distributors" 
          :key="index" 
          class="distributor-card"
        >
          <view class="distributor-header">
            <view class="distributor-info">
              <image class="avatar" :src="item.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
              <view class="info-content">
                <view class="name-wrap">
                  <text class="name">{{item.name}}</text>
                  <text class="level-tag" :style="{ backgroundColor: item.levelColor || '#67C23A' }">{{item.levelName}}</text>
                </view>
                <text class="phone">{{item.phone}}</text>
              </view>
            </view>
            <view class="status-tag" :class="getStatusClass(item.status)">
              {{getStatusText(item.status)}}
            </view>
          </view>
          
          <view class="distributor-stats">
            <view class="stat-item">
              <text class="stat-value">{{item.orderCount || 0}}</text>
              <text class="stat-label">推广订单</text>
            </view>
            <view class="stat-item">
              <text class="stat-value">¥{{formatAmount(item.commissionTotal)}}</text>
              <text class="stat-label">累计佣金</text>
            </view>
            <view class="stat-item">
              <text class="stat-value">{{item.teamCount || 0}}</text>
              <text class="stat-label">团队人数</text>
            </view>
          </view>
          
          <view class="distributor-footer">
            <view class="time">注册时间：{{formatDate(item.createdAt)}}</view>
            <view class="actions">
              <view class="action-btn detail" @click="viewDetail(item)">详情</view>
              
              <block v-if="item.status === 'pending'">
                <view class="action-btn approve" @click="approveDistributor(item)">通过</view>
                <view class="action-btn reject" @click="rejectDistributor(item)">拒绝</view>
              </block>
              
              <block v-else-if="item.status === 'active'">
                <view class="action-btn disable" @click="disableDistributor(item)">禁用</view>
                <view class="action-btn set-level" @click="setLevel(item)">设置等级</view>
              </block>
              
              <block v-else-if="item.status === 'disabled'">
                <view class="action-btn enable" @click="enableDistributor(item)">启用</view>
              </block>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-else-if="!loading">
        <image class="empty-image" src="/static/images/empty-data.png" mode="aspectFit"></image>
        <text class="empty-text">暂无分销员数据</text>
      </view>
      
      <!-- 加载中 -->
      <view class="loading-state" v-if="loading">
        <view class="loading-icon"></view>
        <text class="loading-text">加载中...</text>
      </view>
      
      <!-- 分页 -->
      <view class="pagination" v-if="distributors.length > 0 && !loading">
        <view class="page-info">
          <text>共 {{pagination.total}} 条记录，当前 {{pagination.current}}/{{pagination.totalPages}} 页</text>
        </view>
        <view class="page-actions">
          <view 
            class="page-btn prev" 
            :class="{ 'disabled': pagination.current <= 1 }"
            @click="prevPage"
          >上一页</view>
          <view 
            class="page-btn next" 
            :class="{ 'disabled': pagination.current >= pagination.totalPages }"
            @click="nextPage"
          >下一页</view>
        </view>
      </view>
    </view>
    
    <!-- 设置等级弹窗 -->
    <view class="level-modal" v-if="showLevelModal">
      <view class="modal-mask" @click="closeLevelModal"></view>
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">设置分销员等级</text>
          <view class="close-icon" @click="closeLevelModal"></view>
        </view>
        
        <view class="modal-distributor">
          <image class="avatar" :src="selectedDistributor.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
          <text class="name">{{selectedDistributor.name}}</text>
        </view>
        
        <view class="level-list">
          <view 
            v-for="(level, idx) in levelOptions.slice(1)" 
            :key="idx" 
            class="level-item-select"
            :class="{ 'active': selectedLevel === level.value }"
            @click="selectedLevel = level.value"
          >
            <view class="level-radio">
              <view class="radio-inner" v-if="selectedLevel === level.value"></view>
            </view>
            <text class="level-name">{{level.name}}</text>
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="cancel-btn" @click="closeLevelModal">取消</button>
          <button class="submit-btn" @click="confirmSetLevel">确定</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import distributionService from '@/utils/distributionService';

// 搜索参数
const searchParams = reactive({
  keyword: '',
  status: 'all',
  level: 'all',
  sortBy: 'created_at',
  sortOrder: 'desc',
  page: 1,
  pageSize: 10
});

// 状态选项
const statusOptions = [
  { value: 'all', name: '全部状态' },
  { value: 'active', name: '已启用' },
  { value: 'disabled', name: '已禁用' },
  { value: 'pending', name: '待审核' },
  { value: 'rejected', name: '已拒绝' }
];

// 等级选项
const levelOptions = [
  { value: 'all', name: '全部等级' },
  { value: '1', name: '普通分销员' },
  { value: '2', name: '高级分销员' },
  { value: '3', name: '金牌分销员' }
];

// 排序选项
const sortOptions = [
  { value: { field: 'created_at', order: 'desc' }, name: '注册时间降序' },
  { value: { field: 'created_at', order: 'asc' }, name: '注册时间升序' },
  { value: { field: 'commission_total', order: 'desc' }, name: '佣金总额降序' },
  { value: { field: 'commission_total', order: 'asc' }, name: '佣金总额升序' },
  { value: { field: 'order_count', order: 'desc' }, name: '订单数量降序' },
  { value: { field: 'order_count', order: 'asc' }, name: '订单数量升序' }
];

// 当前选中的状态索引
const statusIndex = ref(0);

// 当前选中的等级索引
const levelIndex = ref(0);

// 当前选中的排序索引
const sortIndex = ref(0);

// 分销员列表
const distributors = ref([]);

// 分页信息
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0
});

// 加载状态
const loading = ref(false);

// 等级设置弹窗
const showLevelModal = ref(false);

// 选中的分销员
const selectedDistributor = ref({});

// 选中的等级
const selectedLevel = ref('');

// 页面加载
onMounted(async () => {
  // 获取分销等级列表
  await getDistributionLevels();
  
  // 获取分销员列表
  await getDistributorsList();
});

// 获取分销等级列表
const getDistributionLevels = async () => {
  try {
    const result = await distributionService.getDistributionLevels();
    
    if (result && result.levels && result.levels.length > 0) {
      // 更新等级选项
      levelOptions.length = 1; // 保留"全部等级"选项
      
      result.levels.forEach(level => {
        levelOptions.push({
          value: level.id.toString(),
          name: level.name
        });
      });
    }
  } catch (error) {
    console.error('获取分销等级列表失败', error);
  }
};

// 获取分销员列表
const getDistributorsList = async (loadMore = false) => {
  try {
    loading.value = true;
    
    const params = {
      ...searchParams,
      page: searchParams.page,
      pageSize: searchParams.pageSize
    };
    
    const result = await distributionService.getDistributorsList(params);
    
    if (result) {
      distributors.value = result.list || [];
      
      // 更新分页信息
      pagination.current = result.pagination.current;
      pagination.total = result.pagination.total;
      pagination.totalPages = result.pagination.totalPages;
    }
  } catch (error) {
    console.error('获取分销员列表失败', error);
    uni.showToast({
      title: '获取分销员列表失败',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
};

// 搜索分销员
const searchDistributors = () => {
  searchParams.page = 1;
  getDistributorsList();
};

// 清除搜索
const clearSearch = () => {
  searchParams.keyword = '';
  searchDistributors();
};

// 状态变化
const onStatusChange = (e) => {
  const index = e.detail.value;
  statusIndex.value = index;
  searchParams.status = statusOptions[index].value;
  searchDistributors();
};

// 等级变化
const onLevelChange = (e) => {
  const index = e.detail.value;
  levelIndex.value = index;
  searchParams.level = levelOptions[index].value;
  searchDistributors();
};

// 排序变化
const onSortChange = (e) => {
  const index = e.detail.value;
  sortIndex.value = index;
  const sortOption = sortOptions[index].value;
  searchParams.sortBy = sortOption.field;
  searchParams.sortOrder = sortOption.order;
  searchDistributors();
};

// 返回上一页
const goBack = () => {
      uni.navigateBack();
};

// 显示帮助
const showHelp = () => {
  uni.showModal({
    title: '分销员管理帮助',
    content: '在此页面您可以查看和管理所有分销员，包括审核申请、设置等级、启用或禁用分销员等操作。',
    showCancel: false
  });
};

// 格式化金额
const formatAmount = (amount) => {
  return (amount || 0).toFixed(2);
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
};

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case 'active':
      return 'status-active';
    case 'disabled':
      return 'status-disabled';
    case 'pending':
      return 'status-pending';
    case 'rejected':
      return 'status-rejected';
    default:
      return '';
  }
};

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'active':
      return '已启用';
    case 'disabled':
      return '已禁用';
    case 'pending':
      return '待审核';
    case 'rejected':
      return '已拒绝';
    default:
      return '未知';
  }
};

// 查看详情
const viewDetail = (item) => {
  uni.navigateTo({
    url: `/subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail?id=${item.id}`
  });
};

// 审核通过
const approveDistributor = (item) => {
  uni.showModal({
    title: '审核通过',
    content: `确定通过 ${item.name} 的分销员申请吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({
            title: '处理中...',
            mask: true
          });
          
          const result = await distributionService.reviewDistributorApplication({
            id: item.id,
            status: 'approved'
          });
          
          uni.hideLoading();
          
          if (result.success) {
            uni.showToast({
              title: '审核通过成功',
              icon: 'success'
            });
            
            // 刷新列表
            getDistributorsList();
          } else {
            uni.showModal({
              title: '审核失败',
              content: result.message || '请稍后再试',
              showCancel: false
            });
          }
        } catch (error) {
          uni.hideLoading();
          console.error('审核失败', error);
          uni.showToast({
            title: '审核失败',
            icon: 'none'
          });
        }
      }
    }
  });
};

// 拒绝申请
const rejectDistributor = (item) => {
  uni.showModal({
    title: '拒绝申请',
    content: `确定拒绝 ${item.name} 的分销员申请吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({
            title: '处理中...',
            mask: true
          });
          
          const result = await distributionService.reviewDistributorApplication({
            id: item.id,
            status: 'rejected'
          });
          
          uni.hideLoading();
          
          if (result.success) {
            uni.showToast({
              title: '已拒绝申请',
              icon: 'success'
            });
            
            // 刷新列表
            getDistributorsList();
          } else {
            uni.showModal({
              title: '操作失败',
              content: result.message || '请稍后再试',
              showCancel: false
            });
          }
        } catch (error) {
          uni.hideLoading();
          console.error('操作失败', error);
          uni.showToast({
            title: '操作失败',
            icon: 'none'
          });
        }
      }
    }
  });
};

// 禁用分销员
const disableDistributor = (item) => {
  uni.showModal({
    title: '禁用分销员',
    content: `确定禁用 ${item.name} 的分销员资格吗？禁用后该用户将无法进行分销活动。`,
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({
            title: '处理中...',
            mask: true
          });
          
          const result = await distributionService.toggleDistributorStatus({
            id: item.id,
            status: 'disabled'
          });
          
          uni.hideLoading();
          
          if (result.success) {
            uni.showToast({
              title: '禁用成功',
              icon: 'success'
            });
            
            // 刷新列表
            getDistributorsList();
          } else {
            uni.showModal({
              title: '操作失败',
              content: result.message || '请稍后再试',
              showCancel: false
            });
          }
        } catch (error) {
          uni.hideLoading();
          console.error('操作失败', error);
          uni.showToast({
            title: '操作失败',
            icon: 'none'
          });
        }
      }
    }
  });
};

// 启用分销员
const enableDistributor = (item) => {
  uni.showModal({
    title: '启用分销员',
    content: `确定启用 ${item.name} 的分销员资格吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({
            title: '处理中...',
            mask: true
          });
          
          const result = await distributionService.toggleDistributorStatus({
            id: item.id,
            status: 'active'
          });
          
          uni.hideLoading();
          
          if (result.success) {
            uni.showToast({
              title: '启用成功',
              icon: 'success'
            });
            
            // 刷新列表
            getDistributorsList();
          } else {
            uni.showModal({
              title: '操作失败',
              content: result.message || '请稍后再试',
              showCancel: false
            });
          }
        } catch (error) {
          uni.hideLoading();
          console.error('操作失败', error);
          uni.showToast({
            title: '操作失败',
            icon: 'none'
          });
        }
      }
    }
  });
};

// 设置等级
const setLevel = (item) => {
  selectedDistributor.value = item;
  selectedLevel.value = item.levelId ? item.levelId.toString() : '1';
  showLevelModal.value = true;
};

// 关闭等级弹窗
const closeLevelModal = () => {
  showLevelModal.value = false;
};

// 确认设置等级
const confirmSetLevel = async () => {
  try {
    uni.showLoading({
      title: '设置中...',
      mask: true
    });
    
    const result = await distributionService.setDistributorLevel({
      id: selectedDistributor.value.id,
      levelId: selectedLevel.value
    });
    
    uni.hideLoading();
    
    if (result.success) {
      uni.showToast({
        title: '设置成功',
        icon: 'success'
      });
      
      closeLevelModal();
      
      // 刷新列表
      getDistributorsList();
    } else {
      uni.showModal({
        title: '设置失败',
        content: result.message || '请稍后再试',
        showCancel: false
      });
    }
  } catch (error) {
    uni.hideLoading();
    console.error('设置等级失败', error);
    uni.showToast({
      title: '设置失败',
      icon: 'none'
    });
  }
};

// 上一页
const prevPage = () => {
  if (pagination.current > 1) {
    searchParams.page = pagination.current - 1;
    getDistributorsList();
  }
};

// 下一页
const nextPage = () => {
  if (pagination.current < pagination.totalPages) {
    searchParams.page = pagination.current + 1;
    getDistributorsList();
  }
};
</script>

<style lang="scss">
.distributors-container {
  min-height: 100vh;
  background-color: #F5F7FA;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 88rpx 32rpx 30rpx;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);
}

.navbar-back {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24rpx;
  height: 24rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.navbar-right {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

/* 搜索筛选区 */
.search-filter {
  background: #FFFFFF;
  padding: 20rpx 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.search-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.search-input-wrap {
  flex: 1;
  height: 72rpx;
  background: #F5F7FA;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  margin-right: 20rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  background-color: #6B0FBE;
  border-radius: 50%;
  position: relative;
  margin-right: 10rpx;
}

.search-icon::before {
  content: '';
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  border: 2rpx solid white;
  border-radius: 50%;
  top: 6rpx;
  left: 6rpx;
}

.search-icon::after {
  content: '';
  position: absolute;
  width: 8rpx;
  height: 2rpx;
  background-color: white;
  transform: rotate(45deg);
  bottom: 8rpx;
  right: 6rpx;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: #333;
}

.clear-icon {
  width: 32rpx;
  height: 32rpx;
  background-color: #909399;
  border-radius: 50%;
  position: relative;
}

.clear-icon::before {
  content: '';
  position: absolute;
  width: 16rpx;
  height: 2rpx;
  background-color: white;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
}

.clear-icon::after {
  content: '';
  position: absolute;
  width: 16rpx;
  height: 2rpx;
  background-color: white;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
}

.search-btn {
  font-size: 28rpx;
  color: #6B0FBE;
}

.filter-options {
  display: flex;
  justify-content: space-between;
}

.filter-item {
  flex: 1;
  margin: 0 10rpx;
}

.filter-item:first-child {
  margin-left: 0;
}

.filter-item:last-child {
  margin-right: 0;
}

.picker-value {
  height: 72rpx;
  background: #F5F7FA;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #333;
  padding: 0 20rpx;
}

.arrow-icon {
  width: 16rpx;
  height: 16rpx;
  border-right: 2rpx solid #999;
  border-bottom: 2rpx solid #999;
  transform: rotate(45deg);
  margin-left: 10rpx;
}

/* 内容区域 */
.content-area {
  padding: 20rpx 30rpx;
}

/* 分销员列表 */
.distributors-list {
  margin-bottom: 30rpx;
}

.distributor-card {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.distributor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.distributor-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}

.info-content {
  flex: 1;
}

.name-wrap {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-right: 16rpx;
}

.level-tag {
  font-size: 22rpx;
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.phone {
  font-size: 24rpx;
  color: #999;
}

.status-tag {
  font-size: 24rpx;
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.status-active {
  background-color: #67C23A;
}

.status-disabled {
  background-color: #909399;
}

.status-pending {
  background-color: #E6A23C;
}

.status-rejected {
  background-color: #F56C6C;
}

.distributor-stats {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

.distributor-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.time {
  font-size: 24rpx;
  color: #999;
}

.actions {
  display: flex;
}

.action-btn {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  margin-left: 16rpx;
}

.action-btn.detail {
  background-color: #F5F7FA;
  color: #333;
}

.action-btn.approve {
  background-color: #67C23A;
  color: #fff;
}

.action-btn.reject {
  background-color: #F56C6C;
  color: #fff;
}

.action-btn.disable {
  background-color: #909399;
  color: #fff;
}

.action-btn.enable {
  background-color: #409EFF;
  color: #fff;
}

.action-btn.set-level {
  background-color: #6B0FBE;
  color: #fff;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载中 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50rpx 0;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 20rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #6B0FBE;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30rpx;
  padding: 20rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.page-info {
  font-size: 24rpx;
  color: #999;
}

.page-actions {
  display: flex;
}

.page-btn {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  margin-left: 16rpx;
  background-color: #6B0FBE;
  color: #fff;
}

.page-btn.disabled {
  background-color: #f5f5f5;
  color: #999;
}

/* 等级设置弹窗 */
.level-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-icon {
  width: 40rpx;
  height: 40rpx;
  position: relative;
}

.close-icon::before,
.close-icon::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 32rpx;
  height: 2rpx;
  background-color: #999;
}

.close-icon::before {
  transform: translate(-50%, -50%) rotate(45deg);
}

.close-icon::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.modal-distributor {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.modal-distributor .avatar {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 16rpx;
  margin-right: 0;
}

.modal-distributor .name {
  margin-right: 0;
}

.level-list {
  margin-bottom: 30rpx;
}

.level-item-select {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.level-item-select:last-child {
  border-bottom: none;
}

.level-radio {
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  border: 2rpx solid #ddd;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.level-item-select.active .level-radio {
  border-color: #6B0FBE;
}

.radio-inner {
  width: 24rpx;
  height: 24rpx;
  border-radius: 12rpx;
  background-color: #6B0FBE;
}

.level-item-select .level-name {
  font-size: 28rpx;
  color: #333;
  margin-right: 0;
}

.modal-footer {
  display: flex;
  justify-content: space-between;
}

.cancel-btn,
.submit-btn {
  width: 48%;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.cancel-btn {
  background: #F5F7FA;
  color: #666;
}

.submit-btn {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
}
</style>