<view class="progress-container"><view class="progress-steps"><view wx:for="{{a}}" wx:for-item="step" wx:key="f" class="{{['step', step.g && 'active', step.h && 'completed']}}"><view wx:if="{{step.a}}" class="{{['step-line', step.b && 'active']}}"></view><view class="step-content"><view class="step-dot"><text wx:if="{{step.c}}">{{step.d}}</text><view wx:else class="check-icon"></view></view><text class="step-label">{{step.e}}</text></view></view></view></view>