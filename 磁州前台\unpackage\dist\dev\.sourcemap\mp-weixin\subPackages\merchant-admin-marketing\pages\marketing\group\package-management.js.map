{"version": 3, "file": "package-management.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/group/package-management.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xncm91cFxwYWNrYWdlLW1hbmFnZW1lbnQudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"package-management-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">团购套餐管理</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 页面内容 -->\r\n    <scroll-view scroll-y class=\"page-content\">\r\n      <!-- 套餐数据概览 -->\r\n      <view class=\"overview-section\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">套餐数据概览</text>\r\n          <view class=\"date-picker\" @click=\"showDatePicker\">\r\n            <text class=\"date-text\">{{dateRange}}</text>\r\n            <view class=\"date-icon\"></view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"stats-cards\">\r\n          <view class=\"stats-card\">\r\n            <view class=\"card-value\">{{packageData.totalPackages}}</view>\r\n            <view class=\"card-label\">套餐总数</view>\r\n            <view class=\"card-trend\" :class=\"packageData.packagesTrend\">\r\n              <view class=\"trend-arrow\"></view>\r\n              <text class=\"trend-value\">{{packageData.packagesGrowth}}</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"stats-card\">\r\n            <view class=\"card-value\">{{packageData.salesCount}}</view>\r\n            <view class=\"card-label\">销售数量</view>\r\n            <view class=\"card-trend\" :class=\"packageData.salesTrend\">\r\n              <view class=\"trend-arrow\"></view>\r\n              <text class=\"trend-value\">{{packageData.salesGrowth}}</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"stats-card\">\r\n            <view class=\"card-value\">¥{{formatNumber(packageData.totalRevenue)}}</view>\r\n            <view class=\"card-label\">套餐收入</view>\r\n            <view class=\"card-trend\" :class=\"packageData.revenueTrend\">\r\n              <view class=\"trend-arrow\"></view>\r\n              <text class=\"trend-value\">{{packageData.revenueGrowth}}</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"stats-card\">\r\n            <view class=\"card-value\">{{packageData.conversionRate}}%</view>\r\n            <view class=\"card-label\">转化率</view>\r\n            <view class=\"card-trend\" :class=\"packageData.conversionTrend\">\r\n              <view class=\"trend-arrow\"></view>\r\n              <text class=\"trend-value\">{{packageData.conversionGrowth}}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 套餐列表 -->\r\n      <view class=\"packages-section\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">套餐列表</text>\r\n          <view class=\"filter-dropdown\" @click=\"showFilterOptions\">\r\n            <text class=\"filter-text\">{{currentFilter}}</text>\r\n            <view class=\"filter-icon\"></view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"packages-list\">\r\n          <view class=\"package-item\" v-for=\"(packageItem, index) in packagesList\" :key=\"index\" @click=\"viewPackageDetail(packageItem)\">\r\n            <view class=\"package-header\">\r\n              <view class=\"package-title-row\">\r\n                <text class=\"package-name\">{{packageItem.name}}</text>\r\n                <view class=\"package-status\" :class=\"packageItem.statusClass\">{{packageItem.statusText}}</view>\r\n              </view>\r\n              <text class=\"package-desc\">{{packageItem.description}}</text>\r\n            </view>\r\n            \r\n            <view class=\"package-content\">\r\n              <view class=\"package-items\">\r\n                <text class=\"package-items-text\">{{packageItem.itemsText}}</text>\r\n              </view>\r\n              \r\n              <view class=\"package-price-info\">\r\n                <view class=\"price-row\">\r\n                  <text class=\"price-label\">原价:</text>\r\n                  <text class=\"price-original\">¥{{packageItem.originalPrice}}</text>\r\n                </view>\r\n                <view class=\"price-row\">\r\n                  <text class=\"price-label\">拼团价:</text>\r\n                  <text class=\"price-group\">¥{{packageItem.groupPrice}}</text>\r\n                </view>\r\n                <view class=\"price-row\">\r\n                  <text class=\"price-label\">节省:</text>\r\n                  <text class=\"price-save\">¥{{packageItem.savingsAmount}}</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n            \r\n            <view class=\"package-footer\">\r\n              <view class=\"package-stats\">\r\n                <view class=\"stat-item\">\r\n                  <text class=\"stat-value\">{{packageItem.salesCount}}</text>\r\n                  <text class=\"stat-label\">销量</text>\r\n                </view>\r\n                <view class=\"stat-item\">\r\n                  <text class=\"stat-value\">{{packageItem.viewCount}}</text>\r\n                  <text class=\"stat-label\">浏览</text>\r\n                </view>\r\n                <view class=\"stat-item\">\r\n                  <text class=\"stat-value\">{{packageItem.conversionRate}}%</text>\r\n                  <text class=\"stat-label\">转化率</text>\r\n                </view>\r\n              </view>\r\n              \r\n              <view class=\"package-actions\">\r\n                <view class=\"action-button edit\" @click.stop=\"editPackage(packageItem)\">\r\n                  <view class=\"action-icon edit-icon\"></view>\r\n                  <text class=\"action-text\">编辑</text>\r\n                </view>\r\n                <view class=\"action-button share\" @click.stop=\"sharePackage(packageItem)\">\r\n                  <view class=\"action-icon share-icon\"></view>\r\n                  <text class=\"action-text\">分享</text>\r\n                </view>\r\n                <view class=\"action-button more\" @click.stop=\"showMoreActions(packageItem)\">\r\n                  <view class=\"action-icon more-icon\"></view>\r\n                  <text class=\"action-text\">更多</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 无数据提示 -->\r\n        <view class=\"empty-state\" v-if=\"packagesList.length === 0\">\r\n          <image class=\"empty-image\" src=\"/static/images/empty-packages.png\" mode=\"aspectFit\"></image>\r\n          <text class=\"empty-text\">暂无团购套餐</text>\r\n          <text class=\"empty-subtext\">点击下方按钮创建您的第一个团购套餐</text>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n    \r\n    <!-- 浮动操作按钮 -->\r\n    <view class=\"floating-action-button\" @click=\"showCreateSteps\">\r\n      <view class=\"fab-content\">\r\n        <view class=\"fab-icon\">+</view>\r\n        <view class=\"fab-text\">创建套餐</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 创建套餐分步骤向导 -->\r\n    <view class=\"step-wizard\" v-if=\"showStepWizard\">\r\n      <view class=\"wizard-overlay\" @click=\"cancelWizard\"></view>\r\n      <view class=\"wizard-content\">\r\n        <view class=\"wizard-header\">\r\n          <text class=\"wizard-title\">创建团购套餐</text>\r\n          <view class=\"close-icon\" @click=\"cancelWizard\">×</view>\r\n        </view>\r\n        \r\n        <view class=\"wizard-body\">\r\n          <view class=\"wizard-step\" v-for=\"(step, index) in createSteps\" :key=\"index\" @click=\"selectStep(step)\">\r\n            <view class=\"step-number\">{{index + 1}}</view>\r\n            <view class=\"step-info\">\r\n              <text class=\"step-title\">{{step.title}}</text>\r\n              <text class=\"step-desc\">{{step.description}}</text>\r\n            </view>\r\n            <view class=\"step-arrow\"></view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      dateRange: '2023-04-01 ~ 2023-04-30',\r\n      currentFilter: '全部套餐',\r\n      \r\n      // 套餐数据\r\n      packageData: {\r\n        totalPackages: 8,\r\n        packagesTrend: 'up',\r\n        packagesGrowth: '25%',\r\n        \r\n        salesCount: 124,\r\n        salesTrend: 'up',\r\n        salesGrowth: '18%',\r\n        \r\n        totalRevenue: 9860.50,\r\n        revenueTrend: 'up',\r\n        revenueGrowth: '22%',\r\n        \r\n        conversionRate: 32,\r\n        conversionTrend: 'up',\r\n        conversionGrowth: '8%'\r\n      },\r\n      \r\n      // 套餐列表\r\n      packagesList: [\r\n        {\r\n          id: 1,\r\n          name: '四菜一汤家庭套餐',\r\n          description: '适合3-4人用餐，经典家庭聚餐套餐',\r\n          itemsText: '红烧肉、糖醋排骨、鱼香肉丝、清炒时蔬、紫菜蛋花汤',\r\n          originalPrice: '168.00',\r\n          groupPrice: '99.00',\r\n          savingsAmount: '69.00',\r\n          salesCount: 56,\r\n          viewCount: 1280,\r\n          conversionRate: 4.3,\r\n          statusText: '进行中',\r\n          statusClass: 'active'\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '双人浪漫晚餐套餐',\r\n          description: '适合情侣约会，浪漫双人套餐',\r\n          itemsText: '牛排2份、沙拉2份、意面2份、甜点2份、红酒1瓶',\r\n          originalPrice: '298.00',\r\n          groupPrice: '199.00',\r\n          savingsAmount: '99.00',\r\n          salesCount: 32,\r\n          viewCount: 876,\r\n          conversionRate: 3.6,\r\n          statusText: '进行中',\r\n          statusClass: 'active'\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '商务午餐套餐',\r\n          description: '适合商务洽谈，高档商务套餐',\r\n          itemsText: '前菜4份、主菜4份、甜点4份、饮料4份',\r\n          originalPrice: '388.00',\r\n          groupPrice: '288.00',\r\n          savingsAmount: '100.00',\r\n          salesCount: 18,\r\n          viewCount: 456,\r\n          conversionRate: 3.9,\r\n          statusText: '已结束',\r\n          statusClass: 'inactive'\r\n        }\r\n      ],\r\n      showStepWizard: false,\r\n      createSteps: [\r\n        { title: '选择套餐类型', description: '选择您要创建的团购套餐类型' },\r\n        { title: '填写套餐信息', description: '填写团购套餐的基本信息' },\r\n        { title: '设置套餐价格', description: '设置团购套餐的价格' },\r\n        { title: '添加套餐内容', description: '添加团购套餐的内容' },\r\n        { title: '完成创建', description: '完成团购套餐的创建' }\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    showHelp() {\r\n      uni.showToast({\r\n        title: '团购套餐帮助',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    showDatePicker() {\r\n      // 显示日期选择器\r\n      uni.showToast({\r\n        title: '日期选择功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    formatNumber(num) {\r\n      return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });\r\n    },\r\n    showFilterOptions() {\r\n      uni.showActionSheet({\r\n        itemList: ['全部套餐', '进行中', '已结束', '销量优先', '转化率优先'],\r\n        success: (res) => {\r\n          const filters = ['全部套餐', '进行中', '已结束', '销量优先', '转化率优先'];\r\n          this.currentFilter = filters[res.tapIndex];\r\n          // 这里可以添加实际的筛选逻辑\r\n        }\r\n      });\r\n    },\r\n    viewPackageDetail(packageItem) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/group/package-detail?id=${packageItem.id}`\r\n      });\r\n    },\r\n    editPackage(packageItem) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/group/create?id=${packageItem.id}&type=package&edit=true`\r\n      });\r\n    },\r\n    sharePackage(packageItem) {\r\n      uni.showToast({\r\n        title: '分享功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    showMoreActions(packageItem) {\r\n      uni.showActionSheet({\r\n        itemList: ['复制套餐', '下架套餐', '删除套餐'],\r\n        success: (res) => {\r\n          switch(res.tapIndex) {\r\n            case 0:\r\n              uni.showToast({ title: '复制套餐功能开发中', icon: 'none' });\r\n              break;\r\n            case 1:\r\n              uni.showToast({ title: '下架套餐功能开发中', icon: 'none' });\r\n              break;\r\n            case 2:\r\n              this.confirmDeletePackage(packageItem);\r\n              break;\r\n          }\r\n        }\r\n      });\r\n    },\r\n    confirmDeletePackage(packageItem) {\r\n      uni.showModal({\r\n        title: '确认删除',\r\n        content: `确定要删除\"${packageItem.name}\"套餐吗？此操作不可恢复。`,\r\n        confirmText: '删除',\r\n        confirmColor: '#FF3B30',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            uni.showToast({ title: '删除成功', icon: 'success' });\r\n            // 这里可以添加实际的删除逻辑\r\n          }\r\n        }\r\n      });\r\n    },\r\n    showCreateSteps() {\r\n      this.showStepWizard = true;\r\n    },\r\n    cancelWizard() {\r\n      this.showStepWizard = false;\r\n    },\r\n    selectStep(step) {\r\n      // 这里可以添加选择步骤后的逻辑\r\n      const index = this.createSteps.indexOf(step);\r\n      \r\n      // 关闭向导\r\n      this.showStepWizard = false;\r\n      \r\n      // 根据选择的步骤跳转到不同的创建页面\r\n      switch(index) {\r\n        case 0: // 选择套餐类型\r\n          uni.navigateTo({\r\n            url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-type'\r\n          });\r\n          break;\r\n        case 1: // 填写套餐信息\r\n          uni.navigateTo({\r\n            url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-info'\r\n          });\r\n          break;\r\n        case 2: // 设置套餐价格\r\n          uni.navigateTo({\r\n            url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-price'\r\n          });\r\n          break;\r\n        case 3: // 添加套餐内容\r\n          uni.navigateTo({\r\n            url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-items'\r\n          });\r\n          break;\r\n        case 4: // 完成创建\r\n          uni.navigateTo({\r\n            url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-confirm'\r\n          });\r\n          break;\r\n        default:\r\n          // 默认跳转到完整的创建页面\r\n          uni.navigateTo({\r\n            url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create?type=package'\r\n          });\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.package-management-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #9040FF, #5E35B1);\r\n  color: #fff;\r\n  padding: 44px 16px 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.navbar-right {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.help-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n}\r\n\r\n.page-content {\r\n  height: calc(100vh - 59px - env(safe-area-inset-bottom));\r\n}\r\n\r\n/* 概览部分样式 */\r\n.overview-section {\r\n  margin: 15px;\r\n  padding: 15px;\r\n  background: #FFFFFF;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.date-picker {\r\n  display: flex;\r\n  align-items: center;\r\n  background: #F5F7FA;\r\n  border-radius: 15px;\r\n  padding: 5px 10px;\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n.date-icon {\r\n  width: 0;\r\n  height: 0;\r\n  border-left: 4px solid transparent;\r\n  border-right: 4px solid transparent;\r\n  border-top: 5px solid #666;\r\n  margin-left: 5px;\r\n}\r\n\r\n.stats-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: 0 -5px;\r\n}\r\n\r\n.stats-card {\r\n  flex: 1;\r\n  min-width: calc(50% - 10px);\r\n  margin: 5px;\r\n  background: #FFFFFF;\r\n  border-radius: 8px;\r\n  padding: 15px;\r\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);\r\n  position: relative;\r\n}\r\n\r\n.card-value {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.card-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.card-trend {\r\n  position: absolute;\r\n  top: 15px;\r\n  right: 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 12px;\r\n}\r\n\r\n.card-trend.up {\r\n  color: #34C759;\r\n}\r\n\r\n.card-trend.down {\r\n  color: #FF3B30;\r\n}\r\n\r\n.trend-arrow {\r\n  width: 0;\r\n  height: 0;\r\n  margin-right: 2px;\r\n}\r\n\r\n.up .trend-arrow {\r\n  border-left: 4px solid transparent;\r\n  border-right: 4px solid transparent;\r\n  border-bottom: 6px solid #34C759;\r\n}\r\n\r\n.down .trend-arrow {\r\n  border-left: 4px solid transparent;\r\n  border-right: 4px solid transparent;\r\n  border-top: 6px solid #FF3B30;\r\n}\r\n\r\n/* 套餐列表样式 */\r\n.packages-section {\r\n  margin: 15px;\r\n  margin-top: 0;\r\n}\r\n\r\n.filter-dropdown {\r\n  display: flex;\r\n  align-items: center;\r\n  background: #F5F7FA;\r\n  border-radius: 15px;\r\n  padding: 5px 10px;\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n.filter-icon {\r\n  width: 0;\r\n  height: 0;\r\n  border-left: 4px solid transparent;\r\n  border-right: 4px solid transparent;\r\n  border-top: 5px solid #666;\r\n  margin-left: 5px;\r\n}\r\n\r\n.packages-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n}\r\n\r\n.package-item {\r\n  background: #FFFFFF;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.package-header {\r\n  padding: 15px;\r\n  border-bottom: 1px solid #F5F7FA;\r\n}\r\n\r\n.package-title-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.package-name {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.package-status {\r\n  font-size: 12px;\r\n  padding: 2px 8px;\r\n  border-radius: 10px;\r\n}\r\n\r\n.package-status.active {\r\n  background: rgba(52, 199, 89, 0.1);\r\n  color: #34C759;\r\n}\r\n\r\n.package-status.inactive {\r\n  background: rgba(255, 59, 48, 0.1);\r\n  color: #FF3B30;\r\n}\r\n\r\n.package-desc {\r\n  font-size: 12px;\r\n  color: #999;\r\n  line-height: 1.5;\r\n}\r\n\r\n.package-content {\r\n  padding: 15px;\r\n  display: flex;\r\n  border-bottom: 1px solid #F5F7FA;\r\n}\r\n\r\n.package-items {\r\n  flex: 1;\r\n  padding-right: 15px;\r\n}\r\n\r\n.package-items-text {\r\n  font-size: 13px;\r\n  color: #666;\r\n  line-height: 1.6;\r\n}\r\n\r\n.package-price-info {\r\n  width: 100px;\r\n  flex-shrink: 0;\r\n  border-left: 1px dashed #E5E7EB;\r\n  padding-left: 15px;\r\n}\r\n\r\n.price-row {\r\n  margin-bottom: 5px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.price-row:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.price-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n  width: 45px;\r\n}\r\n\r\n.price-original {\r\n  font-size: 12px;\r\n  color: #999;\r\n  text-decoration: line-through;\r\n}\r\n\r\n.price-group {\r\n  font-size: 12px;\r\n  color: #FF3B30;\r\n  font-weight: 600;\r\n}\r\n\r\n.price-save {\r\n  font-size: 12px;\r\n  color: #FF9500;\r\n}\r\n\r\n.package-footer {\r\n  padding: 15px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.package-stats {\r\n  display: flex;\r\n  gap: 15px;\r\n}\r\n\r\n.stat-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 10px;\r\n  color: #999;\r\n  margin-top: 2px;\r\n}\r\n\r\n.package-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.action-button {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.action-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.edit-icon {\r\n  background: rgba(0, 122, 255, 0.1);\r\n  position: relative;\r\n}\r\n\r\n.edit-icon:before {\r\n  content: '';\r\n  width: 12px;\r\n  height: 12px;\r\n  border: 1px solid #007AFF;\r\n  border-radius: 2px;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.share-icon {\r\n  background: rgba(52, 199, 89, 0.1);\r\n  position: relative;\r\n}\r\n\r\n.share-icon:before {\r\n  content: '';\r\n  width: 12px;\r\n  height: 6px;\r\n  border-left: 1px solid #34C759;\r\n  border-right: 1px solid #34C759;\r\n  border-bottom: 1px solid #34C759;\r\n}\r\n\r\n.more-icon {\r\n  background: rgba(142, 142, 147, 0.1);\r\n  position: relative;\r\n}\r\n\r\n.more-icon:before {\r\n  content: '';\r\n  width: 12px;\r\n  height: 2px;\r\n  background: #8E8E93;\r\n  position: absolute;\r\n  top: 11px;\r\n  left: 6px;\r\n}\r\n\r\n.more-icon:after {\r\n  content: '';\r\n  width: 2px;\r\n  height: 12px;\r\n  background: #8E8E93;\r\n  position: absolute;\r\n  top: 6px;\r\n  left: 11px;\r\n}\r\n\r\n.action-text {\r\n  font-size: 10px;\r\n  color: #999;\r\n  margin-top: 2px;\r\n}\r\n\r\n/* 空状态样式 */\r\n.empty-state {\r\n  padding: 40px 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.empty-image {\r\n  width: 120px;\r\n  height: 120px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.empty-subtext {\r\n  font-size: 14px;\r\n  color: #999;\r\n  text-align: center;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 浮动操作按钮 */\r\n.floating-action-button {\r\n  position: fixed;\r\n  right: 20px;\r\n  bottom: 30px;\r\n  width: auto;\r\n  height: 50px;\r\n  background: linear-gradient(135deg, #9040FF, #5E35B1);\r\n  border-radius: 25px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 4px 12px rgba(126, 48, 225, 0.3);\r\n  z-index: 10;\r\n  padding: 0 20px;\r\n}\r\n\r\n.fab-content {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.fab-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 50%;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 20px;\r\n  color: #FFFFFF;\r\n  margin-right: 8px;\r\n}\r\n\r\n.fab-text {\r\n  color: #FFFFFF;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 创建套餐分步骤向导样式 */\r\n.step-wizard {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  z-index: 100;\r\n}\r\n\r\n.wizard-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n}\r\n\r\n.wizard-content {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  width: 80%;\r\n  max-width: 400px;\r\n  background: #FFFFFF;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n}\r\n\r\n.wizard-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.wizard-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.close-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 18px;\r\n  color: #999;\r\n}\r\n\r\n.wizard-body {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n}\r\n\r\n.wizard-step {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px;\r\n  background: #F5F7FA;\r\n  border-radius: 8px;\r\n}\r\n\r\n.step-number {\r\n  width: 30px;\r\n  height: 30px;\r\n  border-radius: 50%;\r\n  background: #FFFFFF;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  color: #333;\r\n  margin-right: 10px;\r\n}\r\n\r\n.step-info {\r\n  flex: 1;\r\n}\r\n\r\n.step-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.step-desc {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.step-arrow {\r\n  width: 0;\r\n  height: 0;\r\n  border-left: 4px solid transparent;\r\n  border-right: 4px solid transparent;\r\n  border-top: 5px solid #999;\r\n  margin-left: 10px;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/group/package-management.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAqLA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,eAAe;AAAA;AAAA,MAGf,aAAa;AAAA,QACX,eAAe;AAAA,QACf,eAAe;AAAA,QACf,gBAAgB;AAAA,QAEhB,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,QAEb,cAAc;AAAA,QACd,cAAc;AAAA,QACd,eAAe;AAAA,QAEf,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,MACnB;AAAA;AAAA,MAGD,cAAc;AAAA,QACZ;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,WAAW;AAAA,UACX,eAAe;AAAA,UACf,YAAY;AAAA,UACZ,eAAe;AAAA,UACf,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,YAAY;AAAA,UACZ,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,WAAW;AAAA,UACX,eAAe;AAAA,UACf,YAAY;AAAA,UACZ,eAAe;AAAA,UACf,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,YAAY;AAAA,UACZ,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,WAAW;AAAA,UACX,eAAe;AAAA,UACf,YAAY;AAAA,UACZ,eAAe;AAAA,UACf,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,YAAY;AAAA,UACZ,aAAa;AAAA,QACf;AAAA,MACD;AAAA,MACD,gBAAgB;AAAA,MAChB,aAAa;AAAA,QACX,EAAE,OAAO,UAAU,aAAa,gBAAiB;AAAA,QACjD,EAAE,OAAO,UAAU,aAAa,cAAe;AAAA,QAC/C,EAAE,OAAO,UAAU,aAAa,YAAa;AAAA,QAC7C,EAAE,OAAO,UAAU,aAAa,YAAa;AAAA,QAC7C,EAAE,OAAO,QAAQ,aAAa,YAAY;AAAA,MAC5C;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,iBAAiB;AAEfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,aAAa,KAAK;AAChB,aAAO,IAAI,eAAe,SAAS,EAAE,uBAAuB,GAAG,uBAAuB,EAAA,CAAG;AAAA,IAC1F;AAAA,IACD,oBAAoB;AAClBA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,QAAQ,OAAO,OAAO,QAAQ,OAAO;AAAA,QAChD,SAAS,CAAC,QAAQ;AAChB,gBAAM,UAAU,CAAC,QAAQ,OAAO,OAAO,QAAQ,OAAO;AACtD,eAAK,gBAAgB,QAAQ,IAAI,QAAQ;AAAA,QAE3C;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,kBAAkB,aAAa;AAC7BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,iFAAiF,YAAY,EAAE;AAAA,MACtG,CAAC;AAAA,IACF;AAAA,IACD,YAAY,aAAa;AACvBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,yEAAyE,YAAY,EAAE;AAAA,MAC9F,CAAC;AAAA,IACF;AAAA,IACD,aAAa,aAAa;AACxBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,gBAAgB,aAAa;AAC3BA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,QAAQ,QAAQ,MAAM;AAAA,QACjC,SAAS,CAAC,QAAQ;AAChB,kBAAO,IAAI,UAAQ;AAAA,YACjB,KAAK;AACHA,4BAAG,MAAC,UAAU,EAAE,OAAO,aAAa,MAAM,OAAK,CAAG;AAClD;AAAA,YACF,KAAK;AACHA,4BAAG,MAAC,UAAU,EAAE,OAAO,aAAa,MAAM,OAAK,CAAG;AAClD;AAAA,YACF,KAAK;AACH,mBAAK,qBAAqB,WAAW;AACrC;AAAA,UACJ;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,qBAAqB,aAAa;AAChCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,SAAS,YAAY,IAAI;AAAA,QAClC,aAAa;AAAA,QACb,cAAc;AAAA,QACd,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,UAAQ,CAAG;AAAA,UAElD;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,kBAAkB;AAChB,WAAK,iBAAiB;AAAA,IACvB;AAAA,IACD,eAAe;AACb,WAAK,iBAAiB;AAAA,IACvB;AAAA,IACD,WAAW,MAAM;AAEf,YAAM,QAAQ,KAAK,YAAY,QAAQ,IAAI;AAG3C,WAAK,iBAAiB;AAGtB,cAAO,OAAK;AAAA,QACV,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACP,CAAC;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACP,CAAC;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACP,CAAC;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACP,CAAC;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACP,CAAC;AACD;AAAA,QACF;AAEEA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACP,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClYA,GAAG,WAAW,eAAe;"}