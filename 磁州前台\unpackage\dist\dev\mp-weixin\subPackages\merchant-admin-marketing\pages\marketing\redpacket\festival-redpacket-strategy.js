"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
const _sfc_main = {
  data() {
    return {};
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    shareGuide() {
      common_vendor.index.showActionSheet({
        itemList: ["分享给好友", "分享到朋友圈", "复制链接"],
        success: function(res) {
          common_vendor.index.showToast({
            title: "分享成功",
            icon: "success"
          });
        }
      });
    },
    contactService() {
      common_vendor.index.makePhoneCall({
        phoneNumber: "************"
      });
    }
  }
};
if (!Array) {
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_rect = common_vendor.resolveComponent("rect");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_polygon = common_vendor.resolveComponent("polygon");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  (_component_circle + _component_line + _component_svg + _component_rect + _component_path + _component_polygon + _component_polyline)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.p({
      cx: "18",
      cy: "5",
      r: "3"
    }),
    c: common_vendor.p({
      cx: "6",
      cy: "12",
      r: "3"
    }),
    d: common_vendor.p({
      cx: "18",
      cy: "19",
      r: "3"
    }),
    e: common_vendor.p({
      x1: "8.59",
      y1: "13.51",
      x2: "15.42",
      y2: "17.49"
    }),
    f: common_vendor.p({
      x1: "15.41",
      y1: "6.51",
      x2: "8.59",
      y2: "10.49"
    }),
    g: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    h: common_vendor.o((...args) => $options.shareGuide && $options.shareGuide(...args)),
    i: common_vendor.p({
      x: "3",
      y: "4",
      width: "18",
      height: "18",
      rx: "2",
      ry: "2"
    }),
    j: common_vendor.p({
      x1: "16",
      y1: "2",
      x2: "16",
      y2: "6"
    }),
    k: common_vendor.p({
      x1: "8",
      y1: "2",
      x2: "8",
      y2: "6"
    }),
    l: common_vendor.p({
      x1: "3",
      y1: "10",
      x2: "21",
      y2: "10"
    }),
    m: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#FF6B6B",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    n: common_assets._imports_0$39,
    o: common_vendor.p({
      d: "M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"
    }),
    p: common_vendor.p({
      x1: "7",
      y1: "7",
      x2: "7.01",
      y2: "7"
    }),
    q: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#FF6B6B",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    r: common_vendor.p({
      cx: "12",
      cy: "12",
      r: "10"
    }),
    s: common_vendor.p({
      x1: "12",
      y1: "8",
      x2: "12",
      y2: "16"
    }),
    t: common_vendor.p({
      x1: "8",
      y1: "12",
      x2: "16",
      y2: "12"
    }),
    v: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#FF6B6B",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    w: common_vendor.p({
      d: "M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"
    }),
    x: common_vendor.p({
      d: "M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"
    }),
    y: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "16",
      height: "16",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#FF6B6B",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    z: common_vendor.p({
      points: "12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"
    }),
    A: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "16",
      height: "16",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#FF6B6B",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    B: common_vendor.p({
      d: "M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"
    }),
    C: common_vendor.p({
      cx: "9",
      cy: "7",
      r: "4"
    }),
    D: common_vendor.p({
      d: "M23 21v-2a4 4 0 0 0-3-3.87"
    }),
    E: common_vendor.p({
      d: "M16 3.13a4 4 0 0 1 0 7.75"
    }),
    F: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "16",
      height: "16",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#FF6B6B",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    G: common_vendor.p({
      x: "2",
      y: "3",
      width: "20",
      height: "14",
      rx: "2",
      ry: "2"
    }),
    H: common_vendor.p({
      x1: "8",
      y1: "21",
      x2: "16",
      y2: "21"
    }),
    I: common_vendor.p({
      x1: "12",
      y1: "17",
      x2: "12",
      y2: "21"
    }),
    J: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "16",
      height: "16",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#FF6B6B",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    K: common_vendor.p({
      d: "M22 11.08V12a10 10 0 1 1-5.93-9.14"
    }),
    L: common_vendor.p({
      points: "22 4 12 14.01 9 11.01"
    }),
    M: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#FF6B6B",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    N: common_vendor.p({
      d: "M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
    }),
    O: common_vendor.p({
      points: "14 2 14 8 20 8"
    }),
    P: common_vendor.p({
      x1: "16",
      y1: "13",
      x2: "8",
      y2: "13"
    }),
    Q: common_vendor.p({
      x1: "16",
      y1: "17",
      x2: "8",
      y2: "17"
    }),
    R: common_vendor.p({
      points: "10 9 9 9 8 9"
    }),
    S: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#FF6B6B",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    T: common_vendor.o((...args) => $options.contactService && $options.contactService(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-601419d5"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/redpacket/festival-redpacket-strategy.js.map
