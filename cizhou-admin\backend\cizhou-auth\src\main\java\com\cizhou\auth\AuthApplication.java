package com.cizhou.auth;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 认证服务启动类
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = {"com.cizhou.auth", "com.cizhou.common"})
@EnableDiscoveryClient
@MapperScan("com.cizhou.auth.mapper")
public class AuthApplication {

    public static void main(String[] args) {
        SpringApplication.run(AuthApplication.class, args);
        System.out.println("🔐 磁州生活网认证服务启动成功!");
    }
}
