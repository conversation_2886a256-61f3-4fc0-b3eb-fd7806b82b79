<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">裂变红包</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 裂变红包数据概览 -->
    <view class="overview-section">
      <view class="overview-header">
        <text class="section-title">裂变数据概览</text>
        <view class="date-picker" @click="showDatePicker">
          <text class="date-text">{{dateRange}}</text>
          <view class="date-icon"></view>
        </view>
      </view>
      
      <view class="stats-cards">
        <view class="stats-card">
          <view class="card-value">{{fissionData.totalCount}}</view>
          <view class="card-label">裂变活动数</view>
          <view class="card-trend" :class="fissionData.countTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{fissionData.countGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">{{fissionData.totalUsers}}</view>
          <view class="card-label">参与人数</view>
          <view class="card-trend" :class="fissionData.usersTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{fissionData.usersGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">{{fissionData.avgShare}}</view>
          <view class="card-label">平均分享数</view>
          <view class="card-trend" :class="fissionData.shareTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{fissionData.shareGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">{{fissionData.conversionRate}}%</view>
          <view class="card-label">转化率</view>
          <view class="card-trend" :class="fissionData.conversionTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{fissionData.conversionGrowth}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 裂变红包活动列表 -->
    <view class="fission-list-section">
      <view class="section-header">
        <text class="section-title">裂变活动</text>
        <view class="add-btn" @click="createFission">
          <text class="btn-text">创建活动</text>
          <view class="plus-icon-small"></view>
        </view>
      </view>
      
      <view class="tab-header">
        <view 
          class="tab-item" 
          v-for="(tab, index) in tabList" 
          :key="index"
          :class="{ active: currentTab === index }"
          @tap="switchTab(index)">
          <text class="tab-text">{{tab}}</text>
        </view>
      </view>
      
      <view class="fission-list">
        <view class="fission-item" v-for="(item, index) in filteredFissionList" :key="index" @click="viewFissionDetail(item)">
          <view class="fission-header">
            <view class="fission-title">{{item.title}}</view>
            <view class="fission-status" :class="'status-'+item.status">{{item.statusText}}</view>
          </view>
          
          <view class="fission-content">
            <view class="fission-icon"></view>
            <view class="fission-info">
              <text class="fission-time">{{item.timeRange}}</text>
              <view class="fission-rules">
                <text class="rules-label">裂变规则: </text>
                <text class="rules-value">分享{{item.shareCount}}人可得{{item.rewardAmount}}元</text>
              </view>
              <view class="fission-amount">
                <text class="amount-label">红包金额: </text>
                <text class="amount-value">¥{{item.amount}}/个</text>
              </view>
            </view>
          </view>
          
          <view class="fission-stats">
            <view class="stat-row">
              <text class="stat-label">参与人数:</text>
              <text class="stat-value">{{item.participantCount}}人</text>
            </view>
            <view class="stat-row">
              <text class="stat-label">分享次数:</text>
              <text class="stat-value">{{item.shareCount}}次</text>
            </view>
            <view class="stat-row">
              <text class="stat-label">新增用户:</text>
              <text class="stat-value">{{item.newUserCount}}人</text>
            </view>
          </view>
          
          <view class="fission-actions">
            <view class="action-btn" @click.stop="viewFissionDetail(item)">详情</view>
            <view class="action-btn" @click.stop="editFission(item)" v-if="item.status === 'draft'">编辑</view>
            <view class="action-btn" @click.stop="shareFission(item)" v-if="item.status === 'active'">分享</view>
            <view class="action-btn delete" @click.stop="deleteFission(item)" v-if="item.status === 'draft' || item.status === 'ended'">删除</view>
          </view>
        </view>
        
        <view class="empty-state" v-if="filteredFissionList.length === 0">
          <view class="empty-icon"></view>
          <text class="empty-text">暂无{{tabList[currentTab]}}裂变活动</text>
        </view>
      </view>
    </view>
    
    <!-- 裂变红包模板 -->
    <view class="templates-section">
      <view class="section-header">
        <text class="section-title">裂变模板</text>
      </view>
      
      <scroll-view scroll-x class="templates-scroll" show-scrollbar="false">
        <view class="templates-container">
          <view class="template-card" v-for="(template, index) in fissionTemplates" :key="index" @click="useTemplate(template)">
            <view class="template-preview" :style="{ background: template.color }">
              <view class="template-icon">
                <image class="icon-image" :src="template.icon" mode="aspectFit"></image>
              </view>
              <text class="template-name">{{template.name}}</text>
            </view>
            <view class="template-footer">
              <text class="template-desc">{{template.description}}</text>
              <view class="template-use-btn">使用</view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 裂变攻略 -->
    <view class="strategy-section">
      <view class="section-header">
        <text class="section-title">裂变攻略</text>
      </view>
      
      <view class="strategy-list">
        <view class="strategy-item" v-for="(strategy, index) in strategies" :key="index" @click="viewStrategy(strategy)">
          <view class="strategy-icon" :style="{ background: strategy.color }">
            <image class="icon-image" :src="strategy.icon" mode="aspectFit"></image>
          </view>
          <view class="strategy-content">
            <text class="strategy-title">{{strategy.title}}</text>
            <text class="strategy-desc">{{strategy.description}}</text>
          </view>
          <view class="strategy-arrow"></view>
        </view>
      </view>
    </view>
    
    <!-- 浮动操作按钮 -->
    <view class="floating-action-button" @click="createFission">
      <view class="fab-icon">+</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      dateRange: '2023-04-01 ~ 2023-04-30',
      currentTab: 0,
      tabList: ['全部', '进行中', '未开始', '已结束', '草稿'],
      
      // 裂变数据概览
      fissionData: {
        totalCount: 28,
        countTrend: 'up',
        countGrowth: '18.5%',
        
        totalUsers: 5682,
        usersTrend: 'up',
        usersGrowth: '32.7%',
        
        avgShare: 6.8,
        shareTrend: 'up',
        shareGrowth: '12.3%',
        
        conversionRate: 38.6,
        conversionTrend: 'up',
        conversionGrowth: '8.2%'
      },
      
      // 裂变活动列表
      fissionList: [
        {
          id: 1,
          title: '新用户拉新活动',
          status: 'active',
          statusText: '进行中',
          timeRange: '2023-04-15 ~ 2023-05-15',
          shareCount: 3,
          rewardAmount: 15,
          amount: 5.00,
          participantCount: 1286,
          shareCount: 4752,
          newUserCount: 863
        },
        {
          id: 2,
          title: '五一节日裂变',
          status: 'upcoming',
          statusText: '未开始',
          timeRange: '2023-05-01 ~ 2023-05-07',
          shareCount: 5,
          rewardAmount: 30,
          amount: 8.00,
          participantCount: 0,
          shareCount: 0,
          newUserCount: 0
        },
        {
          id: 3,
          title: '会员专享裂变',
          status: 'draft',
          statusText: '草稿',
          timeRange: '未设置',
          shareCount: 2,
          rewardAmount: 10,
          amount: 5.00,
          participantCount: 0,
          shareCount: 0,
          newUserCount: 0
        },
        {
          id: 4,
          title: '春节裂变活动',
          status: 'ended',
          statusText: '已结束',
          timeRange: '2023-01-20 ~ 2023-02-05',
          shareCount: 3,
          rewardAmount: 20,
          amount: 6.00,
          participantCount: 2358,
          shareCount: 8965,
          newUserCount: 1542
        }
      ],
      
      // 裂变模板
      fissionTemplates: [
        {
          id: 1,
          name: '拉新引流',
          description: '适合获取新用户的裂变活动',
          color: '#FF6B6B',
          icon: '/static/images/redpacket/fission-icon-1.png'
        },
        {
          id: 2,
          name: '会员专享',
          description: '提高会员活跃度和忠诚度',
          color: '#4ECDC4',
          icon: '/static/images/redpacket/fission-icon-2.png'
        },
        {
          id: 3,
          name: '节日狂欢',
          description: '节假日期间提升品牌曝光',
          color: '#FFD166',
          icon: '/static/images/redpacket/fission-icon-3.png'
        },
        {
          id: 4,
          name: '新品推广',
          description: '快速提升新品知名度',
          color: '#6A0572',
          icon: '/static/images/redpacket/fission-icon-4.png'
        }
      ],
      
      // 裂变攻略
      strategies: [
        {
          id: 1,
          title: '如何设计高转化裂变活动',
          description: '了解用户心理，设置合理的裂变门槛和奖励',
          color: '#FF6B6B',
          icon: '/static/images/redpacket/strategy-icon-1.png'
        },
        {
          id: 2,
          title: '裂变活动文案技巧',
          description: '吸引人的文案能大幅提升裂变效果',
          color: '#4ECDC4',
          icon: '/static/images/redpacket/strategy-icon-2.png'
        },
        {
          id: 3,
          title: '裂变红包数据分析指南',
          description: '通过数据分析优化裂变策略',
          color: '#FFD166',
          icon: '/static/images/redpacket/strategy-icon-3.png'
        }
      ]
    }
  },
  computed: {
    filteredFissionList() {
      if (this.currentTab === 0) {
        return this.fissionList;
      } else {
        const statusMap = {
          1: 'active',
          2: 'upcoming',
          3: 'ended',
          4: 'draft'
        };
        return this.fissionList.filter(item => item.status === statusMap[this.currentTab]);
      }
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showHelp() {
      uni.showModal({
        title: '裂变红包帮助',
        content: '裂变红包是一种通过用户分享传播来获取新用户的营销工具，用户分享给指定数量的好友后可获得奖励。',
        showCancel: false
      });
    },
    showDatePicker() {
      // 显示日期选择器
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    switchTab(index) {
      this.currentTab = index;
    },
    createFission() {
      uni.showToast({
        title: '创建裂变活动功能开发中',
        icon: 'none'
      });
    },
    viewFissionDetail(item) {
      uni.showToast({
        title: '查看详情功能开发中',
        icon: 'none'
      });
    },
    editFission(item) {
      uni.showToast({
        title: '编辑功能开发中',
        icon: 'none'
      });
    },
    shareFission(item) {
      uni.showToast({
        title: '分享功能开发中',
        icon: 'none'
      });
    },
    deleteFission(item) {
      uni.showModal({
        title: '删除确认',
        content: `确定要删除"${item.title}"吗？`,
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });
          }
        }
      });
    },
    useTemplate(template) {
      uni.showToast({
        title: '使用模板功能开发中',
        icon: 'none'
      });
    },
    viewStrategy(strategy) {
      uni.showToast({
        title: '攻略详情功能开发中',
        icon: 'none'
      });
    }
  }
}
</script>

<style lang="scss">
.page-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 数据概览样式 */
.overview-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.date-picker {
  display: flex;
  align-items: center;
  background-color: #F5F7FA;
  padding: 5px 10px;
  border-radius: 15px;
}

.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.date-icon {
  width: 12px;
  height: 12px;
  border-left: 1px solid #666;
  border-bottom: 1px solid #666;
  transform: rotate(-45deg);
}

.stats-cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.stats-card {
  width: 48%;
  background: linear-gradient(135deg, #FFF, #F5F7FA);
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 10px;
  position: relative;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.card-value {
  font-size: 22px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.card-label {
  font-size: 12px;
  color: #666;
}

.card-trend {
  position: absolute;
  top: 15px;
  right: 15px;
  display: flex;
  align-items: center;
  font-size: 12px;
}

.card-trend.up {
  color: #FF5858;
}

.card-trend.down {
  color: #2ED573;
}

.trend-arrow {
  width: 8px;
  height: 8px;
  border-left: 1px solid currentColor;
  border-top: 1px solid currentColor;
  margin-right: 2px;
}

.up .trend-arrow {
  transform: rotate(45deg);
}

.down .trend-arrow {
  transform: rotate(-135deg);
}

/* 裂变列表样式 */
.fission-list-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.add-btn {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  padding: 6px 12px;
  border-radius: 15px;
  color: #fff;
}

.btn-text {
  font-size: 12px;
  margin-right: 5px;
}

.plus-icon-small {
  width: 12px;
  height: 12px;
  position: relative;
}

.plus-icon-small::before,
.plus-icon-small::after {
  content: '';
  position: absolute;
  background-color: #fff;
}

.plus-icon-small::before {
  width: 12px;
  height: 2px;
  top: 5px;
  left: 0;
}

.plus-icon-small::after {
  width: 2px;
  height: 12px;
  top: 0;
  left: 5px;
}

.tab-header {
  display: flex;
  border-bottom: 1px solid #eee;
  margin-bottom: 15px;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 10px 0;
  position: relative;
}

.tab-text {
  font-size: 14px;
  color: #666;
}

.tab-item.active .tab-text {
  color: #FF5858;
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 25%;
  width: 50%;
  height: 2px;
  background-color: #FF5858;
  border-radius: 1px;
}

.fission-list {
  margin-top: 10px;
}

.fission-item {
  background-color: #fff;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
  border: 1px solid #eee;
}

.fission-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.fission-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.fission-status {
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 10px;
}

.status-active {
  background-color: #E8F5E9;
  color: #388E3C;
}

.status-upcoming {
  background-color: #E3F2FD;
  color: #1976D2;
}

.status-ended {
  background-color: #EEEEEE;
  color: #757575;
}

.status-draft {
  background-color: #FFF3E0;
  color: #E65100;
}

.fission-content {
  display: flex;
  margin-bottom: 10px;
}

.fission-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: linear-gradient(135deg, #FF9A8B, #FF6B6B);
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.fission-icon::before {
  content: '裂';
  color: #fff;
  font-weight: bold;
  font-size: 16px;
}

.fission-info {
  flex: 1;
}

.fission-time {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
  display: block;
}

.fission-rules,
.fission-amount {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
  display: flex;
}

.rules-label,
.amount-label {
  color: #999;
}

.rules-value,
.amount-value {
  color: #333;
}

.fission-stats {
  background-color: #F9F9F9;
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 10px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  margin-bottom: 5px;
}

.stat-row:last-child {
  margin-bottom: 0;
}

.stat-label {
  color: #666;
}

.stat-value {
  color: #333;
  font-weight: 500;
}

.fission-actions {
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #eee;
  padding-top: 10px;
}

.action-btn {
  margin-left: 10px;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 12px;
  background-color: #F5F7FA;
  color: #666;
}

.action-btn.delete {
  background-color: #FEE8E8;
  color: #FF5858;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
}

.empty-icon {
  width: 60px;
  height: 60px;
  background-color: #F5F7FA;
  border-radius: 30px;
  margin-bottom: 10px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 裂变模板样式 */
.templates-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.templates-scroll {
  margin-top: 15px;
  white-space: nowrap;
}

.templates-container {
  display: inline-flex;
  padding: 5px 0;
}

.template-card {
  width: 140px;
  margin-right: 15px;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.template-preview {
  height: 100px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.template-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.2);
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-image {
  width: 24px;
  height: 24px;
}

.template-name {
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}

.template-footer {
  background-color: #fff;
  padding: 10px;
}

.template-desc {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  display: block;
  white-space: normal;
  height: 32px;
  overflow: hidden;
}

.template-use-btn {
  background-color: #F5F7FA;
  color: #FF5858;
  font-size: 12px;
  padding: 5px 0;
  border-radius: 15px;
  text-align: center;
}

/* 裂变攻略样式 */
.strategy-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 80px;
}

.strategy-list {
  margin-top: 10px;
}

.strategy-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #eee;
}

.strategy-item:last-child {
  border-bottom: none;
}

.strategy-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.strategy-content {
  flex: 1;
}

.strategy-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.strategy-desc {
  font-size: 12px;
  color: #666;
}

.strategy-arrow {
  width: 12px;
  height: 12px;
  border-top: 1px solid #ccc;
  border-right: 1px solid #ccc;
  transform: rotate(45deg);
}

/* 浮动操作按钮 */
.floating-action-button {
  position: fixed;
  bottom: 30px;
  right: 20px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 10px rgba(255, 88, 88, 0.3);
  z-index: 100;
}

.fab-icon {
  font-size: 28px;
  color: #fff;
  font-weight: 300;
}
</style>