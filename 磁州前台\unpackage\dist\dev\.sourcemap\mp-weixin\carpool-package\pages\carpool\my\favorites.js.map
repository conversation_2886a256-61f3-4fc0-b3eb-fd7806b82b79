{"version": 3, "file": "favorites.js", "sources": ["carpool-package/pages/carpool/my/favorites.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/Y2FycG9vbC1wYWNrYWdlXHBhZ2VzXGNhcnBvb2xcbXlcZmF2b3JpdGVzLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"favorites-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-header\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"header-content\">\n        <view class=\"left-action\" @click=\"goBack\">\n          <image src=\"/static/images/tabbar/最新返回键.png\" class=\"action-icon back-icon\"></image>\n        </view>\n        <view class=\"title-area\">\n          <text class=\"page-title\">我的收藏</text>\n        </view>\n        <view class=\"right-action\">\n          <!-- 右侧占位 -->\n        </view>\n      </view>\n    </view>\n    \n    <!-- 内容区域 -->\n    <scroll-view class=\"scrollable-content\" scroll-y enable-back-to-top=\"true\" @scrolltolower=\"loadMore\" refresher-enabled @refresherrefresh=\"onRefresh\" :refresher-triggered=\"isRefreshing\">\n      <!-- 收藏内容列表 -->\n      <view class=\"card-container\">\n        <view class=\"card-item\" v-for=\"(item, index) in favoritesList\" :key=\"item.id\">\n          <!-- 编辑模式选择 -->\n          <view class=\"select-area\" v-if=\"isEditMode\" @click.stop=\"toggleSelectItem(item)\">\n            <view class=\"checkbox\" :class=\"{ checked: selectedItems.includes(item.id) }\">\n              <view class=\"checkbox-inner\" v-if=\"selectedItems.includes(item.id)\"></view>\n            </view>\n          </view>\n          \n          <!-- 卡片内容 -->\n          <view class=\"card-content\" @click=\"viewDetail(item)\">\n            <view class=\"user-info\">\n              <image :src=\"item.userAvatar\" mode=\"aspectFill\" class=\"user-avatar\"></image>\n              <view class=\"user-details\">\n                <text class=\"user-name\">{{item.userName}}</text>\n                <view class=\"user-meta\">\n                  <text class=\"publish-time\">{{item.publishTime}}</text>\n                  <text class=\"trip-type\">{{item.type}}</text>\n                </view>\n              </view>\n            </view>\n            \n            <view class=\"route-info\">\n              <view class=\"route-points\">\n                <view class=\"start-point\">\n                  <view class=\"point-marker start\"></view>\n                  <text class=\"point-text\">{{item.startPoint}}</text>\n                </view>\n                <view class=\"route-line\"></view>\n                <view class=\"end-point\">\n                  <view class=\"point-marker end\"></view>\n                  <text class=\"point-text\">{{item.endPoint}}</text>\n                </view>\n              </view>\n              <view class=\"trip-info\">\n                <view class=\"info-item\">\n                  <image src=\"/static/images/icons/calendar.png\" mode=\"aspectFit\" class=\"info-icon\"></image>\n                  <text class=\"info-text\">{{item.departureTime}}</text>\n                </view>\n                <view class=\"info-item\">\n                  <image src=\"/static/images/icons/people.png\" mode=\"aspectFit\" class=\"info-icon\"></image>\n                  <text class=\"info-text\">{{item.seatCount}}个座位</text>\n                </view>\n                <view class=\"info-item\" v-if=\"item.price\">\n                  <image src=\"/static/images/icons/price.png\" mode=\"aspectFit\" class=\"info-icon\"></image>\n                  <text class=\"info-text price\">¥{{item.price}}/人</text>\n                </view>\n              </view>\n            </view>\n            \n            <!-- 底部状态 -->\n            <view class=\"card-footer\">\n              <view class=\"meta-info\">\n                <view class=\"views\">\n                  <image src=\"/static/images/icons/eye.png\" mode=\"aspectFit\" class=\"meta-icon\"></image>\n                  <text class=\"meta-text\">{{item.viewCount}}</text>\n                </view>\n                <view class=\"messages\">\n                  <image src=\"/static/images/icons/message.png\" mode=\"aspectFit\" class=\"meta-icon\"></image>\n                  <text class=\"meta-text\">{{item.messageCount}}</text>\n                </view>\n              </view>\n              <view class=\"action-area\">\n                <view class=\"cancel-btn\" @click.stop=\"cancelFavorite(item)\" v-if=\"!isEditMode\">\n                  取消收藏\n              </view>\n              <view class=\"status-tag\" :class=\"getStatusClass(item.status)\">{{getStatusText(item.status)}}</view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 无数据提示 -->\n      <view class=\"empty-state\" v-if=\"favoritesList.length === 0 && !isLoading\">\n        <image src=\"/static/images/empty/no-favorites.png\" mode=\"aspectFit\" class=\"empty-image\"></image>\n        <text class=\"empty-text\">暂无收藏信息</text>\n        <button class=\"browse-button\" @click=\"goBrowse\">去浏览</button>\n      </view>\n      \n      <!-- 加载状态 -->\n      <view class=\"loading-state\" v-if=\"isLoading && !isRefreshing\">\n        <text class=\"loading-text\">加载中...</text>\n      </view>\n      \n      <!-- 到底提示 -->\n      <view class=\"list-bottom\" v-if=\"favoritesList.length > 0 && !hasMore\">\n        <text class=\"bottom-text\">— 已经到底啦 —</text>\n      </view>\n    </scroll-view>\n    \n    <!-- 批量操作栏 -->\n    <view class=\"batch-action-bar\" v-if=\"isEditMode\">\n      <view class=\"select-all\" @click=\"toggleSelectAll\">\n        <view class=\"checkbox\" :class=\"{ checked: isAllSelected }\">\n          <view class=\"checkbox-inner\" v-if=\"isAllSelected\"></view>\n        </view>\n        <text class=\"select-text\">全选</text>\n      </view>\n      <button class=\"batch-button\" :disabled=\"selectedItems.length === 0\" @click=\"batchCancel\">取消收藏({{selectedItems.length}})</button>\n    </view>\n\n    <!-- 悬浮编辑按钮 -->\n    <view class=\"float-edit-btn\" @click=\"toggleEditMode\">\n      <text>{{ isEditMode ? '完成' : '编辑' }}</text>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue';\n\n// 状态栏高度\nconst statusBarHeight = ref(20);\n\n// 数据\nconst favoritesList = ref([]);\nconst page = ref(1);\nconst pageSize = ref(10);\nconst hasMore = ref(true);\nconst isLoading = ref(false);\nconst isRefreshing = ref(false);\nconst isEditMode = ref(false);\nconst selectedItems = ref([]);\n\n// 计算属性\nconst isAllSelected = computed(() => {\n  return favoritesList.value.length > 0 && selectedItems.value.length === favoritesList.value.length;\n});\n\n// 页面加载时执行\nonMounted(() => {\n  // 获取状态栏高度\n  const sysInfo = uni.getSystemInfoSync();\n  statusBarHeight.value = sysInfo.statusBarHeight || 20;\n  \n  loadData();\n});\n\n// 加载数据\nconst loadData = () => {\n  if (isLoading.value) return;\n  isLoading.value = true;\n  \n  // 模拟数据加载\n  setTimeout(() => {\n    // 模拟数据\n    const mockData = [\n      {\n        id: '4001',\n        type: '长途拼车',\n        publishTime: '2023-10-15 16:30',\n        status: 'active',\n        startPoint: '磁县政府',\n        endPoint: '石家庄火车站',\n        departureTime: '明天 10:30',\n        seatCount: 3,\n        price: 50,\n        userName: '张先生',\n        userAvatar: '/static/images/avatar/user1.png',\n        viewCount: 128,\n        messageCount: 5\n      },\n      {\n        id: '4002',\n        type: '上下班拼车',\n        publishTime: '2023-10-14 14:15',\n        status: 'active',\n        startPoint: '磁县老城区',\n        endPoint: '邯郸科技学院',\n        departureTime: '工作日 07:30',\n        seatCount: 4,\n        price: 12,\n        userName: '李女士',\n        userAvatar: '/static/images/avatar/user2.png',\n        viewCount: 86,\n        messageCount: 2\n      },\n      {\n        id: '4003',\n        type: '短途拼车',\n        publishTime: '2023-10-13 11:40',\n        status: 'expired',\n        startPoint: '磁县体育场',\n        endPoint: '磁县汽车站',\n        departureTime: '今天 16:00',\n        seatCount: 2,\n        price: 5,\n        userName: '王先生',\n        userAvatar: '/static/images/avatar/user3.png',\n        viewCount: 56,\n        messageCount: 0\n      }\n    ];\n    \n    if (page.value === 1) {\n      favoritesList.value = mockData;\n    } else {\n      favoritesList.value = [...favoritesList.value, ...mockData];\n    }\n    \n    // 模拟没有更多数据\n    if (page.value >= 2) {\n      hasMore.value = false;\n    }\n    \n    isLoading.value = false;\n    isRefreshing.value = false;\n  }, 1000);\n};\n\n// 加载更多\nconst loadMore = () => {\n  if (!hasMore.value || isLoading.value) return;\n  page.value++;\n  loadData();\n};\n\n// 下拉刷新\nconst onRefresh = () => {\n  isRefreshing.value = true;\n  page.value = 1;\n  hasMore.value = true;\n  loadData();\n};\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n\n// 前往浏览页面\nconst goBrowse = () => {\n  uni.navigateTo({\n    url: '/carpool-package/pages/carpool-main/index'\n  });\n};\n\n// 查看详情\nconst viewDetail = (item) => {\n  if (isEditMode.value) {\n    toggleSelectItem(item);\n    return;\n  }\n  \n  uni.navigateTo({\n    url: `/carpool-package/pages/carpool/detail/index?id=${item.id}`\n  });\n};\n\n// 取消收藏\nconst cancelFavorite = (item) => {\n  uni.showModal({\n    title: '提示',\n    content: '确定要取消收藏此条信息吗？',\n    success: (res) => {\n      if (res.confirm) {\n        // 模拟取消收藏操作\n        favoritesList.value = favoritesList.value.filter(i => i.id !== item.id);\n        uni.showToast({\n          title: '已取消收藏',\n          icon: 'success'\n        });\n      }\n    }\n  });\n};\n\n// 切换编辑模式\nconst toggleEditMode = () => {\n  isEditMode.value = !isEditMode.value;\n  if (!isEditMode.value) {\n    // 退出编辑模式，清空选择\n    selectedItems.value = [];\n  }\n};\n\n// 切换选择项\nconst toggleSelectItem = (item) => {\n  const index = selectedItems.value.indexOf(item.id);\n  if (index === -1) {\n    selectedItems.value.push(item.id);\n  } else {\n    selectedItems.value.splice(index, 1);\n  }\n};\n\n// 切换全选\nconst toggleSelectAll = () => {\n  if (isAllSelected.value) {\n    // 已全选，取消全选\n    selectedItems.value = [];\n  } else {\n    // 未全选，设为全选\n    selectedItems.value = favoritesList.value.map(item => item.id);\n  }\n};\n\n// 批量取消收藏\nconst batchCancel = () => {\n  if (selectedItems.value.length === 0) return;\n  \n  uni.showModal({\n    title: '提示',\n    content: `确定要取消收藏这 ${selectedItems.value.length} 条信息吗？`,\n    success: (res) => {\n      if (res.confirm) {\n        // 模拟批量取消收藏操作\n        favoritesList.value = favoritesList.value.filter(item => !selectedItems.value.includes(item.id));\n        selectedItems.value = [];\n        uni.showToast({\n          title: '已取消收藏',\n          icon: 'success'\n        });\n      }\n    }\n  });\n};\n\n// 获取状态文本\nconst getStatusText = (status) => {\n  const statusMap = {\n    active: '可乘坐',\n    pending: '审核中',\n    expired: '已过期'\n  };\n  return statusMap[status] || status;\n};\n\n// 获取状态样式类\nconst getStatusClass = (status) => {\n  const classMap = {\n    active: 'status-active',\n    pending: 'status-pending',\n    expired: 'status-expired'\n  };\n  return classMap[status] || '';\n};\n</script>\n\n<style lang=\"scss\">\n.favorites-container {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background-color: #F5F7FA;\n}\n\n/* 导航栏样式 */\n.custom-header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background-color: #1677FF;\n}\n\n.header-content {\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 12px;\n}\n\n.left-action, .right-action {\n  width: 44px;\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.action-icon {\n  width: 24px;\n  height: 24px;\n}\n\n.back-icon {\n  width: 24px;\n  height: 24px;\n  /* 图标是黑色的，需要转为白色 */\n  filter: brightness(0) invert(1);\n}\n\n.title-area {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.page-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #FFFFFF;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\n}\n\n.edit-text {\n  color: #FFFFFF;\n  font-size: 14px;\n}\n\n/* 内容区域 */\n.scrollable-content {\n  flex: 1;\n  margin-top: calc(44px + var(--status-bar-height) + 5px);\n  padding: 0;\n  margin-bottom: 60px;\n}\n\n/* 卡片容器 */\n.card-container {\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 15px;\n  padding: 5px 0;\n}\n\n/* 卡片项目 */\n.card-item {\n  width: 90%;\n  display: flex;\n  position: relative;\n}\n\n/* 选择区域 */\n.select-area {\n  position: absolute;\n  left: 10px;\n  top: 0;\n  bottom: 0;\n  width: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 5;\n}\n\n/* 复选框 */\n.checkbox {\n  width: 20px;\n  height: 20px;\n  border-radius: 50%;\n  border: 1px solid #DDDDDD;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: #FFFFFF;\n}\n\n.checkbox.checked {\n  border-color: #9C27B0;\n  background-color: #9C27B0;\n}\n\n.checkbox-inner {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background-color: #FFFFFF;\n}\n\n/* 卡片内容 */\n.card-content {\n  flex: 1;\n  background-color: #FFFFFF;\n  border-radius: 12px;\n  padding: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n  margin-left: 0;\n}\n\n/* 用户信息 */\n.user-info {\n  display: flex;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.user-avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 20px;\n  margin-right: 10px;\n}\n\n.user-details {\n  flex: 1;\n}\n\n.user-name {\n  font-size: 15px;\n  font-weight: 500;\n  color: #333333;\n}\n\n.user-meta {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-top: 2px;\n}\n\n.publish-time, .trip-type {\n  font-size: 12px;\n  color: #999999;\n}\n\n/* 路线信息 */\n.route-info {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  border-top: 1px solid #F5F5F5;\n  padding-top: 12px;\n}\n\n.route-points {\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n}\n\n.start-point, .end-point {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.point-marker {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n}\n\n.start {\n  background-color: #1677FF;\n}\n\n.end {\n  background-color: #FF5722;\n}\n\n.route-line {\n  width: 2px;\n  height: 20px;\n  background-color: #DDDDDD;\n  margin-left: 5px;\n}\n\n.point-text {\n  font-size: 16px;\n  color: #333333;\n}\n\n.trip-info {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16px;\n  margin-top: 10px;\n}\n\n.info-item {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n}\n\n.info-icon {\n  width: 24px;\n  height: 24px;\n}\n\n.info-text {\n  font-size: 14px;\n  color: #666666;\n}\n\n.price {\n  color: #FF5722;\n  font-weight: 500;\n}\n\n/* 卡片底部 */\n.card-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 20px;\n  padding-top: 16px;\n  border-top: 1px solid #F5F5F5;\n}\n\n.meta-info {\n  display: flex;\n  gap: 16px;\n}\n\n.views, .messages {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n}\n\n.meta-icon {\n  width: 24px;\n  height: 24px;\n}\n\n.meta-text {\n  font-size: 12px;\n  color: #999999;\n}\n\n.action-area {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.cancel-btn {\n  font-size: 13px;\n  font-weight: 500;\n  padding: 4px 14px;\n  border-radius: 16px;\n  background-color: #FF5722;\n  color: #FFFFFF;\n  text-align: center;\n  box-shadow: 0 2px 6px rgba(255, 87, 34, 0.25);\n  margin-right: 6px;\n}\n\n.cancel-icon {\n  width: 14px;\n  height: 14px;\n  margin-right: 4px;\n  filter: brightness(0) invert(1);\n}\n\n.status-tag {\n  font-size: 12px;\n  padding: 2px 8px;\n  border-radius: 10px;\n}\n\n.status-active {\n  background-color: rgba(22, 119, 255, 0.1);\n  color: #1677FF;\n}\n\n.status-pending {\n  background-color: rgba(255, 152, 0, 0.1);\n  color: #FF9800;\n}\n\n.status-expired {\n  background-color: rgba(153, 153, 153, 0.1);\n  color: #999999;\n}\n\n/* 空状态 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 40px 0;\n}\n\n.empty-image {\n  width: 120px;\n  height: 120px;\n  margin-bottom: 16px;\n}\n\n.empty-text {\n  font-size: 16px;\n  color: #999999;\n  margin-bottom: 20px;\n}\n\n.browse-button {\n  background-color: #9C27B0;\n  color: #FFFFFF;\n  padding: 8px 20px;\n  border-radius: 20px;\n  font-size: 14px;\n}\n\n/* 加载状态 */\n.loading-state {\n  padding: 16px 0;\n  text-align: center;\n}\n\n.loading-text {\n  font-size: 14px;\n  color: #999999;\n}\n\n/* 列表底部 */\n.list-bottom {\n  padding: 16px 0;\n  text-align: center;\n}\n\n.bottom-text {\n  font-size: 14px;\n  color: #999999;\n}\n\n/* 批量操作栏 */\n.batch-action-bar {\n  position: fixed;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  height: 60px;\n  background-color: #FFFFFF;\n  display: flex;\n  align-items: center;\n  padding: 0 16px;\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);\n  z-index: 99;\n}\n\n.select-all {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.select-text {\n  font-size: 14px;\n  color: #333333;\n}\n\n.batch-button {\n  flex: 1;\n  height: 36px;\n  line-height: 36px;\n  border-radius: 18px;\n  background-color: #9C27B0;\n  color: #FFFFFF;\n  font-size: 14px;\n  margin-left: 16px;\n}\n\n.batch-button[disabled] {\n  background-color: #E0E0E0;\n  color: #999999;\n}\n\n/* 悬浮编辑按钮 */\n.float-edit-btn {\n  position: fixed;\n  right: 20px;\n  bottom: 80px;\n  background-color: #1677FF;\n  color: #FFFFFF;\n  width: 60px;\n  height: 60px;\n  border-radius: 30px;\n  font-size: 14px;\n  z-index: 99;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/carpool-package/pages/carpool/my/favorites.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onMounted", "uni", "MiniProgramPage"], "mappings": ";;;;;;AAqIA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAG9B,UAAM,gBAAgBA,cAAAA,IAAI,CAAA,CAAE;AAC5B,UAAM,OAAOA,cAAAA,IAAI,CAAC;AACDA,kBAAG,IAAC,EAAE;AACvB,UAAM,UAAUA,cAAAA,IAAI,IAAI;AACxB,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAC3B,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAC9B,UAAM,aAAaA,cAAAA,IAAI,KAAK;AAC5B,UAAM,gBAAgBA,cAAAA,IAAI,CAAA,CAAE;AAG5B,UAAM,gBAAgBC,cAAQ,SAAC,MAAM;AACnC,aAAO,cAAc,MAAM,SAAS,KAAK,cAAc,MAAM,WAAW,cAAc,MAAM;AAAA,IAC9F,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AAEd,YAAM,UAAUC,oBAAI;AACpB,sBAAgB,QAAQ,QAAQ,mBAAmB;AAEnD;IACF,CAAC;AAGD,UAAM,WAAW,MAAM;AACrB,UAAI,UAAU;AAAO;AACrB,gBAAU,QAAQ;AAGlB,iBAAW,MAAM;AAEf,cAAM,WAAW;AAAA,UACf;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,aAAa;AAAA,YACb,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,eAAe;AAAA,YACf,WAAW;AAAA,YACX,OAAO;AAAA,YACP,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,WAAW;AAAA,YACX,cAAc;AAAA,UACf;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,aAAa;AAAA,YACb,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,eAAe;AAAA,YACf,WAAW;AAAA,YACX,OAAO;AAAA,YACP,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,WAAW;AAAA,YACX,cAAc;AAAA,UACf;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,aAAa;AAAA,YACb,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,eAAe;AAAA,YACf,WAAW;AAAA,YACX,OAAO;AAAA,YACP,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,WAAW;AAAA,YACX,cAAc;AAAA,UACf;AAAA,QACP;AAEI,YAAI,KAAK,UAAU,GAAG;AACpB,wBAAc,QAAQ;AAAA,QAC5B,OAAW;AACL,wBAAc,QAAQ,CAAC,GAAG,cAAc,OAAO,GAAG,QAAQ;AAAA,QAC3D;AAGD,YAAI,KAAK,SAAS,GAAG;AACnB,kBAAQ,QAAQ;AAAA,QACjB;AAED,kBAAU,QAAQ;AAClB,qBAAa,QAAQ;AAAA,MACtB,GAAE,GAAI;AAAA,IACT;AAGA,UAAM,WAAW,MAAM;AACrB,UAAI,CAAC,QAAQ,SAAS,UAAU;AAAO;AACvC,WAAK;AACL;IACF;AAGA,UAAM,YAAY,MAAM;AACtB,mBAAa,QAAQ;AACrB,WAAK,QAAQ;AACb,cAAQ,QAAQ;AAChB;IACF;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,SAAS;AAC3B,UAAI,WAAW,OAAO;AACpB,yBAAiB,IAAI;AACrB;AAAA,MACD;AAEDA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,kDAAkD,KAAK,EAAE;AAAA,MAClE,CAAG;AAAA,IACH;AAGA,UAAM,iBAAiB,CAAC,SAAS;AAC/BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEf,0BAAc,QAAQ,cAAc,MAAM,OAAO,OAAK,EAAE,OAAO,KAAK,EAAE;AACtEA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,iBAAiB,MAAM;AAC3B,iBAAW,QAAQ,CAAC,WAAW;AAC/B,UAAI,CAAC,WAAW,OAAO;AAErB,sBAAc,QAAQ;MACvB;AAAA,IACH;AAGA,UAAM,mBAAmB,CAAC,SAAS;AACjC,YAAM,QAAQ,cAAc,MAAM,QAAQ,KAAK,EAAE;AACjD,UAAI,UAAU,IAAI;AAChB,sBAAc,MAAM,KAAK,KAAK,EAAE;AAAA,MACpC,OAAS;AACL,sBAAc,MAAM,OAAO,OAAO,CAAC;AAAA,MACpC;AAAA,IACH;AAGA,UAAM,kBAAkB,MAAM;AAC5B,UAAI,cAAc,OAAO;AAEvB,sBAAc,QAAQ;MAC1B,OAAS;AAEL,sBAAc,QAAQ,cAAc,MAAM,IAAI,UAAQ,KAAK,EAAE;AAAA,MAC9D;AAAA,IACH;AAGA,UAAM,cAAc,MAAM;AACxB,UAAI,cAAc,MAAM,WAAW;AAAG;AAEtCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,YAAY,cAAc,MAAM,MAAM;AAAA,QAC/C,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEf,0BAAc,QAAQ,cAAc,MAAM,OAAO,UAAQ,CAAC,cAAc,MAAM,SAAS,KAAK,EAAE,CAAC;AAC/F,0BAAc,QAAQ;AACtBA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,CAAC,WAAW;AAChC,YAAM,YAAY;AAAA,QAChB,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,SAAS;AAAA,MACb;AACE,aAAO,UAAU,MAAM,KAAK;AAAA,IAC9B;AAGA,UAAM,iBAAiB,CAAC,WAAW;AACjC,YAAM,WAAW;AAAA,QACf,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,SAAS;AAAA,MACb;AACE,aAAO,SAAS,MAAM,KAAK;AAAA,IAC7B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpWA,GAAG,WAAWC,SAAe;"}