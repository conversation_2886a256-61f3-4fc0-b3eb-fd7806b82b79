/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.progress-container {
  padding: 15px;
  margin-bottom: 15px;
  background-color: #fff;
}
.progress-steps {
  display: flex;
  justify-content: space-between;
  position: relative;
}
.step {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
}
.step:first-child .step-content {
  align-items: flex-start;
}
.step:last-child .step-content {
  align-items: flex-end;
}
.step-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
}
.step-dot {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #E5E5E5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
  transition: all 0.3s;
}
.active .step-dot {
  background-color: #9040FF;
  color: white;
}
.completed .step-dot {
  background-color: #9040FF;
}
.check-icon {
  width: 10px;
  height: 6px;
  border-left: 2px solid white;
  border-bottom: 2px solid white;
  transform: rotate(-45deg) translate(1px, -1px);
}
.step-label {
  font-size: 12px;
  color: #999;
  transition: all 0.3s;
}
.active .step-label, .completed .step-label {
  color: #333;
  font-weight: 500;
}
.step-line {
  position: absolute;
  left: 0;
  right: 0;
  top: 12px;
  height: 2px;
  background-color: #E5E5E5;
  z-index: 1;
}
.step-line.active {
  background-color: #9040FF;
}