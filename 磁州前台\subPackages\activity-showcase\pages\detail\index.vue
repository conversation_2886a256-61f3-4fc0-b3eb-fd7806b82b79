<template>
  <view class="detail-container">
    <!-- 自定义导航栏 - 蓝色渐变效果 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <image class="back-icon" src="/static/images/tabbar/最新返回键.png" mode="aspectFit"></image>
        </view>
        <view class="navbar-title">{{ activityTypeTitle }}</view>
        <view class="navbar-right"></view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container" v-if="loading">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <block v-else>
      <!-- 商品轮播图 - 全屏设计 -->
      <swiper class="product-swiper" 
        :indicator-dots="true" 
        indicator-color="rgba(255,255,255,0.4)"
        indicator-active-color="#FFFFFF"
        :autoplay="true" 
        :interval="4000" 
        :duration="400"
        @change="onSwiperChange"
        :style="{ marginTop: navbarHeight + 'px' }">
      <swiper-item v-for="(item, index) in groupbuy.images" :key="index">
          <image :src="item" mode="aspectFill" class="swiper-image"></image>
      </swiper-item>
    </swiper>

      <!-- 商品基本信息卡片 -->
      <view class="basic-info-card">
        <view class="price-section">
          <view class="price-main">
            <text class="price-symbol">¥</text>
            <text class="price-value">{{formattedGroupPrice}}</text>
            <view class="price-tag">{{discountPercent}}%<text class="price-tag-text">折扣</text></view>
          </view>
          <view class="price-compare">
            <view class="compare-item">
              <text class="compare-label">市场价</text>
              <text class="compare-price">¥{{formattedMarketPrice}}</text>
            </view>
            <view class="compare-item">
              <text class="compare-label">日常价</text>
              <text class="compare-price">¥{{formattedRegularPrice}}</text>
            </view>
          </view>
    </view>
    
        <view class="save-info">
          <view class="save-tag">
            <text class="save-icon">省</text>
            <text class="save-text">团购立省¥{{saveAmount}}</text>
          </view>
          <text class="limited-text">限时特惠</text>
        </view>
        
        <view class="product-title-row">
          <view class="group-tag">拼团</view>
        <text class="product-title">{{groupbuy.title}}</text>
      </view>
      
        <!-- 销量和倒计时 -->
        <view class="sales-countdown">
          <view class="sales-info">
            <text class="sales-count">已售{{groupbuy.soldCount}}件</text>
            <text class="sales-divider">|</text>
            <text class="view-count">{{groupbuy.viewCount || 1200}}人浏览</text>
      </view>
      
          <view class="mini-countdown">
            <text class="countdown-text">距结束</text>
            <text class="time-block">{{countdown.days}}</text>
            <text class="time-colon">:</text>
            <text class="time-block">{{countdown.hours}}</text>
            <text class="time-colon">:</text>
            <text class="time-block">{{countdown.minutes}}</text>
            <text class="time-colon">:</text>
            <text class="time-block">{{countdown.seconds}}</text>
          </view>
      </view>
    </view>
    
      <!-- 分销组件 - 醒目位置 -->
      <distribution-section 
        :itemId="id"
        itemType="group"
        :itemTitle="groupbuy.title"
        :itemPrice="groupbuy.groupPrice"
        :commissionRate="groupbuy.commissionRate || 20"
      />
      
      <!-- 适用门店列表 -->
      <view class="store-list-card">
        <view class="card-header">
          <text class="card-title">适用门店</text>
          <view class="card-more" @click="viewAllStores">
            <text>全部</text>
            <text class="cuIcon-right"></text>
          </view>
        </view>
        <view class="store-list">
          <view class="store-item" v-for="(store, index) in storeList" :key="index" @click="viewStoreDetail(store.id)">
            <image :src="store.logo" class="store-logo"></image>
            <view class="store-info">
              <text class="store-name">{{ store.name }}</text>
              <text class="store-address">{{ store.address }}</text>
            </view>
            <view class="store-distance">
              <text>{{ store.distance }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 本店其他拼团 -->
      <view class="other-group-card">
        <view class="card-header">
          <text class="card-title">本店其他拼团</text>
          <view class="card-more" @click="viewMoreGroups">
            <text>更多</text>
            <text class="cuIcon-right"></text>
          </view>
        </view>
        <view class="other-group-list">
          <view class="other-group-item" v-for="(item, index) in otherGroups" :key="index" @click="navigateToDetail(item.id)">
            <view class="sold-tag-corner">已售{{ item.soldCount }}+</view>
            <view class="item-image-container">
              <image :src="item.image" class="other-group-image"></image>
            </view>
            <view class="other-group-info">
              <view class="shop-location">
                <text class="shop-name">{{ item.shopName || '磁州同城折扣店' }}</text>
                <text class="location-info">距你{{ item.distance || '1km' }} · 近磁州镇</text>
              </view>
              <text class="other-group-title">{{ item.title }}</text>
              <view class="other-group-price">
                <text class="group-price-value">¥{{ item.groupPrice }}</text>
                <text class="market-price-value">¥{{ item.marketPrice }}</text>
                <text class="discount-tag">{{ item.discount || '6.2折热销中' }}</text>
              </view>
              <view class="other-group-bottom">
                <view class="sold-info">
                  <text class="sold-tag">{{ item.soldTag || '新品' }}</text>
                  <text class="sold-count">{{ item.soldCount }}+人选择</text>
                  <text class="usage-info">{{ item.usageInfo || '到店使用' }}</text>
                </view>
                <view class="buy-now-btn">
                  <text>抢购</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 拼团信息卡片 -->
      <view class="group-card">
        <view class="group-header">
          <view class="group-title">
            <image src="/static/demo/group-icon.png" class="group-icon"></image>
            <text>{{groupbuy.minGroupSize || 3}}人团·已有{{groupbuy.soldCount}}人参与</text>
          </view>
          <view class="group-more" @click="toggleGroupBuyMode">
            {{ groupbuy.enableGroupBuy ? '切换到直购模式' : '切换到拼团模式' }}
          </view>
        </view>
        
        <view class="group-location">
          <image src="/static/images/tabbar/位置.png" class="location-icon"></image>
          <text class="location-text">磁州市中心广场东路88号</text>
        </view>
        
        <view class="group-avatars">
          <view class="avatar-item" v-for="i in 5" :key="i">
            <image :src="`/static/demo/avatar${i}.png`" class="avatar-image"></image>
          </view>
          <view class="avatar-more">
            <text>+{{groupbuy.soldCount - 5 > 0 ? groupbuy.soldCount - 5 : 0}}</text>
          </view>
          <view class="recent-join">
            <text class="recent-user">张先生</text>
            <text class="join-time">刚刚参团</text>
          </view>
        </view>
        
        <view class="group-progress">
      <view class="progress-bar">
            <view class="progress-fill" :style="{ width: progressWidth }"></view>
      </view>
          <view class="progress-text">
            <text>已有{{groupbuy.soldCount}}人参团，目标{{groupbuy.targetCount}}人</text>
      </view>
    </view>
    
        <view class="group-tips">
          <text class="tip-icon cuIcon-info"></text>
          <text class="tip-text">团购成功后，请到店核销，有效期7天</text>
        </view>
        
        <view class="verification-info">
          <view class="verification-title">
            <text class="verification-icon cuIcon-location"></text>
            <text class="verification-text">到店核销</text>
          </view>
          <view class="verification-address">地址：磁州市中心广场东路88号</view>
          <view class="verification-time">营业时间：10:00-21:00</view>
        </view>
      </view>
      
      <view class="local-benefits">
        <view class="benefit-item">
          <image src="/static/images/tabbar/品质.png" class="benefit-icon"></image>
          <text class="benefit-text">本地商家</text>
        </view>
        <view class="benefit-item">
          <image src="/static/images/tabbar/准时.png" class="benefit-icon"></image>
          <text class="benefit-text">到店核销</text>
        </view>
        <view class="benefit-item">
          <image src="/static/images/tabbar/保障.png" class="benefit-icon"></image>
          <text class="benefit-text">品质保障</text>
        </view>
      </view>
      
      <!-- 优惠券卡片 -->
      <view class="coupon-card">
        <view class="coupon-left">
          <text class="coupon-title">新人专享券</text>
          <text class="coupon-desc">首单立减10元</text>
        </view>
        <view class="coupon-right" @click="receiveCoupon">
          <text class="coupon-btn">立即领取</text>
        </view>
      </view>
      
      <!-- 商家信息卡片 -->
      <view class="shop-card" @click="goToShop">
      <view class="shop-info">
          <image :src="groupbuy.shopLogo" class="shop-logo"></image>
          <view class="shop-details">
        <text class="shop-name">{{groupbuy.shopName}}</text>
        <view class="shop-rating">
          <view class="rating-stars">
                <image v-for="i in 5" :key="i" 
                  :src="i <= Math.floor(groupbuy.shopRating) ? 
                    '/static/images/tabbar/星星-选中.png' : 
                    '/static/images/tabbar/星星-未选.png'" 
                  class="star-icon"></image>
          </view>
              <text class="rating-score">{{groupbuy.shopRating}}</text>
              <text class="rating-count">({{groupbuy.ratingCount || 238}})</text>
        </view>
      </view>
        </view>
        <view class="shop-action">
        <text>进店</text>
          <text class="cuIcon-right"></text>
      </view>
    </view>
    
      <!-- 详情选项卡 -->
      <view class="detail-tabs">
        <view class="tab-header">
          <view class="tab-item" 
            v-for="(tab, index) in tabs" 
            :key="index"
            :class="{ active: currentTab === index }"
            @click="switchTab(index)">
            <text>{{tab.name}}</text>
            <view class="tab-line" v-if="currentTab === index"></view>
      </view>
    </view>
    
        <view class="tab-content">
    <!-- 商品详情 -->
          <view v-if="currentTab === 0" class="product-details">
        <view class="detail-desc">{{groupbuy.description}}</view>
        
        <!-- 套餐内容 -->
        <view class="package-content">
          <view class="package-title">套餐内容</view>
          <view class="package-items">
            <view class="package-item" v-for="(item, index) in groupbuy.packageItems" :key="index">
              <view class="package-item-header">
                <text class="package-item-name">{{item.name}}</text>
                <text class="package-item-quantity">{{item.quantity}}{{item.unit}}</text>
              </view>
              <text class="package-item-desc">{{item.desc}}</text>
            </view>
          </view>
          <view class="package-notice">
            <text class="notice-title">套餐须知：</text>
            <text class="notice-text">1. 套餐建议3人使用，可提前1小时预约</text>
            <text class="notice-text">2. 套餐内饮料可根据个人喜好替换</text>
            <text class="notice-text">3. 套餐内容不可拆分单独使用</text>
          </view>
        </view>
        
        <image v-for="(img, index) in groupbuy.detailImages" 
          :key="index" 
          :src="img" 
          mode="widthFix" 
          class="detail-image"></image>
      </view>
          
          <!-- 活动规则 -->
          <view v-if="currentTab === 1" class="product-rules">
            <view class="rule-item" v-for="(rule, index) in groupbuy.rules" :key="index">
              <text class="rule-number">{{index + 1}}.</text>
              <text class="rule-text">{{rule}}</text>
      </view>
    </view>
    
    <!-- 用户评价 -->
          <view v-if="currentTab === 2" class="product-reviews">
      <view class="reviews-header">
              <text class="reviews-title">用户评价({{groupbuy.reviews ? groupbuy.reviews.length : 0}})</text>
              <text class="reviews-rate">好评率 98%</text>
            </view>
            
            <view class="review-tags">
              <view class="review-tag" 
                v-for="(tag, index) in reviewTags" 
                :key="index" 
                :class="{ active: activeReviewTag === index }"
                @click="switchReviewTag(index)">
                {{tag}}
        </view>
      </view>
      
            <view class="review-item" v-for="(review, index) in groupbuy.reviews" :key="index">
          <view class="review-header">
            <image :src="review.avatar" class="user-avatar"></image>
            <view class="review-user">
              <text class="user-name">{{review.username}}</text>
              <view class="review-rating">
                <image src="/static/images/tabbar/星星-选中.png" class="star" v-for="i in review.rating" :key="i"></image>
                <image src="/static/images/tabbar/星星-未选.png" class="star" v-for="i in 5-review.rating" :key="i+5"></image>
              </view>
            </view>
            <text class="review-time">{{review.time}}</text>
          </view>
          <view class="review-content">{{review.content}}</view>
          <view class="review-images" v-if="review.images && review.images.length > 0">
            <image v-for="(img, imgIndex) in review.images" :key="imgIndex" :src="img" mode="aspectFill" class="review-image"></image>
          </view>
              
              <view class="review-actions">
                <view class="review-action" @click="likeReview(index)">
                  <text class="cuIcon-appreciate"></text>
                  <text>赞同(23)</text>
                </view>
                <view class="review-action" @click="replyReview(index)">
                  <text class="cuIcon-comment"></text>
                  <text>回复(5)</text>
                </view>
        </view>
      </view>
      
            <view class="empty-reviews" v-if="!groupbuy.reviews || groupbuy.reviews.length === 0">
        <text>暂无评价</text>
            </view>
            
            <view class="more-reviews" v-if="groupbuy.reviews && groupbuy.reviews.length > 0" @click="viewMoreReviews">
              <text>查看全部评价</text>
              <text class="cuIcon-right"></text>
            </view>
          </view>
      </view>
    </view>
      
      <!-- 推荐商品 -->
      <view class="recommend-section">
        <view class="recommend-header">
          <text class="recommend-title">猜你喜欢</text>
        </view>
        <view class="recommend-list">
          <view class="recommend-item" v-for="item in recommendProducts" :key="item.id" @click="viewRecommendProduct(item.id)">
            <image :src="item.image" class="recommend-image"></image>
            <view class="recommend-info">
              <text class="recommend-name">{{item.name}}</text>
              <view class="recommend-price">
                <text class="price-symbol">¥</text>
                <text class="price-value">{{item.price}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 底部安全区 -->
      <view class="safe-area-inset-bottom"></view>
    
    <!-- 底部购买栏 -->
    <view class="bottom-bar">
      <view class="bottom-left">
        <view class="action-btn" @click="contactService">
          <image src="/static/images/tabbar/客服.png" class="action-icon"></image>
          <text>客服</text>
        </view>
        <view class="action-btn" @click="goToShop">
          <image src="/static/images/tabbar/店铺.png" class="action-icon"></image>
          <text>店铺</text>
        </view>
        <view class="action-btn" @click="toggleFavorite">
          <image :src="isFavorite ? '/static/images/tabbar/收藏-选中.png' : '/static/images/tabbar/收藏.png'" class="action-icon"></image>
          <text>收藏</text>
        </view>
        <view class="action-btn" @click="share">
          <image src="/static/images/tabbar/分享.png" class="action-icon"></image>
          <text>分享</text>
        </view>
      </view>
      
      <view class="buy-buttons" v-if="groupbuy.enableGroupBuy">
        <view class="buy-btn normal-buy" @click="buyNow">
          <view class="buy-price">¥{{formattedRegularPrice}}</view>
          <view class="buy-label">单独购买</view>
        </view>
        <view class="buy-btn group-buy" @click="groupBuy">
          <view class="buy-price-row">
            <text class="buy-price">¥{{formattedGroupPrice}}</text>
            <text class="original-tag">¥{{formattedMarketPrice}}</text>
          </view>
          <view class="buy-label">
            <text>{{groupbuy.minGroupSize || 3}}人团</text>
            <text class="save-amount">省¥{{saveAmount}}</text>
          </view>
          <view class="verify-tag">到店核销</view>
        </view>
      </view>
      
      <view class="buy-buttons" v-else>
        <view class="buy-btn group-buy full-width" @click="buyNow">
          <view class="buy-price-row">
            <text class="buy-price">¥{{formattedGroupPrice}}</text>
            <text class="original-tag">¥{{formattedMarketPrice}}</text>
          </view>
          <view class="buy-label">
            <text>立即购买</text>
            <text class="save-amount">省¥{{saveAmount}}</text>
          </view>
          <view class="verify-tag">到店核销</view>
        </view>
      </view>
    </view>
    
    <!-- 购买弹窗 -->
    <view class="buy-popup" v-if="showBuyPopup">
      <view class="popup-mask" @click="closeBuyPopup"></view>
      <view class="popup-content">
        <view class="popup-header">
          <image :src="groupbuy.images[0]" class="popup-product-image"></image>
          <view class="popup-product-info">
            <text class="popup-price">¥{{currentBuyType === 'group' ? formattedGroupPrice : formattedRegularPrice}}</text>
            <text class="popup-stock">库存: {{groupbuy.stock}}件</text>
            <view class="popup-verify-tag">到店核销</view>
          </view>
          <image src="/static/images/tabbar/关闭.png" class="popup-close" @click="closeBuyPopup"></image>
        </view>
        
          <view class="popup-section">
            <text class="section-title">购买数量</text>
            <view class="quantity-selector">
              <view class="quantity-btn" @click="decreaseQuantity">-</view>
              <input type="number" v-model="buyQuantity" class="quantity-input" />
              <view class="quantity-btn" @click="increaseQuantity">+</view>
            </view>
          </view>
        
        <view class="popup-verify-info">
          <text class="verify-title">使用须知</text>
          <text class="verify-item">· 购买后7天内有效</text>
          <text class="verify-item">· 到店出示券码核销</text>
          <text class="verify-item">· 每日10:00-21:00可用</text>
        </view>
        
        <view class="popup-footer">
          <view class="popup-buy-btn" @click="confirmBuy">
            立即购买
          </view>
        </view>
      </view>
    </view>
      
    <!-- 分享弹窗 -->
    <view class="share-popup" v-if="showSharePopup">
      <view class="popup-mask" @click="closeSharePopup"></view>
      <view class="share-content">
        <view class="share-header">
          <text>分享到</text>
          <image src="/static/images/tabbar/关闭.png" class="popup-close" @click="closeSharePopup"></image>
        </view>
        <view class="share-options">
          <view class="share-option" @click="share">
            <image src="/static/demo/wechat.png" class="share-icon"></image>
            <text>微信</text>
          </view>
          <view class="share-option" @click="share">
            <image src="/static/demo/moments.png" class="share-icon"></image>
            <text>朋友圈</text>
          </view>
          <view class="share-option" @click="share">
            <image src="/static/demo/qq.png" class="share-icon"></image>
            <text>QQ</text>
          </view>
          <view class="share-option" @click="share">
            <image src="/static/demo/weibo.png" class="share-icon"></image>
            <text>微博</text>
          </view>
        </view>
        <view class="share-poster" @click="generatePoster">
          <text>生成海报</text>
        </view>
      </view>
    </view>
    </block>
  </view>
</template>

<script>
import { smartNavigate } from '@/utils/navigation.js';
import DistributionSection from '@/components/distribution-section.vue';

export default {
  components: {
    DistributionSection
  },
  data() {
    return {
      id: null,
      type: 'group', // 默认为拼团活动
      groupbuy: {},
      shop: {},
      statusBarHeight: 20,
      navbarHeight: 82,
      tabs: [
        { name: '商品详情' },
        { name: '活动规则' },
        { name: '用户评价' }
      ],
      currentTab: 0,
      currentSwiperIndex: 0,
      countdown: {
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0
      },
      countdownTimer: null,
      loading: true,
      reviewTags: ['全部', '有图(12)', '好评(32)', '差评(1)'],
      showBuyPopup: false,
      buyQuantity: 1,
      currentBuyType: 'normal',
      showSharePopup: false,
      activeReviewTag: 0,
      isFavorite: false,
      recommendProducts: [
        {
          id: 1,
          name: '老磁州水饺',
          price: 25.9,
          image: '/static/demo/food2.jpg'
        },
        {
          id: 2,
          name: '特色烤肉套餐',
          price: 59.9,
          image: '/static/demo/food1.jpg'
        },
        {
          id: 3,
          name: '精品麻辣烫',
          price: 19.9,
          image: '/static/demo/food3.jpg'
        },
        {
          id: 4,
          name: '招牌炸鸡',
          price: 39.9,
          image: '/static/demo/food4.jpg'
        }
      ],
      // 适用门店列表
      storeList: [
        {
          id: 1,
          name: '老磁州美食坊(中心广场店)',
          logo: '/static/demo/shop-logo.png',
          address: '磁州市中心广场东路88号',
          distance: '1km'
        },
        {
          id: 2,
          name: '老磁州美食坊(西湖店)',
          logo: '/static/demo/shop-logo.png',
          address: '磁州市西湖路120号',
          distance: '3.6km'
        },
        {
          id: 3,
          name: '老磁州美食坊(北城店)',
          logo: '/static/demo/shop-logo.png',
          address: '磁州市北城区商业街56号',
          distance: '5.2km'
        }
      ],
      // 本店其他拼团
      otherGroups: [
        {
          id: 101,
          title: '老磁州特色凉皮 2人套餐',
          image: '/static/demo/food2.jpg',
          groupPrice: '29.9',
          marketPrice: '59.9',
          groupSize: 2,
          soldCount: 3000,
          discount: '5.3折热销中',
          distance: '1km',
          soldTag: '新品',
          usageInfo: '到店使用',
          shopName: '磁州同城折扣店'
        },
        {
          id: 102,
          title: '老磁州肉夹馍 家庭套餐',
          image: '/static/demo/food3.jpg',
          groupPrice: '39.9',
          marketPrice: '69.9',
          groupSize: 3,
          soldCount: 1000,
          discount: '6.2折热销中',
          distance: '1.2km',
          soldTag: '',
          usageInfo: '到店使用',
          shopName: '磁州同城折扣店'
        },
        {
          id: 103,
          title: '老磁州特色小吃拼盘',
          image: '/static/demo/food1.jpg',
          groupPrice: '49.9',
          marketPrice: '79.9',
          groupSize: 4,
          soldCount: 900,
          discount: '5.8折热销中',
          distance: '1.5km',
          soldTag: '新品',
          usageInfo: '免预约',
          shopName: '磁州同城折扣店'
        }
      ]
    };
  },
  computed: {
    // 根据活动类型显示对应的标题
    activityTypeTitle() {
      switch(this.type) {
        case 'flash':
          return '秒杀详情';
        case 'group':
          return '拼团详情';
        case 'discount':
          return '满减详情';
        case 'coupon':
          return '优惠券详情';
        default:
          return '活动详情';
      }
    },
    
    // 计算折扣
    discount() {
      const originalPrice = parseFloat(this.groupbuy.originalPrice);
      const groupPrice = parseFloat(this.groupbuy.groupPrice);
      if (originalPrice <= 0) return 10;
      const discount = Math.round((groupPrice / originalPrice) * 10);
      return discount;
    },
    
    // 计算折扣百分比
    discountPercent() {
      const marketPrice = parseFloat(this.groupbuy.marketPrice || this.groupbuy.originalPrice);
      const groupPrice = parseFloat(this.groupbuy.groupPrice);
      if (marketPrice <= 0) return 0;
      const discountPercent = Math.round(100 - (groupPrice / marketPrice) * 100);
      return discountPercent;
    },
    
    // 计算节省金额
    saveAmount() {
      const marketPrice = parseFloat(this.groupbuy.marketPrice || this.groupbuy.originalPrice);
      const groupPrice = parseFloat(this.groupbuy.groupPrice);
      return (marketPrice - groupPrice).toFixed(2);
    },
    
    // 计算进度条宽度
    progressWidth() {
      if (this.groupbuy.targetCount <= 0) return '0%';
      const progress = (this.groupbuy.soldCount / this.groupbuy.targetCount) * 100;
      return progress > 100 ? '100%' : `${progress}%`;
    },
    
    // 格式化团购价格
    formattedGroupPrice() {
      // 如果是数字，转换为带两位小数的字符串
      if (typeof this.groupbuy.groupPrice === 'number') {
        return this.groupbuy.groupPrice.toFixed(2);
      }
      return this.groupbuy.groupPrice;
    },
    
    // 格式化原价
    formattedOriginalPrice() {
      // 如果是数字，转换为带两位小数的字符串
      if (typeof this.groupbuy.originalPrice === 'number') {
        return this.groupbuy.originalPrice.toFixed(2);
      }
      return this.groupbuy.originalPrice;
    },
    
    // 格式化市场价
    formattedMarketPrice() {
      if (typeof this.groupbuy.marketPrice === 'number') {
        return this.groupbuy.marketPrice.toFixed(2);
      }
      return this.groupbuy.marketPrice || this.formattedOriginalPrice;
    },
    
    // 格式化日常价
    formattedRegularPrice() {
      if (typeof this.groupbuy.regularPrice === 'number') {
        return this.groupbuy.regularPrice.toFixed(2);
      }
      return this.groupbuy.regularPrice || this.formattedOriginalPrice;
    },
    
    // 计算与市场价的差价
    marketPriceDiff() {
      const marketPrice = parseFloat(this.groupbuy.marketPrice || this.groupbuy.originalPrice);
      const groupPrice = parseFloat(this.groupbuy.groupPrice);
      return (marketPrice - groupPrice).toFixed(2);
    },
    
    // 计算与日常价的差价
    regularPriceDiff() {
      const regularPrice = parseFloat(this.groupbuy.regularPrice || this.groupbuy.originalPrice);
      const groupPrice = parseFloat(this.groupbuy.groupPrice);
      return (regularPrice - groupPrice).toFixed(2);
    },
    
    // 参与人数展示
    participantCount() {
      return this.groupbuy.soldCount > 0 ? this.groupbuy.soldCount : 0;
    },
    
    // 剩余参与人数
    remainingCount() {
      const remaining = this.groupbuy.targetCount - this.groupbuy.soldCount;
      return remaining > 0 ? remaining : 0;
    },
    
    // 是否已达到目标人数
    isTargetReached() {
      return this.groupbuy.soldCount >= this.groupbuy.targetCount;
    }
  },
  onLoad(options) {
    if (options && options.id) {
      this.id = options.id;
    }
    
    // 获取活动类型
    if (options && options.type) {
      this.type = options.type;
    }
    
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 62; // 状态栏 + 标题栏高度
    
    // 模拟加载数据
    setTimeout(() => {
      this.loadGroupbuyDetail();
      this.startCountdown();
    }, 500);
  },
  onUnload() {
    // 清除倒计时定时器
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
      this.countdownTimer = null;
    }
  },
  // 分享功能
  onShareAppMessage() {
    return {
      title: this.groupbuy.title,
      path: `/subPackages/activity-showcase/pages/detail/index?id=${this.id}`,
      imageUrl: this.groupbuy.images[0]
    };
  },
  methods: {
    // 加载团购详情
    loadGroupbuyDetail() {
      // 模拟API加载数据
      this.loading = true;
      
      // 在实际应用中，这里应该是从API获取数据
      setTimeout(() => {
        // 模拟数据
        this.groupbuy = {
          id: this.id || 1,
          title: '【磁州特色】老磁州脆皮锅贴 正宗手工制作 20个/份',
          images: [
            '/static/demo/food1.jpg',
            '/static/demo/food2.jpg',
            '/static/demo/food3.jpg'
          ],
          groupPrice: '39.90',
          originalPrice: '59.90',
          marketPrice: '69.90',
          regularPrice: '49.90',
          soldCount: 126,
          targetCount: 200,
          startTime: new Date('2023-12-01'),
          endTime: new Date('2023-12-31'),
          tags: ['限时特惠', '热销爆款', '免预约'],
          shopName: '老磁州美食坊',
          shopLogo: '/static/demo/shop-logo.png',
          shopRating: 4.8,
          ratingCount: 238,
          stock: 500,
          description: '老磁州脆皮锅贴，采用传统工艺精心制作，外皮金黄酥脆，内馅鲜嫩多汁，是磁州地区特色美食。本店锅贴选用上等面粉和新鲜猪肉制作，不添加任何防腐剂和人工色素。',
          detailImages: [
            '/static/demo/detail1.jpg',
            '/static/demo/detail2.jpg'
          ],
          rules: [
            '团购券有效期为购买后30天内，请在有效期内使用',
            '营业时间：周一至周日 10:00-22:00，法定节假日正常营业',
            '每张团购券限1人使用，不再与店内其他优惠同享',
            '团购券使用前请提前致电商家预约',
            '如有特殊情况，请与商家协商解决'
          ],
          reviews: [
            {
              id: 1,
              user: {
                name: '张先生',
                avatar: '/static/demo/avatar1.png'
              },
              rating: 5,
              content: '味道很赞，皮薄馅大，一口下去超级满足！店家服务也很好，环境整洁，值得推荐！',
              images: [
                '/static/demo/review1.jpg',
                '/static/demo/review2.jpg'
              ],
              time: '2023-11-28',
              likes: 12
            },
            {
              id: 2,
              user: {
                name: '李女士',
                avatar: '/static/demo/avatar2.png'
              },
              rating: 4,
              content: '第二次来购买了，锅贴依然那么好吃，就是今天人有点多，等了一会儿。',
              images: [],
              time: '2023-11-25',
              likes: 5
            }
          ],
          prices: [
            {
              id: 1,
              name: '标准20个装',
              price: 39.9
            },
            {
              id: 2,
              name: '超值30个装',
              price: 59.9
            },
            {
              id: 3,
              name: '家庭40个装',
              price: 75.9
            }
          ],
          packageItems: [
            {
              name: '脆皮锅贴',
              quantity: 20,
              unit: '个',
              desc: '传统手工制作，外皮酥脆，内馅多汁'
            },
            {
              name: '特制蘸料',
              quantity: 2,
              unit: '份',
              desc: '秘制配方，提味增香'
            }
          ],
          enableGroupBuy: true,
          minGroupSize: 3
        };
        
        this.shop = {
          id: 1,
          name: this.groupbuy.shopName,
          logo: this.groupbuy.shopLogo,
          rating: this.groupbuy.shopRating,
          isFollowed: false
        };
        
        this.loading = false;
      }, 1000);
    },
    
    // 开始倒计时
    startCountdown() {
      // 先立即更新一次
      this.updateCountdown();
      
      // 设置定时器，每秒更新一次
      this.countdownTimer = setInterval(() => {
        this.updateCountdown();
      }, 1000);
    },
    
    // 更新倒计时
    updateCountdown() {
      if (!this.groupbuy || !this.groupbuy.endTime) return;
      
      const now = new Date().getTime();
      const endTime = new Date(this.groupbuy.endTime).getTime();
      const diff = endTime - now;
      
      if (diff <= 0) {
        // 活动已结束
        this.countdown = { days: 0, hours: 0, minutes: 0, seconds: 0 };
        if (this.countdownTimer) {
          clearInterval(this.countdownTimer);
          this.countdownTimer = null;
        }
        return;
      }
      
      // 计算天、时、分、秒
      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);
      
      // 更新倒计时数据
      this.countdown = {
        days: days < 10 ? '0' + days : days,
        hours: hours < 10 ? '0' + hours : hours,
        minutes: minutes < 10 ? '0' + minutes : minutes,
        seconds: seconds < 10 ? '0' + seconds : seconds
      };
    },
    
    // 获取模拟数据
    getMockData(id) {
      // 转为数字
      id = parseInt(id);
      
      // 通用模拟数据
      const mockData = {
        id: id,
        title: '商户特惠 | 正宗磁州肉夹馍 3人套餐',
        images: [
          '/static/demo/food1.jpg',
          '/static/demo/food2.jpg',
          '/static/demo/food3.jpg'
        ],
        groupPrice: 59.90,
        originalPrice: 99.00,
        marketPrice: 99.00,
        regularPrice: 79.00,
        soldCount: 358,
        targetCount: 500,
        startTime: new Date('2023-08-15 10:00:00'),
        endTime: new Date(Date.now() + 86400000 * 3), // 3天后结束
        tags: ['本地美食', '限时特惠', '当日现做'],
        shopName: '老磁州肉夹馍',
        shopLogo: '/static/demo/shop-logo.png',
        shopRating: 4.9,
        ratingCount: 238,
        stock: 200,
        description: '精选五花肉，配以秘制调料，现烤现卖，外酥里嫩，肉香四溢。3人套餐包含肉夹馍3个、凉皮2份、特色饮料3杯，到店消费更有特色小吃免费品尝！',
        detailImages: [
          '/static/demo/food-detail1.jpg',
          '/static/demo/food-detail2.jpg'
        ],
        packageItems: [
          { name: '招牌肉夹馍', quantity: 3, unit: '个', desc: '精选五花肉，外酥里嫩' },
          { name: '特色凉皮', quantity: 2, unit: '份', desc: '劲道爽滑，酸辣可口' },
          { name: '饮料', quantity: 3, unit: '杯', desc: '可乐/雪碧/果茶任选' },
          { name: '赠品小食', quantity: 1, unit: '份', desc: '薯条/鸡米花任选一份' }
        ],
        rules: [
          '有效期：购买后7天内有效',
          '使用方式：到店出示券码核销',
          '营业时间：每日10:00-21:00',
          '地址：磁州市中心广场东路88号',
          '每人限购5份',
          '支持随时退款',
          '团购专享价格',
          '到店消费可免费获赠热茶一杯'
        ],
        enableGroupBuy: true, // 是否开启拼团
        minGroupSize: 3, // 最低成团人数
        reviews: [
          {
            username: '张先生',
            avatar: '/static/demo/avatar1.png',
            rating: 5,
            time: '2023-10-15',
            content: '肉夹馍非常好吃，肉多且入味，饼也烤得恰到好处，酥脆可口。团购价格很实惠，强烈推荐！',
            images: [
              '/static/demo/review1.jpg',
              '/static/demo/review2.jpg'
            ]
          },
          {
            username: '李女士',
            avatar: '/static/demo/avatar2.png',
            rating: 5,
            time: '2023-10-12',
            content: '配送速度很快，包装也很好，肉夹馍还是热乎的。味道很正宗，和老家的味道一样，很满意。',
            images: [
              '/static/demo/review3.jpg'
            ]
          }
        ],
        prices: [
          { label: '市场价', value: '¥30' },
          { label: '日常价', value: '¥25' }
        ],
        viewCount: 1268,
      };
      
      // 根据ID返回不同数据
      if (id === 102) {
        mockData.title = '夏日清凉 | 网红冰淇淋双人套餐';
        mockData.groupPrice = 39.9;
        mockData.originalPrice = 68;
        mockData.regularPrice = 58;
        mockData.soldCount = 128;
        mockData.shopName = '冰雪甜品屋';
        mockData.description = '精选进口食材，纯手工制作，口感细腻，甜而不腻。套餐包含：招牌冰淇淋2份，水果沙拉1份，特调饮品2杯。';
      } else if (id === 103) {
        mockData.title = '周末特惠 | 精致下午茶套餐';
        mockData.groupPrice = 88;
        mockData.originalPrice = 138;
        mockData.regularPrice = 118;
        mockData.soldCount = 42;
        mockData.shopName = '巴黎花园咖啡';
        mockData.description = '法式精致下午茶，环境优雅，食材新鲜。套餐包含：精选茶饮2杯，马卡龙4个，水果塔2个，提拉米苏1份。';
      } else if (id === 104) {
        mockData.title = '磁州特色 | 手工水饺30个';
        mockData.groupPrice = 29.9;
        mockData.originalPrice = 45;
        mockData.regularPrice = 39;
        mockData.soldCount = 84;
        mockData.shopName = '老街饺子馆';
        mockData.description = '选用本地新鲜食材，现包现煮，皮薄馅大，鲜香可口。可选口味：猪肉白菜、韭菜鸡蛋、三鲜、牛肉。';
      } else if (id === 105) {
        mockData.title = '本地特色 | 正宗磁州烤肉套餐';
        mockData.groupPrice = 99;
        mockData.originalPrice = 168;
        mockData.regularPrice = 138;
        mockData.soldCount = 36;
        mockData.shopName = '老街烤肉';
        mockData.description = '传统炭火烤制，选用本地散养黑猪肉，搭配秘制调料，肉质鲜嫩多汁。套餐包含：烤肉拼盘、特色小菜4份、米饭2份。';
      }
      
      return mockData;
    },
    
    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${year}年${month}月${day}日`;
    },
    
    // 导航到商家页面
    goToShop() {
      smartNavigate(`/pages/business/shop-detail?id=${this.groupbuy.id}`);
    },
    
    // 切换选项卡
    switchTab(index) {
      this.currentTab = index;
    },
    
    // 联系客服
    contactService() {
      uni.showToast({
        title: '正在连接客服...',
        icon: 'none'
      });
    },
    
    // 切换收藏状态
    toggleFavorite() {
      this.isFavorite = !this.isFavorite;
      uni.showToast({
        title: this.isFavorite ? '收藏成功' : '已取消收藏',
        icon: 'success'
      });
    },
    
    // 切换关注商家状态
    toggleFollow() {
      if (this.shop) {
        this.shop.isFollowed = !this.shop.isFollowed;
        uni.showToast({
          title: this.shop.isFollowed ? '已关注' : '已取消关注',
          icon: 'success'
        });
      }
    },
    
    // 单独购买
    buyNow() {
      this.currentBuyType = 'normal';
      this.showBuyPopup = true;
    },
    
    // 团购购买
    groupBuy() {
      this.currentBuyType = 'group';
      this.showBuyPopup = true;
    },
    
    // 关闭购买弹窗
    closeBuyPopup() {
      this.showBuyPopup = false;
    },
    
    // 减少购买数量
    decreaseQuantity() {
      if (this.buyQuantity > 1) {
        this.buyQuantity--;
      }
    },
    
    // 增加购买数量
    increaseQuantity() {
      if (this.buyQuantity < this.groupbuy.stock) {
        this.buyQuantity++;
      } else {
        uni.showToast({
          title: '已达到最大库存',
          icon: 'none'
        });
      }
    },
    
    // 确认购买
    confirmBuy() {
      const price = this.currentBuyType === 'group' ? this.groupbuy.groupPrice : this.groupbuy.originalPrice;
      const totalPrice = (parseFloat(price) * this.buyQuantity).toFixed(2);
      
      uni.showModal({
        title: '确认购买',
        content: `您将以¥${totalPrice}购买${this.buyQuantity}件商品，是否确认？`,
        success: (res) => {
          if (res.confirm) {
            // 处理购买逻辑
            uni.showLoading({
              title: '正在下单'
            });
            
            // 模拟API请求
            setTimeout(() => {
              uni.hideLoading();
              this.showBuyPopup = false;
              
              // 跳转到支付页面
              smartNavigate(`/pages/pay/index?amount=${totalPrice}&title=${encodeURIComponent(this.groupbuy.title)}`);
            }, 800);
          }
        }
      });
    },
    
    // 轮播图切换
    onSwiperChange(e) {
      this.currentSwiperIndex = e.detail.current;
    },
    
    // 分享功能
    share() {
      this.showSharePopup = true;
    },
    
    // 关闭分享弹窗
    closeSharePopup() {
      this.showSharePopup = false;
    },
    
    // 切换评价标签
    switchReviewTag(index) {
      this.activeReviewTag = index;
    },
    
    // 点赞评价
    likeReview(index) {
      uni.showToast({
        title: '点赞成功',
        icon: 'success'
      });
    },
    
    // 回复评价
    replyReview(index) {
      uni.showToast({
        title: '暂不支持回复',
        icon: 'none'
      });
    },
    
    // 查看更多评价
    viewMoreReviews() {
      uni.showToast({
        title: '查看更多评价',
        icon: 'none'
      });
    },
    
    // 领取优惠券
    receiveCoupon() {
      uni.showToast({
        title: '优惠券领取成功',
        icon: 'success'
      });
    },
    
    // 切换拼团/直购模式（仅用于演示）
    toggleGroupBuyMode() {
      this.groupbuy.enableGroupBuy = !this.groupbuy.enableGroupBuy;
      // 显示提示
      uni.showToast({
        title: this.groupbuy.enableGroupBuy ? '已切换到拼团模式' : '已切换到直购模式',
        icon: 'none'
      });
    },
    
    // 格式化价格
    formatPrice(price) {
      // ... existing code ...
    },
    
    // 生成分享海报
    generatePoster() {
      uni.showLoading({
        title: '生成中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '海报已生成',
          icon: 'success'
        });
        this.closeSharePopup();
      }, 1500);
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 查看推荐商品
    viewRecommendProduct(productId) {
      smartNavigate(`/subPackages/activity-showcase/pages/detail/index?id=${productId}`);
    },
    
    // 查看所有适用门店
    viewAllStores() {
      uni.showToast({
        title: '查看全部门店',
        icon: 'none'
      });
    },
    
    // 查看门店详情
    viewStoreDetail(storeId) {
      smartNavigate(`/pages/business/store-detail?id=${storeId}`);
    },
    
    // 查看更多拼团
    viewMoreGroups() {
      smartNavigate('/subPackages/activity-showcase/pages/group-buy/index');
    }
  }
};
</script>

<style lang="scss" scoped>
/* 整体容器 */
.detail-container {
  background-color: #F5F7FA;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 导航栏 */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
}

.nav-content {
  display: flex;
  align-items: center;
  height: 80rpx;
}

.nav-icon {
  width: 24rpx;
  height: 24rpx;
  padding: 0 10rpx;
}

.nav-title {
  font-size: 24rpx;
  font-weight: 500;
}

.back-button {
  display: flex;
  align-items: center;
}

.back-icon {
  width: 38rpx;
  height: 38rpx;
}

/* 商品轮播图 */
.product-swiper {
  height: 520rpx; /* 从580rpx进一步减小到520rpx */
  width: 100%;
  border-radius: 0 0 24rpx 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
}

.swiper-image {
  width: 100%;
  height: 100%;
}

/* 商品基本信息卡片 */
.basic-info-card {
  margin: -30rpx 24rpx 24rpx; /* 从-40rpx调整为-30rpx */
  padding: 24rpx 30rpx; /* 从30rpx调整为24rpx 30rpx，减小上下内边距 */
  background-color: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.05);
  position: relative;
  z-index: 10;
}

.price-section {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12rpx; /* 从16rpx减小到12rpx */
}

.price-main {
  display: flex;
  align-items: baseline;
}

.price-symbol {
  font-size: 36rpx;
  font-weight: 500;
  color: #FF3B30;
}

.price-value {
  font-size: 54rpx; /* 从60rpx减小到54rpx */
  font-weight: 700;
  color: #FF3B30;
  line-height: 1;
}

.price-tag {
  background-color: #FF3B30;
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  margin-left: 12rpx;
}

.price-tag-text {
  font-size: 24rpx;
  color: #FFFFFF;
  font-weight: 500;
}

.price-compare {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.compare-item {
  display: flex;
  align-items: center;
  margin-bottom: 6rpx;
}

.compare-label {
  font-size: 24rpx;
  color: #8E8E93;
  margin-right: 8rpx;
}

.compare-price {
  font-size: 26rpx;
  color: #8E8E93;
  text-decoration: line-through;
}

.save-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx; /* 从20rpx减小到16rpx */
}

.save-tag {
  display: flex;
  align-items: center;
  background-color: rgba(255, 59, 48, 0.1);
  padding: 6rpx 16rpx;
  border-radius: 8rpx;
}

.save-icon {
  width: 32rpx;
  height: 32rpx;
  background-color: #FF3B30;
  color: #FFFFFF;
  font-size: 20rpx;
  text-align: center;
  line-height: 32rpx;
  border-radius: 4rpx;
  margin-right: 8rpx;
}

.save-text {
  font-size: 24rpx;
  color: #FF3B30;
  font-weight: 600;
}

.limited-text {
  font-size: 24rpx;
  color: #FF9500;
  font-weight: 600;
}

.market-price {
  font-size: 28rpx;
  color: #8E8E93;
  text-decoration: line-through;
  margin-right: 12rpx;
}

.product-title-row {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx; /* 从24rpx减小到20rpx */
}

.group-tag {
  background-color: #FF3B30;
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
}

.product-title {
  font-size: 32rpx; /* 从36rpx减小到32rpx */
  font-weight: 700;
  color: #000000;
}

/* 销量和倒计时 */
.sales-countdown {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: rgba(255, 59, 48, 0.1);
  padding: 16rpx;
  border-radius: 12rpx;
}

.sales-info {
  display: flex;
  align-items: center;
}

.sales-count {
  font-size: 28rpx;
  color: #8E8E93;
  margin-right: 16rpx;
}

.sales-divider {
  color: #8E8E93;
  margin: 0 16rpx;
}

.view-count {
  font-size: 28rpx;
  color: #8E8E93;
}

.mini-countdown {
  display: flex;
  align-items: center;
}

.countdown-text {
  font-size: 28rpx;
  color: #8E8E93;
  margin-right: 16rpx;
}

.time-block {
  background-color: #FF3B30;
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: 600;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
}

.time-colon {
  color: #FF3B30;
  margin: 0 8rpx;
  font-weight: 600;
}

/* 拼团信息卡片 */
.group-card {
  margin: 0 24rpx 24rpx;
  padding: 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.group-title {
  display: flex;
  align-items: center;
}

.group-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 12rpx;
}

.group-more {
  font-size: 26rpx;
  color: #8E8E93;
}

.group-location {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 10rpx 0;
  border-bottom: 1rpx dashed #F2F2F7;
}

.location-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.location-text {
  font-size: 26rpx;
  color: #333333;
}

.group-avatars {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.avatar-item {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  overflow: hidden;
  border: 2rpx solid #FFFFFF;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.avatar-more {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F2F2F7;
  color: #8E8E93;
  font-size: 24rpx;
  margin-right: 16rpx;
}

.recent-join {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.recent-user {
  font-size: 28rpx;
  color: #333333;
  font-weight: 600;
}

.join-time {
  font-size: 24rpx;
  color: #FF3B30;
}

.group-progress {
  margin-bottom: 24rpx;
}

.progress-bar {
  height: 20rpx;
  background-color: #F2F2F7;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(to right, #FF3B30, #FF9500);
  border-radius: 10rpx;
}

.progress-text {
  font-size: 26rpx;
  color: #8E8E93;
  text-align: center;
}

.group-tips {
  display: flex;
  align-items: center;
  background-color: rgba(255, 59, 48, 0.1);
  padding: 16rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.tip-icon {
  margin-right: 8rpx;
  color: #FF3B30;
}

.tip-text {
  font-size: 28rpx;
  color: #333333;
}

.verification-info {
  margin-bottom: 20rpx;
}

.verification-title {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.verification-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.verification-text {
  font-size: 28rpx;
  color: #333333;
}

.verification-address {
  font-size: 26rpx;
  color: #666666;
}

.verification-time {
  font-size: 26rpx;
  color: #8E8E93;
}

.local-benefits {
  display: flex;
  justify-content: space-around;
  padding-top: 20rpx;
  border-top: 1rpx dashed #F2F2F7;
}

.benefit-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.benefit-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
}

.benefit-text {
  font-size: 24rpx;
  color: #333333;
}

/* 优惠券卡片 */
.coupon-card {
  margin: 0 24rpx 24rpx;
  padding: 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.coupon-left {
  flex: 1;
}

.coupon-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #000000;
  margin-bottom: 8rpx;
}

.coupon-desc {
  font-size: 28rpx;
  color: #8E8E93;
}

.coupon-right {
  background-color: #FF3B30;
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 6rpx;
}

.coupon-btn {
  font-size: 28rpx;
  font-weight: 600;
}

/* 商家信息卡片 */
.shop-card {
  margin: 0 24rpx 24rpx;
  padding: 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
  display: flex;
  align-items: center;
}

.shop-info {
  flex: 1;
  display: flex;
  align-items: center;
}

.shop-logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.shop-details {
  flex: 1;
}

.shop-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
  margin-bottom: 8rpx;
}

.shop-rating {
  display: flex;
  align-items: center;
}

.rating-stars {
  display: flex;
  margin-right: 12rpx;
}

.star-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 4rpx;
}

.rating-score {
  font-size: 26rpx;
  color: #FF9500;
}

.rating-count {
  font-size: 26rpx;
  color: #8E8E93;
  margin-left: 4rpx;
}

.shop-action {
  background-color: #F2F2F7;
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #000000;
}

.chevron-icon {
  width: 24rpx;
  height: 24rpx;
  margin-left: 8rpx;
}

/* 商品参数卡片 */
.params-card {
  margin: 0 24rpx 24rpx;
  padding: 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
}

.params-header {
  font-size: 36rpx;
  font-weight: 700;
  color: #000000;
  margin-bottom: 24rpx;
}

.params-list {
  display: flex;
  flex-wrap: wrap;
}

.param-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24rpx;
}

.param-name {
  font-size: 28rpx;
  color: #8E8E93;
  margin-bottom: 4rpx;
}

.param-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #000000;
}

/* 详情选项卡 */
.detail-tabs {
  margin: 0 24rpx 24rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
  overflow: hidden;
}

.tab-header {
  display: flex;
  border-bottom: 1px solid #F2F2F7;
}

.tab-item {
  flex: 1;
  padding: 24rpx 0;
  text-align: center;
  font-size: 28rpx;
  color: #8E8E93;
  position: relative;
}

.tab-item.active {
  color: #000000;
  font-weight: 500;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #FF3B30;
  border-radius: 2rpx;
}

.tab-content {
  padding: 30rpx;
}

/* 商品详情 */
.product-details {
  
}

.detail-desc {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 24rpx;
}

.detail-image {
  width: 100%;
  margin-bottom: 16rpx;
  border-radius: 12rpx;
}

/* 活动规则 */
.product-rules {
  
}

.rule-item {
  display: flex;
  margin-bottom: 20rpx;
}

.rule-number {
  margin-right: 16rpx;
  color: #FF3B30;
  font-weight: 600;
}

.rule-text {
  flex: 1;
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}

/* 用户评价 */
.product-reviews {
  
}

.reviews-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.reviews-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #000000;
}

.reviews-rate {
  font-size: 28rpx;
  color: #8E8E93;
}

.review-tags {
  display: flex;
  margin-bottom: 24rpx;
}

.review-tag {
  background-color: #F2F2F7;
  color: #8E8E93;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
}

.review-tag.active {
  background-color: #FF3B30;
  color: #FFFFFF;
}

.review-item {
  margin-bottom: 30rpx;
  padding-bottom: 30rpx;
  border-bottom: 1px solid #F2F2F7;
}

.review-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.review-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.user-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.review-user {
  flex: 1;
}

.user-name {
  font-size: 28rpx;
  color: #000000;
  margin-bottom: 4rpx;
}

.review-rating {
  display: flex;
}

.star {
  width: 24rpx;
  height: 24rpx;
  margin-right: 4rpx;
}

.review-time {
  font-size: 24rpx;
  color: #8E8E93;
}

.review-content {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 16rpx;
}

.review-images {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}

.review-image {
  width: 160rpx;
  height: 160rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  border-radius: 8rpx;
}

.review-actions {
  display: flex;
}

.review-action {
  display: flex;
  align-items: center;
  margin-right: 24rpx;
  color: #8E8E93;
  font-size: 24rpx;
}

.empty-reviews {
  text-align: center;
  padding: 60rpx 0;
  color: #8E8E93;
  font-size: 28rpx;
}

.more-reviews {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 0;
  border-top: 1px solid #F2F2F7;
  color: #8E8E93;
  font-size: 28rpx;
}

.recommend-section {
  margin: 0 24rpx 24rpx;
  padding: 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
}

.recommend-header {
  font-size: 36rpx;
  font-weight: 700;
  color: #000000;
  margin-bottom: 24rpx;
}

.recommend-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8rpx;
}

.recommend-item {
  width: calc(50% - 16rpx);
  margin: 0 8rpx 16rpx;
}

.recommend-image {
  width: 100%;
  height: 200rpx;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.recommend-info {
  padding: 0 8rpx;
}

.recommend-name {
  font-size: 28rpx;
  color: #000000;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.recommend-price {
  font-size: 28rpx;
  color: #FF3B30;
  font-weight: 500;
}

/* 底部安全区 */
.safe-area-inset-bottom {
  height: 120rpx;
}

/* 底部购买栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  box-shadow: 0 -4rpx 16rpx rgba(0,0,0,0.05);
  z-index: 100;
}

.bottom-left {
  display: flex;
  align-items: center;
  padding: 0 20rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 20rpx;
}

.action-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
}

.action-btn text {
  font-size: 20rpx;
  color: #8E8E93;
}

.buy-buttons {
  flex: 1;
  display: flex;
  height: 100%;
}

.buy-btn {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.normal-buy {
  background-color: #F2F2F7;
}

.group-buy {
  background: linear-gradient(135deg, #FF3B30, #FF9500);
}

.buy-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
}

.group-buy .buy-price {
  color: #FFFFFF;
}

.buy-price-row {
  display: flex;
  align-items: center;
}

.original-tag {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: line-through;
  margin-left: 8rpx;
}

.buy-label {
  font-size: 24rpx;
  color: #8E8E93;
  margin-top: 4rpx;
  display: flex;
  align-items: center;
}

.group-buy .buy-label {
  color: #FFFFFF;
}

.save-amount {
  background-color: #FFFFFF;
  color: #FF3B30;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  margin-left: 8rpx;
  font-weight: 600;
}

/* 购买弹窗 */
.buy-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}

.popup-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;
  padding: 30rpx;
  transform: translateY(0);
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.popup-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.popup-product-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.popup-product-info {
  flex: 1;
}

.popup-price {
  font-size: 36rpx;
  color: #FF3B30;
  font-weight: 700;
  margin-bottom: 8rpx;
}

.popup-stock {
  font-size: 24rpx;
  color: #8E8E93;
}

.popup-verify-tag {
  background-color: #FF9500;
  color: #FFFFFF;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  display: inline-block;
  margin-top: 8rpx;
}

.popup-close {
  width: 48rpx;
  height: 48rpx;
}

.popup-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 28rpx;
  color: #8E8E93;
  margin-bottom: 16rpx;
}

.quantity-selector {
  display: flex;
  align-items: center;
  border: 1px solid #E5E5EA;
  border-radius: 8rpx;
  width: 240rpx;
}

.quantity-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #8E8E93;
}

.quantity-input {
  flex: 1;
  height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  border-left: 1px solid #E5E5EA;
  border-right: 1px solid #E5E5EA;
}

.popup-verify-info {
  margin-top: 20rpx;
  background-color: #F9F9F9;
  padding: 16rpx;
  border-radius: 8rpx;
  margin-bottom: 30rpx;
}

.verify-title {
  font-size: 28rpx;
  color: #333333;
  font-weight: 600;
  margin-bottom: 10rpx;
  display: block;
}

.verify-item {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.6;
  display: block;
}

.popup-footer {
  margin-top: 60rpx;
}

.popup-buy-btn {
  height: 88rpx;
  background: linear-gradient(135deg, #FF3B30, #FF9500);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 600;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
  width: 100%;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #F2F2F7;
  border-top: 6rpx solid #FF3B30;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #8E8E93;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 分享弹窗 */
.share-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.share-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;
  padding: 30rpx;
  transform: translateY(0);
  animation: slideUp 0.3s ease;
}

.share-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.share-options {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.share-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
}

.share-option text {
  font-size: 24rpx;
  color: #8E8E93;
}

.share-poster {
  height: 88rpx;
  background: linear-gradient(135deg, #FF3B30, #FF9500);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 600;
}

/* 底部购买栏的样式 */
.verify-tag {
  position: absolute;
  top: -20rpx;
  right: 20rpx;
  background-color: #FF9500;
  color: #FFFFFF;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  transform: rotate(5deg);
}

/* 套餐内容样式 */
.package-content {
  background-color: #F9F9F9;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.package-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
  position: relative;
  padding-left: 20rpx;
}

.package-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 8rpx;
  width: 8rpx;
  height: 32rpx;
  background-color: #FF3B30;
  border-radius: 4rpx;
}

.package-items {
  margin-bottom: 16rpx;
}

.package-item {
  padding: 16rpx 0;
  border-bottom: 1px dashed #E5E5EA;
}

.package-item:last-child {
  border-bottom: none;
}

.package-item-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.package-item-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.package-item-quantity {
  font-size: 28rpx;
  color: #FF3B30;
}

.package-item-desc {
  font-size: 24rpx;
  color: #8E8E93;
  line-height: 1.4;
}

.package-notice {
  background-color: rgba(255, 149, 0, 0.1);
  padding: 16rpx;
  border-radius: 8rpx;
}

.notice-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #FF9500;
  margin-bottom: 8rpx;
  display: block;
}

.notice-text {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.6;
  display: block;
}

.buy-btn.group-buy {
  background-color: #FF3B30;
  flex: 2;
}

.buy-btn.full-width {
  flex: 1;
  width: 100%;
}

.buy-price {
  font-size: 36rpx;
  font-weight: 600;
  color: #FFFFFF;
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
  
  .navbar-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
  }
  
  .navbar-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding-top: var(--status-bar-height, 25px);
    padding-left: 30rpx;
    padding-right: 30rpx;
    box-sizing: border-box;
    
    .back-btn {
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .back-icon {
      width: 100%;
      height: 100%;
    }
    
    .navbar-title {
      font-size: 18px;
      font-weight: 600;
      color: #FFFFFF;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
    }
    
    .navbar-right {
      width: 40rpx;
    }
  }
}

/* 分享弹窗样式 */
.share-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

/* 分销弹窗样式 */
.distribution-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.distribution-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;
  padding: 30rpx;
  transform: translateY(0);
  animation: slideUp 0.3s ease;
}

.distribution-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #F2F2F7;
}

.distribution-header text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.distribution-body {
  padding: 20rpx 0;
}

.distribution-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.distribution-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 20rpx;
}

.distribution-text {
  flex: 1;
}

.distribution-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 10rpx;
  display: block;
}

.distribution-desc {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
  display: block;
}

.distribution-data {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #FFF9F9;
  border-radius: 16rpx;
}

.data-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.data-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #FF3B30;
  margin-bottom: 8rpx;
}

.data-label {
  font-size: 24rpx;
  color: #666666;
}

.distribution-btns {
  display: flex;
  justify-content: space-between;
}

.distribution-btn {
  width: 48%;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.apply-btn {
  background: linear-gradient(135deg, #FF3B30, #FF9500);
  color: #FFFFFF;
}

.poster-btn {
  background-color: #F2F2F7;
  color: #333333;
  border: 1px solid #E5E5EA;
}

/* 适用门店列表卡片 */
.store-list-card {
  margin: 0 24rpx 24rpx;
  padding: 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  position: relative;
  padding-left: 20rpx;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 8rpx;
    height: 32rpx;
    background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
    border-radius: 4rpx;
  }
}

.card-more {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #999999;
}

.store-list {
  border-radius: 12rpx;
  overflow: hidden;
}

.store-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #F5F5F7;
  
  &:last-child {
    border-bottom: none;
  }
}

.store-logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.store-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.store-name {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.store-address {
  font-size: 24rpx;
  color: #999999;
}

.store-distance {
  font-size: 24rpx;
  color: #999999;
  padding: 0 10rpx;
}

/* 本店其他拼团卡片 */
.other-group-card {
  margin: 0 24rpx 24rpx;
  padding: 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
}

.other-group-list {
  border-radius: 12rpx;
  overflow: hidden;
}

.other-group-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1px solid #F5F5F7;
  position: relative;
  
  &:last-child {
    border-bottom: none;
  }
}

.sold-tag-corner {
  position: absolute;
  top: 10rpx;
  right: 0;
  background: #FF3B5C;
  color: #FFFFFF;
  font-size: 20rpx;
  padding: 2rpx 12rpx;
  border-radius: 12rpx 0 0 12rpx;
  z-index: 1;
}

.item-image-container {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
}

.other-group-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.other-group-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.shop-location {
  display: flex;
  flex-direction: column;
  margin-bottom: 6rpx;
  
  .shop-name {
    font-size: 24rpx;
    color: #333333;
    font-weight: 500;
  }
  
  .location-info {
    font-size: 22rpx;
    color: #999999;
    margin-top: 4rpx;
  }
}

.other-group-title {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 8rpx;
}

.other-group-price {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.group-price-value {
  font-size: 32rpx;
  color: #FF2C54;
  font-weight: 600;
  margin-right: 12rpx;
}

.market-price-value {
  font-size: 24rpx;
  color: #999999;
  text-decoration: line-through;
  margin-right: 12rpx;
}

.discount-tag {
  font-size: 22rpx;
  color: #FF2C54;
  background-color: rgba(255, 44, 84, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
}

.other-group-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sold-info {
  display: flex;
  align-items: center;
  font-size: 22rpx;
  color: #999999;
}

.sold-tag {
  background-color: #F5F5F7;
  color: #666666;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
  margin-right: 8rpx;
}

.sold-count {
  margin-right: 8rpx;
}

.usage-info {
  background-color: #F5F5F7;
  color: #666666;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
}

.buy-now-btn {
  background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 8rpx 24rpx;
  border-radius: 30rpx;
  font-weight: 500;
}

/* 推荐商品部分样式调整 */
.recommend-section {
  margin: 0 24rpx 24rpx;
  padding: 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
}

.recommend-header {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 24rpx;
  position: relative;
  padding-left: 20rpx;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 8rpx;
    height: 32rpx;
    background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
    border-radius: 4rpx;
  }
}

.recommend-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8rpx;
}

.recommend-item {
  width: calc(50% - 16rpx);
  margin: 0 8rpx 16rpx;
}

.recommend-image {
  width: 100%;
  height: 200rpx;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.recommend-info {
  padding: 0 8rpx;
}

.recommend-name {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.recommend-price {
  font-size: 28rpx;
  color: #FF2C54;
  font-weight: 500;
}
</style> 