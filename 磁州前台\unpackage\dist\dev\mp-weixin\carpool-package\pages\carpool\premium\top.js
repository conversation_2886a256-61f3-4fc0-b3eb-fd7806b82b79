"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
if (!Array) {
  const _component_carpool_nav = common_vendor.resolveComponent("carpool-nav");
  _component_carpool_nav();
}
if (!Math) {
  ConfigurablePremiumActions();
}
const ConfigurablePremiumActions = () => "../../../../components/premium/ConfigurablePremiumActions.js";
const _sfc_main = {
  __name: "top",
  setup(__props) {
    const infoId = common_vendor.ref("");
    const infoData = common_vendor.ref({
      startPoint: "磁州城区",
      endPoint: "邯郸站",
      type: "people-to-car"
    });
    const carpoolData = common_vendor.ref({
      id: infoId.value || "carpool_123",
      title: "拼车置顶",
      description: `${infoData.value.startPoint} → ${infoData.value.endPoint}`,
      type: infoData.value.type
    });
    const topOptions = common_vendor.ref([
      {
        title: "置顶1天",
        desc: "24小时置顶显示",
        price: "2.00",
        days: 1
      },
      {
        title: "置顶3天",
        desc: "72小时置顶显示",
        price: "5.00",
        days: 3
      },
      {
        title: "置顶7天",
        desc: "7天置顶显示",
        price: "10.00",
        days: 7
      }
    ]);
    common_vendor.ref([
      {
        name: "微信支付",
        icon: "/static/images/tabbar/wechat-pay.png",
        id: "wxpay"
      },
      {
        name: "余额支付",
        icon: "/static/images/tabbar/wallet.png",
        id: "balance"
      }
    ]);
    const selectedOption = common_vendor.ref(1);
    common_vendor.ref(0);
    const typeText = common_vendor.computed(() => {
      const typeMap = {
        "people-to-car": "人找车",
        "car-to-people": "车找人",
        "goods-to-car": "货找车",
        "car-to-goods": "车找货"
      };
      return typeMap[infoData.value.type] || "人找车";
    });
    const typeClass = common_vendor.computed(() => {
      return infoData.value.type;
    });
    common_vendor.computed(() => {
      return topOptions.value[selectedOption.value].price;
    });
    common_vendor.onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      if (options && options.id) {
        infoId.value = options.id;
        getInfoDetail();
      }
    });
    const getInfoDetail = () => {
      common_vendor.index.__f__("log", "at carpool-package/pages/carpool/premium/top.vue:171", "获取信息ID:", infoId.value);
      setTimeout(() => {
        infoData.value = {
          startPoint: "磁州城区",
          endPoint: "邯郸站",
          type: "car-to-people"
        };
      }, 500);
    };
    const handleTopCompleted = (result) => {
      common_vendor.index.__f__("log", "at carpool-package/pages/carpool/premium/top.vue:195", "置顶完成:", result);
      if (result.type === "ad") {
        common_vendor.index.showToast({
          title: "置顶成功",
          icon: "success",
          duration: 2e3
        });
      } else if (result.type === "payment") {
        common_vendor.index.showToast({
          title: "支付成功，置顶生效",
          icon: "success",
          duration: 2e3
        });
      }
      setTimeout(() => {
        common_vendor.index.navigateBack();
      }, 2e3);
    };
    const handleTopCancelled = (result) => {
      common_vendor.index.__f__("log", "at carpool-package/pages/carpool/premium/top.vue:221", "置顶取消:", result);
      if (result.type === "ad") {
        common_vendor.index.showToast({
          title: "已取消观看广告",
          icon: "none"
        });
      } else if (result.type === "payment") {
        common_vendor.index.showToast({
          title: "已取消支付",
          icon: "none"
        });
      }
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          title: "置顶信息"
        }),
        b: common_vendor.t(typeText.value),
        c: common_vendor.n(typeClass.value),
        d: common_vendor.t(infoData.value.startPoint),
        e: common_assets._imports_0$27,
        f: common_vendor.t(infoData.value.endPoint),
        g: common_assets._imports_1$42,
        h: common_vendor.o(handleTopCompleted),
        i: common_vendor.o(handleTopCancelled),
        j: common_vendor.p({
          pageType: "carpool_top",
          showMode: "direct",
          itemData: carpoolData.value
        }),
        k: common_assets._imports_2$21
      };
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/carpool-package/pages/carpool/premium/top.js.map
