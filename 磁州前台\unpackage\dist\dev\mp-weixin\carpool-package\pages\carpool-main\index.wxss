/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 全局CSS变量定义 */
:root {
  --status-bar-color: #1677FF;
  --status-bar-bg-color: #1677FF;
  --navbar-bg-color: #1677FF;
}
page {
  background-color: #F5F8FC;
  --status-bar-color: #1677FF;
}

/* 平台特定覆盖 - 确保状态栏颜色正确 */
:root, page {
  --status-bar-color: #1677FF !important;
}
@media (prefers-color-scheme: light), (prefers-color-scheme: dark) {
uni-page-head, uni-page-head .uni-page-head {
    background-color: #1677FF !important;
}
}
/* 强制覆盖小程序状态栏颜色 */
uni-page-head {
  background-color: #1677FF !important;
}
uni-page-head .uni-page-head {
  background-color: #1677FF !important;
}
.carpool-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  padding-bottom: 140rpx;
  padding-top: calc(var(--status-bar-height) + 90rpx);
  /* 只考虑标题栏高度 */
  position: relative;
  overflow-x: hidden;
}

/* 自定义标题栏模块 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  background-color: #1677FF;
  /* 恢复为实色背景 */
  z-index: 103;
  box-shadow: none;
}

/* 标题栏内容 */
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 90rpx;
  padding: 0 30rpx;
  position: relative;
  z-index: 102;
}
.left-action {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.title-area {
  flex: 1;
  text-align: center;
}
.page-title {
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.right-action {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-icon {
  width: 44rpx;
  height: 44rpx;
  filter: brightness(0) invert(1);
}

/* 轮播图区域 - 确保在背景之上 */
.swiper-section {
  padding: 0 32rpx;
  position: relative;
  z-index: 10;
  /* 确保内容显示在圆弧背景之上 */
  margin-top: 50rpx;
  /* 从20rpx增加到50rpx，向下移动30rpx */
  background-color: transparent;
}
.swiper {
  height: 220rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(10, 132, 255, 0.15);
  background-color: #ffffff;
  position: relative;
  z-index: 1;
}
.swiper image {
  width: 100%;
  height: 100%;
  border-radius: 24rpx;
  transition: transform 0.3s ease;
}

/* 搜索框 */
.search-section {
  padding: 0 32rpx;
  position: relative;
  z-index: 10;
  /* 确保内容显示在圆弧背景之上 */
  margin-top: 15rpx;
  /* 与轮播图的间距 */
  margin-bottom: 15rpx;
  /* 下方间距 */
  background-color: transparent;
}
.search-box {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 8rpx;
  box-shadow: 0 8rpx 20rpx rgba(10, 132, 255, 0.15), 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  /* 增强阴影 */
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.7);
  position: relative;
  /* 用于伪元素定位 */
  overflow: hidden;
  /* 确保内容不溢出 */
  /* 添加微妙的内部光效 */
}
.search-box::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.5), transparent);
  pointer-events: none;
}
.search-input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #F2F7FD;
  border-radius: 16rpx;
  padding: 0 20rpx;
  height: 80rpx;
  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
  /* 内阴影 */
}
.search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
  padding: 0 10rpx;
}
.search-exchange {
  padding: 0 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.exchange-icon {
  font-size: 32rpx;
  color: #0A84FF;
  font-weight: bold;
}
.search-button {
  padding: 0 30rpx;
  height: 80rpx;
  background: linear-gradient(to right, #0A84FF, #0040DD);
  color: #ffffff;
  border-radius: 16rpx;
  margin-left: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(10, 132, 255, 0.3), 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  /* 增强阴影 */
  position: relative;
  /* 用于伪元素定位 */
  overflow: hidden;
  /* 确保内容不溢出 */
  /* 添加微妙的内部光效 */
  /* 按下效果 */
}
.search-button::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), transparent);
  pointer-events: none;
}
.search-button:active {
  transform: translateY(1rpx) scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(10, 132, 255, 0.2);
}

/* 四宫格卡片 */
.grid-section {
  padding: 10rpx;
  background-color: transparent;
  margin: 16rpx 32rpx;
  /* 减少上下边距 */
  border-radius: 24rpx;
  position: relative;
  z-index: 1;
}
.grid-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  /* 减少行间距 */
}
.grid-row:last-child {
  margin-bottom: 0;
}
.grid-item {
  width: 48%;
  /* 固定宽度为48% */
  margin: 1%;
  /* 使用margin替代gap */
  padding: 20rpx;
  /* 调整内边距 */
  background-color: #FFFFFF;
  border-radius: 20rpx;
  /* 增大圆角从12rpx到20rpx */
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  box-sizing: border-box;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  /* 减轻阴影 */
  position: relative;
  overflow: hidden;
  border: 1rpx solid #EEEEEE;
  /* 普通浅灰色边框 */
  flex-shrink: 0;
  flex-grow: 0;
  /* 简化光效 */
}
.grid-item::after {
  display: none;
  /* 移除光效 */
}
.grid-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
  z-index: 1;
  border-radius: 32rpx;
}
.grid-item::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.25), transparent 70%);
  z-index: 1;
  border-radius: 32rpx;
}
.grid-item:active {
  transform: translateY(-3rpx) scale(0.98);
  box-shadow: 0 5rpx 12rpx rgba(0, 0, 0, 0.08);
}
.grid-content {
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
  padding: 16rpx;
}
.grid-title {
  font-size: 34rpx;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 8rpx;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 3;
  letter-spacing: 1rpx;
}
.grid-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 3;
}
.people-to-car {
  background: linear-gradient(135deg, #4F6EF7, #1E40AF);
  box-shadow: 0 10rpx 25rpx rgba(79, 110, 247, 0.25), inset 0 1px 1px rgba(255, 255, 255, 0.1);
}
.car-to-people {
  background: linear-gradient(135deg, #F43F5E, #BE123C);
  box-shadow: 0 10rpx 25rpx rgba(244, 63, 94, 0.25), inset 0 1px 1px rgba(255, 255, 255, 0.1);
}
.goods-to-car {
  background: linear-gradient(135deg, #10B981, #047857);
  box-shadow: 0 10rpx 25rpx rgba(16, 185, 129, 0.25), inset 0 1px 1px rgba(255, 255, 255, 0.1);
}
.car-to-goods {
  background: linear-gradient(135deg, #F59E0B, #B45309);
  box-shadow: 0 10rpx 25rpx rgba(245, 158, 11, 0.25), inset 0 1px 1px rgba(255, 255, 255, 0.1);
}

/* 热门路线 */
.section.hot-routes {
  margin: 16rpx 32rpx;
  /* 减少上下边距 */
  padding: 24rpx;
  /* 减少内边距 */
  background-color: #FFFFFF;
  border-radius: 24rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 1;
  border: 1rpx solid rgba(0, 0, 0, 0.02);
  overflow: hidden;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  position: relative;
  padding-bottom: 0;
  /* 移除底部内边距 */
}

/* 移除下边框 */
.section-header::after {
  display: none;
}
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  position: relative;
  padding-left: 0;
  /* 移除左侧内边距 */
}

/* 移除左侧蓝色装饰条 */
.section-title::before {
  display: none;
}
.more-link {
  font-size: 26rpx;
  color: #0A84FF;
  font-weight: 500;
  padding: 6rpx 12rpx;
  /* 增加点击区域 */
  border-radius: 12rpx;
  /* 圆角边框 */
  transition: all 0.2s ease;
  /* 微妙的悬停效果 */
}
.more-link:active {
  background-color: rgba(10, 132, 255, 0.08);
  transform: scale(0.95);
}
.route-list {
  display: flex;
  flex-flow: row wrap;
  width: 100%;
  box-sizing: border-box;
  padding: 4rpx;
  justify-content: space-between;
}
.route-item {
  width: 48%;
  margin-bottom: 16rpx;
  padding: 20rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  box-sizing: border-box;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  border: 1rpx solid #EEEEEE;
  flex: 0 0 auto;
  /* 简化光效 */
}
.route-item::after {
  display: none;
  /* 移除光效 */
}

/* iOS设备特殊处理 */
@supports (-webkit-touch-callout: none) {
.route-list {
    display: flex;
    flex-flow: row wrap;
    width: 100%;
    box-sizing: border-box;
    padding: 4rpx;
}
.route-item {
    width: 48%;
    margin: 1%;
    flex: 0 0 auto;
    box-sizing: border-box;
}
}
.route-item:active {
  transform: translateY(1rpx);
  /* 减轻按下效果 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  background-color: #FAFAFA;
  /* 更淡的背景色 */
}
.route-path {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 2;
}
.start-point, .end-point {
  font-size: 28rpx;
  color: #333333;
  /* 全部使用黑色 */
  font-weight: 500;
  max-width: 42%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 移除单独的颜色设置 */
.start-point {
  /* 移除蓝色 */
}
.end-point {
  /* 保持黑色 */
}

/* 简化箭头设计 */
.route-arrow-container {
  position: relative;
  width: 30rpx;
  /* 减小宽度从50rpx到30rpx */
  height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.route-arrow-container::before {
  content: "";
  position: absolute;
  left: 0;
  right: 10rpx;
  top: 50%;
  height: 1rpx;
  /* 保持细线 */
  background-color: #CCCCCC;
  /* 稍微亮一点的灰色 */
  transform: translateY(-50%);
}
.route-arrow-container::after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  width: 8rpx;
  height: 8rpx;
  border: 1rpx solid #CCCCCC;
  /* 与线条颜色匹配 */
  border-left: none;
  border-bottom: none;
  transform: translateY(-50%) rotate(45deg);
}

/* 最新拼车信息 */
.section.latest-carpools {
  margin: 16rpx 32rpx;
  /* 减少上下边距 */
  padding: 24rpx;
  /* 减少内边距 */
  background-color: #FFFFFF;
  border-radius: 24rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 1;
  border: 1rpx solid rgba(0, 0, 0, 0.02);
  overflow: hidden;
  margin-bottom: 50rpx;
  /* 减少底部边距 */
}
.sort-dropdown {
  position: relative;
}
.sort-label {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666666;
  padding: 8rpx 16rpx;
  background-color: #F2F7FD;
  border-radius: 16rpx;
}
.sort-icon {
  font-size: 20rpx;
  margin-left: 6rpx;
  color: #0A84FF;
}
.sort-options {
  position: absolute;
  right: 0;
  top: 60rpx;
  width: 200rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  z-index: 10;
  overflow: hidden;
}
.sort-option {
  padding: 20rpx 24rpx;
  font-size: 26rpx;
  color: #333333;
  transition: all 0.2s ease;
}
.sort-option:active {
  background-color: #F2F7FD;
}
.carpool-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  padding: 4rpx;
  /* 添加内边距 */
}
.carpool-card {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  /* 增强阴影 */
  transition: all 0.3s ease;
  position: relative;
  border-left: 6rpx solid #0A84FF;
  box-sizing: border-box;
  /* 添加顶部渐变边框 */
  /* 添加微妙的内部光效 */
}
.carpool-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 6rpx;
  /* 留出左侧边框 */
  right: 0;
  height: 4rpx;
  background: linear-gradient(to right, transparent, rgba(10, 132, 255, 0.3), transparent);
  z-index: 2;
}
.carpool-card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 6rpx;
  /* 留出左侧边框 */
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent 60%);
  pointer-events: none;
}
.carpool-card.people-to-car {
  border-left-color: #0A84FF;
}
.carpool-card.car-to-people {
  border-left-color: #FF2D55;
}
.carpool-card.goods-to-car {
  border-left-color: #30D158;
}
.carpool-card.car-to-goods {
  border-left-color: #FF9F0A;
}
.carpool-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
}
.card-header {
  padding: 24rpx;
  position: relative;
  background-color: #FFFFFF;
}
.card-type-indicator {
  position: absolute;
  top: 0;
  left: 0;
  width: 6rpx;
  height: 100%;
  background-color: inherit;
}
.route-direction {
  margin-left: 12rpx;
}
.route-info-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}
.route-info-row:last-child {
  margin-bottom: 0;
}
.route-label {
  width: 80rpx;
  font-size: 26rpx;
  color: #999999;
}
.route-value {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}
.via-points {
  color: #666666;
}
.via-icon {
  margin-right: 8rpx;
  color: #0A84FF;
}
.route-value-wrapper {
  display: flex;
  align-items: center;
}
.card-type-tag {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  color: #FFFFFF;
}
.people-to-car-tag {
  background-color: #0A84FF;
}
.car-to-people-tag {
  background-color: #FF2D55;
}
.goods-to-car-tag {
  background-color: #30D158;
}
.car-to-goods-tag {
  background-color: #FF9F0A;
}
.publish-mode-tag {
  position: absolute;
  top: 70rpx;
  right: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  color: #FFFFFF;
}
.premium {
  background-color: #FF2D55;
}
.ad {
  background-color: #FF9F0A;
}
.card-footer {
  padding: 24rpx;
  border-top: 1px solid #F2F2F7;
  display: flex;
  flex-direction: column;
  background-color: #FFFFFF;
}
.trip-info {
  display: flex;
  gap: 24rpx;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}
.user-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.user-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.card-actions {
  display: flex;
  gap: 16rpx;
}
.info-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.info-icon {
  width: 32rpx;
  height: 32rpx;
}
.info-value {
  font-size: 26rpx;
  color: #666666;
}
.user-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  border: 2rpx solid #FFFFFF;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}
.user-name-wrapper {
  display: flex;
  align-items: center;
  gap: 6rpx;
}
.user-name {
  font-size: 26rpx;
  color: #333333;
  max-width: 120rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 认证标签样式 */
.verified-badge {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, rgba(10, 132, 255, 0.15), rgba(10, 132, 255, 0.05));
  padding: 4rpx 10rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(10, 132, 255, 0.3);
  box-shadow: 0 2rpx 6rpx rgba(10, 132, 255, 0.1);
}
.verified-icon-circle {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: #0A84FF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 6rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.15);
}
.verified-icon-circle svg {
  color: white;
}
.verified-text {
  font-size: 20rpx;
  color: #0A84FF;
  font-weight: 500;
}
.action-button {
  width: 88rpx;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.action-button.phone {
  background-color: #34C759;
  box-shadow: 0 4rpx 12rpx rgba(52, 199, 89, 0.3);
}
.action-button.chat {
  background-color: #0A84FF;
  box-shadow: 0 4rpx 12rpx rgba(10, 132, 255, 0.3);
}
.action-button image {
  width: 48rpx;
  height: 48rpx;
  filter: brightness(0) invert(1);
}

/* 发布类型选择弹窗 */
.publish-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}
.publish-card {
  width: 80%;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);
  animation: card-pop 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
  transform: scale(0.95);
  opacity: 0.8;
}
@keyframes card-pop {
0% {
    transform: scale(0.95);
    opacity: 0.8;
}
100% {
    transform: scale(1);
    opacity: 1;
}
}
.publish-header {
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #F2F2F7;
}
.publish-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.close-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background-color: #F2F2F7;
  display: flex;
  align-items: center;
  justify-content: center;
}
.close-icon {
  font-size: 32rpx;
  color: #999999;
}
.publish-options {
  padding: 32rpx;
  display: flex;
  flex-direction: column;
}
.publish-option-row {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 32rpx;
}
.publish-option-row:last-child {
  margin-bottom: 0;
}
.publish-option {
  width: 48%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 0;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}
.publish-option:active {
  transform: scale(0.95);
  background-color: #F5F8FC;
}
.option-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}
.option-icon-wrapper {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.12);
  border: 2rpx solid rgba(255, 255, 255, 0.7);
  position: relative;
  overflow: hidden;
}
.option-icon-wrapper::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.3), transparent 70%);
  z-index: 1;
}
.option-icon {
  width: 60rpx;
  height: 60rpx;
  filter: brightness(0) invert(1);
  position: relative;
  z-index: 2;
}
.people-car {
  background: linear-gradient(135deg, #0A84FF, #5AC8FA);
}
.car-people {
  background: linear-gradient(135deg, #FF2D55, #FF9500);
}
.goods-car {
  background: linear-gradient(135deg, #30D158, #34C759);
}
.car-goods {
  background: linear-gradient(135deg, #FF9F0A, #FFD60A);
}

/* 底部导航栏 */
.tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 110rpx;
  background-color: rgba(255, 255, 255, 0.95);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}
.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10rpx 0;
  position: relative;
  transition: all 0.2s ease;
}
.tabbar-item:active {
  opacity: 0.7;
}
.tabbar-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 6rpx;
}
.tabbar-text {
  font-size: 22rpx;
  color: #999999;
  line-height: 1;
}
.active-text {
  color: #0A84FF;
  font-weight: 500;
}
.tabbar-item.active .tabbar-icon {
  transform: scale(1.1);
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  border-radius: 50%;
  z-index: 0;
  opacity: 0.4;
}
.bg-circle-1 {
  top: 260rpx;
  /* 调整位置 */
  left: -150rpx;
  width: 300rpx;
  height: 300rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent 70%);
}
.bg-circle-2 {
  top: 80rpx;
  /* 调整位置 */
  right: -100rpx;
  width: 200rpx;
  height: 200rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.25), transparent 70%);
}
.bg-circle-3 {
  bottom: 220rpx;
  left: -120rpx;
  width: 240rpx;
  height: 240rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2), transparent 70%);
}

/* 过渡效果 */
.bg-loaded {
  animation: fadeIn 0.3s ease forwards;
}
.navbar-loaded {
  animation: slideDown 0.3s ease forwards;
}
@keyframes fadeIn {
from {
    opacity: 0.8;
}
to {
    opacity: 1;
}
}
@keyframes slideDown {
from {
    transform: translateY(-5px);
    opacity: 0.8;
}
to {
    transform: translateY(0);
    opacity: 1;
}
}
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  width: 100%;
}

/* 圆弧背景模块 */
.arc-background {
  position: fixed;
  top: calc(var(--status-bar-height) + 90rpx);
  /* 从标题栏底部开始 */
  left: 0;
  right: 0;
  height: 240rpx;
  /* 增加高度以覆盖更多内容区域 */
  background-color: #1677FF;
  border-bottom-left-radius: 32rpx;
  border-bottom-right-radius: 32rpx;
  z-index: 1;
}

/* 移除原来的伪元素背景 */
.carpool-container::before {
  content: none;
}

/* 内容区域布局调整 */
.swiper-section, .search-section, .grid-section, .section {
  position: relative;
  z-index: 10;
  /* 确保内容显示在圆弧背景之上 */
}