{"version": 3, "file": "wallet.js", "sources": ["mock/payment/wallet.js"], "sourcesContent": ["// 钱包模拟数据\r\nexport const walletInfo = {\r\n  balance: 1280.50,\r\n  points: 2560,\r\n  coupons: 8,\r\n  redPackets: 3,\r\n  bankCards: [\r\n    {\r\n      id: 'card-001',\r\n      bank: '中国建设银行',\r\n      type: '储蓄卡',\r\n      number: '6217 **** **** 3456',\r\n      isDefault: true\r\n    },\r\n    {\r\n      id: 'card-002',\r\n      bank: '中国工商银行',\r\n      type: '信用卡',\r\n      number: '6222 **** **** 7890',\r\n      isDefault: false\r\n    }\r\n  ],\r\n  securityLevel: 'high', // low, medium, high\r\n  hasPinCode: true,\r\n  hasFingerprint: true,\r\n  hasFaceID: false\r\n};\r\n\r\n// 获取钱包信息的API函数\r\nexport const fetchWalletInfo = () => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve(walletInfo);\r\n    }, 300);\r\n  });\r\n};\r\n\r\n// 充值的API函数\r\nexport const rechargeWallet = (amount, paymentMethod) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      // 模拟充值成功\r\n      const updatedWallet = {\r\n        ...walletInfo,\r\n        balance: walletInfo.balance + amount\r\n      };\r\n      \r\n      resolve({\r\n        success: true,\r\n        message: '充值成功',\r\n        data: updatedWallet\r\n      });\r\n    }, 800);\r\n  });\r\n};\r\n\r\n// 提现的API函数\r\nexport const withdrawWallet = (amount, bankCardId) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      if (amount <= walletInfo.balance) {\r\n        // 模拟提现成功\r\n        const updatedWallet = {\r\n          ...walletInfo,\r\n          balance: walletInfo.balance - amount\r\n        };\r\n        \r\n        resolve({\r\n          success: true,\r\n          message: '提现申请已提交，预计1-3个工作日到账',\r\n          data: updatedWallet\r\n        });\r\n      } else {\r\n        // 模拟提现失败\r\n        resolve({\r\n          success: false,\r\n          message: '余额不足',\r\n          data: walletInfo\r\n        });\r\n      }\r\n    }, 800);\r\n  });\r\n};\r\n\r\n// 添加银行卡的API函数\r\nexport const addBankCard = (cardInfo) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      const newCard = {\r\n        id: 'card-' + Date.now(),\r\n        ...cardInfo,\r\n        isDefault: false\r\n      };\r\n      \r\n      const updatedWallet = {\r\n        ...walletInfo,\r\n        bankCards: [...walletInfo.bankCards, newCard]\r\n      };\r\n      \r\n      resolve({\r\n        success: true,\r\n        message: '银行卡添加成功',\r\n        data: updatedWallet\r\n      });\r\n    }, 800);\r\n  });\r\n};\r\n\r\n// 设置默认银行卡的API函数\r\nexport const setDefaultBankCard = (cardId) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      const updatedCards = walletInfo.bankCards.map(card => ({\r\n        ...card,\r\n        isDefault: card.id === cardId\r\n      }));\r\n      \r\n      const updatedWallet = {\r\n        ...walletInfo,\r\n        bankCards: updatedCards\r\n      };\r\n      \r\n      resolve({\r\n        success: true,\r\n        message: '已设置为默认银行卡',\r\n        data: updatedWallet\r\n      });\r\n    }, 500);\r\n  });\r\n}; "], "names": [], "mappings": ";AACO,MAAM,aAAa;AAAA,EACxB,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,IACT;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,WAAW;AAAA,IACZ;AAAA,IACD;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,WAAW;AAAA,IACZ;AAAA,EACF;AAAA,EACD,eAAe;AAAA;AAAA,EACf,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,WAAW;AACb;AAGY,MAAC,kBAAkB,MAAM;AACnC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,cAAQ,UAAU;AAAA,IACnB,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,iBAAiB,CAAC,QAAQ,kBAAkB;AACvD,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AAEf,YAAM,gBAAgB;AAAA,QACpB,GAAG;AAAA,QACH,SAAS,WAAW,UAAU;AAAA,MACtC;AAEM,cAAQ;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,QACT,MAAM;AAAA,MACd,CAAO;AAAA,IACF,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,iBAAiB,CAAC,QAAQ,eAAe;AACpD,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,UAAI,UAAU,WAAW,SAAS;AAEhC,cAAM,gBAAgB;AAAA,UACpB,GAAG;AAAA,UACH,SAAS,WAAW,UAAU;AAAA,QACxC;AAEQ,gBAAQ;AAAA,UACN,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,QAChB,CAAS;AAAA,MACT,OAAa;AAEL,gBAAQ;AAAA,UACN,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,QAChB,CAAS;AAAA,MACF;AAAA,IACF,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,cAAc,CAAC,aAAa;AACvC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,YAAM,UAAU;AAAA,QACd,IAAI,UAAU,KAAK,IAAK;AAAA,QACxB,GAAG;AAAA,QACH,WAAW;AAAA,MACnB;AAEM,YAAM,gBAAgB;AAAA,QACpB,GAAG;AAAA,QACH,WAAW,CAAC,GAAG,WAAW,WAAW,OAAO;AAAA,MACpD;AAEM,cAAQ;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,QACT,MAAM;AAAA,MACd,CAAO;AAAA,IACF,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,qBAAqB,CAAC,WAAW;AAC5C,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,YAAM,eAAe,WAAW,UAAU,IAAI,WAAS;AAAA,QACrD,GAAG;AAAA,QACH,WAAW,KAAK,OAAO;AAAA,MACxB,EAAC;AAEF,YAAM,gBAAgB;AAAA,QACpB,GAAG;AAAA,QACH,WAAW;AAAA,MACnB;AAEM,cAAQ;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,QACT,MAAM;AAAA,MACd,CAAO;AAAA,IACF,GAAE,GAAG;AAAA,EACV,CAAG;AACH;;;;;;"}