/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-3b3a55a0, html.data-v-3b3a55a0, #app.data-v-3b3a55a0, .index-container.data-v-3b3a55a0 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.page-container.data-v-3b3a55a0 {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏样式 */
.navbar.data-v-3b3a55a0 {
  display: flex;
  align-items: center;
  height: 44px;
  background-color: #fff;
  padding: 0 15px;
  position: relative;
}
.navbar-back.data-v-3b3a55a0 {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon.data-v-3b3a55a0 {
  width: 12px;
  height: 12px;
  border-top: 2px solid #333;
  border-left: 2px solid #333;
  transform: rotate(-45deg);
}
.navbar-title.data-v-3b3a55a0 {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 600;
  color: #333;
}
.navbar-right.data-v-3b3a55a0 {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.share-icon.data-v-3b3a55a0 {
  color: #333;
}

/* 内容滚动区 */
.content-scroll.data-v-3b3a55a0 {
  flex: 1;
}

/* 页面头部 */
.page-header.data-v-3b3a55a0 {
  height: 180px;
  position: relative;
  overflow: hidden;
}
.header-bg.data-v-3b3a55a0 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20px;
}
.header-title.data-v-3b3a55a0 {
  font-size: 24px;
  font-weight: 700;
  color: #fff;
  margin-bottom: 10px;
}
.header-subtitle.data-v-3b3a55a0 {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
}

/* 内容部分 */
.content-section.data-v-3b3a55a0 {
  padding: 20px 15px;
  background-color: #fff;
  border-radius: 15px 15px 0 0;
  margin-top: -20px;
  position: relative;
  z-index: 1;
}
.section-title.data-v-3b3a55a0 {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  margin-top: 25px;
}
.section-title.data-v-3b3a55a0:first-child {
  margin-top: 0;
}
.title-icon.data-v-3b3a55a0 {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}
.title-text.data-v-3b3a55a0 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
.content-text.data-v-3b3a55a0 {
  font-size: 15px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}
.content-image.data-v-3b3a55a0 {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}
.content-image image.data-v-3b3a55a0 {
  width: 100%;
}

/* 数据卡片 */
.data-cards.data-v-3b3a55a0 {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px 20px;
}
.data-card.data-v-3b3a55a0 {
  width: calc(50% - 10px);
  margin: 5px;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
}
.data-value.data-v-3b3a55a0 {
  font-size: 20px;
  font-weight: 700;
  color: #6EE7B7;
  display: block;
  margin-bottom: 5px;
}
.data-label.data-v-3b3a55a0 {
  font-size: 13px;
  color: #666;
}

/* 场景列表 */
.scenario-list.data-v-3b3a55a0 {
  margin-bottom: 20px;
}
.scenario-item.data-v-3b3a55a0 {
  display: flex;
  margin-bottom: 15px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
}
.scenario-icon.data-v-3b3a55a0 {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}
.scenario-content.data-v-3b3a55a0 {
  flex: 1;
}
.scenario-title.data-v-3b3a55a0 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  display: block;
}
.scenario-desc.data-v-3b3a55a0 {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* 最佳实践 */
.practice-list.data-v-3b3a55a0 {
  margin-bottom: 20px;
}
.practice-item.data-v-3b3a55a0 {
  display: flex;
  margin-bottom: 15px;
}
.practice-number.data-v-3b3a55a0 {
  font-size: 18px;
  font-weight: 700;
  color: #6EE7B7;
  margin-right: 15px;
}
.practice-content.data-v-3b3a55a0 {
  flex: 1;
}
.practice-title.data-v-3b3a55a0 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  display: block;
}
.practice-desc.data-v-3b3a55a0 {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* 案例分析 */
.case-study.data-v-3b3a55a0 {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}
.case-header.data-v-3b3a55a0 {
  margin-bottom: 10px;
}
.case-title.data-v-3b3a55a0 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.case-content.data-v-3b3a55a0 {
  margin-bottom: 15px;
}
.case-desc.data-v-3b3a55a0 {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}
.case-results.data-v-3b3a55a0 {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #eee;
  padding-top: 15px;
}
.result-item.data-v-3b3a55a0 {
  text-align: center;
  flex: 1;
}
.result-label.data-v-3b3a55a0 {
  font-size: 12px;
  color: #999;
  display: block;
  margin-bottom: 5px;
}
.result-value.data-v-3b3a55a0 {
  font-size: 18px;
  font-weight: 600;
  color: #6EE7B7;
}

/* 联系我们 */
.contact-section.data-v-3b3a55a0 {
  margin: 0 15px 30px;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.contact-title.data-v-3b3a55a0 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 10px;
}
.contact-desc.data-v-3b3a55a0 {
  font-size: 14px;
  color: #666;
  display: block;
  margin-bottom: 15px;
}
.contact-btn.data-v-3b3a55a0 {
  background-color: #6EE7B7;
  color: #fff;
  border: none;
  border-radius: 20px;
  padding: 8px 20px;
  font-size: 14px;
}