{"name": "find-up", "version": "4.1.0", "description": "Find a file or directory by walking up parent directories", "license": "MIT", "repository": "sindresorhus/find-up", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "walk", "walking", "path"], "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "devDependencies": {"ava": "^2.1.0", "is-path-inside": "^2.1.0", "tempy": "^0.3.0", "tsd": "^0.7.3", "xo": "^0.24.0"}}