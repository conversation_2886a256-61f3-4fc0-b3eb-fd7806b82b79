/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.package-detail-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: calc(80px + env(safe-area-inset-bottom));
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.more-icon {
  font-size: 20px;
  font-weight: bold;
  transform: rotate(90deg);
}
.page-content {
  height: calc(100vh - 59px - 80px - env(safe-area-inset-bottom));
}

/* 套餐头部信息 */
.package-header {
  margin: 15px;
  padding: 15px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.package-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.package-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
.package-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}
.package-status.active {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}
.package-status.inactive {
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}
.package-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 15px;
}
.package-meta {
  border-top: 1px dashed #E5E7EB;
  padding-top: 15px;
}
.meta-item {
  display: flex;
  margin-bottom: 5px;
}
.meta-item:last-child {
  margin-bottom: 0;
}
.meta-label {
  font-size: 12px;
  color: #999;
  width: 80px;
}
.meta-value {
  font-size: 12px;
  color: #666;
}

/* 价格卡片 */
.price-card {
  margin: 15px;
  padding: 15px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-wrap: wrap;
}
.price-row {
  width: 50%;
  margin-bottom: 15px;
}
.price-row:nth-last-child(-n+2) {
  margin-bottom: 0;
}
.price-label {
  font-size: 12px;
  color: #999;
  display: block;
  margin-bottom: 5px;
}
.price-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.price-value.original {
  text-decoration: line-through;
  color: #999;
}
.price-value.group {
  color: #FF3B30;
}
.price-value.save {
  color: #FF9500;
}
.price-value.discount {
  color: #34C759;
}

/* 商品列表卡片 */
.items-card {
  margin: 15px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}
.card-header {
  padding: 15px;
  border-bottom: 1px solid #F5F7FA;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.items-list {
  padding: 0 15px;
}
.item-row {
  display: flex;
  padding: 15px 0;
  border-bottom: 1px solid #F5F7FA;
}
.item-row:last-child {
  border-bottom: none;
}
.item-number {
  width: 24px;
  height: 24px;
  background: #F0F2F5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: #666;
  margin-right: 10px;
}
.item-info {
  flex: 1;
}
.item-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
}
.item-meta {
  display: flex;
  justify-content: space-between;
}
.item-price {
  font-size: 12px;
  color: #FF3B30;
}
.item-quantity {
  font-size: 12px;
  color: #999;
}
.items-summary {
  padding: 15px;
  background: #F8FAFC;
  border-top: 1px solid #F5F7FA;
}
.summary-text {
  font-size: 12px;
  color: #666;
  text-align: center;
}

/* 销售数据卡片 */
.sales-card {
  margin: 15px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}
.date-picker {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
  font-size: 12px;
  color: #666;
}
.date-icon {
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 5px solid #666;
  margin-left: 5px;
}
.sales-stats {
  display: flex;
  padding: 15px;
  border-bottom: 1px solid #F5F7FA;
}
.stat-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}
.stat-label {
  font-size: 12px;
  color: #999;
}
.sales-chart {
  padding: 15px;
}
.chart-header {
  margin-bottom: 15px;
}
.chart-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}
.chart-container {
  height: 200px;
}
.chart-placeholder {
  width: 100%;
  height: 100%;
  background: #F8FAFC;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.placeholder-text {
  font-size: 14px;
  color: #999;
}

/* 拼团记录卡片 */
.groups-card {
  margin: 15px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 30px;
}
.view-all {
  font-size: 14px;
  color: #9040FF;
}
.groups-list {
  padding: 0 15px;
}
.group-item {
  padding: 15px 0;
  border-bottom: 1px solid #F5F7FA;
}
.group-item:last-child {
  border-bottom: none;
}
.group-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.group-leader {
  display: flex;
  align-items: center;
}
.leader-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 8px;
}
.leader-name {
  font-size: 14px;
  color: #333;
}
.group-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}
.group-status.active {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}
.group-status.success {
  background: rgba(0, 122, 255, 0.1);
  color: #007AFF;
}
.group-status.failed {
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}
.group-progress {
  margin-bottom: 10px;
}
.progress-text {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}
.progress-bar {
  height: 4px;
  background-color: #EBEDF5;
  border-radius: 2px;
  overflow: hidden;
}
.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #9040FF, #5E35B1);
  border-radius: 2px;
}
.group-time {
  font-size: 12px;
  color: #999;
}
.time-value {
  color: #666;
}

/* 底部操作栏 */
.footer-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 80px;
  background: #FFFFFF;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 10;
}
.action-button {
  flex: 1;
  height: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.button-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
}
.edit-icon {
  background: rgba(0, 122, 255, 0.1);
  position: relative;
}
.edit-icon:before {
  content: "";
  width: 12px;
  height: 12px;
  border: 1px solid #007AFF;
  border-radius: 2px;
  transform: rotate(45deg);
}
.share-icon {
  background: rgba(52, 199, 89, 0.1);
  position: relative;
}
.share-icon:before {
  content: "";
  width: 12px;
  height: 6px;
  border-left: 1px solid #34C759;
  border-right: 1px solid #34C759;
  border-bottom: 1px solid #34C759;
}
.button-text {
  font-size: 12px;
  color: #666;
}