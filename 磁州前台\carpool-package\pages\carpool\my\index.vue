<template>
  <view class="carpool-my-container">
    <!-- 自定义导航栏 -->
    <view class="custom-header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="left-action" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="action-icon back-icon"></image>
        </view>
        <view class="title-area">
          <text class="page-title">我的拼车</text>
        </view>
        <view class="right-action">
          <image src="/static/images/tabbar/setting.png" class="action-icon" @click="goSettings"></image>
        </view>
      </view>
    </view>
    
    <!-- 用户信息卡片 - 简化版 -->
    <view class="user-card">
      <view class="user-avatar-container">
        <image class="user-avatar" :src="userInfo.avatar" mode="aspectFill"></image>
      </view>
      <view class="user-name">{{userInfo.nickname}}</view>
      <view class="user-id">ID: {{userInfo.id}}</view>
      <view class="user-credit">信用分: <text class="credit-score">{{userInfo.creditScore}}</text></view>
      
      <view class="user-actions">
        <view class="action-item" @click="goVerification">
          <text class="action-text">{{userInfo.isVerified ? '已认证' : '去认证'}}</text>
        </view>
        <view class="action-divider"></view>
        <view class="action-item" @click="goVIP">
          <text class="action-text">{{userInfo.isVIP ? 'VIP会员' : '开通VIP'}}</text>
        </view>
      </view>
    </view>
    
    <!-- 测试按钮 -->
    <view class="test-button" @click="goToSuccessPage">
      <text class="test-button-text">测试发布成功页面</text>
    </view>
    
    <!-- 统计数据 -->
    <view class="stats-card">
      <view class="stat-item">
        <text class="stat-value">{{stats.publishCount}}</text>
        <text class="stat-label">已发布</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{stats.completedCount}}</text>
        <text class="stat-label">已完成</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{stats.favorCount}}</text>
        <text class="stat-label">收藏</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{stats.tripDistance}}km</text>
        <text class="stat-label">总里程</text>
      </view>
    </view>
    
    <!-- 功能列表 -->
    <view class="feature-section">
      <view class="section-header">
        <text class="section-title">我的发布</text>
      </view>
      
      <view class="feature-grid">
        <view class="feature-item" @click="navigateToList('published')">
          <view class="feature-icon-wrapper blue">
            <image src="/static/images/tabbar/publish-list.png" mode="aspectFit" class="feature-icon"></image>
          </view>
          <text class="feature-text">发布列表</text>
        </view>
        
        <view class="feature-item" @click="goToTripRecords">
          <view class="feature-icon-wrapper purple">
            <image src="/static/images/tabbar/history.png" mode="aspectFit" class="feature-icon"></image>
          </view>
          <text class="feature-text">行程记录</text>
        </view>
        
        <view class="feature-item" @click="goToDriverRatings">
          <view class="feature-icon-wrapper orange">
            <image src="/static/images/tabbar/star-rating.png" mode="aspectFit" class="feature-icon"></image>
          </view>
          <text class="feature-text">我的评价</text>
          <view class="badge" v-if="stats.newRatingCount > 0">{{stats.newRatingCount}}</view>
        </view>
        
        <view class="feature-item" @click="goToDriverProfile">
          <view class="feature-icon-wrapper green">
            <image src="/static/images/tabbar/driver.png" mode="aspectFit" class="feature-icon"></image>
          </view>
          <text class="feature-text">个人中心</text>
        </view>
      </view>
    </view>
    
    <view class="feature-section">
      <view class="section-header">
        <text class="section-title">更多服务</text>
      </view>
      
      <view class="feature-list">
        <view class="list-item" @click="navigateToList('favorites')">
          <view class="item-left">
            <view class="item-icon-wrapper purple">
              <image src="/static/images/tabbar/star.png" mode="aspectFit" class="item-icon"></image>
            </view>
            <text class="item-text">我的收藏</text>
          </view>
          <view class="item-right">
            <image src="/static/images/tabbar/arrow-right.png" mode="aspectFit" class="arrow-icon"></image>
          </view>
        </view>
        
        <view class="list-item" @click="navigateToPage('contact-history')">
          <view class="item-left">
            <view class="item-icon-wrapper green">
              <image src="/static/images/tabbar/history.png" mode="aspectFit" class="item-icon"></image>
            </view>
            <text class="item-text">联系历史</text>
          </view>
          <view class="item-right">
            <view class="notification-badge" v-if="stats.contactHistoryCount > 0">{{stats.contactHistoryCount}}</view>
            <image src="/static/images/tabbar/arrow-right.png" mode="aspectFit" class="arrow-icon"></image>
          </view>
        </view>
        
        <view class="list-item" @click="navigateToPage('message')">
          <view class="item-left">
            <view class="item-icon-wrapper blue">
              <image src="/static/images/tabbar/message.png" mode="aspectFit" class="item-icon"></image>
            </view>
            <text class="item-text">消息中心</text>
          </view>
          <view class="item-right">
            <view class="notification-badge" v-if="stats.unreadMessageCount > 0">{{stats.unreadMessageCount}}</view>
            <image src="/static/images/tabbar/arrow-right.png" mode="aspectFit" class="arrow-icon"></image>
          </view>
        </view>
        
        <view class="list-item" @click="navigateToPage('wallet')">
          <view class="item-left">
            <view class="item-icon-wrapper orange">
              <image src="/static/images/tabbar/wallet.png" mode="aspectFit" class="item-icon"></image>
            </view>
            <text class="item-text">我的钱包</text>
          </view>
          <view class="item-right">
            <image src="/static/images/tabbar/arrow-right.png" mode="aspectFit" class="arrow-icon"></image>
          </view>
        </view>
        
        <view class="list-item" @click="navigateToPage('driver-verification')">
          <view class="item-left">
            <view class="item-icon-wrapper blue">
              <image src="/static/images/tabbar/verify.png" mode="aspectFit" class="item-icon"></image>
            </view>
            <text class="item-text">司机认证</text>
          </view>
          <view class="item-right">
            <view class="verification-badge" v-if="userInfo.isDriverVerified">已认证</view>
            <image src="/static/images/tabbar/arrow-right.png" mode="aspectFit" class="arrow-icon"></image>
          </view>
        </view>
        
        <view class="list-item" @click="navigateToPage('feedback')">
          <view class="item-left">
            <view class="item-icon-wrapper green">
              <image src="/static/images/tabbar/feedback.png" mode="aspectFit" class="item-icon"></image>
            </view>
            <text class="item-text">意见反馈</text>
          </view>
          <view class="item-right">
            <image src="/static/images/tabbar/arrow-right.png" mode="aspectFit" class="arrow-icon"></image>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部导航栏 -->
    <view class="tabbar">
      <view class="tabbar-item" :class="{ active: activeTab === 'home' }" @click="navigateToPage('home')">
        <image :src="activeTab === 'home' ? '/static/images/tabbar/p首页选中.png' : '/static/images/tabbar/p首页.png'" mode="aspectFit" class="tabbar-icon"></image>
        <text class="tabbar-text" :class="{ 'active-text': activeTab === 'home' }">同城</text>
      </view>
      <view class="tabbar-item" :class="{ active: activeTab === 'carpool-main' }" @click="navigateToPage('carpool-main')">
        <image :src="activeTab === 'carpool-main' ? '/static/images/tabbar/p拼车选中.png' : '/static/images/tabbar/p拼车.png'" mode="aspectFit" class="tabbar-icon"></image>
        <text class="tabbar-text" :class="{ 'active-text': activeTab === 'carpool-main' }">拼车</text>
      </view>
      <view class="tabbar-item" :class="{ active: activeTab === 'publish' }" @click="publishNew">
        <image :src="activeTab === 'publish' ? '/static/images/tabbar/p发布选中.png' : '/static/images/tabbar/p发布.png'" mode="aspectFit" class="tabbar-icon"></image>
        <text class="tabbar-text" :class="{ 'active-text': activeTab === 'publish' }">发布</text>
      </view>
      <view class="tabbar-item" :class="{ active: activeTab === 'groups' }" @click="navigateToPage('groups')">
        <image :src="activeTab === 'groups' ? '/static/images/tabbar/p拼车群选中.png' : '/static/images/tabbar/p拼车群.png'" mode="aspectFit" class="tabbar-icon"></image>
        <text class="tabbar-text" :class="{ 'active-text': activeTab === 'groups' }">拼车群</text>
      </view>
      <view class="tabbar-item active">
        <image src="/static/images/tabbar/p我的选中.png" mode="aspectFit" class="tabbar-icon"></image>
        <text class="tabbar-text active-text">我的</text>
      </view>
    </view>
    
    <!-- 底部安全区域 -->
    <view class="safe-area-bottom"></view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { onLoad, onShow, onHide, onUnload, onBackPress } from '@dcloudio/uni-app';

// 用户信息
const userInfo = ref({
  id: '10086',
  nickname: '磁州用户',
  avatar: '/static/images/avatar/user1.png',
  creditScore: 98,
  isVerified: true,
  isVIP: false,
  isDriverVerified: true
});

// 统计数据
const stats = ref({
  publishCount: 12,
  completedCount: 8,
  favorCount: 5,
  tripDistance: 320,
  newRatingCount: 2,
  contactHistoryCount: 3,
  unreadMessageCount: 5
});

// 当前激活的tab
const activeTab = ref('my');

// 状态栏高度
const statusBarHeight = ref(20);

// 生命周期钩子
onMounted(() => {
  // 隐藏原生tabBar
  uni.hideTabBar();
  
  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight;
});

onShow(() => {
  // 确保每次页面显示时都隐藏原生tabBar
  uni.hideTabBar();
});

onLoad(() => {
  getUserInfo();
  getStatistics();
  uni.hideTabBar();
});

onHide(() => {
  // 页面隐藏时，需要根据下一个页面决定是否显示tabBar
});

onUnload(() => {
  // 如果不是返回到拼车主页，需要显示默认tabBar
  const pages = getCurrentPages();
  const prevPage = pages[pages.length - 2];
  if (!prevPage || prevPage.route !== 'carpool-package/pages/carpool-main/index') {
    uni.showTabBar();
  }
});

onBackPress((event) => {
  if (event.from === 'backbutton') {
    uni.navigateTo({
      url: '/carpool-package/pages/carpool-main/index'
    });
    return true;
  }
  return false;
});

// 获取用户信息
const getUserInfo = () => {
  // 这里应该是真实的API调用
  // 目前使用模拟数据
  console.log('获取用户信息');
};

// 获取统计数据
const getStatistics = () => {
  // 这里应该是真实的API调用
  // 目前使用模拟数据
  console.log('获取统计数据');
};

// 查看用户资料
const viewUserProfile = () => {
  uni.navigateTo({
    url: '/carpool-package/pages/carpool/my/profile'
  });
};

// 前往设置页面
const goSettings = () => {
  uni.navigateTo({
    url: '/carpool-package/pages/carpool/my/settings'
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 前往认证页面
const goVerification = () => {
  if (userInfo.value.isVerified) {
    uni.showToast({
      title: '您已通过认证',
      icon: 'success'
    });
    return;
  }
  
  uni.navigateTo({
    url: '/carpool-package/pages/carpool/my/verification'
  });
};

// 前往VIP页面
const goVIP = () => {
  uni.navigateTo({
    url: '/carpool-package/pages/carpool/my/vip'
  });
};

// 导航到列表页面
const navigateToList = (type) => {
  uni.navigateTo({
    url: `/carpool-package/pages/carpool/my/${type === 'published' ? 'published-list' : type === 'pending' ? 'pending-list' : type === 'expired' ? 'expired-list' : 'favorites'}`
  });
};

// 导航到其他页面
const navigateToPage = (page) => {
  let url = '';
  
  switch(page) {
    case 'home':
      uni.switchTab({
        url: '/pages/index/index'
      });
      return;
    case 'carpool-main':
      uni.navigateTo({
        url: '/carpool-package/pages/carpool-main/index'
      });
      return;
    case 'groups':
      uni.navigateTo({
        url: '/carpool-package/pages/carpool/groups/index'
      });
      return;
    case 'message':
      uni.navigateTo({
        url: '/carpool-package/pages/carpool/my/message-center'
      });
      return;
    case 'wallet':
      uni.navigateTo({
        url: '/subPackages/payment/pages/wallet'
      });
      return;
    case 'driver-verification':
      uni.navigateTo({
        url: '/carpool-package/pages/carpool/my/driver-verification'
      });
      return;
    case 'feedback':
      uni.navigateTo({
        url: '/carpool-package/pages/carpool/my/feedback'
      });
      return;
    case 'trip-records':
      uni.navigateTo({
        url: '/carpool-package/pages/carpool/my/trip-records'
      });
      return;
    case 'driver-ratings':
      uni.navigateTo({
        url: '/carpool-package/pages/carpool/my/driver-ratings'
      });
      return;
    case 'driver-profile':
      uni.navigateTo({
        url: '/carpool-package/pages/carpool/my/driver-profile'
      });
      return;
    case 'contact-history':
      uni.navigateTo({
        url: '/carpool-package/pages/carpool/my/contact-history'
      });
      return;
  }
  
  if (url) {
    uni.navigateTo({ url });
  }
};

// 发布新拼车信息
const publishNew = () => {
  activeTab.value = 'publish'; // 设置发布按钮为激活状态
  uni.navigateTo({
    url: '/carpool-package/pages/carpool-main/index'
  });
  // 注意：由于switchTab不支持eventChannel，可能需要其他方式触发弹窗
  // 一种可能的解决方案是使用全局状态或本地存储
  uni.setStorageSync('showPublishPopup', true);
};

// 前往司机评价页面
const goToDriverRatings = () => {
  uni.navigateTo({
    url: '/carpool-package/pages/carpool/my/driver-ratings'
  });
};

// 前往行程记录页面
const goToTripRecords = () => {
  uni.navigateTo({
    url: '/carpool-package/pages/carpool/my/trip-records'
  });
};

// 前往个人中心页面
const goToDriverProfile = () => {
  uni.navigateTo({
    url: '/carpool-package/pages/carpool/my/driver-profile'
  });
};

// 跳转到发布成功页面
const goToSuccessPage = () => {
  // 生成一个模拟的发布ID
  const publishId = Date.now().toString();
  
  // 跳转到成功页面
  uni.navigateTo({
    url: `/carpool-package/pages/carpool/publish/success?id=${publishId}&type=car-to-people&mode=ad`
  });
};
</script>

<style lang="scss">
.carpool-my-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(var(--status-bar-height) + 90rpx); /* 只考虑标题栏高度 */
  padding-bottom: calc(110rpx + env(safe-area-inset-bottom, 0));
}

/* 自定义标题栏模块 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  background-color: #1677FF; /* 恢复为实色背景 */
  z-index: 103;
  box-shadow: none;
}

/* 标题栏内容 */
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 90rpx;
  padding: 0 30rpx;
  position: relative;
  z-index: 102;
}

.left-action {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-area {
  flex: 1;
  text-align: center;
}

.page-title {
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.right-action {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  width: 44rpx;
  height: 44rpx;
  filter: brightness(0) invert(1);
}

/* 用户信息卡片 - 简化版 */
.user-card {
  margin: 60rpx 32rpx 20rpx;
  border-radius: 20rpx;
  background-color: #FFFFFF;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 24rpx;
}

.user-avatar-container {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 12rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 2rpx solid #F2F2F7;
}

.user-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50% !important;
  object-fit: cover;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 6rpx;
}

.user-id {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 6rpx;
}

.user-credit {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 20rpx;
}

.credit-score {
  color: #FF9F0A;
  font-weight: 500;
}

.user-actions {
  display: flex;
  width: 100%;
  border-top: 1px solid #F2F2F7;
  padding-top: 20rpx;
  margin-top: 4rpx;
}

.action-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.action-divider {
  width: 1px;
  height: 32rpx;
  background-color: #F2F2F7;
}

.action-text {
  font-size: 28rpx;
  color: #666666;
}

/* 统计数据卡片 */
.stats-card {
  margin: 0 32rpx 32rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 24rpx 0;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 34rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #8E8E93;
}

/* 功能区块 */
.feature-section {
  margin: 0 32rpx 32rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.section-header {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  position: relative;
  padding-left: 16rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  bottom: 6rpx;
  width: 4rpx;
  background: linear-gradient(to bottom, #0A84FF, #5AC8FA);
  border-radius: 2rpx;
}

.feature-grid {
  display: flex;
  flex-wrap: wrap;
}

.feature-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24rpx;
  position: relative;
}

.feature-icon-wrapper {
  width: 88rpx;
  height: 88rpx;
  border-radius: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 14rpx;
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.1);
}

.blue {
  background: linear-gradient(135deg, #0A84FF, #5AC8FA);
}

.orange {
  background: linear-gradient(135deg, #FF9F0A, #FF2D55);
}

.green {
  background: linear-gradient(135deg, #30D158, #34C759);
}

.gray {
  background: linear-gradient(135deg, #8E8E93, #636366);
}

.purple {
  background: linear-gradient(135deg, #BF5AF2, #A34FC9);
}

.red {
  background: linear-gradient(135deg, #FF2D55, #FF2D55);
}

.feature-icon {
  width: 44rpx;
  height: 44rpx;
  filter: brightness(0) invert(1);
}

.feature-text {
  font-size: 26rpx;
  color: #666666;
}

.badge {
  position: absolute;
  top: -8rpx;
  right: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background-color: #FF2D55;
  color: #ffffff;
  font-size: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
  box-shadow: 0 2rpx 6rpx rgba(255, 45, 85, 0.3);
}

.hot-badge {
  background-color: #FF2D55;
  color: #ffffff;
  padding: 0 8rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
}

/* 列表样式 */
.feature-list {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
}

.list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  position: relative;
}

.list-item:not(:last-child) {
  border-bottom: 1px solid #F2F2F7;
}

.item-left {
  display: flex;
  align-items: center;
}

.item-icon-wrapper {
  width: 64rpx;
  height: 64rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.item-icon {
  width: 36rpx;
  height: 36rpx;
  filter: brightness(0) invert(1);
}

.item-text {
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
}

.item-right {
  display: flex;
  align-items: center;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.5;
}

.notification-badge {
  background-color: #FF3B30;
  color: #FFFFFF;
  font-size: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
  margin-right: 10rpx;
}

.verification-badge {
  background-color: #34C759;
  color: #FFFFFF;
  font-size: 20rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12rpx;
  margin-right: 10rpx;
}

/* 底部导航栏 */
.tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 110rpx;
  background-color: rgba(255, 255, 255, 0.95);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  z-index: 9999;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10rpx 0;
  position: relative;
  transition: all 0.2s ease;
}

.tabbar-item:active {
  opacity: 0.7;
}

.tabbar-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 6rpx;
}

.tabbar-text {
  font-size: 22rpx;
  color: #999999;
  line-height: 1;
}

.active-text {
  color: #0A84FF;
  font-weight: 500;
}

.tabbar-item.active .tabbar-icon {
  transform: scale(1.1);
}

.safe-area-bottom {
  height: calc(env(safe-area-inset-bottom) + 30rpx);
  width: 100%;
}

/* 测试按钮样式 */
.test-button {
  margin: 0 32rpx 20rpx;
  background: linear-gradient(135deg, #0A84FF, #5AC8FA);
  border-radius: 45rpx;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(10, 132, 255, 0.3);
}

.test-button-text {
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 600;
}
</style> 