{"version": 3, "file": "points-rank.js", "sources": ["subPackages/checkin/pages/points-rank.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcY2hlY2tpblxwYWdlc1xwb2ludHMtcmFuay52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"rank-page\">\r\n    <!-- 顶部导航栏 -->\r\n    <view class=\"nav-bg\" :style=\"{ height: (statusBarHeight + 44) + 'px' }\"></view>\r\n    <view class=\"navbar\" :style=\"{ top: statusBarHeight + 'px', height: '44px' }\">\r\n      <view class=\"navbar-left\" @click=\"goBack\">\r\n        <image class=\"back-icon\" src=\"/static/images/tabbar/最新返回键.png\" mode=\"aspectFit\"></image>\r\n      </view>\r\n      <text class=\"navbar-title\">积分排行榜</text>\r\n      <view class=\"navbar-right\"></view>\r\n    </view>\r\n    \r\n    <!-- 内容容器 -->\r\n    <view class=\"content-container\" :style=\"{ paddingTop: (statusBarHeight + 44) + 'px' }\">\r\n      <!-- 标题与说明 -->\r\n      <view class=\"header-section\">\r\n        <view class=\"title-container\">\r\n          <text class=\"main-title\">本月积分TOP榜</text>\r\n          <view class=\"subtitle-container\">\r\n            <view class=\"subtitle-indicator\"></view>\r\n            <text class=\"subtitle\">每月1日自动结算，榜单实时更新</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 顶部三名 -->\r\n      <view class=\"top-three-section\">\r\n        <!-- 第二名 -->\r\n        <view class=\"podium-item second-place\">\r\n          <view class=\"crown silver\"></view>\r\n          <view class=\"avatar-container\">\r\n            <image class=\"avatar\" :src=\"top3[1].avatar\" mode=\"aspectFill\"></image>\r\n            <view class=\"rank-badge silver\">2</view>\r\n          </view>\r\n          <view class=\"user-info\">\r\n            <text class=\"nickname\">{{ top3[1].nickname }}</text>\r\n            <text class=\"points\">{{ formatPoints(top3[1].points) }}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 第一名 -->\r\n        <view class=\"podium-item first-place\">\r\n          <view class=\"crown gold\"></view>\r\n          <view class=\"avatar-container\">\r\n            <image class=\"avatar\" :src=\"top3[0].avatar\" mode=\"aspectFill\"></image>\r\n            <view class=\"rank-badge gold\">1</view>\r\n          </view>\r\n          <view class=\"user-info\">\r\n            <text class=\"nickname\">{{ top3[0].nickname }}</text>\r\n            <text class=\"points\">{{ formatPoints(top3[0].points) }}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 第三名 -->\r\n        <view class=\"podium-item third-place\">\r\n          <view class=\"crown bronze\"></view>\r\n          <view class=\"avatar-container\">\r\n            <image class=\"avatar\" :src=\"top3[2].avatar\" mode=\"aspectFill\"></image>\r\n            <view class=\"rank-badge bronze\">3</view>\r\n          </view>\r\n          <view class=\"user-info\">\r\n            <text class=\"nickname\">{{ top3[2].nickname }}</text>\r\n            <text class=\"points\">{{ formatPoints(top3[2].points) }}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 排行榜列表 -->\r\n      <view class=\"rank-list-container\">\r\n        <view class=\"rank-list-header\">\r\n          <text class=\"rank-header-text\">排名</text>\r\n          <text class=\"rank-header-text user-col\">用户</text>\r\n          <text class=\"rank-header-text score-col\">积分</text>\r\n        </view>\r\n        \r\n        <scroll-view scroll-y class=\"rank-list\">\r\n          <view class=\"rank-item\" v-for=\"(user, idx) in restList\" :key=\"user.id\">\r\n            <view class=\"rank-number\">{{ idx + 4 }}</view>\r\n            <view class=\"rank-user\">\r\n              <image class=\"rank-avatar\" :src=\"user.avatar\" mode=\"aspectFill\"></image>\r\n              <text class=\"rank-nickname\">{{ user.nickname }}</text>\r\n            </view>\r\n            <view class=\"rank-score\">{{ formatPoints(user.points) }}</view>\r\n          </view>\r\n          \r\n          <!-- 没有更多数据提示 -->\r\n          <view class=\"no-more\">\r\n            <view class=\"no-more-line\"></view>\r\n            <text class=\"no-more-text\">仅显示前100名</text>\r\n            <view class=\"no-more-line\"></view>\r\n          </view>\r\n        </scroll-view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue';\r\n\r\n// Vue3迁移代码开始\r\n// 响应式状态\r\nconst statusBarHeight = ref(20);\r\nconst maxRankDisplay = ref(100); // 最多显示100名用户\r\n\r\n// 排行榜数据\r\nconst rankList = ref([\r\n  { id: 1, nickname: '小明', avatar: '/static/images/avatar/avatar1.png', points: 3280 },\r\n  { id: 2, nickname: '小红', avatar: '/static/images/avatar/avatar2.png', points: 2990 },\r\n  { id: 3, nickname: '小刚', avatar: '/static/images/avatar/avatar3.png', points: 2650 },\r\n  { id: 4, nickname: '小美', avatar: '/static/images/avatar/avatar4.png', points: 2200 },\r\n  { id: 5, nickname: '小李', avatar: '/static/images/avatar/avatar5.png', points: 2100 },\r\n  { id: 6, nickname: '小王', avatar: '/static/images/avatar/avatar6.png', points: 2000 },\r\n  { id: 7, nickname: '小陈', avatar: '/static/images/avatar/avatar7.png', points: 1880 },\r\n  { id: 8, nickname: '小赵', avatar: '/static/images/avatar/avatar8.png', points: 1700 },\r\n  { id: 9, nickname: '小孙', avatar: '/static/images/avatar/avatar9.png', points: 1600 },\r\n  { id: 10, nickname: '小周', avatar: '/static/images/avatar/avatar10.png', points: 1500 }\r\n]);\r\n\r\n// 计算属性\r\nconst top3 = computed(() => {\r\n  return rankList.value.slice(0, 3);\r\n});\r\n\r\nconst restList = computed(() => {\r\n  // 只返回前100名（排除前3名后最多显示97名）\r\n  return rankList.value.slice(3, maxRankDisplay.value);\r\n});\r\n\r\n// 生命周期钩子\r\nonMounted(() => {\r\n  try {\r\n    const sysInfo = uni.getSystemInfoSync();\r\n    statusBarHeight.value = sysInfo.statusBarHeight || 20;\r\n  } catch (e) {\r\n    statusBarHeight.value = 20;\r\n  }\r\n  \r\n  // 实际应用中，这里会从服务器获取排行榜数据\r\n  fetchRankData();\r\n});\r\n\r\n// 方法\r\nfunction goBack() {\r\n  uni.navigateBack();\r\n}\r\n\r\nfunction formatPoints(points) {\r\n  return points + '分';\r\n}\r\n\r\n// 获取排行榜数据\r\nfunction fetchRankData() {\r\n  // 模拟API请求，实际项目中应替换为真实API调用\r\n  // uni.request({\r\n  //   url: 'api/points/rank',\r\n  //   success: (res) => {\r\n  //     if(res.data && res.data.list) {\r\n  //       // 限制最多显示100名\r\n  //       rankList.value = res.data.list.slice(0, maxRankDisplay.value);\r\n  //     }\r\n  //   }\r\n  // });\r\n}\r\n// Vue3迁移代码结束\r\n</script>\r\n\r\n<style lang=\"scss\">\r\npage {\r\n  background-color: #FFFFFF;\r\n  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', Arial, sans-serif;\r\n  color: #333333;\r\n}\r\n\r\n.rank-page {\r\n  min-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.nav-bg {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: #3a86ff;\r\n  z-index: 100;\r\n}\r\n\r\n.navbar {\r\n  position: fixed;\r\n  left: 0;\r\n  right: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0 16px;\r\n  z-index: 101;\r\n}\r\n\r\n.navbar-left, .navbar-right {\r\n  width: 32px;\r\n  height: 32px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 17px;\r\n  font-weight: 600;\r\n  color: #FFFFFF;\r\n}\r\n\r\n/* 内容样式 */\r\n.content-container {\r\n  flex: 1;\r\n  margin: 0 auto;\r\n  width: 100%;\r\n  max-width: 500px;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 标题部分 */\r\n.header-section {\r\n  padding: 24px 20px 20px;\r\n}\r\n\r\n.title-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.main-title {\r\n  font-size: 24px;\r\n  font-weight: 700;\r\n  color: #333;\r\n  margin-bottom: 12px;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.subtitle-container {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.subtitle-indicator {\r\n  width: 4px;\r\n  height: 12px;\r\n  background: #3a86ff;\r\n  border-radius: 2px;\r\n}\r\n\r\n.subtitle {\r\n  font-size: 13px;\r\n  color: #888;\r\n  font-weight: 400;\r\n}\r\n\r\n/* 前三名部分 */\r\n.top-three-section {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: flex-end;\r\n  padding: 10px 16px 30px;\r\n  position: relative;\r\n  max-width: 100%;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.podium-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  position: relative;\r\n  width: 33.33%;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.first-place {\r\n  z-index: 3;\r\n}\r\n\r\n.second-place {\r\n  z-index: 2;\r\n  transform: translateY(18px);\r\n  padding-right: 5px;\r\n}\r\n\r\n.third-place {\r\n  z-index: 1;\r\n  transform: translateY(36px);\r\n  padding-left: 5px;\r\n}\r\n\r\n.crown {\r\n  width: 30px;\r\n  height: 20px;\r\n  margin-bottom: -5px;\r\n  background-size: contain;\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n}\r\n\r\n.crown.gold {\r\n  background-image: linear-gradient(45deg, #FFD700, #FFC107);\r\n  clip-path: polygon(50% 0%, 75% 50%, 100% 50%, 75% 100%, 25% 100%, 0% 50%, 25% 50%);\r\n  width: 36px;\r\n  height: 25px;\r\n}\r\n\r\n.crown.silver {\r\n  background-image: linear-gradient(45deg, #C0C0C0, #A9A9A9);\r\n  clip-path: polygon(50% 20%, 70% 50%, 90% 50%, 70% 100%, 30% 100%, 10% 50%, 30% 50%);\r\n}\r\n\r\n.crown.bronze {\r\n  background-image: linear-gradient(45deg, #CD7F32, #B87333);\r\n  clip-path: polygon(50% 20%, 70% 50%, 90% 50%, 70% 100%, 30% 100%, 10% 50%, 30% 50%);\r\n}\r\n\r\n.avatar-container {\r\n  position: relative;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.avatar {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  border: 3px solid #FFFFFF;\r\n  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.first-place .avatar {\r\n  width: 80px;\r\n  height: 80px;\r\n  border: 4px solid #FFD700;\r\n}\r\n\r\n.second-place .avatar {\r\n  border: 3px solid #C0C0C0;\r\n}\r\n\r\n.third-place .avatar {\r\n  border: 3px solid #CD7F32;\r\n}\r\n\r\n.rank-badge {\r\n  position: absolute;\r\n  right: -2px;\r\n  bottom: -2px;\r\n  width: 22px;\r\n  height: 22px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 12px;\r\n  font-weight: 700;\r\n  color: white;\r\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.first-place .rank-badge {\r\n  width: 26px;\r\n  height: 26px;\r\n  font-size: 14px;\r\n}\r\n\r\n.rank-badge.gold {\r\n  background: linear-gradient(135deg, #FFD700, #FFC107);\r\n}\r\n\r\n.rank-badge.silver {\r\n  background: linear-gradient(135deg, #C0C0C0, #A9A9A9);\r\n}\r\n\r\n.rank-badge.bronze {\r\n  background: linear-gradient(135deg, #CD7F32, #B87333);\r\n}\r\n\r\n.user-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.nickname {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #333;\r\n  margin-bottom: 4px;\r\n  max-width: 100%;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  text-align: center;\r\n  box-sizing: border-box;\r\n  padding: 0 2px;\r\n}\r\n\r\n.first-place .nickname {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.points {\r\n  font-size: 15px;\r\n  font-weight: 700;\r\n  color: #3a86ff;\r\n  max-width: 100%;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  text-align: center;\r\n  box-sizing: border-box;\r\n  padding: 0 2px;\r\n}\r\n\r\n.first-place .points {\r\n  font-size: 18px;\r\n}\r\n\r\n/* 排行榜列表 */\r\n.rank-list-container {\r\n  flex: 1;\r\n  background: #FFFFFF;\r\n  border-radius: 20px 20px 0 0;\r\n  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.05);\r\n  overflow: hidden;\r\n  padding: 0;\r\n  margin-top: 16px;\r\n  width: 100%;\r\n}\r\n\r\n.rank-list-header {\r\n  display: flex;\r\n  padding: 16px 20px;\r\n  border-bottom: 1px solid #F5F5F7;\r\n  box-sizing: border-box;\r\n  width: 100%;\r\n}\r\n\r\n.rank-header-text {\r\n  font-size: 13px;\r\n  color: #999;\r\n  font-weight: 500;\r\n}\r\n\r\n.rank-header-text:first-child {\r\n  width: 40px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.rank-header-text.user-col {\r\n  flex: 1;\r\n  padding-left: 52px; /* 头像宽度40px + 间距12px */\r\n}\r\n\r\n.rank-header-text.score-col {\r\n  width: 80px;\r\n  flex-shrink: 0;\r\n  text-align: right;\r\n}\r\n\r\n.rank-list {\r\n  height: 50vh;\r\n  padding: 0 20px;\r\n  box-sizing: border-box;\r\n  padding-right: 25px; /* 增加右侧内边距，避免被滚动条遮挡 */\r\n}\r\n\r\n.rank-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 16px 0;\r\n  border-bottom: 1px solid #F5F5F7;\r\n  width: 100%;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.rank-number {\r\n  width: 40px;\r\n  font-size: 17px;\r\n  font-weight: 600;\r\n  color: #666;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.rank-user {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  flex: 1;\r\n  overflow: hidden;\r\n}\r\n\r\n.rank-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  border: 2px solid #F5F5F7;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.rank-nickname {\r\n  font-size: 15px;\r\n  color: #333;\r\n  font-weight: 500;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  flex: 1;\r\n}\r\n\r\n.rank-score {\r\n  width: 80px;\r\n  flex-shrink: 0;\r\n  text-align: right;\r\n  font-size: 17px;\r\n  font-weight: 600;\r\n  color: #3a86ff;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  padding-right: 5px; /* 为积分数字添加右侧内边距 */\r\n}\r\n\r\n/* 没有更多提示 */\r\n.no-more {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 20px 0;\r\n  gap: 10px;\r\n}\r\n\r\n.no-more-line {\r\n  height: 1px;\r\n  flex: 1;\r\n  background: #EFEFEF;\r\n  max-width: 60px;\r\n}\r\n\r\n.no-more-text {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n/* 自定义滚动条样式 */\r\n::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 2px;\r\n}\r\n\r\n::-webkit-scrollbar-thumb {\r\n  background: #ccc;\r\n  border-radius: 2px;\r\n}\r\n\r\n::-webkit-scrollbar-thumb:hover {\r\n  background: #aaa;\r\n}\r\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/checkin/pages/points-rank.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onMounted", "uni", "MiniProgramPage"], "mappings": ";;;;;;AAsGA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,iBAAiBA,cAAAA,IAAI,GAAG;AAG9B,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACnB,EAAE,IAAI,GAAG,UAAU,MAAM,QAAQ,qCAAqC,QAAQ,KAAM;AAAA,MACpF,EAAE,IAAI,GAAG,UAAU,MAAM,QAAQ,qCAAqC,QAAQ,KAAM;AAAA,MACpF,EAAE,IAAI,GAAG,UAAU,MAAM,QAAQ,qCAAqC,QAAQ,KAAM;AAAA,MACpF,EAAE,IAAI,GAAG,UAAU,MAAM,QAAQ,qCAAqC,QAAQ,KAAM;AAAA,MACpF,EAAE,IAAI,GAAG,UAAU,MAAM,QAAQ,qCAAqC,QAAQ,KAAM;AAAA,MACpF,EAAE,IAAI,GAAG,UAAU,MAAM,QAAQ,qCAAqC,QAAQ,IAAM;AAAA,MACpF,EAAE,IAAI,GAAG,UAAU,MAAM,QAAQ,qCAAqC,QAAQ,KAAM;AAAA,MACpF,EAAE,IAAI,GAAG,UAAU,MAAM,QAAQ,qCAAqC,QAAQ,KAAM;AAAA,MACpF,EAAE,IAAI,GAAG,UAAU,MAAM,QAAQ,qCAAqC,QAAQ,KAAM;AAAA,MACpF,EAAE,IAAI,IAAI,UAAU,MAAM,QAAQ,sCAAsC,QAAQ,KAAM;AAAA,IACxF,CAAC;AAGD,UAAM,OAAOC,cAAQ,SAAC,MAAM;AAC1B,aAAO,SAAS,MAAM,MAAM,GAAG,CAAC;AAAA,IAClC,CAAC;AAED,UAAM,WAAWA,cAAQ,SAAC,MAAM;AAE9B,aAAO,SAAS,MAAM,MAAM,GAAG,eAAe,KAAK;AAAA,IACrD,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AACd,UAAI;AACF,cAAM,UAAUC,oBAAI;AACpB,wBAAgB,QAAQ,QAAQ,mBAAmB;AAAA,MACpD,SAAQ,GAAG;AACV,wBAAgB,QAAQ;AAAA,MACzB;AAAA,IAIH,CAAC;AAGD,aAAS,SAAS;AAChBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAEA,aAAS,aAAa,QAAQ;AAC5B,aAAO,SAAS;AAAA,IAClB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpJA,GAAG,WAAWC,SAAe;"}