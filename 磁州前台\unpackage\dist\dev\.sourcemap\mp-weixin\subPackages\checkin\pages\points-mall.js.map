{"version": 3, "file": "points-mall.js", "sources": ["subPackages/checkin/pages/points-mall.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcY2hlY2tpblxwYWdlc1xwb2ludHMtbWFsbC52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"page-root\">\r\n\t\t<view class=\"nav-bg\" :style=\"{ height: (statusBarHeight + 44) + 'px' }\"></view>\r\n\t\t<view class=\"navbar-content\" :style=\"{ top: statusBarHeight + 'px', height: '44px' }\">\r\n\t\t\t<view class=\"navbar-left\" @click=\"goBack\">\r\n\t\t\t\t<image class=\"back-icon\" src=\"/static/images/tabbar/最新返回键.png\" mode=\"aspectFit\"></image>\r\n\t\t\t</view>\r\n\t\t\t<text class=\"navbar-title\">积分商城</text>\r\n\t\t\t<view class=\"navbar-right\"></view>\r\n\t\t</view>\r\n\t\t<view class=\"points-mall-container\" :style=\"{ paddingTop: (statusBarHeight + 44) + 'px' }\">\r\n\t\t<!-- 积分余额卡片 -->\r\n\t\t<view class=\"points-balance-card\">\r\n\t\t\t<view class=\"balance-container\">\r\n\t\t\t\t<view class=\"balance-label\">我的积分</view>\r\n\t\t\t\t<view class=\"balance-value\">{{ userPoints }}</view>\r\n\t\t\t</view>\r\n\t\t\t\t<view class=\"balance-actions-vertical\">\r\n\t\t\t\t\t<view class=\"balance-detail\" @click=\"navigateTo('/subPackages/checkin/pages/points-detail')\">积分明细</view>\r\n\t\t\t\t\t<view class=\"history-btn\" @click=\"navigateTo('/subPackages/checkin/pages/exchange-history')\">兑换记录</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 商品分类标签 -->\r\n\t\t<view class=\"category-tabs\">\r\n\t\t\t<scroll-view scroll-x class=\"tabs-scroll\" show-scrollbar=\"false\">\r\n\t\t\t\t<view \r\n\t\t\t\t\tclass=\"tab-item\" \r\n\t\t\t\t\t:class=\"{ active: currentCategory === category.id }\"\r\n\t\t\t\t\tv-for=\"(category, index) in categories\" \r\n\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t@click=\"switchCategory(index)\"\r\n\t\t\t\t>\r\n\t\t\t\t\t{{ category.name }}\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 商品列表 -->\r\n\t\t<scroll-view scroll-y class=\"products-container\" refresher-enabled @refresherrefresh=\"refreshProducts\" refresher-triggered=\"isRefreshing\">\r\n\t\t\t<view class=\"product-grid\">\r\n\t\t\t\t<view class=\"product-item\" v-for=\"(product, index) in filteredProducts\" :key=\"index\" @click=\"showProductDetail(product)\">\r\n\t\t\t\t\t<image class=\"product-image\" :src=\"product.image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t<view class=\"product-info\">\r\n\t\t\t\t\t\t<view class=\"product-name\">{{ product.name }}</view>\r\n\t\t\t\t\t\t<view class=\"product-description\">{{ product.description }}</view>\r\n\t\t\t\t\t\t<view class=\"product-bottom\">\r\n\t\t\t\t\t\t\t<view class=\"product-points\">{{ product.points }}积分</view>\r\n\t\t\t\t\t\t\t<view class=\"exchange-btn\" :class=\"{ 'disabled': product.points > userPoints }\">\r\n\t\t\t\t\t\t\t\t{{ product.points > userPoints ? '积分不足' : '立即兑换' }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"product-tag\" v-if=\"product.tag\">{{ product.tag }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 空状态提示 -->\r\n\t\t\t<view class=\"empty-state\" v-if=\"filteredProducts.length === 0\">\r\n\t\t\t\t<image class=\"empty-icon\" src=\"/static/images/tabbar/empty.png\"></image>\r\n\t\t\t\t<view class=\"empty-text\">暂无商品，敬请期待</view>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\t\t\r\n\t\t<!-- 商品详情弹窗 -->\r\n\t\t<view class=\"product-detail-popup\" v-if=\"showPopup\" @click.self=\"showPopup = false\">\r\n\t\t\t<view class=\"popup-content\">\r\n\t\t\t\t<view class=\"popup-close\" @click=\"showPopup = false\">×</view>\r\n\t\t\t\t<image class=\"popup-image\" :src=\"selectedProduct.image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t<view class=\"popup-info\">\r\n\t\t\t\t\t<view class=\"popup-name\">{{ selectedProduct.name }}</view>\r\n\t\t\t\t\t<view class=\"popup-description\">{{ selectedProduct.description }}</view>\r\n\t\t\t\t\t<view class=\"popup-points\">{{ selectedProduct.points }} 积分</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"popup-rules\" v-if=\"selectedProduct.rules\">\r\n\t\t\t\t\t\t<view class=\"rules-title\">兑换规则</view>\r\n\t\t\t\t\t\t<view class=\"rules-content\">{{ selectedProduct.rules }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"popup-stock\" v-if=\"selectedProduct.stock\">\r\n\t\t\t\t\t\t<text class=\"stock-label\">库存:</text>\r\n\t\t\t\t\t\t<text class=\"stock-value\">{{ selectedProduct.stock }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"popup-validity\" v-if=\"selectedProduct.validity\">\r\n\t\t\t\t\t\t<text class=\"validity-label\">有效期:</text>\r\n\t\t\t\t\t\t<text class=\"validity-value\">{{ selectedProduct.validity }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<button class=\"popup-btn\" \r\n\t\t\t\t\t\t:class=\"{ 'disabled': selectedProduct.points > userPoints }\"\r\n\t\t\t\t\t\t:disabled=\"selectedProduct.points > userPoints\"\r\n\t\t\t\t\t\t@click=\"exchangeProduct\">\r\n\t\t\t\t\t\t{{ selectedProduct.points > userPoints ? '积分不足' : '立即兑换' }}\r\n\t\t\t\t\t</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue';\r\n\r\n// Vue3迁移代码开始\r\n// 响应式状态\r\nconst statusBarHeight = ref(20);\r\nconst userPoints = ref(1280);\r\nconst currentCategory = ref(0);\r\nconst isRefreshing = ref(false);\r\nconst showPopup = ref(false);\r\nconst selectedProduct = ref({});\r\n\r\n// 常量数据\r\nconst categories = [\r\n\t{ id: 0, name: '全部' },\r\n\t{ id: 3, name: '虚拟商品' },\r\n\t{ id: 2, name: '实物商品' },\r\n\t{ id: 4, name: '限时兑换' },\r\n\t{ id: 1, name: '优惠券' }\r\n];\r\n\r\n// 商品数据\r\nconst products = ref([\r\n\t{\r\n\t\tid: 1,\r\n\t\tname: '5元通用券',\r\n\t\tdescription: '可用于平台内任意商家消费抵扣',\r\n\t\tpoints: 100,\r\n\t\timage: '/static/images/banner/coupon-1.png',\r\n\t\tcategory: 1,\r\n\t\tvalidity: '兑换后30天内有效',\r\n\t\tstock: 999,\r\n\t\trules: '单笔消费满20元可用，每人每月限兑换5张',\r\n\t\ttag: '热门'\r\n\t},\r\n\t{\r\n\t\tid: 2,\r\n\t\tname: '10元外卖券',\r\n\t\tdescription: '仅限平台认证餐饮商家使用',\r\n\t\tpoints: 180,\r\n\t\timage: '/static/images/banner/coupon-2.png',\r\n\t\tcategory: 1,\r\n\t\tvalidity: '兑换后30天内有效',\r\n\t\tstock: 500,\r\n\t\trules: '单笔消费满30元可用，每人每月限兑换3张'\r\n\t},\r\n\t{\r\n\t\tid: 3,\r\n\t\tname: '精美保温杯',\r\n\t\tdescription: '304不锈钢内胆，24小时保温',\r\n\t\tpoints: 1200,\r\n\t\timage: '/static/images/banner/product-1.png',\r\n\t\tcategory: 2,\r\n\t\tstock: 50,\r\n\t\trules: '兑换后7天内发货，快递费用由平台承担'\r\n\t},\r\n\t{\r\n\t\tid: 4,\r\n\t\tname: '信息置顶服务',\r\n\t\tdescription: '发布信息优先展示24小时',\r\n\t\tpoints: 300,\r\n\t\timage: '/static/images/banner/service-1.png',\r\n\t\tcategory: 3,\r\n\t\tvalidity: '兑换后7天内有效',\r\n\t\trules: '每个信息仅可使用一次，有效期内未使用将自动失效'\r\n\t},\r\n\t{\r\n\t\tid: 5,\r\n\t\tname: '会员月卡',\r\n\t\tdescription: '30天会员特权，免费发布信息',\r\n\t\tpoints: 500,\r\n\t\timage: '/static/images/banner/vip-1.png',\r\n\t\tcategory: 3,\r\n\t\tvalidity: '激活后30天内有效',\r\n\t\trules: '包含会员所有特权，可与其他优惠共享使用',\r\n\t\ttag: '超值'\r\n\t},\r\n\t{\r\n\t\tid: 6,\r\n\t\tname: '限时抢购券',\r\n\t\tdescription: '周末专享，满100减50',\r\n\t\tpoints: 250,\r\n\t\timage: '/static/images/banner/coupon-3.png',\r\n\t\tcategory: 4,\r\n\t\tvalidity: '本周六、日两天有效',\r\n\t\tstock: 100,\r\n\t\trules: '限指定商家使用，详见券面说明',\r\n\t\ttag: '限时'\r\n\t},\r\n\t{\r\n\t\tid: 7,\r\n\t\tname: '信息置顶卡（7天）',\r\n\t\tdescription: '让您的信息在同城列表置顶展示7天，曝光翻倍！',\r\n\t\tpoints: 500,\r\n\t\timage: '/static/images/banner/top-card-placeholder.png',\r\n\t\tcategory: 3,\r\n\t\tvalidity: '兑换后7天内有效',\r\n\t\trules: '仅限同城信息使用，每条信息每月限用一次',\r\n\t\ttag: '虚拟'\r\n\t},\r\n\t{\r\n\t\tid: 8,\r\n\t\tname: 'VIP会员月卡',\r\n\t\tdescription: '享受专属标识、免费发布、优先审核等特权30天',\r\n\t\tpoints: 1200,\r\n\t\timage: '/static/images/banner/vip-card-placeholder.png',\r\n\t\tcategory: 3,\r\n\t\tvalidity: '激活后30天内有效',\r\n\t\trules: '会员期间享受所有VIP特权',\r\n\t\ttag: '虚拟'\r\n\t},\r\n\t{\r\n\t\tid: 9,\r\n\t\tname: '积分抽奖券',\r\n\t\tdescription: '可参与平台积分抽奖活动，赢取实物大奖',\r\n\t\tpoints: 100,\r\n\t\timage: '/static/images/banner/lottery-placeholder.png',\r\n\t\tcategory: 3,\r\n\t\tvalidity: '兑换后30天内有效',\r\n\t\trules: '每次抽奖消耗1张抽奖券',\r\n\t\ttag: '虚拟'\r\n\t},\r\n\t{\r\n\t\tid: 10,\r\n\t\tname: '本地活动报名券',\r\n\t\tdescription: '免费报名本地线下活动、沙龙、讲座等',\r\n\t\tpoints: 80,\r\n\t\timage: '/static/images/banner/event-ticket-placeholder.png',\r\n\t\tcategory: 3,\r\n\t\tvalidity: '兑换后30天内有效',\r\n\t\trules: '部分活动需提前预约，具体以活动规则为准',\r\n\t\ttag: '虚拟'\r\n\t},\r\n\t{\r\n\t\tid: 11,\r\n\t\tname: '专属头像框',\r\n\t\tdescription: '兑换后可装饰个人头像，彰显个性',\r\n\t\tpoints: 60,\r\n\t\timage: '/static/images/banner/avatar-frame-placeholder.png',\r\n\t\tcategory: 3,\r\n\t\tvalidity: '永久有效',\r\n\t\trules: '可在个人中心-头像设置中更换',\r\n\t\ttag: '虚拟'\r\n\t},\r\n\t{\r\n\t\tid: 12,\r\n\t\tname: '话费充值10元',\r\n\t\tdescription: '兑换后系统将自动为您绑定的手机号充值',\r\n\t\tpoints: 1000,\r\n\t\timage: '/static/images/banner/phone-recharge.png',\r\n\t\tcategory: 3,\r\n\t\tvalidity: '兑换后立即到账',\r\n\t\trules: '仅限本人实名认证手机号使用，充值成功后不予退还',\r\n\t\ttag: '热门'\r\n\t},\r\n\t{\r\n\t\tid: 13,\r\n\t\tname: '话费充值30元',\r\n\t\tdescription: '兑换后系统将自动为您绑定的手机号充值',\r\n\t\tpoints: 2500,\r\n\t\timage: '/static/images/banner/phone-recharge.png',\r\n\t\tcategory: 3,\r\n\t\tvalidity: '兑换后立即到账',\r\n\t\trules: '仅限本人实名认证手机号使用，充值成功后不予退还',\r\n\t\ttag: '超值'\r\n\t},\r\n\t{\r\n\t\tid: 14,\r\n\t\tname: '话费充值100元',\r\n\t\tdescription: '兑换后系统将自动为您绑定的手机号充值',\r\n\t\tpoints: 8000,\r\n\t\timage: '/static/images/banner/phone-recharge.png',\r\n\t\tcategory: 3,\r\n\t\tvalidity: '兑换后立即到账',\r\n\t\trules: '仅限本人实名认证手机号使用，充值成功后不予退还',\r\n\t\ttag: '限量'\r\n\t}\r\n]);\r\n\r\n// 计算属性 - 根据当前分类筛选商品\r\nconst filteredProducts = computed(() => {\r\n\tif (currentCategory.value === 0) {\r\n\t\t// 全部分类下，按照以下顺序排序：话费充值、其他虚拟商品、实物商品、优惠券\r\n\t\treturn [...products.value].sort((a, b) => {\r\n\t\t\t// 话费充值（虚拟商品且名称包含\"话费\"）排在最前面\r\n\t\t\tif (a.name.includes('话费') && !b.name.includes('话费')) return -1;\r\n\t\t\tif (!a.name.includes('话费') && b.name.includes('话费')) return 1;\r\n\t\t\t\r\n\t\t\t// 同为话费充值商品，按面额从小到大排序\r\n\t\t\tif (a.name.includes('话费') && b.name.includes('话费')) {\r\n\t\t\t\tconst aValue = parseInt(a.name.match(/\\d+/)[0]);\r\n\t\t\t\tconst bValue = parseInt(b.name.match(/\\d+/)[0]);\r\n\t\t\t\treturn aValue - bValue;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 非话费商品，按分类排序\r\n\t\t\tif (a.category !== b.category) {\r\n\t\t\t\t// 其他虚拟商品排第二\r\n\t\t\t\tif (a.category === 3) return -1;\r\n\t\t\t\tif (b.category === 3) return 1;\r\n\t\t\t\t\r\n\t\t\t\t// 实物商品排第三\r\n\t\t\t\tif (a.category === 2) return -1;\r\n\t\t\t\tif (b.category === 2) return 1;\r\n\t\t\t\t\r\n\t\t\t\t// 限时兑换排第四\r\n\t\t\t\tif (a.category === 4) return -1;\r\n\t\t\t\tif (b.category === 4) return 1;\r\n\t\t\t\t\r\n\t\t\t\t// 优惠券排最后\r\n\t\t\t\treturn a.category - b.category;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 同类型内部按ID排序\r\n\t\t\treturn a.id - b.id;\r\n\t\t});\r\n\t} else {\r\n\t\treturn products.value.filter(product => product.category === currentCategory.value);\r\n\t}\r\n});\r\n\r\n// 生命周期钩子\r\nonMounted(() => {\r\n\t// 获取状态栏高度\r\n\tconst sysInfo = uni.getSystemInfoSync();\r\n\tstatusBarHeight.value = sysInfo.statusBarHeight || 20;\r\n\t\r\n\t// 获取用户积分\r\n\tgetUserPoints();\r\n\t\r\n\t// 确保初始分类为\"全部\"\r\n\tcurrentCategory.value = 0;\r\n});\r\n\r\n// 方法\r\n// 获取用户积分数据\r\nfunction getUserPoints() {\r\n\t// 实际应用中应调用API获取\r\n\t// 示例使用模拟数据\r\n\tsetTimeout(() => {\r\n\t\t// userPoints.value = 1280;\r\n\t}, 500);\r\n}\r\n\r\n// 切换商品分类\r\nfunction switchCategory(index) {\r\n\tcurrentCategory.value = categories[index].id;\r\n}\r\n\r\n// 刷新商品列表\r\nfunction refreshProducts(e) {\r\n\tisRefreshing.value = true;\r\n\t\r\n\t// 模拟刷新操作\r\n\tsetTimeout(() => {\r\n\t\tisRefreshing.value = false;\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '刷新成功',\r\n\t\t\ticon: 'none'\r\n\t\t});\r\n\t}, 1500);\r\n}\r\n\r\n// 显示商品详情\r\nfunction showProductDetail(product) {\r\n\tselectedProduct.value = product;\r\n\tshowPopup.value = true;\r\n}\r\n\r\n// 兑换商品\r\nfunction exchangeProduct() {\r\n\tif (selectedProduct.value.points > userPoints.value) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '积分不足',\r\n\t\t\ticon: 'none'\r\n\t\t});\r\n\t\treturn;\r\n\t}\r\n\t\r\n\tuni.showModal({\r\n\t\ttitle: '确认兑换',\r\n\t\tcontent: `确定使用${selectedProduct.value.points}积分兑换\"${selectedProduct.value.name}\"吗？`,\r\n\t\tsuccess: (res) => {\r\n\t\t\tif (res.confirm) {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '兑换中...'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 模拟兑换操作\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tuserPoints.value -= selectedProduct.value.points;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tshowPopup.value = false;\r\n\t\t\t\t\t\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '兑换成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\t\t\t\t}, 1500);\r\n\t\t\t}\r\n\t\t}\r\n\t});\r\n}\r\n\r\n// 页面导航\r\nfunction navigateTo(url) {\r\n\tuni.navigateTo({\r\n\t\turl: url\r\n\t});\r\n}\r\n\r\n// 返回上一页\r\nfunction goBack() {\r\n\tuni.navigateBack();\r\n}\r\n// Vue3迁移代码结束\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.page-root {\r\n\tposition: relative;\r\n\tmin-height: 100vh;\r\n\tbackground: #f6faff;\r\n}\r\n.nav-bg {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbackground: #1677FF;\r\n\tz-index: 100;\r\n\twidth: 100%;\r\n}\r\n.navbar-content {\r\n\tposition: fixed;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tz-index: 101;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tbackground: transparent;\r\n\twidth: 100%;\r\n}\r\n.navbar-left, .navbar-right {\r\n\twidth: 44px;\r\n\theight: 44px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n.back-icon {\r\n\twidth: 24px;\r\n\theight: 24px;\r\n\tdisplay: block;\r\n\tbackground: none;\r\n\tborder-radius: 0;\r\n\tmargin: 0 auto;\r\n}\r\n.navbar-title {\r\n\tflex: 1;\r\n\ttext-align: center;\r\n\tfont-size: 18px;\r\n\tfont-weight: 600;\r\n\tcolor: #fff;\r\n\tline-height: 44px;\r\n\twhite-space: nowrap;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n}\r\n.history-btn {\r\n\tmargin-top: 0;\r\n\tpadding: 3px 18px;\r\n\tborder: 1.5px solid #1677FF;\r\n\tcolor: #1677FF;\r\n\tborder-radius: 16px;\r\n\tfont-size: 13px;\r\n\tfont-weight: 500;\r\n\tbackground: #fff;\r\n\ttransition: background 0.2s, color 0.2s;\r\n\tline-height: 20px;\r\n\tdisplay: inline-flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n.history-btn:active {\r\n\tbackground: #1677FF;\r\n\tcolor: #fff;\r\n}\r\n.points-mall-container {\r\n\tmin-height: 100vh;\r\n\tposition: relative;\r\n\tbox-sizing: border-box;\r\n\tbackground: #f6faff;\r\n}\r\n\r\n/* 状态栏样式 */\r\n.status-bar {\r\n\tbackground-color: #ffffff;\r\n\twidth: 100%;\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tz-index: 100;\r\n}\r\n\r\n/* 积分余额卡片 */\r\n.points-balance-card {\r\n\tmax-width: 320px;\r\n\tmargin: 18px auto 0 auto;\r\n\tbackground: linear-gradient(135deg, #3a86ff, #1a56cc);\r\n\tborder-radius: 24rpx;\r\n\tpadding: 30rpx;\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tcolor: #fff;\r\n\tbox-shadow: 0 10rpx 30rpx rgba(58, 134, 255, 0.2);\r\n}\r\n\r\n.balance-container {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n\r\n.balance-label {\r\n\tfont-size: 26rpx;\r\n\topacity: 0.9;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.balance-value {\r\n\tfont-size: 48rpx;\r\n\tfont-weight: 700;\r\n\tfont-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;\r\n}\r\n\r\n.balance-actions-vertical {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 10px;\r\n\talign-items: flex-end;\r\n}\r\n\r\n.balance-detail {\r\n\twidth: 92px;\r\n\tpadding: 6px 0;\r\n\tbackground: linear-gradient(90deg, #1677FF 0%, #007aff 100%);\r\n\tcolor: #fff;\r\n\tborder-radius: 18px;\r\n\tfont-size: 14px;\r\n\tfont-weight: 500;\r\n\ttext-align: center;\r\n\tline-height: 22px;\r\n\tcursor: pointer;\r\n\tbox-shadow: 0 2px 8px rgba(22,119,255,0.08);\r\n}\r\n\r\n.history-btn {\r\n\twidth: 92px;\r\n\tpadding: 6px 0;\r\n\tborder: 1.5px solid #1677FF;\r\n\tcolor: #1677FF;\r\n\tborder-radius: 18px;\r\n\tfont-size: 14px;\r\n\tfont-weight: 500;\r\n\tbackground: #fff;\r\n\ttext-align: center;\r\n\tline-height: 22px;\r\n\tcursor: pointer;\r\n\ttransition: background 0.2s, color 0.2s;\r\n}\r\n\r\n.history-btn:active {\r\n\tbackground: #1677FF;\r\n\tcolor: #fff;\r\n}\r\n\r\n/* 分类标签 */\r\n.category-tabs {\r\n\tbackground-color: #fff;\r\n\tpadding: 20rpx 30rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.03);\r\n}\r\n\r\n.tabs-scroll {\r\n\twhite-space: nowrap;\r\n\twidth: 100%;\r\n}\r\n\r\n.tab-item {\r\n\tdisplay: inline-block;\r\n\tpadding: 14rpx 30rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tmargin-right: 20rpx;\r\n\tborder-radius: 40rpx;\r\n\ttransition: all 0.3s;\r\n\tbackground-color: #f5f5f5;\r\n}\r\n\r\n.tab-item.active {\r\n\tbackground: linear-gradient(135deg, #3a86ff, #1a56cc);\r\n\tcolor: #fff;\r\n\tfont-weight: 500;\r\n\tbox-shadow: 0 5rpx 15rpx rgba(58, 134, 255, 0.2);\r\n}\r\n\r\n/* 商品列表 */\r\n.products-container {\r\n\tpadding: 0;\r\n}\r\n\r\n.product-grid {\r\n\tdisplay: grid;\r\n\tgrid-template-columns: repeat(2, 160px);\r\n\tjustify-content: center;\r\n\tgap: 16px;\r\n\tpadding-bottom: 30rpx;\r\n}\r\n\r\n.product-item {\r\n\twidth: 160px;\r\n\tmargin: 0;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 20rpx;\r\n\toverflow: hidden;\r\n\tbox-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.05);\r\n\tposition: relative;\r\n}\r\n\r\n.product-image {\r\n\twidth: 100%;\r\n\theight: 240rpx;\r\n\tobject-fit: cover;\r\n}\r\n\r\n.product-info {\r\n\tpadding: 20rpx;\r\n}\r\n\r\n.product-name {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #333;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.product-description {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\tmargin-bottom: 24rpx;\r\n\theight: 68rpx;\r\n\tdisplay: -webkit-box;\r\n\t-webkit-box-orient: vertical;\r\n\t-webkit-line-clamp: 2;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n}\r\n\r\n.product-bottom {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n}\r\n\r\n.product-points {\r\n\tfont-size: 26rpx;\r\n\tcolor: #ff6b6b;\r\n\tfont-weight: 600;\r\n}\r\n\r\n.exchange-btn {\r\n\tbackground-color: rgba(58, 134, 255, 0.1);\r\n\tcolor: #3a86ff;\r\n\tpadding: 6rpx 16rpx;\r\n\tborder-radius: 30rpx;\r\n\tfont-size: 22rpx;\r\n\tfont-weight: 500;\r\n}\r\n\r\n.exchange-btn.disabled {\r\n\tbackground-color: rgba(0, 0, 0, 0.05);\r\n\tcolor: #999;\r\n}\r\n\r\n.product-tag {\r\n\tposition: absolute;\r\n\ttop: 16rpx;\r\n\tright: 16rpx;\r\n\tbackground: linear-gradient(135deg, #ff6b6b, #ff8e8e);\r\n\tcolor: #fff;\r\n\tpadding: 6rpx 16rpx;\r\n\tborder-radius: 20rpx 0 20rpx 0;\r\n\tfont-size: 22rpx;\r\n\tfont-weight: 500;\r\n\tbox-shadow: 0 4rpx 8rpx rgba(255, 107, 107, 0.2);\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n\tpadding: 100rpx 0;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.empty-icon {\r\n\twidth: 200rpx;\r\n\theight: 200rpx;\r\n\tmargin-bottom: 30rpx;\r\n\topacity: 0.6;\r\n}\r\n\r\n.empty-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n/* 商品详情弹窗 */\r\n.product-detail-popup {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground-color: rgba(0, 0, 0, 0.6);\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tz-index: 999;\r\n}\r\n\r\n.popup-content {\r\n\twidth: 85%;\r\n\tmax-width: 650rpx;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 24rpx;\r\n\toverflow: hidden;\r\n\tposition: relative;\r\n\tmax-height: 85vh;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n\r\n.popup-close {\r\n\tposition: absolute;\r\n\ttop: 20rpx;\r\n\tright: 20rpx;\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tborder-radius: 50%;\r\n\tbackground-color: rgba(0, 0, 0, 0.3);\r\n\tcolor: #fff;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tfont-size: 40rpx;\r\n\tz-index: 10;\r\n}\r\n\r\n.popup-image {\r\n\twidth: 100%;\r\n\theight: 350rpx;\r\n\tobject-fit: cover;\r\n}\r\n\r\n.popup-info {\r\n\tpadding: 30rpx;\r\n}\r\n\r\n.popup-name {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #333;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.popup-description {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 20rpx;\r\n\tline-height: 1.6;\r\n}\r\n\r\n.popup-points {\r\n\tfont-size: 36rpx;\r\n\tcolor: #ff6b6b;\r\n\tfont-weight: 600;\r\n\tmargin-bottom: 24rpx;\r\n}\r\n\r\n.popup-rules {\r\n\tbackground-color: #f9f9f9;\r\n\tpadding: 20rpx;\r\n\tborder-radius: 16rpx;\r\n\tmargin-bottom: 24rpx;\r\n}\r\n\r\n.rules-title {\r\n\tfont-size: 26rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #333;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.rules-content {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tline-height: 1.6;\r\n}\r\n\r\n.popup-stock, .popup-validity {\r\n\tdisplay: flex;\r\n\tfont-size: 26rpx;\r\n\tmargin-bottom: 16rpx;\r\n}\r\n\r\n.stock-label, .validity-label {\r\n\tcolor: #999;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.stock-value, .validity-value {\r\n\tcolor: #333;\r\n}\r\n\r\n.popup-btn {\r\n\twidth: 100%;\r\n\theight: 80rpx;\r\n\tbackground: linear-gradient(135deg, #3a86ff, #1a56cc);\r\n\tcolor: #fff;\r\n\tborder-radius: 40rpx;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 500;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tmargin-top: 30rpx;\r\n\tbox-shadow: 0 10rpx 20rpx rgba(58, 134, 255, 0.2);\r\n}\r\n\r\n.popup-btn.disabled {\r\n\tbackground: #ccc;\r\n\tbox-shadow: none;\r\n}\r\n\r\n.mall-history-row {\r\n\tdisplay: flex;\r\n\tjustify-content: flex-end;\r\n\tpadding: 0 30rpx 10px 30rpx;\r\n\tbackground: transparent;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/checkin/pages/points-mall.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onMounted", "uni", "MiniProgramPage"], "mappings": ";;;;;;AA2GA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,aAAaA,cAAAA,IAAI,IAAI;AAC3B,UAAM,kBAAkBA,cAAAA,IAAI,CAAC;AAC7B,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAC9B,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAC3B,UAAM,kBAAkBA,cAAAA,IAAI,CAAA,CAAE;AAG9B,UAAM,aAAa;AAAA,MAClB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,MACrB,EAAE,IAAI,GAAG,MAAM,OAAQ;AAAA,MACvB,EAAE,IAAI,GAAG,MAAM,OAAQ;AAAA,MACvB,EAAE,IAAI,GAAG,MAAM,OAAQ;AAAA,MACvB,EAAE,IAAI,GAAG,MAAM,MAAO;AAAA,IACvB;AAGA,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACpB;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,QACP,OAAO;AAAA,QACP,KAAK;AAAA,MACL;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,QACP,OAAO;AAAA,MACP;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,QACP,OAAO;AAAA,MACP;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,MACP;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,MACL;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,QACP,OAAO;AAAA,QACP,KAAK;AAAA,MACL;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,MACL;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,MACL;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,MACL;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,MACL;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,MACL;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,MACL;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,MACL;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,MACL;AAAA,IACF,CAAC;AAGD,UAAM,mBAAmBC,cAAQ,SAAC,MAAM;AACvC,UAAI,gBAAgB,UAAU,GAAG;AAEhC,eAAO,CAAC,GAAG,SAAS,KAAK,EAAE,KAAK,CAAC,GAAG,MAAM;AAEzC,cAAI,EAAE,KAAK,SAAS,IAAI,KAAK,CAAC,EAAE,KAAK,SAAS,IAAI;AAAG,mBAAO;AAC5D,cAAI,CAAC,EAAE,KAAK,SAAS,IAAI,KAAK,EAAE,KAAK,SAAS,IAAI;AAAG,mBAAO;AAG5D,cAAI,EAAE,KAAK,SAAS,IAAI,KAAK,EAAE,KAAK,SAAS,IAAI,GAAG;AACnD,kBAAM,SAAS,SAAS,EAAE,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;AAC9C,kBAAM,SAAS,SAAS,EAAE,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;AAC9C,mBAAO,SAAS;AAAA,UAChB;AAGD,cAAI,EAAE,aAAa,EAAE,UAAU;AAE9B,gBAAI,EAAE,aAAa;AAAG,qBAAO;AAC7B,gBAAI,EAAE,aAAa;AAAG,qBAAO;AAG7B,gBAAI,EAAE,aAAa;AAAG,qBAAO;AAC7B,gBAAI,EAAE,aAAa;AAAG,qBAAO;AAG7B,gBAAI,EAAE,aAAa;AAAG,qBAAO;AAC7B,gBAAI,EAAE,aAAa;AAAG,qBAAO;AAG7B,mBAAO,EAAE,WAAW,EAAE;AAAA,UACtB;AAGD,iBAAO,EAAE,KAAK,EAAE;AAAA,QACnB,CAAG;AAAA,MACH,OAAQ;AACN,eAAO,SAAS,MAAM,OAAO,aAAW,QAAQ,aAAa,gBAAgB,KAAK;AAAA,MAClF;AAAA,IACF,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AAEf,YAAM,UAAUC,oBAAI;AACpB,sBAAgB,QAAQ,QAAQ,mBAAmB;AAGnD;AAGA,sBAAgB,QAAQ;AAAA,IACzB,CAAC;AAID,aAAS,gBAAgB;AAGxB,iBAAW,MAAM;AAAA,MAEhB,GAAE,GAAG;AAAA,IACP;AAGA,aAAS,eAAe,OAAO;AAC9B,sBAAgB,QAAQ,WAAW,KAAK,EAAE;AAAA,IAC3C;AAGA,aAAS,gBAAgB,GAAG;AAC3B,mBAAa,QAAQ;AAGrB,iBAAW,MAAM;AAChB,qBAAa,QAAQ;AACrBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACD,GAAE,IAAI;AAAA,IACR;AAGA,aAAS,kBAAkB,SAAS;AACnC,sBAAgB,QAAQ;AACxB,gBAAU,QAAQ;AAAA,IACnB;AAGA,aAAS,kBAAkB;AAC1B,UAAI,gBAAgB,MAAM,SAAS,WAAW,OAAO;AACpDA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AACD;AAAA,MACA;AAEDA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS,OAAO,gBAAgB,MAAM,MAAM,QAAQ,gBAAgB,MAAM,IAAI;AAAA,QAC9E,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChBA,0BAAAA,MAAI,YAAY;AAAA,cACf,OAAO;AAAA,YACZ,CAAK;AAGD,uBAAW,MAAM;AAChB,yBAAW,SAAS,gBAAgB,MAAM;AAC1CA,4BAAG,MAAC,YAAW;AACf,wBAAU,QAAQ;AAElBA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,MAAM;AAAA,cACZ,CAAM;AAAA,YACD,GAAE,IAAI;AAAA,UACP;AAAA,QACD;AAAA,MACH,CAAE;AAAA,IACF;AAGA,aAAS,WAAW,KAAK;AACxBA,oBAAAA,MAAI,WAAW;AAAA,QACd;AAAA,MACF,CAAE;AAAA,IACF;AAGA,aAAS,SAAS;AACjBA,oBAAG,MAAC,aAAY;AAAA,IACjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/ZA,GAAG,WAAWC,SAAe;"}