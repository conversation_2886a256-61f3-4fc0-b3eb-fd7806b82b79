{"version": 3, "file": "points-detail.js", "sources": ["subPackages/checkin/pages/points-detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcY2hlY2tpblxwYWdlc1xwb2ludHMtZGV0YWlsLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"page-root\">\r\n\t\t<!-- 顶部蓝色背景，包含状态栏+导航栏 -->\r\n\t\t<view class=\"nav-bg\" :style=\"{ height: (statusBarHeight + 44) + 'px' }\"></view>\r\n\t\t<!-- 导航栏内容绝对定位在蓝色背景上，顶部margin为statusBarHeight -->\r\n\t\t<view class=\"navbar-content\" :style=\"{ top: statusBarHeight + 'px', height: '44px' }\">\r\n\t\t\t<view class=\"navbar-left\" @click=\"goBack\">\r\n\t\t\t\t<image class=\"back-icon\" src=\"/static/images/tabbar/最新返回键.png\" mode=\"aspectFit\"></image>\r\n\t\t\t</view>\r\n\t\t\t<text class=\"navbar-title\">积分明细</text>\r\n\t\t\t<view class=\"navbar-right\"></view>\r\n\t\t</view>\r\n\t\t<view class=\"points-detail-container\" :style=\"{ paddingTop: (statusBarHeight + 44) + 'px' }\">\r\n\t\t<!-- 筛选标签 -->\r\n\t\t<view class=\"filter-tabs\">\r\n\t\t\t<view \r\n\t\t\t\tclass=\"tab-item\" \r\n\t\t\t\t:class=\"{ active: currentTab === index }\"\r\n\t\t\t\tv-for=\"(tab, index) in tabs\" \r\n\t\t\t\t:key=\"index\"\r\n\t\t\t\t@click=\"switchTab(index)\"\r\n\t\t\t>\r\n\t\t\t\t{{ tab }}\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 记录列表 -->\r\n\t\t<scroll-view \r\n\t\t\tscroll-y \r\n\t\t\tclass=\"record-list\" \r\n\t\t\trefresher-enabled \r\n\t\t\t@refresherrefresh=\"refreshRecords\" \r\n\t\t\t:refresher-triggered=\"isRefreshing\"\r\n\t\t>\r\n\t\t\t<view v-if=\"filteredRecords.length > 0\">\r\n\t\t\t\t<!-- 日期分组 -->\r\n\t\t\t\t<block v-for=\"(group, groupIndex) in groupedRecords\" :key=\"groupIndex\">\r\n\t\t\t\t\t<view class=\"date-header\">{{ group.date }}</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 每组下的记录列表 -->\r\n\t\t\t\t\t<view class=\"record-item\" v-for=\"(record, index) in group.records\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"record-left\">\r\n\t\t\t\t\t\t\t<view class=\"record-title\">{{ record.title }}</view>\r\n\t\t\t\t\t\t\t<view class=\"record-time\">{{ record.time }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"record-points\" :class=\"{ 'income': record.type === 'income', 'expense': record.type === 'expense' }\">\r\n\t\t\t\t\t\t\t{{ record.type === 'income' ? '+' : '-' }}{{ record.points }}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 空状态 -->\r\n\t\t\t<view class=\"empty-state\" v-else>\r\n\t\t\t\t<image class=\"empty-icon\" src=\"/static/images/tabbar/empty.png\"></image>\r\n\t\t\t\t<view class=\"empty-text\">暂无积分记录</view>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue';\r\n\r\n// Vue3迁移代码开始\r\n// 响应式状态\r\nconst statusBarHeight = ref(20);\r\nconst currentTab = ref(0);\r\nconst isRefreshing = ref(false);\r\n\r\n// 常量数据\r\nconst tabs = ['全部', '获取', '使用'];\r\n\r\n// 记录数据\r\nconst records = ref([\r\n\t{\r\n\t\tid: 1,\r\n\t\ttitle: '每日签到',\r\n\t\ttime: '10:25',\r\n\t\tdate: '2023-11-25',\r\n\t\tpoints: 5,\r\n\t\ttype: 'income'\r\n\t},\r\n\t{\r\n\t\tid: 2,\r\n\t\ttitle: '浏览商家完成',\r\n\t\ttime: '15:40',\r\n\t\tdate: '2023-11-25',\r\n\t\tpoints: 10,\r\n\t\ttype: 'income'\r\n\t},\r\n\t{\r\n\t\tid: 3,\r\n\t\ttitle: '兑换优惠券',\r\n\t\ttime: '18:33',\r\n\t\tdate: '2023-11-24',\r\n\t\tpoints: 100,\r\n\t\ttype: 'expense'\r\n\t},\r\n\t{\r\n\t\tid: 4,\r\n\t\ttitle: '分享小程序',\r\n\t\ttime: '09:15',\r\n\t\tdate: '2023-11-23',\r\n\t\tpoints: 20,\r\n\t\ttype: 'income'\r\n\t},\r\n\t{\r\n\t\tid: 5,\r\n\t\ttitle: '评论互动',\r\n\t\ttime: '14:22',\r\n\t\tdate: '2023-11-22',\r\n\t\tpoints: 5,\r\n\t\ttype: 'income'\r\n\t},\r\n\t{\r\n\t\tid: 6,\r\n\t\ttitle: '兑换会员月卡',\r\n\t\ttime: '11:05',\r\n\t\tdate: '2023-11-20',\r\n\t\tpoints: 500,\r\n\t\ttype: 'expense'\r\n\t}\r\n]);\r\n\r\n// 计算属性 - 根据当前选择的标签筛选记录\r\nconst filteredRecords = computed(() => {\r\n\tif (currentTab.value === 0) {\r\n\t\treturn records.value;\r\n\t} else if (currentTab.value === 1) {\r\n\t\t// 获取积分记录\r\n\t\treturn records.value.filter(record => record.type === 'income');\r\n\t} else {\r\n\t\t// 使用积分记录\r\n\t\treturn records.value.filter(record => record.type === 'expense');\r\n\t}\r\n});\r\n\r\n// 格式化日期标签\r\nfunction formatDateLabel(dateStr) {\r\n\tconst today = new Date().toISOString().split('T')[0];\r\n\tconst yesterday = new Date(Date.now() - 86400000).toISOString().split('T')[0];\r\n\t\r\n\tif (dateStr === today) {\r\n\t\treturn '今天';\r\n\t} else if (dateStr === yesterday) {\r\n\t\treturn '昨天';\r\n\t} else {\r\n\t\tconst date = new Date(dateStr);\r\n\t\treturn `${date.getMonth() + 1}月${date.getDate()}日`;\r\n\t}\r\n}\r\n\r\n// 计算属性 - 按日期分组记录\r\nconst groupedRecords = computed(() => {\r\n\tconst groups = {};\r\n\t\r\n\t// 按日期分组\r\n\tfilteredRecords.value.forEach(record => {\r\n\t\tif (!groups[record.date]) {\r\n\t\t\tgroups[record.date] = {\r\n\t\t\t\tdate: formatDateLabel(record.date),\r\n\t\t\t\trecords: []\r\n\t\t\t};\r\n\t\t}\r\n\t\t\r\n\t\tgroups[record.date].records.push(record);\r\n\t});\r\n\t\r\n\t// 转换为数组并按日期排序\r\n\treturn Object.values(groups).sort((a, b) => {\r\n\t\tconst dateA = new Date(a.date.replace('今天', new Date().toISOString().split('T')[0]));\r\n\t\tconst dateB = new Date(b.date.replace('今天', new Date().toISOString().split('T')[0]));\r\n\t\treturn dateB - dateA;\r\n\t});\r\n});\r\n\r\n// 生命周期钩子\r\nonMounted(() => {\r\n\t// 获取状态栏高度\r\n\tconst windowInfo = uni.getWindowInfo();\r\n\tstatusBarHeight.value = windowInfo.statusBarHeight || 20;\r\n\t\r\n\t// 加载积分记录\r\n\tloadPointsRecords();\r\n});\r\n\r\n// 方法\r\n// 切换标签\r\nfunction switchTab(index) {\r\n\tcurrentTab.value = index;\r\n}\r\n\r\n// 刷新记录\r\nfunction refreshRecords() {\r\n\tisRefreshing.value = true;\r\n\t\r\n\t// 模拟刷新操作\r\n\tsetTimeout(() => {\r\n\t\tloadPointsRecords();\r\n\t\tisRefreshing.value = false;\r\n\t\t\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '刷新成功',\r\n\t\t\ticon: 'none'\r\n\t\t});\r\n\t}, 1500);\r\n}\r\n\r\n// 加载积分记录\r\nfunction loadPointsRecords() {\r\n\t// 在实际应用中，应该从API获取数据\r\n\t// 这里使用模拟数据\r\n\t// setTimeout(() => {\r\n\t//   records.value = [...];\r\n\t// }, 500);\r\n}\r\n\r\n// 返回上一页\r\nfunction goBack() {\r\n\tuni.navigateBack();\r\n}\r\n// Vue3迁移代码结束\r\n</script>\r\n\r\n<style lang=\"scss\">\r\npage {\r\n\tbackground-color: #F8F8F8;\r\n\tfont-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;\r\n}\r\n\r\n.page-root {\r\n\tposition: relative;\r\n\tmin-height: 100vh;\r\n\tbackground: #f6faff;\r\n}\r\n\r\n.nav-bg {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbackground: #1677FF;\r\n\tz-index: 100;\r\n\twidth: 100%;\r\n}\r\n\r\n.navbar-content {\r\n\tposition: fixed;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tz-index: 101;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tbackground: transparent;\r\n\twidth: 100%;\r\n}\r\n\r\n.navbar-left, .navbar-right {\r\n\twidth: 44px;\r\n\theight: 44px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.back-icon {\r\n\twidth: 24px;\r\n\theight: 24px;\r\n\tdisplay: block;\r\n\tbackground: none;\r\n\tborder-radius: 0;\r\n\tmargin: 0 auto;\r\n}\r\n\r\n.navbar-title {\r\n\tflex: 1;\r\n\ttext-align: center;\r\n\tfont-size: 18px;\r\n\tfont-weight: 600;\r\n\tcolor: #fff;\r\n\tline-height: 44px;\r\n\twhite-space: nowrap;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n}\r\n\r\n/* 筛选标签 */\r\n.filter-tabs {\r\n\tdisplay: flex;\r\n\tbackground: #eaf3ff;\r\n\tborder-radius: 10px;\r\n\toverflow: hidden;\r\n\tmargin: 0 0 10px 0;\r\n\tpadding: 2px;\r\n\theight: 34px;\r\n}\r\n\r\n.tab-item {\r\n\tflex: 1;\r\n\ttext-align: center;\r\n\tfont-size: 13px;\r\n\tcolor: #1677FF;\r\n\tborder-radius: 8px;\r\n\tbackground: none;\r\n\tline-height: 30px;\r\n\ttransition: all 0.2s;\r\n\tmargin: 0 2px;\r\n}\r\n\r\n.tab-item.active {\r\n\tbackground: linear-gradient(90deg, #1677FF 0%, #007aff 100%);\r\n\tcolor: #fff;\r\n\tfont-weight: 600;\r\n\tbox-shadow: 0 2px 8px rgba(22,119,255,0.10);\r\n}\r\n\r\n.record-item {\r\n\tbackground: #fff;\r\n\tborder-radius: 7px;\r\n\tbox-shadow: 0 2px 8px rgba(22,119,255,0.04);\r\n\tborder-left: 3px solid #1677FF;\r\n\tmargin: 0 16px 7px 16px;\r\n\tpadding: 14px 14px 14px 14px;\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tword-break: break-all;\r\n}\r\n\r\n.record-left {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n\r\n.record-title {\r\n\tfont-size: 13px;\r\n}\r\n\r\n.record-time {\r\n\tfont-size: 11px;\r\n}\r\n\r\n.record-points {\r\n\tfont-size: 14px;\r\n\tfont-weight: 700;\r\n}\r\n\r\n.record-points.income {\r\n\tcolor: #1677FF;\r\n\tfont-weight: 700;\r\n}\r\n\r\n.record-points.expense {\r\n\tcolor: #ff4d4f;\r\n\tfont-weight: 700;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n\tpadding: 100rpx 0;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.empty-icon {\r\n\twidth: 200rpx;\r\n\theight: 200rpx;\r\n\tmargin-bottom: 30rpx;\r\n\topacity: 0.6;\r\n}\r\n\r\n.empty-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #999;\r\n}\r\n</style> \r\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/checkin/pages/points-detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onMounted", "uni", "MiniProgramPage"], "mappings": ";;;;;;AAmEA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,aAAaA,cAAAA,IAAI,CAAC;AACxB,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAG9B,UAAM,OAAO,CAAC,MAAM,MAAM,IAAI;AAG9B,UAAM,UAAUA,cAAAA,IAAI;AAAA,MACnB;AAAA,QACC,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACN;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACN;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACN;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACN;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACN;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AAGD,UAAM,kBAAkBC,cAAQ,SAAC,MAAM;AACtC,UAAI,WAAW,UAAU,GAAG;AAC3B,eAAO,QAAQ;AAAA,MACjB,WAAY,WAAW,UAAU,GAAG;AAElC,eAAO,QAAQ,MAAM,OAAO,YAAU,OAAO,SAAS,QAAQ;AAAA,MAChE,OAAQ;AAEN,eAAO,QAAQ,MAAM,OAAO,YAAU,OAAO,SAAS,SAAS;AAAA,MAC/D;AAAA,IACF,CAAC;AAGD,aAAS,gBAAgB,SAAS;AACjC,YAAM,SAAQ,oBAAI,QAAO,YAAa,EAAC,MAAM,GAAG,EAAE,CAAC;AACnD,YAAM,YAAY,IAAI,KAAK,KAAK,IAAG,IAAK,KAAQ,EAAE,YAAa,EAAC,MAAM,GAAG,EAAE,CAAC;AAE5E,UAAI,YAAY,OAAO;AACtB,eAAO;AAAA,MACT,WAAY,YAAY,WAAW;AACjC,eAAO;AAAA,MACT,OAAQ;AACN,cAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,eAAO,GAAG,KAAK,aAAa,CAAC,IAAI,KAAK,SAAS;AAAA,MAC/C;AAAA,IACF;AAGA,UAAM,iBAAiBA,cAAQ,SAAC,MAAM;AACrC,YAAM,SAAS,CAAA;AAGf,sBAAgB,MAAM,QAAQ,YAAU;AACvC,YAAI,CAAC,OAAO,OAAO,IAAI,GAAG;AACzB,iBAAO,OAAO,IAAI,IAAI;AAAA,YACrB,MAAM,gBAAgB,OAAO,IAAI;AAAA,YACjC,SAAS,CAAE;AAAA,UACf;AAAA,QACG;AAED,eAAO,OAAO,IAAI,EAAE,QAAQ,KAAK,MAAM;AAAA,MACzC,CAAE;AAGD,aAAO,OAAO,OAAO,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM;AAC3C,cAAM,QAAQ,IAAI,KAAK,EAAE,KAAK,QAAQ,OAAM,oBAAI,KAAI,GAAG,YAAa,EAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC;AACnF,cAAM,QAAQ,IAAI,KAAK,EAAE,KAAK,QAAQ,OAAM,oBAAI,KAAI,GAAG,YAAa,EAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC;AACnF,eAAO,QAAQ;AAAA,MACjB,CAAE;AAAA,IACF,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AAEf,YAAM,aAAaC,oBAAI;AACvB,sBAAgB,QAAQ,WAAW,mBAAmB;AAAA,IAIvD,CAAC;AAID,aAAS,UAAU,OAAO;AACzB,iBAAW,QAAQ;AAAA,IACpB;AAGA,aAAS,iBAAiB;AACzB,mBAAa,QAAQ;AAGrB,iBAAW,MAAM;AAEhB,qBAAa,QAAQ;AAErBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACD,GAAE,IAAI;AAAA,IACR;AAYA,aAAS,SAAS;AACjBA,oBAAG,MAAC,aAAY;AAAA,IACjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7NA,GAAG,WAAWC,SAAe;"}