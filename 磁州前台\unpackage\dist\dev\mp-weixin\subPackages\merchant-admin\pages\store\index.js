"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      storeInfo: {
        avatar: "/static/images/store-avatar.png",
        name: "磁州小吃美食店",
        description: "专注本地特色小吃，用心制作每一道美食",
        rating: 4.8,
        orderCount: 2580,
        viewCount: 12560,
        followerCount: 680,
        labels: ["特色小吃", "本地美食", "堂食", "外卖", "预订"],
        status: "open",
        operationHours: "09:00-21:00",
        isVerified: true
      },
      categories: [
        { id: 1, name: "招牌菜", count: 12 },
        { id: 2, name: "凉菜", count: 8 },
        { id: 3, name: "热菜", count: 15 },
        { id: 4, name: "主食", count: 6 },
        { id: 5, name: "汤类", count: 4 },
        { id: 6, name: "饮品", count: 7 }
      ],
      menuItems: [
        { id: 101, name: "铁板鱿鱼", price: 38, sales: 256, image: "/static/images/dish1.png", category: 1 },
        { id: 102, name: "爆炒腰花", price: 42, sales: 198, image: "/static/images/dish2.png", category: 1 },
        { id: 103, name: "剁椒鱼头", price: 58, sales: 172, image: "/static/images/dish3.png", category: 1 },
        { id: 104, name: "凉拌海蜇", price: 22, sales: 118, image: "/static/images/dish4.png", category: 2 },
        { id: 105, name: "手撕包菜", price: 18, sales: 167, image: "/static/images/dish5.png", category: 3 }
      ],
      currentTab: 1,
      // 当前选中的标签页（店铺管理）
      tabItems: [
        {
          id: 0,
          icon: "dashboard",
          text: "商家中心",
          url: "/subPackages/merchant-admin-home/pages/merchant-home/index"
        },
        {
          id: 1,
          icon: "store",
          text: "店铺管理",
          url: "/subPackages/merchant-admin/pages/store/index"
        },
        {
          id: 2,
          icon: "marketing",
          text: "营销中心",
          url: "/subPackages/merchant-admin-marketing/pages/marketing/index"
        },
        {
          id: 3,
          icon: "orders",
          text: "订单管理",
          url: "/subPackages/merchant-admin-order/pages/order/index"
        },
        {
          id: "more",
          icon: "more",
          text: "更多",
          url: ""
        }
      ]
    };
  },
  onLoad() {
    this.setStatusBarHeight();
  },
  methods: {
    // 设置状态栏高度
    setStatusBarHeight() {
      common_vendor.index.getSystemInfo({
        success: (res) => {
          this.statusBarHeight = res.statusBarHeight;
          if (typeof document !== "undefined") {
            document.documentElement.style.setProperty("--status-bar-height", `${this.statusBarHeight}px`);
          }
        }
      });
    },
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 编辑头像
    editAvatar() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          this.storeInfo.avatar = res.tempFilePaths[0];
          common_vendor.index.showToast({
            title: "头像已更新",
            icon: "success"
          });
        }
      });
    },
    // 预览店铺
    previewStore() {
      common_vendor.index.showToast({
        title: "店铺预览功能开发中",
        icon: "none"
      });
    },
    // 导航到具体功能页面
    navigateTo(page) {
      const pageMap = {
        "basicInfo": "/subPackages/merchant-admin/pages/store/basic-info",
        "operationInfo": "/subPackages/merchant-admin/pages/store/operation-info",
        "locationInfo": "/subPackages/merchant-admin/pages/store/location-info",
        "categoryManage": "/subPackages/merchant-admin/pages/store/category",
        "productManage": "/subPackages/merchant-admin/pages/store/product",
        "serviceManage": "/subPackages/merchant-admin/pages/store/service",
        "storeAlbum": "/subPackages/merchant-admin/pages/store/album",
        "videoManage": "/subPackages/merchant-admin/pages/store/video",
        "storeCulture": "/subPackages/merchant-admin/pages/store/culture",
        "storeVerify": "/subPackages/merchant-admin/pages/store/verify",
        "qualificationManage": "/subPackages/merchant-admin/pages/store/qualification",
        "qualificationRemind": "/subPackages/merchant-admin/pages/store/remind",
        "activityList": "/subPackages/merchant-admin/pages/store/activity-list",
        "activityOperation": "/subPackages/merchant-admin/pages/store/activity-operation",
        "activityData": "/subPackages/merchant-admin/pages/store/activity-data"
      };
      const url = pageMap[page];
      if (url) {
        common_vendor.index.navigateTo({ url });
      } else {
        common_vendor.index.showToast({
          title: "功能开发中",
          icon: "none"
        });
      }
    },
    // 切换底部导航栏
    switchTab(tabId) {
      if (tabId === "more") {
        this.showMoreOptions();
        return;
      }
      if (tabId === this.currentTab)
        return;
      this.currentTab = tabId;
      if (tabId === 1) {
        return;
      }
      common_vendor.index.redirectTo({
        url: this.tabItems[tabId].url,
        fail: (err) => {
          common_vendor.index.__f__("error", "at subPackages/merchant-admin/pages/store/index.vue:431", "redirectTo失败:", err);
          common_vendor.index.switchTab({
            url: this.tabItems[tabId].url,
            fail: (switchErr) => {
              common_vendor.index.__f__("error", "at subPackages/merchant-admin/pages/store/index.vue:436", "switchTab也失败:", switchErr);
              common_vendor.index.showToast({
                title: "页面跳转失败，请稍后再试",
                icon: "none"
              });
            }
          });
        }
      });
    },
    // 显示更多选项弹出菜单
    showMoreOptions() {
      const moreOptions = ["客户运营", "分析洞察", "系统设置"];
      common_vendor.index.showActionSheet({
        itemList: moreOptions,
        success: (res) => {
          const routes = [
            "/subPackages/merchant-admin-customer/pages/customer/index",
            "/subPackages/merchant-admin/pages/settings/index"
          ];
          common_vendor.index.navigateTo({
            url: routes[res.tapIndex]
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$7,
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: common_assets._imports_1$30,
    d: $data.storeInfo.avatar || "/static/images/default-store.png",
    e: common_assets._imports_2$42,
    f: common_vendor.o((...args) => $options.editAvatar && $options.editAvatar(...args)),
    g: common_vendor.t($data.storeInfo.name),
    h: $data.storeInfo.isVerified
  }, $data.storeInfo.isVerified ? {} : {}, {
    i: common_vendor.t($data.storeInfo.description || "暂无店铺介绍"),
    j: common_vendor.t($data.storeInfo.rating),
    k: common_vendor.t($data.storeInfo.orderCount),
    l: common_vendor.t($data.storeInfo.visitorCount),
    m: common_vendor.o((...args) => $options.previewStore && $options.previewStore(...args)),
    n: $data.storeInfo.status === "open" ? 1 : "",
    o: common_vendor.t($data.storeInfo.status === "open" ? "营业中" : "休息中"),
    p: common_vendor.t($data.storeInfo.operationHours),
    q: common_assets._imports_3$36,
    r: common_vendor.o(($event) => $options.navigateTo("basicInfo")),
    s: common_assets._imports_4$26,
    t: common_vendor.o(($event) => $options.navigateTo("operationInfo")),
    v: common_assets._imports_5$23,
    w: common_vendor.o(($event) => $options.navigateTo("locationInfo")),
    x: common_assets._imports_6$20,
    y: common_vendor.o(($event) => $options.navigateTo("categoryManage")),
    z: common_assets._imports_7$11,
    A: common_vendor.o(($event) => $options.navigateTo("productManage")),
    B: common_assets._imports_8$9,
    C: common_vendor.o(($event) => $options.navigateTo("serviceManage")),
    D: common_assets._imports_9$11,
    E: common_vendor.o(($event) => $options.navigateTo("storeAlbum")),
    F: common_assets._imports_10$7,
    G: common_vendor.o(($event) => $options.navigateTo("videoManage")),
    H: common_assets._imports_11$10,
    I: common_vendor.o(($event) => $options.navigateTo("storeCulture")),
    J: common_assets._imports_12$6,
    K: common_vendor.o(($event) => $options.navigateTo("storeVerify")),
    L: common_assets._imports_13$4,
    M: common_vendor.o(($event) => $options.navigateTo("qualificationManage")),
    N: common_assets._imports_14$6,
    O: common_vendor.o(($event) => $options.navigateTo("qualificationRemind")),
    P: common_assets._imports_15$5,
    Q: common_vendor.o(($event) => $options.navigateTo("activityList")),
    R: common_assets._imports_16$3,
    S: common_vendor.o(($event) => $options.navigateTo("activityOperation")),
    T: common_assets._imports_17$3,
    U: common_vendor.o(($event) => $options.navigateTo("activityData")),
    V: common_vendor.f($data.tabItems, (tab, index, i0) => {
      return common_vendor.e({
        a: $data.currentTab === tab.id
      }, $data.currentTab === tab.id ? {} : {}, {
        b: common_vendor.n(tab.icon),
        c: common_vendor.t(tab.text),
        d: index,
        e: $data.currentTab === tab.id ? 1 : "",
        f: common_vendor.o(($event) => $options.switchTab(tab.id), index)
      });
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin/pages/store/index.js.map
