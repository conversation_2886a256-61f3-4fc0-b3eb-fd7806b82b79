<template>
  <view class="discount-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <image class="back-icon" src="/static/images/tabbar/最新返回键.png" mode="aspectFit"></image>
        </view>
        <view class="navbar-title">满减优惠</view>
        <view class="navbar-right">
          <view class="close-btn" @click="goBack">
            <svg class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="22" height="22">
              <path d="M512 421.490332L331.349941 240.840273c-24.988383-24.988383-65.35828-24.988383-90.346664 0-24.988383 24.988383-24.988383 65.35828 0 90.346664L421.653336 512 240.840273 692.812059c-24.988383 24.988383-24.988383 65.35828 0 90.346664 24.988383 24.988383 65.35828 24.988383 90.346664 0L512 602.509668l180.650059 180.650059c24.988383 24.988383 65.35828 24.988383 90.346664 0 24.988383-24.988383 24.988383-65.35828 0-90.346664L602.346664 512l180.813063-180.812059c24.988383-24.988383 24.988383-65.35828 0-90.346664-24.988383-24.988383-65.35828-24.988383-90.346664 0L512 421.490332z" fill="#FFFFFF"></path>
            </svg>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 筛选选项卡 -->
    <view class="filter-tabs">
      <view 
        class="tab-item" 
        v-for="(tab, index) in tabs" 
        :key="index"
        :class="{ active: currentTabIndex === index }"
        @click="switchTab(index)"
      >
        <text>{{ tab.name }}</text>
      </view>
    </view>
    
    <!-- 内容区域 -->
    <scroll-view 
      class="content-scroll" 
      scroll-y 
      @scrolltolower="loadMore"
    >
      <!-- 商家满减活动列表 -->
      <view class="discount-list">
        <view 
          class="discount-item" 
          v-for="(item, index) in discountItems" 
          :key="index"
          @click="navigateToDetail(item.id)"
        >
          <view class="merchant-info">
            <image class="merchant-logo" :src="item.logo" mode="aspectFill"></image>
            <view class="merchant-name">{{ item.merchantName }}</view>
            <view class="discount-tag" v-if="item.tag">{{ item.tag }}</view>
          </view>
          
          <view class="discount-rules">
            <view 
              class="rule-item" 
              v-for="(rule, ruleIndex) in item.rules" 
              :key="ruleIndex"
              :class="{ 'highlight': rule.highlight }"
            >
              <text class="rule-text">{{ rule.text }}</text>
            </view>
          </view>
          
          <view class="discount-footer">
            <view class="discount-time">
              <text>{{ getTimeText(item.startTime, item.endTime) }}</text>
            </view>
            <view class="discount-btn">
              <text>立即使用</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 加载更多提示 -->
      <view class="loading-more" v-if="loading">
        <text>加载中...</text>
      </view>
      
      <!-- 到底了提示 -->
      <view class="no-more" v-if="noMore">
        <text>已经到底啦~</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      refreshing: false,
      loading: false,
      noMore: false,
      currentTabIndex: 0,
      tabs: [
        { name: '全部' },
        { name: '餐饮美食' },
        { name: '休闲娱乐' },
        { name: '生活服务' }
      ],
      discountItems: [
        {
          id: 1,
          merchantName: '星巴克咖啡',
          logo: 'https://via.placeholder.com/100x100',
          tag: '热门',
          rules: [
            { text: '满50减10', highlight: false },
            { text: '满100减30', highlight: true },
            { text: '满200减60', highlight: false }
          ],
          startTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          endTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 2,
          merchantName: '肯德基',
          logo: 'https://via.placeholder.com/100x100',
          tag: '限时',
          rules: [
            { text: '满59减15', highlight: false },
            { text: '满99减30', highlight: true },
            { text: '满199减60', highlight: false }
          ],
          startTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          endTime: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 3,
          merchantName: '海底捞火锅',
          logo: 'https://via.placeholder.com/100x100',
          tag: '爆款',
          rules: [
            { text: '满200减30', highlight: false },
            { text: '满400减80', highlight: true },
            { text: '满600减150', highlight: false }
          ],
          startTime: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
          endTime: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 4,
          merchantName: '喜茶',
          logo: 'https://via.placeholder.com/100x100',
          tag: '新店',
          rules: [
            { text: '满40减8', highlight: false },
            { text: '满80减20', highlight: true },
            { text: '满120减35', highlight: false }
          ],
          startTime: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          endTime: new Date(Date.now() + 25 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 5,
          merchantName: '优衣库',
          logo: 'https://via.placeholder.com/100x100',
          tag: '折扣',
          rules: [
            { text: '满300减50', highlight: false },
            { text: '满500减100', highlight: true },
            { text: '满800减200', highlight: false }
          ],
          startTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          endTime: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString()
        }
      ]
    }
  },
  onLoad() {
    // 页面加载时获取数据
    this.fetchData()
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },
    
    // 切换选项卡
    switchTab(index) {
      this.currentTabIndex = index
      this.fetchData()
    },
    
    // 下拉刷新
    onRefresh() {
      // 获取当前滚动位置
      const query = uni.createSelectorQuery().in(this);
      query.select('.content-scroll').boundingClientRect(data => {
        // 只有在滚动到顶部或接近顶部时才刷新
        if (data && data.top <= 5) {
          this.refreshing = true
          
          // 模拟刷新数据
          setTimeout(() => {
            this.fetchData()
            this.refreshing = false
            uni.showToast({
              title: '刷新成功',
              icon: 'none'
            })
          }, 1500)
        } else {
          // 不在顶部，取消刷新状态
          this.refreshing = false
        }
      }).exec();
    },
    
    // 加载更多
    loadMore() {
      if (this.loading || this.noMore) return
      
      this.loading = true
      
      // 模拟加载更多数据
      setTimeout(() => {
        // 添加更多满减活动数据
        const moreItems = [
          {
            id: 6,
            merchantName: '必胜客',
            logo: 'https://via.placeholder.com/100x100',
            tag: '人气',
            rules: [
              { text: '满100减20', highlight: false },
              { text: '满200减50', highlight: true },
              { text: '满300减80', highlight: false }
            ],
            startTime: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
            endTime: new Date(Date.now() + 18 * 24 * 60 * 60 * 1000).toISOString()
          },
          {
            id: 7,
            merchantName: '屈臣氏',
            logo: 'https://via.placeholder.com/100x100',
            tag: '特惠',
            rules: [
              { text: '满99减20', highlight: false },
              { text: '满199减50', highlight: true },
              { text: '满299减100', highlight: false }
            ],
            startTime: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString(),
            endTime: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString()
          }
        ]
        
        this.discountItems = [...this.discountItems, ...moreItems]
        this.noMore = true // 示例中加载一次后就没有更多数据
        this.loading = false
      }, 1500)
    },
    
    // 获取数据
    fetchData() {
      // 实际项目中，这里应该根据当前选中的选项卡调用API获取数据
      // 示例中使用的是静态数据
    },
    
    // 导航到详情页
    navigateToDetail(id) {
      uni.navigateTo({
        url: `/subPackages/activity-showcase/pages/discount/detail?id=${id}`
      })
    },
    
    // 获取活动时间文本
    getTimeText(startTime, endTime) {
      const start = new Date(startTime)
      const end = new Date(endTime)
      
      const startMonth = start.getMonth() + 1
      const startDay = start.getDate()
      const endMonth = end.getMonth() + 1
      const endDay = end.getDate()
      
      return `${startMonth}.${startDay}-${endMonth}.${endDay}`
    }
  }
}
</script>

<style lang="scss" scoped>
.discount-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F2F2F7;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
  
  .navbar-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #5E5CE6 0%, #5856D6 100%);
  }
  
  .navbar-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding-top: var(--status-bar-height, 25px);
    padding-left: 30rpx;
    padding-right: 30rpx;
    box-sizing: border-box;
    
    .back-btn {
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .back-icon {
      width: 100%;
      height: 100%;
    }
    
    .navbar-title {
      font-size: 18px;
      font-weight: 600;
      color: #FFFFFF;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
    }
    
    .navbar-right {
      width: 80rpx;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      
      .close-btn {
        width: 64rpx;
        height: 64rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

/* 筛选选项卡 */
.filter-tabs {
  position: relative;
  display: flex;
  background-color: #FFFFFF;
  height: 88rpx;
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
  overflow-x: auto;
  white-space: nowrap;
  
  .tab-item {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding: 0 30rpx;
    font-size: 28rpx;
    color: #666666;
    position: relative;
    height: 100%;
    
    &.active {
      color: #5856D6;
      font-weight: 600;
      
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40rpx;
        height: 4rpx;
        background-color: #5856D6;
        border-radius: 2rpx;
      }
    }
  }
}

/* 内容区域 */
.content-scroll {
  flex: 1;
  width: 100%;
}

/* 满减活动列表 */
.discount-list {
  padding: 20rpx;
}

.discount-item {
  margin-bottom: 20rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
  
  .merchant-info {
    display: flex;
    align-items: center;
    padding: 20rpx;
    border-bottom: 1px solid #F2F2F7;
    
    .merchant-logo {
      width: 60rpx;
      height: 60rpx;
      border-radius: 30rpx;
      margin-right: 16rpx;
    }
    
    .merchant-name {
      flex: 1;
      font-size: 28rpx;
      font-weight: 600;
      color: #333333;
    }
    
    .discount-tag {
      padding: 4rpx 12rpx;
      font-size: 22rpx;
      color: #FFFFFF;
      border-radius: 10rpx;
      background: linear-gradient(135deg, #5E5CE6 0%, #5856D6 100%);
    }
  }
  
  .discount-rules {
    padding: 20rpx;
    display: flex;
    flex-wrap: wrap;
    
    .rule-item {
      margin-right: 20rpx;
      margin-bottom: 10rpx;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      background-color: #F2F2F7;
      
      &.highlight {
        background: linear-gradient(135deg, #5E5CE6 0%, #5856D6 100%);
        
        .rule-text {
          color: #FFFFFF;
        }
      }
      
      .rule-text {
        font-size: 24rpx;
        color: #666666;
      }
    }
  }
  
  .discount-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx;
    border-top: 1px solid #F2F2F7;
    
    .discount-time {
      font-size: 24rpx;
      color: #999999;
    }
    
    .discount-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 60rpx;
      width: 160rpx;
      border-radius: 30rpx;
      background: linear-gradient(135deg, #5E5CE6 0%, #5856D6 100%);
      font-size: 26rpx;
      font-weight: 500;
      color: #FFFFFF;
    }
  }
}

/* 加载更多和到底了提示 */
.loading-more, .no-more {
  text-align: center;
  padding: 20rpx 0;
  font-size: 24rpx;
  color: #999999;
}
</style> 