"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      dateRange: "2023-04-01 ~ 2023-04-30",
      currentType: "全部类型",
      // 红包数据概览
      redpacketData: {
        totalCount: 5862,
        countTrend: "up",
        countGrowth: "12.5%",
        totalAmount: 58620.5,
        amountTrend: "up",
        amountGrowth: "15.2%",
        receiveRate: 76.8,
        receiveRateTrend: "up",
        receiveRateGrowth: "3.5%",
        conversionRate: 42.3,
        conversionTrend: "up",
        conversionGrowth: "5.2%"
      },
      // 图表选项
      chartTabs: ["发放数量", "领取金额", "转化率"],
      currentChartTab: 0,
      // 用户数据
      userData: {
        newUserRate: 35,
        activeUserRate: 68,
        repurchaseRate: 42
      },
      // 红包类型数据
      typeData: [
        {
          name: "普通红包",
          count: 3256,
          percentage: 55,
          color: "#FF5858"
        },
        {
          name: "裂变红包",
          count: 1528,
          percentage: 26,
          color: "#4ECDC4"
        },
        {
          name: "群发红包",
          count: 782,
          percentage: 13,
          color: "#FFD166"
        },
        {
          name: "红包雨",
          count: 296,
          percentage: 6,
          color: "#6A0572"
        }
      ],
      // 排行榜选项
      rankingTabs: ["转化率", "领取率", "分享率"],
      currentRankingTab: 0,
      // 排行榜数据
      rankingList: [
        {
          title: "新用户专享红包",
          time: "2023-04-15 ~ 2023-04-20",
          value: 68.5
        },
        {
          title: "五一节日红包",
          time: "2023-05-01 ~ 2023-05-07",
          value: 62.3
        },
        {
          title: "周末限时红包",
          time: "2023-04-22 ~ 2023-04-23",
          value: 58.9
        },
        {
          title: "会员生日红包",
          time: "2023-04-01 ~ 2023-04-30",
          value: 52.4
        },
        {
          title: "满减活动红包",
          time: "2023-04-10 ~ 2023-04-15",
          value: 48.7
        }
      ]
    };
  },
  computed: {
    rankingValueUnit() {
      const units = ["%", "%", "%"];
      return units[this.currentRankingTab];
    }
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    showHelp() {
      common_vendor.index.showModal({
        title: "红包数据帮助",
        content: "在此页面您可以查看红包活动的各项数据指标和分析报告，帮助您优化红包营销策略。",
        showCancel: false
      });
    },
    showDatePicker() {
      common_vendor.index.showToast({
        title: "日期选择功能开发中",
        icon: "none"
      });
    },
    showTypeFilter() {
      common_vendor.index.showToast({
        title: "类型筛选功能开发中",
        icon: "none"
      });
    },
    formatNumber(num) {
      return num.toFixed(2);
    },
    switchChartTab(index) {
      this.currentChartTab = index;
    },
    switchRankingTab(index) {
      this.currentRankingTab = index;
    },
    exportReport() {
      common_vendor.index.showToast({
        title: "数据报表导出功能开发中",
        icon: "none"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: common_vendor.t($data.dateRange),
    d: common_vendor.o((...args) => $options.showDatePicker && $options.showDatePicker(...args)),
    e: common_vendor.t($data.currentType),
    f: common_vendor.o((...args) => $options.showTypeFilter && $options.showTypeFilter(...args)),
    g: common_vendor.t($data.redpacketData.totalCount),
    h: common_vendor.t($data.redpacketData.countGrowth),
    i: common_vendor.n($data.redpacketData.countTrend),
    j: common_vendor.t($options.formatNumber($data.redpacketData.totalAmount)),
    k: common_vendor.t($data.redpacketData.amountGrowth),
    l: common_vendor.n($data.redpacketData.amountTrend),
    m: common_vendor.t($data.redpacketData.receiveRate),
    n: common_vendor.t($data.redpacketData.receiveRateGrowth),
    o: common_vendor.n($data.redpacketData.receiveRateTrend),
    p: common_vendor.t($data.redpacketData.conversionRate),
    q: common_vendor.t($data.redpacketData.conversionGrowth),
    r: common_vendor.n($data.redpacketData.conversionTrend),
    s: common_vendor.f($data.chartTabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab),
        b: index,
        c: $data.currentChartTab === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchChartTab(index), index)
      };
    }),
    t: common_assets._imports_0$36,
    v: common_vendor.t($data.userData.newUserRate),
    w: "conic-gradient(#FF5858 " + $data.userData.newUserRate * 3.6 + "deg, #F5F7FA 0)",
    x: common_vendor.t($data.userData.activeUserRate),
    y: "conic-gradient(#4ECDC4 " + $data.userData.activeUserRate * 3.6 + "deg, #F5F7FA 0)",
    z: common_vendor.t($data.userData.repurchaseRate),
    A: "conic-gradient(#FFD166 " + $data.userData.repurchaseRate * 3.6 + "deg, #F5F7FA 0)",
    B: common_vendor.f($data.typeData, (item, index, i0) => {
      return {
        a: item.percentage + "%",
        b: item.color,
        c: common_vendor.t(item.name),
        d: common_vendor.t(item.count),
        e: common_vendor.t(item.percentage),
        f: index
      };
    }),
    C: common_vendor.f($data.rankingTabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab),
        b: index,
        c: $data.currentRankingTab === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchRankingTab(index), index)
      };
    }),
    D: common_vendor.f($data.rankingList, (item, index, i0) => {
      return {
        a: common_vendor.t(index + 1),
        b: index < 3 ? 1 : "",
        c: common_vendor.t(item.title),
        d: common_vendor.t(item.time),
        e: common_vendor.t(item.value),
        f: index
      };
    }),
    E: common_vendor.t($options.rankingValueUnit),
    F: common_vendor.o((...args) => $options.exportReport && $options.exportReport(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/redpacket/data-analysis.js.map
