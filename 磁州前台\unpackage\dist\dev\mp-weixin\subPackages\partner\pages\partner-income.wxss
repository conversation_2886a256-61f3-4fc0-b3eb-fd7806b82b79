/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-63ebc8b5, html.data-v-63ebc8b5, #app.data-v-63ebc8b5, .index-container.data-v-63ebc8b5 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.income-container.data-v-63ebc8b5 {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
  padding-top: calc(44px + var(--status-bar-height));
}
.income-overview.data-v-63ebc8b5 {
  margin: 30rpx;
  background: linear-gradient(135deg, #1677FF, #0E5FD8);
  border-radius: 16rpx;
  padding: 30rpx;
  color: #ffffff;
  box-shadow: 0 10rpx 20rpx rgba(22, 119, 255, 0.2);
}
.overview-header.data-v-63ebc8b5 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.overview-title.data-v-63ebc8b5 {
  font-size: 28rpx;
  opacity: 0.9;
}
.month-picker.data-v-63ebc8b5 {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}
.month-picker .cuIcon-calendar.data-v-63ebc8b5 {
  margin-left: 10rpx;
}
.overview-amount.data-v-63ebc8b5 {
  font-size: 60rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}
.overview-stats.data-v-63ebc8b5 {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 20rpx;
}
.stats-item.data-v-63ebc8b5 {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}
.stats-value.data-v-63ebc8b5 {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.stats-label.data-v-63ebc8b5 {
  font-size: 24rpx;
  opacity: 0.8;
}
.stats-divider.data-v-63ebc8b5 {
  width: 1px;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.2);
}
.income-chart.data-v-63ebc8b5 {
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.chart-header.data-v-63ebc8b5 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.chart-title.data-v-63ebc8b5 {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
}
.chart-legend.data-v-63ebc8b5 {
  display: flex;
}
.legend-item.data-v-63ebc8b5 {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
  font-size: 24rpx;
  color: #666666;
}
.legend-color.data-v-63ebc8b5 {
  width: 20rpx;
  height: 10rpx;
  margin-right: 8rpx;
}
.legend-color.level-one.data-v-63ebc8b5 {
  background-color: #1677FF;
}
.legend-color.level-two.data-v-63ebc8b5 {
  background-color: #36CBCB;
}
.chart-content.data-v-63ebc8b5 {
  height: 400rpx;
  display: flex;
  align-items: flex-end;
}
.chart-bars.data-v-63ebc8b5 {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  width: 100%;
  height: 300rpx;
}
.chart-bar.data-v-63ebc8b5 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  width: 40rpx;
}
.bar-item.data-v-63ebc8b5 {
  width: 40rpx;
  border-radius: 4rpx 4rpx 0 0;
}
.bar-item.level-one.data-v-63ebc8b5 {
  background-color: #1677FF;
}
.bar-item.level-two.data-v-63ebc8b5 {
  background-color: #36CBCB;
}
.bar-date.data-v-63ebc8b5 {
  margin-top: 10rpx;
  font-size: 22rpx;
  color: #999999;
}
.filter-section.data-v-63ebc8b5 {
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 0 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.filter-tabs.data-v-63ebc8b5 {
  display: flex;
}
.tab-item.data-v-63ebc8b5 {
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #666666;
  position: relative;
}
.tab-item.active.data-v-63ebc8b5 {
  color: #1677FF;
  font-weight: 500;
}
.tab-item.active.data-v-63ebc8b5::after {
  content: "";
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #1677FF;
  border-radius: 2rpx;
}
.income-list.data-v-63ebc8b5 {
  padding: 0 30rpx;
}
.income-item.data-v-63ebc8b5 {
  display: flex;
  justify-content: space-between;
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.item-left.data-v-63ebc8b5 {
  display: flex;
  flex-direction: column;
}
.item-title.data-v-63ebc8b5 {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 10rpx;
}
.item-desc.data-v-63ebc8b5 {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 10rpx;
}
.item-time.data-v-63ebc8b5 {
  font-size: 24rpx;
  color: #999999;
}
.item-right.data-v-63ebc8b5 {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
}
.item-amount.data-v-63ebc8b5 {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.item-amount.level-one-text.data-v-63ebc8b5 {
  color: #1677FF;
}
.item-amount.level-two-text.data-v-63ebc8b5 {
  color: #36CBCB;
}
.item-level.data-v-63ebc8b5 {
  font-size: 24rpx;
  color: #999999;
  padding: 4rpx 12rpx;
  background-color: #f5f5f5;
  border-radius: 20rpx;
}
.empty-state.data-v-63ebc8b5 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}
.empty-state image.data-v-63ebc8b5 {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 30rpx;
}
.empty-state text.data-v-63ebc8b5 {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 40rpx;
}
.empty-state .share-btn.data-v-63ebc8b5 {
  width: 300rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: linear-gradient(135deg, #1677FF, #0E5FD8);
  color: #ffffff;
  font-size: 28rpx;
  border-radius: 40rpx;
}
.load-more.data-v-63ebc8b5, .load-end.data-v-63ebc8b5 {
  text-align: center;
  font-size: 26rpx;
  color: #999999;
  padding: 30rpx 0;
}

/* 自定义导航栏样式 */
.custom-navbar.data-v-63ebc8b5 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  padding: 0 30rpx;
  padding-top: 44px;
  /* 状态栏高度 */
  position: fixed;
  /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  background-image: linear-gradient(135deg, #0066FF, #0052CC);
  /* 改为与发布页一致的渐变角度 */
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.15);
  z-index: 100;
  /* 提高z-index确保在最上层 */
}
.navbar-title.data-v-63ebc8b5 {
  position: absolute;
  left: 0;
  right: 0;
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 700;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  text-align: center;
}
.navbar-right.data-v-63ebc8b5 {
  width: 40rpx;
  height: 40rpx;
}
.navbar-left.data-v-63ebc8b5 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 20;
  /* 确保在标题上层，可以被点击 */
}
.back-icon.data-v-63ebc8b5 {
  width: 100%;
  height: 100%;
}
.safe-area-top.data-v-63ebc8b5 {
  height: var(--status-bar-height);
  width: 100%;
  background-image: linear-gradient(135deg, #0066FF, #0052CC);
}