{"version": 3, "file": "coupons.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/member/coupons.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xtZW1iZXJcY291cG9ucy52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"coupons-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">会员卡券</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 卡券概览 -->\r\n    <view class=\"overview-section\">\r\n      <view class=\"overview-header\">\r\n        <text class=\"section-title\">卡券概览</text>\r\n        <view class=\"date-picker\" @click=\"showDatePicker\">\r\n          <text class=\"date-text\">{{dateRange}}</text>\r\n          <view class=\"date-icon\"></view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"stats-cards\">\r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">{{couponData.totalCoupons}}</view>\r\n          <view class=\"card-label\">总卡券数</view>\r\n        </view>\r\n        \r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">{{couponData.issuedCoupons}}</view>\r\n          <view class=\"card-label\">已发放卡券</view>\r\n          <view class=\"card-trend\" :class=\"couponData.issuedTrend\">\r\n            <view class=\"trend-arrow\"></view>\r\n            <text class=\"trend-value\">{{couponData.issuedGrowth}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">{{couponData.usedCoupons}}</view>\r\n          <view class=\"card-label\">已使用卡券</view>\r\n          <view class=\"card-trend\" :class=\"couponData.usedTrend\">\r\n            <view class=\"trend-arrow\"></view>\r\n            <text class=\"trend-value\">{{couponData.usedGrowth}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">{{couponData.useRate}}%</view>\r\n          <view class=\"card-label\">使用率</view>\r\n          <view class=\"card-trend\" :class=\"couponData.useRateTrend\">\r\n            <view class=\"trend-arrow\"></view>\r\n            <text class=\"trend-value\">{{couponData.useRateGrowth}}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 卡券分类标签 -->\r\n    <view class=\"tabs-section\">\r\n      <scroll-view scroll-x class=\"tabs-scroll\" show-scrollbar=\"false\">\r\n        <view class=\"tabs\">\r\n          <view \r\n            class=\"tab-item\" \r\n            v-for=\"(tab, index) in tabs\" \r\n            :key=\"index\"\r\n            :class=\"{ active: currentTab === index }\"\r\n            @click=\"switchTab(index)\"\r\n          >\r\n            {{tab}}\r\n          </view>\r\n        </view>\r\n      </scroll-view>\r\n    </view>\r\n    \r\n    <!-- 卡券列表 -->\r\n    <view class=\"coupons-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">{{tabs[currentTab]}}</text>\r\n        <view class=\"add-btn\" @click=\"createCoupon\">\r\n          <text class=\"btn-text\">添加卡券</text>\r\n          <view class=\"plus-icon\"></view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"coupons-list\">\r\n        <view class=\"coupon-item\" v-for=\"(coupon, index) in filteredCoupons\" :key=\"index\" @click=\"editCoupon(coupon)\">\r\n          <view class=\"coupon-card\" :class=\"coupon.type\">\r\n            <view class=\"coupon-left\">\r\n              <view class=\"coupon-value\">\r\n                <text class=\"value-prefix\" v-if=\"coupon.type === 'discount'\">{{coupon.value}}<text class=\"value-unit\">折</text></text>\r\n                <text class=\"value-prefix\" v-else>¥<text class=\"value-number\">{{coupon.value}}</text></text>\r\n              </view>\r\n              <view class=\"coupon-condition\">{{coupon.condition}}</view>\r\n            </view>\r\n            <view class=\"coupon-divider\">\r\n              <view class=\"circle top\"></view>\r\n              <view class=\"dashed-line\"></view>\r\n              <view class=\"circle bottom\"></view>\r\n            </view>\r\n            <view class=\"coupon-right\">\r\n              <view class=\"coupon-name\">{{coupon.name}}</view>\r\n              <view class=\"coupon-desc\">{{coupon.description}}</view>\r\n              <view class=\"coupon-period\">{{coupon.validPeriod}}</view>\r\n              <view class=\"coupon-levels\">\r\n                <text class=\"level-tag\" v-for=\"(level, levelIndex) in coupon.memberLevels\" :key=\"levelIndex\">{{level}}</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"coupon-status\" :class=\"coupon.status\">{{coupon.statusText}}</view>\r\n          </view>\r\n          <view class=\"coupon-actions\">\r\n            <view class=\"action-btn issue\" @click.stop=\"issueCoupon(coupon)\">\r\n              <svg class=\"svg-icon\" viewBox=\"0 0 24 24\" fill=\"#FF6FD8\">\r\n                <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-2 10h-4v4h-2v-4H7v-2h4V7h2v4h4v2z\"/>\r\n              </svg>\r\n              <text>发放</text>\r\n            </view>\r\n            <view class=\"action-btn stats\" @click.stop=\"viewStats(coupon)\">\r\n              <svg class=\"svg-icon\" viewBox=\"0 0 24 24\" fill=\"#3813C2\">\r\n                <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\r\n              </svg>\r\n              <text>统计</text>\r\n            </view>\r\n            <view class=\"action-btn toggle\">\r\n              <switch :checked=\"coupon.enabled\" @change=\"(e) => toggleCoupon(coupon, e)\" color=\"#FF6FD8\" />\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 浮动操作按钮 -->\r\n    <view class=\"floating-action-button\" @click=\"createCoupon\">\r\n      <view class=\"fab-icon\">+</view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      dateRange: '2023-04-01 ~ 2023-04-30',\r\n      currentTab: 0,\r\n      \r\n      // 卡券数据概览\r\n      couponData: {\r\n        totalCoupons: 16,\r\n        issuedCoupons: 3568,\r\n        issuedTrend: 'up',\r\n        issuedGrowth: '15.2%',\r\n        usedCoupons: 1245,\r\n        usedTrend: 'up',\r\n        usedGrowth: '8.7%',\r\n        useRate: 34.9,\r\n        useRateTrend: 'down',\r\n        useRateGrowth: '2.3%'\r\n      },\r\n      \r\n      // 卡券分类标签\r\n      tabs: ['全部卡券', '满减券', '折扣券', '代金券', '会员专享'],\r\n      \r\n      // 卡券列表\r\n      coupons: [\r\n        {\r\n          id: 1,\r\n          name: '新人专享券',\r\n          description: '新会员专享优惠',\r\n          type: 'cash',\r\n          value: 10,\r\n          condition: '满100元可用',\r\n          validPeriod: '领取后30天内有效',\r\n          category: '代金券',\r\n          memberLevels: ['全部会员'],\r\n          status: 'active',\r\n          statusText: '进行中',\r\n          enabled: true\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '会员日特惠',\r\n          description: '每月28日会员专享',\r\n          type: 'discount',\r\n          value: 8.5,\r\n          condition: '无门槛',\r\n          validPeriod: '每月28日当天有效',\r\n          category: '折扣券',\r\n          memberLevels: ['银卡会员', '金卡会员', '钻石会员'],\r\n          status: 'active',\r\n          statusText: '进行中',\r\n          enabled: true\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '生日礼券',\r\n          description: '会员生日当月专享',\r\n          type: 'cash',\r\n          value: 50,\r\n          condition: '满200元可用',\r\n          validPeriod: '生日当月有效',\r\n          category: '会员专享',\r\n          memberLevels: ['银卡会员', '金卡会员', '钻石会员'],\r\n          status: 'active',\r\n          statusText: '进行中',\r\n          enabled: true\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '满减优惠券',\r\n          description: '全场通用满减券',\r\n          type: 'cash',\r\n          value: 20,\r\n          condition: '满200元可用',\r\n          validPeriod: '2023-04-01至2023-04-30',\r\n          category: '满减券',\r\n          memberLevels: ['全部会员'],\r\n          status: 'active',\r\n          statusText: '进行中',\r\n          enabled: true\r\n        },\r\n        {\r\n          id: 5,\r\n          name: '钻石会员专享',\r\n          description: '钻石会员专享优惠',\r\n          type: 'discount',\r\n          value: 7.5,\r\n          condition: '无门槛',\r\n          validPeriod: '长期有效',\r\n          category: '会员专享',\r\n          memberLevels: ['钻石会员'],\r\n          status: 'active',\r\n          statusText: '进行中',\r\n          enabled: true\r\n        },\r\n        {\r\n          id: 6,\r\n          name: '限时满减券',\r\n          description: '周末限时优惠',\r\n          type: 'cash',\r\n          value: 30,\r\n          condition: '满300元可用',\r\n          validPeriod: '每周六日有效',\r\n          category: '满减券',\r\n          memberLevels: ['全部会员'],\r\n          status: 'active',\r\n          statusText: '进行中',\r\n          enabled: true\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    filteredCoupons() {\r\n      if (this.currentTab === 0) {\r\n        return this.coupons;\r\n      } else {\r\n        const category = this.tabs[this.currentTab];\r\n        return this.coupons.filter(coupon => coupon.category === category);\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    \r\n    showHelp() {\r\n      uni.showModal({\r\n        title: '会员卡券帮助',\r\n        content: '会员卡券是指为会员提供的各类优惠券，包括满减券、折扣券、代金券等，可以提高会员购买转化率和复购率。',\r\n        showCancel: false\r\n      });\r\n    },\r\n    \r\n    showDatePicker() {\r\n      // 实现日期选择器\r\n      uni.showToast({\r\n        title: '日期选择功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    \r\n    switchTab(index) {\r\n      this.currentTab = index;\r\n    },\r\n    \r\n    createCoupon() {\r\n      uni.navigateTo({\r\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/create-coupon?type=member'\r\n      });\r\n    },\r\n    \r\n    editCoupon(coupon) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/edit-coupon?id=${coupon.id}`\r\n      });\r\n    },\r\n    \r\n    issueCoupon(coupon) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/issue-coupon?id=${coupon.id}`\r\n      });\r\n    },\r\n    \r\n    viewStats(coupon) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/coupon-stats?id=${coupon.id}`\r\n      });\r\n    },\r\n    \r\n    toggleCoupon(coupon, e) {\r\n      // 更新卡券状态\r\n      const index = this.coupons.findIndex(item => item.id === coupon.id);\r\n      if (index !== -1) {\r\n        this.coupons[index].enabled = e.detail.value;\r\n        this.coupons[index].status = e.detail.value ? 'active' : 'inactive';\r\n        this.coupons[index].statusText = e.detail.value ? '进行中' : '已暂停';\r\n      }\r\n      \r\n      uni.showToast({\r\n        title: e.detail.value ? `${coupon.name}已启用` : `${coupon.name}已禁用`,\r\n        icon: 'none'\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.coupons-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #FF6FD8, #3813C2);\r\n  color: #fff;\r\n  padding: 44px 16px 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 2px 10px rgba(255, 111, 216, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.navbar-right {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.help-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 通用部分样式 */\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.add-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  background: #FF6FD8;\r\n  border-radius: 15px;\r\n  padding: 5px 12px;\r\n  color: white;\r\n}\r\n\r\n.btn-text {\r\n  font-size: 13px;\r\n  margin-right: 5px;\r\n}\r\n\r\n.plus-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  position: relative;\r\n}\r\n\r\n.plus-icon:before,\r\n.plus-icon:after {\r\n  content: '';\r\n  position: absolute;\r\n  background: white;\r\n}\r\n\r\n.plus-icon:before {\r\n  width: 12px;\r\n  height: 2px;\r\n  top: 5px;\r\n  left: 0;\r\n}\r\n\r\n.plus-icon:after {\r\n  height: 12px;\r\n  width: 2px;\r\n  left: 5px;\r\n  top: 0;\r\n}\r\n\r\n/* 概览部分样式 */\r\n.overview-section {\r\n  margin: 15px;\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  padding: 15px;\r\n}\r\n\r\n.overview-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.date-picker {\r\n  display: flex;\r\n  align-items: center;\r\n  background: #F5F7FA;\r\n  border-radius: 15px;\r\n  padding: 5px 10px;\r\n}\r\n\r\n.date-text {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-right: 5px;\r\n}\r\n\r\n.date-icon {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-top: 2px solid #666;\r\n  border-right: 2px solid #666;\r\n  transform: rotate(135deg);\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: 0 -7.5px;\r\n}\r\n\r\n.stats-card {\r\n  width: 50%;\r\n  padding: 7.5px;\r\n  box-sizing: border-box;\r\n  position: relative;\r\n}\r\n\r\n.card-value {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 5px;\r\n  background: #F8FAFC;\r\n  padding: 15px;\r\n  border-radius: 10px;\r\n  border-left: 3px solid #FF6FD8;\r\n}\r\n\r\n.card-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n  position: absolute;\r\n  bottom: 20px;\r\n  left: 25px;\r\n}\r\n\r\n.card-trend {\r\n  position: absolute;\r\n  bottom: 20px;\r\n  right: 25px;\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 12px;\r\n}\r\n\r\n.card-trend.up {\r\n  color: #34C759;\r\n}\r\n\r\n.card-trend.down {\r\n  color: #FF3B30;\r\n}\r\n\r\n.trend-arrow {\r\n  width: 0;\r\n  height: 0;\r\n  margin-right: 3px;\r\n}\r\n\r\n.card-trend.up .trend-arrow {\r\n  border-left: 4px solid transparent;\r\n  border-right: 4px solid transparent;\r\n  border-bottom: 6px solid #34C759;\r\n}\r\n\r\n.card-trend.down .trend-arrow {\r\n  border-left: 4px solid transparent;\r\n  border-right: 4px solid transparent;\r\n  border-top: 6px solid #FF3B30;\r\n}\r\n\r\n/* 标签页样式 */\r\n.tabs-section {\r\n  margin: 15px 15px 0;\r\n}\r\n\r\n.tabs-scroll {\r\n  white-space: nowrap;\r\n}\r\n\r\n.tabs {\r\n  display: inline-flex;\r\n  padding: 5px 0;\r\n}\r\n\r\n.tab-item {\r\n  padding: 8px 16px;\r\n  font-size: 14px;\r\n  color: #666;\r\n  margin-right: 10px;\r\n  background: #fff;\r\n  border-radius: 20px;\r\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);\r\n  transition: all 0.3s;\r\n}\r\n\r\n.tab-item.active {\r\n  background: #FF6FD8;\r\n  color: white;\r\n  box-shadow: 0 2px 8px rgba(255, 111, 216, 0.3);\r\n}\r\n\r\n/* 卡券列表样式 */\r\n.coupons-section {\r\n  margin: 15px;\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  padding: 15px;\r\n}\r\n\r\n.coupons-list {\r\n  margin-top: 10px;\r\n}\r\n\r\n.coupon-item {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.coupon-card {\r\n  display: flex;\r\n  background: white;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\r\n  position: relative;\r\n}\r\n\r\n.coupon-card.cash {\r\n  background: linear-gradient(135deg, rgba(255, 111, 216, 0.1), rgba(255, 111, 216, 0.05));\r\n}\r\n\r\n.coupon-card.discount {\r\n  background: linear-gradient(135deg, rgba(56, 19, 194, 0.1), rgba(56, 19, 194, 0.05));\r\n}\r\n\r\n.coupon-left {\r\n  width: 100px;\r\n  padding: 15px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.coupon-value {\r\n  font-size: 24px;\r\n  font-weight: 700;\r\n  color: #FF6FD8;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.coupon-card.discount .coupon-value {\r\n  color: #3813C2;\r\n}\r\n\r\n.value-prefix {\r\n  font-size: 16px;\r\n}\r\n\r\n.value-number {\r\n  font-size: 24px;\r\n}\r\n\r\n.value-unit {\r\n  font-size: 14px;\r\n}\r\n\r\n.coupon-condition {\r\n  font-size: 12px;\r\n  color: #666;\r\n  text-align: center;\r\n}\r\n\r\n.coupon-divider {\r\n  position: relative;\r\n  width: 1px;\r\n  background: repeating-linear-gradient(to bottom, #ddd 0, #ddd 5px, transparent 5px, transparent 10px);\r\n}\r\n\r\n.circle {\r\n  position: absolute;\r\n  width: 16px;\r\n  height: 16px;\r\n  background: #F5F7FA;\r\n  border-radius: 50%;\r\n  left: -7.5px;\r\n}\r\n\r\n.circle.top {\r\n  top: -8px;\r\n}\r\n\r\n.circle.bottom {\r\n  bottom: -8px;\r\n}\r\n\r\n.coupon-right {\r\n  flex: 1;\r\n  padding: 15px;\r\n  position: relative;\r\n}\r\n\r\n.coupon-name {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.coupon-desc {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.coupon-period {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.coupon-levels {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.level-tag {\r\n  font-size: 10px;\r\n  color: #FF6FD8;\r\n  background: rgba(255, 111, 216, 0.1);\r\n  padding: 2px 8px;\r\n  border-radius: 10px;\r\n  margin-right: 5px;\r\n  margin-bottom: 3px;\r\n}\r\n\r\n.coupon-card.discount .level-tag {\r\n  color: #3813C2;\r\n  background: rgba(56, 19, 194, 0.1);\r\n}\r\n\r\n.coupon-status {\r\n  position: absolute;\r\n  top: 15px;\r\n  right: 15px;\r\n  font-size: 12px;\r\n  padding: 2px 8px;\r\n  border-radius: 10px;\r\n}\r\n\r\n.coupon-status.active {\r\n  background: rgba(52, 199, 89, 0.1);\r\n  color: #34C759;\r\n}\r\n\r\n.coupon-status.inactive {\r\n  background: rgba(142, 142, 147, 0.1);\r\n  color: #8E8E93;\r\n}\r\n\r\n.coupon-actions {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  padding: 10px 15px;\r\n  background: #F8FAFC;\r\n  border-radius: 0 0 12px 12px;\r\n}\r\n\r\n.action-btn {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.action-btn text {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-top: 3px;\r\n}\r\n\r\n.svg-icon {\r\n  width: 20px;\r\n  height: 20px;\r\n}\r\n\r\n.action-btn.toggle {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n/* 浮动操作按钮 */\r\n.floating-action-button {\r\n  position: fixed;\r\n  bottom: 30px;\r\n  right: 30px;\r\n  width: 56px;\r\n  height: 56px;\r\n  border-radius: 28px;\r\n  background: linear-gradient(135deg, #FF6FD8, #3813C2);\r\n  box-shadow: 0 4px 15px rgba(255, 111, 216, 0.3);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 100;\r\n}\r\n\r\n.fab-icon {\r\n  font-size: 28px;\r\n  color: #fff;\r\n  font-weight: 300;\r\n  line-height: 1;\r\n  margin-top: -2px;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media screen and (max-width: 375px) {\r\n  .stats-card {\r\n    width: 100%;\r\n  }\r\n  \r\n  .coupon-left {\r\n    width: 80px;\r\n  }\r\n  \r\n  .coupon-value {\r\n    font-size: 20px;\r\n  }\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/member/coupons.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA2IA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,YAAY;AAAA;AAAA,MAGZ,YAAY;AAAA,QACV,cAAc;AAAA,QACd,eAAe;AAAA,QACf,aAAa;AAAA,QACb,cAAc;AAAA,QACd,aAAa;AAAA,QACb,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,cAAc;AAAA,QACd,eAAe;AAAA,MAChB;AAAA;AAAA,MAGD,MAAM,CAAC,QAAQ,OAAO,OAAO,OAAO,MAAM;AAAA;AAAA,MAG1C,SAAS;AAAA,QACP;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW;AAAA,UACX,aAAa;AAAA,UACb,UAAU;AAAA,UACV,cAAc,CAAC,MAAM;AAAA,UACrB,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW;AAAA,UACX,aAAa;AAAA,UACb,UAAU;AAAA,UACV,cAAc,CAAC,QAAQ,QAAQ,MAAM;AAAA,UACrC,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW;AAAA,UACX,aAAa;AAAA,UACb,UAAU;AAAA,UACV,cAAc,CAAC,QAAQ,QAAQ,MAAM;AAAA,UACrC,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW;AAAA,UACX,aAAa;AAAA,UACb,UAAU;AAAA,UACV,cAAc,CAAC,MAAM;AAAA,UACrB,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW;AAAA,UACX,aAAa;AAAA,UACb,UAAU;AAAA,UACV,cAAc,CAAC,MAAM;AAAA,UACrB,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW;AAAA,UACX,aAAa;AAAA,UACb,UAAU;AAAA,UACV,cAAc,CAAC,MAAM;AAAA,UACrB,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,kBAAkB;AAChB,UAAI,KAAK,eAAe,GAAG;AACzB,eAAO,KAAK;AAAA,aACP;AACL,cAAM,WAAW,KAAK,KAAK,KAAK,UAAU;AAC1C,eAAO,KAAK,QAAQ,OAAO,YAAU,OAAO,aAAa,QAAQ;AAAA,MACnE;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IAED,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MACd,CAAC;AAAA,IACF;AAAA,IAED,iBAAiB;AAEfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IAED,UAAU,OAAO;AACf,WAAK,aAAa;AAAA,IACnB;AAAA,IAED,eAAe;AACbA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IAED,WAAW,QAAQ;AACjBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,wEAAwE,OAAO,EAAE;AAAA,MACxF,CAAC;AAAA,IACF;AAAA,IAED,YAAY,QAAQ;AAClBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,yEAAyE,OAAO,EAAE;AAAA,MACzF,CAAC;AAAA,IACF;AAAA,IAED,UAAU,QAAQ;AAChBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,yEAAyE,OAAO,EAAE;AAAA,MACzF,CAAC;AAAA,IACF;AAAA,IAED,aAAa,QAAQ,GAAG;AAEtB,YAAM,QAAQ,KAAK,QAAQ,UAAU,UAAQ,KAAK,OAAO,OAAO,EAAE;AAClE,UAAI,UAAU,IAAI;AAChB,aAAK,QAAQ,KAAK,EAAE,UAAU,EAAE,OAAO;AACvC,aAAK,QAAQ,KAAK,EAAE,SAAS,EAAE,OAAO,QAAQ,WAAW;AACzD,aAAK,QAAQ,KAAK,EAAE,aAAa,EAAE,OAAO,QAAQ,QAAQ;AAAA,MAC5D;AAEAA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,EAAE,OAAO,QAAQ,GAAG,OAAO,IAAI,QAAQ,GAAG,OAAO,IAAI;AAAA,QAC5D,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpUA,GAAG,WAAW,eAAe;"}