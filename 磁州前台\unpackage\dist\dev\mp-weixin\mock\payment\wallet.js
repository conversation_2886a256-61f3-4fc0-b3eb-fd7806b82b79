"use strict";
const walletInfo = {
  balance: 1280.5,
  points: 2560,
  coupons: 8,
  redPackets: 3,
  bankCards: [
    {
      id: "card-001",
      bank: "中国建设银行",
      type: "储蓄卡",
      number: "6217 **** **** 3456",
      isDefault: true
    },
    {
      id: "card-002",
      bank: "中国工商银行",
      type: "信用卡",
      number: "6222 **** **** 7890",
      isDefault: false
    }
  ],
  securityLevel: "high",
  // low, medium, high
  hasPinCode: true,
  hasFingerprint: true,
  hasFaceID: false
};
const fetchWalletInfo = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(walletInfo);
    }, 300);
  });
};
const rechargeWallet = (amount, paymentMethod) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const updatedWallet = {
        ...walletInfo,
        balance: walletInfo.balance + amount
      };
      resolve({
        success: true,
        message: "充值成功",
        data: updatedWallet
      });
    }, 800);
  });
};
const withdrawWallet = (amount, bankCardId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      if (amount <= walletInfo.balance) {
        const updatedWallet = {
          ...walletInfo,
          balance: walletInfo.balance - amount
        };
        resolve({
          success: true,
          message: "提现申请已提交，预计1-3个工作日到账",
          data: updatedWallet
        });
      } else {
        resolve({
          success: false,
          message: "余额不足",
          data: walletInfo
        });
      }
    }, 800);
  });
};
const addBankCard = (cardInfo) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newCard = {
        id: "card-" + Date.now(),
        ...cardInfo,
        isDefault: false
      };
      const updatedWallet = {
        ...walletInfo,
        bankCards: [...walletInfo.bankCards, newCard]
      };
      resolve({
        success: true,
        message: "银行卡添加成功",
        data: updatedWallet
      });
    }, 800);
  });
};
const setDefaultBankCard = (cardId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const updatedCards = walletInfo.bankCards.map((card) => ({
        ...card,
        isDefault: card.id === cardId
      }));
      const updatedWallet = {
        ...walletInfo,
        bankCards: updatedCards
      };
      resolve({
        success: true,
        message: "已设置为默认银行卡",
        data: updatedWallet
      });
    }, 500);
  });
};
exports.addBankCard = addBankCard;
exports.fetchWalletInfo = fetchWalletInfo;
exports.rechargeWallet = rechargeWallet;
exports.setDefaultBankCard = setDefaultBankCard;
exports.withdrawWallet = withdrawWallet;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/mock/payment/wallet.js.map
