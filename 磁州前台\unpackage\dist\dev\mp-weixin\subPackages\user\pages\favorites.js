"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  __name: "favorites",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const navbarHeight = common_vendor.ref(64);
    const tabsHeight = common_vendor.ref(44);
    const tabs = common_vendor.ref([
      { name: "收藏" },
      { name: "关注" }
    ]);
    const currentTab = common_vendor.ref(0);
    const collectList = common_vendor.ref([]);
    const followList = common_vendor.ref([]);
    const page = common_vendor.ref([1, 1]);
    common_vendor.ref(10);
    const hasMore = common_vendor.ref([true, true]);
    const refreshing = common_vendor.ref([false, false]);
    const tabLineStyle = common_vendor.computed(() => {
      return {
        transform: `translateX(${currentTab.value * (100 / tabs.value.length)}%)`,
        width: `${100 / tabs.value.length}%`
      };
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const switchTab = (index) => {
      currentTab.value = index;
    };
    const onSwiperChange = (e) => {
      currentTab.value = e.detail.current;
    };
    const loadCollectList = () => {
      setTimeout(() => {
        const mockData = Array.from({ length: 10 }, (_, i) => ({
          id: `collect_${page.value[0]}_${i}`,
          title: `收藏内容标题 ${page.value[0]}_${i}`,
          desc: "这是收藏内容的简短描述，可能包含多行文本内容...",
          image: "/static/images/service1.jpg",
          time: "2023-10-15",
          type: ["商家", "服务", "商品", "资讯"][Math.floor(Math.random() * 4)]
        }));
        if (page.value[0] === 1) {
          collectList.value = mockData;
        } else {
          collectList.value = [...collectList.value, ...mockData];
        }
        hasMore.value[0] = page.value[0] < 3;
        refreshing.value[0] = false;
      }, 500);
    };
    const loadFollowList = () => {
      setTimeout(() => {
        const mockData = Array.from({ length: 10 }, (_, i) => ({
          id: `follow_${page.value[1]}_${i}`,
          name: `用户名称 ${page.value[1]}_${i}`,
          avatar: "/static/images/avatar.png",
          info: "发布了20条内容，获得188人关注",
          isFollowed: true
        }));
        if (page.value[1] === 1) {
          followList.value = mockData;
        } else {
          followList.value = [...followList.value, ...mockData];
        }
        hasMore.value[1] = page.value[1] < 3;
        refreshing.value[1] = false;
      }, 500);
    };
    const loadMore = (tabIndex) => {
      if (!hasMore.value[tabIndex])
        return;
      page.value[tabIndex]++;
      if (tabIndex === 0) {
        loadCollectList();
      } else {
        loadFollowList();
      }
    };
    const onRefresh = (tabIndex) => {
      refreshing.value[tabIndex] = true;
      page.value[tabIndex] = 1;
      hasMore.value[tabIndex] = true;
      if (tabIndex === 0) {
        loadCollectList();
      } else {
        loadFollowList();
      }
    };
    const viewDetail = (item) => {
      common_vendor.index.navigateTo({
        url: `/pages/detail/detail?id=${item.id}&type=${item.type}`
      });
    };
    const viewUser = (item) => {
      common_vendor.index.navigateTo({
        url: `/pages/user/profile?id=${item.id}`
      });
    };
    const toggleFollow = (item) => {
      item.isFollowed = !item.isFollowed;
      common_vendor.index.showToast({
        title: item.isFollowed ? "已关注" : "已取消关注",
        icon: "none"
      });
    };
    common_vendor.onMounted(() => {
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight;
      navbarHeight.value = statusBarHeight.value + 44;
      loadCollectList();
      loadFollowList();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$5,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_vendor.f(tabs.value, (tab, index, i0) => {
          return {
            a: common_vendor.t(tab.name),
            b: index,
            c: currentTab.value === index ? 1 : "",
            d: common_vendor.o(($event) => switchTab(index), index)
          };
        }),
        e: common_vendor.s(tabLineStyle.value),
        f: navbarHeight.value + "px",
        g: collectList.value.length > 0
      }, collectList.value.length > 0 ? {
        h: common_vendor.f(collectList.value, (item, index, i0) => {
          return {
            a: item.image,
            b: common_vendor.t(item.title),
            c: common_vendor.t(item.desc),
            d: common_vendor.t(item.time),
            e: common_vendor.t(item.type),
            f: index,
            g: common_vendor.o(($event) => viewDetail(item), index)
          };
        })
      } : {
        i: common_assets._imports_1$3
      }, {
        j: collectList.value.length > 0 && !hasMore.value[0]
      }, collectList.value.length > 0 && !hasMore.value[0] ? {} : {}, {
        k: common_vendor.o(($event) => loadMore(0)),
        l: refreshing.value[0],
        m: common_vendor.o(($event) => onRefresh(0)),
        n: followList.value.length > 0
      }, followList.value.length > 0 ? {
        o: common_vendor.f(followList.value, (item, index, i0) => {
          return {
            a: item.avatar,
            b: common_vendor.t(item.name),
            c: common_vendor.t(item.info),
            d: common_vendor.t(item.isFollowed ? "已关注" : "关注"),
            e: common_vendor.o(($event) => toggleFollow(item), index),
            f: index,
            g: common_vendor.o(($event) => viewUser(item), index)
          };
        })
      } : {
        p: common_assets._imports_1$3
      }, {
        q: followList.value.length > 0 && !hasMore.value[1]
      }, followList.value.length > 0 && !hasMore.value[1] ? {} : {}, {
        r: common_vendor.o(($event) => loadMore(1)),
        s: refreshing.value[1],
        t: common_vendor.o(($event) => onRefresh(1)),
        v: currentTab.value,
        w: common_vendor.o(onSwiperChange),
        x: navbarHeight.value + tabsHeight.value + "px"
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/user/pages/favorites.js.map
