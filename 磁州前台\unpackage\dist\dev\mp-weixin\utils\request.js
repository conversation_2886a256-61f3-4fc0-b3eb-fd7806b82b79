"use strict";
const common_vendor = require("../common/vendor.js");
const BASE_URL = "http://localhost:8080/api";
const HTTP_METHOD = {
  GET: "GET",
  POST: "POST",
  PUT: "PUT",
  DELETE: "DELETE"
};
const ERROR_CODE_MAP = {
  400: "请求参数错误",
  401: "未授权，请重新登录",
  403: "拒绝访问",
  404: "请求的资源不存在",
  500: "服务器内部错误",
  502: "网关错误",
  503: "服务不可用",
  504: "网关超时"
};
let requestQueue = [];
const removeRequest = (config) => {
  const index = requestQueue.findIndex((item) => {
    return item.url === config.url && item.method === config.method && JSON.stringify(item.data) === JSON.stringify(config.data);
  });
  if (index !== -1) {
    requestQueue.splice(index, 1);
  }
};
const checkSameRequest = (config) => {
  const index = requestQueue.findIndex((item) => {
    return item.url === config.url && item.method === config.method && JSON.stringify(item.data) === JSON.stringify(config.data);
  });
  if (index !== -1) {
    return true;
  }
  requestQueue.push(config);
  return false;
};
const buildUrl = (url) => {
  if (url.startsWith("http")) {
    return url;
  }
  return `${BASE_URL}${url.startsWith("/") ? url : `/${url}`}`;
};
const getToken = () => {
  try {
    const userInfo = common_vendor.index.getStorageSync("user_info");
    return userInfo ? userInfo.token : "";
  } catch (e) {
    common_vendor.index.__f__("error", "at utils/request.js:91", "获取token失败", e);
    return "";
  }
};
const handleResponseError = (response) => {
  const { statusCode, data } = response;
  if (statusCode !== 200) {
    const message = ERROR_CODE_MAP[statusCode] || "网络异常，请稍后再试";
    if (statusCode === 401) {
      common_vendor.index.removeStorageSync("user_info");
      setTimeout(() => {
        common_vendor.index.navigateTo({
          url: "/pages/login/login"
        });
      }, 1500);
    }
    return Promise.reject({ message, response });
  }
  if (data && data.code !== 0 && data.code !== 200) {
    return Promise.reject({ message: data.message || "请求失败", response });
  }
  return data;
};
const request = (options = {}) => {
  const { url, method = HTTP_METHOD.GET, data = {}, header = {}, loading = true, retry = 1 } = options;
  const config = {
    url: buildUrl(url),
    method,
    data,
    header: {
      "Content-Type": "application/json",
      ...header
    }
  };
  const token = getToken();
  if (token) {
    config.header.Authorization = `Bearer ${token}`;
  }
  if (checkSameRequest(config)) {
    return Promise.reject({ message: "请勿重复请求" });
  }
  if (loading) {
    common_vendor.index.showLoading({
      title: "加载中...",
      mask: true
    });
  }
  return new Promise((resolve, reject) => {
    common_vendor.index.request({
      ...config,
      success: (response) => {
        try {
          const result = handleResponseError(response);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      },
      fail: (error) => {
        if (retry > 0 && (error.errMsg || "").includes("request:fail")) {
          setTimeout(() => {
            options.retry = retry - 1;
            resolve(request(options));
          }, 1e3);
          return;
        }
        reject({ message: "网络连接失败，请检查网络", error });
      },
      complete: () => {
        if (loading) {
          common_vendor.index.hideLoading();
        }
        removeRequest(config);
      }
    });
  });
};
const request$1 = {
  get: (url, data = {}, options = {}) => {
    return request({ url, method: HTTP_METHOD.GET, data, ...options });
  },
  post: (url, data = {}, options = {}) => {
    return request({ url, method: HTTP_METHOD.POST, data, ...options });
  },
  put: (url, data = {}, options = {}) => {
    return request({ url, method: HTTP_METHOD.PUT, data, ...options });
  },
  delete: (url, data = {}, options = {}) => {
    return request({ url, method: HTTP_METHOD.DELETE, data, ...options });
  },
  // 导出原始request方法，供特殊需求使用
  request
};
exports.request = request$1;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/request.js.map
