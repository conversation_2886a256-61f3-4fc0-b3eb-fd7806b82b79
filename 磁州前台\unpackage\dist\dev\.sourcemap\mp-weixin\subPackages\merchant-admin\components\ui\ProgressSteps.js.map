{"version": 3, "file": "ProgressSteps.js", "sources": ["subPackages/merchant-admin/components/ui/ProgressSteps.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7Avc3ViUGFja2FnZXMvbWVyY2hhbnQtYWRtaW4vY29tcG9uZW50cy91aS9Qcm9ncmVzc1N0ZXBzLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"progress-container\">\r\n    <view class=\"progress-steps\">\r\n      <view \r\n        v-for=\"(step, index) in steps\" \r\n        :key=\"index\"\r\n        class=\"step\" \r\n        :class=\"{active: currentStep >= index + 1, completed: currentStep > index + 1}\"\r\n      >\r\n        <view \r\n          v-if=\"index > 0\" \r\n          class=\"step-line\" \r\n          :class=\"{active: currentStep > index}\"\r\n        ></view>\r\n        \r\n        <view class=\"step-content\">\r\n          <view class=\"step-dot\">\r\n            <text v-if=\"currentStep <= index + 1\">{{ index + 1 }}</text>\r\n            <view class=\"check-icon\" v-else></view>\r\n          </view>\r\n          <text class=\"step-label\">{{ step }}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'ProgressSteps',\r\n  props: {\r\n    steps: {\r\n      type: Array,\r\n      required: true\r\n    },\r\n    currentStep: {\r\n      type: Number,\r\n      default: 1\r\n    }\r\n  },\r\n  computed: {\r\n    progressPercentage() {\r\n      return ((this.currentStep - 1) / (this.steps.length - 1)) * 100;\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.progress-container {\r\n  padding: 15px;\r\n  margin-bottom: 15px;\r\n  background-color: #fff;\r\n}\r\n\r\n.progress-steps {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  position: relative;\r\n}\r\n\r\n.step {\r\n  position: relative;\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  \r\n  &:first-child {\r\n    .step-content {\r\n      align-items: flex-start;\r\n    }\r\n  }\r\n  \r\n  &:last-child {\r\n    .step-content {\r\n      align-items: flex-end;\r\n    }\r\n  }\r\n}\r\n\r\n.step-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.step-dot {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 50%;\r\n  background-color: #E5E5E5;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 8px;\r\n  transition: all 0.3s;\r\n  \r\n  .active & {\r\n    background-color: #9040FF;\r\n    color: white;\r\n  }\r\n  \r\n  .completed & {\r\n    background-color: #9040FF;\r\n  }\r\n}\r\n\r\n.check-icon {\r\n  width: 10px;\r\n  height: 6px;\r\n  border-left: 2px solid white;\r\n  border-bottom: 2px solid white;\r\n  transform: rotate(-45deg) translate(1px, -1px);\r\n}\r\n\r\n.step-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n  transition: all 0.3s;\r\n  \r\n  .active &, .completed & {\r\n    color: #333;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n.step-line {\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  top: 12px;\r\n  height: 2px;\r\n  background-color: #E5E5E5;\r\n  z-index: 1;\r\n  \r\n  &.active {\r\n    background-color: #9040FF;\r\n  }\r\n}\r\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin/components/ui/ProgressSteps.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;AA4BA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,IACX;AAAA,IACD,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,qBAAqB;AACnB,cAAS,KAAK,cAAc,MAAM,KAAK,MAAM,SAAS,KAAM;AAAA,IAC9D;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;AC5CA,GAAG,gBAAgB,SAAS;"}