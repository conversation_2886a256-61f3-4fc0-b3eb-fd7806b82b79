/**
 * 更新merchant模块路径引用脚本
 * 将原来的 /pages/merchant/ 路径更新为 /subPackages/merchant-plugin/pages/
 */

const fs = require('fs');
const path = require('path');

// 递归获取目录下的所有文件
function getAllFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !filePath.includes('node_modules') && !filePath.includes('unpackage')) {
      fileList = getAllFiles(filePath, fileList);
    } else if (stat.isFile() && (file.endsWith('.vue') || file.endsWith('.js'))) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// 更新文件内容中的路径引用
function updatePathsInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    
    // 更新路径
    content = content.replace(/['"]\/pages\/merchant\//g, '"/subPackages/merchant-plugin/pages/');
    content = content.replace(/['"]\.\.\/merchant\//g, '"../../../subPackages/merchant-plugin/pages/');
    
    // 如果内容有变化，写回文件
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`更新文件: ${filePath}`);
    }
  } catch (err) {
    console.error(`处理文件 ${filePath} 时出错:`, err);
  }
}

// 主函数
function main() {
  console.log('开始更新merchant模块路径引用...');
  
  const rootDir = path.resolve(__dirname, '..');
  const allFiles = getAllFiles(rootDir);
  
  let updatedCount = 0;
  
  allFiles.forEach(file => {
    try {
      updatePathsInFile(file);
      updatedCount++;
    } catch (err) {
      console.error(`处理文件 ${file} 时出错:`, err);
    }
  });
  
  console.log(`处理完成! 共处理 ${allFiles.length} 个文件，更新了 ${updatedCount} 个文件的引用。`);
}

main(); 