"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      settings: {
        autoApprove: true,
        receiveNotification: true,
        expiryNotification: false,
        maxAmount: 200,
        dailyLimit: 100,
        userLimit: 3,
        templateCount: 5,
        defaultTemplate: {
          id: 1,
          name: "默认红包",
          preview: "/static/images/redpacket-template-default.png"
        },
        blacklistCount: 7
      },
      showLimitPopup: false,
      currentLimit: "",
      currentLimitTitle: "",
      tempLimitValue: 0
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    showHelp() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/redpacket/guide"
      });
    },
    toggleAutoApprove(e) {
      this.settings.autoApprove = e.detail.value;
      this.saveSettings();
    },
    toggleReceiveNotification(e) {
      this.settings.receiveNotification = e.detail.value;
      this.saveSettings();
    },
    toggleExpiryNotification(e) {
      this.settings.expiryNotification = e.detail.value;
      this.saveSettings();
    },
    showLimitPicker(limitType) {
      this.currentLimit = limitType;
      this.tempLimitValue = this.settings[limitType];
      if (limitType === "dailyLimit") {
        this.currentLimitTitle = "每日发放上限";
      } else if (limitType === "userLimit") {
        this.currentLimitTitle = "用户领取限制";
      }
      this.showLimitPopup = true;
    },
    cancelLimitPicker() {
      this.showLimitPopup = false;
    },
    confirmLimitPicker() {
      this.settings[this.currentLimit] = this.tempLimitValue;
      this.saveSettings();
      this.showLimitPopup = false;
    },
    decreaseLimit() {
      if (this.tempLimitValue > 1) {
        this.tempLimitValue--;
      }
    },
    increaseLimit() {
      this.tempLimitValue++;
    },
    navigateTo(url) {
      common_vendor.index.navigateTo({
        url
      });
    },
    clearCache() {
      common_vendor.index.showLoading({
        title: "清除中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "缓存已清除",
          icon: "success"
        });
      }, 1e3);
    },
    saveSettings() {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-marketing/pages/marketing/redpacket/settings.vue:316", "保存设置", this.settings);
      common_vendor.index.showToast({
        title: "设置已保存",
        icon: "success",
        duration: 1e3
      });
    }
  }
};
if (!Array) {
  const _component_line = common_vendor.resolveComponent("line");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_line + _component_svg)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: $data.settings.autoApprove,
    d: common_vendor.o((...args) => $options.toggleAutoApprove && $options.toggleAutoApprove(...args)),
    e: $data.settings.receiveNotification,
    f: common_vendor.o((...args) => $options.toggleReceiveNotification && $options.toggleReceiveNotification(...args)),
    g: $data.settings.expiryNotification,
    h: common_vendor.o((...args) => $options.toggleExpiryNotification && $options.toggleExpiryNotification(...args)),
    i: common_vendor.t($data.settings.maxAmount),
    j: common_vendor.t($data.settings.dailyLimit),
    k: common_vendor.o(($event) => $options.showLimitPicker("dailyLimit")),
    l: common_vendor.t($data.settings.userLimit),
    m: common_vendor.o(($event) => $options.showLimitPicker("userLimit")),
    n: common_vendor.t($data.settings.templateCount),
    o: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-admin-marketing/pages/marketing/redpacket/template")),
    p: "url(" + $data.settings.defaultTemplate.preview + ")",
    q: common_vendor.t($data.settings.blacklistCount),
    r: common_vendor.o((...args) => $options.clearCache && $options.clearCache(...args)),
    s: $data.showLimitPopup
  }, $data.showLimitPopup ? {
    t: common_vendor.o((...args) => $options.cancelLimitPicker && $options.cancelLimitPicker(...args))
  } : {}, {
    v: $data.showLimitPopup
  }, $data.showLimitPopup ? {
    w: common_vendor.t($data.currentLimitTitle),
    x: common_vendor.o((...args) => $options.cancelLimitPicker && $options.cancelLimitPicker(...args)),
    y: common_vendor.p({
      x1: "5",
      y1: "12",
      x2: "19",
      y2: "12"
    }),
    z: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "24",
      height: "24",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    A: common_vendor.o((...args) => $options.decreaseLimit && $options.decreaseLimit(...args)),
    B: common_vendor.t($data.tempLimitValue),
    C: common_vendor.p({
      x1: "12",
      y1: "5",
      x2: "12",
      y2: "19"
    }),
    D: common_vendor.p({
      x1: "5",
      y1: "12",
      x2: "19",
      y2: "12"
    }),
    E: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "24",
      height: "24",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    F: common_vendor.o((...args) => $options.increaseLimit && $options.increaseLimit(...args)),
    G: common_vendor.o((...args) => $options.cancelLimitPicker && $options.cancelLimitPicker(...args)),
    H: common_vendor.o((...args) => $options.confirmLimitPicker && $options.confirmLimitPicker(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-f049e702"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/redpacket/settings.js.map
