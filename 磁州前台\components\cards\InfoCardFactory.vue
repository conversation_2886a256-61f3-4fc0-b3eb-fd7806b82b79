<template>
  <!-- 使用条件渲染替代动态组件 -->
  <JobCard 
    v-if="cardType === 'JobCard'" 
    :item="item" 
  />
  <HouseRentCard 
    v-else-if="cardType === 'HouseRentCard'" 
    :item="item" 
  />
  <SecondHandCard 
    v-else-if="cardType === 'SecondHandCard'" 
    :item="item" 
  />
  <BaseInfoCard 
    v-else 
    :item="item" 
  />
</template>

<script setup>
import { computed } from 'vue';
import BaseInfoCard from './BaseInfoCard.vue';
import JobCard from './JobCard.vue';
import HouseRentCard from './HouseRentCard.vue';
import SecondHandCard from './SecondHandCard.vue';

const props = defineProps({
  item: {
    type: Object,
    required: true
  }
});

const cardType = computed(() => {
  const categoryMap = {
    '招聘信息': 'JobCard',
    '求职信息': 'JobCard',
    '房屋出租': 'HouseRentCard',
    '房屋出售': 'HouseRentCard',
    '二手闲置': 'SecondHandCard',
    '二手车辆': 'SecondHandCard',
    '到家服务': 'BaseInfoCard',
    '寻找服务': 'BaseInfoCard',
    '生意转让': 'BaseInfoCard',
    '宠物信息': 'BaseInfoCard',
    '商家活动': 'BaseInfoCard',
    '婚恋交友': 'BaseInfoCard',
    '车辆服务': 'BaseInfoCard',
    '磁州拼车': 'BaseInfoCard',
    '教育培训': 'BaseInfoCard',
    '其他服务': 'BaseInfoCard'
  };
  
  // 根据分类返回对应的卡片组件类型
  return categoryMap[props.item.category] || 'BaseInfoCard';
});
</script> 