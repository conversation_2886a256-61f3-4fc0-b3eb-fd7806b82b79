"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const isSearchVisible = common_vendor.ref(false);
    const searchKeyword = common_vendor.ref("");
    const currentTab = common_vendor.ref(0);
    const tabs = common_vendor.ref(["全部", "本地", "长途", "通勤", "我的群"]);
    const isRefreshing = common_vendor.ref(false);
    const isLoading = common_vendor.ref(false);
    const hasMore = common_vendor.ref(true);
    const page = common_vendor.ref(1);
    const activeTab = common_vendor.ref("groups");
    const showQRCode = common_vendor.ref(false);
    const currentGroup = common_vendor.ref(null);
    const showPublishPopup = common_vendor.ref(false);
    const statusBarHeight = common_vendor.ref(20);
    const isAdmin = common_vendor.ref(false);
    const groups = common_vendor.ref([
      {
        id: 1,
        name: "磁州-邯郸拼车群",
        avatar: "/static/images/tabbar/group-avatar1.png",
        memberCount: 128,
        type: "local",
        typeText: "本地",
        description: "磁州到邯郸日常拼车，安全出行，拼车省钱",
        tags: ["安全", "准时", "舒适"],
        routeInfo: "磁州 → 邯郸",
        lastActiveTime: "10分钟前",
        isJoined: true,
        qrcode: "/static/images/tabbar/qrcode1.png"
      },
      {
        id: 2,
        name: "磁州-石家庄通勤群",
        avatar: "/static/images/tabbar/group-avatar2.png",
        memberCount: 86,
        type: "commute",
        typeText: "通勤",
        description: "工作日通勤拼车，每天准时发车，欢迎加入",
        tags: ["通勤", "准时", "固定"],
        routeInfo: "磁州 → 石家庄",
        lastActiveTime: "30分钟前",
        isJoined: false,
        qrcode: "/static/images/tabbar/qrcode2.png"
      },
      {
        id: 3,
        name: "磁州-北京长途拼车",
        avatar: "/static/images/tabbar/group-avatar3.png",
        memberCount: 64,
        type: "longDistance",
        typeText: "长途",
        description: "周末和节假日磁州到北京长途拼车，舒适安全",
        tags: ["长途", "舒适", "周末"],
        routeInfo: "磁州 → 北京",
        lastActiveTime: "2小时前",
        isJoined: false,
        qrcode: "/static/images/tabbar/qrcode3.png"
      },
      {
        id: 4,
        name: "磁州本地拼车群",
        avatar: "/static/images/tabbar/group-avatar4.png",
        memberCount: 256,
        type: "local",
        typeText: "本地",
        description: "磁州城区及周边拼车，随时发车，方便快捷",
        tags: ["本地", "灵活", "快捷"],
        routeInfo: "磁州城区",
        lastActiveTime: "1小时前",
        isJoined: true,
        qrcode: "/static/images/tabbar/qrcode4.png"
      }
    ]);
    const filteredGroups = common_vendor.computed(() => {
      let result = groups.value;
      if (currentTab.value > 0) {
        const tabType = ["", "local", "longDistance", "commute", ""][currentTab.value];
        if (currentTab.value === 4) {
          result = result.filter((group) => group.isJoined);
        } else if (tabType) {
          result = result.filter((group) => group.type === tabType);
        }
      }
      if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase();
        result = result.filter(
          (group) => {
            var _a;
            return group.name.toLowerCase().includes(keyword) || group.description.toLowerCase().includes(keyword) || ((_a = group.routeInfo) == null ? void 0 : _a.toLowerCase().includes(keyword)) || group.tags.some((tag) => tag.toLowerCase().includes(keyword));
          }
        );
      }
      return result;
    });
    common_vendor.onMounted(() => {
      loadGroups();
      common_vendor.index.hideTabBar();
      const systemInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = systemInfo.statusBarHeight;
      const userInfo = common_vendor.index.getStorageSync("userInfo") || {};
      isAdmin.value = !!userInfo.isAdmin;
    });
    common_vendor.onShow(() => {
      common_vendor.index.hideTabBar();
    });
    common_vendor.onHide(() => {
    });
    common_vendor.onUnload(() => {
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2];
      if (!prevPage || prevPage.route !== "carpool-package/pages/carpool-main/index") {
        common_vendor.index.showTabBar();
      }
    });
    const goBack = () => {
      common_vendor.index.navigateBack({
        fail: () => {
          common_vendor.index.navigateTo({
            url: "/carpool-package/pages/carpool-main/index"
          });
        }
      });
    };
    const showSearch = () => {
      isSearchVisible.value = true;
    };
    const hideSearch = () => {
      isSearchVisible.value = false;
      searchKeyword.value = "";
    };
    const switchTab = (index) => {
      currentTab.value = index;
    };
    const searchGroups = () => {
      common_vendor.index.__f__("log", "at carpool-package/pages/carpool/groups/index.vue:397", "搜索关键词：", searchKeyword.value);
    };
    const loadGroups = () => {
      isLoading.value = true;
      setTimeout(() => {
        isLoading.value = false;
        hasMore.value = page.value < 3;
        page.value++;
      }, 1e3);
    };
    const onRefresh = () => {
      isRefreshing.value = true;
      page.value = 1;
      setTimeout(() => {
        isRefreshing.value = false;
        common_vendor.index.showToast({
          title: "刷新成功",
          icon: "success"
        });
      }, 1e3);
    };
    const loadMore = () => {
      if (hasMore.value && !isLoading.value) {
        loadGroups();
      }
    };
    const viewGroupDetail = (group) => {
      currentGroup.value = group;
      showQRCode.value = true;
    };
    const hideQRCode = () => {
      showQRCode.value = false;
      currentGroup.value = null;
    };
    const saveQRCode = () => {
      if (currentGroup.value && currentGroup.value.qrcode) {
        common_vendor.index.saveImageToPhotosAlbum({
          filePath: currentGroup.value.qrcode,
          success: () => {
            common_vendor.index.showToast({
              title: "二维码已保存到相册",
              icon: "success"
            });
          },
          fail: () => {
            common_vendor.index.showToast({
              title: "保存失败，请检查权限",
              icon: "none"
            });
          }
        });
      }
    };
    const shareQRCode = () => {
      common_vendor.index.share({
        provider: "weixin",
        scene: "WXSceneSession",
        type: 2,
        imageUrl: currentGroup.value ? currentGroup.value.qrcode : "",
        title: `邀请您加入${currentGroup.value ? currentGroup.value.name : "拼车群"}`,
        success: function() {
          common_vendor.index.showToast({
            title: "分享成功",
            icon: "success"
          });
        },
        fail: function() {
          common_vendor.index.showToast({
            title: "分享失败",
            icon: "none"
          });
        }
      });
    };
    const createGroup = () => {
      const userInfo = common_vendor.index.getStorageSync("userInfo") || {};
      if (userInfo.isAdmin) {
        common_vendor.index.navigateTo({
          url: "/pages/carpool/groups/create"
        });
      } else {
        common_vendor.index.showModal({
          title: "提示",
          content: "只有管理员可以创建拼车群",
          showCancel: false
        });
      }
    };
    const navigateToPage = (page2) => {
      if (page2 === "groups")
        return;
      activeTab.value = page2;
      switch (page2) {
        case "home":
          common_vendor.index.showTabBar();
          setTimeout(() => {
            common_vendor.index.switchTab({
              url: "/pages/index/index"
            });
          }, 50);
          break;
        case "carpool":
          common_vendor.index.hideTabBar();
          common_vendor.index.navigateTo({
            url: "/carpool-package/pages/carpool-main/index"
          });
          break;
        case "my":
          common_vendor.index.hideTabBar();
          common_vendor.index.navigateTo({
            url: "/carpool-package/pages/carpool/my/index"
          });
          break;
      }
    };
    const publishCarpool = () => {
      showPublishPopup.value = true;
      activeTab.value = "publish";
    };
    const navigateToPublish = (type) => {
      showPublishPopup.value = false;
      let url = "";
      switch (type) {
        case "people-to-car":
          url = "/carpool-package/pages/carpool/publish/people-to-car";
          break;
        case "car-to-people":
          url = "/carpool-package/pages/carpool/publish/car-to-people";
          break;
        case "goods-to-car":
          url = "/carpool-package/pages/carpool/publish/goods-to-car";
          break;
        case "car-to-goods":
          url = "/carpool-package/pages/carpool/publish/car-to-goods";
          break;
        default:
          url = "/carpool-package/pages/carpool/publish/people-to-car";
      }
      common_vendor.index.navigateTo({
        url
      });
    };
    const closePublishPopup = () => {
      showPublishPopup.value = false;
      activeTab.value = "groups";
    };
    const toggleDesc = (group) => {
      group._descExpand = !group._descExpand;
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: common_assets._imports_1$31,
        d: common_vendor.o(showSearch),
        e: statusBarHeight.value + "px",
        f: isSearchVisible.value
      }, isSearchVisible.value ? {
        g: common_assets._imports_1$31,
        h: common_vendor.o(searchGroups),
        i: searchKeyword.value,
        j: common_vendor.o(($event) => searchKeyword.value = $event.detail.value),
        k: common_vendor.o(hideSearch)
      } : {}, {
        l: common_vendor.f(tabs.value, (tab, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(tab),
            b: currentTab.value === index
          }, currentTab.value === index ? {} : {}, {
            c: index,
            d: currentTab.value === index ? 1 : "",
            e: common_vendor.o(($event) => switchTab(index), index)
          });
        }),
        m: common_vendor.f(filteredGroups.value, (group, index, i0) => {
          return common_vendor.e({
            a: group.avatar,
            b: common_vendor.t(group.name),
            c: common_vendor.t(group.memberCount),
            d: common_vendor.t(group.typeText),
            e: common_vendor.n(group.type),
            f: common_vendor.o(($event) => viewGroupDetail(group), index),
            g: !group._descExpand && group.description.length > 36
          }, !group._descExpand && group.description.length > 36 ? {
            h: common_vendor.t(group.description.slice(0, 36) + "...")
          } : {
            i: common_vendor.t(group.description)
          }, {
            j: group.description.length > 36
          }, group.description.length > 36 ? {
            k: common_vendor.t(group._descExpand ? "收起" : "展开"),
            l: common_vendor.o(($event) => toggleDesc(group), index)
          } : {}, {
            m: common_vendor.f(group.tags, (tag, tagIndex, i1) => {
              return {
                a: common_vendor.t(tag),
                b: tagIndex
              };
            }),
            n: group.routeInfo
          }, group.routeInfo ? {
            o: common_assets._imports_2$27,
            p: common_vendor.t(group.routeInfo)
          } : {}, {
            q: common_vendor.t(group.lastActiveTime),
            r: index,
            s: common_vendor.o(($event) => viewGroupDetail(group), index)
          });
        }),
        n: isLoading.value
      }, isLoading.value ? {} : {}, {
        o: filteredGroups.value.length === 0 && !isLoading.value
      }, filteredGroups.value.length === 0 && !isLoading.value ? common_vendor.e({
        p: common_assets._imports_3$25,
        q: isAdmin.value
      }, isAdmin.value ? {
        r: common_vendor.o(createGroup)
      } : {}) : {}, {
        s: isRefreshing.value,
        t: common_vendor.o(onRefresh),
        v: common_vendor.o(loadMore),
        w: isAdmin.value
      }, isAdmin.value ? {
        x: common_assets._imports_2$26,
        y: common_vendor.o(createGroup)
      } : {}, {
        z: activeTab.value === "home" ? "/static/images/tabbar/p首页选中.png" : "/static/images/tabbar/p首页.png",
        A: activeTab.value === "home" ? 1 : "",
        B: activeTab.value === "home" ? 1 : "",
        C: common_vendor.o(($event) => navigateToPage("home")),
        D: activeTab.value === "carpool" ? "/static/images/tabbar/p拼车选中.png" : "/static/images/tabbar/p拼车.png",
        E: activeTab.value === "carpool" ? 1 : "",
        F: activeTab.value === "carpool" ? 1 : "",
        G: common_vendor.o(($event) => navigateToPage("carpool")),
        H: activeTab.value === "publish" ? "/static/images/tabbar/p发布选中.png" : "/static/images/tabbar/p发布.png",
        I: activeTab.value === "publish" ? 1 : "",
        J: activeTab.value === "publish" ? 1 : "",
        K: common_vendor.o(publishCarpool),
        L: common_assets._imports_5$16,
        M: activeTab.value === "my" ? "/static/images/tabbar/p我的选中.png" : "/static/images/tabbar/p我的.png",
        N: activeTab.value === "my" ? 1 : "",
        O: activeTab.value === "my" ? 1 : "",
        P: common_vendor.o(($event) => navigateToPage("my")),
        Q: showQRCode.value
      }, showQRCode.value ? common_vendor.e({
        R: common_vendor.t(currentGroup.value ? currentGroup.value.name : "拼车群"),
        S: common_vendor.o(hideQRCode),
        T: currentGroup.value
      }, currentGroup.value ? {
        U: common_vendor.p({
          d: "M3 12h18M3 6h18M3 18h18"
        }),
        V: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "#0A84FF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        W: common_vendor.t(currentGroup.value.routeInfo),
        X: common_vendor.f(currentGroup.value.tags, (tag, tagIndex, i0) => {
          return {
            a: common_vendor.t(tag),
            b: tagIndex
          };
        }),
        Y: common_vendor.t(currentGroup.value.description),
        Z: common_vendor.t(currentGroup.value.memberCount),
        aa: common_vendor.t(currentGroup.value.lastActiveTime)
      } : {}, {
        ab: currentGroup.value ? currentGroup.value.qrcode || "/static/images/tabbar/default-qrcode.png" : "/static/images/tabbar/default-qrcode.png",
        ac: common_vendor.o(saveQRCode),
        ad: common_vendor.o(shareQRCode),
        ae: common_vendor.o(() => {
        }),
        af: common_vendor.o(hideQRCode)
      }) : {}, {
        ag: showPublishPopup.value
      }, showPublishPopup.value ? {
        ah: common_vendor.o(closePublishPopup),
        ai: common_assets._imports_6$13,
        aj: common_vendor.o(($event) => navigateToPublish("people-to-car")),
        ak: common_assets._imports_7$8,
        al: common_vendor.o(($event) => navigateToPublish("car-to-people")),
        am: common_assets._imports_8$4,
        an: common_vendor.o(($event) => navigateToPublish("goods-to-car")),
        ao: common_assets._imports_9$6,
        ap: common_vendor.o(($event) => navigateToPublish("car-to-goods")),
        aq: common_vendor.o(() => {
        }),
        ar: common_vendor.o(closePublishPopup)
      } : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/carpool-package/pages/carpool/groups/index.js.map
