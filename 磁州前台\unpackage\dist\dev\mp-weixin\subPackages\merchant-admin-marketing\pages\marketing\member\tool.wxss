
/* 工具页面样式开始 */
.tool-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #8E2DE2, #4A00E0);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(74, 0, 224, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
}

/* 工具内容样式 */
.tool-content {
  padding: 20rpx;
}
.tool-header {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.tool-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}
.icon-image {
  width: 50rpx;
  height: 50rpx;
}
.tool-info {
  flex: 1;
}
.tool-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}
.tool-desc {
  font-size: 24rpx;
  color: #999;
}
.section-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 积分概览 */
.stats-row {
  display: flex;
  justify-content: space-between;
}
.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.stat-label {
  font-size: 24rpx;
  color: #999;
}

/* 表单样式 */
.form-item {
  margin-bottom: 20rpx;
}
.form-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}
.form-input-group {
  display: flex;
  align-items: center;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  overflow: hidden;
}
.form-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}
.input-suffix {
  padding: 0 20rpx;
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  height: 80rpx;
  line-height: 80rpx;
}
.save-btn {
  background: #4A00E0;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  margin-top: 30rpx;
}

/* 任务列表 */
.task-list {
  margin-bottom: 20rpx;
}
.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.task-item:last-child {
  border-bottom: none;
}
.task-info {
  flex: 1;
}
.task-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}
.task-reward {
  font-size: 24rpx;
  color: #FF9500;
}

/* 卡券列表 */
.coupon-list {
  margin-bottom: 20rpx;
}
.coupon-item {
  background: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.coupon-info {
  flex: 1;
}
.coupon-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}
.coupon-value {
  font-size: 26rpx;
  color: #FF6B22;
  font-weight: 600;
  margin-bottom: 8rpx;
  display: block;
}
.coupon-validity {
  font-size: 24rpx;
  color: #999;
}
.coupon-actions {
  display: flex;
  gap: 15rpx;
}
.action-btn {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
}
.action-btn.edit {
  background: rgba(74, 0, 224, 0.1);
  color: #4A00E0;
}
.action-btn.delete {
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}

/* 活动列表 */
.activity-list {
  margin-bottom: 20rpx;
}
.activity-item {
  background: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.activity-info {
  flex: 1;
}
.activity-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}
.activity-time {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}
.activity-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  display: inline-block;
}
.activity-status.upcoming {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}
.activity-status.active {
  background: rgba(0, 122, 255, 0.1);
  color: #007AFF;
}
.activity-status.ended {
  background: rgba(142, 142, 147, 0.1);
  color: #8E8E93;
}
.activity-actions {
  display: flex;
  gap: 15rpx;
}

/* 添加按钮 */
.add-btn {
  background: #4A00E0;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  margin-top: 20rpx;
}

/* 空状态 */
.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(74, 0, 224, 0.1);
  border-left: 4rpx solid #4A00E0;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}
@keyframes spin {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.loading-text {
  font-size: 28rpx;
  color: #999;
}
/* 工具页面样式结束 */
