/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-7b3d7571, html.data-v-7b3d7571, #app.data-v-7b3d7571, .index-container.data-v-7b3d7571 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.promotion-container.data-v-7b3d7571 {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}
.navbar.data-v-7b3d7571 {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #3846cd, #2c3aa0);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(56, 70, 205, 0.15);
}
.navbar-back.data-v-7b3d7571 {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon.data-v-7b3d7571 {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title.data-v-7b3d7571 {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right.data-v-7b3d7571 {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon.data-v-7b3d7571 {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 海报预览部分 */
.poster-preview-section.data-v-7b3d7571 {
  margin: 16px;
}
.preview-card.data-v-7b3d7571 {
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 16px;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.poster-image.data-v-7b3d7571 {
  width: 100%;
  max-width: 300px;
  height: 400px;
  border-radius: 10px;
  background-color: #f5f5f5;
  object-fit: contain;
}
.preview-footer.data-v-7b3d7571 {
  margin-top: 12px;
  text-align: center;
}
.preview-text.data-v-7b3d7571 {
  font-size: 14px;
  color: #666;
}
.action-buttons.data-v-7b3d7571 {
  display: flex;
  gap: 12px;
}
.action-button.data-v-7b3d7571 {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  border: none;
}
.action-button.save.data-v-7b3d7571 {
  background: linear-gradient(135deg, #34C759, #32D74B);
  color: #FFFFFF;
}
.action-button.share.data-v-7b3d7571 {
  background: linear-gradient(135deg, #3846cd, #2c3aa0);
  color: #FFFFFF;
}
.button-icon.data-v-7b3d7571 {
  margin-right: 6px;
}

/* 自定义选项部分 */
.customization-section.data-v-7b3d7571 {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
}
.option-group.data-v-7b3d7571 {
  margin-bottom: 20px;
}
.option-group.data-v-7b3d7571:last-child {
  margin-bottom: 0;
}
.option-title.data-v-7b3d7571 {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  display: block;
}
.template-scroll.data-v-7b3d7571 {
  width: 100%;
  white-space: nowrap;
}
.template-list.data-v-7b3d7571 {
  display: flex;
  padding-bottom: 8px;
}
.template-item.data-v-7b3d7571 {
  width: 80px;
  height: 120px;
  margin-right: 12px;
  border-radius: 10px;
  overflow: hidden;
  border: 2px solid transparent;
  position: relative;
  transition: all 0.3s ease;
  flex-shrink: 0;
}
.template-item.active.data-v-7b3d7571 {
  border-color: #3846cd;
  transform: scale(1.05);
}
.template-thumb.data-v-7b3d7571 {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.theme-options.data-v-7b3d7571 {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}
.theme-option.data-v-7b3d7571 {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  border: 2px solid transparent;
  position: relative;
  transition: all 0.3s ease;
}
.theme-option.active.data-v-7b3d7571 {
  border-color: #3846cd;
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(56, 70, 205, 0.2);
}
.theme-check.data-v-7b3d7571 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.toggle-option.data-v-7b3d7571 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #F0F0F0;
}
.toggle-option.data-v-7b3d7571:last-child {
  border-bottom: none;
}
.toggle-label.data-v-7b3d7571 {
  font-size: 14px;
  color: #333;
}
.custom-input.data-v-7b3d7571 {
  width: 100%;
  height: 44px;
  background-color: #F5F7FA;
  border-radius: 12px;
  padding: 0 16px;
  font-size: 14px;
  color: #333;
  border: 1px solid transparent;
}
.custom-input.data-v-7b3d7571:focus {
  border-color: #3846cd;
  background-color: #FFFFFF;
}

/* 内容预览 */
.content-preview.data-v-7b3d7571 {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
}
.preview-info.data-v-7b3d7571 {
  padding: 0;
}
.preview-title.data-v-7b3d7571 {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}
.preview-detail.data-v-7b3d7571 {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
}
.detail-item.data-v-7b3d7571 {
  font-size: 14px;
  color: #666;
  margin-right: 20px;
  margin-bottom: 5px;
}
.price.data-v-7b3d7571 {
  color: #ff4400;
  font-weight: bold;
}
.original-price.data-v-7b3d7571 {
  color: #999;
  text-decoration: line-through;
}

/* 佣金信息 */
.commission-info.data-v-7b3d7571 {
  margin: 16px;
}
.commission-card.data-v-7b3d7571 {
  background: linear-gradient(135deg, #fff8f0, #fff5e6);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(255, 153, 0, 0.1);
}
.commission-header.data-v-7b3d7571 {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.commission-icon.data-v-7b3d7571 {
  width: 24px;
  height: 24px;
  margin-right: 10px;
}
.commission-title.data-v-7b3d7571 {
  font-size: 16px;
  font-weight: bold;
  color: #ff9900;
}
.commission-content.data-v-7b3d7571 {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
}
.commission-rate.data-v-7b3d7571, .commission-estimate.data-v-7b3d7571 {
  font-size: 14px;
  color: #ff6600;
}