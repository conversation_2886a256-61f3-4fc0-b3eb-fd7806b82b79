{"version": 3, "file": "index.js", "sources": ["subPackages/franchise/pages/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcZnJhbmNoaXNlXHBhZ2VzXGluZGV4LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"franchise-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\">\n      <view class=\"navbar-left\" @click=\"goBack\">\n        <image src=\"/static/images/tabbar/最新返回键.png\" class=\"back-icon\"></image>\n      </view>\n      <view class=\"navbar-title\">区域加盟</view>\n      <view class=\"navbar-right\">\n        <!-- 预留位置与发布页面保持一致 -->\n      </view>\n    </view>\n    \n    <!-- 添加顶部安全区域 -->\n    <view class=\"safe-area-top\"></view>\n    \n    <!-- 加盟介绍卡片 -->\n    <view class=\"franchise-card intro-card\">\n      <view class=\"intro-header\">\n        <view class=\"intro-title\">\n          <text class=\"title-text\">区域加盟招募</text>\n          <view class=\"title-tag\">招募中</view>\n        </view>\n        <view class=\"intro-subtitle\">成为区域合伙人，共享千亿市场</view>\n      </view>\n      <view class=\"intro-content\">\n        <view class=\"intro-item\">\n          <image class=\"intro-icon\" src=\"/static/images/tabbar/合伙人.png\" mode=\"aspectFit\"></image>\n          <view class=\"intro-info\">\n            <view class=\"intro-name\">专属收益</view>\n            <view class=\"intro-desc\">区域内30%服务收入分成</view>\n          </view>\n        </view>\n        <view class=\"intro-item\">\n          <image class=\"intro-icon\" src=\"/static/images/tabbar/商家信息.png\" mode=\"aspectFit\"></image>\n          <view class=\"intro-info\">\n            <view class=\"intro-name\">运营支持</view>\n            <view class=\"intro-desc\">总部提供全方位技术与营销支持</view>\n          </view>\n        </view>\n        <view class=\"intro-item\">\n          <image class=\"intro-icon\" src=\"/static/images/tabbar/商家活动.png\" mode=\"aspectFit\"></image>\n          <view class=\"intro-info\">\n            <view class=\"intro-name\">资源赋能</view>\n            <view class=\"intro-desc\">商家资源与流量扶持</view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 区域选择卡片 -->\n    <view class=\"franchise-card region-card\">\n      <view class=\"region-header\">\n        <text class=\"region-title\">选择加盟区域</text>\n        <text class=\"region-subtitle\">每个县区限3个加盟名额</text>\n      </view>\n      \n      <!-- 区域选择器 -->\n      <view class=\"region-selector\">\n        <view class=\"selector-row\">\n          <view class=\"selector-item\">\n            <view class=\"selector-label\">省份</view>\n            <picker mode=\"selector\" :range=\"provinces\" @change=\"provinceChange\" class=\"selector-picker\">\n              <view class=\"picker-value\">\n                <text>{{ currentProvince || '请选择' }}</text>\n                <text class=\"cuIcon-unfold\"></text>\n              </view>\n            </picker>\n          </view>\n          <view class=\"selector-item\">\n            <view class=\"selector-label\">城市</view>\n            <picker mode=\"selector\" :range=\"cities\" @change=\"cityChange\" :disabled=\"!currentProvince\" class=\"selector-picker\">\n              <view class=\"picker-value\" :class=\"{'disabled': !currentProvince}\">\n                <text>{{ currentCity || '请选择' }}</text>\n                <text class=\"cuIcon-unfold\"></text>\n              </view>\n            </picker>\n          </view>\n        </view>\n        <view class=\"selector-row\">\n          <view class=\"selector-item full-width\">\n            <view class=\"selector-label\">区/县</view>\n            <picker mode=\"selector\" :range=\"districts\" @change=\"districtChange\" :disabled=\"!currentCity\" class=\"selector-picker\">\n              <view class=\"picker-value\" :class=\"{'disabled': !currentCity}\">\n                <text>{{ currentDistrict || '请选择' }}</text>\n                <text class=\"cuIcon-unfold\"></text>\n              </view>\n            </picker>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 区域地图 -->\n      <view class=\"region-map\">\n        <view class=\"map-title\">\n          <view class=\"map-title-dot\"></view>\n          <text>区域地图</text>\n        </view>\n        <view class=\"map-image\">\n          <text v-if=\"!selectedRegion\">请先选择区域查看地图</text>\n          <text v-else>{{ selectedRegion }} 区域地图</text>\n        </view>\n      </view>\n      \n      <!-- 状态指示器 -->\n      <view class=\"status-indicators\">\n        <view class=\"status-item\">\n          <view class=\"status-dot available-dot\"></view>\n          <text class=\"status-text\">可申请</text>\n        </view>\n        <view class=\"status-item\">\n          <view class=\"status-dot pending-dot\"></view>\n          <text class=\"status-text\">审核中</text>\n        </view>\n        <view class=\"status-item\">\n          <view class=\"status-dot occupied-dot\"></view>\n          <text class=\"status-text\">已占用</text>\n        </view>\n      </view>\n      \n      <!-- 已选区域展示 -->\n      <view class=\"selected-region\" v-if=\"selectedRegion\">\n        <view class=\"region-info\">\n          <text class=\"region-name\">{{ selectedRegion }}</text>\n          <view class=\"region-status\" :class=\"{'available': regionStatus === '可申请', 'pending': regionStatus === '审核中', 'occupied': regionStatus === '已占用'}\">{{ regionStatus }}</view>\n        </view>\n        <view class=\"region-data\">\n          <view class=\"data-box\">\n            <text class=\"data-value\">{{ regionData.merchants }}</text>\n            <text class=\"data-label\">商家数</text>\n          </view>\n          <view class=\"data-box\">\n            <text class=\"data-value\">{{ regionData.users }}</text>\n            <text class=\"data-label\">用户数</text>\n          </view>\n          <view class=\"data-box\">\n            <text class=\"data-value\">{{ regionData.orders }}万</text>\n            <text class=\"data-label\">月订单</text>\n          </view>\n          <view class=\"data-box\">\n            <text class=\"data-value\">{{ regionData.income }}万</text>\n            <text class=\"data-label\">月收入</text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 申请要求卡片 -->\n    <view class=\"franchise-card requirements-card\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">申请要求</text>\n      </view>\n      <view class=\"requirements-list\">\n        <view class=\"requirement-item\">\n          <view class=\"requirement-dot\"></view>\n          <text class=\"requirement-text\">有当地资源或渠道，能够发展商家</text>\n        </view>\n        <view class=\"requirement-item\">\n          <view class=\"requirement-dot\"></view>\n          <text class=\"requirement-text\">有运营团队，能够提供本地化服务</text>\n        </view>\n        <view class=\"requirement-item\">\n          <view class=\"requirement-dot\"></view>\n          <text class=\"requirement-text\">注册有效营业执照的企业或个体工商户</text>\n        </view>\n        <view class=\"requirement-item\">\n          <view class=\"requirement-dot\"></view>\n          <text class=\"requirement-text\">资金实力：县级20万，市级50万，省级200万</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 加盟流程卡片 -->\n    <view class=\"franchise-card process-card\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">加盟流程</text>\n      </view>\n      <view class=\"process-steps\">\n        <view class=\"process-step\">\n          <view class=\"step-circle\">1</view>\n          <view class=\"step-line\" v-if=\"showStepLine(1)\"></view>\n          <view class=\"step-content\">\n            <view class=\"step-title\">提交申请</view>\n            <view class=\"step-desc\">填写基本信息</view>\n          </view>\n        </view>\n        <view class=\"process-step\">\n          <view class=\"step-circle\">2</view>\n          <view class=\"step-line\" v-if=\"showStepLine(2)\"></view>\n          <view class=\"step-content\">\n            <view class=\"step-title\">资质审核</view>\n            <view class=\"step-desc\">3个工作日内</view>\n          </view>\n        </view>\n        <view class=\"process-step\">\n          <view class=\"step-circle\">3</view>\n          <view class=\"step-line\" v-if=\"showStepLine(3)\"></view>\n          <view class=\"step-content\">\n            <view class=\"step-title\">签约授权</view>\n            <view class=\"step-desc\">线上签署协议</view>\n          </view>\n        </view>\n        <view class=\"process-step\">\n          <view class=\"step-circle\">4</view>\n          <view class=\"step-content\">\n            <view class=\"step-title\">系统培训</view>\n            <view class=\"step-desc\">运营能力提升</view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部按钮 -->\n    <view class=\"bottom-actions\">\n      <button class=\"action-btn consult-btn\" @tap=\"contactConsultant\">\n        <text class=\"btn-icon cuIcon-service\"></text>\n        <text>咨询顾问</text>\n      </button>\n      <button class=\"action-btn apply-btn\" @tap=\"submitApplication\" :disabled=\"!canApply\">\n        <text>立即申请</text>\n      </button>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, computed, onMounted } from 'vue';\nimport { province_list, city_list, county_list, city_province_map, county_city_map } from '@/static/data/china-area-data.js';\n\n// 响应式状态\nconst provinces = ref(Object.values(province_list));\nconst cities = ref([]);\nconst districts = ref([]);\nconst provinceCodes = ref(Object.keys(province_list));\nconst cityCodes = ref([]);\nconst districtCodes = ref([]);\nconst currentProvince = ref('');\nconst currentCity = ref('');\nconst currentDistrict = ref('');\nconst selectedRegion = ref('');\nconst regionStatus = ref('');\nconst regionData = reactive({\n  merchants: 0,\n  users: 0,\n  orders: 0,\n  income: 0\n});\n\n// 计算属性\nconst canApply = computed(() => {\n  return selectedRegion.value && regionStatus.value === '可申请';\n});\n\n// 方法\nconst goBack = () => {\n  uni.navigateBack({\n    fail: () => {\n      uni.switchTab({ url: '/pages/my/my' });\n    }\n  });\n};\n\nconst provinceChange = (e) => {\n  const index = e.detail.value;\n  const provinceCode = provinceCodes.value[index];\n  currentProvince.value = province_list[provinceCode];\n  // 获取该省下所有城市\n  cityCodes.value = Object.keys(city_list).filter(code => city_province_map[code] == provinceCode);\n  cities.value = cityCodes.value.map(code => city_list[code]);\n  currentCity.value = '';\n  currentDistrict.value = '';\n  selectedRegion.value = '';\n  districts.value = [];\n};\n\nconst cityChange = (e) => {\n  const index = e.detail.value;\n  const cityCode = cityCodes.value[index];\n  currentCity.value = city_list[cityCode];\n  // 获取该市下所有区县\n  districtCodes.value = Object.keys(county_list).filter(code => county_city_map[code] == cityCode);\n  districts.value = districtCodes.value.map(code => county_list[code]);\n  currentDistrict.value = '';\n  selectedRegion.value = '';\n};\n\nconst districtChange = (e) => {\n  const index = e.detail.value;\n  const districtCode = districtCodes.value[index];\n  currentDistrict.value = county_list[districtCode];\n  selectedRegion.value = `${currentProvince.value} ${currentCity.value} ${currentDistrict.value}`;\n  generateRegionData();\n};\n\nconst generateRegionData = () => {\n  const randomIndex = Math.floor(Math.random() * 10);\n  if (randomIndex < 7) {\n    regionStatus.value = '可申请';\n  } else if (randomIndex < 9) {\n    regionStatus.value = '审核中';\n  } else {\n    regionStatus.value = '已占用';\n  }\n  regionData.merchants = Math.floor(Math.random() * 2000) + 500;\n  regionData.users = Math.floor(Math.random() * 50000) + 10000;\n  regionData.orders = (Math.floor(Math.random() * 100) + 20) / 10;\n  regionData.income = (Math.floor(Math.random() * 500) + 50) / 10;\n};\n\nconst submitApplication = () => {\n  if (!selectedRegion.value || !canApply.value) {\n    uni.showToast({ title: '请先选择可申请的区域', icon: 'none' });\n    return;\n  }\n  uni.navigateTo({ url: '/subPackages/franchise/pages/application-form?region=' + encodeURIComponent(selectedRegion.value) });\n};\n\nconst contactConsultant = () => {\n  uni.showModal({\n    title: '联系加盟顾问',\n    content: '您可以通过以下方式联系我们的加盟顾问：\\n\\n电话：400-888-8888\\n微信：tcqy_jmzx\\n工作时间：9:00-18:00（周一至周五）',\n    showCancel: false,\n    confirmText: '我知道了'\n  });\n};\n\nconst showStepLine = (step) => {\n  return step < 4;\n};\n\n// 生命周期钩子\nonMounted(() => {\n  // 初始化省份列表\n  provinces.value = Object.values(province_list);\n  provinceCodes.value = Object.keys(province_list);\n});\n</script>\n\n<style lang=\"scss\" scoped>\n.franchise-container {\n  padding-bottom: 120rpx; // 为底部按钮预留空间\n  min-height: 100vh;\n  background-color: #F5F7FA;\n}\n\n.safe-area {\n  height: 20rpx;\n  width: 100%;\n}\n\n.safe-area-top {\n  height: 180rpx;\n  width: 100%;\n}\n\n.franchise-card {\n  margin: 30rpx;\n  border-radius: 20rpx;\n  background: #FFFFFF;\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);\n}\n\n.intro-card {\n  background: linear-gradient(135deg, #FFFFFF, #F9FCFF);\n  border: 1px solid rgba(22, 119, 255, 0.1);\n  margin-top: 30rpx; /* 调整顶部边距 */\n}\n\n.intro-header {\n  margin-bottom: 30rpx;\n}\n\n.intro-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10rpx;\n}\n\n.title-text {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333333;\n}\n\n.title-tag {\n  margin-left: 20rpx;\n  font-size: 22rpx;\n  color: #FF6B00;\n  background-color: rgba(255, 107, 0, 0.1);\n  padding: 4rpx 14rpx;\n  border-radius: 4rpx;\n}\n\n.intro-subtitle {\n  font-size: 26rpx;\n  color: #666666;\n}\n\n.intro-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.intro-item {\n  display: flex;\n  align-items: center;\n  padding: 20rpx 0;\n  border-bottom: 1px solid #F5F5F5;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n}\n\n.intro-icon {\n  width: 80rpx;\n  height: 80rpx;\n  margin-right: 20rpx;\n  background-color: rgba(22, 119, 255, 0.06);\n  padding: 15rpx;\n  border-radius: 50%;\n}\n\n.intro-info {\n  flex: 1;\n}\n\n.intro-name {\n  font-size: 28rpx;\n  color: #333333;\n  font-weight: 500;\n  margin-bottom: 6rpx;\n}\n\n.intro-desc {\n  font-size: 24rpx;\n  color: #999999;\n}\n\n.region-header {\n  margin-bottom: 30rpx;\n}\n\n.region-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333333;\n  display: block;\n  margin-bottom: 10rpx;\n}\n\n.region-subtitle {\n  font-size: 24rpx;\n  color: #999999;\n}\n\n.region-selector {\n  padding: 20rpx;\n  background-color: #F8F9FC;\n  border-radius: 12rpx;\n  margin-bottom: 30rpx;\n}\n\n.selector-row {\n  display: flex;\n  margin-bottom: 20rpx;\n}\n\n.selector-item {\n  flex: 1;\n  margin-right: 20rpx;\n}\n\n.selector-item:last-child {\n  margin-right: 0;\n}\n\n.full-width {\n  width: 100%;\n}\n\n.selector-label {\n  font-size: 26rpx;\n  color: #666;\n  margin-bottom: 10rpx;\n  font-weight: 500;\n}\n\n.selector-picker {\n  background-color: #fff;\n  height: 80rpx;\n  border-radius: 8rpx;\n  padding: 0 20rpx;\n  display: flex;\n  align-items: center;\n  border: 1px solid #EAEDF2;\n}\n\n.picker-value {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n  color: #333;\n  font-size: 28rpx;\n}\n\n.picker-value.disabled {\n  color: #bbb;\n}\n\n.picker-value text:last-child {\n  color: #999;\n  font-size: 24rpx;\n}\n\n/* 新增区域图示 */\n.region-map {\n  padding: 0 20rpx;\n  margin-bottom: 30rpx;\n}\n\n.map-title {\n  font-size: 26rpx;\n  color: #666;\n  margin-bottom: 15rpx;\n  display: flex;\n  align-items: center;\n}\n\n.map-title-dot {\n  width: 6rpx;\n  height: 6rpx;\n  border-radius: 50%;\n  background-color: #0066FF;\n  margin-right: 10rpx;\n}\n\n.map-image {\n  width: 100%;\n  height: 300rpx;\n  background-color: #F8F9FC;\n  border-radius: 12rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 状态指示器样式 */\n.status-indicators {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 20rpx;\n  padding: 0 20rpx;\n}\n\n.status-item {\n  display: flex;\n  align-items: center;\n}\n\n.status-dot {\n  width: 16rpx;\n  height: 16rpx;\n  border-radius: 50%;\n  margin-right: 8rpx;\n}\n\n.available-dot {\n  background-color: #34C759;\n}\n\n.pending-dot {\n  background-color: #FF9500;\n}\n\n.occupied-dot {\n  background-color: #FF3B30;\n}\n\n.status-text {\n  font-size: 24rpx;\n  color: #666;\n}\n\n.selected-region {\n  padding: 30rpx;\n  background-color: #F8FCFF;\n  border-radius: 12rpx;\n  border: 1px solid rgba(0, 102, 255, 0.1);\n  margin-top: 20rpx;\n}\n\n.region-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.region-name {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.region-status {\n  padding: 4rpx 12rpx;\n  border-radius: 6rpx;\n  font-size: 24rpx;\n  font-weight: 500;\n}\n\n.available {\n  background-color: rgba(52, 199, 89, 0.1);\n  color: #34C759;\n}\n\n.pending {\n  background-color: rgba(255, 149, 0, 0.1);\n  color: #FF9500;\n}\n\n.occupied {\n  background-color: rgba(255, 59, 48, 0.1);\n  color: #FF3B30;\n}\n\n.region-data {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 10rpx;\n}\n\n.data-box {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  width: 25%;\n}\n\n.data-value {\n  font-size: 32rpx;\n  font-weight: 700;\n  color: #333;\n  margin-bottom: 4rpx;\n}\n\n.data-label {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.card-header {\n  margin-bottom: 20rpx;\n}\n\n.card-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333333;\n  position: relative;\n  padding-left: 20rpx;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    left: 0;\n    top: 50%;\n    transform: translateY(-50%);\n    width: 6rpx;\n    height: 30rpx;\n    background-color: #1677FF;\n    border-radius: 3rpx;\n  }\n}\n\n.requirements-list {\n  padding: 10rpx 0;\n}\n\n.requirement-item {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 20rpx;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n.requirement-dot {\n  width: 12rpx;\n  height: 12rpx;\n  border-radius: 50%;\n  background-color: #1677FF;\n  margin-top: 10rpx;\n  margin-right: 15rpx;\n  flex-shrink: 0;\n}\n\n.requirement-text {\n  font-size: 26rpx;\n  color: #666666;\n  line-height: 1.5;\n  flex: 1;\n}\n\n.process-steps {\n  padding: 20rpx 0;\n}\n\n.process-step {\n  display: flex;\n  align-items: flex-start;\n  position: relative;\n  padding-bottom: 40rpx;\n  \n  &:last-child {\n    padding-bottom: 0;\n  }\n}\n\n.step-circle {\n  width: 50rpx;\n  height: 50rpx;\n  border-radius: 25rpx;\n  background-color: #1677FF;\n  color: #FFFFFF;\n  font-size: 28rpx;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 20rpx;\n  position: relative;\n  z-index: 2;\n}\n\n.step-line {\n  position: absolute;\n  left: 25rpx;\n  top: 50rpx;\n  width: 2rpx;\n  height: calc(100% - 50rpx);\n  background-color: #E5E5E5;\n  z-index: 1;\n}\n\n.step-content {\n  flex: 1;\n}\n\n.step-title {\n  font-size: 28rpx;\n  color: #333333;\n  font-weight: 500;\n  margin-bottom: 6rpx;\n}\n\n.step-desc {\n  font-size: 24rpx;\n  color: #999999;\n}\n\n.bottom-actions {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 110rpx;\n  display: flex;\n  align-items: center;\n  padding: 0 30rpx;\n  background-color: #FFFFFF;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  z-index: 100;\n}\n\n.action-btn {\n  height: 80rpx;\n  line-height: 80rpx;\n  border-radius: 40rpx;\n  font-size: 28rpx;\n  font-weight: 500;\n  margin: 0;\n  \n  &.consult-btn {\n    flex: 1;\n    background-color: #F5F7FA;\n    color: #666666;\n    margin-right: 20rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    \n    .btn-icon {\n      margin-right: 8rpx;\n    }\n  }\n  \n  &.apply-btn {\n    flex: 2;\n    background: linear-gradient(90deg, #1677FF, #4F9DFF);\n    color: #FFFFFF;\n    \n    &[disabled] {\n      background: linear-gradient(90deg, #CCCCCC, #E5E5E5);\n      color: #FFFFFF;\n      opacity: 1;\n    }\n  }\n}\n\n/* 修改自定义导航栏样式，匹配发布页 */\n:deep(.bg-gradient-blue) {\n  background: linear-gradient(135deg, #0066FF, #0052CC);\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n}\n\n:deep(.cu-custom .cu-bar) {\n  height: 88rpx !important;\n  padding-top: 44px !important; /* 状态栏高度 */\n}\n\n:deep(.cu-custom .content) {\n  top: calc(44px + 40rpx) !important; /* 调整标题位置上移 */\n  display: flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n  width: 100% !important;\n}\n\n:deep(.cu-custom .action) {\n  margin-top: 8rpx !important; /* 调整关闭键位置与标题对齐 */\n}\n\n:deep(.cu-bar .action:first-child) {\n  margin-left: 30rpx;\n  font-size: 30rpx;\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: relative;\n  z-index: 1;\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* 自定义导航栏 */\n.custom-navbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 88rpx;\n  padding: 0 30rpx;\n  padding-top: 44px; /* 状态栏高度 */\n  position: fixed; /* 改为固定定位 */\n  top: 0;\n  left: 0;\n  right: 0;\n  background-image: linear-gradient(135deg, #0066FF, #0052CC); /* 改为与发布页一致的渐变角度 */\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.15);\n  z-index: 100; /* 提高z-index确保在最上层 */\n}\n\n.navbar-title {\n  position: absolute;\n  left: 0;\n  right: 0;\n  color: #ffffff;\n  font-size: 36rpx;\n  font-weight: 700;\n  font-family: 'AlimamaShuHeiTi', sans-serif;\n  text-align: center;\n}\n\n.navbar-right {\n  width: 40rpx;\n  height: 40rpx;\n}\n\n.navbar-left {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  z-index: 20; /* 确保在标题上层，可以被点击 */\n}\n\n.back-icon {\n  width: 100%;\n  height: 100%;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/franchise/pages/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "province_list", "reactive", "computed", "uni", "city_list", "city_province_map", "county_list", "county_city_map", "onMounted"], "mappings": ";;;;;;;AAsOA,UAAM,YAAYA,cAAAA,IAAI,OAAO,OAAOC,0BAAAA,aAAa,CAAC;AAClD,UAAM,SAASD,cAAAA,IAAI,CAAA,CAAE;AACrB,UAAM,YAAYA,cAAAA,IAAI,CAAA,CAAE;AACxB,UAAM,gBAAgBA,cAAAA,IAAI,OAAO,KAAKC,0BAAAA,aAAa,CAAC;AACpD,UAAM,YAAYD,cAAAA,IAAI,CAAA,CAAE;AACxB,UAAM,gBAAgBA,cAAAA,IAAI,CAAA,CAAE;AAC5B,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,cAAcA,cAAAA,IAAI,EAAE;AAC1B,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,iBAAiBA,cAAAA,IAAI,EAAE;AAC7B,UAAM,eAAeA,cAAAA,IAAI,EAAE;AAC3B,UAAM,aAAaE,cAAAA,SAAS;AAAA,MAC1B,WAAW;AAAA,MACX,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV,CAAC;AAGD,UAAM,WAAWC,cAAQ,SAAC,MAAM;AAC9B,aAAO,eAAe,SAAS,aAAa,UAAU;AAAA,IACxD,CAAC;AAGD,UAAM,SAAS,MAAM;AACnBC,oBAAAA,MAAI,aAAa;AAAA,QACf,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU,EAAE,KAAK,eAAgB,CAAA;AAAA,QACtC;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,iBAAiB,CAAC,MAAM;AAC5B,YAAM,QAAQ,EAAE,OAAO;AACvB,YAAM,eAAe,cAAc,MAAM,KAAK;AAC9C,sBAAgB,QAAQH,wCAAc,YAAY;AAElD,gBAAU,QAAQ,OAAO,KAAKI,0BAAAA,SAAS,EAAE,OAAO,UAAQC,0BAAiB,kBAAC,IAAI,KAAK,YAAY;AAC/F,aAAO,QAAQ,UAAU,MAAM,IAAI,UAAQD,0BAAAA,UAAU,IAAI,CAAC;AAC1D,kBAAY,QAAQ;AACpB,sBAAgB,QAAQ;AACxB,qBAAe,QAAQ;AACvB,gBAAU,QAAQ;IACpB;AAEA,UAAM,aAAa,CAAC,MAAM;AACxB,YAAM,QAAQ,EAAE,OAAO;AACvB,YAAM,WAAW,UAAU,MAAM,KAAK;AACtC,kBAAY,QAAQA,oCAAU,QAAQ;AAEtC,oBAAc,QAAQ,OAAO,KAAKE,0BAAAA,WAAW,EAAE,OAAO,UAAQC,0BAAe,gBAAC,IAAI,KAAK,QAAQ;AAC/F,gBAAU,QAAQ,cAAc,MAAM,IAAI,UAAQD,0BAAAA,YAAY,IAAI,CAAC;AACnE,sBAAgB,QAAQ;AACxB,qBAAe,QAAQ;AAAA,IACzB;AAEA,UAAM,iBAAiB,CAAC,MAAM;AAC5B,YAAM,QAAQ,EAAE,OAAO;AACvB,YAAM,eAAe,cAAc,MAAM,KAAK;AAC9C,sBAAgB,QAAQA,sCAAY,YAAY;AAChD,qBAAe,QAAQ,GAAG,gBAAgB,KAAK,IAAI,YAAY,KAAK,IAAI,gBAAgB,KAAK;AAC7F;IACF;AAEA,UAAM,qBAAqB,MAAM;AAC/B,YAAM,cAAc,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE;AACjD,UAAI,cAAc,GAAG;AACnB,qBAAa,QAAQ;AAAA,MACzB,WAAa,cAAc,GAAG;AAC1B,qBAAa,QAAQ;AAAA,MACzB,OAAS;AACL,qBAAa,QAAQ;AAAA,MACtB;AACD,iBAAW,YAAY,KAAK,MAAM,KAAK,OAAQ,IAAG,GAAI,IAAI;AAC1D,iBAAW,QAAQ,KAAK,MAAM,KAAK,OAAQ,IAAG,GAAK,IAAI;AACvD,iBAAW,UAAU,KAAK,MAAM,KAAK,OAAM,IAAK,GAAG,IAAI,MAAM;AAC7D,iBAAW,UAAU,KAAK,MAAM,KAAK,OAAM,IAAK,GAAG,IAAI,MAAM;AAAA,IAC/D;AAEA,UAAM,oBAAoB,MAAM;AAC9B,UAAI,CAAC,eAAe,SAAS,CAAC,SAAS,OAAO;AAC5CH,sBAAG,MAAC,UAAU,EAAE,OAAO,cAAc,MAAM,OAAM,CAAE;AACnD;AAAA,MACD;AACDA,0BAAI,WAAW,EAAE,KAAK,0DAA0D,mBAAmB,eAAe,KAAK,EAAC,CAAE;AAAA,IAC5H;AAEA,UAAM,oBAAoB,MAAM;AAC9BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,aAAa;AAAA,MACjB,CAAG;AAAA,IACH;AAEA,UAAM,eAAe,CAAC,SAAS;AAC7B,aAAO,OAAO;AAAA,IAChB;AAGAK,kBAAAA,UAAU,MAAM;AAEd,gBAAU,QAAQ,OAAO,OAAOR,0BAAa,aAAA;AAC7C,oBAAc,QAAQ,OAAO,KAAKA,0BAAa,aAAA;AAAA,IACjD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9UD,GAAG,WAAW,eAAe;"}