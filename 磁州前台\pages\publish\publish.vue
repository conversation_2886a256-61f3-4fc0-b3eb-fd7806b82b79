<template>
	<view class="publish-container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="navbar-left" @click="goBack">
				<image class="back-icon" src="/static/images/tabbar/最新返回键.png"></image>
			</view>
			<view class="navbar-title">发布信息</view>
			<view class="navbar-right">
				<!-- 删除原来的按钮 -->
			</view>
		</view>
		
		<!-- 内容区域 -->
		<scroll-view scroll-y class="content-scroll">
			<!-- 免责声明 -->
			<view class="disclaimer">
				<view class="disclaimer-list">
					<view class="disclaimer-item">
						<text class="disclaimer-content">内容规范：禁止发布违法、侵权或违背公序良俗的信息，禁发网络兼职，信用贷款等垃圾信息！违者需自行承担法律后果。</text>
					</view>
				</view>
			</view>
			
			<!-- 恢复商家入驻卡片 -->
			<view class="entry-cards">
				<!-- 商家入驻卡片 -->
				<view class="entry-card merchant-card" @click="handleMerchantApply">
					<view class="join-card-left">
						<view class="join-icon-wrap">
							<image class="join-icon" src="/static/images/tabbar/入驻卡片.png"></image>
						</view>
						<view class="join-info">
							<text class="join-title">我是商家，点击入驻商圈</text>
							<text class="join-desc">超低成本，获得更多商机！</text>
						</view>
					</view>
					<button class="join-btn" @click.stop="handleMerchantApply">立即申请</button>
					<view class="hot-badge">热门</view>
				</view>
			</view>
			
			<!-- 发布类目选择 -->
			<view class="category-section">
				<view class="category-section-title">请选择发布类目</view>
				
				<view class="category-grid">
					<!-- 第一行 -->
					<view class="category-item" hover-class="category-item-hover" @click="navigateToServiceCategory">
						<view class="category-icon-wrapper">
							<image class="category-icon" src="/static/images/tabbar/到家服务.png" mode="aspectFit"></image>
						</view>
						<text class="category-name">到家服务</text>
					</view>
					
					<view class="category-item" hover-class="category-item-hover" @click="navigateToPublishDetail({name: '寻找服务', type: 'find_service'})">
						<view class="category-icon-wrapper">
							<image class="category-icon" src="/static/images/tabbar/寻找服务.png" mode="aspectFit"></image>
						</view>
						<text class="category-name">寻找服务</text>
					</view>
					
					<view class="category-item" hover-class="category-item-hover" @click="navigateToPublishDetail({name: '招聘信息', type: 'hire'})">
						<view class="category-icon-wrapper">
							<image class="category-icon" src="/static/images/tabbar/招聘信息.png" mode="aspectFit"></image>
						</view>
						<text class="category-name">招聘信息</text>
					</view>
					
					<view class="category-item" hover-class="category-item-hover" @click="navigateToPublishDetail({name: '求职信息', type: 'job_wanted'})">
						<view class="category-icon-wrapper">
							<image class="category-icon" src="/static/images/tabbar/求职信息.png" mode="aspectFit"></image>
						</view>
						<text class="category-name">求职信息</text>
					</view>
					
					<view class="category-item" hover-class="category-item-hover" @click="navigateToPublishDetail({name: '房屋出租', type: 'house_rent'})">
						<view class="category-icon-wrapper">
							<image class="category-icon" src="/static/images/tabbar/出租.png" mode="aspectFit"></image>
						</view>
						<text class="category-name">房屋出租</text>
					</view>
					
					<!-- 第二行 -->
					<view class="category-item" hover-class="category-item-hover" @click="navigateToPublishDetail({name: '房屋出售', type: 'house_sell'})">
						<view class="category-icon-wrapper">
							<image class="category-icon" src="/static/images/tabbar/房屋出售.png" mode="aspectFit"></image>
						</view>
						<text class="category-name">房屋出售</text>
					</view>
					
					<view class="category-item" hover-class="category-item-hover" @click="navigateToPublishDetail({name: '二手车辆', type: 'used_car'})">
						<view class="category-icon-wrapper">
							<image class="category-icon" src="/static/images/tabbar/二手车辆.png" mode="aspectFit"></image>
						</view>
						<text class="category-name">二手车辆</text>
					</view>
					
					<view class="category-item" hover-class="category-item-hover" @click="navigateToPublishDetail({name: '宠物信息', type: 'pet'})">
						<view class="category-icon-wrapper">
							<image class="category-icon" src="/static/images/tabbar/宠物信息.png" mode="aspectFit"></image>
						</view>
						<text class="category-name">宠物信息</text>
					</view>
					
					<view class="category-item" hover-class="category-item-hover" @click="navigateToPublishDetail({name: '商家活动', type: 'merchant_activity'})">
						<view class="category-icon-wrapper">
							<image class="category-icon" src="/static/images/tabbar/商家活动.png" mode="aspectFit"></image>
						</view>
						<text class="category-name">商家活动</text>
					</view>
					
					<view class="category-item" hover-class="category-item-hover" @click="navigateToPublishDetail({name: '车辆服务', type: 'car_service'})">
						<view class="category-icon-wrapper">
							<image class="category-icon" src="/static/images/tabbar/车辆服务.png" mode="aspectFit"></image>
						</view>
						<text class="category-name">车辆服务</text>
					</view>
					
					<view class="category-item" hover-class="category-item-hover" @click="navigateToPublishDetail({name: '二手闲置', type: 'second_hand'})">
						<view class="category-icon-wrapper">
							<image class="category-icon" src="/static/images/tabbar/二手闲置.png" mode="aspectFit"></image>
						</view>
						<text class="category-name">二手闲置</text>
					</view>
					
					<!-- 第三行 -->
					<view class="category-item" hover-class="category-item-hover" @click="navigateToPublishDetail({name: '磁州拼车', type: 'carpool'})">
						<view class="category-icon-wrapper">
							<image class="category-icon" src="/static/images/tabbar/磁州拼车.png" mode="aspectFit"></image>
						</view>
						<text class="category-name">磁州拼车</text>
					</view>
					
					<view class="category-item" hover-class="category-item-hover" @click="navigateToPublishDetail({name: '生意转让', type: 'business_transfer'})">
						<view class="category-icon-wrapper">
							<image class="category-icon" src="/static/images/tabbar/生意转让.png" mode="aspectFit"></image>
						</view>
						<text class="category-name">生意转让</text>
					</view>
					
					<view class="category-item" hover-class="category-item-hover" @click="navigateToPublishDetail({name: '教育培训', type: 'education'})">
						<view class="category-icon-wrapper">
							<image class="category-icon" src="/static/images/tabbar/教育培训.png" mode="aspectFit"></image>
						</view>
						<text class="category-name">教育培训</text>
					</view>
					
					<view class="category-item" hover-class="category-item-hover" @click="navigateToPublishDetail({name: '其他服务', type: 'other_service'})">
						<view class="category-icon-wrapper">
							<image class="category-icon" src="/static/images/tabbar/其他服务.png" mode="aspectFit"></image>
						</view>
						<text class="category-name">其他服务</text>
					</view>
					
					<view class="category-item" hover-class="category-item-hover" @click="navigateToPublishDetail({name: '婚恋交友', type: 'dating'})">
						<view class="category-icon-wrapper">
							<image class="category-icon" src="/static/images/tabbar/婚恋交友.png" mode="aspectFit"></image>
						</view>
						<text class="category-name">婚恋交友</text>
					</view>
					
					<view class="category-item test-category-item" hover-class="category-item-hover" @click="navigateToPublishDetail({name: '测试发布', type: 'test'})" @longpress="showTestDataManager">
						<view class="category-icon-wrapper test-icon-wrapper">
							<text class="test-icon">测</text>
						</view>
						<text class="category-name">测试发布</text>
						<text class="test-badge" v-if="testPublishCount > 0">{{testPublishCount}}</text>
					</view>

					<!-- 新增商家入驻测试按钮 -->
					<view class="category-item merchant-test-category-item" hover-class="category-item-hover" @click="testMerchantJoin">
						<view class="category-icon-wrapper merchant-test-icon-wrapper">
							<text class="test-icon">商</text>
						</view>
						<text class="category-name">商家入驻测试</text>
						<text class="merchant-test-badge" v-if="merchantTestCount > 0">{{merchantTestCount}}</text>
					</view>
				</view>
			</view>
			<RedPacketEntry v-model="form.redPacket" />
		</scroll-view>
	</view>
</template>

<script setup>
import { ref } from 'vue';
import { onLoad, onShow } from '@dcloudio/uni-app';

// --- 数据迁移开始 ---
const imageLoadStatus = ref({});
const preloadComplete = ref(false);
const testPublishCount = ref(0);
const merchantTestCount = ref(0);
const form = ref({
	redPacket: null
});
// --- 数据迁移结束 ---

// --- 方法迁移开始 ---
// 设置页面优先级
const setPagePriority = () => {
	if (uni.canIUse && uni.canIUse('setPageInfo')) {
		try {
			// @ts-ignore
			wx.setPageInfo({
				pageType: 'homePage',
				performanceLevel: 'high'
			});
		} catch (e) {
			console.error('设置页面优先级失败', e);
		}
	}
};

// 预加载图片
const preloadImages = () => {
	const imageList = [
		'/static/images/tabbar/到家服务.png',
		'/static/images/tabbar/寻找服务.png',
		'/static/images/tabbar/招聘信息.png',
		'/static/images/tabbar/求职信息.png',
		'/static/images/tabbar/出租.png',
		'/static/images/tabbar/房屋出售.png',
		'/static/images/tabbar/二手车辆.png',
		'/static/images/tabbar/宠物信息.png',
		'/static/images/tabbar/商家活动.png',
		'/static/images/tabbar/车辆服务.png',
		'/static/images/tabbar/二手闲置.png',
		'/static/images/tabbar/磁州拼车.png',
		'/static/images/tabbar/生意转让.png',
		'/static/images/tabbar/教育培训.png',
		'/static/images/tabbar/其他服务.png'
	];

	let loadedCount = 0;
	const totalImages = imageList.length;

	imageList.forEach(img => {
		uni.getImageInfo({
			src: img,
			success: () => {
				imageLoadStatus.value[img] = true;
				loadedCount++;
				if (loadedCount === totalImages) {
					preloadComplete.value = true;
				}
			},
			fail: () => {
				console.error('图片加载失败:', img);
				loadedCount++;
				if (loadedCount === totalImages) {
					preloadComplete.value = true;
				}
			}
		});
	});

	setTimeout(() => {
		preloadComplete.value = true;
	}, 1000);
};

// 加载测试发布数据计数
const loadTestPublishCount = () => {
	try {
		const testData = uni.getStorageSync('testPublishData') || [];
		testPublishCount.value = testData.length;
	} catch (e) {
		console.error('加载测试数据计数失败', e);
		testPublishCount.value = 0;
	}
};

// 加载商家入驻测试数据计数
const loadMerchantTestCount = () => {
	try {
		const merchantTestData = uni.getStorageSync('merchantTestData') || [];
		merchantTestCount.value = merchantTestData.length;
	} catch (e) {
		console.error('加载商家入驻测试数据计数失败', e);
		merchantTestCount.value = 0;
	}
};

// 商家入驻处理
const handleMerchantApply = () => {
	uni.navigateTo({
		url: '/pages/business/join'
	});
};

// 个人入驻处理
const handlePersonalApply = () => {
	uni.showToast({
		title: '正在开发个人入驻功能',
		icon: 'none'
	});
};

// 商家入驻测试功能
const testMerchantJoin = () => {
	uni.showModal({
		title: '商家入驻测试',
		content: '请输入任意内容，完成快速商家入驻测试',
		editable: true,
		placeholderText: '输入商家名称',
		success: (res) => {
			if (res.confirm) {
				const shopName = res.content || '测试商家';
				uni.showLoading({
					title: '处理中',
					mask: true
				});
				setTimeout(() => {
					uni.hideLoading();
					try {
						const merchantTestData = uni.getStorageSync('merchantTestData') || [];
						const newMerchantData = {
							id: Date.now().toString(),
							shopName: shopName,
							address: '测试地址',
							category: '测试类目',
							scale: '1-10人',
							businessTime: '09:00-21:00',
							contactPhone: '***********',
							description: '这是一个测试商家',
							createTime: new Date().toLocaleString(),
							joinMethod: 'free',
							shopImage: '/static/images/tabbar/商家入驻.png',
							logo: '/static/images/tabbar/商家入驻.png',
							qrcode: '',
							album: []
						};
						merchantTestData.push(newMerchantData);
						uni.setStorageSync('merchantTestData', merchantTestData);
						merchantTestCount.value = merchantTestData.length;
						uni.setStorageSync('lastMerchantId', newMerchantData.id);
						uni.setStorageSync('lastMerchantData', newMerchantData);
						
						// 跳转到商家入驻成功页面
						uni.navigateTo({
							url: `/pages/business/success?id=${newMerchantData.id}&shopName=${encodeURIComponent(shopName)}&memberType=${encodeURIComponent('测试版')}&isTest=true`,
							fail: (err) => {
								console.error('跳转到商家入驻成功页面失败:', err);
								// 如果商家入驻成功页面不存在，则尝试跳转到通用成功页面
								uni.navigateTo({
									url: '/pages/publish/success?id=' + newMerchantData.id + '&type=merchant',
									fail: (err2) => {
										console.error('跳转到通用成功页面也失败:', err2);
										uni.showToast({
											title: '入驻成功',
											icon: 'success',
											duration: 2000
										});
									}
								});
							}
						});
					} catch (e) {
						console.error('存储商家测试数据失败', e);
						uni.showToast({
							title: '入驻成功',
							icon: 'success',
							duration: 2000
						});
					}
				}, 800);
			}
		}
	});
};

// 导航到详情页
const navigateToPublishDetail = (item) => {
	if (item.type === 'test') {
		setTimeout(() => {
			uni.showModal({
				title: '测试发布',
				content: '请输入任意内容进行测试发布',
				editable: true,
				placeholderText: '随意输入一个字符',
				success: (res) => {
					if (res.confirm) {
						const content = res.content || '测试内容';
						uni.showLoading({
							title: '发布中',
							mask: true
						});
						setTimeout(() => {
							uni.hideLoading();
							try {
								const testData = uni.getStorageSync('testPublishData') || [];
								const newData = {
									id: Date.now().toString(),
									type: 'test',
									category: '测试发布',
									categoryName: '测试发布',
									title: '测试发布',
									content: content,
									createTime: new Date().toLocaleString()
								};
								testData.push(newData);
								uni.setStorageSync('testPublishData', testData);
								testPublishCount.value = testData.length;
								uni.setStorageSync('lastPublishId', newData.id);
								uni.setStorageSync('lastPublishData', newData);
								uni.navigateTo({
									url: '/pages/publish/success?id=' + newData.id,
									fail: (err) => {
										console.error('跳转到发布成功页面失败:', err);
										uni.showToast({
											title: '发布成功',
											icon: 'success',
											duration: 2000
										});
									}
								});
							} catch (e) {
								console.error('存储测试数据失败', e);
								uni.showToast({
									title: '发布成功',
									icon: 'success',
									duration: 2000
								});
							}
						}, 800);
					}
				}
			});
		}, 300);
		return;
	}

	uni.showLoading({
		title: '加载中',
		mask: true
	});
	const params = {
		type: item.type,
		name: item.name,
		category: item.name,
		categoryType: item.type
	};
	const queryString = Object.keys(params)
		.map(key => `${key}=${encodeURIComponent(params[key])}`)
		.join('&');
	try {
		uni.navigateTo({
			url: `/pages/publish/detail?${queryString}&isPublish=true`,
			success: function() {
				console.log('导航成功');
				uni.hideLoading();
			},
			fail: function(err) {
				console.error('导航失败:', err);
				uni.navigateTo({
					url: `../publish/detail?${queryString}&isPublish=true`,
					success: function() {
						uni.hideLoading();
					},
					fail: function(err2) {
						console.error('第二次导航失败:', err2);
						uni.hideLoading();
						uni.showToast({
							title: '页面跳转失败，请重启应用',
							icon: 'none'
						});
					}
				});
			}
		});
	} catch (e) {
		console.error('导航异常:', e);
		uni.hideLoading();
		uni.showToast({
			title: '系统繁忙，请稍后再试',
			icon: 'none'
		});
	}
};

const showTestDataManager = () => {
	try {
		const testData = uni.getStorageSync('testPublishData') || [];
		if (testData.length === 0) {
			uni.showToast({
				title: '暂无测试数据',
				icon: 'none'
			});
			return;
		}
		uni.showActionSheet({
			itemList: ['查看测试数据', '清除测试数据'],
			success: (res) => {
				if (res.tapIndex === 0) {
					viewTestData(testData);
				} else if (res.tapIndex === 1) {
					clearTestData();
				}
			}
		});
	} catch (e) {
		console.error('打开测试数据管理器失败', e);
		uni.showToast({
			title: '操作失败，请重试',
			icon: 'none'
		});
	}
};

const viewTestData = (testData) => {
	const dataList = testData.map((item, index) => {
		return `${index + 1}. ${item.content} (${item.date})`;
	}).join('\n\n');
	uni.showModal({
		title: '测试数据列表',
		content: dataList,
		confirmText: '关闭',
		showCancel: false
	});
};

const clearTestData = () => {
	uni.showModal({
		title: '确认清除',
		content: '确定要清除所有测试数据吗？',
		success: (res) => {
			if (res.confirm) {
				try {
					uni.removeStorageSync('testPublishData');
					testPublishCount.value = 0;
					uni.showToast({
						title: '测试数据已清除',
						icon: 'success'
					});
				} catch (e) {
					console.error('清除测试数据失败', e);
					uni.showToast({
						title: '清除失败，请重试',
						icon: 'none'
					});
				}
			}
		}
	});
};

const navigateToServiceCategory = () => {
	console.log('跳转到服务类型选择页面');
	uni.navigateTo({
		url: '/pages/publish/service-category'
	});
};
// --- 方法迁移结束 ---

// --- 生命周期钩子迁移开始 ---
onLoad(() => {
	setPagePriority();
	preloadImages();
	loadTestPublishCount();
	loadMerchantTestCount();
});

onShow(() => {
	loadTestPublishCount();
	loadMerchantTestCount();
});
// --- 生命周期钩子迁移结束 ---
</script>

<style lang="scss">
/* 使用系统默认字体 */
.publish-container {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* 替换所有使用阿里妈妈字体的地方 */
.title, .heading, .subtitle, .publish-title, .section-title {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  font-weight: bold; /* 使用系统字体的粗体代替阿里妈妈黑体 */
}

	.publish-container {
		min-height: 100vh;
		background-color: #f5f7fa;
		display: flex;
		flex-direction: column;
		position: relative;
		padding-bottom: 30rpx;
		padding-top: 44px; /* 为状态栏预留空间 */
	}
	
	/* 内容区滚动视图 */
	.content-scroll {
		flex: 1;
		box-sizing: border-box;
		padding-top: 150rpx; /* 增加顶部内边距，为固定导航栏留出空间 */
	}
	
	/* 自定义导航栏 */
	.custom-navbar {
		background: linear-gradient(135deg, #0066FF, #0052CC);
		height: 88rpx;
		padding-top: 44px; /* 状态栏高度 */
		display: flex;
		align-items: center;
		justify-content: center;
		position: fixed; /* 改为固定定位 */
		top: 0;
		left: 0;
		right: 0;
		padding-bottom: 10rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
		z-index: 100; /* 提高z-index确保在最上层 */
	}
	
	.navbar-title {
		color: #FFFFFF;
		font-size: 36rpx;
		font-weight: 600;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}
	
	.navbar-left {
		position: absolute;
		left: 20rpx;
		height: 88rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-icon {
		width: 48rpx;
		height: 48rpx;
		display: block;
	}
	
	.navbar-right {
		position: absolute;
		right: 20rpx;
		display: flex;
	}
	
	.navbar-btn {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		transition: all 0.2s;
	}
	
	.navbar-btn:active {
		background-color: rgba(255, 255, 255, 0.2);
	}
	
	.navbar-btn-icon {
		width: 44rpx;
		height: 44rpx;
	}
	
	/* 免责声明 */
	.disclaimer {
		padding: 20rpx 30rpx 10rpx 30rpx;
		margin: 10rpx 20rpx 25rpx;
		/* 去掉背景卡片 */
		/* background: #fff; */
		/* border-radius: 10rpx; */
	}
	
	.disclaimer-list {
		display: flex;
		flex-direction: column;
		gap: 12rpx;
	}
	
	.disclaimer-item {
		display: flex;
		align-items: flex-start;
		font-size: 24rpx;
		color: #444;
		line-height: 1.7;
	}
	
	.disclaimer-index {
		font-weight: bold;
		margin-right: 8rpx;
		color: #0052CC;
		font-size: 26rpx;
		flex-shrink: 0;
	}
	
	.disclaimer-content {
		flex: 1;
		color: #999;
	}
	
	.disclaimer-note {
		margin-top: 12rpx;
		font-size: 20rpx;
		color: #999;
		text-align: left;
	}
	
	/* 入驻卡片 */
	.entry-cards {
 padding: 0 30rpx;
	margin-bottom: 15rpx;
	position: relative;
	z-index: 5;
	}
	
	.entry-card {
	margin-bottom: 15rpx;
	background: #EEF1F6;
		border-radius: 16rpx;
	box-shadow: 10rpx 10rpx 20rpx rgba(174, 184, 210, 0.8),
				-10rpx -10rpx 20rpx rgba(255, 255, 255, 0.9),
				inset 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.6);
		display: flex;
		align-items: center;
	justify-content: space-between;
	padding: 20rpx 20rpx 20rpx 20rpx;
		position: relative;
	z-index: 2;
	border: none;
		overflow: hidden;
	transition: all 0.2s ease;
	}
	
	.entry-card:active {
	transform: scale(0.98);
	box-shadow: 6rpx 6rpx 12rpx rgba(174, 184, 210, 0.8),
				-6rpx -6rpx 12rpx rgba(255, 255, 255, 0.9),
				inset 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.6);
	}
	
.entry-card::before {
		content: "";
		position: absolute;
		top: 0;
 left: 0;
		right: 0;
		bottom: 0;
 background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0) 100%);
		z-index: 0;
	}
	
.join-card-left {
	display: flex;
	align-items: center;
	position: relative;
	z-index: 2;
	}
	
.join-icon-wrap {
	width: 70rpx;
	height: 70rpx;
	background: #EEF1F6;
	border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
	margin-right: 20rpx;
	box-shadow: 5rpx 5rpx 10rpx rgba(174, 184, 210, 0.6),
				-5rpx -5rpx 10rpx rgba(255, 255, 255, 0.9),
				inset 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.4);
		position: relative;
	z-index: 3;
	border: none;
	transition: all 0.2s ease;
	}
	
.personal-icon-wrap {
	background: #EEF1F6;
}

.join-icon {
	width: 42rpx;
	height: 42rpx;
	opacity: 1;
	filter: none;
}

.join-info {
	display: flex;
	flex-direction: column;
	}
	
.join-title {
	color: #3D56C1;
	font-size: 28rpx;
	font-weight: 700;
	margin-bottom: 6rpx;
	text-shadow: 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.8);
	font-family: 'AlimamaShuHeiTi', sans-serif;
	letter-spacing: 1rpx;
	}
	
.personal-title {
	color: #4a89dc;
}

.join-desc {
	color: #5F6A8A;
	font-size: 22rpx;
	text-shadow: 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.6);
	}
	
.join-btn {
	background: linear-gradient(145deg, #394FC2, #4A67D7);
	color: #ffffff;
	font-size: 24rpx;
	padding: 0 24rpx;
	height: 60rpx;
	line-height: 60rpx;
	border-radius: 30rpx;
	margin: 0;
		position: relative;
	z-index: 2;
	box-shadow: 5rpx 5rpx 10rpx rgba(61, 86, 193, 0.3),
				-2rpx -2rpx 8rpx rgba(255, 255, 255, 0.3),
				inset 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.2);
	font-weight: 600;
	border: none;
	transition: all 0.2s ease;
	font-family: 'AlimamaShuHeiTi', sans-serif;
	text-align: center;
	letter-spacing: 1px;
	}
	
.join-btn:active {
	transform: scale(0.96);
	box-shadow: 2rpx 2rpx 4rpx rgba(61, 86, 193, 0.3),
				-1rpx -1rpx 4rpx rgba(255, 255, 255, 0.3),
				inset 1rpx 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.personal-join-btn {
	background: linear-gradient(145deg, #3e7ece, #4a89dc);
	box-shadow: 5rpx 5rpx 10rpx rgba(74, 137, 220, 0.3),
				-2rpx -2rpx 8rpx rgba(255, 255, 255, 0.3),
				inset 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.2);
	}
	
.hot-badge {
	position: absolute;
	top: -2rpx;
	right: 6rpx;
	background: #FF5757;
	color: #ffffff;
	font-size: 18rpx;
	font-weight: bold;
	padding: 4rpx 10rpx;
	border-radius: 0 0 10rpx 10rpx;
	box-shadow: 0 3rpx 6rpx rgba(255, 87, 87, 0.4);
	display: block;
	}
	
	/* 发布类目 */
	.category-section {
 margin: 0 20rpx 30rpx;
		background-color: #FFFFFF;
		border-radius: 16rpx;
 padding: 20rpx 25rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
		animation: fadeIn 0.6s ease-in-out;
		animation-delay: 0.3s;
		animation-fill-mode: both;
	}
	
	.category-section-title {
		font-size: 30rpx;
		color: #333;
		font-weight: 600;
 padding: 8rpx 0 16rpx;
		position: relative;
		display: flex;
		align-items: center;
	}
	
	.category-section-title::before {
		content: "";
		display: block;
		width: 8rpx;
		height: 32rpx;
		background-color: #0052CC;
		border-radius: 4rpx;
		margin-right: 16rpx;
	}
	
	.category-grid {
		display: flex;
		flex-wrap: wrap;
		margin: 0 -10rpx;
	}
	
	.category-item {
		width: 25%;
		display: flex;
		flex-direction: column;
		align-items: center;
 padding: 15rpx 0;
		position: relative;
		transition: all 0.2s;
		animation: fadeIn 0.3s ease-in-out;
		animation-fill-mode: both;
	}
	
	/* 简化延迟动画 */
	.category-item:nth-child(1) { animation-delay: 0.1s; }
	.category-item:nth-child(2) { animation-delay: 0.1s; }
	.category-item:nth-child(3) { animation-delay: 0.1s; }
	.category-item:nth-child(4) { animation-delay: 0.1s; }
	.category-item:nth-child(5) { animation-delay: 0.1s; }
	.category-item:nth-child(6) { animation-delay: 0.15s; }
	.category-item:nth-child(7) { animation-delay: 0.15s; }
	.category-item:nth-child(8) { animation-delay: 0.15s; }
	.category-item:nth-child(9) { animation-delay: 0.15s; }
	.category-item:nth-child(10) { animation-delay: 0.15s; }
	.category-item:nth-child(11) { animation-delay: 0.2s; }
	.category-item:nth-child(12) { animation-delay: 0.2s; }
	.category-item:nth-child(13) { animation-delay: 0.2s; }
	.category-item:nth-child(14) { animation-delay: 0.2s; }
	.category-item:nth-child(15) { animation-delay: 0.2s; }
	
	.category-item::after {
		content: "";
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 1;
		border-radius: 0;
	}
	
	.category-item-hover {
		transform: scale(0.95);
		background-color: rgba(0, 0, 0, 0.03);
	}
	
	.category-icon-wrapper {
 width: 100rpx;
	height: 100rpx;
	margin-bottom: 8rpx;
		position: relative;
		z-index: 2;
		transition: all 0.2s;
		border-radius: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: transparent;
		overflow: hidden;
	}
	
	.category-item-hover .category-icon-wrapper {
		transform: scale(0.9);
	}
	
	.category-icon {
 width: 75rpx;
	height: 75rpx;
	}
	
	.category-name {
		font-size: 24rpx;
		color: #333;
		text-align: center;
		position: relative;
		z-index: 2;
		font-weight: 500;
 margin-top: 6rpx;
	}
	
	.test-icon-wrapper {
		background-color: rgba(255, 107, 107, 0.1);
		border: 2rpx dashed #ff6b6b;
	}
	
	.test-icon {
		font-size: 32rpx;
		color: #ff4f4f;
		font-weight: 600;
	}
	
	.test-category-item {
		position: relative;
	}
	
	.test-badge {
		position: absolute;
		top: 6rpx;
		right: 8rpx;
		background: linear-gradient(135deg, #ff6b6b, #ff4f4f);
		color: white;
		font-size: 16rpx;
		padding: 2rpx 8rpx;
		border-radius: 8rpx;
		box-shadow: 0 2rpx 4rpx rgba(255, 107, 107, 0.3);
		font-weight: 500;
		z-index: 3;
	}

/* 商家入驻测试按钮样式 */
.merchant-test-category-item {
	position: relative;
}

.merchant-test-icon-wrapper {
	background-color: #0052CC;
}

.merchant-test-badge {
	position: absolute;
	top: -5px;
	right: -5px;
	background: #ff4d4f;
	color: #ffffff;
	font-size: 10px;
	height: 16px;
	min-width: 16px;
	line-height: 16px;
	text-align: center;
	border-radius: 8px;
	padding: 0 4px;
	font-weight: bold;
	}
</style> 