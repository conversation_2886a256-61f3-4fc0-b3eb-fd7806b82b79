<template>
  <view class="video-container">
    <!-- 顶部导航栏 -->
    <view class="header-area">
      <!-- 顶部安全区域 -->
      <view class="safe-area-top"></view>
      
      <!-- 自定义导航栏 -->
      <view class="custom-navbar">
        <view class="navbar-left" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="back-icon" style="filter: brightness(0) invert(1);"></image>
        </view>
        <view class="navbar-title">视频展示</view>
        <view class="navbar-right">
          <!-- 占位元素保持导航栏平衡 -->
        </view>
      </view>
    </view>
    
    <!-- 背景装饰 -->
    <view class="bg-decoration bg-circle-1"></view>
    <view class="bg-decoration bg-circle-2"></view>
    <view class="bg-decoration bg-circle-3"></view>
    
    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 视频上传区域 -->
      <view class="upload-section">
        <view class="section-header">
          <text class="section-title">宣传视频</text>
        </view>
        
        <view class="upload-area" @click="uploadVideo">
          <view class="upload-icon-wrapper">
            <text class="upload-icon">+</text>
          </view>
          <text class="upload-text">上传宣传视频</text>
          <text class="upload-desc">建议上传30秒以内的精彩视频，大小不超过30MB</text>
        </view>
        
        <view class="video-tips">
          <view class="tips-title">
            <text class="tips-icon">💡</text>
            <text class="tips-text">视频上传小提示</text>
          </view>
          <view class="tips-content">
            <text class="tips-item">· 视频必须是MP4格式</text>
            <text class="tips-item">· 建议使用横版视频，画面更美观</text>
            <text class="tips-item">· 视频内容需真实反映商家实际情况</text>
          </view>
        </view>
      </view>
      
      <!-- 视频列表 -->
      <view class="video-section">
        <view class="section-header">
          <text class="section-title">视频管理</text>
        </view>
        
        <view class="video-list" v-if="videos.length > 0">
          <view class="video-item" v-for="(video, index) in videos" :key="index">
            <view class="video-wrapper">
              <image :src="video.cover" mode="aspectFill" class="video-cover"></image>
              <view class="play-icon" @click="playVideo(video.url)">
                <text class="iconfont">▶</text>
              </view>
              <view class="video-duration">{{video.duration}}</view>
            </view>
            <view class="video-info">
              <view class="video-name">{{video.name}}</view>
              <view class="video-meta">
                <text class="upload-time">{{video.uploadTime}}</text>
                <text class="video-size">{{video.size}}</text>
              </view>
              <view class="video-actions">
                <view class="action-btn edit-btn" @click="editVideoInfo(index)">
                  <text class="action-text">编辑</text>
                </view>
                <view class="action-btn delete-btn" @click="deleteVideo(index)">
                  <text class="action-text">删除</text>
                </view>
                <view class="action-btn set-btn" @click="setMainVideo(index)" v-if="!video.isMain">
                  <text class="action-text">设为主视频</text>
                </view>
                <view class="status-tag" v-if="video.isMain">
                  <text class="status-text">主视频</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-state" v-else>
          <image class="empty-icon" src="/static/images/tabbar/无数据.png"></image>
          <text class="empty-text">暂无视频，快去添加吧</text>
        </view>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
    
    <!-- 视频编辑弹窗 -->
    <view class="modal-overlay" v-if="showEditModal" @click="hideModal"></view>
    <view class="video-modal" v-if="showEditModal">
      <view class="modal-header">
        <text class="modal-title">编辑视频信息</text>
      </view>
      <view class="modal-content">
        <view class="form-item">
          <text class="form-label required">视频名称</text>
          <input type="text" class="form-input" v-model="editVideoData.name" placeholder="请输入视频名称" maxlength="20" />
          <text class="input-counter">{{editVideoData.name.length}}/20</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">视频描述</text>
          <textarea class="form-textarea" v-model="editVideoData.description" placeholder="请输入视频描述（选填）" maxlength="100" />
          <text class="input-counter">{{editVideoData.description ? editVideoData.description.length : 0}}/100</text>
        </view>
      </view>
      <view class="modal-footer">
        <button class="modal-btn cancel" @click="hideModal">取消</button>
        <button class="modal-btn confirm" @click="saveVideoInfo" :disabled="!editVideoData.name.trim()">确认</button>
      </view>
    </view>
    
    <!-- 删除确认弹窗 -->
    <view class="modal-overlay" v-if="showDeleteModal"></view>
    <view class="delete-modal" v-if="showDeleteModal">
      <view class="modal-header">
        <text class="modal-title">删除视频</text>
      </view>
      <view class="modal-content">
        <view class="confirm-message">确定要删除这个视频吗？</view>
        <view class="confirm-warning">删除后无法恢复，请谨慎操作</view>
      </view>
      <view class="modal-footer">
        <button class="modal-btn cancel" @click="cancelDelete">取消</button>
        <button class="modal-btn confirm delete" @click="confirmDelete">删除</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      videos: [
        {
          id: 1,
          name: '店铺环境展示',
          description: '展示店铺整体环境和特色装修',
          url: 'https://example.com/video1.mp4',
          cover: '/static/images/video-cover-1.jpg',
          duration: '00:25',
          uploadTime: '2023-10-20',
          size: '12.5MB',
          isMain: true
        },
        {
          id: 2,
          name: '招牌菜制作过程',
          description: '展示特色小酥肉制作全过程',
          url: 'https://example.com/video2.mp4',
          cover: '/static/images/video-cover-2.jpg',
          duration: '00:18',
          uploadTime: '2023-10-21',
          size: '8.3MB',
          isMain: false
        }
      ],
      showEditModal: false,
      showDeleteModal: false,
      editVideoIndex: -1,
      editVideoData: {
        name: '',
        description: ''
      },
      deleteVideoIndex: -1,
      statusBarHeight: 20
    }
  },
  onLoad() {
    // 页面加载完成后的处理
    this.setStatusBarHeight();
  },
  methods: {
    // 设置状态栏高度
    setStatusBarHeight() {
      // 获取系统信息设置状态栏高度
      uni.getSystemInfo({
        success: (res) => {
          this.statusBarHeight = res.statusBarHeight;
          // 将状态栏高度设置为CSS变量
          if (typeof document !== 'undefined') {
            document.documentElement.style.setProperty('--status-bar-height', `${this.statusBarHeight}px`);
          }
        }
      });
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 上传视频
    uploadVideo() {
      // 在真实场景中，这里应该调用选择视频的API
      uni.showToast({
        title: '视频上传功能开发中',
        icon: 'none'
      });
      
      // 模拟上传视频，实际开发中应使用uni.chooseVideo
      setTimeout(() => {
        // 添加一个模拟的新视频
        const newVideo = {
          id: Date.now(),
          name: '新上传的视频',
          description: '',
          url: 'https://example.com/new-video.mp4',
          cover: '/static/images/video-cover-new.jpg',
          duration: '00:15',
          uploadTime: new Date().toISOString().split('T')[0],
          size: '6.2MB',
          isMain: false
        };
        
        this.videos.push(newVideo);
        
        // 打开编辑弹窗让用户编辑视频信息
        this.editVideoInfo(this.videos.length - 1);
      }, 1000);
    },
    
    // 播放视频
    playVideo(url) {
      // 在真实场景中，这里应该调用视频播放器
      uni.showToast({
        title: '视频播放器开发中',
        icon: 'none'
      });
    },
    
    // 编辑视频信息
    editVideoInfo(index) {
      this.editVideoIndex = index;
      const video = this.videos[index];
      this.editVideoData = {
        name: video.name,
        description: video.description || ''
      };
      this.showEditModal = true;
    },
    
    // 隐藏弹窗
    hideModal() {
      this.showEditModal = false;
    },
    
    // 保存视频信息
    saveVideoInfo() {
      if (!this.editVideoData.name.trim()) {
        uni.showToast({
          title: '请输入视频名称',
          icon: 'none'
        });
        return;
      }
      
      if (this.editVideoIndex >= 0 && this.editVideoIndex < this.videos.length) {
        this.videos[this.editVideoIndex].name = this.editVideoData.name;
        this.videos[this.editVideoIndex].description = this.editVideoData.description;
        
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        });
        
        this.hideModal();
      }
    },
    
    // 删除视频
    deleteVideo(index) {
      this.deleteVideoIndex = index;
      this.showDeleteModal = true;
    },
    
    // 取消删除
    cancelDelete() {
      this.showDeleteModal = false;
      this.deleteVideoIndex = -1;
    },
    
    // 确认删除
    confirmDelete() {
      if (this.deleteVideoIndex >= 0 && this.deleteVideoIndex < this.videos.length) {
        // 如果删除的是主视频，需要重新设置主视频
        if (this.videos[this.deleteVideoIndex].isMain && this.videos.length > 1) {
          // 找到非当前视频的第一个视频设为主视频
          for (let i = 0; i < this.videos.length; i++) {
            if (i !== this.deleteVideoIndex) {
              this.videos[i].isMain = true;
              break;
            }
          }
        }
        
        this.videos.splice(this.deleteVideoIndex, 1);
        
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        });
      }
      
      this.showDeleteModal = false;
      this.deleteVideoIndex = -1;
    },
    
    // 设置主视频
    setMainVideo(index) {
      // 取消之前的主视频标记
      for (let i = 0; i < this.videos.length; i++) {
        this.videos[i].isMain = false;
      }
      
      // 设置新的主视频
      this.videos[index].isMain = true;
      
      uni.showToast({
        title: '已设为主视频',
        icon: 'success'
      });
    }
  }
}
</script>

<style lang="scss">
.video-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(110rpx + var(--status-bar-height, 20px));
}

/* 顶部导航栏样式 */
.header-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
  color: #fff;
}

.safe-area-top {
  height: var(--status-bar-height, 20px);
  width: 100%;
}

.custom-navbar {
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
}

.navbar-left, .navbar-right {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 48rpx;
  height: 48rpx;
}

/* 背景装饰样式 */
.bg-decoration {
  position: absolute;
  z-index: -1;
  border-radius: 50%;
  opacity: 0.07;
  background-color: #0A84FF;
}

.bg-circle-1 {
  top: -100rpx;
  right: -150rpx;
  width: 400rpx;
  height: 400rpx;
}

.bg-circle-2 {
  top: 20%;
  left: -200rpx;
  width: 500rpx;
  height: 500rpx;
}

.bg-circle-3 {
  bottom: 10%;
  right: -100rpx;
  width: 300rpx;
  height: 300rpx;
}

/* 内容区域 */
.content-scroll {
  padding: 30rpx;
}

/* 视频上传区域 */
.upload-section, .video-section {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 20rpx;
  display: inline-block;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 8rpx;
  height: 32rpx;
  background-color: #0A84FF;
  border-radius: 4rpx;
}

/* 上传区域 */
.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #F5F8FC;
  border: 2rpx dashed #CCCCCC;
  border-radius: 16rpx;
  padding: 40rpx 0;
  margin-bottom: 30rpx;
}

.upload-icon-wrapper {
  width: 100rpx;
  height: 100rpx;
  background-color: rgba(10, 132, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.upload-icon {
  font-size: 50rpx;
  color: #0A84FF;
}

.upload-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.upload-desc {
  font-size: 24rpx;
  color: #999;
}

/* 提示说明 */
.video-tips {
  padding: 24rpx;
  background-color: rgba(10, 132, 255, 0.05);
  border-radius: 16rpx;
}

.tips-title {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.tips-icon {
  font-size: 28rpx;
  margin-right: 10rpx;
}

.tips-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.tips-content {
  display: flex;
  flex-direction: column;
}

.tips-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.8;
}

/* 视频列表 */
.video-list {
  margin-bottom: 20rpx;
}

.video-item {
  display: flex;
  margin-bottom: 30rpx;
  background-color: #F5F8FC;
  border-radius: 16rpx;
  overflow: hidden;
}

.video-wrapper {
  width: 240rpx;
  height: 180rpx;
  position: relative;
  flex-shrink: 0;
}

.video-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.play-icon .iconfont {
  color: #FFFFFF;
  font-size: 24rpx;
}

.video-duration {
  position: absolute;
  right: 10rpx;
  bottom: 10rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #FFFFFF;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
}

.video-info {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
}

.video-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-meta {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 20rpx;
}

.upload-time {
  margin-right: 20rpx;
}

.video-actions {
  display: flex;
  align-items: center;
  margin-top: auto;
}

.action-btn {
  padding: 8rpx 16rpx;
  background-color: rgba(10, 132, 255, 0.1);
  border-radius: 30rpx;
  margin-right: 16rpx;
}

.action-text {
  font-size: 24rpx;
  color: #0A84FF;
}

.edit-btn {
  background-color: rgba(10, 132, 255, 0.1);
}

.delete-btn {
  background-color: rgba(255, 59, 48, 0.1);
}

.delete-btn .action-text {
  color: #FF3B30;
}

.set-btn {
  background-color: rgba(52, 199, 89, 0.1);
}

.set-btn .action-text {
  color: #34C759;
}

.status-tag {
  padding: 8rpx 16rpx;
  background-color: rgba(255, 149, 0, 0.1);
  border-radius: 30rpx;
}

.status-text {
  font-size: 24rpx;
  color: #FF9500;
}

/* 空状态 */
.empty-state {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 100rpx;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
}

.video-modal, .delete-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  overflow: hidden;
  z-index: 1000;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);
}

.modal-header {
  padding: 30rpx;
  text-align: center;
  border-bottom: 2rpx solid #F2F2F7;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-content {
  padding: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
  position: relative;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.required::after {
  content: '*';
  color: #FF3B30;
  margin-left: 6rpx;
}

.form-input {
  width: 100%;
  height: 88rpx;
  background-color: #F5F8FC;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.form-textarea {
  width: 100%;
  height: 160rpx;
  background-color: #F5F8FC;
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.input-counter {
  position: absolute;
  right: 24rpx;
  bottom: 10rpx;
  font-size: 24rpx;
  color: #999;
}

/* 确认删除 */
.confirm-message {
  font-size: 28rpx;
  color: #333;
  text-align: center;
  margin-bottom: 20rpx;
}

.confirm-warning {
  font-size: 24rpx;
  color: #FF3B30;
  text-align: center;
}

.modal-footer {
  display: flex;
  border-top: 2rpx solid #F2F2F7;
}

.modal-btn {
  flex: 1;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  background-color: transparent;
}

.modal-btn::after {
  border: none;
}

.cancel {
  color: #999;
  border-right: 1px solid #F2F2F7;
}

.confirm {
  color: #0A84FF;
  font-weight: 500;
}

.delete {
  color: #FF3B30;
}

/* 适配暗黑模式 */
@media (prefers-color-scheme: dark) {
  .video-container {
    background-color: #1C1C1E;
  }
  
  .upload-section,
  .video-section,
  .video-modal, 
  .delete-modal {
    background-color: #2C2C2E;
  }
  
  .section-title,
  .modal-title,
  .form-label,
  .confirm-message,
  .upload-text,
  .tips-text {
    color: #FFFFFF;
  }
  
  .upload-area {
    background-color: #3A3A3C;
    border-color: #666666;
  }
  
  .video-item {
    background-color: #3A3A3C;
  }
  
  .video-name {
    color: #FFFFFF;
  }
  
  .form-input,
  .form-textarea {
    background-color: #3A3A3C;
    color: #FFFFFF;
  }
  
  .modal-header,
  .modal-footer {
    border-color: #3A3A3C;
  }
  
  .video-tips {
    background-color: rgba(10, 132, 255, 0.1);
  }
  
  .tips-item {
    color: #A8A8A8;
  }
}
</style> 