<template>
  <view class="group-buy-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <image class="back-icon" src="/static/images/tabbar/最新返回键.png" mode="aspectFit"></image>
        </view>
        <view class="navbar-title">拼团活动</view>
        <view class="navbar-right">
          <view class="search-btn" @click="navigateToSearch">
            <svg class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="22" height="22">
              <path d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6c3.2 3.2 8.4 3.2 11.6 0l43.6-43.5c3.2-3.2 3.2-8.4 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z" fill="#FFFFFF"></path>
            </svg>
          </view>
          <view class="close-btn" @click="goBack">
            <svg class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="22" height="22">
              <path d="M512 421.490332L331.349941 240.840273c-24.988383-24.988383-65.35828-24.988383-90.346664 0-24.988383 24.988383-24.988383 65.35828 0 90.346664L421.653336 512 240.840273 692.812059c-24.988383 24.988383-24.988383 65.35828 0 90.346664 24.988383 24.988383 65.35828 24.988383 90.346664 0L512 602.509668l180.650059 180.650059c24.988383 24.988383 65.35828 24.988383 90.346664 0 24.988383-24.988383 24.988383-65.35828 0-90.346664L602.346664 512l180.813063-180.812059c24.988383-24.988383 24.988383-65.35828 0-90.346664-24.988383-24.988383-65.35828-24.988383-90.346664 0L512 421.490332z" fill="#FFFFFF"></path>
            </svg>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 分类选项卡 -->
    <view class="category-tabs">
      <view 
        class="tab-item" 
        v-for="(tab, index) in tabs" 
        :key="index"
        :class="{ active: currentTabIndex === index }"
        @click="switchTab(index)"
      >
        <text>{{ tab.name }}</text>
      </view>
      <view class="tab-line" :style="tabLineStyle"></view>
    </view>
    
    <!-- 内容区域 -->
    <scroll-view 
      class="content-scroll" 
      scroll-y 
      @scrolltolower="loadMore"
    >
      <!-- 轮播广告 -->
      <swiper class="banner-swiper" indicator-dots autoplay circular
        :indicator-color="'rgba(255, 255, 255, 0.6)'"
        :indicator-active-color="'#ffffff'"
        v-if="currentTabIndex === 0">
        <swiper-item v-for="(banner, index) in banners" :key="index">
          <image class="banner-image" :src="banner.image" mode="aspectFill"></image>
        </swiper-item>
      </swiper>
      
      <!-- 分类图标 -->
      <view class="category-icons" v-if="currentTabIndex === 0">
        <view class="category-item" v-for="(category, index) in categories" :key="index" @click="filterByCategory(category)">
          <image class="category-icon" :src="category.icon" mode="aspectFill"></image>
          <text class="category-name">{{ category.name }}</text>
        </view>
      </view>
      
      <!-- 限时拼团 -->
      <view class="section limited-time" v-if="currentTabIndex === 0">
        <view class="section-header">
          <text class="section-title">限时拼团</text>
          <view class="countdown">
            <text class="countdown-label">距结束</text>
            <text class="countdown-time">{{ limitedTimeCountdown }}</text>
          </view>
        </view>
        
        <scroll-view class="limited-scroll" scroll-x>
          <view class="limited-items">
            <view class="limited-item" v-for="(item, index) in limitedTimeItems" :key="index" @click="navigateToDetail(item.id)">
              <image class="item-image" :src="item.image" mode="aspectFill"></image>
              <view class="item-info">
                <view class="item-title">{{ item.title }}</view>
                <view class="price-container">
                  <text class="group-price">¥{{ item.groupPrice }}</text>
                  <text class="daily-price">¥{{ item.dailyPrice }}</text>
                  <text class="original-price">¥{{ item.marketPrice }}</text>
                </view>
                <view class="group-info">
                  <text class="group-count">{{ item.groupCount }}人团</text>
                  <view class="join-btn">去拼团</view>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 拼团列表 -->
      <view class="section group-list-section">
        <view class="section-header" v-if="currentTabIndex === 0">
          <text class="section-title">热门拼团</text>
        </view>
        
        <view class="group-items">
          <view 
            class="group-item" 
            v-for="(item, index) in groupItems" 
            :key="index"
            @click="navigateToDetail(item.id)"
          >
            <view class="item-image-container">
              <image class="item-image" :src="item.image" mode="aspectFill"></image>
              <view class="item-tag" v-if="item.tag">{{ item.tag }}</view>
            </view>
            <view class="item-content">
              <view class="item-title">{{ item.title }}</view>
              <view class="item-desc">{{ item.description }}</view>
              
              <!-- 三种价格醒目比对 -->
              <view class="price-comparison">
                <view class="price-row">
                  <text class="price-label">拼团价</text>
                  <text class="price-value group-price">¥{{ item.groupPrice }}</text>
                </view>
                <view class="price-row">
                  <text class="price-label">日常价</text>
                  <text class="price-value daily-price">¥{{ item.dailyPrice }}</text>
                </view>
                <view class="price-row">
                  <text class="price-label">市场价</text>
                  <text class="price-value market-price">¥{{ item.marketPrice }}</text>
                </view>
                <view class="price-save">
                  <text class="save-text">拼团立省¥{{ (item.marketPrice - item.groupPrice).toFixed(2) }}</text>
                </view>
              </view>
              
              <view class="item-footer">
                <view class="group-progress">
                  <view class="avatars">
                    <image 
                      class="user-avatar" 
                      v-for="(avatar, avatarIndex) in item.userAvatars.slice(0, 3)" 
                      :key="avatarIndex" 
                      :src="avatar"
                    ></image>
                    <view class="avatar-count" v-if="item.joinCount > 3">+{{ item.joinCount - 3 }}</view>
                  </view>
                  <text class="progress-text">{{ item.joinCount }}人已参团</text>
                </view>
                <view class="action-btn">
                  <text>{{ item.groupCount }}人团 ¥{{ item.groupPrice }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 我的拼团 -->
      <view class="section my-groups-section" v-if="currentTabIndex === 1">
        <view v-if="myGroups.length === 0" class="empty-tip">
          <image class="empty-image" src="/static/images/empty-group.png" mode="aspectFit"></image>
          <text class="empty-text">您还没有参与任何拼团活动</text>
          <view class="empty-btn" @click="switchTab(0)">
            <text>去拼团</text>
          </view>
        </view>
        <view v-else class="my-group-list">
          <view 
            class="my-group-item" 
            v-for="(item, index) in myGroups" 
            :key="index"
            @click="navigateToDetail(item.id)"
          >
            <view class="item-image-container">
              <image class="item-image" :src="item.image" mode="aspectFill"></image>
              <view class="item-status-tag" :class="item.status">{{ getStatusText(item.status) }}</view>
            </view>
            <view class="item-content">
              <view class="item-title">{{ item.title }}</view>
              
              <!-- 三种价格比对 -->
              <view class="price-comparison compact">
                <view class="price-row">
                  <text class="price-label">拼团价</text>
                  <text class="price-value group-price">¥{{ item.groupPrice }}</text>
                </view>
                <view class="price-row">
                  <text class="price-label">日常价</text>
                  <text class="price-value daily-price">¥{{ item.dailyPrice }}</text>
                </view>
              </view>
              
              <view class="group-status">
                <view class="status-info">
                  <text class="status-text" :class="item.status">{{ getStatusText(item.status) }}</text>
                  <text class="status-desc" v-if="item.status === 'pending'">
                    还差{{ item.groupCount - item.joinCount }}人成团
                  </text>
                </view>
                <view class="status-action" v-if="item.status === 'pending'">
                  <view class="share-btn" @click.stop="shareGroup(item.id)">邀请好友</view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 加载更多提示 -->
      <view class="loading-more" v-if="loading">
        <text>加载中...</text>
      </view>
      
      <!-- 到底了提示 -->
      <view class="no-more" v-if="noMore">
        <text>已经到底啦~</text>
      </view>
    </scroll-view>
    
    <!-- 浮动按钮 -->
    <view class="float-btn" @click="scrollToTop">
      <view class="btn-icon">
        <svg class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="20" height="20">
          <path d="M512 352L188 676c-12.3 12.3-32.2 12.3-44.5 0-12.3-12.3-12.3-32.2 0-44.5l346-346c6.2-6.2 14.3-9.3 22.3-9.3s16.1 3.1 22.3 9.3l346 346c12.3 12.3 12.3 32.2 0 44.5-12.3 12.3-32.2 12.3-44.5 0L512 352z" fill="#FFFFFF"></path>
        </svg>
      </view>
    </view>
    
    <!-- 底部导航 -->
    <view class="bottom-tabbar">
      <view class="tab-item" @click="navigateToHome">
        <view class="tab-icon">
          <svg class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="24" height="24">
            <path d="M512 128L128 447.936V896h255.936V640H640v256h255.936V447.936L512 128z" :fill="isHomePage ? '#FF2C54' : '#666666'"></path>
          </svg>
        </view>
        <text class="tab-text" :class="{ active: isHomePage }">首页</text>
      </view>
      <view class="tab-item" @click="navigateToCategory">
        <view class="tab-icon">
          <svg class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="24" height="24">
            <path d="M320 320H192V192h128v128zm256 0H448V192h128v128zm256 0H704V192h128v128zM320 576H192V448h128v128zm256 0H448V448h128v128zm256 0H704V448h128v128zM320 832H192V704h128v128zm256 0H448V704h128v128zm256 0H704V704h128v128z" fill="#666666"></path>
          </svg>
        </view>
        <text class="tab-text">分类</text>
      </view>
      <view class="tab-item" @click="navigateToCart">
        <view class="tab-icon">
          <svg class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="24" height="24">
            <path d="M922.9 701.9H327.4l29.9-60.9 496.8-.9c16.8 0 31.2-12 34.2-28.6l68.8-385.1c1.8-10.1-.9-20.5-7.5-28.4a34.99 34.99 0 0 0-26.6-12.5l-632-2.1-5.4-25.4c-3.4-16.2-18-28-34.6-28H96.5a35.3 35.3 0 1 0 0 70.6h125.9L246 312l58.1 281.3-74.8 122.1a34.96 34.96 0 0 0-3 36.8c6 11.9 18.1 19.4 31.5 19.4h62.8a102.43 102.43 0 0 0-20.6 61.7c0 56.6 46 102.6 102.6 102.6s102.6-46 102.6-102.6c0-22.3-7.4-44-20.6-61.7h161.1a102.43 102.43 0 0 0-20.6 61.7c0 56.6 46 102.6 102.6 102.6s102.6-46 102.6-102.6c0-22.3-7.4-44-20.6-61.7H923c19.4 0 35.3-15.8 35.3-35.3a35.42 35.42 0 0 0-35.4-35.2zM305.7 253l575.8 1.9-56.4 315.8-452.3.8L305.7 253zm96.9 612.7c-17.4 0-31.6-14.2-31.6-31.6 0-17.4 14.2-31.6 31.6-31.6s31.6 14.2 31.6 31.6a31.6 31.6 0 0 1-31.6 31.6zm325.1 0c-17.4 0-31.6-14.2-31.6-31.6 0-17.4 14.2-31.6 31.6-31.6s31.6 14.2 31.6 31.6a31.6 31.6 0 0 1-31.6 31.6z" fill="#666666"></path>
          </svg>
        </view>
        <text class="tab-text">购物车</text>
      </view>
      <view class="tab-item" @click="navigateToMine">
        <view class="tab-icon">
          <svg class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="24" height="24">
            <path d="M858.5 763.6a374 374 0 0 0-80.6-119.5 375.63 375.63 0 0 0-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 0 0-80.6 119.5A371.7 371.7 0 0 0 136 901.8a8 8 0 0 0 8 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 0 0 8-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z" fill="#666666"></path>
          </svg>
        </view>
        <text class="tab-text">我的</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      refreshing: false,
      loading: false,
      noMore: false,
      currentTabIndex: 0,
      isHomePage: false,
      tabs: [
        { name: '全部拼团' },
        { name: '我的拼团' }
      ],
      banners: [
        { image: 'https://via.placeholder.com/750x300', link: '' },
        { image: 'https://via.placeholder.com/750x300', link: '' },
        { image: 'https://via.placeholder.com/750x300', link: '' }
      ],
      categories: [
        { name: '美食', icon: 'https://via.placeholder.com/60x60', id: 'food' },
        { name: '电器', icon: 'https://via.placeholder.com/60x60', id: 'appliance' },
        { name: '服饰', icon: 'https://via.placeholder.com/60x60', id: 'clothing' },
        { name: '美妆', icon: 'https://via.placeholder.com/60x60', id: 'beauty' },
        { name: '家居', icon: 'https://via.placeholder.com/60x60', id: 'home' }
      ],
      limitedTimeItems: [
        {
          id: 1,
          image: 'https://via.placeholder.com/200x200',
          title: '高端智能扫地机器人',
          marketPrice: '3999',
          dailyPrice: '2999',
          groupPrice: '1999',
          groupCount: 3
        },
        {
          id: 2,
          image: 'https://via.placeholder.com/200x200',
          title: '九阳豆浆机',
          marketPrice: '599',
          dailyPrice: '499',
          groupPrice: '299',
          groupCount: 2
        },
        {
          id: 3,
          image: 'https://via.placeholder.com/200x200',
          title: '小米空气净化器',
          marketPrice: '999',
          dailyPrice: '899',
          groupPrice: '699',
          groupCount: 3
        }
      ],
      limitedTimeCountdown: '23:59:59',
      groupItems: [
        {
          id: 2,
          image: 'https://via.placeholder.com/300x300',
          title: '九阳豆浆机',
          description: '破壁免滤双预约多功能',
          groupPrice: '299',
          dailyPrice: '499',
          marketPrice: '599',
          tag: '热门',
          groupCount: 2,
          joinCount: 156,
          userAvatars: [
            'https://via.placeholder.com/50x50',
            'https://via.placeholder.com/50x50',
            'https://via.placeholder.com/50x50',
            'https://via.placeholder.com/50x50'
          ],
          endTime: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 3,
          image: 'https://via.placeholder.com/300x300',
          title: '小米空气净化器',
          description: '除菌除醛除异味',
          groupPrice: '699',
          dailyPrice: '899',
          marketPrice: '999',
          tag: '爆款',
          groupCount: 3,
          joinCount: 78,
          userAvatars: [
            'https://via.placeholder.com/50x50',
            'https://via.placeholder.com/50x50'
          ],
          endTime: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 4,
          image: 'https://via.placeholder.com/300x300',
          title: '华为智能手表',
          description: '心率监测，睡眠分析，运动追踪',
          groupPrice: '899',
          dailyPrice: '1099',
          marketPrice: '1299',
          tag: '新品',
          groupCount: 2,
          joinCount: 45,
          userAvatars: [
            'https://via.placeholder.com/50x50',
            'https://via.placeholder.com/50x50',
            'https://via.placeholder.com/50x50'
          ],
          endTime: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 5,
          image: 'https://via.placeholder.com/300x300',
          title: '飞利浦电动牙刷',
          description: '声波震动，智能提醒，长效续航',
          groupPrice: '399',
          dailyPrice: '599',
          marketPrice: '699',
          tag: '限量',
          groupCount: 5,
          joinCount: 230,
          userAvatars: [
            'https://via.placeholder.com/50x50',
            'https://via.placeholder.com/50x50'
          ],
          endTime: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString()
        }
      ],
      myGroups: [
        {
          id: 6,
          image: 'https://via.placeholder.com/300x300',
          title: '小米空气净化器',
          description: '除菌除醛除异味',
          groupPrice: '699',
          dailyPrice: '899',
          marketPrice: '999',
          groupCount: 3,
          joinCount: 2,
          status: 'pending',
          endTime: new Date(Date.now() + 12 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 7,
          image: 'https://via.placeholder.com/300x300',
          title: '华为智能手表',
          description: '心率监测，睡眠分析，运动追踪',
          groupPrice: '899',
          dailyPrice: '1099',
          marketPrice: '1299',
          groupCount: 2,
          joinCount: 2,
          status: 'success',
          endTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 8,
          image: 'https://via.placeholder.com/300x300',
          title: '飞利浦电动牙刷',
          description: '声波震动，智能提醒，长效续航',
          groupPrice: '399',
          dailyPrice: '599',
          marketPrice: '699',
          groupCount: 5,
          joinCount: 3,
          status: 'failed',
          endTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
        }
      ]
    }
  },
  computed: {
    tabLineStyle() {
      const width = 100 / this.tabs.length
      const left = this.currentTabIndex * width
      return {
        width: width + '%',
        transform: `translateX(${left * 100}%)`
      }
    }
  },
  onLoad() {
    // 页面加载时获取数据
    this.fetchData()
    // 启动倒计时
    this.startCountdown()
    // 检查是否是首页
    const pages = getCurrentPages()
    if (pages.length === 1) {
      this.isHomePage = true
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },
    
    // 切换选项卡
    switchTab(index) {
      this.currentTabIndex = index
      this.fetchData()
    },
    
    // 加载更多
    loadMore() {
      if (this.loading || this.noMore) return
      
      this.loading = true
      
      // 模拟加载更多数据
      setTimeout(() => {
        if (this.currentTabIndex === 0) {
          // 添加更多拼团数据
          const moreItems = [
            {
              id: 9,
              image: 'https://via.placeholder.com/300x300',
              title: '德尔玛加湿器',
              description: '大容量，静音设计，智能恒湿',
              groupPrice: '199',
              dailyPrice: '249',
              marketPrice: '299',
              tag: '特惠',
              groupCount: 2,
              joinCount: 67,
              userAvatars: [
                'https://via.placeholder.com/50x50',
                'https://via.placeholder.com/50x50'
              ],
              endTime: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
              id: 10,
              image: 'https://via.placeholder.com/300x300',
              title: '小熊酸奶机',
              description: '家用全自动，陶瓷内胆',
              groupPrice: '159',
              dailyPrice: '199',
              marketPrice: '259',
              tag: '热卖',
              groupCount: 3,
              joinCount: 120,
              userAvatars: [
                'https://via.placeholder.com/50x50'
              ],
              endTime: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString()
            }
          ]
          
          this.groupItems = [...this.groupItems, ...moreItems]
        }
        
        this.noMore = true // 示例中加载一次后就没有更多数据
        this.loading = false
      }, 1500)
    },
    
    // 获取数据
    fetchData() {
      // 实际项目中，这里应该根据当前选中的选项卡调用API获取数据
      // 这里将在下一步实现
    },
    
    // 导航到详情页
    navigateToDetail(id) {
      uni.navigateTo({
        url: `/subPackages/activity-showcase/pages/detail/index?id=${id}&type=group`
      })
    },
    
    // 导航到搜索页
    navigateToSearch() {
      uni.navigateTo({
        url: '/subPackages/activity-showcase/pages/search/index'
      })
    },
    
    // 按分类筛选
    filterByCategory(category) {
      uni.showToast({
        title: `已选择${category.name}分类`,
        icon: 'none'
      })
      // 实际项目中应该根据分类筛选数据
    },
    
    // 启动倒计时
    startCountdown() {
      // 模拟倒计时
      let hours = 23
      let minutes = 59
      let seconds = 59
      
      setInterval(() => {
        seconds--
        if (seconds < 0) {
          seconds = 59
          minutes--
          if (minutes < 0) {
            minutes = 59
            hours--
            if (hours < 0) {
              hours = 23
            }
          }
        }
        
        this.limitedTimeCountdown = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      }, 1000)
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'pending': '拼团中',
        'success': '拼团成功',
        'failed': '拼团失败'
      }
      return statusMap[status] || ''
    },
    
    // 分享拼团
    shareGroup(id) {
      uni.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      })
    },
    
    // 滚动到顶部
    scrollToTop() {
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 300
      })
    },
    
    // 导航到首页
    navigateToHome() {
      if (!this.isHomePage) {
        uni.switchTab({
          url: '/pages/index/index'
        })
      }
    },
    
    // 导航到分类页
    navigateToCategory() {
      uni.switchTab({
        url: '/pages/category/index'
      })
    },
    
    // 导航到购物车
    navigateToCart() {
      uni.switchTab({
        url: '/pages/cart/index'
      })
    },
    
    // 导航到我的页面
    navigateToMine() {
      uni.switchTab({
        url: '/pages/mine/index'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.group-buy-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F5F5F7;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
  
  .navbar-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
    box-shadow: 0 4rpx 20rpx rgba(255, 44, 84, 0.3);
  }
  
  .navbar-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding-top: var(--status-bar-height, 25px);
    padding-left: 30rpx;
    padding-right: 30rpx;
    box-sizing: border-box;
    
    .back-btn {
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
      border-radius: 50%;
      padding: 16rpx;
    }
    
    .back-icon {
      width: 100%;
      height: 100%;
    }
    
    .navbar-title {
      font-size: 18px;
      font-weight: 600;
      color: #FFFFFF;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .navbar-right {
      width: 160rpx;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 20rpx;
      
      .search-btn, .close-btn {
        width: 64rpx;
        height: 64rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        border-radius: 50%;
      }
    }
  }
}

/* 分类选项卡 */
.category-tabs {
  position: relative;
  display: flex;
  background-color: #FFFFFF;
  height: 88rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  border-radius: 0 0 35rpx 35rpx;
  margin: 0 20rpx;
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
  
  .tab-item {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 28rpx;
    color: #666666;
    position: relative;
    
    &.active {
      color: #FF2C54;
      font-weight: 600;
    }
  }
  
  .tab-line {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 4rpx;
    background-color: #FF2C54;
    transition: transform 0.3s ease;
    border-radius: 2rpx;
  }
}

/* 内容区域 */
.content-scroll {
  flex: 1;
  width: 100%;
  padding: 0 20rpx;
  padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
  margin-top: 20rpx;
}

/* 轮播图 */
.banner-swiper {
  width: 100%;
  height: 300rpx;
  border-radius: 35rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 30rpx;
  
  .banner-image {
    width: 100%;
    height: 100%;
  }
}

/* 分类图标 */
.category-icons {
  display: flex;
  justify-content: space-around;
  padding: 40rpx 20rpx;
  background-color: #FFFFFF;
  margin-bottom: 30rpx;
  border-radius: 35rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);
  
  .category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .category-icon {
      width: 100rpx;
      height: 100rpx;
      border-radius: 35rpx;
      margin-bottom: 16rpx;
      box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.08);
      transition: transform 0.3s ease;
      
      &:active {
        transform: scale(0.95);
      }
    }
    
    .category-name {
      font-size: 24rpx;
      color: #333333;
      font-weight: 500;
    }
  }
}

/* 通用section样式 */
.section {
  background-color: #FFFFFF;
  margin-bottom: 30rpx;
  padding: 30rpx 0;
  border-radius: 35rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30rpx 20rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
      position: relative;
      padding-left: 20rpx;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 8rpx;
        height: 32rpx;
        background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
        border-radius: 4rpx;
      }
    }
    
    .countdown {
      display: flex;
      align-items: center;
      background: rgba(255, 44, 84, 0.08);
      border-radius: 20rpx;
      padding: 6rpx 12rpx;
      
      .countdown-label {
        font-size: 24rpx;
        color: #666666;
        margin-right: 10rpx;
      }
      
      .countdown-time {
        font-size: 24rpx;
        color: #FF2C54;
        font-weight: 600;
      }
    }
  }
}

/* 限时拼团 */
.limited-time {
  .limited-scroll {
    width: 100%;
    white-space: nowrap;
    
    .limited-items {
      padding: 0 20rpx;
      display: inline-block;
      
      .limited-item {
        display: inline-block;
        width: 240rpx;
        margin-right: 20rpx;
        background-color: #FFFFFF;
        border-radius: 25rpx;
        overflow: hidden;
        box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
        transition: transform 0.3s ease;
        
        &:active {
          transform: translateY(-5rpx);
        }
        
        .item-image {
          width: 240rpx;
          height: 240rpx;
          border-radius: 25rpx 25rpx 0 0;
        }
        
        .item-info {
          padding: 16rpx;
          
          .item-title {
            font-size: 26rpx;
            color: #333333;
            white-space: normal;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            height: 72rpx;
            line-height: 1.4;
            font-weight: 500;
          }
          
          .price-container {
            margin-top: 10rpx;
            display: flex;
            flex-wrap: wrap;
            align-items: baseline;
            
            .group-price {
              font-size: 32rpx;
              color: #FF2C54;
              font-weight: 600;
              margin-right: 10rpx;
            }
            
            .daily-price {
              font-size: 24rpx;
              color: #FF9500;
              text-decoration: line-through;
              margin-right: 10rpx;
            }
            
            .original-price {
              font-size: 22rpx;
              color: #999999;
              text-decoration: line-through;
            }
          }
          
          .group-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 16rpx;
            
            .group-count {
              font-size: 22rpx;
              color: #FF2C54;
              background-color: rgba(255, 44, 84, 0.1);
              padding: 4rpx 10rpx;
              border-radius: 20rpx;
            }
            
            .join-btn {
              font-size: 22rpx;
              color: #FFFFFF;
              background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
              padding: 6rpx 16rpx;
              border-radius: 20rpx;
              box-shadow: 0 4rpx 8rpx rgba(255, 44, 84, 0.2);
            }
          }
        }
      }
    }
  }
}

/* 加载更多和到底了提示 */
.loading-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 24rpx;
  color: #999999;
}

/* 拼团列表 */
.group-list-section {
  .group-items {
    padding: 0 20rpx;
    
    .group-item {
      display: flex;
      margin-bottom: 30rpx;
      padding: 20rpx;
      background-color: #FFFFFF;
      border-radius: 30rpx;
      box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.06);
      transition: transform 0.3s ease;
      
      &:active {
        transform: scale(0.98);
      }
      
      .item-image-container {
        position: relative;
        width: 220rpx;
        height: 220rpx;
        margin-right: 20rpx;
        
        .item-image {
          width: 100%;
          height: 100%;
          border-radius: 25rpx;
          box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
        }
        
        .item-tag {
          position: absolute;
          top: 10rpx;
          left: 10rpx;
          padding: 4rpx 12rpx;
          font-size: 20rpx;
          color: #FFFFFF;
          border-radius: 15rpx;
          background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
          box-shadow: 0 4rpx 8rpx rgba(255, 44, 84, 0.2);
        }
      }
      
      .item-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        
        .item-title {
          font-size: 28rpx;
          font-weight: 600;
          color: #333333;
          margin-bottom: 8rpx;
        }
        
        .item-desc {
          font-size: 24rpx;
          color: #666666;
          margin-bottom: 12rpx;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        .price-comparison {
          margin-bottom: 12rpx;
          padding: 12rpx;
          background: rgba(245, 245, 247, 0.6);
          border-radius: 20rpx;
          
          .price-row {
            display: flex;
            align-items: center;
            margin-bottom: 6rpx;
            
            .price-label {
              font-size: 22rpx;
              color: #666666;
              width: 80rpx;
            }
            
            .price-value {
              font-size: 24rpx;
              
              &.group-price {
                font-size: 32rpx;
                color: #FF2C54;
                font-weight: 600;
              }
              
              &.daily-price {
                color: #FF9500;
                text-decoration: line-through;
              }
              
              &.market-price {
                color: #999999;
                text-decoration: line-through;
              }
            }
          }
          
          .price-save {
            margin-top: 8rpx;
            
            .save-text {
              display: inline-block;
              font-size: 20rpx;
              color: #FFFFFF;
              background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
              padding: 4rpx 12rpx;
              border-radius: 15rpx;
              box-shadow: 0 4rpx 8rpx rgba(255, 44, 84, 0.2);
            }
          }
          
          &.compact {
            .price-row {
              display: inline-flex;
              margin-right: 20rpx;
              
              .price-label {
                width: auto;
                margin-right: 8rpx;
              }
            }
          }
        }
        
        .item-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: auto;
          
          .group-progress {
            display: flex;
            align-items: center;
            
            .avatars {
              display: flex;
              margin-right: 10rpx;
              
              .user-avatar {
                width: 40rpx;
                height: 40rpx;
                border-radius: 50%;
                border: 2rpx solid #FFFFFF;
                margin-left: -10rpx;
                box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
                
                &:first-child {
                  margin-left: 0;
                }
              }
              
              .avatar-count {
                width: 40rpx;
                height: 40rpx;
                border-radius: 50%;
                background-color: #F2F2F7;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 20rpx;
                color: #666666;
                margin-left: -10rpx;
                box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
              }
            }
            
            .progress-text {
              font-size: 22rpx;
              color: #666666;
            }
          }
          
          .action-btn {
            height: 60rpx;
            padding: 0 20rpx;
            background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
            border-radius: 30rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24rpx;
            color: #FFFFFF;
            box-shadow: 0 4rpx 12rpx rgba(255, 44, 84, 0.3);
          }
        }
      }
    }
  }
}

/* 我的拼团 */
.my-groups-section {
  .empty-tip {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 60rpx 0;
    
    .empty-image {
      width: 200rpx;
      height: 200rpx;
      margin-bottom: 20rpx;
      opacity: 0.8;
    }
    
    .empty-text {
      font-size: 28rpx;
      color: #999999;
      margin-bottom: 30rpx;
    }
    
    .empty-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 80rpx;
      width: 300rpx;
      border-radius: 40rpx;
      background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
      font-size: 28rpx;
      font-weight: 500;
      color: #FFFFFF;
      box-shadow: 0 8rpx 16rpx rgba(255, 44, 84, 0.2);
      transition: transform 0.3s ease;
      
      &:active {
        transform: scale(0.95);
      }
    }
  }
  
  .my-group-list {
    padding: 0 20rpx;
    
    .my-group-item {
      display: flex;
      margin-bottom: 20rpx;
      padding: 20rpx;
      background-color: #FFFFFF;
      border-radius: 30rpx;
      box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.06);
      transition: transform 0.3s ease;
      
      &:active {
        transform: scale(0.98);
      }
      
      .item-image-container {
        position: relative;
        width: 160rpx;
        height: 160rpx;
        margin-right: 20rpx;
        
        .item-image {
          width: 100%;
          height: 100%;
          border-radius: 25rpx;
          box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
        }
        
        .item-status-tag {
          position: absolute;
          top: 10rpx;
          left: 10rpx;
          padding: 4rpx 12rpx;
          font-size: 20rpx;
          color: #FFFFFF;
          border-radius: 15rpx;
          box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
          
          &.pending {
            background: linear-gradient(135deg, #FF9500 0%, #FFBD2E 100%);
          }
          
          &.success {
            background: linear-gradient(135deg, #34C759 0%, #30DB5B 100%);
          }
          
          &.failed {
            background: linear-gradient(135deg, #FF3B30 0%, #FF5E54 100%);
          }
        }
      }
      
      .item-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        
        .item-title {
          font-size: 28rpx;
          font-weight: 600;
          color: #333333;
          margin-bottom: 12rpx;
        }
        
        .group-status {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 12rpx;
          
          .status-info {
            .status-text {
              font-size: 24rpx;
              font-weight: 600;
              margin-right: 10rpx;
              
              &.pending {
                color: #FF9500;
              }
              
              &.success {
                color: #34C759;
              }
              
              &.failed {
                color: #FF3B30;
              }
            }
            
            .status-desc {
              font-size: 22rpx;
              color: #666666;
            }
          }
          
          .status-action {
            .share-btn {
              height: 56rpx;
              padding: 0 20rpx;
              background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
              border-radius: 28rpx;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 24rpx;
              color: #FFFFFF;
              box-shadow: 0 4rpx 12rpx rgba(255, 44, 84, 0.3);
            }
          }
        }
      }
    }
  }
}

/* 浮动按钮 */
.float-btn {
  position: fixed;
  right: 30rpx;
  bottom: 160rpx;
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(255, 44, 84, 0.3);
  z-index: 90;
  transform: translateZ(0);
  transition: transform 0.3s ease;
  
  &:active {
    transform: scale(0.9);
  }
}

/* 底部导航 */
.bottom-tabbar {
  position: fixed;
  left: 20rpx;
  right: 20rpx;
  bottom: 20rpx;
  height: 100rpx;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: space-around;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
  z-index: 99;
  padding-bottom: env(safe-area-inset-bottom);
  border-radius: 35rpx;
  
  .tab-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    
    .tab-icon {
      width: 48rpx;
      height: 48rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: transform 0.3s ease;
      
      &:active {
        transform: scale(0.9);
      }
    }
    
    .tab-text {
      font-size: 20rpx;
      color: #666666;
      margin-top: 4rpx;
      
      &.active {
        color: #FF2C54;
        font-weight: 500;
      }
    }
  }
}
</style> 