/**
 * 首页使用的错误处理示例
 */
import { api } from '../../utils/request.js';
import { handleError, createError, ErrorType } from '../../utils/errorHandler';
import { errorHandlerMixin } from '../../utils/errorHandler';
import pageErrorGuard from '../../utils/pageErrorGuard';

// 安全地加载数据
export async function safeLoadData(apiCall, defaultValue = [], options = {}) {
  try {
    const data = await apiCall();
    return data || defaultValue;
  } catch (error) {
    // 处理错误
    handleError(error, {
      ...options,
      showToast: options.showToast !== false, // 默认显示Toast
    });
    
    // 返回默认值
    return defaultValue;
  }
}

// 带错误处理的API示例
export const indexApi = {
  // 获取首页轮播图
  getBanners: async () => {
    return safeLoadData(
      () => api.getFeaturedPosts(),
      [
        { id: 1, image: '/static/images/banner/banner-1.png', url: '' },
        { id: 2, image: '/static/images/banner/banner-2.png', url: '' },
      ],
      { showToast: false } // 静默处理错误
    );
  },
  
  // 获取首页信息列表
  getInfoList: async (params) => {
    try {
      const result = await api.getPosts(params);
      return result;
    } catch (error) {
      // 网络错误特殊处理，显示本地缓存数据
      if (error.type === ErrorType.NETWORK) {
        // 尝试获取缓存数据
        const cachedData = uni.getStorageSync('info_list_cache');
        if (cachedData) {
          uni.showToast({
            title: '网络异常，显示缓存数据',
            icon: 'none'
          });
          return cachedData;
        }
      }
      
      // 其他错误正常处理
      handleError(error);
      return [];
    }
  },
  
  // 提交表单示例（包含完整错误处理流程）
  submitForm: async (formData) => {
    try {
      // 表单验证
      if (!formData.title) {
        throw createError({
          type: ErrorType.VALIDATION,
          message: '标题不能为空'
        });
      }
      
      // 调用API
      const result = await api.createPostRaw(formData); // 使用原始API
      
      // 成功处理
      uni.showToast({
        title: '发布成功',
        icon: 'success'
      });
      
      return result;
    } catch (error) {
      // 处理不同类型的错误
      switch (error.type) {
        case ErrorType.VALIDATION:
          // 表单验证错误
          handleError(error);
          break;
          
        case ErrorType.AUTH:
          // 认证错误，跳转登录
          handleError(error, {
            onAuthError: () => {
              uni.navigateTo({
                url: '/pages/auth/login'
              });
            }
          });
          break;
          
        case ErrorType.NETWORK:
          // 网络错误，提供重试选项
          handleError(error, {
            onNetworkError: () => {
              uni.showModal({
                title: '网络错误',
                content: '请检查网络连接后重试',
                confirmText: '重试',
                success: (res) => {
                  if (res.confirm) {
                    // 重试逻辑
                    indexApi.submitForm(formData);
                  }
                }
              });
            }
          });
          break;
          
        default:
          // 其他错误默认处理
          handleError(error);
      }
      
      // 返回空对象
      return null;
    }
  }
};

// 导出Vue组件错误处理混入
export const indexErrorMixin = {
  // 合并基本错误处理混入
  mixins: [errorHandlerMixin, pageErrorGuard],
  
  // 添加页面特定的错误处理
  methods: {
    // 处理轮播图加载错误
    handleBannerError() {
      // 加载默认图片
      return [
        { id: 1, image: '/static/images/banner/default.png', url: '' }
      ];
    },
    
    // 处理拉取刷新失败
    handleRefreshError(error) {
      handleError(error, {
        showToast: true,
        message: '刷新失败，请稍后重试'
      });
      
      // 停止刷新动画
      uni.stopPullDownRefresh();
    }
  }
}; 