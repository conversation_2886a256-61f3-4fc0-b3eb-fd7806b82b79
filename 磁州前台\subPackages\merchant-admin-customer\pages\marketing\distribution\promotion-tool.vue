<template>
  <view class="promotion-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">推广海报</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 页面内容 -->
    <view class="content-container">
      <view class="poster-preview">
        <image class="poster-image" :src="currentPoster" mode="widthFix"></image>
        <text class="preview-text">预览效果</text>
      </view>
      
      <view class="poster-options">
        <text class="section-title">选择海报模板</text>
        <view class="poster-templates">
          <view 
            v-for="(poster, index) in posters" 
            :key="index"
            class="template-item"
            :class="{active: currentPosterIndex === index}"
            @tap="selectPoster(index)"
          >
            <image class="template-image" :src="poster" mode="aspectFill"></image>
          </view>
        </view>
      </view>
      
      <view class="action-buttons">
        <button class="save-btn" @tap="savePoster">保存到相册</button>
        <button class="share-btn" @tap="sharePoster">分享海报</button>
      </view>
      
      <view class="poster-tips">
        <view class="tip-title">推广提示：</view>
        <view class="tip-item">1. 保存海报后可分享到朋友圈</view>
        <view class="tip-item">2. 顾客扫描海报上的二维码即可访问您的店铺</view>
        <view class="tip-item">3. 顾客通过您的海报购买商品，您可获得相应佣金</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      posters: [
        '/static/images/distribution/poster-1.png',
        '/static/images/distribution/poster-2.png',
        '/static/images/distribution/poster-3.png',
        '/static/images/distribution/poster-4.png'
      ],
      currentPosterIndex: 0
    }
  },
  computed: {
    currentPoster() {
      return this.posters[this.currentPosterIndex];
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    
    showHelp() {
      uni.showModal({
        title: '推广海报帮助',
        content: '推广海报是分销员推广店铺和商品的重要工具，通过分享海报可以获得更多客户，提高销售佣金。',
        showCancel: false
      });
    },
    
    selectPoster(index) {
      this.currentPosterIndex = index;
    },
    
    savePoster() {
      uni.showLoading({
        title: '保存中...'
      });
      
      // 模拟保存过程
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '已保存到相册',
          icon: 'success'
        });
      }, 1500);
    },
    
    sharePoster() {
      uni.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      });
    }
  }
}
</script>

<style lang="scss">
.promotion-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #9370DB, #8A2BE2);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(138, 43, 226, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 内容样式 */
.content-container {
  padding: 20px;
}

.poster-preview {
  background-color: #FFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.poster-image {
  width: 100%;
  max-width: 300px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-text {
  margin-top: 12px;
  font-size: 14px;
  color: #666;
}

.poster-options {
  background-color: #FFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.poster-templates {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding: 5px 0;
  -webkit-overflow-scrolling: touch;
}

.template-item {
  flex: 0 0 auto;
  width: 80px;
  height: 120px;
  margin-right: 12px;
  border-radius: 6px;
  overflow: hidden;
  border: 2px solid transparent;
  transition: border-color 0.3s;
}

.template-item.active {
  border-color: #8A2BE2;
}

.template-image {
  width: 100%;
  height: 100%;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.save-btn, .share-btn {
  width: 48%;
  height: 44px;
  line-height: 44px;
  border-radius: 22px;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
}

.save-btn {
  background-color: #8A2BE2;
  color: #FFF;
}

.share-btn {
  background-color: #fff;
  color: #8A2BE2;
  border: 1px solid #8A2BE2;
}

.poster-tips {
  background-color: #FFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.tip-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.tip-item {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}
</style> 