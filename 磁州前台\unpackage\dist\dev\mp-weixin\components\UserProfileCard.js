"use strict";
const common_vendor = require("../common/vendor.js");
const _sfc_main = {
  name: "UserProfileCard",
  props: {
    userInfo: {
      type: Object,
      default: () => ({
        nickname: "",
        avatar: "",
        userId: "",
        joinDate: "",
        bio: "",
        vipLevel: 0
      })
    }
  },
  methods: {
    handleEditProfile() {
      this.$emit("edit-profile");
    },
    handleDataAnalysis() {
      this.$emit("data-analysis");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $props.userInfo.avatar || "/static/images/default-avatar.png",
    b: common_vendor.t($props.userInfo.nickname || "磁州居民"),
    c: common_vendor.t($props.userInfo.vipLevel || 3),
    d: common_vendor.t($props.userInfo.userId || "88965"),
    e: common_vendor.t($props.userInfo.joinDate || "2023年6月"),
    f: common_vendor.t($props.userInfo.bio || "热爱生活，热爱磁州"),
    g: common_vendor.o((...args) => $options.handleEditProfile && $options.handleEditProfile(...args)),
    h: common_vendor.o((...args) => $options.handleDataAnalysis && $options.handleDataAnalysis(...args))
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/UserProfileCard.js.map
