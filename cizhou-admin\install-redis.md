# Redis 安装指南

## 📥 Windows 安装 Redis

### 方法一：使用 MSI 安装包（推荐）

1. **下载 Redis**
   - 访问：https://github.com/tporadowski/redis/releases
   - 下载最新的 `.msi` 文件（如 `Redis-x64-5.0.14.1.msi`）

2. **安装 Redis**
   - 双击 `.msi` 文件
   - 勾选 "Add the Redis installation folder to the PATH environment variable"
   - 勾选 "Run the Redis Server"
   - 设置端口为 6379（默认）
   - 设置密码为 `cizhou123456`

3. **验证安装**
   ```bash
   # 检查 Redis 服务
   sc query redis
   
   # 连接 Redis
   redis-cli -a cizhou123456
   
   # 测试命令
   ping
   set test "hello"
   get test
   exit
   ```

### 方法二：手动安装

1. **下载 Redis**
   - 访问：https://github.com/microsoftarchive/redis/releases
   - 下载 `Redis-x64-3.0.504.zip`

2. **解压和配置**
   ```bash
   # 解压到 C:\Redis
   # 编辑 redis.windows.conf
   # 设置密码：requirepass cizhou123456
   ```

3. **安装为 Windows 服务**
   ```bash
   # 以管理员身份运行命令提示符
   cd C:\Redis
   redis-server --service-install redis.windows.conf --loglevel verbose
   redis-server --service-start
   ```

## 🔧 配置文件

创建或编辑 `redis.conf`：

```conf
# 绑定地址
bind 127.0.0.1

# 端口
port 6379

# 密码
requirepass cizhou123456

# 持久化
save 900 1
save 300 10
save 60 10000

# 日志级别
loglevel notice

# 日志文件
logfile "redis.log"

# 数据库数量
databases 16
```

## 📋 配置信息

安装完成后的配置信息：
- **主机**: localhost
- **端口**: 6379
- **密码**: cizhou123456

## 🚨 常见问题

### 问题1：服务启动失败
```bash
# 检查端口占用
netstat -ano | findstr :6379

# 手动启动服务
net start redis

# 或者直接运行
redis-server redis.conf
```

### 问题2：连接被拒绝
```bash
# 检查防火墙设置
# 确保 Redis 服务正在运行
# 检查配置文件中的 bind 设置
```

### 问题3：密码认证失败
```bash
# 连接时指定密码
redis-cli -h localhost -p 6379 -a cizhou123456

# 或者连接后认证
redis-cli
auth cizhou123456
```

## ✅ 安装成功标志

- Redis 服务在 Windows 服务中显示为 "正在运行"
- 可以通过 `redis-cli` 连接
- 可以执行基本的 Redis 命令（SET、GET、PING）
