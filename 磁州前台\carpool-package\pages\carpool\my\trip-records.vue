<template>
  <view class="trip-records-container">
    <!-- 自定义导航栏 -->
    <view class="custom-header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="left-action" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="action-icon back-icon"></image>
        </view>
        <view class="title-area">
          <text class="page-title">行程记录</text>
        </view>
        <view class="right-action">
          <!-- 预留位置 -->
        </view>
      </view>
    </view>
    
    <!-- 切换标签 -->
    <view class="tab-container" :style="{ marginTop: (statusBarHeight + 44) + 'px' }">
      <view class="tab-item" 
        v-for="(tab, index) in tabs" 
        :key="index" 
        :class="{ active: currentTab === tab.value }"
        @click="switchTab(tab.value)">
        <text class="tab-text">{{tab.label}}</text>
      </view>
    </view>
    
    <!-- 行程列表 -->
    <scroll-view class="trip-list-scroll" scroll-y @scrolltolower="loadMore" refresher-enabled @refresherrefresh="onRefresh" :refresher-triggered="isRefreshing">
      <view class="trip-list" v-if="tripList.length > 0">
        <view class="trip-item" v-for="(item, index) in tripList" :key="item.id">
          <view class="trip-header">
            <view class="trip-type" :class="{'driver': item.role === 'driver', 'passenger': item.role === 'passenger'}">
              <text class="type-text">{{item.role === 'driver' ? '我是车主' : '我是乘客'}}</text>
            </view>
            <view class="trip-status" :class="item.status">
              <text class="status-text">{{getStatusText(item.status)}}</text>
            </view>
          </view>
          
          <view class="trip-route">
            <view class="route-point start">
              <view class="point-icon start"></view>
              <text class="point-name">{{item.startPoint}}</text>
            </view>
            <view class="route-line"></view>
            <view class="route-point end">
              <view class="point-icon end"></view>
              <text class="point-name">{{item.endPoint}}</text>
            </view>
          </view>
          
          <view class="trip-info">
            <view class="info-row">
              <text class="info-label">出发时间</text>
              <text class="info-value">{{item.departureTime}}</text>
            </view>
            <view class="info-row">
              <text class="info-label">{{item.role === 'driver' ? '乘客数' : '同行人数'}}</text>
              <text class="info-value">{{item.passengerCount}}人</text>
            </view>
            <view class="info-row">
              <text class="info-label">费用</text>
              <text class="info-value price">¥{{item.price.toFixed(2)}}</text>
            </view>
          </view>
          
          <view class="trip-actions">
            <button class="action-btn detail" @click="viewTripDetail(item)">查看详情</button>
            <button class="action-btn contact" v-if="item.status === 'ongoing'" @click="contactDriver(item)">联系{{item.role === 'driver' ? '乘客' : '车主'}}</button>
            <button class="action-btn rate" v-if="item.status === 'completed' && !item.isRated" @click="rateTrip(item)">评价</button>
          </view>
        </view>
      </view>
      
      <!-- 无数据提示 -->
      <view class="empty-state" v-if="tripList.length === 0 && !isLoading">
        <image src="/static/images/empty/no-trips.png" mode="aspectFit" class="empty-image"></image>
        <text class="empty-text">暂无行程记录</text>
      </view>
      
      <!-- 加载状态 -->
      <view class="loading-state" v-if="isLoading && !isRefreshing">
        <text class="loading-text">加载中...</text>
      </view>
      
      <!-- 到底提示 -->
      <view class="list-bottom" v-if="tripList.length > 0 && !hasMore">
        <text class="bottom-text">— 已经到底啦 —</text>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 状态栏高度
const statusBarHeight = ref(20)

// 数据定义
const tabs = ref([
  { label: '全部', value: 'all' },
  { label: '进行中', value: 'ongoing' },
  { label: '已完成', value: 'completed' },
  { label: '已取消', value: 'canceled' }
])
const currentTab = ref('all')
const tripList = ref([])
const page = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)
const isLoading = ref(false)
const isRefreshing = ref(false)

// 生命周期钩子
onMounted(() => {
  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 20
  
  loadTrips()
})

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 切换标签
const switchTab = (tab) => {
  if (currentTab.value === tab) return
  currentTab.value = tab
  page.value = 1
  tripList.value = []
  hasMore.value = true
  loadTrips()
}

// 加载行程数据
const loadTrips = () => {
  if (isLoading.value) return
  isLoading.value = true
  
  // 模拟数据加载
  setTimeout(() => {
    // 模拟行程数据
    const mockTrips = [
      {
        id: '1001',
        role: 'driver',
        status: 'completed',
        startPoint: '磁县政府',
        endPoint: '邯郸火车站',
        departureTime: '2023-11-15 10:30',
        passengerCount: 3,
        price: 35.00,
        isRated: true
      },
      {
        id: '1002',
        role: 'passenger',
        status: 'ongoing',
        startPoint: '磁县老城区',
        endPoint: '邯郸科技学院',
        departureTime: '2023-12-05 07:30',
        passengerCount: 1,
        price: 25.00,
        isRated: false
      },
      {
        id: '1003',
        role: 'driver',
        status: 'canceled',
        startPoint: '磁县新城区',
        endPoint: '邯郸东站',
        departureTime: '2023-11-05 09:15',
        passengerCount: 0,
        price: 30.00,
        isRated: false
      }
    ]
    
    // 根据当前标签筛选数据
    let filteredTrips = mockTrips
    if (currentTab.value !== 'all') {
      filteredTrips = mockTrips.filter(item => item.status === currentTab.value)
    }
    
    if (page.value === 1) {
      tripList.value = filteredTrips
    } else {
      tripList.value = [...tripList.value, ...filteredTrips]
    }
    
    // 模拟没有更多数据
    if (page.value >= 2) {
      hasMore.value = false
    }
    
    isLoading.value = false
    isRefreshing.value = false
  }, 1000)
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'ongoing': '进行中',
    'completed': '已完成',
    'canceled': '已取消'
  }
  return statusMap[status] || '未知状态'
}

// 加载更多
const loadMore = () => {
  if (hasMore.value && !isLoading.value) {
    page.value++
    loadTrips()
  }
}

// 下拉刷新
const onRefresh = () => {
  isRefreshing.value = true
  page.value = 1
  hasMore.value = true
  loadTrips()
}

// 查看行程详情
const viewTripDetail = (item) => {
  uni.navigateTo({
    url: `/carpool-package/pages/carpool/trip-detail/index?id=${item.id}`
  })
}

// 联系车主/乘客
const contactDriver = (item) => {
  uni.showModal({
    title: `联系${item.role === 'driver' ? '乘客' : '车主'}`,
    content: `是否拨打${item.role === 'driver' ? '乘客' : '车主'}电话？`,
    success: (res) => {
      if (res.confirm) {
        uni.makePhoneCall({
          phoneNumber: '13812345678',
          fail: () => {
            uni.showToast({
              title: '拨打电话失败',
              icon: 'none'
            })
          }
        })
      }
    }
  })
}

// 评价行程
const rateTrip = (item) => {
  uni.navigateTo({
    url: `/carpool-package/pages/carpool/rating/index?id=${item.id}&role=${item.role}`
  })
}

// 暴露方法给外部访问
defineExpose({
  loadTrips,
  switchTab
})
</script>

<style lang="scss">
.trip-records-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(90rpx + var(--status-bar-height, 40px));
}

/* 自定义导航栏 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
}

.header-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
}

.left-action, .right-action {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  width: 24px;
  height: 24px;
}

.back-icon {
  width: 24px;
  height: 24px;
  /* 图标是黑色的，需要转为白色 */
  filter: brightness(0) invert(1);
}

.title-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 切换标签 */
.tab-container {
  display: flex;
  background-color: #FFFFFF;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.tab-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;
  position: relative;
}

.tab-item.active {
  background-color: rgba(10, 132, 255, 0.1);
}

.tab-item.active .tab-text {
  color: #0A84FF;
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #0A84FF;
}

.tab-text {
  font-size: 28rpx;
  color: #666666;
}

/* 行程列表 */
.trip-list-scroll {
  height: calc(100vh - 90rpx - var(--status-bar-height, 40px) - 120rpx);
}

.trip-list {
  padding: 0 20rpx;
}

.trip-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.trip-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.trip-type {
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  background-color: #F2F2F7;
}

.trip-type.driver {
  background-color: rgba(10, 132, 255, 0.1);
}

.trip-type.passenger {
  background-color: rgba(52, 199, 89, 0.1);
}

.type-text {
  font-size: 24rpx;
  color: #666666;
}

.trip-type.driver .type-text {
  color: #0A84FF;
}

.trip-type.passenger .type-text {
  color: #34C759;
}

.trip-status {
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

.trip-status.ongoing {
  background-color: rgba(255, 159, 10, 0.1);
}

.trip-status.completed {
  background-color: rgba(52, 199, 89, 0.1);
}

.trip-status.canceled {
  background-color: rgba(142, 142, 147, 0.1);
}

.status-text {
  font-size: 24rpx;
}

.trip-status.ongoing .status-text {
  color: #FF9F0A;
}

.trip-status.completed .status-text {
  color: #34C759;
}

.trip-status.canceled .status-text {
  color: #8E8E93;
}

/* 行程路线 */
.trip-route {
  background-color: #F9F9F9;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.route-point {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.route-point.end {
  margin-bottom: 0;
}

.point-icon {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.point-icon.start {
  background-color: #34C759;
}

.point-icon.end {
  background-color: #FF3B30;
}

.point-name {
  font-size: 28rpx;
  color: #333333;
}

.route-line {
  width: 2rpx;
  height: 30rpx;
  background-color: #DDDDDD;
  margin-left: 9rpx;
  margin-bottom: 16rpx;
}

/* 行程信息 */
.trip-info {
  margin-bottom: 20rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 26rpx;
  color: #666666;
}

.info-value {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
}

.info-value.price {
  color: #FF3B30;
}

/* 行程操作 */
.trip-actions {
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #F2F2F7;
  padding-top: 20rpx;
}

.action-btn {
  font-size: 26rpx;
  padding: 8rpx 24rpx;
  border-radius: 20rpx;
  margin-left: 16rpx;
  line-height: 1.5;
}

.action-btn.detail {
  background-color: rgba(142, 142, 147, 0.1);
  color: #666666;
}

.action-btn.contact {
  background-color: rgba(10, 132, 255, 0.1);
  color: #0A84FF;
}

.action-btn.rate {
  background-color: rgba(255, 159, 10, 0.1);
  color: #FF9F0A;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #8E8E93;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  padding: 30rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #8E8E93;
}

/* 到底提示 */
.list-bottom {
  text-align: center;
  padding: 30rpx 0;
}

.bottom-text {
  font-size: 24rpx;
  color: #8E8E93;
}
</style> 