<template>
  <view class="orders-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="navigateBack">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M19 12H5M12 19l-7-7 7-7" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        <view class="navbar-title">分销订单</view>
        <view class="navbar-right">
          <view class="search-btn" @click="showSearch">
            <svg class="icon" viewBox="0 0 24 24" width="20" height="20">
              <circle cx="11" cy="11" r="8" stroke="#FFFFFF" stroke-width="2"></circle>
              <path d="m21 21-4.35-4.35" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 内容区域 -->
    <view class="content">
      <!-- 订单统计卡片 -->
      <view class="stats-card" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-number">{{ orderStats.totalCount }}</text>
            <text class="stat-label">总订单</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ orderStats.todayCount }}</text>
            <text class="stat-label">今日新增</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ orderStats.pendingCount }}</text>
            <text class="stat-label">待处理</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">¥{{ orderStats.totalCommission }}</text>
            <text class="stat-label">总佣金</text>
          </view>
        </view>
      </view>
      
      <!-- 订单筛选标签 -->
      <view class="filter-tabs" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: '#FFFFFF',
        padding: '20rpx 30rpx',
        marginBottom: '30rpx'
      }">
        <scroll-view class="tabs-scroll" scroll-x>
          <view class="tabs-container">
            <view 
              v-for="(tab, index) in orderTabs" 
              :key="index"
              class="tab-item"
              :class="{ active: currentTab === index }"
              @click="switchTab(index)"
            >
              <text>{{ tab.name }}</text>
              <view class="tab-indicator" v-if="currentTab === index"></view>
            </view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 订单列表 -->
      <view class="orders-list">
        <view 
          v-for="order in filteredOrders"
          :key="order.id"
          class="order-card"
          :style="{
            borderRadius: '35px',
            boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
            background: '#FFFFFF',
            padding: '30rpx',
            marginBottom: '30rpx'
          }"
          @click="viewOrderDetail(order)"
        >
          <!-- 订单头部 -->
          <view class="order-header">
            <view class="order-info">
              <text class="order-number">订单号：{{ order.orderNumber }}</text>
              <text class="order-time">{{ order.createTime }}</text>
            </view>
            <view class="order-status" :style="{
              background: getStatusBackground(order.status),
              borderRadius: '20rpx',
              padding: '8rpx 16rpx'
            }">
              <text :style="{ color: getStatusColor(order.status) }">{{ getStatusText(order.status) }}</text>
            </view>
          </view>
          
          <!-- 商品信息 -->
          <view class="product-info">
            <image class="product-image" :src="order.productImage" mode="aspectFill"></image>
            <view class="product-details">
              <text class="product-name">{{ order.productName }}</text>
              <view class="product-specs">
                <text class="product-spec">{{ order.specification }}</text>
              </view>
              <view class="price-quantity">
                <text class="product-price">¥{{ order.productPrice }}</text>
                <text class="product-quantity">x{{ order.quantity }}</text>
              </view>
            </view>
          </view>
          
          <!-- 订单底部 -->
          <view class="order-footer">
            <view class="customer-info">
              <text class="customer-label">购买用户：</text>
              <text class="customer-name">{{ order.customerName }}</text>
            </view>
            <view class="commission-info">
              <text class="commission-label">预计佣金：</text>
              <text class="commission-amount">¥{{ order.commission }}</text>
            </view>
          </view>
          
          <!-- 操作按钮 -->
          <view class="order-actions" v-if="getOrderActions(order.status).length > 0">
            <view 
              v-for="action in getOrderActions(order.status)"
              :key="action.key"
              class="action-btn"
              :style="{
                background: action.primary ? 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)' : 'transparent',
                border: action.primary ? 'none' : '2rpx solid #AC39FF',
                borderRadius: '25rpx',
                padding: '12rpx 24rpx'
              }"
              @click.stop="handleAction(action.key, order)"
            >
              <text :style="{ color: action.primary ? '#FFFFFF' : '#AC39FF' }">{{ action.text }}</text>
            </view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-state" v-if="filteredOrders.length === 0">
          <view class="empty-content" :style="{
            borderRadius: '35px',
            background: '#FFFFFF',
            padding: '80rpx 40rpx',
            textAlign: 'center'
          }">
            <svg class="empty-icon" viewBox="0 0 24 24" width="80" height="80">
              <path d="M9 17h6M9 13h6M9 9h6M5 21V5a2 2 0 012-2h10a2 2 0 012 2v16l-3-2-2 2-2-2-2 2-2-2-3 2z" stroke="#C7C7CC" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
            <text class="empty-text">暂无{{ orderTabs[currentTab].name }}订单</text>
            <view class="empty-action" @click="navigateTo('/subPackages/activity-showcase/pages/distribution/index')" :style="{
              background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)',
              borderRadius: '30rpx',
              padding: '16rpx 32rpx',
              marginTop: '40rpx'
            }">
              <text style="color: #FFFFFF;">去推广商品</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 页面状态
const currentTab = ref(0);
const searchKeyword = ref('');

// 订单统计
const orderStats = ref({
  totalCount: 156,
  todayCount: 12,
  pendingCount: 8,
  totalCommission: 2456.78
});

// 订单标签
const orderTabs = ref([
  { name: '全部', status: 'all' },
  { name: '待付款', status: 'pending_payment' },
  { name: '待发货', status: 'pending_delivery' },
  { name: '已发货', status: 'delivered' },
  { name: '已完成', status: 'completed' },
  { name: '已失效', status: 'invalid' }
]);

// 订单列表
const orderList = ref([
  {
    id: 'O001',
    orderNumber: '20240101001',
    status: 'completed',
    createTime: '2024-01-01 14:30',
    productName: 'iPhone 15 Pro 深空黑 256GB',
    productImage: 'https://via.placeholder.com/120',
    specification: '深空黑 256GB',
    productPrice: 8999,
    quantity: 1,
    customerName: '张三',
    commission: 449.95
  },
  {
    id: 'O002',
    orderNumber: '20240102001',
    status: 'pending_payment',
    createTime: '2024-01-02 10:15',
    productName: '华为Mate 60 Pro 雅川青 512GB',
    productImage: 'https://via.placeholder.com/120',
    specification: '雅川青 512GB',
    productPrice: 6999,
    quantity: 1,
    customerName: '李四',
    commission: 349.95
  },
  {
    id: 'O003',
    orderNumber: '20240103001',
    status: 'pending_delivery',
    createTime: '2024-01-03 16:20',
    productName: '小米14 Ultra 白色 1TB',
    productImage: 'https://via.placeholder.com/120',
    specification: '白色 1TB',
    productPrice: 5999,
    quantity: 2,
    customerName: '王五',
    commission: 599.90
  }
]);

// 计算属性
const filteredOrders = computed(() => {
  const status = orderTabs.value[currentTab.value].status;
  if (status === 'all') {
    return orderList.value;
  }
  return orderList.value.filter(order => order.status === status);
});

// 方法
const navigateBack = () => {
  uni.navigateBack();
};

const showSearch = () => {
  uni.showToast({
    title: '搜索功能开发中',
    icon: 'none'
  });
};

const switchTab = (index) => {
  currentTab.value = index;
};

const getStatusColor = (status) => {
  switch(status) {
    case 'pending_payment': return '#FF9500';
    case 'pending_delivery': return '#5AC8FA';
    case 'delivered': return '#34C759';
    case 'completed': return '#AC39FF';
    case 'invalid': return '#8E8E93';
    default: return '#333333';
  }
};

const getStatusBackground = (status) => {
  switch(status) {
    case 'pending_payment': return 'rgba(255,149,0,0.1)';
    case 'pending_delivery': return 'rgba(90,200,250,0.1)';
    case 'delivered': return 'rgba(52,199,89,0.1)';
    case 'completed': return 'rgba(172,57,255,0.1)';
    case 'invalid': return 'rgba(142,142,147,0.1)';
    default: return 'rgba(51,51,51,0.1)';
  }
};

const getStatusText = (status) => {
  switch(status) {
    case 'pending_payment': return '待付款';
    case 'pending_delivery': return '待发货';
    case 'delivered': return '已发货';
    case 'completed': return '已完成';
    case 'invalid': return '已失效';
    default: return '未知状态';
  }
};

const getOrderActions = (status) => {
  switch(status) {
    case 'pending_payment':
      return [
        { key: 'remind', text: '提醒付款', primary: false },
        { key: 'cancel', text: '取消订单', primary: false }
      ];
    case 'pending_delivery':
      return [
        { key: 'track', text: '查看物流', primary: true }
      ];
    case 'delivered':
      return [
        { key: 'track', text: '查看物流', primary: true }
      ];
    default:
      return [];
  }
};

const handleAction = (action, order) => {
  switch(action) {
    case 'remind':
      uni.showToast({ title: '已提醒用户付款', icon: 'success' });
      break;
    case 'cancel':
      uni.showModal({
        title: '确认取消',
        content: '确定要取消这个订单吗？',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({ title: '订单已取消', icon: 'success' });
          }
        }
      });
      break;
    case 'track':
      uni.showToast({ title: '查看物流功能开发中', icon: 'none' });
      break;
  }
};

const viewOrderDetail = (order) => {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/distribution/order-detail/index?id=${order.id}`
  });
};

const navigateTo = (url) => {
  uni.navigateTo({ url });
};

onMounted(() => {
  // 页面加载时的初始化操作
});
</script>

<style lang="scss" scoped>
.orders-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(180deg, #F2F2F7 0%, #E8E8ED 100%);
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  z-index: 100;
  
  .navbar-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%);
  }
  
  .navbar-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 62px;
    margin-top: var(--status-bar-height, 25px);
    padding: 0 30rpx;
    
    .back-btn, .search-btn {
      width: 80rpx;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.2);
      
      .icon {
        width: 48rpx;
        height: 48rpx;
      }
    }
    
    .navbar-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #FFFFFF;
    }
    
    .navbar-right {
      width: 80rpx;
      display: flex;
      justify-content: flex-end;
    }
  }
}

.content {
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
  padding: 30rpx;
  flex: 1;
}

.stats-card {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20rpx;
    
    .stat-item {
      text-align: center;
      
      .stat-number {
        display: block;
        font-size: 32rpx;
        font-weight: 600;
        color: #FFFFFF;
        margin-bottom: 8rpx;
      }
      
      .stat-label {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
}

.filter-tabs {
  .tabs-scroll {
    width: 100%;
    
    .tabs-container {
      display: flex;
      white-space: nowrap;
      
      .tab-item {
        position: relative;
        padding: 20rpx 30rpx;
        margin-right: 20rpx;
        
        text {
          font-size: 28rpx;
          color: #8E8E93;
          transition: color 0.3s ease;
        }
        
        .tab-indicator {
          position: absolute;
          bottom: 10rpx;
          left: 50%;
          transform: translateX(-50%);
          width: 40rpx;
          height: 4rpx;
          border-radius: 2rpx;
          background: linear-gradient(90deg, #AC39FF 0%, #B87AFF 100%);
        }
        
        &.active text {
          color: #AC39FF;
          font-weight: 500;
        }
      }
    }
  }
}

.order-card {
  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20rpx;
    
    .order-info {
      .order-number {
        display: block;
        font-size: 28rpx;
        color: #333333;
        font-weight: 500;
        margin-bottom: 8rpx;
      }
      
      .order-time {
        font-size: 24rpx;
        color: #8E8E93;
      }
    }
    
    .order-status text {
      font-size: 24rpx;
      font-weight: 500;
    }
  }
  
  .product-info {
    display: flex;
    margin-bottom: 20rpx;
    
    .product-image {
      width: 120rpx;
      height: 120rpx;
      border-radius: 16rpx;
      margin-right: 20rpx;
    }
    
    .product-details {
      flex: 1;
      
      .product-name {
        display: block;
        font-size: 28rpx;
        color: #333333;
        font-weight: 500;
        margin-bottom: 12rpx;
        line-height: 1.4;
      }
      
      .product-specs {
        margin-bottom: 12rpx;
        
        .product-spec {
          font-size: 24rpx;
          color: #8E8E93;
        }
      }
      
      .price-quantity {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .product-price {
          font-size: 32rpx;
          color: #FF3B69;
          font-weight: 600;
        }
        
        .product-quantity {
          font-size: 24rpx;
          color: #8E8E93;
        }
      }
    }
  }
  
  .order-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
    border-top: 1rpx solid #F0F0F0;
    margin-bottom: 20rpx;
    
    .customer-info, .commission-info {
      .customer-label, .commission-label {
        font-size: 24rpx;
        color: #8E8E93;
      }
      
      .customer-name {
        font-size: 26rpx;
        color: #333333;
        font-weight: 500;
      }
      
      .commission-amount {
        font-size: 28rpx;
        color: #AC39FF;
        font-weight: 600;
      }
    }
  }
  
  .order-actions {
    display: flex;
    justify-content: flex-end;
    gap: 20rpx;
    
    .action-btn {
      text {
        font-size: 26rpx;
        font-weight: 500;
      }
    }
  }
}

.empty-state {
  .empty-content {
    .empty-icon {
      margin-bottom: 30rpx;
    }
    
    .empty-text {
      display: block;
      font-size: 28rpx;
      color: #8E8E93;
      margin-bottom: 20rpx;
    }
    
    .empty-action text {
      font-size: 28rpx;
      font-weight: 500;
    }
  }
}
</style>