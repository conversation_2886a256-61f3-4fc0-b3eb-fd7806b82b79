# MySQL 8.0 安装指南

## 📥 下载和安装

### Windows 安装步骤：

1. **下载 MySQL**
   - 访问：https://dev.mysql.com/downloads/mysql/
   - 选择 "MySQL Installer for Windows"
   - 下载 `mysql-installer-web-community-8.0.xx.x.msi`

2. **运行安装程序**
   - 双击下载的 `.msi` 文件
   - 选择 "Developer Default" 安装类型
   - 点击 "Next" 继续

3. **配置 MySQL**
   - **端口**：保持默认 3306
   - **Root 密码**：设置为 `cizhou123456`
   - **认证方式**：选择 "Use Legacy Authentication Method"
   - **Windows 服务**：勾选 "Start the MySQL Server at System Startup"

4. **完成安装**
   - 点击 "Execute" 开始安装
   - 等待安装完成
   - 测试连接是否成功

## 🔧 验证安装

打开命令提示符，运行：

```bash
# 检查 MySQL 服务状态
sc query mysql

# 连接 MySQL
mysql -uroot -pcizhou123456

# 在 MySQL 中执行
SHOW DATABASES;
EXIT;
```

## 📋 配置信息

安装完成后的配置信息：
- **主机**: localhost
- **端口**: 3306
- **用户名**: root
- **密码**: cizhou123456

## 🚨 常见问题

### 问题1：端口被占用
```bash
# 检查端口占用
netstat -ano | findstr :3306

# 如果被占用，可以在安装时选择其他端口（如3307）
```

### 问题2：服务启动失败
```bash
# 手动启动 MySQL 服务
net start mysql

# 或者在服务管理器中启动
services.msc
```

### 问题3：密码设置问题
如果忘记密码或需要重置：
1. 停止 MySQL 服务
2. 以安全模式启动
3. 重置 root 密码

## ✅ 安装成功标志

- MySQL 服务在 Windows 服务中显示为 "正在运行"
- 可以通过命令行连接到 MySQL
- 可以执行基本的 SQL 命令
