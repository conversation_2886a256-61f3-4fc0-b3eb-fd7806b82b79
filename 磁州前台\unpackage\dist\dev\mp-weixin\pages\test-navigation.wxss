
.test-container {
  padding: 40rpx;
}
.header {
  margin-bottom: 40rpx;
}
.title {
  font-size: 40rpx;
  font-weight: bold;
}
.button-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}
.test-button {
  background-color: #1677FF;
  color: white;
  padding: 20rpx;
  border-radius: 8rpx;
}
.result-panel {
  padding: 20rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
}
.result-title {
  font-weight: bold;
  margin-bottom: 10rpx;
  display: block;
}
.result-content {
  font-family: monospace;
}
