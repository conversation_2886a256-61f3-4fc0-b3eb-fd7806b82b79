/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-172fcc21, html.data-v-172fcc21, #app.data-v-172fcc21, .index-container.data-v-172fcc21 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.qrcode-page.data-v-172fcc21 {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 30rpx;
}
.navbar.data-v-172fcc21 {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #3846cd, #2c3aa0);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(56, 70, 205, 0.15);
}
.navbar-back.data-v-172fcc21 {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon.data-v-172fcc21 {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title.data-v-172fcc21 {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.qrcode-container.data-v-172fcc21 {
  margin: 30rpx;
  display: flex;
  justify-content: center;
}
.qrcode-card.data-v-172fcc21 {
  width: 100%;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  padding: 30rpx;
}
.qrcode-header.data-v-172fcc21 {
  text-align: center;
  margin-bottom: 30rpx;
}
.qrcode-title.data-v-172fcc21 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}
.qrcode-subtitle.data-v-172fcc21 {
  font-size: 26rpx;
  color: #999;
  display: block;
}
.qrcode-content.data-v-172fcc21 {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 0;
}
.qrcode-image.data-v-172fcc21 {
  width: 400rpx;
  height: 400rpx;
}
.qrcode-loading.data-v-172fcc21 {
  width: 400rpx;
  height: 400rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.loading-spinner.data-v-172fcc21 {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #3846cd;
  border-radius: 50%;
  animation: spin-172fcc21 1s linear infinite;
  margin-bottom: 20rpx;
}
@keyframes spin-172fcc21 {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.loading-text.data-v-172fcc21 {
  font-size: 28rpx;
  color: #999;
}
.qrcode-footer.data-v-172fcc21 {
  text-align: center;
  margin-top: 30rpx;
}
.qrcode-tip.data-v-172fcc21 {
  font-size: 26rpx;
  color: #999;
}
.action-buttons.data-v-172fcc21 {
  display: flex;
  justify-content: space-between;
  padding: 0 30rpx;
  margin-top: 40rpx;
}
.action-btn.data-v-172fcc21 {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  margin: 0 20rpx;
}
.save-btn.data-v-172fcc21 {
  background: linear-gradient(135deg, #3846cd, #2c3aa0);
  color: #fff;
  border: none;
}
.share-btn.data-v-172fcc21 {
  background-color: #fff;
  color: #3846cd;
  border: 1px solid #3846cd;
}
.promotion-tips.data-v-172fcc21 {
  margin: 40rpx 30rpx 30rpx;
  border-radius: 16rpx;
  background-color: #fff;
  padding: 20rpx;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.tips-title.data-v-172fcc21 {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}
.tips-content.data-v-172fcc21 {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  display: block;
  margin-bottom: 10rpx;
}