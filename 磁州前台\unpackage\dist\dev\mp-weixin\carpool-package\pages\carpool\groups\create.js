"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const _sfc_main = {
  __name: "create",
  setup(__props) {
    const groupInfo = common_vendor.ref({
      name: "",
      description: "",
      startLocation: "",
      endLocation: "",
      qrcode: "",
      contactInfo: ""
    });
    const tags = common_vendor.ref(["上班族", "学生", "长途", "短途", "每日拼车", "周末拼车", "临时拼车", "商务出行"]);
    const selectedTags = common_vendor.ref([]);
    const agreePrivacy = common_vendor.ref(false);
    const isAdmin = common_vendor.ref(false);
    const canSubmit = common_vendor.computed(() => {
      return groupInfo.value.name && groupInfo.value.description && groupInfo.value.startLocation && groupInfo.value.endLocation && groupInfo.value.qrcode && groupInfo.value.contactInfo && agreePrivacy.value;
    });
    common_vendor.onMounted(() => {
      const userInfo = common_vendor.index.getStorageSync("userInfo") || {};
      isAdmin.value = !!userInfo.isAdmin;
      if (!isAdmin.value) {
        common_vendor.index.showModal({
          title: "提示",
          content: "只有管理员可以创建拼车群",
          showCancel: false,
          success: () => {
            common_vendor.index.navigateBack();
          }
        });
      }
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const toggleTag = (tag) => {
      if (selectedTags.value.includes(tag)) {
        selectedTags.value = selectedTags.value.filter((item) => item !== tag);
      } else {
        if (selectedTags.value.length < 3) {
          selectedTags.value.push(tag);
        } else {
          common_vendor.index.showToast({
            title: "最多选择3个标签",
            icon: "none"
          });
        }
      }
    };
    const selectLocation = (type) => {
      common_vendor.index.chooseLocation({
        success: (res) => {
          if (type === "start") {
            groupInfo.value.startLocation = res.name;
          } else {
            groupInfo.value.endLocation = res.name;
          }
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "选择位置失败",
            icon: "none"
          });
        }
      });
    };
    const uploadQRCode = () => {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          common_vendor.index.showLoading({
            title: "上传中..."
          });
          common_vendor.index.uploadFile({
            url: "https://api.example.com/upload",
            // 替换为实际的上传接口
            filePath: res.tempFilePaths[0],
            name: "file",
            success: (uploadRes) => {
              try {
                const data = JSON.parse(uploadRes.data);
                if (data.code === 0) {
                  groupInfo.value.qrcode = data.data.url;
                } else {
                  common_vendor.index.showToast({
                    title: data.msg || "上传失败",
                    icon: "none"
                  });
                }
              } catch (e) {
                common_vendor.index.showToast({
                  title: "上传失败",
                  icon: "none"
                });
              }
            },
            fail: () => {
              common_vendor.index.showToast({
                title: "上传失败",
                icon: "none"
              });
            },
            complete: () => {
              common_vendor.index.hideLoading();
            }
          });
        }
      });
    };
    const privacyChanged = (e) => {
      agreePrivacy.value = e.detail.value.length > 0;
    };
    const showPrivacyPolicy = () => {
      common_vendor.index.showModal({
        title: "拼车群管理规范",
        content: "1. 群内禁止发布违法信息\n2. 请文明用语，互相尊重\n3. 遵守交通规则，确保安全\n4. 请勿发布虚假信息\n5. 平台有权对违规内容进行处理",
        showCancel: false
      });
    };
    const submitForm = () => {
      if (!canSubmit.value) {
        common_vendor.index.showToast({
          title: "请完成所有必填项",
          icon: "none"
        });
        return;
      }
      if (!isAdmin.value) {
        common_vendor.index.showToast({
          title: "只有管理员可以创建拼车群",
          icon: "none"
        });
        return;
      }
      const formData = {
        ...groupInfo.value,
        tags: selectedTags.value
      };
      common_vendor.index.__f__("log", "at carpool-package/pages/carpool/groups/create.vue:275", "提交表单数据:", formData);
      common_vendor.index.showLoading({
        title: "创建中..."
      });
      common_vendor.index.request({
        url: "https://api.example.com/carpool/groups/create",
        // 替换为实际的创建接口
        method: "POST",
        data: formData,
        header: {
          "content-type": "application/json",
          "Authorization": common_vendor.index.getStorageSync("token") || ""
        },
        success: (res) => {
          if (res.statusCode === 200 && res.data.code === 0) {
            common_vendor.index.showToast({
              title: "创建成功",
              icon: "success"
            });
            setTimeout(() => {
              common_vendor.index.navigateBack();
            }, 1500);
          } else {
            common_vendor.index.showToast({
              title: res.data.msg || "创建失败",
              icon: "none"
            });
          }
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "网络异常，请重试",
            icon: "none"
          });
        },
        complete: () => {
          common_vendor.index.hideLoading();
        }
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: groupInfo.value.name,
        d: common_vendor.o(($event) => groupInfo.value.name = $event.detail.value),
        e: groupInfo.value.description,
        f: common_vendor.o(($event) => groupInfo.value.description = $event.detail.value),
        g: common_vendor.f(tags.value, (tag, index, i0) => {
          return {
            a: common_vendor.t(tag),
            b: index,
            c: selectedTags.value.includes(tag) ? 1 : "",
            d: common_vendor.o(($event) => toggleTag(tag), index)
          };
        }),
        h: common_vendor.t(groupInfo.value.startLocation || "选择出发地"),
        i: common_assets._imports_0$27,
        j: common_vendor.o(($event) => selectLocation("start")),
        k: common_vendor.t(groupInfo.value.endLocation || "选择目的地"),
        l: common_assets._imports_0$27,
        m: common_vendor.o(($event) => selectLocation("end")),
        n: groupInfo.value.qrcode
      }, groupInfo.value.qrcode ? {
        o: groupInfo.value.qrcode
      } : {
        p: common_assets._imports_2$26
      }, {
        q: common_vendor.o(uploadQRCode),
        r: groupInfo.value.contactInfo,
        s: common_vendor.o(($event) => groupInfo.value.contactInfo = $event.detail.value),
        t: agreePrivacy.value,
        v: common_vendor.o(showPrivacyPolicy),
        w: common_vendor.o(privacyChanged),
        x: !canSubmit.value,
        y: common_vendor.o(submitForm)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/carpool-package/pages/carpool/groups/create.js.map
