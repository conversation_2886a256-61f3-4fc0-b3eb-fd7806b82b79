<template>
  <view class="promotion-report-container">
    <uni-nav-bar title="推广报表" :border="false" backgroundColor="#8A2BE2" color="#fff" statusBar fixed>
      <template #left>
        <view class="nav-left" @click="goBack">
          <uni-icons type="arrow-left" color="#fff" size="22"></uni-icons>
        </view>
      </template>
    </uni-nav-bar>
    
    <view class="content">
      <view class="stats-cards">
        <view class="stats-card">
          <view class="stats-title">今日推广订单</view>
          <view class="stats-value">0</view>
        </view>
        <view class="stats-card">
          <view class="stats-title">今日预估佣金</view>
          <view class="stats-value">¥0.00</view>
        </view>
        <view class="stats-card">
          <view class="stats-title">本月推广订单</view>
          <view class="stats-value">0</view>
        </view>
        <view class="stats-card">
          <view class="stats-title">本月预估佣金</view>
          <view class="stats-value">¥0.00</view>
        </view>
      </view>
      
      <view class="report-tabs">
        <view class="tab-header">
          <view class="tab" :class="{ active: activeTab === 'orders' }" @click="activeTab = 'orders'">推广订单</view>
          <view class="tab" :class="{ active: activeTab === 'commission' }" @click="activeTab = 'commission'">佣金明细</view>
        </view>
        
        <view class="tab-content">
          <!-- 推广订单 -->
          <view v-if="activeTab === 'orders'" class="orders-list">
            <view class="empty-tip" v-if="ordersList.length === 0">
              <image src="/static/images/empty.png" mode="aspectFit" class="empty-image"></image>
              <view class="empty-text">暂无推广订单数据</view>
            </view>
            <view v-else class="order-item" v-for="(item, index) in ordersList" :key="index">
              <view class="order-header">
                <view class="order-no">订单号：{{item.orderNo}}</view>
                <view class="order-status">{{item.status}}</view>
              </view>
              <view class="order-info">
                <view class="goods-info">
                  <image class="goods-image" :src="item.goodsImage" mode="aspectFill"></image>
                  <view class="goods-detail">
                    <view class="goods-name">{{item.goodsName}}</view>
                    <view class="goods-price">¥{{item.price}}</view>
                  </view>
                </view>
                <view class="commission-info">
                  <view class="commission-label">佣金：</view>
                  <view class="commission-value">¥{{item.commission}}</view>
                </view>
              </view>
              <view class="order-footer">
                <view class="order-time">{{item.time}}</view>
              </view>
            </view>
          </view>
          
          <!-- 佣金明细 -->
          <view v-if="activeTab === 'commission'" class="commission-list">
            <view class="empty-tip" v-if="commissionList.length === 0">
              <image src="/static/images/empty.png" mode="aspectFit" class="empty-image"></image>
              <view class="empty-text">暂无佣金明细数据</view>
            </view>
            <view v-else class="commission-item" v-for="(item, index) in commissionList" :key="index">
              <view class="commission-header">
                <view class="commission-title">{{item.title}}</view>
                <view class="commission-amount" :class="{'income': item.type === 'income'}">{{item.type === 'income' ? '+' : '-'}}¥{{item.amount}}</view>
              </view>
              <view class="commission-time">{{item.time}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      activeTab: 'orders',
      ordersList: [],
      commissionList: []
    }
  },
  onLoad() {
    // 加载数据
    this.loadData();
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    loadData() {
      // 模拟数据请求
      // 实际应用中应通过API请求获取数据
      setTimeout(() => {
        // 这里仅作为示例，真实场景需要替换为API调用
        this.ordersList = [];
        this.commissionList = [];
      }, 500);
    }
  }
}
</script>

<style lang="scss" scoped>
.promotion-report-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content {
  margin-top: 100rpx;
  padding: 20rpx;
}

.stats-cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.stats-card {
  width: 48%;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.stats-title {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.stats-value {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
}

.report-tabs {
  margin-top: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.tab-header {
  display: flex;
  border-bottom: 1rpx solid #eee;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab.active {
  color: #8A2BE2;
  font-weight: bold;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #8A2BE2;
}

.tab-content {
  min-height: 500rpx;
  padding: 20rpx;
}

.empty-tip {
  text-align: center;
  padding: 60rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}

.order-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.order-no {
  font-size: 26rpx;
  color: #666;
}

.order-status {
  font-size: 26rpx;
  color: #8A2BE2;
}

.goods-info {
  display: flex;
  margin-bottom: 20rpx;
}

.goods-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.goods-detail {
  flex: 1;
}

.goods-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.goods-price {
  font-size: 26rpx;
  color: #ff6b6b;
}

.commission-info {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20rpx;
}

.commission-label {
  font-size: 26rpx;
  color: #666;
}

.commission-value {
  font-size: 26rpx;
  color: #ff6b6b;
}

.order-footer {
  text-align: right;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.commission-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.commission-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.commission-title {
  font-size: 28rpx;
  color: #333;
}

.commission-amount {
  font-size: 28rpx;
  color: #ff6b6b;
}

.commission-amount.income {
  color: #27ae60;
}

.commission-time {
  font-size: 24rpx;
  color: #999;
  text-align: right;
}
</style> 