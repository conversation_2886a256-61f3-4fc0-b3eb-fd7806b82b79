{"version": 3, "file": "carpoolApi.js", "sources": ["api/carpoolApi.js"], "sourcesContent": ["/**\n * 拼车API服务\n * 处理拼车相关的所有API调用\n */\n\nimport request from '@/utils/request';\n\n// 拼车API服务类\nclass CarpoolApiService {\n\n  /**\n   * 获取拼车列表\n   * @param {Object} params - 查询参数\n   * @param {number} params.page - 页码\n   * @param {number} params.limit - 每页数量\n   * @param {string} params.type - 拼车类型 (people-to-car, car-to-people)\n   * @param {string} params.departure - 出发地\n   * @param {string} params.destination - 目的地\n   * @param {string} params.date - 出发日期\n   * @returns {Promise} 拼车列表\n   */\n  async getCarpoolList(params = {}) {\n    try {\n      const queryParams = {\n        page: params.page || 1,\n        limit: params.limit || 10,\n        type: params.type || null,\n        departure: params.departure || null,\n        destination: params.destination || null,\n        date: params.date || null\n      };\n\n      const response = await request.get('/carpool/list', { params: queryParams });\n      \n      return {\n        success: true,\n        data: response.data || [],\n        total: response.total || 0,\n        page: response.page || 1,\n        limit: response.limit || 10\n      };\n    } catch (error) {\n      console.error('获取拼车列表失败:', error);\n      return {\n        success: false,\n        data: [],\n        message: error.message || '获取拼车信息失败'\n      };\n    }\n  }\n\n  /**\n   * 获取拼车详情\n   * @param {number} carpoolId - 拼车ID\n   * @returns {Promise} 拼车详情\n   */\n  async getCarpoolDetail(carpoolId) {\n    try {\n      const response = await request.get(`/carpool/${carpoolId}`);\n      \n      return {\n        success: true,\n        data: response.data\n      };\n    } catch (error) {\n      console.error('获取拼车详情失败:', error);\n      return {\n        success: false,\n        message: error.message || '获取拼车详情失败'\n      };\n    }\n  }\n\n  /**\n   * 发布拼车信息\n   * @param {Object} carpoolData - 拼车数据\n   * @returns {Promise} 发布结果\n   */\n  async publishCarpool(carpoolData) {\n    try {\n      const response = await request.post('/carpool/publish', carpoolData);\n      \n      return {\n        success: true,\n        data: response.data,\n        message: '发布成功'\n      };\n    } catch (error) {\n      console.error('发布拼车失败:', error);\n      return {\n        success: false,\n        message: error.message || '发布失败，请重试'\n      };\n    }\n  }\n\n  /**\n   * 更新拼车信息\n   * @param {number} carpoolId - 拼车ID\n   * @param {Object} carpoolData - 拼车数据\n   * @returns {Promise} 更新结果\n   */\n  async updateCarpool(carpoolId, carpoolData) {\n    try {\n      const response = await request.put(`/carpool/${carpoolId}`, carpoolData);\n      \n      return {\n        success: true,\n        data: response.data,\n        message: '更新成功'\n      };\n    } catch (error) {\n      console.error('更新拼车失败:', error);\n      return {\n        success: false,\n        message: error.message || '更新失败，请重试'\n      };\n    }\n  }\n\n  /**\n   * 删除拼车信息\n   * @param {number} carpoolId - 拼车ID\n   * @returns {Promise} 删除结果\n   */\n  async deleteCarpool(carpoolId) {\n    try {\n      await request.delete(`/carpool/${carpoolId}`);\n      \n      return {\n        success: true,\n        message: '删除成功'\n      };\n    } catch (error) {\n      console.error('删除拼车失败:', error);\n      return {\n        success: false,\n        message: error.message || '删除失败，请重试'\n      };\n    }\n  }\n\n  /**\n   * 获取我的拼车列表\n   * @param {Object} params - 查询参数\n   * @param {number} params.page - 页码\n   * @param {number} params.limit - 每页数量\n   * @param {string} params.status - 状态筛选\n   * @returns {Promise} 我的拼车列表\n   */\n  async getMyCarpoolList(params = {}) {\n    try {\n      const queryParams = {\n        page: params.page || 1,\n        limit: params.limit || 10,\n        status: params.status || null\n      };\n\n      const response = await request.get('/carpool/my', { params: queryParams });\n      \n      return {\n        success: true,\n        data: response.data || [],\n        total: response.total || 0,\n        page: response.page || 1,\n        limit: response.limit || 10\n      };\n    } catch (error) {\n      console.error('获取我的拼车列表失败:', error);\n      return {\n        success: false,\n        data: [],\n        message: error.message || '获取我的拼车失败'\n      };\n    }\n  }\n\n  /**\n   * 申请加入拼车\n   * @param {number} carpoolId - 拼车ID\n   * @param {Object} applicationData - 申请数据\n   * @returns {Promise} 申请结果\n   */\n  async applyCarpool(carpoolId, applicationData) {\n    try {\n      const response = await request.post(`/carpool/${carpoolId}/apply`, applicationData);\n      \n      return {\n        success: true,\n        data: response.data,\n        message: '申请成功'\n      };\n    } catch (error) {\n      console.error('申请拼车失败:', error);\n      return {\n        success: false,\n        message: error.message || '申请失败，请重试'\n      };\n    }\n  }\n\n  /**\n   * 处理拼车申请\n   * @param {number} applicationId - 申请ID\n   * @param {string} action - 操作 (accept, reject)\n   * @param {string} reason - 原因（拒绝时）\n   * @returns {Promise} 处理结果\n   */\n  async handleApplication(applicationId, action, reason = '') {\n    try {\n      const response = await request.post(`/carpool/application/${applicationId}/${action}`, {\n        reason: reason\n      });\n      \n      return {\n        success: true,\n        message: action === 'accept' ? '已接受申请' : '已拒绝申请'\n      };\n    } catch (error) {\n      console.error('处理申请失败:', error);\n      return {\n        success: false,\n        message: error.message || '处理失败，请重试'\n      };\n    }\n  }\n\n  /**\n   * 获取拼车申请列表\n   * @param {number} carpoolId - 拼车ID\n   * @returns {Promise} 申请列表\n   */\n  async getApplications(carpoolId) {\n    try {\n      const response = await request.get(`/carpool/${carpoolId}/applications`);\n      \n      return {\n        success: true,\n        data: response.data || []\n      };\n    } catch (error) {\n      console.error('获取申请列表失败:', error);\n      return {\n        success: false,\n        data: [],\n        message: error.message || '获取申请列表失败'\n      };\n    }\n  }\n\n  /**\n   * 拼车置顶\n   * @param {number} carpoolId - 拼车ID\n   * @param {string} duration - 置顶时长 (1h, 3h, 7h)\n   * @param {string} paymentType - 支付类型 (paid, ad)\n   * @returns {Promise} 置顶结果\n   */\n  async topCarpool(carpoolId, duration, paymentType = 'paid') {\n    try {\n      const response = await request.post(`/carpool/${carpoolId}/top`, {\n        duration: duration,\n        payment_type: paymentType\n      });\n      \n      return {\n        success: true,\n        data: response.data,\n        message: '置顶成功'\n      };\n    } catch (error) {\n      console.error('拼车置顶失败:', error);\n      return {\n        success: false,\n        message: error.message || '置顶失败，请重试'\n      };\n    }\n  }\n\n  /**\n   * 刷新拼车信息\n   * @param {number} carpoolId - 拼车ID\n   * @param {string} paymentType - 支付类型 (paid, ad)\n   * @returns {Promise} 刷新结果\n   */\n  async refreshCarpool(carpoolId, paymentType = 'paid') {\n    try {\n      const response = await request.post(`/carpool/${carpoolId}/refresh`, {\n        payment_type: paymentType\n      });\n      \n      return {\n        success: true,\n        data: response.data,\n        message: '刷新成功'\n      };\n    } catch (error) {\n      console.error('刷新拼车失败:', error);\n      return {\n        success: false,\n        message: error.message || '刷新失败，请重试'\n      };\n    }\n  }\n\n  /**\n   * 获取热门路线\n   * @returns {Promise} 热门路线列表\n   */\n  async getHotRoutes() {\n    try {\n      const response = await request.get('/carpool/hot-routes');\n      \n      return {\n        success: true,\n        data: response.data || []\n      };\n    } catch (error) {\n      console.error('获取热门路线失败:', error);\n      return {\n        success: false,\n        data: [],\n        message: error.message || '获取热门路线失败'\n      };\n    }\n  }\n\n  /**\n   * 搜索拼车\n   * @param {Object} searchParams - 搜索参数\n   * @param {string} searchParams.departure - 出发地\n   * @param {string} searchParams.destination - 目的地\n   * @param {string} searchParams.date - 出发日期\n   * @param {string} searchParams.type - 拼车类型\n   * @returns {Promise} 搜索结果\n   */\n  async searchCarpool(searchParams) {\n    try {\n      const response = await request.get('/carpool/search', { params: searchParams });\n      \n      return {\n        success: true,\n        data: response.data || [],\n        total: response.total || 0\n      };\n    } catch (error) {\n      console.error('搜索拼车失败:', error);\n      return {\n        success: false,\n        data: [],\n        message: error.message || '搜索失败'\n      };\n    }\n  }\n\n  /**\n   * 获取拼车统计信息\n   * @returns {Promise} 统计信息\n   */\n  async getCarpoolStats() {\n    try {\n      const response = await request.get('/carpool/stats');\n      \n      return {\n        success: true,\n        data: response.data\n      };\n    } catch (error) {\n      console.error('获取统计信息失败:', error);\n      return {\n        success: false,\n        data: {},\n        message: error.message || '获取统计信息失败'\n      };\n    }\n  }\n\n  /**\n   * 举报拼车信息\n   * @param {number} carpoolId - 拼车ID\n   * @param {Object} reportData - 举报数据\n   * @returns {Promise} 举报结果\n   */\n  async reportCarpool(carpoolId, reportData) {\n    try {\n      const response = await request.post(`/carpool/${carpoolId}/report`, reportData);\n      \n      return {\n        success: true,\n        message: '举报成功，我们会尽快处理'\n      };\n    } catch (error) {\n      console.error('举报失败:', error);\n      return {\n        success: false,\n        message: error.message || '举报失败，请重试'\n      };\n    }\n  }\n}\n\n// 创建单例实例\nconst carpoolApi = new CarpoolApiService();\n\nexport default carpoolApi;\n"], "names": ["request", "uni"], "mappings": ";;;AAQA,MAAM,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAatB,MAAM,eAAe,SAAS,IAAI;AAChC,QAAI;AACF,YAAM,cAAc;AAAA,QAClB,MAAM,OAAO,QAAQ;AAAA,QACrB,OAAO,OAAO,SAAS;AAAA,QACvB,MAAM,OAAO,QAAQ;AAAA,QACrB,WAAW,OAAO,aAAa;AAAA,QAC/B,aAAa,OAAO,eAAe;AAAA,QACnC,MAAM,OAAO,QAAQ;AAAA,MAC7B;AAEM,YAAM,WAAW,MAAMA,cAAAA,QAAQ,IAAI,iBAAiB,EAAE,QAAQ,YAAW,CAAE;AAE3E,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,SAAS,QAAQ,CAAE;AAAA,QACzB,OAAO,SAAS,SAAS;AAAA,QACzB,MAAM,SAAS,QAAQ;AAAA,QACvB,OAAO,SAAS,SAAS;AAAA,MACjC;AAAA,IACK,SAAQ,OAAO;AACdC,oEAAc,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,CAAE;AAAA,QACR,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,iBAAiB,WAAW;AAChC,QAAI;AACF,YAAM,WAAW,MAAMD,sBAAQ,IAAI,YAAY,SAAS,EAAE;AAE1D,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,SAAS;AAAA,MACvB;AAAA,IACK,SAAQ,OAAO;AACdC,oEAAc,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,eAAe,aAAa;AAChC,QAAI;AACF,YAAM,WAAW,MAAMD,cAAO,QAAC,KAAK,oBAAoB,WAAW;AAEnE,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,SAAS;AAAA,QACf,SAAS;AAAA,MACjB;AAAA,IACK,SAAQ,OAAO;AACdC,oBAAc,MAAA,MAAA,SAAA,2BAAA,WAAW,KAAK;AAC9B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,MAAM,cAAc,WAAW,aAAa;AAC1C,QAAI;AACF,YAAM,WAAW,MAAMD,cAAAA,QAAQ,IAAI,YAAY,SAAS,IAAI,WAAW;AAEvE,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,SAAS;AAAA,QACf,SAAS;AAAA,MACjB;AAAA,IACK,SAAQ,OAAO;AACdC,oBAAc,MAAA,MAAA,SAAA,4BAAA,WAAW,KAAK;AAC9B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,cAAc,WAAW;AAC7B,QAAI;AACF,YAAMD,cAAAA,QAAQ,OAAO,YAAY,SAAS,EAAE;AAE5C,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS;AAAA,MACjB;AAAA,IACK,SAAQ,OAAO;AACdC,oBAAc,MAAA,MAAA,SAAA,4BAAA,WAAW,KAAK;AAC9B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUD,MAAM,iBAAiB,SAAS,IAAI;AAClC,QAAI;AACF,YAAM,cAAc;AAAA,QAClB,MAAM,OAAO,QAAQ;AAAA,QACrB,OAAO,OAAO,SAAS;AAAA,QACvB,QAAQ,OAAO,UAAU;AAAA,MACjC;AAEM,YAAM,WAAW,MAAMD,cAAAA,QAAQ,IAAI,eAAe,EAAE,QAAQ,YAAW,CAAE;AAEzE,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,SAAS,QAAQ,CAAE;AAAA,QACzB,OAAO,SAAS,SAAS;AAAA,QACzB,MAAM,SAAS,QAAQ;AAAA,QACvB,OAAO,SAAS,SAAS;AAAA,MACjC;AAAA,IACK,SAAQ,OAAO;AACdC,oBAAc,MAAA,MAAA,SAAA,4BAAA,eAAe,KAAK;AAClC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,CAAE;AAAA,QACR,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,MAAM,aAAa,WAAW,iBAAiB;AAC7C,QAAI;AACF,YAAM,WAAW,MAAMD,cAAO,QAAC,KAAK,YAAY,SAAS,UAAU,eAAe;AAElF,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,SAAS;AAAA,QACf,SAAS;AAAA,MACjB;AAAA,IACK,SAAQ,OAAO;AACdC,oBAAc,MAAA,MAAA,SAAA,4BAAA,WAAW,KAAK;AAC9B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,MAAM,kBAAkB,eAAe,QAAQ,SAAS,IAAI;AAC1D,QAAI;AACF,YAAM,WAAW,MAAMD,cAAO,QAAC,KAAK,wBAAwB,aAAa,IAAI,MAAM,IAAI;AAAA,QACrF;AAAA,MACR,CAAO;AAED,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,WAAW,WAAW,UAAU;AAAA,MACjD;AAAA,IACK,SAAQ,OAAO;AACdC,oBAAc,MAAA,MAAA,SAAA,4BAAA,WAAW,KAAK;AAC9B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,gBAAgB,WAAW;AAC/B,QAAI;AACF,YAAM,WAAW,MAAMD,cAAAA,QAAQ,IAAI,YAAY,SAAS,eAAe;AAEvE,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,SAAS,QAAQ,CAAE;AAAA,MACjC;AAAA,IACK,SAAQ,OAAO;AACdC,qEAAc,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,CAAE;AAAA,QACR,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,MAAM,WAAW,WAAW,UAAU,cAAc,QAAQ;AAC1D,QAAI;AACF,YAAM,WAAW,MAAMD,sBAAQ,KAAK,YAAY,SAAS,QAAQ;AAAA,QAC/D;AAAA,QACA,cAAc;AAAA,MACtB,CAAO;AAED,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,SAAS;AAAA,QACf,SAAS;AAAA,MACjB;AAAA,IACK,SAAQ,OAAO;AACdC,oBAAc,MAAA,MAAA,SAAA,4BAAA,WAAW,KAAK;AAC9B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,MAAM,eAAe,WAAW,cAAc,QAAQ;AACpD,QAAI;AACF,YAAM,WAAW,MAAMD,sBAAQ,KAAK,YAAY,SAAS,YAAY;AAAA,QACnE,cAAc;AAAA,MACtB,CAAO;AAED,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,SAAS;AAAA,QACf,SAAS;AAAA,MACjB;AAAA,IACK,SAAQ,OAAO;AACdC,oBAAc,MAAA,MAAA,SAAA,4BAAA,WAAW,KAAK;AAC9B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,eAAe;AACnB,QAAI;AACF,YAAM,WAAW,MAAMD,cAAAA,QAAQ,IAAI,qBAAqB;AAExD,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,SAAS,QAAQ,CAAE;AAAA,MACjC;AAAA,IACK,SAAQ,OAAO;AACdC,qEAAc,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,CAAE;AAAA,QACR,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWD,MAAM,cAAc,cAAc;AAChC,QAAI;AACF,YAAM,WAAW,MAAMD,cAAAA,QAAQ,IAAI,mBAAmB,EAAE,QAAQ,aAAY,CAAE;AAE9E,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,SAAS,QAAQ,CAAE;AAAA,QACzB,OAAO,SAAS,SAAS;AAAA,MACjC;AAAA,IACK,SAAQ,OAAO;AACdC,oBAAc,MAAA,MAAA,SAAA,4BAAA,WAAW,KAAK;AAC9B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,CAAE;AAAA,QACR,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,kBAAkB;AACtB,QAAI;AACF,YAAM,WAAW,MAAMD,cAAAA,QAAQ,IAAI,gBAAgB;AAEnD,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,SAAS;AAAA,MACvB;AAAA,IACK,SAAQ,OAAO;AACdC,qEAAc,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,CAAE;AAAA,QACR,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,MAAM,cAAc,WAAW,YAAY;AACzC,QAAI;AACF,YAAM,WAAW,MAAMD,cAAO,QAAC,KAAK,YAAY,SAAS,WAAW,UAAU;AAE9E,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS;AAAA,MACjB;AAAA,IACK,SAAQ,OAAO;AACdC,oBAAc,MAAA,MAAA,SAAA,4BAAA,SAAS,KAAK;AAC5B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AACH;AAGK,MAAC,aAAa,IAAI,kBAAiB;;"}