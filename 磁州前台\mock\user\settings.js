// 用户设置模拟数据
export const userSettings = {
  notifications: {
    pushEnabled: true,
    commentNotify: true,
    likeNotify: true,
    followNotify: true,
    systemNotify: true,
    activityNotify: true,
    messageNotify: true
  },
  privacy: {
    profileVisible: 'public', // public, friends, private
    phoneVisible: 'friends', // public, friends, private
    locationVisible: 'friends', // public, friends, private
    allowStrangerMessage: true
  },
  theme: {
    darkMode: false,
    fontScale: 1,
    colorScheme: 'blue' // blue, green, orange, purple
  },
  language: 'zh-CN',
  autoPlay: true,
  dataUsage: {
    saveData: false,
    autoDownload: true
  }
};

// 获取用户设置的API函数
export const fetchUserSettings = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(userSettings);
    }, 300);
  });
};

// 更新用户设置的API函数
export const updateUserSettings = (data) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟更新成功
      const updatedSettings = {
        ...userSettings,
        ...data
      };
      resolve({
        success: true,
        data: updatedSettings
      });
    }, 500);
  });
}; 