const fs = require('fs');
const path = require('path');

// 定义要创建的页面
const pages = [
  // 红包营销页面
  {
    path: 'subPackages/merchant-admin/pages/marketing/redpacket/tool.vue',
    title: '红包营销工具',
    helpTitle: '红包营销工具帮助',
    helpContent: '在此页面您可以使用各种红包营销工具，提高营销效果。',
    bgColor: '#FF5858, #FF0000'
  },
  {
    path: 'subPackages/merchant-admin/pages/marketing/redpacket/create-template.vue',
    title: '创建红包模板',
    helpTitle: '创建红包模板帮助',
    helpContent: '在此页面您可以创建红包模板，方便快速创建红包活动。',
    bgColor: '#FF5858, #FF0000'
  },
  {
    path: 'subPackages/merchant-admin/pages/marketing/redpacket/guide.vue',
    title: '红包营销指南',
    helpTitle: '红包营销指南帮助',
    helpContent: '在此页面您可以查看红包营销的最佳实践和操作指南。',
    bgColor: '#FF5858, #FF0000'
  },
  {
    path: 'subPackages/merchant-admin/pages/marketing/redpacket/data-analysis.vue',
    title: '红包数据分析',
    helpTitle: '红包数据分析帮助',
    helpContent: '在此页面您可以查看红包活动的数据分析，了解活动效果。',
    bgColor: '#FF5858, #FF0000'
  },
  {
    path: 'subPackages/merchant-admin/pages/marketing/redpacket/settings.vue',
    title: '红包营销设置',
    helpTitle: '红包营销设置帮助',
    helpContent: '在此页面您可以配置红包营销的相关设置。',
    bgColor: '#FF5858, #FF0000'
  },
  
  // 分销系统页面
  {
    path: 'subPackages/merchant-admin-marketing/pages/marketing/distribution/distributors.vue',
    title: '分销员管理',
    helpTitle: '分销员管理帮助',
    helpContent: '在此页面您可以管理所有分销员，查看他们的业绩和状态。',
    bgColor: '#9370DB, #8A2BE2'
  },
  {
    path: 'subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail.vue',
    title: '分销员详情',
    helpTitle: '分销员详情帮助',
    helpContent: '在此页面您可以查看分销员的详细信息，包括基本资料、业绩数据等。',
    bgColor: '#9370DB, #8A2BE2'
  },
  {
    path: 'subPackages/merchant-admin-marketing/pages/marketing/distribution/conditions.vue',
    title: '分销条件设置',
    helpTitle: '分销条件设置帮助',
    helpContent: '在此页面您可以设置成为分销员的条件，如购买金额、会员等级等。',
    bgColor: '#9370DB, #8A2BE2'
  },
  {
    path: 'subPackages/merchant-admin-marketing/pages/marketing/distribution/levels.vue',
    title: '分销等级设置',
    helpTitle: '分销等级设置帮助',
    helpContent: '在此页面您可以设置分销员的等级体系，包括升级条件、佣金比例等。',
    bgColor: '#9370DB, #8A2BE2'
  },
  {
    path: 'subPackages/merchant-admin-marketing/pages/marketing/distribution/withdrawal.vue',
    title: '提现设置',
    helpTitle: '提现设置帮助',
    helpContent: '在此页面您可以设置分销员佣金提现的规则，如最低提现金额、手续费等。',
    bgColor: '#9370DB, #8A2BE2'
  },
  {
    path: 'subPackages/merchant-admin-marketing/pages/marketing/distribution/agreement.vue',
    title: '分销协议',
    helpTitle: '分销协议帮助',
    helpContent: '在此页面您可以设置分销员必须同意的协议内容。',
    bgColor: '#9370DB, #8A2BE2'
  },
  {
    path: 'subPackages/merchant-admin-marketing/pages/marketing/distribution/commission-rules.vue',
    title: '佣金规则',
    helpTitle: '佣金规则帮助',
    helpContent: '在此页面您可以设置分销佣金的计算规则，如佣金比例、结算周期等。',
    bgColor: '#9370DB, #8A2BE2'
  },
  {
    path: 'subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-tool.vue',
    title: '推广工具',
    helpTitle: '推广工具帮助',
    helpContent: '在此页面您可以管理分销员的推广工具，如推广海报、专属链接等。',
    bgColor: '#9370DB, #8A2BE2'
  },
  {
    path: 'subPackages/merchant-admin-marketing/pages/marketing/distribution/add-distributor.vue',
    title: '添加分销员',
    helpTitle: '添加分销员帮助',
    helpContent: '在此页面您可以手动添加新的分销员。',
    bgColor: '#9370DB, #8A2BE2'
  },
  {
    path: 'subPackages/merchant-admin-marketing/pages/marketing/distribution/pay-commission.vue',
    title: '佣金发放',
    helpTitle: '佣金发放帮助',
    helpContent: '在此页面您可以管理分销佣金的发放，查看佣金记录等。',
    bgColor: '#9370DB, #8A2BE2'
  },
  {
    path: 'subPackages/merchant-admin-marketing/pages/marketing/distribution/create-activity.vue',
    title: '创建分销活动',
    helpTitle: '创建分销活动帮助',
    helpContent: '在此页面您可以创建新的分销活动，设置活动规则、佣金比例等。',
    bgColor: '#9370DB, #8A2BE2'
  },
  {
    path: 'subPackages/merchant-admin-marketing/pages/marketing/distribution/qrcode.vue',
    title: '推广二维码',
    helpTitle: '推广二维码帮助',
    helpContent: '在此页面您可以生成和管理专属推广二维码。',
    bgColor: '#9370DB, #8A2BE2'
  },
  {
    path: 'subPackages/merchant-admin-marketing/pages/marketing/distribution/product-cards.vue',
    title: '商品推广卡片',
    helpTitle: '商品推广卡片帮助',
    helpContent: '在此页面您可以生成和管理商品推广卡片。',
    bgColor: '#9370DB, #8A2BE2'
  },
  {
    path: 'subPackages/merchant-admin-marketing/pages/marketing/distribution/channels.vue',
    title: '分销员渠道',
    helpTitle: '分销员渠道帮助',
    helpContent: '在此页面您可以管理分销员的渠道来源和表现。',
    bgColor: '#9370DB, #8A2BE2'
  },
  {
    path: 'subPackages/merchant-admin-marketing/pages/marketing/distribution/reports.vue',
    title: '推广报表',
    helpTitle: '推广报表帮助',
    helpContent: '在此页面您可以查看推广数据和业绩报表。',
    bgColor: '#9370DB, #8A2BE2'
  },
  
  // 会员特权页面
  {
    path: 'subPackages/merchant-admin/pages/marketing/member/level-setting.vue',
    title: '会员等级设置',
    helpTitle: '会员等级设置帮助',
    helpContent: '在此页面您可以设置会员等级体系，包括升级条件、等级特权等。',
    bgColor: '#7B68EE, #5E5CE6'
  },
  {
    path: 'subPackages/merchant-admin/pages/marketing/member/privilege-setting.vue',
    title: '会员权益设置',
    helpTitle: '会员权益设置帮助',
    helpContent: '在此页面您可以设置不同会员等级的权益，如折扣、积分倍率等。',
    bgColor: '#7B68EE, #5E5CE6'
  },
  {
    path: 'subPackages/merchant-admin/pages/marketing/member/growth-setting.vue',
    title: '成长值设置',
    helpTitle: '成长值设置帮助',
    helpContent: '在此页面您可以设置会员成长值的获取规则和使用方式。',
    bgColor: '#7B68EE, #5E5CE6'
  },
  {
    path: 'subPackages/merchant-admin/pages/marketing/member/member-list.vue',
    title: '会员列表',
    helpTitle: '会员列表帮助',
    helpContent: '在此页面您可以查看所有会员的列表，进行会员管理。',
    bgColor: '#7B68EE, #5E5CE6'
  },
  {
    path: 'subPackages/merchant-admin/pages/marketing/member/member-detail.vue',
    title: '会员详情',
    helpTitle: '会员详情帮助',
    helpContent: '在此页面您可以查看会员的详细信息，包括基本资料、消费记录等。',
    bgColor: '#7B68EE, #5E5CE6'
  },
  {
    path: 'subPackages/merchant-admin/pages/marketing/member/marketing-tool.vue',
    title: '会员营销工具',
    helpTitle: '会员营销工具帮助',
    helpContent: '在此页面您可以使用各种会员营销工具，提高会员活跃度和忠诚度。',
    bgColor: '#7B68EE, #5E5CE6'
  },
  {
    path: 'subPackages/merchant-admin/pages/marketing/member/data-analysis.vue',
    title: '会员数据分析',
    helpTitle: '会员数据分析帮助',
    helpContent: '在此页面您可以查看会员相关的数据分析，了解会员价值和行为特征。',
    bgColor: '#7B68EE, #5E5CE6'
  },
  {
    path: 'subPackages/merchant-admin/pages/marketing/member/settings.vue',
    title: '会员系统设置',
    helpTitle: '会员系统设置帮助',
    helpContent: '在此页面您可以配置会员系统的基本设置，如会员规则、自动升级等。',
    bgColor: '#7B68EE, #5E5CE6'
  }
];

// 页面模板
const pageTemplate = (title, helpTitle, helpContent, bgColor) => `<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">${title}</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 页面内容 -->
    <view class="content-container">
      <text class="page-tip">此页面正在开发中...</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 数据将在这里定义
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showHelp() {
      uni.showModal({
        title: '${helpTitle}',
        content: '${helpContent}',
        showCancel: false
      });
    }
  }
}
</script>

<style lang="scss">
.page-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, ${bgColor});
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 内容样式 */
.content-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.page-tip {
  font-size: 16px;
  color: #999;
  margin-top: 20px;
}
</style>`;

// 创建页面文件
pages.forEach(page => {
  const dirPath = path.dirname(page.path);
  
  // 创建目录（如果不存在）
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
  
  // 创建文件
  fs.writeFileSync(
    page.path,
    pageTemplate(page.title, page.helpTitle, page.helpContent, page.bgColor)
  );
  
  console.log(`Created: ${page.path}`);
});

console.log('All pages created successfully!'); 