/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.marketing-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  position: relative;
}
.page-content {
  height: calc(100vh - 77px);
  /* 减去导航栏高度 */
  box-sizing: border-box;
  padding-bottom: 60px;
  /* 添加底部空间，避免内容被底部导航栏遮挡 */
}
.bottom-space {
  height: 60px;
  /* 底部导航栏高度 + 一些额外空间 */
}

/* 浮动操作按钮样式已经在底部导航样式中定义 */
/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  color: #fff;
  padding: 44px 16px 10px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(255, 120, 0, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 概览部分样式 */
.overview-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}
.overview-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.title-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.date-picker {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
}
.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}
.date-icon {
  width: 12px;
  height: 12px;
  border-top: 2px solid #666;
  border-right: 2px solid #666;
  transform: rotate(135deg);
}

/* 数据卡片样式 */
.data-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}
.data-card {
  width: 50%;
  padding: 7.5px;
  box-sizing: border-box;
}
.card-content {
  background: #F8FAFC;
  border-radius: 10px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  border-left: 3px solid #FF7600;
}
.card-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}
.card-label {
  font-size: 12px;
  color: #999;
}
.card-trend {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-size: 12px;
  margin-top: 8px;
}
.card-trend.up {
  color: #34C759;
}
.card-trend.down {
  color: #FF3B30;
}
.trend-arrow {
  width: 0;
  height: 0;
  margin-right: 3px;
}
.card-trend.up .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 6px solid #34C759;
}
.card-trend.down .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 6px solid #FF3B30;
}

/* 工具导航样式 */
.tool-navigation {
  position: -webkit-sticky;
  position: sticky;
  top: 77px;
  /* 导航栏高度 */
  z-index: 99;
  background: #fff;
  margin-bottom: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.tab-scroll {
  white-space: nowrap;
  padding: 0 8px;
  height: 56px;
  /* 增加高度容纳更大字体 */
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  /* 提高iOS滑动体验 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
}

/* 隐藏滚动条 */
.tab-scroll::-webkit-scrollbar {
  display: none;
}

/* 直接给tab-text设置样式，这是实际显示文本的元素 */
.tool-navigation .tab-scroll .tab-item .tab-text {
  font-size: 15px !important;
  /* 调整为15px */
  font-weight: 700 !important;
  /* 加粗显示 */
}
.tool-navigation .tab-scroll .tab-item.active .tab-text {
  font-size: 16px !important;
  /* 调整为16px */
  font-weight: 800 !important;
  /* 更粗的字体 */
  color: #FF7600;
}

/* 增加更多样式特异性来确保生效 */
.tab-scroll text.tab-text {
  font-size: 15px !important;
  font-weight: 700 !important;
  /* 加粗显示 */
}
.tab-scroll .tab-item.active text.tab-text {
  font-size: 16px !important;
  font-weight: 800 !important;
  /* 更粗的字体 */
}
.tool-navigation .tab-item {
  display: inline-block;
  padding: 0 12px;
  /* 稍微减少内边距 */
  height: 46px;
  /* 增加高度 */
  line-height: 46px;
  /* 增加行高 */
  color: #666;
  position: relative;
  margin: 0 4px;
  /* 增加标签间距 */
  transition: all 0.2s ease;
  /* 添加过渡动画 */
  text-align: center;
  /* 文字居中 */
  -webkit-tap-highlight-color: transparent;
  /* 去除默认点击高亮 */
}
.tool-navigation .tab-item.active {
  color: #FF7600;
  font-weight: 700;
  /* 加粗激活的标签 */
  font-size: 26px !important;
  /* 使用!important确保样式生效 */
  text-shadow: 0 1px 2px rgba(255, 118, 0, 0.1);
  /* 添加文字阴影 */
}

/* 为第一个和最后一个标签添加特殊间距，让边缘美观 */
.tool-navigation .tab-item:first-child {
  margin-left: 2px;
}
.tool-navigation .tab-item:last-child {
  margin-right: 2px;
}
.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 15px;
  right: 15px;
  height: 4px;
  /* 增加下划线高度 */
  background: #FF7600;
  border-radius: 4px 4px 0 0;
  /* 调整下划线圆角 */
}

/* 图表部分样式 */
.chart-section {
  margin: 0 15px 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.filter-dropdown {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
}
.selected-value {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}
.dropdown-arrow {
  width: 8px;
  height: 8px;
  border-top: 1.5px solid #666;
  border-right: 1.5px solid #666;
  transform: rotate(135deg);
}
.chart-container {
  height: auto;
  margin: 10px 0;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.chart-area {
  position: relative;
  height: 220px;
  margin: 10px 0 30px;
  background-color: #FCFCFF;
  border-radius: 8px;
  border: 1px solid rgba(200, 210, 230, 0.3);
  padding: 20px;
  box-sizing: border-box;
}
.y-axis-labels {
  position: absolute;
  left: 10px;
  top: 10px;
  bottom: 30px;
  width: 30px;
  z-index: 2;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.y-label {
  font-size: 11px;
  color: #8E8E93;
  text-align: right;
  transform: translateY(-50%);
}
.chart-grid {
  position: absolute;
  left: 45px;
  right: 15px;
  top: 10px;
  bottom: 30px;
  z-index: 1;
}
.h-grid-line {
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  background-color: rgba(200, 210, 230, 0.3);
}
.chart-column {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 1px;
  z-index: 2;
}
.v-grid-line {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: rgba(200, 210, 230, 0.2);
}
.x-label {
  position: absolute;
  font-size: 11px;
  color: #8E8E93;
  transform: translateX(-50%);
  text-align: center;
  bottom: -25px;
  white-space: nowrap;
}
.chart-lines-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: none;
}
.revenue-area {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 118, 0, 0.2), rgba(255, 118, 0, 0.05));
  opacity: 0.8;
  z-index: 2;
  border-top: 2px solid #FF7600;
}
.conversion-area {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 122, 255, 0.2), rgba(0, 122, 255, 0.05));
  opacity: 0.8;
  z-index: 2;
  border-top: 2px solid #007AFF;
}
.data-point {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  transform: translate(-50%, 50%);
  z-index: 10;
  background-color: #fff;
}
.data-point.revenue {
  border: 2px solid #FF7600;
  box-shadow: 0 1px 3px rgba(255, 118, 0, 0.2);
}
.data-point.conversion {
  border: 2px solid #007AFF;
  box-shadow: 0 1px 3px rgba(0, 122, 255, 0.2);
}
.chart-legend {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}
.legend-item {
  display: flex;
  align-items: center;
  margin: 0 10px;
}
.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 5px;
}
.legend-color.revenue {
  background: #FF7600;
}
.legend-color.conversion {
  background: #007AFF;
}
.legend-text {
  font-size: 12px;
  color: #666;
}

/* 优惠券标签样式 */
.tab-content {
  margin: 15px;
}
.tool-detail-section {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
  margin-bottom: 15px;
}
.add-tool-btn {
  display: flex;
  align-items: center;
  background: #FF7600;
  border-radius: 15px;
  padding: 5px 10px;
  color: white;
}
.btn-text {
  font-size: 13px;
  margin-right: 5px;
}
.plus-icon-small {
  width: 12px;
  height: 12px;
  position: relative;
}
.plus-icon-small:before,
.plus-icon-small:after {
  content: "";
  position: absolute;
  background: white;
}
.plus-icon-small:before {
  width: 12px;
  height: 2px;
  top: 5px;
  left: 0;
}
.plus-icon-small:after {
  height: 12px;
  width: 2px;
  left: 5px;
  top: 0;
}

/* 优惠券卡片样式 */
.tool-cards {
  margin-top: 15px;
}
.coupon-card {
  background: linear-gradient(135deg, #FFF8F2, #FFFFFF);
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(255, 118, 0, 0.1);
  border-left: 4px solid #FF7600;
}
.coupon-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.coupon-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.coupon-status {
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  color: white;
}
.status-active {
  background: #34C759;
}
.status-expired {
  background: #8E8E93;
}
.status-upcoming {
  background: #FF9500;
}
.coupon-value {
  font-size: 24px;
  font-weight: bold;
  color: #FF7600;
  margin: 10px 0;
}
.discount-symbol {
  font-size: 16px;
  margin-right: 2px;
}
.coupon-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  font-size: 13px;
  color: #666;
}
.coupon-stats {
  display: flex;
  border-top: 1px dashed #E5E5EA;
  padding-top: 10px;
}
.stat-item {
  flex: 1;
  text-align: center;
}
.stat-value {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}
.stat-label {
  font-size: 12px;
  color: #8E8E93;
}

/* 满减活动样式 */
.discount-list {
  margin-top: 15px;
}
.discount-item {
  display: flex;
  background: #FFFFFF;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  border-left: 3px solid #FF7600;
  position: relative;
}
.discount-content {
  flex: 2;
}
.discount-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}
.discount-rules {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 8px;
}
.rule-item {
  background: rgba(255, 118, 0, 0.1);
  border-radius: 12px;
  padding: 3px 8px;
  margin-right: 8px;
  margin-bottom: 5px;
}
.rule-text {
  font-size: 12px;
  color: #FF7600;
  font-weight: 500;
}
.discount-time {
  font-size: 12px;
  color: #8E8E93;
}
.discount-stats {
  flex: 1;
  padding-left: 15px;
  border-left: 1px dashed #E5E5EA;
}
.stat-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}
.item-arrow {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 10px;
  height: 10px;
  border-top: 2px solid #C7C7CC;
  border-right: 2px solid #C7C7CC;
  transform: rotate(45deg);
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 40px 15px;
  margin-top: 20px;
}
.empty-icon {
  width: 80px;
  height: 80px;
  background: rgba(255, 118, 0, 0.1);
  border-radius: 40px;
  margin-bottom: 20px;
  position: relative;
}
.empty-icon:before,
.empty-icon:after {
  content: "";
  position: absolute;
  background: #FF7600;
}
.empty-icon:before {
  width: 40px;
  height: 4px;
  top: 38px;
  left: 20px;
}
.empty-icon:after {
  width: 4px;
  height: 40px;
  left: 38px;
  top: 20px;
}
.empty-text {
  font-size: 16px;
  color: #8E8E93;
  margin-bottom: 20px;
}
.empty-action {
  background: #FF7600;
  border-radius: 20px;
  padding: 10px 20px;
}
.action-text {
  color: white;
  font-size: 14px;
  font-weight: 500;
}

/* 分销概览样式 */
.distribution-overview {
  margin-bottom: 15px;
}
.overview-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
  margin-bottom: 15px;
}
.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.overview-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.overview-period {
  background: #F5F7FA;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  color: #666;
}
.overview-stats {
  display: flex;
  justify-content: space-around;
}
.stat-box {
  text-align: center;
}
.stat-number {
  display: block;
  font-size: 18px;
  font-weight: bold;
  color: #FF7600;
  margin-bottom: 5px;
}
.stat-desc {
  font-size: 13px;
  color: #8E8E93;
}
.distribution-settings {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.settings-text {
  font-size: 15px;
  color: #333;
}
.settings-arrow {
  width: 10px;
  height: 10px;
  border-top: 2px solid #C7C7CC;
  border-right: 2px solid #C7C7CC;
  transform: rotate(45deg);
}

/* 活动列表样式 */
.active-campaigns {
  margin: 0 15px 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}
.view-all {
  font-size: 14px;
  color: #FF7600;
}
.campaign-list {
  margin-top: 10px;
}
.campaign-item {
  background: #F8FAFC;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 10px;
  position: relative;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}
.campaign-status {
  position: absolute;
  top: 15px;
  right: 15px;
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 10px;
  z-index: 2;
  /* 确保状态标签在最上层 */
}
.campaign-status.active {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}
.campaign-status.ending {
  background: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}
.campaign-info {
  margin-bottom: 15px;
  position: relative;
  width: 100%;
  /* 确保宽度占满，便于管理布局 */
}
.campaign-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  margin-right: 60px;
  /* 为状态标签预留空间 */
  display: block;
  /* 确保名称单独一行 */
}
.campaign-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.campaign-time {
  font-size: 12px;
  color: #999;
  display: block;
  /* 确保时间单独一行 */
  width: auto;
  /* 根据内容自动调整宽度 */
  max-width: 60%;
  /* 最大宽度限制 */
  text-overflow: ellipsis;
  /* 文本溢出显示省略号 */
  white-space: nowrap;
  /* 防止换行 */
  overflow: hidden;
  /* 隐藏溢出部分 */
}
.campaign-metrics {
  display: flex;
}
.metric-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 5px 0;
  border-right: 1px solid #f0f0f0;
}
.metric-item:last-child {
  border-right: none;
}
.metric-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 3px;
}
.metric-label {
  font-size: 12px;
  color: #999;
}

/* 营销工具网格 */
.tool-section {
  margin: 10px 15px 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}
.tool-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}
.tool-card {
  width: 25%;
  padding: 7.5px;
  box-sizing: border-box;
  margin-bottom: 15px;
  transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
  position: relative;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
}
.tool-card.hover {
  transform: translateY(-5px) scale(1.03);
}
.tool-icon-wrap {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
  background: white;
  position: relative;
  overflow: hidden;
}
.tool-card.hover .tool-icon-wrap {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}
.tool-icon-wrap::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  border-radius: 50%;
  z-index: 1;
}
.tool-icon-svg {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  transition: all 0.3s ease;
}
.tool-icon-wrap.coupon {
  background: linear-gradient(135deg, #FF9966, #FF5E62);
}
.tool-icon-wrap.discount {
  background: linear-gradient(135deg, #FDEB71, #F8D800);
}
.tool-icon-wrap.group {
  background: linear-gradient(135deg, #36D1DC, #5B86E5);
}
.tool-icon-wrap.flash {
  background: linear-gradient(135deg, #FF6B6B, #F04172);
}
.tool-icon-wrap.points {
  background: linear-gradient(135deg, #38ADAE, #30CE9B);
}
.tool-icon-wrap.distribution {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
}
.tool-icon-wrap.member {
  background: linear-gradient(135deg, #8E2DE2, #4A00E0);
}
.tool-icon-wrap.redpacket {
  background: linear-gradient(135deg, #FF5858, #FF0000);
}

/* SVG图标样式 */
.tool-icon-svg {
  color: #fff;
}
.tool-name {
  font-size: 14px;
  color: #333;
  text-align: center;
  margin-bottom: 3px;
  font-weight: 500;
  transition: color 0.3s ease;
  width: 100%;
  display: inline-block;
}
.tool-name.three-chars {
  letter-spacing: 2px;
  text-indent: 2px;
}
.tool-card.hover .tool-name {
  color: #FF7600;
}
.tool-desc {
  font-size: 10px;
  color: #999;
  text-align: center;
  height: 28px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  transition: color 0.3s ease;
}

/* 高级营销功能样式 */
.advanced-section {
  margin: 0 15px 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}
.advanced-tools {
  margin-top: 10px;
}
.advanced-tool {
  background: #F8FAFC;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}
.tool-top {
  display: flex;
  margin-bottom: 10px;
}
.tool-icon {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-right: 15px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
}
.tool-icon.ai {
  background: linear-gradient(135deg, #9040FF, #5E35B1);
}
.tool-icon.segment {
  background: linear-gradient(135deg, #4481EB, #04BEFE);
}
.tool-icon.automation {
  background: linear-gradient(135deg, #FFB74D, #FF9800);
}
.icon-image {
  width: 24px;
  height: 24px;
  object-fit: contain;
}
.tool-info {
  flex: 1;
}
.tool-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}
.tool-subtitle {
  font-size: 12px;
  color: #666;
}
.tool-description {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 10px;
}
.tool-tags {
  display: flex;
  flex-wrap: wrap;
}
.tool-tag {
  font-size: 10px;
  color: #FF7600;
  background: rgba(255, 118, 0, 0.1);
  padding: 3px 8px;
  border-radius: 10px;
  margin-right: 8px;
  margin-bottom: 5px;
}

/* 营销日历样式 */
.calendar-section {
  margin: 0 15px 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}
.calendar-month {
  font-size: 14px;
  color: #999;
}
.calendar-events {
  margin-top: 10px;
}
.event-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}
.event-item:last-child {
  border-bottom: none;
}
.event-date {
  width: 50px;
  height: 50px;
  background: #F8FAFC;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  flex-shrink: 0;
}
.date-day {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
.date-month {
  font-size: 12px;
  color: #999;
}
.event-content {
  flex: 1;
  margin-right: 15px;
}
.event-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}
.event-info {
  display: flex;
  align-items: center;
}
.event-type {
  font-size: 10px;
  padding: 2px 8px;
  border-radius: 10px;
  margin-right: 10px;
}
.event-type.new-product {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}
.event-type.discount {
  background: rgba(255, 69, 58, 0.1);
  color: #FF453A;
}
.event-type.member {
  background: rgba(88, 86, 214, 0.1);
  color: #5856D6;
}
.event-time {
  font-size: 12px;
  color: #999;
}
.event-action {
  background: #F8FAFC;
  border-radius: 15px;
  padding: 5px 10px;
}
.action-text {
  font-size: 12px;
  color: #FF7600;
}
.add-event-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
  margin-top: 10px;
  border-top: 1px solid #f0f0f0;
}
.plus-icon {
  width: 16px;
  height: 16px;
  background: #FF7600;
  border-radius: 8px;
  position: relative;
  margin-right: 8px;
}
.plus-icon::before {
  content: "";
  position: absolute;
  width: 8px;
  height: 2px;
  background: #fff;
  top: 7px;
  left: 4px;
}
.plus-icon::after {
  content: "";
  position: absolute;
  width: 2px;
  height: 8px;
  background: #fff;
  top: 4px;
  left: 7px;
}
.btn-text {
  font-size: 14px;
  color: #FF7600;
}

/* AI营销建议样式 */
.ai-insights {
  margin: 0 15px 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}
.ai-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}
.ai-badge {
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #fff;
  font-size: 12px;
  font-weight: bold;
  width: 24px;
  height: 24px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}
.ai-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.insights-list {
  margin-top: 10px;
}
.insight-item {
  background: #F8FAFC;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}
.insight-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  margin-right: 12px;
  margin-bottom: 10px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.insight-icon.insight {
  background: rgba(52, 199, 89, 0.1);
  position: relative;
}
.insight-icon.insight::before {
  content: "";
  width: 18px;
  height: 18px;
  background: #34C759;
  border-radius: 9px;
  position: absolute;
}
.insight-icon.insight::after {
  content: "i";
  color: #fff;
  font-weight: bold;
  font-size: 14px;
  position: relative;
  z-index: 1;
}
.insight-icon.warning {
  background: rgba(255, 149, 0, 0.1);
  position: relative;
}
.insight-icon.warning::before {
  content: "!";
  color: #FF9500;
  font-weight: bold;
  font-size: 20px;
}
.insight-icon.opportunity {
  background: rgba(88, 86, 214, 0.1);
  position: relative;
}
.insight-icon.opportunity::before {
  content: "+";
  color: #5856D6;
  font-weight: bold;
  font-size: 20px;
}
.insight-content {
  margin-bottom: 15px;
}
.insight-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}
.insight-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}
.insight-actions {
  display: flex;
  justify-content: flex-end;
}
.action-btn {
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 14px;
  margin-left: 10px;
}
.action-btn.primary {
  background: #FF7600;
  color: #fff;
}
.action-btn.secondary {
  background: #F0F0F0;
  color: #666;
}

/* 浮动操作按钮 */
.floating-action-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: #FF7600;
  box-shadow: 0 4px 15px rgba(255, 118, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}
.fab-icon {
  font-size: 28px;
  color: #fff;
  font-weight: 300;
  line-height: 1;
  margin-top: -2px;
}

/* 响应式调整 */
@media screen and (max-width: 350px) {
.tool-card {
    width: 33.33%;
}
}
/* 智能营销助手样式 */
.ai-marketing-section {
  margin: 15px;
  padding: 15px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.ai-tools-grid {
  margin-top: 10px;
}
.ai-tool-card {
  display: flex;
  align-items: center;
  padding: 15px;
  margin-bottom: 10px;
  background: #F8FAFC;
  border-radius: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}
.ai-tool-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}
.ai-tool-icon.blue {
  background-color: rgba(25, 137, 250, 0.1);
}
.ai-tool-icon.yellow {
  background-color: rgba(255, 149, 0, 0.1);
}
.ai-tool-icon.green {
  background-color: rgba(52, 199, 89, 0.1);
}
.ai-tool-content {
  flex: 1;
}
.ai-tool-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}
.ai-tool-desc {
  font-size: 12px;
  color: #999;
  line-height: 1.5;
}

/* 底部导航栏样式调整，确保图标正确显示 */
 .tab-icon {
  display: block !important;
}

/* 确保底部导航栏的SVG图标正确显示 */
 .tab-icon svg {
  width: 22px;
  height: 22px;
  fill: currentColor;
}

/* 营销中心特定的样式调整 */
 .tab-item.active[data-tab=marketing] .tab-icon {
  color: #FF7600;
}
 .tab-item.active[data-tab=marketing] .tab-text {
  color: #FF7600;
}

/* 底部导航栏 */
.tab-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  background-color: var(--bg-primary, #FFFFFF);
  border-top: 1px solid var(--border-light, #F0F0F0);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  padding: 0 10px;
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #FFFFFF;
  flex-shrink: 0;
}
.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
  transition: all 0.2s ease;
  padding: 8px 0;
  margin: 0 8px;
}
.tab-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 3px;
  transition: all 0.25s cubic-bezier(0.3, 0.7, 0.4, 1.5);
  background-position: center;
  background-repeat: no-repeat;
  background-size: 22px;
  opacity: 0.7;
}
.tab-text {
  font-size: 10px;
  color: var(--text-tertiary, #999999);
  transition: color 0.2s ease, transform 0.25s ease;
  transform: scale(0.9);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  padding: 0 2px;
}
.active-indicator {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 16px;
  height: 3px;
  border-radius: 0 0 4px 4px;
  background: linear-gradient(90deg, var(--brand-primary, #1677FF), var(--accent-purple, #7265E6));
}
.tab-item.active .tab-icon {
  transform: translateY(-2px);
  opacity: 1;
}
.tab-item.active .tab-text {
  color: var(--brand-primary, #1677FF);
  font-weight: 500;
  transform: scale(1);
}

/* 导航图标样式 */
.tab-icon.dashboard {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z'/%3E%3C/svg%3E");
}
.tab-icon.store {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M20 4H4v2h16V4zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6h1zm-9 4H6v-4h6v4z'/%3E%3C/svg%3E");
}
.tab-icon.marketing {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z'/%3E%3C/svg%3E");
}
.tab-icon.orders {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
}
.tab-icon.more {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z'/%3E%3C/svg%3E");
}
@media screen and (max-width: 375px) {
  /* 在较小屏幕上优化导航栏 */
.tab-text {
    font-size: 9px;
}
.tab-icon {
    margin-bottom: 2px;
    background-size: 20px;
}
}
/* 导航图标样式 */
.tab-icon.dashboard {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z'/%3E%3C/svg%3E");
}
.tab-icon.store {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M20 4H4v2h16V4zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6h1zm-9 4H6v-4h6v4z'/%3E%3C/svg%3E");
}
.tab-icon.marketing {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF7600'%3E%3Cpath d='M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z'/%3E%3C/svg%3E");
}
.tab-icon.orders {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
}
.tab-icon.more {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z'/%3E%3C/svg%3E");
}

/* 浮动操作按钮 */
.floating-action-button {
  position: fixed;
  bottom: 80px;
  right: 20px;
  width: 56px;
  height: 56px;
  border-radius: 16px;
  background: linear-gradient(135deg, #FF7600, #FF4500);
  box-shadow: 0 4px 16px rgba(255, 118, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.floating-action-button:active {
  transform: scale(0.95) translateY(2px);
  box-shadow: 0 2px 8px rgba(255, 118, 0, 0.2);
}
.fab-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.fab-icon-plus {
  width: 20px;
  height: 20px;
  position: relative;
  margin-bottom: 2px;
}
.fab-icon-plus::before,
.fab-icon-plus::after {
  content: "";
  position: absolute;
  background-color: white;
}
.fab-icon-plus::before {
  width: 20px;
  height: 2px;
  top: 9px;
  left: 0;
}
.fab-icon-plus::after {
  width: 2px;
  height: 20px;
  top: 0;
  left: 9px;
}
.fab-text {
  font-size: 12px;
  color: white;
  font-weight: 500;
  margin-top: 2px;
}
.safe-area-bottom {
  height: calc(60px + env(safe-area-inset-bottom));
}
@media screen and (max-width: 375px) {
  /* 在较小屏幕上优化导航栏 */
.tab-text {
    font-size: 9px;
}
.tab-icon {
    margin-bottom: 2px;
    background-size: 20px;
}
}
/* 在较宽屏幕上优化导航栏，确保元素不会挤压 */
@media screen and (min-width: 400px) {
.tab-bar {
    padding: 0 10px;
}
.tab-item {
    margin: 0 5px;
}
}
/* 导航图标样式 */
.tab-bar .tab-icon.dashboard {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z'/%3E%3C/svg%3E");
}
.tab-bar .tab-icon.store {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M20 4H4v2h16V4zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6h1zm-9 4H6v-4h6v4z'/%3E%3C/svg%3E");
}
.tab-bar .tab-icon.marketing {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF7600'%3E%3Cpath d='M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z'/%3E%3C/svg%3E");
}
.tab-bar .tab-icon.orders {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
}
.tab-bar .tab-icon.more {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z'/%3E%3C/svg%3E");
}

/* 底部导航栏 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 56px;
  background-color: #FFFFFF;
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-top: 1px solid #F0F0F0;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 100;
}
.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 6px 0;
  box-sizing: border-box;
  position: relative;
}
.nav-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  color: #999999;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: visible;
}
.nav-text {
  font-size: 10px;
  color: #999999;
}
.nav-item.active .nav-icon {
  color: #1989FA;
}
.nav-item.active .nav-text {
  color: #1989FA;
}

/* 营销中心用橙色 */
.nav-item.active:nth-child(3) .nav-icon {
  color: #FF7600;
}
.nav-item.active:nth-child(3) .nav-text {
  color: #FF7600;
}

/* 数据概览用蓝色 */
.nav-item.active:nth-child(1) .nav-icon {
  color: #1989FA;
}
.nav-item.active:nth-child(1) .nav-text {
  color: #1989FA;
}
.safe-area-bottom {
  height: calc(56px + env(safe-area-inset-bottom));
}