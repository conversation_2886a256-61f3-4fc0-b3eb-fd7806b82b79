{"version": 3, "file": "secondHand.js", "sources": ["mock/publish/secondHand.js"], "sourcesContent": ["// 二手闲置详情模拟数据\r\nexport const secondHandDetail = {\r\n  id: 'second-hand-001',\r\n  title: '9成新iPhone 13 128G 午夜蓝',\r\n  price: 3800,\r\n  originalPrice: 5999, // 原价\r\n  category: '手机数码',\r\n  subcategory: '手机',\r\n  brand: 'Apple',\r\n  model: 'iPhone 13',\r\n  purchaseTime: '2022-10', // 购买时间\r\n  usageTime: '1年5个月', // 使用时长\r\n  condition: '9成新',\r\n  warranty: '还有6个月保修期',\r\n  accessories: ['原装充电器', '原装数据线', '保护壳', '钢化膜'],\r\n  description: '因换新机出售，iPhone 13 128G 午夜蓝色，无拆修，无进水，电池健康度89%，面容ID正常，所有功能正常，成色新，附赠原装充电器、数据线、保护壳和钢化膜。',\r\n  contact: {\r\n    name: '王先生',\r\n    phone: '132****4567',\r\n    wechat: 'wangxiansheng123'\r\n  },\r\n  deliveryMethod: ['自提', '邮寄'], // 交付方式\r\n  paymentMethod: ['微信', '支付宝', '现金'], // 支付方式\r\n  negotiable: true, // 是否可议价\r\n  images: [\r\n    '/static/images/secondhand/phone-1.jpg',\r\n    '/static/images/secondhand/phone-2.jpg',\r\n    '/static/images/secondhand/phone-3.jpg',\r\n    '/static/images/secondhand/phone-4.jpg'\r\n  ],\r\n  location: {\r\n    latitude: 36.314736,\r\n    longitude: 114.711234,\r\n    address: '磁县城区幸福路789号'\r\n  },\r\n  publishTime: '2024-03-13 16:20:00',\r\n  views: 165,\r\n  likes: 12,\r\n  collections: 7,\r\n  status: 'active' // active, sold, expired\r\n};\r\n\r\n// 相关二手闲置模拟数据\r\nexport const relatedSecondHands = [\r\n  {\r\n    id: 'second-hand-002',\r\n    title: '95新华为P40 Pro 256G 亮黑色',\r\n    price: 2200,\r\n    originalPrice: 5988,\r\n    category: '手机数码',\r\n    subcategory: '手机',\r\n    condition: '9.5成新',\r\n    image: '/static/images/secondhand/phone-5.jpg'\r\n  },\r\n  {\r\n    id: 'second-hand-003',\r\n    title: '全新未拆封小米13 Ultra 512G',\r\n    price: 5500,\r\n    originalPrice: 6999,\r\n    category: '手机数码',\r\n    subcategory: '手机',\r\n    condition: '全新',\r\n    image: '/static/images/secondhand/phone-6.jpg'\r\n  },\r\n  {\r\n    id: 'second-hand-004',\r\n    title: '8成新iPad Pro 11英寸 2021款 256G',\r\n    price: 4200,\r\n    originalPrice: 6799,\r\n    category: '手机数码',\r\n    subcategory: '平板电脑',\r\n    condition: '8成新',\r\n    image: '/static/images/secondhand/tablet-1.jpg'\r\n  }\r\n];\r\n\r\n// 获取二手闲置详情的API函数\r\nexport const fetchSecondHandDetail = (id) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve(secondHandDetail);\r\n    }, 500);\r\n  });\r\n};\r\n\r\n// 获取相关二手闲置的API函数\r\nexport const fetchRelatedSecondHands = () => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve(relatedSecondHands);\r\n    }, 500);\r\n  });\r\n}; "], "names": [], "mappings": ";AACO,MAAM,mBAAmB;AAAA,EAC9B,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,OAAO;AAAA,EACP,eAAe;AAAA;AAAA,EACf,UAAU;AAAA,EACV,aAAa;AAAA,EACb,OAAO;AAAA,EACP,OAAO;AAAA,EACP,cAAc;AAAA;AAAA,EACd,WAAW;AAAA;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa,CAAC,SAAS,SAAS,OAAO,KAAK;AAAA,EAC5C,aAAa;AAAA,EACb,SAAS;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,EACT;AAAA,EACD,gBAAgB,CAAC,MAAM,IAAI;AAAA;AAAA,EAC3B,eAAe,CAAC,MAAM,OAAO,IAAI;AAAA;AAAA,EACjC,YAAY;AAAA;AAAA,EACZ,QAAQ;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,UAAU;AAAA,IACV,WAAW;AAAA,IACX,SAAS;AAAA,EACV;AAAA,EACD,aAAa;AAAA,EACb,OAAO;AAAA,EACP,OAAO;AAAA,EACP,aAAa;AAAA,EACb,QAAQ;AAAA;AACV;AAGO,MAAM,qBAAqB;AAAA,EAChC;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,OAAO;AAAA,IACP,eAAe;AAAA,IACf,UAAU;AAAA,IACV,aAAa;AAAA,IACb,WAAW;AAAA,IACX,OAAO;AAAA,EACR;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,OAAO;AAAA,IACP,eAAe;AAAA,IACf,UAAU;AAAA,IACV,aAAa;AAAA,IACb,WAAW;AAAA,IACX,OAAO;AAAA,EACR;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,OAAO;AAAA,IACP,eAAe;AAAA,IACf,UAAU;AAAA,IACV,aAAa;AAAA,IACb,WAAW;AAAA,IACX,OAAO;AAAA,EACR;AACH;AAGY,MAAC,wBAAwB,CAAC,OAAO;AAC3C,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,cAAQ,gBAAgB;AAAA,IACzB,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,0BAA0B,MAAM;AAC3C,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,cAAQ,kBAAkB;AAAA,IAC3B,GAAE,GAAG;AAAA,EACV,CAAG;AACH;;;"}