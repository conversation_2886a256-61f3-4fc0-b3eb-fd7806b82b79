// 交易记录模拟数据
export const transactionList = [
  {
    id: 'trans-001',
    type: 'expense', // income, expense, refund, withdraw, recharge
    amount: 68.00,
    balance: 1280.50,
    title: '商家消费',
    description: '磁县老味道餐厅',
    time: '2024-03-15 12:30',
    status: 'success', // success, pending, failed
    paymentMethod: '钱包余额',
    category: '餐饮美食',
    orderNo: 'DD20240315123001',
    icon: '/static/images/payment/restaurant.png'
  },
  {
    id: 'trans-002',
    type: 'recharge',
    amount: 200.00,
    balance: 1348.50,
    title: '钱包充值',
    description: '微信支付充值',
    time: '2024-03-14 18:45',
    status: 'success',
    paymentMethod: '微信支付',
    category: '充值',
    orderNo: 'CZ20240314184501',
    icon: '/static/images/payment/recharge.png'
  },
  {
    id: 'trans-003',
    type: 'expense',
    amount: 45.50,
    balance: 1148.50,
    title: '商家消费',
    description: '磁县优品生活超市',
    time: '2024-03-13 10:20',
    status: 'success',
    paymentMethod: '钱包余额',
    category: '日用百货',
    orderNo: 'DD20240313102001',
    icon: '/static/images/payment/shopping.png'
  },
  {
    id: 'trans-004',
    type: 'withdraw',
    amount: 500.00,
    balance: 1194.00,
    title: '提现',
    description: '提现至建设银行(3456)',
    time: '2024-03-10 16:30',
    status: 'success',
    paymentMethod: '银行卡',
    category: '提现',
    orderNo: 'TX20240310163001',
    icon: '/static/images/payment/withdraw.png'
  },
  {
    id: 'trans-005',
    type: 'income',
    amount: 120.00,
    balance: 1694.00,
    title: '收入',
    description: '二手商品出售',
    time: '2024-03-08 14:15',
    status: 'success',
    paymentMethod: '钱包余额',
    category: '二手交易',
    orderNo: 'SR20240308141501',
    icon: '/static/images/payment/income.png'
  },
  {
    id: 'trans-006',
    type: 'refund',
    amount: 25.50,
    balance: 1574.00,
    title: '退款',
    description: '商品退款-磁县优品生活超市',
    time: '2024-03-07 09:40',
    status: 'success',
    paymentMethod: '钱包余额',
    category: '退款',
    orderNo: 'TK20240307094001',
    icon: '/static/images/payment/refund.png'
  }
];

// 交易分类
export const transactionCategories = [
  { id: 'all', name: '全部' },
  { id: 'expense', name: '支出' },
  { id: 'income', name: '收入' },
  { id: 'refund', name: '退款' },
  { id: 'withdraw', name: '提现' },
  { id: 'recharge', name: '充值' }
];

// 获取交易记录的API函数
export const fetchTransactionList = (type = 'all', page = 1, pageSize = 10) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let result = [...transactionList];
      
      // 按类型筛选
      if (type !== 'all') {
        result = result.filter(item => item.type === type);
      }
      
      // 分页处理
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      const data = result.slice(start, end);
      
      // 返回数据和分页信息
      resolve({
        list: data,
        total: result.length,
        hasMore: end < result.length
      });
    }, 500);
  });
};

// 获取交易详情的API函数
export const fetchTransactionDetail = (id) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const transaction = transactionList.find(item => item.id === id);
      
      if (transaction) {
        // 添加更多详细信息
        const detailedTransaction = {
          ...transaction,
          merchant: {
            name: transaction.description,
            address: '磁县城区幸福路123号',
            phone: '0310-12345678'
          },
          items: [
            {
              name: '商品1',
              price: transaction.amount * 0.6,
              quantity: 1
            },
            {
              name: '商品2',
              price: transaction.amount * 0.4,
              quantity: 2
            }
          ],
          paymentTime: transaction.time,
          completeTime: transaction.status === 'success' ? transaction.time : null,
          remark: '无'
        };
        
        resolve({
          success: true,
          data: detailedTransaction
        });
      } else {
        resolve({
          success: false,
          message: '交易记录不存在'
        });
      }
    }, 500);
  });
};

// 获取交易统计的API函数
export const fetchTransactionStats = (month) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟统计数据
      const stats = {
        totalExpense: 139.00,
        totalIncome: 120.00,
        totalRefund: 25.50,
        totalWithdraw: 500.00,
        totalRecharge: 200.00,
        categoryStats: [
          { category: '餐饮美食', amount: 68.00, percentage: 48.9 },
          { category: '日用百货', amount: 45.50, percentage: 32.7 },
          { category: '其他', amount: 25.50, percentage: 18.4 }
        ],
        dailyStats: [
          { date: '03-07', expense: 0, income: 0, refund: 25.50 },
          { date: '03-08', expense: 0, income: 120.00, refund: 0 },
          { date: '03-09', expense: 0, income: 0, refund: 0 },
          { date: '03-10', expense: 0, income: 0, refund: 0, withdraw: 500.00 },
          { date: '03-11', expense: 0, income: 0, refund: 0 },
          { date: '03-12', expense: 0, income: 0, refund: 0 },
          { date: '03-13', expense: 45.50, income: 0, refund: 0 },
          { date: '03-14', expense: 0, income: 0, refund: 0, recharge: 200.00 },
          { date: '03-15', expense: 68.00, income: 0, refund: 0 }
        ]
      };
      
      resolve(stats);
    }, 800);
  });
}; 