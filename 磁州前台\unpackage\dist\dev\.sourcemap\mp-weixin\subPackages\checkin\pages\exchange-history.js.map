{"version": 3, "file": "exchange-history.js", "sources": ["subPackages/checkin/pages/exchange-history.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcY2hlY2tpblxwYWdlc1xleGNoYW5nZS1oaXN0b3J5LnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"page-root\">\r\n\t\t<view class=\"nav-bg\" :style=\"{ height: (statusBarHeight + 44) + 'px' }\"></view>\r\n\t\t<view class=\"navbar-content\" :style=\"{ top: statusBarHeight + 'px', height: '44px' }\">\r\n\t\t\t<view class=\"navbar-left\" @click=\"goBack\">\r\n\t\t\t\t<image class=\"back-icon\" src=\"/static/images/tabbar/最新返回键.png\" mode=\"aspectFit\"></image>\r\n\t\t\t</view>\r\n\t\t\t<text class=\"navbar-title\">兑换记录</text>\r\n\t\t\t<view class=\"navbar-right\"></view>\r\n\t\t</view>\r\n\t\t<view class=\"exchange-history-container\" :style=\"{ paddingTop: (statusBarHeight + 44) + 'px' }\">\r\n\t\t<!-- 筛选标签 -->\r\n\t\t<view class=\"filter-tabs\">\r\n\t\t\t<view \r\n\t\t\t\tclass=\"tab-item\" \r\n\t\t\t\t:class=\"{ active: currentTab === index }\"\r\n\t\t\t\tv-for=\"(tab, index) in tabs\" \r\n\t\t\t\t:key=\"index\"\r\n\t\t\t\t@click=\"switchTab(index)\"\r\n\t\t\t>\r\n\t\t\t\t{{ tab }}\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 记录列表 -->\r\n\t\t<scroll-view \r\n\t\t\tscroll-y \r\n\t\t\tclass=\"record-list\" \r\n\t\t\trefresher-enabled \r\n\t\t\t@refresherrefresh=\"refreshRecords\" \r\n\t\t\t:refresher-triggered=\"isRefreshing\"\r\n\t\t>\r\n\t\t\t<view v-if=\"filteredRecords.length > 0\">\r\n\t\t\t\t<view class=\"record-item\" v-for=\"(record, index) in filteredRecords\" :key=\"index\" @click=\"showRecordDetail(record)\">\r\n\t\t\t\t\t<view class=\"record-main\">\r\n\t\t\t\t\t\t<image class=\"record-image\" :src=\"record.image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t<view class=\"record-info\">\r\n\t\t\t\t\t\t\t<view class=\"record-name\">{{ record.name }}</view>\r\n\t\t\t\t\t\t\t<view class=\"record-time\">{{ record.time }}</view>\r\n\t\t\t\t\t\t\t<view class=\"record-status\" :class=\"'status-' + record.status\">{{ statusText[record.status] }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"record-points\">{{ record.points }}积分</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 空状态 -->\r\n\t\t\t<view class=\"empty-state\" v-else>\r\n\t\t\t\t<image class=\"empty-icon\" src=\"/static/images/tabbar/empty.png\"></image>\r\n\t\t\t\t<view class=\"empty-text\">暂无兑换记录</view>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\t\t\r\n\t\t<!-- 详情弹窗 -->\r\n\t\t<view class=\"detail-popup\" v-if=\"showDetailPopup\" @click.self=\"closeDetailPopup\">\r\n\t\t\t<view class=\"popup-content\">\r\n\t\t\t\t<view class=\"popup-header\">\r\n\t\t\t\t\t<text class=\"popup-title\">兑换详情</text>\r\n\t\t\t\t\t<view class=\"popup-close\" @click=\"closeDetailPopup\">×</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"popup-body\">\r\n\t\t\t\t\t<view class=\"detail-item\">\r\n\t\t\t\t\t\t<image class=\"detail-image\" :src=\"selectedRecord.image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t<view class=\"detail-info\">\r\n\t\t\t\t\t\t\t<view class=\"detail-name\">{{ selectedRecord.name }}</view>\r\n\t\t\t\t\t\t\t<view class=\"detail-points\">{{ selectedRecord.points }}积分</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"detail-grid\">\r\n\t\t\t\t\t\t<view class=\"grid-item\">\r\n\t\t\t\t\t\t\t<view class=\"grid-label\">兑换时间</view>\r\n\t\t\t\t\t\t\t<view class=\"grid-value\">{{ selectedRecord.fullTime || selectedRecord.time }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"grid-item\">\r\n\t\t\t\t\t\t\t<view class=\"grid-label\">订单编号</view>\r\n\t\t\t\t\t\t\t<view class=\"grid-value\">{{ selectedRecord.orderNo }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"grid-item\">\r\n\t\t\t\t\t\t\t<view class=\"grid-label\">兑换状态</view>\r\n\t\t\t\t\t\t\t<view class=\"grid-value status\" :class=\"'status-' + selectedRecord.status\">{{ statusText[selectedRecord.status] }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"grid-item\" v-if=\"selectedRecord.status === 'using'\">\r\n\t\t\t\t\t\t\t<view class=\"grid-label\">有效期</view>\r\n\t\t\t\t\t\t\t<view class=\"grid-value\">{{ selectedRecord.validity }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"grid-item\" v-if=\"selectedRecord.status === 'shipping' || selectedRecord.status === 'shipped'\">\r\n\t\t\t\t\t\t\t<view class=\"grid-label\">收货地址</view>\r\n\t\t\t\t\t\t\t<view class=\"grid-value\">{{ selectedRecord.address }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"grid-item\" v-if=\"selectedRecord.status === 'shipped'\">\r\n\t\t\t\t\t\t\t<view class=\"grid-label\">物流信息</view>\r\n\t\t\t\t\t\t\t<view class=\"grid-value\">{{ selectedRecord.logistics }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"detail-actions\" v-if=\"selectedRecord.status === 'using'\">\r\n\t\t\t\t\t\t<button class=\"action-btn\" @click=\"useProduct\">立即使用</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail-actions\" v-if=\"selectedRecord.status === 'shipping'\">\r\n\t\t\t\t\t\t<button class=\"action-btn\" @click=\"trackOrder\">查看物流</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail-actions\" v-if=\"selectedRecord.status === 'shipped'\">\r\n\t\t\t\t\t\t<button class=\"action-btn confirm-btn\" @click=\"confirmReceive\">确认收货</button>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue';\r\n\r\n// Vue3迁移代码开始\r\n// 响应式状态\r\nconst statusBarHeight = ref(20);\r\nconst currentTab = ref(0);\r\nconst isRefreshing = ref(false);\r\nconst showDetailPopup = ref(false);\r\nconst selectedRecord = ref({});\r\n\r\n// 常量数据\r\nconst tabs = ['全部', '待发货', '已发货', '使用中'];\r\nconst statusText = {\r\n\t'checking': '审核中',\r\n\t'shipping': '待发货',\r\n\t'shipped': '已发货',\r\n\t'using': '使用中',\r\n\t'used': '已使用',\r\n\t'expired': '已过期'\r\n};\r\n\r\n// 记录数据\r\nconst records = ref([\r\n\t{\r\n\t\tid: 1,\r\n\t\tname: '5元通用券',\r\n\t\tpoints: 100,\r\n\t\ttime: '2023-11-25 14:30',\r\n\t\tstatus: 'using',\r\n\t\timage: '/static/images/banner/coupon-1.png',\r\n\t\torderNo: 'E202311250001',\r\n\t\tvalidity: '2023-12-25前有效',\r\n\t\ttype: 'coupon'\r\n\t},\r\n\t{\r\n\t\tid: 2,\r\n\t\tname: '精美保温杯',\r\n\t\tpoints: 1200,\r\n\t\ttime: '2023-11-22 09:15',\r\n\t\tstatus: 'shipping',\r\n\t\timage: '/static/images/banner/product-1.png',\r\n\t\torderNo: 'E202311220003',\r\n\t\taddress: '河北省邯郸市肥乡区北尹堡村1号',\r\n\t\ttype: 'product'\r\n\t},\r\n\t{\r\n\t\tid: 3,\r\n\t\tname: '10元外卖券',\r\n\t\tpoints: 180,\r\n\t\ttime: '2023-11-20 18:45',\r\n\t\tstatus: 'used',\r\n\t\timage: '/static/images/banner/coupon-2.png',\r\n\t\torderNo: 'E202311200007',\r\n\t\ttype: 'coupon'\r\n\t},\r\n\t{\r\n\t\tid: 4,\r\n\t\tname: '会员月卡',\r\n\t\tpoints: 500,\r\n\t\ttime: '2023-11-15 10:22',\r\n\t\tstatus: 'expired',\r\n\t\timage: '/static/images/banner/vip-1.png',\r\n\t\torderNo: 'E202311150012',\r\n\t\ttype: 'vip'\r\n\t},\r\n\t{\r\n\t\tid: 5,\r\n\t\tname: '限时抢购券',\r\n\t\tpoints: 250,\r\n\t\ttime: '2023-11-10 16:30',\r\n\t\tstatus: 'shipped',\r\n\t\timage: '/static/images/banner/coupon-3.png',\r\n\t\torderNo: 'E202311100025',\r\n\t\tlogistics: '顺丰速运 SF1234567890',\r\n\t\taddress: '河北省邯郸市肥乡区北尹堡村1号',\r\n\t\ttype: 'coupon'\r\n\t}\r\n]);\r\n\r\n// 计算属性\r\nconst filteredRecords = computed(() => {\r\n\tif (currentTab.value === 0) {\r\n\t\treturn records.value;\r\n\t} else if (currentTab.value === 1) {\r\n\t\t// 待发货\r\n\t\treturn records.value.filter(record => record.status === 'shipping');\r\n\t} else if (currentTab.value === 2) {\r\n\t\t// 已发货\r\n\t\treturn records.value.filter(record => record.status === 'shipped');\r\n\t} else {\r\n\t\t// 使用中\r\n\t\treturn records.value.filter(record => record.status === 'using');\r\n\t}\r\n});\r\n\r\n// 生命周期钩子\r\nonMounted(() => {\r\n\t// 获取状态栏高度\r\n\tconst sysInfo = uni.getSystemInfoSync();\r\n\tstatusBarHeight.value = sysInfo.statusBarHeight || 20;\r\n\t\r\n\t// 加载兑换记录\r\n\tloadExchangeRecords();\r\n});\r\n\r\n// 方法\r\n// 切换标签\r\nfunction switchTab(index) {\r\n\tcurrentTab.value = index;\r\n}\r\n\r\n// 刷新记录\r\nfunction refreshRecords() {\r\n\tisRefreshing.value = true;\r\n\t\r\n\t// 模拟刷新操作\r\n\tsetTimeout(() => {\r\n\t\tloadExchangeRecords();\r\n\t\tisRefreshing.value = false;\r\n\t\t\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '刷新成功',\r\n\t\t\ticon: 'none'\r\n\t\t});\r\n\t}, 1500);\r\n}\r\n\r\n// 加载兑换记录\r\nfunction loadExchangeRecords() {\r\n\t// 在实际应用中，应该从API获取数据\r\n\t// 这里使用模拟数据\r\n\t// setTimeout(() => {\r\n\t//   records.value = [...];\r\n\t// }, 500);\r\n}\r\n\r\n// 显示记录详情\r\nfunction showRecordDetail(record) {\r\n\tselectedRecord.value = { ...record };\r\n\tshowDetailPopup.value = true;\r\n}\r\n\r\n// 关闭详情弹窗\r\nfunction closeDetailPopup() {\r\n\tshowDetailPopup.value = false;\r\n}\r\n\r\n// 使用商品\r\nfunction useProduct() {\r\n\tif (selectedRecord.value.type === 'coupon') {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '优惠券已复制到剪贴板',\r\n\t\t\ticon: 'none'\r\n\t\t});\r\n\t} else if (selectedRecord.value.type === 'vip') {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: 'VIP权益已激活',\r\n\t\t\ticon: 'success'\r\n\t\t});\r\n\t} else {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '即将跳转到使用页面',\r\n\t\t\ticon: 'none'\r\n\t\t});\r\n\t}\r\n\t\r\n\tcloseDetailPopup();\r\n}\r\n\r\n// 查看物流\r\nfunction trackOrder() {\r\n\tuni.showToast({\r\n\t\ttitle: '正在开发中，敬请期待',\r\n\t\ticon: 'none'\r\n\t});\r\n\t\r\n\t// closeDetailPopup();\r\n}\r\n\r\n// 确认收货\r\nfunction confirmReceive() {\r\n\tuni.showModal({\r\n\t\ttitle: '确认收货',\r\n\t\tcontent: '确认已收到商品？确认后无法撤销',\r\n\t\tsuccess: (res) => {\r\n\t\t\tif (res.confirm) {\r\n\t\t\t\t// 更新状态\r\n\t\t\t\tconst index = records.value.findIndex(item => item.id === selectedRecord.value.id);\r\n\t\t\t\tif (index !== -1) {\r\n\t\t\t\t\trecords.value[index].status = 'using';\r\n\t\t\t\t\tselectedRecord.value.status = 'using';\r\n\t\t\t\t\t\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '确认收货成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t});\r\n}\r\n\r\n// 返回上一页\r\nfunction goBack() {\r\n\tuni.navigateBack();\r\n}\r\n// Vue3迁移代码结束\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.page-root {\r\n\tposition: relative;\r\n\tmin-height: 100vh;\r\n\tbackground: #f6faff;\r\n}\r\n.nav-bg {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbackground: #1677FF;\r\n\tz-index: 100;\r\n\twidth: 100%;\r\n}\r\n.navbar-content {\r\n\tposition: fixed;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tz-index: 101;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tbackground: transparent;\r\n\twidth: 100%;\r\n}\r\n.navbar-left, .navbar-right {\r\n\twidth: 44px;\r\n\theight: 44px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n.back-icon {\r\n\twidth: 24px;\r\n\theight: 24px;\r\n\tdisplay: block;\r\n\tbackground: none;\r\n\tborder-radius: 0;\r\n\tmargin: 0 auto;\r\n}\r\n.navbar-title {\r\n\tflex: 1;\r\n\ttext-align: center;\r\n\tfont-size: 18px;\r\n\tfont-weight: 600;\r\n\tcolor: #fff;\r\n\tline-height: 44px;\r\n\twhite-space: nowrap;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n}\r\n.exchange-history-container {\r\n\tmin-height: 100vh;\r\n\tposition: relative;\r\n\tbox-sizing: border-box;\r\n\tbackground: #f6faff;\r\n}\r\n.record-list {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n}\r\n.record-item {\r\n\tmax-width: 300px;\r\n\twidth: 100%;\r\n\tmargin: 0 auto 16px auto;\r\n\tbackground: #fff;\r\n\tborder-radius: 14px;\r\n\tbox-shadow: 0 6px 24px rgba(22,119,255,0.12), 0 1.5px 6px rgba(0,0,0,0.06);\r\n\tpadding: 18px 16px;\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tword-break: break-all;\r\n\ttransition: box-shadow 0.3s;\r\n}\r\n.record-item:active, .record-item:focus, .record-item:hover {\r\n\tbox-shadow: 0 12px 32px rgba(22,119,255,0.18), 0 3px 12px rgba(0,0,0,0.10);\r\n}\r\n.record-main {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tflex: 1;\r\n}\r\n.record-image {\r\n\twidth: 120rpx;\r\n\theight: 120rpx;\r\n\tborder-radius: 12rpx;\r\n\tobject-fit: cover;\r\n\tbackground-color: #f5f5f5;\r\n\tmargin-right: 20rpx;\r\n}\r\n.record-info {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n.record-name {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tfont-weight: 500;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n.record-time {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n.record-status {\r\n\tdisplay: inline-block;\r\n\tfont-size: 22rpx;\r\n\tpadding: 4rpx 12rpx;\r\n\tborder-radius: 10rpx;\r\n}\r\n.status-checking {\r\n\tbackground-color: rgba(153, 153, 153, 0.1);\r\n\tcolor: #999;\r\n}\r\n.status-shipping {\r\n\tbackground-color: rgba(83, 166, 255, 0.1);\r\n\tcolor: #3a86ff;\r\n}\r\n.status-shipped {\r\n\tbackground-color: rgba(255, 102, 0, 0.1);\r\n\tcolor: #ff6600;\r\n}\r\n.status-using {\r\n\tbackground-color: rgba(0, 191, 131, 0.1);\r\n\tcolor: #00bf83;\r\n}\r\n.status-used {\r\n\tbackground-color: rgba(153, 153, 153, 0.1);\r\n\tcolor: #999;\r\n}\r\n.status-expired {\r\n\tbackground-color: rgba(153, 153, 153, 0.1);\r\n\tcolor: #999;\r\n}\r\n.record-points {\r\n\tfont-size: 28rpx;\r\n\tcolor: #ff6b6b;\r\n\tfont-weight: 500;\r\n}\r\n.empty-state {\r\n\tpadding: 100rpx 0;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n.empty-icon {\r\n\twidth: 200rpx;\r\n\theight: 200rpx;\r\n\tmargin-bottom: 30rpx;\r\n\topacity: 0.6;\r\n}\r\n.empty-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #999;\r\n}\r\n.detail-popup {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground-color: rgba(0, 0, 0, 0.6);\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tz-index: 999;\r\n}\r\n.popup-content {\r\n\twidth: 80%;\r\n\tmax-width: 600rpx;\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 24rpx;\r\n\toverflow: hidden;\r\n\tbox-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.15);\r\n}\r\n.popup-header {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tpadding: 30rpx;\r\n\tborder-bottom: 1px solid #f1f1f1;\r\n}\r\n.popup-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #333;\r\n}\r\n.popup-close {\r\n\tfont-size: 40rpx;\r\n\tcolor: #999;\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n.popup-body {\r\n\tpadding: 30rpx;\r\n}\r\n.detail-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding-bottom: 30rpx;\r\n\tmargin-bottom: 30rpx;\r\n\tborder-bottom: 1px solid #f5f5f5;\r\n}\r\n.detail-image {\r\n\twidth: 150rpx;\r\n\theight: 150rpx;\r\n\tborder-radius: 12rpx;\r\n\tobject-fit: cover;\r\n\tmargin-right: 20rpx;\r\n}\r\n.detail-info {\r\n\tflex: 1;\r\n}\r\n.detail-name {\r\n\tfont-size: 30rpx;\r\n\tfont-weight: 500;\r\n\tcolor: #333;\r\n\tmargin-bottom: 12rpx;\r\n}\r\n.detail-points {\r\n\tfont-size: 28rpx;\r\n\tcolor: #ff6b6b;\r\n\tfont-weight: 500;\r\n}\r\n.detail-grid {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n}\r\n.grid-item {\r\n\twidth: 100%;\r\n\tmargin-bottom: 24rpx;\r\n}\r\n.grid-label {\r\n\tfont-size: 26rpx;\r\n\tcolor: #999;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n.grid-value {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tword-break: break-all;\r\n}\r\n.grid-value.status {\r\n\tdisplay: inline-block;\r\n\tpadding: 4rpx 12rpx;\r\n\tborder-radius: 10rpx;\r\n}\r\n.detail-actions {\r\n\tmargin-top: 40rpx;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n}\r\n.action-btn {\r\n\twidth: 80%;\r\n\theight: 80rpx;\r\n\tline-height: 80rpx;\r\n\tbackground: linear-gradient(135deg, #3a86ff, #1a56cc);\r\n\tcolor: #FFFFFF;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 500;\r\n\tborder-radius: 40rpx;\r\n\tbox-shadow: 0 10rpx 20rpx rgba(58, 134, 255, 0.2);\r\n}\r\n.confirm-btn {\r\n\tbackground: linear-gradient(135deg, #ff6600, #ff8533);\r\n\tbox-shadow: 0 10rpx 20rpx rgba(255, 102, 0, 0.2);\r\n}\r\n.filter-tabs {\r\n\tmargin: calc(var(--status-bar-height, 0) + 88rpx + 20rpx) 30rpx 30rpx;\r\n\tdisplay: flex;\r\n\tbackground-color: #eaf3ff;\r\n\tborder-radius: 16rpx;\r\n\toverflow: hidden;\r\n\tpadding: 6rpx;\r\n}\r\n.tab-item {\r\n\tflex: 1;\r\n\ttext-align: center;\r\n\tpadding: 16rpx 0;\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\ttransition: all 0.3s;\r\n\tborder-radius: 12rpx;\r\n}\r\n.tab-item.active {\r\n\tbackground-color: #e6f0ff;\r\n\tcolor: #1677FF;\r\n\tfont-weight: 600;\r\n\tbox-shadow: 0 2rpx 8rpx rgba(22,119,255,0.08);\r\n\tborder: 1.5px solid #1677FF;\r\n}\r\n.tab-item:not(.active):hover {\r\n\tbackground: #f0f7ff;\r\n}\r\n</style> \r\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/checkin/pages/exchange-history.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onMounted", "uni", "MiniProgramPage"], "mappings": ";;;;;;AAsHA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,aAAaA,cAAAA,IAAI,CAAC;AACxB,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAC9B,UAAM,kBAAkBA,cAAAA,IAAI,KAAK;AACjC,UAAM,iBAAiBA,cAAAA,IAAI,CAAA,CAAE;AAG7B,UAAM,OAAO,CAAC,MAAM,OAAO,OAAO,KAAK;AACvC,UAAM,aAAa;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,WAAW;AAAA,IACZ;AAGA,UAAM,UAAUA,cAAAA,IAAI;AAAA,MACnB;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,QACT,UAAU;AAAA,QACV,MAAM;AAAA,MACN;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS;AAAA,QACT,MAAM;AAAA,MACN;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,QACT,MAAM;AAAA,MACN;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,QACT,MAAM;AAAA,MACN;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,QACT,WAAW;AAAA,QACX,SAAS;AAAA,QACT,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AAGD,UAAM,kBAAkBC,cAAQ,SAAC,MAAM;AACtC,UAAI,WAAW,UAAU,GAAG;AAC3B,eAAO,QAAQ;AAAA,MACjB,WAAY,WAAW,UAAU,GAAG;AAElC,eAAO,QAAQ,MAAM,OAAO,YAAU,OAAO,WAAW,UAAU;AAAA,MACpE,WAAY,WAAW,UAAU,GAAG;AAElC,eAAO,QAAQ,MAAM,OAAO,YAAU,OAAO,WAAW,SAAS;AAAA,MACnE,OAAQ;AAEN,eAAO,QAAQ,MAAM,OAAO,YAAU,OAAO,WAAW,OAAO;AAAA,MAC/D;AAAA,IACF,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AAEf,YAAM,UAAUC,oBAAI;AACpB,sBAAgB,QAAQ,QAAQ,mBAAmB;AAAA,IAIpD,CAAC;AAID,aAAS,UAAU,OAAO;AACzB,iBAAW,QAAQ;AAAA,IACpB;AAGA,aAAS,iBAAiB;AACzB,mBAAa,QAAQ;AAGrB,iBAAW,MAAM;AAEhB,qBAAa,QAAQ;AAErBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACD,GAAE,IAAI;AAAA,IACR;AAYA,aAAS,iBAAiB,QAAQ;AACjC,qBAAe,QAAQ,EAAE,GAAG;AAC5B,sBAAgB,QAAQ;AAAA,IACzB;AAGA,aAAS,mBAAmB;AAC3B,sBAAgB,QAAQ;AAAA,IACzB;AAGA,aAAS,aAAa;AACrB,UAAI,eAAe,MAAM,SAAS,UAAU;AAC3CA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACD,WAAU,eAAe,MAAM,SAAS,OAAO;AAC/CA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACH,OAAQ;AACNA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACD;AAED;IACD;AAGA,aAAS,aAAa;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAE;AAAA,IAGF;AAGA,aAAS,iBAAiB;AACzBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAEhB,kBAAM,QAAQ,QAAQ,MAAM,UAAU,UAAQ,KAAK,OAAO,eAAe,MAAM,EAAE;AACjF,gBAAI,UAAU,IAAI;AACjB,sBAAQ,MAAM,KAAK,EAAE,SAAS;AAC9B,6BAAe,MAAM,SAAS;AAE9BA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,MAAM;AAAA,cACZ,CAAM;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACH,CAAE;AAAA,IACF;AAGA,aAAS,SAAS;AACjBA,oBAAG,MAAC,aAAY;AAAA,IACjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9TA,GAAG,WAAWC,SAAe;"}