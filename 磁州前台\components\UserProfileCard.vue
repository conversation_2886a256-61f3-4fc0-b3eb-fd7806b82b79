<template>
  <view class="user-profile-card">
    <view class="user-profile-content">
      <view class="user-avatar-container">
        <image class="user-avatar" :src="userInfo.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
      </view>
      <view class="user-info">
        <view class="user-name-row">
          <text class="user-name">{{userInfo.nickname || '磁州居民'}}</text>
          <view class="user-vip-tag">VIP{{userInfo.vipLevel || 3}}</view>
        </view>
        <view class="user-id-row">ID: {{userInfo.userId || '88965'}} · {{userInfo.joinDate || '2023年6月'}}入住</view>
        <view class="user-bio">{{userInfo.bio || '热爱生活，热爱磁州'}}</view>
      </view>
    </view>
    <view class="card-actions">
      <button class="action-button edit-profile" @click="handleEditProfile">
        编辑资料
      </button>
      <button class="action-button data-analysis" @click="handleDataAnalysis">
        数据分析
      </button>
    </view>
  </view>
</template>

<script>
export default {
  name: 'UserProfileCard',
  props: {
    userInfo: {
      type: Object,
      default: () => ({
        nickname: '',
        avatar: '',
        userId: '',
        joinDate: '',
        bio: '',
        vipLevel: 0
      })
    }
  },
  methods: {
    handleEditProfile() {
      this.$emit('edit-profile');
    },
    handleDataAnalysis() {
      this.$emit('data-analysis');
    }
  }
}
</script>

<style lang="scss">
.user-profile-card {
  width: 100%;
  background-color: #ffffff;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
  
  .user-profile-content {
    display: flex;
    flex-direction: row;
    align-items: center;
    
    .user-avatar-container {
      position: relative;
      margin-right: 16px;
      
      .user-avatar {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        background-color: #f5f5f5;
        border: 0.5px solid rgba(60, 60, 67, 0.1);
        box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
      }
    }
    
    .user-info {
      flex: 1;
      
      .user-name-row {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 6px;
        
        .user-name {
          font-size: 18px;
          font-weight: 600;
          color: #1d1d1f;
          margin-right: 8px;
          letter-spacing: -0.2px;
        }
        
        .user-vip-tag {
          background-color: #007AFF;
          color: #ffffff;
          font-size: 12px;
          padding: 2px 6px;
          border-radius: 4px;
          font-weight: 500;
          letter-spacing: -0.1px;
        }
      }
      
      .user-id-row {
        font-size: 13px;
        color: #86868b;
        margin-bottom: 6px;
        letter-spacing: -0.1px;
      }
      
      .user-bio {
        font-size: 14px;
        color: #515154;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        letter-spacing: -0.1px;
        line-height: 1.4;
      }
    }
  }
  
  .card-actions {
    display: flex;
    margin-top: 16px;
    justify-content: space-between;
    
    .action-button {
      width: 48%;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 15px;
      border-radius: 8px;
      padding: 0;
      margin: 0;
      line-height: 1;
      font-weight: 500;
      letter-spacing: -0.1px;
      transition: all 0.2s ease;
      
      &.edit-profile {
        background-color: #f5f5f7;
        color: #1d1d1f;
        
        &:active {
          background-color: #e5e5ea;
        }
      }
      
      &.data-analysis {
        background-color: #007AFF;
        color: #ffffff;
        
        &:active {
          background-color: #0071e3;
        }
      }
    }
  }
}
</style> 