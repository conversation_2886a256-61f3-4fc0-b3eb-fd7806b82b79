<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">红包群发</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 红包群发数据概览 -->
    <view class="overview-section">
      <view class="overview-header">
        <text class="section-title">群发数据概览</text>
        <view class="date-picker" @click="showDatePicker">
          <text class="date-text">{{dateRange}}</text>
          <view class="date-icon"></view>
        </view>
      </view>
      
      <view class="stats-cards">
        <view class="stats-card">
          <view class="card-value">{{massData.totalCount}}</view>
          <view class="card-label">群发总次数</view>
          <view class="card-trend" :class="massData.countTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{massData.countGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">{{massData.totalUsers}}</view>
          <view class="card-label">覆盖用户</view>
          <view class="card-trend" :class="massData.usersTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{massData.usersGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">¥{{formatNumber(massData.totalAmount)}}</view>
          <view class="card-label">发放总额</view>
          <view class="card-trend" :class="massData.amountTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{massData.amountGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">{{massData.conversionRate}}%</view>
          <view class="card-label">转化率</view>
          <view class="card-trend" :class="massData.conversionTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{massData.conversionGrowth}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 群发记录 -->
    <view class="mass-history-section">
      <view class="section-header">
        <text class="section-title">群发记录</text>
        <view class="add-btn" @click="createMassSending">
          <text class="btn-text">新建群发</text>
          <view class="plus-icon-small"></view>
        </view>
      </view>
      
      <view class="tab-header">
        <view 
          class="tab-item" 
          v-for="(tab, index) in tabList" 
          :key="index"
          :class="{ active: currentTab === index }"
          @tap="switchTab(index)">
          <text class="tab-text">{{tab}}</text>
        </view>
      </view>
      
      <view class="mass-list">
        <view class="mass-item" v-for="(item, index) in filteredMassList" :key="index" @click="viewMassDetail(item)">
          <view class="mass-header">
            <view class="mass-title">{{item.title}}</view>
            <view class="mass-status" :class="'status-'+item.status">{{item.statusText}}</view>
          </view>
          
          <view class="mass-content">
            <view class="mass-icon" :class="item.type"></view>
            <view class="mass-info">
              <text class="mass-time">发送时间: {{item.sendTime}}</text>
              <view class="mass-amount">
                <text class="amount-label">红包金额: </text>
                <text class="amount-value" v-if="item.type === 'fixed'">¥{{item.amount}}/个</text>
                <text class="amount-value" v-else-if="item.type === 'random'">¥{{item.minAmount}}-{{item.maxAmount}}/个</text>
              </view>
              <view class="mass-target">
                <text class="target-label">发送对象: </text>
                <text class="target-value">{{item.targetText}}</text>
              </view>
            </view>
          </view>
          
          <view class="mass-stats">
            <view class="stat-row">
              <text class="stat-label">发送人数:</text>
              <text class="stat-value">{{item.sentCount}}人</text>
            </view>
            <view class="stat-row">
              <text class="stat-label">领取人数:</text>
              <text class="stat-value">{{item.receivedCount}}人</text>
            </view>
            <view class="stat-row">
              <text class="stat-label">使用人数:</text>
              <text class="stat-value">{{item.usedCount}}人</text>
            </view>
          </view>
          
          <view class="mass-actions">
            <view class="action-btn" @click.stop="viewMassDetail(item)">详情</view>
            <view class="action-btn" @click.stop="repeatMass(item)" v-if="item.status === 'completed'">再次发送</view>
            <view class="action-btn delete" @click.stop="deleteMass(item)" v-if="item.status === 'draft'">删除</view>
          </view>
        </view>
        
        <view class="empty-state" v-if="filteredMassList.length === 0">
          <view class="empty-icon"></view>
          <text class="empty-text">暂无{{tabList[currentTab]}}群发记录</text>
        </view>
      </view>
    </view>
    
    <!-- 群发攻略 -->
    <view class="strategy-section">
      <view class="section-header">
        <text class="section-title">群发攻略</text>
      </view>
      
      <view class="strategy-list">
        <view class="strategy-item" v-for="(strategy, index) in strategies" :key="index" @click="viewStrategy(strategy)">
          <view class="strategy-icon" :style="{ background: strategy.color }">
            <image class="icon-image" :src="strategy.icon" mode="aspectFit"></image>
          </view>
          <view class="strategy-content">
            <text class="strategy-title">{{strategy.title}}</text>
            <text class="strategy-desc">{{strategy.description}}</text>
          </view>
          <view class="strategy-arrow"></view>
        </view>
      </view>
    </view>
    
    <!-- 浮动操作按钮 -->
    <view class="floating-action-button" @click="createMassSending">
      <view class="fab-icon">+</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      dateRange: '2023-04-01 ~ 2023-04-30',
      currentTab: 0,
      tabList: ['全部', '进行中', '已完成', '草稿'],
      
      // 群发数据概览
      massData: {
        totalCount: 42,
        countTrend: 'up',
        countGrowth: '15.3%',
        
        totalUsers: 3568,
        usersTrend: 'up',
        usersGrowth: '23.7%',
        
        totalAmount: 8765.50,
        amountTrend: 'up',
        amountGrowth: '18.2%',
        
        conversionRate: 32.6,
        conversionTrend: 'up',
        conversionGrowth: '5.8%'
      },
      
      // 群发记录列表
      massList: [
        {
          id: 1,
          title: '新用户欢迎红包',
          type: 'fixed',
          status: 'completed',
          statusText: '已完成',
          sendTime: '2023-04-15 14:30',
          amount: 10.00,
          targetText: '新注册用户',
          sentCount: 568,
          receivedCount: 452,
          usedCount: 326
        },
        {
          id: 2,
          title: '五一节日红包',
          type: 'random',
          status: 'processing',
          statusText: '进行中',
          sendTime: '2023-05-01 10:00',
          minAmount: 5.00,
          maxAmount: 50.00,
          targetText: '全部会员',
          sentCount: 1200,
          receivedCount: 876,
          usedCount: 543
        },
        {
          id: 3,
          title: '老用户回馈红包',
          type: 'fixed',
          status: 'draft',
          statusText: '草稿',
          sendTime: '未发送',
          amount: 15.00,
          targetText: '三个月未消费用户',
          sentCount: 0,
          receivedCount: 0,
          usedCount: 0
        }
      ],
      
      // 群发攻略
      strategies: [
        {
          id: 1,
          title: '如何提高红包领取率',
          description: '了解用户行为，精准设置红包金额和发放时间',
          color: '#FF6B6B',
          icon: '/static/images/redpacket/strategy-icon-1.png'
        },
        {
          id: 2,
          title: '会员分层群发策略',
          description: '根据会员等级和消费习惯制定差异化红包策略',
          color: '#4ECDC4',
          icon: '/static/images/redpacket/strategy-icon-2.png'
        },
        {
          id: 3,
          title: '节假日红包营销攻略',
          description: '把握节假日营销节点，提升红包营销效果',
          color: '#FFD166',
          icon: '/static/images/redpacket/strategy-icon-3.png'
        }
      ]
    }
  },
  computed: {
    filteredMassList() {
      if (this.currentTab === 0) {
        return this.massList;
      } else {
        const statusMap = {
          1: 'processing',
          2: 'completed',
          3: 'draft'
        };
        return this.massList.filter(item => item.status === statusMap[this.currentTab]);
      }
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showHelp() {
      uni.showModal({
        title: '红包群发帮助',
        content: '红包群发功能可以帮助您向指定用户群体批量发送红包，提升用户活跃度和转化率。',
        showCancel: false
      });
    },
    showDatePicker() {
      // 显示日期选择器
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    formatNumber(num) {
      return num.toFixed(2);
    },
    switchTab(index) {
      this.currentTab = index;
    },
    createMassSending() {
      uni.showToast({
        title: '创建群发功能开发中',
        icon: 'none'
      });
    },
    viewMassDetail(item) {
      uni.showToast({
        title: '查看详情功能开发中',
        icon: 'none'
      });
    },
    repeatMass(item) {
      uni.showToast({
        title: '再次发送功能开发中',
        icon: 'none'
      });
    },
    deleteMass(item) {
      uni.showModal({
        title: '删除确认',
        content: `确定要删除"${item.title}"吗？`,
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });
          }
        }
      });
    },
    viewStrategy(strategy) {
      uni.showToast({
        title: '攻略详情功能开发中',
        icon: 'none'
      });
    }
  }
}
</script>

<style lang="scss">
.page-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 数据概览样式 */
.overview-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.date-picker {
  display: flex;
  align-items: center;
  background-color: #F5F7FA;
  padding: 5px 10px;
  border-radius: 15px;
}

.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.date-icon {
  width: 12px;
  height: 12px;
  border-left: 1px solid #666;
  border-bottom: 1px solid #666;
  transform: rotate(-45deg);
}

.stats-cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.stats-card {
  width: 48%;
  background: linear-gradient(135deg, #FFF, #F5F7FA);
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 10px;
  position: relative;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.card-value {
  font-size: 22px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.card-label {
  font-size: 12px;
  color: #666;
}

.card-trend {
  position: absolute;
  top: 15px;
  right: 15px;
  display: flex;
  align-items: center;
  font-size: 12px;
}

.card-trend.up {
  color: #FF5858;
}

.card-trend.down {
  color: #2ED573;
}

.trend-arrow {
  width: 8px;
  height: 8px;
  border-left: 1px solid currentColor;
  border-top: 1px solid currentColor;
  margin-right: 2px;
}

.up .trend-arrow {
  transform: rotate(45deg);
}

.down .trend-arrow {
  transform: rotate(-135deg);
}

/* 群发记录样式 */
.mass-history-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.add-btn {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  padding: 6px 12px;
  border-radius: 15px;
  color: #fff;
}

.btn-text {
  font-size: 12px;
  margin-right: 5px;
}

.plus-icon-small {
  width: 12px;
  height: 12px;
  position: relative;
}

.plus-icon-small::before,
.plus-icon-small::after {
  content: '';
  position: absolute;
  background-color: #fff;
}

.plus-icon-small::before {
  width: 12px;
  height: 2px;
  top: 5px;
  left: 0;
}

.plus-icon-small::after {
  width: 2px;
  height: 12px;
  top: 0;
  left: 5px;
}

.tab-header {
  display: flex;
  border-bottom: 1px solid #eee;
  margin-bottom: 15px;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 10px 0;
  position: relative;
}

.tab-text {
  font-size: 14px;
  color: #666;
}

.tab-item.active .tab-text {
  color: #FF5858;
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 25%;
  width: 50%;
  height: 2px;
  background-color: #FF5858;
  border-radius: 1px;
}

.mass-list {
  margin-top: 10px;
}

.mass-item {
  background-color: #fff;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
  border: 1px solid #eee;
}

.mass-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.mass-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.mass-status {
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 10px;
}

.status-completed {
  background-color: #E1F5FE;
  color: #0288D1;
}

.status-processing {
  background-color: #E8F5E9;
  color: #388E3C;
}

.status-draft {
  background-color: #EEEEEE;
  color: #757575;
}

.mass-content {
  display: flex;
  margin-bottom: 10px;
}

.mass-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: #FFE0E0;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mass-icon.fixed::before {
  content: '固';
  color: #FF5858;
  font-weight: bold;
}

.mass-icon.random::before {
  content: '随';
  color: #FF5858;
  font-weight: bold;
}

.mass-info {
  flex: 1;
}

.mass-time {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
  display: block;
}

.mass-amount,
.mass-target {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
  display: flex;
}

.amount-label,
.target-label {
  color: #999;
}

.amount-value,
.target-value {
  color: #333;
}

.mass-stats {
  background-color: #F9F9F9;
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 10px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  margin-bottom: 5px;
}

.stat-row:last-child {
  margin-bottom: 0;
}

.stat-label {
  color: #666;
}

.stat-value {
  color: #333;
  font-weight: 500;
}

.mass-actions {
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #eee;
  padding-top: 10px;
}

.action-btn {
  margin-left: 10px;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 12px;
  background-color: #F5F7FA;
  color: #666;
}

.action-btn.delete {
  background-color: #FEE8E8;
  color: #FF5858;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
}

.empty-icon {
  width: 60px;
  height: 60px;
  background-color: #F5F7FA;
  border-radius: 30px;
  margin-bottom: 10px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 群发攻略样式 */
.strategy-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 80px;
}

.strategy-list {
  margin-top: 10px;
}

.strategy-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #eee;
}

.strategy-item:last-child {
  border-bottom: none;
}

.strategy-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-image {
  width: 24px;
  height: 24px;
}

.strategy-content {
  flex: 1;
}

.strategy-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.strategy-desc {
  font-size: 12px;
  color: #666;
}

.strategy-arrow {
  width: 12px;
  height: 12px;
  border-top: 1px solid #ccc;
  border-right: 1px solid #ccc;
  transform: rotate(45deg);
}

/* 浮动操作按钮 */
.floating-action-button {
  position: fixed;
  bottom: 30px;
  right: 20px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 10px rgba(255, 88, 88, 0.3);
  z-index: 100;
}

.fab-icon {
  font-size: 28px;
  color: #fff;
  font-weight: 300;
}
</style> 