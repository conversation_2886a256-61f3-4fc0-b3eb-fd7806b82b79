"use strict";
const common_vendor = require("../../../../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const userInfo = common_vendor.reactive({
      name: "张店长",
      avatar: ""
    });
    const merchantInfo = common_vendor.reactive({
      location: "磁县",
      level: "钻石商家",
      rating: 4.9,
      followers: 1280
    });
    const currentDate = common_vendor.ref("2023年6月18日");
    common_vendor.ref(true);
    const currentStatsType = common_vendor.ref("today");
    const todayIncome = common_vendor.ref("12,846");
    const todayOrders = common_vendor.ref("128");
    const pendingOrders = common_vendor.ref("3");
    const currentTab = common_vendor.ref(0);
    common_vendor.reactive([
      "基于AI分析的经营建议",
      "潜在问题提醒",
      "机会点识别与推荐"
    ]);
    const quickActions = common_vendor.reactive([
      {
        name: "活动管理",
        icon: "icon-campaign",
        url: "/subPackages/merchant-admin/pages/activity/index"
      },
      {
        name: "发布商品",
        icon: "icon-product",
        url: "/subPackages/merchant-admin/pages/products/create"
      },
      {
        name: "核销中心",
        icon: "icon-verification",
        url: "/subPackages/merchant-admin-marketing/pages/marketing/verification/index"
      },
      {
        name: "数据分析",
        icon: "icon-analysis",
        url: "/subPackages/merchant-admin/pages/analysis/index"
      },
      {
        name: "客户管理",
        icon: "icon-customer",
        url: "/subPackages/merchant-admin-customer/pages/customer/index"
      },
      {
        name: "设置",
        icon: "icon-setting",
        url: "/subPackages/merchant-admin/pages/settings/index"
      }
    ]);
    const statsData = common_vendor.reactive({
      today: [
        {
          title: "今日销售额",
          value: "¥12,846",
          trendValue: "+15.2%",
          trend: "up",
          type: "primary",
          icon: "sales"
        },
        {
          title: "访客数量",
          value: "1,286",
          trendValue: "+8.5%",
          trend: "up",
          type: "secondary",
          icon: "visitor"
        },
        {
          title: "订单数量",
          value: "128",
          trendValue: "+12.3%",
          trend: "up",
          type: "success",
          icon: "order"
        },
        {
          title: "退款金额",
          value: "¥1,234",
          trendValue: "-5.2%",
          trend: "down",
          type: "warning",
          icon: "refund"
        }
      ],
      week: [
        {
          title: "本周销售额",
          value: "¥86,235",
          trendValue: "+10.8%",
          trend: "up",
          type: "primary",
          icon: "sales"
        },
        {
          title: "访客数量",
          value: "8,562",
          trendValue: "+6.2%",
          trend: "up",
          type: "secondary",
          icon: "visitor"
        },
        {
          title: "订单数量",
          value: "862",
          trendValue: "+9.1%",
          trend: "up",
          type: "success",
          icon: "order"
        },
        {
          title: "退款金额",
          value: "¥7,823",
          trendValue: "-2.8%",
          trend: "down",
          type: "warning",
          icon: "refund"
        }
      ],
      month: [
        {
          title: "本月销售额",
          value: "¥356,428",
          trendValue: "+18.5%",
          trend: "up",
          type: "primary",
          icon: "sales"
        },
        {
          title: "访客数量",
          value: "35,621",
          trendValue: "+12.4%",
          trend: "up",
          type: "secondary",
          icon: "visitor"
        },
        {
          title: "订单数量",
          value: "3,562",
          trendValue: "+15.7%",
          trend: "up",
          type: "success",
          icon: "order"
        },
        {
          title: "退款金额",
          value: "¥28,456",
          trendValue: "-8.3%",
          trend: "down",
          type: "warning",
          icon: "refund"
        }
      ]
    });
    const todos = common_vendor.reactive([
      {
        title: "待处理订单提醒",
        time: "今天 14:00",
        completed: false,
        priority: "high",
        type: "order"
      },
      {
        title: "客户消息待回复",
        time: "今天 16:00",
        completed: true,
        priority: "medium",
        type: "message"
      },
      {
        title: '"夏季特惠"活动即将到期',
        time: "明天 10:00",
        completed: false,
        priority: "high",
        type: "activity"
      },
      {
        title: "商品置顶服务即将到期",
        time: "后天 18:00",
        completed: false,
        priority: "medium",
        type: "promotion"
      },
      {
        title: "更新商品库存",
        time: "周五 12:00",
        completed: false,
        priority: "medium",
        type: "inventory"
      },
      {
        title: "推广位即将到期",
        time: "周六 10:00",
        completed: false,
        priority: "high",
        type: "promotion"
      }
    ]);
    common_vendor.reactive([
      {
        title: "618购物节特惠",
        startDate: "2023-06-01",
        endDate: "2023-06-18",
        status: "active",
        views: "2.5k",
        sales: "¥15,628",
        progress: 85
      },
      {
        title: "夏季新品上市",
        startDate: "2023-05-15",
        endDate: "2023-06-15",
        status: "ending",
        views: "1.8k",
        sales: "¥12,386",
        progress: 95
      },
      {
        title: "会员专享折扣",
        startDate: "2023-06-10",
        endDate: "2023-06-30",
        status: "upcoming",
        views: "856",
        sales: "¥6,245",
        progress: 0
      }
    ]);
    const tabItems = common_vendor.reactive([
      {
        id: 0,
        icon: "dashboard",
        text: "商家中心",
        url: "/subPackages/merchant-admin-home/pages/merchant-home/index"
      },
      {
        id: 1,
        icon: "store",
        text: "店铺管理",
        url: "/subPackages/merchant-admin/pages/store/index"
      },
      {
        id: 2,
        icon: "marketing",
        text: "营销中心",
        url: "/subPackages/merchant-admin-marketing/pages/marketing/index"
      },
      {
        id: 3,
        icon: "orders",
        text: "订单管理",
        url: "/subPackages/merchant-admin-order/pages/order/index"
      },
      {
        id: "more",
        icon: "more",
        text: "更多",
        url: ""
      }
    ]);
    const currentStats = common_vendor.computed(() => {
      return statsData[currentStatsType.value] || statsData.today;
    });
    common_vendor.computed(() => {
      return todos.filter((todo) => !todo.completed).length;
    });
    common_vendor.computed(() => {
      const mainTabs = tabItems.slice(0, 4);
      mainTabs.push({
        id: "more",
        icon: "more",
        text: "更多",
        url: ""
      });
      return mainTabs;
    });
    function getCurrentDate() {
      const date = /* @__PURE__ */ new Date();
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      currentDate.value = `${year}年${month}月${day}日`;
    }
    function switchStatsType(type) {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-home/pages/merchant-home/index.vue:556", "正在切换到:", type);
      currentStatsType.value = type;
      let typeText = type === "today" ? "今日" : type === "week" ? "本周" : "本月";
      common_vendor.index.showToast({
        title: "切换到" + typeText,
        icon: "none",
        duration: 1500
      });
    }
    function handleQuickAction(action) {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-home/pages/merchant-home/index.vue:595", "正在跳转到:", action.url);
      try {
        if (!action.url) {
          throw new Error("URL不存在");
        }
        if (action.name === "商家中心") {
          common_vendor.index.navigateTo({
            url: "/subPackages/merchant-admin-home/pages/merchant-home/index",
            fail: (err) => {
              common_vendor.index.__f__("error", "at subPackages/merchant-admin-home/pages/merchant-home/index.vue:608", "导航失败:", err);
              common_vendor.index.showToast({
                title: "页面跳转失败，请稍后再试",
                icon: "none",
                duration: 2e3
              });
            }
          });
          return;
        }
        if (action.name === "核销中心") {
          common_vendor.index.__f__("log", "at subPackages/merchant-admin-home/pages/merchant-home/index.vue:621", "跳转到核销中心:", action.url);
          common_vendor.index.navigateTo({
            url: action.url,
            success: () => {
              common_vendor.index.__f__("log", "at subPackages/merchant-admin-home/pages/merchant-home/index.vue:625", "成功跳转到核销中心");
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at subPackages/merchant-admin-home/pages/merchant-home/index.vue:628", "核销中心跳转失败:", err);
              common_vendor.index.switchTab({
                url: action.url,
                fail: (switchErr) => {
                  common_vendor.index.__f__("error", "at subPackages/merchant-admin-home/pages/merchant-home/index.vue:633", "核销中心switchTab也失败:", switchErr);
                  common_vendor.index.showToast({
                    title: "核销中心页面跳转失败，请稍后再试",
                    icon: "none",
                    duration: 2e3
                  });
                }
              });
            }
          });
          return;
        }
        common_vendor.index.navigateTo({
          url: action.url,
          fail: (err) => {
            common_vendor.index.__f__("error", "at subPackages/merchant-admin-home/pages/merchant-home/index.vue:650", "导航失败:", err);
            common_vendor.index.switchTab({
              url: action.url,
              fail: (switchErr) => {
                common_vendor.index.__f__("error", "at subPackages/merchant-admin-home/pages/merchant-home/index.vue:656", "switchTab也失败:", switchErr);
                common_vendor.index.showToast({
                  title: "页面跳转失败，请稍后再试",
                  icon: "none",
                  duration: 2e3
                });
              }
            });
          }
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/merchant-admin-home/pages/merchant-home/index.vue:669", "处理快捷操作时出错:", error);
        common_vendor.index.showToast({
          title: "功能正在开发中",
          icon: "none",
          duration: 2e3
        });
      }
    }
    function switchTab(tabId) {
      if (tabId === "more") {
        showMoreOptions();
        return;
      }
      if (tabId === currentTab.value)
        return;
      currentTab.value = tabId;
      if (tabId === 0) {
        common_vendor.index.navigateTo({
          url: "/subPackages/merchant-admin-home/pages/merchant-home/index",
          fail: (err) => {
            common_vendor.index.__f__("error", "at subPackages/merchant-admin-home/pages/merchant-home/index.vue:755", "导航到商家中心失败:", err);
            common_vendor.index.showToast({
              title: "页面跳转失败，请稍后再试",
              icon: "none"
            });
          }
        });
        return;
      }
      common_vendor.index.switchTab({
        url: tabItems[tabId].url,
        fail: (err) => {
          common_vendor.index.__f__("error", "at subPackages/merchant-admin-home/pages/merchant-home/index.vue:769", "switchTab失败:", err);
          common_vendor.index.navigateTo({
            url: tabItems[tabId].url,
            fail: (navErr) => {
              common_vendor.index.__f__("error", "at subPackages/merchant-admin-home/pages/merchant-home/index.vue:774", "导航失败:", navErr);
              common_vendor.index.showToast({
                title: "页面跳转失败，请稍后再试",
                icon: "none"
              });
            }
          });
        }
      });
    }
    function showMoreOptions() {
      const moreOptions = tabItems.slice(4).map((item) => item.text);
      common_vendor.index.showActionSheet({
        itemList: moreOptions,
        success: (res) => {
          const tabIndex = 4 + res.tapIndex;
          currentTab.value = tabItems[tabIndex].id;
          common_vendor.index.switchTab({
            url: tabItems[tabIndex].url,
            fail: (err) => {
              common_vendor.index.__f__("error", "at subPackages/merchant-admin-home/pages/merchant-home/index.vue:801", "switchTab失败:", err);
              common_vendor.index.navigateTo({
                url: tabItems[tabIndex].url,
                fail: (navErr) => {
                  common_vendor.index.__f__("error", "at subPackages/merchant-admin-home/pages/merchant-home/index.vue:806", "导航失败:", navErr);
                  common_vendor.index.showToast({
                    title: "页面跳转失败，请稍后再试",
                    icon: "none"
                  });
                }
              });
            }
          });
        }
      });
    }
    function viewStatDetail(stat) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-home/pages/statistics/detail?type=${stat.type}`
      });
    }
    function goBack() {
      common_vendor.index.navigateBack();
    }
    function viewAssistantDetail() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-home/pages/assistant/detail"
      });
    }
    function handleInsight(type) {
      const actions = {
        trend: {
          title: "消费趋势分析",
          url: "/subPackages/merchant-admin-home/pages/analytics/consumer-trends"
        },
        pricing: {
          title: "竞品价格监测",
          url: "/subPackages/merchant-admin-home/pages/analytics/competitor-pricing"
        },
        forecast: {
          title: "销售预测模型",
          url: "/subPackages/merchant-admin-home/pages/analytics/sales-forecast"
        }
      };
      const action = actions[type];
      if (action) {
        common_vendor.index.navigateTo({
          url: action.url,
          success: () => {
            common_vendor.index.showToast({
              title: `正在查看${action.title}`,
              icon: "none"
            });
          }
        });
      }
    }
    const statusBarHeight = common_vendor.ref(20);
    const navbarHeight = common_vendor.ref(64);
    common_vendor.onMounted(() => {
      getCurrentDate();
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight;
      navbarHeight.value = statusBarHeight.value + 44;
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(goBack),
        b: common_vendor.t(userInfo.name),
        c: common_vendor.p({
          d: "M12 17V17.01M12 13.5C12 12.12 13.12 11 14.5 11C15.88 11 17 12.12 17 13.5C17 14.88 15.88 16 14.5 16H14.475C14.4558 15.9997 14.437 15.9948 14.4201 15.9858C14.4032 15.9768 14.3886 15.9639 14.3777 15.9482C14.3667 15.9325 14.3598 15.9144 14.3573 15.8955C14.3549 15.8766 14.357 15.8573 14.364 15.84L15 13.5H14.5C13.12 13.5 12 12.38 12 11C12 9.62 13.12 8.5 14.5 8.5C15.88 8.5 17 9.62 17 11V11.5",
          stroke: "#0A84FF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        d: common_vendor.p({
          d: "M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
          stroke: "#0A84FF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        e: common_vendor.p({
          width: "18",
          height: "18",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        f: common_vendor.t(currentDate.value),
        g: common_vendor.p({
          d: "M12 15C15.866 15 19 11.866 19 8C19 4.13401 15.866 1 12 1C8.13401 1 5 4.13401 5 8C5 11.866 8.13401 15 12 15Z",
          fill: "white",
          ["fill-opacity"]: "0.2"
        }),
        h: common_vendor.p({
          d: "M12 15C15.866 15 19 11.866 19 8C19 4.13401 15.866 1 12 1C8.13401 1 5 4.13401 5 8C5 11.866 8.13401 15 12 15Z",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        i: common_vendor.p({
          d: "M8.21 13.89L7 23L12 20L17 23L15.79 13.88",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        j: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        k: common_vendor.t(merchantInfo.level),
        l: common_vendor.t(todayIncome.value),
        m: common_vendor.t(todayOrders.value),
        n: common_vendor.t(pendingOrders.value),
        o: common_vendor.p({
          d: "M19 11H5C3.89543 11 3 11.8954 3 13V20C3 21.1046 3.89543 22 5 22H19C20.1046 22 21 21.1046 21 20V13C21 11.8954 20.1046 11 19 11Z",
          stroke: "#0A84FF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        p: common_vendor.p({
          d: "M7 11V7C7 5.93913 7.42143 4.92172 8.17157 4.17157C8.92172 3.42143 9.93913 3 11 3H13C14.0609 3 15.0783 3.42143 15.8284 4.17157C16.5786 4.92172 17 5.93913 17 7V11",
          stroke: "#0A84FF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        q: common_vendor.p({
          width: "20",
          height: "20",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        r: common_vendor.p({
          d: "M9 18L15 12L9 6",
          stroke: "#0A84FF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        s: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        t: common_vendor.f(quickActions, (action, index, i0) => {
          return common_vendor.e({
            a: action.icon === "icon-product"
          }, action.icon === "icon-product" ? {
            b: "8ed27b9c-13-" + i0 + "," + ("8ed27b9c-12-" + i0),
            c: common_vendor.p({
              d: "M20 7L12 3L4 7M20 7V17L12 21M20 7L12 11M12 21L4 17V7M12 21V11M4 7L12 11",
              stroke: "white",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            d: "8ed27b9c-12-" + i0,
            e: common_vendor.p({
              width: "24",
              height: "24",
              viewBox: "0 0 24 24",
              fill: "none",
              xmlns: "http://www.w3.org/2000/svg"
            })
          } : action.icon === "icon-verification" ? {
            g: "8ed27b9c-15-" + i0 + "," + ("8ed27b9c-14-" + i0),
            h: common_vendor.p({
              d: "M9 11L12 14L22 4M21 12V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H16",
              stroke: "white",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            i: "8ed27b9c-14-" + i0,
            j: common_vendor.p({
              width: "24",
              height: "24",
              viewBox: "0 0 24 24",
              fill: "none",
              xmlns: "http://www.w3.org/2000/svg"
            })
          } : action.icon === "icon-order" ? {
            l: "8ed27b9c-17-" + i0 + "," + ("8ed27b9c-16-" + i0),
            m: common_vendor.p({
              d: "M9 17H15M9 13H15M9 9H15M5 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3H5C3.89543 3 3 3.89543 3 5V19C3 20.1046 3.89543 21 5 21Z",
              stroke: "white",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            n: "8ed27b9c-16-" + i0,
            o: common_vendor.p({
              width: "24",
              height: "24",
              viewBox: "0 0 24 24",
              fill: "none",
              xmlns: "http://www.w3.org/2000/svg"
            })
          } : action.icon === "icon-analysis" ? {
            q: "8ed27b9c-19-" + i0 + "," + ("8ed27b9c-18-" + i0),
            r: common_vendor.p({
              d: "M16 8V16M12 11V16M8 14V16M6 20H18C19.1046 20 20 19.1046 20 18V6C20 4.89543 19.1046 4 18 4H6C4.89543 4 4 4.89543 4 6V18C4 19.1046 4.89543 20 6 20Z",
              stroke: "white",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            s: "8ed27b9c-18-" + i0,
            t: common_vendor.p({
              width: "24",
              height: "24",
              viewBox: "0 0 24 24",
              fill: "none",
              xmlns: "http://www.w3.org/2000/svg"
            })
          } : action.icon === "icon-customer" ? {
            w: "8ed27b9c-21-" + i0 + "," + ("8ed27b9c-20-" + i0),
            x: common_vendor.p({
              d: "M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21M23 21V19C22.9993 18.1137 22.7044 17.2528 22.1614 16.5523C21.6184 15.8519 20.8581 15.3516 20 15.13M16 3.13C16.8604 3.35031 17.623 3.85071 18.1676 4.55232C18.7122 5.25392 19.0078 6.11683 19.0078 7.005C19.0078 7.89318 18.7122 8.75608 18.1676 9.45769C17.623 10.1593 16.8604 10.6597 16 10.88M13 7C13 9.20914 11.2091 11 9 11C6.79086 11 5 9.20914 5 7C5 4.79086 6.79086 3 9 3C11.2091 3 13 4.79086 13 7Z",
              stroke: "white",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            y: "8ed27b9c-20-" + i0,
            z: common_vendor.p({
              width: "24",
              height: "24",
              viewBox: "0 0 24 24",
              fill: "none",
              xmlns: "http://www.w3.org/2000/svg"
            })
          } : action.icon === "icon-campaign" ? {
            B: "8ed27b9c-23-" + i0 + "," + ("8ed27b9c-22-" + i0),
            C: common_vendor.p({
              d: "M11 17.25C11 17.25 16 14 16 9.75C16 5.5 11 5.5 11 9.75M11 17.25C11 17.25 6 14 6 9.75C6 5.5 11 5.5 11 9.75M11 17.25V21M11 9.75V3",
              stroke: "white",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            D: "8ed27b9c-22-" + i0,
            E: common_vendor.p({
              width: "24",
              height: "24",
              viewBox: "0 0 24 24",
              fill: "none",
              xmlns: "http://www.w3.org/2000/svg"
            })
          } : action.icon === "icon-setting" ? {
            G: "8ed27b9c-25-" + i0 + "," + ("8ed27b9c-24-" + i0),
            H: common_vendor.p({
              d: "M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z",
              stroke: "white",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            I: "8ed27b9c-26-" + i0 + "," + ("8ed27b9c-24-" + i0),
            J: common_vendor.p({
              d: "M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.258 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.0113 9.77251C4.28059 9.5799 4.48572 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z",
              stroke: "white",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            K: "8ed27b9c-24-" + i0,
            L: common_vendor.p({
              width: "24",
              height: "24",
              viewBox: "0 0 24 24",
              fill: "none",
              xmlns: "http://www.w3.org/2000/svg"
            })
          } : {}, {
            f: action.icon === "icon-verification",
            k: action.icon === "icon-order",
            p: action.icon === "icon-analysis",
            v: action.icon === "icon-customer",
            A: action.icon === "icon-campaign",
            F: action.icon === "icon-setting",
            M: common_vendor.n(action.icon),
            N: common_vendor.t(action.name),
            O: index,
            P: common_vendor.o(($event) => handleQuickAction(action), index)
          });
        }),
        v: common_vendor.p({
          d: "M16 6L18.29 8.29L13.41 13.17L9.41 9.17L2 16.59L3.41 18L9.41 12L13.41 16L19.71 9.71L22 12V6H16Z",
          stroke: "#0A84FF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        w: common_vendor.p({
          width: "20",
          height: "20",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        x: currentStatsType.value === "today" ? 1 : "",
        y: common_vendor.o(($event) => switchStatsType("today")),
        z: currentStatsType.value === "week" ? 1 : "",
        A: common_vendor.o(($event) => switchStatsType("week")),
        B: currentStatsType.value === "month" ? 1 : "",
        C: common_vendor.o(($event) => switchStatsType("month")),
        D: common_vendor.f(currentStats.value, (stat, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(stat.title),
            b: common_vendor.t(stat.value),
            c: stat.trend === "up"
          }, stat.trend === "up" ? {
            d: "8ed27b9c-30-" + i0 + "," + ("8ed27b9c-29-" + i0),
            e: common_vendor.p({
              d: "M12 20L12 4M12 4L18 10M12 4L6 10",
              stroke: "currentColor",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            f: "8ed27b9c-29-" + i0,
            g: common_vendor.p({
              width: "16",
              height: "16",
              viewBox: "0 0 24 24",
              fill: "none",
              xmlns: "http://www.w3.org/2000/svg"
            })
          } : {
            h: "8ed27b9c-32-" + i0 + "," + ("8ed27b9c-31-" + i0),
            i: common_vendor.p({
              d: "M12 4L12 20M12 20L6 14M12 20L18 14",
              stroke: "currentColor",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            j: "8ed27b9c-31-" + i0,
            k: common_vendor.p({
              width: "16",
              height: "16",
              viewBox: "0 0 24 24",
              fill: "none",
              xmlns: "http://www.w3.org/2000/svg"
            })
          }, {
            l: common_vendor.t(stat.trendValue),
            m: common_vendor.n(stat.trend),
            n: index,
            o: common_vendor.o(($event) => viewStatDetail(stat), index)
          });
        }),
        E: common_vendor.p({
          d: "M12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3Z",
          stroke: "#0A84FF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        F: common_vendor.p({
          d: "M9 12C9 12.5523 9.44772 13 10 13C10.5523 13 11 12.5523 11 12C11 11.4477 10.5523 11 10 11C9.44772 11 9 11.4477 9 12Z",
          fill: "#0A84FF"
        }),
        G: common_vendor.p({
          d: "M13 12C13 12.5523 13.4477 13 14 13C14.5523 13 15 12.5523 15 12C15 11.4477 14.5523 11 14 11C13.4477 11 13 11.4477 13 12Z",
          fill: "#0A84FF"
        }),
        H: common_vendor.p({
          d: "M12 18C14.5 18 16.5 16.5 17 15H7C7.5 16.5 9.5 18 12 18Z",
          stroke: "#0A84FF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        I: common_vendor.p({
          width: "20",
          height: "20",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        J: common_vendor.p({
          d: "M9 18L15 12L9 6",
          stroke: "#0A84FF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        K: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        L: common_vendor.o(viewAssistantDetail),
        M: common_vendor.p({
          d: "M16 6L18.29 8.29L13.41 13.17L9.41 9.17L2 16.59L3.41 18L9.41 12L13.41 16L19.71 9.71L22 12V6H16Z",
          stroke: "#0A84FF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        N: common_vendor.p({
          width: "28",
          height: "28",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        O: common_vendor.p({
          d: "M9 18L15 12L9 6",
          stroke: "#8E8E93",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        P: common_vendor.p({
          width: "20",
          height: "20",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        Q: common_vendor.o(($event) => handleInsight("trend")),
        R: common_vendor.p({
          d: "M12 8V12M12 16H12.01M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z",
          stroke: "#FF9F0A",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        S: common_vendor.p({
          width: "28",
          height: "28",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        T: common_vendor.p({
          d: "M9 18L15 12L9 6",
          stroke: "#8E8E93",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        U: common_vendor.p({
          width: "20",
          height: "20",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        V: common_vendor.o(($event) => handleInsight("pricing")),
        W: common_vendor.p({
          d: "M12 8V16M12 16L8 12M12 16L16 12M3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12Z",
          stroke: "#30D158",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        X: common_vendor.p({
          width: "28",
          height: "28",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        Y: common_vendor.p({
          d: "M9 18L15 12L9 6",
          stroke: "#8E8E93",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        Z: common_vendor.p({
          width: "20",
          height: "20",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        aa: common_vendor.o(($event) => handleInsight("forecast")),
        ab: common_vendor.f(tabItems, (tab, index, i0) => {
          return common_vendor.e({
            a: currentTab.value === tab.id
          }, currentTab.value === tab.id ? {} : {}, {
            b: common_vendor.n(tab.icon),
            c: common_vendor.t(tab.text),
            d: index,
            e: currentTab.value === tab.id ? 1 : "",
            f: common_vendor.o(($event) => switchTab(tab.id), index)
          });
        })
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-8ed27b9c"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-home/pages/merchant-home/index.js.map
