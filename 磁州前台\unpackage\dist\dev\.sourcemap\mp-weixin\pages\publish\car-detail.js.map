{"version": 3, "file": "car-detail.js", "sources": ["pages/publish/car-detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHVibGlzaC9jYXItZGV0YWlsLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"car-detail-container\">\n    <!-- 添加自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"navbar-left\" @click=\"goBack\">\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\n      </view>\n      <view class=\"navbar-title\">二手车辆详情</view>\n      <view class=\"navbar-right\">\n        <!-- 占位 -->\n      </view>\n    </view>\n    \n    <!-- 隐藏的分享按钮，用于自动触发 -->\n    <button id=\"shareButton\" class=\"hidden-share-btn\" open-type=\"share\"></button>\n    \n    <view class=\"car-detail-wrapper\">\n      <!-- 车辆基本信息卡片 -->\n      <view class=\"content-card car-info-card\">\n        <view class=\"car-header\">\n          <view class=\"car-title-row\">\n            <text class=\"car-title\">{{carData.title}}</text>\n            <text class=\"car-price\">{{carData.price}}</text>\n          </view>\n          <view class=\"car-meta\">\n            <view class=\"car-tag-group\">\n              <view class=\"car-tag\" v-for=\"(tag, index) in carData.tags\" :key=\"index\">{{tag}}</view>\n            </view>\n            <text class=\"car-publish-time\">发布于 {{formatTime(carData.publishTime)}}</text>\n          </view>\n        </view>\n        \n        <!-- 车辆图片轮播 -->\n        <swiper class=\"car-swiper\" :indicator-dots=\"true\" :autoplay=\"true\" :interval=\"3000\" :duration=\"500\">\n          <swiper-item v-for=\"(image, index) in carData.images\" :key=\"index\">\n            <image :src=\"image\" mode=\"aspectFill\" class=\"car-image\"></image>\n          </swiper-item>\n        </swiper>\n        \n        <!-- 基本信息 -->\n        <view class=\"car-basic-info\">\n          <view class=\"info-item\">\n            <text class=\"info-label\">品牌型号</text>\n            <text class=\"info-value\">{{carData.brand}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">上牌时间</text>\n            <text class=\"info-value\">{{carData.registerTime}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">行驶里程</text>\n            <text class=\"info-value\">{{carData.mileage}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">排放标准</text>\n            <text class=\"info-value\">{{carData.emission}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 车辆配置 -->\n      <view class=\"content-card car-config-card\">\n        <view class=\"section-title\">车辆配置</view>\n        <view class=\"config-list\">\n          <view class=\"config-item\" v-for=\"(item, index) in carData.configs\" :key=\"index\">\n            <text class=\"config-icon iconfont\" :class=\"item.icon\"></text>\n            <text class=\"config-text\">{{item.name}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 车况信息 -->\n      <view class=\"content-card car-condition-card\">\n        <view class=\"section-title\">车况信息</view>\n        <view class=\"condition-list\">\n          <view class=\"condition-item\" v-for=\"(item, index) in carData.conditions\" :key=\"index\">\n            <text class=\"condition-label\">{{item.label}}</text>\n            <text class=\"condition-value\">{{item.value}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 车辆描述 -->\n      <view class=\"content-card description-card\">\n        <view class=\"section-title\">车辆描述</view>\n        <view class=\"description-content\">\n          <text class=\"description-text\">{{carData.description}}</text>\n        </view>\n      </view>\n      \n      <!-- 车主信息 -->\n      <view class=\"content-card owner-card\">\n        <view class=\"owner-header\">\n          <view class=\"owner-avatar\">\n            <image :src=\"carData.owner.avatar\" mode=\"aspectFill\"></image>\n          </view>\n          <view class=\"owner-info\">\n            <text class=\"owner-name\">{{carData.owner.name}}</text>\n            <view class=\"owner-meta\">\n              <text class=\"owner-type\">{{carData.owner.type}}</text>\n              <text class=\"owner-rating\">信用等级 {{carData.owner.rating}}</text>\n            </view>\n          </view>\n          <view class=\"owner-auth\" v-if=\"carData.owner.isVerified\">\n            <text class=\"iconfont icon-verified\"></text>\n            <text class=\"auth-text\">已认证</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 联系方式 -->\n      <view class=\"content-card contact-card\">\n        <view class=\"contact-header\">\n          <text class=\"card-title\">联系方式</text>\n        </view>\n        <view class=\"contact-content\">\n          <view class=\"contact-item\">\n            <text class=\"contact-label\">联系人</text>\n            <text class=\"contact-value\">{{carData.contact.name}}</text>\n          </view>\n          <view class=\"contact-item\">\n            <text class=\"contact-label\">电话</text>\n            <text class=\"contact-value contact-phone\" @click=\"callPhone\">{{carData.contact.phone}}</text>\n          </view>\n          <view class=\"contact-tips\">\n            <text class=\"tips-icon iconfont icon-info\"></text>\n            <text class=\"tips-text\">请说明在\"磁州生活网\"看到的信息</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 举报卡片 -->\n      <report-card></report-card>\n      \n      <!-- 相似车辆推荐 -->\n      <view class=\"content-card related-cars-card\">\n        <view class=\"section-title\">相关车辆推荐</view>\n        <view class=\"related-cars-content\">\n          <!-- 简洁的车辆列表 -->\n          <view class=\"related-cars-list\">\n            <view class=\"related-car-item\" \n                 v-for=\"(car, index) in relatedCars.slice(0, 3)\" \n                 :key=\"index\" \n                 @click=\"navigateToCarDetail(car.id)\">\n              <view class=\"car-item-content\">\n                <view class=\"car-item-left\">\n                  <image class=\"car-image\" :src=\"car.image\" mode=\"aspectFill\"></image>\n                </view>\n                <view class=\"car-item-middle\">\n                  <text class=\"car-item-title\">{{car.title}}</text>\n                  <view class=\"car-item-brand\">{{car.brand}} · {{car.mileage}}</view>\n                  <view class=\"car-item-tags\">\n                    <text class=\"car-item-tag\" v-for=\"(tag, tagIndex) in car.tags.slice(0, 2)\" :key=\"tagIndex\">{{tag}}</text>\n                    <text class=\"car-item-tag-more\" v-if=\"car.tags && car.tags.length > 2\">+{{car.tags.length - 2}}</text>\n                  </view>\n                </view>\n                <view class=\"car-item-right\">\n                  <text class=\"car-item-price\">{{car.price}}</text>\n                </view>\n              </view>\n            </view>\n            \n            <!-- 暂无数据提示 -->\n            <view class=\"empty-related-cars\" v-if=\"relatedCars.length === 0\">\n              <image src=\"/static/images/empty.png\" class=\"empty-image\" mode=\"aspectFit\"></image>\n              <text class=\"empty-text\">暂无相关车辆</text>\n            </view>\n          </view>\n          \n          <!-- 查看更多按钮 -->\n          <view class=\"view-more-btn\" v-if=\"relatedCars.length > 0\" @click.stop=\"navigateToCarList\">\n            <text class=\"view-more-text\">查看更多车辆信息</text>\n            <text class=\"view-more-icon iconfont icon-right\"></text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"interaction-toolbar\">\n      <view class=\"toolbar-item\" @click=\"goToHome\">\n        <image src=\"/static/images/tabbar/a首页.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">首页</text>\n      </view>\n      <view class=\"toolbar-item\" @click=\"toggleCollect\">\n        <image src=\"/static/images/tabbar/a收藏.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">收藏</text>\n      </view>\n      <button class=\"share-button toolbar-item\" open-type=\"share\">\n        <image src=\"/static/images/tabbar/a分享.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">分享</text>\n      </button>\n      <view class=\"toolbar-item\" @click=\"openChat\">\n        <image src=\"/static/images/tabbar/a消息.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">私信</text>\n      </view>\n      <view class=\"toolbar-item call-button\" @click=\"callPhone\">\n        <view class=\"call-button-content\">\n          <text class=\"call-text\">打电话</text>\n          <text class=\"call-subtitle\">请说在磁州生活网看到的</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport ReportCard from '@/components/ReportCard.vue'\n\n// 状态栏高度\nconst statusBarHeight = ref(20);\n\n// 获取状态栏高度\nonMounted(() => {\n  try {\n    const sysInfo = uni.getSystemInfoSync();\n    statusBarHeight.value = sysInfo.statusBarHeight || 20;\n  } catch (e) {\n    console.error('获取状态栏高度失败', e);\n  }\n  \n  // 获取路由参数\n  const pages = getCurrentPages();\n  const currentPage = pages[pages.length - 1];\n  const options = currentPage.options || {};\n  \n  // 获取车辆ID\n  const id = options.id || '';\n  console.log('车辆详情页ID:', id);\n  \n  // 加载相关车辆推荐\n  loadRelatedCars();\n});\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack({\n    fail: () => {\n      uni.switchTab({\n        url: '/pages/index/index'\n      });\n    }\n  });\n};\n\n// 格式化时间\nconst formatTime = (timestamp) => {\n  const date = new Date(timestamp);\n  return `${date.getMonth() + 1}月${date.getDate()}日`;\n};\n\n// 响应式数据\nconst isCollected = ref(false);\nconst carData = ref({\n  id: 'car12345',\n  title: '2019款大众帕萨特',\n  price: '15.8万',\n  tags: ['一手车', '车况良好', '可分期'],\n  publishTime: Date.now() - 86400000 * 2, // 2天前\n  images: [\n    '/static/images/car1.jpg',\n    '/static/images/car2.jpg',\n    '/static/images/car3.jpg'\n  ],\n  brand: '大众帕萨特 2019款 330TSI 豪华版',\n  registerTime: '2019年6月',\n  mileage: '3.5万公里',\n  emission: '国六',\n  configs: [\n    { name: '自动挡', icon: 'icon-gear' },\n    { name: '真皮座椅', icon: 'icon-seat' },\n    { name: '倒车影像', icon: 'icon-camera' },\n    { name: '定速巡航', icon: 'icon-cruise' },\n    { name: '天窗', icon: 'icon-sunroof' },\n    { name: '导航', icon: 'icon-navigation' }\n  ],\n  conditions: [\n    { label: '车况', value: '良好' },\n    { label: '过户次数', value: '0次' },\n    { label: '保养记录', value: '全程4S店' },\n    { label: '事故情况', value: '无事故' }\n  ],\n  description: '2019年6月上牌，一手车，全程4S店保养，车况良好，无事故，无泡水，无火烧。配置齐全，真皮座椅，倒车影像，定速巡航，天窗，导航等。价格可谈，支持分期付款。',\n  owner: {\n    name: '张先生',\n    avatar: '/static/images/avatar.png',\n    type: '个人',\n    rating: 'A+',\n    isVerified: true\n  },\n  contact: {\n    name: '张先生',\n    phone: '13912345678'\n  }\n});\n\nconst similarCars = ref([\n  {\n    id: 'car001',\n    title: '2018款大众帕萨特',\n    price: '14.5万',\n    brand: '大众帕萨特',\n    mileage: '4.2万公里',\n    image: '/static/images/car-similar1.jpg'\n  },\n  {\n    id: 'car002',\n    title: '2020款大众帕萨特',\n    price: '16.8万',\n    brand: '大众帕萨特',\n    mileage: '2.8万公里',\n    image: '/static/images/car-similar2.jpg'\n  }\n]);\n\n// 相关车辆推荐数据\nconst relatedCars = ref([]);\n\n// 加载相关车辆推荐\nconst loadRelatedCars = () => {\n  // 这里应该调用API获取数据\n  // 实际项目中应该根据当前车辆的品牌、价格区间等进行相关性匹配\n  \n  // 模拟数据，使用现有的similarCars数据，并增加一些额外数据\n  setTimeout(() => {\n    relatedCars.value = [\n      ...similarCars.value.map(car => ({\n        ...car,\n        tags: ['车况好', '无事故']\n      })),\n      {\n        id: 'car003',\n        title: '2019款本田雅阁',\n        price: '16.2万',\n        brand: '本田雅阁',\n        mileage: '3.6万公里',\n        image: '/static/images/car-similar3.jpg',\n        tags: ['一手车', '全程4S保养', '无事故']\n      }\n    ];\n  }, 500);\n};\n\n// 跳转到车辆详情页\nconst navigateToCarDetail = (carId) => {\n  // 防止跳转到当前页面\n  if (carId === carData.value.id) {\n    return;\n  }\n  \n  uni.navigateTo({\n    url: `/pages/publish/car-detail?id=${carId}`\n  });\n};\n\n// 跳转到二手车列表页\nconst navigateToCarList = (e) => {\n  if (e) e.stopPropagation();\n  const carCategory = carData.value.tags?.[0] || '';\n  uni.navigateTo({ \n    url: `/subPackages/service/pages/filter?type=second_car&title=${encodeURIComponent('二手车辆')}&category=${encodeURIComponent(carCategory)}&active=second_car` \n  });\n};\n\n// 方法\nconst toggleCollect = () => {\n  isCollected.value = !isCollected.value;\n  if (isCollected.value) {\n    uni.showToast({\n      title: '收藏成功',\n      icon: 'success'\n    });\n  }\n};\n\nconst showShareOptions = () => {\n  uni.showShareMenu({\n    withShareTicket: true,\n    menus: ['shareAppMessage', 'shareTimeline']\n  });\n};\n\nconst callPhone = () => {\n  uni.makePhoneCall({\n    phoneNumber: carData.value.contact.phone,\n    fail: () => {\n      uni.showToast({\n        title: '拨打电话失败',\n        icon: 'none'\n      });\n    }\n  });\n};\n\n// 跳转到首页\nconst goToHome = () => {\n  uni.switchTab({\n    url: '/pages/index/index'\n  });\n};\n\n// 打开私信聊天\nconst openChat = () => {\n  if (!carData.value.owner || !carData.value.owner.id) {\n    uni.showToast({\n      title: '无法获取发布者信息',\n      icon: 'none'\n    });\n    return;\n  }\n  \n  // 跳转到聊天页面\n  uni.navigateTo({\n    url: `/pages/chat/index?userId=${carData.value.owner.id}&username=${encodeURIComponent(carData.value.owner.name || '用户')}`\n  });\n};\n</script>\n\n<style>\n.car-detail-container {\n  min-height: 100vh;\n  background-color: #f5f7fa;\n  padding-bottom: 150rpx;\n  padding-top: 0; /* 移除顶部内边距，由导航栏控制 */\n}\n\n.car-detail-wrapper {\n  padding: 24rpx;\n  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */\n}\n\n.content-card {\n  background-color: #fff;\n  border-radius: 16rpx;\n  margin-bottom: 24rpx;\n  padding: 30rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n}\n\n/* 车辆基本信息卡片 */\n.car-title-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.car-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.car-price {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #ff4d4f;\n}\n\n.car-meta {\n  margin-bottom: 24rpx;\n}\n\n.car-tag-group {\n  display: flex;\n  flex-wrap: wrap;\n  margin-bottom: 12rpx;\n}\n\n.car-tag {\n  font-size: 24rpx;\n  color: #1890ff;\n  background-color: rgba(24, 144, 255, 0.1);\n  padding: 4rpx 16rpx;\n  border-radius: 6rpx;\n  margin-right: 16rpx;\n  margin-bottom: 12rpx;\n}\n\n.car-publish-time {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 轮播图 */\n.car-swiper {\n  height: 400rpx;\n  border-radius: 12rpx;\n  overflow: hidden;\n  margin-bottom: 24rpx;\n}\n\n.car-image {\n  width: 100%;\n  height: 100%;\n}\n\n/* 基本信息 */\n.car-basic-info {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 24rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n}\n\n.info-item {\n  width: 50%;\n  padding: 12rpx 24rpx;\n  box-sizing: border-box;\n}\n\n.info-label {\n  font-size: 26rpx;\n  color: #999;\n  margin-bottom: 8rpx;\n  display: block;\n}\n\n.info-value {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n/* 车辆配置 */\n.config-list {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -12rpx;\n}\n\n.config-item {\n  width: 33.33%;\n  padding: 12rpx;\n  box-sizing: border-box;\n  display: flex;\n  align-items: center;\n}\n\n.config-icon {\n  font-size: 32rpx;\n  color: #1890ff;\n  margin-right: 8rpx;\n}\n\n.config-text {\n  font-size: 26rpx;\n  color: #666;\n}\n\n/* 车况信息 */\n.condition-list {\n  display: flex;\n  flex-direction: column;\n}\n\n.condition-item {\n  display: flex;\n  padding: 16rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.condition-item:last-child {\n  border-bottom: none;\n}\n\n.condition-label {\n  width: 160rpx;\n  font-size: 28rpx;\n  color: #999;\n}\n\n.condition-value {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333;\n}\n\n/* 车辆描述 */\n.description-content {\n  padding: 24rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n}\n\n.description-text {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.6;\n}\n\n/* 车主信息 */\n.owner-header {\n  display: flex;\n  align-items: center;\n}\n\n.owner-avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  overflow: hidden;\n  margin-right: 20rpx;\n}\n\n.owner-avatar image {\n  width: 100%;\n  height: 100%;\n}\n\n.owner-info {\n  flex: 1;\n}\n\n.owner-name {\n  font-size: 30rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.owner-meta {\n  display: flex;\n  align-items: center;\n}\n\n.owner-type, .owner-rating {\n  font-size: 24rpx;\n  color: #666;\n  margin-right: 16rpx;\n}\n\n/* 相似车辆推荐样式 */\n.related-cars-card {\n  margin-top: 12px;\n  background-color: #fff;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\n}\n\n.related-cars-content {\n  padding: 0 16px 16px;\n  overflow: hidden;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 16px;\n  position: relative;\n  padding-left: 10px;\n}\n\n.section-title::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 4px;\n  width: 3px;\n  height: 16px;\n  background-color: #0052CC;\n  border-radius: 3px;\n}\n\n/* 相关车辆列表样式 */\n.related-cars-list {\n  margin-bottom: 12px;\n}\n\n.related-car-item {\n  padding: 12px 0;\n  border-bottom: 1px solid #f5f5f5;\n}\n\n.related-car-item:last-child {\n  border-bottom: none;\n}\n\n.car-item-content {\n  display: flex;\n  align-items: center;\n}\n\n.car-item-left {\n  margin-right: 12px;\n}\n\n.car-image {\n  width: 80px;\n  height: 60px;\n  border-radius: 8px;\n  background-color: #f5f7fa;\n  object-fit: cover;\n}\n\n.car-item-middle {\n  flex: 1;\n}\n\n.car-item-title {\n  font-size: 15px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 4px;\n}\n\n.car-item-brand {\n  font-size: 13px;\n  color: #666;\n  margin-bottom: 4px;\n}\n\n.car-item-tags {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.car-item-tag {\n  font-size: 12px;\n  color: #666;\n  background-color: #f5f7fa;\n  padding: 2px 6px;\n  border-radius: 4px;\n  margin-right: 6px;\n  margin-bottom: 4px;\n}\n\n.car-item-tag-more {\n  font-size: 12px;\n  color: #999;\n  padding: 2px 0;\n}\n\n.car-item-right {\n  text-align: right;\n}\n\n.car-item-price {\n  font-size: 15px;\n  color: #ff4d4f;\n  font-weight: 500;\n}\n\n/* 查看更多按钮 */\n.view-more-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 44px;\n  background-color: #f7f9fc;\n  border-radius: 8px;\n  margin-top: 8px;\n}\n\n.view-more-text {\n  font-size: 14px;\n  color: #0052CC;\n}\n\n.view-more-icon {\n  margin-left: 4px;\n  font-size: 12px;\n  color: #0052CC;\n}\n\n/* 空数据提示 */\n.empty-related-cars {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 24px 0;\n}\n\n.empty-image {\n  width: 80px;\n  height: 80px;\n  margin-bottom: 12px;\n}\n\n.empty-text {\n  font-size: 14px;\n  color: #999;\n}\n\n/* 底部互动工具栏 */\n.interaction-toolbar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background-color: #fff;\n  padding: 10rpx 10rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-top: 1rpx solid #f0f0f0;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  height: 120rpx;\n  z-index: 100;\n}\n\n.toolbar-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 6rpx 0;\n  margin: 0 4rpx;\n}\n\n.toolbar-icon {\n  width: 44rpx;\n  height: 44rpx;\n  margin-bottom: 6rpx;\n}\n\n.toolbar-text {\n  font-size: 22rpx;\n  color: #666;\n}\n\n.share-button {\n  background: transparent;\n  border: none;\n  margin: 0;\n  padding: 0;\n  line-height: normal;\n  border-radius: 0;\n  flex: 1;\n}\n\n.share-button::after {\n  display: none;\n}\n\n.call-button {\n  flex: 3; /* 占据更多空间，原来是2，现在改为3让按钮更长 */\n  background: linear-gradient(135deg, #0052CC, #0066FF);\n  height: 90rpx;\n  margin: 0 0 0 10rpx;\n  border-radius: 45rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n}\n\n.call-button-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n}\n\n.call-text {\n  color: #fff;\n  font-size: 30rpx;\n  font-weight: bold;\n  line-height: 1.2;\n}\n\n.call-subtitle {\n  color: rgba(255, 255, 255, 0.9);\n  font-size: 20rpx;\n  line-height: 1.2;\n}\n\n/* 隐藏原来的底部操作栏 */\n.action-bar {\n  display: none;\n}\n\n/* 隐藏的分享按钮 */\n.hidden-share-btn {\n  position: absolute;\n  top: -9999rpx;\n  left: -9999rpx;\n  width: 0;\n  height: 0;\n  padding: 0;\n  margin: 0;\n  opacity: 0;\n}\n\n/* 自定义导航栏样式 */\n.custom-navbar {\n  background: linear-gradient(135deg, #0066FF, #0052CC);\n  height: 88rpx;\n  padding-top: 44px; /* 状态栏高度 */\n  display: flex;\n  align-items: center;\n  position: fixed; /* 改为固定定位 */\n  top: 0;\n  left: 0;\n  right: 0;\n  padding-bottom: 10rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n  z-index: 100; /* 提高z-index确保在最上层 */\n}\n\n.navbar-left {\n  width: 60px;\n  display: flex;\n  align-items: center;\n}\n\n.back-icon {\n  width: 20px;\n  height: 20px;\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 17px;\n  font-weight: 500;\n  color: #fff;\n}\n\n.navbar-right {\n  width: 60px;\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/publish/car-detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni", "MiniProgramPage"], "mappings": ";;;;;;AAgNA,MAAM,aAAa,MAAW;;;;AAG9B,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAG9BC,kBAAAA,UAAU,MAAM;AACd,UAAI;AACF,cAAM,UAAUC,oBAAI;AACpB,wBAAgB,QAAQ,QAAQ,mBAAmB;AAAA,MACpD,SAAQ,GAAG;AACVA,kFAAc,aAAa,CAAC;AAAA,MAC7B;AAGD,YAAM,QAAQ;AACd,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,UAAU,YAAY,WAAW;AAGvC,YAAM,KAAK,QAAQ,MAAM;AACzBA,oBAAY,MAAA,MAAA,OAAA,uCAAA,YAAY,EAAE;AAG1B;IACF,CAAC;AAGD,UAAM,SAAS,MAAM;AACnBA,oBAAAA,MAAI,aAAa;AAAA,QACf,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,KAAK;AAAA,UACb,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,cAAc;AAChC,YAAM,OAAO,IAAI,KAAK,SAAS;AAC/B,aAAO,GAAG,KAAK,aAAa,CAAC,IAAI,KAAK,SAAS;AAAA,IACjD;AAGA,UAAM,cAAcF,cAAAA,IAAI,KAAK;AAC7B,UAAM,UAAUA,cAAAA,IAAI;AAAA,MAClB,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM,CAAC,OAAO,QAAQ,KAAK;AAAA,MAC3B,aAAa,KAAK,IAAK,IAAG,QAAW;AAAA;AAAA,MACrC,QAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,MACD,OAAO;AAAA,MACP,cAAc;AAAA,MACd,SAAS;AAAA,MACT,UAAU;AAAA,MACV,SAAS;AAAA,QACP,EAAE,MAAM,OAAO,MAAM,YAAa;AAAA,QAClC,EAAE,MAAM,QAAQ,MAAM,YAAa;AAAA,QACnC,EAAE,MAAM,QAAQ,MAAM,cAAe;AAAA,QACrC,EAAE,MAAM,QAAQ,MAAM,cAAe;AAAA,QACrC,EAAE,MAAM,MAAM,MAAM,eAAgB;AAAA,QACpC,EAAE,MAAM,MAAM,MAAM,kBAAmB;AAAA,MACxC;AAAA,MACD,YAAY;AAAA,QACV,EAAE,OAAO,MAAM,OAAO,KAAM;AAAA,QAC5B,EAAE,OAAO,QAAQ,OAAO,KAAM;AAAA,QAC9B,EAAE,OAAO,QAAQ,OAAO,QAAS;AAAA,QACjC,EAAE,OAAO,QAAQ,OAAO,MAAO;AAAA,MAChC;AAAA,MACD,aAAa;AAAA,MACb,OAAO;AAAA,QACL,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,YAAY;AAAA,MACb;AAAA,MACD,SAAS;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACH,CAAC;AAED,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,SAAS;AAAA,QACT,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,SAAS;AAAA,QACT,OAAO;AAAA,MACR;AAAA,IACH,CAAC;AAGD,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAG1B,UAAM,kBAAkB,MAAM;AAK5B,iBAAW,MAAM;AACf,oBAAY,QAAQ;AAAA,UAClB,GAAG,YAAY,MAAM,IAAI,UAAQ;AAAA,YAC/B,GAAG;AAAA,YACH,MAAM,CAAC,OAAO,KAAK;AAAA,UAC3B,EAAQ;AAAA,UACF;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,OAAO;AAAA,YACP,OAAO;AAAA,YACP,SAAS;AAAA,YACT,OAAO;AAAA,YACP,MAAM,CAAC,OAAO,UAAU,KAAK;AAAA,UAC9B;AAAA,QACP;AAAA,MACG,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,sBAAsB,CAAC,UAAU;AAErC,UAAI,UAAU,QAAQ,MAAM,IAAI;AAC9B;AAAA,MACD;AAEDE,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,gCAAgC,KAAK;AAAA,MAC9C,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,CAAC,MAAM;;AAC/B,UAAI;AAAG,UAAE;AACT,YAAM,gBAAc,aAAQ,MAAM,SAAd,mBAAqB,OAAM;AAC/CA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,2DAA2D,mBAAmB,MAAM,CAAC,aAAa,mBAAmB,WAAW,CAAC;AAAA,MAC1I,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,MAAM;AAC1B,kBAAY,QAAQ,CAAC,YAAY;AACjC,UAAI,YAAY,OAAO;AACrBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AASA,UAAM,YAAY,MAAM;AACtBA,oBAAAA,MAAI,cAAc;AAAA,QAChB,aAAa,QAAQ,MAAM,QAAQ;AAAA,QACnC,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrB,UAAI,CAAC,QAAQ,MAAM,SAAS,CAAC,QAAQ,MAAM,MAAM,IAAI;AACnDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAGDA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4BAA4B,QAAQ,MAAM,MAAM,EAAE,aAAa,mBAAmB,QAAQ,MAAM,MAAM,QAAQ,IAAI,CAAC;AAAA,MAC5H,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/ZA,GAAG,WAAWC,SAAe;"}