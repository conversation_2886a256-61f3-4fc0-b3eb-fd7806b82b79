import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { userApi } from '@/api/user'
import { removeToken, setToken, getToken } from '@/utils/auth'
import type { UserInfo, LoginParams, LoginResponse } from '@/types/user'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>('')
  const userInfo = ref<UserInfo | null>(null)
  const permissions = ref<string[]>([])
  const roles = ref<string[]>([])

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const hasPermission = computed(() => (permission: string) => {
    return permissions.value.includes(permission) || roles.value.includes('admin')
  })
  const hasRole = computed(() => (role: string) => {
    return roles.value.includes(role)
  })
  const hasAnyRole = computed(() => (roleList: string[]) => {
    return roleList.some(role => roles.value.includes(role))
  })

  // 登录
  const login = async (loginParams: LoginParams): Promise<void> => {
    try {
      const response = await userApi.login(loginParams)
      const { token: accessToken, userInfo: user } = response.data
      
      // 保存token
      token.value = accessToken
      setToken(accessToken)
      
      // 保存用户信息
      userInfo.value = user
      
      // 获取用户权限
      await getUserPermissions()
      
      ElMessage.success('登录成功')
    } catch (error) {
      ElMessage.error('登录失败，请检查用户名和密码')
      throw error
    }
  }

  // 登出
  const logout = async (): Promise<void> => {
    try {
      await userApi.logout()
    } catch (error) {
      console.error('登出接口调用失败:', error)
    } finally {
      // 清除本地数据
      token.value = ''
      userInfo.value = null
      permissions.value = []
      roles.value = []
      removeToken()
      
      ElMessage.success('已退出登录')
    }
  }

  // 获取用户信息
  const getUserInfo = async (): Promise<UserInfo> => {
    try {
      const response = await userApi.getUserInfo()
      userInfo.value = response.data
      return response.data
    } catch (error) {
      ElMessage.error('获取用户信息失败')
      throw error
    }
  }

  // 获取用户权限
  const getUserPermissions = async (): Promise<void> => {
    try {
      const response = await userApi.getUserPermissions()
      permissions.value = response.data.permissions || []
      roles.value = response.data.roles || []
    } catch (error) {
      console.error('获取用户权限失败:', error)
      permissions.value = []
      roles.value = []
    }
  }

  // 刷新token
  const refreshToken = async (): Promise<void> => {
    try {
      const response = await userApi.refreshToken()
      const { token: newToken } = response.data
      
      token.value = newToken
      setToken(newToken)
    } catch (error) {
      console.error('刷新token失败:', error)
      // token刷新失败，需要重新登录
      await logout()
      throw error
    }
  }

  // 修改密码
  const changePassword = async (oldPassword: string, newPassword: string): Promise<void> => {
    try {
      await userApi.changePassword({ oldPassword, newPassword })
      ElMessage.success('密码修改成功')
    } catch (error) {
      ElMessage.error('密码修改失败')
      throw error
    }
  }

  // 更新用户信息
  const updateUserInfo = async (updateData: Partial<UserInfo>): Promise<void> => {
    try {
      const response = await userApi.updateUserInfo(updateData)
      userInfo.value = { ...userInfo.value, ...response.data }
      ElMessage.success('用户信息更新成功')
    } catch (error) {
      ElMessage.error('用户信息更新失败')
      throw error
    }
  }

  // 初始化用户状态
  const initUser = async (): Promise<void> => {
    const savedToken = getToken()
    if (savedToken) {
      token.value = savedToken
      try {
        await getUserInfo()
        await getUserPermissions()
      } catch (error) {
        // token无效，清除本地数据
        await logout()
      }
    }
  }

  // 重置用户状态
  const resetUser = (): void => {
    token.value = ''
    userInfo.value = null
    permissions.value = []
    roles.value = []
    removeToken()
  }

  // 检查权限
  const checkPermission = (permission: string): boolean => {
    return hasPermission.value(permission)
  }

  // 检查角色
  const checkRole = (role: string): boolean => {
    return hasRole.value(role)
  }

  // 检查是否有任一角色
  const checkAnyRole = (roleList: string[]): boolean => {
    return hasAnyRole.value(roleList)
  }

  return {
    // 状态
    token,
    userInfo,
    permissions,
    roles,
    
    // 计算属性
    isLoggedIn,
    hasPermission,
    hasRole,
    hasAnyRole,
    
    // 方法
    login,
    logout,
    getUserInfo,
    getUserPermissions,
    refreshToken,
    changePassword,
    updateUserInfo,
    initUser,
    resetUser,
    checkPermission,
    checkRole,
    checkAnyRole
  }
})
