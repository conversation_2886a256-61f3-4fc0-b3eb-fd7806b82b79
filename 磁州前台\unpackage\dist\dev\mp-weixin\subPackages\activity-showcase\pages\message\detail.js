"use strict";
const common_vendor = require("../../../../common/vendor.js");
const utils_date = require("../../../../utils/date.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "detail",
  setup(__props) {
    const message = common_vendor.ref({
      title: "",
      content: "",
      type: "",
      createTime: /* @__PURE__ */ new Date(),
      images: [],
      actionText: ""
    });
    const getMessageDetail = async (id) => {
      try {
        message.value = {
          title: "活动通知",
          content: "您关注的活动即将开始...",
          type: "系统通知",
          createTime: /* @__PURE__ */ new Date(),
          images: [],
          actionText: "立即查看"
        };
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/activity-showcase/pages/message/detail.vue:63", "获取消息详情失败：", error);
      }
    };
    const previewImage = (index) => {
      common_vendor.index.previewImage({
        current: index,
        urls: message.value.images
      });
    };
    const handleAction = () => {
      common_vendor.index.showToast({
        title: "操作成功",
        icon: "success"
      });
    };
    common_vendor.onMounted(() => {
      getMessageDetail();
    });
    return (_ctx, _cache) => {
      var _a, _b;
      return common_vendor.e({
        a: common_vendor.t(message.value.title),
        b: common_vendor.t(common_vendor.unref(utils_date.formatTime)(message.value.createTime)),
        c: common_vendor.t(message.value.type),
        d: message.value.content,
        e: (_a = message.value.images) == null ? void 0 : _a.length
      }, ((_b = message.value.images) == null ? void 0 : _b.length) ? {
        f: common_vendor.f(message.value.images, (img, index, i0) => {
          return {
            a: index,
            b: img,
            c: common_vendor.o(($event) => previewImage(index), index)
          };
        })
      } : {}, {
        g: common_vendor.t(message.value.actionText || "查看详情"),
        h: common_vendor.o(handleAction)
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-e79a3555"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/message/detail.js.map
