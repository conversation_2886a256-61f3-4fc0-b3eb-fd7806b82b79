{"version": 3, "file": "uniapi.js", "sources": ["utils/uniapi.js"], "sourcesContent": ["/**\r\n * uni-app API 封装\r\n * 提供常用的 uni API 的封装，使其更易于使用\r\n */\r\n\r\n/**\r\n * 显示消息提示框\r\n * @param {string|object} options 提示内容或配置对象\r\n * @returns {Promise} Promise对象\r\n */\r\nexport function showToast(options) {\r\n  return new Promise((resolve) => {\r\n    if (typeof options === 'string') {\r\n      uni.showToast({\r\n        title: options,\r\n        icon: 'none',\r\n        duration: 2000,\r\n        success: resolve,\r\n        fail: resolve\r\n      });\r\n    } else {\r\n      uni.showToast({\r\n        title: options.title || '',\r\n        icon: options.icon || 'none',\r\n        duration: options.duration || 2000,\r\n        mask: options.mask || false,\r\n        success: resolve,\r\n        fail: resolve\r\n      });\r\n    }\r\n  });\r\n}\r\n\r\n/**\r\n * 显示加载中提示框\r\n * @param {string|object} options 提示内容或配置对象\r\n * @returns {Promise} Promise对象\r\n */\r\nexport function showLoading(options) {\r\n  return new Promise((resolve) => {\r\n    if (typeof options === 'string') {\r\n      uni.showLoading({\r\n        title: options,\r\n        mask: true,\r\n        success: resolve,\r\n        fail: resolve\r\n      });\r\n    } else {\r\n      uni.showLoading({\r\n        title: options.title || '',\r\n        mask: options.mask !== undefined ? options.mask : true,\r\n        success: resolve,\r\n        fail: resolve\r\n      });\r\n    }\r\n  });\r\n}\r\n\r\n/**\r\n * 隐藏加载中提示框\r\n */\r\nexport function hideLoading() {\r\n  uni.hideLoading();\r\n}\r\n\r\n/**\r\n * 显示模态对话框\r\n * @param {object} options 配置对象\r\n * @returns {Promise} Promise对象，resolve的结果为 { confirm: boolean, cancel: boolean }\r\n */\r\nexport function showModal(options) {\r\n  return new Promise((resolve) => {\r\n    uni.showModal({\r\n      title: options.title || '提示',\r\n      content: options.content || '',\r\n      showCancel: options.showCancel !== undefined ? options.showCancel : true,\r\n      cancelText: options.cancelText || '取消',\r\n      confirmText: options.confirmText || '确定',\r\n      success: resolve,\r\n      fail: () => resolve({ confirm: false, cancel: true })\r\n    });\r\n  });\r\n}\r\n\r\n/**\r\n * 显示操作菜单\r\n * @param {object} options 配置对象\r\n * @returns {Promise} Promise对象，resolve的结果为 { tapIndex: number }\r\n */\r\nexport function showActionSheet(options) {\r\n  return new Promise((resolve) => {\r\n    uni.showActionSheet({\r\n      itemList: options.itemList || [],\r\n      itemColor: options.itemColor || '#000000',\r\n      success: resolve,\r\n      fail: () => resolve({ tapIndex: -1 })\r\n    });\r\n  });\r\n}\r\n\r\n/**\r\n * 设置剪贴板内容\r\n * @param {string} data 需要设置的内容\r\n * @returns {Promise} Promise对象\r\n */\r\nexport function setClipboardData(data) {\r\n  return new Promise((resolve, reject) => {\r\n    uni.setClipboardData({\r\n      data,\r\n      success: resolve,\r\n      fail: reject\r\n    });\r\n  });\r\n}\r\n\r\n/**\r\n * 获取剪贴板内容\r\n * @returns {Promise<string>} Promise对象，resolve的结果为剪贴板内容\r\n */\r\nexport function getClipboardData() {\r\n  return new Promise((resolve, reject) => {\r\n    uni.getClipboardData({\r\n      success: (res) => resolve(res.data),\r\n      fail: reject\r\n    });\r\n  });\r\n}\r\n\r\n/**\r\n * 保存图片到相册\r\n * @param {string} filePath 图片文件路径\r\n * @returns {Promise} Promise对象\r\n */\r\nexport function saveImageToPhotosAlbum(filePath) {\r\n  return new Promise((resolve, reject) => {\r\n    uni.saveImageToPhotosAlbum({\r\n      filePath,\r\n      success: resolve,\r\n      fail: reject\r\n    });\r\n  });\r\n}\r\n\r\n/**\r\n * 预览图片\r\n * @param {object} options 配置对象\r\n * @returns {Promise} Promise对象\r\n */\r\nexport function previewImage(options) {\r\n  return new Promise((resolve) => {\r\n    uni.previewImage({\r\n      urls: options.urls || [],\r\n      current: options.current || options.urls[0],\r\n      success: resolve,\r\n      fail: resolve\r\n    });\r\n  });\r\n}\r\n\r\n/**\r\n * 获取系统信息\r\n * @returns {Promise<object>} Promise对象，resolve的结果为系统信息\r\n */\r\nexport function getSystemInfo() {\r\n  return new Promise((resolve, reject) => {\r\n    uni.getSystemInfo({\r\n      success: resolve,\r\n      fail: reject\r\n    });\r\n  });\r\n}\r\n\r\n/**\r\n * 导航到指定页面\r\n * @param {string|object} options 页面路径或配置对象\r\n * @returns {Promise} Promise对象\r\n */\r\nexport function navigateTo(options) {\r\n  return new Promise((resolve, reject) => {\r\n    if (typeof options === 'string') {\r\n      uni.navigateTo({\r\n        url: options,\r\n        success: resolve,\r\n        fail: reject\r\n      });\r\n    } else {\r\n      uni.navigateTo({\r\n        url: options.url,\r\n        success: resolve,\r\n        fail: reject\r\n      });\r\n    }\r\n  });\r\n}\r\n\r\n/**\r\n * 重定向到指定页面\r\n * @param {string|object} options 页面路径或配置对象\r\n * @returns {Promise} Promise对象\r\n */\r\nexport function redirectTo(options) {\r\n  return new Promise((resolve, reject) => {\r\n    if (typeof options === 'string') {\r\n      uni.redirectTo({\r\n        url: options,\r\n        success: resolve,\r\n        fail: reject\r\n      });\r\n    } else {\r\n      uni.redirectTo({\r\n        url: options.url,\r\n        success: resolve,\r\n        fail: reject\r\n      });\r\n    }\r\n  });\r\n}\r\n\r\n/**\r\n * 返回上一页\r\n * @param {number|object} options 返回的页面数或配置对象\r\n * @returns {Promise} Promise对象\r\n */\r\nexport function navigateBack(options) {\r\n  return new Promise((resolve) => {\r\n    if (typeof options === 'number') {\r\n      uni.navigateBack({\r\n        delta: options,\r\n        success: resolve,\r\n        fail: resolve\r\n      });\r\n    } else {\r\n      uni.navigateBack({\r\n        delta: options?.delta || 1,\r\n        success: resolve,\r\n        fail: resolve\r\n      });\r\n    }\r\n  });\r\n} "], "names": ["uni"], "mappings": ";;AAUO,SAAS,UAAU,SAAS;AACjC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,QAAI,OAAO,YAAY,UAAU;AAC/BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,QACV,SAAS;AAAA,QACT,MAAM;AAAA,MACd,CAAO;AAAA,IACP,OAAW;AACLA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,QAAQ,SAAS;AAAA,QACxB,MAAM,QAAQ,QAAQ;AAAA,QACtB,UAAU,QAAQ,YAAY;AAAA,QAC9B,MAAM,QAAQ,QAAQ;AAAA,QACtB,SAAS;AAAA,QACT,MAAM;AAAA,MACd,CAAO;AAAA,IACF;AAAA,EACL,CAAG;AACH;;"}