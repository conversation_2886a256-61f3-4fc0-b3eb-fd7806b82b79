/**
 * 微信小程序激励广告服务
 * 处理激励视频广告的展示和奖励发放
 */

import request from '@/utils/request';

class RewardedAdService {
  constructor() {
    this.videoAd = null;
    this.currentRewardType = null;
    this.currentCarpoolId = null;
    this.currentMerchantId = null;
    this.currentInfoId = null;
    this.adUnitId = 'adunit-9699c56c26082b54'; // 您的真实广告单元ID
  }

  /**
   * 初始化激励视频广告
   */
  initRewardedVideoAd() {
    // 检查微信小程序环境
    if (typeof wx === 'undefined' || !wx.createRewardedVideoAd) {
      console.warn('当前环境不支持激励视频广告');
      return false;
    }

    try {
      this.videoAd = wx.createRewardedVideoAd({
        adUnitId: this.adUnitId
      });

      // 监听广告加载成功
      this.videoAd.onLoad(() => {
        console.log('激励视频广告加载成功');
      });

      // 监听广告加载失败
      this.videoAd.onError((err) => {
        console.error('激励视频广告加载失败', err);
        uni.showToast({
          title: '广告加载失败',
          icon: 'none'
        });
      });

      // 监听广告关闭
      this.videoAd.onClose((res) => {
        if (res && res.isEnded || res === undefined) {
          // 广告播放完成，发放奖励
          console.log('激励视频广告完成，发放奖励');
          this.grantReward();
        } else {
          // 播放中途退出，不发放奖励
          console.log('激励视频广告未完成');
          uni.showToast({
            title: '请观看完整广告',
            icon: 'none'
          });
        }
      });

      return true;
    } catch (error) {
      console.error('初始化激励视频广告失败', error);
      return false;
    }
  }

  /**
   * 显示激励视频广告
   * @param {string} rewardType - 奖励类型: 'free_publish', 'free_top', 'free_refresh', 'merchant_renew', 'merchant_join', 'merchant_top', 'merchant_refresh'
   * @param {number} carpoolId - 拼车信息ID（置顶和刷新时需要）
   * @param {number} merchantId - 商家ID（商家相关功能时需要）
   * @param {number} infoId - 信息ID（商家信息置顶和刷新时需要）
   */
  async showRewardedVideoAd(rewardType, carpoolId = null, merchantId = null, infoId = null) {
    this.currentRewardType = rewardType;
    this.currentCarpoolId = carpoolId;
    this.currentMerchantId = merchantId;
    this.currentInfoId = infoId;

    // 检查是否已初始化
    if (!this.videoAd) {
      const initSuccess = this.initRewardedVideoAd();
      if (!initSuccess) {
        uni.showToast({
          title: '广告功能不可用',
          icon: 'none'
        });
        return false;
      }
    }

    try {
      // 显示广告
      await this.videoAd.show();
      return true;
    } catch (error) {
      console.error('显示激励视频广告失败', error);
      
      // 失败重试
      try {
        await this.videoAd.load();
        await this.videoAd.show();
        return true;
      } catch (retryError) {
        console.error('重试显示激励视频广告失败', retryError);
        uni.showToast({
          title: '广告显示失败',
          icon: 'none'
        });
        return false;
      }
    }
  }

  /**
   * 发放奖励
   */
  async grantReward() {
    try {
      // 获取用户信息
      const userInfo = uni.getStorageSync('userInfo');
      if (!userInfo || !userInfo.id) {
        uni.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }

      // 构建请求数据
      const requestData = {
        reward_type: this.currentRewardType,
        user_id: userInfo.id,
        ad_unit_id: this.adUnitId,
        timestamp: Date.now()
      };

      // 如果是置顶或刷新，需要传递拼车信息ID
      if (this.currentCarpoolId) {
        requestData.carpool_id = this.currentCarpoolId;
      }

      // 如果是商家相关功能，需要传递商家ID
      if (this.currentMerchantId) {
        requestData.merchant_id = this.currentMerchantId;
      }

      // 如果是商家信息置顶或刷新，需要传递信息ID
      if (this.currentInfoId) {
        requestData.info_id = this.currentInfoId;
      }

      // 根据奖励类型调用不同的API
      let apiUrl = '/api/carpool/ads/reward';
      if (this.currentRewardType === 'merchant_renew') {
        apiUrl = '/api/merchant/ads/renew';
      } else if (this.currentRewardType === 'merchant_join') {
        apiUrl = '/api/merchant/ads/join';
      } else if (this.currentRewardType === 'merchant_top') {
        apiUrl = '/api/merchant/ads/top';
      } else if (this.currentRewardType === 'merchant_refresh') {
        apiUrl = '/api/merchant/ads/refresh';
      }

      // 调用后台API发放奖励
      const response = await request.post(apiUrl, requestData);

      if (response.data && response.data.success) {
        // 奖励发放成功
        const rewardData = response.data.data;
        this.showRewardSuccess(rewardData);
      } else {
        // 奖励发放失败
        uni.showToast({
          title: response.data?.message || '奖励发放失败',
          icon: 'none'
        });
      }

    } catch (error) {
      console.error('发放奖励失败', error);
      uni.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    }
  }

  /**
   * 显示奖励成功提示
   * @param {Object} rewardData - 奖励数据
   */
  showRewardSuccess(rewardData) {
    let message = '奖励发放成功';
    
    switch (rewardData.type) {
      case 'free_publish':
        message = '获得1次免费发布机会！';
        break;
      case 'free_top':
        message = '置顶2小时成功！';
        break;
      case 'free_refresh':
        message = '刷新成功！';
        break;
      case 'merchant_renew':
        message = `商家入驻续费成功！延长${rewardData.days || 7}天`;
        break;
      case 'merchant_join':
        message = '商家入驻成功！获得1个月免费特权';
        break;
      case 'merchant_top':
        message = '商家信息置顶成功！置顶2小时';
        break;
      case 'merchant_refresh':
        message = '商家信息刷新成功！';
        break;
    }

    uni.showToast({
      title: message,
      icon: 'success',
      duration: 2000
    });

    // 触发页面刷新事件
    uni.$emit('rewardGranted', {
      type: rewardData.type,
      data: rewardData
    });
  }

  /**
   * 检查今日观看次数
   * @param {string} adType - 广告类型
   */
  async checkTodayWatchCount(adType) {
    try {
      const userInfo = uni.getStorageSync('userInfo');
      if (!userInfo || !userInfo.id) {
        return { canWatch: false, message: '请先登录' };
      }

      const response = await request.get('/api/carpool/ads/stats', {
        params: {
          user_id: userInfo.id,
          ad_type: adType,
          date: new Date().toISOString().split('T')[0]
        }
      });

      const stats = response.data;
      const dailyLimit = this.getDailyLimit(adType);

      if (stats.today_views >= dailyLimit) {
        return {
          canWatch: false,
          message: `今日观看次数已达上限（${dailyLimit}次）`
        };
      }

      return {
        canWatch: true,
        remaining: dailyLimit - stats.today_views
      };

    } catch (error) {
      console.error('检查观看次数失败', error);
      return { canWatch: true }; // 出错时允许观看
    }
  }

  /**
   * 获取每日观看限制
   * @param {string} adType - 广告类型
   */
  getDailyLimit(adType) {
    const limits = {
      'publish': 5,
      'top': 3,
      'refresh': 5,
      'merchant_renew': 2,    // 商家续费每月限制2次
      'merchant_join': 1,     // 商家入驻每月限制1次
      'merchant_top': 3,      // 商家置顶每日限制3次
      'merchant_refresh': 5   // 商家刷新每日限制5次
    };
    return limits[adType] || 3;
  }

  /**
   * 销毁广告实例
   */
  destroy() {
    if (this.videoAd) {
      this.videoAd.destroy();
      this.videoAd = null;
    }
  }
}

// 创建单例实例
const rewardedAdService = new RewardedAdService();

export default rewardedAdService;
