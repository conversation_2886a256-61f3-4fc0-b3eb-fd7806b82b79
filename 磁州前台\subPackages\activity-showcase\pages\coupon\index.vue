<template>
  <view class="coupon-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <image class="back-icon" src="/static/images/tabbar/最新返回键.png" mode="aspectFit"></image>
        </view>
        <view class="navbar-title">优惠券</view>
        <view class="navbar-right">
          <view class="close-btn" @click="goBack">
            <svg class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="22" height="22">
              <path d="M512 421.490332L331.349941 240.840273c-24.988383-24.988383-65.35828-24.988383-90.346664 0-24.988383 24.988383-24.988383 65.35828 0 90.346664L421.653336 512 240.840273 692.812059c-24.988383 24.988383-24.988383 65.35828 0 90.346664 24.988383 24.988383 65.35828 24.988383 90.346664 0L512 602.509668l180.650059 180.650059c24.988383 24.988383 65.35828 24.988383 90.346664 0 24.988383-24.988383 24.988383-65.35828 0-90.346664L602.346664 512l180.813063-180.812059c24.988383-24.988383 24.988383-65.35828 0-90.346664-24.988383-24.988383-65.35828-24.988383-90.346664 0L512 421.490332z" fill="#FFFFFF"></path>
            </svg>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 分类选项卡 -->
    <view class="category-tabs">
      <view 
        class="tab-item" 
        v-for="(tab, index) in tabs" 
        :key="index"
        :class="{ active: currentTabIndex === index }"
        @click="switchTab(index)"
      >
        <text>{{ tab.name }}</text>
      </view>
      <view class="tab-line" :style="tabLineStyle"></view>
    </view>
    
    <!-- 内容区域 -->
    <scroll-view 
      class="content-scroll" 
      scroll-y 
      @scrolltolower="loadMore"
    >
      <!-- 可领取优惠券 -->
      <view class="coupon-list" v-if="currentTabIndex === 0">
        <view 
          class="coupon-item" 
          v-for="(item, index) in availableCoupons" 
          :key="index"
        >
          <view class="coupon-left">
            <view class="coupon-amount">
              <text class="amount-symbol" v-if="item.type === 'cash'">¥</text>
              <text class="amount-value">{{ item.value }}</text>
              <text class="amount-condition" v-if="item.minAmount > 0">满{{ item.minAmount }}可用</text>
              <text class="amount-condition" v-else>无门槛</text>
            </view>
          </view>
          <view class="coupon-right">
            <view class="coupon-info" @click="viewCouponDetail(item.id)">
              <view class="coupon-title">{{ item.title }}</view>
              <view class="coupon-desc">{{ item.description }}</view>
              <view class="coupon-time">{{ getTimeText(item.startTime, item.endTime) }}</view>
            </view>
            <view class="coupon-btn" @click="receiveCoupon(item.id)">
              <text>立即领取</text>
            </view>
          </view>
          <view class="coupon-tag" v-if="item.tag">{{ item.tag }}</view>
          <view class="coupon-border"></view>
        </view>
      </view>
      
      <!-- 我的优惠券 -->
      <view class="my-coupon-section" v-else>
        <view class="coupon-status-tabs">
          <view 
            class="status-tab" 
            v-for="(status, index) in couponStatus" 
            :key="index"
            :class="{ active: currentStatusIndex === index }"
            @click="switchStatus(index)"
          >
            <text>{{ status.name }}</text>
          </view>
        </view>
        
        <view class="my-coupon-list" v-if="getMyCoupons().length > 0">
          <view 
            class="coupon-item" 
            v-for="(item, index) in getMyCoupons()" 
            :key="index"
            :class="{ 'coupon-used': item.status === 'used', 'coupon-expired': item.status === 'expired' }"
          >
            <view class="coupon-left">
              <view class="coupon-amount">
                <text class="amount-symbol" v-if="item.type === 'cash'">¥</text>
                <text class="amount-value">{{ item.value }}</text>
                <text class="amount-condition" v-if="item.minAmount > 0">满{{ item.minAmount }}可用</text>
                <text class="amount-condition" v-else>无门槛</text>
              </view>
            </view>
            <view class="coupon-right">
              <view class="coupon-info">
                <view class="coupon-title">{{ item.title }}</view>
                <view class="coupon-desc">{{ item.description }}</view>
                <view class="coupon-time">{{ getTimeText(item.startTime, item.endTime) }}</view>
              </view>
              <view class="coupon-btn" v-if="item.status === 'valid'" @click="useCoupon(item.id)">
                <text>立即使用</text>
              </view>
              <view class="coupon-status" v-else>
                <text>{{ getStatusText(item.status) }}</text>
              </view>
            </view>
            <view class="coupon-tag" v-if="item.tag">{{ item.tag }}</view>
            <view class="coupon-border"></view>
            <view class="coupon-mask" v-if="item.status !== 'valid'"></view>
            <view class="coupon-stamp" v-if="item.status === 'used'">已使用</view>
            <view class="coupon-stamp" v-else-if="item.status === 'expired'">已过期</view>
          </view>
        </view>
        
        <view class="empty-tip" v-else>
          <image class="empty-image" src="/static/images/empty-coupon.png" mode="aspectFit"></image>
          <text class="empty-text">{{ getEmptyText() }}</text>
          <view class="empty-btn" @click="switchTab(0)" v-if="currentStatusIndex === 0">
            <text>去领券</text>
          </view>
        </view>
      </view>
      
      <!-- 加载更多提示 -->
      <view class="loading-more" v-if="loading">
        <text>加载中...</text>
      </view>
      
      <!-- 到底了提示 -->
      <view class="no-more" v-if="noMore">
        <text>已经到底啦~</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      refreshing: false,
      loading: false,
      noMore: false,
      currentTabIndex: 0,
      currentStatusIndex: 0,
      tabs: [
        { name: '可领取' },
        { name: '我的券' }
      ],
      couponStatus: [
        { name: '未使用' },
        { name: '已使用' },
        { name: '已过期' }
      ],
      availableCoupons: [
        {
          id: 1,
          title: '新人专享券',
          description: '仅限新用户使用',
          type: 'cash',
          value: '30',
          minAmount: 0,
          startTime: new Date(Date.now()).toISOString(),
          endTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          tag: '新人专享'
        },
        {
          id: 2,
          title: '满减优惠券',
          description: '全场通用',
          type: 'cash',
          value: '10',
          minAmount: 99,
          startTime: new Date(Date.now()).toISOString(),
          endTime: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
          tag: '热门'
        },
        {
          id: 3,
          title: '满减优惠券',
          description: '全场通用',
          type: 'cash',
          value: '50',
          minAmount: 299,
          startTime: new Date(Date.now()).toISOString(),
          endTime: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000).toISOString(),
          tag: '限量'
        },
        {
          id: 4,
          title: '满减优惠券',
          description: '仅限食品类',
          type: 'cash',
          value: '20',
          minAmount: 129,
          startTime: new Date(Date.now()).toISOString(),
          endTime: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 5,
          title: '满减优惠券',
          description: '仅限电器类',
          type: 'cash',
          value: '100',
          minAmount: 1000,
          startTime: new Date(Date.now()).toISOString(),
          endTime: new Date(Date.now() + 25 * 24 * 60 * 60 * 1000).toISOString(),
          tag: '特惠'
        }
      ],
      myCoupons: [
        {
          id: 6,
          title: '满减优惠券',
          description: '全场通用',
          type: 'cash',
          value: '15',
          minAmount: 99,
          startTime: new Date(Date.now()).toISOString(),
          endTime: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'valid',
          tag: '热门'
        },
        {
          id: 7,
          title: '满减优惠券',
          description: '仅限服装类',
          type: 'cash',
          value: '30',
          minAmount: 199,
          startTime: new Date(Date.now()).toISOString(),
          endTime: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'valid'
        },
        {
          id: 8,
          title: '满减优惠券',
          description: '全场通用',
          type: 'cash',
          value: '50',
          minAmount: 299,
          startTime: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          endTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'expired',
          tag: '限量'
        },
        {
          id: 9,
          title: '满减优惠券',
          description: '仅限食品类',
          type: 'cash',
          value: '20',
          minAmount: 129,
          startTime: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
          endTime: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'used'
        }
      ]
    }
  },
  computed: {
    tabLineStyle() {
      const width = 100 / this.tabs.length
      const left = this.currentTabIndex * width
      return {
        width: width + '%',
        transform: `translateX(${left * 100}%)`
      }
    }
  },
  onLoad() {
    // 页面加载时获取数据
    this.fetchData()
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },
    
    // 切换选项卡
    switchTab(index) {
      this.currentTabIndex = index
      this.fetchData()
    },
    
    // 切换优惠券状态
    switchStatus(index) {
      this.currentStatusIndex = index
    },
    
    // 获取我的优惠券列表
    getMyCoupons() {
      const statusMap = ['valid', 'used', 'expired']
      const status = statusMap[this.currentStatusIndex]
      return this.myCoupons.filter(item => item.status === status)
    },
    
    // 加载更多
    loadMore() {
      if (this.loading || this.noMore) return
      
      this.loading = true
      
      // 模拟加载更多数据
      setTimeout(() => {
        if (this.currentTabIndex === 0) {
          // 添加更多可领取优惠券
          const moreCoupons = [
            {
              id: 10,
              title: '满减优惠券',
              description: '仅限母婴类',
              type: 'cash',
              value: '25',
              minAmount: 199,
              startTime: new Date(Date.now()).toISOString(),
              endTime: new Date(Date.now() + 18 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
              id: 11,
              title: '满减优惠券',
              description: '仅限美妆类',
              type: 'cash',
              value: '40',
              minAmount: 299,
              startTime: new Date(Date.now()).toISOString(),
              endTime: new Date(Date.now() + 12 * 24 * 60 * 60 * 1000).toISOString(),
              tag: '爆款'
            }
          ]
          
          this.availableCoupons = [...this.availableCoupons, ...moreCoupons]
        }
        
        this.noMore = true // 示例中加载一次后就没有更多数据
        this.loading = false
      }, 1500)
    },
    
    // 获取数据
    fetchData() {
      // 实际项目中，这里应该根据当前选中的选项卡调用API获取数据
      // 示例中使用的是静态数据
    },
    
    // 领取优惠券
    receiveCoupon(id) {
      // 实际项目中，这里应该调用API领取优惠券
      uni.showLoading({
        title: '领取中...'
      })
      
      setTimeout(() => {
        uni.hideLoading()
        
        // 模拟领取成功
        const coupon = this.availableCoupons.find(item => item.id === id)
        if (coupon) {
          // 将优惠券添加到我的优惠券列表
          const myCoupon = {
            ...coupon,
            id: Date.now(), // 生成新ID
            status: 'valid'
          }
          this.myCoupons.push(myCoupon)
          
          uni.showToast({
            title: '领取成功',
            icon: 'success'
          })
        }
      }, 1000)
    },
    
    // 使用优惠券
    useCoupon(id) {
      // 跳转到优惠券详情页
      uni.navigateTo({
        url: `/subPackages/activity-showcase/pages/coupon/detail?id=${id}`
      })
    },
    
    // 查看优惠券详情
    viewCouponDetail(id) {
      uni.navigateTo({
        url: `/subPackages/activity-showcase/pages/coupon/detail?id=${id}`
      });
    },
    
    // 获取活动时间文本
    getTimeText(startTime, endTime) {
      const start = new Date(startTime)
      const end = new Date(endTime)
      
      const startMonth = start.getMonth() + 1
      const startDay = start.getDate()
      const endMonth = end.getMonth() + 1
      const endDay = end.getDate()
      
      return `${startMonth}.${startDay}-${endMonth}.${endDay}`
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'valid': '未使用',
        'used': '已使用',
        'expired': '已过期'
      }
      return statusMap[status] || ''
    },
    
    // 获取空状态提示文本
    getEmptyText() {
      const textMap = [
        '您还没有未使用的优惠券',
        '您还没有已使用的优惠券',
        '您还没有已过期的优惠券'
      ]
      return textMap[this.currentStatusIndex] || '暂无数据'
    }
  }
}
</script>

<style lang="scss" scoped>
.coupon-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F2F2F7;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
  
  .navbar-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #FFCC00 0%, #FF9500 100%);
  }
  
  .navbar-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding-top: var(--status-bar-height, 25px);
    padding-left: 30rpx;
    padding-right: 30rpx;
    box-sizing: border-box;
    
    .back-btn {
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .back-icon {
      width: 100%;
      height: 100%;
    }
    
    .navbar-title {
      font-size: 18px;
      font-weight: 600;
      color: #FFFFFF;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
    }
    
    .navbar-right {
      width: 80rpx;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      
      .close-btn {
        width: 64rpx;
        height: 64rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

/* 分类选项卡 */
.category-tabs {
  position: relative;
  display: flex;
  background-color: #FFFFFF;
  height: 88rpx;
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
  
  .tab-item {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 28rpx;
    color: #666666;
    position: relative;
    
    &.active {
      color: #FF9500;
      font-weight: 600;
    }
  }
  
  .tab-line {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 4rpx;
    background-color: #FF9500;
    transition: transform 0.3s ease;
  }
}

/* 内容区域 */
.content-scroll {
  flex: 1;
  width: 100%;
}

/* 优惠券列表 */
.coupon-list {
  padding: 20rpx;
}

.coupon-item {
  position: relative;
  display: flex;
  margin-bottom: 20rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
  
  &.coupon-used, &.coupon-expired {
    opacity: 0.7;
  }
  
  .coupon-left {
    width: 200rpx;
    background: linear-gradient(135deg, #FFCC00 0%, #FF9500 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20rpx;
    position: relative;
    
    .coupon-amount {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #FFFFFF;
      
      .amount-symbol {
        font-size: 28rpx;
        font-weight: 600;
      }
      
      .amount-value {
        font-size: 60rpx;
        font-weight: 700;
        line-height: 1;
      }
      
      .amount-condition {
        font-size: 22rpx;
        margin-top: 8rpx;
      }
    }
  }
  
  .coupon-right {
    flex: 1;
    padding: 20rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    
    .coupon-info {
      .coupon-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #333333;
        margin-bottom: 8rpx;
      }
      
      .coupon-desc {
        font-size: 24rpx;
        color: #666666;
        margin-bottom: 12rpx;
      }
      
      .coupon-time {
        font-size: 22rpx;
        color: #999999;
      }
    }
    
    .coupon-btn {
      align-self: flex-end;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 60rpx;
      width: 160rpx;
      border-radius: 30rpx;
      background: linear-gradient(135deg, #FFCC00 0%, #FF9500 100%);
      font-size: 26rpx;
      font-weight: 500;
      color: #FFFFFF;
    }
    
    .coupon-status {
      align-self: flex-end;
      font-size: 26rpx;
      color: #999999;
    }
  }
  
  .coupon-tag {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    padding: 4rpx 12rpx;
    font-size: 22rpx;
    color: #FFFFFF;
    border-radius: 10rpx;
    background-color: rgba(255, 59, 48, 0.8);
  }
  
  .coupon-border {
    position: absolute;
    left: 200rpx;
    top: 0;
    bottom: 0;
    width: 0;
    border-left: 1px dashed #F2F2F7;
    
    &::before, &::after {
      content: '';
      position: absolute;
      left: -10rpx;
      width: 20rpx;
      height: 20rpx;
      border-radius: 50%;
      background-color: #F2F2F7;
    }
    
    &::before {
      top: -10rpx;
    }
    
    &::after {
      bottom: -10rpx;
    }
  }
  
  .coupon-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.1);
    pointer-events: none;
  }
  
  .coupon-stamp {
    position: absolute;
    top: 50%;
    right: 60rpx;
    transform: translateY(-50%) rotate(-30deg);
    font-size: 60rpx;
    font-weight: 700;
    color: rgba(255, 59, 48, 0.6);
    border: 6rpx solid rgba(255, 59, 48, 0.6);
    padding: 10rpx 20rpx;
    border-radius: 16rpx;
  }
}

/* 我的优惠券 */
.my-coupon-section {
  .coupon-status-tabs {
    display: flex;
    background-color: #FFFFFF;
    padding: 20rpx;
    
    .status-tab {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 60rpx;
      font-size: 26rpx;
      color: #666666;
      position: relative;
      border-radius: 30rpx;
      margin: 0 10rpx;
      
      &.active {
        color: #FFFFFF;
        background: linear-gradient(135deg, #FFCC00 0%, #FF9500 100%);
      }
    }
  }
  
  .my-coupon-list {
    padding: 20rpx;
  }
  
  .empty-tip {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 60rpx 0;
    
    .empty-image {
      width: 200rpx;
      height: 200rpx;
      margin-bottom: 20rpx;
    }
    
    .empty-text {
      font-size: 28rpx;
      color: #999999;
      margin-bottom: 30rpx;
    }
    
    .empty-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 80rpx;
      width: 300rpx;
      border-radius: 40rpx;
      background: linear-gradient(135deg, #FFCC00 0%, #FF9500 100%);
      font-size: 28rpx;
      font-weight: 500;
      color: #FFFFFF;
    }
  }
}

/* 加载更多和到底了提示 */
.loading-more, .no-more {
  text-align: center;
  padding: 20rpx 0;
  font-size: 24rpx;
  color: #999999;
}
</style> 