{"version": 3, "file": "success.js", "sources": ["subPackages/payment/pages/success.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNccGF5bWVudFxwYWdlc1xzdWNjZXNzLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"success-container\">\r\n\t\t<view class=\"success-content\">\r\n\t\t\t<image class=\"success-icon\" src=\"/static/images/pay/success.png\" mode=\"aspectFit\"></image>\r\n\t\t\t<view class=\"success-title\">支付成功</view>\r\n\t\t\t<view class=\"success-desc\">您的订单已支付完成</view>\r\n\t\t\t\r\n\t\t\t<view class=\"order-info\">\r\n\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t<text class=\"label\">订单类型</text>\r\n\t\t\t\t\t<text class=\"value\">{{getOrderTypeText(orderInfo.orderType)}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-item\" v-if=\"orderInfo.days\">\r\n\t\t\t\t\t<text class=\"label\">置顶时长</text>\r\n\t\t\t\t\t<text class=\"value\">{{orderInfo.days}}天</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t<text class=\"label\">支付金额</text>\r\n\t\t\t\t\t<text class=\"value\">¥{{orderInfo.amount}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t<text class=\"label\">订单编号</text>\r\n\t\t\t\t\t<text class=\"value\">{{orderInfo.orderNo}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"action-buttons\">\r\n\t\t\t\t<button class=\"action-btn view-btn\" @click=\"viewDetail\">查看详情</button>\r\n\t\t\t\t<button class=\"action-btn back-btn\" @click=\"goBack\">返回首页</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue';\r\n\r\n// 响应式状态\r\nconst orderInfo = ref({\r\n\torderType: '',\r\n\tdays: 0,\r\n\tamount: 0,\r\n\torderNo: ''\r\n});\r\n\r\n// 获取订单类型文本\r\nconst getOrderTypeText = (type) => {\r\n\tconst typeMap = {\r\n\t\t'top': '信息置顶',\r\n\t\t'publish': '信息发布',\r\n\t\t'vip': '会员开通'\r\n\t};\r\n\treturn typeMap[type] || type;\r\n};\r\n\r\n// 查看详情\r\nconst viewDetail = () => {\r\n\tconst infoId = uni.getStorageSync('lastPublishId');\r\n\tuni.navigateTo({\r\n\t\turl: `/pages/publish/detail?id=${infoId}`\r\n\t});\r\n};\r\n\r\n// 返回首页\r\nconst goBack = () => {\r\n\tuni.switchTab({\r\n\t\turl: '/pages/index/index'\r\n\t});\r\n};\r\n\r\n// 生命周期钩子\r\nonMounted(() => {\r\n\t// 获取路由参数\r\n\tconst pages = getCurrentPages();\r\n\tconst currentPage = pages[pages.length - 1];\r\n\tconst options = currentPage.options;\r\n\t\r\n\tif (options.orderInfo) {\r\n\t\torderInfo.value = JSON.parse(decodeURIComponent(options.orderInfo));\r\n\t}\r\n});\r\n</script>\r\n\r\n<style>\r\n\t.success-container {\r\n\t\tmin-height: 100vh;\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tpadding: 40rpx;\r\n\t}\r\n\t\r\n\t.success-content {\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tborder-radius: 24rpx;\r\n\t\tpadding: 60rpx 40rpx;\r\n\t\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t}\r\n\t\r\n\t.success-icon {\r\n\t\twidth: 160rpx;\r\n\t\theight: 160rpx;\r\n\t\tmargin-bottom: 40rpx;\r\n\t}\r\n\t\r\n\t.success-title {\r\n\t\tfont-size: 40rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: 600;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t\r\n\t.success-desc {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666;\r\n\t\tmargin-bottom: 60rpx;\r\n\t}\r\n\t\r\n\t.order-info {\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #f8f9fa;\r\n\t\tborder-radius: 16rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: 40rpx;\r\n\t}\r\n\t\r\n\t.info-item {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 20rpx 0;\r\n\t\tborder-bottom: 2rpx solid #f0f0f0;\r\n\t}\r\n\t\r\n\t.info-item:last-child {\r\n\t\tborder-bottom: none;\r\n\t}\r\n\t\r\n\t.label {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\t\r\n\t.value {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\t\r\n\t.action-buttons {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\tmargin-top: 40rpx;\r\n\t}\r\n\t\r\n\t.action-btn {\r\n\t\twidth: 45%;\r\n\t\theight: 88rpx;\r\n\t\tborder-radius: 44rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 500;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\ttransition: all 0.3s;\r\n\t}\r\n\t\r\n\t.view-btn {\r\n\t\tbackground-color: #0052CC;\r\n\t\tcolor: #FFFFFF;\r\n\t\tbox-shadow: 0 8rpx 16rpx rgba(0, 82, 204, 0.2);\r\n\t}\r\n\t\r\n\t.back-btn {\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tcolor: #666;\r\n\t\tborder: 2rpx solid #e5e5e5;\r\n\t}\r\n\t\r\n\t.action-btn:active {\r\n\t\ttransform: scale(0.98);\r\n\t}\r\n</style> \r\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/payment/pages/success.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "onMounted", "MiniProgramPage"], "mappings": ";;;;;;AAsCA,UAAM,YAAYA,cAAAA,IAAI;AAAA,MACrB,WAAW;AAAA,MACX,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,SAAS;AAAA,IACV,CAAC;AAGD,UAAM,mBAAmB,CAAC,SAAS;AAClC,YAAM,UAAU;AAAA,QACf,OAAO;AAAA,QACP,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACC,aAAO,QAAQ,IAAI,KAAK;AAAA,IACzB;AAGA,UAAM,aAAa,MAAM;AACxB,YAAM,SAASC,cAAAA,MAAI,eAAe,eAAe;AACjDA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,4BAA4B,MAAM;AAAA,MACzC,CAAE;AAAA,IACF;AAGA,UAAM,SAAS,MAAM;AACpBA,oBAAAA,MAAI,UAAU;AAAA,QACb,KAAK;AAAA,MACP,CAAE;AAAA,IACF;AAGAC,kBAAAA,UAAU,MAAM;AAEf,YAAM,QAAQ;AACd,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,UAAU,YAAY;AAE5B,UAAI,QAAQ,WAAW;AACtB,kBAAU,QAAQ,KAAK,MAAM,mBAAmB,QAAQ,SAAS,CAAC;AAAA,MAClE;AAAA,IACF,CAAC;;;;;;;;;;;;;;;;;AC/ED,GAAG,WAAWC,SAAe;"}