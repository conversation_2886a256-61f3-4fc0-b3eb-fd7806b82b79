"use strict";
function formatTime(time, format = "YYYY-MM-DD HH:mm:ss") {
  if (!time)
    return "";
  const date = new Date(time);
  if (isNaN(date.getTime()))
    return "";
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  const second = date.getSeconds();
  return format.replace("YYYY", year).replace("MM", month.toString().padStart(2, "0")).replace("DD", day.toString().padStart(2, "0")).replace("HH", hour.toString().padStart(2, "0")).replace("mm", minute.toString().padStart(2, "0")).replace("ss", second.toString().padStart(2, "0"));
}
function formatAmount(amount, decimals = 2) {
  if (typeof amount !== "number")
    return "0.00";
  return amount.toFixed(decimals);
}
function formatNumber(num) {
  if (typeof num !== "number")
    return "0";
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}
function formatDistance(meters) {
  if (typeof meters !== "number")
    return "";
  if (meters < 1e3) {
    return `${Math.round(meters)}米`;
  } else {
    return `${(meters / 1e3).toFixed(1)}公里`;
  }
}
exports.formatAmount = formatAmount;
exports.formatDistance = formatDistance;
exports.formatNumber = formatNumber;
exports.formatTime = formatTime;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/format.js.map
