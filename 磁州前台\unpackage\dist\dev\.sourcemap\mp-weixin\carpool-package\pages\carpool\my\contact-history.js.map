{"version": 3, "file": "contact-history.js", "sources": ["carpool-package/pages/carpool/my/contact-history.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/Y2FycG9vbC1wYWNrYWdlXHBhZ2VzXGNhcnBvb2xcbXlcY29udGFjdC1oaXN0b3J5LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"contact-history-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-header\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"header-content\">\n        <view class=\"left-action\" @click=\"goBack\">\n          <image src=\"/static/images/tabbar/最新返回键.png\" class=\"action-icon back-icon\"></image>\n        </view>\n        <view class=\"title-area\">\n          <text class=\"page-title\">联系历史</text>\n        </view>\n        <view class=\"right-action\">\n          <!-- 预留位置 -->\n        </view>\n      </view>\n    </view>\n    \n    <!-- 联系历史列表 -->\n    <view class=\"history-list\" :style=\"{ paddingTop: (statusBarHeight + 44 + 20) + 'px' }\">\n      <view class=\"empty-state\" v-if=\"contactHistory.length === 0\">\n        <image src=\"/static/images/icons/empty-history.png\" class=\"empty-icon\"></image>\n        <text class=\"empty-text\">暂无联系历史</text>\n      </view>\n      \n      <view class=\"history-item\" v-for=\"(item, index) in contactHistory\" :key=\"index\">\n        <view class=\"history-header\">\n          <view class=\"driver-info\">\n            <text class=\"driver-name\">{{getDriverName(item)}}</text>\n            <text class=\"driver-phone\">{{formatPhone(item.phoneNumber)}}</text>\n          </view>\n          <text class=\"contact-time\">{{formatDate(item.contactTime)}}</text>\n        </view>\n        \n        <view class=\"trip-info\">\n          <view class=\"route-info\">\n            <view class=\"route-point\">\n              <view class=\"point start\"></view>\n              <text class=\"point-text\">{{item.startLocation}}</text>\n            </view>\n            <view class=\"route-line\"></view>\n            <view class=\"route-point\">\n              <view class=\"point end\"></view>\n              <text class=\"point-text\">{{item.endLocation}}</text>\n            </view>\n          </view>\n          <view class=\"time-info\">\n            <text class=\"departure-date\">{{item.departureDate}}</text>\n            <text class=\"departure-time\">{{item.departureTime}}</text>\n          </view>\n        </view>\n        \n        <view class=\"action-buttons\">\n          <button class=\"action-btn call\" @click=\"callDriver(item)\">\n            <image src=\"/static/images/icons/phone.png\" class=\"btn-icon\"></image>\n            <text>再次联系</text>\n          </button>\n          <button class=\"action-btn rate\" @click=\"rateDriver(item)\">\n            <image src=\"/static/images/icons/star.png\" class=\"btn-icon\"></image>\n            <text>{{hasRated(item) ? '修改评价' : '评价司机'}}</text>\n          </button>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部安全区域 -->\n    <view class=\"safe-area-bottom\"></view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue';\nimport { onShow } from '@dcloudio/uni-app';\n\n// 状态栏高度\nconst statusBarHeight = ref(20);\n\n// 联系历史和评价历史数据\nconst contactHistory = ref([]);\nconst ratingsHistory = ref([]);\n\n// 生命周期钩子\nonMounted(() => {\n  // 获取状态栏高度\n  const sysInfo = uni.getSystemInfoSync();\n  statusBarHeight.value = sysInfo.statusBarHeight || 20;\n  \n  loadContactHistory();\n  loadRatingsHistory();\n  \n  // 模拟一些测试数据\n  if (contactHistory.value.length === 0) {\n    contactHistory.value = [\n      {\n        driverId: '1001',\n        carpoolId: '2001',\n        phoneNumber: '13812345678',\n        startLocation: '磁县政府',\n        endLocation: '邯郸火车站',\n        departureDate: '2023-10-20',\n        departureTime: '09:30',\n        contactTime: new Date().toISOString()\n      },\n      {\n        driverId: '1002',\n        carpoolId: '2002',\n        phoneNumber: '13987654321',\n        startLocation: '磁县老城区',\n        endLocation: '邯郸科技学院',\n        departureDate: '2023-10-21',\n        departureTime: '14:00',\n        contactTime: new Date(Date.now() - 86400000).toISOString() // 昨天\n      }\n    ];\n  }\n});\n\nonShow(() => {\n  // 页面显示时重新加载数据，以防评价后返回\n  loadContactHistory();\n  loadRatingsHistory();\n});\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n\n// 加载联系历史\nconst loadContactHistory = () => {\n  const history = uni.getStorageSync('contactHistory') || [];\n  // 按联系时间倒序排序\n  contactHistory.value = history.sort((a, b) => {\n    return new Date(b.contactTime) - new Date(a.contactTime);\n  });\n};\n\n// 加载评价历史\nconst loadRatingsHistory = () => {\n  ratingsHistory.value = uni.getStorageSync('ratingsHistory') || [];\n};\n\n// 获取司机姓名\nconst getDriverName = (item) => {\n  const driversInfo = uni.getStorageSync('driversInfo') || {};\n  if (driversInfo[item.driverId]) {\n    return driversInfo[item.driverId].name;\n  }\n  return '司机' + item.phoneNumber.substr(-4);\n};\n\n// 格式化电话号码\nconst formatPhone = (phone) => {\n  if (!phone) return '';\n  return phone.replace(/(\\d{3})(\\d{4})(\\d{4})/, '$1 $2 $3');\n};\n\n// 格式化日期\nconst formatDate = (dateString) => {\n  const date = new Date(dateString);\n  const now = new Date();\n  const diffDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));\n  \n  if (diffDays === 0) {\n    // 今天，显示时间\n    const hours = date.getHours().toString().padStart(2, '0');\n    const minutes = date.getMinutes().toString().padStart(2, '0');\n    return `今天 ${hours}:${minutes}`;\n  } else if (diffDays === 1) {\n    return '昨天';\n  } else if (diffDays < 7) {\n    return `${diffDays}天前`;\n  } else {\n    const year = date.getFullYear();\n    const month = (date.getMonth() + 1).toString().padStart(2, '0');\n    const day = date.getDate().toString().padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n};\n\n// 再次联系司机\nconst callDriver = (item) => {\n  // 更新联系时间\n  const contactHistoryData = uni.getStorageSync('contactHistory') || [];\n  const index = contactHistoryData.findIndex(contact => \n    contact.driverId === item.driverId && \n    contact.carpoolId === item.carpoolId\n  );\n  \n  if (index !== -1) {\n    contactHistoryData[index].contactTime = new Date().toISOString();\n    uni.setStorageSync('contactHistory', contactHistoryData);\n  }\n  \n  // 拨打电话\n  uni.makePhoneCall({\n    phoneNumber: item.phoneNumber,\n    fail: (err) => {\n      console.error('拨打电话失败', err);\n      uni.showToast({\n        title: '拨打电话失败',\n        icon: 'none'\n      });\n    }\n  });\n};\n\n// 评价司机\nconst rateDriver = (item) => {\n  console.log('点击评价司机按钮', item);\n  // 确保所有参数都存在\n  if (!item.driverId || !item.phoneNumber) {\n    uni.showToast({\n      title: '缺少司机信息，无法评价',\n      icon: 'none'\n    });\n    return;\n  }\n  \n  // 使用正确的页面路径格式\n  const url = `/carpool-package/pages/carpool/my/create-rating?driverId=${item.driverId}&phoneNumber=${item.phoneNumber}&carpoolId=${item.carpoolId}&startLocation=${encodeURIComponent(item.startLocation)}&endLocation=${encodeURIComponent(item.endLocation)}&departureTime=${item.departureTime}&departureDate=${item.departureDate}`;\n  console.log('尝试跳转到路径:', url);\n  \n  uni.navigateTo({\n    url: url,\n    success: (res) => {\n      console.log('导航到评价页面成功');\n    },\n    fail: (err) => {\n      console.error('导航到评价页面失败', err);\n      // 尝试使用不同的路径格式作为备选\n      const alternativeUrl = `../create-rating?driverId=${item.driverId}&phoneNumber=${item.phoneNumber}&carpoolId=${item.carpoolId}&startLocation=${encodeURIComponent(item.startLocation)}&endLocation=${encodeURIComponent(item.endLocation)}&departureTime=${item.departureTime}&departureDate=${item.departureDate}`;\n      console.log('尝试备选路径:', alternativeUrl);\n      \n      uni.navigateTo({\n        url: alternativeUrl,\n        success: () => console.log('使用备选路径成功导航'),\n        fail: (error) => {\n          console.error('备选路径也失败', error);\n          uni.showToast({\n            title: '跳转评价页面失败',\n            icon: 'none'\n          });\n        }\n      });\n    }\n  });\n};\n\n// 检查是否已评价\nconst hasRated = (item) => {\n  return ratingsHistory.value.some(rating => \n    rating.driverId === item.driverId && \n    rating.carpoolId === item.carpoolId\n  );\n};\n</script>\n\n<style lang=\"scss\">\n.contact-history-container {\n  min-height: 100vh;\n  background-color: #F5F8FC;\n  position: relative;\n}\n\n/* 自定义导航栏 */\n.custom-header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background-color: #1677FF;\n}\n\n.header-content {\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 12px;\n}\n\n.left-action, .right-action {\n  width: 44px;\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.action-icon {\n  width: 24px;\n  height: 24px;\n}\n\n.back-icon {\n  width: 24px;\n  height: 24px;\n  /* 图标是黑色的，需要转为白色 */\n  filter: brightness(0) invert(1);\n}\n\n.title-area {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.page-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #FFFFFF;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\n}\n\n/* 空状态 */\n.empty-state {\n  padding: 100rpx 0;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.empty-icon {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 20rpx;\n}\n\n.empty-text {\n  font-size: 28rpx;\n  color: #8E8E93;\n}\n\n/* 历史列表 */\n.history-list {\n  position: relative;\n  padding: 20rpx 32rpx;\n}\n\n.history-item {\n  background-color: #FFFFFF;\n  border-radius: 16rpx;\n  padding: 24rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.history-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.driver-info {\n  display: flex;\n  flex-direction: column;\n}\n\n.driver-name {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #333333;\n  margin-bottom: 4rpx;\n}\n\n.driver-phone {\n  font-size: 24rpx;\n  color: #666666;\n}\n\n.contact-time {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n/* 行程信息 */\n.trip-info {\n  background-color: #F9F9F9;\n  border-radius: 12rpx;\n  padding: 16rpx;\n  margin-bottom: 16rpx;\n}\n\n.route-info {\n  margin-bottom: 12rpx;\n}\n\n.route-point {\n  display: flex;\n  align-items: center;\n  margin-bottom: 12rpx;\n}\n\n.point {\n  width: 16rpx;\n  height: 16rpx;\n  border-radius: 8rpx;\n  margin-right: 12rpx;\n}\n\n.point.start {\n  background-color: #34C759;\n}\n\n.point.end {\n  background-color: #FF3B30;\n}\n\n.point-text {\n  font-size: 26rpx;\n  color: #333333;\n}\n\n.route-line {\n  width: 2rpx;\n  height: 30rpx;\n  background-color: #DDDDDD;\n  margin-left: 7rpx;\n  margin-bottom: 12rpx;\n}\n\n.time-info {\n  display: flex;\n  justify-content: space-between;\n}\n\n.departure-date, .departure-time {\n  font-size: 24rpx;\n  color: #666666;\n}\n\n/* 操作按钮 */\n.action-buttons {\n  display: flex;\n  justify-content: flex-end;\n}\n\n.action-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 12rpx 24rpx;\n  border-radius: 30rpx;\n  margin-left: 16rpx;\n  font-size: 24rpx;\n}\n\n.action-btn.call {\n  background-color: #F2F7FD;\n  color: #0A84FF;\n  border: 1px solid #0A84FF;\n}\n\n.action-btn.rate {\n  background-color: #0A84FF;\n  color: #FFFFFF;\n}\n\n.btn-icon {\n  width: 24rpx;\n  height: 24rpx;\n  margin-right: 8rpx;\n}\n\n.action-btn.rate .btn-icon {\n  filter: brightness(0) invert(1);\n}\n\n.safe-area-bottom {\n  height: env(safe-area-inset-bottom);\n  width: 100%;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/carpool-package/pages/carpool/my/contact-history.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni", "onShow", "MiniProgramPage"], "mappings": ";;;;;;AA0EA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAG9B,UAAM,iBAAiBA,cAAAA,IAAI,CAAA,CAAE;AAC7B,UAAM,iBAAiBA,cAAAA,IAAI,CAAA,CAAE;AAG7BC,kBAAAA,UAAU,MAAM;AAEd,YAAM,UAAUC,oBAAI;AACpB,sBAAgB,QAAQ,QAAQ,mBAAmB;AAEnD;AACA;AAGA,UAAI,eAAe,MAAM,WAAW,GAAG;AACrC,uBAAe,QAAQ;AAAA,UACrB;AAAA,YACE,UAAU;AAAA,YACV,WAAW;AAAA,YACX,aAAa;AAAA,YACb,eAAe;AAAA,YACf,aAAa;AAAA,YACb,eAAe;AAAA,YACf,eAAe;AAAA,YACf,cAAa,oBAAI,KAAM,GAAC,YAAa;AAAA,UACtC;AAAA,UACD;AAAA,YACE,UAAU;AAAA,YACV,WAAW;AAAA,YACX,aAAa;AAAA,YACb,eAAe;AAAA,YACf,aAAa;AAAA,YACb,eAAe;AAAA,YACf,eAAe;AAAA,YACf,aAAa,IAAI,KAAK,KAAK,IAAG,IAAK,KAAQ,EAAE,YAAa;AAAA;AAAA,UAC3D;AAAA,QACP;AAAA,MACG;AAAA,IACH,CAAC;AAEDC,kBAAAA,OAAO,MAAM;AAEX;AACA;IACF,CAAC;AAGD,UAAM,SAAS,MAAM;AACnBD,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,qBAAqB,MAAM;AAC/B,YAAM,UAAUA,cAAG,MAAC,eAAe,gBAAgB,KAAK,CAAA;AAExD,qBAAe,QAAQ,QAAQ,KAAK,CAAC,GAAG,MAAM;AAC5C,eAAO,IAAI,KAAK,EAAE,WAAW,IAAI,IAAI,KAAK,EAAE,WAAW;AAAA,MAC3D,CAAG;AAAA,IACH;AAGA,UAAM,qBAAqB,MAAM;AAC/B,qBAAe,QAAQA,cAAG,MAAC,eAAe,gBAAgB,KAAK,CAAA;AAAA,IACjE;AAGA,UAAM,gBAAgB,CAAC,SAAS;AAC9B,YAAM,cAAcA,cAAG,MAAC,eAAe,aAAa,KAAK,CAAA;AACzD,UAAI,YAAY,KAAK,QAAQ,GAAG;AAC9B,eAAO,YAAY,KAAK,QAAQ,EAAE;AAAA,MACnC;AACD,aAAO,OAAO,KAAK,YAAY,OAAO,EAAE;AAAA,IAC1C;AAGA,UAAM,cAAc,CAAC,UAAU;AAC7B,UAAI,CAAC;AAAO,eAAO;AACnB,aAAO,MAAM,QAAQ,yBAAyB,UAAU;AAAA,IAC1D;AAGA,UAAM,aAAa,CAAC,eAAe;AACjC,YAAM,OAAO,IAAI,KAAK,UAAU;AAChC,YAAM,MAAM,oBAAI;AAChB,YAAM,WAAW,KAAK,OAAO,MAAM,SAAS,MAAO,KAAK,KAAK,GAAG;AAEhE,UAAI,aAAa,GAAG;AAElB,cAAM,QAAQ,KAAK,SAAU,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACxD,cAAM,UAAU,KAAK,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC5D,eAAO,MAAM,KAAK,IAAI,OAAO;AAAA,MACjC,WAAa,aAAa,GAAG;AACzB,eAAO;AAAA,MACX,WAAa,WAAW,GAAG;AACvB,eAAO,GAAG,QAAQ;AAAA,MACtB,OAAS;AACL,cAAM,OAAO,KAAK;AAClB,cAAM,SAAS,KAAK,aAAa,GAAG,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC9D,cAAM,MAAM,KAAK,QAAS,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACrD,eAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,MAC/B;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,SAAS;AAE3B,YAAM,qBAAqBA,cAAG,MAAC,eAAe,gBAAgB,KAAK,CAAA;AACnE,YAAM,QAAQ,mBAAmB;AAAA,QAAU,aACzC,QAAQ,aAAa,KAAK,YAC1B,QAAQ,cAAc,KAAK;AAAA,MAC/B;AAEE,UAAI,UAAU,IAAI;AAChB,2BAAmB,KAAK,EAAE,eAAc,oBAAI,KAAI,GAAG;AACnDA,sBAAAA,MAAI,eAAe,kBAAkB,kBAAkB;AAAA,MACxD;AAGDA,oBAAAA,MAAI,cAAc;AAAA,QAChB,aAAa,KAAK;AAAA,QAClB,MAAM,CAAC,QAAQ;AACbA,wBAAc,MAAA,MAAA,SAAA,+DAAA,UAAU,GAAG;AAC3BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,SAAS;AAC3BA,oBAAY,MAAA,MAAA,OAAA,+DAAA,YAAY,IAAI;AAE5B,UAAI,CAAC,KAAK,YAAY,CAAC,KAAK,aAAa;AACvCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAGD,YAAM,MAAM,4DAA4D,KAAK,QAAQ,gBAAgB,KAAK,WAAW,cAAc,KAAK,SAAS,kBAAkB,mBAAmB,KAAK,aAAa,CAAC,gBAAgB,mBAAmB,KAAK,WAAW,CAAC,kBAAkB,KAAK,aAAa,kBAAkB,KAAK,aAAa;AACrUA,oBAAY,MAAA,MAAA,OAAA,+DAAA,YAAY,GAAG;AAE3BA,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,QACA,SAAS,CAAC,QAAQ;AAChBA,wBAAAA,MAAA,MAAA,OAAA,+DAAY,WAAW;AAAA,QACxB;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAc,MAAA,MAAA,SAAA,+DAAA,aAAa,GAAG;AAE9B,gBAAM,iBAAiB,6BAA6B,KAAK,QAAQ,gBAAgB,KAAK,WAAW,cAAc,KAAK,SAAS,kBAAkB,mBAAmB,KAAK,aAAa,CAAC,gBAAgB,mBAAmB,KAAK,WAAW,CAAC,kBAAkB,KAAK,aAAa,kBAAkB,KAAK,aAAa;AACjTA,wBAAY,MAAA,MAAA,OAAA,+DAAA,WAAW,cAAc;AAErCA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,YACL,SAAS,MAAMA,cAAAA,MAAA,MAAA,OAAA,+DAAY,YAAY;AAAA,YACvC,MAAM,CAAC,UAAU;AACfA,4BAAc,MAAA,MAAA,SAAA,+DAAA,WAAW,KAAK;AAC9BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,CAAC,SAAS;AACzB,aAAO,eAAe,MAAM;AAAA,QAAK,YAC/B,OAAO,aAAa,KAAK,YACzB,OAAO,cAAc,KAAK;AAAA,MAC9B;AAAA,IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7PA,GAAG,WAAWE,SAAe;"}