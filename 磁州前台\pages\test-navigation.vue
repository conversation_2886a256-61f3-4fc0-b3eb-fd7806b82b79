<template>
  <view class="test-container">
    <view class="header">
      <text class="title">跳转测试页面</text>
    </view>
    
    <view class="button-group">
      <button class="test-button" @click="testNavigate('/pages/publish/info-detail')">
        跳转到信息详情页
      </button>
      <button class="test-button" @click="testNavigate('/pages/publish/publish')">
        跳转到发布页
      </button>
      <button class="test-button" @click="testNavigate('/pages/publish/detail')">
        跳转到发布详情页
      </button>
    </view>
    
    <view class="result-panel">
      <text class="result-title">测试结果:</text>
      <text class="result-content">{{result}}</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      result: '未执行测试'
    }
  },
  methods: {
    testNavigate(url) {
      this.result = `正在跳转到: ${url}`;
      
      uni.navigateTo({
        url,
        success: () => {
          this.result = `跳转成功: ${url}`;
        },
        fail: (err) => {
          this.result = `跳转失败: ${url}, 错误: ${JSON.stringify(err)}`;
        }
      });
    }
  }
}
</script>

<style>
.test-container {
  padding: 40rpx;
}

.header {
  margin-bottom: 40rpx;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.test-button {
  background-color: #1677FF;
  color: white;
  padding: 20rpx;
  border-radius: 8rpx;
}

.result-panel {
  padding: 20rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
}

.result-title {
  font-weight: bold;
  margin-bottom: 10rpx;
  display: block;
}

.result-content {
  font-family: monospace;
}
</style> 