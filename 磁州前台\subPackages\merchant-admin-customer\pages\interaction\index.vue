<template>
  <view class="interaction-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">客户互动管理</text>
      <view class="navbar-right">
        <view class="help-icon">?</view>
      </view>
    </view>
    
    <!-- 互动数据概览 -->
    <view class="overview-section">
      <view class="overview-card">
        <view class="overview-item">
          <text class="overview-value">{{interactionData.totalMessages}}</text>
          <text class="overview-label">消息总量</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{interactionData.unreadMessages}}</text>
          <text class="overview-label">未读消息</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{interactionData.responseRate}}%</text>
          <text class="overview-label">响应率</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{interactionData.avgResponseTime}}</text>
          <text class="overview-label">平均响应时间</text>
        </view>
      </view>
    </view>
    
    <!-- 互动管理模块 -->
    <view class="module-section">
      <view class="section-header">
        <text class="section-title">互动管理</text>
      </view>
      
      <view class="module-grid">
        <!-- 消息中心 -->
        <view class="module-card" @click="navigateTo('./message-center')">
          <view class="card-icon message-icon">💬</view>
          <text class="card-name">消息中心</text>
          <text class="card-desc">客户消息、系统通知</text>
          <view class="card-badge" v-if="interactionData.unreadMessages > 0">{{interactionData.unreadMessages}}</view>
          <view class="arrow-icon">›</view>
        </view>
        
        <!-- 客户咨询 -->
        <view class="module-card" @click="navigateTo('./customer-inquiry')">
          <view class="card-icon inquiry-icon">❓</view>
          <text class="card-name">客户咨询</text>
          <text class="card-desc">问题咨询、自助回复</text>
          <view class="card-badge" v-if="pendingInquiries > 0">{{pendingInquiries}}</view>
          <view class="arrow-icon">›</view>
        </view>
        
        <!-- 互动活动 -->
        <view class="module-card" @click="navigateTo('./activities')">
          <view class="card-icon activity-icon">🎯</view>
          <text class="card-name">互动活动</text>
          <text class="card-desc">投票、问卷、抽奖</text>
          <view class="arrow-icon">›</view>
        </view>
        
        <!-- 客户通知 -->
        <view class="module-card" @click="navigateTo('./notifications')">
          <view class="card-icon notification-icon">📢</view>
          <text class="card-name">客户通知</text>
          <text class="card-desc">活动提醒、优惠通知</text>
          <view class="arrow-icon">›</view>
        </view>
      </view>
    </view>
    
    <!-- 最近消息 -->
    <view class="recent-section">
      <view class="section-header">
        <text class="section-title">最近消息</text>
        <text class="section-more" @click="navigateTo('./message-center')">查看全部</text>
      </view>
      
      <view class="message-list">
        <view 
          v-for="(message, index) in recentMessages" 
          :key="index"
          class="message-item"
          :class="{'unread': !message.read}"
          @click="viewMessageDetail(message.id)">
          <image class="customer-avatar" :src="message.avatar" mode="aspectFill"></image>
          <view class="message-content">
            <view class="message-header">
              <text class="customer-name">{{message.customerName}}</text>
              <text class="message-time">{{message.time}}</text>
            </view>
            <text class="message-text">{{message.content}}</text>
          </view>
          <view class="unread-indicator" v-if="!message.read"></view>
        </view>
      </view>
    </view>
    
    <!-- 互动工具 -->
    <view class="tools-section">
      <view class="section-header">
        <text class="section-title">互动工具</text>
      </view>
      
      <view class="tools-list">
        <view class="tool-item" @click="navigateTo('./auto-reply')">
          <view class="tool-icon auto-reply-icon">🤖</view>
          <view class="tool-content">
            <text class="tool-name">自动回复设置</text>
            <text class="tool-desc">设置常见问题自动回复规则</text>
          </view>
          <view class="arrow-icon">›</view>
        </view>
        
        <view class="tool-item" @click="navigateTo('./templates')">
          <view class="tool-icon template-icon">📝</view>
          <view class="tool-content">
            <text class="tool-name">消息模板</text>
            <text class="tool-desc">常用回复模板管理</text>
          </view>
          <view class="arrow-icon">›</view>
        </view>
        
        <view class="tool-item" @click="navigateTo('./feedback')">
          <view class="tool-icon feedback-icon">📊</view>
          <view class="tool-content">
            <text class="tool-name">客户反馈管理</text>
            <text class="tool-desc">收集与分析客户反馈</text>
          </view>
          <view class="arrow-icon">›</view>
        </view>
        
        <view class="tool-item" @click="navigateTo('./birthday')">
          <view class="tool-icon birthday-icon">🎂</view>
          <view class="tool-content">
            <text class="tool-name">生日关怀</text>
            <text class="tool-desc">生日祝福自动发送</text>
          </view>
          <view class="arrow-icon">›</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      interactionData: {
        totalMessages: '256',
        unreadMessages: '18',
        responseRate: '92.5',
        avgResponseTime: '15分钟'
      },
      pendingInquiries: 7,
      recentMessages: [
        {
          id: '1001',
          customerName: '张三',
          avatar: '/static/images/avatar-1.png',
          content: '请问我的订单什么时候能到货？订单号：CZ20230515001',
          time: '10分钟前',
          read: false
        },
        {
          id: '1002',
          customerName: '李四',
          avatar: '/static/images/avatar-2.png',
          content: '你们店里的水果新鲜吗？想买一些送人',
          time: '30分钟前',
          read: false
        },
        {
          id: '1003',
          customerName: '王五',
          avatar: '/static/images/avatar-3.png',
          content: '请问你们有没有提供蔬菜配送服务？',
          time: '2小时前',
          read: true
        },
        {
          id: '1004',
          customerName: '赵六',
          avatar: '/static/images/avatar-4.png',
          content: '我的会员积分怎么查询？',
          time: '昨天',
          read: true
        }
      ]
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    navigateTo(url) {
      uni.navigateTo({ url });
    },
    viewMessageDetail(id) {
      uni.navigateTo({
        url: `./message-detail?id=${id}`
      });
    }
  }
}
</script>

<style>
.interaction-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 20px;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}

.overview-section {
  padding: 16px;
}

.overview-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.overview-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.overview-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 12px;
  color: #999;
}

.module-section {
  padding: 0 16px;
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-more {
  font-size: 12px;
  color: #1677FF;
}

.module-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.module-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-bottom: 12px;
}

.message-icon {
  background-color: #e6f7ff;
  color: #1677FF;
}

.inquiry-icon {
  background-color: #fff7e6;
  color: #fa8c16;
}

.activity-icon {
  background-color: #f6ffed;
  color: #52c41a;
}

.notification-icon {
  background-color: #fff1f0;
  color: #f5222d;
}

.card-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.card-desc {
  font-size: 12px;
  color: #999;
  text-align: center;
}

.card-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  min-width: 18px;
  height: 18px;
  border-radius: 9px;
  background-color: #ff4d4f;
  color: #fff;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 5px;
}

.arrow-icon {
  position: absolute;
  right: 12px;
  top: 12px;
  font-size: 20px;
  color: #ccc;
}

.recent-section {
  padding: 0 16px;
  margin-bottom: 20px;
}

.message-list {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  overflow: hidden;
}

.message-item {
  display: flex;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.message-item:last-child {
  border-bottom: none;
}

.message-item.unread {
  background-color: #f6f9fe;
}

.customer-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 12px;
  background-color: #f0f0f0;
}

.message-content {
  flex: 1;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.customer-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.message-time {
  font-size: 12px;
  color: #999;
}

.message-text {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.unread-indicator {
  position: absolute;
  top: 16px;
  left: 4px;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background-color: #ff4d4f;
}

.tools-section {
  padding: 0 16px;
}

.tools-list {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  overflow: hidden;
}

.tool-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.tool-item:last-child {
  border-bottom: none;
}

.tool-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin-right: 12px;
}

.auto-reply-icon {
  background-color: #e6f7ff;
  color: #1677FF;
}

.template-icon {
  background-color: #f6ffed;
  color: #52c41a;
}

.feedback-icon {
  background-color: #fff7e6;
  color: #fa8c16;
}

.birthday-icon {
  background-color: #fff1f0;
  color: #f5222d;
}

.tool-content {
  flex: 1;
}

.tool-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.tool-desc {
  font-size: 12px;
  color: #999;
}
</style>