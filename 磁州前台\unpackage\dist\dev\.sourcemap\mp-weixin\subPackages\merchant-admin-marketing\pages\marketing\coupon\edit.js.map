{"version": 3, "file": "edit.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/coupon/edit.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xjb3Vwb25cZWRpdC52dWU"], "sourcesContent": ["<template>\n  <view class=\"coupon-edit-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @tap=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">编辑优惠券</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @tap=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 表单容器 -->\n    <scroll-view scroll-y class=\"form-container\">\n      <!-- 基本信息 -->\n      <view class=\"form-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">基本信息</text>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"item-label\">优惠券名称</text>\n          <input type=\"text\" class=\"item-input\" v-model=\"couponForm.title\" placeholder=\"请输入优惠券名称\" />\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"item-label\">优惠券类型</text>\n          <view class=\"item-selector\" @tap=\"showCouponTypeSelector\">\n            <text class=\"selector-value\">{{couponForm.typeText}}</text>\n            <view class=\"selector-arrow\"></view>\n          </view>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"item-label\">优惠金额</text>\n          <view class=\"amount-input-wrapper\">\n            <text class=\"amount-symbol\">¥</text>\n            <input type=\"digit\" class=\"amount-input\" v-model=\"couponForm.value\" placeholder=\"请输入优惠金额\" />\n          </view>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"item-label\">使用门槛</text>\n          <view class=\"amount-input-wrapper\">\n            <text class=\"threshold-text\">满</text>\n            <input type=\"digit\" class=\"amount-input\" v-model=\"couponForm.minSpend\" placeholder=\"请输入最低消费金额\" />\n            <text class=\"threshold-text\">元可用</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 有效期设置 -->\n      <view class=\"form-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">有效期设置</text>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"item-label\">有效期类型</text>\n          <view class=\"validity-type-selector\">\n            <view \n              class=\"type-option\" \n              :class=\"{'active': validityType === 'fixed'}\"\n              @tap=\"setValidityType('fixed')\">\n              <view class=\"option-radio\">\n                <view class=\"radio-inner\" v-if=\"validityType === 'fixed'\"></view>\n              </view>\n              <text class=\"option-text\">固定日期</text>\n            </view>\n            \n            <view \n              class=\"type-option\" \n              :class=\"{'active': validityType === 'dynamic'}\"\n              @tap=\"setValidityType('dynamic')\">\n              <view class=\"option-radio\">\n                <view class=\"radio-inner\" v-if=\"validityType === 'dynamic'\"></view>\n              </view>\n              <text class=\"option-text\">领取后生效</text>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"form-item\" v-if=\"validityType === 'fixed'\">\n          <text class=\"item-label\">开始日期</text>\n          <view class=\"date-selector\" @tap=\"showStartDatePicker\">\n            <text class=\"date-value\">{{couponForm.startDate}}</text>\n            <view class=\"date-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                <rect x=\"3\" y=\"4\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"></rect>\n                <line x1=\"16\" y1=\"2\" x2=\"16\" y2=\"6\"></line>\n                <line x1=\"8\" y1=\"2\" x2=\"8\" y2=\"6\"></line>\n                <line x1=\"3\" y1=\"10\" x2=\"21\" y2=\"10\"></line>\n              </svg>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"form-item\" v-if=\"validityType === 'fixed'\">\n          <text class=\"item-label\">结束日期</text>\n          <view class=\"date-selector\" @tap=\"showEndDatePicker\">\n            <text class=\"date-value\">{{couponForm.expireDate}}</text>\n            <view class=\"date-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                <rect x=\"3\" y=\"4\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"></rect>\n                <line x1=\"16\" y1=\"2\" x2=\"16\" y2=\"6\"></line>\n                <line x1=\"8\" y1=\"2\" x2=\"8\" y2=\"6\"></line>\n                <line x1=\"3\" y1=\"10\" x2=\"21\" y2=\"10\"></line>\n              </svg>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"form-item\" v-if=\"validityType === 'dynamic'\">\n          <text class=\"item-label\">有效天数</text>\n          <view class=\"days-input-wrapper\">\n            <input type=\"number\" class=\"days-input\" v-model=\"couponForm.validDays\" placeholder=\"请输入有效天数\" />\n            <text class=\"days-text\">天</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 发放设置 -->\n      <view class=\"form-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">发放设置</text>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"item-label\">发放总量</text>\n          <view class=\"amount-input-wrapper\">\n            <input type=\"number\" class=\"amount-input\" v-model=\"couponForm.totalCount\" placeholder=\"请输入发放总量\" />\n            <text class=\"amount-unit\">张</text>\n          </view>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"item-label\">每人限领</text>\n          <view class=\"amount-input-wrapper\">\n            <input type=\"number\" class=\"amount-input\" v-model=\"couponForm.perPersonLimit\" placeholder=\"请输入每人限领数量\" />\n            <text class=\"amount-unit\">张</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 使用规则 -->\n      <view class=\"form-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">使用规则</text>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"item-label\">适用商品</text>\n          <view class=\"product-selector\" @tap=\"showProductSelector\">\n            <text class=\"selector-value\">{{couponForm.applicableProducts}}</text>\n            <view class=\"selector-arrow\"></view>\n          </view>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"item-label\">使用说明</text>\n          <textarea class=\"item-textarea\" v-model=\"couponForm.instructions\" placeholder=\"请输入使用说明\" />\n        </view>\n      </view>\n    </scroll-view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"bottom-action-bar\">\n      <view class=\"action-button cancel\" @tap=\"goBack\">\n        <text class=\"button-text\">取消</text>\n      </view>\n      <view class=\"action-button save\" @tap=\"saveCoupon\">\n        <text class=\"button-text\">保存</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { ref, reactive, onMounted } from 'vue';\n\nexport default {\n  setup() {\n    // 响应式状态\n    const couponId = ref('');\n    const couponForm = reactive({\n      id: '',\n      title: '',\n      type: 'discount',\n      typeText: '满减券',\n      value: '',\n      minSpend: '',\n      startDate: formatDate(new Date()),\n      expireDate: formatDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)), // 30天后\n      validDays: '30',\n      totalCount: '',\n      perPersonLimit: '1',\n      applicableProducts: '全部商品',\n      instructions: ''\n    });\n    \n    const validityType = ref('fixed');\n    \n    // 方法\n    function goBack() {\n      uni.navigateBack();\n    }\n    \n    function showHelp() {\n      uni.showModal({\n        title: '帮助信息',\n        content: '编辑优惠券页面可以修改优惠券的各项参数，包括名称、金额、有效期等。保存后将立即生效。',\n        showCancel: false\n      });\n    }\n    \n    function showCouponTypeSelector() {\n      uni.showActionSheet({\n        itemList: ['满减券', '折扣券', '无门槛券'],\n        success: (res) => {\n          const types = ['discount', 'percent', 'free'];\n          const texts = ['满减券', '折扣券', '无门槛券'];\n          \n          couponForm.type = types[res.tapIndex];\n          couponForm.typeText = texts[res.tapIndex];\n        }\n      });\n    }\n    \n    function setValidityType(type) {\n      validityType.value = type;\n    }\n    \n    function showStartDatePicker() {\n      uni.showDatePicker({\n        current: couponForm.startDate,\n        success: (res) => {\n          couponForm.startDate = res.date;\n        }\n      });\n    }\n    \n    function showEndDatePicker() {\n      uni.showDatePicker({\n        current: couponForm.expireDate,\n        success: (res) => {\n          couponForm.expireDate = res.date;\n        }\n      });\n    }\n    \n    function showProductSelector() {\n      uni.showActionSheet({\n        itemList: ['全部商品', '指定商品', '指定分类'],\n        success: (res) => {\n          const options = ['全部商品', '指定商品', '指定分类'];\n          couponForm.applicableProducts = options[res.tapIndex];\n          \n          if (res.tapIndex > 0) {\n            // 如果选择了指定商品或分类，显示选择界面\n            uni.showToast({\n              title: '请在下一页选择',\n              icon: 'none'\n            });\n          }\n        }\n      });\n    }\n    \n    function saveCoupon() {\n      // 表单验证\n      if (!couponForm.title) {\n        return uni.showToast({\n          title: '请输入优惠券名称',\n          icon: 'none'\n        });\n      }\n      \n      if (!couponForm.value) {\n        return uni.showToast({\n          title: '请输入优惠金额',\n          icon: 'none'\n        });\n      }\n      \n      if (couponForm.type !== 'free' && !couponForm.minSpend) {\n        return uni.showToast({\n          title: '请输入使用门槛',\n          icon: 'none'\n        });\n      }\n      \n      if (!couponForm.totalCount) {\n        return uni.showToast({\n          title: '请输入发放总量',\n          icon: 'none'\n        });\n      }\n      \n      // 在实际应用中，这里应该调用API保存优惠券\n      \n      uni.showLoading({\n        title: '保存中...'\n      });\n      \n      setTimeout(() => {\n        uni.hideLoading();\n        \n        uni.showToast({\n          title: '保存成功',\n          icon: 'success'\n        });\n        \n        setTimeout(() => {\n          uni.navigateBack();\n        }, 1500);\n      }, 1000);\n    }\n    \n    function formatDate(date) {\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      return `${year}-${month}-${day}`;\n    }\n    \n    function loadCouponData(id) {\n      // 在实际应用中，这里应该调用API获取优惠券详情\n      couponId.value = id;\n      \n      // 模拟加载数据\n      uni.showLoading({\n        title: '加载中...'\n      });\n      \n      setTimeout(() => {\n        // 模拟数据\n        Object.assign(couponForm, {\n          id: id,\n          title: '新客专享优惠',\n          type: 'discount',\n          typeText: '满减券',\n          value: '10',\n          minSpend: '100',\n          startDate: '2023-04-15',\n          expireDate: '2023-05-15',\n          validDays: '30',\n          totalCount: '500',\n          perPersonLimit: '1',\n          applicableProducts: '全部商品',\n          instructions: '仅限新用户使用，不可与其他优惠同时使用'\n        });\n        \n        uni.hideLoading();\n      }, 500);\n    }\n    \n    onMounted(() => {\n      const pages = getCurrentPages();\n      const currentPage = pages[pages.length - 1];\n      const options = currentPage.$page?.options || {};\n      \n      if (options.id) {\n        loadCouponData(options.id);\n      }\n    });\n    \n    return {\n      couponForm,\n      validityType,\n      goBack,\n      showHelp,\n      showCouponTypeSelector,\n      setValidityType,\n      showStartDatePicker,\n      showEndDatePicker,\n      showProductSelector,\n      saveCoupon\n    };\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.coupon-edit-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: 80px; /* 为底部操作栏留出空间 */\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #FF9966, #FF5E62);\n  color: #fff;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(255, 94, 98, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: bold;\n}\n\n/* 表单容器样式 */\n.form-container {\n  height: calc(100vh - 80px - 44px - 15px - 15px);\n}\n\n.form-section {\n  margin: 15px;\n  background: #fff;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n}\n\n.section-header {\n  margin-bottom: 15px;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.form-item {\n  margin-bottom: 15px;\n}\n\n.form-item:last-child {\n  margin-bottom: 0;\n}\n\n.item-label {\n  font-size: 14px;\n  color: #666;\n  margin-bottom: 8px;\n  display: block;\n}\n\n.item-input {\n  width: 100%;\n  height: 44px;\n  background: #F5F7FA;\n  border-radius: 8px;\n  padding: 0 12px;\n  font-size: 14px;\n  color: #333;\n  box-sizing: border-box;\n}\n\n.item-selector {\n  width: 100%;\n  height: 44px;\n  background: #F5F7FA;\n  border-radius: 8px;\n  padding: 0 12px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.selector-value {\n  font-size: 14px;\n  color: #333;\n}\n\n.selector-arrow {\n  width: 10px;\n  height: 10px;\n  border-right: 2px solid #999;\n  border-bottom: 2px solid #999;\n  transform: rotate(45deg);\n}\n\n.amount-input-wrapper {\n  display: flex;\n  align-items: center;\n  background: #F5F7FA;\n  border-radius: 8px;\n  padding: 0 12px;\n  height: 44px;\n}\n\n.amount-symbol {\n  font-size: 14px;\n  color: #333;\n  margin-right: 5px;\n}\n\n.amount-input {\n  flex: 1;\n  height: 44px;\n  font-size: 14px;\n  color: #333;\n  background: transparent;\n}\n\n.amount-unit {\n  font-size: 14px;\n  color: #666;\n}\n\n.threshold-text {\n  font-size: 14px;\n  color: #666;\n  margin: 0 5px;\n}\n\n.validity-type-selector {\n  display: flex;\n}\n\n.type-option {\n  display: flex;\n  align-items: center;\n  margin-right: 20px;\n}\n\n.option-radio {\n  width: 18px;\n  height: 18px;\n  border-radius: 9px;\n  border: 1px solid #ddd;\n  margin-right: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.radio-inner {\n  width: 10px;\n  height: 10px;\n  border-radius: 5px;\n  background: #FF5E62;\n}\n\n.option-text {\n  font-size: 14px;\n  color: #333;\n}\n\n.type-option.active .option-text {\n  color: #FF5E62;\n}\n\n.date-selector {\n  width: 100%;\n  height: 44px;\n  background: #F5F7FA;\n  border-radius: 8px;\n  padding: 0 12px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.date-value {\n  font-size: 14px;\n  color: #333;\n}\n\n.date-icon {\n  width: 20px;\n  height: 20px;\n  color: #999;\n}\n\n.days-input-wrapper {\n  display: flex;\n  align-items: center;\n  background: #F5F7FA;\n  border-radius: 8px;\n  padding: 0 12px;\n  height: 44px;\n}\n\n.days-input {\n  flex: 1;\n  height: 44px;\n  font-size: 14px;\n  color: #333;\n  background: transparent;\n}\n\n.days-text {\n  font-size: 14px;\n  color: #666;\n}\n\n.product-selector {\n  width: 100%;\n  height: 44px;\n  background: #F5F7FA;\n  border-radius: 8px;\n  padding: 0 12px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.item-textarea {\n  width: 100%;\n  height: 100px;\n  background: #F5F7FA;\n  border-radius: 8px;\n  padding: 12px;\n  font-size: 14px;\n  color: #333;\n  box-sizing: border-box;\n}\n\n/* 底部操作栏样式 */\n.bottom-action-bar {\n  position: fixed;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: #fff;\n  display: flex;\n  padding: 15px;\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);\n  z-index: 90;\n}\n\n.action-button {\n  flex: 1;\n  height: 44px;\n  border-radius: 22px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 0 5px;\n}\n\n.action-button.cancel {\n  background: #F5F7FA;\n}\n\n.action-button.save {\n  background: linear-gradient(135deg, #FF9966, #FF5E62);\n}\n\n.button-text {\n  font-size: 16px;\n  font-weight: 500;\n}\n\n.action-button.cancel .button-text {\n  color: #666;\n}\n\n.action-button.save .button-text {\n  color: #fff;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/coupon/edit.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "uni", "onMounted"], "mappings": ";;AAqLA,MAAK,YAAU;AAAA,EACb,QAAQ;AAEN,UAAM,WAAWA,kBAAI,EAAE;AACvB,UAAM,aAAaC,cAAAA,SAAS;AAAA,MAC1B,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,MACV,WAAW,WAAW,oBAAI,MAAM;AAAA,MAChC,YAAY,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,CAAC;AAAA;AAAA,MACtE,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,MACpB,cAAc;AAAA,IAChB,CAAC;AAED,UAAM,eAAeD,kBAAI,OAAO;AAGhC,aAAS,SAAS;AAChBE,oBAAG,MAAC,aAAY;AAAA,IAClB;AAEA,aAAS,WAAW;AAClBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MACd,CAAC;AAAA,IACH;AAEA,aAAS,yBAAyB;AAChCA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,OAAO,OAAO,MAAM;AAAA,QAC/B,SAAS,CAAC,QAAQ;AAChB,gBAAM,QAAQ,CAAC,YAAY,WAAW,MAAM;AAC5C,gBAAM,QAAQ,CAAC,OAAO,OAAO,MAAM;AAEnC,qBAAW,OAAO,MAAM,IAAI,QAAQ;AACpC,qBAAW,WAAW,MAAM,IAAI,QAAQ;AAAA,QAC1C;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,gBAAgB,MAAM;AAC7B,mBAAa,QAAQ;AAAA,IACvB;AAEA,aAAS,sBAAsB;AAC7BA,oBAAAA,MAAI,eAAe;AAAA,QACjB,SAAS,WAAW;AAAA,QACpB,SAAS,CAAC,QAAQ;AAChB,qBAAW,YAAY,IAAI;AAAA,QAC7B;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,oBAAoB;AAC3BA,oBAAAA,MAAI,eAAe;AAAA,QACjB,SAAS,WAAW;AAAA,QACpB,SAAS,CAAC,QAAQ;AAChB,qBAAW,aAAa,IAAI;AAAA,QAC9B;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,sBAAsB;AAC7BA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,QAAQ,QAAQ,MAAM;AAAA,QACjC,SAAS,CAAC,QAAQ;AAChB,gBAAM,UAAU,CAAC,QAAQ,QAAQ,MAAM;AACvC,qBAAW,qBAAqB,QAAQ,IAAI,QAAQ;AAEpD,cAAI,IAAI,WAAW,GAAG;AAEpBA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,aAAa;AAEpB,UAAI,CAAC,WAAW,OAAO;AACrB,eAAOA,cAAAA,MAAI,UAAU;AAAA,UACnB,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAEA,UAAI,CAAC,WAAW,OAAO;AACrB,eAAOA,cAAAA,MAAI,UAAU;AAAA,UACnB,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAEA,UAAI,WAAW,SAAS,UAAU,CAAC,WAAW,UAAU;AACtD,eAAOA,cAAAA,MAAI,UAAU;AAAA,UACnB,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAEA,UAAI,CAAC,WAAW,YAAY;AAC1B,eAAOA,cAAAA,MAAI,UAAU;AAAA,UACnB,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAIAA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAEfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAED,mBAAW,MAAM;AACfA,wBAAG,MAAC,aAAY;AAAA,QACjB,GAAE,IAAI;AAAA,MACR,GAAE,GAAI;AAAA,IACT;AAEA,aAAS,WAAW,MAAM;AACxB,YAAM,OAAO,KAAK;AAClB,YAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,YAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAChC;AAEA,aAAS,eAAe,IAAI;AAE1B,eAAS,QAAQ;AAGjBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAED,iBAAW,MAAM;AAEf,eAAO,OAAO,YAAY;AAAA,UACxB;AAAA,UACA,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,UACV,OAAO;AAAA,UACP,UAAU;AAAA,UACV,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,gBAAgB;AAAA,UAChB,oBAAoB;AAAA,UACpB,cAAc;AAAA,QAChB,CAAC;AAEDA,sBAAG,MAAC,YAAW;AAAA,MAChB,GAAE,GAAG;AAAA,IACR;AAEAC,kBAAAA,UAAU,MAAM;;AACd,YAAM,QAAQ;AACd,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,YAAU,iBAAY,UAAZ,mBAAmB,YAAW,CAAA;AAE9C,UAAI,QAAQ,IAAI;AACd,uBAAe,QAAQ,EAAE;AAAA,MAC3B;AAAA,IACF,CAAC;AAED,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;EAEJ;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3XA,GAAG,WAAW,eAAe;"}