{"version": 3, "file": "people-to-car.js", "sources": ["carpool-package/pages/carpool/publish/people-to-car.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/Y2FycG9vbC1wYWNrYWdlXHBhZ2VzXGNhcnBvb2xccHVibGlzaFxwZW9wbGUtdG8tY2FyLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"publish-container\">\n    <!-- 导航栏 -->\n    <carpool-nav title=\"发布人找车信息\"></carpool-nav>\n    \n    <view class=\"form-container\">\n      <view class=\"form-group\">\n        <view class=\"form-title\">行程信息</view>\n        \n        <view class=\"form-item\">\n          <text class=\"label\">出发地</text>\n          <view class=\"input-wrapper\">\n            <input type=\"text\" v-model=\"formData.startPoint\" placeholder=\"请输入出发地\" />\n            <view class=\"location-btn\" @click=\"chooseLocation('start')\">\n              <image src=\"/static/images/tabbar/location.png\" mode=\"aspectFit\"></image>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"label\">目的地</text>\n          <view class=\"input-wrapper\">\n            <input type=\"text\" v-model=\"formData.endPoint\" placeholder=\"请输入目的地\" />\n            <view class=\"location-btn\" @click=\"chooseLocation('end')\">\n              <image src=\"/static/images/tabbar/location.png\" mode=\"aspectFit\"></image>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"label\">途径地点</text>\n          <view class=\"via-points-container\">\n            <view v-for=\"(point, index) in formData.viaPoints\" :key=\"index\" class=\"via-point-item\">\n              <view class=\"input-wrapper\">\n                <input type=\"text\" v-model=\"formData.viaPoints[index]\" placeholder=\"请输入途径地点\" />\n                <view class=\"location-btn\" @click=\"chooseLocation('via', index)\">\n                  <image src=\"/static/images/tabbar/location.png\" mode=\"aspectFit\"></image>\n                </view>\n              </view>\n              <view class=\"via-point-actions\">\n                <view class=\"via-point-delete\" @click=\"removeViaPoint(index)\">\n                  <text class=\"delete-icon\">×</text>\n                </view>\n              </view>\n            </view>\n            \n            <view class=\"add-via-point\" @click=\"addViaPoint\" v-if=\"formData.viaPoints.length < 3\">\n              <text class=\"add-icon\">+</text>\n              <text class=\"add-text\">添加途径地点</text>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"label\">出发日期</text>\n          <picker mode=\"date\" :value=\"formData.departureDate\" start=\"2023-01-01\" end=\"2030-12-31\" @change=\"onDateChange\">\n            <view class=\"picker-value\">\n              <text>{{formData.departureDate || '请选择出发日期'}}</text>\n              <image src=\"/static/images/tabbar/arrow-right.png\" mode=\"aspectFit\"></image>\n            </view>\n          </picker>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"label\">出发时间</text>\n          <picker mode=\"time\" :value=\"formData.departureTime\" @change=\"onTimeChange\">\n            <view class=\"picker-value\">\n              <text>{{formData.departureTime || '请选择出发时间'}}</text>\n              <image src=\"/static/images/tabbar/arrow-right.png\" mode=\"aspectFit\"></image>\n            </view>\n          </picker>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"label\">乘车人数</text>\n          <picker mode=\"selector\" :range=\"passengerOptions\" :value=\"passengerIndex\" @change=\"onPassengerChange\">\n            <view class=\"picker-value\">\n              <text>{{formData.passengers || '请选择乘车人数'}}</text>\n              <image src=\"/static/images/tabbar/arrow-right.png\" mode=\"aspectFit\"></image>\n            </view>\n          </picker>\n        </view>\n      </view>\n      \n      <view class=\"form-group\">\n        <view class=\"form-title\">联系方式</view>\n        \n        <view class=\"form-item\">\n          <text class=\"label\">联系人</text>\n          <input type=\"text\" v-model=\"formData.contactName\" placeholder=\"请输入联系人姓名（选填）\" />\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"label\">手机号码</text>\n          <input type=\"number\" v-model=\"formData.contactPhone\" placeholder=\"请输入手机号码\" maxlength=\"11\" />\n        </view>\n      </view>\n      \n      <view class=\"form-group\">\n        <view class=\"form-title\">补充说明</view>\n        \n        <view class=\"form-item\">\n          <textarea v-model=\"formData.remark\" placeholder=\"请输入补充说明（选填）\" maxlength=\"200\" />\n          <view class=\"word-count\">{{formData.remark.length}}/200</view>\n        </view>\n      </view>\n    </view>\n    \n    <view class=\"submit-section\">\n      <view class=\"agreement\">\n        <checkbox-group @change=\"onAgreementChange\">\n          <label>\n            <checkbox :checked=\"formData.agreement\" color=\"#0A84FF\" />\n            <text>我已阅读并同意</text>\n            <text class=\"link\" @click.stop=\"viewAgreement\">《拼车服务协议》</text>\n          </label>\n        </checkbox-group>\n      </view>\n      \n      <!-- 发布方式选择 - 使用ConfigurablePremiumActions组件 -->\n      <view class=\"publish-options\">\n        <view class=\"options-title\">选择发布方式</view>\n        \n        <!-- 使用ConfigurablePremiumActions组件 -->\n        <ConfigurablePremiumActions\n          :pageType=\"'publish'\"\n          :showMode=\"'direct'\"\n          @action-completed=\"handleActionCompleted\"\n        />\n      </view>\n      \n      <button class=\"submit-btn\" :disabled=\"!formData.agreement\" @click=\"submitForm\">\n        发布信息\n      </button>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport CarpoolNav from '/components/carpool-nav.vue';\nimport { ref, onMounted } from 'vue';\nimport ConfigurablePremiumActions from '/components/premium/ConfigurablePremiumActions.vue';\n// 引入真实API\nimport api from '@/api/index.js';\n\n// 表单数据\nconst formData = ref({\n  startPoint: '',\n  endPoint: '',\n  departureDate: '',\n  departureTime: '',\n  passengers: '',\n  contactName: '',\n  contactPhone: '',\n  remark: '',\n  agreement: false,\n  viaPoints: []\n});\n\n// 乘车人数选项\nconst passengerOptions = ref(['1人', '2人', '3人', '4人', '5人', '6人']);\nconst passengerIndex = ref(0);\n\n// 发布模式\nconst publishMode = ref('ad'); // 默认为广告模式\n\n// 选择的选项\nconst selectedOption = ref(null);\n\n// 处理页面参数\nonMounted((options) => {\n  // 处理发布模式参数\n  if (options && options.mode) {\n    publishMode.value = options.mode;\n  }\n});\n\n// 选择位置\nconst chooseLocation = (type, index) => {\n  uni.chooseLocation({\n    success: (res) => {\n      if (type === 'start') {\n        formData.value.startPoint = res.name;\n      } else if (type === 'end') {\n        formData.value.endPoint = res.name;\n      } else if (type === 'via') {\n        if (index !== undefined) {\n          formData.value.viaPoints[index] = res.name;\n        } else {\n          formData.value.viaPoints.push(res.name);\n        }\n      }\n    }\n  });\n};\n\n// 日期选择\nconst onDateChange = (e) => {\n  formData.value.departureDate = e.detail.value;\n};\n\n// 时间选择\nconst onTimeChange = (e) => {\n  formData.value.departureTime = e.detail.value;\n};\n\n// 乘车人数选择\nconst onPassengerChange = (e) => {\n  passengerIndex.value = e.detail.value;\n  formData.value.passengers = passengerOptions.value[passengerIndex.value];\n};\n\n// 协议同意状态变更\nconst onAgreementChange = (e) => {\n  formData.value.agreement = e.detail.value.length > 0;\n};\n\n// 查看协议\nconst viewAgreement = () => {\n  uni.navigateTo({\n    url: '/pages/carpool/agreement'\n  });\n};\n\n// 提交表单\nconst submitForm = () => {\n  // 表单验证\n  if (!formData.value.startPoint) {\n    showToast('请输入出发地');\n    return;\n  }\n  if (!formData.value.endPoint) {\n    showToast('请输入目的地');\n    return;\n  }\n  if (!formData.value.departureDate) {\n    showToast('请选择出发日期');\n    return;\n  }\n  if (!formData.value.departureTime) {\n    showToast('请选择出发时间');\n    return;\n  }\n  if (!formData.value.passengers) {\n    showToast('请选择乘车人数');\n    return;\n  }\n  // 联系人姓名为选填，移除验证\n  if (!formData.value.contactPhone) {\n    showToast('请输入手机号码');\n    return;\n  }\n  if (!/^1\\d{10}$/.test(formData.value.contactPhone)) {\n    showToast('手机号码格式不正确');\n    return;\n  }\n  \n  // 使用选择的操作进行处理\n  if (!selectedOption.value) {\n    showToast('请选择发布方式');\n    return;\n  }\n  \n  // 表单直接提交到服务器\n  submitToServer();\n};\n\n// 处理广告发布\nconst handleAdPublish = () => {\n  // 显示广告\n  uni.showLoading({\n    title: '正在加载广告...'\n  });\n  \n  // 模拟广告加载\n  setTimeout(() => {\n    uni.hideLoading();\n    \n    // 模拟广告播放完成\n    uni.showModal({\n      title: '广告播放完成',\n      content: '感谢您观看广告，现在可以免费发布拼车信息',\n      showCancel: false,\n      success: () => {\n        submitToServer();\n      }\n    });\n  }, 1500);\n};\n\n// 处理付费发布\nconst handlePremiumPublish = () => {\n  uni.showModal({\n    title: '付费发布',\n    content: '您将支付5元获得置顶发布特权，是否继续？',\n    success: (res) => {\n      if (res.confirm) {\n        // 模拟支付过程\n        uni.showLoading({\n          title: '正在支付...'\n        });\n        \n        setTimeout(() => {\n          uni.hideLoading();\n          uni.showToast({\n            title: '支付成功',\n            icon: 'success'\n          });\n          \n          // 提交到服务器\n          submitToServer();\n        }, 1500);\n      }\n    }\n  });\n};\n\n// 提交到服务器 - 使用真实API\nconst submitToServer = async () => {\n  try {\n    // 显示加载状态\n    uni.showLoading({\n      title: '发布中...'\n    });\n\n    // 整理表单数据\n    const formDataToSubmit = {\n      type: 'people-to-car',\n      departure: formData.value.startPoint,\n      destination: formData.value.endPoint,\n      departure_time: `${formData.value.date} ${formData.value.time}`,\n      passenger_count: parseInt(passengerOptions.value[passengerIndex.value]),\n      price: parseFloat(formData.value.price) || 0,\n      contact: formData.value.contact,\n      description: formData.value.remark,\n      via_points: formData.value.viaPoints.filter(point => point.trim() !== ''),\n      publish_mode: publishMode.value\n    };\n\n    console.log('提交的表单数据：', formDataToSubmit);\n\n    // 调用真实API发布拼车信息\n    const result = await api.carpool.publish(formDataToSubmit);\n\n    if (result.success) {\n      // 发布成功，跳转到成功页面\n      uni.navigateTo({\n        url: `/carpool-package/pages/carpool/publish/success?id=${result.data.id}&type=people-to-car&mode=${publishMode.value}`\n      });\n    } else {\n      // 发布失败，显示错误信息\n      uni.showToast({\n        title: result.message || '发布失败，请重试',\n        icon: 'none'\n      });\n    }\n  } catch (error) {\n    console.error('发布拼车信息失败:', error);\n    uni.showToast({\n      title: '网络错误，请重试',\n      icon: 'none'\n    });\n  } finally {\n    uni.hideLoading();\n  }\n};\n\n// 显示提示\nconst showToast = (title) => {\n  uni.showToast({\n    title: title,\n    icon: 'none'\n  });\n};\n\n// 选择发布方式\nconst selectPublishMode = (mode) => {\n  publishMode.value = mode;\n};\n\n// 添加途径地点\nconst addViaPoint = () => {\n  formData.value.viaPoints.push('');\n};\n\n// 移除途径地点\nconst removeViaPoint = (index) => {\n  formData.value.viaPoints.splice(index, 1);\n};\n\n// 处理操作完成后的逻辑\nconst handleActionCompleted = (actionType, result) => {\n  // 保存选择的选项\n  selectedOption.value = result;\n  \n  if (actionType === 'ad') {\n    publishMode.value = 'ad';\n  } else if (actionType === 'paid') {\n    publishMode.value = 'premium';\n  }\n  \n  console.log('选择的推广选项:', actionType, result);\n};\n</script>\n\n<style lang=\"scss\">\n.publish-container {\n  min-height: 100vh;\n  background-color: #f5f7fa;\n  padding-bottom: 40rpx;\n}\n\n/* 自定义导航栏 */\n.custom-navbar {\n  display: flex;\n  align-items: center;\n  height: 120rpx;\n  padding: 0 30rpx;\n  background-color: #0A84FF;\n  position: relative;\n}\n\n.navbar-left {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 10;\n}\n\n.navbar-title {\n  position: absolute;\n  left: 0;\n  right: 0;\n  text-align: center;\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #ffffff;\n}\n\n/* 表单容器 */\n.form-container {\n  padding: 30rpx;\n}\n\n.form-group {\n  background-color: #ffffff;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n}\n\n.form-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333333;\n  margin-bottom: 20rpx;\n  padding-left: 20rpx;\n  border-left: 8rpx solid #0A84FF;\n}\n\n.form-item {\n  margin-bottom: 30rpx;\n}\n\n.label {\n  display: block;\n  font-size: 28rpx;\n  color: #666666;\n  margin-bottom: 10rpx;\n}\n\ninput, .picker-value {\n  width: 100%;\n  height: 80rpx;\n  background-color: #f8f8f8;\n  border-radius: 10rpx;\n  padding: 0 20rpx;\n  font-size: 28rpx;\n  color: #333333;\n  box-sizing: border-box;\n}\n\n.picker-value {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.picker-value image {\n  width: 40rpx;\n  height: 40rpx;\n}\n\n.input-wrapper {\n  position: relative;\n}\n\n.location-btn {\n  position: absolute;\n  right: 20rpx;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.location-btn image {\n  width: 40rpx;\n  height: 40rpx;\n}\n\ntextarea {\n  width: 100%;\n  height: 200rpx;\n  background-color: #f8f8f8;\n  border-radius: 10rpx;\n  padding: 20rpx;\n  font-size: 28rpx;\n  color: #333333;\n  box-sizing: border-box;\n}\n\n.word-count {\n  text-align: right;\n  font-size: 24rpx;\n  color: #999999;\n  margin-top: 10rpx;\n}\n\n/* 提交区域 */\n.submit-section {\n  padding: 0 30rpx;\n  margin-top: 40rpx;\n}\n\n.agreement {\n  margin-bottom: 30rpx;\n  font-size: 26rpx;\n  color: #666666;\n}\n\n.link {\n  color: #0A84FF;\n}\n\n.submit-btn {\n  height: 90rpx;\n  border-radius: 45rpx;\n  background-image: linear-gradient(135deg, #0A84FF, #0055B8);\n  color: #ffffff;\n  font-size: 32rpx;\n  font-weight: 600;\n  box-shadow: 0 8rpx 16rpx rgba(10, 132, 255, 0.3);\n}\n\n.submit-btn[disabled] {\n  background-image: linear-gradient(135deg, #cccccc, #999999);\n  box-shadow: none;\n  color: #ffffff;\n}\n\n/* 发布方式选择 */\n.publish-options {\n  margin-bottom: 30rpx;\n}\n\n.options-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333333;\n  margin-bottom: 20rpx;\n}\n\n.option-card {\n  background-color: #ffffff;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  position: relative;\n  transition: all 0.3s ease;\n}\n\n.option-card:active {\n  transform: translateY(-2rpx);\n  box-shadow: 0 6rpx 15rpx rgba(0, 0, 0, 0.08);\n}\n\n.option-selected {\n  border: 2rpx solid #0A84FF;\n  background-color: rgba(10, 132, 255, 0.05);\n}\n\n.option-icon {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 20rpx;\n  flex-shrink: 0;\n}\n\n.option-icon image {\n  width: 40rpx;\n  height: 40rpx;\n  filter: brightness(0) invert(1);\n}\n\n.ad-icon {\n  background: linear-gradient(135deg, #2c96ff, #5f65ff);\n}\n\n.premium-icon {\n  background: linear-gradient(135deg, #ff9500, #ff2d55);\n}\n\n.option-content {\n  flex: 1;\n}\n\n.option-name {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #333333;\n  margin-bottom: 6rpx;\n  display: flex;\n  align-items: center;\n}\n\n.option-tag {\n  font-size: 20rpx;\n  padding: 2rpx 10rpx;\n  border-radius: 10rpx;\n  margin-left: 10rpx;\n  color: #ffffff;\n}\n\n.recommend {\n  background-color: #1677FF;\n}\n\n.premium {\n  background-color: #ff9500;\n}\n\n.option-desc {\n  font-size: 24rpx;\n  color: #666666;\n}\n\n.option-checkbox {\n  width: 40rpx;\n  height: 40rpx;\n  border-radius: 50%;\n  border: 2rpx solid #dddddd;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-left: 20rpx;\n  flex-shrink: 0;\n}\n\n.option-selected .option-checkbox {\n  border-color: #0A84FF;\n}\n\n.checkbox-inner {\n  width: 20rpx;\n  height: 20rpx;\n  background-color: #0A84FF;\n  border-radius: 50%;\n}\n\n/* 途径地点容器 */\n.via-points-container {\n  margin-bottom: 30rpx;\n}\n\n.via-point-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10rpx;\n}\n\n.via-point-actions {\n  display: flex;\n  align-items: center;\n  margin-left: 10rpx;\n}\n\n.via-point-delete {\n  width: 20rpx;\n  height: 20rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n}\n\n.delete-icon {\n  color: #ff0000;\n}\n\n.add-via-point {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 80rpx;\n  background-color: #ffffff;\n  border-radius: 10rpx;\n  cursor: pointer;\n  margin-top: 10rpx;\n}\n\n.add-icon {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #0A84FF;\n  margin-right: 10rpx;\n}\n\n.add-text {\n  font-size: 28rpx;\n  color: #0A84FF;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/carpool-package/pages/carpool/publish/people-to-car.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni", "api", "MiniProgramPage"], "mappings": ";;;;;;;AA2IA,MAAM,aAAa,MAAW;AAE9B,MAAM,6BAA6B,MAAW;;;;AAK9C,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACnB,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,eAAe;AAAA,MACf,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,WAAW,CAAE;AAAA,IACf,CAAC;AAGD,UAAM,mBAAmBA,cAAAA,IAAI,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,CAAC;AACjE,UAAM,iBAAiBA,cAAAA,IAAI,CAAC;AAG5B,UAAM,cAAcA,cAAAA,IAAI,IAAI;AAG5B,UAAM,iBAAiBA,cAAAA,IAAI,IAAI;AAG/BC,kBAAS,UAAC,CAAC,YAAY;AAErB,UAAI,WAAW,QAAQ,MAAM;AAC3B,oBAAY,QAAQ,QAAQ;AAAA,MAC7B;AAAA,IACH,CAAC;AAGD,UAAM,iBAAiB,CAAC,MAAM,UAAU;AACtCC,oBAAAA,MAAI,eAAe;AAAA,QACjB,SAAS,CAAC,QAAQ;AAChB,cAAI,SAAS,SAAS;AACpB,qBAAS,MAAM,aAAa,IAAI;AAAA,UACxC,WAAiB,SAAS,OAAO;AACzB,qBAAS,MAAM,WAAW,IAAI;AAAA,UACtC,WAAiB,SAAS,OAAO;AACzB,gBAAI,UAAU,QAAW;AACvB,uBAAS,MAAM,UAAU,KAAK,IAAI,IAAI;AAAA,YAChD,OAAe;AACL,uBAAS,MAAM,UAAU,KAAK,IAAI,IAAI;AAAA,YACvC;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,eAAe,CAAC,MAAM;AAC1B,eAAS,MAAM,gBAAgB,EAAE,OAAO;AAAA,IAC1C;AAGA,UAAM,eAAe,CAAC,MAAM;AAC1B,eAAS,MAAM,gBAAgB,EAAE,OAAO;AAAA,IAC1C;AAGA,UAAM,oBAAoB,CAAC,MAAM;AAC/B,qBAAe,QAAQ,EAAE,OAAO;AAChC,eAAS,MAAM,aAAa,iBAAiB,MAAM,eAAe,KAAK;AAAA,IACzE;AAGA,UAAM,oBAAoB,CAAC,MAAM;AAC/B,eAAS,MAAM,YAAY,EAAE,OAAO,MAAM,SAAS;AAAA,IACrD;AAGA,UAAM,gBAAgB,MAAM;AAC1BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,aAAa,MAAM;AAEvB,UAAI,CAAC,SAAS,MAAM,YAAY;AAC9B,kBAAU,QAAQ;AAClB;AAAA,MACD;AACD,UAAI,CAAC,SAAS,MAAM,UAAU;AAC5B,kBAAU,QAAQ;AAClB;AAAA,MACD;AACD,UAAI,CAAC,SAAS,MAAM,eAAe;AACjC,kBAAU,SAAS;AACnB;AAAA,MACD;AACD,UAAI,CAAC,SAAS,MAAM,eAAe;AACjC,kBAAU,SAAS;AACnB;AAAA,MACD;AACD,UAAI,CAAC,SAAS,MAAM,YAAY;AAC9B,kBAAU,SAAS;AACnB;AAAA,MACD;AAED,UAAI,CAAC,SAAS,MAAM,cAAc;AAChC,kBAAU,SAAS;AACnB;AAAA,MACD;AACD,UAAI,CAAC,YAAY,KAAK,SAAS,MAAM,YAAY,GAAG;AAClD,kBAAU,WAAW;AACrB;AAAA,MACD;AAGD,UAAI,CAAC,eAAe,OAAO;AACzB,kBAAU,SAAS;AACnB;AAAA,MACD;AAGD;IACF;AAqDA,UAAM,iBAAiB,YAAY;AACjC,UAAI;AAEFA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,QACb,CAAK;AAGD,cAAM,mBAAmB;AAAA,UACvB,MAAM;AAAA,UACN,WAAW,SAAS,MAAM;AAAA,UAC1B,aAAa,SAAS,MAAM;AAAA,UAC5B,gBAAgB,GAAG,SAAS,MAAM,IAAI,IAAI,SAAS,MAAM,IAAI;AAAA,UAC7D,iBAAiB,SAAS,iBAAiB,MAAM,eAAe,KAAK,CAAC;AAAA,UACtE,OAAO,WAAW,SAAS,MAAM,KAAK,KAAK;AAAA,UAC3C,SAAS,SAAS,MAAM;AAAA,UACxB,aAAa,SAAS,MAAM;AAAA,UAC5B,YAAY,SAAS,MAAM,UAAU,OAAO,WAAS,MAAM,KAAM,MAAK,EAAE;AAAA,UACxE,cAAc,YAAY;AAAA,QAChC;AAEIA,sBAAA,MAAA,MAAA,OAAA,kEAAY,YAAY,gBAAgB;AAGxC,cAAM,SAAS,MAAMC,UAAG,IAAC,QAAQ,QAAQ,gBAAgB;AAEzD,YAAI,OAAO,SAAS;AAElBD,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK,qDAAqD,OAAO,KAAK,EAAE,4BAA4B,YAAY,KAAK;AAAA,UAC7H,CAAO;AAAA,QACP,OAAW;AAELA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,OAAO,WAAW;AAAA,YACzB,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,kEAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,UAAY;AACRA,sBAAG,MAAC,YAAW;AAAA,MAChB;AAAA,IACH;AAGA,UAAM,YAAY,CAAC,UAAU;AAC3BA,oBAAAA,MAAI,UAAU;AAAA,QACZ;AAAA,QACA,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAQA,UAAM,cAAc,MAAM;AACxB,eAAS,MAAM,UAAU,KAAK,EAAE;AAAA,IAClC;AAGA,UAAM,iBAAiB,CAAC,UAAU;AAChC,eAAS,MAAM,UAAU,OAAO,OAAO,CAAC;AAAA,IAC1C;AAGA,UAAM,wBAAwB,CAAC,YAAY,WAAW;AAEpD,qBAAe,QAAQ;AAEvB,UAAI,eAAe,MAAM;AACvB,oBAAY,QAAQ;AAAA,MACxB,WAAa,eAAe,QAAQ;AAChC,oBAAY,QAAQ;AAAA,MACrB;AAEDA,oBAAA,MAAA,MAAA,OAAA,kEAAY,YAAY,YAAY,MAAM;AAAA,IAC5C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjZA,GAAG,WAAWE,SAAe;"}