"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const DistributionSection = () => "../../../../components/distribution-section.js";
const _sfc_main = {
  components: {
    DistributionSection
  },
  data() {
    return {
      id: null,
      statusBarHeight: 20,
      navbarHeight: 82,
      loading: true,
      discount: {},
      isFavorite: false,
      commissionAmount: "30"
    };
  },
  onLoad(options) {
    if (options && options.id) {
      this.id = options.id;
    }
    const systemInfo = common_vendor.index.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 62;
    setTimeout(() => {
      this.loadDiscountDetail();
    }, 500);
  },
  methods: {
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 加载满减详情
    loadDiscountDetail() {
      this.loading = true;
      setTimeout(() => {
        this.discount = {
          id: this.id || 1,
          merchantName: "星巴克咖啡",
          merchantLogo: "/static/demo/shop-logo.png",
          merchantRating: 4.8,
          merchantId: 101,
          rules: [
            { text: "满50减10", highlight: false },
            { text: "满100减30", highlight: true },
            { text: "满200减60", highlight: false }
          ],
          startTime: /* @__PURE__ */ new Date(),
          endTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1e3),
          descriptions: [
            "活动时间：2023年10月1日至2023年10月31日",
            "活动范围：全场通用，特价商品除外",
            "使用方式：下单结算时自动抵扣",
            "限制说明：每人每天最多使用3次",
            "其他说明：最终解释权归商家所有"
          ],
          recommendProducts: [
            {
              id: 1,
              name: "美式咖啡（大）",
              price: "30",
              image: "/static/demo/product1.jpg"
            },
            {
              id: 2,
              name: "拿铁咖啡（中）",
              price: "32",
              image: "/static/demo/product2.jpg"
            },
            {
              id: 3,
              name: "摩卡咖啡（大）",
              price: "36",
              image: "/static/demo/product3.jpg"
            },
            {
              id: 4,
              name: "焦糖玛奇朵",
              price: "38",
              image: "/static/demo/product1.jpg"
            }
          ]
        };
        this.loading = false;
      }, 1e3);
    },
    // 获取活动时间文本
    getTimeText(startTime, endTime) {
      const start = new Date(startTime);
      const end = new Date(endTime);
      const startMonth = start.getMonth() + 1;
      const startDay = start.getDate();
      const endMonth = end.getMonth() + 1;
      const endDay = end.getDate();
      return `${startMonth}.${startDay} - ${endMonth}.${endDay}`;
    },
    // 进入店铺
    goToShop() {
      common_vendor.index.navigateTo({
        url: `/subPackages/shop/pages/detail?id=${this.discount.merchantId}`
      });
    },
    // 查看全部商品
    viewAllProducts() {
      common_vendor.index.navigateTo({
        url: `/subPackages/shop/pages/products?merchantId=${this.discount.merchantId}`
      });
    },
    // 跳转到商品详情
    goToProductDetail(productId) {
      common_vendor.index.navigateTo({
        url: `/subPackages/product/pages/detail?id=${productId}`
      });
    },
    // 获取最高满减金额
    getHighestDiscountAmount() {
      const highestRule = this.discount.rules.find((rule) => rule.highlight);
      if (highestRule) {
        return highestRule.text.split("减")[1];
      }
      return "0";
    },
    // 切换收藏状态
    toggleFavorite() {
      this.isFavorite = !this.isFavorite;
      common_vendor.index.showToast({
        title: this.isFavorite ? "已收藏" : "已取消收藏",
        icon: "none"
      });
    },
    // 联系客服
    contactService() {
      common_vendor.index.showToast({
        title: "正在连接客服...",
        icon: "none"
      });
    },
    // 使用满减
    useDiscount() {
      common_vendor.index.navigateTo({
        url: `/subPackages/shop/pages/detail?id=${this.discount.merchantId}&discountId=${this.id}`
      });
    }
  }
};
if (!Array) {
  const _component_distribution_section = common_vendor.resolveComponent("distribution-section");
  _component_distribution_section();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$7,
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: $data.loading
  }, $data.loading ? {} : {
    d: $data.discount.merchantLogo,
    e: common_vendor.t($data.discount.merchantName),
    f: common_vendor.f(5, (i, k0, i0) => {
      return {
        a: i,
        b: i <= Math.floor($data.discount.merchantRating) ? "/static/images/tabbar/星星-选中.png" : "/static/images/tabbar/星星-未选.png"
      };
    }),
    g: common_vendor.t($data.discount.merchantRating),
    h: common_vendor.o((...args) => $options.goToShop && $options.goToShop(...args)),
    i: $data.navbarHeight + "px",
    j: common_vendor.t($options.getTimeText($data.discount.startTime, $data.discount.endTime)),
    k: common_vendor.f($data.discount.rules, (rule, index, i0) => {
      return common_vendor.e({
        a: rule.highlight
      }, rule.highlight ? {} : {}, {
        b: common_vendor.t(rule.text),
        c: index,
        d: rule.highlight ? 1 : ""
      });
    }),
    l: common_vendor.p({
      itemId: $data.id,
      itemType: "discount",
      itemTitle: $data.discount.merchantName + "满减活动",
      itemPrice: $options.getHighestDiscountAmount(),
      commissionRate: $data.discount.commissionRate || 8
    }),
    m: common_vendor.f($data.discount.descriptions, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: index
      };
    }),
    n: common_vendor.o((...args) => $options.viewAllProducts && $options.viewAllProducts(...args)),
    o: common_vendor.f($data.discount.recommendProducts, (item, index, i0) => {
      return {
        a: item.image,
        b: common_vendor.t(item.name),
        c: common_vendor.t(item.price),
        d: index,
        e: common_vendor.o(($event) => $options.goToProductDetail(item.id), index)
      };
    })
  }, {
    p: $data.isFavorite ? "/static/images/tabbar/收藏-选中.png" : "/static/images/tabbar/收藏.png",
    q: common_vendor.o((...args) => $options.toggleFavorite && $options.toggleFavorite(...args)),
    r: common_assets._imports_1$55,
    s: common_vendor.o((...args) => $options.contactService && $options.contactService(...args)),
    t: common_vendor.o((...args) => $options.useDiscount && $options.useDiscount(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-2734631e"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/discount/detail.js.map
