// 导入各模块的API
import * as newsApi from './news/index';
import * as infoApi from './info/index';
import * as commonApi from './common/index';
import * as businessApi from './business/index';
import * as publishApi from './publish/index';
import * as userApi from './user/index';
import * as activityApi from './activity/index';
import * as paymentApi from './payment/index';
import * as serviceApi from './service/index';

// 导入服务相关API
import { fetchServiceList, fetchServiceCategories, fetchHotServices } from './service/serviceList';
import { fetchPublishList, fetchPublishDetail } from './service/publishList';

// 创建一个新的serviceApi对象，而不是修改导入的对象
const extendedServiceApi = {
  ...serviceApi,
  fetchPublishList,
  fetchPublishDetail
};

// 统一导出API服务
export default {
  // 新闻相关API
  news: {
    getCategories: () => newsApi.newsCategories,
    getList: newsApi.fetchNewsList,
    getDetail: newsApi.fetchNewsDetail,
    getMoreComments: newsApi.fetchMoreComments
  },
  
  // 信息相关API
  info: {
    getCategories: () => infoApi.infoCategories,
    getVisibleCategories: () => infoApi.visibleCategories,
    getTopped: infoApi.fetchToppedInfo,
    getAll: infoApi.fetchAllInfo,
    getByCategory: infoApi.fetchInfoByCategory
  },
  
  // 商圈相关API
  business: {
    getCategories: businessApi.fetchBusinessCategories,
    getShopList: businessApi.fetchShopList,
    getShopDetail: businessApi.fetchShopDetail
  },
  
  // 发布相关API
  publish: {
    // 生意转让
    getBusinessTransferDetail: publishApi.fetchBusinessTransferDetail,
    getRelatedShops: publishApi.fetchRelatedShops,
    
    // 房屋出租
    getHouseRentDetail: publishApi.fetchHouseRentDetail,
    getRelatedHouses: publishApi.fetchRelatedHouses,
    
    // 招聘信息
    getJobDetail: publishApi.fetchJobDetail,
    getRelatedJobs: publishApi.fetchRelatedJobs,
    
    // 房屋出售
    getHouseSaleDetail: publishApi.fetchHouseSaleDetail,
    getRelatedHouseSales: publishApi.fetchRelatedHouseSales,
    
    // 二手车辆
    getCarDetail: publishApi.fetchCarDetail,
    getRelatedCars: publishApi.fetchRelatedCars,
    
    // 二手闲置
    getSecondHandDetail: publishApi.fetchSecondHandDetail,
    getRelatedSecondHands: publishApi.fetchRelatedSecondHands
  },
  
  // 用户相关API
  user: {
    // 用户资料相关
    getProfile: userApi.fetchUserProfile,
    getStats: userApi.fetchUserStats,
    updateProfile: userApi.updateUserProfile,
    
    // 用户设置相关
    getSettings: userApi.fetchUserSettings,
    updateSettings: userApi.updateUserSettings,
    
    // 用户认证相关
    login: userApi.login,
    register: userApi.register,
    getVerificationCode: userApi.getVerificationCode,
    logout: userApi.logout,
    refreshToken: userApi.refreshAuthToken
  },
  
  // 活动相关API
  activity: {
    // 活动列表相关
    getCategories: activityApi.fetchActivityCategories,
    getList: activityApi.fetchActivityList,
    
    // 活动详情相关
    getDetail: activityApi.fetchActivityDetail,
    getRelated: activityApi.fetchRelatedActivities,
    getMoreComments: activityApi.fetchMoreComments,
    
    // 活动参与相关
    participate: activityApi.participateActivity,
    cancelParticipation: activityApi.cancelParticipation
  },
  
  // 支付相关API
  payment: {
    // 钱包相关
    getWalletInfo: paymentApi.fetchWalletInfo,
    recharge: paymentApi.rechargeWallet,
    withdraw: paymentApi.withdrawWallet,
    addBankCard: paymentApi.addBankCard,
    setDefaultBankCard: paymentApi.setDefaultBankCard,
    
    // 交易记录相关
    getTransactionList: paymentApi.fetchTransactionList,
    getTransactionDetail: paymentApi.fetchTransactionDetail,
    getTransactionStats: paymentApi.fetchTransactionStats
  },
  
  // 服务相关API
  service: {
    // 服务列表相关
    getCategories: serviceApi.fetchServiceCategories,
    getList: serviceApi.fetchServiceList,
    getHotServices: serviceApi.fetchHotServices,
    
    // 服务详情相关
    getDetail: serviceApi.fetchServiceDetail,
    getRelated: serviceApi.fetchRelatedServices,
    getMoreReviews: serviceApi.fetchMoreReviews,
    
    // 服务收藏相关
    collect: serviceApi.collectService,
    cancelCollect: serviceApi.cancelCollectService,
    
    // 发布列表相关API
    fetchPublishList,
    fetchPublishDetail
  },
  
  // 广告相关API
  ad: {
    getBanner: commonApi.fetchAdBanner
  }
};

// 导出扩展后的serviceApi
export { extendedServiceApi as serviceApi }; 