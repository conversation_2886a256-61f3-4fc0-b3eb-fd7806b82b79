<template>
  <div v-if="globalLoading" class="global-loading-overlay">
    <div class="loading-container">
      <div class="loading-spinner">
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
      </div>
      <div class="loading-text">{{ loadingText }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAppStore } from '@/stores/app'

const appStore = useAppStore()

const globalLoading = computed(() => appStore.globalLoading)
const loadingText = ref('加载中...')

// 可以通过props传入自定义加载文本
interface Props {
  text?: string
}

const props = withDefaults(defineProps<Props>(), {
  text: '加载中...'
})

watch(() => props.text, (newText) => {
  loadingText.value = newText
}, { immediate: true })
</script>

<style lang="scss" scoped>
.global-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: all 0.3s ease;

  .dark & {
    background-color: rgba(0, 0, 0, 0.9);
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.loading-spinner {
  position: relative;
  width: 60px;
  height: 60px;
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-top: 3px solid var(--el-color-primary);
  border-radius: 50%;
  animation: spin 1.2s linear infinite;

  &:nth-child(1) {
    animation-delay: 0s;
  }

  &:nth-child(2) {
    animation-delay: 0.1s;
    transform: scale(0.8);
    border-top-color: var(--el-color-success);
  }

  &:nth-child(3) {
    animation-delay: 0.2s;
    transform: scale(0.6);
    border-top-color: var(--el-color-warning);
  }

  &:nth-child(4) {
    animation-delay: 0.3s;
    transform: scale(0.4);
    border-top-color: var(--el-color-info);
  }
}

.loading-text {
  font-size: 16px;
  color: var(--el-text-color-primary);
  font-weight: 500;
  letter-spacing: 0.5px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .loading-spinner {
    width: 50px;
    height: 50px;
  }

  .loading-text {
    font-size: 14px;
  }
}
</style>
