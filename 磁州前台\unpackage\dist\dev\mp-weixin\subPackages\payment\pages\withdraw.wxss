
.withdraw-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
}
.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}
.back-icon {
  width: 40rpx;
  height: 40rpx;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}
.navbar-right {
  width: 80rpx;
}

/* 提现金额区域 */
.withdraw-section {
  background-color: #fff;
  margin: 30rpx;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}
.section-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}
.amount-input-area {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.currency-symbol {
  font-size: 50rpx;
  font-weight: bold;
  margin-right: 20rpx;
}
.amount-input {
  font-size: 60rpx;
  font-weight: bold;
  flex: 1;
}
.balance-info {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
}
.withdraw-all {
  color: #0052CC;
}

/* 提现方式 */
.withdraw-method {
  background-color: #fff;
  margin: 30rpx;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}
.bank-card {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fc;
  border-radius: 10rpx;
}
.bank-logo {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.bank-logo-img {
  width: 60rpx;
  height: 60rpx;
}
.bank-info {
  flex: 1;
}
.bank-name {
  font-size: 28rpx;
  margin-bottom: 10rpx;
}
.card-number {
  font-size: 24rpx;
  color: #999;
}
.bank-action {
  font-size: 24rpx;
  color: #0052CC;
}
.add-card {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx;
  background-color: #f8f9fc;
  border-radius: 10rpx;
  color: #0052CC;
  font-size: 28rpx;
}
.add-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

/* 提现说明 */
.withdraw-notice {
  margin: 30rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}
.notice-title {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
}
.notice-item {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
  line-height: 1.5;
}

/* 底部按钮区域 */
.bottom-btn-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.withdraw-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #0052CC;
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
}
.btn-disabled {
  background-color: #cccccc;
  color: #ffffff;
}
