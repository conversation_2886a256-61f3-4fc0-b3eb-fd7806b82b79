"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  __name: "education-detail",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const getStatusBarHeight = () => {
      common_vendor.index.getSystemInfo({
        success: (res) => {
          statusBarHeight.value = res.statusBarHeight || 20;
        }
      });
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const formatTime = (timestamp) => {
      const date = new Date(timestamp);
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    };
    const isCollected = common_vendor.ref(false);
    const courseData = common_vendor.ref({
      id: "course12345",
      title: "少儿英语启蒙课程",
      price: "2999元/期",
      tags: ["小班教学", "外教授课", "免费试听", "趣味教学"],
      publishTime: Date.now() - 864e5 * 2,
      // 2天前
      images: [
        "/static/images/course1.jpg",
        "/static/images/course2.jpg",
        "/static/images/course3.jpg",
        "/static/images/course4.jpg"
      ],
      type: "少儿英语",
      target: "4-12岁儿童",
      startTime: "2024-04-01",
      duration: "3个月",
      contents: [
        { label: "课程目标", value: "培养英语兴趣，建立语言基础，提升口语表达能力" },
        { label: "课程特色", value: "互动教学，寓教于乐，情景对话，角色扮演" },
        { label: "教学方式", value: "小班教学（每班8人），外教授课，中教辅助" },
        { label: "课程安排", value: "每周2次课，每次90分钟，课后作业辅导" },
        { label: "教材使用", value: "牛津少儿英语教材，配套练习册和多媒体资源" },
        { label: "学习效果", value: "掌握300个基础词汇，能进行简单日常对话" }
      ],
      schedule: [
        {
          time: "周一 16:00-17:30",
          title: "英语启蒙",
          description: "基础词汇、简单对话、字母发音"
        },
        {
          time: "周三 16:00-17:30",
          title: "趣味英语",
          description: "英语游戏、歌曲学习、情景对话"
        },
        {
          time: "周六 10:00-11:30",
          title: "英语实践",
          description: "角色扮演、故事阅读、口语练习"
        },
        {
          time: "周日 10:00-11:30",
          title: "英语活动",
          description: "英语角、文化体验、成果展示"
        }
      ],
      features: [
        {
          icon: "icon-teacher",
          title: "专业外教",
          description: "来自英语母语国家的外教，均持有TESOL/TEFL证书"
        },
        {
          icon: "icon-class",
          title: "小班教学",
          description: "每班不超过8人，确保每个孩子都能得到充分关注"
        },
        {
          icon: "icon-material",
          title: "优质教材",
          description: "使用国际知名教材，配套丰富的多媒体教学资源"
        },
        {
          icon: "icon-method",
          title: "趣味教学",
          description: "通过游戏、歌曲、故事等多种方式激发学习兴趣"
        },
        {
          icon: "icon-progress",
          title: "进度跟踪",
          description: "定期评估学习效果，及时调整教学计划"
        }
      ],
      teacher: {
        name: "Sarah Johnson",
        avatar: "/static/images/avatar.png",
        title: "资深外教",
        rating: "98%",
        isVerified: true,
        introduction: "来自英国伦敦，拥有剑桥大学教育学硕士学位，TESOL证书持有者。5年少儿英语教学经验，擅长互动教学和趣味教学法。曾在新加坡国际学校任教，深受学生喜爱。教学风格活泼有趣，善于激发孩子的学习兴趣。"
      },
      contact: {
        name: "王老师",
        phone: "13912345678"
      }
    });
    const similarCourses = common_vendor.ref([
      {
        id: "course001",
        title: "少儿英语进阶课程",
        price: "3999元/期",
        type: "少儿英语",
        target: "6-12岁儿童",
        image: "/static/images/course-similar1.jpg"
      },
      {
        id: "course002",
        title: "少儿英语口语课程",
        price: "2599元/期",
        type: "少儿英语",
        target: "4-12岁儿童",
        image: "/static/images/course-similar2.jpg"
      },
      {
        id: "course003",
        title: "少儿英语阅读写作",
        price: "3299元/期",
        type: "少儿英语",
        target: "8-12岁儿童",
        image: "/static/images/course-similar3.jpg"
      },
      {
        id: "course004",
        title: "少儿英语自然拼读",
        price: "2899元/期",
        type: "少儿英语",
        target: "5-10岁儿童",
        image: "/static/images/course-similar4.jpg"
      }
    ]);
    const relatedCourses = common_vendor.ref([]);
    const loadRelatedCourses = () => {
      setTimeout(() => {
        relatedCourses.value = similarCourses.value.map((course) => ({
          ...course,
          tags: ["专业师资", "小班教学", "趣味教学"]
        }));
      }, 500);
    };
    const navigateToCourseDetail = (courseId) => {
      if (courseId === courseData.value.id) {
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/publish/education-detail?id=${courseId}`
      });
    };
    const navigateToEducationList = (e) => {
      var _a;
      if (e)
        e.stopPropagation();
      const educationCategory = ((_a = courseData.value.tags) == null ? void 0 : _a[0]) || "";
      common_vendor.index.navigateTo({
        url: `/subPackages/service/pages/filter?type=education&title=${encodeURIComponent("教育培训")}&category=${encodeURIComponent(educationCategory)}&active=education`
      });
    };
    const toggleCollect = () => {
      isCollected.value = !isCollected.value;
      if (isCollected.value) {
        common_vendor.index.showToast({
          title: "收藏成功",
          icon: "success"
        });
      }
    };
    const callPhone = () => {
      common_vendor.index.makePhoneCall({
        phoneNumber: courseData.value.contact.phone,
        fail: () => {
          common_vendor.index.showToast({
            title: "拨打电话失败",
            icon: "none"
          });
        }
      });
    };
    const goToHome = () => {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    };
    const openChat = () => {
      if (!courseData.value.contact || !courseData.value.contact.id) {
        common_vendor.index.showToast({
          title: "无法获取教育机构信息",
          icon: "none"
        });
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/chat/index?userId=${courseData.value.contact.id}&username=${encodeURIComponent(courseData.value.contact.name || "教育机构")}`
      });
    };
    common_vendor.onMounted(() => {
      getStatusBarHeight();
      common_vendor.index.setNavigationBarTitle({
        title: "课程详情"
      });
      common_vendor.index.setNavigationBarColor({
        frontColor: "#000000",
        backgroundColor: "#ffffff"
      });
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      const id = options.id || "";
      common_vendor.index.__f__("log", "at pages/publish/education-detail.vue:486", "课程详情页ID:", id);
      loadRelatedCourses();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_vendor.t(courseData.value.title),
        e: common_vendor.t(courseData.value.price),
        f: common_vendor.f(courseData.value.tags, (tag, index, i0) => {
          return {
            a: common_vendor.t(tag),
            b: index
          };
        }),
        g: common_vendor.t(formatTime(courseData.value.publishTime)),
        h: common_vendor.f(courseData.value.images, (image, index, i0) => {
          return {
            a: image,
            b: index
          };
        }),
        i: common_vendor.t(courseData.value.type),
        j: common_vendor.t(courseData.value.target),
        k: common_vendor.t(courseData.value.startTime),
        l: common_vendor.t(courseData.value.duration),
        m: common_vendor.t(courseData.value.contact.name),
        n: common_vendor.t(courseData.value.contact.phone),
        o: common_vendor.o(callPhone),
        p: common_vendor.f(courseData.value.contents, (item, index, i0) => {
          return {
            a: common_vendor.t(item.label),
            b: common_vendor.t(item.value),
            c: index
          };
        }),
        q: common_vendor.f(courseData.value.schedule, (item, index, i0) => {
          return {
            a: common_vendor.t(item.time),
            b: common_vendor.t(item.title),
            c: common_vendor.t(item.description),
            d: index
          };
        }),
        r: common_vendor.f(courseData.value.features, (item, index, i0) => {
          return {
            a: common_vendor.n(item.icon),
            b: common_vendor.t(item.title),
            c: common_vendor.t(item.description),
            d: index
          };
        }),
        s: courseData.value.teacher.avatar,
        t: common_vendor.t(courseData.value.teacher.name),
        v: common_vendor.t(courseData.value.teacher.title),
        w: common_vendor.t(courseData.value.teacher.rating),
        x: courseData.value.teacher.isVerified
      }, courseData.value.teacher.isVerified ? {} : {}, {
        y: common_vendor.t(courseData.value.teacher.introduction),
        z: common_vendor.f(relatedCourses.value.slice(0, 3), (course, index, i0) => {
          return common_vendor.e({
            a: course.image,
            b: common_vendor.t(course.title),
            c: common_vendor.t(course.type),
            d: common_vendor.t(course.target),
            e: common_vendor.f(course.tags.slice(0, 2), (tag, tagIndex, i1) => {
              return {
                a: common_vendor.t(tag),
                b: tagIndex
              };
            }),
            f: course.tags && course.tags.length > 2
          }, course.tags && course.tags.length > 2 ? {
            g: common_vendor.t(course.tags.length - 2)
          } : {}, {
            h: common_vendor.t(course.price),
            i: index,
            j: common_vendor.o(($event) => navigateToCourseDetail(course.id), index)
          });
        }),
        A: relatedCourses.value.length === 0
      }, relatedCourses.value.length === 0 ? {
        B: common_assets._imports_1$3
      } : {}, {
        C: relatedCourses.value.length > 0
      }, relatedCourses.value.length > 0 ? {
        D: common_vendor.o(navigateToEducationList)
      } : {}, {
        E: common_assets._imports_12,
        F: common_vendor.o(goToHome),
        G: common_assets._imports_3$2,
        H: common_vendor.o(toggleCollect),
        I: common_assets._imports_3$3,
        J: common_assets._imports_14,
        K: common_vendor.o(openChat),
        L: common_vendor.o(callPhone)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/publish/education-detail.js.map
