/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly VITE_APP_VERSION: string
  readonly VITE_APP_DESCRIPTION: string
  readonly VITE_API_BASE_URL: string
  readonly VITE_API_TIMEOUT: string
  readonly VITE_UPLOAD_URL: string
  readonly VITE_UPLOAD_MAX_SIZE: string
  readonly VITE_APP_MOCK: string
  readonly VITE_APP_DEBUG: string
  readonly VITE_APP_SOURCEMAP: string
  readonly VITE_PROXY_TARGET: string
  readonly VITE_CDN_URL: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// 全局类型声明
declare global {
  interface Window {
    $confirm: any
  }
}
