import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置 NProgress
NProgress.configure({ 
  showSpinner: false,
  minimum: 0.2,
  easing: 'ease',
  speed: 500
})

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login/index.vue'),
    meta: {
      title: '登录',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/layout/index.vue'),
    redirect: '/dashboard',
    meta: {
      title: '首页',
      requiresAuth: true
    },
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard/index.vue'),
        meta: {
          title: '仪表盘',
          icon: 'Dashboard',
          requiresAuth: true,
          keepAlive: true
        }
      },
      {
        path: '/user',
        name: 'UserManagement',
        component: () => import('@/views/User/index.vue'),
        meta: {
          title: '用户管理',
          icon: 'User',
          requiresAuth: true,
          permissions: ['user:view']
        },
        children: [
          {
            path: '/user/list',
            name: 'UserList',
            component: () => import('@/views/User/UserList.vue'),
            meta: {
              title: '用户列表',
              requiresAuth: true,
              permissions: ['user:view']
            }
          },
          {
            path: '/user/auth',
            name: 'UserAuth',
            component: () => import('@/views/User/UserAuth.vue'),
            meta: {
              title: '用户认证',
              requiresAuth: true,
              permissions: ['user:auth']
            }
          }
        ]
      },
      {
        path: '/merchant',
        name: 'MerchantManagement',
        component: () => import('@/views/Merchant/index.vue'),
        meta: {
          title: '商家管理',
          icon: 'Shop',
          requiresAuth: true,
          permissions: ['merchant:view']
        },
        children: [
          {
            path: '/merchant/list',
            name: 'MerchantList',
            component: () => import('@/views/Merchant/MerchantList.vue'),
            meta: {
              title: '商家列表',
              requiresAuth: true,
              permissions: ['merchant:view']
            }
          },
          {
            path: '/merchant/audit',
            name: 'MerchantAudit',
            component: () => import('@/views/Merchant/MerchantAudit.vue'),
            meta: {
              title: '入驻审核',
              requiresAuth: true,
              permissions: ['merchant:audit']
            }
          }
        ]
      },
      {
        path: '/content',
        name: 'ContentManagement',
        component: () => import('@/views/Content/index.vue'),
        meta: {
          title: '内容管理',
          icon: 'Document',
          requiresAuth: true,
          permissions: ['content:view']
        },
        children: [
          {
            path: '/content/list',
            name: 'ContentList',
            component: () => import('@/views/Content/ContentList.vue'),
            meta: {
              title: '内容列表',
              requiresAuth: true,
              permissions: ['content:view']
            }
          },
          {
            path: '/content/audit',
            name: 'ContentAudit',
            component: () => import('@/views/Content/ContentAudit.vue'),
            meta: {
              title: '内容审核',
              requiresAuth: true,
              permissions: ['content:audit']
            }
          },
          {
            path: '/content/category',
            name: 'ContentCategory',
            component: () => import('@/views/Content/ContentCategory.vue'),
            meta: {
              title: '分类管理',
              requiresAuth: true,
              permissions: ['content:category']
            }
          }
        ]
      },
      {
        path: '/order',
        name: 'OrderManagement',
        component: () => import('@/views/Order/index.vue'),
        meta: {
          title: '订单管理',
          icon: 'ShoppingCart',
          requiresAuth: true,
          permissions: ['order:view']
        },
        children: [
          {
            path: '/order/list',
            name: 'OrderList',
            component: () => import('@/views/Order/OrderList.vue'),
            meta: {
              title: '订单列表',
              requiresAuth: true,
              permissions: ['order:view']
            }
          },
          {
            path: '/order/refund',
            name: 'OrderRefund',
            component: () => import('@/views/Order/OrderRefund.vue'),
            meta: {
              title: '退款管理',
              requiresAuth: true,
              permissions: ['order:refund']
            }
          }
        ]
      },
      {
        path: '/marketing',
        name: 'MarketingManagement',
        component: () => import('@/views/Marketing/index.vue'),
        meta: {
          title: '营销管理',
          icon: 'Present',
          requiresAuth: true,
          permissions: ['marketing:view']
        },
        children: [
          {
            path: '/marketing/activity',
            name: 'ActivityManagement',
            component: () => import('@/views/Marketing/ActivityManagement.vue'),
            meta: {
              title: '活动管理',
              requiresAuth: true,
              permissions: ['marketing:activity']
            }
          },
          {
            path: '/marketing/coupon',
            name: 'CouponManagement',
            component: () => import('@/views/Marketing/CouponManagement.vue'),
            meta: {
              title: '优惠券管理',
              requiresAuth: true,
              permissions: ['marketing:coupon']
            }
          }
        ]
      },
      {
        path: '/data',
        name: 'DataAnalysis',
        component: () => import('@/views/Data/index.vue'),
        meta: {
          title: '数据分析',
          icon: 'DataAnalysis',
          requiresAuth: true,
          permissions: ['data:view']
        },
        children: [
          {
            path: '/data/overview',
            name: 'DataOverview',
            component: () => import('@/views/Data/DataOverview.vue'),
            meta: {
              title: '数据概览',
              requiresAuth: true,
              permissions: ['data:view']
            }
          },
          {
            path: '/data/report',
            name: 'DataReport',
            component: () => import('@/views/Data/DataReport.vue'),
            meta: {
              title: '数据报表',
              requiresAuth: true,
              permissions: ['data:report']
            }
          }
        ]
      },
      {
        path: '/system',
        name: 'SystemManagement',
        component: () => import('@/views/System/index.vue'),
        meta: {
          title: '系统管理',
          icon: 'Setting',
          requiresAuth: true,
          permissions: ['system:view']
        },
        children: [
          {
            path: '/system/admin',
            name: 'AdminManagement',
            component: () => import('@/views/System/AdminManagement.vue'),
            meta: {
              title: '管理员管理',
              requiresAuth: true,
              permissions: ['system:admin']
            }
          },
          {
            path: '/system/role',
            name: 'RoleManagement',
            component: () => import('@/views/System/RoleManagement.vue'),
            meta: {
              title: '角色管理',
              requiresAuth: true,
              permissions: ['system:role']
            }
          },
          {
            path: '/system/permission',
            name: 'PermissionManagement',
            component: () => import('@/views/System/PermissionManagement.vue'),
            meta: {
              title: '权限管理',
              requiresAuth: true,
              permissions: ['system:permission']
            }
          },
          {
            path: '/system/config',
            name: 'SystemConfig',
            component: () => import('@/views/System/SystemConfig.vue'),
            meta: {
              title: '系统配置',
              requiresAuth: true,
              permissions: ['system:config']
            }
          },
          {
            path: '/system/log',
            name: 'SystemLog',
            component: () => import('@/views/System/SystemLog.vue'),
            meta: {
              title: '系统日志',
              requiresAuth: true,
              permissions: ['system:log']
            }
          }
        ]
      }
    ]
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/Error/404.vue'),
    meta: {
      title: '页面不存在',
      hideInMenu: true
    }
  },
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('@/views/Error/403.vue'),
    meta: {
      title: '访问被拒绝',
      hideInMenu: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start()
  
  const userStore = useUserStore()
  const appStore = useAppStore()
  
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 磁州生活网后台管理系统` : '磁州生活网后台管理系统'
  
  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    if (!userStore.isLoggedIn) {
      // 未登录，跳转到登录页
      next({ name: 'Login', query: { redirect: to.fullPath } })
      return
    }
    
    // 检查权限
    if (to.meta.permissions && Array.isArray(to.meta.permissions)) {
      const hasPermission = to.meta.permissions.some(permission => 
        userStore.checkPermission(permission)
      )
      
      if (!hasPermission) {
        // 无权限，跳转到403页面
        next({ name: 'Forbidden' })
        return
      }
    }
  }
  
  // 已登录用户访问登录页，重定向到首页
  if (to.name === 'Login' && userStore.isLoggedIn) {
    next({ name: 'Dashboard' })
    return
  }
  
  // 添加到缓存视图
  if (to.meta.keepAlive && to.name) {
    appStore.addCachedView(to.name as string)
  }
  
  next()
})

router.afterEach(() => {
  NProgress.done()
})

export default router
