<view class="growth-rule-container"><view class="navbar"><view class="navbar-back" bindtap="{{a}}"><view class="back-icon"></view></view><text class="navbar-title">成长值规则</text><view class="navbar-right"></view></view><view class="rule-content"><view class="section-card"><view class="section-title">成长值获取规则</view><view class="rule-form"><view class="form-item"><text class="form-label">消费成长值比例</text><view class="form-input-group"><input type="number" class="form-input" value="{{b}}" bindinput="{{c}}"/><text class="input-suffix">成长值/元</text></view></view><view class="form-item"><text class="form-label">连续签到奖励</text><view class="form-input-group"><input type="number" class="form-input" value="{{d}}" bindinput="{{e}}"/><text class="input-suffix">成长值/天</text></view></view><view class="form-item"><text class="form-label">完善资料奖励</text><view class="form-input-group"><input type="number" class="form-input" value="{{f}}" bindinput="{{g}}"/><text class="input-suffix">成长值</text></view></view><view class="form-item"><text class="form-label">首次购买奖励</text><view class="form-input-group"><input type="number" class="form-input" value="{{h}}" bindinput="{{i}}"/><text class="input-suffix">成长值</text></view></view><view class="form-item"><text class="form-label">邀请新用户奖励</text><view class="form-input-group"><input type="number" class="form-input" value="{{j}}" bindinput="{{k}}"/><text class="input-suffix">成长值/人</text></view></view></view></view><view class="section-card"><view class="section-title">成长值等级对应</view><view class="level-list"><view wx:for="{{l}}" wx:for-item="level" wx:key="h" class="level-item"><view class="level-header"><text class="level-name">{{level.a}}</text><text class="level-growth">{{level.b}}-{{level.c}}成长值</text></view><view class="level-benefits"><text wx:for="{{level.d}}" wx:for-item="benefit" wx:key="b" class="benefit-item">{{benefit.a}}</text></view><view class="level-actions"><text class="action-btn edit" bindtap="{{level.e}}">编辑</text><text wx:if="{{level.f}}" class="action-btn delete" bindtap="{{level.g}}">删除</text></view></view></view><button class="add-btn" bindtap="{{m}}">添加成长值等级</button></view><view class="section-card"><view class="section-title">成长值规则说明</view><view class="form-item"><text class="form-label">规则说明</text><block wx:if="{{r0}}"><textarea class="form-textarea" placeholder="请输入成长值规则说明" value="{{n}}" bindinput="{{o}}"/></block></view><view class="form-item switch-item"><text class="form-label">成长值可视化</text><switch checked="{{p}}" bindchange="{{q}}" color="#4A00E0"/></view><view class="form-item switch-item"><text class="form-label">等级保护机制</text><switch checked="{{r}}" bindchange="{{s}}" color="#4A00E0"/></view><view wx:if="{{t}}" class="form-item"><text class="form-label">保护期限</text><view class="form-input-group"><input type="number" class="form-input" value="{{v}}" bindinput="{{w}}"/><text class="input-suffix">天</text></view></view></view><view class="section-card"><view class="section-title">成长值任务</view><view class="task-list"><view wx:for="{{x}}" wx:for-item="task" wx:key="e" class="task-item"><view class="task-info"><text class="task-name">{{task.a}}</text><text class="task-reward">奖励: {{task.b}}成长值</text></view><view class="task-status"><switch checked="{{task.c}}" bindchange="{{task.d}}" color="#4A00E0"/></view></view></view><button class="add-btn" bindtap="{{y}}">添加成长值任务</button></view></view><view class="bottom-bar"><button class="save-btn" bindtap="{{z}}">保存规则</button></view></view>