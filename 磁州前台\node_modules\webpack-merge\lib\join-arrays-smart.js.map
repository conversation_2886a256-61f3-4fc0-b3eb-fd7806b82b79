{"version": 3, "file": "join-arrays-smart.js", "sourceRoot": "", "sources": ["../src/join-arrays-smart.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,iCAAuE;AACvE,iCAA8E;AAE9E,IAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;AAE9B,SAAS,UAAU,CACjB,KAAsB,EACtB,GAAQ,EACR,OAAc,EACd,IAAW;IAEX,IACE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC;QAC1C,CAAC,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO,CAAC;QACvE,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;QAChE,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,EAChE;QACA,OAAO,KAAK,CAAC;KACd;SAAM,IACL,CAAC,IAAI,CAAC,IAAI;QACV,CAAC,IAAI,CAAC,OAAO;QACb,CAAC,IAAI,CAAC,OAAO;QACb,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACxC,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAClD;QACA,yFAAyF;QACzF,OAAO,KAAK,CAAC;KACd;SAAM,IACL,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC;QAC9B,CAAC,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EACtC;QACA,sEAAsE;QACtE,OAAO,KAAK,CAAC;KACd;IAED,iCAAiC;IACjC,IAAI,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,EAAE;QAC/B,IAAI,CAAC,KAAK,GAAG,kBAAS,CACpB,IAAI,CAAC,KAAK,EACV,OAAO,CAAC,KAAK,EACb,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,OAAO,CAAC,CACnC,CAAC;QAEF,OAAO,IAAI,CAAC;KACb;IAED,+DAA+D;IAC/D,IAAI,OAAO,CAAC,MAAM,EAAE;QAClB,IAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC;QAE1E,OAAO,IAAI,CAAC,GAAG,CAAC;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC;QAElB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAE7B,IAAI,UAAU,EAAE;YACd,IAAI,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;SACxC;KACF;SAAM,IAAI,OAAO,CAAC,KAAK,EAAE;QACxB,OAAO,IAAI,CAAC,GAAG,CAAC;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC;QACpB,OAAO,IAAI,CAAC,MAAM,CAAC;QAEnB,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;KAC5B;SAAM,IACL,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC;QACzC,CAAC,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,EAChC;QACA,IAAM,WAAW,GAAG,UAAC,MAAwB;YAC3C,OAAA,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,MAAM,QAAA,EAAE,CAAC,CAAC,CAAC,MAAM;QAAhD,CAAgD,CAAC;QACnD,qDAAqD;QACrD,IAAM,WAAW,GAAG,UAAC,KAAc;YACjC,OAAA,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK;QAArD,CAAqD,CAAC;QAExD,IAAI,OAAO,SAAW,CAAC;QACvB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC;YACpE,OAAO,GAAG,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;YAEpC,IAAI,UAAU,EAAE;gBACd,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;aAC3C;YAED,OAAO,IAAI,CAAC,MAAM,CAAC;YAEnB,IAAI,UAAU,EAAE;gBACd,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC;aACzB;SACF;aAAM;YACL,OAAO,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;SAChE;QACD,IAAM,UAAU,GAAG,EAAE;aAClB,MAAM,CAAC,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC;aACtC,GAAG,CAAC,WAAW,CAAC,CAAC;QAEpB,IAAM,UAAU,GAAG,IAAI,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;QAC/D,IAAM,WAAW,GAAM,GAAG,SAAI,UAAY,CAAC;QAE3C,QAAQ,KAAK,CAAC,WAAW,CAAC,EAAE;YAC1B,KAAK,qBAAa,CAAC,OAAO;gBACxB,IAAI,CAAC,UAAU,CAAC,GAAG,SACd,uBAAc,CAAC,UAAU,EAAE,OAAO,EAAE,YAAY,CAAC,EACjD,OAAO,EACV,GAAG,CAAC,WAAW,CAAC,CAAC;gBACnB,MAAM;YACR,KAAK,qBAAa,CAAC,OAAO;gBACxB,IAAI,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC;gBAClD,MAAM;YACR,KAAK,qBAAa,CAAC,MAAM,CAAC;YAC1B;gBACE,IAAI,CAAC,UAAU,CAAC,GAAG,cAAc,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;SAC3E;KACF;IAED,IAAI,OAAO,CAAC,OAAO,EAAE;QACnB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;KAChC;IAED,IAAI,OAAO,CAAC,OAAO,EAAE;QACnB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;KAChC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAoIQ,gCAAU;AAlInB;;;;GAIG;AACH,SAAS,WAAW,CAAC,CAAM,EAAE,CAAM;IAC3B,IAAA;;UAEL,EAFM,aAAK,EAAE,aAEb,CAAC;IAEF,OAAO,gBAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC/B,CAAC;AAED,SAAS,eAAe,CAAC,QAAiB,EAAE,KAAc;IACxD,IAAM,YAAY,GAAG,YAAY,CAAC;IAElC,OAAO,gBAAO,CACZ,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,EAChC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CACpC,CAAC;AACJ,CAAC;AAED,SAAS,YAAY,CAAC,QAAiB,EAAE,KAAc;IACrD,IAAI,eAAe,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE;QACpC,uCAAuC;QACvC,kBAAS,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAC3B,OAAO,IAAI,CAAC;KACb;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAqGoB,oCAAY;AAnGjC;;;;;;;EAOE;AACF,SAAS,cAAc,CAAC,UAAqB,EAAE,eAA0B;IACvE,IAAM,SAAS,GAAG,EAAE,CAAC;IAErB,8FAA8F;IAC9F,IAAI,4BAA4B,GAAG,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;IAE9D,KAAK,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;QAClD,IAAM,YAAY,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;QACnC,IAAM,sBAAsB,GAAG,8BAA8B,CAC3D,eAAe,EACf,YAAY,EACZ,4BAA4B,CAC7B,CAAC;QACF,IAAM,mCAAmC,GAAG,sBAAsB,KAAK,CAAC,CAAC,CAAC;QAE1E,IAAI,mCAAmC,EAAE;YACvC,sFAAsF;YACtF,gCAAgC;YAChC,KACE,IAAI,CAAC,GAAG,4BAA4B,EACpC,CAAC,GAAG,sBAAsB,EAC1B,CAAC,IAAI,CAAC,EACN;gBACA,IAAM,aAAa,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;gBAEzC,mFAAmF;gBACnF,sFAAsF;gBACtF,yBAAyB;gBACzB,IAAM,4BAA4B,GAChC,8BAA8B,CAAC,UAAU,EAAE,aAAa,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;gBAEtE,IAAI,CAAC,4BAA4B,EAAE;oBACjC,SAAS,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;iBAClC;gBACD,4BAA4B,IAAI,CAAC,CAAC;aACnC;YAED,YAAY,CAAC,YAAY,EAAE,eAAe,CAAC,4BAA4B,CAAC,CAAC,CAAC;YAC1E,4FAA4F;YAC5F,SAAS,CAAC,OAAO,CAAC,eAAe,CAAC,4BAA4B,CAAC,CAAC,CAAC;YAEjE,4BAA4B,IAAI,CAAC,CAAC;SACnC;aAAM;YACL,IAAM,kCAAkC,GACtC,8BAA8B,CAC5B,SAAS,EACT,YAAY,EACZ,SAAS,CAAC,MAAM,GAAG,CAAC,CACrB,KAAK,CAAC,CAAC,CAAC;YAEX,IAAI,CAAC,kCAAkC,EAAE;gBACvC,SAAS,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;aACjC;SACF;KACF;IAED,iCAAiC;IACjC,KACE,4BAA4B,EAC5B,4BAA4B,IAAI,CAAC,EACjC,4BAA4B,IAAI,CAAC,EACjC;QACA,IAAM,aAAa,GAAG,eAAe,CAAC,4BAA4B,CAAC,CAAC;QACpE,IAAM,kCAAkC,GACtC,8BAA8B,CAC5B,SAAS,EACT,aAAa,EACb,SAAS,CAAC,MAAM,GAAG,CAAC,CACrB,KAAK,CAAC,CAAC,CAAC;QAEX,IAAI,CAAC,kCAAkC,EAAE;YACvC,SAAS,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;SAClC;KACF;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,8BAA8B,CACrC,OAAkB,EAClB,WAAoB,EACpB,aAAqB;IAErB,KAAK,IAAI,CAAC,GAAG,aAAa,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;QAC1C,IAAI,eAAe,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;YAC5C,OAAO,CAAC,CAAC;SACV;KACF;IACD,OAAO,CAAC,CAAC,CAAC;AACZ,CAAC"}