/**
 * 安全路由守卫
 */
import { SECURITY_CONSTANTS } from '@/config/securityConfig';
import { hasPermission, PERMISSION_LEVELS } from './permissionControl';
import { createSecurityLog, SECURITY_EVENTS, LOG_LEVELS } from './securityAudit';
import store from '@/store';

// 路由权限配置
const ROUTE_PERMISSIONS = {
  // 用户相关
  '/pages/user/profile': PERMISSION_LEVELS.USER,
  '/pages/user/settings': PERMISSION_LEVELS.USER,
  
  // 商家管理
  '/subPackages/merchant-admin/': PERMISSION_LEVELS.MERCHANT,
  
  // 管理员
  '/pages/admin/': PERMISSION_LEVELS.ADMIN,
  
  // 推广工具
  '/subPackages/promotion/': PERMISSION_LEVELS.USER
};

/**
 * 检查路由是否需要登录
 * @param {string} url 路由地址
 * @returns {boolean} 是否需要登录
 */
export const requiresAuth = (url) => {
  // 检查是否是敏感路由
  return SECURITY_CONSTANTS.SENSITIVE_ROUTES.some(route => url.startsWith(route));
};

/**
 * 检查路由权限
 * @param {string} url 路由地址
 * @returns {boolean} 是否有权限访问
 */
export const checkRoutePermission = (url) => {
  // 默认权限级别
  let requiredLevel = PERMISSION_LEVELS.GUEST;
  
  // 查找匹配的路由权限配置
  for (const route in ROUTE_PERMISSIONS) {
    if (url.startsWith(route)) {
      requiredLevel = ROUTE_PERMISSIONS[route];
      break;
    }
  }
  
  // 检查用户是否有权限
  return hasPermission(requiredLevel);
};

/**
 * 路由拦截器
 * @param {Object} options 路由选项
 * @returns {boolean} 是否允许导航
 */
export const routeInterceptor = (options) => {
  const url = options.url;
  
  // 检查是否需要登录
  if (requiresAuth(url)) {
    const token = uni.getStorageSync(SECURITY_CONSTANTS.TOKEN_KEY);
    
    if (!token) {
      // 记录未授权访问
      createSecurityLog(SECURITY_EVENTS.UNAUTHORIZED_ACCESS, {
        url,
        timestamp: new Date().toISOString()
      }, LOG_LEVELS.SECURITY);
      
      // 跳转到登录页
      uni.navigateTo({
        url: '/pages/login/index?redirect=' + encodeURIComponent(url)
      });
      
      return false;
    }
    
    // 检查路由权限
    if (!checkRoutePermission(url)) {
      uni.showToast({
        title: '您没有权限访问此页面',
        icon: 'none'
      });
      
      // 记录权限不足访问
      createSecurityLog(SECURITY_EVENTS.UNAUTHORIZED_ACCESS, {
        url,
        userId: store.state.user?.userId,
        reason: 'insufficient_permissions',
        timestamp: new Date().toISOString()
      }, LOG_LEVELS.SECURITY);
      
      return false;
    }
  }
  
  return true;
};

/**
 * 注册全局路由拦截器
 */
export const registerRouteGuard = () => {
  // 注册页面跳转拦截器
  const routeMethods = ['navigateTo', 'redirectTo', 'reLaunch', 'switchTab'];
  
  routeMethods.forEach(method => {
    const original = uni[method];
    
    uni[method] = (options) => {
      if (routeInterceptor(options)) {
        return original(options);
      }
    };
  });
};

export default {
  requiresAuth,
  checkRoutePermission,
  routeInterceptor,
  registerRouteGuard
}; 