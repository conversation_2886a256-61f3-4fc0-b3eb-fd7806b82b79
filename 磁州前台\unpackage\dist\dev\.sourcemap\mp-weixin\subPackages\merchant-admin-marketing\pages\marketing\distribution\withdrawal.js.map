{"version": 3, "file": "withdrawal.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/distribution/withdrawal.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xkaXN0cmlidXRpb25cd2l0aGRyYXdhbC52dWU"], "sourcesContent": ["<template>\n  <view class=\"withdrawal-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">提现设置</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 提现规则卡片 -->\n    <view class=\"settings-card\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">提现规则</text>\n      </view>\n      \n      <view class=\"form-item\">\n        <text class=\"form-label\">最低提现金额</text>\n        <view class=\"form-input-group\">\n          <text class=\"input-prefix\">¥</text>\n          <input type=\"digit\" v-model=\"minWithdrawalAmount\" class=\"form-input\" placeholder=\"请输入金额\" />\n        </view>\n      </view>\n      \n      <view class=\"form-item\">\n        <text class=\"form-label\">提现手续费</text>\n        <view class=\"form-input-group\">\n          <input type=\"digit\" v-model=\"withdrawalFee\" class=\"form-input\" placeholder=\"请输入比例\" />\n          <text class=\"input-suffix\">%</text>\n        </view>\n      </view>\n      \n      <view class=\"form-item\">\n        <text class=\"form-label\">提现到账时间</text>\n        <view class=\"form-select\" @click=\"showTimeOptions\">\n          <text class=\"select-value\">{{timeOptions[selectedTime]}}</text>\n          <view class=\"arrow-icon down\"></view>\n        </view>\n      </view>\n      \n      <view class=\"form-item\">\n        <text class=\"form-label\">自动审核</text>\n        <view class=\"form-switch\">\n          <switch :checked=\"autoApprove\" @change=\"toggleAutoApprove\" color=\"#6B0FBE\" />\n        </view>\n      </view>\n    </view>\n    \n    <!-- 提现方式卡片 -->\n    <view class=\"methods-card\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">提现方式</text>\n      </view>\n      \n      <view class=\"method-item\" v-for=\"(method, index) in withdrawalMethods\" :key=\"index\">\n        <view class=\"method-left\">\n          <view class=\"method-icon\" :class=\"`method-${method.id}`\">\n            <image class=\"method-image\" :src=\"method.icon\" mode=\"aspectFit\"></image>\n          </view>\n          <text class=\"method-name\">{{method.name}}</text>\n        </view>\n        <view class=\"method-switch\">\n          <switch :checked=\"method.enabled\" @change=\"(e) => toggleMethod(index, e)\" color=\"#6B0FBE\" />\n        </view>\n      </view>\n      \n      <view class=\"method-tip\">\n        <text class=\"tip-text\">至少需要开启一种提现方式</text>\n      </view>\n    </view>\n    \n    <!-- 提现说明卡片 -->\n    <view class=\"description-card\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">提现说明</text>\n      </view>\n      \n      <view class=\"form-item\">\n        <textarea v-model=\"withdrawalDescription\" class=\"form-textarea\" placeholder=\"请输入提现说明，将在用户提现页面显示\"></textarea>\n      </view>\n    </view>\n    \n    <!-- 保存按钮 -->\n    <view class=\"button-container\">\n      <button class=\"save-button\" @click=\"saveSettings\">保存设置</button>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue';\n\n// 最低提现金额\nconst minWithdrawalAmount = ref('50');\n\n// 提现手续费\nconst withdrawalFee = ref('0');\n\n// 提现到账时间选项\nconst timeOptions = {\n  'realtime': '实时到账',\n  'T1': 'T+1天到账',\n  'T2': 'T+2天到账',\n  'T3': 'T+3天到账'\n};\n\n// 选中的到账时间\nconst selectedTime = ref('T1');\n\n// 是否自动审核\nconst autoApprove = ref(false);\n\n// 提现方式\nconst withdrawalMethods = ref([\n  {\n    id: 'wechat',\n    name: '微信零钱',\n    icon: '/static/images/payment/wechat.png',\n    enabled: true\n  },\n  {\n    id: 'alipay',\n    name: '支付宝',\n    icon: '/static/images/payment/alipay.png',\n    enabled: true\n  },\n  {\n    id: 'bank',\n    name: '银行卡',\n    icon: '/static/images/payment/bank.png',\n    enabled: false\n  }\n]);\n\n// 提现说明\nconst withdrawalDescription = ref('1. 提现金额必须大于等于最低提现金额\\n2. 提现申请提交后将在1-3个工作日内处理\\n3. 如有疑问，请联系客服');\n\n// 页面加载\nonMounted(() => {\n  // 获取提现设置\n  getWithdrawalSettings();\n});\n\n// 获取提现设置\nconst getWithdrawalSettings = () => {\n  // 这里应该从API获取设置\n  // 暂时使用模拟数据\n};\n\n// 显示到账时间选项\nconst showTimeOptions = () => {\n  const options = Object.values(timeOptions);\n  \n  uni.showActionSheet({\n    itemList: options,\n    success: (res) => {\n      const keys = Object.keys(timeOptions);\n      selectedTime.value = keys[res.tapIndex];\n    }\n  });\n};\n\n// 切换自动审核\nconst toggleAutoApprove = (e) => {\n  autoApprove.value = e.detail.value;\n};\n\n// 切换提现方式\nconst toggleMethod = (index, e) => {\n  withdrawalMethods.value[index].enabled = e.detail.value;\n  \n  // 检查是否至少有一种提现方式开启\n  const enabledMethods = withdrawalMethods.value.filter(method => method.enabled);\n  if (enabledMethods.length === 0) {\n    uni.showToast({\n      title: '至少需要开启一种提现方式',\n      icon: 'none'\n    });\n    withdrawalMethods.value[index].enabled = true;\n  }\n};\n\n// 保存设置\nconst saveSettings = () => {\n  // 验证输入\n  if (!minWithdrawalAmount.value || Number(minWithdrawalAmount.value) <= 0) {\n    uni.showToast({\n      title: '请输入有效的最低提现金额',\n      icon: 'none'\n    });\n    return;\n  }\n  \n  if (!withdrawalFee.value || Number(withdrawalFee.value) < 0 || Number(withdrawalFee.value) > 100) {\n    uni.showToast({\n      title: '请输入有效的提现手续费比例',\n      icon: 'none'\n    });\n    return;\n  }\n  \n  // 这里应该调用API保存设置\n  uni.showLoading({\n    title: '保存中...'\n  });\n  \n  setTimeout(() => {\n    uni.hideLoading();\n    uni.showToast({\n      title: '保存成功',\n      icon: 'success'\n    });\n    \n    // 返回上一页\n    setTimeout(() => {\n      uni.navigateBack();\n    }, 1500);\n  }, 1000);\n};\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n\n// 显示帮助\nconst showHelp = () => {\n  uni.showModal({\n    title: '提现设置帮助',\n    content: '您可以设置分销员的提现规则，包括最低提现金额、手续费、到账时间和提现方式等。',\n    showCancel: false\n  });\n};\n</script>\n\n<style lang=\"scss\">\n.withdrawal-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: env(safe-area-inset-bottom);\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\n  color: #fff;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: bold;\n}\n\n/* 卡片样式 */\n.settings-card, .methods-card, .description-card {\n  margin: 16px;\n  background-color: #FFFFFF;\n  border-radius: 20px;\n  overflow: hidden;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);\n}\n\n.card-header {\n  padding: 16px;\n  border-bottom: 1px solid #F0F0F0;\n}\n\n.card-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n/* 表单项样式 */\n.form-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px;\n  border-bottom: 1px solid #F0F0F0;\n}\n\n.form-item:last-child {\n  border-bottom: none;\n}\n\n.form-label {\n  font-size: 15px;\n  color: #333;\n}\n\n.form-input-group {\n  display: flex;\n  align-items: center;\n  background-color: #F5F7FA;\n  border-radius: 8px;\n  padding: 0 12px;\n  height: 36px;\n}\n\n.input-prefix {\n  font-size: 15px;\n  color: #333;\n  margin-right: 4px;\n}\n\n.input-suffix {\n  font-size: 15px;\n  color: #333;\n  margin-left: 4px;\n}\n\n.form-input {\n  height: 36px;\n  width: 80px;\n  font-size: 15px;\n  color: #333;\n  text-align: right;\n}\n\n.form-select {\n  display: flex;\n  align-items: center;\n  background-color: #F5F7FA;\n  border-radius: 8px;\n  padding: 0 12px;\n  height: 36px;\n}\n\n.select-value {\n  font-size: 14px;\n  color: #333;\n  margin-right: 8px;\n}\n\n.arrow-icon {\n  width: 8px;\n  height: 8px;\n  border-top: 2px solid #CCCCCC;\n  border-right: 2px solid #CCCCCC;\n  transform: rotate(45deg);\n}\n\n.arrow-icon.down {\n  transform: rotate(135deg);\n}\n\n.form-switch {\n  height: 36px;\n  display: flex;\n  align-items: center;\n}\n\n.form-textarea {\n  width: 100%;\n  height: 120px;\n  background-color: #F5F7FA;\n  border-radius: 8px;\n  padding: 12px;\n  font-size: 14px;\n  color: #333;\n}\n\n/* 提现方式样式 */\n.method-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px;\n  border-bottom: 1px solid #F0F0F0;\n}\n\n.method-left {\n  display: flex;\n  align-items: center;\n}\n\n.method-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 12px;\n  background-color: #F5F7FA;\n}\n\n.method-image {\n  width: 24px;\n  height: 24px;\n}\n\n.method-name {\n  font-size: 15px;\n  color: #333;\n}\n\n.method-switch {\n  height: 36px;\n  display: flex;\n  align-items: center;\n}\n\n.method-tip {\n  padding: 0 16px 16px;\n}\n\n.tip-text {\n  font-size: 12px;\n  color: #999;\n}\n\n/* 按钮样式 */\n.button-container {\n  margin: 24px 16px;\n}\n\n.save-button {\n  height: 44px;\n  border-radius: 22px;\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\n  color: #FFFFFF;\n  font-size: 16px;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: none;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/distribution/withdrawal.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni", "MiniProgramPage"], "mappings": ";;;;;AAgGA,UAAM,sBAAsBA,cAAAA,IAAI,IAAI;AAGpC,UAAM,gBAAgBA,cAAAA,IAAI,GAAG;AAG7B,UAAM,cAAc;AAAA,MAClB,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AAGA,UAAM,eAAeA,cAAAA,IAAI,IAAI;AAG7B,UAAM,cAAcA,cAAAA,IAAI,KAAK;AAG7B,UAAM,oBAAoBA,cAAAA,IAAI;AAAA,MAC5B;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,IACH,CAAC;AAGD,UAAM,wBAAwBA,cAAAA,IAAI,4DAA4D;AAG9FC,kBAAAA,UAAU,MAAM;AAEd;IACF,CAAC;AAGD,UAAM,wBAAwB,MAAM;AAAA,IAGpC;AAGA,UAAM,kBAAkB,MAAM;AAC5B,YAAM,UAAU,OAAO,OAAO,WAAW;AAEzCC,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU;AAAA,QACV,SAAS,CAAC,QAAQ;AAChB,gBAAM,OAAO,OAAO,KAAK,WAAW;AACpC,uBAAa,QAAQ,KAAK,IAAI,QAAQ;AAAA,QACvC;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,CAAC,MAAM;AAC/B,kBAAY,QAAQ,EAAE,OAAO;AAAA,IAC/B;AAGA,UAAM,eAAe,CAAC,OAAO,MAAM;AACjC,wBAAkB,MAAM,KAAK,EAAE,UAAU,EAAE,OAAO;AAGlD,YAAM,iBAAiB,kBAAkB,MAAM,OAAO,YAAU,OAAO,OAAO;AAC9E,UAAI,eAAe,WAAW,GAAG;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD,0BAAkB,MAAM,KAAK,EAAE,UAAU;AAAA,MAC1C;AAAA,IACH;AAGA,UAAM,eAAe,MAAM;AAEzB,UAAI,CAAC,oBAAoB,SAAS,OAAO,oBAAoB,KAAK,KAAK,GAAG;AACxEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI,CAAC,cAAc,SAAS,OAAO,cAAc,KAAK,IAAI,KAAK,OAAO,cAAc,KAAK,IAAI,KAAK;AAChGA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAGDA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAGD,mBAAW,MAAM;AACfA,wBAAG,MAAC,aAAY;AAAA,QACjB,GAAE,IAAI;AAAA,MACR,GAAE,GAAI;AAAA,IACT;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MAChB,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1OA,GAAG,WAAWC,SAAe;"}