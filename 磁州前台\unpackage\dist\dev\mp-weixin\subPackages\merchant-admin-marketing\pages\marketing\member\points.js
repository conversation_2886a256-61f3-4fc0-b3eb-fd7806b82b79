"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      dateRange: "2023-04-01 ~ 2023-04-30",
      // 积分数据概览
      pointsData: {
        totalPoints: 256840,
        pointsTrend: "up",
        pointsGrowth: "12.5%",
        usedPoints: 98560,
        usedTrend: "up",
        usedGrowth: "8.3%",
        activeUsers: 1256,
        usersTrend: "up",
        usersGrowth: "5.7%",
        conversionRate: 38.4,
        conversionTrend: "up",
        conversionGrowth: "2.1%"
      },
      // 积分规则
      pointsRules: [
        {
          name: "消费积分",
          description: "会员购物可获得积分奖励",
          type: "purchase",
          iconPath: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1.41 16.09V20h-2.67v-1.93c-1.71-.36-3.16-1.46-3.27-3.4h1.96c.1 1.05.82 1.87 2.65 1.87 1.96 0 2.4-.98 2.4-1.59 0-.83-.44-1.61-2.67-2.14-2.48-.6-4.18-1.62-4.18-3.67 0-1.72 1.39-2.84 3.11-3.21V4h2.67v1.95c1.86.45 2.79 1.86 2.85 3.39H14.3c-.05-1.11-.64-1.87-2.22-1.87-1.5 0-2.4.68-2.4 1.64 0 .84.65 1.39 2.67 1.91s4.18 1.39 4.18 3.91c-.01 1.83-1.38 2.83-3.12 3.16z",
          iconColor: "#FF9500",
          value: "10积分/元"
        },
        {
          name: "每日签到",
          description: "会员每日签到获得积分",
          type: "checkin",
          iconPath: "M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7v-5z",
          iconColor: "#34C759",
          value: "5积分/次"
        },
        {
          name: "商品评价",
          description: "购买商品后评价获得积分",
          type: "review",
          iconPath: "M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-7 12h-2v-2h2v2zm0-4h-2V6h2v4z",
          iconColor: "#007AFF",
          value: "20积分/次"
        },
        {
          name: "分享商品",
          description: "分享商品给好友获得积分",
          type: "share",
          iconPath: "M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z",
          iconColor: "#FF2D55",
          value: "15积分/次"
        }
      ],
      // 积分商品
      pointsProducts: [
        {
          id: 1,
          name: "精美保温杯",
          image: "/static/images/points-product-1.jpg",
          points: 1200,
          status: "热门"
        },
        {
          id: 2,
          name: "无线充电器",
          image: "/static/images/points-product-2.jpg",
          points: 2e3,
          status: "新品"
        },
        {
          id: 3,
          name: "精致小台灯",
          image: "/static/images/points-product-3.jpg",
          points: 1500,
          status: "热门"
        },
        {
          id: 4,
          name: "优质雨伞",
          image: "/static/images/points-product-4.jpg",
          points: 800,
          status: ""
        }
      ]
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    showHelp() {
      common_vendor.index.showModal({
        title: "会员积分帮助",
        content: "会员积分是会员在消费、签到等行为后获得的奖励，可用于兑换商品或服务。",
        showCancel: false
      });
    },
    showDatePicker() {
      common_vendor.index.showToast({
        title: "日期选择功能开发中",
        icon: "none"
      });
    },
    navigateToRules() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/points/rules"
      });
    },
    navigateToPointsMall() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/points/management"
      });
    },
    viewProduct(product) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/points/detail?id=${product.id}`
      });
    }
  }
};
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: common_vendor.t($data.dateRange),
    d: common_vendor.o((...args) => $options.showDatePicker && $options.showDatePicker(...args)),
    e: common_vendor.t($data.pointsData.totalPoints),
    f: common_vendor.t($data.pointsData.pointsGrowth),
    g: common_vendor.n($data.pointsData.pointsTrend),
    h: common_vendor.t($data.pointsData.usedPoints),
    i: common_vendor.t($data.pointsData.usedGrowth),
    j: common_vendor.n($data.pointsData.usedTrend),
    k: common_vendor.t($data.pointsData.activeUsers),
    l: common_vendor.t($data.pointsData.usersGrowth),
    m: common_vendor.n($data.pointsData.usersTrend),
    n: common_vendor.t($data.pointsData.conversionRate),
    o: common_vendor.t($data.pointsData.conversionGrowth),
    p: common_vendor.n($data.pointsData.conversionTrend),
    q: common_vendor.o((...args) => $options.navigateToRules && $options.navigateToRules(...args)),
    r: common_vendor.f($data.pointsRules, (rule, index, i0) => {
      return {
        a: "fd88e518-1-" + i0 + "," + ("fd88e518-0-" + i0),
        b: common_vendor.p({
          d: rule.iconPath
        }),
        c: rule.iconColor,
        d: "fd88e518-0-" + i0,
        e: common_vendor.n(rule.type),
        f: common_vendor.t(rule.name),
        g: common_vendor.t(rule.description),
        h: common_vendor.t(rule.value),
        i: index
      };
    }),
    s: common_vendor.p({
      viewBox: "0 0 24 24"
    }),
    t: common_vendor.o((...args) => $options.navigateToPointsMall && $options.navigateToPointsMall(...args)),
    v: common_vendor.f($data.pointsProducts, (product, index, i0) => {
      return {
        a: product.image,
        b: common_vendor.t(product.name),
        c: common_vendor.t(product.points),
        d: common_vendor.t(product.status),
        e: common_vendor.n(product.status === "热门" ? "hot" : product.status === "新品" ? "new" : ""),
        f: index,
        g: common_vendor.o(($event) => $options.viewProduct(product), index)
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/member/points.js.map
