<view class="trip-records-container"><view class="custom-header" style="{{'padding-top:' + c}}"><view class="header-content"><view class="left-action" bindtap="{{b}}"><image src="{{a}}" class="action-icon back-icon"></image></view><view class="title-area"><text class="page-title">行程记录</text></view><view class="right-action"></view></view></view><view class="tab-container" style="{{'margin-top:' + e}}"><view wx:for="{{d}}" wx:for-item="tab" wx:key="b" class="{{['tab-item', tab.c && 'active']}}" bindtap="{{tab.d}}"><text class="tab-text">{{tab.a}}</text></view></view><scroll-view class="trip-list-scroll" scroll-y bindscrolltolower="{{l}}" refresher-enabled bindrefresherrefresh="{{m}}" refresher-triggered="{{n}}"><view wx:if="{{f}}" class="trip-list"><view wx:for="{{g}}" wx:for-item="item" wx:key="r" class="trip-item"><view class="trip-header"><view class="{{['trip-type', item.b && 'driver', item.c && 'passenger']}}"><text class="type-text">{{item.a}}</text></view><view class="{{['trip-status', item.e]}}"><text class="status-text">{{item.d}}</text></view></view><view class="trip-route"><view class="route-point start"><view class="point-icon start"></view><text class="point-name">{{item.f}}</text></view><view class="route-line"></view><view class="route-point end"><view class="point-icon end"></view><text class="point-name">{{item.g}}</text></view></view><view class="trip-info"><view class="info-row"><text class="info-label">出发时间</text><text class="info-value">{{item.h}}</text></view><view class="info-row"><text class="info-label">{{item.i}}</text><text class="info-value">{{item.j}}人</text></view><view class="info-row"><text class="info-label">费用</text><text class="info-value price">¥{{item.k}}</text></view></view><view class="trip-actions"><button class="action-btn detail" bindtap="{{item.l}}">查看详情</button><button wx:if="{{item.m}}" class="action-btn contact" bindtap="{{item.o}}">联系{{item.n}}</button><button wx:if="{{item.p}}" class="action-btn rate" bindtap="{{item.q}}">评价</button></view></view></view><view wx:if="{{h}}" class="empty-state"><image src="{{i}}" mode="aspectFit" class="empty-image"></image><text class="empty-text">暂无行程记录</text></view><view wx:if="{{j}}" class="loading-state"><text class="loading-text">加载中...</text></view><view wx:if="{{k}}" class="list-bottom"><text class="bottom-text">— 已经到底啦 —</text></view></scroll-view></view>