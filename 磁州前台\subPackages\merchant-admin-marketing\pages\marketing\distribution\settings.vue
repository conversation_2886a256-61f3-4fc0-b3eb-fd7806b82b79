<template>
  <view class="settings-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">分销设置</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 设置卡片 -->
    <view class="settings-card">
      <view class="settings-header">
        <text class="settings-title">分销功能</text>
        <switch :checked="distributionEnabled" @change="toggleDistribution" color="#6B0FBE" />
      </view>
      
      <view class="settings-items">
        <view class="settings-item" @click="navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/distribution/conditions')">
          <view class="item-left">
            <view class="item-icon orange">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M22 11.08V12C21.9988 14.1564 21.3005 16.2547 20.0093 17.9818C18.7182 19.709 16.9033 20.9725 14.8354 21.5839C12.7674 22.1953 10.5573 22.1219 8.53447 21.3746C6.51168 20.6273 4.78465 19.2461 3.61096 17.4371C2.43727 15.628 1.87979 13.4881 2.02168 11.3363C2.16356 9.18455 2.99721 7.13631 4.39828 5.49706C5.79935 3.85781 7.69279 2.71537 9.79619 2.24013C11.8996 1.7649 14.1003 1.98232 16.07 2.85999" stroke="#FF9500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M22 4L12 14.01L9 11.01" stroke="#FF9500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </view>
            <text class="item-title">成为分销员条件</text>
          </view>
          <view class="item-right">
            <text class="item-value">购买商品并申请</text>
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <view class="settings-item" @click="navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/distribution/levels')">
          <view class="item-left">
            <view class="item-icon blue">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8 6H21" stroke="#007AFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M8 12H21" stroke="#007AFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M8 18H21" stroke="#007AFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M3 6H3.01" stroke="#007AFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M3 12H3.01" stroke="#007AFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M3 18H3.01" stroke="#007AFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </view>
            <text class="item-title">分销等级设置</text>
          </view>
          <view class="item-right">
            <text class="item-value">3个等级</text>
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <view class="settings-item" @click="navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/distribution/withdrawal')">
          <view class="item-left">
            <view class="item-icon green">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M17 9V7C17 5.89543 16.1046 5 15 5H5C3.89543 5 3 5.89543 3 7V13C3 14.1046 3.89543 15 5 15H7" stroke="#34C759" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M13 13H21V19H13V13Z" stroke="#34C759" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M13 17H9V19H13V17Z" stroke="#34C759" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </view>
            <text class="item-title">提现设置</text>
          </view>
          <view class="item-right">
            <text class="item-value">最低¥50元</text>
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <view class="settings-item" @click="navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/distribution/agreement')">
          <view class="item-left">
            <view class="item-icon purple">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M14 3V7C14 7.26522 14.1054 7.51957 14.2929 7.70711C14.4804 7.89464 14.7348 8 15 8H19" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M17 21H7C6.46957 21 5.96086 20.7893 5.58579 20.4142C5.21071 20.0391 5 19.5304 5 19V5C5 4.46957 5.21071 3.96086 5.58579 3.58579C5.96086 3.21071 6.46957 3 7 3H14L19 8V19C19 19.5304 18.7893 20.0391 18.4142 20.4142C18.0391 20.7893 17.5304 21 17 21Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M9 7H10" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M9 13H15" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M9 17H15" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </view>
            <text class="item-title">分销协议</text>
          </view>
          <view class="item-right">
            <view class="arrow-icon"></view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 说明卡片 -->
    <view class="info-card">
      <view class="info-header">
        <view class="info-icon">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M12 16V12" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M12 8H12.01" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </view>
        <text class="info-title">分销功能说明</text>
      </view>
      <view class="info-content">
        <text class="info-text">开启分销功能后，用户可以成为您的分销员，通过分享商品获得佣金。您可以设置分销员的条件、等级、提现规则等。</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 分销功能开关状态
const distributionEnabled = ref(true);

// 页面加载
onMounted(() => {
  // 获取分销设置
  getDistributionSettings();
});

// 获取分销设置
const getDistributionSettings = () => {
  // 这里应该从API获取分销设置
  // 暂时使用模拟数据
  distributionEnabled.value = true;
};

// 切换分销功能
const toggleDistribution = (e) => {
  distributionEnabled.value = e.detail.value;
  
  // 保存设置
  saveDistributionSettings();
};

// 保存分销设置
const saveDistributionSettings = () => {
  // 这里应该调用API保存设置
  uni.showToast({
    title: distributionEnabled.value ? '分销功能已开启' : '分销功能已关闭',
    icon: 'none'
  });
};

// 页面导航
const navigateTo = (url) => {
  uni.navigateTo({ url });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 显示帮助
const showHelp = () => {
  uni.showModal({
    title: '分销设置帮助',
    content: '在这里您可以设置分销功能的各项规则，包括分销员条件、等级、提现规则和分销协议。',
    showCancel: false
  });
};
</script>

<style lang="scss">
.settings-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 设置卡片样式 */
.settings-card {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #F0F0F0;
}

.settings-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.settings-items {
  padding: 0 16px;
}

.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #F0F0F0;
}

.settings-item:last-child {
  border-bottom: none;
}

.item-left {
  display: flex;
  align-items: center;
}

.item-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.item-icon.orange {
  background-color: rgba(255, 149, 0, 0.1);
}

.item-icon.blue {
  background-color: rgba(0, 122, 255, 0.1);
}

.item-icon.green {
  background-color: rgba(52, 199, 89, 0.1);
}

.item-icon.purple {
  background-color: rgba(107, 15, 190, 0.1);
}

.item-title {
  font-size: 15px;
  color: #333;
}

.item-right {
  display: flex;
  align-items: center;
}

.item-value {
  font-size: 14px;
  color: #999;
  margin-right: 8px;
}

.arrow-icon {
  width: 8px;
  height: 8px;
  border-top: 2px solid #CCCCCC;
  border-right: 2px solid #CCCCCC;
  transform: rotate(45deg);
}

/* 说明卡片样式 */
.info-card {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
}

.info-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.info-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.info-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
}

.info-content {
  padding: 0 4px;
}

.info-text {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}
</style> 