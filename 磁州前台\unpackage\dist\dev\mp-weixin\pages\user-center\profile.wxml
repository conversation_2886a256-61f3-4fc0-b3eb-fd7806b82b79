<view class="profile-container"><view class="ios-navbar" style="{{'padding-top:' + d}}"><view class="navbar-left" bindtap="{{b}}"><view class="back-button"><image src="{{a}}" class="back-icon"></image></view></view><view class="navbar-title">{{c}}</view></view><scroll-view class="profile-content" scroll-y style="{{'padding-top:' + aJ}}"><view class="profile-header"><view class="profile-card"><view class="profile-avatar-container"><image class="profile-avatar" src="{{e}}" mode="aspectFill"></image></view><view class="profile-info"><view class="profile-name-row"><text class="profile-name">{{f}}</text><view class="vip-badge"><text class="vip-text">VIP3</text></view><view wx:if="{{g}}" class="settings-btn" bindtap="{{h}}">设置</view></view><view class="profile-id-row"><text class="profile-id">ID: {{i}} · {{j}}入住</text></view><view class="profile-motto"><text class="motto-text">{{k}}</text></view></view></view><view wx:if="{{false}}" class="profile-actions"><button class="action-btn edit-btn" bindtap="{{l}}"> 编辑资料 </button><button class="action-btn stats-btn" bindtap="{{m}}"> 数据分析 </button></view></view><view class="stats-card"><view class="stats-item" bindtap="{{o}}"><text class="stats-value">{{n}}</text><text class="stats-label">关注</text></view><view class="stats-item" bindtap="{{q}}"><text class="stats-value">{{p}}</text><text class="stats-label">粉丝</text></view><view class="stats-item" bindtap="{{s}}"><text class="stats-value">{{r}}</text><text class="stats-label">获赞</text></view><view class="stats-item" bindtap="{{v}}"><text class="stats-value">{{t}}</text><text class="stats-label">店铺</text></view><view class="stats-item" bindtap="{{x}}"><text class="stats-value">{{w}}</text><text class="stats-label">发布</text></view></view><view class="content-tabs"><view wx:for="{{y}}" wx:for-item="tab" wx:key="b" class="{{['tab-item', tab.c && 'active']}}" bindtap="{{tab.d}}"><text class="tab-text">{{tab.a}}</text></view><view class="tab-indicator" style="{{'left:' + z + ';' + ('width:' + A)}}"></view></view><swiper class="content-swiper" current="{{aG}}" bindchange="{{aH}}" style="{{'height:' + aI}}"><swiper-item><scroll-view scroll-y class="tab-scroll" bindscrolltolower="{{am}}" refresher-enabled refresher-triggered="{{an}}" bindrefresherrefresh="{{ao}}"><block wx:if="{{B}}"><view class="shop-card"><view class="shop-header"><image class="shop-icon" src="{{C}}" mode="aspectFill" bindtap="{{D}}"></image><view class="shop-info"><text class="shop-name" bindtap="{{F}}">{{E}}</text><text class="shop-description">{{G}}</text></view></view><view class="shop-contact"><view class="contact-item"><image class="contact-icon" src="{{H}}"></image><text class="contact-text">{{I}}</text></view><view class="contact-item" bindtap="{{M}}"><image class="contact-icon" src="{{J}}"></image><text class="contact-text address-text">{{K}}</text><image class="navigation-icon" src="{{L}}"></image></view></view><view class="shop-actions"><button wx:if="{{N}}" class="shop-btn primary-btn" bindtap="{{O}}">编辑店铺</button><button wx:if="{{P}}" class="shop-btn secondary-btn" bindtap="{{Q}}">推广店铺</button><button wx:if="{{R}}" class="shop-btn secondary-btn" open-type="share" data-type="share">转发</button><button wx:if="{{S}}" class="shop-btn primary-btn" bindtap="{{T}}">查看详情</button><button wx:if="{{U}}" class="shop-btn secondary-btn" bindtap="{{V}}">去逛逛</button></view></view><view class="activities-card"><view class="section-header"><text class="section-title">{{W}}</text><button wx:if="{{X}}" class="add-btn" bindtap="{{Y}}">添加活动</button></view><block wx:if="{{Z}}"><view class="activity-list"><view wx:for="{{aa}}" wx:for-item="act" wx:key="j" class="activity-item" bindtap="{{act.k}}"><image class="activity-image" src="{{act.a}}" mode="aspectFill"></image><view class="activity-content"><view class="activity-header"><text class="activity-title">{{act.b}}</text><text class="activity-time">{{act.c}}</text></view><text class="activity-desc">{{act.d}}</text></view><view class="activity-actions"><button wx:if="{{ab}}" class="activity-btn" data-type="edit" catchtap="{{act.e}}">编辑</button><button wx:if="{{ac}}" class="activity-btn" data-type="promote" catchtap="{{act.f}}">推广</button><button wx:if="{{ad}}" class="activity-btn" data-type="share" open-type="share" data-activity="{{act.g}}">转发</button><button wx:if="{{ae}}" class="activity-btn primary" catchtap="{{act.h}}">参与</button><button wx:if="{{af}}" class="activity-btn" data-type="share" open-type="share" data-activity="{{act.i}}">分享</button></view></view></view></block><view wx:else class="empty-state"><image class="empty-icon" src="{{ag}}" mode="aspectFit"></image><text class="empty-text">暂无活动</text></view></view></block><view wx:if="{{ah}}" class="empty-card"><image class="empty-icon large" src="{{ai}}" mode="aspectFit"></image><text class="empty-text">{{aj}}</text><button wx:if="{{ak}}" class="create-btn" bindtap="{{al}}">创建店铺</button></view></scroll-view></swiper-item><swiper-item><scroll-view scroll-y class="tab-scroll" bindscrolltolower="{{aD}}" refresher-enabled refresher-triggered="{{aE}}" bindrefresherrefresh="{{aF}}"><block wx:if="{{ap}}"><view class="published-list"><view wx:for="{{aq}}" wx:for-item="item" wx:key="m" class="published-item"><view class="publish-header"><text class="publish-type">{{item.a}}</text><text class="publish-date">{{item.b}}</text></view><view class="publish-content" bindtap="{{item.g}}"><text class="publish-title">{{item.c}}</text><text class="publish-desc">{{item.d}}</text><view wx:if="{{item.e}}" class="publish-images"><image wx:for="{{item.f}}" wx:for-item="img" wx:key="a" src="{{img.b}}" mode="aspectFill" class="publish-image"></image></view></view><view class="publish-actions"><button wx:if="{{ar}}" class="activity-btn" data-type="edit" catchtap="{{item.h}}">编辑</button><button wx:if="{{as}}" class="activity-btn" data-type="promote" catchtap="{{item.i}}">推广</button><button wx:if="{{at}}" class="activity-btn" data-type="share" open-type="share" data-item="{{item.j}}">转发</button><button wx:if="{{av}}" class="activity-btn primary" catchtap="{{item.k}}">联系</button><button wx:if="{{aw}}" class="activity-btn" data-type="share" open-type="share" data-item="{{item.l}}">分享</button></view></view><view wx:if="{{ax}}" class="add-more"><button class="add-btn" bindtap="{{ay}}"><text class="add-icon">+</text><text>发布新内容</text></button></view></view></block><view wx:else class="empty-card"><image class="empty-icon" src="{{az}}" mode="aspectFit"></image><text class="empty-text">{{aA}}</text><button wx:if="{{aB}}" class="add-btn" bindtap="{{aC}}">发布新内容</button></view></scroll-view></swiper-item></swiper></scroll-view><view wx:if="{{aK}}" class="custom-modal"><view class="modal-mask" bindtap="{{aL}}"></view><view class="modal-content"><view class="modal-title">{{aM}}</view><view class="modal-body"><view class="modal-text">刷新需要支付费用，刷新后{{aN}}将获得更多曝光</view><view class="price-text">本次刷新需支付<text class="price-amount">2元</text></view><view class="rank-hint"><image class="rank-icon" src="{{aO}}" mode="aspectFit"></image><text>刷新后将在所有付费置顶中排名第一位</text></view><view class="special-hint">购买刷新套餐更划算！</view></view><view class="modal-footer"><button class="modal-btn cancel-btn" bindtap="{{aQ}}">{{aP}}</button><button class="modal-btn confirm-btn" bindtap="{{aS}}">{{aR}}</button></view></view></view></view>