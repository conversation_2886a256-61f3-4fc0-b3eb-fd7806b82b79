<template>
  <view class="withdraw-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">佣金提现</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 可提现金额卡片 -->
    <view class="balance-card">
      <view class="balance-header">
        <text class="balance-label">可提现佣金</text>
      </view>
      
      <view class="balance-amount">
        <text class="currency">¥</text>
        <text class="amount">{{formatCommission(availableCommission)}}</text>
      </view>
      
      <view class="balance-tips">
        <text>提现将在1-3个工作日内到账</text>
      </view>
    </view>
    
    <!-- 提现表单 -->
    <view class="form-card">
      <view class="form-header">
        <text class="form-title">提现申请</text>
      </view>
      
      <view class="form-content">
        <view class="form-item">
          <text class="form-label">提现金额</text>
          <view class="amount-input-wrap">
            <text class="currency-symbol">¥</text>
            <input 
              class="amount-input" 
              type="digit" 
              v-model="formData.amount" 
              placeholder="请输入提现金额" 
              @input="validateAmount"
            />
          </view>
          <view class="amount-tips">
            <text>最低提现金额{{withdrawalSettings.minAmount}}元</text>
            <text class="all-amount" @click="withdrawAll">全部提现</text>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">提现方式</text>
          <view class="method-options">
            <view 
              v-for="(method, index) in withdrawMethods" 
              :key="index" 
              class="method-option" 
              :class="{ 'active': formData.method === method.value }"
              @click="selectMethod(method.value)"
            >
              <view class="method-icon" :class="method.value"></view>
              <text class="method-name">{{method.name}}</text>
            </view>
          </view>
        </view>
        
        <view class="form-item" v-if="formData.method === 'wechat'">
          <text class="form-label">微信号</text>
          <input class="form-input" type="text" v-model="formData.account" placeholder="请输入微信号" />
        </view>
        
        <view class="form-item" v-if="formData.method === 'alipay'">
          <text class="form-label">支付宝账号</text>
          <input class="form-input" type="text" v-model="formData.account" placeholder="请输入支付宝账号" />
        </view>
        
        <view class="form-item" v-if="formData.method === 'bank'">
          <text class="form-label">银行卡号</text>
          <input class="form-input" type="text" v-model="formData.account" placeholder="请输入银行卡号" />
        </view>
        
        <view class="form-item" v-if="formData.method === 'bank'">
          <text class="form-label">开户行</text>
          <input class="form-input" type="text" v-model="formData.bankName" placeholder="请输入开户行" />
        </view>
        
        <view class="form-item">
          <text class="form-label">真实姓名</text>
          <input class="form-input" type="text" v-model="formData.realName" placeholder="请输入真实姓名" />
        </view>
        
        <view class="form-item">
          <text class="form-label">手机号码</text>
          <input class="form-input" type="number" v-model="formData.mobile" placeholder="请输入手机号码" maxlength="11" />
        </view>
      </view>
      
      <view class="form-footer">
        <view class="fee-info">
          <text>手续费：{{formData.amount ? (formData.amount * withdrawalSettings.feeRate).toFixed(2) : '0.00'}}元 ({{withdrawalSettings.feeRate * 100}}%)</text>
          <text>实际到账：{{formData.amount ? (formData.amount * (1 - withdrawalSettings.feeRate)).toFixed(2) : '0.00'}}元</text>
        </view>
        
        <button class="submit-btn" :disabled="!canSubmit" :class="{ 'disabled': !canSubmit }" @click="submitWithdrawal">确认提现</button>
      </view>
    </view>
    
    <!-- 提现记录 -->
    <view class="records-card">
      <view class="records-header">
        <text class="records-title">提现记录</text>
        <view class="view-all" @click="navigateTo('/subPackages/distribution/pages/commission')">查看全部</view>
      </view>
      
      <view class="records-list" v-if="withdrawalRecords.length > 0">
        <view 
          v-for="(record, index) in withdrawalRecords" 
          :key="index" 
          class="record-item"
        >
          <view class="record-info">
            <view class="record-main">
              <text class="record-type">提现到{{getMethodName(record.method)}}</text>
              <text class="record-amount">-¥{{formatCommission(record.amount)}}</text>
            </view>
            
            <view class="record-sub">
              <text class="record-time">{{formatTime(record.createdAt)}}</text>
              <text class="record-status" :class="getStatusClass(record.status)">{{getStatusText(record.status)}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="empty-records" v-else>
        <text>暂无提现记录</text>
      </view>
    </view>
    
    <!-- 提现说明 -->
    <view class="tips-card">
      <view class="tips-header">
        <text class="tips-title">提现说明</text>
      </view>
      
      <view class="tips-content">
        <view class="tip-item">
          <view class="tip-icon"></view>
          <text class="tip-text">提现金额最低{{withdrawalSettings.minAmount}}元，最高{{withdrawalSettings.maxAmount}}元</text>
        </view>
        
        <view class="tip-item">
          <view class="tip-icon"></view>
          <text class="tip-text">提现手续费为提现金额的{{withdrawalSettings.feeRate * 100}}%</text>
        </view>
        
        <view class="tip-item">
          <view class="tip-icon"></view>
          <text class="tip-text">提现申请将在1-3个工作日内处理，请耐心等待</text>
        </view>
        
        <view class="tip-item">
          <view class="tip-icon"></view>
          <text class="tip-text">提现账户必须与实名认证信息一致</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import distributionService from '@/utils/distributionService';

// 可提现佣金
const availableCommission = ref(0);

// 提现设置
const withdrawalSettings = reactive({
  minAmount: 50,
  maxAmount: 5000,
  feeRate: 0.01,
  withdrawalCycle: 'weekly',
  withdrawalDays: [1, 4], // 周一和周四可提现
  autoApprove: false,
  needRealName: true,
  withdrawalMethods: ['wechat', 'alipay', 'bank']
});

// 提现方式
const withdrawMethods = [
  { name: '微信', value: 'wechat' },
  { name: '支付宝', value: 'alipay' },
  { name: '银行卡', value: 'bank' }
];

// 表单数据
const formData = reactive({
  amount: '',
  method: 'wechat',
  account: '',
  bankName: '',
  realName: '',
  mobile: ''
});

// 提现记录
const withdrawalRecords = ref([]);

// 是否可以提交
const canSubmit = computed(() => {
  const amountValid = formData.amount && 
                      parseFloat(formData.amount) >= withdrawalSettings.minAmount &&
                      parseFloat(formData.amount) <= Math.min(withdrawalSettings.maxAmount, availableCommission.value);
  
  const accountValid = formData.account.trim() !== '';
  const bankNameValid = formData.method !== 'bank' || formData.bankName.trim() !== '';
  const realNameValid = formData.realName.trim() !== '';
  const mobileValid = formData.mobile.trim() !== '' && formData.mobile.length === 11;
  
  return amountValid && accountValid && bankNameValid && realNameValid && mobileValid;
});

// 页面加载
onMounted(async () => {
  // 获取可提现佣金
  await getAvailableCommission();
  
  // 获取提现设置
  await getWithdrawalSettings();
  
  // 获取提现记录
  await getWithdrawalRecords();
});

// 获取可提现佣金
const getAvailableCommission = async () => {
  try {
    const info = await distributionService.getDistributorInfo({
      userId: '1001' // 模拟数据，实际应从用户系统获取
    });
    
    if (info) {
      availableCommission.value = info.availableCommission;
    }
  } catch (error) {
    console.error('获取可提现佣金失败', error);
    uni.showToast({
      title: '获取可提现佣金失败',
      icon: 'none'
    });
  }
};

// 获取提现设置
const getWithdrawalSettings = async () => {
  try {
    const settings = await distributionService.getWithdrawalSettings();
    
    if (settings) {
      Object.assign(withdrawalSettings, settings);
    }
  } catch (error) {
    console.error('获取提现设置失败', error);
  }
};

// 获取提现记录
const getWithdrawalRecords = async () => {
  try {
    // 这里应该调用API获取提现记录
    // 暂时使用模拟数据
    withdrawalRecords.value = [
      {
        id: 'W1001',
        amount: 100,
        fee: 1,
        actualAmount: 99,
        method: 'wechat',
        account: 'wx123456',
        status: 'pending',
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 'W1002',
        amount: 200,
        fee: 2,
        actualAmount: 198,
        method: 'alipay',
        account: '<EMAIL>',
        status: 'success',
        createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString()
      }
    ];
  } catch (error) {
    console.error('获取提现记录失败', error);
  }
};

// 验证提现金额
const validateAmount = () => {
  if (!formData.amount) return;
  
  let amount = parseFloat(formData.amount);
  
  // 限制小数点后两位
  formData.amount = amount.toFixed(2);
  
  // 限制最大金额
  if (amount > Math.min(withdrawalSettings.maxAmount, availableCommission.value)) {
    formData.amount = Math.min(withdrawalSettings.maxAmount, availableCommission.value).toFixed(2);
    uni.showToast({
      title: `提现金额不能超过${formData.amount}元`,
      icon: 'none'
    });
  }
  
  // 限制最小金额
  if (amount < withdrawalSettings.minAmount) {
    uni.showToast({
      title: `提现金额不能低于${withdrawalSettings.minAmount}元`,
      icon: 'none'
    });
  }
};

// 全部提现
const withdrawAll = () => {
  formData.amount = Math.min(withdrawalSettings.maxAmount, availableCommission.value).toFixed(2);
};

// 选择提现方式
const selectMethod = (method) => {
  formData.method = method;
  formData.account = ''; // 切换方式时清空账号
  if (method !== 'bank') {
    formData.bankName = '';
  }
};

// 提交提现申请
const submitWithdrawal = async () => {
  if (!canSubmit.value) return;
  
  try {
    uni.showLoading({
      title: '提交中...',
      mask: true
    });
    
    const result = await distributionService.applyWithdrawal({
      amount: parseFloat(formData.amount),
      method: formData.method,
      account: formData.account,
      bankName: formData.bankName,
      realName: formData.realName,
      mobile: formData.mobile
    });
    
    uni.hideLoading();
    
    if (result.success) {
      uni.showModal({
        title: '提现申请成功',
        content: '您的提现申请已提交，请耐心等待处理。',
        showCancel: false,
        success: () => {
          // 刷新数据
          getAvailableCommission();
          getWithdrawalRecords();
          
          // 清空表单
          formData.amount = '';
        }
      });
    } else {
      uni.showModal({
        title: '提现申请失败',
        content: result.message || '请稍后再试',
        showCancel: false
      });
    }
  } catch (error) {
    uni.hideLoading();
    console.error('提交提现申请失败', error);
    uni.showToast({
      title: '提交提现申请失败',
      icon: 'none'
    });
  }
};

// 获取提现方式名称
const getMethodName = (method) => {
  const methodMap = {
    'wechat': '微信',
    'alipay': '支付宝',
    'bank': '银行卡'
  };
  return methodMap[method] || method;
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '处理中',
    'success': '已到账',
    'failed': '提现失败'
  };
  return statusMap[status] || status;
};

// 获取状态样式类
const getStatusClass = (status) => {
  return {
    'pending': 'status-pending',
    'success': 'status-success',
    'failed': 'status-failed'
  }[status] || '';
};

// 格式化佣金
const formatCommission = (amount) => {
  return distributionService.formatCommission(amount);
};

// 格式化时间
const formatTime = (time) => {
  if (!time) return '';
  
  const date = new Date(time);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 页面导航
const navigateTo = (url) => {
  uni.navigateTo({ url });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 显示帮助
const showHelp = () => {
  uni.showModal({
    title: '提现帮助',
    content: `提现金额最低${withdrawalSettings.minAmount}元，最高${withdrawalSettings.maxAmount}元。提现手续费为提现金额的${withdrawalSettings.feeRate * 100}%。提现申请将在1-3个工作日内处理，请耐心等待。`,
    showCancel: false
  });
};
</script>

<style lang="scss">
.withdraw-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 88rpx 32rpx 30rpx;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);
}

.navbar-back {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24rpx;
  height: 24rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.navbar-right {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

/* 可提现金额卡片 */
.balance-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  text-align: center;
}

.balance-header {
  margin-bottom: 20rpx;
}

.balance-label {
  font-size: 28rpx;
  color: #666;
}

.balance-amount {
  margin-bottom: 20rpx;
}

.currency {
  font-size: 36rpx;
  font-weight: 600;
  color: #FF5722;
}

.amount {
  font-size: 60rpx;
  font-weight: 600;
  color: #FF5722;
}

.balance-tips {
  font-size: 24rpx;
  color: #999;
}

/* 表单卡片 */
.form-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.form-header {
  margin-bottom: 30rpx;
}

.form-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.form-content {
  margin-bottom: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.amount-input-wrap {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 0 20rpx;
  height: 80rpx;
}

.currency-symbol {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 10rpx;
}

.amount-input {
  flex: 1;
  height: 100%;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.amount-tips {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #999;
}

.all-amount {
  color: #6B0FBE;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.method-options {
  display: flex;
  margin: 0 -10rpx;
}

.method-option {
  flex: 1;
  margin: 0 10rpx;
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 2rpx solid transparent;
}

.method-option.active {
  border-color: #6B0FBE;
  background: rgba(107, 15, 190, 0.05);
}

.method-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
  border-radius: 40rpx;
}

.method-icon.wechat {
  background-color: #07C160;
}

.method-icon.alipay {
  background-color: #1677FF;
}

.method-icon.bank {
  background-color: #FF9500;
}

.method-name {
  font-size: 26rpx;
  color: #333;
}

.form-footer {
  padding-top: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.fee-info {
  display: flex;
  flex-direction: column;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.submit-btn {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 32rpx;
  padding: 20rpx 0;
  line-height: 1.5;
  width: 100%;
}

.submit-btn.disabled {
  background: #cccccc;
  color: #ffffff;
}

/* 提现记录 */
.records-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.records-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.records-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.view-all {
  font-size: 26rpx;
  color: #6B0FBE;
}

.record-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-info {
  display: flex;
  flex-direction: column;
}

.record-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.record-type {
  font-size: 28rpx;
  color: #333;
}

.record-amount {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.record-sub {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

.record-status {
  font-size: 24rpx;
}

.record-status.status-pending {
  color: #FF9500;
}

.record-status.status-success {
  color: #34C759;
}

.record-status.status-failed {
  color: #FF3B30;
}

.empty-records {
  text-align: center;
  padding: 30rpx 0;
  font-size: 26rpx;
  color: #999;
}

/* 提现说明 */
.tips-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.tips-header {
  margin-bottom: 20rpx;
}

.tips-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.tips-content {
  margin-bottom: 20rpx;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  width: 16rpx;
  height: 16rpx;
  border-radius: 8rpx;
  background-color: #6B0FBE;
  margin-right: 16rpx;
  margin-top: 10rpx;
  flex-shrink: 0;
}

.tip-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}
</style> 