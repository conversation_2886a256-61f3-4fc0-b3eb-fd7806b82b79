"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  const _component_line = common_vendor.resolveComponent("line");
  (_component_path + _component_circle + _component_svg + _component_polyline + _component_line)();
}
const _sfc_main = {
  __name: "index",
  setup(__props, { expose: __expose }) {
    const posterStyles = common_vendor.ref(["样式一", "样式二", "样式三"]);
    const currentStyle = common_vendor.ref(0);
    const currentProduct = common_vendor.ref(0);
    const posterUrls = common_vendor.ref([
      "/static/images/distribution/poster-1.png",
      "/static/images/distribution/poster-2.png",
      "/static/images/distribution/poster-3.png"
    ]);
    const currentPosterUrl = common_vendor.computed(() => {
      return posterUrls.value[currentStyle.value];
    });
    const products = common_vendor.ref([
      {
        id: 1,
        name: "Apple iPhone 14 Pro Max 256GB 暗夜紫 移动联通电信5G双卡双待手机",
        image: "https://via.placeholder.com/120",
        price: "8999.00",
        originalPrice: "9999.00",
        commission: "300.00"
      },
      {
        id: 2,
        name: "小米12S Ultra 12GB+256GB 丹青黑 骁龙8+旗舰处理器 徕卡专业光学镜头",
        image: "https://via.placeholder.com/120",
        price: "5999.00",
        originalPrice: "6999.00",
        commission: "200.00"
      },
      {
        id: 3,
        name: "华为Mate 50 Pro 8GB+256GB 曜金黑 超光变XMAGE影像 北斗卫星消息",
        image: "https://via.placeholder.com/120",
        price: "6799.00",
        originalPrice: "7299.00",
        commission: "250.00"
      }
    ]);
    const promotionTexts = common_vendor.ref([
      {
        id: 1,
        content: "🔥【限时特惠】iPhone 14 Pro Max 立省1000元！全网最低价，手慢无！点击链接立即抢购：https://t.cn/A6JxYZ7p"
      },
      {
        id: 2,
        content: "✨ 想要高性价比的旗舰手机？小米12S Ultra 现在只要5999元！徕卡专业光学镜头，拍照效果堪比单反！戳我购买：https://t.cn/A6JxYZ7p"
      },
      {
        id: 3,
        content: "💯 华为Mate 50 Pro 新品首发特惠！支持北斗卫星消息，紧急情况也能发短信！现在下单还送豪华配件大礼包！速戳：https://t.cn/A6JxYZ7p"
      }
    ]);
    function switchStyle(index) {
      currentStyle.value = index;
    }
    function selectProduct(index) {
      currentProduct.value = index;
    }
    function previewPoster() {
      common_vendor.index.previewImage({
        urls: [currentPosterUrl.value],
        current: currentPosterUrl.value
      });
    }
    function savePoster() {
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "保存成功",
          icon: "success"
        });
      }, 1500);
    }
    function copyText(text) {
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success"
          });
        }
      });
    }
    function navigateTo(url) {
      common_vendor.index.navigateTo({ url });
    }
    __expose({
      onShareAppMessage() {
        return {
          title: products.value[currentProduct.value].name,
          path: `/subPackages/activity-showcase/pages/detail/index?id=${products.value[currentProduct.value].id}&source=distribution`,
          imageUrl: currentPosterUrl.value
        };
      }
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.f(posterStyles.value, (style, index, i0) => {
          return {
            a: common_vendor.t(style),
            b: index,
            c: currentStyle.value === index ? 1 : "",
            d: common_vendor.o(($event) => switchStyle(index), index),
            e: currentStyle.value === index ? "#FFFFFF" : "#666666",
            f: currentStyle.value === index ? "#AC39FF" : "#F8F8F8",
            g: index < posterStyles.value.length - 1 ? "1rpx solid #EFEFEF" : "none"
          };
        }),
        b: currentPosterUrl.value,
        c: common_assets._imports_0$60,
        d: common_vendor.p({
          d: "M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z",
          stroke: "#AC39FF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        e: common_vendor.p({
          cx: "12",
          cy: "12",
          r: "3",
          stroke: "#AC39FF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        f: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "20",
          height: "20"
        }),
        g: common_vendor.o(previewPoster),
        h: common_vendor.p({
          d: "M19 21H5a2 2 0 01-2-2V5a2 2 0 012-2h11l5 5v11a2 2 0 01-2 2z",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        i: common_vendor.p({
          points: "17 21 17 13 7 13 7 21",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        j: common_vendor.p({
          points: "7 3 7 8 15 8",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        k: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "20",
          height: "20"
        }),
        l: common_vendor.o(savePoster),
        m: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#AC39FF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        n: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        o: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/distribution/products/index")),
        p: common_vendor.f(products.value, (product, index, i0) => {
          return common_vendor.e({
            a: product.image,
            b: common_vendor.t(product.name),
            c: common_vendor.t(product.price),
            d: common_vendor.t(product.originalPrice),
            e: common_vendor.t(product.commission),
            f: currentProduct.value === index
          }, currentProduct.value === index ? {
            g: "7a8142ff-10-" + i0 + "," + ("7a8142ff-9-" + i0),
            h: common_vendor.p({
              d: "M5 12l5 5L20 7",
              stroke: "#FFFFFF",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            i: "7a8142ff-9-" + i0,
            j: common_vendor.p({
              viewBox: "0 0 24 24",
              width: "16",
              height: "16"
            })
          } : {}, {
            k: currentProduct.value === index ? "0" : "1rpx solid #CCCCCC",
            l: currentProduct.value === index ? "#AC39FF" : "transparent",
            m: index,
            n: currentProduct.value === index ? 1 : "",
            o: common_vendor.o(($event) => selectProduct(index), index),
            p: currentProduct.value === index ? "rgba(172,57,255,0.1)" : "#F8F8F8",
            q: currentProduct.value === index ? "1rpx solid #AC39FF" : "1rpx solid transparent"
          });
        }),
        q: common_vendor.f(promotionTexts.value, (text, index, i0) => {
          return {
            a: common_vendor.t(text.content),
            b: common_vendor.o(($event) => copyText(text.content), index),
            c: index
          };
        }),
        r: common_vendor.p({
          cx: "18",
          cy: "5",
          r: "3",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        s: common_vendor.p({
          cx: "6",
          cy: "12",
          r: "3",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        t: common_vendor.p({
          cx: "18",
          cy: "19",
          r: "3",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        v: common_vendor.p({
          x1: "8.59",
          y1: "13.51",
          x2: "15.42",
          y2: "17.49",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        w: common_vendor.p({
          x1: "15.41",
          y1: "6.51",
          x2: "8.59",
          y2: "10.49",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        x: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "20",
          height: "20"
        })
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-7a8142ff"]]);
_sfc_main.__runtimeHooks = 2;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/distribution/poster/index.js.map
