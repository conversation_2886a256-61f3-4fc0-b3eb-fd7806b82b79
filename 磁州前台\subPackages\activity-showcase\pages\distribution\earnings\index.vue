<template>
  <view class="earnings-container">
    <!-- 头部统计卡片 -->
    <view class="statistics-card" :style="{
      borderRadius: '35px',
      boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
      background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)',
      padding: '30rpx',
      marginBottom: '30rpx',
      position: 'relative',
      overflow: 'hidden'
    }">
      <!-- 背景装饰 -->
      <view class="bg-decoration" :style="{
        position: 'absolute',
        top: '-50rpx',
        right: '-50rpx',
        width: '300rpx',
        height: '300rpx',
        borderRadius: '50%',
        background: 'rgba(255,255,255,0.1)',
        zIndex: '1'
      }"></view>
      <view class="bg-decoration" :style="{
        position: 'absolute',
        bottom: '-80rpx',
        left: '-80rpx',
        width: '250rpx',
        height: '250rpx',
        borderRadius: '50%',
        background: 'rgba(255,255,255,0.08)',
        zIndex: '1'
      }"></view>
      
      <!-- 总收益 -->
      <view class="total-earnings" :style="{
        position: 'relative',
        zIndex: '2'
      }">
        <text class="label" :style="{
          fontSize: '28rpx',
          color: 'rgba(255,255,255,0.9)',
          marginBottom: '10rpx',
          display: 'block'
        }">总收益(元)</text>
        <text class="value" :style="{
          fontSize: '60rpx',
          fontWeight: 'bold',
          color: '#FFFFFF',
          display: 'block',
          textShadow: '0 2rpx 4rpx rgba(0,0,0,0.1)'
        }">{{ totalEarnings }}</text>
      </view>
      
      <!-- 收益统计 -->
      <view class="earnings-stats" :style="{
        display: 'flex',
        justifyContent: 'space-around',
        marginTop: '30rpx',
        position: 'relative',
        zIndex: '2',
        background: 'rgba(255,255,255,0.1)',
        borderRadius: '20rpx',
        padding: '20rpx 0'
      }">
        <view class="stat-item">
          <text class="stat-value" :style="{
            fontSize: '32rpx',
            fontWeight: 'bold',
            color: '#FFFFFF',
            display: 'block',
            textAlign: 'center'
          }">{{ waitingEarnings }}</text>
          <text class="stat-label" :style="{
            fontSize: '24rpx',
            color: 'rgba(255,255,255,0.8)',
            display: 'block',
            textAlign: 'center'
          }">待结算</text>
        </view>
        
        <view class="stat-item">
          <text class="stat-value" :style="{
            fontSize: '32rpx',
            fontWeight: 'bold',
            color: '#FFFFFF',
            display: 'block',
            textAlign: 'center'
          }">{{ settledEarnings }}</text>
          <text class="stat-label" :style="{
            fontSize: '24rpx',
            color: 'rgba(255,255,255,0.8)',
            display: 'block',
            textAlign: 'center'
          }">已结算</text>
        </view>
        
        <view class="stat-item">
          <text class="stat-value" :style="{
            fontSize: '32rpx',
            fontWeight: 'bold',
            color: '#FFFFFF',
            display: 'block',
            textAlign: 'center'
          }">{{ withdrawnEarnings }}</text>
          <text class="stat-label" :style="{
            fontSize: '24rpx',
            color: 'rgba(255,255,255,0.8)',
            display: 'block',
            textAlign: 'center'
          }">已提现</text>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="action-buttons" :style="{
        display: 'flex',
        justifyContent: 'space-between',
        marginTop: '30rpx',
        position: 'relative',
        zIndex: '2'
      }">
        <view class="action-btn withdraw-btn" @click="navigateTo('/subPackages/activity-showcase/pages/distribution/withdraw/index')" :style="{
          flex: '1',
          margin: '0 10rpx',
          padding: '20rpx 0',
          background: 'rgba(255,255,255,0.2)',
          borderRadius: '35px',
          textAlign: 'center',
          color: '#FFFFFF',
          fontSize: '28rpx',
          fontWeight: '500',
          border: '1rpx solid rgba(255,255,255,0.3)'
        }">
          <text>申请提现</text>
        </view>
        
        <view class="action-btn record-btn" @click="navigateTo('/subPackages/activity-showcase/pages/distribution/records/index')" :style="{
          flex: '1',
          margin: '0 10rpx',
          padding: '20rpx 0',
          background: 'rgba(255,255,255,0.2)',
          borderRadius: '35px',
          textAlign: 'center',
          color: '#FFFFFF',
          fontSize: '28rpx',
          fontWeight: '500',
          border: '1rpx solid rgba(255,255,255,0.3)'
        }">
          <text>收益明细</text>
        </view>
      </view>
    </view>
    
    <!-- 收益趋势图 -->
    <view class="trend-card" :style="{
      borderRadius: '35px',
      boxShadow: '0 8px 20px rgba(0,0,0,0.08)',
      background: '#FFFFFF',
      padding: '30rpx',
      marginBottom: '30rpx'
    }">
      <view class="card-header">
        <text class="card-title" :style="{
          fontSize: '32rpx',
          fontWeight: '600',
          color: '#333333'
        }">收益趋势</text>
        
        <view class="time-filter">
          <view 
            v-for="(period, index) in timePeriods" 
            :key="index"
            class="filter-item"
            :class="{ active: currentPeriod === index }"
            @click="switchPeriod(index)"
            :style="{
              fontSize: '24rpx',
              color: currentPeriod === index ? '#AC39FF' : '#666666',
              padding: '6rpx 16rpx',
              borderRadius: '20rpx',
              marginLeft: '10rpx',
              background: currentPeriod === index ? 'rgba(172,57,255,0.1)' : 'transparent'
            }"
          >
            {{ period }}
          </view>
        </view>
      </view>
      
      <!-- 图表区域 -->
      <view class="chart-area" :style="{
        height: '400rpx',
        marginTop: '30rpx'
      }">
        <!-- 这里可以集成第三方图表库，如ECharts或F2 -->
        <view class="chart-placeholder" :style="{
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: '#F8F8F8',
          borderRadius: '20rpx'
        }">
          <text :style="{ color: '#999999' }">收益趋势图表</text>
        </view>
      </view>
    </view>
    
    <!-- 收益来源 -->
    <view class="source-card" :style="{
      borderRadius: '35px',
      boxShadow: '0 8px 20px rgba(0,0,0,0.08)',
      background: '#FFFFFF',
      padding: '30rpx',
      marginBottom: '30rpx'
    }">
      <view class="card-header">
        <text class="card-title" :style="{
          fontSize: '32rpx',
          fontWeight: '600',
          color: '#333333'
        }">收益来源</text>
      </view>
      
      <!-- 饼图区域 -->
      <view class="pie-chart-area" :style="{
        height: '400rpx',
        marginTop: '30rpx',
        display: 'flex'
      }">
        <!-- 饼图占位 -->
        <view class="pie-placeholder" :style="{
          flex: '1',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: '#F8F8F8',
          borderRadius: '20rpx'
        }">
          <text :style="{ color: '#999999' }">收益来源分布图</text>
        </view>
        
        <!-- 图例 -->
        <view class="legend" :style="{
          marginLeft: '20rpx',
          width: '200rpx',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center'
        }">
          <view 
            v-for="(source, index) in incomeSources" 
            :key="index"
            class="legend-item"
            :style="{
              display: 'flex',
              alignItems: 'center',
              marginBottom: '15rpx'
            }"
          >
            <view class="color-dot" :style="{
              width: '20rpx',
              height: '20rpx',
              borderRadius: '50%',
              background: source.color,
              marginRight: '10rpx'
            }"></view>
            <text class="source-name" :style="{
              fontSize: '24rpx',
              color: '#666666'
            }">{{ source.name }}</text>
            <text class="source-percent" :style="{
              fontSize: '24rpx',
              color: '#333333',
              fontWeight: '500',
              marginLeft: 'auto'
            }">{{ source.percent }}%</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 收益规则 -->
    <view class="rules-card" :style="{
      borderRadius: '35px',
      boxShadow: '0 8px 20px rgba(0,0,0,0.08)',
      background: '#FFFFFF',
      padding: '30rpx',
      marginBottom: '30rpx'
    }">
      <view class="card-header">
        <text class="card-title" :style="{
          fontSize: '32rpx',
          fontWeight: '600',
          color: '#333333'
        }">收益规则</text>
      </view>
      
      <view class="rules-content" :style="{
        marginTop: '20rpx'
      }">
        <view 
          v-for="(rule, index) in earningsRules" 
          :key="index"
          class="rule-item"
          :style="{
            marginBottom: '20rpx',
            paddingBottom: '20rpx',
            borderBottom: index < earningsRules.length - 1 ? '1rpx solid #F2F2F7' : 'none'
          }"
        >
          <view class="rule-title" :style="{
            display: 'flex',
            alignItems: 'center',
            marginBottom: '10rpx'
          }">
            <view class="rule-icon" :style="{
              width: '40rpx',
              height: '40rpx',
              borderRadius: '50%',
              background: 'rgba(172,57,255,0.1)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '10rpx'
            }">
              <text :style="{
                color: '#AC39FF',
                fontSize: '24rpx',
                fontWeight: '500'
              }">{{ index + 1 }}</text>
            </view>
            <text :style="{
              fontSize: '28rpx',
              fontWeight: '500',
              color: '#333333'
            }">{{ rule.title }}</text>
          </view>
          
          <text class="rule-desc" :style="{
            fontSize: '26rpx',
            color: '#666666',
            lineHeight: '1.6',
            paddingLeft: '50rpx'
          }">{{ rule.description }}</text>
        </view>
      </view>
    </view>
    
    <!-- 底部安全区域 -->
    <view class="safe-area-bottom" :style="{
      height: '100rpx',
      paddingBottom: 'env(safe-area-inset-bottom)'
    }"></view>
  </view>
</template>

<script setup>
import { ref } from 'vue';

// 收益数据
const totalEarnings = ref('1,234.56');
const waitingEarnings = ref('123.45');
const settledEarnings = ref('1,111.11');
const withdrawnEarnings = ref('800.00');

// 时间周期
const timePeriods = ref(['7天', '30天', '90天', '全部']);
const currentPeriod = ref(1); // 默认选择30天

// 收益来源数据
const incomeSources = ref([
  { name: '直接佣金', percent: 65, color: '#AC39FF' },
  { name: '团队佣金', percent: 25, color: '#FF3B69' },
  { name: '活动奖励', percent: 10, color: '#FF9500' }
]);

// 收益规则
const earningsRules = ref([
  { 
    title: '佣金结算规则', 
    description: '订单完成后，佣金将在7天后自动结算到您的账户，可随时申请提现。' 
  },
  { 
    title: '提现规则', 
    description: '单笔提现金额不低于10元，每月可提现3次，超出次数将收取1%手续费。' 
  },
  { 
    title: '团队收益规则', 
    description: '您可获得直接下级分销订单的10%佣金，二级下级的5%佣金作为团队收益。' 
  }
]);

// 切换时间周期
function switchPeriod(index) {
  currentPeriod.value = index;
  // 这里可以添加加载对应时间段数据的逻辑
}

// 页面导航
function navigateTo(url) {
  uni.navigateTo({ url });
}
</script>

<style scoped>
.earnings-container {
  padding: 30rpx;
  background-color: #F2F2F7;
  min-height: 100vh;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.time-filter {
  display: flex;
}

.filter-item {
  transition: all 0.3s ease;
}

.filter-item.active {
  font-weight: 500;
}

.action-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
}
</style> 