{"version": 3, "file": "create-package-info.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/group/create-package-info.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xncm91cFxjcmVhdGUtcGFja2FnZS1pbmZvLnZ1ZQ"], "sourcesContent": ["<!-- 创建中 -->\n<template>\n  <view class=\"create-package-info-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">拼团活动</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 步骤指示器 -->\n    <view class=\"step-indicator\">\n      <view class=\"step-progress\">\n        <view class=\"step-progress-bar\" style=\"width: 40%\"></view>\n      </view>\n      <view class=\"step-text\">步骤 2/5</view>\n    </view>\n    \n    <!-- 页面内容 -->\n    <scroll-view scroll-y class=\"page-content\">\n      <view class=\"page-title\">填写套餐基本信息</view>\n      <view class=\"page-subtitle\">请填写团购套餐的基本信息</view>\n      \n      <view class=\"form-section\">\n        <view class=\"form-item\">\n          <text class=\"form-label\">套餐名称 <text class=\"required\">*</text></text>\n          <input class=\"form-input\" type=\"text\" v-model=\"packageInfo.name\" placeholder=\"请输入套餐名称，如：四菜一汤家庭套餐\" maxlength=\"30\" />\n          <text class=\"input-counter\">{{packageInfo.name.length}}/30</text>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"form-label\">套餐描述</text>\n          <textarea class=\"form-textarea\" v-model=\"packageInfo.description\" placeholder=\"请输入套餐描述信息，如：适合3-4人用餐，荤素搭配，营养均衡\" maxlength=\"100\"></textarea>\n          <text class=\"input-counter\">{{packageInfo.description.length}}/100</text>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"form-label\">套餐分类 <text class=\"required\">*</text></text>\n          <picker mode=\"selector\" :range=\"categoryOptions\" @change=\"onCategoryChange\" class=\"form-picker\">\n            <view class=\"picker-value\">\n              <text v-if=\"packageInfo.category\">{{categoryOptions[packageInfo.categoryIndex]}}</text>\n              <text v-else class=\"placeholder\">请选择套餐分类</text>\n              <view class=\"picker-arrow\"></view>\n            </view>\n          </picker>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"form-label\">成团人数 <text class=\"required\">*</text></text>\n          <view class=\"number-picker\">\n            <view class=\"number-btn minus\" @tap=\"decrementGroupSize\">-</view>\n            <input class=\"number-input\" type=\"number\" v-model=\"packageInfo.groupSize\" />\n            <view class=\"number-btn plus\" @tap=\"incrementGroupSize\">+</view>\n            <text class=\"unit-text\">人</text>\n          </view>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"form-label\">活动有效期 <text class=\"required\">*</text></text>\n          <view class=\"date-range-picker\">\n            <picker mode=\"date\" :value=\"packageInfo.startDate\" @change=\"onStartDateChange\" class=\"date-picker\">\n              <view class=\"date-picker-value\">\n                <text v-if=\"packageInfo.startDate\">{{packageInfo.startDate}}</text>\n                <text v-else class=\"placeholder\">开始日期</text>\n              </view>\n            </picker>\n            <text class=\"date-separator\">至</text>\n            <picker mode=\"date\" :value=\"packageInfo.endDate\" @change=\"onEndDateChange\" class=\"date-picker\">\n              <view class=\"date-picker-value\">\n                <text v-if=\"packageInfo.endDate\">{{packageInfo.endDate}}</text>\n                <text v-else class=\"placeholder\">结束日期</text>\n              </view>\n            </picker>\n          </view>\n        </view>\n      </view>\n    </scroll-view>\n    \n    <!-- 底部按钮 -->\n    <view class=\"footer-buttons\">\n      <button class=\"btn btn-secondary\" @click=\"goBack\">上一步</button>\n      <button class=\"btn btn-primary\" @click=\"nextStep\">下一步</button>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      packageInfo: {\n        name: '',\n        description: '',\n        category: '',\n        categoryIndex: 0,\n        groupSize: 2,\n        startDate: '',\n        endDate: ''\n      },\n      categoryOptions: ['餐饮美食', '休闲娱乐', '美容美发', '生活服务', '其他']\n    }\n  },\n  onLoad() {\n    // 尝试从本地存储获取之前保存的数据\n    try {\n      const packageType = uni.getStorageSync('packageType');\n      const savedInfo = uni.getStorageSync('packageInfo');\n      \n      if (packageType) {\n        console.log('获取到套餐类型:', packageType);\n      }\n      \n      if (savedInfo) {\n        this.packageInfo = JSON.parse(savedInfo);\n      }\n    } catch (e) {\n      console.error('读取本地存储失败:', e);\n    }\n  },\n  methods: {\n    goBack() {\n      // 保存当前页面数据\n      this.saveData();\n      uni.navigateBack();\n    },\n    nextStep() {\n      // 表单验证\n      if (!this.packageInfo.name) {\n        uni.showToast({\n          title: '请输入套餐名称',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      if (!this.packageInfo.category) {\n        uni.showToast({\n          title: '请选择套餐分类',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      if (!this.packageInfo.startDate || !this.packageInfo.endDate) {\n        uni.showToast({\n          title: '请选择活动有效期',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 保存数据并跳转到下一步\n      this.saveData();\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-price'\n      });\n    },\n    saveData() {\n      // 保存当前页面数据到本地存储\n      try {\n        uni.setStorageSync('packageInfo', JSON.stringify(this.packageInfo));\n      } catch (e) {\n        console.error('保存数据失败:', e);\n      }\n    },\n    onCategoryChange(e) {\n      const index = e.detail.value;\n      this.packageInfo.categoryIndex = index;\n      this.packageInfo.category = this.categoryOptions[index];\n    },\n    onStartDateChange(e) {\n      this.packageInfo.startDate = e.detail.value;\n    },\n    onEndDateChange(e) {\n      this.packageInfo.endDate = e.detail.value;\n    },\n    decrementGroupSize() {\n      if (this.packageInfo.groupSize > 2) {\n        this.packageInfo.groupSize--;\n      }\n    },\n    incrementGroupSize() {\n      if (this.packageInfo.groupSize < 100) {\n        this.packageInfo.groupSize++;\n      }\n    },\n    showHelp() {\n      uni.showToast({\n        title: '帮助信息',\n        icon: 'none'\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.create-package-info-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  display: flex;\n  flex-direction: column;\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #9040FF, #5E35B1);\n  color: #fff;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 20px;\n  height: 20px;\n  border-radius: 50%;\n  border: 1px solid #fff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  color: #fff;\n}\n\n/* 步骤指示器 */\n.step-indicator {\n  padding: 15px;\n  background: #FFFFFF;\n}\n\n.step-progress {\n  height: 4px;\n  background-color: #EBEDF5;\n  border-radius: 2px;\n  margin-bottom: 5px;\n  position: relative;\n}\n\n.step-progress-bar {\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 100%;\n  background: linear-gradient(90deg, #9040FF, #5E35B1);\n  border-radius: 2px;\n}\n\n.step-text {\n  font-size: 12px;\n  color: #999;\n  text-align: right;\n}\n\n/* 页面内容 */\n.page-content {\n  flex: 1;\n  padding: 20px 15px;\n}\n\n.page-title {\n  font-size: 20px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.page-subtitle {\n  font-size: 14px;\n  color: #999;\n  margin-bottom: 20px;\n}\n\n/* 表单样式 */\n.form-section {\n  background: #FFFFFF;\n  border-radius: 12px;\n  padding: 15px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n}\n\n.form-item {\n  margin-bottom: 20px;\n}\n\n.form-item:last-child {\n  margin-bottom: 0;\n}\n\n.form-label {\n  font-size: 14px;\n  color: #333;\n  margin-bottom: 8px;\n  display: block;\n}\n\n.required {\n  color: #FF3B30;\n}\n\n.form-input {\n  width: 100%;\n  height: 45px;\n  background: #F5F7FA;\n  border-radius: 8px;\n  padding: 0 12px;\n  font-size: 14px;\n  color: #333;\n  border: 1px solid #EBEDF5;\n}\n\n.form-textarea {\n  width: 100%;\n  height: 100px;\n  background: #F5F7FA;\n  border-radius: 8px;\n  padding: 12px;\n  font-size: 14px;\n  color: #333;\n  border: 1px solid #EBEDF5;\n}\n\n.input-counter {\n  font-size: 12px;\n  color: #999;\n  text-align: right;\n  margin-top: 5px;\n}\n\n.form-picker {\n  width: 100%;\n}\n\n.picker-value {\n  width: 100%;\n  height: 45px;\n  background: #F5F7FA;\n  border-radius: 8px;\n  padding: 0 12px;\n  font-size: 14px;\n  color: #333;\n  border: 1px solid #EBEDF5;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.placeholder {\n  color: #999;\n}\n\n.picker-arrow {\n  width: 0;\n  height: 0;\n  border-left: 5px solid transparent;\n  border-right: 5px solid transparent;\n  border-top: 6px solid #999;\n}\n\n.number-picker {\n  display: flex;\n  align-items: center;\n  height: 45px;\n}\n\n.number-btn {\n  width: 45px;\n  height: 45px;\n  background: #F5F7FA;\n  border: 1px solid #EBEDF5;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 18px;\n  color: #333;\n}\n\n.number-btn.minus {\n  border-radius: 8px 0 0 8px;\n}\n\n.number-btn.plus {\n  border-radius: 0 8px 8px 0;\n}\n\n.number-input {\n  width: 60px;\n  height: 45px;\n  background: #F5F7FA;\n  border-top: 1px solid #EBEDF5;\n  border-bottom: 1px solid #EBEDF5;\n  text-align: center;\n  font-size: 14px;\n  color: #333;\n}\n\n.unit-text {\n  margin-left: 10px;\n  font-size: 14px;\n  color: #666;\n}\n\n.date-range-picker {\n  display: flex;\n  align-items: center;\n}\n\n.date-picker {\n  flex: 1;\n}\n\n.date-picker-value {\n  height: 45px;\n  background: #F5F7FA;\n  border-radius: 8px;\n  padding: 0 12px;\n  font-size: 14px;\n  color: #333;\n  border: 1px solid #EBEDF5;\n  display: flex;\n  align-items: center;\n}\n\n.date-separator {\n  margin: 0 10px;\n  font-size: 14px;\n  color: #666;\n}\n\n/* 底部按钮 */\n.footer-buttons {\n  padding: 15px;\n  background: #FFFFFF;\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);\n  display: flex;\n  justify-content: space-between;\n  gap: 15px;\n}\n\n.btn {\n  flex: 1;\n  height: 50px;\n  border-radius: 25px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n  font-weight: 600;\n  border: none;\n}\n\n.btn-primary {\n  background: linear-gradient(135deg, #9040FF, #5E35B1);\n  color: #FFFFFF;\n}\n\n.btn-secondary {\n  background: #F5F7FA;\n  color: #666;\n  border: 1px solid #EBEDF5;\n}\n</style>\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-info.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA2FA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,aAAa;AAAA,QACX,MAAM;AAAA,QACN,aAAa;AAAA,QACb,UAAU;AAAA,QACV,eAAe;AAAA,QACf,WAAW;AAAA,QACX,WAAW;AAAA,QACX,SAAS;AAAA,MACV;AAAA,MACD,iBAAiB,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,IAAI;AAAA,IACxD;AAAA,EACD;AAAA,EACD,SAAS;AAEP,QAAI;AACF,YAAM,cAAcA,cAAAA,MAAI,eAAe,aAAa;AACpD,YAAM,YAAYA,cAAAA,MAAI,eAAe,aAAa;AAElD,UAAI,aAAa;AACfA,sBAAY,MAAA,MAAA,OAAA,6FAAA,YAAY,WAAW;AAAA,MACrC;AAEA,UAAI,WAAW;AACb,aAAK,cAAc,KAAK,MAAM,SAAS;AAAA,MACzC;AAAA,IACF,SAAS,GAAG;AACVA,oBAAc,MAAA,MAAA,SAAA,6FAAA,aAAa,CAAC;AAAA,IAC9B;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AAEP,WAAK,SAAQ;AACbA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,WAAW;AAET,UAAI,CAAC,KAAK,YAAY,MAAM;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,YAAY,UAAU;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,YAAY,aAAa,CAAC,KAAK,YAAY,SAAS;AAC5DA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAGA,WAAK,SAAQ;AACbA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IACD,WAAW;AAET,UAAI;AACFA,sBAAG,MAAC,eAAe,eAAe,KAAK,UAAU,KAAK,WAAW,CAAC;AAAA,MACpE,SAAS,GAAG;AACVA,wIAAc,WAAW,CAAC;AAAA,MAC5B;AAAA,IACD;AAAA,IACD,iBAAiB,GAAG;AAClB,YAAM,QAAQ,EAAE,OAAO;AACvB,WAAK,YAAY,gBAAgB;AACjC,WAAK,YAAY,WAAW,KAAK,gBAAgB,KAAK;AAAA,IACvD;AAAA,IACD,kBAAkB,GAAG;AACnB,WAAK,YAAY,YAAY,EAAE,OAAO;AAAA,IACvC;AAAA,IACD,gBAAgB,GAAG;AACjB,WAAK,YAAY,UAAU,EAAE,OAAO;AAAA,IACrC;AAAA,IACD,qBAAqB;AACnB,UAAI,KAAK,YAAY,YAAY,GAAG;AAClC,aAAK,YAAY;AAAA,MACnB;AAAA,IACD;AAAA,IACD,qBAAqB;AACnB,UAAI,KAAK,YAAY,YAAY,KAAK;AACpC,aAAK,YAAY;AAAA,MACnB;AAAA,IACD;AAAA,IACD,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpMA,GAAG,WAAW,eAAe;"}