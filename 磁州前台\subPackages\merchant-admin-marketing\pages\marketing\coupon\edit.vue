<template>
  <view class="coupon-edit-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @tap="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">编辑优惠券</text>
      <view class="navbar-right">
        <view class="help-icon" @tap="showHelp">?</view>
      </view>
    </view>
    
    <!-- 表单容器 -->
    <scroll-view scroll-y class="form-container">
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">基本信息</text>
        </view>
        
        <view class="form-item">
          <text class="item-label">优惠券名称</text>
          <input type="text" class="item-input" v-model="couponForm.title" placeholder="请输入优惠券名称" />
        </view>
        
        <view class="form-item">
          <text class="item-label">优惠券类型</text>
          <view class="item-selector" @tap="showCouponTypeSelector">
            <text class="selector-value">{{couponForm.typeText}}</text>
            <view class="selector-arrow"></view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="item-label">优惠金额</text>
          <view class="amount-input-wrapper">
            <text class="amount-symbol">¥</text>
            <input type="digit" class="amount-input" v-model="couponForm.value" placeholder="请输入优惠金额" />
          </view>
        </view>
        
        <view class="form-item">
          <text class="item-label">使用门槛</text>
          <view class="amount-input-wrapper">
            <text class="threshold-text">满</text>
            <input type="digit" class="amount-input" v-model="couponForm.minSpend" placeholder="请输入最低消费金额" />
            <text class="threshold-text">元可用</text>
          </view>
        </view>
      </view>
      
      <!-- 有效期设置 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">有效期设置</text>
        </view>
        
        <view class="form-item">
          <text class="item-label">有效期类型</text>
          <view class="validity-type-selector">
            <view 
              class="type-option" 
              :class="{'active': validityType === 'fixed'}"
              @tap="setValidityType('fixed')">
              <view class="option-radio">
                <view class="radio-inner" v-if="validityType === 'fixed'"></view>
              </view>
              <text class="option-text">固定日期</text>
            </view>
            
            <view 
              class="type-option" 
              :class="{'active': validityType === 'dynamic'}"
              @tap="setValidityType('dynamic')">
              <view class="option-radio">
                <view class="radio-inner" v-if="validityType === 'dynamic'"></view>
              </view>
              <text class="option-text">领取后生效</text>
            </view>
          </view>
        </view>
        
        <view class="form-item" v-if="validityType === 'fixed'">
          <text class="item-label">开始日期</text>
          <view class="date-selector" @tap="showStartDatePicker">
            <text class="date-value">{{couponForm.startDate}}</text>
            <view class="date-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="16" y1="2" x2="16" y2="6"></line>
                <line x1="8" y1="2" x2="8" y2="6"></line>
                <line x1="3" y1="10" x2="21" y2="10"></line>
              </svg>
            </view>
          </view>
        </view>
        
        <view class="form-item" v-if="validityType === 'fixed'">
          <text class="item-label">结束日期</text>
          <view class="date-selector" @tap="showEndDatePicker">
            <text class="date-value">{{couponForm.expireDate}}</text>
            <view class="date-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="16" y1="2" x2="16" y2="6"></line>
                <line x1="8" y1="2" x2="8" y2="6"></line>
                <line x1="3" y1="10" x2="21" y2="10"></line>
              </svg>
            </view>
          </view>
        </view>
        
        <view class="form-item" v-if="validityType === 'dynamic'">
          <text class="item-label">有效天数</text>
          <view class="days-input-wrapper">
            <input type="number" class="days-input" v-model="couponForm.validDays" placeholder="请输入有效天数" />
            <text class="days-text">天</text>
          </view>
        </view>
      </view>
      
      <!-- 发放设置 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">发放设置</text>
        </view>
        
        <view class="form-item">
          <text class="item-label">发放总量</text>
          <view class="amount-input-wrapper">
            <input type="number" class="amount-input" v-model="couponForm.totalCount" placeholder="请输入发放总量" />
            <text class="amount-unit">张</text>
          </view>
        </view>
        
        <view class="form-item">
          <text class="item-label">每人限领</text>
          <view class="amount-input-wrapper">
            <input type="number" class="amount-input" v-model="couponForm.perPersonLimit" placeholder="请输入每人限领数量" />
            <text class="amount-unit">张</text>
          </view>
        </view>
      </view>
      
      <!-- 使用规则 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">使用规则</text>
        </view>
        
        <view class="form-item">
          <text class="item-label">适用商品</text>
          <view class="product-selector" @tap="showProductSelector">
            <text class="selector-value">{{couponForm.applicableProducts}}</text>
            <view class="selector-arrow"></view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="item-label">使用说明</text>
          <textarea class="item-textarea" v-model="couponForm.instructions" placeholder="请输入使用说明" />
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-action-bar">
      <view class="action-button cancel" @tap="goBack">
        <text class="button-text">取消</text>
      </view>
      <view class="action-button save" @tap="saveCoupon">
        <text class="button-text">保存</text>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, reactive, onMounted } from 'vue';

export default {
  setup() {
    // 响应式状态
    const couponId = ref('');
    const couponForm = reactive({
      id: '',
      title: '',
      type: 'discount',
      typeText: '满减券',
      value: '',
      minSpend: '',
      startDate: formatDate(new Date()),
      expireDate: formatDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)), // 30天后
      validDays: '30',
      totalCount: '',
      perPersonLimit: '1',
      applicableProducts: '全部商品',
      instructions: ''
    });
    
    const validityType = ref('fixed');
    
    // 方法
    function goBack() {
      uni.navigateBack();
    }
    
    function showHelp() {
      uni.showModal({
        title: '帮助信息',
        content: '编辑优惠券页面可以修改优惠券的各项参数，包括名称、金额、有效期等。保存后将立即生效。',
        showCancel: false
      });
    }
    
    function showCouponTypeSelector() {
      uni.showActionSheet({
        itemList: ['满减券', '折扣券', '无门槛券'],
        success: (res) => {
          const types = ['discount', 'percent', 'free'];
          const texts = ['满减券', '折扣券', '无门槛券'];
          
          couponForm.type = types[res.tapIndex];
          couponForm.typeText = texts[res.tapIndex];
        }
      });
    }
    
    function setValidityType(type) {
      validityType.value = type;
    }
    
    function showStartDatePicker() {
      uni.showDatePicker({
        current: couponForm.startDate,
        success: (res) => {
          couponForm.startDate = res.date;
        }
      });
    }
    
    function showEndDatePicker() {
      uni.showDatePicker({
        current: couponForm.expireDate,
        success: (res) => {
          couponForm.expireDate = res.date;
        }
      });
    }
    
    function showProductSelector() {
      uni.showActionSheet({
        itemList: ['全部商品', '指定商品', '指定分类'],
        success: (res) => {
          const options = ['全部商品', '指定商品', '指定分类'];
          couponForm.applicableProducts = options[res.tapIndex];
          
          if (res.tapIndex > 0) {
            // 如果选择了指定商品或分类，显示选择界面
            uni.showToast({
              title: '请在下一页选择',
              icon: 'none'
            });
          }
        }
      });
    }
    
    function saveCoupon() {
      // 表单验证
      if (!couponForm.title) {
        return uni.showToast({
          title: '请输入优惠券名称',
          icon: 'none'
        });
      }
      
      if (!couponForm.value) {
        return uni.showToast({
          title: '请输入优惠金额',
          icon: 'none'
        });
      }
      
      if (couponForm.type !== 'free' && !couponForm.minSpend) {
        return uni.showToast({
          title: '请输入使用门槛',
          icon: 'none'
        });
      }
      
      if (!couponForm.totalCount) {
        return uni.showToast({
          title: '请输入发放总量',
          icon: 'none'
        });
      }
      
      // 在实际应用中，这里应该调用API保存优惠券
      
      uni.showLoading({
        title: '保存中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        });
        
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }, 1000);
    }
    
    function formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
    
    function loadCouponData(id) {
      // 在实际应用中，这里应该调用API获取优惠券详情
      couponId.value = id;
      
      // 模拟加载数据
      uni.showLoading({
        title: '加载中...'
      });
      
      setTimeout(() => {
        // 模拟数据
        Object.assign(couponForm, {
          id: id,
          title: '新客专享优惠',
          type: 'discount',
          typeText: '满减券',
          value: '10',
          minSpend: '100',
          startDate: '2023-04-15',
          expireDate: '2023-05-15',
          validDays: '30',
          totalCount: '500',
          perPersonLimit: '1',
          applicableProducts: '全部商品',
          instructions: '仅限新用户使用，不可与其他优惠同时使用'
        });
        
        uni.hideLoading();
      }, 500);
    }
    
    onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.$page?.options || {};
      
      if (options.id) {
        loadCouponData(options.id);
      }
    });
    
    return {
      couponForm,
      validityType,
      goBack,
      showHelp,
      showCouponTypeSelector,
      setValidityType,
      showStartDatePicker,
      showEndDatePicker,
      showProductSelector,
      saveCoupon
    };
  }
}
</script>

<style lang="scss">
.coupon-edit-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 80px; /* 为底部操作栏留出空间 */
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF9966, #FF5E62);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(255, 94, 98, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 表单容器样式 */
.form-container {
  height: calc(100vh - 80px - 44px - 15px - 15px);
}

.form-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.form-item {
  margin-bottom: 15px;
}

.form-item:last-child {
  margin-bottom: 0;
}

.item-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  display: block;
}

.item-input {
  width: 100%;
  height: 44px;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 14px;
  color: #333;
  box-sizing: border-box;
}

.item-selector {
  width: 100%;
  height: 44px;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.selector-value {
  font-size: 14px;
  color: #333;
}

.selector-arrow {
  width: 10px;
  height: 10px;
  border-right: 2px solid #999;
  border-bottom: 2px solid #999;
  transform: rotate(45deg);
}

.amount-input-wrapper {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  height: 44px;
}

.amount-symbol {
  font-size: 14px;
  color: #333;
  margin-right: 5px;
}

.amount-input {
  flex: 1;
  height: 44px;
  font-size: 14px;
  color: #333;
  background: transparent;
}

.amount-unit {
  font-size: 14px;
  color: #666;
}

.threshold-text {
  font-size: 14px;
  color: #666;
  margin: 0 5px;
}

.validity-type-selector {
  display: flex;
}

.type-option {
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.option-radio {
  width: 18px;
  height: 18px;
  border-radius: 9px;
  border: 1px solid #ddd;
  margin-right: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.radio-inner {
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background: #FF5E62;
}

.option-text {
  font-size: 14px;
  color: #333;
}

.type-option.active .option-text {
  color: #FF5E62;
}

.date-selector {
  width: 100%;
  height: 44px;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.date-value {
  font-size: 14px;
  color: #333;
}

.date-icon {
  width: 20px;
  height: 20px;
  color: #999;
}

.days-input-wrapper {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  height: 44px;
}

.days-input {
  flex: 1;
  height: 44px;
  font-size: 14px;
  color: #333;
  background: transparent;
}

.days-text {
  font-size: 14px;
  color: #666;
}

.product-selector {
  width: 100%;
  height: 44px;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.item-textarea {
  width: 100%;
  height: 100px;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  color: #333;
  box-sizing: border-box;
}

/* 底部操作栏样式 */
.bottom-action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  display: flex;
  padding: 15px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 90;
}

.action-button {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 5px;
}

.action-button.cancel {
  background: #F5F7FA;
}

.action-button.save {
  background: linear-gradient(135deg, #FF9966, #FF5E62);
}

.button-text {
  font-size: 16px;
  font-weight: 500;
}

.action-button.cancel .button-text {
  color: #666;
}

.action-button.save .button-text {
  color: #fff;
}
</style>