{"version": 3, "file": "categories.js", "sources": ["mock/business/categories.js"], "sourcesContent": ["// 商圈分类模拟数据\r\nexport const businessCategories = [\r\n  '餐饮美食', '休闲娱乐', '美容美发', '教育培训',\r\n  '酒店住宿', '生活服务', '医疗健康', '汽车服务',\r\n  '家居建材', '数码电器', '服装服饰', '其他'\r\n];\r\n\r\n// 获取商圈分类的API函数\r\nexport const fetchBusinessCategories = () => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve(businessCategories);\r\n    }, 200);\r\n  });\r\n}; "], "names": [], "mappings": ";AACO,MAAM,qBAAqB;AAAA,EAChC;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAAA,EACxB;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAAA,EACxB;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAC1B;AAGY,MAAC,0BAA0B,MAAM;AAC3C,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,cAAQ,kBAAkB;AAAA,IAC3B,GAAE,GAAG;AAAA,EACV,CAAG;AACH;;"}