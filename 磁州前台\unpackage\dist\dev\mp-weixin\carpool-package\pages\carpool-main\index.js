"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const banners = common_vendor.reactive([
      { id: 1, image: "/static/images/tabbar/carpool-banner1.jpg", url: "/pages/carpool/activity/discount" },
      { id: 2, image: "/static/images/tabbar/carpool-banner2.jpg", url: "/pages/carpool/activity/newuser" }
    ]);
    const hotRoutes = common_vendor.reactive([
      { id: 1, start: "磁县", end: "邯郸", count: 128 },
      { id: 2, start: "邯郸", end: "磁县", count: 112 },
      { id: 3, start: "邯郸", end: "北京", count: 86 },
      { id: 4, start: "北京", end: "邯郸", count: 72 },
      { id: 5, start: "邯郸", end: "石家庄", count: 65 },
      { id: 6, start: "石家庄", end: "邯郸", count: 53 }
    ]);
    const activeTab = common_vendor.ref("carpool");
    const latestCarpools = common_vendor.reactive([
      {
        id: 1,
        type: "people-to-car",
        typeText: "人找车",
        avatar: "/static/images/avatar/user1.png",
        username: "张先生",
        isVerified: true,
        startPoint: "磁州城区-汽车站",
        endPoint: "邯郸火车站",
        departureTime: "今天 14:30",
        seats: 1,
        phone: "13812345678",
        userId: "user123",
        publishMode: "premium",
        // 付费置顶
        viaPoints: ["磁州东区", "峰峰矿区"]
      },
      {
        id: 2,
        type: "car-to-people",
        typeText: "车找人",
        avatar: "/static/images/avatar/user2.png",
        username: "李师傅",
        isVerified: true,
        startPoint: "邯郸机场",
        endPoint: "磁州城区-人民医院",
        departureTime: "今天 18:00",
        price: 30,
        seats: 3,
        phone: "13987654321",
        userId: "user456",
        publishMode: "ad",
        // 广告发布
        viaPoints: ["邯郸东站", "武安市"]
      },
      {
        id: 3,
        type: "goods-to-car",
        typeText: "货找车",
        avatar: "/static/images/avatar/user3.png",
        username: "王经理",
        isVerified: false,
        startPoint: "磁州商贸城",
        endPoint: "邯郸物流园",
        departureTime: "明天 09:00",
        phone: "13765432198",
        userId: "user789",
        publishMode: "premium",
        // 付费置顶
        viaPoints: ["永年区", "丛台区"]
      },
      {
        id: 4,
        type: "car-to-goods",
        typeText: "车找货",
        avatar: "/static/images/avatar/user4.png",
        username: "赵师傅",
        isVerified: true,
        startPoint: "邯郸市区",
        endPoint: "磁县工业园",
        departureTime: "明天 10:30",
        seats: 0,
        phone: "13598765432",
        userId: "user321",
        publishMode: "normal",
        // 普通发布
        viaPoints: []
      }
    ]);
    const statusBarHeight = common_vendor.ref(0);
    const showAnimation = common_vendor.ref(false);
    const startPoint = common_vendor.ref("");
    const endPoint = common_vendor.ref("");
    const showPublishPopup = common_vendor.ref(false);
    const sortedCarpoolList = common_vendor.ref([]);
    const sortType = common_vendor.ref("default");
    const showSortOptions = common_vendor.ref(false);
    const currentType = common_vendor.ref("");
    const filterStartPoint = common_vendor.ref("");
    const filterEndPoint = common_vendor.ref("");
    const sortTypeText = common_vendor.computed(() => {
      switch (sortType.value) {
        case "default":
          return "默认排序";
        case "departureTime":
          return "出发时间";
        case "publishTime":
          return "发布时间";
        default:
          return "默认排序";
      }
    });
    const setStatusBarHeight = () => {
      try {
        const systemInfo = common_vendor.index.getSystemInfoSync();
        statusBarHeight.value = systemInfo.statusBarHeight || 20;
        if (systemInfo.brand) {
          const brand = systemInfo.brand.toLowerCase();
          if (brand.includes("iphone")) {
            if (systemInfo.safeAreaInsets && systemInfo.safeAreaInsets.top > 20) {
              statusBarHeight.value = Math.max(statusBarHeight.value, systemInfo.safeAreaInsets.top);
            }
          }
        }
        if (typeof document !== "undefined" && document.documentElement) {
          document.documentElement.style.setProperty("--status-bar-height", `${statusBarHeight.value}px`);
          document.documentElement.style.setProperty("--status-bar-color", "#1677FF");
          document.documentElement.style.setProperty("--status-bar-bg-color", "#1677FF");
        }
        common_vendor.index.setStorageSync("statusBarHeight", statusBarHeight.value);
        common_vendor.index.setNavigationBarColor({
          frontColor: "#ffffff",
          backgroundColor: "#1677FF",
          animation: {
            duration: 0,
            timingFunc: "easeIn"
          }
        });
        if (systemInfo.platform) {
          common_vendor.index.setStorageSync("platform", systemInfo.platform);
          common_vendor.index.setStorageSync("isIOS", systemInfo.platform === "ios");
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at carpool-package/pages/carpool-main/index.vue:426", "设置状态栏高度出错:", e);
        statusBarHeight.value = 20;
        if (typeof document !== "undefined" && document.documentElement) {
          document.documentElement.style.setProperty("--status-bar-height", `${statusBarHeight.value}px`);
        }
        common_vendor.index.setStorageSync("statusBarHeight", statusBarHeight.value);
      }
    };
    const handleBannerClick = (banner) => {
      common_vendor.index.navigateTo({
        url: "/carpool-package" + banner.url
      });
    };
    const searchRoutes = () => {
      if (!startPoint.value || !endPoint.value) {
        common_vendor.index.showToast({ title: "请输入出发地和目的地", icon: "none" });
        return;
      }
      common_vendor.index.navigateTo({
        url: `/carpool-package/pages/carpool/list/index?start=${startPoint.value}&end=${endPoint.value}`
      });
    };
    const getTypeText = (type) => {
      switch (type) {
        case "people-to-car":
          return "人找车";
        case "car-to-people":
          return "车找人";
        case "goods-to-car":
          return "货找车";
        case "car-to-goods":
          return "车找货";
        default:
          return "全部";
      }
    };
    const navigateTo = (type) => {
      currentType.value = type;
      sortCarpoolList();
      common_vendor.index.pageScrollTo({ selector: ".latest-carpools", duration: 300 });
      common_vendor.index.showToast({ title: `已筛选${getTypeText(type)}信息`, icon: "none", duration: 1500 });
    };
    const viewMoreRoutes = () => {
      common_vendor.index.navigateTo({ url: "/carpool-package/pages/carpool/routes/index" });
    };
    const selectRoute = (route) => {
      if (!route || !route.start || !route.end) {
        common_vendor.index.showToast({ title: "路线数据不完整", icon: "none" });
        return;
      }
      filterStartPoint.value = route.start;
      filterEndPoint.value = route.end;
      sortCarpoolList();
      common_vendor.index.pageScrollTo({ selector: ".latest-carpools", duration: 300 });
      common_vendor.index.showToast({ title: `已筛选 ${route.start}→${route.end} 路线`, icon: "none", duration: 1500 });
    };
    const viewDetail = (item) => {
      common_vendor.index.navigateTo({ url: `/carpool-package/pages/carpool/detail/index?id=${item.id}&type=${item.type}` });
    };
    const callPhone = (phone) => {
      common_vendor.index.makePhoneCall({ phoneNumber: phone });
    };
    const chatWith = (userId) => {
      common_vendor.index.navigateTo({ url: `/pages/message/chat?userId=${userId}` });
    };
    const publishCarpool = () => {
      activeTab.value = "publish";
      showPublishPopup.value = true;
    };
    const getLatestCarpools = () => {
      sortCarpoolList();
    };
    const parseDepartureTime = (timeStr) => {
      const now = /* @__PURE__ */ new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);
      if (timeStr.includes("今天")) {
        const timeParts = timeStr.split(" ")[1].split(":");
        return today.setHours(parseInt(timeParts[0]), parseInt(timeParts[1]));
      } else if (timeStr.includes("明天")) {
        const timeParts = timeStr.split(" ")[1].split(":");
        return tomorrow.setHours(parseInt(timeParts[0]), parseInt(timeParts[1]));
      } else {
        return new Date(timeStr).getTime();
      }
    };
    const sortCarpoolList = () => {
      let filteredList = latestCarpools.slice();
      if (currentType.value) {
        filteredList = filteredList.filter((item) => item.type === currentType.value);
      }
      if (filterStartPoint.value && filterEndPoint.value) {
        filteredList = filteredList.filter(
          (item) => item.startPoint.includes(filterStartPoint.value) && item.endPoint.includes(filterEndPoint.value)
        );
      }
      sortedCarpoolList.value = filteredList.sort((a, b) => {
        if (a.publishMode === "premium" && b.publishMode !== "premium")
          return -1;
        if (a.publishMode !== "premium" && b.publishMode === "premium")
          return 1;
        if (sortType.value === "departureTime") {
          return parseDepartureTime(a.departureTime) - parseDepartureTime(b.departureTime);
        } else if (sortType.value === "publishTime") {
          return b.id - a.id;
        }
        return 0;
      });
      if (sortedCarpoolList.value.length === 0 && (currentType.value || filterStartPoint.value)) {
        common_vendor.index.showToast({ title: "没有找到符合条件的拼车信息", icon: "none", duration: 2e3 });
      }
    };
    const navigateToPage = (page) => {
      activeTab.value = page;
      switch (page) {
        case "home":
          common_vendor.index.showTabBar();
          setTimeout(() => common_vendor.index.switchTab({ url: "/pages/index/index" }), 50);
          break;
        case "groups":
          common_vendor.index.hideTabBar();
          common_vendor.index.navigateTo({ url: "/carpool-package/pages/carpool/groups/index" });
          break;
        case "my":
          common_vendor.index.hideTabBar();
          common_vendor.index.navigateTo({ url: "/carpool-package/pages/carpool/my/index" });
          break;
      }
    };
    const closePublishPopup = () => {
      showPublishPopup.value = false;
      activeTab.value = "carpool";
    };
    const navigateToPublish = (type) => {
      closePublishPopup();
      let url = "";
      switch (type) {
        case "people-to-car":
          url = "/carpool-package/pages/carpool/publish/people-to-car";
          break;
        case "car-to-people":
          url = "/carpool-package/pages/carpool/publish/car-to-people";
          break;
        case "goods-to-car":
          url = "/carpool-package/pages/carpool/publish/goods-to-car";
          break;
        case "car-to-goods":
          url = "/carpool-package/pages/carpool/publish/car-to-goods";
          break;
        default:
          url = "/carpool-package/pages/carpool/publish/people-to-car";
      }
      common_vendor.index.navigateTo({ url });
    };
    const toggleSortOptions = () => {
      showSortOptions.value = !showSortOptions.value;
    };
    const changeSortType = (type) => {
      sortType.value = type;
      showSortOptions.value = false;
      sortCarpoolList();
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    common_vendor.onLoad((options) => {
      setStatusBarHeight();
      getLatestCarpools();
      activeTab.value = "carpool";
      common_vendor.index.hideTabBar();
      setTimeout(() => {
        showAnimation.value = true;
      }, 300);
      const currentPage = getCurrentPages().pop();
      const eventChannel = currentPage.getOpenerEventChannel();
      if (eventChannel && eventChannel.on) {
        eventChannel.on("showPublishPopup", () => {
          showPublishPopup.value = true;
        });
      }
      const showPopup = common_vendor.index.getStorageSync("showPublishPopup");
      if (showPopup) {
        showPublishPopup.value = true;
        common_vendor.index.removeStorageSync("showPublishPopup");
      }
      sortCarpoolList();
    });
    common_vendor.onReady(() => {
      setTimeout(() => {
        const storedHeight = common_vendor.index.getStorageSync("statusBarHeight");
        if (storedHeight && storedHeight !== statusBarHeight.value) {
          statusBarHeight.value = storedHeight;
          if (typeof document !== "undefined" && document.documentElement) {
            document.documentElement.style.setProperty("--status-bar-height", `${statusBarHeight.value}px`);
          }
        }
      }, 50);
    });
    common_vendor.onShow(() => {
      common_vendor.index.hideTabBar();
    });
    common_vendor.onHide(() => {
      setTimeout(() => common_vendor.index.showTabBar(), 100);
    });
    common_vendor.onUnload(() => {
      common_vendor.index.showTabBar();
    });
    common_vendor.onBackPress((event) => {
      if (event.from === "backbutton") {
        common_vendor.index.showTabBar();
        common_vendor.index.switchTab({ url: "/pages/index/index" });
        return true;
      }
      return false;
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: common_assets._imports_1$30,
        d: statusBarHeight.value + "px",
        e: common_vendor.f(banners, (item, index, i0) => {
          return {
            a: item.image,
            b: index,
            c: common_vendor.o(($event) => handleBannerClick(item), index)
          };
        }),
        f: startPoint.value,
        g: common_vendor.o(($event) => startPoint.value = $event.detail.value),
        h: endPoint.value,
        i: common_vendor.o(($event) => endPoint.value = $event.detail.value),
        j: common_vendor.o(searchRoutes),
        k: common_vendor.o(($event) => navigateTo("people-to-car")),
        l: common_vendor.o(($event) => navigateTo("car-to-people")),
        m: common_vendor.o(($event) => navigateTo("goods-to-car")),
        n: common_vendor.o(($event) => navigateTo("car-to-goods")),
        o: common_vendor.o(viewMoreRoutes),
        p: common_vendor.f(hotRoutes, (route, index, i0) => {
          return {
            a: common_vendor.t(route.start),
            b: common_vendor.t(route.end),
            c: index,
            d: common_vendor.o(($event) => selectRoute(route), index)
          };
        }),
        q: common_vendor.t(sortTypeText.value),
        r: common_vendor.o(toggleSortOptions),
        s: showSortOptions.value
      }, showSortOptions.value ? {
        t: common_vendor.o(($event) => changeSortType("default")),
        v: common_vendor.o(($event) => changeSortType("departureTime")),
        w: common_vendor.o(($event) => changeSortType("publishTime"))
      } : {}, {
        x: common_vendor.f(sortedCarpoolList.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.startPoint),
            b: item.viaPoints && item.viaPoints.length > 0
          }, item.viaPoints && item.viaPoints.length > 0 ? {
            c: common_vendor.t(item.viaPoints.join(" → "))
          } : {}, {
            d: common_vendor.t(item.endPoint),
            e: common_vendor.t(item.typeText),
            f: common_vendor.n(item.type + "-tag"),
            g: common_vendor.t(item.publishMode === "premium" ? "置顶" : "广告"),
            h: common_vendor.n(item.publishMode),
            i: common_vendor.t(item.departureTime),
            j: item.price
          }, item.price ? {
            k: common_assets._imports_3$24,
            l: common_vendor.t(item.price)
          } : {}, {
            m: common_vendor.t(item.seats),
            n: item.avatar,
            o: common_vendor.t(item.username),
            p: item.isVerified
          }, item.isVerified ? {
            q: "f9f47938-1-" + i0 + "," + ("f9f47938-0-" + i0),
            r: common_vendor.p({
              d: "M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z",
              fill: "currentColor"
            }),
            s: "f9f47938-0-" + i0,
            t: common_vendor.p({
              width: "12",
              height: "12",
              viewBox: "0 0 24 24",
              fill: "none",
              xmlns: "http://www.w3.org/2000/svg"
            })
          } : {}, {
            v: common_vendor.o(($event) => callPhone(item.phone), index),
            w: common_vendor.o(($event) => chatWith(item.userId), index),
            x: index,
            y: common_vendor.o(($event) => viewDetail(item), index),
            z: common_vendor.n(item.type)
          });
        }),
        y: common_assets._imports_1$28,
        z: common_assets._imports_6$14,
        A: common_assets._imports_6,
        B: common_assets._imports_6$4,
        C: showPublishPopup.value
      }, showPublishPopup.value ? {
        D: common_vendor.o(closePublishPopup),
        E: common_assets._imports_6$13,
        F: common_vendor.o(($event) => navigateToPublish("people-to-car")),
        G: common_assets._imports_7$8,
        H: common_vendor.o(($event) => navigateToPublish("car-to-people")),
        I: common_assets._imports_8$4,
        J: common_vendor.o(($event) => navigateToPublish("goods-to-car")),
        K: common_assets._imports_9$6,
        L: common_vendor.o(($event) => navigateToPublish("car-to-goods")),
        M: common_vendor.o(() => {
        }),
        N: common_vendor.o(closePublishPopup)
      } : {}, {
        O: common_assets._imports_11$8,
        P: common_vendor.o(($event) => navigateToPage("home")),
        Q: activeTab.value === "carpool" ? "/static/images/tabbar/p拼车选中.png" : "/static/images/tabbar/p拼车.png",
        R: activeTab.value === "carpool" ? 1 : "",
        S: activeTab.value === "carpool" ? 1 : "",
        T: activeTab.value === "publish" ? "/static/images/tabbar/p发布选中.png" : "/static/images/tabbar/p发布.png",
        U: activeTab.value === "publish" ? 1 : "",
        V: activeTab.value === "publish" ? 1 : "",
        W: common_vendor.o(publishCarpool),
        X: activeTab.value === "groups" ? "/static/images/tabbar/p拼车群选中.png" : "/static/images/tabbar/p拼车群.png",
        Y: activeTab.value === "groups" ? 1 : "",
        Z: activeTab.value === "groups" ? 1 : "",
        aa: common_vendor.o(($event) => navigateToPage("groups")),
        ab: activeTab.value === "my" ? "/static/images/tabbar/p我的选中.png" : "/static/images/tabbar/p我的.png",
        ac: activeTab.value === "my" ? 1 : "",
        ad: activeTab.value === "my" ? 1 : "",
        ae: common_vendor.o(($event) => navigateToPage("my"))
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/carpool-package/pages/carpool-main/index.js.map
