/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-31b6d405, html.data-v-31b6d405, #app.data-v-31b6d405, .index-container.data-v-31b6d405 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.qrcode-container.data-v-31b6d405 {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 30rpx;
  padding-top: calc(44px + var(--status-bar-height));
}

/* 自定义导航栏 */
.custom-navbar.data-v-31b6d405 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  padding: 0 30rpx;
  padding-top: 44px;
  /* 状态栏高度 */
  position: fixed;
  /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  background-image: linear-gradient(135deg, #0066FF, #0052CC);
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.15);
  z-index: 100;
  /* 提高z-index确保在最上层 */
}
.navbar-title.data-v-31b6d405 {
  position: absolute;
  left: 0;
  right: 0;
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 700;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  text-align: center;
}
.navbar-left.data-v-31b6d405 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 20;
  /* 确保在标题上层，可以被点击 */
}
.back-icon.data-v-31b6d405 {
  width: 100%;
  height: 100%;
}
.safe-area-top.data-v-31b6d405 {
  height: var(--status-bar-height);
  width: 100%;
  background-image: linear-gradient(135deg, #0066FF, #0052CC);
}

/* 二维码卡片 */
.qrcode-card.data-v-31b6d405 {
  margin: 30rpx;
  padding: 40rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.08);
}
.user-info.data-v-31b6d405 {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}
.avatar.data-v-31b6d405 {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  border: 2rpx solid #f0f0f0;
}
.user-detail.data-v-31b6d405 {
  flex: 1;
}
.nickname.data-v-31b6d405 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 10rpx;
}
.level-tag.data-v-31b6d405 {
  display: inline-block;
  font-size: 24rpx;
  color: #0066FF;
  background-color: rgba(0, 102, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}
.qrcode-wrapper.data-v-31b6d405 {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  margin-bottom: 40rpx;
}
.qrcode-bg.data-v-31b6d405 {
  width: 400rpx;
  height: 400rpx;
  opacity: 0.05;
  position: absolute;
  z-index: 1;
}
.qrcode-box.data-v-31b6d405 {
  width: 400rpx;
  height: 400rpx;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 2;
}
.qrcode-image.data-v-31b6d405 {
  width: 360rpx;
  height: 360rpx;
}
.qrcode-tip.data-v-31b6d405 {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666666;
}
.action-buttons.data-v-31b6d405 {
  display: flex;
  justify-content: space-around;
}
.action-btn.data-v-31b6d405 {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: transparent;
  border: none;
  padding: 0;
  width: 200rpx;
}
.action-btn.data-v-31b6d405::after {
  border: none;
}
.action-btn .btn-icon.data-v-31b6d405 {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}
.action-btn text.data-v-31b6d405 {
  font-size: 28rpx;
  color: #333333;
}
.save-btn.data-v-31b6d405 {
  color: #0066FF;
}
.share-btn.data-v-31b6d405 {
  color: #FF6B00;
}

/* 推广提示 */
.promotion-tips.data-v-31b6d405 {
  margin: 30rpx;
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.08);
}
.tips-header.data-v-31b6d405 {
  margin-bottom: 20rpx;
  position: relative;
}
.tips-header.data-v-31b6d405::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -10rpx;
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(to right, #0066FF, #36CBCB);
  border-radius: 2rpx;
}
.tips-title.data-v-31b6d405 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.tips-content.data-v-31b6d405 {
  margin-top: 30rpx;
}
.tip-item.data-v-31b6d405 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}
.tip-item.data-v-31b6d405:last-child {
  margin-bottom: 0;
}
.tip-dot.data-v-31b6d405 {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #0066FF;
  margin-top: 12rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}
.tip-text.data-v-31b6d405 {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
}

/* 分享弹窗 */
.share-modal.data-v-31b6d405 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: flex-end;
}
.modal-mask.data-v-31b6d405 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}
.modal-content.data-v-31b6d405 {
  width: 100%;
  background-color: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  padding-bottom: env(safe-area-inset-bottom);
  position: relative;
  z-index: 1000;
}
.modal-title.data-v-31b6d405 {
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.share-options.data-v-31b6d405 {
  display: flex;
  padding: 40rpx 30rpx;
  justify-content: space-around;
}
.share-option.data-v-31b6d405 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.share-option image.data-v-31b6d405 {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
}
.share-option text.data-v-31b6d405 {
  font-size: 28rpx;
  color: #333333;
}
.cancel-btn.data-v-31b6d405 {
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 32rpx;
  color: #333333;
  border-top: 10rpx solid #f5f5f5;
}
.cancel-btn.data-v-31b6d405::after {
  border: none;
}