<!-- 会员等级页面开始 -->
<template>
  <view class="member-level-container">
    <!-- 页面标题区域 -->
    <view class="page-header">
      <view class="title-section">
        <text class="page-title">会员等级</text>
        <text class="page-subtitle">管理店铺会员等级体系</text>
      </view>
      
      <!-- 添加等级按钮 -->
      <view class="add-level-btn" @click="showAddLevelModal">
        <text class="btn-text">添加等级</text>
        <text class="btn-icon">+</text>
      </view>
    </view>
    
    <!-- 会员等级列表 -->
    <view class="level-list">
      <view v-if="levels.length === 0" class="empty-tip">
        <image class="empty-icon" src="/static/images/empty-data.svg"></image>
        <text class="empty-text">暂无会员等级，点击"添加等级"创建</text>
      </view>
      
      <view v-else class="level-cards">
        <view v-for="(level, index) in levels" :key="index" class="level-card">
          <view class="level-card-header" :style="{ backgroundColor: level.color }">
            <view class="level-name">{{ level.name }}</view>
            <view class="level-actions">
              <text class="action-btn edit" @click="editLevel(level)">编辑</text>
              <text class="action-btn delete" @click="confirmDeleteLevel(level)">删除</text>
            </view>
          </view>
          <view class="level-card-body">
            <view class="level-info-item">
              <text class="info-label">等级图标：</text>
              <image class="level-icon" :src="level.icon" mode="aspectFit"></image>
            </view>
            <view class="level-info-item">
              <text class="info-label">所需成长值：</text>
              <text class="info-value">{{ level.growthRequired }}</text>
            </view>
            <view class="level-info-item">
              <text class="info-label">折扣权益：</text>
              <text class="info-value">{{ level.discount }}折</text>
            </view>
            <view class="level-info-item">
              <text class="info-label">积分倍率：</text>
              <text class="info-value">{{ level.pointsMultiplier }}倍</text>
            </view>
            <view class="level-info-item">
              <text class="info-label">特权说明：</text>
              <text class="info-value">{{ level.description || '暂无特权说明' }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 添加/编辑等级弹窗 -->
    <uni-popup ref="levelFormPopup" type="center">
      <view class="level-form-popup">
        <view class="popup-header">
          <text class="popup-title">{{ isEditing ? '编辑等级' : '添加等级' }}</text>
          <text class="popup-close" @click="closeLevelModal">×</text>
        </view>
        <view class="popup-body">
          <view class="form-item">
            <text class="form-label">等级名称</text>
            <input class="form-input" v-model="levelForm.name" placeholder="请输入等级名称" />
          </view>
          <view class="form-item">
            <text class="form-label">等级颜色</text>
            <view class="color-picker">
              <view 
                v-for="(color, idx) in colorOptions" 
                :key="idx" 
                class="color-option"
                :class="{ active: levelForm.color === color }"
                :style="{ backgroundColor: color }"
                @click="levelForm.color = color"
              ></view>
            </view>
          </view>
          <view class="form-item">
            <text class="form-label">等级图标</text>
            <view class="icon-upload">
              <image v-if="levelForm.icon" class="preview-icon" :src="levelForm.icon" mode="aspectFit"></image>
              <view v-else class="upload-btn" @click="chooseIcon">
                <text class="upload-icon">+</text>
                <text class="upload-text">上传图标</text>
              </view>
            </view>
          </view>
          <view class="form-item">
            <text class="form-label">所需成长值</text>
            <input class="form-input" type="number" v-model="levelForm.growthRequired" placeholder="请输入所需成长值" />
          </view>
          <view class="form-item">
            <text class="form-label">折扣权益</text>
            <input class="form-input" type="digit" v-model="levelForm.discount" placeholder="请输入折扣（如9.5代表9.5折）" />
          </view>
          <view class="form-item">
            <text class="form-label">积分倍率</text>
            <input class="form-input" type="digit" v-model="levelForm.pointsMultiplier" placeholder="请输入积分倍率" />
          </view>
          <view class="form-item">
            <text class="form-label">特权说明</text>
            <textarea class="form-textarea" v-model="levelForm.description" placeholder="请输入特权说明"></textarea>
          </view>
        </view>
        <view class="popup-footer">
          <button class="cancel-btn" @click="closeLevelModal">取消</button>
          <button class="confirm-btn" @click="saveLevelForm">确认</button>
        </view>
      </view>
    </uni-popup>
    
    <!-- 删除确认弹窗 -->
    <uni-popup ref="deleteConfirmPopup" type="dialog">
      <uni-popup-dialog
        type="warning"
        title="删除确认"
        content="确定要删除该会员等级吗？删除后将无法恢复。"
        :before-close="true"
        @confirm="deleteLevel"
        @close="closeDeleteConfirm"
      ></uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      levels: [], // 会员等级列表
      levelForm: {
        id: '',
        name: '',
        color: '#8A2BE2', // 默认紫色
        icon: '',
        growthRequired: '',
        discount: '',
        pointsMultiplier: '',
        description: ''
      },
      isEditing: false, // 是否为编辑模式
      currentLevelId: null, // 当前编辑的等级ID
      colorOptions: [
        '#8A2BE2', // 紫色
        '#FF4500', // 橙红色
        '#1E90FF', // 道奇蓝
        '#32CD32', // 酸橙绿
        '#FFD700', // 金色
        '#FF69B4', // 热粉红
        '#20B2AA', // 浅海绿
        '#FF8C00'  // 深橙色
      ]
    };
  },
  onLoad() {
    this.fetchLevels();
  },
  methods: {
    // 获取会员等级列表
    fetchLevels() {
      // 模拟数据，实际项目中应从API获取
      this.levels = [
        {
          id: '1',
          name: '普通会员',
          color: '#8A2BE2',
          icon: '/static/images/level-normal.svg',
          growthRequired: 0,
          discount: 10,
          pointsMultiplier: 1,
          description: '基础会员权益'
        },
        {
          id: '2',
          name: '银卡会员',
          color: '#1E90FF',
          icon: '/static/images/level-silver.svg',
          growthRequired: 1000,
          discount: 9.5,
          pointsMultiplier: 1.2,
          description: '享受9.5折优惠，积分1.2倍'
        },
        {
          id: '3',
          name: '金卡会员',
          color: '#FFD700',
          icon: '/static/images/level-gold.svg',
          growthRequired: 3000,
          discount: 9,
          pointsMultiplier: 1.5,
          description: '享受9折优惠，积分1.5倍，生日礼包'
        }
      ];
    },
    
    // 显示添加等级弹窗
    showAddLevelModal() {
      this.isEditing = false;
      this.resetLevelForm();
      this.$refs.levelFormPopup.open();
    },
    
    // 编辑等级
    editLevel(level) {
      this.isEditing = true;
      this.currentLevelId = level.id;
      this.levelForm = { ...level };
      this.$refs.levelFormPopup.open();
    },
    
    // 关闭等级表单弹窗
    closeLevelModal() {
      this.$refs.levelFormPopup.close();
      this.resetLevelForm();
    },
    
    // 重置表单
    resetLevelForm() {
      this.levelForm = {
        id: '',
        name: '',
        color: '#8A2BE2',
        icon: '',
        growthRequired: '',
        discount: '',
        pointsMultiplier: '',
        description: ''
      };
      this.currentLevelId = null;
    },
    
    // 选择图标
    chooseIcon() {
      uni.chooseImage({
        count: 1,
        success: (res) => {
          this.levelForm.icon = res.tempFilePaths[0];
          // 实际项目中应上传图片到服务器并获取URL
        }
      });
    },
    
    // 保存等级表单
    saveLevelForm() {
      // 表单验证
      if (!this.levelForm.name) {
        uni.showToast({
          title: '请输入等级名称',
          icon: 'none'
        });
        return;
      }
      
      if (!this.levelForm.growthRequired && this.levelForm.growthRequired !== 0) {
        uni.showToast({
          title: '请输入所需成长值',
          icon: 'none'
        });
        return;
      }
      
      if (!this.levelForm.discount) {
        uni.showToast({
          title: '请输入折扣权益',
          icon: 'none'
        });
        return;
      }
      
      if (!this.levelForm.pointsMultiplier) {
        uni.showToast({
          title: '请输入积分倍率',
          icon: 'none'
        });
        return;
      }
      
      if (this.isEditing) {
        // 编辑现有等级
        const index = this.levels.findIndex(item => item.id === this.currentLevelId);
        if (index !== -1) {
          this.levels[index] = { ...this.levelForm, id: this.currentLevelId };
        }
      } else {
        // 添加新等级
        const newLevel = {
          ...this.levelForm,
          id: Date.now().toString() // 生成临时ID，实际项目中应由后端生成
        };
        this.levels.push(newLevel);
      }
      
      // 关闭弹窗
      this.$refs.levelFormPopup.close();
      this.resetLevelForm();
      
      // 提示成功
      uni.showToast({
        title: this.isEditing ? '编辑成功' : '添加成功',
        icon: 'success'
      });
    },
    
    // 确认删除等级
    confirmDeleteLevel(level) {
      this.currentLevelId = level.id;
      this.$refs.deleteConfirmPopup.open();
    },
    
    // 关闭删除确认弹窗
    closeDeleteConfirm() {
      this.$refs.deleteConfirmPopup.close();
    },
    
    // 删除等级
    deleteLevel() {
      const index = this.levels.findIndex(item => item.id === this.currentLevelId);
      if (index !== -1) {
        this.levels.splice(index, 1);
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        });
      }
      this.currentLevelId = null;
    }
  }
};
</script>

<style>
/* 会员等级页面样式开始 */
.member-level-container {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.title-section {
  display: flex;
  flex-direction: column;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.page-subtitle {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

.add-level-btn {
  display: flex;
  align-items: center;
  background-color: #6200ee;
  color: #fff;
  padding: 16rpx 30rpx;
  border-radius: 50rpx;
}

.btn-text {
  font-size: 28rpx;
}

.btn-icon {
  font-size: 32rpx;
  margin-left: 8rpx;
}

.level-list {
  margin-top: 20rpx;
}

.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.level-cards {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.level-card {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.level-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  color: #fff;
}

.level-name {
  font-size: 32rpx;
  font-weight: bold;
}

.level-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
}

.level-card-body {
  padding: 20rpx 30rpx;
}

.level-info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.info-label {
  width: 180rpx;
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.level-icon {
  width: 60rpx;
  height: 60rpx;
}

/* 弹窗样式 */
.level-form-popup {
  width: 650rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.popup-close {
  font-size: 40rpx;
  color: #999;
}

.popup-body {
  padding: 30rpx;
  max-height: 70vh;
  overflow-y: auto;
}

.form-item {
  margin-bottom: 24rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.form-textarea {
  width: 100%;
  height: 160rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.color-picker {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.color-option {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: 2rpx solid transparent;
}

.color-option.active {
  border-color: #333;
  transform: scale(1.1);
}

.icon-upload {
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-icon {
  width: 100rpx;
  height: 100rpx;
}

.upload-btn {
  width: 100rpx;
  height: 100rpx;
  border: 1rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.upload-icon {
  font-size: 40rpx;
  color: #999;
}

.upload-text {
  font-size: 20rpx;
  color: #999;
  margin-top: 6rpx;
}

.popup-footer {
  display: flex;
  border-top: 1rpx solid #eee;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  height: 90rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
}

.cancel-btn {
  color: #666;
  background-color: #f5f5f5;
}

.confirm-btn {
  color: #fff;
  background-color: #6200ee;
}
/* 会员等级页面样式结束 */
</style>
<!-- 会员等级页面结束 --> 