<!-- 满减活动创建页面 (create.vue) -->
<template>
    <view class="discount-create-container">
      <!-- 自定义导航栏 -->
      <view class="navbar">
        <view class="navbar-back" @tap="goBack">
          <view class="back-icon"></view>
        </view>
        <text class="navbar-title">创建满减活动</text>
        <view class="navbar-right">
          <text class="save-btn" @tap="saveDiscount">保存</text>
        </view>
      </view>
      
      <!-- 创建表单 -->
      <view class="create-form">
        <!-- 基本信息 -->
        <view class="form-section">
          <view class="section-header">
            <text class="section-title">基本信息</text>
          </view>
          
          <view class="form-item">
            <text class="item-label">活动名称</text>
            <input class="item-input" v-model="discountForm.title" placeholder="请输入活动名称" maxlength="20" />
            <text class="input-count">{{discountForm.title.length}}/20</text>
          </view>
          
          <view class="form-item">
            <text class="item-label">活动时间</text>
            <view class="date-picker" @tap="showDatePicker('start')">
              <text class="date-text">{{discountForm.startDate || '开始日期'}}</text>
            </view>
            <text class="date-separator">至</text>
            <view class="date-picker" @tap="showDatePicker('end')">
              <text class="date-text">{{discountForm.endDate || '结束日期'}}</text>
            </view>
          </view>
          
          <view class="form-item">
            <text class="item-label">活动状态</text>
            <view class="status-switch">
              <text class="status-text">{{discountForm.status === 'active' ? '启用' : '暂停'}}</text>
              <switch 
                :checked="discountForm.status === 'active'" 
                @change="onStatusChange" 
                color="#F8D800" 
              />
            </view>
          </view>
        </view>
        
        <!-- 满减规则 -->
        <view class="form-section">
          <view class="section-header">
            <text class="section-title">满减规则</text>
            <text class="add-rule" @tap="addRule">+ 添加规则</text>
          </view>
          
          <view class="rules-list">
            <view 
              class="rule-item" 
              v-for="(rule, index) in discountForm.rules" 
              :key="index"
            >
              <view class="rule-inputs">
                <view class="rule-input-group">
                  <text class="input-prefix">满</text>
                  <input 
                    class="rule-input" 
                    type="digit" 
                    v-model="rule.minAmount" 
                    placeholder="0.00"
                  />
                  <text class="input-suffix">元</text>
                </view>
                <text class="rule-separator">减</text>
                <view class="rule-input-group">
                  <input 
                    class="rule-input" 
                    type="digit" 
                    v-model="rule.discountAmount" 
                    placeholder="0.00"
                  />
                  <text class="input-suffix">元</text>
                </view>
              </view>
              <view class="rule-delete" @tap="deleteRule(index)">
                <text class="delete-icon">×</text>
              </view>
            </view>
            
            <view class="empty-rules" v-if="discountForm.rules.length === 0">
              <text class="empty-text">请添加满减规则</text>
            </view>
          </view>
        </view>
        
        <!-- 使用设置 -->
        <view class="form-section">
          <view class="section-header">
            <text class="section-title">使用设置</text>
          </view>
          
          <view class="form-item">
            <text class="item-label">适用商品</text>
            <view class="item-right" @tap="selectProducts">
              <text class="item-value">{{discountForm.applicableProducts || '全部商品'}}</text>
              <text class="item-arrow">></text>
            </view>
          </view>
          
          <view class="form-item">
            <text class="item-label">叠加使用</text>
            <switch 
              :checked="discountForm.canStack" 
              @change="onStackChange" 
              color="#F8D800" 
            />
          </view>
          
          <view class="form-item">
            <text class="item-label">每人限用</text>
            <view class="limit-input-group">
              <input 
                class="limit-input" 
                type="number" 
                v-model="discountForm.perPersonLimit" 
                placeholder="不限制"
              />
              <text class="input-suffix">次</text>
            </view>
          </view>
          
          <view class="form-item textarea-item">
            <text class="item-label">活动说明</text>
            <textarea 
              class="item-textarea" 
              v-model="discountForm.instructions" 
              placeholder="请输入活动说明" 
              maxlength="200"
            ></textarea>
            <text class="textarea-count">{{discountForm.instructions.length}}/200</text>
          </view>
        </view>
        
        <!-- 分销设置 -->
        <view class="form-section" v-if="hasMerchantDistribution">
          <view class="section-header">
            <text class="section-title">分销设置</text>
            <text class="section-desc">设置此活动的分销规则和佣金</text>
          </view>
          
          <distribution-setting 
            :initial-settings="discountForm.distributionSettings"
            @update="updateDistributionSettings"
          />
        </view>
        
        <!-- 营销建议 -->
        <view class="form-section">
          <view class="section-header">
            <text class="section-title">营销建议</text>
          </view>
          
          <view class="tips-list">
            <view class="tip-item">
              <view class="tip-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="12"></line>
                  <line x1="12" y1="16" x2="12.01" y2="16"></line>
                </svg>
              </view>
              <text class="tip-text">满减活动是提高客单价的有效方式，建议满减门槛设置在平均客单价的1.2-1.5倍</text>
          </view>
          <view class="tip-item">
            <view class="tip-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="12"></line>
                <line x1="12" y1="16" x2="12.01" y2="16"></line>
              </svg>
            </view>
            <text class="tip-text">多级满减规则可以覆盖不同消费能力的用户，提高活动参与度</text>
          </view>
          <view class="tip-item">
            <view class="tip-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="12"></line>
                <line x1="12" y1="16" x2="12.01" y2="16"></line>
              </svg>
            </view>
            <text class="tip-text">建议优惠金额控制在满减门槛的10%-15%之间，既有吸引力又能保证利润</text>
          </view>
          </view>
        </view>

        <!-- 活动推广 -->
        <view class="form-section">
          <view class="section-header">
            <text class="section-title">活动推广</text>
            <text class="section-desc">发布、置顶或刷新活动，提升曝光率</text>
          </view>
          
          <MarketingPromotionActions 
            :activity-type="'discount'"
            :activity-id="tempDiscountId"
            :publish-mode-only="true"
            :show-actions="['publish']"
            @action-completed="handlePromotionCompleted"
          />
        </view>
      </view>
    </view>
    
    <!-- 底部保存按钮 -->
    <view class="bottom-save-bar">
      <button class="save-button" @tap="saveDiscount">保存</button>
    </view>
    
    <!-- 日期选择器 -->
    <uni-calendar 
      v-if="showDatePickerDialog"
      :insert="false"
      :start-date="'2020-01-01'"
      :end-date="'2030-12-31'"
      @confirm="onDateSelect"
      @close="closeDatePicker"
    />
</template>

<script>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import DistributionSetting from '../distribution/components/DistributionSetting.vue';
import { distributionService } from '/subPackages/merchant-admin-marketing/services/distributionService';
import MarketingPromotionActions from '/subPackages/merchant-admin-marketing/components/MarketingPromotionActions.vue';

export default {
  components: {
    DistributionSetting,
    MarketingPromotionActions
  },
  setup() {
    // 响应式状态
    const discountForm = reactive({
      title: '',
      startDate: '',
      endDate: '',
      status: 'active',
      rules: [],
      applicableProducts: '全部商品',
      canStack: false,
      perPersonLimit: '3',
      instructions: '',
      distributionSettings: {
        enabled: false,
        commissionMode: 'percentage',
        commissions: {
          level1: '',
          level2: '',
          level3: ''
        },
        enableLevel3: false
      }
    });
    
    const showDatePickerDialog = ref(false);
    const currentDatePicker = ref(''); // 'start' or 'end'
    const hasMerchantDistribution = ref(false); // 商家是否开通分销功能
    const tempDiscountId = ref('temp-' + Date.now()); // 临时ID，实际应该从后端获取
    
    // 方法
    function goBack() {
      uni.navigateBack();
    }
    
    function showDatePicker(type) {
      currentDatePicker.value = type;
      showDatePickerDialog.value = true;
    }
    
    function closeDatePicker() {
      showDatePickerDialog.value = false;
    }
    
    function onDateSelect(e) {
      const selectedDate = e.fulldate;
      if (currentDatePicker.value === 'start') {
        discountForm.startDate = selectedDate;
      } else {
        discountForm.endDate = selectedDate;
      }
      closeDatePicker();
    }
    
    function onStatusChange(e) {
      discountForm.status = e.detail.value ? 'active' : 'paused';
    }
    
    function onStackChange(e) {
      discountForm.canStack = e.detail.value;
    }
    
    function addRule() {
      discountForm.rules.push({
        minAmount: '',
        discountAmount: ''
      });
    }
    
    function deleteRule(index) {
      discountForm.rules.splice(index, 1);
    }
    
    function selectProducts() {
      uni.showActionSheet({
        itemList: ['全部商品', '指定商品', '指定分类'],
        success: (res) => {
          switch(res.tapIndex) {
            case 0:
              discountForm.applicableProducts = '全部商品';
              break;
            case 1:
              // 在实际应用中，这里应该打开商品选择页面
              discountForm.applicableProducts = '指定商品';
              break;
            case 2:
              // 在实际应用中，这里应该打开分类选择页面
              discountForm.applicableProducts = '指定分类';
              break;
          }
        }
      });
    }

    // 更新分销设置
    function updateDistributionSettings(settings) {
      discountForm.distributionSettings = settings;
    }
    
    // 检查商家是否开通分销功能
    async function checkMerchantDistribution() {
      try {
        // 获取商家信息
        const instance = getCurrentInstance();
        const merchantInfo = instance.proxy.$store.state.merchant.merchantInfo || {};
        
        // 检查是否开通分销功能
        hasMerchantDistribution.value = await distributionService.checkMerchantDistribution(merchantInfo);
        
        // 如果开通了分销功能，获取默认设置
        if (hasMerchantDistribution.value) {
          const settings = await distributionService.getMerchantDistributionSettings(merchantInfo);
          discountForm.distributionSettings = settings;
        }
      } catch (error) {
        console.error('检查分销功能失败', error);
        hasMerchantDistribution.value = false;
      }
    }
    
    function validateForm() {
      if (!discountForm.title.trim()) {
        uni.showToast({
          title: '请输入活动名称',
          icon: 'none'
        });
        return false;
      }
      
      if (!discountForm.startDate || !discountForm.endDate) {
        uni.showToast({
          title: '请选择活动时间',
          icon: 'none'
        });
        return false;
      }
      
      if (discountForm.rules.length === 0) {
        uni.showToast({
          title: '请添加至少一条满减规则',
          icon: 'none'
        });
        return false;
      }
      
      for (const rule of discountForm.rules) {
        if (!rule.minAmount || !rule.discountAmount) {
          uni.showToast({
            title: '请完善满减规则',
            icon: 'none'
          });
          return false;
        }
        
        if (parseFloat(rule.discountAmount) >= parseFloat(rule.minAmount)) {
          uni.showToast({
            title: '优惠金额不能大于等于满减金额',
            icon: 'none'
          });
          return false;
        }
      }
      
      return true;
    }
    
    // 保存满减活动
    async function saveDiscount() {
      if (!validateForm()) {
        return;
      }
      
      // 在实际应用中，这里应该调用API保存数据
      
      uni.showLoading({
        title: '保存中...'
      });

      // 处理分销设置
      if (hasMerchantDistribution.value && discountForm.distributionSettings.enabled) {
        // 验证分销设置
        const { valid, errors } = distributionService.validateDistributionSettings(discountForm.distributionSettings);
        if (!valid) {
          uni.showToast({
            title: errors[0],
            icon: 'none'
          });
          return;
        }
        
        // 保存分销设置
        const success = await distributionService.saveActivityDistributionSettings('discount', 'temp-id-123', discountForm.distributionSettings);
        if (!success) {
          return;
        }
      }
      
      setTimeout(() => {
        uni.hideLoading();
        
        uni.showToast({
          title: '创建成功',
          icon: 'success'
        });
        
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }, 1000);
    }
    
    // 处理推广操作完成事件
    const handlePromotionCompleted = (data) => {
      console.log('推广操作完成:', data);
      // 根据不同操作类型处理结果
      if (data.action === 'publish') {
        uni.showToast({
          title: '发布成功',
          icon: 'success'
        });
      } else if (data.action === 'top') {
        uni.showToast({
          title: '置顶成功',
          icon: 'success'
        });
      } else if (data.action === 'refresh') {
        uni.showToast({
          title: '刷新成功',
          icon: 'success'
        });
      }
    };
    
    onMounted(() => {
      // 默认添加一条空规则
      addRule();
      checkMerchantDistribution();
    });
    
    return {
      discountForm,
      showDatePickerDialog,
      currentDatePicker,
      goBack,
      showDatePicker,
      closeDatePicker,
      onDateSelect,
      onStatusChange,
      onStackChange,
      addRule,
      deleteRule,
      selectProducts,
      saveDiscount,
      updateDistributionSettings,
      checkMerchantDistribution,
      hasMerchantDistribution,
      handlePromotionCompleted,
      tempDiscountId
    };
  }
}
</script>

<style lang="scss">
.discount-create-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 80px; /* 为底部按钮留出空间 */
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FDEB71, #F8D800);
  color: #333;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(248, 216, 0, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #333;
  border-bottom: 2px solid #333;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  padding: 0 10px;
}

.save-btn {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

/* 表单样式 */
.create-form {
  padding: 15px;
}

.form-section {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-desc {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.add-rule {
  font-size: 14px;
  color: #F8D800;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.form-item:last-child {
  border-bottom: none;
}

.item-label {
  width: 80px;
  font-size: 14px;
  color: #666;
}

.item-input {
  flex: 1;
  height: 24px;
  font-size: 14px;
  color: #333;
}

.input-count {
  font-size: 12px;
  color: #999;
  margin-left: 10px;
}

.date-picker {
  flex: 1;
  height: 36px;
  background: #f5f5f5;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
}

.date-text {
  font-size: 14px;
  color: #333;
}

.date-separator {
  margin: 0 10px;
  color: #999;
}

.status-switch {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.status-text {
  font-size: 14px;
  color: #333;
  margin-right: 10px;
}

/* 规则样式 */
.rules-list {
  
}

.rule-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  background: #f9f9f9;
  border-radius: 8px;
  padding: 12px;
}

.rule-inputs {
  flex: 1;
  display: flex;
  align-items: center;
}

.rule-input-group {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 6px;
  padding: 8px 12px;
  border: 1px solid #eee;
}

.input-prefix {
  font-size: 14px;
  color: #666;
  margin-right: 5px;
}

.rule-input {
  width: 80px;
  font-size: 14px;
  color: #333;
  text-align: center;
}

.input-suffix {
  font-size: 14px;
  color: #666;
  margin-left: 5px;
}

.rule-separator {
  margin: 0 10px;
  color: #666;
  font-size: 14px;
}

.rule-delete {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
}

.delete-icon {
  font-size: 18px;
  color: #999;
}

.empty-rules {
  padding: 30px 0;
  display: flex;
  justify-content: center;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 使用设置样式 */
.item-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.item-value {
  font-size: 14px;
  color: #333;
  margin-right: 5px;
}

.item-arrow {
  font-size: 14px;
  color: #999;
}

.limit-input-group {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.limit-input {
  width: 60px;
  font-size: 14px;
  color: #333;
  text-align: right;
}

.textarea-item {
  flex-direction: column;
  align-items: flex-start;
}

.item-textarea {
  width: 100%;
  height: 100px;
  font-size: 14px;
  color: #333;
  background: #f9f9f9;
  border-radius: 8px;
  padding: 10px;
  margin-top: 10px;
  box-sizing: border-box;
}

.textarea-count {
  align-self: flex-end;
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

/* 分销设置样式 */
.form-section .distribution-setting {
  margin-top: 0;
  padding: 0;
}

/* 营销建议样式 */
.tips-list {
  
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  margin-top: 2px;
  color: #F8D800;
}

.tip-text {
  flex: 1;
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}

/* 活动推广样式 */
.form-section .marketing-promotion-container {
  margin-top: 10px;
}

/* 底部保存按钮样式 */
.bottom-save-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 15px;
  background: #fff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 90;
}

.save-button {
  width: 100%;
  height: 44px;
  background: linear-gradient(135deg, #FDEB71, #F8D800);
  color: #333;
  font-size: 16px;
  font-weight: 500;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}
</style>