{"version": 3, "file": "create-package-price.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/group/create-package-price.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xncm91cFxjcmVhdGUtcGFja2FnZS1wcmljZS52dWU"], "sourcesContent": ["<!-- 创建中 -->\n<template>\n  <view class=\"create-package-price-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">拼团活动</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 步骤指示器 -->\n    <view class=\"step-indicator\">\n      <view class=\"step-progress\">\n        <view class=\"step-progress-bar\" style=\"width: 60%\"></view>\n      </view>\n      <view class=\"step-text\">步骤 3/5</view>\n    </view>\n    \n    <!-- 页面内容 -->\n    <scroll-view scroll-y class=\"page-content\">\n      <view class=\"page-title\">设置团购套餐价格</view>\n      <view class=\"page-subtitle\">请设置团购套餐的价格信息</view>\n      \n      <view class=\"form-section\">\n        <view class=\"form-item\">\n          <text class=\"form-label\">市场价 <text class=\"required\">*</text></text>\n          <view class=\"price-input-wrapper\">\n            <text class=\"price-symbol\">¥</text>\n            <input class=\"form-input\" type=\"digit\" v-model=\"priceInfo.marketPrice\" placeholder=\"请输入套餐市场价\" />\n          </view>\n          <text class=\"form-tip\">市场价是指套餐内所有商品/服务的原价总和</text>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"form-label\">日常价 <text class=\"required\">*</text></text>\n          <view class=\"price-input-wrapper\">\n            <text class=\"price-symbol\">¥</text>\n            <input class=\"form-input\" type=\"digit\" v-model=\"priceInfo.regularPrice\" placeholder=\"请输入套餐日常价\" />\n          </view>\n          <text class=\"form-tip\">日常价是指套餐平时销售的价格</text>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"form-label\">拼团价 <text class=\"required\">*</text></text>\n          <view class=\"price-input-wrapper\">\n            <text class=\"price-symbol\">¥</text>\n            <input class=\"form-input\" type=\"digit\" v-model=\"priceInfo.groupPrice\" placeholder=\"请输入套餐拼团价\" />\n          </view>\n          <text class=\"form-tip\">拼团价是指用户参与拼团时的优惠价格</text>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"form-label\">单人限购</text>\n          <view class=\"number-picker\">\n            <view class=\"number-btn minus\" @tap=\"decrementLimitPerUser\">-</view>\n            <input class=\"number-input\" type=\"number\" v-model=\"priceInfo.limitPerUser\" />\n            <view class=\"number-btn plus\" @tap=\"incrementLimitPerUser\">+</view>\n            <text class=\"unit-text\">件</text>\n          </view>\n          <text class=\"form-tip\">0表示不限购，最多可设置为99</text>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"form-label\">库存数量 <text class=\"required\">*</text></text>\n          <view class=\"number-picker\">\n            <view class=\"number-btn minus\" @tap=\"decrementStock\">-</view>\n            <input class=\"number-input\" type=\"number\" v-model=\"priceInfo.stock\" />\n            <view class=\"number-btn plus\" @tap=\"incrementStock\">+</view>\n            <text class=\"unit-text\">件</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 价格预览 -->\n      <view class=\"price-preview\">\n        <view class=\"preview-header\">价格预览</view>\n        \n        <view class=\"preview-content\">\n          <view class=\"preview-item\">\n            <text class=\"preview-label\">市场价</text>\n            <text class=\"preview-value original\">¥{{formatPrice(priceInfo.marketPrice)}}</text>\n          </view>\n          \n          <view class=\"preview-item\">\n            <text class=\"preview-label\">日常价</text>\n            <text class=\"preview-value regular\">¥{{formatPrice(priceInfo.regularPrice)}}</text>\n          </view>\n          \n          <view class=\"preview-item\">\n            <text class=\"preview-label\">拼团价</text>\n            <text class=\"preview-value group\">¥{{formatPrice(priceInfo.groupPrice)}}</text>\n          </view>\n          \n          <view class=\"preview-item\">\n            <text class=\"preview-label\">节省金额</text>\n            <text class=\"preview-value save\">¥{{calculateSavings()}}</text>\n          </view>\n          \n          <view class=\"preview-item\">\n            <text class=\"preview-label\">折扣率</text>\n            <text class=\"preview-value discount\">{{calculateDiscount()}}折</text>\n          </view>\n        </view>\n      </view>\n    </scroll-view>\n    \n    <!-- 底部按钮 -->\n    <view class=\"footer-buttons\">\n      <button class=\"btn btn-secondary\" @click=\"goBack\">上一步</button>\n      <button class=\"btn btn-primary\" @click=\"nextStep\">下一步</button>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      priceInfo: {\n        marketPrice: '',\n        regularPrice: '',\n        groupPrice: '',\n        limitPerUser: 1,\n        stock: 100\n      }\n    }\n  },\n  onLoad() {\n    // 尝试从本地存储获取之前保存的数据\n    try {\n      const savedPriceInfo = uni.getStorageSync('packagePriceInfo');\n      if (savedPriceInfo) {\n        this.priceInfo = JSON.parse(savedPriceInfo);\n      }\n    } catch (e) {\n      console.error('读取本地存储失败:', e);\n    }\n  },\n  methods: {\n    goBack() {\n      // 保存当前页面数据\n      this.saveData();\n      uni.navigateBack();\n    },\n    nextStep() {\n      // 表单验证\n      if (!this.priceInfo.marketPrice) {\n        uni.showToast({\n          title: '请输入市场价',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      if (!this.priceInfo.regularPrice) {\n        uni.showToast({\n          title: '请输入日常价',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      if (!this.priceInfo.groupPrice) {\n        uni.showToast({\n          title: '请输入拼团价',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 检查价格大小关系\n      const marketPrice = parseFloat(this.priceInfo.marketPrice);\n      const regularPrice = parseFloat(this.priceInfo.regularPrice);\n      const groupPrice = parseFloat(this.priceInfo.groupPrice);\n      \n      if (marketPrice <= regularPrice) {\n        uni.showToast({\n          title: '市场价应高于日常价',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      if (regularPrice <= groupPrice) {\n        uni.showToast({\n          title: '日常价应高于拼团价',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 保存数据并跳转到下一步\n      this.saveData();\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-items'\n      });\n    },\n    saveData() {\n      // 保存当前页面数据到本地存储\n      try {\n        uni.setStorageSync('packagePriceInfo', JSON.stringify(this.priceInfo));\n      } catch (e) {\n        console.error('保存数据失败:', e);\n      }\n    },\n    formatPrice(price) {\n      if (!price) return '0.00';\n      return parseFloat(price).toFixed(2);\n    },\n    calculateSavings() {\n      const marketPrice = parseFloat(this.priceInfo.marketPrice) || 0;\n      const groupPrice = parseFloat(this.priceInfo.groupPrice) || 0;\n      const savings = marketPrice - groupPrice;\n      return savings > 0 ? savings.toFixed(2) : '0.00';\n    },\n    calculateDiscount() {\n      const marketPrice = parseFloat(this.priceInfo.marketPrice) || 1;\n      const groupPrice = parseFloat(this.priceInfo.groupPrice) || 0;\n      const discount = (groupPrice / marketPrice) * 10;\n      return discount.toFixed(1);\n    },\n    decrementLimitPerUser() {\n      if (this.priceInfo.limitPerUser > 0) {\n        this.priceInfo.limitPerUser--;\n      }\n    },\n    incrementLimitPerUser() {\n      if (this.priceInfo.limitPerUser < 99) {\n        this.priceInfo.limitPerUser++;\n      }\n    },\n    decrementStock() {\n      if (this.priceInfo.stock > 1) {\n        this.priceInfo.stock--;\n      }\n    },\n    incrementStock() {\n      if (this.priceInfo.stock < 9999) {\n        this.priceInfo.stock++;\n      }\n    },\n    showHelp() {\n      uni.showToast({\n        title: '帮助信息',\n        icon: 'none'\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.create-package-price-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  display: flex;\n  flex-direction: column;\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #9040FF, #5E35B1);\n  color: #fff;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 20px;\n  height: 20px;\n  border-radius: 50%;\n  border: 1px solid #fff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  color: #fff;\n}\n\n/* 步骤指示器 */\n.step-indicator {\n  padding: 15px;\n  background: #FFFFFF;\n}\n\n.step-progress {\n  height: 4px;\n  background-color: #EBEDF5;\n  border-radius: 2px;\n  margin-bottom: 5px;\n  position: relative;\n}\n\n.step-progress-bar {\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 100%;\n  background: linear-gradient(90deg, #9040FF, #5E35B1);\n  border-radius: 2px;\n}\n\n.step-text {\n  font-size: 12px;\n  color: #999;\n  text-align: right;\n}\n\n/* 页面内容 */\n.page-content {\n  flex: 1;\n  padding: 20px 15px;\n}\n\n.page-title {\n  font-size: 20px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.page-subtitle {\n  font-size: 14px;\n  color: #999;\n  margin-bottom: 20px;\n}\n\n/* 表单样式 */\n.form-section {\n  background: #FFFFFF;\n  border-radius: 12px;\n  padding: 15px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n}\n\n.form-item {\n  margin-bottom: 20px;\n}\n\n.form-item:last-child {\n  margin-bottom: 0;\n}\n\n.form-label {\n  font-size: 14px;\n  color: #333;\n  margin-bottom: 8px;\n  display: block;\n}\n\n.required {\n  color: #FF3B30;\n}\n\n.price-input-wrapper {\n  display: flex;\n  align-items: center;\n  background: #F5F7FA;\n  border-radius: 8px;\n  border: 1px solid #EBEDF5;\n  padding: 0 12px;\n}\n\n.price-symbol {\n  font-size: 14px;\n  color: #333;\n  margin-right: 5px;\n}\n\n.form-input {\n  flex: 1;\n  height: 45px;\n  font-size: 14px;\n  color: #333;\n  background: transparent;\n}\n\n.form-tip {\n  font-size: 12px;\n  color: #999;\n  margin-top: 5px;\n}\n\n.number-picker {\n  display: flex;\n  align-items: center;\n  height: 45px;\n}\n\n.number-btn {\n  width: 45px;\n  height: 45px;\n  background: #F5F7FA;\n  border: 1px solid #EBEDF5;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 18px;\n  color: #333;\n}\n\n.number-btn.minus {\n  border-radius: 8px 0 0 8px;\n}\n\n.number-btn.plus {\n  border-radius: 0 8px 8px 0;\n}\n\n.number-input {\n  width: 60px;\n  height: 45px;\n  background: #F5F7FA;\n  border-top: 1px solid #EBEDF5;\n  border-bottom: 1px solid #EBEDF5;\n  text-align: center;\n  font-size: 14px;\n  color: #333;\n}\n\n.unit-text {\n  margin-left: 10px;\n  font-size: 14px;\n  color: #666;\n}\n\n/* 价格预览 */\n.price-preview {\n  background: #FFFFFF;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n}\n\n.preview-header {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 15px;\n  padding-bottom: 10px;\n  border-bottom: 1px dashed #EBEDF5;\n}\n\n.preview-content {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.preview-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.preview-label {\n  font-size: 14px;\n  color: #666;\n}\n\n.preview-value {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.preview-value.original {\n  text-decoration: line-through;\n  color: #999;\n  font-weight: normal;\n}\n\n.preview-value.regular {\n  color: #666;\n}\n\n.preview-value.group {\n  color: #FF3B30;\n}\n\n.preview-value.save {\n  color: #FF9500;\n}\n\n.preview-value.discount {\n  color: #34C759;\n}\n\n/* 底部按钮 */\n.footer-buttons {\n  padding: 15px;\n  background: #FFFFFF;\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);\n  display: flex;\n  justify-content: space-between;\n  gap: 15px;\n}\n\n.btn {\n  flex: 1;\n  height: 50px;\n  border-radius: 25px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n  font-weight: 600;\n  border: none;\n}\n\n.btn-primary {\n  background: linear-gradient(135deg, #9040FF, #5E35B1);\n  color: #FFFFFF;\n}\n\n.btn-secondary {\n  background: #F5F7FA;\n  color: #666;\n  border: 1px solid #EBEDF5;\n}\n</style>\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-price.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAuHA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,QACT,aAAa;AAAA,QACb,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAEP,QAAI;AACF,YAAM,iBAAiBA,cAAAA,MAAI,eAAe,kBAAkB;AAC5D,UAAI,gBAAgB;AAClB,aAAK,YAAY,KAAK,MAAM,cAAc;AAAA,MAC5C;AAAA,IACF,SAAS,GAAG;AACVA,oBAAc,MAAA,MAAA,SAAA,8FAAA,aAAa,CAAC;AAAA,IAC9B;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AAEP,WAAK,SAAQ;AACbA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,WAAW;AAET,UAAI,CAAC,KAAK,UAAU,aAAa;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,UAAU,cAAc;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,UAAU,YAAY;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAGA,YAAM,cAAc,WAAW,KAAK,UAAU,WAAW;AACzD,YAAM,eAAe,WAAW,KAAK,UAAU,YAAY;AAC3D,YAAM,aAAa,WAAW,KAAK,UAAU,UAAU;AAEvD,UAAI,eAAe,cAAc;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,gBAAgB,YAAY;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAGA,WAAK,SAAQ;AACbA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IACD,WAAW;AAET,UAAI;AACFA,sBAAG,MAAC,eAAe,oBAAoB,KAAK,UAAU,KAAK,SAAS,CAAC;AAAA,MACvE,SAAS,GAAG;AACVA,yIAAc,WAAW,CAAC;AAAA,MAC5B;AAAA,IACD;AAAA,IACD,YAAY,OAAO;AACjB,UAAI,CAAC;AAAO,eAAO;AACnB,aAAO,WAAW,KAAK,EAAE,QAAQ,CAAC;AAAA,IACnC;AAAA,IACD,mBAAmB;AACjB,YAAM,cAAc,WAAW,KAAK,UAAU,WAAW,KAAK;AAC9D,YAAM,aAAa,WAAW,KAAK,UAAU,UAAU,KAAK;AAC5D,YAAM,UAAU,cAAc;AAC9B,aAAO,UAAU,IAAI,QAAQ,QAAQ,CAAC,IAAI;AAAA,IAC3C;AAAA,IACD,oBAAoB;AAClB,YAAM,cAAc,WAAW,KAAK,UAAU,WAAW,KAAK;AAC9D,YAAM,aAAa,WAAW,KAAK,UAAU,UAAU,KAAK;AAC5D,YAAM,WAAY,aAAa,cAAe;AAC9C,aAAO,SAAS,QAAQ,CAAC;AAAA,IAC1B;AAAA,IACD,wBAAwB;AACtB,UAAI,KAAK,UAAU,eAAe,GAAG;AACnC,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA,IACD,wBAAwB;AACtB,UAAI,KAAK,UAAU,eAAe,IAAI;AACpC,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA,IACD,iBAAiB;AACf,UAAI,KAAK,UAAU,QAAQ,GAAG;AAC5B,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA,IACD,iBAAiB;AACf,UAAI,KAAK,UAAU,QAAQ,MAAM;AAC/B,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA,IACD,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3PA,GAAG,WAAW,eAAe;"}