/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 覆盖整个顶部区域的蓝色容器 */
.blue-header-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  pointer-events: auto;
}

/* 蓝色头部区域 */
.blue-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: #3846cd;
  width: 100%;
  /* 这里的padding-top会被动态设置 */
  pointer-events: auto;
}
.safe-area-inset-top {
  display: none;
  /* 不再需要这个元素 */
}

/* 页面整体样式 */
.shop-detail-page {
  min-height: 100vh;
  width: 100vw;
  background-color: #f7f8fc;
  position: relative;
  padding-bottom: 120rpx;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* 系统标题栏 */
.navbar {
  height: 90rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx 0 20rpx;
  width: 100%;
  box-sizing: border-box;
}
.navbar-left, .navbar-right {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon-wrap {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  background-color: transparent;
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}
.back-icon-wrap:active {
  transform: scale(0.92);
  background-color: transparent;
}
.back-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 1;
  filter: brightness(0) invert(1);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 34rpx;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* 顶部图片轮播 */
.shop-gallery {
  position: relative;
  width: 100%;
  height: 520rpx;
  margin-top: calc(var(--status-bar-height, 44px) + 90rpx);
  border-radius: 0 0 40rpx 40rpx;
  overflow: hidden;
  box-shadow: 0 12rpx 30rpx rgba(0, 0, 0, 0.08);
}
.gallery-swiper {
  width: 100%;
  height: 100%;
}
.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 商家基本信息卡片 */
.shop-info-card {
  margin: -70rpx 30rpx 30rpx;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  padding: 36rpx;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 2;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
}
.shop-basic-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.shop-logo-container {
  width: 130rpx;
  height: 130rpx;
  border-radius: 24rpx;
  background-color: #f5f7fa;
  overflow: hidden;
  margin-right: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  border: 2rpx solid rgba(255, 255, 255, 0.8);
  position: relative;
}
.shop-logo-container::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 24rpx;
  box-shadow: inset 0 0 0 1rpx rgba(255, 255, 255, 0.4);
  pointer-events: none;
}
.shop-logo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.shop-title-container {
  flex: 1;
}
.shop-name {
  font-size: 40rpx;
  font-weight: 600;
  color: #222;
  margin-bottom: 16rpx;
  display: block;
  letter-spacing: -0.5rpx;
}
.shop-category {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.category-tag {
  font-size: 24rpx;
  color: #2738C0;
  background-color: rgba(39, 56, 192, 0.08);
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
  font-weight: 500;
}
.shop-scale {
  font-size: 24rpx;
  color: #666;
  background-color: #f0f2f5;
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
}
.shop-stats {
  display: flex;
  align-items: center;
}
.stat-item {
  font-size: 24rpx;
  color: #888;
}
.stat-divider {
  margin: 0 16rpx;
  color: #ddd;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 36rpx;
}
.action-btn {
  flex: 1;
  height: 70rpx;
  line-height: 70rpx;
  border-radius: 35rpx;
  text-align: center;
  margin: 0 10rpx;
  font-size: 28rpx;
  color: #FFFFFF;
  background: linear-gradient(to right, #007AFF, #5AC8FA);
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}
.follow-btn {
  background: linear-gradient(to right, #007AFF, #5AC8FA);
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}
.following {
  background: linear-gradient(to right, #8E8E93, #C7C7CC);
  box-shadow: 0 4rpx 12rpx rgba(142, 142, 147, 0.2);
}
.contact-btn {
  background: linear-gradient(to right, #007AFF, #5AC8FA);
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}
.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
}
.btn-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
  flex-shrink: 0;
}
.phone-icon {
  filter: brightness(0) invert(1);
}
.btn-text {
  color: inherit;
  font-family: "AlimamaShuHeiTi", sans-serif;
  font-size: 28rpx;
  line-height: 28rpx;
  padding-top: 2rpx;
}

/* 详情卡片通用样式 */
.detail-section {
  margin: 30rpx 30rpx 0;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  padding: 36rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
  transition: all 0.3s ease;
}
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.header-left {
  display: flex;
  align-items: center;
}
.section-icon-wrap {
  width: 56rpx;
  height: 56rpx;
  border-radius: 16rpx;
  background-color: rgba(39, 56, 192, 0.08);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}
.section-icon {
  width: 32rpx;
  height: 32rpx;
  display: block;
}
.section-title {
  display: none !important;
}
.section-title-text {
  font-size: 34rpx;
  font-weight: 600;
  color: #222;
  letter-spacing: -0.5rpx;
  line-height: 1.2;
  padding: 0 4rpx;
  display: inline-block;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}
.view-all {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #2738C0;
  background-color: rgba(39, 56, 192, 0.08);
  padding: 8rpx 20rpx;
  border-radius: 28rpx;
}
.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  margin-left: 8rpx;
}

/* 信息列表 */
.info-list {
  background-color: rgba(249, 250, 252, 0.7);
  border-radius: 28rpx;
  overflow: hidden;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}
.info-item {
  display: flex;
  align-items: center;
  padding: 28rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.04);
}
.info-item:last-child {
  border-bottom: none;
}
.info-icon-wrap {
  width: 68rpx;
  height: 68rpx;
  border-radius: 20rpx;
  background-color: rgba(39, 56, 192, 0.08);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}
.info-icon {
  width: 36rpx;
  height: 36rpx;
}
.info-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.info-label {
  font-size: 26rpx;
  color: #888;
  margin-bottom: 8rpx;
}
.info-value {
  font-size: 30rpx;
  color: #222;
  font-weight: 500;
}
.navigate-btn {
  padding: 6rpx 24rpx;
  background-color: rgba(39, 56, 192, 0.08);
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #2738C0;
  margin-left: 20rpx;
  font-weight: 500;
}
.action-icon-wrap {
  margin-left: 20rpx;
}
.action-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.3;
}
.location-item {
  align-items: flex-start;
}

/* 商家介绍 */
.description-content {
  background-color: rgba(249, 250, 252, 0.7);
  border-radius: 28rpx;
  padding: 28rpx;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}
.description-text {
  font-size: 30rpx;
  color: #444;
  line-height: 1.7;
  white-space: pre-wrap;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

/* 商家相册 */
.photos-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8rpx;
}
.photo-item {
  width: calc(33.33% - 16rpx);
  aspect-ratio: 1;
  margin: 8rpx;
  border-radius: 24rpx;
  overflow: hidden;
  background-color: #f5f7fa;
  box-shadow: 0 8rpx 15rpx rgba(0, 0, 0, 0.05);
  position: relative;
  transform: translateZ(0);
  transition: transform 0.2s ease;
}
.photo-item:active {
  transform: scale(0.97);
}
.photo-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 二维码 */
.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 0;
}
.qrcode-image {
  width: 320rpx;
  height: 320rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.1);
  border-radius: 28rpx;
  background-color: white;
  padding: 20rpx;
}
.qrcode-tip {
  font-size: 26rpx;
  color: #888;
  background-color: rgba(39, 56, 192, 0.08);
  padding: 12rpx 30rpx;
  border-radius: 30rpx;
}

/* 底部工具栏 */
.bottom-toolbar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 110rpx;
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 -5rpx 20rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  padding: 0;
  z-index: 100;
  border-top: 1rpx solid rgba(0, 0, 0, 0.03);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}
.toolbar-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  flex: 1;
  position: relative;
  transition: opacity 0.2s ease;
}
.toolbar-btn:active {
  opacity: 0.7;
}
.toolbar-call-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 85%;
  flex: 2.5;
  background-color: #2738C0;
  border-radius: 36rpx;
  margin: 12rpx 15rpx;
  box-shadow: 0 6rpx 15rpx rgba(39, 56, 192, 0.2);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}
.toolbar-call-btn:active {
  transform: scale(0.97);
  box-shadow: 0 4rpx 10rpx rgba(39, 56, 192, 0.2);
}
.call-text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}
.call-subtext {
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.85);
  margin-top: 2rpx;
  white-space: nowrap;
}
.toolbar-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}
.toolbar-text {
  font-size: 22rpx;
  color: #444;
}

/* 添加联系电话下方的提示样式 */
.info-tip {
  font-size: 22rpx;
  color: #3846cd;
  margin-top: 6rpx;
}

/* 悬浮海报按钮 */
.float-poster-btn {
  position: fixed;
  right: 30rpx;
  bottom: 200rpx;
  width: 100rpx;
  height: 100rpx;
  background: rgba(240, 240, 240, 0.9);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
  z-index: 90;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1rpx solid rgba(230, 230, 230, 0.6);
  transition: all 0.2s ease;
}
.float-poster-btn:active {
  transform: scale(0.95);
  box-shadow: 0 3rpx 10rpx rgba(0, 0, 0, 0.08);
}
.poster-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 4rpx;
}
.poster-text {
  font-size: 20rpx;
  color: #444;
  line-height: 1;
}

/* 添加举报图标样式 */
.report-icon-wrap {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}
.report-icon-wrap:active {
  transform: scale(0.92);
  background-color: rgba(255, 255, 255, 0.25);
}
.report-icon {
  width: 36rpx;
  height: 36rpx;
  opacity: 1;
  filter: brightness(0) invert(1);
}

/* 举报提示卡片样式 */
.report-tip-section {
  margin: 30rpx 30rpx 0;
  background-color: #f9fafe;
  border-radius: 32rpx;
  padding: 20rpx 24rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}
.report-tip-content {
  display: flex;
  align-items: center;
}
.report-tip-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 12rpx;
}
.report-tip-text {
  flex: 1;
  font-size: 24rpx;
  color: #666;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}
.report-arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.3;
}

/* 确保所有文本在真机上正确显示的全局设置 */
text, view {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

/* 消费红包相关样式 */
.consume-red-packet-container {
  padding: 20rpx;
}
.consume-activity-card {
  background: linear-gradient(135deg, #fff5f5, #fff1f0);
  border-radius: 28rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 69, 58, 0.1);
  border: 1px solid rgba(255, 69, 58, 0.15);
}
.activity-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.merchant-logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  border: 1px solid rgba(255, 255, 255, 0.6);
}
.activity-info {
  flex: 1;
}
.activity-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff4538;
  margin-bottom: 10rpx;
  display: block;
}
.activity-desc {
  font-size: 24rpx;
  color: #666;
  display: block;
}
.activity-details {
  margin-top: 20rpx;
}
.detail-item {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}
.button-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
}
.draw-button {
  background: linear-gradient(135deg, #ff4538, #ff7b6c);
  color: #fff;
  font-size: 28rpx;
  padding: 10rpx 40rpx;
  border-radius: 30rpx;
  border: none;
  box-shadow: 0 4rpx 8rpx rgba(255, 69, 58, 0.3);
}
.detail-text {
  font-size: 24rpx;
  color: #999;
}
.more-text {
  font-size: 24rpx;
  color: #ff4538;
  padding: 4rpx 12rpx;
  background: rgba(255, 69, 58, 0.1);
  border-radius: 20rpx;
}

/* 红包弹窗样式 */
.red-packet-popup {
  background-color: transparent;
  width: 600rpx;
}
.drawing-area {
  background: rgba(0, 0, 0, 0.6);
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.red-packet-animation {
  text-align: center;
}
.red-packet-img {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.drawing-text {
  font-size: 32rpx;
  color: #fff;
  margin-top: 20rpx;
}
.result-area {
  background: linear-gradient(135deg, #FA2A2D, #FF4E4E);
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 16rpx rgba(255, 0, 0, 0.2);
}
.result-content {
  text-align: center;
}
.result-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}
.result-title {
  font-size: 36rpx;
  color: #FFE4B5;
  margin-bottom: 20rpx;
  display: block;
}
.result-amount {
  font-size: 80rpx;
  font-weight: bold;
  color: #FFFFFF;
  margin: 20rpx 0;
  display: block;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.result-desc {
  font-size: 28rpx;
  color: #FFE4B5;
  margin-bottom: 40rpx;
  display: block;
}
.result-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
}
.use-now-btn, .close-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  margin: 0 10rpx;
}
.use-now-btn {
  background-color: #FFFFFF;
  color: #FA2A2D;
  border: none;
}
.close-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: #FFFFFF;
  border: 1rpx solid rgba(255, 255, 255, 0.5);
}