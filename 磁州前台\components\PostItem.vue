// 引入红包卡片和弹窗组件
import { RedPacketCard, RedPacketPopup } from '@/components/RedPacket';
import { getRedPacketDetail } from '@/utils/redPacket.js';

export default {
  components: {
    RedPacketCard,
    RedPacketPopup
  },
  data() {
    return {
      redPacket: null, // 红包数据
      showRedPacketPopup: false, // 是否显示红包弹窗
      redPacketRecords: [], // 红包领取记录
      redPacketPopupStep: 'grab', // 红包弹窗步骤
    };
  },
  computed: {
    // 是否包含红包
    hasRedPacket() {
      return this.post && this.post.redPacket;
    },
  },
  mounted() {
    // 如果存在红包，获取红包数据
    if (this.hasRedPacket) {
      this.fetchRedPacketData();
    }
  },
  methods: {
    // 获取红包数据
    fetchRedPacketData() {
      getRedPacketDetail(this.post.redPacket).then(res => {
        this.redPacket = res;
      }).catch(err => {
        console.error('获取红包数据失败:', err);
      });
    },
    
    // 点击抢红包
    handleGrabRedPacket() {
      this.redPacketPopupStep = 'grab';
      this.showRedPacketPopup = true;
    },
    
    // 抢红包成功
    handleGrabSuccess(result) {
      // 刷新红包数据
      this.fetchRedPacketData();
    },
    
    // 查看红包详情
    handleViewRedPacket() {
      this.redPacketPopupStep = 'detail';
      this.showRedPacketPopup = true;
      
      // 获取红包领取记录
      // 这里应该调用获取红包记录的接口
      this.redPacketRecords = []; // 模拟数据
    },
  }
} 