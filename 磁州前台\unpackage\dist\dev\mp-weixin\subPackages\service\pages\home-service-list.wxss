/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-59785f67, html.data-v-59785f67, #app.data-v-59785f67, .index-container.data-v-59785f67 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.service-list-container.data-v-59785f67 {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

/* 自定义导航栏 */
.custom-navbar.data-v-59785f67 {
  display: flex;
  align-items: center;
  height: 44px;
  background: #1677FF;
  color: #fff;
  padding: 0 15px;
  position: relative;
  z-index: 100;
  box-shadow: none;
  border-radius: 0;
}
.back-btn.data-v-59785f67 {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.back-icon.data-v-59785f67 {
  width: 10px;
  height: 10px;
  border-top: 2px solid #fff;
  border-left: 2px solid #fff;
  transform: rotate(-45deg);
}
.navbar-title.data-v-59785f67 {
  flex: 1;
  text-align: center;
  color: #fff;
  font-size: 18px;
  font-weight: 500;
}
.navbar-right.data-v-59785f67 {
  width: 40px;
}

/* 分类标签栏 */
.category-tabs.data-v-59785f67 {
  background-color: #ffffff;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: none;
  border-bottom: 1rpx solid #f5f5f5;
  margin-top: 0;
}
.tabs-scroll.data-v-59785f67 {
  white-space: nowrap;
  height: 80rpx;
  padding: 0 10rpx;
}
.tab-item.data-v-59785f67 {
  display: inline-block;
  padding: 0 30rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  color: #333;
  position: relative;
}
.tab-item.active.data-v-59785f67 {
  color: #1677FF;
  font-weight: 500;
}
.tab-item.active.data-v-59785f67::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #1677FF;
  border-radius: 2rpx;
}

/* 筛选栏 */
.filter-section.data-v-59785f67 {
  display: flex;
  align-items: center;
  height: 80rpx;
  background-color: #fff;
  padding: 0 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.filter-item.data-v-59785f67 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 28rpx;
  color: #666;
  position: relative;
}
.filter-item.data-v-59785f67:not(:last-child)::after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1rpx;
  height: 30rpx;
  background-color: #f0f0f0;
}
.filter-text.data-v-59785f67 {
  font-size: 28rpx;
  color: #666;
}
.active-filter.data-v-59785f67 {
  color: #1677FF;
  font-weight: 500;
}
.filter-arrow.data-v-59785f67 {
  width: 14rpx;
  height: 14rpx;
  border-right: 2rpx solid #999;
  border-bottom: 2rpx solid #999;
  transform: rotate(45deg);
  margin-left: 8rpx;
  margin-top: -6rpx;
  transition: transform 0.2s;
}
.filter-arrow.arrow-up.data-v-59785f67 {
  transform: rotate(-135deg);
  margin-top: 6rpx;
  border-right: 2rpx solid #1677FF;
  border-bottom: 2rpx solid #1677FF;
}
.filter-icon.data-v-59785f67 {
  width: 24rpx;
  height: 24rpx;
  border: none;
  position: relative;
  margin-left: 8rpx;
  transform: none;
}
.filter-icon.data-v-59785f67::before, .filter-icon.data-v-59785f67::after {
  content: "";
  position: absolute;
  background-color: #999;
}
.filter-icon.data-v-59785f67::before {
  width: 100%;
  height: 2rpx;
  top: 6rpx;
  left: 0;
}
.filter-icon.data-v-59785f67::after {
  width: 100%;
  height: 2rpx;
  bottom: 6rpx;
  left: 0;
}
.filter-icon.active-icon.data-v-59785f67::before, .filter-icon.active-icon.data-v-59785f67::after {
  background-color: #1677FF;
}

/* 下拉菜单样式 */
.filter-dropdown.data-v-59785f67 {
  position: absolute;
  background-color: #fff;
  z-index: 101;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  max-height: 600rpx;
  animation: dropDown-59785f67 0.25s cubic-bezier(0.215, 0.61, 0.355, 1);
  border-radius: 0 0 12rpx 12rpx;
  overflow: hidden;
}
@keyframes dropDown-59785f67 {
from {
    transform: translateY(-8rpx);
    opacity: 0;
}
to {
    transform: translateY(0);
    opacity: 1;
}
}
.dropdown-scroll.data-v-59785f67 {
  max-height: 600rpx;
}
.dropdown-item.data-v-59785f67 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.dropdown-item.active-item.data-v-59785f67 {
  color: #1677FF;
}
.dropdown-item-text.data-v-59785f67 {
  font-size: 28rpx;
}
.dropdown-item-check.data-v-59785f67 {
  color: #1677FF;
  font-weight: bold;
}
.dropdown-title.data-v-59785f67 {
  font-size: 28rpx;
  font-weight: 500;
}

/* 遮罩层 */
.filter-mask.data-v-59785f67 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 100;
}

/* iOS风格服务列表 */
.service-scroll.data-v-59785f67 {
  flex: 1;
  overflow: hidden;
  padding: 0;
  margin-bottom: 16px;
}
.service-list.data-v-59785f67 {
  padding: 8px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.service-item.data-v-59785f67 {
  background-color: #fff;
  border-radius: 12px;
  margin-bottom: 12px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease;
  width: 94%;
}
.service-item.data-v-59785f67:active {
  transform: scale(0.98);
}
.service-content.data-v-59785f67 {
  padding: 16px;
}
.service-header.data-v-59785f67 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.service-tag-container.data-v-59785f67 {
  position: relative;
}
.service-tag.data-v-59785f67 {
  background-color: rgba(0, 122, 255, 0.1);
  color: #007aff;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}
.service-time.data-v-59785f67 {
  font-size: 12px;
  color: #8e8e93;
}
.service-title.data-v-59785f67 {
  font-size: 16px;
  line-height: 1.4;
  color: #000;
  margin-bottom: 12px;
  font-weight: 500;
  /* 标题最多显示两行，超出部分显示省略号 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.service-images.data-v-59785f67 {
  display: flex;
  margin-bottom: 12px;
  gap: 8px;
  justify-content: center;
}
.service-image.data-v-59785f67 {
  width: 102px;
  height: 102px;
  border-radius: 6px;
  object-fit: cover;
}

/* iOS风格信息卡片 */
.service-info-card.data-v-59785f67 {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 10px 12px;
  margin-bottom: 10px;
}
.info-row.data-v-59785f67 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.info-item.data-v-59785f67 {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 6px;
}
.info-label.data-v-59785f67 {
  font-size: 12px;
  color: #8e8e93;
}
.info-value.data-v-59785f67 {
  font-size: 14px;
  color: #1c1c1e;
  font-weight: 500;
}
.info-value.highlight.data-v-59785f67 {
  color: #ff3b30;
  font-weight: 600;
}

/* iOS风格底部栏 */
.service-footer.data-v-59785f67 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 10px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  margin-top: 6px;
}
.service-meta.data-v-59785f67 {
  display: flex;
  align-items: center;
}
.views-icon.data-v-59785f67 {
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%238e8e93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z'%3E%3C/path%3E%3Ccircle cx='12' cy='12' r='3'%3E%3C/circle%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  margin-right: 4px;
}
.service-views.data-v-59785f67 {
  font-size: 13px;
  color: #8e8e93;
}
.service-actions.data-v-59785f67 {
  display: flex;
}
.action-btn.data-v-59785f67 {
  display: flex;
  align-items: center;
  background: #007aff;
  padding: 6px 12px;
  border-radius: 16px;
  transition: background-color 0.2s ease;
}
.action-btn.data-v-59785f67:active {
  background: #0062cc;
}
.action-text.data-v-59785f67 {
  font-size: 13px;
  color: #fff;
  font-weight: 500;
}
.contact-icon.data-v-59785f67 {
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23fff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  margin-right: 4px;
}

/* iOS风格空状态 */
.empty-state.data-v-59785f67 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  margin-top: 20px;
  width: 100%;
}
.empty-image.data-v-59785f67 {
  width: 120px;
  height: 120px;
  margin-bottom: 20px;
  opacity: 0.8;
}
.empty-text.data-v-59785f67 {
  font-size: 16px;
  color: #8e8e93;
  margin-bottom: 8px;
}
.empty-tips.data-v-59785f67 {
  font-size: 14px;
  color: #aeaeb2;
}

/* iOS风格发布按钮 */
.publish-btn.data-v-59785f67 {
  position: fixed;
  right: 16px;
  bottom: 50px;
  background: #007aff;
  width: 120px;
  height: 42px;
  border-radius: 21px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
  z-index: 100;
}
.publish-btn.data-v-59785f67:active {
  background: #0062cc;
}
.publish-icon.data-v-59785f67 {
  font-size: 20px;
  color: #fff;
  margin-right: 4px;
  font-weight: 400;
  line-height: 20px;
}
.publish-text.data-v-59785f67 {
  color: #fff;
  font-size: 15px;
  font-weight: 500;
}

/* iOS风格加载更多 */
.loading-more.data-v-59785f67 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  width: 100%;
}
.loading-indicator.data-v-59785f67 {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(0, 122, 255, 0.2);
  border-top-color: #007aff;
  border-radius: 50%;
  animation: spin-59785f67 0.8s linear infinite;
  margin-right: 8px;
}
@keyframes spin-59785f67 {
to {
    transform: rotate(360deg);
}
}
.loading-text.data-v-59785f67 {
  font-size: 14px;
  color: #8e8e93;
}

/* iOS风格整体样式适配 */
.ios-style.data-v-59785f67 {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", sans-serif;
  letter-spacing: -0.2px;
}
.ios-style .service-item.data-v-59785f67 {
  border: none;
}
@media (hover: hover) {
.tab-item.data-v-59785f67:hover {
    background-color: rgba(0, 122, 255, 0.05);
}
.service-item.data-v-59785f67:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}
.action-btn.data-v-59785f67:hover {
    background: #0062cc;
}
}
.area-dropdown.data-v-59785f67 {
  left: 0;
  width: 50%;
}
.sort-dropdown.data-v-59785f67 {
  right: 0;
  width: 50%;
}