"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      privileges: [],
      // 会员特权列表
      privilegeForm: {
        id: "",
        name: "",
        color: "#FF6B22",
        // 默认橙色
        icon: "",
        type: "",
        value: "",
        applicableLevels: [],
        description: ""
      },
      isEditing: false,
      // 是否为编辑模式
      currentPrivilegeId: null,
      // 当前编辑的特权ID
      colorOptions: [
        "#FF6B22",
        // 橙色
        "#8A2BE2",
        // 紫色
        "#1E90FF",
        // 道奇蓝
        "#32CD32",
        // 酸橙绿
        "#FFD700",
        // 金色
        "#FF69B4",
        // 热粉红
        "#20B2AA",
        // 浅海绿
        "#FF8C00"
        // 深橙色
      ],
      privilegeTypes: [
        "折扣优惠",
        "积分加速",
        "专属礼品",
        "免费服务",
        "专属活动",
        "专属客服",
        "其他特权"
      ],
      memberLevels: []
      // 会员等级列表
    };
  },
  onLoad() {
    this.fetchPrivileges();
    this.fetchMemberLevels();
  },
  methods: {
    // 页面导航
    navigateTo(url) {
      common_vendor.index.navigateTo({
        url
      });
    },
    // 获取会员特权列表
    fetchPrivileges() {
      this.privileges = [
        {
          id: "1",
          name: "生日礼包",
          color: "#FF69B4",
          icon: "/static/images/privilege-birthday.svg",
          type: "专属礼品",
          value: "价值100元礼品",
          applicableLevels: ["金卡会员", "钻石会员"],
          description: "会员生日当月可领取专属生日礼包一份"
        },
        {
          id: "2",
          name: "专属折扣",
          color: "#FF6B22",
          icon: "/static/images/privilege-discount.svg",
          type: "折扣优惠",
          value: "9折起",
          applicableLevels: ["银卡会员", "金卡会员", "钻石会员"],
          description: "会员专享商品折扣，最低享受9折优惠"
        },
        {
          id: "3",
          name: "积分翻倍",
          color: "#1E90FF",
          icon: "/static/images/privilege-points.svg",
          type: "积分加速",
          value: "最高2倍积分",
          applicableLevels: ["金卡会员", "钻石会员"],
          description: "消费即可获得最高2倍积分奖励"
        }
      ];
    },
    // 获取会员等级列表
    fetchMemberLevels() {
      this.memberLevels = [
        {
          id: "1",
          name: "普通会员"
        },
        {
          id: "2",
          name: "银卡会员"
        },
        {
          id: "3",
          name: "金卡会员"
        },
        {
          id: "4",
          name: "钻石会员"
        }
      ];
    },
    // 显示添加特权弹窗
    showAddPrivilegeModal() {
      this.isEditing = false;
      this.resetPrivilegeForm();
      this.$refs.privilegeFormPopup.open();
    },
    // 编辑特权
    editPrivilege(privilege) {
      this.isEditing = true;
      this.currentPrivilegeId = privilege.id;
      this.privilegeForm = { ...privilege };
      this.$refs.privilegeFormPopup.open();
    },
    // 关闭特权表单弹窗
    closePrivilegeModal() {
      this.$refs.privilegeFormPopup.close();
      this.resetPrivilegeForm();
    },
    // 重置表单
    resetPrivilegeForm() {
      this.privilegeForm = {
        id: "",
        name: "",
        color: "#FF6B22",
        icon: "",
        type: "",
        value: "",
        applicableLevels: [],
        description: ""
      };
      this.currentPrivilegeId = null;
    },
    // 选择图标
    chooseIcon() {
      common_vendor.index.chooseImage({
        count: 1,
        success: (res) => {
          this.privilegeForm.icon = res.tempFilePaths[0];
        }
      });
    },
    // 特权类型选择变化
    onTypeChange(e) {
      const index = e.detail.value;
      this.privilegeForm.type = this.privilegeTypes[index];
    },
    // 检查等级是否已选中
    isLevelSelected(levelName) {
      return this.privilegeForm.applicableLevels.includes(levelName);
    },
    // 切换等级选择状态
    toggleLevelSelection(levelName) {
      const index = this.privilegeForm.applicableLevels.indexOf(levelName);
      if (index === -1) {
        this.privilegeForm.applicableLevels.push(levelName);
      } else {
        this.privilegeForm.applicableLevels.splice(index, 1);
      }
    },
    // 保存特权表单
    savePrivilegeForm() {
      if (!this.privilegeForm.name) {
        common_vendor.index.showToast({
          title: "请输入特权名称",
          icon: "none"
        });
        return;
      }
      if (!this.privilegeForm.type) {
        common_vendor.index.showToast({
          title: "请选择特权类型",
          icon: "none"
        });
        return;
      }
      if (!this.privilegeForm.value) {
        common_vendor.index.showToast({
          title: "请输入特权价值",
          icon: "none"
        });
        return;
      }
      if (this.privilegeForm.applicableLevels.length === 0) {
        common_vendor.index.showToast({
          title: "请选择适用等级",
          icon: "none"
        });
        return;
      }
      if (this.isEditing) {
        const index = this.privileges.findIndex((item) => item.id === this.currentPrivilegeId);
        if (index !== -1) {
          this.privileges[index] = { ...this.privilegeForm, id: this.currentPrivilegeId };
        }
      } else {
        const newPrivilege = {
          ...this.privilegeForm,
          id: Date.now().toString()
          // 生成临时ID，实际项目中应由后端生成
        };
        this.privileges.push(newPrivilege);
      }
      this.$refs.privilegeFormPopup.close();
      this.resetPrivilegeForm();
      common_vendor.index.showToast({
        title: this.isEditing ? "编辑成功" : "添加成功",
        icon: "success"
      });
    },
    // 确认删除特权
    confirmDeletePrivilege(privilege) {
      this.currentPrivilegeId = privilege.id;
      this.$refs.deleteConfirmPopup.open();
    },
    // 关闭删除确认弹窗
    closeDeleteConfirm() {
      this.$refs.deleteConfirmPopup.close();
    },
    // 删除特权
    deletePrivilege() {
      const index = this.privileges.findIndex((item) => item.id === this.currentPrivilegeId);
      if (index !== -1) {
        this.privileges.splice(index, 1);
        common_vendor.index.showToast({
          title: "删除成功",
          icon: "success"
        });
      }
      this.currentPrivilegeId = null;
    }
  }
};
if (!Array) {
  const _component_uni_popup = common_vendor.resolveComponent("uni-popup");
  const _component_uni_popup_dialog = common_vendor.resolveComponent("uni-popup-dialog");
  (_component_uni_popup + _component_uni_popup_dialog)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.showAddPrivilegeModal && $options.showAddPrivilegeModal(...args)),
    b: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-admin-marketing/pages/marketing/member/member-discount")),
    c: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-admin-marketing/pages/marketing/member/points-acceleration")),
    d: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-admin-marketing/pages/marketing/member/free-shipping")),
    e: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-admin-marketing/pages/marketing/member/birthday-gift")),
    f: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-admin-marketing/pages/marketing/member/vip-service")),
    g: $data.privileges.length === 0
  }, $data.privileges.length === 0 ? {
    h: common_assets._imports_0$46
  } : {
    i: common_vendor.f($data.privileges, (privilege, index, i0) => {
      return {
        a: common_vendor.t(privilege.name),
        b: common_vendor.o(($event) => $options.editPrivilege(privilege), index),
        c: common_vendor.o(($event) => $options.confirmDeletePrivilege(privilege), index),
        d: privilege.color,
        e: privilege.icon,
        f: common_vendor.f(privilege.applicableLevels, (level, idx, i1) => {
          return {
            a: common_vendor.t(level),
            b: idx
          };
        }),
        g: common_vendor.t(privilege.type),
        h: common_vendor.t(privilege.value),
        i: common_vendor.t(privilege.description || "暂无特权说明"),
        j: index
      };
    })
  }, {
    j: common_vendor.t($data.isEditing ? "编辑特权" : "添加特权"),
    k: common_vendor.o((...args) => $options.closePrivilegeModal && $options.closePrivilegeModal(...args)),
    l: $data.privilegeForm.name,
    m: common_vendor.o(($event) => $data.privilegeForm.name = $event.detail.value),
    n: common_vendor.f($data.colorOptions, (color, idx, i0) => {
      return {
        a: idx,
        b: $data.privilegeForm.color === color ? 1 : "",
        c: color,
        d: common_vendor.o(($event) => $data.privilegeForm.color = color, idx)
      };
    }),
    o: $data.privilegeForm.icon
  }, $data.privilegeForm.icon ? {
    p: $data.privilegeForm.icon
  } : {
    q: common_vendor.o((...args) => $options.chooseIcon && $options.chooseIcon(...args))
  }, {
    r: common_vendor.t($data.privilegeForm.type || "请选择特权类型"),
    s: $data.privilegeTypes,
    t: common_vendor.o((...args) => $options.onTypeChange && $options.onTypeChange(...args)),
    v: $data.privilegeForm.value,
    w: common_vendor.o(($event) => $data.privilegeForm.value = $event.detail.value),
    x: common_vendor.f($data.memberLevels, (level, idx, i0) => {
      return {
        a: common_vendor.t($options.isLevelSelected(level.name) ? "✓" : ""),
        b: common_vendor.t(level.name),
        c: idx,
        d: $options.isLevelSelected(level.name) ? 1 : "",
        e: common_vendor.o(($event) => $options.toggleLevelSelection(level.name), idx)
      };
    }),
    y: $data.privilegeForm.description,
    z: common_vendor.o(($event) => $data.privilegeForm.description = $event.detail.value),
    A: common_vendor.o((...args) => $options.closePrivilegeModal && $options.closePrivilegeModal(...args)),
    B: common_vendor.o((...args) => $options.savePrivilegeForm && $options.savePrivilegeForm(...args)),
    C: common_vendor.sr("privilegeFormPopup", "99eee3fc-0"),
    D: common_vendor.p({
      type: "center"
    }),
    E: common_vendor.o($options.deletePrivilege),
    F: common_vendor.o($options.closeDeleteConfirm),
    G: common_vendor.p({
      type: "warning",
      title: "删除确认",
      content: "确定要删除该会员特权吗？删除后将无法恢复。",
      ["before-close"]: true
    }),
    H: common_vendor.sr("deleteConfirmPopup", "99eee3fc-1"),
    I: common_vendor.p({
      type: "dialog"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-99eee3fc"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/member/member-privilege.js.map
