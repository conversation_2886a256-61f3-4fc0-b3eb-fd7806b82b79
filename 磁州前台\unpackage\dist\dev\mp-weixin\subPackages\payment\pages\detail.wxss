
.wallet-detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 30rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
}
.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}
.back-icon {
  width: 40rpx;
  height: 40rpx;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}
.navbar-right {
  width: 80rpx;
}

/* 余额卡片 */
.balance-card {
  background: linear-gradient(to right, #0052CC, #0066FF);
  margin: 0 30rpx;
  padding: 30rpx;
  border-radius: 20rpx;
  color: #fff;
  overflow: hidden;
  box-shadow: 0 8rpx 20rpx rgba(0, 82, 204, 0.2);
}
.balance-title {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 20rpx;
}
.balance-amount {
  font-size: 60rpx;
  font-weight: bold;
  margin-bottom: 40rpx;
}
.balance-info {
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 20rpx;
  border-radius: 10rpx;
}
.info-item {
  text-align: center;
}
.info-value {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}
.info-label {
  font-size: 24rpx;
  opacity: 0.8;
}
.info-divider {
  width: 1px;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.3);
}

/* 筛选标签 */
.filter-tabs {
  display: flex;
  background-color: #fff;
  margin: 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}
.filter-tab {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}
.filter-tab.active {
  color: #0052CC;
  font-weight: 500;
}
.filter-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #0052CC;
  border-radius: 2rpx;
}

/* 交易记录列表 */
.transaction-list {
  margin: 0 30rpx;
}
.month-header {
  font-size: 24rpx;
  color: #999;
  padding: 20rpx 0;
}
.transaction-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}
.transaction-left {
  margin-right: 20rpx;
}
.transaction-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.income-icon {
  background-color: rgba(7, 193, 96, 0.1);
}
.expense-icon {
  background-color: rgba(245, 108, 108, 0.1);
}
.type-icon {
  width: 40rpx;
  height: 40rpx;
}
.transaction-center {
  flex: 1;
}
.transaction-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.transaction-time {
  font-size: 24rpx;
  color: #999;
}
.transaction-right {
  text-align: right;
}
.transaction-amount {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}
.income {
  color: #07c160;
}
.expense {
  color: #f56c6c;
}
.transaction-status {
  font-size: 24rpx;
  color: #999;
}

/* 加载更多 */
.load-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 24rpx;
  color: #999;
}
.load-more {
  color: #0052CC;
}

/* 空状态 */
.empty-view {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}
