<template>
  <view class="products-container">
    <!-- 搜索栏 -->
    <view class="search-bar" :style="{
      padding: '20rpx 30rpx',
      background: '#FFFFFF',
      position: 'sticky',
      top: 0,
      zIndex: 100,
      display: 'flex',
      alignItems: 'center',
      boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
    }">
      <view class="search-input-wrapper" :style="{
        flex: 1,
        height: '70rpx',
        borderRadius: '35rpx',
        background: '#F2F2F7',
        display: 'flex',
        alignItems: 'center',
        paddingLeft: '20rpx'
      }">
        <svg class="icon" viewBox="0 0 24 24" width="20" height="20">
          <circle cx="11" cy="11" r="8" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
          <path d="M21 21l-4.35-4.35" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>
        <input 
          type="text" 
          v-model="searchKeyword" 
          placeholder="搜索商品" 
          :style="{
            flex: 1,
            height: '100%',
            border: 'none',
            background: 'transparent',
            marginLeft: '10rpx',
            fontSize: '28rpx'
          }"
          @confirm="searchProducts"
        />
      </view>
      
      <view class="filter-btn" @click="toggleFilterPanel" :style="{
        display: 'flex',
        alignItems: 'center',
        marginLeft: '20rpx',
        padding: '0 10rpx'
      }">
        <text :style="{
          fontSize: '28rpx',
          color: '#666666',
          marginRight: '5rpx'
        }">筛选</text>
        <svg class="icon" viewBox="0 0 24 24" width="20" height="20">
          <path d="M22 3H2l8 9.46V19l4 2v-8.54L22 3z" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>
      </view>
    </view>
    
    <!-- 分类标签栏 -->
    <scroll-view 
      scroll-x 
      class="category-tabs" 
      :style="{
        whiteSpace: 'nowrap',
        padding: '20rpx 30rpx',
        background: '#FFFFFF',
        marginBottom: '20rpx',
        boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
      }"
    >
      <view 
        v-for="(category, index) in categories" 
        :key="index"
        class="category-item"
        :class="{ active: currentCategory === index }"
        @click="switchCategory(index)"
        :style="{
          display: 'inline-block',
          padding: '10rpx 30rpx',
          marginRight: '20rpx',
          borderRadius: '30rpx',
          fontSize: '28rpx',
          color: currentCategory === index ? '#FFFFFF' : '#666666',
          background: currentCategory === index ? 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)' : '#F2F2F7',
          boxShadow: currentCategory === index ? '0 4px 10px rgba(172,57,255,0.2)' : 'none'
        }"
      >
        {{ category.name }}
      </view>
    </scroll-view>
    
    <!-- 排序栏 -->
    <view class="sort-bar" :style="{
      display: 'flex',
      padding: '20rpx 30rpx',
      background: '#FFFFFF',
      marginBottom: '20rpx',
      boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
    }">
      <view 
        v-for="(option, index) in sortOptions" 
        :key="index"
        class="sort-option"
        :class="{ active: currentSort === index }"
        @click="switchSort(index)"
        :style="{
          flex: 1,
          textAlign: 'center',
          fontSize: '26rpx',
          color: currentSort === index ? '#AC39FF' : '#666666',
          fontWeight: currentSort === index ? '500' : '400',
          position: 'relative',
          paddingBottom: '10rpx'
        }"
      >
        {{ option.name }}
        <view v-if="currentSort === index" class="sort-indicator" :style="{
          position: 'absolute',
          bottom: 0,
          left: '50%',
          transform: 'translateX(-50%)',
          width: '40rpx',
          height: '4rpx',
          background: 'linear-gradient(90deg, #AC39FF 0%, #B87AFF 100%)',
          borderRadius: '2rpx'
        }"></view>
      </view>
    </view>
    
    <!-- 商品列表 -->
    <view class="products-list" :style="{
      padding: '0 20rpx'
    }">
      <view class="products-grid" :style="{
        display: 'grid',
        gridTemplateColumns: 'repeat(2, 1fr)',
        gap: '20rpx'
      }">
        <view 
          v-for="(product, index) in filteredProducts" 
          :key="index"
          class="product-item"
          @click="viewProductDetail(product)"
          :style="{
            background: '#FFFFFF',
            borderRadius: '20rpx',
            overflow: 'hidden',
            boxShadow: '0 4px 10px rgba(0,0,0,0.05)'
          }"
        >
          <view class="product-image-wrapper" :style="{
            position: 'relative',
            paddingTop: '100%'
          }">
            <image 
              :src="product.image" 
              mode="aspectFill" 
              :style="{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%'
              }"
            ></image>
            
            <view v-if="product.tag" class="product-tag" :style="{
              position: 'absolute',
              top: '10rpx',
              left: '10rpx',
              padding: '5rpx 15rpx',
              borderRadius: '20rpx',
              background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',
              color: '#FFFFFF',
              fontSize: '22rpx',
              fontWeight: '500'
            }">
              {{ product.tag }}
            </view>
          </view>
          
          <view class="product-info" :style="{
            padding: '20rpx'
          }">
            <text class="product-name" :style="{
              fontSize: '28rpx',
              fontWeight: '500',
              color: '#333333',
              marginBottom: '10rpx',
              display: 'block',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: '-webkit-box',
              '-webkit-line-clamp': '2',
              '-webkit-box-orient': 'vertical',
              lineHeight: '1.4'
            }">{{ product.name }}</text>
            
            <view class="product-price-row" :style="{
              display: 'flex',
              alignItems: 'baseline',
              marginBottom: '10rpx'
            }">
              <text :style="{
                fontSize: '24rpx',
                color: '#FF3B69',
                marginRight: '5rpx'
              }">¥</text>
              <text :style="{
                fontSize: '32rpx',
                fontWeight: '600',
                color: '#FF3B69',
                marginRight: '10rpx'
              }">{{ product.price }}</text>
              <text :style="{
                fontSize: '24rpx',
                color: '#999999',
                textDecoration: 'line-through'
              }">¥{{ product.originalPrice }}</text>
            </view>
            
            <view class="product-commission-row" :style="{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }">
              <view class="commission-info" :style="{
                display: 'flex',
                alignItems: 'center'
              }">
                <text :style="{
                  fontSize: '24rpx',
                  color: '#AC39FF',
                  marginRight: '5rpx'
                }">佣金:</text>
                <text :style="{
                  fontSize: '24rpx',
                  fontWeight: '600',
                  color: '#AC39FF'
                }">¥{{ product.commission }}</text>
              </view>
              
              <text :style="{
                fontSize: '22rpx',
                color: '#999999'
              }">已售{{ product.soldCount }}</text>
            </view>
          </view>
          
          <view class="promote-btn" @click.stop="createPoster(product)" :style="{
            position: 'absolute',
            bottom: '20rpx',
            right: '20rpx',
            padding: '8rpx 20rpx',
            borderRadius: '30rpx',
            background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)',
            color: '#FFFFFF',
            fontSize: '24rpx',
            fontWeight: '500',
            boxShadow: '0 4px 10px rgba(172,57,255,0.2)'
          }">
            推广
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view v-if="filteredProducts.length === 0" class="empty-state" :style="{
        padding: '100rpx 0',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center'
      }">
        <image src="/static/images/empty/empty-products.png" mode="aspectFit" :style="{
          width: '200rpx',
          height: '200rpx',
          marginBottom: '20rpx'
        }"></image>
        <text :style="{
          fontSize: '28rpx',
          color: '#999999'
        }">暂无相关商品</text>
      </view>
      
      <!-- 加载更多 -->
      <view v-if="filteredProducts.length > 0" class="load-more" :style="{
        textAlign: 'center',
        padding: '30rpx 0'
      }">
        <text v-if="loading" :style="{
          fontSize: '26rpx',
          color: '#999999'
        }">加载中...</text>
        <text v-else-if="noMore" :style="{
          fontSize: '26rpx',
          color: '#999999'
        }">没有更多商品了</text>
        <text v-else @click="loadMore" :style="{
          fontSize: '26rpx',
          color: '#AC39FF'
        }">加载更多</text>
      </view>
    </view>
    
    <!-- 筛选面板 -->
    <view v-if="showFilterPanel" class="filter-panel-mask" @click="toggleFilterPanel" :style="{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0,0,0,0.5)',
      zIndex: 999
    }"></view>
    
    <view v-if="showFilterPanel" class="filter-panel" :style="{
      position: 'fixed',
      top: 0,
      right: 0,
      width: '70%',
      height: '100%',
      background: '#FFFFFF',
      zIndex: 1000,
      boxShadow: '-5px 0 15px rgba(0,0,0,0.1)',
      display: 'flex',
      flexDirection: 'column'
    }">
      <view class="filter-header" :style="{
        padding: '30rpx',
        borderBottom: '1rpx solid #EFEFEF',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }">
        <text :style="{
          fontSize: '32rpx',
          fontWeight: '600',
          color: '#333333'
        }">筛选条件</text>
        
        <view class="close-btn" @click="toggleFilterPanel" :style="{
          width: '60rpx',
          height: '60rpx',
          borderRadius: '50%',
          background: '#F2F2F7',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }">
          <svg class="icon" viewBox="0 0 24 24" width="20" height="20">
            <path d="M18 6L6 18M6 6l12 12" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
      </view>
      
      <scroll-view scroll-y class="filter-content" :style="{
        flex: 1,
        padding: '30rpx'
      }">
        <!-- 价格区间 -->
        <view class="filter-section" :style="{
          marginBottom: '30rpx'
        }">
          <text class="section-title" :style="{
            fontSize: '28rpx',
            fontWeight: '500',
            color: '#333333',
            marginBottom: '20rpx',
            display: 'block'
          }">价格区间</text>
          
          <view class="price-range" :style="{
            display: 'flex',
            alignItems: 'center'
          }">
            <input 
              type="digit" 
              v-model="minPrice" 
              placeholder="最低价" 
              :style="{
                width: '150rpx',
                height: '70rpx',
                background: '#F2F2F7',
                borderRadius: '10rpx',
                padding: '0 20rpx',
                fontSize: '26rpx'
              }"
            />
            <text :style="{
              margin: '0 20rpx',
              color: '#999999'
            }">-</text>
            <input 
              type="digit" 
              v-model="maxPrice" 
              placeholder="最高价" 
              :style="{
                width: '150rpx',
                height: '70rpx',
                background: '#F2F2F7',
                borderRadius: '10rpx',
                padding: '0 20rpx',
                fontSize: '26rpx'
              }"
            />
          </view>
        </view>
        
        <!-- 佣金比例 -->
        <view class="filter-section" :style="{
          marginBottom: '30rpx'
        }">
          <text class="section-title" :style="{
            fontSize: '28rpx',
            fontWeight: '500',
            color: '#333333',
            marginBottom: '20rpx',
            display: 'block'
          }">佣金比例</text>
          
          <view class="commission-options" :style="{
            display: 'flex',
            flexWrap: 'wrap'
          }">
            <view 
              v-for="(option, index) in commissionOptions" 
              :key="index"
              class="commission-option"
              :class="{ active: selectedCommission === index }"
              @click="selectCommission(index)"
              :style="{
                padding: '10rpx 30rpx',
                borderRadius: '30rpx',
                background: selectedCommission === index ? 'rgba(172,57,255,0.1)' : '#F2F2F7',
                border: selectedCommission === index ? '1rpx solid #AC39FF' : '1rpx solid transparent',
                color: selectedCommission === index ? '#AC39FF' : '#666666',
                fontSize: '26rpx',
                marginRight: '20rpx',
                marginBottom: '20rpx'
              }"
            >
              {{ option }}
            </view>
          </view>
        </view>
        
        <!-- 商品标签 -->
        <view class="filter-section" :style="{
          marginBottom: '30rpx'
        }">
          <text class="section-title" :style="{
            fontSize: '28rpx',
            fontWeight: '500',
            color: '#333333',
            marginBottom: '20rpx',
            display: 'block'
          }">商品标签</text>
          
          <view class="tag-options" :style="{
            display: 'flex',
            flexWrap: 'wrap'
          }">
            <view 
              v-for="(tag, index) in tagOptions" 
              :key="index"
              class="tag-option"
              :class="{ active: selectedTags.includes(tag) }"
              @click="toggleTag(tag)"
              :style="{
                padding: '10rpx 30rpx',
                borderRadius: '30rpx',
                background: selectedTags.includes(tag) ? 'rgba(172,57,255,0.1)' : '#F2F2F7',
                border: selectedTags.includes(tag) ? '1rpx solid #AC39FF' : '1rpx solid transparent',
                color: selectedTags.includes(tag) ? '#AC39FF' : '#666666',
                fontSize: '26rpx',
                marginRight: '20rpx',
                marginBottom: '20rpx'
              }"
            >
              {{ tag }}
            </view>
          </view>
        </view>
      </scroll-view>
      
      <view class="filter-footer" :style="{
        padding: '30rpx',
        borderTop: '1rpx solid #EFEFEF',
        display: 'flex'
      }">
        <view class="reset-btn" @click="resetFilter" :style="{
          flex: 1,
          height: '80rpx',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: '40rpx',
          border: '1rpx solid #EFEFEF',
          color: '#666666',
          fontSize: '28rpx',
          marginRight: '20rpx'
        }">
          重置
        </view>
        
        <view class="apply-btn" @click="applyFilter" :style="{
          flex: 1,
          height: '80rpx',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: '40rpx',
          background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)',
          color: '#FFFFFF',
          fontSize: '28rpx',
          boxShadow: '0 5px 15px rgba(172,57,255,0.3)'
        }">
          确定
        </view>
      </view>
    </view>
    
    <!-- 底部安全区域 -->
    <view class="safe-area-bottom" :style="{
      height: '100rpx',
      paddingBottom: 'env(safe-area-inset-bottom)'
    }"></view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';

// 搜索关键词
const searchKeyword = ref('');

// 分类数据
const categories = ref([
  { id: 0, name: '全部' },
  { id: 1, name: '数码' },
  { id: 2, name: '家电' },
  { id: 3, name: '服装' },
  { id: 4, name: '美妆' },
  { id: 5, name: '食品' },
  { id: 6, name: '家居' },
  { id: 7, name: '母婴' }
]);
const currentCategory = ref(0);

// 排序选项
const sortOptions = ref([
  { name: '综合排序', field: 'default', order: 'desc' },
  { name: '销量优先', field: 'soldCount', order: 'desc' },
  { name: '价格升序', field: 'price', order: 'asc' },
  { name: '价格降序', field: 'price', order: 'desc' },
  { name: '佣金比例', field: 'commissionRate', order: 'desc' }
]);
const currentSort = ref(0);

// 筛选面板
const showFilterPanel = ref(false);
const minPrice = ref('');
const maxPrice = ref('');
const commissionOptions = ref(['5%以上', '10%以上', '15%以上', '20%以上']);
const selectedCommission = ref(-1);
const tagOptions = ref(['热销', '新品', '限时', '包邮', '满减', '秒杀']);
const selectedTags = ref([]);

// 加载状态
const loading = ref(false);
const noMore = ref(false);

// 商品数据
const products = ref([
  {
    id: 1,
    name: 'Apple iPhone 14 Pro Max 256GB 暗夜紫 移动联通电信5G双卡双待手机',
    image: 'https://via.placeholder.com/300',
    price: '8999.00',
    originalPrice: '9999.00',
    commission: '300.00',
    commissionRate: '3%',
    soldCount: 1253,
    category: 1,
    tag: '热销'
  },
  {
    id: 2,
    name: '小米12S Ultra 12GB+256GB 丹青黑 骁龙8+旗舰处理器 徕卡专业光学镜头',
    image: 'https://via.placeholder.com/300',
    price: '5999.00',
    originalPrice: '6999.00',
    commission: '200.00',
    commissionRate: '3.3%',
    soldCount: 985,
    category: 1,
    tag: '新品'
  },
  {
    id: 3,
    name: '华为Mate 50 Pro 8GB+256GB 曜金黑 超光变XMAGE影像 北斗卫星消息',
    image: 'https://via.placeholder.com/300',
    price: '6799.00',
    originalPrice: '7299.00',
    commission: '250.00',
    commissionRate: '3.7%',
    soldCount: 756,
    category: 1,
    tag: '限时'
  },
  {
    id: 4,
    name: '戴森(Dyson) 吸尘器 V12 Detect Slim 手持无线吸尘器',
    image: 'https://via.placeholder.com/300',
    price: '4290.00',
    originalPrice: '4990.00',
    commission: '215.00',
    commissionRate: '5%',
    soldCount: 432,
    category: 2,
    tag: '热销'
  },
  {
    id: 5,
    name: '美的(Midea) 新风空调 1.5匹 壁挂式 智能家电 KFR-35GW/N8XHA1',
    image: 'https://via.placeholder.com/300',
    price: '3299.00',
    originalPrice: '3899.00',
    commission: '165.00',
    commissionRate: '5%',
    soldCount: 321,
    category: 2,
    tag: '满减'
  },
  {
    id: 6,
    name: 'NIKE 耐克 AIR FORCE 1 男子运动鞋 休闲板鞋',
    image: 'https://via.placeholder.com/300',
    price: '799.00',
    originalPrice: '899.00',
    commission: '80.00',
    commissionRate: '10%',
    soldCount: 1876,
    category: 3,
    tag: '包邮'
  }
]);

// 过滤后的商品列表
const filteredProducts = computed(() => {
  let result = [...products.value];
  
  // 根据分类筛选
  if (currentCategory.value !== 0) {
    result = result.filter(item => item.category === categories.value[currentCategory.value].id);
  }
  
  // 根据搜索关键词筛选
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    result = result.filter(item => item.name.toLowerCase().includes(keyword));
  }
  
  // 根据排序选项排序
  const { field, order } = sortOptions.value[currentSort.value];
  
  if (field !== 'default') {
    result.sort((a, b) => {
      let comparison = 0;
      
      if (field === 'price' || field === 'commissionRate' || field === 'soldCount') {
        // 数值型字段，需要转换为数字
        const aValue = field === 'commissionRate' 
          ? parseFloat(a[field].replace('%', '')) 
          : parseFloat(a[field]);
        const bValue = field === 'commissionRate' 
          ? parseFloat(b[field].replace('%', '')) 
          : parseFloat(b[field]);
        
        comparison = aValue - bValue;
      } else {
        // 字符串型字段
        comparison = a[field].localeCompare(b[field]);
      }
      
      return order === 'asc' ? comparison : -comparison;
    });
  }
  
  return result;
});

// 搜索商品
function searchProducts() {
  // 这里可以添加搜索逻辑
  uni.showToast({
    title: '搜索: ' + searchKeyword.value,
    icon: 'none'
  });
}

// 切换分类
function switchCategory(index) {
  currentCategory.value = index;
}

// 切换排序方式
function switchSort(index) {
  currentSort.value = index;
}

// 切换筛选面板
function toggleFilterPanel() {
  showFilterPanel.value = !showFilterPanel.value;
}

// 选择佣金比例
function selectCommission(index) {
  selectedCommission.value = selectedCommission.value === index ? -1 : index;
}

// 切换标签选择
function toggleTag(tag) {
  const index = selectedTags.value.indexOf(tag);
  if (index === -1) {
    selectedTags.value.push(tag);
  } else {
    selectedTags.value.splice(index, 1);
  }
}

// 重置筛选条件
function resetFilter() {
  minPrice.value = '';
  maxPrice.value = '';
  selectedCommission.value = -1;
  selectedTags.value = [];
}

// 应用筛选条件
function applyFilter() {
  // 这里可以添加应用筛选的逻辑
  showFilterPanel.value = false;
  
  uni.showToast({
    title: '筛选条件已应用',
    icon: 'success'
  });
}

// 查看商品详情
function viewProductDetail(product) {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/detail/index?id=${product.id}&source=distribution`
  });
}

// 创建推广海报
function createPoster(product) {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/distribution/poster/index?id=${product.id}`
  });
}

// 加载更多
function loadMore() {
  if (loading.value || noMore.value) return;
  
  loading.value = true;
  
  // 模拟加载更多数据
  setTimeout(() => {
    // 这里应该调用API获取更多数据
    // 模拟没有更多数据
    noMore.value = true;
    loading.value = false;
  }, 1500);
}
</script>

<style scoped>
.products-container {
  background-color: #F8F8F8;
  min-height: 100vh;
}

.category-item:active, .promote-btn:active, .reset-btn:active, .apply-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
}

.product-item {
  position: relative;
  transition: transform 0.3s ease;
}

.product-item:active {
  transform: scale(0.98);
}
</style> 