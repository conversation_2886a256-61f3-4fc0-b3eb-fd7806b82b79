/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 页面容器 */
.flash-edit-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
  position: relative;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  color: white;
  padding: 48px 20px 16px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(255, 120, 0, 0.15);
}
.navbar-left {
  width: 40px;
}
.back-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.navbar-title {
  flex: 1;
  text-align: center;
}
.title-text {
  font-size: 18px;
  font-weight: 600;
}
.navbar-right {
  min-width: 40px;
}
.save-button {
  font-size: 16px;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16px;
}

/* 内容区域 */
.content-area {
  flex: 1;
  box-sizing: border-box;
  height: calc(100vh - 80px - 60px);
}

/* 表单区域 */
.form-section {
  padding: 12px;
}
.form-group {
  background: #FFFFFF;
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
}
.form-header {
  padding: 12px 16px;
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  border-bottom: 1px solid #F5F5F5;
}
.form-item {
  padding: 12px 16px;
  border-bottom: 1px solid #F5F5F5;
  position: relative;
}
.form-item:last-child {
  border-bottom: none;
}
.form-label {
  display: block;
  font-size: 14px;
  color: #333333;
  margin-bottom: 8px;
}
.required:after {
  content: "*";
  color: #FF3B30;
  margin-left: 4px;
}
.form-input {
  width: 100%;
  height: 40px;
  background: #F9F9F9;
  border: 1px solid #EEEEEE;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
  color: #333333;
}
.form-textarea {
  width: 100%;
  height: 100px;
  background: #F9F9F9;
  border: 1px solid #EEEEEE;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  color: #333333;
}
.form-counter {
  position: absolute;
  right: 16px;
  bottom: 12px;
  font-size: 12px;
  color: #999999;
}
.form-tip {
  font-size: 12px;
  color: #999999;
  margin-top: 8px;
  display: block;
}
.form-error {
  font-size: 12px;
  color: #FF3B30;
  margin-top: 8px;
  display: block;
}
.discount-value {
  height: 40px;
  line-height: 40px;
  font-size: 16px;
  color: #FF3B30;
  font-weight: 600;
}
.picker-value {
  width: 100%;
  height: 40px;
  background: #F9F9F9;
  border: 1px solid #EEEEEE;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
  color: #333333;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.arrow-icon {
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #999999;
}
.switch-label {
  font-size: 14px;
  color: #666666;
  margin-left: 8px;
}

/* 图片上传 */
.image-uploader {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -4px;
}
.image-item {
  width: calc(25% - 8px);
  margin: 4px;
  position: relative;
  aspect-ratio: 1;
}
.detail-image-item {
  width: calc(33.33% - 8px);
}
.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}
.delete-icon {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  z-index: 10;
}
.upload-button {
  width: calc(25% - 8px);
  margin: 4px;
  aspect-ratio: 1;
  background: #F9F9F9;
  border: 1px dashed #DDDDDD;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999999;
}
.detail-upload {
  width: calc(33.33% - 8px);
}

/* 底部空间 */
.bottom-space {
  height: 80px;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #FFFFFF;
  padding: 10px 16px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 90;
  display: flex;
}
.action-button {
  flex: 1;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 6px;
}
.action-button.preview {
  background: #F5F5F5;
  color: #666666;
}
.action-button.save {
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  color: white;
}
.button-text {
  font-size: 16px;
  font-weight: 500;
}
@media screen and (min-width: 768px) {
.form-section {
    max-width: 600px;
    margin: 0 auto;
}
.bottom-bar {
    max-width: 600px;
    left: 50%;
    transform: translateX(-50%);
}
}