"use strict";
const common_vendor = require("../common/vendor.js");
if (!Math) {
  ConfigurablePremiumActions();
}
const ConfigurablePremiumActions = () => "./premium/ConfigurablePremiumActions.js";
const _sfc_main = {
  __name: "FloatPromotionButton",
  props: {
    // 按钮位置
    position: {
      type: Object,
      default: () => ({
        right: "30rpx",
        bottom: "180rpx"
      })
    },
    // 按钮大小
    size: {
      type: String,
      default: "80rpx"
    },
    // 按钮图标
    icon: {
      type: String,
      default: "/static/images/promotion-icon.svg"
    },
    // 页面类型
    pageType: {
      type: String,
      default: "publish"
    },
    // 显示模式
    showMode: {
      type: String,
      default: "direct"
    },
    // 项目数据
    itemData: {
      type: Object,
      default: () => ({
        id: "",
        title: "推广",
        description: "提升曝光度"
      })
    }
  },
  emits: ["action-completed", "action-cancelled"],
  setup(__props, { emit: __emit }) {
    const emit = __emit;
    const showPromotionModal = common_vendor.ref(false);
    const handleActionCompleted = (result) => {
      common_vendor.index.__f__("log", "at components/FloatPromotionButton.vue:91", "推广操作完成:", result);
      showPromotionModal.value = false;
      emit("action-completed", result);
    };
    const handleActionCancelled = (result) => {
      common_vendor.index.__f__("log", "at components/FloatPromotionButton.vue:98", "推广操作取消:", result);
      showPromotionModal.value = false;
      emit("action-cancelled", result);
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: __props.icon,
        b: __props.position.right,
        c: __props.position.bottom,
        d: __props.size,
        e: __props.size,
        f: common_vendor.o(($event) => showPromotionModal.value = true),
        g: showPromotionModal.value
      }, showPromotionModal.value ? {
        h: common_vendor.o(($event) => showPromotionModal.value = false),
        i: common_vendor.o(handleActionCompleted),
        j: common_vendor.o(handleActionCancelled),
        k: common_vendor.p({
          pageType: __props.pageType,
          showMode: __props.showMode,
          itemData: __props.itemData
        }),
        l: common_vendor.o(() => {
        }),
        m: common_vendor.o(($event) => showPromotionModal.value = false)
      } : {});
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-c3c113f2"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/FloatPromotionButton.js.map
