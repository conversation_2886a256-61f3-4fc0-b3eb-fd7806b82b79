<view class="points-rules-container"><view class="navbar"><view class="navbar-left"><view class="back-button" bindtap="{{c}}"><svg wx:if="{{b}}" u-s="{{['d']}}" u-i="1e380f02-0" bind:__l="__l" u-p="{{b}}"><path wx:if="{{a}}" u-i="1e380f02-1,1e380f02-0" bind:__l="__l" u-p="{{a}}"/></svg></view></view><view class="navbar-title"><text class="title-text">积分规则设置</text></view><view class="navbar-right"></view></view><scroll-view scroll-y class="content-area"><view class="rules-card"><view class="card-header"><text class="card-title">积分规则设置</text><view wx:if="{{d}}" class="edit-text" bindtap="{{e}}">保存</view><view wx:else class="edit-text" bindtap="{{f}}">编辑</view></view><view class="rules-list"><view wx:for="{{g}}" wx:for-item="rule" wx:key="g" class="rule-item"><view class="{{['rule-icon', rule.a]}}"><view class="icon-circle"></view></view><view class="rule-content"><view class="rule-title">{{rule.b}}</view><view class="rule-desc">{{rule.c}}</view></view><view wx:if="{{h}}" class="rule-points">+{{rule.d}}</view><view wx:else class="rule-points-edit"><text class="plus-sign">+</text><input type="number" class="points-input" value="{{rule.e}}" bindinput="{{rule.f}}"/></view></view></view></view><view wx:if="{{i}}" class="add-rule-button" bindtap="{{j}}"><text class="add-icon">+</text><text class="add-text">添加新规则</text></view><view class="bottom-space"></view></scroll-view><view wx:if="{{k}}" class="modal"><view class="modal-mask" bindtap="{{l}}"></view><view class="modal-content"><view class="modal-header"><text class="modal-title">添加积分规则</text><view class="modal-close" bindtap="{{m}}">×</view></view><view class="modal-body"><view class="form-item"><text class="form-label">规则类型</text><picker bindchange="{{o}}" value="{{p}}" range="{{q}}" range-key="label"><view class="picker">{{n}}</view></picker></view><view class="form-item"><text class="form-label">规则名称</text><input type="text" class="form-input" placeholder="请输入规则名称" value="{{r}}" bindinput="{{s}}"/></view><view class="form-item"><text class="form-label">规则描述</text><input type="text" class="form-input" placeholder="请输入规则描述" value="{{t}}" bindinput="{{v}}"/></view><view class="form-item"><text class="form-label">积分值</text><input type="number" class="form-input" placeholder="请输入积分值" value="{{w}}" bindinput="{{x}}"/></view></view><view class="modal-footer"><view class="modal-button cancel" bindtap="{{y}}">取消</view><view class="modal-button confirm" bindtap="{{z}}">确定</view></view></view></view></view>