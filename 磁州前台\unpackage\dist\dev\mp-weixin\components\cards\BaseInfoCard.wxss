
.info-item {
  margin-bottom: 20rpx;
  border-radius: 28rpx;
  background-color: #FFFFFF;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  position: relative;
  border: none;
  overflow: hidden;
  padding: 24rpx 20rpx;
}
.topped-item {
  background: linear-gradient(135deg, #FFFFFF, #FFF9F0);
  border-left: 6rpx solid #FF9500;
}
.paid-top {
  background: linear-gradient(135deg, #FFFFFF, #FFF9F0);
  border-left: 6rpx solid #FF9500;
}
.ad-top {
  background: linear-gradient(135deg, #FFFFFF, #F0F7FF);
  border-left: 6rpx solid #007AFF;
}
.top-indicator {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
}
.top-badge {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-bottom-left-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  transform: translateZ(0);
}
.paid-badge {
  background: linear-gradient(135deg, #FF9500, #FF3B30);
  box-shadow: 0 4rpx 12rpx rgba(255, 59, 48, 0.2);
}
.ad-badge {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}
.top-badge-icon {
  color: #FFFFFF;
  margin-right: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.top-badge-text {
  font-size: 20rpx;
  color: #FFFFFF;
  font-weight: 600;
}
.info-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
}
.info-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.info-category-wrapper {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.info-category {
  background: linear-gradient(135deg, #0062CC, #0091E6);
  border-radius: 10rpx;
  padding: 4rpx 12rpx;
  border: 0.5rpx solid rgba(0, 98, 204, 0.2);
  box-shadow: 0 4rpx 8rpx rgba(0, 98, 204, 0.15);
  transform: translateZ(0);
  margin-right: 12rpx;
  display: inline-flex;
  align-items: center;
}
.category-job {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
}
.category-resume {
  background: linear-gradient(135deg, #5856D6, #AF52DE);
}
.category-house-rent {
  background: linear-gradient(135deg, #FF9500, #FF3B30);
}
.category-house-sale {
  background: linear-gradient(135deg, #FF2D55, #FF3B30);
}
.category-secondhand {
  background: linear-gradient(135deg, #34C759, #30B0C7);
}
.category-used-car {
  background: linear-gradient(135deg, #FF9500, #FFCC00);
}
.category-home-service {
  background: linear-gradient(135deg, #5AC8FA, #007AFF);
}
.category-find-service {
  background: linear-gradient(135deg, #AF52DE, #5856D6);
}
.category-business {
  background: linear-gradient(135deg, #FF2D55, #FF9500);
}
.category-pet {
  background: linear-gradient(135deg, #FFCC00, #FF9500);
}
.category-merchant {
  background: linear-gradient(135deg, #FF3B30, #FF2D55);
}
.category-dating {
  background: linear-gradient(135deg, #FF2D55, #AF52DE);
}
.category-car-service {
  background: linear-gradient(135deg, #34C759, #5AC8FA);
}
.category-carpool {
  background: linear-gradient(135deg, #007AFF, #5856D6);
}
.category-education {
  background: linear-gradient(135deg, #5856D6, #007AFF);
}
.category-other {
  background: linear-gradient(135deg, #8E8E93, #636366);
}
.category-text {
  font-size: 20rpx;
  color: #FFFFFF;
  font-weight: 600;
}
.info-time {
  font-size: 22rpx;
  color: #8E8E93;
  font-weight: 400;
  background-color: rgba(142, 142, 147, 0.08);
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
  margin-left: 8rpx;
}
.info-main {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  margin-top: 8rpx;
  margin-bottom: 12rpx;
  width: 100%;
}
.info-content-wrapper {
  flex: 1;
  overflow: hidden;
  min-width: 0;
}
.info-text {
  font-size: 28rpx;
  color: #1C1C1E;
  line-height: 1.4;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.bold-text {
  font-weight: 600;
  color: #000000;
}
.info-images-right {
  width: 160rpx;
  height: 160rpx;
  flex-shrink: 0;
  position: relative;
  margin-left: 10rpx;
  border-radius: 12rpx;
  overflow: hidden;
}
.info-image-container-right {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #f2f2f7;
  position: relative;
}
.info-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 12rpx;
}
.image-count-right {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 18rpx;
  padding: 2rpx 10rpx;
  border-radius: 8rpx;
  z-index: 2;
}
.info-tags {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -4rpx 10rpx;
}
.info-tag {
  background-color: rgba(0, 122, 255, 0.08);
  border-radius: 16rpx;
  padding: 4rpx 12rpx;
  margin: 4rpx;
}
.info-tag-text {
  font-size: 20rpx;
  color: #007AFF;
  font-weight: 500;
}
.info-status {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 16rpx;
  padding: 4rpx 12rpx;
  z-index: 2;
}
.info-status-text {
  font-size: 20rpx;
  color: #FFFFFF;
  font-weight: 600;
}
.core-data-area {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10rpx;
  margin-bottom: 12rpx;
  padding-top: 10rpx;
  border-top: 1rpx solid rgba(60, 60, 67, 0.1);
}
.core-data-item {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.02);
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  margin-right: 10rpx;
  margin-bottom: 8rpx;
}
.core-data-icon {
  color: #007AFF;
  margin-right: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background-color: rgba(0, 122, 255, 0.1);
}
.price-icon {
  background: linear-gradient(135deg, #FF9500, #FF3B30);
}
.area-icon {
  background: linear-gradient(135deg, #34C759, #30B0C7);
}
.salary-icon {
  background: linear-gradient(135deg, #FF3B30, #FF9500);
}
.location-icon {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
}
.core-data-value {
  font-size: 24rpx;
  color: #636366;
  font-weight: 500;
}
.price-value {
  color: #FF3B30;
  font-weight: 700;
}
.price-unit {
  font-size: 18rpx;
  color: #FF3B30;
  font-weight: 500;
  margin-left: 4rpx;
}
.salary-value {
  color: #FF3B30;
  font-weight: 700;
}
.info-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 10rpx;
  border-top: 1rpx solid rgba(60, 60, 67, 0.1);
  position: relative;
  z-index: 1;
}
.info-user {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.02);
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
}
.user-avatar-container {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  margin-right: 8rpx;
  border: 1rpx solid #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 122, 255, 0.1);
  overflow: hidden;
}
.user-avatar-svg {
  width: 20px;
  height: 20px;
  color: #007AFF;
}
.user-name {
  font-size: 22rpx;
  color: #636366;
  font-weight: 500;
}
.info-stats {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
}
.info-views {
  display: flex;
  align-items: center;
  margin-right: 12rpx;
  background: rgba(0, 0, 0, 0.02);
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
}
.view-icon {
  color: #636366;
  margin-right: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.view-count {
  font-size: 22rpx;
  color: #636366;
  font-weight: 500;
}
.info-redpacket {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #FF3B30, #FF9500);
  border-radius: 20rpx;
  padding: 6rpx 12rpx;
  margin-right: 12rpx;
}
.redpacket-icon {
  color: #FFFFFF;
  margin-right: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.redpacket-amount {
  font-size: 22rpx;
  color: #FFFFFF;
  font-weight: 700;
  margin-right: 6rpx;
}
.redpacket-btn {
  background-color: #FFFFFF;
  border-radius: 50%;
  width: 30rpx;
  height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.redpacket-btn text {
  font-size: 18rpx;
  color: #FF3B30;
  font-weight: 700;
}
.info-actions {
  display: flex;
  margin-left: auto;
}
.info-action {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6rpx 12rpx;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 20rpx;
  margin: 0 4rpx;
}
.info-action-icon {
  color: #8E8E93;
  margin-right: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.info-action-text {
  font-size: 22rpx;
  color: #8E8E93;
  font-weight: 500;
}
