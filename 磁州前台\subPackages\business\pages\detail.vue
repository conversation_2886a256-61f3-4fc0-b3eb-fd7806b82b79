<template>
  <view class="shop-detail-container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <text>加载中...</text>
    </view>
    
    <!-- 商店详情内容 -->
    <block v-else-if="shopDetail">
      <!-- 轮播图 -->
      <swiper class="shop-swiper" indicator-dots autoplay circular>
        <swiper-item v-for="(image, index) in shopDetail.images" :key="index">
          <image :src="image" mode="aspectFill" class="swiper-image"></image>
        </swiper-item>
      </swiper>
      
      <!-- 商店基本信息 -->
      <view class="shop-info-card">
        <view class="shop-title">
          <text class="shop-name">{{ shopDetail.name }}</text>
          <text v-if="shopDetail.isOfficial" class="official-tag">官方</text>
        </view>
        
        <view class="shop-rating">
          <text class="rating-score">{{ shopDetail.rating }}</text>
          <text class="rating-sales">月售{{ shopDetail.sales }}单</text>
        </view>
        
        <view class="shop-tags">
          <text v-for="(tag, index) in shopDetail.tags" :key="index" class="tag">{{ tag }}</text>
        </view>
        
        <view class="shop-address-row">
          <view class="address-label">
            <text class="icon">📍</text>
            <text>{{ shopDetail.address }}</text>
          </view>
          <view class="address-actions">
            <button class="action-btn" @tap="copyAddress">复制</button>
            <button class="action-btn" @tap="openMap">导航</button>
          </view>
        </view>
        
        <view class="shop-contact-row">
          <view class="contact-label">
            <text class="icon">📞</text>
            <text>{{ shopDetail.phone }}</text>
          </view>
          <button class="action-btn" @tap="callPhone">拨打</button>
        </view>
        
        <view class="shop-time-row">
          <text class="icon">🕒</text>
          <text>营业时间：{{ shopDetail.businessHours }}</text>
        </view>
      </view>
      
      <!-- 促销信息 -->
      <view v-if="shopDetail.promotion" class="promotion-card">
        <view class="card-title">优惠活动</view>
        <view class="promotion-content">
          <text class="promotion-tag">优惠</text>
          <text>{{ shopDetail.promotion }}</text>
        </view>
      </view>
      
      <!-- 商店介绍 -->
      <view class="description-card">
        <view class="card-title">商家介绍</view>
        <text class="description-content">{{ shopDetail.description }}</text>
      </view>
    </block>
    
    <!-- 错误提示 -->
    <view v-else class="error-container">
      <text>获取商店信息失败，请稍后再试</text>
      <button class="retry-btn" @tap="loadShopDetail">重试</button>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-action-bar">
      <button class="action-btn share-btn">
        <text class="icon">📤</text>
        <text>分享</text>
      </button>
      <button class="action-btn collect-btn">
        <text class="icon">⭐</text>
        <text>收藏</text>
      </button>
      <button class="primary-btn">联系商家</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import api from '@/mock/api';

// 响应式数据
const shopDetail = ref(null);
const loading = ref(true);
const shopId = ref('');

// 获取商店详情
const loadShopDetail = async () => {
  loading.value = true;
  try {
    const result = await api.business.getShopDetail(shopId.value);
    shopDetail.value = result;
  } catch (error) {
    console.error('获取商店详情失败', error);
    shopDetail.value = null;
  } finally {
    loading.value = false;
  }
};

// 复制地址
const copyAddress = () => {
  uni.setClipboardData({
    data: shopDetail.value.address,
    success: () => {
      uni.showToast({
        title: '地址已复制',
        icon: 'success'
      });
    }
  });
};

// 打开地图导航
const openMap = () => {
  // 这里应该使用实际的经纬度，这里仅做示例
  uni.showToast({
    title: '打开地图导航',
    icon: 'success'
  });
};

// 拨打电话
const callPhone = () => {
  uni.makePhoneCall({
    phoneNumber: shopDetail.value.phone
  });
};

// 页面加载时获取参数并加载数据
onMounted(() => {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.$page?.options || {};
  
  if (options.id) {
    shopId.value = options.id;
    loadShopDetail();
  } else {
    uni.showToast({
      title: '参数错误',
      icon: 'error'
    });
  }
});
</script>

<style scoped>
.shop-detail-container {
  padding-bottom: 100rpx;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  color: #999;
}

.retry-btn {
  margin-top: 20rpx;
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
}

.shop-swiper {
  height: 400rpx;
}

.swiper-image {
  width: 100%;
  height: 100%;
}

.shop-info-card, .promotion-card, .description-card {
  margin: 20rpx;
  padding: 30rpx;
  border-radius: 12rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.shop-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.shop-name {
  font-size: 36rpx;
  font-weight: bold;
}

.official-tag {
  font-size: 22rpx;
  color: #fff;
  background-color: #ff6b00;
  padding: 4rpx 10rpx;
  border-radius: 4rpx;
  margin-left: 10rpx;
}

.shop-rating {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.rating-score {
  color: #ff6b00;
  font-weight: bold;
  margin-right: 10rpx;
  font-size: 32rpx;
}

.rating-sales {
  color: #999;
  font-size: 26rpx;
}

.shop-tags {
  margin-bottom: 20rpx;
}

.tag {
  display: inline-block;
  font-size: 24rpx;
  color: #666;
  background-color: #f5f5f5;
  padding: 6rpx 16rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
  border-radius: 4rpx;
}

.shop-address-row, .shop-contact-row, .shop-time-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
  font-size: 28rpx;
}

.address-label, .contact-label {
  display: flex;
  align-items: center;
  flex: 1;
}

.icon {
  margin-right: 10rpx;
}

.address-actions {
  display: flex;
}

.action-btn {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  margin-left: 10rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  border-left: 8rpx solid #1989fa;
  padding-left: 20rpx;
}

.promotion-content {
  display: flex;
  align-items: center;
}

.promotion-tag {
  font-size: 24rpx;
  color: #fff;
  background-color: #ff6b00;
  padding: 4rpx 10rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
}

.description-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.share-btn, .collect-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  font-size: 24rpx;
  margin-right: 30rpx;
}

.primary-btn {
  flex: 1;
  background-color: #1989fa;
  color: #fff;
  font-size: 30rpx;
  padding: 20rpx 0;
  border-radius: 40rpx;
}
</style> 