"use strict";
const common_vendor = require("../common/vendor.js");
const common_assets = require("../common/assets.js");
const _sfc_main = {
  __name: "FabButtons",
  props: {
    pageName: {
      type: String,
      default: ""
    },
    pageInfo: {
      type: Object,
      default: () => ({
        title: "磁州生活网",
        path: "/pages/index/index",
        imageUrl: ""
      })
    }
  },
  setup(__props, { expose: __expose }) {
    const props = __props;
    const showQrcode = common_vendor.ref(false);
    const showBackToTop = common_vendor.ref(false);
    const scrollTop = common_vendor.ref(0);
    const isHomePage = common_vendor.computed(() => props.pageName === "index");
    const setScrollTop = (newScrollTop) => {
      scrollTop.value = newScrollTop;
      showBackToTop.value = newScrollTop > 200;
    };
    const scrollToTop = () => {
      common_vendor.index.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });
    };
    const onShare = () => {
    };
    const onKefu = () => {
      showQrcode.value = true;
    };
    __expose({
      setScrollTop
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$15,
        b: common_vendor.o(onShare),
        c: isHomePage.value || showBackToTop.value
      }, isHomePage.value || showBackToTop.value ? {
        d: common_assets._imports_0$14,
        e: common_vendor.o(($event) => scrollToTop())
      } : {}, {
        f: common_assets._imports_2$47,
        g: common_vendor.o(onKefu),
        h: showQrcode.value
      }, showQrcode.value ? {
        i: common_assets._imports_3$1,
        j: common_vendor.o(($event) => showQrcode.value = false),
        k: common_vendor.o(($event) => showQrcode.value = false)
      } : {});
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-dd5b99c1"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/FabButtons.js.map
