@echo off
chcp 65001 >nul

echo.
echo ========================================
echo   磁州生活网 - 超简单安装（小白专用）
echo ========================================
echo.

echo 🎯 只需要安装一个软件：Node.js
echo.

echo ========================================
echo 第一步：检查是否已安装 Node.js
echo ========================================
echo.

where node >nul 2>&1
if not errorlevel 1 (
    echo ✅ 太好了！Node.js 已经安装了
    echo    版本：
    node --version
    echo.
    echo 🚀 可以直接启动系统了！
    echo    运行：超简单启动.bat
    echo.
    pause
    exit /b 0
)

echo ❌ Node.js 未安装，需要安装一下
echo.

echo ========================================
echo 第二步：下载 Node.js（超简单）
echo ========================================
echo.

echo 🌐 正在为您打开 Node.js 官网...
start https://nodejs.org/zh-cn/

echo.
echo 📋 安装步骤（只需要3步）：
echo.
echo   1️⃣  在打开的网页中，点击绿色的"下载"按钮
echo       （会自动选择适合您电脑的版本）
echo.
echo   2️⃣  下载完成后，双击安装文件
echo       一路点击"下一步"即可（保持默认设置）
echo.
echo   3️⃣  安装完成后，重新运行这个脚本
echo.

echo ========================================
echo 重要提示
echo ========================================
echo.

echo ✅ 安装时请保持默认设置
echo ✅ 不需要修改任何配置
echo ✅ 安装完成后关闭所有命令窗口
echo ✅ 重新打开命令窗口运行：超简单启动.bat
echo.

echo ❓ 如果不知道怎么操作：
echo    1. 下载完成后双击 .msi 文件
echo    2. 看到安装界面后一直点"下一步"
echo    3. 最后点"完成"
echo    4. 重启电脑（可选，但推荐）
echo.

echo ========================================
echo 安装完成后做什么？
echo ========================================
echo.

echo 安装完 Node.js 后：
echo   1. 关闭这个窗口
echo   2. 重新打开命令窗口
echo   3. 进入项目文件夹
echo   4. 运行：超简单启动.bat
echo.

echo 💡 提示：如果安装过程中遇到问题
echo    可以重启电脑后重试
echo.

pause
