"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      // 商品分类
      categories: [
        { id: 1, name: "热销推荐" },
        { id: 2, name: "主食" },
        { id: 3, name: "小吃" },
        { id: 4, name: "饮品" },
        { id: 5, name: "甜点" }
      ],
      // 当前选中的分类索引，-1 表示全部
      currentCategory: -1,
      // 商品列表
      products: [
        {
          id: 1,
          name: "磁州特色小酥肉",
          image: "/static/images/product-1.jpg",
          category_id: 1,
          price: "28.00",
          original_price: "35.00",
          sales: 156,
          inventory: 999,
          status: 1
        },
        {
          id: 2,
          name: "手工水饺",
          image: "/static/images/product-2.jpg",
          category_id: 2,
          price: "22.00",
          original_price: "",
          sales: 208,
          inventory: 500,
          status: 1
        },
        {
          id: 3,
          name: "香辣鸡翅",
          image: "/static/images/product-3.jpg",
          category_id: 3,
          price: "15.00",
          original_price: "18.00",
          sales: 321,
          inventory: 100,
          status: 1
        },
        {
          id: 4,
          name: "冰镇酸梅汤",
          image: "/static/images/product-4.jpg",
          category_id: 4,
          price: "8.00",
          original_price: "",
          sales: 213,
          inventory: 200,
          status: 1
        },
        {
          id: 5,
          name: "红豆双皮奶",
          image: "/static/images/product-5.jpg",
          category_id: 5,
          price: "12.00",
          original_price: "15.00",
          sales: 87,
          inventory: 150,
          status: 0
        }
      ],
      searchKeyword: "",
      loading: false,
      refreshing: false,
      isSelectMode: false,
      selectedProducts: [],
      showDeleteModal: false,
      statusBarHeight: 20
    };
  },
  computed: {
    // 根据分类和搜索关键词过滤商品
    filteredProducts() {
      let result = [...this.products];
      if (this.currentCategory !== -1) {
        const categoryId = this.categories[this.currentCategory].id;
        result = result.filter((item) => item.category_id === categoryId);
      }
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase();
        result = result.filter(
          (item) => item.name.toLowerCase().includes(keyword)
        );
      }
      return result;
    }
  },
  onLoad() {
    this.setStatusBarHeight();
  },
  methods: {
    // 设置状态栏高度
    setStatusBarHeight() {
      common_vendor.index.getSystemInfo({
        success: (res) => {
          this.statusBarHeight = res.statusBarHeight;
          if (typeof document !== "undefined") {
            document.documentElement.style.setProperty("--status-bar-height", `${this.statusBarHeight}px`);
          }
        }
      });
    },
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 根据分类ID获取分类名称
    getCategoryName(categoryId) {
      const category = this.categories.find((item) => item.id === categoryId);
      return category ? category.name : "未分类";
    },
    // 切换分类
    switchCategory(index) {
      this.currentCategory = index;
    },
    // 搜索商品
    searchProducts() {
      common_vendor.index.hideKeyboard();
    },
    // 清除搜索
    clearSearch() {
      this.searchKeyword = "";
    },
    // 刷新列表
    refreshList() {
      this.refreshing = true;
      setTimeout(() => {
        this.refreshing = false;
        common_vendor.index.showToast({
          title: "刷新成功",
          icon: "none"
        });
      }, 1e3);
    },
    // 加载更多
    loadMore() {
      if (this.loading)
        return;
      this.loading = true;
      setTimeout(() => {
        this.loading = false;
      }, 1e3);
    },
    // 切换选择模式
    toggleSelectMode() {
      this.isSelectMode = !this.isSelectMode;
      if (!this.isSelectMode) {
        this.selectedProducts = [];
      }
    },
    // 判断商品是否被选中
    isProductSelected(productId) {
      return this.selectedProducts.includes(productId);
    },
    // 切换商品选择状态
    toggleProductSelection(productId) {
      const index = this.selectedProducts.indexOf(productId);
      if (index === -1) {
        this.selectedProducts.push(productId);
      } else {
        this.selectedProducts.splice(index, 1);
      }
    },
    // 商品点击处理
    onProductTap(product) {
      if (this.isSelectMode) {
        this.toggleProductSelection(product.id);
      } else {
        this.editProduct(product);
      }
    },
    // 切换商品上下架状态
    toggleProductStatus(product) {
      product.status = product.status === 1 ? 0 : 1;
      common_vendor.index.showToast({
        title: product.status === 1 ? "商品已上架" : "商品已下架",
        icon: "success"
      });
    },
    // 编辑商品
    editProduct(product) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin/pages/store/product-edit?id=${product.id}`
      });
    },
    // 批量设置商品状态
    batchSetStatus(status) {
      if (this.selectedProducts.length === 0) {
        common_vendor.index.showToast({
          title: "请先选择商品",
          icon: "none"
        });
        return;
      }
      this.products.forEach((product) => {
        if (this.selectedProducts.includes(product.id)) {
          product.status = status;
        }
      });
      common_vendor.index.showToast({
        title: status === 1 ? "批量上架成功" : "批量下架成功",
        icon: "success"
      });
      this.toggleSelectMode();
    },
    // 显示删除确认弹窗
    confirmBatchDelete() {
      if (this.selectedProducts.length === 0) {
        common_vendor.index.showToast({
          title: "请先选择商品",
          icon: "none"
        });
        return;
      }
      this.showDeleteModal = true;
    },
    // 取消删除
    cancelDelete() {
      this.showDeleteModal = false;
    },
    // 确认删除
    confirmDelete() {
      this.products = this.products.filter(
        (product) => !this.selectedProducts.includes(product.id)
      );
      this.showDeleteModal = false;
      common_vendor.index.showToast({
        title: "删除成功",
        icon: "success"
      });
      this.toggleSelectMode();
    },
    // 导航到指定页面
    navigateTo(page) {
      const pageMap = {
        addProduct: "/subPackages/merchant-admin/pages/store/product-edit"
      };
      const url = pageMap[page];
      if (url) {
        common_vendor.index.navigateTo({ url });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$7,
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: common_vendor.o(($event) => $options.navigateTo("addProduct")),
    d: $data.currentCategory === -1 ? 1 : "",
    e: common_vendor.o(($event) => $options.switchCategory(-1)),
    f: common_vendor.f($data.categories, (category, index, i0) => {
      return {
        a: common_vendor.t(category.name),
        b: $data.currentCategory === index ? 1 : "",
        c: index,
        d: common_vendor.o(($event) => $options.switchCategory(index), index)
      };
    }),
    g: common_assets._imports_1$48,
    h: common_vendor.o((...args) => $options.searchProducts && $options.searchProducts(...args)),
    i: $data.searchKeyword,
    j: common_vendor.o(($event) => $data.searchKeyword = $event.detail.value),
    k: $data.searchKeyword
  }, $data.searchKeyword ? {
    l: common_vendor.o((...args) => $options.clearSearch && $options.clearSearch(...args))
  } : {}, {
    m: $data.searchKeyword
  }, $data.searchKeyword ? {
    n: common_vendor.t($data.searchKeyword),
    o: common_vendor.t($options.filteredProducts.length)
  } : {}, {
    p: $data.isSelectMode
  }, $data.isSelectMode ? {
    q: common_vendor.t($data.selectedProducts.length),
    r: common_vendor.o(($event) => $options.batchSetStatus(1)),
    s: common_vendor.o(($event) => $options.batchSetStatus(0)),
    t: common_vendor.o((...args) => $options.confirmBatchDelete && $options.confirmBatchDelete(...args))
  } : {}, {
    v: common_vendor.f($options.filteredProducts, (product, k0, i0) => {
      return common_vendor.e($data.isSelectMode ? {
        a: $options.isProductSelected(product.id) ? 1 : "",
        b: common_vendor.o(($event) => $options.toggleProductSelection(product.id), product.id)
      } : {}, {
        c: product.image,
        d: common_vendor.t(product.name),
        e: common_vendor.t($options.getCategoryName(product.category_id)),
        f: common_vendor.t(product.price),
        g: product.original_price
      }, product.original_price ? {
        h: common_vendor.t(product.original_price)
      } : {}, {
        i: common_vendor.t(product.sales),
        j: common_vendor.t(product.inventory),
        k: common_vendor.t(product.status === 1 ? "在售" : "下架"),
        l: product.status === 0 ? 1 : "",
        m: common_vendor.t(product.status === 1 ? "下架" : "上架"),
        n: common_vendor.o(($event) => $options.toggleProductStatus(product), product.id),
        o: common_vendor.o(($event) => $options.editProduct(product), product.id),
        p: common_vendor.o(() => {
        }, product.id),
        q: $options.isProductSelected(product.id) ? 1 : "",
        r: product.id,
        s: common_vendor.o(($event) => $options.onProductTap(product), product.id),
        t: common_vendor.o((...args) => $options.toggleSelectMode && $options.toggleSelectMode(...args), product.id)
      });
    }),
    w: $data.isSelectMode,
    x: $data.loading
  }, $data.loading ? {} : {}, {
    y: $options.filteredProducts.length === 0 && !$data.loading
  }, $options.filteredProducts.length === 0 && !$data.loading ? {
    z: common_assets._imports_2$43,
    A: common_vendor.t($data.searchKeyword ? "没有找到相关商品" : "暂无商品，快去添加吧")
  } : {}, {
    B: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args)),
    C: $data.refreshing,
    D: common_vendor.o((...args) => $options.refreshList && $options.refreshList(...args)),
    E: !$data.isSelectMode
  }, !$data.isSelectMode ? {
    F: common_vendor.o((...args) => $options.toggleSelectMode && $options.toggleSelectMode(...args))
  } : {}, {
    G: $data.showDeleteModal
  }, $data.showDeleteModal ? {} : {}, {
    H: $data.showDeleteModal
  }, $data.showDeleteModal ? {
    I: common_vendor.t($data.selectedProducts.length),
    J: common_vendor.o((...args) => $options.cancelDelete && $options.cancelDelete(...args)),
    K: common_vendor.o((...args) => $options.confirmDelete && $options.confirmDelete(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin/pages/store/product.js.map
