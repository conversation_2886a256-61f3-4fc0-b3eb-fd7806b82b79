<template>
  <view class="distribution-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="navigateBack">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M19 12H5M12 19l-7-7 7-7" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        <view class="navbar-title">分销中心</view>
        <view class="navbar-right">
          <view class="help-btn" @click="showHelp">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <circle cx="12" cy="12" r="10" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
              <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              <line x1="12" y1="17" x2="12.01" y2="17" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
            </svg>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view 
      class="content-scroll" 
      scroll-y="true"
      :scroll-anchoring="true"
      :enhanced="true"
      :bounces="true"
      :show-scrollbar="false"
      @scrolltolower="loadMore"
    >
      <!-- 分销员信息卡片 -->
      <view class="distributor-card" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: 'linear-gradient(135deg, #FFFFFF 0%, #F9F5FF 100%)',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <view class="distributor-header">
          <view class="distributor-avatar-container">
            <image class="distributor-avatar" :src="distributorInfo.avatar" mode="aspectFill"></image>
            <view class="distributor-badge" v-if="distributorInfo.isVerified" :style="{
              background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)'
            }">
              <svg class="icon" viewBox="0 0 24 24" width="12" height="12">
                <path d="M9 12l2 2 4-4" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
            </view>
          </view>
          <view class="distributor-info">
            <view class="distributor-name-container">
              <text class="distributor-name">{{ distributorInfo.name }}</text>
              <view class="distributor-level" :style="{
                background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)'
              }">
                <text>{{ distributorInfo.level }}</text>
              </view>
            </view>
            <text class="distributor-id">ID: {{ distributorInfo.id }}</text>
            <view class="distributor-stats">
              <text class="stat-item">已推广 {{ distributorInfo.promotedDays }} 天</text>
              <text class="stat-divider">|</text>
              <text class="stat-item">团队 {{ distributorInfo.teamCount }} 人</text>
            </view>
          </view>
          <view class="qrcode-btn" @click="showQrcode" :style="{
            background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)'
          }">
            <svg class="icon" viewBox="0 0 24 24" width="20" height="20">
              <path d="M3 3h7v7H3z M14 3h7v7h-7z M3 14h7v7H3z M17 17h2v2h-2z M14 14h2v2h-2z M19 14h2v2h-2z M14 19h7v2h-7z" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
            <text>推广码</text>
          </view>
        </view>
        
        <view class="level-progress">
          <view class="level-progress-header">
            <text class="level-text">{{ distributorInfo.level }}</text>
            <text class="next-level-text">{{ distributorInfo.nextLevel }}</text>
          </view>
          <view class="progress-bar-bg" :style="{
            background: 'rgba(172,57,255,0.2)',
            borderRadius: '10rpx',
            height: '10rpx',
            width: '100%'
          }">
            <view class="progress-bar-fill" :style="{
              width: distributorInfo.upgradeProgress + '%',
              background: 'linear-gradient(90deg, #AC39FF 0%, #B87AFF 100%)',
              borderRadius: '10rpx',
              height: '100%'
            }"></view>
          </view>
          <text class="progress-hint">再推广{{ distributorInfo.ordersToUpgrade }}个订单升级为{{ distributorInfo.nextLevel }}</text>
        </view>
      </view>
      
      <!-- 收益概览卡片 -->
      <view class="earnings-card" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: '#FFFFFF',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <view class="card-header">
          <text class="card-title">收益概览</text>
          <view class="header-right" @click="navigateTo('/subPackages/activity-showcase/pages/distribution/earnings/index')">
            <text class="view-all">查看全部</text>
            <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
              <path d="M9 18l6-6-6-6" stroke="#AC39FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
        
        <view class="total-earnings">
          <view class="earnings-label">
            <text>累计收益(元)</text>
            <svg class="icon" viewBox="0 0 24 24" width="16" height="16" @click="showEarningsTip">
              <circle cx="12" cy="12" r="10" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
              <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              <line x1="12" y1="17" x2="12.01" y2="17" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
            </svg>
          </view>
          <text class="earnings-value">{{ distributorInfo.totalEarnings }}</text>
          <view class="earnings-trend">
            <text class="trend-value">较上月 </text>
            <text class="trend-up" v-if="distributorInfo.earningsTrend > 0">+{{ distributorInfo.earningsTrend }}%</text>
            <text class="trend-down" v-else>{{ distributorInfo.earningsTrend }}%</text>
          </view>
        </view>
        
        <view class="earnings-grid">
          <view class="earnings-grid-item">
            <text class="grid-value">{{ distributorInfo.todayEarnings }}</text>
            <text class="grid-label">今日收益(元)</text>
          </view>
          <view class="earnings-grid-item">
            <text class="grid-value">{{ distributorInfo.pendingEarnings }}</text>
            <text class="grid-label">待结算(元)</text>
          </view>
          <view class="earnings-grid-item">
            <text class="grid-value">{{ distributorInfo.withdrawableAmount }}</text>
            <text class="grid-label">可提现(元)</text>
          </view>
        </view>
        
        <view class="action-buttons">
          <view class="action-btn withdraw-btn" @click="navigateTo('/subPackages/activity-showcase/pages/distribution/withdraw/index')" :style="{
            background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)'
          }">
            <text>立即提现</text>
          </view>
          <view class="action-btn record-btn" @click="navigateTo('/subPackages/activity-showcase/pages/distribution/records/index')">
            <text>收益明细</text>
          </view>
        </view>
      </view>
      
      <!-- 功能入口 -->
      <view class="feature-grid" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: '#FFFFFF',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <view class="feature-item" @click="navigateTo('/subPackages/activity-showcase/pages/distribution/orders/index')">
          <view class="feature-icon" :style="{
            background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)'
          }">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <path d="M9 17h6M9 13h6M9 9h6M5 21V5a2 2 0 012-2h10a2 2 0 012 2v16l-3-2-2 2-2-2-2 2-2-2-3 2z" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
          <text class="feature-name">分销订单</text>
          <text class="feature-count">{{ orderStats.totalCount }}单</text>
        </view>
        
        <view class="feature-item" @click="navigateTo('/subPackages/activity-showcase/pages/distribution/team/index')">
          <view class="feature-icon" :style="{
            background: 'linear-gradient(135deg, #34C759 0%, #30D158 100%)'
          }">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <path d="M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2M9 11a4 4 0 100-8 4 4 0 000 8zM23 21v-2a4 4 0 00-3-3.87M16 3.13a4 4 0 010 7.75" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
          <text class="feature-name">我的团队</text>
          <text class="feature-count">{{ distributorInfo.teamCount }}人</text>
        </view>
        
        <view class="feature-item" @click="navigateTo('/subPackages/activity-showcase/pages/distribution/poster/index')">
          <view class="feature-icon" :style="{
            background: 'linear-gradient(135deg, #5AC8FA 0%, #1C84FF 100%)'
          }">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></rect>
              <circle cx="8.5" cy="8.5" r="1.5" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
              <path d="M21 15l-5-5L5 21" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
          <text class="feature-name">推广海报</text>
          <text class="feature-count">{{ posterStats.count }}张</text>
        </view>
        
        <view class="feature-item" @click="navigateTo('/subPackages/activity-showcase/pages/distribution/products/index')">
          <view class="feature-icon" :style="{
            background: 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)'
          }">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <circle cx="9" cy="21" r="1" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
              <circle cx="20" cy="21" r="1" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
              <path d="M1 1h4l2.68 13.39a2 2 0 002 1.61h9.72a2 2 0 002-1.61L23 6H6" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
          <text class="feature-name">推广商品</text>
          <text class="feature-count">{{ productStats.count }}个</text>
        </view>
      </view>
      
      <!-- 分销订单 -->
      <view class="orders-card" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: '#FFFFFF',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <view class="card-header">
          <text class="card-title">最近订单</text>
          <view class="view-all-btn" @click="navigateTo('/subPackages/activity-showcase/pages/distribution/orders/index')">
            <text>查看全部</text>
            <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
              <path d="M9 18l6-6-6-6" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
        
        <view class="order-tabs">
          <view 
            v-for="(tab, index) in orderTabs" 
            :key="index"
            class="order-tab"
            :class="{ active: currentOrderTab === index }"
            @click="switchOrderTab(index)"
          >
            <text>{{ tab.name }}</text>
            <view class="tab-indicator" v-if="currentOrderTab === index"></view>
          </view>
        </view>
        
        <view class="order-list">
          <view 
            v-for="(order, index) in getOrdersByStatus(orderTabs[currentOrderTab].status)"
            :key="order.id"
            class="order-item"
            @click="navigateTo(`/subPackages/activity-showcase/pages/distribution/order-detail/index?id=${order.id}`)"
          >
            <view class="order-header">
              <text class="order-id">订单号：{{ order.orderNumber }}</text>
              <text class="order-status" :style="{
                color: getOrderStatusColor(order.status)
              }">{{ getOrderStatusText(order.status) }}</text>
            </view>
            
            <view class="order-product">
              <image class="product-image" :src="order.productImage" mode="aspectFill"></image>
              <view class="product-info">
                <text class="product-name">{{ order.productName }}</text>
                <view class="product-price-qty">
                  <text class="product-price">¥{{ order.productPrice }}</text>
                  <text class="product-qty">x{{ order.quantity }}</text>
                </view>
              </view>
            </view>
            
            <view class="order-footer">
              <view class="order-time">
                <text>{{ order.orderTime }}</text>
              </view>
              <view class="order-commission">
                <text class="commission-label">预计收益：</text>
                <text class="commission-value">¥{{ order.commission }}</text>
              </view>
            </view>
          </view>
          
          <!-- 空状态 -->
          <view class="empty-state" v-if="getOrdersByStatus(orderTabs[currentOrderTab].status).length === 0">
            <text class="empty-text">暂无{{ orderTabs[currentOrderTab].name }}订单</text>
          </view>
        </view>
      </view>
      
      <!-- 团队概览 -->
      <view class="team-card" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: '#FFFFFF',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <view class="card-header">
          <text class="card-title">团队概览</text>
          <view class="view-all-btn" @click="navigateTo('/subPackages/activity-showcase/pages/distribution/team/index')">
            <text>查看全部</text>
            <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
              <path d="M9 18l6-6-6-6" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
        
        <view class="team-stats">
          <view class="team-stat-item">
            <text class="stat-value">{{ teamStats.totalMembers }}</text>
            <text class="stat-label">团队总人数</text>
          </view>
          <view class="team-stat-item">
            <text class="stat-value">{{ teamStats.directMembers }}</text>
            <text class="stat-label">直属成员</text>
          </view>
          <view class="team-stat-item">
            <text class="stat-value">{{ teamStats.indirectMembers }}</text>
            <text class="stat-label">间接成员</text>
          </view>
          <view class="team-stat-item">
            <text class="stat-value">{{ teamStats.newMembers }}</text>
            <text class="stat-label">本月新增</text>
          </view>
        </view>
        
        <view class="team-members">
          <text class="section-subtitle">团队成员</text>
          <view class="member-list">
            <view 
              v-for="(member, index) in teamMembers" 
              :key="member.id"
              class="member-item"
              @click="navigateTo(`/subPackages/activity-showcase/pages/distribution/member-detail/index?id=${member.id}`)"
            >
              <image class="member-avatar" :src="member.avatar" mode="aspectFill"></image>
              <view class="member-info">
                <view class="member-name-level">
                  <text class="member-name">{{ member.name }}</text>
                  <view class="member-level" :style="{
                    background: getLevelColor(member.level)
                  }">
                    <text>{{ member.level }}</text>
                  </view>
                </view>
                <view class="member-stats">
                  <text class="member-stat">{{ member.joinTime }}</text>
                  <text class="member-stat-divider">|</text>
                  <text class="member-stat">贡献收益: ¥{{ member.contribution }}</text>
                </view>
              </view>
              <svg class="arrow-icon" viewBox="0 0 24 24" width="16" height="16">
                <path d="M9 18l6-6-6-6" stroke="#C7C7CC" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
            </view>
          </view>
          
          <!-- 空状态 -->
          <view class="empty-state" v-if="teamMembers.length === 0">
            <text class="empty-text">暂无团队成员</text>
            <view class="action-btn invite-btn" @click="showInviteQrcode" :style="{
              background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)'
            }">
              <text>邀请好友加入</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
    
    <!-- 底部导航栏 -->
    <view class="tabbar">
      <view 
        class="tabbar-item" 
        :class="{active: currentTabBar === 'home'}" 
        @click="switchTabBar('home')"
        data-tab="home"
      >
        <view class="tab-icon home"></view>
        <text class="tabbar-text">首页</text>
      </view>
      <view 
        class="tabbar-item" 
        :class="{active: currentTabBar === 'discover'}" 
        @click="switchTabBar('discover')"
        data-tab="discover"
      >
        <view class="tab-icon discover"></view>
        <text class="tabbar-text">本地商城</text>
      </view>
      <view 
        class="tabbar-item" 
        :class="{active: currentTabBar === 'distribution'}" 
        @click="switchTabBar('distribution')"
        data-tab="distribution"
      >
        <view class="tab-icon distribution"></view>
        <text class="tabbar-text">分销</text>
      </view>
      <view 
        class="tabbar-item" 
        :class="{active: currentTabBar === 'message'}" 
        @click="switchTabBar('message')"
        data-tab="message"
      >
        <view class="tab-icon message">
          <view class="badge" v-if="unreadMessageCount > 0">{{ unreadMessageCount > 99 ? '99+' : unreadMessageCount }}</view>
        </view>
        <text class="tabbar-text">消息</text>
      </view>
      <view 
        class="tabbar-item" 
        :class="{active: currentTabBar === 'my'}" 
        @click="switchTabBar('my')"
        data-tab="my"
      >
        <view class="tab-icon user"></view>
        <text class="tabbar-text">我的</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 底部导航栏当前选中项
const currentTabBar = ref('distribution');

// 未读消息数量
const unreadMessageCount = ref(3);

// 分销员信息
const distributorInfo = ref({
  name: '张三',
  id: 'D0001',
  avatar: 'https://via.placeholder.com/100',
  isVerified: true,
  level: '初级分销商',
  nextLevel: '中级分销商',
  promotedDays: 30,
  teamCount: 10,
  upgradeProgress: 75,
  ordersToUpgrade: 20,
  totalEarnings: 1234.56,
  pendingEarnings: 123.45,
  withdrawableAmount: 1111.11,
  todayEarnings: 23.45,
  earningsTrend: 15
});

// 分销订单统计
const orderStats = ref({
  totalCount: 150,
  todayCount: 20,
  pendingCount: 10,
  completedCount: 120
});

// 推广海报统计
const posterStats = ref({
  count: 8
});

// 推广商品统计
const productStats = ref({
  count: 80
});

// 团队统计
const teamStats = ref({
  totalMembers: 100,
  directMembers: 30,
  indirectMembers: 70,
  newMembers: 15
});

// 订单标签页
const currentOrderTab = ref(0);
const orderTabs = ref([
  { name: '全部', status: 'all' },
  { name: '待付款', status: 'pending_payment' },
  { name: '待发货', status: 'pending_delivery' },
  { name: '已发货', status: 'delivered' },
  { name: '已完成', status: 'completed' },
  { name: '已失效', status: 'invalid' }
]);

// 团队成员
const teamMembers = ref([
  { id: 'M001', name: '李四', avatar: 'https://via.placeholder.com/50', level: '初级分销商', joinTime: '2023-01-01', contribution: 123.45 },
  { id: 'M002', name: '王五', avatar: 'https://via.placeholder.com/50', level: '中级分销商', joinTime: '2023-02-15', contribution: 234.56 },
  { id: 'M003', name: '赵六', avatar: 'https://via.placeholder.com/50', level: '高级分销商', joinTime: '2023-03-01', contribution: 345.67 }
]);

// 返回上一页
const navigateBack = () => {
  uni.navigateBack();
};

// 显示帮助
const showHelp = () => {
  uni.showToast({
    title: '分销规则说明',
    icon: 'none'
  });
};

// 显示二维码
const showQrcode = () => {
  uni.showToast({
    title: '显示推广二维码',
    icon: 'none'
  });
};

// 显示收益提示
const showEarningsTip = () => {
  uni.showToast({
    title: '累计收益包括已结算和未结算的总收益',
    icon: 'none'
  });
};

// 显示邀请二维码
const showInviteQrcode = () => {
  uni.showToast({
    title: '显示邀请二维码',
    icon: 'none'
  });
};

// 切换订单标签页
const switchOrderTab = (index) => {
  currentOrderTab.value = index;
};

// 获取订单状态颜色
const getOrderStatusColor = (status) => {
  switch(status) {
    case 'pending_payment':
      return '#FF9500';
    case 'pending_delivery':
      return '#5AC8FA';
    case 'delivered':
      return '#34C759';
    case 'completed':
      return '#AC39FF';
    case 'invalid':
      return '#8E8E93';
    default:
      return '#333333';
  }
};

// 获取订单状态文本
const getOrderStatusText = (status) => {
  switch(status) {
    case 'pending_payment':
      return '待付款';
    case 'pending_delivery':
      return '待发货';
    case 'delivered':
      return '已发货';
    case 'completed':
      return '已完成';
    case 'invalid':
      return '已失效';
    default:
      return '未知状态';
  }
};

// 获取等级颜色
const getLevelColor = (level) => {
  switch(level) {
    case '初级分销商':
      return 'linear-gradient(135deg, #5AC8FA 0%, #1C84FF 100%)';
    case '中级分销商':
      return 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)';
    case '高级分销商':
      return 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)';
    default:
      return 'linear-gradient(135deg, #8E8E93 0%, #C7C7CC 100%)';
  }
};

// 根据状态获取订单列表
const getOrdersByStatus = (status) => {
  // 模拟数据
  const allOrders = [
    {
      id: 'O001',
      orderNumber: '20230101001',
      status: 'completed',
      productName: 'iPhone 14 Pro 深空黑 256G',
      productImage: 'https://via.placeholder.com/100',
      productPrice: 7999,
      quantity: 1,
      orderTime: '2023-01-01 12:00:00',
      commission: 399.95
    },
    {
      id: 'O002',
      orderNumber: '20230102001',
      status: 'pending_payment',
      productName: '华为Mate 50 Pro 曜金黑 512G',
      productImage: 'https://via.placeholder.com/100',
      productPrice: 6999,
      quantity: 1,
      orderTime: '2023-01-02 12:00:00',
      commission: 349.95
    },
    {
      id: 'O003',
      orderNumber: '20230103001',
      status: 'pending_delivery',
      productName: '小米12S Ultra 陶瓷白 256G',
      productImage: 'https://via.placeholder.com/100',
      productPrice: 5999,
      quantity: 1,
      orderTime: '2023-01-03 12:00:00',
      commission: 299.95
    },
    {
      id: 'O004',
      orderNumber: '20230104001',
      status: 'delivered',
      productName: 'OPPO Find X5 Pro 陶瓷白 256G',
      productImage: 'https://via.placeholder.com/100',
      productPrice: 4999,
      quantity: 1,
      orderTime: '2023-01-04 12:00:00',
      commission: 249.95
    },
    {
      id: 'O005',
      orderNumber: '20230105001',
      status: 'invalid',
      productName: '三星Galaxy S22 Ultra 幻影黑 512G',
      productImage: 'https://via.placeholder.com/100',
      productPrice: 8999,
      quantity: 1,
      orderTime: '2023-01-05 12:00:00',
      commission: 449.95
    }
  ];
  
  if (status === 'all') {
    return allOrders;
  }
  
  return allOrders.filter(order => order.status === status);
};

// 页面导航
const navigateTo = (url) => {
  uni.navigateTo({ url });
};

// 切换底部导航栏
const switchTabBar = (tab) => {
  if (currentTabBar.value === tab) return;
      
  currentTabBar.value = tab;
      
  // 根据选中的标签页进行相应的导航
  switch(tab) {
    case 'home':
      uni.reLaunch({
        url: '/subPackages/activity-showcase/pages/index/index',
        fail: (err) => {
          console.error('页面跳转失败:', err);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
      break;
    case 'discover':
      uni.navigateTo({
        url: '/subPackages/activity-showcase/pages/discover/index',
        fail: (err) => {
          console.error('页面跳转失败:', err);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
      break;
    case 'distribution':
      // 已在分销中心页面，不需要导航
      break;
    case 'message':
      uni.navigateTo({
        url: '/subPackages/activity-showcase/pages/message/index',
        fail: (err) => {
          console.error('页面跳转失败:', err);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
      break;
    case 'my':
      uni.navigateTo({
        url: '/subPackages/activity-showcase/pages/my/index',
        fail: (err) => {
          console.error('页面跳转失败:', err);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
      break;
  }
};

// 加载更多
const loadMore = () => {
  // 加载更多数据...
};

// 页面加载
onMounted(() => {
  // 初始化数据
});
</script>

<style lang="scss" scoped>
.distribution-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F2F2F7;
  position: relative;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
  
  .navbar-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 4px 6px rgba(172,57,255,0.15);
  }
  
  .navbar-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 30rpx;
    padding-top: var(--status-bar-height, 25px);
    box-sizing: border-box;
  }
  
  .navbar-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #FFFFFF;
    letter-spacing: 0.5px;
  }
  
  .back-btn, .help-btn {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .icon {
      width: 48rpx;
      height: 48rpx;
    }
  }
}

/* 内容区域 */
.content-scroll {
  position: absolute;
  top: calc(var(--status-bar-height, 25px) + 62px);
  left: 0;
  right: 0;
  bottom: 100rpx;
  padding: 30rpx;
  box-sizing: border-box;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 34px; /* iOS 安全区域高度 */
}

/* 分销员信息卡片 */
.distributor-card {
  display: flex;
  flex-direction: column;
  
  .distributor-header {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;
    
    .distributor-avatar-container {
      position: relative;
      margin-right: 20rpx;
      
      .distributor-avatar {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        border: 3rpx solid #FFFFFF;
        box-shadow: 0 4px 10px rgba(0,0,0,0.1);
      }
      
      .distributor-badge {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 36rpx;
        height: 36rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2rpx solid #FFFFFF;
      }
    }
    
    .distributor-info {
      flex: 1;
      
      .distributor-name-container {
        display: flex;
        align-items: center;
        margin-bottom: 10rpx;
        
        .distributor-name {
          font-size: 32rpx;
          font-weight: 600;
          color: #333333;
          margin-right: 10rpx;
        }
        
        .distributor-level {
          padding: 4rpx 12rpx;
          border-radius: 20rpx;
          
          text {
            font-size: 20rpx;
            color: #FFFFFF;
            font-weight: 500;
          }
        }
      }
      
      .distributor-id {
        font-size: 24rpx;
        color: #8E8E93;
        margin-bottom: 10rpx;
      }
      
      .distributor-stats {
        display: flex;
        align-items: center;
        
        .stat-item {
          font-size: 24rpx;
          color: #8E8E93;
        }
        
        .stat-divider {
          margin: 0 10rpx;
          font-size: 24rpx;
          color: #D1D1D6;
        }
      }
    }
    
    .qrcode-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100rpx;
      height: 100rpx;
      border-radius: 20rpx;
      
      .icon {
        width: 40rpx;
        height: 40rpx;
        margin-bottom: 5rpx;
      }
      
      text {
        font-size: 20rpx;
        color: #FFFFFF;
      }
    }
  }
  
  .level-progress {
    margin-top: 10rpx;
    
    .level-progress-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10rpx;
      
      .level-text, .next-level-text {
        font-size: 24rpx;
        color: #333333;
      }
    }
    
    .progress-hint {
      font-size: 22rpx;
      color: #8E8E93;
      margin-top: 10rpx;
      text-align: center;
    }
  }
}

/* 收益概览卡片 */
.earnings-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .card-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }
    
    .header-right {
      display: flex;
      align-items: center;
      
      .view-all {
        font-size: 24rpx;
        color: #AC39FF;
        margin-right: 5rpx;
      }
      
      .icon {
        width: 24rpx;
        height: 24rpx;
      }
    }
  }
  
  .total-earnings {
    text-align: center;
    margin-bottom: 30rpx;
    
    .earnings-label {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 10rpx;
      
      text {
        font-size: 28rpx;
        color: #8E8E93;
        margin-right: 5rpx;
      }
      
      .icon {
        width: 28rpx;
        height: 28rpx;
      }
    }
    
    .earnings-value {
      font-size: 48rpx;
      font-weight: 700;
      color: #AC39FF;
      margin-bottom: 10rpx;
    }
    
    .earnings-trend {
      .trend-value {
        font-size: 24rpx;
        color: #8E8E93;
      }
      
      .trend-up {
        font-size: 24rpx;
        color: #FF3B30;
      }
      
      .trend-down {
        font-size: 24rpx;
        color: #34C759;
      }
    }
  }
  
  .earnings-grid {
    display: flex;
    justify-content: space-around;
    margin-bottom: 30rpx;
    
    .earnings-grid-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .grid-value {
        font-size: 32rpx;
        font-weight: 600;
        color: #333333;
        margin-bottom: 5rpx;
      }
      
      .grid-label {
        font-size: 24rpx;
        color: #8E8E93;
      }
    }
  }
  
  .action-buttons {
    display: flex;
    justify-content: space-between;
    
    .action-btn {
      flex: 1;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 40rpx;
      
      text {
        font-size: 28rpx;
        font-weight: 500;
      }
      
      &.withdraw-btn {
        margin-right: 15rpx;
        
        text {
          color: #FFFFFF;
        }
      }
      
      &.record-btn {
        border: 2rpx solid #AC39FF;
        
        text {
          color: #AC39FF;
        }
      }
    }
  }
}

/* 功能入口 */
.feature-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  
  .feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .feature-icon {
      width: 80rpx;
      height: 80rpx;
      border-radius: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 10rpx;
      
      .icon {
        width: 40rpx;
        height: 40rpx;
      }
    }
    
    .feature-name {
      font-size: 24rpx;
      color: #333333;
      margin-bottom: 5rpx;
    }
    
    .feature-count {
      font-size: 20rpx;
      color: #8E8E93;
    }
  }
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  
  .card-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
  }
  
  .header-right {
    display: flex;
    align-items: center;
    
    .view-all {
      font-size: 24rpx;
      color: #AC39FF;
      margin-right: 5rpx;
    }
    
    .icon {
      width: 24rpx;
      height: 24rpx;
    }
  }
  
  .view-all-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%);
    border-radius: 30rpx;
    padding: 10rpx 20rpx;
    box-shadow: 0 4rpx 8rpx rgba(172, 57, 255, 0.25);
    transition: all 0.3s ease;
    
    &:active {
      transform: scale(0.95);
      box-shadow: 0 2rpx 4rpx rgba(172, 57, 255, 0.15);
    }
    
    text {
      font-size: 24rpx;
      color: #FFFFFF;
      font-weight: 500;
      margin-right: 5rpx;
    }
    
    .icon {
      width: 24rpx;
      height: 24rpx;
    }
  }
}

/* 订单卡片 */
.orders-card {
  margin-bottom: 30rpx;
  
  .order-tabs {
    display: flex;
    overflow-x: auto;
    margin: 20rpx 0;
    
    .order-tab {
      padding: 10rpx 20rpx;
      position: relative;
      white-space: nowrap;
      
      text {
        font-size: 28rpx;
        color: #8E8E93;
        transition: color 0.3s ease;
      }
      
      .tab-indicator {
        position: absolute;
        bottom: -5rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 40rpx;
        height: 3px;
        border-radius: 1.5px;
        background: linear-gradient(90deg, #AC39FF 0%, #B87AFF 100%);
        transition: all 0.3s ease;
      }
      
      &.active {
        text {
          color: #AC39FF;
          font-weight: 500;
        }
      }
    }
  }
  
  .order-list {
    .order-item {
      background: #F9F9F9;
      border-radius: 20rpx;
      padding: 20rpx;
      margin-bottom: 30rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
      transition: transform 0.2s ease;
      
      &:active {
        transform: scale(0.98);
      }
      
      .order-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15rpx;
        
        .order-id {
          font-size: 24rpx;
          color: #8E8E93;
        }
        
        .order-status {
          font-size: 24rpx;
          font-weight: 500;
        }
      }
      
      .order-product {
        display: flex;
        margin-bottom: 15rpx;
        
        .product-image {
          width: 100rpx;
          height: 100rpx;
          border-radius: 10rpx;
          margin-right: 15rpx;
        }
        
        .product-info {
          flex: 1;
          
          .product-name {
            font-size: 28rpx;
            color: #333333;
            margin-bottom: 10rpx;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          
          .product-price-qty {
            display: flex;
            justify-content: space-between;
            
            .product-price {
              font-size: 28rpx;
              color: #FF3B69;
              font-weight: 600;
            }
            
            .product-qty {
              font-size: 24rpx;
              color: #8E8E93;
            }
          }
        }
      }
      
      .order-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .order-time {
          font-size: 24rpx;
          color: #8E8E93;
        }
        
        .order-commission {
          display: flex;
          align-items: center;
          
          .commission-label {
            font-size: 24rpx;
            color: #8E8E93;
          }
          
          .commission-value {
            font-size: 28rpx;
            color: #FF3B69;
            font-weight: 600;
          }
        }
      }
    }
  }
}

/* 团队卡片 */
.team-card {
  margin-bottom: 30rpx;
  
  .team-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 30rpx;
    
    .team-stat-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .stat-value {
        font-size: 32rpx;
        font-weight: 600;
        color: #333333;
        margin-bottom: 5rpx;
      }
      
      .stat-label {
        font-size: 24rpx;
        color: #8E8E93;
      }
    }
  }
  
  .team-members {
    .section-subtitle {
      font-size: 28rpx;
      font-weight: 600;
      color: #333333;
      margin-bottom: 20rpx;
      display: block;
    }
    
    .member-list {
      .member-item {
        display: flex;
        align-items: center;
        padding: 20rpx;
        border-bottom: 1rpx solid #F2F2F7;
        margin-bottom: 20rpx;
        background: #F9F9F9;
        border-radius: 20rpx;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
        transition: transform 0.2s ease;
        
        &:active {
          transform: scale(0.98);
        }
        
        &:last-child {
          border-bottom: none;
        }
        
        .member-avatar {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          margin-right: 15rpx;
        }
        
        .member-info {
          flex: 1;
          
          .member-name-level {
            display: flex;
            align-items: center;
            margin-bottom: 10rpx;
            
            .member-name {
              font-size: 28rpx;
              color: #333333;
              margin-right: 10rpx;
            }
            
            .member-level {
              padding: 4rpx 12rpx;
              border-radius: 20rpx;
              
              text {
                font-size: 20rpx;
                color: #FFFFFF;
                font-weight: 500;
              }
            }
          }
          
          .member-stats {
            display: flex;
            align-items: center;
            
            .member-stat {
              font-size: 24rpx;
              color: #8E8E93;
            }
            
            .member-stat-divider {
              margin: 0 10rpx;
              font-size: 24rpx;
              color: #D1D1D6;
            }
          }
        }
        
        .arrow-icon {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
  }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  background: #F9F9F9;
  border-radius: 20rpx;
  margin: 20rpx 0;
  
  .empty-image {
    width: 160rpx;
    height: 160rpx;
    margin-bottom: 20rpx;
    opacity: 0.7;
  }
  
  .empty-text {
    font-size: 28rpx;
    color: #8E8E93;
    margin-bottom: 30rpx;
    font-weight: 500;
  }
  
  .action-btn {
    padding: 16rpx 40rpx;
    border-radius: 35px;
    background: linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%);
    box-shadow: 0 4rpx 8rpx rgba(172, 57, 255, 0.25);
    
    text {
      color: #FFFFFF;
      font-size: 28rpx;
      font-weight: 500;
    }
    
    &:active {
      opacity: 0.9;
      transform: scale(0.98);
      box-shadow: 0 2rpx 4rpx rgba(172, 57, 255, 0.15);
    }
  }
}

/* 底部导航栏 */
.tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #FFFFFF;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 99;
  border-top: 1rpx solid #EEEEEE;
}
  
.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 6px 0;
  box-sizing: border-box;
  position: relative;
}
    
.tabbar-item:active {
  transform: scale(0.9);
}
    
.tabbar-item.active .tab-icon {
  transform: translateY(-5rpx);
}
      
.tabbar-item.active .tabbar-text {
  color: #FF3B69;
  font-weight: 600;
  transform: translateY(-2rpx);
}
    
.tab-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  color: #999999;
  display: flex;
  justify-content: center;
  align-items: center;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 首页图标 */
.tab-icon.home {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E");
}

.tabbar-item.active[data-tab="home"] .tab-icon.home {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E");
}

/* 发现图标 */
.tab-icon.discover {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5.5-2.5l7.51-3.49L17.5 6.5 9.99 9.99 6.5 17.5zm5.5-6.6c.61 0 1.1.49 1.1 1.1s-.49 1.1-1.1 1.1-1.1-.49-1.1-1.1.49-1.1 1.1-1.1z'/%3E%3C/svg%3E");
}

.tabbar-item.active[data-tab="discover"] .tab-icon.discover {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5.5-2.5l7.51-3.49L17.5 6.5 9.99 9.99 6.5 17.5zm5.5-6.6c.61 0 1.1.49 1.1 1.1s-.49 1.1-1.1 1.1-1.1-.49-1.1-1.1.49-1.1 1.1-1.1z'/%3E%3C/svg%3E");
}

/* 分销图标 */
.tab-icon.distribution {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E");
}

.tabbar-item.active[data-tab="distribution"] .tab-icon.distribution {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E");
}

/* 消息图标 */
.tab-icon.message {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H5.17L4 17.17V4h16v12z'/%3E%3C/svg%3E");
}

.tabbar-item.active[data-tab="message"] .tab-icon.message {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E");
}

/* 我的图标 */
.tab-icon.user {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}

.tabbar-item.active[data-tab="my"] .tab-icon.user {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}

.tabbar-item.active .tab-icon {
  filter: drop-shadow(0 1px 2px rgba(255, 59, 105, 0.3));
}
      
.badge {
  position: absolute;
  top: -8rpx;
  right: -12rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #FF453A, #FF2D55);
  color: #FFFFFF;
  font-size: 18rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 6rpx;
  box-sizing: border-box;
  font-weight: 600;
  box-shadow: 0 2rpx 6rpx rgba(255, 45, 85, 0.3);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transform: scale(0.9);
}
    
.tabbar-text {
  font-size: 22rpx;
  color: #8E8E93;
  margin-top: 2rpx;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}
    
.tabbar-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) scaleX(0);
  width: 30rpx;
  height: 4rpx;
  background: #FF3B69;
  border-radius: 2rpx;
  transition: transform 0.3s ease;
}

.tabbar-item.active::after {
  transform: translateX(-50%) scaleX(1);
}
</style> 