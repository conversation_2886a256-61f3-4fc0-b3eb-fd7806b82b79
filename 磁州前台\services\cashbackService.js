/**
 * 返利系统服务
 * 提供电商平台对接、返利计算等功能
 */

import request from '@/utils/request';

const API_BASE = '/api/cashback';

/**
 * 获取返利系统首页数据
 * @returns {Promise} 返利系统首页数据
 */
export function getCashbackHomeData() {
  return request({
    url: '/api/cashback/home',
    method: 'GET'
  });
}

/**
 * 获取平台列表
 * @param {Object} params 查询参数
 * @returns {Promise} 平台列表数据
 */
export function getPlatformList(params) {
  return request({
    url: '/api/cashback/platforms',
    method: 'GET',
    params
  });
}

/**
 * 获取平台详情
 * @param {string} id 平台ID
 * @returns {Promise} 平台详情数据
 */
export function getPlatformDetail(id) {
  return request({
    url: `/api/cashback/platforms/${id}`,
    method: 'GET'
  });
}

/**
 * 获取分类列表
 * @param {Object} params 查询参数
 * @returns {Promise} 分类列表数据
 */
export function getCategoryList(params) {
  return request({
    url: '/api/cashback/categories',
    method: 'GET',
    params
  });
}

/**
 * 获取分类详情
 * @param {string} id 分类ID
 * @returns {Promise} 分类详情数据
 */
export function getCategoryDetail(id) {
  return request({
    url: `/api/cashback/categories/${id}`,
    method: 'GET'
  });
}

/**
 * 获取商品列表
 * @param {Object} params 查询参数
 * @returns {Promise} 商品列表数据
 */
export function getProductList(params) {
  return request({
    url: '/api/cashback/products',
    method: 'GET',
    params
  });
}

/**
 * 获取商品详情
 * @param {string} id 商品ID
 * @returns {Promise} 商品详情数据
 */
export function getProductDetail(id) {
  return request({
    url: `/api/cashback/products/${id}`,
    method: 'GET'
  });
}

/**
 * 获取优惠券列表
 * @param {Object} params 查询参数
 * @returns {Promise} 优惠券列表数据
 */
export function getCouponList(params) {
  return request({
    url: '/api/cashback/coupons',
    method: 'GET',
    params
  });
}

/**
 * 领取优惠券
 * @param {string} id 优惠券ID
 * @returns {Promise} 领取结果
 */
export function claimCoupon(id) {
  return request({
    url: `/api/cashback/coupons/${id}/claim`,
    method: 'POST'
  });
}

/**
 * 获取用户返利记录
 * @param {Object} params 查询参数
 * @returns {Promise} 返利记录数据
 */
export function getCashbackRecords(params) {
  return request({
    url: '/api/cashback/records',
    method: 'GET',
    params
  });
}

/**
 * 获取用户钱包信息
 * @returns {Promise} 钱包信息数据
 */
export function getWalletInfo() {
  return request({
    url: '/api/cashback/wallet',
    method: 'GET'
  });
}

/**
 * 申请提现
 * @param {Object} data 提现数据
 * @returns {Promise} 提现申请结果
 */
export function applyWithdraw(data) {
  return request({
    url: '/api/cashback/withdraw',
    method: 'POST',
    data
  });
}

/**
 * 获取提现记录
 * @param {Object} params 查询参数
 * @returns {Promise} 提现记录数据
 */
export function getWithdrawRecords(params) {
  return request({
    url: '/api/cashback/withdraw/records',
    method: 'GET',
    params
  });
}

/**
 * 获取用户订单列表
 * @param {Object} params 查询参数
 * @returns {Promise} 订单列表数据
 */
export function getOrderList(params) {
  return request({
    url: '/api/cashback/orders',
    method: 'GET',
    params
  });
}

/**
 * 获取订单详情
 * @param {string} id 订单ID
 * @returns {Promise} 订单详情数据
 */
export function getOrderDetail(id) {
  return request({
    url: `/api/cashback/orders/${id}`,
    method: 'GET'
  });
}

/**
 * 收藏商品
 * @param {string} id 商品ID
 * @returns {Promise} 收藏结果
 */
export function favoriteProduct(id) {
  return request({
    url: `/api/cashback/products/${id}/favorite`,
    method: 'POST'
  });
}

/**
 * 取消收藏商品
 * @param {string} id 商品ID
 * @returns {Promise} 取消收藏结果
 */
export function unfavoriteProduct(id) {
  return request({
    url: `/api/cashback/products/${id}/favorite`,
    method: 'DELETE'
  });
}

/**
 * 获取收藏商品列表
 * @param {Object} params 查询参数
 * @returns {Promise} 收藏商品列表数据
 */
export function getFavoriteList(params) {
  return request({
    url: '/api/cashback/favorites',
    method: 'GET',
    params
  });
}

/**
 * 搜索商品
 * @param {Object} params 搜索参数
 * @returns {Promise} 搜索结果数据
 */
export function searchProducts(params) {
  return request({
    url: '/api/cashback/search',
    method: 'GET',
    params
  });
}

/**
 * 获取热门搜索关键词
 * @returns {Promise} 热门搜索关键词数据
 */
export function getHotSearchKeywords() {
  return request({
    url: '/api/cashback/search/hot',
    method: 'GET'
  });
}

/**
 * 获取搜索历史
 * @returns {Promise} 搜索历史数据
 */
export function getSearchHistory() {
  return request({
    url: '/api/cashback/search/history',
    method: 'GET'
  });
}

/**
 * 清除搜索历史
 * @returns {Promise} 清除结果
 */
export function clearSearchHistory() {
  return request({
    url: '/api/cashback/search/history',
    method: 'DELETE'
  });
}

/**
 * 获取活动列表
 * @param {Object} params 查询参数
 * @returns {Promise} 活动列表数据
 */
export function getActivityList(params) {
  return request({
    url: '/api/cashback/activities',
    method: 'GET',
    params
  });
}

/**
 * 获取活动详情
 * @param {string} id 活动ID
 * @returns {Promise} 活动详情数据
 */
export function getActivityDetail(id) {
  return request({
    url: `/api/cashback/activities/${id}`,
    method: 'GET'
  });
}

/**
 * 获取话题列表
 * @param {Object} params 查询参数
 * @returns {Promise} 话题列表数据
 */
export function getTopicList(params) {
  return request({
    url: '/api/cashback/topics',
    method: 'GET',
    params
  });
}

/**
 * 获取话题详情
 * @param {string} id 话题ID
 * @returns {Promise} 话题详情数据
 */
export function getTopicDetail(id) {
  return request({
    url: `/api/cashback/topics/${id}`,
    method: 'GET'
  });
}

/**
 * 提交反馈
 * @param {Object} data 反馈数据
 * @returns {Promise} 提交结果
 */
export function submitFeedback(data) {
  return request({
    url: '/api/cashback/feedback',
    method: 'POST',
    data
  });
}

/**
 * 获取消息列表
 * @param {Object} params 查询参数
 * @returns {Promise} 消息列表数据
 */
export function getMessageList(params) {
  return request({
    url: '/api/cashback/messages',
    method: 'GET',
    params
  });
}

/**
 * 标记消息为已读
 * @param {string} id 消息ID
 * @returns {Promise} 标记结果
 */
export function markMessageRead(id) {
  return request({
    url: `/api/cashback/messages/${id}/read`,
    method: 'PUT'
  });
}

/**
 * 标记所有消息为已读
 * @returns {Promise} 标记结果
 */
export function markAllMessagesRead() {
  return request({
    url: '/api/cashback/messages/read-all',
    method: 'PUT'
  });
}

/**
 * 获取未读消息数量
 * @returns {Promise} 未读消息数量
 */
export function getUnreadMessageCount() {
  return request({
    url: '/api/cashback/messages/unread-count',
    method: 'GET'
  });
}

/**
 * 获取秒杀商品列表
 * @param {Object} params 查询参数
 * @returns {Promise} 秒杀商品列表数据
 */
export function getFlashSaleProducts(params) {
  return request({
    url: '/api/cashback/flash-sale',
    method: 'GET',
    params
  });
}

/**
 * 获取推荐商品列表
 * @param {Object} params 查询参数
 * @returns {Promise} 推荐商品列表数据
 */
export function getRecommendProducts(params) {
  return request({
    url: '/api/cashback/recommend',
    method: 'GET',
    params
  });
}

/**
 * 获取平台优惠券
 * @param {string} platformId 平台ID
 * @returns {Promise} 平台优惠券列表数据
 */
export function getPlatformCoupons(platformId) {
  return request({
    url: `/api/cashback/platforms/${platformId}/coupons`,
    method: 'GET'
  });
}

/**
 * 获取平台商品
 * @param {Object} params 查询参数
 * @returns {Promise} 平台商品列表数据
 */
export function getPlatformProducts(params) {
  return request({
    url: `/api/cashback/platforms/${params.platformId}/products`,
    method: 'GET',
    params
  });
}

/**
 * 绑定平台账号
 * @param {Object} data 绑定数据
 * @returns {Promise} 绑定结果
 */
export function bindPlatform(data) {
  return request({
    url: '/api/cashback/platforms/bind',
    method: 'POST',
    data
  });
}

/**
 * 解绑平台账号
 * @param {string} platformId 平台ID
 * @returns {Promise} 解绑结果
 */
export function unbindPlatform(platformId) {
  return request({
    url: '/api/cashback/platforms/unbind',
    method: 'POST',
    data: { platformId }
  });
}

/**
 * 分享商品
 * @param {string} id 商品ID
 * @returns {Promise} 分享信息
 */
export function shareProduct(id) {
  return request({
    url: `/api/cashback/products/${id}/share`,
    method: 'POST'
  });
}

/**
 * 获取分类商品列表
 * @param {Object} params 查询参数
 * @returns {Promise} 分类商品列表数据
 */
export function getCategoryProducts(params) {
  return request({
    url: `/api/cashback/categories/${params.categoryId}/products`,
    method: 'GET',
    params
  });
}

/**
 * 查询订单状态
 * @param {string} orderId 订单ID
 * @returns {Promise} 订单状态信息
 */
export function checkOrderStatus(orderId) {
  return request({
    url: `/api/cashback/orders/${orderId}/status`,
    method: 'GET'
  });
}

/**
 * 获取已绑定的平台
 * @returns {Promise} 已绑定的平台列表
 */
export function getBoundPlatforms() {
  return request({
    url: '/api/cashback/platforms/bound',
    method: 'GET'
  });
}

/**
 * 同步平台订单
 * @param {string} platformId 平台ID
 * @returns {Promise} 同步结果
 */
export function syncPlatformOrders(platformId) {
  return request({
    url: '/api/cashback/orders/sync',
    method: 'POST',
    data: { platformId }
  });
}

/**
 * 获取分销员等级
 * @returns {Promise} 分销员等级信息
 */
export function getDistributorLevel() {
  return request({
    url: '/api/cashback/distributor/level',
    method: 'GET'
  });
}

/**
 * 申请成为分销员
 * @param {Object} data 申请参数
 * @returns {Promise} 申请结果
 */
export function applyDistributor(data) {
  return request({
    url: '/api/cashback/distributor/apply',
    method: 'POST',
    data
  });
}

/**
 * 获取钱包交易记录
 * @param {Object} params 查询参数
 * @returns {Promise} 钱包交易记录数据
 */
export function getWalletTransactions(params) {
  return request({
    url: '/api/cashback/wallet/transactions',
    method: 'GET',
    params
  });
}

/**
 * 获取提现设置
 * @returns {Promise} 提现设置数据
 */
export function getWithdrawSettings() {
  return request({
    url: '/api/cashback/withdraw/settings',
    method: 'GET'
  });
}

/**
 * 获取银行卡列表
 * @returns {Promise} 银行卡列表数据
 */
export function getBankCards() {
  return request({
    url: '/api/cashback/bank-cards',
    method: 'GET'
  });
}

/**
 * 提现到银行卡/微信/支付宝
 * @param {Object} data 提现数据
 * @returns {Promise} 提现结果
 */
export function withdrawCashback(data) {
  return request({
    url: '/api/cashback/withdraw',
    method: 'POST',
    data
  });
}

/**
 * 获取返利统计信息
 * @returns {Promise} 返利统计数据
 */
export function getCashbackStats() {
  return request({
    url: '/api/cashback/stats',
    method: 'GET'
  });
}

/**
 * 获取收藏商品列表 (别名: getFavoriteList)
 * @param {Object} params 查询参数
 * @returns {Promise} 收藏商品列表数据
 */
export function getFavoriteProducts(params) {
  return getFavoriteList(params);
} 