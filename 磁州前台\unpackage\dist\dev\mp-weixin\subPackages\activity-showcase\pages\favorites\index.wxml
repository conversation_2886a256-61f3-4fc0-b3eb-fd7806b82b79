<view class="favorites-container data-v-5e0c0445"><view class="custom-navbar data-v-5e0c0445" style="{{'position:' + 'fixed' + ';' + ('top:' + 0) + ';' + ('left:' + 0) + ';' + ('right:' + 0) + ';' + ('height:' + 'calc(var(--status-bar-height, 25px) + 62px)') + ';' + ('width:' + '100%') + ';' + ('z-index:' + 100)}}"><view class="navbar-bg data-v-5e0c0445" style="{{'position:' + 'absolute' + ';' + ('top:' + 0) + ';' + ('left:' + 0) + ';' + ('width:' + '100%') + ';' + ('height:' + '100%') + ';' + ('background:' + 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)') + ';' + ('box-shadow:' + '0 2px 10px rgba(0,0,0,0.05)')}}"></view><view class="navbar-content data-v-5e0c0445" style="{{'position:' + 'relative' + ';' + ('display:' + 'flex') + ';' + ('align-items:' + 'center') + ';' + ('justify-content:' + 'space-between') + ';' + ('height:' + '100%') + ';' + ('padding:' + '0 30rpx') + ';' + ('padding-top:' + 'var(--status-bar-height, 25px)') + ';' + ('box-sizing:' + 'border-box')}}"><view class="back-btn data-v-5e0c0445" bindtap="{{b}}" style="{{'width:' + '80rpx' + ';' + ('height:' + '80rpx') + ';' + ('display:' + 'flex') + ';' + ('align-items:' + 'center') + ';' + ('justify-content:' + 'center')}}"><image class="data-v-5e0c0445" src="{{a}}" mode="aspectFit" style="{{'width:' + '40rpx' + ';' + ('height:' + '40rpx')}}"></image></view><text class="navbar-title data-v-5e0c0445" style="{{'font-size:' + '36rpx' + ';' + ('font-weight:' + '600') + ';' + ('color:' + '#FFFFFF')}}">我的收藏</text><view class="data-v-5e0c0445" style="width:80rpx"></view></view></view><view class="content-area data-v-5e0c0445" style="{{'padding-top:' + 'calc(var(--status-bar-height, 25px) + 62px)' + ';' + ('padding-bottom:' + q) + ';' + ('min-height:' + '100vh') + ';' + ('box-sizing:' + 'border-box')}}"><view class="data-v-5e0c0445" style="{{'display:' + 'flex' + ';' + ('align-items:' + 'center') + ';' + ('justify-content:' + 'space-between') + ';' + ('padding:' + '20rpx 30rpx') + ';' + ('background:' + '#FFFFFF') + ';' + ('margin-bottom:' + '20rpx') + ';' + ('box-shadow:' + '0 2px 10px rgba(0,0,0,0.05)')}}"><scroll-view scroll-x class="category-tabs data-v-5e0c0445" style="{{'white-space:' + 'nowrap' + ';' + ('flex:' + 1) + ';' + ('overflow:' + 'hidden')}}"><view wx:for="{{c}}" wx:for-item="category" wx:key="b" class="{{['category-item', 'data-v-5e0c0445', category.c && 'active']}}" bindtap="{{category.d}}" style="{{'display:' + 'inline-block' + ';' + ('padding:' + '10rpx 30rpx') + ';' + ('margin-right:' + '20rpx') + ';' + ('border-radius:' + '30rpx') + ';' + ('font-size:' + '28rpx') + ';' + ('color:' + category.e) + ';' + ('background:' + category.f) + ';' + ('box-shadow:' + category.g)}}">{{category.a}}</view></scroll-view><view class="edit-btn data-v-5e0c0445" bindtap="{{e}}" style="{{'margin-left:' + '20rpx' + ';' + ('padding:' + '10rpx 30rpx') + ';' + ('border-radius:' + '30rpx') + ';' + ('font-size:' + '28rpx') + ';' + ('font-weight:' + '500') + ';' + ('background:' + 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)') + ';' + ('color:' + '#FFFFFF') + ';' + ('box-shadow:' + '0 4px 10px rgba(255,59,105,0.2)')}}">{{d}}</view></view><view class="favorites-list data-v-5e0c0445" style="{{'padding:' + '0 20rpx'}}"><view wx:for="{{f}}" wx:for-item="item" wx:key="w" class="favorite-item data-v-5e0c0445" style="{{'background:' + '#FFFFFF' + ';' + ('border-radius:' + '20rpx') + ';' + ('margin-bottom:' + '20rpx') + ';' + ('position:' + 'relative') + ';' + ('overflow:' + 'hidden') + ';' + ('box-shadow:' + '0 4px 10px rgba(0,0,0,0.05)')}}"><view wx:if="{{g}}" class="select-box data-v-5e0c0445" catchtap="{{item.f}}" style="{{'position:' + 'absolute' + ';' + ('top:' + '20rpx') + ';' + ('left:' + '20rpx') + ';' + ('width:' + '40rpx') + ';' + ('height:' + '40rpx') + ';' + ('border-radius:' + '50%') + ';' + ('border:' + item.g) + ';' + ('background:' + item.h) + ';' + ('display:' + 'flex') + ';' + ('align-items:' + 'center') + ';' + ('justify-content:' + 'center') + ';' + ('z-index:' + '10')}}"><svg wx:if="{{item.a}}" u-s="{{['d']}}" class="icon data-v-5e0c0445" u-i="{{item.d}}" bind:__l="__l" u-p="{{item.e}}"><path wx:if="{{item.c}}" class="data-v-5e0c0445" u-i="{{item.b}}" bind:__l="__l" u-p="{{item.c}}"></path></svg></view><view class="item-content data-v-5e0c0445" bindtap="{{item.q}}" style="{{'display:' + 'flex' + ';' + ('padding:' + '20rpx')}}"><image class="data-v-5e0c0445" src="{{item.i}}" mode="aspectFill" style="{{'width:' + '200rpx' + ';' + ('height:' + '200rpx') + ';' + ('border-radius:' + '10rpx') + ';' + ('margin-right:' + '20rpx')}}"></image><view class="item-info data-v-5e0c0445" style="{{'flex:' + '1' + ';' + ('display:' + 'flex') + ';' + ('flex-direction:' + 'column') + ';' + ('justify-content:' + 'space-between')}}"><view class="item-top data-v-5e0c0445"><text class="item-title data-v-5e0c0445" style="{{'font-size:' + '28rpx' + ';' + ('font-weight:' + '500') + ';' + ('color:' + '#333333') + ';' + ('margin-bottom:' + '10rpx') + ';' + ('display:' + 'block') + ';' + ('overflow:' + 'hidden') + ';' + ('text-overflow:' + 'ellipsis') + ';' + ('display:' + '-webkit-box') + ';' + ('-webkit-line-clamp:' + '2') + ';' + ('-webkit-box-orient:' + 'vertical') + ';' + ('line-height:' + '1.4')}}">{{item.j}}</text><text class="item-shop data-v-5e0c0445" style="{{'font-size:' + '24rpx' + ';' + ('color:' + '#999999') + ';' + ('margin-bottom:' + '10rpx') + ';' + ('display:' + 'block')}}">{{item.k}}</text></view><view class="item-bottom data-v-5e0c0445" style="{{'display:' + 'flex' + ';' + ('justify-content:' + 'space-between') + ';' + ('align-items:' + 'flex-end')}}"><view class="price-info data-v-5e0c0445"><view class="current-price data-v-5e0c0445" style="{{'display:' + 'flex' + ';' + ('align-items:' + 'baseline')}}"><text class="data-v-5e0c0445" style="{{'font-size:' + '24rpx' + ';' + ('color:' + '#FF3B69') + ';' + ('margin-right:' + '5rpx')}}">¥</text><text class="data-v-5e0c0445" style="{{'font-size:' + '32rpx' + ';' + ('font-weight:' + '600') + ';' + ('color:' + '#FF3B69')}}">{{item.l}}</text></view><text wx:if="{{item.m}}" class="original-price data-v-5e0c0445" style="{{'font-size:' + '24rpx' + ';' + ('color:' + '#999999') + ';' + ('text-decoration:' + 'line-through')}}">¥{{item.n}}</text></view><view class="item-tag data-v-5e0c0445" style="{{'padding:' + '6rpx 16rpx' + ';' + ('border-radius:' + '20rpx') + ';' + ('background:' + item.p) + ';' + ('color:' + '#FFFFFF') + ';' + ('font-size:' + '22rpx') + ';' + ('font-weight:' + '500')}}">{{item.o}}</view></view></view></view><view class="favorite-time data-v-5e0c0445" style="{{'padding:' + '10rpx 20rpx' + ';' + ('border-top:' + '1rpx solid #F2F2F7') + ';' + ('display:' + 'flex') + ';' + ('justify-content:' + 'space-between') + ';' + ('align-items:' + 'center')}}"><text class="data-v-5e0c0445" style="{{'font-size:' + '24rpx' + ';' + ('color:' + '#999999')}}">收藏于 {{item.r}}</text><view class="action-btn data-v-5e0c0445" catchtap="{{item.v}}" style="{{'display:' + 'flex' + ';' + ('align-items:' + 'center')}}"><svg wx:if="{{i}}" u-s="{{['d']}}" class="icon data-v-5e0c0445" style="{{'margin-right:' + '5rpx'}}" u-i="{{item.t}}" bind:__l="__l" u-p="{{i}}"><path wx:if="{{h}}" class="data-v-5e0c0445" u-i="{{item.s}}" bind:__l="__l" u-p="{{h}}"></path></svg><text class="data-v-5e0c0445" style="{{'font-size:' + '24rpx' + ';' + ('color:' + '#FF3B69')}}">取消收藏</text></view></view></view><view wx:if="{{j}}" class="empty-state data-v-5e0c0445" style="{{'padding:' + '100rpx 0' + ';' + ('display:' + 'flex') + ';' + ('flex-direction:' + 'column') + ';' + ('align-items:' + 'center')}}"><image class="data-v-5e0c0445" src="{{k}}" mode="aspectFit" style="{{'width:' + '200rpx' + ';' + ('height:' + '200rpx') + ';' + ('margin-bottom:' + '20rpx')}}"></image><text class="data-v-5e0c0445" style="{{'font-size:' + '28rpx' + ';' + ('color:' + '#999999') + ';' + ('margin-bottom:' + '30rpx')}}">暂无收藏内容</text><view class="action-btn data-v-5e0c0445" bindtap="{{l}}" style="{{'padding:' + '16rpx 40rpx' + ';' + ('border-radius:' + '35px') + ';' + ('background:' + 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)') + ';' + ('color:' + '#FFFFFF') + ';' + ('font-size:' + '28rpx') + ';' + ('box-shadow:' + '0 5px 15px rgba(255,59,105,0.3)')}}"> 去探索 </view></view><view wx:if="{{m}}" class="load-more data-v-5e0c0445" style="{{'text-align:' + 'center' + ';' + ('padding:' + '30rpx 0')}}"><text wx:if="{{n}}" class="data-v-5e0c0445" style="{{'font-size:' + '26rpx' + ';' + ('color:' + '#999999')}}">加载中...</text><text wx:else class="data-v-5e0c0445" bindtap="{{o}}" style="{{'font-size:' + '26rpx' + ';' + ('color:' + '#FF3B69')}}">加载更多</text></view><view wx:if="{{p}}" class="no-more data-v-5e0c0445" style="{{'text-align:' + 'center' + ';' + ('padding:' + '30rpx 0')}}"><text class="data-v-5e0c0445" style="{{'font-size:' + '26rpx' + ';' + ('color:' + '#999999')}}">没有更多收藏了</text></view></view></view><view wx:if="{{r}}" class="bottom-action-bar data-v-5e0c0445" style="{{'position:' + 'fixed' + ';' + ('bottom:' + 0) + ';' + ('left:' + 0) + ';' + ('right:' + 0) + ';' + ('height:' + '120rpx') + ';' + ('background:' + '#FFFFFF') + ';' + ('box-shadow:' + '0 -2px 10px rgba(0,0,0,0.05)') + ';' + ('display:' + 'flex') + ';' + ('align-items:' + 'center') + ';' + ('justify-content:' + 'space-between') + ';' + ('padding:' + '0 30rpx') + ';' + ('padding-bottom:' + 'env(safe-area-inset-bottom)') + ';' + ('z-index:' + 100)}}"><view class="select-all-btn data-v-5e0c0445" bindtap="{{y}}" style="{{'display:' + 'flex' + ';' + ('align-items:' + 'center')}}"><view class="select-box data-v-5e0c0445" style="{{'width:' + '40rpx' + ';' + ('height:' + '40rpx') + ';' + ('border-radius:' + '50%') + ';' + ('border:' + w) + ';' + ('background:' + x) + ';' + ('display:' + 'flex') + ';' + ('align-items:' + 'center') + ';' + ('justify-content:' + 'center') + ';' + ('margin-right:' + '10rpx')}}"><svg wx:if="{{s}}" u-s="{{['d']}}" class="icon data-v-5e0c0445" u-i="5e0c0445-4" bind:__l="__l" u-p="{{v}}"><path wx:if="{{t}}" class="data-v-5e0c0445" u-i="5e0c0445-5,5e0c0445-4" bind:__l="__l" u-p="{{t}}"></path></svg></view><text class="data-v-5e0c0445" style="{{'font-size:' + '28rpx' + ';' + ('color:' + '#333333')}}">全选</text></view><view class="delete-btn data-v-5e0c0445" bindtap="{{A}}" style="{{'padding:' + '16rpx 40rpx' + ';' + ('border-radius:' + '35px') + ';' + ('background:' + B) + ';' + ('color:' + '#FFFFFF') + ';' + ('font-size:' + '28rpx') + ';' + ('box-shadow:' + C)}}"> 删除({{z}}) </view></view></view>