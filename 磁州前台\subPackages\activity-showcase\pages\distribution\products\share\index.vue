<template>
  <view class="share-page">
    <view class="nav-bar">
      <view class="nav-back" @click="goBack">
        <text class="iconfont icon-arrow-left"></text>
      </view>
      <view class="nav-title">分享商品</view>
    </view>

    <!-- 分享方式 -->
    <view class="share-methods">
      <view class="method-item" @click="shareToWeChat">
        <view class="method-icon wechat">
          <text class="iconfont icon-wechat"></text>
        </view>
        <text>微信好友</text>
      </view>
      <view class="method-item" @click="shareToMoments">
        <view class="method-icon moments">
          <text class="iconfont icon-moments"></text>
        </view>
        <text>朋友圈</text>
      </view>
      <view class="method-item" @click="copyLink">
        <view class="method-icon copy">
          <text class="iconfont icon-copy"></text>
        </view>
        <text>复制链接</text>
      </view>
      <view class="method-item" @click="generateQRCode">
        <view class="method-icon qrcode">
          <text class="iconfont icon-qrcode"></text>
        </view>
        <text>二维码</text>
      </view>
    </view>

    <!-- 推广链接 -->
    <view class="promotion-link">
      <view class="section-title">推广链接</view>
      <view class="link-container">
        <input v-model="promotionUrl" readonly class="link-input" />
        <button @click="copyLink" class="copy-btn">复制</button>
      </view>
    </view>

    <!-- 分享统计 -->
    <view class="share-stats">
      <view class="section-title">分享统计</view>
      <view class="stats-grid">
        <view class="stat-item">
          <view class="stat-number">{{ shareData.views }}</view>
          <view class="stat-label">浏览量</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{ shareData.shares }}</view>
          <view class="stat-label">分享次数</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{ shareData.orders }}</view>
          <view class="stat-label">成交订单</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">¥{{ shareData.earnings }}</view>
          <view class="stat-label">累计收益</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const promotionUrl = ref('')
const shareData = ref({
  views: 0,
  shares: 0,
  orders: 0,
  earnings: '0.00'
})

const shareToWeChat = () => {
  // 分享到微信好友
}

const copyLink = () => {
  uni.setClipboardData({
    data: promotionUrl.value,
    success: () => {
      uni.showToast({ title: '链接已复制' })
    }
  })
}
</script>

<style lang="scss" scoped>
.share-methods {
  display: flex;
  justify-content: space-around;
  background: #fff;
  padding: 40rpx 20rpx;
  margin-bottom: 20rpx;
  
  .method-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .method-icon {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 10rpx;
      
      &.wechat { background: #07c160; color: #fff; }
      &.moments { background: #ff6b6b; color: #fff; }
      &.copy { background: #409eff; color: #fff; }
      &.qrcode { background: #909399; color: #fff; }
    }
    
    text {
      font-size: 24rpx;
      color: #666;
    }
  }
}

.promotion-link {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .link-container {
    display: flex;
    align-items: center;
    margin-top: 20rpx;
    
    .link-input {
      flex: 1;
      background: #f5f5f5;
      padding: 20rpx;
      border-radius: 8rpx;
      margin-right: 20rpx;
    }
    
    .copy-btn {
      background: #ff6b6b;
      color: #fff;
      padding: 20rpx 30rpx;
      border-radius: 8rpx;
    }
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-top: 20rpx;
  
  .stat-item {
    text-align: center;
    padding: 30rpx;
    background: #f8f9fa;
    border-radius: 12rpx;
    
    .stat-number {
      font-size: 36rpx;
      font-weight: bold;
      color: #ff6b6b;
      margin-bottom: 10rpx;
    }
    
    .stat-label {
      font-size: 24rpx;
      color: #666;
    }
  }
}
</style>