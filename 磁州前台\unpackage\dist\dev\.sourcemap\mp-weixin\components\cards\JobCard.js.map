{"version": 3, "file": "JobCard.js", "sources": ["components/cards/JobCard.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7AvY29tcG9uZW50cy9jYXJkcy9Kb2JDYXJkLnZ1ZQ"], "sourcesContent": ["<template>\n  <BaseInfoCard :item=\"item\">\n    <template #content>\n      <view class=\"job-details\">\n        <!-- 公司信息 -->\n        <view class=\"company-info\" v-if=\"item.company\">\n          <view class=\"company-logo\" v-if=\"item.companyLogo\">\n            <image :src=\"item.companyLogo\" class=\"company-logo-img\" mode=\"aspectFill\"></image>\n          </view>\n          <view class=\"company-data\">\n            <view class=\"company-name\">{{item.company}}</view>\n            <view class=\"company-meta\">\n              <text class=\"company-size\" v-if=\"item.companySize\">{{item.companySize}}</text>\n              <text class=\"company-type\" v-if=\"item.companyType\">{{item.companyType}}</text>\n            </view>\n          </view>\n          <view class=\"company-auth\" v-if=\"item.isVerified\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#007AFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <path d=\"M22 11.08V12a10 10 0 1 1-5.93-9.14\"></path>\n              <polyline points=\"22 4 12 14.01 9 11.01\"></polyline>\n            </svg>\n            <text class=\"auth-text\">已认证</text>\n          </view>\n        </view>\n        \n        <!-- 职位要求 -->\n        <view class=\"job-requirements\">\n          <view class=\"req-item\" v-if=\"item.education\">\n            <view class=\"req-icon education-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <path d=\"M22 10v6M2 10l10-5 10 5-10 5z\"></path>\n                <path d=\"M6 12v5c3 3 9 3 12 0v-5\"></path>\n              </svg>\n            </view>\n            <text class=\"req-text\">{{item.education}}</text>\n          </view>\n          \n          <view class=\"req-item\" v-if=\"item.experience\">\n            <view class=\"req-icon experience-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <rect x=\"2\" y=\"7\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\"></rect>\n                <path d=\"M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16\"></path>\n              </svg>\n            </view>\n            <text class=\"req-text\">{{item.experience}}</text>\n          </view>\n          \n          <view class=\"req-item\" v-if=\"item.jobType\">\n            <view class=\"req-icon jobtype-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n                <polyline points=\"12 6 12 12 16 14\"></polyline>\n              </svg>\n            </view>\n            <text class=\"req-text\">{{item.jobType}}</text>\n          </view>\n        </view>\n        \n        <!-- 福利标签 - 过滤掉与tags重复的项 -->\n        <view class=\"job-benefits\" v-if=\"filteredBenefits.length\">\n          <view class=\"benefit-tag\" v-for=\"(benefit, index) in filteredBenefits\" :key=\"index\">\n            <text class=\"benefit-text\">{{benefit}}</text>\n          </view>\n        </view>\n      </view>\n    </template>\n  </BaseInfoCard>\n</template>\n\n<script setup>\nimport { computed } from 'vue';\nimport BaseInfoCard from './BaseInfoCard.vue';\n\nconst props = defineProps({\n  item: {\n    type: Object,\n    required: true\n  }\n});\n\n// 过滤掉与tags重复的福利项\nconst filteredBenefits = computed(() => {\n  if (!props.item.benefits || !props.item.benefits.length) return [];\n  if (!props.item.tags || !props.item.tags.length) return props.item.benefits;\n  \n  // 将tags转换为小写，用于不区分大小写的比较\n  const tagsLowerCase = props.item.tags.map(tag => tag.toLowerCase());\n  \n  // 过滤benefits，去除与tags重复的项\n  return props.item.benefits.filter(benefit => \n    !tagsLowerCase.includes(benefit.toLowerCase())\n  );\n});\n</script>\n\n<style scoped>\n.job-details {\n  margin-top: 16rpx;\n  padding-top: 16rpx;\n  border-top: 1rpx solid rgba(60, 60, 67, 0.1);\n}\n\n/* 公司信息 */\n.company-info {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16rpx;\n  background: rgba(0, 0, 0, 0.02);\n  padding: 16rpx;\n  border-radius: 16rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n}\n\n.company-logo {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 12rpx;\n  overflow: hidden;\n  margin-right: 16rpx;\n  background: #fff;\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);\n}\n\n.company-logo-img {\n  width: 100%;\n  height: 100%;\n}\n\n.company-data {\n  flex: 1;\n}\n\n.company-name {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #1c1c1e;\n  margin-bottom: 6rpx;\n}\n\n.company-meta {\n  display: flex;\n  align-items: center;\n  gap: 16rpx;\n}\n\n.company-size, .company-type {\n  font-size: 22rpx;\n  color: #8e8e93;\n}\n\n.company-auth {\n  display: flex;\n  align-items: center;\n  background: rgba(0, 122, 255, 0.1);\n  padding: 4rpx 12rpx;\n  border-radius: 16rpx;\n}\n\n.auth-text {\n  font-size: 22rpx;\n  color: #007AFF;\n  margin-left: 4rpx;\n}\n\n/* 职位要求 */\n.job-requirements {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16rpx;\n  margin-bottom: 16rpx;\n}\n\n.req-item {\n  display: flex;\n  align-items: center;\n  background: rgba(0, 0, 0, 0.02);\n  padding: 8rpx 16rpx;\n  border-radius: 24rpx;\n}\n\n.req-icon {\n  margin-right: 8rpx;\n  color: #8e8e93;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.education-icon {\n  color: #5856D6;\n}\n\n.experience-icon {\n  color: #FF9500;\n}\n\n.jobtype-icon {\n  color: #34C759;\n}\n\n.req-text {\n  font-size: 24rpx;\n  color: #636366;\n}\n\n/* 福利标签 */\n.job-benefits {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12rpx;\n}\n\n.benefit-tag {\n  background: linear-gradient(135deg, rgba(0, 122, 255, 0.1), rgba(88, 86, 214, 0.1));\n  border-radius: 16rpx;\n  padding: 6rpx 16rpx;\n  border: 1rpx solid rgba(0, 122, 255, 0.2);\n}\n\n.benefit-text {\n  font-size: 22rpx;\n  color: #007AFF;\n  font-weight: 500;\n}\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/components/cards/JobCard.vue'\nwx.createComponent(Component)"], "names": ["computed"], "mappings": ";;;;;;;;;;;;;AAuEA,MAAM,eAAe,MAAW;;;;;;;;;;AAEhC,UAAM,QAAQ;AAQd,UAAM,mBAAmBA,cAAQ,SAAC,MAAM;AACtC,UAAI,CAAC,MAAM,KAAK,YAAY,CAAC,MAAM,KAAK,SAAS;AAAQ,eAAO;AAChE,UAAI,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM,KAAK,KAAK;AAAQ,eAAO,MAAM,KAAK;AAGnE,YAAM,gBAAgB,MAAM,KAAK,KAAK,IAAI,SAAO,IAAI,YAAW,CAAE;AAGlE,aAAO,MAAM,KAAK,SAAS;AAAA,QAAO,aAChC,CAAC,cAAc,SAAS,QAAQ,YAAW,CAAE;AAAA,MACjD;AAAA,IACA,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3FD,GAAG,gBAAgB,SAAS;"}