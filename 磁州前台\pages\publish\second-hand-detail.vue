<template>
  <view class="second-hand-detail-container">
    <!-- 添加自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">二手闲置详情</view>
      <view class="navbar-right">
        <!-- 占位 -->
      </view>
    </view>
    
    <!-- 隐藏的分享按钮，用于自动触发 -->
    <button id="shareButton" class="hidden-share-btn" open-type="share"></button>
    
    <view class="second-hand-detail-wrapper">
      <!-- 商品基本信息卡片 -->
      <view class="content-card goods-info-card">
        <view class="goods-header">
          <view class="goods-title-row">
            <text class="goods-title">{{goodsData.title}}</text>
            <text class="goods-price">{{goodsData.price}}</text>
          </view>
          <view class="goods-meta">
            <view class="goods-tag-group">
              <view class="goods-tag" v-for="(tag, index) in goodsData.tags" :key="index">{{tag}}</view>
            </view>
            <text class="goods-publish-time">发布于 {{formatTime(goodsData.publishTime)}}</text>
          </view>
        </view>
        
        <!-- 商品图片轮播 -->
        <swiper class="goods-swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500">
          <swiper-item v-for="(image, index) in goodsData.images" :key="index">
            <image :src="image" mode="aspectFill" class="goods-image"></image>
          </swiper-item>
        </swiper>
        
        <!-- 基本信息 -->
        <view class="goods-basic-info">
          <view class="info-item">
            <text class="info-label">商品类型</text>
            <text class="info-value">{{goodsData.type}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">新旧程度</text>
            <text class="info-value">{{goodsData.condition}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">购买时间</text>
            <text class="info-value">{{goodsData.purchaseTime}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">交易地点</text>
            <text class="info-value">{{goodsData.location}}</text>
          </view>
        </view>
      </view>
      
      <!-- 商品描述 -->
      <view class="content-card description-card">
        <view class="section-title">商品描述</view>
        <view class="description-content">
          <text class="description-text">{{goodsData.description}}</text>
        </view>
      </view>
      
      <!-- 商品详情 -->
      <view class="content-card details-card">
        <view class="section-title">商品详情</view>
        <view class="details-list">
          <view class="details-item" v-for="(item, index) in goodsData.details" :key="index">
            <text class="details-label">{{item.label}}</text>
            <text class="details-value">{{item.value}}</text>
          </view>
        </view>
      </view>
      
      <!-- 交易方式 -->
      <view class="content-card trade-card">
        <view class="section-title">交易方式</view>
        <view class="trade-list">
          <view class="trade-item" v-for="(item, index) in goodsData.tradeMethods" :key="index">
            <text class="trade-icon iconfont" :class="item.icon"></text>
            <view class="trade-info">
              <text class="trade-title">{{item.title}}</text>
              <text class="trade-desc">{{item.description}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 卖家信息 -->
      <view class="content-card seller-card">
        <view class="seller-header">
          <view class="seller-avatar">
            <image :src="goodsData.seller.avatar" mode="aspectFill"></image>
          </view>
          <view class="seller-info">
            <text class="seller-name">{{goodsData.seller.name}}</text>
            <view class="seller-meta">
              <text class="seller-type">{{goodsData.seller.type}}</text>
              <text class="seller-rating">信用等级 {{goodsData.seller.rating}}</text>
            </view>
          </view>
          <view class="seller-auth" v-if="goodsData.seller.isVerified">
            <text class="iconfont icon-verified"></text>
            <text class="auth-text">已认证</text>
          </view>
        </view>
      </view>
      
      <!-- 联系方式 -->
      <view class="content-card contact-card">
        <view class="contact-header">
          <text class="card-title">联系方式</text>
        </view>
        <view class="contact-content">
          <view class="contact-item">
            <text class="contact-label">联系人</text>
            <text class="contact-value">{{goodsData.contact.name}}</text>
          </view>
          <view class="contact-item">
            <text class="contact-label">电话</text>
            <text class="contact-value contact-phone" @click="callPhone">{{goodsData.contact.phone}}</text>
          </view>
          <view class="contact-tips">
            <text class="tips-icon iconfont icon-info"></text>
            <text class="tips-text">请说明在"磁州生活网"看到的信息</text>
          </view>
        </view>
      </view>
      
      <!-- 举报卡片 -->
      <report-card></report-card>
      
      <!-- 相关物品推荐 -->
      <view class="content-card related-goods-card">
        <view class="section-title">相关物品推荐</view>
        <view class="related-goods-content">
          <!-- 简洁的物品列表 -->
          <view class="related-goods-list">
            <view class="related-goods-item" 
                 v-for="(item, index) in relatedGoods.slice(0, 3)" 
                 :key="index" 
                 @click="navigateToGoodsDetail(item.id)">
              <view class="goods-item-content">
                <view class="goods-item-left">
                  <image class="goods-image" :src="item.image" mode="aspectFill"></image>
                </view>
                <view class="goods-item-middle">
                  <text class="goods-item-title">{{item.title}}</text>
                  <view class="goods-item-condition">{{item.condition}}</view>
                  <view class="goods-item-tags">
                    <text class="goods-item-tag" v-for="(tag, tagIndex) in item.tags.slice(0, 2)" :key="tagIndex">{{tag}}</text>
                    <text class="goods-item-tag-more" v-if="item.tags.length > 2">+{{item.tags.length - 2}}</text>
                  </view>
                </view>
                <view class="goods-item-right">
                  <text class="goods-item-price">{{item.price}}</text>
                </view>
              </view>
            </view>
            
            <!-- 暂无数据提示 -->
            <view class="empty-related-goods" v-if="relatedGoods.length === 0">
              <image src="/static/images/empty.png" class="empty-image" mode="aspectFit"></image>
              <text class="empty-text">暂无相关物品</text>
            </view>
          </view>
          
          <!-- 查看更多按钮 -->
          <view class="view-more-btn" v-if="relatedGoods.length > 0" @click.stop="navigateToGoodsList">
            <text class="view-more-text">查看更多二手物品</text>
            <text class="view-more-icon iconfont icon-right"></text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="interaction-toolbar">
      <view class="toolbar-item" @click="goToHome">
        <image src="/static/images/tabbar/a首页.png" class="toolbar-icon"></image>
        <text class="toolbar-text">首页</text>
      </view>
      <view class="toolbar-item" @click="toggleCollect">
        <image src="/static/images/tabbar/a收藏.png" class="toolbar-icon"></image>
        <text class="toolbar-text">收藏</text>
      </view>
      <button class="share-button toolbar-item" open-type="share">
        <image src="/static/images/tabbar/a分享.png" class="toolbar-icon"></image>
        <text class="toolbar-text">分享</text>
      </button>
      <view class="toolbar-item" @click="openChat">
        <image src="/static/images/tabbar/a消息.png" class="toolbar-icon"></image>
        <text class="toolbar-text">私信</text>
      </view>
      <view class="toolbar-item call-button" @click="callPhone">
        <view class="call-button-content">
          <text class="call-text">打电话</text>
          <text class="call-subtitle">请说在磁州生活网看到的</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import ReportCard from '@/components/ReportCard.vue'

// 状态栏高度
const statusBarHeight = ref(20);

// 获取状态栏高度
onMounted(() => {
  try {
    const sysInfo = uni.getSystemInfoSync();
    statusBarHeight.value = sysInfo.statusBarHeight || 20;
  } catch (e) {
    console.error('获取状态栏高度失败', e);
  }
  
  // 获取路由参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};
  
  // 获取商品ID
  const id = options.id || '';
  console.log('商品详情页ID:', id);
  
  // 加载相关物品推荐
  loadRelatedGoods();
});

// 返回上一页
const goBack = () => {
  uni.navigateBack({
    fail: () => {
      uni.switchTab({
        url: '/pages/index/index'
      });
    }
  });
};

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  return `${date.getMonth() + 1}月${date.getDate()}日`;
};

// 响应式数据
const isCollected = ref(false);
const goodsData = ref({
  id: 'goods12345',
  title: 'iPhone 13 Pro Max',
  price: '5999元',
  tags: ['95新', '无维修', '可面交'],
  publishTime: Date.now() - 86400000 * 2, // 2天前
  images: [
    '/static/images/goods1.jpg',
    '/static/images/goods2.jpg',
    '/static/images/goods3.jpg'
  ],
  type: '手机数码',
  condition: '95新',
  purchaseTime: '2023年1月',
  location: '磁县城区',
  description: 'iPhone 13 Pro Max 256G 远峰蓝，2023年1月购买，无维修无进水，配件齐全，可面交验机。',
  details: [
    { label: '品牌型号', value: 'iPhone 13 Pro Max' },
    { label: '内存容量', value: '256G' },
    { label: '颜色', value: '远峰蓝' },
    { label: '配件', value: '原装充电器、数据线、包装盒' },
    { label: '保修', value: '已过保' }
  ],
  tradeMethods: [
    {
      icon: 'icon-face',
      title: '当面交易',
      description: '支持当面验机，确认无误后交易'
    },
    {
      icon: 'icon-delivery',
      title: '快递交易',
      description: '支持快递发货，收到货后确认'
    }
  ],
  seller: {
    name: '张先生',
    avatar: '/static/images/avatar.png',
    type: '个人',
    rating: 'A+',
    isVerified: true
  },
  contact: {
    name: '张先生',
    phone: '13912345678'
  }
});

// 相关物品推荐数据
const relatedGoods = ref([]);

// 加载相关物品推荐
const loadRelatedGoods = () => {
  // 这里应该调用API获取数据
  // 实际项目中应该根据当前物品的类型、标签等进行相关性匹配
  
  // 模拟数据
  setTimeout(() => {
    relatedGoods.value = [
      {
        id: 'goods001',
        title: 'iPhone 12 Pro 128G',
        condition: '95新',
        price: '4599元',
        image: '/static/images/goods1.jpg',
        tags: ['无维修', '面交验机', '保修中']
      },
      {
        id: 'goods002',
        title: 'iPhone 14 256G',
        condition: '99新',
        price: '6999元',
        image: '/static/images/goods2.jpg',
        tags: ['官换机', '发票齐全']
      },
      {
        id: 'goods003',
        title: 'MacBook Pro 2022',
        condition: '9成新',
        price: '9800元',
        image: '/static/images/goods3.jpg',
        tags: ['M2芯片', '16G内存', '官方保修']
      }
    ];
  }, 500);
};

// 跳转到商品详情页
const navigateToGoodsDetail = (goodsId) => {
  // 防止跳转到当前页面
  if (goodsId === goodsData.value.id) {
    return;
  }
  
  uni.navigateTo({
    url: `/pages/publish/second-hand-detail?id=${goodsId}`
  });
};

// 跳转到二手闲置列表页
const navigateToGoodsList = (e) => {
  if (e) e.stopPropagation();
  const goodsCategory = goodsData.value.type || '';
  uni.navigateTo({
    url: `/subPackages/service/pages/filter?type=second-hand&title=${encodeURIComponent('二手闲置')}&category=${encodeURIComponent(goodsCategory)}&active=second_hand`
  });
};

// 方法
const toggleCollect = () => {
  isCollected.value = !isCollected.value;
  if (isCollected.value) {
    uni.showToast({
      title: '收藏成功',
      icon: 'success'
    });
  }
};

const showShareOptions = () => {
  uni.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage', 'shareTimeline']
  });
};

const callPhone = () => {
  uni.makePhoneCall({
    phoneNumber: goodsData.value.contact.phone,
    fail: () => {
      uni.showToast({
        title: '拨打电话失败',
        icon: 'none'
      });
    }
  });
};

// 跳转到首页
const goToHome = () => {
  uni.switchTab({
    url: '/pages/index/index'
  });
};

// 打开私信聊天
const openChat = () => {
  if (!goodsData.value.seller || !goodsData.value.seller.id) {
    uni.showToast({
      title: '无法获取卖家信息',
      icon: 'none'
    });
    return;
  }
  
  // 跳转到聊天页面
  uni.navigateTo({
    url: `/pages/chat/index?userId=${goodsData.value.seller.id}&username=${encodeURIComponent(goodsData.value.seller.name || '卖家')}`
  });
};
</script>

<style>
.second-hand-detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 150rpx;
  padding-top: 0; /* 移除顶部内边距，由导航栏控制 */
}

.second-hand-detail-wrapper {
  padding: 24rpx;
  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */
}

.content-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 商品基本信息卡片 */
.goods-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.goods-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.goods-price {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff4d4f;
}

.goods-meta {
  margin-bottom: 24rpx;
}

.goods-tag-group {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12rpx;
}

.goods-tag {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
  margin-bottom: 12rpx;
}

.goods-publish-time {
  font-size: 24rpx;
  color: #999;
}

/* 轮播图 */
.goods-swiper {
  height: 400rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
}

.goods-image {
  width: 100%;
  height: 100%;
}

/* 基本信息 */
.goods-basic-info {
  display: flex;
  flex-wrap: wrap;
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}

.info-item {
  width: 50%;
  padding: 12rpx 24rpx;
  box-sizing: border-box;
}

.info-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 商品描述 */
.description-content {
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}

.description-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 商品详情 */
.details-list {
  display: flex;
  flex-direction: column;
}

.details-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.details-item:last-child {
  border-bottom: none;
}

.details-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}

.details-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 交易方式 */
.trade-list {
  display: flex;
  flex-direction: column;
}

.trade-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.trade-item:last-child {
  border-bottom: none;
}

.trade-icon {
  font-size: 40rpx;
  color: #1890ff;
  margin-right: 20rpx;
}

.trade-info {
  flex: 1;
}

.trade-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.trade-desc {
  font-size: 26rpx;
  color: #666;
}

/* 卖家信息 */
.seller-header {
  display: flex;
  align-items: center;
}

.seller-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
}

.seller-avatar image {
  width: 100%;
  height: 100%;
}

.seller-info {
  flex: 1;
}

.seller-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.seller-meta {
  display: flex;
  align-items: center;
}

.seller-type, .seller-rating {
  font-size: 24rpx;
  color: #666;
  margin-right: 16rpx;
}

/* 联系方式 */
.contact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.contact-content {
  display: flex;
  flex-wrap: wrap;
}

.contact-item {
  width: 50%;
  padding: 12rpx 24rpx;
  box-sizing: border-box;
}

.contact-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}

.contact-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.contact-phone {
  color: #ff4d4f;
}

.contact-tips {
  width: 100%;
  padding: 12rpx 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  margin-top: 24rpx;
  display: flex;
  align-items: center;
}

.tips-icon {
  font-size: 40rpx;
  color: #1890ff;
  margin-right: 16rpx;
}

.tips-text {
  font-size: 28rpx;
  color: #666;
}

/* 相关物品推荐 */
.related-goods-card {
  margin-bottom: 24rpx;
}

.related-goods-content {
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}

.related-goods-list {
  margin-bottom: 24rpx;
}

.related-goods-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.related-goods-item:last-child {
  border-bottom: none;
}

.goods-item-content {
  display: flex;
  align-items: center;
}

.goods-item-left {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 16rpx;
}

.goods-item-left image {
  width: 100%;
  height: 100%;
}

.goods-item-middle {
  flex: 1;
}

.goods-item-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.goods-item-condition {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.goods-item-tags {
  display: flex;
  flex-wrap: wrap;
}

.goods-item-tag {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
  margin-bottom: 8rpx;
}

.goods-item-tag-more {
  font-size: 24rpx;
  color: #999;
}

.goods-item-right {
  width: 120rpx;
  text-align: right;
}

.goods-item-price {
  font-size: 28rpx;
  color: #ff4d4f;
  font-weight: 500;
}

.empty-related-goods {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
}

.empty-image {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 16rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.view-more-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16rpx 0;
  background-color: #fff;
  border-radius: 12rpx;
  border: 1rpx solid #f0f0f0;
}

.view-more-text {
  font-size: 28rpx;
  color: #1890ff;
  font-weight: 500;
  margin-right: 8rpx;
}

.view-more-icon {
  font-size: 24rpx;
  color: #1890ff;
}

/* 底部互动工具栏 */
.interaction-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10rpx 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  height: 120rpx;
  z-index: 100;
}

.toolbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6rpx 0;
  margin: 0 4rpx;
}

.toolbar-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}

.toolbar-text {
  font-size: 22rpx;
  color: #666;
}

.share-button {
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
  line-height: normal;
  border-radius: 0;
  flex: 1;
}

.share-button::after {
  display: none;
}

.call-button {
  flex: 3; /* 占据更多空间，原来是2，现在改为3让按钮更长 */
  background: linear-gradient(135deg, #0052CC, #0066FF);
  height: 90rpx;
  margin: 0 0 0 10rpx;
  border-radius: 45rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
}

.call-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.call-text {
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  line-height: 1.2;
}

.call-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 20rpx;
  line-height: 1.2;
}

/* 隐藏原来的底部操作栏 */
.action-bar {
  display: none;
}

/* 隐藏的分享按钮 */
.hidden-share-btn {
  position: absolute;
  top: -9999rpx;
  left: -9999rpx;
  width: 0;
  height: 0;
  padding: 0;
  margin: 0;
  opacity: 0;
}

/* 自定义导航栏样式 */
.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}

.navbar-left {
  width: 60px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 20px;
  height: 20px;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 500;
  color: #fff;
}

.navbar-right {
  width: 60px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style> 