/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-f45a6859, html.data-v-f45a6859, #app.data-v-f45a6859, .index-container.data-v-f45a6859 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.group-buy-page.data-v-f45a6859 {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F5F5F7;
}

/* 自定义导航栏 */
.custom-navbar.data-v-f45a6859 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
}
.custom-navbar .navbar-bg.data-v-f45a6859 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
  box-shadow: 0 4rpx 20rpx rgba(255, 44, 84, 0.3);
}
.custom-navbar .navbar-content.data-v-f45a6859 {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding-top: var(--status-bar-height, 25px);
  padding-left: 30rpx;
  padding-right: 30rpx;
  box-sizing: border-box;
}
.custom-navbar .navbar-content .back-btn.data-v-f45a6859 {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border-radius: 50%;
  padding: 16rpx;
}
.custom-navbar .navbar-content .back-icon.data-v-f45a6859 {
  width: 100%;
  height: 100%;
}
.custom-navbar .navbar-content .navbar-title.data-v-f45a6859 {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.custom-navbar .navbar-content .navbar-right.data-v-f45a6859 {
  width: 160rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 20rpx;
}
.custom-navbar .navbar-content .navbar-right .search-btn.data-v-f45a6859, .custom-navbar .navbar-content .navbar-right .close-btn.data-v-f45a6859 {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border-radius: 50%;
}

/* 分类选项卡 */
.category-tabs.data-v-f45a6859 {
  position: relative;
  display: flex;
  background-color: #FFFFFF;
  height: 88rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  border-radius: 0 0 35rpx 35rpx;
  margin: 0 20rpx;
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
}
.category-tabs .tab-item.data-v-f45a6859 {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #666666;
  position: relative;
}
.category-tabs .tab-item.active.data-v-f45a6859 {
  color: #FF2C54;
  font-weight: 600;
}
.category-tabs .tab-line.data-v-f45a6859 {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 4rpx;
  background-color: #FF2C54;
  transition: transform 0.3s ease;
  border-radius: 2rpx;
}

/* 内容区域 */
.content-scroll.data-v-f45a6859 {
  flex: 1;
  width: 100%;
  padding: 0 20rpx;
  padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
  margin-top: 20rpx;
}

/* 轮播图 */
.banner-swiper.data-v-f45a6859 {
  width: 100%;
  height: 300rpx;
  border-radius: 35rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 30rpx;
}
.banner-swiper .banner-image.data-v-f45a6859 {
  width: 100%;
  height: 100%;
}

/* 分类图标 */
.category-icons.data-v-f45a6859 {
  display: flex;
  justify-content: space-around;
  padding: 40rpx 20rpx;
  background-color: #FFFFFF;
  margin-bottom: 30rpx;
  border-radius: 35rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);
}
.category-icons .category-item.data-v-f45a6859 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.category-icons .category-item .category-icon.data-v-f45a6859 {
  width: 100rpx;
  height: 100rpx;
  border-radius: 35rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}
.category-icons .category-item .category-icon.data-v-f45a6859:active {
  transform: scale(0.95);
}
.category-icons .category-item .category-name.data-v-f45a6859 {
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
}

/* 通用section样式 */
.section.data-v-f45a6859 {
  background-color: #FFFFFF;
  margin-bottom: 30rpx;
  padding: 30rpx 0;
  border-radius: 35rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);
}
.section .section-header.data-v-f45a6859 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx 20rpx;
}
.section .section-header .section-title.data-v-f45a6859 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  position: relative;
  padding-left: 20rpx;
}
.section .section-header .section-title.data-v-f45a6859::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
  border-radius: 4rpx;
}
.section .section-header .countdown.data-v-f45a6859 {
  display: flex;
  align-items: center;
  background: rgba(255, 44, 84, 0.08);
  border-radius: 20rpx;
  padding: 6rpx 12rpx;
}
.section .section-header .countdown .countdown-label.data-v-f45a6859 {
  font-size: 24rpx;
  color: #666666;
  margin-right: 10rpx;
}
.section .section-header .countdown .countdown-time.data-v-f45a6859 {
  font-size: 24rpx;
  color: #FF2C54;
  font-weight: 600;
}

/* 限时拼团 */
.limited-time .limited-scroll.data-v-f45a6859 {
  width: 100%;
  white-space: nowrap;
}
.limited-time .limited-scroll .limited-items.data-v-f45a6859 {
  padding: 0 20rpx;
  display: inline-block;
}
.limited-time .limited-scroll .limited-items .limited-item.data-v-f45a6859 {
  display: inline-block;
  width: 240rpx;
  margin-right: 20rpx;
  background-color: #FFFFFF;
  border-radius: 25rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}
.limited-time .limited-scroll .limited-items .limited-item.data-v-f45a6859:active {
  transform: translateY(-5rpx);
}
.limited-time .limited-scroll .limited-items .limited-item .item-image.data-v-f45a6859 {
  width: 240rpx;
  height: 240rpx;
  border-radius: 25rpx 25rpx 0 0;
}
.limited-time .limited-scroll .limited-items .limited-item .item-info.data-v-f45a6859 {
  padding: 16rpx;
}
.limited-time .limited-scroll .limited-items .limited-item .item-info .item-title.data-v-f45a6859 {
  font-size: 26rpx;
  color: #333333;
  white-space: normal;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 72rpx;
  line-height: 1.4;
  font-weight: 500;
}
.limited-time .limited-scroll .limited-items .limited-item .item-info .price-container.data-v-f45a6859 {
  margin-top: 10rpx;
  display: flex;
  flex-wrap: wrap;
  align-items: baseline;
}
.limited-time .limited-scroll .limited-items .limited-item .item-info .price-container .group-price.data-v-f45a6859 {
  font-size: 32rpx;
  color: #FF2C54;
  font-weight: 600;
  margin-right: 10rpx;
}
.limited-time .limited-scroll .limited-items .limited-item .item-info .price-container .daily-price.data-v-f45a6859 {
  font-size: 24rpx;
  color: #FF9500;
  text-decoration: line-through;
  margin-right: 10rpx;
}
.limited-time .limited-scroll .limited-items .limited-item .item-info .price-container .original-price.data-v-f45a6859 {
  font-size: 22rpx;
  color: #999999;
  text-decoration: line-through;
}
.limited-time .limited-scroll .limited-items .limited-item .item-info .group-info.data-v-f45a6859 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
}
.limited-time .limited-scroll .limited-items .limited-item .item-info .group-info .group-count.data-v-f45a6859 {
  font-size: 22rpx;
  color: #FF2C54;
  background-color: rgba(255, 44, 84, 0.1);
  padding: 4rpx 10rpx;
  border-radius: 20rpx;
}
.limited-time .limited-scroll .limited-items .limited-item .item-info .group-info .join-btn.data-v-f45a6859 {
  font-size: 22rpx;
  color: #FFFFFF;
  background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(255, 44, 84, 0.2);
}

/* 加载更多和到底了提示 */
.loading-more.data-v-f45a6859, .no-more.data-v-f45a6859 {
  text-align: center;
  padding: 30rpx 0;
  font-size: 24rpx;
  color: #999999;
}

/* 拼团列表 */
.group-list-section .group-items.data-v-f45a6859 {
  padding: 0 20rpx;
}
.group-list-section .group-items .group-item.data-v-f45a6859 {
  display: flex;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #FFFFFF;
  border-radius: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.06);
  transition: transform 0.3s ease;
}
.group-list-section .group-items .group-item.data-v-f45a6859:active {
  transform: scale(0.98);
}
.group-list-section .group-items .group-item .item-image-container.data-v-f45a6859 {
  position: relative;
  width: 220rpx;
  height: 220rpx;
  margin-right: 20rpx;
}
.group-list-section .group-items .group-item .item-image-container .item-image.data-v-f45a6859 {
  width: 100%;
  height: 100%;
  border-radius: 25rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.group-list-section .group-items .group-item .item-image-container .item-tag.data-v-f45a6859 {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  padding: 4rpx 12rpx;
  font-size: 20rpx;
  color: #FFFFFF;
  border-radius: 15rpx;
  background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
  box-shadow: 0 4rpx 8rpx rgba(255, 44, 84, 0.2);
}
.group-list-section .group-items .group-item .item-content.data-v-f45a6859 {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.group-list-section .group-items .group-item .item-content .item-title.data-v-f45a6859 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}
.group-list-section .group-items .group-item .item-content .item-desc.data-v-f45a6859 {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.group-list-section .group-items .group-item .item-content .price-comparison.data-v-f45a6859 {
  margin-bottom: 12rpx;
  padding: 12rpx;
  background: rgba(245, 245, 247, 0.6);
  border-radius: 20rpx;
}
.group-list-section .group-items .group-item .item-content .price-comparison .price-row.data-v-f45a6859 {
  display: flex;
  align-items: center;
  margin-bottom: 6rpx;
}
.group-list-section .group-items .group-item .item-content .price-comparison .price-row .price-label.data-v-f45a6859 {
  font-size: 22rpx;
  color: #666666;
  width: 80rpx;
}
.group-list-section .group-items .group-item .item-content .price-comparison .price-row .price-value.data-v-f45a6859 {
  font-size: 24rpx;
}
.group-list-section .group-items .group-item .item-content .price-comparison .price-row .price-value.group-price.data-v-f45a6859 {
  font-size: 32rpx;
  color: #FF2C54;
  font-weight: 600;
}
.group-list-section .group-items .group-item .item-content .price-comparison .price-row .price-value.daily-price.data-v-f45a6859 {
  color: #FF9500;
  text-decoration: line-through;
}
.group-list-section .group-items .group-item .item-content .price-comparison .price-row .price-value.market-price.data-v-f45a6859 {
  color: #999999;
  text-decoration: line-through;
}
.group-list-section .group-items .group-item .item-content .price-comparison .price-save.data-v-f45a6859 {
  margin-top: 8rpx;
}
.group-list-section .group-items .group-item .item-content .price-comparison .price-save .save-text.data-v-f45a6859 {
  display: inline-block;
  font-size: 20rpx;
  color: #FFFFFF;
  background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
  padding: 4rpx 12rpx;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 8rpx rgba(255, 44, 84, 0.2);
}
.group-list-section .group-items .group-item .item-content .price-comparison.compact .price-row.data-v-f45a6859 {
  display: inline-flex;
  margin-right: 20rpx;
}
.group-list-section .group-items .group-item .item-content .price-comparison.compact .price-row .price-label.data-v-f45a6859 {
  width: auto;
  margin-right: 8rpx;
}
.group-list-section .group-items .group-item .item-content .item-footer.data-v-f45a6859 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}
.group-list-section .group-items .group-item .item-content .item-footer .group-progress.data-v-f45a6859 {
  display: flex;
  align-items: center;
}
.group-list-section .group-items .group-item .item-content .item-footer .group-progress .avatars.data-v-f45a6859 {
  display: flex;
  margin-right: 10rpx;
}
.group-list-section .group-items .group-item .item-content .item-footer .group-progress .avatars .user-avatar.data-v-f45a6859 {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #FFFFFF;
  margin-left: -10rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}
.group-list-section .group-items .group-item .item-content .item-footer .group-progress .avatars .user-avatar.data-v-f45a6859:first-child {
  margin-left: 0;
}
.group-list-section .group-items .group-item .item-content .item-footer .group-progress .avatars .avatar-count.data-v-f45a6859 {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #F2F2F7;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #666666;
  margin-left: -10rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}
.group-list-section .group-items .group-item .item-content .item-footer .group-progress .progress-text.data-v-f45a6859 {
  font-size: 22rpx;
  color: #666666;
}
.group-list-section .group-items .group-item .item-content .item-footer .action-btn.data-v-f45a6859 {
  height: 60rpx;
  padding: 0 20rpx;
  background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #FFFFFF;
  box-shadow: 0 4rpx 12rpx rgba(255, 44, 84, 0.3);
}

/* 我的拼团 */
.my-groups-section .empty-tip.data-v-f45a6859 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}
.my-groups-section .empty-tip .empty-image.data-v-f45a6859 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
  opacity: 0.8;
}
.my-groups-section .empty-tip .empty-text.data-v-f45a6859 {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 30rpx;
}
.my-groups-section .empty-tip .empty-btn.data-v-f45a6859 {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;
  width: 300rpx;
  border-radius: 40rpx;
  background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
  font-size: 28rpx;
  font-weight: 500;
  color: #FFFFFF;
  box-shadow: 0 8rpx 16rpx rgba(255, 44, 84, 0.2);
  transition: transform 0.3s ease;
}
.my-groups-section .empty-tip .empty-btn.data-v-f45a6859:active {
  transform: scale(0.95);
}
.my-groups-section .my-group-list.data-v-f45a6859 {
  padding: 0 20rpx;
}
.my-groups-section .my-group-list .my-group-item.data-v-f45a6859 {
  display: flex;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #FFFFFF;
  border-radius: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.06);
  transition: transform 0.3s ease;
}
.my-groups-section .my-group-list .my-group-item.data-v-f45a6859:active {
  transform: scale(0.98);
}
.my-groups-section .my-group-list .my-group-item .item-image-container.data-v-f45a6859 {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
}
.my-groups-section .my-group-list .my-group-item .item-image-container .item-image.data-v-f45a6859 {
  width: 100%;
  height: 100%;
  border-radius: 25rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.my-groups-section .my-group-list .my-group-item .item-image-container .item-status-tag.data-v-f45a6859 {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  padding: 4rpx 12rpx;
  font-size: 20rpx;
  color: #FFFFFF;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
}
.my-groups-section .my-group-list .my-group-item .item-image-container .item-status-tag.pending.data-v-f45a6859 {
  background: linear-gradient(135deg, #FF9500 0%, #FFBD2E 100%);
}
.my-groups-section .my-group-list .my-group-item .item-image-container .item-status-tag.success.data-v-f45a6859 {
  background: linear-gradient(135deg, #34C759 0%, #30DB5B 100%);
}
.my-groups-section .my-group-list .my-group-item .item-image-container .item-status-tag.failed.data-v-f45a6859 {
  background: linear-gradient(135deg, #FF3B30 0%, #FF5E54 100%);
}
.my-groups-section .my-group-list .my-group-item .item-content.data-v-f45a6859 {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.my-groups-section .my-group-list .my-group-item .item-content .item-title.data-v-f45a6859 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12rpx;
}
.my-groups-section .my-group-list .my-group-item .item-content .group-status.data-v-f45a6859 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12rpx;
}
.my-groups-section .my-group-list .my-group-item .item-content .group-status .status-info .status-text.data-v-f45a6859 {
  font-size: 24rpx;
  font-weight: 600;
  margin-right: 10rpx;
}
.my-groups-section .my-group-list .my-group-item .item-content .group-status .status-info .status-text.pending.data-v-f45a6859 {
  color: #FF9500;
}
.my-groups-section .my-group-list .my-group-item .item-content .group-status .status-info .status-text.success.data-v-f45a6859 {
  color: #34C759;
}
.my-groups-section .my-group-list .my-group-item .item-content .group-status .status-info .status-text.failed.data-v-f45a6859 {
  color: #FF3B30;
}
.my-groups-section .my-group-list .my-group-item .item-content .group-status .status-info .status-desc.data-v-f45a6859 {
  font-size: 22rpx;
  color: #666666;
}
.my-groups-section .my-group-list .my-group-item .item-content .group-status .status-action .share-btn.data-v-f45a6859 {
  height: 56rpx;
  padding: 0 20rpx;
  background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
  border-radius: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #FFFFFF;
  box-shadow: 0 4rpx 12rpx rgba(255, 44, 84, 0.3);
}

/* 浮动按钮 */
.float-btn.data-v-f45a6859 {
  position: fixed;
  right: 30rpx;
  bottom: 160rpx;
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(255, 44, 84, 0.3);
  z-index: 90;
  transform: translateZ(0);
  transition: transform 0.3s ease;
}
.float-btn.data-v-f45a6859:active {
  transform: scale(0.9);
}

/* 底部导航 */
.bottom-tabbar.data-v-f45a6859 {
  position: fixed;
  left: 20rpx;
  right: 20rpx;
  bottom: 20rpx;
  height: 100rpx;
  background-color: rgba(255, 255, 255, 0.9);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: space-around;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
  z-index: 99;
  padding-bottom: env(safe-area-inset-bottom);
  border-radius: 35rpx;
}
.bottom-tabbar .tab-item.data-v-f45a6859 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.bottom-tabbar .tab-item .tab-icon.data-v-f45a6859 {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}
.bottom-tabbar .tab-item .tab-icon.data-v-f45a6859:active {
  transform: scale(0.9);
}
.bottom-tabbar .tab-item .tab-text.data-v-f45a6859 {
  font-size: 20rpx;
  color: #666666;
  margin-top: 4rpx;
}
.bottom-tabbar .tab-item .tab-text.active.data-v-f45a6859 {
  color: #FF2C54;
  font-weight: 500;
}