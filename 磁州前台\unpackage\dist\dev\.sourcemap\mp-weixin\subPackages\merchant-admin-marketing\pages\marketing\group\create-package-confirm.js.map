{"version": 3, "file": "create-package-confirm.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/group/create-package-confirm.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xncm91cFxjcmVhdGUtcGFja2FnZS1jb25maXJtLnZ1ZQ"], "sourcesContent": ["<!-- 创建中 -->\n<template>\n  <view class=\"create-package-confirm-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">拼团活动</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 步骤指示器 -->\n    <view class=\"step-indicator\">\n      <view class=\"step-progress\">\n        <view class=\"step-progress-bar\" style=\"width: 100%\"></view>\n      </view>\n      <view class=\"step-text\">步骤 5/5</view>\n    </view>\n    \n    <!-- 页面内容 -->\n    <scroll-view scroll-y class=\"page-content\">\n      <view class=\"page-title\">确认套餐信息</view>\n      <view class=\"page-subtitle\">请确认团购套餐信息无误后提交</view>\n      \n      <!-- 套餐基本信息 -->\n      <view class=\"info-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">基本信息</text>\n          <view class=\"edit-btn\" @tap=\"editSection('info')\">编辑</view>\n        </view>\n        \n        <view class=\"section-content\">\n          <view class=\"info-item\">\n            <text class=\"info-label\">套餐名称</text>\n            <text class=\"info-value\">{{packageInfo.name}}</text>\n          </view>\n          \n          <view class=\"info-item\">\n            <text class=\"info-label\">套餐分类</text>\n            <text class=\"info-value\">{{packageInfo.category}}</text>\n          </view>\n          \n          <view class=\"info-item\">\n            <text class=\"info-label\">成团人数</text>\n            <text class=\"info-value\">{{packageInfo.groupSize}}人</text>\n          </view>\n          \n          <view class=\"info-item\">\n            <text class=\"info-label\">活动有效期</text>\n            <text class=\"info-value\">{{packageInfo.startDate}} 至 {{packageInfo.endDate}}</text>\n          </view>\n          \n          <view class=\"info-item\" v-if=\"packageInfo.description\">\n            <text class=\"info-label\">套餐描述</text>\n            <text class=\"info-value\">{{packageInfo.description}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 套餐价格信息 -->\n      <view class=\"info-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">价格信息</text>\n          <view class=\"edit-btn\" @tap=\"editSection('price')\">编辑</view>\n        </view>\n        \n        <view class=\"section-content\">\n          <view class=\"info-item\">\n            <text class=\"info-label\">市场价</text>\n            <text class=\"info-value\">¥{{priceInfo.marketPrice}}</text>\n          </view>\n          \n          <view class=\"info-item\">\n            <text class=\"info-label\">日常价</text>\n            <text class=\"info-value\">¥{{priceInfo.regularPrice}}</text>\n          </view>\n          \n          <view class=\"info-item\">\n            <text class=\"info-label\">拼团价</text>\n            <text class=\"info-value price\">¥{{priceInfo.groupPrice}}</text>\n          </view>\n          \n          <view class=\"info-item\">\n            <text class=\"info-label\">单人限购</text>\n            <text class=\"info-value\">{{priceInfo.limitPerUser > 0 ? priceInfo.limitPerUser + '件' : '不限购'}}</text>\n          </view>\n          \n          <view class=\"info-item\">\n            <text class=\"info-label\">库存数量</text>\n            <text class=\"info-value\">{{priceInfo.stock}}件</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 套餐内容信息 -->\n      <view class=\"info-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">套餐内容</text>\n          <view class=\"edit-btn\" @tap=\"editSection('items')\">编辑</view>\n        </view>\n        \n        <view class=\"section-content\">\n          <view v-for=\"(item, index) in packageItems\" :key=\"index\" class=\"package-item\">\n            <view class=\"item-header\">\n              <text class=\"item-title\">套餐项 {{index + 1}}</text>\n            </view>\n            \n            <view class=\"item-content\">\n              <view class=\"item-info\">\n                <text class=\"info-label\">名称:</text>\n                <text class=\"info-value\">{{item.name}}</text>\n              </view>\n              \n              <view class=\"item-info\">\n                <text class=\"info-label\">数量:</text>\n                <text class=\"info-value\">{{item.quantity}} {{item.unit}}</text>\n              </view>\n              \n              <view class=\"item-info\">\n                <text class=\"info-label\">原价:</text>\n                <text class=\"info-value\">¥{{item.price}}</text>\n              </view>\n              \n              <view class=\"item-info\" v-if=\"item.description\">\n                <text class=\"info-label\">描述:</text>\n                <text class=\"info-value\">{{item.description}}</text>\n              </view>\n            </view>\n          </view>\n          \n          <view class=\"total-value\">\n            <text>套餐原价总值: </text>\n            <text class=\"value\">¥{{calculateTotalValue()}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 支付和核销设置 -->\n      <view class=\"info-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">支付和核销设置</text>\n          <view class=\"edit-btn\" @tap=\"editSection('payment')\">编辑</view>\n        </view>\n        \n        <view class=\"section-content\">\n          <view class=\"info-item\">\n            <text class=\"info-label\">支付方式</text>\n            <text class=\"info-value\">{{paymentInfo.paymentType === 'online' ? '线上支付' : '到店支付'}}</text>\n          </view>\n          \n          <view class=\"info-item\">\n            <text class=\"info-label\">核销方式</text>\n            <text class=\"info-value\">{{paymentInfo.verifyType === 'online' ? '线上核销' : '到店核销'}}</text>\n          </view>\n          \n          <view class=\"info-item\" v-if=\"paymentInfo.verifyType === 'offline'\">\n            <text class=\"info-label\">核销码有效期</text>\n            <text class=\"info-value\">{{paymentInfo.verifyCodeValidDays}}天</text>\n          </view>\n          \n          <view class=\"info-item\" v-if=\"paymentInfo.verifyType === 'offline'\">\n            <text class=\"info-label\">可核销次数</text>\n            <text class=\"info-value\">{{paymentInfo.verifyTimes}}次</text>\n          </view>\n        </view>\n      </view>\n    </scroll-view>\n    \n    <!-- 底部按钮 -->\n    <view class=\"footer-buttons\">\n      <button class=\"btn btn-secondary\" @click=\"goBack\">上一步</button>\n      <button class=\"btn btn-primary\" @click=\"submitPackage\">提交套餐</button>\n    </view>\n    \n    <!-- 提交成功弹窗 -->\n    <view class=\"modal\" v-if=\"showSuccessModal\">\n      <view class=\"modal-mask\"></view>\n      <view class=\"modal-content success-modal\">\n        <view class=\"success-icon\">\n          <view class=\"checkmark\"></view>\n        </view>\n        <view class=\"success-title\">创建成功</view>\n        <view class=\"success-message\">团购套餐已成功创建</view>\n        <button class=\"success-btn\" @tap=\"goToList\">返回套餐列表</button>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      packageInfo: {\n        name: '',\n        category: '',\n        groupSize: 2,\n        startDate: '',\n        endDate: '',\n        description: ''\n      },\n      priceInfo: {\n        marketPrice: '0.00',\n        regularPrice: '0.00',\n        groupPrice: '0.00',\n        limitPerUser: 1,\n        stock: 100\n      },\n      packageItems: [],\n      paymentInfo: {\n        paymentType: 'offline', // offline: 到店支付, online: 线上支付\n        verifyType: 'offline', // offline: 到店核销, online: 线上核销\n        verifyCodeValidDays: 30,\n        verifyTimes: 1\n      },\n      showSuccessModal: false\n    }\n  },\n  onLoad() {\n    // 尝试从本地存储获取之前保存的数据\n    try {\n      const savedInfo = uni.getStorageSync('packageInfo');\n      if (savedInfo) {\n        this.packageInfo = JSON.parse(savedInfo);\n      }\n      \n      const savedPriceInfo = uni.getStorageSync('packagePriceInfo');\n      if (savedPriceInfo) {\n        this.priceInfo = JSON.parse(savedPriceInfo);\n      }\n      \n      const savedItems = uni.getStorageSync('packageItems');\n      if (savedItems) {\n        this.packageItems = JSON.parse(savedItems);\n      }\n      \n      const savedPaymentInfo = uni.getStorageSync('packagePaymentInfo');\n      if (savedPaymentInfo) {\n        this.paymentInfo = JSON.parse(savedPaymentInfo);\n      } else {\n        // 如果没有保存过支付信息，则使用默认值\n        uni.setStorageSync('packagePaymentInfo', JSON.stringify(this.paymentInfo));\n      }\n    } catch (e) {\n      console.error('读取本地存储失败:', e);\n    }\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack();\n    },\n    editSection(section) {\n      switch (section) {\n        case 'info':\n          uni.navigateTo({\n            url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-info'\n          });\n          break;\n        case 'price':\n          uni.navigateTo({\n            url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-price'\n          });\n          break;\n        case 'items':\n          uni.navigateTo({\n            url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-items'\n          });\n          break;\n        case 'payment':\n          // 这里可以添加支付和核销设置页面的跳转\n          uni.showToast({\n            title: '支付和核销设置功能开发中',\n            icon: 'none'\n          });\n          break;\n      }\n    },\n    calculateTotalValue() {\n      let total = 0;\n      this.packageItems.forEach(item => {\n        total += parseFloat(item.price) * parseFloat(item.quantity);\n      });\n      return total.toFixed(2);\n    },\n    submitPackage() {\n      // 显示加载中\n      uni.showLoading({\n        title: '提交中...'\n      });\n      \n      // 模拟提交数据\n      setTimeout(() => {\n        // 隐藏加载中\n        uni.hideLoading();\n        \n        // 显示成功弹窗\n        this.showSuccessModal = true;\n        \n        // 清除本地存储的数据\n        try {\n          uni.removeStorageSync('packageInfo');\n          uni.removeStorageSync('packagePriceInfo');\n          uni.removeStorageSync('packageItems');\n          uni.removeStorageSync('packagePaymentInfo');\n          uni.removeStorageSync('packageType');\n        } catch (e) {\n          console.error('清除本地存储失败:', e);\n        }\n      }, 1500);\n    },\n    goToList() {\n      // 跳转到套餐列表页面\n      uni.redirectTo({\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/group/package-management'\n      });\n    },\n    showHelp() {\n      uni.showToast({\n        title: '帮助信息',\n        icon: 'none'\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.create-package-confirm-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  display: flex;\n  flex-direction: column;\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #9040FF, #5E35B1);\n  color: #fff;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 20px;\n  height: 20px;\n  border-radius: 50%;\n  border: 1px solid #fff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  color: #fff;\n}\n\n/* 步骤指示器 */\n.step-indicator {\n  padding: 15px;\n  background: #FFFFFF;\n}\n\n.step-progress {\n  height: 4px;\n  background-color: #EBEDF5;\n  border-radius: 2px;\n  margin-bottom: 5px;\n  position: relative;\n}\n\n.step-progress-bar {\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 100%;\n  background: linear-gradient(90deg, #9040FF, #5E35B1);\n  border-radius: 2px;\n}\n\n.step-text {\n  font-size: 12px;\n  color: #999;\n  text-align: right;\n}\n\n/* 页面内容 */\n.page-content {\n  flex: 1;\n  padding: 20px 15px;\n}\n\n.page-title {\n  font-size: 20px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.page-subtitle {\n  font-size: 14px;\n  color: #999;\n  margin-bottom: 20px;\n}\n\n/* 信息区块样式 */\n.info-section {\n  background: #FFFFFF;\n  border-radius: 12px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n  overflow: hidden;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px;\n  border-bottom: 1px solid #EBEDF5;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.edit-btn {\n  font-size: 14px;\n  color: #9040FF;\n}\n\n.section-content {\n  padding: 15px;\n}\n\n.info-item {\n  display: flex;\n  margin-bottom: 10px;\n}\n\n.info-item:last-child {\n  margin-bottom: 0;\n}\n\n.info-label {\n  width: 80px;\n  font-size: 14px;\n  color: #666;\n}\n\n.info-value {\n  flex: 1;\n  font-size: 14px;\n  color: #333;\n}\n\n.info-value.price {\n  color: #FF3B30;\n  font-weight: 600;\n}\n\n/* 套餐项样式 */\n.package-item {\n  background: #F9F9F9;\n  border-radius: 8px;\n  padding: 12px;\n  margin-bottom: 10px;\n}\n\n.package-item:last-child {\n  margin-bottom: 0;\n}\n\n.item-header {\n  margin-bottom: 10px;\n}\n\n.item-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n}\n\n.item-content {\n  display: flex;\n  flex-direction: column;\n  gap: 5px;\n}\n\n.item-info {\n  display: flex;\n  font-size: 13px;\n}\n\n.total-value {\n  margin-top: 15px;\n  text-align: right;\n  font-size: 14px;\n  color: #666;\n}\n\n.total-value .value {\n  font-weight: 600;\n  color: #333;\n}\n\n/* 底部按钮 */\n.footer-buttons {\n  padding: 15px;\n  background: #FFFFFF;\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);\n  display: flex;\n  justify-content: space-between;\n  gap: 15px;\n}\n\n.btn {\n  flex: 1;\n  height: 50px;\n  border-radius: 25px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n  font-weight: 600;\n  border: none;\n}\n\n.btn-primary {\n  background: linear-gradient(135deg, #9040FF, #5E35B1);\n  color: #FFFFFF;\n}\n\n.btn-secondary {\n  background: #F5F7FA;\n  color: #666;\n  border: 1px solid #EBEDF5;\n}\n\n/* 成功弹窗样式 */\n.modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 999;\n}\n\n.modal-mask {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n}\n\n.modal-content {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: #FFFFFF;\n  border-radius: 12px;\n  padding: 30px;\n  width: 80%;\n  max-width: 300px;\n}\n\n.success-modal {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.success-icon {\n  width: 60px;\n  height: 60px;\n  background: #34C759;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 15px;\n}\n\n.checkmark {\n  width: 30px;\n  height: 15px;\n  border-left: 3px solid #fff;\n  border-bottom: 3px solid #fff;\n  transform: rotate(-45deg);\n}\n\n.success-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 10px;\n}\n\n.success-message {\n  font-size: 14px;\n  color: #666;\n  margin-bottom: 20px;\n  text-align: center;\n}\n\n.success-btn {\n  width: 100%;\n  height: 45px;\n  background: linear-gradient(135deg, #9040FF, #5E35B1);\n  color: #FFFFFF;\n  border-radius: 22.5px;\n  font-size: 16px;\n  font-weight: 600;\n  border: none;\n}\n</style>\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-confirm.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAiMA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,aAAa;AAAA,QACX,MAAM;AAAA,QACN,UAAU;AAAA,QACV,WAAW;AAAA,QACX,WAAW;AAAA,QACX,SAAS;AAAA,QACT,aAAa;AAAA,MACd;AAAA,MACD,WAAW;AAAA,QACT,aAAa;AAAA,QACb,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,OAAO;AAAA,MACR;AAAA,MACD,cAAc,CAAE;AAAA,MAChB,aAAa;AAAA,QACX,aAAa;AAAA;AAAA,QACb,YAAY;AAAA;AAAA,QACZ,qBAAqB;AAAA,QACrB,aAAa;AAAA,MACd;AAAA,MACD,kBAAkB;AAAA,IACpB;AAAA,EACD;AAAA,EACD,SAAS;AAEP,QAAI;AACF,YAAM,YAAYA,cAAAA,MAAI,eAAe,aAAa;AAClD,UAAI,WAAW;AACb,aAAK,cAAc,KAAK,MAAM,SAAS;AAAA,MACzC;AAEA,YAAM,iBAAiBA,cAAAA,MAAI,eAAe,kBAAkB;AAC5D,UAAI,gBAAgB;AAClB,aAAK,YAAY,KAAK,MAAM,cAAc;AAAA,MAC5C;AAEA,YAAM,aAAaA,cAAAA,MAAI,eAAe,cAAc;AACpD,UAAI,YAAY;AACd,aAAK,eAAe,KAAK,MAAM,UAAU;AAAA,MAC3C;AAEA,YAAM,mBAAmBA,cAAAA,MAAI,eAAe,oBAAoB;AAChE,UAAI,kBAAkB;AACpB,aAAK,cAAc,KAAK,MAAM,gBAAgB;AAAA,aACzC;AAELA,sBAAG,MAAC,eAAe,sBAAsB,KAAK,UAAU,KAAK,WAAW,CAAC;AAAA,MAC3E;AAAA,IACF,SAAS,GAAG;AACVA,oBAAc,MAAA,MAAA,SAAA,gGAAA,aAAa,CAAC;AAAA,IAC9B;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,YAAY,SAAS;AACnB,cAAQ,SAAO;AAAA,QACb,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACP,CAAC;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACP,CAAC;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACP,CAAC;AACD;AAAA,QACF,KAAK;AAEHA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AACD;AAAA,MACJ;AAAA,IACD;AAAA,IACD,sBAAsB;AACpB,UAAI,QAAQ;AACZ,WAAK,aAAa,QAAQ,UAAQ;AAChC,iBAAS,WAAW,KAAK,KAAK,IAAI,WAAW,KAAK,QAAQ;AAAA,MAC5D,CAAC;AACD,aAAO,MAAM,QAAQ,CAAC;AAAA,IACvB;AAAA,IACD,gBAAgB;AAEdA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAGD,iBAAW,MAAM;AAEfA,sBAAG,MAAC,YAAW;AAGf,aAAK,mBAAmB;AAGxB,YAAI;AACFA,8BAAI,kBAAkB,aAAa;AACnCA,8BAAI,kBAAkB,kBAAkB;AACxCA,8BAAI,kBAAkB,cAAc;AACpCA,8BAAI,kBAAkB,oBAAoB;AAC1CA,8BAAI,kBAAkB,aAAa;AAAA,QACrC,SAAS,GAAG;AACVA,wBAAA,MAAA,MAAA,SAAA,gGAAc,aAAa,CAAC;AAAA,QAC9B;AAAA,MACD,GAAE,IAAI;AAAA,IACR;AAAA,IACD,WAAW;AAETA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IACD,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrUA,GAAG,WAAW,eAAe;"}