"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _component_line = common_vendor.resolveComponent("line");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_rect = common_vendor.resolveComponent("rect");
  (_component_line + _component_svg + _component_polyline + _component_path + _component_circle + _component_rect)();
}
if (!Math) {
  InfoCardFactory();
}
const InfoCardFactory = () => "../cards/InfoCardFactory.js";
const _sfc_main = {
  __name: "InfoList",
  props: {
    allInfoList: {
      type: Array,
      default: () => []
    },
    toppedInfoList: {
      type: Array,
      default: () => []
    },
    adBanner: {
      type: Object,
      default: null
    }
  },
  emits: ["tab-change"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const currentInfoTab = common_vendor.ref(0);
    const visibleCategories = common_vendor.ref([
      "到家服务",
      "寻找服务",
      "生意转让",
      "招聘信息",
      "求职信息",
      "房屋出租",
      "房屋出售",
      "二手车辆",
      "宠物信息",
      "商家活动",
      "婚恋交友",
      "车辆服务",
      "二手闲置",
      "磁州拼车",
      "教育培训",
      "其他服务"
    ]);
    const infoCategories = common_vendor.ref([
      "到家服务",
      "寻找服务",
      "生意转让",
      "招聘信息",
      "求职信息",
      "房屋出租",
      "房屋出售",
      "二手车辆",
      "宠物信息",
      "商家活动",
      "婚恋交友",
      "车辆服务",
      "二手闲置",
      "磁州拼车",
      "教育培训",
      "其他服务"
    ]);
    const isSticky = common_vendor.ref(false);
    const stickyTop = common_vendor.ref(0);
    const tabsHeight = common_vendor.ref(88);
    const tabsScrollLeft = common_vendor.ref(0);
    const showTopOptionsModal = common_vendor.ref(false);
    const hasMoreData = common_vendor.ref(true);
    const combinedInfoList = common_vendor.computed(() => {
      let toppedItems = [];
      let normalItems = [];
      if (currentInfoTab.value === 0) {
        toppedItems = props.toppedInfoList || [];
        normalItems = props.allInfoList || [];
      } else {
        const selectedCategory = infoCategories.value[currentInfoTab.value - 1];
        toppedItems = (props.toppedInfoList || []).filter((item) => item.category === selectedCategory);
        normalItems = (props.allInfoList || []).filter((item) => item.category === selectedCategory);
      }
      toppedItems = toppedItems.map((item, index) => ({
        ...item,
        isTopped: true,
        key: `top-${item.id || index}-${Date.now()}`
      }));
      normalItems = normalItems.map((item, index) => ({
        ...item,
        isTopped: false,
        key: `normal-${item.id || index}-${Date.now()}`
      }));
      const allItems = [...toppedItems, ...normalItems];
      const result = [];
      let uniqueCounter = 0;
      const adImages = [
        "/static/images/banner/ad-banner.jpg",
        "/static/images/banner/banner-1.png",
        "/static/images/banner/banner-2.png",
        "/static/images/banner/banner-3.jpg"
      ];
      if (props.adBanner && props.adBanner.image) {
        adImages.unshift(props.adBanner.image);
      }
      allItems.forEach((item, index) => {
        result.push(item);
        if ((index + 1) % 5 === 0) {
          const ad = {
            isAd: true,
            image: adImages[uniqueCounter % adImages.length],
            key: `ad-${uniqueCounter++}-${Date.now()}`
          };
          result.push(ad);
        }
      });
      return result;
    });
    const stickyStyle = common_vendor.computed(() => ({
      top: `${stickyTop.value}px`
    }));
    const hasTopItems = common_vendor.computed(() => {
      return props.toppedInfoList && props.toppedInfoList.length > 0;
    });
    function setSticky(sticky, top = 0) {
      isSticky.value = sticky;
      stickyTop.value = top;
    }
    function switchInfoTab(index) {
      if (currentInfoTab.value === index)
        return;
      currentInfoTab.value = index;
      emit("tab-change", {
        index,
        name: index === 0 ? "最新发布" : infoCategories.value[index - 1]
      });
      common_vendor.nextTick$1(() => {
        const query = common_vendor.index.createSelectorQuery();
        query.select(`#tab-${index}`).boundingClientRect((data) => {
          if (data) {
            const screenWidth = common_vendor.index.getSystemInfoSync().windowWidth;
            const scrollTarget = data.left - screenWidth / 2 + data.width / 2;
            tabsScrollLeft.value = scrollTarget;
          }
        }).exec();
      });
    }
    function navigateTo(url) {
      if (!url)
        return;
      common_vendor.index.navigateTo({
        url,
        fail: (err) => {
          common_vendor.index.__f__("error", "at components/index/InfoList.vue:372", "页面跳转失败:", err);
          common_vendor.index.showToast({
            title: "页面开发中",
            icon: "none"
          });
        }
      });
    }
    function navigateToInfoDetail(item) {
      const detailPageMap = {
        "招聘信息": "job-detail",
        "求职信息": "job-seeking-detail",
        "房屋出租": "house-rent-detail",
        "房屋出售": "house-sale-detail",
        "二手车辆": "car-detail",
        "宠物信息": "pet-detail",
        "二手闲置": "second-hand-detail",
        "婚恋交友": "dating-detail",
        "商家活动": "merchant-activity-detail",
        "磁州拼车": "carpool-detail",
        "教育培训": "education-detail",
        "到家服务": "home-service-detail",
        "寻找服务": "find-service-detail",
        "生意转让": "business-transfer-detail",
        // 默认跳转到通用信息详情页
        "default": "info-detail"
      };
      const pageType = detailPageMap[item.category] || detailPageMap["default"];
      let params = {
        id: item.id,
        category: encodeURIComponent(item.category || "")
      };
      const url = `/pages/publish/${pageType}?${Object.entries(params).map(([key, value]) => `${key}=${value}`).join("&")}`;
      common_vendor.index.__f__("log", "at components/index/InfoList.vue:415", "跳转到详情页:", url);
      navigateTo(url);
    }
    function showTopOptions() {
      showTopOptionsModal.value = true;
    }
    function hideTopOptions() {
      showTopOptionsModal.value = false;
    }
    function selectTopOption(type) {
      if (type === "paid") {
        navigateTo("/pages/top/paid");
      } else if (type === "ad") {
        common_vendor.index.showToast({
          title: "正在加载广告...",
          icon: "loading"
        });
        setTimeout(() => {
          common_vendor.index.hideToast();
          common_vendor.index.showModal({
            title: "广告观看完成",
            content: "恭喜您获得2小时置顶特权！",
            showCancel: false,
            success: () => {
              hideTopOptions();
            }
          });
        }, 1500);
      }
    }
    function showFilterOptions() {
      common_vendor.index.showToast({
        title: "筛选功能开发中",
        icon: "none"
      });
    }
    const tabsTop = common_vendor.ref(0);
    common_vendor.ref(null);
    common_vendor.onMounted(() => {
      isSticky.value = false;
      tabsHeight.value = 80;
      common_vendor.nextTick$1(() => {
        const query = common_vendor.index.createSelectorQuery();
        query.select(".info-tabs-container").boundingClientRect((data) => {
          if (data) {
            tabsHeight.value = data.height;
            tabsTop.value = data.top;
          }
        }).exec();
      });
    });
    __expose({
      setSticky
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          x1: "12",
          y1: "5",
          x2: "12",
          y2: "19"
        }),
        b: common_vendor.p({
          x1: "5",
          y1: "12",
          x2: "19",
          y2: "12"
        }),
        c: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        d: common_vendor.o(($event) => navigateTo("/pages/publish/detail")),
        e: common_vendor.p({
          points: "22 12 18 12 15 21 9 3 6 12 2 12"
        }),
        f: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        g: currentInfoTab.value === 0
      }, currentInfoTab.value === 0 ? {} : {}, {
        h: currentInfoTab.value === 0 ? 1 : "",
        i: common_vendor.o(($event) => switchInfoTab(0)),
        j: `tab-0`,
        k: common_vendor.f(visibleCategories.value, (cat, idx, i0) => {
          return common_vendor.e({
            a: cat === "招聘信息" || cat === "求职信息"
          }, cat === "招聘信息" || cat === "求职信息" ? {
            b: "80f2f579-6-" + i0 + "," + ("80f2f579-5-" + i0),
            c: common_vendor.p({
              d: "M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"
            })
          } : {}, {
            d: cat === "招聘信息" || cat === "求职信息"
          }, cat === "招聘信息" || cat === "求职信息" ? {
            e: "80f2f579-7-" + i0 + "," + ("80f2f579-5-" + i0),
            f: common_vendor.p({
              cx: "8.5",
              cy: "7",
              r: "4"
            })
          } : {}, {
            g: cat === "房屋出租" || cat === "房屋出售"
          }, cat === "房屋出租" || cat === "房屋出售" ? {
            h: "80f2f579-8-" + i0 + "," + ("80f2f579-5-" + i0),
            i: common_vendor.p({
              d: "M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"
            })
          } : {}, {
            j: cat === "房屋出租" || cat === "房屋出售"
          }, cat === "房屋出租" || cat === "房屋出售" ? {
            k: "80f2f579-9-" + i0 + "," + ("80f2f579-5-" + i0),
            l: common_vendor.p({
              points: "9 22 9 12 15 12 15 22"
            })
          } : {}, {
            m: cat === "二手闲置" || cat === "二手车辆"
          }, cat === "二手闲置" || cat === "二手车辆" ? {
            n: "80f2f579-10-" + i0 + "," + ("80f2f579-5-" + i0),
            o: common_vendor.p({
              cx: "9",
              cy: "21",
              r: "1"
            })
          } : {}, {
            p: cat === "二手闲置" || cat === "二手车辆"
          }, cat === "二手闲置" || cat === "二手车辆" ? {
            q: "80f2f579-11-" + i0 + "," + ("80f2f579-5-" + i0),
            r: common_vendor.p({
              cx: "20",
              cy: "21",
              r: "1"
            })
          } : {}, {
            s: cat === "二手闲置" || cat === "二手车辆"
          }, cat === "二手闲置" || cat === "二手车辆" ? {
            t: "80f2f579-12-" + i0 + "," + ("80f2f579-5-" + i0),
            v: common_vendor.p({
              d: "M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"
            })
          } : {}, {
            w: cat === "到家服务" || cat === "寻找服务"
          }, cat === "到家服务" || cat === "寻找服务" ? {
            x: "80f2f579-13-" + i0 + "," + ("80f2f579-5-" + i0),
            y: common_vendor.p({
              d: "M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"
            })
          } : {}, {
            z: cat === "宠物信息"
          }, cat === "宠物信息" ? {
            A: "80f2f579-14-" + i0 + "," + ("80f2f579-5-" + i0),
            B: common_vendor.p({
              cx: "12",
              cy: "12",
              r: "10"
            })
          } : {}, {
            C: cat === "宠物信息"
          }, cat === "宠物信息" ? {
            D: "80f2f579-15-" + i0 + "," + ("80f2f579-5-" + i0),
            E: common_vendor.p({
              d: "M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"
            })
          } : {}, {
            F: cat === "宠物信息"
          }, cat === "宠物信息" ? {
            G: "80f2f579-16-" + i0 + "," + ("80f2f579-5-" + i0),
            H: common_vendor.p({
              x1: "12",
              y1: "17",
              x2: "12.01",
              y2: "17"
            })
          } : {}, {
            I: cat === "其他服务"
          }, cat === "其他服务" ? {
            J: "80f2f579-17-" + i0 + "," + ("80f2f579-5-" + i0),
            K: common_vendor.p({
              x: "3",
              y: "3",
              width: "18",
              height: "18",
              rx: "2",
              ry: "2"
            })
          } : {}, {
            L: cat === "其他服务"
          }, cat === "其他服务" ? {
            M: "80f2f579-18-" + i0 + "," + ("80f2f579-5-" + i0),
            N: common_vendor.p({
              cx: "8.5",
              cy: "8.5",
              r: "1.5"
            })
          } : {}, {
            O: cat === "其他服务"
          }, cat === "其他服务" ? {
            P: "80f2f579-19-" + i0 + "," + ("80f2f579-5-" + i0),
            Q: common_vendor.p({
              points: "21 15 16 10 5 21"
            })
          } : {}, {
            R: "80f2f579-5-" + i0,
            S: common_vendor.t(cat),
            T: currentInfoTab.value === infoCategories.value.indexOf(cat) + 1
          }, currentInfoTab.value === infoCategories.value.indexOf(cat) + 1 ? {} : {}, {
            U: cat,
            V: currentInfoTab.value === infoCategories.value.indexOf(cat) + 1 ? 1 : "",
            W: common_vendor.o(($event) => switchInfoTab(infoCategories.value.indexOf(cat) + 1), cat),
            X: `tab-${infoCategories.value.indexOf(cat) + 1}`
          });
        }),
        l: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        m: common_vendor.p({
          x1: "4",
          y1: "21",
          x2: "4",
          y2: "14"
        }),
        n: common_vendor.p({
          x1: "4",
          y1: "10",
          x2: "4",
          y2: "3"
        }),
        o: common_vendor.p({
          x1: "12",
          y1: "21",
          x2: "12",
          y2: "12"
        }),
        p: common_vendor.p({
          x1: "12",
          y1: "8",
          x2: "12",
          y2: "3"
        }),
        q: common_vendor.p({
          x1: "20",
          y1: "21",
          x2: "20",
          y2: "16"
        }),
        r: common_vendor.p({
          x1: "20",
          y1: "12",
          x2: "20",
          y2: "3"
        }),
        s: common_vendor.p({
          x1: "1",
          y1: "14",
          x2: "7",
          y2: "14"
        }),
        t: common_vendor.p({
          x1: "9",
          y1: "8",
          x2: "15",
          y2: "8"
        }),
        v: common_vendor.p({
          x1: "17",
          y1: "16",
          x2: "23",
          y2: "16"
        }),
        w: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        x: common_vendor.o(showFilterOptions),
        y: tabsScrollLeft.value,
        z: isSticky.value ? 1 : "",
        A: common_vendor.s(stickyStyle.value),
        B: isSticky.value
      }, isSticky.value ? {
        C: tabsHeight.value + "rpx"
      } : {}, {
        D: hasTopItems.value
      }, hasTopItems.value ? {
        E: common_vendor.p({
          d: "M12 2L2 7l10 5 10-5-10-5z"
        }),
        F: common_vendor.p({
          d: "M2 17l10 5 10-5"
        }),
        G: common_vendor.p({
          d: "M2 12l10 5 10-5"
        }),
        H: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        I: common_vendor.o(showTopOptions)
      } : {}, {
        J: common_vendor.f(combinedInfoList.value, (item, globalIndex, i0) => {
          return common_vendor.e({
            a: !item.isAd
          }, !item.isAd ? {
            b: common_vendor.o(($event) => navigateToInfoDetail(item), item.key),
            c: "80f2f579-34-" + i0,
            d: common_vendor.p({
              item
            })
          } : {
            e: "80f2f579-36-" + i0 + "," + ("80f2f579-35-" + i0),
            f: common_vendor.p({
              x: "2",
              y: "3",
              width: "20",
              height: "14",
              rx: "2",
              ry: "2"
            }),
            g: "80f2f579-37-" + i0 + "," + ("80f2f579-35-" + i0),
            h: common_vendor.p({
              x1: "8",
              y1: "21",
              x2: "16",
              y2: "21"
            }),
            i: "80f2f579-38-" + i0 + "," + ("80f2f579-35-" + i0),
            j: common_vendor.p({
              x1: "12",
              y1: "17",
              x2: "12",
              y2: "21"
            }),
            k: "80f2f579-35-" + i0,
            l: common_vendor.p({
              xmlns: "http://www.w3.org/2000/svg",
              width: "100%",
              height: "100%",
              viewBox: "0 0 24 24",
              fill: "none",
              stroke: "currentColor",
              ["stroke-width"]: "1",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            })
          }, {
            m: item.key
          });
        }),
        K: hasMoreData.value
      }, hasMoreData.value ? {
        L: common_vendor.p({
          cx: "12",
          cy: "12",
          r: "10"
        }),
        M: common_vendor.p({
          d: "M12 6v6l4 2"
        }),
        N: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "24",
          height: "24",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        })
      } : {}, {
        O: showTopOptionsModal.value
      }, showTopOptionsModal.value ? {
        P: common_vendor.p({
          x1: "18",
          y1: "6",
          x2: "6",
          y2: "18"
        }),
        Q: common_vendor.p({
          x1: "6",
          y1: "6",
          x2: "18",
          y2: "18"
        }),
        R: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "20",
          height: "20",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        S: common_vendor.o(hideTopOptions),
        T: common_vendor.p({
          x1: "12",
          y1: "1",
          x2: "12",
          y2: "23"
        }),
        U: common_vendor.p({
          d: "M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"
        }),
        V: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "24",
          height: "24",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        W: common_vendor.p({
          points: "9 18 15 12 9 6"
        }),
        X: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        Y: common_vendor.o(($event) => selectTopOption("paid")),
        Z: common_vendor.p({
          x: "2",
          y: "3",
          width: "20",
          height: "14",
          rx: "2",
          ry: "2"
        }),
        aa: common_vendor.p({
          x1: "8",
          y1: "21",
          x2: "16",
          y2: "21"
        }),
        ab: common_vendor.p({
          x1: "12",
          y1: "17",
          x2: "12",
          y2: "21"
        }),
        ac: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "24",
          height: "24",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        ad: common_vendor.p({
          points: "9 18 15 12 9 6"
        }),
        ae: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        af: common_vendor.o(($event) => selectTopOption("ad")),
        ag: common_vendor.o(() => {
        }),
        ah: common_vendor.o(hideTopOptions)
      } : {});
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-80f2f579"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/index/InfoList.js.map
