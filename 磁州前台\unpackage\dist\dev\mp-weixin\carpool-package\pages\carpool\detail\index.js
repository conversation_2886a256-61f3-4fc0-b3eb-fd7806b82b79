"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
if (!Math) {
  FloatPromotionButton();
}
const FloatPromotionButton = () => "../../../../components/FloatPromotionButton.js";
const _sfc_main = {
  __name: "index",
  setup(__props, { expose: __expose }) {
    const statusBarHeight = common_vendor.ref(20);
    const carpoolId = common_vendor.ref("");
    const isFavorite = common_vendor.ref(false);
    const carpoolInfo = common_vendor.ref({
      id: 1,
      type: "car-to-people",
      startPoint: "磁州城区-汽车站",
      startAddress: "河北省邯郸市磁县磁州镇汽车站",
      endPoint: "邯郸火车站",
      endAddress: "河北省邯郸市邯山区陵西大街火车站",
      departureDate: "2023-10-25",
      departureTime: "15:30",
      distance: "35",
      estimatedDuration: "1",
      tripNotes: "途经安徽-河南-陕西-汉中，请提前10分钟到达上车地点",
      passengers: 1,
      availableSeats: 3,
      price: "30",
      username: "李师傅",
      avatar: "/static/images/avatar/user2.png",
      phone: "13987654321",
      isVerified: true,
      isPremium: true,
      publishTime: "2023-10-22 12:30",
      expiryTime: "2023-10-25 15:30",
      remark: "准时发车，不等待，有行李提前告知，谢谢配合",
      carModel: "大众途观",
      carColor: "白色",
      carPlate: "冀G·12345",
      rating: 4.8,
      ratingCount: 125,
      ratingTags: ["准时守信", "驾驶平稳", "热情礼貌", "车内整洁"]
    });
    const typeText = common_vendor.computed(() => {
      const typeMap = {
        "people-to-car": "人找车",
        "car-to-people": "车找人",
        "goods-to-car": "货找车",
        "car-to-goods": "车找货"
      };
      return typeMap[carpoolInfo.value.type] || "人找车";
    });
    common_vendor.onMounted(() => {
      var _a;
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight || 20;
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = ((_a = currentPage.$page) == null ? void 0 : _a.options) || {};
      if (options && options.id) {
        carpoolId.value = options.id;
        if (options.type) {
          carpoolInfo.value.type = options.type;
        }
        getCarpoolDetail();
      }
    });
    const goBack = () => {
      common_vendor.index.navigateBack({
        delta: 1
      });
    };
    const onShareAppMessage = (options) => {
      return {
        title: `${typeText.value}：${carpoolInfo.value.startPoint} → ${carpoolInfo.value.endPoint}`,
        path: `/carpool-package/pages/carpool/detail/index?id=${carpoolId.value}&type=${carpoolInfo.value.type}`,
        imageUrl: "/static/images/share-cover.png",
        desc: `出发时间: ${carpoolInfo.value.departureDate} ${carpoolInfo.value.departureTime}`,
        success: (res) => {
          common_vendor.index.showToast({
            title: "转发成功",
            icon: "success"
          });
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at carpool-package/pages/carpool/detail/index.vue:357", "转发失败", err);
        }
      };
    };
    const onShareTimeline = () => {
      return {
        title: `${typeText.value}：${carpoolInfo.value.startPoint} → ${carpoolInfo.value.endPoint}`,
        query: `id=${carpoolId.value}&type=${carpoolInfo.value.type}`,
        imageUrl: "/static/images/share-cover.png",
        success: (res) => {
          common_vendor.index.showToast({
            title: "转发成功",
            icon: "success"
          });
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at carpool-package/pages/carpool/detail/index.vue:375", "转发失败", err);
        }
      };
    };
    const getCarpoolDetail = () => {
      common_vendor.index.__f__("log", "at carpool-package/pages/carpool/detail/index.vue:384", "获取拼车ID:", carpoolId.value, "类型:", carpoolInfo.value.type);
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      const mockData = {
        // 车找人的示例数据
        "car-to-people": {
          id: carpoolId.value,
          type: "car-to-people",
          startPoint: "磁州城区-汽车站",
          startAddress: "磁州区新世纪广场北侧100米",
          endPoint: "邯郸火车站",
          endAddress: "邯郸市丛台区陵西大街61号",
          distance: "35",
          departureDate: "2023-10-15",
          departureTime: "14:30",
          availableSeats: 3,
          price: "25",
          username: "李师傅",
          avatar: "/static/images/avatar/user2.png",
          isVerified: true,
          isPremium: true,
          publishTime: "2023-10-14 09:30",
          expiryTime: "2023-10-17 09:30",
          contactPhone: "13987654321",
          userId: "user456",
          carModel: "大众朗逸",
          carColor: "白色",
          carPlate: "冀D·12345",
          remark: "准时发车，不等人，请提前到达上车点。可提供小费上门接送。",
          rating: 4.8,
          ratingCount: 125,
          ratingTags: ["准时守信", "驾驶平稳", "热情礼貌", "车内整洁"]
        },
        // 人找车的示例数据
        "people-to-car": {
          id: carpoolId.value,
          type: "people-to-car",
          startPoint: "磁县人民医院",
          startAddress: "磁县人民医院南门",
          endPoint: "邯郸东站",
          endAddress: "邯郸市丛台区邯郸东站",
          distance: "30",
          departureDate: "2023-10-16",
          departureTime: "09:30",
          passengers: 2,
          price: "20",
          username: "王先生",
          avatar: "/static/images/avatar/user3.png",
          isVerified: true,
          isPremium: false,
          publishTime: "2023-10-15 08:30",
          expiryTime: "2023-10-18 08:30",
          contactPhone: "13812345678",
          userId: "user789",
          remark: "两人行李少，希望拼车去高铁站。",
          rating: 4.8,
          ratingCount: 125,
          ratingTags: ["准时守信", "驾驶平稳", "热情礼貌", "车内整洁"]
        },
        // 货找车的示例数据
        "goods-to-car": {
          id: carpoolId.value,
          type: "goods-to-car",
          startPoint: "磁县商贸城",
          startAddress: "磁县商贸城西门",
          endPoint: "邯郸市区",
          endAddress: "邯郸市丛台区中心广场",
          distance: "28",
          departureDate: "2023-10-17",
          departureTime: "13:00",
          price: "40",
          username: "张先生",
          avatar: "/static/images/avatar/user4.png",
          isVerified: true,
          isPremium: false,
          publishTime: "2023-10-16 10:30",
          expiryTime: "2023-10-19 10:30",
          contactPhone: "13698765432",
          userId: "user101",
          remark: "一箱水果，约20公斤，需要送到市区。",
          rating: 4.8,
          ratingCount: 125,
          ratingTags: ["准时守信", "驾驶平稳", "热情礼貌", "车内整洁"]
        },
        // 车找货的示例数据
        "car-to-goods": {
          id: carpoolId.value,
          type: "car-to-goods",
          startPoint: "邯郸物流园",
          startAddress: "邯郸市物流园区北门",
          endPoint: "磁县",
          endAddress: "磁县城区",
          distance: "32",
          departureDate: "2023-10-18",
          departureTime: "16:00",
          price: "35",
          username: "赵师傅",
          avatar: "/static/images/avatar/user5.png",
          isVerified: true,
          isPremium: true,
          publishTime: "2023-10-17 14:30",
          expiryTime: "2023-10-20 14:30",
          contactPhone: "13765432109",
          userId: "user202",
          carModel: "五菱小卡",
          carColor: "白色",
          carPlate: "冀D·54321",
          remark: "回程车辆，可带小件货物，当天到达。",
          rating: 4.8,
          ratingCount: 125,
          ratingTags: ["准时守信", "驾驶平稳", "热情礼貌", "车内整洁"]
        }
      };
      setTimeout(() => {
        const type = carpoolInfo.value.type || "car-to-people";
        const originalType = carpoolInfo.value.type;
        carpoolInfo.value = mockData[type] || mockData["car-to-people"];
        carpoolInfo.value.type = originalType;
        common_vendor.index.hideLoading();
      }, 500);
    };
    const showActionSheet = () => {
      common_vendor.index.showActionSheet({
        itemList: ["评价司机", "举报", "刷新", "复制信息"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              rateDriver();
              break;
            case 1:
              reportCarpool();
              break;
            case 2:
              refreshCarpool();
              break;
            case 3:
              copyInfo();
              break;
          }
        }
      });
    };
    const reportCarpool = () => {
      common_vendor.index.navigateTo({
        url: `/pages/carpool/report?id=${carpoolId.value}`
      });
    };
    const refreshCarpool = () => {
      common_vendor.index.showLoading({
        title: "刷新中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "刷新成功",
          icon: "success"
        });
      }, 1e3);
    };
    const showPromotionTool = () => {
      common_vendor.index.navigateTo({
        url: `/subPackages/promotion/pages/promotion-tool?type=carpool&id=${carpoolInfo.value.id}`
      });
    };
    const copyInfo = () => {
      const info = `${typeText.value}：${carpoolInfo.value.startPoint} → ${carpoolInfo.value.endPoint}，出发时间：${carpoolInfo.value.departureDate} ${carpoolInfo.value.departureTime}，联系人：${carpoolInfo.value.username}，电话：${carpoolInfo.value.contactPhone}`;
      common_vendor.index.setClipboardData({
        data: info,
        success: () => {
          common_vendor.index.showToast({
            title: "已复制到剪贴板",
            icon: "success"
          });
        }
      });
    };
    const toggleFavorite = () => {
      isFavorite.value = !isFavorite.value;
      common_vendor.index.showToast({
        title: isFavorite.value ? "收藏成功" : "已取消收藏",
        icon: "success"
      });
    };
    const callDriver = () => {
      common_vendor.index.makePhoneCall({
        phoneNumber: carpoolInfo.value.phone
      });
    };
    const rateDriver = () => {
      const contactHistory = common_vendor.index.getStorageSync("contactHistory") || [];
      const hasContacted = contactHistory.some((item) => item.userId === carpoolInfo.value.userId);
      if (hasContacted) {
        common_vendor.index.navigateTo({
          url: `/carpool-package/pages/carpool/my/create-rating?driverId=${carpoolInfo.value.userId}&phoneNumber=${carpoolInfo.value.contactPhone}&carpoolId=${carpoolInfo.value.id}&startLocation=${encodeURIComponent(carpoolInfo.value.startPoint)}&endLocation=${encodeURIComponent(carpoolInfo.value.endPoint)}&departureTime=${carpoolInfo.value.departureTime}&departureDate=${carpoolInfo.value.departureDate}`
        });
      } else {
        common_vendor.index.showModal({
          title: "提示",
          content: "您需要先联系司机后才能进行评价，是否立即联系司机？",
          success: (res) => {
            if (res.confirm) {
              callDriver();
            }
          }
        });
      }
    };
    const sendMessage = () => {
      common_vendor.index.__f__("log", "at carpool-package/pages/carpool/detail/index.vue:631", "发送私信");
      common_vendor.index.navigateTo({
        url: `/pages/message/chat?userId=${carpoolInfo.value.userId}&username=${encodeURIComponent(carpoolInfo.value.username)}&avatar=${encodeURIComponent(carpoolInfo.value.avatar)}&type=carpool&carpoolId=${carpoolInfo.value.id}`
      });
    };
    __expose({
      onShareAppMessage,
      onShareTimeline
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: common_assets._imports_1$4,
        d: common_vendor.o(showActionSheet),
        e: statusBarHeight.value + "px",
        f: common_vendor.o(showPromotionTool),
        g: common_vendor.p({
          position: {
            right: "30rpx",
            bottom: "180rpx"
          },
          size: "100rpx"
        }),
        h: common_vendor.t(typeText.value),
        i: carpoolInfo.value.isVerified
      }, carpoolInfo.value.isVerified ? {
        j: common_vendor.p({
          d: "M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z",
          fill: "currentColor"
        }),
        k: common_vendor.p({
          width: "14",
          height: "14",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        })
      } : {}, {
        l: common_vendor.n(carpoolInfo.value.type),
        m: common_vendor.t(carpoolInfo.value.startPoint),
        n: carpoolInfo.value.startAddress
      }, carpoolInfo.value.startAddress ? {
        o: common_vendor.t(carpoolInfo.value.startAddress)
      } : {}, {
        p: common_vendor.t(carpoolInfo.value.distance),
        q: common_vendor.t(carpoolInfo.value.endPoint),
        r: carpoolInfo.value.endAddress
      }, carpoolInfo.value.endAddress ? {
        s: common_vendor.t(carpoolInfo.value.endAddress)
      } : {}, {
        t: common_vendor.t(carpoolInfo.value.startPoint),
        v: common_vendor.t(carpoolInfo.value.endPoint),
        w: common_vendor.t(carpoolInfo.value.distance),
        x: common_vendor.t(carpoolInfo.value.estimatedDuration),
        y: carpoolInfo.value.tripNotes
      }, carpoolInfo.value.tripNotes ? {
        z: common_vendor.t(carpoolInfo.value.tripNotes)
      } : {}, {
        A: common_vendor.t(carpoolInfo.value.departureDate),
        B: common_vendor.t(carpoolInfo.value.departureTime),
        C: carpoolInfo.value.type === "people-to-car"
      }, carpoolInfo.value.type === "people-to-car" ? {
        D: common_vendor.t(carpoolInfo.value.passengers)
      } : {}, {
        E: carpoolInfo.value.type === "car-to-people"
      }, carpoolInfo.value.type === "car-to-people" ? {
        F: common_vendor.t(carpoolInfo.value.availableSeats)
      } : {}, {
        G: carpoolInfo.value.price
      }, carpoolInfo.value.price ? {
        H: common_vendor.t(carpoolInfo.value.price)
      } : {}, {
        I: carpoolInfo.value.avatar,
        J: common_vendor.t(carpoolInfo.value.username),
        K: carpoolInfo.value.isVerified
      }, carpoolInfo.value.isVerified ? {
        L: common_vendor.p({
          d: "M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z",
          fill: "currentColor"
        }),
        M: common_vendor.p({
          width: "12",
          height: "12",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        })
      } : {}, {
        N: carpoolInfo.value.isPremium
      }, carpoolInfo.value.isPremium ? {} : {}, {
        O: common_vendor.t(carpoolInfo.value.publishTime),
        P: carpoolInfo.value.type === "car-to-people"
      }, carpoolInfo.value.type === "car-to-people" ? common_vendor.e({
        Q: common_vendor.t(carpoolInfo.value.ratingCount || 0),
        R: (carpoolInfo.value.rating || 0) * 20 + "%",
        S: common_vendor.t(carpoolInfo.value.rating || 0),
        T: carpoolInfo.value.ratingTags && carpoolInfo.value.ratingTags.length > 0
      }, carpoolInfo.value.ratingTags && carpoolInfo.value.ratingTags.length > 0 ? {
        U: common_vendor.f(carpoolInfo.value.ratingTags, (tag, index, i0) => {
          return {
            a: common_vendor.t(tag),
            b: index
          };
        })
      } : {}) : {}, {
        V: carpoolInfo.value.type.includes("car-to")
      }, carpoolInfo.value.type.includes("car-to") ? {
        W: common_vendor.t(carpoolInfo.value.carModel),
        X: common_vendor.t(carpoolInfo.value.carColor),
        Y: common_vendor.t(carpoolInfo.value.carPlate)
      } : {}, {
        Z: carpoolInfo.value.remark
      }, carpoolInfo.value.remark ? {
        aa: common_vendor.t(carpoolInfo.value.remark)
      } : {}, {
        ab: common_assets._imports_2$3,
        ac: common_vendor.t(carpoolInfo.value.publishTime),
        ad: common_vendor.t(carpoolInfo.value.expiryTime),
        ae: common_assets._imports_3$3,
        af: isFavorite.value ? "/static/images/tabbar/a收藏选中.png" : "/static/images/tabbar/a收藏.png",
        ag: common_vendor.t(isFavorite.value ? "已收藏" : "收藏"),
        ah: common_vendor.o(toggleFavorite),
        ai: common_assets._imports_4,
        aj: common_vendor.o(rateDriver),
        ak: common_assets._imports_5,
        al: common_vendor.o(sendMessage),
        am: common_assets._imports_6,
        an: common_vendor.o(callDriver)
      });
    };
  }
};
_sfc_main.__runtimeHooks = 6;
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/carpool-package/pages/carpool/detail/index.js.map
