<template>
  <view class="detail-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">分销员详情</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 基本信息卡片 -->
    <view class="info-card">
      <view class="distributor-header">
        <image class="avatar" :src="distributorInfo.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
        <view class="info-content">
          <view class="name-wrap">
            <text class="name">{{distributorInfo.name || '未知用户'}}</text>
            <text class="level-tag" :style="{ backgroundColor: distributorInfo.levelColor || '#67C23A' }">
              {{distributorInfo.levelName || '普通分销员'}}
            </text>
            <text class="status-tag" :class="getStatusClass(distributorInfo.status)">
              {{getStatusText(distributorInfo.status)}}
            </text>
          </view>
          <text class="phone">{{distributorInfo.phone || '无联系方式'}}</text>
        </view>
      </view>
      
      <view class="info-grid">
        <view class="info-item">
          <text class="item-label">注册时间</text>
          <text class="item-value">{{formatDate(distributorInfo.createdAt)}}</text>
        </view>
        <view class="info-item">
          <text class="item-label">邀请码</text>
          <text class="item-value">{{distributorInfo.inviteCode || '无'}}</text>
        </view>
        <view class="info-item">
          <text class="item-label">上级分销员</text>
          <text class="item-value">{{distributorInfo.parentName || '无'}}</text>
        </view>
        <view class="info-item">
          <text class="item-label">团队人数</text>
          <text class="item-value">{{distributorInfo.teamCount || 0}}人</text>
        </view>
      </view>
      
      <view class="action-btns">
        <block v-if="distributorInfo.status === 'pending'">
          <view class="action-btn approve" @click="approveDistributor">通过申请</view>
          <view class="action-btn reject" @click="rejectDistributor">拒绝申请</view>
        </block>
        
        <block v-else-if="distributorInfo.status === 'active'">
          <view class="action-btn disable" @click="disableDistributor">禁用分销员</view>
          <view class="action-btn set-level" @click="setLevel">设置等级</view>
        </block>
        
        <block v-else-if="distributorInfo.status === 'disabled'">
          <view class="action-btn enable" @click="enableDistributor">启用分销员</view>
        </block>
      </view>
    </view>
    
    <!-- 数据概览 -->
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">数据概览</text>
      </view>
      
      <view class="stats-grid">
        <view class="stats-item">
          <text class="stats-value">{{distributorInfo.orderCount || 0}}</text>
          <text class="stats-label">推广订单</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">¥{{formatAmount(distributorInfo.commissionTotal)}}</text>
          <text class="stats-label">累计佣金</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">¥{{formatAmount(distributorInfo.commissionAvailable)}}</text>
          <text class="stats-label">可提现佣金</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">¥{{formatAmount(distributorInfo.commissionWithdrawn)}}</text>
          <text class="stats-label">已提现佣金</text>
        </view>
      </view>
    </view>
    
    <!-- 选项卡 -->
    <view class="tabs">
      <view 
        class="tab-item" 
        :class="{ 'active': activeTab === 'orders' }"
        @click="activeTab = 'orders'"
      >
        <text>推广订单</text>
      </view>
      <view 
        class="tab-item" 
        :class="{ 'active': activeTab === 'team' }"
        @click="activeTab = 'team'"
      >
        <text>团队成员</text>
      </view>
      <view 
        class="tab-item" 
        :class="{ 'active': activeTab === 'commission' }"
        @click="activeTab = 'commission'"
      >
        <text>佣金记录</text>
      </view>
      <view 
        class="tab-item" 
        :class="{ 'active': activeTab === 'withdraw' }"
        @click="activeTab = 'withdraw'"
      >
        <text>提现记录</text>
      </view>
    </view>
    
    <!-- 内容区域 -->
    <view class="tab-content">
      <!-- 推广订单 -->
      <view v-if="activeTab === 'orders'">
        <view class="order-list" v-if="orders.length > 0">
          <view 
            v-for="(item, index) in orders" 
            :key="index" 
            class="order-item"
          >
            <view class="order-header">
              <text class="order-id">订单号：{{item.orderNo}}</text>
              <text class="order-status">{{item.statusText}}</text>
            </view>
            
            <view class="order-content">
              <view class="product-info">
                <image class="product-image" :src="item.productImage" mode="aspectFill"></image>
                <view class="product-detail">
                  <text class="product-name">{{item.productName}}</text>
                  <view class="product-meta">
                    <text class="product-price">¥{{item.productPrice}}</text>
                    <text class="product-quantity">x{{item.quantity}}</text>
                  </view>
                </view>
              </view>
              
              <view class="order-footer">
                <view class="order-time">{{formatDate(item.createdAt)}}</view>
                <view class="order-amount">
                  <text>订单金额：</text>
                  <text class="amount">¥{{item.orderAmount}}</text>
                </view>
                <view class="commission-info">
                  <text>佣金：</text>
                  <text class="commission">¥{{item.commission}}</text>
                  <text class="commission-status" :class="getCommissionStatusClass(item.commissionStatus)">
                    {{getCommissionStatusText(item.commissionStatus)}}
                  </text>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-state" v-else-if="!orderLoading">
          <image class="empty-image" src="/static/images/empty-order.png" mode="aspectFit"></image>
          <text class="empty-text">暂无推广订单</text>
        </view>
        
        <!-- 加载中 -->
        <view class="loading-state" v-if="orderLoading">
          <view class="loading-icon"></view>
          <text class="loading-text">加载中...</text>
        </view>
        
        <!-- 分页 -->
        <view class="pagination" v-if="orders.length > 0 && !orderLoading">
          <view class="page-info">
            <text>共 {{orderPagination.total}} 条记录，当前 {{orderPagination.current}}/{{orderPagination.totalPages}} 页</text>
          </view>
          <view class="page-actions">
            <view 
              class="page-btn prev" 
              :class="{ 'disabled': orderPagination.current <= 1 }"
              @click="prevOrderPage"
            >上一页</view>
            <view 
              class="page-btn next" 
              :class="{ 'disabled': orderPagination.current >= orderPagination.totalPages }"
              @click="nextOrderPage"
            >下一页</view>
          </view>
        </view>
      </view>
      
      <!-- 团队成员 -->
      <view v-if="activeTab === 'team'">
        <view class="team-list" v-if="teamMembers.length > 0">
          <view 
            v-for="(item, index) in teamMembers" 
            :key="index" 
            class="team-item"
            @click="viewTeamMember(item)"
          >
            <view class="member-info">
              <image class="member-avatar" :src="item.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
              <view class="member-detail">
                <view class="member-name-wrap">
                  <text class="member-name">{{item.name}}</text>
                  <text class="member-level" :style="{ backgroundColor: item.levelColor || '#67C23A' }">{{item.levelName}}</text>
                </view>
                <text class="member-phone">{{item.phone}}</text>
              </view>
            </view>
            
            <view class="member-stats">
              <view class="member-stat">
                <text class="stat-label">推广订单</text>
                <text class="stat-value">{{item.orderCount || 0}}</text>
              </view>
              <view class="member-stat">
                <text class="stat-label">累计佣金</text>
                <text class="stat-value">¥{{formatAmount(item.commissionTotal)}}</text>
              </view>
              <view class="member-stat">
                <text class="stat-label">团队人数</text>
                <text class="stat-value">{{item.teamCount || 0}}</text>
              </view>
            </view>
            
            <view class="member-footer">
              <text class="join-time">加入时间：{{formatDate(item.createdAt)}}</text>
              <view class="arrow-right"></view>
            </view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-state" v-else-if="!teamLoading">
          <image class="empty-image" src="/static/images/empty-team.png" mode="aspectFit"></image>
          <text class="empty-text">暂无团队成员</text>
        </view>
        
        <!-- 加载中 -->
        <view class="loading-state" v-if="teamLoading">
          <view class="loading-icon"></view>
          <text class="loading-text">加载中...</text>
        </view>
        
        <!-- 分页 -->
        <view class="pagination" v-if="teamMembers.length > 0 && !teamLoading">
          <view class="page-info">
            <text>共 {{teamPagination.total}} 条记录，当前 {{teamPagination.current}}/{{teamPagination.totalPages}} 页</text>
          </view>
          <view class="page-actions">
            <view 
              class="page-btn prev" 
              :class="{ 'disabled': teamPagination.current <= 1 }"
              @click="prevTeamPage"
            >上一页</view>
            <view 
              class="page-btn next" 
              :class="{ 'disabled': teamPagination.current >= teamPagination.totalPages }"
              @click="nextTeamPage"
            >下一页</view>
          </view>
        </view>
      </view>
      
      <!-- 佣金记录 -->
      <view v-if="activeTab === 'commission'">
        <view class="commission-list" v-if="commissions.length > 0">
          <view 
            v-for="(item, index) in commissions" 
            :key="index" 
            class="commission-item"
          >
            <view class="commission-header">
              <view class="commission-type" :class="getCommissionTypeClass(item.type)">
                <text>{{getCommissionTypeText(item.type)}}</text>
              </view>
              <text class="commission-amount" :class="{ 'income': item.amount > 0, 'expense': item.amount < 0 }">
                {{item.amount > 0 ? '+' : ''}}{{formatAmount(item.amount)}}
              </text>
            </view>
            
            <view class="commission-content">
              <text class="commission-desc">{{item.description || '佣金记录'}}</text>
              <text class="commission-order" v-if="item.orderNo">订单号：{{item.orderNo}}</text>
              <text class="commission-time">{{formatDateTime(item.createdAt)}}</text>
            </view>
            
            <view class="commission-footer">
              <text class="commission-status" :class="getCommissionStatusClass(item.status)">
                {{getCommissionStatusText(item.status)}}
              </text>
            </view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-state" v-else-if="!commissionLoading">
          <image class="empty-image" src="/static/images/empty-commission.png" mode="aspectFit"></image>
          <text class="empty-text">暂无佣金记录</text>
        </view>
        
        <!-- 加载中 -->
        <view class="loading-state" v-if="commissionLoading">
          <view class="loading-icon"></view>
          <text class="loading-text">加载中...</text>
        </view>
        
        <!-- 分页 -->
        <view class="pagination" v-if="commissions.length > 0 && !commissionLoading">
          <view class="page-info">
            <text>共 {{commissionPagination.total}} 条记录，当前 {{commissionPagination.current}}/{{commissionPagination.totalPages}} 页</text>
          </view>
          <view class="page-actions">
            <view 
              class="page-btn prev" 
              :class="{ 'disabled': commissionPagination.current <= 1 }"
              @click="prevCommissionPage"
            >上一页</view>
            <view 
              class="page-btn next" 
              :class="{ 'disabled': commissionPagination.current >= commissionPagination.totalPages }"
              @click="nextCommissionPage"
            >下一页</view>
          </view>
        </view>
      </view>
      
      <!-- 提现记录 -->
      <view v-if="activeTab === 'withdraw'">
        <view class="withdraw-list" v-if="withdrawals.length > 0">
          <view 
            v-for="(item, index) in withdrawals" 
            :key="index" 
            class="withdraw-item"
          >
            <view class="withdraw-header">
              <text class="withdraw-id">提现单号：{{item.withdrawNo}}</text>
              <text class="withdraw-status" :class="getWithdrawStatusClass(item.status)">
                {{getWithdrawStatusText(item.status)}}
              </text>
            </view>
            
            <view class="withdraw-content">
              <view class="withdraw-info">
                <text class="withdraw-label">提现金额</text>
                <text class="withdraw-amount">¥{{formatAmount(item.amount)}}</text>
              </view>
              
              <view class="withdraw-info">
                <text class="withdraw-label">手续费</text>
                <text class="withdraw-fee">¥{{formatAmount(item.fee)}}</text>
              </view>
              
              <view class="withdraw-info">
                <text class="withdraw-label">实际到账</text>
                <text class="withdraw-actual">¥{{formatAmount(item.actualAmount)}}</text>
              </view>
              
              <view class="withdraw-info">
                <text class="withdraw-label">提现方式</text>
                <text class="withdraw-method">{{getWithdrawMethodText(item.method)}}</text>
              </view>
              
              <view class="withdraw-info">
                <text class="withdraw-label">提现账号</text>
                <text class="withdraw-account">{{item.account}}</text>
              </view>
              
              <view class="withdraw-info">
                <text class="withdraw-label">申请时间</text>
                <text class="withdraw-time">{{formatDateTime(item.createdAt)}}</text>
              </view>
              
              <view class="withdraw-info" v-if="item.processedAt">
                <text class="withdraw-label">处理时间</text>
                <text class="withdraw-time">{{formatDateTime(item.processedAt)}}</text>
              </view>
              
              <view class="withdraw-info" v-if="item.remark">
                <text class="withdraw-label">备注</text>
                <text class="withdraw-remark">{{item.remark}}</text>
              </view>
            </view>
            
            <view class="withdraw-footer" v-if="item.status === 'pending'">
              <view class="withdraw-btn approve" @click="approveWithdrawal(item)">通过</view>
              <view class="withdraw-btn reject" @click="rejectWithdrawal(item)">拒绝</view>
            </view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-state" v-else-if="!withdrawLoading">
          <image class="empty-image" src="/static/images/empty-withdraw.png" mode="aspectFit"></image>
          <text class="empty-text">暂无提现记录</text>
        </view>
        
        <!-- 加载中 -->
        <view class="loading-state" v-if="withdrawLoading">
          <view class="loading-icon"></view>
          <text class="loading-text">加载中...</text>
        </view>
        
        <!-- 分页 -->
        <view class="pagination" v-if="withdrawals.length > 0 && !withdrawLoading">
          <view class="page-info">
            <text>共 {{withdrawPagination.total}} 条记录，当前 {{withdrawPagination.current}}/{{withdrawPagination.totalPages}} 页</text>
          </view>
          <view class="page-actions">
            <view 
              class="page-btn prev" 
              :class="{ 'disabled': withdrawPagination.current <= 1 }"
              @click="prevWithdrawPage"
            >上一页</view>
            <view 
              class="page-btn next" 
              :class="{ 'disabled': withdrawPagination.current >= withdrawPagination.totalPages }"
              @click="nextWithdrawPage"
            >下一页</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import distributionService from '@/utils/distributionService';

// 分销员ID
const distributorId = ref('');

// 分销员详情
const distributorInfo = ref({});

// 当前激活的选项卡
const activeTab = ref('orders');

// 推广订单列表
const orders = ref([]);

// 订单加载状态
const orderLoading = ref(false);

// 订单分页信息
const orderPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0
});

// 团队成员列表
const teamMembers = ref([]);

// 团队加载状态
const teamLoading = ref(false);

// 团队分页信息
const teamPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0
});

// 佣金记录列表
const commissions = ref([]);

// 佣金加载状态
const commissionLoading = ref(false);

// 佣金分页信息
const commissionPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0
});

// 提现记录列表
const withdrawals = ref([]);

// 提现加载状态
const withdrawLoading = ref(false);

// 提现分页信息
const withdrawPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0
});

// 页面加载
onMounted(() => {
  // 获取路由参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.$page?.options || {};
  
  // 设置分销员ID
  distributorId.value = options.id || '';
  
  // 获取分销员详情
  if (distributorId.value) {
    getDistributorDetail();
  } else {
    uni.showToast({
      title: '缺少分销员ID',
      icon: 'none'
    });
    
    setTimeout(() => {
      goBack();
    }, 1500);
  }
});

// 获取分销员详情
const getDistributorDetail = async () => {
  try {
    uni.showLoading({
      title: '加载中...',
      mask: true
    });
    
    const result = await distributionService.getDistributorDetail(distributorId.value);
    
    uni.hideLoading();
    
    if (result) {
      distributorInfo.value = result;
    } else {
      uni.showToast({
        title: '获取分销员详情失败',
        icon: 'none'
      });
    }
  } catch (error) {
    uni.hideLoading();
    console.error('获取分销员详情失败', error);
    uni.showToast({
      title: '获取分销员详情失败',
      icon: 'none'
    });
  }
};

// 格式化金额
const formatAmount = (amount) => {
  return (amount || 0).toFixed(2);
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知';
  
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
};

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case 'active':
      return 'status-active';
    case 'disabled':
      return 'status-disabled';
    case 'pending':
      return 'status-pending';
    case 'rejected':
      return 'status-rejected';
    default:
      return '';
  }
};

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'active':
      return '已启用';
    case 'disabled':
      return '已禁用';
    case 'pending':
      return '待审核';
    case 'rejected':
      return '已拒绝';
    default:
      return '未知';
  }
};

// 审核通过
const approveDistributor = () => {
  uni.showModal({
    title: '审核通过',
    content: `确定通过 ${distributorInfo.value.name} 的分销员申请吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({
            title: '处理中...',
            mask: true
          });
          
          const result = await distributionService.reviewDistributorApplication({
            id: distributorId.value,
            status: 'approved'
          });
          
          uni.hideLoading();
          
          if (result.success) {
            uni.showToast({
              title: '审核通过成功',
              icon: 'success'
            });
            
            // 刷新详情
            getDistributorDetail();
          } else {
            uni.showModal({
              title: '审核失败',
              content: result.message || '请稍后再试',
              showCancel: false
            });
          }
        } catch (error) {
          uni.hideLoading();
          console.error('审核失败', error);
          uni.showToast({
            title: '审核失败',
            icon: 'none'
          });
        }
      }
    }
  });
};

// 拒绝申请
const rejectDistributor = () => {
  uni.showModal({
    title: '拒绝申请',
    content: `确定拒绝 ${distributorInfo.value.name} 的分销员申请吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({
            title: '处理中...',
            mask: true
          });
          
          const result = await distributionService.reviewDistributorApplication({
            id: distributorId.value,
            status: 'rejected'
          });
          
          uni.hideLoading();
          
          if (result.success) {
            uni.showToast({
              title: '已拒绝申请',
              icon: 'success'
            });
            
            // 刷新详情
            getDistributorDetail();
          } else {
            uni.showModal({
              title: '操作失败',
              content: result.message || '请稍后再试',
              showCancel: false
            });
          }
        } catch (error) {
          uni.hideLoading();
          console.error('操作失败', error);
          uni.showToast({
            title: '操作失败',
            icon: 'none'
          });
        }
      }
    }
  });
};

// 禁用分销员
const disableDistributor = () => {
  uni.showModal({
    title: '禁用分销员',
    content: `确定禁用 ${distributorInfo.value.name} 的分销员资格吗？禁用后该用户将无法进行分销活动。`,
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({
            title: '处理中...',
            mask: true
          });
          
          const result = await distributionService.toggleDistributorStatus({
            id: distributorId.value,
            status: 'disabled'
          });
          
          uni.hideLoading();
          
          if (result.success) {
            uni.showToast({
              title: '禁用成功',
              icon: 'success'
            });
            
            // 刷新详情
            getDistributorDetail();
          } else {
            uni.showModal({
              title: '操作失败',
              content: result.message || '请稍后再试',
              showCancel: false
            });
          }
        } catch (error) {
          uni.hideLoading();
          console.error('操作失败', error);
          uni.showToast({
            title: '操作失败',
            icon: 'none'
          });
        }
      }
    }
  });
};

// 启用分销员
const enableDistributor = () => {
  uni.showModal({
    title: '启用分销员',
    content: `确定启用 ${distributorInfo.value.name} 的分销员资格吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({
            title: '处理中...',
            mask: true
          });
          
          const result = await distributionService.toggleDistributorStatus({
            id: distributorId.value,
            status: 'active'
          });
          
          uni.hideLoading();
          
          if (result.success) {
            uni.showToast({
              title: '启用成功',
              icon: 'success'
            });
            
            // 刷新详情
            getDistributorDetail();
          } else {
            uni.showModal({
              title: '操作失败',
              content: result.message || '请稍后再试',
              showCancel: false
            });
          }
        } catch (error) {
          uni.hideLoading();
          console.error('操作失败', error);
          uni.showToast({
            title: '操作失败',
            icon: 'none'
          });
        }
      }
    }
  });
};

// 设置等级
const setLevel = () => {
  uni.navigateTo({
    url: `/subPackages/merchant-admin-marketing/pages/marketing/distribution/set-level?id=${distributorId.value}`
  });
};

// 返回上一页
const goBack = () => {
      uni.navigateBack();
};

// 显示帮助
const showHelp = () => {
      uni.showModal({
        title: '分销员详情帮助',
    content: '在此页面您可以查看分销员的详细信息，包括基本资料、推广订单、团队成员、佣金记录和提现记录等。您还可以对分销员进行审核、禁用/启用和设置等级等操作。',
        showCancel: false
      });
};

// 监听选项卡变化
watch(activeTab, (newVal) => {
  if (newVal === 'orders' && orders.value.length === 0) {
    getDistributionOrders();
  } else if (newVal === 'team' && teamMembers.value.length === 0) {
    getTeamMembers();
  } else if (newVal === 'commission' && commissions.value.length === 0) {
    getCommissionRecords();
  } else if (newVal === 'withdraw' && withdrawals.value.length === 0) {
    getWithdrawRecords();
  }
});

// 获取推广订单列表
const getDistributionOrders = async () => {
  try {
    orderLoading.value = true;
    
    const params = {
      distributorId: distributorId.value,
      page: orderPagination.current,
      pageSize: orderPagination.pageSize
    };
    
    const result = await distributionService.getDistributionOrders(params);
    
    if (result) {
      orders.value = result.list || [];
      
      // 更新分页信息
      orderPagination.current = result.pagination.current;
      orderPagination.total = result.pagination.total;
      orderPagination.totalPages = result.pagination.totalPages;
    }
  } catch (error) {
    console.error('获取推广订单列表失败', error);
    uni.showToast({
      title: '获取推广订单列表失败',
      icon: 'none'
    });
  } finally {
    orderLoading.value = false;
  }
};

// 获取佣金状态样式类
const getCommissionStatusClass = (status) => {
  switch (status) {
    case 'paid':
      return 'status-paid';
    case 'pending':
      return 'status-pending';
    case 'frozen':
      return 'status-frozen';
    case 'cancelled':
      return 'status-cancelled';
    default:
      return '';
  }
};

// 获取佣金状态文本
const getCommissionStatusText = (status) => {
  switch (status) {
    case 'paid':
      return '已结算';
    case 'pending':
      return '待结算';
    case 'frozen':
      return '已冻结';
    case 'cancelled':
      return '已取消';
    default:
      return '未知';
  }
};

// 上一页订单
const prevOrderPage = () => {
  if (orderPagination.current > 1) {
    orderPagination.current--;
    getDistributionOrders();
  }
};

// 下一页订单
const nextOrderPage = () => {
  if (orderPagination.current < orderPagination.totalPages) {
    orderPagination.current++;
    getDistributionOrders();
  }
};

// 获取团队成员列表
const getTeamMembers = async () => {
  try {
    teamLoading.value = true;
    
    const params = {
      distributorId: distributorId.value,
      page: teamPagination.current,
      pageSize: teamPagination.pageSize
    };
    
    const result = await distributionService.getTeamMembers(params);
    
    if (result) {
      teamMembers.value = result.list || [];
      
      // 更新分页信息
      teamPagination.current = result.pagination.current;
      teamPagination.total = result.pagination.total;
      teamPagination.totalPages = result.pagination.totalPages;
    }
  } catch (error) {
    console.error('获取团队成员列表失败', error);
    uni.showToast({
      title: '获取团队成员列表失败',
      icon: 'none'
    });
  } finally {
    teamLoading.value = false;
  }
};

// 查看团队成员详情
const viewTeamMember = (item) => {
  uni.navigateTo({
    url: `/subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail?id=${item.id}`
  });
};

// 上一页团队
const prevTeamPage = () => {
  if (teamPagination.current > 1) {
    teamPagination.current--;
    getTeamMembers();
  }
};

// 下一页团队
const nextTeamPage = () => {
  if (teamPagination.current < teamPagination.totalPages) {
    teamPagination.current++;
    getTeamMembers();
  }
};

// 获取佣金记录列表
const getCommissionRecords = async () => {
  try {
    commissionLoading.value = true;
    
    const params = {
      distributorId: distributorId.value,
      page: commissionPagination.current,
      pageSize: commissionPagination.pageSize
    };
    
    const result = await distributionService.getCommissionList(params);
    
    if (result) {
      commissions.value = result.list || [];
      
      // 更新分页信息
      commissionPagination.current = result.pagination.current;
      commissionPagination.total = result.pagination.total;
      commissionPagination.totalPages = result.pagination.totalPages;
    }
  } catch (error) {
    console.error('获取佣金记录列表失败', error);
    uni.showToast({
      title: '获取佣金记录列表失败',
      icon: 'none'
    });
  } finally {
    commissionLoading.value = false;
  }
};

// 格式化日期时间
const formatDateTime = (dateString) => {
  if (!dateString) return '未知';
  
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hour = String(date.getHours()).padStart(2, '0');
  const minute = String(date.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hour}:${minute}`;
};

// 获取佣金类型样式类
const getCommissionTypeClass = (type) => {
  switch (type) {
    case 'order':
      return 'type-order';
    case 'withdraw':
      return 'type-withdraw';
    case 'refund':
      return 'type-refund';
    case 'adjust':
      return 'type-adjust';
    default:
      return 'type-other';
  }
};

// 获取佣金类型文本
const getCommissionTypeText = (type) => {
  switch (type) {
    case 'order':
      return '订单佣金';
    case 'withdraw':
      return '佣金提现';
    case 'refund':
      return '订单退款';
    case 'adjust':
      return '佣金调整';
    default:
      return '其他';
  }
};

// 上一页佣金
const prevCommissionPage = () => {
  if (commissionPagination.current > 1) {
    commissionPagination.current--;
    getCommissionRecords();
  }
};

// 下一页佣金
const nextCommissionPage = () => {
  if (commissionPagination.current < commissionPagination.totalPages) {
    commissionPagination.current++;
    getCommissionRecords();
  }
};

// 获取提现记录列表
const getWithdrawRecords = async () => {
  try {
    withdrawLoading.value = true;
    
    const params = {
      distributorId: distributorId.value,
      page: withdrawPagination.current,
      pageSize: withdrawPagination.pageSize
    };
    
    const result = await distributionService.getWithdrawRecords(params);
    
    if (result) {
      withdrawals.value = result.list || [];
      
      // 更新分页信息
      withdrawPagination.current = result.pagination.current;
      withdrawPagination.total = result.pagination.total;
      withdrawPagination.totalPages = result.pagination.totalPages;
    }
  } catch (error) {
    console.error('获取提现记录列表失败', error);
    uni.showToast({
      title: '获取提现记录列表失败',
      icon: 'none'
    });
  } finally {
    withdrawLoading.value = false;
  }
};

// 获取提现状态样式类
const getWithdrawStatusClass = (status) => {
  switch (status) {
    case 'pending':
      return 'status-pending';
    case 'approved':
      return 'status-approved';
    case 'rejected':
      return 'status-rejected';
    case 'processing':
      return 'status-processing';
    case 'completed':
      return 'status-completed';
    case 'failed':
      return 'status-failed';
    default:
      return '';
  }
};

// 获取提现状态文本
const getWithdrawStatusText = (status) => {
  switch (status) {
    case 'pending':
      return '待审核';
    case 'approved':
      return '已通过';
    case 'rejected':
      return '已拒绝';
    case 'processing':
      return '处理中';
    case 'completed':
      return '已完成';
    case 'failed':
      return '提现失败';
    default:
      return '未知';
  }
};

// 获取提现方式文本
const getWithdrawMethodText = (method) => {
  switch (method) {
    case 'wechat':
      return '微信零钱';
    case 'alipay':
      return '支付宝';
    case 'bank':
      return '银行卡';
    default:
      return '其他';
  }
};

// 审核通过提现
const approveWithdrawal = (item) => {
  uni.showModal({
    title: '审核通过',
    content: `确定通过此笔 ${formatAmount(item.amount)}元 的提现申请吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({
            title: '处理中...',
            mask: true
          });
          
          const result = await distributionService.reviewWithdrawal({
            id: item.id,
            status: 'approved'
          });
          
          uni.hideLoading();
          
          if (result.success) {
            uni.showToast({
              title: '审核通过成功',
              icon: 'success'
            });
            
            // 刷新提现列表
            getWithdrawRecords();
          } else {
            uni.showModal({
              title: '审核失败',
              content: result.message || '请稍后再试',
              showCancel: false
            });
          }
        } catch (error) {
          uni.hideLoading();
          console.error('审核失败', error);
          uni.showToast({
            title: '审核失败',
            icon: 'none'
          });
        }
      }
    }
  });
};

// 拒绝提现
const rejectWithdrawal = (item) => {
  uni.showModal({
    title: '拒绝提现',
    content: '请输入拒绝原因',
    editable: true,
    placeholderText: '请输入拒绝原因',
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({
            title: '处理中...',
            mask: true
          });
          
          const result = await distributionService.reviewWithdrawal({
            id: item.id,
            status: 'rejected',
            remark: res.content || '管理员拒绝'
          });
          
          uni.hideLoading();
          
          if (result.success) {
            uni.showToast({
              title: '已拒绝提现',
              icon: 'success'
            });
            
            // 刷新提现列表
            getWithdrawRecords();
          } else {
            uni.showModal({
              title: '操作失败',
              content: result.message || '请稍后再试',
              showCancel: false
            });
          }
        } catch (error) {
          uni.hideLoading();
          console.error('操作失败', error);
          uni.showToast({
            title: '操作失败',
            icon: 'none'
          });
        }
      }
    }
  });
};

// 上一页提现
const prevWithdrawPage = () => {
  if (withdrawPagination.current > 1) {
    withdrawPagination.current--;
    getWithdrawRecords();
  }
};

// 下一页提现
const nextWithdrawPage = () => {
  if (withdrawPagination.current < withdrawPagination.totalPages) {
    withdrawPagination.current++;
    getWithdrawRecords();
  }
};
</script>

<style lang="scss">
.detail-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 88rpx 32rpx 30rpx;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);
}

.navbar-back {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24rpx;
  height: 24rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.navbar-right {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

/* 基本信息卡片 */
.info-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.distributor-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 30rpx;
  background-color: #f5f5f5;
}

.info-content {
  flex: 1;
}

.name-wrap {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}

.name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 16rpx;
}

.level-tag {
  font-size: 24rpx;
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
}

.status-tag {
  font-size: 24rpx;
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.status-active {
  background-color: #67C23A;
}

.status-disabled {
  background-color: #909399;
}

.status-pending {
  background-color: #E6A23C;
}

.status-rejected {
  background-color: #F56C6C;
}

.phone {
  font-size: 28rpx;
  color: #666;
}

.info-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item {
  width: 50%;
  padding: 10rpx;
  box-sizing: border-box;
}

.item-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}

.item-value {
  font-size: 28rpx;
  color: #333;
}

.action-btns {
  display: flex;
  justify-content: center;
  margin-top: 30rpx;
}

.action-btn {
  padding: 16rpx 30rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  margin: 0 16rpx;
}

.action-btn.approve {
  background-color: #67C23A;
  color: #fff;
}

.action-btn.reject {
  background-color: #F56C6C;
  color: #fff;
}

.action-btn.disable {
  background-color: #909399;
  color: #fff;
}

.action-btn.enable {
  background-color: #409EFF;
  color: #fff;
}

.action-btn.set-level {
  background-color: #6B0FBE;
  color: #fff;
}

/* 数据概览 */
.stats-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.card-header {
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.stats-grid {
  display: flex;
  flex-wrap: wrap;
}

.stats-item {
  width: 50%;
  padding: 20rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 26rpx;
  color: #999;
}

/* 选项卡 */
.tabs {
  display: flex;
  background: #FFFFFF;
  margin: 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #6B0FBE;
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #6B0FBE;
  border-radius: 2rpx;
}

/* 内容区域 */
.tab-content {
  margin: 0 30rpx 30rpx;
}

.placeholder {
  height: 300rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.placeholder text {
  font-size: 28rpx;
  color: #999;
}

/* 订单列表 */
.order-list {
  margin-bottom: 30rpx;
}

.order-item {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.order-id {
  font-size: 28rpx;
  color: #333;
}

.order-status {
  font-size: 28rpx;
  color: #6B0FBE;
}

.product-info {
  display: flex;
  margin-bottom: 20rpx;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}

.product-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-meta {
  display: flex;
  justify-content: space-between;
}

.product-price {
  font-size: 28rpx;
  color: #FF5722;
}

.product-quantity {
  font-size: 28rpx;
  color: #999;
}

.order-footer {
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.order-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.order-amount {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.amount {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.commission-info {
  font-size: 26rpx;
  color: #666;
  display: flex;
  align-items: center;
}

.commission {
  font-size: 28rpx;
  color: #FF5722;
  font-weight: 600;
  margin-right: 10rpx;
}

.commission-status {
  font-size: 24rpx;
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.status-paid {
  background-color: #67C23A;
}

.status-pending {
  background-color: #E6A23C;
}

.status-frozen {
  background-color: #409EFF;
}

.status-cancelled {
  background-color: #F56C6C;
}

/* 团队成员列表 */
.team-list {
  margin-bottom: 30rpx;
}

.team-item {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.member-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.member-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}

.member-detail {
  flex: 1;
}

.member-name-wrap {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.member-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-right: 16rpx;
}

.member-level {
  font-size: 22rpx;
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.member-phone {
  font-size: 24rpx;
  color: #999;
}

.member-stats {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}

.member-stat {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.member-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.join-time {
  font-size: 24rpx;
  color: #999;
}

.arrow-right {
  width: 16rpx;
  height: 16rpx;
  border-top: 2rpx solid #999;
  border-right: 2rpx solid #999;
  transform: rotate(45deg);
}

/* 佣金记录列表 */
.commission-list {
  margin-bottom: 30rpx;
}

.commission-item {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.commission-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.commission-type {
  font-size: 24rpx;
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.type-order {
  background-color: #409EFF;
}

.type-withdraw {
  background-color: #67C23A;
}

.type-refund {
  background-color: #F56C6C;
}

.type-adjust {
  background-color: #E6A23C;
}

.type-other {
  background-color: #909399;
}

.commission-amount {
  font-size: 32rpx;
  font-weight: 600;
}

.commission-amount.income {
  color: #67C23A;
}

.commission-amount.expense {
  color: #F56C6C;
}

.commission-content {
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}

.commission-desc {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.commission-order {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.commission-time {
  font-size: 24rpx;
  color: #999;
}

.commission-footer {
  display: flex;
  justify-content: flex-end;
}

.commission-status {
  font-size: 24rpx;
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

/* 提现记录列表 */
.withdraw-list {
  margin-bottom: 30rpx;
}

.withdraw-item {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.withdraw-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.withdraw-id {
  font-size: 28rpx;
  color: #333;
}

.withdraw-status {
  font-size: 24rpx;
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.status-pending {
  background-color: #E6A23C;
}

.status-approved {
  background-color: #409EFF;
}

.status-rejected {
  background-color: #F56C6C;
}

.status-processing {
  background-color: #909399;
}

.status-completed {
  background-color: #67C23A;
}

.status-failed {
  background-color: #F56C6C;
}

.withdraw-content {
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}

.withdraw-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.withdraw-info:last-child {
  margin-bottom: 0;
}

.withdraw-label {
  font-size: 26rpx;
  color: #999;
}

.withdraw-amount,
.withdraw-fee,
.withdraw-actual {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.withdraw-method,
.withdraw-account,
.withdraw-time,
.withdraw-remark {
  font-size: 26rpx;
  color: #666;
  max-width: 70%;
  text-align: right;
}

.withdraw-footer {
  display: flex;
  justify-content: flex-end;
}

.withdraw-btn {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  margin-left: 16rpx;
}

.withdraw-btn.approve {
  background-color: #67C23A;
  color: #fff;
}

.withdraw-btn.reject {
  background-color: #F56C6C;
  color: #fff;
}
</style>