<template>
  <view class="activity-showcase-container">
    <!-- 可滚动的弧形背景 -->
    <view class="content-bg-scroll"></view>
    
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <image class="back-icon" src="/static/images/tabbar/最新返回键.png" mode="aspectFit"></image>
        </view>
        <view class="navbar-title">{{ navbarTitle }}</view>
        <view class="navbar-right">
          <view class="close-btn" @click="closeActivityCenter">
            <text class="close-icon"></text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 内容区域 -->
    <scroll-view 
      class="content-scroll" 
      scroll-y 
      @scrolltolower="loadMore"
      :scroll-anchoring="true"
      :enhanced="true"
      :bounces="true"
      :show-scrollbar="false"
    >
      <!-- 顶部轮播图 -->
      <view class="banner-outer">
        <swiper
          class="banner-swiper"
          indicator-dots
          :autoplay="swiperConfig.autoplay"
          :circular="swiperConfig.circular"
          :indicator-color="swiperConfig.indicatorColor"
          :indicator-active-color="swiperConfig.indicatorActiveColor"
          :interval="swiperConfig.interval"
          :duration="swiperConfig.duration"
        >
        <swiper-item v-for="(banner, index) in banners" :key="index">
          <view class="banner-item">
            <image class="banner-image" :src="banner.image" mode="aspectFill" :lazy-load="true"></image>
            <view class="banner-info">
              <text class="banner-title">{{ banner.title }}</text>
              <text class="banner-desc">{{ banner.description }}</text>
            </view>
          </view>
        </swiper-item>
      </swiper>
      </view>
      
      <!-- 白色内容区域，包含所有剩余内容 -->
      <view class="white-content">
      <!-- 活动分类导航 -->
      <view class="category-nav">
          <view 
            class="category-item" 
            v-for="(category, index) in categories" 
            :key="index" 
            @click="navigateToCategory(category.type)"
          >
          <view class="category-icon" :class="[`icon-${category.type}`]">
              <svg v-if="category.type === 'flash'" class="icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="24" height="24">
                <path d="M13 3L4 14h7v7l9-11h-7V3z" fill="currentColor"></path>
            </svg>
              <svg v-else-if="category.type === 'group'" class="icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="24" height="24">
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2M9 7a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM23 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75" fill="currentColor"></path>
            </svg>
              <svg v-else-if="category.type === 'discount'" class="icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="24" height="24">
                <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" fill="currentColor"></path>
            </svg>
              <svg v-else-if="category.type === 'coupon'" class="icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="24" height="24">
                <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" fill="currentColor"></path>
            </svg>
          </view>
          <text class="category-name">{{ category.name }}</text>
        </view>
      </view>
      
      <!-- 同城活动模块 -->
        <ActivityCenter />
      
      <!-- 加载更多提示 -->
      <view class="loading-more" v-if="loading">
        <text>加载中...</text>
      </view>
      
      <!-- 到底了提示 -->
      <view class="no-more" v-if="noMore">
        <text>已经到底啦~</text>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
      </view>
    </scroll-view>
    
    <!-- 底部导航栏 -->
    <view class="tabbar">
      <view 
        class="tabbar-item" 
        :class="{active: currentTab === 'home'}" 
        @click="switchTab('home')"
        data-tab="home"
      >
        <view class="tab-icon home"></view>
        <text class="tabbar-text">首页</text>
        </view>
      <view 
        class="tabbar-item" 
        :class="{active: currentTab === 'discover'}" 
        @click="switchTab('discover')"
        data-tab="discover"
      >
        <view class="tab-icon discover"></view>
        <text class="tabbar-text">本地商城</text>
      </view>
      <view 
        class="tabbar-item" 
        :class="{active: currentTab === 'distribution'}" 
        @click="switchTab('distribution')"
        data-tab="distribution"
      >
        <view class="tab-icon distribution"></view>
        <text class="tabbar-text">分销</text>
      </view>
      <view 
        class="tabbar-item" 
        :class="{active: currentTab === 'message'}" 
        @click="switchTab('message')"
        data-tab="message"
      >
        <view class="tab-icon message">
          <view class="badge" v-if="unreadMessageCount > 0">{{ unreadMessageCount > 99 ? '99+' : unreadMessageCount }}</view>
        </view>
        <text class="tabbar-text">消息</text>
      </view>
            <view 
        class="tabbar-item" 
        :class="{active: currentTab === 'my'}" 
        @click="switchTab('my')"
        data-tab="my"
      >
        <view class="tab-icon user"></view>
        <text class="tabbar-text">我的</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import ActivityCenter from '../../components/activity/ActivityCenter.vue';
import configManager from '../../utils/configManager.js';

// 当前选中的标签页
const currentTab = ref('home');

// 未读消息数量
const unreadMessageCount = ref(3);

// 轮播图数据
const banners = ref([
  {
    image: '/static/images/activity/banner-1.jpg',
          title: '618年中大促',
          description: '全场低至5折起'
        },
        {
    image: '/static/images/activity/banner-2.jpg',
          title: '新人专享礼',
          description: '首单立减30元'
        },
        {
    image: '/static/images/activity/banner-3.jpg',
          title: '周末狂欢趴',
          description: '满200减50'
        }
]);

// 分类导航数据
const categories = ref([
        { name: '限时秒杀', type: 'flash' },
        { name: '拼团活动', type: 'group' },
        { name: '满减优惠', type: 'discount' },
        { name: '优惠券', type: 'coupon' }
]);

// 加载状态
const loading = ref(false);
const noMore = ref(false);

// 配置相关的计算属性
const navbarConfig = computed(() => configManager.getConfig('structure.navbar') || {})
const bannerConfig = computed(() => configManager.getConfig('structure.banner') || {})
const cardConfig = computed(() => configManager.getConfig('display.cardStyle') || {})

// 动态导航栏标题
const navbarTitle = computed(() => navbarConfig.value.title || '活动中心')

// 动态轮播图配置
const swiperConfig = computed(() => ({
  autoplay: bannerConfig.value.autoplay !== false,
  circular: bannerConfig.value.circular !== false,
  interval: bannerConfig.value.interval || 5000,
  duration: bannerConfig.value.duration || 500,
  indicatorColor: bannerConfig.value.indicatorColor || 'rgba(255, 255, 255, 0.6)',
  indicatorActiveColor: bannerConfig.value.indicatorActiveColor || '#ffffff'
}))

// 页面加载
onMounted(async () => {
  console.log('活动中心页面加载');

  // 初始化配置管理器
  try {
    await configManager.init()
    console.log('配置管理器初始化成功')
  } catch (error) {
    console.warn('配置管理器初始化失败，使用默认配置:', error)
  }

  // 获取未读消息数量
  getUnreadMessageCount();
});

// 返回上一页
function goBack() {
  uni.navigateBack();
}

// 关闭活动中心
function closeActivityCenter() {
  uni.navigateBack({
    delta: 1
  });
}
    
    // 导航到分类页面
function navigateToCategory(type) {
      let url = '';
      
      switch(type) {
        case 'flash':
      url = '/subPackages/activity-showcase/pages/flash-sale/index';
          break;
        case 'group':
      url = '/subPackages/activity-showcase/pages/group-buy/index';
          break;
        case 'discount':
      url = '/subPackages/activity-showcase/pages/discount/index';
          break;
        case 'coupon':
      url = '/subPackages/activity-showcase/pages/coupon/index';
          break;
        default:
      url = '/subPackages/activity-showcase/pages/index/index';
      }
      
      uni.navigateTo({ url });
}
    
    // 切换底部导航标签页
function switchTab(tab) {
  if (currentTab.value === tab) return;
      
  currentTab.value = tab;
      
      // 根据选中的标签页进行相应的导航
      switch(tab) {
        case 'home':
          // 已在首页，不需要导航
          break;
        case 'discover':
          uni.navigateTo({
            url: '/subPackages/activity-showcase/pages/discover/index'
          });
          break;
        case 'distribution':
          uni.navigateTo({
            url: '/subPackages/activity-showcase/pages/distribution/index'
          });
          break;
        case 'message':
          uni.navigateTo({
            url: '/subPackages/activity-showcase/pages/message/index'
          });
          break;
        case 'my':
          uni.navigateTo({
            url: '/subPackages/activity-showcase/pages/my/index'
          });
          break;
      }
}
    
    // 获取未读消息数量
function getUnreadMessageCount() {
      // 这里应该是从服务器获取未读消息数量
      // 示例中使用模拟数据
      setTimeout(() => {
    unreadMessageCount.value = Math.floor(Math.random() * 10);
      }, 1000);
}

// 加载更多
function loadMore() {
  if (loading.value || noMore.value) return;
  
  loading.value = true;
  
  // 模拟加载更多数据
      setTimeout(() => {
    // 这里可以根据实际情况添加更多数据
    // 示例中假设已经没有更多数据了
    noMore.value = true;
    loading.value = false;
  }, 1500);
}
</script>

<style scoped>
.activity-showcase-container {
  min-height: 100vh;
  background-color: #f7f7f7;
  padding-bottom: env(safe-area-inset-bottom);
  position: relative;
}

/* 可滚动的弧形背景 */
.content-bg-scroll {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 400rpx;
  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
  border-bottom-left-radius: 80rpx;
  border-bottom-right-radius: 80rpx;
  z-index: 1;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 64px);
  width: 100%;
  z-index: 100;
  padding-top: var(--status-bar-height, 25px);
  box-sizing: border-box;
  }
  
  .navbar-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
  height: 64px;
    padding-left: 30rpx;
    padding-right: 30rpx;
    box-sizing: border-box;
  }
  
  .back-btn {
  width: 60rpx;
  height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  color: #ffffff;
    transition: all 0.2s ease;
  position: relative;
  z-index: 10;
}
    
.back-btn:active {
      transform: scale(0.9);
      background: rgba(255, 255, 255, 0.3);
  }
  
  .navbar-title {
    font-size: 36rpx;
  font-weight: 600;
    color: #FFFFFF;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.navbar-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 60rpx; /* 限制宽度，确保不会有额外空间 */
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  transition: all 0.2s ease;
}

.close-btn:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.3);
}

.close-icon {
  font-size: 40rpx;
  font-weight: 300;
}

.back-icon {
  width: 50rpx;
  height: 50rpx;
}

/* 内容区域 */
.content-scroll {
  position: absolute;
  top: calc(var(--status-bar-height, 25px) + 64px);
  left: 0;
  right: 0;
  bottom: 100rpx;
  background-color: transparent;
  z-index: 2;
}

/* 轮播图 */
.banner-outer {
  position: relative;
  margin: 25rpx 30rpx 50rpx;
  border-radius: 30rpx;
  overflow: hidden;
  box-shadow: 0 25rpx 45rpx rgba(0, 0, 0, 0.35), 0 15rpx 25rpx rgba(0, 0, 0, 0.2);
  border: 12rpx solid #ffffff;
  transform: translateZ(0);
  z-index: 3;
  animation: float 6s ease-in-out infinite;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  padding: 2rpx;
  background-color: rgba(255, 255, 255, 0.8);
}

@keyframes float {
  0% { transform: translateY(0) translateZ(0); }
  50% { transform: translateY(-10rpx) translateZ(0); }
  100% { transform: translateY(0) translateZ(0); }
}

.banner-swiper {
  width: 100%;
  height: 300rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
}
  
  .banner-item {
    position: relative;
    width: 100%;
    height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FFFFFF;
}
    
    .banner-image {
      width: 100%;
      height: 100%;
  border-radius: 16rpx;
  box-shadow: inset 0 0 15rpx rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
    }
    
    .banner-info {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      padding: 30rpx 25rpx;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0));
  border-radius: 0 0 16rpx 16rpx;
      backdrop-filter: blur(10rpx);
}
      
      .banner-title {
        font-size: 32rpx;
        font-weight: 700;
        color: #FFFFFF;
        margin-bottom: 8rpx;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
      }
      
      .banner-desc {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.9);
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
      }

/* 白色内容区域 */
.white-content {
  background: #f8f9fc;
  position: relative;
  padding-top: 40rpx;
  padding-bottom: 20rpx;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  box-shadow: 0 -6rpx 20rpx rgba(0, 0, 0, 0.03);
  margin-top: -40rpx;
  z-index: 4;
}

/* 活动分类导航 */
.category-nav {
  display: flex;
  justify-content: space-around;
  padding: 15rpx 15rpx; /* 从25rpx减少到15rpx */
  background: rgba(255, 255, 255, 0.9);
  margin: 0 18rpx 20rpx; /* 修改上边距为0 */
  border-radius: 35rpx;
  box-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.1), 
              0 5rpx 15rpx rgba(0, 0, 0, 0.05),
              inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}
  
.category-nav::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
    z-index: 0;
  }
  
  .category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10rpx 0; /* 从15rpx减少到10rpx */
    width: 25%;
    transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    z-index: 1;
}
    
.category-item:active {
      transform: scale(0.92);
    }
    
    .category-icon {
      width: 80rpx; /* 从90rpx减少到80rpx */
      height: 80rpx; /* 从90rpx减少到80rpx */
      border-radius: 24rpx; /* 修改：从16rpx增加到24rpx，使圆角更大 */
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 8rpx; /* 从12rpx减少到8rpx */
      box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.2),
                  inset 0 2rpx 3rpx rgba(255, 255, 255, 0.5);
      position: relative;
      overflow: hidden;
      color: #ffffff;
}

.category-icon .icon {
      /* 移除旋转效果 */
}
      
.category-icon::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 50%;
        background: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
        border-radius: 24rpx 24rpx 0 0; /* 保持与正方形圆角一致，从16rpx更新为24rpx */
      }
      
.icon-flash {
        background: linear-gradient(135deg, #FF453A, #FF2D55);
      }
      
.icon-group {
        background: linear-gradient(135deg, #34C759, #30D158);
      }
      
.icon-discount {
        background: linear-gradient(135deg, #5E5CE6, #5856D6);
      }
      
.icon-coupon {
        background: linear-gradient(135deg, #FF9F0A, #FF9500);
    }
    
    .category-name {
      font-size: 24rpx; /* 从26rpx减少到24rpx */
      color: #333333;
      font-weight: 600;
      margin-top: 3rpx; /* 从5rpx减少到3rpx */
      text-shadow: 0 1rpx 0 rgba(255, 255, 255, 0.8);
}

/* 加载更多和到底了提示 */
.loading-more, .no-more {
  text-align: center;
  padding: 20rpx 0;
  font-size: 24rpx;
  color: #8E8E93;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-more::before {
    content: '';
    width: 28rpx;
    height: 28rpx;
    border: 3rpx solid #8E8E93;
    border-top-color: transparent;
    border-radius: 50%;
    margin-right: 8rpx;
    animation: loading 0.8s linear infinite;
}

@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
}

/* 底部导航栏 */
.tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #FFFFFF;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 99;
  border-top: 1rpx solid #EEEEEE;
}
  
  .tabbar-item {
  flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
  padding: 6px 0;
  box-sizing: border-box;
    position: relative;
}
    
.tabbar-item:active {
      transform: scale(0.9);
    }
    
.tabbar-item.active .tab-icon {
        transform: translateY(-5rpx);
      }
      
.tabbar-item.active .tabbar-text {
  color: #FF3B69;
        font-weight: 600;
        transform: translateY(-2rpx);
}
    
.tab-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  color: #999999;
      display: flex;
      justify-content: center;
      align-items: center;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 首页图标 */
.tab-icon.home {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E");
}

.tabbar-item.active[data-tab="home"] .tab-icon.home {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E");
}

/* 发现图标 */
.tab-icon.discover {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5.5-2.5l7.51-3.49L17.5 6.5 9.99 9.99 6.5 17.5zm5.5-6.6c.61 0 1.1.49 1.1 1.1s-.49 1.1-1.1 1.1-1.1-.49-1.1-1.1.49-1.1 1.1-1.1z'/%3E%3C/svg%3E");
}

.tabbar-item.active[data-tab="discover"] .tab-icon.discover {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5.5-2.5l7.51-3.49L17.5 6.5 9.99 9.99 6.5 17.5zm5.5-6.6c.61 0 1.1.49 1.1 1.1s-.49 1.1-1.1 1.1-1.1-.49-1.1-1.1.49-1.1 1.1-1.1z'/%3E%3C/svg%3E");
}

/* 分销图标 */
.tab-icon.distribution {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E");
}

.tabbar-item.active[data-tab="distribution"] .tab-icon.distribution {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E");
}

/* 消息图标 */
.tab-icon.message {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H5.17L4 17.17V4h16v12z'/%3E%3C/svg%3E");
}

.tabbar-item.active[data-tab="message"] .tab-icon.message {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E");
}

/* 我的图标 */
.tab-icon.user {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}

.tabbar-item.active[data-tab="my"] .tab-icon.user {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}

.tabbar-item.active .tab-icon {
  filter: drop-shadow(0 1px 2px rgba(255, 59, 105, 0.3));
      }
      
      .badge {
        position: absolute;
        top: -8rpx;
        right: -12rpx;
        min-width: 32rpx;
        height: 32rpx;
        border-radius: 16rpx;
        background: linear-gradient(135deg, #FF453A, #FF2D55);
        color: #FFFFFF;
        font-size: 18rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 6rpx;
        box-sizing: border-box;
        font-weight: 600;
        box-shadow: 0 2rpx 6rpx rgba(255, 45, 85, 0.3);
        border: 1rpx solid rgba(255, 255, 255, 0.8);
        transform: scale(0.9);
    }
    
    .tabbar-text {
      font-size: 22rpx;
      color: #8E8E93;
      margin-top: 2rpx;
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }
    
.tabbar-item::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%) scaleX(0);
      width: 30rpx;
      height: 4rpx;
  background: #FF3B69;
      border-radius: 2rpx;
      transition: transform 0.3s ease;
    }
    
.tabbar-item.active::after {
      transform: translateX(-50%) scaleX(1);
    }
</style>

 
 