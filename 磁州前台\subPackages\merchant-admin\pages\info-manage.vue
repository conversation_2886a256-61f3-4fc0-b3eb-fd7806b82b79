<template>
  <view class="info-manage-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">信息管理</view>
      <view class="navbar-right"></view>
    </view>

    <!-- 内容区域 -->
    <view class="content" :style="{ marginTop: (navbarHeight + 10) + 'px' }">
      <!-- 统计卡片 -->
      <view class="stats-card">
        <view class="stats-header">
          <text class="stats-title">信息统计</text>
          <text class="stats-subtitle">今日数据概览</text>
        </view>
        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-number">{{ statsData.totalInfos }}</text>
            <text class="stat-label">发布信息</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ statsData.topInfos }}</text>
            <text class="stat-label">置顶信息</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ statsData.todayViews }}</text>
            <text class="stat-label">今日浏览</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ statsData.todayLeads }}</text>
            <text class="stat-label">今日咨询</text>
          </view>
        </view>
      </view>

      <!-- 信息列表 -->
      <view class="info-list-card">
        <view class="list-header">
          <text class="list-title">我的信息</text>
          <view class="list-actions">
            <button class="action-btn" @click="refreshAllInfos">
              <image src="/static/images/tabbar/刷新.png" class="action-icon"></image>
              批量刷新
            </button>
          </view>
        </view>

        <view class="info-list">
          <view 
            class="info-item" 
            v-for="(item, index) in infoList" 
            :key="index"
            @click="viewInfoDetail(item)"
          >
            <view class="info-content">
              <view class="info-header">
                <text class="info-title">{{ item.title }}</text>
                <view class="info-status" :class="item.status">
                  <text class="status-text">{{ getStatusText(item.status) }}</text>
                </view>
              </view>
              
              <view class="info-meta">
                <text class="info-category">{{ item.category }}</text>
                <text class="info-time">{{ formatTime(item.publishTime) }}</text>
              </view>

              <view class="info-stats">
                <view class="stat-item">
                  <image src="/static/images/tabbar/浏览.png" class="stat-icon"></image>
                  <text class="stat-text">{{ item.views }}</text>
                </view>
                <view class="stat-item">
                  <image src="/static/images/tabbar/咨询.png" class="stat-icon"></image>
                  <text class="stat-text">{{ item.leads }}</text>
                </view>
                <view class="stat-item" v-if="item.isTop">
                  <image src="/static/images/tabbar/置顶.png" class="stat-icon"></image>
                  <text class="stat-text">置顶中</text>
                </view>
              </view>
            </view>

            <view class="info-actions">
              <!-- 使用新的广告按钮组件 -->
              <MerchantAdButtons
                :showTopButton="true"
                :showRefreshButton="true"
                :isTopDisabled="item.isTop"
                :merchantId="item.merchantId"
                :infoId="item.id.toString()"
                :remainingAds="remainingAds"
                :showTips="false"
                @adSuccess="handleAdSuccess"
                @adFailed="handleAdFailed"
                @adCancelled="handleAdCancelled"
              />
            </view>
          </view>

          <!-- 空状态 -->
          <view class="empty-state" v-if="infoList.length === 0">
            <image src="/static/images/empty-info.png" class="empty-icon"></image>
            <text class="empty-text">暂无发布信息</text>
            <button class="empty-action" @click="goToPublish">立即发布</button>
          </view>
        </view>
      </view>

      <!-- 广告次数提示卡片 -->
      <view class="ad-tips-card">
        <view class="tips-header">
          <image src="/static/images/tabbar/提示.png" class="tips-icon"></image>
          <text class="tips-title">广告次数说明</text>
        </view>
        <view class="tips-content">
          <view class="tip-item">
            <text class="tip-label">置顶广告：</text>
            <text class="tip-value">每日最多3次，置顶2小时</text>
          </view>
          <view class="tip-item">
            <text class="tip-label">刷新广告：</text>
            <text class="tip-value">每日最多5次，立即生效</text>
          </view>
          <view class="tip-item">
            <text class="tip-label">今日剩余：</text>
            <text class="tip-value">置顶{{ remainingAds.top }}次，刷新{{ remainingAds.refresh }}次</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import MerchantAdButtons from '@/components/MerchantAdButtons.vue';

// 响应式数据
const statusBarHeight = ref(20);
const navbarHeight = ref(64);

const statsData = reactive({
  totalInfos: 0,
  topInfos: 0,
  todayViews: 0,
  todayLeads: 0
});

const infoList = ref([]);

const remainingAds = reactive({
  top: 3,
  refresh: 5
});

// 方法
const goBack = () => {
  uni.navigateBack();
};

const goToPublish = () => {
  uni.navigateTo({
    url: '/subPackages/merchant-admin/pages/info-publish'
  });
};

const viewInfoDetail = (item) => {
  uni.navigateTo({
    url: `/subPackages/merchant-admin/pages/info-detail?id=${item.id}`
  });
};

const getStatusText = (status) => {
  const statusMap = {
    'active': '正常',
    'pending': '审核中',
    'rejected': '已拒绝',
    'expired': '已过期'
  };
  return statusMap[status] || '未知';
};

const formatTime = (time) => {
  const date = new Date(time);
  const now = new Date();
  const diff = now - date;
  
  if (diff < 60000) return '刚刚';
  if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
  if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
  return Math.floor(diff / 86400000) + '天前';
};

// 广告成功回调
const handleAdSuccess = (result) => {
  console.log('广告观看成功:', result);

  if (result.type === 'top') {
    // 找到对应的信息项并更新状态
    const item = infoList.value.find(info => info.id.toString() === result.data.info_id);
    if (item) {
      item.isTop = true;
      remainingAds.top--;
      statsData.topInfos++;
    }

    uni.showToast({
      title: '置顶成功！置顶2小时',
      icon: 'success'
    });
  } else if (result.type === 'refresh') {
    // 找到对应的信息项并更新时间
    const item = infoList.value.find(info => info.id.toString() === result.data.info_id);
    if (item) {
      item.publishTime = new Date().toISOString();
      remainingAds.refresh--;
    }

    uni.showToast({
      title: '刷新成功！',
      icon: 'success'
    });
  }
};

// 广告失败回调
const handleAdFailed = (type) => {
  console.log('广告观看失败:', type);
  uni.showToast({
    title: '广告播放失败，请重试',
    icon: 'none'
  });
};

// 广告取消回调
const handleAdCancelled = (type) => {
  console.log('用户取消观看广告:', type);
};

// 批量刷新所有信息
const refreshAllInfos = async () => {
  try {
    const result = await uni.showModal({
      title: '批量刷新',
      content: `批量刷新将消耗${infoList.value.length}次刷新次数，确定要继续吗？`,
      confirmText: '确定',
      cancelText: '取消'
    });

    if (!result.confirm) return;

    if (remainingAds.refresh < infoList.value.length) {
      uni.showToast({
        title: '刷新次数不足',
        icon: 'none'
      });
      return;
    }

    uni.showLoading({ title: '批量刷新中...' });

    // 模拟批量刷新
    await new Promise(resolve => setTimeout(resolve, 2000));

    infoList.value.forEach(item => {
      item.publishTime = new Date().toISOString();
    });

    remainingAds.refresh -= infoList.value.length;

    uni.hideLoading();
    uni.showToast({
      title: '批量刷新成功！',
      icon: 'success'
    });

  } catch (error) {
    uni.hideLoading();
    console.error('批量刷新失败:', error);
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none'
    });
  }
};

// 加载数据
const loadData = () => {
  // 模拟加载统计数据
  statsData.totalInfos = 8;
  statsData.topInfos = 2;
  statsData.todayViews = 156;
  statsData.todayLeads = 23;

  // 模拟加载信息列表
  infoList.value = [
    {
      id: 1,
      title: '磁州特色小吃店招聘服务员',
      category: '招聘求职',
      status: 'active',
      publishTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      views: 89,
      leads: 12,
      isTop: true,
      merchantId: 'M000001'
    },
    {
      id: 2,
      title: '磁州本地优质蔬菜配送服务',
      category: '生活服务',
      status: 'active',
      publishTime: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
      views: 67,
      leads: 8,
      isTop: false,
      merchantId: 'M000001'
    }
  ];
};

// 生命周期
onMounted(() => {
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight || 20;
  navbarHeight.value = statusBarHeight.value + 44;
  
  loadData();
});
</script>

<style>
/* 页面样式 */
.info-manage-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  z-index: 1000;
}

.navbar-left, .navbar-right {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 32rpx;
  height: 32rpx;
  filter: brightness(0) invert(1);
}

.navbar-title {
  color: #ffffff;
  font-size: 34rpx;
  font-weight: 600;
}

/* 内容区域 */
.content {
  padding: 30rpx;
}

/* 统计卡片 */
.stats-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.stats-header {
  margin-bottom: 30rpx;
}

.stats-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.stats-subtitle {
  font-size: 24rpx;
  color: #999;
}

.stats-grid {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #667eea;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 信息列表卡片 */
.info-list-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.list-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.list-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
}

.action-icon {
  width: 24rpx;
  height: 24rpx;
  filter: brightness(0) invert(1);
}

/* 信息项 */
.info-item {
  display: flex;
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
}

.info-item:active {
  transform: scale(0.98);
}

.info-content {
  flex: 1;
  margin-right: 20rpx;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.info-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  flex: 1;
  margin-right: 20rpx;
}

.info-status {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.info-status.active {
  background: #e6f7ff;
  color: #1890ff;
}

.info-status.pending {
  background: #fff7e6;
  color: #fa8c16;
}

.info-meta {
  display: flex;
  gap: 20rpx;
  margin-bottom: 16rpx;
}

.info-category, .info-time {
  font-size: 22rpx;
  color: #999;
}

.info-stats {
  display: flex;
  gap: 30rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.stat-icon {
  width: 20rpx;
  height: 20rpx;
  opacity: 0.6;
}

.stat-text {
  font-size: 22rpx;
  color: #666;
}

/* 信息操作按钮 */
.info-actions {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  min-width: 160rpx;
}

.ad-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 12rpx;
  border-radius: 12rpx;
  border: none;
  font-size: 22rpx;
  transition: all 0.3s ease;
}

.top-btn {
  background: linear-gradient(135deg, #ff9a00, #ff6a00);
  color: #fff;
}

.top-btn:disabled {
  background: #e0e0e0;
  color: #999;
}

.refresh-btn {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: #fff;
}

.btn-icon {
  width: 24rpx;
  height: 24rpx;
  filter: brightness(0) invert(1);
}

.btn-text {
  font-size: 20rpx;
  line-height: 1.2;
  text-align: center;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 0;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 30rpx;
  display: block;
}

.empty-action {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  border: none;
}

/* 广告提示卡片 */
.ad-tips-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.tips-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.tips-icon {
  width: 32rpx;
  height: 32rpx;
}

.tips-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.tips-content {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
}

.tip-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-label {
  font-size: 24rpx;
  color: #666;
}

.tip-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}
</style>
