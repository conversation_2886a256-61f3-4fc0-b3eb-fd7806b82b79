<template>
  <view class="life-service-container">
    <!-- 自定义导航栏 -->
    <custom-navbar :title="pageTitle" :show-back="true"></custom-navbar>
    
    <!-- 内容区域 -->
    <view class="content-container">
      <!-- 外卖红包 -->
      <block v-if="serviceType === 'takeout'">
        <view class="service-header">
          <view class="service-icon-container" style="background-color: #FFE8E0;">
            <svg class="service-icon" viewBox="0 0 24 24" width="32" height="32">
              <path fill="#FF6B6B" d="M15.5,21L14,8H16.23L15.1,3.46L16.84,3L18.09,8H22L20.5,21H15.5M5,11H10A3,3 0 0,1 13,14H2A3,3 0 0,1 5,11M13,18A3,3 0 0,1 10,21H5A3,3 0 0,1 2,18H13M3,15H8L9.5,16.5L11,15H12A1,1 0 0,1 13,16A1,1 0 0,1 12,17H3A1,1 0 0,1 2,16A1,1 0 0,1 3,15Z" />
            </svg>
          </view>
          <view class="service-info">
            <text class="service-title">外卖红包</text>
            <text class="service-desc">最高返2%-5%</text>
          </view>
        </view>
        
        <view class="platform-list">
          <view class="platform-item" @tap="goToPlatformService('meituan')">
            <image class="platform-logo" src="/static/images/cashback/platform-meituan.png" mode="aspectFit"></image>
            <view class="platform-info">
              <text class="platform-name">美团外卖</text>
              <text class="platform-desc">最高返5%</text>
            </view>
            <svg class="arrow-icon" viewBox="0 0 24 24" width="24" height="24">
              <path fill="#CCCCCC" d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
            </svg>
          </view>
          
          <view class="platform-item" @tap="goToPlatformService('eleme')">
            <image class="platform-logo" src="/static/images/cashback/platform-eleme.png" mode="aspectFit"></image>
            <view class="platform-info">
              <text class="platform-name">饿了么</text>
              <text class="platform-desc">最高返3%</text>
            </view>
            <svg class="arrow-icon" viewBox="0 0 24 24" width="24" height="24">
              <path fill="#CCCCCC" d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
            </svg>
          </view>
        </view>
      </block>
      
      <!-- 打车红包 -->
      <block v-if="serviceType === 'taxi'">
        <view class="service-header">
          <view class="service-icon-container" style="background-color: #FFF2D6;">
            <svg class="service-icon" viewBox="0 0 24 24" width="32" height="32">
              <path fill="#FFA726" d="M5,11L6.5,6.5H17.5L19,11M17.5,16A1.5,1.5 0 0,1 16,14.5A1.5,1.5 0 0,1 17.5,13A1.5,1.5 0 0,1 19,14.5A1.5,1.5 0 0,1 17.5,16M6.5,16A1.5,1.5 0 0,1 5,14.5A1.5,1.5 0 0,1 6.5,13A1.5,1.5 0 0,1 8,14.5A1.5,1.5 0 0,1 6.5,16M18.92,6C18.72,5.42 18.16,5 17.5,5H6.5C5.84,5 5.28,5.42 5.08,6L3,12V20A1,1 0 0,0 4,21H5A1,1 0 0,0 6,20V19H18V20A1,1 0 0,0 19,21H20A1,1 0 0,0 21,20V12L18.92,6Z" />
            </svg>
          </view>
          <view class="service-info">
            <text class="service-title">打车红包</text>
            <text class="service-desc">最高返2.4%-5%</text>
          </view>
        </view>
        
        <view class="platform-list">
          <view class="platform-item" @tap="goToPlatformService('didi')">
            <image class="platform-logo" src="/static/images/cashback/platform-didi.png" mode="aspectFit"></image>
            <view class="platform-info">
              <text class="platform-name">滴滴出行</text>
              <text class="platform-desc">最高返5%</text>
            </view>
            <svg class="arrow-icon" viewBox="0 0 24 24" width="24" height="24">
              <path fill="#CCCCCC" d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
            </svg>
          </view>
          
          <view class="platform-item" @tap="goToPlatformService('caocao')">
            <image class="platform-logo" src="/static/images/cashback/platform-caocao.png" mode="aspectFit"></image>
            <view class="platform-info">
              <text class="platform-name">曹操出行</text>
              <text class="platform-desc">最高返2.4%</text>
            </view>
            <svg class="arrow-icon" viewBox="0 0 24 24" width="24" height="24">
              <path fill="#CCCCCC" d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
            </svg>
          </view>
        </view>
      </block>
      
      <!-- 电影票 -->
      <block v-if="serviceType === 'movie'">
        <view class="service-header">
          <view class="service-icon-container" style="background-color: #FFE0EC;">
            <svg class="service-icon" viewBox="0 0 24 24" width="32" height="32">
              <path fill="#E91E63" d="M18,9H16V7H18M18,13H16V11H18M18,17H16V15H18M8,9H6V7H8M8,13H6V11H8M8,17H6V15H8M18,3V5H16V3H8V5H6V3H4V21H6V19H8V21H16V19H18V21H20V3H18Z" />
            </svg>
          </view>
          <view class="service-info">
            <text class="service-title">电影票8折起</text>
            <text class="service-desc">返10%</text>
          </view>
        </view>
        
        <view class="platform-list">
          <view class="platform-item" @tap="goToPlatformService('maoyan')">
            <image class="platform-logo" src="/static/images/cashback/platform-maoyan.png" mode="aspectFit"></image>
            <view class="platform-info">
              <text class="platform-name">猫眼电影</text>
              <text class="platform-desc">最高返10%</text>
            </view>
            <svg class="arrow-icon" viewBox="0 0 24 24" width="24" height="24">
              <path fill="#CCCCCC" d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
            </svg>
          </view>
          
          <view class="platform-item" @tap="goToPlatformService('taopiaopiao')">
            <image class="platform-logo" src="/static/images/cashback/platform-taopiaopiao.png" mode="aspectFit"></image>
            <view class="platform-info">
              <text class="platform-name">淘票票</text>
              <text class="platform-desc">最高返8%</text>
            </view>
            <svg class="arrow-icon" viewBox="0 0 24 24" width="24" height="24">
              <path fill="#CCCCCC" d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
            </svg>
          </view>
        </view>
      </block>
      
      <!-- 其他服务类型... -->
      <block v-if="serviceType === 'express'">
        <view class="service-header">
          <view class="service-icon-container" style="background-color: #E3F1FF;">
            <svg class="service-icon" viewBox="0 0 24 24" width="32" height="32">
              <path fill="#2196F3" d="M3,14H5V20H19V14H21V21A1,1 0 0,1 20,22H4A1,1 0 0,1 3,21V14M17,4H7V2H17V4M17.5,5L12,10.5L6.5,5H17.5M20,6.4L17.9,8.5L15.5,6.1L16.9,4.7L20,7.8V6.4M5.93,4.7L7.33,6.1L4.93,8.5L2.83,6.4V7.8L5.93,4.7Z" />
            </svg>
          </view>
          <view class="service-info">
            <text class="service-title">寄快递返现</text>
            <text class="service-desc">返15%</text>
          </view>
        </view>
        
        <view class="platform-list">
          <view class="platform-item" @tap="goToPlatformService('sf')">
            <image class="platform-logo" src="/static/images/cashback/platform-sf.png" mode="aspectFit"></image>
            <view class="platform-info">
              <text class="platform-name">顺丰速运</text>
              <text class="platform-desc">最高返15%</text>
            </view>
            <svg class="arrow-icon" viewBox="0 0 24 24" width="24" height="24">
              <path fill="#CCCCCC" d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
            </svg>
          </view>
          
          <view class="platform-item" @tap="goToPlatformService('jd-express')">
            <image class="platform-logo" src="/static/images/cashback/platform-jd-express.png" mode="aspectFit"></image>
            <view class="platform-info">
              <text class="platform-name">京东物流</text>
              <text class="platform-desc">最高返12%</text>
            </view>
            <svg class="arrow-icon" viewBox="0 0 24 24" width="24" height="24">
              <path fill="#CCCCCC" d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
            </svg>
          </view>
        </view>
      </block>
      
      <!-- 优惠券 -->
      <block v-if="serviceType === 'coupon'">
        <view class="service-header special-header">
          <view class="special-info">
            <text class="special-title">淘宝-搜了么</text>
            <text class="special-desc">免费奶茶喝到爽</text>
            <text class="special-subdesc">下单再返3元</text>
          </view>
          <view class="special-price">
            <text class="price-value">15元</text>
            <text class="price-tag">券</text>
          </view>
        </view>
        
        <view class="coupon-detail">
          <image class="coupon-image" src="/static/images/cashback/coupon-detail.png" mode="widthFix"></image>
          <view class="coupon-info">
            <text class="coupon-title">搜了么奶茶专享券</text>
            <text class="coupon-value">15元</text>
            <text class="coupon-condition">无门槛</text>
            <text class="coupon-valid">有效期：2023.6.1-2023.12.31</text>
            <button class="get-coupon-btn" @tap="getCoupon">立即领取</button>
          </view>
          <view class="coupon-desc">
            <text class="desc-title">使用说明</text>
            <text class="desc-item">1. 打开淘宝APP，搜索"搜了么"</text>
            <text class="desc-item">2. 进入搜了么小程序</text>
            <text class="desc-item">3. 选择奶茶商品下单</text>
            <text class="desc-item">4. 结算时选择使用优惠券</text>
            <text class="desc-item">5. 下单后返利将在确认收货后到账</text>
          </view>
        </view>
      </block>
      
      <!-- 会员充值 -->
      <block v-if="serviceType === 'vip'">
        <view class="service-header">
          <view class="service-icon-container" style="background-color: #E8F5E9;">
            <svg class="service-icon" viewBox="0 0 24 24" width="32" height="32">
              <path fill="#4CAF50" d="M12,8H4A2,2 0 0,0 2,10V14A2,2 0 0,0 4,16H5V20A1,1 0 0,0 6,21H8A1,1 0 0,0 9,20V16H12L17,20V4L12,8M21.5,12C21.5,13.71 20.54,15.26 19,16V8C20.53,8.75 21.5,10.3 21.5,12Z" />
            </svg>
          </view>
          <view class="service-info">
            <text class="service-title">会员充值</text>
            <text class="service-desc">3.6元起</text>
          </view>
        </view>
        
        <view class="vip-list">
          <view class="vip-item" v-for="(item, index) in vipList" :key="index" @tap="goToVipDetail(item)">
            <image class="vip-logo" :src="item.logo" mode="aspectFit"></image>
            <view class="vip-info">
              <text class="vip-name">{{ item.name }}</text>
              <text class="vip-desc">{{ item.desc }}</text>
            </view>
            <view class="vip-price">
              <text class="price-value">{{ item.price }}元起</text>
            </view>
          </view>
        </view>
      </block>
    </view>
  </view>
</template>

<script>
import CustomNavbar from '../../components/CustomNavbar.vue';

export default {
  components: {
    CustomNavbar
  },
  data() {
    return {
      serviceType: '',
      pageTitle: '',
      vipList: [
        {
          id: 1,
          name: '腾讯视频VIP',
          logo: '/static/images/cashback/vip-tencent.png',
          desc: '月卡/季卡/年卡',
          price: '19.8'
        },
        {
          id: 2,
          name: '爱奇艺VIP',
          logo: '/static/images/cashback/vip-iqiyi.png',
          desc: '月卡/季卡/年卡',
          price: '19.0'
        },
        {
          id: 3,
          name: '优酷VIP',
          logo: '/static/images/cashback/vip-youku.png',
          desc: '月卡/季卡/年卡',
          price: '18.8'
        },
        {
          id: 4,
          name: '芒果TV VIP',
          logo: '/static/images/cashback/vip-mgtv.png',
          desc: '月卡/季卡/年卡',
          price: '15.0'
        },
        {
          id: 5,
          name: '网易云音乐VIP',
          logo: '/static/images/cashback/vip-netease.png',
          desc: '月卡/季卡/年卡',
          price: '8.0'
        },
        {
          id: 6,
          name: 'QQ音乐VIP',
          logo: '/static/images/cashback/vip-qqmusic.png',
          desc: '月卡/季卡/年卡',
          price: '15.0'
        }
      ]
    };
  },
  onLoad(options) {
    this.serviceType = options.type || 'takeout';
    this.setPageTitle();
  },
  methods: {
    setPageTitle() {
      switch(this.serviceType) {
        case 'takeout':
          this.pageTitle = '外卖红包';
          break;
        case 'taxi':
          this.pageTitle = '打车红包';
          break;
        case 'movie':
          this.pageTitle = '电影票优惠';
          break;
        case 'express':
          this.pageTitle = '快递返现';
          break;
        case 'coupon':
          this.pageTitle = '优惠券';
          break;
        case 'vip':
          this.pageTitle = '会员充值';
          break;
        default:
          this.pageTitle = '生活服务';
      }
    },
    goToPlatformService(platform) {
      uni.showToast({
        title: '平台服务功能正在开发中',
        icon: 'none',
        duration: 2000
      });
    },
    goToVipDetail(vip) {
      uni.showToast({
        title: '会员充值功能正在开发中',
        icon: 'none',
        duration: 2000
      });
    },
    getCoupon() {
      uni.showLoading({
        title: '领取中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '领取成功',
          icon: 'success'
        });
      }, 1000);
    }
  }
};
</script>

<style lang="scss" scoped>
.life-service-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content-container {
  padding-top: calc(var(--status-bar-height) + 44px);
  padding-bottom: 20px;
}

.service-header {
  margin: 16px;
  padding: 20px;
  background-color: #FFFFFF;
  border-radius: 16px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  
  &.special-header {
    background: linear-gradient(135deg, #F5F0FF 0%, #EDE7F6 100%);
    padding: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .special-info {
      .special-title {
        font-size: 16px;
        color: #333333;
        margin-bottom: 6px;
        display: block;
      }
      
      .special-desc {
        font-size: 20px;
        color: #9C27B0;
        font-weight: 600;
        margin-bottom: 4px;
        display: block;
      }
      
      .special-subdesc {
        font-size: 14px;
        color: #666666;
        display: block;
      }
    }
    
    .special-price {
      background-color: #FF5252;
      border-radius: 24px;
      padding: 8px 16px;
      display: flex;
      align-items: center;
      
      .price-value {
        font-size: 24px;
        font-weight: 600;
        color: #FFFFFF;
      }
      
      .price-tag {
        font-size: 14px;
        color: #FFFFFF;
        background-color: rgba(255, 255, 255, 0.3);
        border-radius: 12px;
        padding: 2px 6px;
        margin-left: 4px;
      }
    }
  }
  
  .service-icon-container {
    width: 56px;
    height: 56px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
  }
  
  .service-info {
    .service-title {
      font-size: 18px;
      font-weight: 600;
      color: #333333;
      margin-bottom: 6px;
      display: block;
    }
    
    .service-desc {
      font-size: 14px;
      color: #9C27B0;
      display: block;
    }
  }
}

.platform-list {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 8px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  
  .platform-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #F0F0F0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .platform-logo {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      margin-right: 12px;
    }
    
    .platform-info {
      flex: 1;
      
      .platform-name {
        font-size: 16px;
        color: #333333;
        margin-bottom: 4px;
        display: block;
      }
      
      .platform-desc {
        font-size: 14px;
        color: #9C27B0;
        display: block;
      }
    }
    
    .arrow-icon {
      margin-left: 8px;
    }
  }
}

.coupon-detail {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  
  .coupon-image {
    width: 100%;
    border-radius: 12px;
    margin-bottom: 16px;
  }
  
  .coupon-info {
    padding: 16px 0;
    border-bottom: 1px dashed #EEEEEE;
    margin-bottom: 16px;
    
    .coupon-title {
      font-size: 18px;
      font-weight: 600;
      color: #333333;
      margin-bottom: 12px;
      display: block;
    }
    
    .coupon-value {
      font-size: 24px;
      font-weight: 700;
      color: #FF5252;
      margin-bottom: 6px;
      display: block;
    }
    
    .coupon-condition {
      font-size: 14px;
      color: #666666;
      margin-bottom: 6px;
      display: block;
    }
    
    .coupon-valid {
      font-size: 12px;
      color: #999999;
      margin-bottom: 16px;
      display: block;
    }
    
    .get-coupon-btn {
      width: 100%;
      height: 44px;
      background-color: #9C27B0;
      color: #FFFFFF;
      font-size: 16px;
      font-weight: 500;
      border-radius: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;
    }
  }
  
  .coupon-desc {
    .desc-title {
      font-size: 16px;
      font-weight: 600;
      color: #333333;
      margin-bottom: 12px;
      display: block;
    }
    
    .desc-item {
      font-size: 14px;
      color: #666666;
      margin-bottom: 8px;
      display: block;
      line-height: 1.5;
    }
  }
}

.vip-list {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 8px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  
  .vip-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #F0F0F0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .vip-logo {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      margin-right: 12px;
    }
    
    .vip-info {
      flex: 1;
      
      .vip-name {
        font-size: 16px;
        color: #333333;
        margin-bottom: 4px;
        display: block;
      }
      
      .vip-desc {
        font-size: 14px;
        color: #666666;
        display: block;
      }
    }
    
    .vip-price {
      .price-value {
        font-size: 16px;
        font-weight: 600;
        color: #FF5252;
      }
    }
  }
}
</style> 