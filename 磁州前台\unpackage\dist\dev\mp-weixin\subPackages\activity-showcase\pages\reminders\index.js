"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
if (!Array) {
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_uni_popup = common_vendor.resolveComponent("uni-popup");
  (_component_circle + _component_path + _component_svg + _component_line + _component_uni_popup)();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const currentTab = common_vendor.ref(0);
    const isRefreshing = common_vendor.ref(false);
    const remindersList = common_vendor.ref([]);
    const settingsPopup = common_vendor.ref(null);
    const settings = common_vendor.ref({
      activityStart: true,
      activityChange: true,
      registrationSuccess: true,
      activityCancel: true
    });
    const reminderTimeOptions = ["提前15分钟", "提前30分钟", "提前1小时", "提前2小时", "提前1天"];
    const reminderTimeIndex = common_vendor.ref(2);
    const reminderTabs = [
      { name: "全部", type: "all", emptyText: "暂无提醒", emptyImage: "/static/images/empty-reminders.png" },
      { name: "未读", type: "unread", emptyText: "暂无未读提醒", emptyImage: "/static/images/empty-unread.png" },
      { name: "活动", type: "activity", emptyText: "暂无活动提醒", emptyImage: "/static/images/empty-activity.png" },
      { name: "系统", type: "system", emptyText: "暂无系统提醒", emptyImage: "/static/images/empty-system.png" }
    ];
    const mockReminders = [
      {
        id: "1001",
        title: "活动即将开始",
        description: '您报名参加的"磁州文化节"将于明天开始，请准时参加。',
        category: "activity_start",
        type: "activity",
        time: "10分钟前",
        isRead: false,
        activityInfo: {
          id: "1001",
          name: "磁州文化节",
          date: "2024-06-15 09:00",
          location: "磁州文化广场",
          image: "/static/demo/activity1.jpg"
        }
      },
      {
        id: "1002",
        title: "报名成功通知",
        description: '恭喜您成功报名"亲子户外拓展活动"，请按时参加。',
        category: "registration_success",
        type: "activity",
        time: "2小时前",
        isRead: false,
        activityInfo: {
          id: "1002",
          name: "亲子户外拓展活动",
          date: "2024-05-28 14:00",
          location: "磁州森林公园",
          image: "/static/demo/activity2.jpg"
        }
      },
      {
        id: "1003",
        title: "活动地点变更",
        description: '"社区篮球赛"的活动地点已变更，请查看详情。',
        category: "activity_change",
        type: "activity",
        time: "昨天 15:30",
        isRead: true,
        activityInfo: {
          id: "1003",
          name: "社区篮球赛",
          date: "2024-05-20 10:00",
          location: "磁州体育中心(新)",
          image: "/static/demo/activity3.jpg"
        }
      },
      {
        id: "1004",
        title: "活动已取消",
        description: '很抱歉，"传统文化体验课"因故取消，报名费用将原路退回。',
        category: "activity_cancel",
        type: "activity",
        time: "昨天 10:15",
        isRead: true,
        activityInfo: {
          id: "1004",
          name: "传统文化体验课",
          date: "2024-05-15 15:00",
          location: "磁州文化馆",
          image: "/static/demo/activity4.jpg"
        }
      },
      {
        id: "1005",
        title: "系统通知",
        description: "您的账号已成功升级为VIP会员，可享受更多活动优惠。",
        category: "system",
        type: "system",
        time: "2天前",
        isRead: false
      }
    ];
    common_vendor.onMounted(() => {
      loadReminders();
    });
    const loadReminders = () => {
      remindersList.value = mockReminders;
    };
    const getRemindersByType = (type) => {
      if (type === "all") {
        return remindersList.value;
      } else if (type === "unread") {
        return remindersList.value.filter((reminder) => !reminder.isRead);
      } else {
        return remindersList.value.filter((reminder) => reminder.type === type);
      }
    };
    const switchTab = (index) => {
      currentTab.value = index;
    };
    const onSwiperChange = (e) => {
      currentTab.value = e.detail.current;
    };
    const onRefresh = () => {
      isRefreshing.value = true;
      setTimeout(() => {
        loadReminders();
        isRefreshing.value = false;
      }, 1e3);
    };
    const loadMore = () => {
      common_vendor.index.__f__("log", "at subPackages/activity-showcase/pages/reminders/index.vue:349", "加载更多提醒");
    };
    const getReminderIconPath = (category) => {
      const iconMap = {
        "activity_start": "M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z",
        "registration_success": "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z",
        "activity_change": "M19 21v-2a4 4 0 00-4-4H9a4 4 0 00-4 4v2M12 11a4 4 0 100-8 4 4 0 000 8z",
        "activity_cancel": "M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z",
        "system": "M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
      };
      return iconMap[category] || iconMap["system"];
    };
    const getReminderIconBg = (category) => {
      const bgMap = {
        "activity_start": "linear-gradient(135deg, #34C759 0%, #7ED321 100%)",
        "registration_success": "linear-gradient(135deg, #5AC8FA 0%, #90E0FF 100%)",
        "activity_change": "linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)",
        "activity_cancel": "linear-gradient(135deg, #FF3B30 0%, #FF9580 100%)",
        "system": "linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)"
      };
      return bgMap[category] || bgMap["system"];
    };
    const getPrimaryActionText = (category) => {
      const actionMap = {
        "activity_start": "查看详情",
        "registration_success": "查看订单",
        "activity_change": "查看变更",
        "activity_cancel": "查看退款",
        "system": "了解详情"
      };
      return actionMap[category] || "查看详情";
    };
    const getPrimaryActionBgColor = (category) => {
      const bgColorMap = {
        "activity_start": "linear-gradient(135deg, #34C759 0%, #7ED321 100%)",
        "registration_success": "linear-gradient(135deg, #5AC8FA 0%, #90E0FF 100%)",
        "activity_change": "linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)",
        "activity_cancel": "linear-gradient(135deg, #FF3B30 0%, #FF9580 100%)",
        "system": "linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)"
      };
      return bgColorMap[category] || "linear-gradient(135deg, #34C759 0%, #7ED321 100%)";
    };
    const handlePrimaryAction = (reminder) => {
      switch (reminder.category) {
        case "activity_start":
          if (reminder.activityInfo) {
            navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${reminder.activityInfo.id}`);
          }
          break;
        case "registration_success":
          navigateTo("/subPackages/activity-showcase/pages/orders/index?status=all");
          break;
        case "activity_change":
          if (reminder.activityInfo) {
            navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${reminder.activityInfo.id}`);
          }
          break;
        case "activity_cancel":
          navigateTo("/subPackages/activity-showcase/pages/orders/index?status=cancelled");
          break;
        default:
          viewReminderDetail(reminder);
      }
      if (!reminder.isRead) {
        markAsRead(reminder);
      }
    };
    const viewReminderDetail = (reminder) => {
      if (!reminder.isRead) {
        markAsRead(reminder);
      }
      if (reminder.activityInfo) {
        navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${reminder.activityInfo.id}`);
      } else {
        common_vendor.index.showModal({
          title: reminder.title,
          content: reminder.description,
          showCancel: false
        });
      }
    };
    const markAsRead = (reminder) => {
      const index = remindersList.value.findIndex((item) => item.id === reminder.id);
      if (index !== -1) {
        remindersList.value[index].isRead = true;
      }
    };
    const deleteReminder = (reminder) => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确认删除该提醒吗？",
        success: function(res) {
          if (res.confirm) {
            const index = remindersList.value.findIndex((item) => item.id === reminder.id);
            if (index !== -1) {
              remindersList.value.splice(index, 1);
            }
            common_vendor.index.showToast({
              title: "删除成功",
              icon: "success"
            });
          }
        }
      });
    };
    const showSettings = () => {
      settingsPopup.value.open();
    };
    const closeSettings = () => {
      settingsPopup.value.close();
    };
    const toggleSetting = (key) => {
      settings.value[key] = !settings.value[key];
    };
    const onReminderTimeChange = (e) => {
      reminderTimeIndex.value = e.detail.value;
    };
    const clearReadReminders = () => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确认清除所有已读提醒吗？",
        success: function(res) {
          if (res.confirm) {
            remindersList.value = remindersList.value.filter((item) => !item.isRead);
            common_vendor.index.showToast({
              title: "清除成功",
              icon: "success"
            });
          }
        }
      });
    };
    const clearAllReminders = () => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确认清除所有提醒吗？",
        success: function(res) {
          if (res.confirm) {
            remindersList.value = [];
            common_vendor.index.showToast({
              title: "清除成功",
              icon: "success"
            });
          }
        }
      });
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const navigateTo = (url) => {
      common_vendor.index.navigateTo({ url });
    };
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: common_vendor.p({
          cx: "12",
          cy: "12",
          r: "3",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        d: common_vendor.p({
          d: "M19.4 15a1.65 1.65 0 00.33 1.82l.06.06a2 2 0 010 2.83 2 2 0 01-2.83 0l-.06-.06a1.65 1.65 0 00-1.82-.33 1.65 1.65 0 00-1 1.51V21a2 2 0 01-2 2 2 2 0 01-2-2v-.09A1.65 1.65 0 009 19.4a1.65 1.65 0 00-1.82.33l-.06.06a2 2 0 01-2.83 0 2 2 0 010-2.83l.06-.06a1.65 1.65 0 00.33-1.82 1.65 1.65 0 00-1.51-1H3a2 2 0 01-2-2 2 2 0 012-2h.09A1.65 1.65 0 004.6 9a1.65 1.65 0 00-.33-1.82l-.06-.06a2 2 0 010-2.83 2 2 0 012.83 0l.06.06a1.65 1.65 0 001.82.33H9a1.65 1.65 0 001-1.51V3a2 2 0 012-2 2 2 0 012 2v.09a1.65 1.65 0 001 1.51 1.65 1.65 0 001.82-.33l.06-.06a2 2 0 012.83 0 2 2 0 010 2.83l-.06.06a1.65 1.65 0 00-.33 1.82V9a1.65 1.65 0 001.51 1H21a2 2 0 012 2 2 2 0 01-2 2h-.09a1.65 1.65 0 00-1.51 1z",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        e: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        f: common_vendor.o(showSettings),
        g: common_vendor.f(reminderTabs, (tab, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(tab.name),
            b: currentTab.value === index
          }, currentTab.value === index ? {} : {}, {
            c: index,
            d: currentTab.value === index ? 1 : "",
            e: common_vendor.o(($event) => switchTab(index), index)
          });
        }),
        h: common_vendor.f(reminderTabs, (tab, tabIndex, i0) => {
          return common_vendor.e({
            a: common_vendor.f(getRemindersByType(tab.type), (reminder, k1, i1) => {
              return common_vendor.e({
                a: "f3ba5f86-4-" + i0 + "-" + i1 + "," + ("f3ba5f86-3-" + i0 + "-" + i1),
                b: common_vendor.p({
                  d: getReminderIconPath(reminder.category),
                  stroke: "#FFFFFF",
                  ["stroke-width"]: "2",
                  ["stroke-linecap"]: "round",
                  ["stroke-linejoin"]: "round"
                }),
                c: "f3ba5f86-3-" + i0 + "-" + i1,
                d: getReminderIconBg(reminder.category),
                e: common_vendor.t(reminder.title),
                f: common_vendor.t(reminder.time),
                g: common_vendor.t(reminder.description),
                h: reminder.activityInfo
              }, reminder.activityInfo ? {
                i: reminder.activityInfo.image,
                j: common_vendor.t(reminder.activityInfo.name),
                k: common_vendor.t(reminder.activityInfo.date),
                l: common_vendor.t(reminder.activityInfo.location)
              } : {}, {
                m: common_vendor.t(getPrimaryActionText(reminder.category)),
                n: getPrimaryActionBgColor(reminder.category),
                o: common_vendor.o(($event) => handlePrimaryAction(reminder), reminder.id),
                p: !reminder.isRead
              }, !reminder.isRead ? {
                q: common_vendor.o(($event) => markAsRead(reminder), reminder.id)
              } : {
                r: common_vendor.o(($event) => deleteReminder(reminder), reminder.id)
              }, {
                s: common_vendor.o(($event) => viewReminderDetail(reminder), reminder.id),
                t: reminder.id,
                v: reminder.isRead ? 1 : ""
              });
            }),
            b: getRemindersByType(tab.type).length === 0
          }, getRemindersByType(tab.type).length === 0 ? {
            c: tab.emptyImage || "/static/images/empty-reminders.png",
            d: common_vendor.t(tab.emptyText || "暂无相关提醒"),
            e: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/list/index"), tabIndex)
          } : {}, {
            f: common_vendor.o(onRefresh, tabIndex),
            g: common_vendor.o(loadMore, tabIndex),
            h: tabIndex
          });
        }),
        i: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        j: isRefreshing.value,
        k: currentTab.value,
        l: common_vendor.o(onSwiperChange),
        m: common_vendor.p({
          x1: "18",
          y1: "6",
          x2: "6",
          y2: "18",
          stroke: "#333333",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        n: common_vendor.p({
          x1: "6",
          y1: "6",
          x2: "18",
          y2: "18",
          stroke: "#333333",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        o: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        p: common_vendor.o(closeSettings),
        q: settings.value.activityStart,
        r: common_vendor.o(($event) => toggleSetting("activityStart")),
        s: settings.value.activityChange,
        t: common_vendor.o(($event) => toggleSetting("activityChange")),
        v: settings.value.registrationSuccess,
        w: common_vendor.o(($event) => toggleSetting("registrationSuccess")),
        x: settings.value.activityCancel,
        y: common_vendor.o(($event) => toggleSetting("activityCancel")),
        z: common_vendor.t(reminderTimeOptions[reminderTimeIndex.value]),
        A: common_vendor.p({
          d: "M6 9l6 6 6-6",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        B: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        C: reminderTimeOptions,
        D: reminderTimeIndex.value,
        E: common_vendor.o(onReminderTimeChange),
        F: common_vendor.o(clearReadReminders),
        G: common_vendor.o(clearAllReminders),
        H: common_vendor.sr(settingsPopup, "f3ba5f86-5", {
          "k": "settingsPopup"
        }),
        I: common_vendor.p({
          type: "bottom"
        })
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f3ba5f86"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/reminders/index.js.map
