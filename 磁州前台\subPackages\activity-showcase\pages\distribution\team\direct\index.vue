<template>
  <view class="direct-team-page">
    <view class="nav-bar">
      <view class="nav-back" @click="goBack">
        <text class="iconfont icon-arrow-left"></text>
      </view>
      <view class="nav-title">直属团队</view>
    </view>

    <!-- 团队统计 -->
    <view class="team-stats">
      <view class="stat-card">
        <view class="stat-number">{{ teamStats.totalMembers }}</view>
        <view class="stat-label">团队人数</view>
      </view>
      <view class="stat-card">
        <view class="stat-number">{{ teamStats.activeMembers }}</view>
        <view class="stat-label">活跃成员</view>
      </view>
      <view class="stat-card">
        <view class="stat-number">¥{{ teamStats.totalEarnings }}</view>
        <view class="stat-label">团队收益</view>
      </view>
    </view>

    <!-- 搜索栏 -->
    <view class="search-bar">
      <input v-model="searchKeyword" placeholder="搜索团队成员" class="search-input" />
      <text class="iconfont icon-search search-icon"></text>
    </view>

    <!-- 团队成员列表 -->
    <view class="member-list">
      <view v-for="member in filteredMembers" :key="member.id" class="member-item">
        <view class="member-avatar">
          <image :src="member.avatar" mode="aspectFill" />
          <view v-if="member.isActive" class="active-badge"></view>
        </view>
        <view class="member-info">
          <view class="member-name">{{ member.nickname }}</view>
          <view class="member-level">{{ member.levelName }}</view>
          <view class="join-time">加入时间：{{ member.joinTime }}</view>
        </view>
        <view class="member-stats">
          <view class="stat-item">
            <text class="stat-value">{{ member.orderCount }}</text>
            <text class="stat-label">订单</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">¥{{ member.earnings }}</text>
            <text class="stat-label">收益</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 邀请按钮 -->
    <view class="invite-section">
      <button class="invite-btn" @click="inviteMembers">
        <text class="iconfont icon-add"></text>
        邀请新成员
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

const searchKeyword = ref('')
const teamStats = ref({
  totalMembers: 0,
  activeMembers: 0,
  totalEarnings: '0.00'
})
const members = ref([])

const filteredMembers = computed(() => {
  if (!searchKeyword.value) return members.value
  return members.value.filter(member => 
    member.nickname.includes(searchKeyword.value)
  )
})

onMounted(() => {
  loadTeamData()
})
</script>

<style lang="scss" scoped>
.team-stats {
  display: flex;
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .stat-card {
    flex: 1;
    text-align: center;
    
    .stat-number {
      font-size: 32rpx;
      font-weight: bold;
      color: #ff6b6b;
      margin-bottom: 10rpx;
    }
    
    .stat-label {
      font-size: 24rpx;
      color: #666;
    }
  }
}

.search-bar {
  position: relative;
  padding: 0 30rpx 20rpx;
  
  .search-input {
    width: 100%;
    background: #fff;
    padding: 20rpx 60rpx 20rpx 30rpx;
    border-radius: 50rpx;
    border: 1rpx solid #e0e0e0;
  }
  
  .search-icon {
    position: absolute;
    right: 60rpx;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
  }
}

.member-item {
  display: flex;
  align-items: center;
  background: #fff;
  padding: 30rpx;
  margin-bottom: 2rpx;
  
  .member-avatar {
    position: relative;
    margin-right: 20rpx;
    
    image {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
    }
    
    .active-badge {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 20rpx;
      height: 20rpx;
      background: #07c160;
      border-radius: 50%;
      border: 2rpx solid #fff;
    }
  }
  
  .member-info {
    flex: 1;
    
    .member-name {
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 8rpx;
    }
    
    .member-level {
      font-size: 24rpx;
      color: #ff6b6b;
      margin-bottom: 8rpx;
    }
    
    .join-time {
      font-size: 22rpx;
      color: #999;
    }
  }
  
  .member-stats {
    display: flex;
    
    .stat-item {
      text-align: center;
      margin-left: 30rpx;
      
      .stat-value {
        display: block;
        font-size: 24rpx;
        font-weight: bold;
        color: #333;
      }
      
      .stat-label {
        font-size: 20rpx;
        color: #999;
      }
    }
  }
}

.invite-section {
  padding: 30rpx;
  
  .invite-btn {
    width: 100%;
    background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
    color: #fff;
    height: 80rpx;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .iconfont {
      margin-right: 10rpx;
    }
  }
}
</style>