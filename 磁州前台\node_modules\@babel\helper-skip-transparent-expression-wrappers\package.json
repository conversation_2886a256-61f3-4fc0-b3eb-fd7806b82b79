{"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "7.27.1", "description": "Helper which skips types and parentheses", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}