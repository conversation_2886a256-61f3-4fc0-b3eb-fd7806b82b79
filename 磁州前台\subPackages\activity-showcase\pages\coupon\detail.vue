<template>
  <view class="coupon-detail-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <image class="back-icon" src="/static/images/tabbar/最新返回键.png" mode="aspectFit"></image>
        </view>
        <view class="navbar-title">优惠券详情</view>
        <view class="navbar-right"></view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container" v-if="loading">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <block v-else>
      <!-- 优惠券信息卡片 -->
      <view class="coupon-card" :style="{ marginTop: navbarHeight + 'px' }">
        <view class="coupon-header">
          <view class="coupon-amount">
            <text class="amount-symbol">¥</text>
            <text class="amount-value">{{ coupon.value }}</text>
          </view>
          <view class="coupon-condition">
            <text v-if="coupon.minAmount > 0">满{{ coupon.minAmount }}可用</text>
            <text v-else>无门槛</text>
          </view>
        </view>
        
        <view class="coupon-info">
          <view class="coupon-title">{{ coupon.title }}</view>
          <view class="coupon-time">{{ getTimeText(coupon.startTime, coupon.endTime) }}</view>
        </view>
        
        <view class="coupon-tag" v-if="coupon.tag">{{ coupon.tag }}</view>
        
        <!-- 分销组件 - 醒目位置 -->
        <distribution-section 
          :itemId="id"
          itemType="coupon"
          :itemTitle="coupon.title"
          :itemPrice="coupon.value"
          :commissionRate="coupon.commissionRate || 10"
        />
      </view>
      
      <!-- 使用说明 -->
      <view class="usage-card">
        <view class="card-header">
          <text class="card-title">使用说明</text>
        </view>
        <view class="usage-content">
          <view class="usage-item" v-for="(item, index) in coupon.usageRules" :key="index">
            <view class="item-dot"></view>
            <text class="item-text">{{ item }}</text>
          </view>
        </view>
      </view>
      
      <!-- 适用商品 -->
      <view class="products-card">
        <view class="card-header">
          <text class="card-title">适用商品</text>
          <view class="card-more" @click="viewAllProducts">
            <text>查看全部</text>
            <text class="arrow-icon">›</text>
          </view>
        </view>
        
        <view class="product-list">
          <view class="product-item" 
            v-for="(item, index) in recommendProducts" 
            :key="index"
            @click="goToProductDetail(item.id)">
            <image :src="item.image" mode="aspectFill" class="product-image"></image>
            <view class="product-info">
              <text class="product-name">{{ item.name }}</text>
              <view class="product-price">
                <text class="price-symbol">¥</text>
                <text class="price-value">{{ item.price }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </block>
    
    <!-- 底部按钮 -->
    <view class="bottom-bar">
      <view class="action-btn" @click="shareCoupon">
        <svg class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="24" height="24">
          <path d="M768 686.08c-32.768 0-61.952 12.8-84.48 32.768L399.872 573.952c2.56-10.24 4.608-20.992 4.608-32.256 0-11.264-2.048-22.016-4.608-32.256l283.648-144.896c22.528 19.968 51.712 32.768 84.48 32.768 70.144 0 127.488-57.344 127.488-127.488S838.144 142.336 768 142.336s-127.488 57.344-127.488 127.488c0 11.264 2.048 22.016 4.608 32.256L361.472 446.976c-22.528-19.968-51.712-32.768-84.48-32.768-70.144 0-127.488 57.344-127.488 127.488s57.344 127.488 127.488 127.488c32.768 0 61.952-12.8 84.48-32.768l283.648 144.896c-2.56 10.24-4.608 20.992-4.608 32.256 0 70.144 57.344 127.488 127.488 127.488s127.488-57.344 127.488-127.488S838.144 686.08 768 686.08z" fill="#8E8E93"></path>
        </svg>
        <text>分享</text>
      </view>
      <view class="action-btn" @click="collectCoupon">
        <svg class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="24" height="24">
          <path d="M512 896l-307.2-268.8c-8.533-7.467-17.067-16-25.6-25.6-76.8-76.8-76.8-198.4 0-275.2 76.8-76.8 198.4-76.8 275.2 0L512 384l57.6-57.6c76.8-76.8 198.4-76.8 275.2 0 76.8 76.8 76.8 198.4 0 275.2-8.533 8.533-17.067 17.067-25.6 25.6L512 896z" fill="#8E8E93"></path>
        </svg>
        <text>收藏</text>
      </view>
      <view class="get-btn" @click="getCoupon">
        <text>立即领取</text>
      </view>
    </view>
  </view>
</template>

<script>
import DistributionSection from '@/components/distribution-section.vue';

export default {
  components: {
    DistributionSection
  },
  data() {
    return {
      id: null,
      statusBarHeight: 20,
      navbarHeight: 82,
      loading: true,
      coupon: {},
      recommendProducts: [],
      commissionAmount: '20'
    }
  },
  onLoad(options) {
    if (options && options.id) {
      this.id = options.id;
    }
    
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 62; // 状态栏 + 标题栏高度
    
    // 模拟加载数据
    setTimeout(() => {
      this.loadCouponDetail();
    }, 500);
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 加载优惠券详情
    loadCouponDetail() {
      // 模拟API加载数据
      this.loading = true;
      
      // 在实际应用中，这里应该是从API获取数据
      setTimeout(() => {
        // 模拟数据
        this.coupon = {
          id: this.id || 1,
          title: '新人专享优惠券',
          description: '仅限新用户使用',
          type: 'cash',
          value: '50',
          minAmount: 199,
          startTime: new Date(),
          endTime: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000),
          tag: '新人专享',
          usageRules: [
            '仅限新用户使用',
            '每人限领1张',
            '有效期15天',
            '全场通用，部分特殊商品除外',
            '不可与其他优惠券叠加使用'
          ]
        };
        
        // 模拟推荐商品数据
        this.recommendProducts = [
          {
            id: 1,
            name: 'iPhone 13 Pro Max',
            price: '7999',
            image: '/static/demo/product1.jpg'
          },
          {
            id: 2,
            name: 'MacBook Pro 14英寸',
            price: '12999',
            image: '/static/demo/product2.jpg'
          },
          {
            id: 3,
            name: 'AirPods Pro 2',
            price: '1999',
            image: '/static/demo/product3.jpg'
          }
        ];
        
        this.loading = false;
      }, 1000);
    },
    
    // 获取活动时间文本
    getTimeText(startTime, endTime) {
      const start = new Date(startTime);
      const end = new Date(endTime);
      
      const startMonth = start.getMonth() + 1;
      const startDay = start.getDate();
      const endMonth = end.getMonth() + 1;
      const endDay = end.getDate();
      
      return `${startMonth}.${startDay} - ${endMonth}.${endDay}`;
    },
    
    // 查看全部适用商品
    viewAllProducts() {
      uni.navigateTo({
        url: `/subPackages/product/pages/list?couponId=${this.id}`
      });
    },
    
    // 跳转到商品详情
    goToProductDetail(productId) {
      uni.navigateTo({
        url: `/subPackages/product/pages/detail?id=${productId}`
      });
    },
    
    // 分享优惠券
    shareCoupon() {
      uni.showToast({
        title: '分享功能开发中',
        icon: 'none'
      });
    },
    
    // 收藏优惠券
    collectCoupon() {
      uni.showToast({
        title: '已收藏',
        icon: 'success'
      });
    },
    
    // 领取优惠券
    getCoupon() {
      uni.showLoading({
        title: '领取中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        
        uni.showToast({
          title: '领取成功',
          icon: 'success'
        });
        
        // 延迟返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }, 1000);
    }
  }
}
</script>

<style lang="scss" scoped>
.coupon-detail-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 120rpx; /* 为底部按钮留出空间 */
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
  
  .navbar-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #FF9500 0%, #FFCC00 100%);
  }
  
  .navbar-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding-top: var(--status-bar-height, 25px);
    padding-left: 30rpx;
    padding-right: 30rpx;
    box-sizing: border-box;
  }
  
  .back-btn {
    width: 40rpx;
    height: 40rpx;
    margin-right: 10rpx;
  }
  
  .back-icon {
    width: 100%;
    height: 100%;
  }
  
  .navbar-title {
    font-size: 18px;
    font-weight: 600;
    color: #FFFFFF;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  
  .loading-spinner {
    width: 80rpx;
    height: 80rpx;
    border: 6rpx solid #f3f3f3;
    border-top: 6rpx solid #FF9500;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }
  
  .loading-text {
    font-size: 28rpx;
    color: #8E8E93;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 优惠券信息卡片 */
.coupon-card {
  background: linear-gradient(135deg, #FF9500 0%, #FFCC00 100%);
  border-radius: 35rpx;
  padding: 40rpx 30rpx;
  margin: 30rpx;
  box-shadow: 0 15rpx 35rpx rgba(255, 149, 0, 0.2);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: -100rpx;
    right: -100rpx;
    width: 300rpx;
    height: 300rpx;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
  }
  
  &::after {
    content: '';
    position: absolute;
    bottom: -80rpx;
    left: -80rpx;
    width: 200rpx;
    height: 200rpx;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
  }
  
  .coupon-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 30rpx;
  }
  
  .coupon-amount {
    display: flex;
    align-items: baseline;
    color: #FFFFFF;
    
    .amount-symbol {
      font-size: 40rpx;
      font-weight: 600;
      margin-right: 5rpx;
    }
    
    .amount-value {
      font-size: 100rpx;
      font-weight: 800;
      line-height: 1;
      text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
    }
  }
  
  .coupon-condition {
    font-size: 28rpx;
    color: #FFFFFF;
    font-weight: 600;
    margin-top: 10rpx;
    background-color: rgba(255, 255, 255, 0.2);
    padding: 8rpx 20rpx;
    border-radius: 20rpx;
  }
  
  .coupon-info {
    text-align: center;
  }
  
  .coupon-title {
    font-size: 36rpx;
    font-weight: 700;
    color: #FFFFFF;
    margin-bottom: 10rpx;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  }
  
  .coupon-time {
    font-size: 26rpx;
    color: rgba(255, 255, 255, 0.9);
  }
  
  /* 分销入口 */
  .distribution-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 30rpx;
    margin-top: 20rpx;
    padding: 20rpx;
    background: linear-gradient(135deg, #FFF9F9 0%, #FFF5F5 100%);
    border-radius: 20rpx;
    box-shadow: 0 4rpx 12rpx rgba(255, 149, 0, 0.1);
    
    .distribution-left {
      display: flex;
      align-items: center;
    }
    
    .distribution-icon {
      width: 60rpx;
      height: 60rpx;
      margin-right: 15rpx;
      
      .icon-image {
        width: 100%;
        height: 100%;
      }
    }
    
    .distribution-info {
      display: flex;
      flex-direction: column;
    }
    
    .distribution-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333333;
      margin-bottom: 5rpx;
    }
    
    .distribution-desc {
      font-size: 24rpx;
      color: #FF9500;
      font-weight: 600;
    }
    
    .distribution-right {
      display: flex;
      align-items: center;
    }
    
    .distribution-btn {
      font-size: 26rpx;
      color: #FF9500;
      font-weight: 600;
    }
    
    .arrow-icon {
      font-size: 32rpx;
      color: #FF9500;
      margin-left: 5rpx;
    }
  }
  
  .coupon-tag {
    position: absolute;
    top: 30rpx;
    right: 30rpx;
    background-color: #FF3B30;
    color: #FFFFFF;
    font-size: 24rpx;
    font-weight: 600;
    padding: 6rpx 16rpx;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.3);
    transform: rotate(15deg);
  }
}

/* 使用说明卡片 */
.usage-card {
  background-color: #FFFFFF;
  border-radius: 35rpx;
  padding: 30rpx;
  margin: 0 30rpx 30rpx 30rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);
  
  .card-header {
    margin-bottom: 20rpx;
    border-bottom: 1rpx solid #EFEFEF;
    padding-bottom: 20rpx;
  }
  
  .card-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
  }
  
  .usage-content {
    display: flex;
    flex-direction: column;
  }
  
  .usage-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15rpx;
    
    .item-dot {
      width: 12rpx;
      height: 12rpx;
      border-radius: 50%;
      background-color: #FF9500;
      margin-top: 12rpx;
      margin-right: 15rpx;
      flex-shrink: 0;
    }
    
    .item-text {
      font-size: 28rpx;
      color: #666666;
      line-height: 1.6;
    }
  }
}

/* 适用商品卡片 */
.products-card {
  background-color: #FFFFFF;
  border-radius: 35rpx;
  padding: 30rpx;
  margin: 0 30rpx 30rpx 30rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    border-bottom: 1rpx solid #EFEFEF;
    padding-bottom: 20rpx;
    
    .card-more {
      display: flex;
      align-items: center;
      font-size: 26rpx;
      color: #8E8E93;
      
      .arrow-icon {
        font-size: 30rpx;
        margin-left: 5rpx;
      }
    }
  }
  
  .product-list {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10rpx;
  }
  
  .product-item {
    width: calc(33.33% - 20rpx);
    margin: 10rpx;
    border-radius: 20rpx;
    overflow: hidden;
    box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.05);
    background-color: #FFFFFF;
    transition: transform 0.3s ease;
    
    &:active {
      transform: scale(0.98);
    }
    
    .product-image {
      width: 100%;
      height: 180rpx;
      object-fit: cover;
    }
    
    .product-info {
      padding: 15rpx;
    }
    
    .product-name {
      font-size: 24rpx;
      color: #333333;
      line-height: 1.4;
      height: 68rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
    
    .product-price {
      display: flex;
      align-items: baseline;
      margin-top: 10rpx;
      
      .price-symbol {
        font-size: 22rpx;
        color: #FF9500;
      }
      
      .price-value {
        font-size: 28rpx;
        font-weight: 600;
        color: #FF9500;
      }
    }
  }
}

/* 底部按钮 */
.bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100rpx;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.05);
  z-index: 99;
  
  .action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 40rpx;
    
    .icon {
      margin-bottom: 5rpx;
    }
    
    text {
      font-size: 22rpx;
      color: #8E8E93;
    }
  }
  
  .get-btn {
    flex: 1;
    height: 80rpx;
    background: linear-gradient(135deg, #FF9500 0%, #FFCC00 100%);
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30rpx;
    font-weight: 600;
    color: #FFFFFF;
    box-shadow: 0 8rpx 16rpx rgba(255, 149, 0, 0.2);
    transition: transform 0.3s ease;
    
    &:active {
      transform: translateY(5rpx);
      box-shadow: 0 4rpx 8rpx rgba(255, 149, 0, 0.15);
    }
  }
}
</style> 