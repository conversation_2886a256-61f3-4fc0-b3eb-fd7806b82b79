package com.cizhou.user.controller;

import com.cizhou.common.core.page.PageQuery;
import com.cizhou.common.core.page.PageResult;
import com.cizhou.common.core.result.Result;
import com.cizhou.user.dto.DistributorAuditDTO;
import com.cizhou.user.dto.DistributorQueryDTO;
import com.cizhou.user.service.DistributorService;
import com.cizhou.user.vo.DistributorDetailVO;
import com.cizhou.user.vo.DistributorListVO;
import com.cizhou.user.vo.DistributorStatsVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 分销员管理控制器
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/distributors")
@RequiredArgsConstructor
@Tag(name = "分销员管理", description = "分销员管理相关接口")
public class DistributorController {

    private final DistributorService distributorService;

    @Operation(summary = "获取分销员列表")
    @GetMapping
    public Result<PageResult<DistributorListVO>> getDistributorList(@Valid DistributorQueryDTO queryDTO, 
                                                                    PageQuery pageQuery) {
        log.info("获取分销员列表，查询条件: {}", queryDTO);
        PageResult<DistributorListVO> result = distributorService.getDistributorList(queryDTO, pageQuery);
        return Result.success("获取分销员列表成功", result);
    }

    @Operation(summary = "获取分销员详情")
    @GetMapping("/{id}")
    public Result<DistributorDetailVO> getDistributorDetail(@Parameter(description = "分销员ID") @PathVariable Long id) {
        log.info("获取分销员详情，分销员ID: {}", id);
        DistributorDetailVO result = distributorService.getDistributorDetail(id);
        return Result.success("获取分销员详情成功", result);
    }

    @Operation(summary = "审核分销员申请")
    @PostMapping("/{id}/audit")
    public Result<Void> auditDistributor(@Parameter(description = "分销员ID") @PathVariable Long id,
                                         @Valid @RequestBody DistributorAuditDTO auditDTO) {
        log.info("审核分销员申请，分销员ID: {}, 审核信息: {}", id, auditDTO);
        distributorService.auditDistributor(id, auditDTO);
        return Result.success("分销员审核完成");
    }

    @Operation(summary = "批量审核分销员申请")
    @PostMapping("/batch/audit")
    public Result<Void> batchAuditDistributors(@RequestBody List<Long> ids,
                                               @Valid @RequestBody DistributorAuditDTO auditDTO) {
        log.info("批量审核分销员申请，分销员IDs: {}, 审核信息: {}", ids, auditDTO);
        distributorService.batchAuditDistributors(ids, auditDTO);
        return Result.success("批量审核分销员完成");
    }

    @Operation(summary = "更新分销员状态")
    @PutMapping("/{id}/status")
    public Result<Void> updateDistributorStatus(@Parameter(description = "分销员ID") @PathVariable Long id,
                                                @Parameter(description = "状态") @RequestParam Integer status) {
        log.info("更新分销员状态，分销员ID: {}, 状态: {}", id, status);
        distributorService.updateDistributorStatus(id, status);
        return Result.success("分销员状态更新成功");
    }

    @Operation(summary = "更新分销员等级")
    @PutMapping("/{id}/level")
    public Result<Void> updateDistributorLevel(@Parameter(description = "分销员ID") @PathVariable Long id,
                                               @Parameter(description = "等级") @RequestParam Integer level) {
        log.info("更新分销员等级，分销员ID: {}, 等级: {}", id, level);
        distributorService.updateDistributorLevel(id, level);
        return Result.success("分销员等级更新成功");
    }

    @Operation(summary = "获取分销员团队")
    @GetMapping("/{id}/team")
    public Result<PageResult<DistributorListVO>> getDistributorTeam(@Parameter(description = "分销员ID") @PathVariable Long id,
                                                                    PageQuery pageQuery) {
        log.info("获取分销员团队，分销员ID: {}", id);
        PageResult<DistributorListVO> result = distributorService.getDistributorTeam(id, pageQuery);
        return Result.success("获取分销员团队成功", result);
    }

    @Operation(summary = "获取分销员统计数据")
    @GetMapping("/stats")
    public Result<DistributorStatsVO> getDistributorStats() {
        log.info("获取分销员统计数据");
        DistributorStatsVO result = distributorService.getDistributorStats();
        return Result.success("获取分销员统计数据成功", result);
    }

    @Operation(summary = "导出分销员数据")
    @GetMapping("/export")
    public Result<String> exportDistributors(@Valid DistributorQueryDTO queryDTO) {
        log.info("导出分销员数据，查询条件: {}", queryDTO);
        String downloadUrl = distributorService.exportDistributors(queryDTO);
        return Result.success("分销员数据导出成功", downloadUrl);
    }
}
