<template>
  <view class="verify-container">
    <!-- 顶部背景渐变 -->
    <view class="top-gradient"></view>
    
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 导航栏 -->
    <view class="navbar">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">商家认证</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 内容区域 -->
    <scroll-view scroll-y class="content">
      <!-- 认证说明卡片 -->
      <view class="info-card">
        <view class="info-header">
          <image src="/static/images/tabbar/认证.png" class="info-icon"></image>
          <text class="info-title">商家认证说明</text>
        </view>
        <view class="info-content">
          <text class="info-text">商家认证后，您的店铺将获得官方认证标识，提升用户信任度和转化率。认证商家在平台享有搜索排名优先、流量扶持等多重特权。</text>
        </view>
        <view class="info-tips">
          <text class="tip-title">认证须知：</text>
          <text class="tip-item">· 认证过程大约需要1-3个工作日</text>
          <text class="tip-item">· 请确保上传资料真实有效</text>
          <text class="tip-item">· 认证成功后12个月内有效</text>
        </view>
      </view>
      
      <!-- 认证表单 -->
      <view class="form-section">
        <view class="section-title">基本信息</view>
        
        <view class="form-item">
          <text class="form-label">商家名称</text>
          <input class="form-input" v-model="formData.shopName" placeholder="与营业执照一致" />
        </view>
        
        <view class="form-item">
          <text class="form-label">法定代表人</text>
          <input class="form-input" v-model="formData.legalPerson" placeholder="营业执照上的法定代表人" />
        </view>
        
        <view class="form-item">
          <text class="form-label">经营地址</text>
          <input class="form-input" v-model="formData.address" placeholder="详细经营地址" />
        </view>
        
        <view class="form-item">
          <text class="form-label">联系电话</text>
          <input class="form-input" v-model="formData.contactPhone" placeholder="联系电话" type="number" />
        </view>
        
        <view class="form-item">
          <text class="form-label">经营范围</text>
          <textarea class="form-textarea" v-model="formData.businessScope" placeholder="与营业执照一致"></textarea>
        </view>
      </view>
      
      <!-- 证件上传 -->
      <view class="form-section">
        <view class="section-title">证件上传</view>
        
        <view class="watermark-notice">
          <text class="watermark-notice-text">上传的图片将自动添加"只用于磁州生活网认证"水印，请确保图片清晰可见</text>
        </view>
        
        <view class="upload-item">
          <text class="upload-label">营业执照</text>
          <view class="upload-desc">请上传营业执照正本清晰照片</view>
          <view class="upload-wrapper" @click="chooseImage('license')">
            <view class="upload-placeholder" v-if="!formData.licenseImage">
              <image src="/static/images/tabbar/上传.png" class="upload-icon"></image>
              <text class="upload-text">点击上传</text>
            </view>
            <image v-else :src="formData.licenseImage" mode="aspectFit" class="preview-image"></image>
          </view>
        </view>
        
        <view class="upload-item">
          <text class="upload-label">法人身份证正面</text>
          <view class="upload-desc">请上传法人身份证人像面</view>
          <view class="upload-wrapper" @click="chooseImage('idCardFront')">
            <view class="upload-placeholder" v-if="!formData.idCardFrontImage">
              <image src="/static/images/tabbar/上传.png" class="upload-icon"></image>
              <text class="upload-text">点击上传</text>
            </view>
            <image v-else :src="formData.idCardFrontImage" mode="aspectFit" class="preview-image"></image>
          </view>
        </view>
        
        <view class="upload-item">
          <text class="upload-label">法人身份证背面</text>
          <view class="upload-desc">请上传法人身份证国徽面</view>
          <view class="upload-wrapper" @click="chooseImage('idCardBack')">
            <view class="upload-placeholder" v-if="!formData.idCardBackImage">
              <image src="/static/images/tabbar/上传.png" class="upload-icon"></image>
              <text class="upload-text">点击上传</text>
            </view>
            <image v-else :src="formData.idCardBackImage" mode="aspectFit" class="preview-image"></image>
          </view>
        </view>
        
        <view class="upload-item">
          <text class="upload-label">店铺门头照</text>
          <view class="upload-desc">请上传清晰的店铺门头照片</view>
          <view class="upload-wrapper" @click="chooseImage('storefront')">
            <view class="upload-placeholder" v-if="!formData.storefrontImage">
              <image src="/static/images/tabbar/上传.png" class="upload-icon"></image>
              <text class="upload-text">点击上传</text>
            </view>
            <image v-else :src="formData.storefrontImage" mode="aspectFit" class="preview-image"></image>
          </view>
        </view>
      </view>
      
      <!-- 行业资质 -->
      <view class="form-section">
        <view class="section-title">行业资质（可选）</view>
        <view class="form-tip">根据您的经营类型，可上传相关行业资质证明，增加认证通过率</view>
        
        <view class="upload-item">
          <text class="upload-label">行业资质证明</text>
          <view class="upload-desc">如：食品经营许可证、卫生许可证等</view>
          <view class="upload-wrapper" @click="chooseImage('qualification')">
            <view class="upload-placeholder" v-if="!formData.qualificationImage">
              <image src="/static/images/tabbar/上传.png" class="upload-icon"></image>
              <text class="upload-text">点击上传</text>
            </view>
            <image v-else :src="formData.qualificationImage" mode="aspectFit" class="preview-image"></image>
          </view>
        </view>
      </view>
      
      <!-- 认证协议 -->
      <view class="agreement-section">
        <checkbox-group @change="checkboxChange">
          <label class="agreement-label">
            <checkbox value="agree" :checked="isAgreed" color="#1677FF" />
            <text class="agreement-text">我已阅读并同意</text>
            <text class="agreement-link" @click="showAgreement">《商家认证服务协议》</text>
          </label>
        </checkbox-group>
      </view>
      
      <!-- 提交按钮 -->
      <view class="submit-section">
        <button class="submit-btn" :disabled="!isFormValid" :class="{'submit-btn-disabled': !isFormValid}" @click="submitVerify">提交认证申请</button>
        <view class="submit-tip">提交后，平台将在1-3个工作日内完成审核</view>
      </view>
    </scroll-view>
    
    <!-- 隐藏的画布，用于生成水印图片 -->
    <canvas canvas-id="watermarkCanvas" style="position: absolute; left: -9999px; width: 300px; height: 300px;"></canvas>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20,
      isAgreed: false,
      formData: {
        shopName: '',
        legalPerson: '',
        address: '',
        contactPhone: '',
        businessScope: '',
        licenseImage: '',
        idCardFrontImage: '',
        idCardBackImage: '',
        storefrontImage: '',
        qualificationImage: ''
      }
    }
  },
  computed: {
    isFormValid() {
      return this.isAgreed && 
        this.formData.shopName && 
        this.formData.legalPerson && 
        this.formData.address && 
        this.formData.contactPhone && 
        this.formData.businessScope && 
        this.formData.licenseImage && 
        this.formData.idCardFrontImage && 
        this.formData.idCardBackImage && 
        this.formData.storefrontImage;
    }
  },
  onLoad() {
    // 获取状态栏高度
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    
    // 如果有商家信息，自动填充部分表单
    this.loadMerchantInfo();
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    
    loadMerchantInfo() {
      // 模拟从本地或API获取商家信息
      const merchantInfo = uni.getStorageSync('lastMerchantData');
      if (merchantInfo) {
        this.formData.shopName = merchantInfo.shopName || '';
        this.formData.address = merchantInfo.address || '';
        this.formData.contactPhone = merchantInfo.contactPhone || '';
      }
    },
    
    chooseImage(type) {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          
          // 显示上传中提示
          uni.showLoading({
            title: '处理中...',
            mask: true
          });
          
          // 添加水印
          this.addWatermark(tempFilePath, type);
        }
      });
    },
    
    // 添加水印功能
    addWatermark(imagePath, type) {
      const watermarkText = '只用于磁州生活网认证';
      
      // 获取图片信息
      uni.getImageInfo({
        src: imagePath,
        success: (imageInfo) => {
          // 创建画布
          const ctx = uni.createCanvasContext('watermarkCanvas');
          const canvasWidth = imageInfo.width;
          const canvasHeight = imageInfo.height;
          
          // 绘制原图
          ctx.drawImage(imagePath, 0, 0, canvasWidth, canvasHeight);
          
          // 设置水印样式
          ctx.setGlobalAlpha(0.3); // 透明度
          ctx.setFillStyle('#333333'); // 水印颜色
          ctx.setFontSize(Math.max(canvasWidth * 0.04, 16)); // 水印字体大小，基于图片宽度
          ctx.setTextAlign('center');
          ctx.setTextBaseline('middle');
          
          // 绘制文本水印（斜向排布）
          ctx.translate(canvasWidth / 2, canvasHeight / 2);
          ctx.rotate(-20 * Math.PI / 180); // 旋转角度
          
          // 绘制多行水印，铺满整个图片
          const stepX = canvasWidth / 2;
          const stepY = canvasHeight / 4;
          
          for (let x = -canvasWidth; x < canvasWidth; x += stepX) {
            for (let y = -canvasHeight; y < canvasHeight; y += stepY) {
              ctx.fillText(watermarkText, x, y);
            }
          }
          
          // 恢复默认设置
          ctx.rotate(20 * Math.PI / 180);
          ctx.translate(-canvasWidth / 2, -canvasHeight / 2);
          ctx.setGlobalAlpha(1);
          
          // 生成新图片
          ctx.draw(false, () => {
            setTimeout(() => {
              uni.canvasToTempFilePath({
                canvasId: 'watermarkCanvas',
                success: (res) => {
                  // 隐藏加载提示
                  uni.hideLoading();
                  
                  // 根据类型设置对应的图片
                  switch(type) {
                    case 'license':
                      this.formData.licenseImage = res.tempFilePath;
                      break;
                    case 'idCardFront':
                      this.formData.idCardFrontImage = res.tempFilePath;
                      break;
                    case 'idCardBack':
                      this.formData.idCardBackImage = res.tempFilePath;
                      break;
                    case 'storefront':
                      this.formData.storefrontImage = res.tempFilePath;
                      break;
                    case 'qualification':
                      this.formData.qualificationImage = res.tempFilePath;
                      break;
                  }
                },
                fail: (err) => {
                  console.error('生成水印图片失败', err);
                  uni.hideLoading();
                  uni.showToast({
                    title: '处理图片失败',
                    icon: 'none'
                  });
                }
              });
            }, 200);
          });
        },
        fail: (err) => {
          console.error('获取图片信息失败', err);
          uni.hideLoading();
          uni.showToast({
            title: '处理图片失败',
            icon: 'none'
          });
          
          // 处理失败时仍使用原图
          switch(type) {
            case 'license':
              this.formData.licenseImage = imagePath;
              break;
            case 'idCardFront':
              this.formData.idCardFrontImage = imagePath;
              break;
            case 'idCardBack':
              this.formData.idCardBackImage = imagePath;
              break;
            case 'storefront':
              this.formData.storefrontImage = imagePath;
              break;
            case 'qualification':
              this.formData.qualificationImage = imagePath;
              break;
          }
        }
      });
    },
    
    checkboxChange(e) {
      this.isAgreed = e.detail.value.includes('agree');
    },
    
    showAgreement() {
      uni.showModal({
        title: '商家认证服务协议',
        content: '提交认证即表示您同意遵守本平台商家认证服务协议，保证所提交的所有资料真实有效。认证商家需遵守平台规则，提供优质服务，若有违规行为，平台有权取消认证资格。',
        confirmText: '我已阅读',
        showCancel: false
      });
    },
    
    submitVerify() {
      if (!this.isFormValid) {
        uni.showToast({
          title: '请完善认证信息',
          icon: 'none'
        });
        return;
      }
      
      // 显示提交中提示
      uni.showLoading({
        title: '提交中...',
        mask: true
      });
      
      // 模拟提交过程
      setTimeout(() => {
        uni.hideLoading();
        
        // 保存认证申请信息
        uni.setStorageSync('verifyApplyData', {
          ...this.formData,
          applyTime: new Date().toLocaleString(),
          status: 'pending'
        });
        
        // 跳转到认证成功提交页面
        uni.navigateTo({
          url: '/pages/business/verify-success'
        });
      }, 1500);
    }
  }
}
</script>

<style>
.verify-container {
  min-height: 100vh;
  background-color: #f8f9fc;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 顶部渐变背景 */
.top-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: linear-gradient(135deg, #0046B3, #1677FF);
  z-index: 0;
}

/* 导航栏 */
.navbar {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  position: relative;
  z-index: 1;
  width: 100%;
}

.navbar-left {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24px;
  height: 24px;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #fff;
}

.navbar-right {
  width: 44px;
}

/* 内容区域 */
.content {
  position: relative;
  z-index: 1;
  padding: 20px;
  min-height: calc(100vh - 120px);
  width: 100%;
  box-sizing: border-box;
  max-width: 600px;
  margin: 0 auto;
}

/* 认证说明卡片 */
.info-card {
  background-color: #fff;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}

.info-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.info-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.info-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.info-content {
  margin-bottom: 16px;
}

.info-text {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

.info-tips {
  background-color: #f8f9fc;
  padding: 12px;
  border-radius: 8px;
}

.tip-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

.tip-item {
  font-size: 13px;
  color: #666;
  line-height: 1.6;
  display: block;
}

/* 表单部分 */
.form-section {
  background-color: #fff;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  padding-left: 12px;
  border-left: 4px solid #1677FF;
  line-height: 1.2;
}

.form-tip {
  font-size: 13px;
  color: #999;
  margin-bottom: 16px;
}

.form-item {
  margin-bottom: 16px;
}

.form-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

.form-input {
  width: 100%;
  height: 44px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 14px;
  color: #333;
  background-color: #f8f9fc;
  box-sizing: border-box;
}

.form-textarea {
  width: 100%;
  height: 80px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  color: #333;
  background-color: #f8f9fc;
  box-sizing: border-box;
}

/* 上传部分 */
.upload-item {
  margin-bottom: 20px;
}

.upload-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 4px;
  display: block;
}

.upload-desc {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}

.upload-wrapper {
  height: 160px;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fc;
  overflow: hidden;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-icon {
  width: 32px;
  height: 32px;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 14px;
  color: #999;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 协议部分 */
.agreement-section {
  padding: 16px 0;
  width: 100%;
}

.agreement-label {
  display: flex;
  align-items: center;
}

.agreement-text {
  font-size: 14px;
  color: #666;
  margin-left: 4px;
}

.agreement-link {
  font-size: 14px;
  color: #1677FF;
}

/* 提交按钮 */
.submit-section {
  margin: 20px 0 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.submit-btn {
  width: 100%;
  height: 48px;
  background: linear-gradient(135deg, #1677FF, #0052CC);
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  border-radius: 24px;
  margin-bottom: 12px;
  box-shadow: 0 6px 12px rgba(0, 82, 204, 0.2);
}

.submit-btn-disabled {
  background: linear-gradient(135deg, #a0a0a0, #7e7e7e);
  box-shadow: none;
}

.submit-tip {
  font-size: 12px;
  color: #999;
  text-align: center;
}

/* 水印提示样式 */
.watermark-notice {
  margin-bottom: 15px;
  padding: 8px 12px;
  background-color: #f0f8ff;
  border-radius: 8px;
  border-left: 3px solid #1677FF;
}

.watermark-notice-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}
</style> 