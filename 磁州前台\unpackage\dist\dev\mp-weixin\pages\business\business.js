"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
if (!Math) {
  (MerchantRecommend + FabButtons)();
}
const FabButtons = () => "../../components/FabButtons.js";
const MerchantRecommend = () => "../../components/index/MerchantRecommend.js";
const _sfc_main = {
  __name: "business",
  setup(__props) {
    const currentTab = common_vendor.ref(0);
    const statusBarHeight = common_vendor.ref(20);
    const searchKeyword = common_vendor.ref("");
    const locationAuthChecked = common_vendor.ref(false);
    const bannerList = common_vendor.reactive([
      { image: "/static/images/banner/banner-1.png" },
      { image: "/static/images/banner/banner-2.png" },
      { image: "/static/images/banner/banner-3.jpg" }
    ]);
    common_vendor.reactive([
      { name: "房产楼盘", icon: "/static/images/tabbar/房产楼盘.png" },
      { name: "美食小吃", icon: "/static/images/tabbar/美食小吃.png" },
      { name: "装修家居", icon: "/static/images/tabbar/装修家居.png" },
      { name: "母婴专区", icon: "/static/images/tabbar/母婴专区.png", hot: true },
      { name: "休闲娱乐", icon: "/static/images/tabbar/休闲娱乐.png" },
      { name: "商到家服务", icon: "/static/images/tabbar/商到家服务.png" },
      { name: "开锁换锁", icon: "/static/images/tabbar/开锁换锁.png" },
      { name: "数码通讯", icon: "/static/images/tabbar/数码通讯.png" },
      { name: "商车辆服务", icon: "/static/images/tabbar/商车辆服务.png" },
      { name: "教育培训", icon: "/static/images/tabbar/商教育培训.png" },
      { name: "婚纱摄影", icon: "/static/images/tabbar/婚纱摄影.png" },
      { name: "农林牧渔", icon: "/static/images/tabbar/农林牧渔.png" },
      { name: "广告传媒", icon: "/static/images/tabbar/广告传媒.png" },
      { name: "其他行业", icon: "/static/images/tabbar/其他.png" }
    ]);
    const allBusinessList = common_vendor.reactive([
      {
        id: "1",
        logo: "/static/images/cizhou.png",
        name: "五分利电器",
        description: "家电全网调货，全场特价，送货上门",
        category: "数码电器",
        scale: "10-20人",
        hasConsumeRedPacket: true,
        isNew: false,
        distance: 1.2,
        isOpen: true,
        isVerified: true,
        isFollowing: false,
        isHot: true,
        hasDiscount: true,
        rating: "4.9",
        reviewCount: 156,
        contactPhone: "188-8888-8888",
        address: "河北省邯郸市磁县祥和路",
        features: [
          { icon: "/static/images/tabbar/送货.png", text: "免费送货" },
          { icon: "/static/images/tabbar/安装.png", text: "上门安装" },
          { icon: "/static/images/tabbar/保修.png", text: "全国联保" }
        ]
      },
      {
        id: "2",
        logo: "/static/images/cizhou.png",
        name: "金鼎家居",
        description: "全屋定制、软装搭配、设计施工一站式服务",
        category: "家居家装",
        scale: "20-50人",
        hasConsumeRedPacket: true,
        isNew: true,
        distance: 0.8,
        isOpen: true,
        isVerified: true,
        isFollowing: false,
        isHot: false,
        hasDiscount: false,
        rating: "4.7",
        reviewCount: 98,
        contactPhone: "133-3333-3333",
        address: "河北省邯郸市磁县滏阳路68号",
        features: [
          { icon: "/static/images/tabbar/设计.png", text: "免费设计" },
          { icon: "/static/images/tabbar/测量.png", text: "上门测量" },
          { icon: "/static/images/tabbar/保障.png", text: "质保5年" }
        ]
      },
      {
        id: "3",
        logo: "/static/images/cizhou.png",
        name: "鲜丰水果",
        description: "新鲜水果，每日直采，支持微信下单，全城配送",
        category: "生鲜果蔬",
        scale: "5-10人",
        hasConsumeRedPacket: true,
        isNew: false,
        distance: 2.5,
        isOpen: true,
        isVerified: false,
        isFollowing: false,
        isHot: false,
        hasDiscount: true,
        rating: "4.8",
        reviewCount: 215,
        contactPhone: "177-7777-7777",
        address: "河北省邯郸市磁县时代广场A区112号",
        features: [
          { icon: "/static/images/tabbar/配送.png", text: "全城配送" },
          { icon: "/static/images/tabbar/新鲜.png", text: "每日新鲜" }
        ]
      },
      {
        id: "4",
        logo: "/static/images/cizhou.png",
        name: "磁州书院",
        description: "综合性文化教育机构，少儿教育、成人培训、艺术培训",
        category: "文化教育",
        scale: "10-15人",
        hasConsumeRedPacket: true,
        isNew: false,
        distance: 3.1,
        isOpen: false,
        isVerified: true,
        isFollowing: false,
        isHot: false,
        hasDiscount: false,
        rating: "4.9",
        reviewCount: 87,
        contactPhone: "155-5555-5555",
        address: "河北省邯郸市磁县文化路29号",
        features: [
          { icon: "/static/images/tabbar/教育.png", text: "专业教师" },
          { icon: "/static/images/tabbar/证书.png", text: "官方认证" }
        ]
      },
      {
        id: "5",
        logo: "/static/images/cizhou.png",
        name: "康美大药房",
        description: "全天24小时营业，医保定点药房，送药上门服务",
        category: "医疗健康",
        scale: "15-20人",
        hasConsumeRedPacket: true,
        isNew: false,
        distance: 1.8,
        isOpen: true,
        isVerified: true,
        isFollowing: false,
        isHot: true,
        hasDiscount: true,
        rating: "4.8",
        reviewCount: 176,
        contactPhone: "199-9999-9999",
        address: "河北省邯郸市磁县健康路45号",
        features: [
          { icon: "/static/images/tabbar/医保.png", text: "医保定点" },
          { icon: "/static/images/tabbar/送药.png", text: "送药上门" },
          { icon: "/static/images/tabbar/24小时.png", text: "24小时营业" }
        ]
      }
    ]);
    const businessList = common_vendor.computed(() => {
      switch (currentTab.value) {
        case 1:
          return allBusinessList.filter((b) => b.isNew).sort((a, b) => new Date(b.joinDate) - new Date(a.joinDate));
        case 2:
          return [...allBusinessList].sort((a, b) => a.distance - b.distance);
        case 0:
        default:
          return allBusinessList;
      }
    });
    const changeTab = (index) => {
      currentTab.value = index;
      if (index === 2 && !locationAuthChecked.value) {
        checkLocationPermission();
      }
    };
    const checkLocationPermission = () => {
      common_vendor.index.showModal({
        title: "位置信息授权",
        content: "我们需要获取您的位置信息以展示附近的商家，是否同意？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.getLocation({
              type: "wgs84",
              success: (locRes) => {
                common_vendor.index.__f__("log", "at pages/business/business.vue:440", "当前位置：", locRes.latitude, locRes.longitude);
                common_vendor.index.showToast({ title: "已获取位置", icon: "none" });
              },
              fail: () => {
                common_vendor.index.showToast({ title: "获取位置失败", icon: "error" });
              }
            });
          }
          locationAuthChecked.value = true;
        }
      });
    };
    const navigateToJoin = () => {
      common_vendor.index.navigateTo({ url: "/pages/business/join" });
    };
    const navigateToFilter = (category) => {
      common_vendor.index.navigateTo({ url: `/pages/business/filter?category=${category}` });
    };
    const navigateToShopDetail = (id) => {
      common_vendor.index.navigateTo({ url: `/pages/business/shop-detail?id=${id}` });
    };
    const followBusiness = (id) => {
      common_vendor.index.__f__("log", "at pages/business/business.vue:467", `关注商家 ID: ${id}`);
      common_vendor.index.showToast({ title: "关注成功", icon: "success" });
    };
    const callBusiness = (phoneNumber) => {
      if (!phoneNumber) {
        common_vendor.index.showToast({
          title: "暂无联系电话",
          icon: "none"
        });
        return;
      }
      common_vendor.index.makePhoneCall({
        phoneNumber,
        success: () => {
          common_vendor.index.__f__("log", "at pages/business/business.vue:483", "拨打电话成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("log", "at pages/business/business.vue:486", "拨打电话失败", err);
        }
      });
    };
    const navigateToBusiness = (address, name) => {
      if (!address) {
        common_vendor.index.showToast({
          title: "暂无地址信息",
          icon: "none"
        });
        return;
      }
      common_vendor.index.openLocation({
        latitude: 36.313076,
        longitude: 114.347312,
        name,
        address,
        scale: 18
      });
    };
    const shareBusiness = (business) => {
      if (!business)
        return;
      common_vendor.index.showShareMenu({
        withShareTicket: true,
        menus: ["shareAppMessage", "shareTimeline"]
      });
      common_vendor.index.onShareAppMessage(() => {
        return {
          title: business.name + " - " + business.description,
          path: "/pages/business/shop-detail?id=" + business.id,
          imageUrl: business.logo
        };
      });
      common_vendor.index.showActionSheet({
        itemList: ["分享给朋友", "分享到朋友圈", "生成分享图片"],
        success: (res) => {
          if (res.tapIndex === 2) {
            common_vendor.index.showToast({
              title: "生成分享图片功能开发中",
              icon: "none"
            });
          }
        }
      });
    };
    common_vendor.onMounted(() => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = systemInfo.statusBarHeight || 20;
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: statusBarHeight.value + "px",
        b: common_vendor.f(bannerList, (banner, index, i0) => {
          return {
            a: banner.image,
            b: index
          };
        }),
        c: common_assets._imports_0$9,
        d: common_assets._imports_2$4,
        e: common_vendor.o(navigateToJoin),
        f: common_vendor.o(navigateToJoin),
        g: common_vendor.p({
          pageName: "business",
          pageInfo: {
            title: "磁州生活网 - 同城商圈",
            path: "/pages/business/business",
            imageUrl: "/static/images/banner/banner-1.png"
          }
        }),
        h: searchKeyword.value,
        i: common_vendor.o(($event) => searchKeyword.value = $event.detail.value),
        j: common_assets._imports_0$10,
        k: common_assets._imports_3$5,
        l: common_vendor.o(($event) => navigateToFilter("房产楼盘")),
        m: common_assets._imports_4$1,
        n: common_vendor.o(($event) => navigateToFilter("美食小吃")),
        o: common_assets._imports_5$1,
        p: common_vendor.o(($event) => navigateToFilter("装修家居")),
        q: common_assets._imports_6$1,
        r: common_vendor.o(($event) => navigateToFilter("母婴专区")),
        s: common_assets._imports_7,
        t: common_vendor.o(($event) => navigateToFilter("休闲娱乐")),
        v: common_assets._imports_8,
        w: common_vendor.o(($event) => navigateToFilter("到家服务")),
        x: common_assets._imports_9$1,
        y: common_vendor.o(($event) => navigateToFilter("开锁换锁")),
        z: common_assets._imports_10$1,
        A: common_vendor.o(($event) => navigateToFilter("数码通讯")),
        B: common_assets._imports_11,
        C: common_vendor.o(($event) => navigateToFilter("车辆服务")),
        D: common_assets._imports_12$1,
        E: common_vendor.o(($event) => navigateToFilter("教育培训")),
        F: common_assets._imports_13,
        G: common_vendor.o(($event) => navigateToFilter("婚纱摄影")),
        H: common_assets._imports_14$1,
        I: common_vendor.o(($event) => navigateToFilter("农林牧渔")),
        J: common_assets._imports_15,
        K: common_vendor.o(($event) => navigateToFilter("广告传媒")),
        L: common_assets._imports_16,
        M: common_vendor.o(($event) => navigateToFilter("其他行业")),
        N: currentTab.value === 0
      }, currentTab.value === 0 ? {} : {}, {
        O: currentTab.value === 0 ? 1 : "",
        P: common_vendor.o(($event) => changeTab(0)),
        Q: currentTab.value === 1
      }, currentTab.value === 1 ? {} : {}, {
        R: currentTab.value === 1 ? 1 : "",
        S: common_vendor.o(($event) => changeTab(1)),
        T: currentTab.value === 2
      }, currentTab.value === 2 ? {} : {}, {
        U: currentTab.value === 2 ? 1 : "",
        V: common_vendor.o(($event) => changeTab(2)),
        W: common_vendor.f(businessList.value, (business, k0, i0) => {
          return common_vendor.e({
            a: business.logo,
            b: business.isOpen
          }, business.isOpen ? {} : {}, {
            c: common_vendor.t(business.name),
            d: business.isVerified
          }, business.isVerified ? {
            e: common_assets._imports_2$5
          } : {}, {
            f: common_vendor.t(business.rating || "4.8"),
            g: common_vendor.f(5, (i, k1, i1) => {
              return {
                a: i
              };
            }),
            h: common_vendor.t(business.reviewCount || "128"),
            i: business.distance
          }, business.distance ? {
            j: common_vendor.t(business.distance)
          } : {}, {
            k: common_vendor.t(business.description),
            l: business.category
          }, business.category ? {
            m: common_vendor.t(business.category)
          } : {}, {
            n: business.hasConsumeRedPacket
          }, business.hasConsumeRedPacket ? {
            o: common_assets._imports_0$11
          } : {}, {
            p: business.isNew
          }, business.isNew ? {} : {}, {
            q: business.isHot
          }, business.isHot ? {} : {}, {
            r: business.hasDiscount
          }, business.hasDiscount ? {} : {}, {
            s: business.features && business.features.length
          }, business.features && business.features.length ? {
            t: common_vendor.f(business.features, (feature, idx, i1) => {
              return {
                a: feature.icon,
                b: common_vendor.t(feature.text),
                c: idx
              };
            })
          } : {}, {
            v: common_vendor.o(($event) => callBusiness(business.contactPhone), business.id),
            w: common_vendor.o(($event) => navigateToBusiness(business.address, business.name), business.id),
            x: common_vendor.o(($event) => shareBusiness(business), business.id),
            y: common_vendor.t(business.isFollowing ? "已关注" : "+ 关注"),
            z: business.isFollowing ? 1 : "",
            A: common_vendor.o(($event) => followBusiness(business.id), business.id),
            B: business.id,
            C: common_vendor.o(($event) => navigateToShopDetail(business.id), business.id)
          });
        }),
        X: common_assets._imports_6,
        Y: common_assets._imports_3$4,
        Z: common_assets._imports_2$1
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ec9204da"]]);
_sfc_main.__runtimeHooks = 2;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/business/business.js.map
