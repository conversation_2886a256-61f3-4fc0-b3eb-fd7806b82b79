<template>
  <view class="password-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">密码管理</text>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 安全提示 -->
    <view class="security-tip">
      <view class="tip-icon">
        <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="12" y1="8" x2="12" y2="12"></line>
          <line x1="12" y1="16" x2="12.01" y2="16"></line>
        </svg>
      </view>
      <text class="tip-text">为了保障账号安全，建议定期更换密码，并开启双因素认证</text>
    </view>
    
    <!-- 密码修改 -->
    <view class="section-card">
      <view class="section-header">
        <text class="section-title">修改密码</text>
      </view>
      
      <view class="form-item">
        <text class="form-label">当前密码</text>
        <input type="password" class="form-input" placeholder="请输入当前密码" password="true" v-model="passwordForm.oldPassword" />
      </view>
      
      <view class="form-item">
        <text class="form-label">新密码</text>
        <input type="password" class="form-input" placeholder="请输入新密码" password="true" v-model="passwordForm.newPassword" />
      </view>
      
      <view class="form-item">
        <text class="form-label">确认新密码</text>
        <input type="password" class="form-input" placeholder="请再次输入新密码" password="true" v-model="passwordForm.confirmPassword" />
      </view>
      
      <view class="password-strength">
        <text class="strength-label">密码强度</text>
        <view class="strength-bar">
          <view class="strength-fill" :class="passwordStrengthClass"></view>
        </view>
        <text class="strength-text">{{passwordStrengthText}}</text>
      </view>
      
      <view class="password-tips">
        <text class="tips-title">密码要求：</text>
        <view class="tips-item">
          <text class="tips-dot">•</text>
          <text class="tips-text">长度不少于8位</text>
        </view>
        <view class="tips-item">
          <text class="tips-dot">•</text>
          <text class="tips-text">包含大小写字母、数字和特殊符号</text>
        </view>
        <view class="tips-item">
          <text class="tips-dot">•</text>
          <text class="tips-text">不能与前3次使用的密码相同</text>
        </view>
      </view>
      
      <button class="primary-button" @click="changePassword">确认修改</button>
    </view>
    
    <!-- 双因素认证 -->
    <view class="section-card">
      <view class="section-header">
        <text class="section-title">双因素认证</text>
        <switch :checked="twoFactorEnabled" @change="toggleTwoFactor" color="#1677FF" />
      </view>
      
      <view class="two-factor-content" v-if="twoFactorEnabled">
        <view class="qrcode-section">
          <text class="qrcode-title">扫描二维码</text>
          <view class="qrcode-image">
            <image src="/static/images/qrcode-placeholder.png" mode="aspectFit"></image>
          </view>
          <text class="qrcode-desc">使用Google Authenticator或其他身份验证器应用扫描上方二维码</text>
        </view>
        
        <view class="backup-codes">
          <text class="backup-title">备用验证码</text>
          <text class="backup-desc">请保存以下备用验证码，当您无法使用主要验证方式时可使用</text>
          <view class="codes-container">
            <view class="code-item" v-for="(code, index) in backupCodes" :key="index">
              <text class="code-text">{{code}}</text>
            </view>
          </view>
          <button class="secondary-button" @click="downloadBackupCodes">下载备用码</button>
        </view>
      </view>
      
      <view class="two-factor-disabled" v-else>
        <view class="disabled-icon">
          <svg viewBox="0 0 24 24" width="48" height="48" fill="none" stroke="currentColor" stroke-width="1.5">
            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
            <path d="M8 11l3 3 6-6"></path>
          </svg>
        </view>
        <text class="disabled-title">提升账号安全性</text>
        <text class="disabled-desc">开启双因素认证后，登录时除了输入密码外，还需要输入验证码，有效防止账号被盗</text>
      </view>
    </view>
    
    <!-- 登录通知设置 -->
    <view class="section-card">
      <view class="section-header">
        <text class="section-title">登录通知</text>
      </view>
      
      <view class="notification-item">
        <view class="notification-info">
          <text class="notification-title">短信通知</text>
          <text class="notification-desc">登录时向绑定手机发送短信通知</text>
        </view>
        <switch :checked="notifications.sms" @change="toggleNotification('sms')" color="#1677FF" />
      </view>
      
      <view class="notification-item">
        <view class="notification-info">
          <text class="notification-title">邮件通知</text>
          <text class="notification-desc">登录时向绑定邮箱发送邮件通知</text>
        </view>
        <switch :checked="notifications.email" @change="toggleNotification('email')" color="#1677FF" />
      </view>
      
      <view class="notification-item">
        <view class="notification-info">
          <text class="notification-title">异常登录通知</text>
          <text class="notification-desc">检测到异常登录行为时发送通知</text>
        </view>
        <switch :checked="notifications.abnormal" @change="toggleNotification('abnormal')" color="#1677FF" />
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      twoFactorEnabled: true,
      backupCodes: [
        'ABCD-EFGH-IJKL',
        'MNOP-QRST-UVWX',
        'YZAB-CDEF-GHIJ',
        'KLMN-OPQR-STUV',
        'WXYZ-1234-5678',
        '9012-3456-7890'
      ],
      notifications: {
        sms: true,
        email: true,
        abnormal: true
      }
    }
  },
  computed: {
    passwordStrength() {
      const password = this.passwordForm.newPassword;
      if (!password) return 0;
      
      let strength = 0;
      // 长度检查
      if (password.length >= 8) strength += 1;
      // 包含数字
      if (/\d/.test(password)) strength += 1;
      // 包含小写字母
      if (/[a-z]/.test(password)) strength += 1;
      // 包含大写字母
      if (/[A-Z]/.test(password)) strength += 1;
      // 包含特殊字符
      if (/[^a-zA-Z0-9]/.test(password)) strength += 1;
      
      return strength;
    },
    passwordStrengthClass() {
      const strength = this.passwordStrength;
      if (strength <= 1) return 'weak';
      if (strength <= 3) return 'medium';
      return 'strong';
    },
    passwordStrengthText() {
      const strength = this.passwordStrength;
      if (strength <= 1) return '弱';
      if (strength <= 3) return '中';
      return '强';
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    changePassword() {
      // 验证密码
      if (!this.passwordForm.oldPassword) {
        uni.showToast({
          title: '请输入当前密码',
          icon: 'none'
        });
        return;
      }
      
      if (!this.passwordForm.newPassword) {
        uni.showToast({
          title: '请输入新密码',
          icon: 'none'
        });
        return;
      }
      
      if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {
        uni.showToast({
          title: '两次输入的密码不一致',
          icon: 'none'
        });
        return;
      }
      
      if (this.passwordStrength < 3) {
        uni.showToast({
          title: '密码强度不够，请按要求设置',
          icon: 'none'
        });
        return;
      }
      
      // 模拟修改密码
      uni.showLoading({
        title: '修改中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '密码修改成功',
          icon: 'success'
        });
        
        // 清空表单
        this.passwordForm = {
          oldPassword: '',
          newPassword: '',
          confirmPassword: ''
        };
      }, 1500);
    },
    toggleTwoFactor(e) {
      this.twoFactorEnabled = e.detail.value;
      
      if (this.twoFactorEnabled) {
        uni.showToast({
          title: '已开启双因素认证',
          icon: 'success'
        });
      } else {
        uni.showModal({
          title: '关闭双因素认证',
          content: '关闭双因素认证将降低账号安全性，确定要关闭吗？',
          success: (res) => {
            if (res.confirm) {
              uni.showToast({
                title: '已关闭双因素认证',
                icon: 'none'
              });
            } else {
              this.twoFactorEnabled = true;
            }
          }
        });
      }
    },
    downloadBackupCodes() {
      uni.showToast({
        title: '备用码已下载',
        icon: 'success'
      });
    },
    toggleNotification(type) {
      this.notifications[type] = !this.notifications[type];
      
      uni.showToast({
        title: this.notifications[type] ? '已开启通知' : '已关闭通知',
        icon: 'none'
      });
    }
  }
}
</script>

<style>
.password-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 20px;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
}

.security-tip {
  margin: 16px;
  padding: 12px;
  background-color: #e6f7ff;
  border-radius: 8px;
  display: flex;
  align-items: center;
}

.tip-icon {
  margin-right: 12px;
  color: #1677FF;
}

.tip-text {
  font-size: 14px;
  color: #333;
  flex: 1;
  line-height: 1.5;
}

.section-card {
  margin: 16px;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.form-item {
  margin-bottom: 16px;
}

.form-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  display: block;
}

.form-input {
  width: 100%;
  height: 44px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
  color: #333;
  background-color: #f9f9f9;
}

.password-strength {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.strength-label {
  font-size: 14px;
  color: #666;
  margin-right: 12px;
  width: 60px;
}

.strength-bar {
  flex: 1;
  height: 6px;
  background-color: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
  margin-right: 12px;
}

.strength-fill {
  height: 100%;
  border-radius: 3px;
  width: 0%;
}

.strength-fill.weak {
  width: 30%;
  background-color: #f5222d;
}

.strength-fill.medium {
  width: 60%;
  background-color: #faad14;
}

.strength-fill.strong {
  width: 100%;
  background-color: #52c41a;
}

.strength-text {
  font-size: 14px;
  font-weight: 500;
  width: 30px;
}

.password-tips {
  margin-bottom: 20px;
  padding: 12px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.tips-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  display: block;
}

.tips-item {
  display: flex;
  margin-bottom: 4px;
}

.tips-dot {
  margin-right: 8px;
  color: #999;
}

.tips-text {
  font-size: 12px;
  color: #999;
  flex: 1;
}

.primary-button {
  width: 100%;
  height: 44px;
  background-color: #1677FF;
  color: #fff;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.secondary-button {
  width: 100%;
  height: 44px;
  background-color: #f0f0f0;
  color: #333;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 12px;
}

.two-factor-content {
  
}

.qrcode-section {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qrcode-title {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 12px;
  align-self: flex-start;
}

.qrcode-image {
  width: 180px;
  height: 180px;
  background-color: #f9f9f9;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}

.qrcode-desc {
  font-size: 12px;
  color: #999;
  text-align: center;
  line-height: 1.5;
}

.backup-codes {
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.backup-title {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 8px;
  display: block;
}

.backup-desc {
  font-size: 12px;
  color: #999;
  margin-bottom: 12px;
  display: block;
  line-height: 1.5;
}

.codes-container {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12px;
}

.code-item {
  width: 50%;
  padding: 8px 0;
}

.code-text {
  font-size: 14px;
  color: #333;
  font-family: monospace;
}

.two-factor-disabled {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.disabled-icon {
  color: #1677FF;
  margin-bottom: 16px;
}

.disabled-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.disabled-desc {
  font-size: 14px;
  color: #666;
  text-align: center;
  line-height: 1.5;
}

.notification-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.notification-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.notification-info {
  flex: 1;
}

.notification-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.notification-desc {
  font-size: 12px;
  color: #999;
}
</style> 