{"version": 3, "file": "edit.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/discount/edit.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xkaXNjb3VudFxlZGl0LnZ1ZQ"], "sourcesContent": ["<!-- 满减活动编辑页面 (edit.vue) -->\n<template>\n  <view class=\"discount-edit-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @tap=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">编辑满减活动</text>\n      <view class=\"navbar-right\">\n        <text class=\"save-btn\" @tap=\"saveDiscount\">保存</text>\n      </view>\n    </view>\n    \n    <!-- 编辑表单 -->\n    <view class=\"edit-form\">\n      <!-- 基本信息 -->\n      <view class=\"form-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">基本信息</text>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"item-label\">活动名称</text>\n          <input class=\"item-input\" v-model=\"discountForm.title\" placeholder=\"请输入活动名称\" maxlength=\"20\" />\n          <text class=\"input-count\">{{discountForm.title.length}}/20</text>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"item-label\">活动时间</text>\n          <view class=\"date-picker\" @tap=\"showDatePicker('start')\">\n            <text class=\"date-text\">{{discountForm.startDate || '开始日期'}}</text>\n          </view>\n          <text class=\"date-separator\">至</text>\n          <view class=\"date-picker\" @tap=\"showDatePicker('end')\">\n            <text class=\"date-text\">{{discountForm.endDate || '结束日期'}}</text>\n          </view>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"item-label\">活动状态</text>\n          <view class=\"status-switch\">\n            <text class=\"status-text\">{{discountForm.status === 'active' ? '启用' : '暂停'}}</text>\n            <switch \n              :checked=\"discountForm.status === 'active'\" \n              @change=\"onStatusChange\" \n              color=\"#F8D800\" \n            />\n          </view>\n        </view>\n      </view>\n      \n      <!-- 满减规则 -->\n      <view class=\"form-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">满减规则</text>\n          <text class=\"add-rule\" @tap=\"addRule\">+ 添加规则</text>\n        </view>\n        \n        <view class=\"rules-list\">\n          <view \n            class=\"rule-item\" \n            v-for=\"(rule, index) in discountForm.rules\" \n            :key=\"index\"\n          >\n            <view class=\"rule-inputs\">\n              <view class=\"rule-input-group\">\n                <text class=\"input-prefix\">满</text>\n                <input \n                  class=\"rule-input\" \n                  type=\"digit\" \n                  v-model=\"rule.minAmount\" \n                  placeholder=\"0.00\"\n                />\n                <text class=\"input-suffix\">元</text>\n              </view>\n              <text class=\"rule-separator\">减</text>\n              <view class=\"rule-input-group\">\n                <input \n                  class=\"rule-input\" \n                  type=\"digit\" \n                  v-model=\"rule.discountAmount\" \n                  placeholder=\"0.00\"\n                />\n                <text class=\"input-suffix\">元</text>\n              </view>\n            </view>\n            <view class=\"rule-delete\" @tap=\"deleteRule(index)\">\n              <text class=\"delete-icon\">×</text>\n            </view>\n          </view>\n          \n          <view class=\"empty-rules\" v-if=\"discountForm.rules.length === 0\">\n            <text class=\"empty-text\">请添加满减规则</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 使用设置 -->\n      <view class=\"form-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">使用设置</text>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"item-label\">适用商品</text>\n          <view class=\"item-right\" @tap=\"selectProducts\">\n            <text class=\"item-value\">{{discountForm.applicableProducts || '全部商品'}}</text>\n            <text class=\"item-arrow\">></text>\n          </view>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"item-label\">叠加使用</text>\n          <switch \n            :checked=\"discountForm.canStack\" \n            @change=\"onStackChange\" \n            color=\"#F8D800\" \n          />\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"item-label\">每人限用</text>\n          <view class=\"limit-input-group\">\n            <input \n              class=\"limit-input\" \n              type=\"number\" \n              v-model=\"discountForm.perPersonLimit\" \n              placeholder=\"不限制\"\n            />\n            <text class=\"input-suffix\">次</text>\n          </view>\n        </view>\n        \n        <view class=\"form-item textarea-item\">\n          <text class=\"item-label\">活动说明</text>\n          <textarea \n            class=\"item-textarea\" \n            v-model=\"discountForm.instructions\" \n            placeholder=\"请输入活动说明\" \n            maxlength=\"200\"\n          ></textarea>\n          <text class=\"textarea-count\">{{discountForm.instructions.length}}/200</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部保存按钮 -->\n    <view class=\"bottom-save-bar\">\n      <button class=\"save-button\" @tap=\"saveDiscount\">保存</button>\n    </view>\n    \n    <!-- 日期选择器 -->\n    <uni-calendar \n      v-if=\"showDatePickerDialog\"\n      :insert=\"false\"\n      :start-date=\"'2020-01-01'\"\n      :end-date=\"'2030-12-31'\"\n      @confirm=\"onDateSelect\"\n      @close=\"closeDatePicker\"\n    />\n  </view>\n</template>\n\n<script>\nimport { ref, reactive, onMounted } from 'vue';\n\nexport default {\n  setup() {\n    // 响应式状态\n    const discountId = ref('');\n    const discountForm = reactive({\n      title: '',\n      startDate: '',\n      endDate: '',\n      status: 'active',\n      rules: [],\n      applicableProducts: '全部商品',\n      canStack: false,\n      perPersonLimit: '3',\n      instructions: ''\n    });\n    \n    const showDatePickerDialog = ref(false);\n    const currentDatePicker = ref(''); // 'start' or 'end'\n    \n    // 方法\n    function goBack() {\n      uni.navigateBack();\n    }\n    \n    function showDatePicker(type) {\n      currentDatePicker.value = type;\n      showDatePickerDialog.value = true;\n    }\n    \n    function closeDatePicker() {\n      showDatePickerDialog.value = false;\n    }\n    \n    function onDateSelect(e) {\n      const selectedDate = e.fulldate;\n      if (currentDatePicker.value === 'start') {\n        discountForm.startDate = selectedDate;\n      } else {\n        discountForm.endDate = selectedDate;\n      }\n      closeDatePicker();\n    }\n    \n    function onStatusChange(e) {\n      discountForm.status = e.detail.value ? 'active' : 'paused';\n    }\n    \n    function onStackChange(e) {\n      discountForm.canStack = e.detail.value;\n    }\n    \n    function addRule() {\n      discountForm.rules.push({\n        minAmount: '',\n        discountAmount: ''\n      });\n    }\n    \n    function deleteRule(index) {\n      discountForm.rules.splice(index, 1);\n    }\n    \n    function selectProducts() {\n      uni.showActionSheet({\n        itemList: ['全部商品', '指定商品', '指定分类'],\n        success: (res) => {\n          switch(res.tapIndex) {\n            case 0:\n              discountForm.applicableProducts = '全部商品';\n              break;\n            case 1:\n              // 在实际应用中，这里应该打开商品选择页面\n              discountForm.applicableProducts = '指定商品';\n              break;\n            case 2:\n              // 在实际应用中，这里应该打开分类选择页面\n              discountForm.applicableProducts = '指定分类';\n              break;\n          }\n        }\n      });\n    }\n    \n    function validateForm() {\n      if (!discountForm.title.trim()) {\n        uni.showToast({\n          title: '请输入活动名称',\n          icon: 'none'\n        });\n        return false;\n      }\n      \n      if (!discountForm.startDate || !discountForm.endDate) {\n        uni.showToast({\n          title: '请选择活动时间',\n          icon: 'none'\n        });\n        return false;\n      }\n      \n      if (discountForm.rules.length === 0) {\n        uni.showToast({\n          title: '请添加至少一条满减规则',\n          icon: 'none'\n        });\n        return false;\n      }\n      \n      for (const rule of discountForm.rules) {\n        if (!rule.minAmount || !rule.discountAmount) {\n          uni.showToast({\n            title: '请完善满减规则',\n            icon: 'none'\n          });\n          return false;\n        }\n        \n        if (parseFloat(rule.discountAmount) >= parseFloat(rule.minAmount)) {\n          uni.showToast({\n            title: '优惠金额不能大于等于满减金额',\n            icon: 'none'\n          });\n          return false;\n        }\n      }\n      \n      return true;\n    }\n    \n    function saveDiscount() {\n      if (!validateForm()) {\n        return;\n      }\n      \n      // 在实际应用中，这里应该调用API保存数据\n      \n      uni.showLoading({\n        title: '保存中...'\n      });\n      \n      setTimeout(() => {\n        uni.hideLoading();\n        \n        uni.showToast({\n          title: '保存成功',\n          icon: 'success'\n        });\n        \n        setTimeout(() => {\n          uni.navigateBack();\n        }, 1500);\n      }, 1000);\n    }\n    \n    function loadDiscountData(id) {\n      // 在实际应用中，这里应该调用API获取满减活动详情\n      discountId.value = id;\n      \n      // 模拟加载数据\n      uni.showLoading({\n        title: '加载中...'\n      });\n      \n      // 模拟数据\n      setTimeout(() => {\n        Object.assign(discountForm, {\n          title: '春季促销活动',\n          startDate: '2023-04-01',\n          endDate: '2023-04-30',\n          status: 'active',\n          rules: [\n            { minAmount: '100', discountAmount: '10' },\n            { minAmount: '200', discountAmount: '25' },\n            { minAmount: '300', discountAmount: '50' }\n          ],\n          applicableProducts: '全部商品',\n          canStack: false,\n          perPersonLimit: '3',\n          instructions: '活动期间，每位用户最多可使用3次满减优惠，不可与其他优惠同时使用'\n        });\n        \n        uni.hideLoading();\n      }, 500);\n    }\n    \n    onMounted(() => {\n      const pages = getCurrentPages();\n      const currentPage = pages[pages.length - 1];\n      const options = currentPage.$page?.options || {};\n      \n      if (options.id) {\n        loadDiscountData(options.id);\n      } else {\n        // 默认添加一条空规则\n        addRule();\n      }\n    });\n    \n    return {\n      discountForm,\n      showDatePickerDialog,\n      currentDatePicker,\n      goBack,\n      showDatePicker,\n      closeDatePicker,\n      onDateSelect,\n      onStatusChange,\n      onStackChange,\n      addRule,\n      deleteRule,\n      selectProducts,\n      saveDiscount\n    };\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.discount-edit-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: 80px; /* 为底部按钮留出空间 */\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #FDEB71, #F8D800);\n  color: #333;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(248, 216, 0, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #333;\n  border-bottom: 2px solid #333;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  padding: 0 10px;\n}\n\n.save-btn {\n  font-size: 16px;\n  color: #333;\n  font-weight: 500;\n}\n\n/* 表单样式 */\n.edit-form {\n  padding: 15px;\n}\n\n.form-section {\n  background: #fff;\n  border-radius: 12px;\n  padding: 15px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.add-rule {\n  font-size: 14px;\n  color: #F8D800;\n}\n\n.form-item {\n  display: flex;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.form-item:last-child {\n  border-bottom: none;\n}\n\n.item-label {\n  width: 80px;\n  font-size: 14px;\n  color: #666;\n}\n\n.item-input {\n  flex: 1;\n  height: 24px;\n  font-size: 14px;\n  color: #333;\n}\n\n.input-count {\n  font-size: 12px;\n  color: #999;\n  margin-left: 10px;\n}\n\n.date-picker {\n  flex: 1;\n  height: 36px;\n  background: #f5f5f5;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 10px;\n}\n\n.date-text {\n  font-size: 14px;\n  color: #333;\n}\n\n.date-separator {\n  margin: 0 10px;\n  color: #999;\n}\n\n.status-switch {\n  flex: 1;\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n}\n\n.status-text {\n  font-size: 14px;\n  color: #333;\n  margin-right: 10px;\n}\n\n/* 规则样式 */\n.rules-list {\n  \n}\n\n.rule-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 15px;\n  background: #f9f9f9;\n  border-radius: 8px;\n  padding: 12px;\n}\n\n.rule-inputs {\n  flex: 1;\n  display: flex;\n  align-items: center;\n}\n\n.rule-input-group {\n  display: flex;\n  align-items: center;\n  background: #fff;\n  border-radius: 6px;\n  padding: 8px 12px;\n  border: 1px solid #eee;\n}\n\n.input-prefix {\n  font-size: 14px;\n  color: #666;\n  margin-right: 5px;\n}\n\n.rule-input {\n  width: 80px;\n  font-size: 14px;\n  color: #333;\n  text-align: center;\n}\n\n.input-suffix {\n  font-size: 14px;\n  color: #666;\n  margin-left: 5px;\n}\n\n.rule-separator {\n  margin: 0 10px;\n  color: #666;\n  font-size: 14px;\n}\n\n.rule-delete {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background: #f0f0f0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-left: 10px;\n}\n\n.delete-icon {\n  font-size: 18px;\n  color: #999;\n}\n\n.empty-rules {\n  padding: 30px 0;\n  display: flex;\n  justify-content: center;\n}\n\n.empty-text {\n  font-size: 14px;\n  color: #999;\n}\n\n/* 使用设置样式 */\n.item-right {\n  flex: 1;\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n}\n\n.item-value {\n  font-size: 14px;\n  color: #333;\n  margin-right: 5px;\n}\n\n.item-arrow {\n  font-size: 14px;\n  color: #999;\n}\n\n.limit-input-group {\n  flex: 1;\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n}\n\n.limit-input {\n  width: 60px;\n  font-size: 14px;\n  color: #333;\n  text-align: right;\n}\n\n.textarea-item {\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.item-textarea {\n  width: 100%;\n  height: 100px;\n  font-size: 14px;\n  color: #333;\n  background: #f9f9f9;\n  border-radius: 8px;\n  padding: 10px;\n  margin-top: 10px;\n  box-sizing: border-box;\n}\n\n.textarea-count {\n  align-self: flex-end;\n  font-size: 12px;\n  color: #999;\n  margin-top: 5px;\n}\n\n/* 底部保存按钮样式 */\n.bottom-save-bar {\n  position: fixed;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  padding: 15px;\n  background: #fff;\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);\n  z-index: 90;\n}\n\n.save-button {\n  width: 100%;\n  height: 44px;\n  background: linear-gradient(135deg, #FDEB71, #F8D800);\n  color: #333;\n  font-size: 16px;\n  font-weight: 500;\n  border-radius: 22px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: none;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/discount/edit.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "uni", "onMounted"], "mappings": ";;AAuKA,MAAK,YAAU;AAAA,EACb,QAAQ;AAEN,UAAM,aAAaA,kBAAI,EAAE;AACzB,UAAM,eAAeC,cAAAA,SAAS;AAAA,MAC5B,OAAO;AAAA,MACP,WAAW;AAAA,MACX,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,OAAO,CAAE;AAAA,MACT,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,cAAc;AAAA,IAChB,CAAC;AAED,UAAM,uBAAuBD,kBAAI,KAAK;AACtC,UAAM,oBAAoBA,kBAAI,EAAE;AAGhC,aAAS,SAAS;AAChBE,oBAAG,MAAC,aAAY;AAAA,IAClB;AAEA,aAAS,eAAe,MAAM;AAC5B,wBAAkB,QAAQ;AAC1B,2BAAqB,QAAQ;AAAA,IAC/B;AAEA,aAAS,kBAAkB;AACzB,2BAAqB,QAAQ;AAAA,IAC/B;AAEA,aAAS,aAAa,GAAG;AACvB,YAAM,eAAe,EAAE;AACvB,UAAI,kBAAkB,UAAU,SAAS;AACvC,qBAAa,YAAY;AAAA,aACpB;AACL,qBAAa,UAAU;AAAA,MACzB;AACA;IACF;AAEA,aAAS,eAAe,GAAG;AACzB,mBAAa,SAAS,EAAE,OAAO,QAAQ,WAAW;AAAA,IACpD;AAEA,aAAS,cAAc,GAAG;AACxB,mBAAa,WAAW,EAAE,OAAO;AAAA,IACnC;AAEA,aAAS,UAAU;AACjB,mBAAa,MAAM,KAAK;AAAA,QACtB,WAAW;AAAA,QACX,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH;AAEA,aAAS,WAAW,OAAO;AACzB,mBAAa,MAAM,OAAO,OAAO,CAAC;AAAA,IACpC;AAEA,aAAS,iBAAiB;AACxBA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,QAAQ,QAAQ,MAAM;AAAA,QACjC,SAAS,CAAC,QAAQ;AAChB,kBAAO,IAAI,UAAQ;AAAA,YACjB,KAAK;AACH,2BAAa,qBAAqB;AAClC;AAAA,YACF,KAAK;AAEH,2BAAa,qBAAqB;AAClC;AAAA,YACF,KAAK;AAEH,2BAAa,qBAAqB;AAClC;AAAA,UACJ;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,eAAe;AACtB,UAAI,CAAC,aAAa,MAAM,QAAQ;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,aAAa,aAAa,CAAC,aAAa,SAAS;AACpDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,aAAa,MAAM,WAAW,GAAG;AACnCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,iBAAW,QAAQ,aAAa,OAAO;AACrC,YAAI,CAAC,KAAK,aAAa,CAAC,KAAK,gBAAgB;AAC3CA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AACD,iBAAO;AAAA,QACT;AAEA,YAAI,WAAW,KAAK,cAAc,KAAK,WAAW,KAAK,SAAS,GAAG;AACjEA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AACD,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,eAAe;AACtB,UAAI,CAAC,aAAY,GAAI;AACnB;AAAA,MACF;AAIAA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAEfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAED,mBAAW,MAAM;AACfA,wBAAG,MAAC,aAAY;AAAA,QACjB,GAAE,IAAI;AAAA,MACR,GAAE,GAAI;AAAA,IACT;AAEA,aAAS,iBAAiB,IAAI;AAE5B,iBAAW,QAAQ;AAGnBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAGD,iBAAW,MAAM;AACf,eAAO,OAAO,cAAc;AAAA,UAC1B,OAAO;AAAA,UACP,WAAW;AAAA,UACX,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO;AAAA,YACL,EAAE,WAAW,OAAO,gBAAgB,KAAM;AAAA,YAC1C,EAAE,WAAW,OAAO,gBAAgB,KAAM;AAAA,YAC1C,EAAE,WAAW,OAAO,gBAAgB,KAAK;AAAA,UAC1C;AAAA,UACD,oBAAoB;AAAA,UACpB,UAAU;AAAA,UACV,gBAAgB;AAAA,UAChB,cAAc;AAAA,QAChB,CAAC;AAEDA,sBAAG,MAAC,YAAW;AAAA,MAChB,GAAE,GAAG;AAAA,IACR;AAEAC,kBAAAA,UAAU,MAAM;;AACd,YAAM,QAAQ;AACd,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,YAAU,iBAAY,UAAZ,mBAAmB,YAAW,CAAA;AAE9C,UAAI,QAAQ,IAAI;AACd,yBAAiB,QAAQ,EAAE;AAAA,aACtB;AAEL;MACF;AAAA,IACF,CAAC;AAED,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;EAEJ;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5XA,GAAG,WAAW,eAAe;"}