{"version": 3, "file": "fission-redpacket-guide.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/redpacket/fission-redpacket-guide.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xyZWRwYWNrZXRcZmlzc2lvbi1yZWRwYWNrZXQtZ3VpZGUudnVl"], "sourcesContent": ["<template>\n  <view class=\"page-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">裂变红包详解</text>\n      <view class=\"navbar-right\">\n        <view class=\"share-icon\" @click=\"shareGuide\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n<circle cx=\"18\" cy=\"5\" r=\"3\"></circle>\n            <circle cx=\"6\" cy=\"12\" r=\"3\"></circle>\n            <circle cx=\"18\" cy=\"19\" r=\"3\"></circle>\n            <line x1=\"8.59\" y1=\"13.51\" x2=\"15.42\" y2=\"17.49\"></line>\n            <line x1=\"15.41\" y1=\"6.51\" x2=\"8.59\" y2=\"10.49\"></line>\n          </svg>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 页面内容 -->\n    <scroll-view scroll-y class=\"content-scroll\">\n      <!-- 页面头部 -->\n      <view class=\"page-header\">\n        <view class=\"header-bg\" style=\"background: linear-gradient(135deg, #43CBFF, #9708CC);\">\n          <text class=\"header-title\">如何利用裂变红包实现用户增长</text>\n          <text class=\"header-subtitle\">低成本高效率的用户获取策略</text>\n        </view>\n      </view>\n      \n      <!-- 内容部分 -->\n      <view class=\"content-section\">\n        <view class=\"section-title\">\n          <view class=\"title-icon\" style=\"background-color: rgba(151, 8, 204, 0.1);\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#9708CC\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <path d=\"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"></path>\n              <circle cx=\"9\" cy=\"7\" r=\"4\"></circle>\n              <path d=\"M23 21v-2a4 4 0 0 0-3-3.87\"></path>\n              <path d=\"M16 3.13a4 4 0 0 1 0 7.75\"></path>\n            </svg>\n          </view>\n          <text class=\"title-text\">什么是裂变红包</text>\n        </view>\n        \n        <view class=\"content-text\">\n          <text>裂变红包是一种基于社交传播的营销工具，通过激励用户分享红包给好友，从而实现用户快速增长的营销方式。裂变红包利用了用户社交网络的传播效应，每个用户都成为品牌的自发推广者，形成\"1传10、10传100\"的指数级增长。</text>\n        </view>\n        \n        <view class=\"content-image\">\n          <image src=\"/static/images/fission-redpacket.png\" mode=\"widthFix\"></image>\n        </view>\n        \n        <view class=\"section-title\">\n          <view class=\"title-icon\" style=\"background-color: rgba(151, 8, 204, 0.1);\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#9708CC\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <path d=\"M22 12h-4l-3 9L9 3l-3 9H2\"></path>\n            </svg>\n          </view>\n          <text class=\"title-text\">裂变红包的核心原理</text>\n        </view>\n        \n        <view class=\"principle-diagram\">\n          <view class=\"diagram-step\">\n            <view class=\"step-circle\">1</view>\n            <view class=\"step-content\">\n              <text class=\"step-title\">用户领取红包</text>\n              <text class=\"step-desc\">用户在平台领取初始红包</text>\n            </view>\n          </view>\n          <view class=\"step-arrow\"></view>\n          <view class=\"diagram-step\">\n            <view class=\"step-circle\">2</view>\n            <view class=\"step-content\">\n              <text class=\"step-title\">分享给好友</text>\n              <text class=\"step-desc\">用户将红包链接分享给好友</text>\n            </view>\n          </view>\n          <view class=\"step-arrow\"></view>\n          <view class=\"diagram-step\">\n            <view class=\"step-circle\">3</view>\n            <view class=\"step-content\">\n              <text class=\"step-title\">好友注册/领取</text>\n              <text class=\"step-desc\">好友注册并领取红包</text>\n            </view>\n          </view>\n          <view class=\"step-arrow\"></view>\n          <view class=\"diagram-step\">\n            <view class=\"step-circle\">4</view>\n            <view class=\"step-content\">\n              <text class=\"step-title\">双方获得奖励</text>\n              <text class=\"step-desc\">邀请者和被邀请者均获得奖励</text>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"section-title\">\n          <view class=\"title-icon\" style=\"background-color: rgba(151, 8, 204, 0.1);\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#9708CC\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <path d=\"M12 2L2 7l10 5 10-5-10-5z\"></path>\n              <path d=\"M2 17l10 5 10-5\"></path>\n              <path d=\"M2 12l10 5 10-5\"></path>\n            </svg>\n          </view>\n          <text class=\"title-text\">常见裂变红包模式</text>\n        </view>\n        \n        <view class=\"model-list\">\n          <view class=\"model-item\">\n            <view class=\"model-header\">\n              <view class=\"model-icon\" style=\"background-color: rgba(151, 8, 204, 0.1);\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#9708CC\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                  <path d=\"M6 9H4.5a2.5 2.5 0 0 1 0-5H6\"></path>\n                  <path d=\"M18 9h1.5a2.5 2.5 0 0 0 0-5H18\"></path>\n                  <path d=\"M4 22h16\"></path>\n                  <path d=\"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22\"></path>\n                  <path d=\"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22\"></path>\n                  <path d=\"M18 2H6v7a6 6 0 0 0 12 0V2Z\"></path>\n                </svg>\n              </view>\n              <text class=\"model-title\">拆红包模式</text>\n            </view>\n            <view class=\"model-content\">\n              <text class=\"model-desc\">用户需邀请一定数量的好友助力，才能拆开获得红包。每位助力好友也可获得小额红包，形成链式传播。</text>\n              <view class=\"model-advantages\">\n                <text class=\"advantages-title\">优势：</text>\n                <view class=\"advantage-point\">\n                  <text class=\"point-dot\"></text>\n                  <text class=\"point-text\">用户有强烈的邀请动机</text>\n                </view>\n                <view class=\"advantage-point\">\n                  <text class=\"point-dot\"></text>\n                  <text class=\"point-text\">助力门槛低，易于传播</text>\n                </view>\n                <view class=\"advantage-point\">\n                  <text class=\"point-dot\"></text>\n                  <text class=\"point-text\">适合新用户拉新场景</text>\n                </view>\n              </view>\n            </view>\n          </view>\n          \n          <view class=\"model-item\">\n            <view class=\"model-header\">\n              <view class=\"model-icon\" style=\"background-color: rgba(151, 8, 204, 0.1);\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#9708CC\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                  <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n                  <line x1=\"12\" y1=\"8\" x2=\"12\" y2=\"16\"></line>\n                  <line x1=\"8\" y1=\"12\" x2=\"16\" y2=\"12\"></line>\n                </svg>\n              </view>\n              <text class=\"model-title\">瓜分红包模式</text>\n            </view>\n            <view class=\"model-content\">\n              <text class=\"model-desc\">设置一个大额红包池，邀请好友共同瓜分。参与人数越多，每人分得的金额可能越小，但也可设置最低保障金额。</text>\n              <view class=\"model-advantages\">\n                <text class=\"advantages-title\">优势：</text>\n                <view class=\"advantage-point\">\n                  <text class=\"point-dot\"></text>\n                  <text class=\"point-text\">有趣的博弈心理，增加参与感</text>\n                </view>\n                <view class=\"advantage-point\">\n                  <text class=\"point-dot\"></text>\n                  <text class=\"point-text\">适合大型活动和节日营销</text>\n                </view>\n                <view class=\"advantage-point\">\n                  <text class=\"point-dot\"></text>\n                  <text class=\"point-text\">可控制总成本</text>\n                </view>\n              </view>\n            </view>\n          </view>\n          \n          <view class=\"model-item\">\n            <view class=\"model-header\">\n              <view class=\"model-icon\" style=\"background-color: rgba(151, 8, 204, 0.1);\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#9708CC\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                  <path d=\"M8 3H5a2 2 0 0 0-2 2v3\"></path>\n                  <path d=\"M21 8V5a2 2 0 0 0-2-2h-3\"></path>\n                  <path d=\"M3 16v3a2 2 0 0 0 2 2h3\"></path>\n                  <path d=\"M16 21h3a2 2 0 0 0 2-2v-3\"></path>\n                </svg>\n              </view>\n              <text class=\"model-title\">阶梯奖励模式</text>\n            </view>\n            <view class=\"model-content\">\n              <text class=\"model-desc\">用户邀请的好友数量越多，获得的奖励越高。设置不同的邀请人数门槛，对应不同等级的奖励。</text>\n              <view class=\"model-advantages\">\n                <text class=\"advantages-title\">优势：</text>\n                <view class=\"advantage-point\">\n                  <text class=\"point-dot\"></text>\n                  <text class=\"point-text\">激励用户持续邀请</text>\n                </view>\n                <view class=\"advantage-point\">\n                  <text class=\"point-dot\"></text>\n                  <text class=\"point-text\">提高用户参与度和粘性</text>\n                </view>\n                <view class=\"advantage-point\">\n                  <text class=\"point-dot\"></text>\n                  <text class=\"point-text\">适合长期运营的裂变活动</text>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"section-title\">\n          <view class=\"title-icon\" style=\"background-color: rgba(151, 8, 204, 0.1);\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#9708CC\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <path d=\"M12 20h9\"></path>\n              <path d=\"M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z\"></path>\n            </svg>\n          </view>\n          <text class=\"title-text\">裂变红包设计要点</text>\n        </view>\n        \n        <view class=\"design-points\">\n          <view class=\"design-point\">\n            <view class=\"point-header\">\n              <view class=\"point-icon\" style=\"background-color: rgba(151, 8, 204, 0.1);\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#9708CC\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                  <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n                  <path d=\"M12 8v4\"></path>\n                  <path d=\"M12 16h.01\"></path>\n                </svg>\n              </view>\n              <text class=\"point-title\">合理的奖励机制</text>\n            </view>\n            <text class=\"point-desc\">设计既能激励用户分享，又能保证商业可持续的奖励机制。奖励金额要有吸引力，但不宜过高导致亏损。</text>\n          </view>\n          \n          <view class=\"design-point\">\n            <view class=\"point-header\">\n              <view class=\"point-icon\" style=\"background-color: rgba(151, 8, 204, 0.1);\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#9708CC\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                  <path d=\"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"></path>\n                  <circle cx=\"9\" cy=\"7\" r=\"4\"></circle>\n                  <path d=\"M23 21v-2a4 4 0 0 0-3-3.87\"></path>\n                  <path d=\"M16 3.13a4 4 0 0 1 0 7.75\"></path>\n                </svg>\n              </view>\n              <text class=\"point-title\">简单的参与流程</text>\n            </view>\n            <text class=\"point-desc\">裂变过程要简单易懂，减少参与门槛。分享、注册、领取等步骤越简单，转化率越高。</text>\n          </view>\n          \n          <view class=\"design-point\">\n            <view class=\"point-header\">\n              <view class=\"point-icon\" style=\"background-color: rgba(151, 8, 204, 0.1);\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#9708CC\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                  <rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"></rect>\n                  <circle cx=\"8.5\" cy=\"8.5\" r=\"1.5\"></circle>\n                  <polyline points=\"21 15 16 10 5 21\"></polyline>\n                </svg>\n              </view>\n              <text class=\"point-title\">吸引人的视觉设计</text>\n            </view>\n            <text class=\"point-desc\">红包的视觉设计要吸引人，包括封面图、文案和动效等，增强用户分享的意愿。</text>\n          </view>\n          \n          <view class=\"design-point\">\n            <view class=\"point-header\">\n              <view class=\"point-icon\" style=\"background-color: rgba(151, 8, 204, 0.1);\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#9708CC\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                  <path d=\"M22 11.08V12a10 10 0 1 1-5.93-9.14\"></path>\n                  <polyline points=\"22 4 12 14.01 9 11.01\"></polyline>\n                </svg>\n              </view>\n              <text class=\"point-title\">有效的防刷机制</text>\n            </view>\n            <text class=\"point-desc\">设置合理的防刷机制，如手机号验证、实名认证、IP限制等，防止恶意刷取红包。</text>\n          </view>\n        </view>\n        \n        <view class=\"section-title\">\n          <view class=\"title-icon\" style=\"background-color: rgba(151, 8, 204, 0.1);\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#9708CC\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <path d=\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\"></path>\n              <polyline points=\"14 2 14 8 20 8\"></polyline>\n              <line x1=\"16\" y1=\"13\" x2=\"8\" y2=\"13\"></line>\n              <line x1=\"16\" y1=\"17\" x2=\"8\" y2=\"17\"></line>\n              <polyline points=\"10 9 9 9 8 9\"></polyline>\n            </svg>\n          </view>\n          <text class=\"title-text\">裂变红包效果分析</text>\n        </view>\n        \n        <view class=\"metrics-cards\">\n          <view class=\"metrics-card\">\n            <text class=\"metrics-title\">裂变系数</text>\n            <text class=\"metrics-value\">1.8-3.5</text>\n            <text class=\"metrics-desc\">每个用户平均能带来的新用户数量</text>\n          </view>\n          <view class=\"metrics-card\">\n            <text class=\"metrics-title\">获客成本</text>\n            <text class=\"metrics-value\">3-15元</text>\n            <text class=\"metrics-desc\">获取一个新用户的平均成本</text>\n          </view>\n          <view class=\"metrics-card\">\n            <text class=\"metrics-title\">转化率</text>\n            <text class=\"metrics-value\">15%-30%</text>\n            <text class=\"metrics-desc\">被邀请用户的注册转化率</text>\n          </view>\n          <view class=\"metrics-card\">\n            <text class=\"metrics-title\">活跃度</text>\n            <text class=\"metrics-value\">提升40%</text>\n            <text class=\"metrics-desc\">用户活跃度平均提升幅度</text>\n          </view>\n        </view>\n        \n        <view class=\"section-title\">\n          <view class=\"title-icon\" style=\"background-color: rgba(151, 8, 204, 0.1);\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#9708CC\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <path d=\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\"></path>\n              <polyline points=\"14 2 14 8 20 8\"></polyline>\n              <line x1=\"16\" y1=\"13\" x2=\"8\" y2=\"13\"></line>\n              <line x1=\"16\" y1=\"17\" x2=\"8\" y2=\"17\"></line>\n              <polyline points=\"10 9 9 9 8 9\"></polyline>\n            </svg>\n          </view>\n          <text class=\"title-text\">成功案例分析</text>\n        </view>\n        \n        <view class=\"case-study\">\n          <view class=\"case-header\">\n            <text class=\"case-title\">某电商平台新用户裂变案例</text>\n          </view>\n          <view class=\"case-content\">\n            <text class=\"case-desc\">某电商平台推出\"邀请好友，双方各得50元优惠券\"的裂变活动。用户通过分享专属链接邀请好友注册并完成首单，邀请者和被邀请者均可获得50元优惠券。活动期间，平台新增用户20万，获客成本降至8元/人，是传统广告获客成本的1/5，且新用户30天留存率提升了25%。</text>\n          </view>\n          <view class=\"case-results\">\n            <view class=\"result-item\">\n              <text class=\"result-label\">新增用户</text>\n              <text class=\"result-value\">20万</text>\n            </view>\n            <view class=\"result-item\">\n              <text class=\"result-label\">获客成本降低</text>\n              <text class=\"result-value\">80%</text>\n            </view>\n            <view class=\"result-item\">\n              <text class=\"result-label\">留存率提升</text>\n              <text class=\"result-value\">25%</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 联系我们 -->\n      <view class=\"contact-section\">\n        <text class=\"contact-title\">需要定制裂变红包方案？</text>\n        <text class=\"contact-desc\">我们的专业团队可为您量身定制高效的裂变营销方案</text>\n        <button class=\"contact-btn\" @click=\"contactService\">获取方案</button>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      \n    }\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack();\n    },\n    shareGuide() {\n      uni.showActionSheet({\n        itemList: ['分享给好友', '分享到朋友圈', '复制链接'],\n        success: function(res) {\n          uni.showToast({\n            title: '分享成功',\n            icon: 'success'\n          });\n        }\n      });\n    },\n    contactService() {\n      uni.makePhoneCall({\n        phoneNumber: '************'\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n/* 导航栏样式 */\n.navbar {\n  display: flex;\n  align-items: center;\n  height: 44px;\n  background-color: #fff;\n  padding: 0 15px;\n  position: relative;\n}\n\n.navbar-back {\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-top: 2px solid #333;\n  border-left: 2px solid #333;\n  transform: rotate(-45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 17px;\n  font-weight: 600;\n  color: #333;\n}\n\n.navbar-right {\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.share-icon {\n  color: #333;\n}\n\n/* 内容滚动区 */\n.content-scroll {\n  flex: 1;\n}\n\n/* 页面头部 */\n.page-header {\n  height: 180px;\n  position: relative;\n  overflow: hidden;\n}\n\n.header-bg {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  padding: 20px;\n}\n\n.header-title {\n  font-size: 24px;\n  font-weight: 700;\n  color: #fff;\n  margin-bottom: 10px;\n}\n\n.header-subtitle {\n  font-size: 16px;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n/* 内容部分 */\n.content-section {\n  padding: 20px 15px;\n  background-color: #fff;\n  border-radius: 15px 15px 0 0;\n  margin-top: -20px;\n  position: relative;\n  z-index: 1;\n}\n\n.section-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 15px;\n  margin-top: 25px;\n}\n\n.section-title:first-child {\n  margin-top: 0;\n}\n\n.title-icon {\n  width: 36px;\n  height: 36px;\n  border-radius: 18px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 10px;\n}\n\n.title-text {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n}\n\n.content-text {\n  font-size: 15px;\n  color: #666;\n  line-height: 1.6;\n  margin-bottom: 15px;\n}\n\n.content-image {\n  width: 100%;\n  border-radius: 8px;\n  overflow: hidden;\n  margin-bottom: 20px;\n}\n\n.content-image image {\n  width: 100%;\n}\n\n/* 原理图解 */\n.principle-diagram {\n  background-color: #f9f9f9;\n  border-radius: 8px;\n  padding: 20px 15px;\n  margin-bottom: 20px;\n}\n\n.diagram-step {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.step-circle {\n  width: 30px;\n  height: 30px;\n  border-radius: 15px;\n  background-color: #9708CC;\n  color: #fff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 600;\n  margin-right: 10px;\n  flex-shrink: 0;\n}\n\n.step-content {\n  flex: 1;\n}\n\n.step-title {\n  font-size: 15px;\n  font-weight: 600;\n  color: #333;\n  display: block;\n  margin-bottom: 3px;\n}\n\n.step-desc {\n  font-size: 13px;\n  color: #666;\n}\n\n.step-arrow {\n  width: 12px;\n  height: 12px;\n  border-right: 2px solid #9708CC;\n  border-bottom: 2px solid #9708CC;\n  transform: rotate(45deg);\n  margin: 0 auto 15px;\n}\n\n/* 模式列表 */\n.model-list {\n  margin-bottom: 20px;\n}\n\n.model-item {\n  background-color: #f9f9f9;\n  border-radius: 8px;\n  padding: 15px;\n  margin-bottom: 15px;\n}\n\n.model-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.model-icon {\n  width: 36px;\n  height: 36px;\n  border-radius: 18px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 10px;\n}\n\n.model-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.model-content {\n  padding-left: 46px;\n}\n\n.model-desc {\n  font-size: 14px;\n  color: #666;\n  line-height: 1.5;\n  margin-bottom: 10px;\n}\n\n.model-advantages {\n  margin-top: 10px;\n}\n\n.advantages-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n  display: block;\n}\n\n.advantage-point {\n  display: flex;\n  margin-bottom: 5px;\n}\n\n.advantage-point:last-child {\n  margin-bottom: 0;\n}\n\n.point-dot {\n  width: 6px;\n  height: 6px;\n  border-radius: 3px;\n  background-color: #9708CC;\n  margin-top: 6px;\n  margin-right: 8px;\n  flex-shrink: 0;\n}\n\n.point-text {\n  font-size: 13px;\n  color: #666;\n  line-height: 1.4;\n}\n\n/* 设计要点 */\n.design-points {\n  margin-bottom: 20px;\n}\n\n.design-point {\n  margin-bottom: 15px;\n  padding: 15px;\n  background-color: #f9f9f9;\n  border-radius: 8px;\n}\n\n.point-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.point-icon {\n  width: 32px;\n  height: 32px;\n  border-radius: 16px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 10px;\n}\n\n.point-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.point-desc {\n  font-size: 14px;\n  color: #666;\n  line-height: 1.5;\n}\n\n/* 效果指标 */\n.metrics-cards {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -5px 20px;\n}\n\n.metrics-card {\n  width: calc(50% - 10px);\n  margin: 5px;\n  background-color: #f9f9f9;\n  border-radius: 8px;\n  padding: 15px;\n  text-align: center;\n}\n\n.metrics-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n  display: block;\n  margin-bottom: 8px;\n}\n\n.metrics-value {\n  font-size: 20px;\n  font-weight: 700;\n  color: #9708CC;\n  display: block;\n  margin-bottom: 5px;\n}\n\n.metrics-desc {\n  font-size: 12px;\n  color: #666;\n}\n\n/* 案例分析 */\n.case-study {\n  background-color: #f9f9f9;\n  border-radius: 8px;\n  padding: 15px;\n  margin-bottom: 20px;\n}\n\n.case-header {\n  margin-bottom: 10px;\n}\n\n.case-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.case-content {\n  margin-bottom: 15px;\n}\n\n.case-desc {\n  font-size: 14px;\n  color: #666;\n  line-height: 1.5;\n}\n\n.case-results {\n  display: flex;\n  justify-content: space-between;\n  border-top: 1px solid #eee;\n  padding-top: 15px;\n}\n\n.result-item {\n  text-align: center;\n  flex: 1;\n}\n\n.result-label {\n  font-size: 12px;\n  color: #999;\n  display: block;\n  margin-bottom: 5px;\n}\n\n.result-value {\n  font-size: 18px;\n  font-weight: 600;\n  color: #9708CC;\n}\n\n/* 联系我们 */\n.contact-section {\n  margin: 0 15px 30px;\n  padding: 20px;\n  background-color: #fff;\n  border-radius: 10px;\n  text-align: center;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.05);\n}\n\n.contact-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  display: block;\n  margin-bottom: 10px;\n}\n\n.contact-desc {\n  font-size: 14px;\n  color: #666;\n  display: block;\n  margin-bottom: 15px;\n}\n\n.contact-btn {\n  background: linear-gradient(135deg, #43CBFF, #9708CC);\n  color: #fff;\n  border: none;\n  border-radius: 20px;\n  padding: 8px 20px;\n  font-size: 14px;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/redpacket/fission-redpacket-guide.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAsWA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO,CAEP;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,aAAa;AACXA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,SAAS,UAAU,MAAM;AAAA,QACpC,SAAS,SAAS,KAAK;AACrBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,iBAAiB;AACfA,oBAAAA,MAAI,cAAc;AAAA,QAChB,aAAa;AAAA,MACf,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChYA,GAAG,WAAW,eAAe;"}