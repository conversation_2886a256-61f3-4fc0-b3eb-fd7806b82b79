<template>
  <view class="bind-phone-container">
    <!-- 页面标题 -->
    <view class="page-header">
      <view class="header-title">绑定手机号</view>
      <view class="header-desc">绑定手机号可以更安全地使用同城服务</view>
    </view>
    
    <!-- 手机号输入表单 -->
    <view class="form-container">
      <!-- 手机号输入区域 -->
      <view class="input-group">
        <text class="input-label">手机号</text>
        <view class="input-wrapper">
          <input 
            type="number" 
            class="phone-input" 
            placeholder="请输入手机号" 
            maxlength="11" 
            v-model="phone"
            @input="validatePhone"
          />
        </view>
      </view>
      
      <!-- 验证码输入区域 -->
      <view class="input-group">
        <text class="input-label">验证码</text>
        <view class="input-wrapper code-wrapper">
          <input 
            type="number" 
            class="code-input" 
            placeholder="请输入验证码" 
            maxlength="6" 
            v-model="verifyCode"
          />
          <view 
            class="get-code-btn" 
            :class="{ 'get-code-btn-disabled': !canGetCode || countdown > 0 || isSending }"
            @click="getVerifyCode"
          >
            <text v-if="countdown > 0">{{countdown}}秒后重新获取</text>
            <text v-else-if="isSending">发送中...</text>
            <text v-else>获取验证码</text>
          </view>
        </view>
      </view>
      
      <!-- 绑定按钮 -->
      <button 
        class="bind-btn" 
        :class="{ 'bind-btn-active': canBind }" 
        :disabled="!canBind"
        @click="bindPhone"
      >
        绑定手机号
      </button>
      
      <!-- 提示信息 -->
      <view class="tips-text">
        绑定即代表您已阅读并同意
        <text class="link-text" @click="showAgreement('privacy')">《隐私政策》</text>
        和
        <text class="link-text" @click="showAgreement('service')">《服务协议》</text>
      </view>
      
      <!-- 开发模式验证码显示 -->
      <view class="debug-code" v-if="isDev && debugCode">
        <text>测试验证码: {{debugCode}}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { saveUserInfo, getLocalUserInfo } from '@/utils/userProfile.js';
import { sendVerifyCode, verifyCode } from '@/utils/sms.js';

// 响应式数据
const phone = ref('');
const verifyCode = ref('');
const countdown = ref(0);
const isPhoneValid = ref(false);
const isSending = ref(false);
const debugCode = ref('');
const isDev = ref(process.env.NODE_ENV === 'development');

// 计算属性
const canGetCode = computed(() => {
  return isPhoneValid.value && countdown.value === 0 && !isSending.value;
});

const canBind = computed(() => {
  return isPhoneValid.value && verifyCode.value.length === 6;
});

// 方法
const validatePhone = () => {
  // 手机号验证
  const phoneRegex = /^1[3-9]\d{9}$/;
  isPhoneValid.value = phoneRegex.test(phone.value);
};

// 获取验证码
const getVerifyCode = () => {
  if (!canGetCode.value) return;
  
  // 设置发送中状态
  isSending.value = true;
  
  // 调用短信发送服务
  sendVerifyCode(phone.value)
    .then(res => {
      // 开始倒计时
      countdown.value = 60;
      const timer = setInterval(() => {
        countdown.value--;
        if (countdown.value <= 0) {
          clearInterval(timer);
        }
      }, 1000);
      
      // 显示发送成功提示
      uni.showToast({
        title: '验证码已发送',
        icon: 'none'
      });
      
      // 开发环境显示验证码方便测试
      if (isDev.value && res.code) {
        debugCode.value = res.code;
        console.log('当前验证码:', res.code);
      }
    })
    .catch(err => {
      uni.showToast({
        title: err.message || '发送失败，请重试',
        icon: 'none'
      });
    })
    .finally(() => {
      isSending.value = false;
    });
};

// 绑定手机号
const bindPhone = () => {
  if (!canBind.value) return;
  
  // 显示加载状态
  uni.showLoading({
    title: '验证中...',
    mask: true
  });
  
  // 检查验证码是否正确
  if (verifyCode(phone.value, verifyCode.value)) {
    // 验证成功，更新用户信息
    updateUserPhone();
  } else {
    // 验证失败
    uni.hideLoading();
    uni.showToast({
      title: '验证码错误或已过期',
      icon: 'none',
      duration: 2000
    });
  }
};

// 更新用户手机号
const updateUserPhone = () => {
  // 更新本地用户信息，添加手机号
  const userInfo = getLocalUserInfo() || {};
  userInfo.phone = phone.value;
  
  // 保存更新后的用户信息
  saveUserInfo(userInfo);
  
  // 调用接口更新服务端用户信息 (实际项目中应实现)
  // 模拟API调用
  setTimeout(() => {
    uni.hideLoading();
    
    // 显示绑定成功提示
    uni.showToast({
      title: '手机号绑定成功',
      icon: 'success',
      duration: 2000,
      success: () => {
        // 延迟返回上一页，确保提示显示完毕
        setTimeout(() => {
          uni.navigateBack();
        }, 2000);
      }
    });
  }, 1500);
};

// 显示协议
const showAgreement = (type) => {
  const title = type === 'privacy' ? '隐私政策' : '服务协议';
  uni.navigateTo({
    url: `/pages/common/agreement?type=${type}&title=${encodeURIComponent(title)}`
  });
};

// 生命周期钩子
onMounted(() => {
  // 检查用户是否已经绑定手机号
  const userInfo = getLocalUserInfo();
  if (userInfo && userInfo.phone) {
    phone.value = userInfo.phone;
    isPhoneValid.value = true;
    
    // 提示用户已绑定手机号
    uni.showModal({
      title: '提示',
      content: '您已绑定手机号，是否需要更换？',
      cancelText: '返回',
      confirmText: '更换',
      success: (res) => {
        if (res.cancel) {
          // 用户选择返回，返回上一页
          uni.navigateBack();
        }
        // 用户选择更换，留在当前页面
      }
    });
  }
});
</script>

<style>
.bind-phone-container {
  padding: 40rpx;
  background-color: #f8f9fc;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 60rpx;
  padding-top: 20rpx;
}

.header-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.header-desc {
  font-size: 28rpx;
  color: #666;
}

.form-container {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.input-group {
  margin-bottom: 30rpx;
}

.input-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
  font-weight: 500;
}

.input-wrapper {
  display: flex;
  border-bottom: 1px solid #eaeaea;
  padding: 14rpx 0;
}

.phone-input, .code-input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
  color: #333;
}

.code-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.get-code-btn {
  padding: 12rpx 20rpx;
  background-color: #007aff;
  color: #ffffff;
  font-size: 24rpx;
  border-radius: 8rpx;
  white-space: nowrap;
  transition: all 0.3s;
}

.get-code-btn-disabled {
  background-color: #cccccc;
  color: #ffffff;
}

.bind-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #cccccc;
  color: #ffffff;
  font-size: 32rpx;
  border-radius: 45rpx;
  margin-top: 50rpx;
  margin-bottom: 30rpx;
  border: none;
  transition: all 0.3s;
}

.bind-btn-active {
  background-color: #007aff;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}

.tips-text {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  margin-top: 20rpx;
}

.link-text {
  color: #007aff;
}

.debug-code {
  margin-top: 40rpx;
  border-top: 1px dashed #eaeaea;
  padding-top: 20rpx;
  font-size: 24rpx;
  color: #ff6b00;
  text-align: center;
}
</style> 