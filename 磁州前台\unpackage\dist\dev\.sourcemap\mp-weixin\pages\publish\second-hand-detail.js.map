{"version": 3, "file": "second-hand-detail.js", "sources": ["pages/publish/second-hand-detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHVibGlzaC9zZWNvbmQtaGFuZC1kZXRhaWwudnVl"], "sourcesContent": ["<template>\n  <view class=\"second-hand-detail-container\">\n    <!-- 添加自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"navbar-left\" @click=\"goBack\">\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\n      </view>\n      <view class=\"navbar-title\">二手闲置详情</view>\n      <view class=\"navbar-right\">\n        <!-- 占位 -->\n      </view>\n    </view>\n    \n    <!-- 隐藏的分享按钮，用于自动触发 -->\n    <button id=\"shareButton\" class=\"hidden-share-btn\" open-type=\"share\"></button>\n    \n    <view class=\"second-hand-detail-wrapper\">\n      <!-- 商品基本信息卡片 -->\n      <view class=\"content-card goods-info-card\">\n        <view class=\"goods-header\">\n          <view class=\"goods-title-row\">\n            <text class=\"goods-title\">{{goodsData.title}}</text>\n            <text class=\"goods-price\">{{goodsData.price}}</text>\n          </view>\n          <view class=\"goods-meta\">\n            <view class=\"goods-tag-group\">\n              <view class=\"goods-tag\" v-for=\"(tag, index) in goodsData.tags\" :key=\"index\">{{tag}}</view>\n            </view>\n            <text class=\"goods-publish-time\">发布于 {{formatTime(goodsData.publishTime)}}</text>\n          </view>\n        </view>\n        \n        <!-- 商品图片轮播 -->\n        <swiper class=\"goods-swiper\" :indicator-dots=\"true\" :autoplay=\"true\" :interval=\"3000\" :duration=\"500\">\n          <swiper-item v-for=\"(image, index) in goodsData.images\" :key=\"index\">\n            <image :src=\"image\" mode=\"aspectFill\" class=\"goods-image\"></image>\n          </swiper-item>\n        </swiper>\n        \n        <!-- 基本信息 -->\n        <view class=\"goods-basic-info\">\n          <view class=\"info-item\">\n            <text class=\"info-label\">商品类型</text>\n            <text class=\"info-value\">{{goodsData.type}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">新旧程度</text>\n            <text class=\"info-value\">{{goodsData.condition}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">购买时间</text>\n            <text class=\"info-value\">{{goodsData.purchaseTime}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">交易地点</text>\n            <text class=\"info-value\">{{goodsData.location}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 商品描述 -->\n      <view class=\"content-card description-card\">\n        <view class=\"section-title\">商品描述</view>\n        <view class=\"description-content\">\n          <text class=\"description-text\">{{goodsData.description}}</text>\n        </view>\n      </view>\n      \n      <!-- 商品详情 -->\n      <view class=\"content-card details-card\">\n        <view class=\"section-title\">商品详情</view>\n        <view class=\"details-list\">\n          <view class=\"details-item\" v-for=\"(item, index) in goodsData.details\" :key=\"index\">\n            <text class=\"details-label\">{{item.label}}</text>\n            <text class=\"details-value\">{{item.value}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 交易方式 -->\n      <view class=\"content-card trade-card\">\n        <view class=\"section-title\">交易方式</view>\n        <view class=\"trade-list\">\n          <view class=\"trade-item\" v-for=\"(item, index) in goodsData.tradeMethods\" :key=\"index\">\n            <text class=\"trade-icon iconfont\" :class=\"item.icon\"></text>\n            <view class=\"trade-info\">\n              <text class=\"trade-title\">{{item.title}}</text>\n              <text class=\"trade-desc\">{{item.description}}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 卖家信息 -->\n      <view class=\"content-card seller-card\">\n        <view class=\"seller-header\">\n          <view class=\"seller-avatar\">\n            <image :src=\"goodsData.seller.avatar\" mode=\"aspectFill\"></image>\n          </view>\n          <view class=\"seller-info\">\n            <text class=\"seller-name\">{{goodsData.seller.name}}</text>\n            <view class=\"seller-meta\">\n              <text class=\"seller-type\">{{goodsData.seller.type}}</text>\n              <text class=\"seller-rating\">信用等级 {{goodsData.seller.rating}}</text>\n            </view>\n          </view>\n          <view class=\"seller-auth\" v-if=\"goodsData.seller.isVerified\">\n            <text class=\"iconfont icon-verified\"></text>\n            <text class=\"auth-text\">已认证</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 联系方式 -->\n      <view class=\"content-card contact-card\">\n        <view class=\"contact-header\">\n          <text class=\"card-title\">联系方式</text>\n        </view>\n        <view class=\"contact-content\">\n          <view class=\"contact-item\">\n            <text class=\"contact-label\">联系人</text>\n            <text class=\"contact-value\">{{goodsData.contact.name}}</text>\n          </view>\n          <view class=\"contact-item\">\n            <text class=\"contact-label\">电话</text>\n            <text class=\"contact-value contact-phone\" @click=\"callPhone\">{{goodsData.contact.phone}}</text>\n          </view>\n          <view class=\"contact-tips\">\n            <text class=\"tips-icon iconfont icon-info\"></text>\n            <text class=\"tips-text\">请说明在\"磁州生活网\"看到的信息</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 举报卡片 -->\n      <report-card></report-card>\n      \n      <!-- 相关物品推荐 -->\n      <view class=\"content-card related-goods-card\">\n        <view class=\"section-title\">相关物品推荐</view>\n        <view class=\"related-goods-content\">\n          <!-- 简洁的物品列表 -->\n          <view class=\"related-goods-list\">\n            <view class=\"related-goods-item\" \n                 v-for=\"(item, index) in relatedGoods.slice(0, 3)\" \n                 :key=\"index\" \n                 @click=\"navigateToGoodsDetail(item.id)\">\n              <view class=\"goods-item-content\">\n                <view class=\"goods-item-left\">\n                  <image class=\"goods-image\" :src=\"item.image\" mode=\"aspectFill\"></image>\n                </view>\n                <view class=\"goods-item-middle\">\n                  <text class=\"goods-item-title\">{{item.title}}</text>\n                  <view class=\"goods-item-condition\">{{item.condition}}</view>\n                  <view class=\"goods-item-tags\">\n                    <text class=\"goods-item-tag\" v-for=\"(tag, tagIndex) in item.tags.slice(0, 2)\" :key=\"tagIndex\">{{tag}}</text>\n                    <text class=\"goods-item-tag-more\" v-if=\"item.tags.length > 2\">+{{item.tags.length - 2}}</text>\n                  </view>\n                </view>\n                <view class=\"goods-item-right\">\n                  <text class=\"goods-item-price\">{{item.price}}</text>\n                </view>\n              </view>\n            </view>\n            \n            <!-- 暂无数据提示 -->\n            <view class=\"empty-related-goods\" v-if=\"relatedGoods.length === 0\">\n              <image src=\"/static/images/empty.png\" class=\"empty-image\" mode=\"aspectFit\"></image>\n              <text class=\"empty-text\">暂无相关物品</text>\n            </view>\n          </view>\n          \n          <!-- 查看更多按钮 -->\n          <view class=\"view-more-btn\" v-if=\"relatedGoods.length > 0\" @click.stop=\"navigateToGoodsList\">\n            <text class=\"view-more-text\">查看更多二手物品</text>\n            <text class=\"view-more-icon iconfont icon-right\"></text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"interaction-toolbar\">\n      <view class=\"toolbar-item\" @click=\"goToHome\">\n        <image src=\"/static/images/tabbar/a首页.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">首页</text>\n      </view>\n      <view class=\"toolbar-item\" @click=\"toggleCollect\">\n        <image src=\"/static/images/tabbar/a收藏.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">收藏</text>\n      </view>\n      <button class=\"share-button toolbar-item\" open-type=\"share\">\n        <image src=\"/static/images/tabbar/a分享.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">分享</text>\n      </button>\n      <view class=\"toolbar-item\" @click=\"openChat\">\n        <image src=\"/static/images/tabbar/a消息.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">私信</text>\n      </view>\n      <view class=\"toolbar-item call-button\" @click=\"callPhone\">\n        <view class=\"call-button-content\">\n          <text class=\"call-text\">打电话</text>\n          <text class=\"call-subtitle\">请说在磁州生活网看到的</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport ReportCard from '@/components/ReportCard.vue'\n\n// 状态栏高度\nconst statusBarHeight = ref(20);\n\n// 获取状态栏高度\nonMounted(() => {\n  try {\n    const sysInfo = uni.getSystemInfoSync();\n    statusBarHeight.value = sysInfo.statusBarHeight || 20;\n  } catch (e) {\n    console.error('获取状态栏高度失败', e);\n  }\n  \n  // 获取路由参数\n  const pages = getCurrentPages();\n  const currentPage = pages[pages.length - 1];\n  const options = currentPage.options || {};\n  \n  // 获取商品ID\n  const id = options.id || '';\n  console.log('商品详情页ID:', id);\n  \n  // 加载相关物品推荐\n  loadRelatedGoods();\n});\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack({\n    fail: () => {\n      uni.switchTab({\n        url: '/pages/index/index'\n      });\n    }\n  });\n};\n\n// 格式化时间\nconst formatTime = (timestamp) => {\n  const date = new Date(timestamp);\n  return `${date.getMonth() + 1}月${date.getDate()}日`;\n};\n\n// 响应式数据\nconst isCollected = ref(false);\nconst goodsData = ref({\n  id: 'goods12345',\n  title: 'iPhone 13 Pro Max',\n  price: '5999元',\n  tags: ['95新', '无维修', '可面交'],\n  publishTime: Date.now() - 86400000 * 2, // 2天前\n  images: [\n    '/static/images/goods1.jpg',\n    '/static/images/goods2.jpg',\n    '/static/images/goods3.jpg'\n  ],\n  type: '手机数码',\n  condition: '95新',\n  purchaseTime: '2023年1月',\n  location: '磁县城区',\n  description: 'iPhone 13 Pro Max 256G 远峰蓝，2023年1月购买，无维修无进水，配件齐全，可面交验机。',\n  details: [\n    { label: '品牌型号', value: 'iPhone 13 Pro Max' },\n    { label: '内存容量', value: '256G' },\n    { label: '颜色', value: '远峰蓝' },\n    { label: '配件', value: '原装充电器、数据线、包装盒' },\n    { label: '保修', value: '已过保' }\n  ],\n  tradeMethods: [\n    {\n      icon: 'icon-face',\n      title: '当面交易',\n      description: '支持当面验机，确认无误后交易'\n    },\n    {\n      icon: 'icon-delivery',\n      title: '快递交易',\n      description: '支持快递发货，收到货后确认'\n    }\n  ],\n  seller: {\n    name: '张先生',\n    avatar: '/static/images/avatar.png',\n    type: '个人',\n    rating: 'A+',\n    isVerified: true\n  },\n  contact: {\n    name: '张先生',\n    phone: '13912345678'\n  }\n});\n\n// 相关物品推荐数据\nconst relatedGoods = ref([]);\n\n// 加载相关物品推荐\nconst loadRelatedGoods = () => {\n  // 这里应该调用API获取数据\n  // 实际项目中应该根据当前物品的类型、标签等进行相关性匹配\n  \n  // 模拟数据\n  setTimeout(() => {\n    relatedGoods.value = [\n      {\n        id: 'goods001',\n        title: 'iPhone 12 Pro 128G',\n        condition: '95新',\n        price: '4599元',\n        image: '/static/images/goods1.jpg',\n        tags: ['无维修', '面交验机', '保修中']\n      },\n      {\n        id: 'goods002',\n        title: 'iPhone 14 256G',\n        condition: '99新',\n        price: '6999元',\n        image: '/static/images/goods2.jpg',\n        tags: ['官换机', '发票齐全']\n      },\n      {\n        id: 'goods003',\n        title: 'MacBook Pro 2022',\n        condition: '9成新',\n        price: '9800元',\n        image: '/static/images/goods3.jpg',\n        tags: ['M2芯片', '16G内存', '官方保修']\n      }\n    ];\n  }, 500);\n};\n\n// 跳转到商品详情页\nconst navigateToGoodsDetail = (goodsId) => {\n  // 防止跳转到当前页面\n  if (goodsId === goodsData.value.id) {\n    return;\n  }\n  \n  uni.navigateTo({\n    url: `/pages/publish/second-hand-detail?id=${goodsId}`\n  });\n};\n\n// 跳转到二手闲置列表页\nconst navigateToGoodsList = (e) => {\n  if (e) e.stopPropagation();\n  const goodsCategory = goodsData.value.type || '';\n  uni.navigateTo({\n    url: `/subPackages/service/pages/filter?type=second-hand&title=${encodeURIComponent('二手闲置')}&category=${encodeURIComponent(goodsCategory)}&active=second_hand`\n  });\n};\n\n// 方法\nconst toggleCollect = () => {\n  isCollected.value = !isCollected.value;\n  if (isCollected.value) {\n    uni.showToast({\n      title: '收藏成功',\n      icon: 'success'\n    });\n  }\n};\n\nconst showShareOptions = () => {\n  uni.showShareMenu({\n    withShareTicket: true,\n    menus: ['shareAppMessage', 'shareTimeline']\n  });\n};\n\nconst callPhone = () => {\n  uni.makePhoneCall({\n    phoneNumber: goodsData.value.contact.phone,\n    fail: () => {\n      uni.showToast({\n        title: '拨打电话失败',\n        icon: 'none'\n      });\n    }\n  });\n};\n\n// 跳转到首页\nconst goToHome = () => {\n  uni.switchTab({\n    url: '/pages/index/index'\n  });\n};\n\n// 打开私信聊天\nconst openChat = () => {\n  if (!goodsData.value.seller || !goodsData.value.seller.id) {\n    uni.showToast({\n      title: '无法获取卖家信息',\n      icon: 'none'\n    });\n    return;\n  }\n  \n  // 跳转到聊天页面\n  uni.navigateTo({\n    url: `/pages/chat/index?userId=${goodsData.value.seller.id}&username=${encodeURIComponent(goodsData.value.seller.name || '卖家')}`\n  });\n};\n</script>\n\n<style>\n.second-hand-detail-container {\n  min-height: 100vh;\n  background-color: #f5f7fa;\n  padding-bottom: 150rpx;\n  padding-top: 0; /* 移除顶部内边距，由导航栏控制 */\n}\n\n.second-hand-detail-wrapper {\n  padding: 24rpx;\n  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */\n}\n\n.content-card {\n  background-color: #fff;\n  border-radius: 16rpx;\n  margin-bottom: 24rpx;\n  padding: 30rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n}\n\n/* 商品基本信息卡片 */\n.goods-title-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.goods-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.goods-price {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #ff4d4f;\n}\n\n.goods-meta {\n  margin-bottom: 24rpx;\n}\n\n.goods-tag-group {\n  display: flex;\n  flex-wrap: wrap;\n  margin-bottom: 12rpx;\n}\n\n.goods-tag {\n  font-size: 24rpx;\n  color: #1890ff;\n  background-color: rgba(24, 144, 255, 0.1);\n  padding: 4rpx 16rpx;\n  border-radius: 6rpx;\n  margin-right: 16rpx;\n  margin-bottom: 12rpx;\n}\n\n.goods-publish-time {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 轮播图 */\n.goods-swiper {\n  height: 400rpx;\n  border-radius: 12rpx;\n  overflow: hidden;\n  margin-bottom: 24rpx;\n}\n\n.goods-image {\n  width: 100%;\n  height: 100%;\n}\n\n/* 基本信息 */\n.goods-basic-info {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 24rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n}\n\n.info-item {\n  width: 50%;\n  padding: 12rpx 24rpx;\n  box-sizing: border-box;\n}\n\n.info-label {\n  font-size: 26rpx;\n  color: #999;\n  margin-bottom: 8rpx;\n  display: block;\n}\n\n.info-value {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n/* 商品描述 */\n.description-content {\n  padding: 24rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n}\n\n.description-text {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.6;\n}\n\n/* 商品详情 */\n.details-list {\n  display: flex;\n  flex-direction: column;\n}\n\n.details-item {\n  display: flex;\n  padding: 16rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.details-item:last-child {\n  border-bottom: none;\n}\n\n.details-label {\n  width: 160rpx;\n  font-size: 28rpx;\n  color: #999;\n}\n\n.details-value {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333;\n}\n\n/* 交易方式 */\n.trade-list {\n  display: flex;\n  flex-direction: column;\n}\n\n.trade-item {\n  display: flex;\n  align-items: flex-start;\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.trade-item:last-child {\n  border-bottom: none;\n}\n\n.trade-icon {\n  font-size: 40rpx;\n  color: #1890ff;\n  margin-right: 20rpx;\n}\n\n.trade-info {\n  flex: 1;\n}\n\n.trade-title {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.trade-desc {\n  font-size: 26rpx;\n  color: #666;\n}\n\n/* 卖家信息 */\n.seller-header {\n  display: flex;\n  align-items: center;\n}\n\n.seller-avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  overflow: hidden;\n  margin-right: 20rpx;\n}\n\n.seller-avatar image {\n  width: 100%;\n  height: 100%;\n}\n\n.seller-info {\n  flex: 1;\n}\n\n.seller-name {\n  font-size: 30rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.seller-meta {\n  display: flex;\n  align-items: center;\n}\n\n.seller-type, .seller-rating {\n  font-size: 24rpx;\n  color: #666;\n  margin-right: 16rpx;\n}\n\n/* 联系方式 */\n.contact-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24rpx;\n}\n\n.card-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.contact-content {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.contact-item {\n  width: 50%;\n  padding: 12rpx 24rpx;\n  box-sizing: border-box;\n}\n\n.contact-label {\n  font-size: 26rpx;\n  color: #999;\n  margin-bottom: 8rpx;\n  display: block;\n}\n\n.contact-value {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n.contact-phone {\n  color: #ff4d4f;\n}\n\n.contact-tips {\n  width: 100%;\n  padding: 12rpx 24rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n  margin-top: 24rpx;\n  display: flex;\n  align-items: center;\n}\n\n.tips-icon {\n  font-size: 40rpx;\n  color: #1890ff;\n  margin-right: 16rpx;\n}\n\n.tips-text {\n  font-size: 28rpx;\n  color: #666;\n}\n\n/* 相关物品推荐 */\n.related-goods-card {\n  margin-bottom: 24rpx;\n}\n\n.related-goods-content {\n  padding: 24rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n}\n\n.related-goods-list {\n  margin-bottom: 24rpx;\n}\n\n.related-goods-item {\n  display: flex;\n  align-items: center;\n  padding: 16rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.related-goods-item:last-child {\n  border-bottom: none;\n}\n\n.goods-item-content {\n  display: flex;\n  align-items: center;\n}\n\n.goods-item-left {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 8rpx;\n  overflow: hidden;\n  margin-right: 16rpx;\n}\n\n.goods-item-left image {\n  width: 100%;\n  height: 100%;\n}\n\n.goods-item-middle {\n  flex: 1;\n}\n\n.goods-item-title {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.goods-item-condition {\n  font-size: 24rpx;\n  color: #999;\n  margin-bottom: 8rpx;\n}\n\n.goods-item-tags {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.goods-item-tag {\n  font-size: 24rpx;\n  color: #1890ff;\n  background-color: rgba(24, 144, 255, 0.1);\n  padding: 4rpx 16rpx;\n  border-radius: 6rpx;\n  margin-right: 16rpx;\n  margin-bottom: 8rpx;\n}\n\n.goods-item-tag-more {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.goods-item-right {\n  width: 120rpx;\n  text-align: right;\n}\n\n.goods-item-price {\n  font-size: 28rpx;\n  color: #ff4d4f;\n  font-weight: 500;\n}\n\n.empty-related-goods {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 40rpx 0;\n}\n\n.empty-image {\n  width: 120rpx;\n  height: 120rpx;\n  margin-bottom: 16rpx;\n}\n\n.empty-text {\n  font-size: 28rpx;\n  color: #999;\n}\n\n.view-more-btn {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 16rpx 0;\n  background-color: #fff;\n  border-radius: 12rpx;\n  border: 1rpx solid #f0f0f0;\n}\n\n.view-more-text {\n  font-size: 28rpx;\n  color: #1890ff;\n  font-weight: 500;\n  margin-right: 8rpx;\n}\n\n.view-more-icon {\n  font-size: 24rpx;\n  color: #1890ff;\n}\n\n/* 底部互动工具栏 */\n.interaction-toolbar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background-color: #fff;\n  padding: 10rpx 10rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-top: 1rpx solid #f0f0f0;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  height: 120rpx;\n  z-index: 100;\n}\n\n.toolbar-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 6rpx 0;\n  margin: 0 4rpx;\n}\n\n.toolbar-icon {\n  width: 44rpx;\n  height: 44rpx;\n  margin-bottom: 6rpx;\n}\n\n.toolbar-text {\n  font-size: 22rpx;\n  color: #666;\n}\n\n.share-button {\n  background: transparent;\n  border: none;\n  margin: 0;\n  padding: 0;\n  line-height: normal;\n  border-radius: 0;\n  flex: 1;\n}\n\n.share-button::after {\n  display: none;\n}\n\n.call-button {\n  flex: 3; /* 占据更多空间，原来是2，现在改为3让按钮更长 */\n  background: linear-gradient(135deg, #0052CC, #0066FF);\n  height: 90rpx;\n  margin: 0 0 0 10rpx;\n  border-radius: 45rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n}\n\n.call-button-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n}\n\n.call-text {\n  color: #fff;\n  font-size: 30rpx;\n  font-weight: bold;\n  line-height: 1.2;\n}\n\n.call-subtitle {\n  color: rgba(255, 255, 255, 0.9);\n  font-size: 20rpx;\n  line-height: 1.2;\n}\n\n/* 隐藏原来的底部操作栏 */\n.action-bar {\n  display: none;\n}\n\n/* 隐藏的分享按钮 */\n.hidden-share-btn {\n  position: absolute;\n  top: -9999rpx;\n  left: -9999rpx;\n  width: 0;\n  height: 0;\n  padding: 0;\n  margin: 0;\n  opacity: 0;\n}\n\n/* 自定义导航栏样式 */\n.custom-navbar {\n  background: linear-gradient(135deg, #0066FF, #0052CC);\n  height: 88rpx;\n  padding-top: 44px; /* 状态栏高度 */\n  display: flex;\n  align-items: center;\n  position: fixed; /* 改为固定定位 */\n  top: 0;\n  left: 0;\n  right: 0;\n  padding-bottom: 10rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n  z-index: 100; /* 提高z-index确保在最上层 */\n}\n\n.navbar-left {\n  width: 60px;\n  display: flex;\n  align-items: center;\n}\n\n.back-icon {\n  width: 20px;\n  height: 20px;\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 17px;\n  font-weight: 500;\n  color: #fff;\n}\n\n.navbar-right {\n  width: 60px;\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/publish/second-hand-detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni", "MiniProgramPage"], "mappings": ";;;;;;AAmNA,MAAM,aAAa,MAAW;;;;AAG9B,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAG9BC,kBAAAA,UAAU,MAAM;AACd,UAAI;AACF,cAAM,UAAUC,oBAAI;AACpB,wBAAgB,QAAQ,QAAQ,mBAAmB;AAAA,MACpD,SAAQ,GAAG;AACVA,0FAAc,aAAa,CAAC;AAAA,MAC7B;AAGD,YAAM,QAAQ;AACd,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,UAAU,YAAY,WAAW;AAGvC,YAAM,KAAK,QAAQ,MAAM;AACzBA,oBAAY,MAAA,MAAA,OAAA,+CAAA,YAAY,EAAE;AAG1B;IACF,CAAC;AAGD,UAAM,SAAS,MAAM;AACnBA,oBAAAA,MAAI,aAAa;AAAA,QACf,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,KAAK;AAAA,UACb,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,cAAc;AAChC,YAAM,OAAO,IAAI,KAAK,SAAS;AAC/B,aAAO,GAAG,KAAK,aAAa,CAAC,IAAI,KAAK,SAAS;AAAA,IACjD;AAGA,UAAM,cAAcF,cAAAA,IAAI,KAAK;AAC7B,UAAM,YAAYA,cAAAA,IAAI;AAAA,MACpB,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM,CAAC,OAAO,OAAO,KAAK;AAAA,MAC1B,aAAa,KAAK,IAAK,IAAG,QAAW;AAAA;AAAA,MACrC,QAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,MACD,MAAM;AAAA,MACN,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,aAAa;AAAA,MACb,SAAS;AAAA,QACP,EAAE,OAAO,QAAQ,OAAO,oBAAqB;AAAA,QAC7C,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,QAChC,EAAE,OAAO,MAAM,OAAO,MAAO;AAAA,QAC7B,EAAE,OAAO,MAAM,OAAO,gBAAiB;AAAA,QACvC,EAAE,OAAO,MAAM,OAAO,MAAO;AAAA,MAC9B;AAAA,MACD,cAAc;AAAA,QACZ;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,MACF;AAAA,MACD,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,YAAY;AAAA,MACb;AAAA,MACD,SAAS;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACH,CAAC;AAGD,UAAM,eAAeA,cAAAA,IAAI,CAAA,CAAE;AAG3B,UAAM,mBAAmB,MAAM;AAK7B,iBAAW,MAAM;AACf,qBAAa,QAAQ;AAAA,UACnB;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,WAAW;AAAA,YACX,OAAO;AAAA,YACP,OAAO;AAAA,YACP,MAAM,CAAC,OAAO,QAAQ,KAAK;AAAA,UAC5B;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,WAAW;AAAA,YACX,OAAO;AAAA,YACP,OAAO;AAAA,YACP,MAAM,CAAC,OAAO,MAAM;AAAA,UACrB;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,WAAW;AAAA,YACX,OAAO;AAAA,YACP,OAAO;AAAA,YACP,MAAM,CAAC,QAAQ,SAAS,MAAM;AAAA,UAC/B;AAAA,QACP;AAAA,MACG,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,wBAAwB,CAAC,YAAY;AAEzC,UAAI,YAAY,UAAU,MAAM,IAAI;AAClC;AAAA,MACD;AAEDE,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,wCAAwC,OAAO;AAAA,MACxD,CAAG;AAAA,IACH;AAGA,UAAM,sBAAsB,CAAC,MAAM;AACjC,UAAI;AAAG,UAAE;AACT,YAAM,gBAAgB,UAAU,MAAM,QAAQ;AAC9CA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4DAA4D,mBAAmB,MAAM,CAAC,aAAa,mBAAmB,aAAa,CAAC;AAAA,MAC7I,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,MAAM;AAC1B,kBAAY,QAAQ,CAAC,YAAY;AACjC,UAAI,YAAY,OAAO;AACrBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AASA,UAAM,YAAY,MAAM;AACtBA,oBAAAA,MAAI,cAAc;AAAA,QAChB,aAAa,UAAU,MAAM,QAAQ;AAAA,QACrC,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrB,UAAI,CAAC,UAAU,MAAM,UAAU,CAAC,UAAU,MAAM,OAAO,IAAI;AACzDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAGDA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4BAA4B,UAAU,MAAM,OAAO,EAAE,aAAa,mBAAmB,UAAU,MAAM,OAAO,QAAQ,IAAI,CAAC;AAAA,MAClI,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/ZA,GAAG,WAAWC,SAAe;"}