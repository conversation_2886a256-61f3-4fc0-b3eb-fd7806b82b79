"use strict";
const common_vendor = require("../../../../../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_line = common_vendor.resolveComponent("line");
  (_component_path + _component_svg + _component_circle + _component_line)();
}
const _sfc_main = {
  __name: "promotion-tool",
  setup(__props) {
    const promotionParams = common_vendor.reactive({
      type: "product",
      // 默认类型
      id: "",
      title: "",
      image: "",
      extraParams: {}
      // 存储额外参数
    });
    common_vendor.reactive({
      name: "磁州同城生活",
      slogan: "本地生活好物优选",
      logo: "/static/images/logo.png"
    });
    common_vendor.ref("D88652");
    const posterTemplates = common_vendor.ref([]);
    const defaultTemplates = [
      {
        name: "商品推广",
        thumb: "/static/images/distribution/poster-thumb-1.png",
        url: "/static/images/distribution/poster-1.png",
        type: "product"
      },
      {
        name: "店铺推广",
        thumb: "/static/images/distribution/poster-thumb-2.png",
        url: "/static/images/distribution/poster-2.png",
        type: "store"
      },
      {
        name: "活动推广",
        thumb: "/static/images/distribution/poster-thumb-3.png",
        url: "/static/images/distribution/poster-3.png",
        type: "activity"
      },
      {
        name: "会员推广",
        thumb: "/static/images/distribution/poster-thumb-4.png",
        url: "/static/images/distribution/poster-4.png",
        type: "member"
      },
      {
        name: "节日主题",
        thumb: "/static/images/distribution/poster-thumb-5.png",
        url: "/static/images/distribution/poster-5.png",
        type: "general"
      }
    ];
    const typeTemplates = {
      "carpool": [
        {
          name: "拼车专用",
          thumb: "/static/images/distribution/carpool-thumb-1.png",
          url: "/static/images/distribution/carpool-1.png",
          type: "carpool"
        }
      ],
      "secondhand": [
        {
          name: "二手商品专用",
          thumb: "/static/images/distribution/secondhand-thumb-1.png",
          url: "/static/images/distribution/secondhand-1.png",
          type: "secondhand"
        }
      ],
      "house": [
        {
          name: "房屋租售专用",
          thumb: "/static/images/distribution/house-thumb-1.png",
          url: "/static/images/distribution/house-1.png",
          type: "house"
        }
      ],
      "service": [
        {
          name: "服务推广专用",
          thumb: "/static/images/distribution/service-thumb-1.png",
          url: "/static/images/distribution/service-1.png",
          type: "service"
        }
      ],
      "community": [
        {
          name: "社区信息专用",
          thumb: "/static/images/distribution/community-thumb-1.png",
          url: "/static/images/distribution/community-1.png",
          type: "community"
        }
      ]
    };
    const historyPosters = common_vendor.reactive([
      { image: "/static/images/distribution/poster-1.png", date: "2023-04-25" },
      { image: "/static/images/distribution/poster-2.png", date: "2023-04-20" },
      { image: "/static/images/distribution/poster-3.png", date: "2023-04-15" },
      { image: "/static/images/distribution/poster-4.png", date: "2023-04-10" }
    ]);
    const posterTypes = [
      {
        name: "店铺",
        value: "store",
        icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M9 22V12H15V22" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
      },
      {
        name: "商品",
        value: "product",
        icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20 7L12 3L4 7M20 7V17L12 21M20 7L12 11M12 21L4 17V7M12 21V11M4 7L12 11" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
      },
      {
        name: "活动",
        value: "activity",
        icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
      },
      {
        name: "会员",
        value: "member",
        icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
      }
    ];
    const themeColors = [
      { bgColor: "#FFFFFF", textColor: "#333333" },
      { bgColor: "#6B0FBE", textColor: "#FFFFFF" },
      { bgColor: "#A764CA", textColor: "#FFFFFF" },
      { bgColor: "#F5F7FA", textColor: "#333333" },
      { bgColor: "#FFE8F0", textColor: "#FF3B30" },
      { bgColor: "#E8F8FF", textColor: "#007AFF" }
    ];
    const currentPosterIndex = common_vendor.ref(0);
    const selectedType = common_vendor.ref("product");
    const selectedTheme = common_vendor.ref(themeColors[0]);
    const showLogo = common_vendor.ref(true);
    const showId = common_vendor.ref(true);
    const showPromoText = common_vendor.ref(false);
    const customText = common_vendor.ref("");
    const currentPoster = common_vendor.computed(() => {
      if (posterTemplates.value.length === 0)
        return "";
      return posterTemplates.value[currentPosterIndex.value].url;
    });
    common_vendor.onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      parsePromotionParams(options);
      loadPosterTemplates();
      generatePromotionContent();
    });
    const parsePromotionParams = (options) => {
      promotionParams.type = options.type || "product";
      promotionParams.id = options.id || "";
      promotionParams.title = options.title ? decodeURIComponent(options.title) : "";
      promotionParams.image = options.image ? decodeURIComponent(options.image) : "";
      const extraParams = {};
      Object.keys(options).forEach((key) => {
        if (!["type", "id", "title", "image"].includes(key)) {
          extraParams[key] = decodeURIComponent(options[key] || "");
        }
      });
      promotionParams.extraParams = extraParams;
      selectedType.value = promotionParams.type;
    };
    const loadPosterTemplates = () => {
      const typeSpecificTemplates = typeTemplates[promotionParams.type] || [];
      posterTemplates.value = [
        ...typeSpecificTemplates,
        ...defaultTemplates.filter(
          (template) => template.type === "general" || template.type === promotionParams.type
        )
      ];
      if (posterTemplates.value.length === 0) {
        posterTemplates.value = defaultTemplates;
      }
    };
    const generatePromotionContent = () => {
      switch (promotionParams.type) {
        case "carpool":
          customText.value = `【${promotionParams.title}】${promotionParams.extraParams.departure}→${promotionParams.extraParams.destination}，出发时间：${promotionParams.extraParams.departureTime}，车费：¥${promotionParams.extraParams.price}，剩余座位：${promotionParams.extraParams.seats}个`;
          break;
        case "secondhand":
          customText.value = `【${promotionParams.title}】${promotionParams.extraParams.condition || ""}，原价：¥${promotionParams.extraParams.originalPrice || ""}，现价：¥${promotionParams.extraParams.price || ""}`;
          break;
        case "house":
          customText.value = `【${promotionParams.title}】${promotionParams.extraParams.location || ""}，${promotionParams.extraParams.area || ""}㎡，${promotionParams.extraParams.roomType || ""}，${promotionParams.extraParams.rentType || ""}：¥${promotionParams.extraParams.price || ""}`;
          break;
        case "service":
          customText.value = `【${promotionParams.title}】${promotionParams.extraParams.category || ""}，${promotionParams.extraParams.merchantName || ""}提供，价格：¥${promotionParams.extraParams.price || ""}`;
          break;
        case "product":
          customText.value = `【${promotionParams.title}】${promotionParams.extraParams.category || ""}，价格：¥${promotionParams.extraParams.price || ""}`;
          break;
        case "content":
          customText.value = `【${promotionParams.title}】${promotionParams.extraParams.summary || ""}`;
          break;
        case "job":
          customText.value = `【${promotionParams.title}】${promotionParams.extraParams.company || ""}招聘，薪资：${promotionParams.extraParams.salary || ""}，地点：${promotionParams.extraParams.location || ""}`;
          break;
        case "activity":
          customText.value = `【${promotionParams.title}】时间：${promotionParams.extraParams.startTime || ""}，地点：${promotionParams.extraParams.location || ""}，主办方：${promotionParams.extraParams.organizer || ""}`;
          break;
        case "community":
          customText.value = `【${promotionParams.title}】${promotionParams.extraParams.category || ""}，地点：${promotionParams.extraParams.location || ""}，联系方式：${promotionParams.extraParams.contact || ""}`;
          break;
        default:
          customText.value = `【${promotionParams.title}】`;
      }
      showPromoText.value = true;
    };
    const selectPoster = (index) => {
      currentPosterIndex.value = index;
    };
    const selectTheme = (theme) => {
      selectedTheme.value = theme;
    };
    const selectType = (type) => {
      selectedType.value = type;
    };
    const toggleLogo = (e) => {
      showLogo.value = e.detail.value;
    };
    const toggleId = (e) => {
      showId.value = e.detail.value;
    };
    const togglePromoText = (e) => {
      showPromoText.value = e.detail.value;
    };
    const savePoster = () => {
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "保存成功",
          icon: "success"
        });
        const now = /* @__PURE__ */ new Date();
        const dateStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, "0")}-${String(now.getDate()).padStart(2, "0")}`;
        historyPosters.unshift({
          image: currentPoster.value,
          date: dateStr
        });
      }, 1500);
    };
    const sharePoster = () => {
      common_vendor.index.showActionSheet({
        itemList: ["分享给朋友", "分享到朋友圈"],
        success: (res) => {
          common_vendor.index.showToast({
            title: "分享成功",
            icon: "success"
          });
          const shareType = res.tapIndex === 0 ? "friend" : "timeline";
          logShareEvent(shareType);
        }
      });
    };
    const logShareEvent = (shareType) => {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-tool.vue:501", "记录分享事件", {
        type: promotionParams.type,
        id: promotionParams.id,
        shareType
      });
    };
    const loadHistoryPoster = (item) => {
      common_vendor.index.showToast({
        title: "加载历史海报",
        icon: "none"
      });
    };
    const clearHistory = () => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要清空历史记录吗？",
        success: (res) => {
          if (res.confirm) {
            historyPosters.splice(0, historyPosters.length);
            common_vendor.index.showToast({
              title: "已清空",
              icon: "success"
            });
          }
        }
      });
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const showHelp = () => {
      common_vendor.index.showModal({
        title: "推广海报使用帮助",
        content: "选择喜欢的海报模板，自定义颜色和文案，生成精美海报进行分享推广。",
        showCancel: false
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(goBack),
        b: common_vendor.o(showHelp),
        c: currentPoster.value,
        d: selectedTheme.value.textColor,
        e: selectedTheme.value.bgColor,
        f: common_vendor.p({
          d: "M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        g: common_vendor.p({
          d: "M7 10L12 15L17 10",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        h: common_vendor.p({
          d: "M12 15V3",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        i: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        j: common_vendor.o(savePoster),
        k: common_vendor.p({
          cx: "18",
          cy: "5",
          r: "3",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        l: common_vendor.p({
          cx: "6",
          cy: "12",
          r: "3",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        m: common_vendor.p({
          cx: "18",
          cy: "19",
          r: "3",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        n: common_vendor.p({
          x1: "8.59",
          y1: "13.51",
          x2: "15.42",
          y2: "17.49",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        o: common_vendor.p({
          x1: "15.41",
          y1: "6.51",
          x2: "8.59",
          y2: "10.49",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        p: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        q: common_vendor.o(sharePoster),
        r: common_vendor.f(posterTemplates.value, (poster, index, i0) => {
          return {
            a: poster.thumb,
            b: index,
            c: currentPosterIndex.value === index ? 1 : "",
            d: common_vendor.o(($event) => selectPoster(index), index)
          };
        }),
        s: common_vendor.f(themeColors, (theme, index, i0) => {
          return common_vendor.e({
            a: selectedTheme.value === theme
          }, selectedTheme.value === theme ? {
            b: "04fe5d2e-11-" + i0 + "," + ("04fe5d2e-10-" + i0),
            c: common_vendor.p({
              d: "M20 6L9 17L4 12",
              stroke: "white",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            d: "04fe5d2e-10-" + i0,
            e: common_vendor.p({
              width: "16",
              height: "16",
              viewBox: "0 0 24 24",
              fill: "none",
              xmlns: "http://www.w3.org/2000/svg"
            })
          } : {}, {
            f: index,
            g: selectedTheme.value === theme ? 1 : "",
            h: theme.bgColor,
            i: common_vendor.o(($event) => selectTheme(theme), index)
          });
        }),
        t: common_vendor.f(posterTypes, (type, index, i0) => {
          return {
            a: type.icon,
            b: common_vendor.t(type.name),
            c: index,
            d: selectedType.value === type.value ? 1 : "",
            e: common_vendor.o(($event) => selectType(type.value), index)
          };
        }),
        v: showLogo.value,
        w: common_vendor.o(toggleLogo),
        x: showId.value,
        y: common_vendor.o(toggleId),
        z: showPromoText.value,
        A: common_vendor.o(togglePromoText),
        B: showPromoText.value
      }, showPromoText.value ? {
        C: customText.value,
        D: common_vendor.o(($event) => customText.value = $event.detail.value)
      } : {}, {
        E: common_vendor.o(clearHistory),
        F: common_vendor.f(historyPosters, (item, index, i0) => {
          return {
            a: item.image,
            b: common_vendor.t(item.date),
            c: index,
            d: common_vendor.o(($event) => loadHistoryPoster(), index)
          };
        })
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-tool.js.map
