
.service-detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 150rpx;
  padding-top: 0; /* 移除顶部内边距，由导航栏控制 */
}

/* 自定义导航栏样式 */
.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}
.navbar-left {
  width: 60px;
  display: flex;
  align-items: center;
}
.back-icon {
  width: 20px;
  height: 20px;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 500;
  color: #fff;
}
.navbar-right {
  width: 60px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.service-detail-wrapper {
  padding: 24rpx;
  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */
}

/* 内容卡片基础样式 */
.content-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 服务基本信息卡片 */
.service-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.service-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.service-price {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff4d4f;
}
.service-meta {
  margin-bottom: 24rpx;
}
.service-tag-group {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12rpx;
}
.service-tag {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
  margin-bottom: 12rpx;
}
.service-publish-time {
  font-size: 24rpx;
  color: #999;
}

/* 轮播图 */
.service-swiper {
  height: 400rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.service-image {
  width: 100%;
  height: 100%;
}

/* 基本信息 */
.service-basic-info {
  display: flex;
  flex-wrap: wrap;
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  border: 1px solid rgba(24, 144, 255, 0.1);
}
.info-item {
  width: 50%;
  padding: 12rpx 24rpx;
  box-sizing: border-box;
}
.info-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}
.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 区块标题优化 - 添加蓝色竖线 */
.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 16rpx;
}
.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 6rpx;
  height: 28rpx;
  background-color: #1890ff;
  border-radius: 3rpx;
}

/* 服务内容 */
.content-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -12rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  padding: 16rpx;
}
.content-item {
  width: 33.33%;
  padding: 12rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}
.content-icon {
  font-size: 32rpx;
  color: #1890ff;
  margin-right: 8rpx;
}
.content-text {
  font-size: 26rpx;
  color: #666;
}

/* 服务说明 */
.desc-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  padding: 20rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  border: 1px solid rgba(24, 144, 255, 0.1);
}

/* 服务保障 */
.guarantee-list {
  display: flex;
  flex-direction: column;
  background-color: #f9fafc;
  border-radius: 12rpx;
  padding: 16rpx;
}
.guarantee-item {
  display: flex;
  align-items: flex-start;
  padding: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.guarantee-item:last-child {
  border-bottom: none;
}
.guarantee-icon {
  font-size: 40rpx;
  color: #1890ff;
  margin-right: 16rpx;
}
.guarantee-info {
  flex: 1;
}
.guarantee-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}
.guarantee-desc {
  font-size: 26rpx;
  color: #666;
}

/* 服务商信息 */
.provider-header {
  display: flex;
  align-items: center;
  background-color: #f9fafc;
  border-radius: 12rpx;
  padding: 20rpx;
}
.provider-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
  border: 2rpx solid #e8e8e8;
}
.provider-avatar image {
  width: 100%;
  height: 100%;
}
.provider-info {
  flex: 1;
}
.provider-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}
.provider-meta {
  display: flex;
  align-items: center;
}
.provider-type, .provider-rating {
  font-size: 24rpx;
  color: #666;
  margin-right: 16rpx;
}

/* 联系方式样式 - 电话显示为绿色，提示为黄色 */
.contact-content {
  padding: 20rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}
.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.contact-label {
  width: 120rpx;
  font-size: 28rpx;
  color: #888;
}
.contact-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.contact-phone {
  color: #52c41a; /* 修改为绿色 */
  font-weight: 500;
}
.contact-tips {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
  padding: 10rpx 16rpx;
  background-color: rgba(255, 152, 0, 0.1);
  border-radius: 8rpx;
}
.tips-icon {
  font-size: 24rpx;
  color: #ff9800; /* 黄色 */
  margin-right: 8rpx;
}
.tips-text {
  font-size: 24rpx;
  color: #ff9800; /* 黄色 */
}

/* 举报卡片样式 */
.report-card {
  margin: 0 auto 24rpx;
  padding: 0;
  overflow: hidden;
}
.report-content {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  background-color: #fff;
  position: relative;
}
.report-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
}
.report-text-container {
  flex: 1;
}
.report-title {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
}
.report-subtitle {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
  line-height: 1.4;
}
.report-arrow {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 40rpx;
}
.arrow-icon {
  width: 16rpx;
  height: 16rpx;
  border-top: 2rpx solid #999;
  border-right: 2rpx solid #999;
  transform: rotate(45deg);
}
.arrow-icon::after {
  content: '';
  display: inline-block;
  width: 16rpx;
  height: 16rpx;
  border-top: 2rpx solid #999;
  border-right: 2rpx solid #999;
  transform: rotate(45deg);
}
.report-content:active {
  background-color: #f9f9f9;
}

/* 底部互动工具栏 */
.interaction-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10rpx 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  height: 120rpx;
  z-index: 100;
}
.toolbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6rpx 0;
  margin: 0 4rpx;
}
.toolbar-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}
.toolbar-text {
  font-size: 22rpx;
  color: #666;
}
.share-button {
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
  line-height: normal;
  border-radius: 0;
  flex: 1;
}
.share-button::after {
  display: none;
}
.call-button {
  flex: 3; /* 占据更多空间，原来是2，现在改为3让按钮更长 */
  background: linear-gradient(135deg, #0052CC, #0066FF);
  height: 90rpx;
  margin: 0 0 0 10rpx;
  border-radius: 45rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
}
.call-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.call-text {
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  line-height: 1.2;
}
.call-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 20rpx;
  line-height: 1.2;
}

/* 隐藏原来的底部操作栏 */
.action-bar {
  display: none;
}

/* 隐藏的分享按钮 */
.hidden-share-btn {
  position: absolute;
  top: -9999rpx;
  left: -9999rpx;
  width: 0;
  height: 0;
  padding: 0;
  margin: 0;
  opacity: 0;
}

/* 悬浮海报按钮 */
.float-poster-btn {
  position: fixed;
  right: 30rpx;
  bottom: 200rpx;
  width: 100rpx;
  height: 100rpx;
  background: rgba(240, 240, 240, 0.9);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.08);
  z-index: 90;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1rpx solid rgba(230, 230, 230, 0.6);
  transition: all 0.2s ease;
}
.float-poster-btn:active {
  transform: scale(0.95);
  box-shadow: 0 3rpx 10rpx rgba(0,0,0,0.08);
}
.poster-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 4rpx;
}
.poster-text {
  font-size: 20rpx;
  color: #444;
  line-height: 1;
}

/* 相关服务推荐样式 */
.related-services-card {
  margin-top: 12px;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}
.related-services-content {
  padding: 0 16px 16px;
  overflow: hidden;
}

/* 相关服务列表样式 */
.related-services-list {
  margin-bottom: 12px;
}
.related-service-item {
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}
.related-service-item:last-child {
  border-bottom: none;
}
.service-item-content {
  display: flex;
  align-items: center;
}
.service-item-left {
  margin-right: 12px;
}
.provider-logo {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #f5f7fa;
}
.service-item-middle {
  flex: 1;
  overflow: hidden;
}
.service-item-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.service-item-provider {
  font-size: 13px;
  color: #666;
  margin-bottom: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.service-item-tags {
  display: flex;
  flex-wrap: wrap;
}
.service-item-tag {
  font-size: 11px;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  margin-right: 6px;
}
.service-item-tag-more {
  font-size: 11px;
  color: #999;
}
.service-item-right {
  min-width: 80px;
  text-align: right;
}
.service-item-price {
  font-size: 15px;
  font-weight: 500;
  color: #ff5252;
}

/* 查看更多按钮样式 */
.view-more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  background-color: #f7f9fc;
  border-radius: 8px;
  margin-top: 8px;
}
.view-more-text {
  font-size: 14px;
  color: #1890ff;
}
.view-more-icon {
  margin-left: 4px;
  font-size: 12px;
  color: #1890ff;
}

/* 空数据提示样式 */
.empty-related-services {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 0;
}
.empty-image {
  width: 80px;
  height: 80px;
  margin-bottom: 12px;
}
.empty-text {
  font-size: 14px;
  color: #999;
}
