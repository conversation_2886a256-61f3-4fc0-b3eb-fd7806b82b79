/**
 * 活动服务 - 用于营销中心和活动中心之间的数据互通
 */

// 模拟活动数据存储
let activities = [];

// 活动类型枚举
const ActivityType = {
  FLASH: 'flash',
  GROUP: 'group',
  DISCOUNT: 'discount',
  COUPON: 'coupon'
};

// 活动状态枚举
const ActivityStatus = {
  DRAFT: 'draft',      // 草稿
  PUBLISHED: 'published', // 已发布
  ONGOING: 'ongoing',  // 进行中
  ENDED: 'ended',      // 已结束
  CANCELLED: 'cancelled' // 已取消
};

/**
 * 活动服务类
 */
class ActivityService {
  /**
   * 创建活动
   * @param {Object} activityData 活动数据
   * @returns {Object} 创建的活动对象
   */
  static createActivity(activityData) {
    const id = `activity_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
    const activity = {
      id,
      ...activityData,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString(),
      status: ActivityStatus.DRAFT
    };
    
    activities.push(activity);
    return activity;
  }
  
  /**
   * 发布活动
   * @param {String} id 活动ID
   * @param {Object} publishOptions 发布选项
   * @returns {Object} 更新后的活动对象
   */
  static publishActivity(id, publishOptions = {}) {
    const activity = activities.find(a => a.id === id);
    if (!activity) return null;
    
    activity.status = ActivityStatus.PUBLISHED;
    activity.updateTime = new Date().toISOString();
    activity.publishTime = new Date().toISOString();
    activity.publishOptions = publishOptions;
    
    return activity;
  }
  
  /**
   * 获取活动列表
   * @param {Object} filters 过滤条件
   * @returns {Array} 活动列表
   */
  static getActivities(filters = {}) {
    let result = [...activities];
    
    // 按类型过滤
    if (filters.type) {
      result = result.filter(a => a.type === filters.type);
    }
    
    // 按状态过滤
    if (filters.status) {
      result = result.filter(a => a.status === filters.status);
    }
    
    // 只获取已发布的活动
    if (filters.onlyPublished) {
      result = result.filter(a => a.status === ActivityStatus.PUBLISHED);
    }
    
    // 按商家ID过滤
    if (filters.merchantId) {
      result = result.filter(a => a.merchantId === filters.merchantId);
    }
    
    // 按结束时间排序
    if (filters.sortByEndTime) {
      result.sort((a, b) => new Date(a.endTime) - new Date(b.endTime));
    }
    
    // 分页
    if (filters.page && filters.pageSize) {
      const start = (filters.page - 1) * filters.pageSize;
      const end = start + filters.pageSize;
      result = result.slice(start, end);
    }
    
    return result;
  }
  
  /**
   * 获取单个活动
   * @param {String} id 活动ID
   * @returns {Object} 活动对象
   */
  static getActivity(id) {
    return activities.find(a => a.id === id) || null;
  }
  
  /**
   * 更新活动
   * @param {String} id 活动ID
   * @param {Object} updateData 更新数据
   * @returns {Object} 更新后的活动对象
   */
  static updateActivity(id, updateData) {
    const index = activities.findIndex(a => a.id === id);
    if (index === -1) return null;
    
    activities[index] = {
      ...activities[index],
      ...updateData,
      updateTime: new Date().toISOString()
    };
    
    return activities[index];
  }
  
  /**
   * 删除活动
   * @param {String} id 活动ID
   * @returns {Boolean} 是否删除成功
   */
  static deleteActivity(id) {
    const index = activities.findIndex(a => a.id === id);
    if (index === -1) return false;
    
    activities.splice(index, 1);
    return true;
  }
  
  /**
   * 取消活动
   * @param {String} id 活动ID
   * @returns {Object} 更新后的活动对象
   */
  static cancelActivity(id) {
    return this.updateActivity(id, { status: ActivityStatus.CANCELLED });
  }
  
  /**
   * 获取活动统计信息
   * @returns {Object} 统计信息
   */
  static getActivityStats() {
    return {
      total: activities.length,
      published: activities.filter(a => a.status === ActivityStatus.PUBLISHED).length,
      ongoing: activities.filter(a => a.status === ActivityStatus.ONGOING).length,
      ended: activities.filter(a => a.status === ActivityStatus.ENDED).length
    };
  }
  
  /**
   * 检查活动状态并更新
   * 用于定期检查活动是否开始或结束
   */
  static checkAndUpdateActivityStatus() {
    const now = new Date();
    
    activities.forEach(activity => {
      const startTime = new Date(activity.startTime);
      const endTime = new Date(activity.endTime);
      
      if (activity.status === ActivityStatus.PUBLISHED && now >= startTime && now < endTime) {
        activity.status = ActivityStatus.ONGOING;
        activity.updateTime = new Date().toISOString();
      } else if ((activity.status === ActivityStatus.PUBLISHED || activity.status === ActivityStatus.ONGOING) && now >= endTime) {
        activity.status = ActivityStatus.ENDED;
        activity.updateTime = new Date().toISOString();
      }
    });
  }
  
  /**
   * 模拟加载初始数据
   * 实际项目中应该从API获取
   */
  static loadInitialData() {
    // 这里可以从API加载数据
    // 目前使用模拟数据
    const mockActivities = [
      {
        id: 'flash_001',
        type: ActivityType.FLASH,
        name: 'iPhone 13 Pro Max限时秒杀',
        description: '苹果最新旗舰手机限时特惠',
        coverImage: 'https://via.placeholder.com/300x300',
        images: ['https://via.placeholder.com/300x300'],
        originalPrice: '8999',
        flashPrice: '7999',
        stockTotal: 200,
        soldCount: 156,
        startTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        endTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        status: ActivityStatus.ONGOING,
        merchantId: 'merchant_001',
        createTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        updateTime: new Date().toISOString(),
        publishTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        tag: '热门'
      },
      {
        id: 'group_001',
        type: ActivityType.GROUP,
        name: '九阳豆浆机拼团',
        description: '破壁免滤双预约多功能',
        coverImage: 'https://via.placeholder.com/300x300',
        images: ['https://via.placeholder.com/300x300'],
        originalPrice: '599',
        flashPrice: '299',
        stockTotal: 100,
        soldCount: 56,
        startTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        endTime: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
        status: ActivityStatus.ONGOING,
        merchantId: 'merchant_002',
        createTime: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        updateTime: new Date().toISOString(),
        publishTime: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        tag: '2人团'
      }
    ];
    
    activities = mockActivities;
  }
}

// 初始化时加载数据
ActivityService.loadInitialData();

// 导出
export {
  ActivityService,
  ActivityType,
  ActivityStatus
}; 