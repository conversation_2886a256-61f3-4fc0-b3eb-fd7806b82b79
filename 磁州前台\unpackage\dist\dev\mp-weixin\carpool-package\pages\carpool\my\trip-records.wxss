/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.trip-records-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(90rpx + var(--status-bar-height, 40px));
}

/* 自定义导航栏 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
}
.header-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
}
.left-action, .right-action {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-icon {
  width: 24px;
  height: 24px;
}
.back-icon {
  width: 24px;
  height: 24px;
  /* 图标是黑色的，需要转为白色 */
  filter: brightness(0) invert(1);
}
.title-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 切换标签 */
.tab-container {
  display: flex;
  background-color: #FFFFFF;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
}
.tab-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;
  position: relative;
}
.tab-item.active {
  background-color: rgba(10, 132, 255, 0.1);
}
.tab-item.active .tab-text {
  color: #0A84FF;
  font-weight: 600;
}
.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #0A84FF;
}
.tab-text {
  font-size: 28rpx;
  color: #666666;
}

/* 行程列表 */
.trip-list-scroll {
  height: calc(100vh - 90rpx - var(--status-bar-height, 40px) - 120rpx);
}
.trip-list {
  padding: 0 20rpx;
}
.trip-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.trip-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.trip-type {
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  background-color: #F2F2F7;
}
.trip-type.driver {
  background-color: rgba(10, 132, 255, 0.1);
}
.trip-type.passenger {
  background-color: rgba(52, 199, 89, 0.1);
}
.type-text {
  font-size: 24rpx;
  color: #666666;
}
.trip-type.driver .type-text {
  color: #0A84FF;
}
.trip-type.passenger .type-text {
  color: #34C759;
}
.trip-status {
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}
.trip-status.ongoing {
  background-color: rgba(255, 159, 10, 0.1);
}
.trip-status.completed {
  background-color: rgba(52, 199, 89, 0.1);
}
.trip-status.canceled {
  background-color: rgba(142, 142, 147, 0.1);
}
.status-text {
  font-size: 24rpx;
}
.trip-status.ongoing .status-text {
  color: #FF9F0A;
}
.trip-status.completed .status-text {
  color: #34C759;
}
.trip-status.canceled .status-text {
  color: #8E8E93;
}

/* 行程路线 */
.trip-route {
  background-color: #F9F9F9;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}
.route-point {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.route-point.end {
  margin-bottom: 0;
}
.point-icon {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}
.point-icon.start {
  background-color: #34C759;
}
.point-icon.end {
  background-color: #FF3B30;
}
.point-name {
  font-size: 28rpx;
  color: #333333;
}
.route-line {
  width: 2rpx;
  height: 30rpx;
  background-color: #DDDDDD;
  margin-left: 9rpx;
  margin-bottom: 16rpx;
}

/* 行程信息 */
.trip-info {
  margin-bottom: 20rpx;
}
.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}
.info-row:last-child {
  margin-bottom: 0;
}
.info-label {
  font-size: 26rpx;
  color: #666666;
}
.info-value {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
}
.info-value.price {
  color: #FF3B30;
}

/* 行程操作 */
.trip-actions {
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #F2F2F7;
  padding-top: 20rpx;
}
.action-btn {
  font-size: 26rpx;
  padding: 8rpx 24rpx;
  border-radius: 20rpx;
  margin-left: 16rpx;
  line-height: 1.5;
}
.action-btn.detail {
  background-color: rgba(142, 142, 147, 0.1);
  color: #666666;
}
.action-btn.contact {
  background-color: rgba(10, 132, 255, 0.1);
  color: #0A84FF;
}
.action-btn.rate {
  background-color: rgba(255, 159, 10, 0.1);
  color: #FF9F0A;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.empty-text {
  font-size: 28rpx;
  color: #8E8E93;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  padding: 30rpx 0;
}
.loading-text {
  font-size: 28rpx;
  color: #8E8E93;
}

/* 到底提示 */
.list-bottom {
  text-align: center;
  padding: 30rpx 0;
}
.bottom-text {
  font-size: 24rpx;
  color: #8E8E93;
}