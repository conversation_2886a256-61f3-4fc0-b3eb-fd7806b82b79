{"version": 3, "file": "points.js", "sources": ["subPackages/checkin/pages/points.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcY2hlY2tpblxwYWdlc1xwb2ludHMudnVl"], "sourcesContent": ["<template>\n\t<view class=\"points-container\">\n\t\t<!-- 状态栏占位 -->\n\t\t<view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\n\t\t\n\t\t<!-- 导航栏 -->\n\t\t<view class=\"navbar\" :style=\"{ top: statusBarHeight + 'px' }\">\n\t\t\t<view class=\"navbar-left\" @click=\"goBack\">\n\t\t\t\t<image class=\"back-icon\" src=\"/static/images/tabbar/最新返回键.png\" mode=\"aspectFit\"></image>\n\t\t\t\t</view>\n\t\t\t<text class=\"navbar-title\">每日签到</text>\n\t\t\t<view class=\"navbar-right\"></view>\n\t\t</view>\n\t\t\n\t\t<!-- 内容区域 -->\n\t\t<view class=\"content-area\" :style=\"{ paddingTop: contentMarginTop }\">\n\t\t\t<!-- 积分概览卡片 -->\n\t\t\t<view class=\"points-overview-card\">\n\t\t\t\t<view class=\"points-balance-section\">\n\t\t\t\t\t<view class=\"points-title\">我的积分</view>\n\t\t\t\t\t<view class=\"points-balance\">{{userPoints}}</view>\n\t\t\t\t\t<view class=\"points-balance-divider\"></view>\n\t\t\t\t\t<view class=\"points-actions\">\n\t\t\t\t\t\t<view class=\"action-btn\" @click=\"navigateTo('/subPackages/checkin/pages/points-detail')\">积分明细</view>\n\t\t\t\t\t\t<view class=\"action-btn primary\" @click=\"navigateTo('/subPackages/checkin/pages/points-mall')\">积分商城</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 签到模块 -->\n\t\t\t<view class=\"sign-in-module\">\n\t\t\t\t<view class=\"rule-float-btn\" @click=\"showPointsRules\">\n\t\t\t\t\t<image class=\"rules-icon\" src=\"/static/images/tabbar/问号.png\" mode=\"aspectFit\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"module-header\">\n\t\t\t\t\t<view class=\"module-title-container\">\n\t\t\t\t\t\t<text class=\"module-title\">每日签到</text>\n\t\t\t\t\t\t<text class=\"module-subtitle\">连续签到奖励更多积分</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"rank-btn\" @click=\"navigateTo('./points-rank')\">\n\t\t\t\t\t\t<text class=\"rank-btn-text\">积分排行榜</text>\n\t\t\t\t\t\t<image class=\"rank-btn-icon\" src=\"/static/images/tabbar/rank.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"calendar-container\">\n\t\t\t\t\t<view class=\"calendar-week-header\">\n\t\t\t\t\t\t<text class=\"week-day\" v-for=\"day in ['日','一','二','三','四','五','六']\" :key=\"day\">{{day}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"calendar-days\">\n\t\t\t\t\t\t<view class=\"day-item\" \n\t\t\t\t\t\t\tv-for=\"(day, index) in calendarDays\" \n\t\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t\t:class=\"{\n\t\t\t\t\t\t\t\t'signed': day.signed,\n\t\t\t\t\t\t\t\t'today': day.isToday,\n\t\t\t\t\t\t\t\t'future': day.isFuture,\n\t\t\t\t\t\t\t\t'empty': !day.day\n\t\t\t\t\t\t\t}\">\n\t\t\t\t\t\t\t<text class=\"day-number\">{{day.day}}</text>\n\t\t\t\t\t\t\t<text class=\"day-status\" v-if=\"day.signed\">+{{day.points}}</text>\n\t\t\t\t\t\t\t<text class=\"day-status sign-text\" v-else-if=\"day.isToday && !day.signed\">签到</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"sign-btn-container\">\n\t\t\t\t\t<button class=\"sign-in-btn\" \n\t\t\t\t\t\t:class=\"{ 'signed-today': isTodaySigned }\"\n\t\t\t\t\t\t:disabled=\"isTodaySigned\"\n\t\t\t\t\t\t@click=\"signIn\">\n\t\t\t\t\t\t{{ isTodaySigned ? '今日已签到' : '立即签到' }}\n\t\t\t\t\t</button>\n\t\t\t\t\t<view class=\"sign-in-tip\">已连续签到 {{continuousDays}} 天</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 每日任务模块 -->\n\t\t\t<view class=\"daily-tasks-module\">\n\t\t\t\t<view class=\"module-header\">\n\t\t\t\t\t<text class=\"module-title\">每日任务</text>\n\t\t\t\t\t<text class=\"module-subtitle\">完成任务可获取更多积分</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"task-list\">\n\t\t\t\t\t<view class=\"task-item\" v-for=\"(task, index) in dailyTasks\" :key=\"index\">\n\t\t\t\t\t\t<view class=\"task-info\">\n\t\t\t\t\t\t\t<view class=\"task-icon-wrap\" :style=\"{ backgroundColor: task.iconBg }\">\n\t\t\t\t\t\t\t\t<image :src=\"task.icon\" class=\"task-icon\"></image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"task-detail\">\n\t\t\t\t\t\t\t\t<view class=\"task-name\">{{task.name}}</view>\n\t\t\t\t\t\t\t\t<view class=\"task-desc\">{{task.description}}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"task-status\">\n\t\t\t\t\t\t\t<view class=\"task-points\">+{{task.points}}</view>\n\t\t\t\t\t\t\t<button class=\"task-btn\" \n\t\t\t\t\t\t\t\t:class=\"{ \n\t\t\t\t\t\t\t\t\t'task-completed': task.completed, \n\t\t\t\t\t\t\t\t\t'task-in-progress': task.progress < task.target && task.progress > 0 \n\t\t\t\t\t\t\t\t}\" \n\t\t\t\t\t\t\t\t@click=\"handleTask(index)\">\n\t\t\t\t\t\t\t\t<text v-if=\"task.completed\">已完成</text>\n\t\t\t\t\t\t\t\t<text v-else-if=\"task.progress > 0\">\n\t\t\t\t\t\t\t\t\t{{ task.progress }}/{{ task.target }}\n\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t<text v-else>去完成</text>\n\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t<view class=\"progress-bar\" v-if=\"task.progress < task.target && task.progress > 0\">\n\t\t\t\t\t\t\t\t<view class=\"progress-fill\" :style=\"{ width: (task.progress / task.target * 100) + '%' }\"></view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 全新的积分规则弹窗 -->\n\t\t<view class=\"popup-mask\" v-if=\"showRulesPopup\" @click=\"closePointsRules\"></view>\n\t\t<view class=\"new-rules-popup\" v-if=\"showRulesPopup\">\n\t\t\t<view class=\"new-rules-header\">\n\t\t\t\t<text class=\"new-rules-title\">积分规则</text>\n\t\t\t\t<view class=\"new-rules-close\" @click=\"closePointsRules\">×</view>\n\t\t\t</view>\n\t\t\t<view class=\"new-rules-content\">\n\t\t\t\t<view class=\"new-rule-item\">\n\t\t\t\t\t<view class=\"new-rule-title\">\n\t\t\t\t\t\t<view class=\"rule-badge\"></view>\n\t\t\t\t\t\t<text>签到规则</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"new-rule-desc\">每日签到可获得2积分。连续签到有额外奖励，连续7天可额外获得30积分。</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"new-rule-item\">\n\t\t\t\t\t<view class=\"new-rule-title\">\n\t\t\t\t\t\t<view class=\"rule-badge\"></view>\n\t\t\t\t\t\t<text>任务奖励</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"new-rule-desc\">完成每日任务可获得相应积分，每个任务每天只能获取一次奖励。</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"new-rule-item\">\n\t\t\t\t\t<view class=\"new-rule-title\">\n\t\t\t\t\t\t<view class=\"rule-badge\"></view>\n\t\t\t\t\t\t<text>积分有效期</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"new-rule-desc\">积分自获取之日起有效期为一年，请在有效期内使用完毕。</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"new-rule-item\">\n\t\t\t\t\t<view class=\"new-rule-title\">\n\t\t\t\t\t\t<view class=\"rule-badge\"></view>\n\t\t\t\t\t\t<text>积分使用</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"new-rule-desc\">积分可在积分商城兑换商品或优惠券，也可参与平台特定活动。</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"new-rule-item\">\n\t\t\t\t\t<view class=\"new-rule-title\">\n\t\t\t\t\t\t<view class=\"rule-badge\"></view>\n\t\t\t\t\t\t<text>其他说明</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"new-rule-desc\">使用虚假手段获取积分的行为一经发现，将取消相关积分并可能封禁账号。</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, onUnmounted, reactive } from 'vue';\nimport { onHide } from '@dcloudio/uni-app';\n\n// 响应式状态\nconst statusBarHeight = ref(20);\nconst userPoints = ref(1280);\nconst isTodaySigned = ref(false);\nconst continuousDays = ref(3);\nconst calendarDays = ref([]);\nconst showRulesPopup = ref(false);\n\n// 内部状态\nconst _data = reactive({\n  _pendingShareTask: null,\n  _pendingCommentTask: null,\n  _pendingProfileTask: null,\n  _categoryViewInterval: null\n});\n\n// 每日任务数据\nconst dailyTasks = ref([\n  {\n    name: '浏览同城团购',\n    description: '浏览社区团购页面，每次+2分',\n    icon: '/static/images/tabbar/阿团购.png',\n    iconBg: 'rgba(22, 119, 255, 0.08)',\n    points: 2,\n    progress: 1,\n    target: 5,\n    completed: false\n  },\n  {\n    name: '浏览商家',\n    description: '浏览商家详情页，每次+1分',\n    icon: '/static/images/tabbar/阿商家.png',\n    iconBg: 'rgba(22, 119, 255, 0.08)',\n    points: 1,\n    progress: 1,\n    target: 5,\n    completed: false\n  },\n  {\n    name: '浏览商家活动',\n    description: '浏览商家活动页面，每次+1分',\n    icon: '/static/images/tabbar/阿商家.png',\n    iconBg: 'rgba(22, 119, 255, 0.08)',\n    points: 1,\n    progress: 1,\n    target: 5,\n    completed: false\n  },\n  {\n    name: '浏览分类信息',\n    description: '浏览招聘、兼职、转让等分类信息',\n    icon: '/static/images/tabbar/阿分类.png',\n    iconBg: 'rgba(22, 119, 255, 0.08)',\n    points: 1,\n    progress: 1,\n    target: 5,\n    completed: false\n  },\n  {\n    name: '完成同城团购',\n    description: '参与并完成一次同城团购订单',\n    icon: '/static/images/tabbar/阿团购.png',\n    iconBg: 'rgba(22, 119, 255, 0.08)',\n    points: 20,\n    progress: 0,\n    target: 1,\n    completed: false\n  },\n  {\n    name: '发布信息',\n    description: '发布一条同城信息',\n    icon: '/static/images/tabbar/阿发布.png',\n    iconBg: 'rgba(22, 119, 255, 0.08)',\n    points: 2,\n    progress: 0,\n    target: 1,\n    completed: false\n  },\n  {\n    name: '分享小程序',\n    description: '分享小程序到群聊',\n    icon: '/static/images/tabbar/阿分享.png',\n    iconBg: 'rgba(22, 119, 255, 0.08)',\n    points: 5,\n    progress: 0,\n    target: 1,\n    completed: false\n  },\n  {\n    name: '参与本地活动',\n    description: '浏览活动页并报名参加',\n    icon: '/static/images/tabbar/阿活动.png',\n    iconBg: 'rgba(22, 119, 255, 0.08)',\n    points: 5,\n    progress: 0,\n    target: 1,\n    completed: false\n  },\n  {\n    name: '评论互动',\n    description: '发表评论',\n    icon: '/static/images/tabbar/阿评论.png',\n    iconBg: 'rgba(22, 119, 255, 0.08)',\n    points: 1,\n    progress: 1,\n    target: 1,\n    completed: false\n  },\n  {\n    name: '完善个人资料',\n    description: '补充完善个人信息',\n    icon: '/static/images/tabbar/阿资料.png',\n    iconBg: 'rgba(22, 119, 255, 0.08)',\n    points: 10,\n    progress: 0,\n    target: 1,\n    completed: false\n  }\n]);\n\n// 计算属性\nconst contentMarginTop = computed(() => {\n  return statusBarHeight.value + 88 + 'px';\n});\n\n// 生命周期钩子\nonMounted(() => {\n  // 设置系统标题栏标题\n  uni.setNavigationBarTitle({\n    title: '每日签到'\n  });\n  \n  // 获取状态栏高度\n  try {\n    const windowInfo = uni.getWindowInfo();\n    statusBarHeight.value = windowInfo.statusBarHeight || 20;\n  \n    // 设置CSS变量(小程序环境)\n    uni.setStorageSync('statusBarHeight', statusBarHeight.value);\n  } catch (e) {\n    console.error('获取系统信息失败:', e);\n    statusBarHeight.value = 20; // 设置默认值\n  }\n  \n  // 生成日历数据\n  generateCalendarDays();\n  \n  // 获取用户积分数据\n  getUserPointsData();\n});\n\n// 清理定时器\nonHide(() => {\n  // 清除分类浏览定时器\n  if (_data._categoryViewInterval) {\n    clearInterval(_data._categoryViewInterval);\n    _data._categoryViewInterval = null;\n  }\n});\n\nonUnmounted(() => {\n  // 清除分类浏览定时器\n  if (_data._categoryViewInterval) {\n    clearInterval(_data._categoryViewInterval);\n    _data._categoryViewInterval = null;\n  }\n});\n\n// 方法\n// 生成日历数据\nconst generateCalendarDays = () => {\n  const now = new Date();\n  const currentDay = now.getDate();\n  const currentMonth = now.getMonth();\n  const currentYear = now.getFullYear();\n  \n  // 获取当月第一天是星期几（0为周日，1-6为周一至周六）\n  const firstDayOfWeek = new Date(currentYear, currentMonth, 1).getDay();\n  \n  // 获取当月总天数\n  const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();\n  \n  const days = [];\n  \n  // 填充当月第一天之前的空白格子\n  for (let i = 0; i < firstDayOfWeek; i++) {\n    days.push({\n      day: '',\n      signed: false,\n      isToday: false,\n      isFuture: false,\n      points: 0,\n      empty: true\n    });\n  }\n  \n  // 填充日期\n  for (let i = 1; i <= daysInMonth; i++) {\n    days.push({\n      day: i,\n      signed: i < currentDay,  // 假设当天之前的都已签到\n      isToday: i === currentDay,\n      isFuture: i > currentDay,\n      points: getSignPoints(i),\n      empty: false\n    });\n  }\n  \n  // 填充当月最后一天之后的空白格子，确保日历行数完整\n  const totalDays = firstDayOfWeek + daysInMonth;\n  const remainingCells = totalDays % 7 === 0 ? 0 : 7 - (totalDays % 7);\n  \n  for (let i = 0; i < remainingCells; i++) {\n    days.push({\n      day: '',\n      signed: false,\n      isToday: false, \n      isFuture: false,\n      points: 0,\n      empty: true\n    });\n  }\n  \n  calendarDays.value = days;\n  \n  // 检查今日是否已签到\n  const todayObj = calendarDays.value.find(day => day.isToday);\n  if (todayObj) {\n    isTodaySigned.value = todayObj.signed;\n  }\n};\n\n// 获取签到积分规则\nconst getSignPoints = (day) => {\n  // 签到积分统一为2分\n  return 2;\n};\n\n// 获取用户积分数据\nconst getUserPointsData = () => {\n  // 这里应该调用后端API获取用户积分数据\n  // 示例仅使用模拟数据\n  setTimeout(() => {\n    // 模拟API返回数据\n  }, 500);\n};\n\n// 签到操作\nconst signIn = () => {\n  if (isTodaySigned.value) return;\n  \n  // 这里应该调用后端API进行签到\n  // 示例仅使用模拟数据\n  uni.showLoading({ title: '签到中...' });\n  \n  setTimeout(() => {\n    // 更新今日签到状态\n    const todayIndex = calendarDays.value.findIndex(day => day.isToday);\n    if (todayIndex !== -1) {\n      calendarDays.value[todayIndex].signed = true;\n      userPoints.value += calendarDays.value[todayIndex].points;\n      isTodaySigned.value = true;\n      continuousDays.value += 1;\n    }\n    \n    uni.hideLoading();\n    uni.showToast({ \n      title: '签到成功 +' + calendarDays.value[todayIndex].points + '积分', \n      icon: 'none' \n    });\n  }, 800);\n};\n\n// 处理任务\nconst handleTask = (index) => {\n  const task = dailyTasks.value[index];\n  \n  if (task.completed) {\n    uni.showToast({ \n      title: '任务已完成', \n      icon: 'none' \n    });\n    return;\n  }\n  \n  // 根据任务类型跳转到对应页面\n  switch (task.name) {\n    case '浏览商家':\n      // 首先尝试作为主Tab页跳转\n      uni.switchTab({\n        url: '/pages/business/business',\n        success: () => {\n          console.log('成功跳转到商家页面');\n          // 如果已经达到目标次数，不再增加进度，但仍然给予积分奖励\n          if (task.progress >= task.target) {\n            // 已完成所有目标次数，但仍然奖励积分\n            userPoints.value += task.points;\n            uni.showToast({\n              title: '已浏览商家 +1积分',\n              icon: 'none',\n              duration: 2000\n            });\n          } else {\n            // 跳转成功才增加进度\n            updateTaskProgress(index, 1);\n            uni.showToast({\n              title: '已浏览商家 +1积分',\n              icon: 'none',\n              duration: 2000\n            });\n          }\n        },\n        fail: () => {\n          // 如果不是Tab页，尝试普通页面跳转\n          uni.navigateTo({\n            url: '/pages/business/business',\n            success: () => {\n              console.log('成功跳转到商家页面(navigateTo)');\n              // 如果已经达到目标次数，不再增加进度，但仍然给予积分奖励\n              if (task.progress >= task.target) {\n                // 已完成所有目标次数，但仍然奖励积分\n                userPoints.value += task.points;\n                uni.showToast({\n                  title: '已浏览商家 +1积分',\n                  icon: 'none',\n                  duration: 2000\n                });\n              } else {\n                // 跳转成功才增加进度\n                updateTaskProgress(index, 1);\n                uni.showToast({\n                  title: '已浏览商家 +1积分',\n                  icon: 'none',\n                  duration: 2000\n                });\n              }\n            },\n            fail: (err) => {\n              console.error('跳转失败:', err);\n              uni.showToast({\n                title: '页面跳转失败',\n                icon: 'none'\n              });\n            }\n          });\n        }\n      });\n      break;\n    case '浏览商家活动':\n      // 检查页面是否存在\n      uni.navigateTo({\n        url: '/pages/business/activity',\n        success: () => {\n          console.log('成功跳转到商家活动页面');\n          // 如果已经达到目标次数，不再增加进度，但仍然给予积分奖励\n          if (task.progress >= task.target) {\n            // 已完成所有目标次数，但仍然奖励积分\n            userPoints.value += task.points;\n            uni.showToast({\n              title: '已浏览商家活动 +1积分',\n              icon: 'none',\n              duration: 2000\n            });\n          } else {\n            // 正常增加进度并奖励积分\n            updateTaskProgress(index, 1);\n            uni.showToast({\n              title: '已浏览商家活动 +1积分',\n              icon: 'none',\n              duration: 2000\n            });\n          }\n        },\n        fail: (err) => {\n          console.error('商家活动页面跳转失败:', err);\n          // 尝试跳转到商家页面作为替代\n          uni.switchTab({\n            url: '/pages/business/business',\n            success: () => {\n              console.log('跳转到商家页面替代');\n              // 提示用户使用替代页面\n              uni.showToast({\n                title: '商家活动页面开发中，请浏览商家页面完成任务',\n                icon: 'none',\n                duration: 2000\n              });\n              // 如果已经达到目标次数，不再增加进度，但仍然给予积分奖励\n              if (task.progress >= task.target) {\n                // 已完成所有目标次数，但仍然奖励积分\n                userPoints.value += task.points;\n              } else {\n                // 浏览替代页面也增加进度\n                updateTaskProgress(index, 1);\n              }\n            },\n            fail: () => {\n              uni.showToast({\n                title: '页面跳转失败',\n                icon: 'none'\n              });\n            }\n          });\n        }\n      });\n      break;\n    case '浏览分类信息':\n      uni.switchTab({\n        url: '/pages/index/index',\n        success: () => {\n          updateTaskProgress(index);\n          \n          // 延迟一小段时间，确保首页已加载完成\n          setTimeout(() => {\n            // 获取首页实例并调用其scrollToInfoSection方法\n            const pages = getCurrentPages();\n            const indexPage = pages[pages.length - 1];\n            if (indexPage && indexPage.$vm && typeof indexPage.$vm.scrollToInfoSection === 'function') {\n              indexPage.$vm.scrollToInfoSection();\n            }\n          }, 500);\n        },\n        fail: (err) => {\n          console.error('首页跳转失败:', err);\n        }\n      });\n      break;\n    case '发布信息':\n      uni.switchTab({\n        url: '/pages/publish/publish',\n        success: () => {\n          console.log('成功跳转到发布页面');\n          updateTaskProgress(index);\n        },\n        fail: () => {\n          uni.navigateTo({\n            url: '/pages/publish/publish',\n            success: () => {\n              console.log('成功跳转到发布页面(navigateTo)');\n              updateTaskProgress(index);\n            },\n            fail: (err) => {\n              console.error('跳转失败:', err);\n              uni.showToast({\n                title: '页面跳转失败',\n                icon: 'none'\n              });\n            }\n          });\n        }\n      });\n      break;\n    case '分享小程序':\n      // 调用分享API\n      uni.showShareMenu({\n        withShareTicket: true, // 获取分享票据，用于验证是否分享到群\n        menus: ['shareAppMessage', 'shareTimeline'], // 显示分享到好友和朋友圈菜单\n        success: () => {\n          uni.showToast({ \n            title: '请点击右上角分享到群聊', \n            icon: 'none',\n            duration: 3000\n          });\n          \n          // 分享需要用户操作，设置一个标记，在onShareAppMessage中增加进度\n          _data._pendingShareTask = index;\n          \n          // 10秒后重置，避免长时间挂起\n          setTimeout(() => {\n            if (_data._pendingShareTask === index) {\n              _data._pendingShareTask = null;\n              uni.showToast({\n                title: '分享超时，请重试',\n                icon: 'none'\n              });\n            }\n          }, 10000);\n        },\n        fail: (err) => {\n          console.error('分享菜单显示失败:', err);\n          uni.showToast({\n            title: '分享功能不可用',\n            icon: 'none' \n          });\n        }\n      });\n      break;\n    case '浏览同城团购':\n      // 检查页面是否存在\n      uni.navigateTo({\n        url: '/pages/community/shopping',\n        success: () => {\n          console.log('成功跳转到社区团购页面');\n          // 如果已经达到目标次数，不再增加进度，但仍然给予积分奖励\n          if (task.progress >= task.target) {\n            // 已完成所有目标次数，但仍然奖励积分\n            userPoints.value += task.points;\n            uni.showToast({\n              title: '已浏览同城团购 +2积分',\n              icon: 'none',\n              duration: 2000\n            });\n          } else {\n            // 正常增加进度并奖励积分\n            updateTaskProgress(index, 1);\n            uni.showToast({\n              title: '已浏览同城团购 +2积分',\n              icon: 'none',\n              duration: 2000\n            });\n          }\n        },\n        fail: (err) => {\n          console.error('社区团购页面跳转失败:', err);\n          // 尝试跳转到商家页面作为替代\n          uni.switchTab({\n            url: '/pages/business/business',\n            success: () => {\n              console.log('跳转到商家页面替代');\n              // 提示用户使用替代页面\n              uni.showToast({\n                title: '同城团购正在开发中，请浏览商家页面完成任务',\n                icon: 'none',\n                duration: 2000\n              });\n              // 如果已经达到目标次数，不再增加进度，但仍然给予积分奖励\n              if (task.progress >= task.target) {\n                // 已完成所有目标次数，但仍然奖励积分\n                userPoints.value += task.points;\n              } else {\n                // 浏览替代页面也增加进度\n                updateTaskProgress(index, 1);\n              }\n            },\n            fail: () => {\n              uni.showToast({\n                title: '页面跳转失败',\n                icon: 'none'\n              });\n            }\n          });\n        }\n      });\n      break;\n    case '完成同城团购':\n      // 跳转到团购页面\n      uni.navigateTo({\n        url: '/pages/community/shopping',\n        success: () => {\n          console.log('成功跳转到社区团购页面');\n          uni.showToast({\n            title: '请完成一笔团购订单获取积分',\n            icon: 'none',\n            duration: 3000\n          });\n        },\n        fail: (err) => {\n          console.error('社区团购页面跳转失败:', err);\n          // 尝试跳转到商家页面作为替代\n          uni.switchTab({\n            url: '/pages/business/business',\n            success: () => {\n              console.log('跳转到商家页面替代');\n              uni.showToast({\n                title: '同城团购功能开发中，请稍后再试',\n                icon: 'none',\n                duration: 2000\n              });\n            },\n            fail: () => {\n              uni.showToast({\n                title: '页面跳转失败',\n                icon: 'none'\n              });\n            }\n          });\n        }\n      });\n      break;\n\t  case '参与本地活动':\n      uni.navigateTo({\n        url: '/pages/activity/list',\n        success: () => {\n          console.log('成功跳转到活动列表页');\n          updateTaskProgress(index);\n        },\n        fail: (err) => {\n          console.error('活动列表跳转失败:', err);\n          uni.showToast({\n            title: '活动页面跳转失败',\n            icon: 'none'\n          });\n        }\n      });\n      break;\n    case '评论互动':\n      uni.switchTab({\n        url: '/pages/index/index',\n        success: () => {\n          console.log('成功跳转到首页');\n          setTimeout(() => {\n            uni.showToast({\n              title: '请在内容下方评论',\n              icon: 'none',\n              duration: 2000\n            });\n          }, 500);\n          \n          // 设置评论任务标记，用户需要在评论组件中实际发表评论才能获得进度\n          // 这里仅做演示，实际应该在评论成功的回调中增加进度\n          _data._pendingCommentTask = index;\n          \n          // 模拟用户10秒后发表评论\n          setTimeout(() => {\n            if (_data._pendingCommentTask === index) {\n              updateTaskProgress(index);\n              _data._pendingCommentTask = null;\n            }\n          }, 10000);\n        },\n        fail: (err) => {\n          console.error('首页跳转失败:', err);\n          uni.showToast({\n            title: '页面跳转失败',\n            icon: 'none'\n          });\n        }\n      });\n      break;\n    case '完善个人资料':\n      // 尝试使用相对路径\n      uni.navigateTo({\n        url: '/subPackages/checkin/pages/profile',\n        success: () => {\n          console.log('成功跳转到个人资料页');\n          // 这里不立即增加进度，应该在用户实际完善资料后增加\n          // 设置一个标记，在profile页面保存资料后回调增加进度\n          _data._pendingProfileTask = index;\n          \n          // 模拟用户5秒后完善资料\n          setTimeout(() => {\n            if (_data._pendingProfileTask === index) {\n              updateTaskProgress(index);\n              _data._pendingProfileTask = null;\n            }\n          }, 5000);\n        },\n        fail: (err) => {\n          console.error('个人资料页跳转失败(相对路径):', err);\n          // 尝试使用绝对路径\n          uni.navigateTo({\n            url: '/pages/new-points/profile',\n            success: () => {\n              console.log('成功跳转到个人资料页(绝对路径)');\n              // 同上，不立即增加进度\n              _data._pendingProfileTask = index;\n              \n              // 模拟用户5秒后完善资料\n              setTimeout(() => {\n                if (_data._pendingProfileTask === index) {\n                  updateTaskProgress(index);\n                  _data._pendingProfileTask = null;\n                }\n              }, 5000);\n            },\n            fail: (e) => {\n              console.error('个人资料页跳转失败(绝对路径):', e);\n              uni.showToast({\n                title: '个人资料页面不存在',\n                icon: 'none'\n              });\n            }\n          });\n        }\n      });\n      break;\n    default:\n      console.log('未知任务类型:', task.name);\n      uni.showToast({ \n        title: '任务类型未定义', \n        icon: 'none' \n      });\n      break;\n  }\n};\n\n// 更新任务进度的方法，从handleTask中提取出来\nconst updateTaskProgress = (index, incrementValue = 1) => {\n  const task = dailyTasks.value[index];\n  if (task.progress < task.target) {\n    const newProgress = Math.min(task.progress + incrementValue, task.target);\n    dailyTasks.value[index].progress = newProgress;\n    // 判断任务是否完成\n    if (newProgress >= task.target) {\n      dailyTasks.value[index].completed = true;\n      userPoints.value += task.points;\n    }\n  }\n};\n\n// 页面导航\nconst navigateTo = (url, successCallback) => {\n  if (url.startsWith('/pages/new-points/')) {\n    const relativePath = url.replace('/pages/new-points/', './');\n    uni.navigateTo({\n      url: relativePath,\n      success: () => {\n        if (successCallback && typeof successCallback === 'function') {\n          successCallback();\n        }\n      },\n      fail: (err) => {\n        console.error('导航失败:', err);\n        // 失败后尝试使用绝对路径\n        uni.navigateTo({\n          url: url,\n          success: successCallback,\n          fail: (e) => {\n            console.error('绝对路径导航也失败:', e);\n            // 如果有回调函数，仍然执行它\n            if (successCallback && typeof successCallback === 'function') {\n              successCallback();\n            }\n            uni.showToast({\n              title: '已增加任务进度',\n              icon: 'none'\n            });\n          }\n        });\n      }\n    });\n  } else if (url.startsWith('/subPackages/checkin/pages/')) {\n    const relativePath = url.replace('/subPackages/checkin/pages/', './');\n    uni.navigateTo({\n      url: relativePath,\n      success: () => {\n        if (successCallback && typeof successCallback === 'function') {\n          successCallback();\n        }\n      },\n      fail: (err) => {\n        console.error('导航失败:', err);\n        // 失败后尝试使用绝对路径\n        uni.navigateTo({\n          url: url,\n          success: successCallback,\n          fail: (e) => {\n            console.error('绝对路径导航也失败:', e);\n            // 如果有回调函数，仍然执行它\n            if (successCallback && typeof successCallback === 'function') {\n              successCallback();\n            }\n            uni.showToast({\n              title: '已增加任务进度',\n              icon: 'none'\n            });\n          }\n        });\n      }\n    });\n  } else {\n    // 其他页面使用绝对路径\n    uni.navigateTo({\n      url: url,\n      success: successCallback,\n      fail: (err) => {\n        console.error('导航失败:', err);\n        // 如果有回调函数，仍然执行它\n        if (successCallback && typeof successCallback === 'function') {\n          successCallback();\n        }\n        uni.showToast({\n          title: '已增加任务进度',\n          icon: 'none'\n        });\n      }\n    });\n  }\n};\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n\n// 显示积分规则\nconst showPointsRules = () => {\n  showRulesPopup.value = true;\n};\n\n// 关闭积分规则\nconst closePointsRules = () => {\n  showRulesPopup.value = false;\n};\n\n// 分享回调方法\ndefineExpose({\n  onShareAppMessage(res) {\n    // 设置分享内容\n    const shareData = {\n      title: '同城小程序 - 每日签到赚积分',\n      path: '/subPackages/checkin/pages/points',\n      imageUrl: '/static/images/share-cover.png',\n      success: (res) => {\n        // 检查是否有shareTickets，判断是否分享到群\n        if (res.shareTickets && res.shareTickets.length > 0) {\n          console.log('成功分享到群聊');\n          // 如果有待处理的分享任务，完成它\n          if (_data._pendingShareTask !== null && _data._pendingShareTask !== undefined) {\n            const taskIndex = _data._pendingShareTask;\n            _data._pendingShareTask = null;\n            \n            // 更新任务进度\n            updateTaskProgress(taskIndex);\n            \n            uni.showToast({\n              title: '群分享成功，获得积分！',\n              icon: 'success'\n            });\n          }\n        } else {\n          console.log('分享成功，但不是分享到群');\n          uni.showToast({\n            title: '请分享到群聊才能获得积分',\n            icon: 'none'\n          });\n        }\n      },\n      fail: (err) => {\n        console.error('分享失败:', err);\n      }\n    };\n    \n    return shareData;\n  }\n});\n</script>\n\n<style lang=\"scss\" scoped>\npage {\n\tbackground-color: #F0F2F5;\n\tfont-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;\n}\n\n.points-container {\n\tmin-height: 100vh;\n\tbackground: #f8f8f8;\n\tposition: relative;\n\tpadding-bottom: 24px;\n}\n\n/* 导航栏样式 */\n.status-bar {\n\tbackground: #1677FF;\n\twidth: 100%;\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tz-index: 100;\n}\n\n.navbar {\n\tposition: fixed;\n\tleft: 0;\n\tright: 0;\n\theight: 44px;\n\tbackground: #1677FF;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 0 16px;\n\tbox-shadow: 0 2px 8px rgba(22, 119, 255, 0.04);\n\tborder-bottom: 1px solid #f1f1f1;\n\tz-index: 100;\n}\n\n.navbar-left, .navbar-right {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.back-icon {\n\twidth: 24px;\n\theight: 24px;\n\tdisplay: block;\n\tbackground: none;\n\tborder-radius: 0;\n\tmargin: 0 auto;\n}\n\n.navbar-title {\n\tfont-size: 18px;\n\tfont-weight: 700;\n\tcolor: #fff;\n\tletter-spacing: 0.5px;\n}\n\n/* 内容区域 */\n.content-area {\n\tpadding-top: 88px;\n\tpadding-bottom: 30rpx;\n}\n\n/* 积分概览卡片 */\n.points-overview-card {\n\tbackground: #FFFFFF;\n\tborder-radius: 16rpx;\n\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);\n\tmargin: 20rpx 24rpx 30rpx;\n\tpadding: 24rpx;\n\tposition: relative;\n\toverflow: hidden;\n\t\n\t&::before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 6rpx;\n\t\tbackground: linear-gradient(90deg, #1677FF, #06B6D4);\n\t}\n}\n\n.points-balance-section {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tpadding: 10rpx 0;\n}\n\n.points-title {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tmargin-bottom: 8rpx;\n}\n\n.points-balance {\n\tfont-size: 60rpx;\n\tfont-weight: bold;\n\tcolor: #1677FF;\n\tmargin-bottom: 16rpx;\n\tfont-family: 'DIN Condensed', Arial, sans-serif;\n\tbackground: linear-gradient(90deg, #1677FF, #06B6D4);\n\t-webkit-background-clip: text;\n\tbackground-clip: text;\n\t-webkit-text-fill-color: transparent;\n\ttext-fill-color: transparent;\n\tletter-spacing: 1rpx;\n}\n\n.points-balance-divider {\n\twidth: 30%;\n\theight: 2rpx;\n\tbackground: #f0f0f0;\n\tmargin-bottom: 16rpx;\n}\n\n.points-actions {\n\tdisplay: flex;\n\twidth: 100%;\n\tjustify-content: center;\n\tgap: 30rpx;\n}\n\n.action-btn {\n\tpadding: 10rpx 24rpx;\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tbackground: #F5F5F5;\n\tborder-radius: 30rpx;\n\t\n\t&.primary {\n\tcolor: #fff;\n\t\tbackground: #1677FF;\n}\n}\n\n/* 签到模块 */\n.sign-in-module {\n\tbackground: #FFFFFF;\n\tborder-radius: 16rpx;\n\tmargin: 0 24rpx 30rpx;\n\tpadding: 30rpx 24rpx;\n\tposition: relative;\n\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);\n}\n\n.rule-float-btn {\n\tposition: absolute;\n\ttop: 18px;\n\tright: 18px;\n\twidth: 32px;\n\theight: 32px;\n\tbackground: linear-gradient(135deg, #fafdff 60%, #eaf3ff 100%);\n\tborder-radius: 50%;\n\tbox-shadow: 0 2px 8px rgba(22,119,255,0.08);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tz-index: 2;\n\tcursor: pointer;\n\ttransition: box-shadow 0.2s;\n}\n.rule-float-btn:active {\n\tbox-shadow: 0 4px 16px rgba(22,119,255,0.12);\n}\n.rules-icon {\n\twidth: 18px;\n\theight: 18px;\n}\n.module-header {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tmargin-bottom: 16px;\n\tpadding: 0 12px;\n}\n.module-title-container {\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.module-title {\n\tfont-size: 16px;\n\tfont-weight: 600;\n\tcolor: #333;\n}\n.module-subtitle {\n\tcolor: #999;\n\tfont-size: 12px;\n\tmargin-top: 2px;\n}\n\n.calendar-container {\n\tbackground: #f8f8f8;\n\tborder-radius: 12px;\n\tmargin: 12px 12px 0 12px;\n\tpadding: 10px 0 6px 0;\n}\n.calendar-week-header {\n\tdisplay: flex;\n\tjustify-content: space-around;\n\tmargin-bottom: 10px;\n}\n.week-day {\n\tfont-size: 13px;\n\tcolor: #999;\n\twidth: 38px;\n\ttext-align: center;\n}\n.calendar-days {\n\tdisplay: grid;\n\tgrid-template-columns: repeat(7, 1fr);\n\tgap: 5px;\n}\n.day-item {\n\tbackground: #fff;\n\tborder-radius: 8px;\n\tmargin: 2px 0;\n\tcolor: #333;\n\tfont-size: 15px;\n\tbox-shadow: 0 1px 3px rgba(22, 119, 255, 0.04);\n\ttransition: background 0.2s;\n\twidth: 38px;\n\theight: 38px;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tposition: relative;\n\tbox-sizing: border-box;\n}\n.day-item.signed {\n\tbackground: #e6f0ff;\n\tcolor: #1677FF;\n}\n.day-item.today {\n\tborder: 2px solid #1677FF;\n}\n.day-number {\n\tfont-size: 13px;\n\tcolor: #333;\n\tmargin-bottom: 2px;\n\tline-height: 1;\n}\n.day-status,\n.day-status.sign-text {\n\tmargin: 0;\n}\n.day-status.sign-text {\n\tbackground: none;\n\tborder-radius: 0;\n\tpadding: 0;\n\tfont-size: 0;\n\tmin-width: 0;\n\theight: auto;\n\tbox-shadow: none;\n\tborder: none;\n}\n\n.sign-btn-container {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tmargin-top: 16px;\n}\n.sign-in-btn {\n\tbackground: linear-gradient(90deg, #1677FF 0%, #007aff 100%);\n\tcolor: #fff;\n\tborder-radius: 18px;\n\tfont-size: 16px;\n\tfont-weight: 600;\n\tpadding: 8px 0;\n\tmargin: 10px 0 0 0;\n\twidth: 80%;\n\tbox-shadow: 0 2px 8px rgba(22, 119, 255, 0.08);\n\tborder: none;\n}\n.sign-in-btn.signed-today {\n\tbackground: #f1f1f1;\n\tcolor: #999;\n}\n.sign-in-btn:active {\n\ttransform: scale(0.98);\n\topacity: 0.9;\n}\n.sign-in-tip {\n\tcolor: #999;\n\tfont-size: 13px;\n\tmargin-top: 6px;\n}\n\n/* 排行榜按钮新样式 */\n.rank-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tbackground: transparent;\n\tborder-radius: 16px;\n\tpadding: 6px 12px;\n\tgap: 4px;\n}\n.rank-btn-text {\n\tcolor: #1677FF;\n\tfont-size: 13px;\n\tfont-weight: 500;\n}\n.rank-btn-icon {\n\twidth: 16px;\n\theight: 16px;\n}\n\n/* 每日任务模块 */\n.daily-tasks-module {\n\tbackground: #FFFFFF;\n\tborder-radius: 16rpx;\n\tmargin: 0 24rpx 30rpx;\n\tpadding: 30rpx 24rpx;\n\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);\n}\n.task-list {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 8px;\n}\n.task-item {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 10px 12px;\n\tbackground: #fff;\n\tborder-radius: 12px;\n\tbox-shadow: 0 1px 3px rgba(22, 119, 255, 0.04);\n\tborder: 1px solid #f1f1f1;\n\ttransition: all 0.3s;\n}\n.task-item:active {\n\ttransform: scale(0.98);\n}\n.task-info {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 10px;\n}\n.task-icon-wrap {\n\twidth: 34px;\n\theight: 34px;\n\tborder-radius: 10px;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbackground: linear-gradient(135deg, #e6f0ff 0%, #f8f8f8 100%);\n\tbox-shadow: 0 2px 6px rgba(22, 119, 255, 0.04);\n}\n.task-icon {\n\twidth: 20px;\n\theight: 20px;\n}\n.task-detail {\n\tdisplay: flex;\n\tflex-direction: column;\n}\n.task-name {\n\tfont-size: 14px;\n\tfont-weight: 600;\n\tcolor: #333;\n\tmargin-bottom: 2px;\n}\n.task-desc {\n\tfont-size: 12px;\n\tcolor: #999;\n}\n.task-status {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: flex-end;\n\tgap: 6px;\n}\n.task-points {\n\tfont-size: 14px;\n\tfont-weight: 700;\n\tcolor: #1677FF;\n}\n.task-btn {\n\tmin-width: 72px;\n\theight: 28px;\n\tbackground: linear-gradient(90deg, #1677FF 0%, #007aff 100%);\n\tcolor: #fff;\n\tfont-size: 12px;\n\tborder-radius: 14px;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: 0 2px 6px rgba(22, 119, 255, 0.08);\n\tfont-weight: 600;\n\tborder: none;\n}\n.task-btn.task-completed {\n\tbackground: #f1f1f1;\n\tcolor: #999;\n\tbox-shadow: none;\n}\n.task-btn.task-in-progress {\n\tbackground: linear-gradient(90deg, #ffb300 0%, #ffec80 100%);\n\tcolor: #fff;\n}\n.progress-bar {\n\twidth: 72px;\n\theight: 4px;\n\tbackground: #f1f1f1;\n\tborder-radius: 2px;\n\toverflow: hidden;\n}\n.progress-fill {\n\theight: 100%;\n\tbackground: linear-gradient(90deg, #1677FF 0%, #63b3ed 100%);\n\tborder-radius: 2px;\n\ttransition: width 0.3s;\n}\n\n/* 任务按钮样式 */\n.task-btn.task-in-progress + .progress-bar {\n\tdisplay: block;\n}\n\n/* 全新的积分规则弹窗样式 */\n.popup-mask {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: rgba(0, 0, 0, 0.25);\n\tbackdrop-filter: blur(2px);\n\tz-index: 1000;\n}\n\n.new-rules-popup {\n\tposition: fixed;\n\tleft: 50%;\n\ttop: 50%;\n\ttransform: translate(-50%, -50%);\n\twidth: 85%;\n\tmax-width: 580rpx;\n\tbackground: #fff;\n\tborder-radius: 18px;\n\tbox-shadow: 0 6px 24px rgba(22, 119, 255, 0.08);\n\tz-index: 1001;\n\tanimation: popupIn 0.3s;\n\toverflow: hidden;\n}\n.new-rules-popup::before {\n\tcontent: '';\n\tdisplay: block;\n\twidth: 100%;\n\theight: 5px;\n\tbackground: linear-gradient(90deg, #1677FF 0%, #007aff 100%);\n}\n.new-rules-header {\n\tpadding: 18px 18px 14px;\n\tborder-bottom: 1px solid #f1f1f1;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n.new-rules-title {\n\tfont-size: 17px;\n\tfont-weight: 700;\n\tcolor: #333;\n}\n.new-rules-close {\n\twidth: 30px;\n\theight: 30px;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 22px;\n\tcolor: #999;\n\tborder-radius: 50%;\n\tbackground: #f8f8f8;\n}\n.new-rules-content {\n\tpadding: 18px;\n\tmax-height: 60vh;\n\toverflow-y: auto;\n}\n.new-rule-item {\n\tmargin-bottom: 16px;\n}\n.new-rule-item:last-child {\n\tmargin-bottom: 0;\n}\n.new-rule-title {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8px;\n\tmargin-bottom: 6px;\n}\n.rule-badge {\n\twidth: 6px;\n\theight: 6px;\n\tbackground: #1677FF;\n\tborder-radius: 50%;\n}\n.new-rule-title text {\n\tfont-size: 14px;\n\tfont-weight: 600;\n\tcolor: #333;\n}\n.new-rule-desc {\n\tfont-size: 13px;\n\tcolor: #999;\n\tline-height: 1.6;\n\tpadding-left: 14px;\n}\n/* 添加总积分显示样式 */\n.task-item:first-child .task-points {\n\tposition: relative;\n\tdisplay: flex;\n\talign-items: center;\n}\n.task-item:first-child .task-points::after {\n\tcontent: '(共+10)';\n\tfont-size: 10px;\n\tcolor: #1677FF;\n\tfont-weight: normal;\n\tposition: static;\n\tmargin-left: 4px;\n\tbackground-color: rgba(22, 119, 255, 0.08);\n\tpadding: 1px 4px;\n\tborder-radius: 8px;\n}\n.task-item:nth-child(2) .task-points {\n\tposition: relative;\n\tdisplay: flex;\n\talign-items: center;\n}\n.task-item:nth-child(2) .task-points::after {\n\tcontent: '(共+5)';\n\tfont-size: 10px;\n\tcolor: #1677FF;\n\tfont-weight: normal;\n\tposition: static;\n\tmargin-left: 4px;\n\tbackground-color: rgba(22, 119, 255, 0.08);\n\tpadding: 1px 4px;\n\tborder-radius: 8px;\n}\n.task-item:nth-child(3) .task-points {\n\tposition: relative;\n\tdisplay: flex;\n\talign-items: center;\n}\n.task-item:nth-child(3) .task-points::after {\n\tcontent: '(共+5)';\n\tfont-size: 10px;\n\tcolor: #1677FF;\n\tfont-weight: normal;\n\tposition: static;\n\tmargin-left: 4px;\n\tbackground-color: rgba(22, 119, 255, 0.08);\n\tpadding: 1px 4px;\n\tborder-radius: 8px;\n}\n.task-item:nth-child(4) .task-points {\n\tposition: relative;\n\tdisplay: flex;\n\talign-items: center;\n}\n.task-item:nth-child(4) .task-points::after {\n\tcontent: '(共+5)';\n\tfont-size: 10px;\n\tcolor: #1677FF;\n\tfont-weight: normal;\n\tposition: static;\n\tmargin-left: 4px;\n\tbackground-color: rgba(22, 119, 255, 0.08);\n\tpadding: 1px 4px;\n\tborder-radius: 8px;\n}\n.task-item:nth-child(5) .task-points {\n\tposition: relative;\n\tdisplay: flex;\n\talign-items: center;\n}\n.task-item:nth-child(5) .task-points::after {\n\tcontent: '';\n\tfont-size: 10px;\n\tcolor: #1677FF;\n\tfont-weight: normal;\n\tposition: static;\n\tmargin-left: 0;\n\tbackground-color: transparent;\n\tpadding: 0;\n\tborder-radius: 8px;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/checkin/pages/points.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "computed", "onMounted", "uni", "onHide", "onUnmounted", "res"], "mappings": ";;;;;;AAgLA,UAAA,kBAAAA,cAAAA,IAAA,EAAA;AACA,UAAA,aAAAA,cAAAA,IAAA,IAAA;AACA,UAAA,gBAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,iBAAAA,cAAAA,IAAA,CAAA;AACA,UAAA,eAAAA,cAAAA,IAAA,CAAA,CAAA;AACA,UAAA,iBAAAA,cAAAA,IAAA,KAAA;AAGA,UAAA,QAAAC,cAAAA,SAAA;AAAA,MACA,mBAAA;AAAA,MACA,qBAAA;AAAA,MACA,qBAAA;AAAA,MACA,uBAAA;AAAA,IACA,CAAA;AAGA,UAAA,aAAAD,cAAAA,IAAA;AAAA,MACA;AAAA,QACA,MAAA;AAAA,QACA,aAAA;AAAA,QACA,MAAA;AAAA,QACA,QAAA;AAAA,QACA,QAAA;AAAA,QACA,UAAA;AAAA,QACA,QAAA;AAAA,QACA,WAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,MAAA;AAAA,QACA,aAAA;AAAA,QACA,MAAA;AAAA,QACA,QAAA;AAAA,QACA,QAAA;AAAA,QACA,UAAA;AAAA,QACA,QAAA;AAAA,QACA,WAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,MAAA;AAAA,QACA,aAAA;AAAA,QACA,MAAA;AAAA,QACA,QAAA;AAAA,QACA,QAAA;AAAA,QACA,UAAA;AAAA,QACA,QAAA;AAAA,QACA,WAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,MAAA;AAAA,QACA,aAAA;AAAA,QACA,MAAA;AAAA,QACA,QAAA;AAAA,QACA,QAAA;AAAA,QACA,UAAA;AAAA,QACA,QAAA;AAAA,QACA,WAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,MAAA;AAAA,QACA,aAAA;AAAA,QACA,MAAA;AAAA,QACA,QAAA;AAAA,QACA,QAAA;AAAA,QACA,UAAA;AAAA,QACA,QAAA;AAAA,QACA,WAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,MAAA;AAAA,QACA,aAAA;AAAA,QACA,MAAA;AAAA,QACA,QAAA;AAAA,QACA,QAAA;AAAA,QACA,UAAA;AAAA,QACA,QAAA;AAAA,QACA,WAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,MAAA;AAAA,QACA,aAAA;AAAA,QACA,MAAA;AAAA,QACA,QAAA;AAAA,QACA,QAAA;AAAA,QACA,UAAA;AAAA,QACA,QAAA;AAAA,QACA,WAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,MAAA;AAAA,QACA,aAAA;AAAA,QACA,MAAA;AAAA,QACA,QAAA;AAAA,QACA,QAAA;AAAA,QACA,UAAA;AAAA,QACA,QAAA;AAAA,QACA,WAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,MAAA;AAAA,QACA,aAAA;AAAA,QACA,MAAA;AAAA,QACA,QAAA;AAAA,QACA,QAAA;AAAA,QACA,UAAA;AAAA,QACA,QAAA;AAAA,QACA,WAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,MAAA;AAAA,QACA,aAAA;AAAA,QACA,MAAA;AAAA,QACA,QAAA;AAAA,QACA,QAAA;AAAA,QACA,UAAA;AAAA,QACA,QAAA;AAAA,QACA,WAAA;AAAA,MACA;AAAA,IACA,CAAA;AAGA,UAAA,mBAAAE,cAAA,SAAA,MAAA;AACA,aAAA,gBAAA,QAAA,KAAA;AAAA,IACA,CAAA;AAGAC,kBAAAA,UAAA,MAAA;AAEAC,oBAAAA,MAAA,sBAAA;AAAA,QACA,OAAA;AAAA,MACA,CAAA;AAGA,UAAA;AACA,cAAA,aAAAA,oBAAA;AACA,wBAAA,QAAA,WAAA,mBAAA;AAGAA,sBAAAA,MAAA,eAAA,mBAAA,gBAAA,KAAA;AAAA,MACA,SAAA,GAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,+CAAA,aAAA,CAAA;AACA,wBAAA,QAAA;AAAA,MACA;AAGA;AAGA;IACA,CAAA;AAGAC,kBAAAA,OAAA,MAAA;AAEA,UAAA,MAAA,uBAAA;AACA,sBAAA,MAAA,qBAAA;AACA,cAAA,wBAAA;AAAA,MACA;AAAA,IACA,CAAA;AAEAC,kBAAAA,YAAA,MAAA;AAEA,UAAA,MAAA,uBAAA;AACA,sBAAA,MAAA,qBAAA;AACA,cAAA,wBAAA;AAAA,MACA;AAAA,IACA,CAAA;AAIA,UAAA,uBAAA,MAAA;AACA,YAAA,MAAA,oBAAA;AACA,YAAA,aAAA,IAAA;AACA,YAAA,eAAA,IAAA;AACA,YAAA,cAAA,IAAA;AAGA,YAAA,iBAAA,IAAA,KAAA,aAAA,cAAA,CAAA,EAAA;AAGA,YAAA,cAAA,IAAA,KAAA,aAAA,eAAA,GAAA,CAAA,EAAA;AAEA,YAAA,OAAA,CAAA;AAGA,eAAA,IAAA,GAAA,IAAA,gBAAA,KAAA;AACA,aAAA,KAAA;AAAA,UACA,KAAA;AAAA,UACA,QAAA;AAAA,UACA,SAAA;AAAA,UACA,UAAA;AAAA,UACA,QAAA;AAAA,UACA,OAAA;AAAA,QACA,CAAA;AAAA,MACA;AAGA,eAAA,IAAA,GAAA,KAAA,aAAA,KAAA;AACA,aAAA,KAAA;AAAA,UACA,KAAA;AAAA,UACA,QAAA,IAAA;AAAA;AAAA,UACA,SAAA,MAAA;AAAA,UACA,UAAA,IAAA;AAAA,UACA,QAAA,cAAA;AAAA,UACA,OAAA;AAAA,QACA,CAAA;AAAA,MACA;AAGA,YAAA,YAAA,iBAAA;AACA,YAAA,iBAAA,YAAA,MAAA,IAAA,IAAA,IAAA,YAAA;AAEA,eAAA,IAAA,GAAA,IAAA,gBAAA,KAAA;AACA,aAAA,KAAA;AAAA,UACA,KAAA;AAAA,UACA,QAAA;AAAA,UACA,SAAA;AAAA,UACA,UAAA;AAAA,UACA,QAAA;AAAA,UACA,OAAA;AAAA,QACA,CAAA;AAAA,MACA;AAEA,mBAAA,QAAA;AAGA,YAAA,WAAA,aAAA,MAAA,KAAA,SAAA,IAAA,OAAA;AACA,UAAA,UAAA;AACA,sBAAA,QAAA,SAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,gBAAA,CAAA,QAAA;AAEA,aAAA;AAAA,IACA;AAGA,UAAA,oBAAA,MAAA;AAGA,iBAAA,MAAA;AAAA,MAEA,GAAA,GAAA;AAAA,IACA;AAGA,UAAA,SAAA,MAAA;AACA,UAAA,cAAA;AAAA;AAIAF,oBAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAEA,iBAAA,MAAA;AAEA,cAAA,aAAA,aAAA,MAAA,UAAA,SAAA,IAAA,OAAA;AACA,YAAA,eAAA,IAAA;AACA,uBAAA,MAAA,UAAA,EAAA,SAAA;AACA,qBAAA,SAAA,aAAA,MAAA,UAAA,EAAA;AACA,wBAAA,QAAA;AACA,yBAAA,SAAA;AAAA,QACA;AAEAA,sBAAA,MAAA,YAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA,WAAA,aAAA,MAAA,UAAA,EAAA,SAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AAAA,MACA,GAAA,GAAA;AAAA,IACA;AAGA,UAAA,aAAA,CAAA,UAAA;AACA,YAAA,OAAA,WAAA,MAAA,KAAA;AAEA,UAAA,KAAA,WAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AACA;AAAA,MACA;AAGA,cAAA,KAAA,MAAA;AAAA,QACA,KAAA;AAEAA,wBAAAA,MAAA,UAAA;AAAA,YACA,KAAA;AAAA,YACA,SAAA,MAAA;AACAA,4BAAAA,MAAA,MAAA,OAAA,+CAAA,WAAA;AAEA,kBAAA,KAAA,YAAA,KAAA,QAAA;AAEA,2BAAA,SAAA,KAAA;AACAA,8BAAAA,MAAA,UAAA;AAAA,kBACA,OAAA;AAAA,kBACA,MAAA;AAAA,kBACA,UAAA;AAAA,gBACA,CAAA;AAAA,cACA,OAAA;AAEA,mCAAA,OAAA,CAAA;AACAA,8BAAAA,MAAA,UAAA;AAAA,kBACA,OAAA;AAAA,kBACA,MAAA;AAAA,kBACA,UAAA;AAAA,gBACA,CAAA;AAAA,cACA;AAAA,YACA;AAAA,YACA,MAAA,MAAA;AAEAA,4BAAAA,MAAA,WAAA;AAAA,gBACA,KAAA;AAAA,gBACA,SAAA,MAAA;AACAA,gCAAAA,MAAA,MAAA,OAAA,+CAAA,uBAAA;AAEA,sBAAA,KAAA,YAAA,KAAA,QAAA;AAEA,+BAAA,SAAA,KAAA;AACAA,kCAAAA,MAAA,UAAA;AAAA,sBACA,OAAA;AAAA,sBACA,MAAA;AAAA,sBACA,UAAA;AAAA,oBACA,CAAA;AAAA,kBACA,OAAA;AAEA,uCAAA,OAAA,CAAA;AACAA,kCAAAA,MAAA,UAAA;AAAA,sBACA,OAAA;AAAA,sBACA,MAAA;AAAA,sBACA,UAAA;AAAA,oBACA,CAAA;AAAA,kBACA;AAAA,gBACA;AAAA,gBACA,MAAA,CAAA,QAAA;AACAA,gCAAA,MAAA,MAAA,SAAA,+CAAA,SAAA,GAAA;AACAA,gCAAAA,MAAA,UAAA;AAAA,oBACA,OAAA;AAAA,oBACA,MAAA;AAAA,kBACA,CAAA;AAAA,gBACA;AAAA,cACA,CAAA;AAAA,YACA;AAAA,UACA,CAAA;AACA;AAAA,QACA,KAAA;AAEAA,wBAAAA,MAAA,WAAA;AAAA,YACA,KAAA;AAAA,YACA,SAAA,MAAA;AACAA,4BAAAA,MAAA,MAAA,OAAA,+CAAA,aAAA;AAEA,kBAAA,KAAA,YAAA,KAAA,QAAA;AAEA,2BAAA,SAAA,KAAA;AACAA,8BAAAA,MAAA,UAAA;AAAA,kBACA,OAAA;AAAA,kBACA,MAAA;AAAA,kBACA,UAAA;AAAA,gBACA,CAAA;AAAA,cACA,OAAA;AAEA,mCAAA,OAAA,CAAA;AACAA,8BAAAA,MAAA,UAAA;AAAA,kBACA,OAAA;AAAA,kBACA,MAAA;AAAA,kBACA,UAAA;AAAA,gBACA,CAAA;AAAA,cACA;AAAA,YACA;AAAA,YACA,MAAA,CAAA,QAAA;AACAA,4BAAA,MAAA,MAAA,SAAA,+CAAA,eAAA,GAAA;AAEAA,4BAAAA,MAAA,UAAA;AAAA,gBACA,KAAA;AAAA,gBACA,SAAA,MAAA;AACAA,gCAAAA,MAAA,MAAA,OAAA,+CAAA,WAAA;AAEAA,gCAAAA,MAAA,UAAA;AAAA,oBACA,OAAA;AAAA,oBACA,MAAA;AAAA,oBACA,UAAA;AAAA,kBACA,CAAA;AAEA,sBAAA,KAAA,YAAA,KAAA,QAAA;AAEA,+BAAA,SAAA,KAAA;AAAA,kBACA,OAAA;AAEA,uCAAA,OAAA,CAAA;AAAA,kBACA;AAAA,gBACA;AAAA,gBACA,MAAA,MAAA;AACAA,gCAAAA,MAAA,UAAA;AAAA,oBACA,OAAA;AAAA,oBACA,MAAA;AAAA,kBACA,CAAA;AAAA,gBACA;AAAA,cACA,CAAA;AAAA,YACA;AAAA,UACA,CAAA;AACA;AAAA,QACA,KAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,KAAA;AAAA,YACA,SAAA,MAAA;AACA,iCAAA,KAAA;AAGA,yBAAA,MAAA;AAEA,sBAAA,QAAA;AACA,sBAAA,YAAA,MAAA,MAAA,SAAA,CAAA;AACA,oBAAA,aAAA,UAAA,OAAA,OAAA,UAAA,IAAA,wBAAA,YAAA;AACA,4BAAA,IAAA;gBACA;AAAA,cACA,GAAA,GAAA;AAAA,YACA;AAAA,YACA,MAAA,CAAA,QAAA;AACAA,4BAAA,MAAA,MAAA,SAAA,+CAAA,WAAA,GAAA;AAAA,YACA;AAAA,UACA,CAAA;AACA;AAAA,QACA,KAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,KAAA;AAAA,YACA,SAAA,MAAA;AACAA,4BAAAA,MAAA,MAAA,OAAA,+CAAA,WAAA;AACA,iCAAA,KAAA;AAAA,YACA;AAAA,YACA,MAAA,MAAA;AACAA,4BAAAA,MAAA,WAAA;AAAA,gBACA,KAAA;AAAA,gBACA,SAAA,MAAA;AACAA,gCAAAA,MAAA,MAAA,OAAA,+CAAA,uBAAA;AACA,qCAAA,KAAA;AAAA,gBACA;AAAA,gBACA,MAAA,CAAA,QAAA;AACAA,gCAAA,MAAA,MAAA,SAAA,+CAAA,SAAA,GAAA;AACAA,gCAAAA,MAAA,UAAA;AAAA,oBACA,OAAA;AAAA,oBACA,MAAA;AAAA,kBACA,CAAA;AAAA,gBACA;AAAA,cACA,CAAA;AAAA,YACA;AAAA,UACA,CAAA;AACA;AAAA,QACA,KAAA;AAEAA,wBAAAA,MAAA,cAAA;AAAA,YACA,iBAAA;AAAA;AAAA,YACA,OAAA,CAAA,mBAAA,eAAA;AAAA;AAAA,YACA,SAAA,MAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA,gBACA,OAAA;AAAA,gBACA,MAAA;AAAA,gBACA,UAAA;AAAA,cACA,CAAA;AAGA,oBAAA,oBAAA;AAGA,yBAAA,MAAA;AACA,oBAAA,MAAA,sBAAA,OAAA;AACA,wBAAA,oBAAA;AACAA,gCAAAA,MAAA,UAAA;AAAA,oBACA,OAAA;AAAA,oBACA,MAAA;AAAA,kBACA,CAAA;AAAA,gBACA;AAAA,cACA,GAAA,GAAA;AAAA,YACA;AAAA,YACA,MAAA,CAAA,QAAA;AACAA,4BAAA,MAAA,MAAA,SAAA,+CAAA,aAAA,GAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA,gBACA,OAAA;AAAA,gBACA,MAAA;AAAA,cACA,CAAA;AAAA,YACA;AAAA,UACA,CAAA;AACA;AAAA,QACA,KAAA;AAEAA,wBAAAA,MAAA,WAAA;AAAA,YACA,KAAA;AAAA,YACA,SAAA,MAAA;AACAA,4BAAAA,MAAA,MAAA,OAAA,+CAAA,aAAA;AAEA,kBAAA,KAAA,YAAA,KAAA,QAAA;AAEA,2BAAA,SAAA,KAAA;AACAA,8BAAAA,MAAA,UAAA;AAAA,kBACA,OAAA;AAAA,kBACA,MAAA;AAAA,kBACA,UAAA;AAAA,gBACA,CAAA;AAAA,cACA,OAAA;AAEA,mCAAA,OAAA,CAAA;AACAA,8BAAAA,MAAA,UAAA;AAAA,kBACA,OAAA;AAAA,kBACA,MAAA;AAAA,kBACA,UAAA;AAAA,gBACA,CAAA;AAAA,cACA;AAAA,YACA;AAAA,YACA,MAAA,CAAA,QAAA;AACAA,4BAAA,MAAA,MAAA,SAAA,+CAAA,eAAA,GAAA;AAEAA,4BAAAA,MAAA,UAAA;AAAA,gBACA,KAAA;AAAA,gBACA,SAAA,MAAA;AACAA,gCAAAA,MAAA,MAAA,OAAA,+CAAA,WAAA;AAEAA,gCAAAA,MAAA,UAAA;AAAA,oBACA,OAAA;AAAA,oBACA,MAAA;AAAA,oBACA,UAAA;AAAA,kBACA,CAAA;AAEA,sBAAA,KAAA,YAAA,KAAA,QAAA;AAEA,+BAAA,SAAA,KAAA;AAAA,kBACA,OAAA;AAEA,uCAAA,OAAA,CAAA;AAAA,kBACA;AAAA,gBACA;AAAA,gBACA,MAAA,MAAA;AACAA,gCAAAA,MAAA,UAAA;AAAA,oBACA,OAAA;AAAA,oBACA,MAAA;AAAA,kBACA,CAAA;AAAA,gBACA;AAAA,cACA,CAAA;AAAA,YACA;AAAA,UACA,CAAA;AACA;AAAA,QACA,KAAA;AAEAA,wBAAAA,MAAA,WAAA;AAAA,YACA,KAAA;AAAA,YACA,SAAA,MAAA;AACAA,4BAAAA,MAAA,MAAA,OAAA,+CAAA,aAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA,gBACA,OAAA;AAAA,gBACA,MAAA;AAAA,gBACA,UAAA;AAAA,cACA,CAAA;AAAA,YACA;AAAA,YACA,MAAA,CAAA,QAAA;AACAA,4BAAA,MAAA,MAAA,SAAA,+CAAA,eAAA,GAAA;AAEAA,4BAAAA,MAAA,UAAA;AAAA,gBACA,KAAA;AAAA,gBACA,SAAA,MAAA;AACAA,gCAAAA,MAAA,MAAA,OAAA,+CAAA,WAAA;AACAA,gCAAAA,MAAA,UAAA;AAAA,oBACA,OAAA;AAAA,oBACA,MAAA;AAAA,oBACA,UAAA;AAAA,kBACA,CAAA;AAAA,gBACA;AAAA,gBACA,MAAA,MAAA;AACAA,gCAAAA,MAAA,UAAA;AAAA,oBACA,OAAA;AAAA,oBACA,MAAA;AAAA,kBACA,CAAA;AAAA,gBACA;AAAA,cACA,CAAA;AAAA,YACA;AAAA,UACA,CAAA;AACA;AAAA,QACA,KAAA;AACAA,wBAAAA,MAAA,WAAA;AAAA,YACA,KAAA;AAAA,YACA,SAAA,MAAA;AACAA,4BAAAA,MAAA,MAAA,OAAA,+CAAA,YAAA;AACA,iCAAA,KAAA;AAAA,YACA;AAAA,YACA,MAAA,CAAA,QAAA;AACAA,4BAAA,MAAA,MAAA,SAAA,+CAAA,aAAA,GAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA,gBACA,OAAA;AAAA,gBACA,MAAA;AAAA,cACA,CAAA;AAAA,YACA;AAAA,UACA,CAAA;AACA;AAAA,QACA,KAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,KAAA;AAAA,YACA,SAAA,MAAA;AACAA,4BAAAA,MAAA,MAAA,OAAA,+CAAA,SAAA;AACA,yBAAA,MAAA;AACAA,8BAAAA,MAAA,UAAA;AAAA,kBACA,OAAA;AAAA,kBACA,MAAA;AAAA,kBACA,UAAA;AAAA,gBACA,CAAA;AAAA,cACA,GAAA,GAAA;AAIA,oBAAA,sBAAA;AAGA,yBAAA,MAAA;AACA,oBAAA,MAAA,wBAAA,OAAA;AACA,qCAAA,KAAA;AACA,wBAAA,sBAAA;AAAA,gBACA;AAAA,cACA,GAAA,GAAA;AAAA,YACA;AAAA,YACA,MAAA,CAAA,QAAA;AACAA,4BAAA,MAAA,MAAA,SAAA,+CAAA,WAAA,GAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA,gBACA,OAAA;AAAA,gBACA,MAAA;AAAA,cACA,CAAA;AAAA,YACA;AAAA,UACA,CAAA;AACA;AAAA,QACA,KAAA;AAEAA,wBAAAA,MAAA,WAAA;AAAA,YACA,KAAA;AAAA,YACA,SAAA,MAAA;AACAA,4BAAAA,MAAA,MAAA,OAAA,+CAAA,YAAA;AAGA,oBAAA,sBAAA;AAGA,yBAAA,MAAA;AACA,oBAAA,MAAA,wBAAA,OAAA;AACA,qCAAA,KAAA;AACA,wBAAA,sBAAA;AAAA,gBACA;AAAA,cACA,GAAA,GAAA;AAAA,YACA;AAAA,YACA,MAAA,CAAA,QAAA;AACAA,4BAAA,MAAA,MAAA,SAAA,+CAAA,oBAAA,GAAA;AAEAA,4BAAAA,MAAA,WAAA;AAAA,gBACA,KAAA;AAAA,gBACA,SAAA,MAAA;AACAA,gCAAAA,MAAA,MAAA,OAAA,+CAAA,kBAAA;AAEA,wBAAA,sBAAA;AAGA,6BAAA,MAAA;AACA,wBAAA,MAAA,wBAAA,OAAA;AACA,yCAAA,KAAA;AACA,4BAAA,sBAAA;AAAA,oBACA;AAAA,kBACA,GAAA,GAAA;AAAA,gBACA;AAAA,gBACA,MAAA,CAAA,MAAA;AACAA,gCAAA,MAAA,MAAA,SAAA,+CAAA,oBAAA,CAAA;AACAA,gCAAAA,MAAA,UAAA;AAAA,oBACA,OAAA;AAAA,oBACA,MAAA;AAAA,kBACA,CAAA;AAAA,gBACA;AAAA,cACA,CAAA;AAAA,YACA;AAAA,UACA,CAAA;AACA;AAAA,QACA;AACAA,wBAAA,MAAA,MAAA,OAAA,+CAAA,WAAA,KAAA,IAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AACA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,qBAAA,CAAA,OAAA,iBAAA,MAAA;AACA,YAAA,OAAA,WAAA,MAAA,KAAA;AACA,UAAA,KAAA,WAAA,KAAA,QAAA;AACA,cAAA,cAAA,KAAA,IAAA,KAAA,WAAA,gBAAA,KAAA,MAAA;AACA,mBAAA,MAAA,KAAA,EAAA,WAAA;AAEA,YAAA,eAAA,KAAA,QAAA;AACA,qBAAA,MAAA,KAAA,EAAA,YAAA;AACA,qBAAA,SAAA,KAAA;AAAA,QACA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,aAAA,CAAA,KAAA,oBAAA;AACA,UAAA,IAAA,WAAA,oBAAA,GAAA;AACA,cAAA,eAAA,IAAA,QAAA,sBAAA,IAAA;AACAA,sBAAAA,MAAA,WAAA;AAAA,UACA,KAAA;AAAA,UACA,SAAA,MAAA;AACA,gBAAA,mBAAA,OAAA,oBAAA,YAAA;AACA;YACA;AAAA,UACA;AAAA,UACA,MAAA,CAAA,QAAA;AACAA,0BAAA,MAAA,MAAA,SAAA,+CAAA,SAAA,GAAA;AAEAA,0BAAAA,MAAA,WAAA;AAAA,cACA;AAAA,cACA,SAAA;AAAA,cACA,MAAA,CAAA,MAAA;AACAA,8BAAA,MAAA,MAAA,SAAA,+CAAA,cAAA,CAAA;AAEA,oBAAA,mBAAA,OAAA,oBAAA,YAAA;AACA;gBACA;AACAA,8BAAAA,MAAA,UAAA;AAAA,kBACA,OAAA;AAAA,kBACA,MAAA;AAAA,gBACA,CAAA;AAAA,cACA;AAAA,YACA,CAAA;AAAA,UACA;AAAA,QACA,CAAA;AAAA,MACA,WAAA,IAAA,WAAA,6BAAA,GAAA;AACA,cAAA,eAAA,IAAA,QAAA,+BAAA,IAAA;AACAA,sBAAAA,MAAA,WAAA;AAAA,UACA,KAAA;AAAA,UACA,SAAA,MAAA;AACA,gBAAA,mBAAA,OAAA,oBAAA,YAAA;AACA;YACA;AAAA,UACA;AAAA,UACA,MAAA,CAAA,QAAA;AACAA,0BAAA,MAAA,MAAA,SAAA,+CAAA,SAAA,GAAA;AAEAA,0BAAAA,MAAA,WAAA;AAAA,cACA;AAAA,cACA,SAAA;AAAA,cACA,MAAA,CAAA,MAAA;AACAA,8BAAA,MAAA,MAAA,SAAA,+CAAA,cAAA,CAAA;AAEA,oBAAA,mBAAA,OAAA,oBAAA,YAAA;AACA;gBACA;AACAA,8BAAAA,MAAA,UAAA;AAAA,kBACA,OAAA;AAAA,kBACA,MAAA;AAAA,gBACA,CAAA;AAAA,cACA;AAAA,YACA,CAAA;AAAA,UACA;AAAA,QACA,CAAA;AAAA,MACA,OAAA;AAEAA,sBAAAA,MAAA,WAAA;AAAA,UACA;AAAA,UACA,SAAA;AAAA,UACA,MAAA,CAAA,QAAA;AACAA,0BAAA,MAAA,MAAA,SAAA,+CAAA,SAAA,GAAA;AAEA,gBAAA,mBAAA,OAAA,oBAAA,YAAA;AACA;YACA;AACAA,0BAAAA,MAAA,UAAA;AAAA,cACA,OAAA;AAAA,cACA,MAAA;AAAA,YACA,CAAA;AAAA,UACA;AAAA,QACA,CAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,SAAA,MAAA;AACAA,oBAAA,MAAA,aAAA;AAAA,IACA;AAGA,UAAA,kBAAA,MAAA;AACA,qBAAA,QAAA;AAAA,IACA;AAGA,UAAA,mBAAA,MAAA;AACA,qBAAA,QAAA;AAAA,IACA;AAGA,aAAA;AAAA,MACA,kBAAA,KAAA;AAEA,cAAA,YAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,UACA,UAAA;AAAA,UACA,SAAA,CAAAG,SAAA;AAEA,gBAAAA,KAAA,gBAAAA,KAAA,aAAA,SAAA,GAAA;AACAH,4BAAAA,MAAA,MAAA,OAAA,+CAAA,SAAA;AAEA,kBAAA,MAAA,sBAAA,QAAA,MAAA,sBAAA,QAAA;AACA,sBAAA,YAAA,MAAA;AACA,sBAAA,oBAAA;AAGA,mCAAA,SAAA;AAEAA,8BAAAA,MAAA,UAAA;AAAA,kBACA,OAAA;AAAA,kBACA,MAAA;AAAA,gBACA,CAAA;AAAA,cACA;AAAA,YACA,OAAA;AACAA,4BAAAA,MAAA,MAAA,OAAA,+CAAA,cAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA,gBACA,OAAA;AAAA,gBACA,MAAA;AAAA,cACA,CAAA;AAAA,YACA;AAAA,UACA;AAAA,UACA,MAAA,CAAA,QAAA;AACAA,0BAAA,MAAA,MAAA,SAAA,gDAAA,SAAA,GAAA;AAAA,UACA;AAAA,QACA;AAEA,eAAA;AAAA,MACA;AAAA,IACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACh/BA,GAAG,WAAW,eAAe;"}