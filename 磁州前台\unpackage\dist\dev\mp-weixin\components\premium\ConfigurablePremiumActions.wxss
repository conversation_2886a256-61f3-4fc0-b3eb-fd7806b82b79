/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-445b6c30, html.data-v-445b6c30, #app.data-v-445b6c30, .index-container.data-v-445b6c30 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.premium-actions-container.data-v-445b6c30 {
  width: 100%;
  margin: 20rpx 0;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", "Helvetica Neue", Helvetica, Arial, sans-serif;
  /* 添加容器动画 */
  animation: fadeInUp-445b6c30 0.3s ease-out;
}

/* 容器动画 */
@keyframes fadeInUp-445b6c30 {
from {
    opacity: 0;
    transform: translateY(20rpx);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
.direct-options.data-v-445b6c30 {
  padding: 0;
}
.premium-option-card.data-v-445b6c30 {
  margin-bottom: 16px;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  background: white;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateZ(0);
  /* 启用硬件加速 */
}

/* 触摸反馈 */
.premium-option-card.data-v-445b6c30:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

/* 悬停效果（适用于支持的设备） */
@media (hover: hover) {
.premium-option-card.data-v-445b6c30:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);
}
}
.option-inner.data-v-445b6c30 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
}
.option-left.data-v-445b6c30 {
  display: flex;
  align-items: center;
  flex: 1;
}
.option-icon-container.data-v-445b6c30 {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  transition: transform 0.3s ease;
}
.option-icon-container.paid-icon.data-v-445b6c30 {
  background: linear-gradient(135deg, #ff9a9e, #fad0c4);
}
.option-icon-container.data-v-445b6c30:active {
  transform: scale(0.9);
}
.option-icon.data-v-445b6c30 {
  width: 24rpx;
  height: 24rpx;
}
.option-content.data-v-445b6c30 {
  flex: 1;
}
.option-title.data-v-445b6c30 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}
.option-title.bold-title.data-v-445b6c30 {
  font-weight: 700;
}
.option-desc.data-v-445b6c30 {
  font-size: 26rpx;
  color: #666;
  display: block;
  line-height: 1.4;
}
.option-right.data-v-445b6c30 {
  display: flex;
  align-items: center;
}
.option-tag.data-v-445b6c30 {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
}
.free-tag.data-v-445b6c30 {
  background: linear-gradient(135deg, #4A90E2, #3B7DFC);
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(74, 144, 226, 0.3);
}
.paid-tag.data-v-445b6c30 {
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
}
.option-tag.data-v-445b6c30:active {
  transform: scale(0.95);
}

/* 广告选项特殊样式 */
.ad-option.data-v-445b6c30 {
  border-left: 4rpx solid #4A90E2;
}
.ad-option .option-icon-container.data-v-445b6c30 {
  background: linear-gradient(135deg, #4A90E2, #3B7DFC);
}

/* 付费选项特殊样式 */
.paid-option.data-v-445b6c30 {
  border-left: 4rpx solid #FF6B6B;
}
.paid-option .option-icon-container.data-v-445b6c30 {
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
}

/* 付费模态框样式 */
.payment-modal.data-v-445b6c30 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
  animation: fadeIn-445b6c30 0.3s ease-out;
}
.payment-content.data-v-445b6c30 {
  width: 90%;
  max-width: 650rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  animation: slideInUp-445b6c30 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.15);
}
.payment-header.data-v-445b6c30 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.payment-title.data-v-445b6c30 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.close-btn.data-v-445b6c30 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
  color: #666;
  font-size: 40rpx;
  transition: all 0.3s ease;
}
.close-btn.data-v-445b6c30:active {
  background: #e0e0e0;
  transform: scale(0.95);
}
.payment-body.data-v-445b6c30 {
  padding: 30rpx;
}
.payment-desc.data-v-445b6c30 {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
  display: block;
}
.duration-options.data-v-445b6c30 {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.duration-item.data-v-445b6c30 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 16rpx;
  background: white;
  transition: all 0.3s ease;
  position: relative;
}
.duration-item.data-v-445b6c30:active {
  transform: scale(0.98);
}
.duration-item.active.data-v-445b6c30 {
  border-color: #4f46e5;
  background: linear-gradient(135deg, #f8faff, #eef2ff);
  box-shadow: 0 4rpx 15rpx rgba(79, 70, 229, 0.15);
}
.duration-info.data-v-445b6c30 {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.duration-text.data-v-445b6c30 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.duration-price.data-v-445b6c30 {
  font-size: 40rpx;
  font-weight: 700;
  color: #ff6b6b;
}
.duration-badge.data-v-445b6c30 {
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8e53);
  color: white;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
}
.payment-footer.data-v-445b6c30 {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}
.payment-btn.data-v-445b6c30 {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}
.cancel-btn.data-v-445b6c30 {
  background: #f5f5f5;
  color: #666;
}
.cancel-btn.data-v-445b6c30:active {
  background: #e0e0e0;
  transform: scale(0.98);
}
.confirm-btn.data-v-445b6c30 {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  color: white;
  box-shadow: 0 4rpx 15rpx rgba(79, 70, 229, 0.3);
}
.confirm-btn.data-v-445b6c30:active {
  transform: scale(0.98);
}
.confirm-btn.disabled.data-v-445b6c30 {
  background: #d1d5db;
  color: #9ca3af;
  box-shadow: none;
  pointer-events: none;
}

/* 模态框动画 */
@keyframes fadeIn-445b6c30 {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
@keyframes slideInUp-445b6c30 {
from {
    opacity: 0;
    transform: translateY(100rpx) scale(0.9);
}
to {
    opacity: 1;
    transform: translateY(0) scale(1);
}
}
/* 响应式设计 */
@media screen and (max-width: 750rpx) {
.option-inner.data-v-445b6c30 {
    padding: 16rpx;
}
.option-icon-container.data-v-445b6c30 {
    width: 40rpx;
    height: 40rpx;
    margin-right: 16rpx;
}
.option-icon.data-v-445b6c30 {
    width: 20rpx;
    height: 20rpx;
}
.option-title.data-v-445b6c30 {
    font-size: 28rpx;
}
.option-desc.data-v-445b6c30 {
    font-size: 24rpx;
}
.option-tag.data-v-445b6c30 {
    padding: 6rpx 12rpx;
    font-size: 22rpx;
}
.payment-content.data-v-445b6c30 {
    width: 95%;
}
.payment-header.data-v-445b6c30,
.payment-body.data-v-445b6c30,
.payment-footer.data-v-445b6c30 {
    padding: 20rpx;
}
.duration-item.data-v-445b6c30 {
    padding: 20rpx;
}
.duration-text.data-v-445b6c30 {
    font-size: 28rpx;
}
.duration-price.data-v-445b6c30 {
    font-size: 36rpx;
}
.payment-btn.data-v-445b6c30 {
    height: 80rpx;
    font-size: 28rpx;
}
}