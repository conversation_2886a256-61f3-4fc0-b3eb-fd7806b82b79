{"version": 3, "file": "favorites.js", "sources": ["subPackages/user/pages/favorites.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcdXNlclxwYWdlc1xmYXZvcml0ZXMudnVl"], "sourcesContent": ["<template>\n  <view class=\"favorites-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"navbar-left\" @click=\"goBack\">\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\n      </view>\n      <view class=\"navbar-title\">收藏/关注</view>\n      <view class=\"navbar-right\"></view>\n    </view>\n    \n    <!-- 顶部选项卡 -->\n    <view class=\"tabs-container\" :style=\"{ top: navbarHeight + 'px' }\">\n      <view \n        class=\"tab-item\" \n        v-for=\"(tab, index) in tabs\" \n        :key=\"index\"\n        :class=\"{ active: currentTab === index }\"\n        @click=\"switchTab(index)\"\n      >\n        <text class=\"tab-text\">{{ tab.name }}</text>\n      </view>\n      <view class=\"tab-line\" :style=\"tabLineStyle\"></view>\n    </view>\n    \n    <!-- 内容区域 -->\n    <view class=\"content-area\" :style=\"{ paddingTop: (navbarHeight + tabsHeight) + 'px' }\">\n      <swiper class=\"content-swiper\" :current=\"currentTab\" @change=\"onSwiperChange\">\n        <!-- 收藏列表 -->\n        <swiper-item>\n          <scroll-view scroll-y class=\"tab-scroll\" @scrolltolower=\"loadMore(0)\" refresher-enabled :refresher-triggered=\"refreshing[0]\" @refresherrefresh=\"onRefresh(0)\">\n            <view v-if=\"collectList.length > 0\" class=\"collect-list\">\n              <view class=\"collect-item\" v-for=\"(item, index) in collectList\" :key=\"index\" @click=\"viewDetail(item)\">\n                <view class=\"collect-left\">\n                  <image class=\"collect-image\" :src=\"item.image\" mode=\"aspectFill\"></image>\n                </view>\n                <view class=\"collect-right\">\n                  <view class=\"collect-title\">{{ item.title }}</view>\n                  <view class=\"collect-desc\">{{ item.desc }}</view>\n                  <view class=\"collect-meta\">\n                    <text class=\"collect-time\">{{ item.time }}</text>\n                    <text class=\"collect-type\">{{ item.type }}</text>\n                  </view>\n                </view>\n              </view>\n            </view>\n            <view v-else class=\"empty-view\">\n              <image class=\"empty-icon\" src=\"/static/images/empty.png\" mode=\"aspectFit\"></image>\n              <view class=\"empty-text\">暂无收藏内容</view>\n            </view>\n            <view v-if=\"collectList.length > 0 && !hasMore[0]\" class=\"list-bottom\">没有更多了</view>\n          </scroll-view>\n        </swiper-item>\n        \n        <!-- 关注列表 -->\n        <swiper-item>\n          <scroll-view scroll-y class=\"tab-scroll\" @scrolltolower=\"loadMore(1)\" refresher-enabled :refresher-triggered=\"refreshing[1]\" @refresherrefresh=\"onRefresh(1)\">\n            <view v-if=\"followList.length > 0\" class=\"follow-list\">\n              <view class=\"follow-item\" v-for=\"(item, index) in followList\" :key=\"index\" @click=\"viewUser(item)\">\n                <view class=\"follow-left\">\n                  <image class=\"follow-avatar\" :src=\"item.avatar\" mode=\"aspectFill\"></image>\n                </view>\n                <view class=\"follow-middle\">\n                  <view class=\"follow-name\">{{ item.name }}</view>\n                  <view class=\"follow-info\">{{ item.info }}</view>\n                </view>\n                <view class=\"follow-right\">\n                  <button class=\"follow-btn\" @click.stop=\"toggleFollow(item)\">\n                    {{ item.isFollowed ? '已关注' : '关注' }}\n                  </button>\n                </view>\n              </view>\n            </view>\n            <view v-else class=\"empty-view\">\n              <image class=\"empty-icon\" src=\"/static/images/empty.png\" mode=\"aspectFit\"></image>\n              <view class=\"empty-text\">暂无关注用户</view>\n            </view>\n            <view v-if=\"followList.length > 0 && !hasMore[1]\" class=\"list-bottom\">没有更多了</view>\n          </scroll-view>\n        </swiper-item>\n      </swiper>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue';\n\n// 响应式数据\nconst statusBarHeight = ref(20);\nconst navbarHeight = ref(64); // 导航栏高度\nconst tabsHeight = ref(44); // 选项卡高度\nconst tabs = ref([\n  { name: '收藏' },\n  { name: '关注' }\n]);\nconst currentTab = ref(0);\nconst collectList = ref([]);\nconst followList = ref([]);\nconst page = ref([1, 1]); // 当前页码\nconst pageSize = ref(10); // 每页显示数量\nconst hasMore = ref([true, true]); // 是否有更多数据\nconst refreshing = ref([false, false]); // 刷新状态\n\n// 计算属性\nconst tabLineStyle = computed(() => {\n  return {\n    transform: `translateX(${currentTab.value * (100 / tabs.value.length)}%)`,\n    width: `${100 / tabs.value.length}%`\n  };\n});\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n\n// 切换选项卡\nconst switchTab = (index) => {\n  currentTab.value = index;\n};\n\n// 轮播图切换事件\nconst onSwiperChange = (e) => {\n  currentTab.value = e.detail.current;\n};\n\n// 加载收藏列表\nconst loadCollectList = () => {\n  // 模拟请求延迟\n  setTimeout(() => {\n    // 模拟数据\n    const mockData = Array.from({ length: 10 }, (_, i) => ({\n      id: `collect_${page.value[0]}_${i}`,\n      title: `收藏内容标题 ${page.value[0]}_${i}`,\n      desc: '这是收藏内容的简短描述，可能包含多行文本内容...',\n      image: '/static/images/service1.jpg',\n      time: '2023-10-15',\n      type: ['商家', '服务', '商品', '资讯'][Math.floor(Math.random() * 4)]\n    }));\n    \n    if (page.value[0] === 1) {\n      collectList.value = mockData;\n    } else {\n      collectList.value = [...collectList.value, ...mockData];\n    }\n    \n    // 模拟是否还有更多数据\n    hasMore.value[0] = page.value[0] < 3;\n    \n    // 关闭刷新状态\n    refreshing.value[0] = false;\n  }, 500);\n};\n\n// 加载关注列表\nconst loadFollowList = () => {\n  // 模拟请求延迟\n  setTimeout(() => {\n    // 模拟数据\n    const mockData = Array.from({ length: 10 }, (_, i) => ({\n      id: `follow_${page.value[1]}_${i}`,\n      name: `用户名称 ${page.value[1]}_${i}`,\n      avatar: '/static/images/avatar.png',\n      info: '发布了20条内容，获得188人关注',\n      isFollowed: true\n    }));\n    \n    if (page.value[1] === 1) {\n      followList.value = mockData;\n    } else {\n      followList.value = [...followList.value, ...mockData];\n    }\n    \n    // 模拟是否还有更多数据\n    hasMore.value[1] = page.value[1] < 3;\n    \n    // 关闭刷新状态\n    refreshing.value[1] = false;\n  }, 500);\n};\n\n// 加载更多数据\nconst loadMore = (tabIndex) => {\n  if (!hasMore.value[tabIndex]) return;\n  \n  page.value[tabIndex]++;\n  if (tabIndex === 0) {\n    loadCollectList();\n  } else {\n    loadFollowList();\n  }\n};\n\n// 下拉刷新\nconst onRefresh = (tabIndex) => {\n  refreshing.value[tabIndex] = true;\n  page.value[tabIndex] = 1;\n  hasMore.value[tabIndex] = true;\n  \n  if (tabIndex === 0) {\n    loadCollectList();\n  } else {\n    loadFollowList();\n  }\n};\n\n// 查看收藏详情\nconst viewDetail = (item) => {\n  uni.navigateTo({\n    url: `/pages/detail/detail?id=${item.id}&type=${item.type}`\n  });\n};\n\n// 查看用户主页\nconst viewUser = (item) => {\n  uni.navigateTo({\n    url: `/pages/user/profile?id=${item.id}`\n  });\n};\n\n// 切换关注状态\nconst toggleFollow = (item) => {\n  item.isFollowed = !item.isFollowed;\n  uni.showToast({\n    title: item.isFollowed ? '已关注' : '已取消关注',\n    icon: 'none'\n  });\n};\n\n// 生命周期钩子\nonMounted(() => {\n  // 获取状态栏高度\n  const sysInfo = uni.getSystemInfoSync();\n  statusBarHeight.value = sysInfo.statusBarHeight;\n  navbarHeight.value = statusBarHeight.value + 44;\n  \n  // 加载初始数据\n  loadCollectList();\n  loadFollowList();\n});\n</script>\n\n<style>\n.favorites-container {\n  min-height: 100vh;\n  background-color: #f5f7fa;\n  position: relative;\n}\n\n/* 自定义导航栏 */\n.custom-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 44px;\n  display: flex;\n  align-items: center;\n  padding: 0 15px;\n  background-color: #0052CC;\n  color: #fff;\n  z-index: 100;\n}\n\n.navbar-left {\n  width: 80rpx;\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 16px;\n  font-weight: 500;\n}\n\n.navbar-right {\n  width: 80rpx;\n  text-align: right;\n}\n\n.back-icon {\n  width: 40rpx;\n  height: 40rpx;\n}\n\n/* 选项卡样式 */\n.tabs-container {\n  position: fixed;\n  left: 0;\n  right: 0;\n  height: 44px;\n  display: flex;\n  background-color: #fff;\n  border-bottom: 1px solid #eee;\n  z-index: 99;\n}\n\n.tab-item {\n  flex: 1;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  position: relative;\n}\n\n.tab-text {\n  font-size: 14px;\n  color: #333;\n  padding: 0 15px;\n}\n\n.tab-item.active .tab-text {\n  color: #0052CC;\n  font-weight: bold;\n}\n\n.tab-line {\n  position: absolute;\n  bottom: 0;\n  height: 3px;\n  background-color: #0052CC;\n  border-radius: 2px;\n  transition: transform 0.3s;\n}\n\n/* 内容区域 */\n.content-area {\n  position: relative;\n  height: 100vh;\n}\n\n.content-swiper {\n  height: 100%;\n}\n\n.tab-scroll {\n  height: 100%;\n}\n\n/* 收藏列表 */\n.collect-list {\n  padding: 15px;\n}\n\n.collect-item {\n  display: flex;\n  padding: 15px;\n  background-color: #fff;\n  border-radius: 8px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.collect-left {\n  width: 120rpx;\n  height: 120rpx;\n  margin-right: 15px;\n}\n\n.collect-image {\n  width: 100%;\n  height: 100%;\n  border-radius: 4px;\n}\n\n.collect-right {\n  flex: 1;\n}\n\n.collect-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8px;\n  display: -webkit-box;\n  -webkit-line-clamp: 1;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.collect-desc {\n  font-size: 14px;\n  color: #666;\n  line-height: 1.4;\n  margin-bottom: 10px;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.collect-meta {\n  display: flex;\n  justify-content: space-between;\n  font-size: 12px;\n  color: #999;\n}\n\n/* 关注列表 */\n.follow-list {\n  padding: 15px;\n}\n\n.follow-item {\n  display: flex;\n  align-items: center;\n  padding: 15px;\n  background-color: #fff;\n  border-radius: 8px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.follow-left {\n  margin-right: 15px;\n}\n\n.follow-avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n}\n\n.follow-middle {\n  flex: 1;\n}\n\n.follow-name {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.follow-info {\n  font-size: 13px;\n  color: #999;\n}\n\n.follow-right {\n  margin-left: 15px;\n}\n\n.follow-btn {\n  min-width: 150rpx;\n  height: 60rpx;\n  line-height: 60rpx;\n  padding: 0 15px;\n  font-size: 13px;\n  border-radius: 30rpx;\n  background-color: #0052CC;\n  color: #fff;\n}\n\n.follow-btn[disabled] {\n  background-color: #eee;\n  color: #999;\n}\n\n/* 空状态 */\n.empty-view {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding-top: 100px;\n}\n\n.empty-icon {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 20px;\n}\n\n.empty-text {\n  font-size: 14px;\n  color: #999;\n}\n\n/* 列表底部 */\n.list-bottom {\n  text-align: center;\n  padding: 15px 0;\n  font-size: 14px;\n  color: #999;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/user/pages/favorites.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "onMounted", "MiniProgramPage"], "mappings": ";;;;;;AAyFA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,eAAeA,cAAAA,IAAI,EAAE;AAC3B,UAAM,aAAaA,cAAAA,IAAI,EAAE;AACzB,UAAM,OAAOA,cAAAA,IAAI;AAAA,MACf,EAAE,MAAM,KAAM;AAAA,MACd,EAAE,MAAM,KAAM;AAAA,IAChB,CAAC;AACD,UAAM,aAAaA,cAAAA,IAAI,CAAC;AACxB,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAC1B,UAAM,aAAaA,cAAAA,IAAI,CAAA,CAAE;AACzB,UAAM,OAAOA,cAAAA,IAAI,CAAC,GAAG,CAAC,CAAC;AACNA,kBAAG,IAAC,EAAE;AACvB,UAAM,UAAUA,cAAAA,IAAI,CAAC,MAAM,IAAI,CAAC;AAChC,UAAM,aAAaA,cAAAA,IAAI,CAAC,OAAO,KAAK,CAAC;AAGrC,UAAM,eAAeC,cAAQ,SAAC,MAAM;AAClC,aAAO;AAAA,QACL,WAAW,cAAc,WAAW,SAAS,MAAM,KAAK,MAAM,OAAO;AAAA,QACrE,OAAO,GAAG,MAAM,KAAK,MAAM,MAAM;AAAA,MACrC;AAAA,IACA,CAAC;AAGD,UAAM,SAAS,MAAM;AACnBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,YAAY,CAAC,UAAU;AAC3B,iBAAW,QAAQ;AAAA,IACrB;AAGA,UAAM,iBAAiB,CAAC,MAAM;AAC5B,iBAAW,QAAQ,EAAE,OAAO;AAAA,IAC9B;AAGA,UAAM,kBAAkB,MAAM;AAE5B,iBAAW,MAAM;AAEf,cAAM,WAAW,MAAM,KAAK,EAAE,QAAQ,GAAI,GAAE,CAAC,GAAG,OAAO;AAAA,UACrD,IAAI,WAAW,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC;AAAA,UACjC,OAAO,UAAU,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC;AAAA,UACnC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM,CAAC,MAAM,MAAM,MAAM,IAAI,EAAE,KAAK,MAAM,KAAK,OAAQ,IAAG,CAAC,CAAC;AAAA,QAC7D,EAAC;AAEF,YAAI,KAAK,MAAM,CAAC,MAAM,GAAG;AACvB,sBAAY,QAAQ;AAAA,QAC1B,OAAW;AACL,sBAAY,QAAQ,CAAC,GAAG,YAAY,OAAO,GAAG,QAAQ;AAAA,QACvD;AAGD,gBAAQ,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI;AAGnC,mBAAW,MAAM,CAAC,IAAI;AAAA,MACvB,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,iBAAiB,MAAM;AAE3B,iBAAW,MAAM;AAEf,cAAM,WAAW,MAAM,KAAK,EAAE,QAAQ,GAAI,GAAE,CAAC,GAAG,OAAO;AAAA,UACrD,IAAI,UAAU,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC;AAAA,UAChC,MAAM,QAAQ,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC;AAAA,UAChC,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,YAAY;AAAA,QACb,EAAC;AAEF,YAAI,KAAK,MAAM,CAAC,MAAM,GAAG;AACvB,qBAAW,QAAQ;AAAA,QACzB,OAAW;AACL,qBAAW,QAAQ,CAAC,GAAG,WAAW,OAAO,GAAG,QAAQ;AAAA,QACrD;AAGD,gBAAQ,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI;AAGnC,mBAAW,MAAM,CAAC,IAAI;AAAA,MACvB,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,WAAW,CAAC,aAAa;AAC7B,UAAI,CAAC,QAAQ,MAAM,QAAQ;AAAG;AAE9B,WAAK,MAAM,QAAQ;AACnB,UAAI,aAAa,GAAG;AAClB;MACJ,OAAS;AACL;MACD;AAAA,IACH;AAGA,UAAM,YAAY,CAAC,aAAa;AAC9B,iBAAW,MAAM,QAAQ,IAAI;AAC7B,WAAK,MAAM,QAAQ,IAAI;AACvB,cAAQ,MAAM,QAAQ,IAAI;AAE1B,UAAI,aAAa,GAAG;AAClB;MACJ,OAAS;AACL;MACD;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,SAAS;AAC3BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,2BAA2B,KAAK,EAAE,SAAS,KAAK,IAAI;AAAA,MAC7D,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,CAAC,SAAS;AACzBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,0BAA0B,KAAK,EAAE;AAAA,MAC1C,CAAG;AAAA,IACH;AAGA,UAAM,eAAe,CAAC,SAAS;AAC7B,WAAK,aAAa,CAAC,KAAK;AACxBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,KAAK,aAAa,QAAQ;AAAA,QACjC,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGAC,kBAAAA,UAAU,MAAM;AAEd,YAAM,UAAUD,oBAAI;AACpB,sBAAgB,QAAQ,QAAQ;AAChC,mBAAa,QAAQ,gBAAgB,QAAQ;AAG7C;AACA;IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/OD,GAAG,WAAWE,SAAe;"}