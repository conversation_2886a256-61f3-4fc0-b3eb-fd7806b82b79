"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_format = require("../../utils/format.js");
const utils_redPacket = require("../../utils/redPacket.js");
const utils_share = require("../../utils/share.js");
const common_assets = require("../../common/assets.js");
const utils_date = require("../../utils/date.js");
const _sfc_main$3 = {
  name: "RedPacketCard",
  props: {
    redPacket: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      showAnimation: false
    };
  },
  computed: {
    // 是否已过期
    isExpired() {
      return Date.now() > this.redPacket.expireTime;
    }
  },
  methods: {
    // 处理点击
    handleClick() {
      this.$emit("view", this.redPacket);
    },
    // 处理抢红包
    handleGrab() {
      if (this.isExpired)
        return;
      this.showAnimation = true;
      setTimeout(() => {
        this.showAnimation = false;
        this.$emit("grab", this.redPacket);
      }, 1e3);
    },
    // 格式化金额
    formatAmount: utils_format.formatAmount,
    // 格式化剩余时间
    formatRemainTime(expireTime) {
      const now = Date.now();
      const remain = expireTime - now;
      if (remain <= 0)
        return "已过期";
      const hours = Math.floor(remain / (60 * 60 * 1e3));
      const minutes = Math.floor(remain % (60 * 60 * 1e3) / (60 * 1e3));
      if (hours > 0) {
        return `剩余${hours}小时${minutes}分钟`;
      } else {
        return `剩余${minutes}分钟`;
      }
    },
    // 获取红包类型文本
    getTypeText(type) {
      const typeMap = {
        [utils_redPacket.RED_PACKET_TYPE.RANDOM]: "拼手气红包",
        [utils_redPacket.RED_PACKET_TYPE.FIXED]: "普通红包"
      };
      return typeMap[type] || "红包";
    },
    async handleShare() {
      try {
        await utils_share.showShareMenu({
          type: "redPacket",
          data: this.redPacket
        });
        common_vendor.index.showToast({
          title: "分享成功",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.showToast({
          title: "分享失败",
          icon: "error"
        });
      }
    }
  }
};
function _sfc_render$3(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0,
    b: $props.redPacket.sender.avatar,
    c: common_vendor.t($props.redPacket.sender.nickname),
    d: common_vendor.t($props.redPacket.title),
    e: !$options.isExpired
  }, !$options.isExpired ? {
    f: common_vendor.t($options.formatAmount($props.redPacket.remainAmount))
  } : {}, {
    g: $props.redPacket.receivedCount / $props.redPacket.totalCount * 100 + "%",
    h: common_vendor.t($props.redPacket.receivedCount),
    i: common_vendor.t($props.redPacket.totalCount),
    j: common_vendor.t($options.formatRemainTime($props.redPacket.expireTime)),
    k: common_vendor.t($options.getTypeText($props.redPacket.type)),
    l: common_vendor.t($options.isExpired ? "已过期" : "抢红包"),
    m: $options.isExpired ? 1 : "",
    n: common_vendor.o((...args) => $options.handleGrab && $options.handleGrab(...args)),
    o: $options.isExpired,
    p: common_vendor.o((...args) => $options.handleClick && $options.handleClick(...args)),
    q: $data.showAnimation
  }, $data.showAnimation ? {
    r: common_assets._imports_1
  } : {}, {
    s: $options.isExpired ? 1 : ""
  });
}
const RedPacketCard = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main$3, [["render", _sfc_render$3]]);
const _sfc_main$2 = {
  name: "RedPacketPopup",
  props: {
    // 是否显示弹窗
    show: {
      type: Boolean,
      default: false
    },
    // 红包数据
    redPacket: {
      type: Object,
      default: () => ({})
    },
    // 红包记录
    records: {
      type: Array,
      default: () => []
    },
    // 初始步骤
    initialStep: {
      type: String,
      default: "grab"
      // grab, opening, result, detail
    }
  },
  data() {
    return {
      currentStep: "grab",
      grabResult: {
        status: "",
        amount: 0,
        message: ""
      }
    };
  },
  watch: {
    show(val) {
      if (val) {
        this.currentStep = this.initialStep;
      }
    },
    initialStep(val) {
      if (this.show) {
        this.currentStep = val;
      }
    }
  },
  methods: {
    // 关闭弹窗
    handleClose() {
      this.$emit("close");
    },
    // 打开红包
    openRedPacket() {
      this.currentStep = "opening";
      utils_redPacket.grabRedPacket(this.redPacket.id).then((res) => {
        setTimeout(() => {
          this.grabResult = {
            status: "success",
            amount: res.amount,
            message: ""
          };
          this.currentStep = "result";
          this.$emit("grab-success", res);
        }, 1500);
      }).catch((err) => {
        setTimeout(() => {
          this.grabResult = {
            status: "fail",
            amount: 0,
            message: err.message || "红包已抢完"
          };
          this.currentStep = "result";
        }, 1500);
      });
    },
    // 格式化金额
    formatAmount(amount) {
      return utils_redPacket.formatRedPacketAmount(amount);
    },
    // 格式化时间
    formatTime(timestamp) {
      return utils_date.formatDate(timestamp, "MM-DD HH:mm");
    },
    // 跳转到钱包
    goToWallet() {
      common_vendor.index.navigateTo({
        url: "/subPackages/payment/pages/wallet"
      });
      this.handleClose();
    }
  }
};
function _sfc_render$2(_ctx, _cache, $props, $setup, $data, $options) {
  var _a, _b, _c, _d, _e, _f;
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.handleClose && $options.handleClose(...args)),
    b: common_vendor.o((...args) => $options.handleClose && $options.handleClose(...args)),
    c: $data.currentStep === "grab"
  }, $data.currentStep === "grab" ? {
    d: ((_a = $props.redPacket.sender) == null ? void 0 : _a.avatar) || "/static/images/default-avatar.png",
    e: common_vendor.t(((_b = $props.redPacket.sender) == null ? void 0 : _b.nickname) || "用户"),
    f: common_vendor.t($props.redPacket.title || "恭喜发财，大吉大利"),
    g: common_assets._imports_0$1,
    h: common_vendor.o((...args) => $options.openRedPacket && $options.openRedPacket(...args))
  } : $data.currentStep === "opening" ? {
    j: common_assets._imports_1$1
  } : $data.currentStep === "result" ? common_vendor.e({
    l: ((_c = $props.redPacket.sender) == null ? void 0 : _c.avatar) || "/static/images/default-avatar.png",
    m: common_vendor.t(((_d = $props.redPacket.sender) == null ? void 0 : _d.nickname) || "用户"),
    n: common_vendor.t($props.redPacket.title || "恭喜发财，大吉大利"),
    o: $data.grabResult.status === "success"
  }, $data.grabResult.status === "success" ? {
    p: common_assets._imports_2,
    q: common_vendor.t($options.formatAmount($data.grabResult.amount))
  } : $data.grabResult.status === "fail" ? {
    s: common_assets._imports_3,
    t: common_vendor.t($data.grabResult.message)
  } : {}, {
    r: $data.grabResult.status === "fail",
    v: $data.grabResult.status === "success"
  }, $data.grabResult.status === "success" ? {
    w: common_vendor.o((...args) => $options.goToWallet && $options.goToWallet(...args))
  } : {
    x: common_vendor.o((...args) => $options.handleClose && $options.handleClose(...args))
  }) : $data.currentStep === "detail" ? common_vendor.e({
    z: ((_e = $props.redPacket.sender) == null ? void 0 : _e.avatar) || "/static/images/default-avatar.png",
    A: common_vendor.t(((_f = $props.redPacket.sender) == null ? void 0 : _f.nickname) || "用户"),
    B: common_vendor.t($props.redPacket.title || "恭喜发财，大吉大利"),
    C: common_vendor.t($props.redPacket.totalCount - $props.redPacket.remainCount || 0),
    D: common_vendor.t($props.redPacket.totalCount || 0),
    E: common_vendor.t($options.formatAmount($props.redPacket.totalAmount - $props.redPacket.remainAmount)),
    F: common_vendor.t($options.formatAmount($props.redPacket.totalAmount)),
    G: $props.records && $props.records.length
  }, $props.records && $props.records.length ? {
    H: common_vendor.f($props.records, (record, index, i0) => {
      return common_vendor.e({
        a: record.avatar || "/static/images/default-avatar.png",
        b: common_vendor.t(record.nickname || "用户"),
        c: common_vendor.t($options.formatAmount(record.amount)),
        d: record.isBest
      }, record.isBest ? {} : {}, {
        e: common_vendor.t($options.formatTime(record.grabTime)),
        f: index
      });
    })
  } : {}, {
    I: common_vendor.o((...args) => $options.handleClose && $options.handleClose(...args))
  }) : {}, {
    i: $data.currentStep === "opening",
    k: $data.currentStep === "result",
    y: $data.currentStep === "detail",
    J: $props.show ? 1 : "",
    K: $props.show ? 1 : ""
  });
}
const RedPacketPopup = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main$2, [["render", _sfc_render$2]]);
const _sfc_main$1 = {
  name: "RedPacketCreator",
  props: {
    // 用户余额（单位：分）
    userBalance: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      // 表单数据
      formData: {
        type: utils_redPacket.RED_PACKET_TYPE.RANDOM,
        // 默认为拼手气红包
        amount: "",
        // 红包金额（元）
        count: 3,
        // 红包个数
        blessing: "恭喜发财，大吉大利",
        // 祝福语
        expireHours: 24
        // 过期时间（小时）
      },
      // 红包类型列表
      packetTypes: [
        {
          name: "拼手气红包",
          value: utils_redPacket.RED_PACKET_TYPE.RANDOM,
          icon: "/static/images/random-packet-icon.png"
        },
        {
          name: "普通红包",
          value: utils_redPacket.RED_PACKET_TYPE.FIXED,
          icon: "/static/images/fixed-packet-icon.png"
        }
      ],
      // 过期时间选项
      expireOptions: [
        { label: "24小时", value: 24 },
        { label: "48小时", value: 48 },
        { label: "72小时", value: 72 }
      ],
      loading: false
      // 提交加载状态
    };
  },
  computed: {
    // 红包总金额（分）
    totalAmountInCents() {
      const amount = parseFloat(this.formData.amount || 0);
      return Math.floor(amount * 100);
    },
    // 拼手气红包每人最多可得金额
    maxAmountPerPerson() {
      if (!this.formData.count || this.formData.count <= 0)
        return 0;
      return this.totalAmountInCents;
    },
    // 普通红包每人固定金额
    fixedAmountPerPerson() {
      if (!this.formData.count || this.formData.count <= 0)
        return 0;
      return Math.floor(this.totalAmountInCents / this.formData.count);
    },
    // 是否余额不足
    insufficientBalance() {
      return this.totalAmountInCents > this.userBalance;
    },
    // 是否可以提交
    canSubmit() {
      return this.totalAmountInCents > 0 && this.formData.count > 0 && !this.insufficientBalance;
    }
  },
  methods: {
    // 格式化金额
    formatAmount(amount) {
      return utils_redPacket.formatRedPacketAmount(amount);
    },
    // 校验金额
    validateAmount() {
      if (this.formData.amount === "")
        return;
      let amount = parseFloat(this.formData.amount);
      if (amount < 0.01) {
        amount = 0.01;
      }
      const maxAmount = this.formatAmount(this.userBalance);
      if (amount > parseFloat(maxAmount)) {
        amount = parseFloat(maxAmount);
      }
      this.formData.amount = amount.toFixed(2);
    },
    // 校验红包个数
    validateCount() {
      if (!this.formData.count) {
        this.formData.count = 1;
        return;
      }
      let count = parseInt(this.formData.count);
      if (count < 1) {
        count = 1;
      }
      if (count > 100) {
        count = 100;
      }
      this.formData.count = count;
    },
    // 减少红包个数
    decreaseCount() {
      if (this.formData.count > 1) {
        this.formData.count--;
      }
    },
    // 增加红包个数
    increaseCount() {
      if (this.formData.count < 100) {
        this.formData.count++;
      }
    },
    // 提交创建红包
    handleSubmit() {
      if (!this.canSubmit || this.loading)
        return;
      this.loading = true;
      const params = {
        type: this.formData.type,
        totalAmount: this.totalAmountInCents,
        totalCount: this.formData.count,
        title: this.formData.blessing,
        expireTime: Date.now() + this.formData.expireHours * 60 * 60 * 1e3
      };
      utils_redPacket.createRedPacket(params).then((res) => {
        this.$emit("create-success", res);
        this.resetForm();
      }).catch((err) => {
        common_vendor.index.showToast({
          title: err.message || "创建红包失败",
          icon: "none"
        });
      }).finally(() => {
        this.loading = false;
      });
    },
    // 重置表单
    resetForm() {
      this.formData = {
        type: utils_redPacket.RED_PACKET_TYPE.RANDOM,
        amount: "",
        count: 3,
        blessing: "恭喜发财，大吉大利",
        expireHours: 24
      };
    }
  }
};
function _sfc_render$1(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.packetTypes, (type, index, i0) => {
      return {
        a: type.icon,
        b: common_vendor.t(type.name),
        c: index,
        d: $data.formData.type === type.value ? 1 : "",
        e: common_vendor.o(($event) => $data.formData.type = type.value, index)
      };
    }),
    b: common_vendor.o([($event) => $data.formData.amount = $event.detail.value, (...args) => $options.validateAmount && $options.validateAmount(...args)]),
    c: $data.formData.amount,
    d: common_vendor.t($options.formatAmount($props.userBalance)),
    e: $data.formData.type === 1
  }, $data.formData.type === 1 ? {
    f: common_vendor.o((...args) => $options.decreaseCount && $options.decreaseCount(...args)),
    g: common_vendor.o([($event) => $data.formData.count = $event.detail.value, (...args) => $options.validateCount && $options.validateCount(...args)]),
    h: $data.formData.count,
    i: common_vendor.o((...args) => $options.increaseCount && $options.increaseCount(...args)),
    j: common_vendor.t($options.formatAmount($options.maxAmountPerPerson))
  } : $data.formData.type === 2 ? {
    l: common_vendor.o((...args) => $options.decreaseCount && $options.decreaseCount(...args)),
    m: common_vendor.o([($event) => $data.formData.count = $event.detail.value, (...args) => $options.validateCount && $options.validateCount(...args)]),
    n: $data.formData.count,
    o: common_vendor.o((...args) => $options.increaseCount && $options.increaseCount(...args)),
    p: common_vendor.t($options.formatAmount($options.fixedAmountPerPerson))
  } : {}, {
    k: $data.formData.type === 2,
    q: $data.formData.blessing,
    r: common_vendor.o(($event) => $data.formData.blessing = $event.detail.value),
    s: common_vendor.t($data.formData.blessing.length),
    t: common_vendor.f($data.expireOptions, (expire, index, i0) => {
      return {
        a: common_vendor.t(expire.label),
        b: index,
        c: $data.formData.expireHours === expire.value ? 1 : "",
        d: common_vendor.o(($event) => $data.formData.expireHours = expire.value, index)
      };
    }),
    v: $options.insufficientBalance
  }, $options.insufficientBalance ? {} : {}, {
    w: common_vendor.t($data.loading ? "创建中..." : "塞入红包"),
    x: !$options.canSubmit || $data.loading,
    y: $data.loading,
    z: common_vendor.o((...args) => $options.handleSubmit && $options.handleSubmit(...args))
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main$1, [["render", _sfc_render$1]]);
const RedPacketCreator = () => "./RedPacketCreator.js";
const _sfc_main = {
  name: "RedPacketSelector",
  components: {
    RedPacketCreator
  },
  props: {
    // 用户余额（单位：分）
    userBalance: {
      type: Number,
      default: 0
    },
    // 已选择的红包
    value: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      showPopup: false,
      selectedRedPacket: null
    };
  },
  watch: {
    value: {
      handler(val) {
        this.selectedRedPacket = val;
      },
      immediate: true
    }
  },
  methods: {
    // 格式化金额
    formatAmount(amount) {
      return utils_redPacket.formatRedPacketAmount(amount);
    },
    // 创建红包成功
    handleCreateSuccess(redPacket) {
      this.selectedRedPacket = redPacket;
      this.$emit("input", redPacket);
      this.showPopup = false;
      common_vendor.index.showToast({
        title: "红包添加成功",
        icon: "success"
      });
    }
  }
};
if (!Array) {
  const _component_red_packet_creator = common_vendor.resolveComponent("red-packet-creator");
  _component_red_packet_creator();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$2,
    b: !$data.selectedRedPacket
  }, !$data.selectedRedPacket ? {} : {
    c: common_vendor.t($options.formatAmount($data.selectedRedPacket.totalAmount))
  }, {
    d: common_vendor.o(($event) => $data.showPopup = true),
    e: $data.showPopup
  }, $data.showPopup ? {
    f: common_vendor.o(($event) => $data.showPopup = false),
    g: common_vendor.o(($event) => $data.showPopup = false),
    h: common_vendor.o($options.handleCreateSuccess),
    i: common_vendor.p({
      userBalance: $props.userBalance
    })
  } : {});
}
const RedPacketSelector = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
const install = function(Vue) {
  if (install.installed)
    return;
  install.installed = true;
  Vue.component(RedPacketCard.name, RedPacketCard);
  Vue.component(RedPacketPopup.name, RedPacketPopup);
  Vue.component(Component.name, Component);
  Vue.component(RedPacketSelector.name, RedPacketSelector);
};
if (typeof window !== "undefined" && window.Vue) {
  install(window.Vue);
}
const RedPacketComponents = {
  install
};
exports.Component = Component;
exports.RedPacketComponents = RedPacketComponents;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/RedPacket/index.js.map
