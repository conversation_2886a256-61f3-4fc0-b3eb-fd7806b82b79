"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      levels: [],
      // 会员等级列表
      levelForm: {
        id: "",
        name: "",
        color: "#8A2BE2",
        // 默认紫色
        icon: "",
        growthRequired: "",
        discount: "",
        pointsMultiplier: "",
        description: ""
      },
      isEditing: false,
      // 是否为编辑模式
      currentLevelId: null,
      // 当前编辑的等级ID
      colorOptions: [
        "#8A2BE2",
        // 紫色
        "#FF4500",
        // 橙红色
        "#1E90FF",
        // 道奇蓝
        "#32CD32",
        // 酸橙绿
        "#FFD700",
        // 金色
        "#FF69B4",
        // 热粉红
        "#20B2AA",
        // 浅海绿
        "#FF8C00"
        // 深橙色
      ]
    };
  },
  onLoad() {
    this.fetchLevels();
  },
  methods: {
    // 获取会员等级列表
    fetchLevels() {
      this.levels = [
        {
          id: "1",
          name: "普通会员",
          color: "#8A2BE2",
          icon: "/static/images/level-normal.svg",
          growthRequired: 0,
          discount: 10,
          pointsMultiplier: 1,
          description: "基础会员权益"
        },
        {
          id: "2",
          name: "银卡会员",
          color: "#1E90FF",
          icon: "/static/images/level-silver.svg",
          growthRequired: 1e3,
          discount: 9.5,
          pointsMultiplier: 1.2,
          description: "享受9.5折优惠，积分1.2倍"
        },
        {
          id: "3",
          name: "金卡会员",
          color: "#FFD700",
          icon: "/static/images/level-gold.svg",
          growthRequired: 3e3,
          discount: 9,
          pointsMultiplier: 1.5,
          description: "享受9折优惠，积分1.5倍，生日礼包"
        }
      ];
    },
    // 显示添加等级弹窗
    showAddLevelModal() {
      this.isEditing = false;
      this.resetLevelForm();
      this.$refs.levelFormPopup.open();
    },
    // 编辑等级
    editLevel(level) {
      this.isEditing = true;
      this.currentLevelId = level.id;
      this.levelForm = { ...level };
      this.$refs.levelFormPopup.open();
    },
    // 关闭等级表单弹窗
    closeLevelModal() {
      this.$refs.levelFormPopup.close();
      this.resetLevelForm();
    },
    // 重置表单
    resetLevelForm() {
      this.levelForm = {
        id: "",
        name: "",
        color: "#8A2BE2",
        icon: "",
        growthRequired: "",
        discount: "",
        pointsMultiplier: "",
        description: ""
      };
      this.currentLevelId = null;
    },
    // 选择图标
    chooseIcon() {
      common_vendor.index.chooseImage({
        count: 1,
        success: (res) => {
          this.levelForm.icon = res.tempFilePaths[0];
        }
      });
    },
    // 保存等级表单
    saveLevelForm() {
      if (!this.levelForm.name) {
        common_vendor.index.showToast({
          title: "请输入等级名称",
          icon: "none"
        });
        return;
      }
      if (!this.levelForm.growthRequired && this.levelForm.growthRequired !== 0) {
        common_vendor.index.showToast({
          title: "请输入所需成长值",
          icon: "none"
        });
        return;
      }
      if (!this.levelForm.discount) {
        common_vendor.index.showToast({
          title: "请输入折扣权益",
          icon: "none"
        });
        return;
      }
      if (!this.levelForm.pointsMultiplier) {
        common_vendor.index.showToast({
          title: "请输入积分倍率",
          icon: "none"
        });
        return;
      }
      if (this.isEditing) {
        const index = this.levels.findIndex((item) => item.id === this.currentLevelId);
        if (index !== -1) {
          this.levels[index] = { ...this.levelForm, id: this.currentLevelId };
        }
      } else {
        const newLevel = {
          ...this.levelForm,
          id: Date.now().toString()
          // 生成临时ID，实际项目中应由后端生成
        };
        this.levels.push(newLevel);
      }
      this.$refs.levelFormPopup.close();
      this.resetLevelForm();
      common_vendor.index.showToast({
        title: this.isEditing ? "编辑成功" : "添加成功",
        icon: "success"
      });
    },
    // 确认删除等级
    confirmDeleteLevel(level) {
      this.currentLevelId = level.id;
      this.$refs.deleteConfirmPopup.open();
    },
    // 关闭删除确认弹窗
    closeDeleteConfirm() {
      this.$refs.deleteConfirmPopup.close();
    },
    // 删除等级
    deleteLevel() {
      const index = this.levels.findIndex((item) => item.id === this.currentLevelId);
      if (index !== -1) {
        this.levels.splice(index, 1);
        common_vendor.index.showToast({
          title: "删除成功",
          icon: "success"
        });
      }
      this.currentLevelId = null;
    }
  }
};
if (!Array) {
  const _component_uni_popup = common_vendor.resolveComponent("uni-popup");
  const _component_uni_popup_dialog = common_vendor.resolveComponent("uni-popup-dialog");
  (_component_uni_popup + _component_uni_popup_dialog)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.showAddLevelModal && $options.showAddLevelModal(...args)),
    b: $data.levels.length === 0
  }, $data.levels.length === 0 ? {
    c: common_assets._imports_0$46
  } : {
    d: common_vendor.f($data.levels, (level, index, i0) => {
      return {
        a: common_vendor.t(level.name),
        b: common_vendor.o(($event) => $options.editLevel(level), index),
        c: common_vendor.o(($event) => $options.confirmDeleteLevel(level), index),
        d: level.color,
        e: level.icon,
        f: common_vendor.t(level.growthRequired),
        g: common_vendor.t(level.discount),
        h: common_vendor.t(level.pointsMultiplier),
        i: common_vendor.t(level.description || "暂无特权说明"),
        j: index
      };
    })
  }, {
    e: common_vendor.t($data.isEditing ? "编辑等级" : "添加等级"),
    f: common_vendor.o((...args) => $options.closeLevelModal && $options.closeLevelModal(...args)),
    g: $data.levelForm.name,
    h: common_vendor.o(($event) => $data.levelForm.name = $event.detail.value),
    i: common_vendor.f($data.colorOptions, (color, idx, i0) => {
      return {
        a: idx,
        b: $data.levelForm.color === color ? 1 : "",
        c: color,
        d: common_vendor.o(($event) => $data.levelForm.color = color, idx)
      };
    }),
    j: $data.levelForm.icon
  }, $data.levelForm.icon ? {
    k: $data.levelForm.icon
  } : {
    l: common_vendor.o((...args) => $options.chooseIcon && $options.chooseIcon(...args))
  }, {
    m: $data.levelForm.growthRequired,
    n: common_vendor.o(($event) => $data.levelForm.growthRequired = $event.detail.value),
    o: $data.levelForm.discount,
    p: common_vendor.o(($event) => $data.levelForm.discount = $event.detail.value),
    q: $data.levelForm.pointsMultiplier,
    r: common_vendor.o(($event) => $data.levelForm.pointsMultiplier = $event.detail.value),
    s: $data.levelForm.description,
    t: common_vendor.o(($event) => $data.levelForm.description = $event.detail.value),
    v: common_vendor.o((...args) => $options.closeLevelModal && $options.closeLevelModal(...args)),
    w: common_vendor.o((...args) => $options.saveLevelForm && $options.saveLevelForm(...args)),
    x: common_vendor.sr("levelFormPopup", "5251c962-0"),
    y: common_vendor.p({
      type: "center"
    }),
    z: common_vendor.o($options.deleteLevel),
    A: common_vendor.o($options.closeDeleteConfirm),
    B: common_vendor.p({
      type: "warning",
      title: "删除确认",
      content: "确定要删除该会员等级吗？删除后将无法恢复。",
      ["before-close"]: true
    }),
    C: common_vendor.sr("deleteConfirmPopup", "5251c962-1"),
    D: common_vendor.p({
      type: "dialog"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/member/member-level.js.map
