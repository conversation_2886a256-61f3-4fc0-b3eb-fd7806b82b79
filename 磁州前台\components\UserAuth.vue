<template>
  <view class="user-auth">
    <!-- 微信小程序环境 -->
    <block v-if="mpWeixin">
      <button v-if="!hasUserInfo" class="auth-btn" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
        <text class="auth-btn-text">{{ buttonText }}</text>
      </button>
      <slot v-else></slot>
    </block>
    
    <!-- 非微信小程序环境 -->
    <block v-else>
      <button v-if="!hasUserInfo" class="auth-btn" @click="mockAuthorize">
        <text class="auth-btn-text">{{ buttonText }}</text>
      </button>
      <slot v-else></slot>
    </block>
    
    <!-- 昵称输入弹窗 -->
    <view class="nickname-popup" v-if="showNicknamePopup">
      <view class="nickname-container">
        <view class="nickname-header">
          <text class="nickname-title">设置昵称</text>
        </view>
        <view class="nickname-content">
          <text class="nickname-tip">请设置您的昵称</text>
          <input class="nickname-input" v-model="nickname" placeholder="请输入昵称" maxlength="12" />
        </view>
        <view class="nickname-footer">
          <button class="cancel-btn" @click="cancelNickname">取消</button>
          <button class="confirm-btn" @click="confirmNickname">确定</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { hasUserInfo as checkUserInfo, updateUserProfile, getLocalUserInfo } from '@/utils/userProfile.js';

// 定义props
const props = defineProps({
  buttonText: {
    type: String,
    default: '获取头像昵称'
  },
  autoAuth: {
    type: Boolean,
    default: true // 默认开启自动授权
  }
});

// 定义事件
const emit = defineEmits(['update:userInfo']);

// 环境检测
const mpWeixin = ref(false);
// 是否已有用户信息
const userInfoExists = ref(false);
// 是否显示昵称输入弹窗
const showNicknamePopup = ref(false);
// 用户选择的头像
const selectedAvatar = ref('');
// 用户输入的昵称
const nickname = ref('');

// 计算是否已有用户信息
const hasUserInfo = computed(() => {
  return userInfoExists.value;
});

// 选择头像回调
const onChooseAvatar = (e) => {
  const avatarUrl = e.detail.avatarUrl;
  selectedAvatar.value = avatarUrl;
  
  // 上传到临时目录
  uni.uploadFile({
    url: 'https://your-api-domain/api/upload/avatar',
    filePath: avatarUrl,
    name: 'file',
    success: (uploadRes) => {
      // 实际项目中，这里应该获取服务器返回的URL
      // 示例中使用本地临时文件路径
      // 打开昵称输入弹窗
      showNicknamePopup.value = true;
    },
    fail: () => {
      // 上传失败时直接使用临时路径
      showNicknamePopup.value = true;
    }
  });
};

// 确认昵称
const confirmNickname = async () => {
  if (!nickname.value.trim()) {
    uni.showToast({
      title: '昵称不能为空',
      icon: 'none'
    });
    return;
  }
  
  // 更新用户信息
  const updatedUser = await updateUserProfile({
    avatarUrl: selectedAvatar.value,
    nickName: nickname.value
  });
  
  if (updatedUser) {
    userInfoExists.value = true;
    showNicknamePopup.value = false;
    
    // 通知父组件
    emit('update:userInfo', updatedUser);
    
    uni.showToast({
      title: '设置成功',
      icon: 'success'
    });
  } else {
    uni.showToast({
      title: '设置失败，请重试',
      icon: 'none'
    });
  }
};

// 自动获取用户信息
const autoGetUserInfo = () => {
  // 只在微信环境下执行
  if (!mpWeixin.value) return;
  
  // 如果已有用户信息则不执行
  if (userInfoExists.value) return;
  
  // 小程序环境下，使用原生接口获取用户信息
  // #ifdef MP-WEIXIN
  // 首先尝试通过getUserProfile获取用户基本信息
  uni.getUserProfile({
    desc: '用于完善用户资料',
    success: (res) => {
      // 保存获取到的昵称
      nickname.value = res.userInfo.nickName || '用户' + Math.floor(Math.random() * 10000);
      
      // 由于头像需要用户主动授权，这里使用默认头像，
      // 并且提示用户点击"获取头像"按钮来选择头像
      selectedAvatar.value = '/static/images/default-avatar.png';
      
      // 自动更新用户信息
      updateUserProfile({
        avatarUrl: selectedAvatar.value,
        nickName: nickname.value
      }).then(updatedUser => {
        if (updatedUser) {
          userInfoExists.value = true;
          // 通知父组件
          emit('update:userInfo', updatedUser);
          
          // 提示用户可以更新头像
          uni.showToast({
            title: '已获取基本信息，点击更换头像',
            icon: 'none',
            duration: 2000
          });
        }
      });
    },
    fail: (err) => {
      console.log('获取用户信息失败', err);
      
      // 失败时，提示用户手动授权
      uni.showToast({
        title: '请点击按钮授权获取头像昵称',
        icon: 'none',
        duration: 2000
      });
    }
  });
  // #endif
  
  // 非微信小程序环境
  // #ifndef MP-WEIXIN
  mockAuthorize();
  // #endif
};

// 取消设置昵称
const cancelNickname = () => {
  showNicknamePopup.value = false;
  selectedAvatar.value = '';
  nickname.value = '';
};

// 模拟授权（非微信小程序环境下使用）
const mockAuthorize = () => {
  uni.showModal({
    title: '提示',
    content: '非微信小程序环境，点击确定模拟授权',
    success: async (res) => {
      if (res.confirm) {
        // 模拟授权成功
        const updatedUser = await updateUserProfile({
          avatarUrl: '/static/images/default-avatar.png',
          nickName: '测试用户' + Math.floor(Math.random() * 1000)
        });
        
        if (updatedUser) {
          userInfoExists.value = true;
          // 通知父组件
          emit('update:userInfo', updatedUser);
        }
      }
    }
  });
};

onMounted(() => {
  // 检查环境
  // #ifdef MP-WEIXIN
  mpWeixin.value = true;
  // #endif
  
  // 检查是否有用户信息
  userInfoExists.value = checkUserInfo();
  
  // 如果已有用户信息，通知父组件
  if (userInfoExists.value) {
    emit('update:userInfo', getLocalUserInfo());
  } else if (props.autoAuth) {
    // 如果开启了自动授权且没有用户信息，自动请求授权
    autoGetUserInfo();
  }
});
</script>

<style lang="scss">
.user-auth {
  width: 100%;
}

.auth-btn {
  background-color: #07c160;
  color: #ffffff;
  border-radius: 44rpx;
  font-size: 28rpx;
  height: 88rpx;
  line-height: 88rpx;
  width: 100%;
  text-align: center;
  padding: 0;
  margin: 0;
  border: none;
}

.auth-btn::after {
  border: none;
}

.auth-btn-text {
  font-size: 28rpx;
  font-weight: 500;
}

.nickname-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nickname-container {
  width: 560rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
}

.nickname-header {
  padding: 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #eee;
}

.nickname-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.nickname-content {
  padding: 30rpx;
}

.nickname-tip {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 20rpx;
}

.nickname-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.nickname-footer {
  display: flex;
  border-top: 1rpx solid #eee;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-size: 30rpx;
  margin: 0;
  padding: 0;
  border-radius: 0;
  background-color: #fff;
}

.cancel-btn {
  color: #666;
  border-right: 1rpx solid #eee;
}

.confirm-btn {
  color: #07c160;
  font-weight: 500;
}

.cancel-btn::after, .confirm-btn::after {
  border: none;
}
</style> 