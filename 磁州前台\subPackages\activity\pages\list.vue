<template>
	<view class="page-container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left">
					<view class="back-button" @tap="goBack">
						<svg class="back-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M15 18L9 12L15 6" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
						</svg>
					</view>
				</view>
				<view class="navbar-title">
					<text class="title-text">活动列表</text>
				</view>
				<view class="navbar-right">
					<view class="filter-button" @tap="showFilterOptions">
						<svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M3 6H21M6 12H18M10 18H14" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
						</svg>
					</view>
				</view>
			</view>
		</view>

		<!-- 状态筛选 -->
		<view class="filter-tabs">
			<view class="tab-item" :class="{ active: currentStatus === 'all' }" @tap="filterByStatus('all')">
				<text class="tab-text">全部</text>
			</view>
			<view class="tab-item" :class="{ active: currentStatus === '进行中' }" @tap="filterByStatus('进行中')">
				<text class="tab-text">进行中</text>
			</view>
			<view class="tab-item" :class="{ active: currentStatus === '未开始' }" @tap="filterByStatus('未开始')">
				<text class="tab-text">未开始</text>
			</view>
			<view class="tab-item" :class="{ active: currentStatus === '已结束' }" @tap="filterByStatus('已结束')">
				<text class="tab-text">已结束</text>
			</view>
		</view>

		<!-- 活动列表 -->
		<scroll-view class="activity-list" scroll-y="true" @scrolltolower="loadMore">
			<template v-if="filteredActivities.length === 0">
				<view class="empty-state">
					<svg class="empty-icon" width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
						<path d="M9 9H9.01" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
						<path d="M15 9H15.01" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
						<path d="M8 14H16" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
					</svg>
					<text class="empty-text">暂无活动信息</text>
				</view>
			</template>
			
			<template v-else>
				<view 
					class="activity-card" 
					v-for="(activity, index) in filteredActivities" 
					:key="index"
					@tap="viewActivityDetail(activity.id)"
				>
					<view class="card-image" :style="{ backgroundImage: `url(${activity.coverImage})` }"></view>
					<view class="card-content">
						<text class="card-title">{{ activity.title }}</text>
						<view class="card-info">
							<view class="info-item">
								<svg class="info-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path d="M12 8V12L15 15" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
									<path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
								</svg>
								<text class="info-text">{{ formatDate(activity.startTime) }} ~ {{ formatDate(activity.endTime) }}</text>
							</view>
							<view class="info-item">
								<svg class="info-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path d="M17 21V19C17 16.7909 15.2091 15 13 15H5C2.79086 15 1 16.7909 1 19V21" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
									<path d="M9 11C11.2091 11 13 9.20914 13 7C13 4.79086 11.2091 3 9 3C6.79086 3 5 4.79086 5 7C5 9.20914 6.79086 11 9 11Z" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
									<path d="M23 21V19C22.9986 17.1771 21.765 15.5857 20 15.13" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
									<path d="M16 3.13C17.7699 3.58317 19.0078 5.17799 19.0078 7.005C19.0078 8.83201 17.7699 10.4268 16 10.88" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
								</svg>
								<text class="info-text">{{ activity.participantsCount || 0 }}人参与</text>
							</view>
						</view>
						
						<view class="card-status" :class="{
							'status-ongoing': activity.status === '进行中',
							'status-upcoming': activity.status === '未开始',
							'status-ended': activity.status === '已结束'
						}">
							{{ activity.status }}
						</view>
						
						<view class="card-actions">
							<view class="action-button edit" @tap.stop="editActivity(activity.id)">
								<svg class="action-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path d="M11 4H4C2.89543 4 2 4.89543 2 6V20C2 21.1046 2.89543 22 4 22H18C19.1046 22 20 21.1046 20 20V13" stroke="#007AFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
									<path d="M18.5 2.5C19.3284 1.67157 20.6716 1.67157 21.5 2.5C22.3284 3.32843 22.3284 4.67157 21.5 5.5L12 15L8 16L9 12L18.5 2.5Z" stroke="#007AFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
								</svg>
								<text class="action-text">编辑</text>
							</view>
							
							<view class="action-button promote" @tap.stop="promoteActivity(activity.id)">
								<svg class="action-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="#FF9500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
									<path d="M7 10L12 15L17 10" stroke="#FF9500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
									<path d="M12 15V3" stroke="#FF9500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
								</svg>
								<text class="action-text">推广</text>
							</view>
							
							<view class="action-button share" @tap.stop="shareActivity(activity.id)">
								<svg class="action-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path d="M18 8C19.6569 8 21 6.65685 21 5C21 3.34315 19.6569 2 18 2C16.3431 2 15 3.34315 15 5C15 6.65685 16.3431 8 18 8Z" stroke="#34C759" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
									<path d="M6 15C7.65685 15 9 13.6569 9 12C9 10.3431 7.65685 9 6 9C4.34315 9 3 10.3431 3 12C3 13.6569 4.34315 15 6 15Z" stroke="#34C759" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
									<path d="M18 22C19.6569 22 21 20.6569 21 19C21 17.3431 19.6569 16 18 16C16.3431 16 15 17.3431 15 19C15 20.6569 16.3431 22 18 22Z" stroke="#34C759" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
									<path d="M8.59 13.51L15.42 17.49" stroke="#34C759" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
									<path d="M15.41 6.51L8.59 10.49" stroke="#34C759" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
								</svg>
								<text class="action-text">转发</text>
							</view>
						</view>
					</view>
				</view>
			</template>
			
			<view class="loading-container" v-if="loading">
				<text class="loading-text">加载中...</text>
			</view>
			
			<view class="no-more" v-if="!loading && !hasMore && filteredActivities.length > 0">
				<text>没有更多数据了</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
	// 页面配置
	navigationStyle: 'custom'
}
</script>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue';
import { onLoad, onShow } from '@dcloudio/uni-app';

// 状态栏高度
const statusBarHeight = ref(20);

// 刷新状态
const isRefreshing = ref(false);

// 当前筛选状态
const currentStatus = ref('all');

// 活动数据
const activityList = reactive([
					{
						id: 1,
						title: '磁州美食节',
		coverImage: '/static/images/banner/banner-1.png',
		startTime: '2024-03-20',
		endTime: '2024-03-25',
						location: '磁县人民广场',
						description: '汇聚磁州各地特色美食，带您品尝地道磁州味道',
		status: '进行中',
		participantsCount: 128,
		viewsCount: 3560,
		commentsCount: 42
					},
					{
						id: 2,
						title: '春季农产品展销会',
		coverImage: '/static/images/banner/banner-2.png',
		startTime: '2024-03-28',
		endTime: '2024-03-30',
						location: '磁县会展中心',
						description: '展示磁州特色农产品，促进农产品销售',
		status: '未开始',
		participantsCount: 89,
		viewsCount: 1253,
		commentsCount: 17
					},
					{
						id: 3,
						title: '磁州文化节',
		coverImage: '/static/images/banner/banner-3.jpg',
		startTime: '2024-04-01',
		endTime: '2024-04-07',
						location: '磁州古镇',
						description: '传承磁州文化，弘扬地方特色',
		status: '未开始',
		participantsCount: 65,
		viewsCount: 980,
		commentsCount: 8
	},
	{
		id: 4,
		title: '春季招聘会',
		coverImage: '/static/images/banner/banner-4.jpg',
		startTime: '2024-03-15',
		endTime: '2024-03-16',
		location: '磁县人才市场',
		description: '提供就业机会，促进人才交流',
		status: '已结束',
		participantsCount: 356,
		viewsCount: 4230,
		commentsCount: 76
	}
]);

// 加载和分页状态
const loading = ref(false);
const hasMore = ref(false);

// 格式化日期
const formatDate = (dateString) => {
	if (!dateString) return '';
	const date = new Date(dateString);
	return `${date.getMonth() + 1}.${date.getDate()}`;
};

// 计算筛选后的活动列表
const filteredActivities = computed(() => {
	if (currentStatus.value === 'all') {
		return activityList;
	}
	return activityList.filter(item => item.status === currentStatus.value);
});

// 页面加载
onLoad(() => {
	// 隐藏系统导航栏
	uni.hideNavigationBar();
});

// 页面显示
onShow(() => {
	// 确保每次显示时都隐藏系统导航栏
	uni.hideNavigationBar();
});

// 初始化
onMounted(() => {
	// 获取状态栏高度
	uni.getSystemInfo({
		success: (res) => {
			statusBarHeight.value = res.statusBarHeight;
		}
	});
});

// 返回上一页
const goBack = () => {
	uni.navigateBack();
};

// 显示筛选选项
const showFilterOptions = () => {
	uni.showActionSheet({
		itemList: ['按时间排序', '按热度排序', '按参与人数排序'],
		success: (res) => {
			uni.showToast({
				title: '排序功能已选择',
				icon: 'none'
			});
		}
	});
};

// 筛选活动
const filterByStatus = (status) => {
	currentStatus.value = status;
};

// 加载更多
const loadMore = () => {
	if (loading.value || !hasMore.value) return;
	loading.value = true;
	
	setTimeout(() => {
		loading.value = false;
	}, 1000);
};

// 查看活动详情
const viewActivityDetail = (id) => {
				uni.navigateTo({
					url: `/subPackages/activity/pages/detail?id=${id}`
	});
};

// 编辑活动
const editActivity = (id) => {
	uni.navigateTo({
		url: `/subPackages/activity/pages/edit?id=${id}`
	});
};

// 推广活动
const promoteActivity = (id) => {
	uni.showActionSheet({
		itemList: ['活动置顶', '首页推荐', '短信推广', '社群分享'],
		success: (res) => {
			const options = ['活动置顶', '首页推荐', '短信推广', '社群分享'];
			uni.showToast({
				title: `已选择${options[res.tapIndex]}`,
				icon: 'none'
			});
		}
	});
};

// 分享活动
const shareActivity = (id) => {
	uni.showShareMenu({
		withShareTicket: true,
		menus: ['shareAppMessage', 'shareTimeline']
	});
};
</script>

<style lang="scss" scoped>
.page-container {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100vh;
		background-color: #f5f5f5;
	position: relative;
	overflow: hidden;
}

/* 自定义导航栏 */
.custom-navbar {
	width: 100%;
	background: linear-gradient(to right, #3a8afe, #6f7bfd);
	z-index: 100;
	position: relative;
}

.status-bar {
	width: 100%;
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 44px;
	padding: 0 12px;
}

.navbar-left {
	display: flex;
	align-items: center;
}

.back-button {
	padding: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.back-icon {
	width: 20px;
	height: 20px;
}

.navbar-title {
	flex: 1;
	text-align: center;
}

.title-text {
	font-size: 18px;
	font-weight: 800;
	color: white;
}

.navbar-right {
	display: flex;
	align-items: center;
}

.filter-button {
	padding: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 状态筛选 */
.filter-tabs {
	display: flex;
	background-color: white;
	border-bottom: 1px solid #e0e0e0;
	z-index: 10;
	margin-bottom: 16px;
}

.tab-item {
	flex: 1;
	padding: 12px 0;
	text-align: center;
	position: relative;
}

.tab-text {
	font-size: 14px;
	color: #666;
}

.tab-item.active .tab-text {
	color: #3a8afe;
	font-weight: 500;
}

.tab-item.active::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	width: 20px;
	height: 3px;
	background-color: #3a8afe;
	border-radius: 3px;
}

/* 活动列表 */
	.activity-list {
	flex: 1;
	overflow-y: auto;
	padding: 0 16px;
}

.empty-state {
		display: flex;
		flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 100%;
	padding: 32px;
}

.empty-icon {
	width: 80px;
	height: 80px;
	margin-bottom: 16px;
}

.empty-text {
	font-size: 16px;
	color: #999;
	text-align: center;
}

/* 活动卡片 */
.activity-card {
	background-color: white;
	border-radius: 20px;
	margin-bottom: 20px;
	box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
		overflow: hidden;
	width: 90%;
	margin-left: auto;
	margin-right: auto;
	transition: transform 0.2s, box-shadow 0.2s;
}

.activity-card:active {
	transform: scale(0.98);
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.card-image {
		width: 100%;
	height: 180px;
	background-size: cover;
	background-position: center;
}

.card-content {
	padding: 18px;
}

.card-title {
	font-size: 18px;
	font-weight: 600;
		color: #333;
	margin-bottom: 10px;
	line-height: 1.4;
	text-align: center;
	}
	
.card-info {
		display: flex;
	align-items: center;
	justify-content: center;
	flex-wrap: wrap;
	margin-bottom: 12px;
}

.info-item {
		display: flex;
		align-items: center;
	margin: 0 10px 6px;
}

.info-icon {
	width: 16px;
	height: 16px;
	margin-right: 4px;
}

.info-text {
	font-size: 13px;
		color: #666;
	}
	
.card-status {
	display: block;
	padding: 6px 0;
	border-radius: 16px;
	font-size: 13px;
	font-weight: 500;
	text-align: center;
	width: 100px;
	margin: 0 auto 12px;
}

.status-ongoing {
	background-color: rgba(58, 138, 254, 0.1);
	color: #3a8afe;
}

.status-upcoming {
	background-color: rgba(111, 123, 253, 0.1);
	color: #6f7bfd;
	}
	
	.status-ended {
	background-color: rgba(128, 128, 128, 0.1);
	color: #808080;
}

.card-actions {
	display: flex;
	justify-content: space-around;
	margin-top: 12px;
	border-top: 1px solid #f0f0f0;
	padding-top: 12px;
}

.action-button {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 8px 20px;
	border-radius: 16px;
}

.action-button:active {
	background-color: rgba(0, 0, 0, 0.05);
}

.action-icon {
	width: 22px;
	height: 22px;
	margin-bottom: 6px;
}

.action-text {
	font-size: 12px;
	color: #666;
}

/* 加载状态 */
.loading-container {
	display: flex;
	justify-content: center;
	padding: 16px 0;
}

.loading-text {
	font-size: 14px;
	color: #999;
}

/* 没有更多数据提示 */
.no-more {
	text-align: center;
	padding: 16px 0;
	font-size: 14px;
	color: #999;
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
	display: none;
	width: 0 !important;
	height: 0 !important;
	-webkit-appearance: none;
	background: transparent;
	}
</style> 
