{"version": 3, "file": "add-privilege.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/member/add-privilege.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xtZW1iZXJcYWRkLXByaXZpbGVnZS52dWU"], "sourcesContent": ["<!-- 添加会员特权页面开始 -->\r\n<template>\r\n  <view class=\"add-privilege-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">添加会员特权</text>\r\n      <view class=\"navbar-right\"></view>\r\n    </view>\r\n    \r\n    <!-- 表单内容 -->\r\n    <view class=\"form-content\">\r\n      <!-- 基本信息 -->\r\n      <view class=\"section-card\">\r\n        <view class=\"section-title\">基本信息</view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">特权名称</text>\r\n          <input class=\"form-input\" v-model=\"privilegeForm.name\" placeholder=\"请输入特权名称\" maxlength=\"10\" />\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">特权描述</text>\r\n          <input class=\"form-input\" v-model=\"privilegeForm.description\" placeholder=\"请输入特权描述\" maxlength=\"20\" />\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">特权图标</text>\r\n          <view class=\"icon-upload\" @click=\"chooseIcon\">\r\n            <image class=\"preview-icon\" v-if=\"privilegeForm.icon\" :src=\"privilegeForm.icon\" mode=\"aspectFit\"></image>\r\n            <view class=\"upload-placeholder\" v-else>\r\n              <text class=\"upload-icon\">+</text>\r\n              <text class=\"upload-text\">上传图标</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">特权类型</text>\r\n          <picker mode=\"selector\" :range=\"privilegeTypes\" range-key=\"name\" @change=\"onTypeChange\">\r\n            <view class=\"picker-view\">\r\n              <text class=\"picker-text\">{{ getTypeName(privilegeForm.type) }}</text>\r\n              <view class=\"picker-arrow\"></view>\r\n            </view>\r\n          </picker>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 特权设置 -->\r\n      <view class=\"section-card\">\r\n        <view class=\"section-title\">特权设置</view>\r\n        \r\n        <view class=\"form-item switch-item\">\r\n          <text class=\"form-label\">默认启用</text>\r\n          <switch :checked=\"privilegeForm.enabled\" @change=\"toggleEnabled\" color=\"#FF6B22\" />\r\n        </view>\r\n        \r\n        <!-- 折扣特权设置 -->\r\n        <block v-if=\"privilegeForm.type === 'discount'\">\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">折扣比例</text>\r\n            <view class=\"form-input-group\">\r\n              <input type=\"digit\" class=\"form-input\" v-model=\"privilegeForm.discountValue\" placeholder=\"请输入折扣比例\" />\r\n              <text class=\"input-suffix\">折</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">适用范围</text>\r\n            <view class=\"radio-group\">\r\n              <view class=\"radio-item\" :class=\"{ active: privilegeForm.discountScope === 'all' }\" @click=\"setDiscountScope('all')\">\r\n                <text class=\"radio-text\">全部商品</text>\r\n              </view>\r\n              <view class=\"radio-item\" :class=\"{ active: privilegeForm.discountScope === 'category' }\" @click=\"setDiscountScope('category')\">\r\n                <text class=\"radio-text\">指定分类</text>\r\n              </view>\r\n              <view class=\"radio-item\" :class=\"{ active: privilegeForm.discountScope === 'product' }\" @click=\"setDiscountScope('product')\">\r\n                <text class=\"radio-text\">指定商品</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"form-item\" v-if=\"privilegeForm.discountScope !== 'all'\">\r\n            <button class=\"select-btn\" @click=\"selectItems\">选择{{ privilegeForm.discountScope === 'category' ? '分类' : '商品' }}</button>\r\n          </view>\r\n        </block>\r\n        \r\n        <!-- 积分特权设置 -->\r\n        <block v-if=\"privilegeForm.type === 'points'\">\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">积分倍率</text>\r\n            <view class=\"form-input-group\">\r\n              <input type=\"digit\" class=\"form-input\" v-model=\"privilegeForm.pointsRatio\" placeholder=\"请输入积分倍率\" />\r\n              <text class=\"input-suffix\">倍</text>\r\n            </view>\r\n          </view>\r\n        </block>\r\n        \r\n        <!-- 配送特权设置 -->\r\n        <block v-if=\"privilegeForm.type === 'delivery'\">\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">免运费条件</text>\r\n            <view class=\"radio-group\">\r\n              <view class=\"radio-item\" :class=\"{ active: privilegeForm.freeShippingCondition === 'none' }\" @click=\"setShippingCondition('none')\">\r\n                <text class=\"radio-text\">无条件</text>\r\n              </view>\r\n              <view class=\"radio-item\" :class=\"{ active: privilegeForm.freeShippingCondition === 'amount' }\" @click=\"setShippingCondition('amount')\">\r\n                <text class=\"radio-text\">满额免运费</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"form-item\" v-if=\"privilegeForm.freeShippingCondition === 'amount'\">\r\n            <text class=\"form-label\">满额金额</text>\r\n            <view class=\"form-input-group\">\r\n              <input type=\"digit\" class=\"form-input\" v-model=\"privilegeForm.freeShippingAmount\" placeholder=\"请输入满额金额\" />\r\n              <text class=\"input-suffix\">元</text>\r\n            </view>\r\n          </view>\r\n        </block>\r\n        \r\n        <!-- 礼包特权设置 -->\r\n        <block v-if=\"privilegeForm.type === 'gift'\">\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">礼包类型</text>\r\n            <view class=\"radio-group\">\r\n              <view class=\"radio-item\" :class=\"{ active: privilegeForm.giftType === 'coupon' }\" @click=\"setGiftType('coupon')\">\r\n                <text class=\"radio-text\">优惠券</text>\r\n              </view>\r\n              <view class=\"radio-item\" :class=\"{ active: privilegeForm.giftType === 'points' }\" @click=\"setGiftType('points')\">\r\n                <text class=\"radio-text\">积分</text>\r\n              </view>\r\n              <view class=\"radio-item\" :class=\"{ active: privilegeForm.giftType === 'product' }\" @click=\"setGiftType('product')\">\r\n                <text class=\"radio-text\">实物礼品</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"form-item\" v-if=\"privilegeForm.giftType === 'coupon'\">\r\n            <button class=\"select-btn\" @click=\"selectCoupons\">选择优惠券</button>\r\n          </view>\r\n          \r\n          <view class=\"form-item\" v-if=\"privilegeForm.giftType === 'points'\">\r\n            <text class=\"form-label\">赠送积分</text>\r\n            <view class=\"form-input-group\">\r\n              <input type=\"number\" class=\"form-input\" v-model=\"privilegeForm.giftPoints\" placeholder=\"请输入赠送积分\" />\r\n              <text class=\"input-suffix\">积分</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"form-item\" v-if=\"privilegeForm.giftType === 'product'\">\r\n            <button class=\"select-btn\" @click=\"selectProducts\">选择礼品</button>\r\n          </view>\r\n        </block>\r\n        \r\n        <!-- 客服特权设置 -->\r\n        <block v-if=\"privilegeForm.type === 'service'\">\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">客服类型</text>\r\n            <view class=\"radio-group\">\r\n              <view class=\"radio-item\" :class=\"{ active: privilegeForm.serviceType === 'priority' }\" @click=\"setServiceType('priority')\">\r\n                <text class=\"radio-text\">优先接入</text>\r\n              </view>\r\n              <view class=\"radio-item\" :class=\"{ active: privilegeForm.serviceType === 'exclusive' }\" @click=\"setServiceType('exclusive')\">\r\n                <text class=\"radio-text\">专属客服</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </block>\r\n      </view>\r\n      \r\n      <!-- 适用会员等级 -->\r\n      <view class=\"section-card\">\r\n        <view class=\"section-title\">适用会员等级</view>\r\n        \r\n        <view class=\"level-list\">\r\n          <view class=\"level-item\" v-for=\"(level, index) in memberLevels\" :key=\"index\">\r\n            <view class=\"level-checkbox\" :class=\"{ checked: level.selected }\" @click=\"toggleLevel(level)\">\r\n              <view class=\"checkbox-inner\" v-if=\"level.selected\"></view>\r\n            </view>\r\n            <view class=\"level-content\">\r\n              <text class=\"level-name\">{{level.name}}</text>\r\n              <text class=\"level-desc\">{{level.memberCount}}名会员</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 有效期设置 -->\r\n      <view class=\"section-card\">\r\n        <view class=\"section-title\">有效期设置</view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">有效期类型</text>\r\n          <view class=\"radio-group\">\r\n            <view class=\"radio-item\" :class=\"{ active: privilegeForm.validityType === 'permanent' }\" @click=\"setValidityType('permanent')\">\r\n              <text class=\"radio-text\">永久有效</text>\r\n            </view>\r\n            <view class=\"radio-item\" :class=\"{ active: privilegeForm.validityType === 'fixed' }\" @click=\"setValidityType('fixed')\">\r\n              <text class=\"radio-text\">固定日期</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <block v-if=\"privilegeForm.validityType === 'fixed'\">\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">开始日期</text>\r\n            <picker mode=\"date\" :value=\"privilegeForm.startDate\" @change=\"onStartDateChange\">\r\n              <view class=\"picker-view\">\r\n                <text class=\"picker-text\">{{ privilegeForm.startDate || '请选择开始日期' }}</text>\r\n                <view class=\"picker-arrow\"></view>\r\n              </view>\r\n            </picker>\r\n          </view>\r\n          \r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">结束日期</text>\r\n            <picker mode=\"date\" :value=\"privilegeForm.endDate\" @change=\"onEndDateChange\">\r\n              <view class=\"picker-view\">\r\n                <text class=\"picker-text\">{{ privilegeForm.endDate || '请选择结束日期' }}</text>\r\n                <view class=\"picker-arrow\"></view>\r\n              </view>\r\n            </picker>\r\n          </view>\r\n        </block>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 底部按钮 -->\r\n    <view class=\"bottom-bar\">\r\n      <button class=\"cancel-btn\" @click=\"goBack\">取消</button>\r\n      <button class=\"save-btn\" @click=\"savePrivilege\">保存</button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 表单数据\r\n      privilegeForm: {\r\n        name: '',\r\n        description: '',\r\n        icon: '',\r\n        type: 'discount',\r\n        enabled: true,\r\n        \r\n        // 折扣特权\r\n        discountValue: '9.5',\r\n        discountScope: 'all',\r\n        \r\n        // 积分特权\r\n        pointsRatio: '1.5',\r\n        \r\n        // 配送特权\r\n        freeShippingCondition: 'none',\r\n        freeShippingAmount: '99',\r\n        \r\n        // 礼包特权\r\n        giftType: 'coupon',\r\n        giftPoints: '100',\r\n        \r\n        // 客服特权\r\n        serviceType: 'priority',\r\n        \r\n        // 有效期\r\n        validityType: 'permanent',\r\n        startDate: '',\r\n        endDate: ''\r\n      },\r\n      \r\n      // 特权类型\r\n      privilegeTypes: [\r\n        { id: 'discount', name: '会员折扣' },\r\n        { id: 'points', name: '积分加速' },\r\n        { id: 'delivery', name: '免费配送' },\r\n        { id: 'gift', name: '礼品赠送' },\r\n        { id: 'service', name: '专属客服' }\r\n      ],\r\n      \r\n      // 会员等级\r\n      memberLevels: [\r\n        {\r\n          id: 1,\r\n          name: '普通会员',\r\n          memberCount: 2156,\r\n          selected: false\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '银卡会员',\r\n          memberCount: 864,\r\n          selected: true\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '金卡会员',\r\n          memberCount: 426,\r\n          selected: true\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '钻石会员',\r\n          memberCount: 116,\r\n          selected: true\r\n        }\r\n      ]\r\n    };\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    \r\n    getTypeName(typeId) {\r\n      const type = this.privilegeTypes.find(item => item.id === typeId);\r\n      return type ? type.name : '请选择特权类型';\r\n    },\r\n    \r\n    onTypeChange(e) {\r\n      const index = e.detail.value;\r\n      this.privilegeForm.type = this.privilegeTypes[index].id;\r\n    },\r\n    \r\n    chooseIcon() {\r\n      uni.chooseImage({\r\n        count: 1,\r\n        sizeType: ['compressed'],\r\n        sourceType: ['album', 'camera'],\r\n        success: (res) => {\r\n          this.privilegeForm.icon = res.tempFilePaths[0];\r\n        }\r\n      });\r\n    },\r\n    \r\n    toggleEnabled(e) {\r\n      this.privilegeForm.enabled = e.detail.value;\r\n    },\r\n    \r\n    setDiscountScope(scope) {\r\n      this.privilegeForm.discountScope = scope;\r\n    },\r\n    \r\n    setShippingCondition(condition) {\r\n      this.privilegeForm.freeShippingCondition = condition;\r\n    },\r\n    \r\n    setGiftType(type) {\r\n      this.privilegeForm.giftType = type;\r\n    },\r\n    \r\n    setServiceType(type) {\r\n      this.privilegeForm.serviceType = type;\r\n    },\r\n    \r\n    toggleLevel(level) {\r\n      const index = this.memberLevels.findIndex(item => item.id === level.id);\r\n      if (index !== -1) {\r\n        this.memberLevels[index].selected = !this.memberLevels[index].selected;\r\n      }\r\n    },\r\n    \r\n    setValidityType(type) {\r\n      this.privilegeForm.validityType = type;\r\n    },\r\n    \r\n    onStartDateChange(e) {\r\n      this.privilegeForm.startDate = e.detail.value;\r\n    },\r\n    \r\n    onEndDateChange(e) {\r\n      this.privilegeForm.endDate = e.detail.value;\r\n    },\r\n    \r\n    selectItems() {\r\n      uni.showToast({\r\n        title: `选择${this.privilegeForm.discountScope === 'category' ? '分类' : '商品'}功能开发中`,\r\n        icon: 'none'\r\n      });\r\n    },\r\n    \r\n    selectCoupons() {\r\n      uni.showToast({\r\n        title: '选择优惠券功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    \r\n    selectProducts() {\r\n      uni.showToast({\r\n        title: '选择礼品功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    \r\n    validateForm() {\r\n      if (!this.privilegeForm.name) {\r\n        uni.showToast({\r\n          title: '请输入特权名称',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      \r\n      if (!this.privilegeForm.description) {\r\n        uni.showToast({\r\n          title: '请输入特权描述',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      \r\n      if (!this.privilegeForm.icon) {\r\n        uni.showToast({\r\n          title: '请上传特权图标',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      \r\n      // 检查是否选择了会员等级\r\n      const hasSelectedLevel = this.memberLevels.some(level => level.selected);\r\n      if (!hasSelectedLevel) {\r\n        uni.showToast({\r\n          title: '请选择适用会员等级',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      \r\n      // 检查固定日期\r\n      if (this.privilegeForm.validityType === 'fixed') {\r\n        if (!this.privilegeForm.startDate) {\r\n          uni.showToast({\r\n            title: '请选择开始日期',\r\n            icon: 'none'\r\n          });\r\n          return false;\r\n        }\r\n        \r\n        if (!this.privilegeForm.endDate) {\r\n          uni.showToast({\r\n            title: '请选择结束日期',\r\n            icon: 'none'\r\n          });\r\n          return false;\r\n        }\r\n      }\r\n      \r\n      return true;\r\n    },\r\n    \r\n    savePrivilege() {\r\n      if (!this.validateForm()) return;\r\n      \r\n      uni.showLoading({\r\n        title: '保存中...'\r\n      });\r\n      \r\n      setTimeout(() => {\r\n        uni.hideLoading();\r\n        uni.showToast({\r\n          title: '会员特权添加成功',\r\n          icon: 'success'\r\n        });\r\n        \r\n        setTimeout(() => {\r\n          uni.navigateBack();\r\n        }, 1500);\r\n      }, 1000);\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n/* 添加会员特权页面样式开始 */\r\n.add-privilege-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: 120rpx;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #FF9500, #FF6B22);\r\n  color: #fff;\r\n  padding: 44px 16px 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 2px 10px rgba(255, 107, 34, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.navbar-right {\r\n  width: 36px;\r\n  height: 36px;\r\n}\r\n\r\n/* 表单内容样式 */\r\n.form-content {\r\n  padding: 20rpx;\r\n}\r\n\r\n.section-card {\r\n  background: #fff;\r\n  border-radius: 12rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.section-title {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 20rpx;\r\n  padding-bottom: 15rpx;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n/* 表单项样式 */\r\n.form-item {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.form-label {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  margin-bottom: 10rpx;\r\n  display: block;\r\n}\r\n\r\n.form-input {\r\n  width: 100%;\r\n  height: 80rpx;\r\n  border: 1rpx solid #ddd;\r\n  border-radius: 8rpx;\r\n  padding: 0 20rpx;\r\n  font-size: 28rpx;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.form-input-group {\r\n  display: flex;\r\n  align-items: center;\r\n  border: 1rpx solid #ddd;\r\n  border-radius: 8rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.form-input-group .form-input {\r\n  flex: 1;\r\n  border: none;\r\n}\r\n\r\n.input-suffix {\r\n  padding: 0 20rpx;\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  background: #f5f5f5;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n}\r\n\r\n/* 图标上传 */\r\n.icon-upload {\r\n  width: 120rpx;\r\n  height: 120rpx;\r\n  border: 1rpx dashed #ddd;\r\n  border-radius: 8rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f9f9f9;\r\n}\r\n\r\n.preview-icon {\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 8rpx;\r\n}\r\n\r\n.upload-placeholder {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.upload-icon {\r\n  font-size: 40rpx;\r\n  color: #999;\r\n  line-height: 1;\r\n}\r\n\r\n.upload-text {\r\n  font-size: 20rpx;\r\n  color: #999;\r\n  margin-top: 8rpx;\r\n}\r\n\r\n/* 选择器样式 */\r\n.picker-view {\r\n  height: 80rpx;\r\n  border: 1rpx solid #ddd;\r\n  border-radius: 8rpx;\r\n  padding: 0 20rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  background: #fff;\r\n}\r\n\r\n.picker-text {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n}\r\n\r\n.picker-arrow {\r\n  width: 16rpx;\r\n  height: 16rpx;\r\n  border-right: 2rpx solid #999;\r\n  border-bottom: 2rpx solid #999;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n/* 单选按钮组 */\r\n.radio-group {\r\n  display: flex;\r\n  gap: 20rpx;\r\n}\r\n\r\n.radio-item {\r\n  flex: 1;\r\n  height: 80rpx;\r\n  border: 1rpx solid #ddd;\r\n  border-radius: 8rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f9f9f9;\r\n}\r\n\r\n.radio-item.active {\r\n  background: rgba(255, 107, 34, 0.1);\r\n  border-color: #FF6B22;\r\n}\r\n\r\n.radio-text {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n}\r\n\r\n.radio-item.active .radio-text {\r\n  color: #FF6B22;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 开关项 */\r\n.switch-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n/* 选择按钮 */\r\n.select-btn {\r\n  background: rgba(255, 107, 34, 0.1);\r\n  color: #FF6B22;\r\n  border: none;\r\n  border-radius: 8rpx;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n  font-size: 28rpx;\r\n}\r\n\r\n/* 会员等级列表 */\r\n.level-list {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.level-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20rpx 0;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.level-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.level-checkbox {\r\n  width: 36rpx;\r\n  height: 36rpx;\r\n  border: 1rpx solid #ddd;\r\n  border-radius: 50%;\r\n  margin-right: 20rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.level-checkbox.checked {\r\n  border-color: #FF6B22;\r\n  background: #FF6B22;\r\n}\r\n\r\n.checkbox-inner {\r\n  width: 18rpx;\r\n  height: 18rpx;\r\n  border-radius: 50%;\r\n  background: #fff;\r\n}\r\n\r\n.level-content {\r\n  flex: 1;\r\n}\r\n\r\n.level-name {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  margin-bottom: 8rpx;\r\n  display: block;\r\n}\r\n\r\n.level-desc {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n/* 底部按钮栏 */\r\n.bottom-bar {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: #fff;\r\n  padding: 20rpx;\r\n  display: flex;\r\n  gap: 20rpx;\r\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.cancel-btn {\r\n  flex: 1;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n  background: #f5f5f5;\r\n  color: #666;\r\n  border: none;\r\n  border-radius: 40rpx;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.save-btn {\r\n  flex: 1;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n  background: #FF6B22;\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 40rpx;\r\n  font-size: 28rpx;\r\n}\r\n/* 添加会员特权页面样式结束 */\r\n</style>\r\n<!-- 添加会员特权页面结束 --> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/member/add-privilege.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA+OA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA;AAAA,MAEL,eAAe;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA;AAAA,QAGT,eAAe;AAAA,QACf,eAAe;AAAA;AAAA,QAGf,aAAa;AAAA;AAAA,QAGb,uBAAuB;AAAA,QACvB,oBAAoB;AAAA;AAAA,QAGpB,UAAU;AAAA,QACV,YAAY;AAAA;AAAA,QAGZ,aAAa;AAAA;AAAA,QAGb,cAAc;AAAA,QACd,WAAW;AAAA,QACX,SAAS;AAAA,MACV;AAAA;AAAA,MAGD,gBAAgB;AAAA,QACd,EAAE,IAAI,YAAY,MAAM,OAAQ;AAAA,QAChC,EAAE,IAAI,UAAU,MAAM,OAAQ;AAAA,QAC9B,EAAE,IAAI,YAAY,MAAM,OAAQ;AAAA,QAChC,EAAE,IAAI,QAAQ,MAAM,OAAQ;AAAA,QAC5B,EAAE,IAAI,WAAW,MAAM,OAAO;AAAA,MAC/B;AAAA;AAAA,MAGD,cAAc;AAAA,QACZ;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,QACX;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,QACX;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,QACX;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,QACZ;AAAA,MACF;AAAA;EAEH;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IAED,YAAY,QAAQ;AAClB,YAAM,OAAO,KAAK,eAAe,KAAK,UAAQ,KAAK,OAAO,MAAM;AAChE,aAAO,OAAO,KAAK,OAAO;AAAA,IAC3B;AAAA,IAED,aAAa,GAAG;AACd,YAAM,QAAQ,EAAE,OAAO;AACvB,WAAK,cAAc,OAAO,KAAK,eAAe,KAAK,EAAE;AAAA,IACtD;AAAA,IAED,aAAa;AACXA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAChB,eAAK,cAAc,OAAO,IAAI,cAAc,CAAC;AAAA,QAC/C;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,cAAc,GAAG;AACf,WAAK,cAAc,UAAU,EAAE,OAAO;AAAA,IACvC;AAAA,IAED,iBAAiB,OAAO;AACtB,WAAK,cAAc,gBAAgB;AAAA,IACpC;AAAA,IAED,qBAAqB,WAAW;AAC9B,WAAK,cAAc,wBAAwB;AAAA,IAC5C;AAAA,IAED,YAAY,MAAM;AAChB,WAAK,cAAc,WAAW;AAAA,IAC/B;AAAA,IAED,eAAe,MAAM;AACnB,WAAK,cAAc,cAAc;AAAA,IAClC;AAAA,IAED,YAAY,OAAO;AACjB,YAAM,QAAQ,KAAK,aAAa,UAAU,UAAQ,KAAK,OAAO,MAAM,EAAE;AACtE,UAAI,UAAU,IAAI;AAChB,aAAK,aAAa,KAAK,EAAE,WAAW,CAAC,KAAK,aAAa,KAAK,EAAE;AAAA,MAChE;AAAA,IACD;AAAA,IAED,gBAAgB,MAAM;AACpB,WAAK,cAAc,eAAe;AAAA,IACnC;AAAA,IAED,kBAAkB,GAAG;AACnB,WAAK,cAAc,YAAY,EAAE,OAAO;AAAA,IACzC;AAAA,IAED,gBAAgB,GAAG;AACjB,WAAK,cAAc,UAAU,EAAE,OAAO;AAAA,IACvC;AAAA,IAED,cAAc;AACZA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,KAAK,KAAK,cAAc,kBAAkB,aAAa,OAAO,IAAI;AAAA,QACzE,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IAED,gBAAgB;AACdA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IAED,iBAAiB;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IAED,eAAe;AACb,UAAI,CAAC,KAAK,cAAc,MAAM;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,KAAK,cAAc,aAAa;AACnCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,KAAK,cAAc,MAAM;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAGA,YAAM,mBAAmB,KAAK,aAAa,KAAK,WAAS,MAAM,QAAQ;AACvE,UAAI,CAAC,kBAAkB;AACrBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAGA,UAAI,KAAK,cAAc,iBAAiB,SAAS;AAC/C,YAAI,CAAC,KAAK,cAAc,WAAW;AACjCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AACD,iBAAO;AAAA,QACT;AAEA,YAAI,CAAC,KAAK,cAAc,SAAS;AAC/BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AACD,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO;AAAA,IACR;AAAA,IAED,gBAAgB;AACd,UAAI,CAAC,KAAK,aAAY;AAAI;AAE1BA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAED,mBAAW,MAAM;AACfA,wBAAG,MAAC,aAAY;AAAA,QACjB,GAAE,IAAI;AAAA,MACR,GAAE,GAAI;AAAA,IACT;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1dA,GAAG,WAAW,eAAe;"}