
.analysis-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 20px;
}
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}
.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}
.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}
.date-selector-container {
  padding: 16px;
  display: flex;
  justify-content: flex-end;
}
.date-selector {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 8px 12px;
  border-radius: 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}
.date-text {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}
.date-icon {
  font-size: 16px;
}
.analysis-section {
  margin-bottom: 20px;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  margin-bottom: 12px;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.section-more {
  font-size: 12px;
  color: #1677FF;
}
.analysis-grid {
  padding: 0 16px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}
.analysis-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-bottom: 12px;
}
.sales-icon, .roi-icon {
  background-color: #e6f7ff;
  color: #1677FF;
}
.time-icon, .funnel-icon {
  background-color: #f6ffed;
  color: #52c41a;
}
.profit-icon, .sensitivity-icon {
  background-color: #fff7e6;
  color: #fa8c16;
}
.customer-icon, .industry-icon {
  background-color: #e6f7ff;
  color: #1677FF;
}
.lifecycle-icon, .price-icon {
  background-color: #f6ffed;
  color: #52c41a;
}
.value-icon, .advantage-icon {
  background-color: #fff7e6;
  color: #fa8c16;
}
.churn-icon {
  background-color: #fff1f0;
  color: #f5222d;
}
.card-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  text-align: center;
}
.card-desc {
  font-size: 12px;
  color: #999;
  text-align: center;
}
