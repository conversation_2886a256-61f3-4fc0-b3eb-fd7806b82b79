/**
 * 日期工具函数
 */

/**
 * 获取当前时间戳
 * @returns {number} 当前时间戳（毫秒）
 */
export function getCurrentTimestamp() {
  return Date.now();
}

/**
 * 获取当前日期对象
 * @returns {Date} 当前日期对象
 */
export function getCurrentDate() {
  return new Date();
}

/**
 * 判断日期是否过期
 * @param {string|number|Date} date 日期
 * @returns {boolean} 是否过期
 */
export function isExpired(date) {
  if (!date) return true;
  return new Date(date).getTime() < Date.now();
}

/**
 * 获取两个日期之间的天数差
 * @param {string|number|Date} date1 日期1
 * @param {string|number|Date} date2 日期2
 * @returns {number} 天数差
 */
export function getDaysDiff(date1, date2) {
  const d1 = new Date(date1).getTime();
  const d2 = new Date(date2).getTime();
  return Math.floor(Math.abs(d1 - d2) / (1000 * 60 * 60 * 24));
}

/**
 * 获取指定日期的开始时间
 * @param {string|number|Date} date 日期
 * @returns {Date} 开始时间
 */
export function getStartOfDay(date) {
  const d = new Date(date);
  d.setHours(0, 0, 0, 0);
  return d;
}

/**
 * 获取指定日期的结束时间
 * @param {string|number|Date} date 日期
 * @returns {Date} 结束时间
 */
export function getEndOfDay(date) {
  const d = new Date(date);
  d.setHours(23, 59, 59, 999);
  return d;
}

/**
 * 获取指定日期所在月份的第一天
 * @param {string|number|Date} date 日期
 * @returns {Date} 月份第一天
 */
export function getFirstDayOfMonth(date) {
  const d = new Date(date);
  d.setDate(1);
  d.setHours(0, 0, 0, 0);
  return d;
}

/**
 * 获取指定日期所在月份的最后一天
 * @param {string|number|Date} date 日期
 * @returns {Date} 月份最后一天
 */
export function getLastDayOfMonth(date) {
  const d = new Date(date);
  d.setMonth(d.getMonth() + 1);
  d.setDate(0);
  d.setHours(23, 59, 59, 999);
  return d;
}

/**
 * 判断是否是同一天
 * @param {string|number|Date} date1 日期1
 * @param {string|number|Date} date2 日期2
 * @returns {boolean} 是否是同一天
 */
export function isSameDay(date1, date2) {
  const d1 = new Date(date1);
  const d2 = new Date(date2);
  return d1.getFullYear() === d2.getFullYear() &&
    d1.getMonth() === d2.getMonth() &&
    d1.getDate() === d2.getDate();
}

/**
 * 获取相对时间描述
 * @param {string|number|Date} date 日期
 * @returns {string} 相对时间描述
 */
export function getRelativeTime(date) {
  const now = Date.now();
  const diff = now - new Date(date).getTime();
  
  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚';
  }
  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    return Math.floor(diff / (60 * 1000)) + '分钟前';
  }
  // 小于24小时
  if (diff < 24 * 60 * 60 * 1000) {
    return Math.floor(diff / (60 * 60 * 1000)) + '小时前';
  }
  // 小于30天
  if (diff < 30 * 24 * 60 * 60 * 1000) {
    return Math.floor(diff / (24 * 60 * 60 * 1000)) + '天前';
  }
  // 小于12个月
  if (diff < 12 * 30 * 24 * 60 * 60 * 1000) {
    return Math.floor(diff / (30 * 24 * 60 * 60 * 1000)) + '个月前';
  }
  // 大于等于12个月
  return Math.floor(diff / (12 * 30 * 24 * 60 * 60 * 1000)) + '年前';
}

/**
 * 格式化日期
 * @param {string|number|Date} date 日期
 * @param {string} format 格式化模式，默认为 'YYYY-MM-DD'
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD') {
  if (!date) return '';
  
  const d = new Date(date);
  if (isNaN(d.getTime())) return '';
  
  const year = d.getFullYear();
  const month = d.getMonth() + 1;
  const day = d.getDate();
  const hour = d.getHours();
  const minute = d.getMinutes();
  const second = d.getSeconds();
  
  return format
    .replace('YYYY', year)
    .replace('MM', month.toString().padStart(2, '0'))
    .replace('DD', day.toString().padStart(2, '0'))
    .replace('HH', hour.toString().padStart(2, '0'))
    .replace('mm', minute.toString().padStart(2, '0'))
    .replace('ss', second.toString().padStart(2, '0'));
}

/**
 * 格式化时间
 * @param {string|number|Date} date 日期
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(date) {
  if (!date) return '';
  
  const d = new Date(date);
  if (isNaN(d.getTime())) return '';
  
  return formatDate(d, 'YYYY-MM-DD HH:mm');
} 