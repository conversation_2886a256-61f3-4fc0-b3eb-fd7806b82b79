"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  setup() {
    const discountId = common_vendor.ref("");
    const discountData = common_vendor.reactive({
      id: 1,
      title: "春季促销活动",
      status: "active",
      statusText: "进行中",
      rules: [
        { minAmount: 100, discountAmount: 10 },
        { minAmount: 200, discountAmount: 25 },
        { minAmount: 300, discountAmount: 50 }
      ],
      timeRange: "2023-04-01 ~ 2023-04-30",
      qrCodeUrl: "/static/images/discount-qrcode-placeholder.png",
      totalOrders: 352,
      totalUsers: 280,
      totalDiscount: "8,562.50",
      conversionRate: 65.3,
      typeText: "满减活动",
      applicableProducts: "全部商品",
      canStack: false,
      perPersonLimit: 3,
      instructions: "活动期间，每位用户最多可使用3次满减优惠，不可与其他优惠同时使用",
      recentRecords: [
        {
          id: 1,
          orderNumber: "DD20230420001",
          userName: "张三",
          time: "2023-04-20 14:30",
          discountAmount: "25.00"
        },
        {
          id: 2,
          orderNumber: "DD20230420002",
          userName: "李四",
          time: "2023-04-20 15:45",
          discountAmount: "10.00"
        },
        {
          id: 3,
          orderNumber: "DD20230421003",
          userName: "王五",
          time: "2023-04-21 09:20",
          discountAmount: "50.00"
        }
      ]
    });
    const showSharePopup = common_vendor.ref(false);
    function goBack() {
      common_vendor.index.navigateBack();
    }
    function showMoreOptions() {
      common_vendor.index.showActionSheet({
        itemList: ["复制活动信息", "导出数据", "设为模板"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              common_vendor.index.setClipboardData({
                data: `${discountData.title}：${discountData.rules.map((rule) => `满${rule.minAmount}减${rule.discountAmount}`).join("，")}，活动时间：${discountData.timeRange}`,
                success: () => {
                  common_vendor.index.showToast({
                    title: "已复制活动信息",
                    icon: "success"
                  });
                }
              });
              break;
            case 1:
              common_vendor.index.showToast({
                title: "正在导出数据...",
                icon: "loading"
              });
              setTimeout(() => {
                common_vendor.index.showToast({
                  title: "导出成功",
                  icon: "success"
                });
              }, 1500);
              break;
            case 2:
              common_vendor.index.showToast({
                title: "已设为模板",
                icon: "success"
              });
              break;
          }
        }
      });
    }
    function toggleDiscountStatus() {
      const isActive = discountData.status === "active";
      const newStatus = isActive ? "paused" : "active";
      const statusText = isActive ? "已暂停" : "进行中";
      discountData.status = newStatus;
      discountData.statusText = statusText;
      common_vendor.index.showToast({
        title: isActive ? "已暂停活动" : "已启用活动",
        icon: "success"
      });
    }
    function confirmDeleteDiscount() {
      common_vendor.index.showModal({
        title: "确认删除",
        content: `确定要删除"${discountData.title}"吗？此操作无法撤销。`,
        confirmColor: "#FF3B30",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "已删除活动",
              icon: "success"
            });
            setTimeout(() => {
              common_vendor.index.navigateBack();
            }, 1500);
          }
        }
      });
    }
    function editDiscount() {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/discount/edit?id=${discountData.id}`,
        animationType: "slide-in-right"
      });
    }
    function viewAllRecords() {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/discount/records?id=${discountData.id}`,
        animationType: "slide-in-right"
      });
    }
    function shareDiscount() {
      showSharePopup.value = true;
    }
    function hideSharePopup() {
      showSharePopup.value = false;
    }
    function shareToChannel(channel) {
      common_vendor.index.showToast({
        title: `已分享到${channel === "wechat" ? "微信" : channel === "moments" ? "朋友圈" : channel === "link" ? "链接已复制" : "二维码已保存"}`,
        icon: "success"
      });
      hideSharePopup();
    }
    function loadDiscountData(id) {
      discountId.value = id;
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
      }, 500);
    }
    common_vendor.onMounted(() => {
      var _a;
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = ((_a = currentPage.$page) == null ? void 0 : _a.options) || {};
      if (options.id) {
        loadDiscountData(options.id);
      }
    });
    return {
      discountData,
      showSharePopup,
      goBack,
      showMoreOptions,
      toggleDiscountStatus,
      confirmDeleteDiscount,
      editDiscount,
      viewAllRecords,
      shareDiscount,
      hideSharePopup,
      shareToChannel
    };
  }
};
if (!Array) {
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_rect = common_vendor.resolveComponent("rect");
  const _component_polygon = common_vendor.resolveComponent("polygon");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  (_component_circle + _component_svg + _component_path + _component_rect + _component_polygon + _component_line + _component_polyline)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $setup.goBack && $setup.goBack(...args)),
    b: common_vendor.p({
      cx: "12",
      cy: "12",
      r: "1"
    }),
    c: common_vendor.p({
      cx: "19",
      cy: "12",
      r: "1"
    }),
    d: common_vendor.p({
      cx: "5",
      cy: "12",
      r: "1"
    }),
    e: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    f: common_vendor.o((...args) => $setup.showMoreOptions && $setup.showMoreOptions(...args)),
    g: common_vendor.t($setup.discountData.title),
    h: common_vendor.t($setup.discountData.statusText),
    i: common_vendor.n("status-" + $setup.discountData.status),
    j: common_vendor.f($setup.discountData.rules, (rule, index, i0) => {
      return {
        a: common_vendor.t(rule.minAmount),
        b: common_vendor.t(rule.discountAmount),
        c: index
      };
    }),
    k: common_vendor.t($setup.discountData.timeRange),
    l: $setup.discountData.status === "active"
  }, $setup.discountData.status === "active" ? {
    m: $setup.discountData.qrCodeUrl
  } : {}, {
    n: common_vendor.t($setup.discountData.totalOrders),
    o: common_vendor.t($setup.discountData.totalUsers),
    p: common_vendor.t($setup.discountData.totalDiscount),
    q: common_vendor.t($setup.discountData.conversionRate),
    r: common_vendor.t($setup.discountData.typeText),
    s: common_vendor.t($setup.discountData.applicableProducts),
    t: common_vendor.t($setup.discountData.canStack ? "允许" : "不允许"),
    v: common_vendor.t($setup.discountData.perPersonLimit),
    w: common_vendor.t($setup.discountData.instructions),
    x: common_vendor.o((...args) => $setup.viewAllRecords && $setup.viewAllRecords(...args)),
    y: common_vendor.f($setup.discountData.recentRecords, (record, index, i0) => {
      return {
        a: common_vendor.t(record.orderNumber),
        b: common_vendor.t(record.time),
        c: common_vendor.t(record.userName),
        d: common_vendor.t(record.discountAmount),
        e: index
      };
    }),
    z: $setup.discountData.recentRecords.length === 0
  }, $setup.discountData.recentRecords.length === 0 ? {} : {}, {
    A: common_vendor.p({
      d: "M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"
    }),
    B: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    C: common_vendor.o((...args) => $setup.editDiscount && $setup.editDiscount(...args)),
    D: $setup.discountData.status === "active"
  }, $setup.discountData.status === "active" ? {
    E: common_vendor.p({
      x: "6",
      y: "4",
      width: "4",
      height: "16"
    }),
    F: common_vendor.p({
      x: "14",
      y: "4",
      width: "4",
      height: "16"
    }),
    G: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    })
  } : {
    H: common_vendor.p({
      points: "5 3 19 12 5 21 5 3"
    }),
    I: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    })
  }, {
    J: common_vendor.t($setup.discountData.status === "active" ? "暂停" : "启用"),
    K: common_vendor.n($setup.discountData.status === "active" ? "pause" : "activate"),
    L: common_vendor.o((...args) => $setup.toggleDiscountStatus && $setup.toggleDiscountStatus(...args)),
    M: common_vendor.p({
      cx: "18",
      cy: "5",
      r: "3"
    }),
    N: common_vendor.p({
      cx: "6",
      cy: "12",
      r: "3"
    }),
    O: common_vendor.p({
      cx: "18",
      cy: "19",
      r: "3"
    }),
    P: common_vendor.p({
      x1: "8.59",
      y1: "13.51",
      x2: "15.42",
      y2: "17.49"
    }),
    Q: common_vendor.p({
      x1: "15.41",
      y1: "6.51",
      x2: "8.59",
      y2: "10.49"
    }),
    R: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    S: common_vendor.o((...args) => $setup.shareDiscount && $setup.shareDiscount(...args)),
    T: common_vendor.p({
      points: "3 6 5 6 21 6"
    }),
    U: common_vendor.p({
      d: "M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"
    }),
    V: common_vendor.p({
      x1: "10",
      y1: "11",
      x2: "10",
      y2: "17"
    }),
    W: common_vendor.p({
      x1: "14",
      y1: "11",
      x2: "14",
      y2: "17"
    }),
    X: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    Y: common_vendor.o((...args) => $setup.confirmDeleteDiscount && $setup.confirmDeleteDiscount(...args)),
    Z: $setup.showSharePopup
  }, $setup.showSharePopup ? {
    aa: common_vendor.o((...args) => $setup.hideSharePopup && $setup.hideSharePopup(...args)),
    ab: common_vendor.o((...args) => $setup.hideSharePopup && $setup.hideSharePopup(...args)),
    ac: common_vendor.p({
      d: "M9.5 9.5c-.3-2.1 1.6-4 4.8-4.3 3.1-.3 5.5 1.2 5.9 3.5.4 2.3-1.5 4.8-4.5 5.2-1 .1-1.9 0-2.7-.4l-2.4 1.2.5-2.2c-1.1-.8-1.8-1.9-1.6-3z"
    }),
    ad: common_vendor.p({
      d: "M4 14.5c-.5-2.5 2.3-5 6.2-5.5 3.9-.5 7.5 1 8 3.5.5 2.5-1.8 5-5.7 5.5-1.2.2-2.3 0-3.4-.4l-3 1.5.6-2.7c-1.4-1-2.4-2.3-2.7-3.9z"
    }),
    ae: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    af: common_vendor.o(($event) => $setup.shareToChannel("wechat")),
    ag: common_vendor.p({
      cx: "12",
      cy: "12",
      r: "10"
    }),
    ah: common_vendor.p({
      d: "M12 8L12 16"
    }),
    ai: common_vendor.p({
      d: "M8 12L16 12"
    }),
    aj: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    ak: common_vendor.o(($event) => $setup.shareToChannel("moments")),
    al: common_vendor.p({
      d: "M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"
    }),
    am: common_vendor.p({
      d: "M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"
    }),
    an: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    ao: common_vendor.o(($event) => $setup.shareToChannel("link")),
    ap: common_vendor.p({
      x: "3",
      y: "3",
      width: "7",
      height: "7"
    }),
    aq: common_vendor.p({
      x: "14",
      y: "3",
      width: "7",
      height: "7"
    }),
    ar: common_vendor.p({
      x: "3",
      y: "14",
      width: "7",
      height: "7"
    }),
    as: common_vendor.p({
      x: "14",
      y: "14",
      width: "7",
      height: "7"
    }),
    at: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    av: common_vendor.o(($event) => $setup.shareToChannel("qrcode"))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/discount/detail.js.map
