
/* 秒杀活动卡片特有样式 */
.flash-sale-card.data-v-0b7110be {
  /* 继承基础卡片样式 */
}

/* 秒杀特有信息区域 */
.flash-sale-special.data-v-0b7110be {
  padding: 20rpx;
  background-color: rgba(255, 59, 48, 0.05);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
}

/* 价格区域 */
.price-section.data-v-0b7110be {
  display: flex;
  align-items: baseline;
  margin-bottom: 20rpx;
}
.current-price.data-v-0b7110be {
  display: flex;
  align-items: baseline;
  color: #ff3b30;
  margin-right: 16rpx;
}
.price-symbol.data-v-0b7110be {
  font-size: 24rpx;
  font-weight: 500;
}
.price-value.data-v-0b7110be {
  font-size: 40rpx;
  font-weight: 700;
}
.original-price.data-v-0b7110be {
  display: flex;
  align-items: baseline;
  margin-right: 16rpx;
}
.price-label.data-v-0b7110be {
  font-size: 22rpx;
  color: #8e8e93;
  margin-right: 4rpx;
}
.price-through.data-v-0b7110be {
  font-size: 24rpx;
  color: #8e8e93;
  text-decoration: line-through;
}
.discount-tag.data-v-0b7110be {
  padding: 4rpx 10rpx;
  background-color: rgba(255, 59, 48, 0.1);
  border-radius: 10rpx;
}
.discount-value.data-v-0b7110be {
  font-size: 22rpx;
  color: #ff3b30;
  font-weight: 500;
}

/* 倒计时 */
.countdown.data-v-0b7110be {
  margin-bottom: 20rpx;
  border-top: 1rpx dashed rgba(255, 59, 48, 0.2);
  padding-top: 16rpx;
}
.countdown-header.data-v-0b7110be {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}
.countdown-title.data-v-0b7110be {
  font-size: 24rpx;
  color: #000000;
  font-weight: 500;
}
.countdown-status.data-v-0b7110be {
  font-size: 24rpx;
  color: #ff3b30;
  font-weight: 500;
}
.countdown-timer.data-v-0b7110be {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
}
.time-block.data-v-0b7110be {
  width: 60rpx;
  height: 60rpx;
  background-color: #1c1c1e;
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 600;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
}
.time-separator.data-v-0b7110be {
  margin: 0 8rpx;
  color: #1c1c1e;
  font-size: 28rpx;
  font-weight: 600;
}

/* 库存进度 */
.stock-progress.data-v-0b7110be {
  margin-top: 16rpx;
  border-top: 1rpx dashed rgba(255, 59, 48, 0.2);
  padding-top: 16rpx;
}
.progress-header.data-v-0b7110be {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}
.progress-title.data-v-0b7110be {
  font-size: 24rpx;
  color: #000000;
  font-weight: 500;
}
.progress-status.data-v-0b7110be {
  font-size: 24rpx;
  color: #ff3b30;
  font-weight: 500;
}
.progress-bar.data-v-0b7110be {
  height: 10rpx;
  background-color: rgba(255, 59, 48, 0.1);
  border-radius: 5rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
}
.progress-inner.data-v-0b7110be {
  height: 100%;
  background-color: #ff3b30;
  border-radius: 5rpx;
  transition: width 0.3s ease;
}
.progress-tip.data-v-0b7110be {
  margin-top: 8rpx;
}
.tip-text.data-v-0b7110be {
  font-size: 22rpx;
  color: #ff3b30;
}
