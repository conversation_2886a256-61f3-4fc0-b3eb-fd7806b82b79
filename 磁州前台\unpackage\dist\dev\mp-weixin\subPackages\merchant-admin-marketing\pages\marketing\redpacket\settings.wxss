/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-f049e702, html.data-v-f049e702, #app.data-v-f049e702, .index-container.data-v-f049e702 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.page-container.data-v-f049e702 {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏样式 */
.navbar.data-v-f049e702 {
  display: flex;
  align-items: center;
  height: 44px;
  background-color: #fff;
  padding: 0 15px;
  position: relative;
}
.navbar-back.data-v-f049e702 {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon.data-v-f049e702 {
  width: 12px;
  height: 12px;
  border-top: 2px solid #333;
  border-left: 2px solid #333;
  transform: rotate(-45deg);
}
.navbar-title.data-v-f049e702 {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 600;
  color: #333;
}
.navbar-right.data-v-f049e702 {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon.data-v-f049e702 {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  border: 1px solid #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #999;
}

/* 设置内容样式 */
.settings-content.data-v-f049e702 {
  flex: 1;
  padding: 15px;
}
.settings-section.data-v-f049e702 {
  margin-bottom: 15px;
}
.section-header.data-v-f049e702 {
  margin-bottom: 10px;
}
.section-title.data-v-f049e702 {
  font-size: 15px;
  font-weight: 600;
  color: #666;
}
.settings-group.data-v-f049e702 {
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
}
.settings-item.data-v-f049e702 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}
.settings-item.data-v-f049e702:last-child {
  border-bottom: none;
}
.item-left.data-v-f049e702 {
  flex: 1;
}
.item-label.data-v-f049e702 {
  font-size: 15px;
  color: #333;
  display: block;
}
.item-desc.data-v-f049e702 {
  font-size: 12px;
  color: #999;
  display: block;
  margin-top: 4px;
}
.item-right.data-v-f049e702 {
  display: flex;
  align-items: center;
}
.item-value.data-v-f049e702 {
  font-size: 14px;
  color: #999;
  margin-right: 5px;
}
.arrow-icon.data-v-f049e702 {
  width: 8px;
  height: 8px;
  border-top: 1px solid #ccc;
  border-right: 1px solid #ccc;
  transform: rotate(45deg);
}
.template-preview.data-v-f049e702 {
  width: 40px;
  height: 60px;
  background-size: cover;
  background-position: center;
  border-radius: 4px;
  margin-right: 10px;
}

/* 清除缓存按钮 */
.clear-cache-btn.data-v-f049e702 {
  background-color: #fff;
  border-radius: 10px;
  padding: 15px;
  text-align: center;
  margin-top: 20px;
  margin-bottom: 30px;
}
.btn-text.data-v-f049e702 {
  font-size: 15px;
  color: #FF4D4F;
}

/* 弹窗样式 */
.popup-mask.data-v-f049e702 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 100;
}
.popup-content.data-v-f049e702 {
  position: fixed;
  left: 15px;
  right: 15px;
  bottom: 30px;
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
  z-index: 101;
}
.popup-header.data-v-f049e702 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.popup-title.data-v-f049e702 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.popup-close.data-v-f049e702 {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #999;
}
.limit-picker.data-v-f049e702 {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}
.limit-control.data-v-f049e702 {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  border: 1px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}
.limit-value.data-v-f049e702 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 30px;
  min-width: 40px;
  text-align: center;
}
.popup-buttons.data-v-f049e702 {
  display: flex;
  justify-content: space-between;
}
.cancel-btn.data-v-f049e702, .confirm-btn.data-v-f049e702 {
  flex: 1;
  height: 40px;
  border-radius: 20px;
  font-size: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.cancel-btn.data-v-f049e702 {
  background-color: #f5f5f5;
  color: #666;
  margin-right: 10px;
}
.confirm-btn.data-v-f049e702 {
  background-color: #FF4D4F;
  color: #fff;
  margin-left: 10px;
}