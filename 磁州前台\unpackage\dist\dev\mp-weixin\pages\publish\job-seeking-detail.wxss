
.job-seeking-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 110rpx;
}
.job-seeking-wrapper {
  padding: 150rpx 20rpx 120rpx; /* 增加顶部内边距，为固定导航栏留出空间 */
}

/* 自定义导航栏样式 */
.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}
.navbar-left {
  width: 60rpx;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border-radius: 50%;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  color: #FFFFFF; /* 修改为白色字体 */
}
.navbar-right {
  display: flex;
  align-items: center;
  padding-right: 20rpx;
}

/* 悬浮举报按钮样式已删除 */
/*
.report-btn {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 30rpx;
  padding: 6rpx 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.report-text {
  font-size: 24rpx;
  color: #FF5151;
}
*/
.content-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-top: 20rpx; /* 添加与标题栏的间隙 */
}

/* 求职基本信息卡片 */
.seeking-title-row {
  display: none;
}
.seeking-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}
.seeking-salary {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff4d4f;
  margin-bottom: 16rpx;
}
.seeking-meta {
  margin-bottom: 24rpx;
}
.seeking-tag-group {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12rpx;
}
.seeking-tag {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
  margin-bottom: 12rpx;
}
.seeking-publish-time {
  font-size: 24rpx;
  color: #999;
}

/* 基本信息 */
.seeking-basic-info {
  display: flex;
  flex-wrap: wrap;
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}
.info-item {
  width: 50%;
  padding: 12rpx 24rpx;
  box-sizing: border-box;
}
.info-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}
.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 求职意向 */
.intention-list {
  display: flex;
  flex-direction: column;
}
.intention-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.intention-item:last-child {
  border-bottom: none;
}
.intention-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}
.intention-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 工作经验 */
.experience-list {
  display: flex;
  flex-direction: column;
}
.experience-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.experience-item:last-child {
  border-bottom: none;
}
.experience-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}
.experience-company {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}
.experience-time {
  font-size: 24rpx;
  color: #999;
}
.experience-position {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}
.experience-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 教育背景 */
.education-list {
  display: flex;
  flex-direction: column;
}
.education-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.education-item:last-child {
  border-bottom: none;
}
.education-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}
.education-school {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}
.education-time {
  font-size: 24rpx;
  color: #999;
}
.education-major {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}
.education-degree {
  font-size: 26rpx;
  color: #666;
}

/* 技能特长 */
.skills-content {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8rpx;
}
.skill-tag {
  font-size: 26rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 8rpx 20rpx;
  border-radius: 6rpx;
  margin: 8rpx;
}

/* 自我评价 */
.evaluation-content {
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}
.evaluation-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 相似求职 */
.similar-list {
  display: flex;
  flex-direction: column;
}
.similar-item {
  padding: 20rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}
.similar-seeking-info {
  display: flex;
  flex-direction: column;
}
.similar-seeking-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}
.similar-seeking-salary {
  font-size: 28rpx;
  color: #ff4d4f;
  margin-bottom: 8rpx;
}
.similar-seeking-meta {
  font-size: 24rpx;
  color: #999;
}

/* 无数据提示 */
.no-data-tip {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  padding: 30rpx 0;
}

/* 底部互动工具栏 */
.interaction-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10rpx 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  height: 120rpx;
  z-index: 100;
}
.toolbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6rpx 0;
  margin: 0 4rpx;
}
.toolbar-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}
.toolbar-text {
  font-size: 22rpx;
  color: #666;
}
.share-button {
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
  line-height: normal;
  border-radius: 0;
  flex: 1;
}
.share-button::after {
  display: none;
}
.call-button {
  flex: 3; /* 占据更多空间，原来是2，现在改为3让按钮更长 */
  background: linear-gradient(135deg, #0052CC, #0066FF);
  height: 90rpx;
  margin: 0 0 0 10rpx;
  border-radius: 45rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
}
.call-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.call-text {
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  line-height: 1.2;
}
.call-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 20rpx;
  line-height: 1.2;
}

/* 隐藏原来的底部操作栏 */
.action-bar {
  display: none;
}

/* 隐藏的分享按钮 */
.hidden-share-btn {
  position: absolute;
  top: -9999rpx;
  left: -9999rpx;
  width: 0;
  height: 0;
  padding: 0;
  margin: 0;
  opacity: 0;
}
.back-icon {
  width: 36rpx;
  height: 36rpx;
  display: block;
}

/* 悬浮海报按钮 */
.float-poster-btn {
  position: fixed;
  right: 30rpx;
  bottom: 200rpx;
  width: 100rpx;
  height: 100rpx;
  background: rgba(240, 240, 240, 0.9);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.08);
  z-index: 90;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1rpx solid rgba(230, 230, 230, 0.6);
  transition: all 0.2s ease;
}
.float-poster-btn:active {
  transform: scale(0.95);
  box-shadow: 0 3rpx 10rpx rgba(0,0,0,0.08);
}
.poster-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 4rpx;
}
.poster-text {
  font-size: 20rpx;
  color: #444;
  line-height: 1;
}

/* 区块标题样式优化 - 添加蓝色竖线 */
.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 16rpx;
}
.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 6rpx;
  height: 28rpx;
  background-color: #1890ff;
  border-radius: 3rpx;
}

/* 联系方式样式 - 电话显示为绿色，提示为黄色 */
.contact-phone {
  color: #52c41a; /* 绿色 */
  font-weight: 500;
}
.contact-tips {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
  padding: 10rpx 16rpx;
  background-color: rgba(255, 152, 0, 0.1);
  border-radius: 8rpx;
}
.tips-icon {
  font-size: 24rpx;
  color: #ff9800; /* 黄色 */
  margin-right: 8rpx;
}
.tips-text {
  font-size: 24rpx;
  color: #ff9800; /* 黄色 */
}

/* 相关求职推荐样式 */
.related-seekings-card {
  margin-top: 12px;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 可折叠标题栏样式 */
.collapsible-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #fff;
  position: relative;
  cursor: pointer;
}

/* 相关求职列表样式 */
.related-seekings-list {
  margin-bottom: 12px;
}
.related-seeking-item {
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}
.related-seeking-item:last-child {
  border-bottom: none;
}
.seeking-item-content {
  display: flex;
  align-items: center;
}
.seeking-item-left {
  margin-right: 12px;
}
.seeker-avatar {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #f5f7fa;
}
.seeking-item-middle {
  flex: 1;
  overflow: hidden;
}
.seeking-item-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.seeking-item-person {
  font-size: 13px;
  color: #666;
  margin-bottom: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.seeking-item-tags {
  display: flex;
  flex-wrap: wrap;
}
.seeking-item-tag {
  font-size: 11px;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  margin-right: 6px;
}
.seeking-item-tag-more {
  font-size: 11px;
  color: #999;
}
.seeking-item-right {
  min-width: 80px;
  text-align: right;
}
.seeking-item-salary {
  font-size: 15px;
  font-weight: 500;
  color: #ff5252;
}

/* 查看更多按钮样式 */
.view-more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  background-color: #f7f9fc;
  border-radius: 8px;
  margin-top: 8px;
}
.view-more-text {
  font-size: 14px;
  color: #1890ff;
}
.view-more-icon {
  margin-left: 4px;
  font-size: 12px;
  color: #1890ff;
}

/* 折叠内容区样式 */
.collapsible-content {
  padding: 0 16px 16px;
  overflow: hidden;
}

/* 空数据提示样式 */
.empty-related-seekings {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 0;
}
.empty-image {
  width: 80px;
  height: 80px;
  margin-bottom: 12px;
}
.empty-text {
  font-size: 14px;
  color: #999;
}
