@echo off
chcp 65001 >nul

echo.
echo ========================================
echo   本地服务状态检查
echo ========================================
echo.

echo 正在检查本地服务状态...
echo.

echo [1] MySQL 服务状态
echo ---------------------
sc query mysql >nul 2>&1
if errorlevel 1 (
    sc query mysql80 >nul 2>&1
    if errorlevel 1 (
        echo X MySQL 服务未安装
        echo   请运行: install-local-services.bat
    ) else (
        echo √ MySQL80 服务已安装
        sc query mysql80 | findstr "STATE"
    )
) else (
    echo √ MySQL 服务已安装
    sc query mysql | findstr "STATE"
)

echo.
echo [2] MySQL 连接测试
echo ---------------------
mysql -uroot -pcizhou123456 -e "SELECT VERSION();" 2>nul
if errorlevel 1 (
    echo X MySQL 连接失败
    echo   请检查：
    echo   - MySQL 服务是否启动
    echo   - 密码是否为: cizhou123456
    echo   - 端口 3306 是否可用
) else (
    echo √ MySQL 连接成功
    mysql -uroot -pcizhou123456 -e "SELECT VERSION();" 2>nul
)

echo.
echo [3] Redis 服务状态
echo ---------------------
sc query redis >nul 2>&1
if errorlevel 1 (
    echo X Redis 服务未安装
    echo   请运行: install-local-services.bat
) else (
    echo √ Redis 服务已安装
    sc query redis | findstr "STATE"
)

echo.
echo [4] Redis 连接测试
echo ---------------------
redis-cli -a cizhou123456 ping >nul 2>&1
if errorlevel 1 (
    redis-cli ping >nul 2>&1
    if errorlevel 1 (
        echo X Redis 连接失败
        echo   请检查：
        echo   - Redis 服务是否启动
        echo   - 端口 6379 是否可用
    ) else (
        echo √ Redis 连接成功 (无密码)
        redis-cli ping
    )
) else (
    echo √ Redis 连接成功 (有密码)
    redis-cli -a cizhou123456 ping
)

echo.
echo [5] Node.js 环境
echo ---------------------
where node >nul 2>&1
if errorlevel 1 (
    echo X Node.js 未安装
    echo   请访问: https://nodejs.org/
) else (
    echo √ Node.js 已安装
    echo   版本: 
    node --version
    echo   npm 版本:
    npm --version
)

echo.
echo [6] 端口占用检查
echo ---------------------
echo 检查关键端口占用情况...

netstat -ano | findstr :3306 >nul 2>&1
if errorlevel 1 (
    echo   3306 (MySQL): 未占用
) else (
    echo √ 3306 (MySQL): 已占用
)

netstat -ano | findstr :6379 >nul 2>&1
if errorlevel 1 (
    echo   6379 (Redis): 未占用
) else (
    echo √ 6379 (Redis): 已占用
)

netstat -ano | findstr :3000 >nul 2>&1
if errorlevel 1 (
    echo   3000 (前端): 未占用
) else (
    echo ! 3000 (前端): 已占用
)

netstat -ano | findstr :8080 >nul 2>&1
if errorlevel 1 (
    echo   8080 (网关): 未占用
) else (
    echo ! 8080 (网关): 已占用
)

netstat -ano | findstr :8081 >nul 2>&1
if errorlevel 1 (
    echo   8081 (认证): 未占用
) else (
    echo ! 8081 (认证): 已占用
)

echo.
echo ========================================
echo 检查结果总结
echo ========================================
echo.

REM 检查所有服务是否就绪
set "all_ready=true"

mysql -uroot -pcizhou123456 -e "SELECT 1;" >nul 2>&1
if errorlevel 1 (
    echo X MySQL 未就绪
    set "all_ready=false"
) else (
    echo √ MySQL 就绪
)

redis-cli -a cizhou123456 ping >nul 2>&1 || redis-cli ping >nul 2>&1
if errorlevel 1 (
    echo X Redis 未就绪
    set "all_ready=false"
) else (
    echo √ Redis 就绪
)

where node >nul 2>&1
if errorlevel 1 (
    echo X Node.js 未就绪
    set "all_ready=false"
) else (
    echo √ Node.js 就绪
)

echo.
if "%all_ready%"=="true" (
    echo 🎉 所有服务就绪！可以启动应用了
    echo.
    echo 下一步：
    echo   运行: start-without-docker.bat
) else (
    echo ⚠️  部分服务未就绪
    echo.
    echo 解决方案：
    echo   1. 运行: install-local-services.bat (安装缺失服务)
    echo   2. 检查服务是否启动
    echo   3. 验证配置是否正确
)

echo.
pause
