<view class="news-container"><scroll-view class="category-scroll" scroll-x><view class="category-list"><view wx:for="{{a}}" wx:for-item="item" wx:key="c" class="{{['category-item', item.b && 'active']}}" bindtap="{{item.d}}">{{item.a}}</view></view></scroll-view><view class="news-list"><view wx:for="{{b}}" wx:for-item="item" wx:key="j" class="news-item card-section fade-in" bindtap="{{item.k}}"><view class="news-content"><view class="news-info"><text class="news-title">{{item.a}}</text><text class="news-desc">{{item.b}}</text><view class="news-meta"><text class="news-time">{{item.c}}</text><text class="news-category">{{item.d}}</text></view></view><image wx:if="{{item.e}}" src="{{item.f}}" class="news-image" mode="aspectFill"></image></view><view class="news-stats"><view class="stat-item"><image src="{{c}}" class="stat-icon"></image><text class="stat-text">{{item.g}}</text></view><view class="stat-item"><image src="{{d}}" class="stat-icon"></image><text class="stat-text">{{item.h}}</text></view><view class="stat-item"><image src="{{e}}" class="stat-icon"></image><text class="stat-text">{{item.i}}</text></view></view></view><view wx:if="{{f}}" class="load-more" bindtap="{{g}}"> 加载更多 </view><view wx:if="{{h}}" class="loading"> 加载中... </view><view wx:if="{{i}}" class="no-more"> 没有更多了 </view><view wx:if="{{j}}" class="empty-list"> 暂无资讯 </view></view><fab-buttons u-i="1cc8ddce-0" bind:__l="__l"/></view>