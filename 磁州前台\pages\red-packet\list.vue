<template>
  <view class="red-packet-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-left" @click="goBack">
        <text class="iconfont icon-back"></text>
      </view>
      <view class="navbar-title">抢红包</view>
      <view class="navbar-right">
        <text class="iconfont icon-filter" @click="showFilter"></text>
      </view>
    </view>
    
    <!-- 顶部统计信息 -->
    <view class="stats-section">
      <view class="stats-card">
        <view class="stats-value">{{totalAmount}}元</view>
        <view class="stats-label">累计发放</view>
      </view>
      <view class="stats-card">
        <view class="stats-value">{{myAmount}}元</view>
        <view class="stats-label">我的收益</view>
      </view>
      <view class="stats-card">
        <view class="stats-value">{{todayCount}}个</view>
        <view class="stats-label">今日红包</view>
      </view>
    </view>
    
    <!-- 红包类型选择 -->
    <view class="filter-tabs">
      <view 
        class="filter-tab" 
        :class="{'active': activeTab === 'all'}"
        @click="switchTab('all')">
        全部红包
      </view>
      <view 
        class="filter-tab" 
        :class="{'active': activeTab === 'merchant'}"
        @click="switchTab('merchant')">
        商家红包
      </view>
      <view 
        class="filter-tab" 
        :class="{'active': activeTab === 'platform'}"
        @click="switchTab('platform')">
        平台红包
      </view>
      <view 
        class="filter-tab" 
        :class="{'active': activeTab === 'info'}"
        @click="switchTab('info')">
        信息红包
      </view>
      <view 
        class="filter-tab" 
        :class="{'active': activeTab === 'nearby'}"
        @click="switchTab('nearby')">
        附近红包
      </view>
    </view>
    
    <!-- 红包列表 -->
    <scroll-view 
      scroll-y 
      class="red-packet-list"
      @scrolltolower="loadMore"
      :refresher-enabled="true"
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh">
      
      <view v-if="loading && !redPackets.length" class="loading-placeholder">
        <uni-load-more status="loading" :contentText="loadingText"></uni-load-more>
      </view>
      
      <view v-else-if="!redPackets.length" class="empty-state">
        <image class="empty-image" src="/static/images/empty-redpacket.png" mode="aspectFit"></image>
        <text class="empty-text">暂无红包，下拉刷新试试</text>
      </view>
      
      <view v-else>
        <view 
          class="red-packet-item" 
          :class="{'info-red-packet-item': item.isInfoRedPacket}"
          v-for="(item, index) in redPackets" 
          :key="item.id"
          @click="navigateToDetail(item)">
          
          <!-- 商家/用户信息 -->
          <view class="merchant-info">
            <image class="merchant-avatar" :src="item.merchantAvatar" mode="aspectFill"></image>
            <view class="merchant-details">
              <text class="merchant-name">{{item.merchantName}}</text>
              <text class="publish-time">{{item.isInfoRedPacket ? item.time : formatTime(item.publishTime)}}</text>
            </view>
            <view class="info-type-tag" v-if="item.isInfoRedPacket">{{item.category}}</view>
          </view>
          
          <!-- 内容区域 - 针对信息红包简化 -->
          <view class="content-section" :class="{'info-content-section': item.isInfoRedPacket}">
            <!-- 信息红包 -->
            <template v-if="item.isInfoRedPacket">
              <text class="info-title">{{item.content}}</text>
              
              <!-- 红包信息简化展示 -->
              <view class="info-red-packet-bar">
                <view class="red-packet-icon-mini">
                  <image src="/static/images/tabbar/抢红包.gif" mode="aspectFit"></image>
                </view>
                <text class="red-packet-amount-mini">¥{{item.redPacketAmount}} · 剩{{item.redPacketRemain}}个</text>
                <view class="grab-button-mini">抢</view>
              </view>
            </template>
            
            <!-- 普通红包保持原样 -->
            <template v-else>
              <view class="info-content">
                <view class="title-row">
                  <text class="title">{{item.title}}</text>
                </view>
                <text class="description">{{item.description}}</text>
                
                <view class="image-list" v-if="item.images && item.images.length">
                  <image 
                    v-for="(img, imgIndex) in item.images.slice(0, 3)" 
                    :key="imgIndex"
                    :src="img"
                    mode="aspectFill"
                    class="content-image"
                    @click.stop="previewImage(item.images, imgIndex)"></image>
                  <view class="image-count" v-if="item.images.length > 3">+{{item.images.length - 3}}</view>
                </view>
              </view>
              
              <view class="red-packet-info">
                <view class="red-packet-icon">
                  <image src="/static/images/red-packet-icon.png" mode="aspectFit"></image>
                </view>
                <view class="red-packet-details">
                  <text class="packet-amount">¥{{item.amount}}</text>
                  <text class="packet-type">{{getPacketTypeText(item.packetType)}}</text>
                </view>
                <view class="packet-status" :class="getStatusClass(item.status)">
                  <text>{{getStatusText(item.status)}}</text>
                </view>
              </view>
            </template>
          </view>
          
          <!-- 底部操作栏 - 仅为普通红包显示 -->
          <view class="action-bar" v-if="!item.isInfoRedPacket">
            <view class="action-stats">
              <text class="stat-item">
                <text class="iconfont icon-view"></text>
                {{item.viewCount}}
              </text>
              <text class="stat-item">
                <text class="iconfont icon-comment"></text>
                {{item.commentCount}}
              </text>
              <text class="stat-item">
                <text class="iconfont icon-like"></text>
                {{item.likeCount}}
              </text>
            </view>
            <view class="action-buttons">
              <button 
                class="grab-btn"
                :class="{'disabled': item.status !== 0 || item.hasGrabbed}"
                @click.stop="grabRedPacket(item, index)">
                {{item.hasGrabbed ? '已领取' : (item.status === 0 ? '抢红包' : '已结束')}}
              </button>
            </view>
          </view>
        </view>
        
        <uni-load-more :status="loadMoreStatus" :contentText="loadMoreText"></uni-load-more>
      </view>
    </scroll-view>
    
    <!-- 底部发布按钮 -->
    <view class="publish-btn" @click="navigateToPublish">
      <text class="iconfont icon-add"></text>
      <text>发布红包</text>
    </view>
  </view>
</template>

<script>
import { formatTime, formatDistance } from '@/utils/format';

export default {
  data() {
    return {
      activeTab: 'all',
      redPackets: [],
      loading: true,
      isRefreshing: false,
      loadMoreStatus: 'more', // more, loading, noMore
      page: 1,
      pageSize: 10,
      totalAmount: '0.00',
      myAmount: '0.00',
      todayCount: 0,
      loadingText: {
        contentdown: '上拉加载更多',
        contentrefresh: '加载中...',
        contentnomore: '没有更多了'
      },
      loadMoreText: {
        contentdown: '上拉加载更多',
        contentrefresh: '加载中...',
        contentnomore: '没有更多了'
      }
    };
  },
  
  onLoad(options) {
    // 如果有tab参数，则切换到对应标签
    if (options && options.tab) {
      this.activeTab = options.tab;
    }
    
    this.loadRedPackets();
    this.loadStats();
  },
  
  methods: {
    goBack() {
      uni.navigateBack();
    },
    
    showFilter() {
      uni.showToast({
        title: '筛选功能开发中',
        icon: 'none'
      });
    },
    
    switchTab(tab) {
      if (this.activeTab === tab) return;
      
      this.activeTab = tab;
      this.redPackets = [];
      this.page = 1;
      this.loadRedPackets();
    },
    
    async loadStats() {
      try {
        // 模拟数据，实际项目中应该从API获取
        this.totalAmount = '8,888.88';
        this.myAmount = '128.88';
        this.todayCount = 56;
      } catch (error) {
        console.error('加载统计数据失败', error);
      }
    },
    
    async loadRedPackets(isRefresh = false) {
      if (isRefresh) {
        this.page = 1;
      }
      
      if (this.page === 1) {
        this.loading = true;
      } else {
        this.loadMoreStatus = 'loading';
      }
      
      try {
        // 模拟API请求，实际项目中应该调用真实API
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 模拟数据
        const mockData = this.getMockData();
        
        if (this.page === 1) {
          this.redPackets = mockData;
        } else {
          this.redPackets = [...this.redPackets, ...mockData];
        }
        
        // 模拟没有更多数据
        if (this.page >= 3) {
          this.loadMoreStatus = 'noMore';
        } else {
          this.loadMoreStatus = 'more';
          this.page++;
        }
      } catch (error) {
        console.error('加载红包列表失败', error);
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      } finally {
        this.loading = false;
        if (isRefresh) {
          this.isRefreshing = false;
        }
      }
    },
    
    // 获取模拟数据
    getMockData() {
      const types = ['normal', 'lucky', 'fixed'];
      const statuses = [0, 0, 0, 0, 1, 2]; // 大部分是进行中的红包
      
      // 根据当前选中的标签筛选数据
      let mockData = [];
      
      if (this.activeTab === 'info') {
        // 信息红包数据
        mockData = Array.from({ length: 10 }, (_, i) => {
          const infoType = this.getRandomInfoType();
          const title = this.getRandomInfoTitle();
          const description = this.getRandomInfoDescription();
          
          return {
            id: `info_rp_${this.page}_${i}`,
            merchantId: `user_${i}`,
            merchantName: `${this.getRandomUserName()}`,
            merchantAvatar: `/static/images/avatar_${(i % 5) + 1}.png`,
            title: title,
            description: description,
            images: this.getRandomImages(i),
            amount: (Math.random() * 50 + 5).toFixed(2),
            packetType: types[Math.floor(Math.random() * types.length)],
            status: statuses[Math.floor(Math.random() * statuses.length)],
            publishTime: new Date(Date.now() - Math.random() * 86400000 * 7).getTime(),
            distance: Math.random() * 5000,
            viewCount: Math.floor(Math.random() * 1000),
            commentCount: Math.floor(Math.random() * 50),
            likeCount: Math.floor(Math.random() * 100),
            hasGrabbed: Math.random() > 0.7,
            infoType: infoType, // 信息类型
            isInfoRedPacket: true, // 标记为信息红包
            
            // 添加与首页信息一致的字段
            category: infoType,
            content: title,
            time: formatTime(new Date(Date.now() - Math.random() * 86400000 * 7).getTime(), 'YYYY-MM-DD HH:mm'),
            views: Math.floor(Math.random() * 1000),
            hasRedPacket: true,
            redPacketAmount: (Math.random() * 50 + 5).toFixed(2),
            redPacketType: types[Math.floor(Math.random() * types.length)],
            redPacketCount: Math.floor(Math.random() * 50) + 10,
            redPacketRemain: Math.floor(Math.random() * 10) + 1
          };
        });
      } else {
        // 原有的红包数据
        mockData = Array.from({ length: 10 }, (_, i) => ({
          id: `rp_${this.page}_${i}`,
          merchantId: `m_${i}`,
          merchantName: `${this.getRandomBusinessName()}`,
          merchantAvatar: `/static/images/avatar_${(i % 5) + 1}.png`,
          title: this.getRandomTitle(),
          description: this.getRandomDescription(),
          images: this.getRandomImages(i),
          amount: (Math.random() * 100 + 5).toFixed(2),
          packetType: types[Math.floor(Math.random() * types.length)],
          status: statuses[Math.floor(Math.random() * statuses.length)],
          publishTime: new Date(Date.now() - Math.random() * 86400000 * 7).getTime(),
          distance: Math.random() * 5000,
          viewCount: Math.floor(Math.random() * 1000),
          commentCount: Math.floor(Math.random() * 50),
          likeCount: Math.floor(Math.random() * 100),
          hasGrabbed: Math.random() > 0.7,
          isInfoRedPacket: false // 标记为非信息红包
        }));
      }
      
      return mockData;
    },
    
    getRandomBusinessName() {
      const names = [
        '品味咖啡馆', '悦享美食城', '鲜果工坊', '时尚服饰店',
        '健康生活馆', '数码科技店', '宠物乐园', '美丽花坊',
        '家居生活馆', '创意礼品店'
      ];
      return names[Math.floor(Math.random() * names.length)];
    },
    
    getRandomTitle() {
      const titles = [
        '开业大酬宾，红包雨来袭！',
        '周年庆典，感恩回馈！',
        '新品上市，抢先体验！',
        '限时特惠，错过等一年！',
        '会员专享，双倍红包！',
        '春节特辑，财神送礼！',
        '夏日清凉，冰爽红包！',
        '金秋送爽，丰收好礼！',
        '冬季温暖，暖心红包！',
        '五一劳动节，犒劳自己！'
      ];
      return titles[Math.floor(Math.random() * titles.length)];
    },
    
    getRandomDescription() {
      const descriptions = [
        '店庆活动期间，凡是到店消费满100元即可参与抽红包活动，最高可得88元现金红包！',
        '为回馈新老顾客，本店特推出线上抢红包活动，抢到红包可直接抵扣消费金额！',
        '关注我们的公众号，参与互动即可获得神秘红包，数量有限，先到先得！',
        '新店开业，发放100个幸运红包，金额随机，最高可得200元！',
        '五星好评送红包，晒图送红包，分享朋友圈再送红包，红包不停！',
        '年终促销，下单立减，还有机会获得平台补贴的超级红包！'
      ];
      return descriptions[Math.floor(Math.random() * descriptions.length)];
    },
    
    getRandomImages(seed) {
      const count = Math.floor(Math.random() * 5) + 1;
      return Array.from({ length: count }, (_, i) => 
        `/static/images/sample_${((seed + i) % 8) + 1}.jpg`
      );
    },
    
    formatTime(timestamp) {
      return formatTime(timestamp);
    },
    
    formatDistance(meters) {
      return formatDistance(meters);
    },
    
    getPacketTypeText(type) {
      const typeMap = {
        'normal': '普通红包',
        'lucky': '拼手气红包',
        'fixed': '固定金额'
      };
      return typeMap[type] || '红包';
    },
    
    getStatusText(status) {
      const statusMap = {
        0: '进行中',
        1: '已领完',
        2: '已过期'
      };
      return statusMap[status] || '未知状态';
    },
    
    getStatusClass(status) {
      const classMap = {
        0: 'status-active',
        1: 'status-finished',
        2: 'status-expired'
      };
      return classMap[status] || '';
    },
    
    navigateToDetail(item) {
      // 如果是信息红包，使用与首页相同的导航逻辑
      if (item.isInfoRedPacket) {
        try {
          // 准备参数
          let params = {
            id: encodeURIComponent(item.id),
            category: encodeURIComponent(item.infoType),
            content: encodeURIComponent(item.description || '')
          };
          
          // 根据信息类型选择对应的详情页
          const detailPageMap = {
            '到家服务': 'home-service-detail',
            '寻找服务': 'find-service-detail',
            '生意转让': 'business-transfer-detail',
            '商业转让': 'business-transfer-detail',
            '招聘信息': 'job-detail',
            '求职信息': 'job-seeking-detail',
            '房屋出租': 'house-rent-detail',
            '房屋出售': 'house-sale-detail',
            '二手车辆': 'car-detail',
            '宠物信息': 'pet-detail',
            '车辆服务': 'vehicle-service-detail',
            '二手闲置': 'second-hand-detail',
            '磁州拼车': 'carpool-detail',
            '教育培训': 'education-detail',
            '其他服务': 'info-detail'
          };
          
          const detailPage = detailPageMap[item.infoType] || 'info-detail';
          const url = `/pages/publish/${detailPage}?${Object.entries(params)
            .map(([key, value]) => `${key}=${value}`)
            .join('&')}`;
          
          uni.navigateTo({
            url: url,
            success: () => {
              console.log('成功跳转到信息详情页:', url);
            },
            fail: (e) => {
              console.error('跳转信息详情页失败', e);
              uni.showToast({
                title: '跳转失败，请重试',
                icon: 'none'
              });
            }
          });
        } catch (error) {
          console.error('导航异常', error);
          uni.showToast({
            title: '页面跳转出错',
            icon: 'none'
          });
        }
      } else {
        // 普通红包跳转到红包详情页
        uni.navigateTo({
          url: `/pages/red-packet/detail?id=${item.id}`
        });
      }
    },
    
    navigateToPublish() {
      uni.navigateTo({
        url: '/pages/publish/publish?type=redpacket'
      });
    },
    
    async grabRedPacket(item, index) {
      if (item.status !== 0 || item.hasGrabbed) return;
      
      try {
        // 模拟抢红包请求
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // 随机决定是否抢到
        const success = Math.random() > 0.3;
        
        if (success) {
          const amount = (Math.random() * 10 + 0.5).toFixed(2);
          
          uni.showModal({
            title: '恭喜您',
            content: `抢到了${amount}元红包`,
            showCancel: false,
            success: () => {
              // 更新红包状态
              this.redPackets[index].hasGrabbed = true;
              
              // 更新我的收益
              this.myAmount = (parseFloat(this.myAmount) + parseFloat(amount)).toFixed(2);
            }
          });
        } else {
          uni.showToast({
            title: '手慢了，红包被抢光了',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('抢红包失败', error);
        uni.showToast({
          title: '网络异常，请重试',
          icon: 'none'
        });
      }
    },
    
    previewImage(images, current) {
      uni.previewImage({
        urls: images,
        current: images[current]
      });
    },
    
    loadMore() {
      if (this.loadMoreStatus !== 'more') return;
      this.loadRedPackets();
    },
    
    onRefresh() {
      this.isRefreshing = true;
      this.loadRedPackets(true);
    },
    
    getRandomUserName() {
      const names = [
        '张先生', '李女士', '王师傅', '赵经理', 
        '刘老师', '陈总', '杨女士', '周先生',
        '吴小姐', '郑经理'
      ];
      return names[Math.floor(Math.random() * names.length)];
    },
    
    getRandomInfoTitle() {
      const titles = [
        '急招送餐员，日结工资+红包奖励',
        '招聘销售人员，底薪3500+提成+红包',
        '高薪诚聘厨师，包吃住，红包福利',
        '招聘保洁人员，待遇优厚，红包补贴',
        '诚聘前台接待，形象气质佳，红包奖励',
        '招聘美容师，有经验者优先，红包补助',
        '招聘仓库管理员，有红包福利',
        '诚聘电工，持证上岗，红包奖励',
        '招聘会计，经验丰富，红包补贴',
        '招聘司机，C1以上，红包奖励'
      ];
      return titles[Math.floor(Math.random() * titles.length)];
    },
    
    getRandomInfoDescription() {
      const descriptions = [
        '工作地点：市中心，工作时间：9:00-18:00，周末双休，节假日三倍工资，面试通过即可领取入职红包！',
        '有责任心，能吃苦耐劳，有相关工作经验者优先，完成业绩有额外红包奖励！',
        '要求有相关工作经验，能够独立完成工作任务，有团队合作精神，推荐成功有红包奖励！',
        '工作轻松，环境优雅，五险一金，带薪年假，节日福利，红包不断！',
        '要求：形象气质佳，沟通能力强，有相关工作经验，入职即发红包！',
        '薪资待遇：3500-6000元/月，提供住宿，有红包奖励计划！',
        '招聘岗位多个，薪资面议，有意者带上简历前来面试，红包等你拿！',
        '有相关工作经验者优先，能够适应倒班，有责任心，推荐成功有红包！',
        '要求会基本办公软件操作，有相关工作经验，红包福利多多！',
        '工作稳定，待遇优厚，有晋升空间，入职就有红包奖励！'
      ];
      return descriptions[Math.floor(Math.random() * descriptions.length)];
    },
    
    getRandomInfoType() {
      const types = [
        '招聘信息', '求职信息', '房屋出租', '二手闲置',
        '生意转让', '车辆服务', '到家服务', '教育培训'
      ];
      return types[Math.floor(Math.random() * types.length)];
    }
  }
};
</script>

<style lang="scss">
.red-packet-container {
  min-height: 100vh;
  background-color: #f7f7f7;
  position: relative;
}

/* 自定义导航栏 */
.custom-navbar {
  height: 90rpx;
  padding: var(--status-bar-height) 30rpx 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  color: #fff;
  position: sticky;
  top: 0;
  z-index: 100;
  
  .navbar-left, .navbar-right {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .iconfont {
      font-size: 40rpx;
    }
  }
  
  .navbar-title {
    font-size: 36rpx;
    font-weight: 500;
  }
}

/* 统计信息区域 */
.stats-section {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  color: #fff;
  border-bottom-left-radius: 30rpx;
  border-bottom-right-radius: 30rpx;
  box-shadow: 0 6rpx 16rpx rgba(255, 107, 107, 0.2);
  
  .stats-card {
    text-align: center;
    flex: 1;
    
    .stats-value {
      font-size: 40rpx;
      font-weight: bold;
      margin-bottom: 10rpx;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
    }
    
    .stats-label {
      font-size: 24rpx;
      opacity: 0.9;
    }
  }
}

/* 筛选标签 */
.filter-tabs {
  display: flex;
  background-color: #fff;
  padding: 20rpx 30rpx;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  .filter-tab {
    flex: 1;
    text-align: center;
    font-size: 28rpx;
    color: #666;
    padding: 16rpx 0;
    position: relative;
    transition: all 0.3s;
    
    &.active {
      color: #FF6B6B;
      font-weight: 500;
      
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40rpx;
        height: 6rpx;
        background-color: #FF6B6B;
        border-radius: 3rpx;
      }
    }
  }
}

/* 红包列表 */
.red-packet-list {
  height: calc(100vh - 90rpx - var(--status-bar-height) - 170rpx - 100rpx);
  padding: 0 20rpx;
}

.loading-placeholder, .empty-state {
  padding: 100rpx 0;
  text-align: center;
  
  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
  }
  
  .empty-text {
    color: #999;
    font-size: 28rpx;
  }
}

.red-packet-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  .merchant-info {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    
    .merchant-avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      margin-right: 20rpx;
    }
    
    .merchant-details {
      flex: 1;
      
      .merchant-name {
        font-size: 30rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 6rpx;
      }
      
      .publish-time {
        font-size: 24rpx;
        color: #999;
      }
    }
    
    .info-type-tag {
      font-size: 22rpx;
      color: #fff;
      background-color: #1677FF;
      padding: 4rpx 12rpx;
      border-radius: 6rpx;
    }
  }
  
  .content-section {
    margin-bottom: 20rpx;
    
    .info-content {
      margin-bottom: 30rpx;
      
      .title-row {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;
        
        .title {
          font-size: 32rpx;
          font-weight: 500;
          color: #333;
          line-height: 1.4;
          flex: 1;
        }
      }
      
      .description {
        font-size: 28rpx;
        color: #666;
        line-height: 1.5;
        margin-bottom: 20rpx;
      }
      
      .image-list {
        display: flex;
        flex-wrap: wrap;
        margin: 0 -5rpx;
        
        .content-image {
          width: calc(33.33% - 10rpx);
          height: 200rpx;
          margin: 5rpx;
          border-radius: 8rpx;
          background-color: #f5f5f5;
        }
        
        .image-count {
          position: absolute;
          right: 20rpx;
          bottom: 20rpx;
          background-color: rgba(0, 0, 0, 0.5);
          color: #fff;
          font-size: 24rpx;
          padding: 6rpx 12rpx;
          border-radius: 20rpx;
        }
      }
    }
    
    .red-packet-info {
      display: flex;
      align-items: center;
      background-color: #FFF7F7;
      padding: 20rpx;
      border-radius: 12rpx;
      border: 1rpx solid #FFE0E0;
      
      .red-packet-icon {
        width: 80rpx;
        height: 80rpx;
        margin-right: 20rpx;
        
        image {
          width: 100%;
          height: 100%;
        }
      }
      
      .red-packet-details {
        flex: 1;
        
        .packet-amount {
          font-size: 36rpx;
          color: #FF4D4F;
          font-weight: bold;
          margin-bottom: 6rpx;
        }
        
        .packet-type {
          font-size: 24rpx;
          color: #FF7875;
        }
      }
      
      .packet-status {
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        
        &.status-active {
          background-color: #FF4D4F;
          color: #fff;
        }
        
        &.status-finished {
          background-color: #999;
          color: #fff;
        }
        
        &.status-expired {
          background-color: #ccc;
          color: #fff;
        }
      }
    }
  }
  
  .action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20rpx;
    border-top: 1rpx solid #f5f5f5;
    
    .action-stats {
      display: flex;
      
      .stat-item {
        margin-right: 30rpx;
        font-size: 24rpx;
        color: #999;
        display: flex;
        align-items: center;
        
        .iconfont {
          margin-right: 6rpx;
          font-size: 28rpx;
        }
      }
    }
    
    .action-buttons {
      .grab-btn {
        background: linear-gradient(135deg, #FF4D4F, #FF7875);
        color: #fff;
        font-size: 26rpx;
        padding: 10rpx 30rpx;
        border-radius: 30rpx;
        border: none;
        box-shadow: 0 4rpx 8rpx rgba(255, 77, 79, 0.2);
        
        &.disabled {
          background: #ccc;
          box-shadow: none;
        }
      }
    }
  }
}

/* 发布按钮 */
.publish-btn {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  width: 180rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #FF4D4F, #FF7875);
  color: #fff;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  box-shadow: 0 6rpx 16rpx rgba(255, 77, 79, 0.3);
  z-index: 99;
  
  .iconfont {
    margin-right: 10rpx;
    font-size: 32rpx;
  }
}

/* 信息红包特有样式 - 精简版 */
.info-red-packet-item {
  border-left: 4rpx solid #FF4D4F;
  padding: 20rpx;
}

.info-content-section {
  margin-bottom: 0;
}

.info-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.info-type-tag {
  font-size: 22rpx;
  color: #fff;
  background-color: #1677FF;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
}

.info-red-packet-bar {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
  
  .red-packet-icon-mini {
    width: 40rpx;
    height: 40rpx;
    margin-right: 10rpx;
    
    image {
      width: 100%;
      height: 100%;
    }
  }
  
  .red-packet-amount-mini {
    flex: 1;
    font-size: 26rpx;
    color: #FF4D4F;
  }
  
  .grab-button-mini {
    background: linear-gradient(135deg, #FF4D4F, #FF7875);
    color: #fff;
    font-size: 24rpx;
    width: 50rpx;
    height: 50rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2rpx 6rpx rgba(255, 77, 79, 0.2);
  }
}
</style> 