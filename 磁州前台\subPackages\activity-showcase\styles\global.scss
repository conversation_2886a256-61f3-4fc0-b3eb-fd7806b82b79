/* 磁州前台全局样式 */

/* CSS变量定义 */
:root {
  /* 主题色彩 */
  --primary-color: #795548;
  --primary-light: #A1887F;
  --primary-dark: #5D4037;
  --secondary-color: #FF9800;
  --secondary-light: #FFB74D;
  --secondary-dark: #F57C00;
  
  /* 功能色彩 */
  --success-color: #4CAF50;
  --warning-color: #FF9800;
  --error-color: #F44336;
  --info-color: #2196F3;
  
  /* 中性色彩 */
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --text-disabled: #CCCCCC;
  
  /* 背景色彩 */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F8F9FA;
  --bg-tertiary: #F5F5F5;
  --bg-overlay: rgba(0, 0, 0, 0.5);
  
  /* 边框色彩 */
  --border-light: #F0F0F0;
  --border-medium: #E0E0E0;
  --border-dark: #CCCCCC;
  
  /* 阴影 */
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.1);
  --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.15);
  
  /* 圆角 */
  --radius-small: 4px;
  --radius-medium: 8px;
  --radius-large: 12px;
  --radius-round: 50%;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
  
  /* 字体大小 */
  --font-xs: 10px;
  --font-sm: 12px;
  --font-md: 14px;
  --font-lg: 16px;
  --font-xl: 18px;
  --font-xxl: 20px;
  --font-title: 24px;
  --font-hero: 32px;
  
  /* 行高 */
  --line-height-tight: 1.2;
  --line-height-normal: 1.4;
  --line-height-relaxed: 1.6;
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* 重置样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* 页面基础样式 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  font-size: var(--font-md);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 通用工具类 */

/* 文本相关 */
.text-primary { color: var(--text-primary) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-tertiary { color: var(--text-tertiary) !important; }
.text-disabled { color: var(--text-disabled) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-error { color: var(--error-color) !important; }
.text-info { color: var(--info-color) !important; }

.text-xs { font-size: var(--font-xs) !important; }
.text-sm { font-size: var(--font-sm) !important; }
.text-md { font-size: var(--font-md) !important; }
.text-lg { font-size: var(--font-lg) !important; }
.text-xl { font-size: var(--font-xl) !important; }
.text-xxl { font-size: var(--font-xxl) !important; }
.text-title { font-size: var(--font-title) !important; }
.text-hero { font-size: var(--font-hero) !important; }

.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }

.font-normal { font-weight: 400 !important; }
.font-medium { font-weight: 500 !important; }
.font-semibold { font-weight: 600 !important; }
.font-bold { font-weight: 700 !important; }

.line-height-tight { line-height: var(--line-height-tight) !important; }
.line-height-normal { line-height: var(--line-height-normal) !important; }
.line-height-relaxed { line-height: var(--line-height-relaxed) !important; }

/* 背景相关 */
.bg-primary { background-color: var(--bg-primary) !important; }
.bg-secondary { background-color: var(--bg-secondary) !important; }
.bg-tertiary { background-color: var(--bg-tertiary) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-error { background-color: var(--error-color) !important; }
.bg-info { background-color: var(--info-color) !important; }

/* 边框相关 */
.border { border: 1px solid var(--border-medium) !important; }
.border-light { border: 1px solid var(--border-light) !important; }
.border-dark { border: 1px solid var(--border-dark) !important; }
.border-none { border: none !important; }

.border-t { border-top: 1px solid var(--border-medium) !important; }
.border-r { border-right: 1px solid var(--border-medium) !important; }
.border-b { border-bottom: 1px solid var(--border-medium) !important; }
.border-l { border-left: 1px solid var(--border-medium) !important; }

/* 圆角相关 */
.rounded-none { border-radius: 0 !important; }
.rounded-sm { border-radius: var(--radius-small) !important; }
.rounded { border-radius: var(--radius-medium) !important; }
.rounded-lg { border-radius: var(--radius-large) !important; }
.rounded-full { border-radius: var(--radius-round) !important; }

/* 阴影相关 */
.shadow-none { box-shadow: none !important; }
.shadow-light { box-shadow: var(--shadow-light) !important; }
.shadow-medium { box-shadow: var(--shadow-medium) !important; }
.shadow-heavy { box-shadow: var(--shadow-heavy) !important; }

/* 间距相关 */
.m-0 { margin: 0 !important; }
.m-xs { margin: var(--spacing-xs) !important; }
.m-sm { margin: var(--spacing-sm) !important; }
.m-md { margin: var(--spacing-md) !important; }
.m-lg { margin: var(--spacing-lg) !important; }
.m-xl { margin: var(--spacing-xl) !important; }
.m-xxl { margin: var(--spacing-xxl) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-xs { margin-top: var(--spacing-xs) !important; }
.mt-sm { margin-top: var(--spacing-sm) !important; }
.mt-md { margin-top: var(--spacing-md) !important; }
.mt-lg { margin-top: var(--spacing-lg) !important; }
.mt-xl { margin-top: var(--spacing-xl) !important; }
.mt-xxl { margin-top: var(--spacing-xxl) !important; }

.mr-0 { margin-right: 0 !important; }
.mr-xs { margin-right: var(--spacing-xs) !important; }
.mr-sm { margin-right: var(--spacing-sm) !important; }
.mr-md { margin-right: var(--spacing-md) !important; }
.mr-lg { margin-right: var(--spacing-lg) !important; }
.mr-xl { margin-right: var(--spacing-xl) !important; }
.mr-xxl { margin-right: var(--spacing-xxl) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-xs { margin-bottom: var(--spacing-xs) !important; }
.mb-sm { margin-bottom: var(--spacing-sm) !important; }
.mb-md { margin-bottom: var(--spacing-md) !important; }
.mb-lg { margin-bottom: var(--spacing-lg) !important; }
.mb-xl { margin-bottom: var(--spacing-xl) !important; }
.mb-xxl { margin-bottom: var(--spacing-xxl) !important; }

.ml-0 { margin-left: 0 !important; }
.ml-xs { margin-left: var(--spacing-xs) !important; }
.ml-sm { margin-left: var(--spacing-sm) !important; }
.ml-md { margin-left: var(--spacing-md) !important; }
.ml-lg { margin-left: var(--spacing-lg) !important; }
.ml-xl { margin-left: var(--spacing-xl) !important; }
.ml-xxl { margin-left: var(--spacing-xxl) !important; }

.mx-0 { margin-left: 0 !important; margin-right: 0 !important; }
.mx-xs { margin-left: var(--spacing-xs) !important; margin-right: var(--spacing-xs) !important; }
.mx-sm { margin-left: var(--spacing-sm) !important; margin-right: var(--spacing-sm) !important; }
.mx-md { margin-left: var(--spacing-md) !important; margin-right: var(--spacing-md) !important; }
.mx-lg { margin-left: var(--spacing-lg) !important; margin-right: var(--spacing-lg) !important; }
.mx-xl { margin-left: var(--spacing-xl) !important; margin-right: var(--spacing-xl) !important; }
.mx-xxl { margin-left: var(--spacing-xxl) !important; margin-right: var(--spacing-xxl) !important; }

.my-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
.my-xs { margin-top: var(--spacing-xs) !important; margin-bottom: var(--spacing-xs) !important; }
.my-sm { margin-top: var(--spacing-sm) !important; margin-bottom: var(--spacing-sm) !important; }
.my-md { margin-top: var(--spacing-md) !important; margin-bottom: var(--spacing-md) !important; }
.my-lg { margin-top: var(--spacing-lg) !important; margin-bottom: var(--spacing-lg) !important; }
.my-xl { margin-top: var(--spacing-xl) !important; margin-bottom: var(--spacing-xl) !important; }
.my-xxl { margin-top: var(--spacing-xxl) !important; margin-bottom: var(--spacing-xxl) !important; }

.p-0 { padding: 0 !important; }
.p-xs { padding: var(--spacing-xs) !important; }
.p-sm { padding: var(--spacing-sm) !important; }
.p-md { padding: var(--spacing-md) !important; }
.p-lg { padding: var(--spacing-lg) !important; }
.p-xl { padding: var(--spacing-xl) !important; }
.p-xxl { padding: var(--spacing-xxl) !important; }

.pt-0 { padding-top: 0 !important; }
.pt-xs { padding-top: var(--spacing-xs) !important; }
.pt-sm { padding-top: var(--spacing-sm) !important; }
.pt-md { padding-top: var(--spacing-md) !important; }
.pt-lg { padding-top: var(--spacing-lg) !important; }
.pt-xl { padding-top: var(--spacing-xl) !important; }
.pt-xxl { padding-top: var(--spacing-xxl) !important; }

.pr-0 { padding-right: 0 !important; }
.pr-xs { padding-right: var(--spacing-xs) !important; }
.pr-sm { padding-right: var(--spacing-sm) !important; }
.pr-md { padding-right: var(--spacing-md) !important; }
.pr-lg { padding-right: var(--spacing-lg) !important; }
.pr-xl { padding-right: var(--spacing-xl) !important; }
.pr-xxl { padding-right: var(--spacing-xxl) !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-xs { padding-bottom: var(--spacing-xs) !important; }
.pb-sm { padding-bottom: var(--spacing-sm) !important; }
.pb-md { padding-bottom: var(--spacing-md) !important; }
.pb-lg { padding-bottom: var(--spacing-lg) !important; }
.pb-xl { padding-bottom: var(--spacing-xl) !important; }
.pb-xxl { padding-bottom: var(--spacing-xxl) !important; }

.pl-0 { padding-left: 0 !important; }
.pl-xs { padding-left: var(--spacing-xs) !important; }
.pl-sm { padding-left: var(--spacing-sm) !important; }
.pl-md { padding-left: var(--spacing-md) !important; }
.pl-lg { padding-left: var(--spacing-lg) !important; }
.pl-xl { padding-left: var(--spacing-xl) !important; }
.pl-xxl { padding-left: var(--spacing-xxl) !important; }

.px-0 { padding-left: 0 !important; padding-right: 0 !important; }
.px-xs { padding-left: var(--spacing-xs) !important; padding-right: var(--spacing-xs) !important; }
.px-sm { padding-left: var(--spacing-sm) !important; padding-right: var(--spacing-sm) !important; }
.px-md { padding-left: var(--spacing-md) !important; padding-right: var(--spacing-md) !important; }
.px-lg { padding-left: var(--spacing-lg) !important; padding-right: var(--spacing-lg) !important; }
.px-xl { padding-left: var(--spacing-xl) !important; padding-right: var(--spacing-xl) !important; }
.px-xxl { padding-left: var(--spacing-xxl) !important; padding-right: var(--spacing-xxl) !important; }

.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.py-xs { padding-top: var(--spacing-xs) !important; padding-bottom: var(--spacing-xs) !important; }
.py-sm { padding-top: var(--spacing-sm) !important; padding-bottom: var(--spacing-sm) !important; }
.py-md { padding-top: var(--spacing-md) !important; padding-bottom: var(--spacing-md) !important; }
.py-lg { padding-top: var(--spacing-lg) !important; padding-bottom: var(--spacing-lg) !important; }
.py-xl { padding-top: var(--spacing-xl) !important; padding-bottom: var(--spacing-xl) !important; }
.py-xxl { padding-top: var(--spacing-xxl) !important; padding-bottom: var(--spacing-xxl) !important; }

/* 布局相关 */
.flex { display: flex !important; }
.inline-flex { display: inline-flex !important; }
.block { display: block !important; }
.inline-block { display: inline-block !important; }
.hidden { display: none !important; }

.flex-row { flex-direction: row !important; }
.flex-col { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

.items-start { align-items: flex-start !important; }
.items-center { align-items: center !important; }
.items-end { align-items: flex-end !important; }
.items-stretch { align-items: stretch !important; }

.justify-start { justify-content: flex-start !important; }
.justify-center { justify-content: center !important; }
.justify-end { justify-content: flex-end !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }
.justify-evenly { justify-content: space-evenly !important; }

.flex-1 { flex: 1 !important; }
.flex-auto { flex: auto !important; }
.flex-none { flex: none !important; }

/* 定位相关 */
.relative { position: relative !important; }
.absolute { position: absolute !important; }
.fixed { position: fixed !important; }
.sticky { position: sticky !important; }

.top-0 { top: 0 !important; }
.right-0 { right: 0 !important; }
.bottom-0 { bottom: 0 !important; }
.left-0 { left: 0 !important; }

/* 宽高相关 */
.w-full { width: 100% !important; }
.h-full { height: 100% !important; }
.w-auto { width: auto !important; }
.h-auto { height: auto !important; }

/* 溢出相关 */
.overflow-hidden { overflow: hidden !important; }
.overflow-auto { overflow: auto !important; }
.overflow-scroll { overflow: scroll !important; }
.overflow-visible { overflow: visible !important; }

/* 透明度相关 */
.opacity-0 { opacity: 0 !important; }
.opacity-25 { opacity: 0.25 !important; }
.opacity-50 { opacity: 0.5 !important; }
.opacity-75 { opacity: 0.75 !important; }
.opacity-100 { opacity: 1 !important; }

/* 过渡动画 */
.transition-fast { transition: all var(--transition-fast) !important; }
.transition-normal { transition: all var(--transition-normal) !important; }
.transition-slow { transition: all var(--transition-slow) !important; }

/* 变换相关 */
.transform { transform: translateZ(0) !important; }
.scale-95 { transform: scale(0.95) !important; }
.scale-100 { transform: scale(1) !important; }
.scale-105 { transform: scale(1.05) !important; }

/* 光标相关 */
.cursor-pointer { cursor: pointer !important; }
.cursor-default { cursor: default !important; }
.cursor-not-allowed { cursor: not-allowed !important; }

/* 用户选择 */
.select-none { user-select: none !important; }
.select-text { user-select: text !important; }
.select-all { user-select: all !important; }

/* 文本溢出 */
.truncate {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

.text-ellipsis-2 {
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important;
  -webkit-box-orient: vertical !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.text-ellipsis-3 {
  display: -webkit-box !important;
  -webkit-line-clamp: 3 !important;
  -webkit-box-orient: vertical !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 安全区域 */
.safe-area-top {
  padding-top: env(safe-area-inset-top) !important;
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom) !important;
}

.safe-area-left {
  padding-left: env(safe-area-inset-left) !important;
}

.safe-area-right {
  padding-right: env(safe-area-inset-right) !important;
}

/* 响应式断点 */
@media (max-width: 480px) {
  .sm\:hidden { display: none !important; }
  .sm\:block { display: block !important; }
  .sm\:flex { display: flex !important; }
  .sm\:text-sm { font-size: var(--font-sm) !important; }
  .sm\:text-md { font-size: var(--font-md) !important; }
  .sm\:p-sm { padding: var(--spacing-sm) !important; }
  .sm\:m-sm { margin: var(--spacing-sm) !important; }
}

@media (min-width: 481px) and (max-width: 768px) {
  .md\:hidden { display: none !important; }
  .md\:block { display: block !important; }
  .md\:flex { display: flex !important; }
  .md\:text-md { font-size: var(--font-md) !important; }
  .md\:text-lg { font-size: var(--font-lg) !important; }
  .md\:p-md { padding: var(--spacing-md) !important; }
  .md\:m-md { margin: var(--spacing-md) !important; }
}

@media (min-width: 769px) {
  .lg\:hidden { display: none !important; }
  .lg\:block { display: block !important; }
  .lg\:flex { display: flex !important; }
  .lg\:text-lg { font-size: var(--font-lg) !important; }
  .lg\:text-xl { font-size: var(--font-xl) !important; }
  .lg\:p-lg { padding: var(--spacing-lg) !important; }
  .lg\:m-lg { margin: var(--spacing-lg) !important; }
}

/* 磁州特色主题 */
.theme-cizhou {
  --primary-color: #8D6E63;
  --primary-light: #BCAAA4;
  --primary-dark: #5D4037;
  --secondary-color: #FF8A65;
  --secondary-light: #FFAB91;
  --secondary-dark: #FF5722;
}

.theme-cizhou-dark {
  --primary-color: #6D4C41;
  --primary-light: #8D6E63;
  --primary-dark: #3E2723;
  --bg-primary: #2C2C2C;
  --bg-secondary: #1E1E1E;
  --bg-tertiary: #121212;
  --text-primary: #FFFFFF;
  --text-secondary: #CCCCCC;
  --text-tertiary: #999999;
  --border-light: #333333;
  --border-medium: #444444;
  --border-dark: #555555;
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

/* 动画工具类 */
.animate-fadeIn { animation: fadeIn 0.5s ease-out; }
.animate-fadeInUp { animation: fadeInUp 0.6s ease-out; }
.animate-fadeInDown { animation: fadeInDown 0.6s ease-out; }
.animate-slideInLeft { animation: slideInLeft 0.5s ease-out; }
.animate-slideInRight { animation: slideInRight 0.5s ease-out; }
.animate-scaleIn { animation: scaleIn 0.4s ease-out; }
.animate-bounce { animation: bounce 1s ease-out; }
.animate-pulse { animation: pulse 2s ease-in-out infinite; }
.animate-shake { animation: shake 0.5s ease-in-out; }

/* 延迟动画 */
.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-500 { animation-delay: 0.5s; }

/* 磁州瓷器纹理背景 */
.bg-cizhou-pattern {
  background-image:
    radial-gradient(circle at 25% 25%, rgba(141, 110, 99, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 138, 101, 0.1) 0%, transparent 50%);
  background-size: 60px 60px;
}

.bg-cizhou-texture {
  background-image:
    linear-gradient(45deg, rgba(141, 110, 99, 0.05) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(141, 110, 99, 0.05) 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, rgba(141, 110, 99, 0.05) 75%),
    linear-gradient(-45deg, transparent 75%, rgba(141, 110, 99, 0.05) 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

/* 磁州风格渐变 */
.bg-gradient-cizhou {
  background: linear-gradient(135deg, #8D6E63 0%, #BCAAA4 50%, #FF8A65 100%);
}

.bg-gradient-cizhou-dark {
  background: linear-gradient(135deg, #6D4C41 0%, #5D4037 50%, #3E2723 100%);
}

.bg-gradient-warm {
  background: linear-gradient(135deg, #FF8A65 0%, #FFAB91 100%);
}

.bg-gradient-earth {
  background: linear-gradient(135deg, #8D6E63 0%, #A1887F 100%);
}

/* 特殊效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.ceramic-shadow {
  box-shadow:
    0 4px 8px rgba(141, 110, 99, 0.1),
    0 8px 16px rgba(141, 110, 99, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.ceramic-border {
  border: 2px solid;
  border-image: linear-gradient(45deg, #8D6E63, #BCAAA4, #FF8A65) 1;
}

/* 磁州瓷器色彩调色板 */
.color-cizhou-brown { color: #8D6E63; }
.color-cizhou-light { color: #BCAAA4; }
.color-cizhou-dark { color: #5D4037; }
.color-cizhou-orange { color: #FF8A65; }
.color-cizhou-cream { color: #FFF8E1; }
.color-cizhou-clay { color: #D7CCC8; }

.bg-cizhou-brown { background-color: #8D6E63; }
.bg-cizhou-light { background-color: #BCAAA4; }
.bg-cizhou-dark { background-color: #5D4037; }
.bg-cizhou-orange { background-color: #FF8A65; }
.bg-cizhou-cream { background-color: #FFF8E1; }
.bg-cizhou-clay { background-color: #D7CCC8; }
