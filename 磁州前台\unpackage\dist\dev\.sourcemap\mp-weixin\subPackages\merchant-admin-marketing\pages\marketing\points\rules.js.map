{"version": 3, "file": "rules.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/points/rules.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xwb2ludHNccnVsZXMudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"points-rules-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-left\">\r\n        <view class=\"back-button\" @tap=\"goBack\">\r\n          <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n            <path d=\"M15 18L9 12L15 6\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n          </svg>\r\n        </view>\r\n      </view>\r\n      <view class=\"navbar-title\">\r\n        <text class=\"title-text\">积分规则设置</text>\r\n      </view>\r\n      <view class=\"navbar-right\">\r\n        <!-- 占位 -->\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 页面内容区域 -->\r\n    <scroll-view scroll-y class=\"content-area\">\r\n      <view class=\"rules-card\">\r\n        <view class=\"card-header\">\r\n          <text class=\"card-title\">积分规则设置</text>\r\n          <view class=\"edit-text\" @tap=\"saveRules\" v-if=\"isEditing\">保存</view>\r\n          <view class=\"edit-text\" @tap=\"startEditing\" v-else>编辑</view>\r\n        </view>\r\n        \r\n        <view class=\"rules-list\">\r\n          <view class=\"rule-item\" v-for=\"(rule, index) in pointsRules\" :key=\"index\">\r\n            <view class=\"rule-icon\" :class=\"rule.type\">\r\n              <view class=\"icon-circle\"></view>\r\n            </view>\r\n            <view class=\"rule-content\">\r\n              <view class=\"rule-title\">{{rule.title}}</view>\r\n              <view class=\"rule-desc\">{{rule.description}}</view>\r\n            </view>\r\n            <view class=\"rule-points\" v-if=\"!isEditing\">+{{rule.points}}</view>\r\n            <view class=\"rule-points-edit\" v-else>\r\n              <text class=\"plus-sign\">+</text>\r\n              <input type=\"number\" class=\"points-input\" v-model=\"rule.points\" />\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 添加新规则按钮 -->\r\n      <view class=\"add-rule-button\" @tap=\"showAddRuleModal\" v-if=\"isEditing\">\r\n        <text class=\"add-icon\">+</text>\r\n        <text class=\"add-text\">添加新规则</text>\r\n      </view>\r\n      \r\n      <!-- 底部空间 -->\r\n      <view class=\"bottom-space\"></view>\r\n    </scroll-view>\r\n    \r\n    <!-- 添加规则弹窗 -->\r\n    <view class=\"modal\" v-if=\"showModal\">\r\n      <view class=\"modal-mask\" @tap=\"hideModal\"></view>\r\n      <view class=\"modal-content\">\r\n        <view class=\"modal-header\">\r\n          <text class=\"modal-title\">添加积分规则</text>\r\n          <view class=\"modal-close\" @tap=\"hideModal\">×</view>\r\n        </view>\r\n        <view class=\"modal-body\">\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">规则类型</text>\r\n            <picker @change=\"typeChange\" :value=\"typeIndex\" :range=\"typeOptions\" range-key=\"label\">\r\n              <view class=\"picker\">\r\n                {{typeOptions[typeIndex].label}}\r\n              </view>\r\n            </picker>\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">规则名称</text>\r\n            <input type=\"text\" class=\"form-input\" v-model=\"newRule.title\" placeholder=\"请输入规则名称\" />\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">规则描述</text>\r\n            <input type=\"text\" class=\"form-input\" v-model=\"newRule.description\" placeholder=\"请输入规则描述\" />\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">积分值</text>\r\n            <input type=\"number\" class=\"form-input\" v-model=\"newRule.points\" placeholder=\"请输入积分值\" />\r\n          </view>\r\n        </view>\r\n        <view class=\"modal-footer\">\r\n          <view class=\"modal-button cancel\" @tap=\"hideModal\">取消</view>\r\n          <view class=\"modal-button confirm\" @tap=\"addRule\">确定</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive } from 'vue';\r\n\r\n// 编辑状态\r\nconst isEditing = ref(false);\r\n\r\n// 积分规则数据\r\nconst pointsRules = ref([\r\n  {\r\n    id: 1,\r\n    type: 'purchase',\r\n    title: '商品购买',\r\n    description: '每消费1元获得1积分',\r\n    points: 1\r\n  },\r\n  {\r\n    id: 2,\r\n    type: 'checkin',\r\n    title: '每日签到',\r\n    description: '每日签到获得积分奖励',\r\n    points: 10\r\n  },\r\n  {\r\n    id: 3,\r\n    type: 'share',\r\n    title: '分享商品',\r\n    description: '分享商品到社交媒体获得积分',\r\n    points: 5\r\n  }\r\n]);\r\n\r\n// 开始编辑\r\nconst startEditing = () => {\r\n  isEditing.value = true;\r\n};\r\n\r\n// 保存规则\r\nconst saveRules = () => {\r\n  isEditing.value = false;\r\n  uni.showToast({\r\n    title: '保存成功',\r\n    icon: 'success'\r\n  });\r\n};\r\n\r\n// 添加规则相关\r\nconst showModal = ref(false);\r\nconst typeOptions = [\r\n  { value: 'purchase', label: '商品购买' },\r\n  { value: 'checkin', label: '每日签到' },\r\n  { value: 'share', label: '分享商品' },\r\n  { value: 'review', label: '评价商品' },\r\n  { value: 'register', label: '注册会员' },\r\n  { value: 'birthday', label: '生日特权' }\r\n];\r\nconst typeIndex = ref(0);\r\nconst newRule = reactive({\r\n  title: '',\r\n  description: '',\r\n  points: '',\r\n  type: 'purchase'\r\n});\r\n\r\n// 显示添加规则弹窗\r\nconst showAddRuleModal = () => {\r\n  showModal.value = true;\r\n  typeIndex.value = 0;\r\n  newRule.title = '';\r\n  newRule.description = '';\r\n  newRule.points = '';\r\n  newRule.type = 'purchase';\r\n};\r\n\r\n// 隐藏弹窗\r\nconst hideModal = () => {\r\n  showModal.value = false;\r\n};\r\n\r\n// 类型选择变化\r\nconst typeChange = (e) => {\r\n  typeIndex.value = e.detail.value;\r\n  newRule.type = typeOptions[typeIndex.value].value;\r\n};\r\n\r\n// 添加规则\r\nconst addRule = () => {\r\n  if (!newRule.title) {\r\n    uni.showToast({\r\n      title: '请输入规则名称',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n  \r\n  if (!newRule.description) {\r\n    uni.showToast({\r\n      title: '请输入规则描述',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n  \r\n  if (!newRule.points) {\r\n    uni.showToast({\r\n      title: '请输入积分值',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n  \r\n  // 添加新规则\r\n  pointsRules.value.push({\r\n    id: Date.now(),\r\n    type: newRule.type,\r\n    title: newRule.title,\r\n    description: newRule.description,\r\n    points: newRule.points\r\n  });\r\n  \r\n  hideModal();\r\n  \r\n  uni.showToast({\r\n    title: '添加成功',\r\n    icon: 'success'\r\n  });\r\n};\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n/* 页面容器 */\r\n.points-rules-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  position: relative;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #FF7600, #FF3C00);\r\n  color: white;\r\n  padding: 48px 20px 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 2px 8px rgba(255, 120, 0, 0.15);\r\n}\r\n\r\n.navbar-left {\r\n  width: 40px;\r\n}\r\n\r\n.back-button {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n}\r\n\r\n.title-text {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.navbar-right {\r\n  width: 40px;\r\n}\r\n\r\n/* 内容区域 */\r\n.content-area {\r\n  flex: 1;\r\n  padding: 16px;\r\n  box-sizing: border-box;\r\n  height: calc(100vh - 80px);\r\n}\r\n\r\n/* 规则卡片 */\r\n.rules-card {\r\n  background-color: #FFFFFF;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  margin-bottom: 16px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.card-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333333;\r\n}\r\n\r\n.edit-text {\r\n  font-size: 14px;\r\n  color: #5E5CE6;\r\n}\r\n\r\n/* 规则列表 */\r\n.rules-list {\r\n  margin-top: 16px;\r\n}\r\n\r\n.rule-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px 0;\r\n  border-bottom: 1px solid #F0F0F0;\r\n}\r\n\r\n.rule-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 12px;\r\n}\r\n\r\n.icon-circle {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 16px;\r\n}\r\n\r\n.rule-icon.purchase {\r\n  background-color: rgba(25, 137, 250, 0.1);\r\n}\r\n\r\n.rule-icon.purchase .icon-circle {\r\n  background-color: #1989FA;\r\n}\r\n\r\n.rule-icon.checkin {\r\n  background-color: rgba(52, 199, 89, 0.1);\r\n}\r\n\r\n.rule-icon.checkin .icon-circle {\r\n  background-color: #34C759;\r\n}\r\n\r\n.rule-icon.share {\r\n  background-color: rgba(255, 149, 0, 0.1);\r\n}\r\n\r\n.rule-icon.share .icon-circle {\r\n  background-color: #FF9500;\r\n}\r\n\r\n.rule-icon.review {\r\n  background-color: rgba(94, 92, 230, 0.1);\r\n}\r\n\r\n.rule-icon.review .icon-circle {\r\n  background-color: #5E5CE6;\r\n}\r\n\r\n.rule-icon.register {\r\n  background-color: rgba(255, 59, 48, 0.1);\r\n}\r\n\r\n.rule-icon.register .icon-circle {\r\n  background-color: #FF3B30;\r\n}\r\n\r\n.rule-icon.birthday {\r\n  background-color: rgba(255, 45, 85, 0.1);\r\n}\r\n\r\n.rule-icon.birthday .icon-circle {\r\n  background-color: #FF2D55;\r\n}\r\n\r\n.rule-content {\r\n  flex: 1;\r\n}\r\n\r\n.rule-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #333333;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.rule-desc {\r\n  font-size: 12px;\r\n  color: #999999;\r\n}\r\n\r\n.rule-points {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #FF7600;\r\n}\r\n\r\n.rule-points-edit {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.plus-sign {\r\n  font-size: 16px;\r\n  color: #FF7600;\r\n  margin-right: 2px;\r\n}\r\n\r\n.points-input {\r\n  width: 50px;\r\n  height: 32px;\r\n  background-color: #F5F7FA;\r\n  border-radius: 4px;\r\n  padding: 0 8px;\r\n  font-size: 16px;\r\n  color: #FF7600;\r\n  text-align: center;\r\n}\r\n\r\n/* 添加规则按钮 */\r\n.add-rule-button {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color: #FFFFFF;\r\n  border-radius: 12px;\r\n  padding: 16px;\r\n  margin-bottom: 16px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  border: 1px dashed #CCCCCC;\r\n}\r\n\r\n.add-icon {\r\n  font-size: 20px;\r\n  color: #999999;\r\n  margin-right: 8px;\r\n}\r\n\r\n.add-text {\r\n  font-size: 16px;\r\n  color: #999999;\r\n}\r\n\r\n/* 底部空间 */\r\n.bottom-space {\r\n  height: 20px;\r\n}\r\n\r\n/* 弹窗样式 */\r\n.modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 999;\r\n}\r\n\r\n.modal-mask {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.modal-content {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background-color: #FFFFFF;\r\n  border-top-left-radius: 16px;\r\n  border-top-right-radius: 16px;\r\n  overflow: hidden;\r\n}\r\n\r\n.modal-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 16px 20px;\r\n  border-bottom: 1px solid #F0F0F0;\r\n}\r\n\r\n.modal-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333333;\r\n}\r\n\r\n.modal-close {\r\n  font-size: 24px;\r\n  color: #999999;\r\n  padding: 0 8px;\r\n}\r\n\r\n.modal-body {\r\n  padding: 20px;\r\n}\r\n\r\n.form-item {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.form-label {\r\n  font-size: 14px;\r\n  color: #666666;\r\n  margin-bottom: 8px;\r\n  display: block;\r\n}\r\n\r\n.form-input, .picker {\r\n  width: 100%;\r\n  height: 44px;\r\n  background-color: #F5F7FA;\r\n  border-radius: 8px;\r\n  padding: 0 12px;\r\n  font-size: 14px;\r\n  color: #333333;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.modal-footer {\r\n  display: flex;\r\n  border-top: 1px solid #F0F0F0;\r\n}\r\n\r\n.modal-button {\r\n  flex: 1;\r\n  height: 50px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 16px;\r\n}\r\n\r\n.modal-button.cancel {\r\n  color: #666666;\r\n  background-color: #FFFFFF;\r\n}\r\n\r\n.modal-button.confirm {\r\n  color: #FFFFFF;\r\n  background-color: #FF7600;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/points/rules.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "reactive", "MiniProgramPage"], "mappings": ";;;;;;;;;;AAmGA,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAG3B,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,QAAQ;AAAA,MACT;AAAA,IACH,CAAC;AAGD,UAAM,eAAe,MAAM;AACzB,gBAAU,QAAQ;AAAA,IACpB;AAGA,UAAM,YAAY,MAAM;AACtB,gBAAU,QAAQ;AAClBC,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,YAAYD,cAAAA,IAAI,KAAK;AAC3B,UAAM,cAAc;AAAA,MAClB,EAAE,OAAO,YAAY,OAAO,OAAQ;AAAA,MACpC,EAAE,OAAO,WAAW,OAAO,OAAQ;AAAA,MACnC,EAAE,OAAO,SAAS,OAAO,OAAQ;AAAA,MACjC,EAAE,OAAO,UAAU,OAAO,OAAQ;AAAA,MAClC,EAAE,OAAO,YAAY,OAAO,OAAQ;AAAA,MACpC,EAAE,OAAO,YAAY,OAAO,OAAQ;AAAA,IACtC;AACA,UAAM,YAAYA,cAAAA,IAAI,CAAC;AACvB,UAAM,UAAUE,cAAAA,SAAS;AAAA,MACvB,OAAO;AAAA,MACP,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAGD,UAAM,mBAAmB,MAAM;AAC7B,gBAAU,QAAQ;AAClB,gBAAU,QAAQ;AAClB,cAAQ,QAAQ;AAChB,cAAQ,cAAc;AACtB,cAAQ,SAAS;AACjB,cAAQ,OAAO;AAAA,IACjB;AAGA,UAAM,YAAY,MAAM;AACtB,gBAAU,QAAQ;AAAA,IACpB;AAGA,UAAM,aAAa,CAAC,MAAM;AACxB,gBAAU,QAAQ,EAAE,OAAO;AAC3B,cAAQ,OAAO,YAAY,UAAU,KAAK,EAAE;AAAA,IAC9C;AAGA,UAAM,UAAU,MAAM;AACpB,UAAI,CAAC,QAAQ,OAAO;AAClBD,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI,CAAC,QAAQ,aAAa;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI,CAAC,QAAQ,QAAQ;AACnBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAGD,kBAAY,MAAM,KAAK;AAAA,QACrB,IAAI,KAAK,IAAK;AAAA,QACd,MAAM,QAAQ;AAAA,QACd,OAAO,QAAQ;AAAA,QACf,aAAa,QAAQ;AAAA,QACrB,QAAQ,QAAQ;AAAA,MACpB,CAAG;AAED;AAEAA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChOA,GAAG,WAAWE,SAAe;"}