<template>
  <view class="finance-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">财务对账系统</text>
      <view class="navbar-right">
        <view class="export-icon" @click="exportData">📊</view>
      </view>
    </view>
    
    <!-- 数据概览卡片 -->
    <view class="overview-card">
      <view class="card-header">
        <text class="card-title">收入概览</text>
        <view class="date-selector">
          <text class="date-label">{{currentDateRange}}</text>
          <view class="date-icon" @click="showDatePicker">📅</view>
        </view>
      </view>
      <view class="overview-data">
        <view class="data-item">
          <text class="data-value">¥{{summaryData.totalRevenue}}</text>
          <text class="data-label">总收入</text>
        </view>
        <view class="data-item">
          <text class="data-value">{{summaryData.orderCount}}</text>
          <text class="data-label">订单数</text>
        </view>
        <view class="data-item">
          <text class="data-value">¥{{summaryData.avgOrderValue}}</text>
          <text class="data-label">客单价</text>
        </view>
      </view>
      <view class="chart-container">
        <!-- 这里应该是图表组件，暂时用占位符 -->
        <view class="chart-placeholder">
          <text class="chart-text">收入趋势图表</text>
        </view>
      </view>
    </view>
    
    <!-- 财务明细Tab -->
    <view class="tab-container">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index"
        class="tab-item"
        :class="{'active': currentTab === tab.value}"
        @click="switchTab(tab.value)">
        {{tab.name}}
      </view>
    </view>
    
    <!-- 日结算记录 -->
    <view v-if="currentTab === 'daily'" class="content-main">
      <view class="table-header">
        <text class="header-cell date">日期</text>
        <text class="header-cell orders">订单数</text>
        <text class="header-cell amount">收入金额</text>
        <text class="header-cell status">状态</text>
      </view>
      <view 
        v-for="(item, index) in dailySettlements" 
        :key="index"
        class="table-row"
        @click="viewDailyDetail(item.date)">
        <text class="cell date">{{item.date}}</text>
        <text class="cell orders">{{item.orderCount}}</text>
        <text class="cell amount">¥{{item.amount}}</text>
        <text class="cell status" :class="'status-' + item.status">{{getStatusText(item.status)}}</text>
      </view>
      
      <!-- 空状态 -->
      <view v-if="dailySettlements.length === 0" class="empty-state">
        <view class="empty-icon">📊</view>
        <text class="empty-text">暂无日结算记录</text>
      </view>
    </view>
    
    <!-- 周期对账单 -->
    <view v-if="currentTab === 'period'" class="content-main">
      <view class="table-header">
        <text class="header-cell period">结算周期</text>
        <text class="header-cell orders">订单数</text>
        <text class="header-cell amount">收入金额</text>
        <text class="header-cell status">状态</text>
      </view>
      <view 
        v-for="(item, index) in periodSettlements" 
        :key="index"
        class="table-row"
        @click="viewPeriodDetail(item.id)">
        <text class="cell period">{{item.startDate}} 至 {{item.endDate}}</text>
        <text class="cell orders">{{item.orderCount}}</text>
        <text class="cell amount">¥{{item.amount}}</text>
        <text class="cell status" :class="'status-' + item.status">{{getStatusText(item.status)}}</text>
      </view>
      
      <!-- 空状态 -->
      <view v-if="periodSettlements.length === 0" class="empty-state">
        <view class="empty-icon">📊</view>
        <text class="empty-text">暂无周期对账记录</text>
      </view>
    </view>
    
    <!-- 提现管理 -->
    <view v-if="currentTab === 'withdraw'" class="content-main">
      <view class="balance-card">
        <text class="balance-label">可提现余额</text>
        <text class="balance-value">¥{{balance}}</text>
        <view class="withdraw-btn" @click="showWithdrawModal">申请提现</view>
      </view>
      
      <view class="section-title">提现记录</view>
      
      <view 
        v-for="(item, index) in withdrawRecords" 
        :key="index"
        class="withdraw-item">
        <view class="withdraw-header">
          <text class="withdraw-amount">¥{{item.amount}}</text>
          <text class="withdraw-status" :class="'status-' + item.status">{{getWithdrawStatusText(item.status)}}</text>
        </view>
        <view class="withdraw-info">
          <text class="withdraw-time">申请时间: {{item.createTime}}</text>
          <text class="withdraw-account">提现账户: {{item.account}}</text>
          <text v-if="item.completeTime" class="withdraw-complete-time">到账时间: {{item.completeTime}}</text>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view v-if="withdrawRecords.length === 0" class="empty-state">
        <view class="empty-icon">💰</view>
        <text class="empty-text">暂无提现记录</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentTab: 'daily', // daily, period, withdraw
      tabs: [
        { name: '日结算记录', value: 'daily' },
        { name: '周期对账单', value: 'period' },
        { name: '提现管理', value: 'withdraw' }
      ],
      currentDateRange: '2023-05-01 至 2023-05-15',
      summaryData: {
        totalRevenue: '2,586.50',
        orderCount: 35,
        avgOrderValue: '73.90'
      },
      dailySettlements: [
        {
          date: '2023-05-15',
          orderCount: 5,
          amount: '356.80',
          status: 'pending' // pending, completed
        },
        {
          date: '2023-05-14',
          orderCount: 8,
          amount: '589.70',
          status: 'completed'
        },
        {
          date: '2023-05-13',
          orderCount: 6,
          amount: '420.50',
          status: 'completed'
        },
        {
          date: '2023-05-12',
          orderCount: 7,
          amount: '512.30',
          status: 'completed'
        },
        {
          date: '2023-05-11',
          orderCount: 9,
          amount: '707.20',
          status: 'completed'
        }
      ],
      periodSettlements: [
        {
          id: 'PS001',
          startDate: '2023-05-01',
          endDate: '2023-05-07',
          orderCount: 22,
          amount: '1,640.00',
          status: 'completed'
        },
        {
          id: 'PS002',
          startDate: '2023-05-08',
          endDate: '2023-05-14',
          orderCount: 30,
          amount: '2,229.70',
          status: 'completed'
        }
      ],
      balance: '2,586.50',
      withdrawRecords: [
        {
          id: 'W001',
          amount: '1,000.00',
          status: 'completed', // pending, processing, completed, failed
          createTime: '2023-05-01 14:30',
          completeTime: '2023-05-02 10:15',
          account: '微信支付 (尾号1234)'
        },
        {
          id: 'W002',
          amount: '500.00',
          status: 'processing',
          createTime: '2023-05-15 09:45',
          completeTime: null,
          account: '支付宝 (li***@example.com)'
        }
      ]
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    switchTab(tab) {
      this.currentTab = tab;
    },
    exportData() {
      uni.showActionSheet({
        itemList: ['导出Excel', '导出PDF', '打印对账单'],
        success: (res) => {
          uni.showToast({
            title: '导出功能开发中',
            icon: 'none'
          });
        }
      });
    },
    showDatePicker() {
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    viewDailyDetail(date) {
      uni.showToast({
        title: '查看日结算详情功能开发中',
        icon: 'none'
      });
    },
    viewPeriodDetail(id) {
      uni.showToast({
        title: '查看周期对账详情功能开发中',
        icon: 'none'
      });
    },
    showWithdrawModal() {
      uni.showModal({
        title: '申请提现',
        content: '确认申请提现¥' + this.balance + '？',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '提现申请已提交',
              icon: 'success'
            });
            
            // 添加新的提现记录
            this.withdrawRecords.unshift({
              id: 'W00' + (this.withdrawRecords.length + 1),
              amount: this.balance,
              status: 'pending',
              createTime: this.getCurrentTime(),
              completeTime: null,
              account: '微信支付 (尾号1234)'
            });
            
            // 清空余额
            this.balance = '0.00';
          }
        }
      });
    },
    getCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hour = String(now.getHours()).padStart(2, '0');
      const minute = String(now.getMinutes()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hour}:${minute}`;
    },
    getStatusText(status) {
      const texts = {
        pending: '待结算',
        completed: '已结算'
      };
      return texts[status] || '未知状态';
    },
    getWithdrawStatusText(status) {
      const texts = {
        pending: '待处理',
        processing: '处理中',
        completed: '已到账',
        failed: '提现失败'
      };
      return texts[status] || '未知状态';
    }
  }
}
</script>

<style>
.finance-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.export-icon {
  font-size: 20px;
  color: #fff;
}

.overview-card {
  margin: 16px;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.date-selector {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.date-icon {
  margin-left: 8px;
  font-size: 16px;
}

.overview-data {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.data-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.data-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.data-label {
  font-size: 12px;
  color: #999;
}

.chart-container {
  height: 200px;
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.chart-placeholder {
  height: 100%;
  background-color: #f9f9f9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-text {
  font-size: 14px;
  color: #999;
}

.tab-container {
  display: flex;
  background-color: #fff;
  margin: 0 16px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 12px 0;
  font-size: 14px;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #1677FF;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 25%;
  right: 25%;
  height: 3px;
  background-color: #1677FF;
  border-radius: 3px 3px 0 0;
}

.content-main {
  margin: 16px;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.table-header {
  display: flex;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
  color: #999;
  font-weight: 500;
}

.table-row {
  display: flex;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.table-row:last-child {
  border-bottom: none;
}

.header-cell, .cell {
  flex: 1;
}

.header-cell.date, .cell.date {
  flex: 1.5;
}

.header-cell.period, .cell.period {
  flex: 2;
}

.cell {
  font-size: 14px;
  color: #333;
}

.cell.amount {
  font-weight: 500;
  color: #ff6a00;
}

.status-pending {
  color: #FF9800;
}

.status-completed {
  color: #4CAF50;
}

.status-processing {
  color: #2196F3;
}

.status-failed {
  color: #F44336;
}

.balance-card {
  background-color: #e6f7ff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.balance-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.balance-value {
  font-size: 28px;
  font-weight: 600;
  color: #1677FF;
  margin-bottom: 16px;
}

.withdraw-btn {
  background-color: #1677FF;
  color: #fff;
  padding: 8px 24px;
  border-radius: 20px;
  font-size: 14px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.withdraw-item {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
}

.withdraw-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.withdraw-amount {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.withdraw-status {
  font-size: 14px;
  font-weight: 500;
}

.withdraw-info {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 0;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  color: #999;
}
</style> 