<template>
  <view class="operation-container">
    <!-- 顶部导航栏 -->
    <view class="header-area">
      <view class="safe-area-top"></view>
      <view class="custom-navbar">
        <view class="navbar-left" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="back-icon" style="filter: brightness(0) invert(1);"></image>
        </view>
        <view class="navbar-title">活动操作</view>
        <view class="navbar-right"></view>
      </view>
    </view>
    
    <!-- 背景装饰 -->
    <view class="bg-decoration bg-circle-1"></view>
    <view class="bg-decoration bg-circle-2"></view>
    <view class="bg-decoration bg-circle-3"></view>
    
    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 活动筛选 -->
      <view class="filter-section">
        <view class="filter-row">
          <view class="filter-label">活动类型</view>
          <view class="filter-options">
            <view 
              class="filter-option" 
              :class="{ active: filters.type === type.value }" 
              v-for="type in typeFilters" 
              :key="type.value"
              @click="filterByType(type.value)"
            >
              {{type.label}}
            </view>
          </view>
        </view>
        <view class="filter-row">
          <view class="filter-label">活动状态</view>
          <view class="filter-options">
            <view 
              class="filter-option" 
              :class="{ active: filters.status === status.value }" 
              v-for="status in statusFilters" 
              :key="status.value"
              @click="filterByStatus(status.value)"
            >
              {{status.label}}
            </view>
          </view>
        </view>
      </view>
      
      <!-- 批量操作 -->
      <view class="batch-actions">
        <view class="batch-selection">
          <text class="selection-count" v-if="selectedActivities.length > 0">已选择 {{selectedActivities.length}} 项</text>
          <text class="selection-action" @click="toggleSelectAll">{{isAllSelected ? '取消全选' : '全选'}}</text>
        </view>
        <view class="batch-buttons">
          <button class="batch-btn" :class="{ disabled: selectedActivities.length === 0 }" @click="batchRefresh">刷新</button>
          <button class="batch-btn" :class="{ disabled: selectedActivities.length === 0 }" @click="batchDelete">删除</button>
          <button class="batch-btn" :class="{ disabled: selectedActivities.length === 0 }" @click="batchEndActivity">结束活动</button>
        </view>
      </view>
      
      <!-- 活动列表 -->
      <view class="activity-list" v-if="filteredActivities.length > 0">
        <view class="activity-item" v-for="(activity, index) in filteredActivities" :key="index">
          <view class="select-box" @click.stop="toggleSelect(activity.id)">
            <view class="checkbox" :class="{ selected: isSelected(activity.id) }">
              <text class="check-icon" v-if="isSelected(activity.id)">✓</text>
            </view>
          </view>
          
          <view class="activity-content" @click="viewDetail(activity.id)">
            <view class="activity-header">
              <view class="activity-title">{{activity.title}}</view>
              <view class="activity-status" :class="activity.status">{{getStatusText(activity.status)}}</view>
            </view>
            
            <view class="activity-info">
              <view class="info-item">
                <text class="info-label">活动时间：</text>
                <text class="info-value">{{activity.timeRange}}</text>
              </view>
              <view class="info-item">
                <text class="info-label">参与人数：</text>
                <text class="info-value">{{activity.participants}}人</text>
              </view>
              <view class="info-item">
                <text class="info-label">浏览次数：</text>
                <text class="info-value">{{activity.views}}次</text>
              </view>
              <view class="info-item" v-if="activity.topExpireTime">
                <text class="info-label">置顶到期：</text>
                <text class="info-value" :class="{'expired': isTopExpired(activity.topExpireTime)}">{{activity.topExpireTime}}</text>
              </view>
            </view>
            
            <view class="activity-stats">
              <view class="stat-item">
                <view class="stat-value">{{activity.views}}</view>
                <view class="stat-label">浏览量</view>
              </view>
              <view class="stat-divider"></view>
              <view class="stat-item">
                <view class="stat-value">{{activity.participants}}</view>
                <view class="stat-label">参与人数</view>
              </view>
              <view class="stat-divider"></view>
              <view class="stat-item">
                <view class="stat-value">{{activity.conversionRate}}%</view>
                <view class="stat-label">转化率</view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-else>
        <image class="empty-icon" src="/static/images/tabbar/无数据.png"></image>
        <text class="empty-text">暂无活动数据</text>
      </view>
      
      <!-- 操作面板 -->
      <view class="action-panel" v-if="selectedActivities.length > 0">
        <view class="panel-header">
          <text class="panel-title">已选择 {{selectedActivities.length}} 项</text>
          <text class="close-panel" @click="clearSelection">×</text>
        </view>
        <view class="panel-actions">
          <view class="panel-btn refresh" @click="batchRefresh">刷新</view>
          <view class="panel-btn top" @click="showTopModal">置顶</view>
          <view class="panel-btn end" @click="batchEndActivity">结束</view>
          <view class="panel-btn delete" @click="batchDelete">删除</view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 置顶弹窗 -->
    <view class="modal-overlay" v-if="showTopSettingModal" @click="cancelTopSetting"></view>
    <view class="top-modal" v-if="showTopSettingModal">
      <view class="modal-header">
        <text class="modal-title">活动置顶</text>
        <view class="close-btn" @click="cancelTopSetting">×</view>
      </view>
      
      <view class="modal-content">
        <view class="top-description">
          <view class="desc-title">置顶说明</view>
          <view class="desc-content">
            <text>置顶可以让您的活动展示在列表顶部，提高曝光度。</text>
            <text>置顶费用根据时长收费，置顶期间可随时取消。</text>
          </view>
        </view>
        
        <view class="top-options">
          <view class="option-title">置顶时长</view>
          <view class="option-list">
            <view 
              class="option-item" 
              :class="{ active: topDuration === option.value }" 
              v-for="option in topDurationOptions" 
              :key="option.value"
              @click="selectTopDuration(option.value)"
            >
              <view class="option-name">{{option.label}}</view>
              <view class="option-price">¥{{option.price}}</view>
            </view>
          </view>
        </view>
        
        <view class="top-preview">
          <text class="preview-label">预计费用</text>
          <text class="preview-price">¥{{getTopPrice()}}</text>
        </view>
      </view>
      
      <view class="modal-footer">
        <button class="modal-btn cancel" @click="cancelTopSetting">取消</button>
        <button class="modal-btn confirm" @click="confirmTopSetting">确认置顶</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20,
      selectedActivities: [],
      showTopSettingModal: false,
      topDuration: 7, // 默认7天
      filters: {
        type: 'all',
        status: 'all'
      },
      typeFilters: [
        { label: '全部类型', value: 'all' },
        { label: '促销活动', value: 'promotion' },
        { label: '节日活动', value: 'festival' },
        { label: '店庆活动', value: 'anniversary' },
        { label: '新品活动', value: 'new_product' }
      ],
      statusFilters: [
        { label: '全部状态', value: 'all' },
        { label: '进行中', value: 'running' },
        { label: '即将开始', value: 'upcoming' },
        { label: '已结束', value: 'ended' }
      ],
      topDurationOptions: [
        { label: '7天', value: 7, price: 49 },
        { label: '15天', value: 15, price: 88 },
        { label: '30天', value: 30, price: 158 }
      ],
      activities: [
        {
          id: 1,
          title: '双十一大促销，全场满300减50',
          type: 'promotion',
          status: 'running',
          timeRange: '2023-11-01 至 2023-11-11',
          participants: 78,
          views: 1258,
          conversionRate: 6.2,
          topExpireTime: '2023-11-05'
        },
        {
          id: 2,
          title: '开业庆典，免费品尝活动',
          type: 'anniversary',
          status: 'ended',
          timeRange: '2023-10-15 至 2023-10-20',
          participants: 126,
          views: 876,
          conversionRate: 14.4
        },
        {
          id: 3,
          title: '周年庆典，抽奖赢大礼',
          type: 'anniversary',
          status: 'upcoming',
          timeRange: '2023-12-01 至 2023-12-10',
          participants: 0,
          views: 322,
          conversionRate: 0
        },
        {
          id: 4,
          title: '夏季特惠，冰爽饮品第二杯半价',
          type: 'promotion',
          status: 'ended',
          timeRange: '2023-07-01 至 2023-08-31',
          participants: 376,
          views: 2541,
          conversionRate: 14.8
        },
        {
          id: 5,
          title: '新品推广，免费试吃活动',
          type: 'new_product',
          status: 'running',
          timeRange: '2023-10-25 至 2023-11-05',
          participants: 47,
          views: 684,
          conversionRate: 6.9,
          topExpireTime: '2023-11-10'
        }
      ]
    }
  },
  computed: {
    filteredActivities() {
      return this.activities.filter(activity => {
        const typeMatch = this.filters.type === 'all' || activity.type === this.filters.type;
        const statusMatch = this.filters.status === 'all' || activity.status === this.filters.status;
        return typeMatch && statusMatch;
      });
    },
    isAllSelected() {
      return this.filteredActivities.length > 0 && 
             this.selectedActivities.length === this.filteredActivities.length;
    }
  },
  onLoad() {
    this.setStatusBarHeight();
  },
  methods: {
    setStatusBarHeight() {
      uni.getSystemInfo({
        success: (res) => {
          this.statusBarHeight = res.statusBarHeight;
          if (typeof document !== 'undefined') {
            document.documentElement.style.setProperty('--status-bar-height', `${this.statusBarHeight}px`);
          }
        }
      });
    },
    goBack() {
      uni.navigateBack();
    },
    
    // 筛选方法
    filterByType(type) {
      this.filters.type = type;
      this.selectedActivities = [];
    },
    filterByStatus(status) {
      this.filters.status = status;
      this.selectedActivities = [];
    },
    getStatusText(status) {
      const statusMap = {
        'running': '进行中',
        'upcoming': '即将开始',
        'ended': '已结束'
      };
      return statusMap[status] || '未知状态';
    },
    
    // 选择活动方法
    toggleSelect(id) {
      const index = this.selectedActivities.indexOf(id);
      if (index > -1) {
        this.selectedActivities.splice(index, 1);
      } else {
        this.selectedActivities.push(id);
      }
    },
    isSelected(id) {
      return this.selectedActivities.includes(id);
    },
    toggleSelectAll() {
      if (this.isAllSelected) {
        this.selectedActivities = [];
      } else {
        this.selectedActivities = this.filteredActivities.map(item => item.id);
      }
    },
    clearSelection() {
      this.selectedActivities = [];
    },
    
    // 置顶相关方法
    isTopExpired(expireTime) {
      return new Date(expireTime) < new Date();
    },
    showTopModal() {
      if (this.selectedActivities.length === 0) {
        uni.showToast({
          title: '请选择要置顶的活动',
          icon: 'none'
        });
        return;
      }
      this.showTopSettingModal = true;
    },
    cancelTopSetting() {
      this.showTopSettingModal = false;
    },
    selectTopDuration(duration) {
      this.topDuration = duration;
    },
    getTopPrice() {
      const option = this.topDurationOptions.find(opt => opt.value === this.topDuration);
      return option ? option.price * this.selectedActivities.length : 0;
    },
    confirmTopSetting() {
      uni.showLoading({
        title: '处理中',
        mask: true
      });
      
      setTimeout(() => {
        uni.hideLoading();
        
        // 更新置顶到期时间
        const now = new Date();
        const expireDate = new Date(now.setDate(now.getDate() + this.topDuration));
        const expireFormatted = `${expireDate.getFullYear()}-${String(expireDate.getMonth() + 1).padStart(2, '0')}-${String(expireDate.getDate()).padStart(2, '0')}`;
        
        this.selectedActivities.forEach(id => {
          const activityIndex = this.activities.findIndex(a => a.id === id);
          if (activityIndex > -1) {
            this.activities[activityIndex].topExpireTime = expireFormatted;
          }
        });
        
        this.showTopSettingModal = false;
        this.selectedActivities = [];
        
        uni.showToast({
          title: '置顶成功',
          icon: 'success'
        });
      }, 1000);
    },
    
    // 批量操作方法
    batchRefresh() {
      if (this.selectedActivities.length === 0) {
        uni.showToast({
          title: '请选择要刷新的活动',
          icon: 'none'
        });
        return;
      }
      
      uni.showLoading({
        title: '刷新中',
        mask: true
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: `成功刷新${this.selectedActivities.length}个活动`,
          icon: 'success'
        });
        this.selectedActivities = [];
      }, 1000);
    },
    batchEndActivity() {
      if (this.selectedActivities.length === 0) {
        uni.showToast({
          title: '请选择要结束的活动',
          icon: 'none'
        });
        return;
      }
      
      uni.showModal({
        title: '结束活动',
        content: `确定要结束选中的${this.selectedActivities.length}个活动吗？`,
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '处理中',
              mask: true
            });
            
            setTimeout(() => {
              uni.hideLoading();
              
              // 更新活动状态
              this.selectedActivities.forEach(id => {
                const activityIndex = this.activities.findIndex(a => a.id === id);
                if (activityIndex > -1 && this.activities[activityIndex].status !== 'ended') {
                  this.activities[activityIndex].status = 'ended';
                }
              });
              
              uni.showToast({
                title: '活动已结束',
                icon: 'success'
              });
              this.selectedActivities = [];
            }, 1000);
          }
        }
      });
    },
    batchDelete() {
      if (this.selectedActivities.length === 0) {
        uni.showToast({
          title: '请选择要删除的活动',
          icon: 'none'
        });
        return;
      }
      
      uni.showModal({
        title: '删除活动',
        content: `确定要删除选中的${this.selectedActivities.length}个活动吗？`,
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '删除中',
              mask: true
            });
            
            setTimeout(() => {
              uni.hideLoading();
              
              // 删除选中的活动
              this.activities = this.activities.filter(activity => !this.selectedActivities.includes(activity.id));
              
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              });
              this.selectedActivities = [];
            }, 1000);
          }
        }
      });
    },
    viewDetail(id) {
      uni.navigateTo({
        url: `/merchant-admin/pages/activity/detail?id=${id}`
      });
    }
  }
}
</script>

<style lang="scss">
.operation-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(110rpx + var(--status-bar-height, 20px));
}

/* 顶部导航栏样式 */
.header-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
  color: #fff;
}

.safe-area-top {
  height: var(--status-bar-height, 20px);
  width: 100%;
}

.custom-navbar {
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
}

.navbar-left, .navbar-right {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 48rpx;
  height: 48rpx;
}

/* 背景装饰样式 */
.bg-decoration {
  position: absolute;
  z-index: -1;
  border-radius: 50%;
  opacity: 0.07;
  background-color: #0A84FF;
}

.bg-circle-1 {
  top: -100rpx;
  right: -150rpx;
  width: 400rpx;
  height: 400rpx;
}

.bg-circle-2 {
  top: 20%;
  left: -200rpx;
  width: 500rpx;
  height: 500rpx;
}

.bg-circle-3 {
  bottom: 10%;
  right: -100rpx;
  width: 300rpx;
  height: 300rpx;
}

.content-scroll {
  padding: 30rpx;
  padding-bottom: 120rpx;
}

/* 筛选区域样式 */
.filter-section {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.filter-row {
  margin: 20rpx 0;
}

.filter-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.filter-option {
  padding: 10rpx 24rpx;
  font-size: 26rpx;
  color: #666;
  background-color: #F5F8FC;
  border-radius: 30rpx;
  margin: 10rpx;
}

.filter-option.active {
  background-color: rgba(10, 132, 255, 0.1);
  color: #0A84FF;
  font-weight: 500;
  border: 1rpx solid #0A84FF;
}

/* 批量操作样式 */
.batch-actions {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.batch-selection {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.selection-count {
  font-size: 28rpx;
  color: #333;
}

.selection-action {
  font-size: 28rpx;
  color: #0A84FF;
}

.batch-buttons {
  display: flex;
  justify-content: space-between;
}

.batch-btn {
  flex: 1;
  margin: 0 10rpx;
  height: 70rpx;
  line-height: 70rpx;
  border-radius: 35rpx;
  font-size: 28rpx;
  color: #FFFFFF;
  background: linear-gradient(135deg, #0A84FF, #0055FF);
  border: none;
}

.batch-btn:first-child {
  margin-left: 0;
}

.batch-btn:last-child {
  margin-right: 0;
}

.batch-btn.disabled {
  background: linear-gradient(135deg, #A8A8A8, #8E8E93);
  opacity: 0.7;
}

/* 活动列表样式 */
.activity-list {
  margin-bottom: 30rpx;
}

.activity-item {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  display: flex;
}

.select-box {
  padding: 10rpx 20rpx 10rpx 10rpx;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  border: 2rpx solid #CCCCCC;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox.selected {
  background-color: #0A84FF;
  border-color: #0A84FF;
}

.check-icon {
  color: #FFFFFF;
  font-size: 24rpx;
}

.activity-content {
  flex: 1;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.activity-title {
  flex: 1;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-right: 20rpx;
}

.activity-status {
  padding: 4rpx 16rpx;
  font-size: 22rpx;
  border-radius: 20rpx;
}

.activity-status.running {
  background-color: rgba(10, 132, 255, 0.1);
  color: #0A84FF;
}

.activity-status.upcoming {
  background-color: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}

.activity-status.ended {
  background-color: rgba(142, 142, 147, 0.1);
  color: #8E8E93;
}

.activity-info {
  margin-bottom: 16rpx;
}

.info-item {
  display: flex;
  margin-bottom: 8rpx;
  font-size: 24rpx;
}

.info-label {
  color: #999;
}

.info-value {
  color: #666;
}

.info-value.expired {
  color: #FF3B30;
}

.activity-stats {
  display: flex;
  border-top: 2rpx solid #F2F2F7;
  padding-top: 16rpx;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #999;
}

.stat-divider {
  width: 2rpx;
  height: 30rpx;
  background-color: #EEEEEE;
  margin-top: 10rpx;
}

/* 空状态样式 */
.empty-state {
  padding: 60rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 操作面板样式 */
.action-panel {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #FFFFFF;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 99;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.panel-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.close-panel {
  font-size: 36rpx;
  color: #999;
}

.panel-actions {
  display: flex;
  justify-content: space-between;
}

.panel-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #FFFFFF;
  border-radius: 40rpx;
  margin: 0 10rpx;
}

.panel-btn.refresh {
  background: linear-gradient(135deg, #34C759, #30DB5B);
}

.panel-btn.top {
  background: linear-gradient(135deg, #0A84FF, #0055FF);
}

.panel-btn.end {
  background: linear-gradient(135deg, #FF9500, #FF8000);
}

.panel-btn.delete {
  background: linear-gradient(135deg, #FF3B30, #FF2D20);
}

/* 置顶弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
}

.top-modal {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  z-index: 1000;
}

.modal-header {
  padding: 30rpx;
  text-align: center;
  position: relative;
  border-bottom: 2rpx solid #F2F2F7;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  font-size: 36rpx;
  color: #999;
}

.modal-content {
  padding: 30rpx;
}

.top-description {
  margin-bottom: 30rpx;
}

.desc-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.desc-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.desc-content text {
  display: block;
  margin-bottom: 10rpx;
}

.top-options {
  margin-bottom: 30rpx;
}

.option-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.option-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.option-item {
  width: calc(33.33% - 20rpx);
  margin: 10rpx;
  border: 2rpx solid #EEEEEE;
  border-radius: 16rpx;
  padding: 20rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.option-item.active {
  border-color: #0A84FF;
  background-color: rgba(10, 132, 255, 0.05);
}

.option-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.option-price {
  font-size: 26rpx;
  color: #FF3B30;
}

.top-preview {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
}

.preview-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.preview-price {
  font-size: 32rpx;
  color: #FF3B30;
  font-weight: 600;
}

.modal-footer {
  display: flex;
  border-top: 2rpx solid #F2F2F7;
}

.modal-btn {
  flex: 1;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  background: none;
}

.modal-btn::after {
  border: none;
}

.cancel {
  color: #999;
  border-right: 1px solid #F2F2F7;
}

.confirm {
  color: #0A84FF;
  font-weight: 500;
}

/* 适配暗黑模式 */
@media (prefers-color-scheme: dark) {
  .operation-container {
    background-color: #1C1C1E;
  }
  
  .filter-section,
  .batch-actions,
  .activity-item,
  .action-panel,
  .top-modal {
    background-color: #2C2C2E;
  }
  
  .filter-label,
  .selection-count,
  .activity-title,
  .stat-value,
  .panel-title,
  .modal-title,
  .desc-title,
  .option-title,
  .preview-label,
  .option-name {
    color: #FFFFFF;
  }
  
  .filter-option {
    background-color: #3A3A3C;
    color: #A8A8A8;
  }
  
  .checkbox {
    border-color: #5A5A5A;
  }
  
  .info-label,
  .info-value,
  .stat-label,
  .desc-content {
    color: #A8A8A8;
  }
  
  .stat-divider {
    background-color: #3A3A3C;
  }
  
  .activity-stats {
    border-color: #3A3A3C;
  }
  
  .option-item {
    border-color: #3A3A3C;
  }
  
  .modal-header,
  .modal-footer {
    border-color: #3A3A3C;
  }
}
</style> 