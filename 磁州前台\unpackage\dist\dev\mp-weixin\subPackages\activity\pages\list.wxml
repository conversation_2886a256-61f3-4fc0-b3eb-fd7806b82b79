<view class="page-container data-v-88f4bae4"><view class="custom-navbar data-v-88f4bae4"><view class="status-bar data-v-88f4bae4" style="{{'height:' + a}}"></view><view class="navbar-content data-v-88f4bae4"><view class="navbar-left data-v-88f4bae4"><view class="back-button data-v-88f4bae4" bindtap="{{d}}"><svg wx:if="{{c}}" u-s="{{['d']}}" class="back-icon data-v-88f4bae4" u-i="88f4bae4-0" bind:__l="__l" u-p="{{c}}"><path wx:if="{{b}}" class="data-v-88f4bae4" u-i="88f4bae4-1,88f4bae4-0" bind:__l="__l" u-p="{{b}}"/></svg></view></view><view class="navbar-title data-v-88f4bae4"><text class="title-text data-v-88f4bae4">活动列表</text></view><view class="navbar-right data-v-88f4bae4"><view class="filter-button data-v-88f4bae4" bindtap="{{g}}"><svg wx:if="{{f}}" class="data-v-88f4bae4" u-s="{{['d']}}" u-i="88f4bae4-2" bind:__l="__l" u-p="{{f}}"><path wx:if="{{e}}" class="data-v-88f4bae4" u-i="88f4bae4-3,88f4bae4-2" bind:__l="__l" u-p="{{e}}"/></svg></view></view></view></view><view class="filter-tabs data-v-88f4bae4"><view class="{{['tab-item', 'data-v-88f4bae4', h && 'active']}}" bindtap="{{i}}"><text class="tab-text data-v-88f4bae4">全部</text></view><view class="{{['tab-item', 'data-v-88f4bae4', j && 'active']}}" bindtap="{{k}}"><text class="tab-text data-v-88f4bae4">进行中</text></view><view class="{{['tab-item', 'data-v-88f4bae4', l && 'active']}}" bindtap="{{m}}"><text class="tab-text data-v-88f4bae4">未开始</text></view><view class="{{['tab-item', 'data-v-88f4bae4', n && 'active']}}" bindtap="{{o}}"><text class="tab-text data-v-88f4bae4">已结束</text></view></view><scroll-view class="activity-list data-v-88f4bae4" scroll-y="true" bindscrolltolower="{{U}}"><block wx:if="{{p}}"><view class="empty-state data-v-88f4bae4"><svg wx:if="{{v}}" u-s="{{['d']}}" class="empty-icon data-v-88f4bae4" u-i="88f4bae4-4" bind:__l="__l" u-p="{{v}}"><path wx:if="{{q}}" class="data-v-88f4bae4" u-i="88f4bae4-5,88f4bae4-4" bind:__l="__l" u-p="{{q}}"/><path wx:if="{{r}}" class="data-v-88f4bae4" u-i="88f4bae4-6,88f4bae4-4" bind:__l="__l" u-p="{{r}}"/><path wx:if="{{s}}" class="data-v-88f4bae4" u-i="88f4bae4-7,88f4bae4-4" bind:__l="__l" u-p="{{s}}"/><path wx:if="{{t}}" class="data-v-88f4bae4" u-i="88f4bae4-8,88f4bae4-4" bind:__l="__l" u-p="{{t}}"/></svg><text class="empty-text data-v-88f4bae4">暂无活动信息</text></view></block><block wx:else><view wx:for="{{w}}" wx:for-item="activity" wx:key="I" class="activity-card data-v-88f4bae4" bindtap="{{activity.J}}"><view class="card-image data-v-88f4bae4" style="{{'background-image:' + activity.a}}"></view><view class="card-content data-v-88f4bae4"><text class="card-title data-v-88f4bae4">{{activity.b}}</text><view class="card-info data-v-88f4bae4"><view class="info-item data-v-88f4bae4"><svg wx:if="{{z}}" u-s="{{['d']}}" class="info-icon data-v-88f4bae4" u-i="{{activity.e}}" bind:__l="__l" u-p="{{z}}"><path wx:if="{{x}}" class="data-v-88f4bae4" u-i="{{activity.c}}" bind:__l="__l" u-p="{{x}}"/><path wx:if="{{y}}" class="data-v-88f4bae4" u-i="{{activity.d}}" bind:__l="__l" u-p="{{y}}"/></svg><text class="info-text data-v-88f4bae4">{{activity.f}} ~ {{activity.g}}</text></view><view class="info-item data-v-88f4bae4"><svg wx:if="{{E}}" u-s="{{['d']}}" class="info-icon data-v-88f4bae4" u-i="{{activity.l}}" bind:__l="__l" u-p="{{E}}"><path wx:if="{{A}}" class="data-v-88f4bae4" u-i="{{activity.h}}" bind:__l="__l" u-p="{{A}}"/><path wx:if="{{B}}" class="data-v-88f4bae4" u-i="{{activity.i}}" bind:__l="__l" u-p="{{B}}"/><path wx:if="{{C}}" class="data-v-88f4bae4" u-i="{{activity.j}}" bind:__l="__l" u-p="{{C}}"/><path wx:if="{{D}}" class="data-v-88f4bae4" u-i="{{activity.k}}" bind:__l="__l" u-p="{{D}}"/></svg><text class="info-text data-v-88f4bae4">{{activity.m}}人参与</text></view></view><view class="{{['card-status', 'data-v-88f4bae4', activity.o && 'status-ongoing', activity.p && 'status-upcoming', activity.q && 'status-ended']}}">{{activity.n}}</view><view class="card-actions data-v-88f4bae4"><view class="action-button edit data-v-88f4bae4" catchtap="{{activity.v}}"><svg wx:if="{{H}}" u-s="{{['d']}}" class="action-icon data-v-88f4bae4" u-i="{{activity.t}}" bind:__l="__l" u-p="{{H}}"><path wx:if="{{F}}" class="data-v-88f4bae4" u-i="{{activity.r}}" bind:__l="__l" u-p="{{F}}"/><path wx:if="{{G}}" class="data-v-88f4bae4" u-i="{{activity.s}}" bind:__l="__l" u-p="{{G}}"/></svg><text class="action-text data-v-88f4bae4">编辑</text></view><view class="action-button promote data-v-88f4bae4" catchtap="{{activity.A}}"><svg wx:if="{{L}}" u-s="{{['d']}}" class="action-icon data-v-88f4bae4" u-i="{{activity.z}}" bind:__l="__l" u-p="{{L}}"><path wx:if="{{I}}" class="data-v-88f4bae4" u-i="{{activity.w}}" bind:__l="__l" u-p="{{I}}"/><path wx:if="{{J}}" class="data-v-88f4bae4" u-i="{{activity.x}}" bind:__l="__l" u-p="{{J}}"/><path wx:if="{{K}}" class="data-v-88f4bae4" u-i="{{activity.y}}" bind:__l="__l" u-p="{{K}}"/></svg><text class="action-text data-v-88f4bae4">推广</text></view><view class="action-button share data-v-88f4bae4" catchtap="{{activity.H}}"><svg wx:if="{{R}}" u-s="{{['d']}}" class="action-icon data-v-88f4bae4" u-i="{{activity.G}}" bind:__l="__l" u-p="{{R}}"><path wx:if="{{M}}" class="data-v-88f4bae4" u-i="{{activity.B}}" bind:__l="__l" u-p="{{M}}"/><path wx:if="{{N}}" class="data-v-88f4bae4" u-i="{{activity.C}}" bind:__l="__l" u-p="{{N}}"/><path wx:if="{{O}}" class="data-v-88f4bae4" u-i="{{activity.D}}" bind:__l="__l" u-p="{{O}}"/><path wx:if="{{P}}" class="data-v-88f4bae4" u-i="{{activity.E}}" bind:__l="__l" u-p="{{P}}"/><path wx:if="{{Q}}" class="data-v-88f4bae4" u-i="{{activity.F}}" bind:__l="__l" u-p="{{Q}}"/></svg><text class="action-text data-v-88f4bae4">转发</text></view></view></view></view></block><view wx:if="{{S}}" class="loading-container data-v-88f4bae4"><text class="loading-text data-v-88f4bae4">加载中...</text></view><view wx:if="{{T}}" class="no-more data-v-88f4bae4"><text class="data-v-88f4bae4">没有更多数据了</text></view></scroll-view></view>