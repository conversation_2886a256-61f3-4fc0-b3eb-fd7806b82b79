/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.group-create-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 100px;
  display: flex;
  flex-direction: column;
}

/* 自定义导航栏 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 进度指示器 */
.progress-container {
  padding: 20px 15px;
  background-color: #fff;
  margin-bottom: 10px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
}
.progress-bar {
  height: 4px;
  background-color: #EBEDF5;
  border-radius: 2px;
  margin-bottom: 15px;
  position: relative;
  overflow: hidden;
}
.progress-track {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: #EBEDF5;
}
.progress-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(to right, #9040FF, #5E35B1);
  border-radius: 2px;
  transition: width 0.3s;
}
.progress-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}
.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
}
.step-line {
  height: 1px;
  background-color: #EBEDF5;
  flex: 1;
  margin: 0 5px;
  transition: background-color 0.3s;
}
.step-line.active {
  background-color: #9040FF;
}
.step-dot {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #EBEDF5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  transition: all 0.3s;
}
.step-dot .check-icon {
  width: 10px;
  height: 6px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(-45deg) translate(1px, -1px);
}
.step-label {
  font-size: 12px;
  color: #999;
  white-space: nowrap;
}
.step.active .step-dot {
  background-color: #9040FF;
  color: #fff;
}
.step.active .step-label {
  color: #9040FF;
  font-weight: 500;
}
.step.completed .step-dot {
  background-color: #9040FF;
}
.step.completed .step-label {
  color: #9040FF;
}

/* 表单容器 */
.form-scroll-view {
  flex: 1;
  padding-bottom: 20px;
}
.form-section {
  margin: 15px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}
.section-header {
  padding: 15px;
  border-bottom: 1px solid #F0F0F0;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 选择方式 */
.selection-options {
  padding: 15px;
}
.selection-option {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 10px;
  background-color: #F9F9F9;
  transition: all 0.3s;
}
.selection-option.active {
  background-color: rgba(126, 48, 225, 0.05);
  border-left: 3px solid #7E30E1;
}
.option-radio {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  border: 2px solid #CCCCCC;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.option-radio .radio-inner {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #7E30E1;
}
.selection-option.active .option-radio {
  border-color: #7E30E1;
}
.option-content {
  flex: 1;
}
.option-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: block;
}
.option-desc {
  font-size: 12px;
  color: #999;
}

/* 商品选择 */
.product-selection {
  padding: 15px;
}
.search-bar {
  display: flex;
  align-items: center;
  background-color: #F5F5F5;
  border-radius: 20px;
  padding: 8px 15px;
  margin-bottom: 15px;
}
.search-icon {
  width: 14px;
  height: 14px;
  border: 2px solid #999;
  border-radius: 50%;
  position: relative;
  margin-right: 8px;
}
.search-icon:after {
  content: "";
  position: absolute;
  width: 2px;
  height: 6px;
  background-color: #999;
  bottom: -4px;
  right: -2px;
  transform: rotate(-45deg);
}
.search-input {
  flex: 1;
  height: 20px;
  font-size: 14px;
}
.filter-options {
  display: flex;
  justify-content: space-around;
  padding: 10px 0;
  margin-bottom: 15px;
  border-bottom: 1px solid #F0F0F0;
}
.filter-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;
}
.arrow-down {
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid #666;
  margin-left: 5px;
}

/* 商品列表 */
.product-list {
  margin-bottom: 20px;
}
.product-item {
  display: flex;
  padding: 15px 0;
  border-bottom: 1px solid #F0F0F0;
}
.checkbox {
  width: 22px;
  height: 22px;
  border: 2px solid #CCC;
  border-radius: 4px;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.checkbox.checked {
  background-color: #7E30E1;
  border-color: #7E30E1;
}
.checkbox .checkbox-inner {
  width: 10px;
  height: 6px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(-45deg) translate(1px, -1px);
}
.product-img {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  margin-right: 10px;
  background-color: #f5f5f5;
  object-fit: cover;
}
.product-info {
  flex: 1;
}
.product-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
  display: block;
}
.product-price {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}
.price-current {
  font-size: 15px;
  font-weight: 600;
  color: #FF3B30;
  margin-right: 5px;
}
.price-original {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}
.product-stock {
  font-size: 12px;
  color: #999;
}

/* 已选商品 */
.selected-products {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #F0F0F0;
}
.selected-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.selected-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}
.clear-all {
  font-size: 12px;
  color: #7E30E1;
}
.selected-list {
  display: flex;
  flex-wrap: wrap;
}
.selected-item {
  width: calc(33.33% - 10px);
  margin-right: 10px;
  margin-bottom: 10px;
  position: relative;
  background-color: #F9F9F9;
  border-radius: 8px;
  padding: 8px;
  overflow: hidden;
}
.selected-img {
  width: 100%;
  height: 70px;
  border-radius: 4px;
  margin-bottom: 5px;
  object-fit: cover;
}
.selected-info {
  padding-top: 5px;
}
.selected-name {
  font-size: 12px;
  color: #333;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;
}
.selected-price {
  font-size: 12px;
  color: #FF3B30;
  font-weight: 500;
}
.remove-btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.3);
  color: #fff;
  font-size: 16px;
  border-radius: 0 0 0 8px;
}

/* 新建商品 */
.create-product {
  padding: 30px 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.create-product-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60%;
  height: 44px;
  border-radius: 22px;
  background: linear-gradient(135deg, #34C759, #32ADE6);
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
  box-shadow: 0 5px 15px rgba(52, 199, 89, 0.2);
}
.btn-icon {
  font-size: 20px;
  margin-right: 5px;
}
.create-product-tip {
  font-size: 12px;
  color: #999;
}

/* 步骤2: 表单样式 */
.form-content {
  padding: 15px;
}
.form-group {
  margin-bottom: 20px;
}
.form-label {
  margin-bottom: 10px;
}
.label-text {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}
.label-text.required:after {
  content: "*";
  color: #FF3B30;
  margin-left: 4px;
}
.form-field {
  position: relative;
}
.form-input {
  width: 100%;
  height: 44px;
  background-color: #F5F5F5;
  border-radius: 8px;
  padding: 0 15px;
  font-size: 14px;
  color: #333;
}
.input-counter {
  position: absolute;
  right: 15px;
  top: 12px;
  font-size: 12px;
  color: #999;
}
.price-input-wrapper {
  position: relative;
}
.price-symbol {
  position: absolute;
  left: 15px;
  top: 12px;
  font-size: 14px;
  color: #333;
}
.price-input {
  padding-left: 30px;
}
.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
  display: block;
}
.number-picker {
  display: flex;
  align-items: center;
  height: 44px;
}
.number-btn {
  width: 44px;
  height: 44px;
  background-color: #F5F5F5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 600;
  color: #999;
}
.number-btn.minus {
  border-radius: 8px 0 0 8px;
}
.number-btn.plus {
  border-radius: 0 8px 8px 0;
}
.number-input {
  width: 60px;
  height: 44px;
  background-color: #F5F5F5;
  text-align: center;
  font-size: 16px;
  color: #333;
}
.unit-text {
  margin-left: 8px;
  font-size: 14px;
  color: #666;
}
.radio-group {
  display: flex;
  margin-bottom: 15px;
}
.radio-item {
  display: flex;
  align-items: center;
  margin-right: 20px;
  padding: 5px 0;
}
.radio-dot {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: 2px solid #CCCCCC;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.radio-dot .radio-dot-inner {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #7E30E1;
}
.radio-item.active .radio-dot {
  border-color: #7E30E1;
}
.radio-label {
  font-size: 14px;
  color: #333;
}
.datetime-picker {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}
.datetime-field {
  background: #F8FAFC;
  border-radius: 4px;
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 12px;
  margin-bottom: 8px;
  position: relative;
}
.datetime-placeholder {
  color: #999;
  font-size: 14px;
}
.datetime-value {
  color: #333;
  font-size: 14px;
}
.datetime-icon {
  width: 10px;
  height: 10px;
  border-top: 2px solid #999;
  border-right: 2px solid #999;
  transform: rotate(45deg);
  position: absolute;
  right: 12px;
}
.datetime-separator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 0;
  color: #666;
}
.picker-component {
  width: 100%;
}

/* 调整时间日期选择布局 */
@media screen and (min-width: 375px) {
.datetime-picker {
    flex-direction: row;
    flex-wrap: wrap;
}
.picker-component {
    width: 48%;
    margin-right: 2%;
}
.datetime-separator {
    display: flex;
    width: 100%;
    justify-content: center;
    margin: 5px 0;
}
}
/* 步骤3：确认创建样式 */
.confirm-content {
  padding: 15px;
}
.activity-card {
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
.activity-header {
  position: relative;
  height: 120px;
}
.activity-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.activity-overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.7));
}
.activity-info {
  position: relative;
  padding: 15px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}
.activity-name {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 5px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}
.activity-time {
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}
.activity-body {
  padding: 15px;
  background-color: #fff;
  display: flex;
}
.product-preview {
  width: 70px;
  height: 70px;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 15px;
}
.product-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.product-count {
  position: absolute;
  right: 0;
  bottom: 0;
  padding: 2px 6px;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 10px;
  border-top-left-radius: 8px;
}
.activity-detail {
  flex: 1;
}
.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}
.detail-label {
  font-size: 13px;
  color: #666;
}
.detail-value {
  font-size: 13px;
  color: #333;
  font-weight: 500;
}
.detail-value.price {
  color: #FF3B30;
}
.confirm-products,
.confirm-rules {
  margin-bottom: 20px;
}
.confirm-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}
.confirm-product-list {
  background-color: #F9F9F9;
  border-radius: 8px;
}
.confirm-product-item {
  display: flex;
  padding: 12px;
  border-bottom: 1px solid #F0F0F0;
}
.confirm-product-item:last-child {
  border-bottom: none;
}
.confirm-product-img {
  width: 50px;
  height: 50px;
  border-radius: 4px;
  margin-right: 10px;
}
.confirm-product-info {
  flex: 1;
}
.confirm-product-name {
  font-size: 13px;
  color: #333;
  margin-bottom: 5px;
  display: block;
}
.confirm-product-price {
  display: flex;
  flex-direction: column;
}
.confirm-price-market {
  color: #999;
  text-decoration: line-through;
  font-size: 11px;
  margin-right: 8px;
  display: block;
}
.confirm-price-regular {
  color: #FF6B00;
  text-decoration: line-through;
  font-size: 11px;
  margin-right: 8px;
  display: block;
  margin-top: 2px;
}
.confirm-price-group {
  font-size: 12px;
  color: #666;
}
.confirm-price-group .price-value {
  color: #FF3B30;
  font-weight: 500;
}
.rules-content {
  background-color: #F9F9F9;
  border-radius: 8px;
  padding: 12px 15px;
}
.rule-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
}
.rule-item:last-child {
  margin-bottom: 0;
}
.rule-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #7E30E1;
  margin-top: 6px;
  margin-right: 8px;
  flex-shrink: 0;
}
.rule-text {
  font-size: 13px;
  color: #666;
  flex: 1;
  line-height: 1.5;
}
.preview-tip {
  display: flex;
  align-items: center;
  background-color: rgba(126, 48, 225, 0.05);
  border-radius: 8px;
  padding: 12px;
}
.tip-icon {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: #7E30E1;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-style: italic;
  margin-right: 10px;
}
.tip-text {
  font-size: 12px;
  color: #7E30E1;
  flex: 1;
  line-height: 1.5;
}

/* 底部操作按钮 */
.footer-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10px 15px;
  padding-bottom: calc(10px + env(safe-area-inset-bottom));
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 99;
}
.btn-primary {
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #fff;
  border: none;
  border-radius: 25px;
  height: 50px;
  line-height: 50px;
  font-size: 16px;
  font-weight: 500;
  flex: 1;
  margin-left: 10px;
  box-shadow: 0 5px 15px rgba(126, 48, 225, 0.2);
  transition: all 0.3s;
}
.btn-primary:active {
  transform: translateY(2px);
  box-shadow: 0 3px 8px rgba(126, 48, 225, 0.2);
}
.btn-secondary {
  background-color: #F0F0F0;
  color: #666;
  border: none;
  border-radius: 25px;
  height: 50px;
  line-height: 50px;
  font-size: 16px;
  font-weight: 500;
  width: 120px;
  transition: all 0.3s;
}
.btn-secondary:active {
  background-color: #E6E6E6;
}

/* 开关容器 */
.switch-container {
  display: flex;
  align-items: center;
  height: 40px;
}
.price-regular {
  color: #FF6B00;
  text-decoration: line-through;
  font-size: 12px;
  margin-right: 8px;
}
.price-market {
  color: #999;
  text-decoration: line-through;
  font-size: 12px;
  margin-right: 8px;
}
.confirm-price-market {
  color: #999;
  text-decoration: line-through;
  font-size: 11px;
  margin-right: 8px;
  display: block;
}
.confirm-price-regular {
  color: #FF6B00;
  text-decoration: line-through;
  font-size: 11px;
  margin-right: 8px;
  display: block;
  margin-top: 2px;
}

/* 套餐拼团样式 */
.package-settings {
  margin-top: 15px;
  padding: 15px;
  background-color: #F8F9FC;
  border-radius: 10px;
}
.form-textarea {
  width: 100%;
  height: 80px;
  background: #FFFFFF;
  border: 1px solid #E5E7EB;
  border-radius: 8px;
  padding: 10px;
  font-size: 14px;
  color: #333;
}
.package-items {
  display: flex;
  flex-direction: column;
  gap: 15px;
}
.package-item {
  background: #FFFFFF;
  border: 1px solid #E5E7EB;
  border-radius: 8px;
  overflow: hidden;
}
.package-item-header {
  background: #F0F2F5;
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.package-item-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}
.package-item-remove {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  line-height: 1;
}
.package-item-content {
  padding: 10px 15px;
}
.package-item-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.package-item-row:last-child {
  margin-bottom: 0;
}
.package-item-label {
  width: 80px;
  font-size: 14px;
  color: #666;
}
.package-item-input {
  flex: 1;
  height: 36px;
  background: #F5F7FA;
  border: 1px solid #E5E7EB;
  border-radius: 6px;
  padding: 0 10px;
  font-size: 14px;
  color: #333;
}
.add-package-item {
  height: 44px;
  background: rgba(144, 64, 255, 0.1);
  border: 1px dashed #9040FF;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.add-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #9040FF;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin-right: 8px;
}
.add-text {
  font-size: 14px;
  color: #9040FF;
}
.number-picker.small {
  height: 32px;
}
.number-picker.small .number-btn {
  width: 24px;
  height: 24px;
}
.number-picker.small .number-input {
  width: 40px;
  height: 24px;
  font-size: 13px;
}

/* 套餐预览样式 */
.package-preview {
  width: 100px;
  height: 100px;
  background: #F5F7FA;
  border-radius: 8px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  overflow: hidden;
}
.package-badge {
  position: absolute;
  top: 0;
  left: 0;
  background: #9040FF;
  color: #FFFFFF;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 0 0 8px 0;
}
.package-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  text-align: center;
  padding: 0 5px;
}
.package-items-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.package-item-text {
  font-size: 10px;
  color: #666;
  line-height: 1.4;
}
.confirm-package {
  margin-top: 20px;
  background: #FFFFFF;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.package-description {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px dashed #E5E7EB;
}
.package-items-list {
  margin-bottom: 15px;
}
.package-item-detail {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #F5F7FA;
}
.package-item-detail:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}
.package-item-number {
  width: 24px;
  height: 24px;
  background: #F0F2F5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: #666;
  margin-right: 10px;
}
.package-item-info {
  flex: 1;
}
.package-item-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
}
.package-item-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.package-item-price {
  font-size: 12px;
  color: #FF3B30;
}
.package-item-quantity {
  font-size: 12px;
  color: #999;
}
.package-summary {
  background: #F8FAFC;
  border-radius: 8px;
  padding: 10px 15px;
}
.package-summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}
.package-summary-row:last-child {
  margin-bottom: 0;
}
.package-summary-row.highlight {
  margin: 10px 0;
  padding: 8px 0;
  border-top: 1px dashed #E5E7EB;
  border-bottom: 1px dashed #E5E7EB;
}
.summary-label {
  font-size: 13px;
  color: #666;
}
.summary-value {
  font-size: 13px;
  color: #333;
  font-weight: 600;
}
.summary-value.save {
  color: #FF3B30;
}
.confirm-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

/* 支付和核销设置 */
.confirm-payment-verification {
  margin-top: 20px;
  background: #FFFFFF;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.setting-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.setting-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #7E30E1;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}
.setting-info {
  flex: 1;
}
.setting-label {
  font-size: 14px;
  color: #333;
}
.setting-value {
  font-size: 14px;
  color: #666;
}

/* 预览卡片样式 */
.preview-card {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
}
.preview-header {
  padding: 20rpx;
  border-bottom: 1rpx solid #F0F0F0;
}
.preview-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.preview-dates {
  font-size: 24rpx;
  color: #999999;
  margin-top: 8rpx;
  display: block;
}
.preview-content {
  padding: 20rpx;
}
.preview-products {
  margin-bottom: 20rpx;
}
.preview-product-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}
.preview-product-item:last-child {
  border-bottom: none;
}
.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
}
.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.product-name {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.product-price {
  display: flex;
  align-items: baseline;
}
.group-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF6B6B;
}
.original-price {
  font-size: 24rpx;
  color: #999999;
  text-decoration: line-through;
  margin-left: 12rpx;
}
.group-rules {
  background-color: #F8F9FA;
  border-radius: 8rpx;
  padding: 16rpx;
}
.rule-item {
  display: flex;
  justify-content: space-between;
  padding: 8rpx 0;
}
.rule-label {
  font-size: 26rpx;
  color: #666666;
}
.rule-value {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
}

/* 表单操作区 */
.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}
.btn-secondary, .btn-primary {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
}
.btn-secondary {
  background-color: #F5F5F5;
  color: #666666;
  margin-right: 20rpx;
}
.btn-primary {
  background: linear-gradient(135deg, #6B0FBE, #9013FE);
  color: #FFFFFF;
}