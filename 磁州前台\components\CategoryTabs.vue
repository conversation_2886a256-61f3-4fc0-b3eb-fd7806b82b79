<template>
  <view class="category-tabs-container">
    <scroll-view 
      class="category-tabs" 
      scroll-x 
      :scroll-left="scrollLeft"
      :scroll-with-animation="true"
      :enhanced="true"
      :show-scrollbar="false"
      :scroll-into-view="scrollIntoView"
    >
      <view 
        v-for="(category, index) in categories" 
        :key="index"
        :id="'category-' + index"
        class="category-tab"
        :class="{ active: activeCategory === category.value }"
        @tap="selectCategory(category.value, index)"
      >
        <view class="category-icon" v-if="category.icon">
          <svg v-if="category.icon === 'job'" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
            <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
          </svg>
          <svg v-else-if="category.icon === 'house'" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
            <polyline points="9 22 9 12 15 12 15 22"></polyline>
          </svg>
          <svg v-else-if="category.icon === 'secondhand'" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="9" cy="21" r="1"></circle>
            <circle cx="20" cy="21" r="1"></circle>
            <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
          </svg>
          <svg v-else-if="category.icon === 'service'" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
            <line x1="12" y1="17" x2="12.01" y2="17"></line>
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="8" y1="6" x2="21" y2="6"></line>
            <line x1="8" y1="12" x2="21" y2="12"></line>
            <line x1="8" y1="18" x2="21" y2="18"></line>
            <line x1="3" y1="6" x2="3.01" y2="6"></line>
            <line x1="3" y1="12" x2="3.01" y2="12"></line>
            <line x1="3" y1="18" x2="3.01" y2="18"></line>
          </svg>
        </view>
        <text class="category-name">{{category.label}}</text>
      </view>
    </scroll-view>
    
    <!-- 筛选按钮 -->
    <view class="filter-button" @tap="openFilter">
      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <line x1="4" y1="21" x2="4" y2="14"></line>
        <line x1="4" y1="10" x2="4" y2="3"></line>
        <line x1="12" y1="21" x2="12" y2="12"></line>
        <line x1="12" y1="8" x2="12" y2="3"></line>
        <line x1="20" y1="21" x2="20" y2="16"></line>
        <line x1="20" y1="12" x2="20" y2="3"></line>
        <line x1="1" y1="14" x2="7" y2="14"></line>
        <line x1="9" y1="8" x2="15" y2="8"></line>
        <line x1="17" y1="16" x2="23" y2="16"></line>
      </svg>
      <text>筛选</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CategoryTabs',
  props: {
    categories: {
      type: Array,
      required: true,
      default: () => [
        { label: '全部', value: 'all', icon: 'all' },
        { label: '招聘信息', value: '招聘信息', icon: 'job' },
        { label: '求职信息', value: '求职信息', icon: 'job' },
        { label: '房屋出租', value: '房屋出租', icon: 'house' },
        { label: '房屋出售', value: '房屋出售', icon: 'house' },
        { label: '二手闲置', value: '二手闲置', icon: 'secondhand' },
        { label: '二手车辆', value: '二手车辆', icon: 'secondhand' },
        { label: '到家服务', value: '到家服务', icon: 'service' },
        { label: '寻找服务', value: '寻找服务', icon: 'service' },
        { label: '生意转让', value: '生意转让', icon: 'business' },
        { label: '宠物信息', value: '宠物信息', icon: 'pet' },
        { label: '商家活动', value: '商家活动', icon: 'merchant' },
        { label: '婚恋交友', value: '婚恋交友', icon: 'dating' },
        { label: '车辆服务', value: '车辆服务', icon: 'car' },
        { label: '磁州拼车', value: '磁州拼车', icon: 'carpool' },
        { label: '教育培训', value: '教育培训', icon: 'education' },
        { label: '其他服务', value: '其他服务', icon: 'other' }
      ]
    },
    activeCategory: {
      type: String,
      default: 'all'
    }
  },
  data() {
    return {
      scrollLeft: 0,
      scrollIntoView: '',
    }
  },
  watch: {
    activeCategory(newVal) {
      this.scrollToCategory(newVal);
    }
  },
  mounted() {
    this.scrollToCategory(this.activeCategory);
  },
  methods: {
    selectCategory(category, index) {
      this.$emit('category-change', category);
      this.scrollIntoView = 'category-' + index;
    },
    scrollToCategory(category) {
      const index = this.categories.findIndex(c => c.value === category);
      if (index > -1) {
        this.scrollIntoView = 'category-' + index;
      }
    },
    openFilter() {
      this.$emit('open-filter');
    }
  }
}
</script>

<style>
.category-tabs-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.category-tabs {
  flex: 1;
  white-space: nowrap;
  height: 80rpx;
}

.category-tab {
  display: inline-flex;
  align-items: center;
  padding: 12rpx 24rpx;
  margin: 0 8rpx;
  background-color: #F2F2F7;
  border-radius: 35rpx;
  transition: all 0.3s;
}

.category-tab:first-child {
  margin-left: 20rpx;
}

.category-tab:last-child {
  margin-right: 20rpx;
}

.category-tab.active {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
}

.category-icon {
  margin-right: 8rpx;
  color: #8E8E93;
  display: flex;
  align-items: center;
  justify-content: center;
}

.active .category-icon {
  color: #FFFFFF;
}

.category-name {
  font-size: 26rpx;
  color: #636366;
  font-weight: 500;
}

.active .category-name {
  color: #FFFFFF;
  font-weight: 600;
}

.filter-button {
  display: flex;
  align-items: center;
  padding: 12rpx 24rpx;
  margin-right: 20rpx;
  background: linear-gradient(135deg, #F2F2F7, #E5E5EA);
  border-radius: 35rpx;
  color: #636366;
}

.filter-button svg {
  margin-right: 8rpx;
  color: #636366;
}

.filter-button text {
  font-size: 26rpx;
  font-weight: 500;
}
</style> 