"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_rect = common_vendor.resolveComponent("rect");
  (_component_path + _component_svg + _component_circle + _component_rect)();
}
const _sfc_main = {
  __name: "detail",
  setup(__props) {
    const flashId = common_vendor.ref(null);
    const showOptions = common_vendor.ref(false);
    const flashSaleData = common_vendor.reactive({
      id: 1,
      name: "夏季清凉风扇特惠",
      flashPrice: 59.9,
      originalPrice: 129.9,
      discountText: "4.6折",
      stockTotal: 100,
      stockSold: 86,
      stockRemain: 14,
      viewCount: 1564,
      conversionRate: 5.5,
      progressPercent: 86,
      images: [
        "/static/images/flash-item1.jpg",
        "/static/images/flash-item1-2.jpg",
        "/static/images/flash-item1-3.jpg"
      ],
      detailImages: [
        "/static/images/flash-detail1.jpg",
        "/static/images/flash-detail2.jpg",
        "/static/images/flash-detail3.jpg"
      ],
      timeRange: "2023-07-01 12:00 ~ 14:00",
      status: "active",
      statusText: "进行中",
      statusClass: "status-active",
      purchaseLimit: 2,
      rules: "活动期间每人限购2件，秒杀商品不支持退换货，数量有限，先到先得",
      description: "<p>这是一款高品质的夏季清凉风扇，采用先进技术，风力强劲，静音设计，让您的夏天更加舒适。</p><p>规格参数：</p><ul><li>功率：30W</li><li>风速：3档可调</li><li>电池容量：4000mAh</li><li>续航时间：4-8小时</li><li>材质：环保ABS</li></ul>",
      orders: [
        {
          userName: "张先生",
          userAvatar: "/static/images/avatar1.jpg",
          quantity: 1,
          time: "2023-07-01 12:05"
        },
        {
          userName: "李女士",
          userAvatar: "/static/images/avatar2.jpg",
          quantity: 2,
          time: "2023-07-01 12:08"
        },
        {
          userName: "王先生",
          userAvatar: "/static/images/avatar3.jpg",
          quantity: 1,
          time: "2023-07-01 12:15"
        }
      ]
    });
    const countdown = common_vendor.reactive({
      days: "00",
      hours: "01",
      minutes: "25",
      seconds: "36"
    });
    const countdownLabel = common_vendor.computed(() => {
      if (flashSaleData.status === "upcoming") {
        return "距开始还剩";
      } else if (flashSaleData.status === "active") {
        return "距结束还剩";
      }
      return "";
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const showMoreOptions = () => {
      showOptions.value = true;
    };
    const hideMoreOptions = () => {
      showOptions.value = false;
    };
    const refreshData = () => {
      common_vendor.index.showLoading({
        title: "刷新中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "数据已更新",
          icon: "success"
        });
      }, 1e3);
    };
    const editFlashSale = () => {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/flash/edit?id=${flashSaleData.id}`
      });
    };
    const shareFlashSale = () => {
      common_vendor.index.showToast({
        title: "生成分享链接中...",
        icon: "loading",
        duration: 1500
      });
      setTimeout(() => {
        common_vendor.index.showModal({
          title: "分享秒杀活动",
          content: `活动"${flashSaleData.name}"的分享链接已创建，可发送给客户或分享到社交媒体`,
          confirmText: "复制链接",
          cancelText: "取消",
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.setClipboardData({
                data: `https://example.com/flash-sale/${flashSaleData.id}`,
                success: () => {
                  common_vendor.index.showToast({
                    title: "链接已复制",
                    icon: "success"
                  });
                }
              });
            }
          }
        });
      }, 1500);
    };
    const toggleStatus = () => {
      const action = flashSaleData.status === "active" ? "暂停" : "启用";
      common_vendor.index.showModal({
        title: `确认${action}`,
        content: `确定要${action}该秒杀活动吗？`,
        confirmText: "确定",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            if (flashSaleData.status === "active") {
              flashSaleData.status = "upcoming";
              flashSaleData.statusText = "未开始";
              flashSaleData.statusClass = "status-upcoming";
            } else {
              flashSaleData.status = "active";
              flashSaleData.statusText = "进行中";
              flashSaleData.statusClass = "status-active";
            }
            common_vendor.index.showToast({
              title: `${action}成功`,
              icon: "success"
            });
          }
        }
      });
    };
    const deleteFlashSale = () => {
      common_vendor.index.showModal({
        title: "确认删除",
        content: `确定要删除"${flashSaleData.name}"秒杀活动吗？一旦删除将无法恢复。`,
        confirmText: "删除",
        confirmColor: "#FF3B30",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "删除成功",
              icon: "success",
              duration: 1500,
              success: () => {
                setTimeout(() => {
                  common_vendor.index.navigateBack();
                }, 1500);
              }
            });
          }
        }
      });
    };
    const viewAllOrders = () => {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/flash/orders?id=${flashSaleData.id}`
      });
    };
    const viewQrCode = () => {
      hideMoreOptions();
      common_vendor.index.showLoading({
        title: "生成二维码..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.previewImage({
          urls: ["/static/images/flash-qrcode.png"]
        });
      }, 1e3);
    };
    const viewDataAnalysis = () => {
      hideMoreOptions();
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/flash/analysis?id=${flashSaleData.id}`
      });
    };
    const copyLink = () => {
      hideMoreOptions();
      common_vendor.index.setClipboardData({
        data: `https://example.com/flash-sale/${flashSaleData.id}`,
        success: () => {
          common_vendor.index.showToast({
            title: "链接已复制",
            icon: "success"
          });
        }
      });
    };
    const exportData = () => {
      hideMoreOptions();
      common_vendor.index.showLoading({
        title: "导出中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "数据已导出",
          icon: "success"
        });
      }, 1500);
    };
    common_vendor.onMounted(() => {
      getOpenerEventChannel();
      const query = common_vendor.index.getSystemInfoSync().platform === "h5" ? location.href.split("?")[1] : "";
      if (query) {
        const params = query.split("&").reduce((res, item) => {
          const [key, value] = item.split("=");
          res[key] = value;
          return res;
        }, {});
        flashId.value = params.id;
      } else {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        if (currentPage && currentPage.options) {
          flashId.value = currentPage.options.id;
        }
      }
      loadFlashSaleData();
      startCountdown();
    });
    const loadFlashSaleData = () => {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-marketing/pages/marketing/flash/detail.vue:557", "加载秒杀活动数据, ID:", flashId.value);
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
      }, 500);
    };
    const startCountdown = () => {
      const targetTime = /* @__PURE__ */ new Date();
      if (flashSaleData.status === "upcoming") {
        targetTime.setHours(targetTime.getHours() + 2);
      } else if (flashSaleData.status === "active") {
        targetTime.setHours(targetTime.getHours() + 1);
        targetTime.setMinutes(targetTime.getMinutes() + 30);
      }
      const updateCountdown = () => {
        const now = /* @__PURE__ */ new Date();
        const diff = targetTime - now;
        if (diff <= 0) {
          countdown.days = "00";
          countdown.hours = "00";
          countdown.minutes = "00";
          countdown.seconds = "00";
          if (flashSaleData.status === "upcoming") {
            flashSaleData.status = "active";
            flashSaleData.statusText = "进行中";
            flashSaleData.statusClass = "status-active";
            startCountdown();
          } else if (flashSaleData.status === "active") {
            flashSaleData.status = "ended";
            flashSaleData.statusText = "已结束";
            flashSaleData.statusClass = "status-ended";
          }
          return;
        }
        const days = Math.floor(diff / (1e3 * 60 * 60 * 24));
        const hours = Math.floor(diff % (1e3 * 60 * 60 * 24) / (1e3 * 60 * 60));
        const minutes = Math.floor(diff % (1e3 * 60 * 60) / (1e3 * 60));
        const seconds = Math.floor(diff % (1e3 * 60) / 1e3);
        countdown.days = days.toString().padStart(2, "0");
        countdown.hours = hours.toString().padStart(2, "0");
        countdown.minutes = minutes.toString().padStart(2, "0");
        countdown.seconds = seconds.toString().padStart(2, "0");
        setTimeout(updateCountdown, 1e3);
      };
      updateCountdown();
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          d: "M15 18L9 12L15 6",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        b: common_vendor.p({
          width: "24",
          height: "24",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        c: common_vendor.o(goBack),
        d: common_vendor.p({
          cx: "12",
          cy: "6",
          r: "2",
          fill: "white"
        }),
        e: common_vendor.p({
          cx: "12",
          cy: "12",
          r: "2",
          fill: "white"
        }),
        f: common_vendor.p({
          cx: "12",
          cy: "18",
          r: "2",
          fill: "white"
        }),
        g: common_vendor.p({
          width: "24",
          height: "24",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        h: common_vendor.o(showMoreOptions),
        i: common_vendor.f(flashSaleData.images, (image, index, i0) => {
          return {
            a: image,
            b: index
          };
        }),
        j: common_vendor.t(flashSaleData.statusText),
        k: common_vendor.n(flashSaleData.statusClass),
        l: common_vendor.t(flashSaleData.name),
        m: common_vendor.t(flashSaleData.flashPrice),
        n: common_vendor.t(flashSaleData.originalPrice),
        o: common_vendor.t(flashSaleData.discountText),
        p: flashSaleData.status !== "ended"
      }, flashSaleData.status !== "ended" ? {
        q: common_vendor.t(countdownLabel.value),
        r: common_vendor.t(countdown.days),
        s: common_vendor.t(countdown.hours),
        t: common_vendor.t(countdown.minutes),
        v: common_vendor.t(countdown.seconds)
      } : {}, {
        w: common_vendor.p({
          d: "M23 4V10H17",
          stroke: "#FF7600",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        x: common_vendor.p({
          d: "M1 20V14H7",
          stroke: "#FF7600",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        y: common_vendor.p({
          d: "M3.51 9.00001C4.01717 7.56645 4.87913 6.2765 6.01547 5.27651C7.1518 4.27653 8.52547 3.60084 10 3.31677C11.4745 3.03271 13.0047 3.15505 14.4111 3.67167C15.8175 4.18829 17.0512 5.07723 17.99 6.24001L23 10M1 14L6.01 17.76C6.94879 18.9228 8.18246 19.8117 9.58886 20.3283C10.9953 20.845 12.5255 20.9673 14 20.6832C15.4745 20.3992 16.8482 19.7235 17.9845 18.7235C19.1209 17.7235 19.9828 16.4336 20.49 15",
          stroke: "#FF7600",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        z: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        A: common_vendor.o(refreshData),
        B: common_vendor.t(flashSaleData.viewCount),
        C: common_vendor.t(flashSaleData.stockSold),
        D: common_vendor.t(flashSaleData.stockRemain),
        E: common_vendor.t(flashSaleData.conversionRate),
        F: flashSaleData.progressPercent + "%",
        G: common_vendor.t(flashSaleData.progressPercent),
        H: common_vendor.t(flashSaleData.stockRemain),
        I: common_vendor.t(flashSaleData.timeRange),
        J: common_vendor.t(flashSaleData.stockTotal),
        K: common_vendor.t(flashSaleData.purchaseLimit > 0 ? flashSaleData.purchaseLimit + "件/人" : "不限购"),
        L: common_vendor.t(flashSaleData.rules),
        M: flashSaleData.description,
        N: common_vendor.f(flashSaleData.detailImages, (image, index, i0) => {
          return {
            a: index,
            b: image
          };
        }),
        O: common_vendor.o(viewAllOrders),
        P: flashSaleData.orders.length > 0
      }, flashSaleData.orders.length > 0 ? {
        Q: common_vendor.f(flashSaleData.orders, (order, index, i0) => {
          return {
            a: order.userAvatar,
            b: common_vendor.t(order.userName),
            c: common_vendor.t(order.quantity),
            d: common_vendor.t(order.time),
            e: index
          };
        })
      } : {
        R: common_assets._imports_0$33
      }, {
        S: common_vendor.p({
          d: "M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13",
          stroke: "#5E5CE6",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        T: common_vendor.p({
          d: "M18.5 2.50001C18.8978 2.10219 19.4374 1.87869 20 1.87869C20.5626 1.87869 21.1022 2.10219 21.5 2.50001C21.8978 2.89784 22.1213 3.4374 22.1213 4.00001C22.1213 4.56262 21.8978 5.10219 21.5 5.50001L12 15L8 16L9 12L18.5 2.50001Z",
          stroke: "#5E5CE6",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        U: common_vendor.p({
          width: "20",
          height: "20",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        V: common_vendor.o(editFlashSale),
        W: common_vendor.p({
          d: "M18 8C19.6569 8 21 6.65685 21 5C21 3.34315 19.6569 2 18 2C16.3431 2 15 3.34315 15 5C15 6.65685 16.3431 8 18 8Z",
          stroke: "#34C759",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        X: common_vendor.p({
          d: "M6 15C7.65685 15 9 13.6569 9 12C9 10.3431 7.65685 9 6 9C4.34315 9 3 10.3431 3 12C3 13.6569 4.34315 15 6 15Z",
          stroke: "#34C759",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        Y: common_vendor.p({
          d: "M18 22C19.6569 22 21 20.6569 21 19C21 17.3431 19.6569 16 18 16C16.3431 16 15 17.3431 15 19C15 20.6569 16.3431 22 18 22Z",
          stroke: "#34C759",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        Z: common_vendor.p({
          d: "M8.59 13.51L15.42 17.49",
          stroke: "#34C759",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        aa: common_vendor.p({
          d: "M15.41 6.51L8.59 10.49",
          stroke: "#34C759",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        ab: common_vendor.p({
          width: "20",
          height: "20",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        ac: common_vendor.o(shareFlashSale),
        ad: flashSaleData.status === "active"
      }, flashSaleData.status === "active" ? {
        ae: common_vendor.p({
          x: "6",
          y: "4",
          width: "4",
          height: "16",
          rx: "1",
          stroke: "#FF9500",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        af: common_vendor.p({
          x: "14",
          y: "4",
          width: "4",
          height: "16",
          rx: "1",
          stroke: "#FF9500",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        ag: common_vendor.p({
          width: "20",
          height: "20",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        })
      } : {
        ah: common_vendor.p({
          d: "M5 3L19 12L5 21V3Z",
          stroke: "#34C759",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        ai: common_vendor.p({
          width: "20",
          height: "20",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        })
      }, {
        aj: common_vendor.t(flashSaleData.status === "active" ? "暂停" : "启用"),
        ak: common_vendor.n(flashSaleData.status === "active" ? "pause" : "activate"),
        al: common_vendor.o(toggleStatus),
        am: common_vendor.p({
          d: "M3 6H5H21",
          stroke: "#FF3B30",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        an: common_vendor.p({
          d: "M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z",
          stroke: "#FF3B30",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        ao: common_vendor.p({
          width: "20",
          height: "20",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        ap: common_vendor.o(deleteFlashSale),
        aq: showOptions.value
      }, showOptions.value ? {
        ar: common_vendor.p({
          x: "3",
          y: "3",
          width: "7",
          height: "7",
          stroke: "#333333",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        as: common_vendor.p({
          x: "14",
          y: "3",
          width: "7",
          height: "7",
          stroke: "#333333",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        at: common_vendor.p({
          x: "3",
          y: "14",
          width: "7",
          height: "7",
          stroke: "#333333",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        av: common_vendor.p({
          x: "14",
          y: "14",
          width: "7",
          height: "7",
          stroke: "#333333",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        aw: common_vendor.p({
          width: "20",
          height: "20",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        ax: common_vendor.o(viewQrCode),
        ay: common_vendor.p({
          d: "M18 20V10",
          stroke: "#333333",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        az: common_vendor.p({
          d: "M12 20V4",
          stroke: "#333333",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        aA: common_vendor.p({
          d: "M6 20V14",
          stroke: "#333333",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        aB: common_vendor.p({
          width: "20",
          height: "20",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        aC: common_vendor.o(viewDataAnalysis),
        aD: common_vendor.p({
          d: "M10 13C10.4295 13.5741 10.9774 14.0492 11.6066 14.3929C12.2357 14.7367 12.9315 14.9411 13.6467 14.9923C14.3618 15.0435 15.0796 14.9404 15.7513 14.6898C16.4231 14.4392 17.0331 14.0471 17.54 13.54L20.54 10.54C21.4508 9.59699 21.9548 8.33397 21.9434 7.02299C21.932 5.71201 21.4061 4.45794 20.4791 3.5309C19.5521 2.60386 18.298 2.07802 16.987 2.06663C15.676 2.05523 14.413 2.55921 13.47 3.47L11.75 5.18",
          stroke: "#333333",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        aE: common_vendor.p({
          d: "M14 11C13.5705 10.4259 13.0226 9.95083 12.3934 9.60706C11.7642 9.2633 11.0685 9.05889 10.3533 9.00768C9.63816 8.95646 8.92037 9.05964 8.24861 9.31023C7.57685 9.56082 6.96684 9.95294 6.45996 10.46L3.45996 13.46C2.54917 14.403 2.04519 15.666 2.05659 16.977C2.06798 18.288 2.59382 19.5421 3.52086 20.4691C4.4479 21.3961 5.70197 21.922 7.01295 21.9334C8.32393 21.9448 9.58694 21.4408 10.53 20.53L12.24 18.82",
          stroke: "#333333",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        aF: common_vendor.p({
          width: "20",
          height: "20",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        aG: common_vendor.o(copyLink),
        aH: common_vendor.p({
          d: "M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15",
          stroke: "#333333",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        aI: common_vendor.p({
          d: "M7 10L12 15L17 10",
          stroke: "#333333",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        aJ: common_vendor.p({
          d: "M12 15V3",
          stroke: "#333333",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        aK: common_vendor.p({
          width: "20",
          height: "20",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        aL: common_vendor.o(exportData),
        aM: common_vendor.o(() => {
        }),
        aN: common_vendor.o(hideMoreOptions)
      } : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/flash/detail.js.map
