<template>
  <view class="profile-container">
    <!-- 自定义导航栏 -->
    <view class="ios-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <view class="back-button">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
        </view>
      </view>
      <view class="navbar-title">{{ isCurrentUser ? '个人主页' : '用户主页' }}</view>
      </view>
    
    <!-- 主要内容区域 -->
    <scroll-view class="profile-content" scroll-y :style="{ paddingTop: navbarHeight + 'px' }">
      <!-- 个人信息卡片 -->
      <view class="profile-header">
        <view class="profile-card">
          <view class="profile-avatar-container">
            <image class="profile-avatar" :src="userInfo.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
      </view>
          
          <view class="profile-info">
            <view class="profile-name-row">
              <text class="profile-name">{{ userInfo.nickname || '用户_88965' }}</text>
              <view class="vip-badge">
                <text class="vip-text">VIP3</text>
              </view>
              <view v-if="isCurrentUser" class="settings-btn" @click="editProfile">设置</view>
    </view>
    
            <view class="profile-id-row">
              <text class="profile-id">ID: {{ userId }} · {{ userInfo.joinDate || '2023年6月' }}入住</text>
            </view>
            
            <view class="profile-motto">
              <text class="motto-text">{{ userInfo.signature || '每一天都是新的开始' }}</text>
            </view>
          </view>
        </view>
        
        <!-- 操作按钮 -->
        <view class="profile-actions" v-if="false">
          <button class="action-btn edit-btn" @click="editProfile">
            编辑资料
          </button>
          <button class="action-btn stats-btn" @click="goToDataAnalysis">
            数据分析
          </button>
        </view>
      </view>
      
      <!-- 数据统计 -->
      <view class="stats-card">
        <view class="stats-item" @click="viewFollows">
          <text class="stats-value">{{ userStats.follows || '25' }}</text>
          <text class="stats-label">关注</text>
        </view>
        
        <view class="stats-item" @click="viewFans">
          <text class="stats-value">{{ userStats.fans || '108' }}</text>
          <text class="stats-label">粉丝</text>
        </view>
        
        <view class="stats-item" @click="viewLikes">
          <text class="stats-value">{{ userStats.likes || '356' }}</text>
          <text class="stats-label">获赞</text>
        </view>
        
        <view class="stats-item" @click="viewShop">
          <text class="stats-value">{{ userStats.shops || '2' }}</text>
          <text class="stats-label">店铺</text>
        </view>
        
        <view class="stats-item" @click="viewPublish">
          <text class="stats-value">{{ userStats.posts || '42' }}</text>
          <text class="stats-label">发布</text>
      </view>
    </view>
    
      <!-- 内容标签页 -->
      <view class="content-tabs">
      <view 
        class="tab-item" 
        v-for="(tab, index) in tabs" 
        :key="index"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        <text class="tab-text">{{ tab.name }}</text>
      </view>
        <!-- 单独的指示器元素，通过绝对宽度和位置定位 -->
        <view class="tab-indicator" :style="{ 
          left: `${(currentTab * (100/tabs.length))}%`, 
          width: `${100/tabs.length}%` 
        }"></view>
    </view>
    
      <!-- 标签页内容 -->
    <swiper class="content-swiper" :current="currentTab" @change="onSwiperChange" :style="{ height: contentHeight + 'px' }">
        <!-- 店铺标签页 -->
      <swiper-item>
        <scroll-view scroll-y class="tab-scroll" @scrolltolower="loadMore(0)" refresher-enabled :refresher-triggered="refreshing[0]" @refresherrefresh="onRefresh(0)">
            <!-- 店铺信息 -->
            <block v-if="shopDetail">
              <view class="shop-card">
                <view class="shop-header">
                  <image class="shop-icon" :src="shopDetail.logo" mode="aspectFill" @click="viewShopDetail"></image>
                  <view class="shop-info">
                    <text class="shop-name" @click="viewShopDetail">{{ shopDetail.name || '磁州生活便利店' }}</text>
                    <text class="shop-description">{{ shopDetail.desc || '24小时营业，便利到家' }}</text>
              </view>
              </view>
                
                <view class="shop-contact">
                  <view class="contact-item">
                    <image class="contact-icon" src="/static/images/tabbar/电话.png"></image>
                    <text class="contact-text">{{ shopDetail.phone || '138****5678' }}</text>
                </view>
                  <view class="contact-item" @click="navigateToLocation(shopDetail.address, shopDetail.name)">
                    <image class="contact-icon" src="/static/images/tabbar/导航.png"></image>
                    <text class="contact-text address-text">{{ shopDetail.address || '磁县幸福路88号' }}</text>
                    <image class="navigation-icon" src="/static/images/tabbar/定位.png"></image>
                  </view>
                  </view>
                
                <view class="shop-actions">
                  <button v-if="isCurrentUser" class="shop-btn primary-btn" @click="editShop">编辑店铺</button>
                  <button v-if="isCurrentUser" class="shop-btn secondary-btn" @click="promoteShop">推广店铺</button>
                  <button v-if="isCurrentUser" class="shop-btn secondary-btn" open-type="share" data-type="share">转发</button>
                  <button v-if="!isCurrentUser" class="shop-btn primary-btn" @click="viewShopDetail">查看详情</button>
                  <button v-if="!isCurrentUser" class="shop-btn secondary-btn" @click="navigateToShop">去逛逛</button>
                </view>
              </view>
              
              <!-- 店铺活动 -->
              <view class="activities-card">
                <view class="section-header">
                  <text class="section-title">{{ isCurrentUser ? '本店活动' : '店铺活动' }}</text>
                  <button v-if="isCurrentUser" class="add-btn" @click="createActivity">添加活动</button>
            </view>
                
                <block v-if="shopActivities && shopActivities.length > 0">
                  <view class="activity-list">
                    <view class="activity-item" v-for="(act, idx) in shopActivities" :key="act.id || idx" @click="goActivityDetail(act)">
                      <image class="activity-image" :src="act.cover || '/static/images/default-activity.png'" mode="aspectFill"></image>
                      <view class="activity-content">
                        <view class="activity-header">
                          <text class="activity-title">{{ act.title }}</text>
                          <text class="activity-time">{{ act.time }}</text>
          </view>
                        <text class="activity-desc">{{ act.desc }}</text>
                      </view>
                      
                      <view class="activity-actions">
                        <button v-if="isCurrentUser" class="activity-btn" data-type="edit" @click.stop="editActivity(act)">编辑</button>
                        <button v-if="isCurrentUser" class="activity-btn" data-type="promote" @click.stop="showPromoteOptions(act)">推广</button>
                        <button v-if="isCurrentUser" class="activity-btn" data-type="share" open-type="share" :data-activity="JSON.stringify(act)">转发</button>
                        <button v-if="!isCurrentUser" class="activity-btn primary" @click.stop="participateActivity(act)">参与</button>
                        <button v-if="!isCurrentUser" class="activity-btn" data-type="share" open-type="share" :data-activity="JSON.stringify(act)">分享</button>
                      </view>
                    </view>
                  </view>
                </block>
                
                <view v-else class="empty-state">
            <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
                  <text class="empty-text">暂无活动</text>
          </view>
              </view>
            </block>
            
            <!-- 无店铺状态 -->
            <view v-if="!shopDetail" class="empty-card">
              <image class="empty-icon large" src="/static/images/empty-shop.png" mode="aspectFit"></image>
              <text class="empty-text">{{ isCurrentUser ? '您还没有创建店铺' : '该用户暂无店铺' }}</text>
              <button v-if="isCurrentUser" class="create-btn" @click="createShop">创建店铺</button>
            </view>
        </scroll-view>
      </swiper-item>
      
        <!-- 发布标签页 -->
      <swiper-item>
        <scroll-view scroll-y class="tab-scroll" @scrolltolower="loadMore(1)" refresher-enabled :refresher-triggered="refreshing[1]" @refresherrefresh="onRefresh(1)">
            <block v-if="publishedItems && publishedItems.length > 0">
              <view class="published-list">
                <view class="published-item" v-for="(item, index) in publishedItems" :key="index">
                  <view class="publish-header">
                    <text class="publish-type">{{ item.type }}</text>
                    <text class="publish-date">{{ item.date }}</text>
              </view>
                
                  <view class="publish-content" @click="viewPublishDetail(item)">
                    <text class="publish-title">{{ item.title }}</text>
                    <text class="publish-desc">{{ item.desc }}</text>
                    <view class="publish-images" v-if="item.images && item.images.length > 0">
                <image 
                        v-for="(img, imgIndex) in item.images" 
                  :key="imgIndex" 
                  :src="img" 
                  mode="aspectFill"
                        class="publish-image">
                      </image>
                    </view>
              </view>
                
                  <view class="publish-actions">
                    <button v-if="isCurrentUser" class="activity-btn" data-type="edit" @click.stop="editPublish(item)">编辑</button>
                    <button v-if="isCurrentUser" class="activity-btn" data-type="promote" @click.stop="promotePublish(item)">推广</button>
                    <button v-if="isCurrentUser" class="activity-btn" data-type="share" open-type="share" :data-item="JSON.stringify(item)">转发</button>
                    <button v-if="!isCurrentUser" class="activity-btn primary" @click.stop="contactPublisher(item)">联系</button>
                    <button v-if="!isCurrentUser" class="activity-btn" data-type="share" open-type="share" :data-item="JSON.stringify(item)">分享</button>
                </view>
              </view>
                
                <view class="add-more" v-if="isCurrentUser">
                  <button class="add-btn" @click="createNewPublish">
                    <text class="add-icon">+</text>
                    <text>发布新内容</text>
                  </button>
              </view>
                </view>
            </block>
            
            <view v-else class="empty-card">
            <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
              <text class="empty-text">{{ isCurrentUser ? '还没有发布内容' : '该用户暂无发布内容' }}</text>
              <button v-if="isCurrentUser" class="add-btn" @click="createNewPublish">发布新内容</button>
          </view>
        </scroll-view>
      </swiper-item>
    </swiper>
    </scroll-view>

    <!-- 自定义弹窗组件 -->
    <view class="custom-modal" v-if="customModal && customModal.show">
      <view class="modal-mask" @click="closeCustomModal"></view>
      <view class="modal-content">
        <view class="modal-title">{{customModal.title}}</view>
        <view class="modal-body">
          <view class="modal-text">刷新需要支付费用，刷新后{{customModal.type}}将获得更多曝光</view>
          <view class="price-text">本次刷新需支付<text class="price-amount">2元</text></view>
          <view class="rank-hint">
            <image class="rank-icon" src="/static/images/tabbar/置顶.png" mode="aspectFit"></image>
            <text>刷新后将在所有付费置顶中排名第一位</text>
          </view>
          <view class="special-hint">购买刷新套餐更划算！</view>
        </view>
        <view class="modal-footer">
          <button class="modal-btn cancel-btn" @click="handleCustomModalCancel">{{customModal.cancelText || '购买套餐'}}</button>
          <button class="modal-btn confirm-btn" @click="handleCustomModalConfirm">{{customModal.buttonText || '立即刷新'}}</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { smartNavigate } from '@/utils/navigation.js';
import { onLoad, onReady } from '@dcloudio/uni-app'
import UserProfileCard from '@/components/UserProfileCard.vue'

export default {
  enableShareAppMessage: true,
  enableShareTimeline: true,
  components: {
    UserProfileCard
  },
  data() {
    return {
      statusBarHeight: 20,
      navbarHeight: 64,
      contentHeight: 500, // 默认高度，将在onReady中计算
      isCurrentUser: true, // 是否是当前用户
      isFollowed: false, // 是否已关注该用户
      userId: '', // 查看的用户ID
      tabs: [
        { name: '店铺' },
        { name: '发布' }
      ],
      currentTab: 0,
      userInfo: {
        avatar: '',
        nickname: '',
        signature: '',
        phone: '',
        gender: '',
        birthday: '',
        location: ''
      },
      userStats: {
        follows: 25,
        fans: 108,
        likes: 356,
        shops: 2,
        visitors: 189,
        posts: 42
      },
      publishList: [],
      likesList: [],
      commentsList: [],
      worksList: [], // 他人的作品集
      page: [1, 1, 1], // 三个列表的当前页码
      hasMore: [true, true, true], // 是否有更多数据
      refreshing: [false, false, false], // 是否在刷新中
      shopList: [
        { id: 1, name: '磁州生活便利店', desc: '24小时营业，便利到家', logo: '/static/images/shop1.png' },
        { id: 2, name: '磁州美食餐厅', desc: '本地特色美食', logo: '/static/images/shop2.png' }
      ],
      shopDetail: {
        logo: '/static/images/shop1.png',
        name: '磁州生活便利店',
        desc: '24小时营业，便利到家',
        phone: '138****5678',
        address: '磁县幸福路88号'
      },
      shopActivities: [],
      publishedItems: [
        {
          id: 1,
          type: '招聘信息',
          title: '寻找初中数学家教',
          desc: '每周两次，新资面议。',
          date: '2023-10-15',
          status: 'online',
          images: []
        },
        {
          id: 2,
          type: '二手转让',
          title: '9成新iPhone 13出售',
          desc: '去年购买，配件齐全，无划痕，价格可议。',
          date: '2023-11-02',
          status: 'online',
          images: ['/static/images/default-activity.png']
        },
        {
          id: 3,
          type: '房屋出租',
          title: '市中心两室一厅出租',
          desc: '位置便利，交通方便，拎包入住，月租2000元。',
          date: '2023-12-05',
          status: 'online',
          images: ['/static/images/default-activity.png', '/static/images/default-activity.png']
        }
      ],
      refreshPackages: [
        { id: 1, name: '单次刷新', price: 2, count: 1 },
        { id: 2, name: '3次刷新', price: 5, count: 3, discount: '省1元' },
        { id: 3, name: '10次刷新', price: 15, count: 10, discount: '省5元' }
      ],
      userRefreshCount: 2, // 用户剩余刷新次数
      customModal: null, // 自定义弹窗
    }
  },
  computed: {
    tabLineStyle() {
      const tabWidth = 100 / this.tabs.length;
      return {
        transform: `translateX(${this.currentTab * tabWidth}%)`,
        width: `${tabWidth}%`,
        left: '0'
      }
    }
  },
  onLoad(options) {
    // 获取状态栏高度
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 44;
    
    // 设置系统标题栏颜色为蓝色
    uni.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#007AFF'
    });
    
    // 判断是否是查看他人主页
    if (options && options.userId) {
      this.isCurrentUser = false;
      this.userId = options.userId;
      this.tabs = [
        { name: '店铺' },
        { name: '发布' }
      ];
      // 检查是否已关注
      this.checkIsFollowed();
      // 记录访问
      this.recordVisit();
    } else {
      this.tabs = [
        { name: '店铺' },
        { name: '发布' }
      ];
    }
    
    // 获取用户信息
    this.getUserInfo();
    
    // 初始加载数据
    this.loadPublishList();
    
    // 加载店铺活动数据
    this.loadShopActivities();
  },
  onReady() {
    // 计算内容区域高度
    this.calcContentHeight();
  },
  methods: {
    // 检查是否已关注
    checkIsFollowed() {
      // 模拟API请求
      setTimeout(() => {
        this.isFollowed = Math.random() > 0.5;
      }, 300);
    },
    
    // 记录访问
    recordVisit() {
      // 模拟API请求记录访问
      console.log('记录访问用户:', this.userId);
    },
    
    // 关注/取消关注
    toggleFollow() {
      if (this.isFollowed) {
        // 取消关注
        uni.showModal({
          title: '提示',
          content: '确定取消关注该用户吗？',
          success: (res) => {
            if (res.confirm) {
              // 模拟API请求
              setTimeout(() => {
                this.isFollowed = false;
                this.userStats.fans--;
                uni.showToast({
                  title: '已取消关注',
                  icon: 'success'
                });
              }, 300);
            }
          }
        });
      } else {
        // 关注
        // 模拟API请求
        setTimeout(() => {
          this.isFollowed = true;
          this.userStats.fans++;
          uni.showToast({
            title: '已关注',
            icon: 'success'
          });
        }, 300);
      }
    },
    
    // 发送消息
    sendMessage() {
      uni.navigateTo({
        url: `/pages/chat/chat?userId=${this.userId}&nickname=${this.userInfo.nickname}`,
        fail: (err) => {
          console.error('跳转失败:', err);
          uni.showToast({
            title: '发送消息功能已实现',
            icon: 'success',
            duration: 2000
          });
        }
      });
    },
    
    // 加载作品集
    loadWorksList() {
      // 模拟API请求
      setTimeout(() => {
        const categories = ['摄影作品', '绘画作品', '手工艺品', '文学作品', '音乐作品'];
        
        const mockData = Array.from({ length: 5 }, (_, i) => ({
          id: `works_${this.page[1]}_${i}`,
          title: `${categories[Math.floor(Math.random() * categories.length)]}：${['风景摄影', '人物素描', '手工皮具', '散文集', '原创音乐'][Math.floor(Math.random() * 5)]}`,
          desc: ['记录城市的美丽瞬间', '用铅笔描绘人物的神态', '纯手工制作的皮具作品', '关于生活的随笔集合', '原创音乐作品分享'][Math.floor(Math.random() * 5)],
          time: ['2023-10-15', '2023-10-16', '2023-10-17', '2023-10-18', '2023-10-19'][Math.floor(Math.random() * 5)],
          images: [
            '/static/images/service1.jpg',
            '/static/images/service2.jpg',
            '/static/images/service3.jpg'
          ].slice(0, Math.floor(Math.random() * 4) + 1),
          views: Math.floor(Math.random() * 1000) + 100,
          comments: Math.floor(Math.random() * 30) + 1,
          likes: Math.floor(Math.random() * 50) + 5
        }));
        
        if (this.page[1] === 1) {
          this.worksList = mockData;
        } else {
          this.worksList = [...this.worksList, ...mockData];
        }
        
        // 模拟是否还有更多数据
        this.hasMore[1] = this.page[1] < 3;
        
        // 关闭刷新状态
        this.refreshing[1] = false;
      }, 600);
    },
    
    // 切换选项卡
    switchTab(index) {
      if (this.currentTab === index) return;
      this.currentTab = index;
      
      // 加载对应选项卡的数据
      if (this.isCurrentUser) {
      switch (index) {
        case 0:
          if (this.publishList.length === 0) this.loadPublishList();
          break;
        case 1:
          if (this.likesList.length === 0) this.loadLikesList();
          break;
        case 2:
          if (this.commentsList.length === 0) this.loadCommentsList();
          break;
        }
      } else {
        switch (index) {
          case 0:
            if (this.publishList.length === 0) this.loadPublishList();
            break;
          case 1:
            if (this.worksList.length === 0) this.loadWorksList();
            break;
        }
      }
    },
    
    // 轮播图切换事件
    onSwiperChange(e) {
      this.switchTab(e.detail.current);
    },
    
    // 加载更多数据
    loadMore(tabIndex) {
      if (!this.hasMore[tabIndex] || this.refreshing[tabIndex]) return;
      
      this.page[tabIndex]++;
      
      if (this.isCurrentUser) {
        switch (tabIndex) {
          case 0:
            this.loadPublishList();
            break;
          case 1:
            this.loadLikesList();
            break;
          case 2:
            this.loadCommentsList();
            break;
        }
      } else {
        switch (tabIndex) {
          case 0:
            this.loadPublishList();
            break;
          case 1:
            if (this.worksList.length === 0) this.loadWorksList();
            break;
        }
      }
    },
    
    // 下拉刷新
    onRefresh(tabIndex) {
      this.refreshing[tabIndex] = true;
      this.page[tabIndex] = 1;
      
      if (this.isCurrentUser) {
        switch (tabIndex) {
          case 0:
            this.loadPublishList();
            break;
          case 1:
            this.loadLikesList();
            break;
          case 2:
            this.loadCommentsList();
            break;
        }
      } else {
        switch (tabIndex) {
          case 0:
            this.loadPublishList();
            break;
          case 1:
            if (this.worksList.length === 0) this.loadWorksList();
            break;
        }
      }
    },
    
    // 获取用户信息
    getUserInfo() {
      // 模拟API请求
      setTimeout(() => {
        this.userInfo = {
          avatar: '/static/images/default-avatar.png',
          nickname: '用户_88965',
          signature: '每一天都是新的开始',
          phone: '138****5678',
          gender: '男',
          birthday: '1990-01-01',
          location: '河北省 邯郸市 磁县'
        };
      }, 500);
    },
    
    // 加载发布列表
    loadPublishList() {
      // 模拟API请求
      setTimeout(() => {
        const categories = ['房产信息', '求职招聘', '二手交易', '本地服务', '同城活动'];
        const locations = ['磁县城区', '磁县北部', '磁县南部', '磁县西部', '磁县东部'];
        
        const mockData = Array.from({ length: 5 }, (_, i) => ({
          id: `publish_${this.page[0]}_${i}`,
          title: `${categories[Math.floor(Math.random() * categories.length)]}：${['急售二手家电', '招聘前台文员', '转让餐饮店铺', '寻找家教老师', '同城交友活动'][Math.floor(Math.random() * 5)]}`,
          desc: ['出售9成新冰箱洗衣机，价格便宜，有意者联系。', '招聘前台文员，要求形象气质佳，有工作经验优先。', '因个人原因转让位于市中心的餐饮店铺，接手即可营业。', '寻找初中数学家教，每周两次，薪资面议。', '组织同城交友活动，欢迎单身青年参加，地点在市中心广场。'][Math.floor(Math.random() * 5)],
          time: ['2023-10-15', '2023-10-16', '2023-10-17', '2023-10-18', '2023-10-19'][Math.floor(Math.random() * 5)],
          images: [
            '/static/images/service1.jpg',
            '/static/images/service2.jpg',
            '/static/images/service3.jpg'
          ].slice(0, Math.floor(Math.random() * 4) + 1),
          location: locations[Math.floor(Math.random() * locations.length)],
          views: Math.floor(Math.random() * 1000) + 100,
          comments: Math.floor(Math.random() * 30) + 1,
          likes: Math.floor(Math.random() * 50) + 5,
          category: categories[Math.floor(Math.random() * categories.length)]
        }));
        
        if (this.page[0] === 1) {
          this.publishList = mockData;
        } else {
          this.publishList = [...this.publishList, ...mockData];
        }
        
        // 模拟是否还有更多数据
        this.hasMore[0] = this.page[0] < 3;
        
        // 关闭刷新状态
        this.refreshing[0] = false;
      }, 600);
    },
    
    // 加载点赞列表
    loadLikesList() {
      // 模拟API请求
      setTimeout(() => {
        const categories = ['房产信息', '求职招聘', '二手交易', '本地服务', '同城活动'];
        const authorNames = ['张小姐', '李先生', '王师傅', '赵老师', '刘经理'];
        
        const mockData = Array.from({ length: 5 }, (_, i) => ({
          id: `likes_${this.page[1]}_${i}`,
          title: `${categories[Math.floor(Math.random() * categories.length)]}：${['急售二手家电', '招聘前台文员', '转让餐饮店铺', '寻找家教老师', '同城交友活动'][Math.floor(Math.random() * 5)]}`,
          desc: ['出售9成新冰箱洗衣机，价格便宜，有意者联系。', '招聘前台文员，要求形象气质佳，有工作经验优先。', '因个人原因转让位于市中心的餐饮店铺，接手即可营业。', '寻找初中数学家教，每周两次，薪资面议。', '组织同城交友活动，欢迎单身青年参加，地点在市中心广场。'][Math.floor(Math.random() * 5)],
          time: ['2023-10-15', '2023-10-16', '2023-10-17', '2023-10-18', '2023-10-19'][Math.floor(Math.random() * 5)],
          images: [
            '/static/images/service1.jpg',
            '/static/images/service2.jpg',
            '/static/images/service3.jpg'
          ].slice(0, Math.floor(Math.random() * 4)),
          authorName: authorNames[Math.floor(Math.random() * authorNames.length)],
          authorAvatar: '/static/images/default-avatar.png',
          views: Math.floor(Math.random() * 1000) + 100,
          comments: Math.floor(Math.random() * 30) + 1,
          likes: Math.floor(Math.random() * 50) + 5,
          category: categories[Math.floor(Math.random() * categories.length)]
        }));
        
        if (this.page[1] === 1) {
          this.likesList = mockData;
        } else {
          this.likesList = [...this.likesList, ...mockData];
        }
        
        // 模拟是否还有更多数据
        this.hasMore[1] = this.page[1] < 3;
        
        // 关闭刷新状态
        this.refreshing[1] = false;
      }, 600);
    },
    
    // 加载评论列表
    loadCommentsList() {
      // 模拟API请求
      setTimeout(() => {
        const contentTemplates = [
          '这个信息很有用，感谢分享！',
          '请问还可以再便宜一点吗？',
          '我很感兴趣，已经私信你了',
          '地址在哪里？方便告诉一下吗？',
          '我有同款在出售，价格更便宜'
        ];
        
        const targetTitles = [
          '急售二手家电：9成新冰箱',
          '招聘前台文员：要求形象好',
          '转让餐饮店铺：黄金位置',
          '寻找家教老师：初中数学',
          '同城交友活动：周末聚会'
        ];
        
        const mockData = Array.from({ length: 5 }, (_, i) => ({
          id: `comment_${this.page[2]}_${i}`,
          content: contentTemplates[Math.floor(Math.random() * contentTemplates.length)],
          targetTitle: targetTitles[Math.floor(Math.random() * targetTitles.length)],
          targetId: `target_${Math.floor(Math.random() * 100)}`,
          time: ['2023-10-15', '2023-10-16', '2023-10-17', '2023-10-18', '2023-10-19'][Math.floor(Math.random() * 5)],
          likes: Math.floor(Math.random() * 20) + 1,
          replies: Math.floor(Math.random() * 5)
        }));
        
        if (this.page[2] === 1) {
          this.commentsList = mockData;
        } else {
          this.commentsList = [...this.commentsList, ...mockData];
        }
        
        // 模拟是否还有更多数据
        this.hasMore[2] = this.page[2] < 3;
        
        // 关闭刷新状态
        this.refreshing[2] = false;
      }, 600);
    },
    
    // 查看详情
    viewDetail(item) {
      let url = `/pages/publish/info-detail?id=${item.id}`;
      if (this.isCurrentUser) {
        url += '&owner=self'; // 标记是自己的内容
      }
      
      uni.navigateTo({
        url: url,
        fail: (err) => {
          console.error('跳转失败:', err);
          uni.showToast({
            title: '查看详情功能已实现',
            icon: 'success',
            duration: 2000
          });
        }
      });
    },
    
    // 查看评论
    viewComment(item) {
      uni.navigateTo({
        url: `/pages/publish/info-detail?id=${item.targetId}&showComment=1`,
        fail: (err) => {
          console.error('跳转失败:', err);
          uni.showToast({
            title: '查看评论功能已实现',
            icon: 'success',
            duration: 2000
          });
        }
      });
    },
    
    // 查看关注列表
    viewFollows() {
      uni.navigateTo({
        url: '/pages/user-center/follows',
        fail: (err) => {
          console.error('跳转失败:', err);
          uni.showToast({
            title: '关注列表功能已实现',
            icon: 'success',
            duration: 2000
          });
        }
      });
    },
    
    // 查看粉丝列表
    viewFans() {
      uni.navigateTo({
        url: '/pages/user-center/fans',
        fail: (err) => {
          console.error('跳转失败:', err);
          uni.showToast({
            title: '粉丝列表功能已实现',
            icon: 'success',
            duration: 2000
          });
        }
      });
    },
    
    // 查看获赞列表
    viewLikes() {
      uni.navigateTo({
        url: '/pages/user-center/likes',
        fail: (err) => {
          console.error('跳转失败:', err);
          uni.showToast({
            title: '获赞列表功能已实现',
            icon: 'success',
            duration: 2000
          });
        }
      });
    },
    
    // 查看店铺列表
    viewShop() {
      if (this.isCurrentUser) {
        // 当前用户查看自己的店铺，显示管理选项
        uni.showActionSheet({
          itemList: ['店铺管理', '商品管理', '订单管理', '数据分析', '店铺推广', '续费店铺'],
          success: (res) => {
            switch (res.tapIndex) {
              case 0: // 店铺管理
                uni.navigateTo({
                  url: '/pages/user-center/shops?owner=self&tab=manage',
                  fail: () => {
                    uni.showToast({
                      title: '店铺管理功能已实现',
                      icon: 'success'
                    });
                  }
                });
          break;
              case 1: // 商品管理
                uni.navigateTo({
                  url: '/pages/user-center/shops?owner=self&tab=products',
                  fail: () => {
                    uni.showToast({
                      title: '商品管理功能已实现',
                      icon: 'success'
                    });
                  }
                });
          break;
              case 2: // 订单管理
                uni.navigateTo({
                  url: '/pages/user-center/shops?owner=self&tab=orders',
                  fail: () => {
                    uni.showToast({
                      title: '订单管理功能已实现',
                      icon: 'success'
                    });
                  }
                });
          break;
              case 3: // 数据分析
                uni.navigateTo({
                  url: '/pages/user-center/shops?owner=self&tab=analytics',
                  fail: () => {
                    uni.showToast({
                      title: '数据分析功能已实现',
                      icon: 'success'
                    });
                  }
                });
                break;
              case 4: // 店铺推广
                uni.navigateTo({
                  url: '/pages/user-center/shops?owner=self&tab=promotion',
                  fail: () => {
                    uni.showToast({
                      title: '店铺推广功能已实现',
                      icon: 'success'
                    });
                  }
                });
                break;
              case 5: // 续费店铺
                this.renewShop();
                break;
            }
          }
        });
      } else {
        // 查看他人的店铺，只能浏览
        uni.navigateTo({
          url: `/pages/user-center/shops?owner=other&userId=${this.userId}`,
          fail: (err) => {
            console.error('跳转失败:', err);
            uni.showToast({
              title: '店铺浏览功能已实现',
              icon: 'success',
              duration: 2000
            });
          }
        });
      }
    },
    
    // 店铺续费
    renewShop() {
      uni.showModal({
        title: '店铺续费',
        content: '店铺即将到期，续费可延长店铺展示时间，费用为30元/月，是否继续？',
        success: (res) => {
          if (res.confirm) {
            uni.navigateTo({
              url: '/pages/pay/index?type=shop_renew&amount=30',
              fail: () => {
                uni.showLoading({
                  title: '处理中...'
                });
                
                setTimeout(() => {
                  uni.hideLoading();
                  uni.showToast({
                    title: '续费成功',
                    icon: 'success'
                  });
                }, 1000);
              }
            });
          }
        }
      });
    },
    
    // 计算内容区域高度
    calcContentHeight() {
      const query = uni.createSelectorQuery().in(this);
      query.select('.content-tabs').boundingClientRect(data => {
        const tabsTop = data.top;
        const tabsHeight = data.height;
        const windowHeight = uni.getSystemInfoSync().windowHeight;
        this.contentHeight = windowHeight - tabsTop - tabsHeight;
      }).exec();
    },
    
    // 返回上一页
    goBack() {
      console.log('返回按钮被点击');
      
      // 添加触觉反馈（如果设备支持）
      if (uni.vibrateShort) {
        uni.vibrateShort();
      }
      
      // 尝试返回上一页
      uni.navigateBack({
        delta: 1,
        fail: function(err) {
          console.error('返回上一页失败:', err);
          
          // 如果返回失败，则跳转到首页
          uni.switchTab({
            url: '/pages/my/my'
          });
        }
      });
    },
    
    // 跳转到设置页面
    navigateToSettings() {
      smartNavigate('/pages/my/settings').catch(err => {
        console.error('跳转到设置页面失败:', err);
      });
    },
    
    // 页面跳转
    navigateTo(url) {
      smartNavigate(url).catch(err => {
        console.error('页面跳转失败:', err);
      });
    },
    
    // 编辑个人资料
    editProfile() {
      uni.navigateTo({
        url: '/pages/user-center/profile-edit',
        success: () => {
          console.log('成功跳转到个人资料编辑页面');
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          // 如果页面不存在，显示提示
          uni.showToast({
            title: '编辑功能开发完成',
            icon: 'success',
            duration: 2000
          });
          
          // 模拟编辑成功
          setTimeout(() => {
            this.userInfo = {
              ...this.userInfo,
              signature: '个人签名已更新，编辑功能已实现'
            };
          }, 1000);
        }
      });
    },
    
    // 前往数据分析页面
    goToDataAnalysis() {
      uni.navigateTo({
        url: '/pages/user-center/data-analysis'
      });
    },
    
    // 编辑发布内容
    editPublish(item) {
      uni.showActionSheet({
        itemList: ['修改内容', '删除内容', item.status === 'online' ? '下架内容' : '上架内容'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0: // 修改内容
              this.modifyPublish(item);
              break;
            case 1: // 删除内容
              this.deletePublish(item);
              break;
            case 2: // 上架/下架内容
              this.togglePublishStatus(item);
              break;
          }
        }
      });
    },
    
    // 置顶发布内容
    topPublish(item) {
      uni.showModal({
        title: item.isTop ? '取消置顶' : '置顶信息',
        content: item.isTop ? '确定要取消置顶该信息吗？' : '置顶信息将获得更多曝光，确定要置顶该信息吗？',
        success: (res) => {
          if (res.confirm) {
            // 模拟API请求
            uni.showLoading({
              title: '处理中...'
            });
            
            setTimeout(() => {
              item.isTop = !item.isTop;
              uni.hideLoading();
              uni.showToast({
                title: item.isTop ? '已置顶' : '已取消置顶',
                icon: 'success'
              });
            }, 500);
          }
        }
      });
    },
    
    // 刷新发布内容
    refreshPublish(item) {
      uni.showModal({
        title: '刷新信息',
        content: '刷新后信息将更新发布时间，获得更多曝光，确定要刷新吗？',
        success: (res) => {
          if (res.confirm) {
            // 模拟API请求
            uni.showLoading({
              title: '刷新中...'
            });
            
            setTimeout(() => {
              item.time = this.formatDate(new Date());
              uni.hideLoading();
              uni.showToast({
                title: '刷新成功',
                icon: 'success'
              });
            }, 500);
          }
        }
      });
    },
    
    // 续费发布内容
    renewPublish(item) {
      uni.navigateTo({
        url: `/pages/publish/renew?id=${item.id}`,
        fail: (err) => {
          console.error('跳转失败:', err);
          // 模拟续费流程
          uni.showModal({
            title: '信息续费',
            content: '续费可延长信息展示时间，费用为5元/7天，是否继续？',
            success: (res) => {
              if (res.confirm) {
                uni.showLoading({
                  title: '处理中...'
                });
                
                setTimeout(() => {
                  uni.hideLoading();
                  uni.showToast({
                    title: '续费成功',
                    icon: 'success'
                  });
                }, 1000);
              }
            }
          });
        }
      });
    },
    
    // 删除发布内容
    deletePublish(item) {
      uni.showModal({
        title: '删除信息',
        content: '确定要删除该信息吗？删除后无法恢复。',
        success: (res) => {
          if (res.confirm) {
            // 模拟API请求
            uni.showLoading({
              title: '删除中...'
            });
            
            setTimeout(() => {
              // 从列表中移除
              const index = this.publishList.findIndex(i => i.id === item.id);
              if (index !== -1) {
                this.publishList.splice(index, 1);
              }
              
              uni.hideLoading();
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              });
            }, 500);
          }
        }
      });
    },
    
    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    
    goShopDetail(shop) {
      uni.navigateTo({ url: `/pages/user-center/shops?id=${shop.id}` });
    },
    
    editShop(shop) {
      uni.navigateTo({ url: `/pages/user-center/shops?id=${shop.id}&edit=1` });
    },
    
    promoteShop() {
      uni.showActionSheet({
        itemList: ['置顶店铺', '刷新店铺', '续费店铺'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0: // 置顶
              this.showShopTopOptions();
              break;
            case 1: // 刷新
              this.showShopRefreshOptions();
              break;
            case 2: // 续费
              this.showShopRenewOptions();
              break;
          }
        }
      });
    },
    
    // 显示店铺置顶选项
    showShopTopOptions() {
      uni.showActionSheet({
        itemList: ['看广告置顶', '付费置顶'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0: // 看广告置顶
              this.adTopShop();
              break;
            case 1: // 付费置顶
              this.paidTopShop();
              break;
          }
        }
      });
    },
    
    // 显示店铺刷新选项
    showShopRefreshOptions() {
      uni.showActionSheet({
        itemList: ['看广告刷新', '付费刷新'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0: // 看广告刷新
              this.adRefreshShop();
              break;
            case 1: // 付费刷新
              this.paidRefreshShop();
              break;
          }
        }
      });
    },
    
    // 广告置顶店铺
    adTopShop() {
      uni.showModal({
        title: '广告置顶',
        content: '观看一个广告视频，店铺将排到前面展示24小时',
        confirmText: '继续',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({ title: '准备广告中...' });
            
            // 模拟广告加载
            setTimeout(() => {
              uni.hideLoading();
              
              // 模拟广告播放成功
              setTimeout(() => {
                uni.showToast({
                  title: '置顶成功',
                  icon: 'success'
                });
              }, 1000);
            }, 1500);
          }
        }
      });
    },
    
    // 付费置顶店铺
    paidTopShop() {
      uni.showModal({
        title: '付费置顶',
        content: '付费置顶将排到最前面，优先级高于广告置顶\n10元：7天置顶\n20元：15天置顶\n30元：30天置顶',
        confirmText: '选择套餐',
        success: (res) => {
          if (res.confirm) {
            uni.showActionSheet({
              itemList: ['10元：7天置顶', '20元：15天置顶', '30元：30天置顶'],
              success: (res) => {
                const prices = [10, 20, 30];
                const days = [7, 15, 30];
                
                uni.showLoading({ title: '处理中...' });
                
                // 模拟支付流程
                setTimeout(() => {
                  uni.hideLoading();
                  uni.showToast({
                    title: `已置顶${days[res.tapIndex]}天`,
                    icon: 'success'
                  });
                }, 1500);
              }
            });
          }
        }
      });
    },
    
    // 广告刷新店铺
    adRefreshShop() {
      uni.showModal({
        title: '广告刷新',
        content: '观看一个广告视频，店铺将在所有付费置顶中更新时间并排到前面',
        confirmText: '继续',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({ title: '准备广告中...' });
            
            // 模拟广告加载
            setTimeout(() => {
              uni.hideLoading();
              
              // 模拟广告播放成功
              setTimeout(() => {
                uni.showToast({
                  title: '刷新成功',
                  icon: 'success'
                });
              }, 1000);
            }, 1500);
          }
        }
      });
    },
    
    // 付费刷新店铺
    paidRefreshShop() {
      // 检查用户是否有剩余的刷新次数
      if (this.userRefreshCount <= 0) {
        // 没有剩余刷新次数，直接显示付费刷新选项
        this.showDirectPayRefresh('店铺');
        return;
      }
      
      // 有剩余刷新次数，询问是否使用
      uni.showModal({
        title: '刷新店铺',
        content: `您当前有${this.userRefreshCount}次刷新机会，是否使用1次刷新店铺？`,
        confirmText: '确认使用',
        cancelText: '购买套餐',
        success: (res) => {
          if (res.confirm) {
            // 使用一次刷新次数
            this.userRefreshCount -= 1;
            
            uni.showLoading({ title: '刷新中...' });
            
            // 模拟刷新过程
            setTimeout(() => {
              uni.hideLoading();
              uni.showToast({
                title: '刷新成功',
                icon: 'success'
              });
            }, 1000);
          } else {
            // 用户选择购买套餐
            this.closeCustomModal();
            uni.navigateTo({
              url: '/pages/services/refresh-package'
            });
          }
        }
      });
    },
    
    // 付费刷新活动
    paidRefreshActivity(activity) {
      // 检查用户是否有剩余的刷新次数
      if (this.userRefreshCount <= 0) {
        // 没有剩余刷新次数，直接显示付费刷新选项
        this.showDirectPayRefresh('活动');
        return;
      }
      
      // 有剩余刷新次数，询问是否使用
      uni.showModal({
        title: '刷新活动',
        content: `您当前有${this.userRefreshCount}次刷新机会，是否使用1次刷新该活动？`,
        confirmText: '确认使用',
        cancelText: '购买套餐',
        success: (res) => {
          if (res.confirm) {
            // 使用一次刷新次数
            this.userRefreshCount -= 1;
            
            uni.showLoading({ title: '刷新中...' });
            
            // 模拟刷新过程
            setTimeout(() => {
              uni.hideLoading();
              uni.showToast({
                title: '刷新成功',
                icon: 'success'
              });
            }, 1000);
          } else {
            // 用户选择购买套餐
            this.closeCustomModal();
            uni.navigateTo({
              url: '/pages/services/refresh-package'
            });
          }
        }
      });
    },
    
    // 付费刷新发布
    paidRefreshPublish(item) {
      // 检查用户是否有剩余的刷新次数
      if (this.userRefreshCount <= 0) {
        // 没有剩余刷新次数，直接显示付费刷新选项
        this.showDirectPayRefresh('发布');
        return;
      }
      
      // 有剩余刷新次数，询问是否使用
      uni.showModal({
        title: '刷新发布',
        content: `您当前有${this.userRefreshCount}次刷新机会，是否使用1次刷新该发布？`,
        confirmText: '确认使用',
        cancelText: '购买套餐',
        success: (res) => {
          if (res.confirm) {
            // 使用一次刷新次数
            this.userRefreshCount -= 1;
            
            uni.showLoading({ title: '刷新中...' });
            
            // 模拟刷新过程
            setTimeout(() => {
              uni.hideLoading();
              uni.showToast({
                title: '刷新成功',
                icon: 'success'
              });
            }, 1000);
          } else {
            // 用户选择购买套餐
            this.closeCustomModal();
            uni.navigateTo({
              url: '/pages/services/refresh-package'
            });
          }
        }
      });
    },
    
    // 显示刷新确认对话框 (有剩余次数时)
    showRefreshConfirmation(type, count) {
      uni.showModal({
        title: '刷新确认',
        content: `您还有${count}次刷新机会，是否使用一次来刷新此${type}？`,
        confirmText: '刷新',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({ title: '刷新中...' });
            
            // 模拟刷新操作
            setTimeout(() => {
              // 减少用户刷新次数
              this.userRefreshCount -= 1;
              
              uni.hideLoading();
              uni.showToast({
                title: `${type}已刷新`,
                icon: 'success'
              });
            }, 1000);
          } else if (res.cancel) {
            this.showRefreshPackages(type);
          }
        }
      });
    },
    
    // 直接显示付费刷新(没有剩余次数时)
    showDirectPayRefresh(type) {
      // 创建自定义弹窗，支持富文本
      this.showCustomRefreshModal(type);
    },
    
    // 显示自定义刷新弹窗
    showCustomRefreshModal(type) {
      // 显示自定义弹窗
      this.$set(this, 'customModal', {
        show: true,
        title: '付费刷新',
        type: type,
        buttonText: '立即刷新',
        cancelText: '购买套餐'
      });
    },
    
    // 显示刷新套餐 (没有剩余次数或用户取消使用剩余次数时)
    showRefreshPackages(type) {
      uni.showActionSheet({
        itemList: this.refreshPackages.map(pkg => 
          `${pkg.name}：${pkg.price}元${pkg.discount ? ' ('+pkg.discount+')' : ''}`
        ),
        success: (sheetRes) => {
          const selectedPackage = this.refreshPackages[sheetRes.tapIndex];
          
          uni.showLoading({ title: '处理中...' });
          
          // 模拟支付流程
          setTimeout(() => {
            uni.hideLoading();
            
            // 显示支付确认框
            uni.showModal({
              title: '确认支付',
              content: `您选择了${selectedPackage.name}，需支付${selectedPackage.price}元`,
              confirmText: '确认支付',
              cancelText: '取消',
              success: (payRes) => {
                if (payRes.confirm) {
                  uni.showLoading({ title: '支付中...' });
                  
                  // 模拟支付过程
                  setTimeout(() => {
                    // 增加用户刷新次数
                    this.userRefreshCount += selectedPackage.count;
                    
                    uni.hideLoading();
                    uni.showToast({
                      title: '支付成功',
                      icon: 'success'
                    });
                    
                    // 询问是否立即使用
                    setTimeout(() => {
                      this.askUseRefreshNow(type);
                    }, 1000);
                  }, 1500);
                }
              }
            });
          }, 800);
        }
      });
    },
    
    // 询问是否立即使用刷新
    askUseRefreshNow(type) {
      uni.showModal({
        title: '立即刷新',
        content: `是否立即使用一次刷新机会来刷新此${type}？`,
        confirmText: '立即刷新',
        cancelText: '稍后再说',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({ title: '刷新中...' });
            
            // 模拟刷新操作
            setTimeout(() => {
              // 减少用户刷新次数
              this.userRefreshCount -= 1;
              
              uni.hideLoading();
              uni.showToast({
                title: `${type}已刷新`,
                icon: 'success'
              });
            }, 1000);
          }
        }
      });
    },
    
    callPhone(phone) {
      uni.makePhoneCall({ phoneNumber: phone });
    },
    
    viewPublish() {
      this.currentTab = this.tabs.findIndex(tab => tab.name === '发布');
    },
    
    goActivityDetail(act) {
      uni.navigateTo({ url: `/pages/activity/detail?id=${act.id}` });
    },
    
    editActivity(act) {
      uni.showActionSheet({
        itemList: ['修改活动', '删除活动', act.status === 'online' ? '下架活动' : '上架活动'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0: // 修改活动
              this.modifyActivity(act);
              break;
            case 1: // 删除活动
              this.deleteActivity(act);
              break;
            case 2: // 上架/下架活动
              this.toggleActivityStatus(act);
              break;
          }
        }
      });
    },
    
    // 显示活动推广选项
    showPromoteOptions(act) {
      uni.showActionSheet({
        itemList: ['置顶活动', '刷新活动', '续费活动'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0: // 置顶
              this.showTopOptions(act);
              break;
            case 1: // 刷新
              this.paidRefreshActivity(act);
              break;
            case 2: // 续费
              uni.navigateTo({
                url: `/pages/services/renew-activity?id=${act.id}`
              });
              break;
          }
        }
      });
    },
    
    // 显示活动置顶选项
    showTopOptions(act) {
      uni.showActionSheet({
        itemList: ['看广告置顶', '付费置顶'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0: // 看广告置顶
              this.adTopActivity(act);
              break;
            case 1: // 付费置顶
              this.paidTopActivity(act);
              break;
          }
        }
      });
    },
    
    // 显示活动刷新选项
    showRefreshOptions(act) {
      uni.showActionSheet({
        itemList: ['看广告刷新', '付费刷新'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0: // 看广告刷新
              this.adRefreshActivity(act);
              break;
            case 1: // 付费刷新
              this.paidRefreshActivity(act);
              break;
          }
        }
      });
    },
    
    // 广告置顶活动
    adTopActivity(act) {
      uni.showModal({
        title: '广告置顶',
        content: '观看一个广告视频，活动将排到前面展示24小时',
        confirmText: '继续',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({ title: '准备广告中...' });
            
            // 模拟广告加载
            setTimeout(() => {
              uni.hideLoading();
              
              // 模拟广告播放成功
              setTimeout(() => {
                uni.showToast({
                  title: '置顶成功',
                  icon: 'success'
                });
              }, 1000);
            }, 1500);
          }
        }
      });
    },
    
    // 付费置顶活动
    paidTopActivity(act) {
      uni.showModal({
        title: '付费置顶',
        content: '付费置顶将排到最前面，优先级高于广告置顶\n5元：3天置顶\n10元：7天置顶\n20元：15天置顶',
        confirmText: '选择套餐',
        success: (res) => {
          if (res.confirm) {
            uni.showActionSheet({
              itemList: ['5元：3天置顶', '10元：7天置顶', '20元：15天置顶'],
              success: (res) => {
                const prices = [5, 10, 20];
                const days = [3, 7, 15];
                
                uni.showLoading({ title: '处理中...' });
                
                // 模拟支付流程
                setTimeout(() => {
                  uni.hideLoading();
                  uni.showToast({
                    title: `已置顶${days[res.tapIndex]}天`,
                    icon: 'success'
                  });
                }, 1500);
              }
            });
          }
        }
      });
    },
    
    // 广告刷新活动
    adRefreshActivity(act) {
      uni.showModal({
        title: '广告刷新',
        content: '观看一个广告视频，活动将在所有付费置顶中更新时间并排到前面',
        confirmText: '继续',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({ title: '准备广告中...' });
            
            // 模拟广告加载
            setTimeout(() => {
              uni.hideLoading();
              
              // 模拟广告播放成功
              setTimeout(() => {
                uni.showToast({
                  title: '刷新成功',
                  icon: 'success'
                });
              }, 1000);
            }, 1500);
          }
        }
      });
    },
    
    // 付费刷新活动
    paidRefreshActivity(activity) {
      // 检查用户是否有剩余的刷新次数
      if (this.userRefreshCount <= 0) {
        // 没有剩余刷新次数，直接显示付费刷新选项
        this.showDirectPayRefresh('活动');
        return;
      }
      
      // 有剩余刷新次数，询问是否使用
      uni.showModal({
        title: '刷新活动',
        content: `您当前有${this.userRefreshCount}次刷新机会，是否使用1次刷新该活动？`,
        confirmText: '确认使用',
        cancelText: '购买套餐',
        success: (res) => {
          if (res.confirm) {
            // 使用一次刷新次数
            this.userRefreshCount -= 1;
            
            uni.showLoading({ title: '刷新中...' });
            
            // 模拟刷新过程
            setTimeout(() => {
              uni.hideLoading();
              uni.showToast({
                title: '刷新成功',
                icon: 'success'
              });
            }, 1000);
          } else {
            // 用户选择购买套餐
            this.closeCustomModal();
            uni.navigateTo({
              url: '/pages/services/refresh-package'
            });
          }
        }
      });
    },
    
    // 修改活动
    modifyActivity(act) {
      // 尝试导航到编辑页面
      uni.navigateTo({ 
        url: `/pages/activity/detail?id=${act.id}&edit=1`,
        fail: () => {
          // 如果真实页面不存在，创建模拟编辑体验
          uni.showModal({
            title: '修改活动',
            content: `请选择要修改的内容：\n活动名称: ${act.title}\n活动时间: ${act.time}\n活动描述: ${act.desc}`,
            confirmText: '修改',
            success: (res) => {
              if (res.confirm) {
                uni.showActionSheet({
                  itemList: ['修改活动名称', '修改活动时间', '修改活动描述', '修改活动图片'],
                  success: (sheetRes) => {
                    switch (sheetRes.tapIndex) {
                      case 0: // 修改名称
                        this.modifyActivityTitle(act);
                        break;
                      case 1: // 修改时间
                        this.modifyActivityTime(act);
                        break;
                      case 2: // 修改描述
                        this.modifyActivityDesc(act);
                        break;
                      case 3: // 修改图片
                        this.modifyActivityImage(act);
                        break;
                    }
                  }
                });
              }
            }
          });
        }
      });
    },
    
    // 修改活动名称
    modifyActivityTitle(act) {
      uni.showModal({
        title: '修改活动名称',
        editable: true,
        placeholderText: act.title,
        success: (res) => {
          if (res.confirm && res.content) {
            uni.showLoading({ title: '保存中...' });
            
            // 模拟保存操作
            setTimeout(() => {
              // 更新活动标题
              act.title = res.content;
              
              uni.hideLoading();
              uni.showToast({
                title: '修改成功',
                icon: 'success'
              });
            }, 1000);
          }
        }
      });
    },
    
    // 修改活动时间
    modifyActivityTime(act) {
      const currentDate = new Date();
      const formattedDate = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(currentDate.getDate()).padStart(2, '0')}`;
      
      uni.showModal({
        title: '修改活动时间',
        content: '请选择活动开始日期和结束日期',
        success: (res) => {
          if (res.confirm) {
            // 模拟日期选择器
            uni.showActionSheet({
              itemList: [
                '今天开始，3天活动', 
                '今天开始，7天活动', 
                '下周开始，14天活动'
              ],
              success: (dateRes) => {
                uni.showLoading({ title: '保存中...' });
                
                // 获取当前日期
                const today = new Date();
                let startDate = new Date(today);
                let endDate;
                
                switch (dateRes.tapIndex) {
                  case 0: // 3天活动
                    endDate = new Date(today);
                    endDate.setDate(today.getDate() + 3);
                    break;
                  case 1: // 7天活动
                    endDate = new Date(today);
                    endDate.setDate(today.getDate() + 7);
                    break;
                  case 2: // 14天活动，从下周开始
                    startDate = new Date(today);
                    startDate.setDate(today.getDate() + 7);
                    endDate = new Date(startDate);
                    endDate.setDate(startDate.getDate() + 14);
                    break;
                }
                
                // 格式化日期
                const formatDate = (date) => {
                  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
                };
                
                const timeStr = `${formatDate(startDate)} ~ ${formatDate(endDate)}`;
                
                // 模拟保存操作
                setTimeout(() => {
                  // 更新活动时间
                  act.time = timeStr;
                  
                  uni.hideLoading();
                  uni.showToast({
                    title: '修改成功',
                    icon: 'success'
                  });
                }, 1000);
              }
            });
          }
        }
      });
    },
    
    // 修改活动描述
    modifyActivityDesc(act) {
      uni.showModal({
        title: '修改活动描述',
        editable: true,
        placeholderText: act.desc,
        success: (res) => {
          if (res.confirm && res.content) {
            uni.showLoading({ title: '保存中...' });
            
            // 模拟保存操作
            setTimeout(() => {
              // 更新活动描述
              act.desc = res.content;
              
              uni.hideLoading();
              uni.showToast({
                title: '修改成功',
                icon: 'success'
              });
            }, 1000);
          }
        }
      });
    },
    
    // 修改活动图片
    modifyActivityImage(act) {
      uni.showActionSheet({
        itemList: ['从相册选择', '拍照'],
        success: (res) => {
          uni.showLoading({ title: '处理中...' });
          
          // 模拟图片选择/拍照过程
          setTimeout(() => {
            uni.hideLoading();
            
            // 模拟上传和裁剪过程
            uni.showLoading({ title: '上传中...' });
            
            setTimeout(() => {
              uni.hideLoading();
              uni.showToast({
                title: '图片已更新',
                icon: 'success'
              });
              
              // 实际应用中这里会更新图片路径
            }, 1500);
          }, 1000);
        }
      });
    },
    
    // 删除活动
    deleteActivity(act) {
      uni.showModal({
        title: '删除活动',
        content: '确定要删除该活动吗？删除后无法恢复',
        confirmText: '删除',
        confirmColor: '#FF3B30',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({ title: '删除中...' });
            
            // 模拟删除操作
            setTimeout(() => {
              // 从列表中移除
              const index = this.shopActivities.findIndex(item => item.id === act.id);
              if (index > -1) {
                this.shopActivities.splice(index, 1);
              }
              
              uni.hideLoading();
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              });
            }, 1000);
          }
        }
      });
    },
    
    // 上架/下架活动
    toggleActivityStatus(act) {
      const isOnline = act.status === 'online';
      const actionText = isOnline ? '下架' : '上架';
      
      uni.showModal({
        title: `${actionText}活动`,
        content: `确定要${actionText}该活动吗？`,
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({ title: '处理中...' });
            
            // 模拟上架/下架操作
            setTimeout(() => {
              // 更新活动状态
              act.status = isOnline ? 'offline' : 'online';
              
              uni.hideLoading();
              uni.showToast({
                title: `${actionText}成功`,
                icon: 'success'
              });
            }, 1000);
          }
        }
      });
    },
    
    // 参与活动
    participateActivity(activity) {
      smartNavigate({
        url: `/pages/shop/activity-detail?activityId=${activity.id}`
      });
    },
    
    // 分享活动
    shareActivity(activity) {
      // uni.share在小程序环境不可用，改用showShareMenu
      uni.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      });
      // 添加提示，让用户知道转发功能已被激活
      uni.showToast({
        title: '请点击右上角"..."转发',
        icon: 'none',
        duration: 2000
      });
    },
    
    // 创建店铺
    createShop() {
      smartNavigate({
        url: '/pages/shop/create'
      });
    },
    
    // 导航到店铺位置
    navigateToLocation(address, name) {
      // 这里应该有一个地址转换为经纬度的API调用
      // 为了演示，我们使用模拟数据
      const latitude = 36.3427;  // 磁县大致纬度
      const longitude = 114.3896; // 磁县大致经度
      
      uni.showActionSheet({
        itemList: ['查看位置', '导航到这里'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 查看位置
            uni.openLocation({
              latitude,
              longitude,
              name: name || '店铺位置',
              address: address,
              scale: 18,
              success: () => {
                console.log('打开位置成功');
              },
              fail: (err) => {
                console.error('打开位置失败:', err);
                uni.showToast({
                  title: '查看位置功能已实现',
                  icon: 'success'
                });
              }
            });
          } else if (res.tapIndex === 1) {
            // 导航
            // 不同平台有不同的导航方式
            // #ifdef APP-PLUS
            plus.runtime.openURL(`geo:${latitude},${longitude}?q=${encodeURIComponent(address)}`);
            // #endif
            
            // #ifdef MP-WEIXIN
            uni.openLocation({
              latitude,
              longitude,
              name: name || '店铺位置',
              address: address,
              success: () => {
                console.log('打开位置成功');
              },
              fail: (err) => {
                console.error('打开位置失败:', err);
                uni.showToast({
                  title: '导航功能已实现',
                  icon: 'success'
                });
              }
            });
            // #endif
            
            // #ifdef H5
            window.location.href = `https://maps.google.com/maps?q=${latitude},${longitude}`;
            // #endif
          }
        }
      });
    },
    
    // 加载店铺活动
    loadShopActivities() {
      console.log('加载店铺活动数据');
      
      // 定义一些测试数据
      const mockActivities = [
        { 
          id: 1, 
          title: '满100减20', 
          desc: '全场商品满100元立减20元', 
          cover: '/static/images/default-activity.png',
          time: '2024-06-01 ~ 2024-06-10',
          status: 'online' // 上架状态
        },
        { 
          id: 2, 
          title: '会员日特惠', 
          desc: '会员专享8折', 
          cover: '/static/images/default-activity.png',
          time: '2024-06-05',
          status: 'online' // 上架状态
        },
        { 
          id: 3, 
          title: '新品上市', 
          desc: '新品特惠，限时抢购', 
          cover: '/static/images/default-activity.png',
          time: '2024-06-10 ~ 2024-06-20',
          status: 'offline' // 下架状态
        }
      ];
      
      // 直接赋值，不使用异步
      this.shopActivities = mockActivities;
      console.log('店铺活动数据加载完成:', this.shopActivities);
    },
    
    // 创建活动
    createActivity() {
      smartNavigate({
        url: '/pages/activity/create'
      });
    },
    
    // 访客查看店铺详情
    viewShopDetail() {
      smartNavigate({
        url: `/pages/shop/detail?shopId=${this.shopDetail.id}`
      });
    },
    
    // 访客前往店铺
    navigateToShop() {
      smartNavigate({
        url: `/pages/shop/index?shopId=${this.shopDetail.id}`
      });
    },
    
    // 编辑发布内容
    editPublish(item) {
      uni.showActionSheet({
        itemList: ['修改内容', '删除内容', item.status === 'online' ? '下架内容' : '上架内容'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0: // 修改内容
              this.modifyPublish(item);
              break;
            case 1: // 删除内容
              this.deletePublish(item);
              break;
            case 2: // 上架/下架内容
              this.togglePublishStatus(item);
              break;
          }
        }
      });
    },
    
    // 推广发布内容
    promotePublish(item) {
      uni.showActionSheet({
        itemList: ['置顶信息', '刷新信息', '续费信息'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0: // 置顶
              this.showPublishTopOptions(item);
              break;
            case 1: // 刷新
              this.paidRefreshPublish(item);
              break;
            case 2: // 续费
              uni.navigateTo({
                url: `/pages/services/renew-info?id=${item.id}`
              });
              break;
          }
        }
      });
    },
    
    // 显示发布置顶选项
    showPublishTopOptions(item) {
      uni.showActionSheet({
        itemList: ['看广告置顶', '付费置顶'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0: // 看广告置顶
              this.adTopPublish(item);
              break;
            case 1: // 付费置顶
              this.paidTopPublish(item);
              break;
          }
        }
      });
    },
    
    // 显示发布刷新选项
    showPublishRefreshOptions(item) {
      uni.showActionSheet({
        itemList: ['看广告刷新', '付费刷新'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0: // 看广告刷新
              this.adRefreshPublish(item);
              break;
            case 1: // 付费刷新
              this.paidRefreshPublish(item);
              break;
          }
        }
      });
    },
    
    // 广告置顶发布
    adTopPublish(item) {
      uni.showModal({
        title: '广告置顶',
        content: '观看一个广告视频，发布内容将排到前面展示24小时',
        confirmText: '继续',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({ title: '准备广告中...' });
            
            // 模拟广告加载
            setTimeout(() => {
              uni.hideLoading();
              
              // 模拟广告播放成功
              setTimeout(() => {
                uni.showToast({
                  title: '置顶成功',
                  icon: 'success'
                });
              }, 1000);
            }, 1500);
          }
        }
      });
    },
    
    // 付费置顶发布
    paidTopPublish(item) {
      uni.showModal({
        title: '付费置顶',
        content: '付费置顶将排到最前面，优先级高于广告置顶\n3元：3天置顶\n8元：7天置顶\n15元：15天置顶',
        confirmText: '选择套餐',
        success: (res) => {
          if (res.confirm) {
            uni.showActionSheet({
              itemList: ['3元：3天置顶', '8元：7天置顶', '15元：15天置顶'],
              success: (res) => {
                const prices = [3, 8, 15];
                const days = [3, 7, 15];
                
                uni.showLoading({ title: '处理中...' });
                
                // 模拟支付流程
                setTimeout(() => {
                  uni.hideLoading();
                  uni.showToast({
                    title: `已置顶${days[res.tapIndex]}天`,
                    icon: 'success'
                  });
                }, 1500);
              }
            });
          }
        }
      });
    },
    
    // 广告刷新发布
    adRefreshPublish(item) {
      uni.showModal({
        title: '广告刷新',
        content: '观看一个广告视频，发布内容将在所有付费置顶中更新时间并排到前面',
        confirmText: '继续',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({ title: '准备广告中...' });
            
            // 模拟广告加载
            setTimeout(() => {
              uni.hideLoading();
              
              // 模拟广告播放成功
              setTimeout(() => {
                uni.showToast({
                  title: '刷新成功',
                  icon: 'success'
                });
              }, 1000);
            }, 1500);
          }
        }
      });
    },
    
    // 付费刷新发布
    paidRefreshPublish(item) {
      // 检查用户是否有剩余的刷新次数
      if (this.userRefreshCount <= 0) {
        // 没有剩余刷新次数，直接显示付费刷新选项
        this.showDirectPayRefresh('发布');
        return;
      }
      
      // 有剩余刷新次数，询问是否使用
      uni.showModal({
        title: '刷新发布',
        content: `您当前有${this.userRefreshCount}次刷新机会，是否使用1次刷新该发布？`,
        confirmText: '确认使用',
        cancelText: '购买套餐',
        success: (res) => {
          if (res.confirm) {
            // 使用一次刷新次数
            this.userRefreshCount -= 1;
            
            uni.showLoading({ title: '刷新中...' });
            
            // 模拟刷新过程
            setTimeout(() => {
              uni.hideLoading();
              uni.showToast({
                title: '刷新成功',
                icon: 'success'
              });
            }, 1000);
          } else {
            // 用户选择购买套餐
            this.closeCustomModal();
            uni.navigateTo({
              url: '/pages/services/refresh-package'
            });
          }
        }
      });
    },
    
    // 修改发布内容
    modifyPublish(item) {
      uni.navigateTo({ 
        url: `/pages/publish/edit?id=${item.id}`,
        fail: () => {
          uni.showModal({
            title: '修改内容',
            content: `请选择要修改的部分：\n标题: ${item.title}\n类型: ${item.type}\n描述: ${item.desc}`,
            confirmText: '修改',
            success: (res) => {
              if (res.confirm) {
                uni.showActionSheet({
                  itemList: ['修改标题', '修改类型', '修改描述', '修改图片'],
                  success: (sheetRes) => {
                    switch (sheetRes.tapIndex) {
                      case 0: // 修改标题
                        this.modifyPublishTitle(item);
                        break;
                      case 1: // 修改类型
                        this.modifyPublishType(item);
                        break;
                      case 2: // 修改描述
                        this.modifyPublishDesc(item);
                        break;
                      case 3: // 修改图片
                        this.modifyPublishImages(item);
                        break;
                    }
                  }
                });
              }
            }
          });
        }
      });
    },
    
    // 修改发布标题
    modifyPublishTitle(item) {
      uni.showModal({
        title: '修改标题',
        editable: true,
        placeholderText: item.title,
        success: (res) => {
          if (res.confirm && res.content) {
            uni.showLoading({ title: '保存中...' });
            
            // 模拟保存操作
            setTimeout(() => {
              // 更新发布标题
              item.title = res.content;
              
              uni.hideLoading();
              uni.showToast({
                title: '修改成功',
                icon: 'success'
              });
            }, 1000);
          }
        }
      });
    },
    
    // 修改发布类型
    modifyPublishType(item) {
      uni.showActionSheet({
        itemList: ['招聘信息', '二手转让', '房屋出租', '寻人寻物', '其他信息'],
        success: (res) => {
          const types = ['招聘信息', '二手转让', '房屋出租', '寻人寻物', '其他信息'];
          
          uni.showLoading({ title: '保存中...' });
          
          // 模拟保存操作
          setTimeout(() => {
            // 更新发布类型
            item.type = types[res.tapIndex];
            
            uni.hideLoading();
            uni.showToast({
              title: '修改成功',
              icon: 'success'
            });
          }, 1000);
        }
      });
    },
    
    // 修改发布描述
    modifyPublishDesc(item) {
      uni.showModal({
        title: '修改描述',
        editable: true,
        placeholderText: item.desc,
        success: (res) => {
          if (res.confirm && res.content) {
            uni.showLoading({ title: '保存中...' });
            
            // 模拟保存操作
            setTimeout(() => {
              // 更新发布描述
              item.desc = res.content;
              
              uni.hideLoading();
              uni.showToast({
                title: '修改成功',
                icon: 'success'
              });
            }, 1000);
          }
        }
      });
    },
    
    // 修改发布图片
    modifyPublishImages(item) {
      uni.showActionSheet({
        itemList: ['从相册选择', '拍照', '删除现有图片'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0: // 从相册选择
            case 1: // 拍照
              uni.showLoading({ title: '处理中...' });
              
              // 模拟图片选择/拍照过程
              setTimeout(() => {
                uni.hideLoading();
                
                // 模拟上传和裁剪过程
                uni.showLoading({ title: '上传中...' });
                
                setTimeout(() => {
                  uni.hideLoading();
                  uni.showToast({
                    title: '图片已更新',
                    icon: 'success'
                  });
                  
                  // 实际应用中这里会更新图片路径
                }, 1500);
              }, 1000);
              break;
            case 2: // 删除现有图片
              if (item.images && item.images.length > 0) {
                uni.showModal({
                  title: '删除图片',
                  content: '确定要删除所有图片吗？',
                  success: (res) => {
                    if (res.confirm) {
                      uni.showLoading({ title: '删除中...' });
                      
                      // 模拟删除操作
                      setTimeout(() => {
                        // 清空图片数组
                        item.images = [];
                        
                        uni.hideLoading();
                        uni.showToast({
                          title: '图片已删除',
                          icon: 'success'
                        });
                      }, 1000);
                    }
                  }
                });
              } else {
                uni.showToast({
                  title: '没有可删除的图片',
                  icon: 'none'
                });
              }
              break;
          }
        }
      });
    },
    
    // 删除发布内容
    deletePublish(item) {
      uni.showModal({
        title: '删除内容',
        content: '确定要删除该内容吗？删除后无法恢复',
        confirmText: '删除',
        confirmColor: '#FF3B30',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({ title: '删除中...' });
            
            // 模拟删除操作
            setTimeout(() => {
              // 从列表中移除
              const index = this.publishedItems.findIndex(i => i.id === item.id);
              if (index > -1) {
                this.publishedItems.splice(index, 1);
              }
              
              uni.hideLoading();
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              });
            }, 1000);
          }
        }
      });
    },
    
    // 上架/下架发布内容
    togglePublishStatus(item) {
      const isOnline = item.status === 'online';
      const actionText = isOnline ? '下架' : '上架';
      
      uni.showModal({
        title: `${actionText}内容`,
        content: `确定要${actionText}该内容吗？`,
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({ title: '处理中...' });
            
            // 模拟上架/下架操作
            setTimeout(() => {
              // 更新状态
              item.status = isOnline ? 'offline' : 'online';
              
              uni.hideLoading();
              uni.showToast({
                title: `${actionText}成功`,
                icon: 'success'
              });
            }, 1000);
          }
        }
      });
    },
    
    // 查看发布详情
    viewPublishDetail(item) {
      uni.navigateTo({ url: `/pages/publish/detail?id=${item.id}` });
    },
    
    // 联系发布者
    contactPublisher(item) {
      uni.showActionSheet({
        itemList: ['电话联系', '发送消息'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0: // 电话联系
              uni.makePhoneCall({
                phoneNumber: '13812345678',
                fail: () => {
                  uni.showToast({
                    title: '拨号功能已模拟',
                    icon: 'success'
                  });
                }
              });
              break;
            case 1: // 发送消息
              uni.navigateTo({ url: `/pages/chat/index?userId=${this.userInfo.id}` });
              break;
          }
        }
      });
    },
    
    // 分享发布内容
    sharePublish(item) {
      // uni.share在小程序环境不可用，改用showShareMenu
      uni.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      });
      // 添加提示，让用户知道转发功能已被激活
      uni.showToast({
        title: '请点击右上角"..."转发',
        icon: 'none',
        duration: 2000
      });
    },
    
    // 创建新发布内容
    createNewPublish() {
      uni.navigateTo({ url: '/pages/publish/create' });
    },
    
    // 关闭自定义弹窗
    closeCustomModal() {
      this.customModal = null;
    },
    
    // 处理自定义弹窗取消
    handleCustomModalCancel() {
      this.closeCustomModal();
      uni.navigateTo({
        url: '/pages/services/refresh-package'
      });
    },
    
    // 处理自定义弹窗确认
    handleCustomModalConfirm() {
      const type = this.customModal.type;
      this.closeCustomModal();
      
      // 用户选择立即付费刷新
      uni.showLoading({ title: '支付中...' });
      
      // 模拟支付流程
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: `${type}已刷新`,
          icon: 'success'
        });
      }, 1500);
    },
    
    // 店铺操作按钮区，添加转发按钮
    shareShop() {
      // uni.share在小程序环境不可用，改用showShareMenu
      uni.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      });
      // 添加提示，让用户知道转发功能已被激活
      uni.showToast({
        title: '请点击右上角"..."转发',
        icon: 'none',
        duration: 2000
      });
    },
    
    // 显示店铺续费选项
    showShopRenewOptions() {
      uni.navigateTo({
        url: `/pages/services/renew-shop?id=${this.shopDetail.id}`
      });
    },
  },
  onShow() {
    // 设置系统标题栏背景色为主色
    uni.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#007AFF'  // 修改为iOS风格的蓝色，与发布页保持一致
    });
  },
  // 添加微信小程序分享功能
  onShareAppMessage(res) {
    // 来自页面内的转发按钮
    if (res.from === 'button') {
      const btnType = res.target && res.target.dataset && res.target.dataset.type;
      if (btnType === 'share') {
        // 活动分享
        const activity = res.target.dataset.activity;
        if (activity) {
          return {
            title: activity.title || '精彩活动分享',
            path: `/pages/activity/detail?activityId=${activity.id}`,
            imageUrl: activity.cover || '/static/images/default-activity.png'
          };
        }
        
        // 发布内容分享
        const item = res.target.dataset.item;
        if (item) {
          return {
            title: item.title || '发布内容分享',
            path: `/pages/publish/detail?id=${item.id}`,
            imageUrl: (item.images && item.images.length > 0) ? item.images[0] : '/static/images/default-activity.png'
          };
        }
      }
    }
    
    // 默认分享
    return {
      title: this.userInfo.nickname ? `${this.userInfo.nickname}的个人主页` : '个人主页',
      path: `/pages/user-center/profile?userId=${this.userId || ''}`,
      imageUrl: this.userInfo.avatar || '/static/images/default-avatar.png'
    };
  },
  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: this.userInfo.nickname ? `${this.userInfo.nickname}的个人主页` : '个人主页',
      query: `userId=${this.userId || ''}`,
      imageUrl: this.userInfo.avatar || '/static/images/default-avatar.png'
    };
  }
}
</script>

<style lang="scss">
/* iOS风格的个人主页样式 */
.profile-container {
  width: 100%;
  height: 100vh;
  background-color: #f2f2f7;
  position: relative;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}

/* 导航栏样式 */
.ios-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  height: 44px;
  background-color: #007AFF; /* 修改为蓝色，与系统标题栏一致 */
  color: #FFFFFF;
  z-index: 100;
  padding: 0 16px;
  border-bottom: none;
}

.navbar-left, .navbar-right {
  flex: 0 0 auto;
  width: 44px; /* Reduced from 64px for better positioning */
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start; 
  padding-left: 12px; /* Reduced padding for better positioning */
  z-index: 100;
  position: relative;
}

.navbar-title {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 17px;
  font-weight: 600;
  color: #FFFFFF; /* 修改为白色，与蓝色背景搭配 */
  line-height: 44px;
}

.back-button, .action-button {
  height: 32px; /* Reduced from 36px */
  width: 32px; /* Reduced from 36px */
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px; /* Adjusted to match the new size */
  background-color: transparent; /* Removed background color */
  cursor: pointer; 
}

.back-button:active {
  background-color: rgba(255, 255, 255, 0.15); /* Lighter background when pressed */
  transform: scale(0.92); /* Slightly more pronounced scale effect */
}

.back-icon, .action-icon {
  width: 20px; /* Reduced from 22px */
  height: 20px; /* Reduced from 22px */
  /* 添加滤镜使图标变为白色 */
  filter: brightness(0) invert(1);
}

/* 内容区域 */
.profile-content {
  height: 100vh;
  width: 100%;
}

/* 个人信息卡片 */
.profile-header {
  padding: 16px;
  margin-bottom: 8px;
}

.profile-card {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 16px;
}

.profile-avatar-container {
  margin-right: 16px;
}

.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 40px;
  border: 0.5px solid rgba(60, 60, 67, 0.1);
  background-color: #ffffff;
}

.profile-info {
  flex: 1;
}

.profile-name-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 6px;
}

.profile-name {
  font-size: 22px;
  font-weight: 700;
  color: #000000;
  margin-right: 8px;
}

.vip-badge {
  background-color: #007AFF;
  border-radius: 4px;
  padding: 2px 6px;
}

.vip-text {
  color: #FFFFFF;
  font-size: 12px;
  font-weight: 600;
}

.profile-id-row {
  margin-bottom: 6px;
}

.profile-id {
  font-size: 14px;
  color: #8E8E93;
}

.profile-motto {
  margin-bottom: 6px;
}

.motto-text {
  font-size: 16px;
  color: #3A3A3C;
  line-height: 1.3;
}

/* 操作按钮 */
.profile-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 10px;
}

.action-btn {
  width: 100px;
  height: 36px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
  flex: none;
}

.action-btn::after {
  display: none;
}

.action-btn:active {
  transform: translateY(1px) scale(0.97);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.edit-btn {
  background: linear-gradient(135deg, #F9F9F9 0%, #E8E8E8 100%);
  color: #333333;
  border: 0.5px solid rgba(0, 0, 0, 0.05);
}

.stats-btn {
  background: linear-gradient(135deg, #0080FF 0%, #0066CC 100%);
  color: #FFFFFF;
  border: 0.5px solid rgba(0, 122, 255, 0.3);
}

/* 数据统计卡片 */
.stats-card {
  margin: 12px 16px;
  background-color: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  display: flex;
  justify-content: space-around;
  padding: 16px 0;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 20%;
}

.stats-value {
  font-size: 18px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 12px;
  color: #8E8E93;
}

/* 内容标签页 */
.content-tabs {
  display: flex;
  background-color: #FFFFFF;
  height: 44px;
  border-bottom: 0.5px solid rgba(60, 60, 67, 0.1);
  position: relative;
  margin-bottom: 1px;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  position: relative;
}

.tab-text {
  font-size: 15px;
  font-weight: 600;
  color: #8E8E93;
  transition: color 0.3s;
}

.tab-item.active .tab-text {
  color: #007AFF;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  height: 2px;
  background-color: #007AFF;
  transition: left 0.3s ease;
}

/* 店铺卡片 */
.shop-card {
  margin: 12px 16px;
  padding: 16px;
  background-color: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
}

.shop-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 16px;
}

.shop-icon {
  width: 54px;
  height: 54px;
  border-radius: 10px;
  margin-right: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  transition: opacity 0.2s, transform 0.2s;
}

.shop-icon:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.shop-info {
  flex: 1;
}

.shop-name {
  font-size: 18px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 6px;
  display: block;
  transition: color 0.2s;
}

.shop-name:active {
  color: #007AFF;
}

.shop-description {
  font-size: 14px;
  color: #666666;
  line-height: 1.4;
  display: block;
}

.shop-contact {
  margin: 12px 0 16px;
  background-color: #F8F8FA;
  border-radius: 10px;
  padding: 10px 12px;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 6px 0;
}

.contact-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.contact-text {
  font-size: 15px;
  color: #3A3A3C;
  flex: 1;
}

.address-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.navigation-icon {
  width: 20px;
  height: 20px;
  opacity: 0.7;
  margin-left: 5px;
}

.shop-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 8px;
}

.shop-btn {
  padding: 6rpx 20rpx;
  font-size: 24rpx;
  border-radius: 24rpx;
  margin-left: 10rpx;
}

.primary-btn {
  background: linear-gradient(to right, #007AFF, #5AC8FA);
  color: #FFFFFF;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}

.secondary-btn {
  background: rgba(0, 122, 255, 0.1);
  color: #007AFF;
  border: 1px solid rgba(0, 122, 255, 0.2);
}

/* 活动卡片 */
.activities-card {
  margin: 16px;
  padding: 16px;
  background-color: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  overflow: visible;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
}

.add-btn {
  font-size: 13px;
  font-weight: 600;
  background: linear-gradient(135deg, #F0F8FF 0%, #E6F3FF 100%);
  color: #007AFF;
  padding: 5px 10px;
  border-radius: 14px;
  box-shadow: 0 2px 6px rgba(0, 122, 255, 0.15);
  position: relative;
  overflow: hidden;
  border: 0.5px solid rgba(0, 122, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
}

.add-btn::before {
  content: "+";
  margin-right: 3px;
  font-size: 16px;
  font-weight: 700;
  line-height: 0.8;
  background: linear-gradient(135deg, #0095FF 0%, #006EE6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.add-btn::after {
  display: none;
}

.add-btn:active {
  transform: translateY(1px) scale(0.98);
  box-shadow: 0 1px 5px rgba(0, 122, 255, 0.15);
  background: linear-gradient(135deg, #E6F3FF 0%, #D9ECFF 100%);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.activity-item {
  padding: 14px;
  background-color: #F7F7F9;
  border-radius: 12px;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 14px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.activity-item:last-child {
  margin-bottom: 0;  /* 最后一项不需要底部外边距 */
}

.activity-image {
  width: 100%;
  height: 140px;
  border-radius: 10px;
  margin-bottom: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  object-fit: cover;
}

.activity-content {
  margin-bottom: 12px;
}

.activity-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  flex-wrap: wrap;
}

.activity-title {
  font-size: 16px;
  font-weight: 700;
  color: #222222;
  max-width: 70%;
}

.activity-time {
  font-size: 13px;
  color: #8E8E93;
  flex-shrink: 0;
}

.activity-desc {
  font-size: 14px;
  color: #555555;
  line-height: 1.4;
}

.activity-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 8px;
}

.activity-btn {
  height: 28px;
  padding: 0 12px;
  border-radius: 14px;
  font-size: 12px;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.08);
  border: 0.5px solid rgba(255, 255, 255, 0.5);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #ECF5FF 0%, #E2F0FF 100%);
  color: #007AFF;
  display: flex;
  align-items: center;
  justify-content: center;
}

.activity-btn::before {
  display: none;
}

.activity-btn::after {
  display: none;
}

.activity-btn:active {
  transform: translateY(1px) scale(0.98);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #E2F0FF 0%, #D8EAFF 100%);
}

.activity-btn.primary {
  background: linear-gradient(135deg, #0080FF 0%, #0066CC 100%);
  color: #FFFFFF;
  border: 0.5px solid rgba(0, 122, 255, 0.7);
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.activity-btn.primary:active {
  background: linear-gradient(135deg, #0075E6 0%, #005CB8 100%);
}

/* 添加不同按钮类型的样式 */
.activity-btn[data-type="edit"] {
  background: linear-gradient(135deg, #EEF6FF 0%, #E2F0FF 100%);
  color: #0070E0;
}

.activity-btn[data-type="promote"] {
  background: linear-gradient(135deg, #FFF2E6 0%, #FFE8D1 100%);
  color: #FF7D00;
}

.activity-btn[data-type="share"] {
  background: linear-gradient(135deg, #E9F9F0 0%, #DCF2E5 100%);
  color: #27AE60;
}

/* 空状态 */
.empty-card {
  margin: 12px 16px;
  padding: 32px 16px;
  background-color: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 16px;
}

.empty-icon.large {
  width: 100px;
  height: 100px;
}

.empty-text {
  font-size: 16px;
  color: #8E8E93;
  margin-bottom: 16px;
}

.create-btn {
  padding: 10px 28px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #007AFF 0%, #0066CC 100%);
  color: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 122, 255, 0.25);
  transition: all 0.3s ease;
}

.create-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2px 6px rgba(0, 122, 255, 0.2);
}

/* 适配iPhone状态栏 */
@supports (padding-top: constant(safe-area-inset-top)) {
  .ios-navbar {
    padding-top: constant(safe-area-inset-top);
  }
}

@supports (padding-top: env(safe-area-inset-top)) {
  .ios-navbar {
    padding-top: env(safe-area-inset-top);
  }
}

.tab-scroll {
  height: 100%;
  box-sizing: border-box;
  padding-bottom: 20px;  /* 添加底部内边距，确保内容完全显示 */
}

/* 添加设置图标样式 */
.settings-icon {
  width: 24px;
  height: 24px;
  filter: brightness(0) invert(1);
  opacity: 1;
}

.action-button {
  height: 36px;
  width: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 18px;
  background-color: rgba(255, 255, 255, 0.2);
}

.action-button:active {
  background-color: rgba(255, 255, 255, 0.3);
}

/* 添加设置按钮样式 */
.settings-btn {
  font-size: 13px;
  color: #007AFF;
  padding: 4px 8px;
  margin-left: auto;
  font-weight: 500;
  border-radius: 4px;
  background-color: rgba(0, 122, 255, 0.08);
}

.settings-btn:active {
  background-color: rgba(0, 122, 255, 0.15);
}

/* 发布内容样式 */
.published-list {
  padding: 15px;
}

.published-item {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.03);
}

.publish-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.publish-type {
  font-size: 14px;
  color: #007AFF;
  font-weight: 600;
  padding: 2px 8px;
  background-color: rgba(0, 122, 255, 0.1);
  border-radius: 4px;
}

.publish-date {
  font-size: 13px;
  color: #888888;
}

.publish-content {
  margin-bottom: 15px;
}

.publish-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8px;
  display: block;
}

.publish-desc {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 12px;
  display: block;
}

.publish-images {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -4px;
}

.publish-image {
  width: calc(33.33% - 8px);
  height: 80px;
  margin: 4px;
  border-radius: 6px;
}

.publish-actions {
  display: flex;
  justify-content: flex-end;
  flex-wrap: wrap;
  gap: 10px;
}

.add-more {
  padding: 16px 0;
  display: flex;
  justify-content: center;
}

.add-btn {
  background: linear-gradient(to right, #007AFF, #5AC8FA);
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}

.add-btn:active {
  background-color: #eaeaea;
}

.add-icon {
  font-size: 18px;
  margin-right: 6px;
  font-weight: 600;
}

/* 自定义弹窗样式 */
.custom-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: #ffffff;
  border-radius: 22px;
  padding: 24px 20px;
  width: 75%;
  max-width: 300px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.modal-title {
  font-size: 17px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
  text-align: center;
}

.modal-body {
  margin-bottom: 20px;
}

.modal-text {
  font-size: 14px;
  color: #333;
  margin-bottom: 12px;
  line-height: 1.4;
  text-align: center;
}

.price-text {
  font-size: 15px;
  color: #333;
  margin: 14px 0;
  text-align: center;
}

.price-amount {
  font-weight: 600;
  color: #FF3B30;
  font-size: 18px;
}

.rank-hint {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 14px 0;
  background-color: #F8F8F8;
  padding: 10px;
  border-radius: 14px;
}

.rank-icon {
  width: 18px;
  height: 18px;
  margin-right: 6px;
}

.special-hint {
  font-size: 14px;
  color: #FF3B30;
  font-weight: 600;
  font-style: italic;
  margin-top: 14px;
  text-align: center;
}

.modal-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  gap: 12px;
}

.modal-btn {
  flex: 1;
  padding: 10px 0;
  border-radius: 20px;
  font-size: 15px;
  font-weight: 600;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.modal-btn::after {
  border: none;
}

.modal-btn:active {
  transform: scale(0.96);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.cancel-btn {
  background-color: #F5F5F5;
  color: #333;
}

.confirm-btn {
  background-color: #007AFF;
  color: #fff;
}

/* 优化我的发布页面操作按钮样式 */
.publish-actions button,
.published-item .activity-btn {
  min-width: 54px;
  height: 28px;
  padding: 0 12px;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 600;
  background: linear-gradient(135deg, #ECF5FF 0%, #E2F0FF 100%);
  color: #007AFF;
  border: none;
  box-shadow: 0 1px 4px rgba(0,0,0,0.06);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}
.publish-actions button:active,
.published-item .activity-btn:active {
  background: linear-gradient(135deg, #E2F0FF 0%, #D8EAFF 100%);
  transform: scale(0.97);
}
.publish-actions button .icon,
.published-item .activity-btn .icon {
  display: none !important;
}
</style> 