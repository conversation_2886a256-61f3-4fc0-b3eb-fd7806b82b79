{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/discount/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcZGlzY291bnRcaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"discount-page\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\">\r\n      <view class=\"navbar-bg\"></view>\r\n      <view class=\"navbar-content\">\r\n        <view class=\"back-btn\" @click=\"goBack\">\r\n          <image class=\"back-icon\" src=\"/static/images/tabbar/最新返回键.png\" mode=\"aspectFit\"></image>\r\n        </view>\r\n        <view class=\"navbar-title\">满减优惠</view>\r\n        <view class=\"navbar-right\">\r\n          <view class=\"close-btn\" @click=\"goBack\">\r\n            <svg class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" width=\"22\" height=\"22\">\r\n              <path d=\"M512 421.490332L331.349941 240.840273c-24.988383-24.988383-65.35828-24.988383-90.346664 0-24.988383 24.988383-24.988383 65.35828 0 90.346664L421.653336 512 240.840273 692.812059c-24.988383 24.988383-24.988383 65.35828 0 90.346664 24.988383 24.988383 65.35828 24.988383 90.346664 0L512 602.509668l180.650059 180.650059c24.988383 24.988383 65.35828 24.988383 90.346664 0 24.988383-24.988383 24.988383-65.35828 0-90.346664L602.346664 512l180.813063-180.812059c24.988383-24.988383 24.988383-65.35828 0-90.346664-24.988383-24.988383-65.35828-24.988383-90.346664 0L512 421.490332z\" fill=\"#FFFFFF\"></path>\r\n            </svg>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 筛选选项卡 -->\r\n    <view class=\"filter-tabs\">\r\n      <view \r\n        class=\"tab-item\" \r\n        v-for=\"(tab, index) in tabs\" \r\n        :key=\"index\"\r\n        :class=\"{ active: currentTabIndex === index }\"\r\n        @click=\"switchTab(index)\"\r\n      >\r\n        <text>{{ tab.name }}</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 内容区域 -->\r\n    <scroll-view \r\n      class=\"content-scroll\" \r\n      scroll-y \r\n      @scrolltolower=\"loadMore\"\r\n    >\r\n      <!-- 商家满减活动列表 -->\r\n      <view class=\"discount-list\">\r\n        <view \r\n          class=\"discount-item\" \r\n          v-for=\"(item, index) in discountItems\" \r\n          :key=\"index\"\r\n          @click=\"navigateToDetail(item.id)\"\r\n        >\r\n          <view class=\"merchant-info\">\r\n            <image class=\"merchant-logo\" :src=\"item.logo\" mode=\"aspectFill\"></image>\r\n            <view class=\"merchant-name\">{{ item.merchantName }}</view>\r\n            <view class=\"discount-tag\" v-if=\"item.tag\">{{ item.tag }}</view>\r\n          </view>\r\n          \r\n          <view class=\"discount-rules\">\r\n            <view \r\n              class=\"rule-item\" \r\n              v-for=\"(rule, ruleIndex) in item.rules\" \r\n              :key=\"ruleIndex\"\r\n              :class=\"{ 'highlight': rule.highlight }\"\r\n            >\r\n              <text class=\"rule-text\">{{ rule.text }}</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"discount-footer\">\r\n            <view class=\"discount-time\">\r\n              <text>{{ getTimeText(item.startTime, item.endTime) }}</text>\r\n            </view>\r\n            <view class=\"discount-btn\">\r\n              <text>立即使用</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 加载更多提示 -->\r\n      <view class=\"loading-more\" v-if=\"loading\">\r\n        <text>加载中...</text>\r\n      </view>\r\n      \r\n      <!-- 到底了提示 -->\r\n      <view class=\"no-more\" v-if=\"noMore\">\r\n        <text>已经到底啦~</text>\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      refreshing: false,\r\n      loading: false,\r\n      noMore: false,\r\n      currentTabIndex: 0,\r\n      tabs: [\r\n        { name: '全部' },\r\n        { name: '餐饮美食' },\r\n        { name: '休闲娱乐' },\r\n        { name: '生活服务' }\r\n      ],\r\n      discountItems: [\r\n        {\r\n          id: 1,\r\n          merchantName: '星巴克咖啡',\r\n          logo: 'https://via.placeholder.com/100x100',\r\n          tag: '热门',\r\n          rules: [\r\n            { text: '满50减10', highlight: false },\r\n            { text: '满100减30', highlight: true },\r\n            { text: '满200减60', highlight: false }\r\n          ],\r\n          startTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),\r\n          endTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()\r\n        },\r\n        {\r\n          id: 2,\r\n          merchantName: '肯德基',\r\n          logo: 'https://via.placeholder.com/100x100',\r\n          tag: '限时',\r\n          rules: [\r\n            { text: '满59减15', highlight: false },\r\n            { text: '满99减30', highlight: true },\r\n            { text: '满199减60', highlight: false }\r\n          ],\r\n          startTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),\r\n          endTime: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString()\r\n        },\r\n        {\r\n          id: 3,\r\n          merchantName: '海底捞火锅',\r\n          logo: 'https://via.placeholder.com/100x100',\r\n          tag: '爆款',\r\n          rules: [\r\n            { text: '满200减30', highlight: false },\r\n            { text: '满400减80', highlight: true },\r\n            { text: '满600减150', highlight: false }\r\n          ],\r\n          startTime: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),\r\n          endTime: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000).toISOString()\r\n        },\r\n        {\r\n          id: 4,\r\n          merchantName: '喜茶',\r\n          logo: 'https://via.placeholder.com/100x100',\r\n          tag: '新店',\r\n          rules: [\r\n            { text: '满40减8', highlight: false },\r\n            { text: '满80减20', highlight: true },\r\n            { text: '满120减35', highlight: false }\r\n          ],\r\n          startTime: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),\r\n          endTime: new Date(Date.now() + 25 * 24 * 60 * 60 * 1000).toISOString()\r\n        },\r\n        {\r\n          id: 5,\r\n          merchantName: '优衣库',\r\n          logo: 'https://via.placeholder.com/100x100',\r\n          tag: '折扣',\r\n          rules: [\r\n            { text: '满300减50', highlight: false },\r\n            { text: '满500减100', highlight: true },\r\n            { text: '满800减200', highlight: false }\r\n          ],\r\n          startTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),\r\n          endTime: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString()\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  onLoad() {\r\n    // 页面加载时获取数据\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n    // 返回上一页\r\n    goBack() {\r\n      uni.navigateBack()\r\n    },\r\n    \r\n    // 切换选项卡\r\n    switchTab(index) {\r\n      this.currentTabIndex = index\r\n      this.fetchData()\r\n    },\r\n    \r\n    // 下拉刷新\r\n    onRefresh() {\r\n      // 获取当前滚动位置\r\n      const query = uni.createSelectorQuery().in(this);\r\n      query.select('.content-scroll').boundingClientRect(data => {\r\n        // 只有在滚动到顶部或接近顶部时才刷新\r\n        if (data && data.top <= 5) {\r\n          this.refreshing = true\r\n          \r\n          // 模拟刷新数据\r\n          setTimeout(() => {\r\n            this.fetchData()\r\n            this.refreshing = false\r\n            uni.showToast({\r\n              title: '刷新成功',\r\n              icon: 'none'\r\n            })\r\n          }, 1500)\r\n        } else {\r\n          // 不在顶部，取消刷新状态\r\n          this.refreshing = false\r\n        }\r\n      }).exec();\r\n    },\r\n    \r\n    // 加载更多\r\n    loadMore() {\r\n      if (this.loading || this.noMore) return\r\n      \r\n      this.loading = true\r\n      \r\n      // 模拟加载更多数据\r\n      setTimeout(() => {\r\n        // 添加更多满减活动数据\r\n        const moreItems = [\r\n          {\r\n            id: 6,\r\n            merchantName: '必胜客',\r\n            logo: 'https://via.placeholder.com/100x100',\r\n            tag: '人气',\r\n            rules: [\r\n              { text: '满100减20', highlight: false },\r\n              { text: '满200减50', highlight: true },\r\n              { text: '满300减80', highlight: false }\r\n            ],\r\n            startTime: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),\r\n            endTime: new Date(Date.now() + 18 * 24 * 60 * 60 * 1000).toISOString()\r\n          },\r\n          {\r\n            id: 7,\r\n            merchantName: '屈臣氏',\r\n            logo: 'https://via.placeholder.com/100x100',\r\n            tag: '特惠',\r\n            rules: [\r\n              { text: '满99减20', highlight: false },\r\n              { text: '满199减50', highlight: true },\r\n              { text: '满299减100', highlight: false }\r\n            ],\r\n            startTime: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString(),\r\n            endTime: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString()\r\n          }\r\n        ]\r\n        \r\n        this.discountItems = [...this.discountItems, ...moreItems]\r\n        this.noMore = true // 示例中加载一次后就没有更多数据\r\n        this.loading = false\r\n      }, 1500)\r\n    },\r\n    \r\n    // 获取数据\r\n    fetchData() {\r\n      // 实际项目中，这里应该根据当前选中的选项卡调用API获取数据\r\n      // 示例中使用的是静态数据\r\n    },\r\n    \r\n    // 导航到详情页\r\n    navigateToDetail(id) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/activity-showcase/pages/discount/detail?id=${id}`\r\n      })\r\n    },\r\n    \r\n    // 获取活动时间文本\r\n    getTimeText(startTime, endTime) {\r\n      const start = new Date(startTime)\r\n      const end = new Date(endTime)\r\n      \r\n      const startMonth = start.getMonth() + 1\r\n      const startDay = start.getDate()\r\n      const endMonth = end.getMonth() + 1\r\n      const endDay = end.getDate()\r\n      \r\n      return `${startMonth}.${startDay}-${endMonth}.${endDay}`\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.discount-page {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  background-color: #F2F2F7;\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.custom-navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: calc(var(--status-bar-height, 25px) + 62px);\r\n  width: 100%;\r\n  z-index: 100;\r\n  \r\n  .navbar-bg {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: linear-gradient(135deg, #5E5CE6 0%, #5856D6 100%);\r\n  }\r\n  \r\n  .navbar-content {\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: 100%;\r\n    padding-top: var(--status-bar-height, 25px);\r\n    padding-left: 30rpx;\r\n    padding-right: 30rpx;\r\n    box-sizing: border-box;\r\n    \r\n    .back-btn {\r\n      width: 40rpx;\r\n      height: 40rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n    }\r\n    \r\n    .back-icon {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n    \r\n    .navbar-title {\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n      color: #FFFFFF;\r\n      position: absolute;\r\n      left: 50%;\r\n      transform: translateX(-50%);\r\n    }\r\n    \r\n    .navbar-right {\r\n      width: 80rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: flex-end;\r\n      \r\n      .close-btn {\r\n        width: 64rpx;\r\n        height: 64rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 筛选选项卡 */\r\n.filter-tabs {\r\n  position: relative;\r\n  display: flex;\r\n  background-color: #FFFFFF;\r\n  height: 88rpx;\r\n  margin-top: calc(var(--status-bar-height, 25px) + 62px);\r\n  overflow-x: auto;\r\n  white-space: nowrap;\r\n  \r\n  .tab-item {\r\n    display: inline-flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    padding: 0 30rpx;\r\n    font-size: 28rpx;\r\n    color: #666666;\r\n    position: relative;\r\n    height: 100%;\r\n    \r\n    &.active {\r\n      color: #5856D6;\r\n      font-weight: 600;\r\n      \r\n      &::after {\r\n        content: '';\r\n        position: absolute;\r\n        bottom: 0;\r\n        left: 50%;\r\n        transform: translateX(-50%);\r\n        width: 40rpx;\r\n        height: 4rpx;\r\n        background-color: #5856D6;\r\n        border-radius: 2rpx;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 内容区域 */\r\n.content-scroll {\r\n  flex: 1;\r\n  width: 100%;\r\n}\r\n\r\n/* 满减活动列表 */\r\n.discount-list {\r\n  padding: 20rpx;\r\n}\r\n\r\n.discount-item {\r\n  margin-bottom: 20rpx;\r\n  background-color: #FFFFFF;\r\n  border-radius: 16rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);\r\n  \r\n  .merchant-info {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 20rpx;\r\n    border-bottom: 1px solid #F2F2F7;\r\n    \r\n    .merchant-logo {\r\n      width: 60rpx;\r\n      height: 60rpx;\r\n      border-radius: 30rpx;\r\n      margin-right: 16rpx;\r\n    }\r\n    \r\n    .merchant-name {\r\n      flex: 1;\r\n      font-size: 28rpx;\r\n      font-weight: 600;\r\n      color: #333333;\r\n    }\r\n    \r\n    .discount-tag {\r\n      padding: 4rpx 12rpx;\r\n      font-size: 22rpx;\r\n      color: #FFFFFF;\r\n      border-radius: 10rpx;\r\n      background: linear-gradient(135deg, #5E5CE6 0%, #5856D6 100%);\r\n    }\r\n  }\r\n  \r\n  .discount-rules {\r\n    padding: 20rpx;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    \r\n    .rule-item {\r\n      margin-right: 20rpx;\r\n      margin-bottom: 10rpx;\r\n      padding: 8rpx 16rpx;\r\n      border-radius: 20rpx;\r\n      background-color: #F2F2F7;\r\n      \r\n      &.highlight {\r\n        background: linear-gradient(135deg, #5E5CE6 0%, #5856D6 100%);\r\n        \r\n        .rule-text {\r\n          color: #FFFFFF;\r\n        }\r\n      }\r\n      \r\n      .rule-text {\r\n        font-size: 24rpx;\r\n        color: #666666;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .discount-footer {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 20rpx;\r\n    border-top: 1px solid #F2F2F7;\r\n    \r\n    .discount-time {\r\n      font-size: 24rpx;\r\n      color: #999999;\r\n    }\r\n    \r\n    .discount-btn {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      height: 60rpx;\r\n      width: 160rpx;\r\n      border-radius: 30rpx;\r\n      background: linear-gradient(135deg, #5E5CE6 0%, #5856D6 100%);\r\n      font-size: 26rpx;\r\n      font-weight: 500;\r\n      color: #FFFFFF;\r\n    }\r\n  }\r\n}\r\n\r\n/* 加载更多和到底了提示 */\r\n.loading-more, .no-more {\r\n  text-align: center;\r\n  padding: 20rpx 0;\r\n  font-size: 24rpx;\r\n  color: #999999;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/discount/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAyFA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,iBAAiB;AAAA,MACjB,MAAM;AAAA,QACJ,EAAE,MAAM,KAAM;AAAA,QACd,EAAE,MAAM,OAAQ;AAAA,QAChB,EAAE,MAAM,OAAQ;AAAA,QAChB,EAAE,MAAM,OAAO;AAAA,MAChB;AAAA,MACD,eAAe;AAAA,QACb;AAAA,UACE,IAAI;AAAA,UACJ,cAAc;AAAA,UACd,MAAM;AAAA,UACN,KAAK;AAAA,UACL,OAAO;AAAA,YACL,EAAE,MAAM,UAAU,WAAW,MAAO;AAAA,YACpC,EAAE,MAAM,WAAW,WAAW,KAAM;AAAA,YACpC,EAAE,MAAM,WAAW,WAAW,MAAM;AAAA,UACrC;AAAA,UACD,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,UACvE,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,EAAE,YAAY;AAAA,QACtE;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,cAAc;AAAA,UACd,MAAM;AAAA,UACN,KAAK;AAAA,UACL,OAAO;AAAA,YACL,EAAE,MAAM,UAAU,WAAW,MAAO;AAAA,YACpC,EAAE,MAAM,UAAU,WAAW,KAAM;AAAA,YACnC,EAAE,MAAM,WAAW,WAAW,MAAM;AAAA,UACrC;AAAA,UACD,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,UACvE,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,EAAE,YAAY;AAAA,QACtE;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,cAAc;AAAA,UACd,MAAM;AAAA,UACN,KAAK;AAAA,UACL,OAAO;AAAA,YACL,EAAE,MAAM,WAAW,WAAW,MAAO;AAAA,YACrC,EAAE,MAAM,WAAW,WAAW,KAAM;AAAA,YACpC,EAAE,MAAM,YAAY,WAAW,MAAM;AAAA,UACtC;AAAA,UACD,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,UACxE,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,EAAE,YAAY;AAAA,QACtE;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,cAAc;AAAA,UACd,MAAM;AAAA,UACN,KAAK;AAAA,UACL,OAAO;AAAA,YACL,EAAE,MAAM,SAAS,WAAW,MAAO;AAAA,YACnC,EAAE,MAAM,UAAU,WAAW,KAAM;AAAA,YACnC,EAAE,MAAM,WAAW,WAAW,MAAM;AAAA,UACrC;AAAA,UACD,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,UACvE,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,EAAE,YAAY;AAAA,QACtE;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,cAAc;AAAA,UACd,MAAM;AAAA,UACN,KAAK;AAAA,UACL,OAAO;AAAA,YACL,EAAE,MAAM,WAAW,WAAW,MAAO;AAAA,YACrC,EAAE,MAAM,YAAY,WAAW,KAAM;AAAA,YACrC,EAAE,MAAM,YAAY,WAAW,MAAM;AAAA,UACtC;AAAA,UACD,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,UACvE,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,EAAE,YAAY;AAAA,QACvE;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAEP,SAAK,UAAU;AAAA,EAChB;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,SAAS;AACPA,oBAAAA,MAAI,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,UAAU,OAAO;AACf,WAAK,kBAAkB;AACvB,WAAK,UAAU;AAAA,IAChB;AAAA;AAAA,IAGD,YAAY;AAEV,YAAM,QAAQA,cAAAA,MAAI,oBAAqB,EAAC,GAAG,IAAI;AAC/C,YAAM,OAAO,iBAAiB,EAAE,mBAAmB,UAAQ;AAEzD,YAAI,QAAQ,KAAK,OAAO,GAAG;AACzB,eAAK,aAAa;AAGlB,qBAAW,MAAM;AACf,iBAAK,UAAU;AACf,iBAAK,aAAa;AAClBA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,aACP;AAAA,UACF,GAAE,IAAI;AAAA,eACF;AAEL,eAAK,aAAa;AAAA,QACpB;AAAA,MACF,CAAC,EAAE,KAAI;AAAA,IACR;AAAA;AAAA,IAGD,WAAW;AACT,UAAI,KAAK,WAAW,KAAK;AAAQ;AAEjC,WAAK,UAAU;AAGf,iBAAW,MAAM;AAEf,cAAM,YAAY;AAAA,UAChB;AAAA,YACE,IAAI;AAAA,YACJ,cAAc;AAAA,YACd,MAAM;AAAA,YACN,KAAK;AAAA,YACL,OAAO;AAAA,cACL,EAAE,MAAM,WAAW,WAAW,MAAO;AAAA,cACrC,EAAE,MAAM,WAAW,WAAW,KAAM;AAAA,cACpC,EAAE,MAAM,WAAW,WAAW,MAAM;AAAA,YACrC;AAAA,YACD,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,YACvE,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,EAAE,YAAY;AAAA,UACtE;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,cAAc;AAAA,YACd,MAAM;AAAA,YACN,KAAK;AAAA,YACL,OAAO;AAAA,cACL,EAAE,MAAM,UAAU,WAAW,MAAO;AAAA,cACpC,EAAE,MAAM,WAAW,WAAW,KAAM;AAAA,cACpC,EAAE,MAAM,YAAY,WAAW,MAAM;AAAA,YACtC;AAAA,YACD,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,YACvE,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,EAAE,YAAY;AAAA,UACvE;AAAA,QACF;AAEA,aAAK,gBAAgB,CAAC,GAAG,KAAK,eAAe,GAAG,SAAS;AACzD,aAAK,SAAS;AACd,aAAK,UAAU;AAAA,MAChB,GAAE,IAAI;AAAA,IACR;AAAA;AAAA,IAGD,YAAY;AAAA,IAGX;AAAA;AAAA,IAGD,iBAAiB,IAAI;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,2DAA2D,EAAE;AAAA,OACnE;AAAA,IACF;AAAA;AAAA,IAGD,YAAY,WAAW,SAAS;AAC9B,YAAM,QAAQ,IAAI,KAAK,SAAS;AAChC,YAAM,MAAM,IAAI,KAAK,OAAO;AAE5B,YAAM,aAAa,MAAM,SAAQ,IAAK;AACtC,YAAM,WAAW,MAAM,QAAQ;AAC/B,YAAM,WAAW,IAAI,SAAQ,IAAK;AAClC,YAAM,SAAS,IAAI,QAAQ;AAE3B,aAAO,GAAG,UAAU,IAAI,QAAQ,IAAI,QAAQ,IAAI,MAAM;AAAA,IACxD;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzRA,GAAG,WAAW,eAAe;"}