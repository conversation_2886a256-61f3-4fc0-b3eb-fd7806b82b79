<template>
	<view class="join-page">
		<!-- 高斯模糊背景 -->
		<view class="blur-bg">
			<image src="/static/images/banner/banner-3.jpg" mode="aspectFill" class="bg-image"></image>
		</view>
		
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 导航栏 -->
		<view class="navbar">
			<view class="navbar-left" @click="goBack">
				<image src="/static/images/tabbar/返回.png" class="back-icon-img"></image>
			</view>
			<text class="navbar-title">商家入驻</text>
			<view class="navbar-right"></view>
		</view>
		
		<!-- 内容区 -->
		<view class="content">
			<!-- 入驻说明卡片 -->
			<view class="intro-card glass-card">
				<view class="intro-header">
					<view class="intro-icon-wrap">
						<image src="/static/images/tabbar/入驻卡片.png" class="intro-icon"></image>
					</view>
					<view class="intro-titles">
						<text class="intro-title">商家免费入驻</text>
						<view class="subtitle-wrapper">
							<text class="intro-subtitle">获取更多商业机会和客户资源</text>
						</view>
					</view>
				</view>
				<view class="intro-stats">
					<view class="stat-item">
						<text class="stat-num">123.4万</text>
						<text class="stat-label">月曝光量</text>
					</view>
					<view class="stat-divider"></view>
					<view class="stat-item">
						<text class="stat-num">567</text>
						<text class="stat-label">已入驻商家</text>
					</view>
					<view class="stat-divider"></view>
					<view class="stat-item">
						<text class="stat-num">89%</text>
						<text class="stat-label">转化率</text>
					</view>
				</view>
			</view>
			
			<!-- 表单区域 -->
			<view class="form-section glass-card">
				<text class="form-title">基本信息</text>
				
				<view class="form-item">
					<text class="form-label">店铺名称</text>
					<input type="text" class="form-input" placeholder="请输入商家名称" v-model="formData.shopName" />
				</view>
				
				<view class="form-item">
					<text class="form-label">详细地址</text>
					<view class="address-wrap">
						<input type="text" class="form-input address-input" placeholder="请输入店铺地址" v-model="formData.address" />
						<view class="location-btn" @click.stop="getLocation">
							<image src="/static/images/tabbar/定位.png" class="location-icon-img"></image>
						</view>
					</view>
					<view class="location-tips" v-if="formData.address">
						<text class="location-text">磁县正义路</text>
						<text class="location-btn-text" @click="getLocation">定位</text>
					</view>
				</view>
				
				<view class="form-item">
					<text class="form-label">所属行业</text>
					<view class="form-select" @click="showCategoryPicker">
						<text :class="{'placeholder': !formData.category}">{{formData.category || '点击选择所属行业'}}</text>
						<image src="/static/images/tabbar/箭头.png" class="select-arrow-img"></image>
					</view>
				</view>
				
				<view class="form-item">
					<text class="form-label">商家规模</text>
					<view class="form-select" @click="showScalePicker">
						<text :class="{'placeholder': !formData.scale}">{{formData.scale || '点击选择商家规模人数'}}</text>
						<image src="/static/images/tabbar/箭头.png" class="select-arrow-img"></image>
					</view>
				</view>
				
				<view class="form-item">
					<text class="form-label">营业时间</text>
					<view class="form-select" @click="showTimePicker">
						<text :class="{'placeholder': !formData.businessTime}">{{formData.businessTime || '点击选择营业时间'}}</text>
						<image src="/static/images/tabbar/箭头.png" class="select-arrow-img"></image>
					</view>
				</view>
				
				<view class="form-item">
					<text class="form-label">联系电话</text>
					<input type="number" class="form-input" placeholder="请输入联系电话" v-model="formData.contactPhone" />
				</view>
				
				<view class="form-item">
					<text class="form-label">商家介绍</text>
					<textarea class="form-textarea" placeholder="请填写商家介绍内容" v-model="formData.description"></textarea>
				</view>
			</view>
			
			<!-- 图片上传区域 -->
			<view class="form-section glass-card">
				<text class="form-title">商家图片</text>
				<view class="upload-box" @click="uploadImage('shopImage')">
					<view class="upload-placeholder">
						<text class="plus-icon">+</text>
					</view>
				</view>
			</view>
			
			<view class="form-section glass-card">
				<text class="form-title">商家logo</text>
				<view class="upload-box" @click="uploadImage('logo')">
					<view class="upload-placeholder">
						<text class="plus-icon">+</text>
					</view>
					<view class="upload-desc-container">
						<text class="upload-desc">请上传商家logo</text>
						<text class="upload-desc">或门头照片</text>
					</view>
				</view>
			</view>
			
			<view class="form-section glass-card">
				<text class="form-title">客服微信二维码</text>
				<view class="upload-box" @click="uploadImage('qrcode')">
					<view class="upload-placeholder">
						<text class="plus-icon">+</text>
					</view>
					<view class="upload-desc-container">
						<text class="upload-desc">请上传客服</text>
						<text class="upload-desc">微信二维码</text>
					</view>
				</view>
			</view>
			
			<view class="form-section glass-card">
				<text class="form-title">商家相册</text>
				<view class="upload-box" @click="uploadImage('album')">
					<view class="upload-placeholder">
						<text class="plus-icon">+</text>
					</view>
					<text class="upload-desc">最多上传10张照片</text>
				</view>
			</view>
			
			<!-- 入驻方式选择 -->
			<view class="form-section glass-card">
				<text class="form-title">请选择入驻方式</text>
				
				<!-- 更清晰的入驻方式选择 -->
				<view class="join-methods">
					<view class="join-option" :class="{'active': formData.joinMethod === 'free'}" @click="selectJoinMethod('free')">
						<view class="option-radio">
							<view class="radio-dot" v-if="formData.joinMethod === 'free'"></view>
						</view>
						<view class="option-content">
							<view class="option-title-row">
								<image src="/static/images/tabbar/广告.png" class="option-icon"></image>
								<text class="option-title">看广告免费入驻</text>
								<view class="option-badge">推荐</view>
							</view>
							<view class="option-desc">观看广告，获得1个月免费特权</view>
						</view>
					</view>
					
					<view class="join-option" :class="{'active': formData.joinMethod === 'paid'}" @click="selectJoinMethod('paid')">
						<view class="option-radio">
							<view class="radio-dot" v-if="formData.joinMethod === 'paid'"></view>
						</view>
						<view class="option-content">
							<view class="option-title-row">
								<image src="/static/images/tabbar/会员.png" class="option-icon"></image>
								<text class="option-title">付费入驻</text>
								<view class="option-badge premium-badge">特权</view>
							</view>
							<view class="option-desc">解锁全部功能，提升曝光量</view>
						</view>
					</view>
				</view>
				
				<!-- 免费入驻特权说明 -->
				<view class="privilege-list" v-if="formData.joinMethod === 'free'">
					<view class="privilege-title">免费特权 (1个月有效期)</view>
					<view class="privilege-items">
						<view class="privilege-item">
							<view class="privilege-dot"></view>
							<text class="privilege-text">店铺基础展示</text>
						</view>
						<view class="privilege-item">
							<view class="privilege-dot"></view>
							<text class="privilege-text">最多展示10个商品</text>
						</view>
						<view class="privilege-item">
							<view class="privilege-dot"></view>
							<text class="privilege-text">上传5张店铺照片</text>
						</view>
					</view>
					
					<button class="watch-ad-button" @click="watchAdToJoin">
						<image src="/static/images/tabbar/广告.png" class="btn-icon"></image>
						观看广告免费入驻
					</button>
				</view>
				
				<!-- 已选版本展示 -->
				<view class="selected-plan apple-card" v-if="formData.joinMethod === 'paid' && formData.version && !showVersionModal">
					<view class="plan-header">
						<view class="plan-badge" :class="getPlanBadgeClass()">{{getVersionName()}}</view>
						<text class="change-plan" @click="showVersionModal = true">更换</text>
					</view>
					<view class="plan-price-row">
						<text class="plan-price-label">¥</text>
						<text class="plan-price-value">{{getVersionPrice()}}</text>
						<text class="plan-price-cycle">/年</text>
					</view>
					<view class="plan-divider"></view>
					<view class="plan-features">
						<view class="plan-feature">
							<view class="feature-dot"></view>
							<text class="feature-text">{{getVersionMainFeature()}}</text>
						</view>
						<view class="plan-feature">
							<view class="feature-dot"></view>
							<text class="feature-text">{{getVersionSecondFeature()}}</text>
						</view>
						<button class="select-other-btn" @click="showVersionModal = true">查看其他版本</button>
					</view>
				</view>
			</view>
			
			<!-- 付费入驻版本弹窗 -->
			<view class="popup-mask" v-if="showVersionModal" @click="hideVersionModal"></view>
			<view class="popup-content version-popup" v-if="showVersionModal">
				<view class="popup-header">
					<text class="popup-title">选择付费入驻版本</text>
					<view class="popup-close" @click="hideVersionModal">
						<image src="/static/images/tabbar/关闭.png" class="close-icon-img"></image>
					</view>
				</view>
				
				<scroll-view class="version-list" scroll-y>
					<!-- 基础版 -->
					<view class="version-card apple-style" :class="{'active': formData.version === 'basic'}" @click="selectVersion('basic')">
						<view class="version-card-bg basic-gradient">
							<view class="version-pill">基础版</view>
							<view class="version-content">
								<view class="version-price-area">
									<text class="version-price-label">¥</text>
									<text class="version-price-value">49.9</text>
									<text class="version-price-cycle">/年</text>
								</view>
								<view class="version-desc">适合个体商户</view>
								
								<view class="version-features">
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">商品发布数量：最多20个</text>
									</view>
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">店铺展示位置：普通位置</text>
									</view>
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">客户数据分析：基础版</text>
									</view>
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">免费获赠一次店铺推广</text>
									</view>
								</view>
								
								<view class="version-select-btn" :class="{'selected': formData.version === 'basic'}">
									<text>{{ formData.version === 'basic' ? '已选择' : '选择此版本' }}</text>
								</view>
							</view>
						</view>
					</view>
					
					<!-- 高级版 -->
					<view class="version-card apple-style" :class="{'active': formData.version === 'premium'}" @click="selectVersion('premium')">
						<view class="version-card-bg premium-gradient">
							<view class="version-badge">
								<text class="badge-text">热门</text>
							</view>
							<view class="version-pill">高级版</view>
							<view class="version-content">
								<view class="version-price-area">
									<text class="version-price-label">¥</text>
									<text class="version-price-value">149.9</text>
									<text class="version-price-cycle">/年</text>
								</view>
								<view class="version-desc">性价比最高</view>
								
								<view class="version-features">
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">商品发布数量：最多50个</text>
									</view>
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">店铺展示位置：优先位置</text>
									</view>
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">客户数据分析：专业版</text>
									</view>
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">赠送3次店铺推广和置顶</text>
									</view>
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">商品视频展示功能</text>
									</view>
								</view>
								
								<view class="version-select-btn premium-select" :class="{'selected': formData.version === 'premium'}">
									<text>{{ formData.version === 'premium' ? '已选择' : '选择此版本' }}</text>
								</view>
							</view>
						</view>
					</view>
					
					<!-- 尊贵版 -->
					<view class="version-card apple-style" :class="{'active': formData.version === 'deluxe'}" @click="selectVersion('deluxe')">
						<view class="version-card-bg deluxe-gradient">
							<view class="version-pill">尊贵版</view>
							<view class="version-content">
								<view class="version-price-area">
									<text class="version-price-label">¥</text>
									<text class="version-price-value">299.9</text>
									<text class="version-price-cycle">/年</text>
								</view>
								<view class="version-desc">全功能无限制</view>
								
								<view class="version-features">
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">商品发布数量：无限制</text>
									</view>
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">店铺展示位置：最佳位置</text>
									</view>
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">客户数据分析：高级版</text>
									</view>
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">免费获赠整年店铺推广</text>
									</view>
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">优先客服一对一服务</text>
									</view>
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">专属VIP店铺标识</text>
									</view>
								</view>
								
								<view class="version-select-btn deluxe-select" :class="{'selected': formData.version === 'deluxe'}">
									<text>{{ formData.version === 'deluxe' ? '已选择' : '选择此版本' }}</text>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
				
				<view class="version-popup-footer">
					<button class="confirm-version-btn" @click="confirmVersion">确认选择</button>
				</view>
			</view>
			
			<!-- 协议同意 -->
			<view class="agreement-section">
				<view class="agreement-checkbox" @click="toggleAgreement">
					<view class="checkbox" :class="{'checked': formData.agreed}"></view>
				</view>
				<view class="agreement-text">我已阅读同意<text class="agreement-link">《服务协议》</text>和<text class="agreement-link">《隐私政策》</text></view>
			</view>
			
			<!-- 提交按钮 -->
			<view class="submit-section">
				<button class="submit-btn" @click="submitForm">确认入驻</button>
			</view>
		</view>
		
		<!-- 分类选择弹出层 -->
		<view class="popup-mask" v-if="showCategoryModal || showScaleModal || showTimeModal" @click="hideAllModals"></view>
		
		<!-- 分类选择 -->
		<view class="popup-content" v-if="showCategoryModal">
			<view class="popup-header">
				<text class="popup-title">选择经营类目</text>
				<view class="popup-close" @click="hideCategoryPicker">
					<image src="/static/images/tabbar/关闭.png" class="close-icon-img"></image>
				</view>
			</view>
			<scroll-view class="category-list" scroll-y>
				<view 
					class="category-item" 
					v-for="(item, index) in categories" 
					:key="index"
					@click="selectCategory(item)"
					:class="{'active': formData.category === item}"
				>
					<text>{{item}}</text>
					<image src="/static/images/tabbar/选中.png" class="check-icon-img" v-if="formData.category === item"></image>
				</view>
			</scroll-view>
		</view>
		
		<!-- 规模选择 -->
		<view class="popup-content" v-if="showScaleModal">
			<view class="popup-header">
				<text class="popup-title">选择商家规模</text>
				<view class="popup-close" @click="hideScalePicker">
					<image src="/static/images/tabbar/关闭.png" class="close-icon-img"></image>
				</view>
			</view>
			<scroll-view class="category-list" scroll-y>
				<view 
					class="category-item" 
					v-for="(item, index) in scales" 
					:key="index"
					@click="selectScale(item)"
					:class="{'active': formData.scale === item}"
				>
					<text>{{item}}</text>
					<image src="/static/images/tabbar/选中.png" class="check-icon-img" v-if="formData.scale === item"></image>
				</view>
			</scroll-view>
		</view>
		
		<!-- 时间选择 -->
		<view class="popup-content" v-if="showTimeModal">
			<view class="popup-header">
				<text class="popup-title">选择营业时间</text>
				<view class="popup-close" @click="hideTimePicker">
					<image src="/static/images/tabbar/关闭.png" class="close-icon-img"></image>
				</view>
			</view>
			<scroll-view class="category-list" scroll-y>
				<view 
					class="category-item" 
					v-for="(item, index) in businessTimes" 
					:key="index"
					@click="selectTime(item)"
					:class="{'active': formData.businessTime === item}"
				>
					<text>{{item}}</text>
					<image src="/static/images/tabbar/选中.png" class="check-icon-img" v-if="formData.businessTime === item"></image>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	// 导入位置服务相关方法
	import { refreshLocation, getCurrentLocation } from '../../utils/locationService.js';
	
	export default {
		data() {
			return {
				statusBarHeight: 20,
				showCategoryModal: false,
				showScaleModal: false,
				showTimeModal: false,
				showVersionModal: false,
				isLocating: false,
				formData: {
					shopName: '',
					category: '',
					contactPhone: '',
					address: '',
					description: '',
					scale: '',
					businessTime: '',
					shopImage: '',
					logo: '',
					qrcode: '',
					albumImages: [],
					joinMethod: 'free',
					version: 'basic',
					agreed: false
				},
				categories: [
					'美食小吃', '休闲娱乐', '房产楼盘', '装修家居', 
					'母婴专区', '到家服务', '开锁换锁', '数码通讯',
					'车辆服务', '教育培训', '婚纱摄影', '农林牧渔',
					'广告传媒', '其他行业'
				],
				scales: [
					'1-5人', '6-10人', '11-20人', '21-50人', '50人以上'
				],
				businessTimes: [
					'全天24小时', '8:00-18:00', '9:00-22:00', '10:00-22:00', '自定义'
				]
			}
		},
		onLoad() {
			// 获取状态栏高度
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
				}
			});
		},
		methods: {
			goBack() {
				uni.navigateBack();
			},
			showCategoryPicker() {
				this.showCategoryModal = true;
			},
			hideCategoryPicker() {
				this.showCategoryModal = false;
			},
			selectCategory(category) {
				this.formData.category = category;
				this.hideCategoryPicker();
			},
			showScalePicker() {
				this.showScaleModal = true;
			},
			hideScalePicker() {
				this.showScaleModal = false;
			},
			selectScale(scale) {
				this.formData.scale = scale;
				this.hideScalePicker();
			},
			showTimePicker() {
				this.showTimeModal = true;
			},
			hideTimePicker() {
				this.showTimeModal = false;
			},
			selectTime(time) {
				this.formData.businessTime = time;
				this.hideTimePicker();
			},
			getLocation() {
				console.log('定位按钮被点击');
				// 防止重复点击
				if (this.isLocating) return;
				this.isLocating = true;
				
				uni.showLoading({
					title: '定位中...'
				});
				
				// 使用默认地址，不请求位置权限
				setTimeout(() => {
					// 设置默认地址
					this.formData.address = "河北省邯郸市磁县祥和路";
					
					uni.hideLoading();
					uni.showToast({
						title: '已获取当前地址',
						icon: 'success'
					});
					this.isLocating = false;
				}, 500);
			},
			uploadImage(type) {
				uni.chooseImage({
					count: type === 'album' ? 9 : 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						const tempFilePaths = res.tempFilePaths;
						
						if (type === 'album') {
							// 限制相册最多10张图片
							const currentCount = this.formData.albumImages.length;
							const canAddCount = 10 - currentCount;
							
							if (tempFilePaths.length > canAddCount) {
								uni.showToast({
									title: `最多上传10张照片，还能上传${canAddCount}张`,
									icon: 'none'
								});
								this.formData.albumImages = [...this.formData.albumImages, ...tempFilePaths.slice(0, canAddCount)];
							} else {
								this.formData.albumImages = [...this.formData.albumImages, ...tempFilePaths];
							}
						} else {
							this.formData[type] = tempFilePaths[0];
						}
						
						uni.showToast({
							title: '上传成功',
							icon: 'success'
						});
					}
				});
			},
			removeImage(type, index) {
				if (type === 'album') {
					this.formData.albumImages.splice(index, 1);
				} else {
					this.formData[type] = '';
				}
			},
			selectJoinMethod(method) {
				this.formData.joinMethod = method;
				
				// 如果选择付费入驻，弹出版本选择弹窗
				if (method === 'paid') {
					this.showVersionModal = true;
				}
			},
			selectVersion(version) {
				this.formData.version = version;
			},
			hideVersionModal() {
				this.showVersionModal = false;
			},
			confirmVersion() {
				this.hideVersionModal();
				// 显示已选择的版本信息
				uni.showToast({
					title: '已选择' + this.getVersionName(),
					icon: 'none'
				});
			},
			getVersionName() {
				const versionMap = {
					'basic': '基础版',
					'premium': '高级版',
					'deluxe': '尊贵版'
				};
				return versionMap[this.formData.version] || '基础版';
			},
			getVersionPrice() {
				const priceMap = {
					'basic': '49.9',
					'premium': '149.9',
					'deluxe': '299.9'
				};
				return priceMap[this.formData.version] || '';
			},
			getVersionMainFeature() {
				const featureMap = {
					'basic': '商品发布数量：最多20个',
					'premium': '商品发布数量：最多50个',
					'deluxe': '商品发布数量：无限制'
				};
				return featureMap[this.formData.version] || '商品发布数量：最多20个';
			},
			getVersionSecondFeature() {
				const featureMap = {
					'basic': '店铺展示位置：普通位置',
					'premium': '店铺展示位置：优先位置',
					'deluxe': '店铺展示位置：最佳位置'
				};
				return featureMap[this.formData.version] || '店铺展示位置：普通位置';
			},
			watchAdToJoin() {
				// 实际项目中调用广告SDK显示广告
				uni.showLoading({
					title: '广告加载中...'
				});
				
				// 模拟观看广告的过程
				setTimeout(() => {
					uni.hideLoading();
					// 广告观看完成后的处理
					uni.showToast({
						title: '已获得1个月免费入驻特权',
						icon: 'success',
						duration: 1500
					});
					
					// 检查表单必填项是否已填
					if (!this.formData.shopName || !this.formData.category || !this.formData.contactPhone || !this.formData.address) {
						setTimeout(() => {
							uni.showToast({
								title: '请先完善商家必填信息',
								icon: 'none',
								duration: 2000
							});
						}, 1500);
						return;
					}
					
					// 如果信息已完善，自动提交
					if (this.formData.shopName && this.formData.category && this.formData.contactPhone && this.formData.address) {
						setTimeout(() => {
							// 自动勾选同意协议
							this.formData.agreed = true;
							
							// 提交表单
							uni.showLoading({
								title: '提交中...'
							});
							
							// 设置会员类型为免费版
							const memberType = '免费版';
							
							setTimeout(() => {
								uni.hideLoading();
								// 直接跳转到成功页面，传递会员类型
								uni.navigateTo({
									url: '/pages/business/success?shopId=' + encodeURIComponent(this.formData.shopName) +
										'&memberType=' + encodeURIComponent(memberType)
								});
							}, 1500);
						}, 1800);
					}
				}, 1500);
			},
			toggleAgreement() {
				this.formData.agreed = !this.formData.agreed;
			},
			submitForm() {
				// 表单验证
				if (!this.formData.shopName) {
					this.showToast('请输入店铺名称');
					return;
				}
				if (!this.formData.category) {
					this.showToast('请选择所属行业');
					return;
				}
				if (!this.formData.contactPhone) {
					this.showToast('请输入联系电话');
					return;
				}
				if (!this.formData.address) {
					this.showToast('请输入店铺地址');
					return;
				}
				if (!this.formData.agreed) {
					this.showToast('请阅读并同意服务协议和隐私政策');
					return;
				}
				
				// 如果是付费入驻但没有选择版本
				if (this.formData.joinMethod === 'paid' && !this.formData.version) {
					this.showToast('请选择入驻版本');
					this.showVersionModal = true;
					return;
				}
				
				// 提交表单
				uni.showLoading({
					title: '提交中...'
				});
				
				// 获取会员类型名称
				let memberType = '免费版';
				if (this.formData.joinMethod === 'paid') {
					memberType = this.getVersionName();
				}
				
				// 模拟提交
				setTimeout(() => {
					uni.hideLoading();
					// 直接跳转到成功页面，传递会员类型
					uni.navigateTo({
						url: '/pages/business/success?shopId=' + encodeURIComponent(this.formData.shopName) + 
							'&memberType=' + encodeURIComponent(memberType)
					});
				}, 1500);
			},
			showToast(title) {
				uni.showToast({
					title: title,
					icon: 'none'
				});
			},
			hideAllModals() {
				this.showCategoryModal = false;
				this.showScaleModal = false;
				this.showTimeModal = false;
			},
			getPlanBadgeClass() {
				const versionMap = {
					'basic': 'basic-badge',
					'premium': 'premium-badge',
					'deluxe': 'deluxe-badge'
				};
				return versionMap[this.formData.version] || 'basic-badge';
			}
		}
	}
</script>

<style>
	/* 页面样式 */
	.join-page {
		min-height: 100vh;
		background-color: #f8f9fc;
		position: relative;
	}
	
	/* 高斯模糊背景 */
	.blur-bg {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		height: 450rpx;
		z-index: 0;
		overflow: hidden;
	}
	
	.bg-image {
		width: 100%;
		height: 100%;
		filter: blur(20px);
		transform: scale(1.1);
		opacity: 0.8;
	}
	
	.blur-bg::after {
		content: '';
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		background: linear-gradient(to bottom, 
			#0052CC 0%, 
			#0066FF 50%,
			#f8f9fc 100%
		);
	}
	
	/* 导航栏 */
	.status-bar, .navbar {
		position: relative;
		z-index: 10;
	}
	
	.navbar {
		height: 44px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;
	}
	
	.navbar-left, .navbar-right {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-icon-img {
		width: 32rpx;
		height: 32rpx;
	}
	
	.navbar-title {
		color: #ffffff;
		font-size: 34rpx;
		font-weight: 500;
		letter-spacing: 1rpx;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
	}
	
	/* 内容区 */
	.content {
		position: relative;
		z-index: 5;
		padding: 30rpx;
		padding-bottom: 80rpx;
	}
	
	/* 玻璃拟态卡片通用样式 */
	.glass-card {
		background-color: rgba(255, 255, 255, 0.92);
		backdrop-filter: blur(16px);
		border-radius: 20rpx;
		padding: 32rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
	}
	
	/* 入驻说明卡片 */
	.intro-card {
		padding: 36rpx;
		position: relative;
		overflow: hidden;
		border-radius: 24rpx;
		background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 249, 255, 0.95));
		box-shadow: 
			0 10rpx 30rpx rgba(22, 119, 255, 0.15), 
			0 6rpx 12rpx rgba(0, 0, 0, 0.1),
			0 1rpx 0 rgba(255, 255, 255, 1) inset,
			0 -20rpx 60rpx rgba(255, 255, 255, 0.5) inset;
		border: 1px solid rgba(255, 255, 255, 0.7);
		transform: translateY(-8rpx);
		transition: all 0.3s ease;
	}
	
	.intro-card::after {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 100rpx;
		background: linear-gradient(to bottom, rgba(255, 255, 255, 0.5), transparent);
		pointer-events: none;
	}
	
	.intro-header {
		display: flex;
		align-items: center;
		margin-bottom: 36rpx;
	}
	
	.intro-icon-wrap {
		width: 90rpx;
		height: 90rpx;
		border-radius: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 24rpx;
		background: linear-gradient(135deg, #1677ff, #0052cc);
		box-shadow: 0 4rpx 12rpx rgba(22, 119, 255, 0.2);
	}
	
	.intro-icon {
		width: 50rpx;
		height: 50rpx;
		filter: brightness(0) invert(1);
	}
	
	.intro-titles {
		flex: 1;
	}
	
	.subtitle-wrapper {
		display: block;
		width: 100%;
	}
	
	.intro-title {
		font-size: 34rpx;
		color: #333;
		font-weight: 600;
		margin-bottom: 12rpx;
		letter-spacing: 1px;
	}
	
	.intro-subtitle {
		font-size: 22rpx;
		color: #666;
		letter-spacing: 0.5px;
		line-height: 1.3;
		display: block;
	}
	
	.intro-stats {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 24rpx;
		background-color: rgba(248, 249, 252, 0.8);
		border-radius: 16rpx;
		padding: 24rpx 20rpx;
		box-shadow: 
			0 4rpx 12rpx rgba(0, 0, 0, 0.05),
			0 1rpx 0 rgba(255, 255, 255, 1) inset;
		border: 1px solid rgba(255, 255, 255, 0.7);
	}
	
	.stat-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 0 10rpx;
	}
	
	.stat-num {
		font-size: 32rpx;
		color: #1677ff;
		font-weight: 600;
		margin-bottom: 10rpx;
		text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
	}
	
	.stat-label {
		font-size: 24rpx;
		color: #666;
		letter-spacing: 1px;
	}
	
	.stat-divider {
		width: 2px;
		height: 44rpx;
		background-color: rgba(0, 0, 0, 0.08);
		margin: 0 15rpx;
	}
	
	/* 表单样式 */
	.form-section {
		margin-bottom: 30rpx;
	}
	
	.form-title {
		font-size: 30rpx;
		color: #333;
		font-weight: 600;
		margin-bottom: 24rpx;
		display: block;
	}
	
	.form-item {
		margin-bottom: 24rpx;
	}
	
	.form-label {
		font-size: 26rpx;
		color: #666;
		margin-bottom: 12rpx;
		display: block;
	}
	
	.form-input {
		height: 80rpx;
		background-color: rgba(248, 249, 252, 0.6);
		border-radius: 12rpx;
		padding: 0 24rpx;
		font-size: 26rpx;
		color: #333;
		border: 1px solid rgba(0, 0, 0, 0.05);
	}
	
	.address-wrap {
		display: flex;
		position: relative;
	}
	
	.address-input {
		padding-right: 90rpx;
		flex: 1;
	}
	
	.location-btn {
		position: absolute;
		right: 0;
		top: 0;
		height: 80rpx;
		width: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: linear-gradient(135deg, #1677ff, #0052cc);
		border-radius: 0 12rpx 12rpx 0;
		z-index: 5;
	}
	
	.location-icon-img {
		width: 32rpx;
		height: 32rpx;
		filter: brightness(0) invert(1);
	}
	
	.form-select {
		height: 80rpx;
		background-color: rgba(248, 249, 252, 0.6);
		border-radius: 12rpx;
		padding: 0 24rpx;
		font-size: 26rpx;
		color: #333;
		border: 1px solid rgba(0, 0, 0, 0.05);
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	
	.placeholder {
		color: #999;
	}
	
	.select-arrow-img {
		width: 24rpx;
		height: 24rpx;
		opacity: 0.6;
	}
	
	.form-textarea {
		height: 160rpx;
		background-color: rgba(248, 249, 252, 0.6);
		border-radius: 12rpx;
		padding: 24rpx;
		font-size: 26rpx;
		color: #333;
		border: 1px solid rgba(0, 0, 0, 0.05);
		width: 100%;
		box-sizing: border-box;
	}
	
	/* 入驻优势 */
	.benefits-section {
		margin-bottom: 30rpx;
	}
	
	.benefits-title {
		font-size: 30rpx;
		color: #333;
		font-weight: 600;
		margin-bottom: 24rpx;
		display: block;
	}
	
	.benefits-list {
		display: flex;
		flex-direction: column;
	}
	
	.benefit-item {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		background-color: rgba(248, 249, 252, 0.6);
		border-radius: 12rpx;
		padding: 16rpx 20rpx;
	}
	
	.benefit-item:last-child {
		margin-bottom: 0;
	}
	
	.benefit-icon-wrap {
		width: 64rpx;
		height: 64rpx;
		border-radius: 16rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: rgba(22, 119, 255, 0.1);
		margin-right: 16rpx;
	}
	
	.benefit-icon-img {
		width: 32rpx;
		height: 32rpx;
	}
	
	.benefit-content {
		flex: 1;
	}
	
	.benefit-name {
		font-size: 26rpx;
		color: #333;
		font-weight: 600;
		margin-bottom: 4rpx;
	}
	
	.benefit-desc {
		font-size: 22rpx;
		color: #666;
	}
	
	/* 提交按钮 */
	.submit-section {
		margin-top: 40rpx;
		margin-bottom: 60rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.submit-btn {
		width: 90%;
		height: 88rpx;
		background: linear-gradient(135deg, #1677ff, #0052cc);
		border-radius: 44rpx;
		color: #ffffff;
		font-size: 30rpx;
		font-weight: 600;
		margin-bottom: 20rpx;
		box-shadow: 0 8rpx 16rpx rgba(22, 119, 255, 0.2);
	}
	
	.agreement-text {
		font-size: 24rpx;
		color: #999;
	}
	
	.agreement-link {
		color: #1677ff;
	}
	
	/* 弹窗样式 */
	.popup-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 100;
		backdrop-filter: blur(3px);
		-webkit-backdrop-filter: blur(3px);
	}
	
	.popup-content {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #fff;
		z-index: 101;
		border-radius: 24rpx 24rpx 0 0;
		padding-bottom: env(safe-area-inset-bottom);
		max-height: 70vh;
		display: flex;
		flex-direction: column;
		box-shadow: 0 -10rpx 30rpx rgba(0, 0, 0, 0.1);
		animation: slideUp 0.3s ease-out;
	}
	
	@keyframes slideUp {
		from {
			transform: translateY(100%);
		}
		to {
			transform: translateY(0);
		}
	}
	
	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 30rpx;
		border-bottom: 1px solid rgba(0, 0, 0, 0.05);
	}
	
	.popup-title {
		font-size: 30rpx;
		font-weight: 600;
		color: #333;
	}
	
	.popup-close {
		width: 56rpx;
		height: 56rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		background-color: rgba(0, 0, 0, 0.05);
	}
	
	.close-icon-img {
		width: 28rpx;
		height: 28rpx;
		opacity: 0.6;
	}
	
	.category-list {
		max-height: calc(70vh - 90rpx);
		padding: 0 30rpx;
	}
	
	.category-list .category-item {
		height: 100rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid rgba(0, 0, 0, 0.05);
		font-size: 28rpx;
		color: #333;
	}
	
	.category-list .category-item.active {
		color: #1677ff;
	}
	
	.check-icon-img {
		width: 36rpx;
		height: 36rpx;
	}
	
	.location-tips {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 8rpx;
		padding: 0 8rpx;
	}
	
	.location-text {
		font-size: 22rpx;
		color: #0052cc;
	}
	
	.location-btn-text {
		font-size: 22rpx;
		color: #0052cc;
		padding: 4rpx 12rpx;
		background-color: rgba(0, 82, 204, 0.05);
		border-radius: 10rpx;
	}
	
	/* 上传图片样式 */
	.upload-box {
		width: 200rpx;
		height: 200rpx;
		background-color: rgba(248, 249, 252, 0.6);
		border-radius: 12rpx;
		border: 1px dashed rgba(0, 0, 0, 0.1);
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		margin-top: 16rpx;
	}
	
	.upload-placeholder {
		width: 80rpx;
		height: 80rpx;
		background-color: rgba(0, 82, 204, 0.05);
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 12rpx;
	}
	
	.plus-icon {
		font-size: 44rpx;
		color: #1677ff;
		font-weight: 200;
	}
	
	.upload-desc-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-top: 8rpx;
	}
	
	.upload-desc {
		font-size: 22rpx;
		color: #999;
		line-height: 32rpx;
		text-align: center;
	}
	
	/* 入驻方式选择 */
	.join-methods {
		margin-top: 20rpx;
	}
	
	.join-option {
		display: flex;
		padding: 20rpx;
		border-radius: 16rpx;
		background-color: rgba(255, 255, 255, 0.5);
		border: 1px solid rgba(0, 0, 0, 0.05);
		margin-bottom: 20rpx;
		transition: all 0.3s ease;
		position: relative;
		overflow: hidden;
	}
	
	.join-option.active {
		background-color: rgba(22, 119, 255, 0.05);
		border: 1px solid rgba(22, 119, 255, 0.3);
		transform: translateY(-4rpx);
		box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.06);
	}
	
	.option-radio {
		width: 36rpx;
		height: 36rpx;
		border-radius: 50%;
		border: 2px solid #e0e0e0;
		margin-right: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.join-option.active .option-radio {
		border-color: #1677ff;
	}
	
	.radio-dot {
		width: 20rpx;
		height: 20rpx;
		border-radius: 50%;
		background-color: #1677ff;
	}
	
	.option-content {
		flex: 1;
	}
	
	.option-title-row {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
	}
	
	.option-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 10rpx;
	}
	
	.option-title {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
		margin-right: 10rpx;
	}
	
	.option-desc {
		font-size: 24rpx;
		color: #666;
	}
	
	.option-badge {
		padding: 2rpx 12rpx;
		background-color: #e6f7ff;
		border-radius: 8rpx;
		font-size: 20rpx;
		color: #1677ff;
	}
	
	.premium-badge {
		background-color: #fff7e6;
		color: #fa8c16;
	}
	
	/* 特权列表 */
	.privilege-list {
		margin-top: 30rpx;
		padding: 24rpx;
		background-color: rgba(248, 249, 252, 0.6);
		border-radius: 16rpx;
		border: 1px dashed rgba(22, 119, 255, 0.3);
	}
	
	.privilege-title {
		font-size: 26rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 16rpx;
	}
	
	.privilege-items {
		margin-bottom: 20rpx;
	}
	
	.privilege-item {
		display: flex;
		align-items: center;
		margin-bottom: 12rpx;
	}
	
	.privilege-dot {
		width: 8rpx;
		height: 8rpx;
		border-radius: 4rpx;
		background-color: #1677ff;
		margin-right: 12rpx;
	}
	
	.privilege-text {
		font-size: 24rpx;
		color: #666;
	}
	
	.watch-ad-button {
		background: linear-gradient(135deg, #1677ff, #0052cc);
		border-radius: 44rpx;
		height: 80rpx;
		font-size: 28rpx;
		color: #fff;
		font-weight: 500;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 20rpx;
		box-shadow: 0 8rpx 16rpx rgba(22, 119, 255, 0.2);
	}
	
	.btn-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 10rpx;
	}
	
	/* 已选版本展示 */
	.selected-plan {
		margin-top: 20rpx;
		padding: 24rpx;
		border-radius: 20rpx;
		background-color: #f8f9fc;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
		width: 92%; /* 从88%增加到92% */
		margin-left: auto;
		margin-right: auto;
		border: 1px solid rgba(230, 235, 245, 0.8);
	}
	
	.apple-card {
		background-color: rgba(255, 255, 255, 0.85);
		backdrop-filter: blur(10px);
		box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
		border: 1px solid rgba(255, 255, 255, 0.5);
	}
	
	.plan-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 14rpx; /* 减小底部间距 */
	}
	
	.plan-badge {
		padding: 6rpx 18rpx;
		border-radius: 14rpx;
		font-size: 22rpx;
		font-weight: 600;
		color: #fff;
	}
	
	.basic-badge {
		background-color: rgba(33, 150, 243, 0.8);
		color: #ffffff;
		border: 1px solid rgba(33, 150, 243, 0.2);
	}
	
	.premium-badge {
		background-color: rgba(255, 152, 0, 0.8);
		color: #ffffff;
		border: 1px solid rgba(255, 152, 0, 0.2);
	}
	
	.deluxe-badge {
		background-color: rgba(156, 39, 176, 0.8);
		color: #ffffff;
		border: 1px solid rgba(156, 39, 176, 0.2);
	}
	
	.plan-price-row {
		display: flex;
		align-items: baseline;
		margin-bottom: 12rpx;
	}
	
	.plan-price-label {
		font-size: 24rpx;
		font-weight: 500;
		color: #333;
	}
	
	.plan-price-value {
		font-size: 40rpx; /* 减小字体大小 */
		font-weight: 700;
		color: #333;
		margin: 0 4rpx;
	}
	
	.plan-price-cycle {
		font-size: 22rpx;
		color: #666;
	}
	
	.change-plan {
		font-size: 24rpx;
		color: #007aff;
		padding: 6rpx 12rpx;
		background-color: rgba(0, 122, 255, 0.1);
		border-radius: 12rpx;
	}
	
	.plan-divider {
		height: 1px;
		background-color: rgba(0, 0, 0, 0.05);
		margin: 12rpx 0 16rpx;
	}
	
	.plan-features {
		margin-bottom: 16rpx;
	}
	
	.plan-feature {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;
		font-size: 24rpx;
	}
	
	.feature-dot {
		width: 8rpx;
		height: 8rpx;
		border-radius: 50%;
		background-color: #1677ff;
		margin-right: 10rpx;
	}
	
	.feature-text {
		color: #666;
		font-size: 24rpx;
	}
	
	.select-other-btn {
		margin-top: 10rpx;
		background-color: rgba(0, 122, 255, 0.08);
		border-radius: 14rpx;
		height: 64rpx;
		line-height: 64rpx;
		color: #007aff;
		font-size: 24rpx;
		font-weight: 500;
		display: flex;
		align-items: center;
		justify-content: center;
		border: 1px solid rgba(0, 122, 255, 0.2);
	}
	
	/* 付费入驻版本弹窗 */
	.version-popup {
		padding-bottom: 100rpx;
		max-height: 75vh; /* 减小最大高度 */
		border-radius: 30rpx 30rpx 0 0;
		box-shadow: 0 -10rpx 30rpx rgba(0, 0, 0, 0.1);
		overflow: hidden;
		background-color: #f5f5f7;
	}
	
	.version-list {
		padding: 20rpx 10rpx; /* 调整左右内边距 */
		max-height: 50vh;
		overflow-y: auto;
		-webkit-overflow-scrolling: touch;
	}
	
	.version-card {
		margin-bottom: 24rpx; /* 减小底部间距 */
		border-radius: 24rpx;
		border: none;
		overflow: hidden;
		transition: all 0.3s ease;
		position: relative;
		box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.06);
		width: 85%; /* 从75%增加到85% */
		margin-left: auto;
		margin-right: auto;
		transform: scale(0.98); /* 略微调整默认缩放 */
	}
	
	.version-card.active {
		transform: scale(1);
		box-shadow: 0 12rpx 30rpx rgba(0, 0, 0, 0.12);
	}
	
	.apple-style {
		background-color: transparent;
	}
	
	.version-card-bg {
		position: relative;
		padding: 24rpx; /* 从28rpx减小到24rpx */
		border-radius: 24rpx;
		overflow: hidden;
		height: 100%;
	}
	
	.basic-gradient {
		background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
		border: 1px solid rgba(33, 150, 243, 0.2);
	}
	
	.premium-gradient {
		background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
		border: 1px solid rgba(255, 193, 7, 0.2);
	}
	
	.deluxe-gradient {
		background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
		border: 1px solid rgba(156, 39, 176, 0.2);
	}
	
	.version-badge {
		position: absolute;
		top: 16rpx;
		right: 16rpx;
		background: linear-gradient(135deg, #ff9a00, #ff6a00);
		color: white;
		font-size: 20rpx;
		font-weight: 600;
		padding: 4rpx 16rpx;
		border-radius: 16rpx;
		box-shadow: 0 2rpx 8rpx rgba(255, 106, 0, 0.3);
	}
	
	.badge-text {
		letter-spacing: 1px;
	}
	
	.version-pill {
		display: inline-block;
		margin-bottom: 24rpx;
		padding: 6rpx 20rpx;
		background-color: rgba(255, 255, 255, 0.5);
		border-radius: 20rpx;
		font-size: 24rpx;
		font-weight: 600;
		color: #333;
		backdrop-filter: blur(10rpx);
		border: 1px solid rgba(255, 255, 255, 0.7);
	}
	
	.version-content {
		display: flex;
		flex-direction: column;
	}
	
	.version-price-area {
		margin: 6rpx 0 12rpx; /* 减小上下间距 */
	}
	
	.version-price-label {
		font-size: 26rpx;
		color: #333;
		font-weight: 600;
	}
	
	.version-price-value {
		font-size: 40rpx; /* 减小字体大小 */
		color: #333;
		font-weight: 700;
	}
	
	.version-price-cycle {
		font-size: 22rpx;
		color: #666;
		font-weight: 400;
		margin-left: 2rpx;
	}
	
	.version-desc {
		font-size: 22rpx;
		color: #666;
		margin-bottom: 16rpx; /* 减小底部间距 */
	}
	
	.version-features {
		background-color: rgba(255, 255, 255, 0.6);
		border-radius: 16rpx;
		padding: 16rpx 18rpx; /* 调整内边距 */
		margin-bottom: 20rpx;
		backdrop-filter: blur(8rpx);
		border: 1px solid rgba(255, 255, 255, 0.8);
	}
	
	.version-feature-item {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx; /* 减小底部间距 */
		font-size: 22rpx; /* 减小字体大小 */
	}
	
	.feature-dot {
		width: 6rpx;
		height: 6rpx;
		border-radius: 3rpx;
		background-color: #333;
		margin-right: 10rpx;
	}
	
	.feature-text {
		font-size: 22rpx;
		color: #333;
		line-height: 1.4;
	}
	
	.version-select-btn {
		width: 100%;
		height: 72rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 36rpx;
		background: rgba(0, 122, 255, 0.9);
		color: #fff;
		font-size: 28rpx;
		font-weight: 500;
		box-shadow: 0 6rpx 12rpx rgba(0, 122, 255, 0.2);
		transition: all 0.3s ease;
	}
	
	.version-select-btn.selected {
		background: rgba(0, 98, 204, 0.9);
	}
	
	.premium-select {
		background: rgba(255, 149, 0, 0.9);
		box-shadow: 0 6rpx 12rpx rgba(255, 149, 0, 0.2);
	}
	
	.premium-select.selected {
		background: rgba(230, 134, 0, 0.9);
	}
	
	.deluxe-select {
		background: rgba(175, 82, 222, 0.9);
		box-shadow: 0 6rpx 12rpx rgba(175, 82, 222, 0.2);
	}
	
	.deluxe-select.selected {
		background: rgba(150, 70, 190, 0.9);
	}
	
	.version-popup-footer {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(10px);
		padding: 24rpx 32rpx;
		border-top: 1px solid rgba(0, 0, 0, 0.05);
	}
	
	.confirm-version-btn {
		width: 100%;
		height: 88rpx;
		background: linear-gradient(135deg, #007AFF, #0062CC);
		border-radius: 44rpx;
		color: #ffffff;
		font-size: 30rpx;
		font-weight: 600;
		box-shadow: 0 8rpx 16rpx rgba(0, 122, 255, 0.2);
	}
</style> 