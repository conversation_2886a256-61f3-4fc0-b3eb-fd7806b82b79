// 房屋出租详情模拟数据
export const houseRentDetail = {
  id: 'rent12345',
  title: '市中心精装两室一厅出租',
  price: '1800元/月',
  tags: ['两室一厅', '精装修', '拎包入住', '交通便利', '家电齐全'],
  publishTime: Date.now() - 86400000 * 2, // 2天前
  images: [
    '/static/images/house1.jpg',
    '/static/images/house2.jpg',
    '/static/images/house3.jpg',
    '/static/images/house4.jpg'
  ],
  houseType: '2室1厅1卫',
  area: '85平方米',
  floor: '6楼/18楼',
  orientation: '南北通透',
  decoration: '精装修',
  community: '阳光花园小区',
  address: '磁县中心城区建设路与振兴路交叉口',
  rentType: '整租',
  paymentMethod: '押一付三',
  availableDate: '随时入住',
  features: ['家电齐全', '拎包入住', '南北通透', '采光好', '交通便利', '配套齐全', '有电梯', '有暖气'],
  facilities: ['冰箱', '洗衣机', '电视', '热水器', '空调', '宽带', '衣柜', '床', '沙发', '餐桌'],
  description: '房屋位于市中心地段，交通便利，周边配套设施齐全，有超市、医院、学校等。\n\n小区环境优美，绿化率高，安保严格，居住舒适安全。\n\n房屋南北通透，采光好，通风良好。家具家电齐全，拎包即可入住。\n\n适合小家庭或白领租住，看房方便，欢迎随时联系。',
  requirements: '要求租户爱护房屋设施，不能养宠物，不能进行装修改造，不能从事违法活动。',
  surroundings: {
    traffic: '距离公交站200米，有10路、23路、35路公交车',
    education: '周边有磁县第一小学、磁县第三中学',
    medical: '附近有磁县人民医院',
    shopping: '周边有万家超市、农贸市场',
    entertainment: '附近有健身房、影院'
  },
  publisher: {
    id: 'user12345',
    name: '王先生',
    avatar: '/static/images/avatar.png',
    type: '个人房东',
    isVerified: true
  },
  contact: {
    name: '王先生',
    phone: '13987654321'
  }
};

// 相关房源推荐数据
export const relatedHouses = [
  {
    id: 'house001',
    title: '阳光花园一室一厅精装修',
    price: '1200元/月',
    area: '50平方米',
    houseType: '1室1厅1卫',
    image: '/static/images/house1.jpg',
    tags: ['精装修', '家电齐全', '拎包入住']
  },
  {
    id: 'house002',
    title: '市中心三室两厅出租',
    price: '2500元/月',
    area: '120平方米',
    houseType: '3室2厅2卫',
    image: '/static/images/house2.jpg',
    tags: ['南北通透', '交通便利', '配套齐全']
  },
  {
    id: 'house003',
    title: '学区房两室一厅出租',
    price: '1600元/月',
    area: '75平方米',
    houseType: '2室1厅1卫',
    image: '/static/images/house3.jpg',
    tags: ['学区房', '精装修', '拎包入住']
  }
];

// 获取房屋出租详情的API函数
export const fetchHouseRentDetail = (id) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(houseRentDetail);
    }, 300);
  });
};

// 获取相关房源的API函数
export const fetchRelatedHouses = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(relatedHouses);
    }, 300);
  });
}; 