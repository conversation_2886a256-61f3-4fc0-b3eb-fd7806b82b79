/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.commission-rules-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 88rpx 32rpx 30rpx;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);
}
.navbar-back {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 24rpx;
  height: 24rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}
.navbar-right {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

/* 卡片通用样式 */
.section-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.section-header {
  margin-bottom: 30rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.section-desc {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
  display: block;
}

/* 佣金模式 */
.mode-options {
  display: flex;
  margin: 0 -10rpx;
}
.mode-option {
  flex: 1;
  margin: 0 10rpx;
  background: #F5F7FA;
  border-radius: 20rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  border: 2rpx solid transparent;
}
.mode-option.active {
  border-color: #6B0FBE;
  background: rgba(107, 15, 190, 0.05);
}
.option-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}
.option-icon.percentage {
  background-color: #6B0FBE;
  border-radius: 50%;
  position: relative;
}
.option-icon.percentage::after {
  content: "%";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}
.option-icon.fixed {
  background-color: #FF9500;
  border-radius: 50%;
  position: relative;
}
.option-icon.fixed::after {
  content: "¥";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}
.option-content {
  flex: 1;
}
.option-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}
.option-desc {
  font-size: 24rpx;
  color: #999;
}

/* 佣金等级 */
.commission-levels {
  margin-bottom: 30rpx;
}
.level-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.level-header {
  display: flex;
  align-items: center;
}
.level-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}
.level-icon.level1 {
  background-color: #6B0FBE;
  border-radius: 50%;
  position: relative;
}
.level-icon.level1::after {
  content: "1";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}
.level-icon.level2 {
  background-color: #409EFF;
  border-radius: 50%;
  position: relative;
}
.level-icon.level2::after {
  content: "2";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}
.level-icon.level3 {
  background-color: #67C23A;
  border-radius: 50%;
  position: relative;
}
.level-icon.level3::after {
  content: "3";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}
.level-name {
  font-size: 28rpx;
  color: #333;
}
.level-input-wrap {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 0 20rpx;
  height: 80rpx;
  width: 200rpx;
}
.level-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: #333;
  text-align: right;
}
.input-unit {
  font-size: 28rpx;
  color: #333;
  margin-left: 10rpx;
}

/* 开关项 */
.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.switch-item:last-child {
  margin-bottom: 0;
}
.switch-label {
  flex: 1;
}
.label-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
  display: block;
}
.label-desc {
  font-size: 24rpx;
  color: #999;
}

/* 规则项 */
.rule-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.rule-label {
  flex: 1;
}
.rule-value {
  width: 200rpx;
}
.picker-value {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-size: 28rpx;
  color: #333;
}
.arrow-icon {
  width: 16rpx;
  height: 16rpx;
  border-right: 2rpx solid #999;
  border-bottom: 2rpx solid #999;
  transform: rotate(45deg);
  margin-left: 10rpx;
}
.rule-input {
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 0 20rpx;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
  text-align: center;
}

/* 限制输入 */
.limit-input {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
}
.limit-label {
  font-size: 28rpx;
  color: #333;
}
.input-wrap {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 0 20rpx;
  height: 80rpx;
  width: 200rpx;
}
.currency {
  font-size: 28rpx;
  color: #333;
  margin-right: 10rpx;
}
.limit-value {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: #333;
}

/* 保存按钮 */
.save-section {
  margin: 30rpx;
}
.save-btn {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 32rpx;
  padding: 20rpx 0;
  line-height: 1.5;
  width: 100%;
}