/*!
  * vue-i18n v9.1.7
  * (c) 2021 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
var VueI18n=function(e,t){"use strict";const a="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag,n=e=>a?Symbol(e):e,r=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),l=e=>"number"==typeof e&&isFinite(e),o=e=>"[object RegExp]"===v(e),s=e=>k(e)&&0===Object.keys(e).length;function i(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const c=Object.assign;function u(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const m=Object.prototype.hasOwnProperty;function f(e,t){return m.call(e,t)}const g=Array.isArray,p=e=>"function"==typeof e,b=e=>"string"==typeof e,d=e=>"boolean"==typeof e,_=e=>null!==e&&"object"==typeof e,h=Object.prototype.toString,v=e=>h.call(e),k=e=>"[object Object]"===v(e),F=[];F[0]={w:[0],i:[3,0],"[":[4],o:[7]},F[1]={w:[1],".":[2],"[":[4],o:[7]},F[2]={w:[2],i:[3,0],0:[3,0]},F[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},F[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},F[5]={"'":[4,0],o:8,l:[5,0]},F[6]={'"':[4,0],o:8,l:[6,0]};const y=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function w(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function M(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(y.test(t)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t)}const L=new Map;function W(e,t){if(!_(e))return null;let a=L.get(t);if(a||(a=function(e){const t=[];let a,n,r,l,o,s,i,c=-1,u=0,m=0;const f=[];function g(){const t=e[c+1];if(5===u&&"'"===t||6===u&&'"'===t)return c++,r="\\"+t,f[0](),!0}for(f[0]=()=>{void 0===n?n=r:n+=r},f[1]=()=>{void 0!==n&&(t.push(n),n=void 0)},f[2]=()=>{f[0](),m++},f[3]=()=>{if(m>0)m--,u=4,f[0]();else{if(m=0,void 0===n)return!1;if(n=M(n),!1===n)return!1;f[1]()}};null!==u;)if(c++,a=e[c],"\\"!==a||!g()){if(l=w(a),i=F[u],o=i[l]||i.l||8,8===o)return;if(u=o[0],void 0!==o[1]&&(s=f[o[1]],s&&(r=a,!1===s())))return;if(7===u)return t}}(t),a&&L.set(t,a)),!a)return null;const n=a.length;let r=e,l=0;for(;l<n;){const e=r[a[l]];if(void 0===e)return null;r=e,l++}return r}function I(e){if(!_(e))return e;for(const t in e)if(f(e,t))if(t.includes(".")){const a=t.split("."),n=a.length-1;let r=e;for(let e=0;e<n;e++)a[e]in r||(r[a[e]]={}),r=r[a[e]];r[a[n]]=e[t],delete e[t],_(r[a[n]])&&I(r[a[n]])}else _(e[t])&&I(e[t]);return e}const T=e=>e,$=e=>"",S=e=>0===e.length?"":e.join(""),C=e=>null==e?"":g(e)||k(e)&&e.toString===h?JSON.stringify(e,null,2):String(e);function E(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function O(e={}){const t=e.locale,a=function(e){const t=l(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(l(e.named.count)||l(e.named.n))?l(e.named.count)?e.named.count:l(e.named.n)?e.named.n:t:t}(e),n=_(e.pluralRules)&&b(t)&&p(e.pluralRules[t])?e.pluralRules[t]:E,r=_(e.pluralRules)&&b(t)&&p(e.pluralRules[t])?E:void 0,o=e.list||[],s=e.named||{};l(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(a,s);function i(t){const a=p(e.messages)?e.messages(t):!!_(e.messages)&&e.messages[t];return a||(e.parent?e.parent.message(t):$)}const c=k(e.processor)&&p(e.processor.normalize)?e.processor.normalize:S,u=k(e.processor)&&p(e.processor.interpolate)?e.processor.interpolate:C,m={list:e=>o[e],named:e=>s[e],plural:e=>e[n(a,e.length,r)],linked:(t,a)=>{const n=i(t)(m);return b(a)?(r=a,e.modifiers?e.modifiers[r]:T)(n):n;var r},message:i,type:k(e.processor)&&b(e.processor.type)?e.processor.type:"text",interpolate:u,normalize:c};return m}let N=0;function P(e={}){const t=b(e.version)?e.version:"9.1.7",a=b(e.locale)?e.locale:"en-US",n=g(e.fallbackLocale)||k(e.fallbackLocale)||b(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:a,r=k(e.messages)?e.messages:{[a]:{}},l=k(e.datetimeFormats)?e.datetimeFormats:{[a]:{}},s=k(e.numberFormats)?e.numberFormats:{[a]:{}},u=c({},e.modifiers||{},{upper:e=>b(e)?e.toUpperCase():e,lower:e=>b(e)?e.toLowerCase():e,capitalize:e=>b(e)?`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`:e}),m=e.pluralRules||{},f=p(e.missing)?e.missing:null,h=!d(e.missingWarn)&&!o(e.missingWarn)||e.missingWarn,v=!d(e.fallbackWarn)&&!o(e.fallbackWarn)||e.fallbackWarn,F=!!e.fallbackFormat,y=!!e.unresolving,w=p(e.postTranslation)?e.postTranslation:null,M=k(e.processor)?e.processor:null,L=!d(e.warnHtmlMessage)||e.warnHtmlMessage,W=!!e.escapeParameter,I=p(e.messageCompiler)?e.messageCompiler:undefined,T=p(e.onWarn)?e.onWarn:i,$=e,S=_($.__datetimeFormatters)?$.__datetimeFormatters:new Map,C=_($.__numberFormatters)?$.__numberFormatters:new Map,E=_($.__meta)?$.__meta:{};N++;return{version:t,cid:N,locale:a,fallbackLocale:n,messages:r,datetimeFormats:l,numberFormats:s,modifiers:u,pluralRules:m,missing:f,missingWarn:h,fallbackWarn:v,fallbackFormat:F,unresolving:y,postTranslation:w,processor:M,warnHtmlMessage:L,escapeParameter:W,messageCompiler:I,onWarn:T,__datetimeFormatters:S,__numberFormatters:C,__meta:E}}function H(e,t,a,n,r){const{missing:l}=e;if(null!==l){const n=l(e,a,t,r);return b(n)?n:t}return t}function j(e,t,a){const n=e;n.__localeChainCache||(n.__localeChainCache=new Map);let r=n.__localeChainCache.get(a);if(!r){r=[];let e=[a];for(;g(e);)e=R(r,e,t);const l=g(t)?t:k(t)?t.default?t.default:null:t;e=b(l)?[l]:l,g(e)&&R(r,e,!1),n.__localeChainCache.set(a,r)}return r}function R(e,t,a){let n=!0;for(let r=0;r<t.length&&d(n);r++){b(t[r])&&(n=D(e,t[r],a))}return n}function D(e,t,a){let n;const r=t.split("-");do{n=x(e,r.join("-"),a),r.splice(-1,1)}while(r.length&&!0===n);return n}function x(e,t,a){let n=!1;if(!e.includes(t)&&(n=!0,t)){n="!"!==t[t.length-1];const r=t.replace(/!/g,"");e.push(r),(g(a)||k(a))&&a[r]&&(n=a[r])}return n}function U(e,t,a){e.__localeChainCache=new Map,j(e,a,t)}const V=()=>"",z=e=>p(e);function A(e,...t){const{fallbackFormat:a,postTranslation:n,unresolving:r,fallbackLocale:o,messages:s}=e,[i,c]=q(...t),m=(d(c.missingWarn),d(c.fallbackWarn),d(c.escapeParameter)?c.escapeParameter:e.escapeParameter),f=!!c.resolvedMessage,h=b(c.default)||d(c.default)?d(c.default)?i:c.default:a?i:"",v=a||""!==h,k=b(c.locale)?c.locale:e.locale;m&&function(e){g(e.list)?e.list=e.list.map((e=>b(e)?u(e):e)):_(e.named)&&Object.keys(e.named).forEach((t=>{b(e.named[t])&&(e.named[t]=u(e.named[t]))}))}(c);let[F,y,w]=f?[i,k,s[k]||{}]:function(e,t,a,n,r,l){const{messages:o}=e,s=j(e,n,a);let i,c={},u=null;const m="translate";for(let a=0;a<s.length&&(i=s[a],c=o[i]||{},null===(u=W(c,t))&&(u=c[t]),!b(u)&&!p(u));a++){const a=H(e,t,i,0,m);a!==t&&(u=a)}return[u,i,c]}(e,i,k,o),M=i;if(f||b(F)||z(F)||v&&(F=h,M=F),!(f||(b(F)||z(F))&&b(y)))return r?-1:i;let L=!1;const I=z(F)?F:J(e,i,y,F,M,(()=>{L=!0}));if(L)return F;const T=function(e,t,a){return t(a)}(0,I,O(function(e,t,a,n){const{modifiers:r,pluralRules:o}=e,s={locale:t,modifiers:r,pluralRules:o,messages:n=>{const r=W(a,n);if(b(r)){let a=!1;const l=J(e,n,t,r,n,(()=>{a=!0}));return a?V:l}return z(r)?r:V}};e.processor&&(s.processor=e.processor);n.list&&(s.list=n.list);n.named&&(s.named=n.named);l(n.plural)&&(s.pluralIndex=n.plural);return s}(e,y,w,c)));return n?n(T):T}function J(e,t,a,n,l,o){const{messageCompiler:s,warnHtmlMessage:i}=e;if(z(n)){const e=n;return e.locale=e.locale||a,e.key=e.key||t,e}const c=s(n,function(e,t,a,n,l,o){return{warnHtmlMessage:l,onError:e=>{throw o&&o(e),e},onCacheKey:e=>((e,t,a)=>r({l:e,k:t,s:a}))(t,a,e)}}(0,a,l,0,i,o));return c.locale=a,c.key=t,c.source=n,c}function q(...e){const[t,a,n]=e,r={};if(!b(t)&&!l(t)&&!z(t))throw Error(14);const o=l(t)?String(t):(z(t),t);return l(a)?r.plural=a:b(a)?r.default=a:k(a)&&!s(a)?r.named=a:g(a)&&(r.list=a),l(n)?r.plural=n:b(n)?r.default=n:k(n)&&c(r,n),[o,r]}function B(e,...t){const{datetimeFormats:a,unresolving:n,fallbackLocale:r}=e,{__datetimeFormatters:l}=e,[o,i,u,m]=G(...t);d(u.missingWarn);d(u.fallbackWarn);const f=!!u.part,g=b(u.locale)?u.locale:e.locale,p=j(e,r,g);if(!b(o)||""===o)return new Intl.DateTimeFormat(g).format(i);let _,h={},v=null;for(let t=0;t<p.length&&(_=p[t],h=a[_]||{},v=h[o],!k(v));t++)H(e,o,_,0,"datetime format");if(!k(v)||!b(_))return n?-1:o;let F=`${_}__${o}`;s(m)||(F=`${F}__${JSON.stringify(m)}`);let y=l.get(F);return y||(y=new Intl.DateTimeFormat(_,c({},v,m)),l.set(F,y)),f?y.formatToParts(i):y.format(i)}function G(...e){const[t,a,n,r]=e;let o,s={},i={};if(b(t)){if(!/\d{4}-\d{2}-\d{2}(T.*)?/.test(t))throw Error(16);o=new Date(t);try{o.toISOString()}catch(e){throw Error(16)}}else if("[object Date]"===v(t)){if(isNaN(t.getTime()))throw Error(15);o=t}else{if(!l(t))throw Error(14);o=t}return b(a)?s.key=a:k(a)&&(s=a),b(n)?s.locale=n:k(n)&&(i=n),k(r)&&(i=r),[s.key||"",o,s,i]}function Y(e,t,a){const n=e;for(const e in a){const a=`${t}__${e}`;n.__datetimeFormatters.has(a)&&n.__datetimeFormatters.delete(a)}}function Z(e,...t){const{numberFormats:a,unresolving:n,fallbackLocale:r}=e,{__numberFormatters:l}=e,[o,i,u,m]=K(...t);d(u.missingWarn);d(u.fallbackWarn);const f=!!u.part,g=b(u.locale)?u.locale:e.locale,p=j(e,r,g);if(!b(o)||""===o)return new Intl.NumberFormat(g).format(i);let _,h={},v=null;for(let t=0;t<p.length&&(_=p[t],h=a[_]||{},v=h[o],!k(v));t++)H(e,o,_,0,"number format");if(!k(v)||!b(_))return n?-1:o;let F=`${_}__${o}`;s(m)||(F=`${F}__${JSON.stringify(m)}`);let y=l.get(F);return y||(y=new Intl.NumberFormat(_,c({},v,m)),l.set(F,y)),f?y.formatToParts(i):y.format(i)}function K(...e){const[t,a,n,r]=e;let o={},s={};if(!l(t))throw Error(14);const i=t;return b(a)?o.key=a:k(a)&&(o=a),b(n)?o.locale=n:k(n)&&(s=n),k(r)&&(s=r),[o.key||"",i,o,s]}function Q(e,t,a){const n=e;for(const e in a){const a=`${t}__${e}`;n.__numberFormatters.has(a)&&n.__numberFormatters.delete(a)}}const X="9.1.7",ee=n("__transrateVNode"),te=n("__datetimeParts"),ae=n("__numberParts"),ne=n("__setPluralRules");let re=0;function le(e){return(a,n,r,l)=>e(n,r,t.getCurrentInstance()||void 0,l)}function oe(e,t){const{messages:a,__i18n:n}=t,r=k(a)?a:g(n)?{}:{[e]:{}};if(g(n)&&n.forEach((({locale:e,resource:t})=>{e?(r[e]=r[e]||{},ie(t,r[e])):ie(t,r)})),t.flatJson)for(const e in r)f(r,e)&&I(r[e]);return r}const se=e=>!_(e)||g(e);function ie(e,t){if(se(e)||se(t))throw Error(20);for(const a in e)f(e,a)&&(se(e[a])||se(t[a])?t[a]=e[a]:ie(e[a],t[a]))}function ce(e={}){const{__root:a}=e,n=void 0===a;let r=!d(e.inheritLocale)||e.inheritLocale;const s=t.ref(a&&r?a.locale.value:b(e.locale)?e.locale:"en-US"),i=t.ref(a&&r?a.fallbackLocale.value:b(e.fallbackLocale)||g(e.fallbackLocale)||k(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:s.value),u=t.ref(oe(s.value,e)),m=t.ref(k(e.datetimeFormats)?e.datetimeFormats:{[s.value]:{}}),f=t.ref(k(e.numberFormats)?e.numberFormats:{[s.value]:{}});let h=a?a.missingWarn:!d(e.missingWarn)&&!o(e.missingWarn)||e.missingWarn,v=a?a.fallbackWarn:!d(e.fallbackWarn)&&!o(e.fallbackWarn)||e.fallbackWarn,F=a?a.fallbackRoot:!d(e.fallbackRoot)||e.fallbackRoot,y=!!e.fallbackFormat,w=p(e.missing)?e.missing:null,M=p(e.missing)?le(e.missing):null,L=p(e.postTranslation)?e.postTranslation:null,I=!d(e.warnHtmlMessage)||e.warnHtmlMessage,T=!!e.escapeParameter;const $=a?a.modifiers:k(e.modifiers)?e.modifiers:{};let S,C=e.pluralRules||a&&a.pluralRules;S=P({version:X,locale:s.value,fallbackLocale:i.value,messages:u.value,datetimeFormats:m.value,numberFormats:f.value,modifiers:$,pluralRules:C,missing:null===M?void 0:M,missingWarn:h,fallbackWarn:v,fallbackFormat:y,unresolving:!0,postTranslation:null===L?void 0:L,warnHtmlMessage:I,escapeParameter:T,__datetimeFormatters:k(S)?S.__datetimeFormatters:void 0,__numberFormatters:k(S)?S.__numberFormatters:void 0,__v_emitter:k(S)?S.__v_emitter:void 0,__meta:{framework:"vue"}}),U(S,s.value,i.value);const E=t.computed({get:()=>s.value,set:e=>{s.value=e,S.locale=s.value}}),O=t.computed({get:()=>i.value,set:e=>{i.value=e,S.fallbackLocale=i.value,U(S,s.value,e)}}),N=t.computed((()=>u.value)),H=t.computed((()=>m.value)),R=t.computed((()=>f.value));function D(e,t,n,r,o,s){let i;if(i=e(S),l(i)&&-1===i){const[e,n]=t();return a&&F?r(a):o(e)}if(s(i))return i;throw Error(14)}function x(...e){return D((t=>A(t,...e)),(()=>q(...e)),0,(t=>t.t(...e)),(e=>e),(e=>b(e)))}const V={normalize:function(e){return e.map((e=>b(e)?t.createVNode(t.Text,null,e,0):e))},interpolate:e=>e,type:"vnode"};function z(e){return u.value[e]||{}}re++,a&&(t.watch(a.locale,(e=>{r&&(s.value=e,S.locale=e,U(S,s.value,i.value))})),t.watch(a.fallbackLocale,(e=>{r&&(i.value=e,S.fallbackLocale=e,U(S,s.value,i.value))})));return{id:re,locale:E,fallbackLocale:O,get inheritLocale(){return r},set inheritLocale(e){r=e,e&&a&&(s.value=a.locale.value,i.value=a.fallbackLocale.value,U(S,s.value,i.value))},get availableLocales(){return Object.keys(u.value).sort()},messages:N,datetimeFormats:H,numberFormats:R,get modifiers(){return $},get pluralRules(){return C||{}},get isGlobal(){return n},get missingWarn(){return h},set missingWarn(e){h=e,S.missingWarn=h},get fallbackWarn(){return v},set fallbackWarn(e){v=e,S.fallbackWarn=v},get fallbackRoot(){return F},set fallbackRoot(e){F=e},get fallbackFormat(){return y},set fallbackFormat(e){y=e,S.fallbackFormat=y},get warnHtmlMessage(){return I},set warnHtmlMessage(e){I=e,S.warnHtmlMessage=e},get escapeParameter(){return T},set escapeParameter(e){T=e,S.escapeParameter=e},t:x,rt:function(...e){const[t,a,n]=e;if(n&&!_(n))throw Error(15);return x(t,a,c({resolvedMessage:!0},n||{}))},d:function(...e){return D((t=>B(t,...e)),(()=>G(...e)),0,(t=>t.d(...e)),(()=>""),(e=>b(e)))},n:function(...e){return D((t=>Z(t,...e)),(()=>K(...e)),0,(t=>t.n(...e)),(()=>""),(e=>b(e)))},te:function(e,t){return null!==W(z(b(t)?t:s.value),e)},tm:function(e){const t=function(e){let t=null;const a=j(S,i.value,s.value);for(let n=0;n<a.length;n++){const r=W(u.value[a[n]]||{},e);if(null!=r){t=r;break}}return t}(e);return null!=t?t:a&&a.tm(e)||{}},getLocaleMessage:z,setLocaleMessage:function(e,t){u.value[e]=t,S.messages=u.value},mergeLocaleMessage:function(e,t){u.value[e]=u.value[e]||{},ie(t,u.value[e]),S.messages=u.value},getDateTimeFormat:function(e){return m.value[e]||{}},setDateTimeFormat:function(e,t){m.value[e]=t,S.datetimeFormats=m.value,Y(S,e,t)},mergeDateTimeFormat:function(e,t){m.value[e]=c(m.value[e]||{},t),S.datetimeFormats=m.value,Y(S,e,t)},getNumberFormat:function(e){return f.value[e]||{}},setNumberFormat:function(e,t){f.value[e]=t,S.numberFormats=f.value,Q(S,e,t)},mergeNumberFormat:function(e,t){f.value[e]=c(f.value[e]||{},t),S.numberFormats=f.value,Q(S,e,t)},getPostTranslationHandler:function(){return p(L)?L:null},setPostTranslationHandler:function(e){L=e,S.postTranslation=e},getMissingHandler:function(){return w},setMissingHandler:function(e){null!==e&&(M=le(e)),w=e,S.missing=M},[ee]:function(...e){return D((t=>{let a;const n=t;try{n.processor=V,a=A(n,...e)}finally{n.processor=null}return a}),(()=>q(...e)),0,(t=>t[ee](...e)),(e=>[t.createVNode(t.Text,null,e,0)]),(e=>g(e)))},[ae]:function(...e){return D((t=>Z(t,...e)),(()=>K(...e)),0,(t=>t[ae](...e)),(()=>[]),(e=>b(e)||g(e)))},[te]:function(...e){return D((t=>B(t,...e)),(()=>G(...e)),0,(t=>t[te](...e)),(()=>[]),(e=>b(e)||g(e)))},[ne]:function(e){C=e,S.pluralRules=C}}}function ue(e={}){const t=ce(function(e){const t=b(e.locale)?e.locale:"en-US",a=b(e.fallbackLocale)||g(e.fallbackLocale)||k(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,n=p(e.missing)?e.missing:void 0,r=!d(e.silentTranslationWarn)&&!o(e.silentTranslationWarn)||!e.silentTranslationWarn,l=!d(e.silentFallbackWarn)&&!o(e.silentFallbackWarn)||!e.silentFallbackWarn,s=!d(e.fallbackRoot)||e.fallbackRoot,i=!!e.formatFallbackMessages,u=k(e.modifiers)?e.modifiers:{},m=e.pluralizationRules,f=p(e.postTranslation)?e.postTranslation:void 0,_=!b(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,h=!!e.escapeParameterHtml,v=!d(e.sync)||e.sync;let F=e.messages;if(k(e.sharedMessages)){const t=e.sharedMessages;F=Object.keys(t).reduce(((e,a)=>{const n=e[a]||(e[a]={});return c(n,t[a]),e}),F||{})}const{__i18n:y,__root:w}=e;return{locale:t,fallbackLocale:a,messages:F,flatJson:e.flatJson,datetimeFormats:e.datetimeFormats,numberFormats:e.numberFormats,missing:n,missingWarn:r,fallbackWarn:l,fallbackRoot:s,fallbackFormat:i,modifiers:u,pluralRules:m,postTranslation:f,warnHtmlMessage:_,escapeParameter:h,inheritLocale:v,__i18n:y,__root:w}}(e)),a={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get formatter(){return{interpolate:()=>[]}},set formatter(e){},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return d(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=d(e)?!e:e},get silentFallbackWarn(){return d(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=d(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get preserveDirectiveContent(){return!0},set preserveDirectiveContent(e){},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...e){const[a,n,r]=e,l={};let o=null,s=null;if(!b(a))throw Error(15);const i=a;return b(n)?l.locale=n:g(n)?o=n:k(n)&&(s=n),g(r)?o=r:k(r)&&(s=r),t.t(i,o||s||{},l)},rt:(...e)=>t.rt(...e),tc(...e){const[a,n,r]=e,o={plural:1};let s=null,i=null;if(!b(a))throw Error(15);const c=a;return b(n)?o.locale=n:l(n)?o.plural=n:g(n)?s=n:k(n)&&(i=n),b(r)?o.locale=r:g(r)?s=r:k(r)&&(i=r),t.t(c,s||i||{},o)},te:(e,a)=>t.te(e,a),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,a){t.setLocaleMessage(e,a)},mergeLocaleMessage(e,a){t.mergeLocaleMessage(e,a)},d:(...e)=>t.d(...e),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,a){t.setDateTimeFormat(e,a)},mergeDateTimeFormat(e,a){t.mergeDateTimeFormat(e,a)},n:(...e)=>t.n(...e),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,a){t.setNumberFormat(e,a)},mergeNumberFormat(e,a){t.mergeNumberFormat(e,a)},getChoiceIndex:(e,t)=>-1,__onComponentInstanceCreated(t){const{componentInstanceCreatedListener:n}=e;n&&n(t,a)}};return a}const me={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}},fe={name:"i18n-t",props:c({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>l(e)||!isNaN(e)}},me),setup(e,a){const{slots:n,attrs:r}=a,l=e.i18n||ke({useScope:e.scope}),o=Object.keys(n).filter((e=>"_"!==e));return()=>{const n={};e.locale&&(n.locale=e.locale),void 0!==e.plural&&(n.plural=b(e.plural)?+e.plural:e.plural);const s=function({slots:e},t){return 1===t.length&&"default"===t[0]?e.default?e.default():[]:t.reduce(((t,a)=>{const n=e[a];return n&&(t[a]=n()),t}),{})}(a,o),i=l[ee](e.keypath,s,n),u=c({},r);return b(e.tag)||_(e.tag)?t.h(e.tag,u,i):t.h(t.Fragment,u,i)}}};function ge(e,a,n,r){const{slots:l,attrs:o}=a;return()=>{const a={part:!0};let s={};e.locale&&(a.locale=e.locale),b(e.format)?a.key=e.format:_(e.format)&&(b(e.format.key)&&(a.key=e.format.key),s=Object.keys(e.format).reduce(((t,a)=>n.includes(a)?c({},t,{[a]:e.format[a]}):t),{}));const i=r(e.value,a,s);let u=[a.key];g(i)?u=i.map(((e,t)=>{const a=l[e.type];return a?a({[e.type]:e.value,index:t,parts:i}):[e.value]})):b(i)&&(u=[i]);const m=c({},o);return b(e.tag)||_(e.tag)?t.h(e.tag,m,u):t.h(t.Fragment,m,u)}}const pe=["localeMatcher","style","unit","unitDisplay","currency","currencyDisplay","useGrouping","numberingSystem","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","notation","formatMatcher"],be={name:"i18n-n",props:c({value:{type:Number,required:!0},format:{type:[String,Object]}},me),setup(e,t){const a=e.i18n||ke({useScope:"parent"});return ge(e,t,pe,((...e)=>a[ae](...e)))}},de=["dateStyle","timeStyle","fractionalSecondDigits","calendar","dayPeriod","numberingSystem","localeMatcher","timeZone","hour12","hourCycle","formatMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName"],_e={name:"i18n-d",props:c({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},me),setup(e,t){const a=e.i18n||ke({useScope:"parent"});return ge(e,t,de,((...e)=>a[te](...e)))}};function he(e){const t=(t,{instance:a,value:n})=>{if(!a||!a.$)throw Error(22);const r=function(e,t){const a=e;if("composition"===e.mode)return a.__getInstance(t)||e.global;{const n=a.__getInstance(t);return null!=n?n.__composer:e.global.__composer}}(e,a.$),o=function(e){if(b(e))return{path:e};if(k(e)){if(!("path"in e))throw Error(19,"path");return e}throw Error(20)}(n);t.textContent=r.t(...function(e){const{path:t,locale:a,args:n,choice:r,plural:o}=e,s={},i=n||{};b(a)&&(s.locale=a);l(r)&&(s.plural=r);l(o)&&(s.plural=o);return[t,i,s]}(o))};return{beforeMount:t,beforeUpdate:t}}function ve(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[ne](t.pluralizationRules||e.pluralizationRules);const a=oe(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(a).forEach((t=>e.mergeLocaleMessage(t,a[t]))),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach((a=>e.mergeDateTimeFormat(a,t.datetimeFormats[a]))),t.numberFormats&&Object.keys(t.numberFormats).forEach((a=>e.mergeNumberFormat(a,t.numberFormats[a]))),e}function ke(e={}){const a=t.getCurrentInstance();if(null==a)throw Error(16);if(!a.appContext.app.__VUE_I18N_SYMBOL__)throw Error(17);const n=t.inject(a.appContext.app.__VUE_I18N_SYMBOL__);if(!n)throw Error(22);const r="composition"===n.mode?n.global:n.global.__composer,l=s(e)?"__i18n"in a.type?"local":"global":e.useScope?e.useScope:"local";if("global"===l){let t=_(e.messages)?e.messages:{};"__i18nGlobal"in a.type&&(t=oe(r.locale.value,{messages:t,__i18n:a.type.__i18nGlobal}));const n=Object.keys(t);if(n.length&&n.forEach((e=>{r.mergeLocaleMessage(e,t[e])})),_(e.datetimeFormats)){const t=Object.keys(e.datetimeFormats);t.length&&t.forEach((t=>{r.mergeDateTimeFormat(t,e.datetimeFormats[t])}))}if(_(e.numberFormats)){const t=Object.keys(e.numberFormats);t.length&&t.forEach((t=>{r.mergeNumberFormat(t,e.numberFormats[t])}))}return r}if("parent"===l){let e=function(e,t){let a=null;const n=t.root;let r=t.parent;for(;null!=r;){const t=e;if("composition"===e.mode)a=t.__getInstance(r);else{const e=t.__getInstance(r);null!=e&&(a=e.__composer)}if(null!=a)break;if(n===r)break;r=r.parent}return a}(n,a);return null==e&&(e=r),e}if("legacy"===n.mode)throw Error(18);const o=n;let i=o.__getInstance(a);if(null==i){const n=a.type,l=c({},e);n.__i18n&&(l.__i18n=n.__i18n),r&&(l.__root=r),i=ce(l),function(e,a,n){t.onMounted((()=>{}),a),t.onUnmounted((()=>{e.__deleteInstance(a)}),a)}(o,a),o.__setInstance(a,i)}return i}const Fe=["locale","fallbackLocale","availableLocales"],ye=["t","rt","d","n","tm"];return e.DatetimeFormat=_e,e.NumberFormat=be,e.Translation=fe,e.VERSION=X,e.createI18n=function(e={}){const a=!d(e.legacy)||e.legacy,r=!!e.globalInjection,l=new Map,o=a?ue(e):ce(e),s=n(""),i={get mode(){return a?"legacy":"composition"},async install(e,...n){e.__VUE_I18N_SYMBOL__=s,e.provide(e.__VUE_I18N_SYMBOL__,i),!a&&r&&function(e,a){const n=Object.create(null);Fe.forEach((e=>{const r=Object.getOwnPropertyDescriptor(a,e);if(!r)throw Error(22);const l=t.isRef(r.value)?{get:()=>r.value.value,set(e){r.value.value=e}}:{get:()=>r.get&&r.get()};Object.defineProperty(n,e,l)})),e.config.globalProperties.$i18n=n,ye.forEach((t=>{const n=Object.getOwnPropertyDescriptor(a,t);if(!n||!n.value)throw Error(22);Object.defineProperty(e.config.globalProperties,`$${t}`,n)}))}(e,i.global),function(e,t,...a){const n=k(a[0])?a[0]:{},r=!!n.useI18nComponentName;(!d(n.globalInstall)||n.globalInstall)&&(e.component(r?"i18n":fe.name,fe),e.component(be.name,be),e.component(_e.name,_e)),e.directive("t",he(t))}(e,i,...n),a&&e.mixin(function(e,a,n){return{beforeCreate(){const r=t.getCurrentInstance();if(!r)throw Error(22);const l=this.$options;if(l.i18n){const t=l.i18n;l.__i18n&&(t.__i18n=l.__i18n),t.__root=a,this.$i18n=this===this.$root?ve(e,t):ue(t)}else this.$i18n=l.__i18n?this===this.$root?ve(e,l):ue({__i18n:l.__i18n,__root:a}):e;e.__onComponentInstanceCreated(this.$i18n),n.__setInstance(r,this.$i18n),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$tc=(...e)=>this.$i18n.tc(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e)},mounted(){},beforeUnmount(){const e=t.getCurrentInstance();if(!e)throw Error(22);delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,n.__deleteInstance(e),delete this.$i18n}}}(o,o.__composer,i))},get global(){return o},__instances:l,__getInstance:e=>l.get(e)||null,__setInstance(e,t){l.set(e,t)},__deleteInstance(e){l.delete(e)}};return i},e.useI18n=ke,e.vTDirective=he,Object.defineProperty(e,"__esModule",{value:!0}),e}({},Vue);
