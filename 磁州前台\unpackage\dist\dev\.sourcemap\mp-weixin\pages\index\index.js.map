{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\n\t<view class=\"index-container\">\n\t\t<!-- 新增的自定义导航栏 -->\n\t\t<view class=\"custom-nav-bar\" :style=\"{ height: navBarFullHeight + 'px' }\">\n\t\t\t<view class=\"nav-status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\n\t\t\t<view class=\"nav-bar-content\" :style=\"{ height: navBarHeight + 'px' }\">\n\t\t\t\t<text class=\"nav-bar-title\">磁州生活网</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 可滚动的弧形背景 -->\n\t\t<view class=\"content-bg-scroll\"></view>\n\t\t\n\t\t<!-- 页面内容区域 -->\n\t\t<view class=\"scrollable-content\" :style=\"{ paddingTop: navBarFullHeight + 'px' }\">\n\t\t\t<!-- 引导关注公众号模块 -->\n\t\t\t<view class=\"follow-guide-in-bg\" v-if=\"showFollowTip\">\n\t\t\t\t<view class=\"follow-guide-content\">\n\t\t\t\t\t<image class=\"follow-avatar\" src=\"/static/images/cizhou.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t<text class=\"follow-text\">已有10万人关注我们，期待你的加入</text>\n\t\t\t\t\t<view class=\"follow-btn-wrap\">\n\t\t\t\t\t\t<button class=\"follow-btn\" @click=\"showQrCode = true\">立即关注</button>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"follow-close\" @click=\"closeFollowTip\">×</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 轮播图 -->\n\t\t\t<view class=\"banner-outer\">\n\t\t\t\t<swiper class=\"banner-swiper\" \n\t\t\t\t\tindicator-dots \n\t\t\t\t\tindicator-color=\"rgba(255,255,255,0.5)\" \n\t\t\t\t\tindicator-active-color=\"#ffffff\"\n\t\t\t\t\tautoplay \n\t\t\t\t\tcircular \n\t\t\t\t\tinterval=\"3000\" \n\t\t\t\t\tduration=\"500\">\n\t\t\t\t\t<swiper-item v-for=\"(item, index) in bannerList\" :key=\"index\">\n\t\t\t\t\t\t<view class=\"banner-item\">\n\t\t\t\t\t\t\t<image :src=\"item.image\" class=\"banner-image\" mode=\"scaleToFill\" @click=\"navigateTo(item.url)\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</swiper-item>\n\t\t\t\t</swiper>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 白色内容区域，包含所有剩余内容 -->\n\t\t\t<view class=\"white-content\">\n\t\t\t<!-- 服务分类网格 -->\n\t\t\t<ServiceGrid :serviceList=\"serviceList\" />\n\t\t\t\t\n\t\t\t\t<!-- 四宫格特色功能区 -->\n\t\t\t\t<FeatureGrid />\n\t\t\t\t\n\t\t\t\t<!-- 同城资讯 -->\n\t\t\t\t<CityNews />\n\t\t\t\n\t\t\t<!-- 商家推荐 -->\n\t\t\t<MerchantRecommend />\n\t\t\t\n\t\t\t\t<!-- 搜索框 -->\n\t\t\t\t<view class=\"search-container\">\n\t\t\t\t\t<view class=\"search-box\">\n\t\t\t\t\t\t<image src=\"/static/images/icons/search.png\" class=\"search-icon\"></image>\n\t\t\t\t\t\t<input type=\"text\" class=\"search-input\" placeholder=\"搜索信息\" placeholder-style=\"color:#b8bdcc;\" @input=\"onSearchInput\" @confirm=\"doSearch\" confirm-type=\"search\"/>\n\t\t\t\t\t\t<view class=\"search-button\" @click=\"doSearch\"><text>搜索</text></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 磁县同城信息 -->\n\t\t\t<InfoList ref=\"infoList\" :allInfoList=\"allInfoList\" :toppedInfoList=\"toppedInfoList\" :adBanner=\"adBanner\" @tab-change=\"handleTabChange\" />\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 二维码弹出层 -->\n\t\t<view class=\"qrcode-popup\" v-if=\"showQrCode\" @click.stop=\"closeQrCode\">\n\t\t\t<view class=\"qrcode-card\" @click.stop>\n\t\t\t\t<view class=\"qrcode-header\">\n\t\t\t\t\t<text class=\"qrcode-title\">关注公众号</text>\n\t\t\t\t\t<view class=\"close-btn\" @click=\"closeQrCode\">×</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"qrcode-content\">\n\t\t\t\t\t<image src=\"/static/images/tabbar/wxacode.jpg\" class=\"qrcode-image\" mode=\"aspectFit\"></image>\n\t\t\t\t\t<text class=\"qrcode-tips\">扫描二维码，关注公众号</text>\n\t\t\t\t\t<text class=\"qrcode-desc\">获取最新城市资讯、优惠活动</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\t\n\t\t\t<!-- 到家服务全屏子分类弹出层 -->\n\t\t\t<view class=\"service-popup\" v-if=\"showServicePopup\">\n\t\t\t\t<view class=\"service-popup-mask\" @click=\"closeServicePopup\"></view>\n\t\t\t\t<view class=\"service-popup-content\">\n\t\t\t\t\t<view class=\"service-popup-header\">\n\t\t\t\t\t\t<text class=\"service-popup-title\">{{ currentServiceCategory.name }}</text>\n\t\t\t\t\t\t<view class=\"service-popup-close\" @click=\"closeServicePopup\">\n\t\t\t\t\t\t\t<text class=\"uni-icon-close\"></text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<scroll-view class=\"service-popup-scroll\" scroll-y=\"true\">\n\t\t\t\t\t\t<view class=\"service-popup-grid\">\n\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\tclass=\"service-popup-item\" \n\t\t\t\t\t\t\t\tv-for=\"(item, index) in currentServiceCategory.subItems\" \n\t\t\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t\t\t@click=\"navigateToSubService(item)\">\n\t\t\t\t\t\t\t\t<image :src=\"item.icon\" class=\"service-popup-icon\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t\t\t<text class=\"service-popup-name\">{{ item.name }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</scroll-view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 位置选择弹窗 -->\n\t\t\t<view class=\"location-popup\" v-if=\"showLocationPicker\">\n\t\t\t\t<view class=\"location-popup-mask\" @click=\"closeLocationPicker\"></view>\n\t\t\t\t<view class=\"location-popup-content\">\n\t\t\t\t\t<view class=\"location-popup-header\">\n\t\t\t\t\t\t<text class=\"location-popup-title\">选择位置</text>\n\t\t\t\t\t\t<view class=\"location-popup-close\" @click=\"closeLocationPicker\">\n\t\t\t\t\t\t\t<text class=\"uni-icon-close\"></text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"location-list\">\n\t\t\t\t\t\t<view \n\t\t\t\t\t\t\tclass=\"location-item\" \n\t\t\t\t\t\t\tv-for=\"(item, index) in locationList\" \n\t\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t\t@click=\"selectLocation(item)\"\n\t\t\t\t\t\t\t:class=\"{'active': locationName === item.name}\">\n\t\t\t\t\t\t\t<text class=\"location-item-name\">{{ item.name }}</text>\n\t\t\t\t\t\t\t<text class=\"uni-icon-checkmarkempty\" v-if=\"locationName === item.name\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script setup>\nimport { ref, onMounted, nextTick } from 'vue';\nimport { onLoad, onShow, onReady, onPageScroll } from '@dcloudio/uni-app';\nimport ServiceGrid from '@/components/index/ServiceGrid.vue';\nimport MerchantRecommend from '@/components/index/MerchantRecommend.vue';\nimport InfoList from '@/components/index/InfoList.vue';\nimport FeatureGrid from '@/components/index/FeatureGrid.vue';\nimport CityNews from '@/components/index/CityNews.vue';\n// 引入真实API替换Mock数据\nimport api from '@/api/index.js';\nimport { homeApi } from '@/api/homeApi.js';\n\n// -- 自定义导航栏所需变量 --\nconst statusBarHeight = ref(0);\nconst navBarHeight = ref(0);\nconst navBarFullHeight = ref(0);\nconst navTitleWidth = ref(0);\n// -- 自定义导航栏所需变量结束 --\n\n// 滚动节流阀\nconst scrollTimer = ref(null);\n\t\t\t\n\t\t\t// 引导关注相关\nconst showFollowTip = ref(true);\nconst showQrCode = ref(false);\n\t\t\t\n\t\t\t// 位置信息\nconst locationName = ref('磁县');\nconst showLocationPicker = ref(false);\nconst locationList = ref([\n\t\t\t\t{ name: '磁县', id: 'cx' },\n\t\t\t\t{ name: '邯郸', id: 'hd' },\n\t\t\t\t{ name: '峰峰', id: 'ff' },\n\t\t\t\t{ name: '武安', id: 'wa' }\n]);\n\t\t\t\n\t\t\t// 未读消息数\nconst unreadMessageCount = ref(5);\n\t\t\t\n\t\t\t// 轮播图数据 - 从后台管理系统获取\nconst bannerList = ref([]);\n\t\t\t\n\t\t\t// 服务分类数据 - 从后台管理系统获取\nconst serviceList = ref([]);\n\t\t\t\n\t\t\t// 服务分类弹窗\nconst showServicePopup = ref(false);\nconst currentServiceCategory = ref({\n\t\t\t\tname: '',\n\t\t\t\tsubItems: []\n});\n\t\t\t\n\t\t\t// 同城信息标签\nconst infoTabs = ref([\n\t\t\t\t{ id: 'all', name: '全部' },\n\t\t\t\t{ id: 'rent', name: '房屋租售' },\n\t\t\t\t{ id: 'job', name: '招聘求职' },\n\t\t\t\t{ id: 'service', name: '生活服务' },\n\t\t\t\t{ id: 'second', name: '二手交易' },\n\t\t\t\t{ id: 'car', name: '车辆信息' },\n\t\t\t\t{ id: 'other', name: '其他信息' }\n]);\n\t\t\t\n\t\t\t// 磁县同城信息分类\nconst infoCategories = ref([\n\t\t\t\t'到家服务',\n\t\t\t\t'寻找服务',\n\t\t\t\t'生意转让',\n\t\t\t\t'招聘信息',\n\t\t\t\t'求职信息',\n\t\t\t\t'房屋出租',\n\t\t\t\t'房屋出售',\n\t\t\t\t'二手车辆',\n\t\t\t\t'宠物信息',\n\t\t\t\t'商家活动',\n\t\t\t\t'婚恋交友',\n\t\t\t\t'车辆服务',\n\t\t\t\t'二手闲置',\n\t\t\t\t'磁州拼车',\n\t\t\t\t'教育培训',\n\t\t\t\t'其他服务'\n]);\n\t\t\t\n\t\t\t// 同城信息数据 - 普通信息\nconst allInfoList = ref([\n\t\t\t\t{ id: 'business-transfer-restaurant-1', category: '生意转让', content: '【红包】黄金地段餐饮店整体转让，地处商业中心，设备齐全可直接营业！', time: '2024-05-16 08:30', views: 186, pageType: 'business-transfer-detail', hasRedPacket: true, redPacketAmount: '15.88', redPacketType: 'fixed', redPacketCount: 30, redPacketRemain: 18 },\n\t\t\t\t{ id: 'business-transfer-no-red-1', category: '生意转让', content: '县城中心奶茶店转让，客流稳定，接手即可盈利，因个人原因急转', time: '2024-05-16 10:15', views: 135, pageType: 'business-transfer-detail', hasRedPacket: false },\n\t\t\t\t{ id: 'red-packet-service-1', category: '到家服务', content: '【红包】专业家庭保洁服务，首单立减20元，预约送10元现金红包！', time: '2024-05-15 09:30', views: 235, pageType: 'home-service-detail', hasRedPacket: true, redPacketAmount: '10.00', redPacketType: 'fixed', redPacketCount: 50, redPacketRemain: 32 },\n\t\t\t\t{ id: 'service-no-red-1', category: '到家服务', content: '专业上门维修空调、冰箱、洗衣机等家电，技术精湛，价格公道', time: '2024-05-15 11:30', views: 176, pageType: 'home-service-detail', hasRedPacket: false },\n\t\t\t\t{ id: 'red-packet-business-1', category: '生意转让', content: '【红包】黄金地段餐饮店整体转让，接手即可盈利，老板定制18.8元红包！', time: '2024-05-14 16:20', views: 328, pageType: 'business-transfer-detail', hasRedPacket: true, redPacketAmount: '18.88', redPacketType: 'fixed', redPacketCount: 100, redPacketRemain: 45 },\n\t\t\t\t{ id: 'red-packet-job-1', category: '招聘信息', content: '【红包】招聘销售经理5名，底薪5000+提成，咨询简历投递送随机红包！', time: '2024-05-14 15:15', views: 456, pageType: 'job-detail', hasRedPacket: true, redPacketAmount: '66.66', redPacketType: 'random', redPacketCount: 30, redPacketRemain: 12 },\n\t\t\t\t{ id: 'job-no-red-1', category: '招聘信息', content: '急招会计1名，五险一金，双休，2年以上工作经验，薪资4500-6000', time: '2024-05-15 14:30', views: 208, pageType: 'job-detail', hasRedPacket: false },\n\t\t\t\t{ id: 'job-seeking-no-red-1', category: '求职信息', content: '计算机专业应届毕业生求职，熟悉前端开发，有项目经验，可立即上岗', time: '2024-05-15 16:45', views: 125, pageType: 'job-seeking-detail', hasRedPacket: false },\n\t\t\t\t{ id: 'red-packet-house-1', category: '房屋出租', content: '【红包】市中心精装两室一厅出租，家电齐全，看房送15元现金红包！', time: '2024-05-13 14:30', views: 289, pageType: 'house-rent-detail', hasRedPacket: true, redPacketAmount: '15.00', redPacketType: 'fixed', redPacketCount: 20, redPacketRemain: 8 },\n\t\t\t\t{ id: 'house-rent-no-red-1', category: '房屋出租', content: '学区房两室一厅出租，家电家具齐全，拎包入住，交通便利', time: '2024-05-14 10:20', views: 197, pageType: 'house-rent-detail', hasRedPacket: false },\n\t\t\t\t{ id: 'house-sale-no-red-1', category: '房屋出售', content: '县城南区新房，三室两厅，120平米，毛坯房，采光好，有车位', time: '2024-05-14 09:15', views: 268, pageType: 'house-sale-detail', hasRedPacket: false },\n\t\t\t\t{ id: 'red-packet-car-1', category: '二手车辆', content: '【红包】2023款本田雅阁2.0L，准新车，行驶5000公里，查询车况送红包！', time: '2024-05-12 13:25', views: 376, pageType: 'car-detail', hasRedPacket: true, redPacketAmount: '20.00', redPacketType: 'fixed', redPacketCount: 25, redPacketRemain: 10 },\n\t\t\t\t{ id: 'car-no-red-1', category: '二手车辆', content: '2020款大众朗逸，1.5L自动挡，行驶3万公里，无事故，一手车', time: '2024-05-14 13:40', views: 189, pageType: 'car-detail', hasRedPacket: false },\n\t\t\t\t{ id: 'pet-no-red-1', category: '宠物信息', content: '家养蓝猫幼崽出售，2个月大，已驱虫，疫苗已做，可上门看猫', time: '2024-05-14 15:20', views: 162, pageType: 'pet-detail', hasRedPacket: false },\n\t\t\t\t{ id: 'merchant-activity-no-red-1', category: '商家活动', content: '新开张火锅店满减活动，满100减30，满200减80，还有精美礼品赠送', time: '2024-05-14 11:25', views: 248, pageType: 'merchant-activity-detail', hasRedPacket: false },\n\t\t\t\t{ id: 'red-packet-vehicle-1', category: '车辆服务', content: '【红包】专业汽车美容贴膜，隐形车衣，预约试用送50元红包！', time: '2024-05-11 11:45', views: 198, pageType: 'vehicle-service-detail', hasRedPacket: true, redPacketAmount: '50.00', redPacketType: 'fixed', redPacketCount: 10, redPacketRemain: 5 },\n\t\t\t\t{ id: 'vehicle-service-no-red-1', category: '车辆服务', content: '专业汽车保养，机油三滤更换，四轮定位，价格实惠，技术可靠', time: '2024-05-14 16:30', views: 156, pageType: 'vehicle-service-detail', hasRedPacket: false },\n\t\t\t\t{ id: 'red-packet-second-hand-1', category: '二手闲置', content: '【红包】全新iPhone 15 Pro Max，黑色256G，抽奖送华为手环！', time: '2024-05-10 10:20', views: 468, pageType: 'second-hand-detail', hasRedPacket: true, redPacketAmount: '88.88', redPacketType: 'random', redPacketCount: 5, redPacketRemain: 2 },\n\t\t\t\t{ id: 'second-hand-no-red-1', category: '二手闲置', content: '9成新MacBook Pro 2022款，M1芯片，16G内存，512G硬盘，原价12999', time: '2024-05-14 17:10', views: 215, pageType: 'second-hand-detail', hasRedPacket: false },\n\t\t\t\t{ id: 'ride-share-no-red-1', category: '磁州拼车', content: '每天早上7点县城到邯郸拼车，轿车舒适，准时发车，长期有效', time: '2024-05-14 18:20', views: 183, pageType: 'carpool-detail', hasRedPacket: false },\n\t\t\t\t{ id: 'education-no-red-1', category: '教育培训', content: '小学初中高中各科辅导，一对一定制教学计划，提分效果明显', time: '2024-05-14 19:10', views: 134, pageType: 'education-detail', hasRedPacket: false },\n\t\t\t\t{ id: 'dating-red-1', category: '婚恋交友', content: '【红包】28岁女士，本科学历，身高165cm，温柔大方，期待遇见有缘人', time: '2024-05-16 09:15', views: 228, pageType: 'dating-detail', hasRedPacket: true, redPacketAmount: '18.88', redPacketType: 'fixed', redPacketCount: 20, redPacketRemain: 12 },\n\t\t\t\t{ id: 'dating-no-red-1', category: '婚恋交友', content: '32岁男士，身高178cm，事业稳定，性格开朗，寻找志同道合的另一半', time: '2024-05-15 14:20', views: 176, pageType: 'dating-detail', hasRedPacket: false },\n\t\t\t\t{ id: 'merchant-activity-red-1', category: '商家活动', content: '【红包】新开业烤肉店满减活动，满200减100，关注公众号送20元无门槛代金券', time: '2024-05-16 10:30', views: 315, pageType: 'merchant-activity-detail', hasRedPacket: true, redPacketAmount: '20.00', redPacketType: 'fixed', redPacketCount: 40, redPacketRemain: 25 },\n\t\t\t\t{ id: 'merchant-activity-no-red-2', category: '商家活动', content: '周年庆典大促，全场化妆品8折起，部分商品买一送一，活动时间5月20-25日', time: '2024-05-15 16:40', views: 198, pageType: 'merchant-activity-detail', hasRedPacket: false },\n\t\t\t\t{ id: 'vehicle-service-no-red-2', category: '车辆服务', content: '专业汽车保养，机油三滤更换，四轮定位，价格实惠，技术可靠', time: '2024-05-14 16:30', views: 156, pageType: 'vehicle-service-detail', hasRedPacket: false },\n\t\t\t\t{ id: 'carpool-1', category: '磁州拼车', content: '磁县→邯郸，每天早8点发车，舒适型轿车，可带行李，剩余3个座位', time: '2024-05-16 07:30', views: 187, pageType: 'carpool-detail', hasRedPacket: false },\n\t\t\t\t{ id: 'carpool-2', category: '磁州拼车', content: '邯郸→磁县，下午5点发车，每天固定班次，可提前预约，支持改签', time: '2024-05-16 08:15', views: 145, pageType: 'carpool-detail', hasRedPacket: false },\n\t\t\t\t{ id: 'carpool-red-1', category: '磁州拼车', content: '【红包】磁县→石家庄，周六早7点发车，舒适商务车，咨询送10元红包！', time: '2024-05-15 16:40', views: 223, pageType: 'carpool-detail', hasRedPacket: true, redPacketAmount: '10.00', redPacketType: 'fixed', redPacketCount: 20, redPacketRemain: 15 },\n\t\t\t\t{ id: 'carpool-3', category: '磁州拼车', content: '磁县→北京，周五晚8点发车，7座商务车，舒适安全，提供夜间服务', time: '2024-05-14 14:20', views: 198, pageType: 'carpool-detail', hasRedPacket: false }\n]);\n\t\t\t\n\t\t\t// 置顶信息列表\nconst toppedInfoList = ref([\n\t\t\t\t{ \n\t\t\t\t\tid: 'topped-job-1', \n\t\t\t\t\tcategory: '招聘信息', \n\t\t\t\t\tcontent: '急招会计1名，五险一金，双休，2年以上工作经验，薪资4500-6000', \n\t\t\t\t\ttime: '2024-05-15 14:30', \n\t\t\t\t\tviews: 208, \n\t\t\t\t\tpageType: 'job-detail', \n\t\t\t\t\tisTopped: true,\n\t\t\t\t\ttopType: 'paid',\n\t\t\t\t\ttopExpiry: '2024-05-20'\n\t\t\t\t},\n\t\t\t\t{ \n\t\t\t\t\tid: 'topped-house-1', \n\t\t\t\t\tcategory: '房屋出租', \n\t\t\t\t\tcontent: '市中心精装两室一厅出租，家电齐全，拎包入住，交通便利，周边配套设施完善', \n\t\t\t\t\ttime: '2024-05-14 10:20', \n\t\t\t\t\tviews: 197, \n\t\t\t\t\tpageType: 'house-rent-detail', \n\t\t\t\t\tisTopped: true,\n\t\t\t\t\ttopType: 'ad',\n\t\t\t\t\ttopExpiry: '2024-05-22',\n\t\t\t\t\timages: ['/static/images/tabbar/wxacode.jpg', '/static/images/tabbar/wxacode.jpg']\n\t\t\t\t}\n]);\n\t\t\t\n\t\t\t// 广告横幅\nconst adBanner = ref({\n\t\t\t\timage: '/static/images/ad-banner.jpg',\n\t\t\t\turl: '/pages/activity/detail?id=3'\n});\n\t\t\t\n\t\t\t// 二维码弹窗\nconst qrCodeData = ref({\n\t\t\t\ttitle: '关注公众号',\n\t\t\t\timage: '/static/images/qrcode.jpg',\n\t\t\t\ttips: '扫码关注磁州生活',\n\t\t\t\tdesc: '获取更多本地信息和优惠'\n});\n\t\t\t\n\t\t\t// 搜索框相关\nconst searchKeyword = ref('');\n\t\t\t\n\t\t\t// 同城信息标签相关\nconst currentInfoTab = ref(0);\nconst isTabsFixed = ref(false);\nconst visibleCategories = ref([\n\t\t\t\t'到家服务', '寻找服务', '生意转让', '招聘信息', '求职信息',\n\t\t\t\t'房屋出租', '房屋出售', '二手车辆', '宠物信息', '商家活动',\n\t\t\t\t'婚恋交友', '车辆服务', '二手闲置', '磁州拼车', '教育培训', '其他服务'\n]);\n\n// 信息数据\nconst infoData = ref({\n  allInfoList: [],\n  toppedInfoList: []\n});\n\n// 更新现有的广告横幅数据\nadBanner.value = {\n  image: '/static/images/banner/ad-banner.jpg',\n  url: '/pages/ad/detail',\n  title: '广告横幅'\n};\n\n// 生命周期钩子\nonMounted(() => {\n  // 加载数据\n  loadHomeData(); // 从后台管理系统加载首页数据\n  loadInfoData();\n  loadNewsData();\n  loadBusinessData();\n});\n\n// 从后台管理系统加载首页数据\nasync function loadHomeData() {\n  try {\n    console.log('开始从后台管理系统加载首页数据...');\n    \n    // 并行加载所有首页数据\n    const [banners, services, config, stats, features, merchants, news] = await Promise.all([\n      homeApi.getBanners(),\n      homeApi.getServiceCategories(),\n      homeApi.getHomeConfig(),\n      homeApi.getHomeStats(),\n      homeApi.getFeatureConfig(),\n      homeApi.getMerchantRecommend(),\n      homeApi.getCityNews()\n    ]);\n    \n    // 更新轮播图数据\n    if (banners && banners.length > 0) {\n      bannerList.value = banners.map(banner => ({\n        id: banner.id,\n        image: banner.image,\n        url: banner.url || '',\n        title: banner.title || ''\n      }));\n      console.log('✅ 轮播图数据已更新:', bannerList.value.length, '张');\n    }\n\n    // 更新服务分类数据\n    if (services && services.length > 0) {\n      serviceList.value = services.map(service => ({\n        icon: service.icon || '/static/images/service/default.png',\n        name: service.name,\n        url: service.url || `/pages/service/${service.id}`\n      }));\n      console.log('✅ 服务分类数据已更新:', serviceList.value.length, '个');\n    }\n\n    // 更新特色功能数据\n    if (features && features.length > 0) {\n      console.log('✅ 特色功能数据已获取:', features.length, '个功能');\n      // 这里可以更新四宫格功能数据\n    }\n\n    // 更新商家推荐数据\n    if (merchants && merchants.length > 0) {\n      console.log('✅ 商家推荐数据已获取:', merchants.length, '个商家');\n      // 这里可以更新商家推荐组件数据\n    }\n\n    // 更新同城资讯数据\n    if (news && news.length > 0) {\n      console.log('✅ 同城资讯数据已获取:', news.length, '条资讯');\n      // 这里可以更新同城资讯组件数据\n    }\n\n    // 更新首页配置\n    if (config && config.site_title) {\n      uni.setNavigationBarTitle({\n        title: config.site_title\n      });\n      console.log('✅ 页面标题已更新:', config.site_title);\n    }\n    \n    // 记录页面访问统计\n    homeApi.recordPageView();\n    \n    console.log('🎉 后台管理系统数据加载完成!');\n    console.log('📊 数据统计:', {\n      banners: bannerList.value.length,\n      services: serviceList.value.length,\n      features: features?.length || 0,\n      merchants: merchants?.length || 0,\n      news: news?.length || 0,\n      config: config?.site_title || '未配置',\n      stats: stats?.total_views || 0\n    });\n    \n  } catch (error) {\n    console.error('加载首页数据失败:', error);\n    \n    // 加载失败时使用默认数据\n    bannerList.value = [\n      { id: 1, image: '/static/images/banner/banner-1.png', url: '', title: '磁州生活网' },\n      { id: 2, image: '/static/images/banner/banner-2.png', url: '', title: '本地生活服务' }\n    ];\n    \n    serviceList.value = [\n      { icon: '/static/images/service/food.png', name: '美食外卖', url: '/pages/service/food' },\n      { icon: '/static/images/service/market.png', name: '超市便利', url: '/pages/service/market' },\n      { icon: '/static/images/service/medicine.png', name: '医药健康', url: '/pages/service/medicine' },\n      { icon: '/static/images/service/fresh.png', name: '生鲜果蔬', url: '/pages/service/fresh' },\n      { icon: '/static/images/service/flower.png', name: '鲜花绿植', url: '/pages/service/flower' },\n      { icon: '/static/images/service/clean.png', name: '家政保洁', url: '/pages/service/clean' },\n      { icon: '/static/images/service/repair.png', name: '维修服务', url: '/pages/service/repair' },\n      { icon: '/static/images/service/more.png', name: '更多服务', url: '/pages/service/more' }\n    ];\n  }\n}\n\n// 加载新闻数据\nasync function loadNewsData() {\n  try {\n    const result = await api.news.getList({ page: 1, limit: 10 });\n    if (result.success) {\n      // 更新新闻数据（如果有新闻组件的话）\n      console.log('新闻数据加载成功:', result.data);\n    }\n  } catch (error) {\n    console.error('加载新闻数据失败:', error);\n  }\n}\n\n// 加载商家数据\nasync function loadBusinessData() {\n  try {\n    const result = await api.business.getList({ page: 1, limit: 10 });\n    if (result.success) {\n      // 更新商家数据（如果有商家组件的话）\n      console.log('商家数据加载成功:', result.data);\n    }\n  } catch (error) {\n    console.error('加载商家数据失败:', error);\n  }\n}\n\n// 加载信息数据 - 使用真实API替换Mock数据\nasync function loadInfoData() {\n  try {\n    // 显示加载状态\n    uni.showLoading({\n      title: '加载中...'\n    });\n\n    // 并行加载置顶信息和普通信息\n    const [toppedResult, allResult] = await Promise.all([\n      api.info.getTopped({ page: 1, limit: 10 }),\n      api.info.getAll({ page: 1, limit: 20 })\n    ]);\n\n    // 处理置顶信息数据\n    if (toppedResult.success) {\n      toppedInfoList.value = toppedResult.data.map(item => ({\n        ...item,\n        images: item.images || [],\n        tags: item.tags || [],\n        views: item.views || 0,\n        likes: item.likes || 0,\n        comments: item.comments || 0\n      }));\n    } else {\n      console.error('获取置顶信息失败:', toppedResult.message);\n    }\n\n    // 处理普通信息数据\n    if (allResult.success) {\n      allInfoList.value = allResult.data.map(item => ({\n        ...item,\n        images: item.images || [],\n        tags: item.tags || [],\n        views: item.views || 0,\n        likes: item.likes || 0,\n        comments: item.comments || 0\n      }));\n    } else {\n      console.error('获取信息列表失败:', allResult.message);\n    }\n\n  } catch (error) {\n    console.error('加载信息数据失败:', error);\n    uni.showToast({\n      title: '加载失败，请重试',\n      icon: 'none'\n    });\n\n    // 加载失败时使用空数据\n    toppedInfoList.value = [];\n    allInfoList.value = [];\n  } finally {\n    uni.hideLoading();\n  }\n}\n\n// 处理标签切换事件\nfunction handleTabChange(tabInfo) {\n  console.log('标签切换:', tabInfo);\n  // 可以根据标签切换加载不同类型的数据\n  // 这里使用的是示例数据，实际应用中可能需要从服务器获取数据\n}\n\n// 组件引用\nconst infoList = ref(null);\n\n\t\t// 检查用户位置\nfunction checkUserLocation() {\n\t\t\t// 这里可以获取用户位置，暂时使用默认值\n\t\t\tconsole.log('检查用户位置');\n}\n\t\t\n\t\t// 加载轮播图数据\nfunction loadBannerData() {\n\t\t\t// 这里可以从API获取数据，暂时使用默认值\n\t\t\tconsole.log('加载轮播图数据');\n}\n\t\t\n\t\t// 加载服务分类数据\nfunction loadServiceData() {\n\t\t\t// 这里可以从API获取数据，暂时使用默认值\n\t\t\tconsole.log('加载服务分类数据');\n}\n\t\t\n\t\t// 加载商家推荐数据\nfunction loadMerchantData() {\n\t\t\t// 这里可以从API获取数据，暂时使用默认值\n\t\t\tconsole.log('加载商家推荐数据');\n}\n\t\t\n\t\t// 关闭关注提示\nfunction closeFollowTip() {\n\tshowFollowTip.value = false;\n\t\t\t// 可以存储状态，避免重复显示\n\t\t\tuni.setStorageSync('hideFollowTip', true);\n}\n\t\t\n\t\t// 打开二维码\nfunction openQrCode() {\n\tshowQrCode.value = true;\n}\n\t\t\n\t\t// 关闭二维码\nfunction closeQrCode() {\n\tshowQrCode.value = false;\n}\n\t\t\n\t\t// 导航到指定页面\nfunction navigateTo(url) {\n\t\t\tif (!url) return;\n\t\t\t\n\t\t\tuni.navigateTo({\n\t\t\t\turl: url,\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('页面跳转失败:', err);\n\t\t\t\t\t// 尝试使用switchTab\n\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\turl: url,\n\t\t\t\t\t\tfail: (err2) => {\n\t\t\t\t\t\t\tconsole.error('switchTab也失败:', err2);\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '页面跳转失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n}\n\t\t\n\t\t// 打开位置选择器\nfunction openLocationPicker() {\n\tshowLocationPicker.value = true;\n}\n\t\t\n\t\t// 关闭位置选择器\nfunction closeLocationPicker() {\n\tshowLocationPicker.value = false;\n}\n\t\t\n\t\t// 选择位置\nfunction selectLocation(item) {\n\tlocationName.value = item.name;\n\tcloseLocationPicker();\n\t\t\t\n\t\t\t// 根据选择的位置重新加载数据\n\tloadBannerData();\n\tloadServiceData();\n\tloadMerchantData();\n\tloadInfoData();\n}\n\t\t\n\t\t// 打开服务分类弹窗\nfunction openServicePopup(category) {\n\tcurrentServiceCategory.value = category;\n\tshowServicePopup.value = true;\n}\n\t\t\n\t\t// 关闭服务分类弹窗\nfunction closeServicePopup() {\n\tshowServicePopup.value = false;\n}\n\t\t\n\t\t// 跳转到子服务页面\nfunction navigateToSubService(item) {\n\t\t\tif (!item.url) return;\n\t\t\t\n\t\t\tuni.navigateTo({\n\t\t\t\turl: item.url,\n\t\t\t\tfail: () => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '页面跳转失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\tcloseServicePopup();\n}\n\t\t\n\t\t// 处理信息标签切换\nfunction handleInfoTabChange(tab) {\n\t\t\tconsole.log('切换到标签:', tab.name);\n\tcurrentInfoTab.value = tab.index;\n\t\t\t// 根据选中的标签过滤信息数据\n}\n\t\t\n\t\t// 搜索框相关方法\nfunction onSearchInput(e) {\n\tsearchKeyword.value = e.detail.value;\n}\n\t\t\n\t\t// 执行搜索\nfunction doSearch() {\n\tif (!searchKeyword.value.trim()) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请输入搜索关键词',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 执行搜索逻辑\n\tperformSimpleSearch();\n}\n\t\t\n\t\t// 简单搜索功能\nfunction performSimpleSearch() {\n\t\t\t// 显示加载中\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '搜索中...'\n\t\t\t});\n\t\t\t\n\t\t\t// 从现有信息列表中搜索\n\t\t\tconst searchResults = [];\n\t\t\t\n\t\t\t// 遍历置顶信息\ntoppedInfoList.value.forEach(item => {\n\t\tif (item.content && item.content.includes(searchKeyword.value)) {\n\t\t\t\t\tsearchResults.push(item);\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\t// 遍历普通信息\n\tallInfoList.value.forEach(item => {\n\t\tif (item.content && item.content.includes(searchKeyword.value)) {\n\t\t\t\t\tsearchResults.push(item);\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\t// 隐藏加载\n\t\t\tsetTimeout(() => {\n\t\t\t\tuni.hideLoading();\n\t\t\t\t\n\t\t\t\t// 显示搜索结果\n\t\t\t\tif (searchResults.length > 0) {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '搜索结果',\n\t\t\t\t\t\tcontent: `找到 ${searchResults.length} 条相关信息，点击确定查看详情`,\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tif (res.confirm && searchResults.length > 0) {\n\t\t\t\t\t\t\t\t// 查看第一条搜索结果详情\n\t\t\t\t\t\tnavigateToInfoDetail(searchResults[0]);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '未找到相关信息',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}, 500);\n}\n\t\t\n\t\t// 导航到信息详情页\nfunction navigateToInfoDetail(item) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/info/detail?id=${item.id}`,\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('导航到信息详情页失败:', err);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '页面跳转失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n}\n\t\t\n\t\t// 处理页面滚动，实现InfoList的吸顶效果\nfunction handlePageScroll(scrollTop) {\n\t\t\t// 确保InfoList组件已经挂载\n\tif (infoList.value) {\n\t\tconst query = uni.createSelectorQuery();\n\t\t\t\t// 使用 'all-info-title-row' 作为参照物，因为它位置稳定\n\t\t\t\tquery.select('.scrollable-content >>> .all-info-title-row').boundingClientRect(data => {\n\t\t\t\t\tif (!data) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconst navBarHeight = 44; // 自定义导航栏高度\n\t\t\t\t\t// 减去1px以消除可能的缝隙\n\t\t\tconst fixedHeaderHeight = statusBarHeight.value + navBarHeight - 1; \n\t\t\t\t\t\n\t\t\t\t\t// 计算出参照物（标题栏）的底部位置\n\t\t\t\t\tconst titleRowBottom = data.top + data.height;\n\t\t\t\t\t\n\t\t\t\t\t// 当参照物（标题栏）的底部滚动到导航栏下方时，触发吸顶\n\t\t\t\t\tif (titleRowBottom <= fixedHeaderHeight) {\n\t\t\t\tinfoList.value.setSticky(true, fixedHeaderHeight);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 否则，取消吸顶\n\t\t\t\tinfoList.value.setSticky(false, 0);\n\t\t\t\t\t}\n\t\t\t\t}).exec();\n\t\t\t}\n\t\t}\n\nonLoad(() => {\n\t// #ifdef MP-WEIXIN\n\tconst systemInfo = uni.getSystemInfoSync();\n\tconst menuButtonInfo = uni.getMenuButtonBoundingClientRect();\n\n\tstatusBarHeight.value = systemInfo.statusBarHeight || 20;\n\tnavBarHeight.value = (menuButtonInfo.top - statusBarHeight.value) * 2 + menuButtonInfo.height + 3;\n\tnavBarFullHeight.value = statusBarHeight.value + navBarHeight.value;\n\tnavTitleWidth.value = menuButtonInfo.left;\n\t// #endif\n\n\t// #ifndef MP-WEIXIN\n\tstatusBarHeight.value = uni.getSystemInfoSync().statusBarHeight || 20;\n\tnavBarHeight.value = 45 + 3;\n\tnavBarFullHeight.value = statusBarHeight.value + navBarHeight.value;\n\tnavTitleWidth.value = uni.getSystemInfoSync().windowWidth - 100; // 模拟一个宽度\n\t// #endif\n\n\tcheckUserLocation();\n\tloadBannerData();\n\tloadServiceData();\n\tloadMerchantData();\n\tloadInfoData();\n});\n\nonShow(() => {\n\tconst currentWindowInfo = uni.getWindowInfo();\n\tstatusBarHeight.value = currentWindowInfo.statusBarHeight || 20;\n});\n\nonReady(() => {\n\tsetTimeout(() => {\n\t\tconst currentWindowInfo = uni.getWindowInfo();\n\t\tstatusBarHeight.value = currentWindowInfo.statusBarHeight || 20;\n\t}, 50);\n});\n\nonPageScroll((e) => {\n\tif (scrollTimer.value) {\n\t\treturn;\n\t}\n\tscrollTimer.value = setTimeout(() => {\n\t\thandlePageScroll(e.scrollTop);\n\t\tscrollTimer.value = null;\n\t}, 100);\n});\n</script>\n\n<style lang=\"scss\">\n.index-container {\n\tposition: relative;\n\tbackground-color: #F5F6FA;\n\tmin-height: 100vh;\n}\n\n/* 导航栏图标样式 */\n.navbar-icon {\n\twidth: 44rpx;\n\theight: 44rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbackground-color: rgba(255, 255, 255, 0.1);\n\tborder-radius: 50%;\n\ttransition: all 0.3s;\n}\n\n.navbar-icon-hover {\n\tbackground-color: rgba(255, 255, 255, 0.2);\n\ttransform: scale(0.95);\n}\n\n.icon-image {\n\twidth: 28rpx;\n\theight: 28rpx;\n\tfilter: brightness(0) invert(1);\n}\n\n/* 可滚动的弧形背景 */\n.content-bg-scroll {\n\tposition: absolute;\n\ttop: 15rpx; /* 向下移动15rpx */\n\tleft: 0;\n\tright: 0;\n\theight: 400rpx; /* 保持原始高度不变 */\n\tbackground-color: #1677FF;\n\tborder-bottom-left-radius: 80rpx;\n\tborder-bottom-right-radius: 80rpx;\n\tz-index: 1;\n}\n\n/* 页面内容区域 */\n.scrollable-content {\n\tposition: relative;\n\tz-index: 2;\n\tbox-sizing: border-box;\n\tpadding-top: 20rpx; /* 添加顶部内边距，与导航栏保持一定距离 */\n}\n\n/* 引导关注模块样式 */\n.follow-guide-in-bg {\n\tposition: relative;\n\tpadding: 0 60rpx;\n\tmargin: 15rpx 0 30rpx 0; /* 将顶部边距调整为15rpx，从原始10rpx增加5rpx */\n\tz-index: 10;\n\twidth: 100%;\n\tbox-sizing: border-box;\n\tdisplay: flex;\n\tjustify-content: center;\n}\n\n.follow-guide-content {\n\tbackground-color: rgba(255, 255, 255, 0.95);\n\tbackdrop-filter: blur(15px);\n\t-webkit-backdrop-filter: blur(15px);\n\tborder-radius: 25rpx;\n\tpadding: 16rpx 26rpx 16rpx 75rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tbox-shadow: 0 12rpx 25rpx rgba(0, 0, 0, 0.15), 0 4rpx 8rpx rgba(0, 0, 0, 0.08), inset 0 0 0 1px rgba(255, 255, 255, 0.8);\n\twidth: 100%;\n\tbox-sizing: border-box;\n\tmax-width: 650rpx;\n\tposition: relative;\n\t/* overflow: hidden; */\n}\n\n.follow-guide-content::before {\n\tcontent: '';\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: linear-gradient(135deg, rgba(22, 119, 255, 0.05), rgba(0, 82, 204, 0.02));\n\tz-index: 0;\n}\n\n.follow-avatar {\n\twidth: 55rpx;\n\theight: 55rpx;\n\tborder-radius: 50%;\n\tbox-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.4), 0 4rpx 8rpx rgba(0, 0, 0, 0.2);\n\tobject-fit: contain;\n\tposition: absolute;\n\tleft: 10rpx;\n\ttop: 50%;\n\ttransform: translateY(-50%);\n\tborder: 3rpx solid #fff;\n\tz-index: 5;\n\tbackground: #fff;\n\tpadding: 0;\n}\n\n@keyframes gentle-pulse {\n\t0% { transform: translateY(-50%) scale(1); -webkit-transform: translateY(-50%) scale(1); }\n\t50% { transform: translateY(-50%) scale(1.03); -webkit-transform: translateY(-50%) scale(1.03); }\n\t100% { transform: translateY(-50%) scale(1); -webkit-transform: translateY(-50%) scale(1); }\n}\n\n.follow-guide-content:hover .follow-avatar {\n\tbox-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.45), 0 5rpx 10rpx rgba(0, 0, 0, 0.25);\n\tanimation: none;\n\ttransform: translateY(-50%) scale(1.06);\n\t-webkit-transform: translateY(-50%) scale(1.06);\n}\n\n.follow-text {\n\tflex: 1;\n\tfont-size: 20rpx;\n\tcolor: #333;\n\tline-height: 1.3;\n\tfont-weight: 500;\n\twhite-space: nowrap;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\tpadding-right: 4rpx;\n\tmargin-left: 8rpx;\n\tposition: relative;\n\tz-index: 1;\n}\n\n.follow-btn-wrap {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tflex-shrink: 0;\n\tposition: relative;\n\tz-index: 1;\n}\n\n.follow-btn {\n\tbackground: linear-gradient(145deg, #394FC2, #4A67D7);\n\tcolor: #ffffff;\n\tfont-size: 22rpx;\n\tpadding: 0 22rpx;\n\theight: 54rpx;\n\tline-height: 54rpx;\n\tborder-radius: 27rpx;\n\tmargin: 0;\n\tposition: relative;\n\tz-index: 2;\n\tbox-shadow: 5rpx 5rpx 10rpx rgba(61, 86, 193, 0.3),\n\t            -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.3),\n\t            inset 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.2);\n\tfont-weight: 600;\n\tborder: none;\n\ttransition: all 0.2s ease;\n\ttext-align: center;\n}\n\n.follow-btn:active {\n\ttransform: scale(0.96);\n\tbox-shadow: 3rpx 3rpx 6rpx rgba(61, 86, 193, 0.3),\n\t            -1rpx -1rpx 3rpx rgba(255, 255, 255, 0.3),\n\t            inset 1rpx 1rpx 3rpx rgba(0, 0, 0, 0.1);\n}\n\n.follow-close {\n\tposition: absolute;\n\ttop: -5rpx;\n\tright: -5rpx;\n\twidth: 26rpx;\n\theight: 26rpx;\n\tbackground-color: rgba(0, 0, 0, 0.3);\n\tcolor: #fff;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 18rpx;\n\tz-index: 10;\n}\n\n/* 轮播图 */\n.banner-outer {\n\tposition: relative;\n\tmargin: 25rpx 30rpx 50rpx; /* 将顶部边距调整为25rpx，从原始20rpx增加5rpx */\n\tborder-radius: 30rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 25rpx 45rpx rgba(0, 0, 0, 0.35), 0 15rpx 25rpx rgba(0, 0, 0, 0.2);\n\tborder: 12rpx solid #ffffff;\n\ttransform: translateZ(0);\n\tz-index: 3;\n\tanimation: float 6s ease-in-out infinite;\n\ttransition: transform 0.3s ease, box-shadow 0.3s ease;\n\tpadding: 2rpx;\n\tbackground-color: rgba(255, 255, 255, 0.8);\n}\n\n@keyframes float {\n\t0% { transform: translateY(0) translateZ(0); }\n\t50% { transform: translateY(-10rpx) translateZ(0); }\n\t100% { transform: translateY(0) translateZ(0); }\n}\n\n.banner-swiper {\n\twidth: 100%;\n\theight: 300rpx;\n\tborder-radius: 16rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);\n}\n\n.banner-item {\n\twidth: 100%;\n\theight: 100%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbackground-color: #FFFFFF;\n}\n\n.banner-image {\n\twidth: 100%;\n\theight: 100%;\n\tborder-radius: 16rpx;\n\tbox-shadow: inset 0 0 15rpx rgba(0, 0, 0, 0.15);\n\ttransition: transform 0.3s ease;\n}\n\n/* 白色内容区域 */\n.white-content {\n\tbackground: #f8f9fc;\n\tposition: relative;\n\tpadding-top: 40rpx;\n\tpadding-bottom: 20rpx;\n\tborder-top-left-radius: 24rpx;\n\tborder-top-right-radius: 24rpx;\n\tbox-shadow: 0 -6rpx 20rpx rgba(0, 0, 0, 0.03);\n\tmargin-top: -40rpx;\n\tz-index: 4;\n}\n\n/* 搜索框样式 */\n.search-container {\n\tpadding: 10rpx 30rpx 35rpx;\n}\n\n.search-box {\n\tdisplay: flex;\n\talign-items: center;\n\tbackground-color: #ffffff;\n\tborder-radius: 40rpx;\n\tpadding: 0 20rpx 0 30rpx;\n\theight: 88rpx;\n\tborder: 1rpx solid #eaedf2;\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n}\n\n.search-icon {\n\twidth: 36rpx;\n\theight: 36rpx;\n\tmargin-right: 16rpx;\n\topacity: 0.4;\n}\n\n.search-input {\n\tflex: 1;\n\theight: 88rpx;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.search-placeholder {\n\tcolor: #b8bdcc;\n\tfont-size: 28rpx;\n}\n\n.search-button {\n\twidth: auto;\n\tmin-width: 90rpx;\n\theight: 60rpx;\n\tline-height: 60rpx;\n\tbackground: #3A86FF;\n\tcolor: #fff;\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\tborder-radius: 30rpx;\n\tpadding: 0 20rpx;\n\tmargin-right: 10rpx;\n\tletter-spacing: 1rpx;\n\ttext-align: center;\n}\n\n/* 二维码弹窗 */\n.qrcode-popup {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: rgba(0, 0, 0, 0.6);\n\tz-index: 999;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.qrcode-card {\n\twidth: 560rpx;\n\tbackground: #FFFFFF;\n\tborder-radius: 20rpx;\n\toverflow: hidden;\n}\n\n.qrcode-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 30rpx 20rpx 20rpx;\n\tborder-bottom: 1px solid #F5F5F5;\n}\n\n.qrcode-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.close-btn {\n\twidth: 40rpx;\n\theight: 40rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tcolor: #999;\n\tfont-size: 40rpx;\n}\n\n.qrcode-content {\n\tpadding: 40rpx;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n}\n\n.qrcode-image {\n\twidth: 320rpx;\n\theight: 320rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.qrcode-tips {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tfont-weight: bold;\n\tmargin-bottom: 10rpx;\n}\n\n.qrcode-desc {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n/* 服务分类弹窗 */\n.service-popup {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tz-index: 999;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.service-popup-mask {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground-color: rgba(0, 0, 0, 0.5);\n}\n\n.service-popup-content {\n\tposition: relative;\n\tbackground-color: #FFFFFF;\n\tborder-radius: 30rpx 30rpx 0 0;\n\tpadding: 30rpx;\n\tmargin-top: auto;\n\tmax-height: 70vh;\n\tdisplay: flex;\n\tflex-direction: column;\n\tanimation: slideUp 0.3s ease;\n}\n\n@keyframes slideUp {\n\tfrom { transform: translateY(100%); }\n\tto { transform: translateY(0); }\n}\n\n.service-popup-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 30rpx;\n}\n\n.service-popup-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n}\n\n.service-popup-close {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tcolor: #999;\n\tfont-size: 40rpx;\n}\n\n.service-popup-scroll {\n\tflex: 1;\n\tmax-height: 60vh;\n}\n\n.service-popup-grid {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tpadding: 10rpx 0;\n}\n\n.service-popup-item {\n\twidth: 25%;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tpadding: 20rpx 0;\n}\n\n.service-popup-icon {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tmargin-bottom: 10rpx;\n}\n\n.service-popup-name {\n\tfont-size: 24rpx;\n\tcolor: #333;\n\ttext-align: center;\n}\n\n/* 位置选择弹窗 */\n.location-popup {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tz-index: 999;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.location-popup-mask {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground-color: rgba(0, 0, 0, 0.5);\n}\n\n.location-popup-content {\n\tposition: relative;\n\tbackground-color: #FFFFFF;\n\tborder-radius: 30rpx 30rpx 0 0;\n\tpadding: 30rpx;\n\tmargin-top: auto;\n\tmax-height: 70vh;\n\tdisplay: flex;\n\tflex-direction: column;\n\tanimation: slideUp 0.3s ease;\n}\n\n.location-popup-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 30rpx;\n}\n\n.location-popup-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n}\n\n.location-popup-close {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tcolor: #999;\n\tfont-size: 40rpx;\n}\n\n.location-list {\n\tmax-height: 60vh;\n\toverflow-y: auto;\n}\n\n.location-item {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 30rpx 20rpx;\n\tborder-bottom: 1rpx solid #F2F2F7;\n}\n\n.location-item:last-child {\n\tborder-bottom: none;\n}\n\n.location-item.active {\n\tcolor: #1677FF;\n}\n\n.location-item-name {\n\tfont-size: 30rpx;\n}\n\n/* 统一字体样式 */\ntext, input, button {\n\tfont-family: -apple-system, BlinkMacSystemFont, \"SF Pro Text\", \"Helvetica Neue\", Helvetica, Arial, sans-serif;\n}\n\n/* 去除所有按钮的默认边框 */\nbutton {\n\tborder: none;\n\toutline: none;\n\tbackground-color: transparent;\n\t-webkit-appearance: none;\n\t-moz-appearance: none;\n\tappearance: none;\n}\n\nbutton::after {\n\tborder: none;\n\toutline: none;\n}\n\n/* -- 自定义导航栏样式 -- */\n.custom-nav-bar {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbackground-color: #1677FF;\n\tz-index: 999;\n\tbox-shadow: 0 4rpx 12rpx rgba(22, 119, 255, 0.2);\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.nav-status-bar {\n\twidth: 100%;\n}\n\n.nav-bar-content {\n\twidth: 100%;\n\tposition: relative;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.nav-bar-title {\n\tcolor: #FFFFFF;\n\tfont-size: 34rpx;\n\tfont-weight: 600;\n\ttext-align: center;\n\tmargin-right: 24px; /* 右侧留出一定空间，平衡胶囊按钮 */\n}\n/* -- 自定义导航栏样式结束 -- */\n</style>\n\n\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni", "homeApi", "api", "navBarHeight", "onLoad", "onShow", "onReady", "onPageScroll", "MiniProgramPage"], "mappings": ";;;;;;;;AA8IA,MAAA,cAAA,MAAA;AACA,MAAA,oBAAA,MAAA;AACA,MAAA,WAAA,MAAA;AACA,MAAA,cAAA,MAAA;AACA,MAAA,WAAA,MAAA;;;;AAMA,UAAA,kBAAAA,cAAAA,IAAA,CAAA;AACA,UAAA,eAAAA,cAAAA,IAAA,CAAA;AACA,UAAA,mBAAAA,cAAAA,IAAA,CAAA;AACA,UAAA,gBAAAA,cAAAA,IAAA,CAAA;AAIA,UAAA,cAAAA,cAAAA,IAAA,IAAA;AAGA,UAAA,gBAAAA,cAAAA,IAAA,IAAA;AACA,UAAA,aAAAA,cAAAA,IAAA,KAAA;AAGA,UAAA,eAAAA,cAAAA,IAAA,IAAA;AACA,UAAA,qBAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,eAAAA,cAAAA,IAAA;AAAA,MACA,EAAA,MAAA,MAAA,IAAA,KAAA;AAAA,MACA,EAAA,MAAA,MAAA,IAAA,KAAA;AAAA,MACA,EAAA,MAAA,MAAA,IAAA,KAAA;AAAA,MACA,EAAA,MAAA,MAAA,IAAA,KAAA;AAAA,IACA,CAAA;AAGAA,kBAAA,IAAA,CAAA;AAGA,UAAA,aAAAA,cAAAA,IAAA,CAAA,CAAA;AAGA,UAAA,cAAAA,cAAAA,IAAA,CAAA,CAAA;AAGA,UAAA,mBAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,yBAAAA,cAAAA,IAAA;AAAA,MACA,MAAA;AAAA,MACA,UAAA,CAAA;AAAA,IACA,CAAA;AAGAA,kBAAAA,IAAA;AAAA,MACA,EAAA,IAAA,OAAA,MAAA,KAAA;AAAA,MACA,EAAA,IAAA,QAAA,MAAA,OAAA;AAAA,MACA,EAAA,IAAA,OAAA,MAAA,OAAA;AAAA,MACA,EAAA,IAAA,WAAA,MAAA,OAAA;AAAA,MACA,EAAA,IAAA,UAAA,MAAA,OAAA;AAAA,MACA,EAAA,IAAA,OAAA,MAAA,OAAA;AAAA,MACA,EAAA,IAAA,SAAA,MAAA,OAAA;AAAA,IACA,CAAA;AAGAA,kBAAAA,IAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACA,CAAA;AAGA,UAAA,cAAAA,cAAAA,IAAA;AAAA,MACA,EAAA,IAAA,kCAAA,UAAA,QAAA,SAAA,qCAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,4BAAA,cAAA,MAAA,iBAAA,SAAA,eAAA,SAAA,gBAAA,IAAA,iBAAA,GAAA;AAAA,MACA,EAAA,IAAA,8BAAA,UAAA,QAAA,SAAA,iCAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,4BAAA,cAAA,MAAA;AAAA,MACA,EAAA,IAAA,wBAAA,UAAA,QAAA,SAAA,oCAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,uBAAA,cAAA,MAAA,iBAAA,SAAA,eAAA,SAAA,gBAAA,IAAA,iBAAA,GAAA;AAAA,MACA,EAAA,IAAA,oBAAA,UAAA,QAAA,SAAA,gCAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,uBAAA,cAAA,MAAA;AAAA,MACA,EAAA,IAAA,yBAAA,UAAA,QAAA,SAAA,uCAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,4BAAA,cAAA,MAAA,iBAAA,SAAA,eAAA,SAAA,gBAAA,KAAA,iBAAA,GAAA;AAAA,MACA,EAAA,IAAA,oBAAA,UAAA,QAAA,SAAA,uCAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,cAAA,cAAA,MAAA,iBAAA,SAAA,eAAA,UAAA,gBAAA,IAAA,iBAAA,GAAA;AAAA,MACA,EAAA,IAAA,gBAAA,UAAA,QAAA,SAAA,uCAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,cAAA,cAAA,MAAA;AAAA,MACA,EAAA,IAAA,wBAAA,UAAA,QAAA,SAAA,mCAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,sBAAA,cAAA,MAAA;AAAA,MACA,EAAA,IAAA,sBAAA,UAAA,QAAA,SAAA,oCAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,qBAAA,cAAA,MAAA,iBAAA,SAAA,eAAA,SAAA,gBAAA,IAAA,iBAAA,EAAA;AAAA,MACA,EAAA,IAAA,uBAAA,UAAA,QAAA,SAAA,8BAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,qBAAA,cAAA,MAAA;AAAA,MACA,EAAA,IAAA,uBAAA,UAAA,QAAA,SAAA,iCAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,qBAAA,cAAA,MAAA;AAAA,MACA,EAAA,IAAA,oBAAA,UAAA,QAAA,SAAA,2CAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,cAAA,cAAA,MAAA,iBAAA,SAAA,eAAA,SAAA,gBAAA,IAAA,iBAAA,GAAA;AAAA,MACA,EAAA,IAAA,gBAAA,UAAA,QAAA,SAAA,oCAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,cAAA,cAAA,MAAA;AAAA,MACA,EAAA,IAAA,gBAAA,UAAA,QAAA,SAAA,gCAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,cAAA,cAAA,MAAA;AAAA,MACA,EAAA,IAAA,8BAAA,UAAA,QAAA,SAAA,uCAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,4BAAA,cAAA,MAAA;AAAA,MACA,EAAA,IAAA,wBAAA,UAAA,QAAA,SAAA,iCAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,0BAAA,cAAA,MAAA,iBAAA,SAAA,eAAA,SAAA,gBAAA,IAAA,iBAAA,EAAA;AAAA,MACA,EAAA,IAAA,4BAAA,UAAA,QAAA,SAAA,gCAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,0BAAA,cAAA,MAAA;AAAA,MACA,EAAA,IAAA,4BAAA,UAAA,QAAA,SAAA,2CAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,sBAAA,cAAA,MAAA,iBAAA,SAAA,eAAA,UAAA,gBAAA,GAAA,iBAAA,EAAA;AAAA,MACA,EAAA,IAAA,wBAAA,UAAA,QAAA,SAAA,kDAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,sBAAA,cAAA,MAAA;AAAA,MACA,EAAA,IAAA,uBAAA,UAAA,QAAA,SAAA,gCAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,kBAAA,cAAA,MAAA;AAAA,MACA,EAAA,IAAA,sBAAA,UAAA,QAAA,SAAA,+BAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,oBAAA,cAAA,MAAA;AAAA,MACA,EAAA,IAAA,gBAAA,UAAA,QAAA,SAAA,uCAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,iBAAA,cAAA,MAAA,iBAAA,SAAA,eAAA,SAAA,gBAAA,IAAA,iBAAA,GAAA;AAAA,MACA,EAAA,IAAA,mBAAA,UAAA,QAAA,SAAA,sCAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,iBAAA,cAAA,MAAA;AAAA,MACA,EAAA,IAAA,2BAAA,UAAA,QAAA,SAAA,2CAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,4BAAA,cAAA,MAAA,iBAAA,SAAA,eAAA,SAAA,gBAAA,IAAA,iBAAA,GAAA;AAAA,MACA,EAAA,IAAA,8BAAA,UAAA,QAAA,SAAA,yCAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,4BAAA,cAAA,MAAA;AAAA,MACA,EAAA,IAAA,4BAAA,UAAA,QAAA,SAAA,gCAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,0BAAA,cAAA,MAAA;AAAA,MACA,EAAA,IAAA,aAAA,UAAA,QAAA,SAAA,mCAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,kBAAA,cAAA,MAAA;AAAA,MACA,EAAA,IAAA,aAAA,UAAA,QAAA,SAAA,kCAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,kBAAA,cAAA,MAAA;AAAA,MACA,EAAA,IAAA,iBAAA,UAAA,QAAA,SAAA,sCAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,kBAAA,cAAA,MAAA,iBAAA,SAAA,eAAA,SAAA,gBAAA,IAAA,iBAAA,GAAA;AAAA,MACA,EAAA,IAAA,aAAA,UAAA,QAAA,SAAA,mCAAA,MAAA,oBAAA,OAAA,KAAA,UAAA,kBAAA,cAAA,MAAA;AAAA,IACA,CAAA;AAGA,UAAA,iBAAAA,cAAAA,IAAA;AAAA,MACA;AAAA,QACA,IAAA;AAAA,QACA,UAAA;AAAA,QACA,SAAA;AAAA,QACA,MAAA;AAAA,QACA,OAAA;AAAA,QACA,UAAA;AAAA,QACA,UAAA;AAAA,QACA,SAAA;AAAA,QACA,WAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,IAAA;AAAA,QACA,UAAA;AAAA,QACA,SAAA;AAAA,QACA,MAAA;AAAA,QACA,OAAA;AAAA,QACA,UAAA;AAAA,QACA,UAAA;AAAA,QACA,SAAA;AAAA,QACA,WAAA;AAAA,QACA,QAAA,CAAA,qCAAA,mCAAA;AAAA,MACA;AAAA,IACA,CAAA;AAGA,UAAA,WAAAA,cAAAA,IAAA;AAAA,MACA,OAAA;AAAA,MACA,KAAA;AAAA,IACA,CAAA;AAGAA,kBAAAA,IAAA;AAAA,MACA,OAAA;AAAA,MACA,OAAA;AAAA,MACA,MAAA;AAAA,MACA,MAAA;AAAA,IACA,CAAA;AAGA,UAAA,gBAAAA,cAAAA,IAAA,EAAA;AAGAA,kBAAA,IAAA,CAAA;AACAA,kBAAA,IAAA,KAAA;AACAA,kBAAAA,IAAA;AAAA,MACA;AAAA,MAAA;AAAA,MAAA;AAAA,MAAA;AAAA,MAAA;AAAA,MACA;AAAA,MAAA;AAAA,MAAA;AAAA,MAAA;AAAA,MAAA;AAAA,MACA;AAAA,MAAA;AAAA,MAAA;AAAA,MAAA;AAAA,MAAA;AAAA,MAAA;AAAA,IACA,CAAA;AAGAA,kBAAAA,IAAA;AAAA,MACA,aAAA,CAAA;AAAA,MACA,gBAAA,CAAA;AAAA,IACA,CAAA;AAGA,aAAA,QAAA;AAAA,MACA,OAAA;AAAA,MACA,KAAA;AAAA,MACA,OAAA;AAAA,IACA;AAGAC,kBAAAA,UAAA,MAAA;AAEA;AACA;AACA;AACA;IACA,CAAA;AAGA,mBAAA,eAAA;AACA,UAAA;AACAC,sBAAAA,MAAA,MAAA,OAAA,gCAAA,oBAAA;AAGA,cAAA,CAAA,SAAA,UAAA,QAAA,OAAA,UAAA,WAAA,IAAA,IAAA,MAAA,QAAA,IAAA;AAAA,UACAC,YAAAA,QAAA,WAAA;AAAA,UACAA,YAAAA,QAAA,qBAAA;AAAA,UACAA,YAAAA,QAAA,cAAA;AAAA,UACAA,YAAAA,QAAA,aAAA;AAAA,UACAA,YAAAA,QAAA,iBAAA;AAAA,UACAA,YAAAA,QAAA,qBAAA;AAAA,UACAA,YAAAA,QAAA,YAAA;AAAA,QACA,CAAA;AAGA,YAAA,WAAA,QAAA,SAAA,GAAA;AACA,qBAAA,QAAA,QAAA,IAAA,aAAA;AAAA,YACA,IAAA,OAAA;AAAA,YACA,OAAA,OAAA;AAAA,YACA,KAAA,OAAA,OAAA;AAAA,YACA,OAAA,OAAA,SAAA;AAAA,UACA,EAAA;AACAD,8BAAA,MAAA,OAAA,gCAAA,eAAA,WAAA,MAAA,QAAA,GAAA;AAAA,QACA;AAGA,YAAA,YAAA,SAAA,SAAA,GAAA;AACA,sBAAA,QAAA,SAAA,IAAA,cAAA;AAAA,YACA,MAAA,QAAA,QAAA;AAAA,YACA,MAAA,QAAA;AAAA,YACA,KAAA,QAAA,OAAA,kBAAA,QAAA,EAAA;AAAA,UACA,EAAA;AACAA,8BAAA,MAAA,OAAA,gCAAA,gBAAA,YAAA,MAAA,QAAA,GAAA;AAAA,QACA;AAGA,YAAA,YAAA,SAAA,SAAA,GAAA;AACAA,8BAAA,MAAA,OAAA,gCAAA,gBAAA,SAAA,QAAA,KAAA;AAAA,QAEA;AAGA,YAAA,aAAA,UAAA,SAAA,GAAA;AACAA,8BAAA,MAAA,OAAA,gCAAA,gBAAA,UAAA,QAAA,KAAA;AAAA,QAEA;AAGA,YAAA,QAAA,KAAA,SAAA,GAAA;AACAA,8BAAA,MAAA,OAAA,gCAAA,gBAAA,KAAA,QAAA,KAAA;AAAA,QAEA;AAGA,YAAA,UAAA,OAAA,YAAA;AACAA,wBAAAA,MAAA,sBAAA;AAAA,YACA,OAAA,OAAA;AAAA,UACA,CAAA;AACAA,wBAAA,MAAA,MAAA,OAAA,gCAAA,cAAA,OAAA,UAAA;AAAA,QACA;AAGAC,oBAAA,QAAA,eAAA;AAEAD,sBAAAA,MAAA,MAAA,OAAA,gCAAA,kBAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,YAAA;AAAA,UACA,SAAA,WAAA,MAAA;AAAA,UACA,UAAA,YAAA,MAAA;AAAA,UACA,WAAA,qCAAA,WAAA;AAAA,UACA,YAAA,uCAAA,WAAA;AAAA,UACA,OAAA,6BAAA,WAAA;AAAA,UACA,SAAA,iCAAA,eAAA;AAAA,UACA,QAAA,+BAAA,gBAAA;AAAA,QACA,CAAA;AAAA,MAEA,SAAA,OAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,gCAAA,aAAA,KAAA;AAGA,mBAAA,QAAA;AAAA,UACA,EAAA,IAAA,GAAA,OAAA,sCAAA,KAAA,IAAA,OAAA,QAAA;AAAA,UACA,EAAA,IAAA,GAAA,OAAA,sCAAA,KAAA,IAAA,OAAA,SAAA;AAAA,QACA;AAEA,oBAAA,QAAA;AAAA,UACA,EAAA,MAAA,mCAAA,MAAA,QAAA,KAAA,sBAAA;AAAA,UACA,EAAA,MAAA,qCAAA,MAAA,QAAA,KAAA,wBAAA;AAAA,UACA,EAAA,MAAA,uCAAA,MAAA,QAAA,KAAA,0BAAA;AAAA,UACA,EAAA,MAAA,oCAAA,MAAA,QAAA,KAAA,uBAAA;AAAA,UACA,EAAA,MAAA,qCAAA,MAAA,QAAA,KAAA,wBAAA;AAAA,UACA,EAAA,MAAA,oCAAA,MAAA,QAAA,KAAA,uBAAA;AAAA,UACA,EAAA,MAAA,qCAAA,MAAA,QAAA,KAAA,wBAAA;AAAA,UACA,EAAA,MAAA,mCAAA,MAAA,QAAA,KAAA,sBAAA;AAAA,QACA;AAAA,MACA;AAAA,IACA;AAGA,mBAAA,eAAA;AACA,UAAA;AACA,cAAA,SAAA,MAAAE,cAAA,KAAA,QAAA,EAAA,MAAA,GAAA,OAAA,GAAA,CAAA;AACA,YAAA,OAAA,SAAA;AAEAF,wBAAA,MAAA,MAAA,OAAA,gCAAA,aAAA,OAAA,IAAA;AAAA,QACA;AAAA,MACA,SAAA,OAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,gCAAA,aAAA,KAAA;AAAA,MACA;AAAA,IACA;AAGA,mBAAA,mBAAA;AACA,UAAA;AACA,cAAA,SAAA,MAAAE,cAAA,SAAA,QAAA,EAAA,MAAA,GAAA,OAAA,GAAA,CAAA;AACA,YAAA,OAAA,SAAA;AAEAF,wBAAA,MAAA,MAAA,OAAA,gCAAA,aAAA,OAAA,IAAA;AAAA,QACA;AAAA,MACA,SAAA,OAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,gCAAA,aAAA,KAAA;AAAA,MACA;AAAA,IACA;AAGA,mBAAA,eAAA;AACA,UAAA;AAEAA,sBAAAA,MAAA,YAAA;AAAA,UACA,OAAA;AAAA,QACA,CAAA;AAGA,cAAA,CAAA,cAAA,SAAA,IAAA,MAAA,QAAA,IAAA;AAAA,UACAE,UAAA,IAAA,KAAA,UAAA,EAAA,MAAA,GAAA,OAAA,IAAA;AAAA,UACAA,UAAA,IAAA,KAAA,OAAA,EAAA,MAAA,GAAA,OAAA,IAAA;AAAA,QACA,CAAA;AAGA,YAAA,aAAA,SAAA;AACA,yBAAA,QAAA,aAAA,KAAA,IAAA,WAAA;AAAA,YACA,GAAA;AAAA,YACA,QAAA,KAAA,UAAA,CAAA;AAAA,YACA,MAAA,KAAA,QAAA,CAAA;AAAA,YACA,OAAA,KAAA,SAAA;AAAA,YACA,OAAA,KAAA,SAAA;AAAA,YACA,UAAA,KAAA,YAAA;AAAA,UACA,EAAA;AAAA,QACA,OAAA;AACAF,wBAAA,MAAA,MAAA,SAAA,gCAAA,aAAA,aAAA,OAAA;AAAA,QACA;AAGA,YAAA,UAAA,SAAA;AACA,sBAAA,QAAA,UAAA,KAAA,IAAA,WAAA;AAAA,YACA,GAAA;AAAA,YACA,QAAA,KAAA,UAAA,CAAA;AAAA,YACA,MAAA,KAAA,QAAA,CAAA;AAAA,YACA,OAAA,KAAA,SAAA;AAAA,YACA,OAAA,KAAA,SAAA;AAAA,YACA,UAAA,KAAA,YAAA;AAAA,UACA,EAAA;AAAA,QACA,OAAA;AACAA,wBAAA,MAAA,MAAA,SAAA,gCAAA,aAAA,UAAA,OAAA;AAAA,QACA;AAAA,MAEA,SAAA,OAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,gCAAA,aAAA,KAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AAGA,uBAAA,QAAA;AACA,oBAAA,QAAA;MACA,UAAA;AACAA,sBAAA,MAAA,YAAA;AAAA,MACA;AAAA,IACA;AAGA,aAAA,gBAAA,SAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,gCAAA,SAAA,OAAA;AAAA,IAGA;AAGA,UAAA,WAAAF,cAAAA,IAAA,IAAA;AAGA,aAAA,oBAAA;AAEAE,oBAAAA,MAAA,MAAA,OAAA,gCAAA,QAAA;AAAA,IACA;AAGA,aAAA,iBAAA;AAEAA,oBAAAA,MAAA,MAAA,OAAA,gCAAA,SAAA;AAAA,IACA;AAGA,aAAA,kBAAA;AAEAA,oBAAAA,MAAA,MAAA,OAAA,gCAAA,UAAA;AAAA,IACA;AAGA,aAAA,mBAAA;AAEAA,oBAAAA,MAAA,MAAA,OAAA,gCAAA,UAAA;AAAA,IACA;AAGA,aAAA,iBAAA;AACA,oBAAA,QAAA;AAEAA,oBAAAA,MAAA,eAAA,iBAAA,IAAA;AAAA,IACA;AAQA,aAAA,cAAA;AACA,iBAAA,QAAA;AAAA,IACA;AAGA,aAAA,WAAA,KAAA;AACA,UAAA,CAAA;AAAA;AAEAA,oBAAAA,MAAA,WAAA;AAAA,QACA;AAAA,QACA,MAAA,CAAA,QAAA;AACAA,wBAAA,MAAA,MAAA,SAAA,gCAAA,WAAA,GAAA;AAEAA,wBAAAA,MAAA,UAAA;AAAA,YACA;AAAA,YACA,MAAA,CAAA,SAAA;AACAA,4BAAA,MAAA,MAAA,SAAA,gCAAA,iBAAA,IAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA,gBACA,OAAA;AAAA,gBACA,MAAA;AAAA,cACA,CAAA;AAAA,YACA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAQA,aAAA,sBAAA;AACA,yBAAA,QAAA;AAAA,IACA;AAGA,aAAA,eAAA,MAAA;AACA,mBAAA,QAAA,KAAA;AACA;AAGA;AACA;AACA;AACA;IACA;AASA,aAAA,oBAAA;AACA,uBAAA,QAAA;AAAA,IACA;AAGA,aAAA,qBAAA,MAAA;AACA,UAAA,CAAA,KAAA;AAAA;AAEAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,KAAA;AAAA,QACA,MAAA,MAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA,CAAA;AAEA;IACA;AAUA,aAAA,cAAA,GAAA;AACA,oBAAA,QAAA,EAAA,OAAA;AAAA,IACA;AAGA,aAAA,WAAA;AACA,UAAA,CAAA,cAAA,MAAA,QAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AACA;AAAA,MACA;AAGA;IACA;AAGA,aAAA,sBAAA;AAEAA,oBAAAA,MAAA,YAAA;AAAA,QACA,OAAA;AAAA,MACA,CAAA;AAGA,YAAA,gBAAA,CAAA;AAGA,qBAAA,MAAA,QAAA,UAAA;AACA,YAAA,KAAA,WAAA,KAAA,QAAA,SAAA,cAAA,KAAA,GAAA;AACA,wBAAA,KAAA,IAAA;AAAA,QACA;AAAA,MACA,CAAA;AAGA,kBAAA,MAAA,QAAA,UAAA;AACA,YAAA,KAAA,WAAA,KAAA,QAAA,SAAA,cAAA,KAAA,GAAA;AACA,wBAAA,KAAA,IAAA;AAAA,QACA;AAAA,MACA,CAAA;AAGA,iBAAA,MAAA;AACAA,sBAAA,MAAA,YAAA;AAGA,YAAA,cAAA,SAAA,GAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,SAAA,MAAA,cAAA,MAAA;AAAA,YACA,SAAA,CAAA,QAAA;AACA,kBAAA,IAAA,WAAA,cAAA,SAAA,GAAA;AAEA,qCAAA,cAAA,CAAA,CAAA;AAAA,cACA;AAAA,YACA;AAAA,UACA,CAAA;AAAA,QACA,OAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA,GAAA,GAAA;AAAA,IACA;AAGA,aAAA,qBAAA,MAAA;AACAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,yBAAA,KAAA,EAAA;AAAA,QACA,MAAA,CAAA,QAAA;AACAA,wBAAA,MAAA,MAAA,SAAA,gCAAA,eAAA,GAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,aAAA,iBAAA,WAAA;AAEA,UAAA,SAAA,OAAA;AACA,cAAA,QAAAA,oBAAA;AAEA,cAAA,OAAA,6CAAA,EAAA,mBAAA,UAAA;AACA,cAAA,CAAA,MAAA;AACA;AAAA,UACA;AAEA,gBAAAG,gBAAA;AAEA,gBAAA,oBAAA,gBAAA,QAAAA,gBAAA;AAGA,gBAAA,iBAAA,KAAA,MAAA,KAAA;AAGA,cAAA,kBAAA,mBAAA;AACA,qBAAA,MAAA,UAAA,MAAA,iBAAA;AAAA,UACA,OAAA;AAEA,qBAAA,MAAA,UAAA,OAAA,CAAA;AAAA,UACA;AAAA,QACA,CAAA,EAAA,KAAA;AAAA,MACA;AAAA,IACA;AAEAC,kBAAAA,OAAA,MAAA;AAEA,YAAA,aAAAJ,oBAAA;AACA,YAAA,iBAAAA,oBAAA;AAEA,sBAAA,QAAA,WAAA,mBAAA;AACA,mBAAA,SAAA,eAAA,MAAA,gBAAA,SAAA,IAAA,eAAA,SAAA;AACA,uBAAA,QAAA,gBAAA,QAAA,aAAA;AACA,oBAAA,QAAA,eAAA;AAUA;AACA;AACA;AACA;AACA;IACA,CAAA;AAEAK,kBAAAA,OAAA,MAAA;AACA,YAAA,oBAAAL,oBAAA;AACA,sBAAA,QAAA,kBAAA,mBAAA;AAAA,IACA,CAAA;AAEAM,kBAAAA,QAAA,MAAA;AACA,iBAAA,MAAA;AACA,cAAA,oBAAAN,oBAAA;AACA,wBAAA,QAAA,kBAAA,mBAAA;AAAA,MACA,GAAA,EAAA;AAAA,IACA,CAAA;AAEAO,kBAAA,aAAA,CAAA,MAAA;AACA,UAAA,YAAA,OAAA;AACA;AAAA,MACA;AACA,kBAAA,QAAA,WAAA,MAAA;AACA,yBAAA,EAAA,SAAA;AACA,oBAAA,QAAA;AAAA,MACA,GAAA,GAAA;AAAA,IACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/xBA,GAAG,WAAWC,SAAe;"}