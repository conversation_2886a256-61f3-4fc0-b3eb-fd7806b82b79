{"version": 3, "file": "my-red-packets.js", "sources": ["pages/user/my-red-packets.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci9teS1yZWQtcGFja2V0cy52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"my-red-packets\">\r\n    <!-- 顶部标签页 -->\r\n    <view class=\"tabs\">\r\n      <view \r\n        v-for=\"(tab, index) in tabs\" \r\n        :key=\"index\"\r\n        class=\"tab-item\"\r\n        :class=\"{ active: currentTab === index }\"\r\n        @click=\"switchTab(index)\"\r\n      >\r\n        {{ tab.name }}\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 红包列表 -->\r\n    <scroll-view \r\n      class=\"red-packet-list\"\r\n      scroll-y\r\n      @scrolltolower=\"loadMore\"\r\n      :refresher-enabled=\"true\"\r\n      @refresherrefresh=\"onRefresh\"\r\n    >\r\n      <view \r\n        v-for=\"(item, index) in redPacketList\" \r\n        :key=\"index\"\r\n        class=\"red-packet-item\"\r\n        @click=\"viewRedPacketDetail(item)\"\r\n      >\r\n        <view class=\"red-packet-info\">\r\n          <view class=\"title\">{{ item.title }}</view>\r\n          <view class=\"time\">{{ formatTime(item.createTime) }}</view>\r\n          <view class=\"amount\" v-if=\"item.type === 'received'\">\r\n            获得 {{ formatAmount(item.amount) }}元\r\n          </view>\r\n          <view class=\"amount\" v-else>\r\n            发出 {{ formatAmount(item.totalAmount) }}元\r\n          </view>\r\n        </view>\r\n        <view class=\"status\" :class=\"item.status\">\r\n          {{ getStatusText(item.status) }}\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 加载更多 -->\r\n      <view class=\"loading\" v-if=\"loading\">加载中...</view>\r\n      <view class=\"no-more\" v-if=\"!hasMore && redPacketList.length > 0\">没有更多了</view>\r\n      <view class=\"empty\" v-if=\"!loading && redPacketList.length === 0\">\r\n        <image src=\"/static/images/empty-red-packet.png\" mode=\"aspectFit\"></image>\r\n        <text>暂无红包记录</text>\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { getMyRedPackets } from '@/utils/redPacket.js';\r\nimport { formatTime, formatAmount } from '@/utils/format.js';\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      tabs: [\r\n        { name: '收到的红包', type: 'received' },\r\n        { name: '发出的红包', type: 'sent' }\r\n      ],\r\n      currentTab: 0,\r\n      redPacketList: [],\r\n      page: 1,\r\n      pageSize: 20,\r\n      loading: false,\r\n      hasMore: true,\r\n      cache: new Map() // 缓存机制\r\n    };\r\n  },\r\n  \r\n  created() {\r\n    this.loadData();\r\n  },\r\n  \r\n  methods: {\r\n    // 切换标签页\r\n    switchTab(index) {\r\n      if (this.currentTab === index) return;\r\n      this.currentTab = index;\r\n      this.page = 1;\r\n      this.redPacketList = [];\r\n      this.hasMore = true;\r\n      this.loadData();\r\n    },\r\n    \r\n    // 加载数据\r\n    async loadData() {\r\n      if (this.loading || !this.hasMore) return;\r\n      \r\n      this.loading = true;\r\n      const type = this.tabs[this.currentTab].type;\r\n      const cacheKey = `${type}_${this.page}`;\r\n      \r\n      try {\r\n        // 检查缓存\r\n        if (this.cache.has(cacheKey)) {\r\n          const cachedData = this.cache.get(cacheKey);\r\n          this.redPacketList = [...this.redPacketList, ...cachedData.list];\r\n          this.hasMore = cachedData.hasMore;\r\n        } else {\r\n          const res = await getMyRedPackets({\r\n            type,\r\n            page: this.page,\r\n            pageSize: this.pageSize\r\n          });\r\n          \r\n          this.redPacketList = [...this.redPacketList, ...res.list];\r\n          this.hasMore = res.hasMore;\r\n          \r\n          // 设置缓存\r\n          this.cache.set(cacheKey, {\r\n            list: res.list,\r\n            hasMore: res.hasMore\r\n          });\r\n        }\r\n        \r\n        this.page++;\r\n      } catch (err) {\r\n        uni.showToast({\r\n          title: '加载失败',\r\n          icon: 'none'\r\n        });\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    \r\n    // 下拉刷新\r\n    async onRefresh() {\r\n      this.page = 1;\r\n      this.redPacketList = [];\r\n      this.hasMore = true;\r\n      this.cache.clear(); // 清除缓存\r\n      await this.loadData();\r\n      uni.stopPullDownRefresh();\r\n    },\r\n    \r\n    // 加载更多\r\n    loadMore() {\r\n      this.loadData();\r\n    },\r\n    \r\n    // 查看红包详情\r\n    viewRedPacketDetail(item) {\r\n      uni.navigateTo({\r\n        url: `/pages/red-packet/detail?id=${item.id}`\r\n      });\r\n    },\r\n    \r\n    // 格式化时间\r\n    formatTime,\r\n    \r\n    // 格式化金额\r\n    formatAmount,\r\n    \r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        pending: '待领取',\r\n        received: '已领取',\r\n        expired: '已过期',\r\n        completed: '已领完'\r\n      };\r\n      return statusMap[status] || status;\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.my-red-packets {\r\n  min-height: 100vh;\r\n  background-color: #f8f8f8;\r\n  \r\n  .tabs {\r\n    display: flex;\r\n    background-color: #fff;\r\n    padding: 20rpx 0;\r\n    position: sticky;\r\n    top: 0;\r\n    z-index: 1;\r\n    \r\n    .tab-item {\r\n      flex: 1;\r\n      text-align: center;\r\n      font-size: 28rpx;\r\n      color: #666;\r\n      position: relative;\r\n      padding: 20rpx 0;\r\n      \r\n      &.active {\r\n        color: #FF6347;\r\n        font-weight: 500;\r\n        \r\n        &::after {\r\n          content: '';\r\n          position: absolute;\r\n          bottom: 0;\r\n          left: 50%;\r\n          transform: translateX(-50%);\r\n          width: 40rpx;\r\n          height: 4rpx;\r\n          background-color: #FF6347;\r\n          border-radius: 2rpx;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .red-packet-list {\r\n    height: calc(100vh - 100rpx);\r\n    \r\n    .red-packet-item {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 30rpx;\r\n      background-color: #fff;\r\n      margin-bottom: 2rpx;\r\n      \r\n      .red-packet-info {\r\n        flex: 1;\r\n        \r\n        .title {\r\n          font-size: 28rpx;\r\n          color: #333;\r\n          margin-bottom: 10rpx;\r\n        }\r\n        \r\n        .time {\r\n          font-size: 24rpx;\r\n          color: #999;\r\n          margin-bottom: 10rpx;\r\n        }\r\n        \r\n        .amount {\r\n          font-size: 32rpx;\r\n          color: #FF6347;\r\n          font-weight: 500;\r\n        }\r\n      }\r\n      \r\n      .status {\r\n        font-size: 24rpx;\r\n        padding: 4rpx 12rpx;\r\n        border-radius: 4rpx;\r\n        \r\n        &.pending {\r\n          color: #FF6347;\r\n          background-color: #FFF0F5;\r\n        }\r\n        \r\n        &.received {\r\n          color: #52c41a;\r\n          background-color: #f6ffed;\r\n        }\r\n        \r\n        &.expired {\r\n          color: #999;\r\n          background-color: #f5f5f5;\r\n        }\r\n        \r\n        &.completed {\r\n          color: #1890ff;\r\n          background-color: #e6f7ff;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .loading, .no-more {\r\n      text-align: center;\r\n      padding: 30rpx;\r\n      color: #999;\r\n      font-size: 24rpx;\r\n    }\r\n    \r\n    .empty {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      padding: 100rpx 0;\r\n      \r\n      image {\r\n        width: 200rpx;\r\n        height: 200rpx;\r\n        margin-bottom: 20rpx;\r\n      }\r\n      \r\n      text {\r\n        font-size: 28rpx;\r\n        color: #999;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/user/my-red-packets.vue'\nwx.createPage(MiniProgramPage)"], "names": ["getMyRedPackets", "uni", "formatTime", "formatAmount"], "mappings": ";;;;;AA2DA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,MAAM;AAAA,QACJ,EAAE,MAAM,SAAS,MAAM,WAAY;AAAA,QACnC,EAAE,MAAM,SAAS,MAAM,OAAO;AAAA,MAC/B;AAAA,MACD,YAAY;AAAA,MACZ,eAAe,CAAE;AAAA,MACjB,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO,oBAAI,IAAI;AAAA;AAAA;EAElB;AAAA,EAED,UAAU;AACR,SAAK,SAAQ;AAAA,EACd;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,UAAU,OAAO;AACf,UAAI,KAAK,eAAe;AAAO;AAC/B,WAAK,aAAa;AAClB,WAAK,OAAO;AACZ,WAAK,gBAAgB;AACrB,WAAK,UAAU;AACf,WAAK,SAAQ;AAAA,IACd;AAAA;AAAA,IAGD,MAAM,WAAW;AACf,UAAI,KAAK,WAAW,CAAC,KAAK;AAAS;AAEnC,WAAK,UAAU;AACf,YAAM,OAAO,KAAK,KAAK,KAAK,UAAU,EAAE;AACxC,YAAM,WAAW,GAAG,IAAI,IAAI,KAAK,IAAI;AAErC,UAAI;AAEF,YAAI,KAAK,MAAM,IAAI,QAAQ,GAAG;AAC5B,gBAAM,aAAa,KAAK,MAAM,IAAI,QAAQ;AAC1C,eAAK,gBAAgB,CAAC,GAAG,KAAK,eAAe,GAAG,WAAW,IAAI;AAC/D,eAAK,UAAU,WAAW;AAAA,eACrB;AACL,gBAAM,MAAM,MAAMA,gCAAgB;AAAA,YAChC;AAAA,YACA,MAAM,KAAK;AAAA,YACX,UAAU,KAAK;AAAA,UACjB,CAAC;AAED,eAAK,gBAAgB,CAAC,GAAG,KAAK,eAAe,GAAG,IAAI,IAAI;AACxD,eAAK,UAAU,IAAI;AAGnB,eAAK,MAAM,IAAI,UAAU;AAAA,YACvB,MAAM,IAAI;AAAA,YACV,SAAS,IAAI;AAAA,UACf,CAAC;AAAA,QACH;AAEA,aAAK;AAAA,MACL,SAAO,KAAK;AACZC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,YAAY;AAChB,WAAK,OAAO;AACZ,WAAK,gBAAgB;AACrB,WAAK,UAAU;AACf,WAAK,MAAM;AACX,YAAM,KAAK;AACXA,oBAAG,MAAC,oBAAmB;AAAA,IACxB;AAAA;AAAA,IAGD,WAAW;AACT,WAAK,SAAQ;AAAA,IACd;AAAA;AAAA,IAGD,oBAAoB,MAAM;AACxBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,+BAA+B,KAAK,EAAE;AAAA,MAC7C,CAAC;AAAA,IACF;AAAA;AAAA,gBAGDC,aAAU;AAAA;AAAA,kBAGVC,aAAY;AAAA;AAAA,IAGZ,cAAc,QAAQ;AACpB,YAAM,YAAY;AAAA,QAChB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,SAAS;AAAA,QACT,WAAW;AAAA;AAEb,aAAO,UAAU,MAAM,KAAK;AAAA,IAC9B;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3KA,GAAG,WAAW,eAAe;"}