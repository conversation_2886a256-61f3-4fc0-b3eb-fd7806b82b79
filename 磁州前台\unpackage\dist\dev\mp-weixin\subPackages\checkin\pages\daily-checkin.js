"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
const _sfc_main = {
  __name: "daily-checkin",
  setup(__props) {
    const checkinPoints = common_vendor.ref(10);
    const weeklyBonus = common_vendor.ref(30);
    const monthlyBonus = common_vendor.ref(100);
    const continuousDays = common_vendor.ref(3);
    const hasCheckedToday = common_vendor.ref(false);
    const currentDate = /* @__PURE__ */ new Date();
    const currentYear = common_vendor.ref(currentDate.getFullYear());
    const currentMonth = common_vendor.ref(currentDate.getMonth() + 1);
    const calendarDays = common_vendor.ref([]);
    const generateCalendar = () => {
      const year = currentYear.value;
      const month = currentMonth.value;
      const firstDay = new Date(year, month - 1, 1).getDay();
      const lastDate = new Date(year, month, 0).getDate();
      const days = [];
      for (let i = 0; i < firstDay; i++) {
        days.push({ date: null });
      }
      const today = /* @__PURE__ */ new Date();
      const isCurrentMonth = today.getFullYear() === year && today.getMonth() + 1 === month;
      const todayDate = today.getDate();
      for (let i = 1; i <= lastDate; i++) {
        const isToday = isCurrentMonth && i === todayDate;
        const isPast = isCurrentMonth ? i < todayDate : new Date(year, month - 1, i) < today;
        days.push({
          date: i,
          isToday,
          disabled: !isPast && !isToday,
          checked: isPast || isToday && hasCheckedToday.value
        });
      }
      const totalCells = 42;
      const remainingCells = totalCells - days.length;
      for (let i = 0; i < remainingCells; i++) {
        days.push({ date: null });
      }
      calendarDays.value = days;
    };
    const checkinRecords = common_vendor.ref([
      {
        date: "2023-12-20",
        time: "08:15",
        points: 10
      },
      {
        date: "2023-12-19",
        time: "09:23",
        points: 10
      },
      {
        date: "2023-12-18",
        time: "07:45",
        points: 10
      },
      {
        date: "2023-12-15",
        time: "12:30",
        points: 10
      },
      {
        date: "2023-12-14",
        time: "08:05",
        points: 10
      }
    ]);
    const doCheckin = () => {
      if (hasCheckedToday.value) {
        common_vendor.index.showToast({
          title: "今日已签到",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "签到中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        hasCheckedToday.value = true;
        continuousDays.value += 1;
        const todayIndex = calendarDays.value.findIndex((day) => day.isToday);
        if (todayIndex !== -1) {
          calendarDays.value[todayIndex].checked = true;
        }
        const now = /* @__PURE__ */ new Date();
        const dateStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, "0")}-${String(now.getDate()).padStart(2, "0")}`;
        const timeStr = `${String(now.getHours()).padStart(2, "0")}:${String(now.getMinutes()).padStart(2, "0")}`;
        checkinRecords.value.unshift({
          date: dateStr,
          time: timeStr,
          points: checkinPoints.value
        });
        common_vendor.index.showToast({
          title: `签到成功，获得${checkinPoints.value}积分`,
          icon: "success"
        });
        if (continuousDays.value === 7) {
          setTimeout(() => {
            common_vendor.index.showToast({
              title: `连续签到7天，额外获得${weeklyBonus.value}积分`,
              icon: "success"
            });
          }, 1500);
        } else if (continuousDays.value === 30) {
          setTimeout(() => {
            common_vendor.index.showToast({
              title: `连续签到30天，额外获得${monthlyBonus.value}积分`,
              icon: "success"
            });
          }, 1500);
        }
      }, 1e3);
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    common_vendor.onMounted(() => {
      generateCalendar();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          d: "M15 18L9 12L15 6",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        b: common_vendor.p({
          width: "24",
          height: "24",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        c: common_vendor.o(goBack),
        d: common_vendor.t(continuousDays.value),
        e: common_vendor.t(currentYear.value),
        f: common_vendor.t(currentMonth.value),
        g: common_vendor.f(["日", "一", "二", "三", "四", "五", "六"], (day, k0, i0) => {
          return {
            a: common_vendor.t(day),
            b: day
          };
        }),
        h: common_vendor.f(calendarDays.value, (day, index, i0) => {
          return common_vendor.e({
            a: day.date
          }, day.date ? {
            b: common_vendor.t(day.date)
          } : {}, {
            c: day.checked
          }, day.checked ? {} : {}, {
            d: index,
            e: !day.date ? 1 : "",
            f: day.checked ? 1 : "",
            g: day.isToday ? 1 : "",
            h: day.disabled ? 1 : ""
          });
        }),
        i: common_vendor.t(hasCheckedToday.value ? "今日已签到" : "立即签到"),
        j: !hasCheckedToday.value
      }, !hasCheckedToday.value ? {
        k: common_vendor.t(checkinPoints.value)
      } : {}, {
        l: hasCheckedToday.value ? 1 : "",
        m: common_vendor.o(doCheckin),
        n: common_vendor.t(checkinPoints.value),
        o: common_vendor.t(weeklyBonus.value),
        p: common_vendor.t(monthlyBonus.value),
        q: common_vendor.f(checkinRecords.value, (record, index, i0) => {
          return {
            a: common_vendor.t(record.date),
            b: common_vendor.t(record.time),
            c: common_vendor.t(record.points),
            d: index
          };
        })
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/checkin/pages/daily-checkin.js.map
