<template>
  <div class="comment-section">
    <!-- 评论输入框 -->
    <div class="comment-input">
      <el-input
        v-model="commentContent"
        type="textarea"
        :rows="3"
        placeholder="写下你的评论..."
        :maxlength="500"
        show-word-limit
      />
      <div class="comment-actions">
        <el-button type="primary" @click="submitComment" :loading="submitting">
          发表评论
        </el-button>
      </div>
    </div>

    <!-- 评论列表 -->
    <div class="comment-list" v-loading="loading">
      <div v-if="comments.length === 0" class="no-comments">
        暂无评论，快来发表第一条评论吧！
      </div>
      <div v-else class="comment-items">
        <div v-for="comment in comments" :key="comment.id" class="comment-item">
          <div class="comment-header">
            <el-avatar :src="comment.avatar" :size="40" />
            <div class="comment-info">
              <span class="username">{{ comment.username }}</span>
              <span class="time">{{ formatTime(comment.createdAt) }}</span>
            </div>
          </div>
          <div class="comment-content">{{ comment.content }}</div>
          <div class="comment-footer">
            <el-button type="text" @click="showReplyInput(comment)">
              回复
            </el-button>
            <el-button
              v-if="comment.userId === currentUser?.id"
              type="text"
              @click="editComment(comment)"
            >
              编辑
            </el-button>
            <el-button
              v-if="comment.userId === currentUser?.id"
              type="text"
              @click="deleteComment(comment.id)"
            >
              删除
            </el-button>
          </div>

          <!-- 回复列表 -->
          <div v-if="comment.replies && comment.replies.length > 0" class="reply-list">
            <div v-for="reply in comment.replies" :key="reply.id" class="reply-item">
              <div class="reply-header">
                <el-avatar :src="reply.avatar" :size="30" />
                <div class="reply-info">
                  <span class="username">{{ reply.username }}</span>
                  <span class="time">{{ formatTime(reply.createdAt) }}</span>
                </div>
              </div>
              <div class="reply-content">{{ reply.content }}</div>
              <div class="reply-footer">
                <el-button type="text" @click="showReplyInput(comment, reply)">
                  回复
                </el-button>
                <el-button
                  v-if="reply.userId === currentUser?.id"
                  type="text"
                  @click="editComment(reply)"
                >
                  编辑
                </el-button>
                <el-button
                  v-if="reply.userId === currentUser?.id"
                  type="text"
                  @click="deleteComment(reply.id)"
                >
                  删除
                </el-button>
              </div>
            </div>
          </div>

          <!-- 回复输入框 -->
          <div v-if="activeReplyId === comment.id" class="reply-input">
            <el-input
              v-model="replyContent"
              type="textarea"
              :rows="2"
              :placeholder="replyTo ? `回复 ${replyTo.username}：` : '写下你的回复...'"
              :maxlength="200"
              show-word-limit
            />
            <div class="reply-actions">
              <el-button @click="cancelReply">取消</el-button>
              <el-button type="primary" @click="submitReply(comment)" :loading="submitting">
                发表回复
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination" v-if="total > 0">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 编辑评论对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑评论"
      width="500px"
    >
      <el-input
        v-model="editContent"
        type="textarea"
        :rows="4"
        :maxlength="500"
        show-word-limit
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updateComment" :loading="submitting">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import axios from 'axios'

const props = defineProps({
  targetId: {
    type: [Number, String],
    required: true
  },
  targetType: {
    type: String,
    required: true,
    validator: (value) => ['post', 'service', 'shop'].includes(value)
  }
})

const userStore = useUserStore()
const currentUser = computed(() => userStore.user)

// 评论列表数据
const comments = ref([])
const loading = ref(false)
const submitting = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 评论输入
const commentContent = ref('')
const replyContent = ref('')
const activeReplyId = ref(null)
const replyTo = ref(null)

// 编辑评论
const editDialogVisible = ref(false)
const editContent = ref('')
const editingComment = ref(null)

// 获取评论列表
const fetchComments = async () => {
  try {
    loading.value = true
    const response = await axios.get(`/api/comments/${props.targetType}/${props.targetId}`, {
      params: {
        page: currentPage.value,
        limit: pageSize.value
      }
    })
    comments.value = response.data.data.comments
    total.value = response.data.data.pagination.total
  } catch (error) {
    ElMessage.error('获取评论列表失败')
    console.error('获取评论列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 提交评论
const submitComment = async () => {
  if (!commentContent.value.trim()) {
    ElMessage.warning('请输入评论内容')
    return
  }

  try {
    submitting.value = true
    await axios.post('/api/comments', {
      content: commentContent.value,
      targetId: props.targetId,
      targetType: props.targetType
    })
    ElMessage.success('评论发表成功')
    commentContent.value = ''
    fetchComments()
  } catch (error) {
    ElMessage.error('评论发表失败')
    console.error('评论发表失败:', error)
  } finally {
    submitting.value = false
  }
}

// 显示回复输入框
const showReplyInput = (comment, reply = null) => {
  activeReplyId.value = comment.id
  replyTo.value = reply
  replyContent.value = ''
}

// 取消回复
const cancelReply = () => {
  activeReplyId.value = null
  replyTo.value = null
  replyContent.value = ''
}

// 提交回复
const submitReply = async (comment) => {
  if (!replyContent.value.trim()) {
    ElMessage.warning('请输入回复内容')
    return
  }

  try {
    submitting.value = true
    await axios.post('/api/comments', {
      content: replyContent.value,
      targetId: props.targetId,
      targetType: props.targetType,
      parentId: comment.id
    })
    ElMessage.success('回复发表成功')
    cancelReply()
    fetchComments()
  } catch (error) {
    ElMessage.error('回复发表失败')
    console.error('回复发表失败:', error)
  } finally {
    submitting.value = false
  }
}

// 编辑评论
const editComment = (comment) => {
  editingComment.value = comment
  editContent.value = comment.content
  editDialogVisible.value = true
}

// 更新评论
const updateComment = async () => {
  if (!editContent.value.trim()) {
    ElMessage.warning('请输入评论内容')
    return
  }

  try {
    submitting.value = true
    await axios.put(`/api/comments/${editingComment.value.id}`, {
      content: editContent.value
    })
    ElMessage.success('评论更新成功')
    editDialogVisible.value = false
    fetchComments()
  } catch (error) {
    ElMessage.error('评论更新失败')
    console.error('评论更新失败:', error)
  } finally {
    submitting.value = false
  }
}

// 删除评论
const deleteComment = async (commentId) => {
  try {
    await ElMessageBox.confirm('确定要删除这条评论吗？', '提示', {
      type: 'warning'
    })
    
    await axios.delete(`/api/comments/${commentId}`)
    ElMessage.success('评论删除成功')
    fetchComments()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('评论删除失败')
      console.error('评论删除失败:', error)
    }
  }
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchComments()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchComments()
}

// 格式化时间
const formatTime = (time) => {
  return formatDistanceToNow(new Date(time), {
    addSuffix: true,
    locale: zhCN
  })
}

onMounted(() => {
  fetchComments()
})
</script>

<style scoped>
.comment-section {
  margin-top: 20px;
}

.comment-input {
  margin-bottom: 20px;
}

.comment-actions {
  margin-top: 10px;
  text-align: right;
}

.comment-list {
  margin-top: 20px;
}

.no-comments {
  text-align: center;
  color: #909399;
  padding: 20px 0;
}

.comment-item {
  padding: 15px 0;
  border-bottom: 1px solid #ebeef5;
}

.comment-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.comment-info {
  margin-left: 10px;
}

.username {
  font-weight: bold;
  margin-right: 10px;
}

.time {
  color: #909399;
  font-size: 12px;
}

.comment-content {
  margin: 10px 0;
  line-height: 1.6;
}

.comment-footer {
  display: flex;
  gap: 10px;
}

.reply-list {
  margin-left: 50px;
  margin-top: 10px;
  padding-left: 10px;
  border-left: 2px solid #ebeef5;
}

.reply-item {
  padding: 10px 0;
}

.reply-header {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.reply-info {
  margin-left: 10px;
}

.reply-content {
  margin: 5px 0;
  line-height: 1.6;
}

.reply-footer {
  display: flex;
  gap: 10px;
}

.reply-input {
  margin-top: 10px;
  margin-left: 50px;
}

.reply-actions {
  margin-top: 10px;
  text-align: right;
}

.pagination {
  margin-top: 20px;
  text-align: center;
}
</style> 