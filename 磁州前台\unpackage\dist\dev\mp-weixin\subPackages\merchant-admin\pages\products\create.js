"use strict";
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = {
  __name: "create",
  setup(__props) {
    const productForm = common_vendor.reactive({
      name: "",
      category: "",
      price: "",
      originalPrice: "",
      stock: 0,
      images: [],
      description: "",
      specs: [],
      purchaseLimit: 0
    });
    const marketingTags = common_vendor.reactive([
      { id: 1, name: "新品" },
      { id: 2, name: "热卖" },
      { id: 3, name: "限时" },
      { id: 4, name: "促销" },
      { id: 5, name: "包邮" },
      { id: 6, name: "品质保障" }
    ]);
    const selectedTags = common_vendor.reactive([]);
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const showHelp = () => {
      common_vendor.index.showToast({
        title: "帮助中心即将上线",
        icon: "none"
      });
    };
    const chooseImage = () => {
      common_vendor.index.chooseImage({
        count: 9 - productForm.images.length,
        sizeType: ["original", "compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          productForm.images = [...productForm.images, ...res.tempFilePaths];
        }
      });
    };
    const removeImage = (index) => {
      productForm.images.splice(index, 1);
    };
    const showCategoryPicker = () => {
      common_vendor.index.showActionSheet({
        itemList: ["食品饮料", "服装服饰", "美妆个护", "家居家纺", "数码电器", "其他分类"],
        success: (res) => {
          const categories = ["食品饮料", "服装服饰", "美妆个护", "家居家纺", "数码电器", "其他分类"];
          productForm.category = categories[res.tapIndex];
        }
      });
    };
    const increaseStock = () => {
      productForm.stock++;
    };
    const decreaseStock = () => {
      if (productForm.stock > 0) {
        productForm.stock--;
      }
    };
    const saveDraft = () => {
      common_vendor.index.showToast({
        title: "已保存草稿",
        icon: "success"
      });
    };
    const addSpec = () => {
      if (productForm.specs.length >= 10) {
        return common_vendor.index.showToast({
          title: "最多添加10个规格参数",
          icon: "none"
        });
      }
      productForm.specs.push({ key: "", value: "" });
    };
    const removeSpec = (index) => {
      productForm.specs.splice(index, 1);
    };
    const toggleTag = (tagId) => {
      const index = selectedTags.indexOf(tagId);
      if (index > -1) {
        selectedTags.splice(index, 1);
      } else {
        if (selectedTags.length >= 3) {
          return common_vendor.index.showToast({
            title: "最多选择3个标签",
            icon: "none"
          });
        }
        selectedTags.push(tagId);
      }
    };
    const publishProduct = () => {
      if (!productForm.name) {
        return common_vendor.index.showToast({ title: "请输入商品名称", icon: "none" });
      }
      if (!productForm.category) {
        return common_vendor.index.showToast({ title: "请选择商品分类", icon: "none" });
      }
      if (!productForm.price) {
        return common_vendor.index.showToast({ title: "请输入商品价格", icon: "none" });
      }
      if (productForm.images.length === 0) {
        return common_vendor.index.showToast({ title: "请至少上传一张商品图片", icon: "none" });
      }
      if (!productForm.description) {
        return common_vendor.index.showToast({ title: "请输入商品描述", icon: "none" });
      }
      const productData = {
        ...productForm,
        tags: selectedTags.map((id) => {
          const tag = marketingTags.find((t) => t.id === id);
          return tag ? tag.name : "";
        }).filter(Boolean)
      };
      common_vendor.index.__f__("log", "at subPackages/merchant-admin/pages/products/create.vue:401", "商品数据:", productData);
      common_vendor.index.showLoading({ title: "正在发布..." });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({ title: "发布成功", icon: "success" });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      }, 2e3);
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(goBack),
        b: common_vendor.o(showHelp),
        c: common_vendor.f(productForm.images, (image, index, i0) => {
          return {
            a: image,
            b: common_vendor.o(($event) => removeImage(index), index),
            c: index
          };
        }),
        d: productForm.images.length < 9
      }, productForm.images.length < 9 ? {
        e: common_vendor.o(chooseImage)
      } : {}, {
        f: productForm.name,
        g: common_vendor.o(($event) => productForm.name = $event.detail.value),
        h: common_vendor.t(productForm.name.length),
        i: common_vendor.t(productForm.category || "请选择商品分类"),
        j: !productForm.category ? 1 : "",
        k: common_vendor.o(showCategoryPicker),
        l: productForm.price,
        m: common_vendor.o(($event) => productForm.price = $event.detail.value),
        n: productForm.originalPrice,
        o: common_vendor.o(($event) => productForm.originalPrice = $event.detail.value),
        p: common_vendor.o(decreaseStock),
        q: productForm.stock <= 0 ? 1 : "",
        r: productForm.stock,
        s: common_vendor.o(($event) => productForm.stock = $event.detail.value),
        t: common_vendor.o(increaseStock),
        v: productForm.description,
        w: common_vendor.o(($event) => productForm.description = $event.detail.value),
        x: common_vendor.t(productForm.description ? productForm.description.length : 0),
        y: common_vendor.o(addSpec),
        z: common_vendor.f(productForm.specs, (spec, index, i0) => {
          return {
            a: spec.key,
            b: common_vendor.o(($event) => spec.key = $event.detail.value, index),
            c: spec.value,
            d: common_vendor.o(($event) => spec.value = $event.detail.value, index),
            e: common_vendor.o(($event) => removeSpec(index), index),
            f: index
          };
        }),
        A: productForm.specs.length === 0
      }, productForm.specs.length === 0 ? {} : {}, {
        B: common_vendor.f(marketingTags, (tag, index, i0) => {
          return {
            a: common_vendor.t(tag.name),
            b: index,
            c: selectedTags.includes(tag.id) ? 1 : "",
            d: common_vendor.o(($event) => toggleTag(tag.id), index)
          };
        }),
        C: productForm.purchaseLimit,
        D: common_vendor.o(($event) => productForm.purchaseLimit = $event.detail.value),
        E: common_vendor.o(saveDraft),
        F: common_vendor.o(publishProduct)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin/pages/products/create.js.map
