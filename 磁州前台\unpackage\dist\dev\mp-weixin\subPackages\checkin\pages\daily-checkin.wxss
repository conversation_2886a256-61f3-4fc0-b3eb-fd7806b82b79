/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 页面容器 */
.checkin-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
  position: relative;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  color: white;
  padding: 48px 20px 16px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(255, 120, 0, 0.15);
}
.navbar-left {
  width: 40px;
}
.back-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.navbar-title {
  flex: 1;
  text-align: center;
}
.title-text {
  font-size: 18px;
  font-weight: 600;
}
.navbar-right {
  width: 40px;
}

/* 内容区域 */
.content-area {
  flex: 1;
  padding: 16px;
  box-sizing: border-box;
  height: calc(100vh - 80px);
}

/* 签到卡片 */
.checkin-card {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.card-header {
  margin-bottom: 16px;
}
.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}
.card-subtitle {
  font-size: 14px;
  color: #FF7600;
  margin-top: 4px;
  display: block;
}

/* 日历部分 */
.calendar-section {
  margin-bottom: 20px;
}
.month-header {
  text-align: center;
  margin-bottom: 12px;
}
.month-text {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
}
.weekdays {
  display: flex;
  margin-bottom: 8px;
}
.weekday {
  flex: 1;
  text-align: center;
  font-size: 14px;
  color: #999999;
}
.days-grid {
  display: flex;
  flex-wrap: wrap;
}
.day-item {
  width: 14.28%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.day-number {
  font-size: 14px;
  color: #333333;
}
.empty {
  visibility: hidden;
}
.checked {
  position: relative;
}
.checked .day-number {
  color: #FFFFFF;
}
.checked::before {
  content: "";
  position: absolute;
  width: 30px;
  height: 30px;
  border-radius: 15px;
  background-color: #FF7600;
  z-index: -1;
}
.today:not(.checked) {
  position: relative;
}
.today:not(.checked) .day-number {
  color: #FF7600;
  font-weight: 600;
}
.today:not(.checked)::before {
  content: "";
  position: absolute;
  width: 30px;
  height: 30px;
  border-radius: 15px;
  border: 1px solid #FF7600;
  z-index: -1;
}
.disabled .day-number {
  color: #CCCCCC;
}

/* 签到按钮 */
.checkin-button-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
.checkin-button {
  width: 200px;
  height: 50px;
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  border-radius: 25px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(255, 120, 0, 0.3);
}
.checkin-button.disabled {
  background: #CCCCCC;
  box-shadow: none;
}
.points-text {
  font-size: 12px;
  margin-top: 2px;
}

/* 规则卡片 */
.rules-card {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.rules-content {
  margin-top: 12px;
}
.rule-item {
  display: flex;
  margin-bottom: 8px;
}
.rule-number {
  color: #FF7600;
  margin-right: 8px;
}
.rule-text {
  color: #666666;
  flex: 1;
}

/* 记录卡片 */
.records-card {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.records-list {
  margin-top: 12px;
}
.record-item {
  display: flex;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #F0F0F0;
}
.record-item:last-child {
  border-bottom: none;
}
.record-date {
  display: flex;
  flex-direction: column;
}
.date-text {
  font-size: 14px;
  color: #333333;
  margin-bottom: 4px;
}
.time-text {
  font-size: 12px;
  color: #999999;
}
.record-points {
  display: flex;
  align-items: center;
}
.points-value {
  font-size: 16px;
  font-weight: 600;
  color: #FF7600;
  margin-right: 4px;
}
.points-label {
  font-size: 12px;
  color: #999999;
}

/* 底部空间 */
.bottom-space {
  height: 20px;
}