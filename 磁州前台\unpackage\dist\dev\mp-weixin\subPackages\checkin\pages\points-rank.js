"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  __name: "points-rank",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const maxRankDisplay = common_vendor.ref(100);
    const rankList = common_vendor.ref([
      { id: 1, nickname: "小明", avatar: "/static/images/avatar/avatar1.png", points: 3280 },
      { id: 2, nickname: "小红", avatar: "/static/images/avatar/avatar2.png", points: 2990 },
      { id: 3, nickname: "小刚", avatar: "/static/images/avatar/avatar3.png", points: 2650 },
      { id: 4, nickname: "小美", avatar: "/static/images/avatar/avatar4.png", points: 2200 },
      { id: 5, nickname: "小李", avatar: "/static/images/avatar/avatar5.png", points: 2100 },
      { id: 6, nickname: "小王", avatar: "/static/images/avatar/avatar6.png", points: 2e3 },
      { id: 7, nickname: "小陈", avatar: "/static/images/avatar/avatar7.png", points: 1880 },
      { id: 8, nickname: "小赵", avatar: "/static/images/avatar/avatar8.png", points: 1700 },
      { id: 9, nickname: "小孙", avatar: "/static/images/avatar/avatar9.png", points: 1600 },
      { id: 10, nickname: "小周", avatar: "/static/images/avatar/avatar10.png", points: 1500 }
    ]);
    const top3 = common_vendor.computed(() => {
      return rankList.value.slice(0, 3);
    });
    const restList = common_vendor.computed(() => {
      return rankList.value.slice(3, maxRankDisplay.value);
    });
    common_vendor.onMounted(() => {
      try {
        const sysInfo = common_vendor.index.getSystemInfoSync();
        statusBarHeight.value = sysInfo.statusBarHeight || 20;
      } catch (e) {
        statusBarHeight.value = 20;
      }
    });
    function goBack() {
      common_vendor.index.navigateBack();
    }
    function formatPoints(points) {
      return points + "分";
    }
    return (_ctx, _cache) => {
      return {
        a: statusBarHeight.value + 44 + "px",
        b: common_assets._imports_0$7,
        c: common_vendor.o(goBack),
        d: statusBarHeight.value + "px",
        e: top3.value[1].avatar,
        f: common_vendor.t(top3.value[1].nickname),
        g: common_vendor.t(formatPoints(top3.value[1].points)),
        h: top3.value[0].avatar,
        i: common_vendor.t(top3.value[0].nickname),
        j: common_vendor.t(formatPoints(top3.value[0].points)),
        k: top3.value[2].avatar,
        l: common_vendor.t(top3.value[2].nickname),
        m: common_vendor.t(formatPoints(top3.value[2].points)),
        n: common_vendor.f(restList.value, (user, idx, i0) => {
          return {
            a: common_vendor.t(idx + 4),
            b: user.avatar,
            c: common_vendor.t(user.nickname),
            d: common_vendor.t(formatPoints(user.points)),
            e: user.id
          };
        }),
        o: statusBarHeight.value + 44 + "px"
      };
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/checkin/pages/points-rank.js.map
