<view class="flash-detail-container"><view class="navbar"><view class="navbar-left"><view class="back-button" bindtap="{{c}}"><svg wx:if="{{b}}" u-s="{{['d']}}" u-i="28927484-0" bind:__l="__l" u-p="{{b}}"><path wx:if="{{a}}" u-i="28927484-1,28927484-0" bind:__l="__l" u-p="{{a}}"/></svg></view></view><view class="navbar-title"><text class="title-text">秒杀活动详情</text></view><view class="navbar-right"><view class="more-button" bindtap="{{h}}"><svg wx:if="{{g}}" u-s="{{['d']}}" u-i="28927484-2" bind:__l="__l" u-p="{{g}}"><circle wx:if="{{d}}" u-i="28927484-3,28927484-2" bind:__l="__l" u-p="{{d}}"/><circle wx:if="{{e}}" u-i="28927484-4,28927484-2" bind:__l="__l" u-p="{{e}}"/><circle wx:if="{{f}}" u-i="28927484-5,28927484-2" bind:__l="__l" u-p="{{f}}"/></svg></view></view></view><scroll-view scroll-y class="content-area"><view class="product-section"><swiper class="product-swiper" indicator-dots autoplay circular><swiper-item wx:for="{{i}}" wx:for-item="image" wx:key="b"><image class="product-image" src="{{image.a}}" mode="aspectFill"></image></swiper-item></swiper><view class="product-info"><view class="{{['status-tag', k]}}">{{j}}</view><view class="product-name">{{l}}</view><view class="product-price"><text class="price-now">¥{{m}}</text><text class="price-original">¥{{n}}</text><text class="discount-tag">{{o}}</text></view><view wx:if="{{p}}" class="countdown-section"><view class="countdown-label">{{q}}</view><view class="countdown-timer"><text class="time-block">{{r}}</text><text class="time-separator">天</text><text class="time-block">{{s}}</text><text class="time-separator">:</text><text class="time-block">{{t}}</text><text class="time-separator">:</text><text class="time-block">{{v}}</text></view></view></view></view><view class="sales-section"><view class="sales-header"><text class="section-title">销售数据</text><view class="refresh-button" bindtap="{{A}}"><svg wx:if="{{z}}" u-s="{{['d']}}" u-i="28927484-6" bind:__l="__l" u-p="{{z}}"><path wx:if="{{w}}" u-i="28927484-7,28927484-6" bind:__l="__l" u-p="{{w}}"/><path wx:if="{{x}}" u-i="28927484-8,28927484-6" bind:__l="__l" u-p="{{x}}"/><path wx:if="{{y}}" u-i="28927484-9,28927484-6" bind:__l="__l" u-p="{{y}}"/></svg><text class="refresh-text">刷新</text></view></view><view class="sales-stats"><view class="stat-item"><view class="stat-value">{{B}}</view><view class="stat-label">浏览量</view></view><view class="stat-item"><view class="stat-value">{{C}}</view><view class="stat-label">已售数量</view></view><view class="stat-item"><view class="stat-value">{{D}}</view><view class="stat-label">剩余库存</view></view><view class="stat-item"><view class="stat-value">{{E}}%</view><view class="stat-label">转化率</view></view></view><view class="progress-bar"><view class="progress-inner" style="{{'width:' + F}}"></view></view><view class="progress-text"><text>已售{{G}}%</text><text>剩余{{H}}件</text></view></view><view class="detail-section"><view class="section-header"><text class="section-title">活动详情</text></view><view class="detail-items"><view class="detail-item"><view class="item-label">活动时间</view><view class="item-value">{{I}}</view></view><view class="detail-item"><view class="item-label">活动库存</view><view class="item-value">{{J}}件</view></view><view class="detail-item"><view class="item-label">限购数量</view><view class="item-value">{{K}}</view></view><view class="detail-item"><view class="item-label">活动规则</view><view class="item-value">{{L}}</view></view></view></view><view class="product-detail-section"><view class="section-header"><text class="section-title">商品详情</text></view><rich-text class="product-description" nodes="{{M}}"></rich-text><view class="product-images"><image wx:for="{{N}}" wx:for-item="image" wx:key="a" class="detail-image" src="{{image.b}}" mode="widthFix"></image></view></view><view class="orders-section"><view class="section-header"><text class="section-title">订单记录</text><view class="view-all" bindtap="{{O}}"><text class="view-all-text">查看全部</text><view class="arrow-icon"></view></view></view><view wx:if="{{P}}" class="orders-list"><view wx:for="{{Q}}" wx:for-item="order" wx:key="e" class="order-item"><view class="order-user"><image class="user-avatar" src="{{order.a}}" mode="aspectFill"></image><text class="user-name">{{order.b}}</text></view><view class="order-info"><text class="order-quantity">{{order.c}}件</text><text class="order-time">{{order.d}}</text></view></view></view><view wx:else class="empty-orders"><image class="empty-image" src="{{R}}" mode="aspectFit"></image><text class="empty-text">暂无订单记录</text></view></view><view class="bottom-space"></view></scroll-view><view class="bottom-bar"><view class="action-buttons"><view class="action-button edit" bindtap="{{V}}"><svg wx:if="{{U}}" u-s="{{['d']}}" u-i="28927484-10" bind:__l="__l" u-p="{{U}}"><path wx:if="{{S}}" u-i="28927484-11,28927484-10" bind:__l="__l" u-p="{{S}}"/><path wx:if="{{T}}" u-i="28927484-12,28927484-10" bind:__l="__l" u-p="{{T}}"/></svg><text class="button-text">编辑</text></view><view class="action-button share" bindtap="{{ac}}"><svg wx:if="{{ab}}" u-s="{{['d']}}" u-i="28927484-13" bind:__l="__l" u-p="{{ab}}"><path wx:if="{{W}}" u-i="28927484-14,28927484-13" bind:__l="__l" u-p="{{W}}"/><path wx:if="{{X}}" u-i="28927484-15,28927484-13" bind:__l="__l" u-p="{{X}}"/><path wx:if="{{Y}}" u-i="28927484-16,28927484-13" bind:__l="__l" u-p="{{Y}}"/><path wx:if="{{Z}}" u-i="28927484-17,28927484-13" bind:__l="__l" u-p="{{Z}}"/><path wx:if="{{aa}}" u-i="28927484-18,28927484-13" bind:__l="__l" u-p="{{aa}}"/></svg><text class="button-text">分享</text></view><view class="{{['action-button', ak]}}" bindtap="{{al}}"><svg wx:if="{{ad}}" u-s="{{['d']}}" u-i="28927484-19" bind:__l="__l" u-p="{{ag}}"><rect wx:if="{{ae}}" u-i="28927484-20,28927484-19" bind:__l="__l" u-p="{{ae}}"/><rect wx:if="{{af}}" u-i="28927484-21,28927484-19" bind:__l="__l" u-p="{{af}}"/></svg><svg wx:else u-s="{{['d']}}" u-i="28927484-22" bind:__l="__l" u-p="{{ai||''}}"><path wx:if="{{ah}}" u-i="28927484-23,28927484-22" bind:__l="__l" u-p="{{ah}}"/></svg><text class="button-text">{{aj}}</text></view><view class="action-button delete" bindtap="{{ap}}"><svg wx:if="{{ao}}" u-s="{{['d']}}" u-i="28927484-24" bind:__l="__l" u-p="{{ao}}"><path wx:if="{{am}}" u-i="28927484-25,28927484-24" bind:__l="__l" u-p="{{am}}"/><path wx:if="{{an}}" u-i="28927484-26,28927484-24" bind:__l="__l" u-p="{{an}}"/></svg><text class="button-text">删除</text></view></view></view><view wx:if="{{aq}}" class="more-options-popup" bindtap="{{aN}}"><view class="popup-mask"></view><view class="popup-content" catchtap="{{aM}}"><view class="popup-option" bindtap="{{ax}}"><svg wx:if="{{aw}}" u-s="{{['d']}}" u-i="28927484-27" bind:__l="__l" u-p="{{aw}}"><rect wx:if="{{ar}}" u-i="28927484-28,28927484-27" bind:__l="__l" u-p="{{ar}}"/><rect wx:if="{{as}}" u-i="28927484-29,28927484-27" bind:__l="__l" u-p="{{as}}"/><rect wx:if="{{at}}" u-i="28927484-30,28927484-27" bind:__l="__l" u-p="{{at}}"/><rect wx:if="{{av}}" u-i="28927484-31,28927484-27" bind:__l="__l" u-p="{{av}}"/></svg><text>查看二维码</text></view><view class="popup-option" bindtap="{{aC}}"><svg wx:if="{{aB}}" u-s="{{['d']}}" u-i="28927484-32" bind:__l="__l" u-p="{{aB}}"><path wx:if="{{ay}}" u-i="28927484-33,28927484-32" bind:__l="__l" u-p="{{ay}}"/><path wx:if="{{az}}" u-i="28927484-34,28927484-32" bind:__l="__l" u-p="{{az}}"/><path wx:if="{{aA}}" u-i="28927484-35,28927484-32" bind:__l="__l" u-p="{{aA}}"/></svg><text>数据分析</text></view><view class="popup-option" bindtap="{{aG}}"><svg wx:if="{{aF}}" u-s="{{['d']}}" u-i="28927484-36" bind:__l="__l" u-p="{{aF}}"><path wx:if="{{aD}}" u-i="28927484-37,28927484-36" bind:__l="__l" u-p="{{aD}}"/><path wx:if="{{aE}}" u-i="28927484-38,28927484-36" bind:__l="__l" u-p="{{aE}}"/></svg><text>复制链接</text></view><view class="popup-option" bindtap="{{aL}}"><svg wx:if="{{aK}}" u-s="{{['d']}}" u-i="28927484-39" bind:__l="__l" u-p="{{aK}}"><path wx:if="{{aH}}" u-i="28927484-40,28927484-39" bind:__l="__l" u-p="{{aH}}"/><path wx:if="{{aI}}" u-i="28927484-41,28927484-39" bind:__l="__l" u-p="{{aI}}"/><path wx:if="{{aJ}}" u-i="28927484-42,28927484-39" bind:__l="__l" u-p="{{aJ}}"/></svg><text>导出数据</text></view></view></view></view>