<template>
  <view class="platforms-container">
    <!-- 自定义导航栏 -->
    <custom-navbar title="热门平台" :show-back="true"></custom-navbar>
    
    <!-- 内容区域 -->
    <view class="content-container">
      <!-- 搜索框 -->
      <view class="search-section">
        <view class="search-bar" @tap="navigateToSearch">
          <svg class="search-icon" viewBox="0 0 24 24" width="20" height="20">
            <path fill="#999999" d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z" />
          </svg>
          <text class="search-placeholder">搜索平台名称</text>
        </view>
      </view>
      
      <!-- 平台列表 -->
      <view class="platforms-grid">
        <platform-card
          v-for="(platform, index) in platforms"
          :key="index"
          :platform="platform"
          @tap="navigateToPlatformDetail(platform)"
        ></platform-card>
      </view>
    </view>
  </view>
</template>

<script>
import CustomNavbar from '../../components/CustomNavbar.vue';
import PlatformCard from '../../components/PlatformCard.vue';

export default {
  components: {
    CustomNavbar,
    PlatformCard
  },
  data() {
    return {
      platforms: [
        { id: 1, name: '淘宝', icon: '/static/images/cashback/platform-taobao.png', desc: '返利比例 0.5%-20%', products: 1000000 },
        { id: 2, name: '京东', icon: '/static/images/cashback/platform-jd.png', desc: '返利比例 0.5%-15%', products: 800000 },
        { id: 3, name: '拼多多', icon: '/static/images/cashback/platform-pdd.png', desc: '返利比例 1%-30%', products: 600000 },
        { id: 4, name: '唯品会', icon: '/static/images/cashback/platform-vip.png', desc: '返利比例 1%-10%', products: 400000 },
        { id: 5, name: '抖音', icon: '/static/images/cashback/platform-douyin.png', desc: '返利比例 1%-25%', products: 500000 },
        { id: 6, name: '天猫', icon: '/static/images/cashback/platform-tmall.png', desc: '返利比例 0.5%-18%', products: 900000 },
        { id: 7, name: '苏宁', icon: '/static/images/cashback/platform-suning.png', desc: '返利比例 1%-12%', products: 300000 },
        { id: 8, name: '小红书', icon: '/static/images/cashback/platform-xiaohongshu.png', desc: '返利比例 2%-20%', products: 200000 },
        { id: 9, name: '考拉海购', icon: '/static/images/cashback/platform-kaola.png', desc: '返利比例 1%-15%', products: 150000 },
        { id: 10, name: '网易严选', icon: '/static/images/cashback/platform-yanxuan.png', desc: '返利比例 2%-10%', products: 100000 },
        { id: 11, name: '亚马逊', icon: '/static/images/cashback/platform-amazon.png', desc: '返利比例 0.5%-8%', products: 500000 },
        { id: 12, name: '当当', icon: '/static/images/cashback/platform-dangdang.png', desc: '返利比例 1%-12%', products: 200000 }
      ]
    };
  },
  onLoad() {
    // 设置页面不显示系统导航栏
    uni.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#9C27B0'
    });
  },
  methods: {
    navigateToSearch() {
      uni.navigateTo({
        url: '/subPackages/cashback/pages/search/index'
      });
    },
    navigateToPlatformDetail(platform) {
      uni.navigateTo({
        url: `/subPackages/cashback/pages/platform-detail/index?id=${platform.id}&name=${platform.name}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.platforms-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content-container {
  padding-top: calc(var(--status-bar-height) + 44px);
  padding-bottom: 20px;
}

.search-section {
  padding: 16px;
  background: linear-gradient(135deg, #AB47BC 0%, #9C27B0 100%);
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  padding-top: 24px;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 10px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .search-icon {
    margin-right: 8px;
  }
  
  .search-placeholder {
    color: #999999;
    font-size: 14px;
  }
}

.platforms-grid {
  padding: 16px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}
</style> 