<template>
  <view class="loading-container" :class="[`size-${size}`, `type-${type}`, { overlay: isOverlay }]">
    <!-- 骨架屏加载 -->
    <view class="skeleton-loading" v-if="type === 'skeleton'">
      <view class="skeleton-item" v-for="n in skeletonCount" :key="n" :class="`skeleton-${skeletonType}`">
        <view class="skeleton-avatar" v-if="skeletonType === 'card'"></view>
        <view class="skeleton-content" v-if="skeletonType === 'card'">
          <view class="skeleton-line skeleton-title"></view>
          <view class="skeleton-line skeleton-text"></view>
          <view class="skeleton-line skeleton-text short"></view>
        </view>
        <view class="skeleton-line" v-if="skeletonType === 'list'"></view>
      </view>
    </view>
    
    <!-- 旋转加载器 -->
    <view class="spinner-loading" v-else-if="type === 'spinner'">
      <view class="spinner" :class="spinnerStyle">
        <view class="spinner-dot" v-for="n in 12" :key="n"></view>
      </view>
      <text class="loading-text" v-if="text">{{ text }}</text>
    </view>
    
    <!-- 脉冲加载 -->
    <view class="pulse-loading" v-else-if="type === 'pulse'">
      <view class="pulse-circle" v-for="n in 3" :key="n"></view>
      <text class="loading-text" v-if="text">{{ text }}</text>
    </view>
    
    <!-- 波浪加载 -->
    <view class="wave-loading" v-else-if="type === 'wave'">
      <view class="wave-bar" v-for="n in 5" :key="n"></view>
      <text class="loading-text" v-if="text">{{ text }}</text>
    </view>
    
    <!-- 点点加载 -->
    <view class="dots-loading" v-else-if="type === 'dots'">
      <view class="dot" v-for="n in 3" :key="n"></view>
      <text class="loading-text" v-if="text">{{ text }}</text>
    </view>
    
    <!-- 进度条加载 -->
    <view class="progress-loading" v-else-if="type === 'progress'">
      <view class="progress-bar">
        <view class="progress-fill" :style="{ width: `${progress}%` }"></view>
      </view>
      <text class="progress-text" v-if="showProgress">{{ progress }}%</text>
      <text class="loading-text" v-if="text">{{ text }}</text>
    </view>
    
    <!-- 自定义加载 -->
    <view class="custom-loading" v-else-if="type === 'custom'">
      <slot name="custom">
        <view class="default-custom">
          <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="12" y1="2" x2="12" y2="6"></line>
            <line x1="12" y1="18" x2="12" y2="22"></line>
            <line x1="4.93" y1="4.93" x2="7.76" y2="7.76"></line>
            <line x1="16.24" y1="16.24" x2="19.07" y2="19.07"></line>
            <line x1="2" y1="12" x2="6" y2="12"></line>
            <line x1="18" y1="12" x2="22" y2="12"></line>
            <line x1="4.93" y1="19.07" x2="7.76" y2="16.24"></line>
            <line x1="16.24" y1="7.76" x2="19.07" y2="4.93"></line>
          </svg>
        </view>
      </slot>
      <text class="loading-text" v-if="text">{{ text }}</text>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'

// Props定义
const props = defineProps({
  // 加载类型
  type: {
    type: String,
    default: 'spinner', // spinner, skeleton, pulse, wave, dots, progress, custom
    validator: (value) => ['spinner', 'skeleton', 'pulse', 'wave', 'dots', 'progress', 'custom'].includes(value)
  },
  // 尺寸
  size: {
    type: String,
    default: 'medium', // small, medium, large
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  // 加载文本
  text: {
    type: String,
    default: ''
  },
  // 是否为遮罩层
  isOverlay: {
    type: Boolean,
    default: false
  },
  // 旋转器样式
  spinnerStyle: {
    type: String,
    default: 'default', // default, dots, bars
    validator: (value) => ['default', 'dots', 'bars'].includes(value)
  },
  // 骨架屏类型
  skeletonType: {
    type: String,
    default: 'card', // card, list, text
    validator: (value) => ['card', 'list', 'text'].includes(value)
  },
  // 骨架屏数量
  skeletonCount: {
    type: Number,
    default: 3
  },
  // 进度值（0-100）
  progress: {
    type: Number,
    default: 0,
    validator: (value) => value >= 0 && value <= 100
  },
  // 是否显示进度数值
  showProgress: {
    type: Boolean,
    default: true
  },
  // 主题色
  color: {
    type: String,
    default: '#2196F3'
  }
})
</script>

<style scoped>
/* 加载组件样式开始 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.loading-container.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  z-index: 9999;
}

/* 尺寸变体 */
.loading-container.size-small {
  padding: 10px;
}

.loading-container.size-large {
  padding: 40px;
}

/* 骨架屏加载 */
.skeleton-loading {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.skeleton-item {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.skeleton-item.skeleton-card {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.skeleton-item.skeleton-list {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.skeleton-avatar {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-line {
  height: 12px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  border-radius: 6px;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-line.skeleton-title {
  height: 16px;
  width: 60%;
}

.skeleton-line.skeleton-text {
  width: 100%;
}

.skeleton-line.skeleton-text.short {
  width: 40%;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 旋转加载器 */
.spinner-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.spinner {
  position: relative;
  width: 40px;
  height: 40px;
}

.size-small .spinner {
  width: 24px;
  height: 24px;
}

.size-large .spinner {
  width: 56px;
  height: 56px;
}

.spinner.default {
  border: 3px solid #f0f0f0;
  border-top: 3px solid #2196F3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner.dots .spinner-dot {
  position: absolute;
  width: 3px;
  height: 3px;
  background: #2196F3;
  border-radius: 50%;
  transform-origin: 20px 20px;
  animation: spinner-dots 1.2s linear infinite;
}

.spinner.dots .spinner-dot:nth-child(1) { transform: rotate(0deg); animation-delay: 0s; }
.spinner.dots .spinner-dot:nth-child(2) { transform: rotate(30deg); animation-delay: -0.1s; }
.spinner.dots .spinner-dot:nth-child(3) { transform: rotate(60deg); animation-delay: -0.2s; }
.spinner.dots .spinner-dot:nth-child(4) { transform: rotate(90deg); animation-delay: -0.3s; }
.spinner.dots .spinner-dot:nth-child(5) { transform: rotate(120deg); animation-delay: -0.4s; }
.spinner.dots .spinner-dot:nth-child(6) { transform: rotate(150deg); animation-delay: -0.5s; }
.spinner.dots .spinner-dot:nth-child(7) { transform: rotate(180deg); animation-delay: -0.6s; }
.spinner.dots .spinner-dot:nth-child(8) { transform: rotate(210deg); animation-delay: -0.7s; }
.spinner.dots .spinner-dot:nth-child(9) { transform: rotate(240deg); animation-delay: -0.8s; }
.spinner.dots .spinner-dot:nth-child(10) { transform: rotate(270deg); animation-delay: -0.9s; }
.spinner.dots .spinner-dot:nth-child(11) { transform: rotate(300deg); animation-delay: -1.0s; }
.spinner.dots .spinner-dot:nth-child(12) { transform: rotate(330deg); animation-delay: -1.1s; }

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes spinner-dots {
  0%, 39%, 100% { opacity: 0; }
  40% { opacity: 1; }
}

/* 脉冲加载 */
.pulse-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-direction: column;
}

.pulse-circle {
  width: 12px;
  height: 12px;
  background: #2196F3;
  border-radius: 50%;
  animation: pulse 1.5s ease-in-out infinite;
}

.pulse-circle:nth-child(1) { animation-delay: 0s; }
.pulse-circle:nth-child(2) { animation-delay: 0.2s; }
.pulse-circle:nth-child(3) { animation-delay: 0.4s; }

@keyframes pulse {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 1;
  }
  40% {
    transform: scale(1);
    opacity: 0.5;
  }
}

/* 波浪加载 */
.wave-loading {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-direction: column;
}

.wave-bar {
  width: 4px;
  height: 20px;
  background: #2196F3;
  border-radius: 2px;
  animation: wave 1.2s ease-in-out infinite;
}

.wave-bar:nth-child(1) { animation-delay: 0s; }
.wave-bar:nth-child(2) { animation-delay: 0.1s; }
.wave-bar:nth-child(3) { animation-delay: 0.2s; }
.wave-bar:nth-child(4) { animation-delay: 0.3s; }
.wave-bar:nth-child(5) { animation-delay: 0.4s; }

@keyframes wave {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

/* 点点加载 */
.dots-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-direction: column;
}

.dot {
  width: 8px;
  height: 8px;
  background: #2196F3;
  border-radius: 50%;
  animation: dots 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }
.dot:nth-child(3) { animation-delay: 0s; }

@keyframes dots {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* 进度条加载 */
.progress-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  width: 100%;
  max-width: 200px;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #2196F3, #21CBF3);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

/* 自定义加载 */
.custom-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.default-custom svg {
  color: #2196F3;
  animation: spin 2s linear infinite;
}

/* 加载文本 */
.loading-text {
  font-size: 14px;
  color: #666;
  text-align: center;
  margin-top: 8px;
}

.size-small .loading-text {
  font-size: 12px;
}

.size-large .loading-text {
  font-size: 16px;
}
/* 加载组件样式结束 */
</style>
