{"version": 3, "file": "member-discount.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/member/member-discount.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xtZW1iZXJcbWVtYmVyLWRpc2NvdW50LnZ1ZQ"], "sourcesContent": ["<!-- 会员折扣页面开始 -->\n<template>\n  <view class=\"member-discount-container\">\n    <!-- 页面标题区域 -->\n    <view class=\"page-header\">\n      <view class=\"title-section\">\n        <text class=\"page-title\">会员折扣</text>\n        <text class=\"page-subtitle\">管理会员专享商品折扣</text>\n      </view>\n      \n      <!-- 添加折扣按钮 -->\n      <view class=\"add-discount-btn\" @click=\"showAddDiscountModal\">\n        <text class=\"btn-text\">添加折扣</text>\n        <text class=\"btn-icon\">+</text>\n      </view>\n    </view>\n    \n    <!-- 会员折扣列表 -->\n    <view class=\"discount-list\">\n      <view v-if=\"discounts.length === 0\" class=\"empty-tip\">\n        <image class=\"empty-icon\" src=\"/static/images/empty-data.svg\"></image>\n        <text class=\"empty-text\">暂无会员折扣，点击\"添加折扣\"创建</text>\n      </view>\n      \n      <view v-else class=\"discount-cards\">\n        <view v-for=\"(discount, index) in discounts\" :key=\"index\" class=\"discount-card\">\n          <view class=\"discount-card-header\" :style=\"{ backgroundColor: discount.color }\">\n            <view class=\"discount-name\">{{ discount.name }}</view>\n            <view class=\"discount-actions\">\n              <text class=\"action-btn edit\" @click=\"editDiscount(discount)\">编辑</text>\n              <text class=\"action-btn delete\" @click=\"confirmDeleteDiscount(discount)\">删除</text>\n            </view>\n          </view>\n          <view class=\"discount-card-body\">\n            <view class=\"discount-info-item\">\n              <text class=\"info-label\">折扣图标：</text>\n              <image class=\"discount-icon\" :src=\"discount.icon\" mode=\"aspectFit\"></image>\n            </view>\n            <view class=\"discount-info-item\">\n              <text class=\"info-label\">适用等级：</text>\n              <view class=\"level-tags\">\n                <text \n                  v-for=\"(level, idx) in discount.applicableLevels\" \n                  :key=\"idx\" \n                  class=\"level-tag\"\n                >{{ level }}</text>\n              </view>\n            </view>\n            <view class=\"discount-info-item\">\n              <text class=\"info-label\">折扣力度：</text>\n              <text class=\"info-value\">{{ discount.value }}</text>\n            </view>\n            <view class=\"discount-info-item\">\n              <text class=\"info-label\">适用范围：</text>\n              <text class=\"info-value\">{{ discount.scope }}</text>\n            </view>\n            <view class=\"discount-info-item\">\n              <text class=\"info-label\">折扣说明：</text>\n              <text class=\"info-value\">{{ discount.description || '暂无折扣说明' }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 添加/编辑折扣弹窗 -->\n    <uni-popup ref=\"discountFormPopup\" type=\"center\">\n      <view class=\"discount-form-popup\">\n        <view class=\"popup-header\">\n          <text class=\"popup-title\">{{ isEditing ? '编辑折扣' : '添加折扣' }}</text>\n          <text class=\"popup-close\" @click=\"closeDiscountModal\">×</text>\n        </view>\n        <view class=\"popup-body\">\n          <view class=\"form-item\">\n            <text class=\"form-label\">折扣名称</text>\n            <input class=\"form-input\" v-model=\"discountForm.name\" placeholder=\"请输入折扣名称\" />\n          </view>\n          <view class=\"form-item\">\n            <text class=\"form-label\">折扣颜色</text>\n            <view class=\"color-picker\">\n              <view \n                v-for=\"(color, idx) in colorOptions\" \n                :key=\"idx\" \n                class=\"color-option\"\n                :class=\"{ active: discountForm.color === color }\"\n                :style=\"{ backgroundColor: color }\"\n                @click=\"discountForm.color = color\"\n              ></view>\n            </view>\n          </view>\n          <view class=\"form-item\">\n            <text class=\"form-label\">折扣图标</text>\n            <view class=\"icon-upload\">\n              <image v-if=\"discountForm.icon\" class=\"preview-icon\" :src=\"discountForm.icon\" mode=\"aspectFit\"></image>\n              <view v-else class=\"upload-btn\" @click=\"chooseIcon\">\n                <text class=\"upload-icon\">+</text>\n                <text class=\"upload-text\">上传图标</text>\n              </view>\n            </view>\n          </view>\n          <view class=\"form-item\">\n            <text class=\"form-label\">折扣力度</text>\n            <input class=\"form-input\" v-model=\"discountForm.value\" placeholder=\"请输入折扣力度，如9折、8.5折等\" />\n          </view>\n          <view class=\"form-item\">\n            <text class=\"form-label\">适用范围</text>\n            <picker class=\"form-picker\" :range=\"discountScopes\" @change=\"onScopeChange\">\n              <view class=\"picker-value\">{{ discountForm.scope || '请选择适用范围' }}</view>\n            </picker>\n          </view>\n          <view class=\"form-item\">\n            <text class=\"form-label\">适用等级</text>\n            <view class=\"level-checkboxes\">\n              <view \n                v-for=\"(level, idx) in memberLevels\" \n                :key=\"idx\" \n                class=\"level-checkbox\"\n                :class=\"{ active: isLevelSelected(level.name) }\"\n                @click=\"toggleLevelSelection(level.name)\"\n              >\n                <text class=\"checkbox-icon\">{{ isLevelSelected(level.name) ? '✓' : '' }}</text>\n                <text class=\"checkbox-label\">{{ level.name }}</text>\n              </view>\n            </view>\n          </view>\n          <view class=\"form-item\">\n            <text class=\"form-label\">折扣说明</text>\n            <textarea class=\"form-textarea\" v-model=\"discountForm.description\" placeholder=\"请输入折扣说明\"></textarea>\n          </view>\n        </view>\n        <view class=\"popup-footer\">\n          <button class=\"cancel-btn\" @click=\"closeDiscountModal\">取消</button>\n          <button class=\"confirm-btn\" @click=\"saveDiscountForm\">确认</button>\n        </view>\n      </view>\n    </uni-popup>\n    \n    <!-- 删除确认弹窗 -->\n    <uni-popup ref=\"deleteConfirmPopup\" type=\"dialog\">\n      <uni-popup-dialog\n        type=\"warning\"\n        title=\"删除确认\"\n        content=\"确定要删除该会员折扣吗？删除后将无法恢复。\"\n        :before-close=\"true\"\n        @confirm=\"deleteDiscount\"\n        @close=\"closeDeleteConfirm\"\n      ></uni-popup-dialog>\n    </uni-popup>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      discounts: [], // 会员折扣列表\n      discountForm: {\n        id: '',\n        name: '',\n        color: '#FF6B22', // 默认橙色\n        icon: '',\n        value: '',\n        scope: '',\n        applicableLevels: [],\n        description: ''\n      },\n      isEditing: false, // 是否为编辑模式\n      currentDiscountId: null, // 当前编辑的折扣ID\n      colorOptions: [\n        '#FF6B22', // 橙色\n        '#8A2BE2', // 紫色\n        '#1E90FF', // 道奇蓝\n        '#32CD32', // 酸橙绿\n        '#FFD700', // 金色\n        '#FF69B4', // 热粉红\n        '#20B2AA', // 浅海绿\n        '#FF8C00'  // 深橙色\n      ],\n      discountScopes: [\n        '全部商品',\n        '指定分类商品',\n        '指定商品',\n        '会员专享商品'\n      ],\n      memberLevels: [] // 会员等级列表\n    };\n  },\n  onLoad() {\n    this.fetchDiscounts();\n    this.fetchMemberLevels();\n  },\n  methods: {\n    // 获取会员折扣列表\n    fetchDiscounts() {\n      // 模拟数据，实际项目中应从API获取\n      this.discounts = [\n        {\n          id: '1',\n          name: '银卡会员折扣',\n          color: '#C0C0C0',\n          icon: '/static/images/discount-silver.svg',\n          value: '9.5折',\n          scope: '全部商品',\n          applicableLevels: ['银卡会员'],\n          description: '银卡会员购买全部商品享受9.5折优惠'\n        },\n        {\n          id: '2',\n          name: '金卡会员折扣',\n          color: '#FFD700',\n          icon: '/static/images/discount-gold.svg',\n          value: '9折',\n          scope: '全部商品',\n          applicableLevels: ['金卡会员'],\n          description: '金卡会员购买全部商品享受9折优惠'\n        },\n        {\n          id: '3',\n          name: '钻石会员折扣',\n          color: '#B9F2FF',\n          icon: '/static/images/discount-diamond.svg',\n          value: '8.5折',\n          scope: '全部商品',\n          applicableLevels: ['钻石会员'],\n          description: '钻石会员购买全部商品享受8.5折优惠'\n        }\n      ];\n    },\n    \n    // 获取会员等级列表\n    fetchMemberLevels() {\n      // 模拟数据，实际项目中应从API获取\n      this.memberLevels = [\n        {\n          id: '1',\n          name: '普通会员'\n        },\n        {\n          id: '2',\n          name: '银卡会员'\n        },\n        {\n          id: '3',\n          name: '金卡会员'\n        },\n        {\n          id: '4',\n          name: '钻石会员'\n        }\n      ];\n    },\n    \n    // 显示添加折扣弹窗\n    showAddDiscountModal() {\n      this.isEditing = false;\n      this.discountForm = {\n        id: '',\n        name: '',\n        color: '#FF6B22',\n        icon: '',\n        value: '',\n        scope: '',\n        applicableLevels: [],\n        description: ''\n      };\n      this.$refs.discountFormPopup.open();\n    },\n    \n    // 编辑折扣\n    editDiscount(discount) {\n      this.isEditing = true;\n      this.currentDiscountId = discount.id;\n      this.discountForm = JSON.parse(JSON.stringify(discount)); // 深拷贝\n      this.$refs.discountFormPopup.open();\n    },\n    \n    // 关闭折扣表单弹窗\n    closeDiscountModal() {\n      this.$refs.discountFormPopup.close();\n    },\n    \n    // 保存折扣表单\n    saveDiscountForm() {\n      // 表单验证\n      if (!this.discountForm.name) {\n        uni.showToast({\n          title: '请输入折扣名称',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      if (!this.discountForm.value) {\n        uni.showToast({\n          title: '请输入折扣力度',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      if (!this.discountForm.scope) {\n        uni.showToast({\n          title: '请选择适用范围',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      if (this.discountForm.applicableLevels.length === 0) {\n        uni.showToast({\n          title: '请选择适用等级',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 保存数据\n      if (this.isEditing) {\n        // 编辑现有折扣\n        const index = this.discounts.findIndex(item => item.id === this.currentDiscountId);\n        if (index !== -1) {\n          this.discounts.splice(index, 1, JSON.parse(JSON.stringify(this.discountForm)));\n        }\n      } else {\n        // 添加新折扣\n        this.discountForm.id = Date.now().toString(); // 生成临时ID\n        this.discounts.push(JSON.parse(JSON.stringify(this.discountForm)));\n      }\n      \n      // 关闭弹窗\n      this.closeDiscountModal();\n      \n      // 提示保存成功\n      uni.showToast({\n        title: this.isEditing ? '折扣修改成功' : '折扣添加成功'\n      });\n    },\n    \n    // 确认删除折扣\n    confirmDeleteDiscount(discount) {\n      this.currentDiscountId = discount.id;\n      this.$refs.deleteConfirmPopup.open();\n    },\n    \n    // 删除折扣\n    deleteDiscount() {\n      const index = this.discounts.findIndex(item => item.id === this.currentDiscountId);\n      if (index !== -1) {\n        this.discounts.splice(index, 1);\n      }\n      \n      this.$refs.deleteConfirmPopup.close();\n      \n      uni.showToast({\n        title: '折扣删除成功'\n      });\n    },\n    \n    // 关闭删除确认弹窗\n    closeDeleteConfirm() {\n      this.$refs.deleteConfirmPopup.close();\n    },\n    \n    // 选择图标\n    chooseIcon() {\n      uni.chooseImage({\n        count: 1,\n        success: (res) => {\n          this.discountForm.icon = res.tempFilePaths[0];\n        }\n      });\n    },\n    \n    // 选择范围变更\n    onScopeChange(e) {\n      const index = e.detail.value;\n      this.discountForm.scope = this.discountScopes[index];\n    },\n    \n    // 判断等级是否被选中\n    isLevelSelected(levelName) {\n      return this.discountForm.applicableLevels.includes(levelName);\n    },\n    \n    // 切换等级选择\n    toggleLevelSelection(levelName) {\n      const index = this.discountForm.applicableLevels.indexOf(levelName);\n      if (index === -1) {\n        this.discountForm.applicableLevels.push(levelName);\n      } else {\n        this.discountForm.applicableLevels.splice(index, 1);\n      }\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.member-discount-container {\n  padding: 20rpx;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.title-section {\n  .page-title {\n    font-size: 36rpx;\n    font-weight: bold;\n    color: #333;\n  }\n  \n  .page-subtitle {\n    font-size: 24rpx;\n    color: #666;\n    margin-top: 6rpx;\n  }\n}\n\n.add-discount-btn {\n  display: flex;\n  align-items: center;\n  background-color: #4A00E0;\n  color: #fff;\n  padding: 12rpx 24rpx;\n  border-radius: 8rpx;\n  \n  .btn-text {\n    font-size: 28rpx;\n  }\n  \n  .btn-icon {\n    font-size: 32rpx;\n    margin-left: 8rpx;\n  }\n}\n\n.discount-list {\n  .empty-tip {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    padding: 100rpx 0;\n    background-color: #fff;\n    border-radius: 12rpx;\n    \n    .empty-icon {\n      width: 160rpx;\n      height: 160rpx;\n      margin-bottom: 20rpx;\n    }\n    \n    .empty-text {\n      font-size: 28rpx;\n      color: #999;\n    }\n  }\n}\n\n.discount-cards {\n  .discount-card {\n    background-color: #fff;\n    border-radius: 12rpx;\n    margin-bottom: 20rpx;\n    overflow: hidden;\n    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n    \n    .discount-card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 20rpx 24rpx;\n      color: #fff;\n      \n      .discount-name {\n        font-size: 32rpx;\n        font-weight: bold;\n      }\n      \n      .discount-actions {\n        display: flex;\n        \n        .action-btn {\n          font-size: 24rpx;\n          padding: 6rpx 16rpx;\n          border-radius: 30rpx;\n          margin-left: 16rpx;\n          \n          &.edit {\n            background-color: rgba(255, 255, 255, 0.3);\n          }\n          \n          &.delete {\n            background-color: rgba(255, 255, 255, 0.3);\n          }\n        }\n      }\n    }\n    \n    .discount-card-body {\n      padding: 24rpx;\n      \n      .discount-info-item {\n        display: flex;\n        margin-bottom: 16rpx;\n        \n        .info-label {\n          width: 160rpx;\n          font-size: 28rpx;\n          color: #666;\n        }\n        \n        .info-value {\n          flex: 1;\n          font-size: 28rpx;\n          color: #333;\n        }\n        \n        .discount-icon {\n          width: 60rpx;\n          height: 60rpx;\n        }\n        \n        .level-tags {\n          display: flex;\n          flex-wrap: wrap;\n          \n          .level-tag {\n            font-size: 24rpx;\n            color: #4A00E0;\n            background-color: rgba(74, 0, 224, 0.1);\n            padding: 6rpx 16rpx;\n            border-radius: 6rpx;\n            margin-right: 12rpx;\n            margin-bottom: 12rpx;\n          }\n        }\n      }\n    }\n  }\n}\n\n.discount-form-popup {\n  width: 650rpx;\n  background-color: #fff;\n  border-radius: 12rpx;\n  overflow: hidden;\n  \n  .popup-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 24rpx;\n    border-bottom: 1rpx solid #eee;\n    \n    .popup-title {\n      font-size: 32rpx;\n      font-weight: bold;\n      color: #333;\n    }\n    \n    .popup-close {\n      font-size: 40rpx;\n      color: #999;\n    }\n  }\n  \n  .popup-body {\n    padding: 24rpx;\n    max-height: 60vh;\n    overflow-y: auto;\n    \n    .form-item {\n      margin-bottom: 24rpx;\n      \n      .form-label {\n        display: block;\n        font-size: 28rpx;\n        color: #333;\n        margin-bottom: 12rpx;\n      }\n      \n      .form-input {\n        width: 100%;\n        height: 80rpx;\n        border: 1rpx solid #ddd;\n        border-radius: 8rpx;\n        padding: 0 20rpx;\n        font-size: 28rpx;\n        box-sizing: border-box;\n      }\n      \n      .form-textarea {\n        width: 100%;\n        height: 160rpx;\n        border: 1rpx solid #ddd;\n        border-radius: 8rpx;\n        padding: 20rpx;\n        font-size: 28rpx;\n        box-sizing: border-box;\n      }\n      \n      .form-picker {\n        width: 100%;\n        height: 80rpx;\n        border: 1rpx solid #ddd;\n        border-radius: 8rpx;\n        padding: 0 20rpx;\n        font-size: 28rpx;\n        box-sizing: border-box;\n        display: flex;\n        align-items: center;\n        \n        .picker-value {\n          color: #333;\n        }\n      }\n      \n      .color-picker {\n        display: flex;\n        flex-wrap: wrap;\n        \n        .color-option {\n          width: 60rpx;\n          height: 60rpx;\n          border-radius: 50%;\n          margin-right: 20rpx;\n          margin-bottom: 20rpx;\n          position: relative;\n          \n          &.active::after {\n            content: '✓';\n            position: absolute;\n            top: 50%;\n            left: 50%;\n            transform: translate(-50%, -50%);\n            color: #fff;\n            font-size: 32rpx;\n          }\n        }\n      }\n      \n      .icon-upload {\n        .preview-icon {\n          width: 100rpx;\n          height: 100rpx;\n          border-radius: 8rpx;\n        }\n        \n        .upload-btn {\n          width: 100rpx;\n          height: 100rpx;\n          border: 1rpx dashed #ddd;\n          border-radius: 8rpx;\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          justify-content: center;\n          \n          .upload-icon {\n            font-size: 40rpx;\n            color: #999;\n            margin-bottom: 4rpx;\n          }\n          \n          .upload-text {\n            font-size: 20rpx;\n            color: #999;\n          }\n        }\n      }\n      \n      .level-checkboxes {\n        display: flex;\n        flex-wrap: wrap;\n        \n        .level-checkbox {\n          display: flex;\n          align-items: center;\n          margin-right: 30rpx;\n          margin-bottom: 20rpx;\n          \n          .checkbox-icon {\n            width: 40rpx;\n            height: 40rpx;\n            border: 1rpx solid #ddd;\n            border-radius: 8rpx;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            margin-right: 10rpx;\n            color: #fff;\n            font-size: 24rpx;\n          }\n          \n          &.active .checkbox-icon {\n            background-color: #4A00E0;\n            border-color: #4A00E0;\n          }\n          \n          .checkbox-label {\n            font-size: 28rpx;\n            color: #333;\n          }\n        }\n      }\n    }\n  }\n  \n  .popup-footer {\n    display: flex;\n    border-top: 1rpx solid #eee;\n    \n    button {\n      flex: 1;\n      height: 90rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 32rpx;\n      border-radius: 0;\n      \n      &.cancel-btn {\n        background-color: #f5f5f5;\n        color: #666;\n      }\n      \n      &.confirm-btn {\n        background-color: #4A00E0;\n        color: #fff;\n      }\n    }\n  }\n}\n</style>\n<!-- 会员折扣页面结束 --> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/member/member-discount.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAwJA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW,CAAE;AAAA;AAAA,MACb,cAAc;AAAA,QACZ,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,kBAAkB,CAAE;AAAA,QACpB,aAAa;AAAA,MACd;AAAA,MACD,WAAW;AAAA;AAAA,MACX,mBAAmB;AAAA;AAAA,MACnB,cAAc;AAAA,QACZ;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACD;AAAA,MACD,gBAAgB;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,MACD,cAAc;;;EAEjB;AAAA,EACD,SAAS;AACP,SAAK,eAAc;AACnB,SAAK,kBAAiB;AAAA,EACvB;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,iBAAiB;AAEf,WAAK,YAAY;AAAA,QACf;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,UACP,kBAAkB,CAAC,MAAM;AAAA,UACzB,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,UACP,kBAAkB,CAAC,MAAM;AAAA,UACzB,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,UACP,kBAAkB,CAAC,MAAM;AAAA,UACzB,aAAa;AAAA,QACf;AAAA;IAEH;AAAA;AAAA,IAGD,oBAAoB;AAElB,WAAK,eAAe;AAAA,QAClB;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,QACR;AAAA;IAEH;AAAA;AAAA,IAGD,uBAAuB;AACrB,WAAK,YAAY;AACjB,WAAK,eAAe;AAAA,QAClB,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,kBAAkB,CAAE;AAAA,QACpB,aAAa;AAAA;AAEf,WAAK,MAAM,kBAAkB;IAC9B;AAAA;AAAA,IAGD,aAAa,UAAU;AACrB,WAAK,YAAY;AACjB,WAAK,oBAAoB,SAAS;AAClC,WAAK,eAAe,KAAK,MAAM,KAAK,UAAU,QAAQ,CAAC;AACvD,WAAK,MAAM,kBAAkB;IAC9B;AAAA;AAAA,IAGD,qBAAqB;AACnB,WAAK,MAAM,kBAAkB;IAC9B;AAAA;AAAA,IAGD,mBAAmB;AAEjB,UAAI,CAAC,KAAK,aAAa,MAAM;AAC3BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,aAAa,OAAO;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,aAAa,OAAO;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,KAAK,aAAa,iBAAiB,WAAW,GAAG;AACnDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAGA,UAAI,KAAK,WAAW;AAElB,cAAM,QAAQ,KAAK,UAAU,UAAU,UAAQ,KAAK,OAAO,KAAK,iBAAiB;AACjF,YAAI,UAAU,IAAI;AAChB,eAAK,UAAU,OAAO,OAAO,GAAG,KAAK,MAAM,KAAK,UAAU,KAAK,YAAY,CAAC,CAAC;AAAA,QAC/E;AAAA,aACK;AAEL,aAAK,aAAa,KAAK,KAAK,IAAG,EAAG;AAClC,aAAK,UAAU,KAAK,KAAK,MAAM,KAAK,UAAU,KAAK,YAAY,CAAC,CAAC;AAAA,MACnE;AAGA,WAAK,mBAAkB;AAGvBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,KAAK,YAAY,WAAW;AAAA,MACrC,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,sBAAsB,UAAU;AAC9B,WAAK,oBAAoB,SAAS;AAClC,WAAK,MAAM,mBAAmB;IAC/B;AAAA;AAAA,IAGD,iBAAiB;AACf,YAAM,QAAQ,KAAK,UAAU,UAAU,UAAQ,KAAK,OAAO,KAAK,iBAAiB;AACjF,UAAI,UAAU,IAAI;AAChB,aAAK,UAAU,OAAO,OAAO,CAAC;AAAA,MAChC;AAEA,WAAK,MAAM,mBAAmB;AAE9BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,MACT,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,qBAAqB;AACnB,WAAK,MAAM,mBAAmB;IAC/B;AAAA;AAAA,IAGD,aAAa;AACXA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,SAAS,CAAC,QAAQ;AAChB,eAAK,aAAa,OAAO,IAAI,cAAc,CAAC;AAAA,QAC9C;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,cAAc,GAAG;AACf,YAAM,QAAQ,EAAE,OAAO;AACvB,WAAK,aAAa,QAAQ,KAAK,eAAe,KAAK;AAAA,IACpD;AAAA;AAAA,IAGD,gBAAgB,WAAW;AACzB,aAAO,KAAK,aAAa,iBAAiB,SAAS,SAAS;AAAA,IAC7D;AAAA;AAAA,IAGD,qBAAqB,WAAW;AAC9B,YAAM,QAAQ,KAAK,aAAa,iBAAiB,QAAQ,SAAS;AAClE,UAAI,UAAU,IAAI;AAChB,aAAK,aAAa,iBAAiB,KAAK,SAAS;AAAA,aAC5C;AACL,aAAK,aAAa,iBAAiB,OAAO,OAAO,CAAC;AAAA,MACpD;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzYA,GAAG,WAAW,eAAe;"}