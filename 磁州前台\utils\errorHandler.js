/**
 * 错误处理工具
 * 统一处理项目中的各类错误，包括：
 * - 网络请求错误
 * - 业务逻辑错误
 * - 运行时错误
 */

// 错误类型
export const ErrorType = {
  NETWORK: 'network', // 网络错误
  API: 'api', // API错误
  AUTH: 'auth', // 认证错误
  PERMISSION: 'permission', // 权限错误
  VALIDATION: 'validation', // 数据验证错误
  BUSINESS: 'business', // 业务逻辑错误
  UNKNOWN: 'unknown' // 未知错误
}

// HTTP状态码错误映射
const HTTP_STATUS_MAP = {
  400: { type: ErrorType.VALIDATION, message: '请求参数错误' },
  401: { type: ErrorType.AUTH, message: '未授权，请重新登录' },
  403: { type: ErrorType.PERMISSION, message: '权限不足，无法访问' },
  404: { type: ErrorType.API, message: '请求的资源不存在' },
  500: { type: ErrorType.API, message: '服务器内部错误' },
  502: { type: ErrorType.NETWORK, message: '网关错误' },
  503: { type: ErrorType.NETWORK, message: '服务不可用' },
  504: { type: ErrorType.NETWORK, message: '网关超时' }
}

// 网络异常错误码映射
const NETWORK_ERROR_MAP = {
  'ECONNABORTED': { type: ErrorType.NETWORK, message: '请求超时，请检查网络' },
  'ECONNREFUSED': { type: ErrorType.NETWORK, message: '服务器拒绝连接' },
  'ENOTFOUND': { type: ErrorType.NETWORK, message: '无法连接到服务器' }
}

// 创建标准错误对象
export const createError = (error, defaultMessage = '未知错误') => {
  // 已经是标准错误对象格式的直接返回
  if (error && error.type && error.message) {
    return error
  }

  // HTTP 响应错误
  if (error && error.response) {
    const { statusCode, data } = error.response
    const statusError = HTTP_STATUS_MAP[statusCode]
    
    if (statusError) {
      return {
        type: statusError.type,
        message: data?.message || statusError.message,
        statusCode,
        data: data,
        originalError: error
      }
    }
  }
  
  // 网络错误
  if (error && error.errMsg) {
    // uni-app 网络请求错误
    const errMsg = error.errMsg
    
    if (errMsg.includes('timeout')) {
      return {
        type: ErrorType.NETWORK,
        message: '请求超时，请检查网络',
        originalError: error
      }
    }
    
    if (errMsg.includes('fail')) {
      return {
        type: ErrorType.NETWORK,
        message: '网络连接失败',
        originalError: error
      }
    }
  }
  
  // 处理 Node.js 类网络错误
  if (error && error.code && NETWORK_ERROR_MAP[error.code]) {
    const networkError = NETWORK_ERROR_MAP[error.code]
    return {
      type: networkError.type,
      message: networkError.message,
      originalError: error
    }
  }
  
  // 业务逻辑错误
  if (error && error.code !== undefined && error.message) {
    return {
      type: ErrorType.BUSINESS,
      message: error.message,
      code: error.code,
      originalError: error
    }
  }
  
  // 默认未知错误
  return {
    type: ErrorType.UNKNOWN,
    message: error?.message || defaultMessage,
    originalError: error
  }
}

// 处理错误并显示提示
export const handleError = (error, options = {}) => {
  const {
    showToast = true,
    autoLogin = true,
    silent = false,
    onAuthError,
    onBusinessError,
    onNetworkError
  } = options
  
  // 转换为标准错误对象
  const standardError = createError(error)
  
  // 记录错误日志
  console.error('[错误处理]', standardError)
  
  // 如果是静默处理，只记录不提示
  if (silent) return standardError
  
  // 特殊错误处理
  switch (standardError.type) {
    case ErrorType.AUTH:
      // 身份验证错误，可以触发重新登录
      if (autoLogin && onAuthError) {
        onAuthError(standardError)
      } else if (showToast) {
        uni.showToast({
          title: standardError.message,
          icon: 'none',
          duration: 2000
        })
      }
      break
      
    case ErrorType.NETWORK:
      // 网络错误处理
      if (onNetworkError) {
        onNetworkError(standardError)
      } else if (showToast) {
        uni.showToast({
          title: standardError.message,
          icon: 'none',
          duration: 2000
        })
      }
      break
      
    case ErrorType.BUSINESS:
      // 业务逻辑错误处理
      if (onBusinessError) {
        onBusinessError(standardError)
      } else if (showToast) {
        uni.showToast({
          title: standardError.message,
          icon: 'none',
          duration: 2000
        })
      }
      break
      
    default:
      // 默认错误提示
      if (showToast) {
        uni.showToast({
          title: standardError.message,
          icon: 'none',
          duration: 2000
        })
      }
  }
  
  return standardError
}

// 业务错误类
export class BusinessError extends Error {
  constructor(message, code) {
    super(message)
    this.name = 'BusinessError'
    this.code = code
  }
}

// 捕获全局未处理的Promise错误
export const setupGlobalErrorHandlers = () => {
  // 全局Promise拒绝处理
  // 注意：小程序环境可能不支持，需要视情况使用
  if (typeof window !== 'undefined' && window.addEventListener) {
    window.addEventListener('unhandledrejection', (event) => {
      console.error('[全局未处理Promise错误]', event.reason)
      // 阻止默认处理
      event.preventDefault()
    })
  }
  
  // 业务日志上报
  const reportError = (error) => {
    // 在这里实现错误上报逻辑
    // 如发送到服务器或第三方监控平台
    console.warn('[错误上报]', error)
    
    // TODO: 实现实际的错误上报逻辑
    // 示例：
    // uni.request({
    //   url: 'https://api.example.com/log/error',
    //   method: 'POST',
    //   data: { error }
    // })
  }
  
  // 返回上报函数供手动调用
  return {
    reportError
  }
}

// 页面错误处理 Mixin
export const errorHandlerMixin = {
  onLoad() {
    this.$errorHandler = handleError
  },
  methods: {
    // 包装API调用
    async callApi(apiPromise, options = {}) {
      try {
        return await apiPromise
      } catch (error) {
        // 处理错误并返回错误对象
        const handledError = handleError(error, options)
        
        // 如果需要抛出错误继续传递，则重新抛出
        if (options.rethrow) {
          throw handledError
        }
        
        // 返回空值或指定的默认值
        return options.defaultValue || null
      }
    }
  }
} 