
.refresh-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 120rpx; /* 为底部按钮留出空间 */
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
}
.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}
.back-icon {
  width: 40rpx;
  height: 40rpx;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}
.navbar-right {
  width: 80rpx;
}

/* 刷新卡片 - 新设计 */
.refresh-card {
  margin: 30rpx;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #0069FF, #0052DD, #0045CC);
  box-shadow: 0 16rpx 32rpx rgba(0, 102, 255, 0.25);
  overflow: hidden;
  position: relative;
}
.refresh-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
  border-radius: 20rpx 20rpx 0 0;
  z-index: 1;
}
.refresh-card-content {
  display: flex;
  align-items: center;
  padding: 30rpx;
  position: relative;
  z-index: 2;
}
.refresh-card-left {
  margin-right: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 90rpx;
  height: 90rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 45rpx;
}
.refresh-big-icon {
  width: 54rpx;
  height: 54rpx;
}
.refresh-card-center {
  flex: 1;
}
.refresh-card-title {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 8rpx;
}
.refresh-card-count {
  font-size: 60rpx;
  font-weight: bold;
  color: #ffffff;
  line-height: 1.2;
}
.refresh-card-expire {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 8rpx;
}
.refresh-card-right {
  margin-left: 20rpx;
}
.use-now-btn {
  background-color: #ffffff;
  color: #0066FF;
  font-size: 28rpx;
  font-weight: bold;
  height: 70rpx;
  line-height: 70rpx;
  padding: 0 30rpx;
  border-radius: 35rpx;
  border: none;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);
  margin: 0;
  min-width: 170rpx;
  text-align: center;
  transition: all 0.2s ease;
}
.use-now-btn:active {
  transform: translateY(3rpx);
  box-shadow: 0 3rpx 8rpx rgba(0, 0, 0, 0.12);
}

/* 信息卡片 */
.info-card {
  background-color: #fff;
  margin: 30rpx;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}
.info-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}
.info-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}
.info-desc text {
  display: block;
  margin-bottom: 10rpx;
}

/* 套餐列表 */
.package-list {
  margin: 30rpx;
}
.package-item {
  display: flex;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.05);
  border: 2rpx solid transparent;
  transition: all 0.3s;
}
.package-item.active {
  border-color: #0066FF;
  background-color: #f0f6ff;
}
.package-select {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}
.select-dot {
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  border: 2rpx solid #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
}
.select-dot.selected {
  border-color: #0066FF;
}
.select-dot.selected::after {
  content: "";
  width: 24rpx;
  height: 24rpx;
  border-radius: 12rpx;
  background-color: #0066FF;
}
.package-content {
  flex: 1;
}
.package-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}
.package-details {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.package-count {
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
}
.package-validity {
  font-size: 28rpx;
  color: #666;
}
.package-feature {
  display: inline-block;
  font-size: 24rpx;
  color: #ff6600;
  background-color: rgba(255, 102, 0, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
}
.package-price {
  display: flex;
  align-items: center;
  color: #ff6600;
}
.price-symbol {
  font-size: 24rpx;
  margin-right: 2rpx;
}
.price-value {
  font-size: 36rpx;
  font-weight: bold;
}

/* FAQ部分 */
.faq-section {
  margin: 30rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}
.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 30rpx;
}
.faq-item {
  margin-bottom: 20rpx;
  border-bottom: 2rpx solid #f5f5f5;
  padding-bottom: 20rpx;
}
.faq-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}
.faq-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.faq-question {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.faq-icon {
  width: 32rpx;
  height: 32rpx;
  transform: rotate(90deg);
  transition: all 0.3s;
}
.faq-icon-open {
  transform: rotate(270deg);
}
.faq-answer {
  font-size: 26rpx;
  color: #666;
  margin-top: 20rpx;
  line-height: 1.6;
  padding-left: 20rpx;
  border-left: 4rpx solid #f0f0f0;
}

/* 底部购买栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.05);
}
.total-price {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}
.total-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6600;
}
.buy-button {
  background-color: #0066FF;
  color: #fff;
  font-size: 30rpx;
  height: 70rpx;
  line-height: 70rpx;
  padding: 0 40rpx;
  border-radius: 35rpx;
  margin: 0;
}
.buy-button:active {
  background-color: #0052CC;
}
