{"version": 3, "file": "product.js", "sources": ["subPackages/merchant-admin/pages/store/product.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW5ccGFnZXNcc3RvcmVccHJvZHVjdC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"product-container\">\r\n    <!-- 顶部导航栏 -->\r\n    <view class=\"header-area\">\r\n      <!-- 顶部安全区域 -->\r\n      <view class=\"safe-area-top\"></view>\r\n      \r\n      <!-- 自定义导航栏 -->\r\n      <view class=\"custom-navbar\">\r\n        <view class=\"navbar-left\" @click=\"goBack\">\r\n          <image src=\"/static/images/tabbar/最新返回键.png\" class=\"back-icon\" style=\"filter: brightness(0) invert(1);\"></image>\r\n        </view>\r\n        <view class=\"navbar-title\">商品管理</view>\r\n        <view class=\"navbar-right\">\r\n          <text class=\"add-product-text\" @click=\"navigateTo('addProduct')\">添加</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 分类切换栏 -->\r\n      <scroll-view class=\"category-scroll\" scroll-x show-scrollbar=\"false\">\r\n        <view class=\"category-tabs\">\r\n          <view \r\n            class=\"category-tab\" \r\n            :class=\"{ active: currentCategory === -1 }\"\r\n            @click=\"switchCategory(-1)\"\r\n          >\r\n            全部\r\n          </view>\r\n          <view \r\n            class=\"category-tab\" \r\n            :class=\"{ active: currentCategory === index }\"\r\n            v-for=\"(category, index) in categories\" \r\n            :key=\"index\"\r\n            @click=\"switchCategory(index)\"\r\n          >\r\n            {{category.name}}\r\n          </view>\r\n        </view>\r\n      </scroll-view>\r\n      \r\n      <!-- 搜索框 -->\r\n      <view class=\"search-box\">\r\n        <image src=\"/static/images/tabbar/搜索.png\" class=\"search-icon\"></image>\r\n        <input \r\n          type=\"text\" \r\n          class=\"search-input\" \r\n          placeholder=\"搜索商品名称/关键词\" \r\n          confirm-type=\"search\"\r\n          v-model=\"searchKeyword\"\r\n          @confirm=\"searchProducts\"\r\n        />\r\n        <text class=\"clear-icon\" v-if=\"searchKeyword\" @click=\"clearSearch\">×</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 背景装饰 -->\r\n    <view class=\"bg-decoration bg-circle-1\"></view>\r\n    <view class=\"bg-decoration bg-circle-2\"></view>\r\n    <view class=\"bg-decoration bg-circle-3\"></view>\r\n    \r\n    <!-- 内容区域 -->\r\n    <scroll-view \r\n      class=\"content-scroll\" \r\n      scroll-y \r\n      @scrolltolower=\"loadMore\"\r\n      refresher-enabled\r\n      :refresher-triggered=\"refreshing\"\r\n      @refresherrefresh=\"refreshList\"\r\n    >\r\n      <!-- 搜索结果提示 -->\r\n      <view class=\"search-result-tip\" v-if=\"searchKeyword\">\r\n        <text class=\"search-keyword\">\"{{searchKeyword}}\"</text>\r\n        <text class=\"search-count\">搜索结果：{{filteredProducts.length}}个商品</text>\r\n      </view>\r\n      \r\n      <!-- 批量操作工具栏 -->\r\n      <view class=\"batch-toolbar\" v-if=\"isSelectMode\">\r\n        <view class=\"selection-info\">\r\n          已选择 <text class=\"selected-count\">{{selectedProducts.length}}</text> 项\r\n        </view>\r\n        <view class=\"batch-actions\">\r\n          <view class=\"batch-btn\" @click=\"batchSetStatus(1)\">\r\n            <text class=\"btn-text\">上架</text>\r\n          </view>\r\n          <view class=\"batch-btn\" @click=\"batchSetStatus(0)\">\r\n            <text class=\"btn-text\">下架</text>\r\n          </view>\r\n          <view class=\"batch-btn delete\" @click=\"confirmBatchDelete\">\r\n            <text class=\"btn-text\">删除</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 商品列表 -->\r\n      <view class=\"product-list\">\r\n        <view \r\n          class=\"product-item\" \r\n          :class=\"{ selected: isProductSelected(product.id) }\"\r\n          v-for=\"product in filteredProducts\" \r\n          :key=\"product.id\"\r\n          @tap=\"onProductTap(product)\"\r\n          @longpress=\"toggleSelectMode\"\r\n        >\r\n          <!-- 选择框 -->\r\n          <view class=\"select-checkbox\" v-if=\"isSelectMode\" @tap.stop=\"toggleProductSelection(product.id)\">\r\n            <view class=\"checkbox\" :class=\"{ checked: isProductSelected(product.id) }\"></view>\r\n          </view>\r\n          \r\n          <!-- 商品内容 -->\r\n          <view class=\"product-content\">\r\n            <image :src=\"product.image\" mode=\"aspectFill\" class=\"product-image\"></image>\r\n            <view class=\"product-info\">\r\n              <view class=\"product-name\">{{product.name}}</view>\r\n              <view class=\"product-category\">{{getCategoryName(product.category_id)}}</view>\r\n              <view class=\"product-price\">\r\n                <text class=\"price\">¥{{product.price}}</text>\r\n                <text class=\"original-price\" v-if=\"product.original_price\">¥{{product.original_price}}</text>\r\n              </view>\r\n              <view class=\"product-stats\">\r\n                <text class=\"sales-count\">销量 {{product.sales}}</text>\r\n                <text class=\"inventory\">库存 {{product.inventory}}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 商品状态 -->\r\n          <view class=\"product-status\" :class=\"{ 'status-off': product.status === 0 }\">\r\n            {{product.status === 1 ? '在售' : '下架'}}\r\n          </view>\r\n          \r\n          <!-- 快捷操作 -->\r\n          <view class=\"quick-actions\" @tap.stop>\r\n            <view class=\"action-btn\" @click=\"toggleProductStatus(product)\">\r\n              {{product.status === 1 ? '下架' : '上架'}}\r\n            </view>\r\n            <view class=\"action-btn edit\" @click=\"editProduct(product)\">\r\n              编辑\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 加载更多状态 -->\r\n      <view class=\"loading-more\" v-if=\"loading\">\r\n        <text class=\"loading-text\">加载中...</text>\r\n      </view>\r\n      \r\n      <!-- 无数据提示 -->\r\n      <view class=\"no-data\" v-if=\"filteredProducts.length === 0 && !loading\">\r\n        <image src=\"/static/images/tabbar/无数据.png\" class=\"no-data-icon\"></image>\r\n        <text class=\"no-data-text\">{{searchKeyword ? '没有找到相关商品' : '暂无商品，快去添加吧'}}</text>\r\n      </view>\r\n      \r\n      <!-- 底部安全区域 -->\r\n      <view class=\"safe-area-bottom\"></view>\r\n    </scroll-view>\r\n    \r\n    <!-- 批量操作浮动按钮 -->\r\n    <view class=\"float-actions\" v-if=\"!isSelectMode\">\r\n      <view class=\"float-btn select-mode-btn\" @click=\"toggleSelectMode\">\r\n        <text class=\"float-btn-text\">批量管理</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 批量删除确认弹窗 -->\r\n    <view class=\"modal-overlay\" v-if=\"showDeleteModal\"></view>\r\n    <view class=\"delete-modal\" v-if=\"showDeleteModal\">\r\n      <view class=\"modal-header\">\r\n        <text class=\"modal-title\">删除商品</text>\r\n      </view>\r\n      <view class=\"modal-content\">\r\n        <view class=\"confirm-message\">确定要删除所选的 {{selectedProducts.length}} 个商品吗？</view>\r\n        <view class=\"confirm-warning\">删除后将无法恢复，请谨慎操作</view>\r\n      </view>\r\n      <view class=\"modal-footer\">\r\n        <button class=\"modal-btn cancel\" @click=\"cancelDelete\">取消</button>\r\n        <button class=\"modal-btn confirm delete\" @click=\"confirmDelete\">删除</button>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 商品分类\r\n      categories: [\r\n        { id: 1, name: '热销推荐' },\r\n        { id: 2, name: '主食' },\r\n        { id: 3, name: '小吃' },\r\n        { id: 4, name: '饮品' },\r\n        { id: 5, name: '甜点' }\r\n      ],\r\n      // 当前选中的分类索引，-1 表示全部\r\n      currentCategory: -1,\r\n      // 商品列表\r\n      products: [\r\n        {\r\n          id: 1,\r\n          name: '磁州特色小酥肉',\r\n          image: '/static/images/product-1.jpg',\r\n          category_id: 1,\r\n          price: '28.00',\r\n          original_price: '35.00',\r\n          sales: 156,\r\n          inventory: 999,\r\n          status: 1\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '手工水饺',\r\n          image: '/static/images/product-2.jpg',\r\n          category_id: 2,\r\n          price: '22.00',\r\n          original_price: '',\r\n          sales: 208,\r\n          inventory: 500,\r\n          status: 1\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '香辣鸡翅',\r\n          image: '/static/images/product-3.jpg',\r\n          category_id: 3,\r\n          price: '15.00',\r\n          original_price: '18.00',\r\n          sales: 321,\r\n          inventory: 100,\r\n          status: 1\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '冰镇酸梅汤',\r\n          image: '/static/images/product-4.jpg',\r\n          category_id: 4,\r\n          price: '8.00',\r\n          original_price: '',\r\n          sales: 213,\r\n          inventory: 200,\r\n          status: 1\r\n        },\r\n        {\r\n          id: 5,\r\n          name: '红豆双皮奶',\r\n          image: '/static/images/product-5.jpg',\r\n          category_id: 5,\r\n          price: '12.00',\r\n          original_price: '15.00',\r\n          sales: 87,\r\n          inventory: 150,\r\n          status: 0\r\n        }\r\n      ],\r\n      searchKeyword: '',\r\n      loading: false,\r\n      refreshing: false,\r\n      isSelectMode: false,\r\n      selectedProducts: [],\r\n      showDeleteModal: false,\r\n      statusBarHeight: 20\r\n    }\r\n  },\r\n  computed: {\r\n    // 根据分类和搜索关键词过滤商品\r\n    filteredProducts() {\r\n      let result = [...this.products];\r\n      \r\n      // 按分类筛选\r\n      if (this.currentCategory !== -1) {\r\n        const categoryId = this.categories[this.currentCategory].id;\r\n        result = result.filter(item => item.category_id === categoryId);\r\n      }\r\n      \r\n      // 按关键词搜索\r\n      if (this.searchKeyword) {\r\n        const keyword = this.searchKeyword.toLowerCase();\r\n        result = result.filter(item => \r\n          item.name.toLowerCase().includes(keyword)\r\n        );\r\n      }\r\n      \r\n      return result;\r\n    }\r\n  },\r\n  onLoad() {\r\n    // 页面加载完成后的处理\r\n    this.setStatusBarHeight();\r\n  },\r\n  methods: {\r\n    // 设置状态栏高度\r\n    setStatusBarHeight() {\r\n      // 获取系统信息设置状态栏高度\r\n      uni.getSystemInfo({\r\n        success: (res) => {\r\n          this.statusBarHeight = res.statusBarHeight;\r\n          // 将状态栏高度设置为CSS变量\r\n          if (typeof document !== 'undefined') {\r\n            document.documentElement.style.setProperty('--status-bar-height', `${this.statusBarHeight}px`);\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 返回上一页\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    \r\n    // 根据分类ID获取分类名称\r\n    getCategoryName(categoryId) {\r\n      const category = this.categories.find(item => item.id === categoryId);\r\n      return category ? category.name : '未分类';\r\n    },\r\n    \r\n    // 切换分类\r\n    switchCategory(index) {\r\n      this.currentCategory = index;\r\n    },\r\n    \r\n    // 搜索商品\r\n    searchProducts() {\r\n      // 搜索逻辑已在计算属性中实现\r\n      // 这里可以添加额外处理，如收起键盘\r\n      uni.hideKeyboard();\r\n    },\r\n    \r\n    // 清除搜索\r\n    clearSearch() {\r\n      this.searchKeyword = '';\r\n    },\r\n    \r\n    // 刷新列表\r\n    refreshList() {\r\n      this.refreshing = true;\r\n      \r\n      // 模拟刷新请求\r\n      setTimeout(() => {\r\n        // 这里可以添加实际的数据刷新逻辑\r\n        this.refreshing = false;\r\n        uni.showToast({\r\n          title: '刷新成功',\r\n          icon: 'none'\r\n        });\r\n      }, 1000);\r\n    },\r\n    \r\n    // 加载更多\r\n    loadMore() {\r\n      if (this.loading) return;\r\n      \r\n      this.loading = true;\r\n      \r\n      // 模拟加载更多请求\r\n      setTimeout(() => {\r\n        // 这里可以添加实际的加载逻辑\r\n        this.loading = false;\r\n      }, 1000);\r\n    },\r\n    \r\n    // 切换选择模式\r\n    toggleSelectMode() {\r\n      this.isSelectMode = !this.isSelectMode;\r\n      if (!this.isSelectMode) {\r\n        this.selectedProducts = [];\r\n      }\r\n    },\r\n    \r\n    // 判断商品是否被选中\r\n    isProductSelected(productId) {\r\n      return this.selectedProducts.includes(productId);\r\n    },\r\n    \r\n    // 切换商品选择状态\r\n    toggleProductSelection(productId) {\r\n      const index = this.selectedProducts.indexOf(productId);\r\n      if (index === -1) {\r\n        this.selectedProducts.push(productId);\r\n      } else {\r\n        this.selectedProducts.splice(index, 1);\r\n      }\r\n    },\r\n    \r\n    // 商品点击处理\r\n    onProductTap(product) {\r\n      if (this.isSelectMode) {\r\n        this.toggleProductSelection(product.id);\r\n      } else {\r\n        this.editProduct(product);\r\n      }\r\n    },\r\n    \r\n    // 切换商品上下架状态\r\n    toggleProductStatus(product) {\r\n      product.status = product.status === 1 ? 0 : 1;\r\n      \r\n      uni.showToast({\r\n        title: product.status === 1 ? '商品已上架' : '商品已下架',\r\n        icon: 'success'\r\n      });\r\n    },\r\n    \r\n    // 编辑商品\r\n    editProduct(product) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/merchant-admin/pages/store/product-edit?id=${product.id}`\r\n      });\r\n    },\r\n    \r\n    // 批量设置商品状态\r\n    batchSetStatus(status) {\r\n      if (this.selectedProducts.length === 0) {\r\n        uni.showToast({\r\n          title: '请先选择商品',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      // 更新选中商品的状态\r\n      this.products.forEach(product => {\r\n        if (this.selectedProducts.includes(product.id)) {\r\n          product.status = status;\r\n        }\r\n      });\r\n      \r\n      uni.showToast({\r\n        title: status === 1 ? '批量上架成功' : '批量下架成功',\r\n        icon: 'success'\r\n      });\r\n      \r\n      // 退出选择模式\r\n      this.toggleSelectMode();\r\n    },\r\n    \r\n    // 显示删除确认弹窗\r\n    confirmBatchDelete() {\r\n      if (this.selectedProducts.length === 0) {\r\n        uni.showToast({\r\n          title: '请先选择商品',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      this.showDeleteModal = true;\r\n    },\r\n    \r\n    // 取消删除\r\n    cancelDelete() {\r\n      this.showDeleteModal = false;\r\n    },\r\n    \r\n    // 确认删除\r\n    confirmDelete() {\r\n      // 从商品列表中移除选中的商品\r\n      this.products = this.products.filter(product => \r\n        !this.selectedProducts.includes(product.id)\r\n      );\r\n      \r\n      this.showDeleteModal = false;\r\n      \r\n      uni.showToast({\r\n        title: '删除成功',\r\n        icon: 'success'\r\n      });\r\n      \r\n      // 退出选择模式\r\n      this.toggleSelectMode();\r\n    },\r\n    \r\n    // 导航到指定页面\r\n    navigateTo(page) {\r\n      const pageMap = {\r\n        addProduct: '/subPackages/merchant-admin/pages/store/product-edit'\r\n      };\r\n      \r\n      const url = pageMap[page];\r\n      if (url) {\r\n        uni.navigateTo({ url });\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.product-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F8FC;\r\n  position: relative;\r\n  padding-top: calc(220rpx + var(--status-bar-height, 20px));\r\n}\r\n\r\n/* 顶部导航栏样式 */\r\n.header-area {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 100;\r\n  background-color: #0A84FF;\r\n  color: #fff;\r\n}\r\n\r\n.safe-area-top {\r\n  height: var(--status-bar-height, 20px);\r\n  width: 100%;\r\n}\r\n\r\n.custom-navbar {\r\n  height: 110rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0 30rpx;\r\n}\r\n\r\n.navbar-title {\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n}\r\n\r\n.navbar-left, .navbar-right {\r\n  width: 80rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.navbar-right {\r\n  width: auto;\r\n}\r\n\r\n.add-product-text {\r\n  font-size: 32rpx;\r\n  color: #FFFFFF;\r\n  padding: 10rpx 0;\r\n}\r\n\r\n.back-icon {\r\n  width: 48rpx;\r\n  height: 48rpx;\r\n}\r\n\r\n/* 分类切换栏 */\r\n.category-scroll {\r\n  width: 100%;\r\n  white-space: nowrap;\r\n  background-color: #0A84FF;\r\n  padding: 0 20rpx;\r\n}\r\n\r\n.category-tabs {\r\n  display: flex;\r\n  padding-bottom: 20rpx;\r\n}\r\n\r\n.category-tab {\r\n  padding: 10rpx 30rpx;\r\n  font-size: 28rpx;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  position: relative;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.category-tab.active {\r\n  color: #FFFFFF;\r\n  font-weight: 600;\r\n}\r\n\r\n.category-tab.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -10rpx;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 40rpx;\r\n  height: 6rpx;\r\n  background-color: #FFFFFF;\r\n  border-radius: 3rpx;\r\n}\r\n\r\n/* 搜索框 */\r\n.search-box {\r\n  display: flex;\r\n  align-items: center;\r\n  background-color: #FFFFFF;\r\n  border-radius: 40rpx;\r\n  margin: 0 30rpx 20rpx;\r\n  padding: 0 20rpx;\r\n  position: relative;\r\n}\r\n\r\n.search-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  margin-right: 10rpx;\r\n}\r\n\r\n.search-input {\r\n  height: 80rpx;\r\n  flex: 1;\r\n  font-size: 28rpx;\r\n  color: #333;\r\n}\r\n\r\n.clear-icon {\r\n  font-size: 40rpx;\r\n  color: #999;\r\n  padding: 0 10rpx;\r\n}\r\n\r\n/* 背景装饰样式 */\r\n.bg-decoration {\r\n  position: absolute;\r\n  z-index: -1;\r\n  border-radius: 50%;\r\n  opacity: 0.07;\r\n  background-color: #0A84FF;\r\n}\r\n\r\n.bg-circle-1 {\r\n  top: -100rpx;\r\n  right: -150rpx;\r\n  width: 400rpx;\r\n  height: 400rpx;\r\n}\r\n\r\n.bg-circle-2 {\r\n  top: 20%;\r\n  left: -200rpx;\r\n  width: 500rpx;\r\n  height: 500rpx;\r\n}\r\n\r\n.bg-circle-3 {\r\n  bottom: 10%;\r\n  right: -100rpx;\r\n  width: 300rpx;\r\n  height: 300rpx;\r\n}\r\n\r\n/* 内容区域 */\r\n.content-scroll {\r\n  height: calc(100vh - 220rpx - var(--status-bar-height, 20px));\r\n  padding: 30rpx;\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* 搜索结果提示 */\r\n.search-result-tip {\r\n  margin-bottom: 20rpx;\r\n  font-size: 28rpx;\r\n  color: #666;\r\n}\r\n\r\n.search-keyword {\r\n  color: #0A84FF;\r\n  font-weight: 600;\r\n  margin-right: 10rpx;\r\n}\r\n\r\n.search-count {\r\n  color: #999;\r\n}\r\n\r\n/* 批量操作工具栏 */\r\n.batch-toolbar {\r\n  background-color: #FFFFFF;\r\n  border-radius: 16rpx;\r\n  padding: 20rpx;\r\n  margin-bottom: 20rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.selection-info {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n}\r\n\r\n.selected-count {\r\n  color: #0A84FF;\r\n  font-weight: 600;\r\n}\r\n\r\n.batch-actions {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.batch-btn {\r\n  padding: 10rpx 20rpx;\r\n  border-radius: 30rpx;\r\n  background-color: rgba(10, 132, 255, 0.1);\r\n  margin-left: 20rpx;\r\n}\r\n\r\n.batch-btn.delete {\r\n  background-color: rgba(255, 59, 48, 0.1);\r\n}\r\n\r\n.btn-text {\r\n  font-size: 26rpx;\r\n  color: #0A84FF;\r\n}\r\n\r\n.delete .btn-text {\r\n  color: #FF3B30;\r\n}\r\n\r\n/* 商品列表 */\r\n.product-list {\r\n  padding-bottom: 100rpx;\r\n}\r\n\r\n.product-item {\r\n  background-color: #FFFFFF;\r\n  border-radius: 16rpx;\r\n  margin-bottom: 20rpx;\r\n  position: relative;\r\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);\r\n  transition: all 0.3s;\r\n}\r\n\r\n.product-item.selected {\r\n  background-color: rgba(10, 132, 255, 0.05);\r\n  border: 2rpx solid #0A84FF;\r\n}\r\n\r\n.product-content {\r\n  display: flex;\r\n  padding: 20rpx;\r\n}\r\n\r\n.select-checkbox {\r\n  position: absolute;\r\n  top: 20rpx;\r\n  left: 20rpx;\r\n  z-index: 10;\r\n}\r\n\r\n.checkbox {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  border-radius: 50%;\r\n  border: 2rpx solid #CCCCCC;\r\n  background-color: #FFFFFF;\r\n}\r\n\r\n.checkbox.checked {\r\n  background-color: #0A84FF;\r\n  border-color: #0A84FF;\r\n  position: relative;\r\n}\r\n\r\n.checkbox.checked::after {\r\n  content: '✓';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #FFFFFF;\r\n  font-size: 24rpx;\r\n}\r\n\r\n.product-image {\r\n  width: 160rpx;\r\n  height: 160rpx;\r\n  border-radius: 12rpx;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.product-info {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n}\r\n\r\n.product-name {\r\n  font-size: 30rpx;\r\n  color: #333;\r\n  font-weight: 500;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.product-category {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  margin-bottom: 10rpx;\r\n  background-color: #F5F8FC;\r\n  display: inline-block;\r\n  padding: 6rpx 16rpx;\r\n  border-radius: 20rpx;\r\n}\r\n\r\n.product-price {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.price {\r\n  font-size: 32rpx;\r\n  color: #FF3B30;\r\n  font-weight: 600;\r\n  margin-right: 10rpx;\r\n}\r\n\r\n.original-price {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  text-decoration: line-through;\r\n}\r\n\r\n.product-stats {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.sales-count {\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.product-status {\r\n  position: absolute;\r\n  top: 20rpx;\r\n  right: 20rpx;\r\n  background-color: rgba(52, 199, 89, 0.1);\r\n  color: #34C759;\r\n  font-size: 24rpx;\r\n  padding: 4rpx 16rpx;\r\n  border-radius: 20rpx;\r\n}\r\n\r\n.status-off {\r\n  background-color: rgba(142, 142, 147, 0.1);\r\n  color: #8E8E93;\r\n}\r\n\r\n.quick-actions {\r\n  display: flex;\r\n  padding: 20rpx;\r\n  border-top: 2rpx solid #F2F2F7;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.action-btn {\r\n  padding: 10rpx 20rpx;\r\n  border-radius: 30rpx;\r\n  background-color: rgba(10, 132, 255, 0.1);\r\n  font-size: 26rpx;\r\n  color: #0A84FF;\r\n  margin-left: 20rpx;\r\n}\r\n\r\n.action-btn.edit {\r\n  background-color: rgba(52, 199, 89, 0.1);\r\n  color: #34C759;\r\n}\r\n\r\n/* 加载更多状态 */\r\n.loading-more {\r\n  text-align: center;\r\n  padding: 30rpx 0;\r\n}\r\n\r\n.loading-text {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n}\r\n\r\n/* 无数据提示 */\r\n.no-data {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 100rpx 0;\r\n}\r\n\r\n.no-data-icon {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.no-data-text {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n}\r\n\r\n/* 底部安全区域 */\r\n.safe-area-bottom {\r\n  height: 100rpx;\r\n}\r\n\r\n/* 批量操作浮动按钮 */\r\n.float-actions {\r\n  position: fixed;\r\n  right: 30rpx;\r\n  bottom: calc(100rpx + env(safe-area-inset-bottom));\r\n  z-index: 50;\r\n}\r\n\r\n.float-btn {\r\n  width: 180rpx;\r\n  height: 80rpx;\r\n  border-radius: 40rpx;\r\n  background: linear-gradient(135deg, #0A84FF, #0055FF);\r\n  color: #FFFFFF;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 6rpx 16rpx rgba(10, 132, 255, 0.3);\r\n}\r\n\r\n.float-btn-text {\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 弹窗样式 */\r\n.modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.6);\r\n  z-index: 999;\r\n}\r\n\r\n.delete-modal {\r\n  position: fixed;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  width: 80%;\r\n  background-color: #FFFFFF;\r\n  border-radius: 24rpx;\r\n  overflow: hidden;\r\n  z-index: 1000;\r\n  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.modal-header {\r\n  padding: 30rpx;\r\n  text-align: center;\r\n  border-bottom: 2rpx solid #F2F2F7;\r\n}\r\n\r\n.modal-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.modal-content {\r\n  padding: 30rpx;\r\n}\r\n\r\n.confirm-message {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  text-align: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.confirm-warning {\r\n  font-size: 24rpx;\r\n  color: #FF3B30;\r\n  text-align: center;\r\n}\r\n\r\n.modal-footer {\r\n  display: flex;\r\n  border-top: 2rpx solid #F2F2F7;\r\n}\r\n\r\n.modal-btn {\r\n  flex: 1;\r\n  height: 100rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 32rpx;\r\n  background-color: transparent;\r\n}\r\n\r\n.modal-btn::after {\r\n  border: none;\r\n}\r\n\r\n.cancel {\r\n  color: #999;\r\n  border-right: 1px solid #F2F2F7;\r\n}\r\n\r\n.confirm {\r\n  color: #0A84FF;\r\n  font-weight: 500;\r\n}\r\n\r\n.delete {\r\n  color: #FF3B30;\r\n}\r\n\r\n/* 适配暗黑模式 */\r\n@media (prefers-color-scheme: dark) {\r\n  .product-container {\r\n    background-color: #1C1C1E;\r\n  }\r\n  \r\n  .search-box {\r\n    background-color: #3A3A3C;\r\n  }\r\n  \r\n  .search-input {\r\n    color: #FFFFFF;\r\n  }\r\n  \r\n  .batch-toolbar,\r\n  .product-item,\r\n  .delete-modal {\r\n    background-color: #2C2C2E;\r\n  }\r\n  \r\n  .product-item.selected {\r\n    background-color: rgba(10, 132, 255, 0.15);\r\n  }\r\n  \r\n  .product-name {\r\n    color: #FFFFFF;\r\n  }\r\n  \r\n  .product-category {\r\n    background-color: #3A3A3C;\r\n  }\r\n  \r\n  .modal-title,\r\n  .confirm-message {\r\n    color: #FFFFFF;\r\n  }\r\n  \r\n  .modal-header,\r\n  .modal-footer,\r\n  .quick-actions {\r\n    border-color: #3A3A3C;\r\n  }\r\n  \r\n  .checkbox {\r\n    background-color: #2C2C2E;\r\n    border-color: #8E8E93;\r\n  }\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin/pages/store/product.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAuLA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA;AAAA,MAEL,YAAY;AAAA,QACV,EAAE,IAAI,GAAG,MAAM,OAAQ;AAAA,QACvB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,QACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,QACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,QACrB,EAAE,IAAI,GAAG,MAAM,KAAK;AAAA,MACrB;AAAA;AAAA,MAED,iBAAiB;AAAA;AAAA,MAEjB,UAAU;AAAA,QACR;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,OAAO;AAAA,UACP,gBAAgB;AAAA,UAChB,OAAO;AAAA,UACP,WAAW;AAAA,UACX,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,OAAO;AAAA,UACP,gBAAgB;AAAA,UAChB,OAAO;AAAA,UACP,WAAW;AAAA,UACX,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,OAAO;AAAA,UACP,gBAAgB;AAAA,UAChB,OAAO;AAAA,UACP,WAAW;AAAA,UACX,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,OAAO;AAAA,UACP,gBAAgB;AAAA,UAChB,OAAO;AAAA,UACP,WAAW;AAAA,UACX,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,OAAO;AAAA,UACP,gBAAgB;AAAA,UAChB,OAAO;AAAA,UACP,WAAW;AAAA,UACX,QAAQ;AAAA,QACV;AAAA,MACD;AAAA,MACD,eAAe;AAAA,MACf,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,kBAAkB,CAAE;AAAA,MACpB,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,IACnB;AAAA,EACD;AAAA,EACD,UAAU;AAAA;AAAA,IAER,mBAAmB;AACjB,UAAI,SAAS,CAAC,GAAG,KAAK,QAAQ;AAG9B,UAAI,KAAK,oBAAoB,IAAI;AAC/B,cAAM,aAAa,KAAK,WAAW,KAAK,eAAe,EAAE;AACzD,iBAAS,OAAO,OAAO,UAAQ,KAAK,gBAAgB,UAAU;AAAA,MAChE;AAGA,UAAI,KAAK,eAAe;AACtB,cAAM,UAAU,KAAK,cAAc,YAAW;AAC9C,iBAAS,OAAO;AAAA,UAAO,UACrB,KAAK,KAAK,cAAc,SAAS,OAAO;AAAA;MAE5C;AAEA,aAAO;AAAA,IACT;AAAA,EACD;AAAA,EACD,SAAS;AAEP,SAAK,mBAAkB;AAAA,EACxB;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,qBAAqB;AAEnBA,oBAAAA,MAAI,cAAc;AAAA,QAChB,SAAS,CAAC,QAAQ;AAChB,eAAK,kBAAkB,IAAI;AAE3B,cAAI,OAAO,aAAa,aAAa;AACnC,qBAAS,gBAAgB,MAAM,YAAY,uBAAuB,GAAG,KAAK,eAAe,IAAI;AAAA,UAC/F;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA;AAAA,IAGD,gBAAgB,YAAY;AAC1B,YAAM,WAAW,KAAK,WAAW,KAAK,UAAQ,KAAK,OAAO,UAAU;AACpE,aAAO,WAAW,SAAS,OAAO;AAAA,IACnC;AAAA;AAAA,IAGD,eAAe,OAAO;AACpB,WAAK,kBAAkB;AAAA,IACxB;AAAA;AAAA,IAGD,iBAAiB;AAGfA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA;AAAA,IAGD,cAAc;AACZ,WAAK,gBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,cAAc;AACZ,WAAK,aAAa;AAGlB,iBAAW,MAAM;AAEf,aAAK,aAAa;AAClBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACF,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,IAGD,WAAW;AACT,UAAI,KAAK;AAAS;AAElB,WAAK,UAAU;AAGf,iBAAW,MAAM;AAEf,aAAK,UAAU;AAAA,MAChB,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,IAGD,mBAAmB;AACjB,WAAK,eAAe,CAAC,KAAK;AAC1B,UAAI,CAAC,KAAK,cAAc;AACtB,aAAK,mBAAmB;MAC1B;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB,WAAW;AAC3B,aAAO,KAAK,iBAAiB,SAAS,SAAS;AAAA,IAChD;AAAA;AAAA,IAGD,uBAAuB,WAAW;AAChC,YAAM,QAAQ,KAAK,iBAAiB,QAAQ,SAAS;AACrD,UAAI,UAAU,IAAI;AAChB,aAAK,iBAAiB,KAAK,SAAS;AAAA,aAC/B;AACL,aAAK,iBAAiB,OAAO,OAAO,CAAC;AAAA,MACvC;AAAA,IACD;AAAA;AAAA,IAGD,aAAa,SAAS;AACpB,UAAI,KAAK,cAAc;AACrB,aAAK,uBAAuB,QAAQ,EAAE;AAAA,aACjC;AACL,aAAK,YAAY,OAAO;AAAA,MAC1B;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB,SAAS;AAC3B,cAAQ,SAAS,QAAQ,WAAW,IAAI,IAAI;AAE5CA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,QAAQ,WAAW,IAAI,UAAU;AAAA,QACxC,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,YAAY,SAAS;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,2DAA2D,QAAQ,EAAE;AAAA,MAC5E,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,eAAe,QAAQ;AACrB,UAAI,KAAK,iBAAiB,WAAW,GAAG;AACtCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAGA,WAAK,SAAS,QAAQ,aAAW;AAC/B,YAAI,KAAK,iBAAiB,SAAS,QAAQ,EAAE,GAAG;AAC9C,kBAAQ,SAAS;AAAA,QACnB;AAAA,MACF,CAAC;AAEDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,WAAW,IAAI,WAAW;AAAA,QACjC,MAAM;AAAA,MACR,CAAC;AAGD,WAAK,iBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,qBAAqB;AACnB,UAAI,KAAK,iBAAiB,WAAW,GAAG;AACtCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,WAAK,kBAAkB;AAAA,IACxB;AAAA;AAAA,IAGD,eAAe;AACb,WAAK,kBAAkB;AAAA,IACxB;AAAA;AAAA,IAGD,gBAAgB;AAEd,WAAK,WAAW,KAAK,SAAS;AAAA,QAAO,aACnC,CAAC,KAAK,iBAAiB,SAAS,QAAQ,EAAE;AAAA;AAG5C,WAAK,kBAAkB;AAEvBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAGD,WAAK,iBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,WAAW,MAAM;AACf,YAAM,UAAU;AAAA,QACd,YAAY;AAAA;AAGd,YAAM,MAAM,QAAQ,IAAI;AACxB,UAAI,KAAK;AACPA,sBAAAA,MAAI,WAAW,EAAE,IAAE,CAAG;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACleA,GAAG,WAAW,eAAe;"}