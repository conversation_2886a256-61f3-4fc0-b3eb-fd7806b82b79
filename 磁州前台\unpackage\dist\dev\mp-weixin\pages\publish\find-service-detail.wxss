
.find-service-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 110rpx;
  padding-top: 150rpx; /* 增加顶部内边距，为固定导航栏留出空间 */
}
.find-service-wrapper {
  padding: 24rpx;
}
.content-card {
  margin-bottom: 24rpx;
  border-radius: 24rpx;
  background-color: #fff;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 需求基本信息卡片 */
.service-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.service-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.service-budget {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff4d4f;
}
.service-meta {
  margin-bottom: 24rpx;
}
.service-tag-group {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12rpx;
}
.service-tag {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
  margin-bottom: 12rpx;
}
.service-publish-time {
  font-size: 24rpx;
  color: #999;
}

/* 需求描述 */
.service-description {
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
}
.desc-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 基本信息 */
.service-basic-info {
  display: flex;
  flex-wrap: wrap;
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}
.info-item {
  width: 50%;
  padding: 12rpx 24rpx;
  box-sizing: border-box;
}
.info-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}
.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 需求详情 */
.detail-list {
  display: flex;
  flex-direction: column;
}
.detail-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.detail-item:last-child {
  border-bottom: none;
}
.detail-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}
.detail-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 补充说明 */
.notes-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  white-space: pre-wrap;
}

/* 发布者信息 */
.publisher-header {
  display: flex;
  align-items: center;
}
.publisher-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
}
.publisher-avatar image {
  width: 100%;
  height: 100%;
}
.publisher-info {
  flex: 1;
}
.publisher-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}
.publisher-meta {
  display: flex;
  align-items: center;
}
.publisher-type, .publisher-rating {
  font-size: 24rpx;
  color: #666;
  margin-right: 16rpx;
}

/* 相似需求 */
.similar-list {
  display: flex;
  flex-direction: column;
}
.similar-item {
  padding: 20rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}
.similar-service-info {
  display: flex;
  flex-direction: column;
}
.similar-service-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}
.similar-service-budget {
  font-size: 28rpx;
  color: #ff4d4f;
  margin-bottom: 8rpx;
}
.similar-service-meta {
  font-size: 24rpx;
  color: #999;
}

/* 底部互动工具栏 */
.interaction-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10rpx 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  height: 120rpx;
  z-index: 100;
}
.toolbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6rpx 0;
  margin: 0 4rpx;
}
.toolbar-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}
.toolbar-text {
  font-size: 22rpx;
  color: #666;
}
.share-button {
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
  line-height: normal;
  border-radius: 0;
  flex: 1;
}
.share-button::after {
  display: none;
}
.call-button {
  flex: 3; /* 占据更多空间，原来是2，现在改为3让按钮更长 */
  background: linear-gradient(135deg, #0052CC, #0066FF);
  height: 90rpx;
  margin: 0 0 0 10rpx;
  border-radius: 45rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
}
.call-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.call-text {
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  line-height: 1.2;
}
.call-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 20rpx;
  line-height: 1.2;
}

/* 隐藏原来的底部操作栏 */
.action-bar {
  display: none;
}

/* 隐藏的分享按钮 */
.hidden-share-btn {
  position: absolute;
  top: -9999rpx;
  left: -9999rpx;
  width: 0;
  height: 0;
  padding: 0;
  margin: 0;
  opacity: 0;
}

/* 悬浮海报按钮 */
.float-poster-btn {
  position: fixed;
  right: 30rpx;
  bottom: 200rpx;
  width: 100rpx;
  height: 100rpx;
  background: rgba(240, 240, 240, 0.9);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.08);
  z-index: 90;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1rpx solid rgba(230, 230, 230, 0.6);
  transition: all 0.2s ease;
}
.float-poster-btn:active {
  transform: scale(0.95);
  box-shadow: 0 3rpx 10rpx rgba(0,0,0,0.08);
}
.poster-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 4rpx;
}
.poster-text {
  font-size: 20rpx;
  color: #444;
  line-height: 1;
}

/* 相关服务推荐样式 */
.related-services-card {
  margin-top: 12px;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}
.related-services-content {
  padding: 0 16px 16px;
  overflow: hidden;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  position: relative;
  padding-left: 10px;
}
.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 4px;
  width: 3px;
  height: 16px;
  background-color: #0052CC;
  border-radius: 3px;
}

/* 相关服务列表样式 */
.related-services-list {
  margin-bottom: 12px;
}
.related-service-item {
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}
.related-service-item:last-child {
  border-bottom: none;
}
.service-item-content {
  display: flex;
  align-items: center;
}
.service-item-left {
  margin-right: 12px;
}
.provider-logo {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #f5f7fa;
}
.service-item-middle {
  flex: 1;
}
.service-item-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}
.service-item-provider {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}
.service-item-tags {
  display: flex;
  flex-wrap: wrap;
}
.service-item-tag {
  font-size: 12px;
  color: #666;
  background-color: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
  margin-right: 6px;
  margin-bottom: 4px;
}
.service-item-tag-more {
  font-size: 12px;
  color: #999;
  padding: 2px 0;
}
.service-item-right {
  text-align: right;
}
.service-item-price {
  font-size: 15px;
  color: #ff4d4f;
  font-weight: 500;
}

/* 查看更多按钮 */
.view-more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  background-color: #f7f9fc;
  border-radius: 8px;
  margin-top: 8px;
}
.view-more-text {
  font-size: 14px;
  color: #0052CC;
}
.view-more-icon {
  margin-left: 4px;
  font-size: 12px;
  color: #0052CC;
}

/* 空数据提示 */
.empty-related-services {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 0;
}
.empty-image {
  width: 80px;
  height: 80px;
  margin-bottom: 12px;
}
.empty-text {
  font-size: 14px;
  color: #999;
}
.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}
