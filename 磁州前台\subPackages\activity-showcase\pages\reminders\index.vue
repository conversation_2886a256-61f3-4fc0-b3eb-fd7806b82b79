<template>
  <view class="reminders-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" mode="aspectFit" class="back-icon"></image>
        </view>
        <view class="navbar-title">活动提醒</view>
        <view class="navbar-right">
          <view class="settings-btn" @click="showSettings">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <circle cx="12" cy="12" r="3" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
              <path d="M19.4 15a1.65 1.65 0 00.33 1.82l.06.06a2 2 0 010 2.83 2 2 0 01-2.83 0l-.06-.06a1.65 1.65 0 00-1.82-.33 1.65 1.65 0 00-1 1.51V21a2 2 0 01-2 2 2 2 0 01-2-2v-.09A1.65 1.65 0 009 19.4a1.65 1.65 0 00-1.82.33l-.06.06a2 2 0 01-2.83 0 2 2 0 010-2.83l.06-.06a1.65 1.65 0 00.33-1.82 1.65 1.65 0 00-1.51-1H3a2 2 0 01-2-2 2 2 0 012-2h.09A1.65 1.65 0 004.6 9a1.65 1.65 0 00-.33-1.82l-.06-.06a2 2 0 010-2.83 2 2 0 012.83 0l.06.06a1.65 1.65 0 001.82.33H9a1.65 1.65 0 001-1.51V3a2 2 0 012-2 2 2 0 012 2v.09a1.65 1.65 0 001 1.51 1.65 1.65 0 001.82-.33l.06-.06a2 2 0 012.83 0 2 2 0 010 2.83l-.06.06a1.65 1.65 0 00-.33 1.82V9a1.65 1.65 0 001.51 1H21a2 2 0 012 2 2 2 0 01-2 2h-.09a1.65 1.65 0 00-1.51 1z" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
      </view>
    </view>

    <!-- 提醒类型标签栏 -->
    <view class="reminder-tabs">
      <view 
        v-for="(tab, index) in reminderTabs" 
        :key="index"
        class="tab-item"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        <text class="tab-text">{{ tab.name }}</text>
        <view class="tab-indicator" v-if="currentTab === index" :style="{
          background: 'linear-gradient(90deg, #34C759 0%, #7ED321 100%)'
        }"></view>
      </view>
    </view>

    <!-- 提醒列表区域 -->
    <swiper class="reminders-swiper" :current="currentTab" @change="onSwiperChange">
      <swiper-item v-for="(tab, tabIndex) in reminderTabs" :key="tabIndex">
        <scroll-view 
          class="tab-content" 
          scroll-y 
          refresher-enabled
          :refresher-triggered="isRefreshing"
          @refresherrefresh="onRefresh"
          @scrolltolower="loadMore"
        >
          <view class="reminders-list">
            <view 
              v-for="reminder in getRemindersByType(tab.type)" 
              :key="reminder.id"
              class="reminder-card"
              :class="{ 'read': reminder.isRead }"
            >
              <!-- 提醒图标 -->
              <view class="reminder-icon" :style="{
                background: getReminderIconBg(reminder.category)
              }">
                <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
                  <path :d="getReminderIconPath(reminder.category)" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
              </view>
              
              <!-- 提醒内容 -->
              <view class="reminder-content" @click="viewReminderDetail(reminder)">
                <view class="reminder-header">
                  <text class="reminder-title">{{ reminder.title }}</text>
                  <text class="reminder-time">{{ reminder.time }}</text>
                </view>
                <text class="reminder-desc">{{ reminder.description }}</text>
                
                <!-- 活动信息 -->
                <view class="activity-info" v-if="reminder.activityInfo">
                  <image :src="reminder.activityInfo.image" class="activity-image" mode="aspectFill"></image>
                  <view class="activity-details">
                    <text class="activity-name">{{ reminder.activityInfo.name }}</text>
                    <view class="activity-meta">
                      <text class="activity-date">{{ reminder.activityInfo.date }}</text>
                      <text class="activity-location">{{ reminder.activityInfo.location }}</text>
                    </view>
                  </view>
                </view>
                
                <!-- 操作按钮 -->
                <view class="reminder-actions">
                  <view 
                    class="action-btn"
                    :style="{
                      background: getPrimaryActionBgColor(reminder.category),
                      color: '#FFFFFF'
                    }"
                    @click.stop="handlePrimaryAction(reminder)"
                  >
                    {{ getPrimaryActionText(reminder.category) }}
                  </view>
                  
                  <view 
                    class="action-btn secondary"
                    @click.stop="markAsRead(reminder)"
                    v-if="!reminder.isRead"
                  >
                    标记为已读
                  </view>
                  
                  <view 
                    class="action-btn secondary"
                    @click.stop="deleteReminder(reminder)"
                    v-else
                  >
                    删除
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 空状态 -->
          <view class="empty-state" v-if="getRemindersByType(tab.type).length === 0">
            <image class="empty-image" :src="tab.emptyImage || '/static/images/empty-reminders.png'"></image>
            <text class="empty-text">{{ tab.emptyText || '暂无相关提醒' }}</text>
            <view class="action-btn" @click="navigateTo('/subPackages/activity-showcase/pages/list/index')" :style="{
              background: 'linear-gradient(135deg, #34C759 0%, #7ED321 100%)',
              borderRadius: '35px',
              boxShadow: '0 5px 15px rgba(52,199,89,0.3)'
            }">
              <text>去参与活动</text>
            </view>
          </view>
        </scroll-view>
      </swiper-item>
    </swiper>

    <!-- 设置弹窗 -->
    <uni-popup ref="settingsPopup" type="bottom">
      <view class="settings-popup">
        <view class="settings-header">
          <text class="settings-title">提醒设置</text>
          <view class="settings-close" @click="closeSettings">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <line x1="18" y1="6" x2="6" y2="18" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
              <line x1="6" y1="6" x2="18" y2="18" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
            </svg>
          </view>
        </view>
        
        <view class="settings-content">
          <view class="settings-section">
            <text class="section-title">通知设置</text>
            <view class="settings-item">
              <text class="item-label">活动开始提醒</text>
              <switch :checked="settings.activityStart" @change="toggleSetting('activityStart')" color="#34C759" />
            </view>
            <view class="settings-item">
              <text class="item-label">活动变更提醒</text>
              <switch :checked="settings.activityChange" @change="toggleSetting('activityChange')" color="#34C759" />
            </view>
            <view class="settings-item">
              <text class="item-label">报名成功提醒</text>
              <switch :checked="settings.registrationSuccess" @change="toggleSetting('registrationSuccess')" color="#34C759" />
            </view>
            <view class="settings-item">
              <text class="item-label">活动取消提醒</text>
              <switch :checked="settings.activityCancel" @change="toggleSetting('activityCancel')" color="#34C759" />
            </view>
          </view>
          
          <view class="settings-section">
            <text class="section-title">提醒时间</text>
            <view class="settings-item">
              <text class="item-label">提前提醒时间</text>
              <picker 
                mode="selector" 
                :range="reminderTimeOptions" 
                :value="reminderTimeIndex" 
                @change="onReminderTimeChange"
              >
                <view class="picker-value">
                  <text>{{ reminderTimeOptions[reminderTimeIndex] }}</text>
                  <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
                    <path d="M6 9l6 6 6-6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                  </svg>
                </view>
              </picker>
            </view>
          </view>
          
          <view class="settings-section">
            <text class="section-title">清除记录</text>
            <view class="clear-options">
              <view class="clear-btn" @click="clearReadReminders">
                <text>清除已读提醒</text>
              </view>
              <view class="clear-btn danger" @click="clearAllReminders">
                <text>清除全部提醒</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 页面状态
const currentTab = ref(0);
const isRefreshing = ref(false);
const remindersList = ref([]);
const settingsPopup = ref(null);

// 设置选项
const settings = ref({
  activityStart: true,
  activityChange: true,
  registrationSuccess: true,
  activityCancel: true
});

const reminderTimeOptions = ['提前15分钟', '提前30分钟', '提前1小时', '提前2小时', '提前1天'];
const reminderTimeIndex = ref(2);

// 提醒标签页
const reminderTabs = [
  { name: '全部', type: 'all', emptyText: '暂无提醒', emptyImage: '/static/images/empty-reminders.png' },
  { name: '未读', type: 'unread', emptyText: '暂无未读提醒', emptyImage: '/static/images/empty-unread.png' },
  { name: '活动', type: 'activity', emptyText: '暂无活动提醒', emptyImage: '/static/images/empty-activity.png' },
  { name: '系统', type: 'system', emptyText: '暂无系统提醒', emptyImage: '/static/images/empty-system.png' }
];

// 模拟数据
const mockReminders = [
  {
    id: '1001',
    title: '活动即将开始',
    description: '您报名参加的"磁州文化节"将于明天开始，请准时参加。',
    category: 'activity_start',
    type: 'activity',
    time: '10分钟前',
    isRead: false,
    activityInfo: {
      id: '1001',
      name: '磁州文化节',
      date: '2024-06-15 09:00',
      location: '磁州文化广场',
      image: '/static/demo/activity1.jpg'
    }
  },
  {
    id: '1002',
    title: '报名成功通知',
    description: '恭喜您成功报名"亲子户外拓展活动"，请按时参加。',
    category: 'registration_success',
    type: 'activity',
    time: '2小时前',
    isRead: false,
    activityInfo: {
      id: '1002',
      name: '亲子户外拓展活动',
      date: '2024-05-28 14:00',
      location: '磁州森林公园',
      image: '/static/demo/activity2.jpg'
    }
  },
  {
    id: '1003',
    title: '活动地点变更',
    description: '"社区篮球赛"的活动地点已变更，请查看详情。',
    category: 'activity_change',
    type: 'activity',
    time: '昨天 15:30',
    isRead: true,
    activityInfo: {
      id: '1003',
      name: '社区篮球赛',
      date: '2024-05-20 10:00',
      location: '磁州体育中心(新)',
      image: '/static/demo/activity3.jpg'
    }
  },
  {
    id: '1004',
    title: '活动已取消',
    description: '很抱歉，"传统文化体验课"因故取消，报名费用将原路退回。',
    category: 'activity_cancel',
    type: 'activity',
    time: '昨天 10:15',
    isRead: true,
    activityInfo: {
      id: '1004',
      name: '传统文化体验课',
      date: '2024-05-15 15:00',
      location: '磁州文化馆',
      image: '/static/demo/activity4.jpg'
    }
  },
  {
    id: '1005',
    title: '系统通知',
    description: '您的账号已成功升级为VIP会员，可享受更多活动优惠。',
    category: 'system',
    type: 'system',
    time: '2天前',
    isRead: false
  }
];

// 生命周期
onMounted(() => {
  loadReminders();
});

// 方法
const loadReminders = () => {
  // 模拟加载数据
  remindersList.value = mockReminders;
};

const getRemindersByType = (type) => {
  if (type === 'all') {
    return remindersList.value;
  } else if (type === 'unread') {
    return remindersList.value.filter(reminder => !reminder.isRead);
  } else {
    return remindersList.value.filter(reminder => reminder.type === type);
  }
};

const switchTab = (index) => {
  currentTab.value = index;
};

const onSwiperChange = (e) => {
  currentTab.value = e.detail.current;
};

const onRefresh = () => {
  isRefreshing.value = true;
  setTimeout(() => {
    loadReminders();
    isRefreshing.value = false;
  }, 1000);
};

const loadMore = () => {
  // 模拟加载更多
  console.log('加载更多提醒');
};

const getReminderIconPath = (category) => {
  const iconMap = {
    'activity_start': 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z',
    'registration_success': 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
    'activity_change': 'M19 21v-2a4 4 0 00-4-4H9a4 4 0 00-4 4v2M12 11a4 4 0 100-8 4 4 0 000 8z',
    'activity_cancel': 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z',
    'system': 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
  };
  return iconMap[category] || iconMap['system'];
};

const getReminderIconBg = (category) => {
  const bgMap = {
    'activity_start': 'linear-gradient(135deg, #34C759 0%, #7ED321 100%)',
    'registration_success': 'linear-gradient(135deg, #5AC8FA 0%, #90E0FF 100%)',
    'activity_change': 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)',
    'activity_cancel': 'linear-gradient(135deg, #FF3B30 0%, #FF9580 100%)',
    'system': 'linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)'
  };
  return bgMap[category] || bgMap['system'];
};

const getPrimaryActionText = (category) => {
  const actionMap = {
    'activity_start': '查看详情',
    'registration_success': '查看订单',
    'activity_change': '查看变更',
    'activity_cancel': '查看退款',
    'system': '了解详情'
  };
  return actionMap[category] || '查看详情';
};

const getPrimaryActionBgColor = (category) => {
  const bgColorMap = {
    'activity_start': 'linear-gradient(135deg, #34C759 0%, #7ED321 100%)',
    'registration_success': 'linear-gradient(135deg, #5AC8FA 0%, #90E0FF 100%)',
    'activity_change': 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)',
    'activity_cancel': 'linear-gradient(135deg, #FF3B30 0%, #FF9580 100%)',
    'system': 'linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)'
  };
  return bgColorMap[category] || 'linear-gradient(135deg, #34C759 0%, #7ED321 100%)';
};

const handlePrimaryAction = (reminder) => {
  switch (reminder.category) {
    case 'activity_start':
      if (reminder.activityInfo) {
        navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${reminder.activityInfo.id}`);
      }
      break;
    case 'registration_success':
      navigateTo('/subPackages/activity-showcase/pages/orders/index?status=all');
      break;
    case 'activity_change':
      if (reminder.activityInfo) {
        navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${reminder.activityInfo.id}`);
      }
      break;
    case 'activity_cancel':
      navigateTo('/subPackages/activity-showcase/pages/orders/index?status=cancelled');
      break;
    default:
      viewReminderDetail(reminder);
  }
  
  // 标记为已读
  if (!reminder.isRead) {
    markAsRead(reminder);
  }
};

const viewReminderDetail = (reminder) => {
  // 标记为已读
  if (!reminder.isRead) {
    markAsRead(reminder);
  }
  
  // 查看详情
  if (reminder.activityInfo) {
    navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${reminder.activityInfo.id}`);
  } else {
    uni.showModal({
      title: reminder.title,
      content: reminder.description,
      showCancel: false
    });
  }
};

const markAsRead = (reminder) => {
  const index = remindersList.value.findIndex(item => item.id === reminder.id);
  if (index !== -1) {
    remindersList.value[index].isRead = true;
  }
};

const deleteReminder = (reminder) => {
  uni.showModal({
    title: '提示',
    content: '确认删除该提醒吗？',
    success: function(res) {
      if (res.confirm) {
        const index = remindersList.value.findIndex(item => item.id === reminder.id);
        if (index !== -1) {
          remindersList.value.splice(index, 1);
        }
        
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        });
      }
    }
  });
};

const showSettings = () => {
  settingsPopup.value.open();
};

const closeSettings = () => {
  settingsPopup.value.close();
};

const toggleSetting = (key) => {
  settings.value[key] = !settings.value[key];
};

const onReminderTimeChange = (e) => {
  reminderTimeIndex.value = e.detail.value;
};

const clearReadReminders = () => {
  uni.showModal({
    title: '提示',
    content: '确认清除所有已读提醒吗？',
    success: function(res) {
      if (res.confirm) {
        remindersList.value = remindersList.value.filter(item => !item.isRead);
        
        uni.showToast({
          title: '清除成功',
          icon: 'success'
        });
      }
    }
  });
};

const clearAllReminders = () => {
  uni.showModal({
    title: '提示',
    content: '确认清除所有提醒吗？',
    success: function(res) {
      if (res.confirm) {
        remindersList.value = [];
        
        uni.showToast({
          title: '清除成功',
          icon: 'success'
        });
      }
    }
  });
};

const goBack = () => {
  uni.navigateBack();
};

const navigateTo = (url) => {
  uni.navigateTo({ url });
};
</script>

<style scoped>
.reminders-container {
  min-height: 100vh;
  background-color: #F5F5F5;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
}

.navbar-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #34C759 0%, #7ED321 100%);
}

.navbar-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 112rpx; /* 原来是107rpx，再增加5rpx */
  padding: var(--status-bar-height) 30rpx 0;
}

.back-btn, .settings-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 36rpx;
  height: 36rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #FFFFFF;
}

.navbar-right {
  display: flex;
  align-items: center;
}

/* 标签栏样式 */
.reminder-tabs {
  display: flex;
  background: #FFFFFF;
  padding: 0 20rpx;
  margin-top: calc(var(--status-bar-height) + 112rpx); /* 原来是107rpx，再增加5rpx */
  border-bottom: 1rpx solid #EEEEEE;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  position: relative;
}

.tab-text {
  font-size: 28rpx;
  color: #333333;
  padding: 0 10rpx;
}

.tab-item.active .tab-text {
  color: #34C759;
  font-weight: 500;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  width: 40rpx;
  height: 6rpx;
  border-radius: 3rpx;
}

/* 提醒列表样式 */
.reminders-swiper {
  height: calc(100vh - var(--status-bar-height) - 112rpx - 70rpx); /* 原来是107rpx，再增加5rpx */
}

.tab-content {
  height: 100%;
  padding: 20rpx;
}

.reminders-list {
  padding-bottom: 30rpx;
}

.reminder-card {
  display: flex;
  background: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
  border-left: 6rpx solid #34C759;
}

.reminder-card.read {
  border-left: 6rpx solid #DDDDDD;
  opacity: 0.8;
}

.reminder-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 30rpx;
}

.reminder-content {
  flex: 1;
  padding: 20rpx 30rpx 20rpx 0;
}

.reminder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.reminder-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.reminder-time {
  font-size: 24rpx;
  color: #999999;
}

.reminder-desc {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.activity-info {
  display: flex;
  background: #F9F9F9;
  border-radius: 10rpx;
  padding: 15rpx;
  margin-bottom: 20rpx;
}

.activity-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 10rpx;
  margin-right: 15rpx;
}

.activity-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.activity-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 10rpx;
}

.activity-meta {
  display: flex;
  flex-direction: column;
}

.activity-date, .activity-location {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 5rpx;
}

.reminder-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.action-btn {
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  margin-left: 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
}

.action-btn.secondary {
  background: #F5F5F5;
  color: #666666;
  border: 1rpx solid #DDDDDD;
  box-shadow: none;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 30rpx;
}

.empty-state .action-btn {
  padding: 15rpx 60rpx;
  font-size: 28rpx;
  color: #FFFFFF;
}

/* 设置弹窗样式 */
.settings-popup {
  background: #FFFFFF;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
  padding: 30rpx;
  max-height: 70vh;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.settings-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.settings-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.settings-content {
  max-height: calc(70vh - 180rpx);
  overflow-y: auto;
}

.settings-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.settings-item:last-child {
  border-bottom: none;
}

.item-label {
  font-size: 28rpx;
  color: #333333;
}

.picker-value {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666666;
}

.clear-options {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
}

.clear-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  background: #F5F5F5;
  color: #666666;
  margin-right: 20rpx;
}

.clear-btn.danger {
  background: linear-gradient(135deg, #FF3B30 0%, #FF9580 100%);
  color: #FFFFFF;
  box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.2);
  margin-right: 0;
}
</style>