{"version": 3, "file": "ProductCard.js", "sources": ["subPackages/activity-showcase/components/mall/ProductCard.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7Avc3ViUGFja2FnZXMvYWN0aXZpdHktc2hvd2Nhc2UvY29tcG9uZW50cy9tYWxsL1Byb2R1Y3RDYXJkLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"product-card\" :style=\"cardStyle\" @click=\"$emit('click', product)\">\r\n    <!-- 商品图片 -->\r\n    <view class=\"product-image-container\">\r\n      <image class=\"product-image\" :src=\"product.coverImage\" mode=\"aspectFill\"></image>\r\n      <view class=\"product-tag\" v-if=\"product.tag\">{{ product.tag }}</view>\r\n    </view>\r\n    \r\n    <!-- 商品信息 -->\r\n    <view class=\"product-info\">\r\n      <view class=\"product-title\">{{ product.title }}</view>\r\n      \r\n      <view class=\"shop-info\" v-if=\"showShop\">\r\n        <image class=\"shop-logo\" :src=\"product.shopLogo\" mode=\"aspectFill\"></image>\r\n        <text class=\"shop-name\">{{ product.shopName }}</text>\r\n        <view class=\"distance\" v-if=\"product.distance\">{{ product.distance }}</view>\r\n      </view>\r\n      \r\n      <view class=\"product-bottom\">\r\n        <view class=\"price-container\">\r\n          <text class=\"price-symbol\">¥</text>\r\n          <text class=\"price-value\">{{ product.price }}</text>\r\n          <text class=\"price-original\" v-if=\"product.originalPrice\">¥{{ product.originalPrice }}</text>\r\n        </view>\r\n        \r\n        <view class=\"sold-count\" v-if=\"product.soldCount\">已售{{ product.soldCount }}+</view>\r\n      </view>\r\n      \r\n      <!-- 商品标签 -->\r\n      <view class=\"product-labels\" v-if=\"product.labels && product.labels.length > 0\">\r\n        <view \r\n          class=\"label-item\"\r\n          v-for=\"(label, index) in product.labels\"\r\n          :key=\"index\"\r\n          :style=\"{ backgroundColor: getLabelColor(label.type) }\"\r\n        >\r\n          {{ label.text }}\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 快捷操作按钮 -->\r\n    <view class=\"action-btn\" @click.stop=\"$emit('addToCart', product)\" v-if=\"showActionBtn\">\r\n      <svg class=\"action-icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n        <path d=\"M12 9v6m-3-3h6\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n      </svg>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\n// 组件属性定义\r\nconst props = defineProps({\r\n  product: {\r\n    type: Object,\r\n    required: true\r\n  },\r\n  showShop: {\r\n    type: Boolean,\r\n    default: true\r\n  },\r\n  showActionBtn: {\r\n    type: Boolean,\r\n    default: true\r\n  },\r\n  cardStyle: {\r\n    type: Object,\r\n    default: () => ({})\r\n  }\r\n});\r\n\r\n// 定义事件\r\ndefineEmits(['click', 'addToCart']);\r\n\r\n// 获取标签颜色\r\nconst getLabelColor = (type) => {\r\n  switch(type) {\r\n    case 'discount':\r\n      return 'rgba(255,59,105,0.1)';\r\n    case 'coupon':\r\n      return 'rgba(255,149,0,0.1)';\r\n    case 'new':\r\n      return 'rgba(52,199,89,0.1)';\r\n    case 'hot':\r\n      return 'rgba(255,59,48,0.1)';\r\n    default:\r\n      return 'rgba(142,142,147,0.1)';\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.product-card {\r\n  width: 100%;\r\n  background-color: #FFFFFF;\r\n  border-radius: 24rpx;\r\n  overflow: hidden;\r\n  position: relative;\r\n  box-shadow: 0 4px 12px rgba(0,0,0,0.05);\r\n  margin-bottom: 20rpx;\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n  \r\n  &:active {\r\n    transform: scale(0.98);\r\n    box-shadow: 0 2px 8px rgba(0,0,0,0.03);\r\n  }\r\n}\r\n\r\n.product-image-container {\r\n  width: 100%;\r\n  height: 300rpx;\r\n  position: relative;\r\n  \r\n  .product-image {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n  \r\n  .product-tag {\r\n    position: absolute;\r\n    top: 16rpx;\r\n    right: 16rpx;\r\n    padding: 6rpx 16rpx;\r\n    background-color: rgba(255,59,105,0.9);\r\n    color: #FFFFFF;\r\n    font-size: 22rpx;\r\n    border-radius: 16rpx;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n.product-info {\r\n  padding: 20rpx;\r\n  \r\n  .product-title {\r\n    font-size: 28rpx;\r\n    font-weight: 500;\r\n    color: #333333;\r\n    margin-bottom: 16rpx;\r\n    line-height: 1.4;\r\n    height: 80rpx;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    display: -webkit-box;\r\n    -webkit-line-clamp: 2;\r\n    -webkit-box-orient: vertical;\r\n  }\r\n  \r\n  .shop-info {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 16rpx;\r\n    \r\n    .shop-logo {\r\n      width: 32rpx;\r\n      height: 32rpx;\r\n      border-radius: 16rpx;\r\n      margin-right: 8rpx;\r\n    }\r\n    \r\n    .shop-name {\r\n      font-size: 24rpx;\r\n      color: #8E8E93;\r\n      flex: 1;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      white-space: nowrap;\r\n    }\r\n    \r\n    .distance {\r\n      font-size: 24rpx;\r\n      color: #8E8E93;\r\n    }\r\n  }\r\n  \r\n  .product-bottom {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    \r\n    .price-container {\r\n      display: flex;\r\n      align-items: baseline;\r\n      \r\n      .price-symbol {\r\n        font-size: 24rpx;\r\n        color: #FF3B69;\r\n        margin-right: 4rpx;\r\n      }\r\n      \r\n      .price-value {\r\n        font-size: 32rpx;\r\n        font-weight: 600;\r\n        color: #FF3B69;\r\n      }\r\n      \r\n      .price-original {\r\n        font-size: 24rpx;\r\n        color: #8E8E93;\r\n        margin-left: 12rpx;\r\n        text-decoration: line-through;\r\n      }\r\n    }\r\n    \r\n    .sold-count {\r\n      font-size: 24rpx;\r\n      color: #8E8E93;\r\n    }\r\n  }\r\n  \r\n  .product-labels {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    margin-top: 16rpx;\r\n    \r\n    .label-item {\r\n      padding: 4rpx 12rpx;\r\n      border-radius: 6rpx;\r\n      font-size: 20rpx;\r\n      margin-right: 12rpx;\r\n      margin-bottom: 8rpx;\r\n      color: #FF3B69;\r\n    }\r\n  }\r\n}\r\n\r\n.action-btn {\r\n  position: absolute;\r\n  bottom: 20rpx;\r\n  right: 20rpx;\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  border-radius: 30rpx;\r\n  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 4px 8px rgba(255,59,105,0.3);\r\n  \r\n  .action-icon {\r\n    width: 32rpx;\r\n    height: 32rpx;\r\n    color: #FFFFFF;\r\n  }\r\n  \r\n  &:active {\r\n    transform: scale(0.9);\r\n  }\r\n}\r\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/components/mall/ProductCard.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2EA,UAAM,gBAAgB,CAAC,SAAS;AAC9B,cAAO,MAAI;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvFA,GAAG,gBAAgB,SAAS;"}