"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_rect = common_vendor.resolveComponent("rect");
  const _component_polygon = common_vendor.resolveComponent("polygon");
  const _component_circle = common_vendor.resolveComponent("circle");
  (_component_path + _component_polyline + _component_line + _component_svg + _component_rect + _component_polygon + _component_circle)();
}
const _sfc_main = {
  __name: "MarketingPromotionActions",
  props: {
    // 活动ID
    activityId: {
      type: String,
      default: ""
    },
    // 活动类型：coupon, discount, flash, group
    activityType: {
      type: String,
      default: "coupon",
      validator: (value) => {
        return ["coupon", "discount", "flash", "group"].includes(value);
      }
    },
    // 控制显示哪些功能
    showActions: {
      type: Array,
      default: () => ["publish", "top", "refresh"],
      validator: (value) => {
        return value.every((item) => ["publish", "top", "refresh"].includes(item));
      }
    },
    // 是否只显示发布模式（新的UI样式）
    publishModeOnly: {
      type: Boolean,
      default: false
    },
    // 显示模式：standard(标准模式) 或 direct(直接显示选项)
    showMode: {
      type: String,
      default: "direct",
      validator: (value) => {
        return ["standard", "direct"].includes(value);
      }
    },
    // 刷新次数
    refreshCount: {
      type: Number,
      default: 0
    }
  },
  emits: ["action-completed"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    common_vendor.ref(false);
    const showPaymentModal = common_vendor.ref(false);
    const paymentModalTitle = common_vendor.ref("");
    const paymentModalDesc = common_vendor.ref("");
    const paymentModalPrice = common_vendor.ref("");
    const paymentMethod = common_vendor.ref("wxpay");
    const currentAction = common_vendor.ref("");
    const currentActionType = common_vendor.ref("");
    const showDurationSelect = common_vendor.ref(false);
    const selectedDuration = common_vendor.ref("3d");
    const showRefreshOptions = common_vendor.ref(false);
    const showRefreshPaymentModal = common_vendor.ref(false);
    const selectedOption = common_vendor.ref(null);
    const selectedPrice = common_vendor.ref(null);
    const remainingRefreshCount = common_vendor.ref(props.refreshCount);
    const refreshPrice = common_vendor.ref("2");
    const refreshPackages = [
      { count: 10, price: "15", label: "10次刷新套餐" },
      { count: 30, price: "39", label: "30次刷新套餐" },
      { count: 100, price: "99", label: "100次刷新套餐" }
    ];
    const showPublish = common_vendor.computed(() => {
      return props.showActions.includes("publish");
    });
    const showTop = common_vendor.computed(() => {
      return !props.publishModeOnly && props.showActions.includes("top");
    });
    const showRefresh = common_vendor.computed(() => {
      return !props.publishModeOnly && props.showActions.includes("refresh");
    });
    const isCarpool = common_vendor.computed(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const route = currentPage ? currentPage.route || "" : "";
      return route.includes("carpool");
    });
    common_vendor.reactive({
      title: "",
      description: "",
      options: []
    });
    const selectDirectOption = (action, optionType) => {
      currentAction.value = action;
      if (optionType === "paid") {
        if (isCarpool.value && action === "publish") {
          const option2 = {
            title: "付费发布",
            subtitle: "付费1元发布一条信息",
            price: "¥1.00",
            icon: "/static/images/premium/paid-publish.png",
            type: "paid",
            duration: "一条信息",
            amount: "1"
          };
          selectedOption.value = option2;
          processPaidAction();
          return;
        }
        showDurationSelect.value = true;
        return;
      }
      let option = null;
      if (action === "publish") {
        if (optionType === "ad") {
          if (isCarpool.value) {
            option = {
              title: "免费发布",
              subtitle: "观看15秒广告发布一条信息",
              price: "免费",
              icon: "/static/images/premium/ad-publish.png",
              type: "ad",
              duration: "一条信息"
            };
          } else {
            option = {
              title: "免费发布",
              subtitle: "观看15秒广告后发布",
              price: "免费",
              icon: "/static/images/premium/ad-publish.png",
              type: "ad",
              duration: "1天"
            };
          }
        }
      } else if (action === "top") {
        if (optionType === "ad") {
          option = {
            title: "广告置顶",
            subtitle: "观看30秒广告获得2小时置顶",
            price: "免费",
            icon: "/static/images/premium/ad-top.png",
            type: "ad",
            duration: "2小时"
          };
        }
      } else if (action === "refresh") {
        if (optionType === "ad") {
          option = {
            title: "广告刷新",
            subtitle: "观看15秒广告刷新一次",
            price: "免费",
            icon: "/static/images/premium/ad-refresh.png",
            type: "ad"
          };
        }
      }
      if (option) {
        selectedOption.value = option;
        if (optionType === "ad") {
          showAd();
        }
      }
    };
    const selectDuration = (duration, price) => {
      selectedDuration.value = duration;
      selectedPrice.value = price;
      let durationText = "";
      if (duration === "3d") {
        durationText = "3天";
        price = "2.8";
      } else if (duration === "1w") {
        durationText = "1周";
        price = "5.8";
      } else if (duration === "1m") {
        durationText = "1个月";
        price = "13.8";
      }
      const option = {
        title: currentAction.value === "publish" ? "付费发布" : currentAction.value === "top" ? "付费置顶" : "付费刷新",
        subtitle: `${currentAction.value === "publish" ? "付费发布" : currentAction.value === "top" ? "付费置顶" : "付费刷新"}${durationText}`,
        price: `¥${price}`,
        icon: currentAction.value === "publish" ? "/static/images/premium/paid-publish.png" : currentAction.value === "top" ? "/static/images/premium/paid-top.png" : "/static/images/premium/paid-refresh.png",
        type: "paid",
        duration: durationText,
        amount: price
      };
      selectedOption.value = option;
      processPaidAction();
      showPaymentDialog("付费发布", `发布${durationText}`, `¥${price}`);
      showDurationSelect.value = false;
    };
    const showPaymentDialog = (title, desc, price) => {
      paymentModalTitle.value = title;
      paymentModalDesc.value = desc;
      paymentModalPrice.value = price;
      showPaymentModal.value = true;
    };
    const closePaymentModal = () => {
      showPaymentModal.value = false;
    };
    const confirmPayment = () => {
      common_vendor.index.showLoading({
        title: "处理支付中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        closePaymentModal();
        switch (currentAction.value) {
          case "publish":
            processPublish();
            break;
          case "top":
            processTop();
            break;
          case "refresh":
            processRefresh();
            break;
        }
      }, 1500);
    };
    const showAd = () => {
      common_vendor.index.showLoading({
        title: "正在加载广告..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showModal({
          title: "广告观看完成",
          content: "您已成功观看广告，即将完成操作",
          showCancel: false,
          success: () => {
            processActionCompletion();
          }
        });
      }, 1500);
    };
    const processPaidAction = () => {
      showDurationSelect.value = false;
      common_vendor.index.showLoading({
        title: "正在处理支付..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showModal({
          title: "支付成功",
          content: "您已成功支付，即将完成操作",
          showCancel: false,
          success: () => {
            processActionCompletion();
          }
        });
      }, 1500);
    };
    const processPublish = () => {
      common_vendor.index.showLoading({
        title: "发布中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: currentActionType.value === "normal" ? "发布成功" : "精选发布成功",
          icon: "success"
        });
        emit("action-completed", {
          action: "publish",
          type: currentActionType.value,
          activityId: props.activityId,
          activityType: props.activityType
        });
      }, 1e3);
    };
    const processTop = () => {
      common_vendor.index.showLoading({
        title: "置顶中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: currentActionType.value === "normal" ? "普通置顶成功" : "高级置顶成功",
          icon: "success"
        });
        emit("action-completed", {
          action: "top",
          type: currentActionType.value,
          activityId: props.activityId,
          activityType: props.activityType
        });
      }, 1e3);
    };
    const processRefresh = () => {
      common_vendor.index.showLoading({
        title: "刷新中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: currentActionType.value === "single" ? "刷新成功" : "刷新套餐购买成功",
          icon: "success"
        });
        emit("action-completed", {
          action: "refresh",
          type: currentActionType.value,
          activityId: props.activityId,
          activityType: props.activityType
        });
      }, 1e3);
    };
    const processActionCompletion = () => {
      const actionType = currentAction.value;
      const optionType = selectedOption.value.type;
      let eventType = "";
      if (optionType === "ad") {
        eventType = "adPublish";
      } else if (optionType === "paid") {
        eventType = "paidPublish";
      } else if (optionType === "count") {
        eventType = "countRefresh";
      }
      emit("action-completed", eventType, {
        action: actionType,
        type: optionType,
        activityId: props.activityId,
        activityType: props.activityType,
        option: selectedOption.value,
        amount: selectedOption.value.amount || "0",
        remainingCount: optionType === "count" ? selectedOption.value.remainingCount : void 0
      });
      let successMessage = "";
      switch (actionType) {
        case "publish":
          successMessage = "发布成功！";
          break;
        case "top":
          successMessage = `置顶成功！有效期${selectedOption.value.duration || ""}`;
          break;
        case "refresh":
          if (optionType === "count") {
            successMessage = `刷新成功！剩余${selectedOption.value.remainingCount}次`;
          } else {
            successMessage = "刷新成功！";
          }
          break;
      }
      common_vendor.index.showToast({
        title: successMessage,
        icon: "success"
      });
    };
    const handleRefreshClick = () => {
      if (remainingRefreshCount.value > 0) {
        showRefreshOptions.value = true;
      } else {
        showRefreshPaymentModal.value = true;
      }
    };
    const closeRefreshOptions = () => {
      showRefreshOptions.value = false;
    };
    const closeRefreshPaymentModal = () => {
      showRefreshPaymentModal.value = false;
    };
    const buyRefreshPackage = () => {
      closeRefreshPaymentModal();
      showRefreshOptions.value = true;
    };
    const confirmPaidRefresh = () => {
      closeRefreshPaymentModal();
      common_vendor.index.showLoading({
        title: "正在处理支付..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showModal({
          title: "支付成功",
          content: "您已成功支付，即将刷新信息",
          showCancel: false,
          success: () => {
            const option = {
              title: "付费刷新",
              subtitle: "立即刷新信息至列表顶部",
              price: `¥${refreshPrice.value}`,
              icon: "/static/images/premium/paid-refresh.png",
              type: "paid",
              amount: refreshPrice.value
            };
            selectedOption.value = option;
            currentAction.value = "refresh";
            processActionCompletion();
          }
        });
      }, 1500);
    };
    const selectRefreshPackage = (pkg) => {
      common_vendor.index.showLoading({
        title: "正在处理支付..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showModal({
          title: "购买成功",
          content: `您已成功购买${pkg.count}次刷新次数`,
          showCancel: false,
          success: () => {
            remainingRefreshCount.value += pkg.count;
            closeRefreshOptions();
            emit("action-completed", "refreshPackage", {
              count: pkg.count,
              price: pkg.price,
              totalCount: remainingRefreshCount.value
            });
          }
        });
      }, 1500);
    };
    const useRefreshCount = () => {
      if (remainingRefreshCount.value <= 0) {
        common_vendor.index.showToast({
          title: "您没有可用的刷新次数",
          icon: "none"
        });
        return;
      }
      remainingRefreshCount.value--;
      closeRefreshOptions();
      common_vendor.index.showLoading({
        title: "正在刷新..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        const option = {
          title: "使用刷新次数",
          subtitle: "使用刷新次数刷新信息",
          price: "免费",
          icon: "/static/images/premium/paid-refresh.png",
          type: "count",
          remainingCount: remainingRefreshCount.value
        };
        selectedOption.value = option;
        currentAction.value = "refresh";
        processActionCompletion();
        emit("action-completed", "useRefreshCount", {
          remainingCount: remainingRefreshCount.value
        });
      }, 1e3);
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: showPublish.value
      }, showPublish.value ? common_vendor.e({
        b: common_vendor.t(__props.publishModeOnly ? "选择发布方式" : "活动发布"),
        c: common_vendor.t(__props.publishModeOnly ? "" : "发布活动到客户端，提升曝光率"),
        d: __props.publishModeOnly
      }, __props.publishModeOnly ? common_vendor.e({
        e: common_vendor.p({
          d: "M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"
        }),
        f: common_vendor.p({
          points: "7.5 4.21 12 6.81 16.5 4.21"
        }),
        g: common_vendor.p({
          points: "7.5 19.79 7.5 14.6 3 12"
        }),
        h: common_vendor.p({
          points: "21 12 16.5 14.6 16.5 19.79"
        }),
        i: common_vendor.p({
          points: "3.27 6.96 12 12.01 20.73 6.96"
        }),
        j: common_vendor.p({
          x1: "12",
          y1: "22.08",
          x2: "12",
          y2: "12"
        }),
        k: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          viewBox: "0 0 24 24",
          width: "24",
          height: "24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2"
        }),
        l: common_vendor.o(($event) => selectDirectOption("publish", "ad")),
        m: common_vendor.p({
          x: "3",
          y: "3",
          width: "18",
          height: "18",
          rx: "2",
          ry: "2"
        }),
        n: common_vendor.p({
          d: "M12 8v8M8 12h8"
        }),
        o: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          viewBox: "0 0 24 24",
          width: "24",
          height: "24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2"
        }),
        p: common_vendor.o(($event) => selectDirectOption("publish", "paid")),
        q: showDurationSelect.value
      }, showDurationSelect.value ? {
        r: selectedDuration.value === "3d" ? 1 : "",
        s: common_vendor.o(($event) => selectDuration("3d")),
        t: selectedDuration.value === "1w" ? 1 : "",
        v: common_vendor.o(($event) => selectDuration("1w")),
        w: selectedDuration.value === "1m" ? 1 : "",
        x: common_vendor.o(($event) => selectDuration("1m"))
      } : {}) : {
        y: common_vendor.p({
          d: "M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"
        }),
        z: common_vendor.p({
          points: "7.5 4.21 12 6.81 16.5 4.21"
        }),
        A: common_vendor.p({
          points: "7.5 19.79 7.5 14.6 3 12"
        }),
        B: common_vendor.p({
          points: "21 12 16.5 14.6 16.5 19.79"
        }),
        C: common_vendor.p({
          points: "3.27 6.96 12 12.01 20.73 6.96"
        }),
        D: common_vendor.p({
          x1: "12",
          y1: "22.08",
          x2: "12",
          y2: "12"
        }),
        E: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          viewBox: "0 0 24 24",
          width: "24",
          height: "24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2"
        }),
        F: common_vendor.o(($event) => selectDirectOption("publish", "ad")),
        G: common_vendor.p({
          points: "12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"
        }),
        H: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          viewBox: "0 0 24 24",
          width: "24",
          height: "24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2"
        }),
        I: common_vendor.o(($event) => selectDirectOption("publish", "featured"))
      }) : {}, {
        J: showTop.value
      }, showTop.value ? {
        K: common_vendor.p({
          d: "M12 19V5M5 12l7-7 7 7"
        }),
        L: common_vendor.p({
          x: "3",
          y: "3",
          width: "18",
          height: "4",
          rx: "1"
        }),
        M: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          viewBox: "0 0 24 24",
          width: "24",
          height: "24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2"
        }),
        N: common_vendor.o(($event) => selectDirectOption("top", "ad")),
        O: common_vendor.p({
          d: "M12 19V5M5 12l7-7 7 7"
        }),
        P: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          viewBox: "0 0 24 24",
          width: "24",
          height: "24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2"
        }),
        Q: common_vendor.o(($event) => selectDirectOption("top", "normal")),
        R: common_vendor.p({
          d: "M12 19V5M5 12l7-7 7 7"
        }),
        S: common_vendor.p({
          cx: "12",
          cy: "5",
          r: "2"
        }),
        T: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          viewBox: "0 0 24 24",
          width: "24",
          height: "24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2"
        }),
        U: common_vendor.o(($event) => selectDirectOption("top", "premium"))
      } : {}, {
        V: showRefresh.value
      }, showRefresh.value ? common_vendor.e({
        W: common_vendor.p({
          d: "M23 4v6h-6M1 20v-6h6"
        }),
        X: common_vendor.p({
          d: "M3.51 9a9 9 0 0114.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0020.49 15"
        }),
        Y: common_vendor.p({
          x: "10",
          y: "1",
          width: "4",
          height: "4",
          rx: "1"
        }),
        Z: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          viewBox: "0 0 24 24",
          width: "24",
          height: "24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2"
        }),
        aa: common_vendor.o(($event) => selectDirectOption("refresh", "ad")),
        ab: common_vendor.p({
          d: "M23 4v6h-6M1 20v-6h6"
        }),
        ac: common_vendor.p({
          d: "M3.51 9a9 9 0 0114.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0020.49 15"
        }),
        ad: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          viewBox: "0 0 24 24",
          width: "24",
          height: "24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2"
        }),
        ae: remainingRefreshCount.value > 0
      }, remainingRefreshCount.value > 0 ? {
        af: common_vendor.t(remainingRefreshCount.value)
      } : {}, {
        ag: common_vendor.o(handleRefreshClick),
        ah: common_vendor.p({
          x: "3",
          y: "3",
          width: "18",
          height: "18",
          rx: "2",
          ry: "2"
        }),
        ai: common_vendor.p({
          d: "M3 9h18M9 21V9"
        }),
        aj: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          viewBox: "0 0 24 24",
          width: "24",
          height: "24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2"
        }),
        ak: common_vendor.o(($event) => selectDirectOption("refresh", "package"))
      }) : {}, {
        al: showPaymentModal.value
      }, showPaymentModal.value ? common_vendor.e({
        am: common_vendor.o(closePaymentModal),
        an: common_vendor.t(paymentModalTitle.value),
        ao: common_vendor.p({
          x1: "18",
          y1: "6",
          x2: "6",
          y2: "18"
        }),
        ap: common_vendor.p({
          x1: "6",
          y1: "6",
          x2: "18",
          y2: "18"
        }),
        aq: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          viewBox: "0 0 24 24",
          width: "20",
          height: "20",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2"
        }),
        ar: common_vendor.o(closePaymentModal),
        as: common_vendor.t(paymentModalDesc.value),
        at: common_vendor.t(paymentModalPrice.value),
        av: common_vendor.p({
          d: "M9 8.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3z"
        }),
        aw: common_vendor.p({
          d: "M15 8.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3z"
        }),
        ax: common_vendor.p({
          d: "M9 15.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3z"
        }),
        ay: common_vendor.p({
          d: "M15 15.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3z"
        }),
        az: common_vendor.p({
          x: "3",
          y: "3",
          width: "18",
          height: "18",
          rx: "2"
        }),
        aA: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          viewBox: "0 0 24 24",
          width: "24",
          height: "24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2"
        }),
        aB: paymentMethod.value === "wxpay"
      }, paymentMethod.value === "wxpay" ? {} : {}, {
        aC: paymentMethod.value === "wxpay" ? 1 : "",
        aD: common_vendor.o(($event) => paymentMethod.value = "wxpay"),
        aE: common_vendor.p({
          d: "M22 9.3V5H2v4.3L12 15l10-5.7z"
        }),
        aF: common_vendor.p({
          d: "M2 9.3V19h20V9.3"
        }),
        aG: common_vendor.p({
          d: "M12 15v4"
        }),
        aH: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          viewBox: "0 0 24 24",
          width: "24",
          height: "24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2"
        }),
        aI: paymentMethod.value === "alipay"
      }, paymentMethod.value === "alipay" ? {} : {}, {
        aJ: paymentMethod.value === "alipay" ? 1 : "",
        aK: common_vendor.o(($event) => paymentMethod.value = "alipay"),
        aL: common_vendor.o(closePaymentModal),
        aM: common_vendor.o(confirmPayment)
      }) : {}, {
        aN: showRefreshPaymentModal.value
      }, showRefreshPaymentModal.value ? {
        aO: common_vendor.o(closeRefreshPaymentModal),
        aP: common_vendor.p({
          fill: "currentColor",
          d: "M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"
        }),
        aQ: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        aR: common_vendor.t(refreshPrice.value),
        aS: common_vendor.o(buyRefreshPackage),
        aT: common_vendor.o(confirmPaidRefresh)
      } : {}, {
        aU: showRefreshOptions.value
      }, showRefreshOptions.value ? common_vendor.e({
        aV: common_vendor.o(closeRefreshOptions),
        aW: common_vendor.p({
          fill: "#0066FF",
          d: "M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"
        }),
        aX: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "20",
          height: "20"
        }),
        aY: common_vendor.t(remainingRefreshCount.value),
        aZ: common_vendor.f(refreshPackages, (pkg, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(pkg.count),
            b: common_vendor.t(pkg.price),
            c: pkg.count === 10
          }, pkg.count === 10 ? {} : {}, {
            d: pkg.count === 30
          }, pkg.count === 30 ? {} : {}, {
            e: pkg.count === 100
          }, pkg.count === 100 ? {} : {}, {
            f: index,
            g: common_vendor.o(($event) => selectRefreshPackage(pkg), index)
          });
        }),
        ba: remainingRefreshCount.value > 0
      }, remainingRefreshCount.value > 0 ? {
        bb: common_vendor.o(useRefreshCount)
      } : {}, {
        bc: common_vendor.o(closeRefreshOptions)
      }) : {});
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-460de224"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/components/MarketingPromotionActions.js.map
