{"version": 3, "file": "publish.js", "sources": ["pages/my/publish.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbXkvcHVibGlzaC52dWU"], "sourcesContent": ["<template>\n  <view class=\"publish-container\">\n    <!-- 顶部导航栏（同步发布信息页面风格） -->\n    <view class=\"custom-navbar\" :style=\"`padding-top: ${statusBarHeight}px; height: ${statusBarHeight + 88}rpx;`\">\n      <view class=\"navbar-left\" @click=\"goBack\">\n        <image class=\"back-icon\" src=\"/static/images/tabbar/最新返回键.png\"></image>\n      </view>\n      <view class=\"navbar-title\">我的发布</view>\n      <view class=\"navbar-right\">\n        <!-- 右侧按钮可自定义 -->\n      </view>\n    </view>\n    \n    <!-- 状态标签栏 -->\n    <view class=\"tab-container\">\n      <view \n        v-for=\"(tab, index) in tabs\" \n        :key=\"index\" \n        class=\"tab-item\" \n        :class=\"{ active: currentTab === index }\"\n        @click=\"switchTab(index)\"\n      >\n        <text class=\"tab-text\">{{ tab }}</text>\n        <view v-if=\"currentTab === index\" class=\"active-line\"></view>\n      </view>\n    </view>\n    \n    <!-- 信息列表 -->\n    <view class=\"list-container\">\n      <view \n        class=\"info-card\" \n        v-for=\"(item, index) in filteredList\" \n        :key=\"index\"\n        @click=\"goToDetail(item)\"\n      >\n        <!-- 信息标题和状态 -->\n        <view class=\"info-header\">\n          <text class=\"info-title\">{{ item.title }}</text>\n          <text class=\"info-status\" :class=\"getStatusClass(item.status)\">{{ item.statusText }}</text>\n        </view>\n        <!-- 信息内容 -->\n        <view class=\"info-content\">{{ item.content }}</view>\n        <!-- 信息时间和统计 -->\n        <view class=\"info-stats\">\n          <text class=\"info-date\">{{ item.date }}</text>\n          <text class=\"info-views\">浏览 {{ item.views }}</text>\n          <text class=\"info-likes\">点赞 {{ item.likes }}</text>\n        </view>\n        <!-- 操作按钮，阻止冒泡 -->\n        <view class=\"info-actions\" @click.stop>\n          <button class=\"activity-btn\" data-type=\"edit\" @click=\"handleEdit(item)\">编辑</button>\n          <button class=\"activity-btn\" data-type=\"promote\" @click=\"promotePublish(item)\">推广</button>\n          <button class=\"activity-btn\" data-type=\"share\" open-type=\"share\" :data-item=\"JSON.stringify(item)\">转发</button>\n        </view>\n      </view>\n      \n      <!-- 无数据提示 -->\n      <view class=\"empty-container\" v-if=\"filteredList.length === 0\">\n        <image class=\"empty-icon\" src=\"/static/images/empty-data.png\"></image>\n        <text class=\"empty-text\">暂无数据</text>\n      </view>\n    </view>\n    \n    <!-- 置顶卡片弹窗 -->\n    <view class=\"top-overlay\" v-if=\"topCardVisible\" @click=\"hideTopCard\">\n      <view class=\"top-card\" @click.stop>\n        <view class=\"top-title\">{{ promotionTypeText }}，提升10倍曝光率</view>\n        <view class=\"top-block ad-block\" @click=\"showAdTips\">\n          <view class=\"block-left\">\n            <view class=\"block-main\">看广告{{ promotionTypeText }}1天</view>\n            <view class=\"block-sub\">免费特权</view>\n          </view>\n          <view class=\"block-right\">\n            <view class=\"free-btn\">免费</view>\n            <view class=\"arrow\">&gt;</view>\n          </view>\n        </view>\n        <view class=\"top-block pay-block\" @click=\"showTopOptions\">\n          <view class=\"block-left\">\n            <view class=\"block-main\">选择{{ promotionTypeText }}时长</view>\n            <view class=\"block-sub\">3天/1周/1月任选</view>\n          </view>\n          <view class=\"block-right\">\n            <view class=\"pay-btn\">付费</view>\n            <view class=\"arrow\">&gt;</view>\n          </view>\n        </view>\n        \n        <!-- 置顶时长选择 -->\n        <view class=\"top-options-dropdown\" v-if=\"topOptionsVisible\">\n          <view class=\"top-options-title\">选择{{ promotionTypeText }}时长</view>\n          <view class=\"top-options-list\">\n            <view \n              class=\"top-option-item\" \n              :class=\"{'active': selectedOption === 0}\" \n              @click=\"selectOption(0)\"\n            >\n              <view class=\"top-option-days\">3天</view>\n              <view class=\"top-option-price\">¥2.8</view>\n            </view>\n            <view \n              class=\"top-option-item\" \n              :class=\"{'active': selectedOption === 1}\" \n              @click=\"selectOption(1)\"\n            >\n              <view class=\"top-option-days\">1周</view>\n              <view class=\"top-option-price\">¥5.8</view>\n            </view>\n            <view \n              class=\"top-option-item\" \n              :class=\"{'active': selectedOption === 2}\" \n              @click=\"selectOption(2)\"\n            >\n              <view class=\"top-option-days\">1个月</view>\n              <view class=\"top-option-price\">¥19.8</view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 广告提示弹窗 -->\n    <view class=\"ad-tips-overlay\" v-if=\"adTipsVisible\" @click=\"hideAdTips\">\n      <view class=\"ad-tips-card\" @click.stop>\n        <view class=\"ad-tips-title\">广告须知</view>\n        <view class=\"ad-tips-content\">\n          <view class=\"ad-tips-item\">1. 完整观看广告可免费获得1天置顶</view>\n          <view class=\"ad-tips-item\">2. 广告必须观看<text class=\"highlight\">10秒以上</text>才能生效</view>\n          <view class=\"ad-tips-item\">3. 提前关闭广告将无法获得置顶特权</view>\n        </view>\n        <view class=\"ad-tips-btns\">\n          <view class=\"ad-tips-btn cancel\" @click=\"hideAdTips\">取消</view>\n          <view class=\"ad-tips-btn confirm\" @click=\"startWatchAd\">观看广告</view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 广告播放弹窗 -->\n    <view class=\"ad-overlay\" v-if=\"adWatchingVisible\">\n      <view class=\"ad-container\">\n        <view class=\"ad-header\">\n          <text class=\"ad-title\">广告</text>\n          <text class=\"ad-countdown\">{{ adCountdown }}秒</text>\n        </view>\n        <view class=\"ad-content\">\n          <image class=\"ad-image\" src=\"/static/images/banner/banner-2.png\" mode=\"aspectFill\"></image>\n          <view class=\"ad-message\">广告内容展示区域</view>\n        </view>\n        <view class=\"ad-footer\">\n          <view class=\"ad-tips\">观看完整广告获得1天置顶特权</view>\n          <view \n            class=\"ad-close-btn\" \n            :class=\"{'ad-close-active': adCountdown <= adMinTime}\" \n            @click=\"closeAd\"\n          >\n            {{ adCountdown <= adMinTime ? '关闭广告' : `请观看${adMinTime}秒 (${adCountdown})` }}\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 置顶结果提示 -->\n    <view class=\"top-result\" v-if=\"topResultVisible\">\n      <view class=\"top-result-content\">\n        <image class=\"top-result-icon\" :src=\"topSuccess ? '/static/images/pay/success.png' : '/static/images/pay/fail.png'\"></image>\n        <view class=\"top-result-text\">{{ topResultText }}</view>\n      </view>\n    </view>\n    \n    <!-- 自定义弹窗组件 -->\n    <view class=\"custom-modal\" v-if=\"customModal && customModal.show\">\n      <view class=\"modal-mask\" @click=\"closeCustomModal\"></view>\n      <view class=\"modal-content\">\n        <view class=\"modal-title\">{{customModal.title}}</view>\n        <view class=\"modal-body\">\n          <view class=\"modal-text\">刷新需要支付费用，刷新后{{customModal.type}}将获得更多曝光</view>\n          <view class=\"price-text\">本次刷新需支付<text class=\"price-amount\">2元</text></view>\n          <view class=\"rank-hint\">\n            <image class=\"rank-icon\" src=\"/static/images/tabbar/置顶.png\" mode=\"aspectFit\"></image>\n            <text>刷新后将在所有付费置顶中排名第一位</text>\n          </view>\n          <view class=\"special-hint\">购买刷新套餐更划算！</view>\n        </view>\n        <view class=\"modal-footer\">\n          <button class=\"modal-btn cancel-btn\" @click=\"handleCustomModalCancel\">{{customModal.cancelText || '购买套餐'}}</button>\n          <button class=\"modal-btn confirm-btn\" @click=\"handleCustomModalConfirm\">{{customModal.buttonText || '立即刷新'}}</button>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  enableShareAppMessage: true,\n  enableShareTimeline: true,\n  data() {\n    return {\n      statusBarHeight: 0,\n      tabs: ['全部', '审核中', '已发布', '已过期'],\n      currentTab: 0,\n      infoList: [\n        {\n          id: 1,\n          title: '招聘服务员',\n          content: '本店诚聘服务员2名，要求形象好，有经验者优先，薪资面议',\n          date: '2024-01-15',\n          views: 156,\n          likes: 23,\n          status: 'passed', // 审核通过\n          statusText: '审核通过'\n        },\n        {\n          id: 2,\n          title: '出售二手电动车',\n          content: '9成新电动车转让，行驶3000公里，价格实惠',\n          date: '2024-01-14',\n          views: 89,\n          likes: 12,\n          status: 'reviewing', // 审核中\n          statusText: '审核中'\n        },\n        {\n          id: 3,\n          title: '求租门面房',\n          content: '求租商业街店铺，面积50-100平米，价格合适即可',\n          date: '2024-01-13',\n          views: 245,\n          likes: 35,\n          status: 'passed', // 审核通过\n          statusText: '审核通过'\n        }\n      ],\n      topCardVisible: false,\n      currentItem: null,\n      topOptionsVisible: false,\n      selectedOption: 0,\n      topOptions: [\n        { days: 3, price: 2.8 },\n        { days: 7, price: 5.8 },\n        { days: 30, price: 19.8 }\n      ],\n      adTipsVisible: false,\n      adWatchingVisible: false,\n      adCountdown: 30,\n      adMinTime: 10,\n      topResultVisible: false,\n      topSuccess: false,\n      topResultText: '',\n      userRefreshCount: 2, // 用户剩余刷新次数\n      customModal: null, // 添加自定义弹窗数据\n      promotionType: 'top', // 'top', 'refresh', 'renew'\n      promotionTypeText: '置顶', // 对应的中文文本\n    };\n  },\n  \n  onLoad() {\n    const sysInfo = uni.getSystemInfoSync();\n    this.statusBarHeight = sysInfo.statusBarHeight;\n  },\n  \n  computed: {\n    filteredList() {\n      if (this.currentTab === 0) {\n        return this.infoList;\n      } else if (this.currentTab === 1) {\n        return this.infoList.filter(item => item.status === 'reviewing');\n      } else if (this.currentTab === 2) {\n        return this.infoList.filter(item => item.status === 'passed');\n      } else if (this.currentTab === 3) {\n        return this.infoList.filter(item => item.status === 'expired');\n      }\n      return [];\n    }\n  },\n  \n  methods: {\n    goBack() {\n      console.log('返回按钮被点击');\n      uni.navigateBack({\n        fail: function() {\n          uni.switchTab({\n            url: '/pages/index/index'\n          });\n        }\n      });\n    },\n    \n    switchTab(index) {\n      this.currentTab = index;\n    },\n    \n    getStatusClass(status) {\n      switch (status) {\n        case 'reviewing':\n          return 'status-reviewing';\n        case 'passed':\n          return 'status-passed';\n        case 'expired':\n          return 'status-expired';\n        default:\n          return '';\n      }\n    },\n    \n    handleTop(item) {\n      this.currentItem = item;\n      this.topCardVisible = true;\n    },\n    \n    hideTopCard() {\n      this.topCardVisible = false;\n      this.topOptionsVisible = false;\n    },\n    \n    showTopOptions() {\n      this.topOptionsVisible = true;\n    },\n    \n    hideTopOptions() {\n      this.topOptionsVisible = false;\n    },\n    \n    selectOption(index) {\n      this.selectedOption = index;\n      const option = this.topOptions[index];\n      const orderInfo = {\n        orderType: this.promotionType, \n        days: option.days,\n        amount: option.price,\n        infoId: this.currentItem.id,\n        userId: uni.getStorageSync('userId')\n      };\n      \n      uni.navigateTo({\n        url: `/pages/pay/index?orderInfo=${encodeURIComponent(JSON.stringify(orderInfo))}`\n      });\n      \n      this.hideTopCard();\n    },\n    \n    showAdTips() {\n      this.adTipsVisible = true;\n    },\n    \n    hideAdTips() {\n      this.adTipsVisible = false;\n    },\n    \n    startWatchAd() {\n      this.adTipsVisible = false;\n      this.adWatchingVisible = true;\n      this.adCountdown = 30;\n      \n      this.adTimer = setInterval(() => {\n        this.adCountdown--;\n        if (this.adCountdown <= 0) {\n          clearInterval(this.adTimer);\n          this.handleAdComplete(true);\n        }\n      }, 1000);\n    },\n    \n    closeAd() {\n      if (this.adCountdown <= 30 - this.adMinTime) {\n        clearInterval(this.adTimer);\n        this.adTimer = null;\n        this.handleAdComplete(true);\n      } else {\n        uni.showToast({\n          title: `请观看至少${this.adMinTime}秒广告`,\n          icon: 'none',\n          duration: 1500\n        });\n      }\n    },\n    \n    handleAdComplete(success) {\n      this.adWatchingVisible = false;\n      \n      if (success) {\n        try {\n          uni.showLoading({ title: '处理中...' });\n          \n          const infoId = this.currentItem.id;\n          \n          setTimeout(() => {\n            uni.hideLoading();\n            \n            this.topResultVisible = true;\n            this.topSuccess = true;\n            this.topResultText = `${this.promotionTypeText}成功！信息已${this.promotionTypeText}1天`;\n            \n            setTimeout(() => {\n              this.topResultVisible = false;\n              this.hideTopCard();\n            }, 2000);\n          }, 1000);\n          \n        } catch (error) {\n          uni.hideLoading();\n          console.error('免费操作请求失败', error);\n          uni.showToast({\n            title: '网络错误，请稍后再试',\n            icon: 'none'\n          });\n        }\n      } else {\n        this.topResultVisible = true;\n        this.topSuccess = false;\n        this.topResultText = `${this.promotionTypeText}失败！请观看至少10秒广告`;\n        setTimeout(() => {\n          this.topResultVisible = false;\n        }, 2000);\n      }\n    },\n    \n    handleRefresh(item) {\n      // 检查用户是否有剩余的刷新次数\n      if (this.userRefreshCount <= 0) {\n        // 没有剩余刷新次数，直接显示付费刷新选项\n        this.showDirectPayRefresh(item);\n        return;\n      }\n      \n      // 有剩余刷新次数，询问是否使用\n      uni.showModal({\n        title: '刷新发布',\n        content: `您当前有${this.userRefreshCount}次刷新机会，是否使用1次刷新该发布？`,\n        confirmText: '确认使用',\n        cancelText: '购买套餐',\n        success: (res) => {\n          if (res.confirm) {\n            // 使用一次刷新次数\n            this.userRefreshCount -= 1;\n            \n            uni.showLoading({ title: '刷新中...' });\n            \n            // 模拟刷新过程\n      setTimeout(() => {\n        uni.hideLoading();\n        uni.showToast({\n          title: '刷新成功',\n          icon: 'success'\n        });\n      }, 1000);\n          } else {\n            // 用户选择购买套餐\n            uni.navigateTo({\n              url: '/pages/services/refresh-package'\n            });\n          }\n        }\n      });\n    },\n    \n    // 修改showDirectPayRefresh方法\n    showDirectPayRefresh(item) {\n      this.customModal = {\n        show: true,\n        title: '付费刷新',\n        type: '发布',\n        cancelText: '购买套餐',\n        buttonText: '立即刷新',\n        item: item\n      };\n    },\n    \n    // 关闭自定义弹窗\n    closeCustomModal() {\n      this.customModal = null;\n    },\n    \n    // 处理自定义弹窗取消\n    handleCustomModalCancel() {\n      this.closeCustomModal();\n      uni.navigateTo({\n        url: '/pages/services/refresh-package'\n      });\n    },\n    \n    // 处理自定义弹窗确认\n    handleCustomModalConfirm() {\n      this.closeCustomModal();\n      \n      // 用户选择立即付费刷新\n      uni.showLoading({ title: '支付中...' });\n      \n      // 模拟支付流程\n      setTimeout(() => {\n        uni.hideLoading();\n        uni.showToast({\n          title: '刷新成功',\n          icon: 'success'\n        });\n      }, 1500);\n    },\n    \n    handleEdit(item) {\n      uni.showActionSheet({\n        itemList: ['修改内容', '删除内容', item.status === 'passed' ? '下架内容' : '上架内容'],\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0: // 修改内容\n              this.modifyPublish(item);\n              break;\n            case 1: // 删除内容\n              this.confirmDelete(item);\n              break;\n            case 2: // 上架/下架内容\n              this.togglePublishStatus(item);\n              break;\n          }\n        }\n      });\n    },\n    \n    modifyPublish(item) {\n      // 直接导航到详情页\n      uni.navigateTo({\n        url: `/pages/publish/detail?id=${item.id}&edit=1`\n      });\n    },\n    \n    togglePublishStatus(item) {\n      const action = item.status === 'passed' ? '下架' : '上架';\n      uni.showModal({\n        title: `${action}内容`,\n        content: `确定要${action}该发布内容吗？`,\n        success: (res) => {\n          if (res.confirm) {\n            uni.showLoading({ title: '处理中...' });\n            \n            // 模拟操作\n            setTimeout(() => {\n              // 更新状态\n              if (item.status === 'passed') {\n                item.status = 'expired';\n                item.statusText = '已下架';\n              } else {\n                item.status = 'passed';\n                item.statusText = '审核通过';\n              }\n              \n              uni.hideLoading();\n              uni.showToast({\n                title: `${action}成功`,\n                icon: 'success'\n              });\n            }, 1000);\n          }\n        }\n      });\n    },\n    \n    promotePublish(item) {\n      this.currentItem = item;\n      uni.showActionSheet({\n        itemList: ['置顶信息', '刷新信息', '续费信息'],\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0: // 置顶\n              this.promotionType = 'top';\n              this.promotionTypeText = '置顶';\n              this.topCardVisible = true;\n              break;\n            case 1: // 刷新\n              uni.navigateTo({\n                url: `/pages/services/refresh-info?id=${item.id}`\n              });\n              break;\n            case 2: // 续费\n              uni.navigateTo({\n                url: `/pages/services/renew-info?id=${item.id}`\n              });\n              break;\n          }\n        }\n      });\n    },\n    \n    handleShare(item) {\n      uni.showShareMenu({\n        withShareTicket: true,\n        menus: ['shareAppMessage', 'shareTimeline']\n      });\n    },\n    \n    handleMore(item) {\n      this.showMoreActions(item);\n    },\n    \n    showMoreActions(item) {\n      uni.showActionSheet({\n        itemList: ['编辑信息', '删除信息', '举报', '复制链接', '收藏', '取消置顶'],\n        success: (res) => {\n          switch(res.tapIndex) {\n            case 0:\n              this.handleEdit(item);\n              break;\n            case 1:\n              this.confirmDelete(item);\n              break;\n            case 2:\n              uni.showToast({\n                title: '举报成功',\n                icon: 'success'\n              });\n              break;\n            case 3:\n              uni.setClipboardData({\n                data: `http://example.com/info/${item.id}`,\n                success: () => {\n                  uni.showToast({\n                    title: '链接已复制',\n                    icon: 'success'\n                  });\n                }\n              });\n              break;\n            case 4:\n              uni.showToast({\n                title: '收藏成功',\n                icon: 'success'\n              });\n              break;\n            case 5:\n              uni.showToast({\n                title: '已取消置顶',\n                icon: 'success'\n              });\n              break;\n          }\n        }\n      });\n    },\n    \n    confirmDelete(item) {\n      uni.showModal({\n        title: '提示',\n        content: '确定要删除该信息吗？',\n        success: (res) => {\n          if (res.confirm) {\n            this.infoList = this.infoList.filter(info => info.id !== item.id);\n            uni.showToast({\n              title: '删除成功',\n              icon: 'success'\n            });\n          }\n        }\n      });\n    },\n    \n    onShareAppMessage(res) {\n      // 来自页面内的转发按钮\n      if (res.from === 'button') {\n        const item = res.target.dataset.item;\n        if (item) {\n          return {\n            title: item.title || '我的发布内容分享',\n            path: `/pages/publish/detail?id=${item.id}`,\n            imageUrl: item.image || '/static/images/default-activity.png'\n          };\n        }\n      }\n      \n      // 默认分享\n      return {\n        title: '我的发布',\n        path: '/pages/my/publish',\n        imageUrl: '/static/images/default-share.png'\n      };\n    },\n    \n    goToDetail(item) {\n      uni.navigateTo({\n        url: `/pages/publish/detail?id=${item.id}`\n      });\n    },\n  }\n};\n</script>\n\n<style scoped>\n.publish-container {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.custom-navbar {\n  background: linear-gradient(135deg, #0066FF, #0052CC);\n  height: 88rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  padding-bottom: 10rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n  z-index: 10;\n}\n\n.navbar-title {\n  color: #FFFFFF;\n  font-size: 36rpx;\n  font-weight: 600;\n  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n  margin: 0 auto;\n  z-index: 1;\n}\n\n.navbar-left, .navbar-right {\n  position: absolute;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 2;\n}\n\n.navbar-left {\n  left: 20rpx;\n  width: 60rpx;\n}\n\n.navbar-right {\n  right: 20rpx;\n  width: 60rpx;\n}\n\n.back-icon {\n  width: 40rpx;\n  height: 40rpx;\n  background: none;\n}\n\n.tab-container {\n  display: flex;\n  justify-content: space-between;\n  flex-wrap: wrap;\n  background: #fff;\n  padding: 0 20rpx;\n  border-bottom: 1rpx solid #eee;\n}\n\n.tab-item {\n  position: relative;\n  height: 80rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 25%;\n  box-sizing: border-box;\n}\n\n.tab-text {\n  font-size: 30rpx;\n  color: #666;\n}\n\n.tab-item.active .tab-text {\n  color: #0052d9;\n  font-weight: bold;\n}\n\n.active-line {\n  position: absolute;\n  bottom: 0;\n  width: 60rpx;\n  height: 6rpx;\n  background: #0052d9;\n  border-radius: 3rpx;\n}\n\n.list-container {\n  padding: 20rpx;\n}\n\n.info-card {\n  background: #fff;\n  border-radius: 12rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n}\n\n.info-header {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 20rpx;\n}\n\n.info-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.info-status {\n  font-size: 24rpx;\n  padding: 6rpx 12rpx;\n  border-radius: 6rpx;\n}\n\n.status-reviewing {\n  background: #FFF8E6;\n  color: #FF9900;\n}\n\n.status-passed {\n  background: #E6F7E6;\n  color: #52C41A;\n}\n\n.status-expired {\n  background: #F0F0F0;\n  color: #999;\n}\n\n.info-content {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.6;\n  margin-bottom: 20rpx;\n}\n\n.info-stats {\n  display: flex;\n  color: #999;\n  font-size: 24rpx;\n  margin-bottom: 30rpx;\n}\n\n.info-date {\n  margin-right: 30rpx;\n}\n\n.info-views {\n  margin-right: 30rpx;\n}\n\n.info-actions {\n  display: flex;\n  gap: 10px;\n  justify-content: flex-end;\n  margin-top: 8px;\n}\n\n.activity-btn {\n  height: 28px;\n  padding: 0 12px;\n  border-radius: 14px;\n  font-size: 12px;\n  font-weight: 600;\n  position: relative;\n  overflow: hidden;\n  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.08);\n  border: 0.5px solid rgba(255, 255, 255, 0.5);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  background: linear-gradient(135deg, #ECF5FF 0%, #E2F0FF 100%);\n  color: #007AFF;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.activity-btn::before {\n  display: none;\n}\n\n.activity-btn::after {\n  display: none;\n}\n\n.activity-btn:active {\n  transform: translateY(1px) scale(0.98);\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  background: linear-gradient(135deg, #E2F0FF 0%, #D8EAFF 100%);\n}\n\n.activity-btn[data-type=\"edit\"] {\n  background: linear-gradient(135deg, #EEF6FF 0%, #E2F0FF 100%);\n  color: #0070E0;\n}\n\n.activity-btn[data-type=\"promote\"] {\n  background: linear-gradient(135deg, #FFF2E6 0%, #FFE8D1 100%);\n  color: #FF7D00;\n}\n\n.activity-btn[data-type=\"share\"] {\n  background: linear-gradient(135deg, #E9F9F0 0%, #DCF2E5 100%);\n  color: #27AE60;\n}\n\n.empty-container {\n  padding: 100rpx 0;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.empty-icon {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 30rpx;\n}\n\n.empty-text {\n  font-size: 30rpx;\n  color: #999;\n}\n\n/* 置顶卡片样式 */\n.top-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.6);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 999;\n}\n\n.top-card {\n  background-color: #fff;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  width: 85%;\n  max-width: 650rpx;\n}\n\n.top-title {\n  font-size: 34rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 30rpx;\n  text-align: center;\n}\n\n.top-block {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 24rpx 20rpx;\n  margin-bottom: 20rpx;\n  border-radius: 12rpx;\n  border: 1rpx solid #f0f0f0;\n}\n\n.ad-block {\n  background-color: #F8FFF9;\n}\n\n.pay-block {\n  background-color: #FFF9F0;\n}\n\n.block-left {\n  flex: 1;\n}\n\n.block-main {\n  font-size: 30rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 6rpx;\n}\n\n.block-sub {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.block-right {\n  display: flex;\n  align-items: center;\n}\n\n.arrow {\n  margin-left: 10rpx;\n  font-size: 24rpx;\n  color: #999;\n}\n\n.ad-tips-overlay, .ad-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.7);\n  z-index: 1000;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.ad-tips-card {\n  width: 80%;\n  background-color: #fff;\n  border-radius: 20rpx;\n  overflow: hidden;\n}\n\n.ad-tips-title {\n  font-size: 34rpx;\n  font-weight: bold;\n  text-align: center;\n  padding: 30rpx 0;\n  color: #333;\n  border-bottom: 1rpx solid #eee;\n}\n\n.ad-tips-content {\n  padding: 30rpx;\n}\n\n.ad-tips-item {\n  font-size: 28rpx;\n  color: #666;\n  margin-bottom: 20rpx;\n  line-height: 1.5;\n}\n\n.highlight {\n  color: #FF6600;\n  font-weight: bold;\n}\n\n.ad-tips-btns {\n  display: flex;\n  border-top: 1rpx solid #eee;\n}\n\n.ad-tips-btn {\n  flex: 1;\n  text-align: center;\n  padding: 24rpx 0;\n  font-size: 30rpx;\n}\n\n.ad-tips-btn.cancel {\n  color: #999;\n  border-right: 1rpx solid #eee;\n}\n\n.ad-tips-btn.confirm {\n  color: #0066FF;\n  font-weight: bold;\n}\n\n.ad-container {\n  width: 90%;\n  background-color: #fff;\n  border-radius: 12rpx;\n  overflow: hidden;\n}\n\n.ad-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20rpx 30rpx;\n  background-color: #f5f5f5;\n}\n\n.ad-title {\n  font-size: 28rpx;\n  color: #999;\n}\n\n.ad-countdown {\n  font-size: 26rpx;\n  color: #FF6600;\n  font-weight: bold;\n}\n\n.ad-content {\n  position: relative;\n  height: 400rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.ad-image {\n  width: 100%;\n  height: 100%;\n}\n\n.ad-message {\n  position: absolute;\n  background-color: rgba(0, 0, 0, 0.5);\n  color: #fff;\n  padding: 10rpx 20rpx;\n  border-radius: 8rpx;\n  font-size: 26rpx;\n}\n\n.ad-footer {\n  padding: 20rpx 30rpx;\n  display: flex;\n  flex-direction: column;\n}\n\n.ad-tips {\n  font-size: 24rpx;\n  color: #999;\n  margin-bottom: 20rpx;\n}\n\n.ad-close-btn {\n  align-self: center;\n  width: 80%;\n  text-align: center;\n  padding: 16rpx 0;\n  border-radius: 50rpx;\n  font-size: 28rpx;\n  background-color: #f0f0f0;\n  color: #999;\n}\n\n.ad-close-active {\n  background-color: #0066FF;\n  color: #fff;\n}\n\n.top-result {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1001;\n  pointer-events: none;\n}\n\n.top-result-content {\n  background-color: rgba(0, 0, 0, 0.7);\n  padding: 40rpx;\n  border-radius: 20rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.top-result-icon {\n  width: 120rpx;\n  height: 120rpx;\n  margin-bottom: 20rpx;\n}\n\n.top-result-text {\n  color: #fff;\n  font-size: 30rpx;\n}\n\n/* 付费置顶选项弹出层 */\n.top-options-dropdown {\n  background-color: #fff;\n  width: 100%;\n  border-radius: 0 0 20rpx 20rpx;\n  padding: 30rpx 20rpx;\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);\n  animation: dropDown 0.3s ease;\n}\n\n@keyframes dropDown {\n  from { \n    opacity: 0;\n    transform: translateY(-50%);\n  }\n  to { \n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.top-options-title {\n  text-align: center;\n  font-size: 32rpx;\n  color: #333;\n  font-weight: bold;\n  margin-bottom: 30rpx;\n  position: relative;\n  padding-bottom: 20rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.top-options-list {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 20rpx;\n  padding: 0 10rpx;\n}\n\n.top-option-item {\n  width: 30%;\n  height: 140rpx;\n  background-color: #f8f8f8;\n  border-radius: 12rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  border: 2rpx solid transparent;\n  transition: all 0.2s;\n}\n\n.top-option-item.active {\n  background-color: #FFF5E6;\n  border-color: #FF9500;\n}\n\n.top-option-days {\n  font-size: 32rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 10rpx;\n}\n\n.top-option-price {\n  font-size: 28rpx;\n  color: #FF9500;\n  font-weight: 600;\n}\n\n/* 付费按钮样式优化 */\n.pay-btn {\n  background-color: #FF9500;\n  color: #fff;\n  font-size: 24rpx;\n  padding: 6rpx 20rpx;\n  border-radius: 30rpx;\n}\n\n/* 免费按钮样式优化 */\n.free-btn {\n  background-color: #34C759;\n  color: #fff;\n  font-size: 24rpx;\n  padding: 6rpx 20rpx;\n  border-radius: 30rpx;\n}\n\n/* 自定义弹窗样式 */\n.custom-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.65);\n  z-index: 1000;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  backdrop-filter: blur(10rpx);\n  -webkit-backdrop-filter: blur(10rpx);\n}\n\n.modal-mask {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: transparent;\n  z-index: 1001;\n}\n\n.modal-content {\n  background-color: rgba(255, 255, 255, 0.98);\n  border-radius: 16rpx;\n  overflow: hidden;\n  width: 85%;\n  max-width: 600rpx;\n  position: relative;\n  z-index: 1002;\n  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.25);\n  transform: translateY(0);\n  animation: modal-in 0.3s cubic-bezier(0.23, 1, 0.32, 1);\n}\n\n@keyframes modal-in {\n  from {\n    opacity: 0;\n    transform: translateY(20rpx);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.modal-title {\n  font-size: 34rpx;\n  font-weight: 600;\n  text-align: center;\n  padding: 30rpx 0;\n  color: #000;\n  border-bottom: 0.5rpx solid rgba(60, 60, 67, 0.12);\n}\n\n.modal-body {\n  padding: 35rpx 30rpx;\n}\n\n.modal-text {\n  font-size: 28rpx;\n  color: rgba(60, 60, 67, 0.85);\n  margin-bottom: 20rpx;\n  line-height: 1.5;\n  text-align: center;\n  letter-spacing: 0.5rpx;\n}\n\n.price-text {\n  font-size: 30rpx;\n  color: rgba(60, 60, 67, 0.85);\n  text-align: center;\n  margin: 25rpx 0 15rpx;\n  font-weight: 500;\n}\n\n.price-amount {\n  font-size: 40rpx;\n  color: #FF2D55;\n  font-weight: 600;\n  margin: 0 6rpx;\n}\n\n.rank-hint {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 25rpx 0;\n  font-size: 26rpx;\n  color: rgba(60, 60, 67, 0.7);\n  background-color: rgba(0, 122, 255, 0.08);\n  padding: 12rpx 20rpx;\n  border-radius: 30rpx;\n}\n\n.rank-icon {\n  width: 32rpx;\n  height: 32rpx;\n  margin-right: 10rpx;\n}\n\n.special-hint {\n  font-size: 26rpx;\n  color: #FF9500;\n  text-align: center;\n  margin: 18rpx 0 10rpx;\n  font-weight: 500;\n}\n\n.modal-footer {\n  display: flex;\n  justify-content: space-between;\n  padding: 0;\n  border-top: 0.5rpx solid rgba(60, 60, 67, 0.12);\n}\n\n.modal-btn {\n  flex: 1;\n  text-align: center;\n  padding: 25rpx 0;\n  font-size: 32rpx;\n  border-radius: 0;\n  border: none;\n  margin: 0;\n  line-height: 1.5;\n  font-weight: 500;\n  transition: background-color 0.2s ease;\n}\n\n.modal-btn::after {\n  border: none;\n}\n\n.cancel-btn {\n  background-color: rgba(242, 242, 247, 0.95);\n  color: #007AFF;\n}\n\n.cancel-btn:active {\n  background-color: rgba(229, 229, 234, 1);\n}\n\n.confirm-btn {\n  background-color: #007AFF;\n  color: white;\n}\n\n.confirm-btn:active {\n  background-color: #0062CC;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/my/publish.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAiMA,MAAA,YAAA;AAAA;;EAGE,OAAA;AACE,WAAA;AAAA,MACE,iBAAA;AAAA;;MAGA,UAAA;AAAA,QACE;AAAA;;UAGE,SAAA;AAAA,UACA,MAAA;AAAA,UACA,OAAA;AAAA,UACA,OAAA;AAAA,UACA,QAAA;AAAA;AAAA,UACA,YAAA;AAAA;QAEF;AAAA;UAEE,OAAA;AAAA,UACA,SAAA;AAAA,UACA,MAAA;AAAA,UACA,OAAA;AAAA,UACA,OAAA;AAAA;;UAEA,YAAA;AAAA;QAEF;AAAA;;UAGE,SAAA;AAAA,UACA,MAAA;AAAA,UACA,OAAA;AAAA,UACA,OAAA;AAAA,UACA,QAAA;AAAA;AAAA,UACA,YAAA;AAAA,QACF;AAAA;MAEF,gBAAA;AAAA,MACA,aAAA;AAAA;MAEA,gBAAA;AAAA;QAEE,EAAA,MAAA,GAAA,OAAA,IAAA;AAAA,QACA,EAAA,MAAA,GAAA,OAAA,IAAA;AAAA,QACA,EAAA,MAAA,IAAA,OAAA,KAAA;AAAA;MAEF,eAAA;AAAA;MAEA,aAAA;AAAA;;MAGA,YAAA;AAAA,MACA,eAAA;AAAA;;;;MAGA,eAAA;AAAA;AAAA,MACA,mBAAA;AAAA;AAAA;;EAIJ,SAAA;AACE,UAAA,UAAAA,oBAAA;AACA,SAAA,kBAAA,QAAA;AAAA;EAGF,UAAA;AAAA,IACE,eAAA;;AAEI,eAAA,KAAA;AAAA;;;;;;MAOF;AACA,aAAA;IACF;AAAA;EAGF,SAAA;AAAA,IACE,SAAA;;AAEEA,oBAAAA,MAAA,aAAA;AAAA,QACE,MAAA,WAAA;AACEA,wBAAAA,MAAA,UAAA;AAAA;UAEA,CAAA;AAAA,QACF;AAAA,MACF,CAAA;AAAA;IAGF,UAAA,OAAA;AACE,WAAA,aAAA;AAAA;;AAIA,cAAA,QAAA;AAAA,QACE,KAAA;;;AAGE,iBAAA;AAAA;;QAGF;AACE,iBAAA;AAAA,MACJ;AAAA;IAGF,UAAA,MAAA;AACE,WAAA,cAAA;;;;;;;IASF,iBAAA;;;IAIA,iBAAA;;;IAIA,aAAA,OAAA;;AAEE,YAAA,SAAA,KAAA,WAAA,KAAA;AACA,YAAA,YAAA;AAAA;QAEE,MAAA,OAAA;AAAA,QACA,QAAA,OAAA;AAAA;QAEA,QAAAA,cAAAA,MAAA,eAAA,QAAA;AAAA;AAGFA,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA,8BAAA,mBAAA,KAAA,UAAA,SAAA,CAAA,CAAA;AAAA,MACF,CAAA;AAEA,WAAA,YAAA;AAAA;;;;;;;IAWF,eAAA;;;AAGE,WAAA,cAAA;;AAGE,aAAA;;;;QAIA;AAAA,MACF,GAAA,GAAA;AAAA;IAGF,UAAA;AACE,UAAA,KAAA,eAAA,KAAA,KAAA,WAAA;;AAEE,aAAA,UAAA;;;AAGAA,sBAAAA,MAAA,UAAA;AAAA,UACE,OAAA,QAAA,KAAA,SAAA;AAAA;;QAGF,CAAA;AAAA,MACF;AAAA;;;;AAOE,YAAA;AACEA,wBAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAEA,gBAAA,SAAA,KAAA,YAAA;AAEA,qBAAA,MAAA;AACEA,0BAAA,MAAA,YAAA;;AAGA,iBAAA,aAAA;AACA,iBAAA,gBAAA,GAAA,KAAA,iBAAA,SAAA,KAAA,iBAAA;AAEA,uBAAA,MAAA;;AAEE,mBAAA,YAAA;AAAA,YACF,GAAA,GAAA;AAAA,UACF,GAAA,GAAA;AAAA;AAGAA,wBAAA,MAAA,YAAA;AACAA,wBAAA,MAAA,MAAA,SAAA,+BAAA,YAAA,KAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACE,OAAA;AAAA;UAEF,CAAA;AAAA,QACF;AAAA;;AAGA,aAAA,aAAA;;AAEA,mBAAA,MAAA;;QAEA,GAAA,GAAA;AAAA,MACF;AAAA;IAGF,cAAA,MAAA;;AAII,aAAA,qBAAA,IAAA;AACA;AAAA,MACF;AAGAA,oBAAAA,MAAA,UAAA;AAAA;;QAGE,aAAA;AAAA,QACA,YAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;;AAIEA,0BAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGN,uBAAA,MAAA;AACEA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA;;cAGA,CAAA;AAAA,YACF,GAAA,GAAA;AAAA;AAGMA,0BAAAA,MAAA,WAAA;AAAA,cACE,KAAA;AAAA,YACF,CAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKA,WAAA,cAAA;AAAA,QACE,MAAA;AAAA;QAEA,MAAA;AAAA,QACA,YAAA;AAAA,QACA,YAAA;AAAA,QACA;AAAA;;;IAKJ,mBAAA;AACE,WAAA,cAAA;AAAA;;;;AAMAA,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA;AAAA,MACF,CAAA;AAAA;;;;AAQAA,oBAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,iBAAA,MAAA;AACEA,sBAAA,MAAA,YAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA;;QAGA,CAAA;AAAA,MACF,GAAA,IAAA;AAAA;IAGF,WAAA,MAAA;AACEA,oBAAAA,MAAA,gBAAA;AAAA;QAEE,SAAA,CAAA,QAAA;AACE,kBAAA,IAAA,UAAA;AAAA;;;;;;YAOE,KAAA;AACE,mBAAA,oBAAA,IAAA;;UAEJ;AAAA,QACF;AAAA,MACF,CAAA;AAAA;IAGF,cAAA,MAAA;AAEEA,oBAAAA,MAAA,WAAA;AAAA;MAEA,CAAA;AAAA;;AAIA,YAAA,SAAA,KAAA,WAAA,WAAA,OAAA;AACAA,oBAAAA,MAAA,UAAA;AAAA,QACE,OAAA,GAAA,MAAA;AAAA;QAEA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;AACEA,0BAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,uBAAA,MAAA;;AAGI,qBAAA,SAAA;AACA,qBAAA,aAAA;AAAA;AAEA,qBAAA,SAAA;;cAEF;AAEAA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA,gBACE,OAAA,GAAA,MAAA;AAAA;cAEF,CAAA;AAAA,YACF,GAAA,GAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;IAGF,eAAA,MAAA;AACE,WAAA,cAAA;AACAA,oBAAAA,MAAA,gBAAA;AAAA,QACE,UAAA,CAAA,QAAA,QAAA,MAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,kBAAA,IAAA,UAAA;AAAA,YACE,KAAA;;;;;YAKA,KAAA;AACEA,4BAAAA,MAAA,WAAA;AAAA;cAEA,CAAA;;YAEF,KAAA;AACEA,4BAAAA,MAAA,WAAA;AAAA,gBACE,KAAA,iCAAA,KAAA,EAAA;AAAA,cACF,CAAA;;UAEJ;AAAA,QACF;AAAA,MACF,CAAA;AAAA;IAGF,YAAA,MAAA;AACEA,oBAAAA,MAAA,cAAA;AAAA,QACE,iBAAA;AAAA,QACA,OAAA,CAAA,mBAAA,eAAA;AAAA,MACF,CAAA;AAAA;IAGF,WAAA,MAAA;;;IAIA,gBAAA,MAAA;AACEA,oBAAAA,MAAA,gBAAA;AAAA;QAEE,SAAA,CAAA,QAAA;AACE,kBAAA,IAAA,UAAA;AAAA;AAEI,mBAAA,WAAA,IAAA;;;;;;AAMAA,4BAAAA,MAAA,UAAA;AAAA;;cAGA,CAAA;;;AAGAA,4BAAAA,MAAA,iBAAA;AAAA,gBACE,MAAA,2BAAA,KAAA,EAAA;AAAA;AAEEA,gCAAAA,MAAA,UAAA;AAAA;;kBAGA,CAAA;AAAA,gBACF;AAAA,cACF,CAAA;;;AAGAA,4BAAAA,MAAA,UAAA;AAAA;;cAGA,CAAA;;;AAGAA,4BAAAA,MAAA,UAAA;AAAA;;cAGA,CAAA;;UAEJ;AAAA,QACF;AAAA,MACF,CAAA;AAAA;IAGF,cAAA,MAAA;AACEA,oBAAAA,MAAA,UAAA;AAAA;QAEE,SAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;;AAEEA,0BAAAA,MAAA,UAAA;AAAA;;YAGA,CAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAME,cAAA,OAAA,IAAA,OAAA,QAAA;AACA,YAAA,MAAA;AACE,iBAAA;AAAA;YAEE,MAAA,4BAAA,KAAA,EAAA;AAAA;;QAGJ;AAAA,MACF;AAGA,aAAA;AAAA;;QAGE,UAAA;AAAA;;IAIJ,WAAA,MAAA;AACEA,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA,4BAAA,KAAA,EAAA;AAAA,MACF,CAAA;AAAA;EAEJ;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvqBA,GAAG,WAAW,eAAe;"}