{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/favorites/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcZmF2b3JpdGVzXGluZGV4LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"favorites-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      right: 0,\n      height: 'calc(var(--status-bar-height, 25px) + 62px)',\n      width: '100%',\n      zIndex: 100\n    }\">\n      <view class=\"navbar-bg\" :style=\"{\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: '100%',\n        height: '100%',\n        background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',\n        boxShadow: '0 2px 10px rgba(0,0,0,0.05)'\n      }\"></view>\n      \n      <view class=\"navbar-content\" :style=\"{\n        position: 'relative',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        height: '100%',\n        padding: '0 30rpx',\n        paddingTop: 'var(--status-bar-height, 25px)',\n        boxSizing: 'border-box'\n      }\">\n        <view class=\"back-btn\" @click=\"goBack\" :style=\"{\n          width: '80rpx',\n          height: '80rpx',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        }\">\n          <image src=\"/static/images/tabbar/最新返回键.png\" mode=\"aspectFit\" :style=\"{\n            width: '40rpx',\n            height: '40rpx'\n          }\"></image>\n        </view>\n        \n        <text class=\"navbar-title\" :style=\"{\n          fontSize: '36rpx',\n          fontWeight: '600',\n          color: '#FFFFFF'\n        }\">我的收藏</text>\n        \n        <!-- 占位元素，保持布局平衡 -->\n        <view style=\"width: 80rpx;\"></view>\n      </view>\n    </view>\n    \n    <!-- 内容区域 -->\n    <view class=\"content-area\" :style=\"{\n      paddingTop: 'calc(var(--status-bar-height, 25px) + 62px)',\n      paddingBottom: isEditMode ? '180rpx' : '0',\n      minHeight: '100vh',\n      boxSizing: 'border-box'\n    }\">\n      <!-- 分类标签栏和编辑按钮 -->\n      <view :style=\"{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        padding: '20rpx 30rpx',\n        background: '#FFFFFF',\n        marginBottom: '20rpx',\n        boxShadow: '0 2px 10px rgba(0,0,0,0.05)'\n      }\">\n        <scroll-view \n          scroll-x \n          class=\"category-tabs\" \n          :style=\"{\n            whiteSpace: 'nowrap',\n            flex: 1,\n            overflow: 'hidden'\n          }\"\n        >\n          <view \n            v-for=\"(category, index) in categories\" \n            :key=\"index\"\n            class=\"category-item\"\n            :class=\"{ active: currentCategory === index }\"\n            @click=\"switchCategory(index)\"\n            :style=\"{\n              display: 'inline-block',\n              padding: '10rpx 30rpx',\n              marginRight: '20rpx',\n              borderRadius: '30rpx',\n              fontSize: '28rpx',\n              color: currentCategory === index ? '#FFFFFF' : '#666666',\n              background: currentCategory === index ? 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)' : '#F2F2F7',\n              boxShadow: currentCategory === index ? '0 4px 10px rgba(255,59,105,0.2)' : 'none'\n            }\"\n          >\n            {{ category.name }}\n          </view>\n        </scroll-view>\n        \n        <!-- 编辑按钮移到这里 -->\n        <view class=\"edit-btn\" @click=\"toggleEditMode\" :style=\"{\n          marginLeft: '20rpx',\n          padding: '10rpx 30rpx',\n          borderRadius: '30rpx',\n          fontSize: '28rpx',\n          fontWeight: '500',\n          background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',\n          color: '#FFFFFF',\n          boxShadow: '0 4px 10px rgba(255,59,105,0.2)'\n        }\">\n          {{ isEditMode ? '完成' : '编辑' }}\n        </view>\n      </view>\n      \n      <!-- 收藏列表 -->\n      <view class=\"favorites-list\" :style=\"{\n        padding: '0 20rpx'\n      }\">\n        <view \n          v-for=\"(item, index) in filteredFavorites\" \n          :key=\"index\"\n          class=\"favorite-item\"\n          :style=\"{\n            background: '#FFFFFF',\n            borderRadius: '20rpx',\n            marginBottom: '20rpx',\n            position: 'relative',\n            overflow: 'hidden',\n            boxShadow: '0 4px 10px rgba(0,0,0,0.05)'\n          }\"\n        >\n          <!-- 选择框 -->\n          <view \n            v-if=\"isEditMode\" \n            class=\"select-box\" \n            @click.stop=\"toggleSelect(item)\"\n            :style=\"{\n              position: 'absolute',\n              top: '20rpx',\n              left: '20rpx',\n              width: '40rpx',\n              height: '40rpx',\n              borderRadius: '50%',\n              border: item.selected ? '0' : '2rpx solid #CCCCCC',\n              background: item.selected ? '#FF3B69' : '#FFFFFF',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              zIndex: '10'\n            }\"\n          >\n            <svg v-if=\"item.selected\" class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n              <path d=\"M5 12l5 5L20 7\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n            </svg>\n          </view>\n          \n          <!-- 内容区域 -->\n          <view class=\"item-content\" @click=\"viewDetail(item)\" :style=\"{\n            display: 'flex',\n            padding: '20rpx'\n          }\">\n            <image \n              :src=\"item.image\" \n              mode=\"aspectFill\" \n              :style=\"{\n                width: '200rpx',\n                height: '200rpx',\n                borderRadius: '10rpx',\n                marginRight: '20rpx'\n              }\"\n            ></image>\n            \n            <view class=\"item-info\" :style=\"{\n              flex: '1',\n              display: 'flex',\n              flexDirection: 'column',\n              justifyContent: 'space-between'\n            }\">\n              <view class=\"item-top\">\n                <text class=\"item-title\" :style=\"{\n                  fontSize: '28rpx',\n                  fontWeight: '500',\n                  color: '#333333',\n                  marginBottom: '10rpx',\n                  display: 'block',\n                  overflow: 'hidden',\n                  textOverflow: 'ellipsis',\n                  display: '-webkit-box',\n                  '-webkit-line-clamp': '2',\n                  '-webkit-box-orient': 'vertical',\n                  lineHeight: '1.4'\n                }\">{{ item.title }}</text>\n                \n                <text class=\"item-shop\" :style=\"{\n                  fontSize: '24rpx',\n                  color: '#999999',\n                  marginBottom: '10rpx',\n                  display: 'block'\n                }\">{{ item.shop }}</text>\n              </view>\n              \n              <view class=\"item-bottom\" :style=\"{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'flex-end'\n              }\">\n                <view class=\"price-info\">\n                  <view class=\"current-price\" :style=\"{\n                    display: 'flex',\n                    alignItems: 'baseline'\n                  }\">\n                    <text :style=\"{\n                      fontSize: '24rpx',\n                      color: '#FF3B69',\n                      marginRight: '5rpx'\n                    }\">¥</text>\n                    <text :style=\"{\n                      fontSize: '32rpx',\n                      fontWeight: '600',\n                      color: '#FF3B69'\n                    }\">{{ item.price }}</text>\n                  </view>\n                  \n                  <text v-if=\"item.originalPrice\" class=\"original-price\" :style=\"{\n                    fontSize: '24rpx',\n                    color: '#999999',\n                    textDecoration: 'line-through'\n                  }\">¥{{ item.originalPrice }}</text>\n                </view>\n                \n                <view class=\"item-tag\" :style=\"{\n                  padding: '6rpx 16rpx',\n                  borderRadius: '20rpx',\n                  background: getTagBackground(item.type),\n                  color: '#FFFFFF',\n                  fontSize: '22rpx',\n                  fontWeight: '500'\n                }\">\n                  {{ getTagText(item.type) }}\n                </view>\n              </view>\n            </view>\n          </view>\n          \n          <!-- 收藏时间 -->\n          <view class=\"favorite-time\" :style=\"{\n            padding: '10rpx 20rpx',\n            borderTop: '1rpx solid #F2F2F7',\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          }\">\n            <text :style=\"{\n              fontSize: '24rpx',\n              color: '#999999'\n            }\">收藏于 {{ item.favoriteTime }}</text>\n            \n            <view class=\"action-btn\" @click.stop=\"removeFavorite(item)\" :style=\"{\n              display: 'flex',\n              alignItems: 'center'\n            }\">\n              <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\" :style=\"{ marginRight: '5rpx' }\">\n                <path d=\"M19 21l-7-5-7 5V5a2 2 0 012-2h10a2 2 0 012 2v16z\" fill=\"#FF3B69\" stroke=\"#FF3B69\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n              </svg>\n              <text :style=\"{\n                fontSize: '24rpx',\n                color: '#FF3B69'\n              }\">取消收藏</text>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 空状态 -->\n        <view v-if=\"filteredFavorites.length === 0\" class=\"empty-state\" :style=\"{\n          padding: '100rpx 0',\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        }\">\n          <image src=\"/static/images/empty/empty-favorites.png\" mode=\"aspectFit\" :style=\"{\n            width: '200rpx',\n            height: '200rpx',\n            marginBottom: '20rpx'\n          }\"></image>\n          <text :style=\"{\n            fontSize: '28rpx',\n            color: '#999999',\n            marginBottom: '30rpx'\n          }\">暂无收藏内容</text>\n          \n          <view class=\"action-btn\" @click=\"goExplore\" :style=\"{\n            padding: '16rpx 40rpx',\n            borderRadius: '35px',\n            background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',\n            color: '#FFFFFF',\n            fontSize: '28rpx',\n            boxShadow: '0 5px 15px rgba(255,59,105,0.3)'\n          }\">\n            去探索\n          </view>\n        </view>\n        \n        <!-- 加载更多 -->\n        <view v-if=\"filteredFavorites.length > 0 && !noMore\" class=\"load-more\" :style=\"{\n          textAlign: 'center',\n          padding: '30rpx 0'\n        }\">\n          <text v-if=\"loading\" :style=\"{\n            fontSize: '26rpx',\n            color: '#999999'\n          }\">加载中...</text>\n          <text v-else @click=\"loadMore\" :style=\"{\n            fontSize: '26rpx',\n            color: '#FF3B69'\n          }\">加载更多</text>\n        </view>\n        \n        <!-- 没有更多数据 -->\n        <view v-if=\"filteredFavorites.length > 0 && noMore\" class=\"no-more\" :style=\"{\n          textAlign: 'center',\n          padding: '30rpx 0'\n        }\">\n          <text :style=\"{\n            fontSize: '26rpx',\n            color: '#999999'\n          }\">没有更多收藏了</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部操作栏 -->\n    <view v-if=\"isEditMode\" class=\"bottom-action-bar\" :style=\"{\n      position: 'fixed',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      height: '120rpx',\n      background: '#FFFFFF',\n      boxShadow: '0 -2px 10px rgba(0,0,0,0.05)',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'space-between',\n      padding: '0 30rpx',\n      paddingBottom: 'env(safe-area-inset-bottom)',\n      zIndex: 100\n    }\">\n      <view class=\"select-all-btn\" @click=\"toggleSelectAll\" :style=\"{\n        display: 'flex',\n        alignItems: 'center'\n      }\">\n        <view class=\"select-box\" :style=\"{\n          width: '40rpx',\n          height: '40rpx',\n          borderRadius: '50%',\n          border: isAllSelected ? '0' : '2rpx solid #CCCCCC',\n          background: isAllSelected ? '#FF3B69' : '#FFFFFF',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          marginRight: '10rpx'\n        }\">\n          <svg v-if=\"isAllSelected\" class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n            <path d=\"M5 12l5 5L20 7\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n          </svg>\n        </view>\n        <text :style=\"{\n          fontSize: '28rpx',\n          color: '#333333'\n        }\">全选</text>\n      </view>\n      \n      <view \n        class=\"delete-btn\" \n        @click=\"batchDelete\"\n        :style=\"{\n          padding: '16rpx 40rpx',\n          borderRadius: '35px',\n          background: selectedCount > 0 ? 'linear-gradient(135deg, #FF3B30 0%, #FF5E3A 100%)' : '#CCCCCC',\n          color: '#FFFFFF',\n          fontSize: '28rpx',\n          boxShadow: selectedCount > 0 ? '0 5px 15px rgba(255,59,48,0.3)' : 'none'\n        }\"\n      >\n        删除({{ selectedCount }})\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed } from 'vue';\n\n// 编辑模式\nconst isEditMode = ref(false);\n\n// 分类数据\nconst categories = ref([\n  { id: 0, name: '全部' },\n  { id: 1, name: '活动' },\n  { id: 2, name: '商品' },\n  { id: 3, name: '优惠券' },\n  { id: 4, name: '拼团' },\n  { id: 5, name: '秒杀' }\n]);\nconst currentCategory = ref(0);\n\n// 加载状态\nconst loading = ref(false);\nconst noMore = ref(false);\n\n// 收藏数据\nconst favorites = ref([\n  {\n    id: 1,\n    title: 'Apple iPhone 14 Pro Max 256GB 暗夜紫 移动联通电信5G双卡双待手机',\n    image: 'https://via.placeholder.com/200',\n    shop: 'Apple官方旗舰店',\n    price: '8999.00',\n    originalPrice: '9999.00',\n    type: 'product',\n    favoriteTime: '2023-05-15 14:30',\n    selected: false\n  },\n  {\n    id: 2,\n    title: '618年中大促全场低至5折起',\n    image: 'https://via.placeholder.com/200',\n    shop: '京东自营',\n    price: '0.00',\n    originalPrice: '',\n    type: 'activity',\n    favoriteTime: '2023-05-14 09:15',\n    selected: false\n  },\n  {\n    id: 3,\n    title: '满300减50全场优惠券',\n    image: 'https://via.placeholder.com/200',\n    shop: '京东自营',\n    price: '50.00',\n    originalPrice: '',\n    type: 'coupon',\n    favoriteTime: '2023-05-10 16:42',\n    selected: false\n  },\n  {\n    id: 4,\n    title: '小米12S Ultra 12GB+256GB 丹青黑 骁龙8+旗舰处理器 徕卡专业光学镜头',\n    image: 'https://via.placeholder.com/200',\n    shop: '小米官方旗舰店',\n    price: '5999.00',\n    originalPrice: '6999.00',\n    type: 'product',\n    favoriteTime: '2023-05-08 11:23',\n    selected: false\n  },\n  {\n    id: 5,\n    title: '3人团：小米空气净化器',\n    image: 'https://via.placeholder.com/200',\n    shop: '小米官方旗舰店',\n    price: '699.00',\n    originalPrice: '999.00',\n    type: 'group',\n    favoriteTime: '2023-05-07 18:05',\n    selected: false\n  },\n  {\n    id: 6,\n    title: '限时秒杀：iPhone 14 Pro',\n    image: 'https://via.placeholder.com/200',\n    shop: 'Apple授权专卖店',\n    price: '6999.00',\n    originalPrice: '8999.00',\n    type: 'flash',\n    favoriteTime: '2023-05-05 10:30',\n    selected: false\n  }\n]);\n\n// 过滤后的收藏列表\nconst filteredFavorites = computed(() => {\n  if (currentCategory.value === 0) {\n    return favorites.value;\n  } else {\n    const categoryMap = {\n      1: 'activity',\n      2: 'product',\n      3: 'coupon',\n      4: 'group',\n      5: 'flash'\n    };\n    return favorites.value.filter(item => item.type === categoryMap[currentCategory.value]);\n  }\n});\n\n// 选中的数量\nconst selectedCount = computed(() => {\n  return favorites.value.filter(item => item.selected).length;\n});\n\n// 是否全选\nconst isAllSelected = computed(() => {\n  return favorites.value.length > 0 && favorites.value.every(item => item.selected);\n});\n\n// 返回上一页\nfunction goBack() {\n  uni.navigateBack();\n}\n\n// 切换编辑模式\nfunction toggleEditMode() {\n  isEditMode.value = !isEditMode.value;\n  \n  // 退出编辑模式时，取消所有选择\n  if (!isEditMode.value) {\n    favorites.value.forEach(item => {\n      item.selected = false;\n    });\n  }\n}\n\n// 切换分类\nfunction switchCategory(index) {\n  currentCategory.value = index;\n}\n\n// 查看详情\nfunction viewDetail(item) {\n  // 如果在编辑模式下，点击项目会选中/取消选中\n  if (isEditMode.value) {\n    toggleSelect(item);\n    return;\n  }\n  \n  // 根据不同类型跳转到不同页面\n  let url = '';\n  \n  switch(item.type) {\n    case 'product':\n      url = `/subPackages/activity-showcase/pages/detail/index?id=${item.id}&type=product`;\n      break;\n    case 'activity':\n      url = `/subPackages/activity-showcase/pages/detail/index?id=${item.id}&type=activity`;\n      break;\n    case 'coupon':\n      url = `/subPackages/activity-showcase/pages/coupon/detail?id=${item.id}`;\n      break;\n    case 'group':\n      url = `/subPackages/activity-showcase/pages/group-buy/detail?id=${item.id}`;\n      break;\n    case 'flash':\n      url = `/subPackages/activity-showcase/pages/flash-sale/detail?id=${item.id}`;\n      break;\n    default:\n      url = `/subPackages/activity-showcase/pages/detail/index?id=${item.id}`;\n  }\n  \n  uni.navigateTo({ url });\n}\n\n// 切换选中状态\nfunction toggleSelect(item) {\n  item.selected = !item.selected;\n}\n\n// 切换全选\nfunction toggleSelectAll() {\n  const newState = !isAllSelected.value;\n  favorites.value.forEach(item => {\n    item.selected = newState;\n  });\n}\n\n// 批量删除\nfunction batchDelete() {\n  if (selectedCount.value === 0) {\n    uni.showToast({\n      title: '请先选择要删除的收藏',\n      icon: 'none'\n    });\n    return;\n  }\n  \n  uni.showModal({\n    title: '删除收藏',\n    content: `确定要删除选中的${selectedCount.value}个收藏吗？`,\n    success: (res) => {\n      if (res.confirm) {\n        // 删除选中的收藏\n        favorites.value = favorites.value.filter(item => !item.selected);\n        \n        uni.showToast({\n          title: '删除成功',\n          icon: 'success'\n        });\n        \n        // 如果删除后没有收藏了，退出编辑模式\n        if (favorites.value.length === 0) {\n          isEditMode.value = false;\n        }\n      }\n    }\n  });\n}\n\n// 取消收藏\nfunction removeFavorite(item) {\n  uni.showModal({\n    title: '取消收藏',\n    content: '确定要取消收藏该内容吗？',\n    success: (res) => {\n      if (res.confirm) {\n        // 从收藏列表中移除\n        const index = favorites.value.findIndex(fav => fav.id === item.id);\n        if (index !== -1) {\n          favorites.value.splice(index, 1);\n        }\n        \n        uni.showToast({\n          title: '已取消收藏',\n          icon: 'success'\n        });\n      }\n    }\n  });\n}\n\n// 前往探索\nfunction goExplore() {\n  uni.switchTab({\n    url: '/pages/index/index'\n  });\n}\n\n// 加载更多\nfunction loadMore() {\n  if (loading.value || noMore.value) return;\n  \n  loading.value = true;\n  \n  // 模拟加载更多数据\n  setTimeout(() => {\n    // 这里应该调用API获取更多数据\n    // 模拟没有更多数据\n    noMore.value = true;\n    loading.value = false;\n  }, 1500);\n}\n\n// 获取标签背景色\nfunction getTagBackground(type) {\n  switch (type) {\n    case 'product':\n      return 'linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%)';\n    case 'activity':\n      return 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)';\n    case 'coupon':\n      return 'linear-gradient(135deg, #FF3B30 0%, #FF5E3A 100%)';\n    case 'group':\n      return 'linear-gradient(135deg, #34C759 0%, #30D158 100%)';\n    case 'flash':\n      return 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)';\n    default:\n      return 'linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)';\n  }\n}\n\n// 获取标签文本\nfunction getTagText(type) {\n  switch (type) {\n    case 'product':\n      return '商品';\n    case 'activity':\n      return '活动';\n    case 'coupon':\n      return '优惠券';\n    case 'group':\n      return '拼团';\n    case 'flash':\n      return '秒杀';\n    default:\n      return '其他';\n  }\n}\n</script>\n\n<style scoped>\n.favorites-container {\n  min-height: 100vh;\n  background-color: #F8F8F8;\n}\n\n.back-btn:active, .edit-btn:active, .category-item:active, .action-btn:active {\n  opacity: 0.8;\n}\n\n.favorite-item {\n  transition: transform 0.3s ease;\n}\n\n.favorite-item:active {\n  transform: scale(0.98);\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/favorites/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni"], "mappings": ";;;;;;;;;;;AA6YA,UAAM,aAAaA,cAAAA,IAAI,KAAK;AAG5B,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,MACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,MACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,MACrB,EAAE,IAAI,GAAG,MAAM,MAAO;AAAA,MACtB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,MACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,IACvB,CAAC;AACD,UAAM,kBAAkBA,cAAAA,IAAI,CAAC;AAG7B,UAAM,UAAUA,cAAAA,IAAI,KAAK;AACzB,UAAM,SAASA,cAAAA,IAAI,KAAK;AAGxB,UAAM,YAAYA,cAAAA,IAAI;AAAA,MACpB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,eAAe;AAAA,QACf,MAAM;AAAA,QACN,cAAc;AAAA,QACd,UAAU;AAAA,MACX;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,eAAe;AAAA,QACf,MAAM;AAAA,QACN,cAAc;AAAA,QACd,UAAU;AAAA,MACX;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,eAAe;AAAA,QACf,MAAM;AAAA,QACN,cAAc;AAAA,QACd,UAAU;AAAA,MACX;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,eAAe;AAAA,QACf,MAAM;AAAA,QACN,cAAc;AAAA,QACd,UAAU;AAAA,MACX;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,eAAe;AAAA,QACf,MAAM;AAAA,QACN,cAAc;AAAA,QACd,UAAU;AAAA,MACX;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,eAAe;AAAA,QACf,MAAM;AAAA,QACN,cAAc;AAAA,QACd,UAAU;AAAA,MACX;AAAA,IACH,CAAC;AAGD,UAAM,oBAAoBC,cAAQ,SAAC,MAAM;AACvC,UAAI,gBAAgB,UAAU,GAAG;AAC/B,eAAO,UAAU;AAAA,MACrB,OAAS;AACL,cAAM,cAAc;AAAA,UAClB,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,QACT;AACI,eAAO,UAAU,MAAM,OAAO,UAAQ,KAAK,SAAS,YAAY,gBAAgB,KAAK,CAAC;AAAA,MACvF;AAAA,IACH,CAAC;AAGD,UAAM,gBAAgBA,cAAQ,SAAC,MAAM;AACnC,aAAO,UAAU,MAAM,OAAO,UAAQ,KAAK,QAAQ,EAAE;AAAA,IACvD,CAAC;AAGD,UAAM,gBAAgBA,cAAQ,SAAC,MAAM;AACnC,aAAO,UAAU,MAAM,SAAS,KAAK,UAAU,MAAM,MAAM,UAAQ,KAAK,QAAQ;AAAA,IAClF,CAAC;AAGD,aAAS,SAAS;AAChBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,aAAS,iBAAiB;AACxB,iBAAW,QAAQ,CAAC,WAAW;AAG/B,UAAI,CAAC,WAAW,OAAO;AACrB,kBAAU,MAAM,QAAQ,UAAQ;AAC9B,eAAK,WAAW;AAAA,QACtB,CAAK;AAAA,MACF;AAAA,IACH;AAGA,aAAS,eAAe,OAAO;AAC7B,sBAAgB,QAAQ;AAAA,IAC1B;AAGA,aAAS,WAAW,MAAM;AAExB,UAAI,WAAW,OAAO;AACpB,qBAAa,IAAI;AACjB;AAAA,MACD;AAGD,UAAI,MAAM;AAEV,cAAO,KAAK,MAAI;AAAA,QACd,KAAK;AACH,gBAAM,wDAAwD,KAAK,EAAE;AACrE;AAAA,QACF,KAAK;AACH,gBAAM,wDAAwD,KAAK,EAAE;AACrE;AAAA,QACF,KAAK;AACH,gBAAM,yDAAyD,KAAK,EAAE;AACtE;AAAA,QACF,KAAK;AACH,gBAAM,4DAA4D,KAAK,EAAE;AACzE;AAAA,QACF,KAAK;AACH,gBAAM,6DAA6D,KAAK,EAAE;AAC1E;AAAA,QACF;AACE,gBAAM,wDAAwD,KAAK,EAAE;AAAA,MACxE;AAEDA,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IACxB;AAGA,aAAS,aAAa,MAAM;AAC1B,WAAK,WAAW,CAAC,KAAK;AAAA,IACxB;AAGA,aAAS,kBAAkB;AACzB,YAAM,WAAW,CAAC,cAAc;AAChC,gBAAU,MAAM,QAAQ,UAAQ;AAC9B,aAAK,WAAW;AAAA,MACpB,CAAG;AAAA,IACH;AAGA,aAAS,cAAc;AACrB,UAAI,cAAc,UAAU,GAAG;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAEDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,WAAW,cAAc,KAAK;AAAA,QACvC,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEf,sBAAU,QAAQ,UAAU,MAAM,OAAO,UAAQ,CAAC,KAAK,QAAQ;AAE/DA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAGD,gBAAI,UAAU,MAAM,WAAW,GAAG;AAChC,yBAAW,QAAQ;AAAA,YACpB;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,aAAS,eAAe,MAAM;AAC5BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEf,kBAAM,QAAQ,UAAU,MAAM,UAAU,SAAO,IAAI,OAAO,KAAK,EAAE;AACjE,gBAAI,UAAU,IAAI;AAChB,wBAAU,MAAM,OAAO,OAAO,CAAC;AAAA,YAChC;AAEDA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,aAAS,YAAY;AACnBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,aAAS,WAAW;AAClB,UAAI,QAAQ,SAAS,OAAO;AAAO;AAEnC,cAAQ,QAAQ;AAGhB,iBAAW,MAAM;AAGf,eAAO,QAAQ;AACf,gBAAQ,QAAQ;AAAA,MACjB,GAAE,IAAI;AAAA,IACT;AAGA,aAAS,iBAAiB,MAAM;AAC9B,cAAQ,MAAI;AAAA,QACV,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,aAAS,WAAW,MAAM;AACxB,cAAQ,MAAI;AAAA,QACV,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChrBA,GAAG,WAAW,eAAe;"}