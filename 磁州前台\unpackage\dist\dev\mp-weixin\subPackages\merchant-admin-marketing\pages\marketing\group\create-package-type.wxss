/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.create-package-type-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  display: flex;
  flex-direction: column;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #fff;
}

/* 步骤指示器 */
.step-indicator {
  padding: 15px;
  background: #FFFFFF;
}
.step-progress {
  height: 4px;
  background-color: #EBEDF5;
  border-radius: 2px;
  margin-bottom: 5px;
  position: relative;
}
.step-progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #9040FF, #5E35B1);
  border-radius: 2px;
}
.step-text {
  font-size: 12px;
  color: #999;
  text-align: right;
}

/* 页面内容 */
.page-content {
  flex: 1;
  padding: 20px 15px;
}
.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}
.page-subtitle {
  font-size: 14px;
  color: #999;
  margin-bottom: 20px;
}

/* 套餐类型样式 */
.package-types {
  margin-bottom: 20px;
}
.package-type {
  display: flex;
  align-items: center;
  background: #FFFFFF;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  position: relative;
}
.package-type.active {
  border: 2px solid #9040FF;
}
.type-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  margin-right: 15px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 24px;
}
.food-icon {
  background-color: #FFE8CC;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FF9500"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5-9h10v2H7z"/></svg>');
}
.service-icon {
  background-color: #E3F2FD;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%230A84FF"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5-9h10v2H7z"/></svg>');
}
.product-icon {
  background-color: #E8F5E9;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2334C759"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5-9h10v2H7z"/></svg>');
}
.custom-icon {
  background-color: #F0E8FF;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%239040FF"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5-9h10v2H7z"/></svg>');
}
.type-info {
  flex: 1;
}
.type-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  display: block;
}
.type-desc {
  font-size: 12px;
  color: #999;
}
.type-check {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #9040FF;
  position: absolute;
  top: 15px;
  right: 15px;
}
.type-check::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 10px;
  height: 5px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: translate(-50%, -60%) rotate(-45deg);
}

/* 设置区域样式 */
.settings-section {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px dashed #EBEDF5;
}
.setting-item {
  margin-bottom: 15px;
}
.setting-item:last-child {
  margin-bottom: 0;
}
.setting-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}
.setting-options {
  display: flex;
  gap: 15px;
}
.option {
  flex: 1;
  height: 40px;
  background: #F5F7FA;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #333;
  position: relative;
  border: 1px solid #EBEDF5;
}
.option.active {
  background: #F0E8FF;
  border-color: #9040FF;
  color: #9040FF;
}
.option-check {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #9040FF;
  position: absolute;
  top: -8px;
  right: -8px;
}
.option-check::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8px;
  height: 4px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: translate(-50%, -60%) rotate(-45deg);
}
.setting-input {
  display: flex;
  align-items: center;
}
.days-input, .times-input {
  width: 60px;
  height: 40px;
  background: #F5F7FA;
  border-radius: 8px;
  border: 1px solid #EBEDF5;
  text-align: center;
  font-size: 14px;
  color: #333;
  margin-right: 10px;
}
.unit {
  font-size: 14px;
  color: #666;
}

/* 底部按钮 */
.footer-buttons {
  padding: 15px;
  background: #FFFFFF;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  gap: 15px;
}
.btn {
  flex: 1;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  border: none;
}
.btn-primary {
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #FFFFFF;
}
.btn-secondary {
  background: #F5F7FA;
  color: #666;
  border: 1px solid #EBEDF5;
}