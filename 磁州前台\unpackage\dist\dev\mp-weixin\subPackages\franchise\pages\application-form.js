"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Array) {
  const _component_cu_custom = common_vendor.resolveComponent("cu-custom");
  _component_cu_custom();
}
const _sfc_main = {
  __name: "application-form",
  setup(__props) {
    const selectedRegion = common_vendor.ref("");
    const formData = common_vendor.reactive({
      name: "",
      phone: "",
      wechat: "",
      companyName: "",
      businessLicense: "",
      companyAddress: "",
      staffCount: "",
      resources: [],
      merchantResources: "",
      advantages: "",
      agreement: false
    });
    const errors = common_vendor.reactive({});
    const submitting = common_vendor.ref(false);
    const staffOptions = ["1-3人", "4-10人", "11-20人", "20人以上"];
    const resourceOptions = [
      { label: "本地商家资源", value: "merchant" },
      { label: "媒体资源", value: "media" },
      { label: "政府资源", value: "government" },
      { label: "协会/商会资源", value: "association" },
      { label: "高校/培训机构资源", value: "education" }
    ];
    common_vendor.onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      if (options.region) {
        selectedRegion.value = decodeURIComponent(options.region);
      }
    });
    const validateField = (field) => {
      switch (field) {
        case "name":
          errors.name = formData.name ? "" : "请输入姓名";
          break;
        case "phone":
          if (!formData.phone) {
            errors.phone = "请输入手机号码";
          } else if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
            errors.phone = "请输入正确的手机号码";
          } else {
            errors.phone = "";
          }
          break;
        case "wechat":
          errors.wechat = formData.wechat ? "" : "请输入微信号";
          break;
        case "companyName":
          errors.companyName = formData.companyName ? "" : "请输入公司名称";
          break;
        case "businessLicense":
          errors.businessLicense = formData.businessLicense ? "" : "请上传营业执照";
          break;
        case "staffCount":
          errors.staffCount = formData.staffCount ? "" : "请选择运营人数";
          break;
        case "resources":
          errors.resources = formData.resources.length > 0 ? "" : "请至少选择一项本地资源";
          break;
        case "agreement":
          errors.agreement = formData.agreement ? "" : "请同意协议后提交";
          break;
      }
    };
    const validateForm = () => {
      validateField("name");
      validateField("phone");
      validateField("wechat");
      validateField("companyName");
      validateField("businessLicense");
      validateField("staffCount");
      validateField("resources");
      validateField("agreement");
      for (const key in errors) {
        if (errors[key]) {
          return false;
        }
      }
      return true;
    };
    const staffChange = (e) => {
      const index = e.detail.value;
      formData.staffCount = staffOptions[index];
      validateField("staffCount");
    };
    const toggleResource = (value) => {
      const index = formData.resources.indexOf(value);
      if (index === -1) {
        formData.resources.push(value);
      } else {
        formData.resources.splice(index, 1);
      }
      validateField("resources");
    };
    const chooseBusinessLicense = () => {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          formData.businessLicense = res.tempFilePaths[0];
          validateField("businessLicense");
        }
      });
    };
    const toggleAgreement = () => {
      formData.agreement = !formData.agreement;
      validateField("agreement");
    };
    const showAgreement = () => {
      common_vendor.index.navigateTo({
        url: "/subPackages/franchise/pages/agreement"
      });
    };
    const submitApplication = () => {
      if (submitting.value)
        return;
      if (!validateForm()) {
        common_vendor.index.showToast({
          title: "请完善表单信息",
          icon: "none"
        });
        return;
      }
      submitting.value = true;
      setTimeout(() => {
        submitting.value = false;
        common_vendor.index.showModal({
          title: "申请提交成功",
          content: "您的区域加盟申请已提交，我们将在3个工作日内审核并联系您。",
          showCancel: false,
          success: () => {
            common_vendor.index.navigateBack();
          }
        });
      }, 1500);
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          bgColor: "bg-gradient-blue",
          isBack: true
        }),
        b: common_vendor.t(selectedRegion.value),
        c: common_vendor.o([($event) => formData.name = $event.detail.value, ($event) => validateField("name")]),
        d: formData.name,
        e: errors.name
      }, errors.name ? {
        f: common_vendor.t(errors.name)
      } : {}, {
        g: common_vendor.o([($event) => formData.phone = $event.detail.value, ($event) => validateField("phone")]),
        h: formData.phone,
        i: errors.phone
      }, errors.phone ? {
        j: common_vendor.t(errors.phone)
      } : {}, {
        k: common_vendor.o([($event) => formData.wechat = $event.detail.value, ($event) => validateField("wechat")]),
        l: formData.wechat,
        m: errors.wechat
      }, errors.wechat ? {
        n: common_vendor.t(errors.wechat)
      } : {}, {
        o: common_vendor.o([($event) => formData.companyName = $event.detail.value, ($event) => validateField("companyName")]),
        p: formData.companyName,
        q: errors.companyName
      }, errors.companyName ? {
        r: common_vendor.t(errors.companyName)
      } : {}, {
        s: formData.businessLicense
      }, formData.businessLicense ? {
        t: formData.businessLicense
      } : {}, {
        v: common_vendor.o(chooseBusinessLicense),
        w: errors.businessLicense
      }, errors.businessLicense ? {
        x: common_vendor.t(errors.businessLicense)
      } : {}, {
        y: formData.companyAddress,
        z: common_vendor.o(($event) => formData.companyAddress = $event.detail.value),
        A: common_vendor.t(formData.staffCount || "请选择运营团队人数"),
        B: staffOptions,
        C: common_vendor.o(staffChange),
        D: errors.staffCount
      }, errors.staffCount ? {
        E: common_vendor.t(errors.staffCount)
      } : {}, {
        F: common_vendor.f(resourceOptions, (item, index, i0) => {
          return {
            a: common_vendor.n(formData.resources.includes(item.value) ? "cuIcon-squarecheckfill text-blue" : "cuIcon-square"),
            b: common_vendor.t(item.label),
            c: index,
            d: common_vendor.o(($event) => toggleResource(item.value), index),
            e: formData.resources.includes(item.value) ? 1 : ""
          };
        }),
        G: errors.resources
      }, errors.resources ? {
        H: common_vendor.t(errors.resources)
      } : {}, {
        I: formData.merchantResources,
        J: common_vendor.o(($event) => formData.merchantResources = $event.detail.value),
        K: common_vendor.t(formData.merchantResources.length),
        L: formData.advantages,
        M: common_vendor.o(($event) => formData.advantages = $event.detail.value),
        N: common_vendor.t(formData.advantages.length),
        O: common_vendor.n(formData.agreement ? "cuIcon-squarecheckfill text-blue" : "cuIcon-square"),
        P: common_vendor.o(toggleAgreement),
        Q: common_vendor.o(showAgreement),
        R: errors.agreement
      }, errors.agreement ? {
        S: common_vendor.t(errors.agreement)
      } : {}, {
        T: !submitting.value
      }, !submitting.value ? {} : {}, {
        U: common_vendor.o(submitApplication),
        V: submitting.value
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-b3ec23a4"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/franchise/pages/application-form.js.map
