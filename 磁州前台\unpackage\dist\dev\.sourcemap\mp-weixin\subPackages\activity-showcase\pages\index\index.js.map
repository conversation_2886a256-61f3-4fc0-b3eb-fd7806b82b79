{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/index/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcaW5kZXhcaW5kZXgudnVl"], "sourcesContent": ["<template>\n  <view class=\"activity-showcase-container\">\n    <!-- 可滚动的弧形背景 -->\n    <view class=\"content-bg-scroll\"></view>\n    \n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\">\n      <view class=\"navbar-content\">\n        <view class=\"back-btn\" @click=\"goBack\">\n          <image class=\"back-icon\" src=\"/static/images/tabbar/最新返回键.png\" mode=\"aspectFit\"></image>\n        </view>\n        <view class=\"navbar-title\">{{ navbarTitle }}</view>\n        <view class=\"navbar-right\">\n          <view class=\"close-btn\" @click=\"closeActivityCenter\">\n            <text class=\"close-icon\"></text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 内容区域 -->\n    <scroll-view \n      class=\"content-scroll\" \n      scroll-y \n      @scrolltolower=\"loadMore\"\n      :scroll-anchoring=\"true\"\n      :enhanced=\"true\"\n      :bounces=\"true\"\n      :show-scrollbar=\"false\"\n    >\n      <!-- 顶部轮播图 -->\n      <view class=\"banner-outer\">\n        <swiper\n          class=\"banner-swiper\"\n          indicator-dots\n          :autoplay=\"swiperConfig.autoplay\"\n          :circular=\"swiperConfig.circular\"\n          :indicator-color=\"swiperConfig.indicatorColor\"\n          :indicator-active-color=\"swiperConfig.indicatorActiveColor\"\n          :interval=\"swiperConfig.interval\"\n          :duration=\"swiperConfig.duration\"\n        >\n        <swiper-item v-for=\"(banner, index) in banners\" :key=\"index\">\n          <view class=\"banner-item\">\n            <image class=\"banner-image\" :src=\"banner.image\" mode=\"aspectFill\" :lazy-load=\"true\"></image>\n            <view class=\"banner-info\">\n              <text class=\"banner-title\">{{ banner.title }}</text>\n              <text class=\"banner-desc\">{{ banner.description }}</text>\n            </view>\n          </view>\n        </swiper-item>\n      </swiper>\n      </view>\n      \n      <!-- 白色内容区域，包含所有剩余内容 -->\n      <view class=\"white-content\">\n      <!-- 活动分类导航 -->\n      <view class=\"category-nav\">\n          <view \n            class=\"category-item\" \n            v-for=\"(category, index) in categories\" \n            :key=\"index\" \n            @click=\"navigateToCategory(category.type)\"\n          >\n          <view class=\"category-icon\" :class=\"[`icon-${category.type}`]\">\n              <svg v-if=\"category.type === 'flash'\" class=\"icon\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\">\n                <path d=\"M13 3L4 14h7v7l9-11h-7V3z\" fill=\"currentColor\"></path>\n            </svg>\n              <svg v-else-if=\"category.type === 'group'\" class=\"icon\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\">\n                <path d=\"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2M9 7a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM23 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75\" fill=\"currentColor\"></path>\n            </svg>\n              <svg v-else-if=\"category.type === 'discount'\" class=\"icon\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\">\n                <path d=\"M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\" fill=\"currentColor\"></path>\n            </svg>\n              <svg v-else-if=\"category.type === 'coupon'\" class=\"icon\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\">\n                <path d=\"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z\" fill=\"currentColor\"></path>\n            </svg>\n          </view>\n          <text class=\"category-name\">{{ category.name }}</text>\n        </view>\n      </view>\n      \n      <!-- 同城活动模块 -->\n        <ActivityCenter />\n      \n      <!-- 加载更多提示 -->\n      <view class=\"loading-more\" v-if=\"loading\">\n        <text>加载中...</text>\n      </view>\n      \n      <!-- 到底了提示 -->\n      <view class=\"no-more\" v-if=\"noMore\">\n        <text>已经到底啦~</text>\n      </view>\n      \n      <!-- 底部安全区域 -->\n      <view class=\"safe-area-bottom\"></view>\n      </view>\n    </scroll-view>\n    \n    <!-- 底部导航栏 -->\n    <view class=\"tabbar\">\n      <view \n        class=\"tabbar-item\" \n        :class=\"{active: currentTab === 'home'}\" \n        @click=\"switchTab('home')\"\n        data-tab=\"home\"\n      >\n        <view class=\"tab-icon home\"></view>\n        <text class=\"tabbar-text\">首页</text>\n        </view>\n      <view \n        class=\"tabbar-item\" \n        :class=\"{active: currentTab === 'discover'}\" \n        @click=\"switchTab('discover')\"\n        data-tab=\"discover\"\n      >\n        <view class=\"tab-icon discover\"></view>\n        <text class=\"tabbar-text\">本地商城</text>\n      </view>\n      <view \n        class=\"tabbar-item\" \n        :class=\"{active: currentTab === 'distribution'}\" \n        @click=\"switchTab('distribution')\"\n        data-tab=\"distribution\"\n      >\n        <view class=\"tab-icon distribution\"></view>\n        <text class=\"tabbar-text\">分销</text>\n      </view>\n      <view \n        class=\"tabbar-item\" \n        :class=\"{active: currentTab === 'message'}\" \n        @click=\"switchTab('message')\"\n        data-tab=\"message\"\n      >\n        <view class=\"tab-icon message\">\n          <view class=\"badge\" v-if=\"unreadMessageCount > 0\">{{ unreadMessageCount > 99 ? '99+' : unreadMessageCount }}</view>\n        </view>\n        <text class=\"tabbar-text\">消息</text>\n      </view>\n            <view \n        class=\"tabbar-item\" \n        :class=\"{active: currentTab === 'my'}\" \n        @click=\"switchTab('my')\"\n        data-tab=\"my\"\n      >\n        <view class=\"tab-icon user\"></view>\n        <text class=\"tabbar-text\">我的</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted, computed } from 'vue';\nimport ActivityCenter from '../../components/activity/ActivityCenter.vue';\nimport configManager from '../../utils/configManager.js';\n\n// 当前选中的标签页\nconst currentTab = ref('home');\n\n// 未读消息数量\nconst unreadMessageCount = ref(3);\n\n// 轮播图数据\nconst banners = ref([\n  {\n    image: '/static/images/activity/banner-1.jpg',\n          title: '618年中大促',\n          description: '全场低至5折起'\n        },\n        {\n    image: '/static/images/activity/banner-2.jpg',\n          title: '新人专享礼',\n          description: '首单立减30元'\n        },\n        {\n    image: '/static/images/activity/banner-3.jpg',\n          title: '周末狂欢趴',\n          description: '满200减50'\n        }\n]);\n\n// 分类导航数据\nconst categories = ref([\n        { name: '限时秒杀', type: 'flash' },\n        { name: '拼团活动', type: 'group' },\n        { name: '满减优惠', type: 'discount' },\n        { name: '优惠券', type: 'coupon' }\n]);\n\n// 加载状态\nconst loading = ref(false);\nconst noMore = ref(false);\n\n// 配置相关的计算属性\nconst navbarConfig = computed(() => configManager.getConfig('structure.navbar') || {})\nconst bannerConfig = computed(() => configManager.getConfig('structure.banner') || {})\nconst cardConfig = computed(() => configManager.getConfig('display.cardStyle') || {})\n\n// 动态导航栏标题\nconst navbarTitle = computed(() => navbarConfig.value.title || '活动中心')\n\n// 动态轮播图配置\nconst swiperConfig = computed(() => ({\n  autoplay: bannerConfig.value.autoplay !== false,\n  circular: bannerConfig.value.circular !== false,\n  interval: bannerConfig.value.interval || 5000,\n  duration: bannerConfig.value.duration || 500,\n  indicatorColor: bannerConfig.value.indicatorColor || 'rgba(255, 255, 255, 0.6)',\n  indicatorActiveColor: bannerConfig.value.indicatorActiveColor || '#ffffff'\n}))\n\n// 页面加载\nonMounted(async () => {\n  console.log('活动中心页面加载');\n\n  // 初始化配置管理器\n  try {\n    await configManager.init()\n    console.log('配置管理器初始化成功')\n  } catch (error) {\n    console.warn('配置管理器初始化失败，使用默认配置:', error)\n  }\n\n  // 获取未读消息数量\n  getUnreadMessageCount();\n});\n\n// 返回上一页\nfunction goBack() {\n  uni.navigateBack();\n}\n\n// 关闭活动中心\nfunction closeActivityCenter() {\n  uni.navigateBack({\n    delta: 1\n  });\n}\n    \n    // 导航到分类页面\nfunction navigateToCategory(type) {\n      let url = '';\n      \n      switch(type) {\n        case 'flash':\n      url = '/subPackages/activity-showcase/pages/flash-sale/index';\n          break;\n        case 'group':\n      url = '/subPackages/activity-showcase/pages/group-buy/index';\n          break;\n        case 'discount':\n      url = '/subPackages/activity-showcase/pages/discount/index';\n          break;\n        case 'coupon':\n      url = '/subPackages/activity-showcase/pages/coupon/index';\n          break;\n        default:\n      url = '/subPackages/activity-showcase/pages/index/index';\n      }\n      \n      uni.navigateTo({ url });\n}\n    \n    // 切换底部导航标签页\nfunction switchTab(tab) {\n  if (currentTab.value === tab) return;\n      \n  currentTab.value = tab;\n      \n      // 根据选中的标签页进行相应的导航\n      switch(tab) {\n        case 'home':\n          // 已在首页，不需要导航\n          break;\n        case 'discover':\n          uni.navigateTo({\n            url: '/subPackages/activity-showcase/pages/discover/index'\n          });\n          break;\n        case 'distribution':\n          uni.navigateTo({\n            url: '/subPackages/activity-showcase/pages/distribution/index'\n          });\n          break;\n        case 'message':\n          uni.navigateTo({\n            url: '/subPackages/activity-showcase/pages/message/index'\n          });\n          break;\n        case 'my':\n          uni.navigateTo({\n            url: '/subPackages/activity-showcase/pages/my/index'\n          });\n          break;\n      }\n}\n    \n    // 获取未读消息数量\nfunction getUnreadMessageCount() {\n      // 这里应该是从服务器获取未读消息数量\n      // 示例中使用模拟数据\n      setTimeout(() => {\n    unreadMessageCount.value = Math.floor(Math.random() * 10);\n      }, 1000);\n}\n\n// 加载更多\nfunction loadMore() {\n  if (loading.value || noMore.value) return;\n  \n  loading.value = true;\n  \n  // 模拟加载更多数据\n      setTimeout(() => {\n    // 这里可以根据实际情况添加更多数据\n    // 示例中假设已经没有更多数据了\n    noMore.value = true;\n    loading.value = false;\n  }, 1500);\n}\n</script>\n\n<style scoped>\n.activity-showcase-container {\n  min-height: 100vh;\n  background-color: #f7f7f7;\n  padding-bottom: env(safe-area-inset-bottom);\n  position: relative;\n}\n\n/* 可滚动的弧形背景 */\n.content-bg-scroll {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 400rpx;\n  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);\n  border-bottom-left-radius: 80rpx;\n  border-bottom-right-radius: 80rpx;\n  z-index: 1;\n}\n\n/* 自定义导航栏 */\n.custom-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: calc(var(--status-bar-height, 25px) + 64px);\n  width: 100%;\n  z-index: 100;\n  padding-top: var(--status-bar-height, 25px);\n  box-sizing: border-box;\n  }\n  \n  .navbar-content {\n    position: relative;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n  height: 64px;\n    padding-left: 30rpx;\n    padding-right: 30rpx;\n    box-sizing: border-box;\n  }\n  \n  .back-btn {\n  width: 60rpx;\n  height: 60rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  color: #ffffff;\n    transition: all 0.2s ease;\n  position: relative;\n  z-index: 10;\n}\n    \n.back-btn:active {\n      transform: scale(0.9);\n      background: rgba(255, 255, 255, 0.3);\n  }\n  \n  .navbar-title {\n    font-size: 36rpx;\n  font-weight: 600;\n    color: #FFFFFF;\n    position: absolute;\n    left: 50%;\n    transform: translateX(-50%);\n    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n}\n\n.navbar-right {\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n  width: 60rpx; /* 限制宽度，确保不会有额外空间 */\n}\n\n.close-btn {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #ffffff;\n  transition: all 0.2s ease;\n}\n\n.close-btn:active {\n  transform: scale(0.9);\n  background: rgba(255, 255, 255, 0.3);\n}\n\n.close-icon {\n  font-size: 40rpx;\n  font-weight: 300;\n}\n\n.back-icon {\n  width: 50rpx;\n  height: 50rpx;\n}\n\n/* 内容区域 */\n.content-scroll {\n  position: absolute;\n  top: calc(var(--status-bar-height, 25px) + 64px);\n  left: 0;\n  right: 0;\n  bottom: 100rpx;\n  background-color: transparent;\n  z-index: 2;\n}\n\n/* 轮播图 */\n.banner-outer {\n  position: relative;\n  margin: 25rpx 30rpx 50rpx;\n  border-radius: 30rpx;\n  overflow: hidden;\n  box-shadow: 0 25rpx 45rpx rgba(0, 0, 0, 0.35), 0 15rpx 25rpx rgba(0, 0, 0, 0.2);\n  border: 12rpx solid #ffffff;\n  transform: translateZ(0);\n  z-index: 3;\n  animation: float 6s ease-in-out infinite;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  padding: 2rpx;\n  background-color: rgba(255, 255, 255, 0.8);\n}\n\n@keyframes float {\n  0% { transform: translateY(0) translateZ(0); }\n  50% { transform: translateY(-10rpx) translateZ(0); }\n  100% { transform: translateY(0) translateZ(0); }\n}\n\n.banner-swiper {\n  width: 100%;\n  height: 300rpx;\n  border-radius: 16rpx;\n  overflow: hidden;\n  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);\n}\n  \n  .banner-item {\n    position: relative;\n    width: 100%;\n    height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: #FFFFFF;\n}\n    \n    .banner-image {\n      width: 100%;\n      height: 100%;\n  border-radius: 16rpx;\n  box-shadow: inset 0 0 15rpx rgba(0, 0, 0, 0.15);\n  transition: transform 0.3s ease;\n    }\n    \n    .banner-info {\n      position: absolute;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      padding: 30rpx 25rpx;\n      background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0));\n  border-radius: 0 0 16rpx 16rpx;\n      backdrop-filter: blur(10rpx);\n}\n      \n      .banner-title {\n        font-size: 32rpx;\n        font-weight: 700;\n        color: #FFFFFF;\n        margin-bottom: 8rpx;\n        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);\n      }\n      \n      .banner-desc {\n        font-size: 24rpx;\n        color: rgba(255, 255, 255, 0.9);\n        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);\n      }\n\n/* 白色内容区域 */\n.white-content {\n  background: #f8f9fc;\n  position: relative;\n  padding-top: 40rpx;\n  padding-bottom: 20rpx;\n  border-top-left-radius: 24rpx;\n  border-top-right-radius: 24rpx;\n  box-shadow: 0 -6rpx 20rpx rgba(0, 0, 0, 0.03);\n  margin-top: -40rpx;\n  z-index: 4;\n}\n\n/* 活动分类导航 */\n.category-nav {\n  display: flex;\n  justify-content: space-around;\n  padding: 15rpx 15rpx; /* 从25rpx减少到15rpx */\n  background: rgba(255, 255, 255, 0.9);\n  margin: 0 18rpx 20rpx; /* 修改上边距为0 */\n  border-radius: 35rpx;\n  box-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.1), \n              0 5rpx 15rpx rgba(0, 0, 0, 0.05),\n              inset 0 1rpx 0 rgba(255, 255, 255, 0.8);\n  backdrop-filter: blur(10rpx);\n  border: 1rpx solid rgba(255, 255, 255, 0.8);\n  position: relative;\n  overflow: hidden;\n}\n  \n.category-nav::before {\n    content: '';\n    position: absolute;\n    top: -50%;\n    left: -50%;\n    width: 200%;\n    height: 200%;\n    background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);\n    z-index: 0;\n  }\n  \n  .category-item {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    padding: 10rpx 0; /* 从15rpx减少到10rpx */\n    width: 25%;\n    transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n    position: relative;\n    z-index: 1;\n}\n    \n.category-item:active {\n      transform: scale(0.92);\n    }\n    \n    .category-icon {\n      width: 80rpx; /* 从90rpx减少到80rpx */\n      height: 80rpx; /* 从90rpx减少到80rpx */\n      border-radius: 24rpx; /* 修改：从16rpx增加到24rpx，使圆角更大 */\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      margin-bottom: 8rpx; /* 从12rpx减少到8rpx */\n      box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.2),\n                  inset 0 2rpx 3rpx rgba(255, 255, 255, 0.5);\n      position: relative;\n      overflow: hidden;\n      color: #ffffff;\n}\n\n.category-icon .icon {\n      /* 移除旋转效果 */\n}\n      \n.category-icon::after {\n        content: '';\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        height: 50%;\n        background: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));\n        border-radius: 24rpx 24rpx 0 0; /* 保持与正方形圆角一致，从16rpx更新为24rpx */\n      }\n      \n.icon-flash {\n        background: linear-gradient(135deg, #FF453A, #FF2D55);\n      }\n      \n.icon-group {\n        background: linear-gradient(135deg, #34C759, #30D158);\n      }\n      \n.icon-discount {\n        background: linear-gradient(135deg, #5E5CE6, #5856D6);\n      }\n      \n.icon-coupon {\n        background: linear-gradient(135deg, #FF9F0A, #FF9500);\n    }\n    \n    .category-name {\n      font-size: 24rpx; /* 从26rpx减少到24rpx */\n      color: #333333;\n      font-weight: 600;\n      margin-top: 3rpx; /* 从5rpx减少到3rpx */\n      text-shadow: 0 1rpx 0 rgba(255, 255, 255, 0.8);\n}\n\n/* 加载更多和到底了提示 */\n.loading-more, .no-more {\n  text-align: center;\n  padding: 20rpx 0;\n  font-size: 24rpx;\n  color: #8E8E93;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.loading-more::before {\n    content: '';\n    width: 28rpx;\n    height: 28rpx;\n    border: 3rpx solid #8E8E93;\n    border-top-color: transparent;\n    border-radius: 50%;\n    margin-right: 8rpx;\n    animation: loading 0.8s linear infinite;\n}\n\n@keyframes loading {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n/* 底部安全区域 */\n.safe-area-bottom {\n  height: env(safe-area-inset-bottom);\n}\n\n/* 底部导航栏 */\n.tabbar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 100rpx;\n  background: #FFFFFF;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  display: flex;\n  justify-content: space-around;\n  align-items: center;\n  padding-bottom: env(safe-area-inset-bottom);\n  z-index: 99;\n  border-top: 1rpx solid #EEEEEE;\n}\n  \n  .tabbar-item {\n  flex: 1;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    height: 100%;\n  padding: 6px 0;\n  box-sizing: border-box;\n    position: relative;\n}\n    \n.tabbar-item:active {\n      transform: scale(0.9);\n    }\n    \n.tabbar-item.active .tab-icon {\n        transform: translateY(-5rpx);\n      }\n      \n.tabbar-item.active .tabbar-text {\n  color: #FF3B69;\n        font-weight: 600;\n        transform: translateY(-2rpx);\n}\n    \n.tab-icon {\n  width: 24px;\n  height: 24px;\n  margin-bottom: 4px;\n  color: #999999;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n  background-size: contain;\n  background-position: center;\n  background-repeat: no-repeat;\n      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n}\n\n/* 首页图标 */\n.tab-icon.home {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E\");\n}\n\n.tabbar-item.active[data-tab=\"home\"] .tab-icon.home {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E\");\n}\n\n/* 发现图标 */\n.tab-icon.discover {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5.5-2.5l7.51-3.49L17.5 6.5 9.99 9.99 6.5 17.5zm5.5-6.6c.61 0 1.1.49 1.1 1.1s-.49 1.1-1.1 1.1-1.1-.49-1.1-1.1.49-1.1 1.1-1.1z'/%3E%3C/svg%3E\");\n}\n\n.tabbar-item.active[data-tab=\"discover\"] .tab-icon.discover {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5.5-2.5l7.51-3.49L17.5 6.5 9.99 9.99 6.5 17.5zm5.5-6.6c.61 0 1.1.49 1.1 1.1s-.49 1.1-1.1 1.1-1.1-.49-1.1-1.1.49-1.1 1.1-1.1z'/%3E%3C/svg%3E\");\n}\n\n/* 分销图标 */\n.tab-icon.distribution {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E\");\n}\n\n.tabbar-item.active[data-tab=\"distribution\"] .tab-icon.distribution {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E\");\n}\n\n/* 消息图标 */\n.tab-icon.message {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H5.17L4 17.17V4h16v12z'/%3E%3C/svg%3E\");\n}\n\n.tabbar-item.active[data-tab=\"message\"] .tab-icon.message {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E\");\n}\n\n/* 我的图标 */\n.tab-icon.user {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E\");\n}\n\n.tabbar-item.active[data-tab=\"my\"] .tab-icon.user {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E\");\n}\n\n.tabbar-item.active .tab-icon {\n  filter: drop-shadow(0 1px 2px rgba(255, 59, 105, 0.3));\n      }\n      \n      .badge {\n        position: absolute;\n        top: -8rpx;\n        right: -12rpx;\n        min-width: 32rpx;\n        height: 32rpx;\n        border-radius: 16rpx;\n        background: linear-gradient(135deg, #FF453A, #FF2D55);\n        color: #FFFFFF;\n        font-size: 18rpx;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        padding: 0 6rpx;\n        box-sizing: border-box;\n        font-weight: 600;\n        box-shadow: 0 2rpx 6rpx rgba(255, 45, 85, 0.3);\n        border: 1rpx solid rgba(255, 255, 255, 0.8);\n        transform: scale(0.9);\n    }\n    \n    .tabbar-text {\n      font-size: 22rpx;\n      color: #8E8E93;\n      margin-top: 2rpx;\n      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n    }\n    \n.tabbar-item::after {\n      content: '';\n      position: absolute;\n      bottom: 0;\n      left: 50%;\n      transform: translateX(-50%) scaleX(0);\n      width: 30rpx;\n      height: 4rpx;\n  background: #FF3B69;\n      border-radius: 2rpx;\n      transition: transform 0.3s ease;\n    }\n    \n.tabbar-item.active::after {\n      transform: translateX(-50%) scaleX(1);\n    }\n</style>\n\n \n ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "config<PERSON><PERSON><PERSON>", "onMounted", "uni"], "mappings": ";;;;;;;;;;;;AA2JA,MAAM,iBAAiB,MAAW;;;;AAIlC,UAAM,aAAaA,cAAAA,IAAI,MAAM;AAG7B,UAAM,qBAAqBA,cAAAA,IAAI,CAAC;AAGhC,UAAM,UAAUA,cAAAA,IAAI;AAAA,MAClB;AAAA,QACE,OAAO;AAAA,QACD,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,MACD;AAAA,QACJ,OAAO;AAAA,QACD,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,MACD;AAAA,QACJ,OAAO;AAAA,QACD,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,IACT,CAAC;AAGD,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACf,EAAE,MAAM,QAAQ,MAAM,QAAS;AAAA,MAC/B,EAAE,MAAM,QAAQ,MAAM,QAAS;AAAA,MAC/B,EAAE,MAAM,QAAQ,MAAM,WAAY;AAAA,MAClC,EAAE,MAAM,OAAO,MAAM,SAAU;AAAA,IACvC,CAAC;AAGD,UAAM,UAAUA,cAAAA,IAAI,KAAK;AACzB,UAAM,SAASA,cAAAA,IAAI,KAAK;AAGxB,UAAM,eAAeC,cAAQ,SAAC,MAAMC,iDAAAA,cAAc,UAAU,kBAAkB,KAAK,EAAE;AACrF,UAAM,eAAeD,cAAQ,SAAC,MAAMC,iDAAAA,cAAc,UAAU,kBAAkB,KAAK,EAAE;AAClED,kBAAQ,SAAC,MAAMC,iDAAAA,cAAc,UAAU,mBAAmB,KAAK,CAAA,CAAE;AAGpF,UAAM,cAAcD,cAAQ,SAAC,MAAM,aAAa,MAAM,SAAS,MAAM;AAGrE,UAAM,eAAeA,cAAQ,SAAC,OAAO;AAAA,MACnC,UAAU,aAAa,MAAM,aAAa;AAAA,MAC1C,UAAU,aAAa,MAAM,aAAa;AAAA,MAC1C,UAAU,aAAa,MAAM,YAAY;AAAA,MACzC,UAAU,aAAa,MAAM,YAAY;AAAA,MACzC,gBAAgB,aAAa,MAAM,kBAAkB;AAAA,MACrD,sBAAsB,aAAa,MAAM,wBAAwB;AAAA,IACnE,EAAE;AAGFE,kBAAAA,UAAU,YAAY;AACpBC,oBAAAA,MAAA,MAAA,OAAA,8DAAY,UAAU;AAGtB,UAAI;AACF,cAAMF,iDAAAA,cAAc,KAAM;AAC1BE,sBAAAA,MAAY,MAAA,OAAA,8DAAA,YAAY;AAAA,MACzB,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,QAAA,8DAAa,sBAAsB,KAAK;AAAA,MACzC;AAGD;IACF,CAAC;AAGD,aAAS,SAAS;AAChBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,aAAS,sBAAsB;AAC7BA,oBAAAA,MAAI,aAAa;AAAA,QACf,OAAO;AAAA,MACX,CAAG;AAAA,IACH;AAGA,aAAS,mBAAmB,MAAM;AAC5B,UAAI,MAAM;AAEV,cAAO,MAAI;AAAA,QACT,KAAK;AACP,gBAAM;AACF;AAAA,QACF,KAAK;AACP,gBAAM;AACF;AAAA,QACF,KAAK;AACP,gBAAM;AACF;AAAA,QACF,KAAK;AACP,gBAAM;AACF;AAAA,QACF;AACF,gBAAM;AAAA,MACL;AAEDA,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IAC5B;AAGA,aAAS,UAAU,KAAK;AACtB,UAAI,WAAW,UAAU;AAAK;AAE9B,iBAAW,QAAQ;AAGf,cAAO,KAAG;AAAA,QACR,KAAK;AAEH;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACjB,CAAW;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACjB,CAAW;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACjB,CAAW;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACjB,CAAW;AACD;AAAA,MACH;AAAA,IACP;AAGA,aAAS,wBAAwB;AAG3B,iBAAW,MAAM;AACnB,2BAAmB,QAAQ,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE;AAAA,MACrD,GAAE,GAAI;AAAA,IACb;AAGA,aAAS,WAAW;AAClB,UAAI,QAAQ,SAAS,OAAO;AAAO;AAEnC,cAAQ,QAAQ;AAGZ,iBAAW,MAAM;AAGnB,eAAO,QAAQ;AACf,gBAAQ,QAAQ;AAAA,MACjB,GAAE,IAAI;AAAA,IACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChUA,GAAG,WAAW,eAAe;"}