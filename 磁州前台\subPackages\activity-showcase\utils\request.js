/**
 * 磁州前台API请求工具
 * 提供统一的HTTP请求封装、拦截器、错误处理等功能
 */

// 请求配置
const REQUEST_CONFIG = {
  // 基础URL
  baseURL: 'https://api.cizhou.com',
  
  // 超时时间
  timeout: 10000,
  
  // 默认请求头
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  
  // 重试配置
  retry: {
    times: 3,
    delay: 1000
  },
  
  // 状态码配置
  statusCodes: {
    SUCCESS: 200,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    SERVER_ERROR: 500,
    NETWORK_ERROR: -1
  }
}

// 请求管理类
class RequestManager {
  constructor() {
    this.requestInterceptors = []
    this.responseInterceptors = []
    this.errorHandlers = []
    this.pendingRequests = new Map()
    this.retryQueue = []
    
    // 设置默认错误处理
    this.setupDefaultErrorHandlers()
  }
  
  // 设置默认错误处理
  setupDefaultErrorHandlers() {
    this.addErrorHandler((error) => {
      console.error('请求错误:', error)
      
      // 根据错误类型显示不同提示
      switch (error.statusCode) {
        case REQUEST_CONFIG.statusCodes.UNAUTHORIZED:
          this.handleUnauthorized()
          break
        case REQUEST_CONFIG.statusCodes.FORBIDDEN:
          uni.showToast({
            title: '访问被拒绝',
            icon: 'none'
          })
          break
        case REQUEST_CONFIG.statusCodes.NOT_FOUND:
          uni.showToast({
            title: '请求的资源不存在',
            icon: 'none'
          })
          break
        case REQUEST_CONFIG.statusCodes.SERVER_ERROR:
          uni.showToast({
            title: '服务器错误，请稍后重试',
            icon: 'none'
          })
          break
        case REQUEST_CONFIG.statusCodes.NETWORK_ERROR:
          uni.showToast({
            title: '网络连接失败',
            icon: 'none'
          })
          break
        default:
          if (error.message) {
            uni.showToast({
              title: error.message,
              icon: 'none'
            })
          }
      }
    })
  }
  
  // 处理未授权
  handleUnauthorized() {
    // 清除本地token
    uni.removeStorageSync('access_token')
    uni.removeStorageSync('refresh_token')
    
    // 跳转到登录页
    uni.reLaunch({
      url: '/pages/login/index'
    })
    
    uni.showToast({
      title: '登录已过期，请重新登录',
      icon: 'none'
    })
  }
  
  // 添加请求拦截器
  addRequestInterceptor(interceptor) {
    if (typeof interceptor === 'function') {
      this.requestInterceptors.push(interceptor)
    }
  }
  
  // 添加响应拦截器
  addResponseInterceptor(interceptor) {
    if (typeof interceptor === 'function') {
      this.responseInterceptors.push(interceptor)
    }
  }
  
  // 添加错误处理器
  addErrorHandler(handler) {
    if (typeof handler === 'function') {
      this.errorHandlers.push(handler)
    }
  }
  
  // 执行请求拦截器
  async executeRequestInterceptors(config) {
    let processedConfig = config
    
    for (const interceptor of this.requestInterceptors) {
      try {
        processedConfig = await interceptor(processedConfig)
      } catch (error) {
        console.error('请求拦截器执行失败:', error)
      }
    }
    
    return processedConfig
  }
  
  // 执行响应拦截器
  async executeResponseInterceptors(response) {
    let processedResponse = response
    
    for (const interceptor of this.responseInterceptors) {
      try {
        processedResponse = await interceptor(processedResponse)
      } catch (error) {
        console.error('响应拦截器执行失败:', error)
      }
    }
    
    return processedResponse
  }
  
  // 执行错误处理器
  executeErrorHandlers(error) {
    this.errorHandlers.forEach(handler => {
      try {
        handler(error)
      } catch (e) {
        console.error('错误处理器执行失败:', e)
      }
    })
  }
  
  // 生成请求唯一标识
  generateRequestKey(config) {
    return `${config.method}_${config.url}_${JSON.stringify(config.data || {})}`
  }
  
  // 取消重复请求
  cancelDuplicateRequest(config) {
    const requestKey = this.generateRequestKey(config)
    
    if (this.pendingRequests.has(requestKey)) {
      const existingRequest = this.pendingRequests.get(requestKey)
      existingRequest.abort()
      this.pendingRequests.delete(requestKey)
    }
    
    return requestKey
  }
  
  // 发送请求
  async request(config) {
    try {
      // 执行请求拦截器
      const processedConfig = await this.executeRequestInterceptors({
        ...REQUEST_CONFIG,
        ...config
      })
      
      // 取消重复请求
      const requestKey = this.cancelDuplicateRequest(processedConfig)
      
      // 创建请求Promise
      const requestPromise = new Promise((resolve, reject) => {
        const requestTask = uni.request({
          url: processedConfig.baseURL + processedConfig.url,
          method: processedConfig.method || 'GET',
          data: processedConfig.data,
          header: {
            ...processedConfig.headers,
            ...this.getAuthHeaders()
          },
          timeout: processedConfig.timeout,
          success: async (response) => {
            try {
              // 执行响应拦截器
              const processedResponse = await this.executeResponseInterceptors(response)
              resolve(processedResponse)
            } catch (error) {
              reject(error)
            }
          },
          fail: (error) => {
            reject({
              ...error,
              statusCode: REQUEST_CONFIG.statusCodes.NETWORK_ERROR,
              message: '网络请求失败'
            })
          },
          complete: () => {
            // 请求完成后从待处理列表中移除
            this.pendingRequests.delete(requestKey)
          }
        })
        
        // 保存请求任务
        this.pendingRequests.set(requestKey, requestTask)
      })
      
      const response = await requestPromise
      
      // 检查响应状态
      if (response.statusCode === REQUEST_CONFIG.statusCodes.SUCCESS) {
        return response.data
      } else {
        throw {
          statusCode: response.statusCode,
          message: response.data?.message || '请求失败',
          data: response.data
        }
      }
      
    } catch (error) {
      // 执行错误处理器
      this.executeErrorHandlers(error)
      
      // 重试逻辑
      if (this.shouldRetry(error, config)) {
        return this.retryRequest(config)
      }
      
      throw error
    }
  }
  
  // 获取认证头
  getAuthHeaders() {
    const token = uni.getStorageSync('access_token')
    return token ? { 'Authorization': `Bearer ${token}` } : {}
  }
  
  // 判断是否应该重试
  shouldRetry(error, config) {
    const retryConfig = config.retry || REQUEST_CONFIG.retry
    const currentRetryCount = config._retryCount || 0
    
    return (
      currentRetryCount < retryConfig.times &&
      (error.statusCode === REQUEST_CONFIG.statusCodes.NETWORK_ERROR ||
       error.statusCode >= 500)
    )
  }
  
  // 重试请求
  async retryRequest(config) {
    const retryConfig = config.retry || REQUEST_CONFIG.retry
    const currentRetryCount = config._retryCount || 0
    
    // 等待重试延迟
    await new Promise(resolve => setTimeout(resolve, retryConfig.delay))
    
    // 增加重试次数
    const newConfig = {
      ...config,
      _retryCount: currentRetryCount + 1
    }
    
    return this.request(newConfig)
  }
  
  // GET请求
  get(url, params = {}, config = {}) {
    return this.request({
      method: 'GET',
      url,
      data: params,
      ...config
    })
  }
  
  // POST请求
  post(url, data = {}, config = {}) {
    return this.request({
      method: 'POST',
      url,
      data,
      ...config
    })
  }
  
  // PUT请求
  put(url, data = {}, config = {}) {
    return this.request({
      method: 'PUT',
      url,
      data,
      ...config
    })
  }
  
  // DELETE请求
  delete(url, config = {}) {
    return this.request({
      method: 'DELETE',
      url,
      ...config
    })
  }
  
  // 上传文件
  upload(url, filePath, formData = {}, config = {}) {
    return new Promise((resolve, reject) => {
      const uploadTask = uni.uploadFile({
        url: REQUEST_CONFIG.baseURL + url,
        filePath,
        name: config.name || 'file',
        formData,
        header: {
          ...this.getAuthHeaders(),
          ...config.headers
        },
        success: (response) => {
          try {
            const data = JSON.parse(response.data)
            resolve(data)
          } catch (error) {
            resolve(response.data)
          }
        },
        fail: (error) => {
          reject(error)
        }
      })
      
      // 监听上传进度
      if (config.onProgress) {
        uploadTask.onProgressUpdate(config.onProgress)
      }
    })
  }
  
  // 下载文件
  download(url, config = {}) {
    return new Promise((resolve, reject) => {
      const downloadTask = uni.downloadFile({
        url: REQUEST_CONFIG.baseURL + url,
        header: {
          ...this.getAuthHeaders(),
          ...config.headers
        },
        success: (response) => {
          if (response.statusCode === 200) {
            resolve(response)
          } else {
            reject(response)
          }
        },
        fail: (error) => {
          reject(error)
        }
      })
      
      // 监听下载进度
      if (config.onProgress) {
        downloadTask.onProgressUpdate(config.onProgress)
      }
    })
  }
  
  // 取消所有请求
  cancelAllRequests() {
    this.pendingRequests.forEach(requestTask => {
      requestTask.abort()
    })
    this.pendingRequests.clear()
  }
  
  // 获取待处理请求数量
  getPendingRequestCount() {
    return this.pendingRequests.size
  }
}

// 创建全局请求管理器实例
const requestManager = new RequestManager()

// 添加默认请求拦截器
requestManager.addRequestInterceptor((config) => {
  // 添加时间戳防止缓存
  if (config.method === 'GET') {
    config.data = {
      ...config.data,
      _t: Date.now()
    }
  }
  
  // 添加设备信息
  const systemInfo = uni.getSystemInfoSync()
  config.headers['X-Device-Type'] = systemInfo.platform
  config.headers['X-App-Version'] = systemInfo.version
  
  return config
})

// 添加默认响应拦截器
requestManager.addResponseInterceptor((response) => {
  // 统一处理响应数据格式
  if (response.data && typeof response.data === 'object') {
    const { code, data, message } = response.data
    
    if (code !== undefined) {
      if (code === 0 || code === 200) {
        return data
      } else {
        throw {
          statusCode: code,
          message: message || '请求失败',
          data: data
        }
      }
    }
  }
  
  return response.data
})

// 导出请求管理器和便捷方法
export {
  requestManager,
  REQUEST_CONFIG
}

// 便捷方法
export const request = (config) => requestManager.request(config)
export const get = (url, params, config) => requestManager.get(url, params, config)
export const post = (url, data, config) => requestManager.post(url, data, config)
export const put = (url, data, config) => requestManager.put(url, data, config)
export const del = (url, config) => requestManager.delete(url, config)
export const upload = (url, filePath, formData, config) => requestManager.upload(url, filePath, formData, config)
export const download = (url, config) => requestManager.download(url, config)

// 默认导出
export default requestManager
