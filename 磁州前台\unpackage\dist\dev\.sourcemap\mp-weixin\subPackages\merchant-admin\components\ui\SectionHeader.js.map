{"version": 3, "file": "SectionHeader.js", "sources": ["subPackages/merchant-admin/components/ui/SectionHeader.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7Avc3ViUGFja2FnZXMvbWVyY2hhbnQtYWRtaW4vY29tcG9uZW50cy91aS9TZWN0aW9uSGVhZGVyLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"section-header\">\r\n    <view class=\"title-with-icon\">\r\n      <slot name=\"icon\">\r\n        <svg class=\"section-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n          <path d=\"M19 11H5C3.89543 11 3 11.8954 3 13V20C3 21.1046 3.89543 22 5 22H19C20.1046 22 21 21.1046 21 20V13C21 11.8954 20.1046 11 19 11Z\" stroke=\"#0A84FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n          <path d=\"M7 11V7C7 5.93913 7.42143 4.92172 8.17157 4.17157C8.92172 3.42143 9.93913 3 11 3H13C14.0609 3 15.0783 3.42143 15.8284 4.17157C16.5786 4.92172 17 5.93913 17 7V11\" stroke=\"#0A84FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n        </svg>\r\n      </slot>\r\n      <text class=\"section-title\">{{ title }}</text>\r\n    </view>\r\n    <view class=\"section-action\" v-if=\"hasAction\" @tap=\"onAction\">\r\n      <text class=\"action-text\">{{ actionText }}</text>\r\n      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n        <path d=\"M9 18L15 12L9 6\" stroke=\"#0A84FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n      </svg>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'SectionHeader',\r\n  props: {\r\n    title: {\r\n      type: String,\r\n      default: '区块标题'\r\n    },\r\n    hasAction: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    actionText: {\r\n      type: String,\r\n      default: '更多'\r\n    }\r\n  },\r\n  methods: {\r\n    onAction() {\r\n      this.$emit('action');\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding: 0 5px;\r\n}\r\n\r\n.title-with-icon {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.section-icon {\r\n  margin-right: 8px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.section-action {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #0A84FF;\r\n}\r\n\r\n.action-text {\r\n  font-size: 14px;\r\n  margin-right: 4px;\r\n}\r\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin/components/ui/SectionHeader.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;AAqBA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,WAAW;AACT,WAAK,MAAM,QAAQ;AAAA,IACrB;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzCA,GAAG,gBAAgB,SAAS;"}