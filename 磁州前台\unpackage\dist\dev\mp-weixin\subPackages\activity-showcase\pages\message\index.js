"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      currentTab: "all",
      unreadCounts: {
        system: 2,
        activity: 1,
        interaction: 3
      },
      messages: [
        {
          id: 1,
          type: "system",
          title: "系统通知",
          content: "您的账号已成功注册，欢迎使用我们的服务！",
          avatar: "https://via.placeholder.com/80x80",
          time: "2023-12-01 10:30:00",
          isRead: false
        },
        {
          id: 2,
          type: "system",
          title: "安全提醒",
          content: "您的账号于今日10:30在新设备上登录，如非本人操作，请及时修改密码。",
          avatar: "https://via.placeholder.com/80x80",
          time: "2023-12-02 15:20:00",
          isRead: false
        },
        {
          id: 3,
          type: "activity",
          title: "拼团成功",
          content: '恭喜您！您参与的"iPhone 14 Pro"拼团已成功，订单正在处理中。',
          avatar: "https://via.placeholder.com/80x80",
          time: "2023-12-03 09:15:00",
          isRead: true
        },
        {
          id: 4,
          type: "activity",
          title: "优惠券到账",
          content: "您有一张满100减20的优惠券已到账，有效期至2023-12-31。",
          avatar: "https://via.placeholder.com/80x80",
          time: "2023-12-04 14:05:00",
          isRead: false
        },
        {
          id: 5,
          type: "interaction",
          title: "新的点赞",
          content: '用户"张三"点赞了您的评论："这个产品真的很好用！"',
          avatar: "https://via.placeholder.com/80x80",
          time: "2023-12-05 11:30:00",
          isRead: false
        },
        {
          id: 6,
          type: "interaction",
          title: "新的评论",
          content: '用户"李四"回复了您的评论："谢谢分享，我也觉得不错。"',
          avatar: "https://via.placeholder.com/80x80",
          time: "2023-12-06 16:45:00",
          isRead: false
        },
        {
          id: 7,
          type: "interaction",
          title: "新的关注",
          content: '用户"王五"关注了您，去看看Ta的主页吧！',
          avatar: "https://via.placeholder.com/80x80",
          time: "2023-12-07 08:20:00",
          isRead: false
        }
      ]
    };
  },
  computed: {
    // 根据当前选中的标签过滤消息
    filteredMessages() {
      if (this.currentTab === "all") {
        return this.messages;
      } else {
        return this.messages.filter((message) => message.type === this.currentTab);
      }
    },
    // 计算总未读消息数
    totalUnreadCount() {
      return this.unreadCounts.system + this.unreadCounts.activity + this.unreadCounts.interaction;
    }
  },
  onLoad() {
    this.fetchMessages();
  },
  methods: {
    // 获取消息列表
    fetchMessages() {
      this.calculateUnreadCounts();
    },
    // 计算各类型未读消息数量
    calculateUnreadCounts() {
      const counts = {
        system: 0,
        activity: 0,
        interaction: 0
      };
      this.messages.forEach((message) => {
        if (!message.isRead) {
          counts[message.type]++;
        }
      });
      this.unreadCounts = counts;
    },
    // 切换消息标签
    switchMessageTab(tab) {
      this.currentTab = tab;
    },
    // 阅读消息
    readMessage(message) {
      if (!message.isRead) {
        message.isRead = true;
        this.calculateUnreadCounts();
      }
      common_vendor.index.navigateTo({
        url: `/subPackages/activity-showcase/pages/message/detail?id=${message.id}`
      });
    },
    // 格式化时间
    formatTime(timestamp) {
      const now = /* @__PURE__ */ new Date();
      const messageTime = new Date(timestamp);
      const diff = now - messageTime;
      if (diff < 24 * 60 * 60 * 1e3 && now.getDate() === messageTime.getDate() && now.getMonth() === messageTime.getMonth() && now.getFullYear() === messageTime.getFullYear()) {
        return messageTime.toTimeString().slice(0, 5);
      }
      const yesterday = new Date(now);
      yesterday.setDate(now.getDate() - 1);
      if (yesterday.getDate() === messageTime.getDate() && yesterday.getMonth() === messageTime.getMonth() && yesterday.getFullYear() === messageTime.getFullYear()) {
        return "昨天 " + messageTime.toTimeString().slice(0, 5);
      }
      if (now.getFullYear() === messageTime.getFullYear()) {
        return `${messageTime.getMonth() + 1}月${messageTime.getDate()}日`;
      }
      return `${messageTime.getFullYear()}/${messageTime.getMonth() + 1}/${messageTime.getDate()}`;
    },
    // 切换底部导航标签页
    switchTab(tab) {
      switch (tab) {
        case "home":
          common_vendor.index.redirectTo({
            url: "/subPackages/activity-showcase/pages/index/index"
          });
          break;
        case "discover":
          common_vendor.index.redirectTo({
            url: "/subPackages/activity-showcase/pages/discover/index"
          });
          break;
        case "distribution":
          common_vendor.index.redirectTo({
            url: "/subPackages/activity-showcase/pages/distribution/index"
          });
          break;
        case "my":
          common_vendor.index.redirectTo({
            url: "/subPackages/activity-showcase/pages/my/index"
          });
          break;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.currentTab === "all" ? 1 : "",
    b: common_vendor.o(($event) => $options.switchMessageTab("all")),
    c: $data.unreadCounts.system > 0
  }, $data.unreadCounts.system > 0 ? {
    d: common_vendor.t($data.unreadCounts.system)
  } : {}, {
    e: $data.currentTab === "system" ? 1 : "",
    f: common_vendor.o(($event) => $options.switchMessageTab("system")),
    g: $data.unreadCounts.activity > 0
  }, $data.unreadCounts.activity > 0 ? {
    h: common_vendor.t($data.unreadCounts.activity)
  } : {}, {
    i: $data.currentTab === "activity" ? 1 : "",
    j: common_vendor.o(($event) => $options.switchMessageTab("activity")),
    k: $data.unreadCounts.interaction > 0
  }, $data.unreadCounts.interaction > 0 ? {
    l: common_vendor.t($data.unreadCounts.interaction)
  } : {}, {
    m: $data.currentTab === "interaction" ? 1 : "",
    n: common_vendor.o(($event) => $options.switchMessageTab("interaction")),
    o: $options.filteredMessages.length === 0
  }, $options.filteredMessages.length === 0 ? {
    p: common_assets._imports_0$57
  } : {}, {
    q: common_vendor.f($options.filteredMessages, (message, index, i0) => {
      return common_vendor.e({
        a: message.avatar,
        b: !message.isRead
      }, !message.isRead ? {} : {}, {
        c: common_vendor.t(message.title),
        d: common_vendor.t($options.formatTime(message.time)),
        e: common_vendor.t(message.content),
        f: index,
        g: common_vendor.o(($event) => $options.readMessage(message), index)
      });
    }),
    r: common_vendor.o(($event) => $options.switchTab("home")),
    s: common_vendor.o(($event) => $options.switchTab("discover")),
    t: common_vendor.o(($event) => $options.switchTab("distribution")),
    v: $data.unreadCounts.system + $data.unreadCounts.activity + $data.unreadCounts.interaction > 0
  }, $data.unreadCounts.system + $data.unreadCounts.activity + $data.unreadCounts.interaction > 0 ? {
    w: common_vendor.t($data.unreadCounts.system + $data.unreadCounts.activity + $data.unreadCounts.interaction > 99 ? "99+" : $data.unreadCounts.system + $data.unreadCounts.activity + $data.unreadCounts.interaction)
  } : {}, {
    x: common_vendor.o(($event) => $options.switchTab("my"))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-624c3b46"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/message/index.js.map
