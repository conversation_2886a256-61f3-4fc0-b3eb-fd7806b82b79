import { createStore } from 'vuex'

// Vue 3不再需要Vue.use(Vuex)

export default createStore({
  state: {
    user: null,
    token: null,
    distributorInfo: null
  },
  mutations: {
    SET_USER(state, user) {
      state.user = user
    },
    SET_TOKEN(state, token) {
      state.token = token
    },
    SET_DISTRIBUTOR_INFO(state, info) {
      state.distributorInfo = info
    }
  },
  actions: {
    setUser({ commit }, user) {
      commit('SET_USER', user)
    },
    setToken({ commit }, token) {
      commit('SET_TOKEN', token)
    },
    setDistributorInfo({ commit }, info) {
      commit('SET_DISTRIBUTOR_INFO', info)
    }
  },
  getters: {
    user: state => state.user,
    token: state => state.token,
    distributorInfo: state => state.distributorInfo,
    isDistributor: state => !!state.distributorInfo
  }
}) 