<!-- 满减活动编辑页面 (edit.vue) -->
<template>
  <view class="discount-edit-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @tap="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">编辑满减活动</text>
      <view class="navbar-right">
        <text class="save-btn" @tap="saveDiscount">保存</text>
      </view>
    </view>
    
    <!-- 编辑表单 -->
    <view class="edit-form">
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">基本信息</text>
        </view>
        
        <view class="form-item">
          <text class="item-label">活动名称</text>
          <input class="item-input" v-model="discountForm.title" placeholder="请输入活动名称" maxlength="20" />
          <text class="input-count">{{discountForm.title.length}}/20</text>
        </view>
        
        <view class="form-item">
          <text class="item-label">活动时间</text>
          <view class="date-picker" @tap="showDatePicker('start')">
            <text class="date-text">{{discountForm.startDate || '开始日期'}}</text>
          </view>
          <text class="date-separator">至</text>
          <view class="date-picker" @tap="showDatePicker('end')">
            <text class="date-text">{{discountForm.endDate || '结束日期'}}</text>
          </view>
        </view>
        
        <view class="form-item">
          <text class="item-label">活动状态</text>
          <view class="status-switch">
            <text class="status-text">{{discountForm.status === 'active' ? '启用' : '暂停'}}</text>
            <switch 
              :checked="discountForm.status === 'active'" 
              @change="onStatusChange" 
              color="#F8D800" 
            />
          </view>
        </view>
      </view>
      
      <!-- 满减规则 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">满减规则</text>
          <text class="add-rule" @tap="addRule">+ 添加规则</text>
        </view>
        
        <view class="rules-list">
          <view 
            class="rule-item" 
            v-for="(rule, index) in discountForm.rules" 
            :key="index"
          >
            <view class="rule-inputs">
              <view class="rule-input-group">
                <text class="input-prefix">满</text>
                <input 
                  class="rule-input" 
                  type="digit" 
                  v-model="rule.minAmount" 
                  placeholder="0.00"
                />
                <text class="input-suffix">元</text>
              </view>
              <text class="rule-separator">减</text>
              <view class="rule-input-group">
                <input 
                  class="rule-input" 
                  type="digit" 
                  v-model="rule.discountAmount" 
                  placeholder="0.00"
                />
                <text class="input-suffix">元</text>
              </view>
            </view>
            <view class="rule-delete" @tap="deleteRule(index)">
              <text class="delete-icon">×</text>
            </view>
          </view>
          
          <view class="empty-rules" v-if="discountForm.rules.length === 0">
            <text class="empty-text">请添加满减规则</text>
          </view>
        </view>
      </view>
      
      <!-- 使用设置 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">使用设置</text>
        </view>
        
        <view class="form-item">
          <text class="item-label">适用商品</text>
          <view class="item-right" @tap="selectProducts">
            <text class="item-value">{{discountForm.applicableProducts || '全部商品'}}</text>
            <text class="item-arrow">></text>
          </view>
        </view>
        
        <view class="form-item">
          <text class="item-label">叠加使用</text>
          <switch 
            :checked="discountForm.canStack" 
            @change="onStackChange" 
            color="#F8D800" 
          />
        </view>
        
        <view class="form-item">
          <text class="item-label">每人限用</text>
          <view class="limit-input-group">
            <input 
              class="limit-input" 
              type="number" 
              v-model="discountForm.perPersonLimit" 
              placeholder="不限制"
            />
            <text class="input-suffix">次</text>
          </view>
        </view>
        
        <view class="form-item textarea-item">
          <text class="item-label">活动说明</text>
          <textarea 
            class="item-textarea" 
            v-model="discountForm.instructions" 
            placeholder="请输入活动说明" 
            maxlength="200"
          ></textarea>
          <text class="textarea-count">{{discountForm.instructions.length}}/200</text>
        </view>
      </view>
    </view>
    
    <!-- 底部保存按钮 -->
    <view class="bottom-save-bar">
      <button class="save-button" @tap="saveDiscount">保存</button>
    </view>
    
    <!-- 日期选择器 -->
    <uni-calendar 
      v-if="showDatePickerDialog"
      :insert="false"
      :start-date="'2020-01-01'"
      :end-date="'2030-12-31'"
      @confirm="onDateSelect"
      @close="closeDatePicker"
    />
  </view>
</template>

<script>
import { ref, reactive, onMounted } from 'vue';

export default {
  setup() {
    // 响应式状态
    const discountId = ref('');
    const discountForm = reactive({
      title: '',
      startDate: '',
      endDate: '',
      status: 'active',
      rules: [],
      applicableProducts: '全部商品',
      canStack: false,
      perPersonLimit: '3',
      instructions: ''
    });
    
    const showDatePickerDialog = ref(false);
    const currentDatePicker = ref(''); // 'start' or 'end'
    
    // 方法
    function goBack() {
      uni.navigateBack();
    }
    
    function showDatePicker(type) {
      currentDatePicker.value = type;
      showDatePickerDialog.value = true;
    }
    
    function closeDatePicker() {
      showDatePickerDialog.value = false;
    }
    
    function onDateSelect(e) {
      const selectedDate = e.fulldate;
      if (currentDatePicker.value === 'start') {
        discountForm.startDate = selectedDate;
      } else {
        discountForm.endDate = selectedDate;
      }
      closeDatePicker();
    }
    
    function onStatusChange(e) {
      discountForm.status = e.detail.value ? 'active' : 'paused';
    }
    
    function onStackChange(e) {
      discountForm.canStack = e.detail.value;
    }
    
    function addRule() {
      discountForm.rules.push({
        minAmount: '',
        discountAmount: ''
      });
    }
    
    function deleteRule(index) {
      discountForm.rules.splice(index, 1);
    }
    
    function selectProducts() {
      uni.showActionSheet({
        itemList: ['全部商品', '指定商品', '指定分类'],
        success: (res) => {
          switch(res.tapIndex) {
            case 0:
              discountForm.applicableProducts = '全部商品';
              break;
            case 1:
              // 在实际应用中，这里应该打开商品选择页面
              discountForm.applicableProducts = '指定商品';
              break;
            case 2:
              // 在实际应用中，这里应该打开分类选择页面
              discountForm.applicableProducts = '指定分类';
              break;
          }
        }
      });
    }
    
    function validateForm() {
      if (!discountForm.title.trim()) {
        uni.showToast({
          title: '请输入活动名称',
          icon: 'none'
        });
        return false;
      }
      
      if (!discountForm.startDate || !discountForm.endDate) {
        uni.showToast({
          title: '请选择活动时间',
          icon: 'none'
        });
        return false;
      }
      
      if (discountForm.rules.length === 0) {
        uni.showToast({
          title: '请添加至少一条满减规则',
          icon: 'none'
        });
        return false;
      }
      
      for (const rule of discountForm.rules) {
        if (!rule.minAmount || !rule.discountAmount) {
          uni.showToast({
            title: '请完善满减规则',
            icon: 'none'
          });
          return false;
        }
        
        if (parseFloat(rule.discountAmount) >= parseFloat(rule.minAmount)) {
          uni.showToast({
            title: '优惠金额不能大于等于满减金额',
            icon: 'none'
          });
          return false;
        }
      }
      
      return true;
    }
    
    function saveDiscount() {
      if (!validateForm()) {
        return;
      }
      
      // 在实际应用中，这里应该调用API保存数据
      
      uni.showLoading({
        title: '保存中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        });
        
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }, 1000);
    }
    
    function loadDiscountData(id) {
      // 在实际应用中，这里应该调用API获取满减活动详情
      discountId.value = id;
      
      // 模拟加载数据
      uni.showLoading({
        title: '加载中...'
      });
      
      // 模拟数据
      setTimeout(() => {
        Object.assign(discountForm, {
          title: '春季促销活动',
          startDate: '2023-04-01',
          endDate: '2023-04-30',
          status: 'active',
          rules: [
            { minAmount: '100', discountAmount: '10' },
            { minAmount: '200', discountAmount: '25' },
            { minAmount: '300', discountAmount: '50' }
          ],
          applicableProducts: '全部商品',
          canStack: false,
          perPersonLimit: '3',
          instructions: '活动期间，每位用户最多可使用3次满减优惠，不可与其他优惠同时使用'
        });
        
        uni.hideLoading();
      }, 500);
    }
    
    onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.$page?.options || {};
      
      if (options.id) {
        loadDiscountData(options.id);
      } else {
        // 默认添加一条空规则
        addRule();
      }
    });
    
    return {
      discountForm,
      showDatePickerDialog,
      currentDatePicker,
      goBack,
      showDatePicker,
      closeDatePicker,
      onDateSelect,
      onStatusChange,
      onStackChange,
      addRule,
      deleteRule,
      selectProducts,
      saveDiscount
    };
  }
}
</script>

<style lang="scss">
.discount-edit-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 80px; /* 为底部按钮留出空间 */
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FDEB71, #F8D800);
  color: #333;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(248, 216, 0, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #333;
  border-bottom: 2px solid #333;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  padding: 0 10px;
}

.save-btn {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

/* 表单样式 */
.edit-form {
  padding: 15px;
}

.form-section {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.add-rule {
  font-size: 14px;
  color: #F8D800;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.form-item:last-child {
  border-bottom: none;
}

.item-label {
  width: 80px;
  font-size: 14px;
  color: #666;
}

.item-input {
  flex: 1;
  height: 24px;
  font-size: 14px;
  color: #333;
}

.input-count {
  font-size: 12px;
  color: #999;
  margin-left: 10px;
}

.date-picker {
  flex: 1;
  height: 36px;
  background: #f5f5f5;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
}

.date-text {
  font-size: 14px;
  color: #333;
}

.date-separator {
  margin: 0 10px;
  color: #999;
}

.status-switch {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.status-text {
  font-size: 14px;
  color: #333;
  margin-right: 10px;
}

/* 规则样式 */
.rules-list {
  
}

.rule-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  background: #f9f9f9;
  border-radius: 8px;
  padding: 12px;
}

.rule-inputs {
  flex: 1;
  display: flex;
  align-items: center;
}

.rule-input-group {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 6px;
  padding: 8px 12px;
  border: 1px solid #eee;
}

.input-prefix {
  font-size: 14px;
  color: #666;
  margin-right: 5px;
}

.rule-input {
  width: 80px;
  font-size: 14px;
  color: #333;
  text-align: center;
}

.input-suffix {
  font-size: 14px;
  color: #666;
  margin-left: 5px;
}

.rule-separator {
  margin: 0 10px;
  color: #666;
  font-size: 14px;
}

.rule-delete {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
}

.delete-icon {
  font-size: 18px;
  color: #999;
}

.empty-rules {
  padding: 30px 0;
  display: flex;
  justify-content: center;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 使用设置样式 */
.item-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.item-value {
  font-size: 14px;
  color: #333;
  margin-right: 5px;
}

.item-arrow {
  font-size: 14px;
  color: #999;
}

.limit-input-group {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.limit-input {
  width: 60px;
  font-size: 14px;
  color: #333;
  text-align: right;
}

.textarea-item {
  flex-direction: column;
  align-items: flex-start;
}

.item-textarea {
  width: 100%;
  height: 100px;
  font-size: 14px;
  color: #333;
  background: #f9f9f9;
  border-radius: 8px;
  padding: 10px;
  margin-top: 10px;
  box-sizing: border-box;
}

.textarea-count {
  align-self: flex-end;
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

/* 底部保存按钮样式 */
.bottom-save-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 15px;
  background: #fff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 90;
}

.save-button {
  width: 100%;
  height: 44px;
  background: linear-gradient(135deg, #FDEB71, #F8D800);
  color: #333;
  font-size: 16px;
  font-weight: 500;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}
</style>