{"version": 3, "file": "index.js", "sources": ["subPackages/merchant-admin/pages/activity/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW5ccGFnZXNcYWN0aXZpdHlcaW5kZXgudnVl"], "sourcesContent": ["<template>\n  <view class=\"activity-management\">\n    <!-- 状态栏高度占位 -->\n    <view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\n    \n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-left\" @tap.stop=\"goBack\">\n        <view class=\"back-btn\">\n          <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M15 18L9 12L15 6\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n        </view>\n      </view>\n      <view class=\"navbar-title\">活动管理</view>\n      <view class=\"navbar-right\">\n        <view class=\"navbar-action\" @tap.stop=\"showMoreOptions\">\n          <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11C11.4477 11 11 11.4477 11 12C11 12.5523 11.4477 13 12 13Z\" fill=\"white\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <path d=\"M19 13C19.5523 13 20 12.5523 20 12C20 11.4477 19.5523 11 19 11C18.4477 11 18 11.4477 18 12C18 12.5523 18.4477 13 19 13Z\" fill=\"white\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <path d=\"M5 13C5.55228 13 6 12.5523 6 12C6 11.4477 5.55228 11 5 11C4.44772 11 4 11.4477 4 12C4 12.5523 4.44772 13 5 13Z\" fill=\"white\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 页面内容区域 -->\n    <scroll-view class=\"content-area\" scroll-y refresher-enabled \n      :refresher-triggered=\"refreshing\"\n      @refresherrefresh=\"onRefresh\">\n      \n      <!-- 活动统计卡片 -->\n      <view class=\"statistics-card\">\n        <view class=\"statistics-item\">\n          <text class=\"statistics-value\">{{statistics.total}}</text>\n          <text class=\"statistics-label\">活动总数</text>\n          </view>\n        <view class=\"statistics-divider\"></view>\n        <view class=\"statistics-item\">\n          <text class=\"statistics-value\">{{statistics.running}}</text>\n          <text class=\"statistics-label\">进行中</text>\n          </view>\n        <view class=\"statistics-divider\"></view>\n        <view class=\"statistics-item\">\n          <text class=\"statistics-value\">{{statistics.upcoming}}</text>\n          <text class=\"statistics-label\">未开始</text>\n          </view>\n        <view class=\"statistics-divider\"></view>\n        <view class=\"statistics-item\">\n          <text class=\"statistics-value\">{{statistics.ended}}</text>\n          <text class=\"statistics-label\">已结束</text>\n        </view>\n      </view>\n      \n      <!-- 筛选标签栏 -->\n      <view class=\"filter-section\">\n        <!-- 活动类型选择 -->\n        <view class=\"filter-category\">\n          <view class=\"category-title\">活动类型</view>\n          <scroll-view class=\"filter-tabs\" scroll-x show-scrollbar=\"false\">\n            <view \n              class=\"filter-tab\" \n              v-for=\"(type, index) in activityTypes\" \n              :key=\"'type-' + index\"\n              :class=\"{ active: currentType === type.value }\"\n              @tap.stop=\"switchType(type.value)\"\n            >\n              <text>{{type.label}}</text>\n          </view>\n          </scroll-view>\n          </view>\n        \n        <!-- 活动状态选择 -->\n        <view class=\"filter-category\">\n          <view class=\"category-title\">活动状态</view>\n          <scroll-view class=\"filter-tabs\" scroll-x show-scrollbar=\"false\">\n            <view \n              class=\"filter-tab\" \n              v-for=\"(tab, index) in filterTabs\" \n              :key=\"'status-' + index\"\n              :class=\"{ active: currentTab === tab.value }\"\n              @tap.stop=\"switchTab(tab.value)\"\n            >\n              <text>{{tab.label}}</text>\n          </view>\n          </scroll-view>\n        </view>\n      </view>\n      \n      <!-- 活动列表占位 -->\n      <view v-if=\"loading\" class=\"loading-container\">\n        <view class=\"loading-spinner\"></view>\n        <text class=\"loading-text\">加载中...</text>\n      </view>\n      \n      <!-- 活动列表 -->\n      <view v-else>\n        <!-- 空状态 -->\n        <view v-if=\"activityList.length === 0\" class=\"empty-container\">\n          <image src=\"/static/images/tabbar/无数据.png\" class=\"empty-image\"></image>\n          <text class=\"empty-text\">暂无活动数据</text>\n          <view class=\"empty-action\" @tap.stop=\"createNewActivity\">\n            <text>立即创建</text>\n          </view>\n        </view>\n        \n        <!-- 活动卡片列表 -->\n        <view v-else class=\"activity-list\">\n          <view \n            v-for=\"(item, index) in activityList\" \n            :key=\"index\" \n            class=\"activity-card\"\n            @tap.stop=\"viewActivityDetail(item.id)\"\n          >\n            <!-- 顶部标签和状态 -->\n            <view class=\"card-header\">\n              <view class=\"status-tags\">\n                <view class=\"type-tag\" :class=\"'type-' + item.type\">\n                  <text>{{ getTypeText(item.type) }}</text>\n                </view>\n                <view class=\"status-tag\" :class=\"'status-' + item.status\">\n                  <text>{{ getStatusText(item.status) }}</text>\n                </view>\n                <view v-if=\"item.isTop\" class=\"top-tag\">\n                  <text>已置顶</text>\n                </view>\n          </view>\n        </view>\n        \n            <!-- 卡片内容 -->\n            <view class=\"card-content\">\n              <!-- 活动封面 -->\n              <view class=\"card-image\">\n                <image :src=\"item.coverImage\" mode=\"aspectFill\"></image>\n            </view>\n              \n              <!-- 活动信息 -->\n              <view class=\"card-info\">\n                <text class=\"activity-title\">{{ item.title }}</text>\n                <view class=\"activity-time\">\n                  <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"#8E8E93\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n                  </svg>\n                  <text>{{ formatDateRange(item.startTime, item.endTime) }}</text>\n                </view>\n              <view class=\"activity-stats\">\n                <view class=\"stat-item\">\n                    <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path d=\"M15 12C15 13.6569 13.6569 15 12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12Z\" stroke=\"#8E8E93\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n                      <path d=\"M2.45825 12C3.73253 7.94288 7.52281 5 12.0004 5C16.4781 5 20.2684 7.94291 21.5426 12C20.2684 16.0571 16.4781 19 12.0005 19C7.52281 19 3.73251 16.0571 2.45825 12Z\" stroke=\"#8E8E93\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n                    </svg>\n                    <text>{{ item.views }}</text>\n                </view>\n                <view class=\"stat-item\">\n                    <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path d=\"M17 21V19C17 16.7909 15.2091 15 13 15H5C2.79086 15 1 16.7909 1 19V21M23 21V19C22.9986 17.1771 21.765 15.5857 20 15.13M16 3.13C17.7699 3.58317 19.0078 5.17799 19.0078 7.005C19.0078 8.83201 17.7699 10.4268 16 10.88M9 11C11.2091 11 13 9.20914 13 7C13 4.79086 11.2091 3 9 3C6.79086 3 5 4.79086 5 7C5 9.20914 6.79086 11 9 11Z\" stroke=\"#8E8E93\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n                    </svg>\n                    <text>{{ item.participants }}</text>\n                </view>\n                </view>\n              </view>\n                </view>\n            \n            <!-- 卡片操作按钮 -->\n            <view class=\"card-actions\">\n              <view class=\"action-button edit\" @tap.stop=\"editActivity(item.id)\">\n                <text>编辑</text>\n              </view>\n              <view class=\"action-button promote\" @tap.stop=\"promoteActivity(item.id)\">\n                <text>推广</text>\n            </view>\n              <view class=\"action-button share\" @tap.stop=\"shareActivity(item.id)\">\n                <text>转发</text>\n          </view>\n              <view class=\"action-button more\" @tap.stop=\"showActivityOptions(item.id)\">\n                <text>更多</text>\n        </view>\n            </view>\n        </view>\n      </view>\n      \n        <!-- 加载更多 -->\n        <view v-if=\"hasMore\" class=\"load-more\" @tap.stop=\"loadMoreActivities\">\n          <text>加载更多</text>\n        </view>\n        <view v-else-if=\"activityList.length > 0\" class=\"no-more\">\n          <text>已加载全部数据</text>\n        </view>\n      </view>\n    </scroll-view>\n    \n    <!-- 发布按钮 - 去掉图标，缩小尺寸 -->\n    <view class=\"fab-button\" @tap.stop=\"createNewActivity\">\n      <text class=\"fab-text\">发布</text>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      statusBarHeight: 20, // 默认值，将在onLoad中获取真实高度\n      refreshing: false,\n      loading: true,\n      currentTab: 'all',\n      currentType: 'all', // 当前选中的活动类型\n      hasMore: true,\n      page: 1,\n      \n      // 活动统计数据\n      statistics: {\n        total: 8,\n        running: 3,\n        upcoming: 1,\n        ended: 4\n      },\n      \n      // 筛选标签\n      filterTabs: [\n        { label: '全部', value: 'all' },\n        { label: '进行中', value: 'running' },\n        { label: '未开始', value: 'upcoming' },\n        { label: '已结束', value: 'ended' },\n        { label: '最近发布', value: 'recent' },\n        { label: '高参与度', value: 'popular' }\n      ],\n      \n      // 活动类型选项\n      activityTypes: [\n        { label: '全部类型', value: 'all' },\n        { label: '团购活动', value: 'groupon' },\n        { label: '优惠券', value: 'coupon' },\n        { label: '秒杀活动', value: 'seckill' },\n        { label: '满减活动', value: 'discount' },\n        { label: '积分兑换', value: 'points' }\n      ],\n      \n      // 活动列表数据\n      activityList: [\n        {\n          id: 1,\n          title: '夏季特惠，全场满300减50',\n          coverImage: '/static/images/activity-1.jpg',\n          startTime: '2023-07-01',\n          endTime: '2023-08-31',\n          status: 'running',\n          views: 1258,\n          participants: 78,\n          isTop: true,\n          type: 'discount' // 活动类型：满减活动\n        },\n        {\n          id: 2,\n          title: '开业庆典，免费品尝活动',\n          coverImage: '/static/images/activity-2.jpg',\n          startTime: '2023-06-15',\n          endTime: '2023-06-20',\n          status: 'ended',\n          views: 876,\n          participants: 126,\n          isTop: false,\n          type: 'coupon' // 活动类型：优惠券\n        },\n        {\n          id: 3,\n          title: '周年庆典，抽奖赢大礼',\n          coverImage: '/static/images/activity-3.jpg',\n          startTime: '2023-12-01',\n          endTime: '2023-12-10',\n          status: 'upcoming',\n          views: 322,\n          participants: 0,\n          isTop: false,\n          type: 'groupon' // 活动类型：团购活动\n        }\n      ]\n    }\n  },\n  onLoad() {\n    this.getStatusBarHeight();\n    this.loadActivityData();\n  },\n  methods: {\n    // 获取状态栏高度\n    getStatusBarHeight() {\n      uni.getSystemInfo({\n        success: (res) => {\n          this.statusBarHeight = res.statusBarHeight;\n        }\n      });\n    },\n    // 返回上一页\n    goBack() {\n      console.log('返回按钮点击');\n      uni.navigateBack();\n    },\n    // 显示更多选项\n    showMoreOptions() {\n      console.log('更多选项按钮点击');\n      uni.showActionSheet({\n        itemList: ['批量管理', '活动统计', '帮助中心'],\n        success: (res) => {\n          // 根据选择执行不同操作\n          console.log('选择了: ', res.tapIndex);\n        }\n      });\n    },\n    // 创建新活动\n    createNewActivity() {\n      console.log('创建新活动按钮点击');\n      \n      // 显示交互反馈\n      uni.showLoading({\n        title: '正在跳转...',\n        mask: true\n      });\n      \n      // 延迟处理，确保视觉效果\n      setTimeout(() => {\n        uni.hideLoading();\n        \n        // 跳转到正确的营销中心页面\n        uni.navigateTo({\n          url: '/subPackages/merchant-admin-marketing/pages/marketing/index',\n          success: () => {\n            console.log('跳转到营销中心成功');\n          },\n          fail: (err) => {\n            console.error('跳转失败:', err);\n            uni.showModal({\n              title: '提示',\n              content: '无法访问营销中心页面',\n              showCancel: false\n            });\n          }\n        });\n      }, 300);\n    },\n    // 下拉刷新\n    onRefresh() {\n      console.log('下拉刷新触发');\n      this.refreshing = true;\n      this.page = 1;\n      this.hasMore = true;\n      this.loadActivityData();\n    },\n    // 加载活动数据\n    loadActivityData() {\n      this.loading = true;\n      \n      // 模拟加载数据\n      setTimeout(() => {\n        // 如果是第一页，清空现有数据\n        if (this.page === 1) {\n          // 模拟根据类型和状态筛选数据\n          const filteredList = this.getFilteredActivities();\n          this.activityList = filteredList;\n        } else {\n          // 模拟加载更多数据\n          // 实际项目中这里应该调用API获取更多数据\n          console.log('加载更多数据，页码：', this.page);\n        }\n        \n        this.loading = false;\n        this.refreshing = false;\n        \n        // 模拟没有更多数据\n        if (this.page > 1) {\n          this.hasMore = false;\n        }\n      }, 1000);\n    },\n    // 获取筛选后的活动列表\n    getFilteredActivities() {\n      // 模拟从服务器获取的完整活动列表\n      const allActivities = [\n        {\n          id: 1,\n          title: '夏季特惠，全场满300减50',\n          coverImage: '/static/images/activity-1.jpg',\n          startTime: '2023-07-01',\n          endTime: '2023-08-31',\n          status: 'running',\n          views: 1258,\n          participants: 78,\n          isTop: true,\n          type: 'discount'\n        },\n        {\n          id: 2,\n          title: '开业庆典，免费品尝活动',\n          coverImage: '/static/images/activity-2.jpg',\n          startTime: '2023-06-15',\n          endTime: '2023-06-20',\n          status: 'ended',\n          views: 876,\n          participants: 126,\n          isTop: false,\n          type: 'coupon'\n        },\n        {\n          id: 3,\n          title: '周年庆典，抽奖赢大礼',\n          coverImage: '/static/images/activity-3.jpg',\n          startTime: '2023-12-01',\n          endTime: '2023-12-10',\n          status: 'upcoming',\n          views: 322,\n          participants: 0,\n          isTop: false,\n          type: 'groupon'\n        },\n        {\n          id: 4,\n          title: '限时秒杀，爆款5折起',\n          coverImage: '/static/images/activity-1.jpg',\n          startTime: '2023-08-15',\n          endTime: '2023-08-17',\n          status: 'running',\n          views: 1890,\n          participants: 245,\n          isTop: false,\n          type: 'seckill'\n        },\n        {\n          id: 5,\n          title: '会员积分兑换活动',\n          coverImage: '/static/images/activity-2.jpg',\n          startTime: '2023-07-20',\n          endTime: '2023-09-20',\n          status: 'running',\n          views: 768,\n          participants: 124,\n          isTop: false,\n          type: 'points'\n        }\n      ];\n      \n      // 根据类型和状态筛选\n      return allActivities.filter(activity => {\n        // 类型筛选\n        const typeMatch = this.currentType === 'all' || activity.type === this.currentType;\n        \n        // 状态筛选\n        let statusMatch = true;\n        if (this.currentTab === 'running') {\n          statusMatch = activity.status === 'running';\n        } else if (this.currentTab === 'upcoming') {\n          statusMatch = activity.status === 'upcoming';\n        } else if (this.currentTab === 'ended') {\n          statusMatch = activity.status === 'ended';\n        }\n        \n        return typeMatch && statusMatch;\n      });\n    },\n    // 加载更多活动\n    loadMoreActivities() {\n      console.log('加载更多按钮点击');\n      if (!this.hasMore) return;\n      \n      this.page++;\n      this.loadActivityData();\n    },\n    // 切换标签\n    switchTab(tabValue) {\n      console.log('切换状态标签：', tabValue);\n      this.currentTab = tabValue;\n      this.page = 1;\n      this.hasMore = true;\n      this.loadActivityData();\n    },\n    // 显示排序选项\n    showSortOptions() {\n      console.log('显示排序选项');\n      uni.showActionSheet({\n        itemList: ['最新发布', '即将开始', '即将结束', '参与人数'],\n        success: (res) => {\n          uni.showToast({\n            title: '排序方式已更改',\n            icon: 'none'\n          });\n        }\n      });\n    },\n    // 显示筛选选项\n    showFilterOptions() {\n      console.log('显示筛选选项');\n        uni.navigateTo({\n        url: '/subPackages/merchant-admin/pages/activity/filter'\n        });\n    },\n    // 获取活动状态文本\n    getStatusText(status) {\n      const statusMap = {\n        'running': '进行中',\n        'ended': '已结束',\n        'upcoming': '未开始'\n      };\n      return statusMap[status] || '未知状态';\n    },\n    // 格式化日期范围\n    formatDateRange(start, end) {\n      if (!start || !end) return '';\n      \n      const startDate = new Date(start);\n      const endDate = new Date(end);\n      \n      const startMonth = startDate.getMonth() + 1;\n      const startDay = startDate.getDate();\n      const endMonth = endDate.getMonth() + 1;\n      const endDay = endDate.getDate();\n      \n      return `${startMonth}.${startDay} - ${endMonth}.${endDay}`;\n    },\n    // 查看活动详情\n    viewActivityDetail(id) {\n      console.log('查看活动详情，ID：', id);\n      uni.navigateTo({\n        url: `/subPackages/merchant-admin/pages/activity/detail?id=${id}`\n      });\n    },\n    // 编辑活动\n    editActivity(id) {\n      console.log('编辑活动按钮点击，ID：', id);\n      uni.navigateTo({\n        url: `/subPackages/merchant-admin/pages/activity/edit?id=${id}`\n      });\n    },\n    // 推广活动\n    promoteActivity(id) {\n      console.log('推广活动按钮点击，ID：', id);\n      // 找到当前活动\n      const activity = this.activityList.find(item => item.id === id);\n      \n      // 根据当前置顶状态显示不同选项\n      const topAction = activity && activity.isTop ? '取消置顶' : '置顶活动';\n      \n      uni.showActionSheet({\n        itemList: [topAction, '推荐到首页', '短信推广', '分享到群', '朋友圈宣传'],\n        success: (res) => {\n          if (res.tapIndex === 0) {\n            // 切换置顶状态\n            this.toggleTopStatus(id);\n          } else {\n            uni.showToast({\n              title: '推广设置成功',\n              icon: 'success'\n            });\n          }\n        }\n      });\n    },\n    // 切换活动置顶状态\n    toggleTopStatus(id) {\n      const index = this.activityList.findIndex(item => item.id === id);\n      if (index !== -1) {\n        // 切换置顶状态\n        const isTop = !this.activityList[index].isTop;\n        this.activityList[index].isTop = isTop;\n        \n        // 显示成功提示\n        uni.showToast({\n          title: isTop ? '活动已置顶' : '已取消置顶',\n          icon: 'success'\n        });\n      }\n    },\n      // 分享活动\n    shareActivity(id) {\n      console.log('分享活动按钮点击，ID：', id);\n      uni.showToast({\n        title: '已打开分享菜单',\n        icon: 'none',\n        duration: 1500\n      });\n      \n      // 注释掉原来的分享API调用，使用showShareMenu代替\n      setTimeout(() => {\n        uni.showToast({\n          title: '活动已分享',\n          icon: 'success'\n        });\n      }, 1500);\n    },\n    // 显示活动更多选项\n    showActivityOptions(id) {\n      console.log('更多按钮点击，ID：', id);\n      uni.showActionSheet({\n        itemList: ['查看数据', '复制活动', '下架活动', '删除活动'],\n        success: (res) => {\n          // 根据选择执行不同操作\n          const actions = ['viewData', 'duplicateActivity', 'stopActivity', 'deleteActivity'];\n          const action = actions[res.tapIndex];\n          \n          if (action && this[action]) {\n            this[action](id);\n          }\n        }\n      });\n    },\n    // 查看数据\n    viewData(id) {\n      console.log('查看数据，ID：', id);\n      uni.navigateTo({\n        url: `/subPackages/merchant-admin/pages/activity/data?id=${id}`\n      });\n    },\n    // 复制活动\n    duplicateActivity(id) {\n      console.log('复制活动，ID：', id);\n      uni.showModal({\n        title: '复制活动',\n        content: '确定要复制该活动吗？',\n        success: (res) => {\n          if (res.confirm) {\n          uni.showToast({\n              title: '活动复制成功',\n            icon: 'success'\n          });\n          }\n        }\n      });\n    },\n    // 下架活动\n    stopActivity(id) {\n      console.log('下架活动，ID：', id);\n      uni.showModal({\n        title: '下架活动',\n        content: '确定要下架该活动吗？',\n        success: (res) => {\n          if (res.confirm) {\n          uni.showToast({\n              title: '活动已下架',\n            icon: 'success'\n          });\n        }\n        }\n      });\n    },\n    // 删除活动\n    deleteActivity(id) {\n      console.log('删除活动，ID：', id);\n      uni.showModal({\n        title: '删除活动',\n        content: '确定要删除该活动吗？删除后不可恢复',\n        success: (res) => {\n          if (res.confirm) {\n            uni.showToast({\n              title: '活动已删除',\n              icon: 'success'\n            });\n          }\n        }\n      });\n    },\n    // 切换活动类型\n    switchType(typeValue) {\n      console.log('切换活动类型：', typeValue);\n      this.currentType = typeValue;\n      this.page = 1;\n      this.hasMore = true;\n      this.loadActivityData();\n    },\n    // 获取活动类型文本\n    getTypeText(type) {\n      const typeMap = {\n        'groupon': '团购活动',\n        'coupon': '优惠券',\n        'seckill': '秒杀活动',\n        'discount': '满减活动',\n        'points': '积分兑换'\n      };\n      return typeMap[type] || '未知类型';\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.activity-management {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background-color: #f5f7fa;\n  position: relative;\n}\n\n/* 状态栏样式 */\n.status-bar {\n  background: linear-gradient(135deg, #007AFF, #5856D6);\n  width: 100%;\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: 1001;\n}\n\n/* 导航栏样式 */\n.navbar {\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 16px;\n  background: linear-gradient(135deg, #007AFF, #5856D6);\n  color: white;\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  z-index: 1000;\n  margin-top: v-bind('statusBarHeight + \"px\"');\n  box-shadow: 0 2px 10px rgba(0, 122, 255, 0.2);\n}\n\n.navbar-left, .navbar-right {\n  display: flex;\n  align-items: center;\n  min-width: 32px;\n}\n\n/* 添加返回按钮样式 */\n.back-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.15);\n  transition: all 0.2s ease;\n  \n  &:active {\n    background: rgba(255, 255, 255, 0.25);\n    transform: scale(0.92);\n  }\n  \n  svg {\n    width: 20px;\n    height: 20px;\n  }\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 500;\n  letter-spacing: 0.5px;\n}\n\n.navbar-action {\n  padding: 4px;\n}\n\n/* 内容区域 */\n.content-area {\n  flex: 1;\n  width: 100%;\n  box-sizing: border-box;\n  padding: 16px;\n  margin-top: v-bind('statusBarHeight + 44 + \"px\"'); /* 状态栏+导航栏高度 */\n  height: v-bind('`calc(100vh - ${statusBarHeight + 44}px)`');\n}\n\n/* 统计卡片 */\n.statistics-card {\n  display: flex;\n  justify-content: space-between;\n  background-color: white;\n  border-radius: 16px;\n  padding: 20px 10px;\n  margin: 0 auto 16px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  width: 92%;\n  transform: translateZ(0);\n  position: relative;\n  overflow: hidden;\n}\n\n.statistics-card::after {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border-radius: 16px;\n  box-shadow: inset 0 1px 1px rgba(255, 255, 255, 0.8);\n  z-index: 1;\n  pointer-events: none;\n}\n\n.statistics-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.statistics-value {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 4px;\n}\n\n.statistics-label {\n  font-size: 12px;\n  color: #666;\n}\n\n.statistics-divider {\n  width: 1px;\n  height: 30px;\n  background-color: #eee;\n}\n\n/* 筛选区域 */\n.filter-section {\n  margin: 0 auto 16px;\n  width: 92%; /* 减小宽度，确保边缘有足够的间距 */\n  background-color: #fff;\n  border-radius: 12px;\n  padding: 14px 12px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\n}\n\n.filter-category {\n  margin-bottom: 12px;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n.category-title {\n  font-size: 14px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 10px;\n  padding-left: 4px;\n}\n\n.filter-tabs {\n  white-space: nowrap;\n  padding-right: 16px;\n  margin-bottom: 4px;\n  overflow-x: auto;\n}\n\n.filter-tabs::-webkit-scrollbar {\n  display: none;\n}\n\n.filter-tab {\n  display: inline-block;\n  padding: 6px 16px;\n  margin-right: 8px;\n  background-color: #F2F2F7;\n  border-radius: 20px;\n  font-size: 13px;\n  color: #666;\n  transition: all 0.2s ease;\n  \n  &:last-child {\n    margin-right: 0;\n  }\n  \n  &.active {\n    background-color: #007AFF;\n    color: white;\n  font-weight: 500;\n  }\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 12px;\n}\n\n/* 活动列表 */\n.activity-list {\n  margin: 0 auto 16px;\n  width: 96%;\n}\n\n.activity-card {\n  background-color: white;\n  border-radius: 16px;\n  margin-bottom: 20px;\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\n  overflow: hidden;\n  position: relative;\n  transform: translateZ(0);\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n  width: 100%;\n}\n\n.activity-card:active {\n  transform: scale(0.98) translateZ(0);\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);\n}\n\n.activity-card::after {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 1px;\n  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.8), transparent);\n  z-index: 1;\n}\n\n.card-header {\n  display: flex;\n  justify-content: center;\n  padding: 12px 16px 0;\n}\n\n.status-tags {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  justify-content: center;\n  gap: 6px;\n}\n\n.status-tag, .type-tag, .top-tag {\n  padding: 3px 8px;\n  border-radius: 10px;\n  font-size: 11px;\n  font-weight: 500;\n}\n\n.type-tag {\n  background-color: #F2F2F7;\n  color: #8E8E93;\n}\n\n.type-groupon {\n  background-color: rgba(125, 122, 255, 0.1);\n  color: #7D7AFF;\n}\n\n.type-coupon {\n  background-color: rgba(255, 69, 58, 0.1);\n  color: #FF453A;\n}\n\n.type-seckill {\n  background-color: rgba(255, 55, 95, 0.1);\n  color: #FF375F;\n}\n\n.type-discount {\n  background-color: rgba(255, 149, 0, 0.1);\n  color: #FF9500;\n}\n\n.type-points {\n  background-color: rgba(52, 199, 89, 0.1);\n  color: #34C759;\n}\n\n.card-content {\n  display: flex;\n  padding: 16px 20px;\n}\n\n.card-image {\n  width: 100px;\n  height: 100px;\n  border-radius: 12px;\n  overflow: hidden;\n  margin-right: 16px;\n  flex-shrink: 0;\n}\n\n.card-image image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.card-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.activity-title {\n  font-size: 17px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 10px;\n  line-height: 1.4;\n}\n\n.activity-time {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8px;\n  font-size: 13px;\n  color: #666;\n}\n\n.activity-time svg {\n  margin-right: 4px;\n}\n\n.activity-stats {\n  display: flex;\n}\n\n.stat-item {\n  display: flex;\n  align-items: center;\n  margin-right: 16px;\n  font-size: 13px;\n  color: #666;\n}\n\n.stat-item svg {\n  margin-right: 4px;\n}\n\n.card-actions {\n  display: flex;\n  border-top: 1px solid #f5f5f5;\n  padding: 12px 16px;\n  justify-content: space-around;\n  gap: 10px;\n}\n\n.action-button {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 13px;\n  color: white;\n  padding: 6px 14px;\n  border-radius: 18px;\n  transition: all 0.2s ease;\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);\n  font-weight: 500;\n  \n  &:active {\n    transform: scale(0.95);\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\n  }\n  \n  &.edit {\n    background: linear-gradient(135deg, #0A84FF, #0060DF);\n  }\n  \n  &.promote {\n    background: linear-gradient(135deg, #FF9500, #E66000);\n  }\n  \n  &.share {\n    background: linear-gradient(135deg, #34C759, #28A745);\n  }\n  \n  &.more {\n    background: linear-gradient(135deg, #8E8E93, #636366);\n  }\n}\n\n/* 空状态 */\n.empty-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 40px 0;\n  width: 92%;\n  margin: 0 auto;\n}\n\n.empty-image {\n  width: 120px;\n  height: 120px;\n  margin-bottom: 16px;\n}\n\n.empty-text {\n  font-size: 16px;\n  color: #999;\n  margin-bottom: 20px;\n}\n\n.empty-action {\n  padding: 8px 20px;\n  background-color: #0A84FF;\n  color: white;\n  border-radius: 20px;\n  font-size: 14px;\n}\n\n/* 加载中 */\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 40px 0;\n  width: 92%;\n  margin: 0 auto;\n}\n\n.loading-spinner {\n  width: 30px;\n  height: 30px;\n  border: 3px solid #f3f3f3;\n  border-top: 3px solid #0A84FF;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 12px;\n}\n\n.loading-text {\n  font-size: 14px;\n  color: #999;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* 加载更多和没有更多 */\n.load-more, .no-more {\n  text-align: center;\n  padding: 16px 0;\n  font-size: 14px;\n  color: #999;\n  width: 92%;\n  margin: 0 auto;\n}\n\n.load-more {\n  color: #0A84FF;\n}\n\n/* 浮动按钮 */\n.fab-button {\n  position: fixed;\n  right: 24px;\n  bottom: 24px;\n  width: 52px;\n  height: 36px;\n  border-radius: 18px;\n  background: linear-gradient(135deg, #0A84FF, #0060DF);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 4px 16px rgba(10, 132, 255, 0.5);\n  z-index: 1000;\n  transition: all 0.2s ease;\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  \n  /* 移除之前的after伪元素 */\n  &::after {\n    content: none;\n  }\n  \n  /* 添加激活状态 */\n  &:active {\n    transform: scale(0.92);\n    box-shadow: 0 2px 8px rgba(10, 132, 255, 0.3);\n    background: linear-gradient(135deg, #0060DF, #0A84FF);\n  }\n  \n  /* 发布文字样式 */\n  .fab-text {\n    font-size: 14px;\n    color: white;\n    font-weight: 500;\n    line-height: 1;\n    text-align: center;\n  }\n}\n\n/* 为置顶卡片添加特殊样式 */\n.activity-card {\n  &:has(.top-tag) {\n    border-left: 3px solid #FF9500;\n  }\n}\n\n.status-running {\n  background-color: rgba(52, 199, 89, 0.1);\n  color: #34C759;\n}\n\n.status-upcoming {\n  background-color: rgba(0, 122, 255, 0.1);\n  color: #007AFF;\n}\n\n.status-ended {\n  background-color: rgba(142, 142, 147, 0.1);\n    color: #8E8E93;\n  }\n\n.top-tag {\n  background-color: rgba(255, 149, 0, 0.1);\n  color: #FF9500;\n}\n</style> \n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin/pages/activity/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAuMA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,iBAAiB;AAAA;AAAA,MACjB,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,aAAa;AAAA;AAAA,MACb,SAAS;AAAA,MACT,MAAM;AAAA;AAAA,MAGN,YAAY;AAAA,QACV,OAAO;AAAA,QACP,SAAS;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACR;AAAA;AAAA,MAGD,YAAY;AAAA,QACV,EAAE,OAAO,MAAM,OAAO,MAAO;AAAA,QAC7B,EAAE,OAAO,OAAO,OAAO,UAAW;AAAA,QAClC,EAAE,OAAO,OAAO,OAAO,WAAY;AAAA,QACnC,EAAE,OAAO,OAAO,OAAO,QAAS;AAAA,QAChC,EAAE,OAAO,QAAQ,OAAO,SAAU;AAAA,QAClC,EAAE,OAAO,QAAQ,OAAO,UAAU;AAAA,MACnC;AAAA;AAAA,MAGD,eAAe;AAAA,QACb,EAAE,OAAO,QAAQ,OAAO,MAAO;AAAA,QAC/B,EAAE,OAAO,QAAQ,OAAO,UAAW;AAAA,QACnC,EAAE,OAAO,OAAO,OAAO,SAAU;AAAA,QACjC,EAAE,OAAO,QAAQ,OAAO,UAAW;AAAA,QACnC,EAAE,OAAO,QAAQ,OAAO,WAAY;AAAA,QACpC,EAAE,OAAO,QAAQ,OAAO,SAAS;AAAA,MAClC;AAAA;AAAA,MAGD,cAAc;AAAA,QACZ;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,cAAc;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,cAAc;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,cAAc;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AACP,SAAK,mBAAkB;AACvB,SAAK,iBAAgB;AAAA,EACtB;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,qBAAqB;AACnBA,oBAAAA,MAAI,cAAc;AAAA,QAChB,SAAS,CAAC,QAAQ;AAChB,eAAK,kBAAkB,IAAI;AAAA,QAC7B;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAED,SAAS;AACPA,oBAAAA,MAAY,MAAA,OAAA,8DAAA,QAAQ;AACpBA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA;AAAA,IAED,kBAAkB;AAChBA,oBAAAA,iFAAY,UAAU;AACtBA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,QAAQ,QAAQ,MAAM;AAAA,QACjC,SAAS,CAAC,QAAQ;AAEhBA,wBAAY,MAAA,MAAA,OAAA,8DAAA,SAAS,IAAI,QAAQ;AAAA,QACnC;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAED,oBAAoB;AAClBA,oBAAAA,MAAA,MAAA,OAAA,8DAAY,WAAW;AAGvBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGfA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK;AAAA,UACL,SAAS,MAAM;AACbA,0BAAAA,MAAA,MAAA,OAAA,8DAAY,WAAW;AAAA,UACxB;AAAA,UACD,MAAM,CAAC,QAAQ;AACbA,0BAAA,MAAA,MAAA,SAAA,8DAAc,SAAS,GAAG;AAC1BA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,SAAS;AAAA,cACT,YAAY;AAAA,YACd,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACF,GAAE,GAAG;AAAA,IACP;AAAA;AAAA,IAED,YAAY;AACVA,oBAAAA,MAAY,MAAA,OAAA,8DAAA,QAAQ;AACpB,WAAK,aAAa;AAClB,WAAK,OAAO;AACZ,WAAK,UAAU;AACf,WAAK,iBAAgB;AAAA,IACtB;AAAA;AAAA,IAED,mBAAmB;AACjB,WAAK,UAAU;AAGf,iBAAW,MAAM;AAEf,YAAI,KAAK,SAAS,GAAG;AAEnB,gBAAM,eAAe,KAAK;AAC1B,eAAK,eAAe;AAAA,eACf;AAGLA,yGAAY,cAAc,KAAK,IAAI;AAAA,QACrC;AAEA,aAAK,UAAU;AACf,aAAK,aAAa;AAGlB,YAAI,KAAK,OAAO,GAAG;AACjB,eAAK,UAAU;AAAA,QACjB;AAAA,MACD,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,IAED,wBAAwB;AAEtB,YAAM,gBAAgB;AAAA,QACpB;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,cAAc;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,cAAc;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,cAAc;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,cAAc;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,cAAc;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACR;AAAA;AAIF,aAAO,cAAc,OAAO,cAAY;AAEtC,cAAM,YAAY,KAAK,gBAAgB,SAAS,SAAS,SAAS,KAAK;AAGvE,YAAI,cAAc;AAClB,YAAI,KAAK,eAAe,WAAW;AACjC,wBAAc,SAAS,WAAW;AAAA,QACpC,WAAW,KAAK,eAAe,YAAY;AACzC,wBAAc,SAAS,WAAW;AAAA,QACpC,WAAW,KAAK,eAAe,SAAS;AACtC,wBAAc,SAAS,WAAW;AAAA,QACpC;AAEA,eAAO,aAAa;AAAA,MACtB,CAAC;AAAA,IACF;AAAA;AAAA,IAED,qBAAqB;AACnBA,oBAAAA,iFAAY,UAAU;AACtB,UAAI,CAAC,KAAK;AAAS;AAEnB,WAAK;AACL,WAAK,iBAAgB;AAAA,IACtB;AAAA;AAAA,IAED,UAAU,UAAU;AAClBA,oBAAY,MAAA,MAAA,OAAA,8DAAA,WAAW,QAAQ;AAC/B,WAAK,aAAa;AAClB,WAAK,OAAO;AACZ,WAAK,UAAU;AACf,WAAK,iBAAgB;AAAA,IACtB;AAAA;AAAA,IAED,kBAAkB;AAChBA,oBAAAA,MAAY,MAAA,OAAA,8DAAA,QAAQ;AACpBA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QACzC,SAAS,CAAC,QAAQ;AAChBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAED,oBAAoB;AAClBA,oBAAAA,MAAY,MAAA,OAAA,8DAAA,QAAQ;AAClBA,oBAAAA,MAAI,WAAW;AAAA,QACf,KAAK;AAAA,MACL,CAAC;AAAA,IACJ;AAAA;AAAA,IAED,cAAc,QAAQ;AACpB,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,SAAS;AAAA,QACT,YAAY;AAAA;AAEd,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAED,gBAAgB,OAAO,KAAK;AAC1B,UAAI,CAAC,SAAS,CAAC;AAAK,eAAO;AAE3B,YAAM,YAAY,IAAI,KAAK,KAAK;AAChC,YAAM,UAAU,IAAI,KAAK,GAAG;AAE5B,YAAM,aAAa,UAAU,SAAQ,IAAK;AAC1C,YAAM,WAAW,UAAU;AAC3B,YAAM,WAAW,QAAQ,SAAQ,IAAK;AACtC,YAAM,SAAS,QAAQ;AAEvB,aAAO,GAAG,UAAU,IAAI,QAAQ,MAAM,QAAQ,IAAI,MAAM;AAAA,IACzD;AAAA;AAAA,IAED,mBAAmB,IAAI;AACrBA,oBAAY,MAAA,MAAA,OAAA,8DAAA,cAAc,EAAE;AAC5BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,wDAAwD,EAAE;AAAA,MACjE,CAAC;AAAA,IACF;AAAA;AAAA,IAED,aAAa,IAAI;AACfA,qGAAY,gBAAgB,EAAE;AAC9BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,sDAAsD,EAAE;AAAA,MAC/D,CAAC;AAAA,IACF;AAAA;AAAA,IAED,gBAAgB,IAAI;AAClBA,qGAAY,gBAAgB,EAAE;AAE9B,YAAM,WAAW,KAAK,aAAa,KAAK,UAAQ,KAAK,OAAO,EAAE;AAG9D,YAAM,YAAY,YAAY,SAAS,QAAQ,SAAS;AAExDA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,WAAW,SAAS,QAAQ,QAAQ,OAAO;AAAA,QACtD,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,aAAa,GAAG;AAEtB,iBAAK,gBAAgB,EAAE;AAAA,iBAClB;AACLA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAED,gBAAgB,IAAI;AAClB,YAAM,QAAQ,KAAK,aAAa,UAAU,UAAQ,KAAK,OAAO,EAAE;AAChE,UAAI,UAAU,IAAI;AAEhB,cAAM,QAAQ,CAAC,KAAK,aAAa,KAAK,EAAE;AACxC,aAAK,aAAa,KAAK,EAAE,QAAQ;AAGjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,QAAQ,UAAU;AAAA,UACzB,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAED,cAAc,IAAI;AAChBA,qGAAY,gBAAgB,EAAE;AAC9BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACZ,CAAC;AAGD,iBAAW,MAAM;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACF,GAAE,IAAI;AAAA,IACR;AAAA;AAAA,IAED,oBAAoB,IAAI;AACtBA,oBAAY,MAAA,MAAA,OAAA,8DAAA,cAAc,EAAE;AAC5BA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QACzC,SAAS,CAAC,QAAQ;AAEhB,gBAAM,UAAU,CAAC,YAAY,qBAAqB,gBAAgB,gBAAgB;AAClF,gBAAM,SAAS,QAAQ,IAAI,QAAQ;AAEnC,cAAI,UAAU,KAAK,MAAM,GAAG;AAC1B,iBAAK,MAAM,EAAE,EAAE;AAAA,UACjB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAED,SAAS,IAAI;AACXA,oBAAY,MAAA,MAAA,OAAA,8DAAA,YAAY,EAAE;AAC1BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,sDAAsD,EAAE;AAAA,MAC/D,CAAC;AAAA,IACF;AAAA;AAAA,IAED,kBAAkB,IAAI;AACpBA,oBAAY,MAAA,MAAA,OAAA,8DAAA,YAAY,EAAE;AAC1BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACjBA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO;AAAA,cACT,MAAM;AAAA,YACR,CAAC;AAAA,UACD;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAED,aAAa,IAAI;AACfA,oBAAY,MAAA,MAAA,OAAA,8DAAA,YAAY,EAAE;AAC1BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACjBA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO;AAAA,cACT,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAED,eAAe,IAAI;AACjBA,oBAAY,MAAA,MAAA,OAAA,8DAAA,YAAY,EAAE;AAC1BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAED,WAAW,WAAW;AACpBA,oBAAY,MAAA,MAAA,OAAA,8DAAA,WAAW,SAAS;AAChC,WAAK,cAAc;AACnB,WAAK,OAAO;AACZ,WAAK,UAAU;AACf,WAAK,iBAAgB;AAAA,IACtB;AAAA;AAAA,IAED,YAAY,MAAM;AAChB,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,UAAU;AAAA;AAEZ,aAAO,QAAQ,IAAI,KAAK;AAAA,IAC1B;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpqBA,GAAG,WAAW,eAAe;"}