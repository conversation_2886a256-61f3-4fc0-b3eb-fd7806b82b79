<view class="tasks-container"><view class="navbar"><view class="navbar-back" bindtap="{{a}}"><view class="back-icon"></view></view><text class="navbar-title">会员任务</text><view class="navbar-right"><view class="help-icon" bindtap="{{b}}">?</view></view></view><view class="overview-section"><view class="overview-header"><text class="section-title">任务概览</text><view class="date-picker" bindtap="{{d}}"><text class="date-text">{{c}}</text><view class="date-icon"></view></view></view><view class="stats-cards"><view class="stats-card"><view class="card-value">{{e}}</view><view class="card-label">总任务数</view></view><view class="stats-card"><view class="card-value">{{f}}</view><view class="card-label">进行中任务</view></view><view class="stats-card"><view class="card-value">{{g}}%</view><view class="card-label">任务完成率</view><view class="{{['card-trend', i]}}"><view class="trend-arrow"></view><text class="trend-value">{{h}}</text></view></view><view class="stats-card"><view class="card-value">{{j}}%</view><view class="card-label">会员参与率</view><view class="{{['card-trend', l]}}"><view class="trend-arrow"></view><text class="trend-value">{{k}}</text></view></view></view></view><view class="tabs-section"><scroll-view scroll-x class="tabs-scroll" show-scrollbar="false"><view class="tabs"><view wx:for="{{m}}" wx:for-item="tab" wx:key="b" class="{{['tab-item', tab.c && 'active']}}" bindtap="{{tab.d}}">{{tab.a}}</view></view></scroll-view></view><view class="tasks-section"><view class="section-header"><text class="section-title">{{n}}</text><view class="add-btn" bindtap="{{o}}"><text class="btn-text">添加任务</text><view class="plus-icon"></view></view></view><view class="tasks-list"><view wx:for="{{p}}" wx:for-item="task" wx:key="n" class="task-item" bindtap="{{task.o}}"><view class="task-icon" style="{{'background:' + task.e}}"><svg wx:if="{{q}}" u-s="{{['d']}}" class="svg-icon" style="{{'fill:' + task.c}}" u-i="{{task.d}}" bind:__l="__l" u-p="{{q}}"><path wx:if="{{task.b}}" u-i="{{task.a}}" bind:__l="__l" u-p="{{task.b}}"></path></svg></view><view class="task-content"><view class="task-header"><text class="task-name">{{task.f}}</text><text class="{{['task-status', task.h]}}">{{task.g}}</text></view><view class="task-desc">{{task.i}}</view><view class="task-meta"><view class="task-reward"><text class="reward-label">奖励: </text><text class="reward-value">{{task.j}}</text></view><view class="task-period"><text class="period-label">周期: </text><text class="period-value">{{task.k}}</text></view></view></view><view class="task-action"><switch checked="{{task.l}}" bindchange="{{task.m}}" color="#F6D365"/></view></view></view></view><view class="floating-action-button" bindtap="{{r}}"><view class="fab-icon">+</view></view></view>