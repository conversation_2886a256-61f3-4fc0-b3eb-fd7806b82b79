
.red-packet-detail {
  padding: 30rpx;
  background: #fff;
  min-height: 100vh;
}
.header {
  text-align: center;
  padding: 40rpx 0;
  background: linear-gradient(45deg, #ff4d4f, #ff7875);
  border-radius: 20rpx;
  color: #fff;
  margin-bottom: 30rpx;
}
.title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.amount {
  font-size: 60rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.status {
  font-size: 28rpx;
  opacity: 0.8;
}
.info-section {
  background: #f8f8f8;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.info-item:last-child {
  margin-bottom: 0;
}
.label {
  color: #666;
  font-size: 28rpx;
}
.value {
  color: #333;
  font-size: 28rpx;
}
.records-section {
  background: #f8f8f8;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.record-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}
.record-item:last-child {
  border-bottom: none;
}
.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.user-info {
  flex: 1;
}
.nickname {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
}
.time {
  font-size: 24rpx;
  color: #999;
}
.amount {
  font-size: 32rpx;
  color: #ff4d4f;
  font-weight: bold;
}
.action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  padding: 20rpx 30rpx;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.share-btn, .grab-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 32rpx;
  margin: 0 10rpx;
}
.share-btn {
  background: #f5f5f5;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}
.share-btn .iconfont {
  margin-right: 6rpx;
  font-size: 28rpx;
}
.grab-btn {
  background: linear-gradient(45deg, #ff4d4f, #ff7875);
  color: #fff;
}
.grab-btn[disabled] {
  background: #ccc;
  color: #fff;
}
