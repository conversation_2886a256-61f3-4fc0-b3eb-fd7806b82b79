<template>
  <view class="product-detail-page">
    <!-- 顶部导航 -->
    <view class="nav-bar">
      <view class="nav-back" @click="goBack">
        <text class="iconfont icon-arrow-left"></text>
      </view>
      <view class="nav-title">商品详情</view>
      <view class="nav-share" @click="shareProduct">
        <text class="iconfont icon-share"></text>
      </view>
    </view>

    <!-- 商品图片轮播 -->
    <view class="product-images">
      <swiper class="swiper" indicator-dots circular>
        <swiper-item v-for="(image, index) in productInfo.images" :key="index">
          <image :src="image" mode="aspectFill" class="product-image" />
        </swiper-item>
      </swiper>
      <view class="promotion-badge">推广商品</view>
    </view>

    <!-- 商品信息 -->
    <view class="product-info">
      <view class="product-title">{{ productInfo.title }}</view>
      <view class="product-subtitle">{{ productInfo.subtitle }}</view>
      <view class="price-section">
        <view class="current-price">¥{{ productInfo.price }}</view>
        <view class="original-price">¥{{ productInfo.originalPrice }}</view>
        <view class="commission-rate">佣金{{ productInfo.commissionRate }}%</view>
      </view>
    </view>

    <!-- 推广收益预览 -->
    <view class="earnings-preview">
      <view class="section-title">推广收益</view>
      <view class="earnings-item">
        <text>一级佣金</text>
        <text class="amount">¥{{ calculateCommission(1) }}</text>
      </view>
      <view class="earnings-item">
        <text>二级佣金</text>
        <text class="amount">¥{{ calculateCommission(2) }}</text>
      </view>
    </view>

    <!-- 商品详情 -->
    <view class="product-details">
      <view class="section-title">商品详情</view>
      <rich-text :nodes="productInfo.content"></rich-text>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <button class="btn-secondary" @click="generatePoster">生成海报</button>
      <button class="btn-primary" @click="shareToWeChat">立即推广</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const productInfo = ref({})

const calculateCommission = (level) => {
  const rate = level === 1 ? 0.15 : 0.08
  return (productInfo.value.price * rate).toFixed(2)
}

const shareProduct = () => {
  uni.navigateTo({
    url: '/subPackages/activity-showcase/pages/distribution/products/share/index'
  })
}

onMounted(() => {
  // 获取商品详情
})
</script>

<style lang="scss" scoped>
.product-detail-page {
  background: #f5f5f5;
  min-height: 100vh;
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 100;
}

.product-images {
  position: relative;
  height: 500rpx;
  
  .swiper {
    height: 100%;
  }
  
  .product-image {
    width: 100%;
    height: 100%;
  }
  
  .promotion-badge {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
    color: #fff;
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
  }
}

.product-info {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .product-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }
  
  .price-section {
    display: flex;
    align-items: center;
    margin-top: 20rpx;
    
    .current-price {
      font-size: 36rpx;
      color: #ff6b6b;
      font-weight: bold;
      margin-right: 20rpx;
    }
    
    .original-price {
      font-size: 28rpx;
      color: #999;
      text-decoration: line-through;
      margin-right: 20rpx;
    }
    
    .commission-rate {
      background: #ff6b6b;
      color: #fff;
      padding: 4rpx 12rpx;
      border-radius: 12rpx;
      font-size: 24rpx;
    }
  }
}

.earnings-preview {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .earnings-item {
    display: flex;
    justify-content: space-between;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .amount {
      color: #ff6b6b;
      font-weight: bold;
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 20rpx 30rpx;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
  
  button {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    margin: 0 10rpx;
    
    &.btn-secondary {
      background: #f0f0f0;
      color: #666;
    }
    
    &.btn-primary {
      background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
      color: #fff;
    }
  }
}
</style>