/**
 * 磁州前台响应式工具
 * 提供屏幕尺寸检测、响应式布局、设备适配等功能
 */

// 断点配置
const BREAKPOINTS = {
  xs: 0,      // 超小屏幕
  sm: 480,    // 小屏幕
  md: 768,    // 中等屏幕
  lg: 1024,   // 大屏幕
  xl: 1200,   // 超大屏幕
  xxl: 1600   // 超超大屏幕
}

// 设备类型
const DEVICE_TYPES = {
  MOBILE: 'mobile',
  TABLET: 'tablet',
  DESKTOP: 'desktop'
}

// 响应式管理类
class ResponsiveManager {
  constructor() {
    this.currentBreakpoint = 'sm'
    this.deviceType = DEVICE_TYPES.MOBILE
    this.screenInfo = {}
    this.listeners = []
    this.init()
  }
  
  // 初始化响应式管理器
  init() {
    this.updateScreenInfo()
    this.setupListeners()
  }
  
  // 更新屏幕信息
  updateScreenInfo() {
    try {
      const systemInfo = uni.getSystemInfoSync()
      this.screenInfo = {
        screenWidth: systemInfo.screenWidth,
        screenHeight: systemInfo.screenHeight,
        windowWidth: systemInfo.windowWidth,
        windowHeight: systemInfo.windowHeight,
        pixelRatio: systemInfo.pixelRatio,
        statusBarHeight: systemInfo.statusBarHeight,
        safeArea: systemInfo.safeArea,
        safeAreaInsets: systemInfo.safeAreaInsets,
        platform: systemInfo.platform,
        system: systemInfo.system,
        brand: systemInfo.brand,
        model: systemInfo.model
      }
      
      // 更新当前断点
      this.updateBreakpoint()
      
      // 更新设备类型
      this.updateDeviceType()
      
    } catch (e) {
      console.error('获取屏幕信息失败:', e)
    }
  }
  
  // 更新断点
  updateBreakpoint() {
    const width = this.screenInfo.windowWidth || this.screenInfo.screenWidth
    let newBreakpoint = 'xs'
    
    Object.keys(BREAKPOINTS).reverse().forEach(bp => {
      if (width >= BREAKPOINTS[bp]) {
        newBreakpoint = bp
        return false
      }
    })
    
    if (newBreakpoint !== this.currentBreakpoint) {
      const oldBreakpoint = this.currentBreakpoint
      this.currentBreakpoint = newBreakpoint
      this.notifyListeners('breakpoint', newBreakpoint, oldBreakpoint)
    }
  }
  
  // 更新设备类型
  updateDeviceType() {
    const width = this.screenInfo.windowWidth || this.screenInfo.screenWidth
    let newDeviceType = DEVICE_TYPES.MOBILE
    
    if (width >= BREAKPOINTS.lg) {
      newDeviceType = DEVICE_TYPES.DESKTOP
    } else if (width >= BREAKPOINTS.md) {
      newDeviceType = DEVICE_TYPES.TABLET
    }
    
    if (newDeviceType !== this.deviceType) {
      const oldDeviceType = this.deviceType
      this.deviceType = newDeviceType
      this.notifyListeners('deviceType', newDeviceType, oldDeviceType)
    }
  }
  
  // 设置监听器
  setupListeners() {
    // 监听窗口大小变化（主要用于H5）
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', () => {
        this.updateScreenInfo()
      })
      
      // 监听设备方向变化
      window.addEventListener('orientationchange', () => {
        setTimeout(() => {
          this.updateScreenInfo()
        }, 100)
      })
    }
  }
  
  // 获取当前断点
  getCurrentBreakpoint() {
    return this.currentBreakpoint
  }
  
  // 获取设备类型
  getDeviceType() {
    return this.deviceType
  }
  
  // 获取屏幕信息
  getScreenInfo() {
    return { ...this.screenInfo }
  }
  
  // 检查是否匹配断点
  matchBreakpoint(breakpoint) {
    const currentWidth = this.screenInfo.windowWidth || this.screenInfo.screenWidth
    return currentWidth >= BREAKPOINTS[breakpoint]
  }
  
  // 检查是否在断点范围内
  matchBreakpointRange(minBreakpoint, maxBreakpoint = null) {
    const currentWidth = this.screenInfo.windowWidth || this.screenInfo.screenWidth
    const minWidth = BREAKPOINTS[minBreakpoint] || 0
    const maxWidth = maxBreakpoint ? BREAKPOINTS[maxBreakpoint] : Infinity
    
    return currentWidth >= minWidth && currentWidth < maxWidth
  }
  
  // 检查是否为移动设备
  isMobile() {
    return this.deviceType === DEVICE_TYPES.MOBILE
  }
  
  // 检查是否为平板设备
  isTablet() {
    return this.deviceType === DEVICE_TYPES.TABLET
  }
  
  // 检查是否为桌面设备
  isDesktop() {
    return this.deviceType === DEVICE_TYPES.DESKTOP
  }
  
  // 检查是否为小屏幕
  isSmallScreen() {
    return this.matchBreakpointRange('xs', 'md')
  }
  
  // 检查是否为大屏幕
  isLargeScreen() {
    return this.matchBreakpoint('lg')
  }
  
  // 检查是否为横屏
  isLandscape() {
    return this.screenInfo.screenWidth > this.screenInfo.screenHeight
  }
  
  // 检查是否为竖屏
  isPortrait() {
    return this.screenInfo.screenWidth <= this.screenInfo.screenHeight
  }
  
  // 获取安全区域信息
  getSafeArea() {
    return this.screenInfo.safeArea || {
      left: 0,
      right: this.screenInfo.screenWidth,
      top: this.screenInfo.statusBarHeight || 0,
      bottom: this.screenInfo.screenHeight,
      width: this.screenInfo.screenWidth,
      height: this.screenInfo.screenHeight - (this.screenInfo.statusBarHeight || 0)
    }
  }
  
  // 获取安全区域内边距
  getSafeAreaInsets() {
    return this.screenInfo.safeAreaInsets || {
      top: this.screenInfo.statusBarHeight || 0,
      right: 0,
      bottom: 0,
      left: 0
    }
  }
  
  // 计算响应式值
  getResponsiveValue(values) {
    // values 格式: { xs: value1, sm: value2, md: value3, ... }
    // 或者数组格式: [xs_value, sm_value, md_value, ...]
    
    if (Array.isArray(values)) {
      const breakpointKeys = Object.keys(BREAKPOINTS)
      const valueMap = {}
      values.forEach((value, index) => {
        if (breakpointKeys[index]) {
          valueMap[breakpointKeys[index]] = value
        }
      })
      values = valueMap
    }
    
    let result = values.xs || values[Object.keys(values)[0]]
    
    Object.keys(BREAKPOINTS).forEach(bp => {
      if (this.matchBreakpoint(bp) && values[bp] !== undefined) {
        result = values[bp]
      }
    })
    
    return result
  }
  
  // 计算响应式尺寸
  getResponsiveSize(baseSize, scale = {}) {
    const defaultScale = {
      xs: 0.8,
      sm: 0.9,
      md: 1,
      lg: 1.1,
      xl: 1.2,
      xxl: 1.3
    }
    
    const currentScale = scale[this.currentBreakpoint] || defaultScale[this.currentBreakpoint] || 1
    return Math.round(baseSize * currentScale)
  }
  
  // 计算网格列数
  getGridColumns(maxColumns = 12, minColumnWidth = 200) {
    const availableWidth = this.screenInfo.windowWidth || this.screenInfo.screenWidth
    const calculatedColumns = Math.floor(availableWidth / minColumnWidth)
    return Math.min(calculatedColumns, maxColumns)
  }
  
  // 添加监听器
  addListener(callback) {
    if (typeof callback === 'function') {
      this.listeners.push(callback)
    }
  }
  
  // 移除监听器
  removeListener(callback) {
    const index = this.listeners.indexOf(callback)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }
  
  // 通知监听器
  notifyListeners(type, newValue, oldValue) {
    this.listeners.forEach(callback => {
      try {
        callback({
          type,
          newValue,
          oldValue,
          breakpoint: this.currentBreakpoint,
          deviceType: this.deviceType,
          screenInfo: this.screenInfo
        })
      } catch (e) {
        console.error('响应式监听器执行失败:', e)
      }
    })
  }
  
  // 获取平台特定样式
  getPlatformStyles() {
    const platform = this.screenInfo.platform
    const styles = {}
    
    switch (platform) {
      case 'ios':
        styles.statusBarHeight = this.screenInfo.statusBarHeight || 44
        styles.navigationBarHeight = 44
        styles.tabBarHeight = 83
        break
      case 'android':
        styles.statusBarHeight = this.screenInfo.statusBarHeight || 24
        styles.navigationBarHeight = 48
        styles.tabBarHeight = 56
        break
      default:
        styles.statusBarHeight = this.screenInfo.statusBarHeight || 0
        styles.navigationBarHeight = 44
        styles.tabBarHeight = 50
    }
    
    return styles
  }
  
  // 获取适配后的字体大小
  getAdaptiveFontSize(baseFontSize) {
    const screenWidth = this.screenInfo.windowWidth || this.screenInfo.screenWidth
    const designWidth = 375 // 设计稿宽度
    const scale = screenWidth / designWidth
    
    // 限制缩放范围
    const minScale = 0.8
    const maxScale = 1.2
    const finalScale = Math.max(minScale, Math.min(maxScale, scale))
    
    return Math.round(baseFontSize * finalScale)
  }
  
  // 获取适配后的尺寸
  getAdaptiveSize(baseSize) {
    return this.getAdaptiveFontSize(baseSize)
  }
}

// 创建全局响应式管理器实例
const responsiveManager = new ResponsiveManager()

// 导出工具函数
export {
  responsiveManager,
  BREAKPOINTS,
  DEVICE_TYPES
}

// 便捷方法
export const getCurrentBreakpoint = () => responsiveManager.getCurrentBreakpoint()
export const getDeviceType = () => responsiveManager.getDeviceType()
export const getScreenInfo = () => responsiveManager.getScreenInfo()
export const matchBreakpoint = (breakpoint) => responsiveManager.matchBreakpoint(breakpoint)
export const isMobile = () => responsiveManager.isMobile()
export const isTablet = () => responsiveManager.isTablet()
export const isDesktop = () => responsiveManager.isDesktop()
export const isSmallScreen = () => responsiveManager.isSmallScreen()
export const isLargeScreen = () => responsiveManager.isLargeScreen()
export const getResponsiveValue = (values) => responsiveManager.getResponsiveValue(values)
export const getResponsiveSize = (baseSize, scale) => responsiveManager.getResponsiveSize(baseSize, scale)
export const addResponsiveListener = (callback) => responsiveManager.addListener(callback)
export const removeResponsiveListener = (callback) => responsiveManager.removeListener(callback)

// 默认导出
export default responsiveManager
