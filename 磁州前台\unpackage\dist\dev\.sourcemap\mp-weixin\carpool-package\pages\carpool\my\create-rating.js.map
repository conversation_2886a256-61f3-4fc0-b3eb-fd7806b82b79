{"version": 3, "file": "create-rating.js", "sources": ["carpool-package/pages/carpool/my/create-rating.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/Y2FycG9vbC1wYWNrYWdlXHBhZ2VzXGNhcnBvb2xcbXlcY3JlYXRlLXJhdGluZy52dWU"], "sourcesContent": ["<template>\n  <view class=\"rating-container\">\n    <!-- 自定义导航栏 - 完全重构 -->\n    <view style=\"position: fixed; top: 0; left: 0; right: 0; height: calc(90px - 4px); background: linear-gradient(135deg, #0A84FF, #0040DD); z-index: 100; padding-top: var(--status-bar-height, 40px);\">\n      <!-- 返回键 - 绝对定位 (向下10px 向右8px) -->\n      <view style=\"position: absolute; left: 8px; top: calc(var(--status-bar-height, 40px) + 10px); width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;\" @click=\"goBack\">\n        <image src=\"/static/images/tabbar/最新返回键.png\" style=\"width: 24px; height: 24px; filter: brightness(0) invert(1);\"></image>\n      </view>\n      \n      <!-- 标题 - 绝对定位 (向下移动10px) -->\n      <view style=\"position: absolute; left: 0; right: 0; top: calc(var(--status-bar-height, 40px) + 15px); text-align: center;\">\n        <text style=\"font-size: 18px; color: white; font-weight: bold;\">评价司机</text>\n      </view>\n    </view>\n    \n    <!-- 占位元素确保内容不被导航栏遮挡 -->\n    <view style=\"height: calc(86px + var(--status-bar-height, 40px));\"></view>\n    \n    <!-- 司机信息 -->\n    <view class=\"driver-info\">\n      <view class=\"driver-avatar-container\">\n        <image class=\"driver-avatar\" :src=\"driverInfo.avatar\" mode=\"aspectFill\"></image>\n      </view>\n      <view class=\"driver-name\">{{driverInfo.name}}</view>\n      <view class=\"driver-car\">{{driverInfo.carModel}} · {{driverInfo.carColor}} · {{driverInfo.carNumber}}</view>\n    </view>\n    \n    <!-- 行程信息 -->\n    <view class=\"trip-info\">\n      <view class=\"trip-route\">\n        <view class=\"route-point\">\n          <view class=\"point start\"></view>\n          <text class=\"point-text\">{{tripInfo.startLocation}}</text>\n        </view>\n        <view class=\"route-line\"></view>\n        <view class=\"route-point\">\n          <view class=\"point end\"></view>\n          <text class=\"point-text\">{{tripInfo.endLocation}}</text>\n        </view>\n      </view>\n      <view class=\"trip-time\">\n        <text class=\"time-label\">出发时间：</text>\n        <text class=\"time-value\">{{tripInfo.departureTime}}</text>\n      </view>\n      <view class=\"trip-date\">\n        <text class=\"date-label\">出发日期：</text>\n        <text class=\"date-value\">{{tripInfo.departureDate}}</text>\n      </view>\n    </view>\n    \n    <!-- 评分部分 -->\n    <view class=\"rating-section\">\n      <view class=\"section-title\">行程评分</view>\n      <view class=\"star-rating\">\n        <view \n          v-for=\"(item, index) in 5\" \n          :key=\"index\" \n          class=\"star-item\"\n          @click=\"selectRating(index + 1)\"\n        >\n          <image \n            :src=\"index < rating ? '/static/images/icons/star-filled.png' : '/static/images/icons/star-empty.png'\" \n            class=\"star-icon\"\n          ></image>\n        </view>\n      </view>\n      <view class=\"rating-text\">{{ratingText}}</view>\n    </view>\n    \n    <!-- 评价标签 -->\n    <view class=\"tags-section\">\n      <view class=\"section-title\">评价标签</view>\n      <view class=\"tags-container\">\n        <view \n          v-for=\"(tag, index) in tags\" \n          :key=\"index\" \n          class=\"tag-item\"\n          :class=\"{ 'selected': selectedTags.includes(tag) }\"\n          @click=\"toggleTag(tag)\"\n        >\n          {{tag}}\n        </view>\n      </view>\n    </view>\n    \n    <!-- 评价内容 -->\n    <view class=\"comment-section\">\n      <view class=\"section-title\">评价内容</view>\n      <textarea \n        class=\"comment-input\" \n        v-model=\"comment\" \n        placeholder=\"请输入您对本次行程的评价（选填）\" \n        maxlength=\"200\"\n      ></textarea>\n      <view class=\"word-count\">{{comment.length}}/200</view>\n    </view>\n    \n    <!-- 匿名评价 -->\n    <view class=\"anonymous-section\">\n      <label class=\"checkbox-label\">\n        <checkbox :checked=\"isAnonymous\" @change=\"toggleAnonymous\" color=\"#0A84FF\" style=\"transform:scale(0.7)\" />\n        <text class=\"anonymous-text\">匿名评价</text>\n      </label>\n    </view>\n    \n    <!-- 提交按钮 -->\n    <button class=\"submit-btn\" :disabled=\"rating === 0\" @click=\"submitRating\">提交评价</button>\n    \n    <!-- 底部安全区域 -->\n    <view class=\"safe-area-bottom\"></view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue';\nimport { onLoad } from '@dcloudio/uni-app';\n\n// 司机信息\nconst driverInfo = ref({\n  id: '',\n  name: '',\n  avatar: '/static/images/avatar/user1.png',\n  carModel: '',\n  carColor: '',\n  carNumber: '',\n  phoneNumber: ''\n});\n\n// 行程信息\nconst tripInfo = ref({\n  id: '',\n  startLocation: '',\n  endLocation: '',\n  departureTime: '',\n  departureDate: '',\n  status: ''\n});\n\n// 评分相关\nconst rating = ref(0);\nconst tags = ref(['准时出发', '路线合理', '驾驶平稳', '态度友好', '车内整洁', '价格合理']);\nconst selectedTags = ref([]);\nconst comment = ref('');\nconst isAnonymous = ref(false);\n\n// 计算属性：评分文本\nconst ratingText = computed(() => {\n  const texts = ['', '很差', '较差', '一般', '不错', '很好'];\n  return rating.value > 0 ? texts[rating.value] : '请选择评分';\n});\n\n// 页面加载\nonMounted(() => {\n  // 只做一些基本初始化\n  console.log('页面已挂载');\n});\n\n// 使用onLoad处理页面参数（uni-app特有生命周期）\nonLoad((options) => {\n  console.log('获取到页面参数:', options);\n  \n  // 从路由参数获取司机和拼车信息\n  if (options && Object.keys(options).length > 0) {\n    // 如果有driverId和carpoolId，说明是从拼车详情页跳转来的\n    if (options.driverId && options.phoneNumber) {\n      driverInfo.value.id = options.driverId;\n      \n      // 从本地存储中查找司机信息\n      const contactHistory = uni.getStorageSync('contactHistory') || [];\n      const contactRecord = contactHistory.find(item => \n        item.driverId === options.driverId && \n        item.phoneNumber === options.phoneNumber\n      );\n      \n      if (contactRecord) {\n        // 填充行程信息\n        tripInfo.value = {\n          id: options.carpoolId || contactRecord.carpoolId,\n          startLocation: decodeURIComponent(options.startLocation || contactRecord.startLocation),\n          endLocation: decodeURIComponent(options.endLocation || contactRecord.endLocation),\n          departureTime: options.departureTime || contactRecord.departureTime,\n          departureDate: options.departureDate || contactRecord.departureDate,\n          status: 'completed'\n        };\n        \n        // 尝试从本地缓存加载司机信息\n        const driversInfo = uni.getStorageSync('driversInfo') || {};\n        if (driversInfo[options.driverId]) {\n          driverInfo.value = { ...driverInfo.value, ...driversInfo[options.driverId] };\n        } else {\n          // 如果没有司机信息，设置默认值并尝试从电话号码获取\n          driverInfo.value.name = '司机' + options.phoneNumber.substr(-4);\n          driverInfo.value.phoneNumber = options.phoneNumber;\n          \n          // 实际应用中应该调用API获取司机信息\n          loadDriverInfoByPhone(options.phoneNumber);\n        }\n      } else {\n        // 没有找到联系记录，但有参数，直接使用参数创建信息\n        driverInfo.value.id = options.driverId;\n        driverInfo.value.name = '司机' + options.phoneNumber.substr(-4);\n        driverInfo.value.phoneNumber = options.phoneNumber;\n        \n        tripInfo.value = {\n          id: options.carpoolId || '',\n          startLocation: decodeURIComponent(options.startLocation || ''),\n          endLocation: decodeURIComponent(options.endLocation || ''),\n          departureTime: options.departureTime || '',\n          departureDate: options.departureDate || '',\n          status: 'completed'\n        };\n        \n        // 尝试加载司机信息\n        loadDriverInfoByPhone(options.phoneNumber);\n      }\n    } else {\n      // 测试数据\n      loadMockData();\n    }\n  } else {\n    // 测试数据\n    loadMockData();\n    \n    // 输出日志\n    console.error('未获取到页面参数，使用测试数据');\n  }\n});\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n\n// 加载行程信息\nconst loadTripInfo = (tripId) => {\n  // 实际应用中应该从API获取数据\n  // 这里使用模拟数据\n  console.log('加载行程信息，ID:', tripId);\n};\n\n// 加载司机信息\nconst loadDriverInfo = (driverId) => {\n  // 实际应用中应该从API获取数据\n  // 这里使用模拟数据\n  console.log('加载司机信息，ID:', driverId);\n};\n\n// 加载模拟数据（仅用于开发测试）\nconst loadMockData = () => {\n  driverInfo.value = {\n    id: 'D12345',\n    name: '张师傅',\n    avatar: '/static/images/avatar/user1.png',\n    carModel: '本田雅阁',\n    carColor: '白色',\n    carNumber: '冀D·12345',\n    phoneNumber: '1234567890'\n  };\n  \n  tripInfo.value = {\n    id: 'T67890',\n    startLocation: '磁县火车站',\n    endLocation: '邯郸东站',\n    departureTime: '14:30',\n    departureDate: '2023-06-15',\n    status: 'completed'\n  };\n};\n\n// 选择评分\nconst selectRating = (score) => {\n  rating.value = score;\n};\n\n// 切换标签选择\nconst toggleTag = (tag) => {\n  if (selectedTags.value.includes(tag)) {\n    selectedTags.value = selectedTags.value.filter(item => item !== tag);\n  } else {\n    if (selectedTags.value.length < 3) {\n      selectedTags.value.push(tag);\n    } else {\n      uni.showToast({\n        title: '最多选择3个标签',\n        icon: 'none'\n      });\n    }\n  }\n};\n\n// 切换匿名评价\nconst toggleAnonymous = (e) => {\n  isAnonymous.value = e.detail.value;\n};\n\n// 提交评价\nconst submitRating = () => {\n  if (rating.value === 0) {\n    uni.showToast({\n      title: '请选择评分',\n      icon: 'none'\n    });\n    return;\n  }\n  \n  // 构建评价数据\n  const ratingData = {\n    driverId: driverInfo.value.id,\n    phoneNumber: driverInfo.value.phoneNumber,\n    carpoolId: tripInfo.value.id,\n    rating: rating.value,\n    tags: selectedTags.value,\n    comment: comment.value,\n    isAnonymous: isAnonymous.value,\n    createTime: new Date().toISOString(),\n    startLocation: tripInfo.value.startLocation,\n    endLocation: tripInfo.value.endLocation,\n    departureTime: tripInfo.value.departureTime,\n    departureDate: tripInfo.value.departureDate\n  };\n  \n  console.log('提交评价数据:', ratingData);\n  \n  // 保存到本地存储\n  const ratingsHistory = uni.getStorageSync('ratingsHistory') || [];\n  \n  // 检查是否已有相同评价，如果有则更新\n  const existingIndex = ratingsHistory.findIndex(item => \n    item.driverId === ratingData.driverId && \n    item.carpoolId === ratingData.carpoolId\n  );\n  \n  if (existingIndex !== -1) {\n    ratingsHistory[existingIndex] = ratingData;\n  } else {\n    ratingsHistory.push(ratingData);\n  }\n  \n  uni.setStorageSync('ratingsHistory', ratingsHistory);\n  \n  // 实际应用中应该调用API提交评价\n  uni.showLoading({\n    title: '提交中...'\n  });\n  \n  // 模拟API调用\n  setTimeout(() => {\n    uni.hideLoading();\n    \n    uni.showToast({\n      title: '评价成功',\n      icon: 'success'\n    });\n    \n    // 延迟返回\n    setTimeout(() => {\n      uni.navigateBack();\n    }, 1500);\n  }, 1000);\n};\n\n// 添加通过电话号码加载司机信息的方法\nconst loadDriverInfoByPhone = (phoneNumber) => {\n  // 实际应用中应该调用API获取司机信息\n  // 这里使用模拟数据\n  console.log('通过电话号码加载司机信息:', phoneNumber);\n  \n  // 模拟API调用\n  setTimeout(() => {\n    // 模拟数据\n    const driverInfoData = {\n      name: '司机' + phoneNumber.substr(-4),\n      avatar: '/static/images/avatar/user1.png',\n      carModel: '未知车型',\n      carColor: '未知颜色',\n      carNumber: '未知车牌',\n      phoneNumber: phoneNumber\n    };\n    \n    // 更新司机信息\n    driverInfo.value = { ...driverInfo.value, ...driverInfoData };\n    \n    // 保存到本地缓存\n    const driversInfo = uni.getStorageSync('driversInfo') || {};\n    driversInfo[driverInfo.value.id] = driverInfoData;\n    uni.setStorageSync('driversInfo', driversInfo);\n  }, 500);\n};\n</script>\n\n<style lang=\"scss\">\n.rating-container {\n  min-height: 100vh;\n  background-color: #F5F8FC;\n  position: relative;\n  padding-top: calc(90rpx + var(--status-bar-height, 40px));\n  padding-bottom: 40rpx;\n}\n\n/* 自定义导航栏 */\n.custom-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #0A84FF, #0040DD);\n  z-index: 100;\n  display: flex;\n  flex-direction: column;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);\n  padding-top: var(--status-bar-height, 40px);\n  /* 增加高度确保有足够空间 */\n  min-height: 120rpx;\n}\n\n.navbar-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 90rpx;\n  padding: 0 24rpx;\n  /* 确保内容有足够的灵活性 */\n  min-height: 90rpx;\n  flex-wrap: nowrap;\n}\n\n.navbar-left {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 40rpx;\n  height: 40rpx;\n}\n\n.navbar-title {\n  font-size: 38rpx;\n  font-weight: 600;\n  color: #ffffff;\n  letter-spacing: 1rpx;\n  text-align: center;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n.navbar-right {\n  width: 60rpx;\n  height: 60rpx;\n}\n\n/* 司机信息 */\n.driver-info {\n  margin: 20rpx 32rpx;\n  background-color: #FFFFFF;\n  border-radius: 16rpx;\n  padding: 24rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.driver-avatar-container {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 60rpx;\n  overflow: hidden;\n  margin-bottom: 16rpx;\n  border: 2rpx solid #F2F2F7;\n}\n\n.driver-avatar {\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n}\n\n.driver-name {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333333;\n  margin-bottom: 8rpx;\n}\n\n.driver-car {\n  font-size: 24rpx;\n  color: #666666;\n}\n\n/* 行程信息 */\n.trip-info {\n  margin: 0 32rpx 32rpx;\n  background-color: #FFFFFF;\n  border-radius: 16rpx;\n  padding: 24rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.trip-route {\n  padding: 16rpx 0;\n}\n\n.route-point {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.point {\n  width: 20rpx;\n  height: 20rpx;\n  border-radius: 10rpx;\n  margin-right: 16rpx;\n}\n\n.point.start {\n  background-color: #34C759;\n}\n\n.point.end {\n  background-color: #FF3B30;\n}\n\n.point-text {\n  font-size: 28rpx;\n  color: #333333;\n}\n\n.route-line {\n  width: 2rpx;\n  height: 40rpx;\n  background-color: #DDDDDD;\n  margin-left: 9rpx;\n  margin-bottom: 16rpx;\n}\n\n.trip-time, .trip-date {\n  display: flex;\n  margin-top: 8rpx;\n}\n\n.time-label, .date-label {\n  font-size: 26rpx;\n  color: #666666;\n  width: 150rpx;\n}\n\n.time-value, .date-value {\n  font-size: 26rpx;\n  color: #333333;\n  font-weight: 500;\n}\n\n/* 评分部分 */\n.rating-section, .tags-section, .comment-section {\n  margin: 0 32rpx 32rpx;\n  background-color: #FFFFFF;\n  border-radius: 16rpx;\n  padding: 24rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.section-title {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #333333;\n  margin-bottom: 20rpx;\n}\n\n.star-rating {\n  display: flex;\n  justify-content: center;\n  margin-bottom: 16rpx;\n}\n\n.star-item {\n  padding: 0 10rpx;\n}\n\n.star-icon {\n  width: 60rpx;\n  height: 60rpx;\n}\n\n.rating-text {\n  text-align: center;\n  font-size: 26rpx;\n  color: #666666;\n}\n\n/* 评价标签 */\n.tags-container {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.tag-item {\n  padding: 12rpx 24rpx;\n  background-color: #F5F5F5;\n  border-radius: 30rpx;\n  margin-right: 16rpx;\n  margin-bottom: 16rpx;\n  font-size: 26rpx;\n  color: #666666;\n}\n\n.tag-item.selected {\n  background-color: #E1F0FF;\n  color: #0A84FF;\n  border: 1rpx solid #0A84FF;\n}\n\n/* 评价内容 */\n.comment-input {\n  width: 100%;\n  height: 200rpx;\n  background-color: #F5F5F5;\n  border-radius: 8rpx;\n  padding: 16rpx;\n  font-size: 28rpx;\n  box-sizing: border-box;\n}\n\n.word-count {\n  text-align: right;\n  font-size: 24rpx;\n  color: #999999;\n  margin-top: 8rpx;\n}\n\n/* 匿名评价 */\n.anonymous-section {\n  margin: 0 32rpx 32rpx;\n  padding: 0 24rpx;\n}\n\n.checkbox-label {\n  display: flex;\n  align-items: center;\n}\n\n.anonymous-text {\n  font-size: 28rpx;\n  color: #666666;\n}\n\n/* 提交按钮 */\n.submit-btn {\n  width: calc(100% - 64rpx);\n  height: 90rpx;\n  background: linear-gradient(135deg, #0A84FF, #0040DD);\n  color: #FFFFFF;\n  font-size: 32rpx;\n  font-weight: 500;\n  border-radius: 45rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 0 32rpx;\n}\n\n.submit-btn[disabled] {\n  background: #CCCCCC;\n  color: #FFFFFF;\n}\n\n.safe-area-bottom {\n  height: env(safe-area-inset-bottom);\n  width: 100%;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/carpool-package/pages/carpool/my/create-rating.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onMounted", "uni", "onLoad", "MiniProgramPage"], "mappings": ";;;;;;AAsHA,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACrB,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA,MACX,aAAa;AAAA,IACf,CAAC;AAGD,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACnB,IAAI;AAAA,MACJ,eAAe;AAAA,MACf,aAAa;AAAA,MACb,eAAe;AAAA,MACf,eAAe;AAAA,MACf,QAAQ;AAAA,IACV,CAAC;AAGD,UAAM,SAASA,cAAAA,IAAI,CAAC;AACpB,UAAM,OAAOA,cAAAA,IAAI,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,CAAC;AACjE,UAAM,eAAeA,cAAAA,IAAI,CAAA,CAAE;AAC3B,UAAM,UAAUA,cAAAA,IAAI,EAAE;AACtB,UAAM,cAAcA,cAAAA,IAAI,KAAK;AAG7B,UAAM,aAAaC,cAAQ,SAAC,MAAM;AAChC,YAAM,QAAQ,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,IAAI;AAC/C,aAAO,OAAO,QAAQ,IAAI,MAAM,OAAO,KAAK,IAAI;AAAA,IAClD,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AAEdC,oBAAAA,MAAY,MAAA,OAAA,6DAAA,OAAO;AAAA,IACrB,CAAC;AAGDC,kBAAM,OAAC,CAAC,YAAY;AAClBD,oGAAY,YAAY,OAAO;AAG/B,UAAI,WAAW,OAAO,KAAK,OAAO,EAAE,SAAS,GAAG;AAE9C,YAAI,QAAQ,YAAY,QAAQ,aAAa;AAC3C,qBAAW,MAAM,KAAK,QAAQ;AAG9B,gBAAM,iBAAiBA,cAAG,MAAC,eAAe,gBAAgB,KAAK,CAAA;AAC/D,gBAAM,gBAAgB,eAAe;AAAA,YAAK,UACxC,KAAK,aAAa,QAAQ,YAC1B,KAAK,gBAAgB,QAAQ;AAAA,UACrC;AAEM,cAAI,eAAe;AAEjB,qBAAS,QAAQ;AAAA,cACf,IAAI,QAAQ,aAAa,cAAc;AAAA,cACvC,eAAe,mBAAmB,QAAQ,iBAAiB,cAAc,aAAa;AAAA,cACtF,aAAa,mBAAmB,QAAQ,eAAe,cAAc,WAAW;AAAA,cAChF,eAAe,QAAQ,iBAAiB,cAAc;AAAA,cACtD,eAAe,QAAQ,iBAAiB,cAAc;AAAA,cACtD,QAAQ;AAAA,YAClB;AAGQ,kBAAM,cAAcA,cAAG,MAAC,eAAe,aAAa,KAAK,CAAA;AACzD,gBAAI,YAAY,QAAQ,QAAQ,GAAG;AACjC,yBAAW,QAAQ,EAAE,GAAG,WAAW,OAAO,GAAG,YAAY,QAAQ,QAAQ;YACnF,OAAe;AAEL,yBAAW,MAAM,OAAO,OAAO,QAAQ,YAAY,OAAO,EAAE;AAC5D,yBAAW,MAAM,cAAc,QAAQ;AAGvC,oCAAsB,QAAQ,WAAW;AAAA,YAC1C;AAAA,UACT,OAAa;AAEL,uBAAW,MAAM,KAAK,QAAQ;AAC9B,uBAAW,MAAM,OAAO,OAAO,QAAQ,YAAY,OAAO,EAAE;AAC5D,uBAAW,MAAM,cAAc,QAAQ;AAEvC,qBAAS,QAAQ;AAAA,cACf,IAAI,QAAQ,aAAa;AAAA,cACzB,eAAe,mBAAmB,QAAQ,iBAAiB,EAAE;AAAA,cAC7D,aAAa,mBAAmB,QAAQ,eAAe,EAAE;AAAA,cACzD,eAAe,QAAQ,iBAAiB;AAAA,cACxC,eAAe,QAAQ,iBAAiB;AAAA,cACxC,QAAQ;AAAA,YAClB;AAGQ,kCAAsB,QAAQ,WAAW;AAAA,UAC1C;AAAA,QACP,OAAW;AAEL;QACD;AAAA,MACL,OAAS;AAEL;AAGAA,sBAAAA,MAAA,MAAA,SAAA,6DAAc,iBAAiB;AAAA,MAChC;AAAA,IACH,CAAC;AAGD,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAiBA,UAAM,eAAe,MAAM;AACzB,iBAAW,QAAQ;AAAA,QACjB,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,UAAU;AAAA,QACV,WAAW;AAAA,QACX,aAAa;AAAA,MACjB;AAEE,eAAS,QAAQ;AAAA,QACf,IAAI;AAAA,QACJ,eAAe;AAAA,QACf,aAAa;AAAA,QACb,eAAe;AAAA,QACf,eAAe;AAAA,QACf,QAAQ;AAAA,MACZ;AAAA,IACA;AAGA,UAAM,eAAe,CAAC,UAAU;AAC9B,aAAO,QAAQ;AAAA,IACjB;AAGA,UAAM,YAAY,CAAC,QAAQ;AACzB,UAAI,aAAa,MAAM,SAAS,GAAG,GAAG;AACpC,qBAAa,QAAQ,aAAa,MAAM,OAAO,UAAQ,SAAS,GAAG;AAAA,MACvE,OAAS;AACL,YAAI,aAAa,MAAM,SAAS,GAAG;AACjC,uBAAa,MAAM,KAAK,GAAG;AAAA,QACjC,OAAW;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACF;AAAA,IACH;AAGA,UAAM,kBAAkB,CAAC,MAAM;AAC7B,kBAAY,QAAQ,EAAE,OAAO;AAAA,IAC/B;AAGA,UAAM,eAAe,MAAM;AACzB,UAAI,OAAO,UAAU,GAAG;AACtBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAGD,YAAM,aAAa;AAAA,QACjB,UAAU,WAAW,MAAM;AAAA,QAC3B,aAAa,WAAW,MAAM;AAAA,QAC9B,WAAW,SAAS,MAAM;AAAA,QAC1B,QAAQ,OAAO;AAAA,QACf,MAAM,aAAa;AAAA,QACnB,SAAS,QAAQ;AAAA,QACjB,aAAa,YAAY;AAAA,QACzB,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA,QACpC,eAAe,SAAS,MAAM;AAAA,QAC9B,aAAa,SAAS,MAAM;AAAA,QAC5B,eAAe,SAAS,MAAM;AAAA,QAC9B,eAAe,SAAS,MAAM;AAAA,MAClC;AAEEA,oBAAY,MAAA,MAAA,OAAA,6DAAA,WAAW,UAAU;AAGjC,YAAM,iBAAiBA,cAAG,MAAC,eAAe,gBAAgB,KAAK,CAAA;AAG/D,YAAM,gBAAgB,eAAe;AAAA,QAAU,UAC7C,KAAK,aAAa,WAAW,YAC7B,KAAK,cAAc,WAAW;AAAA,MAClC;AAEE,UAAI,kBAAkB,IAAI;AACxB,uBAAe,aAAa,IAAI;AAAA,MACpC,OAAS;AACL,uBAAe,KAAK,UAAU;AAAA,MAC/B;AAEDA,oBAAAA,MAAI,eAAe,kBAAkB,cAAc;AAGnDA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAEfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAGD,mBAAW,MAAM;AACfA,wBAAG,MAAC,aAAY;AAAA,QACjB,GAAE,IAAI;AAAA,MACR,GAAE,GAAI;AAAA,IACT;AAGA,UAAM,wBAAwB,CAAC,gBAAgB;AAG7CA,oBAAA,MAAA,MAAA,OAAA,6DAAY,iBAAiB,WAAW;AAGxC,iBAAW,MAAM;AAEf,cAAM,iBAAiB;AAAA,UACrB,MAAM,OAAO,YAAY,OAAO,EAAE;AAAA,UAClC,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,UAAU;AAAA,UACV,WAAW;AAAA,UACX;AAAA,QACN;AAGI,mBAAW,QAAQ,EAAE,GAAG,WAAW,OAAO,GAAG;AAG7C,cAAM,cAAcA,cAAG,MAAC,eAAe,aAAa,KAAK,CAAA;AACzD,oBAAY,WAAW,MAAM,EAAE,IAAI;AACnCA,sBAAAA,MAAI,eAAe,eAAe,WAAW;AAAA,MAC9C,GAAE,GAAG;AAAA,IACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClYA,GAAG,WAAWE,SAAe;"}