"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const CreateButton = () => "../../../components/CreateButton.js";
const _sfc_main = {
  components: {
    CreateButton
  },
  data() {
    return {
      groupSystemEnabled: true,
      dateRange: "2023-04-01 ~ 2023-04-30",
      // 拼团数据
      groupData: {
        totalGroups: 24,
        groupsTrend: "up",
        groupsGrowth: "12%",
        successRate: 85,
        successRateTrend: "up",
        successRateGrowth: "5%",
        totalRevenue: 12580.5,
        revenueTrend: "up",
        revenueGrowth: "18%",
        participantsCount: 156,
        participantsTrend: "up",
        participantsGrowth: "15%"
      },
      // 进行中的拼团
      activeGroups: [
        {
          id: 1,
          name: "iPhone 14 Pro Max",
          image: "/static/images/products/iphone.jpg",
          originalPrice: "9999.00",
          groupPrice: "8799.00",
          requiredMembers: 3,
          currentMembers: 2,
          progressPercent: 66,
          timeLeft: "12小时23分",
          statusText: "进行中",
          statusClass: "active"
        },
        {
          id: 2,
          name: "MacBook Air M2",
          image: "/static/images/products/macbook.jpg",
          originalPrice: "7999.00",
          groupPrice: "7299.00",
          requiredMembers: 5,
          currentMembers: 3,
          progressPercent: 60,
          timeLeft: "5小时47分",
          statusText: "即将结束",
          statusClass: "ending"
        },
        {
          id: 3,
          name: "四菜一汤家庭套餐",
          image: "/static/images/products/food-package.jpg",
          originalPrice: "168.00",
          groupPrice: "99.00",
          requiredMembers: 2,
          currentMembers: 1,
          progressPercent: 50,
          timeLeft: "23小时59分",
          statusText: "套餐拼团",
          statusClass: "package"
        }
      ],
      // 拼团设置
      groupSettings: {
        rulesCount: 5,
        timeLimit: 24,
        discountRange: "7折-9折",
        notificationEnabled: true
      },
      // 拼团工具
      groupTools: [
        {
          name: "创建拼团",
          description: "创建新的拼团活动",
          color: "#34C759",
          svg: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-4.41 0-8 3.59-8 8s3.59 8 8 8 8-3.59 8-8-3.59-8-8-8zm4 10c0 2.21-1.79 4-4 4s-4-1.79-4-4 1.79-4 4-4 4 1.79 4 4zm0-6c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4z"/></svg>'
        },
        {
          name: "拼团管理",
          description: "管理拼团活动",
          color: "#FF3B30",
          svg: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-4.41 0-8 3.59-8 8s3.59 8 8 8 8-3.59 8-8-3.59-8-8-8zm4 10c0 2.21-1.79 4-4 4s-4-1.79-4-4 1.79-4 4-4 4 1.79 4 4zm0-6c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4z"/></svg>'
        },
        {
          name: "数据统计",
          description: "查看拼团相关数据",
          color: "#007AFF",
          svg: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/></svg>'
        },
        {
          name: "团购套餐",
          description: "管理团购套餐",
          color: "#FF9500",
          svg: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3"></path><rect x="9" y="9" width="6" height="6"></rect></svg>'
        }
      ]
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    showHelp() {
      common_vendor.index.showToast({
        title: "拼团活动帮助",
        icon: "none"
      });
    },
    toggleGroupSystem(e) {
      this.groupSystemEnabled = e.detail.value;
      common_vendor.index.showToast({
        title: this.groupSystemEnabled ? "已开启拼团系统" : "已关闭拼团系统",
        icon: "none"
      });
    },
    showDatePicker() {
      common_vendor.index.showToast({
        title: "日期选择功能开发中",
        icon: "none"
      });
    },
    formatNumber(num) {
      return num.toLocaleString("zh-CN", { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    },
    viewAllGroups() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/group/list"
      });
    },
    viewGroupDetail(group) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/group/detail?id=${group.id}`
      });
    },
    navigateToSetting(setting) {
      common_vendor.index.showToast({
        title: `导航到${setting}设置页面`,
        icon: "none"
      });
    },
    useTool(tool) {
      if (tool.name === "创建拼团") {
        common_vendor.index.navigateTo({
          url: "/subPackages/merchant-admin-marketing/pages/marketing/group/create"
        });
      } else if (tool.name === "拼团管理") {
        common_vendor.index.navigateTo({
          url: "/subPackages/merchant-admin-marketing/pages/marketing/group/list"
        });
      } else if (tool.name === "数据统计") {
        common_vendor.index.navigateTo({
          url: "/subPackages/merchant-admin-marketing/pages/marketing/group/statistics"
        });
      } else if (tool.name === "团购套餐") {
        common_vendor.index.navigateTo({
          url: "/subPackages/merchant-admin-marketing/pages/marketing/group/package-management"
        });
      } else {
        common_vendor.index.showToast({
          title: `使用${tool.name}工具`,
          icon: "none"
        });
      }
    },
    createNewGroup() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/group/create"
      });
    }
  }
};
if (!Array) {
  const _component_CreateButton = common_vendor.resolveComponent("CreateButton");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_CreateButton + _component_path + _component_circle + _component_svg)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: common_vendor.o($options.createNewGroup),
    d: common_vendor.p({
      text: "创建拼团",
      theme: "group"
    }),
    e: $data.groupSystemEnabled,
    f: common_vendor.o((...args) => $options.toggleGroupSystem && $options.toggleGroupSystem(...args)),
    g: common_vendor.t($data.dateRange),
    h: common_vendor.o((...args) => $options.showDatePicker && $options.showDatePicker(...args)),
    i: common_vendor.p({
      d: "M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2",
      stroke: "#9040FF",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    j: common_vendor.p({
      cx: "9",
      cy: "7",
      r: "4",
      stroke: "#9040FF",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    k: common_vendor.p({
      d: "M23 21v-2a4 4 0 0 0-3-3.87",
      stroke: "#9040FF",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    l: common_vendor.p({
      d: "M16 3.13a4 4 0 0 1 0 7.75",
      stroke: "#9040FF",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    m: common_vendor.p({
      width: "16",
      height: "16",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }),
    n: common_vendor.t($data.groupData.totalGroups),
    o: common_vendor.t($data.groupData.groupsGrowth),
    p: common_vendor.n($data.groupData.groupsTrend),
    q: common_vendor.p({
      d: "M22 11.08V12a10 10 0 1 1-5.93-9.14",
      stroke: "#34C759",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    r: common_vendor.p({
      d: "M22 4 12 14.01l-3-3",
      stroke: "#34C759",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    s: common_vendor.p({
      width: "16",
      height: "16",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }),
    t: common_vendor.t($data.groupData.successRate),
    v: common_vendor.t($data.groupData.successRateGrowth),
    w: common_vendor.n($data.groupData.successRateTrend),
    x: common_vendor.p({
      d: "M12 1v22M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",
      stroke: "#FF9500",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    y: common_vendor.p({
      width: "16",
      height: "16",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }),
    z: common_vendor.t($options.formatNumber($data.groupData.totalRevenue)),
    A: common_vendor.t($data.groupData.revenueGrowth),
    B: common_vendor.n($data.groupData.revenueTrend),
    C: common_vendor.p({
      d: "M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2",
      stroke: "#007AFF",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    D: common_vendor.p({
      cx: "9",
      cy: "7",
      r: "4",
      stroke: "#007AFF",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    E: common_vendor.p({
      width: "16",
      height: "16",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }),
    F: common_vendor.t($data.groupData.participantsCount),
    G: common_vendor.t($data.groupData.participantsGrowth),
    H: common_vendor.n($data.groupData.participantsTrend),
    I: common_vendor.o((...args) => $options.viewAllGroups && $options.viewAllGroups(...args)),
    J: common_vendor.f($data.activeGroups, (group, index, i0) => {
      return {
        a: group.image,
        b: common_vendor.t(group.name),
        c: common_vendor.t(group.statusText),
        d: common_vendor.n(group.statusClass),
        e: common_vendor.t(group.originalPrice),
        f: common_vendor.t(group.groupPrice),
        g: common_vendor.t(group.requiredMembers),
        h: common_vendor.t(group.timeLeft),
        i: common_vendor.t(group.currentMembers),
        j: common_vendor.t(group.requiredMembers),
        k: common_vendor.t(group.progressPercent),
        l: group.progressPercent + "%",
        m: index,
        n: common_vendor.o(($event) => $options.viewGroupDetail(group), index)
      };
    }),
    K: common_vendor.t($data.groupSettings.rulesCount),
    L: common_vendor.o(($event) => $options.navigateToSetting("rules")),
    M: common_vendor.t($data.groupSettings.timeLimit),
    N: common_vendor.o(($event) => $options.navigateToSetting("time")),
    O: common_vendor.t($data.groupSettings.discountRange),
    P: common_vendor.o(($event) => $options.navigateToSetting("discount")),
    Q: common_vendor.t($data.groupSettings.notificationEnabled ? "已开启" : "未开启"),
    R: common_vendor.o(($event) => $options.navigateToSetting("notification")),
    S: common_vendor.f($data.groupTools, (tool, index, i0) => {
      return {
        a: tool.svg,
        b: tool.color,
        c: common_vendor.t(tool.name),
        d: common_vendor.t(tool.description),
        e: index,
        f: common_vendor.o(($event) => $options.useTool(tool), index)
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/group/management.js.map
