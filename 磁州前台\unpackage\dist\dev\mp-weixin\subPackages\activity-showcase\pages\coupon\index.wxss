/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-976a0f82, html.data-v-976a0f82, #app.data-v-976a0f82, .index-container.data-v-976a0f82 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.coupon-page.data-v-976a0f82 {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F2F2F7;
}

/* 自定义导航栏 */
.custom-navbar.data-v-976a0f82 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
}
.custom-navbar .navbar-bg.data-v-976a0f82 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FFCC00 0%, #FF9500 100%);
}
.custom-navbar .navbar-content.data-v-976a0f82 {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding-top: var(--status-bar-height, 25px);
  padding-left: 30rpx;
  padding-right: 30rpx;
  box-sizing: border-box;
}
.custom-navbar .navbar-content .back-btn.data-v-976a0f82 {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.custom-navbar .navbar-content .back-icon.data-v-976a0f82 {
  width: 100%;
  height: 100%;
}
.custom-navbar .navbar-content .navbar-title.data-v-976a0f82 {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}
.custom-navbar .navbar-content .navbar-right.data-v-976a0f82 {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.custom-navbar .navbar-content .navbar-right .close-btn.data-v-976a0f82 {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 分类选项卡 */
.category-tabs.data-v-976a0f82 {
  position: relative;
  display: flex;
  background-color: #FFFFFF;
  height: 88rpx;
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
}
.category-tabs .tab-item.data-v-976a0f82 {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #666666;
  position: relative;
}
.category-tabs .tab-item.active.data-v-976a0f82 {
  color: #FF9500;
  font-weight: 600;
}
.category-tabs .tab-line.data-v-976a0f82 {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 4rpx;
  background-color: #FF9500;
  transition: transform 0.3s ease;
}

/* 内容区域 */
.content-scroll.data-v-976a0f82 {
  flex: 1;
  width: 100%;
}

/* 优惠券列表 */
.coupon-list.data-v-976a0f82 {
  padding: 20rpx;
}
.coupon-item.data-v-976a0f82 {
  position: relative;
  display: flex;
  margin-bottom: 20rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}
.coupon-item.coupon-used.data-v-976a0f82, .coupon-item.coupon-expired.data-v-976a0f82 {
  opacity: 0.7;
}
.coupon-item .coupon-left.data-v-976a0f82 {
  width: 200rpx;
  background: linear-gradient(135deg, #FFCC00 0%, #FF9500 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20rpx;
  position: relative;
}
.coupon-item .coupon-left .coupon-amount.data-v-976a0f82 {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #FFFFFF;
}
.coupon-item .coupon-left .coupon-amount .amount-symbol.data-v-976a0f82 {
  font-size: 28rpx;
  font-weight: 600;
}
.coupon-item .coupon-left .coupon-amount .amount-value.data-v-976a0f82 {
  font-size: 60rpx;
  font-weight: 700;
  line-height: 1;
}
.coupon-item .coupon-left .coupon-amount .amount-condition.data-v-976a0f82 {
  font-size: 22rpx;
  margin-top: 8rpx;
}
.coupon-item .coupon-right.data-v-976a0f82 {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.coupon-item .coupon-right .coupon-info .coupon-title.data-v-976a0f82 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}
.coupon-item .coupon-right .coupon-info .coupon-desc.data-v-976a0f82 {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 12rpx;
}
.coupon-item .coupon-right .coupon-info .coupon-time.data-v-976a0f82 {
  font-size: 22rpx;
  color: #999999;
}
.coupon-item .coupon-right .coupon-btn.data-v-976a0f82 {
  align-self: flex-end;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60rpx;
  width: 160rpx;
  border-radius: 30rpx;
  background: linear-gradient(135deg, #FFCC00 0%, #FF9500 100%);
  font-size: 26rpx;
  font-weight: 500;
  color: #FFFFFF;
}
.coupon-item .coupon-right .coupon-status.data-v-976a0f82 {
  align-self: flex-end;
  font-size: 26rpx;
  color: #999999;
}
.coupon-item .coupon-tag.data-v-976a0f82 {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  padding: 4rpx 12rpx;
  font-size: 22rpx;
  color: #FFFFFF;
  border-radius: 10rpx;
  background-color: rgba(255, 59, 48, 0.8);
}
.coupon-item .coupon-border.data-v-976a0f82 {
  position: absolute;
  left: 200rpx;
  top: 0;
  bottom: 0;
  width: 0;
  border-left: 1px dashed #F2F2F7;
}
.coupon-item .coupon-border.data-v-976a0f82::before, .coupon-item .coupon-border.data-v-976a0f82::after {
  content: "";
  position: absolute;
  left: -10rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #F2F2F7;
}
.coupon-item .coupon-border.data-v-976a0f82::before {
  top: -10rpx;
}
.coupon-item .coupon-border.data-v-976a0f82::after {
  bottom: -10rpx;
}
.coupon-item .coupon-mask.data-v-976a0f82 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.1);
  pointer-events: none;
}
.coupon-item .coupon-stamp.data-v-976a0f82 {
  position: absolute;
  top: 50%;
  right: 60rpx;
  transform: translateY(-50%) rotate(-30deg);
  font-size: 60rpx;
  font-weight: 700;
  color: rgba(255, 59, 48, 0.6);
  border: 6rpx solid rgba(255, 59, 48, 0.6);
  padding: 10rpx 20rpx;
  border-radius: 16rpx;
}

/* 我的优惠券 */
.my-coupon-section .coupon-status-tabs.data-v-976a0f82 {
  display: flex;
  background-color: #FFFFFF;
  padding: 20rpx;
}
.my-coupon-section .coupon-status-tabs .status-tab.data-v-976a0f82 {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60rpx;
  font-size: 26rpx;
  color: #666666;
  position: relative;
  border-radius: 30rpx;
  margin: 0 10rpx;
}
.my-coupon-section .coupon-status-tabs .status-tab.active.data-v-976a0f82 {
  color: #FFFFFF;
  background: linear-gradient(135deg, #FFCC00 0%, #FF9500 100%);
}
.my-coupon-section .my-coupon-list.data-v-976a0f82 {
  padding: 20rpx;
}
.my-coupon-section .empty-tip.data-v-976a0f82 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}
.my-coupon-section .empty-tip .empty-image.data-v-976a0f82 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.my-coupon-section .empty-tip .empty-text.data-v-976a0f82 {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 30rpx;
}
.my-coupon-section .empty-tip .empty-btn.data-v-976a0f82 {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;
  width: 300rpx;
  border-radius: 40rpx;
  background: linear-gradient(135deg, #FFCC00 0%, #FF9500 100%);
  font-size: 28rpx;
  font-weight: 500;
  color: #FFFFFF;
}

/* 加载更多和到底了提示 */
.loading-more.data-v-976a0f82, .no-more.data-v-976a0f82 {
  text-align: center;
  padding: 20rpx 0;
  font-size: 24rpx;
  color: #999999;
}