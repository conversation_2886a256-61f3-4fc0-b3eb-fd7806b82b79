package com.cizhou.auth.service.impl;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.LineCaptcha;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.cizhou.auth.dto.ChangePasswordRequest;
import com.cizhou.auth.dto.LoginRequest;
import com.cizhou.auth.dto.LoginResponse;
import com.cizhou.auth.entity.AdminUser;
import com.cizhou.auth.mapper.AdminUserMapper;
import com.cizhou.auth.service.AuthService;
import com.cizhou.common.core.exception.BusinessException;
import com.cizhou.common.core.result.ResultCode;
import com.cizhou.common.security.util.JwtUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 认证服务实现类
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    private final AdminUserMapper adminUserMapper;
    private final JwtUtil jwtUtil;
    private final PasswordEncoder passwordEncoder;
    private final StringRedisTemplate redisTemplate;

    private static final String CAPTCHA_KEY_PREFIX = "captcha:";
    private static final String TOKEN_BLACKLIST_PREFIX = "token:blacklist:";
    private static final String LOGIN_ATTEMPTS_PREFIX = "login:attempts:";
    private static final int MAX_LOGIN_ATTEMPTS = 5;
    private static final int CAPTCHA_EXPIRE_TIME = 300; // 5分钟

    @Override
    public LoginResponse login(LoginRequest request) {
        // 检查登录尝试次数
        checkLoginAttempts(request.getUsername());

        // 验证验证码（如果需要）
        if (StrUtil.isNotBlank(request.getCaptcha())) {
            validateCaptcha(request.getCaptchaKey(), request.getCaptcha());
        }

        // 查询用户
        AdminUser user = adminUserMapper.findByUsername(request.getUsername());
        if (user == null) {
            incrementLoginAttempts(request.getUsername());
            throw BusinessException.of(ResultCode.USER_NOT_FOUND);
        }

        // 检查用户状态
        if (user.getStatus() == 0) {
            throw BusinessException.of(ResultCode.USER_DISABLED);
        }

        // 验证密码
        if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
            incrementLoginAttempts(request.getUsername());
            throw BusinessException.of(ResultCode.PASSWORD_ERROR);
        }

        // 清除登录尝试次数
        clearLoginAttempts(request.getUsername());

        // 更新最后登录时间
        user.setLastLoginTime(LocalDateTime.now());
        adminUserMapper.updateById(user);

        // 生成Token
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", user.getId());
        claims.put("username", user.getUsername());
        claims.put("realName", user.getRealName());

        String accessToken = jwtUtil.generateAccessToken(user.getUsername(), claims);
        String refreshToken = jwtUtil.generateRefreshToken(user.getUsername());

        // 构建响应
        LoginResponse.UserInfo userInfo = LoginResponse.UserInfo.builder()
                .id(user.getId())
                .username(user.getUsername())
                .realName(user.getRealName())
                .phone(user.getPhone())
                .email(user.getEmail())
                .avatar(user.getAvatar())
                .status(user.getStatus())
                .roles(getUserRoles(user.getId()))
                .permissions(getUserPermissions(user.getId()))
                .lastLoginTime(user.getLastLoginTime())
                .createTime(user.getCreateTime())
                .build();

        return LoginResponse.builder()
                .token(accessToken)
                .refreshToken(refreshToken)
                .tokenType("Bearer")
                .expiresIn(86400L) // 24小时
                .userInfo(userInfo)
                .build();
    }

    @Override
    public void logout(String token) {
        if (StrUtil.isNotBlank(token)) {
            // 将Token加入黑名单
            long remainingTime = jwtUtil.getTokenRemainingTime(token);
            if (remainingTime > 0) {
                redisTemplate.opsForValue().set(
                        TOKEN_BLACKLIST_PREFIX + token,
                        "1",
                        remainingTime,
                        TimeUnit.SECONDS
                );
            }
        }
    }

    @Override
    public LoginResponse refreshToken(String refreshToken) {
        if (!jwtUtil.validateRefreshToken(refreshToken)) {
            throw BusinessException.of(ResultCode.REFRESH_TOKEN_INVALID);
        }

        String username = jwtUtil.getUsernameFromToken(refreshToken);
        AdminUser user = adminUserMapper.findByUsername(username);
        if (user == null || user.getStatus() == 0) {
            throw BusinessException.of(ResultCode.USER_NOT_FOUND);
        }

        // 生成新的访问Token
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", user.getId());
        claims.put("username", user.getUsername());
        claims.put("realName", user.getRealName());

        String newAccessToken = jwtUtil.generateAccessToken(username, claims);

        // 构建响应
        LoginResponse.UserInfo userInfo = LoginResponse.UserInfo.builder()
                .id(user.getId())
                .username(user.getUsername())
                .realName(user.getRealName())
                .phone(user.getPhone())
                .email(user.getEmail())
                .avatar(user.getAvatar())
                .status(user.getStatus())
                .roles(getUserRoles(user.getId()))
                .permissions(getUserPermissions(user.getId()))
                .lastLoginTime(user.getLastLoginTime())
                .createTime(user.getCreateTime())
                .build();

        return LoginResponse.builder()
                .token(newAccessToken)
                .refreshToken(refreshToken)
                .tokenType("Bearer")
                .expiresIn(86400L)
                .userInfo(userInfo)
                .build();
    }

    @Override
    public Object getUserInfo(String token) {
        String username = jwtUtil.getUsernameFromToken(token);
        AdminUser user = adminUserMapper.findByUsername(username);
        if (user == null) {
            throw BusinessException.of(ResultCode.USER_NOT_FOUND);
        }

        return LoginResponse.UserInfo.builder()
                .id(user.getId())
                .username(user.getUsername())
                .realName(user.getRealName())
                .phone(user.getPhone())
                .email(user.getEmail())
                .avatar(user.getAvatar())
                .status(user.getStatus())
                .roles(getUserRoles(user.getId()))
                .permissions(getUserPermissions(user.getId()))
                .lastLoginTime(user.getLastLoginTime())
                .createTime(user.getCreateTime())
                .build();
    }

    @Override
    public Object getUserPermissions(String token) {
        String username = jwtUtil.getUsernameFromToken(token);
        AdminUser user = adminUserMapper.findByUsername(username);
        if (user == null) {
            throw BusinessException.of(ResultCode.USER_NOT_FOUND);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("roles", getUserRoles(user.getId()));
        result.put("permissions", getUserPermissions(user.getId()));
        return result;
    }

    @Override
    public void changePassword(String token, ChangePasswordRequest request) {
        if (!request.getNewPassword().equals(request.getConfirmPassword())) {
            throw BusinessException.of(ResultCode.BAD_REQUEST, "两次输入的密码不一致");
        }

        String username = jwtUtil.getUsernameFromToken(token);
        AdminUser user = adminUserMapper.findByUsername(username);
        if (user == null) {
            throw BusinessException.of(ResultCode.USER_NOT_FOUND);
        }

        // 验证原密码
        if (!passwordEncoder.matches(request.getOldPassword(), user.getPassword())) {
            throw BusinessException.of(ResultCode.OLD_PASSWORD_ERROR);
        }

        // 更新密码
        user.setPassword(passwordEncoder.encode(request.getNewPassword()));
        user.setUpdateTime(LocalDateTime.now());
        adminUserMapper.updateById(user);
    }

    @Override
    public void generateCaptcha(HttpServletRequest request, HttpServletResponse response) throws IOException {
        // 生成验证码
        LineCaptcha captcha = CaptchaUtil.createLineCaptcha(120, 40, 4, 20);
        String captchaKey = IdUtil.simpleUUID();
        String captchaCode = captcha.getCode();

        // 存储到Redis
        redisTemplate.opsForValue().set(
                CAPTCHA_KEY_PREFIX + captchaKey,
                captchaCode,
                CAPTCHA_EXPIRE_TIME,
                TimeUnit.SECONDS
        );

        // 设置响应头
        response.setContentType("image/png");
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Captcha-Key", captchaKey);

        // 输出图片
        captcha.write(response.getOutputStream());
    }

    @Override
    public boolean validateToken(String token) {
        if (StrUtil.isBlank(token)) {
            return false;
        }

        // 检查是否在黑名单中
        if (redisTemplate.hasKey(TOKEN_BLACKLIST_PREFIX + token)) {
            return false;
        }

        return jwtUtil.validateToken(token);
    }

    /**
     * 验证验证码
     */
    private void validateCaptcha(String captchaKey, String captchaCode) {
        if (StrUtil.isBlank(captchaKey) || StrUtil.isBlank(captchaCode)) {
            throw BusinessException.of(ResultCode.CAPTCHA_ERROR);
        }

        String storedCode = redisTemplate.opsForValue().get(CAPTCHA_KEY_PREFIX + captchaKey);
        if (StrUtil.isBlank(storedCode)) {
            throw BusinessException.of(ResultCode.CAPTCHA_EXPIRED);
        }

        if (!captchaCode.equalsIgnoreCase(storedCode)) {
            throw BusinessException.of(ResultCode.CAPTCHA_ERROR);
        }

        // 验证成功后删除验证码
        redisTemplate.delete(CAPTCHA_KEY_PREFIX + captchaKey);
    }

    /**
     * 检查登录尝试次数
     */
    private void checkLoginAttempts(String username) {
        String key = LOGIN_ATTEMPTS_PREFIX + username;
        String attempts = redisTemplate.opsForValue().get(key);
        if (StrUtil.isNotBlank(attempts) && Integer.parseInt(attempts) >= MAX_LOGIN_ATTEMPTS) {
            throw BusinessException.of(ResultCode.TOO_MANY_REQUESTS, "登录尝试次数过多，请稍后再试");
        }
    }

    /**
     * 增加登录尝试次数
     */
    private void incrementLoginAttempts(String username) {
        String key = LOGIN_ATTEMPTS_PREFIX + username;
        redisTemplate.opsForValue().increment(key);
        redisTemplate.expire(key, 30, TimeUnit.MINUTES);
    }

    /**
     * 清除登录尝试次数
     */
    private void clearLoginAttempts(String username) {
        redisTemplate.delete(LOGIN_ATTEMPTS_PREFIX + username);
    }

    /**
     * 获取用户角色
     */
    private List<String> getUserRoles(Long userId) {
        return adminUserMapper.findRolesByUserId(userId);
    }

    /**
     * 获取用户权限
     */
    private List<String> getUserPermissions(Long userId) {
        List<String> permissions = adminUserMapper.findPermissionsByUserId(userId);
        // 如果是超级管理员，返回所有权限
        List<String> roles = getUserRoles(userId);
        if (roles.contains("admin")) {
            permissions.add("*");
        }
        return permissions;
    }
}
