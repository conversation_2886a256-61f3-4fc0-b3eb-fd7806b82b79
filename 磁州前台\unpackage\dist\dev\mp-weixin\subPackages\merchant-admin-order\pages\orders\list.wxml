<view class="order-list-container"><view class="navbar"><view class="navbar-back" bindtap="{{a}}"><view class="back-icon"></view></view><text class="navbar-title">订单列表</text><view class="navbar-right"><view class="search-icon" bindtap="{{b}}">🔍</view></view></view><view wx:if="{{c}}" class="search-bar"><input class="search-input" placeholder="搜索订单号、客户名称" bindconfirm="{{d}}" value="{{e}}" bindinput="{{f}}"/><view class="search-btn" bindtap="{{g}}">搜索</view><view class="cancel-btn" bindtap="{{h}}">取消</view></view><view class="filter-section"><scroll-view scroll-x="true" class="filter-scroll"><view wx:for="{{i}}" wx:for-item="status" wx:key="b" class="{{['status-tag', status.c]}}" bindtap="{{status.d}}">{{status.a}}</view></scroll-view><view class="filter-button" bindtap="{{j}}"><text class="filter-icon">⚙️</text><text>筛选</text></view></view><view class="sort-section"><view wx:for="{{k}}" wx:for-item="option" wx:key="d" class="{{['sort-option', option.e]}}" bindtap="{{option.f}}">{{option.a}} <text wx:if="{{option.b}}" class="sort-icon">{{option.c}}</text></view></view><view class="order-list"><view wx:for="{{l}}" wx:for-item="order" wx:key="l" class="order-item" bindtap="{{order.m}}"><view class="order-header"><text class="order-number">订单号: {{order.a}}</text><text class="order-status" style="{{'color:' + order.c}}">{{order.b}}</text></view><view class="order-info"><view class="customer-info"><text class="label">客户:</text><text class="value">{{order.d}}</text></view><view class="time-info"><text class="label">下单时间:</text><text class="value">{{order.e}}</text></view><view class="amount-info"><text class="label">订单金额:</text><text class="value price">¥{{order.f}}</text></view></view><view class="product-preview"><image wx:for="{{order.g}}" wx:for-item="product" wx:key="a" src="{{product.b}}" mode="aspectFill" class="product-image"></image><view wx:if="{{order.h}}" class="more-products">+{{order.i}}</view></view><view class="order-actions"><view class="action-btn primary" catchtap="{{order.j}}">处理订单</view><view class="action-btn" catchtap="{{order.k}}">联系客户</view></view></view><view wx:if="{{m}}" class="load-more" bindtap="{{n}}"><text class="load-text">加载更多</text></view><view wx:if="{{o}}" class="empty-state"><view class="empty-icon">📭</view><text class="empty-text">暂无订单数据</text><view class="refresh-btn" bindtap="{{p}}">刷新</view></view></view><view class="batch-action-btn" bindtap="{{r}}"><text class="batch-icon">{{q}}</text></view><view wx:if="{{s}}" class="batch-action-bar"><view class="selection-info"> 已选择 <text class="selected-count">{{t}}</text> 个订单 </view><view class="batch-actions"><view class="batch-btn" bindtap="{{v}}">导出</view><view class="batch-btn" bindtap="{{w}}">打印</view><view class="batch-btn primary" bindtap="{{x}}">批量处理</view></view></view></view>