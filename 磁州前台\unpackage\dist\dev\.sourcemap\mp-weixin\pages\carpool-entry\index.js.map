{"version": 3, "file": "index.js", "sources": ["pages/carpool-entry/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY2FycG9vbC1lbnRyeS9pbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"carpool-entry-container\">\r\n    <view class=\"loading-container\">\r\n      <image src=\"/static/images/tabbar/拼车选中.png\" class=\"loading-icon\"></image>\r\n      <text class=\"loading-text\">正在进入拼车服务...</text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      redirected: false\r\n    }\r\n  },\r\n  onLoad() {\r\n    // 显示加载状态\r\n    uni.showLoading({\r\n      title: '加载中...',\r\n      mask: true\r\n    });\r\n    \r\n    // 延迟跳转，给足够时间加载分包\r\n    setTimeout(() => {\r\n      this.navigateToCarpoolMain();\r\n    }, 500);\r\n  },\r\n  onShow() {\r\n    // 如果已经显示过页面，每次再次显示都自动跳转\r\n    if (this.redirected) {\r\n      this.navigateToCarpoolMain();\r\n    }\r\n  },\r\n  methods: {\r\n    navigateToCarpoolMain() {\r\n      // 隐藏系统TabBar，因为拼车页面有自己的TabBar\r\n      uni.hideTabBar();\r\n      \r\n      // 标记已经跳转过\r\n      this.redirected = true;\r\n      \r\n      // 隐藏加载提示\r\n      uni.hideLoading();\r\n      \r\n      // 跳转到拼车主页\r\n      uni.navigateTo({\r\n        url: '/carpool-package/pages/carpool-main/index',\r\n        fail: (err) => {\r\n          console.error('跳转失败', err);\r\n          uni.showTabBar(); // 如果跳转失败，显示回TabBar\r\n          uni.showToast({\r\n            title: '拼车服务加载失败，请重试',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.carpool-entry-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100vh;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.loading-icon {\r\n  width: 120rpx;\r\n  height: 120rpx;\r\n  margin-bottom: 30rpx;\r\n  animation: pulse 1.5s infinite;\r\n}\r\n\r\n.loading-text {\r\n  font-size: 30rpx;\r\n  color: #666;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    transform: scale(1);\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    transform: scale(1.1);\r\n    opacity: 0.8;\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n    opacity: 1;\r\n  }\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/carpool-entry/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAUA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,YAAY;AAAA,IACd;AAAA,EACD;AAAA,EACD,SAAS;AAEPA,kBAAAA,MAAI,YAAY;AAAA,MACd,OAAO;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAGD,eAAW,MAAM;AACf,WAAK,sBAAqB;AAAA,IAC3B,GAAE,GAAG;AAAA,EACP;AAAA,EACD,SAAS;AAEP,QAAI,KAAK,YAAY;AACnB,WAAK,sBAAqB;AAAA,IAC5B;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,wBAAwB;AAEtBA,oBAAG,MAAC,WAAU;AAGd,WAAK,aAAa;AAGlBA,oBAAG,MAAC,YAAW;AAGfA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL,MAAM,CAAC,QAAQ;AACbA,wBAAc,MAAA,MAAA,SAAA,uCAAA,QAAQ,GAAG;AACzBA,wBAAG,MAAC,WAAU;AACdA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;AC1DA,GAAG,WAAW,eAAe;"}