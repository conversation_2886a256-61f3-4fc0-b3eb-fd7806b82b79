<!-- 专属客服页面开始 -->
<template>
  <view class="vip-service-container">
    <!-- 页面标题区域 -->
    <view class="page-header">
      <view class="title-section">
        <text class="page-title">专属客服</text>
        <text class="page-subtitle">管理会员专属客服服务</text>
      </view>
    </view>
    
    <!-- 专属客服开关 -->
    <view class="section-card">
      <view class="switch-item">
        <view class="switch-content">
          <text class="switch-title">专属客服服务</text>
          <text class="switch-desc">开启后，指定等级会员可享受专属客服服务</text>
        </view>
        <switch :checked="serviceSettings.enabled" @change="toggleVipService" color="#4A00E0" />
      </view>
    </view>
    
    <block v-if="serviceSettings.enabled">
      <!-- 服务设置 -->
      <view class="section-card">
        <view class="section-title">服务设置</view>
        
        <view class="form-item">
          <text class="form-label">服务类型</text>
          <view class="checkbox-group">
            <view 
              v-for="(type, index) in serviceTypes" 
              :key="index" 
              class="checkbox-item" 
              :class="{ active: isServiceTypeSelected(type.value) }"
              @click="toggleServiceType(type.value)"
            >
              <view class="checkbox-icon">
                <view class="checkbox-inner" v-if="isServiceTypeSelected(type.value)"></view>
              </view>
              <text class="checkbox-text">{{ type.label }}</text>
            </view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">优先级</text>
          <view class="radio-group">
            <view 
              v-for="(priority, index) in priorityOptions" 
              :key="index" 
              class="radio-item" 
              :class="{ active: serviceSettings.priority === priority.value }"
              @click="setPriority(priority.value)"
            >
              <text class="radio-text">{{ priority.label }}</text>
            </view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">响应时间</text>
          <view class="form-input-group">
            <input type="number" class="form-input" v-model="serviceSettings.responseTime" />
            <text class="input-suffix">分钟内</text>
          </view>
        </view>
        
        <view class="form-item switch-item">
          <text class="form-label">7×24小时服务</text>
          <switch :checked="serviceSettings.fullTimeService" @change="toggleFullTimeService" color="#4A00E0" />
        </view>
      </view>
      
      <!-- 客服人员设置 -->
      <view class="section-card">
        <view class="section-title">客服人员</view>
        
        <view class="staff-list">
          <view class="staff-item" v-for="(staff, index) in staffList" :key="index">
            <view class="staff-info">
              <image class="staff-avatar" :src="staff.avatar" mode="aspectFill"></image>
              <view class="staff-detail">
                <view class="staff-name">{{ staff.name }}</view>
                <view class="staff-position">{{ staff.position }}</view>
              </view>
            </view>
            <view class="staff-actions">
              <view class="staff-status" :class="{ active: staff.active }">
                {{ staff.active ? '在线' : '离线' }}
              </view>
              <view class="action-btn edit" @click="editStaff(staff)">编辑</view>
              <view class="action-btn delete" @click="confirmDeleteStaff(staff)">删除</view>
            </view>
          </view>
        </view>
        
        <button class="add-btn" @click="showAddStaffModal">添加客服人员</button>
      </view>
      
      <!-- 适用会员等级 -->
      <view class="section-card">
        <view class="section-title">适用会员等级</view>
        
        <view class="level-list">
          <view class="level-item" v-for="(level, index) in memberLevels" :key="index">
            <view class="level-checkbox" :class="{ checked: level.selected }" @click="toggleLevel(level)">
              <view class="checkbox-inner" v-if="level.selected"></view>
            </view>
            <view class="level-content">
              <text class="level-name">{{level.name}}</text>
              <text class="level-desc">{{level.memberCount}}名会员</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 服务说明 -->
      <view class="section-card">
        <view class="section-title">服务说明</view>
        
        <view class="form-item">
          <textarea class="form-textarea" v-model="serviceSettings.description" placeholder="请输入专属客服服务说明" />
        </view>
      </view>
      
      <!-- 保存按钮 -->
      <view class="bottom-bar">
        <button class="save-btn" @click="saveSettings">保存设置</button>
      </view>
    </block>
    
    <!-- 添加/编辑客服弹窗 -->
    <uni-popup ref="staffFormPopup" type="center">
      <view class="staff-form-popup">
        <view class="popup-header">
          <text class="popup-title">{{ isEditing ? '编辑客服' : '添加客服' }}</text>
          <text class="popup-close" @click="closeStaffModal">×</text>
        </view>
        <view class="popup-body">
          <view class="form-item">
            <text class="form-label">客服姓名</text>
            <input class="form-input" v-model="staffForm.name" placeholder="请输入客服姓名" />
          </view>
          <view class="form-item">
            <text class="form-label">客服头像</text>
            <view class="avatar-upload">
              <image v-if="staffForm.avatar" class="preview-avatar" :src="staffForm.avatar" mode="aspectFill"></image>
              <view v-else class="upload-btn" @click="chooseAvatar">
                <text class="upload-icon">+</text>
                <text class="upload-text">上传头像</text>
              </view>
            </view>
          </view>
          <view class="form-item">
            <text class="form-label">职位</text>
            <input class="form-input" v-model="staffForm.position" placeholder="请输入客服职位" />
          </view>
          <view class="form-item">
            <text class="form-label">手机号码</text>
            <input class="form-input" v-model="staffForm.phone" placeholder="请输入客服手机号码" />
          </view>
          <view class="form-item">
            <text class="form-label">微信号</text>
            <input class="form-input" v-model="staffForm.wechat" placeholder="请输入客服微信号" />
          </view>
          <view class="form-item switch-item">
            <text class="form-label">在线状态</text>
            <switch :checked="staffForm.active" @change="toggleStaffStatus" color="#4A00E0" />
          </view>
        </view>
        <view class="popup-footer">
          <button class="cancel-btn" @click="closeStaffModal">取消</button>
          <button class="confirm-btn" @click="saveStaffForm">确认</button>
        </view>
      </view>
    </uni-popup>
    
    <!-- 删除确认弹窗 -->
    <uni-popup ref="deleteConfirmPopup" type="dialog">
      <uni-popup-dialog
        type="warning"
        title="删除确认"
        content="确定要删除该客服人员吗？删除后将无法恢复。"
        :before-close="true"
        @confirm="deleteStaff"
        @close="closeDeleteConfirm"
      ></uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 专属客服服务设置
      serviceSettings: {
        enabled: true,
        serviceTypes: ['online', 'phone', 'wechat'],
        priority: 'high',
        responseTime: 5,
        fullTimeService: false,
        description: '会员专享一对一客服服务，为您提供更快速、更专业、更贴心的服务体验。'
      },
      
      // 客服类型选项
      serviceTypes: [
        { label: '在线客服', value: 'online' },
        { label: '电话客服', value: 'phone' },
        { label: '微信客服', value: 'wechat' },
        { label: '上门服务', value: 'visit' }
      ],
      
      // 优先级选项
      priorityOptions: [
        { label: '普通', value: 'normal' },
        { label: '优先', value: 'medium' },
        { label: '最高', value: 'high' }
      ],
      
      // 客服人员列表
      staffList: [
        {
          id: 1,
          name: '张小美',
          avatar: '/static/images/avatar-1.png',
          position: '高级客服专员',
          phone: '13800138001',
          wechat: 'cs_xiaomei',
          active: true
        },
        {
          id: 2,
          name: '李大壮',
          avatar: '/static/images/avatar-2.png',
          position: '客服主管',
          phone: '13800138002',
          wechat: 'cs_dazhuang',
          active: true
        },
        {
          id: 3,
          name: '王晓丽',
          avatar: '/static/images/avatar-3.png',
          position: '资深客服专员',
          phone: '13800138003',
          wechat: 'cs_xiaoli',
          active: false
        }
      ],
      
      // 会员等级
      memberLevels: [
        {
          id: 1,
          name: '普通会员',
          memberCount: 2156,
          selected: false
        },
        {
          id: 2,
          name: '银卡会员',
          memberCount: 864,
          selected: false
        },
        {
          id: 3,
          name: '金卡会员',
          memberCount: 426,
          selected: true
        },
        {
          id: 4,
          name: '钻石会员',
          memberCount: 116,
          selected: true
        }
      ],
      
      // 客服表单
      staffForm: {
        id: '',
        name: '',
        avatar: '',
        position: '',
        phone: '',
        wechat: '',
        active: true
      },
      
      isEditing: false,
      currentStaffId: null
    };
  },
  methods: {
    // 切换专属客服服务
    toggleVipService(e) {
      this.serviceSettings.enabled = e.detail.value;
    },
    
    // 检查服务类型是否选中
    isServiceTypeSelected(type) {
      return this.serviceSettings.serviceTypes.includes(type);
    },
    
    // 切换服务类型
    toggleServiceType(type) {
      const index = this.serviceSettings.serviceTypes.indexOf(type);
      if (index === -1) {
        this.serviceSettings.serviceTypes.push(type);
      } else {
        this.serviceSettings.serviceTypes.splice(index, 1);
      }
    },
    
    // 设置优先级
    setPriority(priority) {
      this.serviceSettings.priority = priority;
    },
    
    // 切换全时服务
    toggleFullTimeService(e) {
      this.serviceSettings.fullTimeService = e.detail.value;
    },
    
    // 切换会员等级
    toggleLevel(level) {
      const index = this.memberLevels.findIndex(item => item.id === level.id);
      if (index !== -1) {
        this.memberLevels[index].selected = !this.memberLevels[index].selected;
      }
    },
    
    // 显示添加客服弹窗
    showAddStaffModal() {
      this.isEditing = false;
      this.staffForm = {
        id: '',
        name: '',
        avatar: '',
        position: '',
        phone: '',
        wechat: '',
        active: true
      };
      this.$refs.staffFormPopup.open();
    },
    
    // 编辑客服
    editStaff(staff) {
      this.isEditing = true;
      this.currentStaffId = staff.id;
      this.staffForm = JSON.parse(JSON.stringify(staff)); // 深拷贝
      this.$refs.staffFormPopup.open();
    },
    
    // 关闭客服表单弹窗
    closeStaffModal() {
      this.$refs.staffFormPopup.close();
    },
    
    // 切换客服状态
    toggleStaffStatus(e) {
      this.staffForm.active = e.detail.value;
    },
    
    // 选择头像
    chooseAvatar() {
      uni.chooseImage({
        count: 1,
        success: (res) => {
          this.staffForm.avatar = res.tempFilePaths[0];
        }
      });
    },
    
    // 保存客服表单
    saveStaffForm() {
      // 表单验证
      if (!this.staffForm.name) {
        uni.showToast({
          title: '请输入客服姓名',
          icon: 'none'
        });
        return;
      }
      
      if (!this.staffForm.position) {
        uni.showToast({
          title: '请输入客服职位',
          icon: 'none'
        });
        return;
      }
      
      // 保存数据
      if (this.isEditing) {
        // 编辑现有客服
        const index = this.staffList.findIndex(item => item.id === this.currentStaffId);
        if (index !== -1) {
          this.staffList.splice(index, 1, JSON.parse(JSON.stringify(this.staffForm)));
        }
      } else {
        // 添加新客服
        this.staffForm.id = Date.now();
        this.staffList.push(JSON.parse(JSON.stringify(this.staffForm)));
      }
      
      // 关闭弹窗
      this.closeStaffModal();
      
      // 提示保存成功
      uni.showToast({
        title: this.isEditing ? '客服修改成功' : '客服添加成功'
      });
    },
    
    // 确认删除客服
    confirmDeleteStaff(staff) {
      this.currentStaffId = staff.id;
      this.$refs.deleteConfirmPopup.open();
    },
    
    // 删除客服
    deleteStaff() {
      const index = this.staffList.findIndex(item => item.id === this.currentStaffId);
      if (index !== -1) {
        this.staffList.splice(index, 1);
      }
      
      this.$refs.deleteConfirmPopup.close();
      
      uni.showToast({
        title: '客服删除成功'
      });
    },
    
    // 关闭删除确认弹窗
    closeDeleteConfirm() {
      this.$refs.deleteConfirmPopup.close();
    },
    
    // 保存设置
    saveSettings() {
      // 这里应该调用API保存设置
      uni.showToast({
        title: '设置保存成功',
        icon: 'success'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.vip-service-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20rpx;
}

.title-section {
  .page-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
  
  .page-subtitle {
    font-size: 24rpx;
    color: #666;
    margin-top: 6rpx;
  }
}

.section-card {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  .section-title {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    position: relative;
    padding-left: 20rpx;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8rpx;
      height: 30rpx;
      background-color: #4A00E0;
      border-radius: 4rpx;
    }
  }
}

.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .switch-content {
    .switch-title {
      font-size: 30rpx;
      font-weight: bold;
      color: #333;
    }
    
    .switch-desc {
      font-size: 24rpx;
      color: #666;
      margin-top: 6rpx;
    }
  }
}

.form-item {
  margin-bottom: 24rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .form-label {
    display: block;
    font-size: 28rpx;
    color: #333;
    margin-bottom: 12rpx;
  }
  
  .form-input {
    width: 100%;
    height: 80rpx;
    border: 1rpx solid #ddd;
    border-radius: 8rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    box-sizing: border-box;
  }
  
  .form-input-group {
    display: flex;
    align-items: center;
    
    .form-input {
      width: 200rpx;
    }
    
    .input-suffix {
      margin-left: 10rpx;
      font-size: 28rpx;
      color: #333;
    }
  }
  
  .form-textarea {
    width: 100%;
    height: 160rpx;
    border: 1rpx solid #ddd;
    border-radius: 8rpx;
    padding: 20rpx;
    font-size: 28rpx;
    box-sizing: border-box;
  }
  
  .checkbox-group {
    display: flex;
    flex-wrap: wrap;
    
    .checkbox-item {
      display: flex;
      align-items: center;
      margin-right: 30rpx;
      margin-bottom: 20rpx;
      
      .checkbox-icon {
        width: 40rpx;
        height: 40rpx;
        border: 1rpx solid #ddd;
        border-radius: 8rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10rpx;
        
        .checkbox-inner {
          width: 24rpx;
          height: 24rpx;
          background-color: #4A00E0;
        }
      }
      
      &.active .checkbox-icon {
        border-color: #4A00E0;
      }
      
      .checkbox-text {
        font-size: 28rpx;
        color: #333;
      }
    }
  }
  
  .radio-group {
    display: flex;
    
    .radio-item {
      padding: 12rpx 30rpx;
      border: 1rpx solid #ddd;
      border-radius: 8rpx;
      margin-right: 20rpx;
      
      .radio-text {
        font-size: 28rpx;
        color: #333;
      }
      
      &.active {
        background-color: #4A00E0;
        border-color: #4A00E0;
        
        .radio-text {
          color: #fff;
        }
      }
    }
  }
  
  &.switch-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .form-label {
      margin-bottom: 0;
    }
  }
  
  .avatar-upload {
    .preview-avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
    }
    
    .upload-btn {
      width: 120rpx;
      height: 120rpx;
      border: 1rpx dashed #ddd;
      border-radius: 50%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      
      .upload-icon {
        font-size: 40rpx;
        color: #999;
        margin-bottom: 4rpx;
      }
      
      .upload-text {
        font-size: 20rpx;
        color: #999;
      }
    }
  }
}

.staff-list {
  margin-bottom: 24rpx;
  
  .staff-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .staff-info {
      display: flex;
      align-items: center;
      
      .staff-avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-right: 20rpx;
      }
      
      .staff-detail {
        .staff-name {
          font-size: 28rpx;
          color: #333;
          font-weight: bold;
        }
        
        .staff-position {
          font-size: 24rpx;
          color: #666;
          margin-top: 6rpx;
        }
      }
    }
    
    .staff-actions {
      display: flex;
      align-items: center;
      
      .staff-status {
        font-size: 24rpx;
        color: #999;
        padding: 4rpx 16rpx;
        background-color: #f5f5f5;
        border-radius: 20rpx;
        margin-right: 16rpx;
        
        &.active {
          color: #4A00E0;
          background-color: rgba(74, 0, 224, 0.1);
        }
      }
      
      .action-btn {
        font-size: 24rpx;
        padding: 4rpx 16rpx;
        border-radius: 20rpx;
        margin-left: 10rpx;
        
        &.edit {
          color: #4A00E0;
          background-color: rgba(74, 0, 224, 0.1);
        }
        
        &.delete {
          color: #ff4d4f;
          background-color: rgba(255, 77, 79, 0.1);
        }
      }
    }
  }
}

.add-btn {
  width: 100%;
  height: 80rpx;
  background-color: #f5f5f5;
  color: #666;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
}

.level-list {
  .level-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .level-checkbox {
      width: 40rpx;
      height: 40rpx;
      border: 1rpx solid #ddd;
      border-radius: 8rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20rpx;
      
      &.checked {
        background-color: #4A00E0;
        border-color: #4A00E0;
      }
      
      .checkbox-inner {
        width: 20rpx;
        height: 20rpx;
        background-color: #fff;
      }
    }
    
    .level-content {
      flex: 1;
      
      .level-name {
        font-size: 28rpx;
        color: #333;
        font-weight: bold;
      }
      
      .level-desc {
        font-size: 24rpx;
        color: #666;
        margin-top: 6rpx;
      }
    }
  }
}

.bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  .save-btn {
    width: 100%;
    height: 90rpx;
    background-color: #4A00E0;
    color: #fff;
    font-size: 32rpx;
    border-radius: 45rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.staff-form-popup {
  width: 650rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx;
    border-bottom: 1rpx solid #eee;
    
    .popup-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    
    .popup-close {
      font-size: 40rpx;
      color: #999;
    }
  }
  
  .popup-body {
    padding: 24rpx;
    max-height: 60vh;
    overflow-y: auto;
  }
  
  .popup-footer {
    display: flex;
    border-top: 1rpx solid #eee;
    
    button {
      flex: 1;
      height: 90rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      border-radius: 0;
      
      &.cancel-btn {
        background-color: #f5f5f5;
        color: #666;
      }
      
      &.confirm-btn {
        background-color: #4A00E0;
        color: #fff;
      }
    }
  }
}
</style>
<!-- 专属客服页面结束 --> 