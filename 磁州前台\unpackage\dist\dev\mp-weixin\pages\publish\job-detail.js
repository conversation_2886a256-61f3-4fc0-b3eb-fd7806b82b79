"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  __name: "job-detail",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    common_vendor.onMounted(() => {
      try {
        const sysInfo = common_vendor.index.getSystemInfoSync();
        statusBarHeight.value = sysInfo.statusBarHeight || 20;
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/publish/job-detail.vue:340", "获取状态栏高度失败", e);
      }
    });
    const goBack = () => {
      common_vendor.index.navigateBack({
        fail: () => {
          common_vendor.index.switchTab({
            url: "/pages/index/index"
          });
        }
      });
    };
    const formatTime = (timestamp) => {
      const date = new Date(timestamp);
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    };
    common_vendor.ref(false);
    const isFollowed = common_vendor.ref(false);
    const isShared = common_vendor.ref(false);
    const isCommented = common_vendor.ref(false);
    const showRedPacket = common_vendor.ref(false);
    const redPacketGrabbed = common_vendor.ref(false);
    const grabbedAmount = common_vendor.ref("0.00");
    const showCommentArea = common_vendor.ref(false);
    const commentContent = common_vendor.ref("");
    const shareViewCount = common_vendor.ref(0);
    const canGrabRedPacket = common_vendor.computed(() => {
      if (!jobData.value.redPacket.conditions || jobData.value.redPacket.conditions.length === 0) {
        return true;
      }
      let allConditionsMet = true;
      if (jobData.value.redPacket.conditions.includes("转发分享")) {
        if (!isShared.value || shareViewCount.value < 10) {
          allConditionsMet = false;
        }
      }
      if (jobData.value.redPacket.conditions.includes("关注店铺")) {
        if (!isFollowed.value) {
          allConditionsMet = false;
        }
      }
      return allConditionsMet;
    });
    const jobData = common_vendor.ref({
      id: "job12345",
      title: "前端开发工程师",
      salary: "8000-12000元/月",
      tags: ["五险一金", "双休", "包吃住"],
      publishTime: Date.now() - 864e5 * 2,
      // 2天前
      location: "磁县城区",
      experience: "1-3年",
      education: "大专及以上",
      headcount: 3,
      description: "负责公司产品的前端开发工作，包括但不限于PC端、移动端的页面开发和交互实现。与后端开发人员密切合作，确保前后端数据交互的顺畅和高效。参与产品需求分析和功能设计讨论，提供前端技术可行性建议。",
      responsibilities: [
        "负责公司Web前端页面的设计和开发",
        "根据产品需求，分析并给出最优的页面前端结构解决方案",
        "与后端工程师协作，完成数据交互、接口联调",
        "对现有项目进行性能优化，提升用户体验",
        "关注前端技术的发展，根据业务需求引入新技术"
      ],
      requirements: [
        "计算机相关专业，大专及以上学历",
        "1年以上前端开发经验，熟悉HTML5、CSS3、JavaScript",
        "熟悉Vue、React等主流前端框架至少一种",
        "了解响应式布局和移动端适配",
        "有良好的代码风格，重视代码质量",
        "具备良好的沟通能力和团队协作精神"
      ],
      benefits: [
        "五险一金",
        "双休",
        "带薪年假",
        "节日福利",
        "定期团建",
        "免费工作餐",
        "晋升空间"
      ],
      address: "河北省邯郸市磁县北关镇磁州大厦5层",
      contact: {
        name: "张经理",
        phone: "13912345678"
      },
      company: {
        name: "磁县科技有限公司",
        logo: "/static/images/company-logo.png",
        type: "互联网",
        size: "50-100人",
        isVerified: true
      },
      // 红包相关数据
      hasRedPacket: true,
      redPacket: {
        amount: "10.00",
        remain: 8,
        type: "fixed",
        // fixed:固定金额 random:随机金额
        total: 20,
        validity: "2024-06-30",
        description: "感谢您查看我们的招聘信息",
        conditions: ["转发分享"]
        // 领取条件
      }
    });
    const getRedPacketConditionText = () => {
      const conditions = jobData.value.redPacket.conditions || [];
      if (!conditions || conditions.length === 0) {
        return "投递简历再领一个";
      }
      if (conditions.length === 1) {
        return `${conditions[0]}可再领`;
      } else if (conditions.length === 2) {
        return `${conditions[0]}、${conditions[1]}可再领`;
      } else {
        return `${conditions[0]}、${conditions[1]}等可再领`;
      }
    };
    const openRedPacket = () => {
      showRedPacket.value = true;
    };
    const closeRedPacket = () => {
      showRedPacket.value = false;
    };
    const grabRedPacket = () => {
      if (!checkRedPacketConditions()) {
        return;
      }
      common_vendor.index.showLoading({
        title: "正在拆红包..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        redPacketGrabbed.value = true;
        if (jobData.value.redPacket.type === "random") {
          const baseAmount = parseFloat(jobData.value.redPacket.amount);
          const min = baseAmount * 0.5;
          const max = baseAmount * 1.5;
          grabbedAmount.value = (min + Math.random() * (max - min)).toFixed(2);
        } else {
          grabbedAmount.value = jobData.value.redPacket.amount;
        }
        jobData.value.redPacket.remain -= 1;
      }, 1e3);
    };
    const checkRedPacketConditions = () => {
      const conditions = jobData.value.redPacket.conditions || [];
      if (!conditions || conditions.length === 0) {
        return true;
      }
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      if (!userInfo) {
        common_vendor.index.showModal({
          title: "提示",
          content: "请先登录后再领取红包",
          confirmText: "去登录",
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.navigateTo({
                url: "/pages/login/login"
              });
            }
          }
        });
        return false;
      }
      let hasUnmetConditions = false;
      let unmetConditionsText = "";
      if (conditions.includes("转发分享")) {
        const hasShared = common_vendor.index.getStorageSync(`shared_${jobData.value.id}`) || false;
        const shareViewCount2 = common_vendor.index.getStorageSync(`share_views_${jobData.value.id}`) || 0;
        if (!hasShared) {
          hasUnmetConditions = true;
          unmetConditionsText += "- 请先转发分享此招聘信息\n";
        } else if (shareViewCount2 < 10) {
          hasUnmetConditions = true;
          unmetConditionsText += `- 您的分享需要至少10人查看(当前${shareViewCount2}人)
`;
        }
      }
      if (hasUnmetConditions) {
        common_vendor.index.showModal({
          title: "无法领取红包",
          content: `请先完成以下条件:
${unmetConditionsText}`,
          showCancel: false
        });
        return false;
      }
      return true;
    };
    const increaseShareViewCount = () => {
      const currentCount = common_vendor.index.getStorageSync(`share_views_${jobData.value.id}`) || 0;
      common_vendor.index.setStorageSync(`share_views_${jobData.value.id}`, currentCount + 1);
    };
    const markAsCommented = () => {
      common_vendor.index.setStorageSync(`commented_${jobData.value.id}`, true);
    };
    const markAsFollowed = () => {
      common_vendor.index.setStorageSync(`followed_${jobData.value.company.id}`, true);
    };
    const goToWallet = () => {
      common_vendor.index.navigateTo({
        url: "/subPackages/payment/pages/wallet"
      });
    };
    const callPhone = () => {
      common_vendor.index.makePhoneCall({
        phoneNumber: jobData.value.contact.phone,
        fail: () => {
          common_vendor.index.showToast({
            title: "拨打电话失败",
            icon: "none"
          });
        }
      });
    };
    const openLocation = () => {
      common_vendor.index.showToast({
        title: "查看地图位置",
        icon: "none"
      });
    };
    const goToHome = () => {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    };
    const posterImagePath = common_vendor.ref("");
    const showPosterFlag = common_vendor.ref(false);
    const generateShareImage = () => {
      common_vendor.index.showLoading({
        title: "正在生成海报...",
        mask: true
      });
      const posterData = {
        title: jobData.value.title,
        salary: jobData.value.salary,
        company: jobData.value.company.name,
        address: jobData.value.address,
        phone: jobData.value.contact.phone,
        requirements: jobData.value.requirements ? jobData.value.requirements.substring(0, 60) + "..." : "",
        qrcode: "/static/images/tabbar/客服微信.png",
        logo: jobData.value.company.logo,
        bgImage: "/static/images/banner/banner-1.png"
      };
      const ctx = common_vendor.index.createCanvasContext("posterCanvas");
      ctx.save();
      ctx.drawImage(posterData.bgImage, 0, 0, 600, 400);
      ctx.setFillStyle("rgba(0, 0, 0, 0.35)");
      ctx.fillRect(0, 0, 600, 900);
      ctx.restore();
      ctx.save();
      ctx.setFillStyle("#ffffff");
      ctx.fillRect(30, 280, 540, 550);
      ctx.restore();
      ctx.save();
      ctx.beginPath();
      ctx.arc(300, 200, 80, 0, 2 * Math.PI);
      ctx.setFillStyle("#ffffff");
      ctx.fill();
      ctx.clip();
      ctx.drawImage(posterData.logo, 220, 120, 160, 160);
      ctx.restore();
      ctx.setFillStyle("#333333");
      ctx.setFontSize(32);
      ctx.setTextAlign("center");
      ctx.fillText(posterData.title, 300, 350);
      ctx.setFillStyle("#FF6B6B");
      ctx.setFontSize(28);
      ctx.fillText(posterData.salary, 300, 400);
      ctx.beginPath();
      ctx.setStrokeStyle("#eeeeee");
      ctx.setLineWidth(2);
      ctx.moveTo(100, 430);
      ctx.lineTo(500, 430);
      ctx.stroke();
      ctx.setFillStyle("#666666");
      ctx.setFontSize(24);
      ctx.setTextAlign("left");
      ctx.fillText("公司: " + posterData.company, 80, 480);
      ctx.fillText("地址: " + posterData.address, 80, 520);
      const wrapText = (ctx2, text, x, y, maxWidth, lineHeight) => {
        if (text.length === 0)
          return;
        const words = text.split("");
        let line = "";
        let testLine = "";
        let lineCount = 0;
        for (let n = 0; n < words.length; n++) {
          testLine += words[n];
          const metrics = ctx2.measureText(testLine);
          const testWidth = metrics.width;
          if (testWidth > maxWidth && n > 0) {
            ctx2.fillText(line, x, y + lineCount * lineHeight);
            line = words[n];
            testLine = words[n];
            lineCount++;
            if (lineCount >= 3) {
              line += "...";
              ctx2.fillText(line, x, y + lineCount * lineHeight);
              break;
            }
          } else {
            line = testLine;
          }
        }
        if (lineCount < 3) {
          ctx2.fillText(line, x, y + lineCount * lineHeight);
        }
      };
      ctx.setFillStyle("#666666");
      ctx.fillText("岗位要求:", 80, 560);
      wrapText(ctx, posterData.requirements, 80, 600, 440, 35);
      if (posterData.phone) {
        ctx.fillText("联系电话: " + posterData.phone, 80, 680);
      }
      ctx.drawImage(posterData.qrcode, 225, 720, 150, 150);
      ctx.setFillStyle("#999999");
      ctx.setFontSize(20);
      ctx.setTextAlign("center");
      ctx.fillText("长按识别二维码查看详情", 300, 880);
      ctx.setFillStyle("#333333");
      ctx.setFontSize(24);
      ctx.fillText("磁县同城 - 招聘信息", 300, 840);
      ctx.draw(false, () => {
        setTimeout(() => {
          common_vendor.index.canvasToTempFilePath({
            canvasId: "posterCanvas",
            success: (res) => {
              common_vendor.index.hideLoading();
              showPosterModal(res.tempFilePath);
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/publish/job-detail.vue:820", "生成海报失败", err);
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "生成海报失败",
                icon: "none"
              });
            }
          });
        }, 800);
      });
    };
    const showPosterModal = (posterPath) => {
      posterImagePath.value = posterPath;
      showPosterFlag.value = true;
      common_vendor.index.showModal({
        title: "海报已生成",
        content: "海报已生成，是否保存到相册？",
        confirmText: "保存",
        success: (res) => {
          if (res.confirm) {
            savePosterToAlbum(posterPath);
          } else {
            common_vendor.index.previewImage({
              urls: [posterPath],
              current: posterPath
            });
          }
        }
      });
    };
    const savePosterToAlbum = (posterPath) => {
      common_vendor.index.showLoading({
        title: "正在保存..."
      });
      common_vendor.index.saveImageToPhotosAlbum({
        filePath: posterPath,
        success: () => {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "已保存到相册",
            icon: "success"
          });
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at pages/publish/job-detail.vue:872", "保存失败", err);
          if (err.errMsg.indexOf("auth deny") > -1) {
            common_vendor.index.showModal({
              title: "提示",
              content: "保存失败，请授权相册权限后重试",
              confirmText: "去设置",
              success: (res) => {
                if (res.confirm) {
                  common_vendor.index.openSetting();
                }
              }
            });
          } else {
            common_vendor.index.showToast({
              title: "保存失败",
              icon: "none"
            });
          }
        }
      });
    };
    const relatedJobs = common_vendor.ref([]);
    const loadRelatedJobs = () => {
      setTimeout(() => {
        relatedJobs.value = [
          {
            id: "job001",
            title: "销售经理",
            salary: "6000-8000",
            companyName: "磁州科技有限公司",
            companyLogo: "/static/images/tabbar/公司.png",
            tags: ["五险一金", "包吃住", "加班补助"]
          },
          {
            id: "job002",
            title: "前台文员",
            salary: "3500-4500",
            companyName: "磁州商贸有限公司",
            companyLogo: "/static/images/tabbar/企业.png",
            tags: ["五险", "双休", "有食堂"]
          },
          {
            id: "job003",
            title: "网络销售",
            salary: "4000-8000",
            companyName: "磁州网络科技公司",
            companyLogo: "/static/images/tabbar/网络.png",
            tags: ["高提成", "弹性工作", "有培训"]
          }
        ];
      }, 500);
    };
    const navigateToJobDetail = (id) => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      if (id === options.id) {
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/publish/job-detail?id=${id}`
      });
    };
    const navigateToJobList = (e) => {
      var _a;
      if (e)
        e.stopPropagation();
      const jobCategory = ((_a = jobData.value.tags) == null ? void 0 : _a[0]) || "";
      common_vendor.index.navigateTo({
        url: `/subPackages/service/pages/filter?type=job&title=${encodeURIComponent("招聘信息")}&category=${encodeURIComponent(jobCategory)}&active=job`
      });
    };
    common_vendor.onMounted(() => {
      common_vendor.index.setNavigationBarTitle({
        title: "招聘详情"
      });
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      if (options.id) {
        common_vendor.index.__f__("log", "at pages/publish/job-detail.vue:973", "正在获取招聘详情，ID:", options.id);
        isFollowed.value = common_vendor.index.getStorageSync(`followed_${jobData.value.company.id}`) || false;
        isShared.value = common_vendor.index.getStorageSync(`shared_${jobData.value.id}`) || false;
        isCommented.value = common_vendor.index.getStorageSync(`commented_${jobData.value.id}`) || false;
        shareViewCount.value = common_vendor.index.getStorageSync(`share_views_${jobData.value.id}`) || 0;
        if (options.source === "share") {
          increaseShareViewCount();
          shareViewCount.value += 1;
        }
      }
      loadRelatedJobs();
    });
    const toggleFollow = () => {
      if (!checkLoginStatus()) {
        return;
      }
      isFollowed.value = !isFollowed.value;
      if (isFollowed.value) {
        markAsFollowed();
        common_vendor.index.showToast({
          title: "关注成功",
          icon: "success"
        });
      } else {
        common_vendor.index.removeStorageSync(`followed_${jobData.value.company.id}`);
        common_vendor.index.showToast({
          title: "已取消关注",
          icon: "none"
        });
      }
    };
    const showCommentInput = () => {
      if (!checkLoginStatus()) {
        return;
      }
      showCommentArea.value = true;
    };
    const submitComment = () => {
      if (commentContent.value.trim() === "") {
        common_vendor.index.showToast({
          title: "评论内容不能为空",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        markAsCommented();
        common_vendor.index.showToast({
          title: "评论成功",
          icon: "success"
        });
        commentContent.value = "";
        showCommentArea.value = false;
      }, 800);
    };
    const checkLoginStatus = () => {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      if (!userInfo) {
        common_vendor.index.showModal({
          title: "提示",
          content: "请先登录后再操作",
          confirmText: "去登录",
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.navigateTo({
                url: "/pages/login/login"
              });
            }
          }
        });
        return false;
      }
      return true;
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$5,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_assets._imports_10,
        e: common_vendor.o(generateShareImage),
        f: jobData.value.company.logo,
        g: common_vendor.t(jobData.value.company.name),
        h: common_vendor.t(jobData.value.company.type),
        i: common_vendor.t(jobData.value.company.size),
        j: jobData.value.company.isVerified
      }, jobData.value.company.isVerified ? {} : {}, {
        k: common_vendor.t(jobData.value.title),
        l: common_vendor.t(jobData.value.salary),
        m: common_vendor.f(jobData.value.tags, (tag, index, i0) => {
          return {
            a: common_vendor.t(tag),
            b: index
          };
        }),
        n: common_vendor.t(formatTime(jobData.value.publishTime)),
        o: common_vendor.t(jobData.value.location),
        p: common_vendor.t(jobData.value.experience),
        q: common_vendor.t(jobData.value.education),
        r: common_vendor.t(jobData.value.headcount),
        s: jobData.value.description,
        t: common_vendor.f(jobData.value.responsibilities, (item, index, i0) => {
          return {
            a: common_vendor.t(item),
            b: index
          };
        }),
        v: common_vendor.f(jobData.value.requirements, (item, index, i0) => {
          return {
            a: common_vendor.t(item),
            b: index
          };
        }),
        w: common_vendor.f(jobData.value.benefits, (benefit, index, i0) => {
          return {
            a: common_vendor.t(benefit),
            b: index
          };
        }),
        x: common_vendor.t(jobData.value.address),
        y: common_vendor.o(openLocation),
        z: common_assets._imports_2$2,
        A: common_vendor.t(jobData.value.contact.name),
        B: common_vendor.t(jobData.value.contact.phone),
        C: common_vendor.o(callPhone),
        D: jobData.value.hasRedPacket
      }, jobData.value.hasRedPacket ? {
        E: common_assets._imports_0$4,
        F: common_vendor.t(jobData.value.redPacket.type === "random" ? "随机金额红包" : "查看职位领红包"),
        G: common_vendor.t(jobData.value.redPacket.remain),
        H: common_vendor.t(getRedPacketConditionText()),
        I: common_vendor.t(jobData.value.redPacket.amount),
        J: common_vendor.o(openRedPacket)
      } : {}, {
        K: common_vendor.f(relatedJobs.value.slice(0, 3), (job, index, i0) => {
          return common_vendor.e({
            a: job.companyLogo,
            b: common_vendor.t(job.title),
            c: common_vendor.t(job.companyName),
            d: common_vendor.f(job.tags.slice(0, 2), (tag, tagIndex, i1) => {
              return {
                a: common_vendor.t(tag),
                b: tagIndex
              };
            }),
            e: job.tags.length > 2
          }, job.tags.length > 2 ? {
            f: common_vendor.t(job.tags.length - 2)
          } : {}, {
            g: common_vendor.t(job.salary),
            h: index,
            i: common_vendor.o(($event) => navigateToJobDetail(job.id), index)
          });
        }),
        L: relatedJobs.value.length === 0
      }, relatedJobs.value.length === 0 ? {
        M: common_assets._imports_1$3
      } : {}, {
        N: relatedJobs.value.length > 0
      }, relatedJobs.value.length > 0 ? {
        O: common_vendor.o(navigateToJobList)
      } : {}, {
        P: common_assets._imports_12,
        Q: common_vendor.o(goToHome),
        R: isFollowed.value ? "/static/images/tabbar/已收藏选中.png" : "/static/images/tabbar/a关注.png",
        S: common_vendor.t(isFollowed.value ? "已关注" : "关注"),
        T: isFollowed.value ? 1 : "",
        U: common_vendor.o(toggleFollow),
        V: common_assets._imports_3$3,
        W: common_assets._imports_14,
        X: common_vendor.o(showCommentInput),
        Y: common_vendor.o(callPhone),
        Z: showRedPacket.value
      }, showRedPacket.value ? common_vendor.e({
        aa: !redPacketGrabbed.value
      }, !redPacketGrabbed.value ? common_vendor.e({
        ab: common_vendor.o(closeRedPacket),
        ac: common_vendor.t(jobData.value.redPacket.amount),
        ad: jobData.value.redPacket.conditions.includes("转发分享")
      }, jobData.value.redPacket.conditions.includes("转发分享") ? {
        ae: common_assets._imports_2$1,
        af: isShared.value ? 1 : "",
        ag: common_vendor.t(shareViewCount.value),
        ah: common_vendor.t(isShared.value ? "已分享" : "去分享"),
        ai: isShared.value ? 1 : "",
        aj: isShared.value ? 1 : ""
      } : {}, {
        ak: common_vendor.t(canGrabRedPacket.value ? "立即领取" : "完成任务领取"),
        al: canGrabRedPacket.value ? 1 : "",
        am: common_vendor.o(grabRedPacket)
      }) : {}, {
        an: redPacketGrabbed.value
      }, redPacketGrabbed.value ? {
        ao: common_assets._imports_9,
        ap: common_vendor.t(grabbedAmount.value),
        aq: common_vendor.o(goToWallet),
        ar: common_vendor.o(closeRedPacket)
      } : {}, {
        as: common_vendor.o(() => {
        })
      }) : {}, {
        at: showCommentArea.value
      }, showCommentArea.value ? {
        av: common_vendor.o(($event) => showCommentArea.value = false),
        aw: common_vendor.o(($event) => showCommentArea.value = false),
        ax: commentContent.value,
        ay: common_vendor.o(($event) => commentContent.value = $event.detail.value),
        az: common_vendor.t(commentContent.value.length),
        aA: common_vendor.o(submitComment)
      } : {});
    };
  }
};
_sfc_main.__runtimeHooks = 2;
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/publish/job-detail.js.map
