{"version": 3, "file": "TabBar.js", "sources": ["components/TabBar.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7AvY29tcG9uZW50cy9UYWJCYXIudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"tab-bar\">\r\n    <view \r\n      class=\"tab-item\" \r\n      v-for=\"(tab, index) in tabs\" \r\n      :key=\"index\"\r\n      :class=\"{ active: activeTab === tab.id }\"\r\n      :data-tab=\"tab.id\"\r\n      @tap=\"switchTab(tab.id)\">\r\n      <view class=\"tab-icon\" v-html=\"tab.icon\"></view>\r\n      <text class=\"tab-text\">{{tab.name}}</text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    activeTab: {\r\n      type: String,\r\n      default: 'marketing'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      tabs: [\r\n        {\r\n          id: 'dashboard',\r\n          name: '数据概览',\r\n          icon: '<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><rect x=\"3\" y=\"3\" width=\"7\" height=\"7\"></rect><rect x=\"14\" y=\"3\" width=\"7\" height=\"7\"></rect><rect x=\"14\" y=\"14\" width=\"7\" height=\"7\"></rect><rect x=\"3\" y=\"14\" width=\"7\" height=\"7\"></rect></svg>',\r\n          path: ''\r\n        },\r\n        {\r\n          id: 'store',\r\n          name: '店铺管理',\r\n          icon: '<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"currentColor\"><path d=\"M20 4H4v2h16V4zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6h1zm-9 4H6v-4h6v4z\"/></svg>',\r\n          path: '/subPackages/merchant-admin/pages/store/index'\r\n        },\r\n        {\r\n          id: 'marketing',\r\n          name: '营销中心',\r\n          icon: '<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"currentColor\"><path d=\"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\"/></svg>',\r\n          path: '/subPackages/merchant-admin-marketing/pages/marketing/index'\r\n        },\r\n        {\r\n          id: 'order',\r\n          name: '订单管理',\r\n          icon: '<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"currentColor\"><path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z\"/></svg>',\r\n          path: '/subPackages/merchant-admin-order/pages/order/index'\r\n        },\r\n        {\r\n          id: 'more',\r\n          name: '更多',\r\n          icon: '<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"currentColor\"><path d=\"M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z\"/></svg>',\r\n          path: '/subPackages/merchant-admin-home/pages/merchant-home/index'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    switchTab(tabId) {\r\n      // 如果点击的不是当前选中的标签，则进行跳转\r\n      if (tabId !== this.activeTab) {\r\n        const tab = this.tabs.find(t => t.id === tabId);\r\n        if (tab) {\r\n          // 优先使用redirectTo，避免页面堆栈过多\r\n          uni.redirectTo({\r\n            url: tab.path,\r\n            fail: (err) => {\r\n              // 如果redirectTo失败，尝试使用switchTab\r\n              uni.switchTab({\r\n                url: tab.path,\r\n                fail: (switchErr) => {\r\n                  console.error('页面跳转失败:', switchErr);\r\n                  uni.showToast({\r\n                    title: '页面跳转失败，请稍后再试',\r\n                    icon: 'none'\r\n                  });\r\n                }\r\n              });\r\n            }\r\n          });\r\n        }\r\n      }\r\n      \r\n      // 触发事件通知父组件\r\n      this.$emit('tab-change', tabId);\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.tab-bar {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 56px;\r\n  background-color: #FFFFFF;\r\n  display: flex;\r\n  justify-content: space-around;\r\n  align-items: center;\r\n  border-top: 1px solid #F0F0F0;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n  z-index: 100;\r\n}\r\n\r\n.tab-item {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 100%;\r\n  padding: 6px 0;\r\n  box-sizing: border-box;\r\n  position: relative;\r\n}\r\n\r\n.tab-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  margin-bottom: 4px;\r\n  color: #999999;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  overflow: visible;\r\n}\r\n\r\n/* 强制SVG图标显示 */\r\n.tab-icon svg {\r\n  width: 22px !important;\r\n  height: 22px !important;\r\n  min-width: 22px !important;\r\n  min-height: 22px !important;\r\n  display: block !important;\r\n  visibility: visible !important;\r\n  opacity: 1 !important;\r\n  fill: currentColor !important;\r\n}\r\n\r\n.tab-text {\r\n  font-size: 10px;\r\n  color: #999999;\r\n}\r\n\r\n.tab-item.active .tab-icon {\r\n  color: #1989FA;\r\n}\r\n\r\n.tab-item.active .tab-text {\r\n  color: #1989FA;\r\n}\r\n\r\n/* 营销中心用橙色 */\r\n.tab-item.active[data-tab=\"marketing\"] .tab-icon {\r\n  color: #FF7600;\r\n}\r\n\r\n.tab-item.active[data-tab=\"marketing\"] .tab-text {\r\n  color: #FF7600;\r\n}\r\n\r\n/* 数据概览用蓝色 */\r\n.tab-item.active[data-tab=\"dashboard\"] .tab-icon {\r\n  color: #1989FA;\r\n}\r\n\r\n.tab-item.active[data-tab=\"dashboard\"] .tab-text {\r\n  color: #1989FA;\r\n}\r\n\r\n/* 添加底部空间 */\r\n.bottom-space {\r\n  height: 60px;\r\n}\r\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/components/TabBar.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;AAgBA,MAAK,YAAU;AAAA,EACb,OAAO;AAAA,IACL,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,MAAM;AAAA,QACJ;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,UAAU,OAAO;AAEf,UAAI,UAAU,KAAK,WAAW;AAC5B,cAAM,MAAM,KAAK,KAAK,KAAK,OAAK,EAAE,OAAO,KAAK;AAC9C,YAAI,KAAK;AAEPA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK,IAAI;AAAA,YACT,MAAM,CAAC,QAAQ;AAEbA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,KAAK,IAAI;AAAA,gBACT,MAAM,CAAC,cAAc;AACnBA,oFAAc,WAAW,SAAS;AAClCA,gCAAAA,MAAI,UAAU;AAAA,oBACZ,OAAO;AAAA,oBACP,MAAM;AAAA,kBACR,CAAC;AAAA,gBACH;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAGA,WAAK,MAAM,cAAc,KAAK;AAAA,IAChC;AAAA,EACF;AACF;;;;;;;;;;;;;;;;ACxFA,GAAG,gBAAgB,SAAS;"}