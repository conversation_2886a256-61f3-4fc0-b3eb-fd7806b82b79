/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.channels-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.add-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 概览部分样式 */
.overview-section {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
}
.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.overview-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.date-filter {
  display: flex;
  align-items: center;
  background-color: #F5F7FA;
  padding: 6px 10px;
  border-radius: 15px;
  font-size: 12px;
  color: #6B0FBE;
}
.filter-arrow {
  margin-left: 4px;
  display: flex;
  align-items: center;
}

/* 数据卡片样式 */
.data-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}
.data-card {
  position: relative;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(0, 0, 0, 0.04);
  overflow: hidden;
}
.card-value {
  font-size: 22px;
  font-weight: 700;
  color: #333;
  margin-bottom: 4px;
}
.card-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}
.card-trend {
  display: flex;
  align-items: center;
  font-size: 12px;
}
.card-trend.up {
  color: #34C759;
}
.card-trend.up .trend-icon {
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 6px solid #34C759;
  margin-right: 4px;
}
.card-trend.down {
  color: #FF3B30;
}
.card-trend.down .trend-icon {
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 6px solid #FF3B30;
  margin-right: 4px;
}
.card-icon {
  position: absolute;
  top: 14px;
  right: 14px;
  opacity: 0.1;
  transform: scale(1.5);
  transform-origin: top right;
}

/* 筛选部分样式 */
.filter-section {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
}
.search-bar {
  display: flex;
  align-items: center;
  background-color: #F5F7FA;
  border-radius: 12px;
  padding: 0 12px;
  margin-bottom: 16px;
}
.search-icon {
  margin-right: 8px;
}
.search-input {
  flex: 1;
  height: 40px;
  font-size: 14px;
  background-color: transparent;
  border: none;
  color: #333;
}
.filter-tabs {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #F0F0F0;
}
.filter-tab {
  flex: 1;
  text-align: center;
  padding: 12px 0;
  font-size: 14px;
  color: #999;
  position: relative;
}
.filter-tab.active {
  color: #6B0FBE;
  font-weight: 500;
}
.filter-tab.active::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 25%;
  width: 50%;
  height: 3px;
  background-color: #6B0FBE;
  border-radius: 3px;
}

/* 渠道列表样式 */
.channels-list-section {
  margin: 16px;
}
.channel-item {
  background-color: #FFFFFF;
  border-radius: 20px;
  margin-bottom: 16px;
  padding: 16px;
  display: flex;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.03);
}
.channel-avatar-container {
  position: relative;
  margin-right: 16px;
}
.channel-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 2px solid #FFFFFF;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.channel-status {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  border: 2px solid #FFFFFF;
}
.channel-status.online {
  background-color: #34C759;
}
.channel-status.offline {
  background-color: #999999;
}
.channel-info {
  flex: 1;
}
.channel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}
.channel-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.channel-level {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}
.channel-level.level-1 {
  background-color: #E5E5EA;
  color: #8E8E93;
}
.channel-level.level-2 {
  background-color: #FFD700;
  color: #8B6914;
}
.channel-level.level-3 {
  background-color: #B9F2FF;
  color: #007AFF;
}
.channel-stats {
  display: flex;
  margin: 12px 0;
}
.stat-item {
  flex: 1;
  text-align: center;
  border-right: 1px solid #EEEEEE;
}
.stat-item:last-child {
  border-right: none;
}
.stat-value {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.stat-label {
  display: block;
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}
.channel-actions {
  display: flex;
  margin-top: 12px;
  border-top: 1px solid #EEEEEE;
  padding-top: 12px;
}
.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 13px;
}
.action-btn svg {
  margin-right: 4px;
}
.action-btn.view-qrcode {
  color: #6B0FBE;
}
.action-btn.edit-channel {
  color: #6B0FBE;
  border-left: 1px solid #EEEEEE;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}
.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}
.empty-text {
  font-size: 14px;
  color: #999;
  margin-bottom: 16px;
}
.empty-btn {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #FFFFFF;
  border: none;
  border-radius: 20px;
  padding: 8px 20px;
  font-size: 14px;
}

/* 浮动按钮 */
.floating-btn {
  position: fixed;
  right: 24px;
  bottom: 40px;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(107, 15, 190, 0.3);
  z-index: 90;
}