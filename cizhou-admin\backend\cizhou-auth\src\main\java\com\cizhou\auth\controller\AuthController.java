package com.cizhou.auth.controller;

import com.cizhou.auth.dto.LoginRequest;
import com.cizhou.auth.dto.LoginResponse;
import com.cizhou.auth.dto.RefreshTokenRequest;
import com.cizhou.auth.dto.ChangePasswordRequest;
import com.cizhou.auth.service.AuthService;
import com.cizhou.common.core.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * 认证控制器
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Tag(name = "认证管理", description = "用户认证相关接口")
public class AuthController {

    private final AuthService authService;

    @Operation(summary = "用户登录")
    @PostMapping("/login")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest request) {
        log.info("用户登录请求: {}", request.getUsername());
        LoginResponse response = authService.login(request);
        return Result.success("登录成功", response);
    }

    @Operation(summary = "用户登出")
    @PostMapping("/logout")
    public Result<Void> logout(HttpServletRequest request) {
        String token = getTokenFromRequest(request);
        authService.logout(token);
        return Result.success("登出成功");
    }

    @Operation(summary = "刷新Token")
    @PostMapping("/refresh-token")
    public Result<LoginResponse> refreshToken(@Valid @RequestBody RefreshTokenRequest request) {
        LoginResponse response = authService.refreshToken(request.getRefreshToken());
        return Result.success("Token刷新成功", response);
    }

    @Operation(summary = "获取用户信息")
    @GetMapping("/user-info")
    public Result<Object> getUserInfo(HttpServletRequest request) {
        String token = getTokenFromRequest(request);
        Object userInfo = authService.getUserInfo(token);
        return Result.success("获取用户信息成功", userInfo);
    }

    @Operation(summary = "获取用户权限")
    @GetMapping("/permissions")
    public Result<Object> getUserPermissions(HttpServletRequest request) {
        String token = getTokenFromRequest(request);
        Object permissions = authService.getUserPermissions(token);
        return Result.success("获取用户权限成功", permissions);
    }

    @Operation(summary = "修改密码")
    @PostMapping("/change-password")
    public Result<Void> changePassword(@Valid @RequestBody ChangePasswordRequest request,
                                       HttpServletRequest httpRequest) {
        String token = getTokenFromRequest(httpRequest);
        authService.changePassword(token, request);
        return Result.success("密码修改成功");
    }

    @Operation(summary = "获取验证码")
    @GetMapping("/captcha")
    public void getCaptcha(HttpServletRequest request, HttpServletResponse response) throws IOException {
        authService.generateCaptcha(request, response);
    }

    @Operation(summary = "验证Token")
    @PostMapping("/validate")
    public Result<Boolean> validateToken(@RequestParam String token) {
        boolean valid = authService.validateToken(token);
        return Result.success("Token验证完成", valid);
    }

    /**
     * 从请求中获取Token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        return request.getParameter("token");
    }
}
