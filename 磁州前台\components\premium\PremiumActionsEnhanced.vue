<template>
  <view class="premium-actions-enhanced">
    <!-- 主要操作面板 -->
    <view class="premium-panel" :class="{ 'panel-expanded': isPanelExpanded }">
      <!-- 面板头部 -->
      <view class="panel-header" @click="togglePanel">
        <text class="panel-title">{{ title }}</text>
        <view class="panel-icon" :animation="headerIconAnimation">
          <text class="iconfont" :class="isPanelExpanded ? 'icon-arrow-up' : 'icon-arrow-down'"></text>
        </view>
      </view>
      
      <!-- 面板内容 -->
      <view class="panel-content" v-if="isPanelExpanded" :animation="panelAnimation">
        <view class="action-grid">
          <!-- 发布操作 -->
          <view 
            class="action-item" 
            @click="handleAction('publish')"
            :animation="getItemAnimation(0)"
          >
            <view class="action-icon publish-icon">
              <image :src="iconConfig.publish" mode="aspectFit"></image>
            </view>
            <text class="action-label">发布信息</text>
          </view>
          
          <!-- 置顶操作 -->
          <view 
            class="action-item" 
            @click="handleAction('top')"
            :animation="getItemAnimation(1)"
          >
            <view class="action-icon top-icon">
              <image :src="iconConfig.top" mode="aspectFit"></image>
            </view>
            <text class="action-label">置顶信息</text>
          </view>
          
          <!-- 刷新操作 -->
          <view 
            class="action-item" 
            @click="handleAction('refresh')"
            :animation="getItemAnimation(2)"
          >
            <view class="action-icon refresh-icon">
              <image :src="iconConfig.refresh" mode="aspectFit"></image>
            </view>
            <text class="action-label">刷新信息</text>
          </view>
        </view>
        
        <!-- 用户账户信息 -->
        <view class="user-account-info" v-if="showAccountInfo">
          <view class="account-item">
            <text class="account-label">账户余额</text>
            <text class="account-value">¥{{ userAccount.balance }}</text>
          </view>
          <view class="account-item">
            <text class="account-label">剩余免费广告次数</text>
            <text class="account-value">{{ userAccount.freeAdCount }}</text>
          </view>
          <view class="account-item">
            <text class="account-label">会员等级</text>
            <text class="account-value vip-level">{{ getVipLevelText() }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 模态框 -->
    <view class="modal-overlay" v-if="showModal" @click="closeModal" :animation="modalOverlayAnimation"></view>
    <view class="modal-container" v-if="showModal" :animation="modalAnimation">
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">{{ modalConfig.title }}</text>
          <view class="modal-close" @click="closeModal">
            <text class="iconfont icon-close"></text>
          </view>
        </view>
        
        <view class="modal-body">
          <!-- 操作说明 -->
          <view class="modal-description">
            <text>{{ modalConfig.description }}</text>
          </view>
          
          <!-- 选项列表 -->
          <view class="option-list">
            <view 
              class="option-item" 
              v-for="(option, index) in modalConfig.options" 
              :key="index"
              @click="selectOption(option)"
              :class="{ 'option-selected': selectedOption === option }"
              :animation="getOptionAnimation(index)"
            >
              <view class="option-content">
                <view class="option-icon">
                  <image :src="option.icon" mode="aspectFit"></image>
                </view>
                <view class="option-details">
                  <text class="option-title">{{ option.title }}</text>
                  <text class="option-subtitle">{{ option.subtitle }}</text>
                </view>
              </view>
              <view class="option-price">
                <text class="option-original-price" v-if="option.originalPrice">{{ option.originalPrice }}</text>
                <text :class="{'option-discount-price': option.originalPrice}">{{ option.price }}</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 底部按钮 -->
        <view class="modal-footer">
          <button class="btn-cancel" @click="closeModal">取消</button>
          <button class="btn-confirm" @click="confirmAction" :disabled="!selectedOption">确认</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { showAdvertisement, processPayment, logOperation, getUserAccount } from '@/services/AdPaymentService';
import { createSlideInAnimation, createSlideOutAnimation, createBounceAnimation } from '@/utils/AnimationUtils';
import { publishConfig, topConfig, refreshConfig, vipConfig, iconConfig, getTopOptions } from '@/config/premiumConfig';

// 组件属性
const props = defineProps({
  title: {
    type: String,
    default: '推广操作'
  },
  infoId: {
    type: String,
    default: ''
  },
  showAccountInfo: {
    type: Boolean,
    default: true
  }
});

// 组件事件
const emit = defineEmits(['action-completed']);

// 响应式状态
const isPanelExpanded = ref(false);
const showModal = ref(false);
const selectedOption = ref(null);
const currentAction = ref('');
const userAccount = reactive({
  balance: 0,
  points: 0,
  vipLevel: 0,
  freeAdCount: 0,
  hasPaymentPassword: false
});

// 动画状态
const headerIconAnimation = ref(null);
const panelAnimation = ref(null);
const modalAnimation = ref(null);
const modalOverlayAnimation = ref(null);
const itemAnimations = ref([]);

// 模态框配置
const modalConfig = reactive({
  title: '',
  description: '',
  options: []
});

// 组件初始化
onMounted(async () => {
  // 获取用户账户信息
  try {
    const accountInfo = await getUserAccount();
    Object.assign(userAccount, accountInfo);
  } catch (error) {
    console.error('获取用户账户信息失败:', error);
  }
  
  // 初始化动画数组
  for (let i = 0; i < 3; i++) {
    itemAnimations.value.push(null);
  }
});

// 获取VIP等级文本
const getVipLevelText = () => {
  if (userAccount.vipLevel === 0) {
    return '普通用户';
  }
  
  for (const vipLevel of vipConfig.levels) {
    if (vipLevel.level === userAccount.vipLevel) {
      return vipLevel.name;
    }
  }
  
  return `VIP ${userAccount.vipLevel}`;
};

// 获取操作项动画
const getItemAnimation = (index) => {
  return itemAnimations.value[index];
};

// 获取选项动画
const getOptionAnimation = (index) => {
  return null; // 将在完整版本中实现
};

// 切换面板展开/折叠
const togglePanel = () => {
  isPanelExpanded.value = !isPanelExpanded.value;
  
  // 应用动画
  if (isPanelExpanded.value) {
    // 展开面板时的动画
    headerIconAnimation.value = createBounceAnimation(null, { scale: 1.2 }).export();
    
    // 为每个操作项添加动画
    for (let i = 0; i < 3; i++) {
      setTimeout(() => {
        itemAnimations.value[i] = createSlideInAnimation(null, {
          direction: 'bottom',
          distance: 50,
          duration: 300
        }).export();
      }, i * 100);
    }
  } else {
    // 折叠面板时的动画
    headerIconAnimation.value = createBounceAnimation(null, { scale: 1.2 }).export();
  }
};

// 处理操作
const handleAction = (action) => {
  currentAction.value = action;
  selectedOption.value = null;
  
  // 根据不同操作配置模态框
  switch(action) {
    case 'publish':
      configurePublishModal();
      break;
    case 'top':
      configureTopModal();
      break;
    case 'refresh':
      configureRefreshModal();
      break;
  }
  
  showModal.value = true;
  
  // 应用动画
  modalOverlayAnimation.value = createSlideInAnimation(null, {
    direction: 'bottom',
    distance: 0,
    duration: 300
  }).export();
  
  modalAnimation.value = createSlideInAnimation(null, {
    direction: 'bottom',
    distance: 100,
    duration: 300
  }).export();
};

// 配置发布模态框
const configurePublishModal = () => {
  modalConfig.title = '选择发布方式';
  modalConfig.description = '选择一种方式发布您的信息';
  modalConfig.options = [
    {
      title: '免费发布',
      subtitle: `观看${publishConfig.ad.duration}秒广告后发布`,
      price: '免费',
      icon: iconConfig.adPublish,
      type: 'ad'
    },
    {
      title: '付费发布',
      subtitle: '立即发布，无需观看广告',
      price: `¥${publishConfig.paid.price.toFixed(2)}`,
      icon: iconConfig.paidPublish,
      type: 'paid'
    }
  ];
};

// 配置置顶模态框
const configureTopModal = () => {
  modalConfig.title = '选择置顶方式';
  modalConfig.description = '选择一种方式将您的信息置顶';
  modalConfig.options = getTopOptions(userAccount.vipLevel);
};

// 配置刷新模态框
const configureRefreshModal = () => {
  modalConfig.title = '选择刷新方式';
  modalConfig.description = '选择一种方式刷新您的信息';
  modalConfig.options = [
    {
      title: '广告刷新',
      subtitle: `观看${refreshConfig.ad.duration}秒广告刷新一次`,
      price: '免费',
      icon: iconConfig.adRefresh,
      type: 'ad'
    },
    {
      title: '付费刷新',
      subtitle: '立即刷新信息至列表顶部',
      price: `¥${refreshConfig.paid.price.toFixed(2)}`,
      icon: iconConfig.paidRefresh,
      type: 'paid'
    }
  ];
};

// 选择选项
const selectOption = (option) => {
  selectedOption.value = option;
};

// 关闭模态框
const closeModal = () => {
  // 应用动画
  modalAnimation.value = createSlideOutAnimation(null, {
    direction: 'bottom',
    distance: 100,
    duration: 300
  }).export();
  
  modalOverlayAnimation.value = createSlideOutAnimation(null, {
    direction: 'bottom',
    distance: 0,
    duration: 300
  }).export();
  
  // 延迟隐藏模态框
  setTimeout(() => {
    showModal.value = false;
  }, 300);
};

// 确认操作
const confirmAction = () => {
  if (!selectedOption.value) return;
  
  const { type } = selectedOption.value;
  
  if (type === 'ad') {
    showAd();
  } else if (type === 'paid') {
    processPaidAction();
  }
};

// 显示广告
const showAd = async () => {
  try {
    // 根据当前操作类型获取广告配置
    const adConfig = {
      publish: publishConfig.ad,
      top: topConfig.ad,
      refresh: refreshConfig.ad
    }[currentAction.value];
    
    // 显示广告
    const result = await showAdvertisement({
      adType: currentAction.value,
      duration: adConfig.duration
    });
    
    // 广告展示成功，处理操作完成
    if (result.success) {
      processActionCompletion('ad');
    }
  } catch (error) {
    console.error('广告展示失败:', error);
    uni.showToast({
      title: '广告加载失败',
      icon: 'none'
    });
  }
};

// 处理付费操作
const processPaidAction = async () => {
  try {
    // 根据当前操作类型获取付费配置
    let paymentConfig = {};
    let amount = 0;
    let duration = '';
    
    switch (currentAction.value) {
      case 'publish':
        amount = publishConfig.paid.price;
        break;
      case 'top':
        amount = parseFloat(selectedOption.value.price.replace('¥', ''));
        duration = selectedOption.value.duration;
        break;
      case 'refresh':
        amount = refreshConfig.paid.price;
        break;
    }
    
    // 处理支付
    const result = await processPayment({
      payType: currentAction.value,
      amount,
      duration
    });
    
    // 支付成功，处理操作完成
    if (result.success) {
      processActionCompletion('paid');
    }
  } catch (error) {
    console.error('支付处理失败:', error);
    if (error.reason === 'user_cancelled') {
      // 用户取消支付，不做处理
    } else {
      uni.showToast({
        title: '支付失败',
        icon: 'none'
      });
    }
  }
};

// 处理操作完成
const processActionCompletion = async (paymentType) => {
  // 根据不同操作类型进行处理
  const actionType = currentAction.value;
  
  // 关闭模态框
  closeModal();
  
  // 记录操作日志
  await logOperation({
    action: actionType,
    type: paymentType,
    infoId: props.infoId,
    option: selectedOption.value
  });
  
  // 发送操作完成事件
  emit('action-completed', {
    action: actionType,
    type: paymentType,
    infoId: props.infoId,
    option: selectedOption.value
  });
  
  // 显示操作成功提示
  let successMessage = '';
  switch(actionType) {
    case 'publish':
      successMessage = '发布成功！';
      break;
    case 'top':
      successMessage = `置顶成功！有效期${selectedOption.value.duration || ''}`;
      break;
    case 'refresh':
      successMessage = '刷新成功！';
      break;
  }
  
  uni.showToast({
    title: successMessage,
    icon: 'success'
  });
};
</script>

<style lang="scss" scoped>
/* 主容器 */
.premium-actions-enhanced {
  width: 100%;
  margin: 20rpx 0;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* 面板样式 */
.premium-panel {
  background: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background: linear-gradient(to right, #f8f9fa, #ffffff);
  border-bottom: 1px solid #f0f0f0;
}

.panel-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.panel-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: transform 0.3s ease;
}

.panel-expanded .panel-icon {
  transform: rotate(180deg);
}

.panel-content {
  padding: 24rpx;
}

/* 操作网格 */
.action-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.action-item {
  width: calc(33.33% - 20rpx);
  margin: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  border-radius: 12rpx;
  background: #f8f9fa;
  transition: all 0.2s ease;
}

.action-item:active {
  transform: scale(0.96);
  background: #f0f0f0;
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
}

.action-icon image {
  width: 60%;
  height: 60%;
}

.publish-icon {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.top-icon {
  background: linear-gradient(135deg, #ff9a9e, #fad0c4);
}

.refresh-icon {
  background: linear-gradient(135deg, #a1c4fd, #c2e9fb);
}

.action-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 用户账户信息 */
.user-account-info {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.account-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.account-label {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 4rpx;
}

.account-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 600;
}

.vip-level {
  color: #ff9500;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-container {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 650rpx;
  z-index: 1001;
}

.modal-content {
  background: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
}

.modal-body {
  padding: 30rpx;
}

.modal-description {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

/* 选项列表 */
.option-list {
  margin-bottom: 20rpx;
}

.option-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  border: 2rpx solid transparent;
  transition: all 0.2s ease;
}

.option-selected {
  border-color: #007aff;
  background: rgba(0, 122, 255, 0.05);
}

.option-content {
  display: flex;
  align-items: center;
}

.option-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-right: 20rpx;
}

.option-icon image {
  width: 100%;
  height: 100%;
}

.option-details {
  display: flex;
  flex-direction: column;
}

.option-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 6rpx;
}

.option-subtitle {
  font-size: 24rpx;
  color: #999;
}

.option-price {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.option-original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
  margin-bottom: 4rpx;
}

.option-discount-price {
  color: #ff3b30;
}

/* 底部按钮 */
.modal-footer {
  display: flex;
  padding: 20rpx 30rpx 30rpx;
}

.modal-footer button {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  border-radius: 40rpx;
  margin: 0 10rpx;
}

.btn-cancel {
  background: #f2f2f2;
  color: #666;
}

.btn-confirm {
  background: linear-gradient(135deg, #007aff, #5856d6);
  color: #FFFFFF;
}

.btn-confirm:disabled {
  background: #cccccc;
  color: #999999;
}
</style> 