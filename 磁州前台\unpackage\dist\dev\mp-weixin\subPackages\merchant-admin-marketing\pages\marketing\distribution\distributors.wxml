<view class="distributors-container"><view class="navbar"><view class="navbar-back" bindtap="{{a}}"><view class="back-icon"></view></view><text class="navbar-title">分销员管理</text><view class="navbar-right"><view class="help-icon" bindtap="{{b}}">?</view></view></view><view class="search-filter"><view class="search-bar"><view class="search-input-wrap"><view class="search-icon"></view><input class="search-input" type="text" placeholder="搜索分销员姓名/手机号" confirm-type="search" bindconfirm="{{c}}" value="{{d}}" bindinput="{{e}}"/><view wx:if="{{f}}" class="clear-icon" bindtap="{{g}}"></view></view><view class="search-btn" bindtap="{{h}}">搜索</view></view><view class="filter-options"><view class="filter-item"><picker mode="selector" range="{{j}}" range-key="name" value="{{k}}" bindchange="{{l}}"><view class="picker-value"><text>{{i}}</text><view class="arrow-icon"></view></view></picker></view><view class="filter-item"><picker mode="selector" range="{{n}}" range-key="name" value="{{o}}" bindchange="{{p}}"><view class="picker-value"><text>{{m}}</text><view class="arrow-icon"></view></view></picker></view><view class="filter-item"><picker mode="selector" range="{{r}}" range-key="name" value="{{s}}" bindchange="{{t}}"><view class="picker-value"><text>{{q}}</text><view class="arrow-icon"></view></view></picker></view></view></view><view class="content-area"><view wx:if="{{v}}" class="distributors-list"><view wx:for="{{w}}" wx:for-item="item" wx:key="v" class="distributor-card"><view class="distributor-header"><view class="distributor-info"><image class="avatar" src="{{item.a}}" mode="aspectFill"></image><view class="info-content"><view class="name-wrap"><text class="name">{{item.b}}</text><text class="level-tag" style="{{'background-color:' + item.d}}">{{item.c}}</text></view><text class="phone">{{item.e}}</text></view></view><view class="{{['status-tag', item.g]}}">{{item.f}}</view></view><view class="distributor-stats"><view class="stat-item"><text class="stat-value">{{item.h}}</text><text class="stat-label">推广订单</text></view><view class="stat-item"><text class="stat-value">¥{{item.i}}</text><text class="stat-label">累计佣金</text></view><view class="stat-item"><text class="stat-value">{{item.j}}</text><text class="stat-label">团队人数</text></view></view><view class="distributor-footer"><view class="time">注册时间：{{item.k}}</view><view class="actions"><view class="action-btn detail" bindtap="{{item.l}}">详情</view><block wx:if="{{item.m}}"><view class="action-btn approve" bindtap="{{item.n}}">通过</view><view class="action-btn reject" bindtap="{{item.o}}">拒绝</view></block><block wx:elif="{{item.p}}"><view class="action-btn disable" bindtap="{{item.q}}">禁用</view><view class="action-btn set-level" bindtap="{{item.r}}">设置等级</view></block><block wx:elif="{{item.s}}"><view class="action-btn enable" bindtap="{{item.t}}">启用</view></block></view></view></view></view><view wx:elif="{{x}}" class="empty-state"><image class="empty-image" src="{{y}}" mode="aspectFit"></image><text class="empty-text">暂无分销员数据</text></view><view wx:if="{{z}}" class="loading-state"><view class="loading-icon"></view><text class="loading-text">加载中...</text></view><view wx:if="{{A}}" class="pagination"><view class="page-info"><text>共 {{B}} 条记录，当前 {{C}}/{{D}} 页</text></view><view class="page-actions"><view class="{{['page-btn', 'prev', E && 'disabled']}}" bindtap="{{F}}">上一页</view><view class="{{['page-btn', 'next', G && 'disabled']}}" bindtap="{{H}}">下一页</view></view></view></view><view wx:if="{{I}}" class="level-modal"><view class="modal-mask" bindtap="{{J}}"></view><view class="modal-content"><view class="modal-header"><text class="modal-title">设置分销员等级</text><view class="close-icon" bindtap="{{K}}"></view></view><view class="modal-distributor"><image class="avatar" src="{{L}}" mode="aspectFill"></image><text class="name">{{M}}</text></view><view class="level-list"><view wx:for="{{N}}" wx:for-item="level" wx:key="c" class="{{['level-item-select', level.d && 'active']}}" bindtap="{{level.e}}"><view class="level-radio"><view wx:if="{{level.a}}" class="radio-inner"></view></view><text class="level-name">{{level.b}}</text></view></view><view class="modal-footer"><button class="cancel-btn" bindtap="{{O}}">取消</button><button class="submit-btn" bindtap="{{P}}">确定</button></view></view></view></view>