/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.success-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 40rpx;
}
.success-content {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 成功状态卡片 - 新设计 */
.main-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(10, 132, 255, 0.15);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.main-title {
  color: #1976d2;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  text-align: center;
}
.main-desc {
  color: #888;
  font-size: 24rpx;
  margin-bottom: 36rpx;
  text-align: center;
}
.main-btns {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 10rpx;
}
.main-btn {
  flex: 1;
  margin: 0 10rpx;
  background: #eaf3ff;
  color: #1976d2;
  border-radius: 32rpx;
  font-weight: 500;
  text-align: center;
  padding: 25rpx 0;
  height: 180rpx;
  transition: background 0.2s, color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-text-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 10rpx 0;
}
.btn-text {
  font-size: 28rpx;
  line-height: 1.8;
  display: block;
  font-weight: bold;
  letter-spacing: 2rpx;
}
.main-btn-active {
  background: linear-gradient(90deg, #1976d2 0%, #4a90e2 100%);
  color: #fff;
}
.main-btn-active .btn-text {
  color: #fff;
}

/* 置顶信息组件 */
.premium-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  margin-bottom: 30rpx;
}
.premium-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.premium-title-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.premium-title-icon image {
  width: 36rpx;
  height: 36rpx;
}
.premium-title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

/* 自定义置顶选项 */
.custom-premium-actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.premium-option-item {
  background-color: #f8f9fe;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.option-left {
  display: flex;
  align-items: center;
}
.option-circle {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}
.blue-circle {
  background-color: #4a90e2;
}
.orange-circle {
  background-color: #f5a623;
}
.option-icon {
  width: 40rpx;
  height: 40rpx;
  filter: brightness(0) invert(1);
}
.option-info {
  display: flex;
  flex-direction: column;
}
.option-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 6rpx;
}
.option-desc {
  font-size: 24rpx;
  color: #999999;
}
.option-right {
  display: flex;
  align-items: center;
}
.option-button {
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  font-weight: bold;
}
.free-button {
  background-color: #4a90e2;
  color: #ffffff;
}
.paid-button {
  background-color: #f5a623;
  color: #ffffff;
}

/* 信息卡片 */
.info-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}
.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.info-tag {
  margin-left: 15rpx;
  font-size: 22rpx;
  color: #ffffff;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
}
.people-to-car {
  background-color: #0A84FF;
}
.car-to-people {
  background-color: #FF453A;
}
.goods-to-car {
  background-color: #30D158;
}
.car-to-goods {
  background-color: #FF9F0A;
}

/* 路线信息 */
.route-section {
  margin-bottom: 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  padding-bottom: 20rpx;
}
.route-points {
  margin-bottom: 20rpx;
}
.route-point {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.point-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 15rpx;
  flex-shrink: 0;
}
.start {
  background-color: #0A84FF;
}
.end {
  background-color: #FF453A;
}
.point-text {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
}
.route-divider {
  padding-left: 7rpx;
  margin: 10rpx 0;
  display: flex;
}
.divider-line {
  width: 2rpx;
  height: 30rpx;
  background-color: #dddddd;
}
.divider-arrow {
  margin-left: -7rpx;
  margin-top: 30rpx;
}
.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

/* 途径点 */
.via-points {
  margin-top: 20rpx;
  background-color: rgba(10, 132, 255, 0.05);
  border-radius: 12rpx;
  padding: 15rpx;
}
.via-title {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.via-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 10rpx;
  opacity: 0.7;
}
.via-text {
  font-size: 26rpx;
  color: #666666;
  font-weight: 500;
}
.via-list {
  display: flex;
  flex-wrap: wrap;
}
.via-item {
  font-size: 26rpx;
  color: #0A84FF;
  background-color: rgba(10, 132, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
  margin-bottom: 10rpx;
}

/* 详细信息 */
.details-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.detail-row, .contact-row {
  display: flex;
  flex-wrap: wrap;
  gap: 30rpx;
}
.detail-item, .contact-item {
  display: flex;
  align-items: center;
}
.detail-icon, .contact-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 10rpx;
  opacity: 0.6;
}
.detail-text, .contact-text {
  font-size: 28rpx;
  color: #666666;
}
.premium-icon {
  opacity: 1;
}
.premium-text {
  color: #FF9F0A;
  font-weight: 500;
}
.remark-row {
  background-color: rgba(0, 0, 0, 0.02);
  padding: 15rpx;
  border-radius: 12rpx;
}
.remark-label {
  font-size: 26rpx;
  color: #999999;
  margin-right: 10rpx;
}
.remark-content {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
}

/* 温馨提示 */
.tips-card {
  background-color: rgba(10, 132, 255, 0.05);
  border-radius: 24rpx;
  padding: 20rpx 30rpx;
}
.tips-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}
.tips-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 10rpx;
  opacity: 0.7;
}
.tips-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}
.tips-content {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}
.tips-text {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
}

/* 悬浮分享按钮 */
.float-btn {
  position: fixed;
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  background-color: #1677FF;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  padding: 0;
  margin: 0;
  border: none;
}
.share-btn {
  bottom: 40rpx;
  right: 40rpx;
}
.kefu-btn {
  bottom: 40rpx;
  left: 40rpx;
}
.float-icon {
  width: 48rpx;
  height: 48rpx;
  filter: brightness(0) invert(1);
}

/* 分享提示弹窗 */
.share-tips-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.share-tips-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 40rpx;
  max-width: 80%;
  width: 100%;
  text-align: center;
}
.share-tips-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 20rpx;
}
.crown-icon {
  width: 80rpx;
  height: 80rpx;
}
.share-tips-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #FF6B00;
  margin-bottom: 30rpx;
}
.share-tips-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  text-align: left;
}
.tips-item-number {
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #ffffff;
  background-color: #1677FF;
  border-radius: 50%;
  margin-right: 20rpx;
  flex-shrink: 0;
}
.tips-item-content {
  flex: 1;
}
.tips-text {
  font-size: 28rpx;
  color: #333333;
  display: block;
  line-height: 1.5;
}
.share-tips-btns {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
  border-top: 1rpx solid #EEEEEE;
}
.share-btn-item {
  flex: 1;
  padding: 20rpx 0;
  font-size: 30rpx;
  font-weight: bold;
}
.close-btn {
  color: #999999;
  border-right: 1rpx solid #EEEEEE;
}
.share-btn-blue {
  background-color: #1677FF;
  color: #ffffff;
}
.share-btn-green {
  background-color: #07C160;
  color: #ffffff;
}

/* 脉动动画 */
.pulsing-btn {
  animation: pulse 1.5s infinite;
  position: relative;
}
@keyframes pulse {
0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(22, 119, 255, 0.7);
}
70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(22, 119, 255, 0);
}
100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(22, 119, 255, 0);
}
}
/* 客服二维码弹窗 */
.qrcode-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.qrcode-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 30rpx;
  width: 80%;
  max-width: 600rpx;
}
.qrcode-header {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 20rpx;
}
.qrcode-close {
  width: 40rpx;
  height: 40rpx;
  padding: 10rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
}
.close-icon {
  width: 24rpx;
  height: 24rpx;
}
.qrcode-content {
  text-align: center;
}
.qrcode-title-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}
.qrcode-title-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}
.qrcode-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}
.qrcode-desc {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 30rpx;
}
.qrcode-image-container {
  position: relative;
  margin: 0 auto 30rpx;
  width: 300rpx;
  height: 300rpx;
}
.qrcode-image {
  width: 100%;
  height: 100%;
}
.qrcode-scan-hint {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #ffffff;
  font-size: 22rpx;
  border-radius: 0 0 8rpx 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.scan-icon-container {
  margin-right: 8rpx;
  color: #ffffff;
}
.scan-icon {
  width: 24rpx;
  height: 24rpx;
}
.qrcode-info-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}
.qrcode-info-item {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  color: #666666;
  font-size: 24rpx;
}
.info-icon {
  margin-right: 8rpx;
  color: #1677FF;
}
.qrcode-actions {
  display: flex;
  justify-content: space-around;
  border-top: 1rpx solid #EEEEEE;
  padding-top: 20rpx;
}
.qrcode-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #1677FF;
}
.copy-btn {
  border-right: 1rpx solid #EEEEEE;
}
.btn-icon {
  margin-right: 8rpx;
  color: #1677FF;
}