# 推广工具全平台应用方案

## 目录

- [1. 概述](#1-概述)
- [2. 推广工具集](#2-推广工具集)
- [3. 架构设计](#3-架构设计)
- [4. 各业务模块集成方案](#4-各业务模块集成方案)
  - [4.1 拼车系统集成](#41-拼车系统集成)
  - [4.2 二手交易系统集成](#42-二手交易系统集成)
  - [4.3 本地服务系统集成](#43-本地服务系统集成)
  - [4.4 房屋租售系统集成](#44-房屋租售系统集成)
  - [4.5 社区信息系统集成](#45-社区信息系统集成)
  - [4.6 展示型页面集成](#46-展示型页面集成)
  - [4.7 平台级集成与内容管理](#47-平台级集成与内容管理)
- [5. 技术实现详解](#5-技术实现详解)
- [6. 推广效果追踪](#6-推广效果追踪)
- [7. 实施路径](#7-实施路径)

## 1. 概述

### 1.1 背景与目标

本方案旨在将原本仅在分销系统中使用的推广工具（如推广海报、推广二维码等）扩展到小程序的各个业务模块中，使其成为平台的基础能力，提升内容传播效率和用户增长。

### 1.2 推广工具分类

- **收益型推广工具**：与分销系统、合伙人系统等有明确分成机制的场景集成
- **普通推广工具**：不涉及分成，主要用于提升内容传播和曝光

### 1.3 应用原则

- **统一入口**：保持推广工具的使用体验一致性
- **场景适配**：根据不同业务场景提供定制化推广内容
- **简单易用**：降低用户使用门槛，提高推广频率
- **效果可追踪**：提供基本的推广效果数据反馈

## 2. 推广工具集

### 2.1 核心推广工具

| 工具名称 | 功能描述 | 适用场景 |
|---------|---------|---------|
| 推广海报 | 生成包含内容和推广码的精美海报 | 所有内容推广 |
| 推广二维码 | 生成可追踪的带参数二维码 | 线下引流、实体场景 |
| 商品推广卡片 | 生成突出商品特点的卡片 | 商品销售、二手交易 |
| 推广文案 | 提供多种风格的推广文案模板 | 文字类平台分享 |
| 素材库 | 统一管理各类推广素材 | 多渠道持续推广 |
| 推广活动 | 创建和管理推广活动 | 活动营销、限时推广 |

### 2.2 推广工具特性

- **内容智能适配**：根据推广对象自动提取关键信息
- **多模板选择**：提供多种风格模板适应不同场景
- **自定义编辑**：支持文字、颜色、布局等要素调整
- **多渠道分享**：支持微信好友、朋友圈、保存图片等多种分享方式
- **追踪支持**：包含推广来源追踪参数

## 3. 架构设计

### 3.1 整体架构

```
推广工具系统
├── 前端展示层
│   ├── 推广工具组件（可复用）
│   └── 推广工具页面（完整功能）
├── 业务逻辑层
│   ├── 推广服务
│   ├── 内容提取服务
│   └── 分享服务
└── 数据层
    ├── 推广数据存储
    └── 效果追踪模块
```

### 3.2 关键服务

- **PromotionService**：推广工具核心服务，提供推广内容生成能力
- **ContentExtractorService**：从不同业务对象提取推广所需信息
- **SharingService**：处理不同平台的分享逻辑
- **TrackingService**：处理推广效果追踪

### 3.3 组件设计

- **PromotionToolButton**：统一的推广工具入口按钮
- **ShareActionSheet**：统一的分享动作面板
- **PromotionPreview**：推广内容预览组件

## 4. 各业务模块集成方案

### 4.1 拼车系统集成

#### 4.1.1 集成点

- **拼车详情页**：发布者可见的推广工具入口
- **拼车发布成功页**：推荐使用推广工具增加曝光
- **我的拼车列表**：批量推广功能

#### 4.1.2 推广内容

拼车推广应突出以下信息：
- 出发地和目的地
- 出发时间
- 剩余座位数
- 价格信息
- 联系方式（可选）

#### 4.1.3 实现示例

```html
<!-- 拼车详情页推广按钮 -->
<view class="action-bar">
  <button class="main-btn" @click="joinCarpool">我要上车</button>
  <promotion-tool-button 
    v-if="isPublisher" 
    type="carpool" 
    :data="carpoolInfo"
  />
</view>
```

### 4.2 二手交易系统集成

#### 4.2.1 集成点

- **商品详情页**：卖家可见的推广工具入口
- **发布成功页**：推荐分享到朋友圈
- **我的商品管理**：支持批量生成推广内容

#### 4.2.2 推广内容

二手商品推广应突出：
- 商品名称与图片
- 价格（原价与现价对比）
- 新旧程度
- 交易方式
- 卖家信息（可选）

#### 4.2.3 实现示例

```html
<!-- 商品详情页推广区域 -->
<view class="seller-actions" v-if="isSeller">
  <view class="action-title">卖家操作</view>
  <view class="action-buttons">
    <button class="action-btn" @click="editItem">编辑</button>
    <button class="action-btn" @click="removeItem">下架</button>
    <button class="promotion-btn" @click="showPromotionTools">推广商品</button>
  </view>
</view>
```

### 4.3 本地服务系统集成

#### 4.3.1 集成点

- **服务详情页**：服务提供者的推广入口
- **服务商家中心**：推广工具专区
- **活动页面**：活动推广功能

#### 4.3.2 推广内容

服务推广应突出：
- 服务类别与名称
- 服务范围
- 价格信息
- 优惠活动
- 商家口碑/评分

#### 4.3.3 实现示例

```html
<!-- 服务商家中心的推广工具入口 -->
<view class="merchant-tools">
  <view class="tools-section">
    <view class="section-title">推广工具</view>
    <view class="tools-grid">
      <view class="tool-item" @click="navigateToTool('promotion-tool')">
        <image src="/static/images/poster-icon.png" />
        <text>推广海报</text>
      </view>
      <!-- 更多工具入口 -->
    </view>
  </view>
</view>
```

### 4.4 房屋租售系统集成

#### 4.4.1 集成点

- **房源详情页**：发布者可见的推广工具
- **发布成功页**：推荐使用推广工具
- **我的房源管理**：批量推广选项

#### 4.4.2 推广内容

房屋推广应突出：
- 房屋位置与类型
- 面积与户型
- 价格信息（租金/售价）
- 特色亮点
- 室内实景图

#### 4.4.3 实现示例

```html
<!-- 房源详情页的推广入口 -->
<view class="owner-actions" v-if="isOwner">
  <button class="action-button primary" @click="editHouse">编辑房源</button>
  <button class="action-button" @click="promoteHouse">推广房源</button>
</view>
```

### 4.5 社区信息系统集成

#### 4.5.1 集成点

- **信息详情页**：发布者推广选项
- **发布成功页**：推荐分享到社交媒体
- **我的发布管理**：推广统计与再次推广

#### 4.5.2 推广内容

社区信息推广应突出：
- 信息标题与类别
- 关键内容概要
- 有效时间
- 联系方式
- 地理位置（可选）

#### 4.5.3 实现示例

```html
<!-- 信息发布成功页的推广提示 -->
<view class="publish-success">
  <view class="success-icon"></view>
  <view class="success-title">发布成功!</view>
  <view class="success-tips">立即分享，获得更多曝光</view>
  
  <view class="promotion-options">
    <button class="promo-btn" @click="generatePoster">生成海报</button>
    <button class="promo-btn" @click="shareToFriends">分享好友</button>
    <button class="promo-btn" @click="shareToTimeline">分享朋友圈</button>
  </view>
</view>
```

### 4.6 展示型页面集成

#### 4.6.1 适用页面类型

- **同城信息页面**：展示本地热点信息和活动
- **活动页面**：各类线上线下活动展示
- **商品详情页**：产品展示与销售页面
- **资讯文章页面**：新闻、攻略、行业资讯等内容页
- **商家店铺页面**：商家信息与产品服务展示

#### 4.6.2 集成方式

展示型页面的推广工具集成采用轻量级、非侵入式的方式：

1. **浮动按钮式**：在页面右下角或底部添加推广工具浮动按钮
2. **分享菜单集成**：在原生分享菜单中添加推广工具选项
3. **内容底部式**：在内容展示完毕后的操作区域添加推广入口

#### 4.6.3 权限控制

- **内容创建者/所有者**：完整推广工具权限
- **平台运营人员**：可推广指定内容
- **普通用户**：基础分享能力，无收益追踪

#### 4.6.4 实现示例

```html
<!-- 通用展示页面浮动推广按钮 -->
<view class="float-promotion-btn" v-if="hasPromotionPermission">
  <button class="btn-circle" @click="openPromotionTools">
    <image class="btn-icon" src="/static/images/share-icon.png"></image>
  </button>
</view>

<!-- 商品详情页推广区域 -->
<view class="product-action-area">
  <view class="price-area">
    <view class="current-price">¥{{product.price}}</view>
    <view class="original-price" v-if="product.originalPrice">¥{{product.originalPrice}}</view>
  </view>
  
  <view class="action-buttons">
    <button class="action-btn contact">联系卖家</button>
    <button class="action-btn buy">立即购买</button>
    <promotion-tool-button 
      type="product" 
      :data="productData"
      text="分享赚佣金"
      v-if="canEarnCommission"
    />
    <promotion-tool-button 
      type="product" 
      :data="productData"
      text="分享"
      v-else
    />
  </view>
</view>

<!-- 资讯页面底部推广区 -->
<view class="article-footer">
  <view class="article-tags">
    <text class="tag" v-for="(tag, index) in article.tags" :key="index">{{tag}}</text>
  </view>
  
  <view class="article-actions">
    <view class="action-item" @click="likeArticle">
      <image class="action-icon" :src="isLiked ? '/static/images/like-active.png' : '/static/images/like.png'"></image>
      <text>{{article.likes}}</text>
    </view>
    <view class="action-item" @click="commentArticle">
      <image class="action-icon" src="/static/images/comment.png"></image>
      <text>{{article.comments}}</text>
    </view>
    <view class="action-item" @click="showPromotionTools">
      <image class="action-icon" src="/static/images/share.png"></image>
      <text>分享</text>
    </view>
  </view>
</view>
```

#### 4.6.5 技术实现

展示型页面集成推广工具需要添加以下通用能力：

```javascript
// mixins/promotionMixin.js

/**
 * 推广能力混入
 * 为展示型页面提供通用的推广能力
 */
export default {
  data() {
    return {
      // 是否有权限使用推广工具
      hasPromotionPermission: false,
      // 是否可以获取佣金
      canEarnCommission: false,
      // 推广数据对象
      promotionData: {}
    }
  },
  
  created() {
    // 检查用户权限
    this.checkPromotionPermission();
    // 生成推广数据
    this.generatePromotionData();
  },
  
  methods: {
    /**
     * 检查用户是否有使用推广工具的权限
     */
    async checkPromotionPermission() {
      try {
        const { data } = await this.$api.user.getPermissions();
        // 检查是否是内容所有者
        if (this.checkIsContentOwner()) {
          this.hasPromotionPermission = true;
        } else {
          // 检查是否有全局推广权限
          this.hasPromotionPermission = data.permissions.includes('USE_PROMOTION_TOOLS');
        }
        
        // 检查是否有佣金权限
        this.canEarnCommission = data.permissions.includes('EARN_COMMISSION') && 
          this.isEligibleForCommission();
      } catch (e) {
        console.error('获取推广权限失败', e);
        this.hasPromotionPermission = false;
        this.canEarnCommission = false;
      }
    },
    
    /**
     * 检查是否是内容所有者
     */
    checkIsContentOwner() {
      const { userId } = this.$store.state.user;
      // 根据不同页面类型获取内容所有者ID进行比对
      const contentOwnerId = this.getContentOwnerId();
      return userId === contentOwnerId;
    },
    
    /**
     * 获取内容所有者ID
     * 子类应该重写此方法以返回特定内容的所有者ID
     */
    getContentOwnerId() {
      // 默认实现，子类应重写
      return '';
    },
    
    /**
     * 检查该内容是否可以获得佣金
     */
    isEligibleForCommission() {
      // 默认所有内容不可获得佣金，需子类重写
      return false;
    },
    
    /**
     * 生成推广数据对象
     * 子类应该重写此方法以生成特定内容的推广数据
     */
    generatePromotionData() {
      // 默认空实现，子类应重写
      this.promotionData = {};
    },
    
    /**
     * 打开推广工具
     */
    openPromotionTools() {
      if (!this.hasPromotionPermission) {
        uni.showToast({
          title: '您没有权限使用推广工具',
          icon: 'none'
        });
        return;
      }
      
      // 导入推广服务
      const promotionService = require('@/utils/promotionService').default;
      
      // 确定推广类型
      const pageTypeMap = {
        'goods': 'product',
        'article': 'content',
        'service': 'service',
        'shop': 'merchant',
        'activity': 'activity'
      };
      
      // 获取当前页面类型
      const currentPageType = this.pageType || this.$route.meta.pageType || 'default';
      const promotionType = pageTypeMap[currentPageType] || currentPageType;
      
      // 打开推广工具
      promotionService.showPromotionTools(promotionType, this.promotionData);
    }
  }
};
```

### 4.7 平台级集成与内容管理

#### 4.7.1 平台级配置

为确保推广工具在全平台的一致性和可配置性，需建立平台级的推广工具配置系统：

- **全局开关**：可按业务模块启用/禁用推广工具
- **权限矩阵**：定义不同角色的推广能力权限
- **佣金规则**：配置不同内容类型的分佣策略
- **推广模板**：统一管理各类推广内容模板
- **参数配置**：管理全局推广参数和追踪规则

#### 4.7.2 内容管理系统集成

在内容管理系统中集成推广工具，使平台运营人员可以：

1. **批量生成推广**：为精选内容批量生成推广素材
2. **推广活动策划**：创建特定主题的推广活动
3. **数据监控**：监控各类内容推广效果
4. **高级设置**：针对重点内容设置特殊推广策略

#### 4.7.3 平台级推广服务

```javascript
// services/platformPromotionService.js

import request from '@/utils/request';
import store from '@/store';

/**
 * 平台级推广服务
 * 提供全平台通用的推广能力和配置
 */
class PlatformPromotionService {
  /**
   * 获取全局推广配置
   * @returns {Promise} 全局配置信息
   */
  async getGlobalConfig() {
    try {
      const { data } = await request.get('/api/promotion/config');
      return data;
    } catch (error) {
      console.error('获取推广配置失败', error);
      return {};
    }
  }
  
  /**
   * 检查特定业务模块推广工具是否启用
   * @param {string} moduleType - 业务模块类型
   * @returns {Promise<boolean>} 是否启用
   */
  async isModuleEnabled(moduleType) {
    const config = await this.getGlobalConfig();
    return config.enabledModules?.includes(moduleType) ?? false;
  }
  
  /**
   * 获取当前用户的推广权限
   * @returns {Promise<object>} 权限对象
   */
  async getUserPromotionPermissions() {
    try {
      // 先尝试从缓存获取
      if (store.state.promotion?.permissions) {
        return store.state.promotion.permissions;
      }
      
      // 从服务器获取
      const { data } = await request.get('/api/promotion/user/permissions');
      
      // 缓存到状态管理
      store.commit('promotion/SET_PERMISSIONS', data);
      
      return data;
    } catch (error) {
      console.error('获取用户推广权限失败', error);
      return {
        canPromote: false,
        canEarnCommission: false,
        allowedModules: []
      };
    }
  }
  
  /**
   * 获取内容的推广配置
   * @param {string} contentType - 内容类型
   * @param {string} contentId - 内容ID
   * @returns {Promise<object>} 推广配置
   */
  async getContentPromotionConfig(contentType, contentId) {
    try {
      const { data } = await request.get('/api/promotion/content/config', {
        params: { type: contentType, id: contentId }
      });
      return data;
    } catch (error) {
      console.error('获取内容推广配置失败', error);
      return {
        enabled: false,
        templates: [],
        commissionRate: 0
      };
    }
  }
  
  /**
   * 记录推广内容生成事件
   * @param {object} params - 推广参数
   */
  async logPromotionGeneration(params) {
    try {
      await request.post('/api/promotion/logs/generate', params);
    } catch (error) {
      console.error('记录推广生成失败', error);
    }
  }
  
  /**
   * 记录推广内容分享事件
   * @param {object} params - 分享参数
   */
  async logPromotionSharing(params) {
    try {
      await request.post('/api/promotion/logs/share', params);
    } catch (error) {
      console.error('记录推广分享失败', error);
    }
  }
}

export default new PlatformPromotionService();
```

#### 4.7.4 全局推广能力注入

为便于各页面统一接入推广能力，可以通过全局混入方式注入基础推广能力：

```javascript
// main.js 中添加全局混入

import Vue from 'vue';
import App from './App';
import basePromotionMixin from '@/mixins/basePromotionMixin';

// 注册全局推广混入
Vue.mixin(basePromotionMixin);

// 可选：为特定页面类型注册更具体的推广混入
import productPromotionMixin from '@/mixins/productPromotionMixin';
import articlePromotionMixin from '@/mixins/articlePromotionMixin';

// 根据页面类型动态注入对应的推广能力
Vue.prototype.$injectPromotionMixin = function(pageType) {
  const mixins = {
    'product': productPromotionMixin,
    'article': articlePromotionMixin,
    // 其他页面类型...
  };
  
  return mixins[pageType] || {};
};

// 继续Vue初始化...
```

#### 4.7.5 管理后台配置界面

在管理后台中，需要提供以下配置界面：

- **推广工具总开关**：控制整个平台推广工具的启用状态
- **模块配置**：针对每个业务模块的推广工具配置
- **权限管理**：设置不同角色的推广权限
- **佣金设置**：配置不同内容类型的佣金比例
- **模板管理**：管理不同类型内容的推广模板
- **数据看板**：展示全平台推广效果数据

## 5. 技术实现详解

### 5.1 推广工具服务

```javascript
// utils/promotionService.js

/**
 * 推广工具服务
 * 提供各类推广内容生成和分享能力
 */
class PromotionService {
  /**
   * 生成推广参数
   * @param {string} type - 推广类型
   * @param {object} data - 推广对象数据
   * @returns {object} 规范化的推广参数
   */
  generatePromotionParams(type, data) {
    // 基础参数
    const baseParams = {
      type,
      id: data.id,
      title: data.title || '',
      image: data.image || '',
    };
    
    // 根据不同类型处理特定参数
    switch(type) {
      case 'carpool':
        return {
          ...baseParams,
          departure: data.departure,
          destination: data.destination,
          departureTime: data.departureTime,
          price: data.price,
          seats: data.seats
        };
        
      case 'secondhand':
        return {
          ...baseParams,
          price: data.price,
          originalPrice: data.originalPrice,
          condition: data.condition,
          category: data.category
        };
        
      case 'house':
        return {
          ...baseParams,
          location: data.location,
          area: data.area,
          roomType: data.roomType,
          price: data.price,
          rentType: data.rentType
        };
        
      case 'service':
        return {
          ...baseParams,
          category: data.category,
          price: data.price,
          merchantName: data.merchantName,
          rating: data.rating
        };
        
      default:
        return baseParams;
    }
  }
  
  /**
   * 导航到推广工具页面
   * @param {string} toolName - 工具页面名称
   * @param {object} params - 推广参数
   */
  navigateToPromotionTool(toolName, params) {
    const baseUrl = '/subPackages/merchant-admin-marketing/pages/marketing/distribution/';
    
    // 将参数对象转换为URL参数字符串
    const paramStr = Object.entries(params)
      .map(([key, value]) => {
        if (value === undefined || value === null) return '';
        return `${key}=${encodeURIComponent(value)}`;
      })
      .filter(item => item !== '')
      .join('&');
    
    // 导航到对应工具页面
    uni.navigateTo({
      url: `${baseUrl}${toolName}?${paramStr}`
    });
  }
  
  /**
   * 显示推广工具选择器
   * @param {string} type - 推广类型
   * @param {object} data - 推广对象数据
   */
  showPromotionTools(type, data) {
    const toolOptions = [
      { name: '推广海报', value: 'promotion-tool' },
      { name: '推广二维码', value: 'qrcode' },
      { name: '推广卡片', value: 'product-cards' },
      { name: '推广文案', value: 'promotion-text' }
    ];
    
    // 显示推广工具选择菜单
    uni.showActionSheet({
      itemList: toolOptions.map(item => item.name),
      success: (res) => {
        const selectedTool = toolOptions[res.tapIndex].value;
        const params = this.generatePromotionParams(type, data);
        this.navigateToPromotionTool(selectedTool, params);
      }
    });
  }
}

export default new PromotionService();
```

### 5.2 推广工具按钮组件

```html
<!-- components/PromotionToolButton.vue -->
<template>
  <button class="promotion-tool-button" @click="onPromotionClick">
    <image class="button-icon" :src="buttonIcon" mode="aspectFit"></image>
    <text class="button-text">{{ buttonText }}</text>
  </button>
</template>

<script>
import promotionService from '@/utils/promotionService';

export default {
  name: 'PromotionToolButton',
  props: {
    // 推广类型
    type: {
      type: String,
      required: true,
      validator: value => {
        return ['carpool', 'secondhand', 'house', 'service', 'community', 'default'].includes(value);
      }
    },
    // 推广数据对象
    data: {
      type: Object,
      required: true
    },
    // 按钮文本，可自定义
    text: {
      type: String,
      default: '推广'
    },
    // 按钮图标，可自定义
    icon: {
      type: String,
      default: '/static/images/promotion-icon.png'
    }
  },
  computed: {
    buttonText() {
      return this.text;
    },
    buttonIcon() {
      return this.icon;
    }
  },
  methods: {
    onPromotionClick() {
      this.$emit('click');
      promotionService.showPromotionTools(this.type, this.data);
    }
  }
}
</script>

<style lang="scss" scoped>
.promotion-tool-button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 76rpx;
  padding: 0 24rpx;
  background: rgba(107, 15, 190, 0.05);
  border: 1px solid rgba(107, 15, 190, 0.2);
  border-radius: 38rpx;
  
  .button-icon {
    width: 32rpx;
    height: 32rpx;
    margin-right: 8rpx;
  }
  
  .button-text {
    font-size: 28rpx;
    color: #6B0FBE;
  }
}
</style>
```

### 5.3 推广工具页面适配

要使现有的推广工具页面支持多业务场景，需要进行以下调整：

```javascript
// 在推广海报页面(promotion-tool.vue)中添加

// 生命周期钩子
onLoad(options) {
  // 获取推广类型和基础信息
  this.promotionType = options.type || 'default';
  this.itemId = options.id;
  this.itemTitle = options.title ? decodeURIComponent(options.title) : '';
  this.itemImage = options.image ? decodeURIComponent(options.image) : '';
  
  // 存储特定类型的额外参数
  this.extraParams = {};
  Object.keys(options).forEach(key => {
    if (!['type', 'id', 'title', 'image'].includes(key)) {
      this.extraParams[key] = decodeURIComponent(options[key] || '');
    }
  });
  
  // 根据不同类型加载适合的模板
  this.loadTemplatesByType();
  
  // 根据参数生成推广内容
  this.generatePromotionContent();
},

// 根据类型加载不同模板
loadTemplatesByType() {
  // 基础模板
  const baseTemplates = [/* 通用模板 */];
  
  // 特定类型的模板
  const typeTemplates = {
    carpool: [/* 拼车专用模板 */],
    secondhand: [/* 二手交易专用模板 */],
    house: [/* 房屋租售专用模板 */],
    service: [/* 服务推广专用模板 */],
    community: [/* 社区信息专用模板 */]
  };
  
  // 合并模板
  this.posterTemplates = [
    ...(typeTemplates[this.promotionType] || []),
    ...baseTemplates
  ];
},

// 生成推广内容
generatePromotionContent() {
  // 基础内容
  const content = {
    title: this.itemTitle,
    image: this.itemImage,
    qrcode: this.generateQrcode()
  };
  
  // 根据不同类型添加特定内容
  switch(this.promotionType) {
    case 'carpool':
      content.subtitle = `${this.extraParams.departure} → ${this.extraParams.destination}`;
      content.details = [
        `出发时间：${this.extraParams.departureTime}`,
        `车费：¥${this.extraParams.price}`,
        `剩余座位：${this.extraParams.seats}个`
      ];
      break;
      
    case 'secondhand':
      content.subtitle = `${this.extraParams.category || '二手商品'}`;
      content.details = [
        `价格：¥${this.extraParams.price}`,
        `成色：${this.extraParams.condition || '未知'}`
      ];
      break;
    
    // 其他类型处理...
  }
  
  this.promotionContent = content;
},

// 生成带参数的二维码
generateQrcode() {
  const baseUrl = 'https://example.com/pages/detail';
  const params = `type=${this.promotionType}&id=${this.itemId}&source=promotion`;
  return `${baseUrl}?${params}`;
}
```

### 5.4 参数传递与解析

不同业务模块需要向推广工具传递不同参数，下面是各类型参数示例：

#### 拼车推广参数

```javascript
{
  type: 'carpool',
  id: '12345',
  title: '磁县到邯郸拼车',
  image: '/static/images/carpool-default.jpg',
  departure: '磁县城区',
  destination: '邯郸火车站',
  departureTime: '2023-07-20 08:00',
  seats: 3,
  price: 15
}
```

#### 二手商品推广参数

```javascript
{
  type: 'secondhand',
  id: '67890',
  title: '9成新iPhone 13',
  image: 'https://example.com/iphone.jpg',
  price: 4999,
  originalPrice: 6799,
  condition: '9成新',
  category: '手机数码'
}
```

#### 房屋推广参数

```javascript
{
  type: 'house',
  id: '24680',
  title: '城区精装两居室出租',
  image: 'https://example.com/house.jpg',
  location: '磁县城区中心地段',
  area: 89,
  roomType: '2室1厅1卫',
  price: 1200,
  rentType: '月租'
}
```

## 6. 推广效果追踪

### 6.1 追踪机制设计

- **参数标记**：在所有推广链接和二维码中嵌入追踪参数
- **打开统计**：记录推广内容的打开次数和来源
- **转化跟踪**：记录由推广带来的注册、下单等转化行为
- **分享统计**：记录推广内容的分享次数和渠道

### 6.2 追踪参数结构

```
https://example.com/pages/detail?
type=[业务类型]&
id=[内容ID]&
source=promotion&
channel=[分享渠道]&
promoter=[推广者ID]
```

### 6.3 数据收集流程

1. 用户生成推广内容时记录基本信息
2. 推广内容被打开时记录打开事件
3. 用户通过推广内容进行后续操作时记录转化事件
4. 定期汇总分析推广效果数据

### 6.4 效果反馈

- **实时提醒**：推广内容被打开或转化时向推广者发送通知
- **数据报表**：提供推广效果数据看板
- **优化建议**：根据历史数据提供推广优化建议

## 7. 实施路径

### 7.1 阶段规划

#### 阶段一：基础架构搭建（2周）
- 构建推广工具服务层
- 开发通用推广按钮组件
- 改造现有推广工具页面支持多场景

#### 阶段二：核心业务模块集成（3周）
- 拼车系统集成
- 二手交易系统集成
- 房屋租售系统集成
- 服务系统集成

#### 阶段三：功能完善与优化（2周）
- 推广效果追踪系统构建
- 数据分析与报表功能
- 用户体验优化

### 7.2 优先级排序

1. **高优先级**
   - 推广工具服务层构建
   - 拼车和二手交易系统集成
   - 基础推广效果追踪

2. **中优先级**
   - 房屋租售和服务系统集成
   - 推广数据统计与分析
   - 用户推广激励机制

3. **低优先级**
   - 推广内容模板丰富
   - 高级推广效果分析
   - 推广工具个性化定制

### 7.3 评估指标

- **工具使用率**：推广工具的使用频次和覆盖率
- **分享转化率**：推广内容带来的访问与转化
- **用户增长**：推广带来的新用户注册量
- **交易促进**：推广带来的交易量增长

---

通过以上设计，推广工具将不再局限于分销系统，而是成为整个小程序的基础能力，服务于各个业务模块，提升平台的用户增长和内容传播效率。 