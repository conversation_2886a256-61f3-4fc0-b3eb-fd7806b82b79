version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: cizhou-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: cizhou123456
      MYSQL_DATABASE: cizhou_admin
      MYSQL_USER: cizhou
      MYSQL_PASSWORD: cizhou123456
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - cizhou-network

  # Redis缓存
  redis:
    image: redis:7.0-alpine
    container_name: cizhou-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass cizhou123456
    networks:
      - cizhou-network

  # Nacos服务注册中心
  nacos:
    image: nacos/nacos-server:v2.3.0
    container_name: cizhou-nacos
    restart: unless-stopped
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql
      MYSQL_SERVICE_PORT: 3306
      MYSQL_SERVICE_DB_NAME: nacos
      MYSQL_SERVICE_USER: root
      MYSQL_SERVICE_PASSWORD: cizhou123456
      MYSQL_SERVICE_DB_PARAM: characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useSSL=false&allowPublicKeyRetrieval=true
      NACOS_AUTH_ENABLE: true
      NACOS_AUTH_TOKEN: SecretKey012345678901234567890123456789012345678901234567890123456789
      NACOS_AUTH_IDENTITY_KEY: nacos
      NACOS_AUTH_IDENTITY_VALUE: nacos
    ports:
      - "8848:8848"
      - "9848:9848"
    volumes:
      - nacos_data:/home/<USER>/data
    depends_on:
      - mysql
    networks:
      - cizhou-network

  # RabbitMQ消息队列
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: cizhou-rabbitmq
    restart: unless-stopped
    environment:
      RABBITMQ_DEFAULT_USER: cizhou
      RABBITMQ_DEFAULT_PASS: cizhou123456
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - cizhou-network

  # API网关
  gateway:
    build:
      context: ./cizhou-gateway
      dockerfile: Dockerfile
    container_name: cizhou-gateway
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      NACOS_SERVER_ADDR: nacos:8848
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      REDIS_HOST: redis
      REDIS_PORT: 6379
    depends_on:
      - mysql
      - redis
      - nacos
    networks:
      - cizhou-network

  # 认证服务
  auth-service:
    build:
      context: ./cizhou-auth
      dockerfile: Dockerfile
    container_name: cizhou-auth
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      NACOS_SERVER_ADDR: nacos:8848
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      REDIS_HOST: redis
      REDIS_PORT: 6379
    depends_on:
      - mysql
      - redis
      - nacos
    networks:
      - cizhou-network

  # 用户服务
  user-service:
    build:
      context: ./cizhou-user-service
      dockerfile: Dockerfile
    container_name: cizhou-user-service
    restart: unless-stopped
    ports:
      - "8082:8082"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      NACOS_SERVER_ADDR: nacos:8848
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      REDIS_HOST: redis
      REDIS_PORT: 6379
    depends_on:
      - mysql
      - redis
      - nacos
    networks:
      - cizhou-network

  # 商家服务
  merchant-service:
    build:
      context: ./cizhou-merchant-service
      dockerfile: Dockerfile
    container_name: cizhou-merchant-service
    restart: unless-stopped
    ports:
      - "8083:8083"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      NACOS_SERVER_ADDR: nacos:8848
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      REDIS_HOST: redis
      REDIS_PORT: 6379
    depends_on:
      - mysql
      - redis
      - nacos
    networks:
      - cizhou-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  nacos_data:
    driver: local
  rabbitmq_data:
    driver: local

networks:
  cizhou-network:
    driver: bridge
