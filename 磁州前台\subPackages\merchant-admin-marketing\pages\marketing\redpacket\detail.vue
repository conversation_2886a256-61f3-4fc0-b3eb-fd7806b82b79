<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">红包详情</text>
      <view class="navbar-right">
        <view class="more-icon" @click="showMoreActions">...</view>
      </view>
    </view>
    
    <!-- 红包基本信息 -->
    <view class="redpacket-header">
      <view class="redpacket-cover">
        <image class="cover-image" :src="redpacketData.coverUrl" mode="aspectFill"></image>
        <view class="redpacket-status" :class="'status-'+redpacketData.status">{{redpacketData.statusText}}</view>
      </view>
      <view class="redpacket-info">
        <text class="redpacket-name">{{redpacketData.name}}</text>
        <view class="redpacket-type">
          <view class="type-tag" :class="'type-'+redpacketData.type">{{redpacketData.typeText}}</view>
        </view>
        <view class="redpacket-time">
          <view class="time-icon"></view>
          <text class="time-text">{{redpacketData.timeRange}}</text>
        </view>
      </view>
    </view>
    
    <!-- 数据概览卡片 -->
    <view class="stats-card">
      <view class="stats-header">
        <text class="stats-title">数据概览</text>
        <view class="stats-action" @click="viewFullStats">
          <text class="action-text">查看详细</text>
          <view class="action-arrow"></view>
        </view>
      </view>
      
      <view class="stats-grid">
        <view class="stats-item">
          <text class="stats-value">{{redpacketData.totalCount}}</text>
          <text class="stats-label">总数量</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{redpacketData.sentCount}}</text>
          <text class="stats-label">已发放</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{redpacketData.receivedCount}}</text>
          <text class="stats-label">已领取</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{redpacketData.usedCount}}</text>
          <text class="stats-label">已使用</text>
        </view>
      </view>
      
      <view class="progress-section">
        <view class="progress-header">
          <text class="progress-title">发放进度</text>
          <text class="progress-value">{{redpacketData.sentCount}}/{{redpacketData.totalCount}}</text>
        </view>
        <view class="progress-bar">
          <view class="progress-fill" :style="{ width: (redpacketData.sentCount / redpacketData.totalCount * 100) + '%' }"></view>
        </view>
      </view>
    </view>
    
    <!-- 红包设置信息 -->
    <view class="settings-card">
      <view class="settings-header">
        <text class="settings-title">红包设置</text>
        <view class="settings-action" v-if="redpacketData.status === 'draft'" @click="editRedpacket">
          <text class="action-text">编辑</text>
          <view class="action-arrow"></view>
        </view>
      </view>
      
      <view class="settings-list">
        <view class="settings-item">
          <text class="item-label">红包金额</text>
          <text class="item-value" v-if="redpacketData.amountType === 'fixed'">¥{{redpacketData.fixedAmount}}/个</text>
          <text class="item-value" v-else-if="redpacketData.amountType === 'random'">¥{{redpacketData.minAmount}}-{{redpacketData.maxAmount}}/个</text>
        </view>
        
        <view class="settings-item">
          <text class="item-label">发放对象</text>
          <text class="item-value">{{redpacketData.targetText}}</text>
        </view>
        
        <view class="settings-item">
          <text class="item-label">使用门槛</text>
          <text class="item-value">{{redpacketData.thresholdText}}</text>
        </view>
        
        <view class="settings-item">
          <text class="item-label">有效期</text>
          <text class="item-value">领取后{{redpacketData.validity}}天有效</text>
        </view>
        
        <view class="settings-item">
          <text class="item-label">单用户领取上限</text>
          <text class="item-value">{{redpacketData.userLimit}}个</text>
        </view>
      </view>
    </view>
    
    <!-- 使用说明 -->
    <view class="description-card">
      <view class="description-header">
        <text class="description-title">使用说明</text>
      </view>
      <view class="description-content">
        <text class="description-text">{{redpacketData.description}}</text>
      </view>
    </view>
    
    <!-- 领取记录 -->
    <view class="records-card">
      <view class="records-header">
        <text class="records-title">领取记录</text>
        <view class="records-action" @click="viewAllRecords">
          <text class="action-text">查看全部</text>
          <view class="action-arrow"></view>
        </view>
      </view>
      
      <view class="records-list">
        <view class="records-item" v-for="(record, index) in redpacketData.records" :key="index">
          <view class="user-avatar">
            <image class="avatar-image" :src="record.avatar" mode="aspectFill"></image>
          </view>
          <view class="record-info">
            <text class="user-name">{{record.userName}}</text>
            <text class="record-time">{{record.time}}</text>
          </view>
          <view class="record-amount">
            <text class="amount-value">¥{{record.amount}}</text>
            <text class="amount-status" v-if="record.used">已使用</text>
            <text class="amount-status unused" v-else>未使用</text>
          </view>
        </view>
        
        <view class="empty-records" v-if="redpacketData.records.length === 0">
          <view class="empty-icon"></view>
          <text class="empty-text">暂无领取记录</text>
        </view>
      </view>
    </view>
    
    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <view class="action-button share" v-if="redpacketData.status === 'active'" @click="shareRedpacket">
        <view class="button-icon share-icon"></view>
        <text class="button-text">分享红包</text>
      </view>
      
      <view class="action-button start" v-if="redpacketData.status === 'upcoming'" @click="startRedpacket">
        <view class="button-icon start-icon"></view>
        <text class="button-text">立即开始</text>
      </view>
      
      <view class="action-button stop" v-if="redpacketData.status === 'active'" @click="stopRedpacket">
        <view class="button-icon stop-icon"></view>
        <text class="button-text">结束活动</text>
      </view>
      
      <view class="action-button delete" v-if="redpacketData.status === 'draft' || redpacketData.status === 'ended'" @click="deleteRedpacket">
        <view class="button-icon delete-icon"></view>
        <text class="button-text">删除活动</text>
      </view>
      
      <view class="action-button duplicate" @click="duplicateRedpacket">
        <view class="button-icon duplicate-icon"></view>
        <text class="button-text">复制活动</text>
      </view>
    </view>
    
    <!-- 分享弹窗 -->
    <view class="share-popup" v-if="showSharePopup">
      <view class="popup-mask" @click="closeSharePopup"></view>
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">分享红包</text>
          <view class="popup-close" @click="closeSharePopup">×</view>
        </view>
        
        <view class="share-options">
          <view class="share-option" v-for="(option, index) in shareOptions" :key="index" @click="shareVia(option.type)">
            <view class="option-icon" :style="{ background: option.color }">
              <image class="icon-image" :src="option.icon" mode="aspectFit"></image>
            </view>
            <text class="option-text">{{option.name}}</text>
          </view>
        </view>
        
        <view class="share-qrcode">
          <image class="qrcode-image" src="/static/images/redpacket/qrcode-placeholder.png" mode="aspectFit"></image>
          <text class="qrcode-tip">保存二维码分享</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      showSharePopup: false,
      
      // 红包详情数据
      redpacketData: {
        id: 1,
        name: '新用户专享红包',
        coverUrl: '/static/images/redpacket/cover-placeholder.jpg',
        type: 'normal',
        typeText: '普通红包',
        status: 'active',
        statusText: '进行中',
        timeRange: '2023-04-15 ~ 2023-04-30',
        
        // 数据统计
        totalCount: 1000,
        sentCount: 568,
        receivedCount: 452,
        usedCount: 326,
        
        // 红包设置
        amountType: 'fixed',
        fixedAmount: '10.00',
        minAmount: '',
        maxAmount: '',
        target: 'all',
        targetText: '所有用户',
        threshold: 'none',
        thresholdText: '无门槛',
        validity: '7',
        userLimit: '1',
        
        // 使用说明
        description: '1. 每位用户限领1个红包\n2. 红包领取后7天内有效\n3. 红包可在下单时直接抵扣\n4. 不可与其他优惠同时使用\n5. 最终解释权归商家所有',
        
        // 领取记录
        records: [
          {
            userName: '用户1354***89',
            avatar: '/static/images/redpacket/avatar1.jpg',
            time: '2023-04-20 14:32:56',
            amount: '10.00',
            used: true
          },
          {
            userName: '用户1567***23',
            avatar: '/static/images/redpacket/avatar2.jpg',
            time: '2023-04-20 14:28:12',
            amount: '10.00',
            used: false
          },
          {
            userName: '用户1892***45',
            avatar: '/static/images/redpacket/avatar3.jpg',
            time: '2023-04-20 14:15:38',
            amount: '10.00',
            used: true
          }
        ]
      },
      
      // 分享选项
      shareOptions: [
        {
          name: '微信好友',
          type: 'wechat',
          icon: '/static/images/redpacket/wechat-icon.png',
          color: '#07C160'
        },
        {
          name: '朋友圈',
          type: 'moments',
          icon: '/static/images/redpacket/moments-icon.png',
          color: '#07C160'
        },
        {
          name: '微博',
          type: 'weibo',
          icon: '/static/images/redpacket/weibo-icon.png',
          color: '#E6162D'
        },
        {
          name: '短信',
          type: 'sms',
          icon: '/static/images/redpacket/sms-icon.png',
          color: '#FF9500'
        }
      ]
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showMoreActions() {
      uni.showActionSheet({
        itemList: ['导出数据', '复制链接', '设为模板'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.exportData();
              break;
            case 1:
              this.copyLink();
              break;
            case 2:
              this.saveAsTemplate();
              break;
          }
        }
      });
    },
    exportData() {
      uni.showToast({
        title: '导出功能开发中',
        icon: 'none'
      });
    },
    copyLink() {
      uni.setClipboardData({
        data: 'https://example.com/redpacket/' + this.redpacketData.id,
        success: () => {
          uni.showToast({
            title: '链接已复制',
            icon: 'success'
          });
        }
      });
    },
    saveAsTemplate() {
      uni.showToast({
        title: '已保存为模板',
        icon: 'success'
      });
    },
    viewFullStats() {
      uni.showToast({
        title: '详细数据功能开发中',
        icon: 'none'
      });
    },
    editRedpacket() {
      uni.showToast({
        title: '编辑功能开发中',
        icon: 'none'
      });
    },
    viewAllRecords() {
      uni.showToast({
        title: '查看全部记录功能开发中',
        icon: 'none'
      });
    },
    shareRedpacket() {
      this.showSharePopup = true;
    },
    closeSharePopup() {
      this.showSharePopup = false;
    },
    shareVia(type) {
      uni.showToast({
        title: '分享功能开发中',
        icon: 'none'
      });
      this.closeSharePopup();
    },
    startRedpacket() {
      uni.showModal({
        title: '确认开始',
        content: '确定要立即开始此红包活动吗？',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '活动已开始',
              icon: 'success'
            });
            this.redpacketData.status = 'active';
            this.redpacketData.statusText = '进行中';
          }
        }
      });
    },
    stopRedpacket() {
      uni.showModal({
        title: '确认结束',
        content: '确定要结束此红包活动吗？结束后不可恢复。',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '活动已结束',
              icon: 'success'
            });
            this.redpacketData.status = 'ended';
            this.redpacketData.statusText = '已结束';
          }
        }
      });
    },
    deleteRedpacket() {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除此红包活动吗？删除后不可恢复。',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          }
        }
      });
    },
    duplicateRedpacket() {
      uni.showToast({
        title: '复制功能开发中',
        icon: 'none'
      });
    }
  }
}
</script>

<style lang="scss">
.page-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 80px;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.more-icon {
  font-size: 20px;
  font-weight: bold;
  transform: rotate(90deg);
}

/* 红包头部信息 */
.redpacket-header {
  background-color: #fff;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.redpacket-cover {
  width: 100%;
  height: 160px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  margin-bottom: 15px;
}

.cover-image {
  width: 100%;
  height: 100%;
}

.redpacket-status {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px 10px;
  border-radius: 15px;
  font-size: 12px;
}

.status-active {
  background-color: #E8F5E9;
  color: #388E3C;
}

.status-upcoming {
  background-color: #E3F2FD;
  color: #1976D2;
}

.status-ended {
  background-color: #EEEEEE;
  color: #757575;
}

.status-draft {
  background-color: #FFF3E0;
  color: #E65100;
}

.redpacket-info {
  padding: 0 5px;
}

.redpacket-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.redpacket-type {
  margin-bottom: 10px;
}

.type-tag {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.type-normal {
  background-color: #FFE0E0;
  color: #FF5858;
}

.type-fission {
  background-color: #E0F7F4;
  color: #4ECDC4;
}

.type-mass {
  background-color: #FFF3E0;
  color: #FFD166;
}

.type-rain {
  background-color: #E8E0F7;
  color: #6A0572;
}

.redpacket-time {
  display: flex;
  align-items: center;
}

.time-icon {
  width: 14px;
  height: 14px;
  background-color: #ccc;
  border-radius: 7px;
  margin-right: 5px;
}

.time-text {
  font-size: 12px;
  color: #666;
}

/* 数据概览卡片 */
.stats-card {
  background-color: #fff;
  padding: 15px;
  margin: 0 15px 15px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.stats-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.stats-action {
  display: flex;
  align-items: center;
}

.action-text {
  font-size: 12px;
  color: #999;
  margin-right: 5px;
}

.action-arrow {
  width: 8px;
  height: 8px;
  border-top: 1px solid #999;
  border-right: 1px solid #999;
  transform: rotate(45deg);
}

.stats-grid {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.stats-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.stats-label {
  font-size: 12px;
  color: #999;
}

.progress-section {
  padding-top: 10px;
  border-top: 1px solid #eee;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.progress-title {
  font-size: 14px;
  color: #333;
}

.progress-value {
  font-size: 12px;
  color: #666;
}

.progress-bar {
  height: 6px;
  background-color: #F5F7FA;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FF5858, #FF0000);
  border-radius: 3px;
}

/* 红包设置卡片 */
.settings-card {
  background-color: #fff;
  padding: 15px;
  margin: 0 15px 15px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.settings-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.settings-action {
  display: flex;
  align-items: center;
}

.settings-list {
  padding-top: 5px;
}

.settings-item {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #f5f5f5;
}

.settings-item:last-child {
  border-bottom: none;
}

.item-label {
  font-size: 14px;
  color: #666;
}

.item-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

/* 使用说明卡片 */
.description-card {
  background-color: #fff;
  padding: 15px;
  margin: 0 15px 15px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.description-header {
  margin-bottom: 10px;
}

.description-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.description-content {
  padding: 10px;
  background-color: #F9F9F9;
  border-radius: 8px;
}

.description-text {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

/* 领取记录卡片 */
.records-card {
  background-color: #fff;
  padding: 15px;
  margin: 0 15px 15px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.records-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.records-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.records-action {
  display: flex;
  align-items: center;
}

.records-list {
  max-height: 300px;
  overflow-y: auto;
}

.records-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f5f5f5;
}

.records-item:last-child {
  border-bottom: none;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  overflow: hidden;
  margin-right: 10px;
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.record-info {
  flex: 1;
}

.user-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
  display: block;
}

.record-time {
  font-size: 12px;
  color: #999;
}

.record-amount {
  text-align: right;
}

.amount-value {
  font-size: 16px;
  font-weight: 600;
  color: #FF5858;
  display: block;
  margin-bottom: 5px;
}

.amount-status {
  font-size: 12px;
  color: #388E3C;
}

.amount-status.unused {
  color: #999;
}

.empty-records {
  padding: 30px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-icon {
  width: 60px;
  height: 60px;
  background-color: #F5F7FA;
  border-radius: 30px;
  margin-bottom: 10px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  display: flex;
  padding: 10px 15px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.action-button {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 5px;
}

.action-button.share {
  background: linear-gradient(135deg, #FF5858, #FF0000);
  color: #fff;
}

.action-button.start {
  background: linear-gradient(135deg, #4ECDC4, #2BAF9F);
  color: #fff;
}

.action-button.stop {
  background: linear-gradient(135deg, #FF9500, #FF5E3A);
  color: #fff;
}

.action-button.delete {
  background-color: #F5F7FA;
  color: #FF3B30;
}

.action-button.duplicate {
  background-color: #F5F7FA;
  color: #666;
}

.button-icon {
  width: 16px;
  height: 16px;
  margin-right: 5px;
}

.button-text {
  font-size: 14px;
  font-weight: 500;
}

/* 分享弹窗 */
.share-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}

.popup-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  padding: 15px;
  transform: translateY(0);
  transition: transform 0.3s;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.popup-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.popup-close {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #999;
}

.share-options {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
}

.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.option-icon {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.icon-image {
  width: 30px;
  height: 30px;
}

.option-text {
  font-size: 12px;
  color: #666;
}

.share-qrcode {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 0;
  border-top: 1px solid #eee;
}

.qrcode-image {
  width: 150px;
  height: 150px;
  margin-bottom: 10px;
}

.qrcode-tip {
  font-size: 12px;
  color: #999;
}
</style>