"use strict";
const common_vendor = require("../common/vendor.js");
const mixins_basePromotionMixin = require("./basePromotionMixin.js");
const merchantPromotionMixin = {
  mixins: [mixins_basePromotionMixin.basePromotionMixin],
  data() {
    return {
      // 设置页面类型为商家
      pageType: "merchant"
    };
  },
  methods: {
    /**
     * 重写：判断当前用户是否是内容所有者
     */
    isContentOwner() {
      var _a, _b, _c, _d, _e;
      const currentUserId = ((_c = (_b = (_a = this.$store) == null ? void 0 : _a.state) == null ? void 0 : _b.user) == null ? void 0 : _c.userId) || "";
      const merchantId = ((_d = this.shopData) == null ? void 0 : _d.merchantId) || ((_e = this.shopData) == null ? void 0 : _e.ownerId) || "";
      return currentUserId && merchantId && currentUserId === merchantId;
    },
    /**
     * 重写：判断当前内容是否支持佣金
     */
    isCommissionContent() {
      var _a;
      const canDistribute = ((_a = this.shopData) == null ? void 0 : _a.canDistribute) !== false;
      return canDistribute;
    },
    /**
     * 重写：生成推广数据
     */
    generatePromotionData() {
      var _a;
      const shop = this.shopData || {};
      this.promotionData = {
        id: shop.id || "",
        title: shop.shopName || "商家详情",
        image: shop.logo || ((_a = shop.images) == null ? void 0 : _a[0]) || "/static/images/tabbar/商家入驻.png",
        category: shop.category || "",
        address: shop.address || "",
        contactPhone: shop.contactPhone || "",
        description: shop.description ? shop.description.length > 50 ? shop.description.substring(0, 50) + "..." : shop.description : ""
        // 如果有更多商家特定字段，可以在这里添加
      };
    },
    /**
     * 显示商家推广浮层
     */
    showMerchantPromotion() {
      if (!this.hasPromotionPermission) {
        common_vendor.index.showToast({
          title: "暂无推广权限",
          icon: "none"
        });
        return;
      }
      this.openPromotionTools();
    }
  }
};
exports.merchantPromotionMixin = merchantPromotionMixin;
//# sourceMappingURL=../../.sourcemap/mp-weixin/mixins/merchantPromotionMixin.js.map
