package com.cizhou.finance;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 财务服务启动类
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = {"com.cizhou.finance", "com.cizhou.common"})
@EnableDiscoveryClient
@MapperScan("com.cizhou.finance.mapper")
public class FinanceServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(FinanceServiceApplication.class, args);
        System.out.println("💰 磁州生活网财务服务启动成功!");
    }
}
