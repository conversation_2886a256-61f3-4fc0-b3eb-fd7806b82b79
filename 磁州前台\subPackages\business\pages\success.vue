<template>
  <view class="success-container">
    <!-- 顶部背景渐变 -->
    <view class="top-gradient"></view>
    
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 导航栏 -->
    <view class="navbar">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">商家入驻</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 内容区域 -->
    <view class="content">
      <!-- 成功信息 -->
      <view class="success-info">
        <view class="success-message-card">
          <image :src="celebrateIcon || '/static/images/tabbar/成功.png'" class="success-message-icon"></image>
          <view class="success-message-content">
            <view class="success-message-title">恭喜！您已成功入驻</view>
            <view class="success-message-subtitle">审核通过，您的店铺已正式上线</view>
        </view>
      </view>
      
        <view class="shop-info">
          <view class="shop-avatar">
            <image :src="shopInfo.logo || '/static/images/default-shop.png'" mode="aspectFill"></image>
          </view>
          <view class="shop-name">{{shopInfo.name}}</view>
          <view class="shop-verify-tip" v-if="!isVerified">
            <image src="/static/images/tabbar/认证.png" class="verify-icon"></image>
            <text class="verify-text">去认证享更多权益</text>
            <view class="verify-btn" @click="goToVerify">去认证</view>
          </view>
          <view class="shop-verified" v-if="isVerified">
            <image src="/static/images/tabbar/已认证.png" class="verified-icon"></image>
            <text class="verified-text">已认证商家</text>
          </view>
          <view class="test-mark" v-if="isTestMerchant">测试账号</view>
        </view>
        
        <!-- 入驻版本信息 -->
        <view class="membership-info" v-if="memberType">
          <view class="membership-badge" :class="memberClass">{{memberType}}</view>
          <view class="membership-date">{{expiryDate}} 到期</view>
        </view>
      </view>
      
      <!-- 商家数据概览 -->
      <view class="data-overview">
        <view class="data-card">
          <view class="data-item">
            <view class="data-num">120万+</view>
            <view class="data-label">平台月流量</view>
          </view>
          <view class="data-divider"></view>
          <view class="data-item">
            <view class="data-num">32%</view>
            <view class="data-label">商家转化率</view>
          </view>
          <view class="data-divider"></view>
          <view class="data-item">
            <view class="data-num">16.8万</view>
            <view class="data-label">注册用户</view>
          </view>
        </view>
      </view>
      
      <!-- 认证好处提示（仅未认证商家显示） -->
      <view class="verify-benefits" v-if="!isVerified">
        <view class="verify-benefits-title">
          <image src="/static/images/tabbar/认证.png" class="verify-benefit-icon"></image>
          <text>商家认证享多重权益</text>
        </view>
        <view class="verify-benefits-list">
          <view class="verify-benefit-item">
            <view class="benefit-dot"></view>
            <text class="benefit-text">店铺获得官方认证标识，提升信任度</text>
          </view>
          <view class="verify-benefit-item">
            <view class="benefit-dot"></view>
            <text class="benefit-text">搜索结果排名靠前，提高曝光量</text>
          </view>
          <view class="verify-benefit-item">
            <view class="benefit-dot"></view>
            <text class="benefit-text">获取平台流量扶持，拓展客源渠道</text>
          </view>
          <view class="verify-benefit-item">
            <view class="benefit-dot"></view>
            <text class="benefit-text">专属客服通道，问题快速解决</text>
          </view>
        </view>
        <button class="verify-now-btn" @click="goToVerify">立即认证</button>
      </view>
      
      <!-- 功能入口 -->
      <view class="function-section">
        <view class="function-title">商家中心功能</view>
        <view class="function-grid">
          <view class="function-item" @click="navigateTo("/subPackages/merchant-plugin/pages/goods')">
            <view class="function-icon-bg bg-blue">
              <image src="/static/images/tabbar/商品.png" class="function-icon"></image>
            </view>
            <view class="function-name">商品管理</view>
          </view>
          <view class="function-item" @click="navigateTo("/subPackages/merchant-plugin/pages/orders')">
            <view class="function-icon-bg bg-orange">
              <image src="/static/images/tabbar/订单.png" class="function-icon"></image>
            </view>
            <view class="function-name">订单管理</view>
          </view>
          <view class="function-item" @click="navigateTo("/subPackages/merchant-plugin/pages/marketing')">
            <view class="function-icon-bg bg-green">
              <image src="/static/images/tabbar/营销.png" class="function-icon"></image>
            </view>
            <view class="function-name">营销推广</view>
          </view>
          <view class="function-item" @click="navigateTo("/subPackages/merchant-plugin/pages/analysis')">
            <view class="function-icon-bg bg-purple">
              <image src="/static/images/tabbar/数据.png" class="function-icon"></image>
            </view>
            <view class="function-name">数据分析</view>
          </view>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button class="primary-btn" @click="goToMerchantCenter">进入商家后台</button>
        <button class="secondary-btn" @click="goToShopPage">查看店铺页面</button>
      </view>
      
      <!-- 下一步提示 -->
      <view class="next-steps">
        <view class="next-title">推荐您下一步</view>
        <view class="step-item" @click="navigateTo("/subPackages/merchant-plugin/pages/goods')">
          <view class="step-left">
            <view class="step-num">1</view>
            <view class="step-text">上传店铺商品，丰富店铺内容</view>
          </view>
          <view class="step-right">
            <text class="go-text">去上传</text>
            <image src="/static/images/tabbar/arrow-up.png" class="go-icon"></image>
          </view>
        </view>
        <view class="step-item" @click="navigateTo("/subPackages/merchant-plugin/pages/marketing')">
          <view class="step-left">
            <view class="step-num">2</view>
            <view class="step-text">开通店铺推广，提升曝光量</view>
          </view>
          <view class="step-right">
            <text class="go-text">去开通</text>
            <image src="/static/images/tabbar/arrow-up.png" class="go-icon"></image>
          </view>
        </view>
        <view class="step-item" @click="goToMerchantCenter">
          <view class="step-left">
            <view class="step-num">3</view>
            <view class="step-text">完善店铺信息，提高用户信任度</view>
          </view>
          <view class="step-right">
            <text class="go-text">去完善</text>
            <image src="/static/images/tabbar/arrow-up.png" class="go-icon"></image>
          </view>
        </view>
      </view>
      
      <!-- 联系客服 -->
      <view class="contact-section">
        <view class="contact-text">遇到问题？联系客服获取帮助</view>
        <button class="contact-btn" @click="contactCustomerService">联系客服</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20,
      shopInfo: {
        logo: '',
        name: '品牌专卖店',
        id: 'shop123456'
      },
      celebrateIcon: '',
      memberType: '基础版',
      expiryDate: '2024-12-31',
      isTestMerchant: false,
      isVerified: false
    }
  },
  computed: {
    memberClass() {
      const classMap = {
        '基础版': 'basic-badge',
        '高级版': 'premium-badge',
        '尊贵版': 'deluxe-badge',
        '免费版': 'free-badge',
        '测试版': 'test-badge'
      };
      return classMap[this.memberType] || 'basic-badge';
    }
  },
  onLoad(options) {
    // 获取状态栏高度
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    
    // 获取管理后台上传的喝彩图标
    this.getCelebrateIcon();
    
    // 检查是否是测试入驻的商家
    if (options.id) {
      this.loadMerchantData(options.id);
    }
    // 如果有参数传入则获取
    else if (options.shopId) {
      // 实际项目中应该从后端获取店铺信息
      this.getShopInfo(options.shopId);
    }
    
    // 获取会员类型
    if (options.memberType) {
      this.memberType = decodeURIComponent(options.memberType);
      
      // 设置到期时间
      const now = new Date();
      if (this.memberType === '免费版') {
        // 免费版一个月
        now.setMonth(now.getMonth() + 1);
      } else {
        // 付费版一年
        now.setFullYear(now.getFullYear() + 1);
      }
      
      this.expiryDate = now.getFullYear() + '-' + 
        String(now.getMonth() + 1).padStart(2, '0') + '-' + 
        String(now.getDate()).padStart(2, '0');
    }
  },
  methods: {
    goBack() {
      uni.switchTab({
        url: '/pages/index/index'
      });
    },
    navigateTo(url) {
      uni.navigateTo({
        url: url
      });
    },
    goToMerchantCenter() {
      uni.navigateTo({
        url: '/pages/my/merchant'
      });
    },
    goToShopPage() {
      uni.navigateTo({
        url: `/pages/business/shop-detail?id=${this.shopInfo.id}`
      });
    },
    getShopInfo(shopId) {
      /**
       * 获取商家信息，包括商家LOGO
       * 
       * 注意：实际生产环境应从后端获取完整的商家信息
       * 商家LOGO应通过shopInfo.logo字段获取
       */
      
      // 生产环境代码
      uni.request({
        url: `https://api.example.com/shop/${shopId}`, // 替换为实际的API接口
        method: 'GET',
        success: (res) => {
          if (res.data) {
            this.shopInfo = {
              logo: res.data.logo || '/static/images/default-shop.png',
              name: res.data.name || '品牌专卖店',
              id: shopId
            };
          }
        },
        fail: () => {
          // 请求失败时使用默认数据
          this.shopInfo = {
            logo: '/static/images/default-shop.png',
            name: '品牌专卖店',
            id: shopId
          };
        }
      });
      
      // TODO: 开发阶段临时使用，上线前移除此代码！
      // 模拟商家数据用于本地开发测试
      setTimeout(() => {
        this.shopInfo = {
          logo: '/static/images/avatar/商家.png', // 确保在开发阶段使用实际存在的图片
          name: '品牌专卖店',
          id: shopId
        };
      }, 500);
    },
    contactCustomerService() {
      uni.makePhoneCall({
        phoneNumber: '************',
        fail: () => {
          uni.showToast({
            title: '拨打客服电话失败',
            icon: 'none'
          });
        }
      });
    },
    loadMerchantData(id) {
      try {
        // 从本地存储中获取测试商家数据
        const merchantTestData = uni.getStorageSync('merchantTestData') || [];
        // 查找匹配的商家数据
        const merchant = merchantTestData.find(item => item.id === id);
        
        if (merchant) {
          // 设置为测试商家
          this.isTestMerchant = true;
          // 更新商家信息
          this.shopInfo = {
            logo: merchant.logo || '/static/images/default-shop.png',
            name: merchant.shopName || '测试商家',
            id: merchant.id
          };
          // 设置会员类型为测试版
          this.memberType = '测试版';
          
          // 设置到期时间 (测试版默认一个月)
          const now = new Date();
          now.setMonth(now.getMonth() + 1);
          const month = (now.getMonth() + 1).toString().padStart(2, '0');
          const day = now.getDate().toString().padStart(2, '0');
          this.expiryDate = `${now.getFullYear()}-${month}-${day}`;
          
          console.log('成功加载测试商家数据', merchant);
        } else {
          console.error('未找到对应ID的测试商家数据');
          // 使用默认数据
          this.memberType = '免费版';
        }
      } catch (e) {
        console.error('加载测试商家数据失败', e);
        // 出错时使用默认数据
        this.memberType = '免费版';
      }
    },
    goToVerify() {
      // 导航到认证页面
      uni.navigateTo({
        url: '/pages/business/verify'
      });
    },
    getCelebrateIcon() {
      /**
       * 获取管理后台上传的喝彩图标
       * 注意：在实际生产环境中，应该从后端API获取管理员在后台配置的图标
       * API应该返回类似格式：{ url: '图片URL', width: 宽度, height: 高度 }
       * 
       * 接口路径示例：/api/config/system/celebrate-icon
       * 
       * 当后端API尚未实现时，使用本地模拟数据进行开发测试
       */
      
      // 生产环境代码
      // 调用接口获取管理后台设置的喝彩图标
      uni.request({
        url: 'https://api.example.com/config/celebrate-icon', // 替换为实际的API接口
        method: 'GET',
        success: (res) => {
          if (res.data && res.data.url) {
            this.celebrateIcon = res.data.url;
          }
        },
        fail: () => {
          console.log('获取喝彩图标失败，使用默认图标');
        }
      });
      
      // TODO: 开发阶段临时使用，上线前移除此代码！
      // 使用本地图标模拟管理员上传的喝彩图标
      this.celebrateIcon = '/static/images/tabbar/喝彩.png';
    }
  }
}
</script>

<style lang="scss">
.success-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f9fc;
}

/* 顶部渐变背景 */
.top-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 30vh;
  background: linear-gradient(135deg, #0046B3, #1677FF);
  border-bottom-left-radius: 30px;
  border-bottom-right-radius: 30px;
  z-index: 0;
}

/* 导航栏 */
.navbar {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  position: relative;
  z-index: 1;
}

.navbar-left {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24px;
  height: 24px;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #fff;
}

.navbar-right {
  width: 44px;
}

/* 内容区域 */
.content {
  flex: 1;
  position: relative;
  z-index: 1;
  padding: 0 20px 30px;
}

/* 成功信息 */
.success-info {
  margin-top: 20px;
  text-align: center;
  position: relative;
  z-index: 5;
}

.success-message-card {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 16px;
  padding: 16px 20px;
  margin-bottom: 20px;
  box-shadow: 
    0 3px 6px rgba(0, 0, 0, 0.05),
    0 8px 16px rgba(0, 0, 0, 0.1),
    0 12px 24px -10px rgba(0, 82, 204, 0.15);
  transform: perspective(1000px) translateZ(0) rotateX(0.5deg);
  transform-style: preserve-3d;
  border: 1px solid rgba(255, 255, 255, 0.7);
  transition: transform 0.3s ease;
  position: relative;
}

.success-message-card::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.6) 0%, 
    rgba(255, 255, 255, 0.2) 50%, 
    rgba(255, 255, 255, 0) 100%);
  border-radius: 16px;
  pointer-events: none;
}

.success-message-icon {
  width: 48px;
  height: 48px;
  margin-right: 16px;
  flex-shrink: 0;
}

.success-message-content {
  flex: 1;
  text-align: left;
}

.success-message-title {
  font-size: 18px;
  font-weight: bold;
  color: #0052CC;
  margin-bottom: 4px;
}

.success-message-subtitle {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.shop-info {
  background-color: #fff;
  border-radius: 16px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  box-shadow: 
    0 3px 6px rgba(0, 0, 0, 0.05),
    0 8px 20px rgba(0, 0, 0, 0.1), 
    0 15px 30px -12px rgba(0, 82, 204, 0.2);
  transform: perspective(1000px) translateZ(0) rotateX(1deg);
  transform-style: preserve-3d;
  border: 1px solid rgba(255, 255, 255, 0.7);
  transition: transform 0.3s ease;
}

.shop-info::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.6) 0%, 
    rgba(255, 255, 255, 0.2) 50%, 
    rgba(255, 255, 255, 0) 100%);
  border-radius: 16px;
  pointer-events: none;
}

.shop-info::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  box-shadow: inset 0 1px 1px rgba(255, 255, 255, 0.5);
  border-radius: 16px;
  pointer-events: none;
}

.shop-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #f0f4ff;
  margin-bottom: 12px;
  background-color: #f8f8f8;
  box-shadow: 0 4px 10px rgba(0, 82, 204, 0.15);
}

.shop-avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.shop-name {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.shop-verify-tip {
  display: flex;
  align-items: center;
  background-color: #f0f8ff;
  padding: 4px 12px;
  border-radius: 20px;
}

.verify-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.verify-text {
  font-size: 14px;
  color: #0052CC;
}

.verify-btn {
  font-size: 14px;
  color: #0052CC;
  margin-left: 4px;
  cursor: pointer;
}

.shop-verified {
  display: flex;
  align-items: center;
  background-color: #f0f8ff;
  padding: 4px 12px;
  border-radius: 20px;
}

.verified-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.verified-text {
  font-size: 14px;
  color: #0052CC;
}

/* 数据概览 */
.data-overview {
  margin-top: 20px;
}

.data-card {
  background-color: #fff;
  border-radius: 16px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  position: relative;
  box-shadow: 
    0 3px 6px rgba(0, 0, 0, 0.05),
    0 8px 16px rgba(0, 0, 0, 0.1),
    0 12px 24px -10px rgba(0, 82, 204, 0.15);
  transform: perspective(1000px) translateZ(0) rotateX(0.5deg);
  transform-style: preserve-3d;
  border: 1px solid rgba(255, 255, 255, 0.7);
  transition: transform 0.3s ease;
}

.data-card::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.6) 0%, 
    rgba(255, 255, 255, 0.2) 50%, 
    rgba(255, 255, 255, 0) 100%);
  border-radius: 16px;
  pointer-events: none;
}

.data-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.data-num {
  font-size: 18px;
  font-weight: bold;
  color: #0052CC;
  margin-bottom: 4px;
}

.data-label {
  font-size: 12px;
  color: #666;
}

.data-divider {
  width: 1px;
  background-color: #eee;
  margin: 0 10px;
}

/* 功能入口 */
.function-section {
  margin-top: 24px;
}

.function-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
}

.function-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px;
}

.function-item {
  width: 25%;
  padding: 8px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.function-icon-bg {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.bg-blue {
  background-color: #e6f0ff;
}

.bg-orange {
  background-color: #fff2e6;
}

.bg-green {
  background-color: #e6fff0;
}

.bg-purple {
  background-color: #f0e6ff;
}

.function-icon {
  width: 28px;
  height: 28px;
}

.function-name {
  font-size: 14px;
  color: #333;
  text-align: center;
}

/* 操作按钮 */
.action-buttons {
  margin-top: 30px;
  display: flex;
  flex-direction: column;
}

.primary-btn {
  height: 50px;
  background: linear-gradient(135deg, #0052CC, #2684FF);
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  border-radius: 25px;
  margin-bottom: 12px;
  box-shadow: 0 6px 12px rgba(0, 82, 204, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1;
}

.secondary-btn {
  height: 50px;
  background-color: #fff;
  color: #0052CC;
  font-size: 16px;
  font-weight: 500;
  border-radius: 25px;
  border: 1px solid #0052CC;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1;
}

/* 下一步提示 */
.next-steps {
  margin-top: 30px;
  background-color: #fff;
  border-radius: 16px;
  padding: 20px;
  position: relative;
  box-shadow: 
    0 3px 6px rgba(0, 0, 0, 0.05),
    0 8px 16px rgba(0, 0, 0, 0.08),
    0 12px 20px -8px rgba(0, 82, 204, 0.15);
  transform: perspective(1000px) translateZ(0) rotateX(0.8deg);
  transform-style: preserve-3d;
  border: 1px solid rgba(255, 255, 255, 0.7);
  transition: transform 0.3s ease;
}

.next-steps::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.5) 0%, 
    rgba(255, 255, 255, 0.2) 50%, 
    rgba(255, 255, 255, 0) 100%);
  border-radius: 16px;
  pointer-events: none;
}

.next-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
}

.step-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.step-item:last-child {
  border-bottom: none;
}

.step-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.step-num {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: #0052CC;
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.step-text {
  font-size: 14px;
  color: #333;
}

.step-right {
  display: flex;
  align-items: center;
}

.go-text {
  font-size: 14px;
  color: #0052CC;
  margin-right: 4px;
}

.go-icon {
  width: 16px;
  height: 16px;
  transform: rotate(90deg);
}

/* 联系客服 */
.contact-section {
  margin-top: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.contact-text {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
}

.contact-btn {
  width: 140px;
  height: 36px;
  line-height: 36px;
  background: linear-gradient(135deg, #2FB8FF, #0076FF);
  color: #fff;
  font-size: 14px;
  border-radius: 18px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 8px rgba(0, 118, 255, 0.25);
  position: relative;
  overflow: hidden;
}

.contact-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  border-radius: 18px;
}

/* 会员信息 */
.membership-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20rpx;
}

.membership-badge {
  padding: 6rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  color: #fff;
  background: linear-gradient(135deg, #1677ff, #0052cc);
  box-shadow: 0 4rpx 8rpx rgba(22, 119, 255, 0.2);
}

.free-badge {
  background: linear-gradient(135deg, #52c41a, #389e0d);
}

.basic-badge {
  background: linear-gradient(135deg, #1677ff, #0052cc);
}

.premium-badge {
  background: linear-gradient(135deg, #fa8c16, #d46b08);
}

.deluxe-badge {
  background: linear-gradient(135deg, #722ed1, #531dab);
}

.test-badge {
  background: linear-gradient(135deg, #ffa500, #ff8c00);
}

.test-mark {
  position: absolute;
  top: -6px;
  right: -10px;
  background-color: #ff9800;
  color: #fff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  transform: scale(0.85);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 2;
}

.membership-date {
  margin-top: 8rpx;
  font-size: 22rpx;
  color: #666;
}

/* 认证好处卡片 */
.verify-benefits {
  margin-top: 20px;
  background-color: #fff;
  border-radius: 16px;
  padding: 20px;
  position: relative;
  box-shadow: 
    0 3px 6px rgba(0, 0, 0, 0.05),
    0 8px 16px rgba(0, 0, 0, 0.08),
    0 12px 20px -8px rgba(0, 82, 204, 0.15);
  transform: perspective(1000px) translateZ(0) rotateX(0.7deg);
  transform-style: preserve-3d;
  border: 1px solid rgba(255, 255, 255, 0.7);
  transition: transform 0.3s ease;
}

.verify-benefits::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.5) 0%, 
    rgba(255, 255, 255, 0.2) 50%, 
    rgba(255, 255, 255, 0) 100%);
  border-radius: 16px;
  pointer-events: none;
}

.verify-benefits-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
}

.verify-benefit-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.verify-benefits-list {
  margin-bottom: 16px;
}

.verify-benefit-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.benefit-dot {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background-color: #1677FF;
  margin-right: 8px;
}

.benefit-text {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}

.verify-now-btn {
  background: linear-gradient(135deg, #1677FF, #0052CC);
  color: #fff;
  font-size: 16px;
  height: 40px;
  border-radius: 20px;
  margin-top: 10px;
  box-shadow: 0 4px 8px rgba(0, 82, 204, 0.2);
}

/* 卡片基础立体效果 */
.card-3d-effect {
  position: relative;
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 
    0 3px 6px rgba(0, 0, 0, 0.05),
    0 8px 15px rgba(0, 0, 0, 0.1), 
    0 0 0 1px rgba(0, 0, 0, 0.02);
  overflow: hidden;
  transform: translateZ(0);
}

.card-3d-effect::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.6) 0%, 
    rgba(255, 255, 255, 0.2) 50%, 
    rgba(255, 255, 255, 0) 100%);
  z-index: 1;
  pointer-events: none;
}

.card-3d-effect::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  box-shadow: inset 0 1px 1px rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  z-index: 2;
  pointer-events: none;
}
</style> 