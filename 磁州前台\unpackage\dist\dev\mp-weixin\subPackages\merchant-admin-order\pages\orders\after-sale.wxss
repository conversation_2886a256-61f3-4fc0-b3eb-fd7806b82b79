
.after-sale-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}
.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}
.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}
.tab-container {
  display: flex;
  background-color: #fff;
  padding: 0 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}
.tab-item {
  padding: 16px 0;
  margin-right: 24px;
  position: relative;
  display: flex;
  align-items: center;
}
.tab-item.active {
  color: #1677FF;
  font-weight: 500;
}
.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background-color: #1677FF;
  border-radius: 3px 3px 0 0;
}
.tab-badge {
  min-width: 16px;
  height: 16px;
  border-radius: 8px;
  background-color: #ff4d4f;
  color: #fff;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 4px;
  padding: 0 4px;
}
.content-main {
  padding: 16px;
}
.refund-item, .complaint-item, .review-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}
.refund-header, .complaint-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}
.refund-number, .complaint-title {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}
.refund-status, .complaint-status {
  font-size: 14px;
  font-weight: 500;
}
.status-pending {
  color: #FF9800;
}
.status-approved, .status-processing {
  color: #2196F3;
}
.status-completed, .status-resolved {
  color: #4CAF50;
}
.status-rejected {
  color: #F44336;
}
.order-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 14px;
  color: #666;
}
.refund-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}
.customer-info {
  display: flex;
  align-items: center;
}
.customer-avatar {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  margin-right: 8px;
}
.customer-name {
  font-size: 14px;
  color: #333;
}
.refund-amount {
  display: flex;
  align-items: center;
}
.label {
  font-size: 14px;
  color: #666;
  margin-right: 4px;
}
.amount {
  font-size: 16px;
  color: #ff6a00;
  font-weight: 600;
}
.refund-reason {
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
}
.reason {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}
.action-buttons {
  display: flex;
  justify-content: flex-end;
}
.action-btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  margin-left: 12px;
  background-color: #f0f0f0;
  color: #333;
}
.action-btn.primary {
  background-color: #1677FF;
  color: #fff;
}
.action-btn.reject {
  background-color: #ff4d4f;
  color: #fff;
}
.complaint-content {
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
}
.complaint-text {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}
.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}
.rating {
  display: flex;
  align-items: center;
}
.star {
  color: #ddd;
  font-size: 16px;
}
.star.filled {
  color: #FFAB00;
}
.rating-text {
  font-size: 14px;
  color: #666;
  margin-left: 4px;
}
.review-content {
  margin-bottom: 12px;
}
.review-text {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8px;
}
.review-images {
  display: flex;
  flex-wrap: wrap;
}
.review-image {
  width: 80px;
  height: 80px;
  margin-right: 8px;
  margin-bottom: 8px;
  border-radius: 4px;
}
.review-footer {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
  margin-bottom: 12px;
}
.reply-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}
.merchant-reply {
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 4px;
}
.reply-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  margin-bottom: 4px;
  display: block;
}
.reply-content {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}
.reply-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  display: block;
  text-align: right;
}
.reply-form {
  display: flex;
}
.reply-input {
  flex: 1;
  height: 36px;
  border: 1px solid #ddd;
  border-radius: 18px;
  padding: 0 12px;
  font-size: 14px;
}
.reply-btn {
  width: 60px;
  height: 36px;
  background-color: #1677FF;
  color: #fff;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  font-size: 14px;
}
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 0;
}
.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}
.empty-text {
  font-size: 16px;
  color: #999;
}
