
.activity-showcase-container.data-v-91893c28 {
  min-height: 100vh;
  background-color: #f7f7f7;
  padding-bottom: env(safe-area-inset-bottom);
  position: relative;
}

/* 可滚动的弧形背景 */
.content-bg-scroll.data-v-91893c28 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 400rpx;
  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
  border-bottom-left-radius: 80rpx;
  border-bottom-right-radius: 80rpx;
  z-index: 1;
}

/* 自定义导航栏 */
.custom-navbar.data-v-91893c28 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 64px);
  width: 100%;
  z-index: 100;
  padding-top: var(--status-bar-height, 25px);
  box-sizing: border-box;
}
.navbar-content.data-v-91893c28 {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
  height: 64px;
    padding-left: 30rpx;
    padding-right: 30rpx;
    box-sizing: border-box;
}
.back-btn.data-v-91893c28 {
  width: 60rpx;
  height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  color: #ffffff;
    transition: all 0.2s ease;
  position: relative;
  z-index: 10;
}
.back-btn.data-v-91893c28:active {
      transform: scale(0.9);
      background: rgba(255, 255, 255, 0.3);
}
.navbar-title.data-v-91893c28 {
    font-size: 36rpx;
  font-weight: 600;
    color: #FFFFFF;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.navbar-right.data-v-91893c28 {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 60rpx; /* 限制宽度，确保不会有额外空间 */
}
.close-btn.data-v-91893c28 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  transition: all 0.2s ease;
}
.close-btn.data-v-91893c28:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.3);
}
.close-icon.data-v-91893c28 {
  font-size: 40rpx;
  font-weight: 300;
}
.back-icon.data-v-91893c28 {
  width: 50rpx;
  height: 50rpx;
}

/* 内容区域 */
.content-scroll.data-v-91893c28 {
  position: absolute;
  top: calc(var(--status-bar-height, 25px) + 64px);
  left: 0;
  right: 0;
  bottom: 100rpx;
  background-color: transparent;
  z-index: 2;
}

/* 轮播图 */
.banner-outer.data-v-91893c28 {
  position: relative;
  margin: 25rpx 30rpx 50rpx;
  border-radius: 30rpx;
  overflow: hidden;
  box-shadow: 0 25rpx 45rpx rgba(0, 0, 0, 0.35), 0 15rpx 25rpx rgba(0, 0, 0, 0.2);
  border: 12rpx solid #ffffff;
  transform: translateZ(0);
  z-index: 3;
  animation: float-91893c28 6s ease-in-out infinite;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  padding: 2rpx;
  background-color: rgba(255, 255, 255, 0.8);
}
@keyframes float-91893c28 {
0% { transform: translateY(0) translateZ(0);
}
50% { transform: translateY(-10rpx) translateZ(0);
}
100% { transform: translateY(0) translateZ(0);
}
}
.banner-swiper.data-v-91893c28 {
  width: 100%;
  height: 300rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
}
.banner-item.data-v-91893c28 {
    position: relative;
    width: 100%;
    height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FFFFFF;
}
.banner-image.data-v-91893c28 {
      width: 100%;
      height: 100%;
  border-radius: 16rpx;
  box-shadow: inset 0 0 15rpx rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
}
.banner-info.data-v-91893c28 {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      padding: 30rpx 25rpx;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0));
  border-radius: 0 0 16rpx 16rpx;
      -webkit-backdrop-filter: blur(10rpx);
              backdrop-filter: blur(10rpx);
}
.banner-title.data-v-91893c28 {
        font-size: 32rpx;
        font-weight: 700;
        color: #FFFFFF;
        margin-bottom: 8rpx;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
}
.banner-desc.data-v-91893c28 {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.9);
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

/* 白色内容区域 */
.white-content.data-v-91893c28 {
  background: #f8f9fc;
  position: relative;
  padding-top: 40rpx;
  padding-bottom: 20rpx;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  box-shadow: 0 -6rpx 20rpx rgba(0, 0, 0, 0.03);
  margin-top: -40rpx;
  z-index: 4;
}

/* 活动分类导航 */
.category-nav.data-v-91893c28 {
  display: flex;
  justify-content: space-around;
  padding: 15rpx 15rpx; /* 从25rpx减少到15rpx */
  background: rgba(255, 255, 255, 0.9);
  margin: 0 18rpx 20rpx; /* 修改上边距为0 */
  border-radius: 35rpx;
  box-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.1), 
              0 5rpx 15rpx rgba(0, 0, 0, 0.05),
              inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}
.category-nav.data-v-91893c28::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
    z-index: 0;
}
.category-item.data-v-91893c28 {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10rpx 0; /* 从15rpx减少到10rpx */
    width: 25%;
    transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    z-index: 1;
}
.category-item.data-v-91893c28:active {
      transform: scale(0.92);
}
.category-icon.data-v-91893c28 {
      width: 80rpx; /* 从90rpx减少到80rpx */
      height: 80rpx; /* 从90rpx减少到80rpx */
      border-radius: 24rpx; /* 修改：从16rpx增加到24rpx，使圆角更大 */
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 8rpx; /* 从12rpx减少到8rpx */
      box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.2),
                  inset 0 2rpx 3rpx rgba(255, 255, 255, 0.5);
      position: relative;
      overflow: hidden;
      color: #ffffff;
}
.category-icon .icon.data-v-91893c28 {
      /* 移除旋转效果 */
}
.category-icon.data-v-91893c28::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 50%;
        background: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
        border-radius: 24rpx 24rpx 0 0; /* 保持与正方形圆角一致，从16rpx更新为24rpx */
}
.icon-flash.data-v-91893c28 {
        background: linear-gradient(135deg, #FF453A, #FF2D55);
}
.icon-group.data-v-91893c28 {
        background: linear-gradient(135deg, #34C759, #30D158);
}
.icon-discount.data-v-91893c28 {
        background: linear-gradient(135deg, #5E5CE6, #5856D6);
}
.icon-coupon.data-v-91893c28 {
        background: linear-gradient(135deg, #FF9F0A, #FF9500);
}
.category-name.data-v-91893c28 {
      font-size: 24rpx; /* 从26rpx减少到24rpx */
      color: #333333;
      font-weight: 600;
      margin-top: 3rpx; /* 从5rpx减少到3rpx */
      text-shadow: 0 1rpx 0 rgba(255, 255, 255, 0.8);
}

/* 加载更多和到底了提示 */
.loading-more.data-v-91893c28, .no-more.data-v-91893c28 {
  text-align: center;
  padding: 20rpx 0;
  font-size: 24rpx;
  color: #8E8E93;
  display: flex;
  align-items: center;
  justify-content: center;
}
.loading-more.data-v-91893c28::before {
    content: '';
    width: 28rpx;
    height: 28rpx;
    border: 3rpx solid #8E8E93;
    border-top-color: transparent;
    border-radius: 50%;
    margin-right: 8rpx;
    animation: loading-91893c28 0.8s linear infinite;
}
@keyframes loading-91893c28 {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}

/* 底部安全区域 */
.safe-area-bottom.data-v-91893c28 {
  height: env(safe-area-inset-bottom);
}

/* 底部导航栏 */
.tabbar.data-v-91893c28 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #FFFFFF;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 99;
  border-top: 1rpx solid #EEEEEE;
}
.tabbar-item.data-v-91893c28 {
  flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
  padding: 6px 0;
  box-sizing: border-box;
    position: relative;
}
.tabbar-item.data-v-91893c28:active {
      transform: scale(0.9);
}
.tabbar-item.active .tab-icon.data-v-91893c28 {
        transform: translateY(-5rpx);
}
.tabbar-item.active .tabbar-text.data-v-91893c28 {
  color: #FF3B69;
        font-weight: 600;
        transform: translateY(-2rpx);
}
.tab-icon.data-v-91893c28 {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  color: #999999;
      display: flex;
      justify-content: center;
      align-items: center;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 首页图标 */
.tab-icon.home.data-v-91893c28 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E");
}
.tabbar-item.active[data-tab="home"] .tab-icon.home.data-v-91893c28 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E");
}

/* 发现图标 */
.tab-icon.discover.data-v-91893c28 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5.5-2.5l7.51-3.49L17.5 6.5 9.99 9.99 6.5 17.5zm5.5-6.6c.61 0 1.1.49 1.1 1.1s-.49 1.1-1.1 1.1-1.1-.49-1.1-1.1.49-1.1 1.1-1.1z'/%3E%3C/svg%3E");
}
.tabbar-item.active[data-tab="discover"] .tab-icon.discover.data-v-91893c28 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5.5-2.5l7.51-3.49L17.5 6.5 9.99 9.99 6.5 17.5zm5.5-6.6c.61 0 1.1.49 1.1 1.1s-.49 1.1-1.1 1.1-1.1-.49-1.1-1.1.49-1.1 1.1-1.1z'/%3E%3C/svg%3E");
}

/* 分销图标 */
.tab-icon.distribution.data-v-91893c28 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E");
}
.tabbar-item.active[data-tab="distribution"] .tab-icon.distribution.data-v-91893c28 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E");
}

/* 消息图标 */
.tab-icon.message.data-v-91893c28 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H5.17L4 17.17V4h16v12z'/%3E%3C/svg%3E");
}
.tabbar-item.active[data-tab="message"] .tab-icon.message.data-v-91893c28 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E");
}

/* 我的图标 */
.tab-icon.user.data-v-91893c28 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}
.tabbar-item.active[data-tab="my"] .tab-icon.user.data-v-91893c28 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}
.tabbar-item.active .tab-icon.data-v-91893c28 {
  filter: drop-shadow(0 1px 2px rgba(255, 59, 105, 0.3));
}
.badge.data-v-91893c28 {
        position: absolute;
        top: -8rpx;
        right: -12rpx;
        min-width: 32rpx;
        height: 32rpx;
        border-radius: 16rpx;
        background: linear-gradient(135deg, #FF453A, #FF2D55);
        color: #FFFFFF;
        font-size: 18rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 6rpx;
        box-sizing: border-box;
        font-weight: 600;
        box-shadow: 0 2rpx 6rpx rgba(255, 45, 85, 0.3);
        border: 1rpx solid rgba(255, 255, 255, 0.8);
        transform: scale(0.9);
}
.tabbar-text.data-v-91893c28 {
      font-size: 22rpx;
      color: #8E8E93;
      margin-top: 2rpx;
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}
.tabbar-item.data-v-91893c28::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%) scaleX(0);
      width: 30rpx;
      height: 4rpx;
  background: #FF3B69;
      border-radius: 2rpx;
      transition: transform 0.3s ease;
}
.tabbar-item.active.data-v-91893c28::after {
      transform: translateX(-50%) scaleX(1);
}
