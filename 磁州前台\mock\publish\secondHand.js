// 二手闲置详情模拟数据
export const secondHandDetail = {
  id: 'second-hand-001',
  title: '9成新iPhone 13 128G 午夜蓝',
  price: 3800,
  originalPrice: 5999, // 原价
  category: '手机数码',
  subcategory: '手机',
  brand: 'Apple',
  model: 'iPhone 13',
  purchaseTime: '2022-10', // 购买时间
  usageTime: '1年5个月', // 使用时长
  condition: '9成新',
  warranty: '还有6个月保修期',
  accessories: ['原装充电器', '原装数据线', '保护壳', '钢化膜'],
  description: '因换新机出售，iPhone 13 128G 午夜蓝色，无拆修，无进水，电池健康度89%，面容ID正常，所有功能正常，成色新，附赠原装充电器、数据线、保护壳和钢化膜。',
  contact: {
    name: '王先生',
    phone: '132****4567',
    wechat: 'wangxiansheng123'
  },
  deliveryMethod: ['自提', '邮寄'], // 交付方式
  paymentMethod: ['微信', '支付宝', '现金'], // 支付方式
  negotiable: true, // 是否可议价
  images: [
    '/static/images/secondhand/phone-1.jpg',
    '/static/images/secondhand/phone-2.jpg',
    '/static/images/secondhand/phone-3.jpg',
    '/static/images/secondhand/phone-4.jpg'
  ],
  location: {
    latitude: 36.314736,
    longitude: 114.711234,
    address: '磁县城区幸福路789号'
  },
  publishTime: '2024-03-13 16:20:00',
  views: 165,
  likes: 12,
  collections: 7,
  status: 'active' // active, sold, expired
};

// 相关二手闲置模拟数据
export const relatedSecondHands = [
  {
    id: 'second-hand-002',
    title: '95新华为P40 Pro 256G 亮黑色',
    price: 2200,
    originalPrice: 5988,
    category: '手机数码',
    subcategory: '手机',
    condition: '9.5成新',
    image: '/static/images/secondhand/phone-5.jpg'
  },
  {
    id: 'second-hand-003',
    title: '全新未拆封小米13 Ultra 512G',
    price: 5500,
    originalPrice: 6999,
    category: '手机数码',
    subcategory: '手机',
    condition: '全新',
    image: '/static/images/secondhand/phone-6.jpg'
  },
  {
    id: 'second-hand-004',
    title: '8成新iPad Pro 11英寸 2021款 256G',
    price: 4200,
    originalPrice: 6799,
    category: '手机数码',
    subcategory: '平板电脑',
    condition: '8成新',
    image: '/static/images/secondhand/tablet-1.jpg'
  }
];

// 获取二手闲置详情的API函数
export const fetchSecondHandDetail = (id) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(secondHandDetail);
    }, 500);
  });
};

// 获取相关二手闲置的API函数
export const fetchRelatedSecondHands = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(relatedSecondHands);
    }, 500);
  });
}; 