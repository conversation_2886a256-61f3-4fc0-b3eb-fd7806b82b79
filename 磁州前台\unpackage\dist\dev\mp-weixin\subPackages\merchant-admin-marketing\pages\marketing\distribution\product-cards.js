"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
if (!Array) {
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_path = common_vendor.resolveComponent("path");
  (_component_circle + _component_polyline + _component_svg + _component_path)();
}
const _sfc_main = {
  __name: "product-cards",
  setup(__props) {
    const shopInfo = common_vendor.reactive({
      name: "磁州同城生活",
      logo: "/static/images/logo.png"
    });
    const products = common_vendor.reactive([
      {
        id: 1,
        name: "Apple iPhone 14 Pro Max 256GB 暗夜紫",
        image: "/static/images/products/iphone.jpg",
        price: 8999,
        originalPrice: 9599,
        commission: 450,
        sold: 86,
        stock: 100
      },
      {
        id: 2,
        name: "Apple Watch Series 8 GPS 45mm 午夜色",
        image: "/static/images/products/watch.jpg",
        price: 3299,
        originalPrice: 3499,
        commission: 165,
        sold: 72,
        stock: 100
      },
      {
        id: 3,
        name: "Apple AirPods Pro 2 主动降噪无线耳机",
        image: "/static/images/products/airpods.jpg",
        price: 1999,
        originalPrice: 2199,
        commission: 100,
        sold: 94,
        stock: 100
      },
      {
        id: 4,
        name: "Apple MacBook Air M2 芯片 13.6英寸",
        image: "/static/images/products/macbook.jpg",
        price: 7999,
        originalPrice: 8499,
        commission: 400,
        sold: 58,
        stock: 100
      },
      {
        id: 5,
        name: "Apple iPad Air 5 10.9英寸 Wi-Fi版",
        image: "/static/images/products/ipad.jpg",
        price: 4799,
        originalPrice: 4999,
        commission: 240,
        sold: 65,
        stock: 100
      }
    ]);
    const templates = [
      { name: "横版卡片", value: "horizontal" },
      { name: "竖版卡片", value: "vertical" },
      { name: "简约卡片", value: "minimal" }
    ];
    const cardStyles = [
      { name: "紫色", value: "purple", color: "#6B0FBE" },
      { name: "蓝色", value: "blue", color: "#007AFF" },
      { name: "绿色", value: "green", color: "#34C759" },
      { name: "红色", value: "red", color: "#FF3B30" },
      { name: "橙色", value: "orange", color: "#FF9500" }
    ];
    const selectedProduct = common_vendor.ref(null);
    const currentTemplate = common_vendor.ref(templates[0]);
    const currentStyle = common_vendor.ref("purple");
    const showOriginalPrice = common_vendor.ref(true);
    const showPromoTag = common_vendor.ref(false);
    const promoTagText = common_vendor.ref("限时优惠");
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const showHistory = () => {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/distribution/product-cards/history"
      });
    };
    const showSearch = () => {
      common_vendor.index.showToast({
        title: "搜索功能开发中",
        icon: "none"
      });
    };
    const selectProduct = (product) => {
      selectedProduct.value = product;
    };
    const showTemplates = () => {
      common_vendor.index.showActionSheet({
        itemList: templates.map((item) => item.name),
        success: (res) => {
          currentTemplate.value = templates[res.tapIndex];
        }
      });
    };
    const selectStyle = (style) => {
      currentStyle.value = style;
    };
    const toggleOriginalPrice = (e) => {
      showOriginalPrice.value = e.detail.value;
    };
    const togglePromoTag = (e) => {
      showPromoTag.value = e.detail.value;
    };
    const generateCard = () => {
      common_vendor.index.showLoading({
        title: "生成中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showModal({
          title: "卡片生成成功",
          content: "推广卡片已生成，您可以保存到相册或直接分享",
          confirmText: "分享",
          cancelText: "保存",
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.showShareMenu({
                withShareTicket: true,
                menus: ["shareAppMessage", "shareTimeline"]
              });
            } else {
              common_vendor.index.showToast({
                title: "已保存到相册",
                icon: "success"
              });
            }
          }
        });
      }, 1500);
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(goBack),
        b: common_vendor.p({
          cx: "12",
          cy: "12",
          r: "10",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        c: common_vendor.p({
          points: "12 6 12 12 16 14",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        d: common_vendor.p({
          width: "20",
          height: "20",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        e: common_vendor.o(showHistory),
        f: common_vendor.p({
          d: "M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z",
          stroke: "#6B0FBE",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        g: common_vendor.p({
          d: "M21 21L16.65 16.65",
          stroke: "#6B0FBE",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        h: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        i: common_vendor.o(showSearch),
        j: common_vendor.f(products, (product, index, i0) => {
          return common_vendor.e({
            a: selectedProduct.value === product
          }, selectedProduct.value === product ? {
            b: "c9d53024-7-" + i0 + "," + ("c9d53024-6-" + i0),
            c: common_vendor.p({
              d: "M20 6L9 17L4 12",
              stroke: "white",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            d: "c9d53024-6-" + i0,
            e: common_vendor.p({
              width: "16",
              height: "16",
              viewBox: "0 0 24 24",
              fill: "none",
              xmlns: "http://www.w3.org/2000/svg"
            })
          } : {}, {
            f: product.image,
            g: common_vendor.t(product.name),
            h: common_vendor.t(product.price),
            i: common_vendor.t(product.commission),
            j: common_vendor.t(product.sold),
            k: common_vendor.t(product.stock),
            l: index,
            m: selectedProduct.value === product ? 1 : "",
            n: common_vendor.o(($event) => selectProduct(product), index)
          });
        }),
        k: selectedProduct.value
      }, selectedProduct.value ? common_vendor.e({
        l: common_vendor.t(currentTemplate.value.name),
        m: common_vendor.o(showTemplates),
        n: selectedProduct.value.image,
        o: common_vendor.t(selectedProduct.value.name),
        p: common_vendor.t(selectedProduct.value.price),
        q: selectedProduct.value.originalPrice
      }, selectedProduct.value.originalPrice ? {
        r: common_vendor.t(selectedProduct.value.originalPrice)
      } : {}, {
        s: shopInfo.logo,
        t: common_vendor.t(shopInfo.name),
        v: common_vendor.n(currentTemplate.value.value),
        w: common_vendor.f(cardStyles, (style, index, i0) => {
          return {
            a: style.color,
            b: common_vendor.t(style.name),
            c: index,
            d: currentStyle.value === style.value ? 1 : "",
            e: common_vendor.o(($event) => selectStyle(style.value), index)
          };
        }),
        x: showOriginalPrice.value,
        y: common_vendor.o(toggleOriginalPrice),
        z: showPromoTag.value,
        A: common_vendor.o(togglePromoTag),
        B: showPromoTag.value
      }, showPromoTag.value ? {
        C: promoTagText.value,
        D: common_vendor.o(($event) => promoTagText.value = $event.detail.value)
      } : {}, {
        E: common_vendor.o(generateCard)
      }) : {}, {
        F: !selectedProduct.value
      }, !selectedProduct.value ? {
        G: common_assets._imports_1$40
      } : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/distribution/product-cards.js.map
