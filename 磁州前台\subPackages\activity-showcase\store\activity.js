/**
 * 磁州前台活动状态管理
 * 使用Pinia进行状态管理，提供活动数据的统一管理
 */

import { defineStore } from 'pinia'
import activityApi from '../api/activity'

export const useActivityStore = defineStore('activity', {
  state: () => ({
    // 活动列表
    activities: [],
    
    // 推荐活动
    recommendedActivities: [],
    
    // 热门活动
    popularActivities: [],
    
    // 当前活动详情
    currentActivity: null,
    
    // 用户参与的活动
    userActivities: [],
    
    // 活动评论
    activityComments: {},
    
    // 搜索结果
    searchResults: [],
    
    // 活动标签
    tags: [],
    
    // 加载状态
    loading: {
      list: false,
      detail: false,
      recommended: false,
      popular: false,
      search: false,
      comments: false,
      userActivities: false
    },
    
    // 分页信息
    pagination: {
      list: {
        page: 1,
        size: 10,
        total: 0,
        hasMore: true
      },
      search: {
        page: 1,
        size: 10,
        total: 0,
        hasMore: true
      },
      userActivities: {
        page: 1,
        size: 10,
        total: 0,
        hasMore: true
      }
    },
    
    // 筛选条件
    filters: {
      type: '',
      tags: [],
      isRecommended: false
    },
    
    // 搜索关键词
    searchKeyword: '',
    
    // 用户交互状态
    userInteractions: {
      joinedActivities: new Set(),
      likedActivities: new Set(),
      viewedActivities: new Set()
    }
  }),

  getters: {
    // 获取进行中的活动
    activeActivities: (state) => {
      return state.activities.filter(activity => activity.status === 'active')
    },
    
    // 获取已结束的活动
    endedActivities: (state) => {
      return state.activities.filter(activity => activity.status === 'ended')
    },
    
    // 根据类型筛选活动
    getActivitiesByType: (state) => (type) => {
      return state.activities.filter(activity => activity.type === type)
    },
    
    // 根据标签筛选活动
    getActivitiesByTags: (state) => (tags) => {
      if (!tags || tags.length === 0) return state.activities
      return state.activities.filter(activity => 
        activity.tags && activity.tags.some(tag => tags.includes(tag))
      )
    },
    
    // 获取用户已参与的活动ID列表
    joinedActivityIds: (state) => {
      return Array.from(state.userInteractions.joinedActivities)
    },
    
    // 获取用户已点赞的活动ID列表
    likedActivityIds: (state) => {
      return Array.from(state.userInteractions.likedActivities)
    },
    
    // 检查用户是否已参与某个活动
    isActivityJoined: (state) => (activityId) => {
      return state.userInteractions.joinedActivities.has(activityId)
    },
    
    // 检查用户是否已点赞某个活动
    isActivityLiked: (state) => (activityId) => {
      return state.userInteractions.likedActivities.has(activityId)
    },
    
    // 获取活动评论
    getActivityComments: (state) => (activityId) => {
      return state.activityComments[activityId] || []
    }
  },

  actions: {
    // 获取活动列表
    async fetchActivities(params = {}, loadMore = false) {
      try {
        if (!loadMore) {
          this.loading.list = true
          this.pagination.list.page = 1
        }
        
        const requestParams = {
          ...params,
          page: this.pagination.list.page,
          size: this.pagination.list.size,
          ...this.filters
        }
        
        const response = await activityApi.getList(requestParams)
        
        if (loadMore) {
          this.activities.push(...response.data)
        } else {
          this.activities = response.data
        }
        
        this.pagination.list.total = response.total
        this.pagination.list.hasMore = response.data.length === this.pagination.list.size
        
        if (loadMore) {
          this.pagination.list.page++
        }
        
      } catch (error) {
        console.error('获取活动列表失败:', error)
        throw error
      } finally {
        this.loading.list = false
      }
    },

    // 获取推荐活动
    async fetchRecommendedActivities(limit = 5) {
      try {
        this.loading.recommended = true
        const response = await activityApi.getRecommended(limit)
        this.recommendedActivities = response
      } catch (error) {
        console.error('获取推荐活动失败:', error)
        throw error
      } finally {
        this.loading.recommended = false
      }
    },

    // 获取热门活动
    async fetchPopularActivities(limit = 5) {
      try {
        this.loading.popular = true
        const response = await activityApi.getPopular(limit)
        this.popularActivities = response
      } catch (error) {
        console.error('获取热门活动失败:', error)
        throw error
      } finally {
        this.loading.popular = false
      }
    },

    // 获取活动详情
    async fetchActivityDetail(id) {
      try {
        this.loading.detail = true
        const response = await activityApi.getDetail(id)
        this.currentActivity = response
        
        // 记录为已浏览
        this.userInteractions.viewedActivities.add(id)
        
        // 检查用户参与状态
        await this.checkUserInteractionStatus(id)
        
        return response
      } catch (error) {
        console.error('获取活动详情失败:', error)
        throw error
      } finally {
        this.loading.detail = false
      }
    },

    // 搜索活动
    async searchActivities(keyword, params = {}, loadMore = false) {
      try {
        if (!loadMore) {
          this.loading.search = true
          this.pagination.search.page = 1
          this.searchKeyword = keyword
        }
        
        const requestParams = {
          ...params,
          page: this.pagination.search.page,
          size: this.pagination.search.size
        }
        
        const response = await activityApi.search(keyword, requestParams)
        
        if (loadMore) {
          this.searchResults.push(...response.data)
        } else {
          this.searchResults = response.data
        }
        
        this.pagination.search.total = response.total
        this.pagination.search.hasMore = response.data.length === this.pagination.search.size
        
        if (loadMore) {
          this.pagination.search.page++
        }
        
      } catch (error) {
        console.error('搜索活动失败:', error)
        throw error
      } finally {
        this.loading.search = false
      }
    },

    // 参与活动
    async joinActivity(id, data = {}) {
      try {
        await activityApi.join(id, data)
        
        // 更新用户交互状态
        this.userInteractions.joinedActivities.add(id)
        
        // 更新活动参与人数
        this.updateActivityParticipantCount(id, 1)
        
        // 更新用户活动列表
        await this.fetchUserActivities()
        
      } catch (error) {
        console.error('参与活动失败:', error)
        throw error
      }
    },

    // 取消参与活动
    async leaveActivity(id) {
      try {
        await activityApi.leave(id)
        
        // 更新用户交互状态
        this.userInteractions.joinedActivities.delete(id)
        
        // 更新活动参与人数
        this.updateActivityParticipantCount(id, -1)
        
        // 更新用户活动列表
        await this.fetchUserActivities()
        
      } catch (error) {
        console.error('取消参与失败:', error)
        throw error
      }
    },

    // 分享活动
    async shareActivity(id) {
      try {
        await activityApi.share(id)
        
        // 更新活动分享次数
        this.updateActivityShareCount(id, 1)
        
      } catch (error) {
        console.error('分享活动失败:', error)
        throw error
      }
    },

    // 点赞活动
    async likeActivity(id) {
      try {
        await activityApi.like(id)
        
        // 更新用户交互状态
        this.userInteractions.likedActivities.add(id)
        
      } catch (error) {
        console.error('点赞失败:', error)
        throw error
      }
    },

    // 取消点赞活动
    async unlikeActivity(id) {
      try {
        await activityApi.unlike(id)
        
        // 更新用户交互状态
        this.userInteractions.likedActivities.delete(id)
        
      } catch (error) {
        console.error('取消点赞失败:', error)
        throw error
      }
    },

    // 评论活动
    async commentActivity(id, content) {
      try {
        await activityApi.comment(id, content)
        
        // 刷新评论列表
        await this.fetchActivityComments(id)
        
        // 更新活动评论数
        this.updateActivityCommentCount(id, 1)
        
      } catch (error) {
        console.error('评论失败:', error)
        throw error
      }
    },

    // 获取活动评论
    async fetchActivityComments(id, params = {}) {
      try {
        this.loading.comments = true
        const response = await activityApi.getComments(id, params)
        this.activityComments[id] = response.data
        return response
      } catch (error) {
        console.error('获取评论失败:', error)
        throw error
      } finally {
        this.loading.comments = false
      }
    },

    // 获取用户参与的活动
    async fetchUserActivities(params = {}, loadMore = false) {
      try {
        if (!loadMore) {
          this.loading.userActivities = true
          this.pagination.userActivities.page = 1
        }
        
        const requestParams = {
          ...params,
          page: this.pagination.userActivities.page,
          size: this.pagination.userActivities.size
        }
        
        const response = await activityApi.getUserActivities(requestParams)
        
        if (loadMore) {
          this.userActivities.push(...response.data)
        } else {
          this.userActivities = response.data
        }
        
        this.pagination.userActivities.total = response.total
        this.pagination.userActivities.hasMore = response.data.length === this.pagination.userActivities.size
        
        if (loadMore) {
          this.pagination.userActivities.page++
        }
        
        // 更新用户交互状态
        response.data.forEach(activity => {
          this.userInteractions.joinedActivities.add(activity.id)
        })
        
      } catch (error) {
        console.error('获取用户活动失败:', error)
        throw error
      } finally {
        this.loading.userActivities = false
      }
    },

    // 获取活动标签
    async fetchTags() {
      try {
        const response = await activityApi.getTags()
        this.tags = response
      } catch (error) {
        console.error('获取标签失败:', error)
        throw error
      }
    },

    // 检查用户交互状态
    async checkUserInteractionStatus(activityId) {
      try {
        const joinStatus = await activityApi.checkJoinStatus(activityId)
        if (joinStatus.isJoined) {
          this.userInteractions.joinedActivities.add(activityId)
        }
      } catch (error) {
        console.error('检查用户状态失败:', error)
      }
    },

    // 更新筛选条件
    updateFilters(filters) {
      this.filters = { ...this.filters, ...filters }
    },

    // 清空搜索结果
    clearSearchResults() {
      this.searchResults = []
      this.searchKeyword = ''
      this.pagination.search.page = 1
      this.pagination.search.hasMore = true
    },

    // 更新活动参与人数
    updateActivityParticipantCount(activityId, delta) {
      const updateActivity = (activity) => {
        if (activity.id === activityId) {
          activity.participantCount = (activity.participantCount || 0) + delta
        }
      }
      
      this.activities.forEach(updateActivity)
      this.recommendedActivities.forEach(updateActivity)
      this.popularActivities.forEach(updateActivity)
      this.searchResults.forEach(updateActivity)
      
      if (this.currentActivity && this.currentActivity.id === activityId) {
        this.currentActivity.participantCount = (this.currentActivity.participantCount || 0) + delta
      }
    },

    // 更新活动分享次数
    updateActivityShareCount(activityId, delta) {
      const updateActivity = (activity) => {
        if (activity.id === activityId) {
          activity.shareCount = (activity.shareCount || 0) + delta
        }
      }
      
      this.activities.forEach(updateActivity)
      this.recommendedActivities.forEach(updateActivity)
      this.popularActivities.forEach(updateActivity)
      this.searchResults.forEach(updateActivity)
      
      if (this.currentActivity && this.currentActivity.id === activityId) {
        this.currentActivity.shareCount = (this.currentActivity.shareCount || 0) + delta
      }
    },

    // 更新活动评论数
    updateActivityCommentCount(activityId, delta) {
      const updateActivity = (activity) => {
        if (activity.id === activityId) {
          activity.commentCount = (activity.commentCount || 0) + delta
        }
      }
      
      this.activities.forEach(updateActivity)
      this.recommendedActivities.forEach(updateActivity)
      this.popularActivities.forEach(updateActivity)
      this.searchResults.forEach(updateActivity)
      
      if (this.currentActivity && this.currentActivity.id === activityId) {
        this.currentActivity.commentCount = (this.currentActivity.commentCount || 0) + delta
      }
    },

    // 重置状态
    reset() {
      this.activities = []
      this.recommendedActivities = []
      this.popularActivities = []
      this.currentActivity = null
      this.userActivities = []
      this.activityComments = {}
      this.searchResults = []
      this.searchKeyword = ''
      
      // 重置分页
      Object.keys(this.pagination).forEach(key => {
        this.pagination[key].page = 1
        this.pagination[key].hasMore = true
      })
      
      // 重置筛选条件
      this.filters = {
        type: '',
        tags: [],
        isRecommended: false
      }
    }
  },

  // 持久化配置
  persist: {
    key: 'activity-store',
    storage: {
      getItem: (key) => uni.getStorageSync(key),
      setItem: (key, value) => uni.setStorageSync(key, value),
      removeItem: (key) => uni.removeStorageSync(key)
    },
    paths: ['userInteractions', 'filters'] // 只持久化用户交互状态和筛选条件
  }
})

export default useActivityStore
