<template>
  <view class="store-culture-container">
    <!-- 顶部导航栏 -->
    <view class="header-area">
      <!-- 顶部安全区域 -->
      <view class="safe-area-top"></view>
      
      <!-- 自定义导航栏 -->
      <view class="custom-navbar">
        <view class="navbar-left" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="back-icon" style="filter: brightness(0) invert(1);"></image>
        </view>
        <view class="navbar-title">商家故事</view>
        <view class="navbar-right">
          <!-- 占位元素保持导航栏平衡 -->
        </view>
      </view>
    </view>
    
    <!-- 背景装饰 -->
    <view class="bg-decoration bg-circle-1"></view>
    <view class="bg-decoration bg-circle-2"></view>
    <view class="bg-decoration bg-circle-3"></view>
    
    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 预览区域 -->
      <view class="preview-section">
        <view class="section-header">
          <text class="section-title">展示效果</text>
          <view class="preview-action" @click="previewStory">
            <text class="action-text">预览效果</text>
          </view>
        </view>
        
        <view class="culture-preview">
          <view class="preview-item">
            <image src="/static/images/culture-preview.jpg" mode="widthFix" class="preview-image"></image>
          </view>
          
          <view class="preview-tips">
            <text class="tips-icon">💡</text>
            <text class="tips-text">图文并茂的商家故事能够更好地展示店铺特色和文化，提升顾客信任度</text>
          </view>
        </view>
      </view>
      
      <!-- 商家故事编辑 -->
      <view class="culture-section">
        <view class="section-header">
          <text class="section-title">商家故事</text>
        </view>
        
        <view class="form-item">
          <text class="form-label required">标题</text>
          <input type="text" class="form-input" v-model="formData.title" placeholder="请输入标题（20字以内）" maxlength="20" />
          <text class="input-counter">{{formData.title.length}}/20</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">副标题</text>
          <input type="text" class="form-input" v-model="formData.subtitle" placeholder="请输入副标题（30字以内）" maxlength="30" />
          <text class="input-counter">{{formData.subtitle.length}}/30</text>
        </view>
        
        <view class="form-item">
          <text class="form-label required">封面图片</text>
          
          <view class="upload-area" v-if="!formData.coverImage" @click="uploadImage('cover')">
            <view class="upload-icon-wrapper">
              <text class="upload-icon">+</text>
            </view>
            <text class="upload-text">上传封面图片</text>
          </view>
          
          <view class="image-preview-area" v-else>
            <image :src="formData.coverImage" mode="aspectFill" class="preview-cover"></image>
            <view class="image-actions">
              <view class="image-action-btn" @click="uploadImage('cover')">更换</view>
              <view class="image-action-btn delete" @click="removeImage('cover')">删除</view>
            </view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label required">故事内容</text>
          
          <view class="rich-editor">
            <textarea class="form-textarea" v-model="formData.content" placeholder="请输入商家故事内容，可以讲述品牌创立过程、经营理念、特色亮点等" maxlength="2000" />
            <text class="input-counter">{{formData.content.length}}/2000</text>
            
            <view class="editor-tools">
              <view class="tool-btn" @click="uploadImage('content')">
                <text class="tool-icon">🖼️</text>
                <text class="tool-text">插入图片</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 内容图片预览 -->
        <view class="content-images" v-if="formData.contentImages.length > 0">
          <view class="image-item" v-for="(image, index) in formData.contentImages" :key="index">
            <image :src="image" mode="aspectFill" class="content-image"></image>
            <view class="image-delete" @click="removeContentImage(index)">×</view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">创始人信息</text>
          
          <view class="founder-info">
            <view class="founder-avatar-area">
              <view class="upload-avatar" v-if="!formData.founderAvatar" @click="uploadImage('founder')">
                <text class="upload-icon">+</text>
              </view>
              <image v-else :src="formData.founderAvatar" mode="aspectFill" class="founder-avatar" @click="uploadImage('founder')"></image>
            </view>
            
            <view class="founder-details">
              <input type="text" class="form-input founder-name" v-model="formData.founderName" placeholder="创始人姓名" maxlength="10" />
              <input type="text" class="form-input founder-title" v-model="formData.founderTitle" placeholder="职位/头衔" maxlength="15" />
            </view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">联系方式展示</text>
          
          <checkbox-group @change="onContactChange">
            <label class="contact-option" v-for="(option, index) in contactOptions" :key="index">
              <checkbox :value="option.value" :checked="formData.contactDisplay.includes(option.value)" color="#0A84FF" />
              <text class="option-label">{{option.label}}</text>
            </label>
          </checkbox-group>
        </view>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
    
    <!-- 底部保存按钮 -->
    <view class="footer">
      <view class="safe-area-bottom-footer"></view>
      <view class="footer-content">
        <button class="save-btn" @click="saveStoreStory">保存修改</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        title: '磁州小吃美食店的品牌故事',
        subtitle: '一家专注本地特色美食30年的老字号',
        coverImage: '/static/images/culture-cover.jpg',
        content: '磁州小吃美食店创立于1992年，由张师傅一家人创办。\n\n作为磁县本地人，张师傅从小就对本地美食有着浓厚的兴趣，尤其对小酥肉、磁州包子等传统特色小吃的制作工艺钻研颇深。\n\n创业初期，店铺仅有20平米，主要经营早点和小吃。凭借着地道的口味和优质的服务，小店逐渐有了固定客源。\n\n2008年，为适应市场需求，张师傅带领第二代传承人对店面进行了全面升级，扩大了营业面积，丰富了产品种类，将传统制作工艺与现代餐饮理念相结合。\n\n这些年来，我们始终坚持"匠心制作，用心服务"的经营理念，严格把控食材质量，认真对待每一道菜品，希望能将磁州特色美食文化传承和发扬光大。',
        contentImages: [
          '/static/images/content-image-1.jpg',
          '/static/images/content-image-2.jpg'
        ],
        founderAvatar: '/static/images/founder.jpg',
        founderName: '张大厨',
        founderTitle: '创始人/主厨',
        contactDisplay: ['phone', 'wechat']
      },
      contactOptions: [
        { label: '店铺电话', value: 'phone' },
        { label: '微信号', value: 'wechat' },
        { label: '电子邮箱', value: 'email' },
        { label: '社交媒体', value: 'social' }
      ],
      statusBarHeight: 20
    }
  },
  onLoad() {
    // 页面加载完成后的处理
    this.setStatusBarHeight();
  },
  methods: {
    // 设置状态栏高度
    setStatusBarHeight() {
      // 获取系统信息设置状态栏高度
      uni.getSystemInfo({
        success: (res) => {
          this.statusBarHeight = res.statusBarHeight;
          // 将状态栏高度设置为CSS变量
          if (typeof document !== 'undefined') {
            document.documentElement.style.setProperty('--status-bar-height', `${this.statusBarHeight}px`);
          }
        }
      });
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 预览商家故事
    previewStory() {
      // 验证必填项
      if (!this.formData.title) {
        uni.showToast({
          title: '请填写标题',
          icon: 'none'
        });
        return;
      }
      
      if (!this.formData.coverImage) {
        uni.showToast({
          title: '请上传封面图片',
          icon: 'none'
        });
        return;
      }
      
      if (!this.formData.content) {
        uni.showToast({
          title: '请填写故事内容',
          icon: 'none'
        });
        return;
      }
      
      uni.showToast({
        title: '预览功能开发中',
        icon: 'none'
      });
    },
    
    // 上传图片
    uploadImage(type) {
      uni.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          
          // 检查文件大小
          const maxSize = 5 * 1024 * 1024; // 5MB
          
          uni.getFileInfo({
            filePath: tempFilePath,
            success: (fileInfo) => {
              if (fileInfo.size > maxSize) {
                uni.showToast({
                  title: '文件大小不能超过5MB',
                  icon: 'none'
                });
                return;
              }
              
              // 显示加载中
              uni.showLoading({
                title: '上传中...',
                mask: true
              });
              
              // 模拟上传过程
              setTimeout(() => {
                // 根据类型设置不同的属性
                if (type === 'cover') {
                  this.formData.coverImage = tempFilePath;
                } else if (type === 'content') {
                  this.formData.contentImages.push(tempFilePath);
                } else if (type === 'founder') {
                  this.formData.founderAvatar = tempFilePath;
                }
                
                uni.hideLoading();
                uni.showToast({
                  title: '上传成功',
                  icon: 'success'
                });
              }, 1000);
            }
          });
        }
      });
    },
    
    // 删除图片
    removeImage(type) {
      if (type === 'cover') {
        this.formData.coverImage = '';
      } else if (type === 'founder') {
        this.formData.founderAvatar = '';
      }
      
      uni.showToast({
        title: '已删除',
        icon: 'success'
      });
    },
    
    // 删除内容图片
    removeContentImage(index) {
      this.formData.contentImages.splice(index, 1);
      
      uni.showToast({
        title: '已删除',
        icon: 'success'
      });
    },
    
    // 联系方式选择变更
    onContactChange(e) {
      this.formData.contactDisplay = e.detail.value;
    },
    
    // 保存商家故事
    saveStoreStory() {
      // 验证必填项
      if (!this.formData.title) {
        uni.showToast({
          title: '请填写标题',
          icon: 'none'
        });
        return;
      }
      
      if (!this.formData.coverImage) {
        uni.showToast({
          title: '请上传封面图片',
          icon: 'none'
        });
        return;
      }
      
      if (!this.formData.content) {
        uni.showToast({
          title: '请填写故事内容',
          icon: 'none'
        });
        return;
      }
      
      // 显示加载中
      uni.showLoading({
        title: '保存中...',
        mask: true
      });
      
      // 模拟保存过程
      setTimeout(() => {
        // 这里应该是实际的保存逻辑
        uni.hideLoading();
        uni.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 2000,
          success: () => {
            // 延迟返回上一页
            setTimeout(() => {
              this.goBack();
            }, 2000);
          }
        });
      }, 1500);
    }
  }
}
</script>

<style lang="scss">
.store-culture-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(110rpx + var(--status-bar-height, 20px));
  padding-bottom: 180rpx;
}

/* 顶部导航栏样式 */
.header-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
  color: #fff;
}

.safe-area-top {
  height: var(--status-bar-height, 20px);
  width: 100%;
}

.custom-navbar {
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
}

.navbar-left, .navbar-right {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 48rpx;
  height: 48rpx;
}

/* 背景装饰样式 */
.bg-decoration {
  position: absolute;
  z-index: -1;
  border-radius: 50%;
  opacity: 0.07;
  background-color: #0A84FF;
}

.bg-circle-1 {
  top: -100rpx;
  right: -150rpx;
  width: 400rpx;
  height: 400rpx;
}

.bg-circle-2 {
  top: 20%;
  left: -200rpx;
  width: 500rpx;
  height: 500rpx;
}

.bg-circle-3 {
  bottom: 10%;
  right: -100rpx;
  width: 300rpx;
  height: 300rpx;
}

/* 内容区域 */
.content-scroll {
  padding: 30rpx;
}

.preview-section, .culture-section {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 20rpx;
  display: inline-block;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 8rpx;
  height: 32rpx;
  background-color: #0A84FF;
  border-radius: 4rpx;
}

.preview-action {
  padding: 8rpx 16rpx;
  background-color: rgba(10, 132, 255, 0.1);
  border-radius: 30rpx;
}

.action-text {
  font-size: 26rpx;
  color: #0A84FF;
}

/* 预览区域 */
.culture-preview {
  margin-bottom: 20rpx;
}

.preview-item {
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.preview-image {
  width: 100%;
  display: block;
  border-radius: 16rpx;
}

.preview-tips {
  display: flex;
  padding: 20rpx;
  background-color: rgba(10, 132, 255, 0.05);
  border-radius: 12rpx;
}

.tips-icon {
  font-size: 28rpx;
  margin-right: 10rpx;
}

.tips-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  flex: 1;
}

/* 表单项样式 */
.form-item {
  margin-bottom: 30rpx;
  position: relative;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.required::after {
  content: '*';
  color: #FF3B30;
  margin-left: 6rpx;
}

.form-input {
  width: 100%;
  background-color: #F5F8FC;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  border: 2rpx solid transparent;
}

.form-textarea {
  width: 100%;
  height: 360rpx;
  background-color: #F5F8FC;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  line-height: 1.5;
  border: 2rpx solid transparent;
}

.form-input:focus,
.form-textarea:focus {
  border-color: #0A84FF;
}

.input-counter {
  position: absolute;
  right: 24rpx;
  bottom: 24rpx;
  font-size: 24rpx;
  color: #999;
}

/* 上传区域 */
.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #F5F8FC;
  border: 2rpx dashed #CCCCCC;
  border-radius: 16rpx;
  padding: 40rpx 0;
}

.upload-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(10, 132, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.upload-icon {
  font-size: 40rpx;
  color: #0A84FF;
}

.upload-text {
  font-size: 26rpx;
  color: #666;
}

/* 图片预览 */
.image-preview-area {
  position: relative;
  margin-bottom: 20rpx;
}

.preview-cover {
  width: 100%;
  height: 400rpx;
  border-radius: 16rpx;
  object-fit: cover;
}

.image-actions {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  display: flex;
}

.image-action-btn {
  padding: 8rpx 16rpx;
  background-color: rgba(10, 132, 255, 0.5);
  color: #FFFFFF;
  font-size: 24rpx;
  border-radius: 8rpx;
  margin-left: 10rpx;
}

.image-action-btn.delete {
  background-color: rgba(255, 59, 48, 0.5);
}

/* 富文本编辑器 */
.rich-editor {
  position: relative;
  margin-bottom: 20rpx;
}

.editor-tools {
  display: flex;
  padding: 16rpx 0;
  margin-top: 20rpx;
}

.tool-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx 20rpx;
  background-color: #F5F8FC;
  border-radius: 8rpx;
  margin-right: 16rpx;
}

.tool-icon {
  margin-right: 6rpx;
}

.tool-text {
  font-size: 24rpx;
  color: #333;
}

/* 内容图片预览 */
.content-images {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
  margin-bottom: 30rpx;
}

.image-item {
  width: calc(33.33% - 20rpx);
  margin: 10rpx;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  padding-bottom: calc(33.33% - 20rpx); /* 保持1:1的宽高比 */
}

.content-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-delete {
  position: absolute;
  top: 6rpx;
  right: 6rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

/* 创始人信息 */
.founder-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.founder-avatar-area {
  width: 120rpx;
  height: 120rpx;
  margin-right: 30rpx;
}

.upload-avatar {
  width: 100%;
  height: 100%;
  background-color: #F5F8FC;
  border: 2rpx dashed #CCCCCC;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.founder-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  object-fit: cover;
}

.founder-details {
  flex: 1;
}

.founder-name {
  margin-bottom: 20rpx;
}

/* 联系方式设置 */
.contact-option {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.option-label {
  font-size: 28rpx;
  color: #333;
  margin-left: 10rpx;
}

/* 底部按钮区域 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  padding: 20rpx 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 99;
}

.safe-area-bottom-footer {
  height: constant(safe-area-inset-bottom);
  height: env(safe-area-inset-bottom);
}

.footer-content {
  display: flex;
  justify-content: center;
}

.save-btn {
  width: 90%;
  height: 88rpx;
  background: linear-gradient(135deg, #0A84FF, #0055FF);
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(10, 132, 255, 0.3);
}

.save-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 8rpx rgba(10, 132, 255, 0.3);
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 100rpx;
}

/* 适配暗黑模式 */
@media (prefers-color-scheme: dark) {
  .store-culture-container {
    background-color: #1C1C1E;
  }
  
  .preview-section,
  .culture-section {
    background-color: #2C2C2E;
  }
  
  .section-title,
  .form-label,
  .tool-text,
  .option-label {
    color: #FFFFFF;
  }
  
  .preview-tips {
    background-color: rgba(10, 132, 255, 0.1);
  }
  
  .tips-text {
    color: #A8A8A8;
  }
  
  .form-input,
  .form-textarea,
  .tool-btn,
  .upload-area,
  .upload-avatar {
    background-color: #3A3A3C;
    border-color: #666666;
  }
  
  .form-input,
  .form-textarea {
    color: #FFFFFF;
  }
  
  .upload-text {
    color: #A8A8A8;
  }
  
  .footer {
    background-color: #2C2C2E;
  }
}
</style> 