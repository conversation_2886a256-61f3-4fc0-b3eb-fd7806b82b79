<template>
  <view class="expired-list-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <image src="/static/images/icons/back-white.png" mode="aspectFit" class="back-icon"></image>
        </view>
        <view class="navbar-title">已过期</view>
        <view class="navbar-right">
          <text class="clear-text" @click="showClearConfirm">清空</text>
        </view>
      </view>
    </view>
    
    <!-- 内容区域 -->
    <scroll-view class="scrollable-content" scroll-y @scrolltolower="loadMore" refresher-enabled @refresherrefresh="onRefresh" :refresher-triggered="isRefreshing">
      <!-- 过期内容列表 -->
      <view class="card-list">
        <view class="card-item" v-for="(item, index) in expiredList" :key="item.id">
          <!-- 卡片头部 -->
          <view class="card-header">
            <view class="header-left">
              <text class="card-type">{{item.type}}</text>
              <text class="publish-time">{{item.publishTime}}</text>
            </view>
            <view class="header-right">
              <text class="status-tag status-expired">已过期</text>
            </view>
          </view>
          
          <!-- 卡片内容 -->
          <view class="card-content">
            <view class="route-info">
              <view class="route-points">
                <view class="start-point">
                  <view class="point-marker start"></view>
                  <text class="point-text">{{item.startPoint}}</text>
                </view>
                <view class="route-line"></view>
                <view class="end-point">
                  <view class="point-marker end"></view>
                  <text class="point-text">{{item.endPoint}}</text>
                </view>
              </view>
              <view class="trip-info">
                <view class="info-item">
                  <image src="/static/images/icons/calendar.png" mode="aspectFit" class="info-icon"></image>
                  <text class="info-text">{{item.departureTime}}</text>
                </view>
                <view class="info-item">
                  <image src="/static/images/icons/people.png" mode="aspectFit" class="info-icon"></image>
                  <text class="info-text">{{item.seatCount}}个座位</text>
                </view>
                <view class="info-item" v-if="item.price">
                  <image src="/static/images/icons/price.png" mode="aspectFit" class="info-icon"></image>
                  <text class="info-text price">¥{{item.price}}/人</text>
                </view>
              </view>
            </view>
            
            <!-- 过期信息 -->
            <view class="expired-info">
              <image src="/static/images/icons/time-expired.png" mode="aspectFit" class="expired-icon"></image>
              <text class="expired-text">信息已于 {{item.expiredTime}} 过期</text>
            </view>
          </view>
          
          <!-- 卡片底部按钮 -->
          <view class="card-actions">
            <button class="action-button outline" @click="deleteItem(item)">删除</button>
            <button class="action-button primary" @click="republishItem(item)">重新发布</button>
          </view>
        </view>
      </view>
      
      <!-- 无数据提示 -->
      <view class="empty-state" v-if="expiredList.length === 0 && !isLoading">
        <image src="/static/images/empty/no-expired.png" mode="aspectFit" class="empty-image"></image>
        <text class="empty-text">暂无过期信息</text>
      </view>
      
      <!-- 加载状态 -->
      <view class="loading-state" v-if="isLoading && !isRefreshing">
        <text class="loading-text">加载中...</text>
      </view>
      
      <!-- 到底提示 -->
      <view class="list-bottom" v-if="expiredList.length > 0 && !hasMore">
        <text class="bottom-text">— 已经到底啦 —</text>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 数据
const expiredList = ref([]);
const page = ref(1);
const pageSize = ref(10);
const hasMore = ref(true);
const isLoading = ref(false);
const isRefreshing = ref(false);

// 页面加载时执行
onMounted(() => {
  loadData();
});

// 加载数据
const loadData = () => {
  if (isLoading.value) return;
  isLoading.value = true;
  
  // 模拟数据加载
  setTimeout(() => {
    // 模拟数据
    const mockData = [
      {
        id: '3001',
        type: '长途拼车',
        publishTime: '2023-10-10 16:30',
        expiredTime: '2023-10-12 16:30',
        startPoint: '磁县政府',
        endPoint: '石家庄火车站',
        departureTime: '2023-10-12 10:30',
        seatCount: 3,
        price: 50
      },
      {
        id: '3002',
        type: '上下班拼车',
        publishTime: '2023-10-05 14:15',
        expiredTime: '2023-10-09 14:15',
        startPoint: '磁县老城区',
        endPoint: '邯郸科技学院',
        departureTime: '2023-10-09 07:30',
        seatCount: 4,
        price: 12
      },
      {
        id: '3003',
        type: '短途拼车',
        publishTime: '2023-09-30 11:40',
        expiredTime: '2023-10-02 11:40',
        startPoint: '磁县体育场',
        endPoint: '磁县汽车站',
        departureTime: '2023-10-01 16:00',
        seatCount: 2,
        price: 5
      }
    ];
    
    if (page.value === 1) {
      expiredList.value = mockData;
    } else {
      expiredList.value = [...expiredList.value, ...mockData];
    }
    
    // 模拟没有更多数据
    if (page.value >= 2) {
      hasMore.value = false;
    }
    
    isLoading.value = false;
    isRefreshing.value = false;
  }, 1000);
};

// 加载更多
const loadMore = () => {
  if (!hasMore.value || isLoading.value) return;
  page.value++;
  loadData();
};

// 下拉刷新
const onRefresh = () => {
  isRefreshing.value = true;
  page.value = 1;
  hasMore.value = true;
  loadData();
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 删除信息
const deleteItem = (item) => {
  uni.showModal({
    title: '提示',
    content: '确定要删除此条信息吗？',
    success: (res) => {
      if (res.confirm) {
        // 模拟删除操作
        expiredList.value = expiredList.value.filter(i => i.id !== item.id);
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        });
      }
    }
  });
};

// 重新发布
const republishItem = (item) => {
  uni.navigateTo({
    url: `/carpool-package/pages/carpool/publish/index?id=${item.id}&type=republish`
  });
};

// 显示清空确认
const showClearConfirm = () => {
  if (expiredList.value.length === 0) {
    uni.showToast({
      title: '暂无数据可清空',
      icon: 'none'
    });
    return;
  }
  
  uni.showModal({
    title: '提示',
    content: '确定要清空所有过期信息吗？此操作不可恢复。',
    success: (res) => {
      if (res.confirm) {
        clearAll();
      }
    }
  });
};

// 清空所有
const clearAll = () => {
  expiredList.value = [];
  uni.showToast({
    title: '已清空',
    icon: 'success'
  });
};
</script>

<style scoped>
.expired-list-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
}

/* 导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  padding-top: var(--status-bar-height);
  background: linear-gradient(135deg, #9E9E9E, #757575);
  z-index: 100;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 15px;
}

.navbar-left {
  width: 50px;
}

.back-icon {
  width: 20px;
  height: 20px;
}

.navbar-title {
  flex: 1;
  text-align: center;
  color: #FFFFFF;
  font-size: 18px;
  font-weight: 500;
}

.navbar-right {
  width: 50px;
  text-align: right;
}

.clear-text {
  font-size: 14px;
  color: #FFFFFF;
}

/* 内容区域 */
.scrollable-content {
  flex: 1;
  margin-top: calc(44px + var(--status-bar-height));
  padding: 12px;
}

/* 卡片列表 */
.card-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.card-item {
  background-color: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #F8FAFB;
  border-bottom: 1px solid #EEEEEE;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-type {
  font-size: 15px;
  font-weight: 500;
  color: #333333;
}

.publish-time {
  font-size: 12px;
  color: #999999;
}

.status-tag {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.status-expired {
  background-color: rgba(153, 153, 153, 0.1);
  color: #999999;
}

/* 卡片内容 */
.card-content {
  padding: 16px;
}

.route-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.route-points {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.start-point, .end-point {
  display: flex;
  align-items: center;
  gap: 10px;
}

.point-marker {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.start {
  background-color: #1677FF;
}

.end {
  background-color: #FF5722;
}

.route-line {
  width: 2px;
  height: 20px;
  background-color: #DDDDDD;
  margin-left: 5px;
}

.point-text {
  font-size: 16px;
  color: #333333;
}

.trip-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 10px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.info-icon {
  width: 24px;
  height: 24px;
}

.info-text {
  font-size: 14px;
  color: #666666;
}

.price {
  color: #FF5722;
  font-weight: 500;
}

/* 过期信息 */
.expired-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px dashed #EEEEEE;
}

.expired-icon {
  width: 24px;
  height: 24px;
}

.expired-text {
  font-size: 12px;
  color: #999999;
}

/* 卡片底部按钮 */
.card-actions {
  display: flex;
  justify-content: flex-end;
  padding: 20px 24px;
  gap: 20px;
  border-top: 1px solid #EEEEEE;
}

.action-button {
  padding: 10px 20px;
  font-size: 16px;
  height: 44px;
  min-width: 90px;
  border-radius: 6px;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-button.outline {
  color: #666666;
  border: 1px solid #DDDDDD;
}

.action-button.primary {
  color: #FFFFFF;
  background-color: #1677FF;
  border: 1px solid #1677FF;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  color: #999999;
  margin-bottom: 20px;
}

/* 加载状态 */
.loading-state {
  padding: 16px 0;
  text-align: center;
}

.loading-text {
  font-size: 14px;
  color: #999999;
}

/* 列表底部 */
.list-bottom {
  padding: 16px 0;
  text-align: center;
}

.bottom-text {
  font-size: 14px;
  color: #999999;
}
</style> 