@echo off

echo.
echo ========================================
echo   磁州生活网本地服务安装指南
echo ========================================
echo.

echo 请按照以下步骤手动安装所需服务：
echo.

echo ========================================
echo 第一步：安装 Node.js
echo ========================================
echo.

echo 1. 打开浏览器访问：https://nodejs.org/
echo 2. 点击下载 LTS 版本（推荐 18.x 或 20.x）
echo 3. 下载 Windows Installer (.msi) 64-bit
echo 4. 运行安装程序，确保勾选 "Add to PATH"
echo 5. 安装完成后重启命令提示符
echo.

echo 验证安装：
echo   node --version
echo   npm --version
echo.

set /p node_done="Node.js 安装完成后按回车继续..."

echo.
echo ========================================
echo 第二步：安装 MySQL
echo ========================================
echo.

echo 1. 打开浏览器访问：https://dev.mysql.com/downloads/mysql/
echo 2. 点击 "MySQL Installer for Windows"
echo 3. 下载 mysql-installer-web-community 版本
echo 4. 运行安装程序
echo 5. 选择 "Developer Default" 安装类型
echo 6. 在配置页面设置：
echo    - Root 密码：cizhou123456
echo    - 端口：3306
echo    - 认证方式：Use Legacy Authentication Method
echo 7. 勾选 "Start the MySQL Server at System Startup"
echo 8. 完成安装
echo.

echo 验证安装：
echo   mysql -uroot -pcizhou123456
echo   (在MySQL中输入：SELECT VERSION(); 然后输入：EXIT;)
echo.

set /p mysql_done="MySQL 安装完成后按回车继续..."

echo.
echo ========================================
echo 第三步：安装 Redis
echo ========================================
echo.

echo 1. 打开浏览器访问：https://github.com/tporadowski/redis/releases
echo 2. 下载最新的 .msi 文件（如：Redis-x64-5.0.14.1.msi）
echo 3. 运行安装程序
echo 4. 勾选 "Add the Redis installation folder to the PATH"
echo 5. 勾选 "Run the Redis Server"
echo 6. 设置端口为 6379
echo 7. 设置密码为：cizhou123456
echo 8. 完成安装
echo.

echo 验证安装：
echo   redis-cli -a cizhou123456
echo   (在Redis中输入：ping 然后输入：exit)
echo.

set /p redis_done="Redis 安装完成后按回车继续..."

echo.
echo ========================================
echo 第四步：验证所有安装
echo ========================================
echo.

echo 请在新的命令提示符窗口中运行以下命令验证：
echo.

echo 检查 Node.js：
echo   node --version
echo   npm --version
echo.

echo 检查 MySQL：
echo   mysql -uroot -pcizhou123456 -e "SELECT VERSION();"
echo.

echo 检查 Redis：
echo   redis-cli -a cizhou123456 ping
echo.

echo ========================================
echo 第五步：启动系统
echo ========================================
echo.

echo 所有服务安装完成后：
echo.

echo 1. 关闭当前命令提示符
echo 2. 重新打开命令提示符
echo 3. 进入项目目录：cd cizhou-admin
echo 4. 运行：start-without-docker.bat
echo.

echo 如果一切正常，系统将：
echo   - 自动检测服务状态
echo   - 初始化数据库
echo   - 提供启动指导
echo.

echo ========================================
echo 配置信息汇总
echo ========================================
echo.

echo MySQL 配置：
echo   主机：localhost
echo   端口：3306
echo   用户：root
echo   密码：cizhou123456
echo.

echo Redis 配置：
echo   主机：localhost
echo   端口：6379
echo   密码：cizhou123456
echo.

echo 默认管理员账号：
echo   用户名：admin
echo   密码：admin123
echo.

echo ========================================
echo 安装指南完成
echo ========================================
echo.

echo 如果安装过程中遇到问题：
echo   1. 确保以管理员身份运行安装程序
echo   2. 检查防火墙设置
echo   3. 确保端口 3306 和 6379 未被占用
echo   4. 重启计算机后重试
echo.

pause
