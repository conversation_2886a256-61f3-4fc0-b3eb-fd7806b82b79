"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
const __default__ = {
  // 页面配置
  navigationStyle: "custom"
};
const _sfc_main = /* @__PURE__ */ Object.assign(__default__, {
  __name: "list",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    common_vendor.ref(false);
    const currentStatus = common_vendor.ref("all");
    const activityList = common_vendor.reactive([
      {
        id: 1,
        title: "磁州美食节",
        coverImage: "/static/images/banner/banner-1.png",
        startTime: "2024-03-20",
        endTime: "2024-03-25",
        location: "磁县人民广场",
        description: "汇聚磁州各地特色美食，带您品尝地道磁州味道",
        status: "进行中",
        participantsCount: 128,
        viewsCount: 3560,
        commentsCount: 42
      },
      {
        id: 2,
        title: "春季农产品展销会",
        coverImage: "/static/images/banner/banner-2.png",
        startTime: "2024-03-28",
        endTime: "2024-03-30",
        location: "磁县会展中心",
        description: "展示磁州特色农产品，促进农产品销售",
        status: "未开始",
        participantsCount: 89,
        viewsCount: 1253,
        commentsCount: 17
      },
      {
        id: 3,
        title: "磁州文化节",
        coverImage: "/static/images/banner/banner-3.jpg",
        startTime: "2024-04-01",
        endTime: "2024-04-07",
        location: "磁州古镇",
        description: "传承磁州文化，弘扬地方特色",
        status: "未开始",
        participantsCount: 65,
        viewsCount: 980,
        commentsCount: 8
      },
      {
        id: 4,
        title: "春季招聘会",
        coverImage: "/static/images/banner/banner-4.jpg",
        startTime: "2024-03-15",
        endTime: "2024-03-16",
        location: "磁县人才市场",
        description: "提供就业机会，促进人才交流",
        status: "已结束",
        participantsCount: 356,
        viewsCount: 4230,
        commentsCount: 76
      }
    ]);
    const loading = common_vendor.ref(false);
    const hasMore = common_vendor.ref(false);
    const formatDate = (dateString) => {
      if (!dateString)
        return "";
      const date = new Date(dateString);
      return `${date.getMonth() + 1}.${date.getDate()}`;
    };
    const filteredActivities = common_vendor.computed(() => {
      if (currentStatus.value === "all") {
        return activityList;
      }
      return activityList.filter((item) => item.status === currentStatus.value);
    });
    common_vendor.onLoad(() => {
      common_vendor.index.hideNavigationBar();
    });
    common_vendor.onShow(() => {
      common_vendor.index.hideNavigationBar();
    });
    common_vendor.onMounted(() => {
      common_vendor.index.getSystemInfo({
        success: (res) => {
          statusBarHeight.value = res.statusBarHeight;
        }
      });
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const showFilterOptions = () => {
      common_vendor.index.showActionSheet({
        itemList: ["按时间排序", "按热度排序", "按参与人数排序"],
        success: (res) => {
          common_vendor.index.showToast({
            title: "排序功能已选择",
            icon: "none"
          });
        }
      });
    };
    const filterByStatus = (status) => {
      currentStatus.value = status;
    };
    const loadMore = () => {
      if (loading.value || !hasMore.value)
        return;
      loading.value = true;
      setTimeout(() => {
        loading.value = false;
      }, 1e3);
    };
    const viewActivityDetail = (id) => {
      common_vendor.index.navigateTo({
        url: `/subPackages/activity/pages/detail?id=${id}`
      });
    };
    const editActivity = (id) => {
      common_vendor.index.navigateTo({
        url: `/subPackages/activity/pages/edit?id=${id}`
      });
    };
    const promoteActivity = (id) => {
      common_vendor.index.showActionSheet({
        itemList: ["活动置顶", "首页推荐", "短信推广", "社群分享"],
        success: (res) => {
          const options = ["活动置顶", "首页推荐", "短信推广", "社群分享"];
          common_vendor.index.showToast({
            title: `已选择${options[res.tapIndex]}`,
            icon: "none"
          });
        }
      });
    };
    const shareActivity = (id) => {
      common_vendor.index.showShareMenu({
        withShareTicket: true,
        menus: ["shareAppMessage", "shareTimeline"]
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: statusBarHeight.value + "px",
        b: common_vendor.p({
          d: "M15 18L9 12L15 6",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        c: common_vendor.p({
          width: "24",
          height: "24",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        d: common_vendor.o(goBack),
        e: common_vendor.p({
          d: "M3 6H21M6 12H18M10 18H14",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        f: common_vendor.p({
          width: "22",
          height: "22",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        g: common_vendor.o(showFilterOptions),
        h: currentStatus.value === "all" ? 1 : "",
        i: common_vendor.o(($event) => filterByStatus("all")),
        j: currentStatus.value === "进行中" ? 1 : "",
        k: common_vendor.o(($event) => filterByStatus("进行中")),
        l: currentStatus.value === "未开始" ? 1 : "",
        m: common_vendor.o(($event) => filterByStatus("未开始")),
        n: currentStatus.value === "已结束" ? 1 : "",
        o: common_vendor.o(($event) => filterByStatus("已结束")),
        p: filteredActivities.value.length === 0
      }, filteredActivities.value.length === 0 ? {
        q: common_vendor.p({
          d: "M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        r: common_vendor.p({
          d: "M9 9H9.01",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        s: common_vendor.p({
          d: "M15 9H15.01",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        t: common_vendor.p({
          d: "M8 14H16",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        v: common_vendor.p({
          width: "80",
          height: "80",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        })
      } : {
        w: common_vendor.f(filteredActivities.value, (activity, index, i0) => {
          return {
            a: `url(${activity.coverImage})`,
            b: common_vendor.t(activity.title),
            c: "88f4bae4-10-" + i0 + "," + ("88f4bae4-9-" + i0),
            d: "88f4bae4-11-" + i0 + "," + ("88f4bae4-9-" + i0),
            e: "88f4bae4-9-" + i0,
            f: common_vendor.t(formatDate(activity.startTime)),
            g: common_vendor.t(formatDate(activity.endTime)),
            h: "88f4bae4-13-" + i0 + "," + ("88f4bae4-12-" + i0),
            i: "88f4bae4-14-" + i0 + "," + ("88f4bae4-12-" + i0),
            j: "88f4bae4-15-" + i0 + "," + ("88f4bae4-12-" + i0),
            k: "88f4bae4-16-" + i0 + "," + ("88f4bae4-12-" + i0),
            l: "88f4bae4-12-" + i0,
            m: common_vendor.t(activity.participantsCount || 0),
            n: common_vendor.t(activity.status),
            o: activity.status === "进行中" ? 1 : "",
            p: activity.status === "未开始" ? 1 : "",
            q: activity.status === "已结束" ? 1 : "",
            r: "88f4bae4-18-" + i0 + "," + ("88f4bae4-17-" + i0),
            s: "88f4bae4-19-" + i0 + "," + ("88f4bae4-17-" + i0),
            t: "88f4bae4-17-" + i0,
            v: common_vendor.o(($event) => editActivity(activity.id), index),
            w: "88f4bae4-21-" + i0 + "," + ("88f4bae4-20-" + i0),
            x: "88f4bae4-22-" + i0 + "," + ("88f4bae4-20-" + i0),
            y: "88f4bae4-23-" + i0 + "," + ("88f4bae4-20-" + i0),
            z: "88f4bae4-20-" + i0,
            A: common_vendor.o(($event) => promoteActivity(activity.id), index),
            B: "88f4bae4-25-" + i0 + "," + ("88f4bae4-24-" + i0),
            C: "88f4bae4-26-" + i0 + "," + ("88f4bae4-24-" + i0),
            D: "88f4bae4-27-" + i0 + "," + ("88f4bae4-24-" + i0),
            E: "88f4bae4-28-" + i0 + "," + ("88f4bae4-24-" + i0),
            F: "88f4bae4-29-" + i0 + "," + ("88f4bae4-24-" + i0),
            G: "88f4bae4-24-" + i0,
            H: common_vendor.o(($event) => shareActivity(activity.id), index),
            I: index,
            J: common_vendor.o(($event) => viewActivityDetail(activity.id), index)
          };
        }),
        x: common_vendor.p({
          d: "M12 8V12L15 15",
          stroke: "#666666",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        y: common_vendor.p({
          d: "M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z",
          stroke: "#666666",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        z: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        A: common_vendor.p({
          d: "M17 21V19C17 16.7909 15.2091 15 13 15H5C2.79086 15 1 16.7909 1 19V21",
          stroke: "#666666",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        B: common_vendor.p({
          d: "M9 11C11.2091 11 13 9.20914 13 7C13 4.79086 11.2091 3 9 3C6.79086 3 5 4.79086 5 7C5 9.20914 6.79086 11 9 11Z",
          stroke: "#666666",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        C: common_vendor.p({
          d: "M23 21V19C22.9986 17.1771 21.765 15.5857 20 15.13",
          stroke: "#666666",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        D: common_vendor.p({
          d: "M16 3.13C17.7699 3.58317 19.0078 5.17799 19.0078 7.005C19.0078 8.83201 17.7699 10.4268 16 10.88",
          stroke: "#666666",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        E: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        F: common_vendor.p({
          d: "M11 4H4C2.89543 4 2 4.89543 2 6V20C2 21.1046 2.89543 22 4 22H18C19.1046 22 20 21.1046 20 20V13",
          stroke: "#007AFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        G: common_vendor.p({
          d: "M18.5 2.5C19.3284 1.67157 20.6716 1.67157 21.5 2.5C22.3284 3.32843 22.3284 4.67157 21.5 5.5L12 15L8 16L9 12L18.5 2.5Z",
          stroke: "#007AFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        H: common_vendor.p({
          width: "20",
          height: "20",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        I: common_vendor.p({
          d: "M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15",
          stroke: "#FF9500",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        J: common_vendor.p({
          d: "M7 10L12 15L17 10",
          stroke: "#FF9500",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        K: common_vendor.p({
          d: "M12 15V3",
          stroke: "#FF9500",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        L: common_vendor.p({
          width: "20",
          height: "20",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        M: common_vendor.p({
          d: "M18 8C19.6569 8 21 6.65685 21 5C21 3.34315 19.6569 2 18 2C16.3431 2 15 3.34315 15 5C15 6.65685 16.3431 8 18 8Z",
          stroke: "#34C759",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        N: common_vendor.p({
          d: "M6 15C7.65685 15 9 13.6569 9 12C9 10.3431 7.65685 9 6 9C4.34315 9 3 10.3431 3 12C3 13.6569 4.34315 15 6 15Z",
          stroke: "#34C759",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        O: common_vendor.p({
          d: "M18 22C19.6569 22 21 20.6569 21 19C21 17.3431 19.6569 16 18 16C16.3431 16 15 17.3431 15 19C15 20.6569 16.3431 22 18 22Z",
          stroke: "#34C759",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        P: common_vendor.p({
          d: "M8.59 13.51L15.42 17.49",
          stroke: "#34C759",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        Q: common_vendor.p({
          d: "M15.41 6.51L8.59 10.49",
          stroke: "#34C759",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        R: common_vendor.p({
          width: "20",
          height: "20",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        })
      }, {
        S: loading.value
      }, loading.value ? {} : {}, {
        T: !loading.value && !hasMore.value && filteredActivities.value.length > 0
      }, !loading.value && !hasMore.value && filteredActivities.value.length > 0 ? {} : {}, {
        U: common_vendor.o(loadMore)
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-88f4bae4"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/activity/pages/list.js.map
