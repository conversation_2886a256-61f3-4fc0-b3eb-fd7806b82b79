"use strict";
const common_vendor = require("../common/vendor.js");
const utils_request = require("../utils/request.js");
class UserApiService {
  /**
   * 用户登录
   * @param {Object} credentials - 登录凭据
   * @param {string} credentials.phone - 手机号
   * @param {string} credentials.password - 密码
   * @returns {Promise} 登录结果
   */
  async login(credentials) {
    try {
      const response = await utils_request.request.post("/auth/login", {
        phone: credentials.phone,
        password: credentials.password
      });
      if (response.data && response.data.token) {
        common_vendor.index.setStorageSync("token", response.data.token);
        common_vendor.index.setStorageSync("userInfo", response.data.user);
        return {
          success: true,
          data: response.data,
          message: "登录成功"
        };
      } else {
        return {
          success: false,
          message: response.message || "登录失败"
        };
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at api/userApi.js:42", "登录失败:", error);
      return {
        success: false,
        message: error.message || "网络错误，请重试"
      };
    }
  }
  /**
   * 微信小程序登录
   * @param {Object} wxData - 微信登录数据
   * @param {string} wxData.code - 微信授权码
   * @param {Object} wxData.userInfo - 微信用户信息
   * @returns {Promise} 登录结果
   */
  async wxLogin(wxData) {
    try {
      const response = await utils_request.request.post("/auth/wx-login", {
        code: wxData.code,
        user_info: wxData.userInfo
      });
      if (response.data && response.data.token) {
        common_vendor.index.setStorageSync("token", response.data.token);
        common_vendor.index.setStorageSync("userInfo", response.data.user);
        return {
          success: true,
          data: response.data,
          message: "登录成功"
        };
      } else {
        return {
          success: false,
          message: response.message || "微信登录失败"
        };
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at api/userApi.js:81", "微信登录失败:", error);
      return {
        success: false,
        message: error.message || "网络错误，请重试"
      };
    }
  }
  /**
   * 用户注册
   * @param {Object} userData - 注册数据
   * @param {string} userData.phone - 手机号
   * @param {string} userData.password - 密码
   * @param {string} userData.nickname - 昵称
   * @param {string} userData.sms_code - 短信验证码
   * @returns {Promise} 注册结果
   */
  async register(userData) {
    try {
      const response = await utils_request.request.post("/auth/register", userData);
      if (response.data) {
        return {
          success: true,
          data: response.data,
          message: "注册成功"
        };
      } else {
        return {
          success: false,
          message: response.message || "注册失败"
        };
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at api/userApi.js:115", "注册失败:", error);
      return {
        success: false,
        message: error.message || "网络错误，请重试"
      };
    }
  }
  /**
   * 发送短信验证码
   * @param {string} phone - 手机号
   * @param {string} type - 验证码类型 (register, login, reset_password)
   * @returns {Promise} 发送结果
   */
  async sendSmsCode(phone, type = "register") {
    try {
      const response = await utils_request.request.post("/auth/send-sms", {
        phone,
        type
      });
      return {
        success: true,
        message: "验证码发送成功"
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at api/userApi.js:141", "发送验证码失败:", error);
      return {
        success: false,
        message: error.message || "发送失败，请重试"
      };
    }
  }
  /**
   * 获取用户信息
   * @returns {Promise} 用户信息
   */
  async getUserInfo() {
    try {
      const response = await utils_request.request.get("/user/profile");
      if (response.data) {
        common_vendor.index.setStorageSync("userInfo", response.data);
        return {
          success: true,
          data: response.data
        };
      } else {
        return {
          success: false,
          message: "获取用户信息失败"
        };
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at api/userApi.js:172", "获取用户信息失败:", error);
      return {
        success: false,
        message: error.message || "网络错误"
      };
    }
  }
  /**
   * 更新用户信息
   * @param {Object} userData - 用户数据
   * @returns {Promise} 更新结果
   */
  async updateUserInfo(userData) {
    try {
      const response = await utils_request.request.put("/user/profile", userData);
      if (response.data) {
        common_vendor.index.setStorageSync("userInfo", response.data);
        return {
          success: true,
          data: response.data,
          message: "更新成功"
        };
      } else {
        return {
          success: false,
          message: response.message || "更新失败"
        };
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at api/userApi.js:205", "更新用户信息失败:", error);
      return {
        success: false,
        message: error.message || "网络错误，请重试"
      };
    }
  }
  /**
   * 修改密码
   * @param {Object} passwordData - 密码数据
   * @param {string} passwordData.old_password - 旧密码
   * @param {string} passwordData.new_password - 新密码
   * @returns {Promise} 修改结果
   */
  async changePassword(passwordData) {
    try {
      const response = await utils_request.request.put("/user/password", passwordData);
      return {
        success: true,
        message: "密码修改成功"
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at api/userApi.js:229", "修改密码失败:", error);
      return {
        success: false,
        message: error.message || "修改失败，请重试"
      };
    }
  }
  /**
   * 用户登出
   * @returns {Promise} 登出结果
   */
  async logout() {
    try {
      await utils_request.request.post("/auth/logout");
      common_vendor.index.removeStorageSync("token");
      common_vendor.index.removeStorageSync("userInfo");
      return {
        success: true,
        message: "退出成功"
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at api/userApi.js:254", "退出失败:", error);
      common_vendor.index.removeStorageSync("token");
      common_vendor.index.removeStorageSync("userInfo");
      return {
        success: true,
        message: "退出成功"
      };
    }
  }
  /**
   * 检查登录状态
   * @returns {boolean} 是否已登录
   */
  isLoggedIn() {
    const token = common_vendor.index.getStorageSync("token");
    const userInfo = common_vendor.index.getStorageSync("userInfo");
    return !!(token && userInfo);
  }
  /**
   * 获取当前用户信息（从本地存储）
   * @returns {Object|null} 用户信息
   */
  getCurrentUser() {
    return common_vendor.index.getStorageSync("userInfo") || null;
  }
  /**
   * 获取用户token（从本地存储）
   * @returns {string|null} 用户token
   */
  getToken() {
    return common_vendor.index.getStorageSync("token") || null;
  }
}
const userApi = new UserApiService();
exports.userApi = userApi;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/userApi.js.map
