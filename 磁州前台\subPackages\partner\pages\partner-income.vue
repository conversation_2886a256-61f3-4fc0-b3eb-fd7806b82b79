<template>
  <view class="income-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/最新返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">收益明细</view>
      <view class="navbar-right">
        <!-- 预留位置与发布页面保持一致 -->
      </view>
    </view>
    
    <!-- 添加顶部安全区域 -->
    <view class="safe-area-top"></view>

    <!-- 收益总览 -->
    <view class="income-overview">
      <view class="overview-header">
        <text class="overview-title">总收益(元)</text>
        <picker mode="date" fields="month" :value="currentMonth" @change="monthChange">
          <view class="month-picker">
            {{formattedMonth}}
            <text class="cuIcon-calendar"></text>
          </view>
        </picker>
      </view>
      <view class="overview-amount">{{totalIncome}}</view>
      <view class="overview-stats">
        <view class="stats-item">
          <text class="stats-value">{{firstLevelIncome}}</text>
          <text class="stats-label">一级收益</text>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <text class="stats-value">{{secondLevelIncome}}</text>
          <text class="stats-label">二级收益</text>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <text class="stats-value">{{incomeOrders}}</text>
          <text class="stats-label">订单数</text>
        </view>
      </view>
    </view>

    <!-- 收益图表 -->
    <view class="income-chart">
      <view class="chart-header">
        <text class="chart-title">收益趋势</text>
        <view class="chart-legend">
          <view class="legend-item">
            <view class="legend-color level-one"></view>
            <text>一级收益</text>
          </view>
          <view class="legend-item">
            <view class="legend-color level-two"></view>
            <text>二级收益</text>
          </view>
        </view>
      </view>
      <view class="chart-content">
        <!-- 简化版图表，实际项目中可以使用图表组件 -->
        <view class="chart-bars">
          <view class="chart-bar" v-for="(item, index) in chartData" :key="index">
            <view class="bar-item level-two" :style="{height: item.level2 + 'rpx'}"></view>
            <view class="bar-item level-one" :style="{height: item.level1 + 'rpx'}"></view>
            <text class="bar-date">{{item.date}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 筛选区域 -->
    <view class="filter-section">
      <view class="filter-tabs">
        <view 
          class="tab-item" 
          :class="{'active': currentTab === 'all'}"
          @tap="switchTab('all')"
        >全部</view>
        <view 
          class="tab-item" 
          :class="{'active': currentTab === 'level1'}"
          @tap="switchTab('level1')"
        >一级收益</view>
        <view 
          class="tab-item" 
          :class="{'active': currentTab === 'level2'}"
          @tap="switchTab('level2')"
        >二级收益</view>
      </view>
    </view>

    <!-- 收益明细列表 -->
    <view class="income-list" v-if="filteredIncome.length > 0">
      <view class="income-item" v-for="(item, index) in filteredIncome" :key="index">
        <view class="item-left">
          <text class="item-title">{{item.title}}</text>
          <text class="item-desc">{{item.desc}}</text>
          <text class="item-time">{{item.time}}</text>
        </view>
        <view class="item-right">
          <text class="item-amount" :class="{'level-one-text': item.level === 1, 'level-two-text': item.level === 2}">+{{item.amount}}</text>
          <text class="item-level">{{item.level === 1 ? '一级收益' : '二级收益'}}</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <image src="/static/images/no-income.png" mode="aspectFit"></image>
      <text>暂无收益记录</text>
      <button class="share-btn" @tap="navigateToPoster">去推广赚佣金</button>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" v-if="filteredIncome.length > 0 && hasMore">
      <text @tap="loadMore" v-if="!isLoading">加载更多</text>
      <text v-else>加载中...</text>
    </view>

    <view class="load-end" v-if="filteredIncome.length > 0 && !hasMore">
      <text>- 已经到底了 -</text>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';

// 响应式数据
const currentMonth = ref('2023-11'); // 当前选择的月份
const totalIncome = ref('3628.50'); // 总收益
const firstLevelIncome = ref('2745.20'); // 一级收益
const secondLevelIncome = ref('883.30'); // 二级收益
const incomeOrders = ref(56); // 订单数
const currentTab = ref('all'); // 当前选中的标签
const incomeList = ref([]); // 收益列表
const page = ref(1); // 当前页码
const pageSize = ref(10); // 每页条数
const hasMore = ref(true); // 是否有更多数据
const isLoading = ref(false); // 是否正在加载
const chartData = ref([
  { date: '11-01', level1: 80, level2: 20 },
  { date: '11-05', level1: 120, level2: 50 },
  { date: '11-10', level1: 60, level2: 30 },
  { date: '11-15', level1: 100, level2: 40 },
  { date: '11-20', level1: 150, level2: 60 },
  { date: '11-25', level1: 90, level2: 35 },
  { date: '11-30', level1: 110, level2: 45 }
]);

// 计算属性
// 格式化月份显示
const formattedMonth = computed(() => {
  const [year, month] = currentMonth.value.split('-');
  return `${year}年${parseInt(month)}月`;
});

// 过滤后的收益列表
const filteredIncome = computed(() => {
  if (currentTab.value === 'all') {
    return incomeList.value;
  } else if (currentTab.value === 'level1') {
    return incomeList.value.filter(item => item.level === 1);
  } else {
    return incomeList.value.filter(item => item.level === 2);
  }
});

// 方法
// 月份变化
const monthChange = (e) => {
  currentMonth.value = e.detail.value;
  page.value = 1;
  hasMore.value = true;
  incomeList.value = [];
  loadData();
  
  // 更新图表数据
  updateChartData();
};

// 切换标签
const switchTab = (tab) => {
  currentTab.value = tab;
};

// 加载数据
const loadData = () => {
  isLoading.value = true;
  
  // 模拟API请求
  setTimeout(() => {
    // 模拟数据
    const mockData = generateMockIncome();
    
    if (page.value === 1) {
      incomeList.value = mockData;
    } else {
      incomeList.value = [...incomeList.value, ...mockData];
    }
    
    // 判断是否还有更多数据
    hasMore.value = page.value < 3; // 模拟只有3页数据
    
    isLoading.value = false;
  }, 500);
};

// 加载更多
const loadMore = () => {
  if (isLoading.value || !hasMore.value) return;
  
  page.value++;
  loadData();
};

// 更新图表数据
const updateChartData = () => {
  // 在实际项目中，这里会根据选择的月份从服务器获取数据
  // 这里只是简单模拟
  const [year, month] = currentMonth.value.split('-');
  const daysInMonth = new Date(parseInt(year), parseInt(month), 0).getDate();
  
  chartData.value = [];
  
  // 生成7个数据点
  const step = Math.floor(daysInMonth / 6);
  for (let i = 0; i < 7; i++) {
    const day = i * step + 1;
    chartData.value.push({
      date: `${month}-${day < 10 ? '0' + day : day}`,
      level1: Math.floor(Math.random() * 100) + 50,
      level2: Math.floor(Math.random() * 50) + 10
    });
  }
};

// 跳转到海报页面
const navigateToPoster = () => {
  uni.navigateTo({
    url: '/pages/my/partner-poster'
  });
};

// 生成模拟收益数据
const generateMockIncome = () => {
  const mockIncome = [];
  const titles = ['商品订单佣金', '会员充值佣金', '服务预约佣金', '活动报名佣金'];
  const names = ['张先生', '李女士', '王先生', '赵女士', '刘先生', '陈女士', '杨先生', '周女士'];
  
  // 随机生成10条记录
  for (let i = 0; i < 10; i++) {
    const level = Math.random() > 0.7 ? 2 : 1; // 70%是一级收益，30%是二级收益
    const title = titles[Math.floor(Math.random() * titles.length)];
    const name = names[Math.floor(Math.random() * names.length)];
    
    // 随机金额，一级收益通常比二级高
    const amount = level === 1 
      ? (Math.floor(Math.random() * 100) + 30).toFixed(2)
      : (Math.floor(Math.random() * 50) + 10).toFixed(2);
    
    // 随机时间，本月内
    const [year, month] = currentMonth.value.split('-');
    const day = Math.floor(Math.random() * 28) + 1;
    const hour = Math.floor(Math.random() * 24);
    const minute = Math.floor(Math.random() * 60);
    const time = `${year}-${month}-${day < 10 ? '0' + day : day} ${hour < 10 ? '0' + hour : hour}:${minute < 10 ? '0' + minute : minute}`;
    
    mockIncome.push({
      id: 'income_' + Date.now() + i,
      title,
      desc: `来自${name}的订单`,
      time,
      amount,
      level
    });
  }
  
  // 按时间倒序排序
  mockIncome.sort((a, b) => new Date(b.time) - new Date(a.time));
  
  return mockIncome;
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 生命周期钩子 - 页面加载
onMounted(() => {
  // 初始化加载数据
  loadData();
});
</script>

<style lang="scss" scoped>
.income-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
  padding-top: calc(44px + var(--status-bar-height));
}

.income-overview {
  margin: 30rpx;
  background: linear-gradient(135deg, #1677FF, #0E5FD8);
  border-radius: 16rpx;
  padding: 30rpx;
  color: #ffffff;
  box-shadow: 0 10rpx 20rpx rgba(22, 119, 255, 0.2);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.overview-title {
  font-size: 28rpx;
  opacity: 0.9;
}

.month-picker {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  
  .cuIcon-calendar {
    margin-left: 10rpx;
  }
}

.overview-amount {
  font-size: 60rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.overview-stats {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 20rpx;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stats-value {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 24rpx;
  opacity: 0.8;
}

.stats-divider {
  width: 1px;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.2);
}

.income-chart {
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.chart-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
}

.chart-legend {
  display: flex;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
  font-size: 24rpx;
  color: #666666;
}

.legend-color {
  width: 20rpx;
  height: 10rpx;
  margin-right: 8rpx;
  
  &.level-one {
    background-color: #1677FF;
  }
  
  &.level-two {
    background-color: #36CBCB;
  }
}

.chart-content {
  height: 400rpx;
  display: flex;
  align-items: flex-end;
}

.chart-bars {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  width: 100%;
  height: 300rpx;
}

.chart-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  width: 40rpx;
}

.bar-item {
  width: 40rpx;
  border-radius: 4rpx 4rpx 0 0;
  
  &.level-one {
    background-color: #1677FF;
  }
  
  &.level-two {
    background-color: #36CBCB;
  }
}

.bar-date {
  margin-top: 10rpx;
  font-size: 22rpx;
  color: #999999;
}

.filter-section {
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 0 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.filter-tabs {
  display: flex;
}

.tab-item {
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #666666;
  position: relative;
  
  &.active {
    color: #1677FF;
    font-weight: 500;
    
    &::after {
      content: '';
      position: absolute;
      left: 50%;
      bottom: 0;
      transform: translateX(-50%);
      width: 40rpx;
      height: 4rpx;
      background-color: #1677FF;
      border-radius: 2rpx;
    }
  }
}

.income-list {
  padding: 0 30rpx;
}

.income-item {
  display: flex;
  justify-content: space-between;
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.item-left {
  display: flex;
  flex-direction: column;
}

.item-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 10rpx;
}

.item-desc {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 10rpx;
}

.item-time {
  font-size: 24rpx;
  color: #999999;
}

.item-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
}

.item-amount {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  
  &.level-one-text {
    color: #1677FF;
  }
  
  &.level-two-text {
    color: #36CBCB;
  }
}

.item-level {
  font-size: 24rpx;
  color: #999999;
  padding: 4rpx 12rpx;
  background-color: #f5f5f5;
  border-radius: 20rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
  
  image {
    width: 240rpx;
    height: 240rpx;
    margin-bottom: 30rpx;
  }
  
  text {
    font-size: 28rpx;
    color: #999999;
    margin-bottom: 40rpx;
  }
  
  .share-btn {
    width: 300rpx;
    height: 80rpx;
    line-height: 80rpx;
    background: linear-gradient(135deg, #1677FF, #0E5FD8);
    color: #ffffff;
    font-size: 28rpx;
    border-radius: 40rpx;
  }
}

.load-more, .load-end {
  text-align: center;
  font-size: 26rpx;
  color: #999999;
  padding: 30rpx 0;
}

/* 自定义导航栏样式 */
.custom-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  padding: 0 30rpx;
  padding-top: 44px; /* 状态栏高度 */
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  background-image: linear-gradient(135deg, #0066FF, #0052CC); /* 改为与发布页一致的渐变角度 */
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.15);
  z-index: 100; /* 提高z-index确保在最上层 */
}

.navbar-title {
  position: absolute;
  left: 0;
  right: 0;
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 700;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  text-align: center;
}

.navbar-right {
  width: 40rpx;
  height: 40rpx;
}

.navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 20; /* 确保在标题上层，可以被点击 */
}

.back-icon {
  width: 100%;
  height: 100%;
}

.safe-area-top {
  height: var(--status-bar-height);
  width: 100%;
  background-image: linear-gradient(135deg, #0066FF, #0052CC);
}
</style>