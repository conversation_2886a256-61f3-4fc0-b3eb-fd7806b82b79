<template>
  <view class="channels-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">分销员渠道</text>
      <view class="navbar-right">
        <view class="add-icon" @click="addChannel">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 5V19M5 12H19" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </view>
      </view>
    </view>
    
    <!-- 渠道数据概览 -->
    <view class="overview-section">
      <view class="overview-header">
        <text class="overview-title">渠道数据概览</text>
        <view class="date-filter" @click="showDatePicker">
          <text>{{dateRange}}</text>
          <view class="filter-arrow">
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M6 9L12 15L18 9" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
        </view>
      </view>
      
      <!-- 数据卡片 -->
      <view class="data-cards">
        <view class="data-card">
          <view class="card-value">{{channelStats.totalChannels}}</view>
          <view class="card-label">分销员总数</view>
          <view class="card-trend" :class="channelStats.channelsTrend">
            <view class="trend-icon"></view>
            <text>{{channelStats.channelsGrowth}}</text>
          </view>
          <view class="card-icon distributor-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M9 11C11.2091 11 13 9.20914 13 7C13 4.79086 11.2091 3 9 3C6.79086 3 5 4.79086 5 7C5 9.20914 6.79086 11 9 11Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M23 21V19C22.9993 18.1137 22.7044 17.2528 22.1614 16.5523C21.6184 15.8519 20.8581 15.3516 20 15.13" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M16 3.13C16.8604 3.35031 17.623 3.85071 18.1676 4.55232C18.7122 5.25392 19.0078 6.11683 19.0078 7.005C19.0078 7.89318 18.7122 8.75608 18.1676 9.45769C17.623 10.1593 16.8604 10.6597 16 10.88" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
        </view>
        
        <view class="data-card">
          <view class="card-value">{{channelStats.activeChannels}}</view>
          <view class="card-label">活跃分销员</view>
          <view class="card-trend" :class="channelStats.activeTrend">
            <view class="trend-icon"></view>
            <text>{{channelStats.activeGrowth}}</text>
          </view>
          <view class="card-icon active-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M16 21V19C16 17.9391 15.5786 16.9217 14.8284 16.1716C14.0783 15.4214 13.0609 15 12 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21" stroke="#34C759" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M8.5 11C10.7091 11 12.5 9.20914 12.5 7C12.5 4.79086 10.7091 3 8.5 3C6.29086 3 4.5 4.79086 4.5 7C4.5 9.20914 6.29086 11 8.5 11Z" stroke="#34C759" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M20 8V14" stroke="#34C759" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M23 11H17" stroke="#34C759" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
        </view>
        
        <view class="data-card">
          <view class="card-value">¥{{formatNumber(channelStats.totalCommission)}}</view>
          <view class="card-label">佣金总额</view>
          <view class="card-trend" :class="channelStats.commissionTrend">
            <view class="trend-icon"></view>
            <text>{{channelStats.commissionGrowth}}</text>
          </view>
          <view class="card-icon commission-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 1V23M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" stroke="#FF9500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
        </view>
        
        <view class="data-card">
          <view class="card-value">{{channelStats.totalOrders}}</view>
          <view class="card-label">订单数量</view>
          <view class="card-trend" :class="channelStats.ordersTrend">
            <view class="trend-icon"></view>
            <text>{{channelStats.ordersGrowth}}</text>
          </view>
          <view class="card-icon orders-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 17H15M9 13H15M9 9H15M5 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3H5C3.89543 3 3 3.89543 3 5V19C3 20.1046 3.89543 21 5 21Z" stroke="#007AFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 渠道筛选和搜索 -->
    <view class="filter-section">
      <view class="search-bar">
        <view class="search-icon">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z" stroke="#999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M21 21L16.65 16.65" stroke="#999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </view>
        <input type="text" class="search-input" placeholder="搜索分销员" v-model="searchKeyword" @input="handleSearch" />
      </view>
      
      <view class="filter-tabs">
        <view 
          v-for="(tab, index) in filterTabs" 
          :key="index" 
          class="filter-tab" 
          :class="{ active: currentTab === tab.value }"
          @click="switchTab(tab.value)"
        >
          <text>{{tab.name}}</text>
        </view>
      </view>
    </view>
    
    <!-- 分销员列表 -->
    <view class="channels-list-section">
      <view 
        v-for="(channel, index) in filteredChannels" 
        :key="index" 
        class="channel-item"
        @click="viewChannelDetail(channel)"
      >
        <view class="channel-avatar-container">
          <image class="channel-avatar" :src="channel.avatar" mode="aspectFill"></image>
          <view class="channel-status" :class="channel.status"></view>
        </view>
        
        <view class="channel-info">
          <view class="channel-header">
            <text class="channel-name">{{channel.name}}</text>
            <view class="channel-level" :class="'level-' + channel.level">{{channel.levelName}}</view>
          </view>
          
          <view class="channel-stats">
            <view class="stat-item">
              <text class="stat-value">{{channel.orders}}</text>
              <text class="stat-label">推广订单</text>
            </view>
            <view class="stat-item">
              <text class="stat-value">¥{{channel.commission}}</text>
              <text class="stat-label">佣金收益</text>
            </view>
            <view class="stat-item">
              <text class="stat-value">{{channel.fans}}</text>
              <text class="stat-label">粉丝数量</text>
            </view>
          </view>
          
          <view class="channel-actions">
            <view class="action-btn view-qrcode" @click.stop="viewQrcode(channel)">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 11V3H11V11H3ZM3 21V13H11V21H3ZM13 11V3H21V11H13ZM13 21V13H21V21H13ZM5 9H9V5H5V9ZM15 9H19V5H15V9ZM5 19H9V15H5V19ZM15 19H19V15H15V19Z" stroke="#6B0FBE" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <text>查看二维码</text>
            </view>
            <view class="action-btn edit-channel" @click.stop="editChannel(channel)">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="#6B0FBE" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M18.5 2.5C18.8978 2.10217 19.4374 1.87868 20 1.87868C20.5626 1.87868 21.1022 2.10217 21.5 2.5C21.8978 2.89782 22.1213 3.43739 22.1213 4C22.1213 4.56261 21.8978 5.10217 21.5 5.5L12 15L8 16L9 12L18.5 2.5Z" stroke="#6B0FBE" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <text>编辑</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 空状态展示 -->
      <view class="empty-state" v-if="filteredChannels.length === 0">
        <image class="empty-image" src="/static/images/empty-state.png" mode="aspectFit"></image>
        <text class="empty-text">暂无符合条件的分销员</text>
        <button class="empty-btn" @click="addChannel">添加分销员</button>
      </view>
    </view>
    
    <!-- 新建渠道浮动按钮 -->
    <view class="floating-btn" @click="addChannel">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 5V19M5 12H19" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';

// 日期范围
const dateRange = ref('2023-04-01 ~ 2023-04-30');

// 渠道统计数据
const channelStats = reactive({
  totalChannels: 248,
  channelsTrend: 'up',
  channelsGrowth: '12%',
  
  activeChannels: 186,
  activeTrend: 'up',
  activeGrowth: '8%',
  
  totalCommission: 15820.50,
  commissionTrend: 'up',
  commissionGrowth: '15%',
  
  totalOrders: 1245,
  ordersTrend: 'down',
  ordersGrowth: '3%'
});

// 格式化数字
const formatNumber = (num) => {
  return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

// 显示日期选择器
const showDatePicker = () => {
  // 日期选择逻辑
  uni.showToast({
    title: '日期筛选功能开发中',
    icon: 'none'
  });
};

// 基础方法
const goBack = () => {
  uni.navigateBack();
};

const addChannel = () => {
  uni.navigateTo({
    url: '/subPackages/merchant-admin-marketing/pages/distribution/channels/add'
  });
};

// 筛选标签
const filterTabs = ref([
  { name: '全部', value: 'all' },
  { name: '活跃', value: 'active' },
  { name: '新增', value: 'new' },
  { name: '休眠', value: 'inactive' }
]);

const currentTab = ref('all');
const searchKeyword = ref('');

// 渠道列表数据
const channels = reactive([
  {
    id: '1001',
    name: '张小明',
    avatar: '/static/images/avatars/avatar1.png',
    status: 'online',
    level: 3,
    levelName: '钻石会员',
    orders: 89,
    commission: '4,526.50',
    fans: 245,
    joinTime: '2023-03-15'
  },
  {
    id: '1002',
    name: '王丽丽',
    avatar: '/static/images/avatars/avatar2.png',
    status: 'online',
    level: 2,
    levelName: '黄金会员',
    orders: 56,
    commission: '2,830.80',
    fans: 128,
    joinTime: '2023-03-18'
  },
  {
    id: '1003',
    name: '李大壮',
    avatar: '/static/images/avatars/avatar3.png',
    status: 'offline',
    level: 1,
    levelName: '白银会员',
    orders: 34,
    commission: '1,254.30',
    fans: 76,
    joinTime: '2023-03-22'
  },
  {
    id: '1004',
    name: '赵小红',
    avatar: '/static/images/avatars/avatar4.png',
    status: 'online',
    level: 2,
    levelName: '黄金会员',
    orders: 42,
    commission: '2,102.60',
    fans: 94,
    joinTime: '2023-03-25'
  },
  {
    id: '1005',
    name: '刘伟',
    avatar: '/static/images/avatars/avatar5.png',
    status: 'offline',
    level: 1,
    levelName: '白银会员',
    orders: 28,
    commission: '986.40',
    fans: 52,
    joinTime: '2023-04-01'
  }
]);

// 根据筛选条件过滤渠道
const filteredChannels = computed(() => {
  let result = [...channels];
  
  // 根据标签筛选
  if (currentTab.value !== 'all') {
    if (currentTab.value === 'active') {
      result = result.filter(item => item.status === 'online');
    } else if (currentTab.value === 'inactive') {
      result = result.filter(item => item.status === 'offline');
    } else if (currentTab.value === 'new') {
      // 假设14天内为新增
      const twoWeeksAgo = new Date();
      twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);
      result = result.filter(item => {
        const joinDate = new Date(item.joinTime);
        return joinDate >= twoWeeksAgo;
      });
    }
  }
  
  // 根据关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    result = result.filter(item => 
      item.name.toLowerCase().includes(keyword) || 
      item.id.includes(keyword)
    );
  }
  
  return result;
});

// 切换筛选标签
const switchTab = (tab) => {
  currentTab.value = tab;
};

// 搜索处理
const handleSearch = () => {
  // 实时搜索，无需额外处理，computed会自动更新
};

// 查看渠道详情
const viewChannelDetail = (channel) => {
  uni.navigateTo({
    url: `/subPackages/merchant-admin-marketing/pages/distribution/channels/detail?id=${channel.id}`
  });
};

// 查看二维码
const viewQrcode = (channel) => {
  uni.navigateTo({
    url: `/subPackages/merchant-admin-marketing/pages/distribution/qrcode/index?channelId=${channel.id}&channelName=${encodeURIComponent(channel.name)}`
  });
};

// 编辑渠道
const editChannel = (channel) => {
  uni.navigateTo({
    url: `/subPackages/merchant-admin-marketing/pages/distribution/channels/edit?id=${channel.id}`
  });
};
</script>

<style lang="scss">
.channels-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 概览部分样式 */
.overview-section {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.overview-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.date-filter {
  display: flex;
  align-items: center;
  background-color: #F5F7FA;
  padding: 6px 10px;
  border-radius: 15px;
  font-size: 12px;
  color: #6B0FBE;
}

.filter-arrow {
  margin-left: 4px;
  display: flex;
  align-items: center;
}

/* 数据卡片样式 */
.data-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.data-card {
  position: relative;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.card-value {
  font-size: 22px;
  font-weight: 700;
  color: #333;
  margin-bottom: 4px;
}

.card-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}

.card-trend {
  display: flex;
  align-items: center;
  font-size: 12px;
  
  &.up {
    color: #34C759;
    
    .trend-icon {
      width: 0;
      height: 0;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
      border-bottom: 6px solid #34C759;
      margin-right: 4px;
    }
  }
  
  &.down {
    color: #FF3B30;
    
    .trend-icon {
      width: 0;
      height: 0;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
      border-top: 6px solid #FF3B30;
      margin-right: 4px;
    }
  }
}

.card-icon {
  position: absolute;
  top: 14px;
  right: 14px;
  opacity: 0.1;
  transform: scale(1.5);
  transform-origin: top right;
}

/* 筛选部分样式 */
.filter-section {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: #F5F7FA;
  border-radius: 12px;
  padding: 0 12px;
  margin-bottom: 16px;
}

.search-icon {
  margin-right: 8px;
}

.search-input {
  flex: 1;
  height: 40px;
  font-size: 14px;
  background-color: transparent;
  border: none;
  color: #333;
}

.filter-tabs {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #F0F0F0;
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 12px 0;
  font-size: 14px;
  color: #999;
  position: relative;
  
  &.active {
    color: #6B0FBE;
    font-weight: 500;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -1px;
      left: 25%;
      width: 50%;
      height: 3px;
      background-color: #6B0FBE;
      border-radius: 3px;
    }
  }
}

/* 渠道列表样式 */
.channels-list-section {
  margin: 16px;
}

.channel-item {
  background-color: #FFFFFF;
  border-radius: 20px;
  margin-bottom: 16px;
  padding: 16px;
  display: flex;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.03);
}

.channel-avatar-container {
  position: relative;
  margin-right: 16px;
}

.channel-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 2px solid #FFFFFF;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.channel-status {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  border: 2px solid #FFFFFF;
  
  &.online {
    background-color: #34C759;
  }
  
  &.offline {
    background-color: #999999;
  }
}

.channel-info {
  flex: 1;
}

.channel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.channel-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.channel-level {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  
  &.level-1 {
    background-color: #E5E5EA;
    color: #8E8E93;
  }
  
  &.level-2 {
    background-color: #FFD700;
    color: #8B6914;
  }
  
  &.level-3 {
    background-color: #B9F2FF;
    color: #007AFF;
  }
}

.channel-stats {
  display: flex;
  margin: 12px 0;
}

.stat-item {
  flex: 1;
  text-align: center;
  border-right: 1px solid #EEEEEE;
  
  &:last-child {
    border-right: none;
  }
}

.stat-value {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.channel-actions {
  display: flex;
  margin-top: 12px;
  border-top: 1px solid #EEEEEE;
  padding-top: 12px;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 13px;
  
  svg {
    margin-right: 4px;
  }
  
  &.view-qrcode {
    color: #6B0FBE;
  }
  
  &.edit-channel {
    color: #6B0FBE;
    border-left: 1px solid #EEEEEE;
  }
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 14px;
  color: #999;
  margin-bottom: 16px;
}

.empty-btn {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #FFFFFF;
  border: none;
  border-radius: 20px;
  padding: 8px 20px;
  font-size: 14px;
}

/* 浮动按钮 */
.floating-btn {
  position: fixed;
  right: 24px;
  bottom: 40px;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(107, 15, 190, 0.3);
  z-index: 90;
}
</style> 