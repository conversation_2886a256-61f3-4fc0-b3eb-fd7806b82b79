"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Array) {
  const _component_cu_custom = common_vendor.resolveComponent("cu-custom");
  _component_cu_custom();
}
const _sfc_main = {
  __name: "agreement",
  setup(__props) {
    const agreeAgreement = () => {
      common_vendor.index.navigateBack();
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          bgColor: "bg-gradient-blue",
          isBack: true
        }),
        b: common_vendor.o(agreeAgreement)
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-495bcd48"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/franchise/pages/agreement.js.map
