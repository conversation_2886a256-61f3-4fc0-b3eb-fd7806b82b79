<template>
  <view class="category-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">分类管理</text>
      <view class="navbar-right">
        <view class="add-icon" @click="showAddModal">
          <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="#fff" stroke-width="2">
            <line x1="12" y1="5" x2="12" y2="19"></line>
            <line x1="5" y1="12" x2="19" y2="12"></line>
          </svg>
        </view>
      </view>
    </view>
    
    <!-- 分类列表 -->
    <view class="category-list" v-if="categories.length > 0">
      <view class="list-header">
        <text class="header-name">分类名称</text>
        <text class="header-count">商品数</text>
        <text class="header-action">操作</text>
      </view>
      
      <view class="list-body">
        <view class="category-item" v-for="(item, index) in categories" :key="item.id">
          <view class="item-drag">
            <svg viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="#999" stroke-width="2">
              <line x1="8" y1="6" x2="16" y2="6"></line>
              <line x1="8" y1="12" x2="16" y2="12"></line>
              <line x1="8" y1="18" x2="16" y2="18"></line>
            </svg>
          </view>
          <view class="item-info">
            <view class="item-name-wrapper">
              <view class="color-dot" :style="{backgroundColor: item.color}"></view>
              <text class="item-name">{{item.name}}</text>
            </view>
            <text class="item-count">{{item.count}}个</text>
          </view>
          <view class="item-actions">
            <text class="action-btn edit" @click="editCategory(item)">编辑</text>
            <text class="action-btn delete" @click="deleteCategory(item.id)">删除</text>
          </view>
        </view>
      </view>
      
      <view class="tips">
        <text>提示：长按拖动可调整分类顺序</text>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <image src="/static/images/shop/empty-category.png" mode="aspectFit" class="empty-image"></image>
      <text class="empty-text">暂无分类，点击右上角添加</text>
      <button class="add-button" @click="showAddModal">添加分类</button>
    </view>
    
    <!-- 添加/编辑分类弹窗 -->
    <uni-popup ref="categoryModal" type="dialog">
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">{{isEditing ? '编辑分类' : '添加分类'}}</text>
          <view class="close-icon" @click="closeModal">
            <svg viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="#999" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </view>
        </view>
        
        <view class="modal-body">
          <view class="form-item">
            <text class="form-label">分类名称</text>
            <input type="text" v-model="categoryForm.name" placeholder="请输入分类名称" maxlength="10" />
            <text class="input-count">{{categoryForm.name.length}}/10</text>
          </view>
          
          <view class="form-item">
            <text class="form-label">分类颜色</text>
            <view class="color-picker">
              <view 
                v-for="(color, index) in colorOptions" 
                :key="index"
                :class="['color-option', {'active': categoryForm.color === color}]"
                :style="{backgroundColor: color}"
                @click="selectColor(color)">
              </view>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">分类图标</text>
            <view class="icon-picker">
              <view class="icon-upload" @click="uploadIcon">
                <image v-if="categoryForm.icon" :src="categoryForm.icon" mode="aspectFit" class="icon-preview"></image>
                <view class="upload-placeholder" v-else>
                  <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4"></path>
                    <polyline points="17 8 12 3 7 8"></polyline>
                    <line x1="12" y1="3" x2="12" y2="15"></line>
                  </svg>
                  <text>上传图标</text>
                </view>
              </view>
              <text class="icon-tip">建议尺寸60x60px，PNG格式</text>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">排序</text>
            <view class="sort-input">
              <input type="number" v-model="categoryForm.sort" placeholder="数字越小越靠前" />
            </view>
          </view>
          
          <view class="form-item">
            <view class="switch-item">
              <text>在前端显示</text>
              <switch :checked="categoryForm.isShow" @change="toggleShow" color="#1677FF" />
            </view>
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="cancel-button" @click="closeModal">取消</button>
          <button class="confirm-button" @click="saveCategory">确定</button>
        </view>
      </view>
    </uni-popup>
    
    <!-- 删除确认弹窗 -->
    <uni-popup ref="deleteModal" type="dialog">
      <uni-popup-dialog 
        title="删除分类" 
        content="确定要删除该分类吗？删除后该分类下的商品将变为无分类状态。" 
        :before-close="true" 
        @confirm="confirmDelete" 
        @close="cancelDelete">
      </uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      categories: [
        { id: 1, name: '热销推荐', color: '#ff4d4f', icon: '/static/images/shop/category-hot.png', count: 12, sort: 1, isShow: true },
        { id: 2, name: '新品上市', color: '#1677FF', icon: '/static/images/shop/category-new.png', count: 8, sort: 2, isShow: true },
        { id: 3, name: '折扣专区', color: '#faad14', icon: '/static/images/shop/category-discount.png', count: 15, sort: 3, isShow: true },
        { id: 4, name: '人气单品', color: '#52c41a', icon: '/static/images/shop/category-popular.png', count: 10, sort: 4, isShow: true },
        { id: 5, name: '季节限定', color: '#722ed1', icon: '/static/images/shop/category-season.png', count: 6, sort: 5, isShow: true }
      ],
      isEditing: false,
      editingId: null,
      categoryForm: {
        name: '',
        color: '#1677FF',
        icon: '',
        sort: 0,
        isShow: true
      },
      colorOptions: [
        '#1677FF', '#52c41a', '#ff4d4f', '#faad14', '#722ed1', 
        '#eb2f96', '#13c2c2', '#fa8c16', '#a0d911', '#1890ff'
      ],
      deleteId: null
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showAddModal() {
      this.isEditing = false;
      this.categoryForm = {
        name: '',
        color: '#1677FF',
        icon: '',
        sort: this.categories.length + 1,
        isShow: true
      };
      this.$refs.categoryModal.open();
    },
    editCategory(category) {
      this.isEditing = true;
      this.editingId = category.id;
      this.categoryForm = {
        name: category.name,
        color: category.color,
        icon: category.icon,
        sort: category.sort,
        isShow: category.isShow
      };
      this.$refs.categoryModal.open();
    },
    closeModal() {
      this.$refs.categoryModal.close();
    },
    selectColor(color) {
      this.categoryForm.color = color;
    },
    toggleShow(e) {
      this.categoryForm.isShow = e.detail.value;
    },
    uploadIcon() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePaths = res.tempFilePaths;
          
          // 模拟上传
          uni.showLoading({
            title: '上传中...'
          });
          
          setTimeout(() => {
            uni.hideLoading();
            this.categoryForm.icon = tempFilePaths[0];
            
            uni.showToast({
              title: '上传成功',
              icon: 'success'
            });
          }, 1000);
        }
      });
    },
    saveCategory() {
      // 表单验证
      if (!this.categoryForm.name) {
        uni.showToast({
          title: '请输入分类名称',
          icon: 'none'
        });
        return;
      }
      
      // 模拟保存
      uni.showLoading({
        title: '保存中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        
        if (this.isEditing) {
          // 编辑现有分类
          const index = this.categories.findIndex(item => item.id === this.editingId);
          if (index !== -1) {
            this.categories[index] = {
              ...this.categories[index],
              name: this.categoryForm.name,
              color: this.categoryForm.color,
              icon: this.categoryForm.icon,
              sort: this.categoryForm.sort,
              isShow: this.categoryForm.isShow
            };
          }
        } else {
          // 添加新分类
          const newCategory = {
            id: Date.now(),
            name: this.categoryForm.name,
            color: this.categoryForm.color,
            icon: this.categoryForm.icon,
            sort: this.categoryForm.sort,
            isShow: this.categoryForm.isShow,
            count: 0
          };
          
          this.categories.push(newCategory);
        }
        
        // 按排序重新排列
        this.categories.sort((a, b) => a.sort - b.sort);
        
        uni.showToast({
          title: this.isEditing ? '编辑成功' : '添加成功',
          icon: 'success'
        });
        
        this.$refs.categoryModal.close();
      }, 1000);
    },
    deleteCategory(id) {
      this.deleteId = id;
      this.$refs.deleteModal.open();
    },
    confirmDelete() {
      if (!this.deleteId) return;
      
      // 模拟删除
      uni.showLoading({
        title: '删除中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        
        this.categories = this.categories.filter(item => item.id !== this.deleteId);
        
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        });
        
        this.deleteId = null;
      }, 1000);
    },
    cancelDelete() {
      this.deleteId = null;
    }
  }
}
</script>

<style>
.category-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-icon {
  width: 24px;
  height: 24px;
  color: #fff;
}

.category-list {
  margin: 16px;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.list-header {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: #f9f9f9;
  border-bottom: 1px solid #f0f0f0;
}

.header-name {
  flex: 1;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.header-count {
  width: 80px;
  font-size: 14px;
  color: #666;
  text-align: center;
  font-weight: 500;
}

.header-action {
  width: 120px;
  font-size: 14px;
  color: #666;
  text-align: center;
  font-weight: 500;
}

.list-body {
  
}

.category-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.category-item:last-child {
  border-bottom: none;
}

.item-drag {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  cursor: move;
}

.item-info {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.item-name-wrapper {
  display: flex;
  align-items: center;
}

.color-dot {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  margin-right: 8px;
}

.item-name {
  font-size: 14px;
  color: #333;
}

.item-count {
  font-size: 14px;
  color: #999;
  margin-right: 16px;
}

.item-actions {
  width: 120px;
  display: flex;
  justify-content: space-around;
}

.action-btn {
  font-size: 14px;
  padding: 4px 8px;
}

.action-btn.edit {
  color: #1677FF;
}

.action-btn.delete {
  color: #ff4d4f;
}

.tips {
  padding: 12px 16px;
  background-color: #f9f9f9;
  border-top: 1px solid #f0f0f0;
}

.tips text {
  font-size: 12px;
  color: #999;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 16px;
}

.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 14px;
  color: #999;
  margin-bottom: 24px;
}

.add-button {
  width: 160px;
  height: 40px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  color: #fff;
  border-radius: 20px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 弹窗样式 */
.modal-content {
  width: 300px;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.close-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 16px;
}

.form-item {
  margin-bottom: 16px;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  display: block;
}

input {
  width: 100%;
  height: 40px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
  background-color: #f9f9f9;
}

.input-count {
  font-size: 12px;
  color: #999;
  text-align: right;
  margin-top: 4px;
  display: block;
}

.color-picker {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.color-option {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  cursor: pointer;
  position: relative;
  border: 2px solid transparent;
}

.color-option.active {
  border-color: #333;
}

.color-option.active:after {
  content: "";
  position: absolute;
  width: 10px;
  height: 5px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(-45deg);
  top: 6px;
  left: 5px;
}

.icon-picker {
  
}

.icon-upload {
  width: 80px;
  height: 80px;
  border: 1px dashed #ddd;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.icon-preview {
  width: 100%;
  height: 100%;
}

.upload-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.upload-placeholder text {
  font-size: 12px;
  margin-top: 4px;
}

.icon-tip {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
  display: block;
}

.sort-input {
  
}

.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.switch-item text {
  font-size: 14px;
  color: #333;
}

.modal-footer {
  display: flex;
  border-top: 1px solid #f0f0f0;
}

.cancel-button {
  flex: 1;
  height: 50px;
  background-color: #fff;
  color: #666;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0;
}

.confirm-button {
  flex: 1;
  height: 50px;
  background-color: #1677FF;
  color: #fff;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0;
}
</style> 