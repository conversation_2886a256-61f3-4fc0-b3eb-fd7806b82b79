<template>
  <view class="scan-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">扫码核销</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 扫码区域 -->
    <view class="scan-area">
      <view class="scan-box">
        <camera device-position="back" flash="auto" @error="handleCameraError" class="scan-camera"></camera>
        <cover-image class="scan-frame" src="/static/images/scan-frame.png"></cover-image>
        <cover-view class="scan-line"></cover-view>
      </view>
      <text class="scan-tip">请将核销码放入框内，即可自动扫描</text>
    </view>
    
    <!-- 快捷操作 -->
    <view class="quick-actions">
      <view class="action-btn" @click="toggleFlash">
        <view class="action-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M9 18h6M12 6V2M7.5 10.5L5 8M16.5 10.5L19 8M12 18a6 6 0 0 0 0-12 6 6 0 0 0 0 12z"></path>
          </svg>
        </view>
        <text class="action-text">开启闪光灯</text>
      </view>
      <view class="action-btn" @click="navigateToManual">
        <view class="action-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
          </svg>
        </view>
        <text class="action-text">手动输入</text>
      </view>
    </view>
    
    <!-- 扫码成功弹窗 -->
    <view class="verification-popup" v-if="showVerificationPopup">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">核销信息</text>
          <view class="popup-close" @click="closePopup">×</view>
        </view>
        
        <view class="verification-info">
          <view class="info-item">
            <text class="info-label">核销类型</text>
            <text class="info-value">{{verificationData.type}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">商品名称</text>
            <text class="info-value">{{verificationData.name}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">用户信息</text>
            <text class="info-value">{{verificationData.user}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">核销码</text>
            <text class="info-value">{{verificationData.code}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">有效期至</text>
            <text class="info-value">{{verificationData.expiry}}</text>
          </view>
        </view>
        
        <view class="verification-actions">
          <button class="btn-cancel" @click="closePopup">取消</button>
          <button class="btn-confirm" @click="confirmVerification">确认核销</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      flashOn: false,
      showVerificationPopup: false,
      verificationData: {
        type: '',
        name: '',
        user: '',
        code: '',
        expiry: ''
      }
    }
  },
  onLoad() {
    // 模拟扫码结果，实际项目中应该通过摄像头扫码获取
    setTimeout(() => {
      this.handleScanResult({
        type: '拼团活动',
        name: '双人下午茶套餐拼团',
        user: '张三 (138****8888)',
        code: 'GP20230618001',
        expiry: '2023-06-25 23:59:59'
      });
    }, 3000);
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showHelp() {
      uni.showToast({
        title: '扫码核销帮助',
        icon: 'none'
      });
    },
    handleCameraError(e) {
      uni.showToast({
        title: '摄像头启动失败，请检查权限设置',
        icon: 'none'
      });
      console.error('相机错误:', e);
    },
    toggleFlash() {
      this.flashOn = !this.flashOn;
      uni.showToast({
        title: this.flashOn ? '闪光灯已开启' : '闪光灯已关闭',
        icon: 'none'
      });
    },
    navigateToManual() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/verification/manual'
      });
    },
    handleScanResult(result) {
      this.verificationData = result;
      this.showVerificationPopup = true;
    },
    closePopup() {
      this.showVerificationPopup = false;
    },
    confirmVerification() {
      uni.showLoading({
        title: '核销中...'
      });
      
      // 模拟核销请求
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '核销成功',
          icon: 'success'
        });
        
        // 关闭弹窗
        this.closePopup();
        
        // 延迟返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }, 1000);
    }
  }
}
</script>

<style lang="scss">
.scan-container {
  min-height: 100vh;
  background-color: #000;
  position: relative;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 扫码区域样式 */
.scan-area {
  padding: 30px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.scan-box {
  width: 280px;
  height: 280px;
  position: relative;
  margin-bottom: 25px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.scan-camera {
  width: 100%;
  height: 100%;
}

.scan-frame {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.scan-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(to right, transparent, #27ae60, transparent);
  box-shadow: 0 0 10px #27ae60;
  animation: scanAnimation 2s linear infinite;
}

@keyframes scanAnimation {
  0% {
    top: 0;
  }
  50% {
    top: 100%;
  }
  100% {
    top: 0;
  }
}

.scan-tip {
  color: #fff;
  font-size: 15px;
  margin-top: 25px;
  text-align: center;
  font-weight: 500;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* 快捷操作样式 */
.quick-actions {
  position: absolute;
  bottom: 50px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  padding: 0 20px;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 35px;
}

.action-icon {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s, box-shadow 0.2s;
}

.action-icon:active {
  transform: scale(0.95);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.action-icon svg {
  width: 28px;
  height: 28px;
  color: #fff;
}

.action-text {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* 核销弹窗样式 */
.verification-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-content {
  width: 85%;
  background: #fff;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: #f8f8f8;
}

.popup-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.popup-close {
  font-size: 22px;
  color: #777;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 18px;
}

.popup-close:active {
  background: rgba(0, 0, 0, 0.05);
}

.verification-info {
  padding: 20px;
}

.info-item {
  display: flex;
  margin-bottom: 16px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 90px;
  font-size: 15px;
  color: #777;
}

.info-value {
  flex: 1;
  font-size: 15px;
  color: #333;
  font-weight: 500;
}

.verification-actions {
  display: flex;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.btn-cancel, .btn-confirm {
  flex: 1;
  height: 56px;
  line-height: 56px;
  text-align: center;
  font-size: 17px;
  font-weight: 500;
  border: none;
  border-radius: 0;
}

.btn-cancel {
  background: #f8f8f8;
  color: #666;
}

.btn-confirm {
  background: #27ae60;
  color: #fff;
}
</style>
