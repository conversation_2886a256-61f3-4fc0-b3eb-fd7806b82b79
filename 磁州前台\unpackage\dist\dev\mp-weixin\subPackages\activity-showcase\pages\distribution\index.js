"use strict";
const common_vendor = require("../../../../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_rect = common_vendor.resolveComponent("rect");
  (_component_path + _component_svg + _component_circle + _component_line + _component_rect)();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const currentTabBar = common_vendor.ref("distribution");
    const unreadMessageCount = common_vendor.ref(3);
    const distributorInfo = common_vendor.ref({
      name: "张三",
      id: "D0001",
      avatar: "https://via.placeholder.com/100",
      isVerified: true,
      level: "初级分销商",
      nextLevel: "中级分销商",
      promotedDays: 30,
      teamCount: 10,
      upgradeProgress: 75,
      ordersToUpgrade: 20,
      totalEarnings: 1234.56,
      pendingEarnings: 123.45,
      withdrawableAmount: 1111.11,
      todayEarnings: 23.45,
      earningsTrend: 15
    });
    const orderStats = common_vendor.ref({
      totalCount: 150,
      todayCount: 20,
      pendingCount: 10,
      completedCount: 120
    });
    const posterStats = common_vendor.ref({
      count: 8
    });
    const productStats = common_vendor.ref({
      count: 80
    });
    const teamStats = common_vendor.ref({
      totalMembers: 100,
      directMembers: 30,
      indirectMembers: 70,
      newMembers: 15
    });
    const currentOrderTab = common_vendor.ref(0);
    const orderTabs = common_vendor.ref([
      { name: "全部", status: "all" },
      { name: "待付款", status: "pending_payment" },
      { name: "待发货", status: "pending_delivery" },
      { name: "已发货", status: "delivered" },
      { name: "已完成", status: "completed" },
      { name: "已失效", status: "invalid" }
    ]);
    const teamMembers = common_vendor.ref([
      { id: "M001", name: "李四", avatar: "https://via.placeholder.com/50", level: "初级分销商", joinTime: "2023-01-01", contribution: 123.45 },
      { id: "M002", name: "王五", avatar: "https://via.placeholder.com/50", level: "中级分销商", joinTime: "2023-02-15", contribution: 234.56 },
      { id: "M003", name: "赵六", avatar: "https://via.placeholder.com/50", level: "高级分销商", joinTime: "2023-03-01", contribution: 345.67 }
    ]);
    const navigateBack = () => {
      common_vendor.index.navigateBack();
    };
    const showHelp = () => {
      common_vendor.index.showToast({
        title: "分销规则说明",
        icon: "none"
      });
    };
    const showQrcode = () => {
      common_vendor.index.showToast({
        title: "显示推广二维码",
        icon: "none"
      });
    };
    const showEarningsTip = () => {
      common_vendor.index.showToast({
        title: "累计收益包括已结算和未结算的总收益",
        icon: "none"
      });
    };
    const showInviteQrcode = () => {
      common_vendor.index.showToast({
        title: "显示邀请二维码",
        icon: "none"
      });
    };
    const switchOrderTab = (index) => {
      currentOrderTab.value = index;
    };
    const getOrderStatusColor = (status) => {
      switch (status) {
        case "pending_payment":
          return "#FF9500";
        case "pending_delivery":
          return "#5AC8FA";
        case "delivered":
          return "#34C759";
        case "completed":
          return "#AC39FF";
        case "invalid":
          return "#8E8E93";
        default:
          return "#333333";
      }
    };
    const getOrderStatusText = (status) => {
      switch (status) {
        case "pending_payment":
          return "待付款";
        case "pending_delivery":
          return "待发货";
        case "delivered":
          return "已发货";
        case "completed":
          return "已完成";
        case "invalid":
          return "已失效";
        default:
          return "未知状态";
      }
    };
    const getLevelColor = (level) => {
      switch (level) {
        case "初级分销商":
          return "linear-gradient(135deg, #5AC8FA 0%, #1C84FF 100%)";
        case "中级分销商":
          return "linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)";
        case "高级分销商":
          return "linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)";
        default:
          return "linear-gradient(135deg, #8E8E93 0%, #C7C7CC 100%)";
      }
    };
    const getOrdersByStatus = (status) => {
      const allOrders = [
        {
          id: "O001",
          orderNumber: "20230101001",
          status: "completed",
          productName: "iPhone 14 Pro 深空黑 256G",
          productImage: "https://via.placeholder.com/100",
          productPrice: 7999,
          quantity: 1,
          orderTime: "2023-01-01 12:00:00",
          commission: 399.95
        },
        {
          id: "O002",
          orderNumber: "20230102001",
          status: "pending_payment",
          productName: "华为Mate 50 Pro 曜金黑 512G",
          productImage: "https://via.placeholder.com/100",
          productPrice: 6999,
          quantity: 1,
          orderTime: "2023-01-02 12:00:00",
          commission: 349.95
        },
        {
          id: "O003",
          orderNumber: "20230103001",
          status: "pending_delivery",
          productName: "小米12S Ultra 陶瓷白 256G",
          productImage: "https://via.placeholder.com/100",
          productPrice: 5999,
          quantity: 1,
          orderTime: "2023-01-03 12:00:00",
          commission: 299.95
        },
        {
          id: "O004",
          orderNumber: "20230104001",
          status: "delivered",
          productName: "OPPO Find X5 Pro 陶瓷白 256G",
          productImage: "https://via.placeholder.com/100",
          productPrice: 4999,
          quantity: 1,
          orderTime: "2023-01-04 12:00:00",
          commission: 249.95
        },
        {
          id: "O005",
          orderNumber: "20230105001",
          status: "invalid",
          productName: "三星Galaxy S22 Ultra 幻影黑 512G",
          productImage: "https://via.placeholder.com/100",
          productPrice: 8999,
          quantity: 1,
          orderTime: "2023-01-05 12:00:00",
          commission: 449.95
        }
      ];
      if (status === "all") {
        return allOrders;
      }
      return allOrders.filter((order) => order.status === status);
    };
    const navigateTo = (url) => {
      common_vendor.index.navigateTo({ url });
    };
    const switchTabBar = (tab) => {
      if (currentTabBar.value === tab)
        return;
      currentTabBar.value = tab;
      switch (tab) {
        case "home":
          common_vendor.index.reLaunch({
            url: "/subPackages/activity-showcase/pages/index/index",
            fail: (err) => {
              common_vendor.index.__f__("error", "at subPackages/activity-showcase/pages/distribution/index.vue:685", "页面跳转失败:", err);
              common_vendor.index.showToast({
                title: "页面跳转失败",
                icon: "none"
              });
            }
          });
          break;
        case "discover":
          common_vendor.index.navigateTo({
            url: "/subPackages/activity-showcase/pages/discover/index",
            fail: (err) => {
              common_vendor.index.__f__("error", "at subPackages/activity-showcase/pages/distribution/index.vue:697", "页面跳转失败:", err);
              common_vendor.index.showToast({
                title: "页面跳转失败",
                icon: "none"
              });
            }
          });
          break;
        case "distribution":
          break;
        case "message":
          common_vendor.index.navigateTo({
            url: "/subPackages/activity-showcase/pages/message/index",
            fail: (err) => {
              common_vendor.index.__f__("error", "at subPackages/activity-showcase/pages/distribution/index.vue:712", "页面跳转失败:", err);
              common_vendor.index.showToast({
                title: "页面跳转失败",
                icon: "none"
              });
            }
          });
          break;
        case "my":
          common_vendor.index.navigateTo({
            url: "/subPackages/activity-showcase/pages/my/index",
            fail: (err) => {
              common_vendor.index.__f__("error", "at subPackages/activity-showcase/pages/distribution/index.vue:724", "页面跳转失败:", err);
              common_vendor.index.showToast({
                title: "页面跳转失败",
                icon: "none"
              });
            }
          });
          break;
      }
    };
    const loadMore = () => {
    };
    common_vendor.onMounted(() => {
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          d: "M19 12H5M12 19l-7-7 7-7",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        b: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        c: common_vendor.o(navigateBack),
        d: common_vendor.p({
          cx: "12",
          cy: "12",
          r: "10",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        e: common_vendor.p({
          d: "M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        f: common_vendor.p({
          x1: "12",
          y1: "17",
          x2: "12.01",
          y2: "17",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        g: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        h: common_vendor.o(showHelp),
        i: distributorInfo.value.avatar,
        j: distributorInfo.value.isVerified
      }, distributorInfo.value.isVerified ? {
        k: common_vendor.p({
          d: "M9 12l2 2 4-4",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        l: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "12",
          height: "12"
        })
      } : {}, {
        m: common_vendor.t(distributorInfo.value.name),
        n: common_vendor.t(distributorInfo.value.level),
        o: common_vendor.t(distributorInfo.value.id),
        p: common_vendor.t(distributorInfo.value.promotedDays),
        q: common_vendor.t(distributorInfo.value.teamCount),
        r: common_vendor.p({
          d: "M3 3h7v7H3z M14 3h7v7h-7z M3 14h7v7H3z M17 17h2v2h-2z M14 14h2v2h-2z M19 14h2v2h-2z M14 19h7v2h-7z",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        s: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "20",
          height: "20"
        }),
        t: common_vendor.o(showQrcode),
        v: common_vendor.t(distributorInfo.value.level),
        w: common_vendor.t(distributorInfo.value.nextLevel),
        x: distributorInfo.value.upgradeProgress + "%",
        y: common_vendor.t(distributorInfo.value.ordersToUpgrade),
        z: common_vendor.t(distributorInfo.value.nextLevel),
        A: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#AC39FF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        B: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        C: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/distribution/earnings/index")),
        D: common_vendor.p({
          cx: "12",
          cy: "12",
          r: "10",
          stroke: "#8E8E93",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        E: common_vendor.p({
          d: "M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",
          stroke: "#8E8E93",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        F: common_vendor.p({
          x1: "12",
          y1: "17",
          x2: "12.01",
          y2: "17",
          stroke: "#8E8E93",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        G: common_vendor.o(showEarningsTip),
        H: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        I: common_vendor.t(distributorInfo.value.totalEarnings),
        J: distributorInfo.value.earningsTrend > 0
      }, distributorInfo.value.earningsTrend > 0 ? {
        K: common_vendor.t(distributorInfo.value.earningsTrend)
      } : {
        L: common_vendor.t(distributorInfo.value.earningsTrend)
      }, {
        M: common_vendor.t(distributorInfo.value.todayEarnings),
        N: common_vendor.t(distributorInfo.value.pendingEarnings),
        O: common_vendor.t(distributorInfo.value.withdrawableAmount),
        P: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/distribution/withdraw/index")),
        Q: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/distribution/records/index")),
        R: common_vendor.p({
          d: "M9 17h6M9 13h6M9 9h6M5 21V5a2 2 0 012-2h10a2 2 0 012 2v16l-3-2-2 2-2-2-2 2-2-2-3 2z",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        S: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        T: common_vendor.t(orderStats.value.totalCount),
        U: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/distribution/orders/index")),
        V: common_vendor.p({
          d: "M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2M9 11a4 4 0 100-8 4 4 0 000 8zM23 21v-2a4 4 0 00-3-3.87M16 3.13a4 4 0 010 7.75",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        W: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        X: common_vendor.t(distributorInfo.value.teamCount),
        Y: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/distribution/team/index")),
        Z: common_vendor.p({
          x: "3",
          y: "3",
          width: "18",
          height: "18",
          rx: "2",
          ry: "2",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        aa: common_vendor.p({
          cx: "8.5",
          cy: "8.5",
          r: "1.5",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        ab: common_vendor.p({
          d: "M21 15l-5-5L5 21",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        ac: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        ad: common_vendor.t(posterStats.value.count),
        ae: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/distribution/poster/index")),
        af: common_vendor.p({
          cx: "9",
          cy: "21",
          r: "1",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        ag: common_vendor.p({
          cx: "20",
          cy: "21",
          r: "1",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        ah: common_vendor.p({
          d: "M1 1h4l2.68 13.39a2 2 0 002 1.61h9.72a2 2 0 002-1.61L23 6H6",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        ai: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        aj: common_vendor.t(productStats.value.count),
        ak: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/distribution/products/index")),
        al: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        am: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        an: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/distribution/orders/index")),
        ao: common_vendor.f(orderTabs.value, (tab, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(tab.name),
            b: currentOrderTab.value === index
          }, currentOrderTab.value === index ? {} : {}, {
            c: index,
            d: currentOrderTab.value === index ? 1 : "",
            e: common_vendor.o(($event) => switchOrderTab(index), index)
          });
        }),
        ap: common_vendor.f(getOrdersByStatus(orderTabs.value[currentOrderTab.value].status), (order, index, i0) => {
          return {
            a: common_vendor.t(order.orderNumber),
            b: common_vendor.t(getOrderStatusText(order.status)),
            c: getOrderStatusColor(order.status),
            d: order.productImage,
            e: common_vendor.t(order.productName),
            f: common_vendor.t(order.productPrice),
            g: common_vendor.t(order.quantity),
            h: common_vendor.t(order.orderTime),
            i: common_vendor.t(order.commission),
            j: order.id,
            k: common_vendor.o(($event) => navigateTo(`/subPackages/activity-showcase/pages/distribution/order-detail/index?id=${order.id}`), order.id)
          };
        }),
        aq: getOrdersByStatus(orderTabs.value[currentOrderTab.value].status).length === 0
      }, getOrdersByStatus(orderTabs.value[currentOrderTab.value].status).length === 0 ? {
        ar: common_vendor.t(orderTabs.value[currentOrderTab.value].name)
      } : {}, {
        as: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        at: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        av: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/distribution/team/index")),
        aw: common_vendor.t(teamStats.value.totalMembers),
        ax: common_vendor.t(teamStats.value.directMembers),
        ay: common_vendor.t(teamStats.value.indirectMembers),
        az: common_vendor.t(teamStats.value.newMembers),
        aA: common_vendor.f(teamMembers.value, (member, index, i0) => {
          return {
            a: member.avatar,
            b: common_vendor.t(member.name),
            c: common_vendor.t(member.level),
            d: getLevelColor(member.level),
            e: common_vendor.t(member.joinTime),
            f: common_vendor.t(member.contribution),
            g: "f7d85fe7-33-" + i0 + "," + ("f7d85fe7-32-" + i0),
            h: "f7d85fe7-32-" + i0,
            i: member.id,
            j: common_vendor.o(($event) => navigateTo(`/subPackages/activity-showcase/pages/distribution/member-detail/index?id=${member.id}`), member.id)
          };
        }),
        aB: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#C7C7CC",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        aC: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        aD: teamMembers.value.length === 0
      }, teamMembers.value.length === 0 ? {
        aE: common_vendor.o(showInviteQrcode)
      } : {}, {
        aF: common_vendor.o(loadMore),
        aG: currentTabBar.value === "home" ? 1 : "",
        aH: common_vendor.o(($event) => switchTabBar("home")),
        aI: currentTabBar.value === "discover" ? 1 : "",
        aJ: common_vendor.o(($event) => switchTabBar("discover")),
        aK: currentTabBar.value === "distribution" ? 1 : "",
        aL: common_vendor.o(($event) => switchTabBar("distribution")),
        aM: unreadMessageCount.value > 0
      }, unreadMessageCount.value > 0 ? {
        aN: common_vendor.t(unreadMessageCount.value > 99 ? "99+" : unreadMessageCount.value)
      } : {}, {
        aO: currentTabBar.value === "message" ? 1 : "",
        aP: common_vendor.o(($event) => switchTabBar("message")),
        aQ: currentTabBar.value === "my" ? 1 : "",
        aR: common_vendor.o(($event) => switchTabBar("my"))
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f7d85fe7"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/distribution/index.js.map
