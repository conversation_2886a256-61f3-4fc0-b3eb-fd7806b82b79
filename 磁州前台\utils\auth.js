const TOKEN_KEY = 'auth_token'
const USER_INFO_KEY = 'user_info'

// 获取 token
export const getToken = () => {
  return uni.getStorageSync(TOKEN_KEY)
}

// 设置 token
export const setToken = (token) => {
  uni.setStorageSync(TOKEN_KEY, token)
}

// 移除 token
export const removeToken = () => {
  uni.removeStorageSync(TOKEN_KEY)
}

// 获取用户信息
export const getUserInfo = () => {
  const userInfo = uni.getStorageSync(USER_INFO_KEY)
  return userInfo ? JSON.parse(userInfo) : null
}

// 设置用户信息
export const setUserInfo = (userInfo) => {
  uni.setStorageSync(USER_INFO_KEY, JSON.stringify(userInfo))
}

// 移除用户信息
export const removeUserInfo = () => {
  uni.removeStorageSync(USER_INFO_KEY)
}

// 检查是否已登录
export const isLoggedIn = () => {
  return !!getToken()
}

// 登出
export const logout = () => {
  removeToken()
  removeUserInfo()
  // 可以在这里添加其他清理操作
}

// 检查登录状态，如果未登录则跳转到登录页
export const checkLogin = () => {
  if (!isLoggedIn()) {
    uni.navigateTo({
      url: '/pages/login/login'
    })
    return false
  }
  return true
} 