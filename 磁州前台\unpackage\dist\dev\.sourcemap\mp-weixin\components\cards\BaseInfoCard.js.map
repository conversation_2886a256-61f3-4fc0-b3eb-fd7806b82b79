{"version": 3, "file": "BaseInfoCard.js", "sources": ["components/cards/BaseInfoCard.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7AvY29tcG9uZW50cy9jYXJkcy9CYXNlSW5mb0NhcmQudnVl"], "sourcesContent": ["<template>\n  <view \n    class=\"info-item\" \n    :class=\"{\n      'topped-item': item.isTopped, \n      'paid-top': item.topType === 'paid',\n      'ad-top': item.topType === 'ad'\n    }\" \n  >\n    <!-- 置顶标识 -->\n    <view class=\"top-indicator\" v-if=\"item.isTopped\">\n      <view class=\"top-badge\" :class=\"{'paid-badge': item.topType === 'paid', 'ad-badge': item.topType === 'ad'}\">\n        <view class=\"top-badge-icon\">\n          <svg v-if=\"item.topType === 'paid'\" xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n            <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n            <path d=\"M16 8l-8 8\"></path>\n            <path d=\"M8 8l8 8\"></path>\n          </svg>\n          <svg v-else xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n            <polygon points=\"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2\"></polygon>\n          </svg>\n        </view>\n        <text class=\"top-badge-text\">{{item.topType === 'paid' ? '付费置顶' : '广告置顶'}}</text>\n      </view>\n    </view>\n    \n    <!-- 信息状态标签 -->\n    <view class=\"info-status\" v-if=\"item.status\">\n      <text class=\"info-status-text\">{{item.status}}</text>\n    </view>\n    \n    <!-- 内容区 -->\n    <view class=\"info-content\">\n      <!-- 头部信息 -->\n      <view class=\"info-header\">\n        <view class=\"info-category-wrapper\">\n          <view class=\"info-category\" :class=\"[item.isTopped ? 'topped-category' : '', getCategoryClass(item.category)]\">\n            <text class=\"category-text\">{{item.category}}</text>\n          </view>\n          <view class=\"info-time\">{{item.time}}</view>\n        </view>\n      </view>\n      \n      <!-- 主要内容 -->\n      <view class=\"info-main\">\n        <view class=\"info-content-wrapper\">\n          <text class=\"info-text\" :class=\"{'bold-text': item.isTopped}\">{{item.content}}</text>\n          \n          <!-- 内容标签 -->\n          <view class=\"info-tags\" v-if=\"item.tags && item.tags.length\">\n            <view class=\"info-tag\" v-for=\"(tag, tagIndex) in item.tags\" :key=\"tagIndex\">\n              <text class=\"info-tag-text\">#{{tag}}</text>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 图片预览 -->\n        <view class=\"info-images-right\" v-if=\"item.images && item.images.length > 0\">\n          <view class=\"info-image-container-right\">\n            <image :src=\"item.images[0]\" class=\"info-image\" mode=\"aspectFill\"></image>\n            <view class=\"image-count-right\" v-if=\"item.images.length > 1\">+{{item.images.length - 1}}</view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 核心数据区域 - 根据不同类型显示不同数据 -->\n      <view class=\"core-data-area\" v-if=\"hasKeyData\">\n        <view class=\"core-data-item\" v-if=\"item.price\">\n          <view class=\"core-data-icon price-icon\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <line x1=\"12\" y1=\"1\" x2=\"12\" y2=\"23\"></line>\n              <path d=\"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"></path>\n            </svg>\n          </view>\n          <text class=\"core-data-value price-value\">¥{{item.price}}<text class=\"price-unit\" v-if=\"item.priceUnit\">{{item.priceUnit}}</text></text>\n        </view>\n        \n        <view class=\"core-data-item\" v-if=\"item.area\">\n          <view class=\"core-data-icon area-icon\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"></rect>\n              <circle cx=\"8.5\" cy=\"8.5\" r=\"1.5\"></circle>\n              <polyline points=\"21 15 16 10 5 21\"></polyline>\n            </svg>\n          </view>\n          <text class=\"core-data-value\">{{item.area}}㎡</text>\n        </view>\n        \n        <view class=\"core-data-item\" v-if=\"item.minSalary || item.maxSalary\">\n          <view class=\"core-data-icon salary-icon\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <rect x=\"2\" y=\"7\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\"></rect>\n              <path d=\"M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16\"></path>\n            </svg>\n          </view>\n          <text class=\"core-data-value salary-value\">{{formatSalary(item.minSalary, item.maxSalary, item.salaryUnit)}}</text>\n        </view>\n        \n        <view class=\"core-data-item\" v-if=\"item.location\">\n          <view class=\"core-data-icon location-icon\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <path d=\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\"></path>\n              <circle cx=\"12\" cy=\"10\" r=\"3\"></circle>\n            </svg>\n          </view>\n          <text class=\"core-data-value\">{{item.location}}</text>\n        </view>\n      </view>\n      \n      <slot name=\"content\"></slot>\n      \n      <!-- 底部信息 -->\n      <view class=\"info-footer\">\n        <view class=\"info-user\">\n          <view class=\"user-avatar-container\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"user-avatar-svg\">\n              <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path>\n              <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\n            </svg>\n          </view>\n          <text class=\"user-name\">{{item.author || '匿名用户'}}</text>\n        </view>\n        \n        <view class=\"info-stats\">\n          <view class=\"info-views\">\n            <view class=\"view-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <path d=\"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"></path>\n                <circle cx=\"12\" cy=\"12\" r=\"3\"></circle>\n              </svg>\n            </view>\n            <text class=\"view-count\">{{item.views || 0}}</text>\n          </view>\n          \n          <!-- 红包信息 -->\n          <view v-if=\"item.hasRedPacket\" class=\"info-redpacket\">\n            <view class=\"redpacket-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <rect x=\"3\" y=\"4\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"></rect>\n                <line x1=\"16\" y1=\"2\" x2=\"16\" y2=\"6\"></line>\n                <line x1=\"8\" y1=\"2\" x2=\"8\" y2=\"6\"></line>\n                <line x1=\"3\" y1=\"10\" x2=\"21\" y2=\"10\"></line>\n              </svg>\n            </view>\n            <text class=\"redpacket-amount\">¥{{item.redPacketAmount}}</text>\n            <view class=\"redpacket-btn\">\n              <text>抢</text>\n            </view>\n          </view>\n          \n          <!-- 互动按钮组 -->\n          <view class=\"info-actions\">\n            <view class=\"info-action\">\n              <view class=\"info-action-icon\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                  <path d=\"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\"></path>\n                </svg>\n              </view>\n              <text class=\"info-action-text\">{{item.likes || 0}}</text>\n            </view>\n            \n            <view class=\"info-action\">\n              <view class=\"info-action-icon\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                  <path d=\"M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z\"></path>\n                </svg>\n              </view>\n              <text class=\"info-action-text\">{{item.comments || 0}}</text>\n            </view>\n            \n            <view class=\"info-action\">\n              <view class=\"info-action-icon\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                  <circle cx=\"18\" cy=\"5\" r=\"3\"></circle>\n                  <circle cx=\"6\" cy=\"12\" r=\"3\"></circle>\n                  <circle cx=\"18\" cy=\"19\" r=\"3\"></circle>\n                  <line x1=\"8.59\" y1=\"13.51\" x2=\"15.42\" y2=\"17.49\"></line>\n                  <line x1=\"15.41\" y1=\"6.51\" x2=\"8.59\" y2=\"10.49\"></line>\n                </svg>\n              </view>\n              <text class=\"info-action-text\">分享</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { computed } from 'vue';\n\nconst props = defineProps({\n  item: {\n    type: Object,\n    required: true\n  }\n});\n\nconst hasKeyData = computed(() => {\n  const item = props.item;\n  return item.price || item.area || (item.minSalary || item.maxSalary) || item.location;\n});\n\nfunction formatSalary(min, max, unit = '月') {\n  if (!min && !max) return '面议';\n  if (!max) return `${min}K/${unit}以上`;\n  if (!min) return `${max}K/${unit}以内`;\n  return `${min}-${max}K/${unit}`;\n}\n\nfunction getCategoryClass(category) {\n  const categoryMap = {\n    '招聘信息': 'category-job',\n    '求职信息': 'category-resume',\n    '房屋出租': 'category-house-rent',\n    '房屋出售': 'category-house-sale',\n    '二手闲置': 'category-secondhand',\n    '二手车辆': 'category-used-car',\n    '到家服务': 'category-home-service',\n    '寻找服务': 'category-find-service',\n    '生意转让': 'category-business',\n    '宠物信息': 'category-pet',\n    '商家活动': 'category-merchant',\n    '婚恋交友': 'category-dating',\n    '车辆服务': 'category-car-service',\n    '磁州拼车': 'category-carpool',\n    '教育培训': 'category-education',\n    '其他服务': 'category-other'\n  };\n  \n  return categoryMap[category] || 'category-other';\n}\n\nconst emits = defineEmits(['like', 'comment', 'share']);\n</script>\n\n<style>\n.info-item {\n  margin-bottom: 20rpx;\n  border-radius: 28rpx;\n  background-color: #FFFFFF;\n  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);\n  position: relative;\n  border: none;\n  overflow: hidden;\n  padding: 24rpx 20rpx;\n}\n\n.topped-item {\n  background: linear-gradient(135deg, #FFFFFF, #FFF9F0);\n  border-left: 6rpx solid #FF9500;\n}\n\n.paid-top {\n  background: linear-gradient(135deg, #FFFFFF, #FFF9F0);\n  border-left: 6rpx solid #FF9500;\n}\n\n.ad-top {\n  background: linear-gradient(135deg, #FFFFFF, #F0F7FF);\n  border-left: 6rpx solid #007AFF;\n}\n\n.top-indicator {\n  position: absolute;\n  top: 0;\n  right: 0;\n  z-index: 2;\n}\n\n.top-badge {\n  display: flex;\n  align-items: center;\n  padding: 8rpx 16rpx;\n  border-bottom-left-radius: 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);\n  transform: translateZ(0);\n}\n\n.paid-badge {\n  background: linear-gradient(135deg, #FF9500, #FF3B30);\n  box-shadow: 0 4rpx 12rpx rgba(255, 59, 48, 0.2);\n}\n\n.ad-badge {\n  background: linear-gradient(135deg, #007AFF, #5AC8FA);\n  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);\n}\n\n.top-badge-icon {\n  color: #FFFFFF;\n  margin-right: 6rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.top-badge-text {\n  font-size: 20rpx;\n  color: #FFFFFF;\n  font-weight: 600;\n}\n\n.info-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  width: 100%;\n}\n\n.info-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.info-category-wrapper {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n}\n\n.info-category {\n  background: linear-gradient(135deg, #0062CC, #0091E6);\n  border-radius: 10rpx;\n  padding: 4rpx 12rpx;\n  border: 0.5rpx solid rgba(0, 98, 204, 0.2);\n  box-shadow: 0 4rpx 8rpx rgba(0, 98, 204, 0.15);\n  transform: translateZ(0);\n  margin-right: 12rpx;\n  display: inline-flex;\n  align-items: center;\n}\n\n.category-job {\n  background: linear-gradient(135deg, #007AFF, #5AC8FA);\n}\n\n.category-resume {\n  background: linear-gradient(135deg, #5856D6, #AF52DE);\n}\n\n.category-house-rent {\n  background: linear-gradient(135deg, #FF9500, #FF3B30);\n}\n\n.category-house-sale {\n  background: linear-gradient(135deg, #FF2D55, #FF3B30);\n}\n\n.category-secondhand {\n  background: linear-gradient(135deg, #34C759, #30B0C7);\n}\n\n.category-used-car {\n  background: linear-gradient(135deg, #FF9500, #FFCC00);\n}\n\n.category-home-service {\n  background: linear-gradient(135deg, #5AC8FA, #007AFF);\n}\n\n.category-find-service {\n  background: linear-gradient(135deg, #AF52DE, #5856D6);\n}\n\n.category-business {\n  background: linear-gradient(135deg, #FF2D55, #FF9500);\n}\n\n.category-pet {\n  background: linear-gradient(135deg, #FFCC00, #FF9500);\n}\n\n.category-merchant {\n  background: linear-gradient(135deg, #FF3B30, #FF2D55);\n}\n\n.category-dating {\n  background: linear-gradient(135deg, #FF2D55, #AF52DE);\n}\n\n.category-car-service {\n  background: linear-gradient(135deg, #34C759, #5AC8FA);\n}\n\n.category-carpool {\n  background: linear-gradient(135deg, #007AFF, #5856D6);\n}\n\n.category-education {\n  background: linear-gradient(135deg, #5856D6, #007AFF);\n}\n\n.category-other {\n  background: linear-gradient(135deg, #8E8E93, #636366);\n}\n\n.category-text {\n  font-size: 20rpx;\n  color: #FFFFFF;\n  font-weight: 600;\n}\n\n.info-time {\n  font-size: 22rpx;\n  color: #8E8E93;\n  font-weight: 400;\n  background-color: rgba(142, 142, 147, 0.08);\n  padding: 4rpx 12rpx;\n  border-radius: 10rpx;\n  letter-spacing: 0.5rpx;\n  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;\n  margin-left: 8rpx;\n}\n\n.info-main {\n  display: flex;\n  flex-direction: row;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-top: 8rpx;\n  margin-bottom: 12rpx;\n  width: 100%;\n}\n\n.info-content-wrapper {\n  flex: 1;\n  overflow: hidden;\n  min-width: 0;\n}\n\n.info-text {\n  font-size: 28rpx;\n  color: #1C1C1E;\n  line-height: 1.4;\n  margin-bottom: 8rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n}\n\n.bold-text {\n  font-weight: 600;\n  color: #000000;\n}\n\n.info-images-right {\n  width: 160rpx;\n  height: 160rpx;\n  flex-shrink: 0;\n  position: relative;\n  margin-left: 10rpx;\n  border-radius: 12rpx;\n  overflow: hidden;\n}\n\n.info-image-container-right {\n  width: 100%;\n  height: 100%;\n  border-radius: 12rpx;\n  overflow: hidden;\n  background-color: #f2f2f7;\n  position: relative;\n}\n\n.info-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  border-radius: 12rpx;\n}\n\n.image-count-right {\n  position: absolute;\n  bottom: 8rpx;\n  right: 8rpx;\n  background-color: rgba(0, 0, 0, 0.6);\n  color: white;\n  font-size: 18rpx;\n  padding: 2rpx 10rpx;\n  border-radius: 8rpx;\n  z-index: 2;\n}\n\n.info-tags {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -4rpx 10rpx;\n}\n\n.info-tag {\n  background-color: rgba(0, 122, 255, 0.08);\n  border-radius: 16rpx;\n  padding: 4rpx 12rpx;\n  margin: 4rpx;\n}\n\n.info-tag-text {\n  font-size: 20rpx;\n  color: #007AFF;\n  font-weight: 500;\n}\n\n.info-status {\n  position: absolute;\n  top: 20rpx;\n  left: 20rpx;\n  background: rgba(0, 0, 0, 0.7);\n  border-radius: 16rpx;\n  padding: 4rpx 12rpx;\n  z-index: 2;\n}\n\n.info-status-text {\n  font-size: 20rpx;\n  color: #FFFFFF;\n  font-weight: 600;\n}\n\n.core-data-area {\n  display: flex;\n  flex-wrap: wrap;\n  margin-top: 10rpx;\n  margin-bottom: 12rpx;\n  padding-top: 10rpx;\n  border-top: 1rpx solid rgba(60, 60, 67, 0.1);\n}\n\n.core-data-item {\n  display: flex;\n  align-items: center;\n  background: rgba(0, 0, 0, 0.02);\n  padding: 6rpx 12rpx;\n  border-radius: 20rpx;\n  margin-right: 10rpx;\n  margin-bottom: 8rpx;\n}\n\n.core-data-icon {\n  color: #007AFF;\n  margin-right: 6rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 32rpx;\n  height: 32rpx;\n  border-radius: 50%;\n  background-color: rgba(0, 122, 255, 0.1);\n}\n\n.price-icon {\n  background: linear-gradient(135deg, #FF9500, #FF3B30);\n}\n\n.area-icon {\n  background: linear-gradient(135deg, #34C759, #30B0C7);\n}\n\n.salary-icon {\n  background: linear-gradient(135deg, #FF3B30, #FF9500);\n}\n\n.location-icon {\n  background: linear-gradient(135deg, #007AFF, #5AC8FA);\n}\n\n.core-data-value {\n  font-size: 24rpx;\n  color: #636366;\n  font-weight: 500;\n}\n\n.price-value {\n  color: #FF3B30;\n  font-weight: 700;\n}\n\n.price-unit {\n  font-size: 18rpx;\n  color: #FF3B30;\n  font-weight: 500;\n  margin-left: 4rpx;\n}\n\n.salary-value {\n  color: #FF3B30;\n  font-weight: 700;\n}\n\n.info-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-top: 10rpx;\n  border-top: 1rpx solid rgba(60, 60, 67, 0.1);\n  position: relative;\n  z-index: 1;\n}\n\n.info-user {\n  display: flex;\n  align-items: center;\n  background: rgba(0, 0, 0, 0.02);\n  padding: 6rpx 12rpx;\n  border-radius: 20rpx;\n}\n\n.user-avatar-container {\n  width: 32rpx;\n  height: 32rpx;\n  border-radius: 50%;\n  margin-right: 8rpx;\n  border: 1rpx solid #FFFFFF;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: rgba(0, 122, 255, 0.1);\n  overflow: hidden;\n}\n\n.user-avatar-svg {\n  width: 20px;\n  height: 20px;\n  color: #007AFF;\n}\n\n.user-name {\n  font-size: 22rpx;\n  color: #636366;\n  font-weight: 500;\n}\n\n.info-stats {\n  display: flex;\n  align-items: center;\n  flex: 1;\n  justify-content: flex-end;\n}\n\n.info-views {\n  display: flex;\n  align-items: center;\n  margin-right: 12rpx;\n  background: rgba(0, 0, 0, 0.02);\n  padding: 6rpx 12rpx;\n  border-radius: 20rpx;\n}\n\n.view-icon {\n  color: #636366;\n  margin-right: 6rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.view-count {\n  font-size: 22rpx;\n  color: #636366;\n  font-weight: 500;\n}\n\n.info-redpacket {\n  display: flex;\n  align-items: center;\n  background: linear-gradient(135deg, #FF3B30, #FF9500);\n  border-radius: 20rpx;\n  padding: 6rpx 12rpx;\n  margin-right: 12rpx;\n}\n\n.redpacket-icon {\n  color: #FFFFFF;\n  margin-right: 6rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.redpacket-amount {\n  font-size: 22rpx;\n  color: #FFFFFF;\n  font-weight: 700;\n  margin-right: 6rpx;\n}\n\n.redpacket-btn {\n  background-color: #FFFFFF;\n  border-radius: 50%;\n  width: 30rpx;\n  height: 30rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.redpacket-btn text {\n  font-size: 18rpx;\n  color: #FF3B30;\n  font-weight: 700;\n}\n\n.info-actions {\n  display: flex;\n  margin-left: auto;\n}\n\n.info-action {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 6rpx 12rpx;\n  background-color: rgba(0, 0, 0, 0.02);\n  border-radius: 20rpx;\n  margin: 0 4rpx;\n}\n\n.info-action-icon {\n  color: #8E8E93;\n  margin-right: 6rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.info-action-text {\n  font-size: 22rpx;\n  color: #8E8E93;\n  font-weight: 500;\n}\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/components/cards/BaseInfoCard.vue'\nwx.createComponent(Component)"], "names": ["computed", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAgMA,UAAM,QAAQ;AAOd,UAAM,aAAaA,cAAQ,SAAC,MAAM;AAChC,YAAM,OAAO,MAAM;AACnB,aAAO,KAAK,SAAS,KAAK,SAAS,KAAK,aAAa,KAAK,cAAc,KAAK;AAAA,IAC/E,CAAC;AAED,aAAS,aAAa,KAAK,KAAK,OAAO,KAAK;AAC1C,UAAI,CAAC,OAAO,CAAC;AAAK,eAAO;AACzB,UAAI,CAAC;AAAK,eAAO,GAAG,GAAG,KAAK,IAAI;AAChC,UAAI,CAAC;AAAK,eAAO,GAAG,GAAG,KAAK,IAAI;AAChC,aAAO,GAAG,GAAG,IAAI,GAAG,KAAK,IAAI;AAAA,IAC/B;AAEA,aAAS,iBAAiB,UAAU;AAClC,YAAM,cAAc;AAAA,QAClB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,MACZ;AAEE,aAAO,YAAY,QAAQ,KAAK;AAAA,IAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvOA,GAAG,gBAAgBC,SAAS;"}