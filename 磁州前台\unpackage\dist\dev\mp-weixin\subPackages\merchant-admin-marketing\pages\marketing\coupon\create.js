"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const MarketingPromotionActions = () => "../../../components/MarketingPromotionActions.js";
const DistributionSetting = () => "../distribution/components/DistributionSetting.js";
const _sfc_main = {
  name: "CouponCreate",
  components: {
    MarketingPromotionActions,
    DistributionSetting
  },
  setup() {
    const selectedType = common_vendor.ref("discount");
    const validityType = common_vendor.ref("fixed");
    const tempCouponId = common_vendor.ref("temp-" + Date.now());
    const hasMerchantDistribution = common_vendor.ref(false);
    const couponTypes = [
      {
        value: "discount",
        name: "满减券",
        description: "满足消费金额门槛后减免固定金额"
      },
      {
        value: "percent",
        name: "折扣券",
        description: "满足消费金额门槛后按比例折扣"
      },
      {
        value: "free",
        name: "无门槛券",
        description: "无消费金额门槛，直接减免固定金额"
      }
    ];
    const couponForm = common_vendor.reactive({
      title: "",
      value: "",
      discount: "",
      minSpend: "",
      startDate: formatDate(/* @__PURE__ */ new Date()),
      expireDate: formatDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1e3)),
      // 30天后
      validDays: "30",
      totalCount: "",
      perPersonLimit: "1",
      applicableProducts: "全部商品",
      instructions: "",
      // 分销设置
      distributionSettings: {
        enabled: false,
        commissionMode: "percentage",
        commissions: {
          level1: "",
          level2: "",
          level3: ""
        },
        enableLevel3: false
      }
    });
    function goBack() {
      common_vendor.index.navigateBack();
    }
    function showHelp() {
      common_vendor.index.showModal({
        title: "帮助信息",
        content: "创建优惠券页面可以设置优惠券的各项参数，包括类型、金额、有效期等。创建后可在优惠券管理页面查看和管理。",
        showCancel: false
      });
    }
    function selectCouponType(type) {
      selectedType.value = type;
    }
    function setValidityType(type) {
      validityType.value = type;
    }
    function showStartDatePicker() {
      common_vendor.index.showDatePicker({
        current: couponForm.startDate,
        success: (res) => {
          couponForm.startDate = res.date;
        }
      });
    }
    function showEndDatePicker() {
      common_vendor.index.showDatePicker({
        current: couponForm.expireDate,
        success: (res) => {
          couponForm.expireDate = res.date;
        }
      });
    }
    function showProductSelector() {
      common_vendor.index.showActionSheet({
        itemList: ["全部商品", "指定商品", "指定分类"],
        success: (res) => {
          const options = ["全部商品", "指定商品", "指定分类"];
          couponForm.applicableProducts = options[res.tapIndex];
          if (res.tapIndex > 0) {
            common_vendor.index.showToast({
              title: "请在下一页选择",
              icon: "none"
            });
          }
        }
      });
    }
    function previewCoupon() {
      if (!validateForm()) {
        return;
      }
      common_vendor.index.showToast({
        title: "预览功能开发中",
        icon: "none"
      });
    }
    async function createCoupon() {
      if (!validateForm()) {
        return;
      }
      common_vendor.index.showLoading({
        title: "创建中..."
      });
      if (hasMerchantDistribution.value && couponForm.distributionSettings.enabled) {
        const success = await saveDistributionSettings();
        if (!success) {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "分销设置保存失败",
            icon: "none"
          });
          return;
        }
      }
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "创建成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      }, 1e3);
    }
    function validateForm() {
      if (!couponForm.title) {
        common_vendor.index.showToast({
          title: "请输入优惠券名称",
          icon: "none"
        });
        return false;
      }
      if (selectedType.value === "discount" && !couponForm.value) {
        common_vendor.index.showToast({
          title: "请输入优惠金额",
          icon: "none"
        });
        return false;
      }
      if (selectedType.value === "percent" && !couponForm.discount) {
        common_vendor.index.showToast({
          title: "请输入折扣比例",
          icon: "none"
        });
        return false;
      }
      if (selectedType.value !== "free" && !couponForm.minSpend) {
        common_vendor.index.showToast({
          title: "请输入使用门槛",
          icon: "none"
        });
        return false;
      }
      if (!couponForm.totalCount) {
        common_vendor.index.showToast({
          title: "请输入发放总量",
          icon: "none"
        });
        return false;
      }
      return true;
    }
    function formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    }
    function loadTemplateData(templateId) {
      common_vendor.index.showLoading({
        title: "加载模板..."
      });
      setTimeout(() => {
        Object.assign(couponForm, {
          title: "模板优惠券",
          value: "20",
          minSpend: "200",
          totalCount: "1000",
          perPersonLimit: "2",
          applicableProducts: "全部商品",
          instructions: "模板优惠券使用说明"
        });
        common_vendor.index.hideLoading();
      }, 500);
    }
    function updateDistributionSettings(settings) {
      couponForm.distributionSettings = settings;
    }
    function checkMerchantDistribution() {
      setTimeout(() => {
        hasMerchantDistribution.value = true;
      }, 500);
    }
    async function saveDistributionSettings() {
      return new Promise((resolve) => {
        if (hasMerchantDistribution.value && couponForm.distributionSettings.enabled) {
          setTimeout(() => {
            common_vendor.index.__f__("log", "at subPackages/merchant-admin-marketing/pages/marketing/coupon/create.vue:499", "分销设置已保存");
            resolve(true);
          }, 500);
        } else {
          resolve(true);
        }
      });
    }
    function handlePromotionCompleted(data) {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-marketing/pages/marketing/coupon/create.vue:510", "推广操作完成:", data);
      if (data.action === "publish") {
        common_vendor.index.showToast({
          title: "发布成功",
          icon: "success"
        });
      }
    }
    common_vendor.onMounted(() => {
      var _a;
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = ((_a = currentPage.$page) == null ? void 0 : _a.options) || {};
      if (options.template) {
        loadTemplateData(options.template);
      }
      checkMerchantDistribution();
    });
    return {
      selectedType,
      validityType,
      couponTypes,
      couponForm,
      hasMerchantDistribution,
      tempCouponId,
      goBack,
      showHelp,
      selectCouponType,
      setValidityType,
      showStartDatePicker,
      showEndDatePicker,
      showProductSelector,
      previewCoupon,
      createCoupon,
      updateDistributionSettings,
      handlePromotionCompleted
    };
  }
};
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  const _component_rect = common_vendor.resolveComponent("rect");
  const _component_DistributionSetting = common_vendor.resolveComponent("DistributionSetting");
  const _component_MarketingPromotionActions = common_vendor.resolveComponent("MarketingPromotionActions");
  (_component_path + _component_line + _component_svg + _component_polyline + _component_rect + _component_DistributionSetting + _component_MarketingPromotionActions)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $setup.goBack && $setup.goBack(...args)),
    b: common_vendor.o((...args) => $setup.showHelp && $setup.showHelp(...args)),
    c: common_vendor.f($setup.couponTypes, (type, index, i0) => {
      return common_vendor.e({
        a: "ea91bffe-1-" + i0 + "," + ("ea91bffe-0-" + i0),
        b: "ea91bffe-2-" + i0 + "," + ("ea91bffe-0-" + i0),
        c: "ea91bffe-3-" + i0 + "," + ("ea91bffe-0-" + i0),
        d: "ea91bffe-4-" + i0 + "," + ("ea91bffe-0-" + i0),
        e: "ea91bffe-0-" + i0,
        f: common_vendor.n("bg-" + type.value),
        g: common_vendor.t(type.name),
        h: common_vendor.t(type.description),
        i: $setup.selectedType === type.value
      }, $setup.selectedType === type.value ? {
        j: "ea91bffe-6-" + i0 + "," + ("ea91bffe-5-" + i0),
        k: common_vendor.p({
          points: "20 6 9 17 4 12"
        }),
        l: "ea91bffe-5-" + i0,
        m: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2"
        })
      } : {}, {
        n: index,
        o: $setup.selectedType === type.value ? 1 : "",
        p: common_vendor.o(($event) => $setup.selectCouponType(type.value), index)
      });
    }),
    d: common_vendor.p({
      d: "M22 12c0-5.52-4.48-10-10-10S2 6.48 2 12s4.48 10 10 10 10-4.48 10-10z"
    }),
    e: common_vendor.p({
      d: "M8 14s1.5 2 4 2 4-2 4-2"
    }),
    f: common_vendor.p({
      x1: "9",
      y1: "9",
      x2: "9.01",
      y2: "9"
    }),
    g: common_vendor.p({
      x1: "15",
      y1: "9",
      x2: "15.01",
      y2: "9"
    }),
    h: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    i: $setup.couponForm.title,
    j: common_vendor.o(($event) => $setup.couponForm.title = $event.detail.value),
    k: $setup.selectedType === "discount"
  }, $setup.selectedType === "discount" ? {
    l: $setup.couponForm.value,
    m: common_vendor.o(($event) => $setup.couponForm.value = $event.detail.value)
  } : {}, {
    n: $setup.selectedType === "percent"
  }, $setup.selectedType === "percent" ? {
    o: $setup.couponForm.discount,
    p: common_vendor.o(($event) => $setup.couponForm.discount = $event.detail.value)
  } : {}, {
    q: $setup.selectedType !== "free"
  }, $setup.selectedType !== "free" ? {
    r: $setup.couponForm.minSpend,
    s: common_vendor.o(($event) => $setup.couponForm.minSpend = $event.detail.value)
  } : {}, {
    t: $setup.validityType === "fixed"
  }, $setup.validityType === "fixed" ? {} : {}, {
    v: $setup.validityType === "fixed" ? 1 : "",
    w: common_vendor.o(($event) => $setup.setValidityType("fixed")),
    x: $setup.validityType === "dynamic"
  }, $setup.validityType === "dynamic" ? {} : {}, {
    y: $setup.validityType === "dynamic" ? 1 : "",
    z: common_vendor.o(($event) => $setup.setValidityType("dynamic")),
    A: $setup.validityType === "fixed"
  }, $setup.validityType === "fixed" ? {
    B: common_vendor.t($setup.couponForm.startDate),
    C: common_vendor.p({
      x: "3",
      y: "4",
      width: "18",
      height: "18",
      rx: "2",
      ry: "2"
    }),
    D: common_vendor.p({
      x1: "16",
      y1: "2",
      x2: "16",
      y2: "6"
    }),
    E: common_vendor.p({
      x1: "8",
      y1: "2",
      x2: "8",
      y2: "6"
    }),
    F: common_vendor.p({
      x1: "3",
      y1: "10",
      x2: "21",
      y2: "10"
    }),
    G: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    H: common_vendor.o((...args) => $setup.showStartDatePicker && $setup.showStartDatePicker(...args))
  } : {}, {
    I: $setup.validityType === "fixed"
  }, $setup.validityType === "fixed" ? {
    J: common_vendor.t($setup.couponForm.expireDate),
    K: common_vendor.p({
      x: "3",
      y: "4",
      width: "18",
      height: "18",
      rx: "2",
      ry: "2"
    }),
    L: common_vendor.p({
      x1: "16",
      y1: "2",
      x2: "16",
      y2: "6"
    }),
    M: common_vendor.p({
      x1: "8",
      y1: "2",
      x2: "8",
      y2: "6"
    }),
    N: common_vendor.p({
      x1: "3",
      y1: "10",
      x2: "21",
      y2: "10"
    }),
    O: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    P: common_vendor.o((...args) => $setup.showEndDatePicker && $setup.showEndDatePicker(...args))
  } : {}, {
    Q: $setup.validityType === "dynamic"
  }, $setup.validityType === "dynamic" ? {
    R: $setup.couponForm.validDays,
    S: common_vendor.o(($event) => $setup.couponForm.validDays = $event.detail.value)
  } : {}, {
    T: $setup.couponForm.totalCount,
    U: common_vendor.o(($event) => $setup.couponForm.totalCount = $event.detail.value),
    V: $setup.couponForm.perPersonLimit,
    W: common_vendor.o(($event) => $setup.couponForm.perPersonLimit = $event.detail.value),
    X: common_vendor.t($setup.couponForm.applicableProducts),
    Y: common_vendor.o((...args) => $setup.showProductSelector && $setup.showProductSelector(...args)),
    Z: $setup.couponForm.instructions,
    aa: common_vendor.o(($event) => $setup.couponForm.instructions = $event.detail.value),
    ab: $setup.hasMerchantDistribution
  }, $setup.hasMerchantDistribution ? {
    ac: common_vendor.o($setup.updateDistributionSettings),
    ad: common_vendor.p({
      settings: $setup.couponForm.distributionSettings
    })
  } : {}, {
    ae: common_vendor.o($setup.handlePromotionCompleted),
    af: common_vendor.p({
      ["activity-type"]: "coupon",
      ["activity-id"]: $setup.tempCouponId,
      ["publish-mode-only"]: true,
      ["show-actions"]: ["publish"]
    }),
    ag: common_vendor.o((...args) => $setup.previewCoupon && $setup.previewCoupon(...args)),
    ah: common_vendor.o((...args) => $setup.createCoupon && $setup.createCoupon(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/coupon/create.js.map
