<template>
  <view class="qrcode-page">
    <!-- 导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">推广二维码</text>
    </view>
    
    <!-- 二维码容器 -->
    <view class="qrcode-container">
      <view class="qrcode-card">
        <view class="qrcode-header">
          <text class="qrcode-title">{{ title }}</text>
          <text class="qrcode-subtitle">扫码查看详情</text>
        </view>
        
        <view class="qrcode-content">
          <image v-if="qrcodePath" class="qrcode-image" :src="qrcodePath" mode="aspectFit"></image>
          <view v-else class="qrcode-loading">
            <view class="loading-spinner"></view>
            <text class="loading-text">生成中...</text>
          </view>
        </view>
        
        <view class="qrcode-footer">
          <text class="qrcode-tip">长按保存图片或分享</text>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn save-btn" @click="saveQrcode">保存到相册</button>
      <button class="action-btn share-btn" @click="shareQrcode">分享</button>
    </view>
    
    <!-- 推广提示 -->
    <view class="promotion-tips">
      <text class="tips-title">使用说明</text>
      <text class="tips-content">1. 保存二维码图片到手机相册</text>
      <text class="tips-content">2. 将二维码分享到微信、QQ等社交平台</text>
      <text class="tips-content">3. 他人扫码后将直接进入内容详情页</text>
      <text class="tips-content">4. 推广成功后可获得相应奖励</text>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import promotionService from '@/utils/promotionService';

// 页面参数
const contentType = ref('');
const contentId = ref('');
const title = ref('磁州生活网');

// 二维码路径
const qrcodePath = ref('');

// 当前用户ID
const userId = ref('');

// 页面加载
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};
  
  contentType.value = options.type || '';
  contentId.value = options.id || '';
  title.value = options.title || '磁州生活网';
  
  // 获取用户信息
  const userInfo = uni.getStorageSync('userInfo');
  if (userInfo && userInfo.userId) {
    userId.value = userInfo.userId;
  }
  
  // 生成二维码
  generateQrcode();
});

// 生成二维码
const generateQrcode = async () => {
  if (!contentType.value || !contentId.value) {
    uni.showToast({
      title: '参数错误',
      icon: 'none'
    });
    setTimeout(() => {
      goBack();
    }, 1500);
    return;
  }
  
  try {
    // 生成二维码
    qrcodePath.value = await promotionService.generateQrcode(
      contentType.value,
      contentId.value,
      userId.value
    );
    
    // 记录推广数据
    promotionService.recordPromotion(contentType.value, contentId.value, 'qrcode');
  } catch (err) {
    console.error('生成二维码失败', err);
    uni.showToast({
      title: '生成二维码失败',
      icon: 'none'
    });
  }
};

// 保存二维码到相册
const saveQrcode = async () => {
  if (!qrcodePath.value) {
    uni.showToast({
      title: '二维码未生成',
      icon: 'none'
    });
    return;
  }
  
  try {
    await promotionService.savePosterToAlbum(qrcodePath.value);
  } catch (err) {
    console.error('保存二维码失败', err);
  }
};

// 分享二维码
const shareQrcode = () => {
  if (!qrcodePath.value) {
    uni.showToast({
      title: '二维码未生成',
      icon: 'none'
    });
    return;
  }
  
  uni.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage', 'shareTimeline']
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.qrcode-page {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 30rpx;
}

.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #3846cd, #2c3aa0);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(56, 70, 205, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.qrcode-container {
  margin: 30rpx;
  display: flex;
  justify-content: center;
}

.qrcode-card {
  width: 100%;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  padding: 30rpx;
}

.qrcode-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.qrcode-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.qrcode-subtitle {
  font-size: 26rpx;
  color: #999;
  display: block;
}

.qrcode-content {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 0;
}

.qrcode-image {
  width: 400rpx;
  height: 400rpx;
}

.qrcode-loading {
  width: 400rpx;
  height: 400rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #3846cd;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.qrcode-footer {
  text-align: center;
  margin-top: 30rpx;
}

.qrcode-tip {
  font-size: 26rpx;
  color: #999;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  padding: 0 30rpx;
  margin-top: 40rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  margin: 0 20rpx;
}

.save-btn {
  background: linear-gradient(135deg, #3846cd, #2c3aa0);
  color: #fff;
  border: none;
}

.share-btn {
  background-color: #fff;
  color: #3846cd;
  border: 1px solid #3846cd;
}

.promotion-tips {
  margin: 40rpx 30rpx 30rpx;
  border-radius: 16rpx;
  background-color: #fff;
  padding: 20rpx;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.tips-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.tips-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  display: block;
  margin-bottom: 10rpx;
}
</style> 