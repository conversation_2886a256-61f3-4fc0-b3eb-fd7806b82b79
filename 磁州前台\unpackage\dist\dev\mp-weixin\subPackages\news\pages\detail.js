"use strict";
const common_vendor = require("../../../common/vendor.js");
const mock_api = require("../../../mock/api.js");
const mock_info_categories = require("../../../mock/info/categories.js");
const static_data_infoListData = require("../../../static/data/infoListData.js");
const common_assets = require("../../../common/assets.js");
const InfoList = () => "../../../components/index/InfoList.js";
const FloatPromotionButton = () => "../../../components/FloatPromotionButton.js";
const _sfc_main = {
  components: {
    InfoList,
    FloatPromotionButton
  },
  setup() {
    const commentText = common_vendor.ref("");
    const currentCommentIndex = common_vendor.ref(-1);
    const replyToUser = common_vendor.ref("");
    const inputBottom = common_vendor.ref(0);
    const inputFocus = common_vendor.ref(false);
    const showBackToTop = common_vendor.ref(false);
    const hasMoreComments = common_vendor.ref(true);
    const statusBarHeight = common_vendor.ref(20);
    const newsId = common_vendor.ref(0);
    const currentInfoTab = common_vendor.ref(0);
    const isTabsFixed = common_vendor.ref(false);
    const adBanner = common_vendor.ref({
      image: "/static/images/ad-banner.jpg",
      url: "/pages/activity/detail?id=3"
    });
    const newsInfo = common_vendor.reactive({
      id: 1,
      title: "",
      time: "",
      source: "",
      category: "",
      image: "",
      imageCaption: "",
      isCollected: false,
      content: "",
      views: 0,
      likes: 0,
      isLiked: false,
      comments: 0,
      commentList: []
    });
    const toppedInfoList = common_vendor.ref([]);
    const allInfoList = common_vendor.ref([]);
    const _tabsPlaceholderTop = common_vendor.ref(null);
    const _currentScrollTop = common_vendor.ref(0);
    const replyPlaceholder = common_vendor.computed(() => {
      return replyToUser.value ? `回复 @${replyToUser.value}` : "写下你的评论...";
    });
    const filteredInfoList = common_vendor.computed(() => {
      if (currentInfoTab.value === 0) {
        return allInfoList.value;
      }
      const selectedCategory = mock_info_categories.infoCategories[currentInfoTab.value - 1];
      return allInfoList.value.filter((item) => item.category === selectedCategory);
    });
    const filteredToppedInfoList = common_vendor.computed(() => {
      if (currentInfoTab.value === 0) {
        return toppedInfoList.value;
      }
      const selectedCategory = mock_info_categories.infoCategories[currentInfoTab.value - 1];
      return toppedInfoList.value.filter((item) => item.category === selectedCategory);
    });
    function onPageLoad(options) {
      if (options.id) {
        newsId.value = options.id;
        common_vendor.index.__f__("log", "at subPackages/news/pages/detail.vue:322", "资讯ID:", newsId.value);
      }
      common_vendor.index.getSystemInfo({
        success: (res) => {
          statusBarHeight.value = res.statusBarHeight;
        }
      });
      fetchNewsDetail(newsId.value);
      fetchInfoData();
      setTimeout(() => {
        getTabsPosition();
      }, 500);
    }
    function fetchNewsDetail(id) {
      common_vendor.index.showLoading({
        title: "加载中"
      });
      mock_api.default.news.getDetail(id).then((data) => {
        Object.assign(newsInfo, data);
        common_vendor.index.hideLoading();
        recordView(id);
      }).catch((err) => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "获取资讯失败",
          icon: "none"
        });
        common_vendor.index.__f__("error", "at subPackages/news/pages/detail.vue:362", "获取资讯失败:", err);
      });
    }
    function fetchInfoData() {
      toppedInfoList.value = static_data_infoListData.infoListData.toppedInfoList.map((item) => {
        return {
          ...item,
          images: item.images || [],
          tags: item.tags || [],
          views: item.views || 0,
          likes: item.likes || 0,
          comments: item.comments || 0
        };
      });
      allInfoList.value = static_data_infoListData.infoListData.allInfoList.map((item) => {
        return {
          ...item,
          images: item.images || [],
          tags: item.tags || [],
          views: item.views || 0,
          likes: item.likes || 0,
          comments: item.comments || 0
        };
      });
      adBanner.value = {
        image: "/static/images/banner/ad-banner.jpg",
        url: "/pages/ad/detail",
        title: "广告横幅"
      };
    }
    function recordView(id) {
      common_vendor.index.__f__("log", "at subPackages/news/pages/detail.vue:406", "记录浏览量，ID:", id);
    }
    function toggleLike() {
      common_vendor.index.vibrateShort({
        success: () => {
          common_vendor.index.__f__("log", "at subPackages/news/pages/detail.vue:415", "振动成功");
        }
      });
      newsInfo.isLiked = !newsInfo.isLiked;
      newsInfo.likes += newsInfo.isLiked ? 1 : -1;
      common_vendor.index.showToast({
        title: newsInfo.isLiked ? "已点赞" : "已取消点赞",
        icon: "none",
        duration: 1500
      });
    }
    function toggleCollect() {
      common_vendor.index.vibrateShort({
        success: () => {
          common_vendor.index.__f__("log", "at subPackages/news/pages/detail.vue:435", "振动成功");
        }
      });
      newsInfo.isCollected = !newsInfo.isCollected;
      common_vendor.index.showToast({
        title: newsInfo.isCollected ? "已收藏" : "已取消收藏",
        icon: "none",
        duration: 1500
      });
    }
    function beforeShare() {
      common_vendor.index.vibrateShort();
      common_vendor.index.__f__("log", "at subPackages/news/pages/detail.vue:453", "准备分享...");
    }
    function showPromotionTool() {
      common_vendor.index.navigateTo({
        url: `/subPackages/promotion/pages/promotion-tool?type=content&id=${newsId.value}`
      });
    }
    function scrollToComment() {
      common_vendor.index.createSelectorQuery().select("#comment-section").boundingClientRect((data) => {
        if (data) {
          common_vendor.index.pageScrollTo({
            scrollTop: data.top,
            duration: 300
          });
        } else {
          common_vendor.index.pageScrollTo({
            scrollTop: 9999,
            duration: 300
          });
        }
      }).exec();
    }
    function toggleCommentLike(index) {
      common_vendor.index.vibrateShort({
        success: () => {
          common_vendor.index.__f__("log", "at subPackages/news/pages/detail.vue:490", "振动成功");
        }
      });
      const comment = newsInfo.commentList[index];
      comment.isLiked = !comment.isLiked;
      comment.likes += comment.isLiked ? 1 : -1;
    }
    function showReplyInput(commentIndex, userName) {
      currentCommentIndex.value = commentIndex;
      replyToUser.value = userName;
      inputFocus.value = true;
      setTimeout(() => {
        common_vendor.index.createSelectorQuery().select(".comment-input-container").boundingClientRect((data) => {
          if (data) {
            common_vendor.index.pageScrollTo({
              scrollTop: data.top,
              duration: 300
            });
          }
        }).exec();
      }, 200);
    }
    function resetReplyState() {
      currentCommentIndex.value = -1;
      replyToUser.value = "";
      commentText.value = "";
      inputFocus.value = false;
    }
    function submitComment() {
      if (!commentText.value.trim()) {
        common_vendor.index.showToast({
          title: "请输入评论内容",
          icon: "none"
        });
        return;
      }
      common_vendor.index.vibrateShort({
        success: () => {
          common_vendor.index.__f__("log", "at subPackages/news/pages/detail.vue:542", "振动成功");
        }
      });
      if (currentCommentIndex.value >= 0 && replyToUser.value) {
        const comment = newsInfo.commentList[currentCommentIndex.value];
        if (!comment.replies) {
          comment.replies = [];
        }
        comment.replies.unshift({
          name: "游客",
          content: commentText.value,
          time: "刚刚",
          replyTo: replyToUser.value,
          isOfficial: false
        });
        newsInfo.comments++;
        comment.showAllReplies = true;
        common_vendor.index.showToast({
          title: "回复成功",
          icon: "success"
        });
        setTimeout(() => {
          const commentSelector = `.comment-item:nth-child(${currentCommentIndex.value + 1})`;
          common_vendor.index.createSelectorQuery().select(commentSelector).boundingClientRect((data) => {
            if (data) {
              common_vendor.index.pageScrollTo({
                scrollTop: data.top,
                duration: 300
              });
            }
          }).exec();
        }, 300);
      } else {
        newsInfo.commentList.unshift({
          avatar: "/static/images/tabbar/user-blue.png",
          name: "游客",
          content: commentText.value,
          time: "刚刚",
          likes: 0,
          isLiked: false,
          isOfficial: false,
          showAllReplies: false,
          replies: []
        });
        newsInfo.comments++;
        common_vendor.index.showToast({
          title: "评论成功",
          icon: "success"
        });
        setTimeout(() => {
          scrollToComment();
        }, 300);
      }
      resetReplyState();
    }
    function toggleReplies(index) {
      common_vendor.index.vibrateShort({
        success: () => {
          common_vendor.index.__f__("log", "at subPackages/news/pages/detail.vue:627", "振动成功");
        }
      });
      newsInfo.commentList[index].showAllReplies = !newsInfo.commentList[index].showAllReplies;
    }
    function loadMoreComments() {
      common_vendor.index.showLoading({
        title: "加载中"
      });
      mock_api.default.news.getMoreComments().then((comments) => {
        newsInfo.commentList.push(...comments);
        hasMoreComments.value = false;
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "已加载全部评论",
          icon: "none"
        });
      }).catch((err) => {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at subPackages/news/pages/detail.vue:655", "加载更多评论失败:", err);
      });
    }
    function onInputFocus(e) {
      inputBottom.value = e.detail.height || 0;
    }
    function onInputBlur() {
      inputBottom.value = 0;
    }
    function navigateTo(url) {
      common_vendor.index.switchTab({
        url,
        fail: (err) => {
          common_vendor.index.__f__("error", "at subPackages/news/pages/detail.vue:674", "导航失败:", err);
          common_vendor.index.navigateTo({
            url
          });
        }
      });
    }
    function navigateToInfoDetail(item) {
      common_vendor.index.navigateTo({
        url: `/pages/publish/${item.pageType || "info-detail"}?id=${item.id}`
      });
    }
    function handleInfoTabChange(tab) {
      common_vendor.index.__f__("log", "at subPackages/news/pages/detail.vue:692", "切换到标签:", tab);
      currentInfoTab.value = tab.index;
    }
    function getTabsPosition() {
      const query = common_vendor.index.createSelectorQuery();
      query.select("#tabsContainer").boundingClientRect((data) => {
        if (data) {
          data.height || 0;
        }
      }).exec();
    }
    function getTabsPlaceholderPosition() {
      common_vendor.nextTick$1(() => {
        const query = common_vendor.index.createSelectorQuery();
        query.select("#tabsPlaceholder").boundingClientRect((data) => {
          if (data) {
            _tabsPlaceholderTop.value = (_currentScrollTop.value || 0) + data.top;
            common_vendor.index.__f__("log", "at subPackages/news/pages/detail.vue:714", "标签占位符位置:", _tabsPlaceholderTop.value);
          }
        }).exec();
      });
    }
    function scrollToTop() {
      common_vendor.index.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });
    }
    function onPageScroll(e) {
      showBackToTop.value = e.scrollTop > 500;
    }
    function onPullDownRefresh() {
      common_vendor.index.__f__("log", "at subPackages/news/pages/detail.vue:736", "下拉刷新");
      fetchNewsDetail(newsId.value);
      setTimeout(() => {
        common_vendor.index.stopPullDownRefresh();
      }, 1e3);
    }
    function onReachBottom() {
      common_vendor.index.__f__("log", "at subPackages/news/pages/detail.vue:747", "页面触底");
      if (hasMoreComments.value) {
        loadMoreComments();
      }
    }
    function onShareAppMessage() {
      return {
        title: newsInfo.title,
        path: `/pages/subPackages/news/detail/index?id=${newsId.value}`
      };
    }
    function onShareTimeline() {
      return {
        title: newsInfo.title,
        query: `id=${newsInfo.id}`,
        path: `/pages/subPackages/news/detail/index?id=${newsId.value}`,
        imageUrl: newsInfo.image
      };
    }
    return {
      // 数据
      commentText,
      currentCommentIndex,
      replyToUser,
      inputBottom,
      inputFocus,
      showBackToTop,
      hasMoreComments,
      statusBarHeight,
      newsId,
      currentInfoTab,
      isTabsFixed,
      visibleCategories: mock_info_categories.visibleCategories,
      infoCategories: mock_info_categories.infoCategories,
      adBanner,
      newsInfo,
      toppedInfoList,
      allInfoList,
      _tabsPlaceholderTop,
      _currentScrollTop,
      // 计算属性
      replyPlaceholder,
      filteredInfoList,
      filteredToppedInfoList,
      // 方法
      onPageLoad,
      fetchNewsDetail,
      recordView,
      toggleLike,
      toggleCollect,
      beforeShare,
      scrollToComment,
      toggleCommentLike,
      showReplyInput,
      resetReplyState,
      submitComment,
      toggleReplies,
      loadMoreComments,
      onInputFocus,
      onInputBlur,
      navigateTo,
      navigateToInfoDetail,
      handleInfoTabChange,
      getTabsPosition,
      getTabsPlaceholderPosition,
      scrollToTop,
      onPageScroll,
      onPullDownRefresh,
      onReachBottom,
      onShareAppMessage,
      onShareTimeline,
      showPromotionTool
    };
  },
  // uni-app 页面生命周期钩子
  onLoad(options) {
    this.onPageLoad(options);
  },
  onReady() {
    common_vendor.index.__f__("log", "at subPackages/news/pages/detail.vue:833", "页面渲染完成");
  },
  onShow() {
    common_vendor.index.__f__("log", "at subPackages/news/pages/detail.vue:836", "页面显示");
  },
  onHide() {
    common_vendor.index.__f__("log", "at subPackages/news/pages/detail.vue:839", "页面隐藏");
  },
  onUnload() {
    common_vendor.index.__f__("log", "at subPackages/news/pages/detail.vue:842", "页面卸载");
  },
  onPullDownRefresh() {
    this.onPullDownRefresh();
  },
  onReachBottom() {
    this.onReachBottom();
  },
  onPageScroll(e) {
    this.onPageScroll(e);
  },
  onShareAppMessage() {
    return this.onShareAppMessage();
  },
  onShareTimeline() {
    return this.onShareTimeline();
  }
};
if (!Array) {
  const _component_FloatPromotionButton = common_vendor.resolveComponent("FloatPromotionButton");
  const _component_InfoList = common_vendor.resolveComponent("InfoList");
  (_component_FloatPromotionButton + _component_InfoList)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($setup.newsInfo.category),
    b: common_vendor.t($setup.newsInfo.title),
    c: common_assets._imports_0$29,
    d: common_vendor.t($setup.newsInfo.source),
    e: common_assets._imports_1$28,
    f: common_vendor.t($setup.newsInfo.time),
    g: common_assets._imports_2$38,
    h: common_vendor.t($setup.newsInfo.views),
    i: common_vendor.o(($event) => $setup.navigateTo("/pages/index/index")),
    j: common_vendor.o($setup.showPromotionTool),
    k: common_vendor.p({
      position: {
        right: "30rpx",
        bottom: "280rpx"
      },
      size: "90rpx"
    }),
    l: $setup.newsInfo.image
  }, $setup.newsInfo.image ? common_vendor.e({
    m: $setup.newsInfo.image,
    n: $setup.newsInfo.imageCaption
  }, $setup.newsInfo.imageCaption ? {
    o: common_vendor.t($setup.newsInfo.imageCaption)
  } : {}) : {}, {
    p: common_vendor.t($setup.newsInfo.content),
    q: common_vendor.t($setup.newsInfo.source),
    r: $setup.newsInfo.isLiked ? "/static/images/tabbar/已点赞选中.png" : "/static/images/tabbar/点赞.png",
    s: common_vendor.t($setup.newsInfo.likes),
    t: $setup.newsInfo.isLiked ? 1 : "",
    v: common_vendor.o((...args) => $setup.toggleLike && $setup.toggleLike(...args)),
    w: common_assets._imports_3$34,
    x: common_vendor.t($setup.newsInfo.comments),
    y: common_vendor.o((...args) => $setup.scrollToComment && $setup.scrollToComment(...args)),
    z: $setup.newsInfo.isCollected ? "/static/images/tabbar/已收藏选中.png" : "/static/images/tabbar/收藏.png",
    A: $setup.newsInfo.isCollected ? 1 : "",
    B: common_vendor.o((...args) => $setup.toggleCollect && $setup.toggleCollect(...args)),
    C: common_assets._imports_2$1,
    D: common_vendor.o((...args) => $setup.beforeShare && $setup.beforeShare(...args)),
    E: $setup.newsInfo.commentList && $setup.newsInfo.commentList.length > 0
  }, $setup.newsInfo.commentList && $setup.newsInfo.commentList.length > 0 ? common_vendor.e({
    F: common_vendor.t($setup.newsInfo.comments),
    G: common_vendor.f($setup.newsInfo.commentList, (comment, index, i0) => {
      return common_vendor.e({
        a: comment.avatar,
        b: common_vendor.t(comment.name),
        c: comment.isOfficial
      }, comment.isOfficial ? {} : {}, {
        d: common_vendor.t(comment.content),
        e: common_vendor.t(comment.time),
        f: common_vendor.o(($event) => $setup.showReplyInput(index, comment.name), index),
        g: comment.isLiked ? "/static/images/tabbar/已点赞选中.png" : "/static/images/tabbar/点赞.png",
        h: common_vendor.t(comment.likes),
        i: comment.isLiked ? 1 : "",
        j: common_vendor.o(($event) => $setup.toggleCommentLike(index), index),
        k: comment.replies && comment.replies.length > 0
      }, comment.replies && comment.replies.length > 0 ? common_vendor.e({
        l: common_vendor.f(comment.showAllReplies ? comment.replies : comment.replies.slice(0, 2), (reply, replyIndex, i1) => {
          return common_vendor.e({
            a: common_vendor.t(reply.name),
            b: reply.isOfficial
          }, reply.isOfficial ? {} : {}, {
            c: reply.replyTo
          }, reply.replyTo ? {} : {}, {
            d: reply.replyTo
          }, reply.replyTo ? {
            e: common_vendor.t(reply.replyTo)
          } : {}, {
            f: common_vendor.t(reply.content),
            g: common_vendor.t(reply.time),
            h: replyIndex,
            i: common_vendor.o(($event) => $setup.showReplyInput(index, reply.name), replyIndex)
          });
        }),
        m: comment.replies.length > 2 && !comment.showAllReplies
      }, comment.replies.length > 2 && !comment.showAllReplies ? {
        n: common_vendor.t(comment.replies.length - 2),
        o: common_vendor.o(($event) => $setup.toggleReplies(index), index)
      } : {}, {
        p: comment.replies.length > 2 && comment.showAllReplies
      }, comment.replies.length > 2 && comment.showAllReplies ? {
        q: common_vendor.o(($event) => $setup.toggleReplies(index), index)
      } : {}) : {}, {
        r: index
      });
    }),
    H: common_assets._imports_5$22,
    I: $setup.hasMoreComments
  }, $setup.hasMoreComments ? {
    J: common_assets._imports_1$30,
    K: common_vendor.o((...args) => $setup.loadMoreComments && $setup.loadMoreComments(...args))
  } : {}) : {
    L: common_assets._imports_7$10
  }, {
    M: $setup.replyPlaceholder,
    N: $setup.inputFocus,
    O: common_vendor.o((...args) => $setup.submitComment && $setup.submitComment(...args)),
    P: common_vendor.o((...args) => $setup.onInputFocus && $setup.onInputFocus(...args)),
    Q: common_vendor.o((...args) => $setup.onInputBlur && $setup.onInputBlur(...args)),
    R: $setup.commentText,
    S: common_vendor.o(($event) => $setup.commentText = $event.detail.value),
    T: $setup.commentText.trim().length > 0 ? 1 : "",
    U: common_vendor.o((...args) => $setup.submitComment && $setup.submitComment(...args)),
    V: common_vendor.o($setup.handleInfoTabChange),
    W: common_vendor.p({
      allInfoList: $setup.filteredInfoList,
      toppedInfoList: $setup.filteredToppedInfoList,
      adBanner: $setup.adBanner
    }),
    X: common_assets._imports_8$7,
    Y: common_vendor.o(($event) => $setup.navigateTo("/pages/index/index")),
    Z: common_assets._imports_9$9,
    aa: common_vendor.o(($event) => $setup.navigateTo("/pages/business/business")),
    ab: common_assets._imports_10$6,
    ac: common_vendor.o(($event) => $setup.navigateTo("/pages/publish/publish")),
    ad: common_assets._imports_11$5,
    ae: common_vendor.o(($event) => $setup.navigateTo("/pages/group/group")),
    af: common_assets._imports_12$4,
    ag: common_vendor.o(($event) => $setup.navigateTo("/pages/my/my")),
    ah: $setup.showBackToTop
  }, $setup.showBackToTop ? {
    ai: common_assets._imports_0$14,
    aj: common_vendor.o((...args) => $setup.scrollToTop && $setup.scrollToTop(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
_sfc_main.__runtimeHooks = 7;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/news/pages/detail.js.map
