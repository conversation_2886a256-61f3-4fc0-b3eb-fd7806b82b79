{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/distribution/orders/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcZGlzdHJpYnV0aW9uXG9yZGVyc1xpbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"orders-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\">\r\n      <view class=\"navbar-bg\"></view>\r\n      <view class=\"navbar-content\">\r\n        <view class=\"back-btn\" @click=\"navigateBack\">\r\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n            <path d=\"M19 12H5M12 19l-7-7 7-7\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n          </svg>\r\n        </view>\r\n        <view class=\"navbar-title\">分销订单</view>\r\n        <view class=\"navbar-right\">\r\n          <view class=\"search-btn\" @click=\"showSearch\">\r\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\">\r\n              <circle cx=\"11\" cy=\"11\" r=\"8\" stroke=\"#FFFFFF\" stroke-width=\"2\"></circle>\r\n              <path d=\"m21 21-4.35-4.35\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            </svg>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 内容区域 -->\r\n    <view class=\"content\">\r\n      <!-- 订单统计卡片 -->\r\n      <view class=\"stats-card\" :style=\"{\r\n        borderRadius: '35px',\r\n        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',\r\n        background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)',\r\n        padding: '30rpx',\r\n        marginBottom: '30rpx'\r\n      }\">\r\n        <view class=\"stats-grid\">\r\n          <view class=\"stat-item\">\r\n            <text class=\"stat-number\">{{ orderStats.totalCount }}</text>\r\n            <text class=\"stat-label\">总订单</text>\r\n          </view>\r\n          <view class=\"stat-item\">\r\n            <text class=\"stat-number\">{{ orderStats.todayCount }}</text>\r\n            <text class=\"stat-label\">今日新增</text>\r\n          </view>\r\n          <view class=\"stat-item\">\r\n            <text class=\"stat-number\">{{ orderStats.pendingCount }}</text>\r\n            <text class=\"stat-label\">待处理</text>\r\n          </view>\r\n          <view class=\"stat-item\">\r\n            <text class=\"stat-number\">¥{{ orderStats.totalCommission }}</text>\r\n            <text class=\"stat-label\">总佣金</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 订单筛选标签 -->\r\n      <view class=\"filter-tabs\" :style=\"{\r\n        borderRadius: '35px',\r\n        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',\r\n        background: '#FFFFFF',\r\n        padding: '20rpx 30rpx',\r\n        marginBottom: '30rpx'\r\n      }\">\r\n        <scroll-view class=\"tabs-scroll\" scroll-x>\r\n          <view class=\"tabs-container\">\r\n            <view \r\n              v-for=\"(tab, index) in orderTabs\" \r\n              :key=\"index\"\r\n              class=\"tab-item\"\r\n              :class=\"{ active: currentTab === index }\"\r\n              @click=\"switchTab(index)\"\r\n            >\r\n              <text>{{ tab.name }}</text>\r\n              <view class=\"tab-indicator\" v-if=\"currentTab === index\"></view>\r\n            </view>\r\n          </view>\r\n        </scroll-view>\r\n      </view>\r\n      \r\n      <!-- 订单列表 -->\r\n      <view class=\"orders-list\">\r\n        <view \r\n          v-for=\"order in filteredOrders\"\r\n          :key=\"order.id\"\r\n          class=\"order-card\"\r\n          :style=\"{\r\n            borderRadius: '35px',\r\n            boxShadow: '0 8px 20px rgba(172,57,255,0.15)',\r\n            background: '#FFFFFF',\r\n            padding: '30rpx',\r\n            marginBottom: '30rpx'\r\n          }\"\r\n          @click=\"viewOrderDetail(order)\"\r\n        >\r\n          <!-- 订单头部 -->\r\n          <view class=\"order-header\">\r\n            <view class=\"order-info\">\r\n              <text class=\"order-number\">订单号：{{ order.orderNumber }}</text>\r\n              <text class=\"order-time\">{{ order.createTime }}</text>\r\n            </view>\r\n            <view class=\"order-status\" :style=\"{\r\n              background: getStatusBackground(order.status),\r\n              borderRadius: '20rpx',\r\n              padding: '8rpx 16rpx'\r\n            }\">\r\n              <text :style=\"{ color: getStatusColor(order.status) }\">{{ getStatusText(order.status) }}</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 商品信息 -->\r\n          <view class=\"product-info\">\r\n            <image class=\"product-image\" :src=\"order.productImage\" mode=\"aspectFill\"></image>\r\n            <view class=\"product-details\">\r\n              <text class=\"product-name\">{{ order.productName }}</text>\r\n              <view class=\"product-specs\">\r\n                <text class=\"product-spec\">{{ order.specification }}</text>\r\n              </view>\r\n              <view class=\"price-quantity\">\r\n                <text class=\"product-price\">¥{{ order.productPrice }}</text>\r\n                <text class=\"product-quantity\">x{{ order.quantity }}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 订单底部 -->\r\n          <view class=\"order-footer\">\r\n            <view class=\"customer-info\">\r\n              <text class=\"customer-label\">购买用户：</text>\r\n              <text class=\"customer-name\">{{ order.customerName }}</text>\r\n            </view>\r\n            <view class=\"commission-info\">\r\n              <text class=\"commission-label\">预计佣金：</text>\r\n              <text class=\"commission-amount\">¥{{ order.commission }}</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 操作按钮 -->\r\n          <view class=\"order-actions\" v-if=\"getOrderActions(order.status).length > 0\">\r\n            <view \r\n              v-for=\"action in getOrderActions(order.status)\"\r\n              :key=\"action.key\"\r\n              class=\"action-btn\"\r\n              :style=\"{\r\n                background: action.primary ? 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)' : 'transparent',\r\n                border: action.primary ? 'none' : '2rpx solid #AC39FF',\r\n                borderRadius: '25rpx',\r\n                padding: '12rpx 24rpx'\r\n              }\"\r\n              @click.stop=\"handleAction(action.key, order)\"\r\n            >\r\n              <text :style=\"{ color: action.primary ? '#FFFFFF' : '#AC39FF' }\">{{ action.text }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 空状态 -->\r\n        <view class=\"empty-state\" v-if=\"filteredOrders.length === 0\">\r\n          <view class=\"empty-content\" :style=\"{\r\n            borderRadius: '35px',\r\n            background: '#FFFFFF',\r\n            padding: '80rpx 40rpx',\r\n            textAlign: 'center'\r\n          }\">\r\n            <svg class=\"empty-icon\" viewBox=\"0 0 24 24\" width=\"80\" height=\"80\">\r\n              <path d=\"M9 17h6M9 13h6M9 9h6M5 21V5a2 2 0 012-2h10a2 2 0 012 2v16l-3-2-2 2-2-2-2 2-2-2-3 2z\" stroke=\"#C7C7CC\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            </svg>\r\n            <text class=\"empty-text\">暂无{{ orderTabs[currentTab].name }}订单</text>\r\n            <view class=\"empty-action\" @click=\"navigateTo('/subPackages/activity-showcase/pages/distribution/index')\" :style=\"{\r\n              background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)',\r\n              borderRadius: '30rpx',\r\n              padding: '16rpx 32rpx',\r\n              marginTop: '40rpx'\r\n            }\">\r\n              <text style=\"color: #FFFFFF;\">去推广商品</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue';\r\n\r\n// 页面状态\r\nconst currentTab = ref(0);\r\nconst searchKeyword = ref('');\r\n\r\n// 订单统计\r\nconst orderStats = ref({\r\n  totalCount: 156,\r\n  todayCount: 12,\r\n  pendingCount: 8,\r\n  totalCommission: 2456.78\r\n});\r\n\r\n// 订单标签\r\nconst orderTabs = ref([\r\n  { name: '全部', status: 'all' },\r\n  { name: '待付款', status: 'pending_payment' },\r\n  { name: '待发货', status: 'pending_delivery' },\r\n  { name: '已发货', status: 'delivered' },\r\n  { name: '已完成', status: 'completed' },\r\n  { name: '已失效', status: 'invalid' }\r\n]);\r\n\r\n// 订单列表\r\nconst orderList = ref([\r\n  {\r\n    id: 'O001',\r\n    orderNumber: '20240101001',\r\n    status: 'completed',\r\n    createTime: '2024-01-01 14:30',\r\n    productName: 'iPhone 15 Pro 深空黑 256GB',\r\n    productImage: 'https://via.placeholder.com/120',\r\n    specification: '深空黑 256GB',\r\n    productPrice: 8999,\r\n    quantity: 1,\r\n    customerName: '张三',\r\n    commission: 449.95\r\n  },\r\n  {\r\n    id: 'O002',\r\n    orderNumber: '20240102001',\r\n    status: 'pending_payment',\r\n    createTime: '2024-01-02 10:15',\r\n    productName: '华为Mate 60 Pro 雅川青 512GB',\r\n    productImage: 'https://via.placeholder.com/120',\r\n    specification: '雅川青 512GB',\r\n    productPrice: 6999,\r\n    quantity: 1,\r\n    customerName: '李四',\r\n    commission: 349.95\r\n  },\r\n  {\r\n    id: 'O003',\r\n    orderNumber: '20240103001',\r\n    status: 'pending_delivery',\r\n    createTime: '2024-01-03 16:20',\r\n    productName: '小米14 Ultra 白色 1TB',\r\n    productImage: 'https://via.placeholder.com/120',\r\n    specification: '白色 1TB',\r\n    productPrice: 5999,\r\n    quantity: 2,\r\n    customerName: '王五',\r\n    commission: 599.90\r\n  }\r\n]);\r\n\r\n// 计算属性\r\nconst filteredOrders = computed(() => {\r\n  const status = orderTabs.value[currentTab.value].status;\r\n  if (status === 'all') {\r\n    return orderList.value;\r\n  }\r\n  return orderList.value.filter(order => order.status === status);\r\n});\r\n\r\n// 方法\r\nconst navigateBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\nconst showSearch = () => {\r\n  uni.showToast({\r\n    title: '搜索功能开发中',\r\n    icon: 'none'\r\n  });\r\n};\r\n\r\nconst switchTab = (index) => {\r\n  currentTab.value = index;\r\n};\r\n\r\nconst getStatusColor = (status) => {\r\n  switch(status) {\r\n    case 'pending_payment': return '#FF9500';\r\n    case 'pending_delivery': return '#5AC8FA';\r\n    case 'delivered': return '#34C759';\r\n    case 'completed': return '#AC39FF';\r\n    case 'invalid': return '#8E8E93';\r\n    default: return '#333333';\r\n  }\r\n};\r\n\r\nconst getStatusBackground = (status) => {\r\n  switch(status) {\r\n    case 'pending_payment': return 'rgba(255,149,0,0.1)';\r\n    case 'pending_delivery': return 'rgba(90,200,250,0.1)';\r\n    case 'delivered': return 'rgba(52,199,89,0.1)';\r\n    case 'completed': return 'rgba(172,57,255,0.1)';\r\n    case 'invalid': return 'rgba(142,142,147,0.1)';\r\n    default: return 'rgba(51,51,51,0.1)';\r\n  }\r\n};\r\n\r\nconst getStatusText = (status) => {\r\n  switch(status) {\r\n    case 'pending_payment': return '待付款';\r\n    case 'pending_delivery': return '待发货';\r\n    case 'delivered': return '已发货';\r\n    case 'completed': return '已完成';\r\n    case 'invalid': return '已失效';\r\n    default: return '未知状态';\r\n  }\r\n};\r\n\r\nconst getOrderActions = (status) => {\r\n  switch(status) {\r\n    case 'pending_payment':\r\n      return [\r\n        { key: 'remind', text: '提醒付款', primary: false },\r\n        { key: 'cancel', text: '取消订单', primary: false }\r\n      ];\r\n    case 'pending_delivery':\r\n      return [\r\n        { key: 'track', text: '查看物流', primary: true }\r\n      ];\r\n    case 'delivered':\r\n      return [\r\n        { key: 'track', text: '查看物流', primary: true }\r\n      ];\r\n    default:\r\n      return [];\r\n  }\r\n};\r\n\r\nconst handleAction = (action, order) => {\r\n  switch(action) {\r\n    case 'remind':\r\n      uni.showToast({ title: '已提醒用户付款', icon: 'success' });\r\n      break;\r\n    case 'cancel':\r\n      uni.showModal({\r\n        title: '确认取消',\r\n        content: '确定要取消这个订单吗？',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            uni.showToast({ title: '订单已取消', icon: 'success' });\r\n          }\r\n        }\r\n      });\r\n      break;\r\n    case 'track':\r\n      uni.showToast({ title: '查看物流功能开发中', icon: 'none' });\r\n      break;\r\n  }\r\n};\r\n\r\nconst viewOrderDetail = (order) => {\r\n  uni.navigateTo({\r\n    url: `/subPackages/activity-showcase/pages/distribution/order-detail/index?id=${order.id}`\r\n  });\r\n};\r\n\r\nconst navigateTo = (url) => {\r\n  uni.navigateTo({ url });\r\n};\r\n\r\nonMounted(() => {\r\n  // 页面加载时的初始化操作\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.orders-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100vh;\r\n  background: linear-gradient(180deg, #F2F2F7 0%, #E8E8ED 100%);\r\n}\r\n\r\n.custom-navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: calc(var(--status-bar-height, 25px) + 62px);\r\n  z-index: 100;\r\n  \r\n  .navbar-bg {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%);\r\n  }\r\n  \r\n  .navbar-content {\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: 62px;\r\n    margin-top: var(--status-bar-height, 25px);\r\n    padding: 0 30rpx;\r\n    \r\n    .back-btn, .search-btn {\r\n      width: 80rpx;\r\n      height: 80rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      border-radius: 50%;\r\n      background: rgba(255, 255, 255, 0.2);\r\n      \r\n      .icon {\r\n        width: 48rpx;\r\n        height: 48rpx;\r\n      }\r\n    }\r\n    \r\n    .navbar-title {\r\n      font-size: 36rpx;\r\n      font-weight: 600;\r\n      color: #FFFFFF;\r\n    }\r\n    \r\n    .navbar-right {\r\n      width: 80rpx;\r\n      display: flex;\r\n      justify-content: flex-end;\r\n    }\r\n  }\r\n}\r\n\r\n.content {\r\n  margin-top: calc(var(--status-bar-height, 25px) + 62px);\r\n  padding: 30rpx;\r\n  flex: 1;\r\n}\r\n\r\n.stats-card {\r\n  .stats-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(4, 1fr);\r\n    gap: 20rpx;\r\n    \r\n    .stat-item {\r\n      text-align: center;\r\n      \r\n      .stat-number {\r\n        display: block;\r\n        font-size: 32rpx;\r\n        font-weight: 600;\r\n        color: #FFFFFF;\r\n        margin-bottom: 8rpx;\r\n      }\r\n      \r\n      .stat-label {\r\n        font-size: 24rpx;\r\n        color: rgba(255, 255, 255, 0.8);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.filter-tabs {\r\n  .tabs-scroll {\r\n    width: 100%;\r\n    \r\n    .tabs-container {\r\n      display: flex;\r\n      white-space: nowrap;\r\n      \r\n      .tab-item {\r\n        position: relative;\r\n        padding: 20rpx 30rpx;\r\n        margin-right: 20rpx;\r\n        \r\n        text {\r\n          font-size: 28rpx;\r\n          color: #8E8E93;\r\n          transition: color 0.3s ease;\r\n        }\r\n        \r\n        .tab-indicator {\r\n          position: absolute;\r\n          bottom: 10rpx;\r\n          left: 50%;\r\n          transform: translateX(-50%);\r\n          width: 40rpx;\r\n          height: 4rpx;\r\n          border-radius: 2rpx;\r\n          background: linear-gradient(90deg, #AC39FF 0%, #B87AFF 100%);\r\n        }\r\n        \r\n        &.active text {\r\n          color: #AC39FF;\r\n          font-weight: 500;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.order-card {\r\n  .order-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: flex-start;\r\n    margin-bottom: 20rpx;\r\n    \r\n    .order-info {\r\n      .order-number {\r\n        display: block;\r\n        font-size: 28rpx;\r\n        color: #333333;\r\n        font-weight: 500;\r\n        margin-bottom: 8rpx;\r\n      }\r\n      \r\n      .order-time {\r\n        font-size: 24rpx;\r\n        color: #8E8E93;\r\n      }\r\n    }\r\n    \r\n    .order-status text {\r\n      font-size: 24rpx;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n  \r\n  .product-info {\r\n    display: flex;\r\n    margin-bottom: 20rpx;\r\n    \r\n    .product-image {\r\n      width: 120rpx;\r\n      height: 120rpx;\r\n      border-radius: 16rpx;\r\n      margin-right: 20rpx;\r\n    }\r\n    \r\n    .product-details {\r\n      flex: 1;\r\n      \r\n      .product-name {\r\n        display: block;\r\n        font-size: 28rpx;\r\n        color: #333333;\r\n        font-weight: 500;\r\n        margin-bottom: 12rpx;\r\n        line-height: 1.4;\r\n      }\r\n      \r\n      .product-specs {\r\n        margin-bottom: 12rpx;\r\n        \r\n        .product-spec {\r\n          font-size: 24rpx;\r\n          color: #8E8E93;\r\n        }\r\n      }\r\n      \r\n      .price-quantity {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        \r\n        .product-price {\r\n          font-size: 32rpx;\r\n          color: #FF3B69;\r\n          font-weight: 600;\r\n        }\r\n        \r\n        .product-quantity {\r\n          font-size: 24rpx;\r\n          color: #8E8E93;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .order-footer {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 20rpx 0;\r\n    border-top: 1rpx solid #F0F0F0;\r\n    margin-bottom: 20rpx;\r\n    \r\n    .customer-info, .commission-info {\r\n      .customer-label, .commission-label {\r\n        font-size: 24rpx;\r\n        color: #8E8E93;\r\n      }\r\n      \r\n      .customer-name {\r\n        font-size: 26rpx;\r\n        color: #333333;\r\n        font-weight: 500;\r\n      }\r\n      \r\n      .commission-amount {\r\n        font-size: 28rpx;\r\n        color: #AC39FF;\r\n        font-weight: 600;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .order-actions {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    gap: 20rpx;\r\n    \r\n    .action-btn {\r\n      text {\r\n        font-size: 26rpx;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.empty-state {\r\n  .empty-content {\r\n    .empty-icon {\r\n      margin-bottom: 30rpx;\r\n    }\r\n    \r\n    .empty-text {\r\n      display: block;\r\n      font-size: 28rpx;\r\n      color: #8E8E93;\r\n      margin-bottom: 20rpx;\r\n    }\r\n    \r\n    .empty-action text {\r\n      font-size: 28rpx;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/distribution/orders/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "onMounted"], "mappings": ";;;;;;;;;;;AAwLA,UAAM,aAAaA,cAAAA,IAAI,CAAC;AACFA,kBAAG,IAAC,EAAE;AAG5B,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACrB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,iBAAiB;AAAA,IACnB,CAAC;AAGD,UAAM,YAAYA,cAAAA,IAAI;AAAA,MACpB,EAAE,MAAM,MAAM,QAAQ,MAAO;AAAA,MAC7B,EAAE,MAAM,OAAO,QAAQ,kBAAmB;AAAA,MAC1C,EAAE,MAAM,OAAO,QAAQ,mBAAoB;AAAA,MAC3C,EAAE,MAAM,OAAO,QAAQ,YAAa;AAAA,MACpC,EAAE,MAAM,OAAO,QAAQ,YAAa;AAAA,MACpC,EAAE,MAAM,OAAO,QAAQ,UAAW;AAAA,IACpC,CAAC;AAGD,UAAM,YAAYA,cAAAA,IAAI;AAAA,MACpB;AAAA,QACE,IAAI;AAAA,QACJ,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,cAAc;AAAA,QACd,eAAe;AAAA,QACf,cAAc;AAAA,QACd,UAAU;AAAA,QACV,cAAc;AAAA,QACd,YAAY;AAAA,MACb;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,cAAc;AAAA,QACd,eAAe;AAAA,QACf,cAAc;AAAA,QACd,UAAU;AAAA,QACV,cAAc;AAAA,QACd,YAAY;AAAA,MACb;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,cAAc;AAAA,QACd,eAAe;AAAA,QACf,cAAc;AAAA,QACd,UAAU;AAAA,QACV,cAAc;AAAA,QACd,YAAY;AAAA,MACb;AAAA,IACH,CAAC;AAGD,UAAM,iBAAiBC,cAAQ,SAAC,MAAM;AACpC,YAAM,SAAS,UAAU,MAAM,WAAW,KAAK,EAAE;AACjD,UAAI,WAAW,OAAO;AACpB,eAAO,UAAU;AAAA,MAClB;AACD,aAAO,UAAU,MAAM,OAAO,WAAS,MAAM,WAAW,MAAM;AAAA,IAChE,CAAC;AAGD,UAAM,eAAe,MAAM;AACzBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAEA,UAAM,aAAa,MAAM;AACvBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAEA,UAAM,YAAY,CAAC,UAAU;AAC3B,iBAAW,QAAQ;AAAA,IACrB;AAEA,UAAM,iBAAiB,CAAC,WAAW;AACjC,cAAO,QAAM;AAAA,QACX,KAAK;AAAmB,iBAAO;AAAA,QAC/B,KAAK;AAAoB,iBAAO;AAAA,QAChC,KAAK;AAAa,iBAAO;AAAA,QACzB,KAAK;AAAa,iBAAO;AAAA,QACzB,KAAK;AAAW,iBAAO;AAAA,QACvB;AAAS,iBAAO;AAAA,MACjB;AAAA,IACH;AAEA,UAAM,sBAAsB,CAAC,WAAW;AACtC,cAAO,QAAM;AAAA,QACX,KAAK;AAAmB,iBAAO;AAAA,QAC/B,KAAK;AAAoB,iBAAO;AAAA,QAChC,KAAK;AAAa,iBAAO;AAAA,QACzB,KAAK;AAAa,iBAAO;AAAA,QACzB,KAAK;AAAW,iBAAO;AAAA,QACvB;AAAS,iBAAO;AAAA,MACjB;AAAA,IACH;AAEA,UAAM,gBAAgB,CAAC,WAAW;AAChC,cAAO,QAAM;AAAA,QACX,KAAK;AAAmB,iBAAO;AAAA,QAC/B,KAAK;AAAoB,iBAAO;AAAA,QAChC,KAAK;AAAa,iBAAO;AAAA,QACzB,KAAK;AAAa,iBAAO;AAAA,QACzB,KAAK;AAAW,iBAAO;AAAA,QACvB;AAAS,iBAAO;AAAA,MACjB;AAAA,IACH;AAEA,UAAM,kBAAkB,CAAC,WAAW;AAClC,cAAO,QAAM;AAAA,QACX,KAAK;AACH,iBAAO;AAAA,YACL,EAAE,KAAK,UAAU,MAAM,QAAQ,SAAS,MAAO;AAAA,YAC/C,EAAE,KAAK,UAAU,MAAM,QAAQ,SAAS,MAAO;AAAA,UACvD;AAAA,QACI,KAAK;AACH,iBAAO;AAAA,YACL,EAAE,KAAK,SAAS,MAAM,QAAQ,SAAS,KAAM;AAAA,UACrD;AAAA,QACI,KAAK;AACH,iBAAO;AAAA,YACL,EAAE,KAAK,SAAS,MAAM,QAAQ,SAAS,KAAM;AAAA,UACrD;AAAA,QACI;AACE,iBAAO;MACV;AAAA,IACH;AAEA,UAAM,eAAe,CAAC,QAAQ,UAAU;AACtC,cAAO,QAAM;AAAA,QACX,KAAK;AACHA,wBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,UAAS,CAAE;AACnD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS;AAAA,YACT,SAAS,CAAC,QAAQ;AAChB,kBAAI,IAAI,SAAS;AACfA,8BAAG,MAAC,UAAU,EAAE,OAAO,SAAS,MAAM,UAAS,CAAE;AAAA,cAClD;AAAA,YACF;AAAA,UACT,CAAO;AACD;AAAA,QACF,KAAK;AACHA,wBAAG,MAAC,UAAU,EAAE,OAAO,aAAa,MAAM,OAAM,CAAE;AAClD;AAAA,MACH;AAAA,IACH;AAEA,UAAM,kBAAkB,CAAC,UAAU;AACjCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,2EAA2E,MAAM,EAAE;AAAA,MAC5F,CAAG;AAAA,IACH;AAEA,UAAM,aAAa,CAAC,QAAQ;AAC1BA,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IACxB;AAEAC,kBAAAA,UAAU,MAAM;AAAA,IAEhB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvWD,GAAG,WAAW,eAAe;"}