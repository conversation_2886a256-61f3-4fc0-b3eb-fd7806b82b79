{"version": 3, "file": "group.js", "sources": ["pages/group/group.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZ3JvdXAvZ3JvdXAudnVl"], "sourcesContent": ["<template>\n\t<view class=\"group-page\">\n\t\t<!-- 自定义导航栏 -->\n\t\t<view class=\"custom-navbar\">\n\t\t\t<view class=\"navbar-title\">进群</view>\n\t\t</view>\n\t\t\n\t\t<!-- 搜索框 -->\n\t\t<view class=\"search-box\">\n\t\t\t<view class=\"search-icon-wrap\">\n\t\t\t\t<image class=\"search-icon\" src=\"/static/images/tabbar/search-dark.png\"></image>\n\t\t\t</view>\n\t\t\t<input \n\t\t\t\tclass=\"search-input\" \n\t\t\t\ttype=\"text\" \n\t\t\t\tv-model=\"searchKeyword\" \n\t\t\t\tplaceholder=\"搜索群组名称\" \n\t\t\t\tconfirm-type=\"search\"\n\t\t\t\t@confirm=\"searchGroups\"\n\t\t\t/>\n\t\t\t<view class=\"voice-icon-wrap\" @click=\"useVoiceSearch\">\n\t\t\t\t<image class=\"voice-icon\" src=\"/static/images/tabbar/语音.png\"></image>\n\t\t\t</view>\n\t\t\t<view class=\"search-btn\" @click=\"searchGroups\">\n\t\t\t\t<image class=\"search-btn-icon\" src=\"/static/images/tabbar/放大镜.png\"></image>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\n\t\t<!-- 左侧类别栏和右侧内容区 -->\n\t\t<view class=\"content-container\">\n\t\t\t<!-- 左侧类别栏 -->\n\t\t\t<scroll-view class=\"category-list\" scroll-y>\n\t\t\t\t<view \n\t\t\t\t\tclass=\"category-item\" \n\t\t\t\t\t:class=\"{'active': currentCategory === category.id}\"\n\t\t\t\t\tv-for=\"(category, index) in categories\" \n\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t@click=\"changeCategory(category.id)\"\n\t\t\t\t>\n\t\t\t\t\t<text class=\"category-text\">{{ category.name }}</text>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t\t\n\t\t\t<!-- 右侧内容区 -->\n\t\t\t<scroll-view class=\"group-content\" scroll-y>\n\t\t\t\t<!-- 显示的群组 -->\n\t\t\t\t<view \n\t\t\t\t\tclass=\"group-item\" \n\t\t\t\t\tv-for=\"(group, index) in displayGroups\" \n\t\t\t\t\t:key=\"index\"\n\t\t\t\t>\n\t\t\t\t\t<image class=\"group-avatar\" :src=\"group.avatar || '/static/images/tabbar/default-group.png'\"></image>\n\t\t\t\t\t\t\t<view class=\"group-info\">\n\t\t\t\t\t\t<text class=\"group-name\">{{ group.name }}</text>\n\t\t\t\t\t\t<text class=\"group-desc\">{{ group.desc.length > 8 ? group.desc.substring(0, 8) + '...' : group.desc }}</text>\n\t\t\t\t\t\t<text class=\"group-count\">{{ group.memberCount || '88' }}人</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<button class=\"join-button\" @click=\"joinGroup(group)\">加入</button>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 无数据提示 -->\n\t\t\t\t<view class=\"no-data\" v-if=\"displayGroups.length === 0\">\n\t\t\t\t\t<text class=\"no-data-text\">暂无群组数据</text>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t\n\t\t<!-- 悬浮按钮 -->\n\t\t<view class=\"share-button\" @click=\"onShare\">\n\t\t\t<image class=\"share-icon\" src=\"/static/images/tabbar/share.png\"></image>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tsearchKeyword: '',\n\t\t\t\tcurrentCategory: 'hot',\n\t\t\t\tcategories: [\n\t\t\t\t\t{ id: 'hot', name: '热门群' },\n\t\t\t\t\t{ id: 'service', name: '服务群' },\n\t\t\t\t\t{ id: 'bus', name: '拼车群' },\n\t\t\t\t\t{ id: 'community', name: '小区群' },\n\t\t\t\t\t{ id: 'town', name: '老乡群' },\n\t\t\t\t\t{ id: 'village', name: '乡镇群' },\n\t\t\t\t\t{ id: 'shop', name: '商家群' },\n\t\t\t\t\t{ id: 'job', name: '求职群' },\n\t\t\t\t\t{ id: 'parents', name: '家长群' },\n\t\t\t\t\t{ id: 'hobby', name: '兴趣群' }\n\t\t\t\t],\n\t\t\t\tgroups: [\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 1,\n\t\t\t\t\t\tname: '磁县招聘求职群',\n\t\t\t\t\t\tdesc: '本地招聘求职信息发布平台，每日更新...',\n\t\t\t\t\t\tavatar: '/static/images/tabbar/group-avatar1.png',\n\t\t\t\t\t\tcategory: 'hot',\n\t\t\t\t\t\tmemberCount: 358\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 2,\n\t\t\t\t\t\tname: '磁县生活服务群',\n\t\t\t\t\t\tdesc: '本地生活服务信息共享',\n\t\t\t\t\t\tavatar: '/static/images/tabbar/group-avatar2.png',\n\t\t\t\t\t\tcategory: 'service',\n\t\t\t\t\t\tmemberCount: 206\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 3,\n\t\t\t\t\t\tname: '磁县-邯郸拼车群',\n\t\t\t\t\t\tdesc: '磁县到邯郸的拼车信息',\n\t\t\t\t\t\tavatar: '/static/images/tabbar/group-avatar3.png',\n\t\t\t\t\t\tcategory: 'bus',\n\t\t\t\t\t\tmemberCount: 129\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 4,\n\t\t\t\t\t\tname: '怡馨小区业主群',\n\t\t\t\t\t\tdesc: '小区业主交流互助平台',\n\t\t\t\t\t\tavatar: '/static/images/tabbar/group-avatar4.png',\n\t\t\t\t\t\tcategory: 'community',\n\t\t\t\t\t\tmemberCount: 186\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 5,\n\t\t\t\t\t\tname: '磁县老乡群',\n\t\t\t\t\t\tdesc: '磁县老乡交流群',\n\t\t\t\t\t\tavatar: '/static/images/tabbar/group-avatar5.png',\n\t\t\t\t\t\tcategory: 'town',\n\t\t\t\t\t\tmemberCount: 412\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 6,\n\t\t\t\t\t\tname: '观台镇群',\n\t\t\t\t\t\tdesc: '观台镇本地信息分享',\n\t\t\t\t\t\tavatar: '/static/images/tabbar/group-avatar6.png',\n\t\t\t\t\t\tcategory: 'village',\n\t\t\t\t\t\tmemberCount: 168\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 7,\n\t\t\t\t\t\tname: '磁县家电商家群',\n\t\t\t\t\t\tdesc: '家电商家交流平台',\n\t\t\t\t\t\tcategory: 'shop',\n\t\t\t\t\t\tmemberCount: 98\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 8,\n\t\t\t\t\t\tname: '磁县初中家长群',\n\t\t\t\t\t\tdesc: '初中家长信息交流平台',\n\t\t\t\t\t\tcategory: 'parents',\n\t\t\t\t\t\tmemberCount: 145\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tisSearching: false\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tfilteredGroups() {\n\t\t\t\treturn this.groups.filter(group => group.category === this.currentCategory);\n\t\t\t},\n\t\t\tdisplayGroups() {\n\t\t\t\t// 如果搜索关键词为空，则显示当前分类下的群组\n\t\t\t\tif (!this.searchKeyword.trim() || !this.isSearching) {\n\t\t\t\t\treturn this.filteredGroups;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 如果有搜索关键词，则搜索所有群组\n\t\t\t\tconst keyword = this.searchKeyword.toLowerCase().trim();\n\t\t\t\treturn this.groups.filter(group => \n\t\t\t\t\tgroup.name.toLowerCase().includes(keyword) || \n\t\t\t\t\tgroup.desc.toLowerCase().includes(keyword)\n\t\t\t\t);\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t},\n\t\tonShareAppMessage() {\n\t\t\t// 自定义转发内容\n\t\t\treturn {\n\t\t\t\ttitle: '快来加入磁县拼车群，小区群，老乡群，求职招聘群，家长群把！',\n\t\t\t\tpath: '/pages/group/group'\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tchangeCategory(categoryId) {\n\t\t\t\tthis.currentCategory = categoryId;\n\t\t\t\t// 切换分类时重置搜索状态\n\t\t\t\tthis.searchKeyword = '';\n\t\t\t\tthis.isSearching = false;\n\t\t\t},\n\t\t\tsearchGroups() {\n\t\t\t\t// 执行搜索操作\n\t\t\t\tthis.isSearching = !!this.searchKeyword.trim();\n\t\t\t},\n\t\t\tuseVoiceSearch() {\n\t\t\t\t// 语音搜索功能，需根据平台支持情况实现\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '语音搜索功能开发中',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t},\n\t\t\tjoinGroup(group) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '加入群聊',\n\t\t\t\t\tcontent: `是否加入\"${group.name}\"？`,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '已发送加群申请',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t},\n\t\t\tonShare() {\n\t\t\t\tif (wx && wx.showShareMenu) {\n\t\t\t\t\twx.showShareMenu({ withShareTicket: true })\n\t\t\t\t}\n\t\t\t\tuni.showToast({ title: '请点击右上角\"...\"进行转发', icon: 'none' })\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.group-page {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tmin-height: 100vh;\n\t\tbackground-color: #f7f7f7;\n\t\tposition: relative;\n\t}\n\t\n\t/* 自定义导航栏 */\n\t.custom-navbar {\n\t\tbackground-image: linear-gradient(135deg, #0052CC, #0066FF);\n\t\theight: 88rpx;\n\t\tpadding-top: 44px; /* 状态栏高度 */\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tposition: relative;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n\t\tz-index: 10;\n\t}\n\t\n\t.navbar-title {\n\t\tflex: 1;\n\t\ttext-align: center;\n\t\tcolor: #FFFFFF;\n\t\tfont-size: 36rpx;\n\t\tfont-weight: 500;\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n\t}\n\t\n\t/* 搜索框 */\n\t.search-box {\n\t\tbackground-color: #ffffff;\n\t\tmargin: 20rpx;\n\t\theight: 72rpx;\n\t\tborder-radius: 36rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 0 20rpx;\n\t\tbox-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);\n\t\tborder: 1rpx solid rgba(235, 238, 245, 0.8);\n\t}\n\t\n\t.search-icon-wrap {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 0 10rpx;\n\t}\n\t\n\t.search-icon {\n\t\twidth: 32rpx;\n\t\theight: 32rpx;\n\t\topacity: 0.5;\n\t}\n\t\n\t.search-input {\n\t\tflex: 1;\n\t\theight: 72rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t}\n\t\n\t.voice-icon-wrap {\n\t\tpadding: 0 10rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\t\n\t.voice-icon {\n\t\twidth: 36rpx;\n\t\theight: 36rpx;\n\t}\n\t\n\t.search-btn {\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tborder-radius: 50%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-left: 10rpx;\n\t}\n\t\n\t.search-btn-icon {\n\t\twidth: 44rpx;\n\t\theight: 44rpx;\n\t}\n\t\n\t/* 内容区 */\n\t.content-container {\n\t\tdisplay: flex;\n\t\tflex: 1;\n\t\toverflow: hidden;\n\t\theight: calc(100vh - 112rpx);\n\t}\n\t\n\t/* 左侧类别栏 */\n\t.category-list {\n\t\twidth: 180rpx;\n\t\tbackground-color: #f2f2f2;\n\t\theight: calc(100vh - 112rpx);\n\t}\n\t\n\t.category-item {\n\t\theight: 100rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tposition: relative;\n\t}\n\t\n\t.category-item.active {\n\t\tbackground-color: #ffffff;\n\t}\n\t\n\t.category-item.active::before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\ttop: 34rpx;\n\t\theight: 32rpx;\n\t\twidth: 6rpx;\n\t\tbackground-color: #0052CC;\n\t\tborder-radius: 0 3rpx 3rpx 0;\n\t}\n\t\n\t.category-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\ttext-align: center;\n\t}\n\t\n\t.category-item.active .category-text {\n\t\tcolor: #0052CC;\n\t\tfont-weight: 500;\n\t}\n\t\n\t/* 右侧群组内容 */\n\t.group-content {\n\t\tflex: 1;\n\t\tbackground-color: #ffffff;\n\t\theight: calc(100vh - 112rpx);\n\t\tpadding: 0 20rpx;\n\t}\n\t\n\t.group-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 20rpx 0;\n\t\tborder-bottom: 1rpx solid #f2f2f2;\n\t}\n\t\n\t.group-avatar {\n\t\twidth: 90rpx;\n\t\theight: 90rpx;\n\t\tborder-radius: 12rpx;\n\t\tbackground-color: #f0f0f0;\n\t\tmargin-right: 20rpx;\n\t}\n\t\n\t.group-info {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\t\n\t.group-name {\n\t\tfont-size: 30rpx;\n\t\tcolor: #333;\n\t\tfont-weight: 500;\n\t\tmargin-bottom: 8rpx;\n\t}\n\t\n\t.group-desc {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t\tmargin-bottom: 8rpx;\n\t\twhite-space: nowrap;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\tmax-width: 380rpx;\n\t}\n\t\n\t.group-count {\n\t\tfont-size: 22rpx;\n\t\tcolor: #aaa;\n\t}\n\t\n\t.join-button {\n\t\tmin-width: 120rpx;\n\t\theight: 60rpx;\n\t\tbackground-color: #0052CC;\n\t\tcolor: #ffffff;\n\t\tfont-size: 26rpx;\n\t\tborder-radius: 30rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin: 0;\n\t\tpadding: 0 20rpx;\n\t}\n\t\n\t.no-data {\n\t\tpadding: 60rpx 0;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\t\n\t.no-data-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999;\n\t}\n\t\n\t/* 悬浮分享按钮 */\n\t.share-button {\n\t\tposition: fixed;\n\t\tright: 32rpx;\n\t\tbottom: 180rpx;\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tborder-radius: 50%;\n\t\tbackground-color: #FFFFFF;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);\n\t\tz-index: 9;\n\t}\n\t\n\t.share-icon {\n\t\twidth: 36rpx;\n\t\theight: 36rpx;\n\t}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/group/group.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "wx"], "mappings": ";;;;EA4EE,OAAA;AACC,WAAA;AAAA,MACC,eAAA;AAAA,MACA,iBAAA;AAAA;QAEC,EAAA,IAAA,OAAA,MAAA,MAAA;AAAA;QAEA,EAAA,IAAA,OAAA,MAAA,MAAA;AAAA;;;;QAKA,EAAA,IAAA,OAAA,MAAA,MAAA;AAAA;;;MAID,QAAA;AAAA,QACC;AAAA;UAEC,MAAA;AAAA;;UAGA,UAAA;AAAA,UACA,aAAA;AAAA;QAED;AAAA;UAEC,MAAA;AAAA,UACA,MAAA;AAAA;UAEA,UAAA;AAAA,UACA,aAAA;AAAA;QAED;AAAA;UAEC,MAAA;AAAA,UACA,MAAA;AAAA;UAEA,UAAA;AAAA,UACA,aAAA;AAAA;QAED;AAAA;UAEC,MAAA;AAAA,UACA,MAAA;AAAA;UAEA,UAAA;AAAA,UACA,aAAA;AAAA;QAED;AAAA;;UAGC,MAAA;AAAA;UAEA,UAAA;AAAA,UACA,aAAA;AAAA;QAED;AAAA;;UAGC,MAAA;AAAA;UAEA,UAAA;AAAA,UACA,aAAA;AAAA;QAED;AAAA;UAEC,MAAA;AAAA,UACA,MAAA;AAAA,UACA,UAAA;AAAA;;QAGD;AAAA;UAEC,MAAA;AAAA,UACA,MAAA;AAAA,UACA,UAAA;AAAA,UACA,aAAA;AAAA,QACD;AAAA;MAED,aAAA;AAAA,IACD;AAAA;EAED,UAAA;AAAA,IACC,iBAAA;AACC,aAAA,KAAA,OAAA,OAAA,WAAA,MAAA,aAAA,KAAA,eAAA;AAAA;IAED,gBAAA;;;MAIC;;AAIA,aAAA,KAAA,OAAA;AAAA,QAAA,WACC,MAAA,KAAA,cAAA,SAAA,OAAA,KACA,MAAA,KAAA,cAAA,SAAA,OAAA;AAAA;IAEF;AAAA;EAED,SAAA;AAAA;EAEA,oBAAA;AAEC,WAAA;AAAA,MACC,OAAA;AAAA;IAED;AAAA;EAED,SAAA;AAAA;AAEE,WAAA,kBAAA;AAEA,WAAA,gBAAA;;;IAGD,eAAA;AAEC,WAAA,cAAA,CAAA,CAAA,KAAA,cAAA,KAAA;AAAA;IAED,iBAAA;AAECA,oBAAAA,MAAA,UAAA;AAAA,QACC,OAAA;AAAA;MAED,CAAA;AAAA;IAED,UAAA,OAAA;AACCA,oBAAAA,MAAA,UAAA;AAAA;QAEC,SAAA,QAAA,MAAA,IAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACC,cAAA,IAAA,SAAA;AACFA,0BAAAA,MAAA,UAAA;AAAA,cACI,OAAA;AAAA;YAEJ,CAAA;AAAA,UACE;AAAA,QACD;AAAA,MACD,CAAA;AAAA;IAED,UAAA;;AAEEC,sBAAAA,KAAA,cAAA,EAAA,iBAAA,KAAA,CAAA;AAAA,MACD;;IAED;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChOD,GAAG,WAAW,eAAe;"}