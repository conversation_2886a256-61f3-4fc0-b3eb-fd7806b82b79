import request from '../utils/request.js'

// 发布信息相关API
export const publishApi = {
  // 发布信息
  publishPost: async (postData) => {
    try {
      const response = await request.post('/api/posts', postData)
      return response.data
    } catch (error) {
      console.error('发布信息失败:', error)
      throw error
    }
  },

  // 获取信息列表
  getPosts: async (params = {}) => {
    try {
      const response = await request.get('/api/posts', { params })
      return response.data
    } catch (error) {
      console.error('获取信息列表失败:', error)
      return { data: [], total: 0 }
    }
  },

  // 获取信息详情
  getPost: async (id) => {
    try {
      const response = await request.get(`/api/posts/${id}`)
      return response.data
    } catch (error) {
      console.error('获取信息详情失败:', error)
      throw error
    }
  },

  // 更新信息
  updatePost: async (id, postData) => {
    try {
      const response = await request.put(`/api/posts/${id}`, postData)
      return response.data
    } catch (error) {
      console.error('更新信息失败:', error)
      throw error
    }
  },

  // 删除信息
  deletePost: async (id) => {
    try {
      const response = await request.delete(`/api/posts/${id}`)
      return response.data
    } catch (error) {
      console.error('删除信息失败:', error)
      throw error
    }
  },

  // 信息置顶
  topPost: async (id, topData) => {
    try {
      const response = await request.post(`/api/posts/${id}/top`, topData)
      return response.data
    } catch (error) {
      console.error('置顶失败:', error)
      throw error
    }
  },

  // 信息刷新
  refreshPost: async (id) => {
    try {
      const response = await request.post(`/api/posts/${id}/refresh`)
      return response.data
    } catch (error) {
      console.error('刷新失败:', error)
      throw error
    }
  },

  // 发布带红包
  addRedPacket: async (id, redPacketData) => {
    try {
      const response = await request.post(`/api/posts/${id}/redpacket`, redPacketData)
      return response.data
    } catch (error) {
      console.error('添加红包失败:', error)
      throw error
    }
  },

  // 领取红包
  claimRedPacket: async (id, claimData) => {
    try {
      const response = await request.post(`/api/posts/${id}/redpacket/claim`, claimData)
      return response.data
    } catch (error) {
      console.error('领取红包失败:', error)
      throw error
    }
  },

  // 获取配置
  getConfigs: async (category = null) => {
    try {
      const params = category ? { category } : {}
      const response = await request.get('/api/configs', { params })
      return response.data
    } catch (error) {
      console.error('获取配置失败:', error)
      return []
    }
  },

  // 获取发布配置
  getPublishConfigs: async () => {
    try {
      const response = await request.get('/api/configs/publish')
      return response.data
    } catch (error) {
      console.error('获取发布配置失败:', error)
      return {}
    }
  },

  // 上传图片
  uploadImage: async (file) => {
    try {
      const formData = new FormData()
      formData.append('image', file)

      const response = await request.post('/api/upload/image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      return response.data
    } catch (error) {
      console.error('上传图片失败:', error)
      throw error
    }
  },

  // 获取分类信息
  getCategories: async () => {
    try {
      const response = await request.get('/api/categories')
      return response.data
    } catch (error) {
      console.error('获取分类失败:', error)
      return []
    }
  },

  // 获取首页推荐信息
  getHomePosts: async (params = {}) => {
    try {
      const response = await request.get('/api/posts/home', { params })
      return response.data
    } catch (error) {
      console.error('获取首页信息失败:', error)
      return []
    }
  },

  // 获取分类信息
  getCategoryPosts: async (category, params = {}) => {
    try {
      const response = await request.get(`/api/posts/category/${category}`, { params })
      return response.data
    } catch (error) {
      console.error('获取分类信息失败:', error)
      return []
    }
  },

  // 获取置顶信息
  getTopPosts: async (category = null) => {
    try {
      const params = { is_top: true, status: 'approved' }
      if (category) params.category = category
      const response = await request.get('/api/posts', { params })
      return response.data
    } catch (error) {
      console.error('获取置顶信息失败:', error)
      return []
    }
  },

  // 搜索信息
  searchPosts: async (keyword, params = {}) => {
    try {
      const searchParams = { ...params, keyword }
      const response = await request.get('/api/posts/search', { params: searchParams })
      return response.data
    } catch (error) {
      console.error('搜索信息失败:', error)
      return { data: [], total: 0 }
    }
  },

  // 获取我的发布
  getMyPosts: async (params = {}) => {
    try {
      const response = await request.get('/api/posts/my', { params })
      return response.data
    } catch (error) {
      console.error('获取我的发布失败:', error)
      return { data: [], total: 0 }
    }
  },

  // 获取信息统计
  getPostStats: async (id) => {
    try {
      const response = await request.get(`/api/posts/${id}/stats`)
      return response.data
    } catch (error) {
      console.error('获取信息统计失败:', error)
      return {}
    }
  }
}

// 默认导出
export default publishApi