<template>
  <view class="order-management-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">订单管理</text>
      <view class="navbar-right">
        <view class="help-icon">?</view>
      </view>
    </view>
    
    <!-- 订单管理视图选择 -->
    <view class="view-selector">
      <view 
        v-for="(item, index) in viewOptions" 
        :key="index" 
        class="view-option"
        :class="{'active': currentView === item.value}"
        @click="switchView(item.value)">
        <text class="view-icon">{{item.icon}}</text>
        <text class="view-name">{{item.name}}</text>
      </view>
    </view>
    
    <!-- 订单筛选条件 -->
    <view class="filter-section">
      <scroll-view scroll-x="true" class="filter-scroll">
        <view 
          v-for="(status, index) in orderStatuses" 
          :key="index"
          :class="['status-tag', {'active': currentStatus === status.value}]"
          @click="filterByStatus(status.value)">
          {{status.name}}
        </view>
      </scroll-view>
      
      <view class="filter-button" @click="showFilterOptions">
        <text class="filter-icon">⚙️</text>
        <text>筛选</text>
      </view>
    </view>
    
    <!-- 主要内容区域 -->
    <view class="content-main">
      <block v-if="orders.length > 0">
        <!-- 列表视图 -->
        <view v-if="currentView === 'list'" class="order-list">
          <navigator 
            v-for="(order, index) in orders" 
            :key="index"
            :url="`./detail?id=${order.id}`"
            class="order-item">
            <view class="order-header">
              <text class="order-number">订单号: {{order.orderNo}}</text>
              <text class="order-status" :style="{color: getStatusColor(order.status)}">{{getStatusText(order.status)}}</text>
            </view>
            <view class="order-info">
              <view class="customer-info">
                <text class="label">客户:</text>
                <text class="value">{{order.customerName}}</text>
              </view>
              <view class="time-info">
                <text class="label">下单时间:</text>
                <text class="value">{{order.createTime}}</text>
              </view>
              <view class="amount-info">
                <text class="label">订单金额:</text>
                <text class="value price">¥{{order.totalAmount}}</text>
              </view>
            </view>
            <view class="order-actions">
              <view class="action-btn primary" @click.stop="handleOrder(order.id)">处理订单</view>
              <view class="action-btn" @click.stop="contactCustomer(order.id)">联系客户</view>
            </view>
          </navigator>
        </view>
        
        <!-- 详情视图占位 -->
        <view v-else-if="currentView === 'detail'" class="order-detail-view">
          <text>详情视图正在开发中...</text>
          <button class="nav-btn" @click="navigateTo('./detail')">查看订单详情示例</button>
        </view>
        
        <!-- 日历视图占位 -->
        <view v-else-if="currentView === 'calendar'" class="order-calendar-view">
          <text>日历视图正在开发中...</text>
          <button class="nav-btn" @click="navigateTo('./calendar')">查看日历视图示例</button>
        </view>
      </block>
      
      <!-- 空状态 -->
      <view v-else class="empty-state">
        <view class="empty-icon">📭</view>
        <text class="empty-text">暂无订单数据</text>
        <view class="refresh-btn" @click="loadOrders">刷新</view>
      </view>
    </view>
    
    <!-- 底部导航 -->
    <view class="bottom-nav">
      <view class="nav-item" @click="navigateTo('./list')">
        <text class="nav-icon">📋</text>
        <text>订单列表</text>
      </view>
      <view class="nav-item" @click="navigateTo('./calendar')">
        <text class="nav-icon">📅</text>
        <text>预约管理</text>
      </view>
      <view class="nav-item" @click="navigateTo('./after-sale')">
        <text class="nav-icon">🔄</text>
        <text>售后服务</text>
      </view>
      <view class="nav-item" @click="navigateTo('./finance')">
        <text class="nav-icon">💰</text>
        <text>财务对账</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentView: 'list', // list, detail, calendar
      currentStatus: 'all',
      viewOptions: [
        { name: '列表视图', value: 'list', icon: '📋' },
        { name: '详情视图', value: 'detail', icon: '📝' },
        { name: '日历视图', value: 'calendar', icon: '📅' }
      ],
      orderStatuses: [
        { name: '全部', value: 'all' },
        { name: '待处理', value: 'pending' },
        { name: '处理中', value: 'processing' },
        { name: '已完成', value: 'completed' },
        { name: '已取消', value: 'cancelled' },
        { name: '退款中', value: 'refunding' }
      ],
      orders: [
        {
          id: '1001',
          orderNo: 'CZ20230501001',
          status: 'pending',
          customerName: '张三',
          createTime: '2023-05-01 10:30',
          totalAmount: '128.00'
        },
        {
          id: '1002',
          orderNo: 'CZ20230501002',
          status: 'processing',
          customerName: '李四',
          createTime: '2023-05-01 11:45',
          totalAmount: '256.50'
        },
        {
          id: '1003',
          orderNo: 'CZ20230502003',
          status: 'completed',
          customerName: '王五',
          createTime: '2023-05-02 09:15',
          totalAmount: '89.90'
        }
      ]
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    switchView(view) {
      this.currentView = view;
    },
    filterByStatus(status) {
      this.currentStatus = status;
      // 这里应该根据状态筛选订单
      this.loadOrders();
    },
    showFilterOptions() {
      uni.showToast({
        title: '筛选功能开发中',
        icon: 'none'
      });
    },
    loadOrders() {
      // 这里应该从服务器加载订单数据
      console.log('加载订单数据，状态:', this.currentStatus);
    },
    handleOrder(orderId) {
      uni.navigateTo({
        url: `./detail?id=${orderId}&action=process`
      });
    },
    contactCustomer(orderId) {
      uni.showToast({
        title: '联系客户功能开发中',
        icon: 'none'
      });
    },
    getStatusColor(status) {
      const colors = {
        pending: '#FF9800',
        processing: '#2196F3',
        completed: '#4CAF50',
        cancelled: '#9E9E9E',
        refunding: '#F44336'
      };
      return colors[status] || '#333333';
    },
    getStatusText(status) {
      const texts = {
        pending: '待处理',
        processing: '处理中',
        completed: '已完成',
        cancelled: '已取消',
        refunding: '退款中'
      };
      return texts[status] || '未知状态';
    },
    navigateTo(url) {
      uni.navigateTo({ url });
    }
  },
  onLoad() {
    this.loadOrders();
  }
}
</script>

<style>
.order-management-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}

.view-selector {
  display: flex;
  background-color: #fff;
  padding: 12px 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.view-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 0;
  position: relative;
}

.view-option.active {
  color: #1677FF;
}

.view-option.active::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background-color: #1677FF;
  border-radius: 3px;
}

.view-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.view-name {
  font-size: 12px;
}

.filter-section {
  display: flex;
  padding: 12px 16px;
  background-color: #fff;
  margin-top: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.filter-scroll {
  flex: 1;
  white-space: nowrap;
}

.status-tag {
  display: inline-block;
  padding: 6px 12px;
  margin-right: 8px;
  background-color: #f0f0f0;
  border-radius: 16px;
  font-size: 12px;
  color: #666;
}

.status-tag.active {
  background-color: #e6f7ff;
  color: #1677FF;
  border: 1px solid #91caff;
}

.filter-button {
  display: flex;
  align-items: center;
  padding: 0 12px;
  margin-left: 8px;
  border-left: 1px solid #eee;
}

.filter-icon {
  margin-right: 4px;
}

.content-main {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.order-list {
  display: flex;
  flex-direction: column;
}

.order-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.order-number {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.order-status {
  font-size: 14px;
  font-weight: 500;
}

.order-info {
  margin-bottom: 12px;
}

.customer-info, .time-info, .amount-info {
  display: flex;
  margin-bottom: 6px;
}

.label {
  width: 70px;
  color: #666;
  font-size: 13px;
}

.value {
  flex: 1;
  font-size: 13px;
  color: #333;
}

.price {
  font-weight: 600;
  color: #ff6a00;
}

.order-actions {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 13px;
  margin-left: 8px;
  background-color: #f0f0f0;
  color: #333;
}

.action-btn.primary {
  background-color: #1677FF;
  color: #fff;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px 0;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  color: #999;
  margin-bottom: 16px;
}

.refresh-btn {
  padding: 8px 24px;
  background-color: #1677FF;
  color: #fff;
  border-radius: 4px;
  font-size: 14px;
}

.bottom-nav {
  display: flex;
  background-color: #fff;
  padding: 8px 0;
  border-top: 1px solid #eee;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 12px;
  color: #666;
}

.nav-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.order-detail-view, .order-calendar-view {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.nav-btn {
  margin-top: 16px;
  background-color: #1677FF;
  color: #fff;
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 4px;
}
</style> 