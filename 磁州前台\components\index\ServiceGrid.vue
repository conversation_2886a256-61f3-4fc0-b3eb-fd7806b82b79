<template>
  <!-- 服务分类 -->
  <view class="service-category card-section fade-in">
    <view class="category-header">
      <view class="section-title-wrap">
        <view class="section-bar"></view>
        <text class="category-title">服务分类</text>
      </view>
      <view class="category-dots">
        <view class="category-dot" :class="{active: categoryPage === 0}" @click="changeCategoryPage(0)"></view>
        <view class="category-dot" :class="{active: categoryPage === 1}" @click="changeCategoryPage(1)"></view>
      </view>
    </view>
    <swiper class="category-swiper" :current="categoryPage" @change="onCategoryPageChange" circular>
      <swiper-item>
        <view class="category-page">
          <view class="category-item" v-for="(item, index) in serviceCategory.slice(0, 10)" :key="item.name" @click="handleCategoryClick(item, index)">
            <image :src="item.icon" class="category-icon"></image>
            <text class="category-name">{{item.name}}</text>
          </view>
        </view>
      </swiper-item>
      <swiper-item>
        <view class="category-page category-page-second">
          <view class="category-item" v-for="(item, index) in serviceCategory.slice(10)" :key="item.name" @click="handleCategoryClick(item, index + 10)">
            <image :src="item.icon" class="category-icon"></image>
            <text class="category-name">{{item.name}}</text>
          </view>
        </view>
      </swiper-item>
    </swiper>
    
    <!-- 到家服务全屏子分类弹出层 -->
    <view class="service-category-popup" v-if="showSubcategory" @click.stop="closeSubcategory">
      <view class="service-category-container" @click.stop>
        <view class="service-category-header">
          <view class="category-header-left">
            <text class="category-title-main">到家服务分类</text>
            <text class="category-subtitle">选择您需要的服务类型</text>
          </view>
          <view class="category-close" @click.stop="closeSubcategory">
            <view class="close-icon"></view>
          </view>
        </view>
        
        <view class="service-category-content">
          <view 
            class="service-type-item" 
            v-for="(item, index) in homeServiceCategories" 
            :key="index"
            @click="handleServiceItemClick(item)"
          >
            <view class="service-type-icon-wrap">
              <image :src="item.icon" class="service-type-icon"></image>
            </view>
            <text class="service-type-name">{{item.name}}</text>
          </view>
        </view>
        
        <view class="service-category-footer">
          <view class="view-all-btn" @click="navigateTo('/subPackages/service/pages/list?type=home')">
            查看所有到家服务
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';

const categoryPage = ref(0);
const showSubcategory = ref(false);

const serviceCategory = ref([
        { name: '到家服务', icon: '/static/images/tabbar/到家服务.png', url: '/subPackages/service/pages/home-service-list' },
        { name: '寻找服务', icon: '/static/images/tabbar/寻找服务.png', url: '/subPackages/service/pages/list?type=find' },
        { name: '生意转让', icon: '/static/images/tabbar/生意转让.png', url: '/subPackages/service/pages/list?type=business' },
        { name: '招聘信息', icon: '/static/images/tabbar/招聘信息.png', url: '/subPackages/service/pages/list?type=job' },
        { name: '求职信息', icon: '/static/images/tabbar/求职信息.png', url: '/subPackages/service/pages/list?type=resume' },
        { name: '房屋出租', icon: '/static/images/tabbar/出租.png', url: '/subPackages/service/pages/list?type=house_rent' },
        { name: '房屋出售', icon: '/static/images/tabbar/出售.png', url: '/subPackages/service/pages/list?type=house_sell' },
        { name: '二手车辆', icon: '/static/images/tabbar/二手车辆.png', url: '/subPackages/service/pages/list?type=second_car' },
        { name: '宠物信息', icon: '/static/images/tabbar/宠物信息.png', url: '/subPackages/service/pages/list?type=pet' },
        { name: '商家活动', icon: '/static/images/tabbar/商家活动.png', url: '/subPackages/service/pages/list?type=merchant_activity' },
        { name: '婚恋交友', icon: '/static/images/tabbar/婚恋交友.png', url: '/subPackages/service/pages/list?type=dating' },
        { name: '车辆服务', icon: '/static/images/tabbar/车辆服务.png', url: '/subPackages/service/pages/list?type=car' },
        { name: '二手闲置', icon: '/static/images/tabbar/二手闲置.png', url: '/subPackages/service/pages/list?type=second_hand' },
        { name: '磁州拼车', icon: '/static/images/tabbar/磁州拼车.png', url: '/subPackages/service/pages/list?type=carpool' },
        { name: '教育培训', icon: '/static/images/tabbar/教育培训.png', url: '/subPackages/service/pages/list?type=education' },
        { name: '其他服务', icon: '/static/images/tabbar/其他服务.png', url: '/subPackages/service/pages/list?type=other' }
]);

const homeServiceCategories = ref([
        { name: '家政服务', icon: '/static/images/tabbar/123/家政服务.png', url: '/subPackages/service/pages/list?type=home&subType=home_cleaning&name=家政服务' },
        { name: '维修改造', icon: '/static/images/tabbar/123/维修改造.png', url: '/subPackages/service/pages/list?type=home&subType=repair&name=维修改造' },
        { name: '上门安装', icon: '/static/images/tabbar/123/上门安装.png', url: '/subPackages/service/pages/list?type=home&subType=installation&name=上门安装' },
        { name: '开锁换锁', icon: '/static/images/tabbar/123/开锁换锁.png', url: '/subPackages/service/pages/list?type=home&subType=locksmith&name=开锁换锁' },
        { name: '搬家拉货', icon: '/static/images/tabbar/123/搬家拉货.png', url: '/subPackages/service/pages/list?type=home&subType=moving&name=搬家拉货' },
        { name: '上门美容', icon: '/static/images/tabbar/123/上门美容.png', url: '/subPackages/service/pages/list?type=home&subType=beauty&name=上门美容' },
        { name: '上门家教', icon: '/static/images/tabbar/123/上门家教.png', url: '/subPackages/service/pages/list?type=home&subType=tutor&name=上门家教' },
        { name: '宠物服务', icon: '/static/images/tabbar/123/宠物服务.png', url: '/subPackages/service/pages/list?type=home&subType=pet_service&name=宠物服务' },
        { name: '上门疏通', icon: '/static/images/tabbar/123/上门疏通.png', url: '/subPackages/service/pages/list?type=home&subType=plumbing&name=上门疏通' },
        { name: '其他类型', icon: '/static/images/tabbar/123/其他类型.png', url: '/subPackages/service/pages/list?type=home&subType=other&name=其他类型' }
]);

function changeCategoryPage(page) {
  categoryPage.value = page;
}

function onCategoryPageChange(e) {
  categoryPage.value = e.detail.current;
}

function handleCategoryClick(item, index) {
      if (index === 0 && item.name === '到家服务') {
    showSubcategory.value = true;
        uni.vibrateShort();
      } else {
        const typeMatch = item.url.match(/type=([^&]+)/);
        const serviceType = typeMatch ? typeMatch[1] : '';
        
        if (serviceType) {
          uni.navigateTo({
            url: `/subPackages/service/pages/list?type=${serviceType}&title=${encodeURIComponent(item.name)}`,
            success: () => {
              console.log('成功跳转到服务列表页面:', item.name);
              uni.vibrateShort();
            },
            fail: (err) => {
              console.error('跳转到服务列表页面失败:', err);
          navigateTo(item.url);
            }
          });
        } else {
      navigateTo(item.url);
        }
      }
}

function closeSubcategory() {
  showSubcategory.value = false;
}

function navigateTo(url) {
      if (!url) return;
      
      try {
        uni.navigateTo({
          url: url,
          fail: (err) => {
            console.error('页面跳转失败:', err);
            uni.switchTab({
              url: url,
              fail: (err2) => {
                console.error('switchTab跳转也失败:', err2);
                uni.showToast({
                  title: '页面跳转失败',
                  icon: 'none'
                });
              }
            });
          }
        });
      } catch (error) {
        console.error('导航出错:', error);
      }
}

function handleServiceItemClick(item) {
      console.log('点击了到家服务子分类:', item.name);
  closeSubcategory();
      uni.vibrateShort();
      
      const subTypeMatch = item.url.match(/subType=([^&]+)/);
      const nameMatch = item.url.match(/name=([^&]+)/);
      
      const subType = subTypeMatch ? subTypeMatch[1] : '';
      const name = nameMatch ? decodeURIComponent(nameMatch[1]) : item.name;
      
      try {
        uni.navigateTo({
          url: `/subPackages/service/pages/list?type=home&subType=${subType}&name=${encodeURIComponent(name)}`,
          success: () => {
            console.log('成功跳转到到家服务子分类:', name);
          },
          fail: (err) => {
            console.error('跳转到到家服务子分类失败:', err);
            uni.navigateTo({
              url: '/subPackages/service/pages/list',
              fail: (err2) => {
                console.error('备用跳转也失败:', err2);
                uni.showToast({
                  title: '页面跳转失败',
                  icon: 'none'
                });
              }
            });
          }
        });
      } catch (error) {
        console.error('到家服务子分类跳转出错:', error);
        uni.navigateTo({
          url: '/subPackages/service/pages/list'
        });
  }
}
</script>

<style lang="scss" scoped>
/* 服务分类 */
.service-category {
  background-color: #FFFFFF;
  padding: 26rpx 20rpx;
  margin: 0 25rpx 30rpx;
  border-radius: 35rpx;
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10rpx 20rpx 10rpx;
  border-bottom: 1px solid rgba(235, 238, 245, 0.5);
  margin-bottom: 20rpx;
}

.section-title-wrap {
  display: flex;
  align-items: center;
}

.section-bar {
  width: 6rpx;
  height: 28rpx;
  background: linear-gradient(to bottom, #007AFF, #5AC8FA);
  border-radius: 3rpx;
  margin-right: 6rpx;
}

.category-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);
}

.category-dots {
  display: flex;
  align-items: center;
}

.category-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  margin: 0 6rpx;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

.category-dot.active {
  width: 24rpx;
  height: 16rpx;
  border-radius: 8rpx;
  background: linear-gradient(90deg, #1677ff, #3a8eff);
  box-shadow: 0 1px 3px rgba(22, 119, 255, 0.2);
}

.category-swiper {
  width: 100%;
  height: 360rpx;
}

.category-page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  padding: 5rpx 0;
}

/* 第二页服务分类样式 */
.category-page-second {
  align-items: flex-start;
  justify-content: flex-start;
  align-content: flex-start;
}

.category-item {
  width: 20%;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 18rpx 0;
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 12rpx;
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
}

.category-item:active .category-icon {
  transform: none;
}

.category-name {
  font-size: 24rpx;
  color: #333;
  font-weight: 400;
  text-align: center;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 4rpx;
}

/* 到家服务全屏子分类弹出层样式 */
.service-category-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  z-index: 1001;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.25s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.service-category-container {
  width: 85%;
  height: auto;
  max-height: 72%;
  max-width: 650rpx;
  background-color: #fff;
  border-radius: 26rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from { transform: translateY(50rpx); opacity: 0.8; }
  to { transform: translateY(0); opacity: 1; }
}

.service-category-header {
  padding: 26rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f5ff;
  background: linear-gradient(to right, #f9fbff, #f0f5ff);
}

.category-header-left {
  display: flex;
  flex-direction: column;
}

.category-title-main {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 6rpx;
}

.category-subtitle {
  font-size: 22rpx;
  color: #777;
}

.category-close {
  width: 50rpx;
  height: 50rpx;
  border-radius: 25rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.04);
  transition: all 0.3s;
}

.category-close:active {
  background-color: rgba(0, 0, 0, 0.1);
  transform: scale(0.9);
}

.close-icon {
  width: 20rpx;
  height: 20rpx;
  position: relative;
}

.close-icon:before, .close-icon:after {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 2px;
  background-color: #666;
  top: 9rpx;
  left: 0;
}

.close-icon:before {
  transform: rotate(45deg);
}

.close-icon:after {
  transform: rotate(-45deg);
}

.service-category-content {
  padding: 25rpx 15rpx;
  display: flex;
  flex-wrap: wrap;
  overflow-y: auto;
  max-height: calc(80vh - 200rpx);
}

.service-type-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx 0;
  margin-bottom: 12rpx;
  transition: all 0.2s;
}

.service-type-item:active {
  transform: scale(0.92);
}

.service-type-icon-wrap {
  width: 90rpx;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15rpx;
}

.service-type-icon {
  width: 90rpx;
  height: 90rpx;
  position: relative;
}

.service-type-name {
  font-size: 22rpx;
  color: #333;
  text-align: center;
  padding: 0 6rpx;
  width: 100%;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.service-category-footer {
  padding: 24rpx 0;
  display: flex;
  justify-content: center;
  border-top: 1px solid #f0f5ff;
  background-color: #f9fbff;
}

.view-all-btn {
  background: linear-gradient(135deg, #1677FF, #0052CC);
  color: #fff;
  font-size: 26rpx;
  padding: 14rpx 50rpx;
  border-radius: 30rpx;
  text-align: center;
  box-shadow: 0 5rpx 12rpx rgba(22, 119, 255, 0.18);
  position: relative;
  overflow: hidden;
}

.view-all-btn:active {
  transform: scale(0.95);
  box-shadow: 0 3rpx 10rpx rgba(22, 119, 255, 0.15);
}

.view-all-btn:after {
  content: '';
  position: absolute;
  top: 0;
  left: -50%;
  width: 150%;
  height: 100%;
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0) 0%, 
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-25deg);
  animation: shine 3s infinite;
}

@keyframes shine {
  0% { left: -150%; }
  50%, 100% { left: 150%; }
}

.card-section {
  background: #ffffff;
  border-radius: 35rpx;
  margin-bottom: 40rpx;
  overflow: hidden;
  border: none;
  box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.08);
  padding: 26rpx 22rpx;
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}
</style> 