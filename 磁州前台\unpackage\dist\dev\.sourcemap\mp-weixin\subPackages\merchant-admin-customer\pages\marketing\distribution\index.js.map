{"version": 3, "file": "index.js", "sources": ["subPackages/merchant-admin-customer/pages/marketing/distribution/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tY3VzdG9tZXJccGFnZXNcbWFya2V0aW5nXGRpc3RyaWJ1dGlvblxpbmRleC52dWU"], "sourcesContent": ["<template>\n  <view class=\"distribution-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">分销系统</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 数据概览卡片 -->\n    <view class=\"overview-section\">\n      <view class=\"overview-header\">\n        <text class=\"section-title\">分销概览</text>\n        <view class=\"date-picker\" @click=\"showDatePicker\">\n          <text class=\"date-text\">{{dateRange}}</text>\n          <view class=\"date-icon\"></view>\n        </view>\n      </view>\n      \n      <view class=\"stats-cards\">\n        <view class=\"stats-card\">\n          <view class=\"card-value\">{{distributionData.totalDistributors}}</view>\n          <view class=\"card-label\">分销员总数</view>\n          <view class=\"card-trend\" :class=\"distributionData.distributorsTrend\">\n            <view class=\"trend-arrow\"></view>\n            <text class=\"trend-value\">{{distributionData.distributorsGrowth}}</text>\n          </view>\n        </view>\n        \n        <view class=\"stats-card\">\n          <view class=\"card-value\">¥{{formatNumber(distributionData.totalCommission)}}</view>\n          <view class=\"card-label\">累计佣金</view>\n          <view class=\"card-trend\" :class=\"distributionData.commissionTrend\">\n            <view class=\"trend-arrow\"></view>\n            <text class=\"trend-value\">{{distributionData.commissionGrowth}}</text>\n          </view>\n        </view>\n        \n        <view class=\"stats-card\">\n          <view class=\"card-value\">{{distributionData.totalOrders}}</view>\n          <view class=\"card-label\">分销订单数</view>\n          <view class=\"card-trend\" :class=\"distributionData.ordersTrend\">\n            <view class=\"trend-arrow\"></view>\n            <text class=\"trend-value\">{{distributionData.ordersGrowth}}</text>\n          </view>\n        </view>\n        \n        <view class=\"stats-card\">\n          <view class=\"card-value\">¥{{formatNumber(distributionData.averageCommission)}}</view>\n          <view class=\"card-label\">平均佣金</view>\n          <view class=\"card-trend\" :class=\"distributionData.averageTrend\">\n            <view class=\"trend-arrow\"></view>\n            <text class=\"trend-value\">{{distributionData.averageGrowth}}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 分销员榜单 -->\n    <view class=\"leaderboard-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">分销员排行榜</text>\n        <text class=\"view-all\" @click=\"viewAllDistributors\">查看全部</text>\n      </view>\n      \n      <view class=\"leaderboard-list\">\n        <view class=\"leaderboard-item\" v-for=\"(item, index) in topDistributors\" :key=\"index\" @click=\"viewDistributorDetail(item)\">\n          <view class=\"rank-badge\" :class=\"{'top-rank': index < 3}\">{{index + 1}}</view>\n          <image class=\"distributor-avatar\" :src=\"item.avatar\" mode=\"aspectFill\"></image>\n          <view class=\"distributor-info\">\n            <view class=\"distributor-name\">{{item.name}}</view>\n            <view class=\"distributor-level\">{{item.level}}</view>\n          </view>\n          <view class=\"distributor-stats\">\n            <view class=\"stat-item\">\n              <text class=\"stat-value\">¥{{formatNumber(item.commission)}}</text>\n              <text class=\"stat-label\">佣金</text>\n            </view>\n            <view class=\"stat-item\">\n              <text class=\"stat-value\">{{item.orders}}</text>\n              <text class=\"stat-label\">订单</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 分销设置卡片 -->\n    <view class=\"settings-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">分销设置</text>\n        <view class=\"switch-container\">\n          <text class=\"switch-label\">分销功能</text>\n          <switch :checked=\"distributionEnabled\" @change=\"toggleDistribution\" color=\"#6B0FBE\" />\n        </view>\n      </view>\n      \n      <view class=\"settings-list\">\n        <view class=\"settings-item\" @click=\"navigateToSetting('conditions')\">\n          <view class=\"item-left\">\n            <view class=\"item-icon conditions\"></view>\n            <text class=\"item-title\">成为分销员条件</text>\n          </view>\n          <view class=\"item-right\">\n            <text class=\"item-value\">{{distributionSettings.conditionText}}</text>\n            <view class=\"item-arrow\"></view>\n          </view>\n        </view>\n        \n        <view class=\"settings-item\" @click=\"navigateToSetting('levels')\">\n          <view class=\"item-left\">\n            <view class=\"item-icon levels\"></view>\n            <text class=\"item-title\">分销等级设置</text>\n          </view>\n          <view class=\"item-right\">\n            <text class=\"item-value\">{{distributionSettings.levelCount}}个等级</text>\n            <view class=\"item-arrow\"></view>\n          </view>\n        </view>\n        \n        <view class=\"settings-item\" @click=\"navigateToSetting('withdrawal')\">\n          <view class=\"item-left\">\n            <view class=\"item-icon withdrawal\"></view>\n            <text class=\"item-title\">提现设置</text>\n          </view>\n          <view class=\"item-right\">\n            <text class=\"item-value\">最低¥{{distributionSettings.minWithdrawal}}元</text>\n            <view class=\"item-arrow\"></view>\n          </view>\n        </view>\n        \n        <view class=\"settings-item\" @click=\"navigateToSetting('agreement')\">\n          <view class=\"item-left\">\n            <view class=\"item-icon agreement\"></view>\n            <text class=\"item-title\">分销协议</text>\n          </view>\n          <view class=\"item-right\">\n            <view class=\"item-arrow\"></view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 佣金规则卡片 -->\n    <view class=\"commission-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">佣金规则</text>\n        <view class=\"edit-btn\" @click=\"editCommissionRules\">编辑</view>\n      </view>\n      \n      <view class=\"commission-rules\">\n        <view class=\"rule-card\">\n          <view class=\"rule-header\">\n            <text class=\"rule-title\">一级分销</text>\n            <text class=\"rule-value\">{{commissionRules.level1}}%</text>\n          </view>\n          <view class=\"rule-desc\">直接推广获得订单金额的佣金比例</view>\n        </view>\n        \n        <view class=\"rule-card\">\n          <view class=\"rule-header\">\n            <text class=\"rule-title\">二级分销</text>\n            <text class=\"rule-value\">{{commissionRules.level2}}%</text>\n          </view>\n          <view class=\"rule-desc\">间接推广获得订单金额的佣金比例</view>\n        </view>\n        \n        <view class=\"rule-card\" v-if=\"commissionRules.level3 > 0\">\n          <view class=\"rule-header\">\n            <text class=\"rule-title\">三级分销</text>\n            <text class=\"rule-value\">{{commissionRules.level3}}%</text>\n          </view>\n          <view class=\"rule-desc\">三级推广获得订单金额的佣金比例</view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 推广工具卡片 -->\n    <view class=\"promotion-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">推广工具</text>\n      </view>\n      \n      <view class=\"promotion-tools\">\n        <view class=\"tool-card\" v-for=\"(tool, index) in promotionTools\" :key=\"index\" @click=\"usePromotionTool(tool)\">\n          <image class=\"tool-icon\" :src=\"tool.icon\" mode=\"aspectFit\"></image>\n          <text class=\"tool-name\">{{tool.name}}</text>\n          <text class=\"tool-desc\">{{tool.description}}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 浮动操作按钮 -->\n    <view class=\"floating-action-button\" @click=\"showActionMenu\">\n      <view class=\"fab-icon\">+</view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      dateRange: '2023-04-01 ~ 2023-04-30',\n      distributionEnabled: true,\n      \n      // 分销数据概览\n      distributionData: {\n        totalDistributors: 268,\n        distributorsTrend: 'up',\n        distributorsGrowth: '12%',\n        \n        totalCommission: 26584.50,\n        commissionTrend: 'up',\n        commissionGrowth: '8.5%',\n        \n        totalOrders: 1256,\n        ordersTrend: 'up',\n        ordersGrowth: '15.2%',\n        \n        averageCommission: 99.20,\n        averageTrend: 'down',\n        averageGrowth: '3.1%'\n      },\n      \n      // 分销员排行榜\n      topDistributors: [\n        {\n          id: 1,\n          name: '张小明',\n          avatar: '/static/images/avatar-1.png',\n          level: '钻石分销员',\n          commission: 5862.50,\n          orders: 86\n        },\n        {\n          id: 2,\n          name: '李佳怡',\n          avatar: '/static/images/avatar-2.png',\n          level: '金牌分销员',\n          commission: 4235.80,\n          orders: 64\n        },\n        {\n          id: 3,\n          name: '王大力',\n          avatar: '/static/images/avatar-3.png',\n          level: '金牌分销员',\n          commission: 3756.20,\n          orders: 58\n        },\n        {\n          id: 4,\n          name: '赵丽丽',\n          avatar: '/static/images/avatar-4.png',\n          level: '银牌分销员',\n          commission: 2845.60,\n          orders: 42\n        },\n        {\n          id: 5,\n          name: '陈小红',\n          avatar: '/static/images/avatar-5.png',\n          level: '银牌分销员',\n          commission: 2356.80,\n          orders: 35\n        }\n      ],\n      \n      // 分销设置\n      distributionSettings: {\n        conditionText: '购买商品并申请',\n        levelCount: 3,\n        minWithdrawal: 50\n      },\n      \n      // 佣金规则\n      commissionRules: {\n        level1: 15,\n        level2: 5,\n        level3: 2\n      },\n      \n      // 推广工具\n      promotionTools: [\n        {\n          id: 1,\n          name: '推广海报',\n          description: '生成专属推广海报',\n          icon: '/static/images/poster-icon.png'\n        },\n        {\n          id: 2,\n          name: '推广二维码',\n          description: '生成专属推广二维码',\n          icon: '/static/images/qrcode-icon.png'\n        },\n        {\n          id: 3,\n          name: '商品推广卡片',\n          description: '生成商品推广卡片',\n          icon: '/static/images/product-card-icon.png'\n        },\n        {\n          id: 4,\n          name: '分销员邀请',\n          description: '邀请好友成为分销员',\n          icon: '/static/images/invite-icon.png'\n        }\n      ]\n    }\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack();\n    },\n    \n    showHelp() {\n      uni.showModal({\n        title: '分销系统帮助',\n        content: '分销系统是一种营销模式，通过发展分销员推广商品，获取佣金的方式促进销售增长。',\n        showCancel: false\n      });\n    },\n    \n    formatNumber(number) {\n      return number.toFixed(2).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\n    },\n    \n    showDatePicker() {\n      // 实现日期选择器\n      uni.showToast({\n        title: '日期选择功能开发中',\n        icon: 'none'\n      });\n    },\n    \n    viewAllDistributors() {\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-customer/pages/marketing/distribution/distributors'\n      });\n    },\n    \n    viewDistributorDetail(distributor) {\n      uni.navigateTo({\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail?id=${distributor.id}`\n      });\n    },\n    \n    toggleDistribution(e) {\n      this.distributionEnabled = e.detail.value;\n      uni.showToast({\n        title: this.distributionEnabled ? '分销功能已开启' : '分销功能已关闭',\n        icon: 'none'\n      });\n    },\n    \n    navigateToSetting(type) {\n      const routes = {\n        conditions: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/conditions',\n        levels: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/levels',\n        withdrawal: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/withdrawal',\n        agreement: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/agreement'\n      };\n      \n      uni.navigateTo({\n        url: routes[type]\n      });\n    },\n    \n    editCommissionRules() {\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/commission-rules'\n      });\n    },\n    \n    usePromotionTool(tool) {\n      // 根据工具ID跳转到不同页面\n      let url = '';\n      \n      switch(tool.id) {\n        case 1: // 推广海报\n          url = '/subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-tool?id=1&name=推广海报';\n          break;\n        case 2: // 推广二维码\n          url = '/subPackages/merchant-admin-marketing/pages/marketing/distribution/qrcode';\n          break;\n        case 3: // 商品推广卡片\n          url = '/subPackages/merchant-admin-marketing/pages/marketing/distribution/product-cards';\n          break;\n        case 4: // 分销员渠道\n          url = '/subPackages/merchant-admin-marketing/pages/marketing/distribution/channels';\n          break;\n        default:\n          url = `/subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-tool?id=${tool.id}&name=${tool.name}`;\n      }\n      \n      uni.navigateTo({\n        url: url\n      });\n    },\n    \n    showActionMenu() {\n      uni.showActionSheet({\n        itemList: ['新增分销员', '导出分销数据', '佣金发放', '分销活动创建'],\n        success: (res) => {\n          const actions = [\n            () => this.addDistributor(),\n            () => this.exportData(),\n            () => this.payCommission(),\n            () => this.createActivity()\n          ];\n          \n          actions[res.tapIndex]();\n        }\n      });\n    },\n    \n    addDistributor() {\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/add-distributor'\n      });\n    },\n    \n    exportData() {\n      uni.showLoading({\n        title: '导出中...'\n      });\n      \n      setTimeout(() => {\n        uni.hideLoading();\n        uni.showToast({\n          title: '数据导出成功',\n          icon: 'success'\n        });\n      }, 1500);\n    },\n    \n    payCommission() {\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/pay-commission'\n      });\n    },\n    \n    createActivity() {\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/create-activity'\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.distribution-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: env(safe-area-inset-bottom);\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\n  color: #fff;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: bold;\n}\n\n/* 通用部分样式 */\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.view-all {\n  font-size: 14px;\n  color: #6B0FBE;\n}\n\n.edit-btn {\n  background: rgba(107, 15, 190, 0.1);\n  color: #6B0FBE;\n  font-size: 14px;\n  padding: 5px 12px;\n  border-radius: 15px;\n}\n\n/* 概览部分样式 */\n.overview-section {\n  margin: 15px;\n  background: #fff;\n  border-radius: 12px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n  padding: 15px;\n}\n\n.overview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.date-picker {\n  display: flex;\n  align-items: center;\n  background: #F5F7FA;\n  border-radius: 15px;\n  padding: 5px 10px;\n}\n\n.date-text {\n  font-size: 12px;\n  color: #666;\n  margin-right: 5px;\n}\n\n.date-icon {\n  width: 8px;\n  height: 8px;\n  border-top: 2px solid #666;\n  border-right: 2px solid #666;\n  transform: rotate(135deg);\n}\n\n/* 统计卡片样式 */\n.stats-cards {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -7.5px;\n}\n\n.stats-card {\n  width: 50%;\n  padding: 7.5px;\n  box-sizing: border-box;\n  position: relative;\n}\n\n.card-value {\n  font-size: 20px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n  background: #F8FAFC;\n  padding: 15px;\n  border-radius: 10px;\n  border-left: 3px solid #6B0FBE;\n}\n\n.card-label {\n  font-size: 12px;\n  color: #999;\n  position: absolute;\n  bottom: 20px;\n  left: 25px;\n}\n\n.card-trend {\n  position: absolute;\n  bottom: 20px;\n  right: 25px;\n  display: flex;\n  align-items: center;\n  font-size: 12px;\n}\n\n.card-trend.up {\n  color: #34C759;\n}\n\n.card-trend.down {\n  color: #FF3B30;\n}\n\n.trend-arrow {\n  width: 0;\n  height: 0;\n  margin-right: 3px;\n}\n\n.card-trend.up .trend-arrow {\n  border-left: 4px solid transparent;\n  border-right: 4px solid transparent;\n  border-bottom: 6px solid #34C759;\n}\n\n.card-trend.down .trend-arrow {\n  border-left: 4px solid transparent;\n  border-right: 4px solid transparent;\n  border-top: 6px solid #FF3B30;\n}\n\n/* 排行榜样式 */\n.leaderboard-section {\n  margin: 15px;\n  background: #fff;\n  border-radius: 12px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n  padding: 15px;\n}\n\n.leaderboard-list {\n  margin-top: 10px;\n}\n\n.leaderboard-item {\n  display: flex;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.leaderboard-item:last-child {\n  border-bottom: none;\n}\n\n.rank-badge {\n  width: 24px;\n  height: 24px;\n  background: #F5F7FA;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  font-weight: bold;\n  color: #999;\n  margin-right: 10px;\n}\n\n.rank-badge.top-rank {\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\n  color: #fff;\n}\n\n.distributor-avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 20px;\n  margin-right: 10px;\n  background-color: #f5f5f5;\n}\n\n.distributor-info {\n  flex: 1;\n}\n\n.distributor-name {\n  font-size: 15px;\n  color: #333;\n  margin-bottom: 3px;\n}\n\n.distributor-level {\n  font-size: 12px;\n  color: #6B0FBE;\n  background: rgba(107, 15, 190, 0.1);\n  display: inline-block;\n  padding: 2px 8px;\n  border-radius: 10px;\n}\n\n.distributor-stats {\n  display: flex;\n  margin-left: 10px;\n}\n\n.stat-item {\n  margin-left: 15px;\n  text-align: right;\n}\n\n.stat-value {\n  display: block;\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 2px;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #999;\n}\n\n/* 设置部分样式 */\n.settings-section {\n  margin: 15px;\n  background: #fff;\n  border-radius: 12px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n  padding: 15px;\n}\n\n.switch-container {\n  display: flex;\n  align-items: center;\n}\n\n.switch-label {\n  font-size: 14px;\n  color: #666;\n  margin-right: 10px;\n}\n\n.settings-list {\n  margin-top: 10px;\n}\n\n.settings-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.settings-item:last-child {\n  border-bottom: none;\n}\n\n.item-left {\n  display: flex;\n  align-items: center;\n}\n\n.item-icon {\n  width: 36px;\n  height: 36px;\n  border-radius: 18px;\n  margin-right: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n}\n\n.item-icon.conditions {\n  background: rgba(255, 149, 0, 0.1);\n}\n\n.item-icon.conditions::before {\n  content: '';\n  width: 16px;\n  height: 16px;\n  border: 2px solid #FF9500;\n  border-radius: 10px;\n  position: absolute;\n}\n\n.item-icon.levels {\n  background: rgba(0, 122, 255, 0.1);\n}\n\n.item-icon.levels::before {\n  content: '';\n  width: 18px;\n  height: 4px;\n  background: #007AFF;\n  position: absolute;\n  top: 12px;\n}\n\n.item-icon.levels::after {\n  content: '';\n  width: 18px;\n  height: 4px;\n  background: #007AFF;\n  position: absolute;\n  top: 20px;\n}\n\n.item-icon.withdrawal {\n  background: rgba(52, 199, 89, 0.1);\n}\n\n.item-icon.withdrawal::before {\n  content: '';\n  width: 16px;\n  height: 10px;\n  border: 2px solid #34C759;\n  border-radius: 2px;\n  position: absolute;\n}\n\n.item-icon.agreement {\n  background: rgba(88, 86, 214, 0.1);\n}\n\n.item-icon.agreement::before {\n  content: '';\n  width: 14px;\n  height: 18px;\n  border: 2px solid #5856D6;\n  border-radius: 2px;\n  position: absolute;\n}\n\n.item-icon.agreement::after {\n  content: '';\n  width: 10px;\n  height: 2px;\n  background: #5856D6;\n  position: absolute;\n  top: 14px;\n}\n\n.item-title {\n  font-size: 15px;\n  color: #333;\n}\n\n.item-right {\n  display: flex;\n  align-items: center;\n}\n\n.item-value {\n  font-size: 14px;\n  color: #999;\n  margin-right: 10px;\n}\n\n.item-arrow {\n  width: 8px;\n  height: 8px;\n  border-top: 1.5px solid #999;\n  border-right: 1.5px solid #999;\n  transform: rotate(45deg);\n}\n\n/* 佣金规则样式 */\n.commission-section {\n  margin: 15px;\n  background: #fff;\n  border-radius: 12px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n  padding: 15px;\n}\n\n.commission-rules {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -7.5px;\n}\n\n.rule-card {\n  width: calc(100% / 3);\n  padding: 7.5px;\n  box-sizing: border-box;\n}\n\n.rule-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 5px;\n}\n\n.rule-title {\n  font-size: 14px;\n  font-weight: 500;\n  color: #333;\n}\n\n.rule-value {\n  font-size: 16px;\n  font-weight: 600;\n  color: #6B0FBE;\n}\n\n.rule-desc {\n  font-size: 12px;\n  color: #999;\n  height: 32px;\n}\n\n/* 推广工具样式 */\n.promotion-section {\n  margin: 15px;\n  background: #fff;\n  border-radius: 12px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n  padding: 15px;\n}\n\n.promotion-tools {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -7.5px;\n}\n\n.tool-card {\n  width: 25%;\n  padding: 7.5px;\n  box-sizing: border-box;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.tool-icon {\n  width: 50px;\n  height: 50px;\n  margin-bottom: 8px;\n}\n\n.tool-name {\n  font-size: 14px;\n  color: #333;\n  margin-bottom: 3px;\n  text-align: center;\n}\n\n.tool-desc {\n  font-size: 10px;\n  color: #999;\n  text-align: center;\n  height: 28px;\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n}\n\n/* 浮动操作按钮 */\n.floating-action-button {\n  position: fixed;\n  bottom: 30px;\n  right: 30px;\n  width: 56px;\n  height: 56px;\n  border-radius: 28px;\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\n  box-shadow: 0 4px 15px rgba(107, 15, 190, 0.3);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 100;\n}\n\n.fab-icon {\n  font-size: 28px;\n  color: #fff;\n  font-weight: 300;\n  line-height: 1;\n  margin-top: -2px;\n}\n\n/* 响应式调整 */\n@media screen and (max-width: 375px) {\n  .rule-card {\n    width: 50%;\n  }\n  \n  .tool-card {\n    width: 33.33%;\n  }\n}\n\n@media screen and (max-width: 320px) {\n  .stats-card {\n    width: 100%;\n  }\n  \n  .tool-card {\n    width: 50%;\n  }\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-customer/pages/marketing/distribution/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA4MA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,qBAAqB;AAAA;AAAA,MAGrB,kBAAkB;AAAA,QAChB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,oBAAoB;AAAA,QAEpB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,QAElB,aAAa;AAAA,QACb,aAAa;AAAA,QACb,cAAc;AAAA,QAEd,mBAAmB;AAAA,QACnB,cAAc;AAAA,QACd,eAAe;AAAA,MAChB;AAAA;AAAA,MAGD,iBAAiB;AAAA,QACf;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AAAA,MACD;AAAA;AAAA,MAGD,sBAAsB;AAAA,QACpB,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,eAAe;AAAA,MAChB;AAAA;AAAA,MAGD,iBAAiB;AAAA,QACf,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,MACT;AAAA;AAAA,MAGD,gBAAgB;AAAA,QACd;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IAED,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MACd,CAAC;AAAA,IACF;AAAA,IAED,aAAa,QAAQ;AACnB,aAAO,OAAO,QAAQ,CAAC,EAAE,QAAQ,yBAAyB,GAAG;AAAA,IAC9D;AAAA,IAED,iBAAiB;AAEfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IAED,sBAAsB;AACpBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IAED,sBAAsB,aAAa;AACjCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4FAA4F,YAAY,EAAE;AAAA,MACjH,CAAC;AAAA,IACF;AAAA,IAED,mBAAmB,GAAG;AACpB,WAAK,sBAAsB,EAAE,OAAO;AACpCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,KAAK,sBAAsB,YAAY;AAAA,QAC9C,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IAED,kBAAkB,MAAM;AACtB,YAAM,SAAS;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,WAAW;AAAA;AAGbA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,OAAO,IAAI;AAAA,MAClB,CAAC;AAAA,IACF;AAAA,IAED,sBAAsB;AACpBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IAED,iBAAiB,MAAM;AAErB,UAAI,MAAM;AAEV,cAAO,KAAK,IAAE;AAAA,QACZ,KAAK;AACH,gBAAM;AACN;AAAA,QACF,KAAK;AACH,gBAAM;AACN;AAAA,QACF,KAAK;AACH,gBAAM;AACN;AAAA,QACF,KAAK;AACH,gBAAM;AACN;AAAA,QACF;AACE,gBAAM,wFAAwF,KAAK,EAAE,SAAS,KAAK,IAAI;AAAA,MAC3H;AAEAA,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,iBAAiB;AACfA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,SAAS,UAAU,QAAQ,QAAQ;AAAA,QAC9C,SAAS,CAAC,QAAQ;AAChB,gBAAM,UAAU;AAAA,YACd,MAAM,KAAK,eAAgB;AAAA,YAC3B,MAAM,KAAK,WAAY;AAAA,YACvB,MAAM,KAAK,cAAe;AAAA,YAC1B,MAAM,KAAK,eAAe;AAAA;AAG5B,kBAAQ,IAAI,QAAQ;QACtB;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,iBAAiB;AACfA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IAED,aAAa;AACXA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACF,GAAE,IAAI;AAAA,IACR;AAAA,IAED,gBAAgB;AACdA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IAED,iBAAiB;AACfA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrcA,GAAG,WAAW,eAAe;"}