<template>
  <view class="carpool-entry-container">
    <view class="loading-container">
      <image src="/static/images/tabbar/拼车选中.png" class="loading-icon"></image>
      <text class="loading-text">正在进入拼车服务...</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      redirected: false
    }
  },
  onLoad() {
    // 显示加载状态
    uni.showLoading({
      title: '加载中...',
      mask: true
    });
    
    // 延迟跳转，给足够时间加载分包
    setTimeout(() => {
      this.navigateToCarpoolMain();
    }, 500);
  },
  onShow() {
    // 如果已经显示过页面，每次再次显示都自动跳转
    if (this.redirected) {
      this.navigateToCarpoolMain();
    }
  },
  methods: {
    navigateToCarpoolMain() {
      // 隐藏系统TabBar，因为拼车页面有自己的TabBar
      uni.hideTabBar();
      
      // 标记已经跳转过
      this.redirected = true;
      
      // 隐藏加载提示
      uni.hideLoading();
      
      // 跳转到拼车主页
      uni.navigateTo({
        url: '/carpool-package/pages/carpool-main/index',
        fail: (err) => {
          console.error('跳转失败', err);
          uni.showTabBar(); // 如果跳转失败，显示回TabBar
          uni.showToast({
            title: '拼车服务加载失败，请重试',
            icon: 'none'
          });
        }
      });
    }
  }
}
</script>

<style lang="scss">
.carpool-entry-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f7fa;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  animation: pulse 1.5s infinite;
}

.loading-text {
  font-size: 30rpx;
  color: #666;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style> 