"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const utils_distributionService = require("../../../utils/distributionService.js");
const _sfc_main = {
  __name: "products",
  setup(__props) {
    const searchKeyword = common_vendor.ref("");
    const filters = [
      { name: "综合排序", value: "default" },
      { name: "佣金优先", value: "commission", sortable: true },
      { name: "销量优先", value: "sales", sortable: true },
      { name: "价格优先", value: "price", sortable: true }
    ];
    const activeFilter = common_vendor.ref("default");
    const products = common_vendor.ref([]);
    const pagination = common_vendor.reactive({
      page: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    });
    const hasMoreData = common_vendor.ref(false);
    const loading = common_vendor.ref(false);
    const showPromotionModal = common_vendor.ref(false);
    const selectedProduct = common_vendor.ref(null);
    common_vendor.onMounted(async () => {
      await getProducts();
    });
    const getProducts = async (loadMore2 = false) => {
      if (loading.value)
        return;
      try {
        loading.value = true;
        const page = loadMore2 ? pagination.page + 1 : 1;
        const result = await utils_distributionService.distributionService.getDistributableProducts({
          page,
          pageSize: pagination.pageSize,
          sortBy: activeFilter.value === "default" ? "commission" : activeFilter.value,
          keyword: searchKeyword.value
        });
        if (result) {
          if (loadMore2) {
            products.value = [...products.value, ...result.list];
          } else {
            products.value = result.list;
          }
          pagination.page = page;
          pagination.total = result.pagination.total;
          pagination.totalPages = result.pagination.totalPages;
          hasMoreData.value = pagination.page < pagination.totalPages;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/distribution/pages/products.vue:196", "获取商品列表失败", error);
        common_vendor.index.showToast({
          title: "获取商品列表失败",
          icon: "none"
        });
      } finally {
        loading.value = false;
      }
    };
    const searchProducts = () => {
      getProducts();
    };
    const clearSearch = () => {
      searchKeyword.value = "";
      getProducts();
    };
    const switchFilter = (filter) => {
      if (activeFilter.value === filter)
        return;
      activeFilter.value = filter;
      getProducts();
    };
    const loadMore = () => {
      if (hasMoreData.value && !loading.value) {
        getProducts(true);
      }
    };
    const promoteProduct = (product) => {
      selectedProduct.value = product;
      showPromotionModal.value = true;
    };
    const closePromotionModal = () => {
      showPromotionModal.value = false;
    };
    const copyPromotionLink = async () => {
      if (!selectedProduct.value)
        return;
      try {
        const result = await utils_distributionService.distributionService.generatePromotionLink({
          type: "product",
          id: selectedProduct.value.id,
          distributorId: "DIS1001"
          // 模拟数据，实际应从用户系统获取
        });
        if (result && result.url) {
          common_vendor.index.setClipboardData({
            data: result.url,
            success: () => {
              common_vendor.index.showToast({
                title: "链接已复制",
                icon: "success"
              });
              closePromotionModal();
            }
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/distribution/pages/products.vue:267", "生成推广链接失败", error);
        common_vendor.index.showToast({
          title: "生成推广链接失败",
          icon: "none"
        });
      }
    };
    const shareToFriends = () => {
      if (!selectedProduct.value)
        return;
      common_vendor.index.showShareMenu({
        withShareTicket: true,
        menus: ["shareAppMessage", "shareTimeline"]
      });
      setTimeout(() => {
        closePromotionModal();
      }, 500);
    };
    const navigateToProduct = (product) => {
      common_vendor.index.navigateTo({
        url: `/pages/product/detail?id=${product.id}&isDistribution=true`
      });
    };
    const navigateTo = (url, product) => {
      if (!product) {
        common_vendor.index.navigateTo({ url });
        return;
      }
      common_vendor.index.navigateTo({
        url: `${url}?type=product&id=${product.id}&title=${encodeURIComponent(product.name)}&image=${encodeURIComponent(product.image)}`
      });
      closePromotionModal();
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const showHelp = () => {
      common_vendor.index.showModal({
        title: "可分销商品帮助",
        content: "可分销商品是平台允许分销员推广并获得佣金的商品。您可以选择商品进行推广，通过分享获得佣金。",
        showCancel: false
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(goBack),
        b: common_vendor.o(showHelp),
        c: common_vendor.o(searchProducts),
        d: searchKeyword.value,
        e: common_vendor.o(($event) => searchKeyword.value = $event.detail.value),
        f: searchKeyword.value
      }, searchKeyword.value ? {
        g: common_vendor.o(clearSearch)
      } : {}, {
        h: common_vendor.o(searchProducts),
        i: common_vendor.f(filters, (filter, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(filter.name),
            b: filter.sortable
          }, filter.sortable ? {} : {}, {
            c: index,
            d: activeFilter.value === filter.value ? 1 : "",
            e: common_vendor.o(($event) => switchFilter(filter.value), index)
          });
        }),
        j: products.value.length > 0
      }, products.value.length > 0 ? {
        k: common_vendor.f(products.value, (product, index, i0) => {
          return {
            a: product.image,
            b: common_vendor.t(product.name),
            c: common_vendor.t(product.price),
            d: common_vendor.t(product.sales),
            e: common_vendor.t(product.commissionRate),
            f: common_vendor.t(product.commission),
            g: common_vendor.o(($event) => promoteProduct(product), index),
            h: index,
            i: common_vendor.o(($event) => navigateToProduct(product), index)
          };
        })
      } : {
        l: common_assets._imports_0$50
      }, {
        m: hasMoreData.value && products.value.length > 0
      }, hasMoreData.value && products.value.length > 0 ? common_vendor.e({
        n: loading.value
      }, loading.value ? {} : {
        o: common_vendor.o(loadMore)
      }) : {}, {
        p: showPromotionModal.value
      }, showPromotionModal.value ? {
        q: common_vendor.o(closePromotionModal),
        r: common_vendor.o(closePromotionModal),
        s: common_vendor.o(($event) => navigateTo("/subPackages/promotion/pages/promotion-tool", selectedProduct.value)),
        t: common_vendor.o(($event) => navigateTo("/subPackages/promotion/pages/qrcode", selectedProduct.value)),
        v: common_vendor.o(copyPromotionLink),
        w: common_vendor.o(shareToFriends)
      } : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/distribution/pages/products.js.map
