<view class="activities-container"><cu-custom wx:if="{{a}}" u-s="{{['d']}}" u-i="7ca7fb6a-0" bind:__l="__l" u-p="{{a}}"><view u-s="{{['d']}}" slot="backText">返回</view><view u-s="{{['d']}}" slot="content">我参与的活动</view></cu-custom><view class="content"><view class="tab-nav"><view wx:for="{{b}}" wx:for-item="tab" wx:key="d" class="{{['tab-item', tab.e && 'active']}}" bindtap="{{tab.f}}"><text>{{tab.a}}</text><view wx:if="{{tab.b}}" class="tab-badge">{{tab.c}}</view></view></view><swiper class="tab-content" current="{{v}}" bindchange="{{w}}" style="{{'height:' + x}}"><swiper-item><scroll-view scroll-y class="scroll-view" bindscrolltolower="{{h}}"><view class="activity-list"><view wx:for="{{c}}" wx:for-item="item" wx:key="j" class="activity-item" bindtap="{{item.k}}"><image class="activity-image" src="{{item.a}}" mode="aspectFill"></image><view class="activity-info"><view class="activity-header"><text class="{{['activity-tag', item.c]}}">{{item.b}}</text><text class="{{['activity-status', item.e]}}">{{item.d}}</text></view><text class="activity-title">{{item.f}}</text><view class="activity-shop"><image class="shop-logo" src="{{item.g}}" mode="aspectFill"></image><text class="shop-name">{{item.h}}</text></view><view class="activity-time"><text>{{item.i}}</text></view></view></view><view wx:if="{{d}}" class="load-more"><text>加载中...</text></view><view wx:if="{{e}}" class="no-more"><text>没有更多活动了</text></view><view wx:if="{{f}}" class="empty-state"><image class="empty-image" src="{{g}}" mode="aspectFit"></image><text class="empty-text">暂无参与的活动</text></view></view></scroll-view></swiper-item><swiper-item><scroll-view scroll-y class="scroll-view" bindscrolltolower="{{n}}"><view class="activity-list"><view wx:for="{{i}}" wx:for-item="item" wx:key="m" class="activity-item" bindtap="{{item.n}}"><image class="activity-image" src="{{item.a}}" mode="aspectFill"></image><view class="activity-info"><view class="activity-header"><text class="activity-tag tag-groupon">拼团</text><text class="{{['activity-status', item.c]}}">{{item.b}}</text></view><text class="activity-title">{{item.d}}</text><view class="activity-shop"><image class="shop-logo" src="{{item.e}}" mode="aspectFill"></image><text class="shop-name">{{item.f}}</text></view><view class="activity-progress"><text class="progress-text">{{item.g}}/{{item.h}}人</text><view class="progress-bar"><view class="progress-inner" style="{{'width:' + item.i}}"></view></view></view><view class="activity-time"><text>{{item.j}}</text><text wx:if="{{item.k}}" class="countdown">剩余 {{item.l}}</text></view></view></view><view wx:if="{{j}}" class="load-more"><text>加载中...</text></view><view wx:if="{{k}}" class="no-more"><text>没有更多拼团了</text></view><view wx:if="{{l}}" class="empty-state"><image class="empty-image" src="{{m}}" mode="aspectFit"></image><text class="empty-text">暂无参与的拼团</text></view></view></scroll-view></swiper-item><swiper-item><scroll-view scroll-y class="scroll-view" bindscrolltolower="{{t}}"><view class="activity-list"><view wx:for="{{o}}" wx:for-item="item" wx:key="k" class="activity-item" bindtap="{{item.l}}"><image class="activity-image" src="{{item.a}}" mode="aspectFill"></image><view class="activity-info"><view class="activity-header"><text class="activity-tag tag-redpacket">红包</text><text class="{{['activity-status', item.c]}}">{{item.b}}</text></view><text class="activity-title">{{item.d}}</text><view class="activity-shop"><image class="shop-logo" src="{{item.e}}" mode="aspectFill"></image><text class="shop-name">{{item.f}}</text></view><view class="redpacket-amount"><text class="amount-value">¥{{item.g}}</text><text class="amount-desc">{{item.h}}</text></view><view class="activity-time"><text>{{item.i}}</text><text class="validity">有效期至 {{item.j}}</text></view></view></view><view wx:if="{{p}}" class="load-more"><text>加载中...</text></view><view wx:if="{{q}}" class="no-more"><text>没有更多红包了</text></view><view wx:if="{{r}}" class="empty-state"><image class="empty-image" src="{{s}}" mode="aspectFit"></image><text class="empty-text">暂无参与的红包活动</text></view></view></scroll-view></swiper-item></swiper></view></view>