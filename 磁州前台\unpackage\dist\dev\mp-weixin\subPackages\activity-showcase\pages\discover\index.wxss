/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-25c293b4, html.data-v-25c293b4, #app.data-v-25c293b4, .index-container.data-v-25c293b4 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.local-mall-container.data-v-25c293b4 {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F2F2F7;
}

/* 自定义导航栏 */
.custom-navbar.data-v-25c293b4 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
}
.custom-navbar .navbar-bg.data-v-25c293b4 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px rgba(255, 59, 105, 0.15);
}
.custom-navbar .navbar-content.data-v-25c293b4 {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 0 30rpx;
  padding-top: var(--status-bar-height, 25px);
  box-sizing: border-box;
}
.custom-navbar .navbar-title.data-v-25c293b4 {
  font-size: 36rpx;
  font-weight: 600;
  color: #FFFFFF;
  letter-spacing: 0.5px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
.custom-navbar .navbar-right.data-v-25c293b4 {
  display: flex;
  align-items: center;
  position: absolute;
  right: 30rpx;
}
.custom-navbar .search-btn.data-v-25c293b4, .custom-navbar .notification-btn.data-v-25c293b4 {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.custom-navbar .search-btn .icon.data-v-25c293b4, .custom-navbar .notification-btn .icon.data-v-25c293b4 {
  width: 48rpx;
  height: 48rpx;
}
.custom-navbar .search-btn .badge.data-v-25c293b4, .custom-navbar .notification-btn .badge.data-v-25c293b4 {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background: #FF3B30;
  color: #FFFFFF;
  font-size: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 6rpx;
  box-sizing: border-box;
  font-weight: 600;
  box-shadow: 0 2rpx 6rpx rgba(255, 59, 48, 0.3);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}

/* 内容区域 */
.content-scroll.data-v-25c293b4 {
  flex: 1;
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
  margin-bottom: 100rpx;
  /* 底部导航栏高度 */
  box-sizing: border-box;
}

/* 内容区域样式 */
.content-scroll.data-v-25c293b4 {
  padding: 0;
}

/* 区块卡片 */
.section-card.data-v-25c293b4 {
  background-color: #FFFFFF;
  margin: 30rpx;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 1;
}
.section-card.data-v-25c293b4:first-of-type {
  margin-top: 20rpx;
}
.section-header.data-v-25c293b4 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.section-header .section-title.data-v-25c293b4 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.section-header .section-more.data-v-25c293b4 {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #FF3B69;
}
.section-header .section-more .icon.data-v-25c293b4 {
  margin-left: 4rpx;
}

/* 商品横向滚动 */
.product-scroll.data-v-25c293b4 {
  white-space: nowrap;
}
.product-scroll .product-scroll-list.data-v-25c293b4 {
  display: inline-flex;
  padding: 10rpx 0;
}

/* 店铺列表 */
.shops-list.data-v-25c293b4 {
  margin-top: 20rpx;
}

/* 商品网格 */
.products-grid.data-v-25c293b4 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-top: 20rpx;
}

/* 底部安全区域 */
.safe-area-bottom.data-v-25c293b4 {
  height: 34px;
  /* iOS 安全区域高度 */
}

/* 底部导航栏 */
.tabbar.data-v-25c293b4 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #FFFFFF;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding-bottom: env(safe-area-inset-bottom);
  /* 适配 iPhone X 以上的底部安全区域 */
  z-index: 99;
  border-top: 1rpx solid #EEEEEE;
}
.tabbar-item.data-v-25c293b4 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 6px 0;
  box-sizing: border-box;
  position: relative;
}
.tabbar-item.data-v-25c293b4:active {
  transform: scale(0.9);
}
.tabbar-item.active .tab-icon.data-v-25c293b4 {
  transform: translateY(-5rpx);
}
.tabbar-item.active .tabbar-text.data-v-25c293b4 {
  color: #FF3B69;
  font-weight: 600;
  transform: translateY(-2rpx);
}
.tab-icon.data-v-25c293b4 {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  color: #999999;
  display: flex;
  justify-content: center;
  align-items: center;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 首页图标 */
.tab-icon.home.data-v-25c293b4 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E");
}
.tabbar-item.active[data-tab=home] .tab-icon.home.data-v-25c293b4 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E");
}

/* 发现图标（本地商城） */
.tab-icon.discover.data-v-25c293b4 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zM1 3c0 .55.45 1 1 1h1l3.6 7.59-1.35 2.44C4.52 15.37 5.48 17 7 17h11c.55 0 1-.45 1-1s-.45-1-1-1H7l1.1-2h7.45c.75 0 1.41-.41 1.75-1.03l3.58-6.49c.37-.66-.11-1.48-.87-1.48H5.21l-.67-1.43c-.16-.35-.52-.57-.9-.57H2c-.55 0-1 .45-1 1zm16 15c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2z'/%3E%3C/svg%3E");
}
.tabbar-item.active[data-tab=discover] .tab-icon.discover.data-v-25c293b4 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zM1 3c0 .55.45 1 1 1h1l3.6 7.59-1.35 2.44C4.52 15.37 5.48 17 7 17h11c.55 0 1-.45 1-1s-.45-1-1-1H7l1.1-2h7.45c.75 0 1.41-.41 1.75-1.03l3.58-6.49c.37-.66-.11-1.48-.87-1.48H5.21l-.67-1.43c-.16-.35-.52-.57-.9-.57H2c-.55 0-1 .45-1 1zm16 15c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2z'/%3E%3C/svg%3E");
}

/* 分销图标 */
.tab-icon.distribution.data-v-25c293b4 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E");
}
.tabbar-item.active[data-tab=distribution] .tab-icon.distribution.data-v-25c293b4 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E");
}

/* 消息图标 */
.tab-icon.message.data-v-25c293b4 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H5.17L4 17.17V4h16v12z'/%3E%3C/svg%3E");
}
.tabbar-item.active[data-tab=message] .tab-icon.message.data-v-25c293b4 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E");
}

/* 我的图标 */
.tab-icon.user.data-v-25c293b4 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}
.tabbar-item.active[data-tab=my] .tab-icon.user.data-v-25c293b4 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}
.tabbar-item.active .tab-icon.data-v-25c293b4 {
  filter: drop-shadow(0 1px 2px rgba(255, 59, 105, 0.3));
}
.badge.data-v-25c293b4 {
  position: absolute;
  top: -8rpx;
  right: -12rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #FF453A, #FF2D55);
  color: #FFFFFF;
  font-size: 18rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 6rpx;
  box-sizing: border-box;
  font-weight: 600;
  box-shadow: 0 2rpx 6rpx rgba(255, 45, 85, 0.3);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transform: scale(0.9);
}
.tabbar-text.data-v-25c293b4 {
  font-size: 22rpx;
  color: #8E8E93;
  margin-top: 2rpx;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}
.tabbar-item.data-v-25c293b4::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) scaleX(0);
  width: 30rpx;
  height: 4rpx;
  background: #FF3B69;
  border-radius: 2rpx;
  transition: transform 0.3s ease;
}
.tabbar-item.active.data-v-25c293b4::after {
  transform: translateX(-50%) scaleX(1);
}