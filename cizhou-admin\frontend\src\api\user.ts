import http from './request'
import type { 
  LoginParams, 
  LoginResponse, 
  UserInfo, 
  ChangePasswordParams,
  UserPermissions,
  RefreshTokenResponse,
  UserListParams,
  UserListItem,
  UserAuthInfo,
  UserAuthAuditParams
} from '@/types/user'

// 用户认证相关API
export const userApi = {
  // 用户登录
  login(params: LoginParams) {
    return http.post<LoginResponse>('/auth/login', params, {
      skipAuth: true,
      showLoading: true,
      loadingText: '登录中...'
    })
  },

  // 用户登出
  logout() {
    return http.post('/auth/logout')
  },

  // 获取用户信息
  getUserInfo() {
    return http.get<UserInfo>('/auth/user-info')
  },

  // 获取用户权限
  getUserPermissions() {
    return http.get<UserPermissions>('/auth/permissions')
  },

  // 刷新Token
  refreshToken() {
    return http.post<RefreshTokenResponse>('/auth/refresh-token')
  },

  // 修改密码
  changePassword(params: ChangePasswordParams) {
    return http.post('/auth/change-password', params)
  },

  // 更新用户信息
  updateUserInfo(params: Partial<UserInfo>) {
    return http.put<UserInfo>('/auth/update-profile', params)
  },

  // 上传头像
  uploadAvatar(file: File) {
    const formData = new FormData()
    formData.append('avatar', file)
    return http.upload<{ url: string }>('/auth/upload-avatar', formData)
  }
}

// C端用户管理API
export const customerApi = {
  // 获取用户列表
  getUserList(params: UserListParams) {
    return http.get<{
      list: UserListItem[]
      total: number
      page: number
      size: number
    }>('/customer/users', { params })
  },

  // 获取用户详情
  getUserDetail(id: string) {
    return http.get<UserListItem>(`/customer/users/${id}`)
  },

  // 更新用户状态
  updateUserStatus(id: string, status: number) {
    return http.put(`/customer/users/${id}/status`, { status })
  },

  // 批量更新用户状态
  batchUpdateUserStatus(ids: string[], status: number) {
    return http.put('/customer/users/batch-status', { ids, status })
  },

  // 重置用户密码
  resetUserPassword(id: string) {
    return http.post<{ password: string }>(`/customer/users/${id}/reset-password`)
  },

  // 删除用户
  deleteUser(id: string) {
    return http.delete(`/customer/users/${id}`)
  },

  // 批量删除用户
  batchDeleteUsers(ids: string[]) {
    return http.delete('/customer/users/batch', { data: { ids } })
  },

  // 导出用户数据
  exportUsers(params: UserListParams) {
    return http.download('/customer/users/export', { params })
  },

  // 获取用户统计数据
  getUserStats() {
    return http.get<{
      totalUsers: number
      activeUsers: number
      newUsers: number
      verifiedUsers: number
      userGrowthTrend: Array<{ date: string; count: number }>
      userLevelDistribution: Array<{ level: string; count: number }>
    }>('/customer/users/stats')
  }
}

// 用户认证管理API
export const userAuthApi = {
  // 获取认证申请列表
  getAuthList(params: {
    page: number
    size: number
    status?: number
    authType?: number
    startTime?: string
    endTime?: string
  }) {
    return http.get<{
      list: UserAuthInfo[]
      total: number
      page: number
      size: number
    }>('/customer/auth', { params })
  },

  // 获取认证详情
  getAuthDetail(id: string) {
    return http.get<UserAuthInfo>(`/customer/auth/${id}`)
  },

  // 审核认证申请
  auditAuth(params: UserAuthAuditParams) {
    return http.post(`/customer/auth/${params.id}/audit`, {
      status: params.status,
      auditRemark: params.auditRemark
    })
  },

  // 批量审核认证申请
  batchAuditAuth(ids: string[], status: number, auditRemark?: string) {
    return http.post('/customer/auth/batch-audit', {
      ids,
      status,
      auditRemark
    })
  },

  // 获取认证统计数据
  getAuthStats() {
    return http.get<{
      totalApplications: number
      pendingApplications: number
      approvedApplications: number
      rejectedApplications: number
      authTrend: Array<{ date: string; count: number }>
      authTypeDistribution: Array<{ type: string; count: number }>
    }>('/customer/auth/stats')
  }
}

// 管理员用户API
export const adminApi = {
  // 获取管理员列表
  getAdminList(params: {
    page: number
    size: number
    username?: string
    realName?: string
    status?: number
  }) {
    return http.get<{
      list: Array<{
        id: string
        username: string
        realName: string
        phone: string
        email: string
        avatar: string
        status: number
        roles: string[]
        lastLoginTime: string
        createTime: string
      }>
      total: number
      page: number
      size: number
    }>('/admin/users', { params })
  },

  // 创建管理员
  createAdmin(params: {
    username: string
    password: string
    realName: string
    phone: string
    email: string
    roleIds: string[]
  }) {
    return http.post('/admin/users', params)
  },

  // 更新管理员
  updateAdmin(id: string, params: {
    realName?: string
    phone?: string
    email?: string
    roleIds?: string[]
  }) {
    return http.put(`/admin/users/${id}`, params)
  },

  // 删除管理员
  deleteAdmin(id: string) {
    return http.delete(`/admin/users/${id}`)
  },

  // 重置管理员密码
  resetAdminPassword(id: string) {
    return http.post<{ password: string }>(`/admin/users/${id}/reset-password`)
  },

  // 更新管理员状态
  updateAdminStatus(id: string, status: number) {
    return http.put(`/admin/users/${id}/status`, { status })
  }
}
