/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.product-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(220rpx + var(--status-bar-height, 20px));
}

/* 顶部导航栏样式 */
.header-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
  color: #fff;
}
.safe-area-top {
  height: var(--status-bar-height, 20px);
  width: 100%;
}
.custom-navbar {
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}
.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
}
.navbar-left, .navbar-right {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.navbar-right {
  width: auto;
}
.add-product-text {
  font-size: 32rpx;
  color: #FFFFFF;
  padding: 10rpx 0;
}
.back-icon {
  width: 48rpx;
  height: 48rpx;
}

/* 分类切换栏 */
.category-scroll {
  width: 100%;
  white-space: nowrap;
  background-color: #0A84FF;
  padding: 0 20rpx;
}
.category-tabs {
  display: flex;
  padding-bottom: 20rpx;
}
.category-tab {
  padding: 10rpx 30rpx;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  position: relative;
  flex-shrink: 0;
}
.category-tab.active {
  color: #FFFFFF;
  font-weight: 600;
}
.category-tab.active::after {
  content: "";
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background-color: #FFFFFF;
  border-radius: 3rpx;
}

/* 搜索框 */
.search-box {
  display: flex;
  align-items: center;
  background-color: #FFFFFF;
  border-radius: 40rpx;
  margin: 0 30rpx 20rpx;
  padding: 0 20rpx;
  position: relative;
}
.search-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}
.search-input {
  height: 80rpx;
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.clear-icon {
  font-size: 40rpx;
  color: #999;
  padding: 0 10rpx;
}

/* 背景装饰样式 */
.bg-decoration {
  position: absolute;
  z-index: -1;
  border-radius: 50%;
  opacity: 0.07;
  background-color: #0A84FF;
}
.bg-circle-1 {
  top: -100rpx;
  right: -150rpx;
  width: 400rpx;
  height: 400rpx;
}
.bg-circle-2 {
  top: 20%;
  left: -200rpx;
  width: 500rpx;
  height: 500rpx;
}
.bg-circle-3 {
  bottom: 10%;
  right: -100rpx;
  width: 300rpx;
  height: 300rpx;
}

/* 内容区域 */
.content-scroll {
  height: calc(100vh - 220rpx - var(--status-bar-height, 20px));
  padding: 30rpx;
  box-sizing: border-box;
}

/* 搜索结果提示 */
.search-result-tip {
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #666;
}
.search-keyword {
  color: #0A84FF;
  font-weight: 600;
  margin-right: 10rpx;
}
.search-count {
  color: #999;
}

/* 批量操作工具栏 */
.batch-toolbar {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}
.selection-info {
  font-size: 28rpx;
  color: #333;
}
.selected-count {
  color: #0A84FF;
  font-weight: 600;
}
.batch-actions {
  display: flex;
  align-items: center;
}
.batch-btn {
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  background-color: rgba(10, 132, 255, 0.1);
  margin-left: 20rpx;
}
.batch-btn.delete {
  background-color: rgba(255, 59, 48, 0.1);
}
.btn-text {
  font-size: 26rpx;
  color: #0A84FF;
}
.delete .btn-text {
  color: #FF3B30;
}

/* 商品列表 */
.product-list {
  padding-bottom: 100rpx;
}
.product-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  position: relative;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}
.product-item.selected {
  background-color: rgba(10, 132, 255, 0.05);
  border: 2rpx solid #0A84FF;
}
.product-content {
  display: flex;
  padding: 20rpx;
}
.select-checkbox {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  z-index: 10;
}
.checkbox {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #CCCCCC;
  background-color: #FFFFFF;
}
.checkbox.checked {
  background-color: #0A84FF;
  border-color: #0A84FF;
  position: relative;
}
.checkbox.checked::after {
  content: "✓";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  font-size: 24rpx;
}
.product-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}
.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.product-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
}
.product-category {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
  background-color: #F5F8FC;
  display: inline-block;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}
.product-price {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.price {
  font-size: 32rpx;
  color: #FF3B30;
  font-weight: 600;
  margin-right: 10rpx;
}
.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}
.product-stats {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
}
.sales-count {
  margin-right: 20rpx;
}
.product-status {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background-color: rgba(52, 199, 89, 0.1);
  color: #34C759;
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}
.status-off {
  background-color: rgba(142, 142, 147, 0.1);
  color: #8E8E93;
}
.quick-actions {
  display: flex;
  padding: 20rpx;
  border-top: 2rpx solid #F2F2F7;
  justify-content: flex-end;
}
.action-btn {
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  background-color: rgba(10, 132, 255, 0.1);
  font-size: 26rpx;
  color: #0A84FF;
  margin-left: 20rpx;
}
.action-btn.edit {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

/* 加载更多状态 */
.loading-more {
  text-align: center;
  padding: 30rpx 0;
}
.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 无数据提示 */
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.no-data-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.no-data-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 100rpx;
}

/* 批量操作浮动按钮 */
.float-actions {
  position: fixed;
  right: 30rpx;
  bottom: calc(100rpx + env(safe-area-inset-bottom));
  z-index: 50;
}
.float-btn {
  width: 180rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background: linear-gradient(135deg, #0A84FF, #0055FF);
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(10, 132, 255, 0.3);
}
.float-btn-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
}
.delete-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  overflow: hidden;
  z-index: 1000;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);
}
.modal-header {
  padding: 30rpx;
  text-align: center;
  border-bottom: 2rpx solid #F2F2F7;
}
.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.modal-content {
  padding: 30rpx;
}
.confirm-message {
  font-size: 28rpx;
  color: #333;
  text-align: center;
  margin-bottom: 20rpx;
}
.confirm-warning {
  font-size: 24rpx;
  color: #FF3B30;
  text-align: center;
}
.modal-footer {
  display: flex;
  border-top: 2rpx solid #F2F2F7;
}
.modal-btn {
  flex: 1;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  background-color: transparent;
}
.modal-btn::after {
  border: none;
}
.cancel {
  color: #999;
  border-right: 1px solid #F2F2F7;
}
.confirm {
  color: #0A84FF;
  font-weight: 500;
}
.delete {
  color: #FF3B30;
}

/* 适配暗黑模式 */
@media (prefers-color-scheme: dark) {
.product-container {
    background-color: #1C1C1E;
}
.search-box {
    background-color: #3A3A3C;
}
.search-input {
    color: #FFFFFF;
}
.batch-toolbar,
.product-item,
.delete-modal {
    background-color: #2C2C2E;
}
.product-item.selected {
    background-color: rgba(10, 132, 255, 0.15);
}
.product-name {
    color: #FFFFFF;
}
.product-category {
    background-color: #3A3A3C;
}
.modal-title,
.confirm-message {
    color: #FFFFFF;
}
.modal-header,
.modal-footer,
.quick-actions {
    border-color: #3A3A3C;
}
.checkbox {
    background-color: #2C2C2E;
    border-color: #8E8E93;
}
}