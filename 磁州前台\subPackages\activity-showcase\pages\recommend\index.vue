<template>
  <view class="recommend-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="15 18 9 12 15 6"></polyline>
          </svg>
        </view>
        <view class="navbar-title">热门推荐</view>
        <view class="navbar-right">
          <view class="filter-btn" @click="showFilterOptions">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
            </svg>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y @scrolltolower="loadMore">
      <!-- 推荐标签 -->
      <view class="recommend-tabs">
        <scroll-view class="tabs-scroll" scroll-x show-scrollbar="false">
          <view 
            class="tab-item" 
            v-for="(tab, index) in recommendTabs" 
            :key="index"
            :class="{ active: currentTab === tab.value }"
            @click="switchTab(tab.value)"
          >
            <view class="tab-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path v-if="tab.value === 'hot'" d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"></path>
                <path v-else-if="tab.value === 'new'" d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 21 12 17.27 5.82 21 7 13.87 2 9l6.91-.74L12 2z"></path>
                <path v-else-if="tab.value === 'sale'" d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                <path v-else-if="tab.value === 'quality'" d="M9 12l2 2 4-4M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z"></path>
                <circle v-else cx="12" cy="12" r="10"></circle>
              </svg>
            </view>
            <text class="tab-text">{{ tab.label }}</text>
          </view>
        </scroll-view>
      </view>

      <!-- 推荐横幅 -->
      <view class="recommend-banner">
        <swiper class="banner-swiper" indicator-dots autoplay circular>
          <swiper-item v-for="(banner, index) in banners" :key="index">
            <view class="banner-item" @click="navigateToBanner(banner)">
              <image class="banner-image" :src="banner.image" mode="aspectFill"></image>
              <view class="banner-overlay">
                <text class="banner-title">{{ banner.title }}</text>
                <text class="banner-desc">{{ banner.description }}</text>
              </view>
            </view>
          </swiper-item>
        </swiper>
      </view>

      <!-- 今日推荐 -->
      <view class="today-recommend">
        <view class="section-header">
          <view class="header-left">
            <view class="section-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 21 12 17.27 5.82 21 7 13.87 2 9l6.91-.74L12 2z"></path>
              </svg>
            </view>
            <text class="section-title">今日推荐</text>
          </view>
          <text class="section-desc">{{ getCurrentDate() }} 为您精选</text>
        </view>
        
        <scroll-view class="today-scroll" scroll-x show-scrollbar="false">
          <view 
            class="today-item" 
            v-for="(item, index) in todayRecommends" 
            :key="item.id"
            @click="navigateToProduct(item)"
          >
            <view class="today-image-container">
              <image class="today-image" :src="item.image" mode="aspectFill" :lazy-load="true"></image>
              <view class="today-badge">
                <text class="badge-text">今日推荐</text>
              </view>
            </view>
            <view class="today-info">
              <text class="today-name">{{ item.name }}</text>
              <view class="today-price">
                <text class="current-price">¥{{ item.price }}</text>
                <text class="original-price" v-if="item.originalPrice">¥{{ item.originalPrice }}</text>
              </view>
              <view class="today-stats">
                <text class="recommend-reason">{{ item.reason }}</text>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 推荐商品列表 -->
      <view class="recommend-products">
        <view class="section-header">
          <view class="header-left">
            <text class="section-title">{{ getTabTitle() }}</text>
          </view>
          <view class="sort-options">
            <view 
              class="sort-item" 
              v-for="(sort, index) in sortTypes" 
              :key="index"
              :class="{ active: currentSort === sort.value }"
              @click="switchSort(sort.value)"
            >
              <text class="sort-text">{{ sort.label }}</text>
            </view>
          </view>
        </view>

        <view class="products-grid">
          <view 
            class="product-item" 
            v-for="(product, index) in filteredProducts" 
            :key="product.id"
            @click="navigateToProduct(product)"
          >
            <view class="product-image-container">
              <image class="product-image" :src="product.image" mode="aspectFill" :lazy-load="true"></image>
              <view class="product-tags">
                <view class="tag" v-if="product.isHot">
                  <text class="tag-text">热</text>
                </view>
                <view class="tag new" v-if="product.isNew">
                  <text class="tag-text">新</text>
                </view>
                <view class="tag sale" v-if="product.onSale">
                  <text class="tag-text">促</text>
                </view>
              </view>
              <view class="product-discount" v-if="product.discount">
                <text class="discount-text">{{ product.discount }}折</text>
              </view>
            </view>
            <view class="product-info">
              <text class="product-name">{{ product.name }}</text>
              <text class="product-desc">{{ product.description }}</text>
              <view class="product-price">
                <text class="current-price">¥{{ product.price }}</text>
                <text class="original-price" v-if="product.originalPrice">¥{{ product.originalPrice }}</text>
              </view>
              <view class="product-stats">
                <view class="stats-left">
                  <text class="sales-count">已售{{ product.sales }}</text>
                  <view class="product-rating">
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <polygon points="12 2 15.09 8.26 22 9 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9 8.91 8.26 12 2"></polygon>
                    </svg>
                    <text class="rating-text">{{ product.rating }}</text>
                  </view>
                </view>
                <view class="recommend-tag">
                  <text class="recommend-text">{{ product.recommendReason }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="loading-more" v-if="loading">
        <text>加载中...</text>
      </view>
      
      <!-- 没有更多数据 -->
      <view class="no-more" v-if="noMore && filteredProducts.length > 0">
        <text>已经到底啦~</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="filteredProducts.length === 0 && !loading">
        <view class="empty-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M16 16s-1.5-2-4-2-4 2-4 2"></path>
            <line x1="9" y1="9" x2="9.01" y2="9"></line>
            <line x1="15" y1="9" x2="15.01" y2="9"></line>
          </svg>
        </view>
        <text class="empty-text">暂无推荐</text>
        <text class="empty-desc">暂时没有相关推荐商品</text>
      </view>

      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>

    <!-- 筛选弹窗 -->
    <view class="filter-modal" v-if="showFilterModal" @click="hideFilterOptions">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">筛选条件</text>
          <view class="close-btn" @click="hideFilterOptions">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </view>
        </view>
        <view class="filter-content">
          <view class="filter-section">
            <text class="filter-title">价格区间</text>
            <view class="filter-options">
              <view 
                class="filter-option" 
                v-for="(price, index) in priceRanges" 
                :key="index"
                :class="{ active: selectedPrice === price.value }"
                @click="selectPrice(price.value)"
              >
                <text class="option-text">{{ price.label }}</text>
              </view>
            </view>
          </view>
          <view class="filter-section">
            <text class="filter-title">商品类型</text>
            <view class="filter-options">
              <view 
                class="filter-option" 
                v-for="(type, index) in productTypes" 
                :key="index"
                :class="{ active: selectedType === type.value }"
                @click="selectType(type.value)"
              >
                <text class="option-text">{{ type.label }}</text>
              </view>
            </view>
          </view>
        </view>
        <view class="filter-actions">
          <view class="action-btn reset" @click="resetFilter">
            <text class="btn-text">重置</text>
          </view>
          <view class="action-btn confirm" @click="applyFilter">
            <text class="btn-text">确定</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const currentTab = ref('hot')
const currentSort = ref('default')
const loading = ref(false)
const noMore = ref(false)
const showFilterModal = ref(false)
const selectedPrice = ref('all')
const selectedType = ref('all')

// 推荐标签
const recommendTabs = ref([
  { label: '热门', value: 'hot' },
  { label: '新品', value: 'new' },
  { label: '促销', value: 'sale' },
  { label: '精选', value: 'quality' }
])

// 排序类型
const sortTypes = ref([
  { label: '综合', value: 'default' },
  { label: '销量', value: 'sales' },
  { label: '价格', value: 'price' },
  { label: '评分', value: 'rating' }
])

// 价格区间
const priceRanges = ref([
  { label: '全部', value: 'all' },
  { label: '0-50元', value: '0-50' },
  { label: '50-100元', value: '50-100' },
  { label: '100-200元', value: '100-200' },
  { label: '200元以上', value: '200+' }
])

// 商品类型
const productTypes = ref([
  { label: '全部', value: 'all' },
  { label: '实物商品', value: 'physical' },
  { label: '虚拟商品', value: 'virtual' },
  { label: '服务类', value: 'service' }
])

// 横幅数据
const banners = ref([
  {
    id: 1,
    title: '热门推荐',
    description: '精选好物，限时优惠',
    image: '/static/images/recommend/banner1.jpg',
    url: '/pages/activity/hot'
  },
  {
    id: 2,
    title: '新品上市',
    description: '全新体验，抢先体验',
    image: '/static/images/recommend/banner2.jpg',
    url: '/pages/activity/new'
  }
])

// 今日推荐
const todayRecommends = ref([
  {
    id: 1,
    name: '精选好物套装',
    image: '/static/images/products/recommend1.jpg',
    price: 89.9,
    originalPrice: 129.9,
    reason: '性价比之选'
  },
  {
    id: 2,
    name: '热销爆款单品',
    image: '/static/images/products/recommend2.jpg',
    price: 59.9,
    reason: '用户好评'
  }
])

// 推荐商品
const products = ref([
  {
    id: 1,
    name: '热门商品A',
    description: '高品质，用户好评如潮',
    image: '/static/images/products/hot1.jpg',
    price: 99.9,
    originalPrice: 149.9,
    sales: 2580,
    rating: 4.9,
    isHot: true,
    isNew: false,
    onSale: true,
    discount: 6.7,
    recommendReason: '热销爆款',
    category: 'hot',
    type: 'physical'
  },
  {
    id: 2,
    name: '新品推荐B',
    description: '全新上市，限时优惠',
    image: '/static/images/products/new1.jpg',
    price: 79.9,
    sales: 156,
    rating: 4.8,
    isHot: false,
    isNew: true,
    onSale: false,
    recommendReason: '新品首发',
    category: 'new',
    type: 'physical'
  }
])

// 计算属性
const filteredProducts = computed(() => {
  let filtered = products.value.filter(product => {
    // 按标签筛选
    if (currentTab.value !== 'all') {
      if (currentTab.value === 'hot' && !product.isHot) return false
      if (currentTab.value === 'new' && !product.isNew) return false
      if (currentTab.value === 'sale' && !product.onSale) return false
      if (currentTab.value === 'quality' && product.rating < 4.5) return false
    }
    
    // 按价格筛选
    if (selectedPrice.value !== 'all') {
      const price = product.price
      switch (selectedPrice.value) {
        case '0-50':
          if (price > 50) return false
          break
        case '50-100':
          if (price <= 50 || price > 100) return false
          break
        case '100-200':
          if (price <= 100 || price > 200) return false
          break
        case '200+':
          if (price <= 200) return false
          break
      }
    }
    
    // 按类型筛选
    if (selectedType.value !== 'all' && product.type !== selectedType.value) {
      return false
    }
    
    return true
  })

  // 排序
  switch (currentSort.value) {
    case 'sales':
      filtered.sort((a, b) => b.sales - a.sales)
      break
    case 'price':
      filtered.sort((a, b) => a.price - b.price)
      break
    case 'rating':
      filtered.sort((a, b) => b.rating - a.rating)
      break
  }

  return filtered
})

// 页面加载
onMounted(() => {
  console.log('热门推荐页面加载')
  loadRecommendData()
})

// 方法
function goBack() {
  uni.navigateBack()
}

function switchTab(tab) {
  currentTab.value = tab
}

function switchSort(sort) {
  currentSort.value = sort
}

function getTabTitle() {
  const tab = recommendTabs.value.find(t => t.value === currentTab.value)
  return tab ? tab.label : '推荐商品'
}

function getCurrentDate() {
  const date = new Date()
  const month = date.getMonth() + 1
  const day = date.getDate()
  return `${month}月${day}日`
}

function showFilterOptions() {
  showFilterModal.value = true
}

function hideFilterOptions() {
  showFilterModal.value = false
}

function selectPrice(price) {
  selectedPrice.value = price
}

function selectType(type) {
  selectedType.value = type
}

function resetFilter() {
  selectedPrice.value = 'all'
  selectedType.value = 'all'
}

function applyFilter() {
  hideFilterOptions()
}

function navigateToProduct(product) {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/products/detail/index?id=${product.id}`
  })
}

function navigateToBanner(banner) {
  uni.navigateTo({
    url: banner.url
  })
}

function loadMore() {
  if (loading.value || noMore.value) return
  
  loading.value = true
  
  // 模拟加载更多数据
  setTimeout(() => {
    noMore.value = true
    loading.value = false
  }, 1000)
}

function loadRecommendData() {
  // 模拟加载推荐数据
  setTimeout(() => {
    console.log('推荐数据加载完成')
  }, 500)
}
</script>

<style scoped>
/* 热门推荐样式开始 */
.recommend-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FF5722 0%, #E64A19 100%);
}

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 87, 34, 0.95);
  backdrop-filter: blur(10px);
  padding-top: var(--status-bar-height, 44px);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
}

.back-btn, .filter-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.2);
}

.back-btn svg, .filter-btn svg {
  color: white;
}

.navbar-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
}

/* 内容区域样式 */
.content-scroll {
  padding-top: calc(var(--status-bar-height, 44px) + 44px);
  height: 100vh;
}

/* 推荐标签样式 */
.recommend-tabs {
  background: rgba(255, 255, 255, 0.95);
  padding: 12px 0;
  margin: 16px 16px 0;
  border-radius: 12px;
}

.tabs-scroll {
  white-space: nowrap;
  padding: 0 16px;
}

.tab-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 16px;
  margin-right: 12px;
  border-radius: 20px;
  transition: all 0.3s ease;
  gap: 4px;
}

.tab-item.active {
  background: #FF5722;
}

.tab-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-icon svg {
  color: #666;
}

.tab-item.active .tab-icon svg {
  color: white;
}

.tab-text {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.tab-item.active .tab-text {
  color: white;
}

/* 推荐横幅样式 */
.recommend-banner {
  margin: 16px;
  border-radius: 12px;
  overflow: hidden;
  height: 160px;
}

.banner-swiper {
  height: 100%;
}

.banner-item {
  position: relative;
  height: 100%;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
}

.banner-title {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.banner-desc {
  display: block;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

/* 今日推荐样式 */
.today-recommend {
  background: white;
  margin: 0 16px 16px;
  border-radius: 12px;
  padding: 16px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #FF5722;
  border-radius: 12px;
}

.section-icon svg {
  color: white;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-desc {
  font-size: 12px;
  color: #666;
}

.today-scroll {
  white-space: nowrap;
}

.today-item {
  display: inline-block;
  width: 140px;
  margin-right: 12px;
}

.today-image-container {
  position: relative;
  width: 140px;
  height: 140px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 8px;
}

.today-image {
  width: 100%;
  height: 100%;
}

.today-badge {
  position: absolute;
  top: 6px;
  left: 6px;
  padding: 2px 6px;
  background: #FF5722;
  border-radius: 8px;
}

.today-badge .badge-text {
  font-size: 10px;
  color: white;
  font-weight: 500;
}

.today-info {
  text-align: left;
}

.today-name {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.today-price {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
}

.current-price {
  font-size: 16px;
  font-weight: 600;
  color: #FF5722;
}

.original-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

.recommend-reason {
  font-size: 12px;
  color: #FF5722;
  background: rgba(255, 87, 34, 0.1);
  padding: 2px 6px;
  border-radius: 8px;
}

/* 推荐商品列表样式 */
.recommend-products {
  background: white;
  margin: 0 16px 16px;
  border-radius: 12px;
  padding: 16px;
}

.sort-options {
  display: flex;
  gap: 8px;
}

.sort-item {
  padding: 4px 12px;
  background: #f5f5f5;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.sort-item.active {
  background: #FF5722;
}

.sort-text {
  font-size: 12px;
  color: #666;
}

.sort-item.active .sort-text {
  color: white;
}

/* 商品网格样式 */
.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-top: 12px;
}

.product-item {
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.product-item:active {
  transform: scale(0.98);
}

.product-image-container {
  position: relative;
  width: 100%;
  height: 120px;
}

.product-image {
  width: 100%;
  height: 100%;
}

.product-tags {
  position: absolute;
  top: 6px;
  left: 6px;
  display: flex;
  gap: 4px;
}

.tag {
  padding: 2px 6px;
  background: #FF5722;
  border-radius: 8px;
}

.tag.new {
  background: #4CAF50;
}

.tag.sale {
  background: #FF9800;
}

.tag-text {
  font-size: 10px;
  color: white;
  font-weight: 500;
}

.product-discount {
  position: absolute;
  top: 6px;
  right: 6px;
  padding: 2px 6px;
  background: #E91E63;
  border-radius: 8px;
}

.discount-text {
  font-size: 10px;
  color: white;
  font-weight: 500;
}

.product-info {
  padding: 12px;
}

.product-name {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-desc {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
}

.product-stats {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stats-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sales-count {
  font-size: 12px;
  color: #666;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 2px;
}

.product-rating svg {
  color: #FFD700;
  fill: #FFD700;
}

.rating-text {
  font-size: 12px;
  color: #666;
}

.recommend-tag {
  padding: 2px 6px;
  background: rgba(255, 87, 34, 0.1);
  border-radius: 8px;
}

.recommend-text {
  font-size: 10px;
  color: #FF5722;
  font-weight: 500;
}

/* 加载状态样式 */
.loading-more, .no-more {
  text-align: center;
  padding: 20px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 20px;
}

.empty-icon {
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-icon svg {
  color: white;
}

.empty-text {
  font-size: 16px;
  color: white;
  font-weight: 500;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

/* 筛选弹窗样式 */
.filter-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 2000;
}

.modal-content {
  background: white;
  border-radius: 16px 16px 0 0;
  width: 100%;
  max-height: 70vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: #f5f5f5;
}

.close-btn svg {
  color: #666;
}

.filter-content {
  padding: 0 20px;
  max-height: 50vh;
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 24px;
}

.filter-title {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-option {
  padding: 8px 16px;
  background: #f5f5f5;
  border-radius: 20px;
  border: 1px solid transparent;
  transition: all 0.3s ease;
}

.filter-option.active {
  background: rgba(255, 87, 34, 0.1);
  border-color: #FF5722;
}

.option-text {
  font-size: 14px;
  color: #666;
}

.filter-option.active .option-text {
  color: #FF5722;
  font-weight: 500;
}

.filter-actions {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #f0f0f0;
}

.action-btn {
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
}

.action-btn.reset {
  background: #f5f5f5;
}

.action-btn.confirm {
  background: #FF5722;
}

.btn-text {
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

.action-btn.confirm .btn-text {
  color: white;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  background: transparent;
}
/* 热门推荐样式结束 */
</style>
