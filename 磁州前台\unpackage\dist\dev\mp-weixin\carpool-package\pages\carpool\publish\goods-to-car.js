"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
if (!Math) {
  CarpoolNav();
}
const CarpoolNav = () => "../../../../components/carpool-nav.js";
const _sfc_main = {
  __name: "goods-to-car",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const formData = common_vendor.ref({
      goodsName: "",
      goodsType: "",
      weight: "",
      volume: "",
      startPoint: "",
      endPoint: "",
      departureDate: "",
      price: "",
      contactName: "",
      contactPhone: "",
      remark: "",
      agreement: false,
      viaPoints: []
    });
    const goodsTypes = common_vendor.ref(["普通货物", "易碎品", "液体", "食品", "电子产品", "家具", "其他"]);
    const goodsTypeIndex = common_vendor.ref(0);
    const publishMode = common_vendor.ref("ad");
    common_vendor.onMounted(() => {
      setStatusBarHeight();
    });
    const setStatusBarHeight = () => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = systemInfo.statusBarHeight;
    };
    const chooseLocation = (type, index) => {
      common_vendor.index.chooseLocation({
        success: (res) => {
          if (type === "start") {
            formData.value.startPoint = res.name;
          } else if (type === "end") {
            formData.value.endPoint = res.name;
          } else if (type === "via") {
            if (index !== void 0 && index !== null) {
              formData.value.viaPoints[index] = res.name;
            } else {
              formData.value.viaPoints.push(res.name);
            }
          }
        }
      });
    };
    const onDateChange = (e) => {
      formData.value.departureDate = e.detail.value;
    };
    const onGoodsTypeChange = (e) => {
      goodsTypeIndex.value = e.detail.value;
      formData.value.goodsType = goodsTypes.value[goodsTypeIndex.value];
    };
    const onAgreementChange = (e) => {
      formData.value.agreement = e.detail.value.length > 0;
    };
    const viewAgreement = () => {
      common_vendor.index.navigateTo({
        url: "/pages/carpool/agreement"
      });
    };
    const submitForm = () => {
      if (!formData.value.goodsName) {
        showToast("请输入货物名称");
        return;
      }
      if (!formData.value.goodsType) {
        showToast("请选择货物类型");
        return;
      }
      if (!formData.value.weight) {
        showToast("请输入货物重量");
        return;
      }
      if (!formData.value.volume) {
        showToast("请输入货物体积");
        return;
      }
      if (!formData.value.startPoint) {
        showToast("请输入出发地");
        return;
      }
      if (!formData.value.endPoint) {
        showToast("请输入目的地");
        return;
      }
      if (!formData.value.departureDate) {
        showToast("请选择发货日期");
        return;
      }
      if (!formData.value.contactPhone) {
        showToast("请输入手机号码");
        return;
      }
      if (!/^1\d{10}$/.test(formData.value.contactPhone)) {
        showToast("手机号码格式不正确");
        return;
      }
      if (publishMode.value === "ad") {
        handleAdPublish();
      } else if (publishMode.value === "premium") {
        handlePremiumPublish();
      }
    };
    const handleAdPublish = () => {
      common_vendor.index.showLoading({
        title: "正在加载广告..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showModal({
          title: "广告播放完成",
          content: "感谢您观看广告，现在可以免费发布拼车信息",
          showCancel: false,
          success: () => {
            submitToServer();
          }
        });
      }, 1500);
    };
    const handlePremiumPublish = () => {
      common_vendor.index.showModal({
        title: "付费发布",
        content: "您将支付5元获得置顶发布特权，是否继续？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({
              title: "正在支付..."
            });
            setTimeout(() => {
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "支付成功",
                icon: "success"
              });
              submitToServer();
            }, 1500);
          }
        }
      });
    };
    const submitToServer = () => {
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      const formDataToSubmit = {
        ...formData.value,
        // 过滤掉空的途径地点
        viaPoints: formData.value.viaPoints.filter((point) => point.trim() !== "")
      };
      common_vendor.index.__f__("log", "at carpool-package/pages/carpool/publish/goods-to-car.vue:392", "提交的表单数据：", formDataToSubmit);
      setTimeout(() => {
        common_vendor.index.hideLoading();
        const publishId = Date.now().toString();
        common_vendor.index.navigateTo({
          url: `/carpool-package/pages/carpool/publish/success?id=${publishId}&type=goods-to-car&mode=${publishMode.value}`
        });
      }, 1e3);
    };
    const showToast = (title) => {
      common_vendor.index.showToast({
        title,
        icon: "none"
      });
    };
    const selectPublishMode = (mode) => {
      publishMode.value = mode;
    };
    const removeViaPoint = (index) => {
      formData.value.viaPoints.splice(index, 1);
    };
    const addViaPoint = () => {
      formData.value.viaPoints.push("");
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          title: "发布货找车信息"
        }),
        b: formData.value.goodsName,
        c: common_vendor.o(($event) => formData.value.goodsName = $event.detail.value),
        d: common_vendor.t(formData.value.goodsType || "请选择货物类型"),
        e: common_assets._imports_0$27,
        f: goodsTypes.value,
        g: goodsTypeIndex.value,
        h: common_vendor.o(onGoodsTypeChange),
        i: formData.value.weight,
        j: common_vendor.o(($event) => formData.value.weight = $event.detail.value),
        k: formData.value.volume,
        l: common_vendor.o(($event) => formData.value.volume = $event.detail.value),
        m: formData.value.startPoint,
        n: common_vendor.o(($event) => formData.value.startPoint = $event.detail.value),
        o: common_assets._imports_2$36,
        p: common_vendor.o(($event) => chooseLocation("start")),
        q: formData.value.endPoint,
        r: common_vendor.o(($event) => formData.value.endPoint = $event.detail.value),
        s: common_assets._imports_2$36,
        t: common_vendor.o(($event) => chooseLocation("end")),
        v: common_vendor.f(formData.value.viaPoints, (point, index, i0) => {
          return {
            a: formData.value.viaPoints[index],
            b: common_vendor.o(($event) => formData.value.viaPoints[index] = $event.detail.value, index),
            c: common_vendor.o(($event) => chooseLocation("via", index), index),
            d: common_vendor.o(($event) => removeViaPoint(index), index),
            e: index
          };
        }),
        w: common_assets._imports_2$36,
        x: formData.value.viaPoints.length < 3
      }, formData.value.viaPoints.length < 3 ? {
        y: common_vendor.o(addViaPoint)
      } : {}, {
        z: common_vendor.t(formData.value.departureDate || "请选择发货日期"),
        A: common_assets._imports_0$27,
        B: formData.value.departureDate,
        C: common_vendor.o(onDateChange),
        D: formData.value.price,
        E: common_vendor.o(($event) => formData.value.price = $event.detail.value),
        F: formData.value.contactName,
        G: common_vendor.o(($event) => formData.value.contactName = $event.detail.value),
        H: formData.value.contactPhone,
        I: common_vendor.o(($event) => formData.value.contactPhone = $event.detail.value),
        J: formData.value.remark,
        K: common_vendor.o(($event) => formData.value.remark = $event.detail.value),
        L: common_vendor.t(formData.value.remark.length),
        M: formData.value.agreement,
        N: common_vendor.o(viewAgreement),
        O: common_vendor.o(onAgreementChange),
        P: common_assets._imports_2$37,
        Q: publishMode.value === "ad"
      }, publishMode.value === "ad" ? {} : {}, {
        R: publishMode.value === "ad" ? 1 : "",
        S: common_vendor.o(($event) => selectPublishMode("ad")),
        T: common_assets._imports_8$6,
        U: publishMode.value === "premium"
      }, publishMode.value === "premium" ? {} : {}, {
        V: publishMode.value === "premium" ? 1 : "",
        W: common_vendor.o(($event) => selectPublishMode("premium")),
        X: !formData.value.agreement,
        Y: common_vendor.o(submitForm)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/carpool-package/pages/carpool/publish/goods-to-car.js.map
