"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      toolId: "",
      toolInfo: null,
      // 积分设置
      pointsSettings: {
        ratio: 10,
        validity: 12,
        discount: 100
      },
      // 任务列表
      tasks: [
        {
          id: 1,
          name: "每日签到",
          reward: "10积分",
          enabled: true
        },
        {
          id: 2,
          name: "分享商品",
          reward: "20积分",
          enabled: true
        },
        {
          id: 3,
          name: "评价订单",
          reward: "30积分",
          enabled: true
        },
        {
          id: 4,
          name: "完成首单",
          reward: "50积分",
          enabled: true
        }
      ],
      // 卡券列表
      coupons: [
        {
          id: 1,
          name: "新人优惠券",
          value: "满100减10",
          validity: "领取后30天内有效"
        },
        {
          id: 2,
          name: "会员生日券",
          value: "满200减30",
          validity: "生日当月有效"
        },
        {
          id: 3,
          name: "节日特惠券",
          value: "满300减50",
          validity: "2023-05-01至2023-05-07"
        }
      ],
      // 活动列表
      activities: [
        {
          id: 1,
          name: "会员日特惠",
          startTime: "2023-05-20",
          endTime: "2023-05-21",
          status: "upcoming",
          statusText: "即将开始"
        },
        {
          id: 2,
          name: "积分翻倍周",
          startTime: "2023-04-15",
          endTime: "2023-04-21",
          status: "active",
          statusText: "进行中"
        },
        {
          id: 3,
          name: "春季促销",
          startTime: "2023-03-01",
          endTime: "2023-03-15",
          status: "ended",
          statusText: "已结束"
        }
      ]
    };
  },
  onLoad(options) {
    if (options.id) {
      this.toolId = options.id;
      this.fetchToolInfo();
    }
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    fetchToolInfo() {
      const toolsData = [
        {
          id: "1",
          name: "会员积分",
          description: "管理会员积分规则",
          icon: "/static/images/points-icon.png",
          color: "linear-gradient(135deg, #FF9500, #FF5E3A)"
        },
        {
          id: "2",
          name: "会员任务",
          description: "设置会员任务和奖励",
          icon: "/static/images/task-icon.png",
          color: "linear-gradient(135deg, #F6D365, #FDA085)"
        },
        {
          id: "3",
          name: "会员卡券",
          description: "发放会员专属卡券",
          icon: "/static/images/coupon-icon.png",
          color: "linear-gradient(135deg, #FF6FD8, #3813C2)"
        },
        {
          id: "4",
          name: "会员活动",
          description: "创建会员专属活动",
          icon: "/static/images/activity-icon.png",
          color: "linear-gradient(135deg, #43E97B, #38F9D7)"
        }
      ];
      this.toolInfo = toolsData.find((tool) => tool.id === this.toolId) || null;
      if (!this.toolInfo) {
        common_vendor.index.showToast({
          title: "工具信息不存在",
          icon: "none"
        });
      }
    },
    saveSettings() {
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "设置保存成功",
          icon: "success"
        });
      }, 1e3);
    },
    toggleTask(task, e) {
      const index = this.tasks.findIndex((item) => item.id === task.id);
      if (index !== -1) {
        this.tasks[index].enabled = e.detail.value;
      }
      common_vendor.index.showToast({
        title: e.detail.value ? `${task.name}已启用` : `${task.name}已禁用`,
        icon: "none"
      });
    },
    addTask() {
      common_vendor.index.showToast({
        title: "添加任务功能开发中",
        icon: "none"
      });
    },
    editCoupon(coupon) {
      common_vendor.index.showToast({
        title: "编辑卡券功能开发中",
        icon: "none"
      });
    },
    deleteCoupon(coupon) {
      common_vendor.index.showModal({
        title: "删除确认",
        content: `确定要删除"${coupon.name}"吗？`,
        success: (res) => {
          if (res.confirm) {
            const index = this.coupons.findIndex((item) => item.id === coupon.id);
            if (index !== -1) {
              this.coupons.splice(index, 1);
              common_vendor.index.showToast({
                title: "删除成功",
                icon: "success"
              });
            }
          }
        }
      });
    },
    addCoupon() {
      common_vendor.index.showToast({
        title: "添加卡券功能开发中",
        icon: "none"
      });
    },
    editActivity(activity) {
      common_vendor.index.showToast({
        title: "编辑活动功能开发中",
        icon: "none"
      });
    },
    deleteActivity(activity) {
      common_vendor.index.showModal({
        title: "删除确认",
        content: `确定要删除"${activity.name}"吗？`,
        success: (res) => {
          if (res.confirm) {
            const index = this.activities.findIndex((item) => item.id === activity.id);
            if (index !== -1) {
              this.activities.splice(index, 1);
              common_vendor.index.showToast({
                title: "删除成功",
                icon: "success"
              });
            }
          }
        }
      });
    },
    addActivity() {
      common_vendor.index.showToast({
        title: "添加活动功能开发中",
        icon: "none"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.t($data.toolInfo.name || "会员工具"),
    c: $data.toolInfo
  }, $data.toolInfo ? common_vendor.e({
    d: $data.toolInfo.icon,
    e: $data.toolInfo.color,
    f: common_vendor.t($data.toolInfo.name),
    g: common_vendor.t($data.toolInfo.description),
    h: $data.toolId === "1"
  }, $data.toolId === "1" ? {
    i: $data.pointsSettings.ratio,
    j: common_vendor.o(($event) => $data.pointsSettings.ratio = $event.detail.value),
    k: $data.pointsSettings.validity,
    l: common_vendor.o(($event) => $data.pointsSettings.validity = $event.detail.value),
    m: $data.pointsSettings.discount,
    n: common_vendor.o(($event) => $data.pointsSettings.discount = $event.detail.value),
    o: common_vendor.o((...args) => $options.saveSettings && $options.saveSettings(...args))
  } : $data.toolId === "2" ? {
    q: common_vendor.f($data.tasks, (task, index, i0) => {
      return {
        a: common_vendor.t(task.name),
        b: common_vendor.t(task.reward),
        c: task.enabled,
        d: common_vendor.o((e) => $options.toggleTask(task, e), index),
        e: index
      };
    }),
    r: common_vendor.o((...args) => $options.addTask && $options.addTask(...args))
  } : $data.toolId === "3" ? {
    t: common_vendor.f($data.coupons, (coupon, index, i0) => {
      return {
        a: common_vendor.t(coupon.name),
        b: common_vendor.t(coupon.value),
        c: common_vendor.t(coupon.validity),
        d: common_vendor.o(($event) => $options.editCoupon(coupon), index),
        e: common_vendor.o(($event) => $options.deleteCoupon(coupon), index),
        f: index
      };
    }),
    v: common_vendor.o((...args) => $options.addCoupon && $options.addCoupon(...args))
  } : $data.toolId === "4" ? {
    x: common_vendor.f($data.activities, (activity, index, i0) => {
      return {
        a: common_vendor.t(activity.name),
        b: common_vendor.t(activity.startTime),
        c: common_vendor.t(activity.endTime),
        d: common_vendor.t(activity.statusText),
        e: common_vendor.n(activity.status),
        f: common_vendor.o(($event) => $options.editActivity(activity), index),
        g: common_vendor.o(($event) => $options.deleteActivity(activity), index),
        h: index
      };
    }),
    y: common_vendor.o((...args) => $options.addActivity && $options.addActivity(...args))
  } : {
    z: common_assets._imports_0$46
  }, {
    p: $data.toolId === "2",
    s: $data.toolId === "3",
    w: $data.toolId === "4"
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/member/tool.js.map
