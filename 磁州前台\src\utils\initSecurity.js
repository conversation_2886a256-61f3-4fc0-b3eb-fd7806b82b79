/**
 * 安全初始化工具
 * 在应用启动时调用此文件初始化所有安全相关功能
 */
import { registerRouteGuard } from './routeGuard';
import { createSecurityLog, SECURITY_EVENTS, LOG_LEVELS } from './securityAudit';
import { secureStorage } from './securityUtils';
import { SECURITY_HEADERS } from '@/config/securityConfig';

/**
 * 初始化安全功能
 */
export const initSecurity = () => {
  console.log('初始化安全功能...');
  
  try {
    // 注册路由守卫
    registerRouteGuard();
    
    // 设置安全头
    setSecurityHeaders();
    
    // 初始化会话安全
    initSessionSecurity();
    
    // 记录应用启动日志
    logAppStart();
    
    console.log('安全功能初始化完成');
    return true;
  } catch (error) {
    console.error('安全功能初始化失败', error);
    return false;
  }
};

/**
 * 设置安全头
 */
const setSecurityHeaders = () => {
  // 对于小程序环境，可能无法直接设置HTTP头
  // 这里可以在请求拦截器中设置
  console.log('设置安全头...');
};

/**
 * 初始化会话安全
 */
const initSessionSecurity = () => {
  console.log('初始化会话安全...');
  
  // 设置最后活动时间
  secureStorage.set('lastActivityTime', new Date().toISOString());
  
  // 检查是否存在旧的不安全存储
  const oldToken = uni.getStorageSync('token');
  if (oldToken) {
    // 迁移到安全存储
    secureStorage.set('auth_token', oldToken);
    uni.removeStorageSync('token');
  }
  
  // 设置会话活动监听器
  const updateLastActivity = () => {
    secureStorage.set('lastActivityTime', new Date().toISOString());
  };
  
  // 监听页面显示事件
  uni.onAppShow(() => {
    updateLastActivity();
  });
  
  // 监听网络请求
  const originalRequest = uni.request;
  uni.request = (options) => {
    updateLastActivity();
    return originalRequest(options);
  };
};

/**
 * 记录应用启动日志
 */
const logAppStart = () => {
  try {
    const systemInfo = uni.getSystemInfoSync();
    
    createSecurityLog(SECURITY_EVENTS.LOGIN_SUCCESS, {
      appVersion: systemInfo.appVersion,
      platform: systemInfo.platform,
      system: systemInfo.system,
      deviceBrand: systemInfo.brand,
      deviceModel: systemInfo.model,
      startTime: new Date().toISOString()
    }, LOG_LEVELS.INFO);
  } catch (error) {
    console.error('记录应用启动日志失败', error);
  }
};

export default {
  initSecurity
}; 