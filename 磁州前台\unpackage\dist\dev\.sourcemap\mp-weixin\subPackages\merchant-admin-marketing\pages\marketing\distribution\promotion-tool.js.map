{"version": 3, "file": "promotion-tool.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-tool.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xkaXN0cmlidXRpb25ccHJvbW90aW9uLXRvb2wudnVl"], "sourcesContent": ["<template>\n  <view class=\"promotion-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">推广海报</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 海报预览 -->\n    <view class=\"poster-preview-section\">\n      <view class=\"preview-card\" :style=\"{ backgroundColor: selectedTheme.bgColor }\">\n        <image class=\"poster-image\" :src=\"currentPoster\" mode=\"aspectFit\"></image>\n        <view class=\"preview-footer\">\n          <text class=\"preview-text\" :style=\"{ color: selectedTheme.textColor }\">预览效果</text>\n        </view>\n      </view>\n      \n      <view class=\"action-buttons\">\n        <button class=\"action-button save\" @click=\"savePoster\">\n          <view class=\"button-icon\">\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M7 10L12 15L17 10\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M12 15V3\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            </svg>\n          </view>\n          <text>保存到相册</text>\n        </button>\n        \n        <button class=\"action-button share\" @click=\"sharePoster\">\n          <view class=\"button-icon\">\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <circle cx=\"18\" cy=\"5\" r=\"3\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <circle cx=\"6\" cy=\"12\" r=\"3\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <circle cx=\"18\" cy=\"19\" r=\"3\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <line x1=\"8.59\" y1=\"13.51\" x2=\"15.42\" y2=\"17.49\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <line x1=\"15.41\" y1=\"6.51\" x2=\"8.59\" y2=\"10.49\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            </svg>\n          </view>\n          <text>分享海报</text>\n        </button>\n      </view>\n    </view>\n    \n    <!-- 自定义选项 -->\n    <view class=\"customization-section\">\n      <!-- 海报模板选择 -->\n      <view class=\"option-group\">\n        <text class=\"option-title\">海报模板</text>\n        <scroll-view scroll-x class=\"template-scroll\">\n          <view class=\"template-list\">\n            <view \n              v-for=\"(poster, index) in posterTemplates\" \n              :key=\"index\" \n              class=\"template-item\"\n              :class=\"{ active: currentPosterIndex === index }\"\n              @click=\"selectPoster(index)\"\n            >\n              <image class=\"template-thumb\" :src=\"poster.thumb\" mode=\"aspectFill\"></image>\n            </view>\n          </view>\n        </scroll-view>\n      </view>\n      \n      <!-- 主题颜色选择 -->\n      <view class=\"option-group\">\n        <text class=\"option-title\">主题颜色</text>\n        <view class=\"theme-options\">\n          <view \n            v-for=\"(theme, index) in themeColors\" \n            :key=\"index\" \n            class=\"theme-option\" \n            :class=\"{ active: selectedTheme === theme }\"\n            :style=\"{ backgroundColor: theme.bgColor }\"\n            @click=\"selectTheme(theme)\"\n          >\n            <view class=\"theme-check\" v-if=\"selectedTheme === theme\">\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M20 6L9 17L4 12\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              </svg>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 海报类型选择 -->\n      <view class=\"option-group\">\n        <text class=\"option-title\">海报类型</text>\n        <view class=\"type-options\">\n          <view \n            v-for=\"(type, index) in posterTypes\" \n            :key=\"index\" \n            class=\"type-option\" \n            :class=\"{ active: selectedType === type.value }\"\n            @click=\"selectType(type.value)\"\n          >\n            <view class=\"option-icon\" v-html=\"type.icon\"></view>\n            <text class=\"option-name\">{{type.name}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 其他选项 -->\n      <view class=\"option-group\">\n        <view class=\"toggle-option\">\n          <text class=\"toggle-label\">显示店铺LOGO</text>\n          <switch :checked=\"showLogo\" @change=\"toggleLogo\" color=\"#6B0FBE\" />\n        </view>\n        \n        <view class=\"toggle-option\">\n          <text class=\"toggle-label\">显示推广员ID</text>\n          <switch :checked=\"showId\" @change=\"toggleId\" color=\"#6B0FBE\" />\n        </view>\n        \n        <view class=\"toggle-option\">\n          <text class=\"toggle-label\">添加推广文案</text>\n          <switch :checked=\"showPromoText\" @change=\"togglePromoText\" color=\"#6B0FBE\" />\n        </view>\n      </view>\n      \n      <!-- 自定义文案 -->\n      <view class=\"option-group\" v-if=\"showPromoText\">\n        <text class=\"option-title\">自定义文案</text>\n        <input \n          class=\"custom-input\" \n          type=\"text\" \n          v-model=\"customText\" \n          placeholder=\"输入自定义文案（最多20字）\" \n          maxlength=\"20\"\n        />\n      </view>\n    </view>\n    \n    <!-- 历史记录 -->\n    <view class=\"history-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">历史记录</text>\n        <text class=\"clear-history\" @click=\"clearHistory\">清空</text>\n      </view>\n      \n      <scroll-view class=\"history-scroll\" scroll-x>\n        <view class=\"history-list\">\n          <view \n            v-for=\"(item, index) in historyPosters\" \n            :key=\"index\" \n            class=\"history-item\"\n            @click=\"loadHistoryPoster(item)\"\n          >\n            <image class=\"history-image\" :src=\"item.image\" mode=\"aspectFill\"></image>\n            <text class=\"history-date\">{{item.date}}</text>\n          </view>\n        </view>\n      </scroll-view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, computed, onMounted } from 'vue';\nimport promotionService from '@/utils/promotionService';\n\n// 推广参数\nconst promotionParams = reactive({\n  type: 'product', // 默认类型\n  id: '',\n  title: '',\n  image: '',\n  extraParams: {} // 存储额外参数\n});\n\n// 店铺信息\nconst storeInfo = reactive({\n  name: '磁州同城生活',\n  slogan: '本地生活好物优选',\n  logo: '/static/images/logo.png'\n});\n\n// 分销员ID\nconst distributorId = ref('D88652');\n\n// 海报模板\nconst posterTemplates = ref([]);\n\n// 默认海报模板\nconst defaultTemplates = [\n  {\n    name: '商品推广',\n    thumb: '/static/images/distribution/poster-thumb-1.png',\n    url: '/static/images/distribution/poster-1.png',\n    type: 'product'\n  },\n  {\n    name: '店铺推广',\n    thumb: '/static/images/distribution/poster-thumb-2.png',\n    url: '/static/images/distribution/poster-2.png',\n    type: 'store'\n  },\n  {\n    name: '活动推广',\n    thumb: '/static/images/distribution/poster-thumb-3.png',\n    url: '/static/images/distribution/poster-3.png',\n    type: 'activity'\n  },\n  {\n    name: '会员推广',\n    thumb: '/static/images/distribution/poster-thumb-4.png',\n    url: '/static/images/distribution/poster-4.png',\n    type: 'member'\n  },\n  {\n    name: '节日主题',\n    thumb: '/static/images/distribution/poster-thumb-5.png',\n    url: '/static/images/distribution/poster-5.png',\n    type: 'general'\n  }\n];\n\n// 特定类型的模板\nconst typeTemplates = {\n  'carpool': [\n    {\n      name: '拼车专用',\n      thumb: '/static/images/distribution/carpool-thumb-1.png',\n      url: '/static/images/distribution/carpool-1.png',\n      type: 'carpool'\n    }\n  ],\n  'secondhand': [\n    {\n      name: '二手商品专用',\n      thumb: '/static/images/distribution/secondhand-thumb-1.png',\n      url: '/static/images/distribution/secondhand-1.png',\n      type: 'secondhand'\n    }\n  ],\n  'house': [\n    {\n      name: '房屋租售专用',\n      thumb: '/static/images/distribution/house-thumb-1.png',\n      url: '/static/images/distribution/house-1.png',\n      type: 'house'\n    }\n  ],\n  'service': [\n    {\n      name: '服务推广专用',\n      thumb: '/static/images/distribution/service-thumb-1.png',\n      url: '/static/images/distribution/service-1.png',\n      type: 'service'\n    }\n  ],\n  'community': [\n    {\n      name: '社区信息专用',\n      thumb: '/static/images/distribution/community-thumb-1.png',\n      url: '/static/images/distribution/community-1.png',\n      type: 'community'\n    }\n  ]\n};\n\n// 历史记录\nconst historyPosters = reactive([\n  { image: '/static/images/distribution/poster-1.png', date: '2023-04-25' },\n  { image: '/static/images/distribution/poster-2.png', date: '2023-04-20' },\n  { image: '/static/images/distribution/poster-3.png', date: '2023-04-15' },\n  { image: '/static/images/distribution/poster-4.png', date: '2023-04-10' }\n]);\n\n// 海报类型\nconst posterTypes = [\n  {\n    name: '店铺',\n    value: 'store',\n    icon: '<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z\" stroke=\"#6B0FBE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M9 22V12H15V22\" stroke=\"#6B0FBE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>'\n  },\n  {\n    name: '商品',\n    value: 'product',\n    icon: '<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M20 7L12 3L4 7M20 7V17L12 21M20 7L12 11M12 21L4 17V7M12 21V11M4 7L12 11\" stroke=\"#6B0FBE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>'\n  },\n  {\n    name: '活动',\n    value: 'activity',\n    icon: '<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"#6B0FBE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>'\n  },\n  {\n    name: '会员',\n    value: 'member',\n    icon: '<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21\" stroke=\"#6B0FBE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z\" stroke=\"#6B0FBE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>'\n  }\n];\n\n// 主题颜色\nconst themeColors = [\n  { bgColor: '#FFFFFF', textColor: '#333333' },\n  { bgColor: '#6B0FBE', textColor: '#FFFFFF' },\n  { bgColor: '#A764CA', textColor: '#FFFFFF' },\n  { bgColor: '#F5F7FA', textColor: '#333333' },\n  { bgColor: '#FFE8F0', textColor: '#FF3B30' },\n  { bgColor: '#E8F8FF', textColor: '#007AFF' }\n];\n\n// 状态变量\nconst currentPosterIndex = ref(0);\nconst selectedType = ref('product');\nconst selectedTheme = ref(themeColors[0]);\nconst showLogo = ref(true);\nconst showId = ref(true);\nconst showPromoText = ref(false);\nconst customText = ref('');\n\n// 当前海报\nconst currentPoster = computed(() => {\n  if (posterTemplates.value.length === 0) return '';\n  return posterTemplates.value[currentPosterIndex.value].url;\n});\n\n// 页面加载\nonMounted(() => {\n  // 获取页面参数\n  const pages = getCurrentPages();\n  const currentPage = pages[pages.length - 1];\n  const options = currentPage.options || {};\n  \n  // 解析推广参数\n  parsePromotionParams(options);\n  \n  // 加载适合的海报模板\n  loadPosterTemplates();\n  \n  // 生成推广内容\n  generatePromotionContent();\n});\n\n// 解析推广参数\nconst parsePromotionParams = (options) => {\n  // 设置基本参数\n  promotionParams.type = options.type || 'product';\n  promotionParams.id = options.id || '';\n  promotionParams.title = options.title ? decodeURIComponent(options.title) : '';\n  promotionParams.image = options.image ? decodeURIComponent(options.image) : '';\n  \n  // 设置额外参数\n  const extraParams = {};\n  Object.keys(options).forEach(key => {\n    if (!['type', 'id', 'title', 'image'].includes(key)) {\n      extraParams[key] = decodeURIComponent(options[key] || '');\n    }\n  });\n  promotionParams.extraParams = extraParams;\n  \n  // 设置默认选中类型\n  selectedType.value = promotionParams.type;\n};\n\n// 加载海报模板\nconst loadPosterTemplates = () => {\n  // 获取特定类型的模板\n  const typeSpecificTemplates = typeTemplates[promotionParams.type] || [];\n  \n  // 合并模板\n  posterTemplates.value = [\n    ...typeSpecificTemplates,\n    ...defaultTemplates.filter(template => \n      template.type === 'general' || template.type === promotionParams.type\n    )\n  ];\n  \n  // 如果没有模板，使用默认模板\n  if (posterTemplates.value.length === 0) {\n    posterTemplates.value = defaultTemplates;\n  }\n};\n\n// 生成推广内容\nconst generatePromotionContent = () => {\n  // 根据不同类型生成不同的推广内容\n  switch(promotionParams.type) {\n    case 'carpool':\n      customText.value = `【${promotionParams.title}】${promotionParams.extraParams.departure}→${promotionParams.extraParams.destination}，出发时间：${promotionParams.extraParams.departureTime}，车费：¥${promotionParams.extraParams.price}，剩余座位：${promotionParams.extraParams.seats}个`;\n      break;\n      \n    case 'secondhand':\n      customText.value = `【${promotionParams.title}】${promotionParams.extraParams.condition || ''}，原价：¥${promotionParams.extraParams.originalPrice || ''}，现价：¥${promotionParams.extraParams.price || ''}`;\n      break;\n      \n    case 'house':\n      customText.value = `【${promotionParams.title}】${promotionParams.extraParams.location || ''}，${promotionParams.extraParams.area || ''}㎡，${promotionParams.extraParams.roomType || ''}，${promotionParams.extraParams.rentType || ''}：¥${promotionParams.extraParams.price || ''}`;\n      break;\n      \n    case 'service':\n      customText.value = `【${promotionParams.title}】${promotionParams.extraParams.category || ''}，${promotionParams.extraParams.merchantName || ''}提供，价格：¥${promotionParams.extraParams.price || ''}`;\n      break;\n      \n    case 'product':\n      customText.value = `【${promotionParams.title}】${promotionParams.extraParams.category || ''}，价格：¥${promotionParams.extraParams.price || ''}`;\n      break;\n      \n    case 'content':\n      customText.value = `【${promotionParams.title}】${promotionParams.extraParams.summary || ''}`;\n      break;\n      \n    case 'job':\n      customText.value = `【${promotionParams.title}】${promotionParams.extraParams.company || ''}招聘，薪资：${promotionParams.extraParams.salary || ''}，地点：${promotionParams.extraParams.location || ''}`;\n      break;\n      \n    case 'activity':\n      customText.value = `【${promotionParams.title}】时间：${promotionParams.extraParams.startTime || ''}，地点：${promotionParams.extraParams.location || ''}，主办方：${promotionParams.extraParams.organizer || ''}`;\n      break;\n      \n    case 'community':\n      customText.value = `【${promotionParams.title}】${promotionParams.extraParams.category || ''}，地点：${promotionParams.extraParams.location || ''}，联系方式：${promotionParams.extraParams.contact || ''}`;\n      break;\n      \n    default:\n      customText.value = `【${promotionParams.title}】`;\n  }\n  \n  // 显示推广文案\n  showPromoText.value = true;\n};\n\n// 选择海报模板\nconst selectPoster = (index) => {\n  currentPosterIndex.value = index;\n};\n\n// 选择主题颜色\nconst selectTheme = (theme) => {\n  selectedTheme.value = theme;\n};\n\n// 选择海报类型\nconst selectType = (type) => {\n  selectedType.value = type;\n};\n\n// 切换显示店铺LOGO\nconst toggleLogo = (e) => {\n  showLogo.value = e.detail.value;\n};\n\n// 切换显示推广员ID\nconst toggleId = (e) => {\n  showId.value = e.detail.value;\n};\n\n// 切换显示推广文案\nconst togglePromoText = (e) => {\n  showPromoText.value = e.detail.value;\n};\n\n// 保存海报到相册\nconst savePoster = () => {\n  uni.showLoading({\n    title: '保存中...'\n  });\n  \n  setTimeout(() => {\n    uni.hideLoading();\n    uni.showToast({\n      title: '保存成功',\n      icon: 'success'\n    });\n    \n    // 记录到历史记录\n    const now = new Date();\n    const dateStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;\n    historyPosters.unshift({\n      image: currentPoster.value,\n      date: dateStr\n    });\n  }, 1500);\n};\n\n// 分享海报\nconst sharePoster = () => {\n  uni.showActionSheet({\n    itemList: ['分享给朋友', '分享到朋友圈'],\n    success: (res) => {\n      uni.showToast({\n        title: '分享成功',\n        icon: 'success'\n      });\n      \n      // 记录分享事件\n      const shareType = res.tapIndex === 0 ? 'friend' : 'timeline';\n      logShareEvent(shareType);\n    }\n  });\n};\n\n// 记录分享事件\nconst logShareEvent = (shareType) => {\n  console.log('记录分享事件', {\n    type: promotionParams.type,\n    id: promotionParams.id,\n    shareType\n  });\n};\n\n// 加载历史海报\nconst loadHistoryPoster = (item) => {\n  // 实际应用中应该加载历史海报的配置\n    uni.showToast({\n    title: '加载历史海报',\n      icon: 'none'\n    });\n};\n\n// 清空历史记录\nconst clearHistory = () => {\n  uni.showModal({\n    title: '提示',\n    content: '确定要清空历史记录吗？',\n    success: (res) => {\n      if (res.confirm) {\n        historyPosters.splice(0, historyPosters.length);\n        uni.showToast({\n          title: '已清空',\n          icon: 'success'\n        });\n      }\n    }\n  });\n};\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n\n// 显示帮助\nconst showHelp = () => {\n  uni.showModal({\n    title: '推广海报使用帮助',\n    content: '选择喜欢的海报模板，自定义颜色和文案，生成精美海报进行分享推广。',\n    showCancel: false\n  });\n};\n</script>\n\n<style lang=\"scss\">\n.promotion-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: env(safe-area-inset-bottom);\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\n  color: #fff;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: bold;\n}\n\n/* 海报预览部分 */\n.poster-preview-section {\n  margin: 16px;\n}\n\n.preview-card {\n  background-color: #FFFFFF;\n  border-radius: 20px;\n  padding: 16px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  margin-bottom: 16px;\n  overflow: hidden;\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.poster-image {\n  width: 100%;\n  max-width: 300px;\n  height: 400px;\n  border-radius: 10px;\n}\n\n.preview-footer {\n  margin-top: 12px;\n  text-align: center;\n}\n\n.preview-text {\n  font-size: 14px;\n  color: #666;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 12px;\n}\n\n.action-button {\n  flex: 1;\n  height: 44px;\n  border-radius: 22px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: 500;\n  border: none;\n}\n\n.action-button.save {\n  background: linear-gradient(135deg, #34C759, #32D74B);\n  color: #FFFFFF;\n}\n\n.action-button.share {\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\n  color: #FFFFFF;\n}\n\n.button-icon {\n  margin-right: 6px;\n}\n\n/* 自定义选项部分 */\n.customization-section {\n  margin: 16px;\n  background-color: #FFFFFF;\n  border-radius: 20px;\n  padding: 20px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);\n}\n\n.option-group {\n  margin-bottom: 20px;\n}\n\n.option-group:last-child {\n  margin-bottom: 0;\n}\n\n.option-title {\n  font-size: 15px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 12px;\n  display: block;\n}\n\n.template-scroll {\n  width: 100%;\n  white-space: nowrap;\n}\n\n.template-list {\n  display: flex;\n  padding-bottom: 8px;\n}\n\n.template-item {\n  width: 80px;\n  height: 120px;\n  margin-right: 12px;\n  border-radius: 10px;\n  overflow: hidden;\n  border: 2px solid transparent;\n  position: relative;\n  transition: all 0.3s ease;\n  flex-shrink: 0;\n}\n\n.template-item.active {\n  border-color: #6B0FBE;\n  transform: scale(1.05);\n}\n\n.template-thumb {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.type-options {\n  display: flex;\n  justify-content: space-between;\n}\n\n.type-option {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 12px 0;\n  border-radius: 12px;\n  transition: all 0.3s ease;\n}\n\n.type-option.active {\n  background-color: rgba(107, 15, 190, 0.05);\n}\n\n.option-icon {\n  margin-bottom: 8px;\n}\n\n.option-name {\n  font-size: 12px;\n  color: #666;\n}\n\n.type-option.active .option-name {\n  color: #6B0FBE;\n  font-weight: 500;\n}\n\n.theme-options {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12px;\n}\n\n.theme-option {\n  width: 40px;\n  height: 40px;\n  border-radius: 20px;\n  border: 2px solid transparent;\n  position: relative;\n  transition: all 0.3s ease;\n}\n\n.theme-option.active {\n  border-color: #6B0FBE;\n  transform: scale(1.05);\n  box-shadow: 0 2px 8px rgba(107, 15, 190, 0.2);\n}\n\n.theme-check {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n\n.toggle-option {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid #F0F0F0;\n}\n\n.toggle-option:last-child {\n  border-bottom: none;\n}\n\n.toggle-label {\n  font-size: 14px;\n  color: #333;\n}\n\n.custom-input {\n  width: 100%;\n  height: 44px;\n  background-color: #F5F7FA;\n  border-radius: 12px;\n  padding: 0 16px;\n  font-size: 14px;\n  color: #333;\n  border: 1px solid transparent;\n}\n\n.custom-input:focus {\n  border-color: #6B0FBE;\n  background-color: #FFFFFF;\n}\n\n/* 历史记录部分 */\n.history-section {\n  margin: 16px;\n  background-color: #FFFFFF;\n  border-radius: 20px;\n  padding: 20px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);\n  margin-bottom: 32px;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.section-title {\n  font-size: 15px;\n  font-weight: 600;\n  color: #333;\n}\n\n.clear-history {\n  font-size: 14px;\n  color: #FF3B30;\n}\n\n.history-scroll {\n  width: 100%;\n}\n\n.history-list {\n  display: flex;\n  padding-bottom: 8px;\n}\n\n.history-item {\n  width: 80px;\n  margin-right: 12px;\n  flex-shrink: 0;\n}\n\n.history-item:last-child {\n  margin-right: 0;\n}\n\n.history-image {\n  width: 80px;\n  height: 120px;\n  border-radius: 10px;\n  border: 1px solid #EEEEEE;\n  margin-bottom: 6px;\n}\n\n.history-date {\n  font-size: 10px;\n  color: #999;\n  text-align: center;\n  display: block;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-tool.vue'\nwx.createPage(MiniProgramPage)"], "names": ["reactive", "ref", "computed", "onMounted", "uni", "MiniProgramPage"], "mappings": ";;;;;;;;;;;;AAuKA,UAAM,kBAAkBA,cAAAA,SAAS;AAAA,MAC/B,MAAM;AAAA;AAAA,MACN,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,aAAa,CAAE;AAAA;AAAA,IACjB,CAAC;AAGiBA,kBAAAA,SAAS;AAAA,MACzB,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAGqBC,kBAAG,IAAC,QAAQ;AAGlC,UAAM,kBAAkBA,cAAAA,IAAI,CAAA,CAAE;AAG9B,UAAM,mBAAmB;AAAA,MACvB;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,QACP,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,QACP,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,QACP,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,QACP,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,QACP,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACH;AAGA,UAAM,gBAAgB;AAAA,MACpB,WAAW;AAAA,QACT;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACF;AAAA,MACD,cAAc;AAAA,QACZ;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACF;AAAA,MACD,SAAS;AAAA,QACP;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACF;AAAA,MACD,WAAW;AAAA,QACT;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACF;AAAA,MACD,aAAa;AAAA,QACX;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACF;AAAA,IACH;AAGA,UAAM,iBAAiBD,cAAAA,SAAS;AAAA,MAC9B,EAAE,OAAO,4CAA4C,MAAM,aAAc;AAAA,MACzE,EAAE,OAAO,4CAA4C,MAAM,aAAc;AAAA,MACzE,EAAE,OAAO,4CAA4C,MAAM,aAAc;AAAA,MACzE,EAAE,OAAO,4CAA4C,MAAM,aAAc;AAAA,IAC3E,CAAC;AAGD,UAAM,cAAc;AAAA,MAClB;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACP;AAAA,IACH;AAGA,UAAM,cAAc;AAAA,MAClB,EAAE,SAAS,WAAW,WAAW,UAAW;AAAA,MAC5C,EAAE,SAAS,WAAW,WAAW,UAAW;AAAA,MAC5C,EAAE,SAAS,WAAW,WAAW,UAAW;AAAA,MAC5C,EAAE,SAAS,WAAW,WAAW,UAAW;AAAA,MAC5C,EAAE,SAAS,WAAW,WAAW,UAAW;AAAA,MAC5C,EAAE,SAAS,WAAW,WAAW,UAAW;AAAA,IAC9C;AAGA,UAAM,qBAAqBC,cAAAA,IAAI,CAAC;AAChC,UAAM,eAAeA,cAAAA,IAAI,SAAS;AAClC,UAAM,gBAAgBA,cAAG,IAAC,YAAY,CAAC,CAAC;AACxC,UAAM,WAAWA,cAAAA,IAAI,IAAI;AACzB,UAAM,SAASA,cAAAA,IAAI,IAAI;AACvB,UAAM,gBAAgBA,cAAAA,IAAI,KAAK;AAC/B,UAAM,aAAaA,cAAAA,IAAI,EAAE;AAGzB,UAAM,gBAAgBC,cAAQ,SAAC,MAAM;AACnC,UAAI,gBAAgB,MAAM,WAAW;AAAG,eAAO;AAC/C,aAAO,gBAAgB,MAAM,mBAAmB,KAAK,EAAE;AAAA,IACzD,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AAEd,YAAM,QAAQ;AACd,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,UAAU,YAAY,WAAW;AAGvC,2BAAqB,OAAO;AAG5B;AAGA;IACF,CAAC;AAGD,UAAM,uBAAuB,CAAC,YAAY;AAExC,sBAAgB,OAAO,QAAQ,QAAQ;AACvC,sBAAgB,KAAK,QAAQ,MAAM;AACnC,sBAAgB,QAAQ,QAAQ,QAAQ,mBAAmB,QAAQ,KAAK,IAAI;AAC5E,sBAAgB,QAAQ,QAAQ,QAAQ,mBAAmB,QAAQ,KAAK,IAAI;AAG5E,YAAM,cAAc,CAAA;AACpB,aAAO,KAAK,OAAO,EAAE,QAAQ,SAAO;AAClC,YAAI,CAAC,CAAC,QAAQ,MAAM,SAAS,OAAO,EAAE,SAAS,GAAG,GAAG;AACnD,sBAAY,GAAG,IAAI,mBAAmB,QAAQ,GAAG,KAAK,EAAE;AAAA,QACzD;AAAA,MACL,CAAG;AACD,sBAAgB,cAAc;AAG9B,mBAAa,QAAQ,gBAAgB;AAAA,IACvC;AAGA,UAAM,sBAAsB,MAAM;AAEhC,YAAM,wBAAwB,cAAc,gBAAgB,IAAI,KAAK,CAAA;AAGrE,sBAAgB,QAAQ;AAAA,QACtB,GAAG;AAAA,QACH,GAAG,iBAAiB;AAAA,UAAO,cACzB,SAAS,SAAS,aAAa,SAAS,SAAS,gBAAgB;AAAA,QAClE;AAAA,MACL;AAGE,UAAI,gBAAgB,MAAM,WAAW,GAAG;AACtC,wBAAgB,QAAQ;AAAA,MACzB;AAAA,IACH;AAGA,UAAM,2BAA2B,MAAM;AAErC,cAAO,gBAAgB,MAAI;AAAA,QACzB,KAAK;AACH,qBAAW,QAAQ,IAAI,gBAAgB,KAAK,IAAI,gBAAgB,YAAY,SAAS,IAAI,gBAAgB,YAAY,WAAW,SAAS,gBAAgB,YAAY,aAAa,QAAQ,gBAAgB,YAAY,KAAK,SAAS,gBAAgB,YAAY,KAAK;AACrQ;AAAA,QAEF,KAAK;AACH,qBAAW,QAAQ,IAAI,gBAAgB,KAAK,IAAI,gBAAgB,YAAY,aAAa,EAAE,QAAQ,gBAAgB,YAAY,iBAAiB,EAAE,QAAQ,gBAAgB,YAAY,SAAS,EAAE;AACjM;AAAA,QAEF,KAAK;AACH,qBAAW,QAAQ,IAAI,gBAAgB,KAAK,IAAI,gBAAgB,YAAY,YAAY,EAAE,IAAI,gBAAgB,YAAY,QAAQ,EAAE,KAAK,gBAAgB,YAAY,YAAY,EAAE,IAAI,gBAAgB,YAAY,YAAY,EAAE,KAAK,gBAAgB,YAAY,SAAS,EAAE;AAC7Q;AAAA,QAEF,KAAK;AACH,qBAAW,QAAQ,IAAI,gBAAgB,KAAK,IAAI,gBAAgB,YAAY,YAAY,EAAE,IAAI,gBAAgB,YAAY,gBAAgB,EAAE,UAAU,gBAAgB,YAAY,SAAS,EAAE;AAC7L;AAAA,QAEF,KAAK;AACH,qBAAW,QAAQ,IAAI,gBAAgB,KAAK,IAAI,gBAAgB,YAAY,YAAY,EAAE,QAAQ,gBAAgB,YAAY,SAAS,EAAE;AACzI;AAAA,QAEF,KAAK;AACH,qBAAW,QAAQ,IAAI,gBAAgB,KAAK,IAAI,gBAAgB,YAAY,WAAW,EAAE;AACzF;AAAA,QAEF,KAAK;AACH,qBAAW,QAAQ,IAAI,gBAAgB,KAAK,IAAI,gBAAgB,YAAY,WAAW,EAAE,SAAS,gBAAgB,YAAY,UAAU,EAAE,OAAO,gBAAgB,YAAY,YAAY,EAAE;AAC3L;AAAA,QAEF,KAAK;AACH,qBAAW,QAAQ,IAAI,gBAAgB,KAAK,OAAO,gBAAgB,YAAY,aAAa,EAAE,OAAO,gBAAgB,YAAY,YAAY,EAAE,QAAQ,gBAAgB,YAAY,aAAa,EAAE;AAClM;AAAA,QAEF,KAAK;AACH,qBAAW,QAAQ,IAAI,gBAAgB,KAAK,IAAI,gBAAgB,YAAY,YAAY,EAAE,OAAO,gBAAgB,YAAY,YAAY,EAAE,SAAS,gBAAgB,YAAY,WAAW,EAAE;AAC7L;AAAA,QAEF;AACE,qBAAW,QAAQ,IAAI,gBAAgB,KAAK;AAAA,MAC/C;AAGD,oBAAc,QAAQ;AAAA,IACxB;AAGA,UAAM,eAAe,CAAC,UAAU;AAC9B,yBAAmB,QAAQ;AAAA,IAC7B;AAGA,UAAM,cAAc,CAAC,UAAU;AAC7B,oBAAc,QAAQ;AAAA,IACxB;AAGA,UAAM,aAAa,CAAC,SAAS;AAC3B,mBAAa,QAAQ;AAAA,IACvB;AAGA,UAAM,aAAa,CAAC,MAAM;AACxB,eAAS,QAAQ,EAAE,OAAO;AAAA,IAC5B;AAGA,UAAM,WAAW,CAAC,MAAM;AACtB,aAAO,QAAQ,EAAE,OAAO;AAAA,IAC1B;AAGA,UAAM,kBAAkB,CAAC,MAAM;AAC7B,oBAAc,QAAQ,EAAE,OAAO;AAAA,IACjC;AAGA,UAAM,aAAa,MAAM;AACvBC,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAGD,cAAM,MAAM,oBAAI;AAChB,cAAM,UAAU,GAAG,IAAI,YAAa,CAAA,IAAI,OAAO,IAAI,SAAU,IAAG,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,IAAI,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC;AAC7H,uBAAe,QAAQ;AAAA,UACrB,OAAO,cAAc;AAAA,UACrB,MAAM;AAAA,QACZ,CAAK;AAAA,MACF,GAAE,IAAI;AAAA,IACT;AAGA,UAAM,cAAc,MAAM;AACxBA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,SAAS,QAAQ;AAAA,QAC5B,SAAS,CAAC,QAAQ;AAChBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAGD,gBAAM,YAAY,IAAI,aAAa,IAAI,WAAW;AAClD,wBAAc,SAAS;AAAA,QACxB;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,CAAC,cAAc;AACnCA,oBAAAA,MAAA,MAAA,OAAA,+FAAY,UAAU;AAAA,QACpB,MAAM,gBAAgB;AAAA,QACtB,IAAI,gBAAgB;AAAA,QACpB;AAAA,MACJ,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,CAAC,SAAS;AAEhCA,oBAAAA,MAAI,UAAU;AAAA,QACd,OAAO;AAAA,QACL,MAAM;AAAA,MACZ,CAAK;AAAA,IACL;AAGA,UAAM,eAAe,MAAM;AACzBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,2BAAe,OAAO,GAAG,eAAe,MAAM;AAC9CA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MAChB,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChiBA,GAAG,WAAWC,SAAe;"}