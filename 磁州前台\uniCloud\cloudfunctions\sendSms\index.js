'use strict';

const crypto = require('crypto');
const Core = require('@alicloud/pop-core');

// 阿里云短信配置，建议使用环境变量存储或云函数环境配置
const config = {
  accessKeyId: process.env.ALI_SMS_ACCESS_KEY_ID || 'YOUR_ACCESS_KEY_ID', // 替换为实际的accessKeyId
  accessKeySecret: process.env.ALI_SMS_ACCESS_KEY_SECRET || 'YOUR_ACCESS_KEY_SECRET', // 替换为实际的accessKeySecret
  endpoint: 'https://dysmsapi.aliyuncs.com',
  apiVersion: '2017-05-25',
  signName: process.env.ALI_SMS_SIGN_NAME || '同城生活网', // 替换为您的签名
  templateCode: process.env.ALI_SMS_TEMPLATE_CODE || 'SMS_123456789', // 替换为您的模板CODE
  regionId: 'cn-hangzhou'
};

/**
 * 调用阿里云短信服务发送短信
 * @param {String} phoneNumber 手机号
 * @param {String} code 验证码
 * @param {String} templateParam 短信模板参数，JSON字符串
 * @returns {Promise} 发送结果
 */
async function sendAliSms(phoneNumber, code, templateParam) {
  // 创建阿里云短信客户端
  const client = new Core({
    accessKeyId: config.accessKeyId,
    accessKeySecret: config.accessKeySecret,
    endpoint: config.endpoint,
    apiVersion: config.apiVersion,
    regionId: config.regionId
  });
  
  // 请求参数
  const params = {
    PhoneNumbers: phoneNumber,
    SignName: config.signName,
    TemplateCode: config.templateCode,
    TemplateParam: templateParam
  };
  
  // 请求选项
  const requestOption = {
    method: 'POST'
  };
  
  try {
    // 发送短信
    const result = await client.request('SendSms', params, requestOption);
    console.log('阿里云短信发送结果:', result);
    
    // 判断是否发送成功
    if (result.Code === 'OK') {
      return {
        success: true,
        message: '短信发送成功',
        requestId: result.RequestId
      };
    } else {
      return {
        success: false,
        message: `短信发送失败: ${result.Message}`,
        code: result.Code,
        requestId: result.RequestId
      };
    }
  } catch (error) {
    console.error('阿里云短信发送异常:', error);
    return {
      success: false,
      message: `短信发送异常: ${error.message || '未知错误'}`,
      code: error.code || 'UNKNOWN_ERROR'
    };
  }
}

/**
 * 验证请求来源是否合法（简易验证，实际项目中应使用更安全的方式）
 * @param {Object} context 云函数上下文
 * @returns {Boolean} 验证结果
 */
function validateRequest(context) {
  // 获取请求来源
  const source = context.CLIENTIP || '';
  
  // 判断请求来源是否在白名单中（示例，实际使用中需完善）
  // 本地开发环境放行
  if (process.env.NODE_ENV === 'development') {
    return true;
  }
  
  // 这里可以添加更多验证逻辑，例如检查请求签名、检查请求频率等
  
  return true; // 示例中默认放行，实际使用中需完善
}

/**
 * 检查请求参数
 * @param {Object} event 请求参数
 * @returns {Object} 检查结果
 */
function checkParams(event) {
  const { phone, code, templateParam } = event;
  
  // 检查手机号
  if (!phone) {
    return {
      valid: false,
      message: '手机号不能为空'
    };
  }
  
  // 检查手机号格式
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(phone)) {
    return {
      valid: false,
      message: '手机号格式不正确'
    };
  }
  
  // 检查验证码
  if (!code) {
    return {
      valid: false,
      message: '验证码不能为空'
    };
  }
  
  // 检查验证码格式
  if (!/^\d{6}$/.test(code)) {
    return {
      valid: false,
      message: '验证码格式不正确'
    };
  }
  
  // 检查模板参数
  if (!templateParam) {
    return {
      valid: false,
      message: '模板参数不能为空'
    };
  }
  
  try {
    // 尝试解析JSON
    JSON.parse(templateParam);
  } catch (e) {
    return {
      valid: false,
      message: '模板参数格式不正确'
    };
  }
  
  return {
    valid: true
  };
}

/**
 * 云函数入口
 * @param {Object} event 请求参数
 * @param {Object} context 云函数上下文
 * @returns {Object} 处理结果
 */
exports.main = async (event, context) => {
  console.log('短信发送请求:', event);
  
  // 验证请求来源
  if (!validateRequest(context)) {
    return {
      success: false,
      message: '请求来源不合法'
    };
  }
  
  // 检查参数
  const checkResult = checkParams(event);
  if (!checkResult.valid) {
    return {
      success: false,
      message: checkResult.message
    };
  }
  
  const { phone, code, templateParam } = event;
  
  // 发送短信
  try {
    const result = await sendAliSms(phone, code, templateParam);
    return result;
  } catch (error) {
    console.error('短信发送处理异常:', error);
    return {
      success: false,
      message: `短信发送处理异常: ${error.message || '未知错误'}`
    };
  }
}; 