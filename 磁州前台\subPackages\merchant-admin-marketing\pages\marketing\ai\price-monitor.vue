<template>
  <view class="price-monitor-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">竞品价格监测</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 页面内容 -->
    <scroll-view scroll-y class="page-content">
      <!-- 概览卡片 -->
      <view class="overview-card">
        <view class="overview-header">
          <text class="overview-title">价格监测概览</text>
          <view class="date-filter">
            <text class="date-text">最近7天</text>
            <view class="filter-icon"></view>
          </view>
        </view>
        <view class="overview-stats">
          <view class="stat-item">
            <text class="stat-value">28</text>
            <text class="stat-label">监测商品</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">12</text>
            <text class="stat-label">价格波动</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">5%</text>
            <text class="stat-label">平均价差</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">3</text>
            <text class="stat-label">需调整</text>
          </view>
        </view>
      </view>
      
      <!-- 价格预警 -->
      <view class="alert-section">
        <view class="section-header">
          <text class="section-title">价格预警</text>
          <text class="view-all">查看全部</text>
        </view>
        <view class="alert-list">
          <view class="alert-item">
            <view class="alert-icon warning"></view>
            <view class="alert-content">
              <text class="alert-title">竞品降价提醒</text>
              <text class="alert-desc">"香奈儿5号香水"在3家竞品店铺平均降价12%，建议调整价格</text>
              <view class="alert-meta">
                <text class="meta-time">2小时前</text>
                <text class="meta-tag">高优先级</text>
              </view>
            </view>
            <view class="alert-action" @click="handleAlert(1)">处理</view>
          </view>
          
          <view class="alert-item">
            <view class="alert-icon info"></view>
            <view class="alert-content">
              <text class="alert-title">竞争优势提醒</text>
              <text class="alert-desc">"SK-II精华液"您的价格比竞品低8%，建议适当提价增加利润</text>
              <view class="alert-meta">
                <text class="meta-time">5小时前</text>
                <text class="meta-tag">中优先级</text>
              </view>
            </view>
            <view class="alert-action" @click="handleAlert(2)">处理</view>
          </view>
          
          <view class="alert-item">
            <view class="alert-icon danger"></view>
            <view class="alert-content">
              <text class="alert-title">库存策略提醒</text>
              <text class="alert-desc">"APPLE AirPods Pro"价格下滑趋势明显，建议控制库存</text>
              <view class="alert-meta">
                <text class="meta-time">1天前</text>
                <text class="meta-tag">高优先级</text>
              </view>
            </view>
            <view class="alert-action" @click="handleAlert(3)">处理</view>
          </view>
        </view>
      </view>
      
      <!-- 竞品价格对比 -->
      <view class="compare-section">
        <view class="section-header">
          <text class="section-title">竞品价格对比</text>
          <view class="filter-dropdown">
            <text class="selected-category">美妆护肤</text>
            <view class="dropdown-icon"></view>
          </view>
        </view>
        
        <view class="product-list">
          <view class="product-item" v-for="(product, index) in products" :key="index" @click="viewProductDetail(product)">
            <image class="product-image" :src="product.image" mode="aspectFill"></image>
            <view class="product-info">
              <text class="product-name">{{product.name}}</text>
              <view class="price-compare">
                <text class="your-price">￥{{product.yourPrice}}</text>
                <text class="price-divider">vs</text>
                <text class="market-price">￥{{product.marketPrice}}</text>
              </view>
              <view class="price-diff" :class="getPriceDiffClass(product)">
                <text class="diff-value">{{getPriceDiffText(product)}}</text>
              </view>
            </view>
            <view class="action-icon"></view>
          </view>
        </view>
      </view>
      
      <!-- 价格趋势图 -->
      <view class="trends-section">
        <view class="section-header">
          <text class="section-title">行业价格趋势</text>
          <view class="trend-tabs">
            <text 
              v-for="(tab, index) in trendTabs" 
              :key="index" 
              :class="['tab-item', currentTrendTab === index ? 'active' : '']"
              @click="switchTrendTab(index)">
              {{tab}}
            </text>
          </view>
        </view>
        
        <view class="trend-chart">
          <!-- 简化的图表，实际项目中应该使用echarts等库 -->
          <view class="chart-container">
            <view class="chart-line your-price"></view>
            <view class="chart-line market-avg"></view>
            <view class="chart-line lowest-price"></view>
          </view>
          <view class="chart-legend">
            <view class="legend-item">
              <view class="legend-color your-price"></view>
              <text class="legend-text">您的价格</text>
            </view>
            <view class="legend-item">
              <view class="legend-color market-avg"></view>
              <text class="legend-text">市场均价</text>
            </view>
            <view class="legend-item">
              <view class="legend-color lowest-price"></view>
              <text class="legend-text">最低价</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 价格优化建议 -->
      <view class="recommendations-section">
        <view class="section-header">
          <text class="section-title">价格优化建议</text>
          <view class="ai-badge">AI</view>
        </view>
        
        <view class="recommendation-list">
          <view class="recommendation-item">
            <view class="recommendation-icon price-up"></view>
            <view class="recommendation-content">
              <text class="recommendation-title">提高价格</text>
              <text class="recommendation-desc">基于竞品分析，"SK-II精华液"可提价5%至￥1350，预计仍可保持销量</text>
              <view class="recommendation-actions">
                <text class="action apply" @click="applyRecommendation(1)">应用</text>
                <text class="action ignore" @click="ignoreRecommendation(1)">忽略</text>
              </view>
            </view>
          </view>
          
          <view class="recommendation-item">
            <view class="recommendation-icon price-down"></view>
            <view class="recommendation-content">
              <text class="recommendation-title">降低价格</text>
              <text class="recommendation-desc">建议"香奈儿5号香水"降价8%至￥850，以提高竞争力，预计销量增长15%</text>
              <view class="recommendation-actions">
                <text class="action apply" @click="applyRecommendation(2)">应用</text>
                <text class="action ignore" @click="ignoreRecommendation(2)">忽略</text>
              </view>
            </view>
          </view>
          
          <view class="recommendation-item">
            <view class="recommendation-icon promotion"></view>
            <view class="recommendation-content">
              <text class="recommendation-title">营销策略</text>
              <text class="recommendation-desc">针对竞品降价商品，建议采用满赠策略代替直降价格，预计可保持利润率</text>
              <view class="recommendation-actions">
                <text class="action apply" @click="applyRecommendation(3)">应用</text>
                <text class="action ignore" @click="ignoreRecommendation(3)">忽略</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 底部空间 -->
      <view style="height: 20px;"></view>
    </scroll-view>
    
    <!-- 底部导航栏 -->
    <tab-bar active-tab="marketing" @tab-change="handleTabChange"></tab-bar>
  </view>
</template>

<script>
import TabBar from '../../../../../components/TabBar.vue'

export default {
  components: {
    TabBar
  },
  data() {
    return {
      trendTabs: ['7天', '30天', '90天', '全年'],
      currentTrendTab: 0,
      products: [
        {
          name: 'SK-II 青春精华露 230ml',
          image: '/static/images/product-1.png',
          yourPrice: 1280,
          marketPrice: 1380,
          change: -7.2
        },
        {
          name: '香奈儿 5号香水 100ml',
          image: '/static/images/product-2.png',
          yourPrice: 920,
          marketPrice: 850,
          change: 8.2
        },
        {
          name: 'APPLE AirPods Pro',
          image: '/static/images/product-3.png',
          yourPrice: 1599,
          marketPrice: 1499,
          change: 6.7
        },
        {
          name: '兰蔻小黑瓶精华 50ml',
          image: '/static/images/product-4.png',
          yourPrice: 890,
          marketPrice: 880,
          change: 1.1
        }
      ]
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showHelp() {
      uni.showToast({
        title: '竞品价格监测帮助',
        icon: 'none'
      });
    },
    switchTrendTab(index) {
      this.currentTrendTab = index;
    },
    getPriceDiffClass(product) {
      if (product.change > 0) {
        return 'higher';
      } else if (product.change < 0) {
        return 'lower';
      } else {
        return 'equal';
      }
    },
    getPriceDiffText(product) {
      if (product.change > 0) {
        return `高${Math.abs(product.change).toFixed(1)}%`;
      } else if (product.change < 0) {
        return `低${Math.abs(product.change).toFixed(1)}%`;
      } else {
        return '持平';
      }
    },
    viewProductDetail(product) {
      uni.showToast({
        title: `查看${product.name}详情`,
        icon: 'none'
      });
    },
    handleAlert(id) {
      uni.showToast({
        title: `处理预警${id}`,
        icon: 'success'
      });
    },
    applyRecommendation(id) {
      uni.showToast({
        title: `已应用建议${id}`,
        icon: 'success'
      });
    },
    ignoreRecommendation(id) {
      uni.showToast({
        title: `已忽略建议${id}`,
        icon: 'none'
      });
    },
    handleTabChange(tabId) {
      // 处理底部标签页切换事件
      console.log('切换到标签:', tabId);
    }
  }
}
</script>

<style lang="scss">
.price-monitor-container {
  min-height: 100vh;
  background-color: #F5F7FA;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF9500, #FF5E3A);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(255, 149, 0, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

.page-content {
  height: calc(100vh - 77px);
  box-sizing: border-box;
  padding: 15px;
}

/* 概览卡片 */
.overview-card {
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 15px;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.overview-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.date-filter {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
}

.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.filter-icon {
  width: 8px;
  height: 8px;
  border-top: 1.5px solid #666;
  border-right: 1.5px solid #666;
  transform: rotate(135deg);
}

.overview-stats {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

/* 警报部分 */
.alert-section {
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 15px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.view-all {
  font-size: 14px;
  color: #FF9500;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  background: #F8FAFC;
  border-radius: 10px;
  margin-bottom: 10px;
}

.alert-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  margin-right: 12px;
  flex-shrink: 0;
}

.alert-icon.warning {
  background: #FFF3E0;
  position: relative;
}

.alert-icon.warning::before {
  content: '!';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FF9800;
  font-weight: 700;
  font-size: 16px;
}

.alert-icon.info {
  background: #E3F2FD;
  position: relative;
}

.alert-icon.info::before {
  content: 'i';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #2196F3;
  font-weight: 700;
  font-size: 16px;
}

.alert-icon.danger {
  background: #FFEBEE;
  position: relative;
}

.alert-icon.danger::before {
  content: '!';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #F44336;
  font-weight: 700;
  font-size: 16px;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.alert-desc {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 5px;
}

.alert-meta {
  display: flex;
  align-items: center;
}

.meta-time {
  font-size: 12px;
  color: #999;
  margin-right: 10px;
}

.meta-tag {
  font-size: 10px;
  color: #FF3B30;
  background: rgba(255, 59, 48, 0.1);
  padding: 2px 6px;
  border-radius: 10px;
}

.alert-action {
  padding: 5px 15px;
  background: #FF9500;
  border-radius: 15px;
  color: white;
  font-size: 12px;
  margin-left: 15px;
}

/* 竞品价格对比部分 */
.compare-section {
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 15px;
}

.filter-dropdown {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
}

.selected-category {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.dropdown-icon {
  width: 8px;
  height: 8px;
  border-top: 1.5px solid #666;
  border-right: 1.5px solid #666;
  transform: rotate(135deg);
}

.product-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #F0F0F0;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  margin-right: 15px;
  background: #F5F7FA;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.price-compare {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.your-price {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.price-divider {
  margin: 0 8px;
  font-size: 12px;
  color: #999;
}

.market-price {
  font-size: 14px;
  color: #666;
}

.price-diff {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
}

.price-diff.higher {
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}

.price-diff.lower {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.price-diff.equal {
  background: rgba(142, 142, 147, 0.1);
  color: #8E8E93;
}

.action-icon {
  width: 16px;
  height: 16px;
  border-top: 2px solid #C7C7CC;
  border-right: 2px solid #C7C7CC;
  transform: rotate(45deg);
  margin-left: 15px;
}

/* 价格趋势图表部分 */
.trends-section {
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 15px;
}

.trend-tabs {
  display: flex;
}

.tab-item {
  padding: 5px 10px;
  font-size: 12px;
  color: #666;
  border-radius: 15px;
  margin-left: 5px;
}

.tab-item.active {
  background: #FF9500;
  color: white;
}

.trend-chart {
  margin-top: 15px;
}

.chart-container {
  height: 200px;
  display: flex;
  align-items: flex-end;
  position: relative;
  margin-bottom: 15px;
}

.chart-line {
  position: absolute;
  left: 0;
  right: 0;
  height: 100%;
}

.chart-line.your-price {
  background: linear-gradient(to bottom, rgba(255, 149, 0, 0.2), transparent);
}

.chart-line.market-avg {
  background: linear-gradient(to bottom, rgba(0, 122, 255, 0.2), transparent);
}

.chart-line.lowest-price {
  background: linear-gradient(to bottom, rgba(52, 199, 89, 0.2), transparent);
}

.chart-legend {
  display: flex;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  margin: 0 10px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 5px;
}

.legend-color.your-price {
  background: #FF9500;
}

.legend-color.market-avg {
  background: #007AFF;
}

.legend-color.lowest-price {
  background: #34C759;
}

.legend-text {
  font-size: 12px;
  color: #666;
}

/* 价格优化建议部分 */
.recommendations-section {
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 15px;
}

.ai-badge {
  display: inline-block;
  background: linear-gradient(135deg, #FF9500, #FF5E3A);
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 3px 6px;
  border-radius: 8px;
  margin-left: 8px;
}

.recommendation-item {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  background: #F8FAFC;
  border-radius: 10px;
  margin-bottom: 10px;
}

.recommendation-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  margin-right: 12px;
  position: relative;
}

.recommendation-icon.price-up {
  background: rgba(52, 199, 89, 0.1);
}

.recommendation-icon.price-up::before {
  content: '↑';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #34C759;
  font-weight: 700;
  font-size: 20px;
}

.recommendation-icon.price-down {
  background: rgba(255, 59, 48, 0.1);
}

.recommendation-icon.price-down::before {
  content: '↓';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FF3B30;
  font-weight: 700;
  font-size: 20px;
}

.recommendation-icon.promotion {
  background: rgba(255, 149, 0, 0.1);
}

.recommendation-icon.promotion::before {
  content: '%';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FF9500;
  font-weight: 700;
  font-size: 16px;
}

.recommendation-content {
  flex: 1;
}

.recommendation-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.recommendation-desc {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 10px;
}

.recommendation-actions {
  display: flex;
  justify-content: flex-end;
}

.action {
  padding: 5px 15px;
  border-radius: 15px;
  font-size: 12px;
  margin-left: 10px;
}

.action.apply {
  background: #FF9500;
  color: white;
}

.action.ignore {
  background: #F5F7FA;
  color: #666;
}
</style> 