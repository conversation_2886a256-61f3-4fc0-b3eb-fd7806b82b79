{"name": "shebang-command", "version": "1.2.0", "description": "Get the command from a shebang", "license": "MIT", "repository": "kevva/shebang-command", "author": {"name": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "github.com/kevva"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["cmd", "command", "parse", "shebang"], "dependencies": {"shebang-regex": "^1.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"ignores": ["test.js"]}}