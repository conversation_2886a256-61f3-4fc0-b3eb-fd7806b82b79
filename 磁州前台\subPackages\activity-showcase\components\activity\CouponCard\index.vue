<template>
  <!-- 优惠券活动卡片 - 苹果风格设计 -->
  <view class="coupon-card">
    <!-- 使用基础活动卡片 -->
    <ActivityCard 
      :item="item" 
      @favorite="$emit('favorite', item.id)"
      @action="$emit('action', { id: item.id, type: item.type, status: item.status })"
    >
      <!-- 优惠券特有信息插槽 -->
      <template #special-info>
        <view class="coupon-special">
          <!-- 优惠券价值区域 -->
          <view class="coupon-value-container">
            <view class="coupon-value-wrapper">
              <text class="value-symbol" v-if="item.couponType === 'cash'">¥</text>
              <text class="value-number">{{item.couponValue}}</text>
              <text class="value-unit" v-if="item.couponType === 'discount'">折</text>
            </view>
            <view class="coupon-type">
              <text>{{couponTypeText}}</text>
            </view>
          </view>
          
          <!-- 使用条件 -->
          <view class="coupon-condition" v-if="item.couponCondition">
            <view class="condition-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
              </svg>
            </view>
            <text class="condition-text">{{item.couponCondition}}</text>
          </view>
          
          <!-- 使用范围 -->
          <view class="coupon-scope" v-if="item.couponScope">
            <view class="scope-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="9" y1="3" x2="9" y2="21"></line>
              </svg>
            </view>
            <text class="scope-text">{{item.couponScope}}</text>
          </view>
          
          <!-- 有效期 -->
          <view class="coupon-validity">
            <view class="validity-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="16" y1="2" x2="16" y2="6"></line>
                <line x1="8" y1="2" x2="8" y2="6"></line>
                <line x1="3" y1="10" x2="21" y2="10"></line>
              </svg>
            </view>
            <text class="validity-text">有效期至: {{item.couponValidity}}</text>
          </view>
          
          <!-- 领取进度 -->
          <view class="coupon-progress" v-if="item.totalCount && item.claimedCount !== undefined">
            <view class="progress-header">
              <text class="progress-title">领取进度</text>
              <text class="progress-status">{{item.claimedCount}}/{{item.totalCount}}张</text>
            </view>
            <view class="progress-bar">
              <view class="progress-inner" :style="{width: progressWidth + '%'}"></view>
            </view>
            <view class="progress-tip" v-if="remainCount > 0">
              <text class="tip-text">仅剩{{remainCount}}张，先到先得！</text>
            </view>
            <view class="progress-tip" v-else>
              <text class="tip-text">已领完，下次早点来哦~</text>
            </view>
          </view>
        </view>
      </template>
    </ActivityCard>
  </view>
</template>

<script setup>
import { computed } from 'vue';
import ActivityCard from '../ActivityCard.vue';

const props = defineProps({
  item: {
    type: Object,
    required: true
  }
});

// 优惠券类型文本
const couponTypeText = computed(() => {
  if (!props.item.couponType) return '优惠券';
  
  switch(props.item.couponType) {
    case 'cash':
      return '现金券';
    case 'discount':
      return '折扣券';
    case 'exchange':
      return '兑换券';
    case 'gift':
      return '礼品券';
    default:
      return '优惠券';
  }
});

// 计算进度条宽度
const progressWidth = computed(() => {
  if (!props.item.claimedCount || !props.item.totalCount) return 0;
  return (props.item.claimedCount / props.item.totalCount) * 100;
});

// 计算剩余数量
const remainCount = computed(() => {
  if (props.item.claimedCount === undefined || !props.item.totalCount) return 0;
  return props.item.totalCount - props.item.claimedCount;
});
</script>

<style scoped>
/* 优惠券活动卡片特有样式 */
.coupon-card {
  /* 继承基础卡片样式 */
}

/* 优惠券特有信息区域 */
.coupon-special {
  padding: 20rpx;
  background: linear-gradient(135deg, rgba(255, 149, 0, 0.05), rgba(255, 59, 48, 0.05));
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
  overflow: hidden;
}

.coupon-special::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 10rpx 10rpx, transparent 12rpx, rgba(255, 149, 0, 0.03) 13rpx);
  background-size: 30rpx 30rpx;
  opacity: 0.5;
  z-index: 0;
  pointer-events: none;
}

/* 优惠券价值区域 */
.coupon-value-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 1;
}

.coupon-value-wrapper {
  display: flex;
  align-items: baseline;
  color: #ff9500;
}

.value-symbol {
  font-size: 30rpx;
  font-weight: 600;
}

.value-number {
  font-size: 60rpx;
  font-weight: 700;
  line-height: 1;
}

.value-unit {
  font-size: 30rpx;
  font-weight: 600;
  margin-left: 4rpx;
}

.coupon-type {
  margin-top: 8rpx;
  padding: 4rpx 16rpx;
  background-color: rgba(255, 149, 0, 0.1);
  border-radius: 20rpx;
}

.coupon-type text {
  font-size: 22rpx;
  color: #ff9500;
  font-weight: 500;
}

/* 使用条件 */
.coupon-condition {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  position: relative;
  z-index: 1;
}

.condition-icon {
  margin-right: 8rpx;
  color: #ff9500;
}

.condition-text {
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
}

/* 使用范围 */
.coupon-scope {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  position: relative;
  z-index: 1;
}

.scope-icon {
  margin-right: 8rpx;
  color: #ff9500;
}

.scope-text {
  font-size: 24rpx;
  color: #333333;
}

/* 有效期 */
.coupon-validity {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 1;
}

.validity-icon {
  margin-right: 8rpx;
  color: #ff9500;
}

.validity-text {
  font-size: 24rpx;
  color: #333333;
}

/* 领取进度 */
.coupon-progress {
  margin-top: 16rpx;
  border-top: 1rpx dashed rgba(255, 149, 0, 0.2);
  padding-top: 16rpx;
  position: relative;
  z-index: 1;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.progress-title {
  font-size: 24rpx;
  color: #000000;
  font-weight: 500;
}

.progress-status {
  font-size: 24rpx;
  color: #ff9500;
  font-weight: 500;
}

.progress-bar {
  height: 10rpx;
  background-color: rgba(255, 149, 0, 0.1);
  border-radius: 5rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
}

.progress-inner {
  height: 100%;
  background-color: #ff9500;
  border-radius: 5rpx;
  transition: width 0.3s ease;
}

.progress-tip {
  margin-top: 8rpx;
}

.tip-text {
  font-size: 22rpx;
  color: #ff9500;
}
</style> 