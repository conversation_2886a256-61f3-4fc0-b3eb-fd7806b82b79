"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const userInfo = common_vendor.ref({
      avatar: "https://via.placeholder.com/180",
      nickname: "张三",
      gender: 1,
      // 1: 男, 2: 女
      birthday: "1990-01-01",
      phone: "13800138000",
      region: "北京市 海淀区",
      isVip: true,
      isVerified: false,
      isWeChatBound: true
    });
    const privacySettings = common_vendor.ref({
      allowProfileView: true,
      showActivityHistory: true,
      receiveActivityPush: true
    });
    function goBack() {
      common_vendor.index.navigateBack();
    }
    function saveProfile() {
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "保存成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      }, 1500);
    }
    function changeAvatar() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePaths = res.tempFilePaths;
          userInfo.value.avatar = tempFilePaths[0];
          common_vendor.index.showToast({
            title: "头像已更新",
            icon: "success"
          });
        }
      });
    }
    function showDatePicker() {
      common_vendor.index.showToast({
        title: "日期选择功能开发中",
        icon: "none"
      });
    }
    function showRegionPicker() {
      common_vendor.index.showToast({
        title: "地区选择功能开发中",
        icon: "none"
      });
    }
    function bindPhone() {
      common_vendor.index.showToast({
        title: "绑定手机号功能开发中",
        icon: "none"
      });
    }
    function changePassword() {
      common_vendor.index.showToast({
        title: "修改密码功能开发中",
        icon: "none"
      });
    }
    function verifyIdentity() {
      common_vendor.index.showToast({
        title: "实名认证功能开发中",
        icon: "none"
      });
    }
    function bindWeChat() {
      common_vendor.index.showToast({
        title: "绑定微信功能开发中",
        icon: "none"
      });
    }
    function deleteAccount() {
      common_vendor.index.showModal({
        title: "注销账号",
        content: "注销账号后，您的所有数据将被清除且无法恢复，确定要注销吗？",
        confirmColor: "#FF3B30",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "注销功能开发中",
              icon: "none"
            });
          }
        }
      });
    }
    function formatPhone(phone) {
      if (!phone)
        return "";
      return phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: common_vendor.o(saveProfile),
        d: userInfo.value.avatar,
        e: common_vendor.p({
          d: "M17 3a2.828 2.828 0 114 4L7.5 20.5 2 22l1.5-5.5L17 3z",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        f: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "20",
          height: "20"
        }),
        g: common_vendor.o(changeAvatar),
        h: userInfo.value.isVip
      }, userInfo.value.isVip ? {} : {}, {
        i: userInfo.value.nickname,
        j: common_vendor.o(($event) => userInfo.value.nickname = $event.detail.value),
        k: userInfo.value.gender === 1 ? 1 : "",
        l: common_vendor.o(($event) => userInfo.value.gender = 1),
        m: userInfo.value.gender === 1 ? "rgba(255,59,105,0.1)" : "#F8F8F8",
        n: userInfo.value.gender === 1 ? "#FF3B69" : "#666666",
        o: userInfo.value.gender === 1 ? "1rpx solid #FF3B69" : "1rpx solid transparent",
        p: userInfo.value.gender === 2 ? 1 : "",
        q: common_vendor.o(($event) => userInfo.value.gender = 2),
        r: userInfo.value.gender === 2 ? "rgba(255,59,105,0.1)" : "#F8F8F8",
        s: userInfo.value.gender === 2 ? "#FF3B69" : "#666666",
        t: userInfo.value.gender === 2 ? "1rpx solid #FF3B69" : "1rpx solid transparent",
        v: common_vendor.t(userInfo.value.birthday || "请选择生日"),
        w: userInfo.value.birthday ? "#333333" : "#999999",
        x: common_vendor.p({
          d: "M6 9l6 6 6-6",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        y: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        z: common_vendor.o(showDatePicker),
        A: userInfo.value.phone
      }, userInfo.value.phone ? {
        B: common_vendor.t(formatPhone(userInfo.value.phone))
      } : {
        C: common_vendor.o(bindPhone)
      }, {
        D: common_vendor.t(userInfo.value.region || "请选择地区"),
        E: userInfo.value.region ? "#333333" : "#999999",
        F: common_vendor.p({
          d: "M6 9l6 6 6-6",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        G: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        H: common_vendor.o(showRegionPicker),
        I: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        J: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        K: common_vendor.o(changePassword),
        L: common_vendor.t(userInfo.value.isVerified ? "已认证" : "未认证"),
        M: userInfo.value.isVerified ? "#34C759" : "#999999",
        N: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        O: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        P: common_vendor.o(verifyIdentity),
        Q: common_vendor.t(userInfo.value.isWeChatBound ? "已绑定" : "未绑定"),
        R: userInfo.value.isWeChatBound ? "#34C759" : "#999999",
        S: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        T: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        U: common_vendor.o(bindWeChat),
        V: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        W: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        X: common_vendor.o(deleteAccount),
        Y: privacySettings.value.allowProfileView,
        Z: common_vendor.o((e) => privacySettings.value.allowProfileView = e.detail.value),
        aa: privacySettings.value.showActivityHistory,
        ab: common_vendor.o((e) => privacySettings.value.showActivityHistory = e.detail.value),
        ac: privacySettings.value.receiveActivityPush,
        ad: common_vendor.o((e) => privacySettings.value.receiveActivityPush = e.detail.value)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-c64ed3ce"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/user-profile/index.js.map
