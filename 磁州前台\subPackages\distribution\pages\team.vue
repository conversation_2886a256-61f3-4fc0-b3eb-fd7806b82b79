<template>
  <view class="team-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">我的团队</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 团队统计卡片 -->
    <view class="stats-card">
      <view class="stats-header">
        <text class="stats-title">团队概览</text>
      </view>
      
      <view class="stats-content">
        <view class="stats-item">
          <text class="item-value">{{teamSummary.level1Count + teamSummary.level2Count}}</text>
          <text class="item-label">团队总人数</text>
        </view>
        
        <view class="stats-item">
          <text class="item-value">¥{{formatCommission(teamSummary.totalContribution)}}</text>
          <text class="item-label">团队总业绩</text>
        </view>
      </view>
      
      <view class="level-stats">
        <view class="level-item">
          <view class="level-icon level1"></view>
          <view class="level-info">
            <text class="level-name">一级成员</text>
            <text class="level-count">{{teamSummary.level1Count}}人</text>
          </view>
        </view>
        
        <view class="level-item">
          <view class="level-icon level2"></view>
          <view class="level-info">
            <text class="level-name">二级成员</text>
            <text class="level-count">{{teamSummary.level2Count}}人</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index" 
        class="tab-item" 
        :class="{ 'active': activeTab === tab.value }"
        @click="switchTab(tab.value)"
      >
        <text>{{tab.name}}</text>
      </view>
    </view>
    
    <!-- 团队成员列表 -->
    <view class="member-list" v-if="teamMembers.length > 0">
      <view 
        v-for="(member, index) in teamMembers" 
        :key="index" 
        class="member-item"
      >
        <view class="member-info">
          <image class="member-avatar" :src="member.avatar" mode="aspectFill"></image>
          <view class="member-details">
            <text class="member-name">{{member.nickname}}</text>
            <view class="member-meta">
              <text class="member-level">{{member.levelName}}</text>
              <text class="member-time">{{formatTime(member.joinTime)}}</text>
            </view>
          </view>
        </view>
        
        <view class="member-stats">
          <view class="stat-item">
            <text class="stat-value">¥{{formatCommission(member.contribution)}}</text>
            <text class="stat-label">贡献佣金</text>
          </view>
          
          <view class="stat-item">
            <text class="stat-value">{{member.orderCount}}</text>
            <text class="stat-label">订单数</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <image class="empty-image" src="/static/images/empty-team.png" mode="aspectFit"></image>
      <text class="empty-text">暂无团队成员</text>
      <button class="invite-btn" @click="navigateTo('/subPackages/distribution/pages/promotion')">邀请好友</button>
    </view>
    
    <!-- 加载更多 -->
    <view class="load-more" v-if="hasMoreData && teamMembers.length > 0">
      <text v-if="loading">加载中...</text>
      <text v-else @click="loadMore">点击加载更多</text>
    </view>
    
    <!-- 团队规则 -->
    <view class="rules-card">
      <view class="card-header">
        <text class="card-title">团队规则</text>
      </view>
      
      <view class="rules-content">
        <view class="rule-item">
          <view class="rule-icon"></view>
          <text class="rule-text">通过您的推广链接注册的用户为您的一级团队成员</text>
        </view>
        
        <view class="rule-item">
          <view class="rule-icon"></view>
          <text class="rule-text">一级团队成员发展的分销员为您的二级团队成员</text>
        </view>
        
        <view class="rule-item">
          <view class="rule-icon"></view>
          <text class="rule-text">您可以获得一级团队成员订单佣金的{{commissionRates.level1}}%</text>
        </view>
        
        <view class="rule-item">
          <view class="rule-icon"></view>
          <text class="rule-text">您可以获得二级团队成员订单佣金的{{commissionRates.level2}}%</text>
        </view>
      </view>
    </view>
    
    <!-- 团队发展建议 -->
    <view class="tips-card">
      <view class="card-header">
        <text class="card-title">发展建议</text>
      </view>
      
      <view class="tips-content">
        <view class="tip-item">
          <view class="tip-icon"></view>
          <text class="tip-text">分享优质商品和专属优惠，吸引更多用户加入</text>
        </view>
        
        <view class="tip-item">
          <view class="tip-icon"></view>
          <text class="tip-text">定期与团队成员沟通，分享推广技巧</text>
        </view>
        
        <view class="tip-item">
          <view class="tip-icon"></view>
          <text class="tip-text">鼓励团队成员发展下线，共同壮大团队</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import distributionService from '@/utils/distributionService';

// 选项卡
const tabs = [
  { name: '全部成员', value: 'all' },
  { name: '一级成员', value: 1 },
  { name: '二级成员', value: 2 }
];

// 当前选中的选项卡
const activeTab = ref('all');

// 团队成员
const teamMembers = ref([]);

// 团队统计
const teamSummary = reactive({
  level1Count: 0,
  level2Count: 0,
  totalContribution: 0
});

// 佣金比例
const commissionRates = reactive({
  level1: 30,
  level2: 10
});

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0
});

// 是否有更多数据
const hasMoreData = ref(false);

// 是否正在加载
const loading = ref(false);

// 页面加载
onMounted(async () => {
  // 获取团队成员
  await getTeamMembers();
});

// 获取团队成员
const getTeamMembers = async (loadMore = false) => {
  if (loading.value) return;
  
  try {
    loading.value = true;
    
    const page = loadMore ? pagination.page + 1 : 1;
    const level = activeTab.value === 'all' ? undefined : activeTab.value;
    
    const result = await distributionService.getTeamMembers({
      page,
      pageSize: pagination.pageSize,
      level
    });
    
    if (result) {
      // 更新团队成员
      if (loadMore) {
        teamMembers.value = [...teamMembers.value, ...result.list];
      } else {
        teamMembers.value = result.list;
      }
      
      // 更新分页信息
      pagination.page = page;
      pagination.total = result.pagination.total;
      pagination.totalPages = result.pagination.totalPages;
      
      // 更新是否有更多数据
      hasMoreData.value = pagination.page < pagination.totalPages;
      
      // 更新团队统计
      if (result.summary) {
        Object.assign(teamSummary, result.summary);
      }
    }
  } catch (error) {
    console.error('获取团队成员失败', error);
    uni.showToast({
      title: '获取团队成员失败',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
};

// 切换选项卡
const switchTab = (tab) => {
  if (activeTab.value === tab) return;
  
  activeTab.value = tab;
  getTeamMembers();
};

// 加载更多
const loadMore = () => {
  if (hasMoreData.value && !loading.value) {
    getTeamMembers(true);
  }
};

// 格式化佣金
const formatCommission = (amount) => {
  return distributionService.formatCommission(amount);
};

// 格式化时间
const formatTime = (time) => {
  if (!time) return '';
  
  const date = new Date(time);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 页面导航
const navigateTo = (url) => {
  uni.navigateTo({ url });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 显示帮助
const showHelp = () => {
  uni.showModal({
    title: '团队帮助',
    content: '团队页面显示您发展的分销员信息和业绩。一级成员是直接通过您的邀请加入的分销员，二级成员是您的一级成员邀请的分销员。',
    showCancel: false
  });
};
</script>

<style lang="scss">
.team-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 88rpx 32rpx 30rpx;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);
}

.navbar-back {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24rpx;
  height: 24rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.navbar-right {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

/* 团队统计卡片 */
.stats-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.stats-header {
  margin-bottom: 30rpx;
}

.stats-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.stats-content {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30rpx;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.item-value {
  font-size: 40rpx;
  font-weight: 600;
  color: #6B0FBE;
  margin-bottom: 8rpx;
}

.item-label {
  font-size: 24rpx;
  color: #666;
}

.level-stats {
  display: flex;
  justify-content: space-around;
  padding-top: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.level-item {
  display: flex;
  align-items: center;
}

.level-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  border-radius: 40rpx;
}

.level-icon.level1 {
  background-color: #6B0FBE;
}

.level-icon.level2 {
  background-color: #409EFF;
}

.level-info {
  display: flex;
  flex-direction: column;
}

.level-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.level-count {
  font-size: 24rpx;
  color: #666;
}

/* 筛选栏 */
.filter-bar {
  display: flex;
  background: #FFFFFF;
  padding: 0 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #6B0FBE;
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #6B0FBE;
  border-radius: 2rpx;
}

/* 团队成员列表 */
.member-list {
  margin: 0 30rpx;
}

.member-item {
  background: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.member-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.member-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
  border: 2rpx solid #f0f0f0;
}

.member-details {
  flex: 1;
}

.member-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.member-meta {
  display: flex;
  align-items: center;
}

.member-level {
  font-size: 24rpx;
  color: #6B0FBE;
  background-color: rgba(107, 15, 190, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
}

.member-time {
  font-size: 24rpx;
  color: #999;
}

.member-stats {
  display: flex;
  justify-content: space-between;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 30rpx;
}

.invite-btn {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  padding: 12rpx 60rpx;
  line-height: 1.5;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 26rpx;
  color: #666;
}

/* 卡片通用样式 */
.rules-card,
.tips-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.card-header {
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 规则和提示 */
.rules-content,
.tips-content {
  margin-bottom: 20rpx;
}

.rule-item,
.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.rule-item:last-child,
.tip-item:last-child {
  margin-bottom: 0;
}

.rule-icon,
.tip-icon {
  width: 16rpx;
  height: 16rpx;
  border-radius: 40rpx;
  background-color: #6B0FBE;
  margin-right: 16rpx;
  margin-top: 10rpx;
  flex-shrink: 0;
}

.rule-text,
.tip-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}
</style> 