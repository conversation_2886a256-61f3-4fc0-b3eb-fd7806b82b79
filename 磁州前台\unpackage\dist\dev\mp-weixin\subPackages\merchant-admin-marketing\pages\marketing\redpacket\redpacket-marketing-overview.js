"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
const _sfc_main = {
  data() {
    return {};
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    shareGuide() {
      common_vendor.index.showActionSheet({
        itemList: ["分享给好友", "分享到朋友圈", "复制链接"],
        success: function(res) {
          common_vendor.index.showToast({
            title: "分享成功",
            icon: "success"
          });
        }
      });
    },
    contactService() {
      common_vendor.index.makePhoneCall({
        phoneNumber: "************"
      });
    }
  }
};
if (!Array) {
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  const _component_rect = common_vendor.resolveComponent("rect");
  (_component_circle + _component_line + _component_svg + _component_path + _component_polyline + _component_rect)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.p({
      cx: "18",
      cy: "5",
      r: "3"
    }),
    c: common_vendor.p({
      cx: "6",
      cy: "12",
      r: "3"
    }),
    d: common_vendor.p({
      cx: "18",
      cy: "19",
      r: "3"
    }),
    e: common_vendor.p({
      x1: "8.59",
      y1: "13.51",
      x2: "15.42",
      y2: "17.49"
    }),
    f: common_vendor.p({
      x1: "15.41",
      y1: "6.51",
      x2: "8.59",
      y2: "10.49"
    }),
    g: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    h: common_vendor.o((...args) => $options.shareGuide && $options.shareGuide(...args)),
    i: common_vendor.p({
      d: "M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"
    }),
    j: common_vendor.p({
      points: "3.27 6.96 12 12.01 20.73 6.96"
    }),
    k: common_vendor.p({
      x1: "12",
      y1: "22.08",
      x2: "12",
      y2: "12"
    }),
    l: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#6EE7B7",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    m: common_assets._imports_0$40,
    n: common_vendor.p({
      points: "22 12 18 12 15 21 9 3 6 12 2 12"
    }),
    o: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#6EE7B7",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    p: common_vendor.p({
      d: "M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"
    }),
    q: common_vendor.p({
      cx: "9",
      cy: "7",
      r: "4"
    }),
    r: common_vendor.p({
      d: "M23 21v-2a4 4 0 0 0-3-3.87"
    }),
    s: common_vendor.p({
      d: "M16 3.13a4 4 0 0 1 0 7.75"
    }),
    t: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#6EE7B7",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    v: common_vendor.p({
      d: "M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"
    }),
    w: common_vendor.p({
      cx: "12",
      cy: "7",
      r: "4"
    }),
    x: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#6EE7B7",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    y: common_vendor.p({
      x: "3",
      y: "4",
      width: "18",
      height: "18",
      rx: "2",
      ry: "2"
    }),
    z: common_vendor.p({
      x1: "16",
      y1: "2",
      x2: "16",
      y2: "6"
    }),
    A: common_vendor.p({
      x1: "8",
      y1: "2",
      x2: "8",
      y2: "6"
    }),
    B: common_vendor.p({
      x1: "3",
      y1: "10",
      x2: "21",
      y2: "10"
    }),
    C: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#6EE7B7",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    D: common_vendor.p({
      d: "M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"
    }),
    E: common_vendor.p({
      cx: "9",
      cy: "7",
      r: "4"
    }),
    F: common_vendor.p({
      d: "M23 21v-2a4 4 0 0 0-3-3.87"
    }),
    G: common_vendor.p({
      d: "M16 3.13a4 4 0 0 1 0 7.75"
    }),
    H: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#6EE7B7",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    I: common_vendor.p({
      d: "M22 11.08V12a10 10 0 1 1-5.93-9.14"
    }),
    J: common_vendor.p({
      points: "22 4 12 14.01 9 11.01"
    }),
    K: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#6EE7B7",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    L: common_vendor.p({
      cx: "12",
      cy: "12",
      r: "10"
    }),
    M: common_vendor.p({
      d: "M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"
    }),
    N: common_vendor.p({
      x1: "12",
      y1: "17",
      x2: "12.01",
      y2: "17"
    }),
    O: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#6EE7B7",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    P: common_vendor.p({
      d: "M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
    }),
    Q: common_vendor.p({
      points: "14 2 14 8 20 8"
    }),
    R: common_vendor.p({
      x1: "16",
      y1: "13",
      x2: "8",
      y2: "13"
    }),
    S: common_vendor.p({
      x1: "16",
      y1: "17",
      x2: "8",
      y2: "17"
    }),
    T: common_vendor.p({
      points: "10 9 9 9 8 9"
    }),
    U: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#6EE7B7",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    V: common_vendor.o((...args) => $options.contactService && $options.contactService(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-3b3a55a0"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/redpacket/redpacket-marketing-overview.js.map
