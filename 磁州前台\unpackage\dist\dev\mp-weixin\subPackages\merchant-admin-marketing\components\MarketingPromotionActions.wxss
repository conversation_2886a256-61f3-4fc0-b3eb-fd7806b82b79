/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-460de224, html.data-v-460de224, #app.data-v-460de224, .index-container.data-v-460de224 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.marketing-promotion-container.data-v-460de224 {
  padding: 20rpx 0;
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica, Arial, sans-serif;
}
.promotion-section.data-v-460de224 {
  margin-bottom: 30rpx;
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.section-header.data-v-460de224 {
  margin-bottom: 20rpx;
}
.section-title.data-v-460de224 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.section-desc.data-v-460de224 {
  font-size: 24rpx;
  color: #999999;
  margin-top: 6rpx;
}
.promotion-options.data-v-460de224 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.promotion-option.data-v-460de224 {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #F8F9FA;
  border-radius: 10rpx;
  position: relative;
  transition: all 0.3s ease;
}
.promotion-option.data-v-460de224:active {
  background-color: #F0F0F0;
  transform: scale(0.99);
}
.option-icon-container.data-v-460de224 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}
.normal-icon.data-v-460de224 {
  background: linear-gradient(135deg, #4FACFE, #00F2FE);
  color: white;
}
.featured-icon.data-v-460de224 {
  background: linear-gradient(135deg, #FFD26F, #FF9A44);
  color: white;
}
.normal-top-icon.data-v-460de224 {
  background: linear-gradient(135deg, #FF9A9E, #FAD0C4);
  color: white;
}
.premium-top-icon.data-v-460de224 {
  background: linear-gradient(135deg, #FF6B6B, #FF3366);
  color: white;
}
.refresh-icon.data-v-460de224 {
  background: linear-gradient(135deg, #A1C4FD, #C2E9FB);
  color: white;
}
.package-icon.data-v-460de224 {
  background: linear-gradient(135deg, #84FAB0, #8FD3F4);
  color: white;
}
.option-content.data-v-460de224 {
  flex: 1;
}
.option-title.data-v-460de224 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}
.option-desc.data-v-460de224 {
  font-size: 24rpx;
  color: #999999;
  margin-top: 4rpx;
}
.option-tag.data-v-460de224 {
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}
.free-tag.data-v-460de224 {
  background-color: #E8F5E9;
  color: #4CAF50;
}
.paid-tag.data-v-460de224 {
  background-color: #FFF3E0;
  color: #FF9800;
}

/* 支付弹窗样式 */
.payment-modal.data-v-460de224 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}
.modal-overlay.data-v-460de224 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.modal-container.data-v-460de224 {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 600rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.modal-header.data-v-460de224 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #F0F0F0;
}
.modal-title.data-v-460de224 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.modal-close.data-v-460de224 {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999999;
}
.modal-content.data-v-460de224 {
  padding: 30rpx;
}
.payment-info.data-v-460de224 {
  margin-bottom: 30rpx;
  text-align: center;
}
.payment-desc.data-v-460de224 {
  font-size: 28rpx;
  color: #666666;
}
.payment-price.data-v-460de224 {
  font-size: 48rpx;
  font-weight: 600;
  color: #FF6B6B;
  margin-top: 16rpx;
  display: block;
}
.payment-methods.data-v-460de224 {
  margin-top: 30rpx;
}
.methods-title.data-v-460de224 {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
  display: block;
}
.method-options.data-v-460de224 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.method-option.data-v-460de224 {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #F8F9FA;
  border-radius: 10rpx;
  transition: all 0.3s ease;
}
.method-option.active.data-v-460de224 {
  background-color: #F0F7FF;
  border: 1rpx solid #4FACFE;
}
.method-icon.data-v-460de224 {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  color: white;
}
.wxpay-icon.data-v-460de224 {
  background-color: #09BB07;
}
.alipay-icon.data-v-460de224 {
  background-color: #00A0E9;
}
.method-name.data-v-460de224 {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}
.method-check.data-v-460de224 {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 1rpx solid #DDDDDD;
  display: flex;
  align-items: center;
  justify-content: center;
}
.method-option.active .method-check.data-v-460de224 {
  border-color: #4FACFE;
}
.check-inner.data-v-460de224 {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #4FACFE;
}
.modal-footer.data-v-460de224 {
  display: flex;
  border-top: 1rpx solid #F0F0F0;
}
.btn-cancel.data-v-460de224, .btn-confirm.data-v-460de224 {
  flex: 1;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  border: none;
  background-color: transparent;
}
.btn-cancel.data-v-460de224 {
  color: #999999;
  border-right: 1rpx solid #F0F0F0;
}
.btn-confirm.data-v-460de224 {
  color: #4FACFE;
  font-weight: 500;
}

/* 新的发布选项卡片样式 */
.publish-options-card.data-v-460de224 {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.publish-option-row.data-v-460de224 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 24rpx;
  border-bottom: 1rpx solid #F5F5F5;
}
.publish-option-row.data-v-460de224:last-child {
  border-bottom: none;
}
.publish-option-left.data-v-460de224 {
  display: flex;
  align-items: center;
  flex: 1;
}
.publish-option-icon.data-v-460de224 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}
.ad-icon.data-v-460de224 {
  background: linear-gradient(135deg, #4FACFE, #00F2FE);
  color: white;
}
.paid-icon.data-v-460de224 {
  background: linear-gradient(135deg, #FF9A44, #FF6B6B);
  color: white;
}
.publish-option-content.data-v-460de224 {
  flex: 1;
}
.publish-option-title.data-v-460de224 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8rpx;
}
.publish-option-desc.data-v-460de224 {
  font-size: 24rpx;
  color: #999999;
}
.publish-option-right.data-v-460de224 {
  margin-left: 20rpx;
}
.publish-btn.data-v-460de224 {
  min-width: 120rpx;
  height: 64rpx;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}
.free-btn.data-v-460de224 {
  background: linear-gradient(135deg, #4FACFE, #00F2FE);
  color: white;
}
.paid-btn.data-v-460de224 {
  background: linear-gradient(135deg, #FF9A44, #FF6B6B);
  color: white;
}

/* 发布时长选择 */
.publish-duration-select.data-v-460de224 {
  padding: 24rpx;
  background-color: #F8F9FA;
}
.duration-title.data-v-460de224 {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
  display: block;
}
.duration-options.data-v-460de224 {
  display: flex;
  justify-content: space-between;
}
.duration-option.data-v-460de224 {
  flex: 1;
  height: 100rpx;
  background-color: #FFFFFF;
  border-radius: 8rpx;
  margin: 0 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #EEEEEE;
  transition: all 0.3s ease;
}
.duration-option.data-v-460de224:first-child {
  margin-left: 0;
}
.duration-option.data-v-460de224:last-child {
  margin-right: 0;
}
.duration-option.active.data-v-460de224 {
  border-color: #FF9A44;
  background-color: #FFF9F5;
}
.duration-text.data-v-460de224 {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 4rpx;
}
.duration-price.data-v-460de224 {
  font-size: 24rpx;
  color: #FF6B6B;
  font-weight: 500;
}
.duration-option.active .duration-text.data-v-460de224 {
  color: #FF6B6B;
  font-weight: 500;
}

/* 刷新付费弹窗样式 */
.refresh-payment-modal.data-v-460de224 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
}
.refresh-payment-overlay.data-v-460de224 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.refresh-payment-content.data-v-460de224 {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 650rpx;
  z-index: 1001;
  background: linear-gradient(135deg, #ffffff, #f8f9ff);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}

/* 现代设计风格的弹窗样式 */
.modern-design.data-v-460de224 {
  border-radius: 18px;
  overflow: hidden;
  background: #ffffff;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.07);
  width: 85%;
  max-width: 330px;
  padding: 0;
  border: none;
}
.refresh-header-gradient.data-v-460de224 {
  background: linear-gradient(135deg, #3a7bd5, #00d2ff);
  padding: 22px 20px;
  color: #fff;
  position: relative;
  text-align: center;
}
.refresh-badge.data-v-460de224 {
  display: inline-flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.25);
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
  border-radius: 20px;
  padding: 4px 12px;
  margin-bottom: 14px;
  color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.refresh-badge svg.data-v-460de224 {
  margin-right: 6px;
}
.refresh-title.data-v-460de224 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 4px;
  display: block;
  letter-spacing: 0.5px;
}
.refresh-subtitle.data-v-460de224 {
  font-size: 14px;
  opacity: 0.9;
  display: block;
}
.refresh-price-area.data-v-460de224 {
  background: #f8faff;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #eef2f9;
}
.price-tag.data-v-460de224 {
  display: flex;
  align-items: baseline;
}
.price-symbol.data-v-460de224 {
  font-size: 16px;
  color: #3a7bd5;
  margin-right: 2px;
}
.price-value.data-v-460de224 {
  font-size: 28px;
  font-weight: 700;
  color: #3a7bd5;
}
.price-label.data-v-460de224 {
  color: #8a9ab0;
  font-size: 14px;
}
.refresh-info-box.data-v-460de224 {
  margin: 0 20px;
  padding: 12px 14px;
  background: #ebf3ff;
  border-left: 3px solid #3a7bd5;
  border-radius: 8px;
}
.info-highlight.data-v-460de224 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}
.info-detail.data-v-460de224 {
  font-size: 12px;
  color: #666;
}
.highlight-text.data-v-460de224 {
  color: #ff6b42;
  font-weight: 600;
}
.refresh-package-hint.data-v-460de224 {
  margin: 16px 20px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.package-hint-left.data-v-460de224 {
  font-size: 13px;
  font-weight: 500;
  color: #3a7bd5;
  background: rgba(58, 123, 213, 0.1);
  padding: 4px 12px;
  border-radius: 14px;
}
.package-hint-right.data-v-460de224 {
  font-size: 12px;
  color: #8a9ab0;
}
.highlight-discount.data-v-460de224 {
  margin-left: 4px;
  font-size: 16px;
  font-weight: 700;
  color: #ff6b42;
}
.refresh-action-buttons.data-v-460de224 {
  display: flex;
  padding: 16px 20px 20px;
  gap: 10px;
}
.btn-check-package.data-v-460de224 {
  flex: 1;
  height: 40px;
  border-radius: 20px;
  border: 1px solid #3a7bd5;
  background: transparent;
  color: #3a7bd5;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-refresh-now.data-v-460de224 {
  flex: 1;
  height: 40px;
  border-radius: 20px;
  background: linear-gradient(135deg, #3a7bd5, #00d2ff);
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 10px rgba(58, 123, 213, 0.3);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 刷新套餐选择弹窗样式 */
.refresh-packages-modal.data-v-460de224 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
}
.refresh-packages-overlay.data-v-460de224 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.refresh-packages-content.data-v-460de224 {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 650rpx;
  z-index: 1001;
  background: linear-gradient(135deg, #ffffff, #f8f9ff);
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}
.refresh-packages-header.data-v-460de224 {
  padding: 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  background: linear-gradient(135deg, #f0f7ff, #e8f4ff);
}
.refresh-packages-title.data-v-460de224 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  position: relative;
}
.refresh-icon-wrapper.data-v-460de224 {
  display: inline-block;
  margin-right: 8rpx;
  font-size: 32rpx;
  animation: spin-460de224 2s infinite linear;
}
@keyframes spin-460de224 {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.refresh-packages-title.data-v-460de224::after {
  content: "";
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #007aff, #5856d6);
  border-radius: 2rpx;
}
.refresh-packages-body.data-v-460de224 {
  padding: 40rpx 30rpx;
}
.refresh-packages-desc.data-v-460de224 {
  font-size: 28rpx;
  color: #555;
  margin-bottom: 30rpx;
  text-align: center;
  font-weight: 500;
  background: rgba(0, 122, 255, 0.08);
  padding: 16rpx;
  border-radius: 12rpx;
  line-height: 1.5;
}
.refresh-packages-list.data-v-460de224 {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  margin-bottom: 40rpx;
}
.refresh-package-item.data-v-460de224 {
  width: 30%;
  height: 180rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #fff8f0 0%, #fff1e6 100%);
  border-radius: 16rpx;
  box-shadow: 0 8rpx 16rpx rgba(255, 145, 85, 0.15);
  border: 1rpx solid rgba(255, 145, 85, 0.1);
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}
.refresh-package-item.data-v-460de224:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 8rpx rgba(255, 145, 85, 0.1);
}
.refresh-package-item.data-v-460de224::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6rpx;
  background: linear-gradient(90deg, #ff6b6b, #ff9f43);
}
.refresh-package-count.data-v-460de224 {
  font-size: 36rpx;
  font-weight: 700;
  color: #ff6b6b;
  margin-bottom: 10rpx;
}
.refresh-package-price.data-v-460de224 {
  font-size: 30rpx;
  font-weight: 600;
  color: #ff9f43;
}
.refresh-package-discount.data-v-460de224 {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  font-size: 22rpx;
  color: #fff;
  font-weight: 600;
  background: linear-gradient(135deg, #ff6b6b, #ff9f43);
  padding: 4rpx 10rpx;
  border-radius: 8rpx;
  transform: rotate(10deg);
  box-shadow: 0 2rpx 6rpx rgba(255, 107, 107, 0.3);
}
.refresh-packages-actions.data-v-460de224 {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx 40rpx;
  gap: 20rpx;
}
.refresh-packages-confirm-btn.data-v-460de224 {
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-size: 30rpx;
  font-weight: 500;
  border-radius: 45rpx;
  background: linear-gradient(135deg, #007aff, #5856d6);
  color: #FFFFFF;
  box-shadow: 0 6rpx 12rpx rgba(0, 122, 255, 0.2);
  transition: all 0.3s;
}
.refresh-packages-confirm-btn.data-v-460de224:active {
  transform: scale(0.98);
  box-shadow: 0 3rpx 6rpx rgba(0, 122, 255, 0.15);
}
.refresh-packages-cancel-btn.data-v-460de224 {
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-size: 30rpx;
  font-weight: 500;
  border-radius: 45rpx;
  background: rgba(0, 0, 0, 0.05);
  color: #666;
  transition: all 0.3s;
}
.refresh-packages-cancel-btn.data-v-460de224:active {
  transform: scale(0.98);
  background: rgba(0, 0, 0, 0.08);
}