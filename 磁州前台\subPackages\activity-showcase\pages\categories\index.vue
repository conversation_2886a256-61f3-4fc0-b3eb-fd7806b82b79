<template>
  <view class="categories-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="15 18 9 12 15 6"></polyline>
          </svg>
        </view>
        <view class="navbar-title">商品分类</view>
        <view class="navbar-right">
          <view class="search-btn" @click="navigateToSearch">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="M21 21l-4.35-4.35"></path>
            </svg>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-container">
      <!-- 左侧分类列表 -->
      <scroll-view class="category-sidebar" scroll-y>
        <view 
          class="category-item" 
          v-for="(category, index) in categories" 
          :key="category.id"
          :class="{ active: currentCategory === category.id }"
          @click="switchCategory(category.id)"
        >
          <view class="category-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path v-if="category.type === 'food'" d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2M7 2v20M21 15V2a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3z"></path>
              <path v-else-if="category.type === 'clothing'" d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 21 12 17.27 5.82 21 7 13.87 2 9l6.91-.74L12 2z"></path>
              <path v-else-if="category.type === 'electronics'" d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
              <path v-else-if="category.type === 'home'" d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
              <path v-else-if="category.type === 'beauty'" d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 21 12 17.27 5.82 21 7 13.87 2 9l6.91-.74L12 2z"></path>
              <path v-else-if="category.type === 'sports'" d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
              <circle v-else cx="12" cy="12" r="10"></circle>
            </svg>
          </view>
          <text class="category-name">{{ category.name }}</text>
          <view class="category-badge" v-if="category.hot">
            <text class="badge-text">热</text>
          </view>
        </view>
      </scroll-view>

      <!-- 右侧子分类和商品 -->
      <scroll-view class="category-content" scroll-y @scrolltolower="loadMore">
        <!-- 子分类导航 -->
        <view class="subcategory-nav" v-if="currentSubcategories.length > 0">
          <scroll-view class="subcategory-scroll" scroll-x show-scrollbar="false">
            <view 
              class="subcategory-item" 
              v-for="(sub, index) in currentSubcategories" 
              :key="sub.id"
              :class="{ active: currentSubcategory === sub.id }"
              @click="switchSubcategory(sub.id)"
            >
              <text class="subcategory-name">{{ sub.name }}</text>
            </view>
          </scroll-view>
        </view>

        <!-- 热门推荐 -->
        <view class="hot-recommend" v-if="hotProducts.length > 0">
          <view class="section-header">
            <text class="section-title">热门推荐</text>
            <view class="section-more" @click="viewMoreHot">
              <text class="more-text">更多</text>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </view>
          </view>
          <scroll-view class="hot-scroll" scroll-x show-scrollbar="false">
            <view 
              class="hot-item" 
              v-for="(product, index) in hotProducts" 
              :key="product.id"
              @click="navigateToProduct(product)"
            >
              <view class="hot-image-container">
                <image class="hot-image" :src="product.image" mode="aspectFill" :lazy-load="true"></image>
                <view class="hot-badge">
                  <text class="badge-text">热</text>
                </view>
              </view>
              <text class="hot-name">{{ product.name }}</text>
              <text class="hot-price">¥{{ product.price }}</text>
            </view>
          </scroll-view>
        </view>

        <!-- 商品网格 -->
        <view class="products-section">
          <view class="section-header">
            <text class="section-title">{{ getCurrentCategoryName() }}</text>
            <view class="sort-btn" @click="showSortOptions">
              <text class="sort-text">{{ sortText }}</text>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            </view>
          </view>
          
          <view class="products-grid">
            <view 
              class="product-item" 
              v-for="(product, index) in filteredProducts" 
              :key="product.id"
              @click="navigateToProduct(product)"
            >
              <view class="product-image-container">
                <image class="product-image" :src="product.image" mode="aspectFill" :lazy-load="true"></image>
                <view class="product-badge" v-if="product.badge">
                  <text class="badge-text">{{ product.badge }}</text>
                </view>
                <view class="product-discount" v-if="product.discount">
                  <text class="discount-text">{{ product.discount }}折</text>
                </view>
              </view>
              <view class="product-info">
                <text class="product-name">{{ product.name }}</text>
                <text class="product-desc">{{ product.description }}</text>
                <view class="product-price">
                  <text class="current-price">¥{{ product.price }}</text>
                  <text class="original-price" v-if="product.originalPrice">¥{{ product.originalPrice }}</text>
                </view>
                <view class="product-stats">
                  <text class="sales-count">已售{{ product.sales }}</text>
                  <view class="product-rating">
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <polygon points="12 2 15.09 8.26 22 9 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9 8.91 8.26 12 2"></polygon>
                    </svg>
                    <text class="rating-text">{{ product.rating }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view class="loading-more" v-if="loading">
          <text>加载中...</text>
        </view>
        
        <!-- 没有更多数据 -->
        <view class="no-more" v-if="noMore && filteredProducts.length > 0">
          <text>已经到底啦~</text>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="filteredProducts.length === 0 && !loading">
          <view class="empty-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M16 16s-1.5-2-4-2-4 2-4 2"></path>
              <line x1="9" y1="9" x2="9.01" y2="9"></line>
              <line x1="15" y1="9" x2="15.01" y2="9"></line>
            </svg>
          </view>
          <text class="empty-text">暂无商品</text>
          <text class="empty-desc">该分类下暂时没有商品</text>
        </view>

        <!-- 底部安全区域 -->
        <view class="safe-area-bottom"></view>
      </scroll-view>
    </view>

    <!-- 排序选项弹窗 -->
    <view class="sort-modal" v-if="showSortModal" @click="hideSortOptions">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">排序方式</text>
          <view class="close-btn" @click="hideSortOptions">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </view>
        </view>
        <view class="sort-options">
          <view 
            class="sort-option" 
            v-for="(option, index) in sortOptions" 
            :key="index"
            :class="{ active: currentSort === option.value }"
            @click="selectSort(option.value)"
          >
            <text class="option-text">{{ option.label }}</text>
            <view class="option-check" v-if="currentSort === option.value">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="20 6 9 17 4 12"></polyline>
              </svg>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const currentCategory = ref('food')
const currentSubcategory = ref('all')
const currentSort = ref('default')
const loading = ref(false)
const noMore = ref(false)
const showSortModal = ref(false)

// 分类数据
const categories = ref([
  { id: 'food', name: '美食', type: 'food', hot: true },
  { id: 'clothing', name: '服装', type: 'clothing' },
  { id: 'electronics', name: '数码', type: 'electronics', hot: true },
  { id: 'home', name: '家居', type: 'home' },
  { id: 'beauty', name: '美妆', type: 'beauty' },
  { id: 'sports', name: '运动', type: 'sports' }
])

// 子分类数据
const subcategories = ref({
  food: [
    { id: 'all', name: '全部' },
    { id: 'snacks', name: '零食' },
    { id: 'drinks', name: '饮品' },
    { id: 'fruits', name: '水果' }
  ],
  clothing: [
    { id: 'all', name: '全部' },
    { id: 'tops', name: '上装' },
    { id: 'bottoms', name: '下装' },
    { id: 'shoes', name: '鞋靴' }
  ],
  electronics: [
    { id: 'all', name: '全部' },
    { id: 'phones', name: '手机' },
    { id: 'computers', name: '电脑' },
    { id: 'accessories', name: '配件' }
  ]
})

// 排序选项
const sortOptions = ref([
  { label: '默认排序', value: 'default' },
  { label: '价格从低到高', value: 'price_asc' },
  { label: '价格从高到低', value: 'price_desc' },
  { label: '销量优先', value: 'sales' },
  { label: '评分优先', value: 'rating' }
])

// 热门商品
const hotProducts = ref([
  {
    id: 1,
    name: '热销零食',
    image: '/static/images/products/snack1.jpg',
    price: 19.9
  },
  {
    id: 2,
    name: '人气饮品',
    image: '/static/images/products/drink1.jpg',
    price: 15.8
  }
])

// 商品数据
const products = ref([
  {
    id: 1,
    name: '美味零食大礼包',
    description: '多种口味，营养健康',
    image: '/static/images/products/snack1.jpg',
    price: 29.9,
    originalPrice: 39.9,
    sales: 1200,
    rating: 4.8,
    category: 'food',
    subcategory: 'snacks',
    badge: '热销',
    discount: 7.5
  },
  {
    id: 2,
    name: '新鲜水果拼盘',
    description: '当季新鲜水果，营养丰富',
    image: '/static/images/products/fruit1.jpg',
    price: 35.0,
    sales: 800,
    rating: 4.9,
    category: 'food',
    subcategory: 'fruits'
  }
])

// 计算属性
const currentSubcategories = computed(() => {
  return subcategories.value[currentCategory.value] || []
})

const filteredProducts = computed(() => {
  let filtered = products.value.filter(product => {
    if (product.category !== currentCategory.value) return false
    if (currentSubcategory.value !== 'all' && product.subcategory !== currentSubcategory.value) return false
    return true
  })

  // 排序
  switch (currentSort.value) {
    case 'price_asc':
      filtered.sort((a, b) => a.price - b.price)
      break
    case 'price_desc':
      filtered.sort((a, b) => b.price - a.price)
      break
    case 'sales':
      filtered.sort((a, b) => b.sales - a.sales)
      break
    case 'rating':
      filtered.sort((a, b) => b.rating - a.rating)
      break
  }

  return filtered
})

const sortText = computed(() => {
  const option = sortOptions.value.find(opt => opt.value === currentSort.value)
  return option ? option.label : '默认排序'
})

// 页面加载
onMounted(() => {
  console.log('商品分类页面加载')
  loadCategoryData()
})

// 方法
function goBack() {
  uni.navigateBack()
}

function navigateToSearch() {
  uni.navigateTo({
    url: '/subPackages/activity-showcase/pages/search/index'
  })
}

function switchCategory(categoryId) {
  currentCategory.value = categoryId
  currentSubcategory.value = 'all'
}

function switchSubcategory(subcategoryId) {
  currentSubcategory.value = subcategoryId
}

function getCurrentCategoryName() {
  const category = categories.value.find(cat => cat.id === currentCategory.value)
  return category ? category.name : '商品'
}

function showSortOptions() {
  showSortModal.value = true
}

function hideSortOptions() {
  showSortModal.value = false
}

function selectSort(sortValue) {
  currentSort.value = sortValue
  hideSortOptions()
}

function navigateToProduct(product) {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/products/detail/index?id=${product.id}`
  })
}

function viewMoreHot() {
  uni.navigateTo({
    url: '/subPackages/activity-showcase/pages/products/hot/index'
  })
}

function loadMore() {
  if (loading.value || noMore.value) return
  
  loading.value = true
  
  // 模拟加载更多数据
  setTimeout(() => {
    noMore.value = true
    loading.value = false
  }, 1000)
}

function loadCategoryData() {
  // 模拟加载分类数据
  setTimeout(() => {
    console.log('分类数据加载完成')
  }, 500)
}
</script>

<style scoped>
/* 商品分类样式开始 */
.categories-container {
  height: 100vh;
  background: #f7f7f7;
}

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(156, 39, 176, 0.95);
  backdrop-filter: blur(10px);
  padding-top: var(--status-bar-height, 44px);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
}

.back-btn, .search-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.2);
}

.back-btn svg, .search-btn svg {
  color: white;
}

.navbar-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
}

/* 内容容器样式 */
.content-container {
  display: flex;
  height: 100vh;
  padding-top: calc(var(--status-bar-height, 44px) + 44px);
}

/* 左侧分类列表样式 */
.category-sidebar {
  width: 100px;
  background: white;
  border-right: 1px solid #f0f0f0;
}

.category-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 8px;
  gap: 6px;
  transition: all 0.3s ease;
}

.category-item.active {
  background: #f8f9fa;
}

.category-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 24px;
  background: #9C27B0;
  border-radius: 0 2px 2px 0;
}

.category-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: #f5f5f5;
  transition: all 0.3s ease;
}

.category-item.active .category-icon {
  background: #9C27B0;
}

.category-icon svg {
  color: #666;
}

.category-item.active .category-icon svg {
  color: white;
}

.category-name {
  font-size: 12px;
  color: #333;
  text-align: center;
}

.category-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #FF5722;
  border-radius: 8px;
}

.category-badge .badge-text {
  font-size: 10px;
  color: white;
  font-weight: 500;
}

/* 右侧内容区域样式 */
.category-content {
  flex: 1;
  background: #f7f7f7;
}

/* 子分类导航样式 */
.subcategory-nav {
  background: white;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.subcategory-scroll {
  white-space: nowrap;
  padding: 0 16px;
}

.subcategory-item {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  margin-right: 8px;
  background: #f5f5f5;
  border-radius: 16px;
  transition: all 0.3s ease;
}

.subcategory-item.active {
  background: #9C27B0;
}

.subcategory-name {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}

.subcategory-item.active .subcategory-name {
  color: white;
}

/* 热门推荐样式 */
.hot-recommend {
  background: white;
  padding: 16px;
  margin-bottom: 8px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-more {
  display: flex;
  align-items: center;
  gap: 4px;
}

.more-text {
  font-size: 14px;
  color: #9C27B0;
}

.section-more svg {
  color: #9C27B0;
}

.hot-scroll {
  white-space: nowrap;
}

.hot-item {
  display: inline-block;
  width: 100px;
  margin-right: 12px;
  text-align: center;
}

.hot-image-container {
  position: relative;
  width: 100px;
  height: 100px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 8px;
}

.hot-image {
  width: 100%;
  height: 100%;
}

.hot-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #FF5722;
  border-radius: 8px;
}

.hot-badge .badge-text {
  font-size: 10px;
  color: white;
  font-weight: 500;
}

.hot-name {
  display: block;
  font-size: 12px;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.hot-price {
  font-size: 14px;
  color: #9C27B0;
  font-weight: 600;
}

/* 商品区域样式 */
.products-section {
  background: white;
  padding: 16px;
}

.sort-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: #f5f5f5;
  border-radius: 16px;
}

.sort-text {
  font-size: 12px;
  color: #666;
}

.sort-btn svg {
  color: #666;
}

/* 商品网格样式 */
.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-top: 12px;
}

.product-item {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: transform 0.3s ease;
}

.product-item:active {
  transform: scale(0.98);
}

.product-image-container {
  position: relative;
  width: 100%;
  height: 120px;
}

.product-image {
  width: 100%;
  height: 100%;
}

.product-badge {
  position: absolute;
  top: 6px;
  left: 6px;
  padding: 2px 6px;
  background: #FF5722;
  border-radius: 8px;
}

.product-badge .badge-text {
  font-size: 10px;
  color: white;
  font-weight: 500;
}

.product-discount {
  position: absolute;
  top: 6px;
  right: 6px;
  padding: 2px 6px;
  background: #FF9800;
  border-radius: 8px;
}

.discount-text {
  font-size: 10px;
  color: white;
  font-weight: 500;
}

.product-info {
  padding: 12px;
}

.product-name {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-desc {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 6px;
}

.current-price {
  font-size: 16px;
  font-weight: 600;
  color: #9C27B0;
}

.original-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

.product-stats {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sales-count {
  font-size: 12px;
  color: #666;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 2px;
}

.product-rating svg {
  color: #FFD700;
  fill: #FFD700;
}

.rating-text {
  font-size: 12px;
  color: #666;
}

/* 加载状态样式 */
.loading-more, .no-more {
  text-align: center;
  padding: 20px;
  color: #666;
  font-size: 14px;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 20px;
}

.empty-icon {
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-icon svg {
  color: #999;
}

.empty-text {
  font-size: 16px;
  color: #666;
  font-weight: 500;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: #999;
  text-align: center;
}

/* 排序弹窗样式 */
.sort-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 2000;
}

.modal-content {
  background: white;
  border-radius: 16px 16px 0 0;
  width: 100%;
  max-height: 50vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: #f5f5f5;
}

.close-btn svg {
  color: #666;
}

.sort-options {
  padding: 0 20px 20px;
}

.sort-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.sort-option:last-child {
  border-bottom: none;
}

.sort-option.active {
  color: #9C27B0;
}

.option-text {
  font-size: 16px;
  color: #333;
}

.sort-option.active .option-text {
  color: #9C27B0;
  font-weight: 500;
}

.option-check svg {
  color: #9C27B0;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  background: transparent;
}
/* 商品分类样式结束 */
</style>
