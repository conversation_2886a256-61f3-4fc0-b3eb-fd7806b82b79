<!-- 秒杀活动详情页面 (detail.vue) -->
<template>
  <view class="flash-detail-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-left">
        <view class="back-button" @tap="goBack">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 18L9 12L15 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </view>
      </view>
      <view class="navbar-title">
        <text class="title-text">秒杀活动详情</text>
      </view>
      <view class="navbar-right">
        <view class="more-button" @tap="showMoreOptions">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="6" r="2" fill="white"/>
            <circle cx="12" cy="12" r="2" fill="white"/>
            <circle cx="12" cy="18" r="2" fill="white"/>
          </svg>
        </view>
      </view>
    </view>
    
    <!-- 页面内容区域 -->
    <scroll-view scroll-y class="content-area">
      <!-- 商品信息区域 -->
      <view class="product-section">
        <swiper class="product-swiper" indicator-dots autoplay circular>
          <swiper-item v-for="(image, index) in flashSaleData.images" :key="index">
            <image class="product-image" :src="image" mode="aspectFill"></image>
          </swiper-item>
        </swiper>
        
        <view class="product-info">
          <view class="status-tag" :class="flashSaleData.statusClass">{{flashSaleData.statusText}}</view>
          <view class="product-name">{{flashSaleData.name}}</view>
          <view class="product-price">
            <text class="price-now">¥{{flashSaleData.flashPrice}}</text>
            <text class="price-original">¥{{flashSaleData.originalPrice}}</text>
            <text class="discount-tag">{{flashSaleData.discountText}}</text>
          </view>
          
          <view class="countdown-section" v-if="flashSaleData.status !== 'ended'">
            <view class="countdown-label">{{countdownLabel}}</view>
            <view class="countdown-timer">
              <text class="time-block">{{countdown.days}}</text>
              <text class="time-separator">天</text>
              <text class="time-block">{{countdown.hours}}</text>
              <text class="time-separator">:</text>
              <text class="time-block">{{countdown.minutes}}</text>
              <text class="time-separator">:</text>
              <text class="time-block">{{countdown.seconds}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 销售数据区域 -->
      <view class="sales-section">
        <view class="sales-header">
          <text class="section-title">销售数据</text>
          <view class="refresh-button" @tap="refreshData">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M23 4V10H17" stroke="#FF7600" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M1 20V14H7" stroke="#FF7600" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M3.51 9.00001C4.01717 7.56645 4.87913 6.2765 6.01547 5.27651C7.1518 4.27653 8.52547 3.60084 10 3.31677C11.4745 3.03271 13.0047 3.15505 14.4111 3.67167C15.8175 4.18829 17.0512 5.07723 17.99 6.24001L23 10M1 14L6.01 17.76C6.94879 18.9228 8.18246 19.8117 9.58886 20.3283C10.9953 20.845 12.5255 20.9673 14 20.6832C15.4745 20.3992 16.8482 19.7235 17.9845 18.7235C19.1209 17.7235 19.9828 16.4336 20.49 15" stroke="#FF7600" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <text class="refresh-text">刷新</text>
          </view>
        </view>
        
        <view class="sales-stats">
          <view class="stat-item">
            <view class="stat-value">{{flashSaleData.viewCount}}</view>
            <view class="stat-label">浏览量</view>
          </view>
          <view class="stat-item">
            <view class="stat-value">{{flashSaleData.stockSold}}</view>
            <view class="stat-label">已售数量</view>
          </view>
          <view class="stat-item">
            <view class="stat-value">{{flashSaleData.stockRemain}}</view>
            <view class="stat-label">剩余库存</view>
          </view>
          <view class="stat-item">
            <view class="stat-value">{{flashSaleData.conversionRate}}%</view>
            <view class="stat-label">转化率</view>
          </view>
        </view>
        
        <view class="progress-bar">
          <view class="progress-inner" :style="{ width: flashSaleData.progressPercent + '%' }"></view>
        </view>
        <view class="progress-text">
          <text>已售{{flashSaleData.progressPercent}}%</text>
          <text>剩余{{flashSaleData.stockRemain}}件</text>
        </view>
      </view>
      
      <!-- 活动详情区域 -->
      <view class="detail-section">
        <view class="section-header">
          <text class="section-title">活动详情</text>
        </view>
        
        <view class="detail-items">
          <view class="detail-item">
            <view class="item-label">活动时间</view>
            <view class="item-value">{{flashSaleData.timeRange}}</view>
          </view>
          <view class="detail-item">
            <view class="item-label">活动库存</view>
            <view class="item-value">{{flashSaleData.stockTotal}}件</view>
          </view>
          <view class="detail-item">
            <view class="item-label">限购数量</view>
            <view class="item-value">{{flashSaleData.purchaseLimit > 0 ? flashSaleData.purchaseLimit + '件/人' : '不限购'}}</view>
          </view>
          <view class="detail-item">
            <view class="item-label">活动规则</view>
            <view class="item-value">{{flashSaleData.rules}}</view>
          </view>
        </view>
      </view>
      
      <!-- 商品详情区域 -->
      <view class="product-detail-section">
        <view class="section-header">
          <text class="section-title">商品详情</text>
        </view>
        
        <rich-text class="product-description" :nodes="flashSaleData.description"></rich-text>
        
        <view class="product-images">
          <image 
            v-for="(image, index) in flashSaleData.detailImages" 
            :key="index" 
            class="detail-image" 
            :src="image" 
            mode="widthFix"
          ></image>
        </view>
      </view>
      
      <!-- 订单记录区域 -->
      <view class="orders-section">
        <view class="section-header">
          <text class="section-title">订单记录</text>
          <view class="view-all" @tap="viewAllOrders">
            <text class="view-all-text">查看全部</text>
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <view class="orders-list" v-if="flashSaleData.orders.length > 0">
          <view class="order-item" v-for="(order, index) in flashSaleData.orders" :key="index">
            <view class="order-user">
              <image class="user-avatar" :src="order.userAvatar" mode="aspectFill"></image>
              <text class="user-name">{{order.userName}}</text>
            </view>
            <view class="order-info">
              <text class="order-quantity">{{order.quantity}}件</text>
              <text class="order-time">{{order.time}}</text>
            </view>
          </view>
        </view>
        
        <view class="empty-orders" v-else>
          <image class="empty-image" src="/static/images/empty-orders.png" mode="aspectFit"></image>
          <text class="empty-text">暂无订单记录</text>
        </view>
      </view>
      
      <!-- 底部空间 -->
      <view class="bottom-space"></view>
    </scroll-view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="action-buttons">
        <view class="action-button edit" @tap="editFlashSale">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="#5E5CE6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M18.5 2.50001C18.8978 2.10219 19.4374 1.87869 20 1.87869C20.5626 1.87869 21.1022 2.10219 21.5 2.50001C21.8978 2.89784 22.1213 3.4374 22.1213 4.00001C22.1213 4.56262 21.8978 5.10219 21.5 5.50001L12 15L8 16L9 12L18.5 2.50001Z" stroke="#5E5CE6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <text class="button-text">编辑</text>
        </view>
        <view class="action-button share" @tap="shareFlashSale">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 8C19.6569 8 21 6.65685 21 5C21 3.34315 19.6569 2 18 2C16.3431 2 15 3.34315 15 5C15 6.65685 16.3431 8 18 8Z" stroke="#34C759" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M6 15C7.65685 15 9 13.6569 9 12C9 10.3431 7.65685 9 6 9C4.34315 9 3 10.3431 3 12C3 13.6569 4.34315 15 6 15Z" stroke="#34C759" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M18 22C19.6569 22 21 20.6569 21 19C21 17.3431 19.6569 16 18 16C16.3431 16 15 17.3431 15 19C15 20.6569 16.3431 22 18 22Z" stroke="#34C759" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M8.59 13.51L15.42 17.49" stroke="#34C759" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M15.41 6.51L8.59 10.49" stroke="#34C759" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <text class="button-text">分享</text>
        </view>
        <view class="action-button" :class="flashSaleData.status === 'active' ? 'pause' : 'activate'" @tap="toggleStatus">
          <svg v-if="flashSaleData.status === 'active'" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="6" y="4" width="4" height="16" rx="1" stroke="#FF9500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <rect x="14" y="4" width="4" height="16" rx="1" stroke="#FF9500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M5 3L19 12L5 21V3Z" stroke="#34C759" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <text class="button-text">{{flashSaleData.status === 'active' ? '暂停' : '启用'}}</text>
        </view>
        <view class="action-button delete" @tap="deleteFlashSale">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M3 6H5H21" stroke="#FF3B30" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="#FF3B30" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <text class="button-text">删除</text>
        </view>
      </view>
    </view>
    
    <!-- 更多选项弹窗 -->
    <view class="more-options-popup" v-if="showOptions" @tap="hideMoreOptions">
      <view class="popup-mask"></view>
      <view class="popup-content" @tap.stop>
        <view class="popup-option" @tap="viewQrCode">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="3" y="3" width="7" height="7" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <rect x="14" y="3" width="7" height="7" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <rect x="3" y="14" width="7" height="7" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <rect x="14" y="14" width="7" height="7" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <text>查看二维码</text>
        </view>
        <view class="popup-option" @tap="viewDataAnalysis">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 20V10" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M12 20V4" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M6 20V14" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <text>数据分析</text>
        </view>
        <view class="popup-option" @tap="copyLink">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10 13C10.4295 13.5741 10.9774 14.0492 11.6066 14.3929C12.2357 14.7367 12.9315 14.9411 13.6467 14.9923C14.3618 15.0435 15.0796 14.9404 15.7513 14.6898C16.4231 14.4392 17.0331 14.0471 17.54 13.54L20.54 10.54C21.4508 9.59699 21.9548 8.33397 21.9434 7.02299C21.932 5.71201 21.4061 4.45794 20.4791 3.5309C19.5521 2.60386 18.298 2.07802 16.987 2.06663C15.676 2.05523 14.413 2.55921 13.47 3.47L11.75 5.18" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M14 11C13.5705 10.4259 13.0226 9.95083 12.3934 9.60706C11.7642 9.2633 11.0685 9.05889 10.3533 9.00768C9.63816 8.95646 8.92037 9.05964 8.24861 9.31023C7.57685 9.56082 6.96684 9.95294 6.45996 10.46L3.45996 13.46C2.54917 14.403 2.04519 15.666 2.05659 16.977C2.06798 18.288 2.59382 19.5421 3.52086 20.4691C4.4479 21.3961 5.70197 21.922 7.01295 21.9334C8.32393 21.9448 9.58694 21.4408 10.53 20.53L12.24 18.82" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <text>复制链接</text>
        </view>
        <view class="popup-option" @tap="exportData">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M7 10L12 15L17 10" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M12 15V3" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <text>导出数据</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';

// 页面参数
const flashId = ref(null);

// 弹窗控制
const showOptions = ref(false);

// 秒杀活动数据
const flashSaleData = reactive({
  id: 1,
  name: '夏季清凉风扇特惠',
  flashPrice: 59.9,
  originalPrice: 129.9,
  discountText: '4.6折',
  stockTotal: 100,
  stockSold: 86,
  stockRemain: 14,
  viewCount: 1564,
  conversionRate: 5.5,
  progressPercent: 86,
  images: [
    '/static/images/flash-item1.jpg',
    '/static/images/flash-item1-2.jpg',
    '/static/images/flash-item1-3.jpg'
  ],
  detailImages: [
    '/static/images/flash-detail1.jpg',
    '/static/images/flash-detail2.jpg',
    '/static/images/flash-detail3.jpg'
  ],
  timeRange: '2023-07-01 12:00 ~ 14:00',
  status: 'active',
  statusText: '进行中',
  statusClass: 'status-active',
  purchaseLimit: 2,
  rules: '活动期间每人限购2件，秒杀商品不支持退换货，数量有限，先到先得',
  description: '<p>这是一款高品质的夏季清凉风扇，采用先进技术，风力强劲，静音设计，让您的夏天更加舒适。</p><p>规格参数：</p><ul><li>功率：30W</li><li>风速：3档可调</li><li>电池容量：4000mAh</li><li>续航时间：4-8小时</li><li>材质：环保ABS</li></ul>',
  orders: [
    {
      userName: '张先生',
      userAvatar: '/static/images/avatar1.jpg',
      quantity: 1,
      time: '2023-07-01 12:05'
    },
    {
      userName: '李女士',
      userAvatar: '/static/images/avatar2.jpg',
      quantity: 2,
      time: '2023-07-01 12:08'
    },
    {
      userName: '王先生',
      userAvatar: '/static/images/avatar3.jpg',
      quantity: 1,
      time: '2023-07-01 12:15'
    }
  ]
});

// 倒计时数据
const countdown = reactive({
  days: '00',
  hours: '01',
  minutes: '25',
  seconds: '36'
});

// 倒计时标签
const countdownLabel = computed(() => {
  if (flashSaleData.status === 'upcoming') {
    return '距开始还剩';
  } else if (flashSaleData.status === 'active') {
    return '距结束还剩';
  }
  return '';
});

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 显示更多选项
const showMoreOptions = () => {
  showOptions.value = true;
};

// 隐藏更多选项
const hideMoreOptions = () => {
  showOptions.value = false;
};

// 刷新数据
const refreshData = () => {
  uni.showLoading({
    title: '刷新中...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: '数据已更新',
      icon: 'success'
    });
  }, 1000);
};

// 编辑秒杀活动
const editFlashSale = () => {
  uni.navigateTo({
    url: `/subPackages/merchant-admin-marketing/pages/marketing/flash/edit?id=${flashSaleData.id}`
  });
};

// 分享秒杀活动
const shareFlashSale = () => {
  uni.showToast({
    title: '生成分享链接中...',
    icon: 'loading',
    duration: 1500
  });
  
  setTimeout(() => {
    uni.showModal({
      title: '分享秒杀活动',
      content: `活动"${flashSaleData.name}"的分享链接已创建，可发送给客户或分享到社交媒体`,
      confirmText: '复制链接',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          uni.setClipboardData({
            data: `https://example.com/flash-sale/${flashSaleData.id}`,
            success: () => {
              uni.showToast({
                title: '链接已复制',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  }, 1500);
};

// 切换活动状态
const toggleStatus = () => {
  const action = flashSaleData.status === 'active' ? '暂停' : '启用';
  
  uni.showModal({
    title: `确认${action}`,
    content: `确定要${action}该秒杀活动吗？`,
    confirmText: '确定',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        if (flashSaleData.status === 'active') {
          flashSaleData.status = 'upcoming';
          flashSaleData.statusText = '未开始';
          flashSaleData.statusClass = 'status-upcoming';
        } else {
          flashSaleData.status = 'active';
          flashSaleData.statusText = '进行中';
          flashSaleData.statusClass = 'status-active';
        }
        
        uni.showToast({
          title: `${action}成功`,
          icon: 'success'
        });
      }
    }
  });
};

// 删除秒杀活动
const deleteFlashSale = () => {
  uni.showModal({
    title: '确认删除',
    content: `确定要删除"${flashSaleData.name}"秒杀活动吗？一旦删除将无法恢复。`,
    confirmText: '删除',
    confirmColor: '#FF3B30',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '删除成功',
          icon: 'success',
          duration: 1500,
          success: () => {
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          }
        });
      }
    }
  });
};

// 查看全部订单
const viewAllOrders = () => {
  uni.navigateTo({
    url: `/subPackages/merchant-admin-marketing/pages/marketing/flash/orders?id=${flashSaleData.id}`
  });
};

// 查看二维码
const viewQrCode = () => {
  hideMoreOptions();
  uni.showLoading({
    title: '生成二维码...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    uni.previewImage({
      urls: ['/static/images/flash-qrcode.png']
    });
  }, 1000);
};

// 查看数据分析
const viewDataAnalysis = () => {
  hideMoreOptions();
  uni.navigateTo({
    url: `/subPackages/merchant-admin-marketing/pages/marketing/flash/analysis?id=${flashSaleData.id}`
  });
};

// 复制链接
const copyLink = () => {
  hideMoreOptions();
  uni.setClipboardData({
    data: `https://example.com/flash-sale/${flashSaleData.id}`,
    success: () => {
      uni.showToast({
        title: '链接已复制',
        icon: 'success'
      });
    }
  });
};

// 导出数据
const exportData = () => {
  hideMoreOptions();
  uni.showLoading({
    title: '导出中...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: '数据已导出',
      icon: 'success'
    });
  }, 1500);
};

// 页面加载
onMounted(() => {
  // 获取页面参数
  const eventChannel = getOpenerEventChannel();
  const query = uni.getSystemInfoSync().platform === 'h5' ? location.href.split('?')[1] : '';
  
  if (query) {
    const params = query.split('&').reduce((res, item) => {
      const [key, value] = item.split('=');
      res[key] = value;
      return res;
    }, {});
    
    flashId.value = params.id;
  } else {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    if (currentPage && currentPage.options) {
      flashId.value = currentPage.options.id;
    }
  }
  
  // 加载秒杀活动数据
  loadFlashSaleData();
  
  // 启动倒计时
  startCountdown();
});

// 加载秒杀活动数据
const loadFlashSaleData = () => {
  // 实际应用中应该从API获取数据
  console.log('加载秒杀活动数据, ID:', flashId.value);
  
  // 模拟API请求
  uni.showLoading({
    title: '加载中...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    // 实际应用中这里应该使用API返回的数据更新flashSaleData
  }, 500);
};

// 启动倒计时
const startCountdown = () => {
  // 设置目标时间（示例：当前时间加上1小时30分钟）
  const targetTime = new Date();
  if (flashSaleData.status === 'upcoming') {
    // 未开始，倒计时到开始时间
    targetTime.setHours(targetTime.getHours() + 2);
  } else if (flashSaleData.status === 'active') {
    // 进行中，倒计时到结束时间
    targetTime.setHours(targetTime.getHours() + 1);
    targetTime.setMinutes(targetTime.getMinutes() + 30);
  }
  
  // 更新倒计时
  const updateCountdown = () => {
    const now = new Date();
    const diff = targetTime - now;
    
    if (diff <= 0) {
      // 倒计时结束
      countdown.days = '00';
      countdown.hours = '00';
      countdown.minutes = '00';
      countdown.seconds = '00';
      
      // 更新活动状态
      if (flashSaleData.status === 'upcoming') {
        flashSaleData.status = 'active';
        flashSaleData.statusText = '进行中';
        flashSaleData.statusClass = 'status-active';
        // 重新启动倒计时到结束时间
        startCountdown();
      } else if (flashSaleData.status === 'active') {
        flashSaleData.status = 'ended';
        flashSaleData.statusText = '已结束';
        flashSaleData.statusClass = 'status-ended';
      }
      
      return;
    }
    
    // 计算天、时、分、秒
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);
    
    // 更新倒计时数据
    countdown.days = days.toString().padStart(2, '0');
    countdown.hours = hours.toString().padStart(2, '0');
    countdown.minutes = minutes.toString().padStart(2, '0');
    countdown.seconds = seconds.toString().padStart(2, '0');
    
    // 继续倒计时
    setTimeout(updateCountdown, 1000);
  };
  
  // 开始倒计时
  updateCountdown();
};
</script>

<style lang="scss">
/* 页面容器 */
.flash-detail-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
  position: relative;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  color: white;
  padding: 48px 20px 16px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(255, 120, 0, 0.15);
}

.navbar-left {
  width: 40px;
}

.back-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-title {
  flex: 1;
  text-align: center;
}

.title-text {
  font-size: 18px;
  font-weight: 600;
}

.navbar-right {
  width: 40px;
  display: flex;
  justify-content: flex-end;
}

.more-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 内容区域 */
.content-area {
  flex: 1;
  box-sizing: border-box;
  height: calc(100vh - 80px - 60px);
}

/* 商品信息区域 */
.product-section {
  background: #FFFFFF;
  margin-bottom: 12px;
}

.product-swiper {
  width: 100%;
  height: 300px;
}

.product-image {
  width: 100%;
  height: 100%;
}

.product-info {
  padding: 16px;
  position: relative;
}

.status-tag {
  position: absolute;
  top: -20px;
  right: 16px;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  color: white;
  z-index: 10;
}

.status-active {
  background: #34C759;
}

.status-upcoming {
  background: #007AFF;
}

.status-ended {
  background: #8E8E93;
}

.product-name {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12px;
  line-height: 1.4;
}

.product-price {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.price-now {
  font-size: 24px;
  font-weight: 700;
  color: #FF3B30;
  margin-right: 8px;
}

.price-original {
  font-size: 16px;
  color: #999999;
  text-decoration: line-through;
  margin-right: 8px;
}

.discount-tag {
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.countdown-section {
  background: #FFF8F0;
  border-radius: 8px;
  padding: 12px;
  margin-top: 8px;
}

.countdown-label {
  font-size: 14px;
  color: #FF7600;
  margin-bottom: 8px;
}

.countdown-timer {
  display: flex;
  align-items: center;
  justify-content: center;
}

.time-block {
  background: #FF7600;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 600;
  min-width: 32px;
  text-align: center;
}

.time-separator {
  margin: 0 4px;
  color: #FF7600;
  font-weight: 600;
}

/* 销售数据区域 */
.sales-section {
  background: #FFFFFF;
  padding: 16px;
  margin-bottom: 12px;
}

.sales-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 17px;
  font-weight: 600;
  color: #333333;
}

.refresh-button {
  display: flex;
  align-items: center;
  color: #FF7600;
}

.refresh-text {
  font-size: 14px;
  margin-left: 4px;
}

.sales-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #999999;
}

.progress-bar {
  height: 8px;
  background: #F0F0F0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-inner {
  height: 100%;
  background: linear-gradient(90deg, #FF7600, #FF3C00);
  border-radius: 4px;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666666;
}

/* 活动详情区域 */
.detail-section {
  background: #FFFFFF;
  padding: 16px;
  margin-bottom: 12px;
}

.section-header {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-items {
  border-radius: 8px;
  overflow: hidden;
}

.detail-item {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid #F5F5F5;
}

.detail-item:last-child {
  border-bottom: none;
}

.item-label {
  width: 80px;
  font-size: 14px;
  color: #666666;
}

.item-value {
  flex: 1;
  font-size: 14px;
  color: #333333;
  line-height: 1.5;
}

/* 商品详情区域 */
.product-detail-section {
  background: #FFFFFF;
  padding: 16px;
  margin-bottom: 12px;
}

.product-description {
  font-size: 14px;
  color: #333333;
  line-height: 1.6;
  margin-bottom: 16px;
}

.product-images {
  width: 100%;
}

.detail-image {
  width: 100%;
  margin-bottom: 12px;
}

/* 订单记录区域 */
.orders-section {
  background: #FFFFFF;
  padding: 16px;
  margin-bottom: 12px;
}

.view-all {
  display: flex;
  align-items: center;
  color: #666666;
}

.view-all-text {
  font-size: 14px;
}

.arrow-icon {
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #666666;
  transform: rotate(-90deg);
  margin-left: 4px;
}

.orders-list {
  margin-top: 8px;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #F5F5F5;
}

.order-item:last-child {
  border-bottom: none;
}

.order-user {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  margin-right: 8px;
}

.user-name {
  font-size: 14px;
  color: #333333;
}

.order-info {
  text-align: right;
}

.order-quantity {
  font-size: 14px;
  color: #333333;
  margin-bottom: 4px;
}

.order-time {
  font-size: 12px;
  color: #999999;
}

.empty-orders {
  padding: 24px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-image {
  width: 80px;
  height: 80px;
  margin-bottom: 12px;
}

.empty-text {
  font-size: 14px;
  color: #999999;
}

/* 底部空间 */
.bottom-space {
  height: 20px;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #FFFFFF;
  padding: 10px 16px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 90;
}

.action-buttons {
  display: flex;
  justify-content: space-around;
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 5px 0;
}

.button-text {
  font-size: 12px;
  margin-top: 4px;
}

.action-button.edit {
  color: #5E5CE6;
}

.action-button.share {
  color: #34C759;
}

.action-button.pause {
  color: #FF9500;
}

.action-button.activate {
  color: #34C759;
}

.action-button.delete {
  color: #FF3B30;
}

/* 更多选项弹窗 */
.more-options-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}

.popup-content {
  position: absolute;
  top: 80px;
  right: 16px;
  background: #FFFFFF;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  width: 160px;
}

.popup-option {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  font-size: 14px;
  color: #333333;
}

.popup-option:active {
  background: #F5F5F5;
}

.popup-option svg {
  margin-right: 8px;
}

@media screen and (min-width: 768px) {
  .product-swiper {
    height: 400px;
  }
  
  .action-buttons {
    max-width: 600px;
    margin: 0 auto;
  }
}
</style>