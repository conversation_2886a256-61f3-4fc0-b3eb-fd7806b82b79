/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-fd261dea, html.data-v-fd261dea, #app.data-v-fd261dea, .index-container.data-v-fd261dea {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.platforms-container.data-v-fd261dea {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.content-container.data-v-fd261dea {
  padding-top: calc(var(--status-bar-height) + 44px);
  padding-bottom: 20px;
}
.search-section.data-v-fd261dea {
  padding: 16px;
  background: linear-gradient(135deg, #AB47BC 0%, #9C27B0 100%);
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  padding-top: 24px;
}
.search-bar.data-v-fd261dea {
  display: flex;
  align-items: center;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 10px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.search-bar .search-icon.data-v-fd261dea {
  margin-right: 8px;
}
.search-bar .search-placeholder.data-v-fd261dea {
  color: #999999;
  font-size: 14px;
}
.platforms-grid.data-v-fd261dea {
  padding: 16px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}