<template>
  <view class="verification-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">核销中心</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 核销功能模块 -->
    <view class="verification-modules">
      <!-- 扫码核销 -->
      <view class="module-card" @click="navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/verification/scan')">
        <view class="module-icon scan">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="3" y1="9" x2="21" y2="9"></line>
            <line x1="3" y1="15" x2="21" y2="15"></line>
            <line x1="9" y1="3" x2="9" y2="21"></line>
            <line x1="15" y1="3" x2="15" y2="21"></line>
          </svg>
        </view>
        <view class="module-content">
          <text class="module-title">扫码核销</text>
          <text class="module-desc">扫描用户核销码完成核销</text>
        </view>
        <view class="module-arrow">
          <view class="arrow-icon"></view>
        </view>
      </view>
      
      <!-- 手动核销 -->
      <view class="module-card" @click="navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/verification/manual')">
        <view class="module-icon manual">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
          </svg>
        </view>
        <view class="module-content">
          <text class="module-title">手动核销</text>
          <text class="module-desc">手动输入核销码完成核销</text>
        </view>
        <view class="module-arrow">
          <view class="arrow-icon"></view>
        </view>
      </view>
      
      <!-- 核销记录 -->
      <view class="module-card" @click="navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/verification/records')">
        <view class="module-icon records">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
            <line x1="16" y1="13" x2="8" y2="13"></line>
            <line x1="16" y1="17" x2="8" y2="17"></line>
            <polyline points="10 9 9 9 8 9"></polyline>
          </svg>
        </view>
        <view class="module-content">
          <text class="module-title">核销记录</text>
          <text class="module-desc">查看历史核销记录</text>
        </view>
        <view class="module-arrow">
          <view class="arrow-icon"></view>
        </view>
      </view>
    </view>
    
    <!-- 最近核销记录 -->
    <view class="recent-records">
      <view class="section-header">
        <text class="section-title">最近核销</text>
        <text class="view-all" @click="navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/verification/records')">查看全部</text>
      </view>
      
      <view class="records-list">
        <view v-if="recentRecords.length === 0" class="empty-records">
          <text class="empty-text">暂无核销记录</text>
        </view>
        <view v-else class="record-item" v-for="(record, index) in recentRecords" :key="index">
          <view class="record-type" :class="record.typeClass">{{record.typeText}}</view>
          <view class="record-content">
            <view class="record-main">
              <text class="record-title">{{record.title}}</text>
              <text class="record-code">{{record.code}}</text>
            </view>
            <view class="record-info">
              <text class="record-user">用户：{{record.user}}</text>
              <text class="record-time">{{record.time}}</text>
            </view>
          </view>
          <view class="record-status" :class="record.statusClass">{{record.status}}</view>
        </view>
      </view>
    </view>
    
    <!-- 核销统计 -->
    <view class="verification-stats">
      <view class="section-header">
        <text class="section-title">核销统计</text>
        <view class="date-filter">
          <text class="date-text">{{dateRange}}</text>
          <view class="date-arrow"></view>
        </view>
      </view>
      
      <view class="stats-cards">
        <view class="stats-card">
          <text class="stats-value">{{stats.totalCount}}</text>
          <text class="stats-label">总核销数</text>
        </view>
        <view class="stats-card">
          <text class="stats-value">{{stats.todayCount}}</text>
          <text class="stats-label">今日核销</text>
        </view>
        <view class="stats-card">
          <text class="stats-value">¥{{stats.totalAmount}}</text>
          <text class="stats-label">核销金额</text>
        </view>
      </view>
      
      <view class="stats-chart">
        <text class="chart-title">近7日核销趋势</text>
        <view class="chart-container">
          <!-- 简易图表，实际项目中可以使用图表组件 -->
          <view class="chart-bars">
            <view class="chart-bar" v-for="(item, index) in chartData" :key="index" :style="{ height: item.height + '%' }">
              <text class="bar-value">{{item.value}}</text>
            </view>
          </view>
          <view class="chart-labels">
            <text class="label-text" v-for="(item, index) in chartData" :key="index">{{item.date}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      dateRange: '最近7天',
      recentRecords: [
        {
          typeText: '拼团',
          typeClass: 'type-group',
          title: '双人下午茶套餐拼团',
          code: 'GP20230618001',
          user: '张三',
          time: '今天 14:30',
          status: '已核销',
          statusClass: 'status-success'
        },
        {
          typeText: '优惠券',
          typeClass: 'type-coupon',
          title: '新店开业满100减20券',
          code: 'CP20230618002',
          user: '李四',
          time: '今天 11:15',
          status: '已核销',
          statusClass: 'status-success'
        },
        {
          typeText: '秒杀',
          typeClass: 'type-flash',
          title: '限时特价烤鸭套餐',
          code: 'FS20230617005',
          user: '王五',
          time: '昨天 18:45',
          status: '已核销',
          statusClass: 'status-success'
        }
      ],
      stats: {
        totalCount: 128,
        todayCount: 15,
        totalAmount: '12,846'
      },
      chartData: [
        { date: '6/12', value: 8, height: 40 },
        { date: '6/13', value: 12, height: 60 },
        { date: '6/14', value: 10, height: 50 },
        { date: '6/15', value: 15, height: 75 },
        { date: '6/16', value: 9, height: 45 },
        { date: '6/17', value: 14, height: 70 },
        { date: '6/18', value: 15, height: 75 }
      ]
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showHelp() {
      uni.showToast({
        title: '核销中心帮助',
        icon: 'none'
      });
    },
    navigateTo(url) {
      uni.navigateTo({
        url: url
      });
    }
  }
}
</script>

<style lang="scss">
.verification-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 20px;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #27ae60, #219150);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4px 20px rgba(39, 174, 96, 0.2);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 核销功能模块样式 */
.verification-modules {
  padding: 20px 15px;
}

.module-card {
  background: #FFFFFF;
  border-radius: 16px;
  margin-bottom: 16px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s, box-shadow 0.2s;
}

.module-card:active {
  transform: scale(0.98);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06);
}

.module-icon {
  width: 56px;
  height: 56px;
  border-radius: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 18px;
}

.module-icon svg {
  width: 28px;
  height: 28px;
}

.module-icon.scan {
  background-color: rgba(39, 174, 96, 0.15);
  color: #27ae60;
}

.module-icon.manual {
  background-color: rgba(41, 128, 185, 0.15);
  color: #2980b9;
}

.module-icon.records {
  background-color: rgba(243, 156, 18, 0.15);
  color: #f39c12;
}

.module-content {
  flex: 1;
}

.module-title {
  font-size: 17px;
  font-weight: 600;
  color: #333;
  margin-bottom: 6px;
}

.module-desc {
  font-size: 13px;
  color: #999;
}

.module-arrow {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-icon {
  width: 8px;
  height: 8px;
  border-top: 2px solid #CCC;
  border-right: 2px solid #CCC;
  transform: rotate(45deg);
}

/* 最近核销记录样式 */
.recent-records {
  margin: 0 15px 20px;
  background: #FFFFFF;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
}

.section-title {
  font-size: 17px;
  font-weight: 600;
  color: #333;
}

.view-all {
  font-size: 14px;
  color: #2980b9;
  font-weight: 500;
}

.empty-records {
  padding: 30px 0;
  text-align: center;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

.record-item {
  display: flex;
  align-items: center;
  padding: 14px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.record-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.record-type {
  width: 60px;
  height: 28px;
  border-radius: 14px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 14px;
  font-weight: 500;
}

.type-group {
  background-color: rgba(39, 174, 96, 0.15);
  color: #27ae60;
}

.type-coupon {
  background-color: rgba(41, 128, 185, 0.15);
  color: #2980b9;
}

.type-flash {
  background-color: rgba(231, 76, 60, 0.15);
  color: #e74c3c;
}

.record-content {
  flex: 1;
}

.record-main {
  margin-bottom: 4px;
}

.record-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-right: 8px;
}

.record-code {
  font-size: 12px;
  color: #999;
}

.record-info {
  display: flex;
  font-size: 12px;
  color: #999;
}

.record-user {
  margin-right: 12px;
}

.record-status {
  font-size: 13px;
  font-weight: 500;
}

.status-success {
  color: #30D158;
}

.status-pending {
  color: #FF9500;
}

.status-failed {
  color: #FF453A;
}

/* 核销统计样式 */
.verification-stats {
  margin: 0 15px 20px;
  background: #FFFFFF;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

.date-filter {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 16px;
  padding: 6px 12px;
}

.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.date-arrow {
  width: 8px;
  height: 8px;
  border-top: 2px solid #666;
  border-right: 2px solid #666;
  transform: rotate(135deg);
}

.stats-cards {
  display: flex;
  justify-content: space-between;
  margin: 20px 0;
}

.stats-card {
  flex: 1;
  text-align: center;
  padding: 15px 0;
  position: relative;
}

.stats-card:not(:last-child):after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  height: 60%;
  width: 1px;
  background: rgba(0, 0, 0, 0.05);
}

.stats-value {
  font-size: 22px;
  font-weight: 700;
  color: #333;
  margin-bottom: 6px;
  display: block;
}

.stats-label {
  font-size: 12px;
  color: #999;
}

.stats-chart {
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding-top: 20px;
}

.chart-title {
  font-size: 15px;
  color: #666;
  margin-bottom: 20px;
  display: block;
  font-weight: 500;
}

.chart-container {
  height: 160px;
}

.chart-bars {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 120px;
}

.chart-bar {
  width: 36px;
  background: linear-gradient(180deg, #27ae60, #219150);
  border-radius: 4px 4px 0 0;
  position: relative;
  display: flex;
  justify-content: center;
}

.bar-value {
  position: absolute;
  top: -20px;
  font-size: 12px;
  color: #666;
}

.chart-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
}

.label-text {
  width: 30px;
  text-align: center;
  font-size: 12px;
  color: #999;
}
</style>