<template>
  <view class="points-rules-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-left">
        <view class="back-button" @tap="goBack">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 18L9 12L15 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </view>
      </view>
      <view class="navbar-title">
        <text class="title-text">积分规则设置</text>
      </view>
      <view class="navbar-right">
        <!-- 占位 -->
      </view>
    </view>
    
    <!-- 页面内容区域 -->
    <scroll-view scroll-y class="content-area">
      <view class="rules-card">
        <view class="card-header">
          <text class="card-title">积分规则设置</text>
          <view class="edit-text" @tap="saveRules" v-if="isEditing">保存</view>
          <view class="edit-text" @tap="startEditing" v-else>编辑</view>
        </view>
        
        <view class="rules-list">
          <view class="rule-item" v-for="(rule, index) in pointsRules" :key="index">
            <view class="rule-icon" :class="rule.type">
              <view class="icon-circle"></view>
            </view>
            <view class="rule-content">
              <view class="rule-title">{{rule.title}}</view>
              <view class="rule-desc">{{rule.description}}</view>
            </view>
            <view class="rule-points" v-if="!isEditing">+{{rule.points}}</view>
            <view class="rule-points-edit" v-else>
              <text class="plus-sign">+</text>
              <input type="number" class="points-input" v-model="rule.points" />
            </view>
          </view>
        </view>
      </view>
      
      <!-- 添加新规则按钮 -->
      <view class="add-rule-button" @tap="showAddRuleModal" v-if="isEditing">
        <text class="add-icon">+</text>
        <text class="add-text">添加新规则</text>
      </view>
      
      <!-- 底部空间 -->
      <view class="bottom-space"></view>
    </scroll-view>
    
    <!-- 添加规则弹窗 -->
    <view class="modal" v-if="showModal">
      <view class="modal-mask" @tap="hideModal"></view>
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">添加积分规则</text>
          <view class="modal-close" @tap="hideModal">×</view>
        </view>
        <view class="modal-body">
          <view class="form-item">
            <text class="form-label">规则类型</text>
            <picker @change="typeChange" :value="typeIndex" :range="typeOptions" range-key="label">
              <view class="picker">
                {{typeOptions[typeIndex].label}}
              </view>
            </picker>
          </view>
          <view class="form-item">
            <text class="form-label">规则名称</text>
            <input type="text" class="form-input" v-model="newRule.title" placeholder="请输入规则名称" />
          </view>
          <view class="form-item">
            <text class="form-label">规则描述</text>
            <input type="text" class="form-input" v-model="newRule.description" placeholder="请输入规则描述" />
          </view>
          <view class="form-item">
            <text class="form-label">积分值</text>
            <input type="number" class="form-input" v-model="newRule.points" placeholder="请输入积分值" />
          </view>
        </view>
        <view class="modal-footer">
          <view class="modal-button cancel" @tap="hideModal">取消</view>
          <view class="modal-button confirm" @tap="addRule">确定</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue';

// 编辑状态
const isEditing = ref(false);

// 积分规则数据
const pointsRules = ref([
  {
    id: 1,
    type: 'purchase',
    title: '商品购买',
    description: '每消费1元获得1积分',
    points: 1
  },
  {
    id: 2,
    type: 'checkin',
    title: '每日签到',
    description: '每日签到获得积分奖励',
    points: 10
  },
  {
    id: 3,
    type: 'share',
    title: '分享商品',
    description: '分享商品到社交媒体获得积分',
    points: 5
  }
]);

// 开始编辑
const startEditing = () => {
  isEditing.value = true;
};

// 保存规则
const saveRules = () => {
  isEditing.value = false;
  uni.showToast({
    title: '保存成功',
    icon: 'success'
  });
};

// 添加规则相关
const showModal = ref(false);
const typeOptions = [
  { value: 'purchase', label: '商品购买' },
  { value: 'checkin', label: '每日签到' },
  { value: 'share', label: '分享商品' },
  { value: 'review', label: '评价商品' },
  { value: 'register', label: '注册会员' },
  { value: 'birthday', label: '生日特权' }
];
const typeIndex = ref(0);
const newRule = reactive({
  title: '',
  description: '',
  points: '',
  type: 'purchase'
});

// 显示添加规则弹窗
const showAddRuleModal = () => {
  showModal.value = true;
  typeIndex.value = 0;
  newRule.title = '';
  newRule.description = '';
  newRule.points = '';
  newRule.type = 'purchase';
};

// 隐藏弹窗
const hideModal = () => {
  showModal.value = false;
};

// 类型选择变化
const typeChange = (e) => {
  typeIndex.value = e.detail.value;
  newRule.type = typeOptions[typeIndex.value].value;
};

// 添加规则
const addRule = () => {
  if (!newRule.title) {
    uni.showToast({
      title: '请输入规则名称',
      icon: 'none'
    });
    return;
  }
  
  if (!newRule.description) {
    uni.showToast({
      title: '请输入规则描述',
      icon: 'none'
    });
    return;
  }
  
  if (!newRule.points) {
    uni.showToast({
      title: '请输入积分值',
      icon: 'none'
    });
    return;
  }
  
  // 添加新规则
  pointsRules.value.push({
    id: Date.now(),
    type: newRule.type,
    title: newRule.title,
    description: newRule.description,
    points: newRule.points
  });
  
  hideModal();
  
  uni.showToast({
    title: '添加成功',
    icon: 'success'
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss">
/* 页面容器 */
.points-rules-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
  position: relative;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  color: white;
  padding: 48px 20px 16px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(255, 120, 0, 0.15);
}

.navbar-left {
  width: 40px;
}

.back-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-title {
  flex: 1;
  text-align: center;
}

.title-text {
  font-size: 18px;
  font-weight: 600;
}

.navbar-right {
  width: 40px;
}

/* 内容区域 */
.content-area {
  flex: 1;
  padding: 16px;
  box-sizing: border-box;
  height: calc(100vh - 80px);
}

/* 规则卡片 */
.rules-card {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}

.edit-text {
  font-size: 14px;
  color: #5E5CE6;
}

/* 规则列表 */
.rules-list {
  margin-top: 16px;
}

.rule-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #F0F0F0;
}

.rule-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.icon-circle {
  width: 32px;
  height: 32px;
  border-radius: 16px;
}

.rule-icon.purchase {
  background-color: rgba(25, 137, 250, 0.1);
}

.rule-icon.purchase .icon-circle {
  background-color: #1989FA;
}

.rule-icon.checkin {
  background-color: rgba(52, 199, 89, 0.1);
}

.rule-icon.checkin .icon-circle {
  background-color: #34C759;
}

.rule-icon.share {
  background-color: rgba(255, 149, 0, 0.1);
}

.rule-icon.share .icon-circle {
  background-color: #FF9500;
}

.rule-icon.review {
  background-color: rgba(94, 92, 230, 0.1);
}

.rule-icon.review .icon-circle {
  background-color: #5E5CE6;
}

.rule-icon.register {
  background-color: rgba(255, 59, 48, 0.1);
}

.rule-icon.register .icon-circle {
  background-color: #FF3B30;
}

.rule-icon.birthday {
  background-color: rgba(255, 45, 85, 0.1);
}

.rule-icon.birthday .icon-circle {
  background-color: #FF2D55;
}

.rule-content {
  flex: 1;
}

.rule-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 4px;
}

.rule-desc {
  font-size: 12px;
  color: #999999;
}

.rule-points {
  font-size: 16px;
  font-weight: 600;
  color: #FF7600;
}

.rule-points-edit {
  display: flex;
  align-items: center;
}

.plus-sign {
  font-size: 16px;
  color: #FF7600;
  margin-right: 2px;
}

.points-input {
  width: 50px;
  height: 32px;
  background-color: #F5F7FA;
  border-radius: 4px;
  padding: 0 8px;
  font-size: 16px;
  color: #FF7600;
  text-align: center;
}

/* 添加规则按钮 */
.add-rule-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px dashed #CCCCCC;
}

.add-icon {
  font-size: 20px;
  color: #999999;
  margin-right: 8px;
}

.add-text {
  font-size: 16px;
  color: #999999;
}

/* 底部空间 */
.bottom-space {
  height: 20px;
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #F0F0F0;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}

.modal-close {
  font-size: 24px;
  color: #999999;
  padding: 0 8px;
}

.modal-body {
  padding: 20px;
}

.form-item {
  margin-bottom: 16px;
}

.form-label {
  font-size: 14px;
  color: #666666;
  margin-bottom: 8px;
  display: block;
}

.form-input, .picker {
  width: 100%;
  height: 44px;
  background-color: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 14px;
  color: #333333;
  display: flex;
  align-items: center;
}

.modal-footer {
  display: flex;
  border-top: 1px solid #F0F0F0;
}

.modal-button {
  flex: 1;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.modal-button.cancel {
  color: #666666;
  background-color: #FFFFFF;
}

.modal-button.confirm {
  color: #FFFFFF;
  background-color: #FF7600;
}
</style> 