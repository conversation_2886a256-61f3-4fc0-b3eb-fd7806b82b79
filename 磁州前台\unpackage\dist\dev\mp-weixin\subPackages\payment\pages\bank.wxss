
.bank-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 120rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
}
.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}
.back-icon {
  width: 40rpx;
  height: 40rpx;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}
.navbar-right {
  width: 80rpx;
}

/* 银行卡列表 */
.bank-card-list {
  padding: 30rpx;
}
.bank-card-item {
  background: linear-gradient(to right, #0052CC, #0066FF);
  border-radius: 20rpx;
  padding: 40rpx;
  color: #fff;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 82, 204, 0.2);
  position: relative;
}
.bank-card-item.selected {
  box-shadow: 0 8rpx 30rpx rgba(0, 82, 204, 0.4);
}
.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}
.bank-logo {
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}
.bank-logo-img {
  width: 60rpx;
  height: 60rpx;
}
.bank-name {
  font-size: 32rpx;
  font-weight: 500;
  flex: 1;
}
.card-type {
  font-size: 24rpx;
  opacity: 0.8;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
}
.card-number {
  font-size: 40rpx;
  font-weight: bold;
  letter-spacing: 4rpx;
  margin-bottom: 40rpx;
}
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.card-holder {
  font-size: 28rpx;
}
.card-actions {
  display: flex;
  align-items: center;
}
.default-tag {
  font-size: 24rpx;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
  margin-right: 20rpx;
}
.card-action {
  font-size: 24rpx;
  margin-left: 20rpx;
  opacity: 0.8;
}
.card-action.delete {
  color: #ffcccc;
}

/* 添加银行卡按钮 */
.add-card-btn {
  position: fixed;
  bottom: 30rpx;
  left: 30rpx;
  right: 30rpx;
  height: 90rpx;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 45rpx;
  font-size: 32rpx;
}
.add-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

/* 空状态 */
.empty-view {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 弹窗样式 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}
.popup-content {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  z-index: 1001;
  padding-bottom: env(safe-area-inset-bottom);
}
.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.popup-title {
  font-size: 32rpx;
  font-weight: 500;
}
.popup-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.popup-body {
  padding: 30rpx;
}
.form-item {
  margin-bottom: 30rpx;
}
.form-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}
.form-input {
  width: 100%;
  height: 90rpx;
  background-color: #f8f9fc;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}
.form-picker {
  width: 100%;
  height: 90rpx;
  background-color: #f8f9fc;
  border-radius: 10rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
}
.picker-value {
  font-size: 28rpx;
  color: #333;
}
.form-checkbox {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
}
.checkbox-label {
  font-size: 28rpx;
  margin-left: 10rpx;
}
.popup-footer {
  display: flex;
  padding: 30rpx;
  border-top: 1rpx solid #f5f5f5;
}
.cancel-btn, .confirm-btn {
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-size: 32rpx;
  border-radius: 45rpx;
}
.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
  margin-right: 20rpx;
}
.confirm-btn {
  background-color: #0052CC;
  color: #fff;
}
.confirm-btn[disabled] {
  background-color: #cccccc;
}
