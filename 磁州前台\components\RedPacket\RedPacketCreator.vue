<template>
  <view class="red-packet-creator">
    <!-- 创建红包表单 -->
    <view class="creator-form">
      <!-- 红包类型选择 -->
      <view class="form-item">
        <view class="form-label">红包类型</view>
        <view class="type-selector">
          <view 
            v-for="(type, index) in packetTypes" 
            :key="index"
            class="type-item"
            :class="{ active: formData.type === type.value }"
            @click="formData.type = type.value"
          >
            <image :src="type.icon" class="type-icon"></image>
            <text class="type-name">{{ type.name }}</text>
          </view>
        </view>
      </view>
      
      <!-- 红包金额 -->
      <view class="form-item">
        <view class="form-label">红包金额</view>
        <view class="amount-input-container">
          <input 
            class="amount-input" 
            type="digit" 
            v-model="formData.amount"
            placeholder="输入红包金额"
            @input="validateAmount"
          />
          <text class="amount-unit">元</text>
        </view>
        <text class="available-balance">当前余额：{{ formatAmount(userBalance) }}元</text>
      </view>
      
      <!-- 红包数量 -->
      <view class="form-item" v-if="formData.type === 1">
        <view class="form-label">红包个数</view>
        <view class="count-selector">
          <button class="count-btn minus" @click="decreaseCount">-</button>
          <input 
            class="count-input" 
            type="number" 
            v-model="formData.count"
            @input="validateCount"
          />
          <button class="count-btn plus" @click="increaseCount">+</button>
        </view>
        <text class="amount-tips">每人最多可得 {{ formatAmount(maxAmountPerPerson) }}元</text>
      </view>
      
      <!-- 红包个数 - 固定红包模式 -->
      <view class="form-item" v-else-if="formData.type === 2">
        <view class="form-label">红包个数</view>
        <view class="count-selector">
          <button class="count-btn minus" @click="decreaseCount">-</button>
          <input 
            class="count-input" 
            type="number" 
            v-model="formData.count"
            @input="validateCount"
          />
          <button class="count-btn plus" @click="increaseCount">+</button>
        </view>
        <text class="amount-tips">每人固定可得 {{ formatAmount(fixedAmountPerPerson) }}元</text>
      </view>
      
      <!-- 红包祝福语 -->
      <view class="form-item">
        <view class="form-label">祝福语</view>
        <view class="blessing-input-container">
          <input 
            class="blessing-input" 
            type="text" 
            v-model="formData.blessing"
            placeholder="恭喜发财，大吉大利"
            maxlength="30"
          />
          <text class="blessing-count">{{ formData.blessing.length }}/30</text>
        </view>
      </view>
      
      <!-- 过期时间设置 -->
      <view class="form-item">
        <view class="form-label">过期时间</view>
        <view class="expire-selector">
          <view 
            v-for="(expire, index) in expireOptions" 
            :key="index"
            class="expire-item"
            :class="{ active: formData.expireHours === expire.value }"
            @click="formData.expireHours = expire.value"
          >
            <text>{{ expire.label }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 提交按钮 -->
    <view class="action-area">
      <view class="balance-warning" v-if="insufficientBalance">
        <text class="warning-text">余额不足，请先充值</text>
      </view>
      
      <button 
        class="submit-btn" 
        :disabled="!canSubmit || loading" 
        :loading="loading"
        @click="handleSubmit"
      >
        {{ loading ? '创建中...' : '塞入红包' }}
      </button>
    </view>
  </view>
</template>

<script>
import { RED_PACKET_TYPE, createRedPacket, formatRedPacketAmount } from '@/utils/redPacket.js';

export default {
  name: 'RedPacketCreator',
  props: {
    // 用户余额（单位：分）
    userBalance: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      // 表单数据
      formData: {
        type: RED_PACKET_TYPE.RANDOM, // 默认为拼手气红包
        amount: '', // 红包金额（元）
        count: 3, // 红包个数
        blessing: '恭喜发财，大吉大利', // 祝福语
        expireHours: 24 // 过期时间（小时）
      },
      // 红包类型列表
      packetTypes: [
        {
          name: '拼手气红包',
          value: RED_PACKET_TYPE.RANDOM,
          icon: '/static/images/random-packet-icon.png'
        },
        {
          name: '普通红包',
          value: RED_PACKET_TYPE.FIXED,
          icon: '/static/images/fixed-packet-icon.png'
        }
      ],
      // 过期时间选项
      expireOptions: [
        { label: '24小时', value: 24 },
        { label: '48小时', value: 48 },
        { label: '72小时', value: 72 }
      ],
      loading: false // 提交加载状态
    };
  },
  computed: {
    // 红包总金额（分）
    totalAmountInCents() {
      const amount = parseFloat(this.formData.amount || 0);
      return Math.floor(amount * 100); // 转换为分
    },
    
    // 拼手气红包每人最多可得金额
    maxAmountPerPerson() {
      if (!this.formData.count || this.formData.count <= 0) return 0;
      // 拼手气红包理论上单个人可以得到接近总金额
      return this.totalAmountInCents;
    },
    
    // 普通红包每人固定金额
    fixedAmountPerPerson() {
      if (!this.formData.count || this.formData.count <= 0) return 0;
      // 普通红包每人金额相等
      return Math.floor(this.totalAmountInCents / this.formData.count);
    },
    
    // 是否余额不足
    insufficientBalance() {
      return this.totalAmountInCents > this.userBalance;
    },
    
    // 是否可以提交
    canSubmit() {
      return (
        this.totalAmountInCents > 0 && 
        this.formData.count > 0 && 
        !this.insufficientBalance
      );
    }
  },
  methods: {
    // 格式化金额
    formatAmount(amount) {
      return formatRedPacketAmount(amount);
    },
    
    // 校验金额
    validateAmount() {
      if (this.formData.amount === '') return;
      
      let amount = parseFloat(this.formData.amount);
      
      // 限制最小金额为0.01元
      if (amount < 0.01) {
        amount = 0.01;
      }
      
      // 限制最大金额
      const maxAmount = this.formatAmount(this.userBalance);
      if (amount > parseFloat(maxAmount)) {
        amount = parseFloat(maxAmount);
      }
      
      // 保留两位小数
      this.formData.amount = amount.toFixed(2);
    },
    
    // 校验红包个数
    validateCount() {
      if (!this.formData.count) {
        this.formData.count = 1;
        return;
      }
      
      let count = parseInt(this.formData.count);
      
      // 至少1个红包
      if (count < 1) {
        count = 1;
      }
      
      // 最多100个红包
      if (count > 100) {
        count = 100;
      }
      
      this.formData.count = count;
    },
    
    // 减少红包个数
    decreaseCount() {
      if (this.formData.count > 1) {
        this.formData.count--;
      }
    },
    
    // 增加红包个数
    increaseCount() {
      if (this.formData.count < 100) {
        this.formData.count++;
      }
    },
    
    // 提交创建红包
    handleSubmit() {
      if (!this.canSubmit || this.loading) return;
      
      this.loading = true;
      
      // 构建请求参数
      const params = {
        type: this.formData.type,
        totalAmount: this.totalAmountInCents,
        totalCount: this.formData.count,
        title: this.formData.blessing,
        expireTime: Date.now() + this.formData.expireHours * 60 * 60 * 1000
      };
      
      // 调用创建红包接口
      createRedPacket(params)
        .then(res => {
          // 创建成功，返回红包数据
          this.$emit('create-success', res);
          this.resetForm();
        })
        .catch(err => {
          // 创建失败，提示错误
          uni.showToast({
            title: err.message || '创建红包失败',
            icon: 'none'
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },
    
    // 重置表单
    resetForm() {
      this.formData = {
        type: RED_PACKET_TYPE.RANDOM,
        amount: '',
        count: 3,
        blessing: '恭喜发财，大吉大利',
        expireHours: 24
      };
    }
  }
}
</script>

<style lang="scss">
.red-packet-creator {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  
  .creator-form {
    .form-item {
      margin-bottom: 30rpx;
      
      &:last-child {
        margin-bottom: 40rpx;
      }
      
      .form-label {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 20rpx;
        font-weight: 500;
      }
    }
    
    .type-selector {
      display: flex;
      justify-content: space-between;
      
      .type-item {
        flex: 1;
        height: 110rpx;
        background-color: #F8F8F8;
        border-radius: 8rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin: 0 10rpx;
        
        &:first-child {
          margin-left: 0;
        }
        
        &:last-child {
          margin-right: 0;
        }
        
        &.active {
          background: linear-gradient(135deg, #FFE4E1 0%, #FFF0F5 100%);
          border: 1px solid #FF6347;
        }
        
        .type-icon {
          width: 48rpx;
          height: 48rpx;
          margin-bottom: 8rpx;
        }
        
        .type-name {
          font-size: 24rpx;
          color: #333;
        }
      }
    }
    
    .amount-input-container {
      position: relative;
      height: 90rpx;
      
      .amount-input {
        height: 100%;
        background-color: #F8F8F8;
        border-radius: 8rpx;
        padding: 0 100rpx 0 30rpx;
        font-size: 32rpx;
        color: #333;
      }
      
      .amount-unit {
        position: absolute;
        right: 30rpx;
        top: 50%;
        transform: translateY(-50%);
        font-size: 28rpx;
        color: #999;
      }
    }
    
    .available-balance {
      font-size: 24rpx;
      color: #999;
      margin-top: 12rpx;
      display: block;
    }
    
    .count-selector {
      display: flex;
      align-items: center;
      height: 90rpx;
      
      .count-btn {
        width: 90rpx;
        height: 90rpx;
        background-color: #F8F8F8;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 36rpx;
        color: #333;
        margin: 0;
        padding: 0;
        line-height: 1;
        
        &.minus {
          border-radius: 8rpx 0 0 8rpx;
        }
        
        &.plus {
          border-radius: 0 8rpx 8rpx 0;
        }
      }
      
      .count-input {
        flex: 1;
        height: 100%;
        background-color: #F8F8F8;
        text-align: center;
        font-size: 32rpx;
        color: #333;
        margin: 0 2rpx;
      }
    }
    
    .amount-tips {
      font-size: 24rpx;
      color: #FF6347;
      margin-top: 12rpx;
      display: block;
    }
    
    .blessing-input-container {
      position: relative;
      height: 90rpx;
      
      .blessing-input {
        height: 100%;
        background-color: #F8F8F8;
        border-radius: 8rpx;
        padding: 0 80rpx 0 30rpx;
        font-size: 28rpx;
        color: #333;
      }
      
      .blessing-count {
        position: absolute;
        right: 20rpx;
        top: 50%;
        transform: translateY(-50%);
        font-size: 24rpx;
        color: #999;
      }
    }
    
    .expire-selector {
      display: flex;
      justify-content: space-between;
      
      .expire-item {
        flex: 1;
        height: 80rpx;
        background-color: #F8F8F8;
        border-radius: 8rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 10rpx;
        font-size: 26rpx;
        color: #333;
        
        &:first-child {
          margin-left: 0;
        }
        
        &:last-child {
          margin-right: 0;
        }
        
        &.active {
          background: linear-gradient(135deg, #FFE4E1 0%, #FFF0F5 100%);
          border: 1px solid #FF6347;
          color: #FF6347;
        }
      }
    }
  }
  
  .action-area {
    .balance-warning {
      text-align: center;
      margin-bottom: 20rpx;
      
      .warning-text {
        font-size: 26rpx;
        color: #FF3B30;
      }
    }
    
    .submit-btn {
      width: 100%;
      height: 90rpx;
      line-height: 90rpx;
      background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);
      border-radius: 45rpx;
      color: #fff;
      font-size: 32rpx;
      font-weight: 500;
      
      &[disabled] {
        opacity: 0.6;
        background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);
        color: #fff;
      }
    }
  }
}
</style> 