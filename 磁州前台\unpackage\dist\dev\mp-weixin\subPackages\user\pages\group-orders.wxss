/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.group-orders-container {
  min-height: 100vh;
  background-color: #F5F7FA;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background: linear-gradient(135deg, #1677FF, #0052CC);
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 100;
}
.navbar-left {
  width: 40px;
  display: flex;
  align-items: center;
}
.back-icon {
  width: 20px;
  height: 20px;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}
.navbar-right {
  width: 40px;
}

/* 状态筛选 */
.filter-tabs {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #F0F0F0;
  z-index: 99;
}
.tab-item {
  flex: 1;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #666;
  position: relative;
}
.tab-item.active {
  color: #1677FF;
  font-weight: 500;
}
.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background-color: #1677FF;
  border-radius: 2px;
}

/* 订单列表 */
.orders-list {
  position: absolute;
  top: 88px;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 10px;
}
.order-item {
  background-color: #FFFFFF;
  border-radius: 12px;
  margin-bottom: 15px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid #F5F5F5;
}
.shop-info {
  display: flex;
  align-items: center;
}
.shop-avatar {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  margin-right: 8px;
}
.shop-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-right: 5px;
}
.shop-arrow {
  width: 14px;
  height: 14px;
}
.order-status {
  font-size: 14px;
  font-weight: 500;
}
.status-1 {
  color: #FF9500;
  /* 待付款 */
}
.status-2 {
  color: #007AFF;
  /* 待发货 */
}
.status-3 {
  color: #34C759;
  /* 待收货 */
}
.status-4 {
  color: #8E8E93;
  /* 已完成 */
}
.status-5 {
  color: #8E8E93;
  /* 已取消 */
}
.order-content {
  display: flex;
  padding: 15px 0;
  border-bottom: 1px solid #F5F5F5;
}
.goods-image {
  width: 80px;
  height: 80px;
  border-radius: 6px;
  margin-right: 12px;
}
.goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.goods-name {
  font-size: 15px;
  color: #333;
  font-weight: 500;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.goods-spec {
  font-size: 13px;
  color: #999;
  margin-bottom: 6px;
}
.goods-price-count {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.goods-price {
  font-size: 15px;
  color: #FF3B30;
  font-weight: 500;
}
.goods-count {
  font-size: 13px;
  color: #999;
}
.order-footer {
  padding: 12px 0;
  display: flex;
  flex-direction: column;
}
.order-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}
.order-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  color: #666;
}
.total-price {
  font-size: 13px;
  color: #333;
}
.price-num {
  font-size: 16px;
  color: #FF3B30;
  font-weight: 500;
}
.order-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
  flex-wrap: wrap;
}
.action-btn {
  height: 30px;
  padding: 0 12px;
  border-radius: 15px;
  font-size: 13px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
  background-color: #F5F5F5;
  color: #666;
  border: 1px solid #E5E5E5;
}
.cancel-btn {
  color: #666;
}
.pay-btn {
  background-color: #1677FF;
  color: #FFF;
  border: none;
}
.contact-btn {
  border: 1px solid #1677FF;
  color: #1677FF;
  background-color: #FFF;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 100px;
}
.empty-icon {
  width: 120px;
  height: 120px;
  margin-bottom: 15px;
}
.empty-text {
  font-size: 16px;
  color: #999;
  margin-bottom: 8px;
}
.empty-subtext {
  font-size: 14px;
  color: #AAAAAA;
  margin-bottom: 20px;
}
.empty-btn {
  width: 120px;
  height: 40px;
  background: linear-gradient(135deg, #1677FF, #0052CC);
  color: #FFF;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

/* 加载状态 */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  color: #999;
  font-size: 14px;
}
.loading-icon {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #1677FF;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}
@keyframes spin {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
/* 加载完成 */
.load-all {
  text-align: center;
  padding: 15px 0;
  color: #999;
  font-size: 13px;
}