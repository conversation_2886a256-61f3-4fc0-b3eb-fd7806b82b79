<template>
  <view class="package-management-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">团购套餐管理</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 页面内容 -->
    <scroll-view scroll-y class="page-content">
      <!-- 套餐数据概览 -->
      <view class="overview-section">
        <view class="section-header">
          <text class="section-title">套餐数据概览</text>
          <view class="date-picker" @click="showDatePicker">
            <text class="date-text">{{dateRange}}</text>
            <view class="date-icon"></view>
          </view>
        </view>
        
        <view class="stats-cards">
          <view class="stats-card">
            <view class="card-value">{{packageData.totalPackages}}</view>
            <view class="card-label">套餐总数</view>
            <view class="card-trend" :class="packageData.packagesTrend">
              <view class="trend-arrow"></view>
              <text class="trend-value">{{packageData.packagesGrowth}}</text>
            </view>
          </view>
          
          <view class="stats-card">
            <view class="card-value">{{packageData.salesCount}}</view>
            <view class="card-label">销售数量</view>
            <view class="card-trend" :class="packageData.salesTrend">
              <view class="trend-arrow"></view>
              <text class="trend-value">{{packageData.salesGrowth}}</text>
            </view>
          </view>
          
          <view class="stats-card">
            <view class="card-value">¥{{formatNumber(packageData.totalRevenue)}}</view>
            <view class="card-label">套餐收入</view>
            <view class="card-trend" :class="packageData.revenueTrend">
              <view class="trend-arrow"></view>
              <text class="trend-value">{{packageData.revenueGrowth}}</text>
            </view>
          </view>
          
          <view class="stats-card">
            <view class="card-value">{{packageData.conversionRate}}%</view>
            <view class="card-label">转化率</view>
            <view class="card-trend" :class="packageData.conversionTrend">
              <view class="trend-arrow"></view>
              <text class="trend-value">{{packageData.conversionGrowth}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 套餐列表 -->
      <view class="packages-section">
        <view class="section-header">
          <text class="section-title">套餐列表</text>
          <view class="filter-dropdown" @click="showFilterOptions">
            <text class="filter-text">{{currentFilter}}</text>
            <view class="filter-icon"></view>
          </view>
        </view>
        
        <view class="packages-list">
          <view class="package-item" v-for="(packageItem, index) in packagesList" :key="index" @click="viewPackageDetail(packageItem)">
            <view class="package-header">
              <view class="package-title-row">
                <text class="package-name">{{packageItem.name}}</text>
                <view class="package-status" :class="packageItem.statusClass">{{packageItem.statusText}}</view>
              </view>
              <text class="package-desc">{{packageItem.description}}</text>
            </view>
            
            <view class="package-content">
              <view class="package-items">
                <text class="package-items-text">{{packageItem.itemsText}}</text>
              </view>
              
              <view class="package-price-info">
                <view class="price-row">
                  <text class="price-label">原价:</text>
                  <text class="price-original">¥{{packageItem.originalPrice}}</text>
                </view>
                <view class="price-row">
                  <text class="price-label">拼团价:</text>
                  <text class="price-group">¥{{packageItem.groupPrice}}</text>
                </view>
                <view class="price-row">
                  <text class="price-label">节省:</text>
                  <text class="price-save">¥{{packageItem.savingsAmount}}</text>
                </view>
              </view>
            </view>
            
            <view class="package-footer">
              <view class="package-stats">
                <view class="stat-item">
                  <text class="stat-value">{{packageItem.salesCount}}</text>
                  <text class="stat-label">销量</text>
                </view>
                <view class="stat-item">
                  <text class="stat-value">{{packageItem.viewCount}}</text>
                  <text class="stat-label">浏览</text>
                </view>
                <view class="stat-item">
                  <text class="stat-value">{{packageItem.conversionRate}}%</text>
                  <text class="stat-label">转化率</text>
                </view>
              </view>
              
              <view class="package-actions">
                <view class="action-button edit" @click.stop="editPackage(packageItem)">
                  <view class="action-icon edit-icon"></view>
                  <text class="action-text">编辑</text>
                </view>
                <view class="action-button share" @click.stop="sharePackage(packageItem)">
                  <view class="action-icon share-icon"></view>
                  <text class="action-text">分享</text>
                </view>
                <view class="action-button more" @click.stop="showMoreActions(packageItem)">
                  <view class="action-icon more-icon"></view>
                  <text class="action-text">更多</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 无数据提示 -->
        <view class="empty-state" v-if="packagesList.length === 0">
          <image class="empty-image" src="/static/images/empty-packages.png" mode="aspectFit"></image>
          <text class="empty-text">暂无团购套餐</text>
          <text class="empty-subtext">点击下方按钮创建您的第一个团购套餐</text>
        </view>
      </view>
    </scroll-view>
    
    <!-- 浮动操作按钮 -->
    <view class="floating-action-button" @click="showCreateSteps">
      <view class="fab-content">
        <view class="fab-icon">+</view>
        <view class="fab-text">创建套餐</view>
      </view>
    </view>
    
    <!-- 创建套餐分步骤向导 -->
    <view class="step-wizard" v-if="showStepWizard">
      <view class="wizard-overlay" @click="cancelWizard"></view>
      <view class="wizard-content">
        <view class="wizard-header">
          <text class="wizard-title">创建团购套餐</text>
          <view class="close-icon" @click="cancelWizard">×</view>
        </view>
        
        <view class="wizard-body">
          <view class="wizard-step" v-for="(step, index) in createSteps" :key="index" @click="selectStep(step)">
            <view class="step-number">{{index + 1}}</view>
            <view class="step-info">
              <text class="step-title">{{step.title}}</text>
              <text class="step-desc">{{step.description}}</text>
            </view>
            <view class="step-arrow"></view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      dateRange: '2023-04-01 ~ 2023-04-30',
      currentFilter: '全部套餐',
      
      // 套餐数据
      packageData: {
        totalPackages: 8,
        packagesTrend: 'up',
        packagesGrowth: '25%',
        
        salesCount: 124,
        salesTrend: 'up',
        salesGrowth: '18%',
        
        totalRevenue: 9860.50,
        revenueTrend: 'up',
        revenueGrowth: '22%',
        
        conversionRate: 32,
        conversionTrend: 'up',
        conversionGrowth: '8%'
      },
      
      // 套餐列表
      packagesList: [
        {
          id: 1,
          name: '四菜一汤家庭套餐',
          description: '适合3-4人用餐，经典家庭聚餐套餐',
          itemsText: '红烧肉、糖醋排骨、鱼香肉丝、清炒时蔬、紫菜蛋花汤',
          originalPrice: '168.00',
          groupPrice: '99.00',
          savingsAmount: '69.00',
          salesCount: 56,
          viewCount: 1280,
          conversionRate: 4.3,
          statusText: '进行中',
          statusClass: 'active'
        },
        {
          id: 2,
          name: '双人浪漫晚餐套餐',
          description: '适合情侣约会，浪漫双人套餐',
          itemsText: '牛排2份、沙拉2份、意面2份、甜点2份、红酒1瓶',
          originalPrice: '298.00',
          groupPrice: '199.00',
          savingsAmount: '99.00',
          salesCount: 32,
          viewCount: 876,
          conversionRate: 3.6,
          statusText: '进行中',
          statusClass: 'active'
        },
        {
          id: 3,
          name: '商务午餐套餐',
          description: '适合商务洽谈，高档商务套餐',
          itemsText: '前菜4份、主菜4份、甜点4份、饮料4份',
          originalPrice: '388.00',
          groupPrice: '288.00',
          savingsAmount: '100.00',
          salesCount: 18,
          viewCount: 456,
          conversionRate: 3.9,
          statusText: '已结束',
          statusClass: 'inactive'
        }
      ],
      showStepWizard: false,
      createSteps: [
        { title: '选择套餐类型', description: '选择您要创建的团购套餐类型' },
        { title: '填写套餐信息', description: '填写团购套餐的基本信息' },
        { title: '设置套餐价格', description: '设置团购套餐的价格' },
        { title: '添加套餐内容', description: '添加团购套餐的内容' },
        { title: '完成创建', description: '完成团购套餐的创建' }
      ]
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showHelp() {
      uni.showToast({
        title: '团购套餐帮助',
        icon: 'none'
      });
    },
    showDatePicker() {
      // 显示日期选择器
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    formatNumber(num) {
      return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    },
    showFilterOptions() {
      uni.showActionSheet({
        itemList: ['全部套餐', '进行中', '已结束', '销量优先', '转化率优先'],
        success: (res) => {
          const filters = ['全部套餐', '进行中', '已结束', '销量优先', '转化率优先'];
          this.currentFilter = filters[res.tapIndex];
          // 这里可以添加实际的筛选逻辑
        }
      });
    },
    viewPackageDetail(packageItem) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/group/package-detail?id=${packageItem.id}`
      });
    },
    editPackage(packageItem) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/group/create?id=${packageItem.id}&type=package&edit=true`
      });
    },
    sharePackage(packageItem) {
      uni.showToast({
        title: '分享功能开发中',
        icon: 'none'
      });
    },
    showMoreActions(packageItem) {
      uni.showActionSheet({
        itemList: ['复制套餐', '下架套餐', '删除套餐'],
        success: (res) => {
          switch(res.tapIndex) {
            case 0:
              uni.showToast({ title: '复制套餐功能开发中', icon: 'none' });
              break;
            case 1:
              uni.showToast({ title: '下架套餐功能开发中', icon: 'none' });
              break;
            case 2:
              this.confirmDeletePackage(packageItem);
              break;
          }
        }
      });
    },
    confirmDeletePackage(packageItem) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除"${packageItem.name}"套餐吗？此操作不可恢复。`,
        confirmText: '删除',
        confirmColor: '#FF3B30',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({ title: '删除成功', icon: 'success' });
            // 这里可以添加实际的删除逻辑
          }
        }
      });
    },
    showCreateSteps() {
      this.showStepWizard = true;
    },
    cancelWizard() {
      this.showStepWizard = false;
    },
    selectStep(step) {
      // 这里可以添加选择步骤后的逻辑
      const index = this.createSteps.indexOf(step);
      
      // 关闭向导
      this.showStepWizard = false;
      
      // 根据选择的步骤跳转到不同的创建页面
      switch(index) {
        case 0: // 选择套餐类型
          uni.navigateTo({
            url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-type'
          });
          break;
        case 1: // 填写套餐信息
          uni.navigateTo({
            url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-info'
          });
          break;
        case 2: // 设置套餐价格
          uni.navigateTo({
            url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-price'
          });
          break;
        case 3: // 添加套餐内容
          uni.navigateTo({
            url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-items'
          });
          break;
        case 4: // 完成创建
          uni.navigateTo({
            url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-confirm'
          });
          break;
        default:
          // 默认跳转到完整的创建页面
          uni.navigateTo({
            url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create?type=package'
          });
      }
    }
  }
}
</script>

<style lang="scss">
.package-management-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

.page-content {
  height: calc(100vh - 59px - env(safe-area-inset-bottom));
}

/* 概览部分样式 */
.overview-section {
  margin: 15px;
  padding: 15px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.date-picker {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
  font-size: 12px;
  color: #666;
}

.date-icon {
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 5px solid #666;
  margin-left: 5px;
}

.stats-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px;
}

.stats-card {
  flex: 1;
  min-width: calc(50% - 10px);
  margin: 5px;
  background: #FFFFFF;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
  position: relative;
}

.card-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.card-label {
  font-size: 12px;
  color: #999;
}

.card-trend {
  position: absolute;
  top: 15px;
  right: 15px;
  display: flex;
  align-items: center;
  font-size: 12px;
}

.card-trend.up {
  color: #34C759;
}

.card-trend.down {
  color: #FF3B30;
}

.trend-arrow {
  width: 0;
  height: 0;
  margin-right: 2px;
}

.up .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 6px solid #34C759;
}

.down .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 6px solid #FF3B30;
}

/* 套餐列表样式 */
.packages-section {
  margin: 15px;
  margin-top: 0;
}

.filter-dropdown {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
  font-size: 12px;
  color: #666;
}

.filter-icon {
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 5px solid #666;
  margin-left: 5px;
}

.packages-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.package-item {
  background: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.package-header {
  padding: 15px;
  border-bottom: 1px solid #F5F7FA;
}

.package-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.package-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.package-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.package-status.active {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.package-status.inactive {
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}

.package-desc {
  font-size: 12px;
  color: #999;
  line-height: 1.5;
}

.package-content {
  padding: 15px;
  display: flex;
  border-bottom: 1px solid #F5F7FA;
}

.package-items {
  flex: 1;
  padding-right: 15px;
}

.package-items-text {
  font-size: 13px;
  color: #666;
  line-height: 1.6;
}

.package-price-info {
  width: 100px;
  flex-shrink: 0;
  border-left: 1px dashed #E5E7EB;
  padding-left: 15px;
}

.price-row {
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}

.price-row:last-child {
  margin-bottom: 0;
}

.price-label {
  font-size: 12px;
  color: #999;
  width: 45px;
}

.price-original {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

.price-group {
  font-size: 12px;
  color: #FF3B30;
  font-weight: 600;
}

.price-save {
  font-size: 12px;
  color: #FF9500;
}

.package-footer {
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.package-stats {
  display: flex;
  gap: 15px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.stat-label {
  font-size: 10px;
  color: #999;
  margin-top: 2px;
}

.package-actions {
  display: flex;
  gap: 10px;
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.action-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-icon {
  background: rgba(0, 122, 255, 0.1);
  position: relative;
}

.edit-icon:before {
  content: '';
  width: 12px;
  height: 12px;
  border: 1px solid #007AFF;
  border-radius: 2px;
  transform: rotate(45deg);
}

.share-icon {
  background: rgba(52, 199, 89, 0.1);
  position: relative;
}

.share-icon:before {
  content: '';
  width: 12px;
  height: 6px;
  border-left: 1px solid #34C759;
  border-right: 1px solid #34C759;
  border-bottom: 1px solid #34C759;
}

.more-icon {
  background: rgba(142, 142, 147, 0.1);
  position: relative;
}

.more-icon:before {
  content: '';
  width: 12px;
  height: 2px;
  background: #8E8E93;
  position: absolute;
  top: 11px;
  left: 6px;
}

.more-icon:after {
  content: '';
  width: 2px;
  height: 12px;
  background: #8E8E93;
  position: absolute;
  top: 6px;
  left: 11px;
}

.action-text {
  font-size: 10px;
  color: #999;
  margin-top: 2px;
}

/* 空状态样式 */
.empty-state {
  padding: 40px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 15px;
}

.empty-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.empty-subtext {
  font-size: 14px;
  color: #999;
  text-align: center;
  line-height: 1.5;
}

/* 浮动操作按钮 */
.floating-action-button {
  position: fixed;
  right: 20px;
  bottom: 30px;
  width: auto;
  height: 50px;
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(126, 48, 225, 0.3);
  z-index: 10;
  padding: 0 20px;
}

.fab-content {
  display: flex;
  align-items: center;
}

.fab-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #FFFFFF;
  margin-right: 8px;
}

.fab-text {
  color: #FFFFFF;
  font-size: 14px;
  font-weight: 500;
}

/* 创建套餐分步骤向导样式 */
.step-wizard {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 100;
}

.wizard-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.wizard-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 400px;
  background: #FFFFFF;
  border-radius: 12px;
  padding: 20px;
}

.wizard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.wizard-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #999;
}

.wizard-body {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.wizard-step {
  display: flex;
  align-items: center;
  padding: 10px;
  background: #F5F7FA;
  border-radius: 8px;
}

.step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #333;
  margin-right: 10px;
}

.step-info {
  flex: 1;
}

.step-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.step-desc {
  font-size: 12px;
  color: #999;
}

.step-arrow {
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 5px solid #999;
  margin-left: 10px;
}
</style> 