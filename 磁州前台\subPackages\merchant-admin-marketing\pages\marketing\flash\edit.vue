<!-- 秒杀活动编辑页面 (edit.vue) -->
<template>
  <view class="flash-edit-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-left">
        <view class="back-button" @tap="goBack">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 18L9 12L15 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </view>
      </view>
      <view class="navbar-title">
        <text class="title-text">编辑秒杀活动</text>
      </view>
      <view class="navbar-right">
        <view class="save-button" @tap="saveFlashSale">保存</view>
      </view>
    </view>
    
    <!-- 页面内容区域 -->
    <scroll-view scroll-y class="content-area">
      <!-- 表单区域 -->
      <view class="form-section">
        <!-- 基本信息 -->
        <view class="form-group">
          <view class="form-header">基本信息</view>
          
          <view class="form-item">
            <text class="form-label required">活动名称</text>
            <input class="form-input" v-model="formData.name" placeholder="请输入秒杀活动名称" maxlength="30" />
            <text class="form-counter">{{formData.name.length}}/30</text>
          </view>
          
          <view class="form-item">
            <text class="form-label required">商品图片</text>
            <view class="image-uploader">
              <view 
                class="image-item" 
                v-for="(image, index) in formData.images" 
                :key="index"
              >
                <image class="uploaded-image" :src="image" mode="aspectFill"></image>
                <view class="delete-icon" @tap="removeImage(index)">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="10" fill="rgba(0,0,0,0.5)"/>
                    <path d="M15 9L9 15M9 9L15 15" stroke="white" stroke-width="2" stroke-linecap="round"/>
                  </svg>
                </view>
              </view>
              <view class="upload-button" @tap="addImage" v-if="formData.images.length < 5">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 5V19M5 12H19" stroke="#999999" stroke-width="2" stroke-linecap="round"/>
                </svg>
              </view>
            </view>
            <text class="form-tip">最多上传5张图片，建议尺寸750x750像素</text>
          </view>
        </view>
        
        <!-- 价格设置 -->
        <view class="form-group">
          <view class="form-header">价格设置</view>
          
          <view class="form-item">
            <text class="form-label required">原价 (元)</text>
            <input class="form-input" type="digit" v-model="formData.originalPrice" placeholder="请输入商品原价" />
          </view>
          
          <view class="form-item">
            <text class="form-label required">秒杀价 (元)</text>
            <input class="form-input" type="digit" v-model="formData.flashPrice" placeholder="请输入秒杀价格" />
          </view>
          
          <view class="form-item">
            <text class="form-label">折扣</text>
            <text class="discount-value">{{discountValue}}</text>
          </view>
        </view>
        
        <!-- 库存设置 -->
        <view class="form-group">
          <view class="form-header">库存设置</view>
          
          <view class="form-item">
            <text class="form-label required">活动库存</text>
            <input class="form-input" type="number" v-model="formData.stockTotal" placeholder="请输入活动库存" />
          </view>
          
          <view class="form-item">
            <text class="form-label">每人限购</text>
            <input class="form-input" type="number" v-model="formData.purchaseLimit" placeholder="0表示不限购" />
            <text class="form-tip">设置为0表示不限购</text>
          </view>
        </view>
        
        <!-- 时间设置 -->
        <view class="form-group">
          <view class="form-header">时间设置</view>
          
          <view class="form-item">
            <text class="form-label required">开始时间</text>
            <picker 
              mode="dateTime" 
              :value="formData.startTime" 
              start="2023-01-01 00:00" 
              end="2030-12-31 23:59" 
              @change="startTimeChange"
            >
              <view class="picker-value">
                <text>{{formData.startTime || '请选择开始时间'}}</text>
                <view class="arrow-icon"></view>
              </view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="form-label required">结束时间</text>
            <picker 
              mode="dateTime" 
              :value="formData.endTime" 
              start="2023-01-01 00:00" 
              end="2030-12-31 23:59" 
              @change="endTimeChange"
            >
              <view class="picker-value">
                <text>{{formData.endTime || '请选择结束时间'}}</text>
                <view class="arrow-icon"></view>
              </view>
            </picker>
            <text class="form-error" v-if="timeError">{{timeError}}</text>
          </view>
        </view>
        
        <!-- 活动规则 -->
        <view class="form-group">
          <view class="form-header">活动规则</view>
          
          <view class="form-item">
            <text class="form-label">活动规则</text>
            <textarea class="form-textarea" v-model="formData.rules" placeholder="请输入活动规则说明" maxlength="200" />
            <text class="form-counter">{{formData.rules.length}}/200</text>
          </view>
        </view>
        
        <!-- 商品详情 -->
        <view class="form-group">
          <view class="form-header">商品详情</view>
          
          <view class="form-item">
            <text class="form-label">商品描述</text>
            <textarea class="form-textarea" v-model="formData.description" placeholder="请输入商品描述" maxlength="500" />
            <text class="form-counter">{{formData.description.length}}/500</text>
          </view>
          
          <view class="form-item">
            <text class="form-label">详情图片</text>
            <view class="image-uploader">
              <view 
                class="image-item detail-image-item" 
                v-for="(image, index) in formData.detailImages" 
                :key="index"
              >
                <image class="uploaded-image" :src="image" mode="aspectFill"></image>
                <view class="delete-icon" @tap="removeDetailImage(index)">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="10" fill="rgba(0,0,0,0.5)"/>
                    <path d="M15 9L9 15M9 9L15 15" stroke="white" stroke-width="2" stroke-linecap="round"/>
                  </svg>
                </view>
              </view>
              <view class="upload-button detail-upload" @tap="addDetailImage">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 5V19M5 12H19" stroke="#999999" stroke-width="2" stroke-linecap="round"/>
                </svg>
              </view>
            </view>
            <text class="form-tip">建议上传清晰的商品细节图</text>
          </view>
        </view>
        
        <!-- 高级设置 -->
        <view class="form-group">
          <view class="form-header">高级设置</view>
          
          <view class="form-item">
            <text class="form-label">活动状态</text>
            <switch 
              :checked="formData.status === 'active'" 
              @change="statusChange" 
              color="#FF7600"
            />
            <text class="switch-label">{{formData.status === 'active' ? '上架中' : '已下架'}}</text>
          </view>
        </view>
      </view>
      
      <!-- 底部空间 -->
      <view class="bottom-space"></view>
    </scroll-view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="action-button preview" @tap="previewFlashSale">
        <text class="button-text">预览</text>
      </view>
      <view class="action-button save" @tap="saveFlashSale">
        <text class="button-text">保存</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';

// 页面参数
const flashId = ref(null);

// 表单数据
const formData = reactive({
  name: '夏季清凉风扇特惠',
  images: [
    '/static/images/flash-item1.jpg',
    '/static/images/flash-item1-2.jpg',
    '/static/images/flash-item1-3.jpg'
  ],
  detailImages: [
    '/static/images/flash-detail1.jpg',
    '/static/images/flash-detail2.jpg',
    '/static/images/flash-detail3.jpg'
  ],
  originalPrice: '129.9',
  flashPrice: '59.9',
  stockTotal: '100',
  stockSold: '0',
  purchaseLimit: '2',
  startTime: '2023-07-01 12:00',
  endTime: '2023-07-01 14:00',
  rules: '活动期间每人限购2件，秒杀商品不支持退换货，数量有限，先到先得',
  description: '这是一款高品质的夏季清凉风扇，采用先进技术，风力强劲，静音设计，让您的夏天更加舒适。\n\n规格参数：\n- 功率：30W\n- 风速：3档可调\n- 电池容量：4000mAh\n- 续航时间：4-8小时\n- 材质：环保ABS',
  status: 'active'
});

// 时间错误提示
const timeError = ref('');

// 计算折扣
const discountValue = computed(() => {
  if (!formData.originalPrice || !formData.flashPrice || formData.originalPrice <= 0) {
    return '- 折';
  }
  
  const discount = (formData.flashPrice / formData.originalPrice * 10).toFixed(1);
  return `${discount} 折`;
});

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 添加商品图片
const addImage = () => {
  uni.chooseImage({
    count: 5 - formData.images.length,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      formData.images = [...formData.images, ...res.tempFilePaths];
    }
  });
};

// 移除商品图片
const removeImage = (index) => {
  formData.images.splice(index, 1);
};

// 添加详情图片
const addDetailImage = () => {
  uni.chooseImage({
    count: 9,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      formData.detailImages = [...formData.detailImages, ...res.tempFilePaths];
    }
  });
};

// 移除详情图片
const removeDetailImage = (index) => {
  formData.detailImages.splice(index, 1);
};

// 开始时间变化
const startTimeChange = (e) => {
  formData.startTime = e.detail.value;
  validateTime();
};

// 结束时间变化
const endTimeChange = (e) => {
  formData.endTime = e.detail.value;
  validateTime();
};

// 验证时间
const validateTime = () => {
  if (!formData.startTime || !formData.endTime) {
    timeError.value = '';
    return;
  }
  
  const start = new Date(formData.startTime.replace(/-/g, '/'));
  const end = new Date(formData.endTime.replace(/-/g, '/'));
  
  if (end <= start) {
    timeError.value = '结束时间必须晚于开始时间';
  } else {
    timeError.value = '';
  }
};

// 状态变化
const statusChange = (e) => {
  formData.status = e.detail.value ? 'active' : 'inactive';
};

// 预览秒杀活动
const previewFlashSale = () => {
  if (!validateForm(true)) return;
  
  uni.showToast({
    title: '预览功能开发中',
    icon: 'none'
  });
};

// 保存秒杀活动
const saveFlashSale = () => {
  if (!validateForm()) return;
  
  uni.showLoading({
    title: '保存中...'
  });
  
  // 模拟API请求
  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 1500,
      success: () => {
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }
    });
  }, 1500);
};

// 表单验证
const validateForm = (isPreview = false) => {
  if (!formData.name.trim()) {
    uni.showToast({
      title: '请输入活动名称',
      icon: 'none'
    });
    return false;
  }
  
  if (formData.images.length === 0) {
    uni.showToast({
      title: '请上传至少一张商品图片',
      icon: 'none'
    });
    return false;
  }
  
  if (!formData.originalPrice || parseFloat(formData.originalPrice) <= 0) {
    uni.showToast({
      title: '请输入有效的原价',
      icon: 'none'
    });
    return false;
  }
  
  if (!formData.flashPrice || parseFloat(formData.flashPrice) <= 0) {
    uni.showToast({
      title: '请输入有效的秒杀价',
      icon: 'none'
    });
    return false;
  }
  
  if (parseFloat(formData.flashPrice) >= parseFloat(formData.originalPrice)) {
    uni.showToast({
      title: '秒杀价必须低于原价',
      icon: 'none'
    });
    return false;
  }
  
  if (!formData.stockTotal || parseInt(formData.stockTotal) <= 0) {
    uni.showToast({
      title: '请输入有效的活动库存',
      icon: 'none'
    });
    return false;
  }
  
  if (!formData.startTime) {
    uni.showToast({
      title: '请选择开始时间',
      icon: 'none'
    });
    return false;
  }
  
  if (!formData.endTime) {
    uni.showToast({
      title: '请选择结束时间',
      icon: 'none'
    });
    return false;
  }
  
  if (timeError.value) {
    uni.showToast({
      title: timeError.value,
      icon: 'none'
    });
    return false;
  }
  
  return true;
};

// 页面加载
onMounted(() => {
  // 获取页面参数
  const eventChannel = getOpenerEventChannel();
  const query = uni.getSystemInfoSync().platform === 'h5' ? location.href.split('?')[1] : '';
  
  if (query) {
    const params = query.split('&').reduce((res, item) => {
      const [key, value] = item.split('=');
      res[key] = value;
      return res;
    }, {});
    
    flashId.value = params.id;
  } else {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    if (currentPage && currentPage.options) {
      flashId.value = currentPage.options.id;
    }
  }
  
  // 加载秒杀活动数据
  loadFlashSaleData();
});

// 加载秒杀活动数据
const loadFlashSaleData = () => {
  if (!flashId.value) return;
  
  // 实际应用中应该从API获取数据
  console.log('加载秒杀活动数据, ID:', flashId.value);
  
  // 模拟API请求
  uni.showLoading({
    title: '加载中...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    // 实际应用中这里应该使用API返回的数据更新formData
  }, 500);
};
</script>

<style lang="scss">
/* 页面容器 */
.flash-edit-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
  position: relative;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  color: white;
  padding: 48px 20px 16px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(255, 120, 0, 0.15);
}

.navbar-left {
  width: 40px;
}

.back-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-title {
  flex: 1;
  text-align: center;
}

.title-text {
  font-size: 18px;
  font-weight: 600;
}

.navbar-right {
  min-width: 40px;
}

.save-button {
  font-size: 16px;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16px;
}

/* 内容区域 */
.content-area {
  flex: 1;
  box-sizing: border-box;
  height: calc(100vh - 80px - 60px);
}

/* 表单区域 */
.form-section {
  padding: 12px;
}

.form-group {
  background: #FFFFFF;
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
}

.form-header {
  padding: 12px 16px;
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  border-bottom: 1px solid #F5F5F5;
}

.form-item {
  padding: 12px 16px;
  border-bottom: 1px solid #F5F5F5;
  position: relative;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  display: block;
  font-size: 14px;
  color: #333333;
  margin-bottom: 8px;
}

.required:after {
  content: '*';
  color: #FF3B30;
  margin-left: 4px;
}

.form-input {
  width: 100%;
  height: 40px;
  background: #F9F9F9;
  border: 1px solid #EEEEEE;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
  color: #333333;
}

.form-textarea {
  width: 100%;
  height: 100px;
  background: #F9F9F9;
  border: 1px solid #EEEEEE;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  color: #333333;
}

.form-counter {
  position: absolute;
  right: 16px;
  bottom: 12px;
  font-size: 12px;
  color: #999999;
}

.form-tip {
  font-size: 12px;
  color: #999999;
  margin-top: 8px;
  display: block;
}

.form-error {
  font-size: 12px;
  color: #FF3B30;
  margin-top: 8px;
  display: block;
}

.discount-value {
  height: 40px;
  line-height: 40px;
  font-size: 16px;
  color: #FF3B30;
  font-weight: 600;
}

.picker-value {
  width: 100%;
  height: 40px;
  background: #F9F9F9;
  border: 1px solid #EEEEEE;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
  color: #333333;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.arrow-icon {
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #999999;
}

.switch-label {
  font-size: 14px;
  color: #666666;
  margin-left: 8px;
}

/* 图片上传 */
.image-uploader {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -4px;
}

.image-item {
  width: calc(25% - 8px);
  margin: 4px;
  position: relative;
  aspect-ratio: 1;
}

.detail-image-item {
  width: calc(33.33% - 8px);
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}

.delete-icon {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  z-index: 10;
}

.upload-button {
  width: calc(25% - 8px);
  margin: 4px;
  aspect-ratio: 1;
  background: #F9F9F9;
  border: 1px dashed #DDDDDD;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999999;
}

.detail-upload {
  width: calc(33.33% - 8px);
}

/* 底部空间 */
.bottom-space {
  height: 80px;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #FFFFFF;
  padding: 10px 16px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 90;
  display: flex;
}

.action-button {
  flex: 1;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 6px;
}

.action-button.preview {
  background: #F5F5F5;
  color: #666666;
}

.action-button.save {
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  color: white;
}

.button-text {
  font-size: 16px;
  font-weight: 500;
}

@media screen and (min-width: 768px) {
  .form-section {
    max-width: 600px;
    margin: 0 auto;
  }
  
  .bottom-bar {
    max-width: 600px;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>