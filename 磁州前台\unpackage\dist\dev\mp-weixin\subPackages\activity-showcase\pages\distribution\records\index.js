"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const tabs = common_vendor.ref([
      { name: "全部", status: "all", label: "总收益" },
      { name: "收入", status: "income", label: "总收入" },
      { name: "提现", status: "withdraw", label: "总提现" }
    ]);
    const currentTab = common_vendor.ref(0);
    const startDate = common_vendor.ref("2023-05-01");
    const endDate = common_vendor.ref("2023-05-31");
    const currentPickerType = common_vendor.ref("");
    const quickPeriods = common_vendor.ref([
      { name: "今天", days: 0 },
      { name: "7天", days: 7 },
      { name: "30天", days: 30 },
      { name: "全部", days: -1 }
    ]);
    const currentPeriod = common_vendor.ref(2);
    const sortOptions = common_vendor.ref([
      { name: "时间降序", field: "time", order: "desc" },
      { name: "时间升序", field: "time", order: "asc" },
      { name: "金额降序", field: "amount", order: "desc" },
      { name: "金额升序", field: "amount", order: "asc" }
    ]);
    const currentSortIndex = common_vendor.ref(0);
    const currentSortOption = common_vendor.computed(() => sortOptions.value[currentSortIndex.value]);
    const showSortMenu = common_vendor.ref(false);
    const loading = common_vendor.ref(false);
    const noMore = common_vendor.ref(false);
    const records = common_vendor.ref([
      {
        id: 1,
        title: "直接佣金",
        amount: "10.50",
        source: "订单号: 2023051500001",
        time: "2023-05-15 14:30:25",
        type: "commission"
      },
      {
        id: 2,
        title: "团队佣金",
        amount: "5.20",
        source: "来自: 张小明",
        time: "2023-05-14 09:15:36",
        type: "team"
      },
      {
        id: 3,
        title: "提现",
        amount: "100.00",
        source: "提现到微信零钱",
        time: "2023-05-10 16:42:18",
        type: "withdraw"
      },
      {
        id: 4,
        title: "活动奖励",
        amount: "20.00",
        source: "新人推广活动",
        time: "2023-05-08 11:23:45",
        type: "reward"
      },
      {
        id: 5,
        title: "直接佣金",
        amount: "15.80",
        source: "订单号: 2023050700002",
        time: "2023-05-07 18:05:12",
        type: "commission"
      }
    ]);
    const filteredRecords = common_vendor.computed(() => {
      let result = records.value;
      if (tabs.value[currentTab.value].status !== "all") {
        if (tabs.value[currentTab.value].status === "income") {
          result = result.filter((record) => record.type !== "withdraw");
        } else {
          result = result.filter((record) => record.type === tabs.value[currentTab.value].status);
        }
      }
      const startTimestamp = new Date(startDate.value).getTime();
      const endTimestamp = new Date(endDate.value).getTime() + 24 * 60 * 60 * 1e3 - 1;
      result = result.filter((record) => {
        const recordTime = new Date(record.time).getTime();
        return recordTime >= startTimestamp && recordTime <= endTimestamp;
      });
      const { field, order } = currentSortOption.value;
      result.sort((a, b) => {
        let comparison = 0;
        if (field === "time") {
          comparison = new Date(a.time).getTime() - new Date(b.time).getTime();
        } else if (field === "amount") {
          comparison = parseFloat(a.amount) - parseFloat(b.amount);
        }
        return order === "asc" ? comparison : -comparison;
      });
      return result;
    });
    const totalAmount = common_vendor.computed(() => {
      let total = 0;
      filteredRecords.value.forEach((record) => {
        if (tabs.value[currentTab.value].status === "all") {
          if (record.type === "withdraw") {
            total -= parseFloat(record.amount);
          } else {
            total += parseFloat(record.amount);
          }
        } else if (tabs.value[currentTab.value].status === "income") {
          if (record.type !== "withdraw") {
            total += parseFloat(record.amount);
          }
        } else if (tabs.value[currentTab.value].status === "withdraw") {
          total += parseFloat(record.amount);
        }
      });
      return total.toFixed(2);
    });
    const recordCount = common_vendor.computed(() => filteredRecords.value.length);
    const avgAmount = common_vendor.computed(() => {
      if (filteredRecords.value.length === 0)
        return "0.00";
      let total = 0;
      filteredRecords.value.forEach((record) => {
        total += parseFloat(record.amount);
      });
      return (total / filteredRecords.value.length).toFixed(2);
    });
    const maxAmount = common_vendor.computed(() => {
      if (filteredRecords.value.length === 0)
        return "0.00";
      let max = 0;
      filteredRecords.value.forEach((record) => {
        const amount = parseFloat(record.amount);
        if (amount > max) {
          max = amount;
        }
      });
      return max.toFixed(2);
    });
    const filterPeriodText = common_vendor.computed(() => {
      return `${startDate.value} 至 ${endDate.value}`;
    });
    function switchTab(index) {
      currentTab.value = index;
    }
    function showDatePicker(type) {
      currentPickerType.value = type;
      common_vendor.index.showToast({
        title: "日期选择功能开发中",
        icon: "none"
      });
    }
    function applyFilter() {
      common_vendor.index.showToast({
        title: "筛选已应用",
        icon: "success"
      });
    }
    function selectQuickPeriod(index) {
      currentPeriod.value = index;
      const today = /* @__PURE__ */ new Date();
      const days = quickPeriods.value[index].days;
      if (days === -1) {
        startDate.value = "2023-01-01";
        endDate.value = formatDate(today);
      } else if (days === 0) {
        startDate.value = formatDate(today);
        endDate.value = formatDate(today);
      } else {
        const pastDate = /* @__PURE__ */ new Date();
        pastDate.setDate(today.getDate() - days);
        startDate.value = formatDate(pastDate);
        endDate.value = formatDate(today);
      }
    }
    function formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    }
    function toggleSortMenu() {
      showSortMenu.value = !showSortMenu.value;
    }
    function selectSortOption(index) {
      currentSortIndex.value = index;
      showSortMenu.value = false;
    }
    function getRecordTypeBg(type) {
      switch (type) {
        case "commission":
          return "linear-gradient(135deg, #34C759 0%, #30D158 100%)";
        case "team":
          return "linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)";
        case "withdraw":
          return "linear-gradient(135deg, #FF3B30 0%, #FF5E3A 100%)";
        case "reward":
          return "linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)";
        default:
          return "linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)";
      }
    }
    function getRecordTypeIcon(type) {
      switch (type) {
        case "commission":
          return "M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6";
        case "team":
          return "M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2M9 11a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM23 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75";
        case "withdraw":
          return "M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4M17 8l-5-5-5 5M12 3v12";
        case "reward":
          return "M12 15c3 0 6-2 6-6s-3-6-6-6-6 2-6 6 3 6 6 6zM2.5 9h4M17.5 9h4M12 15v8M8 21h8";
        default:
          return "M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z";
      }
    }
    function loadMore() {
      if (loading.value || noMore.value)
        return;
      loading.value = true;
      setTimeout(() => {
        noMore.value = true;
        loading.value = false;
      }, 1500);
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.f(tabs.value, (tab, index, i0) => {
          return {
            a: common_vendor.t(tab.name),
            b: index,
            c: currentTab.value === index ? 1 : "",
            d: common_vendor.o(($event) => switchTab(index), index),
            e: currentTab.value === index ? "600" : "400",
            f: currentTab.value === index ? "#FFFFFF" : "#666666",
            g: currentTab.value === index ? "linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)" : "transparent"
          };
        }),
        b: common_vendor.t(startDate.value),
        c: common_vendor.o(($event) => showDatePicker("start")),
        d: common_vendor.t(endDate.value),
        e: common_vendor.o(($event) => showDatePicker("end")),
        f: common_vendor.o(applyFilter),
        g: common_vendor.t(filterPeriodText.value),
        h: common_vendor.f(quickPeriods.value, (period, index, i0) => {
          return {
            a: common_vendor.t(period.name),
            b: index,
            c: currentPeriod.value === index ? 1 : "",
            d: common_vendor.o(($event) => selectQuickPeriod(index), index),
            e: currentPeriod.value === index ? "#AC39FF" : "rgba(255,255,255,0.9)",
            f: currentPeriod.value === index ? "#FFFFFF" : "rgba(255,255,255,0.1)"
          };
        }),
        i: common_vendor.t(tabs.value[currentTab.value].label),
        j: common_vendor.t(totalAmount.value),
        k: common_vendor.t(recordCount.value),
        l: common_vendor.t(avgAmount.value),
        m: common_vendor.t(maxAmount.value),
        n: common_vendor.t(currentSortOption.value.name),
        o: common_vendor.p({
          d: "M6 9l6 6 6-6",
          stroke: "#666666",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        p: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        q: common_vendor.o(toggleSortMenu),
        r: showSortMenu.value
      }, showSortMenu.value ? {
        s: common_vendor.f(sortOptions.value, (option, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(option.name),
            b: currentSortIndex.value === index
          }, currentSortIndex.value === index ? {
            c: "0602b546-3-" + i0 + "," + ("0602b546-2-" + i0),
            d: common_vendor.p({
              d: "M5 12l5 5L20 7",
              stroke: "#AC39FF",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            e: "0602b546-2-" + i0,
            f: common_vendor.p({
              viewBox: "0 0 24 24",
              width: "16",
              height: "16"
            })
          } : {}, {
            g: index,
            h: common_vendor.o(($event) => selectSortOption(index), index),
            i: currentSortIndex.value === index ? "#AC39FF" : "#333333",
            j: currentSortIndex.value === index ? "500" : "400",
            k: index < sortOptions.value.length - 1 ? "1rpx solid #F2F2F7" : "none"
          });
        })
      } : {}, {
        t: common_vendor.f(filteredRecords.value, (record, index, i0) => {
          return {
            a: "0602b546-5-" + i0 + "," + ("0602b546-4-" + i0),
            b: common_vendor.p({
              d: getRecordTypeIcon(record.type),
              stroke: "#FFFFFF",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            c: "0602b546-4-" + i0,
            d: getRecordTypeBg(record.type),
            e: common_vendor.t(record.title),
            f: common_vendor.t(record.type === "withdraw" ? "-" : "+"),
            g: common_vendor.t(record.amount),
            h: record.type === "withdraw" ? "#FF3B30" : "#34C759",
            i: common_vendor.t(record.source),
            j: common_vendor.t(record.time),
            k: index,
            l: index < filteredRecords.value.length - 1 ? "1rpx solid #F2F2F7" : "none"
          };
        }),
        v: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        w: filteredRecords.value.length === 0
      }, filteredRecords.value.length === 0 ? {
        x: common_assets._imports_0$59
      } : {}, {
        y: filteredRecords.value.length > 0
      }, filteredRecords.value.length > 0 ? common_vendor.e({
        z: loading.value
      }, loading.value ? {} : noMore.value ? {} : {
        B: common_vendor.o(loadMore)
      }, {
        A: noMore.value
      }) : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-0602b546"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/distribution/records/index.js.map
