<template>
  <view class="test-page">
    <view class="page-header">
      <text class="page-title">付费功能调试测试</text>
      <text class="page-desc">测试付费模态框是否能正常弹出</text>
    </view>
    
    <!-- 直接测试按钮 -->
    <view class="test-section">
      <view class="section-title">直接测试按钮</view>
      <view class="test-buttons">
        <view class="test-btn" @click="testPaymentModal('publish')">
          <text>测试发布付费</text>
        </view>
        <view class="test-btn" @click="testPaymentModal('top')">
          <text>测试置顶付费</text>
        </view>
        <view class="test-btn" @click="testPaymentModal('refresh')">
          <text>测试刷新付费</text>
        </view>
      </view>
    </view>
    
    <!-- 组件测试 -->
    <view class="test-section">
      <view class="section-title">组件测试 - 商家置顶</view>
      <ConfigurablePremiumActions
        pageType="merchant_top"
        showMode="direct"
        :itemData="{ id: 'test_123', title: '测试置顶', description: '测试商家置顶功能' }"
        @action-completed="handleActionCompleted"
        @action-cancelled="handleActionCancelled"
      />
    </view>
    
    <!-- 组件测试 -->
    <view class="test-section">
      <view class="section-title">组件测试 - 商家刷新</view>
      <ConfigurablePremiumActions
        pageType="merchant_refresh"
        showMode="direct"
        :itemData="{ id: 'test_456', title: '测试刷新', description: '测试商家刷新功能' }"
        @action-completed="handleActionCompleted"
        @action-cancelled="handleActionCancelled"
      />
    </view>
    
    <!-- 测试结果 -->
    <view class="test-results" v-if="testLogs.length > 0">
      <view class="results-title">测试日志</view>
      <view class="log-item" v-for="(log, index) in testLogs" :key="index">
        <text class="log-time">{{ log.time }}</text>
        <text class="log-message">{{ log.message }}</text>
      </view>
    </view>
    
    <!-- 手动模态框测试 -->
    <view class="payment-modal" v-if="showTestModal" @click="closeTestModal">
      <view class="payment-content" @click.stop>
        <view class="payment-header">
          <text class="payment-title">测试付费模态框</text>
          <view class="close-btn" @click="closeTestModal">
            <text>×</text>
          </view>
        </view>
        
        <view class="payment-body">
          <text class="payment-desc">这是一个测试模态框</text>
          
          <view class="duration-options">
            <view class="duration-item" @click="selectTestDuration('3天')">
              <view class="duration-info">
                <text class="duration-text">3天</text>
                <text class="duration-price">¥2.8</text>
              </view>
            </view>
            <view class="duration-item" @click="selectTestDuration('1周')">
              <view class="duration-info">
                <text class="duration-text">1周</text>
                <text class="duration-price">¥5.8</text>
              </view>
              <view class="duration-badge">推荐</view>
            </view>
            <view class="duration-item" @click="selectTestDuration('1个月')">
              <view class="duration-info">
                <text class="duration-text">1个月</text>
                <text class="duration-price">¥19.8</text>
              </view>
            </view>
          </view>
        </view>
        
        <view class="payment-footer">
          <view class="payment-btn cancel-btn" @click="closeTestModal">
            <text>取消</text>
          </view>
          <view class="payment-btn confirm-btn" @click="confirmTestPayment">
            <text>确认支付</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import ConfigurablePremiumActions from '@/components/premium/ConfigurablePremiumActions.vue';

// 测试状态
const showTestModal = ref(false);
const testLogs = ref([]);

// 添加日志
const addLog = (message) => {
  testLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message: message
  });
};

// 直接测试付费模态框
const testPaymentModal = (action) => {
  addLog(`点击测试${action}付费按钮`);
  showTestModal.value = true;
};

// 关闭测试模态框
const closeTestModal = () => {
  addLog('关闭测试模态框');
  showTestModal.value = false;
};

// 选择测试时长
const selectTestDuration = (duration) => {
  addLog(`选择时长: ${duration}`);
};

// 确认测试支付
const confirmTestPayment = () => {
  addLog('确认测试支付');
  showTestModal.value = false;
  uni.showToast({
    title: '测试支付成功',
    icon: 'success'
  });
};

// 处理组件事件
const handleActionCompleted = (result) => {
  addLog(`组件操作完成: ${JSON.stringify(result)}`);
  console.log('组件操作完成:', result);
};

const handleActionCancelled = (result) => {
  addLog(`组件操作取消: ${JSON.stringify(result)}`);
  console.log('组件操作取消:', result);
};

// 页面加载时添加初始日志
addLog('页面加载完成，开始测试');
</script>

<style lang="scss" scoped>
.test-page {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 30rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.page-desc {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.test-section {
  margin-bottom: 30rpx;
  padding: 30rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.test-btn {
  padding: 20rpx;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  color: white;
  border-radius: 12rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.test-btn:active {
  transform: scale(0.98);
}

.test-results {
  margin-top: 30rpx;
  padding: 30rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.results-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.log-item {
  padding: 15rpx;
  margin-bottom: 10rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #4f46e5;
}

.log-time {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 5rpx;
}

.log-message {
  font-size: 26rpx;
  color: #333;
  display: block;
}

/* 复制模态框样式 */
.payment-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(4px);
}

.payment-content {
  width: 90%;
  max-width: 650rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.15);
}

.payment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.payment-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
  color: #666;
  font-size: 40rpx;
}

.payment-body {
  padding: 30rpx;
}

.payment-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
  display: block;
}

.duration-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.duration-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 16rpx;
  background: white;
  transition: all 0.3s ease;
  position: relative;
}

.duration-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.duration-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.duration-price {
  font-size: 40rpx;
  font-weight: 700;
  color: #ff6b6b;
}

.duration-badge {
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8e53);
  color: white;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
}

.payment-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.payment-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  color: white;
  box-shadow: 0 4rpx 15rpx rgba(79, 70, 229, 0.3);
}
</style>
