# 磁州生活网后台管理系统 - 启动脚本测试指南

## 🔍 环境检查

### 第一步：检查必要工具

请在命令提示符中运行以下命令，检查是否安装了必要的工具：

```bash
# 检查 Node.js（需要 18+ 版本）
node --version

# 检查 npm
npm --version

# 检查 Java（需要 17+ 版本）
java -version

# 检查 Docker
docker --version

# 检查 Docker 是否运行
docker info
```

### 第二步：安装缺少的工具

如果上述命令有任何失败，请按照以下链接安装：

- **Node.js**: https://nodejs.org/ (下载 LTS 版本)
- **Java**: https://adoptium.net/ (下载 JDK 17+)
- **Docker**: https://www.docker.com/products/docker-desktop/

## 🧪 脚本测试步骤

### 测试1：环境检查脚本

```bash
# Windows
cd cizhou-admin
check-environment.bat

# 预期结果：显示所有工具的安装状态
```

### 测试2：后端服务启动

```bash
# Windows
cd cizhou-admin\backend
start-dev.bat

# 预期结果：
# 1. 启动 Docker 容器（MySQL、Redis、Nacos）
# 2. 初始化数据库
# 3. 显示服务信息
```

**验证后端服务：**
- 访问 http://localhost:8848/nacos (用户名/密码: nacos/nacos)
- 检查 MySQL 连接：localhost:3306 (root/cizhou123456)
- 检查 Redis 连接：localhost:6379 (密码: cizhou123456)

### 测试3：前端服务启动

```bash
# Windows
cd cizhou-admin\frontend
start-dev.bat

# 预期结果：
# 1. 检查 Node.js 版本
# 2. 安装 npm 依赖（首次运行）
# 3. 启动开发服务器
# 4. 显示访问地址
```

**验证前端服务：**
- 访问 http://localhost:3000
- 看到登录页面
- 使用 admin/admin123 登录

### 测试4：一键启动脚本

```bash
# Windows
cd cizhou-admin
start-all.bat

# 预期结果：
# 1. 检查环境依赖
# 2. 启动后端服务
# 3. 等待后端就绪
# 4. 启动前端服务
```

### 测试5：状态检查脚本

```bash
# Windows（如果支持 bash）
cd cizhou-admin
bash check-status.sh

# 预期结果：显示所有服务的运行状态
```

### 测试6：停止脚本

```bash
# Windows
cd cizhou-admin
stop-all.bat

# 预期结果：
# 1. 停止 Docker 容器
# 2. 停止 Java 进程
# 3. 停止 Node.js 进程
```

## 🐛 常见问题排查

### 问题1：Docker 相关错误

**症状**：Docker 命令失败
**解决方案**：
1. 确保 Docker Desktop 已启动
2. 检查 Docker 是否正在运行：`docker info`
3. 重启 Docker Desktop

### 问题2：端口占用

**症状**：端口 3000、8080、3306 等被占用
**解决方案**：
```bash
# 检查端口占用
netstat -ano | findstr :3000
netstat -ano | findstr :8080
netstat -ano | findstr :3306

# 结束占用进程
taskkill /PID <进程ID> /F
```

### 问题3：npm 安装失败

**症状**：npm install 失败
**解决方案**：
```bash
# 清理缓存
npm cache clean --force

# 删除 node_modules
rmdir /s node_modules

# 使用国内镜像
npm config set registry https://registry.npmmirror.com/

# 重新安装
npm install
```

### 问题4：数据库连接失败

**症状**：无法连接 MySQL
**解决方案**：
1. 检查 Docker 容器状态：`docker ps`
2. 查看容器日志：`docker logs cizhou-mysql-dev`
3. 重启 MySQL 容器：`docker restart cizhou-mysql-dev`

## 📊 测试结果记录

请记录每个测试的结果：

- [ ] 环境检查脚本运行成功
- [ ] 后端服务启动成功
- [ ] 前端服务启动成功
- [ ] 一键启动脚本运行成功
- [ ] 状态检查脚本运行成功
- [ ] 停止脚本运行成功
- [ ] 登录功能测试成功

## 🎯 成功标准

系统启动成功的标志：

1. **后端服务**：
   - Nacos 控制台可访问
   - MySQL、Redis 连接正常
   - API 网关健康检查通过

2. **前端服务**：
   - 前端页面正常加载
   - 登录功能正常
   - 管理后台界面显示正常

3. **整体功能**：
   - 可以使用 admin/admin123 登录
   - 登录后显示管理后台首页
   - 各个菜单可以正常访问

## 📞 获取帮助

如果测试过程中遇到问题：

1. 查看控制台错误信息
2. 检查 Docker 容器日志
3. 参考项目文档
4. 联系技术支持

---

**测试完成后，请将结果反馈给开发团队！**
