"use strict";
const common_vendor = require("../common/vendor.js");
const _sfc_main = {
  __name: "SimplePopup",
  props: {
    type: {
      type: String,
      default: "center"
    }
  },
  setup(__props, { expose: __expose }) {
    const visible = common_vendor.ref(false);
    const open = () => {
      visible.value = true;
    };
    const close = () => {
      visible.value = false;
    };
    __expose({
      open,
      close
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: visible.value
      }, visible.value ? {
        b: common_vendor.n(__props.type),
        c: common_vendor.o(() => {
        }),
        d: common_vendor.o(close)
      } : {});
    };
  }
};
wx.createComponent(_sfc_main);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/SimplePopup.js.map
