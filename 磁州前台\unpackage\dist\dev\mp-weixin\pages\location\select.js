"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const utils_locationService = require("../../utils/locationService.js");
const _sfc_main = {
  __name: "select",
  setup(__props) {
    const keyword = common_vendor.ref("");
    const currentLocation = common_vendor.ref(null);
    const searchResults = common_vendor.ref([]);
    const historyLocations = common_vendor.ref([]);
    const clearKeyword = () => {
      keyword.value = "";
      searchResults.value = [];
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const useCurrentLocation = () => {
      if (!currentLocation.value) {
        common_vendor.index.showLoading({
          title: "获取位置中..."
        });
        utils_locationService.refreshLocation().then((location) => {
          common_vendor.index.hideLoading();
          currentLocation.value = location;
          selectLocation({
            name: location.address || "当前位置",
            address: location.location || "",
            latitude: location.latitude,
            longitude: location.longitude
          });
        }).catch(() => {
          common_vendor.index.hideLoading();
          common_vendor.index.showModal({
            title: "获取位置失败",
            content: "是否使用默认位置？",
            confirmText: "使用默认位置",
            cancelText: "取消",
            success: (res) => {
              if (res.confirm) {
                useDefaultLocation();
              }
            }
          });
        });
      } else {
        selectLocation({
          name: currentLocation.value.address || "当前位置",
          address: currentLocation.value.location || "",
          latitude: currentLocation.value.latitude,
          longitude: currentLocation.value.longitude
        });
      }
    };
    const useDefaultLocation = () => {
      const defaultLoc = {
        name: "磁县",
        address: "河北省邯郸市磁县",
        latitude: 36.313076,
        longitude: 114.347312
      };
      selectLocation(defaultLoc);
    };
    const searchLocation = () => {
      if (!keyword.value.trim()) {
        return;
      }
      common_vendor.index.showLoading({
        title: "搜索中..."
      });
      setTimeout(() => {
        searchResults.value = [
          {
            name: "搜索结果1",
            address: keyword.value + "附近的位置1",
            latitude: 39.908692,
            longitude: 116.397477
          },
          {
            name: "搜索结果2",
            address: keyword.value + "附近的位置2",
            latitude: 39.908692,
            longitude: 116.397477
          }
        ];
        common_vendor.index.hideLoading();
      }, 1e3);
    };
    const selectLocation = (location) => {
      saveToHistory(location);
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2];
      if (prevPage) {
        prevPage.$vm.setSelectedLocation && prevPage.$vm.setSelectedLocation(location);
      }
      common_vendor.index.navigateBack();
    };
    const saveToHistory = (location) => {
      const existIndex = historyLocations.value.findIndex(
        (item) => item.latitude === location.latitude && item.longitude === location.longitude
      );
      if (existIndex > -1) {
        historyLocations.value.splice(existIndex, 1);
      }
      historyLocations.value.unshift(location);
      if (historyLocations.value.length > 10) {
        historyLocations.value = historyLocations.value.slice(0, 10);
      }
      common_vendor.index.setStorageSync("location_history", historyLocations.value);
    };
    const clearHistory = () => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要清空历史记录吗？",
        success: (res) => {
          if (res.confirm) {
            historyLocations.value = [];
            common_vendor.index.setStorageSync("location_history", []);
          }
        }
      });
    };
    common_vendor.onMounted(() => {
      currentLocation.value = utils_locationService.getCurrentLocation();
      if (!currentLocation.value) {
        utils_locationService.refreshLocation().then((location) => {
          currentLocation.value = location;
        }).catch(console.error);
      }
      const history = common_vendor.index.getStorageSync("location_history");
      if (history) {
        historyLocations.value = history;
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(searchLocation),
        b: keyword.value,
        c: common_vendor.o(($event) => keyword.value = $event.detail.value),
        d: keyword.value
      }, keyword.value ? {
        e: common_vendor.o(clearKeyword)
      } : {}, {
        f: common_vendor.o(goBack),
        g: common_vendor.t(currentLocation.value ? currentLocation.value.address : "获取当前位置"),
        h: currentLocation.value
      }, currentLocation.value ? {
        i: common_vendor.t(currentLocation.value.location)
      } : {}, {
        j: common_vendor.o(useCurrentLocation),
        k: searchResults.value.length > 0
      }, searchResults.value.length > 0 ? {
        l: common_vendor.f(searchResults.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.name),
            b: common_vendor.t(item.address),
            c: index,
            d: common_vendor.o(($event) => selectLocation(item), index)
          };
        })
      } : {}, {
        m: historyLocations.value.length > 0 && !keyword.value
      }, historyLocations.value.length > 0 && !keyword.value ? {
        n: common_vendor.o(clearHistory),
        o: common_vendor.f(historyLocations.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.name),
            b: common_vendor.t(item.address),
            c: index,
            d: common_vendor.o(($event) => selectLocation(item), index)
          };
        })
      } : {}, {
        p: keyword.value && searchResults.value.length === 0
      }, keyword.value && searchResults.value.length === 0 ? {
        q: common_assets._imports_0$19
      } : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/location/select.js.map
