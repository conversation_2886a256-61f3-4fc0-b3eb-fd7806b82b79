{"version": 3, "file": "home-service-list.js", "sources": ["subPackages/service/pages/home-service-list.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcc2VydmljZVxwYWdlc1xob21lLXNlcnZpY2UtbGlzdC52dWU"], "sourcesContent": ["<template>\n  <view class=\"service-list-container ios-style\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"back-btn\" @click=\"navigateBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <view class=\"navbar-title\">{{ currentCategory || '家政服务' }}</view>\n      <view class=\"navbar-right\"></view>\n    </view>\n    \n    <!-- 分类标签栏 -->\n    <view class=\"category-tabs\">\n      <scroll-view class=\"tabs-scroll\" scroll-x=\"true\" show-scrollbar=\"false\" :scroll-into-view=\"activeTabId\">\n        <view \n          v-for=\"(category, index) in categories\" \n          :key=\"index\" \n          :id=\"'tab-' + index\"\n          class=\"tab-item\" \n          :class=\"{ active: currentCategory === category }\"\n          @click=\"switchCategory(category)\"\n        >\n          {{ category }}\n        </view>\n      </scroll-view>\n    </view>\n    \n    <!-- 筛选栏 -->\n    <view class=\"filter-section\">\n      <!-- 区域筛选 -->\n      <view class=\"filter-item\" ref=\"areaBtn\" @click=\"toggleAreaFilter\">\n        <text class=\"filter-text\" :class=\"{ 'active-filter': selectedArea !== '全部区域' }\">\n          {{selectedArea}}\n        </text>\n        <view class=\"filter-arrow\" :class=\"{ 'arrow-up': showAreaFilter }\"></view>\n      </view>\n      \n      <!-- 排序筛选 -->\n      <view class=\"filter-item\" ref=\"sortBtn\" @click=\"toggleSortFilter\">\n        <text class=\"filter-text\" :class=\"{ 'active-filter': selectedSort !== '默认排序' }\">\n          {{selectedSort}}\n        </text>\n        <view class=\"filter-arrow\" :class=\"{ 'arrow-up': showSortFilter }\"></view>\n      </view>\n      </view>\n    \n    <!-- 区域筛选弹出内容 -->\n    <view class=\"filter-dropdown area-dropdown\" v-if=\"showAreaFilter\" :style=\"{ top: areaDropdownTop + 'px' }\">\n      <scroll-view scroll-y class=\"dropdown-scroll\">\n        <view class=\"dropdown-item\" \n          v-for=\"(area, index) in areaList\" \n          :key=\"index\"\n          :class=\"{ 'active-item': area === selectedArea }\"\n          @click=\"selectArea(area)\">\n          <text class=\"dropdown-item-text\">{{area}}</text>\n          <text class=\"dropdown-item-check\" v-if=\"area === selectedArea\">✓</text>\n        </view>\n      </scroll-view>\n    </view>\n    \n    <!-- 排序筛选弹出内容 -->\n    <view class=\"filter-dropdown sort-dropdown\" v-if=\"showSortFilter\" :style=\"{ top: sortDropdownTop + 'px' }\">\n      <view class=\"dropdown-item\" \n        v-for=\"(sort, index) in sortList\" \n        :key=\"index\"\n        :class=\"{ 'active-item': sort === selectedSort }\"\n        @click=\"selectSort(sort)\">\n        <text class=\"dropdown-item-text\">{{sort}}</text>\n        <text class=\"dropdown-item-check\" v-if=\"sort === selectedSort\">✓</text>\n      </view>\n    </view>\n    \n    <!-- 遮罩层 -->\n    <view class=\"filter-mask\" \n      v-if=\"showAreaFilter || showSortFilter\"\n      @click=\"closeAllFilters\"></view>\n    \n    <!-- 服务列表 -->\n    <scroll-view class=\"service-scroll\" scroll-y=\"true\" @scrolltolower=\"loadMore\">\n      <view class=\"service-list\">\n        <!-- 空状态 -->\n        <view v-if=\"serviceList.length === 0 && !isLoading\" class=\"empty-state\">\n          <image src=\"/static/images/empty.png\" mode=\"aspectFit\" class=\"empty-image\"></image>\n          <text class=\"empty-text\">暂无相关服务信息</text>\n        </view>\n        \n        <!-- 服务卡片列表 -->\n        <view \n          v-for=\"(service, index) in serviceList\" \n          :key=\"index\" \n          class=\"service-item\"\n          @click=\"navigateToDetail(service.id)\"\n        >\n          <view class=\"service-content\">\n            <view class=\"service-header\">\n              <view class=\"service-tag-container\">\n                <text class=\"service-tag\">{{ service.category }}</text>\n            </view>\n              <text class=\"service-time\">{{ service.time }}</text>\n            </view>\n            \n            <text class=\"service-title\">{{ service.title }}</text>\n            \n            <view class=\"service-images\" v-if=\"service.images && service.images.length > 0\">\n              <image \n                v-for=\"(img, imgIndex) in service.images.slice(0, 3)\" \n                :key=\"imgIndex\" \n                class=\"service-image\"\n                :src=\"img\" \n                mode=\"aspectFill\" \n              ></image>\n            </view>\n            \n            <view class=\"service-info-card\">\n              <view class=\"info-row\">\n                <view class=\"info-item\">\n                  <text class=\"info-label\">服务区域</text>\n                  <text class=\"info-value\">{{ service.area || '全城' }}</text>\n                </view>\n                <view class=\"info-item\">\n                  <text class=\"info-label\">价格参考</text>\n                  <text class=\"info-value highlight\">{{ service.price || '面议' }}</text>\n                </view>\n              </view>\n            </view>\n            \n            <view class=\"service-footer\">\n              <view class=\"service-meta\">\n                <view class=\"views-icon\"></view>\n                <text class=\"service-views\">{{ service.views }}次浏览</text>\n              </view>\n              <view class=\"service-actions\">\n                <view class=\"action-btn\" @click.stop=\"contactService(service.id)\">\n                  <view class=\"contact-icon\"></view>\n                  <text class=\"action-text\">立即联系</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      </view>\n      \n        <!-- 加载更多 -->\n        <view v-if=\"loading && serviceList.length > 0\" class=\"loading-more\">\n          <view class=\"loading-indicator\"></view>\n        <text class=\"loading-text\">加载中...</text>\n      </view>\n        \n        <!-- 没有更多数据 -->\n        <view v-if=\"!hasMore && serviceList.length > 0\" class=\"loading-more\">\n          <text class=\"loading-text\">没有更多数据了</text>\n        </view>\n      </view>\n    </scroll-view>\n    \n    <!-- 发布按钮 -->\n    <view class=\"publish-btn\" @click=\"navigateToPublish\">\n      <text class=\"publish-icon\">+</text>\n      <text class=\"publish-text\">发布服务</text>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, computed, nextTick, onMounted } from 'vue';\n\n// 状态变量\nconst statusBarHeight = ref(20);\nconst currentTab = ref(0);\nconst sortBy = ref('latest');\nconst isRefreshing = ref(false);\nconst hasMore = ref(true);\nconst page = ref(1);\nconst limit = ref(10);\nconst serviceList = ref([]);\nconst subCategories = ref([\n  { name: '全部', type: 'all' },\n  { name: '家政服务', type: 'home_cleaning' },\n  { name: '维修改造', type: 'repair' },\n  { name: '上门安装', type: 'installation' },\n  { name: '开锁换锁', type: 'locksmith' },\n  { name: '搬家拉货', type: 'moving' },\n  { name: '上门美容', type: 'beauty' },\n  { name: '上门家教', type: 'tutor' },\n  { name: '宠物服务', type: 'pet_service' },\n  { name: '上门疏通', type: 'plumbing' },\n  { name: '其他类型', type: 'other' }\n]);\n// 为每个子分类添加示例数据，实际应从数据库获取\nconst sampleServiceData = ref([]);\nconst currentCategory = ref('全部');\nconst categories = ref(['全部', '家政服务', '维修改造', '上门安装', '开锁换锁', '搬家拉货', '上门美容', '上门家教', '宠物服务', '上门疏通', '其他类型']);\nconst sortType = ref('time');\nconst filterActive = ref(false);\nconst loading = ref(false);\nconst isLoading = ref(false);\n\n// 新增筛选相关数据\nconst showAreaFilter = ref(false);\nconst showSortFilter = ref(false);\nconst selectedArea = ref('全部区域');\nconst selectedSort = ref('默认排序');\nconst areaDropdownTop = ref(120); // 区域筛选弹窗位置\nconst sortDropdownTop = ref(120); // 排序筛选弹窗位置\n\n// 筛选选项\nconst areaList = ref(['全部区域', '城区', '磁州镇', '讲武城镇', '岳城镇', '观台镇', '白土镇', '黄沙镇']);\nconst sortList = ref(['默认排序', '离我最近', '最新发布', '热门推荐', '价格最低', '价格最高']);\n\n// 当前用户位置\nconst userLocation = ref(null);\n\n// 示例变量，用于演示目的\nconst demoServiceCategory = ref('上门安装');\n\n// 当前活动tab的ID，用于scroll-into-view\nconst activeTabId = ref('tab-0');\n\n// 初始化示例数据\nconst initSampleData = () => {\n  sampleServiceData.value = [\n    {\n      id: '1',\n      type: 'home_service',\n      serviceType: 'home_cleaning',\n      serviceTypeName: '家政服务',\n      content: '专业保洁团队，提供家庭、办公室清洁服务，价格实惠',\n      time: '2023-06-30 10:25',\n      views: 125,\n      images: ['/static/images/sample/cleaning1.jpg', '/static/images/sample/cleaning2.jpg']\n    },\n    {\n      id: '2',\n      type: 'home_service',\n      serviceType: 'repair',\n      serviceTypeName: '维修改造',\n      content: '水电维修，空调维修，灯具安装，墙面翻新等各类家庭维修服务',\n      time: '2023-06-29 15:40',\n      views: 98,\n      images: ['/static/images/sample/repair1.jpg']\n    },\n    {\n      id: '3',\n      type: 'home_service',\n      serviceType: 'installation',\n      serviceTypeName: '上门安装',\n      content: '家具组装，家电安装，网络设备安装，提供专业上门服务',\n      time: '2023-06-28 09:15',\n      views: 76\n    },\n    {\n      id: '4',\n      type: 'home_service',\n      serviceType: 'locksmith',\n      serviceTypeName: '开锁换锁',\n      content: '24小时开锁服务，换锁，修锁，保险柜开锁，汽车开锁',\n      time: '2023-06-27 21:05',\n      views: 112,\n      images: ['/static/images/sample/lock1.jpg', '/static/images/sample/lock2.jpg']\n    },\n    {\n      id: '5',\n      type: 'home_service',\n      serviceType: 'moving',\n      serviceTypeName: '搬家拉货',\n      content: '专业搬家服务，小型搬家，大件运输，长途托运，价格合理',\n      time: '2023-06-26 14:30',\n      views: 88\n    },\n    {\n      id: '6',\n      type: 'home_service',\n      serviceType: 'beauty',\n      serviceTypeName: '上门美容',\n      content: '专业美甲美睫，上门服务，使用进口产品，安全卫生',\n      time: '2023-06-25 16:20',\n      views: 143,\n      images: ['/static/images/sample/beauty1.jpg']\n    },\n    {\n      id: '7',\n      type: 'home_service',\n      serviceType: 'tutor',\n      serviceTypeName: '上门家教',\n      content: '小学初中高中各科家教，有经验的老师，耐心负责',\n      time: '2023-06-24 11:00',\n      views: 67\n    },\n    {\n      id: '8',\n      type: 'home_service',\n      serviceType: 'pet_service',\n      serviceTypeName: '宠物服务',\n      content: '宠物洗澡，美容，寄养，上门遛狗，专业服务',\n      time: '2023-06-23 13:45',\n      views: 124,\n      images: ['/static/images/sample/pet1.jpg', '/static/images/sample/pet2.jpg']\n    },\n    {\n      id: '9',\n      type: 'home_service',\n      serviceType: 'plumbing',\n      serviceTypeName: '上门疏通',\n      content: '管道疏通，马桶疏通，下水道疏通，快速上门',\n      time: '2023-06-22 08:30',\n      views: 92\n    },\n    {\n      id: '10',\n      type: 'home_service',\n      serviceType: 'other',\n      serviceTypeName: '其他类型',\n      content: '提供各类上门服务，有需要请联系，价格面议',\n      time: '2023-06-21 17:10',\n      views: 78,\n      images: ['/static/images/sample/other1.jpg']\n    }\n  ];\n};\n\n// 返回上一页\nconst navigateBack = () => {\n  uni.navigateBack();\n};\n\n// 切换标签\nconst switchTab = (index) => {\n  if (currentTab.value !== index) {\n    currentTab.value = index;\n    page.value = 1;\n    serviceList.value = [];\n    hasMore.value = true;\n    loadServiceList();\n  }\n};\n\n// 切换排序方式\nconst toggleSort = (sort) => {\n  if (sortBy.value !== sort) {\n    sortBy.value = sort;\n    page.value = 1;\n    serviceList.value = [];\n    hasMore.value = true;\n    loadServiceList();\n  }\n};\n\n// 加载服务列表数据\nconst loadServiceList = async () => {\n  if (loading.value || !hasMore.value) return;\n  \n  loading.value = true;\n  \n  try {\n    // 减少模拟延迟时间，提高响应速度\n    await new Promise(resolve => setTimeout(resolve, 300));\n    \n    // 获取模拟数据（每次最多获取5条，减少渲染压力）\n    const newData = getMockServiceData();\n    \n    // 根据当前分类筛选数据\n    let filteredData = newData;\n    if (currentCategory.value !== '全部') {\n      filteredData = newData.filter(item => item.category === currentCategory.value);\n    }\n    \n    // 根据选择的区域筛选\n    if (selectedArea.value !== '全部区域') {\n      filteredData = filteredData.filter(item => item.area === selectedArea.value || \n                              (item.area && item.area.includes(selectedArea.value)));\n    }\n    \n    // 根据排序方式处理数据\n    applySorting(filteredData);\n    \n    // 更新列表数据\n    if (page.value === 1) {\n      serviceList.value = filteredData;\n    } else {\n      serviceList.value = [...serviceList.value, ...filteredData];\n    }\n    \n    // 更新分页信息\n    hasMore.value = filteredData.length >= limit.value;\n    page.value++;\n    \n  } catch (error) {\n    console.error('加载服务列表失败:', error);\n    uni.showToast({\n      title: '加载失败，请重试',\n      icon: 'none'\n    });\n  } finally {\n    loading.value = false;\n    isRefreshing.value = false;\n  }\n};\n\n// 提取排序逻辑为单独方法，优化代码结构\nconst applySorting = (data) => {\n  if (!data || data.length === 0) return;\n  \n  if (sortType.value === 'popular') {\n    data.sort((a, b) => b.views - a.views);\n  } else if (sortType.value === 'price_low') {\n    // 价格低到高排序（去掉价格单位，仅比较数字部分）\n    data.sort((a, b) => {\n      const priceA = parseFloat(a.price ? a.price.replace(/[^\\d.]/g, '') : '99999');\n      const priceB = parseFloat(b.price ? b.price.replace(/[^\\d.]/g, '') : '99999');\n      return priceA - priceB;\n    });\n  } else if (sortType.value === 'price_high') {\n    // 价格高到低排序\n    data.sort((a, b) => {\n      const priceA = parseFloat(a.price ? a.price.replace(/[^\\d.]/g, '') : '0');\n      const priceB = parseFloat(b.price ? b.price.replace(/[^\\d.]/g, '') : '0');\n      return priceB - priceA;\n    });\n  } else if (sortType.value === 'distance' && userLocation.value) {\n    // 按距离排序\n    data.sort((a, b) => {\n      // 计算两个服务到用户的距离\n      const distanceA = calculateDistance(a.location || getLocationFromArea(a.area));\n      const distanceB = calculateDistance(b.location || getLocationFromArea(b.area));\n      return distanceA - distanceB;\n    });\n  }\n  \n  return data;\n};\n\nconst getMockServiceData = () => {\n  // 模拟服务数据 - 减少返回数据量，提高渲染速度\n  const mockServices = [\n    {\n      id: '101',\n      category: '家政服务',\n      title: '专业家庭保洁 深度清洁 开荒保洁 玻璃清洗',\n      time: '2小时前',\n      views: 128,\n      area: '城区',\n      price: '80元/小时',\n      images: [\n        '/static/images/service/cleaning1.jpg'\n      ],\n      location: { latitude: 36.358, longitude: 114.518 }\n    },\n    {\n      id: '102',\n      category: '维修改造',\n      title: '专业水电维修安装 水管漏水 马桶疏通 灯具维修',\n      time: '3小时前',\n      views: 95,\n      area: '高新区',\n      price: '上门费 30元',\n      images: [\n        '/static/images/service/repair1.jpg'\n      ],\n      location: { latitude: 36.368, longitude: 114.528 }\n    },\n    {\n      id: '103',\n      category: '上门安装',\n      title: '专业安装窗帘 晾衣架 各类家具 价格实惠 服务好',\n      time: '昨天',\n      views: 210,\n      area: '全城',\n      price: '面议',\n      images: [\n        '/static/images/service/install1.jpg'\n      ],\n      location: { latitude: 36.354, longitude: 114.514 }\n    },\n    {\n      id: '104',\n      category: '搬家拉货',\n      title: '专业小型搬家 单身公寓 居民搬家 长短途运输',\n      time: '3天前',\n      views: 156,\n      area: '磁州镇',\n      price: '98元起',\n      images: [\n        '/static/images/service/moving1.jpg'\n      ],\n      location: { latitude: 36.378, longitude: 114.553 }\n    },\n    {\n      id: '105',\n      category: '开锁换锁',\n      title: '专业开锁换锁 汽车锁 保险柜 指纹密码锁安装',\n      time: '4天前',\n      views: 86,\n      area: '岳城镇',\n      price: '开锁 50元起',\n      images: [\n        '/static/images/service/lock1.jpg'\n      ],\n      location: { latitude: 36.398, longitude: 114.493 }\n    }\n  ];\n  \n  // 根据分类过滤\n  let filteredData = mockServices;\n  if (currentCategory.value !== '全部') {\n    filteredData = mockServices.filter(item => item.category === currentCategory.value);\n  }\n  \n  // 减少每页返回的数据量\n  const pageSize = Math.min(5, limit.value);\n  return filteredData.slice(0, pageSize);\n};\n\n// 下拉刷新\nconst onRefresh = () => {\n  isRefreshing.value = true;\n  page.value = 1;\n  hasMore.value = true;\n  loadServiceList();\n};\n\n// 加载更多\nconst loadMore = () => {\n  loadServiceList();\n};\n\n// 跳转到详情页\nconst navigateToDetail = (id) => {\n  // 找到对应的服务\n  const service = serviceList.value.find(item => item.id === id);\n  if (!service) return;\n  \n  // 获取服务的分类，如果服务项没有分类则使用当前选中的分类\n  const serviceCategory = service.category || currentCategory.value || '家政服务';\n  console.log('跳转到详情页 - 服务ID:', id, '分类:', serviceCategory);\n  \n  // 根据分类名称获取对应的服务类型代码\n  let serviceType = service.serviceType || '';\n  if (!serviceType) {\n    switch(serviceCategory) {\n      case '开锁换锁':\n        serviceType = 'locksmith';\n        break;\n      case '上门安装':\n        serviceType = 'installation';\n        break;\n      case '维修改造':\n        serviceType = 'repair';\n        break;\n      case '搬家拉货':\n        serviceType = 'moving';\n        break;\n      case '上门美容':\n        serviceType = 'beauty';\n        break;\n      case '上门家教':\n        serviceType = 'tutor';\n        break;\n      case '宠物服务':\n        serviceType = 'pet_service';\n        break;\n      case '上门疏通':\n        serviceType = 'plumbing';\n        break;\n      case '家政保洁':\n      case '家政服务':\n      default:\n        serviceType = 'home_cleaning';\n        break;\n    }\n  }\n  \n  // 添加触感反馈\n  uni.vibrateShort && uni.vibrateShort();\n  \n  // 显示加载提示\n  uni.showLoading({\n    title: '正在加载...',\n    mask: true\n  });\n  \n  // 构建完整的URL，确保所有必要参数都被传递\n  const url = `/pages/publish/home-service-detail?id=${id}&category=${encodeURIComponent(serviceCategory)}&type=${serviceType}`;\n  console.log('详情页跳转URL:', url);\n  \n  // 执行跳转\n  uni.navigateTo({\n    url: url,\n    success: () => {\n      console.log('跳转成功');\n      uni.hideLoading();\n    },\n    fail: (err) => {\n      console.error('跳转失败:', err);\n      uni.hideLoading();\n      uni.showToast({\n        title: '页面跳转失败，请重试',\n        icon: 'none'\n      });\n    }\n  });\n};\n\n// 跳转到发布页\nconst navigateToPublish = () => {\n  uni.navigateTo({\n    url: '/pages/publish/service-category'\n  });\n};\n\nconst switchCategory = (category) => {\n  if (currentCategory.value !== category) {\n    currentCategory.value = category;\n    page.value = 1;\n    serviceList.value = [];\n    hasMore.value = true;\n    loadServiceList();\n    \n    // 设置活动tab的ID\n    const index = categories.value.findIndex(item => item === category);\n    if (index >= 0) {\n      activeTabId.value = 'tab-' + index;\n    }\n  }\n};\n\nconst sortByTime = () => {\n  sortType.value = 'time';\n  page.value = 1;\n  serviceList.value = [];\n  hasMore.value = true;\n  loadServiceList();\n};\n\nconst sortByPopular = () => {\n  sortType.value = 'popular';\n  page.value = 1;\n  serviceList.value = [];\n  hasMore.value = true;\n  loadServiceList();\n};\n\nconst openFilter = () => {\n  filterActive.value = !filterActive.value;\n  \n  // 添加触感反馈\n  uni.vibrateShort();\n  \n  // 打开筛选页面\n  uni.navigateTo({\n    url: `/subPackages/service/pages/filter?type=home&title=${encodeURIComponent('到家服务')}&category=${encodeURIComponent(currentCategory.value)}`\n  });\n};\n\nconst contactService = (id) => {\n  // 阻止冒泡\n  event.stopPropagation();\n  \n  // 找到对应的服务\n  const service = serviceList.value.find(item => item.id === id);\n  if (!service) return;\n  \n  // 显示联系方式对话框\n  uni.showModal({\n    title: '联系方式',\n    content: '电话: 188****1234\\n微信: same-as-phone',\n    confirmText: '拨打电话',\n    cancelText: '复制微信',\n    success: (res) => {\n      if (res.confirm) {\n        // 拨打电话\n        uni.makePhoneCall({\n          phoneNumber: '18812341234'\n        });\n      } else if (res.cancel) {\n        // 复制微信号\n        uni.setClipboardData({\n          data: 'same-as-phone',\n          success: () => {\n            uni.showToast({\n              title: '微信号已复制',\n              icon: 'none'\n            });\n          }\n        });\n      }\n    }\n  });\n};\n\n// 选择区域\nconst selectArea = (area) => {\n  // 触感反馈\n  uni.vibrateShort();\n  \n  selectedArea.value = area;\n  showAreaFilter.value = false;\n  resetListAndReload();\n};\n\n// 选择排序方式\nconst selectSort = (sort) => {\n  // 触感反馈\n  uni.vibrateShort();\n  \n  selectedSort.value = sort;\n  showSortFilter.value = false;\n  resetListAndReload();\n  \n  // 根据选择的排序设置内部排序类型\n  if (sort === '最新发布') {\n    sortType.value = 'time';\n  } else if (sort === '热门推荐') {\n    sortType.value = 'popular';\n  } else if (sort === '价格最低') {\n    sortType.value = 'price_low';\n  } else if (sort === '价格最高') {\n    sortType.value = 'price_high';\n  } else if (sort === '离我最近') {\n    sortType.value = 'distance';\n    sortByDistance();\n  } else {\n    sortType.value = 'default';\n  }\n};\n\n// 按距离排序\nconst sortByDistance = () => {\n  // 获取当前位置\n  uni.getLocation({\n    type: 'gcj02',\n    success: (res) => {\n      const location = {\n        latitude: res.latitude,\n        longitude: res.longitude\n      };\n      \n      // 存储当前位置\n      userLocation.value = location;\n      \n      // 刷新列表\n      resetListAndReload();\n    },\n    fail: (err) => {\n      console.error('获取位置失败', err);\n      uni.showToast({\n        title: '无法获取位置信息，请检查定位权限',\n        icon: 'none'\n      });\n      \n      // 如果获取位置失败，改回默认排序\n      selectedSort.value = '默认排序';\n      sortType.value = 'default';\n    }\n  });\n};\n\n// 关闭所有筛选弹窗\nconst closeAllFilters = () => {\n  showAreaFilter.value = false;\n  showSortFilter.value = false;\n};\n\n// 重置列表并重新加载\nconst resetListAndReload = () => {\n  page.value = 1;\n  serviceList.value = [];\n  hasMore.value = true;\n  loadServiceList();\n};\n\n// 根据地区名称获取模拟位置坐标\nconst getLocationFromArea = (area) => {\n  // 这里模拟不同区域的地理坐标\n  const locationMap = {\n    '城区': { latitude: 36.354, longitude: 114.511 },\n    '磁州镇': { latitude: 36.374, longitude: 114.551 },\n    '讲武城镇': { latitude: 36.334, longitude: 114.471 },\n    '岳城镇': { latitude: 36.394, longitude: 114.491 },\n    '观台镇': { latitude: 36.314, longitude: 114.531 },\n    '白土镇': { latitude: 36.284, longitude: 114.501 },\n    '黄沙镇': { latitude: 36.404, longitude: 114.461 },\n    '全城': { latitude: 36.354, longitude: 114.511 },\n    '高新区': { latitude: 36.368, longitude: 114.528 }\n  };\n  \n  return locationMap[area] || { latitude: 36.354, longitude: 114.511 };\n};\n\n// 计算两点之间的距离（使用Haversine公式计算球面距离）\nconst calculateDistance = (serviceLocation) => {\n  if (!userLocation.value || !serviceLocation) {\n    return Number.MAX_VALUE; // 返回最大值，排到最后\n  }\n  \n  // 地球半径（单位：千米）\n  const R = 6371;\n  \n  // 将经纬度转换为弧度\n  const lat1 = userLocation.value.latitude * Math.PI / 180;\n  const lat2 = serviceLocation.latitude * Math.PI / 180;\n  const dLat = (serviceLocation.latitude - userLocation.value.latitude) * Math.PI / 180;\n  const dLon = (serviceLocation.longitude - userLocation.value.longitude) * Math.PI / 180;\n  \n  // 使用Haversine公式计算两点之间的距离\n  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +\n          Math.cos(lat1) * Math.cos(lat2) * \n          Math.sin(dLon / 2) * Math.sin(dLon / 2);\n  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n  const distance = R * c;\n  \n  return distance;\n};\n\n// 添加自动滚动到选中分类的方法\nconst scrollToCategory = () => {\n  try {\n    // 获取当前分类的索引\n    const index = categories.value.findIndex(item => item === currentCategory.value);\n    if (index >= 0) {\n      // 设置activeTabId触发scroll-into-view\n      activeTabId.value = 'tab-' + index;\n      console.log('设置滚动到:', activeTabId.value);\n    }\n  } catch (e) {\n    console.error('自动滚动失败:', e);\n  }\n};\n\n// 示例方法：在服务详情页中如何实现\"查看更多信息\"的跳转\n// 这个方法应该在服务详情页实现，这里仅作为示例\nconst exampleViewMoreFromDetail = () => {\n  // 假设当前是在服务详情页，category是当前服务的分类\n  const category = demoServiceCategory.value; // 例如\"上门安装\"\n  \n  // 跳转到服务列表页，并传递分类参数\n  uni.navigateTo({\n    url: `/subPackages/service/pages/home-service-list?category=${encodeURIComponent(category)}`\n  });\n  \n  // 注意：在实际的详情页面中，应该这样使用：\n  // uni.navigateTo({\n  //   url: `/subPackages/service/pages/home-service-list?category=${encodeURIComponent(serviceData.category)}`\n  // });\n};\n\n// 切换区域筛选\nconst toggleAreaFilter = () => {\n  showAreaFilter.value = !showAreaFilter.value;\n  showSortFilter.value = false;\n  \n  if (showAreaFilter.value) {\n    // 动态获取按钮底部位置\n    nextTick(() => {\n      const query = uni.createSelectorQuery();\n      query.select('.filter-item[ref=areaBtn]').boundingClientRect(rect => {\n        if (rect) {\n          areaDropdownTop.value = rect.bottom;\n        }\n      }).exec();\n    });\n  }\n};\n\n// 切换排序筛选\nconst toggleSortFilter = () => {\n  showSortFilter.value = !showSortFilter.value;\n  showAreaFilter.value = false;\n  \n  if (showSortFilter.value) {\n    // 动态获取按钮底部位置\n    nextTick(() => {\n      const query = uni.createSelectorQuery();\n      query.select('.filter-item[ref=sortBtn]').boundingClientRect(rect => {\n        if (rect) {\n          sortDropdownTop.value = rect.bottom;\n        }\n      }).exec();\n    });\n  }\n};\n\n// 页面加载时执行\nonMounted(() => {\n  // 获取状态栏高度\n  const sysInfo = uni.getSystemInfoSync();\n  statusBarHeight.value = sysInfo.statusBarHeight;\n  \n  // 获取路由参数\n  const pages = getCurrentPages();\n  const page = pages[pages.length - 1];\n  const options = page.$page?.options || {};\n  \n  // 初始化示例数据 - 改为延迟初始化，减轻页面初始加载压力\n  setTimeout(() => {\n    initSampleData();\n  }, 100);\n  \n  // 如果有子分类参数，自动切换到对应分类\n  if (options.subType && options.subName) {\n    // 设置当前分类\n    currentCategory.value = decodeURIComponent(options.subName);\n    // 重置页码\n    page.value = 1;\n    serviceList.value = [];\n    hasMore.value = true;\n    \n    // 设置activeTabId\n    const index = categories.value.findIndex(item => item === currentCategory.value);\n    if (index >= 0) {\n      activeTabId.value = 'tab-' + index;\n    }\n  } else if (options.category) {\n    // 如果直接传递了分类名称参数\n    const categoryName = decodeURIComponent(options.category);\n    // 检查是否存在于分类列表中\n    if (categories.value.includes(categoryName)) {\n      currentCategory.value = categoryName;\n      page.value = 1;\n      serviceList.value = [];\n      hasMore.value = true;\n      \n      // 设置activeTabId\n      const index = categories.value.findIndex(item => item === currentCategory.value);\n      if (index >= 0) {\n        activeTabId.value = 'tab-' + index;\n      }\n    }\n  }\n  \n  // 加载服务列表数据 - 延迟加载以减轻初始渲染压力\n  setTimeout(() => {\n    loadServiceList();\n    \n    // 自动滚动到选中的分类\n    nextTick(() => {\n      scrollToCategory();\n    });\n  }, 200);\n});\n\n// 下拉刷新处理\nuni.onPullDownRefresh(() => {\n  page.value = 1;\n  serviceList.value = [];\n  hasMore.value = true;\n  loadServiceList();\n  setTimeout(() => {\n    uni.stopPullDownRefresh();\n  }, 1000);\n});\n</script>\n\n<style lang=\"scss\" scoped>\n.service-list-container {\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: #f5f5f5;\n}\n\n/* 自定义导航栏 */\n.custom-navbar {\n  display: flex;\n  align-items: center;\n  height: 44px;\n  background: #1677FF;\n  color: #fff;\n  padding: 0 15px;\n  position: relative;\n  z-index: 100;\n  box-shadow: none;\n  border-radius: 0;\n}\n\n.back-btn {\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: flex-start;\n}\n\n.back-icon {\n  width: 10px;\n  height: 10px;\n  border-top: 2px solid #fff;\n  border-left: 2px solid #fff;\n  transform: rotate(-45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  color: #fff;\n  font-size: 18px;\n  font-weight: 500;\n}\n\n.navbar-right {\n  width: 40px;\n}\n\n/* 分类标签栏 */\n.category-tabs {\n  background-color: #ffffff;\n  position: sticky;\n  top: 0;\n  z-index: 10;\n  box-shadow: none;\n  border-bottom: 1rpx solid #f5f5f5;\n  margin-top: 0;\n}\n\n.tabs-scroll {\n  white-space: nowrap;\n  height: 80rpx;\n  padding: 0 10rpx;\n}\n\n.tab-item {\n  display: inline-block;\n  padding: 0 30rpx;\n  height: 80rpx;\n  line-height: 80rpx;\n  font-size: 28rpx;\n  color: #333;\n  position: relative;\n}\n\n.tab-item.active {\n  color: #1677FF;\n  font-weight: 500;\n}\n\n.tab-item.active::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 40rpx;\n  height: 4rpx;\n  background-color: #1677FF;\n  border-radius: 2rpx;\n}\n\n/* 筛选栏 */\n.filter-section {\n  display: flex;\n  align-items: center;\n  height: 80rpx;\n  background-color: #fff;\n  padding: 0 20rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.filter-item {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  font-size: 28rpx;\n  color: #666;\n  position: relative;\n}\n\n.filter-item:not(:last-child)::after {\n  content: '';\n  position: absolute;\n  right: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 1rpx;\n  height: 30rpx;\n  background-color: #f0f0f0;\n}\n\n.filter-text {\n  font-size: 28rpx;\n  color: #666;\n}\n\n.active-filter {\n  color: #1677FF;\n  font-weight: 500;\n}\n\n.filter-arrow {\n  width: 14rpx;\n  height: 14rpx;\n  border-right: 2rpx solid #999;\n  border-bottom: 2rpx solid #999;\n  transform: rotate(45deg);\n  margin-left: 8rpx;\n  margin-top: -6rpx;\n  transition: transform 0.2s;\n}\n\n.filter-arrow.arrow-up {\n  transform: rotate(-135deg);\n  margin-top: 6rpx;\n  border-right: 2rpx solid #1677FF;\n  border-bottom: 2rpx solid #1677FF;\n}\n\n.filter-icon {\n  width: 24rpx;\n  height: 24rpx;\n  border: none;\n  position: relative;\n  margin-left: 8rpx;\n  transform: none;\n}\n\n.filter-icon::before, .filter-icon::after {\n  content: '';\n  position: absolute;\n  background-color: #999;\n}\n\n.filter-icon::before {\n  width: 100%;\n  height: 2rpx;\n  top: 6rpx;\n  left: 0;\n}\n\n.filter-icon::after {\n  width: 100%;\n  height: 2rpx;\n  bottom: 6rpx;\n  left: 0;\n}\n\n.filter-icon.active-icon::before, .filter-icon.active-icon::after {\n  background-color: #1677FF;\n}\n\n/* 下拉菜单样式 */\n.filter-dropdown {\n  position: absolute;\n  background-color: #fff;\n  z-index: 101;\n  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);\n  max-height: 600rpx;\n  animation: dropDown 0.25s cubic-bezier(0.215, 0.61, 0.355, 1);\n  border-radius: 0 0 12rpx 12rpx;\n  overflow: hidden;\n}\n\n@keyframes dropDown {\n  from { transform: translateY(-8rpx); opacity: 0; }\n  to { transform: translateY(0); opacity: 1; }\n}\n\n.dropdown-scroll {\n  max-height: 600rpx;\n}\n\n.dropdown-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 24rpx 30rpx;\n  border-bottom: 1rpx solid #f5f5f5;\n}\n\n.dropdown-item.active-item {\n  color: #1677FF;\n}\n\n.dropdown-item-text {\n  font-size: 28rpx;\n}\n\n.dropdown-item-check {\n  color: #1677FF;\n  font-weight: bold;\n}\n\n.dropdown-title {\n  font-size: 28rpx;\n  font-weight: 500;\n}\n\n/* 遮罩层 */\n.filter-mask {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.4);\n  z-index: 100;\n}\n\n/* iOS风格服务列表 */\n.service-scroll {\n  flex: 1;\n  overflow: hidden;\n  padding: 0;\n  margin-bottom: 16px;\n}\n\n.service-list {\n  padding: 8px 0;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.service-item {\n  background-color: #fff;\n  border-radius: 12px;\n  margin-bottom: 12px;\n  overflow: hidden;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n  transition: transform 0.2s ease;\n  width: 94%;\n}\n\n.service-item:active {\n  transform: scale(0.98);\n}\n\n.service-content {\n  padding: 16px;\n}\n\n.service-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.service-tag-container {\n  position: relative;\n}\n\n.service-tag {\n  background-color: rgba(0, 122, 255, 0.1);\n  color: #007aff;\n  font-size: 12px;\n  padding: 2px 8px;\n  border-radius: 4px;\n  font-weight: 500;\n}\n\n.service-time {\n  font-size: 12px;\n  color: #8e8e93;\n}\n\n.service-title {\n  font-size: 16px;\n  line-height: 1.4;\n  color: #000;\n  margin-bottom: 12px;\n  font-weight: 500;\n  /* 标题最多显示两行，超出部分显示省略号 */\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.service-images {\n  display: flex;\n  margin-bottom: 12px;\n  gap: 8px;\n  justify-content: center;\n}\n\n.service-image {\n  width: 102px;\n  height: 102px;\n  border-radius: 6px;\n  object-fit: cover;\n}\n\n/* iOS风格信息卡片 */\n.service-info-card {\n  background-color: #f9f9f9;\n  border-radius: 8px;\n  padding: 10px 12px;\n  margin-bottom: 10px;\n}\n\n.info-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.info-item {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  gap: 6px;\n}\n\n.info-label {\n  font-size: 12px;\n  color: #8e8e93;\n}\n\n.info-value {\n  font-size: 14px;\n  color: #1c1c1e;\n  font-weight: 500;\n}\n\n.info-value.highlight {\n  color: #ff3b30;\n  font-weight: 600;\n}\n\n/* iOS风格底部栏 */\n.service-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-top: 10px;\n  border-top: 1px solid rgba(0, 0, 0, 0.05);\n  margin-top: 6px;\n}\n\n.service-meta {\n  display: flex;\n  align-items: center;\n}\n\n.views-icon {\n  width: 16px;\n  height: 16px;\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%238e8e93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z'%3E%3C/path%3E%3Ccircle cx='12' cy='12' r='3'%3E%3C/circle%3E%3C/svg%3E\");\n  background-size: contain;\n  background-repeat: no-repeat;\n  margin-right: 4px;\n}\n\n.service-views {\n  font-size: 13px;\n  color: #8e8e93;\n}\n\n.service-actions {\n  display: flex;\n}\n\n.action-btn {\n  display: flex;\n  align-items: center;\n  background: #007aff;\n  padding: 6px 12px;\n  border-radius: 16px;\n  transition: background-color 0.2s ease;\n}\n\n.action-btn:active {\n  background: #0062cc;\n}\n\n.action-text {\n  font-size: 13px;\n  color: #fff;\n  font-weight: 500;\n}\n\n.contact-icon {\n  width: 16px;\n  height: 16px;\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23fff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z'%3E%3C/path%3E%3C/svg%3E\");\n  background-size: contain;\n  background-repeat: no-repeat;\n  margin-right: 4px;\n}\n\n/* iOS风格空状态 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 60px 20px;\n  margin-top: 20px;\n  width: 100%;\n}\n\n.empty-image {\n  width: 120px;\n  height: 120px;\n  margin-bottom: 20px;\n  opacity: 0.8;\n}\n\n.empty-text {\n  font-size: 16px;\n  color: #8e8e93;\n  margin-bottom: 8px;\n}\n\n.empty-tips {\n  font-size: 14px;\n  color: #aeaeb2;\n}\n\n/* iOS风格发布按钮 */\n.publish-btn {\n  position: fixed;\n  right: 16px;\n  bottom: 50px;\n  background: #007aff;\n  width: 120px;\n  height: 42px;\n  border-radius: 21px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);\n  z-index: 100;\n}\n\n.publish-btn:active {\n  background: #0062cc;\n}\n\n.publish-icon {\n  font-size: 20px;\n  color: #fff;\n  margin-right: 4px;\n  font-weight: 400;\n  line-height: 20px;\n}\n\n.publish-text {\n  color: #fff;\n  font-size: 15px;\n  font-weight: 500;\n}\n\n/* iOS风格加载更多 */\n.loading-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px 0;\n  width: 100%;\n}\n\n.loading-indicator {\n  width: 18px;\n  height: 18px;\n  border: 2px solid rgba(0, 122, 255, 0.2);\n  border-top-color: #007aff;\n  border-radius: 50%;\n  animation: spin 0.8s linear infinite;\n  margin-right: 8px;\n}\n\n@keyframes spin {\n  to { transform: rotate(360deg); }\n}\n\n.loading-text {\n  font-size: 14px;\n  color: #8e8e93;\n}\n\n/* iOS风格整体样式适配 */\n.ios-style {\n  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;\n  letter-spacing: -0.2px;\n}\n\n.ios-style .service-item {\n  border: none;\n}\n\n@media (hover: hover) {\n  .tab-item:hover {\n    background-color: rgba(0, 122, 255, 0.05);\n  }\n  \n  .service-item:hover {\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\n  }\n  \n  .action-btn:hover {\n    background: #0062cc;\n  }\n}\n\n.area-dropdown {\n  left: 0;\n  width: 50%;\n}\n\n.sort-dropdown {\n  right: 0;\n  width: 50%;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/service/pages/home-service-list.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "nextTick", "onMounted", "page"], "mappings": ";;;;;;AAsKA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AACXA,kBAAG,IAAC,CAAC;AACTA,kBAAG,IAAC,QAAQ;AAC3B,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAC9B,UAAM,UAAUA,cAAAA,IAAI,IAAI;AACxB,UAAM,OAAOA,cAAAA,IAAI,CAAC;AAClB,UAAM,QAAQA,cAAAA,IAAI,EAAE;AACpB,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AACJA,kBAAAA,IAAI;AAAA,MACxB,EAAE,MAAM,MAAM,MAAM,MAAO;AAAA,MAC3B,EAAE,MAAM,QAAQ,MAAM,gBAAiB;AAAA,MACvC,EAAE,MAAM,QAAQ,MAAM,SAAU;AAAA,MAChC,EAAE,MAAM,QAAQ,MAAM,eAAgB;AAAA,MACtC,EAAE,MAAM,QAAQ,MAAM,YAAa;AAAA,MACnC,EAAE,MAAM,QAAQ,MAAM,SAAU;AAAA,MAChC,EAAE,MAAM,QAAQ,MAAM,SAAU;AAAA,MAChC,EAAE,MAAM,QAAQ,MAAM,QAAS;AAAA,MAC/B,EAAE,MAAM,QAAQ,MAAM,cAAe;AAAA,MACrC,EAAE,MAAM,QAAQ,MAAM,WAAY;AAAA,MAClC,EAAE,MAAM,QAAQ,MAAM,QAAS;AAAA,IACjC,CAAC;AAED,UAAM,oBAAoBA,cAAAA,IAAI,CAAA,CAAE;AAChC,UAAM,kBAAkBA,cAAAA,IAAI,IAAI;AAChC,UAAM,aAAaA,cAAG,IAAC,CAAC,MAAM,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,CAAC;AAC7G,UAAM,WAAWA,cAAAA,IAAI,MAAM;AACNA,kBAAG,IAAC,KAAK;AAC9B,UAAM,UAAUA,cAAAA,IAAI,KAAK;AACzB,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAG3B,UAAM,iBAAiBA,cAAAA,IAAI,KAAK;AAChC,UAAM,iBAAiBA,cAAAA,IAAI,KAAK;AAChC,UAAM,eAAeA,cAAAA,IAAI,MAAM;AAC/B,UAAM,eAAeA,cAAAA,IAAI,MAAM;AAC/B,UAAM,kBAAkBA,cAAAA,IAAI,GAAG;AAC/B,UAAM,kBAAkBA,cAAAA,IAAI,GAAG;AAG/B,UAAM,WAAWA,cAAG,IAAC,CAAC,QAAQ,MAAM,OAAO,QAAQ,OAAO,OAAO,OAAO,KAAK,CAAC;AAC9E,UAAM,WAAWA,cAAAA,IAAI,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,CAAC;AAGrE,UAAM,eAAeA,cAAAA,IAAI,IAAI;AAGDA,kBAAG,IAAC,MAAM;AAGtC,UAAM,cAAcA,cAAAA,IAAI,OAAO;AAG/B,UAAM,iBAAiB,MAAM;AAC3B,wBAAkB,QAAQ;AAAA,QACxB;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,iBAAiB;AAAA,UACjB,SAAS;AAAA,UACT,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ,CAAC,uCAAuC,qCAAqC;AAAA,QACtF;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,iBAAiB;AAAA,UACjB,SAAS;AAAA,UACT,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ,CAAC,mCAAmC;AAAA,QAC7C;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,iBAAiB;AAAA,UACjB,SAAS;AAAA,UACT,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,iBAAiB;AAAA,UACjB,SAAS;AAAA,UACT,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ,CAAC,mCAAmC,iCAAiC;AAAA,QAC9E;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,iBAAiB;AAAA,UACjB,SAAS;AAAA,UACT,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,iBAAiB;AAAA,UACjB,SAAS;AAAA,UACT,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ,CAAC,mCAAmC;AAAA,QAC7C;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,iBAAiB;AAAA,UACjB,SAAS;AAAA,UACT,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,iBAAiB;AAAA,UACjB,SAAS;AAAA,UACT,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ,CAAC,kCAAkC,gCAAgC;AAAA,QAC5E;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,iBAAiB;AAAA,UACjB,SAAS;AAAA,UACT,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,iBAAiB;AAAA,UACjB,SAAS;AAAA,UACT,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ,CAAC,kCAAkC;AAAA,QAC5C;AAAA,MACL;AAAA,IACA;AAGA,UAAM,eAAe,MAAM;AACzBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAyBA,UAAM,kBAAkB,YAAY;AAClC,UAAI,QAAQ,SAAS,CAAC,QAAQ;AAAO;AAErC,cAAQ,QAAQ;AAEhB,UAAI;AAEF,cAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;AAGrD,cAAM,UAAU;AAGhB,YAAI,eAAe;AACnB,YAAI,gBAAgB,UAAU,MAAM;AAClC,yBAAe,QAAQ,OAAO,UAAQ,KAAK,aAAa,gBAAgB,KAAK;AAAA,QAC9E;AAGD,YAAI,aAAa,UAAU,QAAQ;AACjC,yBAAe,aAAa,OAAO,UAAQ,KAAK,SAAS,aAAa,SAC7C,KAAK,QAAQ,KAAK,KAAK,SAAS,aAAa,KAAK,CAAE;AAAA,QAC9E;AAGD,qBAAa,YAAY;AAGzB,YAAI,KAAK,UAAU,GAAG;AACpB,sBAAY,QAAQ;AAAA,QAC1B,OAAW;AACL,sBAAY,QAAQ,CAAC,GAAG,YAAY,OAAO,GAAG,YAAY;AAAA,QAC3D;AAGD,gBAAQ,QAAQ,aAAa,UAAU,MAAM;AAC7C,aAAK;AAAA,MAEN,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,0DAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,UAAY;AACR,gBAAQ,QAAQ;AAChB,qBAAa,QAAQ;AAAA,MACtB;AAAA,IACH;AAGA,UAAM,eAAe,CAAC,SAAS;AAC7B,UAAI,CAAC,QAAQ,KAAK,WAAW;AAAG;AAEhC,UAAI,SAAS,UAAU,WAAW;AAChC,aAAK,KAAK,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,KAAK;AAAA,MACzC,WAAa,SAAS,UAAU,aAAa;AAEzC,aAAK,KAAK,CAAC,GAAG,MAAM;AAClB,gBAAM,SAAS,WAAW,EAAE,QAAQ,EAAE,MAAM,QAAQ,WAAW,EAAE,IAAI,OAAO;AAC5E,gBAAM,SAAS,WAAW,EAAE,QAAQ,EAAE,MAAM,QAAQ,WAAW,EAAE,IAAI,OAAO;AAC5E,iBAAO,SAAS;AAAA,QACtB,CAAK;AAAA,MACL,WAAa,SAAS,UAAU,cAAc;AAE1C,aAAK,KAAK,CAAC,GAAG,MAAM;AAClB,gBAAM,SAAS,WAAW,EAAE,QAAQ,EAAE,MAAM,QAAQ,WAAW,EAAE,IAAI,GAAG;AACxE,gBAAM,SAAS,WAAW,EAAE,QAAQ,EAAE,MAAM,QAAQ,WAAW,EAAE,IAAI,GAAG;AACxE,iBAAO,SAAS;AAAA,QACtB,CAAK;AAAA,MACF,WAAU,SAAS,UAAU,cAAc,aAAa,OAAO;AAE9D,aAAK,KAAK,CAAC,GAAG,MAAM;AAElB,gBAAM,YAAY,kBAAkB,EAAE,YAAY,oBAAoB,EAAE,IAAI,CAAC;AAC7E,gBAAM,YAAY,kBAAkB,EAAE,YAAY,oBAAoB,EAAE,IAAI,CAAC;AAC7E,iBAAO,YAAY;AAAA,QACzB,CAAK;AAAA,MACF;AAED,aAAO;AAAA,IACT;AAEA,UAAM,qBAAqB,MAAM;AAE/B,YAAM,eAAe;AAAA,QACnB;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA,YACN;AAAA,UACD;AAAA,UACD,UAAU,EAAE,UAAU,QAAQ,WAAW,QAAS;AAAA,QACnD;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA,YACN;AAAA,UACD;AAAA,UACD,UAAU,EAAE,UAAU,QAAQ,WAAW,QAAS;AAAA,QACnD;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA,YACN;AAAA,UACD;AAAA,UACD,UAAU,EAAE,UAAU,QAAQ,WAAW,QAAS;AAAA,QACnD;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA,YACN;AAAA,UACD;AAAA,UACD,UAAU,EAAE,UAAU,QAAQ,WAAW,QAAS;AAAA,QACnD;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA,YACN;AAAA,UACD;AAAA,UACD,UAAU,EAAE,UAAU,QAAQ,WAAW,QAAS;AAAA,QACnD;AAAA,MACL;AAGE,UAAI,eAAe;AACnB,UAAI,gBAAgB,UAAU,MAAM;AAClC,uBAAe,aAAa,OAAO,UAAQ,KAAK,aAAa,gBAAgB,KAAK;AAAA,MACnF;AAGD,YAAM,WAAW,KAAK,IAAI,GAAG,MAAM,KAAK;AACxC,aAAO,aAAa,MAAM,GAAG,QAAQ;AAAA,IACvC;AAWA,UAAM,WAAW,MAAM;AACrB;IACF;AAGA,UAAM,mBAAmB,CAAC,OAAO;AAE/B,YAAM,UAAU,YAAY,MAAM,KAAK,UAAQ,KAAK,OAAO,EAAE;AAC7D,UAAI,CAAC;AAAS;AAGd,YAAM,kBAAkB,QAAQ,YAAY,gBAAgB,SAAS;AACrEA,iGAAY,kBAAkB,IAAI,OAAO,eAAe;AAGxD,UAAI,cAAc,QAAQ,eAAe;AACzC,UAAI,CAAC,aAAa;AAChB,gBAAO,iBAAe;AAAA,UACpB,KAAK;AACH,0BAAc;AACd;AAAA,UACF,KAAK;AACH,0BAAc;AACd;AAAA,UACF,KAAK;AACH,0BAAc;AACd;AAAA,UACF,KAAK;AACH,0BAAc;AACd;AAAA,UACF,KAAK;AACH,0BAAc;AACd;AAAA,UACF,KAAK;AACH,0BAAc;AACd;AAAA,UACF,KAAK;AACH,0BAAc;AACd;AAAA,UACF,KAAK;AACH,0BAAc;AACd;AAAA,UACF,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AACE,0BAAc;AACd;AAAA,QACH;AAAA,MACF;AAGDA,oBAAAA,MAAI,gBAAgBA,oBAAI;AAGxBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAGD,YAAM,MAAM,yCAAyC,EAAE,aAAa,mBAAmB,eAAe,CAAC,SAAS,WAAW;AAC3HA,oBAAY,MAAA,MAAA,OAAA,0DAAA,aAAa,GAAG;AAG5BA,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,QACA,SAAS,MAAM;AACbA,wBAAAA,MAAA,MAAA,OAAA,0DAAY,MAAM;AAClBA,wBAAG,MAAC,YAAW;AAAA,QAChB;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAA,MAAA,MAAA,SAAA,0DAAc,SAAS,GAAG;AAC1BA,wBAAG,MAAC,YAAW;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,MAAM;AAC9BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAEA,UAAM,iBAAiB,CAAC,aAAa;AACnC,UAAI,gBAAgB,UAAU,UAAU;AACtC,wBAAgB,QAAQ;AACxB,aAAK,QAAQ;AACb,oBAAY,QAAQ;AACpB,gBAAQ,QAAQ;AAChB;AAGA,cAAM,QAAQ,WAAW,MAAM,UAAU,UAAQ,SAAS,QAAQ;AAClE,YAAI,SAAS,GAAG;AACd,sBAAY,QAAQ,SAAS;AAAA,QAC9B;AAAA,MACF;AAAA,IACH;AA8BA,UAAM,iBAAiB,CAAC,OAAO;AAE7B,YAAM,gBAAe;AAGrB,YAAM,UAAU,YAAY,MAAM,KAAK,UAAQ,KAAK,OAAO,EAAE;AAC7D,UAAI,CAAC;AAAS;AAGdA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEfA,0BAAAA,MAAI,cAAc;AAAA,cAChB,aAAa;AAAA,YACvB,CAAS;AAAA,UACT,WAAiB,IAAI,QAAQ;AAErBA,0BAAAA,MAAI,iBAAiB;AAAA,cACnB,MAAM;AAAA,cACN,SAAS,MAAM;AACbA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACpB,CAAa;AAAA,cACF;AAAA,YACX,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,SAAS;AAE3BA,oBAAG,MAAC,aAAY;AAEhB,mBAAa,QAAQ;AACrB,qBAAe,QAAQ;AACvB;IACF;AAGA,UAAM,aAAa,CAAC,SAAS;AAE3BA,oBAAG,MAAC,aAAY;AAEhB,mBAAa,QAAQ;AACrB,qBAAe,QAAQ;AACvB;AAGA,UAAI,SAAS,QAAQ;AACnB,iBAAS,QAAQ;AAAA,MACrB,WAAa,SAAS,QAAQ;AAC1B,iBAAS,QAAQ;AAAA,MACrB,WAAa,SAAS,QAAQ;AAC1B,iBAAS,QAAQ;AAAA,MACrB,WAAa,SAAS,QAAQ;AAC1B,iBAAS,QAAQ;AAAA,MACrB,WAAa,SAAS,QAAQ;AAC1B,iBAAS,QAAQ;AACjB;MACJ,OAAS;AACL,iBAAS,QAAQ;AAAA,MAClB;AAAA,IACH;AAGA,UAAM,iBAAiB,MAAM;AAE3BA,oBAAAA,MAAI,YAAY;AAAA,QACd,MAAM;AAAA,QACN,SAAS,CAAC,QAAQ;AAChB,gBAAM,WAAW;AAAA,YACf,UAAU,IAAI;AAAA,YACd,WAAW,IAAI;AAAA,UACvB;AAGM,uBAAa,QAAQ;AAGrB;QACD;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAc,MAAA,MAAA,SAAA,0DAAA,UAAU,GAAG;AAC3BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAGD,uBAAa,QAAQ;AACrB,mBAAS,QAAQ;AAAA,QAClB;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,kBAAkB,MAAM;AAC5B,qBAAe,QAAQ;AACvB,qBAAe,QAAQ;AAAA,IACzB;AAGA,UAAM,qBAAqB,MAAM;AAC/B,WAAK,QAAQ;AACb,kBAAY,QAAQ;AACpB,cAAQ,QAAQ;AAChB;IACF;AAGA,UAAM,sBAAsB,CAAC,SAAS;AAEpC,YAAM,cAAc;AAAA,QAClB,MAAM,EAAE,UAAU,QAAQ,WAAW,QAAS;AAAA,QAC9C,OAAO,EAAE,UAAU,QAAQ,WAAW,QAAS;AAAA,QAC/C,QAAQ,EAAE,UAAU,QAAQ,WAAW,QAAS;AAAA,QAChD,OAAO,EAAE,UAAU,QAAQ,WAAW,QAAS;AAAA,QAC/C,OAAO,EAAE,UAAU,QAAQ,WAAW,QAAS;AAAA,QAC/C,OAAO,EAAE,UAAU,QAAQ,WAAW,QAAS;AAAA,QAC/C,OAAO,EAAE,UAAU,QAAQ,WAAW,QAAS;AAAA,QAC/C,MAAM,EAAE,UAAU,QAAQ,WAAW,QAAS;AAAA,QAC9C,OAAO,EAAE,UAAU,QAAQ,WAAW,QAAS;AAAA,MACnD;AAEE,aAAO,YAAY,IAAI,KAAK,EAAE,UAAU,QAAQ,WAAW;IAC7D;AAGA,UAAM,oBAAoB,CAAC,oBAAoB;AAC7C,UAAI,CAAC,aAAa,SAAS,CAAC,iBAAiB;AAC3C,eAAO,OAAO;AAAA,MACf;AAGD,YAAM,IAAI;AAGV,YAAM,OAAO,aAAa,MAAM,WAAW,KAAK,KAAK;AACrD,YAAM,OAAO,gBAAgB,WAAW,KAAK,KAAK;AAClD,YAAM,QAAQ,gBAAgB,WAAW,aAAa,MAAM,YAAY,KAAK,KAAK;AAClF,YAAM,QAAQ,gBAAgB,YAAY,aAAa,MAAM,aAAa,KAAK,KAAK;AAGpF,YAAM,IAAI,KAAK,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,OAAO,CAAC,IACxC,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAC9B,KAAK,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,OAAO,CAAC;AAC9C,YAAM,IAAI,IAAI,KAAK,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC;AACvD,YAAM,WAAW,IAAI;AAErB,aAAO;AAAA,IACT;AAGA,UAAM,mBAAmB,MAAM;AAC7B,UAAI;AAEF,cAAM,QAAQ,WAAW,MAAM,UAAU,UAAQ,SAAS,gBAAgB,KAAK;AAC/E,YAAI,SAAS,GAAG;AAEd,sBAAY,QAAQ,SAAS;AAC7BA,wBAAY,MAAA,MAAA,OAAA,0DAAA,UAAU,YAAY,KAAK;AAAA,QACxC;AAAA,MACF,SAAQ,GAAG;AACVA,sBAAc,MAAA,MAAA,SAAA,0DAAA,WAAW,CAAC;AAAA,MAC3B;AAAA,IACH;AAoBA,UAAM,mBAAmB,MAAM;AAC7B,qBAAe,QAAQ,CAAC,eAAe;AACvC,qBAAe,QAAQ;AAEvB,UAAI,eAAe,OAAO;AAExBC,sBAAAA,WAAS,MAAM;AACb,gBAAM,QAAQD,oBAAI;AAClB,gBAAM,OAAO,2BAA2B,EAAE,mBAAmB,UAAQ;AACnE,gBAAI,MAAM;AACR,8BAAgB,QAAQ,KAAK;AAAA,YAC9B;AAAA,UACT,CAAO,EAAE,KAAI;AAAA,QACb,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,mBAAmB,MAAM;AAC7B,qBAAe,QAAQ,CAAC,eAAe;AACvC,qBAAe,QAAQ;AAEvB,UAAI,eAAe,OAAO;AAExBC,sBAAAA,WAAS,MAAM;AACb,gBAAM,QAAQD,oBAAI;AAClB,gBAAM,OAAO,2BAA2B,EAAE,mBAAmB,UAAQ;AACnE,gBAAI,MAAM;AACR,8BAAgB,QAAQ,KAAK;AAAA,YAC9B;AAAA,UACT,CAAO,EAAE,KAAI;AAAA,QACb,CAAK;AAAA,MACF;AAAA,IACH;AAGAE,kBAAAA,UAAU,MAAM;;AAEd,YAAM,UAAUF,oBAAI;AACpB,sBAAgB,QAAQ,QAAQ;AAGhC,YAAM,QAAQ;AACd,YAAMG,QAAO,MAAM,MAAM,SAAS,CAAC;AACnC,YAAM,YAAU,KAAAA,MAAK,UAAL,mBAAY,YAAW,CAAA;AAGvC,iBAAW,MAAM;AACf;MACD,GAAE,GAAG;AAGN,UAAI,QAAQ,WAAW,QAAQ,SAAS;AAEtC,wBAAgB,QAAQ,mBAAmB,QAAQ,OAAO;AAE1D,QAAAA,MAAK,QAAQ;AACb,oBAAY,QAAQ;AACpB,gBAAQ,QAAQ;AAGhB,cAAM,QAAQ,WAAW,MAAM,UAAU,UAAQ,SAAS,gBAAgB,KAAK;AAC/E,YAAI,SAAS,GAAG;AACd,sBAAY,QAAQ,SAAS;AAAA,QAC9B;AAAA,MACL,WAAa,QAAQ,UAAU;AAE3B,cAAM,eAAe,mBAAmB,QAAQ,QAAQ;AAExD,YAAI,WAAW,MAAM,SAAS,YAAY,GAAG;AAC3C,0BAAgB,QAAQ;AACxB,UAAAA,MAAK,QAAQ;AACb,sBAAY,QAAQ;AACpB,kBAAQ,QAAQ;AAGhB,gBAAM,QAAQ,WAAW,MAAM,UAAU,UAAQ,SAAS,gBAAgB,KAAK;AAC/E,cAAI,SAAS,GAAG;AACd,wBAAY,QAAQ,SAAS;AAAA,UAC9B;AAAA,QACF;AAAA,MACF;AAGD,iBAAW,MAAM;AACf;AAGAF,sBAAAA,WAAS,MAAM;AACb;QACN,CAAK;AAAA,MACF,GAAE,GAAG;AAAA,IACR,CAAC;AAGDD,kBAAG,MAAC,kBAAkB,MAAM;AAC1B,WAAK,QAAQ;AACb,kBAAY,QAAQ;AACpB,cAAQ,QAAQ;AAChB;AACA,iBAAW,MAAM;AACfA,sBAAG,MAAC,oBAAmB;AAAA,MACxB,GAAE,GAAI;AAAA,IACT,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACn7BD,GAAG,WAAW,eAAe;"}