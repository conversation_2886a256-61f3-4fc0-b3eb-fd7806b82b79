#!/bin/bash

echo "🔧 设置脚本执行权限"

# 设置根目录脚本权限
chmod +x start-all.sh
chmod +x stop-all.sh
chmod +x check-status.sh

# 设置后端脚本权限
chmod +x backend/start-dev.sh

# 设置前端脚本权限
chmod +x frontend/start-dev.sh

echo "✅ 脚本权限设置完成"
echo ""
echo "📋 可用脚本："
echo "   ./start-all.sh     - 一键启动前后端"
echo "   ./stop-all.sh      - 一键停止所有服务"
echo "   ./check-status.sh  - 检查系统状态"
echo "   backend/start-dev.sh   - 仅启动后端"
echo "   frontend/start-dev.sh  - 仅启动前端"
