<template>
  <view class="records-container">
    <!-- 筛选标签栏 -->
    <view class="filter-tabs" :style="{
      display: 'flex',
      backgroundColor: '#FFFFFF',
      borderRadius: '35px',
      padding: '10rpx',
      marginBottom: '30rpx',
      boxShadow: '0 4px 10px rgba(0,0,0,0.05)'
    }">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index"
        class="tab-item"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
        :style="{
          flex: '1',
          textAlign: 'center',
          padding: '15rpx 0',
          borderRadius: '30rpx',
          fontSize: '28rpx',
          fontWeight: currentTab === index ? '600' : '400',
          color: currentTab === index ? '#FFFFFF' : '#666666',
          background: currentTab === index ? 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)' : 'transparent',
          transition: 'all 0.3s ease'
        }"
      >
        {{ tab.name }}
      </view>
    </view>
    
    <!-- 日期筛选 -->
    <view class="date-filter" :style="{
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: '20rpx 30rpx',
      backgroundColor: '#FFFFFF',
      borderRadius: '35px',
      marginBottom: '30rpx',
      boxShadow: '0 4px 10px rgba(0,0,0,0.05)'
    }">
      <view class="date-selector" @click="showDatePicker('start')" :style="{
        display: 'flex',
        alignItems: 'center'
      }">
        <text :style="{
          fontSize: '26rpx',
          color: '#666666',
          marginRight: '10rpx'
        }">开始日期:</text>
        <text :style="{
          fontSize: '26rpx',
          color: '#333333',
          fontWeight: '500'
        }">{{ startDate }}</text>
      </view>
      
      <text :style="{
        fontSize: '26rpx',
        color: '#999999'
      }">至</text>
      
      <view class="date-selector" @click="showDatePicker('end')" :style="{
        display: 'flex',
        alignItems: 'center'
      }">
        <text :style="{
          fontSize: '26rpx',
          color: '#666666',
          marginRight: '10rpx'
        }">结束日期:</text>
        <text :style="{
          fontSize: '26rpx',
          color: '#333333',
          fontWeight: '500'
        }">{{ endDate }}</text>
      </view>
      
      <view class="filter-btn" @click="applyFilter" :style="{
        padding: '10rpx 20rpx',
        borderRadius: '30rpx',
        background: 'rgba(172,57,255,0.1)',
        color: '#AC39FF',
        fontSize: '26rpx',
        fontWeight: '500'
      }">
        筛选
      </view>
    </view>
    
    <!-- 统计卡片 -->
    <view class="statistics-card" :style="{
      borderRadius: '35px',
      boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
      background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)',
      padding: '30rpx',
      marginBottom: '30rpx',
      position: 'relative',
      overflow: 'hidden'
    }">
      <!-- 背景装饰 -->
      <view class="bg-decoration" :style="{
        position: 'absolute',
        top: '-50rpx',
        right: '-50rpx',
        width: '300rpx',
        height: '300rpx',
        borderRadius: '50%',
        background: 'rgba(255,255,255,0.1)',
        zIndex: '1'
      }"></view>
      <view class="bg-decoration" :style="{
        position: 'absolute',
        bottom: '-80rpx',
        left: '-80rpx',
        width: '250rpx',
        height: '250rpx',
        borderRadius: '50%',
        background: 'rgba(255,255,255,0.08)',
        zIndex: '1'
      }"></view>
      
      <!-- 统计信息 -->
      <view class="statistics-content" :style="{
        position: 'relative',
        zIndex: '2'
      }">
        <view class="period-info" :style="{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '20rpx'
        }">
          <text :style="{
            fontSize: '26rpx',
            color: 'rgba(255,255,255,0.9)'
          }">{{ filterPeriodText }}</text>
          
          <view class="quick-filter" :style="{
            display: 'flex'
          }">
            <view 
              v-for="(period, index) in quickPeriods" 
              :key="index"
              class="period-item"
              :class="{ active: currentPeriod === index }"
              @click="selectQuickPeriod(index)"
              :style="{
                fontSize: '24rpx',
                color: currentPeriod === index ? '#AC39FF' : 'rgba(255,255,255,0.9)',
                padding: '6rpx 16rpx',
                borderRadius: '20rpx',
                marginLeft: '10rpx',
                background: currentPeriod === index ? '#FFFFFF' : 'rgba(255,255,255,0.1)'
              }"
            >
              {{ period.name }}
            </view>
          </view>
        </view>
        
        <view class="total-amount" :style="{
          marginBottom: '20rpx'
        }">
          <text :style="{
            fontSize: '26rpx',
            color: 'rgba(255,255,255,0.9)',
            marginBottom: '10rpx',
            display: 'block'
          }">{{ tabs[currentTab].label }}(元)</text>
          <text :style="{
            fontSize: '50rpx',
            fontWeight: 'bold',
            color: '#FFFFFF',
            display: 'block',
            textShadow: '0 2rpx 4rpx rgba(0,0,0,0.1)'
          }">{{ totalAmount }}</text>
        </view>
        
        <view class="statistics-details" :style="{
          display: 'flex',
          justifyContent: 'space-around',
          background: 'rgba(255,255,255,0.1)',
          borderRadius: '20rpx',
          padding: '20rpx 0'
        }">
          <view class="detail-item">
            <text class="detail-value" :style="{
              fontSize: '32rpx',
              fontWeight: 'bold',
              color: '#FFFFFF',
              display: 'block',
              textAlign: 'center'
            }">{{ recordCount }}</text>
            <text class="detail-label" :style="{
              fontSize: '24rpx',
              color: 'rgba(255,255,255,0.8)',
              display: 'block',
              textAlign: 'center'
            }">记录数</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-value" :style="{
              fontSize: '32rpx',
              fontWeight: 'bold',
              color: '#FFFFFF',
              display: 'block',
              textAlign: 'center'
            }">{{ avgAmount }}</text>
            <text class="detail-label" :style="{
              fontSize: '24rpx',
              color: 'rgba(255,255,255,0.8)',
              display: 'block',
              textAlign: 'center'
            }">平均金额</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-value" :style="{
              fontSize: '32rpx',
              fontWeight: 'bold',
              color: '#FFFFFF',
              display: 'block',
              textAlign: 'center'
            }">{{ maxAmount }}</text>
            <text class="detail-label" :style="{
              fontSize: '24rpx',
              color: 'rgba(255,255,255,0.8)',
              display: 'block',
              textAlign: 'center'
            }">最高金额</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 记录列表 -->
    <view class="records-list" :style="{
      borderRadius: '35px',
      boxShadow: '0 8px 20px rgba(0,0,0,0.08)',
      background: '#FFFFFF',
      padding: '30rpx',
      marginBottom: '30rpx'
    }">
      <view class="list-header" :style="{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '20rpx'
      }">
        <text :style="{
          fontSize: '32rpx',
          fontWeight: '600',
          color: '#333333'
        }">收益明细</text>
        
        <view class="sort-filter" @click="toggleSortMenu" :style="{
          display: 'flex',
          alignItems: 'center'
        }">
          <text :style="{
            fontSize: '26rpx',
            color: '#666666',
            marginRight: '5rpx'
          }">{{ currentSortOption.name }}</text>
          <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
            <path d="M6 9l6 6 6-6" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
      </view>
      
      <!-- 排序菜单 -->
      <view v-if="showSortMenu" class="sort-menu" :style="{
        position: 'absolute',
        right: '30rpx',
        top: '260rpx',
        background: '#FFFFFF',
        borderRadius: '20rpx',
        boxShadow: '0 5px 15px rgba(0,0,0,0.1)',
        zIndex: '100',
        width: '200rpx',
        overflow: 'hidden'
      }">
        <view 
          v-for="(option, index) in sortOptions" 
          :key="index"
          class="sort-option"
          @click="selectSortOption(index)"
          :style="{
            padding: '20rpx',
            fontSize: '26rpx',
            color: currentSortIndex === index ? '#AC39FF' : '#333333',
            fontWeight: currentSortIndex === index ? '500' : '400',
            borderBottom: index < sortOptions.length - 1 ? '1rpx solid #F2F2F7' : 'none',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }"
        >
          <text>{{ option.name }}</text>
          <svg v-if="currentSortIndex === index" class="icon" viewBox="0 0 24 24" width="16" height="16">
            <path d="M5 12l5 5L20 7" stroke="#AC39FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
      </view>
      
      <!-- 记录项目 -->
      <view class="records-items">
        <view 
          v-for="(record, index) in filteredRecords" 
          :key="index"
          class="record-item"
          :style="{
            padding: '20rpx 0',
            borderBottom: index < filteredRecords.length - 1 ? '1rpx solid #F2F2F7' : 'none',
            display: 'flex',
            alignItems: 'center'
          }"
        >
          <view class="record-icon" :style="{
            width: '80rpx',
            height: '80rpx',
            borderRadius: '50%',
            background: getRecordTypeBg(record.type),
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: '20rpx'
          }">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <path :d="getRecordTypeIcon(record.type)" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
          
          <view class="record-info" :style="{ flex: '1' }">
            <view class="record-top" :style="{
              display: 'flex',
              justifyContent: 'space-between',
              marginBottom: '5rpx'
            }">
              <text class="record-title" :style="{
                fontSize: '28rpx',
                fontWeight: '500',
                color: '#333333'
              }">{{ record.title }}</text>
              
              <text class="record-amount" :style="{
                fontSize: '28rpx',
                fontWeight: '600',
                color: record.type === 'withdraw' ? '#FF3B30' : '#34C759'
              }">{{ record.type === 'withdraw' ? '-' : '+' }}{{ record.amount }}</text>
            </view>
            
            <view class="record-bottom" :style="{
              display: 'flex',
              justifyContent: 'space-between'
            }">
              <text class="record-source" :style="{
                fontSize: '24rpx',
                color: '#999999'
              }">{{ record.source }}</text>
              
              <text class="record-time" :style="{
                fontSize: '24rpx',
                color: '#999999'
              }">{{ record.time }}</text>
            </view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view v-if="filteredRecords.length === 0" class="empty-state" :style="{
          padding: '50rpx 0',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center'
        }">
          <image src="/static/images/empty/empty-records.png" mode="aspectFit" :style="{
            width: '200rpx',
            height: '200rpx',
            marginBottom: '20rpx'
          }"></image>
          <text :style="{
            fontSize: '26rpx',
            color: '#999999'
          }">暂无收益记录</text>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view v-if="filteredRecords.length > 0" class="load-more" :style="{
        textAlign: 'center',
        padding: '30rpx 0'
      }">
        <text v-if="loading" :style="{
          fontSize: '26rpx',
          color: '#999999'
        }">加载中...</text>
        <text v-else-if="noMore" :style="{
          fontSize: '26rpx',
          color: '#999999'
        }">没有更多数据了</text>
        <text v-else @click="loadMore" :style="{
          fontSize: '26rpx',
          color: '#AC39FF'
        }">加载更多</text>
      </view>
    </view>
    
    <!-- 底部安全区域 -->
    <view class="safe-area-bottom" :style="{
      height: '100rpx',
      paddingBottom: 'env(safe-area-inset-bottom)'
    }"></view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';

// 标签页数据
const tabs = ref([
  { name: '全部', status: 'all', label: '总收益' },
  { name: '收入', status: 'income', label: '总收入' },
  { name: '提现', status: 'withdraw', label: '总提现' }
]);
const currentTab = ref(0);

// 日期筛选
const startDate = ref('2023-05-01');
const endDate = ref('2023-05-31');
const currentPickerType = ref('');

// 快速时间段筛选
const quickPeriods = ref([
  { name: '今天', days: 0 },
  { name: '7天', days: 7 },
  { name: '30天', days: 30 },
  { name: '全部', days: -1 }
]);
const currentPeriod = ref(2); // 默认选择30天

// 排序选项
const sortOptions = ref([
  { name: '时间降序', field: 'time', order: 'desc' },
  { name: '时间升序', field: 'time', order: 'asc' },
  { name: '金额降序', field: 'amount', order: 'desc' },
  { name: '金额升序', field: 'amount', order: 'asc' }
]);
const currentSortIndex = ref(0);
const currentSortOption = computed(() => sortOptions.value[currentSortIndex.value]);
const showSortMenu = ref(false);

// 加载状态
const loading = ref(false);
const noMore = ref(false);

// 收益记录数据
const records = ref([
  {
    id: 1,
    title: '直接佣金',
    amount: '10.50',
    source: '订单号: 2023051500001',
    time: '2023-05-15 14:30:25',
    type: 'commission'
  },
  {
    id: 2,
    title: '团队佣金',
    amount: '5.20',
    source: '来自: 张小明',
    time: '2023-05-14 09:15:36',
    type: 'team'
  },
  {
    id: 3,
    title: '提现',
    amount: '100.00',
    source: '提现到微信零钱',
    time: '2023-05-10 16:42:18',
    type: 'withdraw'
  },
  {
    id: 4,
    title: '活动奖励',
    amount: '20.00',
    source: '新人推广活动',
    time: '2023-05-08 11:23:45',
    type: 'reward'
  },
  {
    id: 5,
    title: '直接佣金',
    amount: '15.80',
    source: '订单号: 2023050700002',
    time: '2023-05-07 18:05:12',
    type: 'commission'
  }
]);

// 过滤后的记录
const filteredRecords = computed(() => {
  // 根据标签过滤
  let result = records.value;
  
  if (tabs.value[currentTab.value].status !== 'all') {
    if (tabs.value[currentTab.value].status === 'income') {
      result = result.filter(record => record.type !== 'withdraw');
    } else {
      result = result.filter(record => record.type === tabs.value[currentTab.value].status);
    }
  }
  
  // 根据日期过滤
  const startTimestamp = new Date(startDate.value).getTime();
  const endTimestamp = new Date(endDate.value).getTime() + 24 * 60 * 60 * 1000 - 1; // 结束日期的最后一毫秒
  
  result = result.filter(record => {
    const recordTime = new Date(record.time).getTime();
    return recordTime >= startTimestamp && recordTime <= endTimestamp;
  });
  
  // 根据排序选项排序
  const { field, order } = currentSortOption.value;
  
  result.sort((a, b) => {
    let comparison = 0;
    
    if (field === 'time') {
      comparison = new Date(a.time).getTime() - new Date(b.time).getTime();
    } else if (field === 'amount') {
      comparison = parseFloat(a.amount) - parseFloat(b.amount);
    }
    
    return order === 'asc' ? comparison : -comparison;
  });
  
  return result;
});

// 统计数据
const totalAmount = computed(() => {
  let total = 0;
  
  filteredRecords.value.forEach(record => {
    if (tabs.value[currentTab.value].status === 'all') {
      if (record.type === 'withdraw') {
        total -= parseFloat(record.amount);
      } else {
        total += parseFloat(record.amount);
      }
    } else if (tabs.value[currentTab.value].status === 'income') {
      if (record.type !== 'withdraw') {
        total += parseFloat(record.amount);
      }
    } else if (tabs.value[currentTab.value].status === 'withdraw') {
      total += parseFloat(record.amount);
    }
  });
  
  return total.toFixed(2);
});

const recordCount = computed(() => filteredRecords.value.length);

const avgAmount = computed(() => {
  if (filteredRecords.value.length === 0) return '0.00';
  
  let total = 0;
  filteredRecords.value.forEach(record => {
    total += parseFloat(record.amount);
  });
  
  return (total / filteredRecords.value.length).toFixed(2);
});

const maxAmount = computed(() => {
  if (filteredRecords.value.length === 0) return '0.00';
  
  let max = 0;
  filteredRecords.value.forEach(record => {
    const amount = parseFloat(record.amount);
    if (amount > max) {
      max = amount;
    }
  });
  
  return max.toFixed(2);
});

// 筛选周期文本
const filterPeriodText = computed(() => {
  return `${startDate.value} 至 ${endDate.value}`;
});

// 切换标签页
function switchTab(index) {
  currentTab.value = index;
}

// 显示日期选择器
function showDatePicker(type) {
  currentPickerType.value = type;
  
  uni.showToast({
    title: '日期选择功能开发中',
    icon: 'none'
  });
  
  // 实际开发中应该使用日期选择器组件
  // uni.showDatePicker({
  //   success: (res) => {
  //     if (currentPickerType.value === 'start') {
  //       startDate.value = res.date;
  //     } else {
  //       endDate.value = res.date;
  //     }
  //   }
  // });
}

// 应用筛选
function applyFilter() {
  // 这里可以添加筛选逻辑
  uni.showToast({
    title: '筛选已应用',
    icon: 'success'
  });
}

// 选择快速时间段
function selectQuickPeriod(index) {
  currentPeriod.value = index;
  
  const today = new Date();
  const days = quickPeriods.value[index].days;
  
  if (days === -1) {
    // 全部
    startDate.value = '2023-01-01';
    endDate.value = formatDate(today);
  } else if (days === 0) {
    // 今天
    startDate.value = formatDate(today);
    endDate.value = formatDate(today);
  } else {
    // 其他天数
    const pastDate = new Date();
    pastDate.setDate(today.getDate() - days);
    startDate.value = formatDate(pastDate);
    endDate.value = formatDate(today);
  }
}

// 格式化日期
function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

// 切换排序菜单
function toggleSortMenu() {
  showSortMenu.value = !showSortMenu.value;
}

// 选择排序选项
function selectSortOption(index) {
  currentSortIndex.value = index;
  showSortMenu.value = false;
}

// 获取记录类型背景色
function getRecordTypeBg(type) {
  switch (type) {
    case 'commission':
      return 'linear-gradient(135deg, #34C759 0%, #30D158 100%)';
    case 'team':
      return 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)';
    case 'withdraw':
      return 'linear-gradient(135deg, #FF3B30 0%, #FF5E3A 100%)';
    case 'reward':
      return 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)';
    default:
      return 'linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)';
  }
}

// 获取记录类型图标
function getRecordTypeIcon(type) {
  switch (type) {
    case 'commission':
      return 'M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6';
    case 'team':
      return 'M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2M9 11a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM23 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75';
    case 'withdraw':
      return 'M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4M17 8l-5-5-5 5M12 3v12';
    case 'reward':
      return 'M12 15c3 0 6-2 6-6s-3-6-6-6-6 2-6 6 3 6 6 6zM2.5 9h4M17.5 9h4M12 15v8M8 21h8';
    default:
      return 'M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z';
  }
}

// 加载更多
function loadMore() {
  if (loading.value || noMore.value) return;
  
  loading.value = true;
  
  // 模拟加载更多数据
  setTimeout(() => {
    // 这里应该调用API获取更多数据
    // 模拟没有更多数据
    noMore.value = true;
    loading.value = false;
  }, 1500);
}
</script>

<style scoped>
.records-container {
  padding: 30rpx;
  background-color: #F2F2F7;
  min-height: 100vh;
  position: relative;
}

.filter-btn:active, .sort-option:active, .load-more text:active {
  opacity: 0.8;
}
</style> 