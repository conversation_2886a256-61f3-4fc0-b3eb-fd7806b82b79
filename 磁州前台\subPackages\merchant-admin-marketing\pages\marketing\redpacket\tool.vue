<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">红包营销工具</text>
      <view class="navbar-right">
        <view class="settings-icon" @click="navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/redpacket/settings')">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="3"></circle>
            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
          </svg>
        </view>
      </view>
    </view>
    
    <!-- 数据概览 -->
    <view class="overview-section">
      <view class="overview-header">
        <text class="section-title">红包数据概览</text>
        <view class="date-picker" @click="showDatePicker">
          <text class="date-text">{{dateRange}}</text>
          <view class="date-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="16" y1="2" x2="16" y2="6"></line>
              <line x1="8" y1="2" x2="8" y2="6"></line>
              <line x1="3" y1="10" x2="21" y2="10"></line>
            </svg>
          </view>
        </view>
      </view>
      
      <view class="stats-cards">
        <view class="stats-card">
          <text class="stats-value">{{stats.totalSent}}</text>
          <text class="stats-label">发放红包数</text>
        </view>
        <view class="stats-card">
          <text class="stats-value">{{stats.totalAmount}}元</text>
          <text class="stats-label">发放金额</text>
        </view>
        <view class="stats-card">
          <text class="stats-value">{{stats.totalReceived}}</text>
          <text class="stats-label">领取红包数</text>
        </view>
        <view class="stats-card">
          <text class="stats-value">{{stats.conversionRate}}%</text>
          <text class="stats-label">转化率</text>
        </view>
      </view>
      
      <view class="chart-container">
        <view class="chart-header">
          <text class="chart-title">红包领取趋势</text>
          <view class="chart-legend">
            <view class="legend-item">
              <view class="legend-color" style="background-color: #FF4D4F;"></view>
              <text class="legend-text">发放数量</text>
            </view>
            <view class="legend-item">
              <view class="legend-color" style="background-color: #52C41A;"></view>
              <text class="legend-text">领取数量</text>
            </view>
          </view>
        </view>
        <view class="chart-placeholder">
          <!-- 这里放置实际的图表组件 -->
          <view class="chart-mock">
            <view class="chart-column" v-for="(item, index) in chartData" :key="index">
              <view class="column-received" :style="{height: item.received + '%'}"></view>
              <view class="column-sent" :style="{height: item.sent + '%'}"></view>
              <text class="column-label">{{item.date}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 营销工具 -->
    <view class="tools-section">
      <view class="section-header">
        <text class="section-title">红包营销工具</text>
      </view>
      
      <view class="tools-grid">
        <view class="tool-item" @click="navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/redpacket/create')">
          <view class="tool-icon" style="background-color: rgba(255, 77, 79, 0.1);">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#FF4D4F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="12" y1="8" x2="12" y2="16"></line>
              <line x1="8" y1="12" x2="16" y2="12"></line>
            </svg>
          </view>
          <text class="tool-name">创建红包</text>
          <text class="tool-desc">快速创建红包活动</text>
        </view>
        
        <view class="tool-item" @click="navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/redpacket/mass-sending')">
          <view class="tool-icon" style="background-color: rgba(24, 144, 255, 0.1);">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#1890FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
          </view>
          <text class="tool-name">红包群发</text>
          <text class="tool-desc">批量发送红包</text>
        </view>
        
        <view class="tool-item" @click="navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/redpacket/red-rain')">
          <view class="tool-icon" style="background-color: rgba(250, 173, 20, 0.1);">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#FAAD14" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M20 16.2A4.5 4.5 0 0 0 17.5 8h-1.8A7 7 0 1 0 4 14.9"></path>
              <path d="M16 14v6"></path>
              <path d="M8 14v6"></path>
              <path d="M12 16v6"></path>
            </svg>
          </view>
          <text class="tool-name">红包雨</text>
          <text class="tool-desc">限时抢红包活动</text>
        </view>
        
        <view class="tool-item" @click="navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/redpacket/fission')">
          <view class="tool-icon" style="background-color: rgba(114, 46, 209, 0.1);">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#722ED1" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
              <line x1="9" y1="9" x2="9.01" y2="9"></line>
              <line x1="15" y1="9" x2="15.01" y2="9"></line>
            </svg>
          </view>
          <text class="tool-name">裂变红包</text>
          <text class="tool-desc">分享获取更多红包</text>
        </view>
        
        <view class="tool-item" @click="navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/redpacket/template')">
          <view class="tool-icon" style="background-color: rgba(82, 196, 26, 0.1);">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#52C41A" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14 2 14 8 20 8"></polyline>
              <line x1="16" y1="13" x2="8" y2="13"></line>
              <line x1="16" y1="17" x2="8" y2="17"></line>
              <polyline points="10 9 9 9 8 9"></polyline>
            </svg>
          </view>
          <text class="tool-name">红包模板</text>
          <text class="tool-desc">管理红包样式模板</text>
        </view>
        
        <view class="tool-item" @click="navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/redpacket/data-analysis')">
          <view class="tool-icon" style="background-color: rgba(245, 34, 45, 0.1);">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#F5222D" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="20" x2="18" y2="10"></line>
              <line x1="12" y1="20" x2="12" y2="4"></line>
              <line x1="6" y1="20" x2="6" y2="14"></line>
            </svg>
          </view>
          <text class="tool-name">红包数据</text>
          <text class="tool-desc">查看红包数据分析</text>
        </view>
      </view>
    </view>
    
    <!-- 最近活动 -->
    <view class="recent-section">
      <view class="section-header">
        <text class="section-title">最近活动</text>
        <view class="view-all" @click="navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/redpacket/data-analysis')">
          <text class="view-all-text">查看全部</text>
          <view class="arrow-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </view>
        </view>
      </view>
      
      <view class="activity-list">
        <view class="activity-item" v-for="(item, index) in recentActivities" :key="index" @click="navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/redpacket/detail?id=' + item.id)">
          <view class="activity-icon" :style="{backgroundColor: item.iconBg}">
            <view class="icon-inner" :style="{color: item.iconColor}">{{item.icon}}</view>
          </view>
          <view class="activity-content">
            <view class="activity-header">
              <text class="activity-name">{{item.name}}</text>
              <text class="activity-status" :class="'status-' + item.status">{{item.statusText}}</text>
            </view>
            <view class="activity-info">
              <text class="activity-time">{{item.time}}</text>
              <text class="activity-count">发放: {{item.sentCount}} | 领取: {{item.receivedCount}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 营销攻略 -->
    <view class="strategy-section">
      <view class="section-header">
        <text class="section-title">营销攻略</text>
      </view>
      
      <view class="strategy-list">
        <view class="strategy-item" @click="navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/redpacket/guide')">
          <view class="strategy-icon" style="background-color: rgba(255, 77, 79, 0.1);">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#FF4D4F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="16" x2="12" y2="12"></line>
              <line x1="12" y1="8" x2="12.01" y2="8"></line>
            </svg>
          </view>
          <view class="strategy-content">
            <text class="strategy-title">红包营销指南</text>
            <text class="strategy-desc">了解红包营销的基本原理与最佳实践</text>
          </view>
          <view class="strategy-arrow"></view>
        </view>
        
        <view class="strategy-item">
          <view class="strategy-icon" style="background-color: rgba(24, 144, 255, 0.1);">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#1890FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
            </svg>
          </view>
          <view class="strategy-content">
            <text class="strategy-title">节日红包攻略</text>
            <text class="strategy-desc">节日期间红包营销的策略与技巧</text>
          </view>
          <view class="strategy-arrow"></view>
        </view>
        
        <view class="strategy-item">
          <view class="strategy-icon" style="background-color: rgba(82, 196, 26, 0.1);">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#52C41A" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
          </view>
          <view class="strategy-content">
            <text class="strategy-title">用户裂变秘籍</text>
            <text class="strategy-desc">如何利用红包实现用户快速增长</text>
          </view>
          <view class="strategy-arrow"></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      dateRange: '近7天',
      stats: {
        totalSent: 1258,
        totalAmount: 3652.50,
        totalReceived: 986,
        conversionRate: 78.4
      },
      chartData: [
        { date: '周一', sent: 60, received: 45 },
        { date: '周二', sent: 70, received: 55 },
        { date: '周三', sent: 80, received: 65 },
        { date: '周四', sent: 90, received: 75 },
        { date: '周五', sent: 100, received: 85 },
        { date: '周六', sent: 85, received: 70 },
        { date: '周日', sent: 75, received: 60 }
      ],
      recentActivities: [
        {
          id: 1,
          name: '新人专享红包',
          status: 'active',
          statusText: '进行中',
          time: '2023-06-01 ~ 2023-06-30',
          sentCount: 356,
          receivedCount: 289,
          icon: '新',
          iconBg: 'rgba(255, 77, 79, 0.1)',
          iconColor: '#FF4D4F'
        },
        {
          id: 2,
          name: '618购物节红包雨',
          status: 'active',
          statusText: '进行中',
          time: '2023-06-10 ~ 2023-06-18',
          sentCount: 520,
          receivedCount: 412,
          icon: '雨',
          iconBg: 'rgba(24, 144, 255, 0.1)',
          iconColor: '#1890FF'
        },
        {
          id: 3,
          name: '五一劳动节红包',
          status: 'ended',
          statusText: '已结束',
          time: '2023-05-01 ~ 2023-05-05',
          sentCount: 382,
          receivedCount: 285,
          icon: '节',
          iconBg: 'rgba(82, 196, 26, 0.1)',
          iconColor: '#52C41A'
        }
      ]
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showDatePicker() {
      uni.showActionSheet({
        itemList: ['今日', '近7天', '近30天', '自定义'],
        success: (res) => {
          const options = ['今日', '近7天', '近30天', '自定义'];
          this.dateRange = options[res.tapIndex];
          
          if (res.tapIndex === 3) {
            // 打开日期选择器
            uni.showToast({
              title: '打开日期选择器',
              icon: 'none'
            });
          } else {
            // 加载对应时间段的数据
            this.loadData(options[res.tapIndex]);
          }
        }
      });
    },
    loadData(timeRange) {
      // 模拟加载数据
      uni.showLoading({
        title: '加载中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: `已加载${timeRange}数据`,
          icon: 'success'
        });
      }, 500);
    },
    navigateTo(url) {
      uni.navigateTo({
        url: url
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏样式 */
.navbar {
  display: flex;
  align-items: center;
  height: 44px;
  background-color: #fff;
  padding: 0 15px;
  position: relative;
}

.navbar-back {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-top: 2px solid #333;
  border-left: 2px solid #333;
  transform: rotate(-45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 600;
  color: #333;
}

.navbar-right {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.settings-icon {
  color: #333;
}

/* 公共样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 数据概览样式 */
.overview-section {
  padding: 15px;
  background-color: #fff;
  margin-bottom: 10px;
}

.date-picker {
  display: flex;
  align-items: center;
  padding: 5px 10px;
  background-color: #f5f5f5;
  border-radius: 15px;
}

.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.date-icon {
  color: #666;
}

.stats-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px;
}

.stats-card {
  width: calc(50% - 10px);
  margin: 5px;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
}

.stats-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 5px;
}

.stats-label {
  font-size: 12px;
  color: #999;
}

.chart-container {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.chart-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.chart-legend {
  display: flex;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.legend-color {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  margin-right: 5px;
}

.legend-text {
  font-size: 12px;
  color: #999;
}

.chart-placeholder {
  height: 160px;
  position: relative;
}

.chart-mock {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.chart-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  position: relative;
}

.column-sent {
  width: 6px;
  background-color: rgba(255, 77, 79, 0.7);
  border-radius: 3px 3px 0 0;
  margin-bottom: 2px;
}

.column-received {
  width: 6px;
  background-color: rgba(82, 196, 26, 0.7);
  border-radius: 3px 3px 0 0;
}

.column-label {
  position: absolute;
  bottom: -20px;
  font-size: 10px;
  color: #999;
}

/* 营销工具样式 */
.tools-section {
  padding: 15px;
  background-color: #fff;
  margin-bottom: 10px;
}

.tools-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px;
}

.tool-item {
  width: calc(33.33% - 10px);
  margin: 5px;
  padding: 15px 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.tool-icon {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.tool-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.tool-desc {
  font-size: 12px;
  color: #999;
  text-align: center;
}

/* 最近活动样式 */
.recent-section {
  padding: 15px;
  background-color: #fff;
  margin-bottom: 10px;
}

.view-all {
  display: flex;
  align-items: center;
}

.view-all-text {
  font-size: 12px;
  color: #999;
  margin-right: 5px;
}

.arrow-icon {
  color: #999;
}

.activity-list {
  margin: 0 -15px;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.icon-inner {
  font-size: 14px;
  font-weight: 600;
}

.activity-content {
  flex: 1;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.activity-name {
  font-size: 15px;
  font-weight: 600;
  color: #333;
}

.activity-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
}

.status-active {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52C41A;
}

.status-ended {
  background-color: rgba(153, 153, 153, 0.1);
  color: #999;
}

.activity-info {
  display: flex;
  justify-content: space-between;
}

.activity-time, .activity-count {
  font-size: 12px;
  color: #999;
}

/* 营销攻略样式 */
.strategy-section {
  padding: 15px;
  background-color: #fff;
  margin-bottom: 20px;
}

.strategy-list {
  margin: 0 -15px;
}

.strategy-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.strategy-item:last-child {
  border-bottom: none;
}

.strategy-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.strategy-content {
  flex: 1;
}

.strategy-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.strategy-desc {
  font-size: 12px;
  color: #666;
}

.strategy-arrow {
  width: 12px;
  height: 12px;
  border-top: 1px solid #ccc;
  border-right: 1px solid #ccc;
  transform: rotate(45deg);
}
</style>