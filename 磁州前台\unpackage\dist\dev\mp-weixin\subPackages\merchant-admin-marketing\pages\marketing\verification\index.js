"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      dateRange: "最近7天",
      recentRecords: [
        {
          typeText: "拼团",
          typeClass: "type-group",
          title: "双人下午茶套餐拼团",
          code: "GP20230618001",
          user: "张三",
          time: "今天 14:30",
          status: "已核销",
          statusClass: "status-success"
        },
        {
          typeText: "优惠券",
          typeClass: "type-coupon",
          title: "新店开业满100减20券",
          code: "CP20230618002",
          user: "李四",
          time: "今天 11:15",
          status: "已核销",
          statusClass: "status-success"
        },
        {
          typeText: "秒杀",
          typeClass: "type-flash",
          title: "限时特价烤鸭套餐",
          code: "FS20230617005",
          user: "王五",
          time: "昨天 18:45",
          status: "已核销",
          statusClass: "status-success"
        }
      ],
      stats: {
        totalCount: 128,
        todayCount: 15,
        totalAmount: "12,846"
      },
      chartData: [
        { date: "6/12", value: 8, height: 40 },
        { date: "6/13", value: 12, height: 60 },
        { date: "6/14", value: 10, height: 50 },
        { date: "6/15", value: 15, height: 75 },
        { date: "6/16", value: 9, height: 45 },
        { date: "6/17", value: 14, height: 70 },
        { date: "6/18", value: 15, height: 75 }
      ]
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    showHelp() {
      common_vendor.index.showToast({
        title: "核销中心帮助",
        icon: "none"
      });
    },
    navigateTo(url) {
      common_vendor.index.navigateTo({
        url
      });
    }
  }
};
if (!Array) {
  const _component_rect = common_vendor.resolveComponent("rect");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  (_component_rect + _component_line + _component_svg + _component_path + _component_polyline)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: common_vendor.p({
      x: "3",
      y: "3",
      width: "18",
      height: "18",
      rx: "2",
      ry: "2"
    }),
    d: common_vendor.p({
      x1: "3",
      y1: "9",
      x2: "21",
      y2: "9"
    }),
    e: common_vendor.p({
      x1: "3",
      y1: "15",
      x2: "21",
      y2: "15"
    }),
    f: common_vendor.p({
      x1: "9",
      y1: "3",
      x2: "9",
      y2: "21"
    }),
    g: common_vendor.p({
      x1: "15",
      y1: "3",
      x2: "15",
      y2: "21"
    }),
    h: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      width: "24",
      height: "24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    i: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-admin-marketing/pages/marketing/verification/scan")),
    j: common_vendor.p({
      d: "M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"
    }),
    k: common_vendor.p({
      d: "M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"
    }),
    l: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      width: "24",
      height: "24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    m: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-admin-marketing/pages/marketing/verification/manual")),
    n: common_vendor.p({
      d: "M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
    }),
    o: common_vendor.p({
      points: "14 2 14 8 20 8"
    }),
    p: common_vendor.p({
      x1: "16",
      y1: "13",
      x2: "8",
      y2: "13"
    }),
    q: common_vendor.p({
      x1: "16",
      y1: "17",
      x2: "8",
      y2: "17"
    }),
    r: common_vendor.p({
      points: "10 9 9 9 8 9"
    }),
    s: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      width: "24",
      height: "24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    t: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-admin-marketing/pages/marketing/verification/records")),
    v: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-admin-marketing/pages/marketing/verification/records")),
    w: $data.recentRecords.length === 0
  }, $data.recentRecords.length === 0 ? {} : {
    x: common_vendor.f($data.recentRecords, (record, index, i0) => {
      return {
        a: common_vendor.t(record.typeText),
        b: common_vendor.n(record.typeClass),
        c: common_vendor.t(record.title),
        d: common_vendor.t(record.code),
        e: common_vendor.t(record.user),
        f: common_vendor.t(record.time),
        g: common_vendor.t(record.status),
        h: common_vendor.n(record.statusClass),
        i: index
      };
    })
  }, {
    y: common_vendor.t($data.dateRange),
    z: common_vendor.t($data.stats.totalCount),
    A: common_vendor.t($data.stats.todayCount),
    B: common_vendor.t($data.stats.totalAmount),
    C: common_vendor.f($data.chartData, (item, index, i0) => {
      return {
        a: common_vendor.t(item.value),
        b: index,
        c: item.height + "%"
      };
    }),
    D: common_vendor.f($data.chartData, (item, index, i0) => {
      return {
        a: common_vendor.t(item.date),
        b: index
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/verification/index.js.map
