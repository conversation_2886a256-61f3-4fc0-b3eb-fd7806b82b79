<template>
	<view class="business-page">
		<!-- 顶部蓝色背景 -->
		<view class="top-blue-bg"></view>
		
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 系统标题栏 -->
		<view class="navbar">
			<text class="navbar-title">同城商圈</text>
		</view>
		
		<!-- 轮播图 -->
		<view class="swiper-container">
			<swiper class="banner-swiper" circular :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500" indicator-color="rgba(255,255,255,0.4)" indicator-active-color="#ffffff">
				<swiper-item v-for="(banner, index) in bannerList" :key="index">
					<image class="banner-image" :src="banner.image" mode="aspectFill"></image>
				</swiper-item>
			</swiper>
		</view>
		
		<!-- 曝光量和入驻数 -->
		<view class="stats-bar">
			<view class="stat-item">
				<image class="stat-icon" src="/static/images/tabbar/喇叭.png"></image>
			<text class="exposure-text">曝光量: 123.4万次</text>
			</view>
			<text class="stat-divider">|</text>
			<view class="stat-item">
			<text class="in-row">已入驻: 567家</text>
			</view>
		</view>
		
		<!-- 商家推荐模块 -->
		<MerchantRecommend />
		
		<!-- 商家入驻卡片 -->
		<view class="join-card" @click="navigateToJoin">
			<view class="join-card-left">
				<view class="join-icon-wrap">
					<image class="join-icon" src="/static/images/tabbar/入驻卡片.png" />
				</view>
				<view class="join-info">
					<text class="join-title">商家入驻</text>
					<text class="join-desc">免费申请入驻，获取更多商机</text>
				</view>
			</view>
			<button class="join-btn" @click.stop="navigateToJoin">立即申请</button>
			<view class="hot-badge">热门</view>
		</view>
		
		<!-- 悬浮按钮 -->
		<FabButtons 
			pageName="business" 
			:pageInfo="{
				title: '磁州生活网 - 同城商圈',
				path: '/pages/business/business',
				imageUrl: '/static/images/banner/banner-1.png'
			}" 
		/>
		
		<!-- 分类宫格 -->
		<view class="category-grid">
			<!-- 搜索框移到分类宫格内部上方 -->
			<view class="search-container-inner">
				<view class="search-box">
					<input class="search-input" type="text" v-model="searchKeyword" placeholder="搜索或筛选商家" confirm-type="search" />
					<image class="search-icon" src="/static/images/tabbar/放大镜.png"></image>
				</view>
			</view>
			
			<view class="category-row">
				<view class="category-item" @click="navigateToFilter('房产楼盘')">
					<image class="category-icon" src="/static/images/tabbar/房产楼盘.png"></image>
					<text class="category-name">房产楼盘</text>
				</view>
				<view class="category-item" @click="navigateToFilter('美食小吃')">
					<image class="category-icon" src="/static/images/tabbar/美食小吃.png"></image>
					<text class="category-name">美食小吃</text>
				</view>
				<view class="category-item" @click="navigateToFilter('装修家居')">
					<image class="category-icon" src="/static/images/tabbar/装修家居.png"></image>
					<text class="category-name">装修家居</text>
				</view>
				<view class="category-item" @click="navigateToFilter('母婴专区')">
					<image class="category-icon" src="/static/images/tabbar/母婴专区.png"></image>
					<text class="category-name">母婴专区</text>
					<view class="hot-tag">热门</view>
				</view>
			</view>
			
			<view class="category-row">
				<view class="category-item" @click="navigateToFilter('休闲娱乐')">
					<image class="category-icon" src="/static/images/tabbar/休闲娱乐.png"></image>
					<text class="category-name">休闲娱乐</text>
				</view>
				<view class="category-item" @click="navigateToFilter('到家服务')">
					<image class="category-icon" src="/static/images/tabbar/商到家服务.png"></image>
					<text class="category-name">到家服务</text>
				</view>
				<view class="category-item" @click="navigateToFilter('开锁换锁')">
					<image class="category-icon" src="/static/images/tabbar/开锁换锁.png"></image>
					<text class="category-name">开锁换锁</text>
				</view>
				<view class="category-item" @click="navigateToFilter('数码通讯')">
					<image class="category-icon" src="/static/images/tabbar/数码通讯.png"></image>
					<text class="category-name">数码通讯</text>
				</view>
			</view>
			
			<view class="category-row">
				<view class="category-item" @click="navigateToFilter('车辆服务')">
					<image class="category-icon" src="/static/images/tabbar/商车辆服务.png"></image>
					<text class="category-name">车辆服务</text>
				</view>
				<view class="category-item" @click="navigateToFilter('教育培训')">
					<image class="category-icon" src="/static/images/tabbar/商教育培训.png"></image>
					<text class="category-name">教育培训</text>
				</view>
				<view class="category-item" @click="navigateToFilter('婚纱摄影')">
					<image class="category-icon" src="/static/images/tabbar/婚纱摄影.png"></image>
					<text class="category-name">婚纱摄影</text>
				</view>
				<view class="category-item" @click="navigateToFilter('农林牧渔')">
					<image class="category-icon" src="/static/images/tabbar/农林牧渔.png"></image>
					<text class="category-name">农林牧渔</text>
				</view>
			</view>
			
			<view class="category-row">
				<view class="category-item" @click="navigateToFilter('广告传媒')">
					<image class="category-icon" src="/static/images/tabbar/广告传媒.png"></image>
					<text class="category-name">广告传媒</text>
				</view>
				<view class="category-item" @click="navigateToFilter('其他行业')">
					<image class="category-icon" src="/static/images/tabbar/其他.png"></image>
					<text class="category-name">其他行业</text>
				</view>
				<view class="category-item"></view>
				<view class="category-item"></view>
			</view>
		</view>
		
		<!-- 标签页切换 -->
		<view class="tab-bar">
			<view class="tab-item" :class="{active: currentTab === 0}" @click="changeTab(0)">
				<text class="tab-text">推荐</text>
				<view class="tab-line" v-if="currentTab === 0"></view>
			</view>
			<view class="tab-item" :class="{active: currentTab === 1}" @click="changeTab(1)">
				<text class="tab-text">新入</text>
				<view class="tab-line" v-if="currentTab === 1"></view>
			</view>
			<view class="tab-item" :class="{active: currentTab === 2}" @click="changeTab(2)">
				<text class="tab-text">附近</text>
				<view class="tab-line" v-if="currentTab === 2"></view>
			</view>
		</view>
		
		<!-- 商家列表 -->
		<view class="business-list">
			<view class="business-item" v-for="business in businessList" :key="business.id" @click="navigateToShopDetail(business.id)">
				<view class="business-item-content">
					<!-- 商家Logo和基本信息区域 -->
					<view class="business-header">
						<view class="business-logo-container">
							<image class="business-logo" :src="business.logo" mode="aspectFill"></image>
							<view class="business-status" v-if="business.isOpen">营业中</view>
							<view class="business-status closed" v-else>休息中</view>
						</view>
				<view class="business-info">
							<view class="business-name-row">
					<text class="business-name">{{ business.name }}</text>
								<view class="business-verified" v-if="business.isVerified">
									<image class="verified-icon" src="/static/images/tabbar/已认证.png"></image>
								</view>
							</view>
							<view class="business-rating-row">
								<view class="rating-stars">
									<text class="rating-score">{{ business.rating || '4.8' }}</text>
									<view class="stars">
										<text class="star" v-for="i in 5" :key="i">★</text>
									</view>
								</view>
								<text class="business-reviews">{{ business.reviewCount || '128' }}条评价</text>
								<text class="business-distance" v-if="business.distance">{{ business.distance }}km</text>
							</view>
					<text class="business-desc">{{ business.description }}</text>
						</view>
					</view>
					
					<!-- 商家标签区域 -->
					<view class="business-tags-row">
						<scroll-view class="tags-scroll-view" scroll-x show-scrollbar="false">
					<view class="business-tags">
								<view class="business-tag" v-if="business.category">
									<text class="tag-text">{{ business.category }}</text>
								</view>
						<view class="business-tag red-packet-tag" v-if="business.hasConsumeRedPacket">
							<image class="tag-icon" src="/static/images/red-packet-icon.png"></image>
							<text class="tag-text">消费满100元抽红包</text>
						</view>
								<view class="business-tag new-tag" v-if="business.isNew">
									<text class="tag-text">新入驻</text>
					</view>
								<view class="business-tag hot-business-tag" v-if="business.isHot">
									<text class="tag-text">热门商家</text>
				</view>
								<view class="business-tag discount-tag" v-if="business.hasDiscount">
									<text class="tag-text">优惠活动</text>
								</view>
							</view>
						</scroll-view>
					</view>
					
					<!-- 商家特色服务区域 -->
					<view class="business-features" v-if="business.features && business.features.length">
						<view class="feature-item" v-for="(feature, idx) in business.features" :key="idx">
							<image class="feature-icon" :src="feature.icon"></image>
							<text class="feature-text">{{ feature.text }}</text>
						</view>
					</view>
					
					<!-- 商家操作区域 -->
					<view class="business-actions">
						<view class="action-btn call-btn" @click.stop="callBusiness(business.contactPhone)">
							<image class="action-icon" src="/static/images/tabbar/电话.png"></image>
							<text class="action-text">电话</text>
						</view>
						<view class="action-btn nav-btn" @click.stop="navigateToBusiness(business.address, business.name)">
							<image class="action-icon" src="/static/images/tabbar/定位.png"></image>
							<text class="action-text">导航</text>
						</view>
						<view class="action-btn share-btn" @click.stop="shareBusiness(business)">
							<image class="action-icon" src="/static/images/tabbar/分享.png"></image>
							<text class="action-text">分享</text>
						</view>
						<button class="follow-btn" :class="{'following': business.isFollowing}" @click.stop="followBusiness(business.id)">
							{{ business.isFollowing ? '已关注' : '+ 关注' }}
						</button>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import FabButtons from '@/components/FabButtons.vue';
import MerchantRecommend from '@/components/index/MerchantRecommend.vue';

// --- 响应式状态 ---
const currentTab = ref(0);
const statusBarHeight = ref(20);
const searchKeyword = ref('');
const locationAuthChecked = ref(false);

const bannerList = reactive([
					{ image: '/static/images/banner/banner-1.png' },
					{ image: '/static/images/banner/banner-2.png' },
					{ image: '/static/images/banner/banner-3.jpg' }
]);

const categories = reactive([
					{ name: '房产楼盘', icon: '/static/images/tabbar/房产楼盘.png' },
					{ name: '美食小吃', icon: '/static/images/tabbar/美食小吃.png' },
					{ name: '装修家居', icon: '/static/images/tabbar/装修家居.png' },
					{ name: '母婴专区', icon: '/static/images/tabbar/母婴专区.png', hot: true },
					{ name: '休闲娱乐', icon: '/static/images/tabbar/休闲娱乐.png' },
					{ name: '商到家服务', icon: '/static/images/tabbar/商到家服务.png' },
					{ name: '开锁换锁', icon: '/static/images/tabbar/开锁换锁.png' },
					{ name: '数码通讯', icon: '/static/images/tabbar/数码通讯.png' },
					{ name: '商车辆服务', icon: '/static/images/tabbar/商车辆服务.png' },
					{ name: '教育培训', icon: '/static/images/tabbar/商教育培训.png' },
					{ name: '婚纱摄影', icon: '/static/images/tabbar/婚纱摄影.png' },
					{ name: '农林牧渔', icon: '/static/images/tabbar/农林牧渔.png' },
					{ name: '广告传媒', icon: '/static/images/tabbar/广告传媒.png' },
					{ name: '其他行业', icon: '/static/images/tabbar/其他.png' }
]);

const allBusinessList = reactive([
					{
						id: "1",
						logo: '/static/images/cizhou.png',
						name: '五分利电器',
						description: '家电全网调货，全场特价，送货上门',
						category: '数码电器',
						scale: '10-20人',
		hasConsumeRedPacket: true,
		isNew: false,
						distance: 1.2,
						isOpen: true,
						isVerified: true,
						isFollowing: false,
						isHot: true,
						hasDiscount: true,
						rating: '4.9',
						reviewCount: 156,
						contactPhone: '188-8888-8888',
						address: '河北省邯郸市磁县祥和路',
						features: [
							{ icon: '/static/images/tabbar/送货.png', text: '免费送货' },
							{ icon: '/static/images/tabbar/安装.png', text: '上门安装' },
							{ icon: '/static/images/tabbar/保修.png', text: '全国联保' }
						]
					},
					{
						id: "2",
						logo: '/static/images/cizhou.png',
						name: '金鼎家居',
						description: '全屋定制、软装搭配、设计施工一站式服务',
						category: '家居家装',
						scale: '20-50人',
		hasConsumeRedPacket: true,
		isNew: true,
						distance: 0.8,
						isOpen: true,
						isVerified: true,
						isFollowing: false,
						isHot: false,
						hasDiscount: false,
						rating: '4.7',
						reviewCount: 98,
						contactPhone: '133-3333-3333',
						address: '河北省邯郸市磁县滏阳路68号',
						features: [
							{ icon: '/static/images/tabbar/设计.png', text: '免费设计' },
							{ icon: '/static/images/tabbar/测量.png', text: '上门测量' },
							{ icon: '/static/images/tabbar/保障.png', text: '质保5年' }
						]
					},
					{
						id: "3",
						logo: '/static/images/cizhou.png',
						name: '鲜丰水果',
						description: '新鲜水果，每日直采，支持微信下单，全城配送',
						category: '生鲜果蔬',
						scale: '5-10人',
						hasConsumeRedPacket: true,
						isNew: false,
						distance: 2.5,
						isOpen: true,
						isVerified: false,
						isFollowing: false,
						isHot: false,
						hasDiscount: true,
						rating: '4.8',
						reviewCount: 215,
						contactPhone: '177-7777-7777',
						address: '河北省邯郸市磁县时代广场A区112号',
						features: [
							{ icon: '/static/images/tabbar/配送.png', text: '全城配送' },
							{ icon: '/static/images/tabbar/新鲜.png', text: '每日新鲜' }
						]
					},
					{
						id: "4",
						logo: '/static/images/cizhou.png',
						name: '磁州书院',
						description: '综合性文化教育机构，少儿教育、成人培训、艺术培训',
						category: '文化教育',
						scale: '10-15人',
						hasConsumeRedPacket: true,
						isNew: false,
						distance: 3.1,
						isOpen: false,
						isVerified: true,
						isFollowing: false,
						isHot: false,
						hasDiscount: false,
						rating: '4.9',
						reviewCount: 87,
						contactPhone: '155-5555-5555',
						address: '河北省邯郸市磁县文化路29号',
						features: [
							{ icon: '/static/images/tabbar/教育.png', text: '专业教师' },
							{ icon: '/static/images/tabbar/证书.png', text: '官方认证' }
						]
					},
					{
						id: "5",
						logo: '/static/images/cizhou.png',
						name: '康美大药房',
						description: '全天24小时营业，医保定点药房，送药上门服务',
						category: '医疗健康',
						scale: '15-20人',
						hasConsumeRedPacket: true,
						isNew: false,
						distance: 1.8,
						isOpen: true,
						isVerified: true,
						isFollowing: false,
						isHot: true,
						hasDiscount: true,
						rating: '4.8',
						reviewCount: 176,
						contactPhone: '199-9999-9999',
						address: '河北省邯郸市磁县健康路45号',
						features: [
							{ icon: '/static/images/tabbar/医保.png', text: '医保定点' },
							{ icon: '/static/images/tabbar/送药.png', text: '送药上门' },
							{ icon: '/static/images/tabbar/24小时.png', text: '24小时营业' }
						]
					}
]);

const businessList = computed(() => {
	switch (currentTab.value) {
		case 1: // 新入驻
			return allBusinessList.filter(b => b.isNew).sort((a, b) => new Date(b.joinDate) - new Date(a.joinDate));
		case 2: // 附近
			return [...allBusinessList].sort((a, b) => a.distance - b.distance);
		case 0: // 推荐
		default:
			return allBusinessList;
				}
			});

// --- 方法 ---

const changeTab = (index) => {
	currentTab.value = index;
	if (index === 2 && !locationAuthChecked.value) {
		checkLocationPermission();
	}
};

const checkLocationPermission = () => {
	// 实际项目中应使用 uni.getSetting 和 uni.authorize
	uni.showModal({
		title: '位置信息授权',
		content: '我们需要获取您的位置信息以展示附近的商家，是否同意？',
		success: (res) => {
			if (res.confirm) {
				uni.getLocation({
					type: 'wgs84',
					success: (locRes) => {
						console.log('当前位置：', locRes.latitude, locRes.longitude);
						// 此处可以根据经纬度重新排序商家列表
						uni.showToast({ title: '已获取位置', icon: 'none' });
			},
					fail: () => {
						uni.showToast({ title: '获取位置失败', icon: 'error' });
					}
				});
			}
			locationAuthChecked.value = true;
		}
				});
};

const navigateToJoin = () => {
	uni.navigateTo({ url: '/pages/business/join' });
};

const navigateToFilter = (category) => {
	uni.navigateTo({ url: `/pages/business/filter?category=${category}` });
};

const navigateToShopDetail = (id) => {
	uni.navigateTo({ url: `/pages/business/shop-detail?id=${id}` });
};

const followBusiness = (id) => {
	console.log(`关注商家 ID: ${id}`);
	uni.showToast({ title: '关注成功', icon: 'success' });
};

const callBusiness = (phoneNumber) => {
	if (!phoneNumber) {
		uni.showToast({
			title: '暂无联系电话',
			icon: 'none'
		});
		return;
	}
	
	uni.makePhoneCall({
		phoneNumber: phoneNumber,
		success: () => {
			console.log('拨打电话成功');
		},
		fail: (err) => {
			console.log('拨打电话失败', err);
		}
	});
};

const navigateToBusiness = (address, name) => {
	if (!address) {
		uni.showToast({
			title: '暂无地址信息',
			icon: 'none'
		});
		return;
	}
	
	// 这里使用默认坐标，实际项目中应该从后端获取经纬度
	uni.openLocation({
		latitude: 36.313076,
		longitude: 114.347312,
		name: name,
		address: address,
		scale: 18
	});
};

const shareBusiness = (business) => {
	if (!business) return;
	
	uni.showShareMenu({
		withShareTicket: true,
		menus: ['shareAppMessage', 'shareTimeline']
	});
	
	// 设置分享内容
	uni.onShareAppMessage(() => {
		return {
			title: business.name + ' - ' + business.description,
			path: '/pages/business/shop-detail?id=' + business.id,
			imageUrl: business.logo
		};
	});
	
	// 小程序环境下，显示更多分享选项
	// #ifdef MP-WEIXIN
	uni.showActionSheet({
		itemList: ['分享给朋友', '分享到朋友圈', '生成分享图片'],
		success: (res) => {
			if (res.tapIndex === 2) {
				uni.showToast({
					title: '生成分享图片功能开发中',
					icon: 'none'
				});
			}
		}
	});
	// #endif
};

// --- 生命周期 ---
onMounted(() => {
	const systemInfo = uni.getSystemInfoSync();
	statusBarHeight.value = systemInfo.statusBarHeight || 20;
});
</script>

<style scoped>
/* 使用系统默认字体 */
.business-container {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* 其他需要使用阿里妈妈字体的地方也替换为系统字体 */
.title, .heading, .subtitle {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  font-weight: bold; /* 使用系统字体的粗体代替阿里妈妈黑体 */
}

.business-page {
	min-height: 100vh;
	background: #f5f7fa;
	position: relative;
	padding-bottom: 30rpx;
	overflow: hidden;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* 顶部蓝色背景 */
.top-blue-bg {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 320rpx;
	background: linear-gradient(135deg, #0052CC, #0066FF);
	z-index: 0;
}

/* 状态栏占位 */
.status-bar {
	position: relative;
	width: 100%;
	z-index: 1;
}

/* 导航栏样式 */
.navbar {
	position: relative;
	z-index: 1;
	height: 44px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.navbar-title {
	color: #ffffff;
	font-size: 18px;
	font-weight: 700;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* 统计栏 */
.stats-bar {
	position: relative;
	z-index: 1;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	padding: 20rpx 40rpx 5rpx;
	color: #333333;
	font-size: 24rpx;
}

.stat-item {
	display: flex;
	align-items: center;
}

.stat-icon {
	width: 36rpx;
	height: 36rpx;
	margin-right: 8rpx;
	opacity: 0.95;
}

.stat-divider {
	margin: 0 16rpx;
	color: rgba(0, 0, 0, 0.3);
	font-weight: 200;
}

.exposure-text, .in-row {
	color: #999999;
	font-weight: bold;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* 商家入驻卡片 */
.join-card {
	margin: 30rpx 30rpx 40rpx;
	padding: 20rpx 30rpx;
	background: #fff;
	border-radius: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: relative;
}

.join-card::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0) 100%);
	z-index: 0;
}

.join-card-left {
	display: flex;
	align-items: center;
	position: relative;
	z-index: 2;
}

.join-icon-wrap {
	width: 76rpx;
	height: 76rpx;
	background: #EEF1F6;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
	box-shadow: 6rpx 6rpx 12rpx rgba(174, 184, 210, 0.6),
	            -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.9),
	            inset 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.4);
	position: relative;
	z-index: 3;
	border: none;
}

.join-icon {
	width: 46rpx;
	height: 46rpx;
	opacity: 1;
	filter: none;
}

.join-info {
	display: flex;
	flex-direction: column;
}

.join-title {
	color: #3D56C1;
	font-size: 32rpx;
	font-weight: 700;
	margin-bottom: 8rpx;
	text-shadow: 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.8);
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
	letter-spacing: 1rpx;
}

.join-desc {
	color: #5F6A8A;
	font-size: 24rpx;
	text-shadow: 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.join-btn {
	background: linear-gradient(to right, #007AFF, #5AC8FA);
	color: #ffffff;
	font-size: 28rpx;
	padding: 0 30rpx;
	height: 64rpx;
	line-height: 64rpx;
	border-radius: 32rpx;
	margin: 0;
	box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
	transition: transform 0.2s ease;
}

.join-btn:active {
	transform: scale(0.95);
	box-shadow: 0 2rpx 5rpx rgba(0, 122, 255, 0.15);
}

.hot-badge {
	position: absolute;
	top: -3rpx;
	right: 8rpx;
	background: #FF5757;
	color: #ffffff;
	font-size: 20rpx;
	font-weight: bold;
	padding: 6rpx 12rpx;
	border-radius: 0 0 12rpx 12rpx;
	box-shadow: 0 4rpx 8rpx rgba(255, 87, 87, 0.4);
	display: block;
}

/* 分类宫格 */
.category-grid {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 28rpx;
	margin: 15rpx 24rpx 24rpx;
	padding: 20rpx 15rpx;
	position: relative;
	z-index: 2;
	box-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.1), 0 8rpx 15rpx rgba(0, 0, 0, 0.05);
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.6);
	transform: translateZ(0);
}

.category-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.category-row:last-child {
	margin-bottom: 0;
}

.category-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	position: relative;
	padding: 10rpx 0;
	transition: transform 0.3s ease;
}

.category-item:active {
	transform: scale(0.95);
}

.category-icon {
	width: 80rpx;
	height: 80rpx;
	margin-bottom: 6rpx;
	filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.category-name {
	font-size: 22rpx;
	color: #333;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.hot-tag {
	position: absolute;
	top: -3rpx;
	right: 8rpx;
	background: #ff4d4f;
	color: #ffffff;
	font-size: 16rpx;
	padding: 0 6rpx;
	border-radius: 6rpx;
	transform: rotate(10deg);
}

/* 标签页切换 */
.tab-bar {
	display: flex;
	background: rgba(255, 255, 255, 0.95);
	padding: 15rpx 0;
	margin: 30rpx 24rpx;
	position: relative;
	z-index: 2;
	box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
	border-radius: 24rpx;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.6);
	transform: translateZ(0);
}

.tab-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	position: relative;
	padding: 3rpx 0;
}

.tab-text {
	font-size: 30rpx;
	color: #999;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
	padding: 6rpx 0;
}

.tab-item.active .tab-text {
	color: #0052cc;
	font-weight: 500;
}

.tab-line {
	width: 40rpx;
	height: 4rpx;
	background: #0052cc;
	border-radius: 2rpx;
	position: absolute;
	bottom: -3rpx;
}

/* 商家列表 */
.business-list {
	padding: 0 24rpx;
	position: relative;
	z-index: 2;
}

.business-item {
	background: rgba(255, 255, 255, 0.98);
	border-radius: 32rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.06), 0 2rpx 10rpx rgba(0, 0, 0, 0.04);
	overflow: hidden;
	transform: translateZ(0);
	transition: transform 0.3s ease, box-shadow 0.3s ease;
	position: relative;
}

.business-item::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	border-radius: 32rpx;
	border: 1px solid rgba(0, 0, 0, 0.03);
	pointer-events: none;
}

.business-item:active {
	transform: translateY(2rpx) scale(0.995);
	box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.04), 0 2rpx 5rpx rgba(0, 0, 0, 0.02);
}

.business-item-content {
	padding: 24rpx;
}

.business-header {
	display: flex;
	margin-bottom: 16rpx;
}

.business-logo-container {
	position: relative;
	margin-right: 20rpx;
	flex-shrink: 0;
}

.business-logo {
	width: 120rpx;
	height: 120rpx;
	border-radius: 24rpx;
	object-fit: cover;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.6);
}

.business-status {
	position: absolute;
	bottom: -6rpx;
	left: 50%;
	transform: translateX(-50%);
	background: linear-gradient(to right, #4CD964, #32CD32);
	color: white;
	font-size: 20rpx;
	padding: 4rpx 12rpx;
	border-radius: 10rpx;
	white-space: nowrap;
	box-shadow: 0 2rpx 6rpx rgba(76, 217, 100, 0.2);
}

.business-status.closed {
	background: linear-gradient(to right, #8E8E93, #AEAEB2);
	box-shadow: 0 2rpx 6rpx rgba(142, 142, 147, 0.2);
}

.business-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}

.business-name-row {
	display: flex;
	align-items: center;
	margin-bottom: 8rpx;
}

.business-name {
	font-size: 34rpx;
	color: #000;
	font-weight: 600;
	margin-right: 8rpx;
	flex: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", Helvetica, Arial, sans-serif;
}

.business-verified {
	display: flex;
	align-items: center;
	margin-left: 8rpx;
}

.verified-icon {
	width: 32rpx;
	height: 32rpx;
}

.business-rating-row {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

.rating-stars {
	display: flex;
	align-items: center;
	margin-right: 12rpx;
}

.rating-score {
	font-size: 24rpx;
	color: #FF9500;
	font-weight: 600;
	margin-right: 6rpx;
}

.stars {
	display: flex;
}

.star {
	font-size: 22rpx;
	color: #FF9500;
	margin-right: 2rpx;
}

.business-reviews {
	font-size: 22rpx;
	color: #8E8E93;
	margin-right: 12rpx;
}

.business-distance {
	font-size: 22rpx;
	color: #8E8E93;
	background: #F2F2F7;
	padding: 2rpx 10rpx;
	border-radius: 10rpx;
}

.business-desc {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}

.business-tags-row {
	margin: 12rpx 0;
}

.tags-scroll-view {
	width: 100%;
	white-space: nowrap;
}

.business-tags {
	display: inline-flex;
	flex-wrap: nowrap;
}

.business-tag {
	display: inline-flex;
	align-items: center;
	padding: 6rpx 16rpx;
	border-radius: 16rpx;
	margin-right: 12rpx;
	background: #F2F2F7;
}

.business-tag:last-child {
	margin-right: 0;
}

.tag-icon {
	width: 24rpx;
	height: 24rpx;
	margin-right: 6rpx;
}

.tag-text {
	font-size: 22rpx;
	color: #666;
	line-height: 1.2;
}

.red-packet-tag {
	background: rgba(255, 69, 58, 0.1);
}

.red-packet-tag .tag-text {
	color: #FF4538;
}

.new-tag {
	background: rgba(0, 122, 255, 0.1);
}

.new-tag .tag-text {
	color: #007AFF;
}

.hot-business-tag {
	background: rgba(255, 149, 0, 0.1);
}

.hot-business-tag .tag-text {
	color: #FF9500;
}

.discount-tag {
	background: rgba(175, 82, 222, 0.1);
}

.discount-tag .tag-text {
	color: #AF52DE;
}

.business-features {
	display: flex;
	flex-wrap: wrap;
	padding: 12rpx 0;
	border-top: 1rpx solid rgba(0, 0, 0, 0.03);
	margin-top: 12rpx;
}

.feature-item {
	display: flex;
	align-items: center;
	margin-right: 24rpx;
	margin-top: 8rpx;
}

.feature-icon {
	width: 28rpx;
	height: 28rpx;
	margin-right: 6rpx;
}

.feature-text {
	font-size: 24rpx;
	color: #666;
}

.business-actions {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-top: 16rpx;
	padding-top: 16rpx;
	border-top: 1rpx solid rgba(0, 0, 0, 0.03);
}

.action-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 10rpx 0;
	flex: 1;
}

.action-icon {
	width: 36rpx;
	height: 36rpx;
	margin-bottom: 6rpx;
}

.action-text {
	font-size: 22rpx;
	color: #666;
}

.follow-btn {
	background: linear-gradient(to right, #007AFF, #5AC8FA);
	color: #ffffff;
	font-size: 26rpx;
	padding: 0 32rpx;
	height: 64rpx;
	line-height: 64rpx;
	border-radius: 32rpx;
	margin: 0;
	box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
	transition: all 0.2s ease;
	font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", sans-serif;
	font-weight: 500;
}

.follow-btn.following {
	background: #F2F2F7;
	color: #8E8E93;
	box-shadow: none;
	border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.follow-btn:active {
	transform: scale(0.95);
	opacity: 0.9;
}

/* 轮播图 */
.swiper-container {
	position: relative;
	z-index: 1;
	width: 650rpx;
	height: 230rpx;
	margin: 15rpx auto 15rpx;
	box-sizing: content-box;
	border: 12rpx solid #f0f5ff;
	border-radius: 28rpx;
	background: #f0f5ff;
	box-shadow: 0 25rpx 35rpx -15rpx rgba(0, 0, 0, 0.2),
	            0 15rpx 20rpx -15rpx rgba(0, 0, 0, 0.15),
	            inset 0 -2rpx 8rpx rgba(255, 255, 255, 0.7);
	transform: translateY(0);
	transition: transform 0.3s ease, box-shadow 0.3s ease;
	animation: float 6s ease-in-out infinite;
}

@keyframes float {
	0% {
		transform: translateY(0);
	}
	50% {
		transform: translateY(-10rpx);
	}
	100% {
		transform: translateY(0);
	}
}

.banner-swiper {
	width: 100%;
	height: 100%;
	border-radius: 8rpx;
	overflow: hidden;
	border: none;
	box-shadow: inset 0 2rpx 15rpx rgba(0, 0, 0, 0.1);
}

.banner-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
	transform: scale(1.01); /* 轻微放大，防止边缘出现空白 */
	transition: transform 0.3s ease;
}

.swiper-container:hover {
	box-shadow: 0 30rpx 40rpx -15rpx rgba(0, 0, 0, 0.25),
	            0 20rpx 25rpx -15rpx rgba(0, 0, 0, 0.18),
	            inset 0 -2rpx 8rpx rgba(255, 255, 255, 0.7);
	transform: translateY(-5rpx);
}

/* 搜索框 - 更新样式 */
.search-container-inner {
	position: relative;
	z-index: 2;
	margin: 0 20rpx 20rpx;
	width: 75%;
	margin-left: auto;
	margin-right: auto;
}

.search-box {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 36rpx;
	padding: 10rpx 20rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 6rpx 15rpx rgba(0, 0, 0, 0.08);
	border: 1px solid rgba(0, 0, 0, 0.05);
}

.search-input {
	flex: 1;
	font-size: 26rpx;
	height: 60rpx;
	color: #333;
	padding-left: 10rpx;
}

.search-icon {
	width: 32rpx;
	height: 32rpx;
	opacity: 0.6;
}

/* 商家卡片轮播 */
.merchant-recommend-section {
	margin: 24rpx 30rpx 30rpx;
	position: relative;
	z-index: 2;
	background: #ffffff;
	border-radius: 20rpx;
	padding: 24rpx 20rpx 30rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	overflow: hidden;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.section-title-wrap {
	display: flex;
	align-items: center;
}

.section-bar {
	width: 6rpx;
	height: 36rpx;
	background: linear-gradient(180deg, #0052CC, #0066FF);
	border-radius: 3rpx;
	margin-right: 16rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
}

.section-more {
	display: flex;
	align-items: center;
	padding: 6rpx 12rpx;
	border-radius: 16rpx;
	transition: all 0.2s ease;
}

.section-more:active {
	background: rgba(0, 0, 0, 0.05);
	transform: scale(0.96);
}

.more-text {
	font-size: 26rpx;
	color: #007AFF;
}

.more-icon {
	font-size: 26rpx;
	color: #007AFF;
	margin-left: 2rpx;
}

.merchant-swiper {
	width: 100%;
	height: 280rpx;
	margin-top: 20rpx;
}

.merchant-swiper-page {
	display: flex;
	justify-content: space-between;
	padding: 0 5rpx;
}

.merchant-item {
	width: 260rpx;
	height: 280rpx;
	background: #FFFFFF;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
	border: 1rpx solid #F2F2F7;
	padding: 12rpx;
}

.merchant-card {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 15rpx;
	box-sizing: border-box;
	position: relative;
	background: #FFFFFF;
	border-radius: 12rpx;
	border: 1rpx solid #E6EDFA;
}

.merchant-logo {
	width: 90rpx;
	height: 90rpx;
	border-radius: 45rpx;
	margin-bottom: 12rpx;
	border: 2rpx solid #F0F4FF;
	background: #F9F9F9;
}

.merchant-info {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.merchant-name-wrap {
	width: 100%;
	text-align: center;
	margin-bottom: 6rpx;
}

.merchant-name {
	font-size: 26rpx;
	color: #222;
	font-weight: 700;
	text-align: center;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	max-width: 100%;
}

.merchant-desc {
	font-size: 22rpx;
	color: #8a97b2;
	text-align: center;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	width: 100%;
	margin-bottom: 8rpx;
}

.merchant-category {
	font-size: 20rpx;
	color: #007AFF;
	background: rgba(0, 122, 255, 0.1);
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	margin-bottom: 12rpx;
}

.merchant-collect-btn {
	width: 80%;
	height: 56rpx;
	line-height: 56rpx;
	font-size: 24rpx;
	color: #FFFFFF;
	background: linear-gradient(to right, #007AFF, #5AC8FA) !important;
	border-radius: 28rpx;
	padding: 0 10rpx;
	margin: 0;
	position: absolute;
	bottom: 15rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
	text-align: center;
}

.collect-btn-text {
	font-size: 24rpx;
	color: #FFFFFF;
}

.merchant-indicators {
	display: flex;
	justify-content: center;
	margin-top: 15rpx;
}

.merchant-dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 6rpx;
	background-color: #D1D1D6;
	margin: 0 6rpx;
	transition: all 0.3s ease;
}

.merchant-dot.active {
	width: 24rpx;
	background: #007AFF;
}
</style> 
