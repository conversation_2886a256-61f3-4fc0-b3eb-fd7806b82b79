<view class="car-detail-container"><view class="custom-navbar" style="{{'padding-top:' + c}}"><view class="navbar-left" bindtap="{{b}}"><image src="{{a}}" class="back-icon"></image></view><view class="navbar-title">二手车辆详情</view><view class="navbar-right"></view></view><button id="shareButton" class="hidden-share-btn" open-type="share"></button><view class="car-detail-wrapper"><view class="content-card car-info-card"><view class="car-header"><view class="car-title-row"><text class="car-title">{{d}}</text><text class="car-price">{{e}}</text></view><view class="car-meta"><view class="car-tag-group"><view wx:for="{{f}}" wx:for-item="tag" wx:key="b" class="car-tag">{{tag.a}}</view></view><text class="car-publish-time">发布于 {{g}}</text></view></view><swiper class="car-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}"><swiper-item wx:for="{{h}}" wx:for-item="image" wx:key="b"><image src="{{image.a}}" mode="aspectFill" class="car-image"></image></swiper-item></swiper><view class="car-basic-info"><view class="info-item"><text class="info-label">品牌型号</text><text class="info-value">{{i}}</text></view><view class="info-item"><text class="info-label">上牌时间</text><text class="info-value">{{j}}</text></view><view class="info-item"><text class="info-label">行驶里程</text><text class="info-value">{{k}}</text></view><view class="info-item"><text class="info-label">排放标准</text><text class="info-value">{{l}}</text></view></view></view><view class="content-card car-config-card"><view class="section-title">车辆配置</view><view class="config-list"><view wx:for="{{m}}" wx:for-item="item" wx:key="c" class="config-item"><text class="{{['config-icon', 'iconfont', item.a]}}"></text><text class="config-text">{{item.b}}</text></view></view></view><view class="content-card car-condition-card"><view class="section-title">车况信息</view><view class="condition-list"><view wx:for="{{n}}" wx:for-item="item" wx:key="c" class="condition-item"><text class="condition-label">{{item.a}}</text><text class="condition-value">{{item.b}}</text></view></view></view><view class="content-card description-card"><view class="section-title">车辆描述</view><view class="description-content"><text class="description-text">{{o}}</text></view></view><view class="content-card owner-card"><view class="owner-header"><view class="owner-avatar"><image src="{{p}}" mode="aspectFill"></image></view><view class="owner-info"><text class="owner-name">{{q}}</text><view class="owner-meta"><text class="owner-type">{{r}}</text><text class="owner-rating">信用等级 {{s}}</text></view></view><view wx:if="{{t}}" class="owner-auth"><text class="iconfont icon-verified"></text><text class="auth-text">已认证</text></view></view></view><view class="content-card contact-card"><view class="contact-header"><text class="card-title">联系方式</text></view><view class="contact-content"><view class="contact-item"><text class="contact-label">联系人</text><text class="contact-value">{{v}}</text></view><view class="contact-item"><text class="contact-label">电话</text><text class="contact-value contact-phone" bindtap="{{x}}">{{w}}</text></view><view class="contact-tips"><text class="tips-icon iconfont icon-info"></text><text class="tips-text">请说明在"磁州生活网"看到的信息</text></view></view></view><report-card u-i="74e0686f-0" bind:__l="__l"></report-card><view class="content-card related-cars-card"><view class="section-title">相关车辆推荐</view><view class="related-cars-content"><view class="related-cars-list"><view wx:for="{{y}}" wx:for-item="car" wx:key="i" class="related-car-item" bindtap="{{car.j}}"><view class="car-item-content"><view class="car-item-left"><image class="car-image" src="{{car.a}}" mode="aspectFill"></image></view><view class="car-item-middle"><text class="car-item-title">{{car.b}}</text><view class="car-item-brand">{{car.c}} · {{car.d}}</view><view class="car-item-tags"><text wx:for="{{car.e}}" wx:for-item="tag" wx:key="b" class="car-item-tag">{{tag.a}}</text><text wx:if="{{car.f}}" class="car-item-tag-more">+{{car.g}}</text></view></view><view class="car-item-right"><text class="car-item-price">{{car.h}}</text></view></view></view><view wx:if="{{z}}" class="empty-related-cars"><image src="{{A}}" class="empty-image" mode="aspectFit"></image><text class="empty-text">暂无相关车辆</text></view></view><view wx:if="{{B}}" class="view-more-btn" catchtap="{{C}}"><text class="view-more-text">查看更多车辆信息</text><text class="view-more-icon iconfont icon-right"></text></view></view></view></view><view class="interaction-toolbar"><view class="toolbar-item" bindtap="{{E}}"><image src="{{D}}" class="toolbar-icon"></image><text class="toolbar-text">首页</text></view><view class="toolbar-item" bindtap="{{G}}"><image src="{{F}}" class="toolbar-icon"></image><text class="toolbar-text">收藏</text></view><button class="share-button toolbar-item" open-type="share"><image src="{{H}}" class="toolbar-icon"></image><text class="toolbar-text">分享</text></button><view class="toolbar-item" bindtap="{{J}}"><image src="{{I}}" class="toolbar-icon"></image><text class="toolbar-text">私信</text></view><view class="toolbar-item call-button" bindtap="{{K}}"><view class="call-button-content"><text class="call-text">打电话</text><text class="call-subtitle">请说在磁州生活网看到的</text></view></view></view></view>