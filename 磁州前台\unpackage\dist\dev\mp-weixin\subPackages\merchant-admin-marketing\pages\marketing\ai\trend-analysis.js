"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const TabBar = () => "../../../../../components/TabBar.js";
const _sfc_main = {
  components: {
    TabBar
  },
  data() {
    return {
      dateRange: "2023/10/01 - 2023/10/30",
      overviewData: {
        totalSales: 128650,
        salesGrowth: 15.8,
        totalOrders: 1256,
        ordersGrowth: 12.3,
        totalCustomers: 876,
        customersGrowth: 8.5,
        averageOrderValue: 102.43,
        aovGrowth: -2.1
      },
      isLoading: false,
      chartTabs: ["日", "周", "月"],
      activeChartTab: 0,
      chartWidth: 300,
      chartHeight: 150,
      salesPath: "",
      ordersPath: "",
      salesPoints: [],
      ordersPoints: [],
      chartData: {
        dates: ["10/01", "10/05", "10/10", "10/15", "10/20", "10/25", "10/30"],
        sales: [5e3, 8e3, 7500, 12e3, 9e3, 15e3, 13e3],
        orders: [50, 80, 75, 120, 90, 150, 130]
      },
      chartStats: {
        avgDailySales: 4288.33,
        peakSalesDay: "10月25日",
        salesGrowthRate: 15.8
      }
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    refreshData() {
      this.isLoading = true;
      setTimeout(() => {
        this.isLoading = false;
        common_vendor.index.showToast({
          title: "数据已更新",
          icon: "success"
        });
      }, 1500);
    },
    showCategoryFilter() {
      common_vendor.index.showActionSheet({
        itemList: ["全部类别", "美妆", "服装", "科技", "家居", "食品"],
        success: (res) => {
        }
      });
    },
    showDatePicker() {
      common_vendor.index.showToast({
        title: "日期选择功能开发中",
        icon: "none"
      });
    },
    formatNumber(num) {
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    switchChartTab(index) {
      this.activeChartTab = index;
      common_vendor.index.showToast({
        title: "切换到" + this.chartTabs[index] + "视图",
        icon: "none"
      });
      this.calculateChartPaths();
    },
    // 计算销售额和订单量的SVG路径
    calculateChartPaths() {
      const salesData = this.chartData.sales;
      const ordersData = this.chartData.orders;
      const maxSales = Math.max(...salesData);
      const maxOrders = Math.max(...ordersData);
      this.salesPoints = salesData.map((value, index) => {
        const x = index / (salesData.length - 1) * this.chartWidth;
        const y = this.chartHeight - value / maxSales * this.chartHeight;
        return { x, y };
      });
      this.ordersPoints = ordersData.map((value, index) => {
        const x = index / (ordersData.length - 1) * this.chartWidth;
        const y = this.chartHeight - value / maxOrders * this.chartHeight;
        return { x, y };
      });
      this.salesPath = this.generatePath(this.salesPoints);
      this.ordersPath = this.generatePath(this.ordersPoints);
    },
    // 生成SVG路径
    generatePath(points) {
      if (points.length === 0)
        return "";
      let path = `M ${points[0].x} ${points[0].y}`;
      for (let i = 1; i < points.length; i++) {
        const cp1x = points[i - 1].x + (points[i].x - points[i - 1].x) / 3;
        const cp1y = points[i - 1].y;
        const cp2x = points[i].x - (points[i].x - points[i - 1].x) / 3;
        const cp2y = points[i].y;
        path += ` C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${points[i].x} ${points[i].y}`;
      }
      return path;
    },
    applyRecommendation(index) {
      common_vendor.index.showToast({
        title: "已应用建议" + (index + 1),
        icon: "success"
      });
    },
    ignoreRecommendation(index) {
      common_vendor.index.showToast({
        title: "已忽略建议" + (index + 1),
        icon: "none"
      });
    },
    handleTabChange(tabId) {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-marketing/pages/marketing/ai/trend-analysis.vue:486", "切换到标签:", tabId);
    }
  },
  mounted() {
    this.calculateChartPaths();
  }
};
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_tab_bar = common_vendor.resolveComponent("tab-bar");
  (_component_path + _component_svg + _component_circle + _component_tab_bar)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.p({
      d: "M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"
    }),
    c: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      width: "20",
      height: "20",
      fill: "#FFFFFF"
    }),
    d: common_vendor.o((...args) => $options.refreshData && $options.refreshData(...args)),
    e: common_vendor.t($data.dateRange),
    f: common_vendor.p({
      d: "M9 11H7v2h2v-2zm4 0h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2h-2v2H8V2H6v2H5c-1.11 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z"
    }),
    g: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      width: "16",
      height: "16",
      fill: "#666666"
    }),
    h: common_vendor.o((...args) => $options.showDatePicker && $options.showDatePicker(...args)),
    i: common_vendor.p({
      d: "M7 18h2V6H7v12zm4 4h2V2h-2v20zm-8-8h2v-4H3v4zm12 4h2V6h-2v12zm4-8v4h2v-4h-2z"
    }),
    j: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      width: "20",
      height: "20",
      fill: "#6366F1"
    }),
    k: common_vendor.t($options.formatNumber($data.overviewData.totalSales)),
    l: common_vendor.t($data.overviewData.salesGrowth),
    m: common_vendor.p({
      d: "M18 17H6v-2h12v2zm0-4H6v-2h12v2zm0-4H6V7h12v2zM3 22l1.5-1.5L6 22l1.5-1.5L9 22l1.5-1.5L12 22l1.5-1.5L15 22l1.5-1.5L18 22l1.5-1.5L21 22V2l-1.5 1.5L18 2l-1.5 1.5L15 2l-1.5 1.5L12 2l-1.5 1.5L9 2 7.5 3.5 6 2 4.5 3.5 3 2v20z"
    }),
    n: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      width: "20",
      height: "20",
      fill: "#FF9500"
    }),
    o: common_vendor.t($options.formatNumber($data.overviewData.totalOrders)),
    p: common_vendor.t($data.overviewData.ordersGrowth),
    q: common_vendor.p({
      d: "M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5z"
    }),
    r: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      width: "20",
      height: "20",
      fill: "#34C759"
    }),
    s: common_vendor.t($options.formatNumber($data.overviewData.totalCustomers)),
    t: common_vendor.t($data.overviewData.customersGrowth),
    v: common_vendor.p({
      d: "M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"
    }),
    w: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      width: "20",
      height: "20",
      fill: "#FF3B30"
    }),
    x: common_vendor.t($data.overviewData.averageOrderValue),
    y: common_vendor.t($data.overviewData.aovGrowth),
    z: common_vendor.t($data.dateRange),
    A: common_vendor.f($data.chartTabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab),
        b: index,
        c: common_vendor.n($data.activeChartTab === index ? "active" : ""),
        d: common_vendor.o(($event) => $options.switchChartTab(index), index)
      };
    }),
    B: common_vendor.f(5, (i, k0, i0) => {
      return {
        a: i
      };
    }),
    C: common_vendor.p({
      d: $data.salesPath,
      fill: "none",
      stroke: "#6366F1",
      ["stroke-width"]: "2"
    }),
    D: common_vendor.f($data.salesPoints, (point, index, i0) => {
      return {
        a: "sales-point-" + index,
        b: "3c3b79cf-14-" + i0 + ",3c3b79cf-12",
        c: common_vendor.p({
          cx: point.x,
          cy: point.y,
          r: "4",
          fill: "#6366F1"
        })
      };
    }),
    E: common_vendor.p({
      width: $data.chartWidth,
      height: $data.chartHeight,
      viewBox: "0 0 300 150"
    }),
    F: common_vendor.p({
      d: $data.ordersPath,
      fill: "none",
      stroke: "#FF9500",
      ["stroke-width"]: "2"
    }),
    G: common_vendor.f($data.ordersPoints, (point, index, i0) => {
      return {
        a: "orders-point-" + index,
        b: "3c3b79cf-17-" + i0 + ",3c3b79cf-15",
        c: common_vendor.p({
          cx: point.x,
          cy: point.y,
          r: "4",
          fill: "#FF9500"
        })
      };
    }),
    H: common_vendor.p({
      width: $data.chartWidth,
      height: $data.chartHeight,
      viewBox: "0 0 300 150"
    }),
    I: common_vendor.f($data.chartData.dates, (date, index, i0) => {
      return {
        a: common_vendor.t(date),
        b: index
      };
    }),
    J: common_vendor.t($options.formatNumber($data.chartStats.avgDailySales)),
    K: common_vendor.t($data.chartStats.peakSalesDay),
    L: common_vendor.t($data.chartStats.salesGrowthRate),
    M: common_vendor.o((...args) => $options.showCategoryFilter && $options.showCategoryFilter(...args)),
    N: common_vendor.o(($event) => $options.applyRecommendation(0)),
    O: common_vendor.o(($event) => $options.ignoreRecommendation(0)),
    P: common_vendor.o(($event) => $options.applyRecommendation(1)),
    Q: common_vendor.o(($event) => $options.ignoreRecommendation(1)),
    R: common_vendor.o(($event) => $options.applyRecommendation(2)),
    S: common_vendor.o(($event) => $options.ignoreRecommendation(2)),
    T: common_vendor.o($options.handleTabChange),
    U: common_vendor.p({
      ["active-tab"]: "marketing"
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/ai/trend-analysis.js.map
