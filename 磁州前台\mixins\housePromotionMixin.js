/**
 * 房产推广混入
 * 为房产详情页提供推广能力
 */
import basePromotionMixin from './basePromotionMixin';

export default {
  mixins: [basePromotionMixin],

  data() {
    return {
      // 设置页面类型为房产
      pageType: 'house'
    };
  },

  methods: {
    /**
     * 重写：判断当前用户是否是内容所有者
     */
    isContentOwner() {
      // 获取当前用户ID
      const currentUserId = this.$store?.state?.user?.userId || '';
      // 获取房产发布者ID
      const publisherId = this.houseInfo?.publisherId || this.houseDetail?.publisherId || '';

      // 判断当前用户是否是房产发布者
      return currentUserId && publisherId && currentUserId === publisherId;
    },

    /**
     * 重写：判断当前内容是否支持佣金
     */
    isCommissionContent() {
      // 房产通常支持佣金
      const canDistribute = this.houseInfo?.canDistribute !== false || this.houseDetail?.canDistribute !== false; // 默认为true
      
      return canDistribute;
    },

    /**
     * 重写：生成推广数据
     */
    generatePromotionData() {
      // 获取房产数据
      const house = this.houseInfo || this.houseDetail || {};
      
      // 构建推广数据
      this.promotionData = {
        id: house.id || '',
        title: house.title || '房产详情',
        image: house.images?.[0] || house.coverImage || '/static/images/house-default.jpg',
        price: house.price || 0,
        priceUnit: house.priceUnit || '万',
        area: house.area || 0,
        roomType: house.roomType || '',
        location: house.location || house.address || '',
        tags: house.tags || [],
        // 如果有更多房产特定字段，可以在这里添加
      };
    },

    /**
     * 显示房产推广浮层
     */
    showHousePromotion() {
      // 如果没有推广权限，显示提示
      if (!this.hasPromotionPermission) {
        uni.showToast({
          title: '暂无推广权限',
          icon: 'none'
        });
        return;
      }

      // 打开推广工具
      this.openPromotionTools();
    }
  }
}; 