{"version": 3, "file": "HouseRentCard.js", "sources": ["components/cards/HouseRentCard.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7AvY29tcG9uZW50cy9jYXJkcy9Ib3VzZVJlbnRDYXJkLnZ1ZQ"], "sourcesContent": ["<template>\n  <BaseInfoCard :item=\"item\">\n    <template #content>\n      <view class=\"house-details\">\n        <!-- 房屋基本信息 -->\n        <view class=\"house-basic-info\">\n          <view class=\"house-type\" v-if=\"item.houseType\">\n            <view class=\"house-icon type-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <path d=\"M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"></path>\n                <polyline points=\"9 22 9 12 15 12 15 22\"></polyline>\n              </svg>\n            </view>\n            <text class=\"house-text\">{{item.houseType}}</text>\n          </view>\n          \n          <view class=\"house-size\" v-if=\"item.size\">\n            <view class=\"house-icon size-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"></rect>\n                <line x1=\"3\" y1=\"9\" x2=\"21\" y2=\"9\"></line>\n                <line x1=\"9\" y1=\"21\" x2=\"9\" y2=\"9\"></line>\n              </svg>\n            </view>\n            <text class=\"house-text\">{{item.size}}㎡</text>\n          </view>\n          \n          <view class=\"house-floor\" v-if=\"item.floor\">\n            <view class=\"house-icon floor-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <rect x=\"2\" y=\"3\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\"></rect>\n                <line x1=\"8\" y1=\"21\" x2=\"16\" y2=\"21\"></line>\n                <line x1=\"12\" y1=\"17\" x2=\"12\" y2=\"21\"></line>\n              </svg>\n            </view>\n            <text class=\"house-text\">{{item.floor}}</text>\n          </view>\n          \n          <view class=\"house-direction\" v-if=\"item.direction\">\n            <view class=\"house-icon direction-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n                <polyline points=\"16.24 7.76 14.12 14.12 7.76 16.24\"></polyline>\n                <line x1=\"12\" y1=\"12\" x2=\"12.01\" y2=\"12\"></line>\n              </svg>\n            </view>\n            <text class=\"house-text\">{{item.direction}}</text>\n          </view>\n        </view>\n        \n        <!-- 房屋配套设施 -->\n        <view class=\"house-facilities\" v-if=\"item.facilities && item.facilities.length\">\n          <view class=\"facility-tag\" v-for=\"(facility, index) in item.facilities\" :key=\"index\">\n            <view class=\"facility-icon\" :class=\"getFacilityIconClass(facility)\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <polyline points=\"20 6 9 17 4 12\"></polyline>\n              </svg>\n            </view>\n            <text class=\"facility-text\">{{facility}}</text>\n          </view>\n        </view>\n        \n        <!-- 房屋特色标签 -->\n        <view class=\"house-features\" v-if=\"item.features && item.features.length\">\n          <view class=\"feature-tag\" v-for=\"(feature, index) in item.features\" :key=\"index\">\n            <text class=\"feature-text\">{{feature}}</text>\n          </view>\n        </view>\n        \n        <!-- 房屋地理位置 -->\n        <view class=\"house-location\" v-if=\"item.location\">\n          <view class=\"location-icon\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <path d=\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\"></path>\n              <circle cx=\"12\" cy=\"10\" r=\"3\"></circle>\n            </svg>\n          </view>\n          <text class=\"location-text\">{{item.location}}</text>\n        </view>\n      </view>\n    </template>\n  </BaseInfoCard>\n</template>\n\n<script setup>\nimport BaseInfoCard from './BaseInfoCard.vue';\n\nconst props = defineProps({\n  item: {\n    type: Object,\n    required: true\n  }\n});\n\nfunction getFacilityIconClass(facility) {\n  const facilityMap = {\n    '空调': 'ac-icon',\n    '热水器': 'water-icon',\n    '洗衣机': 'washer-icon',\n    '冰箱': 'fridge-icon',\n    '电视': 'tv-icon',\n    '宽带': 'wifi-icon',\n    '衣柜': 'wardrobe-icon',\n    '床': 'bed-icon',\n    '沙发': 'sofa-icon',\n    '天然气': 'gas-icon'\n  };\n  \n  return facilityMap[facility] || 'default-icon';\n}\n</script>\n\n<style scoped>\n.house-details {\n  margin-top: 16rpx;\n  padding-top: 16rpx;\n  border-top: 1rpx solid rgba(60, 60, 67, 0.1);\n}\n\n/* 房屋基本信息 */\n.house-basic-info {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16rpx;\n  margin-bottom: 16rpx;\n}\n\n.house-type, .house-size, .house-floor, .house-direction {\n  display: flex;\n  align-items: center;\n  background: rgba(0, 0, 0, 0.02);\n  padding: 8rpx 16rpx;\n  border-radius: 24rpx;\n  box-shadow: inset 0 1rpx 2rpx rgba(0, 0, 0, 0.05);\n}\n\n.house-icon {\n  margin-right: 8rpx;\n  color: #8e8e93;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.type-icon {\n  color: #FF9500;\n}\n\n.size-icon {\n  color: #34C759;\n}\n\n.floor-icon {\n  color: #5856D6;\n}\n\n.direction-icon {\n  color: #007AFF;\n}\n\n.house-text {\n  font-size: 24rpx;\n  color: #636366;\n  font-weight: 500;\n}\n\n/* 房屋配套设施 */\n.house-facilities {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12rpx;\n  margin-bottom: 16rpx;\n}\n\n.facility-tag {\n  display: flex;\n  align-items: center;\n  background: linear-gradient(135deg, rgba(52, 199, 89, 0.1), rgba(48, 176, 199, 0.1));\n  border-radius: 16rpx;\n  padding: 6rpx 16rpx;\n  border: 1rpx solid rgba(52, 199, 89, 0.2);\n}\n\n.facility-icon {\n  margin-right: 6rpx;\n  color: #34C759;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.ac-icon, .water-icon {\n  color: #007AFF;\n}\n\n.washer-icon, .fridge-icon {\n  color: #5856D6;\n}\n\n.tv-icon, .wifi-icon {\n  color: #FF9500;\n}\n\n.wardrobe-icon, .bed-icon, .sofa-icon {\n  color: #FF3B30;\n}\n\n.gas-icon {\n  color: #FF2D55;\n}\n\n.facility-text {\n  font-size: 22rpx;\n  color: #34C759;\n  font-weight: 500;\n}\n\n/* 房屋特色标签 */\n.house-features {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12rpx;\n  margin-bottom: 16rpx;\n}\n\n.feature-tag {\n  background: linear-gradient(135deg, rgba(255, 149, 0, 0.1), rgba(255, 59, 48, 0.1));\n  border-radius: 16rpx;\n  padding: 6rpx 16rpx;\n  border: 1rpx solid rgba(255, 149, 0, 0.2);\n}\n\n.feature-text {\n  font-size: 22rpx;\n  color: #FF9500;\n  font-weight: 500;\n}\n\n/* 房屋地理位置 */\n.house-location {\n  display: flex;\n  align-items: center;\n  background: rgba(0, 0, 0, 0.02);\n  padding: 12rpx 16rpx;\n  border-radius: 16rpx;\n  margin-top: 16rpx;\n}\n\n.location-icon {\n  color: #007AFF;\n  margin-right: 10rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.location-text {\n  font-size: 26rpx;\n  color: #636366;\n  flex: 1;\n}\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/components/cards/HouseRentCard.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;;;;;;;;;;;;;AAqFA,MAAM,eAAe,MAAW;;;;;;;;;;AAShC,aAAS,qBAAqB,UAAU;AACtC,YAAM,cAAc;AAAA,QAClB,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,MACX;AAEE,aAAO,YAAY,QAAQ,KAAK;AAAA,IAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5GA,GAAG,gBAAgB,SAAS;"}