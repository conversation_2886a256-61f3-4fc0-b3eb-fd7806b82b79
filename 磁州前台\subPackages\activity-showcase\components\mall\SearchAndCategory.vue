<template>
  <view class="search-category-container">
    <!-- 搜索框 -->
    <view class="search-box" @click="$emit('search')">
      <view class="search-input">
        <svg class="search-icon" viewBox="0 0 24 24" width="20" height="20">
          <path d="M11 17.25a6.25 6.25 0 110-12.5 6.25 6.25 0 010 12.5zm0 0L21 21" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>
        <text class="placeholder">搜索磁州商品、店铺</text>
      </view>
      <view class="scan-btn" @click.stop="$emit('scan')">
        <svg class="scan-icon" viewBox="0 0 24 24" width="20" height="20">
          <path d="M7 3H5a2 2 0 0 0-2 2v2M17 3h2a2 2 0 0 1 2 2v2M7 21H5a2 2 0 0 1-2-2v-2M17 21h2a2 2 0 0 0 2-2v-2" stroke="#FF3B69" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          <rect x="9" y="9" width="6" height="6" rx="1" stroke="#FF3B69" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></rect>
        </svg>
      </view>
    </view>

    <!-- 分类导航 -->
    <view class="category-grid">
        <view 
          v-for="(category, index) in categories" 
          :key="index" 
          class="category-item"
          :class="{ active: currentCategory === index }"
          @click="selectCategory(index)"
        >
        <view class="category-icon-wrapper">
          <view class="category-icon" :style="{ background: getGradientBackground(category.bgColor) }">
            <image class="icon-image" :src="category.icon" mode="aspectFit"></image>
          </view>
          <view class="category-indicator" v-if="currentCategory === index"></view>
        </view>
        <text class="category-name">{{ category.name }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';

// 组件属性定义
const props = defineProps({
  categories: {
    type: Array,
    default: () => []
  }
});

// 定义事件
const emit = defineEmits(['search', 'scan', 'categoryChange']);

// 当前选中的分类
const currentCategory = ref(0);

// 选择分类
const selectCategory = (index) => {
  currentCategory.value = index;
  // 触发分类变更事件
  emit('categoryChange', index);
};

// 生成渐变背景
const getGradientBackground = (baseColor) => {
  // 根据基础颜色生成渐变
  return `linear-gradient(135deg, ${baseColor} 0%, ${lightenColor(baseColor, 20)} 100%)`;
};

// 颜色变亮函数
const lightenColor = (hex, percent) => {
  // 将颜色转换为RGB
  let r = parseInt(hex.substring(1, 3), 16);
  let g = parseInt(hex.substring(3, 5), 16);
  let b = parseInt(hex.substring(5, 7), 16);
  
  // 增加亮度
  r = Math.min(255, Math.floor(r * (1 + percent / 100)));
  g = Math.min(255, Math.floor(g * (1 + percent / 100)));
  b = Math.min(255, Math.floor(b * (1 + percent / 100)));
  
  // 转换回HEX格式
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
};
</script>

<style lang="scss" scoped>
.search-category-container {
  padding: 20rpx 30rpx 30rpx;
  background: #FFFFFF;
  border-radius: 0 0 30rpx 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
}

/* 搜索框 */
.search-box {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  
  .search-input {
    flex: 1;
    height: 80rpx;
    background-color: #F2F2F7;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    
    .search-icon {
      margin-right: 10rpx;
      color: #8E8E93;
    }
    
    .placeholder {
      font-size: 28rpx;
      color: #8E8E93;
    }
  }
  
  .scan-btn {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20rpx;
    background-color: #F2F2F7;
    border-radius: 50%;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    
    .scan-icon {
      color: #FF3B69;
    }
  }
}

/* 分类导航 - 网格布局 */
.category-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16rpx 10rpx;
  
  .category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .category-icon-wrapper {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 8rpx;
    }
      
      .category-icon {
      width: 90rpx;
      height: 90rpx;
      border-radius: 24rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      transition: all 0.3s ease;
      box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
        
        .icon-image {
        width: 50rpx;
        height: 50rpx;
        }
    }
    
    .category-indicator {
      position: absolute;
      bottom: -8rpx;
      width: 16rpx;
      height: 4rpx;
      background-color: #FF3B69;
      border-radius: 2rpx;
      transition: all 0.3s ease;
      }
      
      .category-name {
        font-size: 24rpx;
        color: #333333;
      transition: all 0.3s ease;
      width: 100%;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      }
      
      &.active {
        .category-icon {
        transform: translateY(-4rpx);
        box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.1);
      }
      
      .category-indicator {
        width: 32rpx;
        }
        
        .category-name {
          color: #FF3B69;
        font-weight: 600;
      }
    }
  }
}
</style> 