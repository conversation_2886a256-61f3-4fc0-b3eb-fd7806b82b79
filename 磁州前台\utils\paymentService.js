import secureRequest from './secureRequest';
import securityUtils from './securityUtils';
import securityConfig from './securityConfig';
import securityAudit from './securityAudit';

/**
 * 支付服务类
 * 处理各种支付相关功能
 */
class PaymentService {
  constructor() {
    // API基础路径
    this.baseUrl = securityConfig.getApiBaseUrl();
    
    // 支持的支付渠道
    this.paymentChannels = [
      { id: 'wechat', name: '微信支付', icon: 'https://via.placeholder.com/40', description: '微信安全支付' },
      { id: 'alipay', name: '支付宝', icon: 'https://via.placeholder.com/40', description: '支付宝安全快捷支付' },
      { id: 'balance', name: '余额支付', icon: 'https://via.placeholder.com/40', description: '使用账户余额支付' },
      { id: 'unionpay', name: '银联支付', icon: 'https://via.placeholder.com/40', description: '银联在线支付' }
    ];
    
    // 支付状态
    this.paymentStatus = {
      PENDING: 'pending',    // 待支付
      SUCCESS: 'success',    // 支付成功
      FAILED: 'failed',      // 支付失败
      CLOSED: 'closed',      // 已关闭
      REFUNDING: 'refunding', // 退款中
      REFUNDED: 'refunded'   // 已退款
    };
  }
  
  /**
   * 获取支持的支付渠道
   * @returns {Array} 支付渠道列表
   */
  getPaymentChannels() {
    return this.paymentChannels;
  }
  
  /**
   * 创建支付订单
   * @param {Object} params 支付参数
   * @param {String} params.orderId 订单ID
   * @param {Number} params.amount 支付金额
   * @param {String} params.channel 支付渠道
   * @param {String} params.subject 支付标题
   * @param {String} params.body 支付描述
   * @returns {Promise} 支付结果
   */
  async createPayment(params) {
    try {
      // 加密敏感支付信息
      const encryptedParams = {
        ...params,
        amount: securityUtils.encrypt(params.amount.toString())
      };
      
      const response = await secureRequest.post(`${this.baseUrl}/payment/create`, encryptedParams);
      securityAudit.log('创建支付订单', true, { orderId: params.orderId });
      
      return response.data;
    } catch (error) {
      securityAudit.log('创建支付订单失败', false, error);
      throw error;
    }
  }
  
  /**
   * 查询支付状态
   * @param {String} paymentId 支付ID
   * @returns {Promise} 支付状态
   */
  async queryPaymentStatus(paymentId) {
    try {
      const response = await secureRequest.get(`${this.baseUrl}/payment/query`, {
        params: { paymentId }
      });
      securityAudit.log('查询支付状态', true, { paymentId });
      
      return response.data;
    } catch (error) {
      securityAudit.log('查询支付状态失败', false, error);
      throw error;
    }
  }
  
  /**
   * 关闭支付订单
   * @param {String} paymentId 支付ID
   * @returns {Promise} 关闭结果
   */
  async closePayment(paymentId) {
    try {
      const response = await secureRequest.post(`${this.baseUrl}/payment/close`, {
        paymentId
      });
      securityAudit.log('关闭支付订单', true, { paymentId });
      
      return response.data;
    } catch (error) {
      securityAudit.log('关闭支付订单失败', false, error);
      throw error;
    }
  }
  
  /**
   * 申请退款
   * @param {Object} params 退款参数
   * @param {String} params.paymentId 支付ID
   * @param {String} params.orderId 订单ID
   * @param {Number} params.amount 退款金额
   * @param {String} params.reason 退款原因
   * @returns {Promise} 退款结果
   */
  async refund(params) {
    try {
      // 加密敏感退款信息
      const encryptedParams = {
        ...params,
        amount: securityUtils.encrypt(params.amount.toString())
      };
      
      const response = await secureRequest.post(`${this.baseUrl}/payment/refund`, encryptedParams);
      securityAudit.log('申请退款', true, { orderId: params.orderId });
      
      return response.data;
    } catch (error) {
      securityAudit.log('申请退款失败', false, error);
      throw error;
    }
  }
  
  /**
   * 查询退款状态
   * @param {String} refundId 退款ID
   * @returns {Promise} 退款状态
   */
  async queryRefundStatus(refundId) {
    try {
      const response = await secureRequest.get(`${this.baseUrl}/payment/refund/query`, {
        params: { refundId }
      });
      securityAudit.log('查询退款状态', true, { refundId });
      
      return response.data;
    } catch (error) {
      securityAudit.log('查询退款状态失败', false, error);
      throw error;
    }
  }
  
  /**
   * 获取支付记录
   * @param {Object} params 查询参数
   * @param {Number} params.page 页码
   * @param {Number} params.pageSize 每页数量
   * @param {String} params.status 支付状态
   * @returns {Promise} 支付记录
   */
  async getPaymentRecords(params) {
    try {
      const response = await secureRequest.get(`${this.baseUrl}/payment/records`, {
        params
      });
      securityAudit.log('获取支付记录', true);
      
      return response.data;
    } catch (error) {
      securityAudit.log('获取支付记录失败', false, error);
      throw error;
    }
  }
  
  /**
   * 获取支付详情
   * @param {String} paymentId 支付ID
   * @returns {Promise} 支付详情
   */
  async getPaymentDetail(paymentId) {
    try {
      const response = await secureRequest.get(`${this.baseUrl}/payment/detail`, {
        params: { paymentId }
      });
      securityAudit.log('获取支付详情', true, { paymentId });
      
      return response.data;
    } catch (error) {
      securityAudit.log('获取支付详情失败', false, error);
      throw error;
    }
  }
  
  /**
   * 微信支付
   * @param {Object} params 支付参数
   * @returns {Promise} 支付结果
   */
  async wechatPay(params) {
    try {
      const payParams = {
        ...params,
        channel: 'wechat'
      };
      
      const payResult = await this.createPayment(payParams);
      
      if (payResult.success) {
        const payData = payResult.data;
        
        // 调用微信支付
        return new Promise((resolve, reject) => {
          uni.requestPayment({
            provider: 'wxpay',
            timeStamp: payData.timeStamp,
            nonceStr: payData.nonceStr,
            package: payData.package,
            signType: payData.signType,
            paySign: payData.paySign,
            success: (res) => {
              securityAudit.log('微信支付成功', true, { orderId: params.orderId });
              resolve({ success: true, data: res });
            },
            fail: (err) => {
              securityAudit.log('微信支付失败', false, err);
              reject({ success: false, message: err.errMsg || '支付失败' });
            }
          });
        });
      } else {
        return payResult;
      }
    } catch (error) {
      securityAudit.log('微信支付异常', false, error);
      throw error;
    }
  }
  
  /**
   * 支付宝支付
   * @param {Object} params 支付参数
   * @returns {Promise} 支付结果
   */
  async alipay(params) {
    try {
      const payParams = {
        ...params,
        channel: 'alipay'
      };
      
      const payResult = await this.createPayment(payParams);
      
      if (payResult.success) {
        const payData = payResult.data;
        
        // 调用支付宝支付
        return new Promise((resolve, reject) => {
          uni.requestPayment({
            provider: 'alipay',
            orderInfo: payData.orderInfo,
            success: (res) => {
              securityAudit.log('支付宝支付成功', true, { orderId: params.orderId });
              resolve({ success: true, data: res });
            },
            fail: (err) => {
              securityAudit.log('支付宝支付失败', false, err);
              reject({ success: false, message: err.errMsg || '支付失败' });
            }
          });
        });
      } else {
        return payResult;
      }
    } catch (error) {
      securityAudit.log('支付宝支付异常', false, error);
      throw error;
    }
  }
  
  /**
   * 余额支付
   * @param {Object} params 支付参数
   * @returns {Promise} 支付结果
   */
  async balancePay(params) {
    try {
      const payParams = {
        ...params,
        channel: 'balance'
      };
      
      const payResult = await this.createPayment(payParams);
      securityAudit.log('余额支付', true, { orderId: params.orderId });
      
      return payResult;
    } catch (error) {
      securityAudit.log('余额支付异常', false, error);
      throw error;
    }
  }
  
  /**
   * 银联支付
   * @param {Object} params 支付参数
   * @returns {Promise} 支付结果
   */
  async unionPay(params) {
    try {
      const payParams = {
        ...params,
        channel: 'unionpay'
      };
      
      const payResult = await this.createPayment(payParams);
      
      if (payResult.success) {
        const payData = payResult.data;
        
        // 调用银联支付
        return new Promise((resolve, reject) => {
          uni.navigateTo({
            url: `/pages/payment/unionpay?tn=${payData.tn}&orderId=${params.orderId}`,
            events: {
              paymentResult: (result) => {
                if (result.success) {
                  securityAudit.log('银联支付成功', true, { orderId: params.orderId });
                  resolve({ success: true, data: result });
                } else {
                  securityAudit.log('银联支付失败', false, result);
                  reject({ success: false, message: result.message || '支付失败' });
                }
              }
            }
          });
        });
      } else {
        return payResult;
      }
    } catch (error) {
      securityAudit.log('银联支付异常', false, error);
      throw error;
    }
  }
  
  /**
   * 根据渠道执行支付
   * @param {Object} params 支付参数
   * @param {String} channel 支付渠道
   * @returns {Promise} 支付结果
   */
  async payByChannel(params, channel) {
    switch (channel) {
      case 'wechat':
        return this.wechatPay(params);
      case 'alipay':
        return this.alipay(params);
      case 'balance':
        return this.balancePay(params);
      case 'unionpay':
        return this.unionPay(params);
      default:
        throw new Error('不支持的支付渠道');
    }
  }
}

// 导出单例
const paymentService = new PaymentService();
export default paymentService; 