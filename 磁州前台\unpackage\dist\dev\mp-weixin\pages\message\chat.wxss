/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-013fa921, html.data-v-013fa921, #app.data-v-013fa921, .index-container.data-v-013fa921 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.chat-container.data-v-013fa921 {
  min-height: 100vh;
  background-color: #f9f9f9;
  background-image: linear-gradient(rgba(226, 226, 226, 0.3) 1px, transparent 1px), linear-gradient(90deg, rgba(226, 226, 226, 0.3) 1px, transparent 1px);
  background-size: 20px 20px;
  display: flex;
  flex-direction: column;
  padding-top: 0;
  box-sizing: border-box;
  position: relative;
}
.chat-container.has-more-panel.data-v-013fa921 {
  padding-bottom: 400rpx;
}

/* 自定义导航栏 */
.custom-navbar.data-v-013fa921 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(110rpx + var(--status-bar-height));
  padding-top: var(--status-bar-height);
  background: linear-gradient(135deg, #0A84FF, #0040DD);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--status-bar-height) 30rpx 0;
  box-sizing: border-box;
  width: 100%;
}
.navbar-title.data-v-013fa921 {
  position: absolute;
  left: 0;
  right: 0;
  color: #FFFFFF;
  font-size: 36rpx;
  font-weight: 700;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  letter-spacing: 1rpx;
}
.navbar-left.data-v-013fa921, .navbar-right.data-v-013fa921 {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1000;
}
.back-icon.data-v-013fa921 {
  width: 44rpx;
  height: 44rpx;
  filter: brightness(0) invert(1);
}
.safe-area-top.data-v-013fa921 {
  height: var(--status-bar-height);
  width: 100%;
  background: linear-gradient(135deg, #0A84FF, #0040DD);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 998;
}

/* 聊天内容区域 */
.chat-content.data-v-013fa921 {
  height: 100vh;
  padding: 20rpx 30rpx;
  padding-top: calc(110rpx + var(--status-bar-height) + 20rpx);
  padding-bottom: 124rpx;
  box-sizing: border-box;
  width: 100%;
  background-color: #f9f9f9;
}
.load-more.data-v-013fa921 {
  text-align: center;
  font-size: 24rpx;
  color: #999999;
  padding: 20rpx 0;
}
.message-list.data-v-013fa921 {
  padding-bottom: 20rpx;
  width: 100%;
}
.message-item.data-v-013fa921 {
  display: flex;
  margin-bottom: 30rpx;
  width: 100%;
  animation: fadeIn-013fa921 0.3s ease-out;
}
.message-item.self.data-v-013fa921 {
  flex-direction: row-reverse;
  justify-content: flex-start;
}
@keyframes fadeIn-013fa921 {
from {
    opacity: 0;
    transform: translateY(10px);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
.time-divider.data-v-013fa921 {
  width: 100%;
  text-align: center;
  margin: 20rpx 0;
  position: relative;
  left: 0;
  right: 0;
}
.time-divider text.data-v-013fa921 {
  font-size: 24rpx;
  color: #8e8e93;
  background-color: rgba(142, 142, 147, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
}
.avatar-container.data-v-013fa921 {
  margin: 0 20rpx;
  flex-shrink: 0;
  width: 80rpx;
  height: 80rpx;
}
.avatar.data-v-013fa921 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}
.message-bubble.data-v-013fa921 {
  max-width: 55%;
  padding: 20rpx 24rpx;
  border-radius: 20rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
  word-break: break-all;
  word-wrap: break-word;
  flex-shrink: 1;
}
.message-bubble.self.data-v-013fa921 {
  background-color: #007AFF;
  color: #ffffff;
  margin-right: 16rpx;
}
.message-bubble.data-v-013fa921::before {
  content: "";
  position: absolute;
  top: 20rpx;
  left: -16rpx;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 8rpx 16rpx 8rpx 0;
  border-color: transparent #ffffff transparent transparent;
}
.message-bubble.self.data-v-013fa921::before {
  left: auto;
  right: -16rpx;
  border-width: 8rpx 0 8rpx 16rpx;
  border-color: transparent transparent transparent #007AFF;
}
.message-text.data-v-013fa921 {
  font-size: 32rpx;
  line-height: 1.5;
  word-break: break-all;
  word-wrap: break-word;
  white-space: normal;
  display: inline-block;
  max-width: 100%;
}

/* 图片消息样式 */
.image-message.data-v-013fa921 {
  padding: 0;
  border-radius: 20rpx;
  overflow: hidden;
}
.message-image.data-v-013fa921 {
  width: 400rpx;
  max-width: 400rpx;
  border-radius: 20rpx;
}

/* 定位消息样式 */
.location-message.data-v-013fa921 {
  display: flex;
  align-items: center;
  padding: 20rpx;
  width: 400rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}
.location-message .location-icon.data-v-013fa921 {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
}
.location-message .location-icon image.data-v-013fa921 {
  width: 100%;
  height: 100%;
}
.location-message .location-info.data-v-013fa921 {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.location-message .location-name.data-v-013fa921 {
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  color: #333333;
}
.location-message .location-address.data-v-013fa921 {
  font-size: 24rpx;
  color: #8e8e93;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.location-message .location-arrow.data-v-013fa921 {
  width: 24rpx;
  height: 24rpx;
  margin-left: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.location-message .arrow-icon.data-v-013fa921 {
  font-size: 24rpx;
  color: #8e8e93;
}

/* 输入区域 */
.input-area.data-v-013fa921 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12rpx 20rpx;
  padding-bottom: calc(12rpx + env(safe-area-inset-bottom));
  background-color: rgba(249, 249, 249, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  z-index: 99;
}
.input-box.data-v-013fa921 {
  flex: 1;
  background-color: #f2f2f7;
  border-radius: 36rpx;
  padding: 10rpx 20rpx;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
  height: 80rpx;
  display: flex;
  align-items: center;
}
.input-field.data-v-013fa921 {
  width: 100%;
  height: 60rpx;
  font-size: 30rpx;
  line-height: 1.4;
}
.action-btn.data-v-013fa921 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f2f2f7;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  transition: all 0.15s ease;
  position: relative;
}
.action-btn.data-v-013fa921:active {
  transform: scale(0.9);
  background-color: #e0e0e0;
}
.action-btn.has-text.data-v-013fa921 {
  background-color: #0A84FF;
}
.send-text.data-v-013fa921 {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 500;
}
.add-icon.data-v-013fa921 {
  width: 30rpx;
  height: 30rpx;
}
.more-icon.data-v-013fa921 {
  width: 76rpx;
  height: 76rpx;
  border-radius: 16rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}
.panel-icon-image.data-v-013fa921 {
  width: 30rpx;
  height: 30rpx;
}
.safe-area-bottom.data-v-013fa921 {
  height: env(safe-area-inset-bottom);
  width: 100%;
  background-color: #ffffff;
}

/* 抽屉式扩展功能面板 */
.more-panel-mask.data-v-013fa921 {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 90;
}
.more-panel-drawer.data-v-013fa921 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 300rpx;
  background-color: #ffffff;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}
.more-panel-drawer.show.data-v-013fa921 {
  transform: translateY(0);
}
.more-panel-content.data-v-013fa921 {
  padding: 20rpx;
  height: 100%;
  box-sizing: border-box;
}
.more-panel-handle.data-v-013fa921 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.handle-line.data-v-013fa921 {
  width: 60rpx;
  height: 6rpx;
  background-color: #e0e0e0;
  border-radius: 3rpx;
}
.more-grid.data-v-013fa921 {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx 0;
}
.more-item.data-v-013fa921 {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.more-text.data-v-013fa921 {
  font-size: 22rpx;
  color: #333333;
  font-weight: 400;
}
.location-icon.data-v-013fa921 {
  background-color: #07C160;
}
.location-icon image.data-v-013fa921 {
  filter: brightness(0) invert(1);
}
.image-icon.data-v-013fa921 {
  background-color: #1677FF;
}
.image-icon image.data-v-013fa921 {
  filter: brightness(0) invert(1);
}
.file-icon.data-v-013fa921 {
  background-color: #FA5151;
}
.file-icon image.data-v-013fa921 {
  filter: brightness(0) invert(1);
}
.contact-icon.data-v-013fa921 {
  background-color: #6467F0;
}
.contact-icon image.data-v-013fa921 {
  filter: brightness(0) invert(1);
}