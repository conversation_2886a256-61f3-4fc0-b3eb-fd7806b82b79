<template>
  <view class="levels-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">分销等级设置</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 等级开关 -->
    <view class="switch-card">
      <view class="switch-item">
        <view class="switch-label">
          <text class="label-text">启用分销等级</text>
          <text class="label-desc">开启后，分销员将根据业绩自动升级</text>
        </view>
        <switch 
          :checked="settings.enableLevels" 
          color="#6B0FBE" 
          @change="toggleLevels"
        />
      </view>
    </view>
    
    <!-- 等级列表 -->
    <view class="levels-list" v-if="settings.enableLevels">
      <view 
        v-for="(level, index) in levels" 
        :key="index" 
        class="level-card"
      >
        <view class="level-header">
          <view class="level-title">
            <text class="level-name">{{level.name}}</text>
            <text class="level-tag" :style="{ backgroundColor: level.color }">{{level.tag}}</text>
      </view>
          <view class="level-actions">
            <view class="edit-icon" @click="editLevel(index)"></view>
            <view class="delete-icon" v-if="index > 0" @click="deleteLevel(index)"></view>
        </view>
      </view>
      
        <view class="level-content">
          <view class="level-item">
            <text class="item-label">升级条件</text>
            <text class="item-value">{{getUpgradeText(level)}}</text>
    </view>
    
          <view class="level-item">
            <text class="item-label">佣金比例</text>
            <text class="item-value">一级：{{level.commissionRates.level1}}% / 二级：{{level.commissionRates.level2}}%</text>
      </view>
      
          <view class="level-item">
            <text class="item-label">特权说明</text>
            <text class="item-value">{{level.benefits || '无特殊权益'}}</text>
        </view>
        </view>
      </view>
      
      <!-- 添加等级按钮 -->
      <view class="add-level" @click="addLevel">
        <view class="add-icon"></view>
        <text class="add-text">添加等级</text>
      </view>
    </view>
    
    <!-- 等级编辑弹窗 -->
    <view class="level-modal" v-if="showLevelModal">
      <view class="modal-mask" @click="closeLevelModal"></view>
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">{{isEditMode ? '编辑等级' : '添加等级'}}</text>
          <view class="close-icon" @click="closeLevelModal"></view>
        </view>
        
        <view class="form-content">
          <view class="form-item">
            <text class="form-label">等级名称</text>
            <input class="form-input" type="text" v-model="formData.name" placeholder="请输入等级名称" />
          </view>
          
          <view class="form-item">
            <text class="form-label">等级标签</text>
            <input class="form-input" type="text" v-model="formData.tag" placeholder="请输入等级标签" />
          </view>
          
          <view class="form-item">
            <text class="form-label">标签颜色</text>
            <view class="color-picker">
              <view 
                v-for="(color, index) in colorOptions" 
                :key="index" 
                class="color-option" 
                :style="{ backgroundColor: color }"
                :class="{ 'active': formData.color === color }"
                @click="formData.color = color"
              ></view>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">升级条件</text>
            <view class="condition-type">
              <view 
                class="type-option" 
                :class="{ 'active': formData.upgradeType === 'sales' }"
                @click="formData.upgradeType = 'sales'"
              >
                <text>销售额</text>
              </view>
              <view 
                class="type-option" 
                :class="{ 'active': formData.upgradeType === 'orders' }"
                @click="formData.upgradeType = 'orders'"
              >
                <text>订单数</text>
              </view>
            </view>
            <input 
              class="form-input" 
              type="number" 
              v-model="formData.upgradeValue" 
              :placeholder="`请输入最低${formData.upgradeType === 'sales' ? '销售额' : '订单数'}`" 
            />
          </view>
          
          <view class="form-item">
            <text class="form-label">一级佣金比例 (%)</text>
            <input class="form-input" type="digit" v-model="formData.commissionRates.level1" placeholder="请输入一级佣金比例" />
          </view>
          
          <view class="form-item">
            <text class="form-label">二级佣金比例 (%)</text>
            <input class="form-input" type="digit" v-model="formData.commissionRates.level2" placeholder="请输入二级佣金比例" />
          </view>
          
          <view class="form-item">
            <text class="form-label">特权说明</text>
            <textarea class="form-textarea" v-model="formData.benefits" placeholder="请输入该等级特有的权益说明" />
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="cancel-btn" @click="closeLevelModal">取消</button>
          <button class="submit-btn" :disabled="!canSubmit" :class="{ 'disabled': !canSubmit }" @click="submitLevel">确定</button>
        </view>
      </view>
    </view>
    
    <!-- 保存按钮 -->
    <view class="save-section" v-if="settings.enableLevels">
      <button class="save-btn" @click="saveSettings">保存设置</button>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import distributionService from '@/utils/distributionService';

// 等级设置
const settings = reactive({
  enableLevels: true,
  autoUpgrade: true,
  downgradeEnable: false,
  upgradeCycle: 'monthly'
});

// 等级列表
const levels = ref([
  {
    id: 1,
    name: '普通分销员',
    tag: '初级',
    color: '#67C23A',
    upgradeType: 'sales',
    upgradeValue: 0,
    commissionRates: {
      level1: 10,
      level2: 5
    },
    benefits: '基础分销权益'
  },
  {
    id: 2,
    name: '高级分销员',
    tag: '高级',
    color: '#409EFF',
    upgradeType: 'sales',
    upgradeValue: 1000,
    commissionRates: {
      level1: 15,
      level2: 8
    },
    benefits: '专属活动优先参与权'
  },
  {
    id: 3,
    name: '金牌分销员',
    tag: '金牌',
    color: '#E6A23C',
    upgradeType: 'sales',
    upgradeValue: 5000,
    commissionRates: {
      level1: 20,
      level2: 10
    },
    benefits: '专属客服、活动优先参与权'
  }
]);

// 颜色选项
const colorOptions = [
  '#67C23A', '#409EFF', '#E6A23C', '#F56C6C', '#909399', 
  '#6B0FBE', '#FF9500', '#00C58E', '#1989FA', '#FF5722'
];

// 等级编辑弹窗
const showLevelModal = ref(false);

// 是否是编辑模式
const isEditMode = ref(false);

// 当前编辑的等级索引
const currentLevelIndex = ref(-1);

// 表单数据
const formData = reactive({
  name: '',
  tag: '',
  color: '#67C23A',
  upgradeType: 'sales',
  upgradeValue: 0,
  commissionRates: {
    level1: 0,
    level2: 0
  },
  benefits: ''
});

// 是否可以提交
const canSubmit = computed(() => {
  return formData.name.trim() !== '' && 
         formData.tag.trim() !== '' && 
         formData.upgradeValue !== '' &&
         formData.commissionRates.level1 !== '' &&
         formData.commissionRates.level2 !== '';
});

// 页面加载
onMounted(async () => {
  // 获取分销等级设置
  await getDistributionLevels();
});

// 获取分销等级设置
const getDistributionLevels = async () => {
  try {
    const result = await distributionService.getDistributionLevels();
    
    if (result) {
      // 更新设置
      Object.assign(settings, result.settings);
      
      // 更新等级列表
      if (result.levels && result.levels.length > 0) {
        levels.value = result.levels;
      }
    }
  } catch (error) {
    console.error('获取分销等级设置失败', error);
    uni.showToast({
      title: '获取分销等级设置失败',
      icon: 'none'
    });
  }
};

// 切换等级开关
const toggleLevels = (e) => {
  settings.enableLevels = e.detail.value;
};

// 获取升级条件文本
const getUpgradeText = (level) => {
  if (level.upgradeType === 'sales') {
    return `累计销售额 ≥ ${level.upgradeValue}元`;
  } else {
    return `累计订单数 ≥ ${level.upgradeValue}单`;
  }
};

// 添加等级
const addLevel = () => {
  isEditMode.value = false;
  currentLevelIndex.value = -1;
  
  // 重置表单数据
  formData.name = '';
  formData.tag = '';
  formData.color = '#67C23A';
  formData.upgradeType = 'sales';
  formData.upgradeValue = '';
  formData.commissionRates.level1 = '';
  formData.commissionRates.level2 = '';
  formData.benefits = '';
  
  showLevelModal.value = true;
};

// 编辑等级
const editLevel = (index) => {
  isEditMode.value = true;
  currentLevelIndex.value = index;
  
  const level = levels.value[index];
  
  // 填充表单数据
  formData.name = level.name;
  formData.tag = level.tag;
  formData.color = level.color;
  formData.upgradeType = level.upgradeType;
  formData.upgradeValue = level.upgradeValue;
  formData.commissionRates.level1 = level.commissionRates.level1;
  formData.commissionRates.level2 = level.commissionRates.level2;
  formData.benefits = level.benefits;
  
  showLevelModal.value = true;
};

// 删除等级
const deleteLevel = (index) => {
  uni.showModal({
    title: '删除等级',
    content: '确定要删除该等级吗？删除后无法恢复。',
    success: (res) => {
      if (res.confirm) {
        levels.value.splice(index, 1);
      }
    }
  });
};

// 关闭等级编辑弹窗
const closeLevelModal = () => {
  showLevelModal.value = false;
};

// 提交等级
const submitLevel = () => {
  if (!canSubmit.value) return;
  
  const newLevel = {
    name: formData.name,
    tag: formData.tag,
    color: formData.color,
    upgradeType: formData.upgradeType,
    upgradeValue: parseFloat(formData.upgradeValue),
    commissionRates: {
      level1: parseFloat(formData.commissionRates.level1),
      level2: parseFloat(formData.commissionRates.level2)
    },
    benefits: formData.benefits
  };
  
  if (isEditMode.value) {
    // 编辑模式
    levels.value[currentLevelIndex.value] = {
      ...levels.value[currentLevelIndex.value],
      ...newLevel
    };
  } else {
    // 添加模式
    newLevel.id = levels.value.length > 0 ? Math.max(...levels.value.map(l => l.id)) + 1 : 1;
    levels.value.push(newLevel);
  }
  
  closeLevelModal();
};

// 保存设置
const saveSettings = async () => {
  try {
  uni.showLoading({
      title: '保存中...',
      mask: true
    });
    
    const result = await distributionService.saveDistributionLevels({
      settings,
      levels: levels.value
    });
    
    uni.hideLoading();
    
    if (result.success) {
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    });
    } else {
      uni.showModal({
        title: '保存失败',
        content: result.message || '请稍后再试',
        showCancel: false
      });
    }
  } catch (error) {
    uni.hideLoading();
    console.error('保存分销等级设置失败', error);
    uni.showToast({
      title: '保存失败',
      icon: 'none'
    });
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 显示帮助
const showHelp = () => {
  uni.showModal({
    title: '分销等级帮助',
    content: '分销等级系统可以根据分销员的业绩设置不同的佣金比例和特权，激励分销员提升业绩。系统会根据设置的条件自动升级分销员等级。',
    showCancel: false
  });
};
</script>

<style lang="scss">
.levels-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 88rpx 32rpx 30rpx;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);
}

.navbar-back {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24rpx;
  height: 24rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.navbar-right {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

/* 开关卡片 */
.switch-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.switch-label {
  flex: 1;
}

.label-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.label-desc {
  font-size: 24rpx;
  color: #999;
}

/* 等级列表 */
.levels-list {
  margin: 30rpx;
}

.level-card {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.level-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.level-title {
  display: flex;
  align-items: center;
}

.level-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-right: 16rpx;
}

.level-tag {
  font-size: 24rpx;
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.level-actions {
  display: flex;
  align-items: center;
}

.edit-icon,
.delete-icon {
  width: 40rpx;
  height: 40rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-left: 20rpx;
}

.edit-icon {
  background-color: #409EFF;
  border-radius: 50%;
  position: relative;
}

.edit-icon::before,
.edit-icon::after {
  content: '';
  position: absolute;
  background-color: white;
}

.edit-icon::before {
  width: 16rpx;
  height: 2rpx;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.edit-icon::after {
  width: 2rpx;
  height: 16rpx;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.delete-icon {
  background-color: #F56C6C;
  border-radius: 50%;
  position: relative;
}

.delete-icon::before {
  content: '';
  position: absolute;
  width: 16rpx;
  height: 2rpx;
  background-color: white;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
}

.delete-icon::after {
  content: '';
  position: absolute;
  width: 16rpx;
  height: 2rpx;
  background-color: white;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
}

.level-content {
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.level-item {
  margin-bottom: 16rpx;
}

.level-item:last-child {
  margin-bottom: 0;
}

.item-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}

.item-value {
  font-size: 26rpx;
  color: #333;
}

/* 添加等级 */
.add-level {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #ddd;
}

.add-icon {
  width: 40rpx;
  height: 40rpx;
  background-color: #6B0FBE;
  border-radius: 50%;
  position: relative;
  margin-right: 16rpx;
}

.add-icon::before,
.add-icon::after {
  content: '';
  position: absolute;
  background-color: white;
}

.add-icon::before {
  width: 20rpx;
  height: 2rpx;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.add-icon::after {
  width: 2rpx;
  height: 20rpx;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.add-text {
  font-size: 28rpx;
  color: #6B0FBE;
}

/* 等级编辑弹窗 */
.level-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-height: 90%;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-icon {
  width: 40rpx;
  height: 40rpx;
  position: relative;
}

.close-icon::before,
.close-icon::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 32rpx;
  height: 2rpx;
  background-color: #999;
}

.close-icon::before {
  transform: translate(-50%, -50%) rotate(45deg);
}

.close-icon::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.form-content {
  margin-bottom: 30rpx;
}

.form-item {
  margin-bottom: 20rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.form-textarea {
  width: 100%;
  height: 160rpx;
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.color-picker {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.color-option {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  margin: 10rpx;
  border: 2rpx solid transparent;
}

.color-option.active {
  border-color: #333;
  box-shadow: 0 0 0 4rpx rgba(0, 0, 0, 0.1);
}

.condition-type {
  display: flex;
  margin-bottom: 16rpx;
}

.type-option {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  background: #F5F7FA;
  border-radius: 10rpx;
  margin-right: 20rpx;
}

.type-option:last-child {
  margin-right: 0;
}

.type-option.active {
  background: #6B0FBE;
  color: #fff;
}

.modal-footer {
  display: flex;
  justify-content: space-between;
}

.cancel-btn,
.submit-btn {
  width: 48%;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.cancel-btn {
  background: #F5F7FA;
  color: #666;
}

.submit-btn {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
}

.submit-btn.disabled {
  background: #cccccc;
  color: #ffffff;
}

/* 保存按钮 */
.save-section {
  margin: 30rpx;
}

.save-btn {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 32rpx;
  padding: 20rpx 0;
  line-height: 1.5;
  width: 100%;
}
</style>