{"version": 3, "sources": ["../../src/core.js"], "names": ["SIGNALS", "name", "number", "action", "description", "standard", "forced"], "mappings": ";;AAEO,KAAMA,CAAAA,OAAO,CAAG;AACrB;AACEC,IAAI,CAAE,QADR;AAEEC,MAAM,CAAE,CAFV;AAGEC,MAAM,CAAE,WAHV;AAIEC,WAAW,CAAE,iBAJf;AAKEC,QAAQ,CAAE,OALZ,CADqB;;AAQrB;AACEJ,IAAI,CAAE,QADR;AAEEC,MAAM,CAAE,CAFV;AAGEC,MAAM,CAAE,WAHV;AAIEC,WAAW,CAAE,+BAJf;AAKEC,QAAQ,CAAE,MALZ,CARqB;;AAerB;AACEJ,IAAI,CAAE,SADR;AAEEC,MAAM,CAAE,CAFV;AAGEC,MAAM,CAAE,MAHV;AAIEC,WAAW,CAAE,gCAJf;AAKEC,QAAQ,CAAE,OALZ,CAfqB;;AAsBrB;AACEJ,IAAI,CAAE,QADR;AAEEC,MAAM,CAAE,CAFV;AAGEC,MAAM,CAAE,MAHV;AAIEC,WAAW,CAAE,6BAJf;AAKEC,QAAQ,CAAE,MALZ,CAtBqB;;AA6BrB;AACEJ,IAAI,CAAE,SADR;AAEEC,MAAM,CAAE,CAFV;AAGEC,MAAM,CAAE,MAHV;AAIEC,WAAW,CAAE,qBAJf;AAKEC,QAAQ,CAAE,OALZ,CA7BqB;;AAoCrB;AACEJ,IAAI,CAAE,SADR;AAEEC,MAAM,CAAE,CAFV;AAGEC,MAAM,CAAE,MAHV;AAIEC,WAAW,CAAE,SAJf;AAKEC,QAAQ,CAAE,MALZ,CApCqB;;AA2CrB;AACEJ,IAAI,CAAE,QADR;AAEEC,MAAM,CAAE,CAFV;AAGEC,MAAM,CAAE,MAHV;AAIEC,WAAW,CAAE,SAJf;AAKEC,QAAQ,CAAE,KALZ,CA3CqB;;AAkDrB;AACEJ,IAAI,CAAE,QADR;AAEEC,MAAM,CAAE,CAFV;AAGEC,MAAM,CAAE,MAHV;AAIEC,WAAW;AACT,mEALJ;AAMEC,QAAQ,CAAE,KANZ,CAlDqB;;AA0DrB;AACEJ,IAAI,CAAE,QADR;AAEEC,MAAM,CAAE,CAFV;AAGEC,MAAM,CAAE,WAHV;AAIEC,WAAW,CAAE,mDAJf;AAKEC,QAAQ,CAAE,OALZ,CA1DqB;;AAiErB;AACEJ,IAAI,CAAE,QADR;AAEEC,MAAM,CAAE,CAFV;AAGEC,MAAM,CAAE,MAHV;AAIEC,WAAW,CAAE,iCAJf;AAKEC,QAAQ,CAAE,MALZ,CAjEqB;;AAwErB;AACEJ,IAAI,CAAE,SADR;AAEEC,MAAM,CAAE,CAFV;AAGEC,MAAM,CAAE,WAHV;AAIEC,WAAW,CAAE,oBAJf;AAKEC,QAAQ,CAAE,OALZ;AAMEC,MAAM,CAAE,IANV,CAxEqB;;AAgFrB;AACEL,IAAI,CAAE,SADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,WAHV;AAIEC,WAAW,CAAE,6BAJf;AAKEC,QAAQ,CAAE,OALZ,CAhFqB;;AAuFrB;AACEJ,IAAI,CAAE,SADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,MAHV;AAIEC,WAAW,CAAE,oBAJf;AAKEC,QAAQ,CAAE,MALZ,CAvFqB;;AA8FrB;AACEJ,IAAI,CAAE,SADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,WAHV;AAIEC,WAAW,CAAE,6BAJf;AAKEC,QAAQ,CAAE,OALZ,CA9FqB;;AAqGrB;AACEJ,IAAI,CAAE,SADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,WAHV;AAIEC,WAAW,CAAE,uBAJf;AAKEC,QAAQ,CAAE,OALZ,CArGqB;;AA4GrB;AACEJ,IAAI,CAAE,SADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,WAHV;AAIEC,WAAW,CAAE,kBAJf;AAKEC,QAAQ,CAAE,OALZ,CA5GqB;;AAmHrB;AACEJ,IAAI,CAAE,SADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,WAHV;AAIEC,WAAW,CAAE,aAJf;AAKEC,QAAQ,CAAE,MALZ,CAnHqB;;AA0HrB;AACEJ,IAAI,CAAE,WADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,WAHV;AAIEC,WAAW,CAAE,8BAJf;AAKEC,QAAQ,CAAE,OALZ,CA1HqB;;AAiIrB;AACEJ,IAAI,CAAE,SADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,QAHV;AAIEC,WAAW,CAAE,8CAJf;AAKEC,QAAQ,CAAE,OALZ,CAjIqB;;AAwIrB;AACEJ,IAAI,CAAE,QADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,QAHV;AAIEC,WAAW,CAAE,8CAJf;AAKEC,QAAQ,CAAE,OALZ,CAxIqB;;AA+IrB;AACEJ,IAAI,CAAE,SADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,SAHV;AAIEC,WAAW,CAAE,UAJf;AAKEC,QAAQ,CAAE,OALZ;AAMEC,MAAM,CAAE,IANV,CA/IqB;;AAuJrB;AACEL,IAAI,CAAE,SADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,OAHV;AAIEC,WAAW,CAAE,QAJf;AAKEC,QAAQ,CAAE,OALZ;AAMEC,MAAM,CAAE,IANV,CAvJqB;;AA+JrB;AACEL,IAAI,CAAE,SADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,OAHV;AAIEC,WAAW,CAAE,oCAJf;AAKEC,QAAQ,CAAE,OALZ,CA/JqB;;AAsKrB;AACEJ,IAAI,CAAE,SADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,OAHV;AAIEC,WAAW,CAAE,+CAJf;AAKEC,QAAQ,CAAE,OALZ,CAtKqB;;AA6KrB;AACEJ,IAAI,CAAE,UADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,WAHV;AAIEC,WAAW,CAAE,mCAJf;AAKEC,QAAQ,CAAE,OALZ,CA7KqB;;AAoLrB;AACEJ,IAAI,CAAE,SADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,OAHV;AAIEC,WAAW,CAAE,oDAJf;AAKEC,QAAQ,CAAE,OALZ,CApLqB;;AA2LrB;AACEJ,IAAI,CAAE,QADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,QAHV;AAIEC,WAAW,CAAE,kCAJf;AAKEC,QAAQ,CAAE,KALZ,CA3LqB;;AAkMrB;AACEJ,IAAI,CAAE,SADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,MAHV;AAIEC,WAAW,CAAE,mBAJf;AAKEC,QAAQ,CAAE,KALZ,CAlMqB;;AAyMrB;AACEJ,IAAI,CAAE,SADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,MAHV;AAIEC,WAAW,CAAE,cAJf;AAKEC,QAAQ,CAAE,KALZ,CAzMqB;;AAgNrB;AACEJ,IAAI,CAAE,WADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,WAHV;AAIEC,WAAW,CAAE,kBAJf;AAKEC,QAAQ,CAAE,KALZ,CAhNqB;;AAuNrB;AACEJ,IAAI,CAAE,SADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,WAHV;AAIEC,WAAW,CAAE,kBAJf;AAKEC,QAAQ,CAAE,KALZ,CAvNqB;;AA8NrB;AACEJ,IAAI,CAAE,UADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,QAHV;AAIEC,WAAW,CAAE,8BAJf;AAKEC,QAAQ,CAAE,KALZ,CA9NqB;;AAqOrB;AACEJ,IAAI,CAAE,OADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,WAHV;AAIEC,WAAW,CAAE,kBAJf;AAKEC,QAAQ,CAAE,OALZ,CArOqB;;AA4OrB;AACEJ,IAAI,CAAE,SADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,WAHV;AAIEC,WAAW,CAAE,eAJf;AAKEC,QAAQ,CAAE,OALZ,CA5OqB;;AAmPrB;AACEJ,IAAI,CAAE,SADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,QAHV;AAIEC,WAAW,CAAE,iCAJf;AAKEC,QAAQ,CAAE,OALZ,CAnPqB;;AA0PrB;AACEJ,IAAI,CAAE,QADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,WAHV;AAIEC,WAAW,CAAE,6BAJf;AAKEC,QAAQ,CAAE,SALZ,CA1PqB;;AAiQrB;AACEJ,IAAI,CAAE,QADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,MAHV;AAIEC,WAAW,CAAE,qBAJf;AAKEC,QAAQ,CAAE,OALZ,CAjQqB;;AAwQrB;AACEJ,IAAI,CAAE,WADR;AAEEC,MAAM,CAAE,EAFV;AAGEC,MAAM,CAAE,WAHV;AAIEC,WAAW,CAAE,qBAJf;AAKEC,QAAQ,CAAE,OALZ,CAxQqB,CAAhB,C", "sourcesContent": ["/* eslint-disable max-lines */\n// List of known process signals with information about them\nexport const SIGNALS = [\n  {\n    name: '<PERSON>IGH<PERSON>',\n    number: 1,\n    action: 'terminate',\n    description: 'Terminal closed',\n    standard: 'posix',\n  },\n  {\n    name: 'SIGINT',\n    number: 2,\n    action: 'terminate',\n    description: 'User interruption with CTRL-C',\n    standard: 'ansi',\n  },\n  {\n    name: 'SIGQUIT',\n    number: 3,\n    action: 'core',\n    description: 'User interruption with CTRL-\\\\',\n    standard: 'posix',\n  },\n  {\n    name: 'SIGILL',\n    number: 4,\n    action: 'core',\n    description: 'Invalid machine instruction',\n    standard: 'ansi',\n  },\n  {\n    name: 'SIGTR<PERSON>',\n    number: 5,\n    action: 'core',\n    description: 'Debugger breakpoint',\n    standard: 'posix',\n  },\n  {\n    name: 'SIGABRT',\n    number: 6,\n    action: 'core',\n    description: 'Aborted',\n    standard: 'ansi',\n  },\n  {\n    name: 'SIGIOT',\n    number: 6,\n    action: 'core',\n    description: 'Aborted',\n    standard: 'bsd',\n  },\n  {\n    name: '<PERSON>IGB<PERSON>',\n    number: 7,\n    action: 'core',\n    description:\n      'Bus error due to misaligned, non-existing address or paging error',\n    standard: 'bsd',\n  },\n  {\n    name: 'SIGEMT',\n    number: 7,\n    action: 'terminate',\n    description: 'Command should be emulated but is not implemented',\n    standard: 'other',\n  },\n  {\n    name: 'SIGFPE',\n    number: 8,\n    action: 'core',\n    description: 'Floating point arithmetic error',\n    standard: 'ansi',\n  },\n  {\n    name: 'SIGKILL',\n    number: 9,\n    action: 'terminate',\n    description: 'Forced termination',\n    standard: 'posix',\n    forced: true,\n  },\n  {\n    name: 'SIGUSR1',\n    number: 10,\n    action: 'terminate',\n    description: 'Application-specific signal',\n    standard: 'posix',\n  },\n  {\n    name: 'SIGSEGV',\n    number: 11,\n    action: 'core',\n    description: 'Segmentation fault',\n    standard: 'ansi',\n  },\n  {\n    name: 'SIGUSR2',\n    number: 12,\n    action: 'terminate',\n    description: 'Application-specific signal',\n    standard: 'posix',\n  },\n  {\n    name: 'SIGPIPE',\n    number: 13,\n    action: 'terminate',\n    description: 'Broken pipe or socket',\n    standard: 'posix',\n  },\n  {\n    name: 'SIGALRM',\n    number: 14,\n    action: 'terminate',\n    description: 'Timeout or timer',\n    standard: 'posix',\n  },\n  {\n    name: 'SIGTERM',\n    number: 15,\n    action: 'terminate',\n    description: 'Termination',\n    standard: 'ansi',\n  },\n  {\n    name: 'SIGSTKFLT',\n    number: 16,\n    action: 'terminate',\n    description: 'Stack is empty or overflowed',\n    standard: 'other',\n  },\n  {\n    name: 'SIGCHLD',\n    number: 17,\n    action: 'ignore',\n    description: 'Child process terminated, paused or unpaused',\n    standard: 'posix',\n  },\n  {\n    name: 'SIGCLD',\n    number: 17,\n    action: 'ignore',\n    description: 'Child process terminated, paused or unpaused',\n    standard: 'other',\n  },\n  {\n    name: 'SIGCONT',\n    number: 18,\n    action: 'unpause',\n    description: 'Unpaused',\n    standard: 'posix',\n    forced: true,\n  },\n  {\n    name: 'SIGSTOP',\n    number: 19,\n    action: 'pause',\n    description: 'Paused',\n    standard: 'posix',\n    forced: true,\n  },\n  {\n    name: 'SIGTSTP',\n    number: 20,\n    action: 'pause',\n    description: 'Paused using CTRL-Z or \"suspend\"',\n    standard: 'posix',\n  },\n  {\n    name: 'SIGTTIN',\n    number: 21,\n    action: 'pause',\n    description: 'Background process cannot read terminal input',\n    standard: 'posix',\n  },\n  {\n    name: 'SIGBREAK',\n    number: 21,\n    action: 'terminate',\n    description: 'User interruption with CTRL-BREAK',\n    standard: 'other',\n  },\n  {\n    name: 'SIGTTOU',\n    number: 22,\n    action: 'pause',\n    description: 'Background process cannot write to terminal output',\n    standard: 'posix',\n  },\n  {\n    name: 'SIGURG',\n    number: 23,\n    action: 'ignore',\n    description: 'Socket received out-of-band data',\n    standard: 'bsd',\n  },\n  {\n    name: 'SIGXCPU',\n    number: 24,\n    action: 'core',\n    description: 'Process timed out',\n    standard: 'bsd',\n  },\n  {\n    name: 'SIGXFSZ',\n    number: 25,\n    action: 'core',\n    description: 'File too big',\n    standard: 'bsd',\n  },\n  {\n    name: 'SIGVTALRM',\n    number: 26,\n    action: 'terminate',\n    description: 'Timeout or timer',\n    standard: 'bsd',\n  },\n  {\n    name: 'SIGPROF',\n    number: 27,\n    action: 'terminate',\n    description: 'Timeout or timer',\n    standard: 'bsd',\n  },\n  {\n    name: 'SIGWINCH',\n    number: 28,\n    action: 'ignore',\n    description: 'Terminal window size changed',\n    standard: 'bsd',\n  },\n  {\n    name: 'SIGIO',\n    number: 29,\n    action: 'terminate',\n    description: 'I/O is available',\n    standard: 'other',\n  },\n  {\n    name: 'SIGPOLL',\n    number: 29,\n    action: 'terminate',\n    description: 'Watched event',\n    standard: 'other',\n  },\n  {\n    name: 'SIGINFO',\n    number: 29,\n    action: 'ignore',\n    description: 'Request for process information',\n    standard: 'other',\n  },\n  {\n    name: 'SIGPWR',\n    number: 30,\n    action: 'terminate',\n    description: 'Device running out of power',\n    standard: 'systemv',\n  },\n  {\n    name: 'SIGSYS',\n    number: 31,\n    action: 'core',\n    description: 'Invalid system call',\n    standard: 'other',\n  },\n  {\n    name: 'SIGUNUSED',\n    number: 31,\n    action: 'terminate',\n    description: 'Invalid system call',\n    standard: 'other',\n  },\n]\n/* eslint-enable max-lines */\n"], "file": "src/core.js"}