"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_polygon = common_vendor.resolveComponent("polygon");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_uni_popup = common_vendor.resolveComponent("uni-popup");
  (_component_path + _component_svg + _component_circle + _component_polygon + _component_line + _component_uni_popup)();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const currentCategory = common_vendor.ref(0);
    const currentFilter = common_vendor.ref("distance");
    const isRefreshing = common_vendor.ref(false);
    const isLoadingMore = common_vendor.ref(false);
    const shopsList = common_vendor.ref([]);
    const filterPopup = common_vendor.ref(null);
    const selectedCategories = common_vendor.ref([0]);
    const selectedRating = common_vendor.ref(0);
    const selectedServices = common_vendor.ref([]);
    const shopCategories = [
      { name: "全部", value: "all" },
      { name: "餐饮美食", value: "food" },
      { name: "休闲娱乐", value: "entertainment" },
      { name: "旅游住宿", value: "travel" },
      { name: "生活服务", value: "service" },
      { name: "文化教育", value: "education" },
      { name: "体育健身", value: "sports" },
      { name: "亲子活动", value: "family" }
    ];
    const allCategories = [
      { name: "全部", value: "all" },
      { name: "餐饮美食", value: "food" },
      { name: "休闲娱乐", value: "entertainment" },
      { name: "旅游住宿", value: "travel" },
      { name: "生活服务", value: "service" },
      { name: "文化教育", value: "education" },
      { name: "体育健身", value: "sports" },
      { name: "亲子活动", value: "family" },
      { name: "中餐", value: "chinese_food" },
      { name: "西餐", value: "western_food" },
      { name: "快餐", value: "fast_food" },
      { name: "咖啡甜品", value: "cafe" },
      { name: "酒吧", value: "bar" }
    ];
    const ratingOptions = [
      { label: "全部", value: 0 },
      { label: "4.5分以上", value: 4.5 },
      { label: "4.0分以上", value: 4 },
      { label: "3.5分以上", value: 3.5 }
    ];
    const serviceOptions = [
      { label: "免费WiFi", value: "wifi" },
      { label: "停车场", value: "parking" },
      { label: "无障碍设施", value: "accessibility" },
      { label: "提供发票", value: "invoice" },
      { label: "接受预订", value: "reservation" },
      { label: "会员特惠", value: "member" }
    ];
    const mockShops = [
      {
        id: "1001",
        name: "磁州文化体验馆",
        logo: "/static/demo/shop1.jpg",
        category: "文化教育",
        rating: 4.8,
        distance: "1.2km",
        monthlySales: 256,
        tags: ["文化体验", "非遗传承", "亲子互动", "团建活动"],
        promotion: {
          type: "满减",
          text: "满200减30"
        }
      },
      {
        id: "1002",
        name: "磁州美食城",
        logo: "/static/demo/shop2.jpg",
        category: "餐饮美食",
        rating: 4.5,
        distance: "0.8km",
        monthlySales: 512,
        tags: ["特色美食", "团购优惠", "家庭聚餐"],
        promotion: {
          type: "优惠",
          text: "新用户立减15元"
        }
      },
      {
        id: "1003",
        name: "磁州户外拓展中心",
        logo: "/static/demo/shop3.jpg",
        category: "体育健身",
        rating: 4.7,
        distance: "3.5km",
        monthlySales: 128,
        tags: ["户外活动", "团队建设", "亲子互动", "野外生存"],
        promotion: null
      },
      {
        id: "1004",
        name: "磁州亲子乐园",
        logo: "/static/demo/shop4.jpg",
        category: "亲子活动",
        rating: 4.6,
        distance: "2.1km",
        monthlySales: 320,
        tags: ["儿童娱乐", "亲子互动", "教育启蒙"],
        promotion: {
          type: "折扣",
          text: "周末8.5折"
        }
      },
      {
        id: "1005",
        name: "磁州艺术中心",
        logo: "/static/demo/shop5.jpg",
        category: "文化教育",
        rating: 4.9,
        distance: "1.5km",
        monthlySales: 96,
        tags: ["艺术展览", "文化讲座", "艺术培训"],
        promotion: {
          type: "活动",
          text: "新展开幕，门票半价"
        }
      }
    ];
    common_vendor.onMounted(() => {
      loadShops();
    });
    const loadShops = () => {
      shopsList.value = mockShops;
    };
    const getFilteredShops = () => {
      let result = [...shopsList.value];
      if (currentCategory.value !== 0) {
        const category = shopCategories[currentCategory.value].name;
        result = result.filter((shop) => shop.category === category);
      }
      switch (currentFilter.value) {
        case "distance":
          result.sort((a, b) => parseFloat(a.distance) - parseFloat(b.distance));
          break;
        case "rating":
          result.sort((a, b) => b.rating - a.rating);
          break;
        case "sales":
          result.sort((a, b) => b.monthlySales - a.monthlySales);
          break;
      }
      return result;
    };
    const switchCategory = (index) => {
      currentCategory.value = index;
    };
    const setFilter = (filter) => {
      currentFilter.value = filter;
    };
    const onRefresh = () => {
      isRefreshing.value = true;
      setTimeout(() => {
        loadShops();
        isRefreshing.value = false;
      }, 1e3);
    };
    const loadMore = () => {
      if (shopsList.value.length >= 10)
        return;
      isLoadingMore.value = true;
      setTimeout(() => {
        const newShops = [
          {
            id: "1006",
            name: "磁州咖啡馆",
            logo: "/static/demo/shop6.jpg",
            category: "休闲娱乐",
            rating: 4.4,
            distance: "0.5km",
            monthlySales: 420,
            tags: ["咖啡", "下午茶", "轻食"],
            promotion: {
              type: "优惠",
              text: "下午茶套餐8折"
            }
          },
          {
            id: "1007",
            name: "磁州民宿",
            logo: "/static/demo/shop7.jpg",
            category: "旅游住宿",
            rating: 4.8,
            distance: "4.2km",
            monthlySales: 85,
            tags: ["特色民宿", "乡村体验", "亲近自然"],
            promotion: {
              type: "满减",
              text: "连住3晚减100"
            }
          }
        ];
        shopsList.value = [...shopsList.value, ...newShops];
        isLoadingMore.value = false;
      }, 1500);
    };
    const viewShopDetail = (shop) => {
      navigateTo(`/subPackages/activity-showcase/pages/shops/detail?id=${shop.id}`);
    };
    const showFilter = () => {
      filterPopup.value.open();
    };
    const closeFilter = () => {
      filterPopup.value.close();
    };
    const toggleCategory = (index) => {
      const position = selectedCategories.value.indexOf(index);
      if (index === 0) {
        selectedCategories.value = [0];
      } else {
        if (selectedCategories.value.includes(0)) {
          selectedCategories.value = selectedCategories.value.filter((item) => item !== 0);
        }
        if (position !== -1) {
          selectedCategories.value.splice(position, 1);
          if (selectedCategories.value.length === 0) {
            selectedCategories.value = [0];
          }
        } else {
          selectedCategories.value.push(index);
        }
      }
    };
    const selectRating = (index) => {
      selectedRating.value = index;
    };
    const toggleService = (index) => {
      const position = selectedServices.value.indexOf(index);
      if (position !== -1) {
        selectedServices.value.splice(position, 1);
      } else {
        selectedServices.value.push(index);
      }
    };
    const resetFilters = () => {
      selectedCategories.value = [0];
      selectedRating.value = 0;
      selectedServices.value = [];
      currentCategory.value = 0;
      currentFilter.value = "distance";
    };
    const applyFilter = () => {
      common_vendor.index.__f__("log", "at subPackages/activity-showcase/pages/shops/index.vue:555", "应用筛选", {
        categories: selectedCategories.value.map((index) => allCategories[index].value),
        rating: ratingOptions[selectedRating.value].value,
        services: selectedServices.value.map((index) => serviceOptions[index].value)
      });
      common_vendor.index.showToast({
        title: "筛选已应用",
        icon: "success"
      });
      closeFilter();
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const navigateTo = (url) => {
      common_vendor.index.navigateTo({ url });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          d: "M19 12H5M12 19l-7-7 7-7",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        b: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        c: common_vendor.o(goBack),
        d: common_vendor.p({
          cx: "11",
          cy: "11",
          r: "8",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        e: common_vendor.p({
          d: "M21 21l-4.35-4.35",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        f: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        g: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/search/index?type=shops")),
        h: common_vendor.p({
          cx: "11",
          cy: "11",
          r: "8",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        i: common_vendor.p({
          d: "M21 21l-4.35-4.35",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        j: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "20",
          height: "20"
        }),
        k: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/search/index?type=shops")),
        l: common_vendor.p({
          d: "M7 10l5 5 5-5",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        m: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        n: currentFilter.value === "distance" ? 1 : "",
        o: common_vendor.o(($event) => setFilter("distance")),
        p: common_vendor.p({
          d: "M7 10l5 5 5-5",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        q: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        r: currentFilter.value === "rating" ? 1 : "",
        s: common_vendor.o(($event) => setFilter("rating")),
        t: common_vendor.p({
          d: "M7 10l5 5 5-5",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        v: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        w: currentFilter.value === "sales" ? 1 : "",
        x: common_vendor.o(($event) => setFilter("sales")),
        y: common_vendor.p({
          d: "M22 3H2l8 9.46V19l4 2v-8.54L22 3z",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        z: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        A: common_vendor.o(showFilter),
        B: common_vendor.f(shopCategories, (category, index, i0) => {
          return {
            a: common_vendor.t(category.name),
            b: index,
            c: currentCategory.value === index ? 1 : "",
            d: common_vendor.o(($event) => switchCategory(index), index)
          };
        }),
        C: common_vendor.f(getFilteredShops(), (shop, k0, i0) => {
          return common_vendor.e({
            a: shop.logo,
            b: common_vendor.t(shop.name),
            c: common_vendor.t(shop.rating),
            d: "9c228401-17-" + i0 + "," + ("9c228401-16-" + i0),
            e: "9c228401-16-" + i0,
            f: "9c228401-19-" + i0 + "," + ("9c228401-18-" + i0),
            g: "9c228401-20-" + i0 + "," + ("9c228401-18-" + i0),
            h: "9c228401-18-" + i0,
            i: common_vendor.t(shop.category),
            j: "9c228401-22-" + i0 + "," + ("9c228401-21-" + i0),
            k: "9c228401-23-" + i0 + "," + ("9c228401-21-" + i0),
            l: "9c228401-21-" + i0,
            m: common_vendor.t(shop.distance),
            n: "9c228401-25-" + i0 + "," + ("9c228401-24-" + i0),
            o: "9c228401-26-" + i0 + "," + ("9c228401-24-" + i0),
            p: "9c228401-27-" + i0 + "," + ("9c228401-24-" + i0),
            q: "9c228401-24-" + i0,
            r: common_vendor.t(shop.monthlySales),
            s: common_vendor.f(shop.tags.slice(0, 3), (tag, tagIndex, i1) => {
              return {
                a: common_vendor.t(tag),
                b: tagIndex
              };
            }),
            t: shop.tags.length > 3
          }, shop.tags.length > 3 ? {
            v: common_vendor.t(shop.tags.length - 3)
          } : {}, {
            w: shop.promotion
          }, shop.promotion ? {
            x: common_vendor.t(shop.promotion.type),
            y: common_vendor.t(shop.promotion.text)
          } : {}, {
            z: shop.id,
            A: common_vendor.o(($event) => viewShopDetail(shop), shop.id)
          });
        }),
        D: common_vendor.p({
          points: "12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",
          fill: "#FF9500",
          stroke: "#FF9500",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        E: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        F: common_vendor.p({
          d: "M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        G: common_vendor.p({
          d: "M9 22V12h6v10",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        H: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        I: common_vendor.p({
          d: "M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0118 0z",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        J: common_vendor.p({
          cx: "12",
          cy: "10",
          r: "3",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        K: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        L: common_vendor.p({
          d: "M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        M: common_vendor.p({
          cx: "9",
          cy: "7",
          r: "4",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        N: common_vendor.p({
          d: "M23 21v-2a4 4 0 00-3-3.87m-4-12a4 4 0 010 7.75",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        O: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        P: isLoadingMore.value
      }, isLoadingMore.value ? {} : {}, {
        Q: getFilteredShops().length === 0
      }, getFilteredShops().length === 0 ? {
        R: common_assets._imports_0$62,
        S: common_vendor.o(resetFilters)
      } : {}, {
        T: isRefreshing.value,
        U: common_vendor.o(onRefresh),
        V: common_vendor.o(loadMore),
        W: common_vendor.p({
          x1: "18",
          y1: "6",
          x2: "6",
          y2: "18",
          stroke: "#333333",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        X: common_vendor.p({
          x1: "6",
          y1: "6",
          x2: "18",
          y2: "18",
          stroke: "#333333",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        Y: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        Z: common_vendor.o(closeFilter),
        aa: common_vendor.f(allCategories, (category, index, i0) => {
          return {
            a: common_vendor.t(category.name),
            b: index,
            c: selectedCategories.value.includes(index) ? 1 : "",
            d: common_vendor.o(($event) => toggleCategory(index), index)
          };
        }),
        ab: common_vendor.f(ratingOptions, (option, index, i0) => {
          return {
            a: common_vendor.t(option.label),
            b: index,
            c: selectedRating.value === index ? 1 : "",
            d: common_vendor.o(($event) => selectRating(index), index)
          };
        }),
        ac: common_vendor.f(serviceOptions, (option, index, i0) => {
          return {
            a: common_vendor.t(option.label),
            b: index,
            c: selectedServices.value.includes(index) ? 1 : "",
            d: common_vendor.o(($event) => toggleService(index), index)
          };
        }),
        ad: common_vendor.o(resetFilters),
        ae: common_vendor.o(applyFilter),
        af: common_vendor.sr(filterPopup, "9c228401-28", {
          "k": "filterPopup"
        }),
        ag: common_vendor.p({
          type: "bottom"
        })
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-9c228401"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/shops/index.js.map
