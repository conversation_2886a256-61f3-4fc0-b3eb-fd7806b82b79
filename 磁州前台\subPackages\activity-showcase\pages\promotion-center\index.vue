<template>
  <view class="promotion-center-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M19 12H5M12 19l-7-7 7-7" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        <view class="navbar-title">促销中心</view>
        <view class="navbar-right">
          <view class="message-btn" @click="navigateTo('/subPackages/activity-showcase/pages/message/index')">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <path d="M21 15a2 2 0 01-2 2H7l-4 4V5a2 2 0 012-2h14a2 2 0 012 2z" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
            <view class="badge" v-if="unreadMessages > 0">{{ unreadMessages > 99 ? '99+' : unreadMessages }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 顶部轮播图 -->
    <swiper class="banner-swiper" 
      :indicator-dots="true" 
      indicator-color="rgba(255,255,255,0.4)"
      indicator-active-color="#FFFFFF"
      :autoplay="true" 
      :interval="4000" 
      :duration="400"
      :style="{ marginTop: 'calc(var(--status-bar-height) + 90rpx)' }"
    >
      <swiper-item v-for="(banner, index) in banners" :key="index">
        <image :src="banner.image" mode="aspectFill" class="banner-image" @click="handleBannerClick(banner)"></image>
      </swiper-item>
    </swiper>

    <!-- 促销类型导航 -->
    <view class="promotion-nav">
      <view 
        v-for="(item, index) in promotionTypes" 
        :key="index"
        class="nav-item"
        @click="navigateTo(item.url)"
      >
        <view class="nav-icon" :style="{ background: item.bgColor }">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
            <path :d="item.icon" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        <text class="nav-text">{{ item.name }}</text>
      </view>
    </view>

    <!-- 限时特惠区域 -->
    <view class="section flash-sale-section">
      <view class="section-header">
        <view class="header-left">
          <text class="section-title">限时特惠</text>
          <view class="countdown">
            <text class="countdown-label">距结束</text>
            <text class="time-block">{{ countdown.hours }}</text>
            <text class="time-colon">:</text>
            <text class="time-block">{{ countdown.minutes }}</text>
            <text class="time-colon">:</text>
            <text class="time-block">{{ countdown.seconds }}</text>
          </view>
        </view>
        <view class="header-right" @click="navigateTo('/subPackages/activity-showcase/pages/flash-sale/index')">
          <text class="view-all">查看全部</text>
          <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
            <path d="M9 18l6-6-6-6" stroke="#FF3B30" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
      </view>
      
      <scroll-view class="flash-sale-scroll" scroll-x>
        <view class="flash-sale-list">
          <view 
            v-for="(item, index) in flashSaleItems" 
            :key="index"
            class="flash-sale-item"
            @click="viewFlashSaleDetail(item)"
          >
            <image :src="item.image" class="item-image" mode="aspectFill"></image>
            <view class="discount-tag">{{ item.discount }}</view>
            <view class="item-info">
              <text class="item-title">{{ item.title }}</text>
              <view class="item-price">
                <text class="price-current">¥{{ item.currentPrice.toFixed(2) }}</text>
                <text class="price-original">¥{{ item.originalPrice.toFixed(2) }}</text>
              </view>
              <view class="progress-bar">
                <view class="progress-fill" :style="{ width: `${item.soldPercentage}%` }"></view>
              </view>
              <view class="item-sales">
                <text>已售{{ item.soldCount }}件</text>
                <text class="sales-status" v-if="item.soldPercentage > 80">即将售罄</text>
                <text class="sales-status" v-else>热卖中</text>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 拼团优惠区域 -->
    <view class="section group-buy-section">
      <view class="section-header">
        <view class="header-left">
          <text class="section-title">拼团优惠</text>
          <view class="section-tag">低至5折</view>
        </view>
        <view class="header-right" @click="navigateTo('/subPackages/activity-showcase/pages/group-buy/index')">
          <text class="view-all">查看全部</text>
          <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
            <path d="M9 18l6-6-6-6" stroke="#FF9500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
      </view>
      
      <view class="group-buy-grid">
        <view 
          v-for="(item, index) in groupBuyItems.slice(0, 2)" 
          :key="index"
          class="group-buy-item"
          @click="viewGroupBuyDetail(item)"
        >
          <image :src="item.image" class="item-image" mode="aspectFill"></image>
          <view class="item-info">
            <text class="item-title">{{ item.title }}</text>
            <view class="group-info">
              <view class="group-price">
                <text class="price-label">{{ item.groupSize }}人团:</text>
                <text class="price-current">¥{{ item.groupPrice.toFixed(2) }}</text>
                <text class="price-original">¥{{ item.originalPrice.toFixed(2) }}</text>
              </view>
              <view class="group-btn">去拼团</view>
            </view>
          </view>
        </view>
      </view>
      
      <view class="group-buy-list">
        <view 
          v-for="(item, index) in groupBuyItems.slice(2, 5)" 
          :key="index"
          class="list-item"
          @click="viewGroupBuyDetail(item)"
        >
          <image :src="item.image" class="list-item-image" mode="aspectFill"></image>
          <view class="list-item-info">
            <text class="list-item-title">{{ item.title }}</text>
            <view class="list-item-price">
              <view class="group-price">
                <text class="price-label">{{ item.groupSize }}人团:</text>
                <text class="price-current">¥{{ item.groupPrice.toFixed(2) }}</text>
                <text class="price-original">¥{{ item.originalPrice.toFixed(2) }}</text>
              </view>
              <view class="group-btn">去拼团</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 满减优惠区域 -->
    <view class="section discount-section">
      <view class="section-header">
        <view class="header-left">
          <text class="section-title">满减优惠</text>
          <view class="section-tag">满200减30</view>
        </view>
        <view class="header-right" @click="navigateTo('/subPackages/activity-showcase/pages/discount/index')">
          <text class="view-all">查看全部</text>
          <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
            <path d="M9 18l6-6-6-6" stroke="#5AC8FA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
      </view>
      
      <view class="discount-grid">
        <view 
          v-for="(shop, index) in discountShops" 
          :key="index"
          class="discount-shop"
          @click="viewShopDetail(shop)"
        >
          <image :src="shop.logo" class="shop-logo" mode="aspectFill"></image>
          <view class="shop-info">
            <text class="shop-name">{{ shop.name }}</text>
            <view class="discount-tags">
              <view 
                v-for="(tag, tagIndex) in shop.discounts" 
                :key="tagIndex"
                class="discount-tag"
              >
                {{ tag }}
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 优惠券专区 -->
    <view class="section coupon-section">
      <view class="section-header">
        <view class="header-left">
          <text class="section-title">优惠券专区</text>
          <view class="section-tag">天天领券</view>
        </view>
        <view class="header-right" @click="navigateTo('/subPackages/activity-showcase/pages/coupon/index')">
          <text class="view-all">查看全部</text>
          <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
            <path d="M9 18l6-6-6-6" stroke="#FF9500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
      </view>
      
      <scroll-view class="coupon-scroll" scroll-x>
        <view class="coupon-list">
          <view 
            v-for="(coupon, index) in coupons" 
            :key="index"
            class="coupon-item"
            :style="{ background: getCouponBackground(coupon.type) }"
            @click="receiveCoupon(coupon)"
          >
            <view class="coupon-left">
              <view class="coupon-value">
                <text class="currency" v-if="coupon.type !== 'discount'">¥</text>
                <text class="amount">{{ coupon.value }}</text>
                <text class="unit" v-if="coupon.type === 'discount'">折</text>
              </view>
              <text class="coupon-condition">{{ coupon.condition }}</text>
            </view>
            <view class="coupon-divider"></view>
            <view class="coupon-right">
              <text class="coupon-name">{{ coupon.name }}</text>
              <text class="coupon-shop">{{ coupon.shopName }}</text>
              <text class="coupon-validity">{{ coupon.validityPeriod }}</text>
              <view class="coupon-btn">立即领取</view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 推荐活动区域 -->
    <view class="section activity-section">
      <view class="section-header">
        <view class="header-left">
          <text class="section-title">推荐活动</text>
        </view>
        <view class="header-right" @click="navigateTo('/subPackages/activity-showcase/pages/list/index')">
          <text class="view-all">查看全部</text>
          <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
            <path d="M9 18l6-6-6-6" stroke="#FF3B69" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
      </view>
      
      <view class="activity-list">
        <view 
          v-for="(activity, index) in recommendedActivities" 
          :key="index"
          class="activity-item"
          @click="viewActivityDetail(activity)"
        >
          <image :src="activity.image" class="activity-image" mode="aspectFill"></image>
          <view class="activity-info">
            <text class="activity-title">{{ activity.title }}</text>
            <view class="activity-meta">
              <view class="meta-item">
                <svg class="meta-icon" viewBox="0 0 24 24" width="16" height="16">
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></rect>
                  <line x1="16" y1="2" x2="16" y2="6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                  <line x1="8" y1="2" x2="8" y2="6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                  <line x1="3" y1="10" x2="21" y2="10" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                </svg>
                <text class="meta-text">{{ activity.date }}</text>
              </view>
              <view class="meta-item">
                <svg class="meta-icon" viewBox="0 0 24 24" width="16" height="16">
                  <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0118 0z" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                  <circle cx="12" cy="10" r="3" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                </svg>
                <text class="meta-text">{{ activity.location }}</text>
              </view>
            </view>
            <view class="activity-bottom">
              <view class="price-tag" v-if="activity.price > 0">
                <text class="price-symbol">¥</text>
                <text class="price-value">{{ activity.price.toFixed(2) }}</text>
              </view>
              <text class="price-free" v-else>免费</text>
              <view class="participants">
                <view class="avatar-group">
                  <image 
                    v-for="(avatar, avatarIndex) in activity.participants.slice(0, 3)" 
                    :key="avatarIndex"
                    :src="avatar"
                    class="participant-avatar"
                  ></image>
                </view>
                <text class="participant-count">{{ activity.participants.length }}人参与</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

// 页面状态
const unreadMessages = ref(3);
const countdown = ref({ hours: '00', minutes: '00', seconds: '00' });
let countdownTimer = null;

// 轮播图数据
const banners = [
  { 
    id: '1',
    image: '/static/demo/banner1.jpg',
    type: 'flash_sale',
    targetId: '1001'
  },
  { 
    id: '2',
    image: '/static/demo/banner2.jpg',
    type: 'group_buy',
    targetId: '1002'
  },
  { 
    id: '3',
    image: '/static/demo/banner3.jpg',
    type: 'activity',
    targetId: '1003'
  }
];

// 促销类型导航
const promotionTypes = [
  {
    name: '限时特惠',
    icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z',
    url: '/subPackages/activity-showcase/pages/flash-sale/index',
    bgColor: 'linear-gradient(135deg, #FF3B30 0%, #FF9580 100%)'
  },
  {
    name: '拼团优惠',
    icon: 'M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2M9 11a4 4 0 100-8 4 4 0 000 8zM23 21v-2a4 4 0 00-3-3.87m-4-12a4 4 0 010 7.75',
    url: '/subPackages/activity-showcase/pages/group-buy/index',
    bgColor: 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)'
  },
  {
    name: '满减优惠',
    icon: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
    url: '/subPackages/activity-showcase/pages/discount/index',
    bgColor: 'linear-gradient(135deg, #5AC8FA 0%, #90E0FF 100%)'
  },
  {
    name: '优惠券',
    icon: 'M20 7h-4m4 0v16H4V7h4m12 0l-3-3H7l-3 3m4 0h10',
    url: '/subPackages/activity-showcase/pages/coupon/index',
    bgColor: 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)'
  },
  {
    name: '全部活动',
    icon: 'M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10',
    url: '/subPackages/activity-showcase/pages/list/index',
    bgColor: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)'
  }
];

// 限时特惠数据
const flashSaleItems = [
  {
    id: '1001',
    title: '磁州窑陶艺体验课程',
    image: '/static/demo/product1.jpg',
    currentPrice: 99.00,
    originalPrice: 198.00,
    discount: '5折',
    soldCount: 356,
    soldPercentage: 85
  },
  {
    id: '1002',
    title: '磁州特产礼盒',
    image: '/static/demo/product2.jpg',
    currentPrice: 128.00,
    originalPrice: 198.00,
    discount: '6.5折',
    soldCount: 512,
    soldPercentage: 60
  },
  {
    id: '1003',
    title: '户外露营套装',
    image: '/static/demo/product3.jpg',
    currentPrice: 199.00,
    originalPrice: 399.00,
    discount: '5折',
    soldCount: 128,
    soldPercentage: 90
  },
  {
    id: '1004',
    title: '亲子烘焙课程',
    image: '/static/demo/product4.jpg',
    currentPrice: 158.00,
    originalPrice: 258.00,
    discount: '6折',
    soldCount: 245,
    soldPercentage: 75
  }
];

// 拼团优惠数据
const groupBuyItems = [
  {
    id: '2001',
    title: '磁州文化体验一日游',
    image: '/static/demo/activity1.jpg',
    groupPrice: 99.00,
    originalPrice: 198.00,
    groupSize: 3
  },
  {
    id: '2002',
    title: '亲子户外拓展活动',
    image: '/static/demo/activity2.jpg',
    groupPrice: 128.00,
    originalPrice: 198.00,
    groupSize: 2
  },
  {
    id: '2003',
    title: '传统文化体验课',
    image: '/static/demo/activity4.jpg',
    groupPrice: 88.00,
    originalPrice: 158.00,
    groupSize: 3
  },
  {
    id: '2004',
    title: '磁州美食体验',
    image: '/static/demo/product6.jpg',
    groupPrice: 68.00,
    originalPrice: 128.00,
    groupSize: 4
  },
  {
    id: '2005',
    title: '陶艺DIY体验',
    image: '/static/demo/product7.jpg',
    groupPrice: 79.00,
    originalPrice: 158.00,
    groupSize: 2
  }
];

// 满减优惠商铺数据
const discountShops = [
  {
    id: '3001',
    name: '磁州文化体验馆',
    logo: '/static/demo/shop1.jpg',
    discounts: ['满200减30', '满500减100']
  },
  {
    id: '3002',
    name: '磁州美食城',
    logo: '/static/demo/shop2.jpg',
    discounts: ['满100减15', '满300减50']
  },
  {
    id: '3003',
    name: '磁州户外拓展中心',
    logo: '/static/demo/shop3.jpg',
    discounts: ['满300减50', '满600减120']
  },
  {
    id: '3004',
    name: '磁州亲子乐园',
    logo: '/static/demo/shop4.jpg',
    discounts: ['满150减30', '满300减70']
  }
];

// 优惠券数据
const coupons = [
  {
    id: '4001',
    name: '新人专享券',
    shopName: '磁州文化体验馆',
    type: 'cash',
    value: '30',
    condition: '满200可用',
    validityPeriod: '有效期至2024-06-30'
  },
  {
    id: '4002',
    name: '美食专享券',
    shopName: '磁州美食城',
    type: 'cash',
    value: '15',
    condition: '满100可用',
    validityPeriod: '有效期至2024-06-15'
  },
  {
    id: '4003',
    name: '户外活动券',
    shopName: '磁州户外拓展中心',
    type: 'discount',
    value: '8.5',
    condition: '无门槛',
    validityPeriod: '有效期至2024-06-20'
  },
  {
    id: '4004',
    name: '亲子活动券',
    shopName: '磁州亲子乐园',
    type: 'cash',
    value: '50',
    condition: '满300可用',
    validityPeriod: '有效期至2024-06-25'
  }
];

// 推荐活动数据
const recommendedActivities = [
  {
    id: '5001',
    title: '磁州文化节',
    image: '/static/demo/activity1.jpg',
    date: '2024-06-15',
    location: '磁州文化广场',
    price: 0,
    participants: [
      '/static/demo/avatar1.png',
      '/static/demo/avatar2.png',
      '/static/demo/avatar3.png',
      '/static/demo/avatar4.png',
      '/static/demo/avatar5.png'
    ]
  },
  {
    id: '5002',
    title: '亲子户外拓展活动',
    image: '/static/demo/activity2.jpg',
    date: '2024-05-28',
    location: '磁州森林公园',
    price: 128,
    participants: [
      '/static/demo/avatar1.png',
      '/static/demo/avatar3.png',
      '/static/demo/avatar5.png'
    ]
  },
  {
    id: '5003',
    title: '社区篮球赛',
    image: '/static/demo/activity3.jpg',
    date: '2024-06-05',
    location: '磁州体育中心',
    price: 50,
    participants: [
      '/static/demo/avatar2.png',
      '/static/demo/avatar4.png',
      '/static/demo/avatar5.png',
      '/static/demo/avatar1.png'
    ]
  }
];

// 生命周期
onMounted(() => {
  startCountdown();
});

onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }
});

// 方法
const startCountdown = () => {
  // 设置倒计时结束时间（当前时间 + 12小时）
  const endTime = new Date();
  endTime.setHours(endTime.getHours() + 12);
  
  countdownTimer = setInterval(() => {
    const now = new Date();
    const diff = endTime - now;
    
    if (diff <= 0) {
      clearInterval(countdownTimer);
      countdown.value = { hours: '00', minutes: '00', seconds: '00' };
      return;
    }
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);
    
    countdown.value = {
      hours: hours.toString().padStart(2, '0'),
      minutes: minutes.toString().padStart(2, '0'),
      seconds: seconds.toString().padStart(2, '0')
    };
  }, 1000);
};

const handleBannerClick = (banner) => {
  switch (banner.type) {
    case 'flash_sale':
      navigateTo(`/subPackages/activity-showcase/pages/flash-sale/detail?id=${banner.targetId}`);
      break;
    case 'group_buy':
      navigateTo(`/subPackages/activity-showcase/pages/group-buy/detail?id=${banner.targetId}`);
      break;
    case 'activity':
      navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${banner.targetId}`);
      break;
    default:
      navigateTo('/subPackages/activity-showcase/pages/list/index');
  }
};

const viewFlashSaleDetail = (item) => {
  navigateTo(`/subPackages/activity-showcase/pages/flash-sale/detail?id=${item.id}`);
};

const viewGroupBuyDetail = (item) => {
  navigateTo(`/subPackages/activity-showcase/pages/group-buy/detail?id=${item.id}`);
};

const viewShopDetail = (shop) => {
  navigateTo(`/subPackages/activity-showcase/pages/shops/detail?id=${shop.id}`);
};

const viewActivityDetail = (activity) => {
  navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${activity.id}`);
};

const receiveCoupon = (coupon) => {
  uni.showToast({
    title: '领取成功',
    icon: 'success'
  });
};

const getCouponBackground = (type) => {
  const bgMap = {
    'cash': 'linear-gradient(to right, #FF9500, #FFCC00)',
    'discount': 'linear-gradient(to right, #5AC8FA, #90E0FF)',
    'free': 'linear-gradient(to right, #34C759, #7ED321)'
  };
  return bgMap[type] || bgMap['cash'];
};

const goBack = () => {
  uni.navigateBack();
};

const navigateTo = (url) => {
  uni.navigateTo({ url });
};
</script>

<style scoped>
.promotion-center-container {
  min-height: 100vh;
  background-color: #F5F5F5;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
}

.navbar-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
}

.navbar-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 90rpx;
  padding: var(--status-bar-height) 30rpx 0;
}

.back-btn, .message-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-btn {
  position: relative;
}

.badge {
  position: absolute;
  top: 0;
  right: 0;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background: #FF3B30;
  color: #FFFFFF;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #FFFFFF;
}

.navbar-right {
  display: flex;
  align-items: center;
}

/* 轮播图样式 */
.banner-swiper {
  height: 300rpx;
  width: 100%;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 促销类型导航样式 */
.promotion-nav {
  display: flex;
  flex-wrap: wrap;
  background: #FFFFFF;
  padding: 30rpx 20rpx;
  margin: 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.nav-item {
  width: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.nav-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
}

.nav-text {
  font-size: 24rpx;
  color: #333333;
}

/* 通用区域样式 */
.section {
  background: #FFFFFF;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.header-left {
  display: flex;
  align-items: center;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-right: 15rpx;
}

.section-tag {
  font-size: 22rpx;
  color: #FFFFFF;
  background: #FF3B69;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.header-right {
  display: flex;
  align-items: center;
}

.view-all {
  font-size: 26rpx;
  color: #999999;
  margin-right: 6rpx;
}

/* 限时特惠区域样式 */
.flash-sale-section .section-title {
  color: #FF3B30;
}

.countdown {
  display: flex;
  align-items: center;
  background: rgba(255, 59, 48, 0.1);
  border-radius: 20rpx;
  padding: 4rpx 12rpx;
}

.countdown-label {
  font-size: 22rpx;
  color: #FF3B30;
  margin-right: 6rpx;
}

.time-block {
  width: 40rpx;
  height: 40rpx;
  background: #FF3B30;
  color: #FFFFFF;
  font-size: 22rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6rpx;
}

.time-colon {
  font-size: 22rpx;
  color: #FF3B30;
  margin: 0 4rpx;
}

.flash-sale-scroll {
  white-space: nowrap;
}

.flash-sale-list {
  display: flex;
  padding: 10rpx 0;
}

.flash-sale-item {
  display: inline-block;
  width: 280rpx;
  margin-right: 20rpx;
  border-radius: 15rpx;
  overflow: hidden;
  background: #FFFFFF;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
  position: relative;
}

.item-image {
  width: 100%;
  height: 280rpx;
  object-fit: cover;
}

.discount-tag {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background: rgba(255, 59, 48, 0.8);
  color: #FFFFFF;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.item-info {
  padding: 15rpx;
}

.item-title {
  font-size: 26rpx;
  color: #333333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  white-space: normal;
  height: 72rpx;
}

.item-price {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.price-current {
  font-size: 28rpx;
  font-weight: bold;
  color: #FF3B30;
  margin-right: 10rpx;
}

.price-original {
  font-size: 24rpx;
  color: #999999;
  text-decoration: line-through;
}

.progress-bar {
  height: 8rpx;
  background: #F0F0F0;
  border-radius: 4rpx;
  margin-bottom: 10rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #FF3B30;
  border-radius: 4rpx;
}

.item-sales {
  display: flex;
  justify-content: space-between;
  font-size: 22rpx;
  color: #999999;
}

.sales-status {
  color: #FF3B30;
}

/* 拼团优惠区域样式 */
.group-buy-section .section-title {
  color: #FF9500;
}

.group-buy-section .section-tag {
  background: #FF9500;
}

.group-buy-grid {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.group-buy-item {
  width: 48%;
  border-radius: 15rpx;
  overflow: hidden;
  background: #FFFFFF;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.group-buy-item .item-image {
  width: 100%;
  height: 200rpx;
}

.group-buy-item .item-info {
  padding: 15rpx;
}

.group-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.group-price {
  display: flex;
  align-items: baseline;
  flex-wrap: wrap;
}

.price-label {
  font-size: 22rpx;
  color: #666666;
  margin-right: 6rpx;
}

.group-btn {
  font-size: 24rpx;
  color: #FFFFFF;
  background: #FF9500;
  padding: 6rpx 15rpx;
  border-radius: 20rpx;
}

.group-buy-list {
  margin-top: 20rpx;
}

.list-item {
  display: flex;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.list-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.list-item-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
}

.list-item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.list-item-title {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.list-item-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 满减优惠区域样式 */
.discount-section .section-title {
  color: #5AC8FA;
}

.discount-section .section-tag {
  background: #5AC8FA;
}

.discount-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.discount-shop {
  display: flex;
  align-items: center;
  padding: 15rpx;
  background: #F9F9F9;
  border-radius: 15rpx;
}

.shop-logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 15rpx;
}

.shop-info {
  flex: 1;
}

.shop-name {
  font-size: 26rpx;
  color: #333333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.discount-tags {
  display: flex;
  flex-wrap: wrap;
}

.discount-tags .discount-tag {
  font-size: 20rpx;
  color: #5AC8FA;
  background: rgba(90, 200, 250, 0.1);
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
  margin-right: 10rpx;
  margin-bottom: 6rpx;
}

/* 优惠券专区样式 */
.coupon-section .section-title {
  color: #FF9500;
}

.coupon-section .section-tag {
  background: #FF9500;
}

.coupon-scroll {
  white-space: nowrap;
}

.coupon-list {
  display: flex;
  padding: 10rpx 0;
}

.coupon-item {
  display: inline-flex;
  width: 500rpx;
  height: 180rpx;
  margin-right: 20rpx;
  border-radius: 15rpx;
  overflow: hidden;
  position: relative;
}

.coupon-item::before {
  content: '';
  position: absolute;
  left: 160rpx;
  top: -10rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: #F5F5F5;
}

.coupon-item::after {
  content: '';
  position: absolute;
  left: 160rpx;
  bottom: -10rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: #F5F5F5;
}

.coupon-left {
  width: 160rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
}

.coupon-value {
  display: flex;
  align-items: baseline;
}

.currency {
  font-size: 24rpx;
  color: #FFFFFF;
}

.amount {
  font-size: 48rpx;
  font-weight: bold;
  color: #FFFFFF;
}

.unit {
  font-size: 24rpx;
  color: #FFFFFF;
}

.coupon-condition {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 10rpx;
}

.coupon-divider {
  width: 1rpx;
  height: 140rpx;
  background: rgba(255, 255, 255, 0.3);
  margin: 20rpx 0;
  position: relative;
  z-index: 1;
}

.coupon-right {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.coupon-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #FFFFFF;
  margin-bottom: 6rpx;
}

.coupon-shop {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 6rpx;
}

.coupon-validity {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
}

.coupon-btn {
  align-self: flex-end;
  font-size: 24rpx;
  color: #FFFFFF;
  background: rgba(255, 255, 255, 0.2);
  padding: 6rpx 15rpx;
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.4);
}

/* 推荐活动区域样式 */
.activity-section .section-title {
  color: #FF3B69;
}

.activity-list {
  display: flex;
  flex-direction: column;
}

.activity-item {
  display: flex;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.activity-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.activity-image {
  width: 200rpx;
  height: 150rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
}

.activity-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.activity-title {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.activity-meta {
  display: flex;
  margin-bottom: 10rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}

.meta-icon {
  margin-right: 6rpx;
}

.meta-text {
  font-size: 24rpx;
  color: #999999;
}

.activity-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-tag {
  display: flex;
  align-items: baseline;
}

.price-symbol {
  font-size: 22rpx;
  color: #FF3B69;
  margin-right: 2rpx;
}

.price-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #FF3B69;
}

.price-free {
  font-size: 24rpx;
  color: #34C759;
  font-weight: 500;
}

.participants {
  display: flex;
  align-items: center;
}

.avatar-group {
  display: flex;
  margin-right: 10rpx;
}

.participant-avatar {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #FFFFFF;
  margin-left: -10rpx;
}

.participant-avatar:first-child {
  margin-left: 0;
}

.participant-count {
  font-size: 22rpx;
  color: #999999;
}
</style>