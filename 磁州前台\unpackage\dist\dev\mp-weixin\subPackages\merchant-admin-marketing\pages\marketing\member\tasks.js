"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      dateRange: "2023-04-01 ~ 2023-04-30",
      currentTab: 0,
      // 任务数据概览
      taskData: {
        totalTasks: 12,
        activeTasks: 8,
        completionRate: 68.5,
        completionTrend: "up",
        completionGrowth: "5.2%",
        participationRate: 42.3,
        participationTrend: "up",
        participationGrowth: "3.8%"
      },
      // 任务分类标签
      tabs: ["全部任务", "日常任务", "成长任务", "消费任务", "社交任务"],
      // 任务列表
      tasks: [
        {
          id: 1,
          name: "每日签到",
          description: "会员每日签到获得积分奖励",
          reward: "5积分/次",
          period: "每日一次",
          category: "日常任务",
          status: "active",
          statusText: "进行中",
          enabled: true,
          iconPath: "M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7v-5z",
          iconBg: "rgba(246, 211, 101, 0.1)",
          iconColor: "#F6D365"
        },
        {
          id: 2,
          name: "浏览商品",
          description: "每日浏览5件商品获得积分奖励",
          reward: "10积分/次",
          period: "每日一次",
          category: "日常任务",
          status: "active",
          statusText: "进行中",
          enabled: true,
          iconPath: "M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z",
          iconBg: "rgba(246, 211, 101, 0.1)",
          iconColor: "#F6D365"
        },
        {
          id: 3,
          name: "分享商品",
          description: "分享商品给好友获得积分奖励",
          reward: "15积分/次",
          period: "每日三次",
          category: "社交任务",
          status: "active",
          statusText: "进行中",
          enabled: true,
          iconPath: "M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z",
          iconBg: "rgba(246, 211, 101, 0.1)",
          iconColor: "#F6D365"
        },
        {
          id: 4,
          name: "邀请好友",
          description: "邀请好友注册成为会员",
          reward: "50积分/人",
          period: "长期有效",
          category: "社交任务",
          status: "active",
          statusText: "进行中",
          enabled: true,
          iconPath: "M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z",
          iconBg: "rgba(246, 211, 101, 0.1)",
          iconColor: "#F6D365"
        },
        {
          id: 5,
          name: "完善资料",
          description: "完善个人资料获得一次性奖励",
          reward: "30积分",
          period: "一次性",
          category: "成长任务",
          status: "active",
          statusText: "进行中",
          enabled: true,
          iconPath: "M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z",
          iconBg: "rgba(246, 211, 101, 0.1)",
          iconColor: "#F6D365"
        },
        {
          id: 6,
          name: "首次购物",
          description: "首次在商城购物获得奖励",
          reward: "100积分",
          period: "一次性",
          category: "消费任务",
          status: "active",
          statusText: "进行中",
          enabled: true,
          iconPath: "M7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12.9-1.63h7.45c.75 0 1.41-.41 1.75-1.03l3.58-6.49c.08-.14.12-.31.12-.48 0-.55-.45-1-1-1H5.21l-.94-2H1zm16 16c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2z",
          iconBg: "rgba(246, 211, 101, 0.1)",
          iconColor: "#F6D365"
        },
        {
          id: 7,
          name: "商品评价",
          description: "购买商品后评价获得积分奖励",
          reward: "20积分/次",
          period: "每单一次",
          category: "消费任务",
          status: "active",
          statusText: "进行中",
          enabled: true,
          iconPath: "M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-7 12h-2v-2h2v2zm0-4h-2V6h2v4z",
          iconBg: "rgba(246, 211, 101, 0.1)",
          iconColor: "#F6D365"
        },
        {
          id: 8,
          name: "连续签到7天",
          description: "连续签到7天获得额外奖励",
          reward: "50积分",
          period: "每周一次",
          category: "成长任务",
          status: "active",
          statusText: "进行中",
          enabled: true,
          iconPath: "M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7v-5z",
          iconBg: "rgba(246, 211, 101, 0.1)",
          iconColor: "#F6D365"
        }
      ]
    };
  },
  computed: {
    filteredTasks() {
      if (this.currentTab === 0) {
        return this.tasks;
      } else {
        const category = this.tabs[this.currentTab];
        return this.tasks.filter((task) => task.category === category);
      }
    }
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    showHelp() {
      common_vendor.index.showModal({
        title: "会员任务帮助",
        content: "会员任务是指会员可以通过完成特定任务获得积分或其他奖励的活动，可以提高会员活跃度和忠诚度。",
        showCancel: false
      });
    },
    showDatePicker() {
      common_vendor.index.showToast({
        title: "日期选择功能开发中",
        icon: "none"
      });
    },
    switchTab(index) {
      this.currentTab = index;
    },
    createTask() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/member/create-task"
      });
    },
    editTask(task) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/member/edit-task?id=${task.id}`
      });
    },
    toggleTask(task, e) {
      const index = this.tasks.findIndex((item) => item.id === task.id);
      if (index !== -1) {
        this.tasks[index].enabled = e.detail.value;
        this.tasks[index].status = e.detail.value ? "active" : "inactive";
        this.tasks[index].statusText = e.detail.value ? "进行中" : "已暂停";
      }
      common_vendor.index.showToast({
        title: e.detail.value ? `${task.name}已启用` : `${task.name}已禁用`,
        icon: "none"
      });
    }
  }
};
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: common_vendor.t($data.dateRange),
    d: common_vendor.o((...args) => $options.showDatePicker && $options.showDatePicker(...args)),
    e: common_vendor.t($data.taskData.totalTasks),
    f: common_vendor.t($data.taskData.activeTasks),
    g: common_vendor.t($data.taskData.completionRate),
    h: common_vendor.t($data.taskData.completionGrowth),
    i: common_vendor.n($data.taskData.completionTrend),
    j: common_vendor.t($data.taskData.participationRate),
    k: common_vendor.t($data.taskData.participationGrowth),
    l: common_vendor.n($data.taskData.participationTrend),
    m: common_vendor.f($data.tabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab),
        b: index,
        c: $data.currentTab === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    }),
    n: common_vendor.t($data.tabs[$data.currentTab]),
    o: common_vendor.o((...args) => $options.createTask && $options.createTask(...args)),
    p: common_vendor.f($options.filteredTasks, (task, index, i0) => {
      return {
        a: "bbc886e6-1-" + i0 + "," + ("bbc886e6-0-" + i0),
        b: common_vendor.p({
          d: task.iconPath
        }),
        c: task.iconColor,
        d: "bbc886e6-0-" + i0,
        e: task.iconBg,
        f: common_vendor.t(task.name),
        g: common_vendor.t(task.statusText),
        h: common_vendor.n(task.status),
        i: common_vendor.t(task.description),
        j: common_vendor.t(task.reward),
        k: common_vendor.t(task.period),
        l: task.enabled,
        m: common_vendor.o((e) => $options.toggleTask(task, e), index),
        n: index,
        o: common_vendor.o(($event) => $options.editTask(task), index)
      };
    }),
    q: common_vendor.p({
      viewBox: "0 0 24 24"
    }),
    r: common_vendor.o((...args) => $options.createTask && $options.createTask(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/member/tasks.js.map
