"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Math) {
  WalletPage();
}
const WalletPage = () => "../../../components/wallet/WalletPage.js";
const _sfc_main = {
  __name: "wallet",
  setup(__props) {
    common_vendor.index.setNavigationBarColor({
      frontColor: "#ffffff",
      backgroundColor: "#1677FF"
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          scene: "main",
          ["path-prefix"]: "/subPackages/payment/pages"
        })
      };
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/payment/pages/wallet.js.map
