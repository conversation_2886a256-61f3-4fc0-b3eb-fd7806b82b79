"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const DistributionSection = () => "../../../../components/distribution-section.js";
const _sfc_main = {
  components: {
    DistributionSection
  },
  data() {
    return {
      id: null,
      statusBarHeight: 20,
      navbarHeight: 82,
      loading: true,
      coupon: {},
      recommendProducts: [],
      commissionAmount: "20"
    };
  },
  onLoad(options) {
    if (options && options.id) {
      this.id = options.id;
    }
    const systemInfo = common_vendor.index.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 62;
    setTimeout(() => {
      this.loadCouponDetail();
    }, 500);
  },
  methods: {
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 加载优惠券详情
    loadCouponDetail() {
      this.loading = true;
      setTimeout(() => {
        this.coupon = {
          id: this.id || 1,
          title: "新人专享优惠券",
          description: "仅限新用户使用",
          type: "cash",
          value: "50",
          minAmount: 199,
          startTime: /* @__PURE__ */ new Date(),
          endTime: new Date(Date.now() + 15 * 24 * 60 * 60 * 1e3),
          tag: "新人专享",
          usageRules: [
            "仅限新用户使用",
            "每人限领1张",
            "有效期15天",
            "全场通用，部分特殊商品除外",
            "不可与其他优惠券叠加使用"
          ]
        };
        this.recommendProducts = [
          {
            id: 1,
            name: "iPhone 13 Pro Max",
            price: "7999",
            image: "/static/demo/product1.jpg"
          },
          {
            id: 2,
            name: "MacBook Pro 14英寸",
            price: "12999",
            image: "/static/demo/product2.jpg"
          },
          {
            id: 3,
            name: "AirPods Pro 2",
            price: "1999",
            image: "/static/demo/product3.jpg"
          }
        ];
        this.loading = false;
      }, 1e3);
    },
    // 获取活动时间文本
    getTimeText(startTime, endTime) {
      const start = new Date(startTime);
      const end = new Date(endTime);
      const startMonth = start.getMonth() + 1;
      const startDay = start.getDate();
      const endMonth = end.getMonth() + 1;
      const endDay = end.getDate();
      return `${startMonth}.${startDay} - ${endMonth}.${endDay}`;
    },
    // 查看全部适用商品
    viewAllProducts() {
      common_vendor.index.navigateTo({
        url: `/subPackages/product/pages/list?couponId=${this.id}`
      });
    },
    // 跳转到商品详情
    goToProductDetail(productId) {
      common_vendor.index.navigateTo({
        url: `/subPackages/product/pages/detail?id=${productId}`
      });
    },
    // 分享优惠券
    shareCoupon() {
      common_vendor.index.showToast({
        title: "分享功能开发中",
        icon: "none"
      });
    },
    // 收藏优惠券
    collectCoupon() {
      common_vendor.index.showToast({
        title: "已收藏",
        icon: "success"
      });
    },
    // 领取优惠券
    getCoupon() {
      common_vendor.index.showLoading({
        title: "领取中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "领取成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      }, 1e3);
    }
  }
};
if (!Array) {
  const _component_distribution_section = common_vendor.resolveComponent("distribution-section");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_distribution_section + _component_path + _component_svg)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$7,
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: $data.loading
  }, $data.loading ? {} : common_vendor.e({
    d: common_vendor.t($data.coupon.value),
    e: $data.coupon.minAmount > 0
  }, $data.coupon.minAmount > 0 ? {
    f: common_vendor.t($data.coupon.minAmount)
  } : {}, {
    g: common_vendor.t($data.coupon.title),
    h: common_vendor.t($options.getTimeText($data.coupon.startTime, $data.coupon.endTime)),
    i: $data.coupon.tag
  }, $data.coupon.tag ? {
    j: common_vendor.t($data.coupon.tag)
  } : {}, {
    k: common_vendor.p({
      itemId: $data.id,
      itemType: "coupon",
      itemTitle: $data.coupon.title,
      itemPrice: $data.coupon.value,
      commissionRate: $data.coupon.commissionRate || 10
    }),
    l: $data.navbarHeight + "px",
    m: common_vendor.f($data.coupon.usageRules, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: index
      };
    }),
    n: common_vendor.o((...args) => $options.viewAllProducts && $options.viewAllProducts(...args)),
    o: common_vendor.f($data.recommendProducts, (item, index, i0) => {
      return {
        a: item.image,
        b: common_vendor.t(item.name),
        c: common_vendor.t(item.price),
        d: index,
        e: common_vendor.o(($event) => $options.goToProductDetail(item.id), index)
      };
    })
  }), {
    p: common_vendor.p({
      d: "M768 686.08c-32.768 0-61.952 12.8-84.48 32.768L399.872 573.952c2.56-10.24 4.608-20.992 4.608-32.256 0-11.264-2.048-22.016-4.608-32.256l283.648-144.896c22.528 19.968 51.712 32.768 84.48 32.768 70.144 0 127.488-57.344 127.488-127.488S838.144 142.336 768 142.336s-127.488 57.344-127.488 127.488c0 11.264 2.048 22.016 4.608 32.256L361.472 446.976c-22.528-19.968-51.712-32.768-84.48-32.768-70.144 0-127.488 57.344-127.488 127.488s57.344 127.488 127.488 127.488c32.768 0 61.952-12.8 84.48-32.768l283.648 144.896c-2.56 10.24-4.608 20.992-4.608 32.256 0 70.144 57.344 127.488 127.488 127.488s127.488-57.344 127.488-127.488S838.144 686.08 768 686.08z",
      fill: "#8E8E93"
    }),
    q: common_vendor.p({
      viewBox: "0 0 1024 1024",
      xmlns: "http://www.w3.org/2000/svg",
      width: "24",
      height: "24"
    }),
    r: common_vendor.o((...args) => $options.shareCoupon && $options.shareCoupon(...args)),
    s: common_vendor.p({
      d: "M512 896l-307.2-268.8c-8.533-7.467-17.067-16-25.6-25.6-76.8-76.8-76.8-198.4 0-275.2 76.8-76.8 198.4-76.8 275.2 0L512 384l57.6-57.6c76.8-76.8 198.4-76.8 275.2 0 76.8 76.8 76.8 198.4 0 275.2-8.533 8.533-17.067 17.067-25.6 25.6L512 896z",
      fill: "#8E8E93"
    }),
    t: common_vendor.p({
      viewBox: "0 0 1024 1024",
      xmlns: "http://www.w3.org/2000/svg",
      width: "24",
      height: "24"
    }),
    v: common_vendor.o((...args) => $options.collectCoupon && $options.collectCoupon(...args)),
    w: common_vendor.o((...args) => $options.getCoupon && $options.getCoupon(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-ec46b593"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/coupon/detail.js.map
