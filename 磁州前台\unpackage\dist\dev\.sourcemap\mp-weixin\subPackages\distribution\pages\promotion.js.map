{"version": 3, "file": "promotion.js", "sources": ["subPackages/distribution/pages/promotion.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcZGlzdHJpYnV0aW9uXHBhZ2VzXHByb21vdGlvbi52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"promotion-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">推广工具</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 推广工具卡片 -->\r\n    <view class=\"tools-grid\">\r\n      <view class=\"tool-card\" @click=\"navigateTo('/subPackages/promotion/pages/promotion-tool')\">\r\n        <view class=\"tool-icon poster\"></view>\r\n        <text class=\"tool-name\">推广海报</text>\r\n        <text class=\"tool-desc\">生成精美商品海报</text>\r\n      </view>\r\n      \r\n      <view class=\"tool-card\" @click=\"navigateTo('/subPackages/promotion/pages/qrcode')\">\r\n        <view class=\"tool-icon qrcode\"></view>\r\n        <text class=\"tool-name\">推广二维码</text>\r\n        <text class=\"tool-desc\">生成小程序码</text>\r\n      </view>\r\n      \r\n      <view class=\"tool-card\" @click=\"navigateToMerchant('/subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-text')\">\r\n        <view class=\"tool-icon text\"></view>\r\n        <text class=\"tool-name\">推广文案</text>\r\n        <text class=\"tool-desc\">复制推广话术</text>\r\n      </view>\r\n      \r\n      <view class=\"tool-card\" @click=\"navigateToMerchant('/subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-materials')\">\r\n        <view class=\"tool-icon materials\"></view>\r\n        <text class=\"tool-name\">推广素材</text>\r\n        <text class=\"tool-desc\">图片/视频素材</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 推广链接 -->\r\n    <view class=\"link-card\">\r\n      <view class=\"card-header\">\r\n        <text class=\"card-title\">我的推广链接</text>\r\n        <view class=\"refresh-btn\" @click=\"generateLink\">\r\n          <view class=\"refresh-icon\"></view>\r\n          <text>刷新</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"link-content\">\r\n        <text class=\"link-url\">{{promotionLink.url || '生成中...'}}</text>\r\n        <view class=\"link-actions\">\r\n          <button class=\"action-btn copy\" @click=\"copyLink\">复制链接</button>\r\n          <button class=\"action-btn share\" @click=\"shareLink\">分享</button>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 推广指南 -->\r\n    <view class=\"guide-card\">\r\n      <view class=\"card-header\">\r\n        <text class=\"card-title\">推广指南</text>\r\n      </view>\r\n      \r\n      <view class=\"guide-steps\">\r\n        <view class=\"guide-step\">\r\n          <view class=\"step-icon step1\"></view>\r\n          <view class=\"step-content\">\r\n            <text class=\"step-title\">选择工具</text>\r\n            <text class=\"step-desc\">选择合适的推广工具</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"step-arrow\"></view>\r\n        \r\n        <view class=\"guide-step\">\r\n          <view class=\"step-icon step2\"></view>\r\n          <view class=\"step-content\">\r\n            <text class=\"step-title\">分享推广</text>\r\n            <text class=\"step-desc\">分享到社交平台</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"step-arrow\"></view>\r\n        \r\n        <view class=\"guide-step\">\r\n          <view class=\"step-icon step3\"></view>\r\n          <view class=\"step-content\">\r\n            <text class=\"step-title\">获得佣金</text>\r\n            <text class=\"step-desc\">用户下单得佣金</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 推广技巧 -->\r\n    <view class=\"tips-card\">\r\n      <view class=\"card-header\">\r\n        <text class=\"card-title\">推广技巧</text>\r\n      </view>\r\n      \r\n      <view class=\"tips-list\">\r\n        <view class=\"tip-item\">\r\n          <view class=\"tip-icon\"></view>\r\n          <text class=\"tip-text\">选择热门商品进行推广，转化率更高</text>\r\n        </view>\r\n        \r\n        <view class=\"tip-item\">\r\n          <view class=\"tip-icon\"></view>\r\n          <text class=\"tip-text\">在朋友圈分享时，添加个人使用体验</text>\r\n        </view>\r\n        \r\n        <view class=\"tip-item\">\r\n          <view class=\"tip-icon\"></view>\r\n          <text class=\"tip-text\">定期更新推广内容，保持新鲜感</text>\r\n        </view>\r\n        \r\n        <view class=\"tip-item\">\r\n          <view class=\"tip-icon\"></view>\r\n          <text class=\"tip-text\">结合节日活动，推广相关商品</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 推广数据 -->\r\n    <view class=\"data-card\">\r\n      <view class=\"card-header\">\r\n        <text class=\"card-title\">推广数据</text>\r\n        <view class=\"view-all\" @click=\"navigateTo('/subPackages/distribution/pages/team')\">查看详情</view>\r\n      </view>\r\n      \r\n      <view class=\"data-grid\">\r\n        <view class=\"data-item\">\r\n          <text class=\"data-value\">{{promotionData.viewCount}}</text>\r\n          <text class=\"data-label\">浏览量</text>\r\n        </view>\r\n        \r\n        <view class=\"data-item\">\r\n          <text class=\"data-value\">{{promotionData.orderCount}}</text>\r\n          <text class=\"data-label\">订单量</text>\r\n        </view>\r\n        \r\n        <view class=\"data-item\">\r\n          <text class=\"data-value\">{{promotionData.conversionRate}}%</text>\r\n          <text class=\"data-label\">转化率</text>\r\n        </view>\r\n        \r\n        <view class=\"data-item\">\r\n          <text class=\"data-value\">¥{{formatCommission(promotionData.commission)}}</text>\r\n          <text class=\"data-label\">总佣金</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted } from 'vue';\r\nimport distributionService from '@/utils/distributionService';\r\n\r\n// 推广链接\r\nconst promotionLink = reactive({\r\n  url: '',\r\n  shortUrl: '',\r\n  qrCodeUrl: ''\r\n});\r\n\r\n// 推广数据\r\nconst promotionData = reactive({\r\n  viewCount: 0,\r\n  orderCount: 0,\r\n  conversionRate: 0,\r\n  commission: 0\r\n});\r\n\r\n// 用户信息\r\nconst userInfo = reactive({\r\n  userId: '1001', // 模拟数据，实际应从用户系统获取\r\n  distributorId: 'DIS1001'\r\n});\r\n\r\n// 页面加载\r\nonMounted(async () => {\r\n  // 生成推广链接\r\n  await generateLink();\r\n  \r\n  // 获取推广数据\r\n  await getPromotionData();\r\n});\r\n\r\n// 生成推广链接\r\nconst generateLink = async () => {\r\n  try {\r\n    const result = await distributionService.generatePromotionLink({\r\n      type: 'platform',\r\n      id: 'home',\r\n      distributorId: userInfo.distributorId\r\n    });\r\n    \r\n    if (result) {\r\n      Object.assign(promotionLink, result);\r\n    }\r\n  } catch (error) {\r\n    console.error('生成推广链接失败', error);\r\n    uni.showToast({\r\n      title: '生成推广链接失败',\r\n      icon: 'none'\r\n    });\r\n  }\r\n};\r\n\r\n// 获取推广数据\r\nconst getPromotionData = async () => {\r\n  try {\r\n    // 这里应该调用API获取推广数据\r\n    // 暂时使用模拟数据\r\n    promotionData.viewCount = 1256;\r\n    promotionData.orderCount = 45;\r\n    promotionData.conversionRate = 3.58;\r\n    promotionData.commission = 1256.8;\r\n  } catch (error) {\r\n    console.error('获取推广数据失败', error);\r\n  }\r\n};\r\n\r\n// 复制链接\r\nconst copyLink = () => {\r\n  if (!promotionLink.url) {\r\n    uni.showToast({\r\n      title: '链接生成中，请稍后再试',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n  \r\n  uni.setClipboardData({\r\n    data: promotionLink.url,\r\n    success: () => {\r\n      uni.showToast({\r\n        title: '链接已复制',\r\n        icon: 'success'\r\n      });\r\n    }\r\n  });\r\n};\r\n\r\n// 分享链接\r\nconst shareLink = () => {\r\n  if (!promotionLink.url) {\r\n    uni.showToast({\r\n      title: '链接生成中，请稍后再试',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n  \r\n  // 调用分享API\r\n  uni.showShareMenu({\r\n    withShareTicket: true,\r\n    menus: ['shareAppMessage', 'shareTimeline']\r\n  });\r\n};\r\n\r\n// 格式化佣金\r\nconst formatCommission = (amount) => {\r\n  return distributionService.formatCommission(amount);\r\n};\r\n\r\n// 页面导航\r\nconst navigateTo = (url) => {\r\n  uni.navigateTo({ url });\r\n};\r\n\r\n// 导航到商家营销页面\r\nconst navigateToMerchant = (url) => {\r\n  // 检查是否有权限\r\n  uni.showModal({\r\n    title: '提示',\r\n    content: '此功能需要商家权限，是否申请成为商家专属分销员？',\r\n    confirmText: '去申请',\r\n    success: (res) => {\r\n      if (res.confirm) {\r\n        uni.navigateTo({\r\n          url: '/subPackages/distribution/pages/merchant-apply'\r\n        });\r\n      }\r\n    }\r\n  });\r\n};\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 显示帮助\r\nconst showHelp = () => {\r\n  uni.showModal({\r\n    title: '推广工具帮助',\r\n    content: '推广工具页面提供了多种推广方式，包括海报、二维码、文案等。您可以选择适合的工具进行推广，获取佣金。',\r\n    showCancel: false\r\n  });\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.promotion-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: 30rpx;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\r\n  color: #fff;\r\n  padding: 88rpx 32rpx 30rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 72rpx;\r\n  height: 72rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 24rpx;\r\n  height: 24rpx;\r\n  border-left: 4rpx solid #fff;\r\n  border-bottom: 4rpx solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  letter-spacing: 1rpx;\r\n}\r\n\r\n.navbar-right {\r\n  width: 72rpx;\r\n  height: 72rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.help-icon {\r\n  width: 48rpx;\r\n  height: 48rpx;\r\n  border-radius: 24rpx;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 推广工具卡片 */\r\n.tools-grid {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: 30rpx;\r\n  background: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.tool-card {\r\n  width: 50%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 40rpx 0;\r\n  box-sizing: border-box;\r\n  border-right: 1rpx solid #f0f0f0;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.tool-card:nth-child(2n) {\r\n  border-right: none;\r\n}\r\n\r\n.tool-card:nth-child(3), .tool-card:nth-child(4) {\r\n  border-bottom: none;\r\n}\r\n\r\n.tool-icon {\r\n  width: 100rpx;\r\n  height: 100rpx;\r\n  margin-bottom: 20rpx;\r\n  border-radius: 40rpx;\r\n}\r\n\r\n.tool-icon.poster {\r\n  background-color: #FF9500;\r\n}\r\n\r\n.tool-icon.qrcode {\r\n  background-color: #409EFF;\r\n}\r\n\r\n.tool-icon.text {\r\n  background-color: #67C23A;\r\n}\r\n\r\n.tool-icon.materials {\r\n  background-color: #E6A23C;\r\n}\r\n\r\n.tool-name {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.tool-desc {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n/* 卡片通用样式 */\r\n.link-card,\r\n.guide-card,\r\n.tips-card,\r\n.data-card {\r\n  margin: 30rpx;\r\n  background: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.card-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.refresh-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 26rpx;\r\n  color: #6B0FBE;\r\n}\r\n\r\n.refresh-icon {\r\n  width: 28rpx;\r\n  height: 28rpx;\r\n  margin-right: 8rpx;\r\n  background-color: #6B0FBE;\r\n  border-radius: 40rpx;\r\n}\r\n\r\n.view-all {\r\n  font-size: 26rpx;\r\n  color: #6B0FBE;\r\n}\r\n\r\n/* 推广链接 */\r\n.link-content {\r\n  background: #F5F7FA;\r\n  border-radius: 10rpx;\r\n  padding: 20rpx;\r\n}\r\n\r\n.link-url {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  word-break: break-all;\r\n  margin-bottom: 20rpx;\r\n  display: block;\r\n}\r\n\r\n.link-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.action-btn {\r\n  font-size: 24rpx;\r\n  padding: 8rpx 30rpx;\r\n  border-radius: 30rpx;\r\n  margin-left: 20rpx;\r\n  line-height: 1.5;\r\n}\r\n\r\n.action-btn.copy {\r\n  background: #6B0FBE;\r\n  color: #fff;\r\n}\r\n\r\n.action-btn.share {\r\n  background: #FF9500;\r\n  color: #fff;\r\n}\r\n\r\n/* 推广指南 */\r\n.guide-steps {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.guide-step {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.step-icon {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  margin-bottom: 16rpx;\r\n  border-radius: 40rpx;\r\n}\r\n\r\n.step-icon.step1 {\r\n  background-color: #6B0FBE;\r\n}\r\n\r\n.step-icon.step2 {\r\n  background-color: #409EFF;\r\n}\r\n\r\n.step-icon.step3 {\r\n  background-color: #67C23A;\r\n}\r\n\r\n.step-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.step-title {\r\n  font-size: 26rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.step-desc {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.step-arrow {\r\n  width: 40rpx;\r\n  height: 20rpx;\r\n}\r\n\r\n/* 推广技巧 */\r\n.tips-list {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.tip-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.tip-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.tip-icon {\r\n  width: 16rpx;\r\n  height: 16rpx;\r\n  border-radius: 40rpx;\r\n  background-color: #6B0FBE;\r\n  margin-right: 16rpx;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.tip-text {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* 推广数据 */\r\n.data-grid {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.data-item {\r\n  width: 50%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.data-value {\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.data-label {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/distribution/pages/promotion.vue'\nwx.createPage(MiniProgramPage)"], "names": ["reactive", "onMounted", "distributionService", "uni", "MiniProgramPage"], "mappings": ";;;;;;AAkKA,UAAM,gBAAgBA,cAAAA,SAAS;AAAA,MAC7B,KAAK;AAAA,MACL,UAAU;AAAA,MACV,WAAW;AAAA,IACb,CAAC;AAGD,UAAM,gBAAgBA,cAAAA,SAAS;AAAA,MAC7B,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,IACd,CAAC;AAGD,UAAM,WAAWA,cAAAA,SAAS;AAAA,MACxB,QAAQ;AAAA;AAAA,MACR,eAAe;AAAA,IACjB,CAAC;AAGDC,kBAAAA,UAAU,YAAY;AAEpB,YAAM,aAAY;AAGlB,YAAM,iBAAgB;AAAA,IACxB,CAAC;AAGD,UAAM,eAAe,YAAY;AAC/B,UAAI;AACF,cAAM,SAAS,MAAMC,0BAAmB,oBAAC,sBAAsB;AAAA,UAC7D,MAAM;AAAA,UACN,IAAI;AAAA,UACJ,eAAe,SAAS;AAAA,QAC9B,CAAK;AAED,YAAI,QAAQ;AACV,iBAAO,OAAO,eAAe,MAAM;AAAA,QACpC;AAAA,MACF,SAAQ,OAAO;AACdC,sBAAA,MAAA,MAAA,SAAA,uDAAc,YAAY,KAAK;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,mBAAmB,YAAY;AACnC,UAAI;AAGF,sBAAc,YAAY;AAC1B,sBAAc,aAAa;AAC3B,sBAAc,iBAAiB;AAC/B,sBAAc,aAAa;AAAA,MAC5B,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,uDAAc,YAAY,KAAK;AAAA,MAChC;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrB,UAAI,CAAC,cAAc,KAAK;AACtBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAEDA,oBAAAA,MAAI,iBAAiB;AAAA,QACnB,MAAM,cAAc;AAAA,QACpB,SAAS,MAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,YAAY,MAAM;AACtB,UAAI,CAAC,cAAc,KAAK;AACtBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAGDA,oBAAAA,MAAI,cAAc;AAAA,QAChB,iBAAiB;AAAA,QACjB,OAAO,CAAC,mBAAmB,eAAe;AAAA,MAC9C,CAAG;AAAA,IACH;AAGA,UAAM,mBAAmB,CAAC,WAAW;AACnC,aAAOD,0BAAmB,oBAAC,iBAAiB,MAAM;AAAA,IACpD;AAGA,UAAM,aAAa,CAAC,QAAQ;AAC1BC,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IACxB;AAGA,UAAM,qBAAqB,CAAC,QAAQ;AAElCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,WAAW;AAAA,cACb,KAAK;AAAA,YACf,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MAChB,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;AC9SA,GAAG,WAAWC,SAAe;"}