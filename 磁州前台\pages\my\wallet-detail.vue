<template>
  <view class="wallet-detail-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">钱包明细</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 账户余额卡片 -->
    <view class="balance-card" :style="{ marginTop: (navbarHeight + 10) + 'px' }">
      <view class="balance-title">账户余额 (元)</view>
      <view class="balance-amount">{{ balanceInfo.amount.toFixed(2) }}</view>
      <view class="balance-info">
        <view class="info-item">
          <view class="info-value">{{ balanceInfo.totalIncome.toFixed(2) }}</view>
          <view class="info-label">总收入</view>
        </view>
        <view class="info-divider"></view>
        <view class="info-item">
          <view class="info-value">{{ balanceInfo.totalExpense.toFixed(2) }}</view>
          <view class="info-label">总支出</view>
        </view>
      </view>
    </view>
    
    <!-- 交易类型筛选 -->
    <view class="filter-tabs">
      <view 
        class="filter-tab" 
        v-for="(tab, index) in tabs" 
        :key="index"
        :class="{'active': activeTab === index}"
        @click="switchTab(index)"
      >
        {{ tab.name }}
      </view>
    </view>
    
    <!-- 交易记录列表 -->
    <view class="transaction-list">
      <view v-if="filteredTransactions.length > 0">
        <!-- 按月分组的交易记录 -->
        <block v-for="(group, month) in groupedTransactions" :key="month">
          <view class="month-header">{{ month }}</view>
          
          <view class="transaction-item" v-for="(item, index) in group" :key="index">
            <view class="transaction-left">
              <view class="transaction-icon" :class="getTransactionTypeClass(item.type)">
                <image :src="getTransactionTypeIcon(item.type)" class="type-icon"></image>
              </view>
            </view>
            <view class="transaction-center">
              <view class="transaction-title">{{ item.title }}</view>
              <view class="transaction-time">{{ item.time }}</view>
            </view>
            <view class="transaction-right">
              <view class="transaction-amount" :class="{'income': item.type === 'income', 'expense': item.type === 'expense'}">
                {{ item.type === 'income' ? '+' : '-' }}{{ item.amount.toFixed(2) }}
              </view>
              <view class="transaction-status">{{ item.status }}</view>
            </view>
          </view>
        </block>
        
        <!-- 加载更多 -->
        <view class="load-more" v-if="hasMoreData" @click="loadMoreData">
          <text>加载更多</text>
        </view>
        <view class="no-more" v-else>
          <text>没有更多数据了</text>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view v-else class="empty-view">
        <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
        <view class="empty-text">暂无交易记录</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20,
      navbarHeight: 64,
      balanceInfo: {
        amount: 158.50,
        totalIncome: 200.00,
        totalExpense: 41.50,
        frozenAmount: 0.00
      },
      activeTab: 0,
      tabs: [
        { name: '全部', type: 'all' },
        { name: '收入', type: 'income' },
        { name: '支出', type: 'expense' }
      ],
      transactions: [
        {
          id: 'tx001',
          title: '充值',
          time: '2023-11-05 14:30',
          amount: 100.00,
          type: 'income',
          status: '已完成',
          month: '2023年11月'
        },
        {
          id: 'tx002',
          title: '服务支付',
          time: '2023-11-03 09:15',
          amount: 35.00,
          type: 'expense',
          status: '已完成',
          month: '2023年11月'
        },
        {
          id: 'tx003',
          title: '提现',
          time: '2023-10-28 16:22',
          amount: 50.00,
          type: 'expense',
          status: '已完成',
          month: '2023年10月'
        },
        {
          id: 'tx004',
          title: '充值',
          time: '2023-10-15 11:05',
          amount: 100.00,
          type: 'income',
          status: '已完成',
          month: '2023年10月'
        },
        {
          id: 'tx005',
          title: '任务收入',
          time: '2023-09-30 18:45',
          amount: 88.00,
          type: 'income',
          status: '已完成',
          month: '2023年9月'
        }
      ],
      page: 1,
      pageSize: 10,
      hasMoreData: true
    }
  },
  computed: {
    // 根据当前选中的标签筛选交易记录
    filteredTransactions() {
      if (this.activeTab === 0) {
        return this.transactions;
      } else {
        const type = this.tabs[this.activeTab].type;
        return this.transactions.filter(item => item.type === type);
      }
    },
    
    // 按月份分组交易记录
    groupedTransactions() {
      const groups = {};
      
      this.filteredTransactions.forEach(item => {
        if (!groups[item.month]) {
          groups[item.month] = [];
        }
        groups[item.month].push(item);
      });
      
      return groups;
    }
  },
  onLoad() {
    // 获取状态栏高度
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 44;
    
    // 获取钱包余额和交易记录
    this.getWalletBalance();
    this.getTransactions();
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 切换标签
    switchTab(index) {
      this.activeTab = index;
    },
    
    // 获取钱包余额
    getWalletBalance() {
      // 这里应该是从API获取钱包余额
      // 模拟API请求
      setTimeout(() => {
        this.balanceInfo = {
          amount: 158.50,
          totalIncome: 200.00,
          totalExpense: 41.50,
          frozenAmount: 0.00
        };
      }, 500);
    },
    
    // 获取交易记录
    getTransactions() {
      // 模拟API请求获取交易记录
      // 实际应用中应该从服务器获取数据
      setTimeout(() => {
        // 如果是第一页，直接替换数据
        if (this.page === 1) {
          // 数据已经在data中初始化
        } else {
          // 如果是加载更多，追加数据
          if (this.page >= 3) {
            this.hasMoreData = false;
          } else {
            // 模拟加载更多数据
            const moreData = [
              {
                id: 'tx006',
                title: '服务支付',
                time: '2023-09-22 10:30',
                amount: 25.00,
                type: 'expense',
                status: '已完成',
                month: '2023年9月'
              },
              {
                id: 'tx007',
                title: '充值',
                time: '2023-09-15 16:40',
                amount: 50.00,
                type: 'income',
                status: '已完成',
                month: '2023年9月'
              }
            ];
            this.transactions = [...this.transactions, ...moreData];
          }
        }
      }, 500);
    },
    
    // 加载更多数据
    loadMoreData() {
      if (!this.hasMoreData) return;
      
      this.page++;
      this.getTransactions();
    },
    
    // 获取交易类型对应的图标
    getTransactionTypeIcon(type) {
      const icons = {
        'income': '/static/images/tabbar/收入.png',
        'expense': '/static/images/tabbar/支出.png'
      };
      return icons[type] || icons['income'];
    },
    
    // 获取交易类型对应的样式类
    getTransactionTypeClass(type) {
      return {
        'income-icon': type === 'income',
        'expense-icon': type === 'expense'
      };
    }
  }
}
</script>

<style>
.wallet-detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 30rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
}

.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.navbar-right {
  width: 80rpx;
}

/* 余额卡片 */
.balance-card {
  background: linear-gradient(to right, #0052CC, #0066FF);
  margin: 0 30rpx;
  padding: 30rpx;
  border-radius: 20rpx;
  color: #fff;
  overflow: hidden;
  box-shadow: 0 8rpx 20rpx rgba(0, 82, 204, 0.2);
}

.balance-title {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 20rpx;
}

.balance-amount {
  font-size: 60rpx;
  font-weight: bold;
  margin-bottom: 40rpx;
}

.balance-info {
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 20rpx;
  border-radius: 10rpx;
}

.info-item {
  text-align: center;
}

.info-value {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.info-label {
  font-size: 24rpx;
  opacity: 0.8;
}

.info-divider {
  width: 1px;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.3);
}

/* 筛选标签 */
.filter-tabs {
  display: flex;
  background-color: #fff;
  margin: 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.filter-tab.active {
  color: #0052CC;
  font-weight: 500;
}

.filter-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #0052CC;
  border-radius: 2rpx;
}

/* 交易记录列表 */
.transaction-list {
  margin: 0 30rpx;
}

.month-header {
  font-size: 24rpx;
  color: #999;
  padding: 20rpx 0;
}

.transaction-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.transaction-left {
  margin-right: 20rpx;
}

.transaction-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.income-icon {
  background-color: rgba(7, 193, 96, 0.1);
}

.expense-icon {
  background-color: rgba(245, 108, 108, 0.1);
}

.type-icon {
  width: 40rpx;
  height: 40rpx;
}

.transaction-center {
  flex: 1;
}

.transaction-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.transaction-time {
  font-size: 24rpx;
  color: #999;
}

.transaction-right {
  text-align: right;
}

.transaction-amount {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.income {
  color: #07c160;
}

.expense {
  color: #f56c6c;
}

.transaction-status {
  font-size: 24rpx;
  color: #999;
}

/* 加载更多 */
.load-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 24rpx;
  color: #999;
}

.load-more {
  color: #0052CC;
}

/* 空状态 */
.empty-view {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style>