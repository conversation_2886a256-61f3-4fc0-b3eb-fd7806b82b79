<template>
  <view class="post-detail-container">
    <!-- 隐藏的分享按钮，用于自动触发 -->
    <button id="shareButton" class="hidden-share-btn" open-type="share"></button>
    
    <view class="post-detail-wrapper">
      <!-- 信息主体卡片 -->
      <view class="content-card main-info">
        <view class="post-header">
          <view class="post-title">{{ post.title }}</view>
          <view class="post-meta">
            <text class="post-time">发布于 {{ formatTime(post.createdAt) }}</text>
            <text class="post-category">{{ post.category }}</text>
            <view class="post-views">
              <text class="iconfont icon-eye"></text>
              <text>{{ post.views }}</text>
            </view>
          </view>
        </view>

        <view class="post-gallery" v-if="post.images && post.images.length > 0">
          <swiper class="swiper" 
                indicator-dots 
                :autoplay="true" 
                :interval="4000" 
                :duration="500" 
                circular
                v-if="post.images.length > 1">
            <swiper-item v-for="(image, index) in post.images" :key="index" @click="previewImage(index)">
              <image :src="image" class="carousel-image" mode="aspectFill"></image>
          </swiper-item>
        </swiper>
          <view class="single-image" v-else>
            <image :src="post.images[0]" @click="previewImage(0)" mode="widthFix"></image>
          </view>
        </view>

        <view class="divider"></view>

        <view class="post-content">
          <rich-text :nodes="post.content"></rich-text>
        </view>

        <view class="post-tags" v-if="post.tags && post.tags.length > 0">
          <uni-tag v-for="tag in post.tags" :key="tag" :text="tag" size="small" type="default" :inverted="true" class="tag"></uni-tag>
        </view>
      </view>

      <!-- 发布者信息卡片 -->
      <view class="content-card publisher-info">
        <view class="publisher-header">
          <text class="card-title">发布者信息</text>
        </view>
        <view class="publisher-content">
          <view class="publisher-avatar">
            <image class="avatar-image" :src="post.publisher?.avatar" mode="aspectFill"></image>
          </view>
          <view class="publisher-details">
            <view class="publisher-name">{{ post.publisher?.username }}</view>
            <view class="publisher-stats">
              <view class="stat-item">
                <view class="stat-value">{{ post.publisher?.posts || 0 }}</view>
                <view class="stat-label">发布</view>
              </view>
              <view class="stat-item">
                <view class="stat-value">{{ post.publisher?.followers || 0 }}</view>
                <view class="stat-label">粉丝</view>
              </view>
              <view class="stat-item">
                <view class="stat-value">{{ post.publisher?.rating || '5.0' }}</view>
                <view class="stat-label">评分</view>
              </view>
            </view>
          </view>
          <button class="contact-btn" type="primary" size="mini" @click="contactPublisher">联系TA</button>
        </view>
      </view>

      <!-- 位置信息卡片 -->
      <view class="content-card location-info" v-if="post.location">
        <view class="location-header">
          <text class="card-title">位置信息</text>
        </view>
        <view class="location-content">
          <text class="iconfont icon-location"></text>
          <text>{{ post.location.address }}</text>
        </view>
        <view class="location-map">
          <image src="https://via.placeholder.com/600x200?text=Map+Preview" mode="widthFix" class="map-preview"></image>
        </view>
      </view>

      <!-- 评论区卡片 -->
      <view class="content-card comment-section">
        <view class="comment-header">
          <text class="card-title">评论区</text>
        </view>
        <!-- 评论组件，可以自定义或使用uni-app插件 -->
        <view class="comment-list">
          <view class="comment-empty" v-if="comments.length === 0">
            <text>暂无评论，快来发表第一条评论吧！</text>
          </view>
          <view class="comment-item" v-for="(comment, index) in comments" :key="index">
            <view class="comment-user">
              <image class="comment-avatar" :src="comment.avatar" mode="aspectFill"></image>
              <view class="comment-user-info">
                <text class="comment-username">{{ comment.username }}</text>
                <text class="comment-time">{{ formatTime(comment.createdAt) }}</text>
              </view>
            </view>
            <view class="comment-content">{{ comment.content }}</view>
          </view>
        </view>
        <view class="comment-input-area">
          <input class="comment-input" type="text" placeholder="写下你的评论..." v-model="commentText" confirm-type="send" @confirm="submitComment" />
          <button class="comment-submit" type="primary" size="mini" @click="submitComment">发送</button>
      </view>
      </view>
      
      <!-- 相关信息推荐 -->
      <view class="content-card related-posts-card">
        <view class="section-title">相关信息推荐</view>
        <view class="related-posts-content">
          <!-- 简洁的信息列表 -->
          <view class="related-posts-list">
            <view class="related-post-item" 
                 v-for="(relatedPost, index) in relatedPosts.slice(0, 3)" 
                 :key="index" 
                 @click="navigateToPostDetail(relatedPost.id)">
              <view class="post-item-content">
                <view class="post-item-left" v-if="relatedPost.image">
                  <image class="post-image" :src="relatedPost.image" mode="aspectFill"></image>
                </view>
                <view class="post-item-middle">
                  <text class="post-item-title">{{relatedPost.title}}</text>
                  <view class="post-item-category">{{relatedPost.category}}</view>
                  <view class="post-item-meta">
                    <text class="post-item-time">{{formatTime(relatedPost.createdAt)}}</text>
                    <text class="post-item-views">{{relatedPost.views}}浏览</text>
                  </view>
                </view>
              </view>
            </view>
            
            <!-- 暂无数据提示 -->
            <view class="empty-related-posts" v-if="relatedPosts.length === 0">
              <image src="/static/images/empty.png" class="empty-image" mode="aspectFit"></image>
              <text class="empty-text">暂无相关信息</text>
            </view>
          </view>
          
          <!-- 查看更多按钮 -->
          <view class="view-more-btn" v-if="relatedPosts.length > 0" @click.stop="navigateToInfoList">
            <text class="view-more-text">查看更多信息</text>
            <text class="view-more-icon iconfont icon-right"></text>
          </view>
        </view>
      </view>

      <RedPacketEntry v-model="form.redPacket" />
      
      <!-- 添加红包展示部分 -->
      <view class="content-card red-packet-card" v-if="form.redPacket && form.redPacket.amount">
        <view class="section-title">红包福利</view>
        <view class="red-packet-details">
          <view class="red-packet-amount">
            <text class="amount-label">红包金额</text>
            <text class="amount-value">{{form.redPacket.amount}}元</text>
          </view>
          <view class="red-packet-usage">
            <text class="usage-label">红包个数</text>
            <text class="usage-value">共{{form.redPacket.count}}个 剩余{{form.redPacket.remain}}个</text>
          </view>
          <view class="red-packet-method">
            <text class="method-label">红包类型</text>
            <text class="method-value">{{form.redPacket.type === 'fixed' ? '普通红包' : '拼手气红包'}}</text>
          </view>
        </view>
        <view class="red-packet-receive" @click="receiveRedPacket">
          立即领取
        </view>
      </view>
    </view>

    <!-- 底部互动工具栏 -->
    <view class="interaction-toolbar">
      <view class="toolbar-item" @click="goToHome">
        <image src="/static/images/tabbar/a首页.png" class="toolbar-icon"></image>
        <text class="toolbar-text">首页</text>
      </view>
      <view class="toolbar-item" @click="toggleCollect">
        <image src="/static/images/tabbar/a收藏.png" class="toolbar-icon"></image>
        <text class="toolbar-text">收藏</text>
      </view>
      <button class="share-button toolbar-item" open-type="share">
        <image src="/static/images/tabbar/a分享.png" class="toolbar-icon"></image>
        <text class="toolbar-text">分享</text>
      </button>
      <view class="toolbar-item" @click="openChat">
        <image src="/static/images/tabbar/a消息.png" class="toolbar-icon"></image>
        <text class="toolbar-text">私信</text>
      </view>
      <view class="toolbar-item call-button" @click="makePhoneCall">
        <view class="call-button-content">
          <text class="call-text">打电话</text>
          <text class="call-subtitle">请说在磁州生活网看到的</text>
        </view>
      </view>
    </view>

    <!-- 修改分享引导浮层，替换为更简单的UI并添加直接分享按钮 -->
    <view class="share-guide-layer" v-if="showShareGuideLayer" @click="showShareGuideLayer = false">
      <view class="guide-content" @click.stop>
        <view class="guide-close" @click="showShareGuideLayer = false">×</view>
        <view class="guide-title">一键分享</view>
        <view class="guide-desc">点击下方按钮，即可分享给朋友或群聊</view>
        <view class="guide-tips">分享可获得信息置顶特权</view>
        
        <!-- 直接添加分享按钮 -->
        <button class="guide-share-btn" open-type="share">
          立即分享
        </button>
      </view>
    </view>

    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <text class="navbar-title">信息详情</text>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import RedPacketEntry from '@/components/RedPacket/RedPacketEntry.vue'

// 格式化时间函数
const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  return `${date.getMonth() + 1}月${date.getDate()}日`;
};

// 响应式数据
const postId = ref('')
const commentText = ref('')
const comments = ref([])
const isLiked = ref(false)
const isCollected = ref(false)
const shareButtonHighlight = ref(false)
const showShareGuideLayer = ref(false)
const post = ref({
  id: '',
  title: '精装修三室两厅，交通便利，拎包入住',
  content: '位于城市中心，周边配套设施齐全，距离地铁站仅500米。房屋为精装修，家具家电齐全，随时可以入住。小区环境优美，安静舒适，24小时保安巡逻，安全有保障。适合一家人居住，看房方便，欢迎随时联系。',
  category: '房屋出租',
  createdAt: Date.now() - 3600000 * 24 * 3,
  views: 256,
  tags: ['精装修', '地铁附近', '拎包入住', '家电齐全'],
        images: [
    'https://via.placeholder.com/800x450?text=Room+1',
    'https://via.placeholder.com/800x450?text=Room+2',
    'https://via.placeholder.com/800x450?text=Room+3',
  ],
  publisher: {
    username: '房产小能手',
    avatar: 'https://via.placeholder.com/100?text=Avatar',
    posts: 32,
    followers: 128,
    rating: 4.8,
    phone: '13912345678'
  },
  location: {
    address: '某某市某某区某某街123号',
    latitude: 30.123456,
    longitude: 120.123456
  }
})

// 相关信息列表
const relatedPosts = ref([]);

// 加载相关信息
const loadRelatedPosts = () => {
  // 这里可以调用API获取数据
  // 实际项目中应该根据当前信息的分类、标签等进行相关性匹配
  
  // 模拟数据
  setTimeout(() => {
    relatedPosts.value = [
      {
        id: 'post001',
        title: '全新装修两室一厅出租，家电齐全',
        category: '房屋出租',
        createdAt: Date.now() - 86400000 * 1,
        views: 158,
        image: 'https://via.placeholder.com/100?text=Room'
      },
      {
        id: 'post002',
        title: '市中心单身公寓，拎包入住',
        category: '房屋出租',
        createdAt: Date.now() - 86400000 * 2,
        views: 243,
        image: 'https://via.placeholder.com/100?text=Apartment'
      },
      {
        id: 'post003',
        title: '南北通透三居室，学区房，周边配套齐全',
        category: '房屋出租',
        createdAt: Date.now() - 86400000 * 3,
        views: 325,
        image: 'https://via.placeholder.com/100?text=House'
      }
    ];
  }, 500);
};

// 跳转到信息详情页
const navigateToPostDetail = (id) => {
  // 避免重复跳转当前页面
  if (id === postId.value) {
    return;
  }
  
  uni.navigateTo({
    url: `/pages/publish/info-detail?id=${id}`
  });
};

// 跳转到信息列表页
const navigateToInfoList = (e) => {
  if (e) e.stopPropagation();
  const infoCategory = post.value.tags?.[0] || '';
  uni.navigateTo({ 
    url: `/subPackages/service/pages/filter?type=info&title=${encodeURIComponent('生活信息')}&category=${encodeURIComponent(infoCategory)}&active=info` 
  });
};

// 生命周期钩子
onMounted(() => {
  // 修改页面标题和背景色
  uni.setNavigationBarTitle({
    title: '信息详情'
  })
  
  uni.setNavigationBarColor({
    frontColor: '#ffffff',
    backgroundColor: '#0052CC'
  })
  
  // 获取路由参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.$page?.options || {}
  postId.value = options.id || '1'
  post.value.id = postId.value
  console.log('加载信息详情:', postId.value)
  
  // 加载相关信息推荐
  loadRelatedPosts()
  
  // 检查是否需要直接分享
  const shouldDirectShare = options.directShare === '1' || uni.getStorageSync('directShare') === 1
  
  // 清除存储的标记
  if (uni.getStorageSync('directShare')) {
    uni.removeStorageSync('directShare')
  }
  
  // 处理autoShare参数，自动打开分享菜单
  if (options.autoShare === 'true') {
    console.log('检测到autoShare参数，准备显示分享菜单')
    
    // 延迟执行，确保页面已完全加载
    setTimeout(() => {
      // 直接显示分享引导层
      showShareGuideLayer.value = true
    }, 500)
  }
  
  // 如果需要直接分享，模拟点击分享按钮
  if (shouldDirectShare) {
    console.log('检测到直接分享标记，准备直接分享')
    
    // 延迟一点以确保页面加载完成，微信有时候需要一点时间准备分享功能
    setTimeout(() => {
      // 尝试直接触发分享
      triggerDirectShare()
    }, 1000)
  }
  
  // 模拟评论数据
  comments.value = [
    {
      id: 1,
      username: '用户123',
      avatar: 'https://via.placeholder.com/40?text=U1',
      content: '房子位置很好，周边设施齐全，交通便利。',
      createdAt: Date.now() - 3600000 * 48
    },
    {
      id: 2,
      username: '小明',
      avatar: 'https://via.placeholder.com/40?text=U2',
      content: '请问有停车位吗？',
      createdAt: Date.now() - 3600000 * 24
    }
  ]
  
  // 处理红包信息
  const { hasRedPacket, redPacketAmount, redPacketType, redPacketCount, redPacketRemain } = options;
  if (hasRedPacket === '1' && redPacketAmount) {
    form.value.redPacket = {
      amount: redPacketAmount,
      count: redPacketCount || '0',
      remain: redPacketRemain || '0',
      type: redPacketType || 'fixed',
      taskType: 0 // 默认任务类型
    };
    console.log('设置红包信息:', JSON.stringify(form.value.redPacket));
  }
})

// 添加一个方法用于触发分享
const triggerShare = () => {
  // 在小程序环境中，尝试使用API触发分享
  if (uni.canIUse('showShareMenu')) {
  uni.showShareMenu({
    withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline'],
    success: () => {
        console.log('显示分享菜单成功')
      
        // 尝试模拟点击分享按钮
      setTimeout(() => {
          // 获取页面中的所有 button 元素
          const query = uni.createSelectorQuery()
          query.selectAll('.share-button').boundingClientRect()
          query.exec((res) => {
            if (res && res[0] && res[0].length > 0) {
              console.log('找到分享按钮，尝试模拟点击')
              
              // 由于无法直接模拟点击，我们可以通过其他方式提示用户
              uni.showModal({
                title: '分享提示',
                content: '请点击"分享"按钮将信息分享给好友或群聊',
                showCancel: false,
            success: () => {
                  // 用户确认后，保持分享引导层显示
                  showShareGuideLayer.value = true
                }
              })
            } else {
              console.log('未找到分享按钮')
                }
        })
      }, 500)
      },
      fail: (err) => {
        console.error('显示分享菜单失败', err)
    }
  })
  } else {
    // 如果API不可用，显示分享引导层
    showShareGuideLayer.value = true
  }
}

const previewImage = (index) => {
      uni.previewImage({
        current: index,
    urls: post.value.images
  })
}

const goBack = () => {
  uni.navigateBack()
}

const contactPublisher = () => {
  // 假设发布者电话号码保存在post.publisher.phone中
  const phone = post.value.publisher?.phone || '13800138000'; // 默认值为示例电话
  
  // 显示确认提示
  uni.showModal({
    title: '联系发布者',
    content: `确定要拨打${phone}吗？\n记得说在"磁州生活网"看到的哦~`,
    confirmText: '立即拨打',
    confirmColor: '#0052CC',
    success: (res) => {
      if (res.confirm) {
        // 拨打电话
        uni.makePhoneCall({
          phoneNumber: phone,
          success: () => {
            console.log('拨打电话成功');
          },
          fail: (err) => {
            console.error('拨打电话失败', err);
            uni.showToast({
              title: '拨打电话失败',
              icon: 'none'
            });
          }
        });
      }
    }
  });
}

// 打开私信聊天
const openChat = () => {
      uni.navigateTo({
    url: `/pages/chat/index?userId=${post.value.publisher.id}&username=${encodeURIComponent(post.value.publisher.username)}`
  });
};

const submitComment = () => {
  if (!commentText.value.trim()) {
    uni.showToast({
      title: '评论内容不能为空',
      icon: 'none'
    })
    return
  }
  
  // 模拟添加评论
  comments.value.unshift({
    id: Date.now(),
    username: '当前用户',
    avatar: 'https://via.placeholder.com/40?text=Me',
    content: commentText.value,
    createdAt: Date.now()
  })
  
  commentText.value = ''
  
  uni.showToast({
    title: '评论成功',
    icon: 'success'
  })
}

const toggleLike = () => {
  isLiked.value = !isLiked.value
  uni.showToast({
    title: isLiked.value ? '点赞成功' : '已取消点赞',
    icon: 'none'
  })
}

const toggleCollect = () => {
  isCollected.value = !isCollected.value
  uni.showToast({
    title: isCollected.value ? '收藏成功' : '已取消收藏',
    icon: 'none'
  })
}

// 跳转到首页
const goToHome = () => {
  uni.switchTab({
    url: '/pages/index/index'
  });
};

// 打电话
const makePhoneCall = () => {
  // 判断是否有联系电话
  if (post.value.publisher && post.value.publisher.phone) {
    uni.makePhoneCall({
      phoneNumber: post.value.publisher.phone,
      success: () => {
        console.log('拨打电话成功');
      },
      fail: (err) => {
        console.error('拨打电话失败:', err);
        uni.showToast({
          title: '拨打电话失败',
          icon: 'none'
        });
      }
    });
  } else {
    // 如果没有电话，提示用户
    uni.showModal({
      title: '提示',
      content: '该信息暂无联系电话，请通过私信联系发布者',
      showCancel: false
    });
  }
};

// 加载信息详情数据
const loadInfoData = (id) => {
  // 判断是否是从发布页过来的临时数据
  if (id && id.startsWith('temp_')) {
    // 从本地缓存中获取数据
    const publishDataList = uni.getStorageSync('publishDataList') || [];
    const publishData = publishDataList.find(item => item.id === id);
    
    if (publishData) {
      // 更新信息数据
      updateInfoDataFromPublish(publishData);
    } else {
      uni.showToast({
        title: '数据加载失败',
        icon: 'none'
      });
    }
  } else {
    // 从服务器获取数据（这里使用模拟数据）
    console.log('获取信息ID:', id);
    // 保持默认的静态数据
  }
};

// 根据发布数据更新信息详情
const updateInfoDataFromPublish = (publishData) => {
  // 更新基本信息
  post.value = {
    ...post.value,
    id: publishData.id,
    title: publishData.title || '便民信息',
    price: publishData.price || '价格待定',
    publishTime: publishData.publishTime || Date.now(),
    viewCount: Math.floor(Math.random() * 100) + 50, // 随机生成浏览量
    location: publishData.location || '磁县城区',
    description: publishData.introduction || publishData.description || post.value.description
  };
  
  // 更新联系信息
  if (publishData.contact && publishData.phone) {
    post.value.contact = {
      name: publishData.contact,
      phone: publishData.phone
    };
    
    // 同步更新发布者信息
    post.value.publisher = {
      id: `user_${Date.now()}`,
      name: publishData.contact,
      avatar: '/static/images/avatar.jpg',
      rating: 4.5,
      publishCount: Math.floor(Math.random() * 10) + 1
    };
  }
  
  // 更新图片
  if (publishData.images && publishData.images.length > 0) {
    post.value.images = publishData.images;
  }
};

// 添加领取红包功能
const receiveRedPacket = () => {
      uni.showModal({
    title: '领取红包',
    content: '确定要领取这个红包吗？需要完成转发或助力任务后才能获得。',
        success: (res) => {
          if (res.confirm) {
        // 实际应用中应该调用API来领取红包
        uni.showToast({
          title: '领取成功，请完成任务',
          icon: 'success'
            });
          }
        }
      });
};

// 添加直接触发分享的方法
const triggerDirectShare = () => {
  console.log('尝试直接触发分享')
    
  // 微信小程序无法直接通过代码触发分享，但我们可以尝试以下方法
  
  // 1. 设置分享目标可见，并显示提示
  uni.showShareMenu({
    withShareTicket: true,
    showShareItems: ['wechatFriends', 'wechatMoment'],
    success: () => {
      console.log('成功显示分享菜单')
      
      // 2. 尝试寻找页面中的分享按钮并模拟点击
      setTimeout(() => {
        // 这里尝试通过DOM模拟点击，但可能不是在所有平台都有效
        // #ifdef H5
        try {
          const shareBtn = document.querySelector('#shareButton')
          if (shareBtn) {
            console.log('找到隐藏的分享按钮，尝试点击')
            shareBtn.click()
          }
        } catch (e) {
          console.error('尝试触发分享按钮失败', e)
        }
        // #endif
        
        // 3. 如果以上方法都失败，使用系统API尝试呼出分享菜单
        // #ifdef MP-WEIXIN
        try {
          // 微信小程序分享API
          wx.showShareMenu({
            withShareTicket: true,
            menus: ['shareAppMessage', 'shareTimeline'],
            success: () => {
              console.log('微信API成功显示分享菜单')
              
              // 除了显示分享菜单，还可以尝试弹出更明确的提示
              wx.updateShareMenu({
                withShareTicket: true,
                success: () => {
                  console.log('更新分享菜单成功')
                  }
                })
            }
          })
        } catch (e) {
          console.error('微信分享API调用失败', e)
        }
        // #endif
        
        // 4. 最后的备用方案：使用动画提示
        uni.showToast({
          title: '请点击右上角进行分享',
          icon: 'none',
          duration: 2000
        })
      }, 500)
    }
  })
}

const form = reactive({
  redPacket: null
})
</script>

<style>
/* 全局样式 */
.post-detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 110rpx;
  padding-top: 150rpx; /* 增加顶部内边距，为固定导航栏留出空间 */
}

.post-detail-wrapper {
  max-width: 750rpx;
  margin: 0 auto;
  padding: 24rpx;
  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */
}

/* 通用卡片样式 */
.content-card {
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
  padding: 30rpx;
}

.card-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

/* 信息主体卡片 */
.main-info {
  margin-top: 0; /* 调整顶部边距，因为现在使用系统导航栏 */
}

.post-header {
  margin-bottom: 30rpx;
}

.post-title {
  font-size: 38rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.4;
}

.post-meta {
  display: flex;
  flex-wrap: wrap;
  color: #8c8c8c;
  font-size: 26rpx;
  gap: 20rpx;
  align-items: center;
}

.post-category {
  color: #0052CC;
  background-color: rgba(0, 82, 204, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 8rpx;
}

.post-gallery {
  margin: 30rpx 0;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.08);
}

.swiper {
  height: 400rpx;
  border-radius: 12rpx;
}

.carousel-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.single-image image {
  width: 100%;
  border-radius: 12rpx;
}

.divider {
  height: 2rpx;
  background-color: #f0f0f0;
  margin: 30rpx 0;
}

.post-content {
  font-size: 30rpx;
  line-height: 1.8;
  color: #444;
  margin-bottom: 30rpx;
}

.post-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 20rpx;
}

.tag {
  margin-right: 0;
}

/* 发布者信息卡片 */
.publisher-header {
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 20rpx;
}

.publisher-content {
  display: flex;
  align-items: center;
}

.publisher-avatar {
  margin-right: 20rpx;
}

.avatar-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}

.publisher-details {
  flex-grow: 1;
}

.publisher-name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  color: #333;
}

.publisher-stats {
  display: flex;
  gap: 40rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.stat-label {
  font-size: 24rpx;
  color: #8c8c8c;
}

.contact-btn {
  padding: 10rpx 30rpx;
  border-radius: 40rpx;
}

/* 位置信息卡片样式 */
.location-info {
  margin-bottom: 20rpx;
}

.location-content {
  display: flex;
  align-items: center;
  padding: 15rpx 0;
}

.location-content .iconfont {
  font-size: 32rpx;
  color: #0052CC;
  margin-right: 10rpx;
}

.map-preview {
  width: 100%;
  border-radius: 10rpx;
  margin-top: 10rpx;
}

/* 评论区样式 */
.comment-header {
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 20rpx;
}

.comment-list {
  margin-bottom: 30rpx;
}

.comment-empty {
  text-align: center;
  color: #999;
  padding: 40rpx 0;
}

.comment-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.comment-user {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.comment-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 15rpx;
}

.comment-user-info {
  flex: 1;
}

.comment-username {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.comment-time {
  font-size: 24rpx;
  color: #999;
  margin-left: 15rpx;
}

.comment-content {
  font-size: 28rpx;
  color: #444;
  line-height: 1.5;
  padding-left: 75rpx;
}

.comment-input-area {
  display: flex;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.comment-input {
  flex: 1;
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 35rpx;
  padding: 0 20rpx;
  margin-right: 20rpx;
}

.comment-submit {
  border-radius: 35rpx;
}

/* 适配样式 */
@media screen and (max-width: 375px) {
  .post-detail-wrapper {
    padding: 0 20rpx;
  }
  
  .post-title {
    font-size: 34rpx;
  }
  
  .content-card {
    padding: 20rpx;
  }
  
  .related-item {
    width: 200rpx;
  }
}

/* 底部互动工具栏 */
.interaction-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10rpx 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  height: 120rpx;
  z-index: 100;
}

.toolbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6rpx 0;
  margin: 0 4rpx;
}

.toolbar-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}

.toolbar-text {
  font-size: 22rpx;
  color: #666;
}

.share-button {
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
  line-height: normal;
  border-radius: 0;
  flex: 1;
}

.share-button::after {
  display: none;
}

.call-button {
  flex: 3; /* 占据更多空间，原来是2，现在改为3让按钮更长 */
  background: linear-gradient(135deg, #0052CC, #0066FF);
  height: 90rpx;
  margin: 0 0 0 10rpx;
  border-radius: 45rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
}

.call-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.call-text {
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  line-height: 1.2;
}

.call-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 20rpx;
  line-height: 1.2;
}

/* 隐藏的分享按钮 */
.hidden-share-btn {
  position: fixed;
  width: 2rpx;
  height: 2rpx;
  opacity: 0;
  top: -999rpx;
  left: -999rpx;
  z-index: -1;
  overflow: hidden;
  padding: 0;
  margin: 0;
  border: none;
}

.hidden-share-btn::after {
  display: none;
}

/* 添加分享按钮高亮样式 */
.share-button-highlight {
  animation: shareButtonPulse 0.8s infinite alternate;
  background-color: rgba(0, 102, 255, 0.1);
  border-radius: 8rpx;
}

@keyframes shareButtonPulse {
  from { transform: scale(1); }
  to { transform: scale(1.1); background-color: rgba(0, 102, 255, 0.2); }
}

/* 分享引导浮层 */
.share-guide-layer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.guide-content {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  width: 80%;
  max-width: 600rpx;
  text-align: center;
  position: relative;
}

.guide-close {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #333;
  transition: transform 0.3s;
}

.guide-close:active {
  transform: scale(0.9);
}

.guide-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.guide-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.guide-tips {
  font-size: 26rpx;
  color: #ff6600;
  font-weight: bold;
  margin-bottom: 30rpx;
}

/* 分享按钮样式 */
.guide-share-btn {
  background-color: #007AFF;
  color: #FFFFFF !important;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 40rpx;
  padding: 20rpx 0;
  width: 80%;
  margin: 30rpx auto 10rpx;
  box-shadow: 0 6rpx 15rpx rgba(0, 122, 255, 0.3);
  border: none;
  line-height: 1.5;
}

.guide-share-btn::after {
  display: none;
}

/* 相关信息推荐样式 */
.related-posts-card {
  margin-top: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 16rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 6rpx;
  height: 28rpx;
  background-color: #0052CC;
  border-radius: 3rpx;
}

.related-posts-content {
  padding: 0 16px 16px;
  overflow: hidden;
}

/* 相关信息列表样式 */
.related-posts-list {
  margin-bottom: 12px;
}

.related-post-item {
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.related-post-item:last-child {
  border-bottom: none;
}

.post-item-content {
  display: flex;
  align-items: center;
}

.post-item-left {
  margin-right: 12px;
}

.post-image {
  width: 80px;
  height: 60px;
  border-radius: 8px;
  background-color: #f5f7fa;
}

.post-item-middle {
  flex: 1;
  overflow: hidden;
}

.post-item-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.post-item-category {
  font-size: 13px;
  color: #0052CC;
  background-color: rgba(0, 82, 204, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
  display: inline-block;
  margin-bottom: 6px;
}

.post-item-meta {
  display: flex;
  font-size: 12px;
  color: #999;
}

.post-item-time {
  margin-right: 16px;
}

/* 查看更多按钮样式 */
.view-more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  background-color: #f7f9fc;
  border-radius: 8px;
  margin-top: 8px;
}

.view-more-text {
  font-size: 14px;
  color: #0052CC;
}

.view-more-icon {
  margin-left: 4px;
  font-size: 12px;
  color: #0052CC;
}

/* 空数据提示样式 */
.empty-related-posts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 0;
}

.empty-image {
  width: 80px;
  height: 80px;
  margin-bottom: 12px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 红包相关样式 */
.red-packet-details {
  background-color: #FFF5F5;
  border-radius: 16rpx;
  padding: 20rpx;
  margin: 20rpx 0;
}

.red-packet-amount,
.red-packet-usage,
.red-packet-method {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #FFE6E6;
}

.amount-label,
.usage-label,
.method-label {
  color: #666;
  font-size: 28rpx;
}

.amount-value,
.usage-value,
.method-value {
  color: #FF4D4F;
  font-size: 32rpx;
  font-weight: bold;
}

.red-packet-receive {
  flex: 1;
  background-color: #FF4D4F;
  color: white;
  text-align: center;
  padding: 20rpx;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 8rpx;
  margin-top: 10rpx;
}

.red-packet-card {
  border-left: 6rpx solid #FF4D4F;
}

/* 自定义导航栏样式 */
.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}

.navbar-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #fff;
  margin: 0 auto;
}
</style> 