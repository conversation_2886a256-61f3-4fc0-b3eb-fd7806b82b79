
/* 积分规则页面样式开始 */
.points-rule-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 100rpx;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #8E2DE2, #4A00E0);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(74, 0, 224, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
}

/* 规则内容样式 */
.rule-content {
  padding: 20rpx;
}
.section-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 表单样式 */
.form-item {
  margin-bottom: 20rpx;
}
.form-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}
.form-input-group {
  display: flex;
  align-items: center;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  overflow: hidden;
}
.form-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}
.input-suffix {
  padding: 0 20rpx;
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  height: 80rpx;
  line-height: 80rpx;
}
.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 积分等级样式 */
.level-list {
  margin-bottom: 20rpx;
}
.level-item {
  background: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
}
.level-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}
.level-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}
.level-points {
  font-size: 24rpx;
  color: #4A00E0;
  background: rgba(74, 0, 224, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}
.level-benefits {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-bottom: 15rpx;
}
.benefit-item {
  font-size: 24rpx;
  color: #666;
  background: #f0f0f0;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}
.level-actions {
  display: flex;
  gap: 15rpx;
  justify-content: flex-end;
}
.action-btn {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
}
.action-btn.edit {
  background: rgba(74, 0, 224, 0.1);
  color: #4A00E0;
}
.action-btn.delete {
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}

/* 添加按钮 */
.add-btn {
  background: #4A00E0;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  margin-top: 20rpx;
}

/* 底部保存栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.save-btn {
  background: #4A00E0;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  width: 100%;
}
/* 积分规则页面样式结束 */
