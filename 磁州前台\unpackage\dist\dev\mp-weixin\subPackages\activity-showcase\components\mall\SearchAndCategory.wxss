/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-779f5f92, html.data-v-779f5f92, #app.data-v-779f5f92, .index-container.data-v-779f5f92 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.search-category-container.data-v-779f5f92 {
  padding: 20rpx 30rpx 30rpx;
  background: #FFFFFF;
  border-radius: 0 0 30rpx 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
}

/* 搜索框 */
.search-box.data-v-779f5f92 {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.search-box .search-input.data-v-779f5f92 {
  flex: 1;
  height: 80rpx;
  background-color: #F2F2F7;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.search-box .search-input .search-icon.data-v-779f5f92 {
  margin-right: 10rpx;
  color: #8E8E93;
}
.search-box .search-input .placeholder.data-v-779f5f92 {
  font-size: 28rpx;
  color: #8E8E93;
}
.search-box .scan-btn.data-v-779f5f92 {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  background-color: #F2F2F7;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.search-box .scan-btn .scan-icon.data-v-779f5f92 {
  color: #FF3B69;
}

/* 分类导航 - 网格布局 */
.category-grid.data-v-779f5f92 {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16rpx 10rpx;
}
.category-grid .category-item.data-v-779f5f92 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.category-grid .category-item .category-icon-wrapper.data-v-779f5f92 {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 8rpx;
}
.category-grid .category-item .category-icon.data-v-779f5f92 {
  width: 90rpx;
  height: 90rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
}
.category-grid .category-item .category-icon .icon-image.data-v-779f5f92 {
  width: 50rpx;
  height: 50rpx;
}
.category-grid .category-item .category-indicator.data-v-779f5f92 {
  position: absolute;
  bottom: -8rpx;
  width: 16rpx;
  height: 4rpx;
  background-color: #FF3B69;
  border-radius: 2rpx;
  transition: all 0.3s ease;
}
.category-grid .category-item .category-name.data-v-779f5f92 {
  font-size: 24rpx;
  color: #333333;
  transition: all 0.3s ease;
  width: 100%;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.category-grid .category-item.active .category-icon.data-v-779f5f92 {
  transform: translateY(-4rpx);
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.1);
}
.category-grid .category-item.active .category-indicator.data-v-779f5f92 {
  width: 32rpx;
}
.category-grid .category-item.active .category-name.data-v-779f5f92 {
  color: #FF3B69;
  font-weight: 600;
}