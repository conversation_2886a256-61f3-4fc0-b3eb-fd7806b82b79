{"version": 3, "file": "BannerSlider.js", "sources": ["subPackages/activity-showcase/components/mall/BannerSlider.vue", "../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-ejgeW3ni9zdWJQYWNrYWdlcy9hY3Rpdml0eS1zaG93Y2FzZS9jb21wb25lbnRzL21hbGwvQmFubmVyU2xpZGVyLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"banner-slider\" :style=\"sliderStyle\">\r\n    <swiper \r\n      class=\"banner-swiper\" \r\n      :indicator-dots=\"showIndicator\" \r\n      :autoplay=\"autoplay\" \r\n      :interval=\"interval\" \r\n      :duration=\"duration\"\r\n      :circular=\"circular\"\r\n      @change=\"onSwiperChange\"\r\n    >\r\n      <swiper-item v-for=\"(banner, index) in banners\" :key=\"index\" @click=\"onBannerClick(banner)\">\r\n        <image class=\"banner-image\" :src=\"banner.image\" mode=\"aspectFill\"></image>\r\n      </swiper-item>\r\n    </swiper>\r\n    \r\n    <!-- 自定义指示器 -->\r\n    <view class=\"custom-indicator\" v-if=\"showCustomIndicator\">\r\n      <view \r\n        v-for=\"(banner, index) in banners\" \r\n        :key=\"index\"\r\n        class=\"indicator-dot\"\r\n        :class=\"{ active: currentIndex === index }\"\r\n      ></view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue';\r\n\r\n// 组件属性定义\r\nconst props = defineProps({\r\n  banners: {\r\n    type: Array,\r\n    default: () => []\r\n  },\r\n  showIndicator: {\r\n    type: Boolean,\r\n    default: false\r\n  },\r\n  showCustomIndicator: {\r\n    type: Boolean,\r\n    default: true\r\n  },\r\n  autoplay: {\r\n    type: Boolean,\r\n    default: true\r\n  },\r\n  interval: {\r\n    type: Number,\r\n    default: 5000\r\n  },\r\n  duration: {\r\n    type: Number,\r\n    default: 500\r\n  },\r\n  circular: {\r\n    type: Boolean,\r\n    default: true\r\n  },\r\n  sliderStyle: {\r\n    type: Object,\r\n    default: () => ({})\r\n  }\r\n});\r\n\r\n// 定义事件\r\ndefineEmits(['click']);\r\n\r\n// 当前轮播索引\r\nconst currentIndex = ref(0);\r\n\r\n// 轮播变化\r\nconst onSwiperChange = (e) => {\r\n  currentIndex.value = e.detail.current;\r\n};\r\n\r\n// 点击轮播图\r\nconst onBannerClick = (banner) => {\r\n  $emit('click', banner);\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.banner-slider {\r\n  width: 100%;\r\n  height: 300rpx;\r\n  border-radius: 24rpx;\r\n  overflow: hidden;\r\n  position: relative;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 6px 16px rgba(0,0,0,0.1);\r\n}\r\n\r\n.banner-swiper {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.banner-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 24rpx;\r\n}\r\n\r\n.custom-indicator {\r\n  position: absolute;\r\n  bottom: 20rpx;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  display: flex;\r\n  align-items: center;\r\n  \r\n  .indicator-dot {\r\n    width: 16rpx;\r\n    height: 16rpx;\r\n    border-radius: 8rpx;\r\n    background-color: rgba(255,255,255,0.6);\r\n    margin: 0 8rpx;\r\n    transition: all 0.3s ease;\r\n    \r\n    &.active {\r\n      width: 32rpx;\r\n      background-color: #FF3B69;\r\n    }\r\n  }\r\n}\r\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/磁州/subPackages/activity-showcase/components/mall/BannerSlider.vue'\nwx.createComponent(Component)"], "names": ["ref"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuEA,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAG1B,UAAM,iBAAiB,CAAC,MAAM;AAC5B,mBAAa,QAAQ,EAAE,OAAO;AAAA,IAChC;AAGA,UAAM,gBAAgB,CAAC,WAAW;AAChC,YAAM,SAAS,MAAM;AAAA,IACvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChFA,GAAG,gBAAgB,SAAS;"}