{"version": 3, "file": "jobInfo.js", "sources": ["mock/publish/jobInfo.js"], "sourcesContent": ["// 招聘信息详情模拟数据\r\nexport const jobDetail = {\r\n  id: 'job12345',\r\n  title: '急招会计1名，五险一金，双休',\r\n  salary: '4500-6000元/月',\r\n  tags: ['会计', '财务', '双休', '五险一金', '2年经验'],\r\n  publishTime: Date.now() - 86400000 * 1, // 1天前\r\n  company: {\r\n    name: '磁县鑫源贸易有限公司',\r\n    logo: '/static/images/company-logo.png',\r\n    size: '20-99人',\r\n    industry: '贸易/批发/零售',\r\n    address: '磁县经济开发区创业路18号',\r\n    isVerified: true\r\n  },\r\n  jobType: '全职',\r\n  experience: '2年以上',\r\n  education: '大专及以上',\r\n  headcount: '1人',\r\n  department: '财务部',\r\n  workingAddress: '磁县经济开发区创业路18号',\r\n  workingTime: '8:30-17:30，周末双休',\r\n  benefits: ['五险一金', '带薪年假', '节日福利', '员工旅游', '定期体检'],\r\n  responsibilities: [\r\n    '负责公司日常财务核算、报表编制',\r\n    '负责税务申报、税务筹划',\r\n    '负责与银行、税务等部门的对接工作',\r\n    '协助财务经理完成其他财务工作'\r\n  ],\r\n  requirements: [\r\n    '财务、会计等相关专业，大专及以上学历',\r\n    '2年以上财务工作经验，有中小企业工作经验优先',\r\n    '熟悉财务软件操作，熟悉Excel等办公软件',\r\n    '具有良好的沟通能力和团队合作精神',\r\n    '工作认真负责，有较强的学习能力'\r\n  ],\r\n  description: '我公司是一家成立于2010年的贸易企业，主要经营建材、五金、电器等产品。公司发展稳定，团队氛围和谐，福利待遇优厚。\\n\\n现因业务发展需要，急招会计1名，负责公司日常财务工作。我们提供有竞争力的薪资待遇和良好的晋升空间，欢迎有志之士加入我们的团队！',\r\n  publisher: {\r\n    id: 'hr12345',\r\n    name: '张经理（HR）',\r\n    avatar: '/static/images/avatar.png',\r\n    position: '人事经理',\r\n    isVerified: true\r\n  },\r\n  contact: {\r\n    name: '张经理',\r\n    phone: '13876543210',\r\n    email: '<EMAIL>'\r\n  }\r\n};\r\n\r\n// 相关职位推荐数据\r\nexport const relatedJobs = [\r\n  {\r\n    id: 'job001',\r\n    title: '出纳文员',\r\n    salary: '3500-4500元/月',\r\n    company: '磁县华信会计师事务所',\r\n    experience: '1-3年',\r\n    education: '大专',\r\n    tags: ['五险', '双休', '朝九晚五']\r\n  },\r\n  {\r\n    id: 'job002',\r\n    title: '财务主管',\r\n    salary: '6000-8000元/月',\r\n    company: '磁县恒通物流有限公司',\r\n    experience: '3-5年',\r\n    education: '本科',\r\n    tags: ['五险一金', '年终奖', '管理岗位']\r\n  },\r\n  {\r\n    id: 'job003',\r\n    title: '会计助理',\r\n    salary: '3000-4000元/月',\r\n    company: '磁县宏达商贸有限公司',\r\n    experience: '应届毕业生',\r\n    education: '大专',\r\n    tags: ['五险', '包吃住', '有培训']\r\n  }\r\n];\r\n\r\n// 获取招聘信息详情的API函数\r\nexport const fetchJobDetail = (id) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve(jobDetail);\r\n    }, 300);\r\n  });\r\n};\r\n\r\n// 获取相关职位的API函数\r\nexport const fetchRelatedJobs = () => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve(relatedJobs);\r\n    }, 300);\r\n  });\r\n}; "], "names": [], "mappings": ";AACO,MAAM,YAAY;AAAA,EACvB,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM,CAAC,MAAM,MAAM,MAAM,QAAQ,MAAM;AAAA,EACvC,aAAa,KAAK,IAAK,IAAG,QAAW;AAAA;AAAA,EACrC,SAAS;AAAA,IACP,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,YAAY;AAAA,EACb;AAAA,EACD,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,UAAU,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,EACjD,kBAAkB;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA,EACD,cAAc;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA,EACD,aAAa;AAAA,EACb,WAAW;AAAA,IACT,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EACb;AAAA,EACD,SAAS;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,EACR;AACH;AAGO,MAAM,cAAc;AAAA,EACzB;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,MAAM,CAAC,MAAM,MAAM,MAAM;AAAA,EAC1B;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,MAAM,CAAC,QAAQ,OAAO,MAAM;AAAA,EAC7B;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,MAAM,CAAC,MAAM,OAAO,KAAK;AAAA,EAC1B;AACH;AAGY,MAAC,iBAAiB,CAAC,OAAO;AACpC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,cAAQ,SAAS;AAAA,IAClB,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,mBAAmB,MAAM;AACpC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,cAAQ,WAAW;AAAA,IACpB,GAAE,GAAG;AAAA,EACV,CAAG;AACH;;;"}