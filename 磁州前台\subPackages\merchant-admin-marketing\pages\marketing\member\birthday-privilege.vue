<template>
  <view class="birthday-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">生日特权</text>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 生日特权内容 -->
    <view class="privilege-content">
      <!-- 生日特权开关 -->
      <view class="section-card">
        <view class="switch-item">
          <view class="switch-content">
            <text class="switch-title">生日特权</text>
            <text class="switch-desc">开启后，会员在生日当月可享受专属特权</text>
          </view>
          <switch :checked="birthdaySettings.enabled" @change="toggleBirthdayPrivilege" color="#4A00E0" />
        </view>
      </view>
      
      <block v-if="birthdaySettings.enabled">
        <!-- 生日特权设置 -->
        <view class="section-card">
          <view class="section-title">特权设置</view>
          
          <view class="form-item">
            <text class="form-label">特权有效期</text>
            <view class="radio-group">
              <view class="radio-item" :class="{ active: birthdaySettings.validPeriod === 'day' }" @click="setValidPeriod('day')">
                <text class="radio-text">生日当天</text>
              </view>
              <view class="radio-item" :class="{ active: birthdaySettings.validPeriod === 'week' }" @click="setValidPeriod('week')">
                <text class="radio-text">生日当周</text>
              </view>
              <view class="radio-item" :class="{ active: birthdaySettings.validPeriod === 'month' }" @click="setValidPeriod('month')">
                <text class="radio-text">生日当月</text>
              </view>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">提前通知</text>
            <view class="form-input-group">
              <input type="number" class="form-input" v-model="birthdaySettings.notifyDays" />
              <text class="input-suffix">天</text>
            </view>
          </view>
          
          <view class="form-item switch-item">
            <text class="form-label">短信通知</text>
            <switch :checked="birthdaySettings.smsNotify" @change="toggleSmsNotify" color="#4A00E0" />
          </view>
          
          <view class="form-item switch-item">
            <text class="form-label">微信通知</text>
            <switch :checked="birthdaySettings.wechatNotify" @change="toggleWechatNotify" color="#4A00E0" />
          </view>
        </view>
        
        <!-- 生日礼包设置 -->
        <view class="section-card">
          <view class="section-title">生日礼包</view>
          
          <view class="privilege-list">
            <view class="privilege-item" v-for="(privilege, index) in birthdayPrivileges" :key="index">
              <view class="privilege-checkbox" :class="{ checked: privilege.selected }" @click="togglePrivilege(privilege)">
                <view class="checkbox-inner" v-if="privilege.selected"></view>
              </view>
              <view class="privilege-content">
                <text class="privilege-name">{{privilege.name}}</text>
                <text class="privilege-desc">{{privilege.description}}</text>
              </view>
              <view class="privilege-config" @click="configPrivilege(privilege)">
                <text class="config-text">设置</text>
              </view>
            </view>
          </view>
          
          <button class="add-btn" @click="addPrivilege">添加生日特权</button>
        </view>
        
        <!-- 适用会员等级 -->
        <view class="section-card">
          <view class="section-title">适用会员等级</view>
          
          <view class="level-list">
            <view class="level-item" v-for="(level, index) in memberLevels" :key="index">
              <view class="level-checkbox" :class="{ checked: level.selected }" @click="toggleLevel(level)">
                <view class="checkbox-inner" v-if="level.selected"></view>
              </view>
              <view class="level-content">
                <text class="level-name">{{level.name}}</text>
                <text class="level-desc">{{level.memberCount}}名会员</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 生日祝福语 -->
        <view class="section-card">
          <view class="section-title">生日祝福语</view>
          
          <view class="form-item">
            <textarea class="form-textarea" v-model="birthdaySettings.greetingText" placeholder="请输入生日祝福语" />
          </view>
          
          <view class="greeting-preview">
            <view class="preview-title">预览效果</view>
            <view class="preview-card">
              <view class="preview-header">
                <image class="preview-logo" src="/static/images/logo.png" mode="aspectFit"></image>
                <text class="preview-shop-name">磁州生活网</text>
              </view>
              <view class="preview-content">
                <text class="preview-greeting">{{birthdaySettings.greetingText || '亲爱的会员，祝您生日快乐！我们为您准备了专属生日礼包，点击查看详情。'}}</text>
              </view>
              <view class="preview-footer">
                <text class="preview-btn">查看生日礼包</text>
              </view>
            </view>
          </view>
        </view>
      </block>
    </view>
    
    <!-- 保存按钮 -->
    <view class="bottom-bar" v-if="birthdaySettings.enabled">
      <button class="save-btn" @click="saveSettings">保存设置</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 生日特权设置
      birthdaySettings: {
        enabled: true,
        validPeriod: 'month',
        notifyDays: 3,
        smsNotify: true,
        wechatNotify: true,
        greetingText: '亲爱的会员，祝您生日快乐！我们为您准备了专属生日礼包，点击查看详情。'
      },
      
      // 生日特权列表
      birthdayPrivileges: [
        {
          id: 1,
          name: '生日优惠券',
          description: '赠送生日专属优惠券',
          selected: true
        },
        {
          id: 2,
          name: '积分翻倍',
          description: '生日期间消费积分翻倍',
          selected: true
        },
        {
          id: 3,
          name: '生日礼品',
          description: '到店领取生日礼品',
          selected: false
        },
        {
          id: 4,
          name: '专属折扣',
          description: '生日当天消费享受额外折扣',
          selected: true
        },
        {
          id: 5,
          name: '免费配送',
          description: '生日期间订单免费配送',
          selected: false
        }
      ],
      
      // 会员等级
      memberLevels: [
        {
          id: 1,
          name: '普通会员',
          memberCount: 2156,
          selected: false
        },
        {
          id: 2,
          name: '银卡会员',
          memberCount: 864,
          selected: true
        },
        {
          id: 3,
          name: '金卡会员',
          memberCount: 426,
          selected: true
        },
        {
          id: 4,
          name: '钻石会员',
          memberCount: 116,
          selected: true
        }
      ]
    };
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    
    toggleBirthdayPrivilege(e) {
      this.birthdaySettings.enabled = e.detail.value;
    },
    
    setValidPeriod(period) {
      this.birthdaySettings.validPeriod = period;
    },
    
    toggleSmsNotify(e) {
      this.birthdaySettings.smsNotify = e.detail.value;
    },
    
    toggleWechatNotify(e) {
      this.birthdaySettings.wechatNotify = e.detail.value;
    },
    
    togglePrivilege(privilege) {
      const index = this.birthdayPrivileges.findIndex(item => item.id === privilege.id);
      if (index !== -1) {
        this.birthdayPrivileges[index].selected = !this.birthdayPrivileges[index].selected;
      }
    },
    
    configPrivilege(privilege) {
      uni.showToast({
        title: `${privilege.name}设置功能开发中`,
        icon: 'none'
      });
    },
    
    addPrivilege() {
      uni.showToast({
        title: '添加特权功能开发中',
        icon: 'none'
      });
    },
    
    toggleLevel(level) {
      const index = this.memberLevels.findIndex(item => item.id === level.id);
      if (index !== -1) {
        this.memberLevels[index].selected = !this.memberLevels[index].selected;
      }
    },
    
    saveSettings() {
      uni.showLoading({
        title: '保存中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '生日特权设置保存成功',
          icon: 'success'
        });
      }, 1000);
    }
  }
}
</script>

<style>
/* 生日特权页面样式开始 */
.birthday-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 100rpx;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #8E2DE2, #4A00E0);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(74, 0, 224, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
}

/* 特权内容样式 */
.privilege-content {
  padding: 20rpx;
}

.section-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 开关样式 */
.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.switch-content {
  flex: 1;
}

.switch-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.switch-desc {
  font-size: 24rpx;
  color: #999;
}

/* 表单样式 */
.form-item {
  margin-bottom: 20rpx;
}

.form-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.form-input-group {
  display: flex;
  align-items: center;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  overflow: hidden;
}

.form-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.input-suffix {
  padding: 0 20rpx;
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  height: 80rpx;
  line-height: 80rpx;
}

.form-textarea {
  width: 100%;
  height: 160rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

/* 单选按钮组 */
.radio-group {
  display: flex;
  gap: 20rpx;
}

.radio-item {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9f9f9;
}

.radio-item.active {
  background: rgba(74, 0, 224, 0.1);
  border-color: #4A00E0;
}

.radio-text {
  font-size: 26rpx;
  color: #666;
}

.radio-item.active .radio-text {
  color: #4A00E0;
  font-weight: 600;
}

/* 特权列表样式 */
.privilege-list {
  margin-bottom: 20rpx;
}

.privilege-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.privilege-item:last-child {
  border-bottom: none;
}

.privilege-checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 1rpx solid #ddd;
  border-radius: 50%;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.privilege-checkbox.checked {
  border-color: #4A00E0;
  background: #4A00E0;
}

.checkbox-inner {
  width: 18rpx;
  height: 18rpx;
  border-radius: 50%;
  background: #fff;
}

.privilege-content {
  flex: 1;
}

.privilege-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.privilege-desc {
  font-size: 24rpx;
  color: #999;
}

.privilege-config {
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  background: rgba(74, 0, 224, 0.1);
}

.config-text {
  font-size: 24rpx;
  color: #4A00E0;
}

/* 会员等级列表 */
.level-list {
  margin-bottom: 20rpx;
}

.level-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.level-item:last-child {
  border-bottom: none;
}

.level-checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 1rpx solid #ddd;
  border-radius: 50%;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.level-checkbox.checked {
  border-color: #4A00E0;
  background: #4A00E0;
}

.level-content {
  flex: 1;
}

.level-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.level-desc {
  font-size: 24rpx;
  color: #999;
}

/* 祝福语预览 */
.greeting-preview {
  margin-top: 30rpx;
}

.preview-title {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.preview-card {
  border: 1rpx solid #f0f0f0;
  border-radius: 12rpx;
  overflow: hidden;
}

.preview-header {
  background: #4A00E0;
  padding: 20rpx;
  display: flex;
  align-items: center;
}

.preview-logo {
  width: 48rpx;
  height: 48rpx;
  border-radius: 8rpx;
  margin-right: 15rpx;
}

.preview-shop-name {
  font-size: 28rpx;
  color: #fff;
  font-weight: 600;
}

.preview-content {
  padding: 30rpx;
  background: #fff;
}

.preview-greeting {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

.preview-footer {
  padding: 20rpx;
  display: flex;
  justify-content: center;
  border-top: 1rpx solid #f0f0f0;
}

.preview-btn {
  font-size: 28rpx;
  color: #4A00E0;
  font-weight: 600;
}

/* 添加按钮 */
.add-btn {
  background: #4A00E0;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  margin-top: 20rpx;
}

/* 底部保存栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.save-btn {
  background: #4A00E0;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  width: 100%;
}
/* 生日特权页面样式结束 */
</style> 