{"version": 3, "file": "create-coupon.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/create-coupon.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xjcmVhdGUtY291cG9uLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"coupon-create-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\">\r\n      <view class=\"navbar-left\" @tap=\"goBack\">\r\n        <view class=\"back-arrow\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">创建优惠券</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-btn\">\r\n          <text class=\"help-icon\">?</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 进度指示器 -->\r\n    <view class=\"progress-container\">\r\n      <view class=\"progress-bar\">\r\n        <view class=\"progress-track\"></view>\r\n        <view class=\"progress-fill\" :style=\"{width: progressPercentage + '%'}\"></view>\r\n      </view>\r\n      <view class=\"progress-steps\">\r\n        <view class=\"step\" :class=\"{active: currentStep >= 1, completed: currentStep > 1}\">\r\n          <view class=\"step-dot\">\r\n            <text v-if=\"currentStep <= 1\">1</text>\r\n            <view class=\"check-icon\" v-else></view>\r\n          </view>\r\n          <text class=\"step-label\">基本信息</text>\r\n        </view>\r\n        <view class=\"step-line\" :class=\"{active: currentStep > 1}\"></view>\r\n        <view class=\"step\" :class=\"{active: currentStep >= 2, completed: currentStep > 2}\">\r\n          <view class=\"step-dot\">\r\n            <text v-if=\"currentStep <= 2\">2</text>\r\n            <view class=\"check-icon\" v-else></view>\r\n          </view>\r\n          <text class=\"step-label\">使用规则</text>\r\n        </view>\r\n        <view class=\"step-line\" :class=\"{active: currentStep > 2}\"></view>\r\n        <view class=\"step\" :class=\"{active: currentStep >= 3, completed: currentStep > 3}\">\r\n          <view class=\"step-dot\">\r\n            <text v-if=\"currentStep <= 3\">3</text>\r\n            <view class=\"check-icon\" v-else></view>\r\n          </view>\r\n          <text class=\"step-label\">发放设置</text>\r\n        </view>\r\n        <view class=\"step-line\" :class=\"{active: currentStep > 3}\"></view>\r\n        <view class=\"step\" :class=\"{active: currentStep >= 4}\">\r\n          <view class=\"step-dot\">\r\n            <text>4</text>\r\n          </view>\r\n          <text class=\"step-label\">确认创建</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 表单容器 -->\r\n    <scroll-view scroll-y class=\"form-scroll-view\" enable-back-to-top :scroll-into-view=\"scrollToId\">\r\n      <!-- 步骤1: 基本信息 -->\r\n      <view class=\"form-section\" v-if=\"currentStep === 1\" id=\"step1\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">基本信息</text>\r\n          <text class=\"section-subtitle\">设置优惠券的基本信息和样式</text>\r\n        </view>\r\n\r\n        <view class=\"coupon-preview-wrapper\">\r\n          <text class=\"preview-title\">优惠券预览</text>\r\n          <view class=\"coupon-preview\" :style=\"{ background: formData.couponColor }\">\r\n            <view class=\"coupon-preview-content\">\r\n              <text class=\"coupon-name\">{{formData.name || '优惠券名称'}}</text>\r\n              <view class=\"coupon-value-container\">\r\n                <text class=\"coupon-value-symbol\" v-if=\"formData.type === 'amount'\">¥</text>\r\n                <text class=\"coupon-value\">{{formData.type === 'amount' ? formData.value : formData.value + '折'}}</text>\r\n              </view>\r\n              <text class=\"coupon-desc\">{{formData.description || '优惠券描述文字'}}</text>\r\n              <view class=\"coupon-dates\">\r\n                <text class=\"date-label\">有效期：</text>\r\n                <text class=\"date-value\">{{formData.validPeriod ? formData.validPeriod + '天' : '2023.10.01-2023.10.31'}}</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"coupon-dash-border\"></view>\r\n            <view class=\"coupon-extra\">\r\n              <text class=\"coupon-limit\">{{formData.minAmount ? '满' + formData.minAmount + '元可用' : '无门槛'}}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"form-block\">\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label required\">优惠券名称</text>\r\n            <input class=\"form-input\" \r\n              v-model=\"formData.name\" \r\n              placeholder=\"请输入优惠券名称\" \r\n              maxlength=\"20\"\r\n              placeholder-style=\"color: #bbb\" />\r\n            <text class=\"input-limit\">{{formData.name.length}}/20</text>\r\n          </view>\r\n          \r\n          <view class=\"form-item\">\r\n            <text class=\"form-label required\">优惠券类型</text>\r\n            <view class=\"radio-group\">\r\n              <view class=\"radio-item\" \r\n                :class=\"{ active: formData.type === 'amount' }\" \r\n                @tap=\"formData.type = 'amount'\">\r\n                <view class=\"radio-dot\">\r\n                  <view class=\"radio-dot-inner\" v-if=\"formData.type === 'amount'\"></view>\r\n                </view>\r\n                <text class=\"radio-label\">满减券</text>\r\n              </view>\r\n              <view class=\"radio-item\" \r\n                :class=\"{ active: formData.type === 'discount' }\" \r\n                @tap=\"formData.type = 'discount'\">\r\n                <view class=\"radio-dot\">\r\n                  <view class=\"radio-dot-inner\" v-if=\"formData.type === 'discount'\"></view>\r\n                </view>\r\n                <text class=\"radio-label\">折扣券</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"form-item\">\r\n            <text class=\"form-label required\">{{formData.type === 'amount' ? '优惠金额' : '折扣力度'}}</text>\r\n            <view class=\"value-input-wrap\">\r\n              <input class=\"form-input value-input\" \r\n                v-model=\"formData.value\" \r\n                type=\"digit\"\r\n                :placeholder=\"formData.type === 'amount' ? '请输入优惠金额' : '请输入折扣(1-9.9)'\" \r\n                placeholder-style=\"color: #bbb\" />\r\n              <text class=\"value-unit\">{{formData.type === 'amount' ? '元' : '折'}}</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">优惠券颜色</text>\r\n            <view class=\"color-picker\">\r\n              <view class=\"color-option\" \r\n                v-for=\"(color, index) in colorOptions\" \r\n                :key=\"index\"\r\n                :style=\"{ background: color }\"\r\n                :class=\"{ active: formData.couponColor === color }\"\r\n                @tap=\"formData.couponColor = color\">\r\n                <view class=\"color-check\" v-if=\"formData.couponColor === color\">\r\n                  <view class=\"check-mark\"></view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">优惠券描述</text>\r\n            <textarea class=\"form-textarea\" \r\n              v-model=\"formData.description\" \r\n              placeholder=\"请输入优惠券的使用说明或描述\" \r\n              maxlength=\"50\"\r\n              placeholder-style=\"color: #bbb\" />\r\n            <text class=\"input-limit\">{{formData.description.length}}/50</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 步骤2: 使用规则 -->\r\n      <view class=\"form-section\" v-if=\"currentStep === 2\" id=\"step2\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">使用规则</text>\r\n          <text class=\"section-subtitle\">设置优惠券的使用条件和有效期</text>\r\n        </view>\r\n        \r\n        <view class=\"form-block\">\r\n          <!-- 使用门槛 -->\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">使用门槛</text>\r\n            <view class=\"radio-group\">\r\n              <view class=\"radio-item\" \r\n                :class=\"{ active: formData.useThreshold === 'no' }\" \r\n                @tap=\"formData.useThreshold = 'no'; formData.minAmount = ''\">\r\n                <view class=\"radio-dot\">\r\n                  <view class=\"radio-dot-inner\" v-if=\"formData.useThreshold === 'no'\"></view>\r\n                </view>\r\n                <text class=\"radio-label\">无门槛</text>\r\n              </view>\r\n              <view class=\"radio-item\" \r\n                :class=\"{ active: formData.useThreshold === 'yes' }\" \r\n                @tap=\"formData.useThreshold = 'yes'\">\r\n                <view class=\"radio-dot\">\r\n                  <view class=\"radio-dot-inner\" v-if=\"formData.useThreshold === 'yes'\"></view>\r\n                </view>\r\n                <text class=\"radio-label\">满额可用</text>\r\n              </view>\r\n            </view>\r\n            \r\n            <view class=\"amount-input-container\" v-if=\"formData.useThreshold === 'yes'\">\r\n              <text class=\"input-prefix\">满</text>\r\n              <input class=\"form-input threshold-input\" \r\n                v-model=\"formData.minAmount\" \r\n                type=\"digit\"\r\n                placeholder=\"请输入消费满额金额\" \r\n                placeholder-style=\"color: #bbb\" />\r\n              <text class=\"input-suffix\">元可用</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 适用商品 -->\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">适用商品</text>\r\n            <view class=\"radio-group goods-limit-group\">\r\n              <view class=\"radio-item\" \r\n                :class=\"{ active: formData.goodsLimit === 'all' }\" \r\n                @tap=\"formData.goodsLimit = 'all'\">\r\n                <view class=\"radio-dot\">\r\n                  <view class=\"radio-dot-inner\" v-if=\"formData.goodsLimit === 'all'\"></view>\r\n                </view>\r\n                <text class=\"radio-label\">全部商品</text>\r\n              </view>\r\n              <view class=\"radio-item\" \r\n                :class=\"{ active: formData.goodsLimit === 'category' }\" \r\n                @tap=\"formData.goodsLimit = 'category'\">\r\n                <view class=\"radio-dot\">\r\n                  <view class=\"radio-dot-inner\" v-if=\"formData.goodsLimit === 'category'\"></view>\r\n                </view>\r\n                <text class=\"radio-label\">指定品类</text>\r\n              </view>\r\n              <view class=\"radio-item\" \r\n                :class=\"{ active: formData.goodsLimit === 'specific' }\" \r\n                @tap=\"formData.goodsLimit = 'specific'\">\r\n                <view class=\"radio-dot\">\r\n                  <view class=\"radio-dot-inner\" v-if=\"formData.goodsLimit === 'specific'\"></view>\r\n                </view>\r\n                <text class=\"radio-label\">指定商品</text>\r\n              </view>\r\n            </view>\r\n            \r\n            <!-- 指定品类 -->\r\n            <view class=\"category-selection\" v-if=\"formData.goodsLimit === 'category'\">\r\n              <view class=\"selected-categories\">\r\n                <view class=\"selected-tag\" v-for=\"(cat, index) in selectedCategories\" :key=\"index\">\r\n                  <text class=\"tag-text\">{{cat.name}}</text>\r\n                  <text class=\"tag-close\" @tap=\"removeCategory(index)\">×</text>\r\n                </view>\r\n                <view class=\"add-tag\" @tap=\"showCategoryPicker\">\r\n                  <view class=\"add-icon\"></view>\r\n                  <text class=\"add-text\">添加品类</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n            \r\n            <!-- 指定商品 -->\r\n            <view class=\"goods-selection\" v-if=\"formData.goodsLimit === 'specific'\">\r\n              <view class=\"goods-search\">\r\n                <view class=\"search-icon\"></view>\r\n                <input class=\"search-input\" placeholder=\"搜索商品名称\" />\r\n              </view>\r\n              \r\n              <view class=\"selected-goods\">\r\n                <view class=\"empty-tip\" v-if=\"selectedGoods.length === 0\">\r\n                  <text class=\"empty-text\">暂无已选商品，点击下方按钮添加</text>\r\n                </view>\r\n                <view class=\"goods-item\" v-for=\"(goods, index) in selectedGoods\" :key=\"index\">\r\n                  <image class=\"goods-image\" :src=\"goods.image\" mode=\"aspectFill\"></image>\r\n                  <view class=\"goods-info\">\r\n                    <text class=\"goods-name\">{{goods.name}}</text>\r\n                    <text class=\"goods-price\">¥{{goods.price}}</text>\r\n                  </view>\r\n                  <view class=\"remove-goods\" @tap=\"removeGoods(index)\">\r\n                    <text class=\"remove-icon\">×</text>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n              \r\n              <view class=\"add-goods-btn\" @tap=\"selectGoods\">\r\n                <text class=\"btn-text\">选择商品</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 有效期设置 -->\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label required\">有效期设置</text>\r\n            <view class=\"radio-group\">\r\n              <view class=\"radio-item\" \r\n                :class=\"{ active: formData.validityType === 'days' }\" \r\n                @tap=\"formData.validityType = 'days'\">\r\n                <view class=\"radio-dot\">\r\n                  <view class=\"radio-dot-inner\" v-if=\"formData.validityType === 'days'\"></view>\r\n                </view>\r\n                <text class=\"radio-label\">领取后N天内有效</text>\r\n              </view>\r\n              <view class=\"radio-item\" \r\n                :class=\"{ active: formData.validityType === 'fixed' }\" \r\n                @tap=\"formData.validityType = 'fixed'\">\r\n                <view class=\"radio-dot\">\r\n                  <view class=\"radio-dot-inner\" v-if=\"formData.validityType === 'fixed'\"></view>\r\n                </view>\r\n                <text class=\"radio-label\">固定有效期</text>\r\n              </view>\r\n            </view>\r\n            \r\n            <!-- 领取后N天内有效 -->\r\n            <view class=\"validity-days\" v-if=\"formData.validityType === 'days'\">\r\n              <view class=\"value-input-wrap\">\r\n                <input class=\"form-input validity-input\" \r\n                  v-model=\"formData.validPeriod\" \r\n                  type=\"number\"\r\n                  placeholder=\"请输入天数\" \r\n                  placeholder-style=\"color: #bbb\" />\r\n                <text class=\"value-unit\">天</text>\r\n              </view>\r\n            </view>\r\n            \r\n            <!-- 固定有效期 -->\r\n            <view class=\"fixed-date-range\" v-if=\"formData.validityType === 'fixed'\">\r\n              <view class=\"date-picker-wrap\">\r\n                <view class=\"date-input\" @tap=\"showDatePicker('start')\">\r\n                  <text class=\"date-text\" :class=\"{placeholder: !formData.startDate}\">\r\n                    {{formData.startDate || '开始日期'}}\r\n                  </text>\r\n                  <view class=\"calendar-icon\"></view>\r\n                </view>\r\n                <text class=\"date-separator\">至</text>\r\n                <view class=\"date-input\" @tap=\"showDatePicker('end')\">\r\n                  <text class=\"date-text\" :class=\"{placeholder: !formData.endDate}\">\r\n                    {{formData.endDate || '结束日期'}}\r\n                  </text>\r\n                  <view class=\"calendar-icon\"></view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 使用时间段限制 -->\r\n          <view class=\"form-item\">\r\n            <view class=\"switch-container\">\r\n              <text class=\"switch-label\">使用时间段限制</text>\r\n              <switch checked=\"{{formData.useTimeLimit}}\" color=\"#FF7600\" @change=\"toggleTimeLimit\" />\r\n            </view>\r\n            \r\n            <view class=\"time-limit-container\" v-if=\"formData.useTimeLimit\">\r\n              <view class=\"time-range\">\r\n                <view class=\"time-input\" @tap=\"showTimePicker('start')\">\r\n                  <text class=\"time-text\" :class=\"{placeholder: !formData.useTimeStart}\">\r\n                    {{formData.useTimeStart || '开始时间'}}\r\n                  </text>\r\n                  <view class=\"time-icon\"></view>\r\n                </view>\r\n                <text class=\"time-separator\">至</text>\r\n                <view class=\"time-input\" @tap=\"showTimePicker('end')\">\r\n                  <text class=\"time-text\" :class=\"{placeholder: !formData.useTimeEnd}\">\r\n                    {{formData.useTimeEnd || '结束时间'}}\r\n                  </text>\r\n                  <view class=\"time-icon\"></view>\r\n                </view>\r\n              </view>\r\n              <text class=\"time-hint\">例如：设置为 10:00-22:00，则优惠券仅在该时间段内可用</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 使用说明 -->\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">使用说明</text>\r\n            <textarea class=\"form-textarea\" \r\n              v-model=\"formData.useInstructions\" \r\n              placeholder=\"请输入优惠券的使用说明或注意事项\" \r\n              maxlength=\"100\"\r\n              placeholder-style=\"color: #bbb\" />\r\n            <text class=\"input-limit\">{{(formData.useInstructions || '').length}}/100</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 步骤3: 发放设置 -->\r\n      <view class=\"form-section\" v-if=\"currentStep === 3\" id=\"step3\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">发放设置</text>\r\n          <text class=\"section-subtitle\">设置优惠券的发放方式和数量</text>\r\n        </view>\r\n        \r\n        <view class=\"form-block\">\r\n          <!-- 发行总量 -->\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label required\">发行总量</text>\r\n            <view class=\"radio-group\">\r\n              <view class=\"radio-item\" \r\n                :class=\"{ active: formData.quantityType === 'unlimited' }\" \r\n                @tap=\"formData.quantityType = 'unlimited'; formData.totalQuantity = ''\">\r\n                <view class=\"radio-dot\">\r\n                  <view class=\"radio-dot-inner\" v-if=\"formData.quantityType === 'unlimited'\"></view>\r\n                </view>\r\n                <text class=\"radio-label\">不限制</text>\r\n              </view>\r\n              <view class=\"radio-item\" \r\n                :class=\"{ active: formData.quantityType === 'limited' }\" \r\n                @tap=\"formData.quantityType = 'limited'\">\r\n                <view class=\"radio-dot\">\r\n                  <view class=\"radio-dot-inner\" v-if=\"formData.quantityType === 'limited'\"></view>\r\n                </view>\r\n                <text class=\"radio-label\">限制数量</text>\r\n              </view>\r\n            </view>\r\n            \r\n            <view class=\"value-input-wrap\" v-if=\"formData.quantityType === 'limited'\">\r\n              <input class=\"form-input\" \r\n                v-model=\"formData.totalQuantity\" \r\n                type=\"number\"\r\n                placeholder=\"请输入发行总量\" \r\n                placeholder-style=\"color: #bbb\" />\r\n              <text class=\"value-unit\">张</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 每人限领 -->\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label required\">每人限领</text>\r\n            <view class=\"radio-group\">\r\n              <view class=\"radio-item\" \r\n                :class=\"{ active: formData.userLimitType === 'unlimited' }\" \r\n                @tap=\"formData.userLimitType = 'unlimited'; formData.perUserLimit = ''\">\r\n                <view class=\"radio-dot\">\r\n                  <view class=\"radio-dot-inner\" v-if=\"formData.userLimitType === 'unlimited'\"></view>\r\n                </view>\r\n                <text class=\"radio-label\">不限制</text>\r\n              </view>\r\n              <view class=\"radio-item\" \r\n                :class=\"{ active: formData.userLimitType === 'limited' }\" \r\n                @tap=\"formData.userLimitType = 'limited'\">\r\n                <view class=\"radio-dot\">\r\n                  <view class=\"radio-dot-inner\" v-if=\"formData.userLimitType === 'limited'\"></view>\r\n                </view>\r\n                <text class=\"radio-label\">限制数量</text>\r\n              </view>\r\n            </view>\r\n            \r\n            <view class=\"value-input-wrap\" v-if=\"formData.userLimitType === 'limited'\">\r\n              <input class=\"form-input\" \r\n                v-model=\"formData.perUserLimit\" \r\n                type=\"number\"\r\n                placeholder=\"请输入每人可领数量\" \r\n                placeholder-style=\"color: #bbb\" />\r\n              <text class=\"value-unit\">张</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 发放方式 -->\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label required\">发放方式</text>\r\n            <view class=\"issue-methods\">\r\n              <view class=\"issue-method\" \r\n                :class=\"{ active: formData.issueType === 'manual' }\"\r\n                @tap=\"formData.issueType = 'manual'\">\r\n                <view class=\"method-icon manual\" :class=\"{ active: formData.issueType === 'manual' }\"></view>\r\n                <view class=\"method-info\">\r\n                  <text class=\"method-name\">用户手动领取</text>\r\n                  <text class=\"method-desc\">由用户在领券中心主动领取</text>\r\n                </view>\r\n                <view class=\"method-check\" v-if=\"formData.issueType === 'manual'\">\r\n                  <view class=\"check-mark\"></view>\r\n                </view>\r\n              </view>\r\n              \r\n              <view class=\"issue-method\" \r\n                :class=\"{ active: formData.issueType === 'auto' }\"\r\n                @tap=\"formData.issueType = 'auto'\">\r\n                <view class=\"method-icon auto\" :class=\"{ active: formData.issueType === 'auto' }\"></view>\r\n                <view class=\"method-info\">\r\n                  <text class=\"method-name\">系统自动发放</text>\r\n                  <text class=\"method-desc\">满足条件时系统自动发放给用户</text>\r\n                </view>\r\n                <view class=\"method-check\" v-if=\"formData.issueType === 'auto'\">\r\n                  <view class=\"check-mark\"></view>\r\n                </view>\r\n              </view>\r\n              \r\n              <view class=\"issue-method\" \r\n                :class=\"{ active: formData.issueType === 'admin' }\"\r\n                @tap=\"formData.issueType = 'admin'\">\r\n                <view class=\"method-icon admin\" :class=\"{ active: formData.issueType === 'admin' }\"></view>\r\n                <view class=\"method-info\">\r\n                  <text class=\"method-name\">商家手动发放</text>\r\n                  <text class=\"method-desc\">商家通过后台手动发放给指定用户</text>\r\n                </view>\r\n                <view class=\"method-check\" v-if=\"formData.issueType === 'admin'\">\r\n                  <view class=\"check-mark\"></view>\r\n                </view>\r\n              </view>\r\n              \r\n              <view class=\"issue-method\" \r\n                :class=\"{ active: formData.issueType === 'share' }\"\r\n                @tap=\"formData.issueType = 'share'\">\r\n                <view class=\"method-icon share\" :class=\"{ active: formData.issueType === 'share' }\"></view>\r\n                <view class=\"method-info\">\r\n                  <text class=\"method-name\">分享领取</text>\r\n                  <text class=\"method-desc\">用户分享给好友一起领取</text>\r\n                </view>\r\n                <view class=\"method-check\" v-if=\"formData.issueType === 'share'\">\r\n                  <view class=\"check-mark\"></view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 自动发放条件 -->\r\n          <view class=\"form-item\" v-if=\"formData.issueType === 'auto'\">\r\n            <text class=\"form-label\">自动发放条件</text>\r\n            <view class=\"radio-group\">\r\n              <view class=\"radio-item\" \r\n                :class=\"{ active: formData.autoIssueCondition === 'new' }\" \r\n                @tap=\"formData.autoIssueCondition = 'new'\">\r\n                <view class=\"radio-dot\">\r\n                  <view class=\"radio-dot-inner\" v-if=\"formData.autoIssueCondition === 'new'\"></view>\r\n                </view>\r\n                <text class=\"radio-label\">新用户注册</text>\r\n              </view>\r\n              <view class=\"radio-item\" \r\n                :class=\"{ active: formData.autoIssueCondition === 'birthday' }\" \r\n                @tap=\"formData.autoIssueCondition = 'birthday'\">\r\n                <view class=\"radio-dot\">\r\n                  <view class=\"radio-dot-inner\" v-if=\"formData.autoIssueCondition === 'birthday'\"></view>\r\n                </view>\r\n                <text class=\"radio-label\">会员生日</text>\r\n              </view>\r\n              <view class=\"radio-item\" \r\n                :class=\"{ active: formData.autoIssueCondition === 'amount' }\" \r\n                @tap=\"formData.autoIssueCondition = 'amount'\">\r\n                <view class=\"radio-dot\">\r\n                  <view class=\"radio-dot-inner\" v-if=\"formData.autoIssueCondition === 'amount'\"></view>\r\n                </view>\r\n                <text class=\"radio-label\">订单满额</text>\r\n              </view>\r\n            </view>\r\n            \r\n            <!-- 订单满额发放 -->\r\n            <view class=\"order-amount\" v-if=\"formData.autoIssueCondition === 'amount'\">\r\n              <view class=\"amount-input-container\">\r\n                <text class=\"input-prefix\">满</text>\r\n                <input class=\"form-input threshold-input\" \r\n                  v-model=\"formData.autoIssueAmount\" \r\n                  type=\"digit\"\r\n                  placeholder=\"请输入订单满额金额\" \r\n                  placeholder-style=\"color: #bbb\" />\r\n                <text class=\"input-suffix\">元发放</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 领券中心 -->\r\n          <view class=\"form-item\" v-if=\"['manual', 'share'].includes(formData.issueType)\">\r\n            <view class=\"switch-container\">\r\n              <text class=\"switch-label\">在领券中心展示</text>\r\n              <switch checked=\"{{formData.showInCenter}}\" color=\"#FF7600\" @change=\"toggleShowInCenter\" />\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 发放时间 -->\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">发放时间</text>\r\n            <view class=\"radio-group\">\r\n              <view class=\"radio-item\" \r\n                :class=\"{ active: formData.issueTimeType === 'now' }\" \r\n                @tap=\"formData.issueTimeType = 'now'\">\r\n                <view class=\"radio-dot\">\r\n                  <view class=\"radio-dot-inner\" v-if=\"formData.issueTimeType === 'now'\"></view>\r\n                </view>\r\n                <text class=\"radio-label\">立即发放</text>\r\n              </view>\r\n              <view class=\"radio-item\" \r\n                :class=\"{ active: formData.issueTimeType === 'scheduled' }\" \r\n                @tap=\"formData.issueTimeType = 'scheduled'\">\r\n                <view class=\"radio-dot\">\r\n                  <view class=\"radio-dot-inner\" v-if=\"formData.issueTimeType === 'scheduled'\"></view>\r\n                </view>\r\n                <text class=\"radio-label\">定时发放</text>\r\n              </view>\r\n            </view>\r\n            \r\n            <!-- 定时发放 -->\r\n            <view class=\"scheduled-time\" v-if=\"formData.issueTimeType === 'scheduled'\">\r\n              <view class=\"date-picker-wrap\">\r\n                <view class=\"date-input\" @tap=\"showDateTimePicker('issue')\">\r\n                  <text class=\"date-text\" :class=\"{placeholder: !formData.issueTime}\">\r\n                    {{formData.issueTime || '请选择发放时间'}}\r\n                  </text>\r\n                  <view class=\"calendar-icon\"></view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 步骤4: 确认创建 -->\r\n      <view class=\"form-section\" v-if=\"currentStep === 4\" id=\"step4\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">确认创建</text>\r\n          <text class=\"section-subtitle\">确认优惠券信息并创建</text>\r\n        </view>\r\n        \r\n        <view class=\"form-block\">\r\n          <view class=\"preview-card\">\r\n            <text class=\"preview-title\">优惠券预览</text>\r\n            <view class=\"coupon-preview\" :style=\"{ background: formData.couponColor }\">\r\n              <view class=\"coupon-preview-content\">\r\n                <text class=\"coupon-name\">{{formData.name}}</text>\r\n                <view class=\"coupon-value-container\">\r\n                  <text class=\"coupon-value-symbol\" v-if=\"formData.type === 'amount'\">¥</text>\r\n                  <text class=\"coupon-value\">{{formData.type === 'amount' ? formData.value : formData.value + '折'}}</text>\r\n                </view>\r\n                <text class=\"coupon-desc\">{{formData.description || '暂无描述'}}</text>\r\n                <view class=\"coupon-dates\">\r\n                  <text class=\"date-label\">有效期：</text>\r\n                  <text class=\"date-value\">{{formData.validPeriod ? formData.validPeriod + '天' : '2023.10.01-2023.10.31'}}</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"coupon-dash-border\"></view>\r\n              <view class=\"coupon-extra\">\r\n                <text class=\"coupon-limit\">{{formData.minAmount ? '满' + formData.minAmount + '元可用' : '无门槛'}}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"summary-block\">\r\n            <text class=\"summary-title\">优惠券信息</text>\r\n            <view class=\"summary-item\">\r\n              <text class=\"summary-label\">优惠券名称</text>\r\n              <text class=\"summary-value\">{{formData.name}}</text>\r\n            </view>\r\n            <view class=\"summary-item\">\r\n              <text class=\"summary-label\">优惠券类型</text>\r\n              <text class=\"summary-value\">{{formData.type === 'amount' ? '满减券' : '折扣券'}}</text>\r\n            </view>\r\n            <view class=\"summary-item\">\r\n              <text class=\"summary-label\">{{formData.type === 'amount' ? '优惠金额' : '折扣力度'}}</text>\r\n              <text class=\"summary-value\">{{formData.type === 'amount' ? formData.value + '元' : formData.value + '折'}}</text>\r\n            </view>\r\n            <view class=\"summary-item\">\r\n              <text class=\"summary-label\">使用门槛</text>\r\n              <text class=\"summary-value\">{{formData.useThreshold === 'yes' ? '满' + formData.minAmount + '元可用' : '无门槛'}}</text>\r\n            </view>\r\n            <view class=\"summary-item\">\r\n              <text class=\"summary-label\">有效期</text>\r\n              <text class=\"summary-value\">{{getValidityText()}}</text>\r\n            </view>\r\n            <view class=\"summary-item\">\r\n              <text class=\"summary-label\">发放总量</text>\r\n              <text class=\"summary-value\">{{formData.totalQuantity || '不限'}}</text>\r\n            </view>\r\n            <view class=\"summary-item\">\r\n              <text class=\"summary-label\">每人限领</text>\r\n              <text class=\"summary-value\">{{formData.perUserLimit || '不限'}}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 添加发布组件 -->\r\n        <view class=\"form-block\">\r\n          <text class=\"block-title\">活动推广</text>\r\n          <MarketingPromotionActions \r\n            :activity-type=\"'coupon'\"\r\n            :activity-id=\"tempCouponId\"\r\n            :publish-mode-only=\"true\"\r\n            :show-actions=\"['publish']\"\r\n            @action-completed=\"handlePromotionCompleted\"\r\n          />\r\n        </view>\r\n        \r\n        <view class=\"form-actions\">\r\n          <button class=\"btn-prev\" @tap=\"prevStep\">上一步</button>\r\n          <button class=\"btn-create\" @tap=\"saveCoupon\">创建优惠券</button>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 底部操作按钮 -->\r\n      <view class=\"bottom-buttons\">\r\n        <view class=\"btn secondary\" v-if=\"currentStep > 1\" @tap=\"prevStep\">上一步</view>\r\n        <view class=\"btn primary\" @tap=\"nextStep\">{{currentStep < 4 ? '下一步' : '创建优惠券'}}</view>\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport DistributionSetting from './distribution/components/DistributionSetting.vue';\r\n// 使用 require 语法导入\r\nconst distributionMixin = require('/subPackages/merchant-admin-marketing/mixins/distributionMixin').default;\r\nimport MarketingPromotionActions from '/subPackages/merchant-admin-marketing/components/MarketingPromotionActions.vue';\r\n\r\nexport default {\r\n  name: 'CouponCreate',\r\n  components: {\r\n    DistributionSetting,\r\n    MarketingPromotionActions\r\n  },\r\n  mixins: [distributionMixin], // 使用分销混入\r\n  data() {\r\n    return {\r\n      currentStep: 1,\r\n      scrollToId: 'step1',\r\n      formData: {\r\n        // 基本信息\r\n        name: '',\r\n        type: 'amount',  // amount: 满减券, discount: 折扣券\r\n        value: '',       // 金额或者折扣值\r\n        description: '',\r\n        couponColor: 'linear-gradient(135deg, #FF9966, #FF5E62)',\r\n        \r\n        // 使用规则\r\n        minAmount: '',   // 最低消费金额\r\n        goodsLimit: 'all', // all: 全部商品, category: 品类, specific: 指定商品\r\n        selectedCategories: [],\r\n        selectedGoods: [],\r\n        validityType: 'days',  // days: 领取后N天有效, fixed: 固定日期\r\n        validPeriod: '',  // validityType为days时使用\r\n        startDate: '',    // validityType为fixed时使用\r\n        endDate: '',      // validityType为fixed时使用\r\n        useTimeLimit: false, // 是否限制使用时间段\r\n        useTimeStart: '',    // 使用时间段开始\r\n        useTimeEnd: '',      // 使用时间段结束\r\n        \r\n        // 发放设置\r\n        totalQuantity: '',  // 发行总量\r\n        perUserLimit: '1',  // 每人限领\r\n        issueType: 'manual', // manual: 手动领取, auto: 自动发放\r\n        autoIssueCondition: 'new', // new: 新用户注册, amount: 订单满额\r\n        autoIssueAmount: '', // issueCondition为amount时使用\r\n\r\n        // 新增属性\r\n        useThreshold: 'no',  // 使用门槛: no - 无门槛, yes - 满额可用\r\n        useInstructions: '', // 使用说明\r\n        quantityType: 'unlimited',  // 发行总量类型: unlimited - 不限制, limited - 限制数量\r\n        userLimitType: 'limited',   // 每人限领类型: unlimited - 不限制, limited - 限制数量\r\n        issueTimeType: 'now',       // 发放时间类型: now - 立即发放, scheduled - 定时发放\r\n        issueTime: '',              // 定时发放时间\r\n        showInCenter: true,         // 是否在领券中心展示\r\n\r\n        // 分销设置\r\n        distributionSettings: {\r\n          enabled: false,\r\n          commissionMode: 'percentage',\r\n          commissions: {\r\n            level1: '',\r\n            level2: '',\r\n            level3: ''\r\n          },\r\n          enableLevel3: false\r\n        }\r\n      },\r\n      \r\n      // 颜色选项\r\n      colorOptions: [\r\n        'linear-gradient(135deg, #FF9966, #FF5E62)',\r\n        'linear-gradient(135deg, #FFA62E, #EA4D2C)',\r\n        'linear-gradient(135deg, #36D1DC, #5B86E5)',\r\n        'linear-gradient(135deg, #3A1C71, #D76D77)',\r\n        'linear-gradient(135deg, #4776E6, #8E54E9)',\r\n        'linear-gradient(135deg, #00B09B, #96C93D)',\r\n      ],\r\n      \r\n      // 模拟数据\r\n      selectedCategories: [\r\n        { id: 1, name: '女装' },\r\n        { id: 2, name: '男装' }\r\n      ],\r\n      selectedGoods: [\r\n        { \r\n          id: 1, \r\n          name: '2023春季新款连衣裙', \r\n          price: 299.00,\r\n          image: '/static/images/goods-1.jpg'\r\n        }\r\n      ],\r\n      hasMerchantDistribution: false, // 商家是否开通分销功能\r\n      tempCouponId: 'temp-' + Date.now(), // 临时ID，实际应该从后端获取\r\n    }\r\n  },\r\n  computed: {\r\n    progressPercentage() {\r\n      return (this.currentStep / 4) * 100;\r\n    }\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    prevStep() {\r\n      if (this.currentStep > 1) {\r\n        this.currentStep -= 1;\r\n        this.scrollToId = `step${this.currentStep}`;\r\n      }\r\n    },\r\n    nextStep() {\r\n      // 简单验证\r\n      if (this.currentStep === 1) {\r\n        if (!this.formData.name || !this.formData.value) {\r\n          uni.showToast({\r\n            title: '请填写必填项',\r\n            icon: 'none'\r\n          });\r\n          return;\r\n        }\r\n        \r\n        // 验证优惠券金额或折扣\r\n        if (this.formData.type === 'amount') {\r\n          if (isNaN(this.formData.value) || parseFloat(this.formData.value) <= 0) {\r\n            uni.showToast({\r\n              title: '请输入有效的优惠金额',\r\n              icon: 'none'\r\n            });\r\n            return;\r\n          }\r\n        } else {\r\n          const discValue = parseFloat(this.formData.value);\r\n          if (isNaN(discValue) || discValue <= 0 || discValue >= 10) {\r\n            uni.showToast({\r\n              title: '请输入有效的折扣(1-9.9)',\r\n              icon: 'none'\r\n            });\r\n            return;\r\n          }\r\n        }\r\n      }\r\n      \r\n      if (this.currentStep < 4) {\r\n        this.currentStep += 1;\r\n        this.scrollToId = `step${this.currentStep}`;\r\n      } else {\r\n        // 提交表单\r\n        this.submitForm();\r\n      }\r\n    },\r\n    submitForm() {\r\n      uni.showLoading({\r\n        title: '创建中...'\r\n      });\r\n      \r\n      // 模拟API请求\r\n      setTimeout(() => {\r\n        uni.hideLoading();\r\n        uni.showToast({\r\n          title: '创建成功',\r\n          icon: 'success',\r\n          duration: 2000,\r\n          success: () => {\r\n            setTimeout(() => {\r\n              uni.navigateBack();\r\n            }, 2000);\r\n          }\r\n        });\r\n      }, 1500);\r\n    },\r\n    toggleTimeLimit(e) {\r\n      this.formData.useTimeLimit = e.detail.value;\r\n    },\r\n    removeCategory(index) {\r\n      this.selectedCategories.splice(index, 1);\r\n    },\r\n    removeGoods(index) {\r\n      this.selectedGoods.splice(index, 1);\r\n    },\r\n    showCategoryPicker() {\r\n      // 显示品类选择弹窗\r\n      uni.showToast({\r\n        title: '品类选择功能待开发',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    selectGoods() {\r\n      // 显示商品选择弹窗\r\n      uni.showToast({\r\n        title: '商品选择功能待开发',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    showDatePicker(type) {\r\n      // 显示日期选择器\r\n      uni.showToast({\r\n        title: `${type === 'start' ? '开始' : '结束'}日期选择功能待开发`,\r\n        icon: 'none'\r\n      });\r\n    },\r\n    showTimePicker(type) {\r\n      // 显示时间选择器\r\n      uni.showToast({\r\n        title: `${type === 'start' ? '开始' : '结束'}时间选择功能待开发`,\r\n        icon: 'none'\r\n      });\r\n    },\r\n    toggleShowInCenter(e) {\r\n      this.formData.showInCenter = e.detail.value;\r\n    },\r\n    showDateTimePicker(type) {\r\n      // 显示日期时间选择器\r\n      uni.showToast({\r\n        title: '日期时间选择功能待开发',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    getValidityText() {\r\n      if (this.formData.validityType === 'days') {\r\n        return this.formData.validPeriod ? `领取后${this.formData.validPeriod}天内有效` : '领取后N天内有效';\r\n      } else {\r\n        if (this.formData.startDate && this.formData.endDate) {\r\n          return `${this.formData.startDate} 至 ${this.formData.endDate}`;\r\n        } else {\r\n          return '2023.10.01-2023.10.31'; // 默认显示\r\n        }\r\n      }\r\n    },\r\n    getGoodsLimitText() {\r\n      switch (this.formData.goodsLimit) {\r\n        case 'all':\r\n          return '全部商品';\r\n        case 'category':\r\n          return `指定品类(${this.selectedCategories.length}个)`;\r\n        case 'specific':\r\n          return `指定商品(${this.selectedGoods.length}个)`;\r\n        default:\r\n          return '全部商品';\r\n      }\r\n    },\r\n    getIssueTypeText() {\r\n      switch (this.formData.issueType) {\r\n        case 'manual':\r\n          return '用户手动领取';\r\n        case 'auto':\r\n          return '系统自动发放';\r\n        case 'admin':\r\n          return '商家手动发放';\r\n        case 'share':\r\n          return '分享领取';\r\n        default:\r\n          return '用户手动领取';\r\n      }\r\n    },\r\n    getAutoIssueConditionText() {\r\n      switch (this.formData.autoIssueCondition) {\r\n        case 'new':\r\n          return '新用户注册';\r\n        case 'birthday':\r\n          return '会员生日';\r\n        case 'amount':\r\n          return `订单满${this.formData.autoIssueAmount || 0}元`;\r\n        default:\r\n          return '新用户注册';\r\n      }\r\n    },\r\n    \r\n    // 更新分销设置\r\n    updateDistributionSettings(settings) {\r\n      this.formData.distributionSettings = settings;\r\n    },\r\n    \r\n    // 检查商家是否开通分销功能\r\n    checkMerchantDistribution() {\r\n      // 调用API检查商家是否开通分销功能\r\n      // 这里模拟API调用\r\n      setTimeout(() => {\r\n        this.hasMerchantDistribution = true;\r\n      }, 500);\r\n    },\r\n    \r\n    // 保存优惠券\r\n    async saveCoupon() {\r\n      // 模拟API调用\r\n      uni.showLoading({\r\n        title: '保存中...'\r\n      });\r\n      setTimeout(() => {\r\n        uni.hideLoading();\r\n        uni.showToast({\r\n          title: '保存成功',\r\n          icon: 'success',\r\n          duration: 2000,\r\n          success: () => {\r\n            setTimeout(() => {\r\n              uni.navigateBack();\r\n            }, 2000);\r\n          }\r\n        });\r\n      }, 1500);\r\n      \r\n      // 保存分销设置\r\n      if (this.hasMerchantDistribution && this.formData.distributionSettings.enabled) {\r\n        const success = await this.saveActivityDistributionSettings('coupon', this.tempCouponId);\r\n        if (!success) {\r\n          return;\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 处理推广操作完成事件\r\n    handlePromotionCompleted(data) {\r\n      console.log('推广操作完成:', data);\r\n      // 根据不同操作类型处理结果\r\n      if (data.action === 'publish') {\r\n        uni.showToast({\r\n          title: '发布成功',\r\n          icon: 'success'\r\n        });\r\n      } else if (data.action === 'top') {\r\n        uni.showToast({\r\n          title: '置顶成功',\r\n          icon: 'success'\r\n        });\r\n      } else if (data.action === 'refresh') {\r\n        uni.showToast({\r\n          title: '刷新成功',\r\n          icon: 'success'\r\n        });\r\n      }\r\n    },\r\n  },\r\n  mounted() {\r\n    this.checkMerchantDistribution();\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.coupon-create-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.custom-navbar {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 44px 16px 10px;\r\n  background: linear-gradient(135deg, #FF7600, #FF3C00);\r\n  position: relative;\r\n  z-index: 100;\r\n}\r\n\r\n.navbar-left {\r\n  width: 40px;\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.back-arrow {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  color: #fff;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  flex: 1;\r\n  text-align: center;\r\n}\r\n\r\n.navbar-right {\r\n  width: 40px;\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.help-btn {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.help-icon {\r\n  color: #fff;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 进度指示器样式 */\r\n.progress-container {\r\n  background: #fff;\r\n  padding: 20px 15px 25px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);\r\n}\r\n\r\n.progress-bar {\r\n  height: 4px;\r\n  background: #EAEAEA;\r\n  border-radius: 2px;\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.progress-track {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  height: 100%;\r\n  width: 100%;\r\n  background: #EAEAEA;\r\n}\r\n\r\n.progress-fill {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  height: 100%;\r\n  background: #FF7600;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.progress-steps {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0 8px;\r\n}\r\n\r\n.step {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  width: 60px;\r\n}\r\n\r\n.step-dot {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  background: #EAEAEA;\r\n  color: #999;\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-bottom: 8px;\r\n  border: 2px solid transparent;\r\n  position: relative;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.step.active .step-dot {\r\n  background: #FF7600;\r\n  color: #fff;\r\n}\r\n\r\n.step.completed .step-dot {\r\n  background: #FF7600;\r\n  border-color: rgba(255, 118, 0, 0.2);\r\n}\r\n\r\n.check-icon {\r\n  width: 10px;\r\n  height: 6px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(-45deg);\r\n  margin-top: -2px;\r\n}\r\n\r\n.step-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.step.active .step-label {\r\n  color: #333;\r\n  font-weight: 500;\r\n}\r\n\r\n.step-line {\r\n  flex: 1;\r\n  height: 1px;\r\n  background: #EAEAEA;\r\n  margin: 0 5px;\r\n  margin-top: -18px;\r\n  transition: background 0.3s ease;\r\n}\r\n\r\n.step-line.active {\r\n  background: #FF7600;\r\n}\r\n\r\n/* 表单容器样式 */\r\n.form-scroll-view {\r\n  height: calc(100vh - 180px); /* 减去导航栏和进度条的高度 */\r\n}\r\n\r\n.form-section {\r\n  padding: 15px;\r\n  margin-bottom: 60px; /* 为底部按钮留出空间 */\r\n}\r\n\r\n.section-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.section-subtitle {\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n/* 优惠券预览样式 */\r\n.coupon-preview-wrapper {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.preview-title {\r\n  font-size: 14px;\r\n  color: #666;\r\n  margin-bottom: 10px;\r\n  display: block;\r\n}\r\n\r\n.coupon-preview {\r\n  width: 100%;\r\n  height: 160px;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.coupon-preview-content {\r\n  flex: 1;\r\n  padding: 20px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n}\r\n\r\n.coupon-name {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #fff;\r\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.coupon-value-container {\r\n  display: flex;\r\n  align-items: flex-end;\r\n  margin-top: 10px;\r\n  height: 50px;\r\n}\r\n\r\n.coupon-value-symbol {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #fff;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.coupon-value {\r\n  font-size: 40px;\r\n  font-weight: 700;\r\n  color: #fff;\r\n  line-height: 1;\r\n  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.coupon-desc {\r\n  font-size: 12px;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  margin-top: 10px;\r\n}\r\n\r\n.coupon-dates {\r\n  display: flex;\r\n  font-size: 12px;\r\n  color: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.coupon-dash-border {\r\n  height: 4px;\r\n  background-image: linear-gradient(to right, rgba(255, 255, 255, 0.7) 50%, transparent 50%);\r\n  background-size: 16px 2px;\r\n  background-repeat: repeat-x;\r\n}\r\n\r\n.coupon-extra {\r\n  height: 36px;\r\n  background: rgba(0, 0, 0, 0.05);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0 20px;\r\n}\r\n\r\n.coupon-limit {\r\n  font-size: 12px;\r\n  color: #fff;\r\n}\r\n\r\n/* 表单项样式 */\r\n.form-block {\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  padding: 15px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);\r\n}\r\n\r\n.form-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.form-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.form-label {\r\n  font-size: 14px;\r\n  color: #333;\r\n  font-weight: 500;\r\n  margin-bottom: 8px;\r\n  display: block;\r\n}\r\n\r\n.form-label.required::before {\r\n  content: '*';\r\n  color: #FF3B30;\r\n  margin-right: 4px;\r\n}\r\n\r\n.form-input {\r\n  width: 100%;\r\n  height: 44px;\r\n  background: #F8FAFC;\r\n  border: 1px solid #EAEAEA;\r\n  border-radius: 8px;\r\n  padding: 0 12px;\r\n  color: #333;\r\n  font-size: 14px;\r\n  box-sizing: border-box;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.form-input:focus {\r\n  border-color: #FF7600;\r\n  background: #FFF;\r\n  box-shadow: 0 0 0 2px rgba(255, 118, 0, 0.1);\r\n}\r\n\r\n.input-limit {\r\n  font-size: 12px;\r\n  color: #999;\r\n  text-align: right;\r\n  margin-top: 4px;\r\n  display: block;\r\n}\r\n\r\n.form-textarea {\r\n  width: 100%;\r\n  height: 80px;\r\n  background: #F8FAFC;\r\n  border: 1px solid #EAEAEA;\r\n  border-radius: 8px;\r\n  padding: 12px;\r\n  color: #333;\r\n  font-size: 14px;\r\n  box-sizing: border-box;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.form-textarea:focus {\r\n  border-color: #FF7600;\r\n  background: #FFF;\r\n  box-shadow: 0 0 0 2px rgba(255, 118, 0, 0.1);\r\n}\r\n\r\n/* 单选按钮组样式 */\r\n.radio-group {\r\n  display: flex;\r\n  margin-top: 5px;\r\n}\r\n\r\n.radio-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-right: 30px;\r\n  cursor: pointer;\r\n}\r\n\r\n.radio-dot {\r\n  width: 18px;\r\n  height: 18px;\r\n  border-radius: 9px;\r\n  border: 2px solid #CCCCCC;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 8px;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.radio-dot-inner {\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 5px;\r\n  background: #FF7600;\r\n}\r\n\r\n.radio-item.active .radio-dot {\r\n  border-color: #FF7600;\r\n}\r\n\r\n.radio-label {\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n/* 金额输入框样式 */\r\n.value-input-wrap {\r\n  position: relative;\r\n  width: 100%;\r\n}\r\n\r\n.value-input {\r\n  padding-right: 40px;\r\n}\r\n\r\n.value-unit {\r\n  position: absolute;\r\n  right: 12px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n/* 颜色选择器样式 */\r\n.color-picker {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 10px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.color-option {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);\r\n  transition: all 0.2s;\r\n}\r\n\r\n.color-option.active {\r\n  transform: scale(1.1);\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.color-check {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  width: 20px;\r\n  height: 20px;\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.check-mark {\r\n  width: 10px;\r\n  height: 6px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(-45deg);\r\n  margin-top: -2px;\r\n}\r\n\r\n/* 底部按钮样式 */\r\n.bottom-buttons {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 70px;\r\n  background: #FFFFFF;\r\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 0 15px;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n\r\n.btn {\r\n  flex: 1;\r\n  height: 44px;\r\n  border-radius: 22px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  margin: 0 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.2s;\r\n}\r\n\r\n.btn:active {\r\n  transform: scale(0.97);\r\n  opacity: 0.9;\r\n}\r\n\r\n.btn.primary {\r\n  background: linear-gradient(135deg, #FF7600, #FF4D00);\r\n  color: #fff;\r\n}\r\n\r\n.btn.secondary {\r\n  background: #F5F5F5;\r\n  color: #666;\r\n}\r\n\r\n/* 使用规则样式 - 步骤2 */\r\n.sub-section {\r\n  margin-top: 25px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.sub-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #333;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.switch-container {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.switch-label {\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n/* 发放设置样式 - 步骤3 */\r\n.issue-methods {\r\n  margin-top: 10px;\r\n}\r\n\r\n.issue-method {\r\n  display: flex;\r\n  align-items: center;\r\n  background: #F8FAFC;\r\n  border-radius: 10px;\r\n  padding: 15px;\r\n  margin-bottom: 15px;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n}\r\n\r\n.issue-method.active {\r\n  background: #FFF9F5;\r\n  border: 1px solid #FF7600;\r\n}\r\n\r\n.method-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 10px;\r\n  margin-right: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex-shrink: 0;\r\n  position: relative;\r\n}\r\n\r\n.method-icon.manual {\r\n  background: linear-gradient(135deg, #36D1DC, #5B86E5);\r\n}\r\n\r\n.method-icon.auto {\r\n  background: linear-gradient(135deg, #FF9966, #FF5E62);\r\n}\r\n\r\n.method-icon.admin {\r\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\r\n}\r\n\r\n.method-icon.share {\r\n  background: linear-gradient(135deg, #FDEB71, #F8D800);\r\n}\r\n\r\n.method-icon::before {\r\n  font-size: 20px;\r\n  color: #fff;\r\n  font-weight: 600;\r\n}\r\n\r\n.method-icon.manual::before {\r\n  content: '券';\r\n}\r\n\r\n.method-icon.auto::before {\r\n  content: '自';\r\n}\r\n\r\n.method-icon.admin::before {\r\n  content: '商';\r\n}\r\n\r\n.method-icon.share::before {\r\n  content: '享';\r\n}\r\n\r\n.method-info {\r\n  flex: 1;\r\n}\r\n\r\n.method-name {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #333;\r\n  margin-bottom: 5px;\r\n  display: block;\r\n}\r\n\r\n.method-desc {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.method-check {\r\n  width: 22px;\r\n  height: 22px;\r\n  border-radius: 11px;\r\n  background: #FF7600;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* 分销设置样式 */\r\n.distribution-setting {\r\n  margin-top: 20px;\r\n  padding: 15px;\r\n  background: #F8FAFC;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);\r\n}\r\n\r\n.distribution-setting .section-header {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.distribution-setting .section-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.distribution-setting .section-subtitle {\r\n  font-size: 13px;\r\n  color: #666;\r\n}\r\n\r\n.distribution-setting .form-block {\r\n  padding: 0;\r\n  box-shadow: none;\r\n}\r\n\r\n.distribution-setting .form-item {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.distribution-setting .form-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.distribution-setting .form-label {\r\n  font-size: 14px;\r\n  color: #333;\r\n  font-weight: 500;\r\n  margin-bottom: 8px;\r\n  display: block;\r\n}\r\n\r\n.distribution-setting .form-label.required::before {\r\n  content: '';\r\n  margin-right: 0;\r\n}\r\n\r\n.distribution-setting .form-input {\r\n  width: 100%;\r\n  height: 44px;\r\n  background: #FFF;\r\n  border: 1px solid #EAEAEA;\r\n  border-radius: 8px;\r\n  padding: 0 12px;\r\n  color: #333;\r\n  font-size: 14px;\r\n  box-sizing: border-box;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.distribution-setting .form-input:focus {\r\n  border-color: #FF7600;\r\n  background: #FFF;\r\n  box-shadow: 0 0 0 2px rgba(255, 118, 0, 0.1);\r\n}\r\n\r\n.distribution-setting .input-limit {\r\n  font-size: 12px;\r\n  color: #999;\r\n  text-align: right;\r\n  margin-top: 4px;\r\n  display: block;\r\n}\r\n\r\n.distribution-setting .form-textarea {\r\n  width: 100%;\r\n  height: 80px;\r\n  background: #FFF;\r\n  border: 1px solid #EAEAEA;\r\n  border-radius: 8px;\r\n  padding: 12px;\r\n  color: #333;\r\n  font-size: 14px;\r\n  box-sizing: border-box;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.distribution-setting .form-textarea:focus {\r\n  border-color: #FF7600;\r\n  background: #FFF;\r\n  box-shadow: 0 0 0 2px rgba(255, 118, 0, 0.1);\r\n}\r\n\r\n.distribution-setting .radio-group {\r\n  margin-top: 0;\r\n}\r\n\r\n.distribution-setting .radio-item {\r\n  margin-right: 20px;\r\n}\r\n\r\n.distribution-setting .radio-dot {\r\n  width: 16px;\r\n  height: 16px;\r\n  border-radius: 8px;\r\n  border: 1px solid #CCCCCC;\r\n  margin-right: 6px;\r\n}\r\n\r\n.distribution-setting .radio-dot-inner {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 4px;\r\n  background: #FF7600;\r\n}\r\n\r\n.distribution-setting .radio-item.active .radio-dot {\r\n  border-color: #FF7600;\r\n}\r\n\r\n.distribution-setting .radio-label {\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.distribution-setting .value-input-wrap {\r\n  position: relative;\r\n  width: 100%;\r\n}\r\n\r\n.distribution-setting .value-input {\r\n  padding-right: 30px;\r\n}\r\n\r\n.distribution-setting .value-unit {\r\n  position: absolute;\r\n  right: 12px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.distribution-setting .color-picker {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.distribution-setting .color-option {\r\n  width: 36px;\r\n  height: 36px;\r\n  border-radius: 7px;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.distribution-setting .color-option.active {\r\n  transform: scale(1.05);\r\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.distribution-setting .color-check {\r\n  width: 16px;\r\n  height: 16px;\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-radius: 50%;\r\n}\r\n\r\n.distribution-setting .check-mark {\r\n  width: 8px;\r\n  height: 5px;\r\n  border-left: 1.5px solid #fff;\r\n  border-bottom: 1.5px solid #fff;\r\n  transform: rotate(-45deg);\r\n  margin-top: -1.5px;\r\n}\r\n\r\n/* 确认页样式 - 步骤4 */\r\n.summary-card {\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  padding: 15px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.summary-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  padding-bottom: 10px;\r\n  margin-bottom: 10px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.summary-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.summary-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.summary-label {\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.summary-value {\r\n  font-size: 14px;\r\n  color: #333;\r\n  text-align: right;\r\n}\r\n\r\n/* 新增样式 */\r\n.preview-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.block-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.form-actions {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  padding: 0 15px;\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 70px;\r\n  background: #FFFFFF;\r\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n  box-sizing: border-box;\r\n}\r\n\r\n.btn-prev, .btn-create {\r\n  flex: 1;\r\n  height: 44px;\r\n  border-radius: 22px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  margin: 0 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.2s;\r\n}\r\n\r\n.btn-prev {\r\n  background: #F5F5F5;\r\n  color: #666;\r\n}\r\n\r\n.btn-create {\r\n  background: linear-gradient(135deg, #FF7600, #FF4D00);\r\n  color: #fff;\r\n}\r\n\r\n.btn-prev:active, .btn-create:active {\r\n  transform: scale(0.97);\r\n  opacity: 0.9;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/create-coupon.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAqqBA,MAAK,sBAAuB,MAAW;AAEvC,MAAM,oBAAoB,QAAQ,gEAAgE,EAAE;AACpG,MAAK,4BAA6B,MAAW;AAE7C,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,YAAY;AAAA,IACV;AAAA,IACA;AAAA,EACD;AAAA,EACD,QAAQ,CAAC,iBAAiB;AAAA;AAAA,EAC1B,OAAO;AACL,WAAO;AAAA,MACL,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA,QAER,MAAM;AAAA,QACN,MAAM;AAAA;AAAA,QACN,OAAO;AAAA;AAAA,QACP,aAAa;AAAA,QACb,aAAa;AAAA;AAAA,QAGb,WAAW;AAAA;AAAA,QACX,YAAY;AAAA;AAAA,QACZ,oBAAoB,CAAE;AAAA,QACtB,eAAe,CAAE;AAAA,QACjB,cAAc;AAAA;AAAA,QACd,aAAa;AAAA;AAAA,QACb,WAAW;AAAA;AAAA,QACX,SAAS;AAAA;AAAA,QACT,cAAc;AAAA;AAAA,QACd,cAAc;AAAA;AAAA,QACd,YAAY;AAAA;AAAA;AAAA,QAGZ,eAAe;AAAA;AAAA,QACf,cAAc;AAAA;AAAA,QACd,WAAW;AAAA;AAAA,QACX,oBAAoB;AAAA;AAAA,QACpB,iBAAiB;AAAA;AAAA;AAAA,QAGjB,cAAc;AAAA;AAAA,QACd,iBAAiB;AAAA;AAAA,QACjB,cAAc;AAAA;AAAA,QACd,eAAe;AAAA;AAAA,QACf,eAAe;AAAA;AAAA,QACf,WAAW;AAAA;AAAA,QACX,cAAc;AAAA;AAAA;AAAA,QAGd,sBAAsB;AAAA,UACpB,SAAS;AAAA,UACT,gBAAgB;AAAA,UAChB,aAAa;AAAA,YACX,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,UACT;AAAA,UACD,cAAc;AAAA,QAChB;AAAA,MACD;AAAA;AAAA,MAGD,cAAc;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA;AAAA,MAGD,oBAAoB;AAAA,QAClB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,QACrB,EAAE,IAAI,GAAG,MAAM,KAAK;AAAA,MACrB;AAAA,MACD,eAAe;AAAA,QACb;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,QACT;AAAA,MACD;AAAA,MACD,yBAAyB;AAAA;AAAA,MACzB,cAAc,UAAU,KAAK,IAAK;AAAA;AAAA,IACpC;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,qBAAqB;AACnB,aAAQ,KAAK,cAAc,IAAK;AAAA,IAClC;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,WAAW;AACT,UAAI,KAAK,cAAc,GAAG;AACxB,aAAK,eAAe;AACpB,aAAK,aAAa,OAAO,KAAK,WAAW;AAAA,MAC3C;AAAA,IACD;AAAA,IACD,WAAW;AAET,UAAI,KAAK,gBAAgB,GAAG;AAC1B,YAAI,CAAC,KAAK,SAAS,QAAQ,CAAC,KAAK,SAAS,OAAO;AAC/CA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AACD;AAAA,QACF;AAGA,YAAI,KAAK,SAAS,SAAS,UAAU;AACnC,cAAI,MAAM,KAAK,SAAS,KAAK,KAAK,WAAW,KAAK,SAAS,KAAK,KAAK,GAAG;AACtEA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AACD;AAAA,UACF;AAAA,eACK;AACL,gBAAM,YAAY,WAAW,KAAK,SAAS,KAAK;AAChD,cAAI,MAAM,SAAS,KAAK,aAAa,KAAK,aAAa,IAAI;AACzDA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AACD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,UAAI,KAAK,cAAc,GAAG;AACxB,aAAK,eAAe;AACpB,aAAK,aAAa,OAAO,KAAK,WAAW;AAAA,aACpC;AAEL,aAAK,WAAU;AAAA,MACjB;AAAA,IACD;AAAA,IACD,aAAa;AACXA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS,MAAM;AACb,uBAAW,MAAM;AACfA,4BAAG,MAAC,aAAY;AAAA,YACjB,GAAE,GAAI;AAAA,UACT;AAAA,QACF,CAAC;AAAA,MACF,GAAE,IAAI;AAAA,IACR;AAAA,IACD,gBAAgB,GAAG;AACjB,WAAK,SAAS,eAAe,EAAE,OAAO;AAAA,IACvC;AAAA,IACD,eAAe,OAAO;AACpB,WAAK,mBAAmB,OAAO,OAAO,CAAC;AAAA,IACxC;AAAA,IACD,YAAY,OAAO;AACjB,WAAK,cAAc,OAAO,OAAO,CAAC;AAAA,IACnC;AAAA,IACD,qBAAqB;AAEnBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,cAAc;AAEZA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,eAAe,MAAM;AAEnBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,GAAG,SAAS,UAAU,OAAO,IAAI;AAAA,QACxC,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,eAAe,MAAM;AAEnBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,GAAG,SAAS,UAAU,OAAO,IAAI;AAAA,QACxC,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,mBAAmB,GAAG;AACpB,WAAK,SAAS,eAAe,EAAE,OAAO;AAAA,IACvC;AAAA,IACD,mBAAmB,MAAM;AAEvBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,kBAAkB;AAChB,UAAI,KAAK,SAAS,iBAAiB,QAAQ;AACzC,eAAO,KAAK,SAAS,cAAc,MAAM,KAAK,SAAS,WAAW,SAAS;AAAA,aACtE;AACL,YAAI,KAAK,SAAS,aAAa,KAAK,SAAS,SAAS;AACpD,iBAAO,GAAG,KAAK,SAAS,SAAS,MAAM,KAAK,SAAS,OAAO;AAAA,eACvD;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACD;AAAA,IACD,oBAAoB;AAClB,cAAQ,KAAK,SAAS,YAAU;AAAA,QAC9B,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO,QAAQ,KAAK,mBAAmB,MAAM;AAAA,QAC/C,KAAK;AACH,iBAAO,QAAQ,KAAK,cAAc,MAAM;AAAA,QAC1C;AACE,iBAAO;AAAA,MACX;AAAA,IACD;AAAA,IACD,mBAAmB;AACjB,cAAQ,KAAK,SAAS,WAAS;AAAA,QAC7B,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACX;AAAA,IACD;AAAA,IACD,4BAA4B;AAC1B,cAAQ,KAAK,SAAS,oBAAkB;AAAA,QACtC,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO,MAAM,KAAK,SAAS,mBAAmB,CAAC;AAAA,QACjD;AACE,iBAAO;AAAA,MACX;AAAA,IACD;AAAA;AAAA,IAGD,2BAA2B,UAAU;AACnC,WAAK,SAAS,uBAAuB;AAAA,IACtC;AAAA;AAAA,IAGD,4BAA4B;AAG1B,iBAAW,MAAM;AACf,aAAK,0BAA0B;AAAA,MAChC,GAAE,GAAG;AAAA,IACP;AAAA;AAAA,IAGD,MAAM,aAAa;AAEjBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AACD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS,MAAM;AACb,uBAAW,MAAM;AACfA,4BAAG,MAAC,aAAY;AAAA,YACjB,GAAE,GAAI;AAAA,UACT;AAAA,QACF,CAAC;AAAA,MACF,GAAE,IAAI;AAGP,UAAI,KAAK,2BAA2B,KAAK,SAAS,qBAAqB,SAAS;AAC9E,cAAM,UAAU,MAAM,KAAK,iCAAiC,UAAU,KAAK,YAAY;AACvF,YAAI,CAAC,SAAS;AACZ;AAAA,QACF;AAAA,MACF;AAAA,IACD;AAAA;AAAA,IAGD,yBAAyB,MAAM;AAC7BA,oBAAY,MAAA,MAAA,OAAA,iFAAA,WAAW,IAAI;AAE3B,UAAI,KAAK,WAAW,WAAW;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,iBACQ,KAAK,WAAW,OAAO;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH,WAAW,KAAK,WAAW,WAAW;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACD;AAAA,EACF;AAAA,EACD,UAAU;AACR,SAAK,0BAAyB;AAAA,EAChC;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACj/BA,GAAG,WAAW,eAAe;"}