/**
 * 用户信息管理模块
 */

// 默认头像
const DEFAULT_AVATAR = '/static/images/default-avatar.png';
// 默认昵称
const DEFAULT_NICKNAME = '同城用户';

// 本地存储的用户信息的键名
const USER_INFO_KEY = 'user_info';

/**
 * 获取本地存储的用户信息
 */
export const getLocalUserInfo = () => {
  try {
    return uni.getStorageSync(USER_INFO_KEY) || null;
  } catch (e) {
    console.error('获取用户信息失败', e);
    return null;
  }
};

/**
 * 保存用户信息到本地
 * @param {Object} userInfo - 用户信息对象
 */
export const saveUserInfo = (userInfo) => {
  if (!userInfo) return;
  
  try {
    // 合并基本信息
    const savedInfo = {
      ...getLocalUserInfo(),
      ...userInfo,
      // 添加更新时间
      updateTime: new Date().getTime()
    };
    
    uni.setStorageSync(USER_INFO_KEY, savedInfo);
    return savedInfo;
  } catch (e) {
    console.error('保存用户信息失败', e);
    return null;
  }
};

/**
 * 从服务器获取用户信息
 * @param {String} token - 用户token
 */
export const fetchUserInfo = (token) => {
  return new Promise((resolve, reject) => {
    // 这里应该调用后端接口获取用户信息
    // 示例中模拟请求
    setTimeout(() => {
      const userInfo = {
        userId: 'user_' + Math.random().toString(36).substr(2, 9),
        // 如果没有设置过头像和昵称，使用默认值
        avatar: DEFAULT_AVATAR,
        nickname: DEFAULT_NICKNAME,
        // 其他信息
        level: 1,
        points: 0,
        isVip: false
      };
      
      // 保存到本地
      saveUserInfo(userInfo);
      resolve(userInfo);
    }, 500);
  });
};

/**
 * 更新本地用户的昵称和头像
 * @param {Object} profile - 包含昵称和头像的对象
 */
export const updateUserProfile = async (profile) => {
  try {
    // 获取当前用户信息
    const currentUser = getLocalUserInfo();
    
    // 更新信息
    const updatedUser = {
      ...currentUser,
      avatar: profile.avatarUrl || currentUser?.avatar || DEFAULT_AVATAR,
      nickname: profile.nickName || currentUser?.nickname || DEFAULT_NICKNAME
    };
    
    // 保存到本地
    saveUserInfo(updatedUser);
    
    // 同步到服务器
    // 这里应该调用服务器接口保存用户信息
    // 下面是示例代码
    /*
    await uni.request({
      url: 'https://your-api-domain/api/user/updateProfile',
      method: 'POST',
      data: {
        avatar: updatedUser.avatar,
        nickname: updatedUser.nickname
      }
    });
    */
    
    return updatedUser;
  } catch (e) {
    console.error('更新用户资料失败', e);
    return null;
  }
};

/**
 * 获取默认的用户信息
 */
export const getDefaultUserInfo = () => {
  return {
    avatar: DEFAULT_AVATAR,
    nickname: DEFAULT_NICKNAME
  };
};

/**
 * 检查是否有用户信息
 */
export const hasUserInfo = () => {
  const userInfo = getLocalUserInfo();
  return !!(userInfo && (userInfo.nickname !== DEFAULT_NICKNAME || userInfo.avatar !== DEFAULT_AVATAR));
};

/**
 * 登出，清除用户信息
 */
export const logout = () => {
  try {
    uni.removeStorageSync(USER_INFO_KEY);
    return true;
  } catch (e) {
    console.error('清除用户信息失败', e);
    return false;
  }
}; 