<template>
  <view class="customers-management-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">客户运营</text>
      <view class="navbar-right">
        <view class="help-icon">?</view>
      </view>
    </view>
    
    <!-- 客户数据概览 -->
    <view class="data-overview">
      <view class="overview-card">
        <text class="overview-number">{{customerData.totalCustomers}}</text>
        <text class="overview-label">总客户数</text>
      </view>
      <view class="overview-card">
        <text class="overview-number">{{customerData.activeCustomers}}</text>
        <text class="overview-label">活跃客户</text>
      </view>
      <view class="overview-card">
        <text class="overview-number">{{customerData.newCustomers}}</text>
        <text class="overview-label">新增客户</text>
      </view>
      <view class="overview-card">
        <text class="overview-number">{{customerData.memberRate}}%</text>
        <text class="overview-label">会员占比</text>
      </view>
    </view>
    
    <!-- 主要功能模块 -->
    <view class="module-grid">
      <!-- 客户数据中心 -->
      <view class="module-card" @click="navigateTo('./data-center/index')">
        <view class="module-icon data-center-icon">📊</view>
        <view class="module-content">
          <text class="module-title">客户数据中心</text>
          <text class="module-desc">全面的客户数据分析与洞察</text>
        </view>
        <view class="arrow-icon">›</view>
      </view>
      
      <!-- 会员体系管理 -->
      <view class="module-card" @click="navigateTo('./membership/index')">
        <view class="module-icon membership-icon">👑</view>
        <view class="module-content">
          <text class="module-title">会员体系管理</text>
          <text class="module-desc">会员等级、权益与积分规则</text>
        </view>
        <view class="arrow-icon">›</view>
      </view>
      
      <!-- 客户互动管理 -->
      <view class="module-card" @click="navigateTo('./interaction/index')">
        <view class="module-icon interaction-icon">💬</view>
        <view class="module-content">
          <text class="module-title">客户互动管理</text>
          <text class="module-desc">消息中心、活动通知与互动</text>
        </view>
        <view class="arrow-icon">›</view>
      </view>
      
      <!-- 客户忠诚度管理 -->
      <view class="module-card" @click="navigateTo('./loyalty/index')">
        <view class="module-icon loyalty-icon">❤️</view>
        <view class="module-content">
          <text class="module-title">客户忠诚度管理</text>
          <text class="module-desc">复购激励、会员特权与推荐</text>
        </view>
        <view class="arrow-icon">›</view>
      </view>
    </view>
    
    <!-- 最近活跃客户 -->
    <view class="recent-section">
      <view class="section-header">
        <text class="section-title">最近活跃客户</text>
        <text class="section-more" @click="navigateTo('./data-center/active')">查看更多</text>
      </view>
      <view class="customer-list">
        <view 
          v-for="(customer, index) in recentActiveCustomers" 
          :key="index"
          class="customer-item"
          @click="viewCustomerDetail(customer.id)">
          <image class="customer-avatar" :src="customer.avatar" mode="aspectFill"></image>
          <view class="customer-info">
            <text class="customer-name">{{customer.name}}</text>
            <text class="customer-meta">{{customer.lastActive}} | {{customer.orderCount}}次消费</text>
          </view>
          <view class="customer-value">
            <text class="value-label">客户价值</text>
            <text class="value-number">¥{{customer.totalSpent}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      customerData: {
        totalCustomers: '1,256',
        activeCustomers: '368',
        newCustomers: '42',
        memberRate: '58'
      },
      recentActiveCustomers: [
        {
          id: '10001',
          name: '张三',
          avatar: '/static/images/avatar-1.png',
          lastActive: '今天',
          orderCount: 8,
          totalSpent: '1,280.50'
        },
        {
          id: '10002',
          name: '李四',
          avatar: '/static/images/avatar-2.png',
          lastActive: '昨天',
          orderCount: 5,
          totalSpent: '956.00'
        },
        {
          id: '10003',
          name: '王五',
          avatar: '/static/images/avatar-3.png',
          lastActive: '3天前',
          orderCount: 12,
          totalSpent: '2,340.80'
        }
      ]
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    navigateTo(url) {
      uni.navigateTo({ url });
    },
    viewCustomerDetail(id) {
      uni.navigateTo({
        url: `/pages/customer/index?id=${id}`
      });
    }
  }
}
</script>

<style>
.customers-management-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 20px;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}

.data-overview {
  display: flex;
  padding: 16px;
  margin-top: -20px;
  background-color: #fff;
  border-radius: 0 0 16px 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.overview-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 0;
}

.overview-number {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 12px;
  color: #999;
}

.module-grid {
  padding: 16px;
}

.module-card {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.module-icon {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-right: 16px;
}

.data-center-icon {
  background-color: #e6f7ff;
  color: #1677FF;
}

.membership-icon {
  background-color: #fff7e6;
  color: #fa8c16;
}

.interaction-icon {
  background-color: #f6ffed;
  color: #52c41a;
}

.loyalty-icon {
  background-color: #fff1f0;
  color: #f5222d;
}

.module-content {
  flex: 1;
}

.module-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.module-desc {
  font-size: 12px;
  color: #999;
}

.arrow-icon {
  font-size: 24px;
  color: #ccc;
}

.recent-section {
  padding: 0 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-more {
  font-size: 12px;
  color: #1677FF;
}

.customer-list {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  overflow: hidden;
}

.customer-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.customer-item:last-child {
  border-bottom: none;
}

.customer-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 12px;
  background-color: #f0f0f0;
}

.customer-info {
  flex: 1;
}

.customer-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.customer-meta {
  font-size: 12px;
  color: #999;
}

.customer-value {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.value-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 2px;
}

.value-number {
  font-size: 14px;
  font-weight: 600;
  color: #ff6a00;
}
</style> 