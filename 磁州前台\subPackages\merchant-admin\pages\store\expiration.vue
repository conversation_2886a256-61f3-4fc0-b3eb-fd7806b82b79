<template>
  <view class="expiration-container">
    <!-- 顶部导航栏 -->
    <view class="header-area">
      <view class="safe-area-top"></view>
      <view class="custom-navbar">
        <view class="navbar-left" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="back-icon" style="filter: brightness(0) invert(1);"></image>
        </view>
        <view class="navbar-title">到期提醒</view>
        <view class="navbar-right"></view>
      </view>
    </view>
    
    <!-- 背景装饰 -->
    <view class="bg-decoration bg-circle-1"></view>
    <view class="bg-decoration bg-circle-2"></view>
    <view class="bg-decoration bg-circle-3"></view>
    
    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 提醒设置 -->
      <view class="setting-card">
        <view class="card-header">
          <text class="card-title">提醒设置</text>
        </view>
        <view class="setting-item">
          <text class="setting-label">资质到期提醒</text>
          <switch :checked="settings.expirationAlert" @change="toggleSetting('expirationAlert')" color="#0A84FF" />
        </view>
        <view class="setting-item">
          <text class="setting-label">提前提醒时间</text>
          <picker mode="selector" :range="reminderTimeOptions" @change="onReminderTimeChange" class="picker">
            <view class="picker-value">{{reminderTimeOptions[settings.reminderTimeIndex]}}</view>
          </picker>
        </view>
        <view class="setting-item">
          <text class="setting-label">提醒方式</text>
          <view class="checkbox-group">
            <view class="checkbox-item" :class="{ active: settings.reminderMethods.includes('app') }" @click="toggleMethod('app')">
              <text>应用通知</text>
            </view>
            <view class="checkbox-item" :class="{ active: settings.reminderMethods.includes('sms') }" @click="toggleMethod('sms')">
              <text>短信提醒</text>
            </view>
            <view class="checkbox-item" :class="{ active: settings.reminderMethods.includes('wechat') }" @click="toggleMethod('wechat')">
              <text>微信通知</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 筛选标签 -->
      <view class="filter-tabs">
        <view 
          class="tab-item" 
          :class="{ active: currentStatus === status }"
          v-for="status in ['all', 'valid', 'expiring', 'expired']" 
          :key="status"
          @click="filterByStatus(status)"
        >
          {{getStatusText(status)}}
        </view>
      </view>
      
      <!-- 到期项目列表 -->
      <view class="expiration-list" v-if="filteredItems.length > 0">
        <view 
          class="expiration-item" 
          :class="[getItemStatusClass(item)]"
          v-for="(item, index) in filteredItems" 
          :key="index"
        >
          <view class="item-header">
            <text class="item-title">{{item.name}}</text>
            <view class="item-status" :class="[getItemStatusClass(item)]">
              {{getItemStatusText(item)}}
            </view>
          </view>
          
          <view class="item-info">
            <view class="info-row">
              <text class="info-label">证件编号</text>
              <text class="info-value">{{item.number}}</text>
            </view>
            <view class="info-row">
              <text class="info-label">生效日期</text>
              <text class="info-value">{{item.startDate}}</text>
            </view>
            <view class="info-row">
              <text class="info-label">到期日期</text>
              <text class="info-value exp-date" :class="[getItemStatusClass(item)]">{{item.endDate}}</text>
            </view>
            <view class="info-row">
              <text class="info-label">发证机关</text>
              <text class="info-value">{{item.authority}}</text>
            </view>
          </view>
          
          <view class="item-img-preview" @click="previewImage(item.image)">
            <image :src="item.image" mode="aspectFit" class="preview-img"></image>
          </view>
          
          <view class="item-actions">
            <view class="action-btn renew" @click="renewCertificate(index)" v-if="shouldShowRenew(item)">
              <text class="action-icon">↺</text>
              <text>续期</text>
            </view>
            <view class="action-btn replace" @click="replaceCertificate(index)">
              <text class="action-icon">⟲</text>
              <text>更换</text>
            </view>
            <view class="action-btn view" @click="viewDetail(index)">
              <text class="action-icon">👁</text>
              <text>查看</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-else>
        <image src="/static/images/tabbar/无数据.png" class="empty-icon"></image>
        <text class="empty-text">暂无{{getStatusText(currentStatus)}}资质</text>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
    
    <!-- 续期证件弹窗 -->
    <view class="modal-overlay" v-if="showRenewModal" @click="cancelRenew"></view>
    <view class="renew-modal" v-if="showRenewModal">
      <view class="modal-header">
        <text class="modal-title">证件续期</text>
        <view class="close-btn" @click="cancelRenew">×</view>
      </view>
      
      <view class="modal-content">
        <view class="form-item">
          <text class="form-label">证件名称</text>
          <text class="form-value">{{renewItem.name}}</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">证件编号</text>
          <input type="text" class="form-input" v-model="renewForm.number" placeholder="请输入新的证件编号"/>
        </view>
        
        <view class="form-item">
          <text class="form-label">生效日期</text>
          <picker mode="date" :value="renewForm.startDate" @change="onStartDateChange" class="date-picker">
            <view class="picker-view">
              <text class="picker-value">{{renewForm.startDate || '请选择生效日期'}}</text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
        </view>
        
        <view class="form-item">
          <text class="form-label">到期日期</text>
          <picker mode="date" :value="renewForm.endDate" @change="onEndDateChange" class="date-picker">
            <view class="picker-view">
              <text class="picker-value">{{renewForm.endDate || '请选择到期日期'}}</text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
        </view>
        
        <view class="form-item">
          <text class="form-label">证件图片</text>
          <view class="upload-area" @click="uploadRenewImage" v-if="!renewForm.image">
            <text class="upload-icon">+</text>
            <text class="upload-text">上传新证件图片</text>
          </view>
          <view class="uploaded-image" v-else>
            <image :src="renewForm.image" mode="aspectFit" class="image-preview"></image>
            <view class="image-actions">
              <view class="image-action" @click="uploadRenewImage">更换</view>
              <view class="image-action delete" @click="removeRenewImage">删除</view>
            </view>
          </view>
        </view>
      </view>
      
      <view class="modal-footer">
        <button class="modal-btn cancel" @click="cancelRenew">取消</button>
        <button class="modal-btn confirm" @click="confirmRenew" :disabled="!canRenew">提交续期</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20,
      currentStatus: 'all', // all, valid, expiring, expired
      showRenewModal: false,
      reminderTimeOptions: ['提前7天', '提前15天', '提前30天', '提前60天', '提前90天'],
      settings: {
        expirationAlert: true,
        reminderTimeIndex: 2, // 默认提前30天
        reminderMethods: ['app', 'sms']
      },
      certItems: [
        {
          name: '食品经营许可证',
          number: 'JY13511020123456',
          startDate: '2022-05-20',
          endDate: '2023-05-19',
          image: '/static/images/cert-1.jpg',
          authority: '市场监督管理局',
          status: 'expired'
        },
        {
          name: '卫生许可证',
          number: 'WS3511020789123',
          startDate: '2023-01-15',
          endDate: '2023-12-30',
          image: '/static/images/cert-2.jpg',
          authority: '卫生健康委员会',
          status: 'expiring'
        },
        {
          name: '营业执照',
          number: '91350100MA8RBU7X1P',
          startDate: '2023-03-10',
          endDate: '2028-03-09',
          image: '/static/images/cert-3.jpg',
          authority: '市场监督管理局',
          status: 'valid'
        },
        {
          name: '餐饮服务许可证',
          number: 'CS3511023577946',
          startDate: '2023-06-01',
          endDate: '2026-05-31',
          image: '/static/images/cert-4.jpg',
          authority: '市场监督管理局',
          status: 'valid'
        },
      ],
      renewIndex: -1,
      renewItem: {},
      renewForm: {
        number: '',
        startDate: '',
        endDate: '',
        image: ''
      }
    }
  },
  computed: {
    filteredItems() {
      if (this.currentStatus === 'all') {
        return this.certItems;
      }
      return this.certItems.filter(item => item.status === this.currentStatus);
    },
    canRenew() {
      return this.renewForm.number && this.renewForm.startDate && 
             this.renewForm.endDate && this.renewForm.image;
    }
  },
  onLoad() {
    this.setStatusBarHeight();
    this.checkExpiringCertificates();
  },
  methods: {
    setStatusBarHeight() {
      uni.getSystemInfo({
        success: (res) => {
          this.statusBarHeight = res.statusBarHeight;
          if (typeof document !== 'undefined') {
            document.documentElement.style.setProperty('--status-bar-height', `${this.statusBarHeight}px`);
          }
        }
      });
    },
    goBack() {
      uni.navigateBack();
    },
    
    // 筛选相关方法
    getStatusText(status) {
      const statusMap = {
        'all': '全部',
        'valid': '有效',
        'expiring': '即将到期',
        'expired': '已过期'
      };
      return statusMap[status] || '未知';
    },
    filterByStatus(status) {
      this.currentStatus = status;
    },
    getItemStatusClass(item) {
      return item.status;
    },
    getItemStatusText(item) {
      const statusMap = {
        'valid': '有效',
        'expiring': '即将到期',
        'expired': '已过期'
      };
      return statusMap[item.status] || '未知';
    },
    
    // 设置相关方法
    toggleSetting(setting) {
      this.settings[setting] = !this.settings[setting];
    },
    onReminderTimeChange(e) {
      this.settings.reminderTimeIndex = parseInt(e.detail.value);
    },
    toggleMethod(method) {
      const index = this.settings.reminderMethods.indexOf(method);
      if (index > -1) {
        // 确保至少保留一种提醒方式
        if (this.settings.reminderMethods.length > 1) {
          this.settings.reminderMethods.splice(index, 1);
        } else {
          uni.showToast({
            title: '至少需要一种提醒方式',
            icon: 'none'
          });
        }
      } else {
        this.settings.reminderMethods.push(method);
      }
    },
    
    // 证件相关方法
    checkExpiringCertificates() {
      // 检查证件是否即将到期或已过期
      const today = new Date();
      const reminderDays = [7, 15, 30, 60, 90][this.settings.reminderTimeIndex];
      
      this.certItems.forEach(item => {
        const endDate = new Date(item.endDate);
        const diffTime = endDate - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays < 0) {
          item.status = 'expired';
        } else if (diffDays <= reminderDays) {
          item.status = 'expiring';
        } else {
          item.status = 'valid';
        }
      });
    },
    previewImage(url) {
      uni.previewImage({
        urls: [url],
        current: url
      });
    },
    shouldShowRenew(item) {
      return item.status === 'expiring' || item.status === 'expired';
    },
    renewCertificate(index) {
      this.renewIndex = index;
      this.renewItem = { ...this.certItems[index] };
      this.renewForm = {
        number: this.renewItem.number,
        startDate: this.getCurrentDate(),
        endDate: '',
        image: ''
      };
      this.showRenewModal = true;
    },
    replaceCertificate(index) {
      this.renewIndex = index;
      this.renewItem = { ...this.certItems[index] };
      this.renewForm = {
        number: this.renewItem.number,
        startDate: this.renewItem.startDate,
        endDate: this.renewItem.endDate,
        image: ''
      };
      this.showRenewModal = true;
    },
    viewDetail(index) {
      const item = this.certItems[index];
      uni.previewImage({
        urls: [item.image],
        current: item.image
      });
    },
    cancelRenew() {
      this.showRenewModal = false;
    },
    onStartDateChange(e) {
      this.renewForm.startDate = e.detail.value;
    },
    onEndDateChange(e) {
      this.renewForm.endDate = e.detail.value;
    },
    uploadRenewImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.renewForm.image = res.tempFilePaths[0];
        }
      });
    },
    removeRenewImage() {
      this.renewForm.image = '';
    },
    confirmRenew() {
      if (!this.canRenew) {
        uni.showToast({
          title: '请完善续期信息',
          icon: 'none'
        });
        return;
      }
      
      // 检查日期是否有效
      if (new Date(this.renewForm.startDate) >= new Date(this.renewForm.endDate)) {
        uni.showToast({
          title: '到期日期必须晚于生效日期',
          icon: 'none'
        });
        return;
      }
      
      // 更新证件信息
      if (this.renewIndex > -1) {
        const updatedCert = {
          ...this.certItems[this.renewIndex],
          number: this.renewForm.number,
          startDate: this.renewForm.startDate,
          endDate: this.renewForm.endDate,
          image: this.renewForm.image
        };
        
        this.certItems.splice(this.renewIndex, 1, updatedCert);
      }
      
      // 重新检查证件状态
      this.checkExpiringCertificates();
      
      this.showRenewModal = false;
      
      uni.showToast({
        title: '证件更新成功',
        icon: 'success'
      });
    },
    getCurrentDate() {
      const date = new Date();
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
  }
}
</script>

<style lang="scss">
.expiration-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(110rpx + var(--status-bar-height, 20px));
}

/* 顶部导航栏样式 */
.header-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
  color: #fff;
}

.safe-area-top {
  height: var(--status-bar-height, 20px);
  width: 100%;
}

.custom-navbar {
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
}

.navbar-left, .navbar-right {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 48rpx;
  height: 48rpx;
}

/* 背景装饰样式 */
.bg-decoration {
  position: absolute;
  z-index: -1;
  border-radius: 50%;
  opacity: 0.07;
  background-color: #0A84FF;
}

.bg-circle-1 {
  top: -100rpx;
  right: -150rpx;
  width: 400rpx;
  height: 400rpx;
}

.bg-circle-2 {
  top: 20%;
  left: -200rpx;
  width: 500rpx;
  height: 500rpx;
}

.bg-circle-3 {
  bottom: 10%;
  right: -100rpx;
  width: 300rpx;
  height: 300rpx;
}

.content-scroll {
  padding: 30rpx;
}

/* 设置卡片样式 */
.setting-card {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.card-header {
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 20rpx;
}

.card-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 8rpx;
  height: 32rpx;
  background-color: #0A84FF;
  border-radius: 4rpx;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #F2F2F7;
}

.setting-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.setting-label {
  font-size: 28rpx;
  color: #333;
}

.picker {
  height: 60rpx;
}

.picker-value {
  font-size: 28rpx;
  color: #0A84FF;
}

.checkbox-group {
  display: flex;
  flex-wrap: wrap;
}

.checkbox-item {
  background-color: #F5F8FC;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  margin-left: 16rpx;
  font-size: 24rpx;
  color: #666;
}

.checkbox-item.active {
  background-color: rgba(10, 132, 255, 0.1);
  color: #0A84FF;
  border: 1rpx solid #0A84FF;
}

/* 筛选标签样式 */
.filter-tabs {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
  overflow-x: auto;
  white-space: nowrap;
}

.tab-item {
  padding: 16rpx 30rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #F5F8FC;
  border-radius: 30rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.tab-item.active {
  background-color: #0A84FF;
  color: #FFFFFF;
}

/* 到期项目列表样式 */
.expiration-list {
  margin-bottom: 30rpx;
}

.expiration-item {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  border-left: 8rpx solid #34C759;
}

.expiration-item.expiring {
  border-left-color: #FF9500;
}

.expiration-item.expired {
  border-left-color: #FF3B30;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.item-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.item-status {
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  background-color: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.item-status.expiring {
  background-color: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}

.item-status.expired {
  background-color: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}

.item-info {
  margin-bottom: 20rpx;
}

.info-row {
  display: flex;
  margin-bottom: 10rpx;
}

.info-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.exp-date.expiring {
  color: #FF9500;
}

.exp-date.expired {
  color: #FF3B30;
}

.item-img-preview {
  width: 100%;
  height: 300rpx;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  background-color: #F5F8FC;
}

.preview-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.item-actions {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  margin-left: 16rpx;
  font-size: 24rpx;
}

.action-icon {
  margin-right: 6rpx;
  font-size: 24rpx;
}

.action-btn.renew {
  background-color: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}

.action-btn.replace {
  background-color: rgba(10, 132, 255, 0.1);
  color: #0A84FF;
}

.action-btn.view {
  background-color: rgba(142, 142, 147, 0.1);
  color: #8E8E93;
}

/* 空状态样式 */
.empty-state {
  padding: 60rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 40rpx;
}

/* 模态弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
}

.renew-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-height: 90vh;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  z-index: 1000;
  overflow: hidden;
}

.modal-header {
  padding: 30rpx;
  text-align: center;
  position: relative;
  border-bottom: 2rpx solid #F2F2F7;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  font-size: 36rpx;
  color: #999;
}

.modal-content {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.form-value {
  display: block;
  font-size: 28rpx;
  color: #333;
  padding: 16rpx 0;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background-color: #F5F8FC;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
}

.date-picker {
  width: 100%;
}

.picker-view {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #F5F8FC;
  height: 80rpx;
  border-radius: 10rpx;
  padding: 0 20rpx;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999;
}

.upload-area {
  width: 100%;
  height: 300rpx;
  background-color: #F5F8FC;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #CCCCCC;
}

.upload-icon {
  font-size: 48rpx;
  color: #0A84FF;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 28rpx;
  color: #666;
}

.uploaded-image {
  position: relative;
  width: 100%;
  height: 300rpx;
  border-radius: 10rpx;
  overflow: hidden;
}

.image-preview {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.image-actions {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  display: flex;
}

.image-action {
  background-color: rgba(10, 132, 255, 0.7);
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 6rpx;
  margin-left: 10rpx;
}

.image-action.delete {
  background-color: rgba(255, 59, 48, 0.7);
}

.modal-footer {
  display: flex;
  border-top: 2rpx solid #F2F2F7;
}

.modal-btn {
  flex: 1;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  background: none;
}

.modal-btn::after {
  border: none;
}

.cancel {
  color: #999;
  border-right: 1px solid #F2F2F7;
}

.confirm {
  color: #0A84FF;
  font-weight: 500;
}

.confirm[disabled] {
  color: #CCCCCC;
}

/* 适配暗黑模式 */
@media (prefers-color-scheme: dark) {
  .expiration-container {
    background-color: #1C1C1E;
  }
  
  .setting-card,
  .expiration-item,
  .renew-modal {
    background-color: #2C2C2E;
  }
  
  .setting-label,
  .card-title,
  .item-title,
  .form-value,
  .modal-title {
    color: #FFFFFF;
  }
  
  .info-label,
  .info-value {
    color: #A8A8A8;
  }
  
  .setting-item {
    border-color: #3A3A3C;
  }
  
  .checkbox-item {
    background-color: #3A3A3C;
    color: #A8A8A8;
  }
  
  .tab-item {
    background-color: #3A3A3C;
    color: #A8A8A8;
  }
  
  .item-img-preview,
  .upload-area,
  .form-input,
  .picker-view {
    background-color: #3A3A3C;
  }
  
  .form-input,
  .upload-text,
  .form-label {
    color: #A8A8A8;
  }
  
  .modal-header,
  .modal-footer {
    border-color: #3A3A3C;
  }
}
</style> 