# 磁州生活网后台管理系统 - 数据库设计文档

## 文档信息
- **文档版本**: v1.0.0
- **创建日期**: 2025-01-04
- **文档类型**: 数据库设计文档
- **目标读者**: 数据库架构师、后端开发工程师

## 1. 数据库设计原则

### 1.1 设计原则
- **规范化**: 遵循第三范式，减少数据冗余
- **性能优化**: 合理使用反规范化提升查询性能
- **扩展性**: 支持水平和垂直扩展
- **一致性**: 保证数据的完整性和一致性
- **安全性**: 敏感数据加密存储

### 1.2 命名规范
- **表名**: 小写字母，下划线分隔，复数形式
- **字段名**: 小写字母，下划线分隔
- **索引名**: idx_表名_字段名
- **外键名**: fk_表名_字段名

### 1.3 数据类型规范
- **主键**: BIGINT AUTO_INCREMENT
- **时间**: TIMESTAMP，默认CURRENT_TIMESTAMP
- **状态**: TINYINT，使用枚举值
- **金额**: DECIMAL(10,2)
- **文本**: VARCHAR(长度)，超过255使用TEXT

## 2. 数据库架构

### 2.1 数据库分库策略
```
cizhou_admin_user      # 用户相关数据
cizhou_admin_content   # 内容相关数据
cizhou_admin_merchant  # 商家相关数据
cizhou_admin_carpool   # 拼车相关数据
cizhou_admin_cashback  # 返利相关数据
cizhou_admin_payment   # 支付相关数据
cizhou_admin_system    # 系统配置数据
```

### 2.2 分表策略
- **用户表**: 按用户ID取模分表 (users_0, users_1, ...)
- **订单表**: 按时间分表 (orders_202501, orders_202502, ...)
- **日志表**: 按时间分表 (logs_20250101, logs_20250102, ...)

## 3. 核心数据表设计

### 3.1 用户管理模块

#### 3.1.1 用户基础信息表 (users)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码(加密)',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    avatar VARCHAR(255) COMMENT '头像URL',
    nickname VARCHAR(50) COMMENT '昵称',
    gender TINYINT DEFAULT 0 COMMENT '性别:0-未知,1-男,2-女',
    birthday DATE COMMENT '生日',
    province VARCHAR(50) COMMENT '省份',
    city VARCHAR(50) COMMENT '城市',
    district VARCHAR(50) COMMENT '区县',
    address VARCHAR(255) COMMENT '详细地址',
    status TINYINT DEFAULT 1 COMMENT '状态:0-禁用,1-正常,2-待审核',
    user_type TINYINT DEFAULT 1 COMMENT '用户类型:1-普通用户,2-商家,3-分销员,4-合伙人',
    level_id BIGINT COMMENT '会员等级ID',
    points INT DEFAULT 0 COMMENT '积分',
    balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '余额',
    total_consumption DECIMAL(10,2) DEFAULT 0.00 COMMENT '累计消费',
    last_login_time TIMESTAMP COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    register_source VARCHAR(20) COMMENT '注册来源',
    referrer_id BIGINT COMMENT '推荐人ID',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记:0-未删除,1-已删除',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_username (username),
    INDEX idx_phone (phone),
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_user_type (user_type),
    INDEX idx_referrer_id (referrer_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户基础信息表';
```

#### 3.1.2 角色表 (roles)
```sql
CREATE TABLE roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',
    name VARCHAR(50) NOT NULL COMMENT '角色名称',
    code VARCHAR(50) UNIQUE NOT NULL COMMENT '角色编码',
    description VARCHAR(255) COMMENT '角色描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_code (code),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';
```

#### 3.1.3 权限表 (permissions)
```sql
CREATE TABLE permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',
    name VARCHAR(50) NOT NULL COMMENT '权限名称',
    code VARCHAR(100) UNIQUE NOT NULL COMMENT '权限编码',
    type VARCHAR(20) NOT NULL COMMENT '权限类型:menu-菜单,button-按钮,api-接口',
    parent_id BIGINT DEFAULT 0 COMMENT '父权限ID',
    path VARCHAR(255) COMMENT '路径',
    component VARCHAR(255) COMMENT '组件',
    icon VARCHAR(50) COMMENT '图标',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_code (code),
    INDEX idx_parent_id (parent_id),
    INDEX idx_type (type),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';
```

#### 3.1.4 用户角色关联表 (user_roles)
```sql
CREATE TABLE user_roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_user_role (user_id, role_id),
    INDEX idx_user_id (user_id),
    INDEX idx_role_id (role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';
```

#### 3.1.5 角色权限关联表 (role_permissions)
```sql
CREATE TABLE role_permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    permission_id BIGINT NOT NULL COMMENT '权限ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_role_permission (role_id, permission_id),
    INDEX idx_role_id (role_id),
    INDEX idx_permission_id (permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';
```

### 3.2 内容管理模块

#### 3.2.1 内容分类表 (content_categories)
```sql
CREATE TABLE content_categories (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
    name VARCHAR(50) NOT NULL COMMENT '分类名称',
    code VARCHAR(50) UNIQUE NOT NULL COMMENT '分类编码',
    parent_id BIGINT DEFAULT 0 COMMENT '父分类ID',
    icon VARCHAR(255) COMMENT '分类图标',
    description VARCHAR(255) COMMENT '分类描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_hot TINYINT DEFAULT 0 COMMENT '是否热门:0-否,1-是',
    publish_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '发布费用',
    top_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '置顶费用',
    status TINYINT DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_code (code),
    INDEX idx_parent_id (parent_id),
    INDEX idx_status (status),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='内容分类表';
```

#### 3.2.2 内容发布表 (content_posts)
```sql
CREATE TABLE content_posts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '内容ID',
    title VARCHAR(255) NOT NULL COMMENT '标题',
    content TEXT COMMENT '内容',
    summary VARCHAR(500) COMMENT '摘要',
    images JSON COMMENT '图片列表',
    category_id BIGINT NOT NULL COMMENT '分类ID',
    user_id BIGINT NOT NULL COMMENT '发布用户ID',
    contact_name VARCHAR(50) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    contact_wechat VARCHAR(50) COMMENT '微信号',
    location_province VARCHAR(50) COMMENT '省份',
    location_city VARCHAR(50) COMMENT '城市',
    location_district VARCHAR(50) COMMENT '区县',
    location_address VARCHAR(255) COMMENT '详细地址',
    price DECIMAL(10,2) COMMENT '价格',
    is_top TINYINT DEFAULT 0 COMMENT '是否置顶:0-否,1-是',
    top_expire_time TIMESTAMP COMMENT '置顶过期时间',
    view_count INT DEFAULT 0 COMMENT '浏览次数',
    like_count INT DEFAULT 0 COMMENT '点赞次数',
    comment_count INT DEFAULT 0 COMMENT '评论次数',
    share_count INT DEFAULT 0 COMMENT '分享次数',
    status TINYINT DEFAULT 0 COMMENT '状态:0-待审核,1-已发布,2-已下架,3-已拒绝',
    audit_user_id BIGINT COMMENT '审核人ID',
    audit_time TIMESTAMP COMMENT '审核时间',
    audit_remark VARCHAR(255) COMMENT '审核备注',
    expire_time TIMESTAMP COMMENT '过期时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_category_id (category_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_is_top (is_top),
    INDEX idx_created_at (created_at),
    INDEX idx_location_city (location_city),
    FOREIGN KEY (category_id) REFERENCES content_categories(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='内容发布表';
```

### 3.3 商家管理模块

#### 3.3.1 商家信息表 (merchants)
```sql
CREATE TABLE merchants (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '商家ID',
    user_id BIGINT NOT NULL COMMENT '关联用户ID',
    merchant_name VARCHAR(100) NOT NULL COMMENT '商家名称',
    business_license VARCHAR(50) COMMENT '营业执照号',
    legal_person VARCHAR(50) COMMENT '法人姓名',
    contact_name VARCHAR(50) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    contact_email VARCHAR(100) COMMENT '联系邮箱',
    province VARCHAR(50) COMMENT '省份',
    city VARCHAR(50) COMMENT '城市',
    district VARCHAR(50) COMMENT '区县',
    address VARCHAR(255) COMMENT '详细地址',
    longitude DECIMAL(10,6) COMMENT '经度',
    latitude DECIMAL(10,6) COMMENT '纬度',
    business_hours VARCHAR(100) COMMENT '营业时间',
    description TEXT COMMENT '商家描述',
    logo VARCHAR(255) COMMENT '商家Logo',
    images JSON COMMENT '商家图片',
    category_ids JSON COMMENT '经营分类ID列表',
    level TINYINT DEFAULT 1 COMMENT '商家等级:1-普通,2-VIP,3-SVIP',
    rating DECIMAL(3,2) DEFAULT 5.00 COMMENT '评分',
    order_count INT DEFAULT 0 COMMENT '订单数量',
    sales_amount DECIMAL(12,2) DEFAULT 0.00 COMMENT '销售金额',
    status TINYINT DEFAULT 0 COMMENT '状态:0-待审核,1-正常,2-暂停,3-拒绝',
    audit_user_id BIGINT COMMENT '审核人ID',
    audit_time TIMESTAMP COMMENT '审核时间',
    audit_remark VARCHAR(255) COMMENT '审核备注',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_user_id (user_id),
    INDEX idx_merchant_name (merchant_name),
    INDEX idx_status (status),
    INDEX idx_city (city),
    INDEX idx_level (level),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家信息表';
```

#### 3.3.2 商家商品表 (merchant_products)
```sql
CREATE TABLE merchant_products (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '商品ID',
    merchant_id BIGINT NOT NULL COMMENT '商家ID',
    name VARCHAR(255) NOT NULL COMMENT '商品名称',
    description TEXT COMMENT '商品描述',
    images JSON COMMENT '商品图片',
    category_id BIGINT COMMENT '商品分类ID',
    original_price DECIMAL(10,2) COMMENT '原价',
    sale_price DECIMAL(10,2) NOT NULL COMMENT '售价',
    stock INT DEFAULT 0 COMMENT '库存',
    sales_count INT DEFAULT 0 COMMENT '销量',
    is_hot TINYINT DEFAULT 0 COMMENT '是否热门:0-否,1-是',
    is_recommend TINYINT DEFAULT 0 COMMENT '是否推荐:0-否,1-是',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态:0-下架,1-上架,2-待审核',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_category_id (category_id),
    INDEX idx_status (status),
    INDEX idx_is_hot (is_hot),
    INDEX idx_is_recommend (is_recommend),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (merchant_id) REFERENCES merchants(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家商品表';
```

### 3.4 拼车管理模块

#### 3.4.1 拼车信息表 (carpool_posts)
```sql
CREATE TABLE carpool_posts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '拼车ID',
    user_id BIGINT NOT NULL COMMENT '发布用户ID',
    type TINYINT NOT NULL COMMENT '类型:1-车找人,2-人找车',
    departure_province VARCHAR(50) COMMENT '出发省份',
    departure_city VARCHAR(50) NOT NULL COMMENT '出发城市',
    departure_district VARCHAR(50) COMMENT '出发区县',
    departure_address VARCHAR(255) COMMENT '出发详细地址',
    destination_province VARCHAR(50) COMMENT '目的地省份',
    destination_city VARCHAR(50) NOT NULL COMMENT '目的地城市',
    destination_district VARCHAR(50) COMMENT '目的地区县',
    destination_address VARCHAR(255) COMMENT '目的地详细地址',
    departure_time TIMESTAMP NOT NULL COMMENT '出发时间',
    seats INT COMMENT '座位数',
    price DECIMAL(8,2) COMMENT '价格',
    contact_name VARCHAR(50) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    contact_wechat VARCHAR(50) COMMENT '微信号',
    description TEXT COMMENT '备注说明',
    car_model VARCHAR(100) COMMENT '车型',
    car_license VARCHAR(20) COMMENT '车牌号',
    view_count INT DEFAULT 0 COMMENT '浏览次数',
    order_count INT DEFAULT 0 COMMENT '预订次数',
    status TINYINT DEFAULT 0 COMMENT '状态:0-待审核,1-已发布,2-已完成,3-已取消,4-已拒绝',
    audit_user_id BIGINT COMMENT '审核人ID',
    audit_time TIMESTAMP COMMENT '审核时间',
    audit_remark VARCHAR(255) COMMENT '审核备注',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_departure_city (departure_city),
    INDEX idx_destination_city (destination_city),
    INDEX idx_departure_time (departure_time),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='拼车信息表';
```

#### 3.4.2 拼车订单表 (carpool_orders)
```sql
CREATE TABLE carpool_orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单ID',
    order_no VARCHAR(32) UNIQUE NOT NULL COMMENT '订单号',
    carpool_id BIGINT NOT NULL COMMENT '拼车信息ID',
    passenger_id BIGINT NOT NULL COMMENT '乘客用户ID',
    driver_id BIGINT NOT NULL COMMENT '司机用户ID',
    seats INT NOT NULL COMMENT '预订座位数',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '订单金额',
    platform_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '平台服务费',
    driver_amount DECIMAL(10,2) NOT NULL COMMENT '司机收入',
    contact_name VARCHAR(50) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    pickup_address VARCHAR(255) COMMENT '上车地址',
    dropoff_address VARCHAR(255) COMMENT '下车地址',
    status TINYINT DEFAULT 1 COMMENT '状态:1-待确认,2-已确认,3-进行中,4-已完成,5-已取消,6-已退款',
    payment_status TINYINT DEFAULT 0 COMMENT '支付状态:0-未支付,1-已支付,2-已退款',
    payment_time TIMESTAMP COMMENT '支付时间',
    confirm_time TIMESTAMP COMMENT '确认时间',
    start_time TIMESTAMP COMMENT '开始时间',
    finish_time TIMESTAMP COMMENT '完成时间',
    cancel_reason VARCHAR(255) COMMENT '取消原因',
    remark TEXT COMMENT '备注',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_order_no (order_no),
    INDEX idx_carpool_id (carpool_id),
    INDEX idx_passenger_id (passenger_id),
    INDEX idx_driver_id (driver_id),
    INDEX idx_status (status),
    INDEX idx_payment_status (payment_status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (carpool_id) REFERENCES carpool_posts(id),
    FOREIGN KEY (passenger_id) REFERENCES users(id),
    FOREIGN KEY (driver_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='拼车订单表';
```

### 3.5 返利系统模块

#### 3.5.1 返利平台表 (cashback_platforms)
```sql
CREATE TABLE cashback_platforms (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '平台ID',
    name VARCHAR(100) NOT NULL COMMENT '平台名称',
    code VARCHAR(50) UNIQUE NOT NULL COMMENT '平台编码',
    logo VARCHAR(255) COMMENT '平台Logo',
    description TEXT COMMENT '平台描述',
    api_url VARCHAR(255) COMMENT 'API地址',
    app_key VARCHAR(100) COMMENT 'AppKey',
    app_secret VARCHAR(255) COMMENT 'AppSecret',
    commission_rate DECIMAL(5,4) DEFAULT 0.0000 COMMENT '佣金比例',
    settlement_cycle INT DEFAULT 30 COMMENT '结算周期(天)',
    min_withdraw_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '最小提现金额',
    status TINYINT DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    sort_order INT DEFAULT 0 COMMENT '排序',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_code (code),
    INDEX idx_status (status),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='返利平台表';
```

#### 3.5.2 返利订单表 (cashback_orders)
```sql
CREATE TABLE cashback_orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    order_no VARCHAR(64) UNIQUE NOT NULL COMMENT '订单号',
    platform_id BIGINT NOT NULL COMMENT '平台ID',
    platform_order_no VARCHAR(64) COMMENT '平台订单号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    product_name VARCHAR(255) COMMENT '商品名称',
    product_url VARCHAR(500) COMMENT '商品链接',
    order_amount DECIMAL(12,2) NOT NULL COMMENT '订单金额',
    cashback_rate DECIMAL(5,4) NOT NULL COMMENT '返利比例',
    cashback_amount DECIMAL(10,2) NOT NULL COMMENT '返利金额',
    platform_commission DECIMAL(10,2) NOT NULL COMMENT '平台佣金',
    user_cashback DECIMAL(10,2) NOT NULL COMMENT '用户返利',
    order_status TINYINT DEFAULT 1 COMMENT '订单状态:1-待确认,2-已确认,3-已完成,4-已失效',
    cashback_status TINYINT DEFAULT 0 COMMENT '返利状态:0-未返利,1-已返利,2-已失效',
    order_time TIMESTAMP COMMENT '下单时间',
    confirm_time TIMESTAMP COMMENT '确认时间',
    cashback_time TIMESTAMP COMMENT '返利时间',
    settle_time TIMESTAMP COMMENT '结算时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_order_no (order_no),
    INDEX idx_platform_order_no (platform_order_no),
    INDEX idx_platform_id (platform_id),
    INDEX idx_user_id (user_id),
    INDEX idx_order_status (order_status),
    INDEX idx_cashback_status (cashback_status),
    INDEX idx_order_time (order_time),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (platform_id) REFERENCES cashback_platforms(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='返利订单表';
```

## 4. 索引设计策略

### 4.1 主键索引
- 所有表使用BIGINT自增主键
- 保证全局唯一性和高性能

### 4.2 唯一索引
- 用户名、邮箱、手机号等唯一字段
- 订单号、编码等业务唯一字段

### 4.3 普通索引
- 外键字段建立索引
- 查询频繁的字段建立索引
- 状态字段建立索引

### 4.4 复合索引
- 多字段组合查询建立复合索引
- 遵循最左前缀原则

## 5. 数据安全设计

### 5.1 敏感数据加密
- 密码使用BCrypt加密
- 手机号、身份证号等敏感信息AES加密
- 支付相关信息特殊加密处理

### 5.2 数据备份策略
- 每日全量备份
- 每小时增量备份
- 异地备份存储

### 5.3 数据权限控制
- 数据库用户权限最小化
- 敏感操作审计日志
- 数据访问权限控制

---

**文档状态**: 数据库设计完成，待开发实施
**下一步**: API接口设计文档
