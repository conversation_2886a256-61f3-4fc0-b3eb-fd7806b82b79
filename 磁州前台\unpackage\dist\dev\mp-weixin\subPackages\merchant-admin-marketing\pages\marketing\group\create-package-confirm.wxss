/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.create-package-confirm-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  display: flex;
  flex-direction: column;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #fff;
}

/* 步骤指示器 */
.step-indicator {
  padding: 15px;
  background: #FFFFFF;
}
.step-progress {
  height: 4px;
  background-color: #EBEDF5;
  border-radius: 2px;
  margin-bottom: 5px;
  position: relative;
}
.step-progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #9040FF, #5E35B1);
  border-radius: 2px;
}
.step-text {
  font-size: 12px;
  color: #999;
  text-align: right;
}

/* 页面内容 */
.page-content {
  flex: 1;
  padding: 20px 15px;
}
.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}
.page-subtitle {
  font-size: 14px;
  color: #999;
  margin-bottom: 20px;
}

/* 信息区块样式 */
.info-section {
  background: #FFFFFF;
  border-radius: 12px;
  margin-bottom: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #EBEDF5;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.edit-btn {
  font-size: 14px;
  color: #9040FF;
}
.section-content {
  padding: 15px;
}
.info-item {
  display: flex;
  margin-bottom: 10px;
}
.info-item:last-child {
  margin-bottom: 0;
}
.info-label {
  width: 80px;
  font-size: 14px;
  color: #666;
}
.info-value {
  flex: 1;
  font-size: 14px;
  color: #333;
}
.info-value.price {
  color: #FF3B30;
  font-weight: 600;
}

/* 套餐项样式 */
.package-item {
  background: #F9F9F9;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 10px;
}
.package-item:last-child {
  margin-bottom: 0;
}
.item-header {
  margin-bottom: 10px;
}
.item-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}
.item-content {
  display: flex;
  flex-direction: column;
  gap: 5px;
}
.item-info {
  display: flex;
  font-size: 13px;
}
.total-value {
  margin-top: 15px;
  text-align: right;
  font-size: 14px;
  color: #666;
}
.total-value .value {
  font-weight: 600;
  color: #333;
}

/* 底部按钮 */
.footer-buttons {
  padding: 15px;
  background: #FFFFFF;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  gap: 15px;
}
.btn {
  flex: 1;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  border: none;
}
.btn-primary {
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #FFFFFF;
}
.btn-secondary {
  background: #F5F7FA;
  color: #666;
  border: 1px solid #EBEDF5;
}

/* 成功弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}
.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}
.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #FFFFFF;
  border-radius: 12px;
  padding: 30px;
  width: 80%;
  max-width: 300px;
}
.success-modal {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.success-icon {
  width: 60px;
  height: 60px;
  background: #34C759;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
}
.checkmark {
  width: 30px;
  height: 15px;
  border-left: 3px solid #fff;
  border-bottom: 3px solid #fff;
  transform: rotate(-45deg);
}
.success-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}
.success-message {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
  text-align: center;
}
.success-btn {
  width: 100%;
  height: 45px;
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #FFFFFF;
  border-radius: 22.5px;
  font-size: 16px;
  font-weight: 600;
  border: none;
}