{"version": 3, "file": "profile.js", "sources": ["subPackages/user/pages/profile.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcdXNlclxwYWdlc1xwcm9maWxlLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"profile-page\">\n    <!-- 其他内容 -->\n    <view class=\"menu-list\">\n      <!-- 其他菜单项 -->\n      <view class=\"menu-item\" @click=\"navigateToMyRedPackets\">\n        <view class=\"menu-icon\">\n          <image src=\"/static/images/red-packet-icon.png\" mode=\"aspectFit\"></image>\n        </view>\n        <view class=\"menu-text\">我的红包</view>\n        <view class=\"menu-arrow\">›</view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  methods: {\n    navigateToMyRedPackets() {\n      uni.navigateTo({\n        url: '/subPackages/user/pages/my-red-packets'\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.menu-item {\n  display: flex;\n  align-items: center;\n  padding: 30rpx;\n  background-color: #fff;\n  border-bottom: 1px solid #f5f5f5;\n  \n  .menu-icon {\n    width: 40rpx;\n    height: 40rpx;\n    margin-right: 20rpx;\n    \n    image {\n      width: 100%;\n      height: 100%;\n    }\n  }\n  \n  .menu-text {\n    flex: 1;\n    font-size: 28rpx;\n    color: #333;\n  }\n  \n  .menu-arrow {\n    font-size: 32rpx;\n    color: #999;\n  }\n}\n</style> \n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/user/pages/profile.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAiBA,MAAK,YAAU;AAAA,EACb,SAAS;AAAA,IACP,yBAAyB;AACvBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;ACxBA,GAAG,WAAW,eAAe;"}