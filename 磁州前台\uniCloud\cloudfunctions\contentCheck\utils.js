'use strict';

/**
 * 工具类模块
 * 用于辅助内容审核功能实现
 */

const { uniCloud } = require('./uni-cloud-module');

/**
 * 获取OSS文件的公开访问URL
 * @param {String} fileUrl 文件URL，格式为cloud://、oss://等
 * @returns {Promise<String>} 公开访问URL
 */
exports.getOssFile = async (fileUrl) => {
  if (!fileUrl) {
    throw new Error('文件URL不能为空');
  }
  
  try {
    // 针对uni-app云存储路径格式
    if (fileUrl.startsWith('cloud://')) {
      // 使用uniCloud提供的方法获取临时访问链接
      const result = await uniCloud.getTempFileURL({
        fileList: [fileUrl]
      });
      
      if (result.fileList && result.fileList.length > 0) {
        return result.fileList[0].tempFileURL;
      }
      
      throw new Error('获取临时文件URL失败');
    }
    
    // 针对阿里云OSS路径格式
    if (fileUrl.startsWith('oss://')) {
      // 将oss://格式转换为https://格式
      const ossPath = fileUrl.replace('oss://', '');
      const parts = ossPath.split('/');
      const bucket = parts[0];
      const objectKey = parts.slice(1).join('/');
      
      // 使用OSS公开访问路径
      // 此处需根据实际OSS配置和区域调整
      return `https://${bucket}.oss-cn-hangzhou.aliyuncs.com/${objectKey}`;
    }
    
    // 其他格式的URL，可以根据需要扩展
    throw new Error('不支持的文件URL格式');
  } catch (error) {
    console.error('获取文件URL异常:', error);
    throw error;
  }
};

/**
 * 随机生成唯一ID
 * @returns {String} 唯一ID
 */
exports.generateUniqueId = () => {
  return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * 获取文件扩展名
 * @param {String} filePath 文件路径
 * @returns {String} 文件扩展名
 */
exports.getFileExtension = (filePath) => {
  if (!filePath) return '';
  
  const match = filePath.match(/\.([^.]+)$/);
  return match ? match[1].toLowerCase() : '';
};

/**
 * 字符串脱敏处理
 * @param {String} str 原始字符串
 * @param {Number} prefixLen 保留前缀长度
 * @param {Number} suffixLen 保留后缀长度
 * @returns {String} 脱敏后的字符串
 */
exports.maskString = (str, prefixLen = 3, suffixLen = 4) => {
  if (!str) return '';
  
  const len = str.length;
  if (len <= prefixLen + suffixLen) {
    return '*'.repeat(len);
  }
  
  const prefix = str.substr(0, prefixLen);
  const suffix = str.substr(len - suffixLen);
  const maskLen = len - prefixLen - suffixLen;
  
  return `${prefix}${'*'.repeat(maskLen)}${suffix}`;
}; 