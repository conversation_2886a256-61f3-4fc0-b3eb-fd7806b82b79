"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const static_data_chinaAreaData = require("../../../static/data/china-area-data.js");
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const provinces = common_vendor.ref(Object.values(static_data_chinaAreaData.province_list));
    const cities = common_vendor.ref([]);
    const districts = common_vendor.ref([]);
    const provinceCodes = common_vendor.ref(Object.keys(static_data_chinaAreaData.province_list));
    const cityCodes = common_vendor.ref([]);
    const districtCodes = common_vendor.ref([]);
    const currentProvince = common_vendor.ref("");
    const currentCity = common_vendor.ref("");
    const currentDistrict = common_vendor.ref("");
    const selectedRegion = common_vendor.ref("");
    const regionStatus = common_vendor.ref("");
    const regionData = common_vendor.reactive({
      merchants: 0,
      users: 0,
      orders: 0,
      income: 0
    });
    const canApply = common_vendor.computed(() => {
      return selectedRegion.value && regionStatus.value === "可申请";
    });
    const goBack = () => {
      common_vendor.index.navigateBack({
        fail: () => {
          common_vendor.index.switchTab({ url: "/pages/my/my" });
        }
      });
    };
    const provinceChange = (e) => {
      const index = e.detail.value;
      const provinceCode = provinceCodes.value[index];
      currentProvince.value = static_data_chinaAreaData.province_list[provinceCode];
      cityCodes.value = Object.keys(static_data_chinaAreaData.city_list).filter((code) => static_data_chinaAreaData.city_province_map[code] == provinceCode);
      cities.value = cityCodes.value.map((code) => static_data_chinaAreaData.city_list[code]);
      currentCity.value = "";
      currentDistrict.value = "";
      selectedRegion.value = "";
      districts.value = [];
    };
    const cityChange = (e) => {
      const index = e.detail.value;
      const cityCode = cityCodes.value[index];
      currentCity.value = static_data_chinaAreaData.city_list[cityCode];
      districtCodes.value = Object.keys(static_data_chinaAreaData.county_list).filter((code) => static_data_chinaAreaData.county_city_map[code] == cityCode);
      districts.value = districtCodes.value.map((code) => static_data_chinaAreaData.county_list[code]);
      currentDistrict.value = "";
      selectedRegion.value = "";
    };
    const districtChange = (e) => {
      const index = e.detail.value;
      const districtCode = districtCodes.value[index];
      currentDistrict.value = static_data_chinaAreaData.county_list[districtCode];
      selectedRegion.value = `${currentProvince.value} ${currentCity.value} ${currentDistrict.value}`;
      generateRegionData();
    };
    const generateRegionData = () => {
      const randomIndex = Math.floor(Math.random() * 10);
      if (randomIndex < 7) {
        regionStatus.value = "可申请";
      } else if (randomIndex < 9) {
        regionStatus.value = "审核中";
      } else {
        regionStatus.value = "已占用";
      }
      regionData.merchants = Math.floor(Math.random() * 2e3) + 500;
      regionData.users = Math.floor(Math.random() * 5e4) + 1e4;
      regionData.orders = (Math.floor(Math.random() * 100) + 20) / 10;
      regionData.income = (Math.floor(Math.random() * 500) + 50) / 10;
    };
    const submitApplication = () => {
      if (!selectedRegion.value || !canApply.value) {
        common_vendor.index.showToast({ title: "请先选择可申请的区域", icon: "none" });
        return;
      }
      common_vendor.index.navigateTo({ url: "/subPackages/franchise/pages/application-form?region=" + encodeURIComponent(selectedRegion.value) });
    };
    const contactConsultant = () => {
      common_vendor.index.showModal({
        title: "联系加盟顾问",
        content: "您可以通过以下方式联系我们的加盟顾问：\n\n电话：400-888-8888\n微信：tcqy_jmzx\n工作时间：9:00-18:00（周一至周五）",
        showCancel: false,
        confirmText: "我知道了"
      });
    };
    const showStepLine = (step) => {
      return step < 4;
    };
    common_vendor.onMounted(() => {
      provinces.value = Object.values(static_data_chinaAreaData.province_list);
      provinceCodes.value = Object.keys(static_data_chinaAreaData.province_list);
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: common_assets._imports_1$14,
        d: common_assets._imports_2$8,
        e: common_assets._imports_3$7,
        f: common_vendor.t(currentProvince.value || "请选择"),
        g: provinces.value,
        h: common_vendor.o(provinceChange),
        i: common_vendor.t(currentCity.value || "请选择"),
        j: !currentProvince.value ? 1 : "",
        k: cities.value,
        l: common_vendor.o(cityChange),
        m: !currentProvince.value,
        n: common_vendor.t(currentDistrict.value || "请选择"),
        o: !currentCity.value ? 1 : "",
        p: districts.value,
        q: common_vendor.o(districtChange),
        r: !currentCity.value,
        s: !selectedRegion.value
      }, !selectedRegion.value ? {} : {
        t: common_vendor.t(selectedRegion.value)
      }, {
        v: selectedRegion.value
      }, selectedRegion.value ? {
        w: common_vendor.t(selectedRegion.value),
        x: common_vendor.t(regionStatus.value),
        y: regionStatus.value === "可申请" ? 1 : "",
        z: regionStatus.value === "审核中" ? 1 : "",
        A: regionStatus.value === "已占用" ? 1 : "",
        B: common_vendor.t(regionData.merchants),
        C: common_vendor.t(regionData.users),
        D: common_vendor.t(regionData.orders),
        E: common_vendor.t(regionData.income)
      } : {}, {
        F: showStepLine(1)
      }, showStepLine(1) ? {} : {}, {
        G: showStepLine(2)
      }, showStepLine(2) ? {} : {}, {
        H: showStepLine(3)
      }, showStepLine(3) ? {} : {}, {
        I: common_vendor.o(contactConsultant),
        J: common_vendor.o(submitApplication),
        K: !canApply.value
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-db5f4e62"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/franchise/pages/index.js.map
