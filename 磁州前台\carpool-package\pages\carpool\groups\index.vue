<template>
  <view class="container">
    <!-- 导航栏 -->
    <view class="custom-header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="left-action" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="action-icon back-icon"></image>
        </view>
        <view class="title-area">
          <text class="page-title">拼车群</text>
        </view>
        <view class="right-action">
          <image src="/static/images/tabbar/search.png" class="action-icon" @click="showSearch"></image>
        </view>
      </view>
    </view>

    <!-- 搜索框 -->
    <view class="search-section" v-if="isSearchVisible">
      <view class="search-box">
        <image src="/static/images/tabbar/search.png" mode="aspectFit" class="search-icon"></image>
        <input type="text" class="search-input" placeholder="搜索拼车群" v-model="searchKeyword" confirm-type="search" @confirm="searchGroups" />
        <view class="search-cancel" @click="hideSearch">取消</view>
      </view>
    </view>

    <!-- 分类选项卡 -->
    <view class="tabs-section">
      <scroll-view scroll-x class="tabs-scroll" show-scrollbar="false">
        <view 
          class="tab-item" 
          v-for="(tab, index) in tabs" 
          :key="index"
          :class="{ active: currentTab === index }"
          @click="switchTab(index)"
        >
          <text>{{ tab }}</text>
          <view class="tab-indicator" v-if="currentTab === index"></view>
        </view>
      </scroll-view>
    </view>

    <!-- 群组列表 -->
    <scroll-view 
      scroll-y 
      class="groups-list"
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="loadMore"
    >
      <view class="group-card" v-for="(group, index) in filteredGroups" :key="index" @click="viewGroupDetail(group)">
        <view class="group-header">
          <image :src="group.avatar" mode="aspectFill" class="group-avatar"></image>
          <view class="group-info">
            <view class="group-name">{{ group.name }}</view>
            <view class="group-meta">
              <text class="group-members">{{ group.memberCount }}人</text>
              <text class="group-type" :class="group.type">{{ group.typeText }}</text>
            </view>
          </view>
          <view class="group-join" @click.stop="viewGroupDetail(group)">
            <text>查看</text>
          </view>
        </view>
        <view class="group-content">
          <view class="group-description">
            <text v-if="!group._descExpand && group.description.length > 36">{{ group.description.slice(0, 36) + '...' }}</text>
            <text v-else>{{ group.description }}</text>
            <text v-if="group.description.length > 36" class="desc-toggle" @click.stop="toggleDesc(group)">{{ group._descExpand ? '收起' : '展开' }}</text>
          </view>
          <scroll-view class="group-tags-scroll" scroll-x enhanced show-scrollbar="false">
            <view class="group-tags">
              <view class="tag" v-for="(tag, tagIndex) in group.tags" :key="tagIndex">{{ tag }}</view>
            </view>
          </scroll-view>
        </view>
        <view class="group-footer">
          <view class="route-info" v-if="group.routeInfo">
            <view class="route-icon">
              <image src="/static/images/tabbar/route.png" mode="aspectFit"></image>
            </view>
            <view class="route-text">{{ group.routeInfo }}</view>
          </view>
          <view class="group-activity">
            <text>{{ group.lastActiveTime }}</text>
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view class="loading-section" v-if="isLoading">
        <view class="loading-spinner"></view>
        <text>加载中...</text>
      </view>

      <!-- 无数据状态 -->
      <view class="empty-section" v-if="filteredGroups.length === 0 && !isLoading">
        <image src="/static/images/tabbar/empty-groups.png" mode="aspectFit"></image>
        <text>暂无相关拼车群</text>
        <button class="create-btn" @click="createGroup" v-if="isAdmin">创建拼车群</button>
        <text v-else class="empty-tip">拼车群由管理员创建</text>
      </view>
    </scroll-view>

    <!-- 悬浮创建按钮 - 仅管理员可见 -->
    <view class="floating-btn" @click="createGroup" v-if="isAdmin">
      <image src="/static/images/tabbar/plus.png" mode="aspectFit"></image>
    </view>
    
    <!-- 底部导航栏 -->
    <view class="tabbar">
      <view class="tabbar-item" :class="{ active: activeTab === 'home' }" @click="navigateToPage('home')">
        <image :src="activeTab === 'home' ? '/static/images/tabbar/p首页选中.png' : '/static/images/tabbar/p首页.png'" mode="aspectFit" class="tabbar-icon"></image>
        <text class="tabbar-text" :class="{ 'active-text': activeTab === 'home' }">同城</text>
      </view>
      <view class="tabbar-item" :class="{ active: activeTab === 'carpool' }" @click="navigateToPage('carpool')">
        <image :src="activeTab === 'carpool' ? '/static/images/tabbar/p拼车选中.png' : '/static/images/tabbar/p拼车.png'" mode="aspectFit" class="tabbar-icon"></image>
        <text class="tabbar-text" :class="{ 'active-text': activeTab === 'carpool' }">拼车</text>
      </view>
      <view class="tabbar-item" :class="{ active: activeTab === 'publish' }" @click="publishCarpool">
        <image :src="activeTab === 'publish' ? '/static/images/tabbar/p发布选中.png' : '/static/images/tabbar/p发布.png'" mode="aspectFit" class="tabbar-icon"></image>
        <text class="tabbar-text" :class="{ 'active-text': activeTab === 'publish' }">发布</text>
      </view>
      <view class="tabbar-item active">
        <image src="/static/images/tabbar/p拼车群选中.png" mode="aspectFit" class="tabbar-icon"></image>
        <text class="tabbar-text active-text">拼车群</text>
      </view>
      <view class="tabbar-item" :class="{ active: activeTab === 'my' }" @click="navigateToPage('my')">
        <image :src="activeTab === 'my' ? '/static/images/tabbar/p我的选中.png' : '/static/images/tabbar/p我的.png'" mode="aspectFit" class="tabbar-icon"></image>
        <text class="tabbar-text" :class="{ 'active-text': activeTab === 'my' }">我的</text>
      </view>
    </view>
    
    <!-- 群二维码弹窗 -->
    <view class="qrcode-popup" v-if="showQRCode" @click.stop="hideQRCode">
      <view class="qrcode-container" @click.stop>
        <view class="qrcode-header">
          <text class="qrcode-title">{{ currentGroup ? currentGroup.name : '拼车群' }}</text>
          <view class="qrcode-close" @click="hideQRCode">×</view>
        </view>
        <view class="group-info-section" v-if="currentGroup">
          <view class="group-route">
            <view class="route-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#0A84FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M3 12h18M3 6h18M3 18h18"></path>
              </svg>
            </view>
            <text class="route-text">{{ currentGroup.routeInfo }}</text>
          </view>
          <view class="group-tags-display">
            <view class="tag-display" v-for="(tag, tagIndex) in currentGroup.tags" :key="tagIndex">{{ tag }}</view>
          </view>
          <view class="group-desc">
            <text>{{ currentGroup.description }}</text>
          </view>
          <view class="group-members">
            <text class="members-count">{{ currentGroup.memberCount }}人</text>
            <text class="activity-time">{{ currentGroup.lastActiveTime }}活跃</text>
          </view>
        </view>
        <view class="qrcode-content">
          <image :src="currentGroup ? currentGroup.qrcode || '/static/images/tabbar/default-qrcode.png' : '/static/images/tabbar/default-qrcode.png'" mode="aspectFit" class="qrcode-image"></image>
          <text class="qrcode-tips">扫描微信二维码加入拼车群</text>
        </view>
        <view class="qrcode-footer">
          <button class="qrcode-save-btn" @click="saveQRCode">保存二维码</button>
          <button class="qrcode-share-btn" @click="shareQRCode">分享给好友</button>
        </view>
      </view>
    </view>
    
    <!-- 移除聊天对话框，只保留二维码弹窗 -->
    
    <!-- 底部安全区域 -->
    <view class="safe-area-bottom"></view>
    
    <!-- 发布类型选择弹窗 -->
    <view class="publish-popup" v-if="showPublishPopup" @click="closePublishPopup">
      <view class="publish-card" @click.stop>
        <view class="publish-header">
          <text class="publish-title">选择发布类型</text>
          <view class="close-btn" @click="closePublishPopup">
            <text class="close-icon">×</text>
          </view>
        </view>
        <view class="publish-options">
          <view class="publish-option" @click="navigateToPublish('people-to-car')">
            <view class="option-icon-wrapper people-car">
              <image src="/static/images/tabbar/人找车.png" mode="aspectFit" class="option-icon"></image>
            </view>
            <text class="option-text">人找车</text>
          </view>
          <view class="publish-option" @click="navigateToPublish('car-to-people')">
            <view class="option-icon-wrapper car-people">
              <image src="/static/images/tabbar/车找人.png" mode="aspectFit" class="option-icon"></image>
            </view>
            <text class="option-text">车找人</text>
          </view>
          <view class="publish-option" @click="navigateToPublish('goods-to-car')">
            <view class="option-icon-wrapper goods-car">
              <image src="/static/images/tabbar/货找车.png" mode="aspectFit" class="option-icon"></image>
            </view>
            <text class="option-text">货找车</text>
          </view>
          <view class="publish-option" @click="navigateToPublish('car-to-goods')">
            <view class="option-icon-wrapper car-goods">
              <image src="/static/images/tabbar/车找货.png" mode="aspectFit" class="option-icon"></image>
            </view>
            <text class="option-text">车找货</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { onShow, onHide, onUnload } from '@dcloudio/uni-app';

// 搜索相关
const isSearchVisible = ref(false);
const searchKeyword = ref('');

// 标签和分页
const currentTab = ref(0);
const tabs = ref(['全部', '本地', '长途', '通勤', '我的群']);
const isRefreshing = ref(false);
const isLoading = ref(false);
const hasMore = ref(true);
const page = ref(1);

// 当前激活的tab
const activeTab = ref('groups');

// 弹窗控制
const showQRCode = ref(false); // 是否显示二维码弹窗
const currentGroup = ref(null); // 当前选中的群组
const showPublishPopup = ref(false); // 添加发布弹窗控制变量

// 状态栏高度
const statusBarHeight = ref(20);

// 用户是否为管理员
const isAdmin = ref(false);

// 移除聊天消息数据

// 群组数据
const groups = ref([
  {
    id: 1,
    name: '磁州-邯郸拼车群',
    avatar: '/static/images/tabbar/group-avatar1.png',
    memberCount: 128,
    type: 'local',
    typeText: '本地',
    description: '磁州到邯郸日常拼车，安全出行，拼车省钱',
    tags: ['安全', '准时', '舒适'],
    routeInfo: '磁州 → 邯郸',
    lastActiveTime: '10分钟前',
    isJoined: true,
    qrcode: '/static/images/tabbar/qrcode1.png'
  },
  {
    id: 2,
    name: '磁州-石家庄通勤群',
    avatar: '/static/images/tabbar/group-avatar2.png',
    memberCount: 86,
    type: 'commute',
    typeText: '通勤',
    description: '工作日通勤拼车，每天准时发车，欢迎加入',
    tags: ['通勤', '准时', '固定'],
    routeInfo: '磁州 → 石家庄',
    lastActiveTime: '30分钟前',
    isJoined: false,
    qrcode: '/static/images/tabbar/qrcode2.png'
  },
  {
    id: 3,
    name: '磁州-北京长途拼车',
    avatar: '/static/images/tabbar/group-avatar3.png',
    memberCount: 64,
    type: 'longDistance',
    typeText: '长途',
    description: '周末和节假日磁州到北京长途拼车，舒适安全',
    tags: ['长途', '舒适', '周末'],
    routeInfo: '磁州 → 北京',
    lastActiveTime: '2小时前',
    isJoined: false,
    qrcode: '/static/images/tabbar/qrcode3.png'
  },
  {
    id: 4,
    name: '磁州本地拼车群',
    avatar: '/static/images/tabbar/group-avatar4.png',
    memberCount: 256,
    type: 'local',
    typeText: '本地',
    description: '磁州城区及周边拼车，随时发车，方便快捷',
    tags: ['本地', '灵活', '快捷'],
    routeInfo: '磁州城区',
    lastActiveTime: '1小时前',
    isJoined: true,
    qrcode: '/static/images/tabbar/qrcode4.png'
  }
]);

// 计算属性 - 筛选后的群组
const filteredGroups = computed(() => {
  let result = groups.value;
  
  // 根据标签筛选
  if (currentTab.value > 0) {
    const tabType = ['', 'local', 'longDistance', 'commute', ''][currentTab.value];
    if (currentTab.value === 4) {
      // 我的群
      result = result.filter(group => group.isJoined);
    } else if (tabType) {
      result = result.filter(group => group.type === tabType);
    }
  }
  
  // 根据搜索关键词筛选
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    result = result.filter(group => 
      group.name.toLowerCase().includes(keyword) || 
      group.description.toLowerCase().includes(keyword) ||
      group.routeInfo?.toLowerCase().includes(keyword) ||
      group.tags.some(tag => tag.toLowerCase().includes(keyword))
    );
  }
  
  return result;
});

// 生命周期钩子
onMounted(() => {
  loadGroups();
  // 隐藏原生tabBar
  uni.hideTabBar();
  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight;
  
  // 检查用户是否为管理员
  const userInfo = uni.getStorageSync('userInfo') || {};
  isAdmin.value = !!userInfo.isAdmin;
});

onShow(() => {
  // 确保每次页面显示时都隐藏原生tabBar
  uni.hideTabBar();
});

onHide(() => {
  // 页面隐藏时，不需要显示原生tabBar，因为返回到拼车主页也是隐藏状态
});

onUnload(() => {
  // 如果不是返回到拼车主页，需要显示默认tabBar
  const pages = getCurrentPages();
  const prevPage = pages[pages.length - 2];
  if (!prevPage || prevPage.route !== 'carpool-package/pages/carpool-main/index') {
    uni.showTabBar();
  }
});

// 方法
const goBack = () => {
  uni.navigateBack({
    fail: () => {
      // 如果返回失败，则跳转到拼车主页
      uni.navigateTo({
        url: '/carpool-package/pages/carpool-main/index'
      });
    }
  });
};

const showSearch = () => {
  isSearchVisible.value = true;
};

const hideSearch = () => {
  isSearchVisible.value = false;
  searchKeyword.value = '';
};

const switchTab = (index) => {
  currentTab.value = index;
};

const searchGroups = () => {
  console.log('搜索关键词：', searchKeyword.value);
  // 实际应用中这里应该调用API进行搜索
};

const loadGroups = () => {
  isLoading.value = true;
  
  // 模拟API请求
  setTimeout(() => {
    // 在实际应用中，这里应该是API请求获取数据
    isLoading.value = false;
    hasMore.value = page.value < 3; // 模拟只有3页数据
    page.value++;
  }, 1000);
};

const onRefresh = () => {
  isRefreshing.value = true;
  page.value = 1;
  
  // 模拟刷新
  setTimeout(() => {
    // 在实际应用中，这里应该是API请求刷新数据
    isRefreshing.value = false;
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    });
  }, 1000);
};

const loadMore = () => {
  if (hasMore.value && !isLoading.value) {
    loadGroups();
  }
};

const viewGroupDetail = (group) => {
  // 直接显示二维码弹窗
  currentGroup.value = group;
  showQRCode.value = true;
};

// 移除聊天相关函数，只保留二维码展示功能

// 已合并到viewGroupDetail函数中

const hideQRCode = () => {
  showQRCode.value = false;
  currentGroup.value = null;
};

const saveQRCode = () => {
  // 保存二维码到相册
  if (currentGroup.value && currentGroup.value.qrcode) {
    uni.saveImageToPhotosAlbum({
      filePath: currentGroup.value.qrcode,
      success: () => {
        uni.showToast({
          title: '二维码已保存到相册',
          icon: 'success'
        });
      },
      fail: () => {
        uni.showToast({
          title: '保存失败，请检查权限',
          icon: 'none'
        });
      }
    });
  }
};

const shareQRCode = () => {
  // 分享二维码
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 2,
    imageUrl: currentGroup.value ? currentGroup.value.qrcode : '',
    title: `邀请您加入${currentGroup.value ? currentGroup.value.name : '拼车群'}`,
    success: function() {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      });
    },
    fail: function() {
      uni.showToast({
        title: '分享失败',
        icon: 'none'
      });
    }
  });
};

const createGroup = () => {
  // 检查用户是否为管理员
  const userInfo = uni.getStorageSync('userInfo') || {};
  if (userInfo.isAdmin) {
    uni.navigateTo({
      url: '/pages/carpool/groups/create'
    });
  } else {
    uni.showModal({
      title: '提示',
      content: '只有管理员可以创建拼车群',
      showCancel: false
    });
  }
};

const navigateToPage = (page) => {
  // 避免重复点击当前页面
  if (page === 'groups') return;
  
  activeTab.value = page; // 更新激活的标签
  
  switch(page) {
    case 'home':
      // 确保在切换到首页之前恢复系统tabBar
      uni.showTabBar();
      setTimeout(() => {
      uni.switchTab({
        url: '/pages/index/index'
      });
      }, 50);
      break;
    case 'carpool':
      // 隐藏tabBar确保不会在跳转过程中闪烁
      uni.hideTabBar();
      uni.navigateTo({
        url: '/carpool-package/pages/carpool-main/index'
      });
      break;
    case 'my':
      // 隐藏tabBar确保不会在跳转过程中闪烁
      uni.hideTabBar();
      uni.navigateTo({
        url: '/carpool-package/pages/carpool/my/index'
      });
      break;
  }
};

const publishCarpool = () => {
  // 显示发布选择弹窗，而不是直接跳转
  showPublishPopup.value = true;
  activeTab.value = 'publish'; // 设置发布按钮为激活状态
};

// 添加选择发布类型的方法
const navigateToPublish = (type) => {
  showPublishPopup.value = false;
  
  // 根据类型直接导航到相应的表单页面
  let url = '';
  switch(type) {
    case 'people-to-car':
      url = '/carpool-package/pages/carpool/publish/people-to-car';
      break;
    case 'car-to-people':
      url = '/carpool-package/pages/carpool/publish/car-to-people';
      break;
    case 'goods-to-car':
      url = '/carpool-package/pages/carpool/publish/goods-to-car';
      break;
    case 'car-to-goods':
      url = '/carpool-package/pages/carpool/publish/car-to-goods';
      break;
    default:
      url = '/carpool-package/pages/carpool/publish/people-to-car';
  }
  
  uni.navigateTo({
    url
  });
};

// 关闭发布选择弹窗
const closePublishPopup = () => {
  showPublishPopup.value = false;
  activeTab.value = 'groups'; // 重置为拼车群标签
};

const toggleDesc = (group) => {
  // Vue 3 中不再需要 $set，直接设置响应式属性
  group._descExpand = !group._descExpand;
};

const cardTapAnim = (e) => {
  e.currentTarget.classList.add('card-tap-anim');
  setTimeout(() => e.currentTarget.classList.remove('card-tap-anim'), 180);
};

const joinGroupAnim = (e, group) => {
  e.currentTarget.classList.add('btn-tap-anim');
  setTimeout(() => e.currentTarget.classList.remove('btn-tap-anim'), 180);
  joinGroup(group);
};
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background-color: #F5F8FC;
  padding-bottom: 140rpx;
  padding-top: calc(var(--status-bar-height) + 90rpx + 10rpx); /* 标题栏高度 + 10rpx间距 */
  position: relative;
  overflow-x: hidden;
}

/* 自定义标题栏模块 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  background-color: #1677FF; /* 恢复为实色背景 */
  z-index: 103;
  box-shadow: none;
}

/* 标题栏内容 */
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 90rpx;
  padding: 0 30rpx;
  position: relative;
  z-index: 102;
}

.left-action {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-area {
  flex: 1;
  text-align: center;
}

.page-title {
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.right-action {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  width: 44rpx;
  height: 44rpx;
  filter: brightness(0) invert(1);
}

/* 搜索框 */
.search-section {
  padding: 20rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 40rpx;
  padding: 0 24rpx;
  height: 80rpx;
}

.search-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
}

.search-cancel {
  font-size: 28rpx;
  color: #4f8cff;
  margin-left: 20rpx;
  font-weight: 500;
}

/* 分类选项卡 */
.tabs-section {
  position: sticky;
  top: calc(var(--status-bar-height) + 90rpx);
  left: 0;
  right: 0;
  background-color: #FFFFFF; /* 改为白色背景，卡片样式 */
  z-index: 90;
  height: 86rpx;
  display: flex;
  align-items: center;
  border-bottom: none;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05); /* 添加轻微阴影，增强卡片效果 */
  width: 100%;
  border-radius: 16rpx; /* 添加圆角 */
  margin: 10rpx 30rpx; /* 上下间距改为10rpx */
  width: calc(100% - 60rpx); /* 减去左右外边距 */
}

.tabs-scroll {
  white-space: nowrap;
  padding: 0;
  height: 86rpx; /* 与tabs-section高度一致 */
  border-bottom: none;
  box-shadow: none;
  width: 100%;
}

.tab-item {
  display: inline-block;
  padding: 28rpx 30rpx;
  font-size: 30rpx;
  color: #666666; /* 改为深灰色文字 */
  position: relative;
  transition: all 0.3s ease;
  height: 86rpx;
  box-sizing: border-box;
  border-bottom: none;
  text-align: center;
  min-width: 20%;
}

.tab-item.active {
  color: #1677FF; /* 激活状态为蓝色 */
  font-weight: 600;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background: #1677FF; /* 蓝色指示器 */
  border-radius: 6rpx;
}

/* 群组列表 */
.groups-list {
  height: calc(100vh - 210rpx - var(--status-bar-height) - 90rpx);
  padding: 0 30rpx;
  padding-top: 106rpx; /* 设置顶部内边距为分类标签的高度加上一些额外空间 */
  box-sizing: border-box;
  margin-top: 0;
}

.group-card {
  background-color: #ffffff;
  border-radius: 32rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.06);
  width: 100%;
  box-sizing: border-box;
  transform: translateZ(0);
  transition: transform 0.2s, box-shadow 0.2s;
  overflow: hidden;
}

.group-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.04);
}

.group-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.group-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.08);
  border: 2rpx solid #f0f4f8;
}

.group-info {
  flex: 1;
  min-width: 0;
}

.group-name {
  font-size: 36rpx;
  font-weight: 700;
  color: #222;
  margin-bottom: 12rpx;
  line-height: 1.3;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.group-meta {
  display: flex;
  align-items: center;
}

.group-members {
  font-size: 24rpx;
  color: #999;
  margin-right: 16rpx;
}

.group-type {
  font-size: 22rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  background-color: rgba(0,0,0,0.05);
  color: #666;
}

.group-type.local {
  background-color: rgba(25,118,210,0.1);
  color: #1976d2;
}

.group-type.longDistance {
  background-color: rgba(156,39,176,0.1);
  color: #9c27b0;
}

.group-type.commute {
  background-color: rgba(76,175,80,0.1);
  color: #4caf50;
}

.group-join {
  background: linear-gradient(135deg, #4f8cff, #6fc3ff);
  color: #ffffff;
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 16rpx rgba(79,140,255,0.2);
  margin-left: 16rpx;
  transition: transform 0.15s, box-shadow 0.15s;
}

.group-join:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(79,140,255,0.15);
}

/* 移除已加入样式，统一为查看按钮 */

.group-content {
  margin-bottom: 24rpx;
}

.group-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.desc-toggle {
  color: #4f8cff;
  font-size: 24rpx;
  margin-left: 12rpx;
  font-weight: 500;
}

.group-tags-scroll {
  width: 100%;
  height: 60rpx;
}

.group-tags {
  display: inline-flex;
  flex-wrap: nowrap;
  padding: 4rpx 0;
}

.tag {
  font-size: 24rpx;
  color: #4f8cff;
  background: linear-gradient(135deg, #eaf3ff, #d6eaff);
  padding: 8rpx 24rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
  white-space: nowrap;
  font-weight: 500;
}

.group-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f4f8;
}

.route-info {
  display: flex;
  align-items: center;
}

.route-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.route-icon image {
  width: 100%;
  height: 100%;
  opacity: 0.6;
}

.route-text {
  font-size: 26rpx;
  color: #999;
}

.group-activity {
  font-size: 24rpx;
  color: #bbb;
}

/* 加载状态 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f4f8;
  border-top: 4rpx solid #4f8cff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-section image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 40rpx;
  opacity: 0.8;
}

.empty-section text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.create-btn {
  background: linear-gradient(135deg, #4f8cff, #6fc3ff);
  color: #ffffff;
  padding: 20rpx 60rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: 600;
  box-shadow: 0 6rpx 16rpx rgba(79,140,255,0.25);
  letter-spacing: 2rpx;
}

/* 悬浮按钮 */
.floating-btn {
  position: fixed;
  right: 40rpx;
  bottom: 160rpx;
  width: 110rpx;
  height: 110rpx;
  background: linear-gradient(135deg, #4f8cff, #6fc3ff);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(79,140,255,0.3);
  z-index: 99;
  transition: transform 0.15s, box-shadow 0.15s;
}

.floating-btn:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 12rpx rgba(79,140,255,0.2);
}

.floating-btn image {
  width: 50rpx;
  height: 50rpx;
  filter: brightness(0) invert(1);
}

/* 底部导航栏 */
.tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 110rpx;
  background-color: rgba(255, 255, 255, 0.95);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  z-index: 9999;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10rpx 0;
  position: relative;
  transition: all 0.2s ease;
}

.tabbar-item:active {
  opacity: 0.7;
}

.tabbar-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 6rpx;
}

.tabbar-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1;
}

.active-text {
  color: #4f8cff;
  font-weight: 600;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  width: 100%;
}

/* 群二维码弹窗 */
.qrcode-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.qrcode-container {
  width: 650rpx;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  animation: popup 0.3s ease;
}

@keyframes popup {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.qrcode-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f4f8;
}

.qrcode-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
}

.qrcode-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: #999;
}

.group-info-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f4f8;
}

.group-route {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.route-icon {
  margin-right: 10rpx;
  display: flex;
  align-items: center;
}

.route-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
}

.group-tags-display {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.tag-display {
  padding: 6rpx 16rpx;
  background-color: #E6F2FF;
  color: #0A84FF;
  border-radius: 20rpx;
  font-size: 24rpx;
  margin-right: 12rpx;
  margin-bottom: 10rpx;
}

.group-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.group-members {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
}

.members-count {
  color: #0A84FF;
}

.qrcode-content {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qrcode-image {
  width: 350rpx;
  height: 350rpx;
  margin-bottom: 20rpx;
  border: 1rpx solid #f0f4f8;
  border-radius: 12rpx;
}

.qrcode-tips {
  font-size: 28rpx;
  color: #666;
}

.qrcode-footer {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  border-top: 1rpx solid #f0f4f8;
}

.qrcode-save-btn, .qrcode-share-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  font-weight: 600;
  border-radius: 40rpx;
}

.qrcode-save-btn {
  background-color: #f5f7fa;
  color: #666;
  margin-right: 20rpx;
}

.qrcode-share-btn {
  background: linear-gradient(135deg, #4f8cff, #6fc3ff);
  color: #fff;
}

/* 聊天对话框 */
.chat-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.chat-container {
  width: 700rpx;
  height: 900rpx;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  animation: popup 0.3s ease;
  display: flex;
  flex-direction: column;
}

.chat-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f4f8;
  background: linear-gradient(135deg, #4f8cff, #6fc3ff);
}

.chat-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #fff;
}

.chat-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: #fff;
}

.chat-messages {
  flex: 1;
  padding: 20rpx;
  background-color: #f5f7fa;
  overflow: hidden;
}

.messages-scroll {
  height: 100%;
}

.message-item {
  display: flex;
  margin-bottom: 30rpx;
  align-items: flex-start;
}

.message-mine {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin: 0 20rpx;
  border: 2rpx solid #f0f4f8;
}

.message-avatar.mine {
  margin-right: 0;
}

.message-content {
  max-width: 70%;
  border-radius: 20rpx;
  padding: 20rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.message-mine .message-content {
  background: linear-gradient(135deg, #4f8cff, #6fc3ff);
}

.message-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.message-mine .message-text {
  color: #fff;
}

.location-content {
  width: 400rpx;
}

.location-title {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.location-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.message-mine .location-icon {
  filter: brightness(0) invert(1);
}

.location-address {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.message-mine .location-address {
  color: rgba(255, 255, 255, 0.9);
}

.location-preview {
  width: 100%;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.map-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.chat-input-area {
  padding: 20rpx;
  border-top: 1rpx solid #f0f4f8;
}

.chat-toolbar {
  display: flex;
  padding-bottom: 20rpx;
}

.toolbar-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 30rpx;
  opacity: 0.7;
}

.chat-input-box {
  display: flex;
  align-items: center;
}

.chat-input {
  flex: 1;
  height: 80rpx;
  background-color: #f5f7fa;
  border-radius: 40rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
}

.send-btn {
  width: 120rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #4f8cff, #6fc3ff);
  color: #fff;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  font-size: 28rpx;
  font-weight: 600;
}

/* 发布类型选择弹窗样式 */
.publish-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.publish-card {
  width: 80%;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);
  animation: popup-in 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
  transform: scale(0.95);
  opacity: 0.8;
}

@keyframes popup-in {
  0% {
    transform: scale(0.95);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.publish-header {
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #F2F2F7;
}

.publish-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background-color: #F2F2F7;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  font-size: 32rpx;
  color: #999999;
}

.publish-options {
  padding: 32rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 32rpx;
}

.publish-option {
  width: calc(50% - 16rpx);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx 0;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.publish-option:active {
  transform: scale(0.95);
  background-color: #F5F8FC;
}

.option-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.option-icon-wrapper {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.12);
  border: 2rpx solid rgba(255, 255, 255, 0.7);
  position: relative;
  overflow: hidden;
}

.option-icon-wrapper::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.3), transparent 70%);
  z-index: 1;
}

.option-icon {
  width: 60rpx;
  height: 60rpx;
  filter: brightness(0) invert(1);
  position: relative;
  z-index: 2;
}

.people-car {
  background: linear-gradient(135deg, #0A84FF, #5AC8FA);
}

.car-people {
  background: linear-gradient(135deg, #FF2D55, #FF9500);
}

.goods-car {
  background: linear-gradient(135deg, #30D158, #34C759);
}

.car-goods {
  background: linear-gradient(135deg, #FF9F0A, #FFD60A);
}
</style> 