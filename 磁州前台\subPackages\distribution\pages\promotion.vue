<template>
  <view class="promotion-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">推广工具</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 推广工具卡片 -->
    <view class="tools-grid">
      <view class="tool-card" @click="navigateTo('/subPackages/promotion/pages/promotion-tool')">
        <view class="tool-icon poster"></view>
        <text class="tool-name">推广海报</text>
        <text class="tool-desc">生成精美商品海报</text>
      </view>
      
      <view class="tool-card" @click="navigateTo('/subPackages/promotion/pages/qrcode')">
        <view class="tool-icon qrcode"></view>
        <text class="tool-name">推广二维码</text>
        <text class="tool-desc">生成小程序码</text>
      </view>
      
      <view class="tool-card" @click="navigateToMerchant('/subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-text')">
        <view class="tool-icon text"></view>
        <text class="tool-name">推广文案</text>
        <text class="tool-desc">复制推广话术</text>
      </view>
      
      <view class="tool-card" @click="navigateToMerchant('/subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-materials')">
        <view class="tool-icon materials"></view>
        <text class="tool-name">推广素材</text>
        <text class="tool-desc">图片/视频素材</text>
      </view>
    </view>
    
    <!-- 推广链接 -->
    <view class="link-card">
      <view class="card-header">
        <text class="card-title">我的推广链接</text>
        <view class="refresh-btn" @click="generateLink">
          <view class="refresh-icon"></view>
          <text>刷新</text>
        </view>
      </view>
      
      <view class="link-content">
        <text class="link-url">{{promotionLink.url || '生成中...'}}</text>
        <view class="link-actions">
          <button class="action-btn copy" @click="copyLink">复制链接</button>
          <button class="action-btn share" @click="shareLink">分享</button>
        </view>
      </view>
    </view>
    
    <!-- 推广指南 -->
    <view class="guide-card">
      <view class="card-header">
        <text class="card-title">推广指南</text>
      </view>
      
      <view class="guide-steps">
        <view class="guide-step">
          <view class="step-icon step1"></view>
          <view class="step-content">
            <text class="step-title">选择工具</text>
            <text class="step-desc">选择合适的推广工具</text>
          </view>
        </view>
        
        <view class="step-arrow"></view>
        
        <view class="guide-step">
          <view class="step-icon step2"></view>
          <view class="step-content">
            <text class="step-title">分享推广</text>
            <text class="step-desc">分享到社交平台</text>
          </view>
        </view>
        
        <view class="step-arrow"></view>
        
        <view class="guide-step">
          <view class="step-icon step3"></view>
          <view class="step-content">
            <text class="step-title">获得佣金</text>
            <text class="step-desc">用户下单得佣金</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 推广技巧 -->
    <view class="tips-card">
      <view class="card-header">
        <text class="card-title">推广技巧</text>
      </view>
      
      <view class="tips-list">
        <view class="tip-item">
          <view class="tip-icon"></view>
          <text class="tip-text">选择热门商品进行推广，转化率更高</text>
        </view>
        
        <view class="tip-item">
          <view class="tip-icon"></view>
          <text class="tip-text">在朋友圈分享时，添加个人使用体验</text>
        </view>
        
        <view class="tip-item">
          <view class="tip-icon"></view>
          <text class="tip-text">定期更新推广内容，保持新鲜感</text>
        </view>
        
        <view class="tip-item">
          <view class="tip-icon"></view>
          <text class="tip-text">结合节日活动，推广相关商品</text>
        </view>
      </view>
    </view>
    
    <!-- 推广数据 -->
    <view class="data-card">
      <view class="card-header">
        <text class="card-title">推广数据</text>
        <view class="view-all" @click="navigateTo('/subPackages/distribution/pages/team')">查看详情</view>
      </view>
      
      <view class="data-grid">
        <view class="data-item">
          <text class="data-value">{{promotionData.viewCount}}</text>
          <text class="data-label">浏览量</text>
        </view>
        
        <view class="data-item">
          <text class="data-value">{{promotionData.orderCount}}</text>
          <text class="data-label">订单量</text>
        </view>
        
        <view class="data-item">
          <text class="data-value">{{promotionData.conversionRate}}%</text>
          <text class="data-label">转化率</text>
        </view>
        
        <view class="data-item">
          <text class="data-value">¥{{formatCommission(promotionData.commission)}}</text>
          <text class="data-label">总佣金</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import distributionService from '@/utils/distributionService';

// 推广链接
const promotionLink = reactive({
  url: '',
  shortUrl: '',
  qrCodeUrl: ''
});

// 推广数据
const promotionData = reactive({
  viewCount: 0,
  orderCount: 0,
  conversionRate: 0,
  commission: 0
});

// 用户信息
const userInfo = reactive({
  userId: '1001', // 模拟数据，实际应从用户系统获取
  distributorId: 'DIS1001'
});

// 页面加载
onMounted(async () => {
  // 生成推广链接
  await generateLink();
  
  // 获取推广数据
  await getPromotionData();
});

// 生成推广链接
const generateLink = async () => {
  try {
    const result = await distributionService.generatePromotionLink({
      type: 'platform',
      id: 'home',
      distributorId: userInfo.distributorId
    });
    
    if (result) {
      Object.assign(promotionLink, result);
    }
  } catch (error) {
    console.error('生成推广链接失败', error);
    uni.showToast({
      title: '生成推广链接失败',
      icon: 'none'
    });
  }
};

// 获取推广数据
const getPromotionData = async () => {
  try {
    // 这里应该调用API获取推广数据
    // 暂时使用模拟数据
    promotionData.viewCount = 1256;
    promotionData.orderCount = 45;
    promotionData.conversionRate = 3.58;
    promotionData.commission = 1256.8;
  } catch (error) {
    console.error('获取推广数据失败', error);
  }
};

// 复制链接
const copyLink = () => {
  if (!promotionLink.url) {
    uni.showToast({
      title: '链接生成中，请稍后再试',
      icon: 'none'
    });
    return;
  }
  
  uni.setClipboardData({
    data: promotionLink.url,
    success: () => {
      uni.showToast({
        title: '链接已复制',
        icon: 'success'
      });
    }
  });
};

// 分享链接
const shareLink = () => {
  if (!promotionLink.url) {
    uni.showToast({
      title: '链接生成中，请稍后再试',
      icon: 'none'
    });
    return;
  }
  
  // 调用分享API
  uni.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage', 'shareTimeline']
  });
};

// 格式化佣金
const formatCommission = (amount) => {
  return distributionService.formatCommission(amount);
};

// 页面导航
const navigateTo = (url) => {
  uni.navigateTo({ url });
};

// 导航到商家营销页面
const navigateToMerchant = (url) => {
  // 检查是否有权限
  uni.showModal({
    title: '提示',
    content: '此功能需要商家权限，是否申请成为商家专属分销员？',
    confirmText: '去申请',
    success: (res) => {
      if (res.confirm) {
        uni.navigateTo({
          url: '/subPackages/distribution/pages/merchant-apply'
        });
      }
    }
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 显示帮助
const showHelp = () => {
  uni.showModal({
    title: '推广工具帮助',
    content: '推广工具页面提供了多种推广方式，包括海报、二维码、文案等。您可以选择适合的工具进行推广，获取佣金。',
    showCancel: false
  });
};
</script>

<style lang="scss">
.promotion-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 88rpx 32rpx 30rpx;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);
}

.navbar-back {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24rpx;
  height: 24rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.navbar-right {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

/* 推广工具卡片 */
.tools-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.tool-card {
  width: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
  box-sizing: border-box;
  border-right: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
}

.tool-card:nth-child(2n) {
  border-right: none;
}

.tool-card:nth-child(3), .tool-card:nth-child(4) {
  border-bottom: none;
}

.tool-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
  border-radius: 40rpx;
}

.tool-icon.poster {
  background-color: #FF9500;
}

.tool-icon.qrcode {
  background-color: #409EFF;
}

.tool-icon.text {
  background-color: #67C23A;
}

.tool-icon.materials {
  background-color: #E6A23C;
}

.tool-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.tool-desc {
  font-size: 24rpx;
  color: #999;
}

/* 卡片通用样式 */
.link-card,
.guide-card,
.tips-card,
.data-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.refresh-btn {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #6B0FBE;
}

.refresh-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
  background-color: #6B0FBE;
  border-radius: 40rpx;
}

.view-all {
  font-size: 26rpx;
  color: #6B0FBE;
}

/* 推广链接 */
.link-content {
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 20rpx;
}

.link-url {
  font-size: 26rpx;
  color: #666;
  word-break: break-all;
  margin-bottom: 20rpx;
  display: block;
}

.link-actions {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  font-size: 24rpx;
  padding: 8rpx 30rpx;
  border-radius: 30rpx;
  margin-left: 20rpx;
  line-height: 1.5;
}

.action-btn.copy {
  background: #6B0FBE;
  color: #fff;
}

.action-btn.share {
  background: #FF9500;
  color: #fff;
}

/* 推广指南 */
.guide-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.guide-step {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.step-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
  border-radius: 40rpx;
}

.step-icon.step1 {
  background-color: #6B0FBE;
}

.step-icon.step2 {
  background-color: #409EFF;
}

.step-icon.step3 {
  background-color: #67C23A;
}

.step-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.step-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.step-desc {
  font-size: 24rpx;
  color: #999;
}

.step-arrow {
  width: 40rpx;
  height: 20rpx;
}

/* 推广技巧 */
.tips-list {
  margin-bottom: 20rpx;
}

.tip-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  width: 16rpx;
  height: 16rpx;
  border-radius: 40rpx;
  background-color: #6B0FBE;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.tip-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 推广数据 */
.data-grid {
  display: flex;
  flex-wrap: wrap;
}

.data-item {
  width: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.data-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.data-label {
  font-size: 24rpx;
  color: #999;
}
</style> 