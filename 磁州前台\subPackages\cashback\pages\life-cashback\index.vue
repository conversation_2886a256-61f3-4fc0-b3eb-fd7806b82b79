<template>
  <view class="life-cashback-container">
    <!-- 自定义导航栏 -->
    <custom-navbar title="生活返现" :show-back="true"></custom-navbar>
    
    <!-- 内容区域 -->
    <view class="content-container">
      <!-- 生活服务分类 -->
      <view class="category-section">
        <view class="category-grid">
          <!-- 外卖红包 -->
          <view class="category-item" @tap="navigateToService('takeout')">
            <view class="item-icon-container" style="background-color: #FFE8E0;">
              <svg class="item-icon" viewBox="0 0 24 24" width="28" height="28">
                <path fill="#FF6B6B" d="M15.5,21L14,8H16.23L15.1,3.46L16.84,3L18.09,8H22L20.5,21H15.5M5,11H10A3,3 0 0,1 13,14H2A3,3 0 0,1 5,11M13,18A3,3 0 0,1 10,21H5A3,3 0 0,1 2,18H13M3,15H8L9.5,16.5L11,15H12A1,1 0 0,1 13,16A1,1 0 0,1 12,17H3A1,1 0 0,1 2,16A1,1 0 0,1 3,15Z" />
              </svg>
            </view>
            <text class="item-title">领外卖红包</text>
            <text class="item-desc">最高返2%-5%</text>
          </view>
          
          <!-- 打车红包 -->
          <view class="category-item" @tap="navigateToService('taxi')">
            <view class="item-icon-container" style="background-color: #FFF2D6;">
              <svg class="item-icon" viewBox="0 0 24 24" width="28" height="28">
                <path fill="#FFA726" d="M5,11L6.5,6.5H17.5L19,11M17.5,16A1.5,1.5 0 0,1 16,14.5A1.5,1.5 0 0,1 17.5,13A1.5,1.5 0 0,1 19,14.5A1.5,1.5 0 0,1 17.5,16M6.5,16A1.5,1.5 0 0,1 5,14.5A1.5,1.5 0 0,1 6.5,13A1.5,1.5 0 0,1 8,14.5A1.5,1.5 0 0,1 6.5,16M18.92,6C18.72,5.42 18.16,5 17.5,5H6.5C5.84,5 5.28,5.42 5.08,6L3,12V20A1,1 0 0,0 4,21H5A1,1 0 0,0 6,20V19H18V20A1,1 0 0,0 19,21H20A1,1 0 0,0 21,20V12L18.92,6Z" />
              </svg>
            </view>
            <text class="item-title">领打车红包</text>
            <text class="item-desc">最高返2.4%-5%</text>
          </view>
          
          <!-- 电影票 -->
          <view class="category-item" @tap="navigateToService('movie')">
            <view class="item-icon-container" style="background-color: #FFE0EC;">
              <svg class="item-icon" viewBox="0 0 24 24" width="28" height="28">
                <path fill="#E91E63" d="M18,9H16V7H18M18,13H16V11H18M18,17H16V15H18M8,9H6V7H8M8,13H6V11H8M8,17H6V15H8M18,3V5H16V3H8V5H6V3H4V21H6V19H8V21H16V19H18V21H20V3H18Z" />
              </svg>
            </view>
            <text class="item-title">电影票8折起</text>
            <text class="item-desc">返10%</text>
          </view>
          
          <!-- 快递 -->
          <view class="category-item" @tap="navigateToService('express')">
            <view class="item-icon-container" style="background-color: #E3F1FF;">
              <svg class="item-icon" viewBox="0 0 24 24" width="28" height="28">
                <path fill="#2196F3" d="M3,14H5V20H19V14H21V21A1,1 0 0,1 20,22H4A1,1 0 0,1 3,21V14M17,4H7V2H17V4M17.5,5L12,10.5L6.5,5H17.5M20,6.4L17.9,8.5L15.5,6.1L16.9,4.7L20,7.8V6.4M5.93,4.7L7.33,6.1L4.93,8.5L2.83,6.4V7.8L5.93,4.7Z" />
              </svg>
            </view>
            <text class="item-title">寄快递返现</text>
            <text class="item-desc">返15%</text>
          </view>
          
          <!-- 酒店 -->
          <view class="category-item" @tap="navigateToService('hotel')">
            <view class="item-icon-container" style="background-color: #E8F5E9;">
              <svg class="item-icon" viewBox="0 0 24 24" width="28" height="28">
                <path fill="#4CAF50" d="M19,7H11V14H3V5H1V20H3V17H21V20H23V11A4,4 0 0,0 19,7M7,13A3,3 0 0,0 10,10A3,3 0 0,0 7,7A3,3 0 0,0 4,10A3,3 0 0,0 7,13Z" />
              </svg>
            </view>
            <text class="item-title">酒店订房</text>
            <text class="item-desc">返6%</text>
          </view>
          
          <!-- 机票 -->
          <view class="category-item" @tap="navigateToService('flight')">
            <view class="item-icon-container" style="background-color: #E0F7FA;">
              <svg class="item-icon" viewBox="0 0 24 24" width="28" height="28">
                <path fill="#00BCD4" d="M21,16V14L13,9V3.5A1.5,1.5 0 0,0 11.5,2A1.5,1.5 0 0,0 10,3.5V9L2,14V16L10,13.5V19L8,20.5V22L11.5,21L15,22V20.5L13,19V13.5L21,16Z" />
              </svg>
            </view>
            <text class="item-title">机票预订</text>
            <text class="item-desc">返3%</text>
          </view>
          
          <!-- 火车票 -->
          <view class="category-item" @tap="navigateToService('train')">
            <view class="item-icon-container" style="background-color: #FFF3E0;">
              <svg class="item-icon" viewBox="0 0 24 24" width="28" height="28">
                <path fill="#FF9800" d="M18,10H6V5H18M12,17C10.89,17 10,16.1 10,15C10,13.89 10.89,13 12,13A2,2 0 0,1 14,15A2,2 0 0,1 12,17M4,15.5A3.5,3.5 0 0,0 7.5,19L6,20.5V21H18V20.5L16.5,19A3.5,3.5 0 0,0 20,15.5V5C20,1.5 16.42,1 12,1C7.58,1 4,1.5 4,5V15.5Z" />
              </svg>
            </view>
            <text class="item-title">火车票</text>
            <text class="item-desc">返2%</text>
          </view>
          
          <!-- 会员充值 -->
          <view class="category-item" @tap="navigateToService('vip')">
            <view class="item-icon-container" style="background-color: #F3E5F5;">
              <svg class="item-icon" viewBox="0 0 24 24" width="28" height="28">
                <path fill="#9C27B0" d="M12,8H4A2,2 0 0,0 2,10V14A2,2 0 0,0 4,16H5V20A1,1 0 0,0 6,21H8A1,1 0 0,0 9,20V16H12L17,20V4L12,8M21.5,12C21.5,13.71 20.54,15.26 19,16V8C20.53,8.75 21.5,10.3 21.5,12Z" />
              </svg>
            </view>
            <text class="item-title">会员充值</text>
            <text class="item-desc">3.6元起</text>
          </view>
        </view>
      </view>
      
      <!-- 优惠券区域 -->
      <view class="coupon-section">
        <view class="section-header">
          <text class="section-title">热门优惠券</text>
          <view class="section-more" @tap="goToCoupons">
            <text>更多</text>
            <svg class="arrow-icon" viewBox="0 0 24 24" width="16" height="16">
              <path fill="#999999" d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
            </svg>
          </view>
        </view>
        
        <scroll-view scroll-x class="coupons-scroll" show-scrollbar="false">
          <view class="coupons-container">
            <!-- 优惠券1 -->
            <view class="coupon-item" @tap="navigateToService('coupon', 1)">
              <view class="coupon-content">
                <text class="coupon-platform">淘宝-搜了么</text>
                <text class="coupon-desc">免费奶茶喝到爽</text>
                <text class="coupon-subdesc">下单再返3元</text>
              </view>
              <view class="coupon-price">
                <text class="price-value">15元</text>
                <text class="price-tag">券</text>
              </view>
            </view>
            
            <!-- 优惠券2 -->
            <view class="coupon-item" @tap="navigateToService('coupon', 2)">
              <view class="coupon-content">
                <text class="coupon-platform">美团外卖</text>
                <text class="coupon-desc">无门槛红包</text>
                <text class="coupon-subdesc">满20元可用</text>
              </view>
              <view class="coupon-price">
                <text class="price-value">10元</text>
                <text class="price-tag">券</text>
              </view>
            </view>
            
            <!-- 优惠券3 -->
            <view class="coupon-item" @tap="navigateToService('coupon', 3)">
              <view class="coupon-content">
                <text class="coupon-platform">滴滴出行</text>
                <text class="coupon-desc">打车立减</text>
                <text class="coupon-subdesc">全国通用</text>
              </view>
              <view class="coupon-price">
                <text class="price-value">8元</text>
                <text class="price-tag">券</text>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 热门活动 -->
      <view class="activity-section">
        <view class="section-header">
          <text class="section-title">热门活动</text>
        </view>
        
        <view class="activity-list">
          <!-- 活动1 -->
          <view class="activity-item" @tap="navigateToActivity(1)">
            <image class="activity-image" src="/static/images/cashback/activity-1.png" mode="aspectFill"></image>
            <view class="activity-info">
              <text class="activity-title">外卖周末狂欢</text>
              <text class="activity-desc">周末下单满减+返利双重优惠</text>
              <view class="activity-tags">
                <text class="tag">满减</text>
                <text class="tag">返利</text>
              </view>
            </view>
          </view>
          
          <!-- 活动2 -->
          <view class="activity-item" @tap="navigateToActivity(2)">
            <image class="activity-image" src="/static/images/cashback/activity-2.png" mode="aspectFill"></image>
            <view class="activity-info">
              <text class="activity-title">电影节优惠季</text>
              <text class="activity-desc">购票低至5折，再享10%返利</text>
              <view class="activity-tags">
                <text class="tag">折扣</text>
                <text class="tag">返利</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import CustomNavbar from '../../components/CustomNavbar.vue';

export default {
  components: {
    CustomNavbar
  },
  data() {
    return {
      
    };
  },
  onLoad() {
    // 设置页面不显示系统导航栏
    uni.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#9C27B0'
    });
  },
  methods: {
    navigateToService(type, id = 0) {
      uni.navigateTo({
        url: `/subPackages/cashback/pages/life-service/index?type=${type}${id ? '&id=' + id : ''}`
      });
    },
    goToCoupons() {
      uni.showToast({
        title: '优惠券功能正在开发中',
        icon: 'none',
        duration: 2000
      });
    },
    navigateToActivity(id) {
      uni.showToast({
        title: '活动详情功能正在开发中',
        icon: 'none',
        duration: 2000
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.life-cashback-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content-container {
  padding-top: calc(var(--status-bar-height) + 44px);
  padding-bottom: 20px;
}

.category-section {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .item-icon-container {
    width: 52px;
    height: 52px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
  }
  
  .item-title {
    font-size: 14px;
    color: #333333;
    margin-bottom: 4px;
    text-align: center;
  }
  
  .item-desc {
    font-size: 12px;
    color: #9C27B0;
    text-align: center;
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333333;
  }
  
  .section-more {
    display: flex;
    align-items: center;
    color: #999999;
    font-size: 14px;
  }
}

.coupon-section {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.coupons-scroll {
  width: 100%;
  white-space: nowrap;
}

.coupons-container {
  display: inline-flex;
  padding-bottom: 8px;
}

.coupon-item {
  width: 220px;
  height: 100px;
  background: linear-gradient(135deg, #F5F0FF 0%, #EDE7F6 100%);
  border-radius: 12px;
  padding: 16px;
  margin-right: 12px;
  display: flex;
  position: relative;
  overflow: hidden;
  
  &:last-child {
    margin-right: 0;
  }
  
  &::after {
    content: '';
    position: absolute;
    right: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #FFFFFF;
    box-shadow: inset 0 0 0 2px #F5F0FF;
  }
  
  &::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #FFFFFF;
    box-shadow: inset 0 0 0 2px #F5F0FF;
  }
  
  .coupon-content {
    flex: 1;
    
    .coupon-platform {
      font-size: 14px;
      color: #333333;
      margin-bottom: 6px;
      display: block;
    }
    
    .coupon-desc {
      font-size: 16px;
      color: #9C27B0;
      font-weight: 600;
      margin-bottom: 4px;
      display: block;
    }
    
    .coupon-subdesc {
      font-size: 12px;
      color: #666666;
      display: block;
    }
  }
  
  .coupon-price {
    background-color: #FF5252;
    border-radius: 20px;
    padding: 4px 8px;
    height: fit-content;
    display: flex;
    align-items: center;
    
    .price-value {
      font-size: 16px;
      font-weight: 600;
      color: #FFFFFF;
    }
    
    .price-tag {
      font-size: 12px;
      color: #FFFFFF;
      background-color: rgba(255, 255, 255, 0.3);
      border-radius: 10px;
      padding: 0 4px;
      margin-left: 2px;
    }
  }
}

.activity-section {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.activity-item {
  display: flex;
  border-radius: 12px;
  overflow: hidden;
  background-color: #F9F9F9;
  
  .activity-image {
    width: 100px;
    height: 100px;
    object-fit: cover;
  }
  
  .activity-info {
    flex: 1;
    padding: 12px;
    
    .activity-title {
      font-size: 16px;
      font-weight: 600;
      color: #333333;
      margin-bottom: 6px;
      display: block;
    }
    
    .activity-desc {
      font-size: 14px;
      color: #666666;
      margin-bottom: 8px;
      display: block;
    }
    
    .activity-tags {
      display: flex;
      gap: 8px;
      
      .tag {
        font-size: 12px;
        color: #9C27B0;
        background-color: rgba(156, 39, 176, 0.1);
        border-radius: 10px;
        padding: 2px 8px;
      }
    }
  }
}
</style> 