<view class="group-buy-card data-v-3b272413"><activity-card wx:if="{{o}}" class="data-v-3b272413" u-s="{{['special-info']}}" bindfavorite="{{m}}" bindaction="{{n}}" u-i="3b272413-0" bind:__l="__l" u-p="{{o}}"><view class="group-buy-special data-v-3b272413" slot="special-info"><view class="price-section data-v-3b272413"><view class="current-price data-v-3b272413"><text class="price-symbol data-v-3b272413">¥</text><text class="price-value data-v-3b272413">{{a}}</text></view><view class="original-price data-v-3b272413"><text class="price-label data-v-3b272413">原价</text><text class="price-through data-v-3b272413">¥{{b}}</text></view><view class="discount-tag data-v-3b272413"><text class="discount-value data-v-3b272413">{{c}}折</text></view></view><view class="group-progress data-v-3b272413"><view class="progress-header data-v-3b272413"><text class="progress-title data-v-3b272413">拼团进度</text><text class="progress-status data-v-3b272413">{{d}}/{{e}}人</text></view><view class="progress-bar data-v-3b272413"><view class="progress-inner data-v-3b272413" style="{{'width:' + f}}"></view></view><view wx:if="{{g}}" class="progress-tip data-v-3b272413"><text class="tip-text data-v-3b272413">还差{{h}}人成团，快邀请好友吧！</text></view></view><view wx:if="{{i}}" class="group-members data-v-3b272413"><view class="members-title data-v-3b272413">参团好友</view><view class="members-avatars data-v-3b272413"><view wx:for="{{j}}" wx:for-item="member" wx:key="c" class="member-avatar-wrapper data-v-3b272413"><image src="{{member.a}}" class="member-avatar data-v-3b272413" mode="aspectFill"></image><view wx:if="{{member.b}}" class="team-leader-badge data-v-3b272413">团长</view></view><view wx:if="{{k}}" class="more-members data-v-3b272413"><text class="data-v-3b272413">+{{l}}</text></view></view></view></view></activity-card></view>