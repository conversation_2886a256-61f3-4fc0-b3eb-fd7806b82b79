<template>
  <view class="pet-detail-container">
    <!-- 添加自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">宠物信息详情</view>
      <view class="navbar-right">
        <!-- 占位 -->
      </view>
    </view>
    
    <!-- 隐藏的分享按钮，用于自动触发 -->
    <button id="shareButton" class="hidden-share-btn" open-type="share"></button>
    
    <view class="pet-detail-wrapper">
      <!-- 宠物基本信息卡片 -->
      <view class="content-card pet-info-card">
        <view class="pet-header">
          <view class="pet-title-row">
            <text class="pet-title">{{petData.title}}</text>
            <text class="pet-price">{{petData.price}}</text>
          </view>
          <view class="pet-meta">
            <view class="pet-tag-group">
              <view class="pet-tag" v-for="(tag, index) in petData.tags" :key="index">{{tag}}</view>
            </view>
            <text class="pet-publish-time">发布于 {{formatTime(petData.publishTime)}}</text>
          </view>
        </view>
        
        <!-- 宠物图片轮播 -->
        <swiper class="pet-swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500">
          <swiper-item v-for="(image, index) in petData.images" :key="index">
            <image :src="image" mode="aspectFill" class="pet-image"></image>
          </swiper-item>
        </swiper>
        
        <!-- 基本信息 -->
        <view class="pet-basic-info">
          <view class="info-item">
            <text class="info-label">品种</text>
            <text class="info-value">{{petData.breed}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">年龄</text>
            <text class="info-value">{{petData.age}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">性别</text>
            <text class="info-value">{{petData.gender}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">疫苗情况</text>
            <text class="info-value">{{petData.vaccination}}</text>
          </view>
        </view>
      </view>
      
      <!-- 宠物特征 -->
      <view class="content-card pet-features-card">
        <view class="section-title">宠物特征</view>
        <view class="features-list">
          <view class="feature-item" v-for="(item, index) in petData.features" :key="index">
            <text class="feature-label">{{item.label}}</text>
            <text class="feature-value">{{item.value}}</text>
          </view>
        </view>
      </view>
      
      <!-- 健康状况 -->
      <view class="content-card health-card">
        <view class="section-title">健康状况</view>
        <view class="health-list">
          <view class="health-item" v-for="(item, index) in petData.health" :key="index">
            <text class="health-label">{{item.label}}</text>
            <text class="health-value">{{item.value}}</text>
          </view>
        </view>
      </view>
      
      <!-- 宠物描述 -->
      <view class="content-card description-card">
        <view class="section-title">宠物描述</view>
        <view class="description-content">
          <text class="description-text">{{petData.description}}</text>
        </view>
      </view>
      
      <!-- 主人信息 -->
      <view class="content-card owner-card">
        <view class="owner-header">
          <view class="owner-avatar">
            <image :src="petData.owner.avatar" mode="aspectFill"></image>
          </view>
          <view class="owner-info">
            <text class="owner-name">{{petData.owner.name}}</text>
            <view class="owner-meta">
              <text class="owner-type">{{petData.owner.type}}</text>
              <text class="owner-rating">信用等级 {{petData.owner.rating}}</text>
            </view>
          </view>
          <view class="owner-auth" v-if="petData.owner.isVerified">
            <text class="iconfont icon-verified"></text>
            <text class="auth-text">已认证</text>
          </view>
        </view>
      </view>
      
      <!-- 联系方式 -->
      <view class="content-card contact-card">
        <view class="contact-header">
          <text class="section-title">联系方式</text>
        </view>
        <view class="contact-content">
          <view class="contact-item">
            <text class="contact-label">联系人</text>
            <text class="contact-value">{{petData.contact.name}}</text>
          </view>
          <view class="contact-item">
            <text class="contact-label">电话</text>
            <text class="contact-value contact-phone" @click="callPhone">{{petData.contact.phone}}</text>
          </view>
          <view class="contact-tips">
            <text class="tips-icon iconfont icon-info"></text>
            <text class="tips-text">请说明在"磁州生活网"看到的信息</text>
          </view>
        </view>
      </view>
      
      <!-- 相关推荐模块 -->
      <view class="content-card related-pets-card">
        <view class="section-title">相关推荐</view>
        <view class="related-pets-content">
          <view class="related-pets-list">
            <view class="related-pet-item" v-for="(pet, index) in relatedPets.slice(0, 3)" :key="index" @click="navigateToPet(pet.id)">
              <view class="pet-item-content">
                <view class="pet-item-left">
                  <image class="pet-image" :src="pet.image" mode="aspectFill"></image>
                </view>
                <view class="pet-item-middle">
                  <text class="pet-item-title">{{pet.title}}</text>
                  <view class="pet-item-meta">{{pet.breed}} · {{pet.age}}</view>
                  <view class="pet-item-tags">
                    <text class="pet-item-tag" v-for="(tag, tagIndex) in pet.tags.slice(0, 2)" :key="tagIndex">{{tag}}</text>
                    <text class="pet-item-tag-more" v-if="pet.tags && pet.tags.length > 2">+{{pet.tags.length - 2}}</text>
                  </view>
                </view>
                <view class="pet-item-right">
                  <text class="pet-item-price">{{pet.price}}</text>
                </view>
              </view>
        </view>
            <view class="empty-related-pets" v-if="relatedPets.length === 0">
              <image src="/static/images/empty.png" class="empty-image" mode="aspectFit"></image>
              <text class="empty-text">暂无相关推荐</text>
            </view>
          </view>
          <view class="view-more-btn" v-if="relatedPets.length > 0" @click.stop="navigateToPetList">
            <text class="view-more-text">查看更多宠物</text>
            <text class="view-more-icon iconfont icon-right"></text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="interaction-toolbar">
      <view class="toolbar-item" @click="goToHome">
        <image src="/static/images/tabbar/a首页.png" class="toolbar-icon"></image>
        <text class="toolbar-text">首页</text>
      </view>
      <view class="toolbar-item" @click="toggleCollect">
        <image src="/static/images/tabbar/a收藏.png" class="toolbar-icon"></image>
        <text class="toolbar-text">收藏</text>
      </view>
      <button class="share-button toolbar-item" open-type="share">
        <image src="/static/images/tabbar/a分享.png" class="toolbar-icon"></image>
        <text class="toolbar-text">分享</text>
      </button>
      <view class="toolbar-item" @click="openChat">
        <image src="/static/images/tabbar/a消息.png" class="toolbar-icon"></image>
        <text class="toolbar-text">私信</text>
      </view>
      <view class="toolbar-item call-button" @click="callPhone">
        <view class="call-button-content">
          <text class="call-text">打电话</text>
          <text class="call-subtitle">请说在磁州生活网看到的</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 状态栏高度
const statusBarHeight = ref(20);

// 获取状态栏高度
onMounted(() => {
  try {
    const sysInfo = uni.getSystemInfoSync();
    statusBarHeight.value = sysInfo.statusBarHeight || 20;
  } catch (e) {
    console.error('获取状态栏高度失败', e);
  }
});

// 返回上一页
const goBack = () => {
  uni.navigateBack({
    fail: () => {
      uni.switchTab({
        url: '/pages/index/index'
      });
    }
  });
};

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  return `${date.getMonth() + 1}月${date.getDate()}日`;
};

// 响应式数据
const isCollected = ref(false);
const petData = ref({
  id: 'pet12345',
  title: '纯种金毛幼犬',
  price: '2000元',
  tags: ['纯种', '已打疫苗', '可上门看'],
  publishTime: Date.now() - 86400000 * 2, // 2天前
  images: [
    '/static/images/pet1.jpg',
    '/static/images/pet2.jpg',
    '/static/images/pet3.jpg'
  ],
  breed: '金毛',
  age: '3个月',
  gender: '公',
  vaccination: '已打两针',
  features: [
    { label: '毛色', value: '金黄色' },
    { label: '体型', value: '中等' },
    { label: '性格', value: '温顺' },
    { label: '训练情况', value: '已学会基本指令' }
  ],
  health: [
    { label: '驱虫情况', value: '已驱虫' },
    { label: '疫苗情况', value: '已打两针' },
    { label: '体检情况', value: '健康' },
    { label: '其他情况', value: '无' }
  ],
  description: '纯种金毛幼犬，3个月大，已打两针疫苗，已驱虫，身体健康。性格温顺，已学会基本指令，适合家庭饲养。可上门看狗，价格可谈。',
  owner: {
    name: '李女士',
    avatar: '/static/images/avatar.png',
    type: '个人',
    rating: 'A+',
    isVerified: true
  },
  contact: {
    name: '李女士',
    phone: '13912345678'
  }
});

const relatedPets = ref([
  {
    id: 'pet001',
    title: '纯种金毛幼犬',
    price: '1800元',
    breed: '金毛',
    age: '2个月',
    image: '/static/images/pet-similar1.jpg',
    tags: ['纯种', '健康', '可上门看']
  },
  {
    id: 'pet002',
    title: '纯种金毛幼犬',
    price: '2200元',
    breed: '金毛',
    age: '4个月',
    image: '/static/images/pet-similar2.jpg',
    tags: ['纯种', '已打疫苗']
  },
  {
    id: 'pet003',
    title: '拉布拉多幼犬',
    price: '1600元',
    breed: '拉布拉多',
    age: '3个月',
    image: '/static/images/pet-similar3.jpg',
    tags: ['健康', '聪明']
  }
])

// 方法
const toggleCollect = () => {
  isCollected.value = !isCollected.value;
  if (isCollected.value) {
    uni.showToast({
      title: '收藏成功',
      icon: 'success'
    });
  }
};

const showShareOptions = () => {
  uni.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage', 'shareTimeline']
  });
};

const callPhone = () => {
  uni.makePhoneCall({
    phoneNumber: petData.value.contact.phone,
    fail: () => {
      uni.showToast({
        title: '拨打电话失败',
        icon: 'none'
      });
    }
  });
};

const navigateToPet = (id) => {
  if (id === petData.value.id) return;
  uni.navigateTo({ url: `/pages/publish/pet-detail?id=${id}` });
}

const navigateToPetList = (e) => {
  if (e) e.stopPropagation();
  // 获取宠物品种作为分类参数
  const petCategory = petData.value.breed || '';
  uni.navigateTo({
    url: `/subPackages/service/pages/filter?type=pet&title=${encodeURIComponent('宠物信息')}&category=${encodeURIComponent(petCategory)}&active=pet` 
  });
}

// 跳转到首页
const goToHome = () => {
  uni.switchTab({
    url: '/pages/index/index'
  });
};

// 打开私信聊天
const openChat = () => {
  if (!petData.value.owner || !petData.value.owner.id) {
    uni.showToast({
      title: '无法获取发布者信息',
      icon: 'none'
    });
    return;
  }
  
  // 跳转到聊天页面
  uni.navigateTo({
    url: `/pages/chat/chat?targetId=${petData.value.owner.id}&targetName=${encodeURIComponent(petData.value.owner.name)}&targetAvatar=${encodeURIComponent(petData.value.owner.avatar)}`
  });
};

// 生命周期钩子
onMounted(() => {
  // 修改页面标题
  uni.setNavigationBarTitle({
    title: '宠物详情'
  });
});
</script>

<style>
.pet-detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 150rpx;
  padding-top: 0; /* 移除顶部内边距，由导航栏控制 */
}

.pet-detail-wrapper {
  padding: 24rpx;
  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */
}

.content-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 宠物基本信息卡片 */
.pet-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.pet-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.pet-price {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff4d4f;
}

.pet-meta {
  margin-bottom: 24rpx;
}

.pet-tag-group {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12rpx;
}

.pet-tag {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
  margin-bottom: 12rpx;
}

.pet-publish-time {
  font-size: 24rpx;
  color: #999;
}

/* 轮播图 */
.pet-swiper {
  height: 400rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
}

.pet-image {
  width: 100%;
  height: 100%;
}

/* 基本信息 */
.pet-basic-info {
  display: flex;
  flex-wrap: wrap;
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}

.info-item {
  width: 50%;
  padding: 12rpx 24rpx;
  box-sizing: border-box;
}

.info-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 宠物特征 */
.features-list {
  display: flex;
  flex-direction: column;
}

.feature-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.feature-item:last-child {
  border-bottom: none;
}

.feature-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}

.feature-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 健康状况 */
.health-list {
  display: flex;
  flex-direction: column;
}

.health-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.health-item:last-child {
  border-bottom: none;
}

.health-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}

.health-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 宠物描述 */
.description-content {
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}

.description-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 主人信息 */
.owner-header {
  display: flex;
  align-items: center;
}

.owner-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
}

.owner-avatar image {
  width: 100%;
  height: 100%;
}

.owner-info {
  flex: 1;
}

.owner-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.owner-meta {
  display: flex;
  align-items: center;
}

.owner-type, .owner-rating {
  font-size: 24rpx;
  color: #666;
  margin-right: 16rpx;
}

/* 联系方式 */
.contact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background-color: #1890ff;
  border-radius: 4rpx;
}

.contact-content {
  display: flex;
  flex-direction: column;
}

.contact-item {
  display: flex;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}

.contact-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.contact-phone {
  color: #1890ff;
}

.contact-tips {
  display: flex;
  align-items: center;
  margin-top: 12rpx;
}

.tips-icon {
  margin-right: 8rpx;
}

.tips-text {
  font-size: 24rpx;
  color: #999;
}

/* 相关推荐模块 */
.related-pets-card {
  margin-top: 24rpx;
}

.related-pets-content {
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}

.related-pets-list {
  display: flex;
  flex-direction: column;
}

.related-pet-item {
  display: flex;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.pet-item-content {
  display: flex;
  align-items: center;
}

.pet-item-left {
  width: 200rpx;
  height: 150rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 20rpx;
}

.pet-item-middle {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.pet-item-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.pet-item-meta {
  font-size: 24rpx;
  color: #999;
}

.pet-item-tags {
  display: flex;
  flex-wrap: wrap;
}

.pet-item-tag {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
  margin-bottom: 12rpx;
}

.pet-item-tag-more {
  font-size: 24rpx;
  color: #ff4d4f;
}

.pet-item-right {
  width: 120rpx;
  text-align: right;
}

.pet-item-price {
  font-size: 28rpx;
  color: #ff4d4f;
}

.empty-related-pets {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
}

.empty-image {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.view-more-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12rpx 24rpx;
  background-color: #fff;
  border-radius: 12rpx;
  margin-top: 24rpx;
}

.view-more-text {
  font-size: 28rpx;
  color: #1890ff;
  margin-right: 8rpx;
}

.view-more-icon {
  font-size: 24rpx;
  color: #999;
}

/* 底部互动工具栏 */
.interaction-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10rpx 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  height: 120rpx;
  z-index: 100;
}

.toolbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6rpx 0;
  margin: 0 4rpx;
}

.toolbar-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}

.toolbar-text {
  font-size: 22rpx;
  color: #666;
}

.share-button {
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
  line-height: normal;
  border-radius: 0;
  flex: 1;
}

.share-button::after {
  display: none;
}

.call-button {
  flex: 3; /* 占据更多空间，原来是2，现在改为3让按钮更长 */
  background: linear-gradient(135deg, #0052CC, #0066FF);
  height: 90rpx;
  margin: 0 0 0 10rpx;
  border-radius: 45rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
}

.call-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.call-text {
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  line-height: 1.2;
}

.call-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 20rpx;
  line-height: 1.2;
}

/* 隐藏原来的底部操作栏 */
.action-bar {
  display: none;
}

/* 隐藏的分享按钮 */
.hidden-share-btn {
  position: absolute;
  top: -9999rpx;
  left: -9999rpx;
  width: 0;
  height: 0;
  padding: 0;
  margin: 0;
  opacity: 0;
}

/* 自定义导航栏 */
.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}

.navbar-left {
  width: 60px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 20px;
  height: 20px;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 500;
  color: #fff;
}

.navbar-right {
  width: 60px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style> 