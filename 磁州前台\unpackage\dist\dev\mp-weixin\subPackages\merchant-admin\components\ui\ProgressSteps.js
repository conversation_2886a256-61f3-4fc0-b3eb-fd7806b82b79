"use strict";
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = {
  name: "ProgressSteps",
  props: {
    steps: {
      type: Array,
      required: true
    },
    currentStep: {
      type: Number,
      default: 1
    }
  },
  computed: {
    progressPercentage() {
      return (this.currentStep - 1) / (this.steps.length - 1) * 100;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($props.steps, (step, index, i0) => {
      return common_vendor.e({
        a: index > 0
      }, index > 0 ? {
        b: $props.currentStep > index ? 1 : ""
      } : {}, {
        c: $props.currentStep <= index + 1
      }, $props.currentStep <= index + 1 ? {
        d: common_vendor.t(index + 1)
      } : {}, {
        e: common_vendor.t(step),
        f: index,
        g: $props.currentStep >= index + 1 ? 1 : "",
        h: $props.currentStep > index + 1 ? 1 : ""
      });
    })
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin/components/ui/ProgressSteps.js.map
