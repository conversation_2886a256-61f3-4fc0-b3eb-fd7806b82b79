{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/search/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcc2VhcmNoXGluZGV4LnZ1ZQ"], "sourcesContent": ["<!-- 搜索页面开始 -->\r\n<template>\r\n  <view class=\"search-page page-container\">\r\n    <!-- 搜索框 -->\r\n    <view class=\"search-header card\">\r\n      <view class=\"search-input-wrap\">\r\n        <view class=\"search-icon\">\r\n          <svg viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\">\r\n            <path d=\"M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6c3.2 3.2 8.4 3.2 11.6 0l43.6-43.5c3.2-3.2 3.2-8.4 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z\" fill=\"#999\"/>\r\n          </svg>\r\n        </view>\r\n        <input \r\n          type=\"text\"\r\n          class=\"search-input\"\r\n          v-model=\"keyword\"\r\n          placeholder=\"搜索活动、商品、文章\"\r\n          confirm-type=\"search\"\r\n          @confirm=\"handleSearch\"\r\n          focus\r\n        />\r\n        <view v-if=\"keyword\" class=\"clear-icon\" @click=\"clearKeyword\">\r\n          <svg viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\">\r\n            <path d=\"M512 421.490332L871.696581 61.793751l90.509668 90.509668L602.509668 512l359.696581 359.696581-90.509668 90.509668L512 602.509668 152.303419 962.206249l-90.509668-90.509668L421.490332 512 61.793751 152.303419l90.509668-90.509668z\" fill=\"#999\"/>\r\n          </svg>\r\n        </view>\r\n      </view>\r\n      <text class=\"cancel-btn\" @click=\"goBack\">取消</text>\r\n    </view>\r\n    \r\n    <!-- 搜索历史 -->\r\n    <view v-if=\"!keyword && searchHistory.length > 0 && !hasSearched\" class=\"search-history card\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">搜索历史</text>\r\n        <view class=\"clear-btn\" @click=\"clearHistory\">\r\n          <svg class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\">\r\n            <path d=\"M899.1 869.6l-53-305.6H864c14.4 0 26-11.6 26-26V346c0-14.4-11.6-26-26-26H618V138c0-14.4-11.6-26-26-26H432c-14.4 0-26 11.6-26 26v182H160c-14.4 0-26 11.6-26 26v192c0 14.4 11.6 26 26 26h17.9l-53 305.6c-0.3 1.5-0.4 3-0.4 4.4 0 14.4 11.6 26 26 26h723c1.5 0 3-0.1 4.4-0.4 14.2-2.4 23.7-15.9 21.2-30zM204 390h272V182h72v208h272v104H204V390z m468 440V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H416V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H202.8l45.1-260H776l45.1 260H672z\" fill=\"#999\"/>\r\n          </svg>\r\n          <text>清空</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"tag-list\">\r\n        <view \r\n          v-for=\"(item, index) in searchHistory\" \r\n          :key=\"index\"\r\n          class=\"tag-item\"\r\n          @click=\"useHistoryItem(item)\"\r\n        >\r\n          {{ item }}\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 热门搜索 -->\r\n    <view v-if=\"!keyword && !hasSearched\" class=\"hot-search card\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">热门搜索</text>\r\n      </view>\r\n      \r\n      <view class=\"tag-list\">\r\n        <view \r\n          v-for=\"(item, index) in hotSearches\" \r\n          :key=\"index\"\r\n          class=\"tag-item\"\r\n          :class=\"{ 'hot': index < 3 }\"\r\n          @click=\"useHistoryItem(item)\"\r\n        >\r\n          <text v-if=\"index < 3\" class=\"hot-rank\">{{ index + 1 }}</text>\r\n          {{ item }}\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 搜索结果 -->\r\n    <view v-if=\"hasSearched\" class=\"search-results\">\r\n      <!-- 结果筛选 -->\r\n      <view class=\"filter-tabs card\">\r\n        <view \r\n          v-for=\"(tab, index) in filterTabs\" \r\n          :key=\"index\"\r\n          class=\"filter-tab\"\r\n          :class=\"{ active: currentTab === index }\"\r\n          @click=\"switchTab(index)\"\r\n        >\r\n          {{ tab.name }}\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 活动结果 -->\r\n      <view v-if=\"currentTab === 0\" class=\"result-list\">\r\n        <view \r\n          v-for=\"(item, index) in activityResults\"\r\n          :key=\"index\"\r\n          class=\"activity-item card\"\r\n          @click=\"goToDetail(item.id, 'activity')\"\r\n        >\r\n          <image :src=\"item.image\" mode=\"aspectFill\" class=\"item-image\" />\r\n          <view class=\"item-info\">\r\n            <view class=\"item-title\">{{ item.title }}</view>\r\n            <view class=\"item-desc\">{{ item.description }}</view>\r\n            <view class=\"item-meta\">\r\n              <text class=\"time\">{{ item.time }}</text>\r\n              <text class=\"price\">¥{{ item.price }}起</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 商品结果 -->\r\n      <view v-if=\"currentTab === 1\" class=\"result-list\">\r\n        <view \r\n          v-for=\"(item, index) in productResults\"\r\n          :key=\"index\"\r\n          class=\"product-item card\"\r\n          @click=\"goToDetail(item.id, 'product')\"\r\n        >\r\n          <image :src=\"item.image\" mode=\"aspectFill\" class=\"item-image\" />\r\n          <view class=\"item-info\">\r\n            <view class=\"item-title\">{{ item.title }}</view>\r\n            <view class=\"item-desc\">{{ item.description }}</view>\r\n            <view class=\"item-price\">\r\n              <text class=\"current\">¥{{ item.price }}</text>\r\n              <text class=\"original\">¥{{ item.originalPrice }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 文章结果 -->\r\n      <view v-if=\"currentTab === 2\" class=\"result-list\">\r\n        <view \r\n          v-for=\"(item, index) in articleResults\"\r\n          :key=\"index\"\r\n          class=\"article-item card\"\r\n          @click=\"goToDetail(item.id, 'article')\"\r\n        >\r\n          <view class=\"item-info\">\r\n            <view class=\"item-title\">{{ item.title }}</view>\r\n            <view class=\"item-desc\">{{ item.summary }}</view>\r\n            <view class=\"item-meta\">\r\n              <text class=\"time\">{{ item.publishTime }}</text>\r\n              <text class=\"views\">{{ item.views }}阅读</text>\r\n            </view>\r\n          </view>\r\n          <image v-if=\"item.image\" :src=\"item.image\" mode=\"aspectFill\" class=\"item-image\" />\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 加载状态 -->\r\n      <view v-if=\"isLoading\" class=\"loading-state\">\r\n        <view class=\"loading-indicator\"></view>\r\n        <text>加载中...</text>\r\n      </view>\r\n      \r\n      <!-- 加载更多 -->\r\n      <view v-if=\"hasMore && !isLoading\" class=\"load-more\" @click=\"loadMore\">\r\n        <text>点击加载更多</text>\r\n      </view>\r\n      \r\n      <!-- 没有更多 -->\r\n      <view v-if=\"!hasMore && getCurrentResults.length > 0\" class=\"no-more\">\r\n        <text>没有更多内容了</text>\r\n      </view>\r\n      \r\n      <!-- 空状态 -->\r\n      <view v-if=\"hasSearched && !isLoading && getCurrentResults.length === 0\" class=\"empty-state\">\r\n        <image class=\"empty-icon\" src=\"/static/images/empty-search.svg\" />\r\n        <text class=\"empty-text\">没有找到相关内容</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, reactive, computed, onMounted, watch } from 'vue'\r\n\r\n// 搜索关键词\r\nconst keyword = ref('')\r\n\r\n// 是否已搜索\r\nconst hasSearched = ref(false)\r\n\r\n// 是否加载中\r\nconst isLoading = ref(false)\r\n\r\n// 是否有更多\r\nconst hasMore = ref(true)\r\n\r\n// 当前页码\r\nconst pageNum = ref(1)\r\n\r\n// 每页数量\r\nconst pageSize = ref(10)\r\n\r\n// 搜索历史\r\nconst searchHistory = ref<string[]>([])\r\n\r\n// 热门搜索\r\nconst hotSearches = ref([\r\n  '新年音乐节',\r\n  '美食品鉴会',\r\n  '亲子活动',\r\n  '户外露营',\r\n  '瑜伽课程',\r\n  '电影首映',\r\n  '艺术展览',\r\n  '读书会'\r\n])\r\n\r\n// 筛选标签\r\nconst filterTabs = [\r\n  { name: '活动', type: 'activity' },\r\n  { name: '商品', type: 'product' },\r\n  { name: '文章', type: 'article' }\r\n]\r\n\r\n// 当前选中的标签\r\nconst currentTab = ref(0)\r\n\r\n// 活动结果\r\nconst activityResults = ref([\r\n  {\r\n    id: '1',\r\n    title: '2024新年音乐节',\r\n    description: '迎接新年的音乐盛宴，多位知名歌手现场献唱',\r\n    image: '/static/images/activity1.jpg',\r\n    time: '2024-01-01 19:30',\r\n    price: 199\r\n  },\r\n  {\r\n    id: '2',\r\n    title: '美食品鉴会',\r\n    description: '汇聚全球美食，享受舌尖上的盛宴',\r\n    image: '/static/images/activity2.jpg',\r\n    time: '2024-01-15 14:00',\r\n    price: 299\r\n  }\r\n])\r\n\r\n// 商品结果\r\nconst productResults = ref([\r\n  {\r\n    id: '1',\r\n    title: '限量版纪念T恤',\r\n    description: '2024新年音乐节限定款',\r\n    image: '/static/images/product1.jpg',\r\n    price: 129,\r\n    originalPrice: 199\r\n  },\r\n  {\r\n    id: '2',\r\n    title: '美食礼盒',\r\n    description: '精选多国特色美食',\r\n    image: '/static/images/product2.jpg',\r\n    price: 299,\r\n    originalPrice: 399\r\n  }\r\n])\r\n\r\n// 文章结果\r\nconst articleResults = ref([\r\n  {\r\n    id: '1',\r\n    title: '如何准备一场完美的音乐节',\r\n    summary: '从选址到表演者邀请，一文了解音乐节筹备全过程',\r\n    image: '/static/images/article1.jpg',\r\n    publishTime: '2023-12-20',\r\n    views: 1234\r\n  },\r\n  {\r\n    id: '2',\r\n    title: '2024年最值得期待的十大活动',\r\n    summary: '新的一年，哪些活动最值得参与？本文为你一一盘点',\r\n    image: '/static/images/article2.jpg',\r\n    publishTime: '2023-12-25',\r\n    views: 2345\r\n  }\r\n])\r\n\r\n// 获取当前标签的结果\r\nconst getCurrentResults = computed(() => {\r\n  switch (currentTab.value) {\r\n    case 0:\r\n      return activityResults.value\r\n    case 1:\r\n      return productResults.value\r\n    case 2:\r\n      return articleResults.value\r\n    default:\r\n      return []\r\n  }\r\n})\r\n\r\n// 切换标签\r\nconst switchTab = (index: number) => {\r\n  if (currentTab.value === index) return\r\n  \r\n  currentTab.value = index\r\n  pageNum.value = 1\r\n  hasMore.value = true\r\n  \r\n  // 如果当前标签没有数据，则重新搜索\r\n  if (getCurrentResults.value.length === 0) {\r\n    search()\r\n  }\r\n}\r\n\r\n// 搜索方法\r\nconst search = () => {\r\n  isLoading.value = true\r\n  \r\n  // 模拟搜索请求\r\n  setTimeout(() => {\r\n    // 这里应该是实际的搜索请求\r\n    // 根据关键词和当前标签类型获取搜索结果\r\n    \r\n    // 模拟搜索完成\r\n    isLoading.value = false\r\n    \r\n    // 模拟判断是否有更多数据\r\n    if (pageNum.value >= 3) {\r\n      hasMore.value = false\r\n    }\r\n  }, 1000)\r\n}\r\n\r\n// 处理搜索\r\nconst handleSearch = () => {\r\n  if (!keyword.value.trim()) return\r\n  \r\n  // 保存搜索历史\r\n  saveSearchHistory(keyword.value)\r\n  \r\n  // 设置已搜索状态\r\n  hasSearched.value = true\r\n  \r\n  // 重置页码和更多状态\r\n  pageNum.value = 1\r\n  hasMore.value = true\r\n  \r\n  // 清空之前的搜索结果\r\n  if (currentTab.value === 0) {\r\n    activityResults.value = []\r\n  } else if (currentTab.value === 1) {\r\n    productResults.value = []\r\n  } else if (currentTab.value === 2) {\r\n    articleResults.value = []\r\n  }\r\n  \r\n  // 执行搜索\r\n  search()\r\n}\r\n\r\n// 加载更多\r\nconst loadMore = () => {\r\n  if (isLoading.value || !hasMore.value) return\r\n  \r\n  pageNum.value++\r\n  search()\r\n}\r\n\r\n// 清除关键词\r\nconst clearKeyword = () => {\r\n  keyword.value = ''\r\n  hasSearched.value = false\r\n}\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack()\r\n}\r\n\r\n// 保存搜索历史\r\nconst saveSearchHistory = (key: string) => {\r\n  // 如果已存在，则先移除\r\n  const index = searchHistory.value.indexOf(key)\r\n  if (index > -1) {\r\n    searchHistory.value.splice(index, 1)\r\n  }\r\n  \r\n  // 添加到开头\r\n  searchHistory.value.unshift(key)\r\n  \r\n  // 最多保存10条\r\n  if (searchHistory.value.length > 10) {\r\n    searchHistory.value.pop()\r\n  }\r\n  \r\n  // 保存到本地存储\r\n  uni.setStorageSync('searchHistory', JSON.stringify(searchHistory.value))\r\n}\r\n\r\n// 使用历史记录项\r\nconst useHistoryItem = (item: string) => {\r\n  keyword.value = item\r\n  handleSearch()\r\n}\r\n\r\n// 清空历史记录\r\nconst clearHistory = () => {\r\n  uni.showModal({\r\n    title: '提示',\r\n    content: '确定要清空搜索历史吗？',\r\n    success: (res) => {\r\n      if (res.confirm) {\r\n        searchHistory.value = []\r\n        uni.removeStorageSync('searchHistory')\r\n      }\r\n    }\r\n  })\r\n}\r\n\r\n// 跳转到详情页\r\nconst goToDetail = (id: string, type: string) => {\r\n  let url = ''\r\n  \r\n  switch (type) {\r\n    case 'activity':\r\n      url = `/pages/activity/detail?id=${id}`\r\n      break\r\n    case 'product':\r\n      url = `/pages/product/detail?id=${id}`\r\n      break\r\n    case 'article':\r\n      url = `/pages/article/detail?id=${id}`\r\n      break\r\n  }\r\n  \r\n  if (url) {\r\n    uni.navigateTo({ url })\r\n  }\r\n}\r\n\r\n// 获取历史记录\r\nconst getSearchHistory = () => {\r\n  try {\r\n    const history = uni.getStorageSync('searchHistory')\r\n    if (history) {\r\n      searchHistory.value = JSON.parse(history)\r\n    }\r\n  } catch (e) {\r\n    uni.__f__('error','at subPackages/activity-showcase/pages/search/index.vue:442','获取搜索历史失败', e)\r\n  }\r\n}\r\n\r\n// 监听关键词变化\r\nwatch(keyword, (newVal) => {\r\n  if (!newVal) {\r\n    hasSearched.value = false\r\n  }\r\n})\r\n\r\nonMounted(() => {\r\n  getSearchHistory()\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.search-page {\r\n  padding: 0;\r\n  background: #fff;\r\n  min-height: 100vh;\r\n  \r\n  .search-header {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 24rpx;\r\n    background: #fff;\r\n    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n    border-radius: 0;\r\n    position: sticky;\r\n    top: 0;\r\n    z-index: 100;\r\n    \r\n    .search-input-wrap {\r\n      flex: 1;\r\n      height: 72rpx;\r\n      background: #f5f5f5;\r\n      border-radius: 36rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 0 24rpx;\r\n      \r\n      .search-icon {\r\n        width: 40rpx;\r\n        height: 40rpx;\r\n        margin-right: 16rpx;\r\n        \r\n        svg {\r\n          width: 100%;\r\n          height: 100%;\r\n        }\r\n      }\r\n      \r\n      .search-input {\r\n        flex: 1;\r\n        height: 100%;\r\n        font-size: 28rpx;\r\n      }\r\n      \r\n      .clear-icon {\r\n        width: 40rpx;\r\n        height: 40rpx;\r\n        \r\n        svg {\r\n          width: 100%;\r\n          height: 100%;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .cancel-btn {\r\n      padding-left: 24rpx;\r\n      font-size: 28rpx;\r\n      color: #007AFF;\r\n    }\r\n  }\r\n  \r\n  .search-history,\r\n  .hot-search {\r\n    margin-top: 24rpx;\r\n    \r\n    .section-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 24rpx;\r\n      \r\n      .section-title {\r\n        font-size: 32rpx;\r\n        font-weight: 500;\r\n        color: #333;\r\n      }\r\n      \r\n      .clear-btn {\r\n        display: flex;\r\n        align-items: center;\r\n        font-size: 24rpx;\r\n        color: #999;\r\n        \r\n        .icon {\r\n          width: 32rpx;\r\n          height: 32rpx;\r\n          margin-right: 8rpx;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .tag-list {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      \r\n      .tag-item {\r\n        padding: 12rpx 24rpx;\r\n        background: #f5f5f5;\r\n        border-radius: 8rpx;\r\n        font-size: 24rpx;\r\n        color: #666;\r\n        margin-right: 16rpx;\r\n        margin-bottom: 16rpx;\r\n        position: relative;\r\n        \r\n        &.hot {\r\n          padding-left: 48rpx;\r\n          color: #FF6B6B;\r\n          background: rgba(255, 107, 107, 0.1);\r\n        }\r\n        \r\n        .hot-rank {\r\n          position: absolute;\r\n          left: 16rpx;\r\n          top: 50%;\r\n          transform: translateY(-50%);\r\n          font-weight: 600;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .search-results {\r\n    padding: 24rpx;\r\n    \r\n    .filter-tabs {\r\n      display: flex;\r\n      padding: 0;\r\n      margin-bottom: 24rpx;\r\n      position: sticky;\r\n      top: 0;\r\n      z-index: 10;\r\n      background: #fff;\r\n      \r\n      .filter-tab {\r\n        flex: 1;\r\n        text-align: center;\r\n        padding: 24rpx 0;\r\n        font-size: 28rpx;\r\n        color: #666;\r\n        position: relative;\r\n        \r\n        &.active {\r\n          color: #007AFF;\r\n          font-weight: 500;\r\n          \r\n          &::after {\r\n            content: '';\r\n            position: absolute;\r\n            bottom: 0;\r\n            left: 50%;\r\n            transform: translateX(-50%);\r\n            width: 48rpx;\r\n            height: 4rpx;\r\n            background: #007AFF;\r\n            border-radius: 2rpx;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    \r\n    .result-list {\r\n      .activity-item,\r\n      .product-item {\r\n        display: flex;\r\n        padding: 24rpx;\r\n        margin-bottom: 24rpx;\r\n        \r\n        .item-image {\r\n          width: 200rpx;\r\n          height: 200rpx;\r\n          border-radius: 12rpx;\r\n          margin-right: 24rpx;\r\n        }\r\n        \r\n        .item-info {\r\n          flex: 1;\r\n          display: flex;\r\n          flex-direction: column;\r\n          \r\n          .item-title {\r\n            font-size: 32rpx;\r\n            font-weight: 500;\r\n            color: #333;\r\n            margin-bottom: 16rpx;\r\n          }\r\n          \r\n          .item-desc {\r\n            font-size: 26rpx;\r\n            color: #666;\r\n            margin-bottom: 16rpx;\r\n            flex: 1;\r\n          }\r\n          \r\n          .item-meta {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            font-size: 24rpx;\r\n            \r\n            .time {\r\n              color: #999;\r\n            }\r\n            \r\n            .price {\r\n              color: #FF6B6B;\r\n              font-weight: 500;\r\n            }\r\n          }\r\n          \r\n          .item-price {\r\n            .current {\r\n              font-size: 32rpx;\r\n              color: #FF6B6B;\r\n              font-weight: 500;\r\n              margin-right: 16rpx;\r\n            }\r\n            \r\n            .original {\r\n              font-size: 24rpx;\r\n              color: #999;\r\n              text-decoration: line-through;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      \r\n      .article-item {\r\n        display: flex;\r\n        padding: 24rpx;\r\n        margin-bottom: 24rpx;\r\n        \r\n        .item-info {\r\n          flex: 1;\r\n          margin-right: 24rpx;\r\n          \r\n          .item-title {\r\n            font-size: 32rpx;\r\n            font-weight: 500;\r\n            color: #333;\r\n            margin-bottom: 16rpx;\r\n          }\r\n          \r\n          .item-desc {\r\n            font-size: 26rpx;\r\n            color: #666;\r\n            margin-bottom: 16rpx;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n            -webkit-line-clamp: 2;\r\n            overflow: hidden;\r\n          }\r\n          \r\n          .item-meta {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            font-size: 24rpx;\r\n            color: #999;\r\n          }\r\n        }\r\n        \r\n        .item-image {\r\n          width: 200rpx;\r\n          height: 150rpx;\r\n          border-radius: 8rpx;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .loading-state,\r\n    .load-more,\r\n    .no-more {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      padding: 48rpx 0;\r\n      \r\n      text {\r\n        font-size: 28rpx;\r\n        color: #999;\r\n      }\r\n    }\r\n    \r\n    .loading-state {\r\n      .loading-indicator {\r\n        width: 48rpx;\r\n        height: 48rpx;\r\n        border: 4rpx solid #f3f3f3;\r\n        border-top: 4rpx solid #007AFF;\r\n        border-radius: 50%;\r\n        margin-bottom: 16rpx;\r\n        /* 使用静态样式代替动画 */\r\n      }\r\n    }\r\n    \r\n    .load-more {\r\n      text {\r\n        color: #007AFF;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n<!-- 搜索页面结束 --> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/search/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "watch", "onMounted"], "mappings": ";;;;;;;;;;;AAiLM,UAAA,UAAUA,kBAAI,EAAE;AAGhB,UAAA,cAAcA,kBAAI,KAAK;AAGvB,UAAA,YAAYA,kBAAI,KAAK;AAGrB,UAAA,UAAUA,kBAAI,IAAI;AAGlB,UAAA,UAAUA,kBAAI,CAAC;AAGJA,kBAAAA,IAAI,EAAE;AAGjB,UAAA,gBAAgBA,kBAAc,CAAA,CAAE;AAGtC,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAA,CACD;AAGD,UAAM,aAAa;AAAA,MACjB,EAAE,MAAM,MAAM,MAAM,WAAW;AAAA,MAC/B,EAAE,MAAM,MAAM,MAAM,UAAU;AAAA,MAC9B,EAAE,MAAM,MAAM,MAAM,UAAU;AAAA,IAAA;AAI1B,UAAA,aAAaA,kBAAI,CAAC;AAGxB,UAAM,kBAAkBA,cAAAA,IAAI;AAAA,MAC1B;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,IAAA,CACD;AAGD,UAAM,iBAAiBA,cAAAA,IAAI;AAAA,MACzB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,OAAO;AAAA,QACP,OAAO;AAAA,QACP,eAAe;AAAA,MACjB;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,OAAO;AAAA,QACP,OAAO;AAAA,QACP,eAAe;AAAA,MACjB;AAAA,IAAA,CACD;AAGD,UAAM,iBAAiBA,cAAAA,IAAI;AAAA,MACzB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,OAAO;AAAA,QACP,aAAa;AAAA,QACb,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,OAAO;AAAA,QACP,aAAa;AAAA,QACb,OAAO;AAAA,MACT;AAAA,IAAA,CACD;AAGK,UAAA,oBAAoBC,cAAAA,SAAS,MAAM;AACvC,cAAQ,WAAW,OAAO;AAAA,QACxB,KAAK;AACH,iBAAO,gBAAgB;AAAA,QACzB,KAAK;AACH,iBAAO,eAAe;AAAA,QACxB,KAAK;AACH,iBAAO,eAAe;AAAA,QACxB;AACE,iBAAO;MACX;AAAA,IAAA,CACD;AAGK,UAAA,YAAY,CAAC,UAAkB;AACnC,UAAI,WAAW,UAAU;AAAO;AAEhC,iBAAW,QAAQ;AACnB,cAAQ,QAAQ;AAChB,cAAQ,QAAQ;AAGZ,UAAA,kBAAkB,MAAM,WAAW,GAAG;AACjC;MACT;AAAA,IAAA;AAIF,UAAM,SAAS,MAAM;AACnB,gBAAU,QAAQ;AAGlB,iBAAW,MAAM;AAKf,kBAAU,QAAQ;AAGd,YAAA,QAAQ,SAAS,GAAG;AACtB,kBAAQ,QAAQ;AAAA,QAClB;AAAA,SACC,GAAI;AAAA,IAAA;AAIT,UAAM,eAAe,MAAM;AACrB,UAAA,CAAC,QAAQ,MAAM,KAAK;AAAG;AAG3B,wBAAkB,QAAQ,KAAK;AAG/B,kBAAY,QAAQ;AAGpB,cAAQ,QAAQ;AAChB,cAAQ,QAAQ;AAGZ,UAAA,WAAW,UAAU,GAAG;AAC1B,wBAAgB,QAAQ;MAAC,WAChB,WAAW,UAAU,GAAG;AACjC,uBAAe,QAAQ;MAAC,WACf,WAAW,UAAU,GAAG;AACjC,uBAAe,QAAQ;MACzB;AAGO;IAAA;AAIT,UAAM,WAAW,MAAM;AACjB,UAAA,UAAU,SAAS,CAAC,QAAQ;AAAO;AAE/B,cAAA;AACD;IAAA;AAIT,UAAM,eAAe,MAAM;AACzB,cAAQ,QAAQ;AAChB,kBAAY,QAAQ;AAAA,IAAA;AAItB,UAAM,SAAS,MAAM;AACnBC,oBAAA,MAAI,aAAa;AAAA,IAAA;AAIb,UAAA,oBAAoB,CAAC,QAAgB;AAEzC,YAAM,QAAQ,cAAc,MAAM,QAAQ,GAAG;AAC7C,UAAI,QAAQ,IAAI;AACA,sBAAA,MAAM,OAAO,OAAO,CAAC;AAAA,MACrC;AAGc,oBAAA,MAAM,QAAQ,GAAG;AAG3B,UAAA,cAAc,MAAM,SAAS,IAAI;AACnC,sBAAc,MAAM;MACtB;AAGAA,oBAAA,MAAI,eAAe,iBAAiB,KAAK,UAAU,cAAc,KAAK,CAAC;AAAA,IAAA;AAInE,UAAA,iBAAiB,CAAC,SAAiB;AACvC,cAAQ,QAAQ;AACH;IAAA;AAIf,UAAM,eAAe,MAAM;AACzBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,0BAAc,QAAQ;AACtBA,gCAAI,kBAAkB,eAAe;AAAA,UACvC;AAAA,QACF;AAAA,MAAA,CACD;AAAA,IAAA;AAIG,UAAA,aAAa,CAAC,IAAY,SAAiB;AAC/C,UAAI,MAAM;AAEV,cAAQ,MAAM;AAAA,QACZ,KAAK;AACH,gBAAM,6BAA6B,EAAE;AACrC;AAAA,QACF,KAAK;AACH,gBAAM,4BAA4B,EAAE;AACpC;AAAA,QACF,KAAK;AACH,gBAAM,4BAA4B,EAAE;AACpC;AAAA,MACJ;AAEA,UAAI,KAAK;AACHA,sBAAAA,MAAA,WAAW,EAAE,IAAA,CAAK;AAAA,MACxB;AAAA,IAAA;AAIF,UAAM,mBAAmB,MAAM;AACzB,UAAA;AACI,cAAA,UAAUA,cAAAA,MAAI,eAAe,eAAe;AAClD,YAAI,SAAS;AACG,wBAAA,QAAQ,KAAK,MAAM,OAAO;AAAA,QAC1C;AAAA,eACO,GAAG;AACVA,sBAAA,MAAI,MAAM,SAAQ,+DAA8D,YAAY,CAAC;AAAA,MAC/F;AAAA,IAAA;AAIIC,wBAAA,SAAS,CAAC,WAAW;AACzB,UAAI,CAAC,QAAQ;AACX,oBAAY,QAAQ;AAAA,MACtB;AAAA,IAAA,CACD;AAEDC,kBAAAA,UAAU,MAAM;AACG;IAAA,CAClB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrcD,GAAG,WAAW,eAAe;"}