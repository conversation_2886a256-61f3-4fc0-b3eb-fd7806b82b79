<base-info-card wx:if="{{E}}" class="data-v-ea7d2210" u-s="{{['content']}}" u-i="ea7d2210-0" bind:__l="__l" u-p="{{E}}"><view class="job-details data-v-ea7d2210" slot="content"><view wx:if="{{a}}" class="company-info data-v-ea7d2210"><view wx:if="{{b}}" class="company-logo data-v-ea7d2210"><image src="{{c}}" class="company-logo-img data-v-ea7d2210" mode="aspectFill"></image></view><view class="company-data data-v-ea7d2210"><view class="company-name data-v-ea7d2210">{{d}}</view><view class="company-meta data-v-ea7d2210"><text wx:if="{{e}}" class="company-size data-v-ea7d2210">{{f}}</text><text wx:if="{{g}}" class="company-type data-v-ea7d2210">{{h}}</text></view></view><view wx:if="{{i}}" class="company-auth data-v-ea7d2210"><svg wx:if="{{l}}" class="data-v-ea7d2210" u-s="{{['d']}}" u-i="ea7d2210-1,ea7d2210-0" bind:__l="__l" u-p="{{l}}"><path wx:if="{{j}}" class="data-v-ea7d2210" u-i="ea7d2210-2,ea7d2210-1" bind:__l="__l" u-p="{{j}}"></path><polyline wx:if="{{k}}" class="data-v-ea7d2210" u-i="ea7d2210-3,ea7d2210-1" bind:__l="__l" u-p="{{k}}"></polyline></svg><text class="auth-text data-v-ea7d2210">已认证</text></view></view><view class="job-requirements data-v-ea7d2210"><view wx:if="{{m}}" class="req-item data-v-ea7d2210"><view class="req-icon education-icon data-v-ea7d2210"><svg wx:if="{{p}}" class="data-v-ea7d2210" u-s="{{['d']}}" u-i="ea7d2210-4,ea7d2210-0" bind:__l="__l" u-p="{{p}}"><path wx:if="{{n}}" class="data-v-ea7d2210" u-i="ea7d2210-5,ea7d2210-4" bind:__l="__l" u-p="{{n}}"></path><path wx:if="{{o}}" class="data-v-ea7d2210" u-i="ea7d2210-6,ea7d2210-4" bind:__l="__l" u-p="{{o}}"></path></svg></view><text class="req-text data-v-ea7d2210">{{q}}</text></view><view wx:if="{{r}}" class="req-item data-v-ea7d2210"><view class="req-icon experience-icon data-v-ea7d2210"><svg wx:if="{{v}}" class="data-v-ea7d2210" u-s="{{['d']}}" u-i="ea7d2210-7,ea7d2210-0" bind:__l="__l" u-p="{{v}}"><rect wx:if="{{s}}" class="data-v-ea7d2210" u-i="ea7d2210-8,ea7d2210-7" bind:__l="__l" u-p="{{s}}"></rect><path wx:if="{{t}}" class="data-v-ea7d2210" u-i="ea7d2210-9,ea7d2210-7" bind:__l="__l" u-p="{{t}}"></path></svg></view><text class="req-text data-v-ea7d2210">{{w}}</text></view><view wx:if="{{x}}" class="req-item data-v-ea7d2210"><view class="req-icon jobtype-icon data-v-ea7d2210"><svg wx:if="{{A}}" class="data-v-ea7d2210" u-s="{{['d']}}" u-i="ea7d2210-10,ea7d2210-0" bind:__l="__l" u-p="{{A}}"><circle wx:if="{{y}}" class="data-v-ea7d2210" u-i="ea7d2210-11,ea7d2210-10" bind:__l="__l" u-p="{{y}}"></circle><polyline wx:if="{{z}}" class="data-v-ea7d2210" u-i="ea7d2210-12,ea7d2210-10" bind:__l="__l" u-p="{{z}}"></polyline></svg></view><text class="req-text data-v-ea7d2210">{{B}}</text></view></view><view wx:if="{{C}}" class="job-benefits data-v-ea7d2210"><view wx:for="{{D}}" wx:for-item="benefit" wx:key="b" class="benefit-tag data-v-ea7d2210"><text class="benefit-text data-v-ea7d2210">{{benefit.a}}</text></view></view></view></base-info-card>