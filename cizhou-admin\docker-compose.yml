version: '3.8'

services:
  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost:8080
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - gateway
    networks:
      - cizhou-network

  # API网关
  gateway:
    build:
      context: ./backend/cizhou-gateway
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - NACOS_SERVER_ADDR=nacos:8848
    depends_on:
      - nacos
      - redis
    networks:
      - cizhou-network

  # 用户服务
  user-service:
    build:
      context: ./backend/cizhou-user-service
      dockerfile: Dockerfile
    ports:
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - NACOS_SERVER_ADDR=nacos:8848
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
      - nacos
    networks:
      - cizhou-network

  # 商家服务
  merchant-service:
    build:
      context: ./backend/cizhou-merchant-service
      dockerfile: Dockerfile
    ports:
      - "8082:8082"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - NACOS_SERVER_ADDR=nacos:8848
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
      - nacos
    networks:
      - cizhou-network

  # 内容服务
  content-service:
    build:
      context: ./backend/cizhou-content-service
      dockerfile: Dockerfile
    ports:
      - "8083:8083"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - NACOS_SERVER_ADDR=nacos:8848
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
      - MONGODB_HOST=mongodb
    depends_on:
      - mysql
      - redis
      - mongodb
      - nacos
    networks:
      - cizhou-network

  # 订单服务
  order-service:
    build:
      context: ./backend/cizhou-order-service
      dockerfile: Dockerfile
    ports:
      - "8084:8084"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - NACOS_SERVER_ADDR=nacos:8848
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
      - RABBITMQ_HOST=rabbitmq
    depends_on:
      - mysql
      - redis
      - rabbitmq
      - nacos
    networks:
      - cizhou-network

  # MySQL数据库
  mysql:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=cizhou123456
      - MYSQL_DATABASE=cizhou_admin
      - MYSQL_USER=cizhou
      - MYSQL_PASSWORD=cizhou123456
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - cizhou-network

  # Redis缓存
  redis:
    image: redis:7.0-alpine
    ports:
      - "6379:6379"
    command: redis-server --requirepass cizhou123456
    volumes:
      - redis_data:/data
    networks:
      - cizhou-network

  # MongoDB文档数据库
  mongodb:
    image: mongo:7.0
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=cizhou
      - MONGO_INITDB_ROOT_PASSWORD=cizhou123456
      - MONGO_INITDB_DATABASE=cizhou_logs
    volumes:
      - mongodb_data:/data/db
    networks:
      - cizhou-network

  # RabbitMQ消息队列
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      - RABBITMQ_DEFAULT_USER=cizhou
      - RABBITMQ_DEFAULT_PASS=cizhou123456
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - cizhou-network

  # Nacos服务注册中心
  nacos:
    image: nacos/nacos-server:v2.3.0
    ports:
      - "8848:8848"
      - "9848:9848"
    environment:
      - MODE=standalone
      - SPRING_DATASOURCE_PLATFORM=mysql
      - MYSQL_SERVICE_HOST=mysql
      - MYSQL_SERVICE_DB_NAME=nacos
      - MYSQL_SERVICE_USER=cizhou
      - MYSQL_SERVICE_PASSWORD=cizhou123456
    depends_on:
      - mysql
    volumes:
      - nacos_data:/home/<USER>/data
    networks:
      - cizhou-network

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    networks:
      - cizhou-network

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=cizhou123456
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - cizhou-network

volumes:
  mysql_data:
  redis_data:
  mongodb_data:
  rabbitmq_data:
  nacos_data:
  prometheus_data:
  grafana_data:

networks:
  cizhou-network:
    driver: bridge
