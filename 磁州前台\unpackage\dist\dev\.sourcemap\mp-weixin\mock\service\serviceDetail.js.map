{"version": 3, "file": "serviceDetail.js", "sources": ["mock/service/serviceDetail.js"], "sourcesContent": ["// 服务详情模拟数据\r\nexport const serviceDetail = {\r\n  id: 'service-001',\r\n  title: '家电维修',\r\n  icon: '/static/images/service/repair.png',\r\n  images: [\r\n    '/static/images/service/repair-1.jpg',\r\n    '/static/images/service/repair-2.jpg',\r\n    '/static/images/service/repair-3.jpg',\r\n    '/static/images/service/repair-4.jpg'\r\n  ],\r\n  description: '提供各类家电维修服务，包括空调、冰箱、洗衣机、电视等。我们的技师均有5年以上维修经验，持证上岗，服务范围覆盖磁县全境。',\r\n  price: '上门费30元起',\r\n  priceDetails: [\r\n    { name: '上门检测费', price: 30, unit: '次' },\r\n    { name: '小型家电维修', price: 50, unit: '次起' },\r\n    { name: '中型家电维修', price: 80, unit: '次起' },\r\n    { name: '大型家电维修', price: 120, unit: '次起' }\r\n  ],\r\n  rating: 4.8,\r\n  orderCount: 1256,\r\n  category: '家居服务',\r\n  tags: ['上门服务', '快速响应', '专业维修'],\r\n  serviceTime: '8:00-21:00',\r\n  serviceArea: '磁县城区及周边5公里范围',\r\n  provider: {\r\n    id: 'provider-001',\r\n    name: '磁县家电维修中心',\r\n    logo: '/static/images/service/provider-logo.png',\r\n    address: '磁县城区幸福路456号',\r\n    phone: '0310-12345678',\r\n    certification: ['营业执照', '服务资质证书'],\r\n    establishTime: '2015-05',\r\n    employeeCount: 15,\r\n    introduction: '磁县家电维修中心成立于2015年，是磁县规模最大、服务最专业的家电维修机构。我们拥有一支技术精湛、经验丰富的维修团队，致力于为磁县居民提供高质量的家电维修服务。'\r\n  },\r\n  serviceProcess: [\r\n    { step: 1, title: '预约下单', description: '在线选择服务项目，填写地址和预约时间' },\r\n    { step: 2, title: '技师上门', description: '技师按预约时间上门，进行故障检测' },\r\n    { step: 3, title: '报价确认', description: '技师检测后报价，客户确认后进行维修' },\r\n    { step: 4, title: '维修完成', description: '维修完成后，客户验收并支付费用' },\r\n    { step: 5, title: '售后保障', description: '维修后30天内免费保修' }\r\n  ],\r\n  serviceGuarantee: [\r\n    { title: '专业技师', description: '所有技师均持证上岗，有5年以上维修经验' },\r\n    { title: '品质保障', description: '使用原厂配件，30天内免费保修' },\r\n    { title: '准时上门', description: '预约后准时上门，超时赔付' },\r\n    { title: '明码标价', description: '检测后明确报价，不存在隐形消费' }\r\n  ],\r\n  faq: [\r\n    {\r\n      question: '如何预约上门维修服务？',\r\n      answer: '您可以通过我们的小程序预约，也可以直接拨打服务热线0310-12345678进行预约。'\r\n    },\r\n    {\r\n      question: '维修费用如何收取？',\r\n      answer: '我们收取30元上门检测费，维修费用根据故障类型和配件更换情况而定，技师会在检测后给出明确报价，您确认后我们才会进行维修。'\r\n    },\r\n    {\r\n      question: '维修后有保修吗？',\r\n      answer: '我们提供30天的免费保修服务，如果在保修期内出现同样的故障，我们将免费上门维修。'\r\n    },\r\n    {\r\n      question: '可以提供发票吗？',\r\n      answer: '可以，我们可以提供正规发票，请在下单时备注需要开具发票。'\r\n    }\r\n  ],\r\n  reviews: [\r\n    {\r\n      id: 'review-001',\r\n      user: {\r\n        id: 'user-001',\r\n        nickname: '磁州居民',\r\n        avatar: '/static/images/tabbar/user-blue.png'\r\n      },\r\n      rating: 5,\r\n      content: '服务很专业，技师很有经验，很快就解决了冰箱不制冷的问题，价格也合理，非常满意！',\r\n      time: '2024-03-15 14:30',\r\n      images: [\r\n        '/static/images/service/review-1.jpg',\r\n        '/static/images/service/review-2.jpg'\r\n      ],\r\n      likes: 12\r\n    },\r\n    {\r\n      id: 'review-002',\r\n      user: {\r\n        id: 'user-002',\r\n        nickname: '老磁州',\r\n        avatar: '/static/images/tabbar/user-blue.png'\r\n      },\r\n      rating: 4,\r\n      content: '师傅准时上门，态度很好，空调漏水问题解决得很彻底，就是价格稍微有点贵。',\r\n      time: '2024-03-14 10:15',\r\n      images: [],\r\n      likes: 8\r\n    },\r\n    {\r\n      id: 'review-003',\r\n      user: {\r\n        id: 'user-003',\r\n        nickname: '城市生活家',\r\n        avatar: '/static/images/tabbar/user-blue.png'\r\n      },\r\n      rating: 5,\r\n      content: '第二次找他们维修了，服务一如既往的好，洗衣机不排水的问题很快就解决了，而且没有乱收费，良心商家！',\r\n      time: '2024-03-13 16:45',\r\n      images: [\r\n        '/static/images/service/review-3.jpg'\r\n      ],\r\n      likes: 15\r\n    }\r\n  ],\r\n  publishTime: '2024-01-01 09:00:00',\r\n  updateTime: '2024-03-01 10:00:00',\r\n  views: 3256,\r\n  collections: 145,\r\n  shares: 87,\r\n  isCollected: false\r\n};\r\n\r\n// 相关服务模拟数据\r\nexport const relatedServices = [\r\n  {\r\n    id: 'service-002',\r\n    title: '保洁服务',\r\n    icon: '/static/images/service/cleaning.png',\r\n    description: '提供专业的家庭保洁、办公室保洁、开荒保洁等服务',\r\n    price: '50元/小时起',\r\n    rating: 4.7,\r\n    orderCount: 986,\r\n    category: '家居服务',\r\n    tags: ['专业保洁', '定期保洁', '深度清洁']\r\n  },\r\n  {\r\n    id: 'service-003',\r\n    title: '搬家服务',\r\n    icon: '/static/images/service/moving.png',\r\n    description: '提供专业的居民搬家、企业搬迁、小型搬运等服务',\r\n    price: '200元/次起',\r\n    rating: 4.6,\r\n    orderCount: 758,\r\n    category: '家居服务',\r\n    tags: ['专业搬运', '包装服务', '全程保险']\r\n  },\r\n  {\r\n    id: 'service-004',\r\n    title: '管道疏通',\r\n    icon: '/static/images/service/plumbing.png',\r\n    description: '提供厨房、卫生间等各类管道疏通服务',\r\n    price: '80元/次起',\r\n    rating: 4.5,\r\n    orderCount: 632,\r\n    category: '家居服务',\r\n    tags: ['快速响应', '彻底疏通', '免费复查']\r\n  }\r\n];\r\n\r\n// 获取服务详情的API函数\r\nexport const fetchServiceDetail = (id) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve(serviceDetail);\r\n    }, 500);\r\n  });\r\n};\r\n\r\n// 获取相关服务的API函数\r\nexport const fetchRelatedServices = () => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve(relatedServices);\r\n    }, 500);\r\n  });\r\n};\r\n\r\n// 获取更多评价的API函数\r\nexport const fetchMoreReviews = (serviceId, page = 2) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      const moreReviews = [\r\n        {\r\n          id: 'review-004',\r\n          user: {\r\n            id: 'user-004',\r\n            nickname: '家电爱好者',\r\n            avatar: '/static/images/tabbar/user-blue.png'\r\n          },\r\n          rating: 5,\r\n          content: '电视突然不亮了，联系了他们，当天就上门维修好了，而且费用比我想象的便宜，非常感谢！',\r\n          time: '2024-03-12 11:20',\r\n          images: [],\r\n          likes: 7\r\n        },\r\n        {\r\n          id: 'review-005',\r\n          user: {\r\n            id: 'user-005',\r\n            nickname: '磁县新居民',\r\n            avatar: '/static/images/tabbar/user-blue.png'\r\n          },\r\n          rating: 4,\r\n          content: '师傅技术不错，但是上门时间晚了半小时，希望能改进一下。',\r\n          time: '2024-03-11 15:30',\r\n          images: [],\r\n          likes: 3\r\n        }\r\n      ];\r\n      \r\n      resolve({\r\n        list: moreReviews,\r\n        hasMore: false\r\n      });\r\n    }, 800);\r\n  });\r\n};\r\n\r\n// 收藏服务的API函数\r\nexport const collectService = (serviceId) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve({\r\n        success: true,\r\n        message: '收藏成功',\r\n        data: {\r\n          isCollected: true,\r\n          collections: serviceDetail.collections + 1\r\n        }\r\n      });\r\n    }, 300);\r\n  });\r\n};\r\n\r\n// 取消收藏服务的API函数\r\nexport const cancelCollectService = (serviceId) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve({\r\n        success: true,\r\n        message: '已取消收藏',\r\n        data: {\r\n          isCollected: false,\r\n          collections: serviceDetail.collections\r\n        }\r\n      });\r\n    }, 300);\r\n  });\r\n}; "], "names": [], "mappings": ";AACY,MAAC,gBAAgB;AAAA,EAC3B,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA,EACD,aAAa;AAAA,EACb,OAAO;AAAA,EACP,cAAc;AAAA,IACZ,EAAE,MAAM,SAAS,OAAO,IAAI,MAAM,IAAK;AAAA,IACvC,EAAE,MAAM,UAAU,OAAO,IAAI,MAAM,KAAM;AAAA,IACzC,EAAE,MAAM,UAAU,OAAO,IAAI,MAAM,KAAM;AAAA,IACzC,EAAE,MAAM,UAAU,OAAO,KAAK,MAAM,KAAM;AAAA,EAC3C;AAAA,EACD,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,EAC7B,aAAa;AAAA,EACb,aAAa;AAAA,EACb,UAAU;AAAA,IACR,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,IACP,eAAe,CAAC,QAAQ,QAAQ;AAAA,IAChC,eAAe;AAAA,IACf,eAAe;AAAA,IACf,cAAc;AAAA,EACf;AAAA,EACD,gBAAgB;AAAA,IACd,EAAE,MAAM,GAAG,OAAO,QAAQ,aAAa,qBAAsB;AAAA,IAC7D,EAAE,MAAM,GAAG,OAAO,QAAQ,aAAa,mBAAoB;AAAA,IAC3D,EAAE,MAAM,GAAG,OAAO,QAAQ,aAAa,oBAAqB;AAAA,IAC5D,EAAE,MAAM,GAAG,OAAO,QAAQ,aAAa,kBAAmB;AAAA,IAC1D,EAAE,MAAM,GAAG,OAAO,QAAQ,aAAa,cAAe;AAAA,EACvD;AAAA,EACD,kBAAkB;AAAA,IAChB,EAAE,OAAO,QAAQ,aAAa,sBAAuB;AAAA,IACrD,EAAE,OAAO,QAAQ,aAAa,kBAAmB;AAAA,IACjD,EAAE,OAAO,QAAQ,aAAa,eAAgB;AAAA,IAC9C,EAAE,OAAO,QAAQ,aAAa,kBAAmB;AAAA,EAClD;AAAA,EACD,KAAK;AAAA,IACH;AAAA,MACE,UAAU;AAAA,MACV,QAAQ;AAAA,IACT;AAAA,IACD;AAAA,MACE,UAAU;AAAA,MACV,QAAQ;AAAA,IACT;AAAA,IACD;AAAA,MACE,UAAU;AAAA,MACV,QAAQ;AAAA,IACT;AAAA,IACD;AAAA,MACE,UAAU;AAAA,MACV,QAAQ;AAAA,IACT;AAAA,EACF;AAAA,EACD,SAAS;AAAA,IACP;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,QACJ,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,QAAQ;AAAA,MACT;AAAA,MACD,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACD;AAAA,MACD,OAAO;AAAA,IACR;AAAA,IACD;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,QACJ,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,QAAQ;AAAA,MACT;AAAA,MACD,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ,CAAE;AAAA,MACV,OAAO;AAAA,IACR;AAAA,IACD;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,QACJ,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,QAAQ;AAAA,MACT;AAAA,MACD,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,QACN;AAAA,MACD;AAAA,MACD,OAAO;AAAA,IACR;AAAA,EACF;AAAA,EACD,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,aAAa;AACf;AAGY,MAAC,kBAAkB;AAAA,EAC7B;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,IACb,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,EAC9B;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,IACb,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,EAC9B;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,IACb,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,EAC9B;AACH;AAGY,MAAC,qBAAqB,CAAC,OAAO;AACxC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,cAAQ,aAAa;AAAA,IACtB,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,uBAAuB,MAAM;AACxC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,cAAQ,eAAe;AAAA,IACxB,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,mBAAmB,CAAC,WAAW,OAAO,MAAM;AACvD,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,YAAM,cAAc;AAAA,QAClB;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,YACJ,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,QAAQ;AAAA,UACT;AAAA,UACD,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,MAAM;AAAA,UACN,QAAQ,CAAE;AAAA,UACV,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,YACJ,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,QAAQ;AAAA,UACT;AAAA,UACD,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,MAAM;AAAA,UACN,QAAQ,CAAE;AAAA,UACV,OAAO;AAAA,QACR;AAAA,MACT;AAEM,cAAQ;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACjB,CAAO;AAAA,IACF,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,iBAAiB,CAAC,cAAc;AAC3C,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,cAAQ;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,QACT,MAAM;AAAA,UACJ,aAAa;AAAA,UACb,aAAa,cAAc,cAAc;AAAA,QAC1C;AAAA,MACT,CAAO;AAAA,IACF,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,uBAAuB,CAAC,cAAc;AACjD,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,cAAQ;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,QACT,MAAM;AAAA,UACJ,aAAa;AAAA,UACb,aAAa,cAAc;AAAA,QAC5B;AAAA,MACT,CAAO;AAAA,IACF,GAAE,GAAG;AAAA,EACV,CAAG;AACH;;;;;;;;"}