<template>
  <view class="flash-sale-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <image class="back-icon" src="/static/images/tabbar/最新返回键.png" mode="aspectFit"></image>
        </view>
        <view class="navbar-title">限时秒杀</view>
        <view class="navbar-right">
          <view class="close-btn" @click="goBack">
            <svg class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="22" height="22">
              <path d="M512 421.490332L331.349941 240.840273c-24.988383-24.988383-65.35828-24.988383-90.346664 0-24.988383 24.988383-24.988383 65.35828 0 90.346664L421.653336 512 240.840273 692.812059c-24.988383 24.988383-24.988383 65.35828 0 90.346664 24.988383 24.988383 65.35828 24.988383 90.346664 0L512 602.509668l180.650059 180.650059c24.988383 24.988383 65.35828 24.988383 90.346664 0 24.988383-24.988383 24.988383-65.35828 0-90.346664L602.346664 512l180.813063-180.812059c24.988383-24.988383 24.988383-65.35828 0-90.346664-24.988383-24.988383-65.35828-24.988383-90.346664 0L512 421.490332z" fill="#FFFFFF"></path>
            </svg>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 倒计时区域 -->
    <view class="countdown-section">
      <view class="countdown-header">
        <view class="current-round">
          <text class="round-label">{{ currentRound.label }}</text>
          <text class="round-status">{{ currentRound.status }}</text>
        </view>
        <view class="countdown-wrapper">
          <countdown-timer 
            :end-time="currentRound.endTime" 
            type="flash"
            @countdown-end="onCountdownEnd"
          ></countdown-timer>
        </view>
      </view>
      
      <!-- 场次选择 -->
      <scroll-view class="round-scroll" scroll-x show-scrollbar="false">
        <view class="round-list">
          <view 
            class="round-item" 
            v-for="(round, index) in rounds" 
            :key="index"
            :class="{ active: currentRoundIndex === index }"
            @click="switchRound(index)"
          >
            <text class="round-time">{{ round.time }}</text>
            <text class="round-status">{{ round.status }}</text>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 商品列表 -->
    <scroll-view class="content-scroll" scroll-y
      @scrolltolower="loadMore"
      @scroll="handleScroll">
      <view class="products-list">
        <view 
          class="product-item" 
          v-for="(product, index) in products" 
          :key="index"
          @click="navigateToDetail(product.id)"
        >
          <view class="product-image-container">
            <image class="product-image" :src="product.coverImage || product.image" mode="aspectFill"></image>
            <view class="product-tag" v-if="product.tag">{{ product.tag }}</view>
          </view>
          <view class="product-info">
            <view class="product-title">{{ product.name || product.title }}</view>
            <view class="product-desc">{{ product.description }}</view>
            <view class="product-price-row">
              <view class="current-price">
                <text class="price-symbol">¥</text>
                <text class="price-value">{{ product.flashPrice || product.currentPrice }}</text>
              </view>
              <view class="original-price">¥{{ product.originalPrice }}</view>
            </view>
            <view class="product-progress">
              <view class="progress-text">
                <text>已售{{ product.soldCount }}件</text>
                <text>共{{ product.stockTotal || product.totalCount }}件</text>
              </view>
              <view class="progress-bar">
                <view 
                  class="progress-filled" 
                  :style="{width: `${Math.min(100, (product.soldCount / (product.stockTotal || product.totalCount)) * 100)}%`}"
                ></view>
              </view>
            </view>
            <view class="product-btn">
              <text>立即抢购</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 加载更多提示 -->
      <view class="loading-more" v-if="loading">
        <text>加载中...</text>
      </view>
      
      <!-- 到底了提示 -->
      <view class="no-more" v-if="noMore">
        <text>已经到底啦~</text>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="products.length === 0 && !loading">
        <image class="empty-image" src="/static/images/empty-state.png" mode="aspectFit"></image>
        <text class="empty-text">暂无秒杀活动</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import CountdownTimer from '../../components/CountdownTimer.vue'
import { ActivityService, ActivityType } from '@/common/services/ActivityService.js'

export default {
  components: {
    CountdownTimer
  },
  data() {
    return {
      refreshing: false,
      loading: false,
      noMore: false,
      scrollTop: 0,
      currentRoundIndex: 0,
      rounds: [
        { 
          time: '08:00', 
          status: '已开抢', 
          label: '08:00场',
          endTime: new Date(new Date().setHours(10, 0, 0, 0)).toISOString()
        },
        { 
          time: '10:00', 
          status: '抢购中', 
          label: '10:00场',
          endTime: new Date(new Date().setHours(12, 0, 0, 0)).toISOString()
        },
        { 
          time: '12:00', 
          status: '即将开始', 
          label: '12:00场',
          endTime: new Date(new Date().setHours(14, 0, 0, 0)).toISOString()
        },
        { 
          time: '14:00', 
          status: '即将开始', 
          label: '14:00场',
          endTime: new Date(new Date().setHours(16, 0, 0, 0)).toISOString()
        },
        { 
          time: '16:00', 
          status: '即将开始', 
          label: '16:00场',
          endTime: new Date(new Date().setHours(18, 0, 0, 0)).toISOString()
        },
        { 
          time: '18:00', 
          status: '即将开始', 
          label: '18:00场',
          endTime: new Date(new Date().setHours(20, 0, 0, 0)).toISOString()
        },
        { 
          time: '20:00', 
          status: '即将开始', 
          label: '20:00场',
          endTime: new Date(new Date().setHours(22, 0, 0, 0)).toISOString()
        }
      ],
      products: [],
      page: 1,
      pageSize: 10
    }
  },
  computed: {
    currentRound() {
      return this.rounds[this.currentRoundIndex]
    }
  },
  onLoad() {
    // 页面加载时获取数据
    this.fetchData()
  },
  methods: {
    // 监听滚动事件
    handleScroll(e) {
      this.scrollTop = e.detail.scrollTop;
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },
    
    // 切换场次
    switchRound(index) {
      this.currentRoundIndex = index
      this.products = [] // 清空当前产品列表
      this.page = 1 // 重置页码
      this.noMore = false // 重置加载状态
      this.fetchData() // 获取新场次的数据
    },
    
    // 倒计时结束
    onCountdownEnd() {
      // 更新状态或获取下一场数据
      uni.showToast({
        title: '当前场次已结束',
        icon: 'none'
      })
      
      // 自动切换到下一场
      if (this.currentRoundIndex < this.rounds.length - 1) {
        this.currentRoundIndex++
        this.products = [] // 清空当前产品列表
        this.page = 1 // 重置页码
        this.noMore = false // 重置加载状态
        this.fetchData() // 获取新场次的数据
      }
    },
    
    // 加载更多
    loadMore() {
      if (this.loading || this.noMore) return
      
      this.loading = true
      this.page++
      
      // 获取更多数据
      this.fetchMoreData()
    },
    
    // 获取数据
    fetchData() {
      this.loading = true
      
      try {
        // 使用ActivityService获取秒杀活动数据
        const flashSaleActivities = ActivityService.getActivities({
          type: ActivityType.FLASH,
          onlyPublished: true,
          sortByEndTime: true,
          page: this.page,
          pageSize: this.pageSize
        });
        
        if (flashSaleActivities && flashSaleActivities.length > 0) {
          this.products = flashSaleActivities;
          this.loading = false;
        } else {
          // 如果没有数据，可以使用默认数据或显示空状态
          this.products = [];
          this.noMore = true;
          this.loading = false;
        }
      } catch (error) {
        console.error('获取秒杀活动数据失败:', error);
        this.loading = false;
        uni.showToast({
          title: '获取数据失败',
          icon: 'none'
        });
      }
    },
    
    // 获取更多数据
    fetchMoreData() {
      try {
        // 使用ActivityService获取更多秒杀活动数据
        const moreActivities = ActivityService.getActivities({
          type: ActivityType.FLASH,
          onlyPublished: true,
          sortByEndTime: true,
          page: this.page,
          pageSize: this.pageSize
        });
        
        if (moreActivities && moreActivities.length > 0) {
          this.products = [...this.products, ...moreActivities];
          this.loading = false;
        } else {
          // 没有更多数据
          this.noMore = true;
          this.loading = false;
        }
      } catch (error) {
        console.error('获取更多秒杀活动数据失败:', error);
        this.loading = false;
        uni.showToast({
          title: '获取更多数据失败',
          icon: 'none'
        });
      }
    },
    
    // 导航到详情页
    navigateToDetail(id) {
      uni.navigateTo({
        url: `/subPackages/activity-showcase/pages/flash-sale/detail?id=${id}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.flash-sale-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F2F2F7;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
  
  .navbar-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
  }
  
  .navbar-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding-top: var(--status-bar-height, 25px);
    padding-left: 30rpx;
    padding-right: 30rpx;
    box-sizing: border-box;
    
    .back-btn {
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .back-icon {
      width: 100%;
      height: 100%;
    }
    
    .navbar-title {
      font-size: 18px;
      font-weight: 600;
      color: #FFFFFF;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
    }
    
    .navbar-right {
      width: 80rpx;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      
      .close-btn {
        width: 64rpx;
        height: 64rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

/* 倒计时区域 */
.countdown-section {
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
  background-color: #FFFFFF;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
  
  .countdown-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .current-round {
      .round-label {
        font-size: 32rpx;
        font-weight: 600;
        color: #333333;
        margin-right: 12rpx;
      }
      
      .round-status {
        font-size: 24rpx;
        color: #FF3B30;
        background-color: rgba(255, 59, 48, 0.1);
        padding: 4rpx 12rpx;
        border-radius: 12rpx;
      }
    }
    
    .countdown-wrapper {
      // 样式由组件自身提供
    }
  }
  
  .round-scroll {
    width: 100%;
    white-space: nowrap;
    
    .round-list {
      display: inline-flex;
      padding: 10rpx 0;
      
      .round-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 16rpx 30rpx;
        margin-right: 20rpx;
        border-radius: 16rpx;
        background-color: #F2F2F7;
        
        &.active {
          background: linear-gradient(135deg, #FF5A5F 0%, #FF3B30 100%);
          
          .round-time, .round-status {
            color: #FFFFFF;
          }
        }
        
        .round-time {
          font-size: 28rpx;
          font-weight: 600;
          color: #333333;
          margin-bottom: 6rpx;
        }
        
        .round-status {
          font-size: 22rpx;
          color: #666666;
        }
      }
    }
  }
}

/* 商品列表 */
.content-scroll {
  flex: 1;
  width: 100%;
}

.products-list {
  padding: 20rpx;
}

.product-item {
  display: flex;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
  
  .product-image-container {
    position: relative;
    width: 200rpx;
    height: 200rpx;
    margin-right: 20rpx;
    
    .product-image {
      width: 100%;
      height: 100%;
      border-radius: 12rpx;
    }
    
    .product-tag {
      position: absolute;
      top: 10rpx;
      left: 10rpx;
      padding: 4rpx 12rpx;
      font-size: 20rpx;
      color: #FFFFFF;
      border-radius: 10rpx;
      background: linear-gradient(135deg, #FF5A5F 0%, #FF3B30 100%);
    }
  }
  
  .product-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    .product-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333333;
      margin-bottom: 8rpx;
    }
    
    .product-desc {
      font-size: 24rpx;
      color: #666666;
      margin-bottom: 12rpx;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    .product-price-row {
      display: flex;
      align-items: baseline;
      margin-bottom: 12rpx;
      
      .current-price {
        color: #FF3B30;
        font-weight: 600;
        
        .price-symbol {
          font-size: 24rpx;
        }
        
        .price-value {
          font-size: 32rpx;
        }
      }
      
      .original-price {
        margin-left: 12rpx;
        font-size: 24rpx;
        color: #999999;
        text-decoration: line-through;
      }
    }
    
    .product-progress {
      margin-bottom: 16rpx;
      
      .progress-text {
        display: flex;
        justify-content: space-between;
        font-size: 22rpx;
        color: #666666;
        margin-bottom: 8rpx;
      }
      
      .progress-bar {
        height: 8rpx;
        background-color: #F2F2F7;
        border-radius: 4rpx;
        overflow: hidden;
        
        .progress-filled {
          height: 100%;
          background: linear-gradient(90deg, #FF9500 0%, #FF3B30 100%);
          border-radius: 4rpx;
        }
      }
    }
    
    .product-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 60rpx;
      border-radius: 30rpx;
      background: linear-gradient(135deg, #FF5A5F 0%, #FF3B30 100%);
      font-size: 26rpx;
      font-weight: 500;
      color: #FFFFFF;
    }
  }
}

/* 加载更多和到底了提示 */
.loading-more, .no-more {
  text-align: center;
  padding: 20rpx 0;
  font-size: 24rpx;
  color: #999999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  
  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 20rpx;
  }
  
  .empty-text {
    font-size: 28rpx;
    color: #999999;
  }
}
</style> 