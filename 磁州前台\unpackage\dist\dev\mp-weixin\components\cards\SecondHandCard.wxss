
.secondhand-details.data-v-a3a617cd {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(60, 60, 67, 0.1);
}

/* 商品基本信息 */
.product-basic-info.data-v-a3a617cd {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 16rpx;
}
.product-condition.data-v-a3a617cd, .product-brand.data-v-a3a617cd, .product-age.data-v-a3a617cd, .product-transaction.data-v-a3a617cd {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.02);
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  box-shadow: inset 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
}
.product-icon.data-v-a3a617cd {
  margin-right: 8rpx;
  color: #8e8e93;
  display: flex;
  align-items: center;
  justify-content: center;
}
.condition-icon.data-v-a3a617cd {
  color: #34C759;
}
.brand-icon.data-v-a3a617cd {
  color: #FF9500;
}
.age-icon.data-v-a3a617cd {
  color: #5856D6;
}
.transaction-icon.data-v-a3a617cd {
  color: #FF3B30;
}
.product-text.data-v-a3a617cd {
  font-size: 24rpx;
  color: #636366;
  font-weight: 500;
}

/* 价格对比 */
.price-comparison.data-v-a3a617cd {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
  background: rgba(0, 0, 0, 0.02);
  padding: 12rpx 16rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.price-tag.data-v-a3a617cd {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.price-label.data-v-a3a617cd {
  font-size: 20rpx;
  color: #8e8e93;
  margin-bottom: 4rpx;
}
.price-value.data-v-a3a617cd {
  font-size: 26rpx;
  font-weight: 600;
}
.original-value.data-v-a3a617cd {
  color: #8e8e93;
  text-decoration: line-through;
}
.current-value.data-v-a3a617cd {
  color: #FF3B30;
  font-size: 30rpx;
}
.discount-rate.data-v-a3a617cd {
  margin-left: auto;
  background: linear-gradient(135deg, #FF3B30, #FF9500);
  border-radius: 16rpx;
  padding: 8rpx 16rpx;
}
.discount-value.data-v-a3a617cd {
  color: #FFFFFF;
  font-size: 24rpx;
  font-weight: 700;
}

/* 商品描述 */
.product-description.data-v-a3a617cd {
  margin-bottom: 16rpx;
  padding: 16rpx;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 16rpx;
  box-shadow: inset 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
}
.description-text.data-v-a3a617cd {
  font-size: 26rpx;
  color: #636366;
  line-height: 1.5;
}

/* 商品标签 */
.product-tags.data-v-a3a617cd {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 16rpx;
}
.product-tag.data-v-a3a617cd {
  background: linear-gradient(135deg, rgba(88, 86, 214, 0.1), rgba(0, 122, 255, 0.1));
  border-radius: 16rpx;
  padding: 6rpx 16rpx;
  border: 1rpx solid rgba(88, 86, 214, 0.2);
}
.tag-text.data-v-a3a617cd {
  font-size: 22rpx;
  color: #5856D6;
  font-weight: 500;
}

/* 交易方式 */
.transaction-methods.data-v-a3a617cd {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}
.transaction-method.data-v-a3a617cd {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.02);
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
}
.method-icon.data-v-a3a617cd {
  margin-right: 6rpx;
  color: #8e8e93;
  display: flex;
  align-items: center;
  justify-content: center;
}
.pickup-icon.data-v-a3a617cd {
  color: #34C759;
}
.delivery-icon.data-v-a3a617cd {
  color: #007AFF;
}
.local-icon.data-v-a3a617cd {
  color: #5856D6;
}
.face-icon.data-v-a3a617cd {
  color: #FF9500;
}
.negotiate-icon.data-v-a3a617cd {
  color: #FF3B30;
}
.method-text.data-v-a3a617cd {
  font-size: 22rpx;
  color: #636366;
}

/* 车辆特殊信息 */
.vehicle-info.data-v-a3a617cd {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(60, 60, 67, 0.1);
}
.vehicle-specs.data-v-a3a617cd {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 16rpx;
}
.vehicle-spec.data-v-a3a617cd {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(0, 0, 0, 0.02);
  padding: 12rpx 20rpx;
  border-radius: 16rpx;
  flex: 1;
  min-width: 120rpx;
}
.spec-label.data-v-a3a617cd {
  font-size: 20rpx;
  color: #8e8e93;
  margin-bottom: 6rpx;
}
.spec-value.data-v-a3a617cd {
  font-size: 26rpx;
  color: #1c1c1e;
  font-weight: 600;
}
.mileage-value.data-v-a3a617cd {
  color: #FF9500;
}
.year-value.data-v-a3a617cd {
  color: #5856D6;
}
.license-value.data-v-a3a617cd {
  color: #007AFF;
}
