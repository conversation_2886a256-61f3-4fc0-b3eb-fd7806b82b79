"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const CreateButton = () => "../../../components/CreateButton.js";
const _sfc_main = {
  components: {
    CreateButton
  },
  setup() {
    const statistics = common_vendor.reactive({
      total: 8,
      active: 3,
      orders: 546,
      discount: "12,856.50"
    });
    const discounts = common_vendor.ref([
      {
        id: 1,
        title: "春季促销活动",
        status: "active",
        statusText: "进行中",
        rules: [
          { minAmount: 100, discountAmount: 10 },
          { minAmount: 200, discountAmount: 25 },
          { minAmount: 300, discountAmount: 50 }
        ],
        timeRange: "2023-04-01 ~ 2023-04-30",
        usageCount: 352,
        totalDiscount: "8,562.50"
      },
      {
        id: 2,
        title: "周末特惠",
        status: "active",
        statusText: "进行中",
        rules: [
          { minAmount: 150, discountAmount: 15 },
          { minAmount: 300, discountAmount: 40 }
        ],
        timeRange: "每周五至周日",
        usageCount: 126,
        totalDiscount: "3,240.00"
      },
      {
        id: 3,
        title: "五一假期特惠",
        status: "upcoming",
        statusText: "未开始",
        rules: [
          { minAmount: 200, discountAmount: 30 },
          { minAmount: 400, discountAmount: 70 },
          { minAmount: 600, discountAmount: 120 }
        ],
        timeRange: "2023-05-01 ~ 2023-05-03",
        usageCount: 0,
        totalDiscount: "0.00"
      },
      {
        id: 4,
        title: "清仓特惠",
        status: "expired",
        statusText: "已结束",
        rules: [
          { minAmount: 100, discountAmount: 20 },
          { minAmount: 300, discountAmount: 60 }
        ],
        timeRange: "2023-03-15 ~ 2023-03-31",
        usageCount: 68,
        totalDiscount: "1,054.00"
      }
    ]);
    const searchKeyword = common_vendor.ref("");
    const hoveredDiscount = common_vendor.ref(null);
    const displayDiscounts = common_vendor.computed(() => {
      if (!searchKeyword.value) {
        return discounts.value;
      }
      const keyword = searchKeyword.value.toLowerCase();
      return discounts.value.filter((discount) => {
        return discount.title.toLowerCase().includes(keyword);
      });
    });
    function goBack() {
      common_vendor.index.navigateBack();
    }
    function onSearch(e) {
    }
    function showFilterOptions() {
      common_vendor.index.showActionSheet({
        itemList: ["全部", "进行中", "未开始", "已结束"],
        success: (res) => {
        }
      });
    }
    function viewDiscountDetail(discount) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/discount/detail?id=${discount.id}`,
        animationType: "slide-in-right"
      });
    }
    function editDiscount(discount) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/discount/edit?id=${discount.id}`,
        animationType: "slide-in-right"
      });
    }
    function toggleDiscountStatus(discount) {
      const isActive = discount.status === "active";
      const newStatus = isActive ? "paused" : "active";
      const statusText = isActive ? "已暂停" : "进行中";
      discount.status = newStatus;
      discount.statusText = statusText;
      common_vendor.index.showToast({
        title: isActive ? "已暂停活动" : "已启用活动",
        icon: "success"
      });
    }
    function confirmDeleteDiscount(discount) {
      common_vendor.index.showModal({
        title: "确认删除",
        content: `确定要删除"${discount.title}"吗？此操作无法撤销。`,
        confirmColor: "#FF3B30",
        success: (res) => {
          if (res.confirm) {
            deleteDiscount(discount);
          }
        }
      });
    }
    function deleteDiscount(discount) {
      const index = discounts.value.findIndex((item) => item.id === discount.id);
      if (index > -1) {
        discounts.value.splice(index, 1);
      }
      common_vendor.index.showToast({
        title: "已删除活动",
        icon: "success"
      });
    }
    function createNewDiscount() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/discount/create",
        animationType: "slide-in-right"
      });
    }
    function setHoveredDiscount(discountId) {
      hoveredDiscount.value = discountId;
    }
    function clearHoveredDiscount() {
      hoveredDiscount.value = null;
    }
    common_vendor.onMounted(() => {
    });
    return {
      statistics,
      displayDiscounts,
      searchKeyword,
      hoveredDiscount,
      goBack,
      onSearch,
      showFilterOptions,
      viewDiscountDetail,
      editDiscount,
      toggleDiscountStatus,
      confirmDeleteDiscount,
      createNewDiscount,
      setHoveredDiscount,
      clearHoveredDiscount
    };
  }
};
if (!Array) {
  const _component_CreateButton = common_vendor.resolveComponent("CreateButton");
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_polygon = common_vendor.resolveComponent("polygon");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_rect = common_vendor.resolveComponent("rect");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  (_component_CreateButton + _component_circle + _component_line + _component_svg + _component_polygon + _component_path + _component_rect + _component_polyline)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $setup.goBack && $setup.goBack(...args)),
    b: common_vendor.o($setup.createNewDiscount),
    c: common_vendor.p({
      text: "创建满减活动",
      theme: "discount"
    }),
    d: common_vendor.t($setup.statistics.total),
    e: common_vendor.t($setup.statistics.active),
    f: common_vendor.t($setup.statistics.orders),
    g: common_vendor.t($setup.statistics.discount),
    h: common_vendor.p({
      cx: "11",
      cy: "11",
      r: "8"
    }),
    i: common_vendor.p({
      x1: "21",
      y1: "21",
      x2: "16.65",
      y2: "16.65"
    }),
    j: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    k: common_vendor.o([($event) => $setup.searchKeyword = $event.detail.value, (...args) => $setup.onSearch && $setup.onSearch(...args)]),
    l: $setup.searchKeyword,
    m: common_vendor.p({
      points: "22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"
    }),
    n: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    o: common_vendor.o((...args) => $setup.showFilterOptions && $setup.showFilterOptions(...args)),
    p: common_vendor.f($setup.displayDiscounts, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.statusText),
        b: common_vendor.n("status-" + item.status),
        c: common_vendor.t(item.title),
        d: common_vendor.f(item.rules, (rule, ruleIndex, i1) => {
          return {
            a: common_vendor.t(rule.minAmount),
            b: common_vendor.t(rule.discountAmount),
            c: ruleIndex
          };
        }),
        e: common_vendor.t(item.timeRange),
        f: common_vendor.t(item.usageCount),
        g: common_vendor.t(item.totalDiscount),
        h: "16c40466-7-" + i0 + "," + ("16c40466-6-" + i0),
        i: "16c40466-6-" + i0,
        j: common_vendor.o(($event) => $setup.editDiscount(item), index),
        k: item.status === "active"
      }, item.status === "active" ? {
        l: "16c40466-9-" + i0 + "," + ("16c40466-8-" + i0),
        m: common_vendor.p({
          x: "6",
          y: "4",
          width: "4",
          height: "16"
        }),
        n: "16c40466-10-" + i0 + "," + ("16c40466-8-" + i0),
        o: common_vendor.p({
          x: "14",
          y: "4",
          width: "4",
          height: "16"
        }),
        p: "16c40466-8-" + i0,
        q: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2"
        })
      } : {
        r: "16c40466-12-" + i0 + "," + ("16c40466-11-" + i0),
        s: common_vendor.p({
          points: "5 3 19 12 5 21 5 3"
        }),
        t: "16c40466-11-" + i0,
        v: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2"
        })
      }, {
        w: common_vendor.t(item.status === "active" ? "暂停" : "启用"),
        x: common_vendor.n(item.status === "active" ? "pause" : "activate"),
        y: common_vendor.o(($event) => $setup.toggleDiscountStatus(item), index),
        z: "16c40466-14-" + i0 + "," + ("16c40466-13-" + i0),
        A: "16c40466-15-" + i0 + "," + ("16c40466-13-" + i0),
        B: "16c40466-16-" + i0 + "," + ("16c40466-13-" + i0),
        C: "16c40466-17-" + i0 + "," + ("16c40466-13-" + i0),
        D: "16c40466-13-" + i0,
        E: common_vendor.o(($event) => $setup.confirmDeleteDiscount(item), index),
        F: index,
        G: common_vendor.o(($event) => $setup.viewDiscountDetail(item), index),
        H: $setup.hoveredDiscount === item.id ? 1 : "",
        I: common_vendor.o(($event) => $setup.setHoveredDiscount(item.id), index),
        J: common_vendor.o(($event) => $setup.clearHoveredDiscount(), index)
      });
    }),
    q: common_vendor.p({
      d: "M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"
    }),
    r: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    s: common_vendor.p({
      points: "3 6 5 6 21 6"
    }),
    t: common_vendor.p({
      d: "M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"
    }),
    v: common_vendor.p({
      x1: "10",
      y1: "11",
      x2: "10",
      y2: "17"
    }),
    w: common_vendor.p({
      x1: "14",
      y1: "11",
      x2: "14",
      y2: "17"
    }),
    x: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    y: $setup.displayDiscounts.length === 0
  }, $setup.displayDiscounts.length === 0 ? {
    z: common_vendor.p({
      points: "9 10 4 15 9 20"
    }),
    A: common_vendor.p({
      d: "M20 4v7a4 4 0 0 1-4 4H4"
    }),
    B: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/discount/management.js.map
