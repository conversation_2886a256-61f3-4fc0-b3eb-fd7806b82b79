<template>
  <view class="settings-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">系统设置</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 账号安全模块 -->
    <view class="settings-section">
      <view class="section-header">
        <text class="section-title">账号安全</text>
      </view>
      
      <view class="settings-menu">
        <!-- 密码管理 -->
        <view class="menu-item" @click="navigateTo('./security/password')">
          <view class="item-left">
            <view class="item-icon password-icon">
              <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                <path d="M7 11V7a5 5 0 0110 0v4"></path>
              </svg>
            </view>
            <text class="item-name">密码管理</text>
          </view>
          <view class="item-right">
            <text class="item-desc">双因素认证</text>
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <!-- 登录设备管理 -->
        <view class="menu-item" @click="navigateTo('./security/devices')">
          <view class="item-left">
            <view class="item-icon device-icon">
              <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="5" y="2" width="14" height="20" rx="2" ry="2"></rect>
                <line x1="12" y1="18" x2="12" y2="18"></line>
              </svg>
            </view>
            <text class="item-name">登录设备管理</text>
          </view>
          <view class="item-right">
            <text class="item-desc">3台设备</text>
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <!-- 操作日志查询 -->
        <view class="menu-item" @click="navigateTo('./security/logs')">
          <view class="item-left">
            <view class="item-icon log-icon">
              <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z"></path>
                <polyline points="14 2 14 8 20 8"></polyline>
                <line x1="16" y1="13" x2="8" y2="13"></line>
                <line x1="16" y1="17" x2="8" y2="17"></line>
                <polyline points="10 9 9 9 8 9"></polyline>
              </svg>
            </view>
            <text class="item-name">操作日志查询</text>
          </view>
          <view class="item-right">
            <view class="arrow-icon"></view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 员工权限管理模块 -->
    <view class="settings-section">
      <view class="section-header">
        <text class="section-title">员工权限管理</text>
      </view>
      
      <view class="settings-menu">
        <!-- 角色设置 -->
        <view class="menu-item" @click="navigateTo('./staff/roles')">
          <view class="item-left">
            <view class="item-icon role-icon">
              <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="M23 21v-2a4 4 0 00-3-3.87"></path>
                <path d="M16 3.13a4 4 0 010 7.75"></path>
              </svg>
            </view>
            <text class="item-name">角色设置</text>
          </view>
          <view class="item-right">
            <text class="item-desc">管理员、运营、客服等</text>
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <!-- 权限配置 -->
        <view class="menu-item" @click="navigateTo('./staff/permissions')">
          <view class="item-left">
            <view class="item-icon permission-icon">
              <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
              </svg>
            </view>
            <text class="item-name">权限配置</text>
          </view>
          <view class="item-right">
            <text class="item-desc">功能权限、数据权限</text>
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <!-- 操作用户 -->
        <view class="menu-item" @click="navigateTo('./staff/users')">
          <view class="item-left">
            <view class="item-icon user-icon">
              <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M20 21v-2a4 4 0 00-4-4H8a4 4 0 00-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
              </svg>
            </view>
            <text class="item-name">操作用户</text>
          </view>
          <view class="item-right">
            <text class="item-desc">关联操作记录与追踪</text>
            <view class="arrow-icon"></view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 通知设置模块 -->
    <view class="settings-section">
      <view class="section-header">
        <text class="section-title">通知设置</text>
      </view>
      
      <view class="settings-menu">
        <!-- 通知渠道设置 -->
        <view class="menu-item" @click="navigateTo('./notification/channels')">
          <view class="item-left">
            <view class="item-icon channel-icon">
              <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M22 16.92v3a2 2 0 01-2.18 2 19.79 19.79 0 01-8.63-3.07 19.5 19.5 0 01-6-6 19.79 19.79 0 01-3.07-8.67A2 2 0 014.11 2h3a2 2 0 012 1.72 12.84 12.84 0 00.7 2.81 2 2 0 01-.45 2.11L8.09 9.91a16 16 0 006 6l1.27-1.27a2 2 0 012.11-.45 12.84 12.84 0 002.81.7A2 2 0 0122 16.92z"></path>
              </svg>
            </view>
            <text class="item-name">通知渠道设置</text>
          </view>
          <view class="item-right">
            <text class="item-desc">短信、微信、APP推送</text>
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <!-- 通知类型设置 -->
        <view class="menu-item" @click="navigateTo('./notification/types')">
          <view class="item-left">
            <view class="item-icon type-icon">
              <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="16"></line>
                <line x1="8" y1="12" x2="16" y2="12"></line>
              </svg>
            </view>
            <text class="item-name">通知类型设置</text>
          </view>
          <view class="item-right">
            <text class="item-desc">订单、营销、系统</text>
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <!-- 消息模板管理 -->
        <view class="menu-item" @click="navigateTo('./notification/templates')">
          <view class="item-left">
            <view class="item-icon template-icon">
              <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z"></path>
                <polyline points="14 2 14 8 20 8"></polyline>
                <line x1="16" y1="13" x2="8" y2="13"></line>
                <line x1="16" y1="17" x2="8" y2="17"></line>
                <polyline points="10 9 9 9 8 9"></polyline>
              </svg>
            </view>
            <text class="item-name">消息模板管理</text>
          </view>
          <view class="item-right">
            <text class="item-desc">自定义通知内容</text>
            <view class="arrow-icon"></view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 应用集成模块 -->
    <view class="settings-section">
      <view class="section-header">
        <text class="section-title">应用集成</text>
      </view>
      
      <view class="settings-menu">
        <!-- 打印机设置 -->
        <view class="menu-item" @click="navigateTo('./integration/printer')">
          <view class="item-left">
            <view class="item-icon printer-icon">
              <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="6 9 6 2 18 2 18 9"></polyline>
                <path d="M6 18H4a2 2 0 01-2-2v-5a2 2 0 012-2h16a2 2 0 012 2v5a2 2 0 01-2 2h-2"></path>
                <rect x="6" y="14" width="12" height="8"></rect>
              </svg>
            </view>
            <text class="item-name">打印机设置</text>
          </view>
          <view class="item-right">
            <text class="item-desc">小票打印、发票打印</text>
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <!-- 物流对接 -->
        <view class="menu-item" @click="navigateTo('./integration/logistics')">
          <view class="item-left">
            <view class="item-icon logistics-icon">
              <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="1" y="3" width="15" height="13"></rect>
                <polygon points="16 8 20 8 23 11 23 16 16 16 16 8"></polygon>
                <circle cx="5.5" cy="18.5" r="2.5"></circle>
                <circle cx="18.5" cy="18.5" r="2.5"></circle>
              </svg>
            </view>
            <text class="item-name">物流对接</text>
          </view>
          <view class="item-right">
            <text class="item-desc">快递公司对接、运费模板</text>
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <!-- 支付设置 -->
        <view class="menu-item" @click="navigateTo('./integration/payment')">
          <view class="item-left">
            <view class="item-icon payment-icon">
              <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="2" y="4" width="20" height="16" rx="2" ry="2"></rect>
                <line x1="2" y1="10" x2="22" y2="10"></line>
              </svg>
            </view>
            <text class="item-name">支付设置</text>
          </view>
          <view class="item-right">
            <text class="item-desc">支付渠道、结算周期</text>
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <!-- 第三方应用 -->
        <view class="menu-item" @click="navigateTo('./integration/apps')">
          <view class="item-left">
            <view class="item-icon app-icon">
              <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="3" width="7" height="7"></rect>
                <rect x="14" y="3" width="7" height="7"></rect>
                <rect x="14" y="14" width="7" height="7"></rect>
                <rect x="3" y="14" width="7" height="7"></rect>
              </svg>
            </view>
            <text class="item-name">第三方应用</text>
          </view>
          <view class="item-right">
            <text class="item-desc">营销工具、数据分析</text>
            <view class="arrow-icon"></view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    navigateTo(url) {
      uni.navigateTo({ url });
    },
    showHelp() {
      uni.showToast({
        title: '帮助中心功能开发中',
        icon: 'none'
      });
    }
  }
}
</script>

<style>
.settings-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 20px;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}

.settings-section {
  margin-bottom: 20px;
}

.section-header {
  padding: 16px 16px 8px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.settings-menu {
  background-color: #fff;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.item-left {
  display: flex;
  align-items: center;
}

.item-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.password-icon, .log-icon {
  background-color: #e6f7ff;
  color: #1677FF;
}

.device-icon, .role-icon {
  background-color: #fff7e6;
  color: #fa8c16;
}

.permission-icon, .user-icon {
  background-color: #f6ffed;
  color: #52c41a;
}

.channel-icon, .type-icon {
  background-color: #fcf4ff;
  color: #722ed1;
}

.template-icon, .printer-icon {
  background-color: #fff2e8;
  color: #fa541c;
}

.logistics-icon, .payment-icon {
  background-color: #f0f5ff;
  color: #2f54eb;
}

.app-icon {
  background-color: #fff0f6;
  color: #eb2f96;
}

.item-name {
  font-size: 16px;
  color: #333;
}

.item-right {
  display: flex;
  align-items: center;
}

.item-desc {
  font-size: 14px;
  color: #999;
  margin-right: 8px;
}

.arrow-icon {
  width: 8px;
  height: 8px;
  border-top: 1px solid #ccc;
  border-right: 1px solid #ccc;
  transform: rotate(45deg);
}
</style> 