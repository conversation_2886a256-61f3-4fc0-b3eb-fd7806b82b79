"use strict";
const login = (phone, code) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      if (phone && code) {
        resolve({
          success: true,
          token: "mock-token-" + Date.now(),
          refreshToken: "mock-refresh-token-" + Date.now(),
          expiry: Date.now() + 7 * 24 * 60 * 60 * 1e3,
          userInfo: {
            id: "user-001",
            nickname: "磁州居民",
            avatar: "/static/images/tabbar/user-blue.png",
            phone
          }
        });
      } else {
        resolve({
          success: false,
          message: "手机号或验证码错误"
        });
      }
    }, 500);
  });
};
const register = (phone, code, password) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      if (phone && code) {
        resolve({
          success: true,
          token: "mock-token-" + Date.now(),
          refreshToken: "mock-refresh-token-" + Date.now(),
          expiry: Date.now() + 7 * 24 * 60 * 60 * 1e3,
          userInfo: {
            id: "user-" + Date.now(),
            nickname: "磁州新用户",
            avatar: "/static/images/tabbar/user-blue.png",
            phone
          }
        });
      } else {
        resolve({
          success: false,
          message: "注册信息不完整"
        });
      }
    }, 700);
  });
};
const getVerificationCode = (phone) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      if (phone && phone.length === 11) {
        resolve({
          success: true,
          message: "验证码已发送",
          code: "1234"
          // 模拟环境下固定验证码
        });
      } else {
        resolve({
          success: false,
          message: "手机号格式错误"
        });
      }
    }, 300);
  });
};
const logout = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        message: "退出登录成功"
      });
    }, 300);
  });
};
const refreshAuthToken = (refreshToken) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      if (refreshToken) {
        resolve({
          success: true,
          token: "mock-token-" + Date.now(),
          refreshToken: "mock-refresh-token-" + Date.now(),
          expiry: Date.now() + 7 * 24 * 60 * 60 * 1e3
        });
      } else {
        resolve({
          success: false,
          message: "refresh token无效"
        });
      }
    }, 400);
  });
};
exports.getVerificationCode = getVerificationCode;
exports.login = login;
exports.logout = logout;
exports.refreshAuthToken = refreshAuthToken;
exports.register = register;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/mock/user/auth.js.map
