{"version": 3, "file": "success.js", "sources": ["pages/publish/success.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHVibGlzaC9zdWNjZXNzLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"page-bg\">\n    <!-- 顶部导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"nav-left\" @click=\"goBack\">\n        <image class=\"nav-icon\" src=\"/static/images/tabbar/返回.png\"></image>\n\t\t\t</view>\n      <view class=\"nav-title\">发布成功</view>\n      <view class=\"nav-right\">\n        <image class=\"nav-icon\" src=\"/static/images/tabbar/menu.png\"></image>\n        <image class=\"nav-icon\" src=\"/static/images/tabbar/refresh.png\" style=\"margin-left: 16rpx;\"></image>\n\t\t\t</view>\n\t\t</view>\n\t\t\n    <!-- 主内容卡片 -->\n    <view class=\"main-card\">\n      <view class=\"main-title\">信息发布成功 - 磁州生活网</view>\n      <view class=\"main-desc\">完善信息或置顶、红包，可以大幅度提高信息传播效果哦</view>\n      <view class=\"main-btns\">\n        <view\n          v-for=\"(btn, idx) in btnList\"\n          :key=\"idx\"\n          :class=\"['main-btn', idx === 1 ? 'main-btn-active' : '']\"\n          @click=\"btn.action\"\n        >\n          <view class=\"btn-text-container\">\n            <text class=\"btn-text\">{{ btn.text1 }}</text>\n            <text class=\"btn-text\">{{ btn.text2 }}</text>\n\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n    <!-- 置顶推广卡片 - 使用ConfigurablePremiumActions组件 -->\n    <view class=\"premium-card\">\n      <view class=\"premium-title\">\n        <view class=\"premium-title-icon\">\n          <image src=\"/static/images/premium/top.png\" mode=\"aspectFit\"></image>\n        </view>\n        <text>置顶信息，提升10倍曝光率</text>\n      </view>\n      <ConfigurablePremiumActions\n        pageType=\"publish_top\"\n        showMode=\"direct\"\n        :itemData=\"publishData\"\n        @action-completed=\"handleTopActionCompleted\"\n        @action-cancelled=\"handleTopActionCancelled\"\n      />\n    </view>\n\t\t\n\t\t<!-- 悬浮按钮 -->\n\t\t<button class=\"float-btn share-btn\" open-type=\"share\" @click=\"beforeShare\">\n\t\t\t<image class=\"float-icon\" src=\"/static/images/tabbar/share.png\"></image>\n\t\t</button>\n    <view class=\"float-btn kefu-btn\" @click=\"showKefu\">\n      <image class=\"float-icon\" src=\"/static/images/tabbar/kefu.png\"></image>\n\t\t</view>\n    \n    <!-- 置顶结果提示 -->\n    <view class=\"top-result\" v-if=\"topResultVisible\">\n      <view class=\"top-result-content\">\n        <image class=\"top-result-icon\" :src=\"topSuccess ? '/static/images/pay/success.png' : '/static/images/pay/fail.png'\"></image>\n        <view class=\"top-result-text\">{{ topResultText }}</view>\n      </view>\n    </view>\n    \n    <!-- 分享提示弹窗 -->\n    <view class=\"share-tips-overlay\" v-if=\"shareTipsVisible\">\n      <view class=\"share-tips-card\" @click.stop>\n        <view class=\"share-tips-icon\">\n          <image src=\"/static/images/crown.png\" mode=\"aspectFit\" class=\"crown-icon\"></image>\n        </view>\n        <view class=\"share-tips-title\">恭喜你!获得免费置顶和群发机会</view>\n        \n        <view class=\"share-tips-item\">\n          <view class=\"tips-item-number\">1</view>\n          <view class=\"tips-item-content\">\n            <text class=\"tips-text\">把信息分享到朋友圈、微信群或好友处，</text>\n            <text class=\"tips-text\">您的信息将自动置顶1天！</text>\n          </view>\n        </view>\n        \n        <view class=\"share-tips-item\">\n          <view class=\"tips-item-number\">2</view>\n          <view class=\"tips-item-content\">\n            <text class=\"tips-text\">把你发布的信息发送给客服后，客服会给</text>\n            <text class=\"tips-text\">您群发多个群，扩大曝光量!</text>\n          </view>\n        </view>\n        \n        <view class=\"share-tips-btns\">\n          <view class=\"share-btn-item close-btn\" @click=\"hideShareTips\" style=\"background-color: #FFFFFF; color: #999999; border-right: 1rpx solid #EEEEEE; height: 90rpx; font-size: 30rpx; font-weight: 500; display: flex; align-items: center; justify-content: center;\">关闭</view>\n          <view class=\"share-btn-item share-btn-blue pulsing-btn\" @click=\"jumpToDetailAndShare\" style=\"background-color: #007AFF !important; color: #FFFFFF !important; position: relative; line-height: 90rpx; height: 90rpx; padding: 0; overflow: visible; border:none; box-sizing: border-box; display: flex; align-items: center; justify-content: center;\">\n            <text style=\"color: #FFFFFF !important; font-size: 30rpx; font-weight: bold;\">去分享</text>\n          </view>\n          <view class=\"share-btn-item share-btn-green\" @click=\"contactService\" style=\"background-color: #07C160; color: #FFFFFF; height: 90rpx; font-size: 30rpx; font-weight: 500; display: flex; align-items: center; justify-content: center;\">加客服</view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 客服二维码弹窗 -->\n    <view class=\"qrcode-overlay\" v-if=\"qrcodeVisible\" @click=\"hideQrcode\">\n      <view class=\"qrcode-card\" @click.stop>\n        <view class=\"qrcode-header\">\n          <view class=\"qrcode-close\" @click=\"hideQrcode\">\n            <svg t=\"1692586074385\" class=\"close-icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\">\n              <path d=\"M572.16 512l183.466667-183.04a42.666667 42.666667 0 1 0-60.586667-60.586667L512 451.84 328.96 268.373333a42.666667 42.666667 0 0 0-60.586667 60.586667l183.04 183.04-183.04 183.466667a42.666667 42.666667 0 0 0 60.586667 60.586666L512 572.16l183.04 183.466667a42.666667 42.666667 0 0 0 60.586667-60.586667z\" fill=\"#999999\"></path>\n            </svg>\n          </view>\n        </view>\n        \n        <view class=\"qrcode-content\">\n          <view class=\"qrcode-title-container\">\n            <image class=\"qrcode-title-icon\" src=\"/static/images/icons/customer-service.png\" mode=\"aspectFit\"></image>\n            <text class=\"qrcode-title\">微信扫码添加客服</text>\n          </view>\n          \n        <view class=\"qrcode-desc\">添加客服微信，提供更多发布推广服务</view>\n          \n          <view class=\"qrcode-image-container\">\n        <image src=\"/static/images/qrcode.png\" mode=\"aspectFit\" class=\"qrcode-image\"></image>\n            <view class=\"qrcode-scan-hint\">\n              <view class=\"scan-icon-container\">\n                <svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\" class=\"scan-icon\">\n                  <path fill=\"currentColor\" d=\"M9.5,6.5v3h-3v-3H9.5 M11,5H5v6h6V5L11,5z M9.5,14.5v3h-3v-3H9.5 M11,13H5v6h6V13z M17.5,6.5v3h-3v-3H17.5 M19,5h-6v6h6V5L19,5z M13,13h1.5v1.5H13V13z M14.5,14.5H16V16h-1.5V14.5z M16,13h1.5v1.5H16V13z M13,16h1.5v1.5H13V16z M14.5,17.5H16V19h-1.5V17.5z M16,16h1.5v1.5H16V16z M17.5,14.5H19V16h-1.5V14.5z M17.5,17.5H19V19h-1.5V17.5z M22,7h-2V4h-3V2h5V7z M22,22v-5h-2v3h-3v2H22z M2,22h5v-2H4v-3H2V22z M2,2v5h2V4h3V2H2z\"/>\n                </svg>\n        </view>\n              <text>长按识别二维码添加客服</text>\n        </view>\n      </view>\n          \n          <view class=\"qrcode-info-container\">\n            <view class=\"qrcode-info-item\">\n              <svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\" class=\"info-icon\">\n                <path fill=\"currentColor\" d=\"M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,7H13V13H11V7M11,15H13V17H11V15Z\"/>\n              </svg>\n              <text>客服在线时间: 8:00-22:00</text>\n            </view>\n            <view class=\"qrcode-info-item\">\n              <svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\" class=\"info-icon\">\n                <path fill=\"currentColor\" d=\"M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,6A2,2 0 0,0 10,8A2,2 0 0,0 12,10A2,2 0 0,0 14,8A2,2 0 0,0 12,6M12,13C14.67,13 20,14.33 20,17V20H4V17C4,14.33 9.33,13 12,13M12,14.9C9.03,14.9 5.9,16.36 5.9,17V18.1H18.1V17C18.1,16.36 14.97,14.9 12,14.9Z\"/>\n              </svg>\n              <text>客服微信: cishangtc</text>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"qrcode-actions\">\n          <view class=\"qrcode-btn copy-btn\" @click=\"copyWechatId\">\n            <svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\" class=\"btn-icon\">\n              <path fill=\"currentColor\" d=\"M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z\"/>\n            </svg>\n            <text>复制微信号</text>\n          </view>\n          <view class=\"qrcode-btn save-btn\" @click=\"saveQrcode\">\n            <svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\" class=\"btn-icon\">\n              <path fill=\"currentColor\" d=\"M15,9H5V5H15M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3Z\"/>\n            </svg>\n            <text>保存图片</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\t</view>\n</template>\n\n<script setup>\nimport { ref, onMounted, onUnmounted } from 'vue';\nimport ConfigurablePremiumActions from '@/components/premium/ConfigurablePremiumActions.vue';\n\nconst btnList = [\n  { text1: '管理', text2: '信息', action: () => uni.reLaunch({ url: '/pages/my/publish' }) },\n  { text1: '查看', text2: '信息', action: viewInfo },\n  { text1: '再发', text2: '一条', action: () => uni.navigateTo({ url: '/pages/publish/publish' }) },\n  { text1: '分享', text2: '信息', action: shareInfo },\n  { text1: '首', text2: '页', action: () => uni.switchTab({ url: '/pages/index/index' }) }\n];\n\n// 置顶结果相关\nconst topResultVisible = ref(false);\nconst topSuccess = ref(false);\nconst topResultText = ref('');\n\n// 分享提示相关\nconst shareTipsVisible = ref(false);\n\n// 客服二维码相关\nconst qrcodeVisible = ref(false);\nconst wechatId = ref('cishangtc'); // 客服微信ID\n\n// 发布ID\nconst publishId = ref('');\n\n// 发布内容数据 - 适配ConfigurablePremiumActions组件\nconst publishData = ref({\n  id: '',\n  title: '信息置顶',\n  description: '置顶您的信息，获得更多曝光'\n});\n\n// 原始发布内容数据\nconst originalPublishData = ref({\n  title: '',\n  content: '',\n  images: [],\n  category: ''\n});\n\n// 页面加载时显示分享提示\nonMounted(() => {\n  // 获取发布的信息数据\n  const pubId = uni.getStorageSync('lastPublishId');\n  publishId.value = pubId;\n  const pubData = uni.getStorageSync('lastPublishData');\n  \n  if (pubData) {\n    // 同步数据到首页相应分类\n    updateHomePageData(pubData);\n\n    // 保存原始发布数据，用于分享\n    originalPublishData.value = {\n      title: pubData.title || pubData.content || '磁州同城信息',\n      content: pubData.content || pubData.description || '',\n      images: pubData.images || [],\n      category: pubData.category || pubData.categoryName || '同城信息'\n    };\n\n    // 设置ConfigurablePremiumActions组件需要的数据\n    publishData.value = {\n      id: pubId || 'publish_' + Date.now(),\n      title: '信息置顶',\n      description: `置顶\"${pubData.title || pubData.content || '您的信息'}\"，获得更多曝光`\n    };\n  }\n  \n  // 检查信息是否已经置顶\n  checkTopStatus(pubId);\n  \n  // 添加显示分享提示卡片的代码\n  setTimeout(() => {\n    shareTipsVisible.value = true;\n  }, 1000);\n});\n\n// 检查信息是否已经置顶\nconst checkTopStatus = async (infoId) => {\n  if (!infoId) return;\n  \n  try {\n    uni.showLoading({ title: '加载中' });\n    \n    const res = await uni.request({\n      url: '/api/info/top/status',\n      method: 'GET',\n      data: { infoId }\n    });\n    \n    if (res.data && res.data.isTopped) {\n      // 信息已置顶，显示提示\n      uni.showToast({\n        title: `信息已置顶，剩余${res.data.remainingDays}天`,\n        icon: 'none',\n        duration: 3000\n      });\n    }\n    \n    uni.hideLoading();\n  } catch (error) {\n    console.error('检查置顶状态失败', error);\n    uni.hideLoading();\n  }\n};\n\n// 添加更新首页数据的函数\nconst updateHomePageData = (publishData) => {\n  try {\n    // 获取首页全部信息列表\n    const allInfoList = uni.getStorageSync('homeAllInfoList') || [];\n    \n    // 创建新的信息项\n    const newInfo = {\n      id: Date.now(),\n      category: publishData.category || publishData.categoryName,\n      content: publishData.content || publishData.title,\n      time: new Date().toLocaleString(),\n      views: 0\n    };\n    \n    // 将新信息添加到列表前面\n    allInfoList.unshift(newInfo);\n    \n    // 存储更新后的列表\n    uni.setStorageSync('homeAllInfoList', allInfoList);\n    \n    console.log('成功将发布的信息同步到首页', newInfo);\n  } catch (e) {\n    console.error('同步数据到首页失败', e);\n  }\n};\n\n// 在查看信息按钮处理函数中添加更新首页数据的功能\nconst viewInfo = () => {\n  // 获取最后发布的ID\n  const pubId = uni.getStorageSync('lastPublishId');\n  // 导航到详情页\n  uni.navigateTo({ \n    url: '/pages/publish/detail?id=' + pubId \n  });\n};\n\n// 处理置顶操作完成事件\nconst handleTopActionCompleted = (result) => {\n  console.log('置顶操作完成', result);\n  \n  // 显示操作结果\n  topResultVisible.value = true;\n  topSuccess.value = true;\n  \n  if (result.type === 'ad') {\n    topResultText.value = '广告置顶成功！信息已置顶2小时';\n  } else if (result.type === 'paid') {\n    topResultText.value = `付费置顶成功！信息已置顶${result.option.duration}`;\n  }\n  \n  // 2秒后隐藏结果提示\n  setTimeout(() => {\n    topResultVisible.value = false;\n  }, 2000);\n};\n\n// 处理置顶操作取消事件\nconst handleTopActionCancelled = (result) => {\n  console.log('置顶操作取消', result);\n\n  if (result.type === 'ad') {\n    uni.showToast({\n      title: '已取消观看广告',\n      icon: 'none'\n    });\n  } else if (result.type === 'payment') {\n    uni.showToast({\n      title: '已取消支付',\n      icon: 'none'\n    });\n  }\n};\n\nconst goBack = () => uni.navigateBack();\n\n// 隐藏分享提示弹窗\nconst hideShareTips = () => {\n  shareTipsVisible.value = false;\n};\n\n// 分享信息\nconst shareInfo = () => {\n  // 调用beforeShare准备分享数据\n  beforeShare();\n  \n  // 调用系统分享\n  uni.showShareMenu({\n    withShareTicket: true,\n    menus: ['shareAppMessage', 'shareTimeline'],\n    success: () => {\n      console.log('显示分享菜单成功');\n    },\n    fail: (err) => {\n      console.error('显示分享菜单失败', err);\n      // 如果显示分享菜单失败，则显示分享提示弹窗\n  shareTipsVisible.value = true;\n    }\n  });\n};\n\n// 添加beforeShare函数，在分享前准备分享数据\nconst beforeShare = () => {\n  console.log('准备分享数据');\n  \n  // 获取最新的发布数据\n  const pubId = uni.getStorageSync('lastPublishId');\n  \n  if (!pubId) {\n    console.error('未找到发布ID，无法获取分享内容');\n    return;\n  }\n  \n  // 使用本地数据作为备选，以确保分享功能可用\n  const pubData = uni.getStorageSync('lastPublishData');\n  \n  // 收集发布信息的标题和内容\n  let shareTitle = '';\n  let shareImage = '';\n  \n  if (pubData) {\n    // 根据不同类型的发布信息，提取合适的标题\n    if (pubData.title) {\n      shareTitle = pubData.title;\n    } else if (pubData.content) {\n      // 使用内容的前20个字符作为标题\n      shareTitle = pubData.content.substring(0, 20) + (pubData.content.length > 20 ? '...' : '');\n    } else {\n      // 默认标题\n      shareTitle = pubData.category || pubData.categoryName || '磁州同城信息';\n    }\n    \n    // 提取第一张图片作为分享图片\n    if (pubData.images && pubData.images.length > 0) {\n      shareImage = pubData.images[0];\n    }\n  } else {\n    shareTitle = '磁州同城信息';\n  }\n  \n  // 设置全局分享数据\n  const app = getApp();\n  if (app.globalData) {\n    app.globalData.shareInfo = {\n      title: shareTitle,\n      path: '/pages/publish/detail?id=' + pubId,\n      imageUrl: shareImage\n    };\n  } else {\n    app.globalData = { \n      shareInfo: {\n        title: shareTitle,\n        path: '/pages/publish/detail?id=' + pubId,\n        imageUrl: shareImage\n      }\n    };\n  }\n  \n  console.log('分享数据已准备:', app.globalData.shareInfo);\n};\n\n// 跳转到详情页并分享\nconst jumpToDetailAndShare = () => {\n  const pubId = uni.getStorageSync('lastPublishId');\n  if (pubId) {\n    // 先准备分享数据\n    beforeShare();\n    // 隐藏当前提示\n    hideShareTips();\n    // 跳转到信息展示详情页并设置参数，表示需要自动打开分享菜单\n    uni.navigateTo({\n      url: `/pages/publish/info-detail?id=${pubId}&autoShare=true`,\n      success: () => {\n        console.log('已跳转到信息展示详情页，准备自动分享');\n      },\n      fail: (err) => {\n        console.error('跳转信息展示详情页失败', err);\n        uni.showToast({\n          title: '跳转失败，请重试',\n          icon: 'none'\n    });\n      }\n    });\n  } else {\n    uni.showToast({\n      title: '信息ID不存在',\n      icon: 'none'\n    });\n  }\n};\n\n// 联系客服\nconst contactService = () => {\n  // 显示客服二维码弹窗\n  qrcodeVisible.value = true;\n  hideShareTips();\n};\n\n// 隐藏二维码弹窗\nconst hideQrcode = () => {\n  qrcodeVisible.value = false;\n};\n\n// 复制微信号\nconst copyWechatId = () => {\n  uni.setClipboardData({\n    data: wechatId.value,\n    success: function() {\n\t\t\t\t\tuni.showToast({\n        title: '客服微信号已复制',\n        icon: 'success'\n      });\n    }\n  });\n};\n\n// 保存二维码图片\nconst saveQrcode = () => {\n  uni.getSetting({\n    success: (res) => {\n      // 检查是否有保存到相册的权限\n      if (!res.authSetting['scope.writePhotosAlbum']) {\n        uni.authorize({\n          scope: 'scope.writePhotosAlbum',\n          success: () => {\n            saveQrcodeToAlbum();\n          },\n          fail: () => {\n            uni.showModal({\n              title: '提示',\n              content: '需要授权保存图片到相册',\n              success: (res) => {\n                if (res.confirm) {\n                  uni.openSetting();\n                }\n              }\n            });\n          }\n        });\n      } else {\n        saveQrcodeToAlbum();\n      }\n    }\n  });\n};\n\n// 保存二维码到相册\nconst saveQrcodeToAlbum = () => {\n  uni.showLoading({ title: '保存中...' });\n  \n  // 将网络图片先下载到本地\n  uni.downloadFile({\n    url: '/static/images/qrcode.png', // 这里应该是完整的URL路径\n    success: (res) => {\n      if (res.statusCode === 200) {\n        // 保存图片到相册\n        uni.saveImageToPhotosAlbum({\n          filePath: res.tempFilePath,\n          success: () => {\n            uni.hideLoading();\n            uni.showToast({\n              title: '二维码已保存到相册',\n              icon: 'success'\n            });\n          },\n          fail: (err) => {\n            uni.hideLoading();\n            console.error('保存失败', err);\n            uni.showToast({\n              title: '保存失败',\n              icon: 'none'\n            });\n          }\n        });\n      } else {\n        uni.hideLoading();\n        uni.showToast({\n          title: '下载图片失败',\n          icon: 'none'\n        });\n      }\n    },\n    fail: () => {\n      uni.hideLoading();\n      uni.showToast({\n        title: '下载图片失败',\n        icon: 'none'\n      });\n    }\n  });\n};\n\n// 显示客服\nconst showKefu = () => {\n  qrcodeVisible.value = true;\n};\n</script>\n\n<style scoped>\n.page-bg {\n\tmin-height: 100vh;\n  background: linear-gradient(180deg, #4a90e2 0%, #eaf6ff 100%);\n\tposition: relative;\n  padding-bottom: 60rpx;\n}\n.navbar {\n  height: 120rpx;\n  padding-top: 60rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n  background: linear-gradient(90deg, #2580e6 0%, #4a90e2 100%);\n\tposition: relative;\n}\n.nav-left, .nav-right {\n\tposition: absolute;\n  top: 60rpx;\n\theight: 60rpx;\n\tdisplay: flex;\n\talign-items: center;\n}\n.nav-left { \n  left: 30rpx; \n  top: 70rpx;\n  height: 40rpx;\n}\n.nav-right { \n  right: 30rpx; \n}\n.nav-title {\n  color: #fff;\n\tfont-size: 36rpx;\n  font-weight: bold;\n  text-align: center;\n}\n.nav-icon {\n  width: 38rpx;\n  height: 38rpx;\n}\n.main-card {\n  background: #fff;\n  border-radius: 24rpx;\n  margin: 40rpx 30rpx 0 30rpx;\n  padding: 40rpx 30rpx 30rpx 30rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);\n\tdisplay: flex;\n  flex-direction: column;\n\talign-items: center;\n}\n.main-title {\n  color: #1976d2;\n  font-size: 32rpx;\n  font-weight: bold;\n  margin-bottom: 16rpx;\n  text-align: center;\n}\n.main-desc {\n  color: #888;\n  font-size: 24rpx;\n  margin-bottom: 36rpx;\n  text-align: center;\n}\n.main-btns {\n  display: flex;\n  justify-content: space-between;\n  width: 100%;\n  margin-top: 10rpx;\n}\n.main-btn {\n  flex: 1;\n  margin: 0 10rpx;\n  background: #eaf3ff;\n  color: #1976d2;\n  border-radius: 32rpx;\n  font-weight: 500;\n  text-align: center;\n  padding: 25rpx 0;\n  height: 180rpx;\n  transition: background 0.2s, color 0.2s;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n.btn-text-container {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n  height: 100%;\n  padding: 10rpx 0;\n}\n.btn-text {\n  font-size: 28rpx;\n  line-height: 1.8;\n  display: block;\n  font-weight: bold;\n  letter-spacing: 2rpx;\n}\n.main-btn-active {\n  background: linear-gradient(90deg, #1976d2 0%, #4a90e2 100%);\n  color: #fff;\n}\n.premium-card {\n  background: #fff;\n  border-radius: 24rpx;\n  margin: 36rpx 30rpx 0 30rpx;\n  padding: 36rpx 30rpx 30rpx 30rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);\n}\n.premium-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 24rpx;\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n}\n.premium-title-icon {\n  width: 40rpx;\n  height: 40rpx;\n  margin-right: 12rpx;\n  background: linear-gradient(135deg, #3b7dfc 0%, #5e96ff 100%);\n  border-radius: 8rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 8rpx;\n}\n.premium-title-icon image {\n  width: 28rpx;\n  height: 28rpx;\n  filter: brightness(10);\n}\n.float-btn {\n  position: fixed;\n  right: 40rpx;\n  width: 90rpx;\n  height: 90rpx;\n  border-radius: 50%;\n  background: #fff;\n  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.12);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n  z-index: 100;\n}\n.share-btn { bottom: 180rpx; }\n.kefu-btn { bottom: 70rpx; }\n.float-icon {\n  width: 48rpx;\n  height: 48rpx;\n}\n.top-result {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  z-index: 999;\n\tdisplay: flex;\n\tjustify-content: center;\n  align-items: center;\n}\n.top-result-content {\n  background-color: #fff;\n  border-radius: 20rpx;\n  padding: 40rpx;\n  width: 80%;\n  max-width: 600rpx;\n  text-align: center;\n}\n.top-result-icon {\n  width: 100rpx;\n  height: 100rpx;\n  margin-bottom: 20rpx;\n}\n.top-result-text {\n  font-size: 28rpx;\n  color: #333;\n}\n.share-tips-overlay {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n  background-color: rgba(0, 0, 0, 0.7);\n  z-index: 999;\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n  animation: fadeIn 0.3s ease;\n}\n@keyframes fadeIn {\n  from { opacity: 0; }\n  to { opacity: 1; }\n}\n.share-tips-card {\n  width: 85%;\n\tbackground-color: #FFFFFF;\n\tborder-radius: 28rpx;\n\toverflow: hidden;\n\tdisplay: flex;\n\tflex-direction: column;\n  position: relative;\n  animation: zoomIn 0.3s ease;\n}\n@keyframes zoomIn {\n  from { transform: scale(0.9); opacity: 0; }\n  to { transform: scale(1); opacity: 1; }\n}\n.share-tips-icon {\n  text-align: center;\n  margin-top: 30rpx;\n}\n.crown-icon {\n  width: 100rpx;\n  height: 100rpx;\n}\n.share-tips-title {\n  color: #FF6600;\n  font-size: 36rpx;\n  font-weight: bold;\n  text-align: center;\n  margin: 20rpx 0 40rpx;\n  text-shadow: 0 1rpx 3rpx rgba(255, 102, 0, 0.2);\n}\n.share-tips-item {\n\tdisplay: flex;\n  padding: 20rpx 30rpx;\n  align-items: flex-start;\n  margin-bottom: 10rpx;\n}\n.tips-item-number {\n  width: 44rpx;\n  height: 44rpx;\n  background-color: #007AFF;\n  color: #FFFFFF;\n  border-radius: 50%;\n  text-align: center;\n  line-height: 44rpx;\n  font-weight: bold;\n  margin-right: 20rpx;\n  flex-shrink: 0;\n}\n.tips-item-content {\n\tflex: 1;\n}\n.tips-text {\n  font-size: 28rpx;\n\tcolor: #333333;\n  line-height: 1.6;\n  display: block;\n}\n.tips-counter {\n  background-color: #F1F1F1;\n  color: #999999;\n  font-size: 24rpx;\n  padding: 8rpx 30rpx;\n  border-radius: 30rpx;\n  display: inline-block;\n  margin-top: 10rpx;\n}\n.share-tips-btns {\n\tdisplay: flex;\n  margin-top: 30rpx;\n  border-top: 1rpx solid #EEEEEE;\n  flex-direction: row;\n  width: 100%;\n}\n.share-btn-item {\n  flex: 1;\n  height: 90rpx;\n\tdisplay: flex;\n\talign-items: center;\n  justify-content: center;\n  font-size: 30rpx;\n  font-weight: 500;\n  text-align: center;\n}\nbutton.share-btn-item {\n  margin: 0 !important;\n  padding: 0 !important;\n  line-height: 90rpx !important;\n  font-size: 30rpx !important;\n  font-weight: 500 !important;\n  box-sizing: border-box !important;\n  display: flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n  text-align: center !important;\n  flex: 1 !important;\n  height: 90rpx !important;\n  width: auto !important;\n  min-width: 0 !important;\n  min-height: 0 !important;\n  background-color: initial;\n  border: none !important;\n  position: relative;\n}\n.close-btn {\n  color: #999999;\n  border-right: 1rpx solid #EEEEEE;\n  background-color: #FFFFFF;\n}\n.share-btn-blue {\n  background-color: #007AFF !important;\n  color: #FFFFFF !important;\n  position: relative !important;\n  overflow: visible !important;\n  min-height: 90rpx !important;\n  font-weight: bold;\n  animation: pulsing 2s infinite;\n}\n\n.share-btn-blue view {\n  background-color: #007AFF !important;\n  color: #FFFFFF !important;\n  width: 100% !important;\n  height: 100% !important;\n  display: flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n}\n\n@keyframes pulsing {\n  0% {\n    box-shadow: 0 0 0 0 rgba(0, 122, 255, 0.7);\n    transform: scale(1);\n  }\n  70% {\n    box-shadow: 0 0 0 10px rgba(0, 122, 255, 0);\n    transform: scale(1.05);\n  }\n  100% {\n    box-shadow: 0 0 0 0 rgba(0, 122, 255, 0);\n    transform: scale(1);\n  }\n}\n\n.pulsing-btn {\n  animation: pulsing 2s infinite;\n}\n\nbutton.share-btn-item::after {\n  display: none !important;\n  border: none !important;\n  background: none !important;\n  padding: 0 !important;\n  margin: 0 !important;\n  content: none !important;\n}\n\nbutton.share-btn-blue::after {\n  display: none !important;\n}\n\nbutton.float-btn {\n  margin: 0;\n  padding: 0;\n  line-height: normal;\n  background-color: #fff;\n  border: none;\n}\n\n.share-btn-green {\n  background-color: #07C160;\n  color: #FFFFFF;\n}\n.view-count {\n  color: #FF6600;\n  font-weight: bold;\n  margin: 0 6rpx;\n}\n.qrcode-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.7);\n  z-index: 999;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  animation: fadeIn 0.3s ease;\n}\n\n.qrcode-card {\n  width: 85%;\n  max-width: 600rpx;\n  background-color: #FFFFFF;\n  border-radius: 24rpx;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  animation: zoomIn 0.3s ease;\n  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);\n}\n\n.qrcode-header {\n  position: relative;\n  height: 40rpx;\n  padding: 20rpx;\n}\n\n.qrcode-close {\n  position: absolute;\n  right: 20rpx;\n  top: 20rpx;\n  width: 40rpx;\n  height: 40rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 2;\n}\n\n.close-icon {\n  width: 32rpx;\n  height: 32rpx;\n}\n\n.qrcode-content {\n  padding: 0 40rpx 30rpx;\n}\n\n.qrcode-title-container {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 16rpx;\n}\n\n.qrcode-title-icon {\n  width: 48rpx;\n  height: 48rpx;\n  margin-right: 12rpx;\n}\n\n.qrcode-title {\n  color: #1677FF;\n  font-size: 36rpx;\n  font-weight: bold;\n  text-align: center;\n  margin: 0;\n}\n\n.qrcode-desc {\n  font-size: 28rpx;\n  color: #666;\n  text-align: center;\n  margin-bottom: 30rpx;\n}\n\n.qrcode-image-container {\n  position: relative;\n  width: 100%;\n  margin-bottom: 30rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.qrcode-image {\n  width: 320rpx;\n  height: 320rpx;\n  object-fit: contain;\n  border-radius: 12rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\n  border: 1rpx solid #f0f0f0;\n  background-color: #ffffff;\n  padding: 20rpx;\n}\n\n.qrcode-scan-hint {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-top: 16rpx;\n  color: #666;\n  font-size: 24rpx;\n}\n\n.scan-icon-container {\n  margin-right: 8rpx;\n  color: #1677FF;\n}\n\n.qrcode-info-container {\n  background-color: #f9f9f9;\n  border-radius: 16rpx;\n  padding: 20rpx;\n  margin-bottom: 30rpx;\n}\n\n.qrcode-info-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 12rpx;\n  font-size: 26rpx;\n  color: #333;\n}\n\n.qrcode-info-item:last-child {\n  margin-bottom: 0;\n}\n\n.info-icon {\n  margin-right: 10rpx;\n  color: #1677FF;\n}\n\n.qrcode-actions {\n  display: flex;\n  border-top: 1rpx solid #f0f0f0;\n}\n\n.qrcode-btn {\n  flex: 1;\n  height: 90rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n  font-weight: 500;\n  position: relative;\n}\n\n.qrcode-btn:active {\n  background-color: rgba(0, 0, 0, 0.05);\n}\n\n.copy-btn {\n  color: #1677FF;\n  border-right: 1rpx solid #f0f0f0;\n}\n\n.save-btn {\n  background: linear-gradient(135deg, #1677FF, #0062FF);\n  color: #FFFFFF;\n}\n\n.btn-icon {\n  margin-right: 8rpx;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/publish/success.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "ref", "onMounted", "publishData", "res"], "mappings": ";;;;;;;;;;;AAwKA,MAAM,6BAA6B,MAAW;;;;AAE9C,UAAM,UAAU;AAAA,MACd,EAAE,OAAO,MAAM,OAAO,MAAM,QAAQ,MAAMA,cAAG,MAAC,SAAS,EAAE,KAAK,oBAAqB,CAAA,EAAG;AAAA,MACtF,EAAE,OAAO,MAAM,OAAO,MAAM,QAAQ,SAAU;AAAA,MAC9C,EAAE,OAAO,MAAM,OAAO,MAAM,QAAQ,MAAMA,cAAG,MAAC,WAAW,EAAE,KAAK,yBAA0B,CAAA,EAAG;AAAA,MAC7F,EAAE,OAAO,MAAM,OAAO,MAAM,QAAQ,UAAW;AAAA,MAC/C,EAAE,OAAO,KAAK,OAAO,KAAK,QAAQ,MAAMA,cAAG,MAAC,UAAU,EAAE,KAAK,qBAAsB,CAAA,EAAG;AAAA,IACxF;AAGA,UAAM,mBAAmBC,cAAAA,IAAI,KAAK;AAClC,UAAM,aAAaA,cAAAA,IAAI,KAAK;AAC5B,UAAM,gBAAgBA,cAAAA,IAAI,EAAE;AAG5B,UAAM,mBAAmBA,cAAAA,IAAI,KAAK;AAGlC,UAAM,gBAAgBA,cAAAA,IAAI,KAAK;AAC/B,UAAM,WAAWA,cAAAA,IAAI,WAAW;AAGhC,UAAM,YAAYA,cAAAA,IAAI,EAAE;AAGxB,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,aAAa;AAAA,IACf,CAAC;AAGD,UAAM,sBAAsBA,cAAAA,IAAI;AAAA,MAC9B,OAAO;AAAA,MACP,SAAS;AAAA,MACT,QAAQ,CAAE;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AAEd,YAAM,QAAQF,cAAAA,MAAI,eAAe,eAAe;AAChD,gBAAU,QAAQ;AAClB,YAAM,UAAUA,cAAAA,MAAI,eAAe,iBAAiB;AAEpD,UAAI,SAAS;AAEX,2BAAmB,OAAO;AAG1B,4BAAoB,QAAQ;AAAA,UAC1B,OAAO,QAAQ,SAAS,QAAQ,WAAW;AAAA,UAC3C,SAAS,QAAQ,WAAW,QAAQ,eAAe;AAAA,UACnD,QAAQ,QAAQ,UAAU,CAAE;AAAA,UAC5B,UAAU,QAAQ,YAAY,QAAQ,gBAAgB;AAAA,QAC5D;AAGI,oBAAY,QAAQ;AAAA,UAClB,IAAI,SAAS,aAAa,KAAK,IAAK;AAAA,UACpC,OAAO;AAAA,UACP,aAAa,MAAM,QAAQ,SAAS,QAAQ,WAAW,MAAM;AAAA,QACnE;AAAA,MACG;AAGD,qBAAe,KAAK;AAGpB,iBAAW,MAAM;AACf,yBAAiB,QAAQ;AAAA,MAC1B,GAAE,GAAI;AAAA,IACT,CAAC;AAGD,UAAM,iBAAiB,OAAO,WAAW;AACvC,UAAI,CAAC;AAAQ;AAEb,UAAI;AACFA,sBAAAA,MAAI,YAAY,EAAE,OAAO,MAAO,CAAA;AAEhC,cAAM,MAAM,MAAMA,cAAG,MAAC,QAAQ;AAAA,UAC5B,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM,EAAE,OAAQ;AAAA,QACtB,CAAK;AAED,YAAI,IAAI,QAAQ,IAAI,KAAK,UAAU;AAEjCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,WAAW,IAAI,KAAK,aAAa;AAAA,YACxC,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAAA,QACF;AAEDA,sBAAG,MAAC,YAAW;AAAA,MAChB,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,oCAAc,YAAY,KAAK;AAC/BA,sBAAG,MAAC,YAAW;AAAA,MAChB;AAAA,IACH;AAGA,UAAM,qBAAqB,CAACG,iBAAgB;AAC1C,UAAI;AAEF,cAAM,cAAcH,cAAG,MAAC,eAAe,iBAAiB,KAAK,CAAA;AAG7D,cAAM,UAAU;AAAA,UACd,IAAI,KAAK,IAAK;AAAA,UACd,UAAUG,aAAY,YAAYA,aAAY;AAAA,UAC9C,SAASA,aAAY,WAAWA,aAAY;AAAA,UAC5C,OAAM,oBAAI,KAAM,GAAC,eAAgB;AAAA,UACjC,OAAO;AAAA,QACb;AAGI,oBAAY,QAAQ,OAAO;AAG3BH,sBAAAA,MAAI,eAAe,mBAAmB,WAAW;AAEjDA,sBAAA,MAAA,MAAA,OAAA,oCAAY,iBAAiB,OAAO;AAAA,MACrC,SAAQ,GAAG;AACVA,+EAAc,aAAa,CAAC;AAAA,MAC7B;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AAErB,YAAM,QAAQA,cAAAA,MAAI,eAAe,eAAe;AAEhDA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,8BAA8B;AAAA,MACvC,CAAG;AAAA,IACH;AAGA,UAAM,2BAA2B,CAAC,WAAW;AAC3CA,oBAAY,MAAA,MAAA,OAAA,oCAAA,UAAU,MAAM;AAG5B,uBAAiB,QAAQ;AACzB,iBAAW,QAAQ;AAEnB,UAAI,OAAO,SAAS,MAAM;AACxB,sBAAc,QAAQ;AAAA,MAC1B,WAAa,OAAO,SAAS,QAAQ;AACjC,sBAAc,QAAQ,eAAe,OAAO,OAAO,QAAQ;AAAA,MAC5D;AAGD,iBAAW,MAAM;AACf,yBAAiB,QAAQ;AAAA,MAC1B,GAAE,GAAI;AAAA,IACT;AAGA,UAAM,2BAA2B,CAAC,WAAW;AAC3CA,oBAAY,MAAA,MAAA,OAAA,oCAAA,UAAU,MAAM;AAE5B,UAAI,OAAO,SAAS,MAAM;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,WAAa,OAAO,SAAS,WAAW;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAEA,UAAM,SAAS,MAAMA,oBAAI;AAGzB,UAAM,gBAAgB,MAAM;AAC1B,uBAAiB,QAAQ;AAAA,IAC3B;AAGA,UAAM,YAAY,MAAM;AAEtB;AAGAA,oBAAAA,MAAI,cAAc;AAAA,QAChB,iBAAiB;AAAA,QACjB,OAAO,CAAC,mBAAmB,eAAe;AAAA,QAC1C,SAAS,MAAM;AACbA,wBAAAA,uDAAY,UAAU;AAAA,QACvB;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAc,MAAA,MAAA,SAAA,oCAAA,YAAY,GAAG;AAEjC,2BAAiB,QAAQ;AAAA,QACtB;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,cAAc,MAAM;AACxBA,oBAAAA,MAAY,MAAA,OAAA,oCAAA,QAAQ;AAGpB,YAAM,QAAQA,cAAAA,MAAI,eAAe,eAAe;AAEhD,UAAI,CAAC,OAAO;AACVA,sBAAAA,MAAA,MAAA,SAAA,oCAAc,kBAAkB;AAChC;AAAA,MACD;AAGD,YAAM,UAAUA,cAAAA,MAAI,eAAe,iBAAiB;AAGpD,UAAI,aAAa;AACjB,UAAI,aAAa;AAEjB,UAAI,SAAS;AAEX,YAAI,QAAQ,OAAO;AACjB,uBAAa,QAAQ;AAAA,QAC3B,WAAe,QAAQ,SAAS;AAE1B,uBAAa,QAAQ,QAAQ,UAAU,GAAG,EAAE,KAAK,QAAQ,QAAQ,SAAS,KAAK,QAAQ;AAAA,QAC7F,OAAW;AAEL,uBAAa,QAAQ,YAAY,QAAQ,gBAAgB;AAAA,QAC1D;AAGD,YAAI,QAAQ,UAAU,QAAQ,OAAO,SAAS,GAAG;AAC/C,uBAAa,QAAQ,OAAO,CAAC;AAAA,QAC9B;AAAA,MACL,OAAS;AACL,qBAAa;AAAA,MACd;AAGD,YAAM,MAAM;AACZ,UAAI,IAAI,YAAY;AAClB,YAAI,WAAW,YAAY;AAAA,UACzB,OAAO;AAAA,UACP,MAAM,8BAA8B;AAAA,UACpC,UAAU;AAAA,QAChB;AAAA,MACA,OAAS;AACL,YAAI,aAAa;AAAA,UACf,WAAW;AAAA,YACT,OAAO;AAAA,YACP,MAAM,8BAA8B;AAAA,YACpC,UAAU;AAAA,UACX;AAAA,QACP;AAAA,MACG;AAEDA,0BAAY,MAAA,OAAA,oCAAA,YAAY,IAAI,WAAW,SAAS;AAAA,IAClD;AAGA,UAAM,uBAAuB,MAAM;AACjC,YAAM,QAAQA,cAAAA,MAAI,eAAe,eAAe;AAChD,UAAI,OAAO;AAET;AAEA;AAEAA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,iCAAiC,KAAK;AAAA,UAC3C,SAAS,MAAM;AACbA,0BAAAA,MAAA,MAAA,OAAA,oCAAY,oBAAoB;AAAA,UACjC;AAAA,UACD,MAAM,CAAC,QAAQ;AACbA,0BAAc,MAAA,MAAA,SAAA,oCAAA,eAAe,GAAG;AAChCA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAK;AAAA,UACE;AAAA,QACP,CAAK;AAAA,MACL,OAAS;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,iBAAiB,MAAM;AAE3B,oBAAc,QAAQ;AACtB;IACF;AAGA,UAAM,aAAa,MAAM;AACvB,oBAAc,QAAQ;AAAA,IACxB;AAGA,UAAM,eAAe,MAAM;AACzBA,oBAAAA,MAAI,iBAAiB;AAAA,QACnB,MAAM,SAAS;AAAA,QACf,SAAS,WAAW;AACnBA,wBAAAA,MAAI,UAAU;AAAA,YACX,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,aAAa,MAAM;AACvBA,oBAAAA,MAAI,WAAW;AAAA,QACb,SAAS,CAAC,QAAQ;AAEhB,cAAI,CAAC,IAAI,YAAY,wBAAwB,GAAG;AAC9CA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,SAAS,MAAM;AACb;cACD;AAAA,cACD,MAAM,MAAM;AACVA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,SAAS;AAAA,kBACT,SAAS,CAACI,SAAQ;AAChB,wBAAIA,KAAI,SAAS;AACfJ,oCAAG,MAAC,YAAW;AAAA,oBAChB;AAAA,kBACF;AAAA,gBACf,CAAa;AAAA,cACF;AAAA,YACX,CAAS;AAAA,UACT,OAAa;AACL;UACD;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,MAAM;AAC9BA,oBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AAGnCA,oBAAAA,MAAI,aAAa;AAAA,QACf,KAAK;AAAA;AAAA,QACL,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,eAAe,KAAK;AAE1BA,0BAAAA,MAAI,uBAAuB;AAAA,cACzB,UAAU,IAAI;AAAA,cACd,SAAS,MAAM;AACbA,8BAAG,MAAC,YAAW;AACfA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACpB,CAAa;AAAA,cACF;AAAA,cACD,MAAM,CAAC,QAAQ;AACbA,8BAAG,MAAC,YAAW;AACfA,8BAAA,MAAA,MAAA,SAAA,oCAAc,QAAQ,GAAG;AACzBA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACpB,CAAa;AAAA,cACF;AAAA,YACX,CAAS;AAAA,UACT,OAAa;AACLA,0BAAG,MAAC,YAAW;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,QACD,MAAM,MAAM;AACVA,wBAAG,MAAC,YAAW;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrB,oBAAc,QAAQ;AAAA,IACxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvjBA,GAAG,WAAW,eAAe;"}