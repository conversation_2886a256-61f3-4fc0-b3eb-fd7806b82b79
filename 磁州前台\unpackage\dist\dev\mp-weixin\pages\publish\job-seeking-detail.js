"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
if (!Math) {
  ReportCard();
}
const ReportCard = () => "../../components/ReportCard.js";
const _sfc_main = {
  __name: "job-seeking-detail",
  setup(__props, { expose: __expose }) {
    const formatTime = (timestamp) => {
      const date = new Date(timestamp);
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    };
    const parseEducation = (education) => {
      if (typeof education !== "string") {
        return education;
      }
      const educationMap = {
        "1": "小学",
        "2": "初中",
        "3": "高中",
        "4": "中专",
        "5": "大专",
        "6": "本科",
        "7": "硕士",
        "8": "博士"
      };
      if (!isNaN(education) && educationMap[education]) {
        return educationMap[education];
      }
      return education;
    };
    const statusBarHeight = common_vendor.ref(20);
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const isCollected = common_vendor.ref(false);
    const seekingData = common_vendor.ref({
      id: "seeking12345",
      title: "求职行政文员",
      salary: "3000-4000元/月",
      tags: ["行政文员", "应届生", "可立即上岗"],
      publishTime: Date.now() - 864e5,
      // 1天前
      name: "张女士",
      age: 24,
      education: "本科",
      experience: "1年",
      intentions: [
        { label: "期望职位", value: "行政文员" },
        { label: "期望薪资", value: "3000-4000元/月" },
        { label: "期望城市", value: "磁县" },
        { label: "期望行业", value: "不限" }
      ],
      workExperience: [
        {
          company: "某科技有限公司",
          time: "2022.07-2023.06",
          position: "行政助理",
          description: "负责公司日常行政事务，包括文件整理、会议安排、接待来访等工作。"
        }
      ],
      educationInfo: [
        {
          school: "某大学",
          time: "2018.09-2022.06",
          major: "行政管理",
          degree: "本科"
        }
      ],
      skills: ["办公软件", "文档处理", "会议组织", "沟通协调"],
      selfEvaluation: "性格开朗，工作认真负责，有较强的沟通能力和团队协作精神。熟悉办公软件操作，能够独立完成日常行政工作。",
      contact: {
        name: "张女士",
        phone: "13912345678"
      }
    });
    const relatedSeekings = common_vendor.ref([]);
    const loadRelatedSeekings = () => {
      setTimeout(() => {
        relatedSeekings.value = [
          {
            id: "seeking001",
            title: "求职销售代表",
            salary: "4000-6000元/月",
            name: "李先生",
            avatar: "/static/images/avatar/男1.png",
            education: "大专",
            experience: "3年",
            tags: ["销售经验", "有车", "形象好"]
          },
          {
            id: "seeking002",
            title: "求职行政助理",
            salary: "3500-4500元/月",
            name: "王女士",
            avatar: "/static/images/avatar/女1.png",
            education: "本科",
            experience: "应届生",
            tags: ["办公软件熟练", "英语四级", "形象气质佳"]
          },
          {
            id: "seeking003",
            title: "求职前台文员",
            salary: "3000-4000元/月",
            name: "赵女士",
            avatar: "/static/images/avatar/女2.png",
            education: "大专",
            experience: "1年",
            tags: ["有相关经验", "普通话标准", "较强沟通能力"]
          }
        ];
      }, 500);
    };
    const navigateToSeekingDetail = (id) => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      if (id === options.id) {
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/publish/job-seeking-detail?id=${id}`
      });
    };
    const navigateToSeekingList = (e) => {
      var _a;
      if (e)
        e.stopPropagation();
      const seekingCategory = ((_a = seekingData.value.tags) == null ? void 0 : _a[0]) || "";
      common_vendor.index.navigateTo({
        url: `/subPackages/service/pages/filter?type=job-seeking&title=${encodeURIComponent("求职信息")}&category=${encodeURIComponent(seekingCategory)}&active=resume`
      });
    };
    const toggleCollect = () => {
      isCollected.value = !isCollected.value;
      if (isCollected.value) {
        common_vendor.index.showToast({
          title: "收藏成功",
          icon: "success"
        });
      }
    };
    const callPhone = () => {
      common_vendor.index.makePhoneCall({
        phoneNumber: seekingData.value.contact.phone,
        fail: () => {
          common_vendor.index.showToast({
            title: "拨打电话失败",
            icon: "none"
          });
        }
      });
    };
    const goToHome = () => {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    };
    const openChat = () => {
      if (!seekingData.value.contact || !seekingData.value.contact.id) {
        common_vendor.index.showToast({
          title: "无法获取求职者信息",
          icon: "none"
        });
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/chat/index?userId=${seekingData.value.contact.id}&username=${encodeURIComponent(seekingData.value.contact.name || "求职者")}`
      });
    };
    common_vendor.onMounted(() => {
      common_vendor.index.setNavigationBarTitle({
        title: "求职详情"
      });
      statusBarHeight.value = common_vendor.index.getSystemInfoSync().statusBarHeight || 20;
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      common_vendor.index.__f__("log", "at pages/publish/job-seeking-detail.vue:440", "求职详情页参数:", options);
      if (options.id) {
        const cacheKey = `job_seeking_${options.id}`;
        const cachedData = common_vendor.index.getStorageSync(cacheKey);
        if (cachedData) {
          common_vendor.index.__f__("log", "at pages/publish/job-seeking-detail.vue:449", "从缓存获取求职数据:", cachedData);
          try {
            const parsedData = typeof cachedData === "string" ? JSON.parse(cachedData) : cachedData;
            seekingData.value = {
              ...seekingData.value,
              ...parsedData
            };
            if (typeof seekingData.value.education === "string") {
              seekingData.value.education = parseEducation(seekingData.value.education);
            }
            common_vendor.index.__f__("log", "at pages/publish/job-seeking-detail.vue:465", "处理后的求职数据:", seekingData.value);
          } catch (e) {
            common_vendor.index.__f__("error", "at pages/publish/job-seeking-detail.vue:467", "解析缓存数据失败:", e);
          }
        } else {
          common_vendor.index.__f__("log", "at pages/publish/job-seeking-detail.vue:471", "从服务器获取求职详情:", options.id);
          setTimeout(() => {
            const serverData = {
              // 服务端返回的数据...
            };
            seekingData.value = {
              ...seekingData.value,
              ...serverData
            };
            if (typeof seekingData.value.education === "string") {
              seekingData.value.education = parseEducation(seekingData.value.education);
            }
            common_vendor.index.setStorageSync(cacheKey, JSON.stringify(seekingData.value));
          }, 100);
        }
      }
      setTimeout(() => {
        try {
          const query = common_vendor.index.createSelectorQuery();
          query.select("#shareButton").boundingClientRect((data) => {
            if (data) {
              common_vendor.index.__f__("log", "at pages/publish/job-seeking-detail.vue:504", "获取到分享按钮元素");
            }
          }).exec();
        } catch (err) {
          common_vendor.index.__f__("error", "at pages/publish/job-seeking-detail.vue:509", "获取分享按钮失败:", err);
        }
      }, 500);
      loadRelatedSeekings();
    });
    const onShareAppMessage = () => {
      return {
        title: seekingData.value.title,
        path: `/pages/publish/job-seeking-detail?id=${seekingData.value.id}`
      };
    };
    __expose({
      onShareAppMessage
    });
    const posterImagePath = common_vendor.ref("");
    const showPosterFlag = common_vendor.ref(false);
    const generateShareImage = () => {
      common_vendor.index.showLoading({
        title: "生成中..."
      });
      const posterData = {
        title: seekingData.value.title,
        salary: seekingData.value.salary,
        name: seekingData.value.name,
        gender: "女",
        age: seekingData.value.age,
        education: seekingData.value.education,
        experience: seekingData.value.experience,
        phone: seekingData.value.contact.phone,
        skills: seekingData.value.skills ? seekingData.value.skills.substring(0, 60) + "..." : "",
        qrcode: "/static/images/tabbar/客服微信.png",
        logo: "/static/images/tabbar/求职.png",
        bgImage: "/static/images/banner/banner-1.png"
      };
      const ctx = common_vendor.index.createCanvasContext("posterCanvas");
      ctx.save();
      ctx.drawImage(posterData.bgImage, 0, 0, 600, 400);
      ctx.setFillStyle("rgba(0, 0, 0, 0.35)");
      ctx.fillRect(0, 0, 600, 900);
      ctx.restore();
      ctx.save();
      ctx.setFillStyle("#ffffff");
      ctx.fillRect(30, 280, 540, 550);
      ctx.restore();
      ctx.save();
      ctx.beginPath();
      ctx.arc(300, 200, 80, 0, 2 * Math.PI);
      ctx.setFillStyle("#ffffff");
      ctx.fill();
      ctx.clip();
      ctx.drawImage(posterData.logo, 220, 120, 160, 160);
      ctx.restore();
      ctx.setFillStyle("#333333");
      ctx.setFontSize(32);
      ctx.setTextAlign("center");
      ctx.fillText(posterData.title, 300, 350);
      ctx.setFillStyle("#FF6B6B");
      ctx.setFontSize(28);
      ctx.fillText(posterData.salary, 300, 400);
      ctx.beginPath();
      ctx.setStrokeStyle("#eeeeee");
      ctx.setLineWidth(2);
      ctx.moveTo(100, 430);
      ctx.lineTo(500, 430);
      ctx.stroke();
      ctx.setFillStyle("#666666");
      ctx.setFontSize(24);
      ctx.setTextAlign("left");
      const personInfo = `${posterData.name} | ${posterData.gender} | ${posterData.age}岁`;
      ctx.fillText(personInfo, 80, 480);
      const eduExp = `${posterData.education} | ${posterData.experience}`;
      ctx.fillText(eduExp, 80, 520);
      const wrapText = (ctx2, text, x, y, maxWidth, lineHeight) => {
        if (text.length === 0)
          return;
        const words = text.split("");
        let line = "";
        let testLine = "";
        let lineCount = 0;
        for (let n = 0; n < words.length; n++) {
          testLine += words[n];
          const metrics = ctx2.measureText(testLine);
          const testWidth = metrics.width;
          if (testWidth > maxWidth && n > 0) {
            ctx2.fillText(line, x, y + lineCount * lineHeight);
            line = words[n];
            testLine = words[n];
            lineCount++;
            if (lineCount >= 3) {
              line += "...";
              ctx2.fillText(line, x, y + lineCount * lineHeight);
              break;
            }
          } else {
            line = testLine;
          }
        }
        if (lineCount < 3) {
          ctx2.fillText(line, x, y + lineCount * lineHeight);
        }
      };
      ctx.setFillStyle("#666666");
      ctx.fillText("技能特长:", 80, 560);
      wrapText(ctx, posterData.skills, 80, 600, 440, 35);
      if (posterData.phone) {
        ctx.fillText("联系电话: " + posterData.phone, 80, 680);
      }
      ctx.drawImage(posterData.qrcode, 225, 720, 150, 150);
      ctx.setFillStyle("#999999");
      ctx.setFontSize(20);
      ctx.setTextAlign("center");
      ctx.fillText("长按识别二维码查看详情", 300, 880);
      ctx.setFillStyle("#333333");
      ctx.setFontSize(24);
      ctx.fillText("磁县同城 - 求职信息", 300, 840);
      ctx.draw(false, () => {
        setTimeout(() => {
          common_vendor.index.canvasToTempFilePath({
            canvasId: "posterCanvas",
            success: (res) => {
              common_vendor.index.hideLoading();
              showPosterModal(res.tempFilePath);
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/publish/job-seeking-detail.vue:697", "生成海报失败", err);
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "生成海报失败",
                icon: "none"
              });
            }
          });
        }, 800);
      });
    };
    const showPosterModal = (posterPath) => {
      posterImagePath.value = posterPath;
      showPosterFlag.value = true;
      common_vendor.index.showModal({
        title: "海报已生成",
        content: "海报已生成，是否保存到相册？",
        confirmText: "保存",
        success: (res) => {
          if (res.confirm) {
            savePosterToAlbum(posterPath);
          } else {
            common_vendor.index.previewImage({
              urls: [posterPath],
              current: posterPath
            });
          }
        }
      });
    };
    const savePosterToAlbum = (posterPath) => {
      common_vendor.index.showLoading({
        title: "正在保存..."
      });
      common_vendor.index.saveImageToPhotosAlbum({
        filePath: posterPath,
        success: () => {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "已保存到相册",
            icon: "success"
          });
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at pages/publish/job-seeking-detail.vue:749", "保存失败", err);
          if (err.errMsg.indexOf("auth deny") > -1) {
            common_vendor.index.showModal({
              title: "提示",
              content: "保存失败，请授权相册权限后重试",
              confirmText: "去设置",
              success: (res) => {
                if (res.confirm) {
                  common_vendor.index.openSetting();
                }
              }
            });
          } else {
            common_vendor.index.showToast({
              title: "保存失败",
              icon: "none"
            });
          }
        }
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$5,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_vendor.t(seekingData.value.title),
        e: common_vendor.t(seekingData.value.salary),
        f: common_vendor.f(seekingData.value.tags, (tag, index, i0) => {
          return {
            a: common_vendor.t(tag),
            b: index
          };
        }),
        g: common_vendor.t(formatTime(seekingData.value.publishTime)),
        h: common_vendor.t(seekingData.value.name),
        i: common_vendor.t(seekingData.value.age),
        j: common_vendor.t(seekingData.value.education),
        k: common_vendor.t(seekingData.value.experience),
        l: common_vendor.f(seekingData.value.intentions, (item, index, i0) => {
          return {
            a: common_vendor.t(item.label),
            b: common_vendor.t(item.value),
            c: index
          };
        }),
        m: common_vendor.f(seekingData.value.workExperience, (item, index, i0) => {
          return {
            a: common_vendor.t(item.company),
            b: common_vendor.t(item.time),
            c: common_vendor.t(item.position),
            d: common_vendor.t(item.description),
            e: index
          };
        }),
        n: common_vendor.f(seekingData.value.skills, (skill, index, i0) => {
          return {
            a: common_vendor.t(skill),
            b: index
          };
        }),
        o: common_vendor.t(seekingData.value.selfEvaluation),
        p: common_vendor.t(seekingData.value.contact.name),
        q: common_vendor.t(seekingData.value.contact.phone),
        r: common_vendor.o(callPhone),
        s: common_vendor.f(relatedSeekings.value.slice(0, 3), (seeking, index, i0) => {
          return common_vendor.e({
            a: seeking.avatar,
            b: common_vendor.t(seeking.title),
            c: common_vendor.t(seeking.name),
            d: common_vendor.t(seeking.education),
            e: common_vendor.t(seeking.experience),
            f: common_vendor.f(seeking.tags.slice(0, 2), (tag, tagIndex, i1) => {
              return {
                a: common_vendor.t(tag),
                b: tagIndex
              };
            }),
            g: seeking.tags.length > 2
          }, seeking.tags.length > 2 ? {
            h: common_vendor.t(seeking.tags.length - 2)
          } : {}, {
            i: common_vendor.t(seeking.salary),
            j: index,
            k: common_vendor.o(($event) => navigateToSeekingDetail(seeking.id), index)
          });
        }),
        t: relatedSeekings.value.length === 0
      }, relatedSeekings.value.length === 0 ? {
        v: common_assets._imports_1$3
      } : {}, {
        w: relatedSeekings.value.length > 0
      }, relatedSeekings.value.length > 0 ? {
        x: common_vendor.o(navigateToSeekingList)
      } : {}, {
        y: common_assets._imports_12,
        z: common_vendor.o(goToHome),
        A: common_assets._imports_3$2,
        B: common_vendor.o(toggleCollect),
        C: common_assets._imports_3$3,
        D: common_assets._imports_14,
        E: common_vendor.o(openChat),
        F: common_vendor.o(callPhone),
        G: common_assets._imports_10,
        H: common_vendor.o(generateShareImage)
      });
    };
  }
};
_sfc_main.__runtimeHooks = 2;
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/publish/job-seeking-detail.js.map
