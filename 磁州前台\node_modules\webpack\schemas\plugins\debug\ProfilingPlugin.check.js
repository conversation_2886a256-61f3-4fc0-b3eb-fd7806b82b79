/*
 * This file was automatically generated.
 * DO NOT MODIFY BY HAND.
 * Run `yarn fix:special` to update
 */
const r=/^(?:[A-Za-z]:[\\/]|\\\\|\/)/;function t(e,{instancePath:a="",parentData:o,parentDataProperty:n,rootData:s=e}={}){if(!e||"object"!=typeof e||Array.isArray(e))return t.errors=[{params:{type:"object"}}],!1;{const a=0;for(const r in e)if("outputPath"!==r)return t.errors=[{params:{additionalProperty:r}}],!1;if(0===a&&void 0!==e.outputPath){let a=e.outputPath;if("string"!=typeof a)return t.errors=[{params:{type:"string"}}],!1;if(a.includes("!")||!0!==r.test(a))return t.errors=[{params:{}}],!1}}return t.errors=null,!0}module.exports=t,module.exports.default=t;