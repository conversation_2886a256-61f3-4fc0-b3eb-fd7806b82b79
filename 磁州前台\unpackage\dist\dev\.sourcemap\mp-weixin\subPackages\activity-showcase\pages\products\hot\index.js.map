{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/products/hot/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNccHJvZHVjdHNcaG90XGluZGV4LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"product-detail-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\">\n      <view class=\"navbar-bg\"></view>\n      <view class=\"navbar-content\">\n        <view class=\"back-btn\" @click=\"navigateBack\">\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n            <path d=\"M19 12H5M12 19l-7-7 7-7\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n          </svg>\n        </view>\n        <view class=\"navbar-title\">商品详情</view>\n        <view class=\"navbar-right\">\n          <view class=\"share-btn\" @click=\"shareProduct\">\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n              <path d=\"M4 12v8a2 2 0 002 2h12a2 2 0 002-2v-8M16 6l-4-4-4 4M12 2v13\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n            </svg>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 内容区域 -->\n    <scroll-view class=\"content-scroll\" scroll-y :scroll-with-animation=\"true\">\n      <!-- 商品轮播图 -->\n      <swiper class=\"product-swiper\" indicator-dots autoplay circular\n        :indicator-color=\"'rgba(255, 255, 255, 0.6)'\"\n        :indicator-active-color=\"'#ffffff'\"\n        :interval=\"5000\"\n        :duration=\"500\"\n      >\n        <swiper-item v-for=\"(image, index) in product.images\" :key=\"index\">\n          <view class=\"swiper-item\">\n            <image class=\"product-image\" :src=\"image\" mode=\"aspectFill\"></image>\n          </view>\n        </swiper-item>\n      </swiper>\n\n      <!-- 价格信息 -->\n      <view class=\"price-section\">\n        <view class=\"price-row\">\n          <view class=\"current-price\">¥{{ product.price.toFixed(2) }}</view>\n          <view class=\"original-price\">¥{{ product.originalPrice.toFixed(2) }}</view>\n          <view class=\"discount-badge\">{{ discount }}折</view>\n        </view>\n        <view class=\"sales-info\">已售 {{ product.soldCount }} | 库存 {{ product.stock }}</view>\n      </view>\n\n      <!-- 商品标题 -->\n      <view class=\"product-title-section\">\n        <view class=\"product-title\">{{ product.title }}</view>\n        <view class=\"collect-btn\" @click=\"toggleFavorite\">\n          <view class=\"collect-icon\" :class=\"{ active: isFavorite }\">\n            <svg viewBox=\"0 0 24 24\" width=\"22\" height=\"22\">\n              <path d=\"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\"\n                :fill=\"isFavorite ? '#FF3B69' : 'none'\" \n                :stroke=\"isFavorite ? '#FF3B69' : '#8E8E93'\" \n                stroke-width=\"2\" />\n            </svg>\n          </view>\n          <text>{{ isFavorite ? '已收藏' : '收藏' }}</text>\n        </view>\n      </view>\n\n      <!-- 商品标签 -->\n      <view class=\"tags-section\">\n        <view class=\"tag-item\" v-for=\"(tag, index) in product.labels\" :key=\"index\">\n          <view class=\"tag-badge\" :class=\"tag.type\">{{ tag.text }}</view>\n        </view>\n      </view>\n\n      <!-- 促销信息 -->\n      <view class=\"promotion-section\" v-if=\"product.promotions && product.promotions.length > 0\">\n        <view class=\"section-title\">促销信息</view>\n        <view class=\"promotion-list\">\n          <view class=\"promotion-item\" v-for=\"(promo, index) in product.promotions\" :key=\"index\">\n            <view class=\"promo-badge\" :class=\"promo.type\">{{ promo.type === 'coupon' ? '券' : '减' }}</view>\n            <text class=\"promo-text\">{{ promo.text }}</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 选择规格 -->\n      <view class=\"spec-section\" @click=\"openSpecSelector\">\n        <view class=\"section-title\">选择规格</view>\n        <view class=\"spec-content\">\n          <text class=\"spec-text\">{{ selectedSpec || '请选择规格数量' }}</text>\n          <svg class=\"arrow-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n            <path d=\"M9 18l6-6-6-6\" stroke=\"#8E8E93\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n          </svg>\n        </view>\n      </view>\n\n      <!-- 店铺信息 -->\n      <view class=\"shop-section\" @click=\"navigateToShop\">\n        <image class=\"shop-logo\" :src=\"product.shopLogo\" mode=\"aspectFill\"></image>\n        <view class=\"shop-info\">\n          <view class=\"shop-name\">{{ product.shopName }}</view>\n          <view class=\"shop-rating\">\n            <view class=\"rating-stars\">\n              <view class=\"star\" v-for=\"i in 5\" :key=\"i\">\n                <svg viewBox=\"0 0 24 24\" width=\"14\" height=\"14\">\n                  <path d=\"M12 2l2.4 7.2H22l-6 4.8 2.4 7.2-6-4.8-6 4.8 2.4-7.2-6-4.8h7.6z\" :fill=\"i <= shop.rating ? '#FF9500' : '#E0E0E0'\" />\n                </svg>\n              </view>\n            </view>\n            <text class=\"rating-text\">{{ shop.rating.toFixed(1) }}</text>\n          </view>\n        </view>\n        <view class=\"enter-shop-btn\">\n          <text>进店</text>\n          <svg class=\"arrow-icon\" viewBox=\"0 0 24 24\" width=\"14\" height=\"14\">\n            <path d=\"M9 18l6-6-6-6\" stroke=\"#FF3B69\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n          </svg>\n        </view>\n      </view>\n\n      <!-- 商品详情 -->\n      <view class=\"detail-section\">\n        <view class=\"detail-title\">商品详情</view>\n        <view class=\"detail-content\">\n          <rich-text :nodes=\"product.description\"></rich-text>\n          <view class=\"detail-images\">\n            <image \n              v-for=\"(image, index) in product.detailImages\" \n              :key=\"index\"\n              :src=\"image\"\n              mode=\"widthFix\"\n              class=\"detail-image\"\n            ></image>\n          </view>\n        </view>\n      </view>\n\n      <!-- 商品评价 -->\n      <view class=\"reviews-section\">\n        <view class=\"section-header\">\n          <view class=\"section-title\">商品评价</view>\n          <view class=\"view-all\" @click=\"navigateToReviews\">\n            <text>查看全部</text>\n            <svg class=\"arrow-icon\" viewBox=\"0 0 24 24\" width=\"14\" height=\"14\">\n              <path d=\"M9 18l6-6-6-6\" stroke=\"#8E8E93\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n            </svg>\n          </view>\n        </view>\n        <view class=\"reviews-list\" v-if=\"reviews.length > 0\">\n          <view class=\"review-item\" v-for=\"(review, index) in reviews.slice(0, 2)\" :key=\"index\">\n            <view class=\"review-header\">\n              <image class=\"user-avatar\" :src=\"review.userAvatar\" mode=\"aspectFill\"></image>\n              <view class=\"review-user-info\">\n                <view class=\"user-name\">{{ review.userName }}</view>\n                <view class=\"review-rating\">\n                  <view class=\"star\" v-for=\"i in 5\" :key=\"i\">\n                    <svg viewBox=\"0 0 24 24\" width=\"12\" height=\"12\">\n                      <path d=\"M12 2l2.4 7.2H22l-6 4.8 2.4 7.2-6-4.8-6 4.8 2.4-7.2-6-4.8h7.6z\" :fill=\"i <= review.rating ? '#FF9500' : '#E0E0E0'\" />\n                    </svg>\n                  </view>\n                </view>\n              </view>\n              <view class=\"review-time\">{{ review.time }}</view>\n            </view>\n            <view class=\"review-content\">{{ review.content }}</view>\n            <view class=\"review-images\" v-if=\"review.images && review.images.length > 0\">\n              <image \n                v-for=\"(img, imgIndex) in review.images\" \n                :key=\"imgIndex\"\n                :src=\"img\"\n                mode=\"aspectFill\"\n                class=\"review-image\"\n                @click=\"previewImage(review.images, imgIndex)\"\n              ></image>\n            </view>\n          </view>\n        </view>\n        <view class=\"empty-reviews\" v-else>\n          <text>暂无评价</text>\n        </view>\n      </view>\n\n      <!-- 相关推荐 -->\n      <view class=\"recommend-section\">\n        <view class=\"section-title\">相关推荐</view>\n        <scroll-view class=\"recommend-scroll\" scroll-x show-scrollbar=\"false\">\n          <view class=\"recommend-list\">\n            <view class=\"recommend-item\" v-for=\"(item, index) in relatedProducts\" :key=\"index\" @click=\"viewProductDetail(item)\">\n              <image class=\"recommend-image\" :src=\"item.coverImage\" mode=\"aspectFill\"></image>\n              <view class=\"recommend-info\">\n                <text class=\"recommend-title\">{{ item.title }}</text>\n                <view class=\"recommend-price\">¥{{ item.price.toFixed(2) }}</view>\n              </view>\n            </view>\n          </view>\n        </scroll-view>\n      </view>\n\n      <!-- 底部安全区域 -->\n      <view class=\"safe-area-bottom\"></view>\n    </scroll-view>\n\n    <!-- 底部操作栏 -->\n    <view class=\"bottom-bar\">\n      <view class=\"bottom-left\">\n        <view class=\"action-btn shop-btn\" @click=\"navigateToShop\">\n          <svg viewBox=\"0 0 24 24\" width=\"20\" height=\"20\">\n            <path d=\"M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z\" stroke=\"#8E8E93\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" fill=\"none\" />\n            <path d=\"M9 22V12h6v10\" stroke=\"#8E8E93\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" fill=\"none\" />\n          </svg>\n          <text>店铺</text>\n        </view>\n        <view class=\"action-btn cart-btn\" @click=\"navigateToCart\">\n          <svg viewBox=\"0 0 24 24\" width=\"20\" height=\"20\">\n            <path d=\"M9 22a1 1 0 100-2 1 1 0 000 2zM20 22a1 1 0 100-2 1 1 0 000 2zM1 1h4l2.68 13.39a2 2 0 002 1.61h9.72a2 2 0 002-1.61L23 6H6\" stroke=\"#8E8E93\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" fill=\"none\" />\n          </svg>\n          <text>购物车</text>\n          <view class=\"cart-badge\" v-if=\"cartCount > 0\">{{ cartCount }}</view>\n        </view>\n      </view>\n      <view class=\"bottom-right\">\n        <view class=\"add-to-cart-btn\" @click=\"addToCart\">加入购物车</view>\n        <view class=\"buy-now-btn\" @click=\"buyNow\">立即购买</view>\n      </view>\n    </view>\n\n    <!-- 规格选择弹窗 -->\n    <view class=\"spec-popup\" v-if=\"showSpecSelector\" @click.stop=\"closeSpecSelector\">\n      <view class=\"spec-popup-content\" @click.stop>\n        <view class=\"spec-popup-header\">\n          <image class=\"spec-product-image\" :src=\"product.coverImage\" mode=\"aspectFill\"></image>\n          <view class=\"spec-product-info\">\n            <view class=\"spec-product-price\">¥{{ selectedSku ? selectedSku.price.toFixed(2) : product.price.toFixed(2) }}</view>\n            <view class=\"spec-product-stock\">库存: {{ selectedSku ? selectedSku.stock : product.stock }}</view>\n            <view class=\"spec-selected\">{{ getSelectedSpecText() }}</view>\n          </view>\n          <view class=\"spec-close-btn\" @click=\"closeSpecSelector\">\n            <svg viewBox=\"0 0 24 24\" width=\"20\" height=\"20\">\n              <path d=\"M18 6L6 18M6 6l12 12\" stroke=\"#8E8E93\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" />\n            </svg>\n          </view>\n        </view>\n        \n        <view class=\"spec-options\">\n          <view class=\"spec-option-group\" v-for=\"(group, groupIndex) in product.specs\" :key=\"groupIndex\">\n            <view class=\"spec-option-title\">{{ group.title }}</view>\n            <view class=\"spec-option-list\">\n              <view \n                class=\"spec-option-item\" \n                v-for=\"(option, optionIndex) in group.options\" \n                :key=\"optionIndex\"\n                :class=\"{ active: selectedSpecMap[group.title] === option, disabled: !isOptionAvailable(group.title, option) }\"\n                @click=\"selectSpec(group.title, option)\"\n              >\n                {{ option }}\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"quantity-selector\">\n          <view class=\"quantity-title\">数量</view>\n          <view class=\"quantity-control\">\n            <view class=\"quantity-btn minus\" :class=\"{ disabled: quantity <= 1 }\" @click=\"decreaseQuantity\">\n              <svg viewBox=\"0 0 24 24\" width=\"20\" height=\"20\">\n                <path d=\"M5 12h14\" stroke=\"#8E8E93\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" />\n              </svg>\n            </view>\n            <input class=\"quantity-input\" type=\"number\" v-model.number=\"quantity\" />\n            <view class=\"quantity-btn plus\" :class=\"{ disabled: quantity >= maxQuantity }\" @click=\"increaseQuantity\">\n              <svg viewBox=\"0 0 24 24\" width=\"20\" height=\"20\">\n                <path d=\"M12 5v14M5 12h14\" stroke=\"#8E8E93\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" />\n              </svg>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"spec-popup-footer\">\n          <view class=\"add-to-cart-btn\" @click=\"confirmAddToCart\">加入购物车</view>\n          <view class=\"buy-now-btn\" @click=\"confirmBuyNow\">立即购买</view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue';\n\n// 商品数据\nconst product = ref({\n  id: 1,\n  title: 'iPhone 14 Pro 深空黑 256G',\n  price: 7999,\n  originalPrice: 8999,\n  stock: 986,\n  soldCount: 235,\n  coverImage: 'https://via.placeholder.com/300x300',\n  images: [\n    'https://via.placeholder.com/750x750',\n    'https://via.placeholder.com/750x750',\n    'https://via.placeholder.com/750x750'\n  ],\n  detailImages: [\n    'https://via.placeholder.com/750x1500',\n    'https://via.placeholder.com/750x1200'\n  ],\n  shopName: 'Apple授权专卖店',\n  shopLogo: 'https://via.placeholder.com/100',\n  description: '<p>iPhone 14 Pro 搭载 A16 仿生芯片，超强性能与电池续航，支持 5G 网络。</p><p>4800万像素主摄 + 1200万像素超广角 + 1200万像素长焦，带来专业级摄影体验。</p>',\n  labels: [\n    { type: 'discount', text: '满3000减300' },\n    { type: 'new', text: '新品' }\n  ],\n  promotions: [\n    { type: 'discount', text: '满3000减300' },\n    { type: 'coupon', text: '新用户立减200元' }\n  ],\n  specs: [\n    {\n      title: '颜色',\n      options: ['深空黑', '银色', '金色', '暗紫色']\n    },\n    {\n      title: '容量',\n      options: ['128GB', '256GB', '512GB', '1TB']\n    }\n  ],\n  skus: [\n    { specs: { '颜色': '深空黑', '容量': '256GB' }, price: 7999, stock: 986 },\n    { specs: { '颜色': '深空黑', '容量': '512GB' }, price: 9999, stock: 568 },\n    { specs: { '颜色': '银色', '容量': '256GB' }, price: 7999, stock: 756 },\n    { specs: { '颜色': '银色', '容量': '512GB' }, price: 9999, stock: 325 },\n    { specs: { '颜色': '金色', '容量': '256GB' }, price: 7999, stock: 421 },\n    { specs: { '颜色': '金色', '容量': '512GB' }, price: 9999, stock: 132 },\n    { specs: { '颜色': '暗紫色', '容量': '256GB' }, price: 7999, stock: 675 },\n    { specs: { '颜色': '暗紫色', '容量': '512GB' }, price: 9999, stock: 289 }\n  ]\n});\n\n// 店铺数据\nconst shop = ref({\n  id: 1,\n  name: 'Apple授权专卖店',\n  logo: 'https://via.placeholder.com/100',\n  rating: 4.8,\n  productCount: 128,\n  followersCount: 5689\n});\n\n// 评价数据\nconst reviews = ref([\n  {\n    id: 1,\n    userName: '用户123456',\n    userAvatar: 'https://via.placeholder.com/50',\n    rating: 5,\n    content: '手机非常好用，外观漂亮，系统流畅，续航也很棒，相机拍照效果很赞，总体来说非常满意！',\n    time: '2023-07-15',\n    images: [\n      'https://via.placeholder.com/200x200',\n      'https://via.placeholder.com/200x200',\n      'https://via.placeholder.com/200x200'\n    ]\n  },\n  {\n    id: 2,\n    userName: '磁县用户',\n    userAvatar: 'https://via.placeholder.com/50',\n    rating: 4,\n    content: '手机整体不错，就是价格有点贵，但质量和体验确实很好，值得购买。',\n    time: '2023-07-10',\n    images: []\n  }\n]);\n\n// 相关推荐\nconst relatedProducts = ref([\n  {\n    id: 2,\n    title: 'iPhone 14 银色 128G',\n    coverImage: 'https://via.placeholder.com/300x300',\n    price: 6999,\n    originalPrice: 7599\n  },\n  {\n    id: 3,\n    title: 'iPhone 14 Pro Max 暗紫色 256G',\n    coverImage: 'https://via.placeholder.com/300x300',\n    price: 8999,\n    originalPrice: 9999\n  },\n  {\n    id: 4,\n    title: 'AirPods Pro 2代',\n    coverImage: 'https://via.placeholder.com/300x300',\n    price: 1999,\n    originalPrice: 2199\n  },\n  {\n    id: 5,\n    title: 'Apple Watch Series 8',\n    coverImage: 'https://via.placeholder.com/300x300',\n    price: 2999,\n    originalPrice: 3299\n  }\n]);\n\n// 页面状态\nconst isFavorite = ref(false);\nconst cartCount = ref(5);\nconst showSpecSelector = ref(false);\nconst selectedSpecMap = ref({});\nconst quantity = ref(1);\nconst maxQuantity = ref(10);\n\n// 计算折扣\nconst discount = computed(() => {\n  return Math.floor((product.value.price / product.value.originalPrice) * 10);\n});\n\n// 选中的规格文本\nconst selectedSpec = computed(() => {\n  const specs = Object.entries(selectedSpecMap.value).map(([key, value]) => `${key}:${value}`);\n  return specs.length > 0 ? specs.join(', ') : '';\n});\n\n// 根据选中的规格获取SKU\nconst selectedSku = computed(() => {\n  const specs = selectedSpecMap.value;\n  const specKeys = Object.keys(specs);\n  \n  // 检查是否所有规格都已选择\n  if (specKeys.length !== product.value.specs.length) {\n    return null;\n  }\n  \n  // 查找匹配的SKU\n  return product.value.skus.find(sku => {\n    return specKeys.every(key => sku.specs[key] === specs[key]);\n  });\n});\n\n// 选择规格\nfunction selectSpec(title, option) {\n  if (!isOptionAvailable(title, option)) return;\n  \n  selectedSpecMap.value = {\n    ...selectedSpecMap.value,\n    [title]: option\n  };\n}\n\n// 判断规格选项是否可选\nfunction isOptionAvailable(title, option) {\n  // 复制当前已选规格\n  const currentSpecs = { ...selectedSpecMap.value, [title]: option };\n  const selectedKeys = Object.keys(currentSpecs);\n  \n  // 如果不是全部规格都已选择，则判断是否有对应的SKU\n  if (selectedKeys.length < product.value.specs.length) {\n    return product.value.skus.some(sku => {\n      return selectedKeys.every(key => sku.specs[key] === currentSpecs[key]);\n    });\n  }\n  \n  // 如果全部规格都已选择，则判断是否有库存\n  const matchedSku = product.value.skus.find(sku => {\n    return selectedKeys.every(key => sku.specs[key] === currentSpecs[key]);\n  });\n  \n  return matchedSku && matchedSku.stock > 0;\n}\n\n// 获取已选规格文本\nfunction getSelectedSpecText() {\n  const specs = Object.entries(selectedSpecMap.value);\n  if (specs.length === 0) {\n    return '请选择规格';\n  }\n  return specs.map(([key, value]) => `已选: ${key} ${value}`).join(', ');\n}\n\n// 增加数量\nfunction increaseQuantity() {\n  if (quantity.value < maxQuantity.value) {\n    quantity.value++;\n  }\n}\n\n// 减少数量\nfunction decreaseQuantity() {\n  if (quantity.value > 1) {\n    quantity.value--;\n  }\n}\n\n// 打开规格选择器\nfunction openSpecSelector() {\n  showSpecSelector.value = true;\n}\n\n// 关闭规格选择器\nfunction closeSpecSelector() {\n  showSpecSelector.value = false;\n}\n\n// 添加到购物车\nfunction addToCart() {\n  openSpecSelector();\n}\n\n// 确认添加到购物车\nfunction confirmAddToCart() {\n  if (!selectedSku.value) {\n    uni.showToast({\n      title: '请选择完整规格',\n      icon: 'none'\n    });\n    return;\n  }\n  \n  cartCount.value += quantity.value;\n  \n  uni.showToast({\n    title: '已添加到购物车',\n    icon: 'success'\n  });\n  \n  closeSpecSelector();\n}\n\n// 立即购买\nfunction buyNow() {\n  openSpecSelector();\n}\n\n// 确认立即购买\nfunction confirmBuyNow() {\n  if (!selectedSku.value) {\n    uni.showToast({\n      title: '请选择完整规格',\n      icon: 'none'\n    });\n    return;\n  }\n  \n  navigateTo('/subPackages/activity-showcase/pages/orders/confirm?productId=' + product.value.id);\n  closeSpecSelector();\n}\n\n// 切换收藏状态\nfunction toggleFavorite() {\n  isFavorite.value = !isFavorite.value;\n  uni.showToast({\n    title: isFavorite.value ? '已收藏' : '已取消收藏',\n    icon: 'none'\n  });\n}\n\n// 分享商品\nfunction shareProduct() {\n  uni.showShareMenu({\n    withShareTicket: true,\n    success() {\n      console.log('打开分享菜单成功');\n    },\n    fail() {\n      uni.showToast({\n        title: '分享功能开发中',\n        icon: 'none'\n      });\n    }\n  });\n}\n\n// 预览图片\nfunction previewImage(images, current) {\n  uni.previewImage({\n    urls: images,\n    current: current\n  });\n}\n\n// 导航到店铺\nfunction navigateToShop() {\n  navigateTo(`/subPackages/activity-showcase/pages/shops/detail?id=${shop.value.id}`);\n}\n\n// 导航到购物车\nfunction navigateToCart() {\n  navigateTo('/subPackages/activity-showcase/pages/cart/index');\n}\n\n// 导航到评价列表\nfunction navigateToReviews() {\n  navigateTo(`/subPackages/activity-showcase/pages/products/reviews?id=${product.value.id}`);\n}\n\n// 查看商品详情\nfunction viewProductDetail(product) {\n  navigateTo(`/subPackages/activity-showcase/pages/products/detail?id=${product.id}`);\n}\n\n// 导航通用方法\nfunction navigateTo(url) {\n  uni.navigateTo({\n    url,\n    fail: (err) => {\n      console.error('导航失败:', err);\n      uni.showToast({\n        title: '页面跳转失败',\n        icon: 'none'\n      });\n    }\n  });\n}\n\n// 返回上一页\nfunction navigateBack() {\n  uni.navigateBack();\n}\n\n// 页面加载\nonMounted(() => {\n  // 从路由参数获取商品ID\n  const productId = Number(getApp().globalData.route.query.id) || 1;\n  console.log('加载商品详情:', productId);\n  \n  // 这里可以根据ID请求商品数据\n  // 目前使用模拟数据\n});\n</script>\n\n<style lang=\"scss\" scoped>\n.product-detail-container {\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background-color: #F2F2F7;\n}\n\n/* 自定义导航栏 */\n.custom-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: calc(var(--status-bar-height, 25px) + 62px);\n  width: 100%;\n  z-index: 100;\n  \n  .navbar-bg {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);\n    backdrop-filter: blur(10px);\n    -webkit-backdrop-filter: blur(10px);\n    box-shadow: 0 4px 6px rgba(255,59,105,0.15);\n  }\n  \n  .navbar-content {\n    position: relative;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    height: 100%;\n    padding: 0 30rpx;\n    padding-top: var(--status-bar-height, 25px);\n    box-sizing: border-box;\n  }\n  \n  .navbar-title {\n    font-size: 36rpx;\n    font-weight: 600;\n    color: #FFFFFF;\n    letter-spacing: 0.5px;\n    position: absolute;\n    left: 50%;\n    transform: translateX(-50%);\n  }\n  \n  .back-btn, .share-btn {\n    width: 80rpx;\n    height: 80rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  }\n  \n  .back-btn {\n    position: absolute;\n    left: 30rpx;\n  }\n  \n  .navbar-right {\n    position: absolute;\n    right: 30rpx;\n  }\n}\n\n/* 内容区域 */\n.content-scroll {\n  flex: 1;\n  margin-top: calc(var(--status-bar-height, 25px) + 62px);\n  margin-bottom: 120rpx; /* 底部操作栏高度 */\n}\n\n/* 商品轮播图 */\n.product-swiper {\n  width: 100%;\n  height: 750rpx;\n  \n  .swiper-item {\n    width: 100%;\n    height: 100%;\n  }\n  \n  .product-image {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n  }\n}\n\n/* 价格信息 */\n.price-section {\n  background-color: #FFFFFF;\n  padding: 30rpx;\n  \n  .price-row {\n    display: flex;\n    align-items: center;\n  }\n  \n  .current-price {\n    font-size: 48rpx;\n    font-weight: 600;\n    color: #FF3B69;\n  }\n  \n  .original-price {\n    font-size: 28rpx;\n    color: #8E8E93;\n    text-decoration: line-through;\n    margin-left: 16rpx;\n  }\n  \n  .discount-badge {\n    margin-left: 16rpx;\n    background-color: rgba(255, 59, 105, 0.1);\n    color: #FF3B69;\n    font-size: 24rpx;\n    padding: 4rpx 12rpx;\n    border-radius: 16rpx;\n  }\n  \n  .sales-info {\n    font-size: 24rpx;\n    color: #8E8E93;\n    margin-top: 12rpx;\n  }\n}\n\n/* 商品标题 */\n.product-title-section {\n  background-color: #FFFFFF;\n  padding: 30rpx;\n  margin-top: 2rpx;\n  display: flex;\n  justify-content: space-between;\n  \n  .product-title {\n    flex: 1;\n    font-size: 32rpx;\n    font-weight: 500;\n    color: #333333;\n    line-height: 1.5;\n  }\n  \n  .collect-btn {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    margin-left: 30rpx;\n    \n    .collect-icon {\n      margin-bottom: 6rpx;\n      \n      &.active {\n        animation: heartBeat 0.5s ease;\n      }\n    }\n    \n    text {\n      font-size: 22rpx;\n      color: #8E8E93;\n    }\n  }\n}\n\n@keyframes heartBeat {\n  0% { transform: scale(1); }\n  14% { transform: scale(1.3); }\n  28% { transform: scale(1); }\n  42% { transform: scale(1.3); }\n  70% { transform: scale(1); }\n}\n\n/* 商品标签 */\n.tags-section {\n  background-color: #FFFFFF;\n  padding: 0 30rpx 30rpx;\n  display: flex;\n  flex-wrap: wrap;\n  \n  .tag-item {\n    margin-right: 16rpx;\n    margin-bottom: 16rpx;\n    \n    .tag-badge {\n      display: inline-block;\n      padding: 6rpx 16rpx;\n      border-radius: 6rpx;\n      font-size: 24rpx;\n      \n      &.discount {\n        background-color: rgba(255, 149, 0, 0.1);\n        color: #FF9500;\n      }\n      \n      &.new {\n        background-color: rgba(90, 200, 250, 0.1);\n        color: #5AC8FA;\n      }\n      \n      &.hot {\n        background-color: rgba(255, 59, 48, 0.1);\n        color: #FF3B30;\n      }\n      \n      &.coupon {\n        background-color: rgba(255, 45, 85, 0.1);\n        color: #FF2D55;\n      }\n    }\n  }\n}\n\n/* 促销信息 */\n.promotion-section {\n  background-color: #FFFFFF;\n  padding: 30rpx;\n  margin-top: 20rpx;\n  \n  .section-title {\n    font-size: 28rpx;\n    font-weight: 500;\n    color: #333333;\n    margin-bottom: 20rpx;\n  }\n  \n  .promotion-list {\n    .promotion-item {\n      display: flex;\n      align-items: center;\n      margin-bottom: 16rpx;\n      \n      .promo-badge {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 40rpx;\n        height: 40rpx;\n        border-radius: 8rpx;\n        font-size: 22rpx;\n        color: #FFFFFF;\n        margin-right: 16rpx;\n        \n        &.discount {\n          background-color: #FF9500;\n        }\n        \n        &.coupon {\n          background-color: #FF2D55;\n        }\n      }\n      \n      .promo-text {\n        font-size: 26rpx;\n        color: #333333;\n      }\n    }\n  }\n}\n\n/* 选择规格 */\n.spec-section {\n  background-color: #FFFFFF;\n  padding: 30rpx;\n  margin-top: 2rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  \n  .section-title {\n    font-size: 28rpx;\n    font-weight: 500;\n    color: #333333;\n  }\n  \n  .spec-content {\n    display: flex;\n    align-items: center;\n    \n    .spec-text {\n      font-size: 26rpx;\n      color: #8E8E93;\n      margin-right: 10rpx;\n    }\n  }\n}\n\n/* 店铺信息 */\n.shop-section {\n  background-color: #FFFFFF;\n  padding: 30rpx;\n  margin-top: 20rpx;\n  display: flex;\n  align-items: center;\n  \n  .shop-logo {\n    width: 80rpx;\n    height: 80rpx;\n    border-radius: 50%;\n    margin-right: 20rpx;\n  }\n  \n  .shop-info {\n    flex: 1;\n    \n    .shop-name {\n      font-size: 28rpx;\n      font-weight: 500;\n      color: #333333;\n      margin-bottom: 8rpx;\n    }\n    \n    .shop-rating {\n      display: flex;\n      align-items: center;\n      \n      .rating-stars {\n        display: flex;\n        margin-right: 10rpx;\n      }\n      \n      .rating-text {\n        font-size: 24rpx;\n        color: #FF9500;\n      }\n    }\n  }\n  \n  .enter-shop-btn {\n    display: flex;\n    align-items: center;\n    padding: 10rpx 20rpx;\n    background-color: rgba(255, 59, 105, 0.1);\n    border-radius: 30rpx;\n    \n    text {\n      font-size: 24rpx;\n      color: #FF3B69;\n      margin-right: 6rpx;\n    }\n  }\n}\n\n/* 商品详情 */\n.detail-section {\n  background-color: #FFFFFF;\n  padding: 30rpx;\n  margin-top: 20rpx;\n  \n  .detail-title {\n    font-size: 28rpx;\n    font-weight: 500;\n    color: #333333;\n    margin-bottom: 20rpx;\n    position: relative;\n    padding-left: 20rpx;\n    \n    &::before {\n      content: '';\n      position: absolute;\n      left: 0;\n      top: 8rpx;\n      width: 8rpx;\n      height: 28rpx;\n      background: linear-gradient(to bottom, #FF3B69, #FF7A9E);\n      border-radius: 4rpx;\n    }\n  }\n  \n  .detail-content {\n    font-size: 26rpx;\n    color: #333333;\n    line-height: 1.6;\n  }\n  \n  .detail-images {\n    margin-top: 20rpx;\n    \n    .detail-image {\n      width: 100%;\n      height: auto;\n      margin-bottom: 20rpx;\n      border-radius: 12rpx;\n    }\n  }\n}\n\n/* 商品评价 */\n.reviews-section {\n  background-color: #FFFFFF;\n  padding: 30rpx;\n  margin-top: 20rpx;\n  \n  .section-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20rpx;\n    \n    .section-title {\n      font-size: 28rpx;\n      font-weight: 500;\n      color: #333333;\n      position: relative;\n      padding-left: 20rpx;\n      \n      &::before {\n        content: '';\n        position: absolute;\n        left: 0;\n        top: 8rpx;\n        width: 8rpx;\n        height: 28rpx;\n        background: linear-gradient(to bottom, #FF3B69, #FF7A9E);\n        border-radius: 4rpx;\n      }\n    }\n    \n    .view-all {\n      display: flex;\n      align-items: center;\n      \n      text {\n        font-size: 24rpx;\n        color: #8E8E93;\n        margin-right: 6rpx;\n      }\n    }\n  }\n  \n  .reviews-list {\n    .review-item {\n      padding-bottom: 20rpx;\n      border-bottom: 1px solid #F2F2F7;\n      margin-bottom: 20rpx;\n      \n      &:last-child {\n        margin-bottom: 0;\n        border-bottom: none;\n      }\n      \n      .review-header {\n        display: flex;\n        align-items: center;\n        margin-bottom: 16rpx;\n        \n        .user-avatar {\n          width: 60rpx;\n          height: 60rpx;\n          border-radius: 50%;\n          margin-right: 16rpx;\n        }\n        \n        .review-user-info {\n          flex: 1;\n          \n          .user-name {\n            font-size: 26rpx;\n            color: #333333;\n            margin-bottom: 6rpx;\n          }\n          \n          .review-rating {\n            display: flex;\n          }\n        }\n        \n        .review-time {\n          font-size: 22rpx;\n          color: #8E8E93;\n        }\n      }\n      \n      .review-content {\n        font-size: 26rpx;\n        color: #333333;\n        line-height: 1.5;\n        margin-bottom: 16rpx;\n      }\n      \n      .review-images {\n        display: flex;\n        flex-wrap: wrap;\n        \n        .review-image {\n          width: 160rpx;\n          height: 160rpx;\n          border-radius: 8rpx;\n          margin-right: 16rpx;\n          margin-bottom: 16rpx;\n        }\n      }\n    }\n  }\n  \n  .empty-reviews {\n    padding: 60rpx 0;\n    text-align: center;\n    \n    text {\n      font-size: 28rpx;\n      color: #8E8E93;\n    }\n  }\n}\n\n/* 相关推荐 */\n.recommend-section {\n  background-color: #FFFFFF;\n  padding: 30rpx;\n  margin-top: 20rpx;\n  \n  .section-title {\n    font-size: 28rpx;\n    font-weight: 500;\n    color: #333333;\n    margin-bottom: 20rpx;\n    position: relative;\n    padding-left: 20rpx;\n    \n    &::before {\n      content: '';\n      position: absolute;\n      left: 0;\n      top: 8rpx;\n      width: 8rpx;\n      height: 28rpx;\n      background: linear-gradient(to bottom, #FF3B69, #FF7A9E);\n      border-radius: 4rpx;\n    }\n  }\n  \n  .recommend-scroll {\n    white-space: nowrap;\n  }\n  \n  .recommend-list {\n    display: inline-flex;\n    padding: 10rpx 0;\n  }\n  \n  .recommend-item {\n    width: 240rpx;\n    margin-right: 20rpx;\n    \n    .recommend-image {\n      width: 240rpx;\n      height: 240rpx;\n      border-radius: 12rpx;\n      margin-bottom: 12rpx;\n    }\n    \n    .recommend-info {\n      .recommend-title {\n        font-size: 26rpx;\n        color: #333333;\n        white-space: nowrap;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        margin-bottom: 8rpx;\n      }\n      \n      .recommend-price {\n        font-size: 26rpx;\n        font-weight: 500;\n        color: #FF3B69;\n      }\n    }\n  }\n}\n\n/* 底部安全区域 */\n.safe-area-bottom {\n  height: 34px; /* iOS 安全区域高度 */\n}\n\n/* 底部操作栏 */\n.bottom-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 120rpx;\n  background-color: #FFFFFF;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  display: flex;\n  align-items: center;\n  padding-bottom: env(safe-area-inset-bottom);\n  z-index: 90;\n  \n  .bottom-left {\n    display: flex;\n    flex: 1;\n    \n    .action-btn {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      width: 100rpx;\n      position: relative;\n      \n      svg {\n        margin-bottom: 6rpx;\n      }\n      \n      text {\n        font-size: 22rpx;\n        color: #8E8E93;\n      }\n      \n      .cart-badge {\n        position: absolute;\n        top: 0;\n        right: 10rpx;\n        min-width: 32rpx;\n        height: 32rpx;\n        border-radius: 16rpx;\n        background: #FF3B30;\n        color: #FFFFFF;\n        font-size: 20rpx;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        padding: 0 6rpx;\n        box-sizing: border-box;\n      }\n    }\n  }\n  \n  .bottom-right {\n    flex: 2;\n    display: flex;\n    \n    .add-to-cart-btn, .buy-now-btn {\n      flex: 1;\n      height: 80rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 28rpx;\n      font-weight: 500;\n    }\n    \n    .add-to-cart-btn {\n      background-color: rgba(255, 59, 105, 0.1);\n      color: #FF3B69;\n      border-radius: 40rpx 0 0 40rpx;\n    }\n    \n    .buy-now-btn {\n      background: linear-gradient(135deg, #FF3B69, #FF7A9E);\n      color: #FFFFFF;\n      border-radius: 0 40rpx 40rpx 0;\n    }\n  }\n}\n\n/* 规格选择弹窗 */\n.spec-popup {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  z-index: 999;\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-end;\n  \n  .spec-popup-content {\n    background-color: #FFFFFF;\n    border-radius: 30rpx 30rpx 0 0;\n    padding: 30rpx;\n    animation: slideUp 0.3s ease;\n    max-height: 80vh;\n    overflow-y: auto;\n  }\n  \n  .spec-popup-header {\n    display: flex;\n    margin-bottom: 30rpx;\n    position: relative;\n    \n    .spec-product-image {\n      width: 160rpx;\n      height: 160rpx;\n      border-radius: 12rpx;\n      margin-right: 20rpx;\n    }\n    \n    .spec-product-info {\n      flex: 1;\n      \n      .spec-product-price {\n        font-size: 36rpx;\n        font-weight: 600;\n        color: #FF3B69;\n        margin-bottom: 10rpx;\n      }\n      \n      .spec-product-stock {\n        font-size: 24rpx;\n        color: #8E8E93;\n        margin-bottom: 10rpx;\n      }\n      \n      .spec-selected {\n        font-size: 24rpx;\n        color: #333333;\n      }\n    }\n    \n    .spec-close-btn {\n      position: absolute;\n      top: 0;\n      right: 0;\n      width: 60rpx;\n      height: 60rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n  }\n  \n  .spec-options {\n    .spec-option-group {\n      margin-bottom: 30rpx;\n      \n      .spec-option-title {\n        font-size: 28rpx;\n        font-weight: 500;\n        color: #333333;\n        margin-bottom: 20rpx;\n      }\n      \n      .spec-option-list {\n        display: flex;\n        flex-wrap: wrap;\n        \n        .spec-option-item {\n          padding: 12rpx 24rpx;\n          border-radius: 8rpx;\n          background-color: #F2F2F7;\n          font-size: 26rpx;\n          color: #333333;\n          margin-right: 20rpx;\n          margin-bottom: 20rpx;\n          \n          &.active {\n            background-color: rgba(255, 59, 105, 0.1);\n            color: #FF3B69;\n            border: 1px solid #FF3B69;\n          }\n          \n          &.disabled {\n            color: #C8C8C8;\n            background-color: #F8F8F8;\n            pointer-events: none;\n          }\n        }\n      }\n    }\n  }\n  \n  .quantity-selector {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 30rpx;\n    \n    .quantity-title {\n      font-size: 28rpx;\n      font-weight: 500;\n      color: #333333;\n    }\n    \n    .quantity-control {\n      display: flex;\n      align-items: center;\n      \n      .quantity-btn {\n        width: 60rpx;\n        height: 60rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        border: 1px solid #E0E0E0;\n        \n        &.minus {\n          border-radius: 8rpx 0 0 8rpx;\n        }\n        \n        &.plus {\n          border-radius: 0 8rpx 8rpx 0;\n        }\n        \n        &.disabled {\n          color: #C8C8C8;\n          background-color: #F8F8F8;\n          pointer-events: none;\n        }\n      }\n      \n      .quantity-input {\n        width: 80rpx;\n        height: 60rpx;\n        border-top: 1px solid #E0E0E0;\n        border-bottom: 1px solid #E0E0E0;\n        text-align: center;\n        font-size: 28rpx;\n      }\n    }\n  }\n  \n  .spec-popup-footer {\n    display: flex;\n    \n    .add-to-cart-btn, .buy-now-btn {\n      flex: 1;\n      height: 80rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 28rpx;\n      font-weight: 500;\n    }\n    \n    .add-to-cart-btn {\n      background-color: rgba(255, 59, 105, 0.1);\n      color: #FF3B69;\n      border-radius: 40rpx 0 0 40rpx;\n    }\n    \n    .buy-now-btn {\n      background: linear-gradient(135deg, #FF3B69, #FF7A9E);\n      color: #FFFFFF;\n      border-radius: 0 40rpx 40rpx 0;\n    }\n  }\n}\n\n@keyframes slideUp {\n  from { transform: translateY(100%); }\n  to { transform: translateY(0); }\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/products/hot/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "product", "onMounted"], "mappings": ";;;;;;;;;;AA+RA,UAAM,UAAUA,cAAAA,IAAI;AAAA,MAClB,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,eAAe;AAAA,MACf,OAAO;AAAA,MACP,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,QAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,MACD,cAAc;AAAA,QACZ;AAAA,QACA;AAAA,MACD;AAAA,MACD,UAAU;AAAA,MACV,UAAU;AAAA,MACV,aAAa;AAAA,MACb,QAAQ;AAAA,QACN,EAAE,MAAM,YAAY,MAAM,YAAa;AAAA,QACvC,EAAE,MAAM,OAAO,MAAM,KAAM;AAAA,MAC5B;AAAA,MACD,YAAY;AAAA,QACV,EAAE,MAAM,YAAY,MAAM,YAAa;AAAA,QACvC,EAAE,MAAM,UAAU,MAAM,YAAa;AAAA,MACtC;AAAA,MACD,OAAO;AAAA,QACL;AAAA,UACE,OAAO;AAAA,UACP,SAAS,CAAC,OAAO,MAAM,MAAM,KAAK;AAAA,QACnC;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,SAAS,CAAC,SAAS,SAAS,SAAS,KAAK;AAAA,QAC3C;AAAA,MACF;AAAA,MACD,MAAM;AAAA,QACJ,EAAE,OAAO,EAAE,MAAM,OAAO,MAAM,QAAS,GAAE,OAAO,MAAM,OAAO,IAAK;AAAA,QAClE,EAAE,OAAO,EAAE,MAAM,OAAO,MAAM,QAAS,GAAE,OAAO,MAAM,OAAO,IAAK;AAAA,QAClE,EAAE,OAAO,EAAE,MAAM,MAAM,MAAM,QAAS,GAAE,OAAO,MAAM,OAAO,IAAK;AAAA,QACjE,EAAE,OAAO,EAAE,MAAM,MAAM,MAAM,QAAS,GAAE,OAAO,MAAM,OAAO,IAAK;AAAA,QACjE,EAAE,OAAO,EAAE,MAAM,MAAM,MAAM,QAAS,GAAE,OAAO,MAAM,OAAO,IAAK;AAAA,QACjE,EAAE,OAAO,EAAE,MAAM,MAAM,MAAM,QAAS,GAAE,OAAO,MAAM,OAAO,IAAK;AAAA,QACjE,EAAE,OAAO,EAAE,MAAM,OAAO,MAAM,QAAS,GAAE,OAAO,MAAM,OAAO,IAAK;AAAA,QAClE,EAAE,OAAO,EAAE,MAAM,OAAO,MAAM,QAAS,GAAE,OAAO,MAAM,OAAO,IAAK;AAAA,MACnE;AAAA,IACH,CAAC;AAGD,UAAM,OAAOA,cAAAA,IAAI;AAAA,MACf,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,gBAAgB;AAAA,IAClB,CAAC;AAGD,UAAM,UAAUA,cAAAA,IAAI;AAAA,MAClB;AAAA,QACE,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,UACN;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,MACF;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,MAAM;AAAA,QACN,QAAQ,CAAE;AAAA,MACX;AAAA,IACH,CAAC;AAGD,UAAM,kBAAkBA,cAAAA,IAAI;AAAA,MAC1B;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,eAAe;AAAA,MAChB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,eAAe;AAAA,MAChB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,eAAe;AAAA,MAChB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,eAAe;AAAA,MAChB;AAAA,IACH,CAAC;AAGD,UAAM,aAAaA,cAAAA,IAAI,KAAK;AAC5B,UAAM,YAAYA,cAAAA,IAAI,CAAC;AACvB,UAAM,mBAAmBA,cAAAA,IAAI,KAAK;AAClC,UAAM,kBAAkBA,cAAAA,IAAI,CAAA,CAAE;AAC9B,UAAM,WAAWA,cAAAA,IAAI,CAAC;AACtB,UAAM,cAAcA,cAAAA,IAAI,EAAE;AAG1B,UAAM,WAAWC,cAAQ,SAAC,MAAM;AAC9B,aAAO,KAAK,MAAO,QAAQ,MAAM,QAAQ,QAAQ,MAAM,gBAAiB,EAAE;AAAA,IAC5E,CAAC;AAGD,UAAM,eAAeA,cAAQ,SAAC,MAAM;AAClC,YAAM,QAAQ,OAAO,QAAQ,gBAAgB,KAAK,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,GAAG,GAAG,IAAI,KAAK,EAAE;AAC3F,aAAO,MAAM,SAAS,IAAI,MAAM,KAAK,IAAI,IAAI;AAAA,IAC/C,CAAC;AAGD,UAAM,cAAcA,cAAQ,SAAC,MAAM;AACjC,YAAM,QAAQ,gBAAgB;AAC9B,YAAM,WAAW,OAAO,KAAK,KAAK;AAGlC,UAAI,SAAS,WAAW,QAAQ,MAAM,MAAM,QAAQ;AAClD,eAAO;AAAA,MACR;AAGD,aAAO,QAAQ,MAAM,KAAK,KAAK,SAAO;AACpC,eAAO,SAAS,MAAM,SAAO,IAAI,MAAM,GAAG,MAAM,MAAM,GAAG,CAAC;AAAA,MAC9D,CAAG;AAAA,IACH,CAAC;AAGD,aAAS,WAAW,OAAO,QAAQ;AACjC,UAAI,CAAC,kBAAkB,OAAO,MAAM;AAAG;AAEvC,sBAAgB,QAAQ;AAAA,QACtB,GAAG,gBAAgB;AAAA,QACnB,CAAC,KAAK,GAAG;AAAA,MACb;AAAA,IACA;AAGA,aAAS,kBAAkB,OAAO,QAAQ;AAExC,YAAM,eAAe,EAAE,GAAG,gBAAgB,OAAO,CAAC,KAAK,GAAG;AAC1D,YAAM,eAAe,OAAO,KAAK,YAAY;AAG7C,UAAI,aAAa,SAAS,QAAQ,MAAM,MAAM,QAAQ;AACpD,eAAO,QAAQ,MAAM,KAAK,KAAK,SAAO;AACpC,iBAAO,aAAa,MAAM,SAAO,IAAI,MAAM,GAAG,MAAM,aAAa,GAAG,CAAC;AAAA,QAC3E,CAAK;AAAA,MACF;AAGD,YAAM,aAAa,QAAQ,MAAM,KAAK,KAAK,SAAO;AAChD,eAAO,aAAa,MAAM,SAAO,IAAI,MAAM,GAAG,MAAM,aAAa,GAAG,CAAC;AAAA,MACzE,CAAG;AAED,aAAO,cAAc,WAAW,QAAQ;AAAA,IAC1C;AAGA,aAAS,sBAAsB;AAC7B,YAAM,QAAQ,OAAO,QAAQ,gBAAgB,KAAK;AAClD,UAAI,MAAM,WAAW,GAAG;AACtB,eAAO;AAAA,MACR;AACD,aAAO,MAAM,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,OAAO,GAAG,IAAI,KAAK,EAAE,EAAE,KAAK,IAAI;AAAA,IACrE;AAGA,aAAS,mBAAmB;AAC1B,UAAI,SAAS,QAAQ,YAAY,OAAO;AACtC,iBAAS;AAAA,MACV;AAAA,IACH;AAGA,aAAS,mBAAmB;AAC1B,UAAI,SAAS,QAAQ,GAAG;AACtB,iBAAS;AAAA,MACV;AAAA,IACH;AAGA,aAAS,mBAAmB;AAC1B,uBAAiB,QAAQ;AAAA,IAC3B;AAGA,aAAS,oBAAoB;AAC3B,uBAAiB,QAAQ;AAAA,IAC3B;AAGA,aAAS,YAAY;AACnB;IACF;AAGA,aAAS,mBAAmB;AAC1B,UAAI,CAAC,YAAY,OAAO;AACtBC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,gBAAU,SAAS,SAAS;AAE5BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAED;IACF;AAGA,aAAS,SAAS;AAChB;IACF;AAGA,aAAS,gBAAgB;AACvB,UAAI,CAAC,YAAY,OAAO;AACtBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,iBAAW,mEAAmE,QAAQ,MAAM,EAAE;AAC9F;IACF;AAGA,aAAS,iBAAiB;AACxB,iBAAW,QAAQ,CAAC,WAAW;AAC/BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,WAAW,QAAQ,QAAQ;AAAA,QAClC,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,aAAS,eAAe;AACtBA,oBAAAA,MAAI,cAAc;AAAA,QAChB,iBAAiB;AAAA,QACjB,UAAU;AACRA,wBAAAA,wFAAY,UAAU;AAAA,QACvB;AAAA,QACD,OAAO;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,aAAS,aAAa,QAAQ,SAAS;AACrCA,oBAAAA,MAAI,aAAa;AAAA,QACf,MAAM;AAAA,QACN;AAAA,MACJ,CAAG;AAAA,IACH;AAGA,aAAS,iBAAiB;AACxB,iBAAW,wDAAwD,KAAK,MAAM,EAAE,EAAE;AAAA,IACpF;AAGA,aAAS,iBAAiB;AACxB,iBAAW,iDAAiD;AAAA,IAC9D;AAGA,aAAS,oBAAoB;AAC3B,iBAAW,4DAA4D,QAAQ,MAAM,EAAE,EAAE;AAAA,IAC3F;AAGA,aAAS,kBAAkBC,UAAS;AAClC,iBAAW,2DAA2DA,SAAQ,EAAE,EAAE;AAAA,IACpF;AAGA,aAAS,WAAW,KAAK;AACvBD,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,QACA,MAAM,CAAC,QAAQ;AACbA,wBAAA,MAAA,MAAA,SAAA,qEAAc,SAAS,GAAG;AAC1BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,aAAS,eAAe;AACtBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGAE,kBAAAA,UAAU,MAAM;AAEd,YAAM,YAAY,OAAO,SAAS,WAAW,MAAM,MAAM,EAAE,KAAK;AAChEF,4GAAY,WAAW,SAAS;AAAA,IAIlC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnnBD,GAAG,WAAW,eAAe;"}