"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  __name: "house-sale-detail",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    common_vendor.onMounted(() => {
      try {
        const sysInfo = common_vendor.index.getSystemInfoSync();
        statusBarHeight.value = sysInfo.statusBarHeight || 20;
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/publish/house-sale-detail.vue:251", "获取状态栏高度失败", e);
      }
    });
    const goBack = () => {
      common_vendor.index.navigateBack({
        fail: () => {
          common_vendor.index.switchTab({
            url: "/pages/index/index"
          });
        }
      });
    };
    const formatTime = (timestamp) => {
      const date = new Date(timestamp);
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    };
    const isCollected = common_vendor.ref(false);
    const houseData = common_vendor.ref({
      id: "house12345",
      title: "精装三室两厅出售",
      price: "120万",
      tags: ["精装修", "满五唯一", "学区房"],
      publishTime: Date.now() - 864e5 * 2,
      // 2天前
      images: [
        "/static/images/house1.jpg",
        "/static/images/house2.jpg",
        "/static/images/house3.jpg"
      ],
      type: "三室两厅",
      area: "磁县城区",
      size: "120平方米",
      orientation: "南北通透",
      configs: [
        { name: "空调", icon: "icon-ac" },
        { name: "热水器", icon: "icon-water-heater" },
        { name: "洗衣机", icon: "icon-washer" },
        { name: "冰箱", icon: "icon-fridge" },
        { name: "电视", icon: "icon-tv" },
        { name: "宽带", icon: "icon-wifi" }
      ],
      details: [
        { label: "装修情况", value: "精装修" },
        { label: "所在楼层", value: "6/18层" },
        { label: "建筑年代", value: "2018年" },
        { label: "房屋用途", value: "住宅" }
      ],
      location: {
        address: "磁县城区XX路XX号",
        latitude: 36.123456,
        longitude: 114.123456,
        surroundings: [
          { name: "地铁站", distance: "500米", icon: "icon-subway" },
          { name: "公交站", distance: "200米", icon: "icon-bus" },
          { name: "超市", distance: "300米", icon: "icon-supermarket" },
          { name: "学校", distance: "800米", icon: "icon-school" }
        ]
      },
      transaction: [
        { label: "房屋总价", value: "120万" },
        { label: "单价", value: "10000元/㎡" },
        { label: "首付比例", value: "30%" },
        { label: "月供参考", value: "约5000元" }
      ],
      property: [
        { label: "产权年限", value: "70年" },
        { label: "产权类型", value: "商品房" },
        { label: "产权状态", value: "满五唯一" },
        { label: "抵押情况", value: "无抵押" }
      ],
      description: "房屋位于磁县城区核心地段，交通便利，配套齐全。精装修，拎包入住。满五唯一，税费低。周边有地铁、公交、超市、学校等配套设施。",
      owner: {
        name: "王先生",
        avatar: "/static/images/avatar.png",
        type: "个人",
        rating: "A+",
        isVerified: true
      },
      contact: {
        name: "王先生",
        phone: "13912345678"
      }
    });
    const markers = common_vendor.ref([{
      id: 1,
      latitude: houseData.value.location.latitude,
      longitude: houseData.value.location.longitude,
      title: houseData.value.title
    }]);
    const relatedHouses = common_vendor.ref([
      {
        id: "house001",
        title: "精装三室两厅",
        price: "110万",
        type: "三室两厅",
        area: "磁县城区",
        image: "/static/images/house-similar1.jpg",
        tags: ["精装修", "学区房", "满五唯一"]
      },
      {
        id: "house002",
        title: "南北通透大三居",
        price: "128万",
        type: "三室两厅",
        area: "磁县城区",
        image: "/static/images/house-similar2.jpg",
        tags: ["南北通透", "带车位"]
      },
      {
        id: "house003",
        title: "高层景观房",
        price: "135万",
        type: "三室两厅",
        area: "磁县城区",
        image: "/static/images/house-similar3.jpg",
        tags: ["高层", "景观好"]
      }
    ]);
    const toggleCollect = () => {
      isCollected.value = !isCollected.value;
      if (isCollected.value) {
        common_vendor.index.showToast({
          title: "收藏成功",
          icon: "success"
        });
      }
    };
    const callPhone = () => {
      common_vendor.index.makePhoneCall({
        phoneNumber: houseData.value.contact.phone,
        fail: () => {
          common_vendor.index.showToast({
            title: "拨打电话失败",
            icon: "none"
          });
        }
      });
    };
    const navigateToHouse = (id) => {
      if (id === houseData.value.id)
        return;
      common_vendor.index.navigateTo({ url: `/pages/publish/house-sale-detail?id=${id}` });
    };
    const navigateToHouseList = (e) => {
      var _a;
      if (e)
        e.stopPropagation();
      const houseCategory = ((_a = houseData.value.tags) == null ? void 0 : _a[0]) || "";
      common_vendor.index.navigateTo({
        url: `/subPackages/service/pages/filter?type=house_sell&title=${encodeURIComponent("房屋出售")}&category=${encodeURIComponent(houseCategory)}&active=house_sell`
      });
    };
    const goToHome = () => {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    };
    const openChat = () => {
      if (!houseData.value.owner || !houseData.value.owner.id) {
        common_vendor.index.showToast({
          title: "无法获取业主信息",
          icon: "none"
        });
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/chat/index?userId=${houseData.value.owner.id}&username=${encodeURIComponent(houseData.value.owner.name || "业主")}`
      });
    };
    common_vendor.onMounted(() => {
      common_vendor.index.setNavigationBarTitle({
        title: "房屋详情"
      });
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$5,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_vendor.t(houseData.value.title),
        e: common_vendor.t(houseData.value.price),
        f: common_vendor.f(houseData.value.tags, (tag, index, i0) => {
          return {
            a: common_vendor.t(tag),
            b: index
          };
        }),
        g: common_vendor.t(formatTime(houseData.value.publishTime)),
        h: common_vendor.f(houseData.value.images, (image, index, i0) => {
          return {
            a: image,
            b: index
          };
        }),
        i: common_vendor.t(houseData.value.type),
        j: common_vendor.t(houseData.value.area),
        k: common_vendor.t(houseData.value.size),
        l: common_vendor.t(houseData.value.orientation),
        m: common_vendor.f(houseData.value.configs, (item, index, i0) => {
          return {
            a: common_vendor.n(item.icon),
            b: common_vendor.t(item.name),
            c: index
          };
        }),
        n: common_vendor.f(houseData.value.details, (item, index, i0) => {
          return {
            a: common_vendor.t(item.label),
            b: common_vendor.t(item.value),
            c: index
          };
        }),
        o: common_vendor.t(houseData.value.location.address),
        p: houseData.value.location.latitude,
        q: houseData.value.location.longitude,
        r: markers.value,
        s: common_vendor.f(houseData.value.location.surroundings, (item, index, i0) => {
          return {
            a: common_vendor.n(item.icon),
            b: common_vendor.t(item.name),
            c: common_vendor.t(item.distance),
            d: index
          };
        }),
        t: common_vendor.f(houseData.value.transaction, (item, index, i0) => {
          return {
            a: common_vendor.t(item.label),
            b: common_vendor.t(item.value),
            c: index
          };
        }),
        v: common_vendor.f(houseData.value.property, (item, index, i0) => {
          return {
            a: common_vendor.t(item.label),
            b: common_vendor.t(item.value),
            c: index
          };
        }),
        w: common_vendor.t(houseData.value.description),
        x: houseData.value.owner.avatar,
        y: common_vendor.t(houseData.value.owner.name),
        z: common_vendor.t(houseData.value.owner.type),
        A: common_vendor.t(houseData.value.owner.rating),
        B: houseData.value.owner.isVerified
      }, houseData.value.owner.isVerified ? {} : {}, {
        C: common_vendor.t(houseData.value.contact.name),
        D: common_vendor.t(houseData.value.contact.phone),
        E: common_vendor.o(callPhone),
        F: common_vendor.f(relatedHouses.value.slice(0, 3), (house, index, i0) => {
          return common_vendor.e({
            a: house.image,
            b: common_vendor.t(house.title),
            c: common_vendor.t(house.type),
            d: common_vendor.t(house.area),
            e: common_vendor.f(house.tags.slice(0, 2), (tag, tagIndex, i1) => {
              return {
                a: common_vendor.t(tag),
                b: tagIndex
              };
            }),
            f: house.tags && house.tags.length > 2
          }, house.tags && house.tags.length > 2 ? {
            g: common_vendor.t(house.tags.length - 2)
          } : {}, {
            h: common_vendor.t(house.price),
            i: index,
            j: common_vendor.o(($event) => navigateToHouse(house.id), index)
          });
        }),
        G: relatedHouses.value.length === 0
      }, relatedHouses.value.length === 0 ? {
        H: common_assets._imports_1$3
      } : {}, {
        I: relatedHouses.value.length > 0
      }, relatedHouses.value.length > 0 ? {
        J: common_vendor.o(navigateToHouseList)
      } : {}, {
        K: common_assets._imports_12,
        L: common_vendor.o(goToHome),
        M: common_assets._imports_3$2,
        N: common_vendor.o(toggleCollect),
        O: common_assets._imports_3$3,
        P: common_assets._imports_14,
        Q: common_vendor.o(openChat),
        R: common_vendor.o(callPhone)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/publish/house-sale-detail.js.map
