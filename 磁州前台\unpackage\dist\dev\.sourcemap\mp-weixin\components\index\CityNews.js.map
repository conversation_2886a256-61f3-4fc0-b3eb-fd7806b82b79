{"version": 3, "file": "CityNews.js", "sources": ["components/index/CityNews.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7AvY29tcG9uZW50cy9pbmRleC9DaXR5TmV3cy52dWU"], "sourcesContent": ["<template>\n  <!-- 同城资讯 -->\n  <view class=\"city-news card-section fade-in\">\n    <view class=\"section-header\">\n      <view class=\"section-title-wrap\">\n        <!-- 删除蓝色竖道 -->\n        <text class=\"section-title blue-title\">同城资讯</text>\n      </view>\n      <text class=\"more-link blue-link\" @click=\"navigateToMore\">查看更多</text>\n    </view>\n    <view class=\"notice-bar\">\n      <image src=\"/static/images/tabbar/喇叭.png\" class=\"notice-icon\"></image>\n      <swiper class=\"notice-swiper\" vertical autoplay circular interval=\"3000\" duration=\"500\">\n        <swiper-item v-for=\"(item, index) in noticeList\" :key=\"index\" @click=\"navigateToDetail(index)\">\n          <text class=\"notice-text\">{{item}}</text>\n        </swiper-item>\n      </swiper>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref } from 'vue';\n\nconst noticeList = ref([\n  '磁县人民政府关于加强城市管理的通知',\n  '2023年磁县春节期间活动安排',\n  '磁县城区道路施工公告',\n  '关于开展全民健康体检的通知',\n  '磁县文化中心活动预告'\n]);\n\nfunction navigateToMore() {\n  uni.navigateTo({\n    url: '/subPackages/news/pages/list',\n    fail: () => {\n      uni.showToast({\n        title: '页面跳转失败',\n        icon: 'none'\n      });\n    }\n  });\n}\n\nfunction navigateToDetail(index) {\n  uni.navigateTo({\n    url: `/subPackages/news/pages/detail?id=${index + 1}`,\n    fail: () => {\n      uni.showToast({\n        title: '页面跳转失败',\n        icon: 'none'\n      });\n    }\n  });\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.city-news {\n  margin: 24rpx 30rpx 30rpx;\n  position: relative;\n  z-index: 2;\n  background: #ffffff;\n  border-radius: 35rpx;\n  padding: 24rpx 20rpx 30rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\n  overflow: hidden;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.section-title-wrap {\n  display: flex;\n  align-items: center;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;\n}\n\n.blue-title {\n  color: #007AFF;\n}\n\n.more-link {\n  font-size: 26rpx;\n  color: #007AFF;\n  padding: 6rpx 12rpx;\n  border-radius: 16rpx;\n  transition: all 0.2s ease;\n}\n\n.blue-link {\n  color: #007AFF;\n}\n\n.more-link:active {\n  background: rgba(0, 0, 0, 0.05);\n  transform: scale(0.96);\n}\n\n.notice-bar {\n  display: flex;\n  align-items: center;\n  background-color: #F2F2F7;\n  border-radius: 35rpx;\n  padding: 16rpx 20rpx;\n}\n\n.notice-icon {\n  width: 36rpx;\n  height: 36rpx;\n  margin-right: 16rpx;\n  flex-shrink: 0;\n}\n\n.notice-swiper {\n  flex: 1;\n  height: 66rpx;\n}\n\n.notice-text {\n  font-size: 28rpx;\n  color: #333;\n  line-height: 66rpx;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.card-section {\n  margin-bottom: 20rpx;\n}\n\n.fade-in {\n  animation: fadeIn 0.5s ease-in-out;\n}\n\n@keyframes fadeIn {\n  from { opacity: 0; transform: translateY(20rpx); }\n  to { opacity: 1; transform: translateY(0); }\n}\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/components/index/CityNews.vue'\nwx.createComponent(Component)"], "names": ["ref", "uni"], "mappings": ";;;;;;AAwBA,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAED,aAAS,iBAAiB;AACxBC,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAEA,aAAS,iBAAiB,OAAO;AAC/BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,qCAAqC,QAAQ,CAAC;AAAA,QACnD,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;ACrDA,GAAG,gBAAgB,SAAS;"}