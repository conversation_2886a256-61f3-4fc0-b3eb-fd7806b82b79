{"version": 3, "file": "partner-team.js", "sources": ["subPackages/partner/pages/partner-team.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNccGFydG5lclxwYWdlc1xwYXJ0bmVyLXRlYW0udnVl"], "sourcesContent": ["<template>\r\n  <view class=\"team-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <cu-custom bgColor=\"bg-gradient-blue\" isBack>\r\n      <block slot=\"backText\"></block>\r\n      <block slot=\"content\">我的团队</block>\r\n    </cu-custom>\r\n\r\n    <!-- 数据统计卡片 -->\r\n    <view class=\"stats-card\">\r\n      <view class=\"stats-header\">\r\n        <text class=\"stats-title\">团队概览</text>\r\n        <text class=\"stats-time\">{{currentMonth}}月数据</text>\r\n      </view>\r\n      <view class=\"stats-grid\">\r\n        <view class=\"stats-item\">\r\n          <text class=\"stats-num\">{{teamTotalNum}}</text>\r\n          <text class=\"stats-label\">团队总人数</text>\r\n        </view>\r\n        <view class=\"stats-item\">\r\n          <text class=\"stats-num\">{{teamNewNum}}</text>\r\n          <text class=\"stats-label\">本月新增</text>\r\n        </view>\r\n        <view class=\"stats-item\">\r\n          <text class=\"stats-num\">{{teamActiveNum}}</text>\r\n          <text class=\"stats-label\">活跃成员</text>\r\n        </view>\r\n        <view class=\"stats-item\">\r\n          <text class=\"stats-num\">¥ {{teamIncome}}</text>\r\n          <text class=\"stats-label\">团队收益</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 级别切换 -->\r\n    <view class=\"tab-section\">\r\n      <view class=\"tab-header\">\r\n        <view \r\n          class=\"tab-item\" \r\n          :class=\"{'active': currentLevel === 1}\"\r\n          @tap=\"switchLevel(1)\"\r\n        >一级团队</view>\r\n        <view \r\n          class=\"tab-item\" \r\n          :class=\"{'active': currentLevel === 2}\"\r\n          @tap=\"switchLevel(2)\"\r\n        >二级团队</view>\r\n      </view>\r\n      <view class=\"tab-info\">\r\n        <text>{{levelInfo}}</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 成员列表 -->\r\n    <view class=\"team-list\" v-if=\"teamMembers.length > 0\">\r\n      <view class=\"team-item\" v-for=\"(item, index) in teamMembers\" :key=\"index\">\r\n        <view class=\"member-avatar\">\r\n          <image :src=\"item.avatar\" mode=\"aspectFill\"></image>\r\n        </view>\r\n        <view class=\"member-info\">\r\n          <view class=\"member-name-row\">\r\n            <text class=\"member-name\">{{item.nickname}}</text>\r\n            <text class=\"member-level\">{{item.partnerLevel}}</text>\r\n          </view>\r\n          <view class=\"member-data\">\r\n            <text>加入时间: {{formatDate(item.joinTime)}}</text>\r\n          </view>\r\n        </view>\r\n        <view class=\"member-right\">\r\n          <view class=\"member-contribution\">贡献: ¥{{item.contribution}}</view>\r\n          <view class=\"member-fans\">{{item.fansNum}}个粉丝</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 空状态 -->\r\n    <view class=\"empty-state\" v-else>\r\n      <image src=\"/static/images/no-team.png\" mode=\"aspectFit\"></image>\r\n      <text>{{emptyText}}</text>\r\n      <button class=\"share-btn\" @tap=\"shareToFriends\">邀请好友加入</button>\r\n    </view>\r\n\r\n    <!-- 加载更多 -->\r\n    <view class=\"load-more\" v-if=\"teamMembers.length > 0 && hasMore\">\r\n      <text @tap=\"loadMore\" v-if=\"!isLoading\">加载更多</text>\r\n      <text v-else>加载中...</text>\r\n    </view>\r\n\r\n    <view class=\"load-end\" v-if=\"teamMembers.length > 0 && !hasMore\">\r\n      <text>- 已经到底了 -</text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue';\r\n\r\n// 响应式数据\r\nconst currentMonth = ref(new Date().getMonth() + 1);\r\nconst teamTotalNum = ref(86);\r\nconst teamNewNum = ref(12);\r\nconst teamActiveNum = ref(35);\r\nconst teamIncome = ref('1286.50');\r\nconst currentLevel = ref(1);\r\nconst teamMembers = ref([]);\r\nconst page = ref(1);\r\nconst pageSize = ref(10);\r\nconst hasMore = ref(true);\r\nconst isLoading = ref(false);\r\n\r\n// 计算属性\r\n// 级别描述信息\r\nconst levelInfo = computed(() => {\r\n  return currentLevel.value === 1 \r\n    ? '直接关注您的成员' \r\n    : '您的一级团队发展的成员';\r\n});\r\n\r\n// 空状态文本\r\nconst emptyText = computed(() => {\r\n  return currentLevel.value === 1 \r\n    ? '暂无一级团队成员' \r\n    : '暂无二级团队成员';\r\n});\r\n\r\n// 切换级别\r\nconst switchLevel = (level) => {\r\n  if (currentLevel.value === level) return;\r\n  \r\n  currentLevel.value = level;\r\n  page.value = 1;\r\n  hasMore.value = true;\r\n  teamMembers.value = [];\r\n  loadData();\r\n};\r\n\r\n// 加载数据\r\nconst loadData = () => {\r\n  isLoading.value = true;\r\n  \r\n  // 模拟API请求\r\n  setTimeout(() => {\r\n    // 模拟数据\r\n    const mockData = generateMockTeam();\r\n    \r\n    if (page.value === 1) {\r\n      teamMembers.value = mockData;\r\n    } else {\r\n      teamMembers.value = [...teamMembers.value, ...mockData];\r\n    }\r\n    \r\n    // 判断是否还有更多数据\r\n    hasMore.value = page.value < 3; // 模拟只有3页数据\r\n    \r\n    isLoading.value = false;\r\n  }, 500);\r\n};\r\n\r\n// 加载更多\r\nconst loadMore = () => {\r\n  if (isLoading.value || !hasMore.value) return;\r\n  \r\n  page.value++;\r\n  loadData();\r\n};\r\n\r\n// 分享给朋友\r\nconst shareToFriends = () => {\r\n  uni.navigateTo({\r\n    url: '/pages/my/partner-poster'\r\n  });\r\n};\r\n\r\n// 格式化日期\r\nconst formatDate = (dateStr) => {\r\n  const date = new Date(dateStr);\r\n  const year = date.getFullYear();\r\n  const month = (date.getMonth() + 1).toString().padStart(2, '0');\r\n  const day = date.getDate().toString().padStart(2, '0');\r\n  \r\n  return `${year}-${month}-${day}`;\r\n};\r\n\r\n// 生成模拟团队数据\r\nconst generateMockTeam = () => {\r\n  const mockTeam = [];\r\n  const nicknames = ['城市猎人', '科技达人', '生活家', '微笑向暖', '光影世界', '千里之行', '星空漫步', '纸短情长'];\r\n  const avatars = [\r\n    '/static/images/avatar-1.png',\r\n    '/static/images/avatar-2.png',\r\n    '/static/images/avatar-3.png',\r\n    '/static/images/avatar-4.png',\r\n    '/static/images/avatar-5.png',\r\n    '/static/images/avatar-6.png'\r\n  ];\r\n  const levels = ['普通合伙人', '银牌合伙人', '金牌合伙人', '钻石合伙人'];\r\n  \r\n  // 随机生成10条记录\r\n  for (let i = 0; i < 10; i++) {\r\n    const now = new Date();\r\n    // 随机日期，最近60天内\r\n    const randomDays = Math.floor(Math.random() * 60);\r\n    const joinTime = new Date(now.getTime() - randomDays * 24 * 60 * 60 * 1000);\r\n    \r\n    // 随机贡献值\r\n    const contribution = Math.floor(Math.random() * 2000);\r\n    \r\n    // 随机粉丝数\r\n    const fansNum = Math.floor(Math.random() * 50);\r\n    \r\n    // 随机等级，二级团队较少高级合伙人\r\n    let levelIndex;\r\n    if (currentLevel.value === 1) {\r\n      levelIndex = Math.floor(Math.random() * levels.length);\r\n    } else {\r\n      // 二级团队中高级合伙人较少\r\n      levelIndex = Math.floor(Math.random() * 2); // 只在前两级中选择\r\n    }\r\n    \r\n    mockTeam.push({\r\n      id: 'member_' + Date.now() + i,\r\n      nickname: nicknames[Math.floor(Math.random() * nicknames.length)],\r\n      avatar: avatars[Math.floor(Math.random() * avatars.length)],\r\n      joinTime,\r\n      partnerLevel: levels[levelIndex],\r\n      contribution,\r\n      fansNum\r\n    });\r\n  }\r\n  \r\n  return mockTeam;\r\n};\r\n\r\n// 生命周期钩子\r\nonMounted(() => {\r\n  // 初始化加载数据\r\n  loadData();\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.team-container {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n  padding-bottom: 30rpx;\r\n}\r\n\r\n.stats-card {\r\n  margin: 30rpx;\r\n  background-color: #ffffff;\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.stats-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.stats-title {\r\n  font-size: 30rpx;\r\n  font-weight: 500;\r\n  color: #333333;\r\n}\r\n\r\n.stats-time {\r\n  font-size: 24rpx;\r\n  color: #999999;\r\n}\r\n\r\n.stats-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  grid-gap: 30rpx;\r\n}\r\n\r\n.stats-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  background-color: #f8f8f8;\r\n  padding: 20rpx 0;\r\n  border-radius: 12rpx;\r\n}\r\n\r\n.stats-num {\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n  color: #333333;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.stats-label {\r\n  font-size: 24rpx;\r\n  color: #999999;\r\n}\r\n\r\n.tab-section {\r\n  margin: 30rpx;\r\n  background-color: #ffffff;\r\n  border-radius: 16rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.tab-header {\r\n  display: flex;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.tab-item {\r\n  flex: 1;\r\n  height: 80rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 28rpx;\r\n  color: #666666;\r\n  position: relative;\r\n  \r\n  &.active {\r\n    color: #1677FF;\r\n    font-weight: 500;\r\n    \r\n    &::after {\r\n      content: '';\r\n      position: absolute;\r\n      left: 50%;\r\n      bottom: 0;\r\n      transform: translateX(-50%);\r\n      width: 40rpx;\r\n      height: 4rpx;\r\n      background-color: #1677FF;\r\n      border-radius: 2rpx;\r\n    }\r\n  }\r\n}\r\n\r\n.tab-info {\r\n  padding: 20rpx;\r\n  font-size: 24rpx;\r\n  color: #999999;\r\n  text-align: center;\r\n}\r\n\r\n.team-list {\r\n  padding: 0 30rpx;\r\n}\r\n\r\n.team-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 30rpx;\r\n  background-color: #ffffff;\r\n  border-radius: 16rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.member-avatar {\r\n  margin-right: 20rpx;\r\n  \r\n  image {\r\n    width: 90rpx;\r\n    height: 90rpx;\r\n    border-radius: 50%;\r\n  }\r\n}\r\n\r\n.member-info {\r\n  flex: 1;\r\n}\r\n\r\n.member-name-row {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.member-name {\r\n  font-size: 30rpx;\r\n  font-weight: 500;\r\n  color: #333333;\r\n  margin-right: 15rpx;\r\n}\r\n\r\n.member-level {\r\n  font-size: 20rpx;\r\n  color: #ff9500;\r\n  background-color: #fff7e6;\r\n  padding: 4rpx 10rpx;\r\n  border-radius: 8rpx;\r\n}\r\n\r\n.member-data {\r\n  font-size: 24rpx;\r\n  color: #999999;\r\n}\r\n\r\n.member-right {\r\n  text-align: right;\r\n}\r\n\r\n.member-contribution {\r\n  font-size: 26rpx;\r\n  color: #ff6000;\r\n  font-weight: 500;\r\n  margin-bottom: 6rpx;\r\n}\r\n\r\n.member-fans {\r\n  font-size: 22rpx;\r\n  color: #999999;\r\n}\r\n\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 100rpx 0;\r\n  \r\n  image {\r\n    width: 240rpx;\r\n    height: 240rpx;\r\n    margin-bottom: 30rpx;\r\n  }\r\n  \r\n  text {\r\n    font-size: 28rpx;\r\n    color: #999999;\r\n    margin-bottom: 40rpx;\r\n  }\r\n  \r\n  .share-btn {\r\n    width: 300rpx;\r\n    height: 80rpx;\r\n    line-height: 80rpx;\r\n    background: linear-gradient(135deg, #1677FF, #0E5FD8);\r\n    color: #ffffff;\r\n    font-size: 28rpx;\r\n    border-radius: 40rpx;\r\n  }\r\n}\r\n\r\n.load-more, .load-end {\r\n  text-align: center;\r\n  font-size: 26rpx;\r\n  color: #999999;\r\n  padding: 30rpx 0;\r\n}\r\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/partner/pages/partner-team.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "onMounted"], "mappings": ";;;;;;;;;;;AAkGA,UAAM,eAAeA,cAAG,KAAC,oBAAI,KAAM,GAAC,SAAQ,IAAK,CAAC;AAClD,UAAM,eAAeA,cAAAA,IAAI,EAAE;AAC3B,UAAM,aAAaA,cAAAA,IAAI,EAAE;AACzB,UAAM,gBAAgBA,cAAAA,IAAI,EAAE;AAC5B,UAAM,aAAaA,cAAAA,IAAI,SAAS;AAChC,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAC1B,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAC1B,UAAM,OAAOA,cAAAA,IAAI,CAAC;AACDA,kBAAG,IAAC,EAAE;AACvB,UAAM,UAAUA,cAAAA,IAAI,IAAI;AACxB,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAI3B,UAAM,YAAYC,cAAQ,SAAC,MAAM;AAC/B,aAAO,aAAa,UAAU,IAC1B,aACA;AAAA,IACN,CAAC;AAGD,UAAM,YAAYA,cAAQ,SAAC,MAAM;AAC/B,aAAO,aAAa,UAAU,IAC1B,aACA;AAAA,IACN,CAAC;AAGD,UAAM,cAAc,CAAC,UAAU;AAC7B,UAAI,aAAa,UAAU;AAAO;AAElC,mBAAa,QAAQ;AACrB,WAAK,QAAQ;AACb,cAAQ,QAAQ;AAChB,kBAAY,QAAQ;AACpB;IACF;AAGA,UAAM,WAAW,MAAM;AACrB,gBAAU,QAAQ;AAGlB,iBAAW,MAAM;AAEf,cAAM,WAAW;AAEjB,YAAI,KAAK,UAAU,GAAG;AACpB,sBAAY,QAAQ;AAAA,QAC1B,OAAW;AACL,sBAAY,QAAQ,CAAC,GAAG,YAAY,OAAO,GAAG,QAAQ;AAAA,QACvD;AAGD,gBAAQ,QAAQ,KAAK,QAAQ;AAE7B,kBAAU,QAAQ;AAAA,MACnB,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,WAAW,MAAM;AACrB,UAAI,UAAU,SAAS,CAAC,QAAQ;AAAO;AAEvC,WAAK;AACL;IACF;AAGA,UAAM,iBAAiB,MAAM;AAC3BC,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,YAAY;AAC9B,YAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,YAAM,OAAO,KAAK;AAClB,YAAM,SAAS,KAAK,aAAa,GAAG,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC9D,YAAM,MAAM,KAAK,QAAS,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAErD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAChC;AAGA,UAAM,mBAAmB,MAAM;AAC7B,YAAM,WAAW,CAAA;AACjB,YAAM,YAAY,CAAC,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAChF,YAAM,UAAU;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AACE,YAAM,SAAS,CAAC,SAAS,SAAS,SAAS,OAAO;AAGlD,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,cAAM,MAAM,oBAAI;AAEhB,cAAM,aAAa,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE;AAChD,cAAM,WAAW,IAAI,KAAK,IAAI,QAAO,IAAK,aAAa,KAAK,KAAK,KAAK,GAAI;AAG1E,cAAM,eAAe,KAAK,MAAM,KAAK,OAAM,IAAK,GAAI;AAGpD,cAAM,UAAU,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE;AAG7C,YAAI;AACJ,YAAI,aAAa,UAAU,GAAG;AAC5B,uBAAa,KAAK,MAAM,KAAK,WAAW,OAAO,MAAM;AAAA,QAC3D,OAAW;AAEL,uBAAa,KAAK,MAAM,KAAK,OAAM,IAAK,CAAC;AAAA,QAC1C;AAED,iBAAS,KAAK;AAAA,UACZ,IAAI,YAAY,KAAK,IAAK,IAAG;AAAA,UAC7B,UAAU,UAAU,KAAK,MAAM,KAAK,OAAQ,IAAG,UAAU,MAAM,CAAC;AAAA,UAChE,QAAQ,QAAQ,KAAK,MAAM,KAAK,OAAQ,IAAG,QAAQ,MAAM,CAAC;AAAA,UAC1D;AAAA,UACA,cAAc,OAAO,UAAU;AAAA,UAC/B;AAAA,UACA;AAAA,QACN,CAAK;AAAA,MACF;AAED,aAAO;AAAA,IACT;AAGAC,kBAAAA,UAAU,MAAM;AAEd;IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5OD,GAAG,WAAW,eAAe;"}