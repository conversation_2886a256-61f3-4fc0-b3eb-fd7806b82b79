"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_rect = common_vendor.resolveComponent("rect");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  (_component_path + _component_svg + _component_rect + _component_line + _component_circle + _component_polyline)();
}
if (!Math) {
  ActivityStatusCard();
}
const ActivityStatusCard = () => "../../components/activity/ActivityStatusCard.js";
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const isRefreshing = common_vendor.ref(false);
    const currentTab = common_vendor.ref(0);
    const currentActivityType = common_vendor.ref(0);
    const currentCouponStatus = common_vendor.ref(0);
    const currentTabBar = common_vendor.ref("my");
    const unreadMessageCount = common_vendor.ref(3);
    const userInfo = common_vendor.ref({
      nickname: "张三",
      avatar: "https://via.placeholder.com/120",
      id: "10086",
      level: 5,
      isVip: true,
      points: 1280,
      favoriteCount: 16,
      historyCount: 42,
      couponCount: 8,
      orderCount: 12
    });
    const tabs = common_vendor.ref([
      { name: "进行中", status: "ongoing", emptyText: "暂无进行中的活动", actionText: "去参与活动", emptyImage: "/static/images/empty/empty-ongoing.png" },
      { name: "已报名", status: "registered", emptyText: "暂无已报名的活动", actionText: "去浏览活动", emptyImage: "/static/images/empty/empty-registered.png" },
      { name: "已完成", status: "completed", emptyText: "暂无已完成的活动", actionText: "去参与活动", emptyImage: "/static/images/empty/empty-completed.png" },
      { name: "已收藏", status: "favorite", emptyText: "暂无收藏的活动", actionText: "去浏览活动", emptyImage: "/static/images/empty/empty-favorite.png" }
    ]);
    const activityTypes = common_vendor.ref([
      { name: "全部", type: "all", emptyText: "暂无活动参与记录", emptyImage: "/static/images/empty/empty-activity-all.png" },
      { name: "拼团", type: "group", emptyText: "暂无拼团活动参与记录", emptyImage: "/static/images/empty/empty-activity-group.png" },
      { name: "秒杀", type: "flash", emptyText: "暂无秒杀活动参与记录", emptyImage: "/static/images/empty/empty-activity-flash.png" },
      { name: "优惠券", type: "coupon", emptyText: "暂无优惠券活动参与记录", emptyImage: "/static/images/empty/empty-activity-coupon.png" }
    ]);
    const couponStatuses = common_vendor.ref([
      { name: "全部", status: "all", emptyText: "暂无优惠券", emptyImage: "/static/images/empty/empty-coupon-all.png" },
      { name: "未使用", status: "unused", emptyText: "暂无未使用优惠券", emptyImage: "/static/images/empty/empty-coupon-unused.png" },
      { name: "已使用", status: "used", emptyText: "暂无已使用优惠券", emptyImage: "/static/images/empty/empty-coupon-used.png" },
      { name: "已过期", status: "expired", emptyText: "暂无已过期优惠券", emptyImage: "/static/images/empty/empty-coupon-expired.png" }
    ]);
    const orderCounts = common_vendor.ref({
      pendingPayment: 3,
      pendingDelivery: 2,
      pendingReceipt: 1,
      pendingReview: 5,
      afterSale: 0
    });
    const activities = common_vendor.ref([]);
    common_vendor.ref([
      {
        id: 1,
        title: "限时秒杀：iPhone 14 Pro",
        startTime: (/* @__PURE__ */ new Date()).getTime(),
        location: "线上活动",
        type: "flash"
      },
      {
        id: 2,
        title: "3人团：小米空气净化器",
        startTime: (/* @__PURE__ */ new Date()).getTime() + 2 * 60 * 60 * 1e3,
        // 2小时后
        location: "线上活动",
        type: "group"
      },
      {
        id: 3,
        title: "满300减50全场优惠",
        startTime: (/* @__PURE__ */ new Date()).getTime() + 1 * 24 * 60 * 60 * 1e3,
        // 明天
        location: "线上活动",
        type: "discount"
      }
    ]);
    const swiperHeight = common_vendor.ref(600);
    const backToMainMyPage = () => {
      common_vendor.index.navigateBack();
    };
    const showActivityFilter = () => {
      common_vendor.index.showToast({
        title: "筛选功能开发中",
        icon: "none"
      });
    };
    const switchTab = (index) => {
      currentTab.value = index;
      updateSwiperHeight(index);
    };
    const onSwiperChange = (e) => {
      currentTab.value = e.detail.current;
      updateSwiperHeight(e.detail.current);
    };
    const switchActivityType = (index) => {
      currentActivityType.value = index;
      updateSwiperHeight(currentTab.value);
    };
    const updateSwiperHeight = (tabIndex = 0) => {
      setTimeout(() => {
        const query = common_vendor.index.createSelectorQuery();
        query.select(`#activity-list-${tabIndex}`).boundingClientRect((data) => {
          if (data && data.height > 0) {
            swiperHeight.value = Math.max(600, data.height + 100);
          } else {
            const filteredActivities = getFilteredActivities(
              tabs.value[tabIndex].status,
              activityTypes.value[currentActivityType.value].type
            );
            if (filteredActivities.length > 0) {
              swiperHeight.value = filteredActivities.length * 400 + 100;
            } else {
              swiperHeight.value = 600;
            }
          }
        }).exec();
      }, 300);
    };
    const getActivitiesByStatus = (status) => {
      if (status === "ongoing") {
        return [
          {
            id: 1,
            title: "限时秒杀：iPhone 14 Pro",
            coverImage: "https://via.placeholder.com/300x200",
            shopName: "Apple授权专卖店",
            startTime: (/* @__PURE__ */ new Date()).getTime() - 2 * 60 * 60 * 1e3,
            // 2小时前
            endTime: (/* @__PURE__ */ new Date()).getTime() + 10 * 60 * 60 * 1e3,
            // 10小时后
            currentPrice: 6999,
            originalPrice: 8999,
            type: "flash",
            status: "ongoing"
          },
          {
            id: 2,
            title: "3人团：小米空气净化器",
            coverImage: "https://via.placeholder.com/300x200",
            shopName: "小米官方旗舰店",
            startTime: (/* @__PURE__ */ new Date()).getTime() - 5 * 60 * 60 * 1e3,
            // 5小时前
            endTime: (/* @__PURE__ */ new Date()).getTime() + 24 * 60 * 60 * 1e3,
            // 24小时后
            currentPrice: 699,
            originalPrice: 999,
            type: "group",
            status: "ongoing"
          }
        ];
      } else if (status === "registered") {
        return [
          {
            id: 3,
            title: "满300减50全场优惠",
            coverImage: "https://via.placeholder.com/300x200",
            shopName: "京东自营",
            startTime: (/* @__PURE__ */ new Date()).getTime() + 1 * 24 * 60 * 60 * 1e3,
            // 明天
            endTime: (/* @__PURE__ */ new Date()).getTime() + 8 * 24 * 60 * 60 * 1e3,
            // 8天后
            currentPrice: 300,
            type: "discount",
            status: "registered"
          }
        ];
      } else if (status === "completed") {
        return [
          {
            id: 4,
            title: "星巴克咖啡买一送一",
            coverImage: "https://via.placeholder.com/300x200",
            shopName: "星巴克(万达广场店)",
            startTime: (/* @__PURE__ */ new Date()).getTime() - 10 * 24 * 60 * 60 * 1e3,
            // 10天前
            endTime: (/* @__PURE__ */ new Date()).getTime() - 3 * 24 * 60 * 60 * 1e3,
            // 3天前
            currentPrice: 30,
            type: "coupon",
            status: "completed"
          }
        ];
      } else if (status === "favorite") {
        return [
          {
            id: 5,
            title: "华为P50 Pro限时特惠",
            coverImage: "https://via.placeholder.com/300x200",
            shopName: "华为授权体验店",
            startTime: (/* @__PURE__ */ new Date()).getTime() - 1 * 24 * 60 * 60 * 1e3,
            // 1天前
            endTime: (/* @__PURE__ */ new Date()).getTime() + 6 * 24 * 60 * 60 * 1e3,
            // 6天后
            currentPrice: 5299,
            originalPrice: 6488,
            type: "flash",
            status: "favorite"
          }
        ];
      }
      return [];
    };
    const getCouponsByStatus = (status) => {
      if (status === "all") {
        return [
          { id: 1, name: "满300减50全场优惠券", value: 50, condition: "满300元可用", validityPeriod: "2023-12-31", shopName: "京东自营", status: "unused" },
          { id: 2, name: "星巴克咖啡买一送一优惠券", value: 30, condition: "满30元可用", validityPeriod: "2023-11-30", shopName: "星巴克(万达广场店)", status: "unused" },
          { id: 3, name: "满100减10优惠券", value: 10, condition: "满100元可用", validityPeriod: "2023-12-15", shopName: "Apple授权专卖店", status: "used" }
        ];
      } else if (status === "unused") {
        return [
          { id: 1, name: "满300减50全场优惠券", value: 50, condition: "满300元可用", validityPeriod: "2023-12-31", shopName: "京东自营", status: "unused" },
          { id: 2, name: "星巴克咖啡买一送一优惠券", value: 30, condition: "满30元可用", validityPeriod: "2023-11-30", shopName: "星巴克(万达广场店)", status: "unused" }
        ];
      } else if (status === "used") {
        return [
          { id: 1, name: "满100减10优惠券", value: 10, condition: "满100元可用", validityPeriod: "2023-12-15", shopName: "Apple授权专卖店", status: "used" }
        ];
      } else if (status === "expired") {
        return [
          { id: 1, name: "满50减5优惠券", value: 5, condition: "满50元可用", validityPeriod: "2023-11-20", shopName: "小米官方旗舰店", status: "expired" }
        ];
      }
      return [];
    };
    const getFilteredActivities = (status, type) => {
      const statusFiltered = getActivitiesByStatus(status);
      if (type === "all") {
        return statusFiltered;
      }
      return statusFiltered.filter((activity) => activity.type === type);
    };
    const viewActivityDetail = (activity) => {
      let url = "";
      switch (activity.type) {
        case "flash":
          url = `/subPackages/activity-showcase/pages/flash-sale/detail?id=${activity.id}`;
          break;
        case "group":
          url = `/subPackages/activity-showcase/pages/group-buy/detail?id=${activity.id}`;
          break;
        case "discount":
          url = `/subPackages/activity-showcase/pages/discount/detail?id=${activity.id}`;
          break;
        case "coupon":
          url = `/subPackages/activity-showcase/pages/coupon/detail?id=${activity.id}`;
          break;
        default:
          url = `/subPackages/activity-showcase/pages/detail/index?id=${activity.id}&type=${activity.type}`;
      }
      navigateTo(url);
    };
    const shareActivity = (activity) => {
      common_vendor.index.showShareMenu({
        withShareTicket: true,
        success() {
          common_vendor.index.showToast({
            title: "分享成功",
            icon: "success"
          });
        }
      });
    };
    const cancelActivity = (activity) => {
      common_vendor.index.showModal({
        title: "取消活动",
        content: "确定要取消该活动吗？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "已取消活动",
              icon: "success"
            });
          }
        }
      });
    };
    const switchCouponStatus = (index) => {
      currentCouponStatus.value = index;
    };
    const useCoupon = (coupon) => {
      common_vendor.index.showToast({
        title: `已使用优惠券: ${coupon.name}`,
        icon: "success"
      });
    };
    const viewCouponDetail = (coupon) => {
      common_vendor.index.showToast({
        title: `查看优惠券: ${coupon.name} 详情`,
        icon: "none"
      });
    };
    const getCouponBackground = (status) => {
      switch (status) {
        case "unused":
          return "linear-gradient(135deg, #FF8000 0%, #FFA500 80%, #FFBF00 100%)";
        case "used":
          return "linear-gradient(135deg, #2EB84D 0%, #34C759 80%, #30D158 100%)";
        case "expired":
          return "linear-gradient(135deg, #636366 0%, #8E8E93 80%, #AEAEB2 100%)";
        default:
          return "linear-gradient(135deg, #FF8000 0%, #FFA500 80%, #FFBF00 100%)";
      }
    };
    const navigateTo = (url) => {
      common_vendor.index.navigateTo({ url });
    };
    const navigateToMainSettings = () => {
      common_vendor.index.navigateTo({
        url: "/subPackages/activity-showcase/pages/settings/index"
      });
    };
    const onRefresh = () => {
      isRefreshing.value = true;
      setTimeout(() => {
        isRefreshing.value = false;
        common_vendor.index.showToast({
          title: "刷新成功",
          icon: "success"
        });
      }, 1e3);
    };
    const loadMore = () => {
      common_vendor.index.showToast({
        title: "已加载全部数据",
        icon: "none"
      });
    };
    common_vendor.onMounted(() => {
      activities.value = getActivitiesByStatus(tabs.value[currentTab.value].status);
      setTimeout(() => {
        updateSwiperHeight(currentTab.value);
      }, 500);
    });
    common_vendor.onShow(() => {
      activities.value = getActivitiesByStatus(tabs.value[currentTab.value].status);
      setTimeout(() => {
        updateSwiperHeight(currentTab.value);
      }, 500);
    });
    const switchTabBar = (tab) => {
      if (currentTabBar.value === tab)
        return;
      currentTabBar.value = tab;
      switch (tab) {
        case "home":
          common_vendor.index.reLaunch({
            url: "/subPackages/activity-showcase/pages/index/index",
            fail: (err) => {
              common_vendor.index.__f__("error", "at subPackages/activity-showcase/pages/my/index.vue:1254", "页面跳转失败:", err);
              common_vendor.index.showToast({
                title: "页面跳转失败",
                icon: "none"
              });
            }
          });
          break;
        case "discover":
          common_vendor.index.navigateTo({
            url: "/subPackages/activity-showcase/pages/discover/index",
            fail: (err) => {
              common_vendor.index.__f__("error", "at subPackages/activity-showcase/pages/my/index.vue:1266", "页面跳转失败:", err);
              common_vendor.index.showToast({
                title: "页面跳转失败",
                icon: "none"
              });
            }
          });
          break;
        case "distribution":
          common_vendor.index.navigateTo({
            url: "/subPackages/activity-showcase/pages/distribution/index",
            fail: (err) => {
              common_vendor.index.__f__("error", "at subPackages/activity-showcase/pages/my/index.vue:1278", "页面跳转失败:", err);
              common_vendor.index.showToast({
                title: "页面跳转失败",
                icon: "none"
              });
            }
          });
          break;
        case "message":
          common_vendor.index.navigateTo({
            url: "/subPackages/activity-showcase/pages/message/index",
            fail: (err) => {
              common_vendor.index.__f__("error", "at subPackages/activity-showcase/pages/my/index.vue:1290", "页面跳转失败:", err);
              common_vendor.index.showToast({
                title: "页面跳转失败",
                icon: "none"
              });
            }
          });
          break;
      }
    };
    const recommendedActivities = common_vendor.ref([
      {
        id: 101,
        title: "3人拼团：戴森吹风机",
        coverImage: "https://via.placeholder.com/300x200",
        shopName: "戴森官方旗舰店",
        startTime: (/* @__PURE__ */ new Date()).getTime(),
        endTime: (/* @__PURE__ */ new Date()).getTime() + 3 * 24 * 60 * 60 * 1e3,
        // 3天后
        currentPrice: 1999,
        originalPrice: 2999,
        type: "group",
        matchRate: 95,
        participationCount: 1283
      },
      {
        id: 102,
        title: "限时秒杀：小米手环8",
        coverImage: "https://via.placeholder.com/300x200",
        shopName: "小米官方旗舰店",
        startTime: (/* @__PURE__ */ new Date()).getTime(),
        endTime: (/* @__PURE__ */ new Date()).getTime() + 1 * 24 * 60 * 60 * 1e3,
        // 1天后
        currentPrice: 199,
        originalPrice: 299,
        type: "flash",
        matchRate: 87,
        participationCount: 3521
      },
      {
        id: 103,
        title: "满200减50：星巴克咖啡",
        coverImage: "https://via.placeholder.com/300x200",
        shopName: "星巴克(万达广场店)",
        startTime: (/* @__PURE__ */ new Date()).getTime(),
        endTime: (/* @__PURE__ */ new Date()).getTime() + 7 * 24 * 60 * 60 * 1e3,
        // 7天后
        currentPrice: 200,
        type: "discount",
        matchRate: 82,
        participationCount: 872
      }
    ]);
    const getActivityTypeText = (type) => {
      switch (type) {
        case "flash":
          return "秒杀";
        case "group":
          return "拼团";
        case "discount":
          return "优惠";
        case "coupon":
          return "券";
        default:
          return "活动";
      }
    };
    const getRecommendationBackground = (type) => {
      switch (type) {
        case "flash":
          return "linear-gradient(to bottom right, #FFFFFF, #FFF5F5)";
        case "group":
          return "linear-gradient(to bottom right, #FFFFFF, #F5F5FF)";
        case "discount":
          return "linear-gradient(to bottom right, #FFFFFF, #F5FFFA)";
        case "coupon":
          return "linear-gradient(to bottom right, #FFFFFF, #FFFAF5)";
        default:
          return "linear-gradient(to bottom right, #FFFFFF, #F8F8F8)";
      }
    };
    const getRecommendationTagBackground = (type) => {
      switch (type) {
        case "flash":
          return "linear-gradient(135deg, #FF3B30 0%, #FF6E6E 100%)";
        case "group":
          return "linear-gradient(135deg, #5856D6 0%, #7A7AFF 100%)";
        case "discount":
          return "linear-gradient(135deg, #34C759 0%, #5EE077 100%)";
        case "coupon":
          return "linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)";
        default:
          return "linear-gradient(135deg, #5AC8FA 0%, #64D2FF 100%)";
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          d: "M19 12H5M12 19l-7-7 7-7",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        b: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        c: common_vendor.o(backToMainMyPage),
        d: common_vendor.p({
          d: "M22 3H2l8 9.46V19l4 2v-8.54L22 3z",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        e: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        f: common_vendor.o(showActivityFilter),
        g: userInfo.value.avatar || "/static/images/default-avatar.png",
        h: userInfo.value.isVip
      }, userInfo.value.isVip ? {} : {}, {
        i: common_vendor.t(userInfo.value.nickname || "游客"),
        j: common_vendor.t(userInfo.value.level || 1),
        k: common_vendor.t(userInfo.value.id || "未设置"),
        l: common_vendor.t(userInfo.value.points || 0),
        m: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/user-profile/index")),
        n: common_vendor.t(userInfo.value.favoriteCount || 0),
        o: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/favorites/index")),
        p: common_vendor.t(userInfo.value.historyCount || 0),
        q: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/history/index")),
        r: common_vendor.t(userInfo.value.couponCount || 0),
        s: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/coupon/index")),
        t: common_vendor.t(userInfo.value.orderCount || 0),
        v: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/orders/index")),
        w: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#FF3B69",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        x: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        y: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/activity-records/index")),
        z: common_vendor.f(tabs.value, (tab, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(tab.name),
            b: currentTab.value === index
          }, currentTab.value === index ? {} : {}, {
            c: index,
            d: currentTab.value === index ? 1 : "",
            e: common_vendor.o(($event) => switchTab(index), index)
          });
        }),
        A: common_vendor.f(activityTypes.value, (activityType, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(activityType.name),
            b: currentActivityType.value === index
          }, currentActivityType.value === index ? {} : {}, {
            c: index,
            d: currentActivityType.value === index ? 1 : "",
            e: common_vendor.o(($event) => switchActivityType(index), index)
          });
        }),
        B: common_vendor.f(tabs.value, (tab, tabIndex, i0) => {
          return common_vendor.e({
            a: common_vendor.f(getFilteredActivities(tab.status, activityTypes.value[currentActivityType.value].type), (activity, k1, i1) => {
              return {
                a: activity.id,
                b: common_vendor.o(($event) => viewActivityDetail(activity), activity.id),
                c: common_vendor.o(($event) => shareActivity(), activity.id),
                d: common_vendor.o(($event) => cancelActivity(), activity.id),
                e: common_vendor.o(($event) => activity.isFavorite = !activity.isFavorite, activity.id),
                f: "d62e35d3-6-" + i0 + "-" + i1,
                g: common_vendor.p({
                  activity,
                  showCountdown: activity.type === "flash",
                  canCancel: activity.status === "registered"
                })
              };
            }),
            b: `activity-list-${tabIndex}`,
            c: getFilteredActivities(tab.status, activityTypes.value[currentActivityType.value].type).length === 0
          }, getFilteredActivities(tab.status, activityTypes.value[currentActivityType.value].type).length === 0 ? {
            d: tab.emptyImage,
            e: common_vendor.t(tab.emptyText),
            f: common_vendor.t(tab.actionText),
            g: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/index/index"), tabIndex)
          } : {}, {
            h: `tab-content-${tabIndex}`,
            i: tabIndex
          });
        }),
        C: currentTab.value,
        D: common_vendor.o(onSwiperChange),
        E: swiperHeight.value + "px",
        F: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#5AC8FA",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        G: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        H: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/orders/index")),
        I: common_vendor.p({
          x: "2",
          y: "4",
          width: "20",
          height: "16",
          rx: "2",
          stroke: "#5AC8FA",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        J: common_vendor.p({
          x1: "12",
          y1: "16",
          x2: "12",
          y2: "16.01",
          stroke: "#5AC8FA",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        K: common_vendor.p({
          d: "M8 12h8M8 8h8",
          stroke: "#5AC8FA",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        L: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        M: orderCounts.value.pendingPayment > 0
      }, orderCounts.value.pendingPayment > 0 ? {
        N: common_vendor.t(orderCounts.value.pendingPayment)
      } : {}, {
        O: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/orders/index?status=pending_payment")),
        P: common_vendor.p({
          x: "2",
          y: "4",
          width: "20",
          height: "16",
          rx: "2",
          stroke: "#5AC8FA",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        Q: common_vendor.p({
          d: "M16 10V6M8 10V6M4 10h16",
          stroke: "#5AC8FA",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        R: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        S: orderCounts.value.pendingDelivery > 0
      }, orderCounts.value.pendingDelivery > 0 ? {
        T: common_vendor.t(orderCounts.value.pendingDelivery)
      } : {}, {
        U: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/orders/index?status=pending_delivery")),
        V: common_vendor.p({
          d: "M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0118 0z",
          stroke: "#5AC8FA",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        W: common_vendor.p({
          cx: "12",
          cy: "10",
          r: "3",
          stroke: "#5AC8FA",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        X: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        Y: orderCounts.value.pendingReceipt > 0
      }, orderCounts.value.pendingReceipt > 0 ? {
        Z: common_vendor.t(orderCounts.value.pendingReceipt)
      } : {}, {
        aa: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/orders/index?status=pending_receipt")),
        ab: common_vendor.p({
          d: "M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7",
          stroke: "#5AC8FA",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        ac: common_vendor.p({
          d: "M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z",
          stroke: "#5AC8FA",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        ad: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        ae: orderCounts.value.pendingReview > 0
      }, orderCounts.value.pendingReview > 0 ? {
        af: common_vendor.t(orderCounts.value.pendingReview)
      } : {}, {
        ag: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/orders/index?status=pending_review")),
        ah: common_vendor.p({
          d: "M16 15v4a2 2 0 01-2 2h-4a2 2 0 01-2-2v-4M8.929 9.571L12 12.643l3.071-3.072M12 3v9.643",
          stroke: "#5AC8FA",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        ai: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        aj: orderCounts.value.afterSale > 0
      }, orderCounts.value.afterSale > 0 ? {
        ak: common_vendor.t(orderCounts.value.afterSale)
      } : {}, {
        al: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/orders/index?status=after_sale")),
        am: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#FF9500",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        an: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        ao: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/coupon/index")),
        ap: common_vendor.f(couponStatuses.value, (couponStatus, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(couponStatus.name),
            b: currentCouponStatus.value === index
          }, currentCouponStatus.value === index ? {} : {}, {
            c: index,
            d: currentCouponStatus.value === index ? 1 : "",
            e: common_vendor.o(($event) => switchCouponStatus(index), index)
          });
        }),
        aq: common_vendor.f(getCouponsByStatus(couponStatuses.value[currentCouponStatus.value].status), (coupon, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(coupon.value),
            b: common_vendor.t(coupon.condition),
            c: common_vendor.t(coupon.name),
            d: common_vendor.t(coupon.shopName),
            e: common_vendor.t(coupon.validityPeriod),
            f: coupon.status === "unused"
          }, coupon.status === "unused" ? {
            g: common_vendor.o(($event) => useCoupon(coupon), coupon.id)
          } : coupon.status === "used" ? {} : {}, {
            h: coupon.status === "used",
            i: coupon.id,
            j: getCouponBackground(coupon.status),
            k: common_vendor.o(($event) => viewCouponDetail(coupon), coupon.id)
          });
        }),
        ar: getCouponsByStatus(couponStatuses.value[currentCouponStatus.value].status).length === 0
      }, getCouponsByStatus(couponStatuses.value[currentCouponStatus.value].status).length === 0 ? {
        as: couponStatuses.value[currentCouponStatus.value].emptyImage,
        at: common_vendor.t(couponStatuses.value[currentCouponStatus.value].emptyText),
        av: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/coupon/index"))
      } : {}, {
        aw: common_vendor.p({
          d: "M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        ax: common_vendor.p({
          d: "M13.73 21a2 2 0 0 1-3.46 0",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        ay: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        az: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/reminders/index")),
        aA: common_vendor.p({
          cx: "18",
          cy: "5",
          r: "3",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        aB: common_vendor.p({
          cx: "6",
          cy: "12",
          r: "3",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        aC: common_vendor.p({
          cx: "18",
          cy: "19",
          r: "3",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        aD: common_vendor.p({
          x1: "8.59",
          y1: "13.51",
          x2: "15.42",
          y2: "17.49",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        aE: common_vendor.p({
          x1: "15.41",
          y1: "6.51",
          x2: "8.59",
          y2: "10.49",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        aF: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        aG: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/share-records/index")),
        aH: common_vendor.p({
          d: "M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        aI: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        aJ: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/favorites/index")),
        aK: common_vendor.p({
          cx: "12",
          cy: "12",
          r: "10",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        aL: common_vendor.p({
          points: "12 6 12 12 16 14",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        aM: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        aN: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/history/index")),
        aO: common_vendor.p({
          cx: "12",
          cy: "12",
          r: "10",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        aP: common_vendor.p({
          d: "M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        aQ: common_vendor.p({
          x1: "12",
          y1: "17",
          x2: "12.01",
          y2: "17",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        aR: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        aS: common_vendor.o(navigateToMainSettings),
        aT: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#5AC8FA",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        aU: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        aV: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/recommendations/index")),
        aW: common_vendor.f(recommendedActivities.value, (activity, index, i0) => {
          return common_vendor.e({
            a: activity.coverImage,
            b: common_vendor.t(getActivityTypeText(activity.type)),
            c: getRecommendationTagBackground(activity.type),
            d: common_vendor.t(activity.title),
            e: common_vendor.t(activity.shopName),
            f: common_vendor.t(activity.currentPrice),
            g: activity.originalPrice
          }, activity.originalPrice ? {
            h: common_vendor.t(activity.originalPrice)
          } : {}, {
            i: common_vendor.t(activity.participationCount),
            j: activity.id,
            k: common_vendor.o(($event) => viewActivityDetail(activity), activity.id),
            l: getRecommendationBackground(activity.type)
          });
        }),
        aX: recommendedActivities.value.length === 0
      }, recommendedActivities.value.length === 0 ? {
        aY: common_assets._imports_0$56,
        aZ: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/index/index"))
      } : {}, {
        ba: isRefreshing.value,
        bb: common_vendor.o(onRefresh),
        bc: common_vendor.o(loadMore),
        bd: currentTabBar.value === "home" ? 1 : "",
        be: common_vendor.o(($event) => switchTabBar("home")),
        bf: currentTabBar.value === "discover" ? 1 : "",
        bg: common_vendor.o(($event) => switchTabBar("discover")),
        bh: currentTabBar.value === "distribution" ? 1 : "",
        bi: common_vendor.o(($event) => switchTabBar("distribution")),
        bj: unreadMessageCount.value > 0
      }, unreadMessageCount.value > 0 ? {
        bk: common_vendor.t(unreadMessageCount.value > 99 ? "99+" : unreadMessageCount.value)
      } : {}, {
        bl: currentTabBar.value === "message" ? 1 : "",
        bm: common_vendor.o(($event) => switchTabBar("message")),
        bn: currentTabBar.value === "my" ? 1 : "",
        bo: common_vendor.o(($event) => switchTabBar("my"))
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d62e35d3"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/my/index.js.map
