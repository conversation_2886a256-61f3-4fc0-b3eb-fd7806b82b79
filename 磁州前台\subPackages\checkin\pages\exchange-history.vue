<template>
	<view class="page-root">
		<view class="nav-bg" :style="{ height: (statusBarHeight + 44) + 'px' }"></view>
		<view class="navbar-content" :style="{ top: statusBarHeight + 'px', height: '44px' }">
			<view class="navbar-left" @click="goBack">
				<image class="back-icon" src="/static/images/tabbar/最新返回键.png" mode="aspectFit"></image>
			</view>
			<text class="navbar-title">兑换记录</text>
			<view class="navbar-right"></view>
		</view>
		<view class="exchange-history-container" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
		<!-- 筛选标签 -->
		<view class="filter-tabs">
			<view 
				class="tab-item" 
				:class="{ active: currentTab === index }"
				v-for="(tab, index) in tabs" 
				:key="index"
				@click="switchTab(index)"
			>
				{{ tab }}
			</view>
		</view>
		
		<!-- 记录列表 -->
		<scroll-view 
			scroll-y 
			class="record-list" 
			refresher-enabled 
			@refresherrefresh="refreshRecords" 
			:refresher-triggered="isRefreshing"
		>
			<view v-if="filteredRecords.length > 0">
				<view class="record-item" v-for="(record, index) in filteredRecords" :key="index" @click="showRecordDetail(record)">
					<view class="record-main">
						<image class="record-image" :src="record.image" mode="aspectFill"></image>
						<view class="record-info">
							<view class="record-name">{{ record.name }}</view>
							<view class="record-time">{{ record.time }}</view>
							<view class="record-status" :class="'status-' + record.status">{{ statusText[record.status] }}</view>
						</view>
					</view>
					<view class="record-points">{{ record.points }}积分</view>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view class="empty-state" v-else>
				<image class="empty-icon" src="/static/images/tabbar/empty.png"></image>
				<view class="empty-text">暂无兑换记录</view>
			</view>
		</scroll-view>
		
		<!-- 详情弹窗 -->
		<view class="detail-popup" v-if="showDetailPopup" @click.self="closeDetailPopup">
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-title">兑换详情</text>
					<view class="popup-close" @click="closeDetailPopup">×</view>
				</view>
				
				<view class="popup-body">
					<view class="detail-item">
						<image class="detail-image" :src="selectedRecord.image" mode="aspectFill"></image>
						<view class="detail-info">
							<view class="detail-name">{{ selectedRecord.name }}</view>
							<view class="detail-points">{{ selectedRecord.points }}积分</view>
						</view>
					</view>
					
					<view class="detail-grid">
						<view class="grid-item">
							<view class="grid-label">兑换时间</view>
							<view class="grid-value">{{ selectedRecord.fullTime || selectedRecord.time }}</view>
						</view>
						<view class="grid-item">
							<view class="grid-label">订单编号</view>
							<view class="grid-value">{{ selectedRecord.orderNo }}</view>
						</view>
						<view class="grid-item">
							<view class="grid-label">兑换状态</view>
							<view class="grid-value status" :class="'status-' + selectedRecord.status">{{ statusText[selectedRecord.status] }}</view>
						</view>
						<view class="grid-item" v-if="selectedRecord.status === 'using'">
							<view class="grid-label">有效期</view>
							<view class="grid-value">{{ selectedRecord.validity }}</view>
						</view>
						<view class="grid-item" v-if="selectedRecord.status === 'shipping' || selectedRecord.status === 'shipped'">
							<view class="grid-label">收货地址</view>
							<view class="grid-value">{{ selectedRecord.address }}</view>
						</view>
						<view class="grid-item" v-if="selectedRecord.status === 'shipped'">
							<view class="grid-label">物流信息</view>
							<view class="grid-value">{{ selectedRecord.logistics }}</view>
						</view>
					</view>
					
					<view class="detail-actions" v-if="selectedRecord.status === 'using'">
						<button class="action-btn" @click="useProduct">立即使用</button>
					</view>
					<view class="detail-actions" v-if="selectedRecord.status === 'shipping'">
						<button class="action-btn" @click="trackOrder">查看物流</button>
					</view>
					<view class="detail-actions" v-if="selectedRecord.status === 'shipped'">
						<button class="action-btn confirm-btn" @click="confirmReceive">确认收货</button>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// Vue3迁移代码开始
// 响应式状态
const statusBarHeight = ref(20);
const currentTab = ref(0);
const isRefreshing = ref(false);
const showDetailPopup = ref(false);
const selectedRecord = ref({});

// 常量数据
const tabs = ['全部', '待发货', '已发货', '使用中'];
const statusText = {
	'checking': '审核中',
	'shipping': '待发货',
	'shipped': '已发货',
	'using': '使用中',
	'used': '已使用',
	'expired': '已过期'
};

// 记录数据
const records = ref([
	{
		id: 1,
		name: '5元通用券',
		points: 100,
		time: '2023-11-25 14:30',
		status: 'using',
		image: '/static/images/banner/coupon-1.png',
		orderNo: 'E202311250001',
		validity: '2023-12-25前有效',
		type: 'coupon'
	},
	{
		id: 2,
		name: '精美保温杯',
		points: 1200,
		time: '2023-11-22 09:15',
		status: 'shipping',
		image: '/static/images/banner/product-1.png',
		orderNo: 'E202311220003',
		address: '河北省邯郸市肥乡区北尹堡村1号',
		type: 'product'
	},
	{
		id: 3,
		name: '10元外卖券',
		points: 180,
		time: '2023-11-20 18:45',
		status: 'used',
		image: '/static/images/banner/coupon-2.png',
		orderNo: 'E202311200007',
		type: 'coupon'
	},
	{
		id: 4,
		name: '会员月卡',
		points: 500,
		time: '2023-11-15 10:22',
		status: 'expired',
		image: '/static/images/banner/vip-1.png',
		orderNo: 'E202311150012',
		type: 'vip'
	},
	{
		id: 5,
		name: '限时抢购券',
		points: 250,
		time: '2023-11-10 16:30',
		status: 'shipped',
		image: '/static/images/banner/coupon-3.png',
		orderNo: 'E202311100025',
		logistics: '顺丰速运 SF1234567890',
		address: '河北省邯郸市肥乡区北尹堡村1号',
		type: 'coupon'
	}
]);

// 计算属性
const filteredRecords = computed(() => {
	if (currentTab.value === 0) {
		return records.value;
	} else if (currentTab.value === 1) {
		// 待发货
		return records.value.filter(record => record.status === 'shipping');
	} else if (currentTab.value === 2) {
		// 已发货
		return records.value.filter(record => record.status === 'shipped');
	} else {
		// 使用中
		return records.value.filter(record => record.status === 'using');
	}
});

// 生命周期钩子
onMounted(() => {
	// 获取状态栏高度
	const sysInfo = uni.getSystemInfoSync();
	statusBarHeight.value = sysInfo.statusBarHeight || 20;
	
	// 加载兑换记录
	loadExchangeRecords();
});

// 方法
// 切换标签
function switchTab(index) {
	currentTab.value = index;
}

// 刷新记录
function refreshRecords() {
	isRefreshing.value = true;
	
	// 模拟刷新操作
	setTimeout(() => {
		loadExchangeRecords();
		isRefreshing.value = false;
		
		uni.showToast({
			title: '刷新成功',
			icon: 'none'
		});
	}, 1500);
}

// 加载兑换记录
function loadExchangeRecords() {
	// 在实际应用中，应该从API获取数据
	// 这里使用模拟数据
	// setTimeout(() => {
	//   records.value = [...];
	// }, 500);
}

// 显示记录详情
function showRecordDetail(record) {
	selectedRecord.value = { ...record };
	showDetailPopup.value = true;
}

// 关闭详情弹窗
function closeDetailPopup() {
	showDetailPopup.value = false;
}

// 使用商品
function useProduct() {
	if (selectedRecord.value.type === 'coupon') {
		uni.showToast({
			title: '优惠券已复制到剪贴板',
			icon: 'none'
		});
	} else if (selectedRecord.value.type === 'vip') {
		uni.showToast({
			title: 'VIP权益已激活',
			icon: 'success'
		});
	} else {
		uni.showToast({
			title: '即将跳转到使用页面',
			icon: 'none'
		});
	}
	
	closeDetailPopup();
}

// 查看物流
function trackOrder() {
	uni.showToast({
		title: '正在开发中，敬请期待',
		icon: 'none'
	});
	
	// closeDetailPopup();
}

// 确认收货
function confirmReceive() {
	uni.showModal({
		title: '确认收货',
		content: '确认已收到商品？确认后无法撤销',
		success: (res) => {
			if (res.confirm) {
				// 更新状态
				const index = records.value.findIndex(item => item.id === selectedRecord.value.id);
				if (index !== -1) {
					records.value[index].status = 'using';
					selectedRecord.value.status = 'using';
					
					uni.showToast({
						title: '确认收货成功',
						icon: 'success'
					});
				}
			}
		}
	});
}

// 返回上一页
function goBack() {
	uni.navigateBack();
}
// Vue3迁移代码结束
</script>

<style lang="scss">
.page-root {
	position: relative;
	min-height: 100vh;
	background: #f6faff;
}
.nav-bg {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	background: #1677FF;
	z-index: 100;
	width: 100%;
}
.navbar-content {
	position: fixed;
	left: 0;
	right: 0;
	z-index: 101;
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: transparent;
	width: 100%;
}
.navbar-left, .navbar-right {
	width: 44px;
	height: 44px;
	display: flex;
	align-items: center;
	justify-content: center;
}
.back-icon {
	width: 24px;
	height: 24px;
	display: block;
	background: none;
	border-radius: 0;
	margin: 0 auto;
}
.navbar-title {
	flex: 1;
	text-align: center;
	font-size: 18px;
	font-weight: 600;
	color: #fff;
	line-height: 44px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.exchange-history-container {
	min-height: 100vh;
	position: relative;
	box-sizing: border-box;
	background: #f6faff;
}
.record-list {
	display: flex;
	flex-direction: column;
	align-items: center;
}
.record-item {
	max-width: 300px;
	width: 100%;
	margin: 0 auto 16px auto;
	background: #fff;
	border-radius: 14px;
	box-shadow: 0 6px 24px rgba(22,119,255,0.12), 0 1.5px 6px rgba(0,0,0,0.06);
	padding: 18px 16px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	word-break: break-all;
	transition: box-shadow 0.3s;
}
.record-item:active, .record-item:focus, .record-item:hover {
	box-shadow: 0 12px 32px rgba(22,119,255,0.18), 0 3px 12px rgba(0,0,0,0.10);
}
.record-main {
	display: flex;
	align-items: center;
	flex: 1;
}
.record-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 12rpx;
	object-fit: cover;
	background-color: #f5f5f5;
	margin-right: 20rpx;
}
.record-info {
	display: flex;
	flex-direction: column;
}
.record-name {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 10rpx;
}
.record-time {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 10rpx;
}
.record-status {
	display: inline-block;
	font-size: 22rpx;
	padding: 4rpx 12rpx;
	border-radius: 10rpx;
}
.status-checking {
	background-color: rgba(153, 153, 153, 0.1);
	color: #999;
}
.status-shipping {
	background-color: rgba(83, 166, 255, 0.1);
	color: #3a86ff;
}
.status-shipped {
	background-color: rgba(255, 102, 0, 0.1);
	color: #ff6600;
}
.status-using {
	background-color: rgba(0, 191, 131, 0.1);
	color: #00bf83;
}
.status-used {
	background-color: rgba(153, 153, 153, 0.1);
	color: #999;
}
.status-expired {
	background-color: rgba(153, 153, 153, 0.1);
	color: #999;
}
.record-points {
	font-size: 28rpx;
	color: #ff6b6b;
	font-weight: 500;
}
.empty-state {
	padding: 100rpx 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}
.empty-icon {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 30rpx;
	opacity: 0.6;
}
.empty-text {
	font-size: 28rpx;
	color: #999;
}
.detail-popup {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 999;
}
.popup-content {
	width: 80%;
	max-width: 600rpx;
	background-color: #FFFFFF;
	border-radius: 24rpx;
	overflow: hidden;
	box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.15);
}
.popup-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	border-bottom: 1px solid #f1f1f1;
}
.popup-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}
.popup-close {
	font-size: 40rpx;
	color: #999;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
.popup-body {
	padding: 30rpx;
}
.detail-item {
	display: flex;
	align-items: center;
	padding-bottom: 30rpx;
	margin-bottom: 30rpx;
	border-bottom: 1px solid #f5f5f5;
}
.detail-image {
	width: 150rpx;
	height: 150rpx;
	border-radius: 12rpx;
	object-fit: cover;
	margin-right: 20rpx;
}
.detail-info {
	flex: 1;
}
.detail-name {
	font-size: 30rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 12rpx;
}
.detail-points {
	font-size: 28rpx;
	color: #ff6b6b;
	font-weight: 500;
}
.detail-grid {
	display: flex;
	flex-wrap: wrap;
}
.grid-item {
	width: 100%;
	margin-bottom: 24rpx;
}
.grid-label {
	font-size: 26rpx;
	color: #999;
	margin-bottom: 8rpx;
}
.grid-value {
	font-size: 28rpx;
	color: #333;
	word-break: break-all;
}
.grid-value.status {
	display: inline-block;
	padding: 4rpx 12rpx;
	border-radius: 10rpx;
}
.detail-actions {
	margin-top: 40rpx;
	display: flex;
	justify-content: center;
}
.action-btn {
	width: 80%;
	height: 80rpx;
	line-height: 80rpx;
	background: linear-gradient(135deg, #3a86ff, #1a56cc);
	color: #FFFFFF;
	font-size: 28rpx;
	font-weight: 500;
	border-radius: 40rpx;
	box-shadow: 0 10rpx 20rpx rgba(58, 134, 255, 0.2);
}
.confirm-btn {
	background: linear-gradient(135deg, #ff6600, #ff8533);
	box-shadow: 0 10rpx 20rpx rgba(255, 102, 0, 0.2);
}
.filter-tabs {
	margin: calc(var(--status-bar-height, 0) + 88rpx + 20rpx) 30rpx 30rpx;
	display: flex;
	background-color: #eaf3ff;
	border-radius: 16rpx;
	overflow: hidden;
	padding: 6rpx;
}
.tab-item {
	flex: 1;
	text-align: center;
	padding: 16rpx 0;
	font-size: 28rpx;
	color: #666;
	transition: all 0.3s;
	border-radius: 12rpx;
}
.tab-item.active {
	background-color: #e6f0ff;
	color: #1677FF;
	font-weight: 600;
	box-shadow: 0 2rpx 8rpx rgba(22,119,255,0.08);
	border: 1.5px solid #1677FF;
}
.tab-item:not(.active):hover {
	background: #f0f7ff;
}
</style> 
