"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const utils_navigation = require("../../../utils/navigation.js");
const _sfc_main = {
  __name: "messages",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const navbarHeight = common_vendor.ref(64);
    const tabsHeight = common_vendor.ref(44);
    const tabs = common_vendor.ref([
      { name: "系统", unread: 2 },
      { name: "互动", unread: 5 },
      { name: "聊天", unread: 3 }
    ]);
    const currentTab = common_vendor.ref(0);
    const systemMessages = common_vendor.ref([]);
    const interactionMessages = common_vendor.ref([]);
    const chatMessages = common_vendor.ref([]);
    const page = common_vendor.ref([1, 1, 1]);
    common_vendor.ref(10);
    const hasMore = common_vendor.ref([true, true, true]);
    const refreshing = common_vendor.ref([false, false, false]);
    const tabLineStyle = common_vendor.computed(() => {
      return {
        transform: `translateX(${currentTab.value * 100}%)`
      };
    });
    common_vendor.onMounted(() => {
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight;
      navbarHeight.value = statusBarHeight.value + 44;
      loadSystemMessages();
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const switchTab = (index) => {
      if (currentTab.value === index)
        return;
      currentTab.value = index;
      switch (index) {
        case 0:
          if (systemMessages.value.length === 0)
            loadSystemMessages();
          break;
        case 1:
          if (interactionMessages.value.length === 0)
            loadInteractionMessages();
          break;
        case 2:
          if (chatMessages.value.length === 0)
            loadChatMessages();
          break;
      }
    };
    const onSwiperChange = (e) => {
      switchTab(e.detail.current);
    };
    const clearAllMessages = () => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要清空当前分类的所有消息吗？",
        success: (res) => {
          if (res.confirm) {
            switch (currentTab.value) {
              case 0:
                systemMessages.value = [];
                tabs.value[0].unread = 0;
                break;
              case 1:
                interactionMessages.value = [];
                tabs.value[1].unread = 0;
                break;
              case 2:
                chatMessages.value = [];
                tabs.value[2].unread = 0;
                break;
            }
            common_vendor.index.showToast({
              title: "清空成功",
              icon: "success"
            });
          }
        }
      });
    };
    const getActionText = (action) => {
      const actionMap = {
        "like": "点赞了你的",
        "comment": "评论了你的",
        "collect": "收藏了你的",
        "follow": "关注了你",
        "mention": "提到了你"
      };
      return actionMap[action] || "";
    };
    const loadSystemMessages = () => {
      setTimeout(() => {
        const mockData = Array.from({ length: 10 }, (_, i) => ({
          id: `system_${page.value[0]}_${i}`,
          title: `系统通知 ${page.value[0]}_${i}`,
          content: "您的帐号已完成实名认证，现在可以使用更多功能啦！",
          time: getRandomTime(),
          icon: "/static/images/tabbar/系统通知.png",
          isRead: Math.random() > 0.3
          // 70%概率已读
        }));
        if (page.value[0] === 1) {
          systemMessages.value = mockData;
        } else {
          systemMessages.value = [...systemMessages.value, ...mockData];
        }
        hasMore.value[0] = page.value[0] < 3;
        refreshing.value[0] = false;
      }, 500);
    };
    const loadInteractionMessages = () => {
      setTimeout(() => {
        const actions = ["like", "comment", "collect", "follow", "mention"];
        const mockData = Array.from({ length: 10 }, (_, i) => ({
          id: `interaction_${page.value[1]}_${i}`,
          userName: `用户${Math.floor(Math.random() * 1e3)}`,
          userAvatar: "/static/images/default-avatar.png",
          action: actions[Math.floor(Math.random() * actions.length)],
          content: "这是一条动态内容，描述具体的互动信息...",
          contentImage: Math.random() > 0.5 ? "/static/images/service1.jpg" : "",
          // 50%概率有图片
          time: getRandomTime(),
          isRead: Math.random() > 0.5
          // 50%概率已读
        }));
        if (page.value[1] === 1) {
          interactionMessages.value = mockData;
        } else {
          interactionMessages.value = [...interactionMessages.value, ...mockData];
        }
        hasMore.value[1] = page.value[1] < 3;
        refreshing.value[1] = false;
      }, 500);
    };
    const loadChatMessages = () => {
      setTimeout(() => {
        const mockData = Array.from({ length: 10 }, (_, i) => ({
          id: `chat_${page.value[2]}_${i}`,
          userName: `用户${Math.floor(Math.random() * 1e3)}`,
          userAvatar: "/static/images/default-avatar.png",
          lastMessage: "您好，请问有什么可以帮助您的吗？",
          time: getRandomTime(),
          unreadCount: Math.floor(Math.random() * 10)
          // 0-9条未读消息
        }));
        if (page.value[2] === 1) {
          chatMessages.value = mockData;
        } else {
          chatMessages.value = [...chatMessages.value, ...mockData];
        }
        hasMore.value[2] = page.value[2] < 3;
        refreshing.value[2] = false;
      }, 500);
    };
    const getRandomTime = () => {
      const today = /* @__PURE__ */ new Date();
      const randomDays = Math.floor(Math.random() * 7);
      const randomHours = Math.floor(Math.random() * 24);
      const randomMinutes = Math.floor(Math.random() * 60);
      const date = new Date(today);
      date.setDate(date.getDate() - randomDays);
      date.setHours(randomHours, randomMinutes);
      if (randomDays === 0) {
        return `${randomHours.toString().padStart(2, "0")}:${randomMinutes.toString().padStart(2, "0")}`;
      } else {
        return `${date.getMonth() + 1}月${date.getDate()}日`;
      }
    };
    const loadMore = (tabIndex) => {
      if (!hasMore.value[tabIndex] || refreshing.value[tabIndex])
        return;
      page.value[tabIndex]++;
      switch (tabIndex) {
        case 0:
          loadSystemMessages();
          break;
        case 1:
          loadInteractionMessages();
          break;
        case 2:
          loadChatMessages();
          break;
      }
    };
    const onRefresh = (tabIndex) => {
      refreshing.value[tabIndex] = true;
      page.value[tabIndex] = 1;
      switch (tabIndex) {
        case 0:
          loadSystemMessages();
          break;
        case 1:
          loadInteractionMessages();
          break;
        case 2:
          loadChatMessages();
          break;
      }
    };
    const viewSystemMessage = (item) => {
      if (!item.isRead) {
        item.isRead = true;
        updateUnreadCount(0);
      }
      utils_navigation.smartNavigate(`/pages/my/message-detail?id=${item.id}&type=system`);
    };
    const viewInteractionMessage = (item) => {
      if (!item.isRead) {
        item.isRead = true;
        updateUnreadCount(1);
      }
      if (item.action === "follow") {
        utils_navigation.smartNavigate(`/pages/my/profile?userId=${item.userId}`);
      } else {
        utils_navigation.smartNavigate(`/pages/publish/info-detail?id=${item.contentId}`);
      }
    };
    const navigateToChat = (item) => {
      item.unreadCount = 0;
      updateUnreadCount(2);
      utils_navigation.smartNavigate(`/pages/message/chat?userId=${item.id}`);
    };
    const updateUnreadCount = (tabIndex) => {
      let count = 0;
      switch (tabIndex) {
        case 0:
          count = systemMessages.value.filter((item) => !item.isRead).length;
          break;
        case 1:
          count = interactionMessages.value.filter((item) => !item.isRead).length;
          break;
        case 2:
          count = chatMessages.value.reduce((sum, item) => sum + item.unreadCount, 0);
          break;
      }
      tabs.value[tabIndex].unread = count;
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$5,
        b: common_vendor.o(goBack),
        c: common_vendor.o(clearAllMessages),
        d: statusBarHeight.value + "px",
        e: common_vendor.f(tabs.value, (tab, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(tab.name),
            b: tab.unread > 0
          }, tab.unread > 0 ? {
            c: common_vendor.t(tab.unread > 99 ? "99+" : tab.unread)
          } : {}, {
            d: index,
            e: currentTab.value === index ? 1 : "",
            f: common_vendor.o(($event) => switchTab(index), index)
          });
        }),
        f: common_vendor.s(tabLineStyle.value),
        g: navbarHeight.value + "px",
        h: systemMessages.value.length > 0
      }, systemMessages.value.length > 0 ? {
        i: common_vendor.f(systemMessages.value, (item, index, i0) => {
          return common_vendor.e({
            a: item.icon || "/static/images/tabbar/系统通知.png",
            b: !item.isRead
          }, !item.isRead ? {} : {}, {
            c: common_vendor.t(item.title),
            d: common_vendor.t(item.content),
            e: common_vendor.t(item.time),
            f: index,
            g: common_vendor.o(($event) => viewSystemMessage(item), index)
          });
        })
      } : {
        j: common_assets._imports_1$3
      }, {
        k: systemMessages.value.length > 0 && !hasMore.value[0]
      }, systemMessages.value.length > 0 && !hasMore.value[0] ? {} : {}, {
        l: common_vendor.o(($event) => loadMore(0)),
        m: refreshing.value[0],
        n: common_vendor.o(($event) => onRefresh(0)),
        o: interactionMessages.value.length > 0
      }, interactionMessages.value.length > 0 ? {
        p: common_vendor.f(interactionMessages.value, (item, index, i0) => {
          return common_vendor.e({
            a: item.userAvatar || "/static/images/default-avatar.png",
            b: !item.isRead
          }, !item.isRead ? {} : {}, {
            c: common_vendor.t(item.userName),
            d: common_vendor.t(getActionText(item.action)),
            e: common_vendor.t(item.content),
            f: common_vendor.t(item.time),
            g: item.contentImage
          }, item.contentImage ? {
            h: item.contentImage
          } : {}, {
            i: index,
            j: common_vendor.o(($event) => viewInteractionMessage(item), index)
          });
        })
      } : {
        q: common_assets._imports_1$3
      }, {
        r: interactionMessages.value.length > 0 && !hasMore.value[1]
      }, interactionMessages.value.length > 0 && !hasMore.value[1] ? {} : {}, {
        s: common_vendor.o(($event) => loadMore(1)),
        t: refreshing.value[1],
        v: common_vendor.o(($event) => onRefresh(1)),
        w: chatMessages.value.length > 0
      }, chatMessages.value.length > 0 ? {
        x: common_vendor.f(chatMessages.value, (item, index, i0) => {
          return common_vendor.e({
            a: item.userAvatar || "/static/images/default-avatar.png",
            b: item.unreadCount > 0
          }, item.unreadCount > 0 ? {
            c: common_vendor.t(item.unreadCount > 99 ? "99+" : item.unreadCount)
          } : {}, {
            d: common_vendor.t(item.userName),
            e: common_vendor.t(item.lastMessage),
            f: common_vendor.t(item.time),
            g: index,
            h: common_vendor.o(($event) => navigateToChat(item), index)
          });
        })
      } : {
        y: common_assets._imports_1$3
      }, {
        z: chatMessages.value.length > 0 && !hasMore.value[2]
      }, chatMessages.value.length > 0 && !hasMore.value[2] ? {} : {}, {
        A: common_vendor.o(($event) => loadMore(2)),
        B: refreshing.value[2],
        C: common_vendor.o(($event) => onRefresh(2)),
        D: currentTab.value,
        E: common_vendor.o(onSwiperChange),
        F: navbarHeight.value + tabsHeight.value + "px"
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/user/pages/messages.js.map
