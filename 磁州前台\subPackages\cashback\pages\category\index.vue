<template>
  <view class="category-container">
    <!-- 自定义导航栏 -->
    <custom-navbar title="商品分类" :show-back="true"></custom-navbar>
    
    <!-- 内容区域 -->
    <view class="content-container">
      <!-- 分类导航 -->
      <scroll-view class="category-sidebar" scroll-y>
        <view 
          class="category-item" 
          v-for="(category, index) in categories" 
          :key="index"
          :class="{'category-item--active': activeIndex === index}"
          @tap="switchCategory(index)"
        >
          <text>{{ category.name }}</text>
        </view>
      </scroll-view>
      
      <!-- 分类内容 -->
      <scroll-view class="category-content" scroll-y>
        <view class="subcategory-grid">
          <view class="subcategory-section" v-for="(subcategory, subIndex) in currentSubcategories" :key="subIndex">
            <view class="subcategory-title">
              <text>{{ subcategory.name }}</text>
            </view>
            <view class="subcategory-items">
              <view 
                class="subcategory-item" 
                v-for="(item, itemIndex) in subcategory.items" 
                :key="itemIndex"
                @tap="navigateToProductList(item)"
              >
                <image class="subcategory-icon" :src="item.icon" mode="aspectFill"></image>
                <text class="subcategory-name">{{ item.name }}</text>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import CustomNavbar from '../../components/CustomNavbar.vue';

export default {
  components: {
    CustomNavbar
  },
  data() {
    return {
      activeIndex: 0,
      categories: [
        {
          name: '女装',
          subcategories: [
            {
              name: '当季热卖',
              items: [
                { id: 1, name: '连衣裙', icon: '/static/images/cashback/category-dress.png' },
                { id: 2, name: 'T恤', icon: '/static/images/cashback/category-tshirt.png' },
                { id: 3, name: '衬衫', icon: '/static/images/cashback/category-shirt.png' },
                { id: 4, name: '裤子', icon: '/static/images/cashback/category-pants.png' },
                { id: 5, name: '外套', icon: '/static/images/cashback/category-coat.png' },
                { id: 6, name: '半身裙', icon: '/static/images/cashback/category-skirt.png' }
              ]
            },
            {
              name: '流行款式',
              items: [
                { id: 7, name: '牛仔裤', icon: '/static/images/cashback/category-jeans.png' },
                { id: 8, name: '休闲裤', icon: '/static/images/cashback/category-casual.png' },
                { id: 9, name: '卫衣', icon: '/static/images/cashback/category-hoodie.png' },
                { id: 10, name: '针织衫', icon: '/static/images/cashback/category-knitwear.png' }
              ]
            }
          ]
        },
        {
          name: '男装',
          subcategories: [
            {
              name: '当季热卖',
              items: [
                { id: 11, name: 'T恤', icon: '/static/images/cashback/category-mtshirt.png' },
                { id: 12, name: '衬衫', icon: '/static/images/cashback/category-mshirt.png' },
                { id: 13, name: '裤子', icon: '/static/images/cashback/category-mpants.png' },
                { id: 14, name: '外套', icon: '/static/images/cashback/category-mcoat.png' }
              ]
            },
            {
              name: '流行款式',
              items: [
                { id: 15, name: '牛仔裤', icon: '/static/images/cashback/category-mjeans.png' },
                { id: 16, name: '休闲裤', icon: '/static/images/cashback/category-mcasual.png' },
                { id: 17, name: '卫衣', icon: '/static/images/cashback/category-mhoodie.png' },
                { id: 18, name: '西装', icon: '/static/images/cashback/category-suit.png' }
              ]
            }
          ]
        },
        {
          name: '美妆',
          subcategories: [
            {
              name: '热门品类',
              items: [
                { id: 19, name: '面膜', icon: '/static/images/cashback/category-mask.png' },
                { id: 20, name: '口红', icon: '/static/images/cashback/category-lipstick.png' },
                { id: 21, name: '精华', icon: '/static/images/cashback/category-essence.png' },
                { id: 22, name: '眼影', icon: '/static/images/cashback/category-eyeshadow.png' },
                { id: 23, name: '粉底', icon: '/static/images/cashback/category-foundation.png' },
                { id: 24, name: '洁面', icon: '/static/images/cashback/category-cleanser.png' }
              ]
            }
          ]
        },
        {
          name: '数码',
          subcategories: [
            {
              name: '热门设备',
              items: [
                { id: 25, name: '手机', icon: '/static/images/cashback/category-phone.png' },
                { id: 26, name: '笔记本', icon: '/static/images/cashback/category-laptop.png' },
                { id: 27, name: '平板', icon: '/static/images/cashback/category-tablet.png' },
                { id: 28, name: '耳机', icon: '/static/images/cashback/category-headphone.png' },
                { id: 29, name: '相机', icon: '/static/images/cashback/category-camera.png' },
                { id: 30, name: '手表', icon: '/static/images/cashback/category-watch.png' }
              ]
            }
          ]
        },
        {
          name: '家电',
          subcategories: [
            {
              name: '大家电',
              items: [
                { id: 31, name: '电视', icon: '/static/images/cashback/category-tv.png' },
                { id: 32, name: '冰箱', icon: '/static/images/cashback/category-fridge.png' },
                { id: 33, name: '洗衣机', icon: '/static/images/cashback/category-washer.png' },
                { id: 34, name: '空调', icon: '/static/images/cashback/category-ac.png' }
              ]
            },
            {
              name: '小家电',
              items: [
                { id: 35, name: '吸尘器', icon: '/static/images/cashback/category-vacuum.png' },
                { id: 36, name: '电饭煲', icon: '/static/images/cashback/category-cooker.png' },
                { id: 37, name: '加湿器', icon: '/static/images/cashback/category-humidifier.png' },
                { id: 38, name: '电热水壶', icon: '/static/images/cashback/category-kettle.png' }
              ]
            }
          ]
        },
        {
          name: '食品',
          subcategories: [
            {
              name: '休闲零食',
              items: [
                { id: 39, name: '坚果', icon: '/static/images/cashback/category-nuts.png' },
                { id: 40, name: '饼干', icon: '/static/images/cashback/category-biscuit.png' },
                { id: 41, name: '糖果', icon: '/static/images/cashback/category-candy.png' },
                { id: 42, name: '巧克力', icon: '/static/images/cashback/category-chocolate.png' }
              ]
            },
            {
              name: '粮油调味',
              items: [
                { id: 43, name: '大米', icon: '/static/images/cashback/category-rice.png' },
                { id: 44, name: '食用油', icon: '/static/images/cashback/category-oil.png' },
                { id: 45, name: '调味料', icon: '/static/images/cashback/category-spice.png' },
                { id: 46, name: '面条', icon: '/static/images/cashback/category-noodle.png' }
              ]
            }
          ]
        }
      ]
    };
  },
  computed: {
    currentSubcategories() {
      return this.categories[this.activeIndex].subcategories || [];
    }
  },
  onLoad() {
    // 设置页面不显示系统导航栏
    uni.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#9C27B0'
    });
  },
  methods: {
    switchCategory(index) {
      this.activeIndex = index;
    },
    navigateToProductList(item) {
      uni.navigateTo({
        url: `/subPackages/cashback/pages/product-list/index?id=${item.id}&name=${item.name}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.category-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content-container {
  display: flex;
  height: 100vh;
  padding-top: calc(var(--status-bar-height) + 44px);
}

.category-sidebar {
  width: 80px;
  height: 100%;
  background-color: #f5f5f5;
}

.category-item {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  
  text {
    font-size: 14px;
    color: #666666;
  }
  
  &--active {
    background-color: #FFFFFF;
    
    text {
      color: #9C27B0;
      font-weight: 500;
    }
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background-color: #9C27B0;
      border-radius: 0 2px 2px 0;
    }
  }
}

.category-content {
  flex: 1;
  height: 100%;
  background-color: #FFFFFF;
}

.subcategory-grid {
  padding: 16px;
}

.subcategory-section {
  margin-bottom: 24px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.subcategory-title {
  margin-bottom: 16px;
  
  text {
    font-size: 16px;
    font-weight: 500;
    color: #333333;
  }
}

.subcategory-items {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.subcategory-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .subcategory-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    margin-bottom: 8px;
  }
  
  .subcategory-name {
    font-size: 12px;
    color: #666666;
  }
}
</style> 