"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const keyword = common_vendor.ref("");
    const hasSearched = common_vendor.ref(false);
    const isLoading = common_vendor.ref(false);
    const hasMore = common_vendor.ref(true);
    const pageNum = common_vendor.ref(1);
    common_vendor.ref(10);
    const searchHistory = common_vendor.ref([]);
    const hotSearches = common_vendor.ref([
      "新年音乐节",
      "美食品鉴会",
      "亲子活动",
      "户外露营",
      "瑜伽课程",
      "电影首映",
      "艺术展览",
      "读书会"
    ]);
    const filterTabs = [
      { name: "活动", type: "activity" },
      { name: "商品", type: "product" },
      { name: "文章", type: "article" }
    ];
    const currentTab = common_vendor.ref(0);
    const activityResults = common_vendor.ref([
      {
        id: "1",
        title: "2024新年音乐节",
        description: "迎接新年的音乐盛宴，多位知名歌手现场献唱",
        image: "/static/images/activity1.jpg",
        time: "2024-01-01 19:30",
        price: 199
      },
      {
        id: "2",
        title: "美食品鉴会",
        description: "汇聚全球美食，享受舌尖上的盛宴",
        image: "/static/images/activity2.jpg",
        time: "2024-01-15 14:00",
        price: 299
      }
    ]);
    const productResults = common_vendor.ref([
      {
        id: "1",
        title: "限量版纪念T恤",
        description: "2024新年音乐节限定款",
        image: "/static/images/product1.jpg",
        price: 129,
        originalPrice: 199
      },
      {
        id: "2",
        title: "美食礼盒",
        description: "精选多国特色美食",
        image: "/static/images/product2.jpg",
        price: 299,
        originalPrice: 399
      }
    ]);
    const articleResults = common_vendor.ref([
      {
        id: "1",
        title: "如何准备一场完美的音乐节",
        summary: "从选址到表演者邀请，一文了解音乐节筹备全过程",
        image: "/static/images/article1.jpg",
        publishTime: "2023-12-20",
        views: 1234
      },
      {
        id: "2",
        title: "2024年最值得期待的十大活动",
        summary: "新的一年，哪些活动最值得参与？本文为你一一盘点",
        image: "/static/images/article2.jpg",
        publishTime: "2023-12-25",
        views: 2345
      }
    ]);
    const getCurrentResults = common_vendor.computed(() => {
      switch (currentTab.value) {
        case 0:
          return activityResults.value;
        case 1:
          return productResults.value;
        case 2:
          return articleResults.value;
        default:
          return [];
      }
    });
    const switchTab = (index) => {
      if (currentTab.value === index)
        return;
      currentTab.value = index;
      pageNum.value = 1;
      hasMore.value = true;
      if (getCurrentResults.value.length === 0) {
        search();
      }
    };
    const search = () => {
      isLoading.value = true;
      setTimeout(() => {
        isLoading.value = false;
        if (pageNum.value >= 3) {
          hasMore.value = false;
        }
      }, 1e3);
    };
    const handleSearch = () => {
      if (!keyword.value.trim())
        return;
      saveSearchHistory(keyword.value);
      hasSearched.value = true;
      pageNum.value = 1;
      hasMore.value = true;
      if (currentTab.value === 0) {
        activityResults.value = [];
      } else if (currentTab.value === 1) {
        productResults.value = [];
      } else if (currentTab.value === 2) {
        articleResults.value = [];
      }
      search();
    };
    const loadMore = () => {
      if (isLoading.value || !hasMore.value)
        return;
      pageNum.value++;
      search();
    };
    const clearKeyword = () => {
      keyword.value = "";
      hasSearched.value = false;
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const saveSearchHistory = (key) => {
      const index = searchHistory.value.indexOf(key);
      if (index > -1) {
        searchHistory.value.splice(index, 1);
      }
      searchHistory.value.unshift(key);
      if (searchHistory.value.length > 10) {
        searchHistory.value.pop();
      }
      common_vendor.index.setStorageSync("searchHistory", JSON.stringify(searchHistory.value));
    };
    const useHistoryItem = (item) => {
      keyword.value = item;
      handleSearch();
    };
    const clearHistory = () => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要清空搜索历史吗？",
        success: (res) => {
          if (res.confirm) {
            searchHistory.value = [];
            common_vendor.index.removeStorageSync("searchHistory");
          }
        }
      });
    };
    const goToDetail = (id, type) => {
      let url = "";
      switch (type) {
        case "activity":
          url = `/pages/activity/detail?id=${id}`;
          break;
        case "product":
          url = `/pages/product/detail?id=${id}`;
          break;
        case "article":
          url = `/pages/article/detail?id=${id}`;
          break;
      }
      if (url) {
        common_vendor.index.navigateTo({ url });
      }
    };
    const getSearchHistory = () => {
      try {
        const history = common_vendor.index.getStorageSync("searchHistory");
        if (history) {
          searchHistory.value = JSON.parse(history);
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at subPackages/activity-showcase/pages/search/index.vue:442", "获取搜索历史失败", e);
      }
    };
    common_vendor.watch(keyword, (newVal) => {
      if (!newVal) {
        hasSearched.value = false;
      }
    });
    common_vendor.onMounted(() => {
      getSearchHistory();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          d: "M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6c3.2 3.2 8.4 3.2 11.6 0l43.6-43.5c3.2-3.2 3.2-8.4 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z",
          fill: "#999"
        }),
        b: common_vendor.p({
          viewBox: "0 0 1024 1024",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        c: common_vendor.o(handleSearch),
        d: keyword.value,
        e: common_vendor.o(($event) => keyword.value = $event.detail.value),
        f: keyword.value
      }, keyword.value ? {
        g: common_vendor.p({
          d: "M512 421.490332L871.696581 61.793751l90.509668 90.509668L602.509668 512l359.696581 359.696581-90.509668 90.509668L512 602.509668 152.303419 962.206249l-90.509668-90.509668L421.490332 512 61.793751 152.303419l90.509668-90.509668z",
          fill: "#999"
        }),
        h: common_vendor.p({
          viewBox: "0 0 1024 1024",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        i: common_vendor.o(clearKeyword)
      } : {}, {
        j: common_vendor.o(goBack),
        k: !keyword.value && searchHistory.value.length > 0 && !hasSearched.value
      }, !keyword.value && searchHistory.value.length > 0 && !hasSearched.value ? {
        l: common_vendor.p({
          d: "M899.1 869.6l-53-305.6H864c14.4 0 26-11.6 26-26V346c0-14.4-11.6-26-26-26H618V138c0-14.4-11.6-26-26-26H432c-14.4 0-26 11.6-26 26v182H160c-14.4 0-26 11.6-26 26v192c0 14.4 11.6 26 26 26h17.9l-53 305.6c-0.3 1.5-0.4 3-0.4 4.4 0 14.4 11.6 26 26 26h723c1.5 0 3-0.1 4.4-0.4 14.2-2.4 23.7-15.9 21.2-30zM204 390h272V182h72v208h272v104H204V390z m468 440V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H416V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H202.8l45.1-260H776l45.1 260H672z",
          fill: "#999"
        }),
        m: common_vendor.p({
          viewBox: "0 0 1024 1024",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        n: common_vendor.o(clearHistory),
        o: common_vendor.f(searchHistory.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item),
            b: index,
            c: common_vendor.o(($event) => useHistoryItem(item), index)
          };
        })
      } : {}, {
        p: !keyword.value && !hasSearched.value
      }, !keyword.value && !hasSearched.value ? {
        q: common_vendor.f(hotSearches.value, (item, index, i0) => {
          return common_vendor.e({
            a: index < 3
          }, index < 3 ? {
            b: common_vendor.t(index + 1)
          } : {}, {
            c: common_vendor.t(item),
            d: index,
            e: index < 3 ? 1 : "",
            f: common_vendor.o(($event) => useHistoryItem(item), index)
          });
        })
      } : {}, {
        r: hasSearched.value
      }, hasSearched.value ? common_vendor.e({
        s: common_vendor.f(filterTabs, (tab, index, i0) => {
          return {
            a: common_vendor.t(tab.name),
            b: index,
            c: currentTab.value === index ? 1 : "",
            d: common_vendor.o(($event) => switchTab(index), index)
          };
        }),
        t: currentTab.value === 0
      }, currentTab.value === 0 ? {
        v: common_vendor.f(activityResults.value, (item, index, i0) => {
          return {
            a: item.image,
            b: common_vendor.t(item.title),
            c: common_vendor.t(item.description),
            d: common_vendor.t(item.time),
            e: common_vendor.t(item.price),
            f: index,
            g: common_vendor.o(($event) => goToDetail(item.id, "activity"), index)
          };
        })
      } : {}, {
        w: currentTab.value === 1
      }, currentTab.value === 1 ? {
        x: common_vendor.f(productResults.value, (item, index, i0) => {
          return {
            a: item.image,
            b: common_vendor.t(item.title),
            c: common_vendor.t(item.description),
            d: common_vendor.t(item.price),
            e: common_vendor.t(item.originalPrice),
            f: index,
            g: common_vendor.o(($event) => goToDetail(item.id, "product"), index)
          };
        })
      } : {}, {
        y: currentTab.value === 2
      }, currentTab.value === 2 ? {
        z: common_vendor.f(articleResults.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.title),
            b: common_vendor.t(item.summary),
            c: common_vendor.t(item.publishTime),
            d: common_vendor.t(item.views),
            e: item.image
          }, item.image ? {
            f: item.image
          } : {}, {
            g: index,
            h: common_vendor.o(($event) => goToDetail(item.id, "article"), index)
          });
        })
      } : {}, {
        A: isLoading.value
      }, isLoading.value ? {} : {}, {
        B: hasMore.value && !isLoading.value
      }, hasMore.value && !isLoading.value ? {
        C: common_vendor.o(loadMore)
      } : {}, {
        D: !hasMore.value && getCurrentResults.value.length > 0
      }, !hasMore.value && getCurrentResults.value.length > 0 ? {} : {}, {
        E: hasSearched.value && !isLoading.value && getCurrentResults.value.length === 0
      }, hasSearched.value && !isLoading.value && getCurrentResults.value.length === 0 ? {
        F: common_assets._imports_0$58
      } : {}) : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d4b650e0"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/search/index.js.map
