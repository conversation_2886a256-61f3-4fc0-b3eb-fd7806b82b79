{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/distribution/poster/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcZGlzdHJpYnV0aW9uXHBvc3RlclxpbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"poster-container\">\r\n    <!-- 海报预览区域 -->\r\n    <view class=\"poster-preview\" :style=\"{\r\n      borderRadius: '35px',\r\n      boxShadow: '0 8px 20px rgba(0,0,0,0.08)',\r\n      background: '#FFFFFF',\r\n      padding: '30rpx',\r\n      marginBottom: '30rpx',\r\n      display: 'flex',\r\n      flexDirection: 'column',\r\n      alignItems: 'center'\r\n    }\">\r\n      <view class=\"preview-header\" :style=\"{\r\n        width: '100%',\r\n        display: 'flex',\r\n        justifyContent: 'space-between',\r\n        alignItems: 'center',\r\n        marginBottom: '30rpx'\r\n      }\">\r\n        <text class=\"preview-title\" :style=\"{\r\n          fontSize: '32rpx',\r\n          fontWeight: '600',\r\n          color: '#333333'\r\n        }\">海报预览</text>\r\n        \r\n        <view class=\"poster-switch\" :style=\"{\r\n          display: 'flex',\r\n          alignItems: 'center'\r\n        }\">\r\n          <text :style=\"{\r\n            fontSize: '26rpx',\r\n            color: '#666666',\r\n            marginRight: '10rpx'\r\n          }\">样式:</text>\r\n          \r\n          <view class=\"switch-buttons\" :style=\"{\r\n            display: 'flex',\r\n            borderRadius: '20rpx',\r\n            overflow: 'hidden',\r\n            border: '1rpx solid #EFEFEF'\r\n          }\">\r\n            <view \r\n              v-for=\"(style, index) in posterStyles\" \r\n              :key=\"index\"\r\n              class=\"switch-item\"\r\n              :class=\"{ active: currentStyle === index }\"\r\n              @click=\"switchStyle(index)\"\r\n              :style=\"{\r\n                padding: '8rpx 20rpx',\r\n                fontSize: '24rpx',\r\n                color: currentStyle === index ? '#FFFFFF' : '#666666',\r\n                background: currentStyle === index ? '#AC39FF' : '#F8F8F8',\r\n                borderRight: index < posterStyles.length - 1 ? '1rpx solid #EFEFEF' : 'none'\r\n              }\"\r\n            >\r\n              {{ style }}\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 海报图片 -->\r\n      <view class=\"poster-image-wrapper\" :style=\"{\r\n        width: '600rpx',\r\n        height: '900rpx',\r\n        position: 'relative',\r\n        borderRadius: '20rpx',\r\n        overflow: 'hidden',\r\n        boxShadow: '0 10px 30px rgba(0,0,0,0.1)'\r\n      }\">\r\n        <image \r\n          :src=\"currentPosterUrl\" \r\n          mode=\"aspectFill\" \r\n          :style=\"{\r\n            width: '100%',\r\n            height: '100%'\r\n          }\"\r\n        ></image>\r\n        \r\n        <!-- 二维码 -->\r\n        <view class=\"qrcode-wrapper\" :style=\"{\r\n          position: 'absolute',\r\n          right: '30rpx',\r\n          bottom: '30rpx',\r\n          width: '180rpx',\r\n          height: '180rpx',\r\n          background: '#FFFFFF',\r\n          borderRadius: '10rpx',\r\n          padding: '10rpx',\r\n          boxShadow: '0 5px 15px rgba(0,0,0,0.1)'\r\n        }\">\r\n          <image \r\n            src=\"/static/images/distribution/qrcode.png\" \r\n            mode=\"aspectFit\" \r\n            :style=\"{\r\n              width: '100%',\r\n              height: '100%'\r\n            }\"\r\n          ></image>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 操作按钮 -->\r\n      <view class=\"poster-actions\" :style=\"{\r\n        display: 'flex',\r\n        justifyContent: 'center',\r\n        marginTop: '30rpx',\r\n        width: '100%'\r\n      }\">\r\n        <view class=\"action-btn preview-btn\" @click=\"previewPoster\" :style=\"{\r\n          flex: '1',\r\n          margin: '0 10rpx',\r\n          height: '80rpx',\r\n          borderRadius: '40rpx',\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          background: 'rgba(172,57,255,0.1)',\r\n          border: '1rpx solid #AC39FF',\r\n          color: '#AC39FF',\r\n          fontSize: '28rpx',\r\n          fontWeight: '500'\r\n        }\">\r\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\" :style=\"{ marginRight: '10rpx' }\">\r\n            <path d=\"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\" stroke=\"#AC39FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"#AC39FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\r\n          </svg>\r\n          预览\r\n        </view>\r\n        \r\n        <view class=\"action-btn save-btn\" @click=\"savePoster\" :style=\"{\r\n          flex: '1',\r\n          margin: '0 10rpx',\r\n          height: '80rpx',\r\n          borderRadius: '40rpx',\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)',\r\n          color: '#FFFFFF',\r\n          fontSize: '28rpx',\r\n          fontWeight: '500',\r\n          boxShadow: '0 5px 15px rgba(172,57,255,0.3)'\r\n        }\">\r\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\" :style=\"{ marginRight: '10rpx' }\">\r\n            <path d=\"M19 21H5a2 2 0 01-2-2V5a2 2 0 012-2h11l5 5v11a2 2 0 01-2 2z\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            <polyline points=\"17 21 17 13 7 13 7 21\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></polyline>\r\n            <polyline points=\"7 3 7 8 15 8\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></polyline>\r\n          </svg>\r\n          保存到相册\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 商品选择区域 -->\r\n    <view class=\"product-selection\" :style=\"{\r\n      borderRadius: '35px',\r\n      boxShadow: '0 8px 20px rgba(0,0,0,0.08)',\r\n      background: '#FFFFFF',\r\n      padding: '30rpx',\r\n      marginBottom: '30rpx'\r\n    }\">\r\n      <view class=\"selection-header\" :style=\"{\r\n        display: 'flex',\r\n        justifyContent: 'space-between',\r\n        alignItems: 'center',\r\n        marginBottom: '20rpx'\r\n      }\">\r\n        <text class=\"selection-title\" :style=\"{\r\n          fontSize: '32rpx',\r\n          fontWeight: '600',\r\n          color: '#333333'\r\n        }\">选择推广商品</text>\r\n        \r\n        <view class=\"view-all\" @click=\"navigateTo('/subPackages/activity-showcase/pages/distribution/products/index')\" :style=\"{\r\n          fontSize: '26rpx',\r\n          color: '#AC39FF',\r\n          display: 'flex',\r\n          alignItems: 'center'\r\n        }\">\r\n          <text>查看全部</text>\r\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n            <path d=\"M9 18l6-6-6-6\" stroke=\"#AC39FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n          </svg>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 商品列表 -->\r\n      <view class=\"product-list\" :style=\"{\r\n        display: 'flex',\r\n        flexDirection: 'column'\r\n      }\">\r\n        <view \r\n          v-for=\"(product, index) in products\" \r\n          :key=\"index\"\r\n          class=\"product-item\"\r\n          :class=\"{ active: currentProduct === index }\"\r\n          @click=\"selectProduct(index)\"\r\n          :style=\"{\r\n            padding: '20rpx',\r\n            borderRadius: '20rpx',\r\n            marginBottom: '20rpx',\r\n            display: 'flex',\r\n            background: currentProduct === index ? 'rgba(172,57,255,0.1)' : '#F8F8F8',\r\n            border: currentProduct === index ? '1rpx solid #AC39FF' : '1rpx solid transparent'\r\n          }\"\r\n        >\r\n          <image \r\n            :src=\"product.image\" \r\n            mode=\"aspectFill\" \r\n            :style=\"{\r\n              width: '120rpx',\r\n              height: '120rpx',\r\n              borderRadius: '10rpx',\r\n              marginRight: '20rpx'\r\n            }\"\r\n          ></image>\r\n          \r\n          <view class=\"product-info\" :style=\"{ flex: '1' }\">\r\n            <text class=\"product-name\" :style=\"{\r\n              fontSize: '28rpx',\r\n              fontWeight: '500',\r\n              color: '#333333',\r\n              marginBottom: '10rpx',\r\n              display: 'block',\r\n              overflow: 'hidden',\r\n              textOverflow: 'ellipsis',\r\n              display: '-webkit-box',\r\n              '-webkit-line-clamp': '2',\r\n              '-webkit-box-orient': 'vertical'\r\n            }\">{{ product.name }}</text>\r\n            \r\n            <view class=\"product-price\" :style=\"{\r\n              display: 'flex',\r\n              alignItems: 'baseline',\r\n              marginBottom: '10rpx'\r\n            }\">\r\n              <text :style=\"{\r\n                fontSize: '24rpx',\r\n                color: '#FF3B69',\r\n                marginRight: '5rpx'\r\n              }\">¥</text>\r\n              <text :style=\"{\r\n                fontSize: '32rpx',\r\n                fontWeight: '600',\r\n                color: '#FF3B69',\r\n                marginRight: '10rpx'\r\n              }\">{{ product.price }}</text>\r\n              <text :style=\"{\r\n                fontSize: '24rpx',\r\n                color: '#999999',\r\n                textDecoration: 'line-through'\r\n              }\">¥{{ product.originalPrice }}</text>\r\n            </view>\r\n            \r\n            <view class=\"product-commission\" :style=\"{\r\n              display: 'flex',\r\n              alignItems: 'center'\r\n            }\">\r\n              <text :style=\"{\r\n                fontSize: '24rpx',\r\n                color: '#AC39FF',\r\n                marginRight: '5rpx'\r\n              }\">佣金:</text>\r\n              <text :style=\"{\r\n                fontSize: '24rpx',\r\n                fontWeight: '600',\r\n                color: '#AC39FF'\r\n              }\">¥{{ product.commission }}</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"product-check\" :style=\"{\r\n            width: '40rpx',\r\n            height: '40rpx',\r\n            borderRadius: '50%',\r\n            border: currentProduct === index ? '0' : '1rpx solid #CCCCCC',\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            justifyContent: 'center',\r\n            background: currentProduct === index ? '#AC39FF' : 'transparent'\r\n          }\">\r\n            <svg v-if=\"currentProduct === index\" class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n              <path d=\"M5 12l5 5L20 7\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            </svg>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 推广文案 -->\r\n    <view class=\"promotion-text\" :style=\"{\r\n      borderRadius: '35px',\r\n      boxShadow: '0 8px 20px rgba(0,0,0,0.08)',\r\n      background: '#FFFFFF',\r\n      padding: '30rpx',\r\n      marginBottom: '30rpx'\r\n    }\">\r\n      <view class=\"text-header\" :style=\"{\r\n        marginBottom: '20rpx'\r\n      }\">\r\n        <text class=\"text-title\" :style=\"{\r\n          fontSize: '32rpx',\r\n          fontWeight: '600',\r\n          color: '#333333'\r\n        }\">推广文案</text>\r\n      </view>\r\n      \r\n      <!-- 文案列表 -->\r\n      <view class=\"text-list\">\r\n        <view \r\n          v-for=\"(text, index) in promotionTexts\" \r\n          :key=\"index\"\r\n          class=\"text-item\"\r\n          :style=\"{\r\n            padding: '20rpx',\r\n            borderRadius: '20rpx',\r\n            marginBottom: '20rpx',\r\n            background: '#F8F8F8',\r\n            position: 'relative'\r\n          }\"\r\n        >\r\n          <text :style=\"{\r\n            fontSize: '26rpx',\r\n            color: '#333333',\r\n            lineHeight: '1.6'\r\n          }\">{{ text.content }}</text>\r\n          \r\n          <view class=\"copy-btn\" @click=\"copyText(text.content)\" :style=\"{\r\n            position: 'absolute',\r\n            right: '20rpx',\r\n            bottom: '20rpx',\r\n            padding: '10rpx 20rpx',\r\n            borderRadius: '30rpx',\r\n            background: 'rgba(172,57,255,0.1)',\r\n            color: '#AC39FF',\r\n            fontSize: '24rpx',\r\n            fontWeight: '500'\r\n          }\">\r\n            复制\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 分享按钮 -->\r\n    <button \r\n      open-type=\"share\" \r\n      class=\"share-btn\" \r\n      :style=\"{\r\n        width: '100%',\r\n        height: '90rpx',\r\n        borderRadius: '45rpx',\r\n        background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',\r\n        color: '#FFFFFF',\r\n        fontSize: '32rpx',\r\n        fontWeight: '500',\r\n        display: 'flex',\r\n        alignItems: 'center',\r\n        justifyContent: 'center',\r\n        boxShadow: '0 5px 15px rgba(255,59,105,0.3)',\r\n        marginBottom: '30rpx'\r\n      }\"\r\n    >\r\n      <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\" :style=\"{ marginRight: '10rpx' }\">\r\n        <circle cx=\"18\" cy=\"5\" r=\"3\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\r\n        <circle cx=\"6\" cy=\"12\" r=\"3\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\r\n        <circle cx=\"18\" cy=\"19\" r=\"3\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\r\n        <line x1=\"8.59\" y1=\"13.51\" x2=\"15.42\" y2=\"17.49\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\r\n        <line x1=\"15.41\" y1=\"6.51\" x2=\"8.59\" y2=\"10.49\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\r\n      </svg>\r\n      分享给好友\r\n    </button>\r\n    \r\n    <!-- 底部安全区域 -->\r\n    <view class=\"safe-area-bottom\" :style=\"{\r\n      height: '100rpx',\r\n      paddingBottom: 'env(safe-area-inset-bottom)'\r\n    }\"></view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed } from 'vue';\r\n\r\n// 海报样式\r\nconst posterStyles = ref(['样式一', '样式二', '样式三']);\r\nconst currentStyle = ref(0);\r\n\r\n// 当前商品\r\nconst currentProduct = ref(0);\r\n\r\n// 海报URL\r\nconst posterUrls = ref([\r\n  '/static/images/distribution/poster-1.png',\r\n  '/static/images/distribution/poster-2.png',\r\n  '/static/images/distribution/poster-3.png'\r\n]);\r\n\r\n// 当前海报URL\r\nconst currentPosterUrl = computed(() => {\r\n  return posterUrls.value[currentStyle.value];\r\n});\r\n\r\n// 商品列表\r\nconst products = ref([\r\n  {\r\n    id: 1,\r\n    name: 'Apple iPhone 14 Pro Max 256GB 暗夜紫 移动联通电信5G双卡双待手机',\r\n    image: 'https://via.placeholder.com/120',\r\n    price: '8999.00',\r\n    originalPrice: '9999.00',\r\n    commission: '300.00'\r\n  },\r\n  {\r\n    id: 2,\r\n    name: '小米12S Ultra 12GB+256GB 丹青黑 骁龙8+旗舰处理器 徕卡专业光学镜头',\r\n    image: 'https://via.placeholder.com/120',\r\n    price: '5999.00',\r\n    originalPrice: '6999.00',\r\n    commission: '200.00'\r\n  },\r\n  {\r\n    id: 3,\r\n    name: '华为Mate 50 Pro 8GB+256GB 曜金黑 超光变XMAGE影像 北斗卫星消息',\r\n    image: 'https://via.placeholder.com/120',\r\n    price: '6799.00',\r\n    originalPrice: '7299.00',\r\n    commission: '250.00'\r\n  }\r\n]);\r\n\r\n// 推广文案\r\nconst promotionTexts = ref([\r\n  {\r\n    id: 1,\r\n    content: '🔥【限时特惠】iPhone 14 Pro Max 立省1000元！全网最低价，手慢无！点击链接立即抢购：https://t.cn/A6JxYZ7p'\r\n  },\r\n  {\r\n    id: 2,\r\n    content: '✨ 想要高性价比的旗舰手机？小米12S Ultra 现在只要5999元！徕卡专业光学镜头，拍照效果堪比单反！戳我购买：https://t.cn/A6JxYZ7p'\r\n  },\r\n  {\r\n    id: 3,\r\n    content: '💯 华为Mate 50 Pro 新品首发特惠！支持北斗卫星消息，紧急情况也能发短信！现在下单还送豪华配件大礼包！速戳：https://t.cn/A6JxYZ7p'\r\n  }\r\n]);\r\n\r\n// 切换海报样式\r\nfunction switchStyle(index) {\r\n  currentStyle.value = index;\r\n}\r\n\r\n// 选择商品\r\nfunction selectProduct(index) {\r\n  currentProduct.value = index;\r\n}\r\n\r\n// 预览海报\r\nfunction previewPoster() {\r\n  uni.previewImage({\r\n    urls: [currentPosterUrl.value],\r\n    current: currentPosterUrl.value\r\n  });\r\n}\r\n\r\n// 保存海报到相册\r\nfunction savePoster() {\r\n  uni.showLoading({\r\n    title: '保存中...'\r\n  });\r\n  \r\n  // 模拟保存过程\r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    \r\n    uni.showToast({\r\n      title: '保存成功',\r\n      icon: 'success'\r\n    });\r\n  }, 1500);\r\n}\r\n\r\n// 复制文案\r\nfunction copyText(text) {\r\n  uni.setClipboardData({\r\n    data: text,\r\n    success: () => {\r\n      uni.showToast({\r\n        title: '复制成功',\r\n        icon: 'success'\r\n      });\r\n    }\r\n  });\r\n}\r\n\r\n// 页面导航\r\nfunction navigateTo(url) {\r\n  uni.navigateTo({ url });\r\n}\r\n\r\n// 分享配置\r\ndefineExpose({\r\n  onShareAppMessage() {\r\n    return {\r\n      title: products.value[currentProduct.value].name,\r\n      path: `/subPackages/activity-showcase/pages/detail/index?id=${products.value[currentProduct.value].id}&source=distribution`,\r\n      imageUrl: currentPosterUrl.value\r\n    };\r\n  }\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.poster-container {\r\n  padding: 30rpx;\r\n  background-color: #F2F2F7;\r\n  min-height: 100vh;\r\n}\r\n\r\n.action-btn:active, .copy-btn:active, .share-btn:active {\r\n  opacity: 0.9;\r\n  transform: scale(0.98);\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/distribution/poster/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni"], "mappings": ";;;;;;;;;;;;;;AAmYA,UAAA,eAAAA,cAAA,IAAA,CAAA,OAAA,OAAA,KAAA,CAAA;AACA,UAAA,eAAAA,cAAAA,IAAA,CAAA;AAGA,UAAA,iBAAAA,cAAAA,IAAA,CAAA;AAGA,UAAA,aAAAA,cAAAA,IAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACA,CAAA;AAGA,UAAA,mBAAAC,cAAA,SAAA,MAAA;AACA,aAAA,WAAA,MAAA,aAAA,KAAA;AAAA,IACA,CAAA;AAGA,UAAA,WAAAD,cAAAA,IAAA;AAAA,MACA;AAAA,QACA,IAAA;AAAA,QACA,MAAA;AAAA,QACA,OAAA;AAAA,QACA,OAAA;AAAA,QACA,eAAA;AAAA,QACA,YAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,IAAA;AAAA,QACA,MAAA;AAAA,QACA,OAAA;AAAA,QACA,OAAA;AAAA,QACA,eAAA;AAAA,QACA,YAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,IAAA;AAAA,QACA,MAAA;AAAA,QACA,OAAA;AAAA,QACA,OAAA;AAAA,QACA,eAAA;AAAA,QACA,YAAA;AAAA,MACA;AAAA,IACA,CAAA;AAGA,UAAA,iBAAAA,cAAAA,IAAA;AAAA,MACA;AAAA,QACA,IAAA;AAAA,QACA,SAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,IAAA;AAAA,QACA,SAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,IAAA;AAAA,QACA,SAAA;AAAA,MACA;AAAA,IACA,CAAA;AAGA,aAAA,YAAA,OAAA;AACA,mBAAA,QAAA;AAAA,IACA;AAGA,aAAA,cAAA,OAAA;AACA,qBAAA,QAAA;AAAA,IACA;AAGA,aAAA,gBAAA;AACAE,oBAAAA,MAAA,aAAA;AAAA,QACA,MAAA,CAAA,iBAAA,KAAA;AAAA,QACA,SAAA,iBAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,aAAA,aAAA;AACAA,oBAAAA,MAAA,YAAA;AAAA,QACA,OAAA;AAAA,MACA,CAAA;AAGA,iBAAA,MAAA;AACAA,sBAAA,MAAA,YAAA;AAEAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AAAA,MACA,GAAA,IAAA;AAAA,IACA;AAGA,aAAA,SAAA,MAAA;AACAA,oBAAAA,MAAA,iBAAA;AAAA,QACA,MAAA;AAAA,QACA,SAAA,MAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,aAAA,WAAA,KAAA;AACAA,oBAAAA,MAAA,WAAA,EAAA,IAAA,CAAA;AAAA,IACA;AAGA,aAAA;AAAA,MACA,oBAAA;AACA,eAAA;AAAA,UACA,OAAA,SAAA,MAAA,eAAA,KAAA,EAAA;AAAA,UACA,MAAA,wDAAA,SAAA,MAAA,eAAA,KAAA,EAAA,EAAA;AAAA,UACA,UAAA,iBAAA;AAAA,QACA;AAAA,MACA;AAAA,IACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9fA,GAAG,WAAW,eAAe;"}