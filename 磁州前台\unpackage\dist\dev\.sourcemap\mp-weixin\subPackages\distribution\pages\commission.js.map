{"version": 3, "file": "commission.js", "sources": ["subPackages/distribution/pages/commission.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcZGlzdHJpYnV0aW9uXHBhZ2VzXGNvbW1pc3Npb24udnVl"], "sourcesContent": ["<template>\r\n  <view class=\"commission-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">佣金明细</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 佣金统计卡片 -->\r\n    <view class=\"stats-card\">\r\n      <view class=\"stats-item main\">\r\n        <text class=\"item-value\">¥{{formatCommission(commissionSummary.total)}}</text>\r\n        <text class=\"item-label\">累计佣金</text>\r\n      </view>\r\n      \r\n      <view class=\"stats-row\">\r\n        <view class=\"stats-item\">\r\n          <text class=\"item-value\">¥{{formatCommission(commissionSummary.pending)}}</text>\r\n          <text class=\"item-label\">待结算</text>\r\n        </view>\r\n        \r\n        <view class=\"stats-item\">\r\n          <text class=\"item-value\">¥{{formatCommission(commissionSummary.settled)}}</text>\r\n          <text class=\"item-label\">可提现</text>\r\n        </view>\r\n        \r\n        <view class=\"stats-item\">\r\n          <text class=\"item-value\">¥{{formatCommission(commissionSummary.withdrawn)}}</text>\r\n          <text class=\"item-label\">已提现</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"stats-footer\">\r\n        <button class=\"withdraw-btn\" @click=\"navigateTo('/subPackages/distribution/pages/withdraw')\">立即提现</button>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 筛选栏 -->\r\n    <view class=\"filter-bar\">\r\n      <view \r\n        v-for=\"(tab, index) in tabs\" \r\n        :key=\"index\" \r\n        class=\"tab-item\" \r\n        :class=\"{ 'active': activeTab === tab.value }\"\r\n        @click=\"switchTab(tab.value)\"\r\n      >\r\n        <text>{{tab.name}}</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 佣金列表 -->\r\n    <view class=\"commission-list\" v-if=\"commissionRecords.length > 0\">\r\n      <view \r\n        v-for=\"(record, index) in commissionRecords\" \r\n        :key=\"index\" \r\n        class=\"commission-item\"\r\n      >\r\n        <view class=\"commission-info\">\r\n          <view class=\"commission-header\">\r\n            <text class=\"commission-title\">{{record.productName}}</text>\r\n            <text class=\"commission-amount\" :class=\"getStatusClass(record.status)\">{{getStatusPrefix(record.status)}}¥{{formatCommission(record.amount)}}</text>\r\n          </view>\r\n          \r\n          <view class=\"commission-detail\">\r\n            <text class=\"commission-order\">订单号: {{record.orderId}}</text>\r\n            <text class=\"commission-level\">{{getLevelText(record.level)}}佣金</text>\r\n          </view>\r\n          \r\n          <view class=\"commission-footer\">\r\n            <text class=\"commission-time\">{{formatTime(record.createdAt)}}</text>\r\n            <text class=\"commission-status\">{{getStatusText(record.status)}}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 空状态 -->\r\n    <view class=\"empty-state\" v-else>\r\n      <image class=\"empty-image\" src=\"/static/images/empty-data.png\" mode=\"aspectFit\"></image>\r\n      <text class=\"empty-text\">暂无佣金记录</text>\r\n    </view>\r\n    \r\n    <!-- 加载更多 -->\r\n    <view class=\"load-more\" v-if=\"hasMoreData && commissionRecords.length > 0\">\r\n      <text v-if=\"loading\">加载中...</text>\r\n      <text v-else @click=\"loadMore\">点击加载更多</text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted } from 'vue';\r\nimport distributionService from '@/utils/distributionService';\r\n\r\n// 选项卡\r\nconst tabs = [\r\n  { name: '全部', value: 'all' },\r\n  { name: '待结算', value: 'pending' },\r\n  { name: '可提现', value: 'settled' },\r\n  { name: '已提现', value: 'withdrawn' }\r\n];\r\n\r\n// 当前选中的选项卡\r\nconst activeTab = ref('all');\r\n\r\n// 佣金记录\r\nconst commissionRecords = ref([]);\r\n\r\n// 佣金统计\r\nconst commissionSummary = reactive({\r\n  total: 0,\r\n  pending: 0,\r\n  settled: 0,\r\n  withdrawn: 0\r\n});\r\n\r\n// 分页信息\r\nconst pagination = reactive({\r\n  page: 1,\r\n  pageSize: 10,\r\n  total: 0,\r\n  totalPages: 0\r\n});\r\n\r\n// 是否有更多数据\r\nconst hasMoreData = ref(false);\r\n\r\n// 是否正在加载\r\nconst loading = ref(false);\r\n\r\n// 页面加载\r\nonMounted(async () => {\r\n  // 获取佣金记录\r\n  await getCommissionRecords();\r\n});\r\n\r\n// 获取佣金记录\r\nconst getCommissionRecords = async (loadMore = false) => {\r\n  if (loading.value) return;\r\n  \r\n  try {\r\n    loading.value = true;\r\n    \r\n    const page = loadMore ? pagination.page + 1 : 1;\r\n    \r\n    const result = await distributionService.getCommissionRecords({\r\n      page,\r\n      pageSize: pagination.pageSize,\r\n      status: activeTab.value\r\n    });\r\n    \r\n    if (result) {\r\n      // 更新佣金记录\r\n      if (loadMore) {\r\n        commissionRecords.value = [...commissionRecords.value, ...result.list];\r\n      } else {\r\n        commissionRecords.value = result.list;\r\n      }\r\n      \r\n      // 更新分页信息\r\n      pagination.page = page;\r\n      pagination.total = result.pagination.total;\r\n      pagination.totalPages = result.pagination.totalPages;\r\n      \r\n      // 更新是否有更多数据\r\n      hasMoreData.value = pagination.page < pagination.totalPages;\r\n      \r\n      // 更新佣金统计\r\n      if (result.summary) {\r\n        Object.assign(commissionSummary, result.summary);\r\n      }\r\n    }\r\n  } catch (error) {\r\n    console.error('获取佣金记录失败', error);\r\n    uni.showToast({\r\n      title: '获取佣金记录失败',\r\n      icon: 'none'\r\n    });\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\n// 切换选项卡\r\nconst switchTab = (tab) => {\r\n  if (activeTab.value === tab) return;\r\n  \r\n  activeTab.value = tab;\r\n  getCommissionRecords();\r\n};\r\n\r\n// 加载更多\r\nconst loadMore = () => {\r\n  if (hasMoreData.value && !loading.value) {\r\n    getCommissionRecords(true);\r\n  }\r\n};\r\n\r\n// 格式化佣金\r\nconst formatCommission = (amount) => {\r\n  return distributionService.formatCommission(amount);\r\n};\r\n\r\n// 格式化时间\r\nconst formatTime = (time) => {\r\n  if (!time) return '';\r\n  \r\n  const date = new Date(time);\r\n  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\r\n};\r\n\r\n// 获取状态文本\r\nconst getStatusText = (status) => {\r\n  switch (status) {\r\n    case 'pending':\r\n      return '待结算';\r\n    case 'settled':\r\n      return '可提现';\r\n    case 'withdrawn':\r\n      return '已提现';\r\n    default:\r\n      return '未知状态';\r\n  }\r\n};\r\n\r\n// 获取状态前缀\r\nconst getStatusPrefix = (status) => {\r\n  return status === 'pending' ? '+预计 ' : '+';\r\n};\r\n\r\n// 获取状态样式类\r\nconst getStatusClass = (status) => {\r\n  return {\r\n    'pending': 'status-pending',\r\n    'settled': 'status-settled',\r\n    'withdrawn': 'status-withdrawn'\r\n  }[status] || '';\r\n};\r\n\r\n// 获取等级文本\r\nconst getLevelText = (level) => {\r\n  switch (level) {\r\n    case 1:\r\n      return '一级';\r\n    case 2:\r\n      return '二级';\r\n    case 3:\r\n      return '三级';\r\n    default:\r\n      return '';\r\n  }\r\n};\r\n\r\n// 页面导航\r\nconst navigateTo = (url) => {\r\n  uni.navigateTo({ url });\r\n};\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 显示帮助\r\nconst showHelp = () => {\r\n  uni.showModal({\r\n    title: '佣金明细帮助',\r\n    content: '佣金明细页面显示您的所有佣金记录，包括待结算、可提现和已提现的佣金。待结算佣金需要等待订单完成后才能提现。',\r\n    showCancel: false\r\n  });\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.commission-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: 30rpx;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\r\n  color: #fff;\r\n  padding: 88rpx 32rpx 30rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 72rpx;\r\n  height: 72rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 24rpx;\r\n  height: 24rpx;\r\n  border-left: 4rpx solid #fff;\r\n  border-bottom: 4rpx solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  letter-spacing: 1rpx;\r\n}\r\n\r\n.navbar-right {\r\n  width: 72rpx;\r\n  height: 72rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.help-icon {\r\n  width: 48rpx;\r\n  height: 48rpx;\r\n  border-radius: 24rpx;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 佣金统计卡片 */\r\n.stats-card {\r\n  margin: 30rpx;\r\n  background: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.stats-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.stats-item.main {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.stats-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.item-value {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.stats-item.main .item-value {\r\n  font-size: 48rpx;\r\n  color: #6B0FBE;\r\n}\r\n\r\n.item-label {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n}\r\n\r\n.stats-footer {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 30rpx;\r\n}\r\n\r\n.withdraw-btn {\r\n  background: linear-gradient(135deg, #FF9500, #FF5722);\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 40rpx;\r\n  font-size: 28rpx;\r\n  padding: 12rpx 60rpx;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 筛选栏 */\r\n.filter-bar {\r\n  display: flex;\r\n  background: #FFFFFF;\r\n  padding: 0 30rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.tab-item {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 80rpx;\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  position: relative;\r\n}\r\n\r\n.tab-item.active {\r\n  color: #6B0FBE;\r\n  font-weight: 600;\r\n}\r\n\r\n.tab-item.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 40rpx;\r\n  height: 4rpx;\r\n  background-color: #6B0FBE;\r\n  border-radius: 2rpx;\r\n}\r\n\r\n/* 佣金列表 */\r\n.commission-list {\r\n  margin: 0 30rpx;\r\n}\r\n\r\n.commission-item {\r\n  background: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  margin-bottom: 20rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.commission-info {\r\n  padding: 30rpx;\r\n}\r\n\r\n.commission-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.commission-title {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  flex: 1;\r\n}\r\n\r\n.commission-amount {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #FF5722;\r\n}\r\n\r\n.commission-amount.status-pending {\r\n  color: #FF9500;\r\n}\r\n\r\n.commission-amount.status-settled {\r\n  color: #6B0FBE;\r\n}\r\n\r\n.commission-amount.status-withdrawn {\r\n  color: #999;\r\n}\r\n\r\n.commission-detail {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.commission-order {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n}\r\n\r\n.commission-level {\r\n  font-size: 24rpx;\r\n  color: #6B0FBE;\r\n  background-color: rgba(107, 15, 190, 0.1);\r\n  padding: 4rpx 12rpx;\r\n  border-radius: 20rpx;\r\n}\r\n\r\n.commission-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.commission-time {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.commission-status {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 100rpx 0;\r\n}\r\n\r\n.empty-image {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n}\r\n\r\n/* 加载更多 */\r\n.load-more {\r\n  text-align: center;\r\n  padding: 30rpx 0;\r\n  font-size: 26rpx;\r\n  color: #666;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/distribution/pages/commission.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "onMounted", "loadMore", "distributionService", "uni", "MiniProgramPage"], "mappings": ";;;;;;;AAoGA,UAAM,OAAO;AAAA,MACX,EAAE,MAAM,MAAM,OAAO,MAAO;AAAA,MAC5B,EAAE,MAAM,OAAO,OAAO,UAAW;AAAA,MACjC,EAAE,MAAM,OAAO,OAAO,UAAW;AAAA,MACjC,EAAE,MAAM,OAAO,OAAO,YAAa;AAAA,IACrC;AAGA,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAG3B,UAAM,oBAAoBA,cAAAA,IAAI,CAAA,CAAE;AAGhC,UAAM,oBAAoBC,cAAAA,SAAS;AAAA,MACjC,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,WAAW;AAAA,IACb,CAAC;AAGD,UAAM,aAAaA,cAAAA,SAAS;AAAA,MAC1B,MAAM;AAAA,MACN,UAAU;AAAA,MACV,OAAO;AAAA,MACP,YAAY;AAAA,IACd,CAAC;AAGD,UAAM,cAAcD,cAAAA,IAAI,KAAK;AAG7B,UAAM,UAAUA,cAAAA,IAAI,KAAK;AAGzBE,kBAAAA,UAAU,YAAY;AAEpB,YAAM,qBAAoB;AAAA,IAC5B,CAAC;AAGD,UAAM,uBAAuB,OAAOC,YAAW,UAAU;AACvD,UAAI,QAAQ;AAAO;AAEnB,UAAI;AACF,gBAAQ,QAAQ;AAEhB,cAAM,OAAOA,YAAW,WAAW,OAAO,IAAI;AAE9C,cAAM,SAAS,MAAMC,0BAAmB,oBAAC,qBAAqB;AAAA,UAC5D;AAAA,UACA,UAAU,WAAW;AAAA,UACrB,QAAQ,UAAU;AAAA,QACxB,CAAK;AAED,YAAI,QAAQ;AAEV,cAAID,WAAU;AACZ,8BAAkB,QAAQ,CAAC,GAAG,kBAAkB,OAAO,GAAG,OAAO,IAAI;AAAA,UAC7E,OAAa;AACL,8BAAkB,QAAQ,OAAO;AAAA,UAClC;AAGD,qBAAW,OAAO;AAClB,qBAAW,QAAQ,OAAO,WAAW;AACrC,qBAAW,aAAa,OAAO,WAAW;AAG1C,sBAAY,QAAQ,WAAW,OAAO,WAAW;AAGjD,cAAI,OAAO,SAAS;AAClB,mBAAO,OAAO,mBAAmB,OAAO,OAAO;AAAA,UAChD;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdE,sBAAA,MAAA,MAAA,SAAA,wDAAc,YAAY,KAAK;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,UAAY;AACR,gBAAQ,QAAQ;AAAA,MACjB;AAAA,IACH;AAGA,UAAM,YAAY,CAAC,QAAQ;AACzB,UAAI,UAAU,UAAU;AAAK;AAE7B,gBAAU,QAAQ;AAClB;IACF;AAGA,UAAM,WAAW,MAAM;AACrB,UAAI,YAAY,SAAS,CAAC,QAAQ,OAAO;AACvC,6BAAqB,IAAI;AAAA,MAC1B;AAAA,IACH;AAGA,UAAM,mBAAmB,CAAC,WAAW;AACnC,aAAOD,0BAAmB,oBAAC,iBAAiB,MAAM;AAAA,IACpD;AAGA,UAAM,aAAa,CAAC,SAAS;AAC3B,UAAI,CAAC;AAAM,eAAO;AAElB,YAAM,OAAO,IAAI,KAAK,IAAI;AAC1B,aAAO,GAAG,KAAK,YAAa,CAAA,IAAI,OAAO,KAAK,aAAa,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,KAAK,UAAU,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,IACnN;AAGA,UAAM,gBAAgB,CAAC,WAAW;AAChC,cAAQ,QAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,UAAM,kBAAkB,CAAC,WAAW;AAClC,aAAO,WAAW,YAAY,SAAS;AAAA,IACzC;AAGA,UAAM,iBAAiB,CAAC,WAAW;AACjC,aAAO;AAAA,QACL,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,MACjB,EAAI,MAAM,KAAK;AAAA,IACf;AAGA,UAAM,eAAe,CAAC,UAAU;AAC9B,cAAQ,OAAK;AAAA,QACX,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,QAAQ;AAC1BC,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IACxB;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MAChB,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClRA,GAAG,WAAWC,SAAe;"}