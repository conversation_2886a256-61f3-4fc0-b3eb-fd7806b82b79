<template>
  <view class="red-packet-selector">
    <!-- 选择按钮 -->
    <view class="selector-btn" @click="showPopup = true">
      <view class="btn-icon">
        <image src="/static/images/red-packet-icon-small.png" mode="aspectFit"></image>
      </view>
      <view class="btn-text">
        <text v-if="!selectedRedPacket">添加红包</text>
        <text v-else>已添加红包 {{ formatAmount(selectedRedPacket.totalAmount) }}元</text>
      </view>
      <view class="btn-arrow">
        <text class="arrow">›</text>
      </view>
    </view>
    
    <!-- 红包弹窗 -->
    <view class="selector-popup" v-if="showPopup">
      <view class="popup-mask" @click="showPopup = false"></view>
      
      <view class="popup-content">
        <view class="popup-header">
          <text class="title">添加红包</text>
          <view class="close-btn" @click="showPopup = false">
            <text class="close-icon">×</text>
          </view>
        </view>
        
        <view class="popup-body">
          <!-- 红包创建表单 -->
          <red-packet-creator 
            :userBalance="userBalance" 
            @create-success="handleCreateSuccess"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import RedPacketCreator from './RedPacketCreator.vue';
import { formatRedPacketAmount } from '@/utils/redPacket.js';

export default {
  name: 'RedPacketSelector',
  components: {
    RedPacketCreator
  },
  props: {
    // 用户余额（单位：分）
    userBalance: {
      type: Number,
      default: 0
    },
    // 已选择的红包
    value: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      showPopup: false,
      selectedRedPacket: null
    };
  },
  watch: {
    value: {
      handler(val) {
        this.selectedRedPacket = val;
      },
      immediate: true
    }
  },
  methods: {
    // 格式化金额
    formatAmount(amount) {
      return formatRedPacketAmount(amount);
    },
    
    // 创建红包成功
    handleCreateSuccess(redPacket) {
      this.selectedRedPacket = redPacket;
      this.$emit('input', redPacket);
      this.showPopup = false;
      
      // 提示创建成功
      uni.showToast({
        title: '红包添加成功',
        icon: 'success'
      });
    }
  }
}
</script>

<style lang="scss">
.red-packet-selector {
  width: 100%;
  
  .selector-btn {
    display: flex;
    align-items: center;
    background-color: #FFF;
    border-radius: 8rpx;
    padding: 20rpx 24rpx;
    margin-bottom: 20rpx;
    
    .btn-icon {
      width: 48rpx;
      height: 48rpx;
      margin-right: 20rpx;
      
      image {
        width: 100%;
        height: 100%;
      }
    }
    
    .btn-text {
      flex: 1;
      font-size: 28rpx;
      color: #333;
    }
    
    .btn-arrow {
      width: 40rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      
      .arrow {
        font-size: 36rpx;
        color: #999;
        transform: rotate(90deg);
      }
    }
  }
  
  .selector-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    display: flex;
    flex-direction: column;
    
    .popup-mask {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
    }
    
    .popup-content {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #F8F8F8;
      border-radius: 24rpx 24rpx 0 0;
      padding-bottom: env(safe-area-inset-bottom);
      overflow: hidden;
      max-height: 90vh;
      display: flex;
      flex-direction: column;
      
      .popup-header {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100rpx;
        position: relative;
        background-color: #FFF;
        border-bottom: 1px solid #F0F0F0;
        
        .title {
          font-size: 32rpx;
          font-weight: 500;
          color: #333;
        }
        
        .close-btn {
          position: absolute;
          right: 20rpx;
          top: 0;
          bottom: 0;
          width: 80rpx;
          display: flex;
          justify-content: center;
          align-items: center;
          
          .close-icon {
            font-size: 40rpx;
            color: #999;
          }
        }
      }
      
      .popup-body {
        padding: 30rpx;
        overflow-y: auto;
        flex: 1;
      }
    }
  }
}
</style> 