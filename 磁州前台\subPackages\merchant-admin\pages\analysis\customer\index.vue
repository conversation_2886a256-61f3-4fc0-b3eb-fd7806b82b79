<template>
  <view class="customer-analysis-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">客户分析</text>
      <view class="navbar-right">
        <view class="export-icon" @click="exportData">📊</view>
      </view>
    </view>
    
    <!-- 日期选择器 -->
    <view class="date-selector-container">
      <view class="date-selector" @click="showDatePicker">
        <text class="date-text">{{currentDateRange}}</text>
        <text class="date-icon">📅</text>
      </view>
    </view>
    
    <!-- 客户概览 -->
    <view class="overview-section">
      <view class="overview-card">
        <view class="overview-item">
          <text class="overview-value">{{customerData.totalCustomers}}</text>
          <text class="overview-label">总客户数</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{customerData.newCustomers}}</text>
          <text class="overview-label">新增客户</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{customerData.activeRate}}%</text>
          <text class="overview-label">活跃率</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{customerData.retentionRate}}%</text>
          <text class="overview-label">留存率</text>
          <view :class="['trend-icon', customerData.retentionRate >= 65 ? 'up' : 'down']"></view>
        </view>
      </view>
    </view>
    
    <!-- 新老客户占比 -->
    <view class="new-old-section">
      <view class="section-header">
        <text class="section-title">新老客户占比</text>
        <text class="section-more" @click="navigateTo('./new-old')">查看详情</text>
      </view>
      
      <view class="new-old-card">
        <view class="pie-chart-container">
          <view class="pie-chart">
            <view class="pie-segment new" style="transform: rotate(0deg); clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 50%, 100% 100%, 50% 100%);">
            </view>
            <view class="pie-segment old" style="transform: rotate(180deg); clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 50%, 50% 50%);">
            </view>
            <view class="pie-center">
              <text class="pie-text">客户占比</text>
            </view>
          </view>
          
          <view class="pie-legend">
            <view class="legend-item">
              <view class="legend-color new"></view>
              <text class="legend-text">新客户 {{newOldData.newCustomerRate}}%</text>
            </view>
            <view class="legend-item">
              <view class="legend-color old"></view>
              <text class="legend-text">老客户 {{newOldData.oldCustomerRate}}%</text>
            </view>
          </view>
        </view>
        
        <view class="new-old-stats">
          <view class="stats-item">
            <text class="stats-label">新客户获取成本</text>
            <text class="stats-value">¥{{newOldData.acquisitionCost}}</text>
          </view>
          <view class="stats-item">
            <text class="stats-label">新客户转化率</text>
            <text class="stats-value">{{newOldData.conversionRate}}%</text>
          </view>
          <view class="stats-item">
            <text class="stats-label">老客户复购率</text>
            <text class="stats-value">{{newOldData.repurchaseRate}}%</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 客户生命周期分析 -->
    <view class="lifecycle-section">
      <view class="section-header">
        <text class="section-title">客户生命周期分析</text>
        <text class="section-more" @click="navigateTo('./lifecycle')">查看详情</text>
      </view>
      
      <view class="lifecycle-card">
        <view class="lifecycle-chart">
          <!-- 这里应该是图表组件，暂时用占位符 -->
          <view class="chart-placeholder">
            <text class="chart-text">客户生命周期分布图</text>
          </view>
        </view>
        
        <view class="lifecycle-stages">
          <view 
            v-for="(stage, index) in lifecycleData.stages" 
            :key="index"
            class="stage-item">
            <view class="stage-header">
              <view :class="['stage-icon', stage.type]">{{stage.icon}}</view>
              <view class="stage-info">
                <text class="stage-name">{{stage.name}}</text>
                <text class="stage-count">{{stage.count}}人 ({{stage.percentage}}%)</text>
              </view>
            </view>
            <view class="stage-bar">
              <view class="bar-fill" :style="{width: stage.percentage + '%', backgroundColor: stage.color}"></view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 客户价值分布 -->
    <view class="value-section">
      <view class="section-header">
        <text class="section-title">客户价值分布</text>
        <text class="section-more" @click="navigateTo('./value')">查看详情</text>
      </view>
      
      <view class="value-card">
        <view class="value-chart">
          <!-- 这里应该是图表组件，暂时用占位符 -->
          <view class="chart-placeholder">
            <text class="chart-text">客户价值金字塔</text>
          </view>
        </view>
        
        <view class="value-tiers">
          <view 
            v-for="(tier, index) in valueData.tiers" 
            :key="index"
            class="tier-item">
            <view class="tier-header">
              <view class="tier-icon" :style="{backgroundColor: tier.color}">{{tier.icon}}</view>
              <view class="tier-info">
                <text class="tier-name">{{tier.name}}</text>
                <text class="tier-desc">{{tier.description}}</text>
              </view>
            </view>
            <view class="tier-stats">
              <text class="tier-count">{{tier.count}}人</text>
              <text class="tier-value">¥{{tier.avgValue}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 流失预警与召回建议 -->
    <view class="churn-section">
      <view class="section-header">
        <text class="section-title">流失预警与召回建议</text>
        <text class="section-more" @click="navigateTo('./churn')">查看详情</text>
      </view>
      
      <view class="churn-card">
        <view class="churn-stats">
          <view class="churn-item">
            <text class="churn-value">{{churnData.churnRate}}%</text>
            <text class="churn-label">流失率</text>
          </view>
          <view class="churn-item">
            <text class="churn-value">{{churnData.riskCustomers}}</text>
            <text class="churn-label">流失风险客户</text>
          </view>
          <view class="churn-item">
            <text class="churn-value">{{churnData.recallRate}}%</text>
            <text class="churn-label">召回成功率</text>
          </view>
        </view>
        
        <view class="risk-customers">
          <text class="sub-title">高风险客户</text>
          <view class="customer-list">
            <view 
              v-for="(customer, index) in churnData.riskCustomerList" 
              :key="index"
              class="customer-item"
              @click="viewCustomerDetail(customer.id)">
              <image class="customer-avatar" :src="customer.avatar" mode="aspectFill"></image>
              <view class="customer-info">
                <text class="customer-name">{{customer.name}}</text>
                <text class="customer-meta">{{customer.lastPurchase}} | {{customer.riskLevel}}</text>
              </view>
              <view class="action-button" @click.stop="sendRecallMessage(customer.id)">
                <text class="button-text">发送召回</text>
              </view>
            </view>
          </view>
        </view>
        
        <view class="recall-tips">
          <view class="tip-icon">💡</view>
          <view class="tip-content">
            <text class="tip-title">流失防范建议</text>
            <text class="tip-text">{{churnData.preventionTip}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentDateRange: '2023-05-01 至 2023-05-31',
      customerData: {
        totalCustomers: '1,256',
        newCustomers: '128',
        activeRate: '42.5',
        retentionRate: '68.3'
      },
      newOldData: {
        newCustomerRate: 35,
        oldCustomerRate: 65,
        acquisitionCost: '28.50',
        conversionRate: '15.8',
        repurchaseRate: '42.5'
      },
      lifecycleData: {
        stages: [
          {
            name: '新获取',
            count: 128,
            percentage: 10,
            icon: '🆕',
            type: 'new',
            color: '#1677FF'
          },
          {
            name: '成长期',
            count: 356,
            percentage: 28,
            icon: '📈',
            type: 'growth',
            color: '#52c41a'
          },
          {
            name: '成熟期',
            count: 482,
            percentage: 38,
            icon: '⭐',
            type: 'mature',
            color: '#fa8c16'
          },
          {
            name: '衰退期',
            count: 186,
            percentage: 15,
            icon: '📉',
            type: 'decline',
            color: '#ff4d4f'
          },
          {
            name: '流失风险',
            count: 104,
            percentage: 9,
            icon: '⚠️',
            type: 'risk',
            color: '#f5222d'
          }
        ]
      },
      valueData: {
        tiers: [
          {
            name: '高价值客户',
            count: 125,
            avgValue: '1,280.50',
            icon: '💎',
            color: '#1677FF',
            description: '消费频繁，客单价高'
          },
          {
            name: '中高价值客户',
            count: 286,
            avgValue: '680.20',
            icon: '🥇',
            color: '#52c41a',
            description: '稳定消费，有成长空间'
          },
          {
            name: '中等价值客户',
            count: 458,
            avgValue: '320.80',
            icon: '🥈',
            color: '#fa8c16',
            description: '消费稳定，频次适中'
          },
          {
            name: '低价值客户',
            count: 387,
            avgValue: '120.50',
            icon: '🥉',
            color: '#ff4d4f',
            description: '消费频次低，客单价低'
          }
        ]
      },
      churnData: {
        churnRate: '7.5',
        riskCustomers: '104',
        recallRate: '35.8',
        preventionTip: '针对3个月未消费的客户发送专属优惠券，对高价值流失风险客户进行一对一沟通',
        riskCustomerList: [
          {
            id: '10001',
            name: '张三',
            avatar: '/static/images/avatar-1.png',
            lastPurchase: '90天前',
            riskLevel: '高风险'
          },
          {
            id: '10002',
            name: '李四',
            avatar: '/static/images/avatar-2.png',
            lastPurchase: '85天前',
            riskLevel: '高风险'
          },
          {
            id: '10003',
            name: '王五',
            avatar: '/static/images/avatar-3.png',
            lastPurchase: '78天前',
            riskLevel: '中风险'
          },
          {
            id: '10004',
            name: '赵六',
            avatar: '/static/images/avatar-4.png',
            lastPurchase: '75天前',
            riskLevel: '中风险'
          }
        ]
      }
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    navigateTo(url) {
      uni.navigateTo({ url });
    },
    showDatePicker() {
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    exportData() {
      uni.showActionSheet({
        itemList: ['导出Excel', '导出PDF', '生成分析报告'],
        success: (res) => {
          uni.showToast({
            title: '导出功能开发中',
            icon: 'none'
          });
        }
      });
    },
    viewCustomerDetail(id) {
      uni.navigateTo({
        url: `/pages/customer/index?id=${id}`
      });
    },
    sendRecallMessage(id) {
      uni.showToast({
        title: '召回消息已发送',
        icon: 'success'
      });
    }
  }
}
</script>

<style>
.customer-analysis-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 20px;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.export-icon {
  font-size: 20px;
  color: #fff;
}

.date-selector-container {
  padding: 16px;
  display: flex;
  justify-content: flex-end;
}

.date-selector {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 8px 12px;
  border-radius: 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.date-text {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}

.date-icon {
  font-size: 16px;
}

.overview-section, .new-old-section, .lifecycle-section, .value-section, .churn-section {
  padding: 0 16px;
  margin-bottom: 20px;
}

.overview-card, .new-old-card, .lifecycle-card, .value-card, .churn-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.overview-card {
  display: flex;
  justify-content: space-between;
}

.overview-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.overview-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 12px;
  color: #999;
}

.trend-icon {
  width: 0;
  height: 0;
  position: absolute;
  right: -15px;
  top: 8px;
}

.trend-icon.up {
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 8px solid #52c41a;
}

.trend-icon.down {
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 8px solid #f5222d;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-more {
  font-size: 12px;
  color: #1677FF;
}

.pie-chart-container {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.pie-chart {
  width: 120px;
  height: 120px;
  border-radius: 60px;
  position: relative;
  margin-right: 16px;
}

.pie-segment {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  top: 0;
  left: 0;
}

.pie-segment.new {
  background-color: #1677FF;
}

.pie-segment.old {
  background-color: #52c41a;
}

.pie-center {
  position: absolute;
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background-color: #fff;
  top: 30px;
  left: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pie-text {
  font-size: 12px;
  color: #666;
}

.pie-legend {
  flex: 1;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 8px;
}

.legend-color.new {
  background-color: #1677FF;
}

.legend-color.old {
  background-color: #52c41a;
}

.legend-text {
  font-size: 14px;
  color: #666;
}

.new-old-stats {
  display: flex;
  justify-content: space-between;
}

.stats-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.stats-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.lifecycle-chart, .value-chart {
  height: 180px;
  margin-bottom: 16px;
}

.chart-placeholder {
  height: 100%;
  background-color: #f9f9f9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-text {
  font-size: 14px;
  color: #999;
}

.lifecycle-stages {
  
}

.stage-item {
  margin-bottom: 16px;
}

.stage-item:last-child {
  margin-bottom: 0;
}

.stage-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.stage-icon {
  width: 28px;
  height: 28px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  margin-right: 8px;
}

.stage-icon.new {
  background-color: #e6f7ff;
  color: #1677FF;
}

.stage-icon.growth {
  background-color: #f6ffed;
  color: #52c41a;
}

.stage-icon.mature {
  background-color: #fff7e6;
  color: #fa8c16;
}

.stage-icon.decline, .stage-icon.risk {
  background-color: #fff1f0;
  color: #ff4d4f;
}

.stage-info {
  flex: 1;
}

.stage-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  display: block;
}

.stage-count {
  font-size: 12px;
  color: #999;
}

.stage-bar {
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  border-radius: 4px;
}

.value-tiers {
  
}

.tier-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tier-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.tier-header {
  display: flex;
  align-items: center;
}

.tier-icon {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin-right: 12px;
}

.tier-info {
  
}

.tier-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 2px;
}

.tier-desc {
  font-size: 12px;
  color: #999;
}

.tier-stats {
  text-align: right;
}

.tier-count {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 2px;
}

.tier-value {
  font-size: 12px;
  color: #ff6a00;
}

.churn-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.churn-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.churn-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.churn-label {
  font-size: 12px;
  color: #999;
}

.risk-customers {
  margin-bottom: 16px;
}

.sub-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
  display: block;
}

.customer-list {
  
}

.customer-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.customer-item:last-child {
  border-bottom: none;
}

.customer-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 12px;
  background-color: #f0f0f0;
}

.customer-info {
  flex: 1;
}

.customer-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.customer-meta {
  font-size: 12px;
  color: #999;
}

.action-button {
  padding: 6px 12px;
  background-color: #1677FF;
  border-radius: 4px;
}

.button-text {
  font-size: 12px;
  color: #fff;
}

.recall-tips {
  display: flex;
  background-color: #f6ffed;
  padding: 12px;
  border-radius: 4px;
}

.tip-icon {
  font-size: 20px;
  margin-right: 12px;
}

.tip-content {
  flex: 1;
}

.tip-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.tip-text {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
}
</style> 