{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/orders/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcb3JkZXJzXGluZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"orders-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\">\r\n      <view class=\"navbar-bg\"></view>\r\n      <view class=\"navbar-content\">\r\n        <view class=\"back-btn\" @click=\"goBack\">\r\n          <image src=\"/static/images/tabbar/最新返回键.png\" mode=\"aspectFit\" :style=\"{\r\n            width: '40rpx',\r\n            height: '40rpx'\r\n          }\"></image>\r\n        </view>\r\n        <view class=\"navbar-title\">订单管理</view>\r\n        <view class=\"navbar-right\">\r\n          <view class=\"search-btn\" @click=\"navigateTo('/subPackages/activity-showcase/pages/search/index?type=orders')\">\r\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n              <circle cx=\"11\" cy=\"11\" r=\"8\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\r\n              <path d=\"M21 21l-4.35-4.35\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            </svg>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 订单类型标签栏 -->\r\n    <view class=\"order-tabs\">\r\n      <view \r\n        v-for=\"(tab, index) in orderTabs\" \r\n        :key=\"index\"\r\n        class=\"tab-item\"\r\n        :class=\"{ active: currentTab === index }\"\r\n        @click=\"switchTab(index)\"\r\n      >\r\n        <text class=\"tab-text\">{{ tab.name }}</text>\r\n        <view class=\"tab-indicator\" v-if=\"currentTab === index\" :style=\"{\r\n          background: 'linear-gradient(90deg, #5AC8FA 0%, #90E0FF 100%)'\r\n        }\"></view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 订单列表区域 -->\r\n    <swiper class=\"orders-swiper\" :current=\"currentTab\" @change=\"onSwiperChange\">\r\n      <swiper-item v-for=\"(tab, tabIndex) in orderTabs\" :key=\"tabIndex\">\r\n        <scroll-view \r\n          class=\"tab-content\" \r\n          scroll-y \r\n          refresher-enabled\r\n          :refresher-triggered=\"isRefreshing\"\r\n          @refresherrefresh=\"onRefresh\"\r\n          @scrolltolower=\"loadMore\"\r\n        >\r\n          <view class=\"order-list\">\r\n            <view \r\n              v-for=\"order in getOrdersByStatus(tab.status)\" \r\n              :key=\"order.id\"\r\n              class=\"order-card\"\r\n              @click=\"viewOrderDetail(order)\"\r\n            >\r\n              <!-- 订单头部 -->\r\n              <view class=\"order-header\">\r\n                <view class=\"shop-info\">\r\n                  <svg class=\"shop-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n                    <path d=\"M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z\" stroke=\"#5AC8FA\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                    <path d=\"M9 22V12h6v10\" stroke=\"#5AC8FA\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                  </svg>\r\n                  <text class=\"shop-name\">{{ order.shopName }}</text>\r\n                  <svg class=\"arrow-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n                    <path d=\"M9 18l6-6-6-6\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                  </svg>\r\n                </view>\r\n                <view class=\"order-status\" :style=\"{ color: getStatusColor(order.status) }\">\r\n                  {{ getStatusText(order.status) }}\r\n                </view>\r\n              </view>\r\n\r\n              <!-- 订单内容 -->\r\n              <view class=\"order-content\">\r\n                <view \r\n                  v-for=\"(item, itemIndex) in order.items\" \r\n                  :key=\"itemIndex\"\r\n                  class=\"order-item\"\r\n                >\r\n                  <image :src=\"item.image\" class=\"item-image\" mode=\"aspectFill\"></image>\r\n                  <view class=\"item-info\">\r\n                    <text class=\"item-name\">{{ item.name }}</text>\r\n                    <text class=\"item-spec\">{{ item.specification }}</text>\r\n                    <view class=\"item-price-qty\">\r\n                      <text class=\"item-price\">¥{{ item.price.toFixed(2) }}</text>\r\n                      <text class=\"item-qty\">x{{ item.quantity }}</text>\r\n                    </view>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n\r\n              <!-- 订单底部 -->\r\n              <view class=\"order-footer\">\r\n                <view class=\"order-total\">\r\n                  <text class=\"total-text\">共{{ getTotalQuantity(order) }}件商品</text>\r\n                  <text class=\"total-price\">合计: ¥{{ order.totalAmount.toFixed(2) }}</text>\r\n                </view>\r\n                <view class=\"order-actions\">\r\n                  <view \r\n                    v-if=\"order.status === 'pending_payment'\"\r\n                    class=\"action-btn cancel\"\r\n                    @click.stop=\"cancelOrder(order)\"\r\n                  >\r\n                    取消订单\r\n                  </view>\r\n                  <view \r\n                    class=\"action-btn primary\"\r\n                    :style=\"{\r\n                      background: getPrimaryActionBgColor(order.status),\r\n                      color: '#FFFFFF'\r\n                    }\"\r\n                    @click.stop=\"handlePrimaryAction(order)\"\r\n                  >\r\n                    {{ getPrimaryActionText(order.status) }}\r\n                  </view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n\r\n          <!-- 空状态 -->\r\n          <view class=\"empty-state\" v-if=\"getOrdersByStatus(tab.status).length === 0\">\r\n            <image class=\"empty-image\" :src=\"tab.emptyImage || '/static/images/empty-orders.png'\"></image>\r\n            <text class=\"empty-text\">{{ tab.emptyText || '暂无相关订单' }}</text>\r\n            <view class=\"action-btn\" @click=\"navigateTo('/subPackages/activity-showcase/pages/index/index')\" :style=\"{\r\n              background: 'linear-gradient(135deg, #5AC8FA 0%, #90E0FF 100%)',\r\n              borderRadius: '35px',\r\n              boxShadow: '0 5px 15px rgba(90,200,250,0.3)'\r\n            }\">\r\n              <text>去逛逛</text>\r\n            </view>\r\n          </view>\r\n        </scroll-view>\r\n      </swiper-item>\r\n    </swiper>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue';\r\n\r\n// 页面状态\r\nconst currentTab = ref(0);\r\nconst isRefreshing = ref(false);\r\nconst orderList = ref([]);\r\n\r\n// 模拟数据\r\nconst mockOrders = [\r\n  {\r\n    id: '202405120001',\r\n    shopName: '磁州活动中心',\r\n    status: 'pending_payment',\r\n    createTime: '2024-05-12 14:30',\r\n    totalAmount: 199.00,\r\n    items: [\r\n      {\r\n        id: 1,\r\n        name: '夏季亲子户外拓展活动',\r\n        specification: '2大1小家庭套餐',\r\n        price: 199.00,\r\n        quantity: 1,\r\n        image: '/static/demo/activity1.jpg'\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    id: '202405110002',\r\n    shopName: '磁州文化馆',\r\n    status: 'pending_delivery',\r\n    createTime: '2024-05-11 10:15',\r\n    totalAmount: 98.00,\r\n    items: [\r\n      {\r\n        id: 2,\r\n        name: '传统文化体验课',\r\n        specification: '单人票',\r\n        price: 49.00,\r\n        quantity: 2,\r\n        image: '/static/demo/activity2.jpg'\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    id: '202405100003',\r\n    shopName: '磁州体育中心',\r\n    status: 'pending_receipt',\r\n    createTime: '2024-05-10 16:45',\r\n    totalAmount: 150.00,\r\n    items: [\r\n      {\r\n        id: 3,\r\n        name: '篮球训练营',\r\n        specification: '月卡',\r\n        price: 150.00,\r\n        quantity: 1,\r\n        image: '/static/demo/activity3.jpg'\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    id: '202405090004',\r\n    shopName: '磁州美食街',\r\n    status: 'pending_review',\r\n    createTime: '2024-05-09 19:20',\r\n    totalAmount: 88.00,\r\n    items: [\r\n      {\r\n        id: 4,\r\n        name: '美食节门票',\r\n        specification: '双人票',\r\n        price: 88.00,\r\n        quantity: 1,\r\n        image: '/static/demo/activity4.jpg'\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    id: '202405080005',\r\n    shopName: '磁州艺术中心',\r\n    status: 'completed',\r\n    createTime: '2024-05-08 13:10',\r\n    totalAmount: 120.00,\r\n    items: [\r\n      {\r\n        id: 5,\r\n        name: '油画体验课',\r\n        specification: '单人票',\r\n        price: 120.00,\r\n        quantity: 1,\r\n        image: '/static/demo/activity5.jpg'\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\n// 订单标签页\r\nconst orderTabs = [\r\n  { name: '全部', status: 'all', emptyText: '暂无订单', emptyImage: '/static/images/empty-orders.png' },\r\n  { name: '待付款', status: 'pending_payment', emptyText: '暂无待付款订单', emptyImage: '/static/images/empty-payment.png' },\r\n  { name: '待发货', status: 'pending_delivery', emptyText: '暂无待发货订单', emptyImage: '/static/images/empty-delivery.png' },\r\n  { name: '待收货', status: 'pending_receipt', emptyText: '暂无待收货订单', emptyImage: '/static/images/empty-receipt.png' },\r\n  { name: '待评价', status: 'pending_review', emptyText: '暂无待评价订单', emptyImage: '/static/images/empty-review.png' }\r\n];\r\n\r\n// 生命周期\r\nonMounted(() => {\r\n  loadOrders();\r\n});\r\n\r\n// 方法\r\nconst loadOrders = () => {\r\n  // 模拟加载数据\r\n  orderList.value = mockOrders;\r\n};\r\n\r\nconst getOrdersByStatus = (status) => {\r\n  if (status === 'all') {\r\n    return orderList.value;\r\n  }\r\n  return orderList.value.filter(order => order.status === status);\r\n};\r\n\r\nconst switchTab = (index) => {\r\n  currentTab.value = index;\r\n};\r\n\r\nconst onSwiperChange = (e) => {\r\n  currentTab.value = e.detail.current;\r\n};\r\n\r\nconst onRefresh = () => {\r\n  isRefreshing.value = true;\r\n  setTimeout(() => {\r\n    loadOrders();\r\n    isRefreshing.value = false;\r\n  }, 1000);\r\n};\r\n\r\nconst loadMore = () => {\r\n  // 模拟加载更多\r\n  console.log('加载更多订单');\r\n};\r\n\r\nconst getTotalQuantity = (order) => {\r\n  return order.items.reduce((total, item) => total + item.quantity, 0);\r\n};\r\n\r\nconst getStatusText = (status) => {\r\n  const statusMap = {\r\n    'pending_payment': '待付款',\r\n    'pending_delivery': '待发货',\r\n    'pending_receipt': '待收货',\r\n    'pending_review': '待评价',\r\n    'completed': '已完成',\r\n    'cancelled': '已取消',\r\n    'after_sale': '售后中'\r\n  };\r\n  return statusMap[status] || '未知状态';\r\n};\r\n\r\nconst getStatusColor = (status) => {\r\n  const colorMap = {\r\n    'pending_payment': '#FF9500',\r\n    'pending_delivery': '#5AC8FA',\r\n    'pending_receipt': '#5AC8FA',\r\n    'pending_review': '#34C759',\r\n    'completed': '#8E8E93',\r\n    'cancelled': '#8E8E93',\r\n    'after_sale': '#FF3B30'\r\n  };\r\n  return colorMap[status] || '#8E8E93';\r\n};\r\n\r\nconst getPrimaryActionText = (status) => {\r\n  const actionMap = {\r\n    'pending_payment': '立即付款',\r\n    'pending_delivery': '提醒发货',\r\n    'pending_receipt': '确认收货',\r\n    'pending_review': '去评价',\r\n    'completed': '再次购买',\r\n    'cancelled': '删除订单',\r\n    'after_sale': '查看进度'\r\n  };\r\n  return actionMap[status] || '查看详情';\r\n};\r\n\r\nconst getPrimaryActionBgColor = (status) => {\r\n  const bgColorMap = {\r\n    'pending_payment': 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)',\r\n    'pending_delivery': 'linear-gradient(135deg, #5AC8FA 0%, #90E0FF 100%)',\r\n    'pending_receipt': 'linear-gradient(135deg, #5AC8FA 0%, #90E0FF 100%)',\r\n    'pending_review': 'linear-gradient(135deg, #34C759 0%, #7ED321 100%)',\r\n    'completed': 'linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)',\r\n    'cancelled': 'linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)',\r\n    'after_sale': 'linear-gradient(135deg, #FF3B30 0%, #FF9580 100%)'\r\n  };\r\n  return bgColorMap[status] || 'linear-gradient(135deg, #5AC8FA 0%, #90E0FF 100%)';\r\n};\r\n\r\nconst handlePrimaryAction = (order) => {\r\n  switch (order.status) {\r\n    case 'pending_payment':\r\n      navigateTo(`/subPackages/activity-showcase/pages/payment/index?orderId=${order.id}`);\r\n      break;\r\n    case 'pending_delivery':\r\n      remindDelivery(order);\r\n      break;\r\n    case 'pending_receipt':\r\n      confirmReceipt(order);\r\n      break;\r\n    case 'pending_review':\r\n      navigateTo(`/subPackages/activity-showcase/pages/review/index?orderId=${order.id}`);\r\n      break;\r\n    case 'completed':\r\n      buyAgain(order);\r\n      break;\r\n    case 'cancelled':\r\n      deleteOrder(order);\r\n      break;\r\n    default:\r\n      viewOrderDetail(order);\r\n  }\r\n};\r\n\r\nconst viewOrderDetail = (order) => {\r\n  navigateTo(`/subPackages/activity-showcase/pages/orders/detail?id=${order.id}`);\r\n};\r\n\r\nconst cancelOrder = (order) => {\r\n  uni.showModal({\r\n    title: '提示',\r\n    content: '确认取消该订单吗？',\r\n    success: function(res) {\r\n      if (res.confirm) {\r\n        // 模拟取消订单\r\n        uni.showToast({\r\n          title: '订单已取消',\r\n          icon: 'success'\r\n        });\r\n        // 更新订单状态\r\n        const index = orderList.value.findIndex(item => item.id === order.id);\r\n        if (index !== -1) {\r\n          orderList.value[index].status = 'cancelled';\r\n        }\r\n      }\r\n    }\r\n  });\r\n};\r\n\r\nconst remindDelivery = (order) => {\r\n  uni.showToast({\r\n    title: '已提醒商家发货',\r\n    icon: 'success'\r\n  });\r\n};\r\n\r\nconst confirmReceipt = (order) => {\r\n  uni.showModal({\r\n    title: '提示',\r\n    content: '确认已收到商品吗？',\r\n    success: function(res) {\r\n      if (res.confirm) {\r\n        // 模拟确认收货\r\n        uni.showToast({\r\n          title: '确认收货成功',\r\n          icon: 'success'\r\n        });\r\n        // 更新订单状态\r\n        const index = orderList.value.findIndex(item => item.id === order.id);\r\n        if (index !== -1) {\r\n          orderList.value[index].status = 'pending_review';\r\n        }\r\n      }\r\n    }\r\n  });\r\n};\r\n\r\nconst buyAgain = (order) => {\r\n  // 模拟再次购买\r\n  uni.showToast({\r\n    title: '已添加到购物车',\r\n    icon: 'success'\r\n  });\r\n};\r\n\r\nconst deleteOrder = (order) => {\r\n  uni.showModal({\r\n    title: '提示',\r\n    content: '确认删除该订单吗？',\r\n    success: function(res) {\r\n      if (res.confirm) {\r\n        // 模拟删除订单\r\n        uni.showToast({\r\n          title: '订单已删除',\r\n          icon: 'success'\r\n        });\r\n        // 从列表中移除\r\n        const index = orderList.value.findIndex(item => item.id === order.id);\r\n        if (index !== -1) {\r\n          orderList.value.splice(index, 1);\r\n        }\r\n      }\r\n    }\r\n  });\r\n};\r\n\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\nconst navigateTo = (url) => {\r\n  uni.navigateTo({ url });\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.orders-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F5F5;\r\n  padding-bottom: 30rpx;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.custom-navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  z-index: 100;\r\n}\r\n\r\n.navbar-bg {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(135deg, #5AC8FA 0%, #90E0FF 100%);\r\n}\r\n\r\n.navbar-content {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  height: 103rpx; /* 原来是98rpx，增加5rpx */\r\n  padding: calc(var(--status-bar-height) + 8rpx) 30rpx 0; /* 向下移动8rpx */\r\n}\r\n\r\n.back-btn, .search-btn {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.navbar-title {\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n  color: #FFFFFF;\r\n}\r\n\r\n.navbar-right {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n/* 标签栏样式 */\r\n.order-tabs {\r\n  display: flex;\r\n  background: #FFFFFF;\r\n  padding: 0 20rpx;\r\n  margin-top: calc(var(--status-bar-height) + 111rpx); /* 原来是106rpx，增加5rpx */\r\n  border-bottom: 1rpx solid #EEEEEE;\r\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.tab-item {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 20rpx 0;\r\n  position: relative;\r\n}\r\n\r\n.tab-text {\r\n  font-size: 28rpx;\r\n  color: #333333;\r\n  padding: 0 10rpx;\r\n}\r\n\r\n.tab-item.active .tab-text {\r\n  color: #5AC8FA;\r\n  font-weight: 500;\r\n}\r\n\r\n.tab-indicator {\r\n  position: absolute;\r\n  bottom: 0;\r\n  width: 40rpx;\r\n  height: 6rpx;\r\n  border-radius: 3rpx;\r\n}\r\n\r\n/* 订单列表样式 */\r\n.orders-swiper {\r\n  height: calc(100vh - var(--status-bar-height) - 111rpx - 70rpx); /* 原来是106rpx，增加5rpx */\r\n}\r\n\r\n.tab-content {\r\n  height: 100%;\r\n  padding: 20rpx;\r\n}\r\n\r\n.order-list {\r\n  padding-bottom: 30rpx;\r\n}\r\n\r\n.order-card {\r\n  background: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  margin-bottom: 20rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.order-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20rpx 30rpx;\r\n  border-bottom: 1rpx solid #F5F5F5;\r\n}\r\n\r\n.shop-info {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.shop-icon {\r\n  margin-right: 10rpx;\r\n}\r\n\r\n.shop-name {\r\n  font-size: 28rpx;\r\n  color: #333333;\r\n  margin-right: 10rpx;\r\n}\r\n\r\n.arrow-icon {\r\n  margin-left: 10rpx;\r\n}\r\n\r\n.order-status {\r\n  font-size: 26rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.order-content {\r\n  padding: 20rpx 30rpx;\r\n}\r\n\r\n.order-item {\r\n  display: flex;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.order-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.item-image {\r\n  width: 140rpx;\r\n  height: 140rpx;\r\n  border-radius: 10rpx;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.item-info {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n}\r\n\r\n.item-name {\r\n  font-size: 28rpx;\r\n  color: #333333;\r\n  margin-bottom: 10rpx;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.item-spec {\r\n  font-size: 24rpx;\r\n  color: #999999;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.item-price-qty {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.item-price {\r\n  font-size: 28rpx;\r\n  color: #333333;\r\n  font-weight: 500;\r\n}\r\n\r\n.item-qty {\r\n  font-size: 24rpx;\r\n  color: #999999;\r\n}\r\n\r\n.order-footer {\r\n  padding: 20rpx 30rpx;\r\n  border-top: 1rpx solid #F5F5F5;\r\n}\r\n\r\n.order-total {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.total-text {\r\n  font-size: 24rpx;\r\n  color: #999999;\r\n  margin-right: 10rpx;\r\n}\r\n\r\n.total-price {\r\n  font-size: 28rpx;\r\n  color: #333333;\r\n  font-weight: bold;\r\n}\r\n\r\n.order-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  align-items: center;\r\n}\r\n\r\n.action-btn {\r\n  padding: 10rpx 30rpx;\r\n  border-radius: 30rpx;\r\n  font-size: 26rpx;\r\n  margin-left: 20rpx;\r\n}\r\n\r\n.action-btn.cancel {\r\n  border: 1rpx solid #DDDDDD;\r\n  color: #666666;\r\n}\r\n\r\n.action-btn.primary {\r\n  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);\r\n}\r\n\r\n/* 空状态样式 */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 100rpx 0;\r\n}\r\n\r\n.empty-image {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 28rpx;\r\n  color: #999999;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.empty-state .action-btn {\r\n  padding: 15rpx 60rpx;\r\n  font-size: 28rpx;\r\n  color: #FFFFFF;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/orders/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni"], "mappings": ";;;;;;;;;;;;AAiJA,UAAM,aAAaA,cAAAA,IAAI,CAAC;AACxB,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAC9B,UAAM,YAAYA,cAAAA,IAAI,CAAA,CAAE;AAGxB,UAAM,aAAa;AAAA,MACjB;AAAA,QACE,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,OAAO;AAAA,UACL;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,eAAe;AAAA,YACf,OAAO;AAAA,YACP,UAAU;AAAA,YACV,OAAO;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,OAAO;AAAA,UACL;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,eAAe;AAAA,YACf,OAAO;AAAA,YACP,UAAU;AAAA,YACV,OAAO;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,OAAO;AAAA,UACL;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,eAAe;AAAA,YACf,OAAO;AAAA,YACP,UAAU;AAAA,YACV,OAAO;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,OAAO;AAAA,UACL;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,eAAe;AAAA,YACf,OAAO;AAAA,YACP,UAAU;AAAA,YACV,OAAO;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,OAAO;AAAA,UACL;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,eAAe;AAAA,YACf,OAAO;AAAA,YACP,UAAU;AAAA,YACV,OAAO;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACH;AAGA,UAAM,YAAY;AAAA,MAChB,EAAE,MAAM,MAAM,QAAQ,OAAO,WAAW,QAAQ,YAAY,kCAAmC;AAAA,MAC/F,EAAE,MAAM,OAAO,QAAQ,mBAAmB,WAAW,WAAW,YAAY,mCAAoC;AAAA,MAChH,EAAE,MAAM,OAAO,QAAQ,oBAAoB,WAAW,WAAW,YAAY,oCAAqC;AAAA,MAClH,EAAE,MAAM,OAAO,QAAQ,mBAAmB,WAAW,WAAW,YAAY,mCAAoC;AAAA,MAChH,EAAE,MAAM,OAAO,QAAQ,kBAAkB,WAAW,WAAW,YAAY,kCAAmC;AAAA,IAChH;AAGAC,kBAAAA,UAAU,MAAM;AACd;IACF,CAAC;AAGD,UAAM,aAAa,MAAM;AAEvB,gBAAU,QAAQ;AAAA,IACpB;AAEA,UAAM,oBAAoB,CAAC,WAAW;AACpC,UAAI,WAAW,OAAO;AACpB,eAAO,UAAU;AAAA,MAClB;AACD,aAAO,UAAU,MAAM,OAAO,WAAS,MAAM,WAAW,MAAM;AAAA,IAChE;AAEA,UAAM,YAAY,CAAC,UAAU;AAC3B,iBAAW,QAAQ;AAAA,IACrB;AAEA,UAAM,iBAAiB,CAAC,MAAM;AAC5B,iBAAW,QAAQ,EAAE,OAAO;AAAA,IAC9B;AAEA,UAAM,YAAY,MAAM;AACtB,mBAAa,QAAQ;AACrB,iBAAW,MAAM;AACf;AACA,qBAAa,QAAQ;AAAA,MACtB,GAAE,GAAI;AAAA,IACT;AAEA,UAAM,WAAW,MAAM;AAErBC,oBAAAA,MAAA,MAAA,OAAA,+DAAY,QAAQ;AAAA,IACtB;AAEA,UAAM,mBAAmB,CAAC,UAAU;AAClC,aAAO,MAAM,MAAM,OAAO,CAAC,OAAO,SAAS,QAAQ,KAAK,UAAU,CAAC;AAAA,IACrE;AAEA,UAAM,gBAAgB,CAAC,WAAW;AAChC,YAAM,YAAY;AAAA,QAChB,mBAAmB;AAAA,QACnB,oBAAoB;AAAA,QACpB,mBAAmB;AAAA,QACnB,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,aAAa;AAAA,QACb,cAAc;AAAA,MAClB;AACE,aAAO,UAAU,MAAM,KAAK;AAAA,IAC9B;AAEA,UAAM,iBAAiB,CAAC,WAAW;AACjC,YAAM,WAAW;AAAA,QACf,mBAAmB;AAAA,QACnB,oBAAoB;AAAA,QACpB,mBAAmB;AAAA,QACnB,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,aAAa;AAAA,QACb,cAAc;AAAA,MAClB;AACE,aAAO,SAAS,MAAM,KAAK;AAAA,IAC7B;AAEA,UAAM,uBAAuB,CAAC,WAAW;AACvC,YAAM,YAAY;AAAA,QAChB,mBAAmB;AAAA,QACnB,oBAAoB;AAAA,QACpB,mBAAmB;AAAA,QACnB,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,aAAa;AAAA,QACb,cAAc;AAAA,MAClB;AACE,aAAO,UAAU,MAAM,KAAK;AAAA,IAC9B;AAEA,UAAM,0BAA0B,CAAC,WAAW;AAC1C,YAAM,aAAa;AAAA,QACjB,mBAAmB;AAAA,QACnB,oBAAoB;AAAA,QACpB,mBAAmB;AAAA,QACnB,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,aAAa;AAAA,QACb,cAAc;AAAA,MAClB;AACE,aAAO,WAAW,MAAM,KAAK;AAAA,IAC/B;AAEA,UAAM,sBAAsB,CAAC,UAAU;AACrC,cAAQ,MAAM,QAAM;AAAA,QAClB,KAAK;AACH,qBAAW,8DAA8D,MAAM,EAAE,EAAE;AACnF;AAAA,QACF,KAAK;AACH,yBAAoB;AACpB;AAAA,QACF,KAAK;AACH,yBAAe,KAAK;AACpB;AAAA,QACF,KAAK;AACH,qBAAW,6DAA6D,MAAM,EAAE,EAAE;AAClF;AAAA,QACF,KAAK;AACH,mBAAc;AACd;AAAA,QACF,KAAK;AACH,sBAAY,KAAK;AACjB;AAAA,QACF;AACE,0BAAgB,KAAK;AAAA,MACxB;AAAA,IACH;AAEA,UAAM,kBAAkB,CAAC,UAAU;AACjC,iBAAW,yDAAyD,MAAM,EAAE,EAAE;AAAA,IAChF;AAEA,UAAM,cAAc,CAAC,UAAU;AAC7BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,SAAS,KAAK;AACrB,cAAI,IAAI,SAAS;AAEfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAED,kBAAM,QAAQ,UAAU,MAAM,UAAU,UAAQ,KAAK,OAAO,MAAM,EAAE;AACpE,gBAAI,UAAU,IAAI;AAChB,wBAAU,MAAM,KAAK,EAAE,SAAS;AAAA,YACjC;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,iBAAiB,CAAC,UAAU;AAChCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAEA,UAAM,iBAAiB,CAAC,UAAU;AAChCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,SAAS,KAAK;AACrB,cAAI,IAAI,SAAS;AAEfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAED,kBAAM,QAAQ,UAAU,MAAM,UAAU,UAAQ,KAAK,OAAO,MAAM,EAAE;AACpE,gBAAI,UAAU,IAAI;AAChB,wBAAU,MAAM,KAAK,EAAE,SAAS;AAAA,YACjC;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,WAAW,CAAC,UAAU;AAE1BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAEA,UAAM,cAAc,CAAC,UAAU;AAC7BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,SAAS,KAAK;AACrB,cAAI,IAAI,SAAS;AAEfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAED,kBAAM,QAAQ,UAAU,MAAM,UAAU,UAAQ,KAAK,OAAO,MAAM,EAAE;AACpE,gBAAI,UAAU,IAAI;AAChB,wBAAU,MAAM,OAAO,OAAO,CAAC;AAAA,YAChC;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAEA,UAAM,aAAa,CAAC,QAAQ;AAC1BA,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IACxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtcA,GAAG,WAAW,eAAe;"}