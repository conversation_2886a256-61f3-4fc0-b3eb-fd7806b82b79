"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const FabButtons = () => "../../../components/FabButtons.js";
const _sfc_main = {
  data() {
    return {
      activityInfo: {
        id: 1,
        title: "磁州美食节",
        image: "/static/images/banner/banner-1.png",
        time: "2024-03-20 至 2024-03-25",
        location: "磁县人民广场",
        description: "汇聚磁州各地特色美食，带您品尝地道磁州味道。活动期间设有美食品鉴、厨艺比拼、文化展示等多个环节，欢迎广大市民朋友参与。",
        status: "进行中",
        rules: [
          "活动时间：每日10:00-22:00",
          "需要提前在线报名或现场登记",
          "请遵守场地秩序，文明参与活动",
          "主办方保留活动最终解释权"
        ],
        organizer: "磁县文化旅游局",
        phone: "0310-12345678"
      }
    };
  },
  onLoad(options) {
    common_vendor.index.__f__("log", "at subPackages/activity/pages/detail.vue:90", "活动ID:", options.id);
  },
  methods: {
    shareActivity() {
      common_vendor.index.showToast({
        title: "分享功能开发中",
        icon: "none"
      });
    },
    joinActivity() {
      common_vendor.index.showToast({
        title: "报名成功",
        icon: "success"
      });
    }
  },
  components: { FabButtons }
};
if (!Array) {
  const _component_fab_buttons = common_vendor.resolveComponent("fab-buttons");
  _component_fab_buttons();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $data.activityInfo.image,
    b: common_vendor.t($data.activityInfo.title),
    c: common_vendor.t($data.activityInfo.status),
    d: common_vendor.n($data.activityInfo.status === "进行中" ? "status-active" : "status-ended"),
    e: common_assets._imports_1$28,
    f: common_vendor.t($data.activityInfo.time),
    g: common_assets._imports_2$24,
    h: common_vendor.t($data.activityInfo.location),
    i: common_vendor.t($data.activityInfo.description),
    j: common_vendor.f($data.activityInfo.rules, (rule, index, i0) => {
      return {
        a: common_vendor.t(index + 1),
        b: common_vendor.t(rule),
        c: index
      };
    }),
    k: common_vendor.t($data.activityInfo.organizer),
    l: common_vendor.t($data.activityInfo.phone),
    m: common_assets._imports_2$1,
    n: common_vendor.o((...args) => $options.shareActivity && $options.shareActivity(...args)),
    o: common_vendor.o((...args) => $options.joinActivity && $options.joinActivity(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/activity/pages/detail.js.map
