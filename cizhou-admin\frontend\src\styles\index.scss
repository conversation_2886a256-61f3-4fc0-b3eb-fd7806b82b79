// 全局样式文件

// 导入变量
@import './variables.scss';

// 重置样式
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: var(--el-text-color-primary);
  background-color: var(--el-bg-color-page);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--el-bg-color-page);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color-light);
  border-radius: 4px;
  
  &:hover {
    background: var(--el-border-color);
  }
}

// 链接样式
a {
  color: var(--el-color-primary);
  text-decoration: none;
  
  &:hover {
    color: var(--el-color-primary-light-3);
  }
}

// 表格样式
.el-table {
  .el-table__header {
    th {
      background-color: var(--el-fill-color-light);
      color: var(--el-text-color-primary);
      font-weight: 600;
    }
  }
  
  .el-table__row {
    &:hover {
      background-color: var(--el-fill-color-lighter);
    }
  }
}

// 表单样式
.el-form {
  .el-form-item__label {
    font-weight: 500;
  }
}

// 卡片样式
.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .el-card__header {
    border-bottom: 1px solid var(--el-border-color-lighter);
    padding: 16px 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .title {
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }
  }
  
  .el-card__body {
    padding: 20px;
  }
}

// 按钮样式
.el-button {
  border-radius: 6px;
  font-weight: 500;
  
  &.is-circle {
    border-radius: 50%;
  }
  
  &.is-round {
    border-radius: 20px;
  }
}

// 输入框样式
.el-input {
  .el-input__wrapper {
    border-radius: 6px;
  }
}

// 选择器样式
.el-select {
  .el-input__wrapper {
    border-radius: 6px;
  }
}

// 对话框样式
.el-dialog {
  border-radius: 8px;
  
  .el-dialog__header {
    border-bottom: 1px solid var(--el-border-color-lighter);
    padding: 16px 20px;
    
    .el-dialog__title {
      font-size: 16px;
      font-weight: 600;
    }
  }
  
  .el-dialog__body {
    padding: 20px;
  }
  
  .el-dialog__footer {
    border-top: 1px solid var(--el-border-color-lighter);
    padding: 16px 20px;
  }
}

// 抽屉样式
.el-drawer {
  .el-drawer__header {
    border-bottom: 1px solid var(--el-border-color-lighter);
    padding: 16px 20px;
    margin-bottom: 0;
    
    .el-drawer__title {
      font-size: 16px;
      font-weight: 600;
    }
  }
  
  .el-drawer__body {
    padding: 20px;
  }
}

// 分页样式
.el-pagination {
  .el-pagination__total,
  .el-pagination__jump {
    color: var(--el-text-color-regular);
  }
}

// 标签样式
.el-tag {
  border-radius: 4px;
  font-weight: 500;
}

// 徽章样式
.el-badge {
  .el-badge__content {
    border-radius: 10px;
    font-weight: 500;
  }
}

// 工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

// 间距工具类
@for $i from 0 through 10 {
  .m-#{$i} {
    margin: #{$i * 4}px;
  }
  
  .mt-#{$i} {
    margin-top: #{$i * 4}px;
  }
  
  .mr-#{$i} {
    margin-right: #{$i * 4}px;
  }
  
  .mb-#{$i} {
    margin-bottom: #{$i * 4}px;
  }
  
  .ml-#{$i} {
    margin-left: #{$i * 4}px;
  }
  
  .p-#{$i} {
    padding: #{$i * 4}px;
  }
  
  .pt-#{$i} {
    padding-top: #{$i * 4}px;
  }
  
  .pr-#{$i} {
    padding-right: #{$i * 4}px;
  }
  
  .pb-#{$i} {
    padding-bottom: #{$i * 4}px;
  }
  
  .pl-#{$i} {
    padding-left: #{$i * 4}px;
  }
}

// 响应式工具类
@media (max-width: 768px) {
  .hidden-mobile {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .hidden-desktop {
    display: none !important;
  }
}

// 动画类
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(20px);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-20px);
  opacity: 0;
}

// 暗色主题适配
.dark {
  ::-webkit-scrollbar-track {
    background: var(--el-bg-color-page);
  }
  
  ::-webkit-scrollbar-thumb {
    background: var(--el-border-color-darker);
    
    &:hover {
      background: var(--el-border-color-dark);
    }
  }
}
