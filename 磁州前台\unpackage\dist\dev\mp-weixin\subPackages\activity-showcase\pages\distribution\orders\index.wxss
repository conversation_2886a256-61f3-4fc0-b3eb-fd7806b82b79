/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-8fc55181, html.data-v-8fc55181, #app.data-v-8fc55181, .index-container.data-v-8fc55181 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.orders-container.data-v-8fc55181 {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(180deg, #F2F2F7 0%, #E8E8ED 100%);
}
.custom-navbar.data-v-8fc55181 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  z-index: 100;
}
.custom-navbar .navbar-bg.data-v-8fc55181 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%);
}
.custom-navbar .navbar-content.data-v-8fc55181 {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 62px;
  margin-top: var(--status-bar-height, 25px);
  padding: 0 30rpx;
}
.custom-navbar .navbar-content .back-btn.data-v-8fc55181, .custom-navbar .navbar-content .search-btn.data-v-8fc55181 {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
}
.custom-navbar .navbar-content .back-btn .icon.data-v-8fc55181, .custom-navbar .navbar-content .search-btn .icon.data-v-8fc55181 {
  width: 48rpx;
  height: 48rpx;
}
.custom-navbar .navbar-content .navbar-title.data-v-8fc55181 {
  font-size: 36rpx;
  font-weight: 600;
  color: #FFFFFF;
}
.custom-navbar .navbar-content .navbar-right.data-v-8fc55181 {
  width: 80rpx;
  display: flex;
  justify-content: flex-end;
}
.content.data-v-8fc55181 {
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
  padding: 30rpx;
  flex: 1;
}
.stats-card .stats-grid.data-v-8fc55181 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}
.stats-card .stats-grid .stat-item.data-v-8fc55181 {
  text-align: center;
}
.stats-card .stats-grid .stat-item .stat-number.data-v-8fc55181 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #FFFFFF;
  margin-bottom: 8rpx;
}
.stats-card .stats-grid .stat-item .stat-label.data-v-8fc55181 {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}
.filter-tabs .tabs-scroll.data-v-8fc55181 {
  width: 100%;
}
.filter-tabs .tabs-scroll .tabs-container.data-v-8fc55181 {
  display: flex;
  white-space: nowrap;
}
.filter-tabs .tabs-scroll .tabs-container .tab-item.data-v-8fc55181 {
  position: relative;
  padding: 20rpx 30rpx;
  margin-right: 20rpx;
}
.filter-tabs .tabs-scroll .tabs-container .tab-item text.data-v-8fc55181 {
  font-size: 28rpx;
  color: #8E8E93;
  transition: color 0.3s ease;
}
.filter-tabs .tabs-scroll .tabs-container .tab-item .tab-indicator.data-v-8fc55181 {
  position: absolute;
  bottom: 10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  border-radius: 2rpx;
  background: linear-gradient(90deg, #AC39FF 0%, #B87AFF 100%);
}
.filter-tabs .tabs-scroll .tabs-container .tab-item.active text.data-v-8fc55181 {
  color: #AC39FF;
  font-weight: 500;
}
.order-card .order-header.data-v-8fc55181 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}
.order-card .order-header .order-info .order-number.data-v-8fc55181 {
  display: block;
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.order-card .order-header .order-info .order-time.data-v-8fc55181 {
  font-size: 24rpx;
  color: #8E8E93;
}
.order-card .order-header .order-status text.data-v-8fc55181 {
  font-size: 24rpx;
  font-weight: 500;
}
.order-card .product-info.data-v-8fc55181 {
  display: flex;
  margin-bottom: 20rpx;
}
.order-card .product-info .product-image.data-v-8fc55181 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
  margin-right: 20rpx;
}
.order-card .product-info .product-details.data-v-8fc55181 {
  flex: 1;
}
.order-card .product-info .product-details .product-name.data-v-8fc55181 {
  display: block;
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 12rpx;
  line-height: 1.4;
}
.order-card .product-info .product-details .product-specs.data-v-8fc55181 {
  margin-bottom: 12rpx;
}
.order-card .product-info .product-details .product-specs .product-spec.data-v-8fc55181 {
  font-size: 24rpx;
  color: #8E8E93;
}
.order-card .product-info .product-details .price-quantity.data-v-8fc55181 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.order-card .product-info .product-details .price-quantity .product-price.data-v-8fc55181 {
  font-size: 32rpx;
  color: #FF3B69;
  font-weight: 600;
}
.order-card .product-info .product-details .price-quantity .product-quantity.data-v-8fc55181 {
  font-size: 24rpx;
  color: #8E8E93;
}
.order-card .order-footer.data-v-8fc55181 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-top: 1rpx solid #F0F0F0;
  margin-bottom: 20rpx;
}
.order-card .order-footer .customer-info .customer-label.data-v-8fc55181, .order-card .order-footer .customer-info .commission-label.data-v-8fc55181, .order-card .order-footer .commission-info .customer-label.data-v-8fc55181, .order-card .order-footer .commission-info .commission-label.data-v-8fc55181 {
  font-size: 24rpx;
  color: #8E8E93;
}
.order-card .order-footer .customer-info .customer-name.data-v-8fc55181, .order-card .order-footer .commission-info .customer-name.data-v-8fc55181 {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
}
.order-card .order-footer .customer-info .commission-amount.data-v-8fc55181, .order-card .order-footer .commission-info .commission-amount.data-v-8fc55181 {
  font-size: 28rpx;
  color: #AC39FF;
  font-weight: 600;
}
.order-card .order-actions.data-v-8fc55181 {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}
.order-card .order-actions .action-btn text.data-v-8fc55181 {
  font-size: 26rpx;
  font-weight: 500;
}
.empty-state .empty-content .empty-icon.data-v-8fc55181 {
  margin-bottom: 30rpx;
}
.empty-state .empty-content .empty-text.data-v-8fc55181 {
  display: block;
  font-size: 28rpx;
  color: #8E8E93;
  margin-bottom: 20rpx;
}
.empty-state .empty-content .empty-action text.data-v-8fc55181 {
  font-size: 28rpx;
  font-weight: 500;
}