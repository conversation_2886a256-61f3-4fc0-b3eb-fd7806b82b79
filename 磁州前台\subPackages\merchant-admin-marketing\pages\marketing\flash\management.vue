<template>
  <view class="flash-management-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-left">
        <view class="back-button" @tap="goBack">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 18L9 12L15 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </view>
      </view>
      <view class="navbar-title">
        <text class="title-text">限时秒杀管理</text>
      </view>
      <view class="navbar-right">
        <!-- 与营销中心保持一致 -->
      </view>
    </view>
    
    <!-- 顶部操作区 -->
    <view class="top-actions">
      <CreateButton text="创建秒杀活动" theme="flash" @click="createFlashSale" />
    </view>
    
    <!-- 页面内容区域 -->
    <scroll-view scroll-y class="content-area">
      <!-- 数据概览区域 -->
      <view class="overview-section">
        <view class="overview-header">
          <text class="section-title">数据概览</text>
          <view class="date-picker">
            <text class="date-text">近30天</text>
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <view class="overview-cards">
          <view class="overview-card">
            <view class="card-value">{{flashData.totalSales}}</view>
            <view class="card-label">总销售额 (元)</view>
          </view>
          <view class="overview-card">
            <view class="card-value">{{flashData.totalOrders}}</view>
            <view class="card-label">累计订单数</view>
          </view>
          <view class="overview-card">
            <view class="card-value">{{flashData.conversionRate}}%</view>
            <view class="card-label">平均转化率</view>
          </view>
          <view class="overview-card">
            <view class="card-value">{{flashData.avgOrderValue}}</view>
            <view class="card-label">客单价 (元)</view>
          </view>
        </view>
      </view>
      
      <!-- 秒杀活动管理 -->
      <view class="flash-sales-section">
        <view class="section-header">
          <text class="section-title">秒杀活动</text>
          <view class="filter-buttons">
            <view class="filter-button" :class="{ active: activeFilter === 'all' }" @tap="setFilter('all')">全部</view>
            <view class="filter-button" :class="{ active: activeFilter === 'active' }" @tap="setFilter('active')">进行中</view>
            <view class="filter-button" :class="{ active: activeFilter === 'upcoming' }" @tap="setFilter('upcoming')">未开始</view>
            <view class="filter-button" :class="{ active: activeFilter === 'ended' }" @tap="setFilter('ended')">已结束</view>
          </view>
        </view>
        
        <!-- 秒杀活动列表 -->
        <view class="flash-sales-list" v-if="filteredActivities.length > 0">
          <view class="flash-sale-item" v-for="(item, index) in filteredActivities" :key="index" @tap="viewFlashDetail(item)">
            <view class="item-main">
              <view class="item-image-container">
                <image class="item-image" :src="item.image" mode="aspectFill"></image>
                <view class="item-status" :class="item.statusClass">{{item.statusText}}</view>
              </view>
              <view class="item-content">
                <view class="item-name">{{item.name}}</view>
                <view class="item-price">
                  <text class="price-now">¥{{item.flashPrice}}</text>
                  <text class="price-original">¥{{item.originalPrice}}</text>
                </view>
                <view class="item-stats">
                  <view class="stat-item">
                    <text class="stat-value">{{item.stockSold}}/{{item.stockTotal}}</text>
                    <text class="stat-label">已售/总库存</text>
                  </view>
                  <view class="stat-item">
                    <text class="stat-value">{{item.viewCount}}</text>
                    <text class="stat-label">浏览量</text>
                  </view>
                </view>
                <view class="item-time">{{item.timeRange}}</view>
              </view>
            </view>
            <view class="item-actions">
              <view class="action-button edit" @tap.stop="editFlashSale(item)">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="#5E5CE6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M18.5 2.50001C18.8978 2.10219 19.4374 1.87869 20 1.87869C20.5626 1.87869 21.1022 2.10219 21.5 2.50001C21.8978 2.89784 22.1213 3.4374 22.1213 4.00001C22.1213 4.56262 21.8978 5.10219 21.5 5.50001L12 15L8 16L9 12L18.5 2.50001Z" stroke="#5E5CE6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </view>
              <view class="action-button share" @tap.stop="shareFlashSale(item)">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M18 8C19.6569 8 21 6.65685 21 5C21 3.34315 19.6569 2 18 2C16.3431 2 15 3.34315 15 5C15 6.65685 16.3431 8 18 8Z" stroke="#34C759" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M6 15C7.65685 15 9 13.6569 9 12C9 10.3431 7.65685 9 6 9C4.34315 9 3 10.3431 3 12C3 13.6569 4.34315 15 6 15Z" stroke="#34C759" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M18 22C19.6569 22 21 20.6569 21 19C21 17.3431 19.6569 16 18 16C16.3431 16 15 17.3431 15 19C15 20.6569 16.3431 22 18 22Z" stroke="#34C759" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M8.59 13.51L15.42 17.49" stroke="#34C759" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M15.41 6.51L8.59 10.49" stroke="#34C759" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </view>
              <view class="action-button delete" @tap.stop="deleteFlashSale(item)">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M3 6H5H21" stroke="#FF3B30" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="#FF3B30" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-state" v-if="filteredActivities.length === 0">
          <image class="empty-image" src="/static/images/empty-flash.png" mode="aspectFit"></image>
          <text class="empty-text">暂无{{filterLabels[activeFilter]}}秒杀活动</text>
          <view class="empty-desc">秒杀活动是限时限量特价促销的有效方式</view>
        </view>
      </view>
      
      <!-- 秒杀数据分析 -->
      <view class="analytics-section" v-if="filteredActivities.length > 0">
        <view class="section-header">
          <text class="section-title">销售趋势</text>
          <view class="time-selector">
            <text class="time-option" :class="{ active: activeTimeRange === '7d' }" @tap="setTimeRange('7d')">7天</text>
            <text class="time-option" :class="{ active: activeTimeRange === '30d' }" @tap="setTimeRange('30d')">30天</text>
            <text class="time-option" :class="{ active: activeTimeRange === '90d' }" @tap="setTimeRange('90d')">90天</text>
          </view>
        </view>
        
        <view class="analytics-chart">
          <view class="chart-legend">
            <view class="legend-item">
              <view class="legend-color sales"></view>
              <text class="legend-text">销售额</text>
            </view>
            <view class="legend-item">
              <view class="legend-color orders"></view>
              <text class="legend-text">订单量</text>
            </view>
          </view>
          
          <view class="chart-container">
            <!-- 简化的图表，实际项目中可以使用echarts等图表库 -->
            <view class="chart-bars">
              <view class="chart-bar" v-for="(item, index) in chartData" :key="index">
                <view class="bar-group">
                  <view class="bar sales" :style="{ height: item.salesHeight + '%' }"></view>
                  <view class="bar orders" :style="{ height: item.ordersHeight + '%' }"></view>
                </view>
                <text class="bar-label">{{item.date}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 秒杀活动技巧 -->
      <view class="tips-section" v-if="filteredActivities.length === 0">
        <view class="section-header">
          <text class="section-title">秒杀活动技巧</text>
        </view>
        
        <view class="tips-list">
          <view class="tip-item">
            <view class="tip-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="#FF7600" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 16V12" stroke="#FF7600" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 8H12.01" stroke="#FF7600" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </view>
            <view class="tip-content">
              <view class="tip-title">选择热门时段</view>
              <view class="tip-desc">选择用户活跃度高的时段发布秒杀活动，如12:00-13:00或18:00-20:00</view>
            </view>
          </view>
          <view class="tip-item">
            <view class="tip-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="#FF7600" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </view>
            <view class="tip-content">
              <view class="tip-title">明确价格优势</view>
              <view class="tip-desc">秒杀商品价格应明显低于原价，建议达到5-7折的优惠幅度</view>
            </view>
          </view>
          <view class="tip-item">
            <view class="tip-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8 7V3M16 7V3M7 11H17M5 21H19C20.1046 21 21 20.1046 21 19V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V19C3 20.1046 3.89543 21 5 21Z" stroke="#FF7600" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </view>
            <view class="tip-content">
              <view class="tip-title">提前预热宣传</view>
              <view class="tip-desc">秒杀活动发布前1-3天进行预热宣传，增加用户关注度</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 底部空间 -->
      <view class="bottom-space"></view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import CreateButton from '/subPackages/merchant-admin-marketing/components/CreateButton.vue';

// 数据概览
const flashData = reactive({
  totalSales: '15,632.80',
  totalOrders: '342',
  conversionRate: '28.6',
  avgOrderValue: '45.8'
});

// 筛选相关
const activeFilter = ref('all');
const filterLabels = {
  all: '全部',
  active: '进行中',
  upcoming: '未开始',
  ended: '已结束'
};

// 秒杀活动数据
const flashActivities = ref([
  {
    id: 1,
    name: '夏季清凉风扇特惠',
    flashPrice: 59.9,
    originalPrice: 129.9,
    stockTotal: 100,
    stockSold: 86,
    viewCount: 1564,
    image: '/static/images/flash-item1.jpg',
    timeRange: '2023-07-01 12:00 ~ 14:00',
    status: 'active',
    statusText: '进行中',
    statusClass: 'status-active'
  },
  {
    id: 2,
    name: '夏季新品T恤限时秒杀',
    flashPrice: 39.9,
    originalPrice: 99.9,
    stockTotal: 200,
    stockSold: 143,
    viewCount: 2238,
    image: '/static/images/flash-item2.jpg',
    timeRange: '2023-07-02 18:00 ~ 20:00',
    status: 'active',
    statusText: '进行中',
    statusClass: 'status-active'
  },
  {
    id: 3,
    name: '智能手环超值特惠',
    flashPrice: 89.9,
    originalPrice: 199.9,
    stockTotal: 50,
    stockSold: 0,
    viewCount: 562,
    image: '/static/images/flash-item3.jpg',
    timeRange: '2023-07-05 12:00 ~ 14:00',
    status: 'upcoming',
    statusText: '未开始',
    statusClass: 'status-upcoming'
  },
  {
    id: 4,
    name: '厨房小家电秒杀',
    flashPrice: 129.9,
    originalPrice: 299.9,
    stockTotal: 80,
    stockSold: 80,
    viewCount: 1876,
    image: '/static/images/flash-item4.jpg',
    timeRange: '2023-06-25 18:00 ~ 20:00',
    status: 'ended',
    statusText: '已结束',
    statusClass: 'status-ended'
  },
  {
    id: 5,
    name: '儿童玩具特惠',
    flashPrice: 49.9,
    originalPrice: 99.9,
    stockTotal: 150,
    stockSold: 98,
    viewCount: 1245,
    image: '/static/images/flash-item5.jpg',
    timeRange: '2023-06-20 12:00 ~ 14:00',
    status: 'ended',
    statusText: '已结束',
    statusClass: 'status-ended'
  }
]);

// 图表数据
const activeTimeRange = ref('30d');
const chartData = ref([
  { date: '06-01', sales: 1200, orders: 28, salesHeight: 40, ordersHeight: 35 },
  { date: '06-05', sales: 1850, orders: 42, salesHeight: 61.7, ordersHeight: 52.5 },
  { date: '06-10', sales: 1500, orders: 35, salesHeight: 50, ordersHeight: 43.75 },
  { date: '06-15', sales: 2400, orders: 56, salesHeight: 80, ordersHeight: 70 },
  { date: '06-20', sales: 3000, orders: 80, salesHeight: 100, ordersHeight: 100 },
  { date: '06-25', sales: 2200, orders: 52, salesHeight: 73.3, ordersHeight: 65 },
  { date: '06-30', sales: 1800, orders: 40, salesHeight: 60, ordersHeight: 50 }
]);

// 根据筛选条件过滤活动
const filteredActivities = computed(() => {
  if (activeFilter.value === 'all') {
    return flashActivities.value;
  }
  return flashActivities.value.filter(item => item.status === activeFilter.value);
});

// 设置筛选条件
const setFilter = (filter) => {
  activeFilter.value = filter;
};

// 设置时间范围
const setTimeRange = (range) => {
  activeTimeRange.value = range;
  // 在实际应用中，这里应该根据选择的时间范围重新加载图表数据
  loadChartData(range);
};

// 加载图表数据
const loadChartData = (range) => {
  // 模拟不同时间范围的数据
  if (range === '7d') {
    chartData.value = [
      { date: '06-24', sales: 1200, orders: 28, salesHeight: 40, ordersHeight: 35 },
      { date: '06-25', sales: 1850, orders: 42, salesHeight: 61.7, ordersHeight: 52.5 },
      { date: '06-26', sales: 1500, orders: 35, salesHeight: 50, ordersHeight: 43.75 },
      { date: '06-27', sales: 2400, orders: 56, salesHeight: 80, ordersHeight: 70 },
      { date: '06-28', sales: 3000, orders: 80, salesHeight: 100, ordersHeight: 100 },
      { date: '06-29', sales: 2200, orders: 52, salesHeight: 73.3, ordersHeight: 65 },
      { date: '06-30', sales: 1800, orders: 40, salesHeight: 60, ordersHeight: 50 }
    ];
  } else if (range === '30d') {
    chartData.value = [
      { date: '06-01', sales: 1200, orders: 28, salesHeight: 40, ordersHeight: 35 },
      { date: '06-05', sales: 1850, orders: 42, salesHeight: 61.7, ordersHeight: 52.5 },
      { date: '06-10', sales: 1500, orders: 35, salesHeight: 50, ordersHeight: 43.75 },
      { date: '06-15', sales: 2400, orders: 56, salesHeight: 80, ordersHeight: 70 },
      { date: '06-20', sales: 3000, orders: 80, salesHeight: 100, ordersHeight: 100 },
      { date: '06-25', sales: 2200, orders: 52, salesHeight: 73.3, ordersHeight: 65 },
      { date: '06-30', sales: 1800, orders: 40, salesHeight: 60, ordersHeight: 50 }
    ];
  } else if (range === '90d') {
    chartData.value = [
      { date: '04-01', sales: 1000, orders: 22, salesHeight: 33.3, ordersHeight: 27.5 },
      { date: '04-15', sales: 1400, orders: 32, salesHeight: 46.7, ordersHeight: 40 },
      { date: '05-01', sales: 1700, orders: 38, salesHeight: 56.7, ordersHeight: 47.5 },
      { date: '05-15', sales: 2100, orders: 48, salesHeight: 70, ordersHeight: 60 },
      { date: '06-01', sales: 2500, orders: 58, salesHeight: 83.3, ordersHeight: 72.5 },
      { date: '06-15', sales: 2800, orders: 65, salesHeight: 93.3, ordersHeight: 81.25 },
      { date: '06-30', sales: 3000, orders: 80, salesHeight: 100, ordersHeight: 100 }
    ];
  }
};

// 查看秒杀活动详情
const viewFlashDetail = (item) => {
  uni.navigateTo({
    url: `/subPackages/merchant-admin-marketing/pages/marketing/flash/detail?id=${item.id}`
  });
};

// 编辑秒杀活动
const editFlashSale = (item) => {
      uni.navigateTo({
    url: `/subPackages/merchant-admin-marketing/pages/marketing/flash/edit?id=${item.id}`
  });
};

// 分享秒杀活动
const shareFlashSale = (item) => {
  uni.showToast({
    title: '生成分享链接中...',
    icon: 'loading',
    duration: 1500
  });
  
  setTimeout(() => {
    uni.showModal({
      title: '分享秒杀活动',
      content: `活动"${item.name}"的分享链接已创建，可发送给客户或分享到社交媒体`,
      confirmText: '复制链接',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 实际应用中这里应该有真实的分享链接
          uni.setClipboardData({
            data: `https://example.com/flash-sale/${item.id}`,
            success: () => {
              uni.showToast({
                title: '链接已复制',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  }, 1500);
};

// 删除秒杀活动
const deleteFlashSale = (item) => {
  uni.showModal({
    title: '确认删除',
    content: `确定要删除"${item.name}"秒杀活动吗？一旦删除将无法恢复。`,
    confirmText: '删除',
    confirmColor: '#FF3B30',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        // 在实际应用中这里应该调用API删除活动
        const index = flashActivities.value.findIndex(i => i.id === item.id);
        if (index !== -1) {
          flashActivities.value.splice(index, 1);
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          });
        }
      }
    }
  });
};

// 创建秒杀活动
const createFlashSale = () => {
  uni.navigateTo({
    url: '/subPackages/merchant-admin-marketing/pages/marketing/flash/create'
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 页面加载
onMounted(() => {
  console.log('限时秒杀管理页面已加载');
  // 加载秒杀活动数据（实际应用中应该从API获取）
});
</script>

<style lang="scss">
/* 页面容器 */
.flash-management-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
  position: relative;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  color: white;
  padding: 48px 20px 16px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(255, 120, 0, 0.15);
}

.navbar-left {
  width: 40px;
}

.back-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-title {
  flex: 1;
  text-align: center;
}

.title-text {
  font-size: 18px;
  font-weight: 600;
}

.navbar-right {
  width: 40px;
}

/* 顶部操作区 */
.top-actions {
  padding: 16px;
  display: flex;
  justify-content: flex-end;
}

/* 内容区域 */
.content-area {
  flex: 1;
  padding: 16px;
  box-sizing: border-box;
  height: calc(100vh - 80px);
}

/* 底部空间 */
.bottom-space {
  height: 20px;
}

/* 概览区域 */
.overview-section {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 17px;
  font-weight: 600;
  color: #333333;
}

.date-picker {
  display: flex;
  align-items: center;
  background: rgba(255, 120, 0, 0.08);
  padding: 4px 8px;
  border-radius: 16px;
}

.date-text {
  font-size: 14px;
  color: #FF7600;
  margin-right: 4px;
}

.arrow-icon {
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 5px solid #FF7600;
}

.overview-cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.overview-card {
  width: 48%;
  background: #F9F9F9;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
}

.card-value {
  font-size: 20px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 4px;
}

.card-label {
  font-size: 12px;
  color: #666666;
}

/* 秒杀活动管理 */
.flash-sales-section {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.filter-buttons {
  display: flex;
  overflow-x: auto;
  margin-top: 8px;
  width: 100%;
}

.filter-button {
  padding: 6px 12px;
  margin-right: 8px;
  border-radius: 16px;
  font-size: 14px;
  color: #666666;
  background: #F5F5F5;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.filter-button.active {
  background: #FF7600;
  color: white;
}

/* 秒杀活动列表 */
.flash-sales-list {
  margin-top: 8px;
}

.flash-sale-item {
  background: #FFFFFF;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #EEEEEE;
}

.item-main {
  display: flex;
}

.item-image-container {
  position: relative;
  width: 80px;
  height: 80px;
  margin-right: 12px;
  overflow: hidden;
  border-radius: 4px;
}

.item-image {
  width: 100%;
  height: 100%;
}

.item-status {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 0 0 0 8px;
  color: white;
  z-index: 2;
}

.status-active {
  background: #34C759;
}

.status-upcoming {
  background: #007AFF;
}

.status-ended {
  background: #8E8E93;
}

.item-content {
  flex: 1;
}

.item-name {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 6px;
  line-height: 1.3;
}

.item-price {
  margin-bottom: 6px;
}

.price-now {
  font-size: 17px;
  font-weight: 600;
  color: #FF3B30;
  margin-right: 8px;
}

.price-original {
  font-size: 14px;
  color: #999999;
  text-decoration: line-through;
}

.item-stats {
  display: flex;
  margin-bottom: 6px;
}

.stat-item {
  margin-right: 16px;
}

.stat-value {
  font-size: 14px;
  color: #333333;
  margin-right: 4px;
}

.stat-label {
  font-size: 12px;
  color: #999999;
}

.item-time {
  font-size: 12px;
  color: #666666;
}

.item-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
  border-top: 1px solid #F5F5F5;
  padding-top: 12px;
}

.action-button {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 12px;
  background: #F5F5F5;
}

.action-button.edit {
  background: rgba(94, 92, 230, 0.1);
}

.action-button.share {
  background: rgba(52, 199, 89, 0.1);
}

.action-button.delete {
  background: rgba(255, 59, 48, 0.1);
}

/* 空状态 */
.empty-state {
  padding: 32px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-image {
  width: 100px;
  height: 100px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 17px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: #666666;
  text-align: center;
  margin-bottom: 20px;
}

/* 数据分析 */
.analytics-section {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.time-selector {
  display: flex;
  margin-top: 8px;
  background: #F5F5F5;
  border-radius: 16px;
  overflow: hidden;
  align-self: flex-end;
}

.time-option {
  padding: 6px 12px;
  font-size: 14px;
  color: #666666;
  text-align: center;
}

.time-option.active {
  background: #FF7600;
  color: white;
}

.analytics-chart {
  padding: 12px 0;
}

.chart-legend {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin: 0 16px;
}

.legend-color {
  width: 16px;
  height: 8px;
  border-radius: 4px;
  margin-right: 8px;
}

.legend-color.sales {
  background: #FF7600;
}

.legend-color.orders {
  background: #007AFF;
}

.legend-text {
  font-size: 12px;
  color: #666666;
}

.chart-container {
  height: 200px;
  padding: 16px 0;
}

.chart-bars {
  display: flex;
  align-items: flex-end;
  height: 150px;
  justify-content: space-between;
  padding: 0 8px;
}

.chart-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 14%;
}

.bar-group {
  display: flex;
  width: 100%;
  height: 150px;
  align-items: flex-end;
  justify-content: center;
}

.bar {
  border-radius: 2px;
}

.bar.sales {
  width: 12px;
  background: #FF7600;
  margin-right: 4px;
}

.bar.orders {
  width: 12px;
  background: #007AFF;
}

.bar-label {
  font-size: 10px;
  color: #999999;
  margin-top: 8px;
}

/* 秒杀活动技巧 */
.tips-section {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.tips-list {
  margin-top: 8px;
}

.tip-item {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid #F5F5F5;
}

.tip-item:last-child {
  border-bottom: none;
}

.tip-icon {
  width: 36px;
  height: 36px;
  margin-right: 12px;
  color: #FF7600;
}

.tip-content {
  flex: 1;
}

.tip-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 4px;
}

.tip-desc {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
}

@media screen and (min-width: 768px) {
  .overview-card {
    width: 23%;
  }
  
  .filter-buttons {
    width: auto;
    margin-top: 0;
  }
}
</style>
