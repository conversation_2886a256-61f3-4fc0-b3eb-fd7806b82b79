{"name": "cssesc", "version": "2.0.0", "description": "A JavaScript library for escaping CSS strings and identifiers while generating the shortest possible ASCII-only output.", "homepage": "https://mths.be/cssesc", "engines": {"node": ">=4"}, "main": "cssesc.js", "bin": "bin/cssesc", "man": "man/cssesc.1", "keywords": ["css", "escape", "identifier", "string", "tool"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/cssesc.git"}, "bugs": "https://github.com/mathiasbynens/cssesc/issues", "files": ["LICENSE-MIT.txt", "cssesc.js", "bin/", "man/"], "scripts": {"build": "grunt template && babel cssesc.js -o cssesc.js", "test": "mocha tests", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-env": "^1.6.1", "codecov": "^1.0.1", "grunt": "^1.0.1", "grunt-template": "^1.0.0", "istanbul": "^0.4.4", "mocha": "^2.5.3", "regenerate": "^1.2.1", "requirejs": "^2.1.16"}}