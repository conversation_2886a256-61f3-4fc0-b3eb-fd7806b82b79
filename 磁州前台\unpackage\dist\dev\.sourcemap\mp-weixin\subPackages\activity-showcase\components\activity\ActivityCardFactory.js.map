{"version": 3, "file": "ActivityCardFactory.js", "sources": ["subPackages/activity-showcase/components/activity/ActivityCardFactory.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7Avc3ViUGFja2FnZXMvYWN0aXZpdHktc2hvd2Nhc2UvY29tcG9uZW50cy9hY3Rpdml0eS9BY3Rpdml0eUNhcmRGYWN0b3J5LnZ1ZQ"], "sourcesContent": ["<template>\n  <!-- 活动卡片工厂组件 - 根据活动类型动态渲染不同卡片 -->\n  <view class=\"activity-card-wrapper\" @tap=\"navigateToDetail\">\n    <!-- 使用条件渲染选择合适的卡片组件 -->\n    <GroupBuyCard \n      v-if=\"cardType === 'GroupBuyCard'\" \n      :item=\"item\"\n      @favorite=\"handleFavorite\"\n      @action=\"handleAction\"\n    />\n    <FlashSaleCard \n      v-else-if=\"cardType === 'FlashSaleCard'\" \n      :item=\"item\"\n      @favorite=\"handleFavorite\"\n      @action=\"handleAction\"\n    />\n    <CouponCard \n      v-else-if=\"cardType === 'CouponCard'\" \n      :item=\"item\"\n      @favorite=\"handleFavorite\"\n      @action=\"handleAction\"\n    />\n    <DiscountCard \n      v-else-if=\"cardType === 'DiscountCard'\" \n      :item=\"item\"\n      @favorite=\"handleFavorite\"\n      @action=\"handleAction\"\n    />\n    <ActivityCard \n      v-else \n      :item=\"item\"\n      @favorite=\"handleFavorite\"\n      @action=\"handleAction\"\n    />\n  </view>\n</template>\n\n<script setup>\nimport { computed } from 'vue';\nimport ActivityCard from './ActivityCard.vue';\nimport GroupBuyCard from './GroupBuyCard.vue';\nimport FlashSaleCard from './FlashSaleCard/index.vue';\nimport CouponCard from './CouponCard/index.vue';\nimport DiscountCard from './DiscountCard/index.vue';\n\nconst props = defineProps({\n  item: {\n    type: Object,\n    required: true\n  }\n});\n\nconst emit = defineEmits(['navigate', 'favorite', 'action']);\n\n// 根据活动类型确定使用哪种卡片组件\nconst cardType = computed(() => {\n  const typeMap = {\n    'groupBuy': 'GroupBuyCard',\n    'flashSale': 'FlashSaleCard',\n    'coupon': 'CouponCard',\n    'discount': 'DiscountCard'\n  };\n  \n  return typeMap[props.item.type] || 'ActivityCard';\n});\n\n// 跳转到活动详情页\nfunction navigateToDetail() {\n  console.log('点击活动卡片，准备跳转到详情页', props.item.id, props.item.type);\n  emit('navigate', {\n    id: props.item.id,\n    type: props.item.type\n  });\n}\n\n// 处理收藏事件\nfunction handleFavorite(id) {\n  emit('favorite', id);\n}\n\n// 处理操作按钮点击事件\nfunction handleAction(data) {\n  emit('action', data);\n}\n</script>\n\n<style scoped>\n.activity-card-wrapper {\n  cursor: pointer;\n  transition: transform 0.2s ease;\n  position: relative;\n  z-index: 1;\n}\n\n.activity-card-wrapper:active {\n  transform: scale(0.98);\n}\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/components/activity/ActivityCardFactory.vue'\nwx.createComponent(Component)"], "names": ["computed", "uni"], "mappings": ";;;;;AAuCA,MAAM,eAAe,MAAW;AAChC,MAAM,eAAe,MAAW;AAChC,MAAM,gBAAgB,MAAW;AACjC,MAAM,aAAa,MAAW;AAC9B,MAAM,eAAe,MAAW;;;;;;;;;;;AAEhC,UAAM,QAAQ;AAOd,UAAM,OAAO;AAGb,UAAM,WAAWA,cAAQ,SAAC,MAAM;AAC9B,YAAM,UAAU;AAAA,QACd,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,UAAU;AAAA,QACV,YAAY;AAAA,MAChB;AAEE,aAAO,QAAQ,MAAM,KAAK,IAAI,KAAK;AAAA,IACrC,CAAC;AAGD,aAAS,mBAAmB;AAC1BC,oBAAAA,MAAY,MAAA,OAAA,mFAAA,mBAAmB,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI;AAC7D,WAAK,YAAY;AAAA,QACf,IAAI,MAAM,KAAK;AAAA,QACf,MAAM,MAAM,KAAK;AAAA,MACrB,CAAG;AAAA,IACH;AAGA,aAAS,eAAe,IAAI;AAC1B,WAAK,YAAY,EAAE;AAAA,IACrB;AAGA,aAAS,aAAa,MAAM;AAC1B,WAAK,UAAU,IAAI;AAAA,IACrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClFA,GAAG,gBAAgB,SAAS;"}