"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const CustomNavbar = () => "../../components/CustomNavbar.js";
const ProductCard = () => "../../components/ProductCard.js";
const _sfc_main = {
  components: {
    CustomNavbar,
    ProductCard
  },
  data() {
    return {
      searchKeyword: "",
      searched: false,
      loading: false,
      hasMore: true,
      page: 1,
      sortBy: "default",
      sortOrder: "asc",
      showPriceCompare: false,
      expandedProducts: [],
      hotSearchTags: ["iPhone 15", "华为Mate60", "小米14", "笔记本电脑", "耳机", "电视"],
      searchHistory: [],
      searchResults: []
    };
  },
  computed: {
    sortedSearchResults() {
      if (!this.searchResults.length)
        return [];
      let results = [...this.searchResults];
      if (this.sortBy === "default") {
        return results;
      } else if (this.sortBy === "price") {
        return this.sortOrder === "asc" ? results.sort((a, b) => parseFloat(a.price) - parseFloat(b.price)) : results.sort((a, b) => parseFloat(b.price) - parseFloat(a.price));
      } else if (this.sortBy === "cashback") {
        return this.sortOrder === "asc" ? results.sort((a, b) => parseFloat(a.cashback) - parseFloat(b.cashback)) : results.sort((a, b) => parseFloat(b.cashback) - parseFloat(a.cashback));
      }
      return results;
    }
  },
  onLoad(options) {
    common_vendor.index.setNavigationBarColor({
      frontColor: "#ffffff",
      backgroundColor: "#9C27B0"
    });
    this.getSearchHistory();
    if (options.keyword) {
      this.searchKeyword = decodeURIComponent(options.keyword);
      this.searchProducts();
    }
  },
  methods: {
    // 搜索商品
    searchProducts() {
      if (!this.searchKeyword.trim())
        return;
      this.searched = true;
      this.loading = true;
      this.page = 1;
      this.searchResults = [];
      this.hasMore = true;
      this.saveSearchHistory(this.searchKeyword);
      setTimeout(() => {
        this.searchResults = this.getMockSearchResults();
        this.loading = false;
        this.searchResults.forEach((product) => {
          const prices = product.platforms.map((p) => parseFloat(p.price));
          product.minPrice = Math.min(...prices).toFixed(2);
          product.maxPrice = Math.max(...prices).toFixed(2);
        });
      }, 1e3);
    },
    // 加载更多搜索结果
    loadMoreResults() {
      if (!this.hasMore || this.loading)
        return;
      this.loading = true;
      this.page++;
      setTimeout(() => {
        if (this.page > 2) {
          this.hasMore = false;
        } else {
          const moreResults = this.getMockSearchResults();
          this.searchResults = [...this.searchResults, ...moreResults];
        }
        this.loading = false;
      }, 1e3);
    },
    // 排序搜索结果
    sortResults(type) {
      if (this.sortBy === type) {
        this.sortOrder = this.sortOrder === "asc" ? "desc" : "asc";
      } else {
        this.sortBy = type;
        this.sortOrder = type === "default" ? "asc" : "desc";
      }
    },
    // 切换比价显示
    togglePriceCompare() {
      this.showPriceCompare = !this.showPriceCompare;
    },
    // 切换产品平台列表展开/收起
    toggleProductPlatforms(productId) {
      const index = this.expandedProducts.indexOf(productId);
      if (index > -1) {
        this.expandedProducts.splice(index, 1);
      } else {
        this.expandedProducts.push(productId);
      }
    },
    // 选择标签
    selectTag(tag) {
      this.searchKeyword = tag;
      this.searchProducts();
    },
    // 清空搜索
    clearSearch() {
      this.searchKeyword = "";
      this.searched = false;
      this.searchResults = [];
    },
    // 获取搜索历史
    getSearchHistory() {
      const history = common_vendor.index.getStorageSync("searchHistory");
      if (history) {
        this.searchHistory = JSON.parse(history);
      }
    },
    // 保存搜索历史
    saveSearchHistory(keyword) {
      if (!keyword.trim())
        return;
      const index = this.searchHistory.indexOf(keyword);
      if (index > -1) {
        this.searchHistory.splice(index, 1);
      }
      this.searchHistory.unshift(keyword);
      if (this.searchHistory.length > 10) {
        this.searchHistory = this.searchHistory.slice(0, 10);
      }
      common_vendor.index.setStorageSync("searchHistory", JSON.stringify(this.searchHistory));
    },
    // 清空搜索历史
    clearHistory() {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要清空搜索历史吗？",
        success: (res) => {
          if (res.confirm) {
            this.searchHistory = [];
            common_vendor.index.removeStorageSync("searchHistory");
          }
        }
      });
    },
    // 导航到商品详情
    navigateToDetail(product) {
      common_vendor.index.navigateTo({
        url: `/subPackages/cashback/pages/product-detail/index?id=${product.id}`
      });
    },
    // 导航到平台商品详情
    navigateToPlatformDetail(product, platform) {
      common_vendor.index.navigateTo({
        url: `/subPackages/cashback/pages/product-detail/index?id=${product.id}&platform=${platform.name}`
      });
    },
    // 模拟搜索结果数据
    getMockSearchResults() {
      return [
        {
          id: 1,
          title: "Apple iPhone 15 Pro Max (A2850) 256GB 原色钛金属",
          image: "/static/images/cashback/product-1.png",
          price: "9999.00",
          cashback: "300.00",
          platform: "京东",
          platforms: [
            {
              name: "京东",
              icon: "/static/images/cashback/platform-jd.png",
              price: "9999.00",
              cashback: "300.00"
            },
            {
              name: "天猫",
              icon: "/static/images/cashback/platform-tmall.png",
              price: "9989.00",
              cashback: "280.00"
            },
            {
              name: "苏宁",
              icon: "/static/images/cashback/platform-suning.png",
              price: "10099.00",
              cashback: "290.00"
            }
          ]
        },
        {
          id: 2,
          title: "Apple iPhone 15 Pro 256GB 黑色钛金属",
          image: "/static/images/cashback/product-2.png",
          price: "8999.00",
          cashback: "270.00",
          platform: "天猫",
          platforms: [
            {
              name: "京东",
              icon: "/static/images/cashback/platform-jd.png",
              price: "8999.00",
              cashback: "260.00"
            },
            {
              name: "天猫",
              icon: "/static/images/cashback/platform-tmall.png",
              price: "8989.00",
              cashback: "270.00"
            },
            {
              name: "拼多多",
              icon: "/static/images/cashback/platform-pdd.png",
              price: "8899.00",
              cashback: "220.00"
            }
          ]
        },
        {
          id: 3,
          title: "Apple iPhone 15 128GB 蓝色",
          image: "/static/images/cashback/product-3.png",
          price: "5999.00",
          cashback: "180.00",
          platform: "拼多多",
          platforms: [
            {
              name: "京东",
              icon: "/static/images/cashback/platform-jd.png",
              price: "5999.00",
              cashback: "150.00"
            },
            {
              name: "天猫",
              icon: "/static/images/cashback/platform-tmall.png",
              price: "6099.00",
              cashback: "160.00"
            },
            {
              name: "拼多多",
              icon: "/static/images/cashback/platform-pdd.png",
              price: "5899.00",
              cashback: "180.00"
            }
          ]
        },
        {
          id: 4,
          title: "Apple iPhone 14 Pro Max 256GB 深空黑色",
          image: "/static/images/cashback/product-4.png",
          price: "8499.00",
          cashback: "250.00",
          platform: "京东",
          platforms: [
            {
              name: "京东",
              icon: "/static/images/cashback/platform-jd.png",
              price: "8499.00",
              cashback: "250.00"
            },
            {
              name: "天猫",
              icon: "/static/images/cashback/platform-tmall.png",
              price: "8599.00",
              cashback: "240.00"
            },
            {
              name: "苏宁",
              icon: "/static/images/cashback/platform-suning.png",
              price: "8549.00",
              cashback: "230.00"
            }
          ]
        }
      ];
    }
  },
  onReachBottom() {
    this.loadMoreResults();
  }
};
if (!Array) {
  const _component_custom_navbar = common_vendor.resolveComponent("custom-navbar");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_product_card = common_vendor.resolveComponent("product-card");
  (_component_custom_navbar + _component_path + _component_svg + _component_product_card)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      title: "商品搜索",
      ["show-back"]: true
    }),
    b: common_vendor.p({
      fill: "#999999",
      d: "M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"
    }),
    c: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "20",
      height: "20"
    }),
    d: common_vendor.o((...args) => $options.searchProducts && $options.searchProducts(...args)),
    e: $data.searchKeyword,
    f: common_vendor.o(($event) => $data.searchKeyword = $event.detail.value),
    g: $data.searchKeyword
  }, $data.searchKeyword ? {
    h: common_vendor.p({
      fill: "#CCCCCC",
      d: "M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"
    }),
    i: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "16",
      height: "16"
    }),
    j: common_vendor.o((...args) => $options.clearSearch && $options.clearSearch(...args))
  } : {}, {
    k: !$data.searchKeyword && !$data.searchResults.length
  }, !$data.searchKeyword && !$data.searchResults.length ? {
    l: common_vendor.f($data.hotSearchTags, (tag, index, i0) => {
      return {
        a: common_vendor.t(tag),
        b: index,
        c: common_vendor.o(($event) => $options.selectTag(tag), index)
      };
    })
  } : {}, {
    m: !$data.searchKeyword && !$data.searchResults.length && $data.searchHistory.length > 0
  }, !$data.searchKeyword && !$data.searchResults.length && $data.searchHistory.length > 0 ? {
    n: common_vendor.p({
      fill: "#999999",
      d: "M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19M8,9H16V19H8V9M15.5,4L14.5,3H9.5L8.5,4H5V6H19V4H15.5Z"
    }),
    o: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "16",
      height: "16"
    }),
    p: common_vendor.o((...args) => $options.clearHistory && $options.clearHistory(...args)),
    q: common_vendor.f($data.searchHistory, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: index,
        c: common_vendor.o(($event) => $options.selectTag(item), index)
      };
    })
  } : {}, {
    r: $data.searchResults.length > 0
  }, $data.searchResults.length > 0 ? common_vendor.e({
    s: $data.sortBy === "default" ? 1 : "",
    t: common_vendor.o(($event) => $options.sortResults("default")),
    v: common_vendor.p({
      fill: "currentColor",
      d: "M7,15L12,10L17,15H7Z"
    }),
    w: $data.sortBy === "price" && $data.sortOrder === "asc" ? 1 : "",
    x: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "12",
      height: "12"
    }),
    y: common_vendor.p({
      fill: "currentColor",
      d: "M7,10L12,15L17,10H7Z"
    }),
    z: $data.sortBy === "price" && $data.sortOrder === "desc" ? 1 : "",
    A: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "12",
      height: "12"
    }),
    B: $data.sortBy === "price" ? 1 : "",
    C: common_vendor.o(($event) => $options.sortResults("price")),
    D: common_vendor.p({
      fill: "currentColor",
      d: "M7,15L12,10L17,15H7Z"
    }),
    E: $data.sortBy === "cashback" && $data.sortOrder === "asc" ? 1 : "",
    F: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "12",
      height: "12"
    }),
    G: common_vendor.p({
      fill: "currentColor",
      d: "M7,10L12,15L17,10H7Z"
    }),
    H: $data.sortBy === "cashback" && $data.sortOrder === "desc" ? 1 : "",
    I: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "12",
      height: "12"
    }),
    J: $data.sortBy === "cashback" ? 1 : "",
    K: common_vendor.o(($event) => $options.sortResults("cashback")),
    L: $data.showPriceCompare ? 1 : "",
    M: common_vendor.o((...args) => $options.togglePriceCompare && $options.togglePriceCompare(...args)),
    N: !$data.showPriceCompare
  }, !$data.showPriceCompare ? {
    O: common_vendor.f($options.sortedSearchResults, (product, index, i0) => {
      return {
        a: index,
        b: common_vendor.o(($event) => $options.navigateToDetail(product), index),
        c: "0c65924e-15-" + i0,
        d: common_vendor.p({
          product
        })
      };
    })
  } : {
    P: common_vendor.f($options.sortedSearchResults, (product, index, i0) => {
      return common_vendor.e({
        a: product.image,
        b: common_vendor.t(product.title),
        c: common_vendor.t(product.minPrice),
        d: common_vendor.t(product.maxPrice),
        e: "0c65924e-17-" + i0 + "," + ("0c65924e-16-" + i0),
        f: $data.expandedProducts.includes(product.id) ? 1 : "",
        g: "0c65924e-16-" + i0,
        h: common_vendor.o(($event) => $options.toggleProductPlatforms(product.id), index),
        i: $data.expandedProducts.includes(product.id)
      }, $data.expandedProducts.includes(product.id) ? {
        j: common_vendor.f(product.platforms, (platform, pIndex, i1) => {
          return {
            a: platform.icon,
            b: common_vendor.t(platform.name),
            c: common_vendor.t(platform.price),
            d: common_vendor.t(platform.cashback),
            e: pIndex,
            f: common_vendor.o(($event) => $options.navigateToPlatformDetail(product, platform), pIndex)
          };
        })
      } : {}, {
        k: index
      });
    }),
    Q: common_vendor.p({
      fill: "#999999",
      d: "M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z"
    }),
    R: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24"
    })
  }, {
    S: $data.loading
  }, $data.loading ? {} : {}, {
    T: !$data.loading && !$data.hasMore && $data.searchResults.length > 0
  }, !$data.loading && !$data.hasMore && $data.searchResults.length > 0 ? {} : {}) : {}, {
    U: $data.searched && $data.searchResults.length === 0
  }, $data.searched && $data.searchResults.length === 0 ? {
    V: common_assets._imports_0$53
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-0c65924e"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/cashback/pages/search/index.js.map
