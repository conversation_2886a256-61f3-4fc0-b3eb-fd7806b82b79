<template>
  <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
    <view class="navbar-content">
      <!-- 返回按钮 -->
      <view v-if="showBack" class="navbar-back" @tap="goBack">
        <image class="back-icon" src="/static/images/cashback/back.png" mode="aspectFit" />
      </view>
      <view v-else class="navbar-placeholder"></view>
      
      <!-- 标题 -->
      <view class="navbar-title">
        <text>{{ title }}</text>
      </view>
      
      <!-- 关闭按钮 -->
      <view v-if="showClose" class="navbar-close" @tap="close">
        <svg class="close-icon" viewBox="0 0 24 24" width="20" height="20">
          <path fill="#FFFFFF" d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
        </svg>
      </view>
      <view v-else class="navbar-placeholder"></view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CustomNavbar',
  props: {
    title: {
      type: String,
      default: '返利商城'
    },
    showBack: {
      type: Boolean,
      default: false
    },
    showClose: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      statusBarHeight: 20
    };
  },
  created() {
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight || 20;
  },
  methods: {
    goBack() {
      uni.navigateBack({
        delta: 1,
        fail: () => {
          uni.switchTab({
            url: '/pages/index/index'
          });
        }
      });
    },
    close() {
      // 关闭当前页面，返回上一页面或首页
      uni.navigateBack({
        delta: 1,
        fail: () => {
          uni.switchTab({
            url: '/pages/index/index'
          });
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background: linear-gradient(135deg, #AB47BC 0%, #9C27B0 100%);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  
  .navbar-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 44px;
    padding: 0 16px;
  }
  
  .navbar-back, .navbar-close, .navbar-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 16px;
    
    .back-icon {
      width: 20px;
      height: 20px;
    }
    
    .close-icon {
      color: #FFFFFF;
    }
  }
  
  .navbar-title {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    color: #FFFFFF;
    max-width: 60%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style> 