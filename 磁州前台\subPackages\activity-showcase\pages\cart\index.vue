<template>
  <view class="cart-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M19 12H5M12 19l-7-7 7-7" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        <view class="navbar-title">购物车</view>
        <view class="navbar-right">
          <view class="edit-btn" @click="toggleEditMode">
            {{ isEditMode ? '完成' : '编辑' }}
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view 
      class="content-scroll" 
      scroll-y 
      @scrolltolower="loadMore"
      v-if="cartItems.length > 0"
    >
      <!-- 商家分组 -->
      <view 
        class="shop-group"
        v-for="(shop, shopIndex) in cartShops" 
        :key="shopIndex"
      >
        <!-- 商家信息 -->
        <view class="shop-header">
          <view class="checkbox-wrapper" @click="toggleShopSelect(shop.id)">
            <view class="checkbox" :class="{ checked: isShopSelected(shop.id) }">
              <svg v-if="isShopSelected(shop.id)" class="check-icon" viewBox="0 0 24 24" width="16" height="16">
                <path d="M20 6L9 17l-5-5" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
            </view>
          </view>
          <view class="shop-info" @click="viewShop(shop.id)">
            <image class="shop-logo" :src="shop.logo" mode="aspectFill"></image>
            <view class="shop-name">{{ shop.name }}</view>
            <svg class="arrow-icon" viewBox="0 0 24 24" width="16" height="16">
              <path d="M9 18l6-6-6-6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
        
        <!-- 商品列表 -->
        <view class="cart-items">
          <view 
            class="cart-item"
            v-for="(item, itemIndex) in shop.items" 
            :key="itemIndex"
          >
            <view class="checkbox-wrapper" @click="toggleItemSelect(item.id)">
              <view class="checkbox" :class="{ checked: isItemSelected(item.id) }">
                <svg v-if="isItemSelected(item.id)" class="check-icon" viewBox="0 0 24 24" width="16" height="16">
                  <path d="M20 6L9 17l-5-5" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
              </view>
            </view>
            
            <image class="item-image" :src="item.image" mode="aspectFill" @click="viewProduct(item.id)"></image>
            
            <view class="item-info" @click="viewProduct(item.id)">
              <view class="item-name">{{ item.name }}</view>
              <view class="item-specs">{{ item.specs }}</view>
              
              <view class="item-bottom">
                <view class="item-price">¥{{ item.price.toFixed(2) }}</view>
                
                <view class="quantity-control">
                  <view class="quantity-btn minus" @click.stop="decreaseQuantity(item)">
                    <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
                      <path d="M5 12h14" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                  </view>
                  <view class="quantity-value">{{ item.quantity }}</view>
                  <view class="quantity-btn plus" @click.stop="increaseQuantity(item)">
                    <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
                      <path d="M12 5v14M5 12h14" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 猜你喜欢 -->
      <view class="recommend-section">
        <view class="section-title">猜你喜欢</view>
        <view class="product-grid">
          <view 
            class="product-item"
            v-for="(product, index) in recommendProducts" 
            :key="index"
            @click="viewProduct(product.id)"
          >
            <image class="product-image" :src="product.image" mode="aspectFill"></image>
            <view class="product-name">{{ product.name }}</view>
            <view class="product-price">¥{{ product.price.toFixed(2) }}</view>
          </view>
        </view>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
    
    <!-- 空购物车 -->
    <view class="empty-cart" v-else>
      <image class="empty-icon" src="/static/images/empty-cart.png" mode="aspectFit"></image>
      <view class="empty-text">购物车空空如也</view>
      <view class="go-shopping-btn" @click="goShopping">去逛逛</view>
    </view>
    
    <!-- 底部结算栏 -->
    <view class="checkout-bar" v-if="cartItems.length > 0">
      <view class="select-all" @click="toggleSelectAll">
        <view class="checkbox" :class="{ checked: isAllSelected }">
          <svg v-if="isAllSelected" class="check-icon" viewBox="0 0 24 24" width="16" height="16">
            <path d="M20 6L9 17l-5-5" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        <text>全选</text>
      </view>
      
      <view class="total-section" v-if="!isEditMode">
        <view class="total-price">
          <text>合计：</text>
          <text class="price">¥{{ totalPrice.toFixed(2) }}</text>
        </view>
        <view class="checkout-btn" @click="checkout">
          结算({{ selectedCount }})
        </view>
      </view>
      
      <view class="delete-section" v-else>
        <view class="delete-btn" @click="deleteSelected">
          删除
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 编辑模式
const isEditMode = ref(false);

// 购物车商品
const cartItems = ref([]);

// 推荐商品
const recommendProducts = ref([
  {
    id: '101',
    name: 'Apple AirPods Pro 2代',
    image: 'https://via.placeholder.com/300x300',
    price: 1999
  },
  {
    id: '102',
    name: '戴森吹风机 Supersonic HD08',
    image: 'https://via.placeholder.com/300x300',
    price: 3190
  },
  {
    id: '103',
    name: 'NIKE Air Jordan 1 高帮篮球鞋',
    image: 'https://via.placeholder.com/300x300',
    price: 1299
  },
  {
    id: '104',
    name: '三星Galaxy Watch 5 Pro',
    image: 'https://via.placeholder.com/300x300',
    price: 2999
  }
]);

// 已选择的商品ID
const selectedItemIds = ref([]);

// 按商家分组的购物车商品
const cartShops = computed(() => {
  const shops = [];
  const shopMap = {};
  
  cartItems.value.forEach(item => {
    if (!shopMap[item.shopId]) {
      shopMap[item.shopId] = {
        id: item.shopId,
        name: item.shopName,
        logo: item.shopLogo,
        items: []
      };
      shops.push(shopMap[item.shopId]);
    }
    shopMap[item.shopId].items.push(item);
  });
  
  return shops;
});

// 是否全选
const isAllSelected = computed(() => {
  return cartItems.value.length > 0 && selectedItemIds.value.length === cartItems.value.length;
});

// 已选择的商品数量
const selectedCount = computed(() => {
  return selectedItemIds.value.length;
});

// 总价
const totalPrice = computed(() => {
  let total = 0;
  cartItems.value.forEach(item => {
    if (selectedItemIds.value.includes(item.id)) {
      total += item.price * item.quantity;
    }
  });
  return total;
});

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 切换编辑模式
const toggleEditMode = () => {
  isEditMode.value = !isEditMode.value;
};

// 查看商品详情
const viewProduct = (productId) => {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/products/detail/index?id=${productId}`
  });
};

// 查看店铺
const viewShop = (shopId) => {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/shops/detail?id=${shopId}`
  });
};

// 去购物
const goShopping = () => {
  uni.switchTab({
    url: '/subPackages/activity-showcase/pages/discover/index'
  });
};

// 增加商品数量
const increaseQuantity = (item) => {
  if (item.quantity < 99) {
    item.quantity++;
  }
};

// 减少商品数量
const decreaseQuantity = (item) => {
  if (item.quantity > 1) {
    item.quantity--;
  }
};

// 切换商品选择状态
const toggleItemSelect = (itemId) => {
  const index = selectedItemIds.value.indexOf(itemId);
  if (index === -1) {
    selectedItemIds.value.push(itemId);
  } else {
    selectedItemIds.value.splice(index, 1);
  }
};

// 切换店铺商品选择状态
const toggleShopSelect = (shopId) => {
  const shop = cartShops.value.find(s => s.id === shopId);
  if (!shop) return;
  
  const shopItemIds = shop.items.map(item => item.id);
  const allSelected = shopItemIds.every(id => selectedItemIds.value.includes(id));
  
  if (allSelected) {
    // 取消选择该店铺所有商品
    selectedItemIds.value = selectedItemIds.value.filter(id => !shopItemIds.includes(id));
  } else {
    // 选择该店铺所有商品
    shopItemIds.forEach(id => {
      if (!selectedItemIds.value.includes(id)) {
        selectedItemIds.value.push(id);
      }
    });
  }
};

// 切换全选状态
const toggleSelectAll = () => {
  if (isAllSelected.value) {
    selectedItemIds.value = [];
  } else {
    selectedItemIds.value = cartItems.value.map(item => item.id);
  }
};

// 判断商品是否被选中
const isItemSelected = (itemId) => {
  return selectedItemIds.value.includes(itemId);
};

// 判断店铺是否被选中
const isShopSelected = (shopId) => {
  const shop = cartShops.value.find(s => s.id === shopId);
  if (!shop) return false;
  
  return shop.items.every(item => selectedItemIds.value.includes(item.id));
};

// 删除选中的商品
const deleteSelected = () => {
  if (selectedItemIds.value.length === 0) {
    uni.showToast({
      title: '请选择要删除的商品',
      icon: 'none'
    });
    return;
  }
  
  uni.showModal({
    title: '提示',
    content: '确定要删除选中的商品吗？',
    success: (res) => {
      if (res.confirm) {
        cartItems.value = cartItems.value.filter(item => !selectedItemIds.value.includes(item.id));
        selectedItemIds.value = [];
      }
    }
  });
};

// 结算
const checkout = () => {
  if (selectedItemIds.value.length === 0) {
    uni.showToast({
      title: '请选择要结算的商品',
      icon: 'none'
    });
    return;
  }
  
  const selectedItems = cartItems.value.filter(item => selectedItemIds.value.includes(item.id));
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/orders/confirm?from=cart`
  });
};

// 加载更多
const loadMore = () => {
  // 模拟加载更多数据
  uni.showToast({
    title: '已加载全部数据',
    icon: 'none'
  });
};

// 初始化购物车数据
const initCartData = () => {
  // 模拟购物车数据
  cartItems.value = [
    {
      id: '1',
      shopId: 'shop1',
      shopName: 'Apple授权专卖店',
      shopLogo: 'https://via.placeholder.com/100',
      name: 'iPhone 14 Pro 深空黑 256G',
      specs: '颜色：深空黑；内存：256G',
      price: 7999,
      quantity: 1,
      image: 'https://via.placeholder.com/300x300'
    },
    {
      id: '2',
      shopId: 'shop1',
      shopName: 'Apple授权专卖店',
      shopLogo: 'https://via.placeholder.com/100',
      name: 'Apple Watch Series 8 GPS 45mm',
      specs: '颜色：午夜色；尺寸：45mm',
      price: 3499,
      quantity: 1,
      image: 'https://via.placeholder.com/300x300'
    },
    {
      id: '3',
      shopId: 'shop2',
      shopName: '华为官方旗舰店',
      shopLogo: 'https://via.placeholder.com/100',
      name: '华为Mate 50 Pro 曜金黑 512G',
      specs: '颜色：曜金黑；内存：512G',
      price: 6999,
      quantity: 1,
      image: 'https://via.placeholder.com/300x300'
    }
  ];
  
  // 默认全选
  selectedItemIds.value = cartItems.value.map(item => item.id);
};

onMounted(() => {
  initCartData();
});
</script>

<style lang="scss" scoped>
.cart-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F2F2F7;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
  
  .navbar-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 4px 6px rgba(255,59,105,0.15);
  }
  
  .navbar-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 30rpx;
    padding-top: var(--status-bar-height, 25px);
    box-sizing: border-box;
  }
  
  .navbar-left, .navbar-right {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80rpx;
    height: 80rpx;
  }
  
  .navbar-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #FFFFFF;
    letter-spacing: 0.5px;
  }
  
  .icon {
    width: 48rpx;
    height: 48rpx;
  }
  
  .edit-btn {
    font-size: 28rpx;
    color: #FFFFFF;
  }
}

/* 内容区域 */
.content-scroll {
  flex: 1;
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
  margin-bottom: 100rpx; /* 底部结算栏高度 */
}

/* 商家分组 */
.shop-group {
  margin-bottom: 20rpx;
  background-color: #FFFFFF;
}

/* 商家信息 */
.shop-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #F2F2F7;
  
  .checkbox-wrapper {
    padding: 10rpx;
  }
  
  .checkbox {
    width: 36rpx;
    height: 36rpx;
    border-radius: 50%;
    border: 2rpx solid #CCCCCC;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &.checked {
      background-color: #FF3B69;
      border-color: #FF3B69;
    }
    
    .check-icon {
      color: #FFFFFF;
    }
  }
  
  .shop-info {
    flex: 1;
    display: flex;
    align-items: center;
    margin-left: 10rpx;
    
    .shop-logo {
      width: 40rpx;
      height: 40rpx;
      border-radius: 50%;
    }
    
    .shop-name {
      flex: 1;
      font-size: 28rpx;
      color: #333333;
      margin-left: 10rpx;
    }
    
    .arrow-icon {
      width: 32rpx;
      height: 32rpx;
      color: #999999;
    }
  }
}

/* 购物车商品 */
.cart-items {
  padding: 0 30rpx;
}

.cart-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #F2F2F7;
  
  &:last-child {
    border-bottom: none;
  }
  
  .checkbox-wrapper {
    padding: 10rpx;
  }
  
  .checkbox {
    width: 36rpx;
    height: 36rpx;
    border-radius: 50%;
    border: 2rpx solid #CCCCCC;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &.checked {
      background-color: #FF3B69;
      border-color: #FF3B69;
    }
    
    .check-icon {
      color: #FFFFFF;
    }
  }
  
  .item-image {
    width: 160rpx;
    height: 160rpx;
    border-radius: 8rpx;
    margin: 0 20rpx;
  }
  
  .item-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 160rpx;
    justify-content: space-between;
    
    .item-name {
      font-size: 28rpx;
      color: #333333;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
    }
    
    .item-specs {
      font-size: 24rpx;
      color: #999999;
      margin-top: 10rpx;
    }
    
    .item-bottom {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: auto;
      
      .item-price {
        font-size: 32rpx;
        color: #FF3B69;
        font-weight: 600;
      }
      
      .quantity-control {
        display: flex;
        align-items: center;
        
        .quantity-btn {
          width: 48rpx;
          height: 48rpx;
          border: 1rpx solid #EEEEEE;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .quantity-value {
          min-width: 60rpx;
          text-align: center;
          font-size: 28rpx;
          color: #333333;
        }
      }
    }
  }
}

/* 猜你喜欢 */
.recommend-section {
  margin-top: 20rpx;
  padding: 30rpx;
  background-color: #FFFFFF;
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 20rpx;
  }
  
  .product-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;
    
    .product-item {
      background-color: #FFFFFF;
      border-radius: 8rpx;
      overflow: hidden;
      
      .product-image {
        width: 100%;
        height: 320rpx;
        border-radius: 8rpx;
      }
      
      .product-name {
        font-size: 28rpx;
        color: #333333;
        margin-top: 10rpx;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
      }
      
      .product-price {
        font-size: 28rpx;
        color: #FF3B69;
        font-weight: 600;
        margin-top: 10rpx;
      }
    }
  }
}

/* 空购物车 */
.empty-cart {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: calc(var(--status-bar-height, 25px) + 62px);
  
  .empty-icon {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
  }
  
  .empty-text {
    font-size: 28rpx;
    color: #999999;
    margin-bottom: 40rpx;
  }
  
  .go-shopping-btn {
    padding: 20rpx 60rpx;
    background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
    color: #FFFFFF;
    font-size: 28rpx;
    border-radius: 40rpx;
  }
}

/* 底部结算栏 */
.checkout-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  padding-bottom: env(safe-area-inset-bottom); /* 适配 iPhone X 以上的底部安全区域 */
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 99;
  
  .select-all {
    display: flex;
    align-items: center;
    
    .checkbox {
      width: 36rpx;
      height: 36rpx;
      border-radius: 50%;
      border: 2rpx solid #CCCCCC;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 10rpx;
      
      &.checked {
        background-color: #FF3B69;
        border-color: #FF3B69;
      }
      
      .check-icon {
        color: #FFFFFF;
      }
    }
    
    text {
      font-size: 28rpx;
      color: #333333;
    }
  }
  
  .total-section {
    display: flex;
    align-items: center;
    
    .total-price {
      margin-right: 20rpx;
      font-size: 28rpx;
      color: #333333;
      
      .price {
        color: #FF3B69;
        font-weight: 600;
      }
    }
    
    .checkout-btn {
      padding: 16rpx 40rpx;
      background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
      color: #FFFFFF;
      font-size: 28rpx;
      border-radius: 40rpx;
    }
  }
  
  .delete-section {
    .delete-btn {
      padding: 16rpx 40rpx;
      background-color: #FF3B30;
      color: #FFFFFF;
      font-size: 28rpx;
      border-radius: 40rpx;
    }
  }
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 34px; /* iOS 安全区域高度 */
}
</style> 