"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
if (!Array) {
  const _component_template = common_vendor.resolveComponent("template");
  const _component_cu_custom = common_vendor.resolveComponent("cu-custom");
  (_component_template + _component_cu_custom)();
}
const _sfc_main = {
  __name: "partner-team",
  setup(__props) {
    const currentMonth = common_vendor.ref((/* @__PURE__ */ new Date()).getMonth() + 1);
    const teamTotalNum = common_vendor.ref(86);
    const teamNewNum = common_vendor.ref(12);
    const teamActiveNum = common_vendor.ref(35);
    const teamIncome = common_vendor.ref("1286.50");
    const currentLevel = common_vendor.ref(1);
    const teamMembers = common_vendor.ref([]);
    const page = common_vendor.ref(1);
    common_vendor.ref(10);
    const hasMore = common_vendor.ref(true);
    const isLoading = common_vendor.ref(false);
    const levelInfo = common_vendor.computed(() => {
      return currentLevel.value === 1 ? "直接关注您的成员" : "您的一级团队发展的成员";
    });
    const emptyText = common_vendor.computed(() => {
      return currentLevel.value === 1 ? "暂无一级团队成员" : "暂无二级团队成员";
    });
    const switchLevel = (level) => {
      if (currentLevel.value === level)
        return;
      currentLevel.value = level;
      page.value = 1;
      hasMore.value = true;
      teamMembers.value = [];
      loadData();
    };
    const loadData = () => {
      isLoading.value = true;
      setTimeout(() => {
        const mockData = generateMockTeam();
        if (page.value === 1) {
          teamMembers.value = mockData;
        } else {
          teamMembers.value = [...teamMembers.value, ...mockData];
        }
        hasMore.value = page.value < 3;
        isLoading.value = false;
      }, 500);
    };
    const loadMore = () => {
      if (isLoading.value || !hasMore.value)
        return;
      page.value++;
      loadData();
    };
    const shareToFriends = () => {
      common_vendor.index.navigateTo({
        url: "/pages/my/partner-poster"
      });
    };
    const formatDate = (dateStr) => {
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      return `${year}-${month}-${day}`;
    };
    const generateMockTeam = () => {
      const mockTeam = [];
      const nicknames = ["城市猎人", "科技达人", "生活家", "微笑向暖", "光影世界", "千里之行", "星空漫步", "纸短情长"];
      const avatars = [
        "/static/images/avatar-1.png",
        "/static/images/avatar-2.png",
        "/static/images/avatar-3.png",
        "/static/images/avatar-4.png",
        "/static/images/avatar-5.png",
        "/static/images/avatar-6.png"
      ];
      const levels = ["普通合伙人", "银牌合伙人", "金牌合伙人", "钻石合伙人"];
      for (let i = 0; i < 10; i++) {
        const now = /* @__PURE__ */ new Date();
        const randomDays = Math.floor(Math.random() * 60);
        const joinTime = new Date(now.getTime() - randomDays * 24 * 60 * 60 * 1e3);
        const contribution = Math.floor(Math.random() * 2e3);
        const fansNum = Math.floor(Math.random() * 50);
        let levelIndex;
        if (currentLevel.value === 1) {
          levelIndex = Math.floor(Math.random() * levels.length);
        } else {
          levelIndex = Math.floor(Math.random() * 2);
        }
        mockTeam.push({
          id: "member_" + Date.now() + i,
          nickname: nicknames[Math.floor(Math.random() * nicknames.length)],
          avatar: avatars[Math.floor(Math.random() * avatars.length)],
          joinTime,
          partnerLevel: levels[levelIndex],
          contribution,
          fansNum
        });
      }
      return mockTeam;
    };
    common_vendor.onMounted(() => {
      loadData();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          bgColor: "bg-gradient-blue",
          isBack: true
        }),
        b: common_vendor.t(currentMonth.value),
        c: common_vendor.t(teamTotalNum.value),
        d: common_vendor.t(teamNewNum.value),
        e: common_vendor.t(teamActiveNum.value),
        f: common_vendor.t(teamIncome.value),
        g: currentLevel.value === 1 ? 1 : "",
        h: common_vendor.o(($event) => switchLevel(1)),
        i: currentLevel.value === 2 ? 1 : "",
        j: common_vendor.o(($event) => switchLevel(2)),
        k: common_vendor.t(levelInfo.value),
        l: teamMembers.value.length > 0
      }, teamMembers.value.length > 0 ? {
        m: common_vendor.f(teamMembers.value, (item, index, i0) => {
          return {
            a: item.avatar,
            b: common_vendor.t(item.nickname),
            c: common_vendor.t(item.partnerLevel),
            d: common_vendor.t(formatDate(item.joinTime)),
            e: common_vendor.t(item.contribution),
            f: common_vendor.t(item.fansNum),
            g: index
          };
        })
      } : {
        n: common_assets._imports_0$23,
        o: common_vendor.t(emptyText.value),
        p: common_vendor.o(shareToFriends)
      }, {
        q: teamMembers.value.length > 0 && hasMore.value
      }, teamMembers.value.length > 0 && hasMore.value ? common_vendor.e({
        r: !isLoading.value
      }, !isLoading.value ? {
        s: common_vendor.o(loadMore)
      } : {}) : {}, {
        t: teamMembers.value.length > 0 && !hasMore.value
      }, teamMembers.value.length > 0 && !hasMore.value ? {} : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-28a7193a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/partner/pages/partner-team.js.map
