{"version": 3, "file": "bank.js", "sources": ["subPackages/payment/pages/bank.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNccGF5bWVudFxwYWdlc1xiYW5rLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"bank-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\r\n      <view class=\"navbar-left\" @click=\"goBack\">\r\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\r\n      </view>\r\n      <view class=\"navbar-title\">银行卡管理</view>\r\n      <view class=\"navbar-right\"></view>\r\n    </view>\r\n    \r\n    <!-- 银行卡列表 -->\r\n    <view class=\"bank-card-list\" :style=\"{ marginTop: (navbarHeight + 10) + 'px' }\">\r\n      <view v-if=\"bankCards.length > 0\">\r\n        <view \r\n          class=\"bank-card-item\" \r\n          v-for=\"(card, index) in bankCards\" \r\n          :key=\"index\"\r\n          :class=\"{'selected': selectedCardId === card.id}\"\r\n          @click=\"selectCard(card.id)\"\r\n        >\r\n          <view class=\"card-header\">\r\n            <view class=\"bank-logo\">\r\n              <image :src=\"card.bankLogo\" class=\"bank-logo-img\"></image>\r\n            </view>\r\n            <view class=\"bank-name\">{{ card.bankName }}</view>\r\n            <view class=\"card-type\">{{ card.cardType }}</view>\r\n          </view>\r\n          <view class=\"card-number\">**** **** **** {{ card.cardNumberLast4 }}</view>\r\n          <view class=\"card-footer\">\r\n            <view class=\"card-holder\">{{ card.cardHolder }}</view>\r\n            <view class=\"card-actions\">\r\n              <view class=\"default-tag\" v-if=\"card.isDefault\">默认</view>\r\n              <view class=\"card-action\" @click.stop=\"setAsDefault(card.id)\" v-if=\"!card.isDefault\">设为默认</view>\r\n              <view class=\"card-action delete\" @click.stop=\"deleteCard(card.id)\">删除</view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 空状态 -->\r\n      <view v-else class=\"empty-view\">\r\n        <image class=\"empty-icon\" src=\"/static/images/empty.png\" mode=\"aspectFit\"></image>\r\n        <view class=\"empty-text\">暂无银行卡</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 添加银行卡按钮 -->\r\n    <view class=\"add-card-btn\" @click=\"showAddCardPopup\">\r\n      <image src=\"/static/images/tabbar/添加.png\" class=\"add-icon\"></image>\r\n      <text>添加银行卡</text>\r\n    </view>\r\n    \r\n    <!-- 添加银行卡弹窗 -->\r\n    <view class=\"popup-mask\" v-if=\"showPopup\" @click=\"closePopup\"></view>\r\n    <view class=\"popup-content\" v-if=\"showPopup\">\r\n      <view class=\"popup-header\">\r\n        <view class=\"popup-title\">添加银行卡</view>\r\n        <view class=\"popup-close\" @click=\"closePopup\">×</view>\r\n      </view>\r\n      <view class=\"popup-body\">\r\n        <view class=\"form-item\">\r\n          <view class=\"form-label\">持卡人</view>\r\n          <input class=\"form-input\" v-model=\"newCard.cardHolder\" placeholder=\"请输入持卡人姓名\" />\r\n        </view>\r\n        <view class=\"form-item\">\r\n          <view class=\"form-label\">卡号</view>\r\n          <input class=\"form-input\" v-model=\"newCard.cardNumber\" placeholder=\"请输入银行卡号\" type=\"number\" maxlength=\"19\" @input=\"formatCardNumber\" />\r\n        </view>\r\n        <view class=\"form-item\">\r\n          <view class=\"form-label\">开户银行</view>\r\n          <picker class=\"form-picker\" :range=\"bankList\" range-key=\"name\" @change=\"onBankChange\">\r\n            <view class=\"picker-value\">{{ newCard.bankName || '请选择开户银行' }}</view>\r\n          </picker>\r\n        </view>\r\n        <view class=\"form-item\">\r\n          <view class=\"form-label\">手机号</view>\r\n          <input class=\"form-input\" v-model=\"newCard.phone\" placeholder=\"请输入银行预留手机号\" type=\"number\" maxlength=\"11\" />\r\n        </view>\r\n        <view class=\"form-checkbox\">\r\n          <checkbox :checked=\"newCard.isDefault\" @click=\"newCard.isDefault = !newCard.isDefault\" color=\"#0052CC\" />\r\n          <text class=\"checkbox-label\">设为默认银行卡</text>\r\n        </view>\r\n      </view>\r\n      <view class=\"popup-footer\">\r\n        <button class=\"cancel-btn\" @click=\"closePopup\">取消</button>\r\n        <button class=\"confirm-btn\" @click=\"addCard\" :disabled=\"!canAddCard\">确定</button>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, computed, onMounted } from 'vue';\r\n\r\n// 响应式状态\r\nconst statusBarHeight = ref(20);\r\nconst navbarHeight = ref(64);\r\nconst bankCards = ref([\r\n  {\r\n    id: 'card001',\r\n    bankName: '中国建设银行',\r\n    bankLogo: '/static/images/banks/ccb.png',\r\n    cardType: '储蓄卡',\r\n    cardNumber: '6217 0012 3456 7890',\r\n    cardNumberLast4: '7890',\r\n    cardHolder: '张三',\r\n    isDefault: true\r\n  },\r\n  {\r\n    id: 'card002',\r\n    bankName: '中国工商银行',\r\n    bankLogo: '/static/images/banks/icbc.png',\r\n    cardType: '储蓄卡',\r\n    cardNumber: '6222 0212 3456 1234',\r\n    cardNumberLast4: '1234',\r\n    cardHolder: '张三',\r\n    isDefault: false\r\n  }\r\n]);\r\nconst selectedCardId = ref('card001');\r\nconst showPopup = ref(false);\r\nconst newCard = reactive({\r\n  cardHolder: '',\r\n  cardNumber: '',\r\n  bankName: '',\r\n  bankId: '',\r\n  phone: '',\r\n  isDefault: false\r\n});\r\nconst bankList = [\r\n  { id: 'icbc', name: '中国工商银行', logo: '/static/images/banks/icbc.png' },\r\n  { id: 'abc', name: '中国农业银行', logo: '/static/images/banks/abc.png' },\r\n  { id: 'boc', name: '中国银行', logo: '/static/images/banks/boc.png' },\r\n  { id: 'ccb', name: '中国建设银行', logo: '/static/images/banks/ccb.png' },\r\n  { id: 'psbc', name: '中国邮政储蓄银行', logo: '/static/images/banks/psbc.png' },\r\n  { id: 'cmb', name: '招商银行', logo: '/static/images/banks/cmb.png' },\r\n  { id: 'spdb', name: '浦发银行', logo: '/static/images/banks/spdb.png' },\r\n  { id: 'cib', name: '兴业银行', logo: '/static/images/banks/cib.png' }\r\n];\r\n\r\n// 计算属性\r\nconst canAddCard = computed(() => {\r\n  return newCard.cardHolder && \r\n         newCard.cardNumber && \r\n         newCard.cardNumber.length >= 16 &&\r\n         newCard.bankName &&\r\n         newCard.phone && \r\n         newCard.phone.length === 11;\r\n});\r\n\r\n// 生命周期钩子\r\nonMounted(() => {\r\n  // 获取状态栏高度\r\n  const sysInfo = uni.getSystemInfoSync();\r\n  statusBarHeight.value = sysInfo.statusBarHeight;\r\n  navbarHeight.value = statusBarHeight.value + 44;\r\n  \r\n  // 获取银行卡列表\r\n  getBankCards();\r\n});\r\n\r\n// 方法\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 获取银行卡列表\r\nconst getBankCards = () => {\r\n  // 这里应该是从API获取银行卡列表\r\n  // 模拟API请求，数据已在data中初始化\r\n};\r\n\r\n// 选择银行卡\r\nconst selectCard = (cardId) => {\r\n  selectedCardId.value = cardId;\r\n};\r\n\r\n// 设置默认银行卡\r\nconst setAsDefault = (cardId) => {\r\n  uni.showLoading({\r\n    title: '设置中...'\r\n  });\r\n  \r\n  // 模拟API请求\r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    \r\n    // 更新本地数据\r\n    bankCards.value.forEach(card => {\r\n      card.isDefault = card.id === cardId;\r\n    });\r\n    \r\n    uni.showToast({\r\n      title: '设置成功',\r\n      icon: 'success'\r\n    });\r\n  }, 500);\r\n};\r\n\r\n// 删除银行卡\r\nconst deleteCard = (cardId) => {\r\n  uni.showModal({\r\n    title: '提示',\r\n    content: '确定要删除此银行卡吗？',\r\n    success: (res) => {\r\n      if (res.confirm) {\r\n        uni.showLoading({\r\n          title: '删除中...'\r\n        });\r\n        \r\n        // 模拟API请求\r\n        setTimeout(() => {\r\n          uni.hideLoading();\r\n          \r\n          // 更新本地数据\r\n          const index = bankCards.value.findIndex(card => card.id === cardId);\r\n          if (index !== -1) {\r\n            // 如果删除的是默认卡，则设置第一张卡为默认\r\n            const isDefault = bankCards.value[index].isDefault;\r\n            bankCards.value.splice(index, 1);\r\n            \r\n            if (isDefault && bankCards.value.length > 0) {\r\n              bankCards.value[0].isDefault = true;\r\n            }\r\n            \r\n            // 更新选中的卡\r\n            if (selectedCardId.value === cardId) {\r\n              selectedCardId.value = bankCards.value.length > 0 ? bankCards.value[0].id : '';\r\n            }\r\n          }\r\n          \r\n          uni.showToast({\r\n            title: '删除成功',\r\n            icon: 'success'\r\n          });\r\n        }, 500);\r\n      }\r\n    }\r\n  });\r\n};\r\n\r\n// 显示添加银行卡弹窗\r\nconst showAddCardPopup = () => {\r\n  showPopup.value = true;\r\n  resetNewCard();\r\n};\r\n\r\n// 关闭弹窗\r\nconst closePopup = () => {\r\n  showPopup.value = false;\r\n};\r\n\r\n// 重置新卡信息\r\nconst resetNewCard = () => {\r\n  Object.assign(newCard, {\r\n    cardHolder: '',\r\n    cardNumber: '',\r\n    bankName: '',\r\n    bankId: '',\r\n    phone: '',\r\n    isDefault: false\r\n  });\r\n};\r\n\r\n// 格式化卡号输入\r\nconst formatCardNumber = (e) => {\r\n  let value = e.detail.value.replace(/\\s/g, '');\r\n  let formattedValue = '';\r\n  \r\n  for (let i = 0; i < value.length; i++) {\r\n    if (i > 0 && i % 4 === 0) {\r\n      formattedValue += ' ';\r\n    }\r\n    formattedValue += value[i];\r\n  }\r\n  \r\n  newCard.cardNumber = formattedValue;\r\n};\r\n\r\n// 银行选择变化\r\nconst onBankChange = (e) => {\r\n  const index = e.detail.value;\r\n  const selectedBank = bankList[index];\r\n  newCard.bankName = selectedBank.name;\r\n  newCard.bankId = selectedBank.id;\r\n};\r\n\r\n// 添加银行卡\r\nconst addCard = () => {\r\n  if (!canAddCard.value) return;\r\n  \r\n  uni.showLoading({\r\n    title: '添加中...'\r\n  });\r\n  \r\n  // 模拟API请求\r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    \r\n    // 生成新卡信息\r\n    const cardNumber = newCard.cardNumber.replace(/\\s/g, '');\r\n    const cardNumberLast4 = cardNumber.slice(-4);\r\n    const selectedBank = bankList.find(bank => bank.id === newCard.bankId);\r\n    \r\n    const newCardInfo = {\r\n      id: 'card' + Date.now(),\r\n      bankName: newCard.bankName,\r\n      bankLogo: selectedBank ? selectedBank.logo : '/static/images/banks/default.png',\r\n      cardType: '储蓄卡',\r\n      cardNumber: newCard.cardNumber,\r\n      cardNumberLast4: cardNumberLast4,\r\n      cardHolder: newCard.cardHolder,\r\n      isDefault: newCard.isDefault\r\n    };\r\n    \r\n    // 如果设置为默认卡，更新其他卡的状态\r\n    if (newCardInfo.isDefault) {\r\n      bankCards.value.forEach(card => {\r\n        card.isDefault = false;\r\n      });\r\n    }\r\n    \r\n    // 添加到列表\r\n    bankCards.value.push(newCardInfo);\r\n    selectedCardId.value = newCardInfo.id;\r\n    \r\n    // 关闭弹窗\r\n    closePopup();\r\n    \r\n    uni.showToast({\r\n      title: '添加成功',\r\n      icon: 'success'\r\n    });\r\n  }, 800);\r\n};\r\n</script>\r\n\r\n<style>\r\n.bank-container {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n  padding-bottom: 120rpx;\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.custom-navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 44px;\r\n  background-color: #0052CC;\r\n  color: #fff;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 15px;\r\n  z-index: 999;\r\n}\r\n\r\n.navbar-left {\r\n  width: 80rpx;\r\n  height: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n}\r\n\r\n.navbar-right {\r\n  width: 80rpx;\r\n}\r\n\r\n/* 银行卡列表 */\r\n.bank-card-list {\r\n  padding: 30rpx;\r\n}\r\n\r\n.bank-card-item {\r\n  background: linear-gradient(to right, #0052CC, #0066FF);\r\n  border-radius: 20rpx;\r\n  padding: 40rpx;\r\n  color: #fff;\r\n  margin-bottom: 30rpx;\r\n  box-shadow: 0 8rpx 20rpx rgba(0, 82, 204, 0.2);\r\n  position: relative;\r\n}\r\n\r\n.bank-card-item.selected {\r\n  box-shadow: 0 8rpx 30rpx rgba(0, 82, 204, 0.4);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 40rpx;\r\n}\r\n\r\n.bank-logo {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  background-color: rgba(255, 255, 255, 0.9);\r\n  border-radius: 40rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.bank-logo-img {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n}\r\n\r\n.bank-name {\r\n  font-size: 32rpx;\r\n  font-weight: 500;\r\n  flex: 1;\r\n}\r\n\r\n.card-type {\r\n  font-size: 24rpx;\r\n  opacity: 0.8;\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  padding: 6rpx 20rpx;\r\n  border-radius: 20rpx;\r\n}\r\n\r\n.card-number {\r\n  font-size: 40rpx;\r\n  font-weight: bold;\r\n  letter-spacing: 4rpx;\r\n  margin-bottom: 40rpx;\r\n}\r\n\r\n.card-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.card-holder {\r\n  font-size: 28rpx;\r\n}\r\n\r\n.card-actions {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.default-tag {\r\n  font-size: 24rpx;\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  padding: 6rpx 20rpx;\r\n  border-radius: 20rpx;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.card-action {\r\n  font-size: 24rpx;\r\n  margin-left: 20rpx;\r\n  opacity: 0.8;\r\n}\r\n\r\n.card-action.delete {\r\n  color: #ffcccc;\r\n}\r\n\r\n/* 添加银行卡按钮 */\r\n.add-card-btn {\r\n  position: fixed;\r\n  bottom: 30rpx;\r\n  left: 30rpx;\r\n  right: 30rpx;\r\n  height: 90rpx;\r\n  background-color: #0052CC;\r\n  color: #fff;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 45rpx;\r\n  font-size: 32rpx;\r\n}\r\n\r\n.add-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  margin-right: 10rpx;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-view {\r\n  padding: 100rpx 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.empty-icon {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n}\r\n\r\n/* 弹窗样式 */\r\n.popup-mask {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  z-index: 1000;\r\n}\r\n\r\n.popup-content {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background-color: #fff;\r\n  border-radius: 20rpx 20rpx 0 0;\r\n  z-index: 1001;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n\r\n.popup-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 30rpx;\r\n  border-bottom: 1rpx solid #f5f5f5;\r\n}\r\n\r\n.popup-title {\r\n  font-size: 32rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.popup-close {\r\n  font-size: 40rpx;\r\n  color: #999;\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.popup-body {\r\n  padding: 30rpx;\r\n}\r\n\r\n.form-item {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.form-label {\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.form-input {\r\n  width: 100%;\r\n  height: 90rpx;\r\n  background-color: #f8f9fc;\r\n  border-radius: 10rpx;\r\n  padding: 0 20rpx;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.form-picker {\r\n  width: 100%;\r\n  height: 90rpx;\r\n  background-color: #f8f9fc;\r\n  border-radius: 10rpx;\r\n  padding: 0 20rpx;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.picker-value {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n}\r\n\r\n.form-checkbox {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-top: 20rpx;\r\n}\r\n\r\n.checkbox-label {\r\n  font-size: 28rpx;\r\n  margin-left: 10rpx;\r\n}\r\n\r\n.popup-footer {\r\n  display: flex;\r\n  padding: 30rpx;\r\n  border-top: 1rpx solid #f5f5f5;\r\n}\r\n\r\n.cancel-btn, .confirm-btn {\r\n  flex: 1;\r\n  height: 90rpx;\r\n  line-height: 90rpx;\r\n  text-align: center;\r\n  font-size: 32rpx;\r\n  border-radius: 45rpx;\r\n}\r\n\r\n.cancel-btn {\r\n  background-color: #f5f5f5;\r\n  color: #666;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.confirm-btn {\r\n  background-color: #0052CC;\r\n  color: #fff;\r\n}\r\n\r\n.confirm-btn[disabled] {\r\n  background-color: #cccccc;\r\n}\r\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/payment/pages/bank.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "computed", "onMounted", "uni", "MiniProgramPage"], "mappings": ";;;;;;AAgGA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,eAAeA,cAAAA,IAAI,EAAE;AAC3B,UAAM,YAAYA,cAAAA,IAAI;AAAA,MACpB;AAAA,QACE,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,UAAU;AAAA,QACV,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,iBAAiB;AAAA,QACjB,YAAY;AAAA,QACZ,WAAW;AAAA,MACZ;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,UAAU;AAAA,QACV,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,iBAAiB;AAAA,QACjB,YAAY;AAAA,QACZ,WAAW;AAAA,MACZ;AAAA,IACH,CAAC;AACD,UAAM,iBAAiBA,cAAAA,IAAI,SAAS;AACpC,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAC3B,UAAM,UAAUC,cAAAA,SAAS;AAAA,MACvB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,WAAW;AAAA,IACb,CAAC;AACD,UAAM,WAAW;AAAA,MACf,EAAE,IAAI,QAAQ,MAAM,UAAU,MAAM,gCAAiC;AAAA,MACrE,EAAE,IAAI,OAAO,MAAM,UAAU,MAAM,+BAAgC;AAAA,MACnE,EAAE,IAAI,OAAO,MAAM,QAAQ,MAAM,+BAAgC;AAAA,MACjE,EAAE,IAAI,OAAO,MAAM,UAAU,MAAM,+BAAgC;AAAA,MACnE,EAAE,IAAI,QAAQ,MAAM,YAAY,MAAM,gCAAiC;AAAA,MACvE,EAAE,IAAI,OAAO,MAAM,QAAQ,MAAM,+BAAgC;AAAA,MACjE,EAAE,IAAI,QAAQ,MAAM,QAAQ,MAAM,gCAAiC;AAAA,MACnE,EAAE,IAAI,OAAO,MAAM,QAAQ,MAAM,+BAAgC;AAAA,IACnE;AAGA,UAAM,aAAaC,cAAQ,SAAC,MAAM;AAChC,aAAO,QAAQ,cACR,QAAQ,cACR,QAAQ,WAAW,UAAU,MAC7B,QAAQ,YACR,QAAQ,SACR,QAAQ,MAAM,WAAW;AAAA,IAClC,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AAEd,YAAM,UAAUC,oBAAI;AACpB,sBAAgB,QAAQ,QAAQ;AAChC,mBAAa,QAAQ,gBAAgB,QAAQ;AAG7C;IACF,CAAC;AAID,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,eAAe,MAAM;AAAA,IAG3B;AAGA,UAAM,aAAa,CAAC,WAAW;AAC7B,qBAAe,QAAQ;AAAA,IACzB;AAGA,UAAM,eAAe,CAAC,WAAW;AAC/BA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGf,kBAAU,MAAM,QAAQ,UAAQ;AAC9B,eAAK,YAAY,KAAK,OAAO;AAAA,QACnC,CAAK;AAEDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,aAAa,CAAC,WAAW;AAC7BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,YAAY;AAAA,cACd,OAAO;AAAA,YACjB,CAAS;AAGD,uBAAW,MAAM;AACfA,4BAAG,MAAC,YAAW;AAGf,oBAAM,QAAQ,UAAU,MAAM,UAAU,UAAQ,KAAK,OAAO,MAAM;AAClE,kBAAI,UAAU,IAAI;AAEhB,sBAAM,YAAY,UAAU,MAAM,KAAK,EAAE;AACzC,0BAAU,MAAM,OAAO,OAAO,CAAC;AAE/B,oBAAI,aAAa,UAAU,MAAM,SAAS,GAAG;AAC3C,4BAAU,MAAM,CAAC,EAAE,YAAY;AAAA,gBAChC;AAGD,oBAAI,eAAe,UAAU,QAAQ;AACnC,iCAAe,QAAQ,UAAU,MAAM,SAAS,IAAI,UAAU,MAAM,CAAC,EAAE,KAAK;AAAA,gBAC7E;AAAA,cACF;AAEDA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF,GAAE,GAAG;AAAA,UACP;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,mBAAmB,MAAM;AAC7B,gBAAU,QAAQ;AAClB;IACF;AAGA,UAAM,aAAa,MAAM;AACvB,gBAAU,QAAQ;AAAA,IACpB;AAGA,UAAM,eAAe,MAAM;AACzB,aAAO,OAAO,SAAS;AAAA,QACrB,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,WAAW;AAAA,MACf,CAAG;AAAA,IACH;AAGA,UAAM,mBAAmB,CAAC,MAAM;AAC9B,UAAI,QAAQ,EAAE,OAAO,MAAM,QAAQ,OAAO,EAAE;AAC5C,UAAI,iBAAiB;AAErB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAI,IAAI,KAAK,IAAI,MAAM,GAAG;AACxB,4BAAkB;AAAA,QACnB;AACD,0BAAkB,MAAM,CAAC;AAAA,MAC1B;AAED,cAAQ,aAAa;AAAA,IACvB;AAGA,UAAM,eAAe,CAAC,MAAM;AAC1B,YAAM,QAAQ,EAAE,OAAO;AACvB,YAAM,eAAe,SAAS,KAAK;AACnC,cAAQ,WAAW,aAAa;AAChC,cAAQ,SAAS,aAAa;AAAA,IAChC;AAGA,UAAM,UAAU,MAAM;AACpB,UAAI,CAAC,WAAW;AAAO;AAEvBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGf,cAAM,aAAa,QAAQ,WAAW,QAAQ,OAAO,EAAE;AACvD,cAAM,kBAAkB,WAAW,MAAM,EAAE;AAC3C,cAAM,eAAe,SAAS,KAAK,UAAQ,KAAK,OAAO,QAAQ,MAAM;AAErE,cAAM,cAAc;AAAA,UAClB,IAAI,SAAS,KAAK,IAAK;AAAA,UACvB,UAAU,QAAQ;AAAA,UAClB,UAAU,eAAe,aAAa,OAAO;AAAA,UAC7C,UAAU;AAAA,UACV,YAAY,QAAQ;AAAA,UACpB;AAAA,UACA,YAAY,QAAQ;AAAA,UACpB,WAAW,QAAQ;AAAA,QACzB;AAGI,YAAI,YAAY,WAAW;AACzB,oBAAU,MAAM,QAAQ,UAAQ;AAC9B,iBAAK,YAAY;AAAA,UACzB,CAAO;AAAA,QACF;AAGD,kBAAU,MAAM,KAAK,WAAW;AAChC,uBAAe,QAAQ,YAAY;AAGnC;AAEAA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF,GAAE,GAAG;AAAA,IACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/UA,GAAG,WAAWC,SAAe;"}