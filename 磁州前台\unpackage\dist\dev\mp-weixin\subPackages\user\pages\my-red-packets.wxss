/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.my-red-packets {
  min-height: 100vh;
  background-color: #f8f8f8;
}
.my-red-packets .tabs {
  display: flex;
  background-color: #fff;
  padding: 20rpx 0;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 1;
}
.my-red-packets .tabs .tab-item {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
  padding: 20rpx 0;
}
.my-red-packets .tabs .tab-item.active {
  color: #FF6347;
  font-weight: 500;
}
.my-red-packets .tabs .tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #FF6347;
  border-radius: 2rpx;
}
.my-red-packets .red-packet-list {
  height: calc(100vh - 100rpx);
}
.my-red-packets .red-packet-list .red-packet-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #fff;
  margin-bottom: 2rpx;
}
.my-red-packets .red-packet-list .red-packet-item .red-packet-info {
  flex: 1;
}
.my-red-packets .red-packet-list .red-packet-item .red-packet-info .title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.my-red-packets .red-packet-list .red-packet-item .red-packet-info .time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}
.my-red-packets .red-packet-list .red-packet-item .red-packet-info .amount {
  font-size: 32rpx;
  color: #FF6347;
  font-weight: 500;
}
.my-red-packets .red-packet-list .red-packet-item .status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}
.my-red-packets .red-packet-list .red-packet-item .status.pending {
  color: #FF6347;
  background-color: #FFF0F5;
}
.my-red-packets .red-packet-list .red-packet-item .status.received {
  color: #52c41a;
  background-color: #f6ffed;
}
.my-red-packets .red-packet-list .red-packet-item .status.expired {
  color: #999;
  background-color: #f5f5f5;
}
.my-red-packets .red-packet-list .red-packet-item .status.completed {
  color: #1890ff;
  background-color: #e6f7ff;
}
.my-red-packets .red-packet-list .loading, .my-red-packets .red-packet-list .no-more {
  text-align: center;
  padding: 30rpx;
  color: #999;
  font-size: 24rpx;
}
.my-red-packets .red-packet-list .empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}
.my-red-packets .red-packet-list .empty image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.my-red-packets .red-packet-list .empty text {
  font-size: 28rpx;
  color: #999;
}