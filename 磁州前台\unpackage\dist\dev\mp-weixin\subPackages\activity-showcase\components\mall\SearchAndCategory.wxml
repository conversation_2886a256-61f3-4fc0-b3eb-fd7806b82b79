<view class="search-category-container data-v-779f5f92"><view class="search-box data-v-779f5f92" bindtap="{{g}}"><view class="search-input data-v-779f5f92"><svg wx:if="{{b}}" u-s="{{['d']}}" class="search-icon data-v-779f5f92" u-i="779f5f92-0" bind:__l="__l" u-p="{{b}}"><path wx:if="{{a}}" class="data-v-779f5f92" u-i="779f5f92-1,779f5f92-0" bind:__l="__l" u-p="{{a}}"></path></svg><text class="placeholder data-v-779f5f92">搜索磁州商品、店铺</text></view><view class="scan-btn data-v-779f5f92" catchtap="{{f}}"><svg wx:if="{{e}}" u-s="{{['d']}}" class="scan-icon data-v-779f5f92" u-i="779f5f92-2" bind:__l="__l" u-p="{{e}}"><path wx:if="{{c}}" class="data-v-779f5f92" u-i="779f5f92-3,779f5f92-2" bind:__l="__l" u-p="{{c}}"></path><rect wx:if="{{d}}" class="data-v-779f5f92" u-i="779f5f92-4,779f5f92-2" bind:__l="__l" u-p="{{d}}"></rect></svg></view></view><view class="category-grid data-v-779f5f92"><view wx:for="{{h}}" wx:for-item="category" wx:key="e" class="{{['category-item', 'data-v-779f5f92', category.f && 'active']}}" bindtap="{{category.g}}"><view class="category-icon-wrapper data-v-779f5f92"><view class="category-icon data-v-779f5f92" style="{{'background:' + category.b}}"><image class="icon-image data-v-779f5f92" src="{{category.a}}" mode="aspectFit"></image></view><view wx:if="{{category.c}}" class="category-indicator data-v-779f5f92"></view></view><text class="category-name data-v-779f5f92">{{category.d}}</text></view></view></view>