"use strict";
const userProfile = {
  id: "user-001",
  nickname: "磁州居民",
  avatar: "/static/images/tabbar/user-blue.png",
  gender: 1,
  // 1-男，2-女，0-未设置
  phone: "139****5678",
  email: "<EMAIL>",
  birthday: "1990-01-01",
  level: 2,
  points: 520,
  isVip: true,
  vipExpiry: "2024-12-31",
  registerTime: "2023-01-15",
  location: "磁县",
  address: "磁县城区幸福路123号",
  signature: "热爱生活，热爱磁州",
  follows: 28,
  fans: 15,
  likes: 120,
  collections: 45,
  publishCount: 32,
  interests: ["美食", "旅游", "电影", "音乐"]
};
const userStats = {
  publishStats: {
    total: 32,
    infoCount: 15,
    businessCount: 8,
    jobCount: 5,
    houseCount: 4
  },
  interactionStats: {
    likes: 120,
    comments: 85,
    shares: 36,
    collections: 45
  },
  activityStats: {
    participated: 12,
    organized: 3,
    upcoming: 2
  }
};
const fetchUserProfile = (userId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(userProfile);
    }, 300);
  });
};
const fetchUserStats = (userId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(userStats);
    }, 300);
  });
};
const updateUserProfile = (data) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const updatedProfile = {
        ...userProfile,
        ...data
      };
      resolve({
        success: true,
        data: updatedProfile
      });
    }, 500);
  });
};
exports.fetchUserProfile = fetchUserProfile;
exports.fetchUserStats = fetchUserStats;
exports.updateUserProfile = updateUserProfile;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/mock/user/profile.js.map
