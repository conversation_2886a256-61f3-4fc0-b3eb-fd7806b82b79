"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      discounts: [],
      // 会员折扣列表
      discountForm: {
        id: "",
        name: "",
        color: "#FF6B22",
        // 默认橙色
        icon: "",
        value: "",
        scope: "",
        applicableLevels: [],
        description: ""
      },
      isEditing: false,
      // 是否为编辑模式
      currentDiscountId: null,
      // 当前编辑的折扣ID
      colorOptions: [
        "#FF6B22",
        // 橙色
        "#8A2BE2",
        // 紫色
        "#1E90FF",
        // 道奇蓝
        "#32CD32",
        // 酸橙绿
        "#FFD700",
        // 金色
        "#FF69B4",
        // 热粉红
        "#20B2AA",
        // 浅海绿
        "#FF8C00"
        // 深橙色
      ],
      discountScopes: [
        "全部商品",
        "指定分类商品",
        "指定商品",
        "会员专享商品"
      ],
      memberLevels: []
      // 会员等级列表
    };
  },
  onLoad() {
    this.fetchDiscounts();
    this.fetchMemberLevels();
  },
  methods: {
    // 获取会员折扣列表
    fetchDiscounts() {
      this.discounts = [
        {
          id: "1",
          name: "银卡会员折扣",
          color: "#C0C0C0",
          icon: "/static/images/discount-silver.svg",
          value: "9.5折",
          scope: "全部商品",
          applicableLevels: ["银卡会员"],
          description: "银卡会员购买全部商品享受9.5折优惠"
        },
        {
          id: "2",
          name: "金卡会员折扣",
          color: "#FFD700",
          icon: "/static/images/discount-gold.svg",
          value: "9折",
          scope: "全部商品",
          applicableLevels: ["金卡会员"],
          description: "金卡会员购买全部商品享受9折优惠"
        },
        {
          id: "3",
          name: "钻石会员折扣",
          color: "#B9F2FF",
          icon: "/static/images/discount-diamond.svg",
          value: "8.5折",
          scope: "全部商品",
          applicableLevels: ["钻石会员"],
          description: "钻石会员购买全部商品享受8.5折优惠"
        }
      ];
    },
    // 获取会员等级列表
    fetchMemberLevels() {
      this.memberLevels = [
        {
          id: "1",
          name: "普通会员"
        },
        {
          id: "2",
          name: "银卡会员"
        },
        {
          id: "3",
          name: "金卡会员"
        },
        {
          id: "4",
          name: "钻石会员"
        }
      ];
    },
    // 显示添加折扣弹窗
    showAddDiscountModal() {
      this.isEditing = false;
      this.discountForm = {
        id: "",
        name: "",
        color: "#FF6B22",
        icon: "",
        value: "",
        scope: "",
        applicableLevels: [],
        description: ""
      };
      this.$refs.discountFormPopup.open();
    },
    // 编辑折扣
    editDiscount(discount) {
      this.isEditing = true;
      this.currentDiscountId = discount.id;
      this.discountForm = JSON.parse(JSON.stringify(discount));
      this.$refs.discountFormPopup.open();
    },
    // 关闭折扣表单弹窗
    closeDiscountModal() {
      this.$refs.discountFormPopup.close();
    },
    // 保存折扣表单
    saveDiscountForm() {
      if (!this.discountForm.name) {
        common_vendor.index.showToast({
          title: "请输入折扣名称",
          icon: "none"
        });
        return;
      }
      if (!this.discountForm.value) {
        common_vendor.index.showToast({
          title: "请输入折扣力度",
          icon: "none"
        });
        return;
      }
      if (!this.discountForm.scope) {
        common_vendor.index.showToast({
          title: "请选择适用范围",
          icon: "none"
        });
        return;
      }
      if (this.discountForm.applicableLevels.length === 0) {
        common_vendor.index.showToast({
          title: "请选择适用等级",
          icon: "none"
        });
        return;
      }
      if (this.isEditing) {
        const index = this.discounts.findIndex((item) => item.id === this.currentDiscountId);
        if (index !== -1) {
          this.discounts.splice(index, 1, JSON.parse(JSON.stringify(this.discountForm)));
        }
      } else {
        this.discountForm.id = Date.now().toString();
        this.discounts.push(JSON.parse(JSON.stringify(this.discountForm)));
      }
      this.closeDiscountModal();
      common_vendor.index.showToast({
        title: this.isEditing ? "折扣修改成功" : "折扣添加成功"
      });
    },
    // 确认删除折扣
    confirmDeleteDiscount(discount) {
      this.currentDiscountId = discount.id;
      this.$refs.deleteConfirmPopup.open();
    },
    // 删除折扣
    deleteDiscount() {
      const index = this.discounts.findIndex((item) => item.id === this.currentDiscountId);
      if (index !== -1) {
        this.discounts.splice(index, 1);
      }
      this.$refs.deleteConfirmPopup.close();
      common_vendor.index.showToast({
        title: "折扣删除成功"
      });
    },
    // 关闭删除确认弹窗
    closeDeleteConfirm() {
      this.$refs.deleteConfirmPopup.close();
    },
    // 选择图标
    chooseIcon() {
      common_vendor.index.chooseImage({
        count: 1,
        success: (res) => {
          this.discountForm.icon = res.tempFilePaths[0];
        }
      });
    },
    // 选择范围变更
    onScopeChange(e) {
      const index = e.detail.value;
      this.discountForm.scope = this.discountScopes[index];
    },
    // 判断等级是否被选中
    isLevelSelected(levelName) {
      return this.discountForm.applicableLevels.includes(levelName);
    },
    // 切换等级选择
    toggleLevelSelection(levelName) {
      const index = this.discountForm.applicableLevels.indexOf(levelName);
      if (index === -1) {
        this.discountForm.applicableLevels.push(levelName);
      } else {
        this.discountForm.applicableLevels.splice(index, 1);
      }
    }
  }
};
if (!Array) {
  const _component_uni_popup = common_vendor.resolveComponent("uni-popup");
  const _component_uni_popup_dialog = common_vendor.resolveComponent("uni-popup-dialog");
  (_component_uni_popup + _component_uni_popup_dialog)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.showAddDiscountModal && $options.showAddDiscountModal(...args)),
    b: $data.discounts.length === 0
  }, $data.discounts.length === 0 ? {
    c: common_assets._imports_0$46
  } : {
    d: common_vendor.f($data.discounts, (discount, index, i0) => {
      return {
        a: common_vendor.t(discount.name),
        b: common_vendor.o(($event) => $options.editDiscount(discount), index),
        c: common_vendor.o(($event) => $options.confirmDeleteDiscount(discount), index),
        d: discount.color,
        e: discount.icon,
        f: common_vendor.f(discount.applicableLevels, (level, idx, i1) => {
          return {
            a: common_vendor.t(level),
            b: idx
          };
        }),
        g: common_vendor.t(discount.value),
        h: common_vendor.t(discount.scope),
        i: common_vendor.t(discount.description || "暂无折扣说明"),
        j: index
      };
    })
  }, {
    e: common_vendor.t($data.isEditing ? "编辑折扣" : "添加折扣"),
    f: common_vendor.o((...args) => $options.closeDiscountModal && $options.closeDiscountModal(...args)),
    g: $data.discountForm.name,
    h: common_vendor.o(($event) => $data.discountForm.name = $event.detail.value),
    i: common_vendor.f($data.colorOptions, (color, idx, i0) => {
      return {
        a: idx,
        b: $data.discountForm.color === color ? 1 : "",
        c: color,
        d: common_vendor.o(($event) => $data.discountForm.color = color, idx)
      };
    }),
    j: $data.discountForm.icon
  }, $data.discountForm.icon ? {
    k: $data.discountForm.icon
  } : {
    l: common_vendor.o((...args) => $options.chooseIcon && $options.chooseIcon(...args))
  }, {
    m: $data.discountForm.value,
    n: common_vendor.o(($event) => $data.discountForm.value = $event.detail.value),
    o: common_vendor.t($data.discountForm.scope || "请选择适用范围"),
    p: $data.discountScopes,
    q: common_vendor.o((...args) => $options.onScopeChange && $options.onScopeChange(...args)),
    r: common_vendor.f($data.memberLevels, (level, idx, i0) => {
      return {
        a: common_vendor.t($options.isLevelSelected(level.name) ? "✓" : ""),
        b: common_vendor.t(level.name),
        c: idx,
        d: $options.isLevelSelected(level.name) ? 1 : "",
        e: common_vendor.o(($event) => $options.toggleLevelSelection(level.name), idx)
      };
    }),
    s: $data.discountForm.description,
    t: common_vendor.o(($event) => $data.discountForm.description = $event.detail.value),
    v: common_vendor.o((...args) => $options.closeDiscountModal && $options.closeDiscountModal(...args)),
    w: common_vendor.o((...args) => $options.saveDiscountForm && $options.saveDiscountForm(...args)),
    x: common_vendor.sr("discountFormPopup", "4f90d1d5-0"),
    y: common_vendor.p({
      type: "center"
    }),
    z: common_vendor.o($options.deleteDiscount),
    A: common_vendor.o($options.closeDeleteConfirm),
    B: common_vendor.p({
      type: "warning",
      title: "删除确认",
      content: "确定要删除该会员折扣吗？删除后将无法恢复。",
      ["before-close"]: true
    }),
    C: common_vendor.sr("deleteConfirmPopup", "4f90d1d5-1"),
    D: common_vendor.p({
      type: "dialog"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-4f90d1d5"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/member/member-discount.js.map
