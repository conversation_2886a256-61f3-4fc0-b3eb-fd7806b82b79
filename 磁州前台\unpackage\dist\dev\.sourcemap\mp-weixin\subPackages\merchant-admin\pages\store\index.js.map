{"version": 3, "file": "index.js", "sources": ["subPackages/merchant-admin/pages/store/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW5ccGFnZXNcc3RvcmVcaW5kZXgudnVl"], "sourcesContent": ["<template>\n  <view class=\"store-entry-container\">\n    <!-- 顶部导航栏 -->\n    <view class=\"header-area\">\n      <!-- 顶部安全区域 -->\n      <view class=\"safe-area-top\"></view>\n      \n      <!-- 自定义导航栏 -->\n      <view class=\"custom-navbar\">\n        <view class=\"navbar-left\" @click=\"goBack\">\n          <image src=\"/static/images/tabbar/最新返回键.png\" class=\"back-icon\" style=\"filter: brightness(0) invert(1);\"></image>\n        </view>\n        <view class=\"navbar-title\">店铺管理</view>\n        <view class=\"navbar-right\">\n          <image src=\"/static/images/tabbar/更多.png\" class=\"more-icon\"></image>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 背景装饰 -->\n    <view class=\"bg-decoration bg-circle-1\"></view>\n    <view class=\"bg-decoration bg-circle-2\"></view>\n    <view class=\"bg-decoration bg-circle-3\"></view>\n    \n    <!-- 店铺信息卡片 -->\n    <view class=\"store-info-card\">\n      <view class=\"store-header\">\n        <view class=\"store-avatar-wrapper\">\n          <image :src=\"storeInfo.avatar || '/static/images/default-store.png'\" mode=\"aspectFill\" class=\"store-avatar\"></image>\n          <view class=\"edit-avatar-btn\" @click=\"editAvatar\">\n            <image src=\"/static/images/tabbar/编辑.png\" mode=\"aspectFit\" class=\"edit-icon\"></image>\n          </view>\n        </view>\n        <view class=\"store-info\">\n          <view class=\"store-name-wrapper\">\n            <text class=\"store-name\">{{storeInfo.name}}</text>\n            <view class=\"store-badge\" v-if=\"storeInfo.isVerified\">已认证</view>\n          </view>\n          <text class=\"store-desc\">{{storeInfo.description || '暂无店铺介绍'}}</text>\n          <view class=\"store-stats\">\n            <view class=\"stat-item\">\n              <text class=\"stat-value\">{{storeInfo.rating}}</text>\n              <text class=\"stat-label\">评分</text>\n            </view>\n            <view class=\"stat-divider\"></view>\n            <view class=\"stat-item\">\n              <text class=\"stat-value\">{{storeInfo.orderCount}}</text>\n              <text class=\"stat-label\">订单</text>\n            </view>\n            <view class=\"stat-divider\"></view>\n            <view class=\"stat-item\">\n              <text class=\"stat-value\">{{storeInfo.visitorCount}}</text>\n              <text class=\"stat-label\">访客</text>\n            </view>\n          </view>\n        </view>\n        <view class=\"preview-btn\" @click=\"previewStore\">\n          <text class=\"preview-text\">预览</text>\n        </view>\n      </view>\n      <view class=\"store-status-bar\">\n        <view class=\"status-indicator\">\n          <view class=\"status-dot\" :class=\"{'active': storeInfo.status === 'open'}\"></view>\n          <text class=\"status-text\">{{storeInfo.status === 'open' ? '营业中' : '休息中'}}</text>\n        </view>\n        <view class=\"operation-hours\">\n          <text>营业时间: {{storeInfo.operationHours}}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 功能区域 -->\n    <scroll-view class=\"content-scroll\" scroll-y>\n      <!-- 基础信息配置 -->\n      <view class=\"function-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">基础信息配置</text>\n          <text class=\"section-subtitle\">设置店铺基本信息以吸引更多顾客</text>\n        </view>\n        \n        <view class=\"function-grid\">\n          <view class=\"function-item\" @click=\"navigateTo('basicInfo')\">\n            <view class=\"icon-wrapper logo-bg\">\n              <image src=\"/static/images/tabbar/店铺信息.png\" mode=\"aspectFit\" class=\"function-icon\"></image>\n            </view>\n            <text class=\"function-name\">店铺信息</text>\n            <text class=\"function-desc\">LOGO、封面图、简介</text>\n          </view>\n          \n          <view class=\"function-item\" @click=\"navigateTo('operationInfo')\">\n            <view class=\"icon-wrapper operation-bg\">\n              <image src=\"/static/images/tabbar/营业信息.png\" mode=\"aspectFit\" class=\"function-icon\"></image>\n            </view>\n            <text class=\"function-name\">经营信息</text>\n            <text class=\"function-desc\">营业时间、配送范围</text>\n          </view>\n          \n          <view class=\"function-item\" @click=\"navigateTo('locationInfo')\">\n            <view class=\"icon-wrapper location-bg\">\n              <image src=\"/static/images/tabbar/位置信息.png\" mode=\"aspectFit\" class=\"function-icon\"></image>\n            </view>\n            <text class=\"function-name\">位置管理</text>\n            <text class=\"function-desc\">门店地址、导航设置</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 商品服务管理 -->\n      <view class=\"function-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">商品服务管理</text>\n          <text class=\"section-subtitle\">管理您店铺提供的商品与服务</text>\n        </view>\n        \n        <view class=\"function-grid\">\n          <view class=\"function-item\" @click=\"navigateTo('categoryManage')\">\n            <view class=\"icon-wrapper category-bg\">\n              <image src=\"/static/images/tabbar/分类管理.png\" mode=\"aspectFit\" class=\"function-icon\"></image>\n            </view>\n            <text class=\"function-name\">分类管理</text>\n            <text class=\"function-desc\">自定义商品分类体系</text>\n          </view>\n          \n          <view class=\"function-item\" @click=\"navigateTo('productManage')\">\n            <view class=\"icon-wrapper product-bg\">\n              <image src=\"/static/images/tabbar/商品管理.png\" mode=\"aspectFit\" class=\"function-icon\"></image>\n            </view>\n            <text class=\"function-name\">商品管理</text>\n            <text class=\"function-desc\">批量上传、SKU管理</text>\n          </view>\n          \n          <view class=\"function-item\" @click=\"navigateTo('serviceManage')\">\n            <view class=\"icon-wrapper service-bg\">\n              <image src=\"/static/images/tabbar/服务管理.png\" mode=\"aspectFit\" class=\"function-icon\"></image>\n            </view>\n            <text class=\"function-name\">服务项目</text>\n            <text class=\"function-desc\">服务定价、服务说明</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 门店形象管理 -->\n      <view class=\"function-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">门店形象管理</text>\n          <text class=\"section-subtitle\">打造专业店铺形象，提升用户信任度</text>\n        </view>\n        \n        <view class=\"function-grid\">\n          <view class=\"function-item\" @click=\"navigateTo('storeAlbum')\">\n            <view class=\"icon-wrapper album-bg\">\n              <image src=\"/static/images/tabbar/店铺相册.png\" mode=\"aspectFit\" class=\"function-icon\"></image>\n            </view>\n            <text class=\"function-name\">店铺相册</text>\n            <text class=\"function-desc\">环境展示、产品展示</text>\n          </view>\n          \n          <view class=\"function-item\" @click=\"navigateTo('videoManage')\">\n            <view class=\"icon-wrapper video-bg\">\n              <image src=\"/static/images/tabbar/视频展示.png\" mode=\"aspectFit\" class=\"function-icon\"></image>\n            </view>\n            <text class=\"function-name\">视频展示</text>\n            <text class=\"function-desc\">店铺宣传视频、服务展示</text>\n          </view>\n          \n          <view class=\"function-item\" @click=\"navigateTo('storeCulture')\">\n            <view class=\"icon-wrapper culture-bg\">\n              <image src=\"/static/images/tabbar/商家故事.png\" mode=\"aspectFit\" class=\"function-icon\"></image>\n            </view>\n            <text class=\"function-name\">商家故事</text>\n            <text class=\"function-desc\">品牌文化、特色介绍</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 认证与资质管理 -->\n      <view class=\"function-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">认证与资质管理</text>\n          <text class=\"section-subtitle\">提升店铺可信度，获得更多顾客信任</text>\n        </view>\n        \n        <view class=\"function-grid\">\n          <view class=\"function-item\" @click=\"navigateTo('storeVerify')\">\n            <view class=\"icon-wrapper verify-bg\">\n              <image src=\"/static/images/tabbar/商家认证.png\" mode=\"aspectFit\" class=\"function-icon\"></image>\n            </view>\n            <text class=\"function-name\">商家认证</text>\n            <text class=\"function-desc\">企业/个体工商户认证</text>\n          </view>\n          \n          <view class=\"function-item\" @click=\"navigateTo('qualificationManage')\">\n            <view class=\"icon-wrapper qualification-bg\">\n              <image src=\"/static/images/tabbar/资质管理.png\" mode=\"aspectFit\" class=\"function-icon\"></image>\n            </view>\n            <text class=\"function-name\">资质管理</text>\n            <text class=\"function-desc\">经营许可证、行业资质</text>\n          </view>\n          \n          <view class=\"function-item\" @click=\"navigateTo('qualificationRemind')\">\n            <view class=\"icon-wrapper remind-bg\">\n              <image src=\"/static/images/tabbar/提醒管理.png\" mode=\"aspectFit\" class=\"function-icon\"></image>\n            </view>\n            <text class=\"function-name\">到期提醒</text>\n            <text class=\"function-desc\">资质到期提醒与续期</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 我的活动管理 -->\n      <view class=\"function-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">我的活动管理</text>\n          <text class=\"section-subtitle\">管理您发布的营销活动</text>\n        </view>\n        \n        <view class=\"function-grid\">\n          <view class=\"function-item\" @click=\"navigateTo('activityList')\">\n            <view class=\"icon-wrapper activity-bg\">\n              <image src=\"/static/images/tabbar/活动列表.png\" mode=\"aspectFit\" class=\"function-icon\"></image>\n            </view>\n            <text class=\"function-name\">活动列表</text>\n            <text class=\"function-desc\">已发布活动状态管理</text>\n          </view>\n          \n          <view class=\"function-item\" @click=\"navigateTo('activityOperation')\">\n            <view class=\"icon-wrapper operation-bg\">\n              <image src=\"/static/images/tabbar/活动操作.png\" mode=\"aspectFit\" class=\"function-icon\"></image>\n            </view>\n            <text class=\"function-name\">活动操作</text>\n            <text class=\"function-desc\">续费、置顶、刷新</text>\n          </view>\n          \n          <view class=\"function-item\" @click=\"navigateTo('activityData')\">\n            <view class=\"icon-wrapper data-bg\">\n              <image src=\"/static/images/tabbar/活动数据.png\" mode=\"aspectFit\" class=\"function-icon\"></image>\n            </view>\n            <text class=\"function-name\">活动数据</text>\n            <text class=\"function-desc\">浏览量、转化率分析</text>\n          </view>\n        </view>\n      </view>\n    </scroll-view>\n    \n    <!-- 添加底部导航栏，与商家中心页面保持一致 -->\n    <view class=\"tab-bar\">\n      <view \n        class=\"tab-item\" \n        v-for=\"(tab, index) in tabItems\" \n        :key=\"index\"\n        :class=\"{ active: currentTab === tab.id }\"\n        @tap=\"switchTab(tab.id)\">\n        <view class=\"active-indicator\" v-if=\"currentTab === tab.id\"></view>\n        <view class=\"tab-icon\" :class=\"tab.icon\"></view>\n        <text class=\"tab-text\">{{ tab.text }}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      storeInfo: {\n        avatar: '/static/images/store-avatar.png',\n        name: '磁州小吃美食店',\n        description: '专注本地特色小吃，用心制作每一道美食',\n        rating: 4.8,\n        orderCount: 2580,\n        viewCount: 12560,\n        followerCount: 680,\n        labels: ['特色小吃', '本地美食', '堂食', '外卖', '预订'],\n        status: 'open',\n        operationHours: '09:00-21:00',\n        isVerified: true\n      },\n      categories: [\n        { id: 1, name: '招牌菜', count: 12 },\n        { id: 2, name: '凉菜', count: 8 },\n        { id: 3, name: '热菜', count: 15 },\n        { id: 4, name: '主食', count: 6 },\n        { id: 5, name: '汤类', count: 4 },\n        { id: 6, name: '饮品', count: 7 }\n      ],\n      menuItems: [\n        { id: 101, name: '铁板鱿鱼', price: 38, sales: 256, image: '/static/images/dish1.png', category: 1 },\n        { id: 102, name: '爆炒腰花', price: 42, sales: 198, image: '/static/images/dish2.png', category: 1 },\n        { id: 103, name: '剁椒鱼头', price: 58, sales: 172, image: '/static/images/dish3.png', category: 1 },\n        { id: 104, name: '凉拌海蜇', price: 22, sales: 118, image: '/static/images/dish4.png', category: 2 },\n        { id: 105, name: '手撕包菜', price: 18, sales: 167, image: '/static/images/dish5.png', category: 3 }\n      ],\n      currentTab: 1, // 当前选中的标签页（店铺管理）\n      tabItems: [\n        {\n          id: 0,\n          icon: 'dashboard',\n          text: '商家中心',\n          url: '/subPackages/merchant-admin-home/pages/merchant-home/index'\n        },\n        {\n          id: 1,\n          icon: 'store',\n          text: '店铺管理',\n          url: '/subPackages/merchant-admin/pages/store/index'\n        },\n        {\n          id: 2,\n          icon: 'marketing',\n          text: '营销中心',\n          url: '/subPackages/merchant-admin-marketing/pages/marketing/index'\n        },\n        {\n          id: 3,\n          icon: 'orders',\n          text: '订单管理',\n          url: '/subPackages/merchant-admin-order/pages/order/index'\n        },\n        {\n          id: 'more',\n          icon: 'more',\n          text: '更多',\n          url: ''\n        }\n      ]\n    }\n  },\n  onLoad() {\n    // 页面加载完成后的处理\n    this.setStatusBarHeight();\n  },\n  methods: {\n    // 设置状态栏高度\n    setStatusBarHeight() {\n      // 获取系统信息设置状态栏高度\n      uni.getSystemInfo({\n        success: (res) => {\n          this.statusBarHeight = res.statusBarHeight;\n          // 将状态栏高度设置为CSS变量\n          if (typeof document !== 'undefined') {\n            document.documentElement.style.setProperty('--status-bar-height', `${this.statusBarHeight}px`);\n          }\n        }\n      });\n    },\n    \n    // 返回上一页\n    goBack() {\n      uni.navigateBack();\n    },\n    \n    // 编辑头像\n    editAvatar() {\n      uni.chooseImage({\n        count: 1,\n        sizeType: ['compressed'],\n        sourceType: ['album', 'camera'],\n        success: (res) => {\n          // 这里可以添加上传图片的逻辑\n          this.storeInfo.avatar = res.tempFilePaths[0];\n          uni.showToast({\n            title: '头像已更新',\n            icon: 'success'\n          });\n        }\n      });\n    },\n    \n    // 预览店铺\n    previewStore() {\n      uni.showToast({\n        title: '店铺预览功能开发中',\n        icon: 'none'\n      });\n    },\n    \n    // 导航到具体功能页面\n    navigateTo(page) {\n      // 根据不同的页面参数跳转到对应的页面\n      const pageMap = {\n        'basicInfo': '/subPackages/merchant-admin/pages/store/basic-info',\n        'operationInfo': '/subPackages/merchant-admin/pages/store/operation-info',\n        'locationInfo': '/subPackages/merchant-admin/pages/store/location-info',\n        'categoryManage': '/subPackages/merchant-admin/pages/store/category',\n        'productManage': '/subPackages/merchant-admin/pages/store/product',\n        'serviceManage': '/subPackages/merchant-admin/pages/store/service',\n        'storeAlbum': '/subPackages/merchant-admin/pages/store/album',\n        'videoManage': '/subPackages/merchant-admin/pages/store/video',\n        'storeCulture': '/subPackages/merchant-admin/pages/store/culture',\n        'storeVerify': '/subPackages/merchant-admin/pages/store/verify',\n        'qualificationManage': '/subPackages/merchant-admin/pages/store/qualification',\n        'qualificationRemind': '/subPackages/merchant-admin/pages/store/remind',\n        'activityList': '/subPackages/merchant-admin/pages/store/activity-list',\n        'activityOperation': '/subPackages/merchant-admin/pages/store/activity-operation',\n        'activityData': '/subPackages/merchant-admin/pages/store/activity-data'\n      };\n      \n      const url = pageMap[page];\n      if (url) {\n        uni.navigateTo({ url });\n      } else {\n        uni.showToast({\n          title: '功能开发中',\n          icon: 'none'\n        });\n      }\n    },\n    \n    // 切换底部导航栏\n    switchTab(tabId) {\n      // 处理\"更多\"选项\n      if (tabId === 'more') {\n        this.showMoreOptions();\n        return;\n      }\n      \n      if (tabId === this.currentTab) return;\n      \n      this.currentTab = tabId;\n      \n      // 特殊处理店铺管理标签\n      if (tabId === 1) {\n        // 当前已经在店铺管理页面，不需要跳转\n        return;\n      }\n      \n      // 使用redirectTo而不是navigateTo，避免堆栈过多\n      uni.redirectTo({\n        url: this.tabItems[tabId].url,\n        fail: (err) => {\n          console.error('redirectTo失败:', err);\n          // 如果redirectTo失败，尝试使用switchTab\n          uni.switchTab({\n            url: this.tabItems[tabId].url,\n            fail: (switchErr) => {\n              console.error('switchTab也失败:', switchErr);\n              uni.showToast({\n                title: '页面跳转失败，请稍后再试',\n                icon: 'none'\n              });\n            }\n          });\n        }\n      });\n    },\n    \n    // 显示更多选项弹出菜单\n    showMoreOptions() {\n      // 准备更多菜单中的选项\n      const moreOptions = ['客户运营', '分析洞察', '系统设置'];\n      \n      uni.showActionSheet({\n        itemList: moreOptions,\n        success: (res) => {\n          // 根据选择的选项进行跳转\n          const routes = [\n            '/subPackages/merchant-admin-customer/pages/customer/index',\n            '/subPackages/merchant-admin/pages/settings/index'\n          ];\n          uni.navigateTo({\n            url: routes[res.tapIndex]\n          });\n        }\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.store-entry-container {\n  min-height: 100vh;\n  background-color: #F5F8FC;\n  position: relative;\n  padding-top: calc(110rpx + var(--status-bar-height, 20px));\n}\n\n/* 顶部导航栏样式 */\n.header-area {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background-color: #0A84FF;\n  color: #fff;\n}\n\n.safe-area-top {\n  height: var(--status-bar-height, 20px);\n  width: 100%;\n}\n\n.custom-navbar {\n  height: 110rpx;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 30rpx;\n}\n\n.navbar-title {\n  font-size: 36rpx;\n  font-weight: 600;\n}\n\n.navbar-left, .navbar-right {\n  width: 80rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon, .more-icon {\n  width: 48rpx;\n  height: 48rpx;\n}\n\n/* 背景装饰样式 */\n.bg-decoration {\n  position: absolute;\n  z-index: -1;\n  border-radius: 50%;\n  opacity: 0.07;\n  background-color: #0A84FF;\n}\n\n.bg-circle-1 {\n  top: -100rpx;\n  right: -150rpx;\n  width: 400rpx;\n  height: 400rpx;\n}\n\n.bg-circle-2 {\n  top: 10%;\n  left: -200rpx;\n  width: 500rpx;\n  height: 500rpx;\n}\n\n.bg-circle-3 {\n  bottom: 5%;\n  right: -100rpx;\n  width: 300rpx;\n  height: 300rpx;\n}\n\n/* 店铺信息卡片样式 */\n.store-info-card {\n  margin: 30rpx;\n  background-color: #FFFFFF;\n  border-radius: 24rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n  overflow: hidden;\n}\n\n.store-header {\n  padding: 30rpx;\n  display: flex;\n  align-items: center;\n  position: relative;\n}\n\n.store-avatar-wrapper {\n  position: relative;\n}\n\n.store-avatar {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 20rpx;\n  background-color: #f0f0f0;\n  border: 4rpx solid #FFFFFF;\n  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);\n}\n\n.edit-avatar-btn {\n  position: absolute;\n  right: -10rpx;\n  bottom: -10rpx;\n  width: 48rpx;\n  height: 48rpx;\n  background-color: #0A84FF;\n  border-radius: 24rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.edit-icon {\n  width: 24rpx;\n  height: 24rpx;\n  filter: brightness(0) invert(1);\n}\n\n.store-info {\n  flex: 1;\n  padding: 0 20rpx;\n}\n\n.store-name-wrapper {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8rpx;\n}\n\n.store-name {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n  margin-right: 10rpx;\n}\n\n.store-badge {\n  padding: 4rpx 12rpx;\n  background-color: #34C759;\n  color: #FFFFFF;\n  font-size: 20rpx;\n  border-radius: 10rpx;\n}\n\n.store-desc {\n  font-size: 24rpx;\n  color: #666;\n  margin-bottom: 16rpx;\n  height: 34rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.store-stats {\n  display: flex;\n  align-items: center;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.stat-value {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 600;\n}\n\n.stat-label {\n  font-size: 20rpx;\n  color: #999;\n}\n\n.stat-divider {\n  width: 2rpx;\n  height: 30rpx;\n  background-color: #E5E5EA;\n  margin: 0 30rpx;\n}\n\n.preview-btn {\n  position: absolute;\n  right: 30rpx;\n  top: 30rpx;\n  background-color: #0A84FF;\n  color: #FFFFFF;\n  font-size: 24rpx;\n  padding: 8rpx 24rpx;\n  border-radius: 30rpx;\n}\n\n.store-status-bar {\n  padding: 20rpx 30rpx;\n  border-top: 2rpx solid #F2F2F7;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  font-size: 24rpx;\n  color: #666;\n}\n\n.status-indicator {\n  display: flex;\n  align-items: center;\n}\n\n.status-dot {\n  width: 16rpx;\n  height: 16rpx;\n  border-radius: 50%;\n  background-color: #8E8E93;\n  margin-right: 10rpx;\n}\n\n.status-dot.active {\n  background-color: #34C759;\n}\n\n/* 内容滚动区域 */\n.content-scroll {\n  padding: 20rpx 30rpx;\n  padding-bottom: calc(60px + env(safe-area-inset-bottom, 0px) + 120rpx);\n  height: calc(100vh - 280rpx - var(--status-bar-height, 20px));\n}\n\n/* 功能区块样式 */\n.function-section {\n  margin-bottom: 40rpx;\n  background-color: #FFFFFF;\n  border-radius: 24rpx;\n  padding: 30rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.section-header {\n  margin-bottom: 20rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.section-subtitle {\n  font-size: 24rpx;\n  color: #999;\n  margin-top: 6rpx;\n}\n\n.function-grid {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -10rpx;\n}\n\n.function-item {\n  width: calc(33.33% - 20rpx);\n  margin: 10rpx;\n  border-radius: 16rpx;\n  overflow: hidden;\n  background-color: #FFFFFF;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.03);\n  padding: 20rpx 0;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s;\n}\n\n.function-item:active {\n  transform: scale(0.95);\n  opacity: 0.9;\n}\n\n.icon-wrapper {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 16rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n  position: relative;\n  overflow: hidden;\n}\n\n.icon-wrapper::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));\n  border-radius: 50%;\n  z-index: 1;\n}\n\n.function-icon {\n  width: 40rpx;\n  height: 40rpx;\n  filter: brightness(0) invert(1);\n  z-index: 2;\n}\n\n.function-name {\n  font-size: 26rpx;\n  color: #333;\n  font-weight: 500;\n  margin-bottom: 6rpx;\n}\n\n.function-desc {\n  font-size: 20rpx;\n  color: #999;\n  text-align: center;\n  padding: 0 10rpx;\n  height: 28rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  max-width: 100%;\n}\n\n/* 图标背景颜色 */\n.logo-bg {\n  background: linear-gradient(135deg, #1976D2, #64B5F6);\n}\n\n.operation-bg {\n  background: linear-gradient(135deg, #FF9966, #FF5E62);\n}\n\n.location-bg {\n  background: linear-gradient(135deg, #38ADAE, #30CE9B);\n}\n\n.category-bg {\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\n}\n\n.product-bg {\n  background: linear-gradient(135deg, #36D1DC, #5B86E5);\n}\n\n.service-bg {\n  background: linear-gradient(135deg, #FF6B6B, #F04172);\n}\n\n.album-bg {\n  background: linear-gradient(135deg, #FDEB71, #F8D800);\n}\n\n.video-bg {\n  background: linear-gradient(135deg, #8E2DE2, #4A00E0);\n}\n\n.culture-bg {\n  background: linear-gradient(135deg, #795548, #A1887F);\n}\n\n.verify-bg {\n  background: linear-gradient(135deg, #38ADAE, #30CE9B);\n}\n\n.qualification-bg {\n  background: linear-gradient(135deg, #FF5858, #FF0000);\n}\n\n.remind-bg {\n  background: linear-gradient(135deg, #4481EB, #04BEFE);\n}\n\n.activity-bg {\n  background: linear-gradient(135deg, #FF6B6B, #F04172);\n}\n\n.data-bg {\n  background: linear-gradient(135deg, #36D1DC, #5B86E5);\n}\n\n/* 适配暗黑模式 */\n@media (prefers-color-scheme: dark) {\n  .store-entry-container {\n    background-color: #1C1C1E;\n  }\n  \n  .store-info-card,\n  .function-section,\n  .function-item {\n    background-color: #2C2C2E;\n  }\n  \n  .store-name,\n  .section-title,\n  .function-name,\n  .stat-value {\n    color: #FFFFFF;\n  }\n  \n  .store-desc,\n  .section-subtitle,\n  .function-desc,\n  .stat-label,\n  .operation-hours,\n  .status-text {\n    color: #8E8E93;\n  }\n  \n  .store-status-bar {\n    border-top-color: #3A3A3C;\n  }\n}\n\n/* 底部导航栏样式 */\n.tab-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 60px;\n  padding: 0 10px;\n  padding-bottom: env(safe-area-inset-bottom);\n  background-color: #FFFFFF;\n  border-top: 1px solid var(--border-light, #F0F0F0);\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03);\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  flex-shrink: 0;\n}\n\n.tab-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  position: relative;\n  transition: all 0.2s ease;\n  padding: 8px 0;\n  margin: 0 8px;\n}\n\n.tab-icon {\n  width: 24px;\n  height: 24px;\n  margin-bottom: 3px;\n  transition: all 0.25s cubic-bezier(0.3, 0.7, 0.4, 1.5);\n  background-position: center;\n  background-repeat: no-repeat;\n  background-size: 22px;\n  opacity: 0.7;\n}\n\n.tab-text {\n  font-size: 10px;\n  color: var(--text-tertiary, #999999);\n  transition: color 0.2s ease, transform 0.25s ease;\n  transform: scale(0.9);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: 100%;\n  padding: 0 2px;\n}\n\n.active-indicator {\n  position: absolute;\n  top: 0;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 16px;\n  height: 3px;\n  border-radius: 0 0 4px 4px;\n  background: linear-gradient(90deg, var(--brand-primary, #0A84FF), var(--accent-purple, #5E5CE6));\n}\n\n.tab-item.active .tab-icon {\n  transform: translateY(-2px);\n  opacity: 1;\n}\n\n.tab-item.active .tab-text {\n  color: var(--brand-primary, #0A84FF);\n  font-weight: 500;\n  transform: scale(1);\n}\n\n/* 导航图标样式 */\n.tab-icon.dashboard {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z'/%3E%3C/svg%3E\");\n}\n\n.tab-icon.store {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M20 4H4v2h16V4zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6h1zm-9 4H6v-4h6v4z'/%3E%3C/svg%3E\");\n}\n\n.tab-icon.marketing {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z'/%3E%3C/svg%3E\");\n}\n\n.tab-icon.orders {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E\");\n}\n\n.tab-icon.customers {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z'/%3E%3C/svg%3E\");\n}\n\n.tab-icon.analytics {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-5h2v5zm4 0h-2v-3h2v3zm0-5h-2V7h2v2zm4 5h-2V7h2v10z'/%3E%3C/svg%3E\");\n}\n\n.tab-icon.settings {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z'/%3E%3C/svg%3E\");\n}\n\n.tab-icon.more {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z'/%3E%3C/svg%3E\");\n}\n\n@media screen and (max-width: 375px) {\n  /* 在较小屏幕上优化导航栏 */\n  .tab-text {\n    font-size: 9px;\n  }\n  \n  .tab-icon {\n    margin-bottom: 2px;\n    background-size: 20px;\n  }\n}\n\n/* 在较宽屏幕上优化导航栏，确保元素不会挤压 */\n@media screen and (min-width: 400px) {\n  .tab-bar {\n    padding: 0 10px;\n  }\n  \n  .tab-item {\n    margin: 0 5px;\n  }\n}\n\n/* 添加底部安全区域 */\n.safe-area-bottom {\n  height: calc(60px + env(safe-area-inset-bottom));\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin/pages/store/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAqQA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,QACT,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,eAAe;AAAA,QACf,QAAQ,CAAC,QAAQ,QAAQ,MAAM,MAAM,IAAI;AAAA,QACzC,QAAQ;AAAA,QACR,gBAAgB;AAAA,QAChB,YAAY;AAAA,MACb;AAAA,MACD,YAAY;AAAA,QACV,EAAE,IAAI,GAAG,MAAM,OAAO,OAAO,GAAI;AAAA,QACjC,EAAE,IAAI,GAAG,MAAM,MAAM,OAAO,EAAG;AAAA,QAC/B,EAAE,IAAI,GAAG,MAAM,MAAM,OAAO,GAAI;AAAA,QAChC,EAAE,IAAI,GAAG,MAAM,MAAM,OAAO,EAAG;AAAA,QAC/B,EAAE,IAAI,GAAG,MAAM,MAAM,OAAO,EAAG;AAAA,QAC/B,EAAE,IAAI,GAAG,MAAM,MAAM,OAAO,EAAE;AAAA,MAC/B;AAAA,MACD,WAAW;AAAA,QACT,EAAE,IAAI,KAAK,MAAM,QAAQ,OAAO,IAAI,OAAO,KAAK,OAAO,4BAA4B,UAAU,EAAG;AAAA,QAChG,EAAE,IAAI,KAAK,MAAM,QAAQ,OAAO,IAAI,OAAO,KAAK,OAAO,4BAA4B,UAAU,EAAG;AAAA,QAChG,EAAE,IAAI,KAAK,MAAM,QAAQ,OAAO,IAAI,OAAO,KAAK,OAAO,4BAA4B,UAAU,EAAG;AAAA,QAChG,EAAE,IAAI,KAAK,MAAM,QAAQ,OAAO,IAAI,OAAO,KAAK,OAAO,4BAA4B,UAAU,EAAG;AAAA,QAChG,EAAE,IAAI,KAAK,MAAM,QAAQ,OAAO,IAAI,OAAO,KAAK,OAAO,4BAA4B,UAAU,EAAE;AAAA,MAChG;AAAA,MACD,YAAY;AAAA;AAAA,MACZ,UAAU;AAAA,QACR;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAEP,SAAK,mBAAkB;AAAA,EACxB;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,qBAAqB;AAEnBA,oBAAAA,MAAI,cAAc;AAAA,QAChB,SAAS,CAAC,QAAQ;AAChB,eAAK,kBAAkB,IAAI;AAE3B,cAAI,OAAO,aAAa,aAAa;AACnC,qBAAS,gBAAgB,MAAM,YAAY,uBAAuB,GAAG,KAAK,eAAe,IAAI;AAAA,UAC/F;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA;AAAA,IAGD,aAAa;AACXA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAEhB,eAAK,UAAU,SAAS,IAAI,cAAc,CAAC;AAC3CA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,eAAe;AACbA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,WAAW,MAAM;AAEf,YAAM,UAAU;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,kBAAkB;AAAA,QAClB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,uBAAuB;AAAA,QACvB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,QAChB,qBAAqB;AAAA,QACrB,gBAAgB;AAAA;AAGlB,YAAM,MAAM,QAAQ,IAAI;AACxB,UAAI,KAAK;AACPA,sBAAAA,MAAI,WAAW,EAAE,IAAE,CAAG;AAAA,aACjB;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,UAAU,OAAO;AAEf,UAAI,UAAU,QAAQ;AACpB,aAAK,gBAAe;AACpB;AAAA,MACF;AAEA,UAAI,UAAU,KAAK;AAAY;AAE/B,WAAK,aAAa;AAGlB,UAAI,UAAU,GAAG;AAEf;AAAA,MACF;AAGAA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,KAAK,SAAS,KAAK,EAAE;AAAA,QAC1B,MAAM,CAAC,QAAQ;AACbA,wBAAA,MAAA,MAAA,SAAA,2DAAc,iBAAiB,GAAG;AAElCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,KAAK,KAAK,SAAS,KAAK,EAAE;AAAA,YAC1B,MAAM,CAAC,cAAc;AACnBA,4BAAA,MAAA,MAAA,SAAA,2DAAc,iBAAiB,SAAS;AACxCA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,kBAAkB;AAEhB,YAAM,cAAc,CAAC,QAAQ,QAAQ,MAAM;AAE3CA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU;AAAA,QACV,SAAS,CAAC,QAAQ;AAEhB,gBAAM,SAAS;AAAA,YACb;AAAA,YACA;AAAA;AAEFA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK,OAAO,IAAI,QAAQ;AAAA,UAC1B,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjdA,GAAG,WAAW,eAAe;"}