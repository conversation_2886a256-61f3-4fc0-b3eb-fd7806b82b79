<template>
  <view class="history-container">
    <!-- 自定义导航栏 -->
    <cu-custom bgColor="bg-gradient-blue" isBack>
      <block slot="backText"></block>
      <block slot="content">提现记录</block>
    </cu-custom>

    <!-- 筛选区域 -->
    <view class="filter-section">
      <view class="filter-tabs">
        <view 
          class="tab-item" 
          :class="{'active': currentStatus === 'all'}"
          @tap="switchStatus('all')"
        >全部</view>
        <view 
          class="tab-item" 
          :class="{'active': currentStatus === 'pending'}"
          @tap="switchStatus('pending')"
        >审核中</view>
        <view 
          class="tab-item" 
          :class="{'active': currentStatus === 'processing'}"
          @tap="switchStatus('processing')"
        >处理中</view>
        <view 
          class="tab-item" 
          :class="{'active': currentStatus === 'success'}"
          @tap="switchStatus('success')"
        >已到账</view>
        <view 
          class="tab-item" 
          :class="{'active': currentStatus === 'failed'}"
          @tap="switchStatus('failed')"
        >已失败</view>
      </view>
      <view class="filter-date">
        <picker mode="date" fields="month" :value="currentMonth" @change="dateChange">
          <view class="date-picker">
            <text>{{currentMonthText}}</text>
            <text class="cuIcon-calendar"></text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 记录列表 -->
    <view class="history-list" v-if="filteredList.length > 0">
      <view 
        class="history-item" 
        v-for="(item, index) in filteredList" 
        :key="index"
        @tap="viewDetail(item)"
      >
        <view class="item-header">
          <view class="header-left">
            <text class="withdraw-type">{{item.method === 'wechat' ? '微信提现' : item.method === 'alipay' ? '支付宝提现' : '银行卡提现'}}</text>
            <text class="withdraw-time">{{formatTime(item.time)}}</text>
          </view>
          <view class="header-right">
            <text class="withdraw-status" :class="'status-' + item.status">{{statusText[item.status]}}</text>
          </view>
        </view>
        <view class="item-content">
          <view class="content-left">
            <view class="withdraw-amount">¥ {{item.amount.toFixed(2)}}</view>
            <view class="withdraw-fee">手续费: ¥ {{item.fee.toFixed(2)}}</view>
          </view>
          <view class="content-right">
            <text class="actual-amount">实际到账: ¥ {{(item.amount - item.fee).toFixed(2)}}</text>
            <text class="detail-btn">详情 <text class="cuIcon-right"></text></text>
          </view>
        </view>
        <view class="item-footer" v-if="item.status === 'failed'">
          <text class="fail-reason">失败原因: {{item.failReason || '信息有误，请修改后重新提交'}}</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <image src="/static/images/no-record.png" mode="aspectFit"></image>
      <text>暂无提现记录</text>
      <button class="go-withdraw" @tap="goWithdraw">去提现</button>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" v-if="filteredList.length > 0 && hasMore">
      <text @tap="loadMore" v-if="!isLoading">加载更多</text>
      <text v-else>加载中...</text>
    </view>

    <view class="load-end" v-if="filteredList.length > 0 && !hasMore">
      <text>- 已经到底了 -</text>
    </view>

    <!-- 详情弹窗 -->
    <view class="detail-modal" v-if="showDetail">
      <view class="modal-mask" @tap="closeDetail"></view>
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">提现详情</text>
          <text class="close-icon cuIcon-close" @tap="closeDetail"></text>
        </view>

        <view class="detail-status">
          <view class="status-icon" :class="'icon-' + currentDetail.status">
            <text class="cuIcon-check" v-if="currentDetail.status === 'success'"></text>
            <text class="cuIcon-time" v-if="currentDetail.status === 'pending' || currentDetail.status === 'processing'"></text>
            <text class="cuIcon-close" v-if="currentDetail.status === 'failed'"></text>
          </view>
          <view class="status-text">{{statusText[currentDetail.status]}}</view>
          <view class="status-desc" v-if="currentDetail.status === 'pending'">预计1-3个工作日内审核完成</view>
          <view class="status-desc" v-if="currentDetail.status === 'processing'">预计24小时内到账</view>
          <view class="status-desc" v-if="currentDetail.status === 'failed'">{{currentDetail.failReason || '信息有误，请修改后重新提交'}}</view>
        </view>

        <view class="detail-info">
          <view class="info-item">
            <text class="info-label">提现金额</text>
            <text class="info-value">¥ {{currentDetail.amount?.toFixed(2)}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">手续费</text>
            <text class="info-value">¥ {{currentDetail.fee?.toFixed(2)}}</text>
          </view>
          <view class="info-item highlight">
            <text class="info-label">实际到账</text>
            <text class="info-value">¥ {{(currentDetail.amount - currentDetail.fee)?.toFixed(2)}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">提现方式</text>
            <text class="info-value">{{currentDetail.method === 'wechat' ? '微信' : currentDetail.method === 'alipay' ? '支付宝' : '银行卡'}}</text>
          </view>
          <view class="info-item" v-if="currentDetail.method === 'alipay'">
            <text class="info-label">支付宝账号</text>
            <text class="info-value">{{currentDetail.account}}</text>
          </view>
          <view class="info-item" v-if="currentDetail.method === 'bank'">
            <text class="info-label">银行卡信息</text>
            <text class="info-value">{{currentDetail.bankName}} ({{maskBankCard(currentDetail.bankCard)}})</text>
          </view>
          <view class="info-item">
            <text class="info-label">申请时间</text>
            <text class="info-value">{{formatDetailTime(currentDetail.time)}}</text>
          </view>
          <view class="info-item" v-if="currentDetail.auditTime">
            <text class="info-label">审核时间</text>
            <text class="info-value">{{formatDetailTime(currentDetail.auditTime)}}</text>
          </view>
          <view class="info-item" v-if="currentDetail.completeTime">
            <text class="info-label">到账时间</text>
            <text class="info-value">{{formatDetailTime(currentDetail.completeTime)}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">流水号</text>
            <text class="info-value">{{currentDetail.orderNo}}</text>
          </view>
        </view>

        <view class="detail-footer" v-if="currentDetail.status === 'failed'">
          <button class="retry-btn" @tap="retryWithdraw">重新提现</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';

// 响应式状态
const currentStatus = ref('all'); // 当前选中的状态
const currentMonth = ref(''); // 当前选中的月份
const currentMonthText = ref('全部时间'); // 当前选中的月份显示文本
const historyList = ref([]); // 提现记录列表
const page = ref(1); // 当前页码
const pageSize = ref(10); // 每页条数
const hasMore = ref(true); // 是否有更多数据
const isLoading = ref(false); // 是否正在加载
const showDetail = ref(false); // 是否显示详情弹窗
const currentDetail = reactive({}); // 当前查看的详情

// 状态文本映射
const statusText = {
  'success': '已到账',
  'pending': '审核中',
  'processing': '处理中',
  'failed': '提现失败'
};

// 计算属性
const filteredList = computed(() => {
  let result = [...historyList.value];
  
  // 按状态筛选
  if (currentStatus.value !== 'all') {
    result = result.filter(item => item.status === currentStatus.value);
  }
  
  // 按月份筛选
  if (currentMonth.value) {
    const [year, month] = currentMonth.value.split('-');
    result = result.filter(item => {
      const date = new Date(item.time);
      return date.getFullYear() === parseInt(year) && date.getMonth() + 1 === parseInt(month);
    });
  }
  
  return result;
});

// 生命周期钩子
onMounted(() => {
  // 初始化加载数据
  loadData();
});

// 方法
// 切换状态
const switchStatus = (status) => {
  currentStatus.value = status;
};

// 日期变化
const dateChange = (e) => {
  const value = e.detail.value;
  currentMonth.value = value;
  
  if (value) {
    const [year, month] = value.split('-');
    currentMonthText.value = `${year}年${month}月`;
  } else {
    currentMonthText.value = '全部时间';
  }
};

// 重置筛选
const resetFilter = () => {
  currentStatus.value = 'all';
  currentMonth.value = '';
  currentMonthText.value = '全部时间';
};

// 加载数据
const loadData = () => {
  isLoading.value = true;
  
  // 模拟API请求
  setTimeout(() => {
    // 模拟数据
    const mockData = generateMockData();
    
    if (page.value === 1) {
      historyList.value = mockData;
    } else {
      historyList.value = [...historyList.value, ...mockData];
    }
    
    // 判断是否还有更多数据
    hasMore.value = page.value < 3; // 模拟只有3页数据
    
    isLoading.value = false;
  }, 500);
};

// 加载更多
const loadMore = () => {
  if (isLoading.value || !hasMore.value) return;
  
  page.value++;
  loadData();
};

// 查看详情
const viewDetail = (item) => {
  Object.assign(currentDetail, JSON.parse(JSON.stringify(item)));
  showDetail.value = true;
};

// 关闭详情
const closeDetail = () => {
  showDetail.value = false;
};

// 重新提现
const retryWithdraw = () => {
  uni.navigateTo({
    url: '/pages/my/partner-withdraw'
  });
  closeDetail();
};

// 去提现
const goWithdraw = () => {
  uni.navigateTo({
    url: '/pages/my/partner-withdraw'
  });
};

// 格式化时间（简短）
const formatTime = (timeStr) => {
  const date = new Date(timeStr);
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hour = date.getHours().toString().padStart(2, '0');
  const minute = date.getMinutes().toString().padStart(2, '0');
  
  return `${month}-${day} ${hour}:${minute}`;
};

// 格式化时间（详细）
const formatDetailTime = (timeStr) => {
  if (!timeStr) return '';
  
  const date = new Date(timeStr);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hour = date.getHours().toString().padStart(2, '0');
  const minute = date.getMinutes().toString().padStart(2, '0');
  const second = date.getSeconds().toString().padStart(2, '0');
  
  return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
};

// 银行卡号脱敏
const maskBankCard = (cardNo) => {
  if (!cardNo) return '';
  if (cardNo.length < 8) return cardNo;
  
  return cardNo.substring(0, 4) + ' **** **** ' + cardNo.substring(cardNo.length - 4);
};

// 生成模拟数据
const generateMockData = () => {
  const mockData = [];
  const statuses = ['success', 'pending', 'processing', 'failed'];
  const methods = ['wechat', 'alipay', 'bank'];
  const banks = ['中国工商银行', '中国农业银行', '中国建设银行', '招商银行'];
  
  // 随机生成10条记录
  for (let i = 0; i < 10; i++) {
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const method = methods[Math.floor(Math.random() * methods.length)];
    const amount = Math.floor(Math.random() * 1000) + 100;
    const fee = Math.floor(amount * 0.006 * 100) / 100;
    
    // 生成随机时间
    const now = new Date();
    const randomDays = Math.floor(Math.random() * 60); // 最近60天内
    const time = new Date(now.getTime() - randomDays * 24 * 60 * 60 * 1000);
    
    // 生成随机订单号
    const orderNo = 'WD' + Date.now().toString().substring(3) + Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    
    const record = {
      amount,
      fee,
      status,
      method,
      time,
      orderNo
    };
    
    // 根据状态添加额外信息
    if (status === 'success') {
      record.auditTime = new Date(time.getTime() + 24 * 60 * 60 * 1000);
      record.completeTime = new Date(time.getTime() + 48 * 60 * 60 * 1000);
    } else if (status === 'processing') {
      record.auditTime = new Date(time.getTime() + 24 * 60 * 60 * 1000);
    } else if (status === 'failed') {
      record.auditTime = new Date(time.getTime() + 24 * 60 * 60 * 1000);
      record.failReason = '账户信息不匹配，请检查后重新提交';
    }
    
    // 根据提现方式添加额外信息
    if (method === 'alipay') {
      record.account = generateRandomEmail();
    } else if (method === 'bank') {
      record.bankName = banks[Math.floor(Math.random() * banks.length)];
      record.bankCard = generateRandomBankCard();
    }
    
    mockData.push(record);
  }
  
  return mockData;
};

// 生成随机邮箱
const generateRandomEmail = () => {
  const names = ['user', 'john', 'jane', 'alex', 'emma', 'michael', 'sarah'];
  const domains = ['gmail.com', '163.com', 'qq.com', '126.com', 'outlook.com'];
  
  const name = names[Math.floor(Math.random() * names.length)];
  const domain = domains[Math.floor(Math.random() * domains.length)];
  const number = Math.floor(Math.random() * 10000);
  
  return `${name}${number}@${domain}`;
};

// 生成随机银行卡
const generateRandomBankCard = () => {
  let card = '622';
  for (let i = 0; i < 13; i++) {
    card += Math.floor(Math.random() * 10);
  }
  return card;
};
</script>

<style lang="scss" scoped>
.history-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 30rpx;
}

.filter-section {
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.filter-tabs {
  display: flex;
  overflow-x: auto;
  margin-bottom: 20rpx;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

.tab-item {
  padding: 15rpx 30rpx;
  font-size: 28rpx;
  color: #666666;
  position: relative;
  white-space: nowrap;
  
  &.active {
    color: #1677FF;
    font-weight: 500;
    
    &::after {
      content: '';
      position: absolute;
      left: 50%;
      bottom: 0;
      transform: translateX(-50%);
      width: 40rpx;
      height: 4rpx;
      background-color: #1677FF;
      border-radius: 2rpx;
    }
  }
}

.filter-date {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.date-picker {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666666;
  
  .cuIcon-calendar {
    margin-left: 10rpx;
    font-size: 28rpx;
  }
}

.history-list {
  padding: 20rpx 30rpx;
}

.history-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.item-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.header-left {
  .withdraw-type {
    font-size: 30rpx;
    font-weight: 500;
    color: #333333;
    margin-right: 15rpx;
  }
  
  .withdraw-time {
    font-size: 24rpx;
    color: #999999;
  }
}

.withdraw-status {
  font-size: 28rpx;
  font-weight: 500;
  
  &.status-success {
    color: #07c160;
  }
  
  &.status-pending, &.status-processing {
    color: #1677FF;
  }
  
  &.status-failed {
    color: #ff4d4f;
  }
}

.item-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #f0f0f0;
}

.content-left {
  .withdraw-amount {
    font-size: 40rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 5rpx;
  }
  
  .withdraw-fee {
    font-size: 24rpx;
    color: #999999;
  }
}

.content-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  
  .actual-amount {
    font-size: 26rpx;
    color: #666666;
    margin-bottom: 10rpx;
  }
  
  .detail-btn {
    font-size: 24rpx;
    color: #1677FF;
  }
}

.item-footer {
  margin-top: 20rpx;
  
  .fail-reason {
    font-size: 24rpx;
    color: #ff4d4f;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
  
  image {
    width: 240rpx;
    height: 240rpx;
    margin-bottom: 30rpx;
  }
  
  text {
    font-size: 28rpx;
    color: #999999;
    margin-bottom: 40rpx;
  }
  
  .go-withdraw {
    width: 300rpx;
    height: 80rpx;
    line-height: 80rpx;
    background: linear-gradient(135deg, #1677FF, #0E5FD8);
    color: #ffffff;
    font-size: 28rpx;
    border-radius: 40rpx;
  }
}

.load-more, .load-end {
  text-align: center;
  font-size: 26rpx;
  color: #999999;
  padding: 30rpx 0;
}

.detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
  
  .modal-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
  }
  
  .modal-content {
    width: 650rpx;
    max-height: 80vh;
    background-color: #ffffff;
    border-radius: 16rpx;
    position: relative;
    z-index: 1000;
    overflow-y: auto;
  }
  
  .modal-header {
    padding: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
    
    .modal-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333333;
    }
    
    .close-icon {
      font-size: 36rpx;
      color: #999999;
    }
  }
  
  .detail-status {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40rpx 0;
    
    .status-icon {
      width: 100rpx;
      height: 100rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 20rpx;
      
      &.icon-success {
        background-color: rgba(7, 193, 96, 0.1);
        color: #07c160;
      }
      
      &.icon-pending, &.icon-processing {
        background-color: rgba(22, 119, 255, 0.1);
        color: #1677FF;
      }
      
      &.icon-failed {
        background-color: rgba(255, 77, 79, 0.1);
        color: #ff4d4f;
      }
      
      text {
        font-size: 50rpx;
      }
    }
    
    .status-text {
      font-size: 32rpx;
      font-weight: 500;
      margin-bottom: 10rpx;
      
      &.success {
        color: #07c160;
      }
      
      &.pending, &.processing {
        color: #1677FF;
      }
      
      &.failed {
        color: #ff4d4f;
      }
    }
    
    .status-desc {
      font-size: 26rpx;
      color: #999999;
      text-align: center;
      padding: 0 50rpx;
    }
  }
  
  .detail-info {
    padding: 30rpx;
    border-top: 10rpx solid #f5f7fa;
    
    .info-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 25rpx;
      
      .info-label {
        font-size: 28rpx;
        color: #666666;
      }
      
      .info-value {
        font-size: 28rpx;
        color: #333333;
        font-weight: 500;
        max-width: 60%;
        text-align: right;
        word-break: break-all;
      }
      
      &.highlight {
        .info-label, .info-value {
          color: #1677FF;
          font-weight: bold;
        }
      }
    }
  }
  
  .detail-footer {
    padding: 30rpx;
    border-top: 1px solid #f0f0f0;
    
    .retry-btn {
      width: 100%;
      height: 80rpx;
      background: linear-gradient(135deg, #1677FF, #0E5FD8);
      color: #ffffff;
      font-size: 30rpx;
      border-radius: 40rpx;
      font-weight: 500;
    }
  }
}
</style>