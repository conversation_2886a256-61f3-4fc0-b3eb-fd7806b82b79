<view class="coupons-container"><view class="navbar"><view class="navbar-back" bindtap="{{a}}"><view class="back-icon"></view></view><text class="navbar-title">会员卡券</text><view class="navbar-right"><view class="help-icon" bindtap="{{b}}">?</view></view></view><view class="overview-section"><view class="overview-header"><text class="section-title">卡券概览</text><view class="date-picker" bindtap="{{d}}"><text class="date-text">{{c}}</text><view class="date-icon"></view></view></view><view class="stats-cards"><view class="stats-card"><view class="card-value">{{e}}</view><view class="card-label">总卡券数</view></view><view class="stats-card"><view class="card-value">{{f}}</view><view class="card-label">已发放卡券</view><view class="{{['card-trend', h]}}"><view class="trend-arrow"></view><text class="trend-value">{{g}}</text></view></view><view class="stats-card"><view class="card-value">{{i}}</view><view class="card-label">已使用卡券</view><view class="{{['card-trend', k]}}"><view class="trend-arrow"></view><text class="trend-value">{{j}}</text></view></view><view class="stats-card"><view class="card-value">{{l}}%</view><view class="card-label">使用率</view><view class="{{['card-trend', n]}}"><view class="trend-arrow"></view><text class="trend-value">{{m}}</text></view></view></view></view><view class="tabs-section"><scroll-view scroll-x class="tabs-scroll" show-scrollbar="false"><view class="tabs"><view wx:for="{{o}}" wx:for-item="tab" wx:key="b" class="{{['tab-item', tab.c && 'active']}}" bindtap="{{tab.d}}">{{tab.a}}</view></view></scroll-view></view><view class="coupons-section"><view class="section-header"><text class="section-title">{{p}}</text><view class="add-btn" bindtap="{{q}}"><text class="btn-text">添加卡券</text><view class="plus-icon"></view></view></view><view class="coupons-list"><view wx:for="{{r}}" wx:for-item="coupon" wx:key="t" class="coupon-item" bindtap="{{coupon.v}}"><view class="{{['coupon-card', coupon.k]}}"><view class="coupon-left"><view class="coupon-value"><text wx:if="{{coupon.a}}" class="value-prefix">{{coupon.b}}<text class="value-unit">折</text></text><text wx:else class="value-prefix">¥<text class="value-number">{{coupon.c}}</text></text></view><view class="coupon-condition">{{coupon.d}}</view></view><view class="coupon-divider"><view class="circle top"></view><view class="dashed-line"></view><view class="circle bottom"></view></view><view class="coupon-right"><view class="coupon-name">{{coupon.e}}</view><view class="coupon-desc">{{coupon.f}}</view><view class="coupon-period">{{coupon.g}}</view><view class="coupon-levels"><text wx:for="{{coupon.h}}" wx:for-item="level" wx:key="b" class="level-tag">{{level.a}}</text></view></view><view class="{{['coupon-status', coupon.j]}}">{{coupon.i}}</view></view><view class="coupon-actions"><view class="action-btn issue" catchtap="{{coupon.n}}"><svg wx:if="{{t}}" u-s="{{['d']}}" class="svg-icon" u-i="{{coupon.m}}" bind:__l="__l" u-p="{{t}}"><path wx:if="{{s}}" u-i="{{coupon.l}}" bind:__l="__l" u-p="{{s}}"/></svg><text>发放</text></view><view class="action-btn stats" catchtap="{{coupon.q}}"><svg wx:if="{{w}}" u-s="{{['d']}}" class="svg-icon" u-i="{{coupon.p}}" bind:__l="__l" u-p="{{w}}"><path wx:if="{{v}}" u-i="{{coupon.o}}" bind:__l="__l" u-p="{{v}}"/></svg><text>统计</text></view><view class="action-btn toggle"><switch checked="{{coupon.r}}" bindchange="{{coupon.s}}" color="#FF6FD8"/></view></view></view></view></view><view class="floating-action-button" bindtap="{{x}}"><view class="fab-icon">+</view></view></view>