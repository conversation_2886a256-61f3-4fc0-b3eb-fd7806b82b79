{"version": 3, "file": "create.js", "sources": ["carpool-package/pages/carpool/groups/create.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/Y2FycG9vbC1wYWNrYWdlXHBhZ2VzXGNhcnBvb2xcZ3JvdXBzXGNyZWF0ZS52dWU"], "sourcesContent": ["<template>\n  <view class=\"create-group-container\">\n    <!-- 自定义导航栏 -->\n    <view style=\"position: fixed; top: 0; left: 0; right: 0; height: 90px; background: linear-gradient(135deg, #0A84FF, #0040DD); z-index: 100; padding-top: var(--status-bar-height, 40px);\">\n      <!-- 返回键 - 绝对定位 -->\n      <view style=\"position: absolute; left: 8px; top: calc(var(--status-bar-height, 40px) + 10px); width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;\" @click=\"goBack\">\n        <image src=\"/static/images/tabbar/最新返回键.png\" style=\"width: 24px; height: 24px; filter: brightness(0) invert(1);\"></image>\n      </view>\n      \n      <!-- 标题 - 绝对定位 -->\n      <view style=\"position: absolute; left: 0; right: 0; top: calc(var(--status-bar-height, 40px) + 15px); text-align: center;\">\n        <text style=\"font-size: 18px; color: white; font-weight: bold;\">创建拼车群</text>\n      </view>\n    </view>\n    \n    <!-- 占位元素确保内容不被导航栏遮挡 -->\n    <view style=\"height: calc(90px + var(--status-bar-height, 40px));\"></view>\n    \n    <!-- 表单内容 -->\n    <view class=\"form-container\">\n      <view class=\"form-item\">\n        <text class=\"form-label\">群名称</text>\n        <input class=\"form-input\" type=\"text\" v-model=\"groupInfo.name\" placeholder=\"请输入群名称\" />\n      </view>\n      \n      <view class=\"form-item\">\n        <text class=\"form-label\">群描述</text>\n        <textarea class=\"form-textarea\" v-model=\"groupInfo.description\" placeholder=\"请输入群描述，如路线、拼车规则等\" />\n      </view>\n      \n      <view class=\"form-item\">\n        <text class=\"form-label\">群标签</text>\n        <view class=\"tags-container\">\n          <view \n            v-for=\"(tag, index) in tags\" \n            :key=\"index\" \n            class=\"tag-item\"\n            :class=\"{ 'selected': selectedTags.includes(tag) }\"\n            @click=\"toggleTag(tag)\"\n          >\n            {{tag}}\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"form-item\">\n        <text class=\"form-label\">拼车路线</text>\n        <view class=\"location-selectors\">\n          <view class=\"location-input\" @click=\"selectLocation('start')\">\n            <view class=\"location-icon start\"></view>\n            <text class=\"location-text\">{{groupInfo.startLocation || '选择出发地'}}</text>\n            <view class=\"location-arrow\">\n              <image src=\"/static/images/tabbar/arrow-right.png\" style=\"width: 16px; height: 16px;\"></image>\n            </view>\n          </view>\n          \n          <view class=\"location-divider\"></view>\n          \n          <view class=\"location-input\" @click=\"selectLocation('end')\">\n            <view class=\"location-icon end\"></view>\n            <text class=\"location-text\">{{groupInfo.endLocation || '选择目的地'}}</text>\n            <view class=\"location-arrow\">\n              <image src=\"/static/images/tabbar/arrow-right.png\" style=\"width: 16px; height: 16px;\"></image>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"form-item\">\n        <text class=\"form-label\">群二维码</text>\n        <view class=\"qrcode-uploader\" @click=\"uploadQRCode\">\n          <image v-if=\"groupInfo.qrcode\" :src=\"groupInfo.qrcode\" mode=\"aspectFit\" class=\"qrcode-preview\"></image>\n          <view v-else class=\"upload-placeholder\">\n            <image src=\"/static/images/tabbar/plus.png\" style=\"width: 32px; height: 32px;\"></image>\n            <text>上传群二维码</text>\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"form-item\">\n        <text class=\"form-label\">联系方式</text>\n        <input class=\"form-input\" type=\"text\" v-model=\"groupInfo.contactInfo\" placeholder=\"请输入联系方式（手机号/微信）\" />\n      </view>\n      \n      <view class=\"form-item privacy-section\">\n        <checkbox-group @change=\"privacyChanged\">\n          <label class=\"privacy-label\">\n            <checkbox :checked=\"agreePrivacy\" color=\"#0A84FF\" />\n            <text>我已阅读并同意</text>\n            <text class=\"privacy-link\" @click=\"showPrivacyPolicy\">《拼车群管理规范》</text>\n          </label>\n        </checkbox-group>\n      </view>\n      \n      <view class=\"submit-button-area\">\n        <button class=\"submit-btn\" :disabled=\"!canSubmit\" @click=\"submitForm\">创建拼车群</button>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue';\n\n// 状态变量\nconst groupInfo = ref({\n  name: '',\n  description: '',\n  startLocation: '',\n  endLocation: '',\n  qrcode: '',\n  contactInfo: ''\n});\n\nconst tags = ref(['上班族', '学生', '长途', '短途', '每日拼车', '周末拼车', '临时拼车', '商务出行']);\nconst selectedTags = ref([]);\nconst agreePrivacy = ref(false);\nconst isAdmin = ref(false);\n\n// 计算属性：是否可以提交\nconst canSubmit = computed(() => {\n  return groupInfo.value.name && \n         groupInfo.value.description && \n         groupInfo.value.startLocation && \n         groupInfo.value.endLocation && \n         groupInfo.value.qrcode && \n         groupInfo.value.contactInfo && \n         agreePrivacy.value;\n});\n\n// 生命周期钩子\nonMounted(() => {\n  // 检查用户是否为管理员\n  const userInfo = uni.getStorageSync('userInfo') || {};\n  isAdmin.value = !!userInfo.isAdmin;\n  \n  if (!isAdmin.value) {\n    uni.showModal({\n      title: '提示',\n      content: '只有管理员可以创建拼车群',\n      showCancel: false,\n      success: () => {\n        uni.navigateBack();\n      }\n    });\n  }\n});\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n\n// 切换标签选择\nconst toggleTag = (tag) => {\n  if (selectedTags.value.includes(tag)) {\n    selectedTags.value = selectedTags.value.filter(item => item !== tag);\n  } else {\n    if (selectedTags.value.length < 3) {\n      selectedTags.value.push(tag);\n    } else {\n      uni.showToast({\n        title: '最多选择3个标签',\n        icon: 'none'\n      });\n    }\n  }\n};\n\n// 选择位置\nconst selectLocation = (type) => {\n  uni.chooseLocation({\n    success: (res) => {\n      if (type === 'start') {\n        groupInfo.value.startLocation = res.name;\n      } else {\n        groupInfo.value.endLocation = res.name;\n      }\n    },\n    fail: () => {\n      uni.showToast({\n        title: '选择位置失败',\n        icon: 'none'\n      });\n    }\n  });\n};\n\n// 上传群二维码\nconst uploadQRCode = () => {\n  uni.chooseImage({\n    count: 1,\n    sizeType: ['compressed'],\n    sourceType: ['album', 'camera'],\n    success: (res) => {\n      // 上传图片到服务器\n      uni.showLoading({\n        title: '上传中...'\n      });\n      \n      uni.uploadFile({\n        url: 'https://api.example.com/upload', // 替换为实际的上传接口\n        filePath: res.tempFilePaths[0],\n        name: 'file',\n        success: (uploadRes) => {\n          try {\n            const data = JSON.parse(uploadRes.data);\n            if (data.code === 0) {\n              groupInfo.value.qrcode = data.data.url;\n            } else {\n              uni.showToast({\n                title: data.msg || '上传失败',\n                icon: 'none'\n              });\n            }\n          } catch (e) {\n            uni.showToast({\n              title: '上传失败',\n              icon: 'none'\n            });\n          }\n        },\n        fail: () => {\n          uni.showToast({\n            title: '上传失败',\n            icon: 'none'\n          });\n        },\n        complete: () => {\n          uni.hideLoading();\n        }\n      });\n    }\n  });\n};\n\n// 隐私政策变更\nconst privacyChanged = (e) => {\n  agreePrivacy.value = e.detail.value.length > 0;\n};\n\n// 显示隐私政策\nconst showPrivacyPolicy = () => {\n  uni.showModal({\n    title: '拼车群管理规范',\n    content: '1. 群内禁止发布违法信息\\n2. 请文明用语，互相尊重\\n3. 遵守交通规则，确保安全\\n4. 请勿发布虚假信息\\n5. 平台有权对违规内容进行处理',\n    showCancel: false\n  });\n};\n\n// 提交表单\nconst submitForm = () => {\n  if (!canSubmit.value) {\n    uni.showToast({\n      title: '请完成所有必填项',\n      icon: 'none'\n    });\n    return;\n  }\n  \n  if (!isAdmin.value) {\n    uni.showToast({\n      title: '只有管理员可以创建拼车群',\n      icon: 'none'\n    });\n    return;\n  }\n  \n  // 构建提交数据\n  const formData = {\n    ...groupInfo.value,\n    tags: selectedTags.value\n  };\n  \n  console.log('提交表单数据:', formData);\n  \n  // 提交到服务器\n  uni.showLoading({\n    title: '创建中...'\n  });\n  \n  uni.request({\n    url: 'https://api.example.com/carpool/groups/create', // 替换为实际的创建接口\n    method: 'POST',\n    data: formData,\n    header: {\n      'content-type': 'application/json',\n      'Authorization': uni.getStorageSync('token') || ''\n    },\n    success: (res) => {\n      if (res.statusCode === 200 && res.data.code === 0) {\n        uni.showToast({\n          title: '创建成功',\n          icon: 'success'\n        });\n        \n        // 延迟返回\n        setTimeout(() => {\n          uni.navigateBack();\n        }, 1500);\n      } else {\n        uni.showToast({\n          title: res.data.msg || '创建失败',\n          icon: 'none'\n        });\n      }\n    },\n    fail: () => {\n      uni.showToast({\n        title: '网络异常，请重试',\n        icon: 'none'\n      });\n    },\n    complete: () => {\n      uni.hideLoading();\n    }\n  });\n};\n</script>\n\n<style lang=\"scss\">\n.create-group-container {\n  min-height: 100vh;\n  background-color: #F5F8FC;\n  padding-bottom: 40rpx;\n}\n\n.form-container {\n  padding: 30rpx;\n}\n\n.form-item {\n  margin-bottom: 30rpx;\n  background-color: #FFFFFF;\n  border-radius: 16rpx;\n  padding: 24rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);\n}\n\n.form-label {\n  font-size: 28rpx;\n  color: #333333;\n  font-weight: 600;\n  margin-bottom: 16rpx;\n  display: block;\n}\n\n.form-input {\n  width: 100%;\n  height: 80rpx;\n  background-color: #F8F8F8;\n  border-radius: 8rpx;\n  padding: 0 20rpx;\n  font-size: 28rpx;\n  color: #333333;\n}\n\n.form-textarea {\n  width: 100%;\n  height: 180rpx;\n  background-color: #F8F8F8;\n  border-radius: 8rpx;\n  padding: 20rpx;\n  font-size: 28rpx;\n  color: #333333;\n}\n\n.tags-container {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.tag-item {\n  padding: 10rpx 20rpx;\n  background-color: #F2F2F2;\n  border-radius: 30rpx;\n  margin: 10rpx;\n  font-size: 24rpx;\n  color: #666666;\n}\n\n.tag-item.selected {\n  background-color: #E6F2FF;\n  color: #0A84FF;\n  border: 1px solid #0A84FF;\n}\n\n.location-selectors {\n  background-color: #F8F8F8;\n  border-radius: 8rpx;\n  overflow: hidden;\n}\n\n.location-input {\n  display: flex;\n  align-items: center;\n  height: 80rpx;\n  padding: 0 20rpx;\n}\n\n.location-icon {\n  width: 16rpx;\n  height: 16rpx;\n  border-radius: 8rpx;\n  margin-right: 16rpx;\n}\n\n.location-icon.start {\n  background-color: #34C759;\n}\n\n.location-icon.end {\n  background-color: #FF3B30;\n}\n\n.location-text {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333333;\n}\n\n.location-divider {\n  height: 1px;\n  background-color: #E5E5E5;\n  margin-left: 40rpx;\n}\n\n.qrcode-uploader {\n  width: 200rpx;\n  height: 200rpx;\n  border: 1px dashed #CCCCCC;\n  border-radius: 8rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n}\n\n.upload-placeholder {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.upload-placeholder text {\n  font-size: 24rpx;\n  color: #999999;\n  margin-top: 10rpx;\n}\n\n.qrcode-preview {\n  width: 100%;\n  height: 100%;\n}\n\n.privacy-section {\n  background-color: transparent;\n  box-shadow: none;\n  padding: 0;\n}\n\n.privacy-label {\n  display: flex;\n  align-items: center;\n  font-size: 26rpx;\n  color: #666666;\n}\n\n.privacy-link {\n  color: #0A84FF;\n  margin-left: 4rpx;\n}\n\n.submit-button-area {\n  padding: 30rpx;\n  margin-top: 40rpx;\n}\n\n.submit-btn {\n  width: 100%;\n  height: 90rpx;\n  background: linear-gradient(135deg, #0A84FF, #0066CC);\n  border-radius: 45rpx;\n  color: #FFFFFF;\n  font-size: 32rpx;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 4rpx 12rpx rgba(10, 132, 255, 0.3);\n}\n\n.submit-btn[disabled] {\n  background: linear-gradient(135deg, #B8B8B8, #999999);\n  color: #FFFFFF;\n  box-shadow: none;\n}\n\n.empty-tip {\n  font-size: 28rpx;\n  color: #999999;\n  margin-top: 20rpx;\n}\n</style>\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/carpool-package/pages/carpool/groups/create.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onMounted", "uni", "MiniProgramPage"], "mappings": ";;;;;;AAyGA,UAAM,YAAYA,cAAAA,IAAI;AAAA,MACpB,MAAM;AAAA,MACN,aAAa;AAAA,MACb,eAAe;AAAA,MACf,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,aAAa;AAAA,IACf,CAAC;AAED,UAAM,OAAOA,cAAG,IAAC,CAAC,OAAO,MAAM,MAAM,MAAM,QAAQ,QAAQ,QAAQ,MAAM,CAAC;AAC1E,UAAM,eAAeA,cAAAA,IAAI,CAAA,CAAE;AAC3B,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAC9B,UAAM,UAAUA,cAAAA,IAAI,KAAK;AAGzB,UAAM,YAAYC,cAAQ,SAAC,MAAM;AAC/B,aAAO,UAAU,MAAM,QAChB,UAAU,MAAM,eAChB,UAAU,MAAM,iBAChB,UAAU,MAAM,eAChB,UAAU,MAAM,UAChB,UAAU,MAAM,eAChB,aAAa;AAAA,IACtB,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AAEd,YAAM,WAAWC,cAAG,MAAC,eAAe,UAAU,KAAK,CAAA;AACnD,cAAQ,QAAQ,CAAC,CAAC,SAAS;AAE3B,UAAI,CAAC,QAAQ,OAAO;AAClBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,SAAS,MAAM;AACbA,0BAAG,MAAC,aAAY;AAAA,UACjB;AAAA,QACP,CAAK;AAAA,MACF;AAAA,IACH,CAAC;AAGD,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,YAAY,CAAC,QAAQ;AACzB,UAAI,aAAa,MAAM,SAAS,GAAG,GAAG;AACpC,qBAAa,QAAQ,aAAa,MAAM,OAAO,UAAQ,SAAS,GAAG;AAAA,MACvE,OAAS;AACL,YAAI,aAAa,MAAM,SAAS,GAAG;AACjC,uBAAa,MAAM,KAAK,GAAG;AAAA,QACjC,OAAW;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACF;AAAA,IACH;AAGA,UAAM,iBAAiB,CAAC,SAAS;AAC/BA,oBAAAA,MAAI,eAAe;AAAA,QACjB,SAAS,CAAC,QAAQ;AAChB,cAAI,SAAS,SAAS;AACpB,sBAAU,MAAM,gBAAgB,IAAI;AAAA,UAC5C,OAAa;AACL,sBAAU,MAAM,cAAc,IAAI;AAAA,UACnC;AAAA,QACF;AAAA,QACD,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,eAAe,MAAM;AACzBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAEhBA,wBAAAA,MAAI,YAAY;AAAA,YACd,OAAO;AAAA,UACf,CAAO;AAEDA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA;AAAA,YACL,UAAU,IAAI,cAAc,CAAC;AAAA,YAC7B,MAAM;AAAA,YACN,SAAS,CAAC,cAAc;AACtB,kBAAI;AACF,sBAAM,OAAO,KAAK,MAAM,UAAU,IAAI;AACtC,oBAAI,KAAK,SAAS,GAAG;AACnB,4BAAU,MAAM,SAAS,KAAK,KAAK;AAAA,gBACjD,OAAmB;AACLA,gCAAAA,MAAI,UAAU;AAAA,oBACZ,OAAO,KAAK,OAAO;AAAA,oBACnB,MAAM;AAAA,kBACtB,CAAe;AAAA,gBACF;AAAA,cACF,SAAQ,GAAG;AACVA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACpB,CAAa;AAAA,cACF;AAAA,YACF;AAAA,YACD,MAAM,MAAM;AACVA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,YACD,UAAU,MAAM;AACdA,4BAAG,MAAC,YAAW;AAAA,YAChB;AAAA,UACT,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,iBAAiB,CAAC,MAAM;AAC5B,mBAAa,QAAQ,EAAE,OAAO,MAAM,SAAS;AAAA,IAC/C;AAGA,UAAM,oBAAoB,MAAM;AAC9BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MAChB,CAAG;AAAA,IACH;AAGA,UAAM,aAAa,MAAM;AACvB,UAAI,CAAC,UAAU,OAAO;AACpBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI,CAAC,QAAQ,OAAO;AAClBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAGD,YAAM,WAAW;AAAA,QACf,GAAG,UAAU;AAAA,QACb,MAAM,aAAa;AAAA,MACvB;AAEEA,iGAAY,WAAW,QAAQ;AAG/BA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAEDA,oBAAAA,MAAI,QAAQ;AAAA,QACV,KAAK;AAAA;AAAA,QACL,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,gBAAgB;AAAA,UAChB,iBAAiBA,cAAG,MAAC,eAAe,OAAO,KAAK;AAAA,QACjD;AAAA,QACD,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,eAAe,OAAO,IAAI,KAAK,SAAS,GAAG;AACjDA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAGD,uBAAW,MAAM;AACfA,4BAAG,MAAC,aAAY;AAAA,YACjB,GAAE,IAAI;AAAA,UACf,OAAa;AACLA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO,IAAI,KAAK,OAAO;AAAA,cACvB,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,QACD,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,QACD,UAAU,MAAM;AACdA,wBAAG,MAAC,YAAW;AAAA,QAChB;AAAA,MACL,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5TA,GAAG,WAAWC,SAAe;"}