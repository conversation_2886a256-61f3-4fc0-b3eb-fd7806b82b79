<!-- 生日礼包页面开始 -->
<template>
  <view class="birthday-gift-container">
    <!-- 页面标题区域 -->
    <view class="page-header">
      <view class="title-section">
        <text class="page-title">生日礼包</text>
        <text class="page-subtitle">管理会员生日当月专享礼包</text>
      </view>
    </view>
    
    <!-- 生日特权开关 -->
    <view class="section-card">
      <view class="switch-item">
        <view class="switch-content">
          <text class="switch-title">生日特权</text>
          <text class="switch-desc">开启后，会员在生日当月可享受专属礼包</text>
        </view>
        <switch :checked="birthdaySettings.enabled" @change="toggleBirthdayPrivilege" color="#4A00E0" />
      </view>
    </view>
    
    <block v-if="birthdaySettings.enabled">
      <!-- 生日特权设置 -->
      <view class="section-card">
        <view class="section-title">特权设置</view>
        
        <view class="form-item">
          <text class="form-label">特权有效期</text>
          <view class="radio-group">
            <view class="radio-item" :class="{ active: birthdaySettings.validPeriod === 'day' }" @click="setValidPeriod('day')">
              <text class="radio-text">生日当天</text>
            </view>
            <view class="radio-item" :class="{ active: birthdaySettings.validPeriod === 'week' }" @click="setValidPeriod('week')">
              <text class="radio-text">生日当周</text>
            </view>
            <view class="radio-item" :class="{ active: birthdaySettings.validPeriod === 'month' }" @click="setValidPeriod('month')">
              <text class="radio-text">生日当月</text>
            </view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">提前通知</text>
          <view class="form-input-group">
            <input type="number" class="form-input" v-model="birthdaySettings.notifyDays" />
            <text class="input-suffix">天</text>
          </view>
        </view>
        
        <view class="form-item switch-item">
          <text class="form-label">短信通知</text>
          <switch :checked="birthdaySettings.smsNotify" @change="toggleSmsNotify" color="#4A00E0" />
        </view>
        
        <view class="form-item switch-item">
          <text class="form-label">微信通知</text>
          <switch :checked="birthdaySettings.wechatNotify" @change="toggleWechatNotify" color="#4A00E0" />
        </view>
      </view>
      
      <!-- 生日礼包设置 -->
      <view class="section-card">
        <view class="section-title">生日礼包</view>
        
        <view class="privilege-list">
          <view class="privilege-item" v-for="(privilege, index) in birthdayPrivileges" :key="index">
            <view class="privilege-checkbox" :class="{ checked: privilege.selected }" @click="togglePrivilege(privilege)">
              <view class="checkbox-inner" v-if="privilege.selected"></view>
            </view>
            <view class="privilege-content">
              <text class="privilege-name">{{privilege.name}}</text>
              <text class="privilege-desc">{{privilege.description}}</text>
            </view>
            <view class="privilege-config" @click="configPrivilege(privilege)">
              <text class="config-text">设置</text>
            </view>
          </view>
        </view>
        
        <button class="add-btn" @click="addPrivilege">添加生日特权</button>
      </view>
      
      <!-- 适用会员等级 -->
      <view class="section-card">
        <view class="section-title">适用会员等级</view>
        
        <view class="level-list">
          <view class="level-item" v-for="(level, index) in memberLevels" :key="index">
            <view class="level-checkbox" :class="{ checked: level.selected }" @click="toggleLevel(level)">
              <view class="checkbox-inner" v-if="level.selected"></view>
            </view>
            <view class="level-content">
              <text class="level-name">{{level.name}}</text>
              <text class="level-desc">{{level.memberCount}}名会员</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 生日祝福语 -->
      <view class="section-card">
        <view class="section-title">生日祝福语</view>
        
        <view class="form-item">
          <textarea class="form-textarea" v-model="birthdaySettings.greetingText" placeholder="请输入生日祝福语" />
        </view>
        
        <view class="greeting-preview">
          <view class="preview-title">预览效果</view>
          <view class="preview-card">
            <view class="preview-header">
              <image class="preview-logo" src="/static/images/logo.png" mode="aspectFit"></image>
              <text class="preview-shop-name">磁州生活网</text>
            </view>
            <view class="preview-content">
              <text class="preview-greeting">{{birthdaySettings.greetingText || '亲爱的会员，祝您生日快乐！我们为您准备了专属生日礼包，点击查看详情。'}}</text>
            </view>
            <view class="preview-footer">
              <text class="preview-btn">查看生日礼包</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 保存按钮 -->
      <view class="bottom-bar">
        <button class="save-btn" @click="saveSettings">保存设置</button>
      </view>
    </block>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 生日特权设置
      birthdaySettings: {
        enabled: true,
        validPeriod: 'month',
        notifyDays: 3,
        smsNotify: true,
        wechatNotify: true,
        greetingText: '亲爱的会员，祝您生日快乐！我们为您准备了专属生日礼包，点击查看详情。'
      },
      
      // 生日特权列表
      birthdayPrivileges: [
        {
          id: 1,
          name: '生日优惠券',
          description: '赠送生日专属优惠券',
          selected: true
        },
        {
          id: 2,
          name: '积分翻倍',
          description: '生日期间消费积分翻倍',
          selected: true
        },
        {
          id: 3,
          name: '生日礼品',
          description: '到店领取生日礼品',
          selected: false
        },
        {
          id: 4,
          name: '专属折扣',
          description: '生日当天消费享受额外折扣',
          selected: true
        },
        {
          id: 5,
          name: '免费配送',
          description: '生日期间订单免费配送',
          selected: false
        }
      ],
      
      // 会员等级
      memberLevels: [
        {
          id: 1,
          name: '普通会员',
          memberCount: 2156,
          selected: false
        },
        {
          id: 2,
          name: '银卡会员',
          memberCount: 864,
          selected: true
        },
        {
          id: 3,
          name: '金卡会员',
          memberCount: 426,
          selected: true
        },
        {
          id: 4,
          name: '钻石会员',
          memberCount: 116,
          selected: true
        }
      ]
    };
  },
  methods: {
    toggleBirthdayPrivilege(e) {
      this.birthdaySettings.enabled = e.detail.value;
    },
    
    setValidPeriod(period) {
      this.birthdaySettings.validPeriod = period;
    },
    
    toggleSmsNotify(e) {
      this.birthdaySettings.smsNotify = e.detail.value;
    },
    
    toggleWechatNotify(e) {
      this.birthdaySettings.wechatNotify = e.detail.value;
    },
    
    togglePrivilege(privilege) {
      const index = this.birthdayPrivileges.findIndex(item => item.id === privilege.id);
      if (index !== -1) {
        this.birthdayPrivileges[index].selected = !this.birthdayPrivileges[index].selected;
      }
    },
    
    configPrivilege(privilege) {
      uni.showToast({
        title: `${privilege.name}设置功能开发中`,
        icon: 'none'
      });
    },
    
    addPrivilege() {
      uni.showToast({
        title: '添加特权功能开发中',
        icon: 'none'
      });
    },
    
    toggleLevel(level) {
      const index = this.memberLevels.findIndex(item => item.id === level.id);
      if (index !== -1) {
        this.memberLevels[index].selected = !this.memberLevels[index].selected;
      }
    },
    
    saveSettings() {
      // 这里应该调用API保存设置
      uni.showToast({
        title: '设置保存成功',
        icon: 'success'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.birthday-gift-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20rpx;
}

.title-section {
  .page-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
  
  .page-subtitle {
    font-size: 24rpx;
    color: #666;
    margin-top: 6rpx;
  }
}

.section-card {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  .section-title {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    position: relative;
    padding-left: 20rpx;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8rpx;
      height: 30rpx;
      background-color: #4A00E0;
      border-radius: 4rpx;
    }
  }
}

.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .switch-content {
    .switch-title {
      font-size: 30rpx;
      font-weight: bold;
      color: #333;
    }
    
    .switch-desc {
      font-size: 24rpx;
      color: #666;
      margin-top: 6rpx;
    }
  }
}

.form-item {
  margin-bottom: 24rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .form-label {
    display: block;
    font-size: 28rpx;
    color: #333;
    margin-bottom: 12rpx;
  }
  
  .form-input {
    width: 100%;
    height: 80rpx;
    border: 1rpx solid #ddd;
    border-radius: 8rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    box-sizing: border-box;
  }
  
  .form-input-group {
    display: flex;
    align-items: center;
    
    .form-input {
      width: 200rpx;
    }
    
    .input-suffix {
      margin-left: 10rpx;
      font-size: 28rpx;
      color: #333;
    }
  }
  
  .form-textarea {
    width: 100%;
    height: 160rpx;
    border: 1rpx solid #ddd;
    border-radius: 8rpx;
    padding: 20rpx;
    font-size: 28rpx;
    box-sizing: border-box;
  }
  
  .radio-group {
    display: flex;
    
    .radio-item {
      padding: 12rpx 30rpx;
      border: 1rpx solid #ddd;
      border-radius: 8rpx;
      margin-right: 20rpx;
      
      .radio-text {
        font-size: 28rpx;
        color: #333;
      }
      
      &.active {
        background-color: #4A00E0;
        border-color: #4A00E0;
        
        .radio-text {
          color: #fff;
        }
      }
    }
  }
  
  &.switch-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .form-label {
      margin-bottom: 0;
    }
  }
}

.privilege-list {
  margin-bottom: 24rpx;
  
  .privilege-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .privilege-checkbox {
      width: 40rpx;
      height: 40rpx;
      border: 1rpx solid #ddd;
      border-radius: 8rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20rpx;
      
      &.checked {
        background-color: #4A00E0;
        border-color: #4A00E0;
      }
      
      .checkbox-inner {
        width: 20rpx;
        height: 20rpx;
        background-color: #fff;
      }
    }
    
    .privilege-content {
      flex: 1;
      
      .privilege-name {
        font-size: 28rpx;
        color: #333;
        font-weight: bold;
      }
      
      .privilege-desc {
        font-size: 24rpx;
        color: #666;
        margin-top: 6rpx;
      }
    }
    
    .privilege-config {
      .config-text {
        font-size: 28rpx;
        color: #4A00E0;
      }
    }
  }
}

.add-btn {
  width: 100%;
  height: 80rpx;
  background-color: #f5f5f5;
  color: #666;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
}

.level-list {
  .level-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .level-checkbox {
      width: 40rpx;
      height: 40rpx;
      border: 1rpx solid #ddd;
      border-radius: 8rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20rpx;
      
      &.checked {
        background-color: #4A00E0;
        border-color: #4A00E0;
      }
      
      .checkbox-inner {
        width: 20rpx;
        height: 20rpx;
        background-color: #fff;
      }
    }
    
    .level-content {
      flex: 1;
      
      .level-name {
        font-size: 28rpx;
        color: #333;
        font-weight: bold;
      }
      
      .level-desc {
        font-size: 24rpx;
        color: #666;
        margin-top: 6rpx;
      }
    }
  }
}

.greeting-preview {
  .preview-title {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 16rpx;
  }
  
  .preview-card {
    background-color: #f9f9f9;
    border-radius: 12rpx;
    overflow: hidden;
    
    .preview-header {
      display: flex;
      align-items: center;
      padding: 20rpx;
      background-color: #fff;
      border-bottom: 1rpx solid #f0f0f0;
      
      .preview-logo {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        margin-right: 16rpx;
      }
      
      .preview-shop-name {
        font-size: 28rpx;
        color: #333;
        font-weight: bold;
      }
    }
    
    .preview-content {
      padding: 30rpx 20rpx;
      
      .preview-greeting {
        font-size: 28rpx;
        color: #333;
        line-height: 1.6;
      }
    }
    
    .preview-footer {
      padding: 20rpx;
      display: flex;
      justify-content: center;
      border-top: 1rpx solid #f0f0f0;
      
      .preview-btn {
        padding: 12rpx 30rpx;
        background-color: #4A00E0;
        color: #fff;
        font-size: 28rpx;
        border-radius: 30rpx;
      }
    }
  }
}

.bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  .save-btn {
    width: 100%;
    height: 90rpx;
    background-color: #4A00E0;
    color: #fff;
    font-size: 32rpx;
    border-radius: 45rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
<!-- 生日礼包页面结束 --> 