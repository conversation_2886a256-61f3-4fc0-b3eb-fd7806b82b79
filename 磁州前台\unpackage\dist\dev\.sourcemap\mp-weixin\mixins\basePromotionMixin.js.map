{"version": 3, "file": "basePromotionMixin.js", "sources": ["mixins/basePromotionMixin.js"], "sourcesContent": ["/**\r\n * 基础推广能力混入\r\n * 为所有页面提供基本的推广功能\r\n */\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 是否有推广权限\r\n      hasPromotionPermission: false,\r\n      // 是否可以获取佣金\r\n      canEarnCommission: false,\r\n      // 页面类型，用于确定推广内容类型\r\n      pageType: '',\r\n      // 推广数据\r\n      promotionData: {},\r\n      // 是否显示悬浮推广按钮\r\n      showFloatPromotionButton: false\r\n    };\r\n  },\r\n\r\n  created() {\r\n    // 检查页面是否支持推广\r\n    this.initPromotion();\r\n  },\r\n  \r\n  mounted() {\r\n    // 在mounted阶段再次检查，确保DOM已经加载完成\r\n    this.checkAndEnablePromotion();\r\n  },\r\n\r\n  methods: {\r\n    /**\r\n     * 初始化推广能力\r\n     */\r\n    async initPromotion() {\r\n      // 确定页面类型\r\n      this.detectPageType();\r\n\r\n      if (!this.pageType) {\r\n        return;\r\n      }\r\n\r\n      // 检查权限\r\n      await this.checkPromotionPermission();\r\n\r\n      // 生成推广数据\r\n      this.generatePromotionData();\r\n    },\r\n    \r\n    /**\r\n     * 检查并启用推广功能\r\n     * 自动检测当前页面内容，并决定是否显示推广按钮\r\n     */\r\n    checkAndEnablePromotion() {\r\n      try {\r\n        // 获取当前页面路径\r\n        const pages = getCurrentPages();\r\n        const currentPage = pages[pages.length - 1];\r\n        const route = currentPage?.route || '';\r\n        \r\n        // 检测是否是详情页\r\n        if (route.includes('detail')) {\r\n          // 获取页面数据\r\n          const pageData = currentPage.data || {};\r\n          \r\n          // 尝试从页面数据中获取内容ID和类型\r\n          let contentId = '';\r\n          let contentType = '';\r\n          \r\n          // 检测商品详情\r\n          if (pageData.product || pageData.productInfo || pageData.goodsInfo) {\r\n            const product = pageData.product || pageData.productInfo || pageData.goodsInfo;\r\n            contentId = product.id || product._id;\r\n            contentType = 'product';\r\n          } \r\n          // 检测拼车详情\r\n          else if (pageData.carpool || pageData.carpoolInfo) {\r\n            const carpool = pageData.carpool || pageData.carpoolInfo;\r\n            contentId = carpool.id || carpool._id;\r\n            contentType = 'carpool';\r\n          }\r\n          // 检测商家详情\r\n          else if (pageData.shop || pageData.shopData || pageData.merchant) {\r\n            const shop = pageData.shop || pageData.shopData || pageData.merchant;\r\n            contentId = shop.id || shop._id;\r\n            contentType = 'merchant';\r\n          }\r\n          // 检测房产详情\r\n          else if (pageData.house || pageData.houseInfo) {\r\n            const house = pageData.house || pageData.houseInfo;\r\n            contentId = house.id || house._id;\r\n            contentType = 'house';\r\n          }\r\n          // 检测服务详情\r\n          else if (pageData.service || pageData.serviceInfo) {\r\n            const service = pageData.service || pageData.serviceInfo;\r\n            contentId = service.id || service._id;\r\n            contentType = 'service';\r\n          }\r\n          // 检测内容详情\r\n          else if (pageData.article || pageData.content || pageData.news) {\r\n            const content = pageData.article || pageData.content || pageData.news;\r\n            contentId = content.id || content._id;\r\n            contentType = 'content';\r\n          }\r\n          \r\n          // 如果找到了内容ID和类型，自动添加推广按钮\r\n          if (contentId && contentType) {\r\n            this.pageType = contentType;\r\n            this.promotionData = { id: contentId };\r\n            \r\n            // 设置推广权限\r\n            this.hasPromotionPermission = true;\r\n            \r\n            // 添加悬浮推广按钮\r\n            this.addFloatPromotionButton();\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('自动检测推广功能失败', error);\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 添加悬浮推广按钮\r\n     */\r\n    addFloatPromotionButton() {\r\n      // 设置显示悬浮按钮标志\r\n      this.showFloatPromotionButton = true;\r\n      \r\n      // 如果是Vue3环境，可以使用动态组件\r\n      if (this.$nextTick) {\r\n        this.$nextTick(() => {\r\n          // 确保在DOM更新后执行\r\n          console.log('添加悬浮推广按钮成功');\r\n        });\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 检测页面类型\r\n     */\r\n    detectPageType() {\r\n      // 获取当前页面路径\r\n      const pages = getCurrentPages();\r\n      const currentPage = pages[pages.length - 1];\r\n      const route = currentPage?.route || '';\r\n\r\n      // 根据路径判断页面类型\r\n      if (route.includes('carpool')) {\r\n        this.pageType = 'carpool';\r\n      } else if (route.includes('secondhand')) {\r\n        this.pageType = 'secondhand';\r\n      } else if (route.includes('house')) {\r\n        this.pageType = 'house';\r\n      } else if (route.includes('service')) {\r\n        this.pageType = 'service';\r\n      } else if (route.includes('community')) {\r\n        this.pageType = 'community';\r\n      } else if (route.includes('product') || route.includes('goods')) {\r\n        this.pageType = 'product';\r\n      } else if (route.includes('article') || route.includes('news')) {\r\n        this.pageType = 'content';\r\n      } else if (route.includes('activity')) {\r\n        this.pageType = 'activity';\r\n      }\r\n\r\n      // 也可以从页面meta中获取\r\n      this.pageType = this.pageType || this.$route?.meta?.pageType || '';\r\n    },\r\n\r\n    /**\r\n     * 检查用户推广权限\r\n     */\r\n    async checkPromotionPermission() {\r\n      try {\r\n        // 这里可以调用API获取权限\r\n        // 暂时简化处理，默认有基础权限\r\n        this.hasPromotionPermission = this.isContentOwner();\r\n\r\n        // 检查是否可以获取佣金\r\n        this.canEarnCommission = this.isDistributor() && this.isCommissionContent();\r\n      } catch (e) {\r\n        console.error('获取推广权限失败', e);\r\n        this.hasPromotionPermission = false;\r\n        this.canEarnCommission = false;\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 判断当前用户是否是内容所有者\r\n     */\r\n    isContentOwner() {\r\n      // 这里需要子类根据具体业务实现\r\n      // 基础实现默认返回true，方便测试\r\n      return true;\r\n    },\r\n\r\n    /**\r\n     * 判断当前用户是否是分销员\r\n     */\r\n    isDistributor() {\r\n      // 这里可以检查用户角色\r\n      // 暂时返回true用于演示\r\n      return true;\r\n    },\r\n\r\n    /**\r\n     * 判断当前内容是否支持佣金\r\n     */\r\n    isCommissionContent() {\r\n      // 子类需要重写此方法，判断该内容是否支持分佣\r\n      // 默认支持\r\n      return true;\r\n    },\r\n\r\n    /**\r\n     * 生成推广数据\r\n     */\r\n    generatePromotionData() {\r\n      // 子类需要重写此方法，提供具体的推广数据\r\n      this.promotionData = {};\r\n    },\r\n\r\n    /**\r\n     * 打开推广工具\r\n     */\r\n    openPromotionTools() {\r\n      if (!this.hasPromotionPermission) {\r\n        uni.showToast({\r\n          title: '暂无推广权限',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 导入推广服务\r\n      const promotionService = require('@/utils/promotionService').default;\r\n      promotionService.showPromotionTools(this.pageType, this.promotionData);\r\n    },\r\n    \r\n    /**\r\n     * 通用推广方法，可以在任何页面调用\r\n     */\r\n    showPromotion() {\r\n      if (this.pageType && this.promotionData.id) {\r\n        this.openPromotionTools();\r\n      } else {\r\n        uni.showToast({\r\n          title: '当前内容不支持推广',\r\n          icon: 'none'\r\n        });\r\n      }\r\n    }\r\n  }\r\n}; "], "names": ["uni"], "mappings": ";;AAIA,MAAe,qBAAA;AAAA,EACb,OAAO;AACL,WAAO;AAAA;AAAA,MAEL,wBAAwB;AAAA;AAAA,MAExB,mBAAmB;AAAA;AAAA,MAEnB,UAAU;AAAA;AAAA,MAEV,eAAe,CAAE;AAAA;AAAA,MAEjB,0BAA0B;AAAA,IAChC;AAAA,EACG;AAAA,EAED,UAAU;AAER,SAAK,cAAa;AAAA,EACnB;AAAA,EAED,UAAU;AAER,SAAK,wBAAuB;AAAA,EAC7B;AAAA,EAED,SAAS;AAAA;AAAA;AAAA;AAAA,IAIP,MAAM,gBAAgB;AAEpB,WAAK,eAAc;AAEnB,UAAI,CAAC,KAAK,UAAU;AAClB;AAAA,MACD;AAGD,YAAM,KAAK;AAGX,WAAK,sBAAqB;AAAA,IAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,IAMD,0BAA0B;AACxB,UAAI;AAEF,cAAM,QAAQ;AACd,cAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,cAAM,SAAQ,2CAAa,UAAS;AAGpC,YAAI,MAAM,SAAS,QAAQ,GAAG;AAE5B,gBAAM,WAAW,YAAY,QAAQ;AAGrC,cAAI,YAAY;AAChB,cAAI,cAAc;AAGlB,cAAI,SAAS,WAAW,SAAS,eAAe,SAAS,WAAW;AAClE,kBAAM,UAAU,SAAS,WAAW,SAAS,eAAe,SAAS;AACrE,wBAAY,QAAQ,MAAM,QAAQ;AAClC,0BAAc;AAAA,UACf,WAEQ,SAAS,WAAW,SAAS,aAAa;AACjD,kBAAM,UAAU,SAAS,WAAW,SAAS;AAC7C,wBAAY,QAAQ,MAAM,QAAQ;AAClC,0BAAc;AAAA,UACf,WAEQ,SAAS,QAAQ,SAAS,YAAY,SAAS,UAAU;AAChE,kBAAM,OAAO,SAAS,QAAQ,SAAS,YAAY,SAAS;AAC5D,wBAAY,KAAK,MAAM,KAAK;AAC5B,0BAAc;AAAA,UACf,WAEQ,SAAS,SAAS,SAAS,WAAW;AAC7C,kBAAM,QAAQ,SAAS,SAAS,SAAS;AACzC,wBAAY,MAAM,MAAM,MAAM;AAC9B,0BAAc;AAAA,UACf,WAEQ,SAAS,WAAW,SAAS,aAAa;AACjD,kBAAM,UAAU,SAAS,WAAW,SAAS;AAC7C,wBAAY,QAAQ,MAAM,QAAQ;AAClC,0BAAc;AAAA,UACf,WAEQ,SAAS,WAAW,SAAS,WAAW,SAAS,MAAM;AAC9D,kBAAM,UAAU,SAAS,WAAW,SAAS,WAAW,SAAS;AACjE,wBAAY,QAAQ,MAAM,QAAQ;AAClC,0BAAc;AAAA,UACf;AAGD,cAAI,aAAa,aAAa;AAC5B,iBAAK,WAAW;AAChB,iBAAK,gBAAgB,EAAE,IAAI,UAAS;AAGpC,iBAAK,yBAAyB;AAG9B,iBAAK,wBAAuB;AAAA,UAC7B;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,uCAAc,cAAc,KAAK;AAAA,MAClC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,0BAA0B;AAExB,WAAK,2BAA2B;AAGhC,UAAI,KAAK,WAAW;AAClB,aAAK,UAAU,MAAM;AAEnBA,wBAAAA,MAAA,MAAA,OAAA,uCAAY,YAAY;AAAA,QAClC,CAAS;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,iBAAiB;;AAEf,YAAM,QAAQ;AACd,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,SAAQ,2CAAa,UAAS;AAGpC,UAAI,MAAM,SAAS,SAAS,GAAG;AAC7B,aAAK,WAAW;AAAA,MACjB,WAAU,MAAM,SAAS,YAAY,GAAG;AACvC,aAAK,WAAW;AAAA,MACjB,WAAU,MAAM,SAAS,OAAO,GAAG;AAClC,aAAK,WAAW;AAAA,MACjB,WAAU,MAAM,SAAS,SAAS,GAAG;AACpC,aAAK,WAAW;AAAA,MACjB,WAAU,MAAM,SAAS,WAAW,GAAG;AACtC,aAAK,WAAW;AAAA,MACxB,WAAiB,MAAM,SAAS,SAAS,KAAK,MAAM,SAAS,OAAO,GAAG;AAC/D,aAAK,WAAW;AAAA,MACxB,WAAiB,MAAM,SAAS,SAAS,KAAK,MAAM,SAAS,MAAM,GAAG;AAC9D,aAAK,WAAW;AAAA,MACjB,WAAU,MAAM,SAAS,UAAU,GAAG;AACrC,aAAK,WAAW;AAAA,MACjB;AAGD,WAAK,WAAW,KAAK,cAAY,gBAAK,WAAL,mBAAa,SAAb,mBAAmB,aAAY;AAAA,IACjE;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,2BAA2B;AAC/B,UAAI;AAGF,aAAK,yBAAyB,KAAK;AAGnC,aAAK,oBAAoB,KAAK,cAAa,KAAM,KAAK;MACvD,SAAQ,GAAG;AACVA,sBAAc,MAAA,MAAA,SAAA,uCAAA,YAAY,CAAC;AAC3B,aAAK,yBAAyB;AAC9B,aAAK,oBAAoB;AAAA,MAC1B;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,iBAAiB;AAGf,aAAO;AAAA,IACR;AAAA;AAAA;AAAA;AAAA,IAKD,gBAAgB;AAGd,aAAO;AAAA,IACR;AAAA;AAAA;AAAA;AAAA,IAKD,sBAAsB;AAGpB,aAAO;AAAA,IACR;AAAA;AAAA;AAAA;AAAA,IAKD,wBAAwB;AAEtB,WAAK,gBAAgB;IACtB;AAAA;AAAA;AAAA;AAAA,IAKD,qBAAqB;AACnB,UAAI,CAAC,KAAK,wBAAwB;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAChB,CAAS;AACD;AAAA,MACD;AAGD,YAAM,mBAAmB,QAAQ,0BAA0B,EAAE;AAC7D,uBAAiB,mBAAmB,KAAK,UAAU,KAAK,aAAa;AAAA,IACtE;AAAA;AAAA;AAAA;AAAA,IAKD,gBAAgB;AACd,UAAI,KAAK,YAAY,KAAK,cAAc,IAAI;AAC1C,aAAK,mBAAkB;AAAA,MAC/B,OAAa;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAChB,CAAS;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACH;;"}