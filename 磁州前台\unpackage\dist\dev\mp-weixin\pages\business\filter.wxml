<view class="business-filter-container data-v-13788ce4"><view class="custom-navbar data-v-13788ce4" style="{{'padding-top:' + c}}"><view class="back-btn data-v-13788ce4" bindtap="{{a}}"><view class="back-icon data-v-13788ce4"></view></view><view class="navbar-title data-v-13788ce4">{{b}}</view><view class="navbar-right data-v-13788ce4"></view></view><view class="search-container data-v-13788ce4"><view class="search-box data-v-13788ce4"><image class="search-icon data-v-13788ce4" src="{{d}}"></image><input class="search-input data-v-13788ce4" type="text" placeholder="搜索商家名称、商品或服务" confirm-type="search" bindconfirm="{{e}}" value="{{f}}" bindinput="{{g}}"/><view class="search-btn data-v-13788ce4" bindtap="{{h}}">搜索</view></view></view><scroll-view class="top-category-tabs data-v-13788ce4" scroll-x show-scrollbar="{{false}}"><view wx:for="{{i}}" wx:for-item="item" wx:key="b" class="{{['top-category-item', 'data-v-13788ce4', item.c && 'active-top-category']}}" bindtap="{{item.d}}">{{item.a}}</view></scroll-view><scroll-view wx:if="{{j}}" class="subcategory-tabs data-v-13788ce4" scroll-x show-scrollbar="{{false}}"><view wx:for="{{k}}" wx:for-item="subcat" wx:key="b" class="{{['subcategory-item', 'data-v-13788ce4', subcat.c && 'active-subcategory']}}" bindtap="{{subcat.d}}">{{subcat.a}}</view></scroll-view><view class="filter-section data-v-13788ce4"><view class="filter-item data-v-13788ce4" bindtap="{{o}}"><text class="{{['filter-text', 'data-v-13788ce4', m && 'active-filter']}}">{{l}}</text><view class="{{['filter-arrow', 'data-v-13788ce4', n && 'arrow-up']}}"></view></view><view class="filter-item data-v-13788ce4" bindtap="{{s}}"><text class="{{['filter-text', 'data-v-13788ce4', q && 'active-filter']}}">{{p}}</text><view class="{{['filter-arrow', 'data-v-13788ce4', r && 'arrow-up']}}"></view></view></view><view wx:if="{{t}}" class="selected-filters data-v-13788ce4"><scroll-view scroll-x class="filter-tags-scroll data-v-13788ce4" show-scrollbar="false"><view class="filter-tags data-v-13788ce4"><view wx:if="{{v}}" class="filter-tag data-v-13788ce4">{{w}} <text class="tag-close data-v-13788ce4" bindtap="{{x}}">×</text></view><view wx:if="{{y}}" class="filter-tag data-v-13788ce4">{{z}} <text class="tag-close data-v-13788ce4" bindtap="{{A}}">×</text></view><view wx:if="{{B}}" class="filter-tag data-v-13788ce4">{{C}} <text class="tag-close data-v-13788ce4" bindtap="{{D}}">×</text></view><view wx:if="{{E}}" class="filter-tag data-v-13788ce4">{{F}} <text class="tag-close data-v-13788ce4" bindtap="{{G}}">×</text></view><view class="reset-all data-v-13788ce4" bindtap="{{H}}">清除全部</view></view></scroll-view></view><view wx:if="{{I}}" class="filter-dropdown area-dropdown data-v-13788ce4"><scroll-view scroll-y class="dropdown-scroll data-v-13788ce4"><view wx:for="{{J}}" wx:for-item="area" wx:key="c" class="{{['dropdown-item', 'data-v-13788ce4', area.d && 'active-item']}}" bindtap="{{area.e}}"><text class="dropdown-item-text data-v-13788ce4">{{area.a}}</text><text wx:if="{{area.b}}" class="dropdown-item-check data-v-13788ce4">✓</text></view></scroll-view></view><view wx:if="{{K}}" class="filter-dropdown sort-dropdown data-v-13788ce4"><view wx:for="{{L}}" wx:for-item="sort" wx:key="c" class="{{['dropdown-item', 'data-v-13788ce4', sort.d && 'active-item']}}" bindtap="{{sort.e}}"><text class="dropdown-item-text data-v-13788ce4">{{sort.a}}</text><text wx:if="{{sort.b}}" class="dropdown-item-check data-v-13788ce4">✓</text></view></view><view wx:if="{{M}}" class="filter-mask data-v-13788ce4" bindtap="{{N}}"></view><scroll-view scroll-y class="business-list-container data-v-13788ce4" bindscrolltolower="{{U}}" refresher-enabled refresher-triggered="{{V}}" bindrefresherrefresh="{{W}}"><view wx:if="{{O}}" class="result-stats data-v-13788ce4"> 共找到 <text class="stats-number data-v-13788ce4">{{P}}</text> 家商户 </view><view wx:if="{{Q}}" class="data-v-13788ce4"><view wx:for="{{R}}" wx:for-item="item" wx:key="n" class="business-item data-v-13788ce4" bindtap="{{item.o}}"><image class="business-logo data-v-13788ce4" src="{{item.a}}" mode="aspectFill"></image><view class="business-info data-v-13788ce4"><view class="business-name-row data-v-13788ce4"><text class="business-name data-v-13788ce4">{{item.b}}</text><text wx:if="{{item.c}}" class="business-distance data-v-13788ce4">{{item.d}}km</text></view><text class="business-desc data-v-13788ce4">{{item.e}}</text><view class="business-meta data-v-13788ce4"><text class="business-category data-v-13788ce4">{{item.f}}</text><text wx:if="{{item.g}}" class="business-subcategory data-v-13788ce4">{{item.h}}</text><text wx:if="{{item.i}}" class="business-scale data-v-13788ce4">{{item.j}}</text><text wx:if="{{item.k}}" class="business-area data-v-13788ce4">{{item.l}}</text></view></view><button class="follow-btn data-v-13788ce4" catchtap="{{item.m}}">+ 关注</button></view><view wx:if="{{S}}" class="loading-more data-v-13788ce4"><text class="loading-text data-v-13788ce4">加载中...</text></view><view wx:else class="loading-more data-v-13788ce4"><text class="loading-text data-v-13788ce4">没有更多了</text></view></view><view wx:else class="empty-state data-v-13788ce4"><image src="{{T}}" mode="aspectFit" class="empty-image data-v-13788ce4"></image><text class="empty-text data-v-13788ce4">暂无相关商家</text><view class="empty-tips data-v-13788ce4"> 尝试调整筛选条件，或查看其他分类 </view></view></scroll-view></view>