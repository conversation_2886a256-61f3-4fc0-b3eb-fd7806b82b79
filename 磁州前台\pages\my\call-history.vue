<template>
  <view class="call-history-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">接打记录</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 顶部选项卡 -->
    <view class="tabs-container" :style="{ top: navbarHeight + 'px' }">
      <view 
        class="tab-item" 
        v-for="(tab, index) in tabs" 
        :key="index"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        <text class="tab-text">{{ tab.name }}</text>
      </view>
      <view class="tab-line" :style="tabLineStyle"></view>
    </view>
    
    <!-- 内容区域 -->
    <view class="content-area" :style="{ paddingTop: (navbarHeight + tabsHeight) + 'px' }">
      <swiper class="content-swiper" :current="currentTab" @change="onSwiperChange">
        <!-- 拨出电话 -->
        <swiper-item>
          <scroll-view scroll-y class="tab-scroll" @scrolltolower="loadMore(0)" refresher-enabled :refresher-triggered="refreshing[0]" @refresherrefresh="onRefresh(0)">
            <view v-if="outCallList.length > 0" class="call-list">
              <view class="call-item" v-for="(item, index) in outCallList" :key="index">
                <view class="call-left">
                  <image class="call-avatar" :src="item.avatar" mode="aspectFill"></image>
                </view>
                <view class="call-middle">
                  <view class="call-name">{{ item.name }}</view>
                  <view class="call-info">
                    <text class="call-type">{{ item.type }}</text>
                    <text class="call-desc">{{ item.desc }}</text>
                  </view>
                </view>
                <view class="call-right">
                  <text class="call-time">{{ item.time }}</text>
                  <view class="call-again" @click.stop="callAgain(item)">
                    <image class="call-icon" src="/static/images/tabbar/拨打记录.png"></image>
                  </view>
                </view>
              </view>
            </view>
            <view v-else class="empty-view">
              <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
              <view class="empty-text">暂无拨出记录</view>
            </view>
            <view v-if="outCallList.length > 0 && !hasMore[0]" class="list-bottom">没有更多了</view>
          </scroll-view>
        </swiper-item>
        
        <!-- 接听电话 -->
        <swiper-item>
          <scroll-view scroll-y class="tab-scroll" @scrolltolower="loadMore(1)" refresher-enabled :refresher-triggered="refreshing[1]" @refresherrefresh="onRefresh(1)">
            <view v-if="inCallList.length > 0" class="call-list">
              <view class="call-item" v-for="(item, index) in inCallList" :key="index">
                <view class="call-left">
                  <image class="call-avatar" :src="item.avatar" mode="aspectFill"></image>
                </view>
                <view class="call-middle">
                  <view class="call-name">{{ item.name }}</view>
                  <view class="call-info">
                    <text class="call-type">{{ item.type }}</text>
                    <text class="call-desc">{{ item.desc }}</text>
                  </view>
                </view>
                <view class="call-right">
                  <text class="call-time">{{ item.time }}</text>
                  <view class="call-again" @click.stop="callAgain(item)">
                    <image class="call-icon" src="/static/images/tabbar/拨打记录.png"></image>
                  </view>
                </view>
              </view>
            </view>
            <view v-else class="empty-view">
              <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
              <view class="empty-text">暂无接听记录</view>
            </view>
            <view v-if="inCallList.length > 0 && !hasMore[1]" class="list-bottom">没有更多了</view>
          </scroll-view>
        </swiper-item>
        
        <!-- 未接电话 -->
        <swiper-item>
          <scroll-view scroll-y class="tab-scroll" @scrolltolower="loadMore(2)" refresher-enabled :refresher-triggered="refreshing[2]" @refresherrefresh="onRefresh(2)">
            <view v-if="missedCallList.length > 0" class="call-list">
              <view class="call-item" v-for="(item, index) in missedCallList" :key="index">
                <view class="call-left">
                  <image class="call-avatar" :src="item.avatar" mode="aspectFill"></image>
                </view>
                <view class="call-middle">
                  <view class="call-name call-missed">{{ item.name }}</view>
                  <view class="call-info">
                    <text class="call-type call-missed">{{ item.type }}</text>
                    <text class="call-desc">{{ item.desc }}</text>
                  </view>
                </view>
                <view class="call-right">
                  <text class="call-time">{{ item.time }}</text>
                  <view class="call-again" @click.stop="callAgain(item)">
                    <image class="call-icon" src="/static/images/tabbar/拨打记录.png"></image>
                  </view>
                </view>
              </view>
            </view>
            <view v-else class="empty-view">
              <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
              <view class="empty-text">暂无未接来电</view>
            </view>
            <view v-if="missedCallList.length > 0 && !hasMore[2]" class="list-bottom">没有更多了</view>
          </scroll-view>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20,
      navbarHeight: 64, // 导航栏高度
      tabsHeight: 44, // 选项卡高度
      tabs: [
        { name: '拨出' },
        { name: '接听' },
        { name: '未接' }
      ],
      currentTab: 0,
      outCallList: [],
      inCallList: [],
      missedCallList: [],
      page: [1, 1, 1], // 当前页码
      pageSize: 10, // 每页显示数量
      hasMore: [true, true, true], // 是否有更多数据
      refreshing: [false, false, false], // 刷新状态
    }
  },
  computed: {
    tabLineStyle() {
      return {
        transform: `translateX(${this.currentTab * (100 / this.tabs.length)}%)`,
        width: `${100 / this.tabs.length}%`
      }
    }
  },
  onLoad() {
    // 获取状态栏高度
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 44;
    
    // 加载初始数据
    this.loadOutCallList();
    this.loadInCallList();
    this.loadMissedCallList();
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 切换选项卡
    switchTab(index) {
      this.currentTab = index;
    },
    
    // 轮播图切换事件
    onSwiperChange(e) {
      this.currentTab = e.detail.current;
    },
    
    // 加载拨出电话列表
    loadOutCallList() {
      // 模拟请求延迟
      setTimeout(() => {
        // 模拟数据
        const mockData = Array.from({ length: 10 }, (_, i) => ({
          id: `out_${this.page[0]}_${i}`,
          name: `张师傅 ${this.page[0]}_${i}`,
          avatar: '/static/images/avatar.png',
          type: '店铺转让',
          desc: '通话时长: 2分30秒',
          time: '2023-10-15 14:30',
          phone: '13812345678'
        }));
        
        if (this.page[0] === 1) {
          this.outCallList = mockData;
        } else {
          this.outCallList = [...this.outCallList, ...mockData];
        }
        
        // 模拟是否还有更多数据
        this.hasMore[0] = this.page[0] < 3;
        
        // 关闭刷新状态
        this.refreshing[0] = false;
      }, 500);
    },
    
    // 加载接听电话列表
    loadInCallList() {
      // 模拟请求延迟
      setTimeout(() => {
        // 模拟数据
        const mockData = Array.from({ length: 10 }, (_, i) => ({
          id: `in_${this.page[1]}_${i}`,
          name: `李客户 ${this.page[1]}_${i}`,
          avatar: '/static/images/avatar.png',
          type: '求职信息',
          desc: '通话时长: 1分45秒',
          time: '2023-10-14 10:20',
          phone: '13987654321'
        }));
        
        if (this.page[1] === 1) {
          this.inCallList = mockData;
        } else {
          this.inCallList = [...this.inCallList, ...mockData];
        }
        
        // 模拟是否还有更多数据
        this.hasMore[1] = this.page[1] < 3;
        
        // 关闭刷新状态
        this.refreshing[1] = false;
      }, 500);
    },
    
    // 加载未接电话列表
    loadMissedCallList() {
      // 模拟请求延迟
      setTimeout(() => {
        // 模拟数据
        const mockData = Array.from({ length: 10 }, (_, i) => ({
          id: `missed_${this.page[2]}_${i}`,
          name: `王经理 ${this.page[2]}_${i}`,
          avatar: '/static/images/avatar.png',
          type: '招聘信息',
          desc: '未接来电',
          time: '2023-10-13 16:05',
          phone: '13755667788'
        }));
        
        if (this.page[2] === 1) {
          this.missedCallList = mockData;
        } else {
          this.missedCallList = [...this.missedCallList, ...mockData];
        }
        
        // 模拟是否还有更多数据
        this.hasMore[2] = this.page[2] < 3;
        
        // 关闭刷新状态
        this.refreshing[2] = false;
      }, 500);
    },
    
    // 加载更多数据
    loadMore(tabIndex) {
      if (!this.hasMore[tabIndex]) return;
      
      this.page[tabIndex]++;
      if (tabIndex === 0) {
        this.loadOutCallList();
      } else if (tabIndex === 1) {
        this.loadInCallList();
      } else {
        this.loadMissedCallList();
      }
    },
    
    // 下拉刷新
    onRefresh(tabIndex) {
      this.refreshing[tabIndex] = true;
      this.page[tabIndex] = 1;
      this.hasMore[tabIndex] = true;
      
      if (tabIndex === 0) {
        this.loadOutCallList();
      } else if (tabIndex === 1) {
        this.loadInCallList();
      } else {
        this.loadMissedCallList();
      }
    },
    
    // 再次拨打电话
    callAgain(item) {
      uni.makePhoneCall({
        phoneNumber: item.phone,
        success: () => {
          console.log('拨打电话成功');
        },
        fail: (err) => {
          console.error('拨打电话失败', err);
          uni.showToast({
            title: '拨打电话失败',
            icon: 'none'
          });
        }
      });
    }
  }
}
</script>

<style>
.call-history-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  position: relative;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  background-color: #0052CC;
  color: #fff;
  z-index: 100;
}

.navbar-left {
  width: 80rpx;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
}

.navbar-right {
  width: 80rpx;
  text-align: right;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 选项卡样式 */
.tabs-container {
  position: fixed;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  z-index: 99;
}

.tab-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.tab-text {
  font-size: 14px;
  color: #333;
  padding: 0 15px;
}

.tab-item.active .tab-text {
  color: #0052CC;
  font-weight: bold;
}

.tab-line {
  position: absolute;
  bottom: 0;
  height: 3px;
  background-color: #0052CC;
  border-radius: 2px;
  transition: transform 0.3s;
}

/* 内容区域 */
.content-area {
  position: relative;
  height: 100vh;
}

.content-swiper {
  height: 100%;
}

.tab-scroll {
  height: 100%;
}

/* 通话记录列表 */
.call-list {
  padding: 15px;
}

.call-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.call-left {
  margin-right: 15px;
}

.call-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.call-middle {
  flex: 1;
}

.call-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.call-info {
  font-size: 13px;
  color: #999;
}

.call-type {
  margin-right: 10px;
  color: #666;
}

.call-desc {
  color: #999;
}

.call-right {
  text-align: right;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.call-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 10px;
}

.call-again {
  width: 60rpx;
  height: 60rpx;
  background-color: #e6f7ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.call-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 未接电话样式 */
.call-missed {
  color: #ff4d4f !important;
}

/* 空状态 */
.empty-view {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 100px;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 列表底部 */
.list-bottom {
  text-align: center;
  padding: 15px 0;
  font-size: 14px;
  color: #999;
}
</style> 