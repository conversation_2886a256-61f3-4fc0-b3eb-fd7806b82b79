"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const subPackages_activityShowcase_utils_configManager = require("../../utils/configManager.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
if (!Math) {
  ActivityCenter();
}
const ActivityCenter = () => "../../components/activity/ActivityCenter.js";
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const currentTab = common_vendor.ref("home");
    const unreadMessageCount = common_vendor.ref(3);
    const banners = common_vendor.ref([
      {
        image: "/static/images/activity/banner-1.jpg",
        title: "618年中大促",
        description: "全场低至5折起"
      },
      {
        image: "/static/images/activity/banner-2.jpg",
        title: "新人专享礼",
        description: "首单立减30元"
      },
      {
        image: "/static/images/activity/banner-3.jpg",
        title: "周末狂欢趴",
        description: "满200减50"
      }
    ]);
    const categories = common_vendor.ref([
      { name: "限时秒杀", type: "flash" },
      { name: "拼团活动", type: "group" },
      { name: "满减优惠", type: "discount" },
      { name: "优惠券", type: "coupon" }
    ]);
    const loading = common_vendor.ref(false);
    const noMore = common_vendor.ref(false);
    const navbarConfig = common_vendor.computed(() => subPackages_activityShowcase_utils_configManager.configManager.getConfig("structure.navbar") || {});
    const bannerConfig = common_vendor.computed(() => subPackages_activityShowcase_utils_configManager.configManager.getConfig("structure.banner") || {});
    common_vendor.computed(() => subPackages_activityShowcase_utils_configManager.configManager.getConfig("display.cardStyle") || {});
    const navbarTitle = common_vendor.computed(() => navbarConfig.value.title || "活动中心");
    const swiperConfig = common_vendor.computed(() => ({
      autoplay: bannerConfig.value.autoplay !== false,
      circular: bannerConfig.value.circular !== false,
      interval: bannerConfig.value.interval || 5e3,
      duration: bannerConfig.value.duration || 500,
      indicatorColor: bannerConfig.value.indicatorColor || "rgba(255, 255, 255, 0.6)",
      indicatorActiveColor: bannerConfig.value.indicatorActiveColor || "#ffffff"
    }));
    common_vendor.onMounted(async () => {
      common_vendor.index.__f__("log", "at subPackages/activity-showcase/pages/index/index.vue:216", "活动中心页面加载");
      try {
        await subPackages_activityShowcase_utils_configManager.configManager.init();
        common_vendor.index.__f__("log", "at subPackages/activity-showcase/pages/index/index.vue:221", "配置管理器初始化成功");
      } catch (error) {
        common_vendor.index.__f__("warn", "at subPackages/activity-showcase/pages/index/index.vue:223", "配置管理器初始化失败，使用默认配置:", error);
      }
      getUnreadMessageCount();
    });
    function goBack() {
      common_vendor.index.navigateBack();
    }
    function closeActivityCenter() {
      common_vendor.index.navigateBack({
        delta: 1
      });
    }
    function navigateToCategory(type) {
      let url = "";
      switch (type) {
        case "flash":
          url = "/subPackages/activity-showcase/pages/flash-sale/index";
          break;
        case "group":
          url = "/subPackages/activity-showcase/pages/group-buy/index";
          break;
        case "discount":
          url = "/subPackages/activity-showcase/pages/discount/index";
          break;
        case "coupon":
          url = "/subPackages/activity-showcase/pages/coupon/index";
          break;
        default:
          url = "/subPackages/activity-showcase/pages/index/index";
      }
      common_vendor.index.navigateTo({ url });
    }
    function switchTab(tab) {
      if (currentTab.value === tab)
        return;
      currentTab.value = tab;
      switch (tab) {
        case "home":
          break;
        case "discover":
          common_vendor.index.navigateTo({
            url: "/subPackages/activity-showcase/pages/discover/index"
          });
          break;
        case "distribution":
          common_vendor.index.navigateTo({
            url: "/subPackages/activity-showcase/pages/distribution/index"
          });
          break;
        case "message":
          common_vendor.index.navigateTo({
            url: "/subPackages/activity-showcase/pages/message/index"
          });
          break;
        case "my":
          common_vendor.index.navigateTo({
            url: "/subPackages/activity-showcase/pages/my/index"
          });
          break;
      }
    }
    function getUnreadMessageCount() {
      setTimeout(() => {
        unreadMessageCount.value = Math.floor(Math.random() * 10);
      }, 1e3);
    }
    function loadMore() {
      if (loading.value || noMore.value)
        return;
      loading.value = true;
      setTimeout(() => {
        noMore.value = true;
        loading.value = false;
      }, 1500);
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: common_vendor.t(navbarTitle.value),
        d: common_vendor.o(closeActivityCenter),
        e: common_vendor.f(banners.value, (banner, index, i0) => {
          return {
            a: banner.image,
            b: common_vendor.t(banner.title),
            c: common_vendor.t(banner.description),
            d: index
          };
        }),
        f: swiperConfig.value.autoplay,
        g: swiperConfig.value.circular,
        h: swiperConfig.value.indicatorColor,
        i: swiperConfig.value.indicatorActiveColor,
        j: swiperConfig.value.interval,
        k: swiperConfig.value.duration,
        l: common_vendor.f(categories.value, (category, index, i0) => {
          return common_vendor.e({
            a: category.type === "flash"
          }, category.type === "flash" ? {
            b: "91893c28-1-" + i0 + "," + ("91893c28-0-" + i0),
            c: common_vendor.p({
              d: "M13 3L4 14h7v7l9-11h-7V3z",
              fill: "currentColor"
            }),
            d: "91893c28-0-" + i0,
            e: common_vendor.p({
              viewBox: "0 0 24 24",
              xmlns: "http://www.w3.org/2000/svg",
              width: "24",
              height: "24"
            })
          } : category.type === "group" ? {
            g: "91893c28-3-" + i0 + "," + ("91893c28-2-" + i0),
            h: common_vendor.p({
              d: "M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2M9 7a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM23 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75",
              fill: "currentColor"
            }),
            i: "91893c28-2-" + i0,
            j: common_vendor.p({
              viewBox: "0 0 24 24",
              xmlns: "http://www.w3.org/2000/svg",
              width: "24",
              height: "24"
            })
          } : category.type === "discount" ? {
            l: "91893c28-5-" + i0 + "," + ("91893c28-4-" + i0),
            m: common_vendor.p({
              d: "M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",
              fill: "currentColor"
            }),
            n: "91893c28-4-" + i0,
            o: common_vendor.p({
              viewBox: "0 0 24 24",
              xmlns: "http://www.w3.org/2000/svg",
              width: "24",
              height: "24"
            })
          } : category.type === "coupon" ? {
            q: "91893c28-7-" + i0 + "," + ("91893c28-6-" + i0),
            r: common_vendor.p({
              d: "M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z",
              fill: "currentColor"
            }),
            s: "91893c28-6-" + i0,
            t: common_vendor.p({
              viewBox: "0 0 24 24",
              xmlns: "http://www.w3.org/2000/svg",
              width: "24",
              height: "24"
            })
          } : {}, {
            f: category.type === "group",
            k: category.type === "discount",
            p: category.type === "coupon",
            v: common_vendor.n(`icon-${category.type}`),
            w: common_vendor.t(category.name),
            x: index,
            y: common_vendor.o(($event) => navigateToCategory(category.type), index)
          });
        }),
        m: loading.value
      }, loading.value ? {} : {}, {
        n: noMore.value
      }, noMore.value ? {} : {}, {
        o: common_vendor.o(loadMore),
        p: currentTab.value === "home" ? 1 : "",
        q: common_vendor.o(($event) => switchTab("home")),
        r: currentTab.value === "discover" ? 1 : "",
        s: common_vendor.o(($event) => switchTab("discover")),
        t: currentTab.value === "distribution" ? 1 : "",
        v: common_vendor.o(($event) => switchTab("distribution")),
        w: unreadMessageCount.value > 0
      }, unreadMessageCount.value > 0 ? {
        x: common_vendor.t(unreadMessageCount.value > 99 ? "99+" : unreadMessageCount.value)
      } : {}, {
        y: currentTab.value === "message" ? 1 : "",
        z: common_vendor.o(($event) => switchTab("message")),
        A: currentTab.value === "my" ? 1 : "",
        B: common_vendor.o(($event) => switchTab("my"))
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-91893c28"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/index/index.js.map
