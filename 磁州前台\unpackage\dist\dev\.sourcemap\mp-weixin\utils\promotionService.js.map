{"version": 3, "file": "promotionService.js", "sources": ["utils/promotionService.js"], "sourcesContent": ["/**\n * 推广服务\n * 处理各类内容的推广功能\n */\n\nconst promotionService = {\n  /**\n   * 打开推广工具\n   * @param {string} type - 推广类型\n   * @param {object} data - 推广数据\n   */\n  showPromotionTools(type, data) {\n    if (!type || !data || !data.id) {\n      uni.showToast({\n        title: '推广数据不完整',\n        icon: 'none'\n      });\n      return;\n    }\n    \n    // 跳转到推广工具页面\n    uni.navigateTo({\n      url: `/subPackages/promotion/pages/promotion-tool?type=${type}&id=${data.id}`\n    });\n  },\n  \n  /**\n   * 生成分享链接\n   * @param {string} type - 推广类型\n   * @param {string} id - 内容ID\n   * @param {string} [userId] - 推广用户ID，可选\n   * @returns {string} 分享链接\n   */\n  generateShareLink(type, id, userId) {\n    const baseUrl = 'https://cizhou.life';\n    let path = '';\n    \n    switch (type) {\n      case 'carpool':\n        path = `/pages/carpool/detail?id=${id}`;\n        break;\n      case 'product':\n        path = `/pages/product/detail?id=${id}`;\n        break;\n      case 'house':\n        path = `/pages/house/detail?id=${id}`;\n        break;\n      case 'service':\n        path = `/pages/service/detail?id=${id}`;\n        break;\n      case 'merchant':\n        path = `/pages/business/shop-detail?id=${id}`;\n        break;\n      case 'content':\n        path = `/pages/content/detail?id=${id}`;\n        break;\n      case 'activity':\n        path = `/pages/activity/detail?id=${id}`;\n        break;\n      case 'community':\n        path = `/pages/community/detail?id=${id}`;\n        break;\n      default:\n        path = `/pages/index/index?id=${id}&type=${type}`;\n    }\n    \n    // 添加推广用户ID\n    if (userId) {\n      path += `&promoter=${userId}`;\n    }\n    \n    return `${baseUrl}${path}`;\n  },\n  \n  /**\n   * 生成小程序码\n   * @param {string} type - 推广类型\n   * @param {string} id - 内容ID\n   * @param {string} [userId] - 推广用户ID，可选\n   * @returns {Promise<string>} 小程序码临时路径\n   */\n  async generateQrcode(type, id, userId) {\n    return new Promise((resolve, reject) => {\n      uni.showLoading({\n        title: '生成中...'\n      });\n      \n      // 构建参数\n      const scene = userId ? `id=${id}&u=${userId}` : `id=${id}`;\n      const page = this._getPagePath(type);\n      \n      // 调用云函数生成小程序码\n      uni.request({\n        url: 'https://api.cizhou.life/api/promotion/qrcode',\n        method: 'POST',\n        data: {\n          scene,\n          page,\n          width: 280\n        },\n        success: (res) => {\n          uni.hideLoading();\n          if (res.data && res.data.code === 0 && res.data.data) {\n            resolve(res.data.data.tempFilePath);\n          } else {\n            uni.showToast({\n              title: '生成小程序码失败',\n              icon: 'none'\n            });\n            reject(new Error('生成小程序码失败'));\n          }\n        },\n        fail: (err) => {\n          uni.hideLoading();\n          uni.showToast({\n            title: '网络请求失败',\n            icon: 'none'\n          });\n          reject(err);\n        }\n      });\n    });\n  },\n  \n  /**\n   * 生成海报\n   * @param {string} type - 推广类型\n   * @param {object} data - 推广数据\n   * @param {string} userId - 推广用户ID\n   * @param {object} options - 自定义选项\n   * @returns {Promise<string>} 海报临时路径\n   */\n  async generatePoster(type, data, userId, options = {}) {\n    return new Promise((resolve, reject) => {\n      try {\n        uni.showLoading({\n          title: '生成海报中...'\n        });\n        \n        // 创建画布上下文\n        const ctx = uni.createCanvasContext('posterCanvas');\n        \n        // 应用自定义主题颜色\n        const theme = options.theme || { bgColor: '#FFFFFF', textColor: '#333333' };\n        \n        // 绘制背景\n        ctx.setFillStyle(theme.bgColor);\n        ctx.fillRect(0, 0, 600, 900);\n        \n        // 绘制顶部渐变背景\n        const grd = ctx.createLinearGradient(0, 0, 600, 150);\n        if (theme.bgColor === '#FFFFFF' || theme.bgColor === '#F5F7FA') {\n          // 默认蓝色渐变\n          grd.addColorStop(0, '#3846cd');\n          grd.addColorStop(1, '#2c3aa0');\n        } else {\n          // 使用主题颜色的深浅变化\n          grd.addColorStop(0, theme.bgColor);\n          grd.addColorStop(1, this._darkenColor(theme.bgColor, 20));\n        }\n        ctx.setFillStyle(grd);\n        ctx.fillRect(0, 0, 600, 150);\n        \n        // 绘制磁州生活网Logo\n        ctx.setFillStyle('#ffffff');\n        ctx.setFontSize(28);\n        ctx.fillText('磁州生活网', 40, 60);\n        \n        // 绘制标题\n        ctx.setFillStyle(theme.textColor);\n        ctx.setFontSize(36);\n        ctx.fillText(this._truncateText(data.title || '内容标题', 16), 40, 200);\n        \n        // 绘制图片占位区域\n        ctx.setFillStyle('#f5f5f5');\n        ctx.fillRect(40, 240, 520, 300);\n        \n        // 绘制图片中心的图标\n        ctx.setFillStyle('#cccccc');\n        ctx.fillRect(270, 360, 60, 60);\n        ctx.setFillStyle('#ffffff');\n        ctx.fillRect(290, 380, 20, 20);\n        \n        // 绘制内容信息\n        ctx.setFillStyle(theme.textColor === '#FFFFFF' ? '#DDDDDD' : '#666666');\n        ctx.setFontSize(28);\n        \n        let yPosition = 580;\n        \n        // 根据不同类型绘制不同内容\n        switch (type) {\n          case 'carpool':\n            ctx.fillText(`出发: ${data.departure || '起点'}`, 40, yPosition);\n            yPosition += 50;\n            ctx.fillText(`目的地: ${data.destination || '终点'}`, 40, yPosition);\n            yPosition += 50;\n            ctx.fillText(`时间: ${data.departureTime || '出发时间'}`, 40, yPosition);\n            break;\n            \n          case 'product':\n            ctx.fillText(`价格: ¥${data.price || '0.00'}`, 40, yPosition);\n            if (data.originalPrice) {\n              ctx.setFillStyle('#999999');\n              ctx.fillText(`原价: ¥${data.originalPrice}`, 240, yPosition);\n              ctx.setFillStyle(theme.textColor === '#FFFFFF' ? '#DDDDDD' : '#666666');\n            }\n            yPosition += 50;\n            if (data.description) {\n              this._drawWrappedText(ctx, data.description, 40, yPosition, 520, 28, 2);\n              yPosition += 70;\n            }\n            break;\n            \n          case 'house':\n            ctx.fillText(`价格: ${data.price || '0'}${data.priceUnit || ''}`, 40, yPosition);\n            yPosition += 50;\n            ctx.fillText(`${data.roomType || '户型'} | ${data.area || '0'}㎡`, 40, yPosition);\n            yPosition += 50;\n            ctx.fillText(`位置: ${data.location || '地址'}`, 40, yPosition);\n            break;\n            \n          case 'merchant':\n            ctx.fillText(`类型: ${data.category || '商家类型'}`, 40, yPosition);\n            yPosition += 50;\n            ctx.fillText(`地址: ${data.address || '商家地址'}`, 40, yPosition);\n            break;\n            \n          default:\n            if (data.description) {\n              this._drawWrappedText(ctx, data.description, 40, yPosition, 520, 28, 3);\n              yPosition += 100;\n            } else {\n              ctx.fillText('内容描述', 40, yPosition);\n              yPosition += 50;\n            }\n        }\n        \n        // 添加自定义文案\n        if (options.customText) {\n          yPosition += 50;\n          ctx.setFillStyle(theme.textColor);\n          ctx.setFontSize(30);\n          this._drawWrappedText(ctx, options.customText, 40, yPosition, 520, 40, 2);\n          yPosition += 80;\n        }\n        \n        // 绘制二维码区域\n        yPosition = Math.max(yPosition + 30, 700);\n        \n        // 绘制二维码背景\n        ctx.setFillStyle(theme.bgColor === '#FFFFFF' ? '#f8f8f8' : this._lightenColor(theme.bgColor, 10));\n        ctx.fillRect(40, yPosition, 520, 160);\n        \n        // 绘制二维码占位\n        ctx.setFillStyle('#DDDDDD');\n        ctx.fillRect(420, yPosition + 10, 120, 120);\n        \n        // 绘制二维码中心的小图标\n        ctx.setFillStyle('#AAAAAA');\n        ctx.fillRect(460, yPosition + 50, 40, 40);\n        \n        // 绘制二维码提示文字\n        ctx.setFillStyle(theme.textColor);\n        ctx.setFontSize(24);\n        ctx.fillText('扫码查看详情', 60, yPosition + 50);\n        \n        ctx.setFillStyle(theme.textColor === '#FFFFFF' ? '#CCCCCC' : '#666666');\n        ctx.setFontSize(20);\n        ctx.fillText('长按识别小程序码', 60, yPosition + 90);\n        \n        // 显示推广员ID\n        if (options.showId && userId) {\n          ctx.setFillStyle(theme.textColor === '#FFFFFF' ? '#CCCCCC' : '#666666');\n          ctx.setFontSize(18);\n          ctx.fillText(`推广员ID: ${userId}`, 60, yPosition + 130);\n        }\n        \n        // 绘制底部版权信息\n        ctx.setFillStyle('#999999');\n        ctx.setFontSize(20);\n        ctx.fillText('来自磁州生活网', 240, 880);\n        \n        // 绘制完成\n        ctx.draw(false, () => {\n          setTimeout(() => {\n            uni.canvasToTempFilePath({\n              canvasId: 'posterCanvas',\n              success: (res) => {\n                uni.hideLoading();\n                resolve(res.tempFilePath);\n                \n                // 记录生成海报事件\n                this.recordPromotion(type, data.id || '0', 'poster');\n              },\n              fail: (err) => {\n                uni.hideLoading();\n                console.error('生成海报失败', err);\n                \n                // 如果生成失败，使用模板图片\n                if (options.template) {\n                  resolve(options.template);\n                } else {\n                  // 根据类型返回不同的模拟海报路径\n                  let posterPath;\n                  switch (type) {\n                    case 'carpool':\n                      posterPath = '/static/images/distribution/carpool-poster.png';\n                      break;\n                    case 'product':\n                      posterPath = '/static/images/distribution/product-poster.png';\n                      break;\n                    case 'house':\n                      posterPath = '/static/images/distribution/house-poster.png';\n                      break;\n                    case 'merchant':\n                      posterPath = '/static/images/distribution/merchant-poster.png';\n                      break;\n                    case 'service':\n                      posterPath = '/static/images/distribution/service-poster.png';\n                      break;\n                    default:\n                      posterPath = '/static/images/distribution/default-poster.png';\n                  }\n                  resolve(posterPath);\n                }\n              }\n            });\n          }, 500); // 给一点时间让Canvas渲染完成\n        });\n      } catch (err) {\n        uni.hideLoading();\n        console.error('生成海报失败', err);\n        reject(err);\n      }\n    });\n  },\n  \n  /**\n   * 保存海报到相册\n   * @param {string} filePath - 海报文件路径\n   * @returns {Promise<boolean>} 是否保存成功\n   */\n  async savePosterToAlbum(filePath) {\n    return new Promise((resolve, reject) => {\n      uni.saveImageToPhotosAlbum({\n        filePath,\n        success: () => {\n          uni.showToast({\n            title: '已保存到相册',\n            icon: 'success'\n          });\n          resolve(true);\n        },\n        fail: (err) => {\n          if (err.errMsg.indexOf('auth deny') > -1) {\n            uni.showModal({\n              title: '提示',\n              content: '需要授权保存到相册',\n        success: (res) => {\n                if (res.confirm) {\n                  uni.openSetting();\n                }\n              }\n            });\n          } else {\n            uni.showToast({\n              title: '保存失败',\n              icon: 'none'\n            });\n          }\n          reject(err);\n        }\n      });\n    });\n  },\n  \n  /**\n   * 记录推广数据\n   * @param {string} type - 推广类型\n   * @param {string} id - 内容ID\n   * @param {string} method - 推广方式：link, qrcode, poster\n   * @returns {Promise<boolean>} 是否记录成功\n   */\n  async recordPromotion(type, id, method) {\n    try {\n      // 在开发环境中，只记录日志，不发送请求\n      console.log('记录推广数据', {\n        contentType: type,\n        contentId: id,\n        promotionMethod: method\n      });\n      return true;\n    } catch (err) {\n      console.error('记录推广数据失败', err);\n      return false;\n    }\n  },\n  \n  /**\n   * 获取页面路径\n   * @private\n   * @param {string} type - 推广类型\n   * @returns {string} 页面路径\n   */\n  _getPagePath(type) {\n    switch (type) {\n      case 'carpool':\n        return 'pages/carpool/detail';\n      case 'product':\n        return 'pages/product/detail';\n      case 'house':\n        return 'pages/house/detail';\n      case 'service':\n        return 'pages/service/detail';\n      case 'merchant':\n        return 'pages/business/shop-detail';\n      case 'content':\n        return 'pages/content/detail';\n      case 'activity':\n        return 'pages/activity/detail';\n      case 'community':\n        return 'pages/community/detail';\n      default:\n        return 'pages/index/index';\n    }\n  },\n  \n  /**\n   * 截断文本\n   * @private\n   * @param {string} text - 原文本\n   * @param {number} maxLength - 最大长度\n   * @returns {string} 截断后的文本\n   */\n  _truncateText(text, maxLength) {\n    if (!text) return '';\n    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;\n  },\n  \n  /**\n   * 绘制自动换行文本\n   * @private\n   * @param {object} ctx - 画布上下文\n   * @param {string} text - 文本内容\n   * @param {number} x - x坐标\n   * @param {number} y - y坐标\n   * @param {number} maxWidth - 最大宽度\n   * @param {number} lineHeight - 行高\n   * @param {number} maxLines - 最大行数\n   */\n  _drawWrappedText(ctx, text, x, y, maxWidth, lineHeight, maxLines) {\n    if (!text) return;\n    \n    const chars = text.split('');\n    let line = '';\n    let lineCount = 0;\n    \n    for (let i = 0; i < chars.length; i++) {\n      const testLine = line + chars[i];\n      const metrics = ctx.measureText(testLine);\n      const testWidth = metrics.width;\n      \n      if (testWidth > maxWidth && i > 0) {\n        ctx.fillText(line, x, y);\n        line = chars[i];\n        y += lineHeight;\n        lineCount++;\n        \n        if (lineCount >= maxLines - 1 && i < chars.length - 1) {\n          // 达到最大行数，显示省略号\n          line += '...';\n          break;\n        }\n      } else {\n        line = testLine;\n      }\n    }\n    \n    ctx.fillText(line, x, y);\n  },\n  \n  /**\n   * 使颜色变深\n   * @private\n   * @param {string} hex - 十六进制颜色值\n   * @param {number} percent - 变深的百分比\n   * @returns {string} 变深后的颜色\n   */\n  _darkenColor(hex, percent) {\n    // 移除#号\n    hex = hex.replace('#', '');\n    \n    // 转换为RGB\n    let r = parseInt(hex.substring(0, 2), 16);\n    let g = parseInt(hex.substring(2, 4), 16);\n    let b = parseInt(hex.substring(4, 6), 16);\n    \n    // 变暗\n    r = Math.floor(r * (100 - percent) / 100);\n    g = Math.floor(g * (100 - percent) / 100);\n    b = Math.floor(b * (100 - percent) / 100);\n    \n    // 确保值在0-255范围内\n    r = (r < 0) ? 0 : ((r > 255) ? 255 : r);\n    g = (g < 0) ? 0 : ((g > 255) ? 255 : g);\n    b = (b < 0) ? 0 : ((b > 255) ? 255 : b);\n    \n    // 转换回十六进制\n    return '#' + \n      (r.toString(16).padStart(2, '0')) + \n      (g.toString(16).padStart(2, '0')) + \n      (b.toString(16).padStart(2, '0'));\n  },\n  \n  /**\n   * 使颜色变亮\n   * @private\n   * @param {string} hex - 十六进制颜色值\n   * @param {number} percent - 变亮的百分比\n   * @returns {string} 变亮后的颜色\n   */\n  _lightenColor(hex, percent) {\n    // 移除#号\n    hex = hex.replace('#', '');\n    \n    // 转换为RGB\n    let r = parseInt(hex.substring(0, 2), 16);\n    let g = parseInt(hex.substring(2, 4), 16);\n    let b = parseInt(hex.substring(4, 6), 16);\n    \n    // 变亮\n    r = Math.floor(r + (255 - r) * percent / 100);\n    g = Math.floor(g + (255 - g) * percent / 100);\n    b = Math.floor(b + (255 - b) * percent / 100);\n    \n    // 确保值在0-255范围内\n    r = (r < 0) ? 0 : ((r > 255) ? 255 : r);\n    g = (g < 0) ? 0 : ((g > 255) ? 255 : g);\n    b = (b < 0) ? 0 : ((b > 255) ? 255 : b);\n    \n    // 转换回十六进制\n    return '#' + \n      (r.toString(16).padStart(2, '0')) + \n      (g.toString(16).padStart(2, '0')) + \n      (b.toString(16).padStart(2, '0'));\n  }\n};\n\nexport default promotionService; "], "names": ["uni"], "mappings": ";;AAKK,MAAC,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMvB,mBAAmB,MAAM,MAAM;AAC7B,QAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,IAAI;AAC9BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACd,CAAO;AACD;AAAA,IACD;AAGDA,kBAAAA,MAAI,WAAW;AAAA,MACb,KAAK,oDAAoD,IAAI,OAAO,KAAK,EAAE;AAAA,IACjF,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,kBAAkB,MAAM,IAAI,QAAQ;AAClC,UAAM,UAAU;AAChB,QAAI,OAAO;AAEX,YAAQ,MAAI;AAAA,MACV,KAAK;AACH,eAAO,4BAA4B,EAAE;AACrC;AAAA,MACF,KAAK;AACH,eAAO,4BAA4B,EAAE;AACrC;AAAA,MACF,KAAK;AACH,eAAO,0BAA0B,EAAE;AACnC;AAAA,MACF,KAAK;AACH,eAAO,4BAA4B,EAAE;AACrC;AAAA,MACF,KAAK;AACH,eAAO,kCAAkC,EAAE;AAC3C;AAAA,MACF,KAAK;AACH,eAAO,4BAA4B,EAAE;AACrC;AAAA,MACF,KAAK;AACH,eAAO,6BAA6B,EAAE;AACtC;AAAA,MACF,KAAK;AACH,eAAO,8BAA8B,EAAE;AACvC;AAAA,MACF;AACE,eAAO,yBAAyB,EAAE,SAAS,IAAI;AAAA,IAClD;AAGD,QAAI,QAAQ;AACV,cAAQ,aAAa,MAAM;AAAA,IAC5B;AAED,WAAO,GAAG,OAAO,GAAG,IAAI;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,MAAM,eAAe,MAAM,IAAI,QAAQ;AACrC,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACf,CAAO;AAGD,YAAM,QAAQ,SAAS,MAAM,EAAE,MAAM,MAAM,KAAK,MAAM,EAAE;AACxD,YAAM,OAAO,KAAK,aAAa,IAAI;AAGnCA,oBAAAA,MAAI,QAAQ;AAAA,QACV,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,MAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA,OAAO;AAAA,QACR;AAAA,QACD,SAAS,CAAC,QAAQ;AAChBA,wBAAG,MAAC,YAAW;AACf,cAAI,IAAI,QAAQ,IAAI,KAAK,SAAS,KAAK,IAAI,KAAK,MAAM;AACpD,oBAAQ,IAAI,KAAK,KAAK,YAAY;AAAA,UAC9C,OAAiB;AACLA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACpB,CAAa;AACD,mBAAO,IAAI,MAAM,UAAU,CAAC;AAAA,UAC7B;AAAA,QACF;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAG,MAAC,YAAW;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAClB,CAAW;AACD,iBAAO,GAAG;AAAA,QACX;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUD,MAAM,eAAe,MAAM,MAAM,QAAQ,UAAU,CAAA,GAAI;AACrD,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI;AACFA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,QACjB,CAAS;AAGD,cAAM,MAAMA,cAAAA,MAAI,oBAAoB,cAAc;AAGlD,cAAM,QAAQ,QAAQ,SAAS,EAAE,SAAS,WAAW,WAAW;AAGhE,YAAI,aAAa,MAAM,OAAO;AAC9B,YAAI,SAAS,GAAG,GAAG,KAAK,GAAG;AAG3B,cAAM,MAAM,IAAI,qBAAqB,GAAG,GAAG,KAAK,GAAG;AACnD,YAAI,MAAM,YAAY,aAAa,MAAM,YAAY,WAAW;AAE9D,cAAI,aAAa,GAAG,SAAS;AAC7B,cAAI,aAAa,GAAG,SAAS;AAAA,QACvC,OAAe;AAEL,cAAI,aAAa,GAAG,MAAM,OAAO;AACjC,cAAI,aAAa,GAAG,KAAK,aAAa,MAAM,SAAS,EAAE,CAAC;AAAA,QACzD;AACD,YAAI,aAAa,GAAG;AACpB,YAAI,SAAS,GAAG,GAAG,KAAK,GAAG;AAG3B,YAAI,aAAa,SAAS;AAC1B,YAAI,YAAY,EAAE;AAClB,YAAI,SAAS,SAAS,IAAI,EAAE;AAG5B,YAAI,aAAa,MAAM,SAAS;AAChC,YAAI,YAAY,EAAE;AAClB,YAAI,SAAS,KAAK,cAAc,KAAK,SAAS,QAAQ,EAAE,GAAG,IAAI,GAAG;AAGlE,YAAI,aAAa,SAAS;AAC1B,YAAI,SAAS,IAAI,KAAK,KAAK,GAAG;AAG9B,YAAI,aAAa,SAAS;AAC1B,YAAI,SAAS,KAAK,KAAK,IAAI,EAAE;AAC7B,YAAI,aAAa,SAAS;AAC1B,YAAI,SAAS,KAAK,KAAK,IAAI,EAAE;AAG7B,YAAI,aAAa,MAAM,cAAc,YAAY,YAAY,SAAS;AACtE,YAAI,YAAY,EAAE;AAElB,YAAI,YAAY;AAGhB,gBAAQ,MAAI;AAAA,UACV,KAAK;AACH,gBAAI,SAAS,OAAO,KAAK,aAAa,IAAI,IAAI,IAAI,SAAS;AAC3D,yBAAa;AACb,gBAAI,SAAS,QAAQ,KAAK,eAAe,IAAI,IAAI,IAAI,SAAS;AAC9D,yBAAa;AACb,gBAAI,SAAS,OAAO,KAAK,iBAAiB,MAAM,IAAI,IAAI,SAAS;AACjE;AAAA,UAEF,KAAK;AACH,gBAAI,SAAS,QAAQ,KAAK,SAAS,MAAM,IAAI,IAAI,SAAS;AAC1D,gBAAI,KAAK,eAAe;AACtB,kBAAI,aAAa,SAAS;AAC1B,kBAAI,SAAS,QAAQ,KAAK,aAAa,IAAI,KAAK,SAAS;AACzD,kBAAI,aAAa,MAAM,cAAc,YAAY,YAAY,SAAS;AAAA,YACvE;AACD,yBAAa;AACb,gBAAI,KAAK,aAAa;AACpB,mBAAK,iBAAiB,KAAK,KAAK,aAAa,IAAI,WAAW,KAAK,IAAI,CAAC;AACtE,2BAAa;AAAA,YACd;AACD;AAAA,UAEF,KAAK;AACH,gBAAI,SAAS,OAAO,KAAK,SAAS,GAAG,GAAG,KAAK,aAAa,EAAE,IAAI,IAAI,SAAS;AAC7E,yBAAa;AACb,gBAAI,SAAS,GAAG,KAAK,YAAY,IAAI,MAAM,KAAK,QAAQ,GAAG,KAAK,IAAI,SAAS;AAC7E,yBAAa;AACb,gBAAI,SAAS,OAAO,KAAK,YAAY,IAAI,IAAI,IAAI,SAAS;AAC1D;AAAA,UAEF,KAAK;AACH,gBAAI,SAAS,OAAO,KAAK,YAAY,MAAM,IAAI,IAAI,SAAS;AAC5D,yBAAa;AACb,gBAAI,SAAS,OAAO,KAAK,WAAW,MAAM,IAAI,IAAI,SAAS;AAC3D;AAAA,UAEF;AACE,gBAAI,KAAK,aAAa;AACpB,mBAAK,iBAAiB,KAAK,KAAK,aAAa,IAAI,WAAW,KAAK,IAAI,CAAC;AACtE,2BAAa;AAAA,YAC3B,OAAmB;AACL,kBAAI,SAAS,QAAQ,IAAI,SAAS;AAClC,2BAAa;AAAA,YACd;AAAA,QACJ;AAGD,YAAI,QAAQ,YAAY;AACtB,uBAAa;AACb,cAAI,aAAa,MAAM,SAAS;AAChC,cAAI,YAAY,EAAE;AAClB,eAAK,iBAAiB,KAAK,QAAQ,YAAY,IAAI,WAAW,KAAK,IAAI,CAAC;AACxE,uBAAa;AAAA,QACd;AAGD,oBAAY,KAAK,IAAI,YAAY,IAAI,GAAG;AAGxC,YAAI,aAAa,MAAM,YAAY,YAAY,YAAY,KAAK,cAAc,MAAM,SAAS,EAAE,CAAC;AAChG,YAAI,SAAS,IAAI,WAAW,KAAK,GAAG;AAGpC,YAAI,aAAa,SAAS;AAC1B,YAAI,SAAS,KAAK,YAAY,IAAI,KAAK,GAAG;AAG1C,YAAI,aAAa,SAAS;AAC1B,YAAI,SAAS,KAAK,YAAY,IAAI,IAAI,EAAE;AAGxC,YAAI,aAAa,MAAM,SAAS;AAChC,YAAI,YAAY,EAAE;AAClB,YAAI,SAAS,UAAU,IAAI,YAAY,EAAE;AAEzC,YAAI,aAAa,MAAM,cAAc,YAAY,YAAY,SAAS;AACtE,YAAI,YAAY,EAAE;AAClB,YAAI,SAAS,YAAY,IAAI,YAAY,EAAE;AAG3C,YAAI,QAAQ,UAAU,QAAQ;AAC5B,cAAI,aAAa,MAAM,cAAc,YAAY,YAAY,SAAS;AACtE,cAAI,YAAY,EAAE;AAClB,cAAI,SAAS,UAAU,MAAM,IAAI,IAAI,YAAY,GAAG;AAAA,QACrD;AAGD,YAAI,aAAa,SAAS;AAC1B,YAAI,YAAY,EAAE;AAClB,YAAI,SAAS,WAAW,KAAK,GAAG;AAGhC,YAAI,KAAK,OAAO,MAAM;AACpB,qBAAW,MAAM;AACfA,0BAAAA,MAAI,qBAAqB;AAAA,cACvB,UAAU;AAAA,cACV,SAAS,CAAC,QAAQ;AAChBA,8BAAG,MAAC,YAAW;AACf,wBAAQ,IAAI,YAAY;AAGxB,qBAAK,gBAAgB,MAAM,KAAK,MAAM,KAAK,QAAQ;AAAA,cACpD;AAAA,cACD,MAAM,CAAC,QAAQ;AACbA,8BAAG,MAAC,YAAW;AACfA,8BAAc,MAAA,MAAA,SAAA,oCAAA,UAAU,GAAG;AAG3B,oBAAI,QAAQ,UAAU;AACpB,0BAAQ,QAAQ,QAAQ;AAAA,gBAC1C,OAAuB;AAEL,sBAAI;AACJ,0BAAQ,MAAI;AAAA,oBACV,KAAK;AACH,mCAAa;AACb;AAAA,oBACF,KAAK;AACH,mCAAa;AACb;AAAA,oBACF,KAAK;AACH,mCAAa;AACb;AAAA,oBACF,KAAK;AACH,mCAAa;AACb;AAAA,oBACF,KAAK;AACH,mCAAa;AACb;AAAA,oBACF;AACE,mCAAa;AAAA,kBAChB;AACD,0BAAQ,UAAU;AAAA,gBACnB;AAAA,cACF;AAAA,YACf,CAAa;AAAA,UACF,GAAE,GAAG;AAAA,QAChB,CAAS;AAAA,MACF,SAAQ,KAAK;AACZA,sBAAG,MAAC,YAAW;AACfA,sBAAc,MAAA,MAAA,SAAA,oCAAA,UAAU,GAAG;AAC3B,eAAO,GAAG;AAAA,MACX;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,kBAAkB,UAAU;AAChC,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,oBAAAA,MAAI,uBAAuB;AAAA,QACzB;AAAA,QACA,SAAS,MAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAClB,CAAW;AACD,kBAAQ,IAAI;AAAA,QACb;AAAA,QACD,MAAM,CAAC,QAAQ;AACb,cAAI,IAAI,OAAO,QAAQ,WAAW,IAAI,IAAI;AACxCA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,SAAS;AAAA,cACf,SAAS,CAAC,QAAQ;AACV,oBAAI,IAAI,SAAS;AACfA,gCAAG,MAAC,YAAW;AAAA,gBAChB;AAAA,cACF;AAAA,YACf,CAAa;AAAA,UACb,OAAiB;AACLA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACpB,CAAa;AAAA,UACF;AACD,iBAAO,GAAG;AAAA,QACX;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,MAAM,gBAAgB,MAAM,IAAI,QAAQ;AACtC,QAAI;AAEFA,oBAAAA,uDAAY,UAAU;AAAA,QACpB,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,MACzB,CAAO;AACD,aAAO;AAAA,IACR,SAAQ,KAAK;AACZA,oBAAc,MAAA,MAAA,SAAA,oCAAA,YAAY,GAAG;AAC7B,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,aAAa,MAAM;AACjB,YAAQ,MAAI;AAAA,MACV,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT;AACE,eAAO;AAAA,IACV;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,cAAc,MAAM,WAAW;AAC7B,QAAI,CAAC;AAAM,aAAO;AAClB,WAAO,KAAK,SAAS,YAAY,KAAK,UAAU,GAAG,SAAS,IAAI,QAAQ;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaD,iBAAiB,KAAK,MAAM,GAAG,GAAG,UAAU,YAAY,UAAU;AAChE,QAAI,CAAC;AAAM;AAEX,UAAM,QAAQ,KAAK,MAAM,EAAE;AAC3B,QAAI,OAAO;AACX,QAAI,YAAY;AAEhB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAM,WAAW,OAAO,MAAM,CAAC;AAC/B,YAAM,UAAU,IAAI,YAAY,QAAQ;AACxC,YAAM,YAAY,QAAQ;AAE1B,UAAI,YAAY,YAAY,IAAI,GAAG;AACjC,YAAI,SAAS,MAAM,GAAG,CAAC;AACvB,eAAO,MAAM,CAAC;AACd,aAAK;AACL;AAEA,YAAI,aAAa,WAAW,KAAK,IAAI,MAAM,SAAS,GAAG;AAErD,kBAAQ;AACR;AAAA,QACD;AAAA,MACT,OAAa;AACL,eAAO;AAAA,MACR;AAAA,IACF;AAED,QAAI,SAAS,MAAM,GAAG,CAAC;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,aAAa,KAAK,SAAS;AAEzB,UAAM,IAAI,QAAQ,KAAK,EAAE;AAGzB,QAAI,IAAI,SAAS,IAAI,UAAU,GAAG,CAAC,GAAG,EAAE;AACxC,QAAI,IAAI,SAAS,IAAI,UAAU,GAAG,CAAC,GAAG,EAAE;AACxC,QAAI,IAAI,SAAS,IAAI,UAAU,GAAG,CAAC,GAAG,EAAE;AAGxC,QAAI,KAAK,MAAM,KAAK,MAAM,WAAW,GAAG;AACxC,QAAI,KAAK,MAAM,KAAK,MAAM,WAAW,GAAG;AACxC,QAAI,KAAK,MAAM,KAAK,MAAM,WAAW,GAAG;AAGxC,QAAK,IAAI,IAAK,IAAM,IAAI,MAAO,MAAM;AACrC,QAAK,IAAI,IAAK,IAAM,IAAI,MAAO,MAAM;AACrC,QAAK,IAAI,IAAK,IAAM,IAAI,MAAO,MAAM;AAGrC,WAAO,MACJ,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,IAC9B,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,IAC9B,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,cAAc,KAAK,SAAS;AAE1B,UAAM,IAAI,QAAQ,KAAK,EAAE;AAGzB,QAAI,IAAI,SAAS,IAAI,UAAU,GAAG,CAAC,GAAG,EAAE;AACxC,QAAI,IAAI,SAAS,IAAI,UAAU,GAAG,CAAC,GAAG,EAAE;AACxC,QAAI,IAAI,SAAS,IAAI,UAAU,GAAG,CAAC,GAAG,EAAE;AAGxC,QAAI,KAAK,MAAM,KAAK,MAAM,KAAK,UAAU,GAAG;AAC5C,QAAI,KAAK,MAAM,KAAK,MAAM,KAAK,UAAU,GAAG;AAC5C,QAAI,KAAK,MAAM,KAAK,MAAM,KAAK,UAAU,GAAG;AAG5C,QAAK,IAAI,IAAK,IAAM,IAAI,MAAO,MAAM;AACrC,QAAK,IAAI,IAAK,IAAM,IAAI,MAAO,MAAM;AACrC,QAAK,IAAI,IAAK,IAAM,IAAI,MAAO,MAAM;AAGrC,WAAO,MACJ,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,IAC9B,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,IAC9B,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG;AAAA,EAClC;AACH;;"}