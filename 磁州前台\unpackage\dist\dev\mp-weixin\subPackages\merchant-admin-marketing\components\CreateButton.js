"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  name: "CreateButton",
  props: {
    text: {
      type: String,
      default: "创建"
    },
    url: {
      type: String,
      default: ""
    },
    theme: {
      type: String,
      default: "default"
      // 可以是 'coupon', 'discount', 'group', 'flash' 或默认
    }
  },
  computed: {
    themeClass() {
      return `theme-${this.theme}`;
    }
  },
  methods: {
    handleTap() {
      if (this.url) {
        common_vendor.index.navigateTo({
          url: this.url
        });
      } else {
        this.$emit("click");
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($props.text),
    b: common_vendor.n($options.themeClass),
    c: common_vendor.o((...args) => $options.handleTap && $options.handleTap(...args))
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-973321a6"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/components/CreateButton.js.map
