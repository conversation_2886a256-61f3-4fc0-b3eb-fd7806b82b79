{"version": 3, "file": "FabButtons.js", "sources": ["components/FabButtons.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7AvY29tcG9uZW50cy9GYWJCdXR0b25zLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"fab-btns\">\n    <button class=\"fab-btn share-btn\" @click=\"onShare\" open-type=\"share\">\n      <image class=\"fab-icon\" src=\"/static/images/tabbar/share.png\" />\n    </button>\n    <button v-if=\"isHomePage || showBackToTop\" class=\"fab-btn\" @click=\"scrollToTop()\">\n      <image class=\"fab-icon\" src=\"/static/images/tabbar/arrow-up.png\" />\n    </button>\n    <button class=\"fab-btn\" @click=\"onKefu\">\n      <image class=\"fab-icon\" src=\"/static/images/tabbar/service.png\" />\n    </button>\n    <view v-if=\"showQrcode\" class=\"qrcode-popup-mask\" @click.self=\"showQrcode = false\">\n      <view class=\"qrcode-popup\">\n        <image class=\"qrcode-img\" src=\"/static/images/tabbar/wxacode.jpg\" />\n        <view class=\"qrcode-title\">长按识别二维码，联系客服</view>\n        <view class=\"qrcode-close\" @click=\"showQrcode = false\">×</view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed } from 'vue';\n\n// --- Props ---\nconst props = defineProps({\n    pageName: {\n      type: String,\n      default: ''\n    },\n    pageInfo: {\n      type: Object,\n      default: () => ({\n        title: '磁州生活网',\n        path: '/pages/index/index',\n        imageUrl: ''\n      })\n    }\n});\n\n// --- 响应式状态 ---\nconst showQrcode = ref(false);\nconst showBackToTop = ref(false);\nconst scrollTop = ref(0);\n\n// --- Computed ---\nconst isHomePage = computed(() => props.pageName === 'index');\n\n// --- 方法 ---\nconst setScrollTop = (newScrollTop) => {\n  scrollTop.value = newScrollTop;\n  showBackToTop.value = newScrollTop > 200;\n};\n\nconst scrollToTop = () => {\n      uni.pageScrollTo({\n        scrollTop: 0,\n        duration: 300\n      });\n};\n      \nconst onShare = () => {\n      // #ifndef MP-WEIXIN\n  uni.showToast({ title: '请点击右上角\"...\"进行转发', icon: 'none' });\n      // #endif\n};\n\nconst onKefu = () => {\n  showQrcode.value = true;\n};\n\n// --- Expose ---\n// 暴露方法给父组件调用\ndefineExpose({\n  setScrollTop\n});\n\n// --- 页面生命周期 (小程序分享) ---\n// 注意：在Vue 3 <script setup>中，页面生命周期 onShareAppMessage 不能直接使用\n// 需要在父页面中处理分享逻辑，或者使用其他方式。\n// 为了保持功能一致，暂时保留此注释，父页面需要实现分享。\n/*\nonShareAppMessage(() => {\n    return {\n    title: props.pageInfo.title,\n    path: props.pageInfo.path,\n    imageUrl: props.pageInfo.imageUrl || '/static/images/share-default.png'\n  };\n});\n*/\n</script>\n\n<style scoped>\n.fab-btns {\n  position: fixed;\n  right: 32rpx;\n  bottom: 180rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n  z-index: 9999;\n}\n.fab-btn {\n  width: 66rpx;\n  height: 66rpx;\n  border-radius: 50%;\n  background: rgba(240, 240, 240, 0.9);\n  box-shadow: 0 3rpx 20rpx rgba(0,0,0,0.12);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 24rpx;\n  border: none;\n  padding: 0;\n  outline: none;\n  transition: box-shadow 0.2s, transform 0.2s;\n  overflow: hidden;\n  box-sizing: border-box;\n  -webkit-mask-image: -webkit-radial-gradient(white, black);\n}\n.fab-btn:after {\n  border: none;\n}\n.fab-btn:active {\n  box-shadow: 0 1rpx 4rpx rgba(0,0,0,0.12);\n  transform: scale(0.96);\n}\n.share-btn {\n  background: rgba(255, 255, 255, 0.95);\n  width: 66rpx;\n  height: 66rpx;\n  box-shadow: 0 3rpx 20rpx rgba(0,0,0,0.15);\n}\n.share-btn .fab-icon {\n  filter: hue-rotate(100deg) saturate(150%);\n}\n.fab-icon {\n  width: 40rpx;\n  height: 40rpx;\n  display: block;\n}\n.qrcode-popup-mask {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  height: 100vh;\n  background: rgba(0,0,0,0.32);\n  z-index: 10000;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.qrcode-popup {\n  background: #fff;\n  border-radius: 24rpx;\n  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.13);\n  padding: 38rpx 32rpx 28rpx 32rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  position: relative;\n  min-width: 420rpx;\n  animation: fadeInUp 0.3s cubic-bezier(0.23, 1, 0.32, 1);\n}\n.qrcode-img {\n  width: 260rpx;\n  height: 260rpx;\n  border-radius: 18rpx;\n  margin-bottom: 18rpx;\n  box-shadow: 0 2rpx 12rpx rgba(64,158,255,0.10);\n}\n.qrcode-title {\n  font-size: 26rpx;\n  color: #333;\n  margin-bottom: 8rpx;\n  font-weight: 500;\n}\n.qrcode-close {\n  position: absolute;\n  top: 12rpx;\n  right: 18rpx;\n  font-size: 38rpx;\n  color: #bbb;\n  font-weight: bold;\n  cursor: pointer;\n  transition: color 0.2s;\n  z-index: 2;\n}\n.qrcode-close:active {\n  color: #409eff;\n}\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20rpx);\n    filter: saturate(0.8) brightness(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n    filter: saturate(1) brightness(1);\n  }\n}\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/components/FabButtons.vue'\nwx.createComponent(Component)"], "names": ["ref", "computed", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAyBA,UAAM,QAAQ;AAgBd,UAAM,aAAaA,cAAAA,IAAI,KAAK;AAC5B,UAAM,gBAAgBA,cAAAA,IAAI,KAAK;AAC/B,UAAM,YAAYA,cAAAA,IAAI,CAAC;AAGvB,UAAM,aAAaC,cAAAA,SAAS,MAAM,MAAM,aAAa,OAAO;AAG5D,UAAM,eAAe,CAAC,iBAAiB;AACrC,gBAAU,QAAQ;AAClB,oBAAc,QAAQ,eAAe;AAAA,IACvC;AAEA,UAAM,cAAc,MAAM;AACpBC,oBAAAA,MAAI,aAAa;AAAA,QACf,WAAW;AAAA,QACX,UAAU;AAAA,MAClB,CAAO;AAAA,IACP;AAEA,UAAM,UAAU,MAAM;AAAA,IAItB;AAEA,UAAM,SAAS,MAAM;AACnB,iBAAW,QAAQ;AAAA,IACrB;AAIA,aAAa;AAAA,MACX;AAAA,IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;AC1ED,GAAG,gBAAgB,SAAS;"}