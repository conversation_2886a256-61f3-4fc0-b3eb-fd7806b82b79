"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const _sfc_main = {
  __name: "create-rating",
  setup(__props) {
    const driverInfo = common_vendor.ref({
      id: "",
      name: "",
      avatar: "/static/images/avatar/user1.png",
      carModel: "",
      carColor: "",
      carNumber: "",
      phoneNumber: ""
    });
    const tripInfo = common_vendor.ref({
      id: "",
      startLocation: "",
      endLocation: "",
      departureTime: "",
      departureDate: "",
      status: ""
    });
    const rating = common_vendor.ref(0);
    const tags = common_vendor.ref(["准时出发", "路线合理", "驾驶平稳", "态度友好", "车内整洁", "价格合理"]);
    const selectedTags = common_vendor.ref([]);
    const comment = common_vendor.ref("");
    const isAnonymous = common_vendor.ref(false);
    const ratingText = common_vendor.computed(() => {
      const texts = ["", "很差", "较差", "一般", "不错", "很好"];
      return rating.value > 0 ? texts[rating.value] : "请选择评分";
    });
    common_vendor.onMounted(() => {
      common_vendor.index.__f__("log", "at carpool-package/pages/carpool/my/create-rating.vue:155", "页面已挂载");
    });
    common_vendor.onLoad((options) => {
      common_vendor.index.__f__("log", "at carpool-package/pages/carpool/my/create-rating.vue:160", "获取到页面参数:", options);
      if (options && Object.keys(options).length > 0) {
        if (options.driverId && options.phoneNumber) {
          driverInfo.value.id = options.driverId;
          const contactHistory = common_vendor.index.getStorageSync("contactHistory") || [];
          const contactRecord = contactHistory.find(
            (item) => item.driverId === options.driverId && item.phoneNumber === options.phoneNumber
          );
          if (contactRecord) {
            tripInfo.value = {
              id: options.carpoolId || contactRecord.carpoolId,
              startLocation: decodeURIComponent(options.startLocation || contactRecord.startLocation),
              endLocation: decodeURIComponent(options.endLocation || contactRecord.endLocation),
              departureTime: options.departureTime || contactRecord.departureTime,
              departureDate: options.departureDate || contactRecord.departureDate,
              status: "completed"
            };
            const driversInfo = common_vendor.index.getStorageSync("driversInfo") || {};
            if (driversInfo[options.driverId]) {
              driverInfo.value = { ...driverInfo.value, ...driversInfo[options.driverId] };
            } else {
              driverInfo.value.name = "司机" + options.phoneNumber.substr(-4);
              driverInfo.value.phoneNumber = options.phoneNumber;
              loadDriverInfoByPhone(options.phoneNumber);
            }
          } else {
            driverInfo.value.id = options.driverId;
            driverInfo.value.name = "司机" + options.phoneNumber.substr(-4);
            driverInfo.value.phoneNumber = options.phoneNumber;
            tripInfo.value = {
              id: options.carpoolId || "",
              startLocation: decodeURIComponent(options.startLocation || ""),
              endLocation: decodeURIComponent(options.endLocation || ""),
              departureTime: options.departureTime || "",
              departureDate: options.departureDate || "",
              status: "completed"
            };
            loadDriverInfoByPhone(options.phoneNumber);
          }
        } else {
          loadMockData();
        }
      } else {
        loadMockData();
        common_vendor.index.__f__("error", "at carpool-package/pages/carpool/my/create-rating.vue:225", "未获取到页面参数，使用测试数据");
      }
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const loadMockData = () => {
      driverInfo.value = {
        id: "D12345",
        name: "张师傅",
        avatar: "/static/images/avatar/user1.png",
        carModel: "本田雅阁",
        carColor: "白色",
        carNumber: "冀D·12345",
        phoneNumber: "1234567890"
      };
      tripInfo.value = {
        id: "T67890",
        startLocation: "磁县火车站",
        endLocation: "邯郸东站",
        departureTime: "14:30",
        departureDate: "2023-06-15",
        status: "completed"
      };
    };
    const selectRating = (score) => {
      rating.value = score;
    };
    const toggleTag = (tag) => {
      if (selectedTags.value.includes(tag)) {
        selectedTags.value = selectedTags.value.filter((item) => item !== tag);
      } else {
        if (selectedTags.value.length < 3) {
          selectedTags.value.push(tag);
        } else {
          common_vendor.index.showToast({
            title: "最多选择3个标签",
            icon: "none"
          });
        }
      }
    };
    const toggleAnonymous = (e) => {
      isAnonymous.value = e.detail.value;
    };
    const submitRating = () => {
      if (rating.value === 0) {
        common_vendor.index.showToast({
          title: "请选择评分",
          icon: "none"
        });
        return;
      }
      const ratingData = {
        driverId: driverInfo.value.id,
        phoneNumber: driverInfo.value.phoneNumber,
        carpoolId: tripInfo.value.id,
        rating: rating.value,
        tags: selectedTags.value,
        comment: comment.value,
        isAnonymous: isAnonymous.value,
        createTime: (/* @__PURE__ */ new Date()).toISOString(),
        startLocation: tripInfo.value.startLocation,
        endLocation: tripInfo.value.endLocation,
        departureTime: tripInfo.value.departureTime,
        departureDate: tripInfo.value.departureDate
      };
      common_vendor.index.__f__("log", "at carpool-package/pages/carpool/my/create-rating.vue:322", "提交评价数据:", ratingData);
      const ratingsHistory = common_vendor.index.getStorageSync("ratingsHistory") || [];
      const existingIndex = ratingsHistory.findIndex(
        (item) => item.driverId === ratingData.driverId && item.carpoolId === ratingData.carpoolId
      );
      if (existingIndex !== -1) {
        ratingsHistory[existingIndex] = ratingData;
      } else {
        ratingsHistory.push(ratingData);
      }
      common_vendor.index.setStorageSync("ratingsHistory", ratingsHistory);
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "评价成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      }, 1e3);
    };
    const loadDriverInfoByPhone = (phoneNumber) => {
      common_vendor.index.__f__("log", "at carpool-package/pages/carpool/my/create-rating.vue:366", "通过电话号码加载司机信息:", phoneNumber);
      setTimeout(() => {
        const driverInfoData = {
          name: "司机" + phoneNumber.substr(-4),
          avatar: "/static/images/avatar/user1.png",
          carModel: "未知车型",
          carColor: "未知颜色",
          carNumber: "未知车牌",
          phoneNumber
        };
        driverInfo.value = { ...driverInfo.value, ...driverInfoData };
        const driversInfo = common_vendor.index.getStorageSync("driversInfo") || {};
        driversInfo[driverInfo.value.id] = driverInfoData;
        common_vendor.index.setStorageSync("driversInfo", driversInfo);
      }, 500);
    };
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: driverInfo.value.avatar,
        d: common_vendor.t(driverInfo.value.name),
        e: common_vendor.t(driverInfo.value.carModel),
        f: common_vendor.t(driverInfo.value.carColor),
        g: common_vendor.t(driverInfo.value.carNumber),
        h: common_vendor.t(tripInfo.value.startLocation),
        i: common_vendor.t(tripInfo.value.endLocation),
        j: common_vendor.t(tripInfo.value.departureTime),
        k: common_vendor.t(tripInfo.value.departureDate),
        l: common_vendor.f(5, (item, index, i0) => {
          return {
            a: index < rating.value ? "/static/images/icons/star-filled.png" : "/static/images/icons/star-empty.png",
            b: index,
            c: common_vendor.o(($event) => selectRating(index + 1), index)
          };
        }),
        m: common_vendor.t(ratingText.value),
        n: common_vendor.f(tags.value, (tag, index, i0) => {
          return {
            a: common_vendor.t(tag),
            b: index,
            c: selectedTags.value.includes(tag) ? 1 : "",
            d: common_vendor.o(($event) => toggleTag(tag), index)
          };
        }),
        o: comment.value,
        p: common_vendor.o(($event) => comment.value = $event.detail.value),
        q: common_vendor.t(comment.value.length),
        r: isAnonymous.value,
        s: common_vendor.o(toggleAnonymous),
        t: rating.value === 0,
        v: common_vendor.o(submitRating)
      };
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/carpool-package/pages/carpool/my/create-rating.js.map
