<template>
  <view class="progress-container">
    <view class="progress-steps">
      <view 
        v-for="(step, index) in steps" 
        :key="index"
        class="step" 
        :class="{active: currentStep >= index + 1, completed: currentStep > index + 1}"
      >
        <view 
          v-if="index > 0" 
          class="step-line" 
          :class="{active: currentStep > index}"
        ></view>
        
        <view class="step-content">
          <view class="step-dot">
            <text v-if="currentStep <= index + 1">{{ index + 1 }}</text>
            <view class="check-icon" v-else></view>
          </view>
          <text class="step-label">{{ step }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ProgressSteps',
  props: {
    steps: {
      type: Array,
      required: true
    },
    currentStep: {
      type: Number,
      default: 1
    }
  },
  computed: {
    progressPercentage() {
      return ((this.currentStep - 1) / (this.steps.length - 1)) * 100;
    }
  }
}
</script>

<style lang="scss">
.progress-container {
  padding: 15px;
  margin-bottom: 15px;
  background-color: #fff;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  position: relative;
}

.step {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
  
  &:first-child {
    .step-content {
      align-items: flex-start;
    }
  }
  
  &:last-child {
    .step-content {
      align-items: flex-end;
    }
  }
}

.step-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
}

.step-dot {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #E5E5E5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
  transition: all 0.3s;
  
  .active & {
    background-color: #9040FF;
    color: white;
  }
  
  .completed & {
    background-color: #9040FF;
  }
}

.check-icon {
  width: 10px;
  height: 6px;
  border-left: 2px solid white;
  border-bottom: 2px solid white;
  transform: rotate(-45deg) translate(1px, -1px);
}

.step-label {
  font-size: 12px;
  color: #999;
  transition: all 0.3s;
  
  .active &, .completed & {
    color: #333;
    font-weight: 500;
  }
}

.step-line {
  position: absolute;
  left: 0;
  right: 0;
  top: 12px;
  height: 2px;
  background-color: #E5E5E5;
  z-index: 1;
  
  &.active {
    background-color: #9040FF;
  }
}
</style> 