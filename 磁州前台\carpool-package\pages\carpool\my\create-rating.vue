<template>
  <view class="rating-container">
    <!-- 自定义导航栏 - 完全重构 -->
    <view style="position: fixed; top: 0; left: 0; right: 0; height: calc(90px - 4px); background: linear-gradient(135deg, #0A84FF, #0040DD); z-index: 100; padding-top: var(--status-bar-height, 40px);">
      <!-- 返回键 - 绝对定位 (向下10px 向右8px) -->
      <view style="position: absolute; left: 8px; top: calc(var(--status-bar-height, 40px) + 10px); width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;" @click="goBack">
        <image src="/static/images/tabbar/最新返回键.png" style="width: 24px; height: 24px; filter: brightness(0) invert(1);"></image>
      </view>
      
      <!-- 标题 - 绝对定位 (向下移动10px) -->
      <view style="position: absolute; left: 0; right: 0; top: calc(var(--status-bar-height, 40px) + 15px); text-align: center;">
        <text style="font-size: 18px; color: white; font-weight: bold;">评价司机</text>
      </view>
    </view>
    
    <!-- 占位元素确保内容不被导航栏遮挡 -->
    <view style="height: calc(86px + var(--status-bar-height, 40px));"></view>
    
    <!-- 司机信息 -->
    <view class="driver-info">
      <view class="driver-avatar-container">
        <image class="driver-avatar" :src="driverInfo.avatar" mode="aspectFill"></image>
      </view>
      <view class="driver-name">{{driverInfo.name}}</view>
      <view class="driver-car">{{driverInfo.carModel}} · {{driverInfo.carColor}} · {{driverInfo.carNumber}}</view>
    </view>
    
    <!-- 行程信息 -->
    <view class="trip-info">
      <view class="trip-route">
        <view class="route-point">
          <view class="point start"></view>
          <text class="point-text">{{tripInfo.startLocation}}</text>
        </view>
        <view class="route-line"></view>
        <view class="route-point">
          <view class="point end"></view>
          <text class="point-text">{{tripInfo.endLocation}}</text>
        </view>
      </view>
      <view class="trip-time">
        <text class="time-label">出发时间：</text>
        <text class="time-value">{{tripInfo.departureTime}}</text>
      </view>
      <view class="trip-date">
        <text class="date-label">出发日期：</text>
        <text class="date-value">{{tripInfo.departureDate}}</text>
      </view>
    </view>
    
    <!-- 评分部分 -->
    <view class="rating-section">
      <view class="section-title">行程评分</view>
      <view class="star-rating">
        <view 
          v-for="(item, index) in 5" 
          :key="index" 
          class="star-item"
          @click="selectRating(index + 1)"
        >
          <image 
            :src="index < rating ? '/static/images/icons/star-filled.png' : '/static/images/icons/star-empty.png'" 
            class="star-icon"
          ></image>
        </view>
      </view>
      <view class="rating-text">{{ratingText}}</view>
    </view>
    
    <!-- 评价标签 -->
    <view class="tags-section">
      <view class="section-title">评价标签</view>
      <view class="tags-container">
        <view 
          v-for="(tag, index) in tags" 
          :key="index" 
          class="tag-item"
          :class="{ 'selected': selectedTags.includes(tag) }"
          @click="toggleTag(tag)"
        >
          {{tag}}
        </view>
      </view>
    </view>
    
    <!-- 评价内容 -->
    <view class="comment-section">
      <view class="section-title">评价内容</view>
      <textarea 
        class="comment-input" 
        v-model="comment" 
        placeholder="请输入您对本次行程的评价（选填）" 
        maxlength="200"
      ></textarea>
      <view class="word-count">{{comment.length}}/200</view>
    </view>
    
    <!-- 匿名评价 -->
    <view class="anonymous-section">
      <label class="checkbox-label">
        <checkbox :checked="isAnonymous" @change="toggleAnonymous" color="#0A84FF" style="transform:scale(0.7)" />
        <text class="anonymous-text">匿名评价</text>
      </label>
    </view>
    
    <!-- 提交按钮 -->
    <button class="submit-btn" :disabled="rating === 0" @click="submitRating">提交评价</button>
    
    <!-- 底部安全区域 -->
    <view class="safe-area-bottom"></view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';

// 司机信息
const driverInfo = ref({
  id: '',
  name: '',
  avatar: '/static/images/avatar/user1.png',
  carModel: '',
  carColor: '',
  carNumber: '',
  phoneNumber: ''
});

// 行程信息
const tripInfo = ref({
  id: '',
  startLocation: '',
  endLocation: '',
  departureTime: '',
  departureDate: '',
  status: ''
});

// 评分相关
const rating = ref(0);
const tags = ref(['准时出发', '路线合理', '驾驶平稳', '态度友好', '车内整洁', '价格合理']);
const selectedTags = ref([]);
const comment = ref('');
const isAnonymous = ref(false);

// 计算属性：评分文本
const ratingText = computed(() => {
  const texts = ['', '很差', '较差', '一般', '不错', '很好'];
  return rating.value > 0 ? texts[rating.value] : '请选择评分';
});

// 页面加载
onMounted(() => {
  // 只做一些基本初始化
  console.log('页面已挂载');
});

// 使用onLoad处理页面参数（uni-app特有生命周期）
onLoad((options) => {
  console.log('获取到页面参数:', options);
  
  // 从路由参数获取司机和拼车信息
  if (options && Object.keys(options).length > 0) {
    // 如果有driverId和carpoolId，说明是从拼车详情页跳转来的
    if (options.driverId && options.phoneNumber) {
      driverInfo.value.id = options.driverId;
      
      // 从本地存储中查找司机信息
      const contactHistory = uni.getStorageSync('contactHistory') || [];
      const contactRecord = contactHistory.find(item => 
        item.driverId === options.driverId && 
        item.phoneNumber === options.phoneNumber
      );
      
      if (contactRecord) {
        // 填充行程信息
        tripInfo.value = {
          id: options.carpoolId || contactRecord.carpoolId,
          startLocation: decodeURIComponent(options.startLocation || contactRecord.startLocation),
          endLocation: decodeURIComponent(options.endLocation || contactRecord.endLocation),
          departureTime: options.departureTime || contactRecord.departureTime,
          departureDate: options.departureDate || contactRecord.departureDate,
          status: 'completed'
        };
        
        // 尝试从本地缓存加载司机信息
        const driversInfo = uni.getStorageSync('driversInfo') || {};
        if (driversInfo[options.driverId]) {
          driverInfo.value = { ...driverInfo.value, ...driversInfo[options.driverId] };
        } else {
          // 如果没有司机信息，设置默认值并尝试从电话号码获取
          driverInfo.value.name = '司机' + options.phoneNumber.substr(-4);
          driverInfo.value.phoneNumber = options.phoneNumber;
          
          // 实际应用中应该调用API获取司机信息
          loadDriverInfoByPhone(options.phoneNumber);
        }
      } else {
        // 没有找到联系记录，但有参数，直接使用参数创建信息
        driverInfo.value.id = options.driverId;
        driverInfo.value.name = '司机' + options.phoneNumber.substr(-4);
        driverInfo.value.phoneNumber = options.phoneNumber;
        
        tripInfo.value = {
          id: options.carpoolId || '',
          startLocation: decodeURIComponent(options.startLocation || ''),
          endLocation: decodeURIComponent(options.endLocation || ''),
          departureTime: options.departureTime || '',
          departureDate: options.departureDate || '',
          status: 'completed'
        };
        
        // 尝试加载司机信息
        loadDriverInfoByPhone(options.phoneNumber);
      }
    } else {
      // 测试数据
      loadMockData();
    }
  } else {
    // 测试数据
    loadMockData();
    
    // 输出日志
    console.error('未获取到页面参数，使用测试数据');
  }
});

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 加载行程信息
const loadTripInfo = (tripId) => {
  // 实际应用中应该从API获取数据
  // 这里使用模拟数据
  console.log('加载行程信息，ID:', tripId);
};

// 加载司机信息
const loadDriverInfo = (driverId) => {
  // 实际应用中应该从API获取数据
  // 这里使用模拟数据
  console.log('加载司机信息，ID:', driverId);
};

// 加载模拟数据（仅用于开发测试）
const loadMockData = () => {
  driverInfo.value = {
    id: 'D12345',
    name: '张师傅',
    avatar: '/static/images/avatar/user1.png',
    carModel: '本田雅阁',
    carColor: '白色',
    carNumber: '冀D·12345',
    phoneNumber: '1234567890'
  };
  
  tripInfo.value = {
    id: 'T67890',
    startLocation: '磁县火车站',
    endLocation: '邯郸东站',
    departureTime: '14:30',
    departureDate: '2023-06-15',
    status: 'completed'
  };
};

// 选择评分
const selectRating = (score) => {
  rating.value = score;
};

// 切换标签选择
const toggleTag = (tag) => {
  if (selectedTags.value.includes(tag)) {
    selectedTags.value = selectedTags.value.filter(item => item !== tag);
  } else {
    if (selectedTags.value.length < 3) {
      selectedTags.value.push(tag);
    } else {
      uni.showToast({
        title: '最多选择3个标签',
        icon: 'none'
      });
    }
  }
};

// 切换匿名评价
const toggleAnonymous = (e) => {
  isAnonymous.value = e.detail.value;
};

// 提交评价
const submitRating = () => {
  if (rating.value === 0) {
    uni.showToast({
      title: '请选择评分',
      icon: 'none'
    });
    return;
  }
  
  // 构建评价数据
  const ratingData = {
    driverId: driverInfo.value.id,
    phoneNumber: driverInfo.value.phoneNumber,
    carpoolId: tripInfo.value.id,
    rating: rating.value,
    tags: selectedTags.value,
    comment: comment.value,
    isAnonymous: isAnonymous.value,
    createTime: new Date().toISOString(),
    startLocation: tripInfo.value.startLocation,
    endLocation: tripInfo.value.endLocation,
    departureTime: tripInfo.value.departureTime,
    departureDate: tripInfo.value.departureDate
  };
  
  console.log('提交评价数据:', ratingData);
  
  // 保存到本地存储
  const ratingsHistory = uni.getStorageSync('ratingsHistory') || [];
  
  // 检查是否已有相同评价，如果有则更新
  const existingIndex = ratingsHistory.findIndex(item => 
    item.driverId === ratingData.driverId && 
    item.carpoolId === ratingData.carpoolId
  );
  
  if (existingIndex !== -1) {
    ratingsHistory[existingIndex] = ratingData;
  } else {
    ratingsHistory.push(ratingData);
  }
  
  uni.setStorageSync('ratingsHistory', ratingsHistory);
  
  // 实际应用中应该调用API提交评价
  uni.showLoading({
    title: '提交中...'
  });
  
  // 模拟API调用
  setTimeout(() => {
    uni.hideLoading();
    
    uni.showToast({
      title: '评价成功',
      icon: 'success'
    });
    
    // 延迟返回
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }, 1000);
};

// 添加通过电话号码加载司机信息的方法
const loadDriverInfoByPhone = (phoneNumber) => {
  // 实际应用中应该调用API获取司机信息
  // 这里使用模拟数据
  console.log('通过电话号码加载司机信息:', phoneNumber);
  
  // 模拟API调用
  setTimeout(() => {
    // 模拟数据
    const driverInfoData = {
      name: '司机' + phoneNumber.substr(-4),
      avatar: '/static/images/avatar/user1.png',
      carModel: '未知车型',
      carColor: '未知颜色',
      carNumber: '未知车牌',
      phoneNumber: phoneNumber
    };
    
    // 更新司机信息
    driverInfo.value = { ...driverInfo.value, ...driverInfoData };
    
    // 保存到本地缓存
    const driversInfo = uni.getStorageSync('driversInfo') || {};
    driversInfo[driverInfo.value.id] = driverInfoData;
    uni.setStorageSync('driversInfo', driversInfo);
  }, 500);
};
</script>

<style lang="scss">
.rating-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(90rpx + var(--status-bar-height, 40px));
  padding-bottom: 40rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #0A84FF, #0040DD);
  z-index: 100;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  padding-top: var(--status-bar-height, 40px);
  /* 增加高度确保有足够空间 */
  min-height: 120rpx;
}

.navbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 90rpx;
  padding: 0 24rpx;
  /* 确保内容有足够的灵活性 */
  min-height: 90rpx;
  flex-wrap: nowrap;
}

.navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  font-size: 38rpx;
  font-weight: 600;
  color: #ffffff;
  letter-spacing: 1rpx;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.navbar-right {
  width: 60rpx;
  height: 60rpx;
}

/* 司机信息 */
.driver-info {
  margin: 20rpx 32rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.driver-avatar-container {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
  border: 2rpx solid #F2F2F7;
}

.driver-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.driver-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.driver-car {
  font-size: 24rpx;
  color: #666666;
}

/* 行程信息 */
.trip-info {
  margin: 0 32rpx 32rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.trip-route {
  padding: 16rpx 0;
}

.route-point {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.point {
  width: 20rpx;
  height: 20rpx;
  border-radius: 10rpx;
  margin-right: 16rpx;
}

.point.start {
  background-color: #34C759;
}

.point.end {
  background-color: #FF3B30;
}

.point-text {
  font-size: 28rpx;
  color: #333333;
}

.route-line {
  width: 2rpx;
  height: 40rpx;
  background-color: #DDDDDD;
  margin-left: 9rpx;
  margin-bottom: 16rpx;
}

.trip-time, .trip-date {
  display: flex;
  margin-top: 8rpx;
}

.time-label, .date-label {
  font-size: 26rpx;
  color: #666666;
  width: 150rpx;
}

.time-value, .date-value {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
}

/* 评分部分 */
.rating-section, .tags-section, .comment-section {
  margin: 0 32rpx 32rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
}

.star-rating {
  display: flex;
  justify-content: center;
  margin-bottom: 16rpx;
}

.star-item {
  padding: 0 10rpx;
}

.star-icon {
  width: 60rpx;
  height: 60rpx;
}

.rating-text {
  text-align: center;
  font-size: 26rpx;
  color: #666666;
}

/* 评价标签 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
}

.tag-item {
  padding: 12rpx 24rpx;
  background-color: #F5F5F5;
  border-radius: 30rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  font-size: 26rpx;
  color: #666666;
}

.tag-item.selected {
  background-color: #E1F0FF;
  color: #0A84FF;
  border: 1rpx solid #0A84FF;
}

/* 评价内容 */
.comment-input {
  width: 100%;
  height: 200rpx;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.word-count {
  text-align: right;
  font-size: 24rpx;
  color: #999999;
  margin-top: 8rpx;
}

/* 匿名评价 */
.anonymous-section {
  margin: 0 32rpx 32rpx;
  padding: 0 24rpx;
}

.checkbox-label {
  display: flex;
  align-items: center;
}

.anonymous-text {
  font-size: 28rpx;
  color: #666666;
}

/* 提交按钮 */
.submit-btn {
  width: calc(100% - 64rpx);
  height: 90rpx;
  background: linear-gradient(135deg, #0A84FF, #0040DD);
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 32rpx;
}

.submit-btn[disabled] {
  background: #CCCCCC;
  color: #FFFFFF;
}

.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  width: 100%;
}
</style> 