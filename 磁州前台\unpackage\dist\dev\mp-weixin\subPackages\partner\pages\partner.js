"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const utils_userProfile = require("../../../utils/userProfile.js");
const utils_navigation = require("../../../utils/navigation.js");
const _sfc_main = {
  __name: "partner",
  setup(__props) {
    const partnerInfo = common_vendor.reactive({
      nickname: "",
      avatar: "",
      level: 1,
      // 1: 普通合伙人, 2: 银牌合伙人, 3: 金牌合伙人, 4: 钻石合伙人
      totalIncome: "0.00",
      thisMonthIncome: "0.00",
      fansCount: "0"
    });
    const incomeData = common_vendor.reactive({
      today: "0.00",
      yesterday: "0.00",
      thisWeek: "0.00",
      chartData: []
      // 最近7天的收益数据
    });
    const promotionData = common_vendor.reactive({
      viewCount: "0",
      registerCount: "0",
      orderCount: "0",
      conversionRate: "0%"
    });
    const progressWidth = common_vendor.ref(25);
    const canUpgrade = common_vendor.computed(() => {
      return partnerInfo.level < 4;
    });
    const chartData = common_vendor.computed(() => {
      const data = incomeData.chartData || [];
      const maxValue = Math.max(...data, 10);
      return [
        { date: "周一", value: data[0] || 0, height: data[0] ? data[0] / maxValue * 150 : 0 },
        { date: "周二", value: data[1] || 0, height: data[1] ? data[1] / maxValue * 150 : 0 },
        { date: "周三", value: data[2] || 0, height: data[2] ? data[2] / maxValue * 150 : 0 },
        { date: "周四", value: data[3] || 0, height: data[3] ? data[3] / maxValue * 150 : 0 },
        { date: "周五", value: data[4] || 0, height: data[4] ? data[4] / maxValue * 150 : 0 },
        { date: "周六", value: data[5] || 0, height: data[5] ? data[5] / maxValue * 150 : 0 },
        { date: "周日", value: data[6] || 0, height: data[6] ? data[6] / maxValue * 150 : 0 }
      ];
    });
    common_vendor.onMounted(() => {
      getUserInfo();
      getPartnerInfo();
      getIncomeData();
      getPromotionData();
    });
    const getUserInfo = () => {
      const userInfo = utils_userProfile.getLocalUserInfo();
      if (userInfo) {
        partnerInfo.nickname = userInfo.nickname;
        partnerInfo.avatar = userInfo.avatar;
      }
    };
    const getPartnerInfo = () => {
      Object.assign(partnerInfo, {
        level: 2,
        totalIncome: "1280.50",
        thisMonthIncome: "320.75",
        fansCount: "28"
      });
      progressWidth.value = partnerInfo.level / 4 * 100;
    };
    const getIncomeData = () => {
      Object.assign(incomeData, {
        today: "35.60",
        yesterday: "42.80",
        thisWeek: "198.30",
        chartData: [12.5, 18.3, 25.6, 42.8, 35.6, 10.5, 8.2]
      });
    };
    const getPromotionData = () => {
      Object.assign(promotionData, {
        viewCount: "156",
        registerCount: "32",
        orderCount: "18",
        conversionRate: "11.5%"
      });
    };
    const getLevelIcon = () => {
      const icons = {
        1: "/static/images/tabbar/partner-level-1.png",
        2: "/static/images/tabbar/partner-level-2.png",
        3: "/static/images/tabbar/partner-level-3.png",
        4: "/static/images/tabbar/partner-level-4.png"
      };
      return icons[partnerInfo.level] || icons[1];
    };
    const getLevelName = () => {
      const names = {
        1: "普通合伙人",
        2: "银牌合伙人",
        3: "金牌合伙人",
        4: "钻石合伙人"
      };
      return names[partnerInfo.level] || names[1];
    };
    const getCommissionRate = (level) => {
      var _a;
      const commissionRates = {
        1: { 1: 5, 2: 2 },
        // 普通合伙人：一级5%，二级2%
        2: { 1: 8, 2: 3 },
        // 银牌合伙人：一级8%，二级3%
        3: { 1: 12, 2: 5 },
        // 金牌合伙人：一级12%，二级5%
        4: { 1: 15, 2: 8 }
        // 钻石合伙人：一级15%，二级8%
      };
      return ((_a = commissionRates[partnerInfo.level]) == null ? void 0 : _a[level]) || 0;
    };
    const navigateTo = (url) => {
      utils_navigation.smartNavigate(url).catch((err) => {
        common_vendor.index.__f__("error", "at subPackages/partner/pages/partner.vue:304", "智能导航失败:", err);
      });
    };
    const goBack = () => {
      common_vendor.index.navigateBack({
        fail: () => {
          common_vendor.index.switchTab({
            url: "/pages/my/my"
          });
        }
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: partnerInfo.avatar || "/static/images/avatar/default.png",
        d: common_vendor.t(partnerInfo.nickname || "游客"),
        e: getLevelIcon(),
        f: common_vendor.t(getLevelName()),
        g: progressWidth.value + "%",
        h: canUpgrade.value
      }, canUpgrade.value ? {
        i: common_vendor.o(($event) => navigateTo("/subPackages/partner/pages/partner-upgrade"))
      } : {}, {
        j: common_vendor.t(partnerInfo.totalIncome),
        k: common_vendor.t(incomeData.today),
        l: common_vendor.t(incomeData.yesterday),
        m: common_vendor.t(incomeData.thisWeek),
        n: common_vendor.f(chartData.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.value),
            b: item.height + "rpx",
            c: common_vendor.t(item.date),
            d: index
          };
        }),
        o: common_vendor.o(($event) => navigateTo("/subPackages/partner/pages/partner-income")),
        p: common_vendor.o(($event) => navigateTo("/subPackages/partner/pages/partner-fans")),
        q: common_vendor.o(($event) => navigateTo("/subPackages/partner/pages/partner-withdraw")),
        r: common_vendor.o(($event) => navigateTo("/subPackages/partner/pages/partner-poster")),
        s: common_vendor.o(($event) => navigateTo("/subPackages/partner/pages/partner-qrcode")),
        t: common_vendor.o(($event) => navigateTo("/subPackages/partner/pages/partner-rules")),
        v: common_vendor.o(($event) => navigateTo("/subPackages/partner/pages/partner-levels")),
        w: common_assets._imports_2$15,
        x: common_vendor.t(getCommissionRate(1)),
        y: common_assets._imports_2$16,
        z: common_vendor.t(getCommissionRate(2)),
        A: common_vendor.t(promotionData.viewCount),
        B: common_vendor.t(promotionData.registerCount),
        C: common_vendor.t(promotionData.orderCount),
        D: common_vendor.t(promotionData.conversionRate)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-338ed979"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/partner/pages/partner.js.map
