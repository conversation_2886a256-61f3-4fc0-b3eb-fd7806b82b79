"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_index = require("../../api/index.js");
const api_homeApi = require("../../api/homeApi.js");
if (!Math) {
  (ServiceGrid + FeatureGrid + CityNews + MerchantRecommend + InfoList)();
}
const ServiceGrid = () => "../../components/index/ServiceGrid.js";
const MerchantRecommend = () => "../../components/index/MerchantRecommend.js";
const InfoList = () => "../../components/index/InfoList.js";
const FeatureGrid = () => "../../components/index/FeatureGrid.js";
const CityNews = () => "../../components/index/CityNews.js";
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(0);
    const navBarHeight = common_vendor.ref(0);
    const navBarFullHeight = common_vendor.ref(0);
    const navTitleWidth = common_vendor.ref(0);
    const scrollTimer = common_vendor.ref(null);
    const showFollowTip = common_vendor.ref(true);
    const showQrCode = common_vendor.ref(false);
    const locationName = common_vendor.ref("磁县");
    const showLocationPicker = common_vendor.ref(false);
    const locationList = common_vendor.ref([
      { name: "磁县", id: "cx" },
      { name: "邯郸", id: "hd" },
      { name: "峰峰", id: "ff" },
      { name: "武安", id: "wa" }
    ]);
    common_vendor.ref(5);
    const bannerList = common_vendor.ref([]);
    const serviceList = common_vendor.ref([]);
    const showServicePopup = common_vendor.ref(false);
    const currentServiceCategory = common_vendor.ref({
      name: "",
      subItems: []
    });
    common_vendor.ref([
      { id: "all", name: "全部" },
      { id: "rent", name: "房屋租售" },
      { id: "job", name: "招聘求职" },
      { id: "service", name: "生活服务" },
      { id: "second", name: "二手交易" },
      { id: "car", name: "车辆信息" },
      { id: "other", name: "其他信息" }
    ]);
    common_vendor.ref([
      "到家服务",
      "寻找服务",
      "生意转让",
      "招聘信息",
      "求职信息",
      "房屋出租",
      "房屋出售",
      "二手车辆",
      "宠物信息",
      "商家活动",
      "婚恋交友",
      "车辆服务",
      "二手闲置",
      "磁州拼车",
      "教育培训",
      "其他服务"
    ]);
    const allInfoList = common_vendor.ref([
      { id: "business-transfer-restaurant-1", category: "生意转让", content: "【红包】黄金地段餐饮店整体转让，地处商业中心，设备齐全可直接营业！", time: "2024-05-16 08:30", views: 186, pageType: "business-transfer-detail", hasRedPacket: true, redPacketAmount: "15.88", redPacketType: "fixed", redPacketCount: 30, redPacketRemain: 18 },
      { id: "business-transfer-no-red-1", category: "生意转让", content: "县城中心奶茶店转让，客流稳定，接手即可盈利，因个人原因急转", time: "2024-05-16 10:15", views: 135, pageType: "business-transfer-detail", hasRedPacket: false },
      { id: "red-packet-service-1", category: "到家服务", content: "【红包】专业家庭保洁服务，首单立减20元，预约送10元现金红包！", time: "2024-05-15 09:30", views: 235, pageType: "home-service-detail", hasRedPacket: true, redPacketAmount: "10.00", redPacketType: "fixed", redPacketCount: 50, redPacketRemain: 32 },
      { id: "service-no-red-1", category: "到家服务", content: "专业上门维修空调、冰箱、洗衣机等家电，技术精湛，价格公道", time: "2024-05-15 11:30", views: 176, pageType: "home-service-detail", hasRedPacket: false },
      { id: "red-packet-business-1", category: "生意转让", content: "【红包】黄金地段餐饮店整体转让，接手即可盈利，老板定制18.8元红包！", time: "2024-05-14 16:20", views: 328, pageType: "business-transfer-detail", hasRedPacket: true, redPacketAmount: "18.88", redPacketType: "fixed", redPacketCount: 100, redPacketRemain: 45 },
      { id: "red-packet-job-1", category: "招聘信息", content: "【红包】招聘销售经理5名，底薪5000+提成，咨询简历投递送随机红包！", time: "2024-05-14 15:15", views: 456, pageType: "job-detail", hasRedPacket: true, redPacketAmount: "66.66", redPacketType: "random", redPacketCount: 30, redPacketRemain: 12 },
      { id: "job-no-red-1", category: "招聘信息", content: "急招会计1名，五险一金，双休，2年以上工作经验，薪资4500-6000", time: "2024-05-15 14:30", views: 208, pageType: "job-detail", hasRedPacket: false },
      { id: "job-seeking-no-red-1", category: "求职信息", content: "计算机专业应届毕业生求职，熟悉前端开发，有项目经验，可立即上岗", time: "2024-05-15 16:45", views: 125, pageType: "job-seeking-detail", hasRedPacket: false },
      { id: "red-packet-house-1", category: "房屋出租", content: "【红包】市中心精装两室一厅出租，家电齐全，看房送15元现金红包！", time: "2024-05-13 14:30", views: 289, pageType: "house-rent-detail", hasRedPacket: true, redPacketAmount: "15.00", redPacketType: "fixed", redPacketCount: 20, redPacketRemain: 8 },
      { id: "house-rent-no-red-1", category: "房屋出租", content: "学区房两室一厅出租，家电家具齐全，拎包入住，交通便利", time: "2024-05-14 10:20", views: 197, pageType: "house-rent-detail", hasRedPacket: false },
      { id: "house-sale-no-red-1", category: "房屋出售", content: "县城南区新房，三室两厅，120平米，毛坯房，采光好，有车位", time: "2024-05-14 09:15", views: 268, pageType: "house-sale-detail", hasRedPacket: false },
      { id: "red-packet-car-1", category: "二手车辆", content: "【红包】2023款本田雅阁2.0L，准新车，行驶5000公里，查询车况送红包！", time: "2024-05-12 13:25", views: 376, pageType: "car-detail", hasRedPacket: true, redPacketAmount: "20.00", redPacketType: "fixed", redPacketCount: 25, redPacketRemain: 10 },
      { id: "car-no-red-1", category: "二手车辆", content: "2020款大众朗逸，1.5L自动挡，行驶3万公里，无事故，一手车", time: "2024-05-14 13:40", views: 189, pageType: "car-detail", hasRedPacket: false },
      { id: "pet-no-red-1", category: "宠物信息", content: "家养蓝猫幼崽出售，2个月大，已驱虫，疫苗已做，可上门看猫", time: "2024-05-14 15:20", views: 162, pageType: "pet-detail", hasRedPacket: false },
      { id: "merchant-activity-no-red-1", category: "商家活动", content: "新开张火锅店满减活动，满100减30，满200减80，还有精美礼品赠送", time: "2024-05-14 11:25", views: 248, pageType: "merchant-activity-detail", hasRedPacket: false },
      { id: "red-packet-vehicle-1", category: "车辆服务", content: "【红包】专业汽车美容贴膜，隐形车衣，预约试用送50元红包！", time: "2024-05-11 11:45", views: 198, pageType: "vehicle-service-detail", hasRedPacket: true, redPacketAmount: "50.00", redPacketType: "fixed", redPacketCount: 10, redPacketRemain: 5 },
      { id: "vehicle-service-no-red-1", category: "车辆服务", content: "专业汽车保养，机油三滤更换，四轮定位，价格实惠，技术可靠", time: "2024-05-14 16:30", views: 156, pageType: "vehicle-service-detail", hasRedPacket: false },
      { id: "red-packet-second-hand-1", category: "二手闲置", content: "【红包】全新iPhone 15 Pro Max，黑色256G，抽奖送华为手环！", time: "2024-05-10 10:20", views: 468, pageType: "second-hand-detail", hasRedPacket: true, redPacketAmount: "88.88", redPacketType: "random", redPacketCount: 5, redPacketRemain: 2 },
      { id: "second-hand-no-red-1", category: "二手闲置", content: "9成新MacBook Pro 2022款，M1芯片，16G内存，512G硬盘，原价12999", time: "2024-05-14 17:10", views: 215, pageType: "second-hand-detail", hasRedPacket: false },
      { id: "ride-share-no-red-1", category: "磁州拼车", content: "每天早上7点县城到邯郸拼车，轿车舒适，准时发车，长期有效", time: "2024-05-14 18:20", views: 183, pageType: "carpool-detail", hasRedPacket: false },
      { id: "education-no-red-1", category: "教育培训", content: "小学初中高中各科辅导，一对一定制教学计划，提分效果明显", time: "2024-05-14 19:10", views: 134, pageType: "education-detail", hasRedPacket: false },
      { id: "dating-red-1", category: "婚恋交友", content: "【红包】28岁女士，本科学历，身高165cm，温柔大方，期待遇见有缘人", time: "2024-05-16 09:15", views: 228, pageType: "dating-detail", hasRedPacket: true, redPacketAmount: "18.88", redPacketType: "fixed", redPacketCount: 20, redPacketRemain: 12 },
      { id: "dating-no-red-1", category: "婚恋交友", content: "32岁男士，身高178cm，事业稳定，性格开朗，寻找志同道合的另一半", time: "2024-05-15 14:20", views: 176, pageType: "dating-detail", hasRedPacket: false },
      { id: "merchant-activity-red-1", category: "商家活动", content: "【红包】新开业烤肉店满减活动，满200减100，关注公众号送20元无门槛代金券", time: "2024-05-16 10:30", views: 315, pageType: "merchant-activity-detail", hasRedPacket: true, redPacketAmount: "20.00", redPacketType: "fixed", redPacketCount: 40, redPacketRemain: 25 },
      { id: "merchant-activity-no-red-2", category: "商家活动", content: "周年庆典大促，全场化妆品8折起，部分商品买一送一，活动时间5月20-25日", time: "2024-05-15 16:40", views: 198, pageType: "merchant-activity-detail", hasRedPacket: false },
      { id: "vehicle-service-no-red-2", category: "车辆服务", content: "专业汽车保养，机油三滤更换，四轮定位，价格实惠，技术可靠", time: "2024-05-14 16:30", views: 156, pageType: "vehicle-service-detail", hasRedPacket: false },
      { id: "carpool-1", category: "磁州拼车", content: "磁县→邯郸，每天早8点发车，舒适型轿车，可带行李，剩余3个座位", time: "2024-05-16 07:30", views: 187, pageType: "carpool-detail", hasRedPacket: false },
      { id: "carpool-2", category: "磁州拼车", content: "邯郸→磁县，下午5点发车，每天固定班次，可提前预约，支持改签", time: "2024-05-16 08:15", views: 145, pageType: "carpool-detail", hasRedPacket: false },
      { id: "carpool-red-1", category: "磁州拼车", content: "【红包】磁县→石家庄，周六早7点发车，舒适商务车，咨询送10元红包！", time: "2024-05-15 16:40", views: 223, pageType: "carpool-detail", hasRedPacket: true, redPacketAmount: "10.00", redPacketType: "fixed", redPacketCount: 20, redPacketRemain: 15 },
      { id: "carpool-3", category: "磁州拼车", content: "磁县→北京，周五晚8点发车，7座商务车，舒适安全，提供夜间服务", time: "2024-05-14 14:20", views: 198, pageType: "carpool-detail", hasRedPacket: false }
    ]);
    const toppedInfoList = common_vendor.ref([
      {
        id: "topped-job-1",
        category: "招聘信息",
        content: "急招会计1名，五险一金，双休，2年以上工作经验，薪资4500-6000",
        time: "2024-05-15 14:30",
        views: 208,
        pageType: "job-detail",
        isTopped: true,
        topType: "paid",
        topExpiry: "2024-05-20"
      },
      {
        id: "topped-house-1",
        category: "房屋出租",
        content: "市中心精装两室一厅出租，家电齐全，拎包入住，交通便利，周边配套设施完善",
        time: "2024-05-14 10:20",
        views: 197,
        pageType: "house-rent-detail",
        isTopped: true,
        topType: "ad",
        topExpiry: "2024-05-22",
        images: ["/static/images/tabbar/wxacode.jpg", "/static/images/tabbar/wxacode.jpg"]
      }
    ]);
    const adBanner = common_vendor.ref({
      image: "/static/images/ad-banner.jpg",
      url: "/pages/activity/detail?id=3"
    });
    common_vendor.ref({
      title: "关注公众号",
      image: "/static/images/qrcode.jpg",
      tips: "扫码关注磁州生活",
      desc: "获取更多本地信息和优惠"
    });
    const searchKeyword = common_vendor.ref("");
    common_vendor.ref(0);
    common_vendor.ref(false);
    common_vendor.ref([
      "到家服务",
      "寻找服务",
      "生意转让",
      "招聘信息",
      "求职信息",
      "房屋出租",
      "房屋出售",
      "二手车辆",
      "宠物信息",
      "商家活动",
      "婚恋交友",
      "车辆服务",
      "二手闲置",
      "磁州拼车",
      "教育培训",
      "其他服务"
    ]);
    common_vendor.ref({
      allInfoList: [],
      toppedInfoList: []
    });
    adBanner.value = {
      image: "/static/images/banner/ad-banner.jpg",
      url: "/pages/ad/detail",
      title: "广告横幅"
    };
    common_vendor.onMounted(() => {
      loadHomeData();
      loadInfoData();
      loadNewsData();
      loadBusinessData();
    });
    async function loadHomeData() {
      try {
        common_vendor.index.__f__("log", "at pages/index/index.vue:335", "开始从后台管理系统加载首页数据...");
        const [banners, services, config, stats, features, merchants, news] = await Promise.all([
          api_homeApi.homeApi.getBanners(),
          api_homeApi.homeApi.getServiceCategories(),
          api_homeApi.homeApi.getHomeConfig(),
          api_homeApi.homeApi.getHomeStats(),
          api_homeApi.homeApi.getFeatureConfig(),
          api_homeApi.homeApi.getMerchantRecommend(),
          api_homeApi.homeApi.getCityNews()
        ]);
        if (banners && banners.length > 0) {
          bannerList.value = banners.map((banner) => ({
            id: banner.id,
            image: banner.image,
            url: banner.url || "",
            title: banner.title || ""
          }));
          common_vendor.index.__f__("log", "at pages/index/index.vue:356", "✅ 轮播图数据已更新:", bannerList.value.length, "张");
        }
        if (services && services.length > 0) {
          serviceList.value = services.map((service) => ({
            icon: service.icon || "/static/images/service/default.png",
            name: service.name,
            url: service.url || `/pages/service/${service.id}`
          }));
          common_vendor.index.__f__("log", "at pages/index/index.vue:366", "✅ 服务分类数据已更新:", serviceList.value.length, "个");
        }
        if (features && features.length > 0) {
          common_vendor.index.__f__("log", "at pages/index/index.vue:371", "✅ 特色功能数据已获取:", features.length, "个功能");
        }
        if (merchants && merchants.length > 0) {
          common_vendor.index.__f__("log", "at pages/index/index.vue:377", "✅ 商家推荐数据已获取:", merchants.length, "个商家");
        }
        if (news && news.length > 0) {
          common_vendor.index.__f__("log", "at pages/index/index.vue:383", "✅ 同城资讯数据已获取:", news.length, "条资讯");
        }
        if (config && config.site_title) {
          common_vendor.index.setNavigationBarTitle({
            title: config.site_title
          });
          common_vendor.index.__f__("log", "at pages/index/index.vue:392", "✅ 页面标题已更新:", config.site_title);
        }
        api_homeApi.homeApi.recordPageView();
        common_vendor.index.__f__("log", "at pages/index/index.vue:398", "🎉 后台管理系统数据加载完成!");
        common_vendor.index.__f__("log", "at pages/index/index.vue:399", "📊 数据统计:", {
          banners: bannerList.value.length,
          services: serviceList.value.length,
          features: (features == null ? void 0 : features.length) || 0,
          merchants: (merchants == null ? void 0 : merchants.length) || 0,
          news: (news == null ? void 0 : news.length) || 0,
          config: (config == null ? void 0 : config.site_title) || "未配置",
          stats: (stats == null ? void 0 : stats.total_views) || 0
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:410", "加载首页数据失败:", error);
        bannerList.value = [
          { id: 1, image: "/static/images/banner/banner-1.png", url: "", title: "磁州生活网" },
          { id: 2, image: "/static/images/banner/banner-2.png", url: "", title: "本地生活服务" }
        ];
        serviceList.value = [
          { icon: "/static/images/service/food.png", name: "美食外卖", url: "/pages/service/food" },
          { icon: "/static/images/service/market.png", name: "超市便利", url: "/pages/service/market" },
          { icon: "/static/images/service/medicine.png", name: "医药健康", url: "/pages/service/medicine" },
          { icon: "/static/images/service/fresh.png", name: "生鲜果蔬", url: "/pages/service/fresh" },
          { icon: "/static/images/service/flower.png", name: "鲜花绿植", url: "/pages/service/flower" },
          { icon: "/static/images/service/clean.png", name: "家政保洁", url: "/pages/service/clean" },
          { icon: "/static/images/service/repair.png", name: "维修服务", url: "/pages/service/repair" },
          { icon: "/static/images/service/more.png", name: "更多服务", url: "/pages/service/more" }
        ];
      }
    }
    async function loadNewsData() {
      try {
        const result = await api_index.api.news.getList({ page: 1, limit: 10 });
        if (result.success) {
          common_vendor.index.__f__("log", "at pages/index/index.vue:437", "新闻数据加载成功:", result.data);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:440", "加载新闻数据失败:", error);
      }
    }
    async function loadBusinessData() {
      try {
        const result = await api_index.api.business.getList({ page: 1, limit: 10 });
        if (result.success) {
          common_vendor.index.__f__("log", "at pages/index/index.vue:450", "商家数据加载成功:", result.data);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:453", "加载商家数据失败:", error);
      }
    }
    async function loadInfoData() {
      try {
        common_vendor.index.showLoading({
          title: "加载中..."
        });
        const [toppedResult, allResult] = await Promise.all([
          api_index.api.info.getTopped({ page: 1, limit: 10 }),
          api_index.api.info.getAll({ page: 1, limit: 20 })
        ]);
        if (toppedResult.success) {
          toppedInfoList.value = toppedResult.data.map((item) => ({
            ...item,
            images: item.images || [],
            tags: item.tags || [],
            views: item.views || 0,
            likes: item.likes || 0,
            comments: item.comments || 0
          }));
        } else {
          common_vendor.index.__f__("error", "at pages/index/index.vue:482", "获取置顶信息失败:", toppedResult.message);
        }
        if (allResult.success) {
          allInfoList.value = allResult.data.map((item) => ({
            ...item,
            images: item.images || [],
            tags: item.tags || [],
            views: item.views || 0,
            likes: item.likes || 0,
            comments: item.comments || 0
          }));
        } else {
          common_vendor.index.__f__("error", "at pages/index/index.vue:496", "获取信息列表失败:", allResult.message);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:500", "加载信息数据失败:", error);
        common_vendor.index.showToast({
          title: "加载失败，请重试",
          icon: "none"
        });
        toppedInfoList.value = [];
        allInfoList.value = [];
      } finally {
        common_vendor.index.hideLoading();
      }
    }
    function handleTabChange(tabInfo) {
      common_vendor.index.__f__("log", "at pages/index/index.vue:516", "标签切换:", tabInfo);
    }
    const infoList = common_vendor.ref(null);
    function checkUserLocation() {
      common_vendor.index.__f__("log", "at pages/index/index.vue:527", "检查用户位置");
    }
    function loadBannerData() {
      common_vendor.index.__f__("log", "at pages/index/index.vue:533", "加载轮播图数据");
    }
    function loadServiceData() {
      common_vendor.index.__f__("log", "at pages/index/index.vue:539", "加载服务分类数据");
    }
    function loadMerchantData() {
      common_vendor.index.__f__("log", "at pages/index/index.vue:545", "加载商家推荐数据");
    }
    function closeFollowTip() {
      showFollowTip.value = false;
      common_vendor.index.setStorageSync("hideFollowTip", true);
    }
    function closeQrCode() {
      showQrCode.value = false;
    }
    function navigateTo(url) {
      if (!url)
        return;
      common_vendor.index.navigateTo({
        url,
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/index/index.vue:572", "页面跳转失败:", err);
          common_vendor.index.switchTab({
            url,
            fail: (err2) => {
              common_vendor.index.__f__("error", "at pages/index/index.vue:577", "switchTab也失败:", err2);
              common_vendor.index.showToast({
                title: "页面跳转失败",
                icon: "none"
              });
            }
          });
        }
      });
    }
    function closeLocationPicker() {
      showLocationPicker.value = false;
    }
    function selectLocation(item) {
      locationName.value = item.name;
      closeLocationPicker();
      loadBannerData();
      loadServiceData();
      loadMerchantData();
      loadInfoData();
    }
    function closeServicePopup() {
      showServicePopup.value = false;
    }
    function navigateToSubService(item) {
      if (!item.url)
        return;
      common_vendor.index.navigateTo({
        url: item.url,
        fail: () => {
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "none"
          });
        }
      });
      closeServicePopup();
    }
    function onSearchInput(e) {
      searchKeyword.value = e.detail.value;
    }
    function doSearch() {
      if (!searchKeyword.value.trim()) {
        common_vendor.index.showToast({
          title: "请输入搜索关键词",
          icon: "none"
        });
        return;
      }
      performSimpleSearch();
    }
    function performSimpleSearch() {
      common_vendor.index.showLoading({
        title: "搜索中..."
      });
      const searchResults = [];
      toppedInfoList.value.forEach((item) => {
        if (item.content && item.content.includes(searchKeyword.value)) {
          searchResults.push(item);
        }
      });
      allInfoList.value.forEach((item) => {
        if (item.content && item.content.includes(searchKeyword.value)) {
          searchResults.push(item);
        }
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        if (searchResults.length > 0) {
          common_vendor.index.showModal({
            title: "搜索结果",
            content: `找到 ${searchResults.length} 条相关信息，点击确定查看详情`,
            success: (res) => {
              if (res.confirm && searchResults.length > 0) {
                navigateToInfoDetail(searchResults[0]);
              }
            }
          });
        } else {
          common_vendor.index.showToast({
            title: "未找到相关信息",
            icon: "none"
          });
        }
      }, 500);
    }
    function navigateToInfoDetail(item) {
      common_vendor.index.navigateTo({
        url: `/pages/info/detail?id=${item.id}`,
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/index/index.vue:718", "导航到信息详情页失败:", err);
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "none"
          });
        }
      });
    }
    function handlePageScroll(scrollTop) {
      if (infoList.value) {
        const query = common_vendor.index.createSelectorQuery();
        query.select(".scrollable-content >>> .all-info-title-row").boundingClientRect((data) => {
          if (!data) {
            return;
          }
          const navBarHeight2 = 44;
          const fixedHeaderHeight = statusBarHeight.value + navBarHeight2 - 1;
          const titleRowBottom = data.top + data.height;
          if (titleRowBottom <= fixedHeaderHeight) {
            infoList.value.setSticky(true, fixedHeaderHeight);
          } else {
            infoList.value.setSticky(false, 0);
          }
        }).exec();
      }
    }
    common_vendor.onLoad(() => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      const menuButtonInfo = common_vendor.index.getMenuButtonBoundingClientRect();
      statusBarHeight.value = systemInfo.statusBarHeight || 20;
      navBarHeight.value = (menuButtonInfo.top - statusBarHeight.value) * 2 + menuButtonInfo.height + 3;
      navBarFullHeight.value = statusBarHeight.value + navBarHeight.value;
      navTitleWidth.value = menuButtonInfo.left;
      checkUserLocation();
      loadBannerData();
      loadServiceData();
      loadMerchantData();
      loadInfoData();
    });
    common_vendor.onShow(() => {
      const currentWindowInfo = common_vendor.index.getWindowInfo();
      statusBarHeight.value = currentWindowInfo.statusBarHeight || 20;
    });
    common_vendor.onReady(() => {
      setTimeout(() => {
        const currentWindowInfo = common_vendor.index.getWindowInfo();
        statusBarHeight.value = currentWindowInfo.statusBarHeight || 20;
      }, 50);
    });
    common_vendor.onPageScroll((e) => {
      if (scrollTimer.value) {
        return;
      }
      scrollTimer.value = setTimeout(() => {
        handlePageScroll(e.scrollTop);
        scrollTimer.value = null;
      }, 100);
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: statusBarHeight.value + "px",
        b: navBarHeight.value + "px",
        c: navBarFullHeight.value + "px",
        d: showFollowTip.value
      }, showFollowTip.value ? {
        e: common_assets._imports_0$3,
        f: common_vendor.o(($event) => showQrCode.value = true),
        g: common_vendor.o(closeFollowTip)
      } : {}, {
        h: common_vendor.f(bannerList.value, (item, index, i0) => {
          return {
            a: item.image,
            b: common_vendor.o(($event) => navigateTo(item.url), index),
            c: index
          };
        }),
        i: common_vendor.p({
          serviceList: serviceList.value
        }),
        j: common_assets._imports_1$2,
        k: common_vendor.o(onSearchInput),
        l: common_vendor.o(doSearch),
        m: common_vendor.o(doSearch),
        n: common_vendor.sr(infoList, "31c058a0-4", {
          "k": "infoList"
        }),
        o: common_vendor.o(handleTabChange),
        p: common_vendor.p({
          allInfoList: allInfoList.value,
          toppedInfoList: toppedInfoList.value,
          adBanner: adBanner.value
        }),
        q: navBarFullHeight.value + "px",
        r: showQrCode.value
      }, showQrCode.value ? {
        s: common_vendor.o(closeQrCode),
        t: common_assets._imports_3$1,
        v: common_vendor.o(() => {
        }),
        w: common_vendor.o(closeQrCode)
      } : {}, {
        x: showServicePopup.value
      }, showServicePopup.value ? {
        y: common_vendor.o(closeServicePopup),
        z: common_vendor.t(currentServiceCategory.value.name),
        A: common_vendor.o(closeServicePopup),
        B: common_vendor.f(currentServiceCategory.value.subItems, (item, index, i0) => {
          return {
            a: item.icon,
            b: common_vendor.t(item.name),
            c: index,
            d: common_vendor.o(($event) => navigateToSubService(item), index)
          };
        })
      } : {}, {
        C: showLocationPicker.value
      }, showLocationPicker.value ? {
        D: common_vendor.o(closeLocationPicker),
        E: common_vendor.o(closeLocationPicker),
        F: common_vendor.f(locationList.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.name),
            b: locationName.value === item.name
          }, locationName.value === item.name ? {} : {}, {
            c: index,
            d: common_vendor.o(($event) => selectLocation(item), index),
            e: locationName.value === item.name ? 1 : ""
          });
        })
      } : {});
    };
  }
};
_sfc_main.__runtimeHooks = 1;
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
