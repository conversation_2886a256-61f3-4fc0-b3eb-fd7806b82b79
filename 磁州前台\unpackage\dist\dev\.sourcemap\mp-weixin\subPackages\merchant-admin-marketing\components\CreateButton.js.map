{"version": 3, "file": "CreateButton.js", "sources": ["subPackages/merchant-admin-marketing/components/CreateButton.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7Avc3ViUGFja2FnZXMvbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nL2NvbXBvbmVudHMvQ3JlYXRlQnV0dG9uLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"create-button\" :class=\"themeClass\" @tap=\"handleTap\">\n    <view class=\"button-content\">\n      <text class=\"plus-icon\">+</text>\n      <text class=\"button-text\">{{ text }}</text>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'CreateButton',\n  props: {\n    text: {\n      type: String,\n      default: '创建'\n    },\n    url: {\n      type: String,\n      default: ''\n    },\n    theme: {\n      type: String,\n      default: 'default' // 可以是 'coupon', 'discount', 'group', 'flash' 或默认\n    }\n  },\n  computed: {\n    themeClass() {\n      return `theme-${this.theme}`;\n    }\n  },\n  methods: {\n    handleTap() {\n      if (this.url) {\n        uni.navigateTo({\n          url: this.url\n        });\n      } else {\n        this.$emit('click');\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.create-button {\n  height: 40px;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 20px;\n  padding: 0 20px;\n  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n/* 默认主题 - 紫色渐变 */\n.theme-default {\n  background: linear-gradient(135deg, #9254de, #7a36d6);\n  box-shadow: 0 4px 10px rgba(122, 54, 214, 0.2);\n}\n\n/* 优惠券主题 - 红橙色渐变 */\n.theme-coupon {\n  background: linear-gradient(135deg, #FF9966, #FF5E62);\n  box-shadow: 0 4px 10px rgba(255, 94, 98, 0.2);\n}\n\n/* 满减活动主题 - 黄色渐变 */\n.theme-discount {\n  background: linear-gradient(135deg, #FDEB71, #F8D800);\n  box-shadow: 0 4px 10px rgba(248, 216, 0, 0.2);\n}\n\n/* 拼团活动主题 - 紫色渐变 */\n.theme-group {\n  background: linear-gradient(135deg, #9040FF, #5E35B1);\n  box-shadow: 0 4px 10px rgba(144, 64, 255, 0.2);\n}\n\n/* 秒杀活动主题 - 橙红色渐变 */\n.theme-flash {\n  background: linear-gradient(135deg, #FF7600, #FF3C00);\n  box-shadow: 0 4px 10px rgba(255, 118, 0, 0.2);\n}\n\n/* 积分商城主题 - 橙色渐变 */\n.theme-points {\n  background: linear-gradient(135deg, #FF7600, #FF3C00);\n  box-shadow: 0 4px 10px rgba(255, 118, 0, 0.2);\n}\n\n.create-button:active {\n  transform: scale(0.98);\n  opacity: 0.9;\n}\n\n.button-content {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.plus-icon {\n  font-size: 18px;\n  font-weight: bold;\n  color: #fff;\n  margin-right: 4px;\n}\n\n.button-text {\n  font-size: 14px;\n  font-weight: 500;\n  color: #fff;\n}\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/components/CreateButton.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;AAUA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,KAAK;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA;AAAA,IACX;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,aAAa;AACX,aAAO,SAAS,KAAK,KAAK;AAAA,IAC5B;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,YAAY;AACV,UAAI,KAAK,KAAK;AACZA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,KAAK;AAAA,QACZ,CAAC;AAAA,aACI;AACL,aAAK,MAAM,OAAO;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;ACzCA,GAAG,gBAAgB,SAAS;"}