{"version": 3, "names": ["_setFunctionName", "require", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "old_createMetadataMethodsForProperty", "metadataMap", "kind", "property", "decoratorFinishedRef", "getMetadata", "key", "old_assertNotFinished", "old_assertMetadataKey", "metadataForKey", "pub", "public", "priv", "private", "get", "Object", "hasOwnProperty", "call", "constructor", "setMetadata", "value", "Map", "set", "old_convertMetadataMapToFinal", "obj", "parentMetadataMap", "Symbol", "metadata", "for", "metadataKeys", "getOwnPropertySymbols", "length", "i", "metaForKey", "parentMetaForKey", "parentPub", "setPrototypeOf", "privArr", "Array", "from", "values", "parentPriv", "concat", "old_createAddInitializerMethod", "initializers", "addInitializer", "initializer", "old_assertCallable", "push", "old_memberDec", "dec", "name", "desc", "isStatic", "isPrivate", "kindStr", "ctx", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "v", "metadataKind", "metadataName", "access", "assign", "fnName", "Error", "TypeError", "fn", "hint", "old_assertValidReturnValue", "type", "undefined", "init", "old_getInit", "console", "warn", "old_applyMemberDec", "ret", "base", "decInfo", "decs", "prefix", "setFunctionName", "getOwnPropertyDescriptor", "newValue", "newInit", "instance", "ownInitializers", "originalInitializer", "args", "defineProperty", "old_applyMemberDecs", "Class", "protoMetadataMap", "staticMetadataMap", "decInfos", "protoInitializers", "staticInitializers", "existingProtoNonFields", "existingStaticNonFields", "isArray", "prototype", "existingNonFields", "existingKind", "old_pushInitializers", "old_applyClassDecs", "targetClass", "classDecs", "newClass", "nextNewClass", "applyDecs", "memberDecs"], "sources": ["../../src/helpers/applyDecs.js"], "sourcesContent": ["/* @minVersion 7.17.8 */\n/* @onlyBabel7 */\n\nimport setFunctionName from \"setFunctionName\";\nimport toProperty<PERSON><PERSON> from \"toPropertyKey\";\n/**\n * NOTE: This is an old version of the helper, used for 2021-12 decorators.\n * Updates should be done in applyDecs2203R.js.\n */\n\n/**\n  Enums are used in this file, but not assigned to vars to avoid non-hoistable values\n\n  CONSTRUCTOR = 0;\n  PUBLIC = 1;\n  PRIVATE = 2;\n\n  FIELD = 0;\n  ACCESSOR = 1;\n  METHOD = 2;\n  GETTER = 3;\n  SETTER = 4;\n\n  STATIC = 5;\n\n  CLASS = 10; // only used in assertValidReturnValue\n*/\n\nfunction old_createMetadataMethodsForProperty(\n  metadataMap,\n  kind,\n  property,\n  decoratorFinishedRef,\n) {\n  return {\n    getMetadata: function (key) {\n      old_assertNotFinished(decoratorFinishedRef, \"getMetadata\");\n      old_assertMetadataKey(key);\n\n      var metadataForKey = metadataMap[key];\n\n      if (metadataForKey === void 0) return void 0;\n\n      if (kind === 1 /* PUBLIC */) {\n        var pub = metadataForKey.public;\n        if (pub !== void 0) {\n          return pub[property];\n        }\n      } else if (kind === 2 /* PRIVATE */) {\n        var priv = metadataForKey.private;\n        if (priv !== void 0) {\n          return priv.get(property);\n        }\n      } else if (Object.hasOwnProperty.call(metadataForKey, \"constructor\")) {\n        return metadataForKey.constructor;\n      }\n    },\n    setMetadata: function (key, value) {\n      old_assertNotFinished(decoratorFinishedRef, \"setMetadata\");\n      old_assertMetadataKey(key);\n\n      var metadataForKey = metadataMap[key];\n\n      if (metadataForKey === void 0) {\n        metadataForKey = metadataMap[key] = {};\n      }\n\n      if (kind === 1 /* PUBLIC */) {\n        var pub = metadataForKey.public;\n\n        if (pub === void 0) {\n          pub = metadataForKey.public = {};\n        }\n\n        pub[property] = value;\n      } else if (kind === 2 /* PRIVATE */) {\n        var priv = metadataForKey.priv;\n\n        if (priv === void 0) {\n          priv = metadataForKey.private = new Map();\n        }\n\n        priv.set(property, value);\n      } else {\n        metadataForKey.constructor = value;\n      }\n    },\n  };\n}\n\nfunction old_convertMetadataMapToFinal(obj, metadataMap) {\n  var parentMetadataMap = obj[Symbol.metadata || Symbol.for(\"Symbol.metadata\")];\n  var metadataKeys = Object.getOwnPropertySymbols(metadataMap);\n\n  if (metadataKeys.length === 0) return;\n\n  for (var i = 0; i < metadataKeys.length; i++) {\n    var key = metadataKeys[i];\n    var metaForKey = metadataMap[key];\n    var parentMetaForKey = parentMetadataMap ? parentMetadataMap[key] : null;\n\n    var pub = metaForKey.public;\n    var parentPub = parentMetaForKey ? parentMetaForKey.public : null;\n\n    if (pub && parentPub) {\n      Object.setPrototypeOf(pub, parentPub);\n    }\n\n    var priv = metaForKey.private;\n\n    if (priv) {\n      var privArr = Array.from(priv.values());\n      var parentPriv = parentMetaForKey ? parentMetaForKey.private : null;\n\n      if (parentPriv) {\n        privArr = privArr.concat(parentPriv);\n      }\n\n      metaForKey.private = privArr;\n    }\n\n    if (parentMetaForKey) {\n      Object.setPrototypeOf(metaForKey, parentMetaForKey);\n    }\n  }\n\n  if (parentMetadataMap) {\n    Object.setPrototypeOf(metadataMap, parentMetadataMap);\n  }\n\n  obj[Symbol.metadata || Symbol.for(\"Symbol.metadata\")] = metadataMap;\n}\n\nfunction old_createAddInitializerMethod(initializers, decoratorFinishedRef) {\n  return function addInitializer(initializer) {\n    old_assertNotFinished(decoratorFinishedRef, \"addInitializer\");\n    old_assertCallable(initializer, \"An initializer\");\n    initializers.push(initializer);\n  };\n}\n\nfunction old_memberDec(\n  dec,\n  name,\n  desc,\n  metadataMap,\n  initializers,\n  kind,\n  isStatic,\n  isPrivate,\n  value,\n) {\n  var kindStr;\n\n  switch (kind) {\n    case 1 /* ACCESSOR */:\n      kindStr = \"accessor\";\n      break;\n    case 2 /* METHOD */:\n      kindStr = \"method\";\n      break;\n    case 3 /* GETTER */:\n      kindStr = \"getter\";\n      break;\n    case 4 /* SETTER */:\n      kindStr = \"setter\";\n      break;\n    default:\n      kindStr = \"field\";\n  }\n\n  var ctx = {\n    kind: kindStr,\n    name: isPrivate ? \"#\" + name : toPropertyKey(name),\n    isStatic: isStatic,\n    isPrivate: isPrivate,\n  };\n\n  var decoratorFinishedRef = { v: false };\n\n  if (kind !== 0 /* FIELD */) {\n    ctx.addInitializer = old_createAddInitializerMethod(\n      initializers,\n      decoratorFinishedRef,\n    );\n  }\n\n  var metadataKind, metadataName;\n\n  if (isPrivate) {\n    metadataKind = 2 /* PRIVATE */;\n    metadataName = Symbol(name);\n\n    var access = {};\n\n    if (kind === 0 /* FIELD */) {\n      access.get = desc.get;\n      access.set = desc.set;\n    } else if (kind === 2 /* METHOD */) {\n      access.get = function () {\n        return desc.value;\n      };\n    } else {\n      // replace with values that will go through the final getter and setter\n      if (kind === 1 /* ACCESSOR */ || kind === 3 /* GETTER */) {\n        access.get = function () {\n          return desc.get.call(this);\n        };\n      }\n\n      if (kind === 1 /* ACCESSOR */ || kind === 4 /* SETTER */) {\n        access.set = function (v) {\n          desc.set.call(this, v);\n        };\n      }\n    }\n\n    ctx.access = access;\n  } else {\n    metadataKind = 1 /* PUBLIC */;\n    metadataName = name;\n  }\n\n  try {\n    return dec(\n      value,\n      Object.assign(\n        ctx,\n        old_createMetadataMethodsForProperty(\n          metadataMap,\n          metadataKind,\n          metadataName,\n          decoratorFinishedRef,\n        ),\n      ),\n    );\n  } finally {\n    decoratorFinishedRef.v = true;\n  }\n}\n\nfunction old_assertNotFinished(decoratorFinishedRef, fnName) {\n  if (decoratorFinishedRef.v) {\n    throw new Error(\n      \"attempted to call \" + fnName + \" after decoration was finished\",\n    );\n  }\n}\n\nfunction old_assertMetadataKey(key) {\n  if (typeof key !== \"symbol\") {\n    throw new TypeError(\"Metadata keys must be symbols, received: \" + key);\n  }\n}\n\nfunction old_assertCallable(fn, hint) {\n  if (typeof fn !== \"function\") {\n    throw new TypeError(hint + \" must be a function\");\n  }\n}\n\nfunction old_assertValidReturnValue(kind, value) {\n  var type = typeof value;\n\n  if (kind === 1 /* ACCESSOR */) {\n    if (type !== \"object\" || value === null) {\n      throw new TypeError(\n        \"accessor decorators must return an object with get, set, or init properties or void 0\",\n      );\n    }\n    if (value.get !== undefined) {\n      old_assertCallable(value.get, \"accessor.get\");\n    }\n    if (value.set !== undefined) {\n      old_assertCallable(value.set, \"accessor.set\");\n    }\n    if (value.init !== undefined) {\n      old_assertCallable(value.init, \"accessor.init\");\n    }\n    if (value.initializer !== undefined) {\n      old_assertCallable(value.initializer, \"accessor.initializer\");\n    }\n  } else if (type !== \"function\") {\n    var hint;\n    if (kind === 0 /* FIELD */) {\n      hint = \"field\";\n    } else if (kind === 10 /* CLASS */) {\n      hint = \"class\";\n    } else {\n      hint = \"method\";\n    }\n    throw new TypeError(hint + \" decorators must return a function or void 0\");\n  }\n}\n\nfunction old_getInit(desc) {\n  var initializer;\n  if (\n    (initializer = desc.init) == null &&\n    (initializer = desc.initializer) &&\n    typeof console !== \"undefined\"\n  ) {\n    console.warn(\".initializer has been renamed to .init as of March 2022\");\n  }\n  return initializer;\n}\n\nfunction old_applyMemberDec(\n  ret,\n  base,\n  decInfo,\n  name,\n  kind,\n  isStatic,\n  isPrivate,\n  metadataMap,\n  initializers,\n) {\n  var decs = decInfo[0];\n\n  var desc, initializer, prefix, value;\n\n  if (isPrivate) {\n    if (kind === 0 /* FIELD */ || kind === 1 /* ACCESSOR */) {\n      desc = {\n        get: decInfo[3],\n        set: decInfo[4],\n      };\n      prefix = \"get\";\n    } else if (kind === 3 /* GETTER */) {\n      desc = {\n        get: decInfo[3],\n      };\n      prefix = \"get\";\n    } else if (kind === 4 /* SETTER */) {\n      desc = {\n        set: decInfo[3],\n      };\n      prefix = \"set\";\n    } else {\n      desc = {\n        value: decInfo[3],\n      };\n    }\n    if (kind !== 0 /* FIELD */) {\n      if (kind === 1 /* ACCESSOR */) {\n        setFunctionName(decInfo[4], \"#\" + name, \"set\");\n      }\n      setFunctionName(decInfo[3], \"#\" + name, prefix);\n    }\n  } else if (kind !== 0 /* FIELD */) {\n    desc = Object.getOwnPropertyDescriptor(base, name);\n  }\n\n  if (kind === 1 /* ACCESSOR */) {\n    value = {\n      get: desc.get,\n      set: desc.set,\n    };\n  } else if (kind === 2 /* METHOD */) {\n    value = desc.value;\n  } else if (kind === 3 /* GETTER */) {\n    value = desc.get;\n  } else if (kind === 4 /* SETTER */) {\n    value = desc.set;\n  }\n\n  var newValue, get, set;\n\n  if (typeof decs === \"function\") {\n    newValue = old_memberDec(\n      decs,\n      name,\n      desc,\n      metadataMap,\n      initializers,\n      kind,\n      isStatic,\n      isPrivate,\n      value,\n    );\n\n    if (newValue !== void 0) {\n      old_assertValidReturnValue(kind, newValue);\n\n      if (kind === 0 /* FIELD */) {\n        initializer = newValue;\n      } else if (kind === 1 /* ACCESSOR */) {\n        initializer = old_getInit(newValue);\n        get = newValue.get || value.get;\n        set = newValue.set || value.set;\n\n        value = { get: get, set: set };\n      } else {\n        value = newValue;\n      }\n    }\n  } else {\n    for (var i = decs.length - 1; i >= 0; i--) {\n      var dec = decs[i];\n\n      newValue = old_memberDec(\n        dec,\n        name,\n        desc,\n        metadataMap,\n        initializers,\n        kind,\n        isStatic,\n        isPrivate,\n        value,\n      );\n\n      if (newValue !== void 0) {\n        old_assertValidReturnValue(kind, newValue);\n        var newInit;\n\n        if (kind === 0 /* FIELD */) {\n          newInit = newValue;\n        } else if (kind === 1 /* ACCESSOR */) {\n          newInit = old_getInit(newValue);\n          get = newValue.get || value.get;\n          set = newValue.set || value.set;\n\n          value = { get: get, set: set };\n        } else {\n          value = newValue;\n        }\n\n        if (newInit !== void 0) {\n          if (initializer === void 0) {\n            initializer = newInit;\n          } else if (typeof initializer === \"function\") {\n            initializer = [initializer, newInit];\n          } else {\n            initializer.push(newInit);\n          }\n        }\n      }\n    }\n  }\n\n  if (kind === 0 /* FIELD */ || kind === 1 /* ACCESSOR */) {\n    if (initializer === void 0) {\n      // If the initializer was void 0, sub in a dummy initializer\n      initializer = function (instance, init) {\n        return init;\n      };\n    } else if (typeof initializer !== \"function\") {\n      var ownInitializers = initializer;\n\n      initializer = function (instance, init) {\n        var value = init;\n\n        for (var i = 0; i < ownInitializers.length; i++) {\n          value = ownInitializers[i].call(instance, value);\n        }\n\n        return value;\n      };\n    } else {\n      var originalInitializer = initializer;\n\n      initializer = function (instance, init) {\n        return originalInitializer.call(instance, init);\n      };\n    }\n\n    ret.push(initializer);\n  }\n\n  if (kind !== 0 /* FIELD */) {\n    if (kind === 1 /* ACCESSOR */) {\n      desc.get = value.get;\n      desc.set = value.set;\n    } else if (kind === 2 /* METHOD */) {\n      desc.value = value;\n    } else if (kind === 3 /* GETTER */) {\n      desc.get = value;\n    } else if (kind === 4 /* SETTER */) {\n      desc.set = value;\n    }\n\n    if (isPrivate) {\n      if (kind === 1 /* ACCESSOR */) {\n        ret.push(function (instance, args) {\n          return value.get.call(instance, args);\n        });\n        ret.push(function (instance, args) {\n          return value.set.call(instance, args);\n        });\n      } else if (kind === 2 /* METHOD */) {\n        ret.push(value);\n      } else {\n        ret.push(function (instance, args) {\n          return value.call(instance, args);\n        });\n      }\n    } else {\n      Object.defineProperty(base, name, desc);\n    }\n  }\n}\n\nfunction old_applyMemberDecs(\n  ret,\n  Class,\n  protoMetadataMap,\n  staticMetadataMap,\n  decInfos,\n) {\n  var protoInitializers;\n  var staticInitializers;\n\n  var existingProtoNonFields = new Map();\n  var existingStaticNonFields = new Map();\n\n  for (var i = 0; i < decInfos.length; i++) {\n    var decInfo = decInfos[i];\n\n    // skip computed property names\n    if (!Array.isArray(decInfo)) continue;\n\n    var kind = decInfo[1];\n    var name = decInfo[2];\n    var isPrivate = decInfo.length > 3;\n\n    var isStatic = kind >= 5; /* STATIC */\n    var base;\n    var metadataMap;\n    var initializers;\n\n    if (isStatic) {\n      base = Class;\n      metadataMap = staticMetadataMap;\n      kind = kind - 5 /* STATIC */;\n      // initialize staticInitializers when we see a non-field static member\n      if (kind !== 0 /* FIELD */) {\n        staticInitializers = staticInitializers || [];\n        initializers = staticInitializers;\n      }\n    } else {\n      base = Class.prototype;\n      metadataMap = protoMetadataMap;\n      // initialize protoInitializers when we see a non-field member\n      if (kind !== 0 /* FIELD */) {\n        protoInitializers = protoInitializers || [];\n        initializers = protoInitializers;\n      }\n    }\n\n    if (kind !== 0 /* FIELD */ && !isPrivate) {\n      var existingNonFields = isStatic\n        ? existingStaticNonFields\n        : existingProtoNonFields;\n\n      var existingKind = existingNonFields.get(name) || 0;\n\n      if (\n        existingKind === true ||\n        (existingKind === 3 /* GETTER */ && kind !== 4) /* SETTER */ ||\n        (existingKind === 4 /* SETTER */ && kind !== 3) /* GETTER */\n      ) {\n        throw new Error(\n          \"Attempted to decorate a public method/accessor that has the same name as a previously decorated public method/accessor. This is not currently supported by the decorators plugin. Property name was: \" +\n            name,\n        );\n      } else if (!existingKind && kind > 2 /* METHOD */) {\n        existingNonFields.set(name, kind);\n      } else {\n        existingNonFields.set(name, true);\n      }\n    }\n\n    old_applyMemberDec(\n      ret,\n      base,\n      decInfo,\n      name,\n      kind,\n      isStatic,\n      isPrivate,\n      metadataMap,\n      initializers,\n    );\n  }\n\n  old_pushInitializers(ret, protoInitializers);\n  old_pushInitializers(ret, staticInitializers);\n}\n\nfunction old_pushInitializers(ret, initializers) {\n  if (initializers) {\n    ret.push(function (instance) {\n      for (var i = 0; i < initializers.length; i++) {\n        initializers[i].call(instance);\n      }\n      return instance;\n    });\n  }\n}\n\nfunction old_applyClassDecs(ret, targetClass, metadataMap, classDecs) {\n  if (classDecs.length > 0) {\n    var initializers = [];\n    var newClass = targetClass;\n    var name = targetClass.name;\n\n    for (var i = classDecs.length - 1; i >= 0; i--) {\n      var decoratorFinishedRef = { v: false };\n\n      try {\n        var ctx = Object.assign(\n          {\n            kind: \"class\",\n            name: name,\n            addInitializer: old_createAddInitializerMethod(\n              initializers,\n              decoratorFinishedRef,\n            ),\n          },\n          old_createMetadataMethodsForProperty(\n            metadataMap,\n            0 /* CONSTRUCTOR */,\n            name,\n            decoratorFinishedRef,\n          ),\n        );\n        var nextNewClass = classDecs[i](newClass, ctx);\n      } finally {\n        decoratorFinishedRef.v = true;\n      }\n\n      if (nextNewClass !== undefined) {\n        old_assertValidReturnValue(10 /* CLASS */, nextNewClass);\n        newClass = nextNewClass;\n      }\n    }\n\n    ret.push(newClass, function () {\n      for (var i = 0; i < initializers.length; i++) {\n        initializers[i].call(newClass);\n      }\n    });\n  }\n}\n\n/**\n  Basic usage:\n\n  applyDecs(\n    Class,\n    [\n      // member decorators\n      [\n        dec,                // dec or array of decs\n        0,                  // kind of value being decorated\n        'prop',             // name of public prop on class containing the value being decorated,\n        '#p',               // the name of the private property (if is private, void 0 otherwise),\n      ]\n    ],\n    [\n      // class decorators\n      dec1, dec2\n    ]\n  )\n  ```\n\n  Fully transpiled example:\n\n  ```js\n  @dec\n  class Class {\n    @dec\n    a = 123;\n\n    @dec\n    #a = 123;\n\n    @dec\n    @dec2\n    accessor b = 123;\n\n    @dec\n    accessor #b = 123;\n\n    @dec\n    c() { console.log('c'); }\n\n    @dec\n    #c() { console.log('privC'); }\n\n    @dec\n    get d() { console.log('d'); }\n\n    @dec\n    get #d() { console.log('privD'); }\n\n    @dec\n    set e(v) { console.log('e'); }\n\n    @dec\n    set #e(v) { console.log('privE'); }\n  }\n\n\n  // becomes\n  let initializeInstance;\n  let initializeClass;\n\n  let initA;\n  let initPrivA;\n\n  let initB;\n  let initPrivB, getPrivB, setPrivB;\n\n  let privC;\n  let privD;\n  let privE;\n\n  let Class;\n  class _Class {\n    static {\n      let ret = applyDecs(\n        this,\n        [\n          [dec, 0, 'a'],\n          [dec, 0, 'a', (i) => i.#a, (i, v) => i.#a = v],\n          [[dec, dec2], 1, 'b'],\n          [dec, 1, 'b', (i) => i.#privBData, (i, v) => i.#privBData = v],\n          [dec, 2, 'c'],\n          [dec, 2, 'c', () => console.log('privC')],\n          [dec, 3, 'd'],\n          [dec, 3, 'd', () => console.log('privD')],\n          [dec, 4, 'e'],\n          [dec, 4, 'e', () => console.log('privE')],\n        ],\n        [\n          dec\n        ]\n      )\n\n      initA = ret[0];\n\n      initPrivA = ret[1];\n\n      initB = ret[2];\n\n      initPrivB = ret[3];\n      getPrivB = ret[4];\n      setPrivB = ret[5];\n\n      privC = ret[6];\n\n      privD = ret[7];\n\n      privE = ret[8];\n\n      initializeInstance = ret[9];\n\n      Class = ret[10]\n\n      initializeClass = ret[11];\n    }\n\n    a = (initializeInstance(this), initA(this, 123));\n\n    #a = initPrivA(this, 123);\n\n    #bData = initB(this, 123);\n    get b() { return this.#bData }\n    set b(v) { this.#bData = v }\n\n    #privBData = initPrivB(this, 123);\n    get #b() { return getPrivB(this); }\n    set #b(v) { setPrivB(this, v); }\n\n    c() { console.log('c'); }\n\n    #c(...args) { return privC(this, ...args) }\n\n    get d() { console.log('d'); }\n\n    get #d() { return privD(this); }\n\n    set e(v) { console.log('e'); }\n\n    set #e(v) { privE(this, v); }\n  }\n\n  initializeClass(Class);\n */\nexport default function applyDecs(targetClass, memberDecs, classDecs) {\n  var ret = [];\n  var staticMetadataMap = {};\n\n  var protoMetadataMap = {};\n\n  old_applyMemberDecs(\n    ret,\n    targetClass,\n    protoMetadataMap,\n    staticMetadataMap,\n    memberDecs,\n  );\n\n  old_convertMetadataMapToFinal(targetClass.prototype, protoMetadataMap);\n\n  old_applyClassDecs(ret, targetClass, staticMetadataMap, classDecs);\n\n  old_convertMetadataMapToFinal(targetClass, staticMetadataMap);\n\n  return ret;\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,gBAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AAwBA,SAASE,oCAAoCA,CAC3CC,WAAW,EACXC,IAAI,EACJC,QAAQ,EACRC,oBAAoB,EACpB;EACA,OAAO;IACLC,WAAW,EAAE,SAAAA,CAAUC,GAAG,EAAE;MAC1BC,qBAAqB,CAACH,oBAAoB,EAAE,aAAa,CAAC;MAC1DI,qBAAqB,CAACF,GAAG,CAAC;MAE1B,IAAIG,cAAc,GAAGR,WAAW,CAACK,GAAG,CAAC;MAErC,IAAIG,cAAc,KAAK,KAAK,CAAC,EAAE,OAAO,KAAK,CAAC;MAE5C,IAAIP,IAAI,KAAK,CAAC,EAAe;QAC3B,IAAIQ,GAAG,GAAGD,cAAc,CAACE,MAAM;QAC/B,IAAID,GAAG,KAAK,KAAK,CAAC,EAAE;UAClB,OAAOA,GAAG,CAACP,QAAQ,CAAC;QACtB;MACF,CAAC,MAAM,IAAID,IAAI,KAAK,CAAC,EAAgB;QACnC,IAAIU,IAAI,GAAGH,cAAc,CAACI,OAAO;QACjC,IAAID,IAAI,KAAK,KAAK,CAAC,EAAE;UACnB,OAAOA,IAAI,CAACE,GAAG,CAACX,QAAQ,CAAC;QAC3B;MACF,CAAC,MAAM,IAAIY,MAAM,CAACC,cAAc,CAACC,IAAI,CAACR,cAAc,EAAE,aAAa,CAAC,EAAE;QACpE,OAAOA,cAAc,CAACS,WAAW;MACnC;IACF,CAAC;IACDC,WAAW,EAAE,SAAAA,CAAUb,GAAG,EAAEc,KAAK,EAAE;MACjCb,qBAAqB,CAACH,oBAAoB,EAAE,aAAa,CAAC;MAC1DI,qBAAqB,CAACF,GAAG,CAAC;MAE1B,IAAIG,cAAc,GAAGR,WAAW,CAACK,GAAG,CAAC;MAErC,IAAIG,cAAc,KAAK,KAAK,CAAC,EAAE;QAC7BA,cAAc,GAAGR,WAAW,CAACK,GAAG,CAAC,GAAG,CAAC,CAAC;MACxC;MAEA,IAAIJ,IAAI,KAAK,CAAC,EAAe;QAC3B,IAAIQ,GAAG,GAAGD,cAAc,CAACE,MAAM;QAE/B,IAAID,GAAG,KAAK,KAAK,CAAC,EAAE;UAClBA,GAAG,GAAGD,cAAc,CAACE,MAAM,GAAG,CAAC,CAAC;QAClC;QAEAD,GAAG,CAACP,QAAQ,CAAC,GAAGiB,KAAK;MACvB,CAAC,MAAM,IAAIlB,IAAI,KAAK,CAAC,EAAgB;QACnC,IAAIU,IAAI,GAAGH,cAAc,CAACG,IAAI;QAE9B,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;UACnBA,IAAI,GAAGH,cAAc,CAACI,OAAO,GAAG,IAAIQ,GAAG,CAAC,CAAC;QAC3C;QAEAT,IAAI,CAACU,GAAG,CAACnB,QAAQ,EAAEiB,KAAK,CAAC;MAC3B,CAAC,MAAM;QACLX,cAAc,CAACS,WAAW,GAAGE,KAAK;MACpC;IACF;EACF,CAAC;AACH;AAEA,SAASG,6BAA6BA,CAACC,GAAG,EAAEvB,WAAW,EAAE;EACvD,IAAIwB,iBAAiB,GAAGD,GAAG,CAACE,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACE,GAAG,CAAC,iBAAiB,CAAC,CAAC;EAC7E,IAAIC,YAAY,GAAGd,MAAM,CAACe,qBAAqB,CAAC7B,WAAW,CAAC;EAE5D,IAAI4B,YAAY,CAACE,MAAM,KAAK,CAAC,EAAE;EAE/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,YAAY,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;IAC5C,IAAI1B,GAAG,GAAGuB,YAAY,CAACG,CAAC,CAAC;IACzB,IAAIC,UAAU,GAAGhC,WAAW,CAACK,GAAG,CAAC;IACjC,IAAI4B,gBAAgB,GAAGT,iBAAiB,GAAGA,iBAAiB,CAACnB,GAAG,CAAC,GAAG,IAAI;IAExE,IAAII,GAAG,GAAGuB,UAAU,CAACtB,MAAM;IAC3B,IAAIwB,SAAS,GAAGD,gBAAgB,GAAGA,gBAAgB,CAACvB,MAAM,GAAG,IAAI;IAEjE,IAAID,GAAG,IAAIyB,SAAS,EAAE;MACpBpB,MAAM,CAACqB,cAAc,CAAC1B,GAAG,EAAEyB,SAAS,CAAC;IACvC;IAEA,IAAIvB,IAAI,GAAGqB,UAAU,CAACpB,OAAO;IAE7B,IAAID,IAAI,EAAE;MACR,IAAIyB,OAAO,GAAGC,KAAK,CAACC,IAAI,CAAC3B,IAAI,CAAC4B,MAAM,CAAC,CAAC,CAAC;MACvC,IAAIC,UAAU,GAAGP,gBAAgB,GAAGA,gBAAgB,CAACrB,OAAO,GAAG,IAAI;MAEnE,IAAI4B,UAAU,EAAE;QACdJ,OAAO,GAAGA,OAAO,CAACK,MAAM,CAACD,UAAU,CAAC;MACtC;MAEAR,UAAU,CAACpB,OAAO,GAAGwB,OAAO;IAC9B;IAEA,IAAIH,gBAAgB,EAAE;MACpBnB,MAAM,CAACqB,cAAc,CAACH,UAAU,EAAEC,gBAAgB,CAAC;IACrD;EACF;EAEA,IAAIT,iBAAiB,EAAE;IACrBV,MAAM,CAACqB,cAAc,CAACnC,WAAW,EAAEwB,iBAAiB,CAAC;EACvD;EAEAD,GAAG,CAACE,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACE,GAAG,CAAC,iBAAiB,CAAC,CAAC,GAAG3B,WAAW;AACrE;AAEA,SAAS0C,8BAA8BA,CAACC,YAAY,EAAExC,oBAAoB,EAAE;EAC1E,OAAO,SAASyC,cAAcA,CAACC,WAAW,EAAE;IAC1CvC,qBAAqB,CAACH,oBAAoB,EAAE,gBAAgB,CAAC;IAC7D2C,kBAAkB,CAACD,WAAW,EAAE,gBAAgB,CAAC;IACjDF,YAAY,CAACI,IAAI,CAACF,WAAW,CAAC;EAChC,CAAC;AACH;AAEA,SAASG,aAAaA,CACpBC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJnD,WAAW,EACX2C,YAAY,EACZ1C,IAAI,EACJmD,QAAQ,EACRC,SAAS,EACTlC,KAAK,EACL;EACA,IAAImC,OAAO;EAEX,QAAQrD,IAAI;IACV,KAAK,CAAC;MACJqD,OAAO,GAAG,UAAU;MACpB;IACF,KAAK,CAAC;MACJA,OAAO,GAAG,QAAQ;MAClB;IACF,KAAK,CAAC;MACJA,OAAO,GAAG,QAAQ;MAClB;IACF,KAAK,CAAC;MACJA,OAAO,GAAG,QAAQ;MAClB;IACF;MACEA,OAAO,GAAG,OAAO;EACrB;EAEA,IAAIC,GAAG,GAAG;IACRtD,IAAI,EAAEqD,OAAO;IACbJ,IAAI,EAAEG,SAAS,GAAG,GAAG,GAAGH,IAAI,GAAGM,cAAa,CAACN,IAAI,CAAC;IAClDE,QAAQ,EAAEA,QAAQ;IAClBC,SAAS,EAAEA;EACb,CAAC;EAED,IAAIlD,oBAAoB,GAAG;IAAEsD,CAAC,EAAE;EAAM,CAAC;EAEvC,IAAIxD,IAAI,KAAK,CAAC,EAAc;IAC1BsD,GAAG,CAACX,cAAc,GAAGF,8BAA8B,CACjDC,YAAY,EACZxC,oBACF,CAAC;EACH;EAEA,IAAIuD,YAAY,EAAEC,YAAY;EAE9B,IAAIN,SAAS,EAAE;IACbK,YAAY,GAAG,CAAC;IAChBC,YAAY,GAAGlC,MAAM,CAACyB,IAAI,CAAC;IAE3B,IAAIU,MAAM,GAAG,CAAC,CAAC;IAEf,IAAI3D,IAAI,KAAK,CAAC,EAAc;MAC1B2D,MAAM,CAAC/C,GAAG,GAAGsC,IAAI,CAACtC,GAAG;MACrB+C,MAAM,CAACvC,GAAG,GAAG8B,IAAI,CAAC9B,GAAG;IACvB,CAAC,MAAM,IAAIpB,IAAI,KAAK,CAAC,EAAe;MAClC2D,MAAM,CAAC/C,GAAG,GAAG,YAAY;QACvB,OAAOsC,IAAI,CAAChC,KAAK;MACnB,CAAC;IACH,CAAC,MAAM;MAEL,IAAIlB,IAAI,KAAK,CAAC,IAAmBA,IAAI,KAAK,CAAC,EAAe;QACxD2D,MAAM,CAAC/C,GAAG,GAAG,YAAY;UACvB,OAAOsC,IAAI,CAACtC,GAAG,CAACG,IAAI,CAAC,IAAI,CAAC;QAC5B,CAAC;MACH;MAEA,IAAIf,IAAI,KAAK,CAAC,IAAmBA,IAAI,KAAK,CAAC,EAAe;QACxD2D,MAAM,CAACvC,GAAG,GAAG,UAAUoC,CAAC,EAAE;UACxBN,IAAI,CAAC9B,GAAG,CAACL,IAAI,CAAC,IAAI,EAAEyC,CAAC,CAAC;QACxB,CAAC;MACH;IACF;IAEAF,GAAG,CAACK,MAAM,GAAGA,MAAM;EACrB,CAAC,MAAM;IACLF,YAAY,GAAG,CAAC;IAChBC,YAAY,GAAGT,IAAI;EACrB;EAEA,IAAI;IACF,OAAOD,GAAG,CACR9B,KAAK,EACLL,MAAM,CAAC+C,MAAM,CACXN,GAAG,EACHxD,oCAAoC,CAClCC,WAAW,EACX0D,YAAY,EACZC,YAAY,EACZxD,oBACF,CACF,CACF,CAAC;EACH,CAAC,SAAS;IACRA,oBAAoB,CAACsD,CAAC,GAAG,IAAI;EAC/B;AACF;AAEA,SAASnD,qBAAqBA,CAACH,oBAAoB,EAAE2D,MAAM,EAAE;EAC3D,IAAI3D,oBAAoB,CAACsD,CAAC,EAAE;IAC1B,MAAM,IAAIM,KAAK,CACb,oBAAoB,GAAGD,MAAM,GAAG,gCAClC,CAAC;EACH;AACF;AAEA,SAASvD,qBAAqBA,CAACF,GAAG,EAAE;EAClC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC3B,MAAM,IAAI2D,SAAS,CAAC,2CAA2C,GAAG3D,GAAG,CAAC;EACxE;AACF;AAEA,SAASyC,kBAAkBA,CAACmB,EAAE,EAAEC,IAAI,EAAE;EACpC,IAAI,OAAOD,EAAE,KAAK,UAAU,EAAE;IAC5B,MAAM,IAAID,SAAS,CAACE,IAAI,GAAG,qBAAqB,CAAC;EACnD;AACF;AAEA,SAASC,0BAA0BA,CAAClE,IAAI,EAAEkB,KAAK,EAAE;EAC/C,IAAIiD,IAAI,GAAG,OAAOjD,KAAK;EAEvB,IAAIlB,IAAI,KAAK,CAAC,EAAiB;IAC7B,IAAImE,IAAI,KAAK,QAAQ,IAAIjD,KAAK,KAAK,IAAI,EAAE;MACvC,MAAM,IAAI6C,SAAS,CACjB,uFACF,CAAC;IACH;IACA,IAAI7C,KAAK,CAACN,GAAG,KAAKwD,SAAS,EAAE;MAC3BvB,kBAAkB,CAAC3B,KAAK,CAACN,GAAG,EAAE,cAAc,CAAC;IAC/C;IACA,IAAIM,KAAK,CAACE,GAAG,KAAKgD,SAAS,EAAE;MAC3BvB,kBAAkB,CAAC3B,KAAK,CAACE,GAAG,EAAE,cAAc,CAAC;IAC/C;IACA,IAAIF,KAAK,CAACmD,IAAI,KAAKD,SAAS,EAAE;MAC5BvB,kBAAkB,CAAC3B,KAAK,CAACmD,IAAI,EAAE,eAAe,CAAC;IACjD;IACA,IAAInD,KAAK,CAAC0B,WAAW,KAAKwB,SAAS,EAAE;MACnCvB,kBAAkB,CAAC3B,KAAK,CAAC0B,WAAW,EAAE,sBAAsB,CAAC;IAC/D;EACF,CAAC,MAAM,IAAIuB,IAAI,KAAK,UAAU,EAAE;IAC9B,IAAIF,IAAI;IACR,IAAIjE,IAAI,KAAK,CAAC,EAAc;MAC1BiE,IAAI,GAAG,OAAO;IAChB,CAAC,MAAM,IAAIjE,IAAI,KAAK,EAAE,EAAc;MAClCiE,IAAI,GAAG,OAAO;IAChB,CAAC,MAAM;MACLA,IAAI,GAAG,QAAQ;IACjB;IACA,MAAM,IAAIF,SAAS,CAACE,IAAI,GAAG,8CAA8C,CAAC;EAC5E;AACF;AAEA,SAASK,WAAWA,CAACpB,IAAI,EAAE;EACzB,IAAIN,WAAW;EACf,IACE,CAACA,WAAW,GAAGM,IAAI,CAACmB,IAAI,KAAK,IAAI,KAChCzB,WAAW,GAAGM,IAAI,CAACN,WAAW,CAAC,IAChC,OAAO2B,OAAO,KAAK,WAAW,EAC9B;IACAA,OAAO,CAACC,IAAI,CAAC,yDAAyD,CAAC;EACzE;EACA,OAAO5B,WAAW;AACpB;AAEA,SAAS6B,kBAAkBA,CACzBC,GAAG,EACHC,IAAI,EACJC,OAAO,EACP3B,IAAI,EACJjD,IAAI,EACJmD,QAAQ,EACRC,SAAS,EACTrD,WAAW,EACX2C,YAAY,EACZ;EACA,IAAImC,IAAI,GAAGD,OAAO,CAAC,CAAC,CAAC;EAErB,IAAI1B,IAAI,EAAEN,WAAW,EAAEkC,MAAM,EAAE5D,KAAK;EAEpC,IAAIkC,SAAS,EAAE;IACb,IAAIpD,IAAI,KAAK,CAAC,IAAgBA,IAAI,KAAK,CAAC,EAAiB;MACvDkD,IAAI,GAAG;QACLtC,GAAG,EAAEgE,OAAO,CAAC,CAAC,CAAC;QACfxD,GAAG,EAAEwD,OAAO,CAAC,CAAC;MAChB,CAAC;MACDE,MAAM,GAAG,KAAK;IAChB,CAAC,MAAM,IAAI9E,IAAI,KAAK,CAAC,EAAe;MAClCkD,IAAI,GAAG;QACLtC,GAAG,EAAEgE,OAAO,CAAC,CAAC;MAChB,CAAC;MACDE,MAAM,GAAG,KAAK;IAChB,CAAC,MAAM,IAAI9E,IAAI,KAAK,CAAC,EAAe;MAClCkD,IAAI,GAAG;QACL9B,GAAG,EAAEwD,OAAO,CAAC,CAAC;MAChB,CAAC;MACDE,MAAM,GAAG,KAAK;IAChB,CAAC,MAAM;MACL5B,IAAI,GAAG;QACLhC,KAAK,EAAE0D,OAAO,CAAC,CAAC;MAClB,CAAC;IACH;IACA,IAAI5E,IAAI,KAAK,CAAC,EAAc;MAC1B,IAAIA,IAAI,KAAK,CAAC,EAAiB;QAC7B+E,gBAAe,CAACH,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG3B,IAAI,EAAE,KAAK,CAAC;MAChD;MACA8B,gBAAe,CAACH,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG3B,IAAI,EAAE6B,MAAM,CAAC;IACjD;EACF,CAAC,MAAM,IAAI9E,IAAI,KAAK,CAAC,EAAc;IACjCkD,IAAI,GAAGrC,MAAM,CAACmE,wBAAwB,CAACL,IAAI,EAAE1B,IAAI,CAAC;EACpD;EAEA,IAAIjD,IAAI,KAAK,CAAC,EAAiB;IAC7BkB,KAAK,GAAG;MACNN,GAAG,EAAEsC,IAAI,CAACtC,GAAG;MACbQ,GAAG,EAAE8B,IAAI,CAAC9B;IACZ,CAAC;EACH,CAAC,MAAM,IAAIpB,IAAI,KAAK,CAAC,EAAe;IAClCkB,KAAK,GAAGgC,IAAI,CAAChC,KAAK;EACpB,CAAC,MAAM,IAAIlB,IAAI,KAAK,CAAC,EAAe;IAClCkB,KAAK,GAAGgC,IAAI,CAACtC,GAAG;EAClB,CAAC,MAAM,IAAIZ,IAAI,KAAK,CAAC,EAAe;IAClCkB,KAAK,GAAGgC,IAAI,CAAC9B,GAAG;EAClB;EAEA,IAAI6D,QAAQ,EAAErE,GAAG,EAAEQ,GAAG;EAEtB,IAAI,OAAOyD,IAAI,KAAK,UAAU,EAAE;IAC9BI,QAAQ,GAAGlC,aAAa,CACtB8B,IAAI,EACJ5B,IAAI,EACJC,IAAI,EACJnD,WAAW,EACX2C,YAAY,EACZ1C,IAAI,EACJmD,QAAQ,EACRC,SAAS,EACTlC,KACF,CAAC;IAED,IAAI+D,QAAQ,KAAK,KAAK,CAAC,EAAE;MACvBf,0BAA0B,CAAClE,IAAI,EAAEiF,QAAQ,CAAC;MAE1C,IAAIjF,IAAI,KAAK,CAAC,EAAc;QAC1B4C,WAAW,GAAGqC,QAAQ;MACxB,CAAC,MAAM,IAAIjF,IAAI,KAAK,CAAC,EAAiB;QACpC4C,WAAW,GAAG0B,WAAW,CAACW,QAAQ,CAAC;QACnCrE,GAAG,GAAGqE,QAAQ,CAACrE,GAAG,IAAIM,KAAK,CAACN,GAAG;QAC/BQ,GAAG,GAAG6D,QAAQ,CAAC7D,GAAG,IAAIF,KAAK,CAACE,GAAG;QAE/BF,KAAK,GAAG;UAAEN,GAAG,EAAEA,GAAG;UAAEQ,GAAG,EAAEA;QAAI,CAAC;MAChC,CAAC,MAAM;QACLF,KAAK,GAAG+D,QAAQ;MAClB;IACF;EACF,CAAC,MAAM;IACL,KAAK,IAAInD,CAAC,GAAG+C,IAAI,CAAChD,MAAM,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACzC,IAAIkB,GAAG,GAAG6B,IAAI,CAAC/C,CAAC,CAAC;MAEjBmD,QAAQ,GAAGlC,aAAa,CACtBC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJnD,WAAW,EACX2C,YAAY,EACZ1C,IAAI,EACJmD,QAAQ,EACRC,SAAS,EACTlC,KACF,CAAC;MAED,IAAI+D,QAAQ,KAAK,KAAK,CAAC,EAAE;QACvBf,0BAA0B,CAAClE,IAAI,EAAEiF,QAAQ,CAAC;QAC1C,IAAIC,OAAO;QAEX,IAAIlF,IAAI,KAAK,CAAC,EAAc;UAC1BkF,OAAO,GAAGD,QAAQ;QACpB,CAAC,MAAM,IAAIjF,IAAI,KAAK,CAAC,EAAiB;UACpCkF,OAAO,GAAGZ,WAAW,CAACW,QAAQ,CAAC;UAC/BrE,GAAG,GAAGqE,QAAQ,CAACrE,GAAG,IAAIM,KAAK,CAACN,GAAG;UAC/BQ,GAAG,GAAG6D,QAAQ,CAAC7D,GAAG,IAAIF,KAAK,CAACE,GAAG;UAE/BF,KAAK,GAAG;YAAEN,GAAG,EAAEA,GAAG;YAAEQ,GAAG,EAAEA;UAAI,CAAC;QAChC,CAAC,MAAM;UACLF,KAAK,GAAG+D,QAAQ;QAClB;QAEA,IAAIC,OAAO,KAAK,KAAK,CAAC,EAAE;UACtB,IAAItC,WAAW,KAAK,KAAK,CAAC,EAAE;YAC1BA,WAAW,GAAGsC,OAAO;UACvB,CAAC,MAAM,IAAI,OAAOtC,WAAW,KAAK,UAAU,EAAE;YAC5CA,WAAW,GAAG,CAACA,WAAW,EAAEsC,OAAO,CAAC;UACtC,CAAC,MAAM;YACLtC,WAAW,CAACE,IAAI,CAACoC,OAAO,CAAC;UAC3B;QACF;MACF;IACF;EACF;EAEA,IAAIlF,IAAI,KAAK,CAAC,IAAgBA,IAAI,KAAK,CAAC,EAAiB;IACvD,IAAI4C,WAAW,KAAK,KAAK,CAAC,EAAE;MAE1BA,WAAW,GAAG,SAAAA,CAAUuC,QAAQ,EAAEd,IAAI,EAAE;QACtC,OAAOA,IAAI;MACb,CAAC;IACH,CAAC,MAAM,IAAI,OAAOzB,WAAW,KAAK,UAAU,EAAE;MAC5C,IAAIwC,eAAe,GAAGxC,WAAW;MAEjCA,WAAW,GAAG,SAAAA,CAAUuC,QAAQ,EAAEd,IAAI,EAAE;QACtC,IAAInD,KAAK,GAAGmD,IAAI;QAEhB,KAAK,IAAIvC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsD,eAAe,CAACvD,MAAM,EAAEC,CAAC,EAAE,EAAE;UAC/CZ,KAAK,GAAGkE,eAAe,CAACtD,CAAC,CAAC,CAACf,IAAI,CAACoE,QAAQ,EAAEjE,KAAK,CAAC;QAClD;QAEA,OAAOA,KAAK;MACd,CAAC;IACH,CAAC,MAAM;MACL,IAAImE,mBAAmB,GAAGzC,WAAW;MAErCA,WAAW,GAAG,SAAAA,CAAUuC,QAAQ,EAAEd,IAAI,EAAE;QACtC,OAAOgB,mBAAmB,CAACtE,IAAI,CAACoE,QAAQ,EAAEd,IAAI,CAAC;MACjD,CAAC;IACH;IAEAK,GAAG,CAAC5B,IAAI,CAACF,WAAW,CAAC;EACvB;EAEA,IAAI5C,IAAI,KAAK,CAAC,EAAc;IAC1B,IAAIA,IAAI,KAAK,CAAC,EAAiB;MAC7BkD,IAAI,CAACtC,GAAG,GAAGM,KAAK,CAACN,GAAG;MACpBsC,IAAI,CAAC9B,GAAG,GAAGF,KAAK,CAACE,GAAG;IACtB,CAAC,MAAM,IAAIpB,IAAI,KAAK,CAAC,EAAe;MAClCkD,IAAI,CAAChC,KAAK,GAAGA,KAAK;IACpB,CAAC,MAAM,IAAIlB,IAAI,KAAK,CAAC,EAAe;MAClCkD,IAAI,CAACtC,GAAG,GAAGM,KAAK;IAClB,CAAC,MAAM,IAAIlB,IAAI,KAAK,CAAC,EAAe;MAClCkD,IAAI,CAAC9B,GAAG,GAAGF,KAAK;IAClB;IAEA,IAAIkC,SAAS,EAAE;MACb,IAAIpD,IAAI,KAAK,CAAC,EAAiB;QAC7B0E,GAAG,CAAC5B,IAAI,CAAC,UAAUqC,QAAQ,EAAEG,IAAI,EAAE;UACjC,OAAOpE,KAAK,CAACN,GAAG,CAACG,IAAI,CAACoE,QAAQ,EAAEG,IAAI,CAAC;QACvC,CAAC,CAAC;QACFZ,GAAG,CAAC5B,IAAI,CAAC,UAAUqC,QAAQ,EAAEG,IAAI,EAAE;UACjC,OAAOpE,KAAK,CAACE,GAAG,CAACL,IAAI,CAACoE,QAAQ,EAAEG,IAAI,CAAC;QACvC,CAAC,CAAC;MACJ,CAAC,MAAM,IAAItF,IAAI,KAAK,CAAC,EAAe;QAClC0E,GAAG,CAAC5B,IAAI,CAAC5B,KAAK,CAAC;MACjB,CAAC,MAAM;QACLwD,GAAG,CAAC5B,IAAI,CAAC,UAAUqC,QAAQ,EAAEG,IAAI,EAAE;UACjC,OAAOpE,KAAK,CAACH,IAAI,CAACoE,QAAQ,EAAEG,IAAI,CAAC;QACnC,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACLzE,MAAM,CAAC0E,cAAc,CAACZ,IAAI,EAAE1B,IAAI,EAAEC,IAAI,CAAC;IACzC;EACF;AACF;AAEA,SAASsC,mBAAmBA,CAC1Bd,GAAG,EACHe,KAAK,EACLC,gBAAgB,EAChBC,iBAAiB,EACjBC,QAAQ,EACR;EACA,IAAIC,iBAAiB;EACrB,IAAIC,kBAAkB;EAEtB,IAAIC,sBAAsB,GAAG,IAAI5E,GAAG,CAAC,CAAC;EACtC,IAAI6E,uBAAuB,GAAG,IAAI7E,GAAG,CAAC,CAAC;EAEvC,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8D,QAAQ,CAAC/D,MAAM,EAAEC,CAAC,EAAE,EAAE;IACxC,IAAI8C,OAAO,GAAGgB,QAAQ,CAAC9D,CAAC,CAAC;IAGzB,IAAI,CAACM,KAAK,CAAC6D,OAAO,CAACrB,OAAO,CAAC,EAAE;IAE7B,IAAI5E,IAAI,GAAG4E,OAAO,CAAC,CAAC,CAAC;IACrB,IAAI3B,IAAI,GAAG2B,OAAO,CAAC,CAAC,CAAC;IACrB,IAAIxB,SAAS,GAAGwB,OAAO,CAAC/C,MAAM,GAAG,CAAC;IAElC,IAAIsB,QAAQ,GAAGnD,IAAI,IAAI,CAAC;IACxB,IAAI2E,IAAI;IACR,IAAI5E,WAAW;IACf,IAAI2C,YAAY;IAEhB,IAAIS,QAAQ,EAAE;MACZwB,IAAI,GAAGc,KAAK;MACZ1F,WAAW,GAAG4F,iBAAiB;MAC/B3F,IAAI,GAAGA,IAAI,GAAG,CAAC;MAEf,IAAIA,IAAI,KAAK,CAAC,EAAc;QAC1B8F,kBAAkB,GAAGA,kBAAkB,IAAI,EAAE;QAC7CpD,YAAY,GAAGoD,kBAAkB;MACnC;IACF,CAAC,MAAM;MACLnB,IAAI,GAAGc,KAAK,CAACS,SAAS;MACtBnG,WAAW,GAAG2F,gBAAgB;MAE9B,IAAI1F,IAAI,KAAK,CAAC,EAAc;QAC1B6F,iBAAiB,GAAGA,iBAAiB,IAAI,EAAE;QAC3CnD,YAAY,GAAGmD,iBAAiB;MAClC;IACF;IAEA,IAAI7F,IAAI,KAAK,CAAC,IAAgB,CAACoD,SAAS,EAAE;MACxC,IAAI+C,iBAAiB,GAAGhD,QAAQ,GAC5B6C,uBAAuB,GACvBD,sBAAsB;MAE1B,IAAIK,YAAY,GAAGD,iBAAiB,CAACvF,GAAG,CAACqC,IAAI,CAAC,IAAI,CAAC;MAEnD,IACEmD,YAAY,KAAK,IAAI,IACpBA,YAAY,KAAK,CAAC,IAAiBpG,IAAI,KAAK,CAAE,IAC9CoG,YAAY,KAAK,CAAC,IAAiBpG,IAAI,KAAK,CAAE,EAC/C;QACA,MAAM,IAAI8D,KAAK,CACb,uMAAuM,GACrMb,IACJ,CAAC;MACH,CAAC,MAAM,IAAI,CAACmD,YAAY,IAAIpG,IAAI,GAAG,CAAC,EAAe;QACjDmG,iBAAiB,CAAC/E,GAAG,CAAC6B,IAAI,EAAEjD,IAAI,CAAC;MACnC,CAAC,MAAM;QACLmG,iBAAiB,CAAC/E,GAAG,CAAC6B,IAAI,EAAE,IAAI,CAAC;MACnC;IACF;IAEAwB,kBAAkB,CAChBC,GAAG,EACHC,IAAI,EACJC,OAAO,EACP3B,IAAI,EACJjD,IAAI,EACJmD,QAAQ,EACRC,SAAS,EACTrD,WAAW,EACX2C,YACF,CAAC;EACH;EAEA2D,oBAAoB,CAAC3B,GAAG,EAAEmB,iBAAiB,CAAC;EAC5CQ,oBAAoB,CAAC3B,GAAG,EAAEoB,kBAAkB,CAAC;AAC/C;AAEA,SAASO,oBAAoBA,CAAC3B,GAAG,EAAEhC,YAAY,EAAE;EAC/C,IAAIA,YAAY,EAAE;IAChBgC,GAAG,CAAC5B,IAAI,CAAC,UAAUqC,QAAQ,EAAE;MAC3B,KAAK,IAAIrD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,YAAY,CAACb,MAAM,EAAEC,CAAC,EAAE,EAAE;QAC5CY,YAAY,CAACZ,CAAC,CAAC,CAACf,IAAI,CAACoE,QAAQ,CAAC;MAChC;MACA,OAAOA,QAAQ;IACjB,CAAC,CAAC;EACJ;AACF;AAEA,SAASmB,kBAAkBA,CAAC5B,GAAG,EAAE6B,WAAW,EAAExG,WAAW,EAAEyG,SAAS,EAAE;EACpE,IAAIA,SAAS,CAAC3E,MAAM,GAAG,CAAC,EAAE;IACxB,IAAIa,YAAY,GAAG,EAAE;IACrB,IAAI+D,QAAQ,GAAGF,WAAW;IAC1B,IAAItD,IAAI,GAAGsD,WAAW,CAACtD,IAAI;IAE3B,KAAK,IAAInB,CAAC,GAAG0E,SAAS,CAAC3E,MAAM,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC9C,IAAI5B,oBAAoB,GAAG;QAAEsD,CAAC,EAAE;MAAM,CAAC;MAEvC,IAAI;QACF,IAAIF,GAAG,GAAGzC,MAAM,CAAC+C,MAAM,CACrB;UACE5D,IAAI,EAAE,OAAO;UACbiD,IAAI,EAAEA,IAAI;UACVN,cAAc,EAAEF,8BAA8B,CAC5CC,YAAY,EACZxC,oBACF;QACF,CAAC,EACDJ,oCAAoC,CAClCC,WAAW,EACX,CAAC,EACDkD,IAAI,EACJ/C,oBACF,CACF,CAAC;QACD,IAAIwG,YAAY,GAAGF,SAAS,CAAC1E,CAAC,CAAC,CAAC2E,QAAQ,EAAEnD,GAAG,CAAC;MAChD,CAAC,SAAS;QACRpD,oBAAoB,CAACsD,CAAC,GAAG,IAAI;MAC/B;MAEA,IAAIkD,YAAY,KAAKtC,SAAS,EAAE;QAC9BF,0BAA0B,CAAC,EAAE,EAAcwC,YAAY,CAAC;QACxDD,QAAQ,GAAGC,YAAY;MACzB;IACF;IAEAhC,GAAG,CAAC5B,IAAI,CAAC2D,QAAQ,EAAE,YAAY;MAC7B,KAAK,IAAI3E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,YAAY,CAACb,MAAM,EAAEC,CAAC,EAAE,EAAE;QAC5CY,YAAY,CAACZ,CAAC,CAAC,CAACf,IAAI,CAAC0F,QAAQ,CAAC;MAChC;IACF,CAAC,CAAC;EACJ;AACF;AAmJe,SAASE,SAASA,CAACJ,WAAW,EAAEK,UAAU,EAAEJ,SAAS,EAAE;EACpE,IAAI9B,GAAG,GAAG,EAAE;EACZ,IAAIiB,iBAAiB,GAAG,CAAC,CAAC;EAE1B,IAAID,gBAAgB,GAAG,CAAC,CAAC;EAEzBF,mBAAmB,CACjBd,GAAG,EACH6B,WAAW,EACXb,gBAAgB,EAChBC,iBAAiB,EACjBiB,UACF,CAAC;EAEDvF,6BAA6B,CAACkF,WAAW,CAACL,SAAS,EAAER,gBAAgB,CAAC;EAEtEY,kBAAkB,CAAC5B,GAAG,EAAE6B,WAAW,EAAEZ,iBAAiB,EAAEa,SAAS,CAAC;EAElEnF,6BAA6B,CAACkF,WAAW,EAAEZ,iBAAiB,CAAC;EAE7D,OAAOjB,GAAG;AACZ", "ignoreList": []}