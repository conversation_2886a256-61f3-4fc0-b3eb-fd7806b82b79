"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const utils_date = require("../../utils/date.js");
if (!Math) {
  ReportCard();
}
const ReportCard = () => "../../components/ReportCard.js";
const _sfc_main = {
  __name: "merchant-activity-detail",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const activityId = common_vendor.ref("");
    const isCollected = common_vendor.ref(false);
    const showPosterModal = common_vendor.ref(false);
    const posterUrl = common_vendor.ref("");
    const activityData = common_vendor.reactive({
      id: "1",
      title: "周年庆典大优惠",
      activityType: "优惠折扣",
      merchantName: "品味餐厅",
      startDate: "2023-06-01",
      endDate: "2023-06-30",
      address: "河北省邯郸市磁县北顺城街32号",
      description: "品味餐厅两周年庆典，全场菜品8折，满200减30，满300减50，满500减100！还有精美礼品等你来拿！",
      rules: "1. 活动时间：2023年6月1日至6月30日\n2. 折扣与满减不可同时使用\n3. 特价菜品除外\n4. 每桌最多减免100元\n5. 节假日通用",
      tags: ["周年庆", "满减优惠", "有赠品"],
      images: [
        "/static/images/default-image.png",
        "/static/images/default-image.png",
        "/static/images/default-image.png"
      ],
      contact: "李经理",
      phone: "13800138000",
      wechat: "restaurant123",
      publishTime: (/* @__PURE__ */ new Date()).getTime() - 864e5,
      // 一天前
      hasRedPacket: true,
      redPacket: {
        type: "random",
        amount: 20,
        remain: 10,
        total: 20
      }
    });
    const relatedActivities = common_vendor.reactive([
      {
        id: "2",
        title: "新店开业大酬宾",
        merchantName: "鲜果超市",
        activityType: "新店开业",
        startDate: "2023-06-05",
        endDate: "2023-06-15",
        image: "/static/images/default-image.png"
      },
      {
        id: "3",
        title: "夏季特惠活动",
        merchantName: "时尚服饰",
        activityType: "限时特价",
        startDate: "2023-06-10",
        endDate: "2023-06-20",
        image: "/static/images/default-image.png"
      },
      {
        id: "4",
        title: "会员专享日",
        merchantName: "健身中心",
        activityType: "会员专享",
        startDate: "2023-06-15",
        endDate: "2023-06-16",
        image: "/static/images/default-image.png"
      }
    ]);
    const goBack = () => common_vendor.index.navigateBack();
    const previewImage = (index) => {
      common_vendor.index.previewImage({
        current: index,
        urls: activityData.images
      });
    };
    const getActivityStatusText = () => {
      const now = /* @__PURE__ */ new Date();
      const start = new Date(activityData.startDate);
      const end = new Date(activityData.endDate);
      if (now < start)
        return "未开始";
      if (now > end)
        return "已结束";
      return "进行中";
    };
    const getActivityStatusClass = () => {
      const status = getActivityStatusText();
      if (status === "进行中")
        return "status-ongoing";
      if (status === "未开始")
        return "status-not-started";
      return "status-ended";
    };
    const openLocation = () => {
      common_vendor.index.openLocation({
        latitude: 36.36,
        // 示例纬度
        longitude: 114.23,
        // 示例经度
        address: activityData.address,
        name: activityData.merchantName
      });
    };
    const callPhone = () => {
      common_vendor.index.makePhoneCall({ phoneNumber: activityData.phone });
    };
    const openRedPacket = () => {
      common_vendor.index.showToast({ title: "红包功能正在开发中", icon: "none" });
    };
    const getRedPacketConditionText = () => "点击领取";
    const navigateToActivityDetail = (id) => {
      common_vendor.index.navigateTo({ url: `/pages/publish/merchant-activity-detail?id=${id}` });
    };
    const toggleCollect = () => {
      isCollected.value = !isCollected.value;
      common_vendor.index.showToast({ title: isCollected.value ? "收藏成功" : "取消收藏" });
    };
    const shareToFriend = () => {
      common_vendor.index.share({
        provider: "weixin",
        scene: "WXSceneSession",
        type: 0,
        title: activityData.title,
        summary: activityData.description,
        imageUrl: activityData.images[0]
      });
    };
    const contactMerchant = () => {
      common_vendor.index.showActionSheet({
        itemList: ["拨打电话", "添加微信"],
        success: (res) => {
          if (res.tapIndex === 0)
            callPhone();
          if (res.tapIndex === 1) {
            common_vendor.index.setClipboardData({
              data: activityData.wechat,
              success: () => common_vendor.index.showToast({ title: "微信号已复制" })
            });
          }
        }
      });
    };
    const generateShareImage = () => {
      common_vendor.index.showLoading({ title: "海报生成中..." });
      common_vendor.index.createCanvasContext("posterCanvas");
      setTimeout(() => {
        posterUrl.value = "/static/images/default-image.png";
        showPosterModal.value = true;
        common_vendor.index.hideLoading();
      }, 1e3);
    };
    const closePosterModal = () => {
      showPosterModal.value = false;
    };
    const savePoster = () => {
      common_vendor.index.saveImageToPhotosAlbum({
        filePath: posterUrl.value,
        success: () => common_vendor.index.showToast({ title: "保存成功" }),
        fail: () => common_vendor.index.showToast({ title: "保存失败", icon: "error" })
      });
    };
    common_vendor.onLoad((options) => {
      activityId.value = options.id || "";
    });
    common_vendor.onMounted(() => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = systemInfo.statusBarHeight || 20;
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$5,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_assets._imports_10,
        e: common_vendor.o(generateShareImage),
        f: common_vendor.f(activityData.images, (image, index, i0) => {
          return {
            a: image,
            b: common_vendor.o(($event) => previewImage(index), index),
            c: index
          };
        }),
        g: common_vendor.t(activityData.title),
        h: common_vendor.t(getActivityStatusText()),
        i: common_vendor.n(getActivityStatusClass()),
        j: common_vendor.f(activityData.tags, (tag, index, i0) => {
          return {
            a: common_vendor.t(tag),
            b: index
          };
        }),
        k: common_vendor.t(common_vendor.unref(utils_date.formatTime)(activityData.publishTime)),
        l: common_vendor.t(activityData.activityType),
        m: common_vendor.t(activityData.startDate),
        n: common_vendor.t(activityData.endDate),
        o: common_vendor.t(activityData.merchantName),
        p: activityData.description,
        q: activityData.rules,
        r: common_vendor.t(activityData.address),
        s: common_vendor.o(openLocation),
        t: common_assets._imports_2$2,
        v: common_vendor.t(activityData.contact),
        w: common_vendor.t(activityData.phone),
        x: common_vendor.o(callPhone),
        y: activityData.hasRedPacket
      }, activityData.hasRedPacket ? {
        z: common_assets._imports_0$4,
        A: common_vendor.t(activityData.redPacket.type === "random" ? "随机金额红包" : "查看活动领红包"),
        B: common_vendor.t(activityData.redPacket.remain),
        C: common_vendor.t(getRedPacketConditionText()),
        D: common_vendor.t(activityData.redPacket.amount),
        E: common_vendor.o(openRedPacket)
      } : {}, {
        F: common_vendor.f(relatedActivities.slice(0, 3), (activity, index, i0) => {
          return {
            a: activity.image,
            b: common_vendor.t(activity.title),
            c: common_vendor.t(activity.merchantName),
            d: common_vendor.t(activity.activityType),
            e: common_vendor.t(activity.startDate),
            f: common_vendor.t(activity.endDate),
            g: index,
            h: common_vendor.o(($event) => navigateToActivityDetail(activity.id), index)
          };
        }),
        G: isCollected.value ? 1 : "",
        H: !isCollected.value ? 1 : "",
        I: common_vendor.t(isCollected.value ? "已收藏" : "收藏"),
        J: common_vendor.o(toggleCollect),
        K: common_vendor.o(shareToFriend),
        L: common_vendor.o(contactMerchant),
        M: showPosterModal.value
      }, showPosterModal.value ? {
        N: common_vendor.o(closePosterModal),
        O: posterUrl.value,
        P: common_vendor.o(savePoster),
        Q: common_vendor.o(() => {
        }),
        R: common_vendor.o(closePosterModal)
      } : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/publish/merchant-activity-detail.js.map
