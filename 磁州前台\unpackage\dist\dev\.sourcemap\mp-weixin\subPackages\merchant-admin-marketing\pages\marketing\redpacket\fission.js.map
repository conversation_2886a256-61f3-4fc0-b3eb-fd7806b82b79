{"version": 3, "file": "fission.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/redpacket/fission.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xyZWRwYWNrZXRcZmlzc2lvbi52dWU"], "sourcesContent": ["<template>\n  <view class=\"page-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">裂变红包</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 裂变红包数据概览 -->\n    <view class=\"overview-section\">\n      <view class=\"overview-header\">\n        <text class=\"section-title\">裂变数据概览</text>\n        <view class=\"date-picker\" @click=\"showDatePicker\">\n          <text class=\"date-text\">{{dateRange}}</text>\n          <view class=\"date-icon\"></view>\n        </view>\n      </view>\n      \n      <view class=\"stats-cards\">\n        <view class=\"stats-card\">\n          <view class=\"card-value\">{{fissionData.totalCount}}</view>\n          <view class=\"card-label\">裂变活动数</view>\n          <view class=\"card-trend\" :class=\"fissionData.countTrend\">\n            <view class=\"trend-arrow\"></view>\n            <text class=\"trend-value\">{{fissionData.countGrowth}}</text>\n          </view>\n        </view>\n        \n        <view class=\"stats-card\">\n          <view class=\"card-value\">{{fissionData.totalUsers}}</view>\n          <view class=\"card-label\">参与人数</view>\n          <view class=\"card-trend\" :class=\"fissionData.usersTrend\">\n            <view class=\"trend-arrow\"></view>\n            <text class=\"trend-value\">{{fissionData.usersGrowth}}</text>\n          </view>\n        </view>\n        \n        <view class=\"stats-card\">\n          <view class=\"card-value\">{{fissionData.avgShare}}</view>\n          <view class=\"card-label\">平均分享数</view>\n          <view class=\"card-trend\" :class=\"fissionData.shareTrend\">\n            <view class=\"trend-arrow\"></view>\n            <text class=\"trend-value\">{{fissionData.shareGrowth}}</text>\n          </view>\n        </view>\n        \n        <view class=\"stats-card\">\n          <view class=\"card-value\">{{fissionData.conversionRate}}%</view>\n          <view class=\"card-label\">转化率</view>\n          <view class=\"card-trend\" :class=\"fissionData.conversionTrend\">\n            <view class=\"trend-arrow\"></view>\n            <text class=\"trend-value\">{{fissionData.conversionGrowth}}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 裂变红包活动列表 -->\n    <view class=\"fission-list-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">裂变活动</text>\n        <view class=\"add-btn\" @click=\"createFission\">\n          <text class=\"btn-text\">创建活动</text>\n          <view class=\"plus-icon-small\"></view>\n        </view>\n      </view>\n      \n      <view class=\"tab-header\">\n        <view \n          class=\"tab-item\" \n          v-for=\"(tab, index) in tabList\" \n          :key=\"index\"\n          :class=\"{ active: currentTab === index }\"\n          @tap=\"switchTab(index)\">\n          <text class=\"tab-text\">{{tab}}</text>\n        </view>\n      </view>\n      \n      <view class=\"fission-list\">\n        <view class=\"fission-item\" v-for=\"(item, index) in filteredFissionList\" :key=\"index\" @click=\"viewFissionDetail(item)\">\n          <view class=\"fission-header\">\n            <view class=\"fission-title\">{{item.title}}</view>\n            <view class=\"fission-status\" :class=\"'status-'+item.status\">{{item.statusText}}</view>\n          </view>\n          \n          <view class=\"fission-content\">\n            <view class=\"fission-icon\"></view>\n            <view class=\"fission-info\">\n              <text class=\"fission-time\">{{item.timeRange}}</text>\n              <view class=\"fission-rules\">\n                <text class=\"rules-label\">裂变规则: </text>\n                <text class=\"rules-value\">分享{{item.shareCount}}人可得{{item.rewardAmount}}元</text>\n              </view>\n              <view class=\"fission-amount\">\n                <text class=\"amount-label\">红包金额: </text>\n                <text class=\"amount-value\">¥{{item.amount}}/个</text>\n              </view>\n            </view>\n          </view>\n          \n          <view class=\"fission-stats\">\n            <view class=\"stat-row\">\n              <text class=\"stat-label\">参与人数:</text>\n              <text class=\"stat-value\">{{item.participantCount}}人</text>\n            </view>\n            <view class=\"stat-row\">\n              <text class=\"stat-label\">分享次数:</text>\n              <text class=\"stat-value\">{{item.shareCount}}次</text>\n            </view>\n            <view class=\"stat-row\">\n              <text class=\"stat-label\">新增用户:</text>\n              <text class=\"stat-value\">{{item.newUserCount}}人</text>\n            </view>\n          </view>\n          \n          <view class=\"fission-actions\">\n            <view class=\"action-btn\" @click.stop=\"viewFissionDetail(item)\">详情</view>\n            <view class=\"action-btn\" @click.stop=\"editFission(item)\" v-if=\"item.status === 'draft'\">编辑</view>\n            <view class=\"action-btn\" @click.stop=\"shareFission(item)\" v-if=\"item.status === 'active'\">分享</view>\n            <view class=\"action-btn delete\" @click.stop=\"deleteFission(item)\" v-if=\"item.status === 'draft' || item.status === 'ended'\">删除</view>\n          </view>\n        </view>\n        \n        <view class=\"empty-state\" v-if=\"filteredFissionList.length === 0\">\n          <view class=\"empty-icon\"></view>\n          <text class=\"empty-text\">暂无{{tabList[currentTab]}}裂变活动</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 裂变红包模板 -->\n    <view class=\"templates-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">裂变模板</text>\n      </view>\n      \n      <scroll-view scroll-x class=\"templates-scroll\" show-scrollbar=\"false\">\n        <view class=\"templates-container\">\n          <view class=\"template-card\" v-for=\"(template, index) in fissionTemplates\" :key=\"index\" @click=\"useTemplate(template)\">\n            <view class=\"template-preview\" :style=\"{ background: template.color }\">\n              <view class=\"template-icon\">\n                <image class=\"icon-image\" :src=\"template.icon\" mode=\"aspectFit\"></image>\n              </view>\n              <text class=\"template-name\">{{template.name}}</text>\n            </view>\n            <view class=\"template-footer\">\n              <text class=\"template-desc\">{{template.description}}</text>\n              <view class=\"template-use-btn\">使用</view>\n            </view>\n          </view>\n        </view>\n      </scroll-view>\n    </view>\n    \n    <!-- 裂变攻略 -->\n    <view class=\"strategy-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">裂变攻略</text>\n      </view>\n      \n      <view class=\"strategy-list\">\n        <view class=\"strategy-item\" v-for=\"(strategy, index) in strategies\" :key=\"index\" @click=\"viewStrategy(strategy)\">\n          <view class=\"strategy-icon\" :style=\"{ background: strategy.color }\">\n            <image class=\"icon-image\" :src=\"strategy.icon\" mode=\"aspectFit\"></image>\n          </view>\n          <view class=\"strategy-content\">\n            <text class=\"strategy-title\">{{strategy.title}}</text>\n            <text class=\"strategy-desc\">{{strategy.description}}</text>\n          </view>\n          <view class=\"strategy-arrow\"></view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 浮动操作按钮 -->\n    <view class=\"floating-action-button\" @click=\"createFission\">\n      <view class=\"fab-icon\">+</view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      dateRange: '2023-04-01 ~ 2023-04-30',\n      currentTab: 0,\n      tabList: ['全部', '进行中', '未开始', '已结束', '草稿'],\n      \n      // 裂变数据概览\n      fissionData: {\n        totalCount: 28,\n        countTrend: 'up',\n        countGrowth: '18.5%',\n        \n        totalUsers: 5682,\n        usersTrend: 'up',\n        usersGrowth: '32.7%',\n        \n        avgShare: 6.8,\n        shareTrend: 'up',\n        shareGrowth: '12.3%',\n        \n        conversionRate: 38.6,\n        conversionTrend: 'up',\n        conversionGrowth: '8.2%'\n      },\n      \n      // 裂变活动列表\n      fissionList: [\n        {\n          id: 1,\n          title: '新用户拉新活动',\n          status: 'active',\n          statusText: '进行中',\n          timeRange: '2023-04-15 ~ 2023-05-15',\n          shareCount: 3,\n          rewardAmount: 15,\n          amount: 5.00,\n          participantCount: 1286,\n          shareCount: 4752,\n          newUserCount: 863\n        },\n        {\n          id: 2,\n          title: '五一节日裂变',\n          status: 'upcoming',\n          statusText: '未开始',\n          timeRange: '2023-05-01 ~ 2023-05-07',\n          shareCount: 5,\n          rewardAmount: 30,\n          amount: 8.00,\n          participantCount: 0,\n          shareCount: 0,\n          newUserCount: 0\n        },\n        {\n          id: 3,\n          title: '会员专享裂变',\n          status: 'draft',\n          statusText: '草稿',\n          timeRange: '未设置',\n          shareCount: 2,\n          rewardAmount: 10,\n          amount: 5.00,\n          participantCount: 0,\n          shareCount: 0,\n          newUserCount: 0\n        },\n        {\n          id: 4,\n          title: '春节裂变活动',\n          status: 'ended',\n          statusText: '已结束',\n          timeRange: '2023-01-20 ~ 2023-02-05',\n          shareCount: 3,\n          rewardAmount: 20,\n          amount: 6.00,\n          participantCount: 2358,\n          shareCount: 8965,\n          newUserCount: 1542\n        }\n      ],\n      \n      // 裂变模板\n      fissionTemplates: [\n        {\n          id: 1,\n          name: '拉新引流',\n          description: '适合获取新用户的裂变活动',\n          color: '#FF6B6B',\n          icon: '/static/images/redpacket/fission-icon-1.png'\n        },\n        {\n          id: 2,\n          name: '会员专享',\n          description: '提高会员活跃度和忠诚度',\n          color: '#4ECDC4',\n          icon: '/static/images/redpacket/fission-icon-2.png'\n        },\n        {\n          id: 3,\n          name: '节日狂欢',\n          description: '节假日期间提升品牌曝光',\n          color: '#FFD166',\n          icon: '/static/images/redpacket/fission-icon-3.png'\n        },\n        {\n          id: 4,\n          name: '新品推广',\n          description: '快速提升新品知名度',\n          color: '#6A0572',\n          icon: '/static/images/redpacket/fission-icon-4.png'\n        }\n      ],\n      \n      // 裂变攻略\n      strategies: [\n        {\n          id: 1,\n          title: '如何设计高转化裂变活动',\n          description: '了解用户心理，设置合理的裂变门槛和奖励',\n          color: '#FF6B6B',\n          icon: '/static/images/redpacket/strategy-icon-1.png'\n        },\n        {\n          id: 2,\n          title: '裂变活动文案技巧',\n          description: '吸引人的文案能大幅提升裂变效果',\n          color: '#4ECDC4',\n          icon: '/static/images/redpacket/strategy-icon-2.png'\n        },\n        {\n          id: 3,\n          title: '裂变红包数据分析指南',\n          description: '通过数据分析优化裂变策略',\n          color: '#FFD166',\n          icon: '/static/images/redpacket/strategy-icon-3.png'\n        }\n      ]\n    }\n  },\n  computed: {\n    filteredFissionList() {\n      if (this.currentTab === 0) {\n        return this.fissionList;\n      } else {\n        const statusMap = {\n          1: 'active',\n          2: 'upcoming',\n          3: 'ended',\n          4: 'draft'\n        };\n        return this.fissionList.filter(item => item.status === statusMap[this.currentTab]);\n      }\n    }\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack();\n    },\n    showHelp() {\n      uni.showModal({\n        title: '裂变红包帮助',\n        content: '裂变红包是一种通过用户分享传播来获取新用户的营销工具，用户分享给指定数量的好友后可获得奖励。',\n        showCancel: false\n      });\n    },\n    showDatePicker() {\n      // 显示日期选择器\n      uni.showToast({\n        title: '日期选择功能开发中',\n        icon: 'none'\n      });\n    },\n    switchTab(index) {\n      this.currentTab = index;\n    },\n    createFission() {\n      uni.showToast({\n        title: '创建裂变活动功能开发中',\n        icon: 'none'\n      });\n    },\n    viewFissionDetail(item) {\n      uni.showToast({\n        title: '查看详情功能开发中',\n        icon: 'none'\n      });\n    },\n    editFission(item) {\n      uni.showToast({\n        title: '编辑功能开发中',\n        icon: 'none'\n      });\n    },\n    shareFission(item) {\n      uni.showToast({\n        title: '分享功能开发中',\n        icon: 'none'\n      });\n    },\n    deleteFission(item) {\n      uni.showModal({\n        title: '删除确认',\n        content: `确定要删除\"${item.title}\"吗？`,\n        success: (res) => {\n          if (res.confirm) {\n            uni.showToast({\n              title: '删除成功',\n              icon: 'success'\n            });\n          }\n        }\n      });\n    },\n    useTemplate(template) {\n      uni.showToast({\n        title: '使用模板功能开发中',\n        icon: 'none'\n      });\n    },\n    viewStrategy(strategy) {\n      uni.showToast({\n        title: '攻略详情功能开发中',\n        icon: 'none'\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.page-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: env(safe-area-inset-bottom);\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #FF5858, #FF0000);\n  color: #fff;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: bold;\n}\n\n/* 数据概览样式 */\n.overview-section {\n  background-color: #fff;\n  margin: 15px;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.overview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.date-picker {\n  display: flex;\n  align-items: center;\n  background-color: #F5F7FA;\n  padding: 5px 10px;\n  border-radius: 15px;\n}\n\n.date-text {\n  font-size: 12px;\n  color: #666;\n  margin-right: 5px;\n}\n\n.date-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 1px solid #666;\n  border-bottom: 1px solid #666;\n  transform: rotate(-45deg);\n}\n\n.stats-cards {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n}\n\n.stats-card {\n  width: 48%;\n  background: linear-gradient(135deg, #FFF, #F5F7FA);\n  border-radius: 10px;\n  padding: 15px;\n  margin-bottom: 10px;\n  position: relative;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);\n}\n\n.card-value {\n  font-size: 22px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.card-label {\n  font-size: 12px;\n  color: #666;\n}\n\n.card-trend {\n  position: absolute;\n  top: 15px;\n  right: 15px;\n  display: flex;\n  align-items: center;\n  font-size: 12px;\n}\n\n.card-trend.up {\n  color: #FF5858;\n}\n\n.card-trend.down {\n  color: #2ED573;\n}\n\n.trend-arrow {\n  width: 8px;\n  height: 8px;\n  border-left: 1px solid currentColor;\n  border-top: 1px solid currentColor;\n  margin-right: 2px;\n}\n\n.up .trend-arrow {\n  transform: rotate(45deg);\n}\n\n.down .trend-arrow {\n  transform: rotate(-135deg);\n}\n\n/* 裂变列表样式 */\n.fission-list-section {\n  background-color: #fff;\n  margin: 15px;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.add-btn {\n  display: flex;\n  align-items: center;\n  background: linear-gradient(135deg, #FF5858, #FF0000);\n  padding: 6px 12px;\n  border-radius: 15px;\n  color: #fff;\n}\n\n.btn-text {\n  font-size: 12px;\n  margin-right: 5px;\n}\n\n.plus-icon-small {\n  width: 12px;\n  height: 12px;\n  position: relative;\n}\n\n.plus-icon-small::before,\n.plus-icon-small::after {\n  content: '';\n  position: absolute;\n  background-color: #fff;\n}\n\n.plus-icon-small::before {\n  width: 12px;\n  height: 2px;\n  top: 5px;\n  left: 0;\n}\n\n.plus-icon-small::after {\n  width: 2px;\n  height: 12px;\n  top: 0;\n  left: 5px;\n}\n\n.tab-header {\n  display: flex;\n  border-bottom: 1px solid #eee;\n  margin-bottom: 15px;\n}\n\n.tab-item {\n  flex: 1;\n  text-align: center;\n  padding: 10px 0;\n  position: relative;\n}\n\n.tab-text {\n  font-size: 14px;\n  color: #666;\n}\n\n.tab-item.active .tab-text {\n  color: #FF5858;\n  font-weight: 600;\n}\n\n.tab-item.active::after {\n  content: '';\n  position: absolute;\n  bottom: -1px;\n  left: 25%;\n  width: 50%;\n  height: 2px;\n  background-color: #FF5858;\n  border-radius: 1px;\n}\n\n.fission-list {\n  margin-top: 10px;\n}\n\n.fission-item {\n  background-color: #fff;\n  border-radius: 10px;\n  padding: 15px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);\n  border: 1px solid #eee;\n}\n\n.fission-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.fission-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.fission-status {\n  font-size: 12px;\n  padding: 3px 8px;\n  border-radius: 10px;\n}\n\n.status-active {\n  background-color: #E8F5E9;\n  color: #388E3C;\n}\n\n.status-upcoming {\n  background-color: #E3F2FD;\n  color: #1976D2;\n}\n\n.status-ended {\n  background-color: #EEEEEE;\n  color: #757575;\n}\n\n.status-draft {\n  background-color: #FFF3E0;\n  color: #E65100;\n}\n\n.fission-content {\n  display: flex;\n  margin-bottom: 10px;\n}\n\n.fission-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 20px;\n  background: linear-gradient(135deg, #FF9A8B, #FF6B6B);\n  margin-right: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n}\n\n.fission-icon::before {\n  content: '裂';\n  color: #fff;\n  font-weight: bold;\n  font-size: 16px;\n}\n\n.fission-info {\n  flex: 1;\n}\n\n.fission-time {\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 5px;\n  display: block;\n}\n\n.fission-rules,\n.fission-amount {\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 5px;\n  display: flex;\n}\n\n.rules-label,\n.amount-label {\n  color: #999;\n}\n\n.rules-value,\n.amount-value {\n  color: #333;\n}\n\n.fission-stats {\n  background-color: #F9F9F9;\n  border-radius: 8px;\n  padding: 10px;\n  margin-bottom: 10px;\n}\n\n.stat-row {\n  display: flex;\n  justify-content: space-between;\n  font-size: 12px;\n  margin-bottom: 5px;\n}\n\n.stat-row:last-child {\n  margin-bottom: 0;\n}\n\n.stat-label {\n  color: #666;\n}\n\n.stat-value {\n  color: #333;\n  font-weight: 500;\n}\n\n.fission-actions {\n  display: flex;\n  justify-content: flex-end;\n  border-top: 1px solid #eee;\n  padding-top: 10px;\n}\n\n.action-btn {\n  margin-left: 10px;\n  padding: 5px 12px;\n  border-radius: 15px;\n  font-size: 12px;\n  background-color: #F5F7FA;\n  color: #666;\n}\n\n.action-btn.delete {\n  background-color: #FEE8E8;\n  color: #FF5858;\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 30px 0;\n}\n\n.empty-icon {\n  width: 60px;\n  height: 60px;\n  background-color: #F5F7FA;\n  border-radius: 30px;\n  margin-bottom: 10px;\n}\n\n.empty-text {\n  font-size: 14px;\n  color: #999;\n}\n\n/* 裂变模板样式 */\n.templates-section {\n  background-color: #fff;\n  margin: 15px;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.templates-scroll {\n  margin-top: 15px;\n  white-space: nowrap;\n}\n\n.templates-container {\n  display: inline-flex;\n  padding: 5px 0;\n}\n\n.template-card {\n  width: 140px;\n  margin-right: 15px;\n  border-radius: 10px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.template-preview {\n  height: 100px;\n  padding: 15px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.template-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 20px;\n  background: rgba(255, 255, 255, 0.2);\n  margin-bottom: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.icon-image {\n  width: 24px;\n  height: 24px;\n}\n\n.template-name {\n  color: #fff;\n  font-size: 14px;\n  font-weight: 600;\n}\n\n.template-footer {\n  background-color: #fff;\n  padding: 10px;\n}\n\n.template-desc {\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 8px;\n  display: block;\n  white-space: normal;\n  height: 32px;\n  overflow: hidden;\n}\n\n.template-use-btn {\n  background-color: #F5F7FA;\n  color: #FF5858;\n  font-size: 12px;\n  padding: 5px 0;\n  border-radius: 15px;\n  text-align: center;\n}\n\n/* 裂变攻略样式 */\n.strategy-section {\n  background-color: #fff;\n  margin: 15px;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n  margin-bottom: 80px;\n}\n\n.strategy-list {\n  margin-top: 10px;\n}\n\n.strategy-item {\n  display: flex;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid #eee;\n}\n\n.strategy-item:last-child {\n  border-bottom: none;\n}\n\n.strategy-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 20px;\n  margin-right: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.strategy-content {\n  flex: 1;\n}\n\n.strategy-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.strategy-desc {\n  font-size: 12px;\n  color: #666;\n}\n\n.strategy-arrow {\n  width: 12px;\n  height: 12px;\n  border-top: 1px solid #ccc;\n  border-right: 1px solid #ccc;\n  transform: rotate(45deg);\n}\n\n/* 浮动操作按钮 */\n.floating-action-button {\n  position: fixed;\n  bottom: 30px;\n  right: 20px;\n  width: 56px;\n  height: 56px;\n  border-radius: 28px;\n  background: linear-gradient(135deg, #FF5858, #FF0000);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 3px 10px rgba(255, 88, 88, 0.3);\n  z-index: 100;\n}\n\n.fab-icon {\n  font-size: 28px;\n  color: #fff;\n  font-weight: 300;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/redpacket/fission.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA2LA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,SAAS,CAAC,MAAM,OAAO,OAAO,OAAO,IAAI;AAAA;AAAA,MAGzC,aAAa;AAAA,QACX,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,QAEb,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,QAEb,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,aAAa;AAAA,QAEb,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,MACnB;AAAA;AAAA,MAGD,aAAa;AAAA,QACX;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,QAAQ;AAAA,UACR,kBAAkB;AAAA,UAClB,YAAY;AAAA,UACZ,cAAc;AAAA,QACf;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,QAAQ;AAAA,UACR,kBAAkB;AAAA,UAClB,YAAY;AAAA,UACZ,cAAc;AAAA,QACf;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,QAAQ;AAAA,UACR,kBAAkB;AAAA,UAClB,YAAY;AAAA,UACZ,cAAc;AAAA,QACf;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,QAAQ;AAAA,UACR,kBAAkB;AAAA,UAClB,YAAY;AAAA,UACZ,cAAc;AAAA,QAChB;AAAA,MACD;AAAA;AAAA,MAGD,kBAAkB;AAAA,QAChB;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACR;AAAA,MACD;AAAA;AAAA,MAGD,YAAY;AAAA,QACV;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,aAAa;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,aAAa;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,aAAa;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,sBAAsB;AACpB,UAAI,KAAK,eAAe,GAAG;AACzB,eAAO,KAAK;AAAA,aACP;AACL,cAAM,YAAY;AAAA,UAChB,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA;AAEL,eAAO,KAAK,YAAY,OAAO,UAAQ,KAAK,WAAW,UAAU,KAAK,UAAU,CAAC;AAAA,MACnF;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MACd,CAAC;AAAA,IACF;AAAA,IACD,iBAAiB;AAEfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,UAAU,OAAO;AACf,WAAK,aAAa;AAAA,IACnB;AAAA,IACD,gBAAgB;AACdA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,kBAAkB,MAAM;AACtBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,YAAY,MAAM;AAChBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,aAAa,MAAM;AACjBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,cAAc,MAAM;AAClBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,SAAS,KAAK,KAAK;AAAA,QAC5B,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,YAAY,UAAU;AACpBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,aAAa,UAAU;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7ZA,GAAG,WAAW,eAAe;"}