
	/* 红包设置相关样式 */
.switch-control {
	transform: scale(0.8);
}
.input-unit {
	font-size: 28rpx;
	color: #666;
	margin-left: 10rpx;
	margin-right: 10rpx;
}
.radio-group, .checkbox-group {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
}
.radio-item, .checkbox-item {
	display: flex;
	align-items: center;
	margin-right: 30rpx;
	margin-bottom: 20rpx;
}
.radio-circle {
	width: 36rpx;
	height: 36rpx;
	border-radius: 50%;
	border: 2rpx solid #CCCCCC;
	margin-right: 10rpx;
	position: relative;
}
.radio-circle.active {
	border-color: #FF4D4F;
}
.radio-circle.active::after {
	content: '';
	position: absolute;
	width: 24rpx;
	height: 24rpx;
	background-color: #FF4D4F;
	border-radius: 50%;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}
.checkbox-square {
	width: 36rpx;
	height: 36rpx;
	border: 2rpx solid #CCCCCC;
	margin-right: 10rpx;
	position: relative;
}
.checkbox-square.active {
	background-color: #FF4D4F;
	border-color: #FF4D4F;
}
.checkbox-square.active::after {
	content: '✓';
	color: white;
	position: absolute;
	font-size: 24rpx;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}
.radio-text, .checkbox-text {
	font-size: 28rpx;
	color: #333;
}
.date-picker-text {
	flex: 1;
	color: #333;
}
.date-icon {
	width: 40rpx;
	height: 40rpx;
}
.redpacket-tips {
	display: flex;
	align-items: flex-start;
	background-color: #FFF7F7;
	padding: 20rpx;
	border-radius: 10rpx;
	margin: 20rpx 0;
}
.tips-icon {
	width: 32rpx;
	height: 32rpx;
	margin-right: 10rpx;
	flex-shrink: 0;
	margin-top: 6rpx;
}
.tips-text {
	font-size: 24rpx;
	color: #FF6B6B;
	line-height: 1.5;
}

/* 红包样式 */
.red-packet-section {
	margin: 20rpx 0;
	padding: 0 30rpx;
}
.red-packet-container {
	position: relative;
	height: 200rpx;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.red-packet-bg {
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 1;
}
.red-packet-content {
	position: relative;
	z-index: 2;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	height: 100%;
	padding: 20rpx 30rpx;
	color: #fff;
}
.red-packet-title {
	display: flex;
	align-items: center;
	font-size: 32rpx;
	font-weight: bold;
}
.red-packet-icon {
	width: 40rpx;
	height: 40rpx;
	margin-right: 10rpx;
}
.red-packet-info {
	display: flex;
	flex-direction: column;
}
.red-packet-amount {
	font-size: 48rpx;
	font-weight: bold;
	margin-bottom: 6rpx;
}
.red-packet-desc {
	font-size: 24rpx;
	opacity: 0.9;
}
.grab-red-packet-btn {
	width: 240rpx;
	height: 70rpx;
	line-height: 70rpx;
	background: linear-gradient(to right, #FFD700, #FF8C00);
	color: #fff;
	font-size: 28rpx;
	font-weight: bold;
	border-radius: 35rpx;
	text-align: center;
	align-self: flex-end;
	margin-top: 10rpx;
	box-shadow: 0 4rpx 8rpx rgba(255, 140, 0, 0.3);
}
@keyframes fadeInUp {
from {
			opacity: 0;
			transform: translateY(20rpx);
}
to {
			opacity: 1;
			transform: translateY(0);
}
}
@keyframes scaleIn {
from {
			opacity: 0;
			transform: scale(0.95);
}
to {
			opacity: 1;
			transform: scale(1);
}
}
@keyframes shimmer {
0% {
			left: -100%;
}
100% {
			left: 100%;
}
}
	
	/* 添加样式变量以支持不同类目的主题色 */
.job_wanted {
		--theme-color: #3366FF;
		--theme-gradient: linear-gradient(135deg, #3366FF, #6699FF);
}
.hire {
		--theme-color: #0099CC;
		--theme-gradient: linear-gradient(135deg, #0099CC, #33CCFF);
}
.pet {
		--theme-color: #FF9966;
		--theme-gradient: linear-gradient(135deg, #FF9966, #FFCC99);
}
.house_rent, .house_sell {
		--theme-color: #33CC99;
		--theme-gradient: linear-gradient(135deg, #33CC99, #66FFCC);
}
.used_car, .car_service {
		--theme-color: #CC3366;
		--theme-gradient: linear-gradient(135deg, #CC3366, #FF6699);
}
.second_hand {
		--theme-color: #9966CC;
		--theme-gradient: linear-gradient(135deg, #9966CC, #CC99FF);
}
.education {
		--theme-color: #FF6633;
		--theme-gradient: linear-gradient(135deg, #FF6633, #FF9966);
}
.carpool {
		--theme-color: #339999;
		--theme-gradient: linear-gradient(135deg, #339999, #66CCCC);
}
.business_transfer {
		--theme-color: #FF9900;
		--theme-gradient: linear-gradient(135deg, #FF9900, #FFCC66);
}
.other_service {
		--theme-color: #666699;
		--theme-gradient: linear-gradient(135deg, #666699, #9999CC);
}
.home_service {
		--theme-color: #3399CC;
		--theme-gradient: linear-gradient(135deg, #3399CC, #66CCFF);
}
.find_service {
		--theme-color: #6633CC;
		--theme-gradient: linear-gradient(135deg, #6633CC, #9966FF);
}
.publish-detail {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		background-color: #f5f7fa;
		background-image: linear-gradient(to bottom, #eef2f7, #f5f7fa);
}
	
	/* 自定义导航栏 - 统一为蓝色系 */
.custom-navbar {
		background-image: linear-gradient(135deg, #0052CC, #0066FF);
		height: 88rpx;
		padding-top: 44px; /* 状态栏高度 */
		display: flex;
		align-items: center;
		position: fixed; /* 改为固定定位 */
		top: 0;
		left: 0;
		right: 0;
		padding-bottom: 10rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
		z-index: 100; /* 提高z-index确保在最上层 */
}
.back-btn {
		position: absolute;
		left: 20rpx;
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s;
}
.back-btn:active {
		transform: scale(0.95);
}
.back-icon {
		width: 48rpx;
		height: 48rpx;
}
.navbar-title {
		flex: 1;
		text-align: center;
		color: #FFFFFF;
		font-size: 36rpx;
		font-weight: 500;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.navbar-right {
		position: absolute;
		right: 20rpx;
		display: flex;
}
.navbar-btn {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s;
}
.navbar-btn:active {
		transform: scale(0.95);
}
.navbar-btn-icon {
		width: 44rpx;
		height: 44rpx;
		filter: drop-shadow(0 2rpx 2rpx rgba(0, 0, 0, 0.1));
}
	
	/* 免责声明 */
.disclaimer {
		padding: 20rpx 30rpx;
		margin: 170rpx 20rpx 0; /* 往下移动10px，从160rpx改为170rpx */
}
.disclaimer-text {
		font-size: 24rpx;
		color: #999999; /* 改为灰色 */
		line-height: 1.4;
}
	
	/* 内容区域 */
.content-scroll {
		flex: 1;
		height: calc(100vh - 180rpx);
		padding-bottom: 30rpx;
		margin-top: 0; /* 移除顶部边距，因为免责声明已经有了足够的边距 */
}
	
	/* 表单部分 */
.form-container {
		padding: 20rpx;
}
.form-section {
		background-color: #FFFFFF;
		margin-top: 30rpx;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
		transform: translateZ(0);
		transition: transform 0.3s, box-shadow 0.3s;
		border: 1rpx solid rgba(0, 0, 0, 0.03);
		animation: fadeInUp 0.5s ease-out forwards;
		position: relative;
}
	
	/* 添加卡片顶部彩色边框 */
.form-section::after {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 6rpx;
		background: var(--theme-gradient, linear-gradient(to right, #0052CC, #0066FF));
}
.form-section:nth-child(1) {
		animation-delay: 0.1s;
}
.form-section:nth-child(2) {
		animation-delay: 0.2s;
}
.form-section:nth-child(3) {
		animation-delay: 0.3s;
}
.form-section:nth-child(4) {
		animation-delay: 0.4s;
}
.form-section:active {
		transform: translateY(2rpx);
		box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}
.form-item {
		padding: 30rpx;
		position: relative;
		border-bottom: 1rpx solid rgba(0, 0, 0, 0.03);
		transition: background-color 0.3s;
}
.form-item:hover, .form-item:active {
		background-color: rgba(0, 0, 0, 0.01);
}
.form-item:last-child {
		border-bottom: none;
}
.section-title {
		font-size: 30rpx;
		color: #333;
		font-weight: 600;
		margin-bottom: 20rpx;
		position: relative;
		padding-left: 20rpx;
		display: flex;
		align-items: center;
}
.section-title::before {
		content: '';
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 8rpx;
		height: 28rpx;
		background: linear-gradient(to bottom, #0052CC, #0066FF);
		border-radius: 4rpx;
}
	
	/* 添加标题底部阴影线 */
.form-item:first-child {
		border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
		background-color: rgba(0, 0, 0, 0.01);
		padding-bottom: 20rpx;
}
	
	/* 表单项组 - 增强分组布局 */
.form-group {
		margin-bottom: 30rpx;
}
.form-group:last-child {
		margin-bottom: 0;
}
.form-label {
		font-size: 28rpx;
		color: #333;
		font-weight: 500;
		margin-bottom: 20rpx;
		display: flex;
		align-items: center;
}
.form-label-icon {
		width: 32rpx;
		height: 32rpx;
		margin-right: 10rpx;
		opacity: 0.7;
}
.required:after {
		content: '*';
		color: #ff4d4f;
		margin-left: 4rpx;
		font-size: 32rpx;
}
	
	/* 提示文本 */
.form-tip {
		font-size: 24rpx;
		color: #999;
		margin-top: 12rpx;
		padding-left: 10rpx;
		line-height: 1.4;
}
	
	/* 优化表单输入框样式 */
.form-input-wrapper {
		height: 80rpx;
		border-radius: 12rpx;
		background-color: #f5f7fa;
		padding: 0 20rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		box-shadow: inset 0 2rpx 5rpx rgba(0, 0, 0, 0.03);
		border: 1rpx solid rgba(0, 0, 0, 0.02);
		transition: all 0.3s;
		position: relative;
		overflow: hidden;
}
.form-input-wrapper:focus-within::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		height: 3rpx;
		background: var(--theme-gradient, linear-gradient(to right, #1677FF, #1677FF));
}
.form-input-wrapper:focus-within {
		background-color: rgba(22, 119, 255, 0.05);
		box-shadow: inset 0 2rpx 5rpx rgba(22, 119, 255, 0.1);
		border: 1rpx solid rgba(22, 119, 255, 0.2);
		transform: translateY(-2rpx);
}
.form-input {
		flex: 1;
		height: 80rpx;
		font-size: 28rpx;
		color: #333;
		padding: 0 20rpx;
}
.placeholder-text {
		font-size: 28rpx;
		color: #999;
}
.selected-text {
		color: #333;
}
.arrow-icon {
		width: 32rpx;
		height: 32rpx;
		transform: rotate(90deg);
		opacity: 0.5;
}
.textarea-wrapper {
		background-color: #f5f7fa;
		border-radius: 12rpx;
		padding: 20rpx;
		box-shadow: inset 0 2rpx 5rpx rgba(0, 0, 0, 0.03);
		border: 1rpx solid rgba(0, 0, 0, 0.02);
		transition: all 0.3s;
		position: relative;
		overflow: hidden;
}
.textarea-wrapper:focus-within::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		height: 3rpx;
		background: var(--theme-gradient, linear-gradient(to right, #1677FF, #1677FF));
}
.textarea-wrapper:focus-within {
		background-color: rgba(22, 119, 255, 0.05);
		box-shadow: inset 0 2rpx 5rpx rgba(22, 119, 255, 0.1);
		border: 1rpx solid rgba(22, 119, 255, 0.2);
		transform: translateY(-2rpx);
}
.form-textarea {
		width: 100%;
		height: 200rpx;
		font-size: 28rpx;
		color: #333;
		line-height: 1.6;
}
	
	/* 美化上传组件 */
.upload-wrapper {
		display: flex;
		flex-wrap: wrap;
		margin: 0 -10rpx;
}
.upload-btn {
		width: 160rpx;
		height: 160rpx;
		background-color: #f5f7fa;
		border-radius: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 10rpx 20rpx;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
		border: 1rpx dashed rgba(0, 0, 0, 0.1);
		transition: all 0.3s;
}
.upload-btn:active {
		transform: scale(0.98);
		background-color: rgba(0, 82, 204, 0.05);
}
.upload-btn-inner {
		display: flex;
		flex-direction: column;
		align-items: center;
}
.upload-icon {
		width: 48rpx;
		height: 48rpx;
		margin-bottom: 10rpx;
		filter: drop-shadow(0 2rpx 2rpx rgba(0, 0, 0, 0.1));
}
.upload-text {
		font-size: 24rpx;
		color: #999;
}
.upload-preview {
		width: 160rpx;
		height: 160rpx;
		margin: 0 10rpx 20rpx;
		position: relative;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
		border: 2rpx solid #fff;
		transition: all 0.3s;
}
.upload-preview:active {
		transform: scale(0.98);
}
.preview-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
}
.delete-btn {
		position: absolute;
		top: 0;
		right: 0;
		width: 40rpx;
		height: 40rpx;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 2;
}
.delete-icon {
		color: white;
		font-size: 28rpx;
}
.upload-tip {
		font-size: 24rpx;
		color: #999;
		margin-top: 10rpx;
		font-style: italic;
}
	
	/* 性别选择器 */
.gender-selector {
		display: flex;
		margin-bottom: 20rpx;
}
.gender-option {
		flex: 1;
		height: 80rpx;
		background-color: #f5f7fa;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		color: #666;
		margin-right: 20rpx;
		border-radius: 12rpx;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
		border: 1rpx solid rgba(0, 0, 0, 0.02);
		transition: all 0.3s;
}
.gender-option:last-child {
		margin-right: 0;
}
.gender-option:active {
		transform: scale(0.98);
}
.gender-selected {
		background: linear-gradient(to bottom, #e6f0ff, #d6e6ff);
		color: var(--theme-color, #0052CC);
		font-weight: 500;
		box-shadow: 0 4rpx 8rpx rgba(0, 82, 204, 0.1);
		border: 1rpx solid rgba(0, 82, 204, 0.1);
}
	
	/* 优化标签选择器样式 */
.tag-section {
		margin-top: 10rpx;
}
.tag-title {
		font-size: 28rpx;
		color: #333;
		font-weight: 500;
		margin-bottom: 20rpx;
}
.tag-list {
		display: flex;
		flex-wrap: wrap;
		margin: 0 -10rpx;
}
.tag-item {
		padding: 10rpx 24rpx;
		background-color: #f5f7fa;
		border-radius: 30rpx;
		font-size: 26rpx;
		color: #666;
		margin: 0 10rpx 20rpx;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
		border: 1rpx solid rgba(0, 0, 0, 0.02);
		transition: all 0.3s;
}
.tag-item:active {
		transform: scale(0.95);
}
.tag-active {
		background: var(--theme-gradient, linear-gradient(to bottom, #e6f0ff, #d6e6ff));
		color: var(--theme-color, #0052CC);
		box-shadow: 0 4rpx 8rpx rgba(0, 82, 204, 0.1);
		border: 1rpx solid rgba(0, 82, 204, 0.1);
		font-weight: 500;
}
.input-arrow {
		width: 32rpx;
		height: 32rpx;
		transform: rotate(90deg);
		opacity: 0.5;
}
.code-input-wrapper {
		justify-content: space-between;
}
.code-input {
		flex: 0.7;
}
.code-btn {
		padding: 0 30rpx;
		height: 60rpx;
		background: linear-gradient(to right, #0052CC, #0066FF);
		color: #FFFFFF;
		font-size: 26rpx;
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 8rpx rgba(0, 82, 204, 0.15);
		transition: all 0.3s;
}
.code-btn:active {
		transform: scale(0.95);
		box-shadow: 0 2rpx 4rpx rgba(0, 82, 204, 0.15);
}
	
	/* 广告部分 */
.ad-section {
		margin: 30rpx 20rpx;
		border-radius: 16rpx;
		overflow: hidden;
		background-color: #FFFFFF;
		animation: scaleIn 0.5s ease-out 0.5s forwards;
		opacity: 0;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
		padding: 20rpx 15rpx 5rpx;
}
.publish-method-title {
		font-size: 32rpx;
		color: #333;
		font-weight: 600;
		margin: 0rpx 30rpx 20rpx;
		position: relative;
		padding-left: 20rpx;
		display: flex;
		align-items: center;
}
.publish-method-title::before {
		content: '';
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 8rpx;
		height: 28rpx;
		background: linear-gradient(to bottom, #0052CC, #0066FF);
		border-radius: 4rpx;
}
.ad-item {
		padding: 25rpx 30rpx;
		display: flex;
		align-items: center;
		position: relative;
		border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
		transition: all 0.3s;
		margin: 0 15rpx 15rpx;
		border-radius: 12rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}
.ad-item:active {
		opacity: 0.9;
		transform: scale(0.98);
}
.free-ad {
		background: linear-gradient(to right, #E0F2FF, #B8E2FF, #88C9FF);
}
.pay-ad {
		background: linear-gradient(to right, #FFE8C0, #FFD285, #FFC455);
}
.ad-icon-wrap {
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: rgba(255, 255, 255, 0.8);
		width: 70rpx;
		height: 70rpx;
		border-radius: 15rpx;
		margin-right: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
		border: 1rpx solid rgba(255, 255, 255, 0.9);
}
.ad-icon {
		width: 40rpx;
		height: 40rpx;
}
.ad-content {
		flex: 1;
		display: flex;
		flex-direction: column;
}
.ad-title {
		font-size: 30rpx;
		color: #333;
		font-weight: 600;
		margin-bottom: 8rpx;
}
.ad-desc {
		font-size: 24rpx;
		color: #666;
		opacity: 0.9;
}
.ad-btn {
		padding: 10rpx 30rpx;
		font-size: 28rpx;
		font-weight: 600;
		border-radius: 40rpx;
		transition: all 0.3s;
		color: #FFFFFF;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
		letter-spacing: 2rpx;
}
.free-btn {
		background-color: #0052CC;
		background-image: linear-gradient(135deg, #0066FF, #004BB9);
}
.pay-btn {
		background-color: #E67700;
		background-image: linear-gradient(135deg, #FF8800, #E06600);
}
.ad-btn:active {
		transform: scale(0.95);
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
		opacity: 0.9;
}
.arrow-wrap {
		margin-left: 10rpx;
		height: 40rpx;
		display: flex;
		align-items: center;
}
.dropdown-icon {
		width: 32rpx;
		height: 32rpx;
		transform: rotate(90deg);
		opacity: 0.5;
}
	
	/* 美化协议部分 */
.agreement-section {
		padding: 20rpx 0;
		margin-bottom: 20rpx;
}
.agreement-row {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
}
.agreement-checkbox {
		transform: scale(0.8);
		margin-right: 10rpx;
}
.agreement-text {
		font-size: 26rpx;
		color: #666;
		margin: 0 6rpx;
}
.agreement-link {
		font-size: 26rpx;
		color: #0052CC;
		text-decoration: underline;
}
	
	/* 发布按钮 - 统一为蓝色系 */
.publish-btn-wrapper {
		padding: 20rpx 30rpx 40rpx;
		animation: fadeInUp 0.5s ease-out 0.7s forwards;
		opacity: 0;
}
.publish-btn {
		width: 100%;
		height: 88rpx;
		background: #0076FF; /* 更改为与打电话按钮相似的蓝色 */
		color: #FFFFFF;
		font-size: 32rpx;
		font-weight: 500;
		border-radius: 44rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border: none;
		box-shadow: 0 8rpx 16rpx rgba(0, 118, 255, 0.2);
		transition: all 0.3s;
		position: relative;
		overflow: hidden;
}
.publish-btn::after {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
		animation: shimmer 2s infinite;
}
@keyframes shimmer {
0% {
			left: -100%;
}
100% {
			left: 100%;
}
}
.publish-btn:active {
		transform: scale(0.98);
		box-shadow: 0 4rpx 8rpx rgba(0, 82, 204, 0.15);
}
.publish-btn[disabled] {
		background: linear-gradient(to right, #cccccc, #dddddd);
		color: #FFFFFF;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}
.publish-btn[disabled]::after {
		display: none;
}
	
	/* 添加类目特定主题颜色 */
.job_wanted .section-title::before {
		background: linear-gradient(to bottom, #3366FF, #6699FF);
}
.hire .section-title::before {
		background: linear-gradient(to bottom, #0099CC, #33CCFF);
}
.pet .section-title::before {
		background: linear-gradient(to bottom, #FF9966, #FFCC99);
}
.house_rent .section-title::before,
	.house_sell .section-title::before {
		background: linear-gradient(to bottom, #33CC99, #66FFCC);
}
.used_car .section-title::before,
	.car_service .section-title::before {
		background: linear-gradient(to bottom, #CC3366, #FF6699);
}
.second_hand .section-title::before {
		background: linear-gradient(to bottom, #9966CC, #CC99FF);
}
.education .section-title::before {
		background: linear-gradient(to bottom, #FF6633, #FF9966);
}

	/* 发布方式部分样式 */
.publish-method-section {
		margin: 30rpx 0;
		padding: 20rpx 30rpx 30rpx;
		background: linear-gradient(135deg, #ffffff 0%, #f9fbff 100%);
		border-radius: 20rpx;
		box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.06);
		position: relative;
		overflow: hidden;
}
.publish-method-section::before {
		content: '';
		position: absolute;
		left: 0;
		top: 0;
		width: 6rpx;
		height: 100%;
		background: linear-gradient(to bottom, #3b7dfc 0%, #5e96ff 100%);
}
.publish-method-section .section-header {
		display: flex;
		align-items: center;
		margin-bottom: 24rpx;
		padding-bottom: 20rpx;
		border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}
.section-icon {
		width: 48rpx;
		height: 48rpx;
		margin-right: 16rpx;
		background: linear-gradient(135deg, #3b7dfc 0%, #5e96ff 100%);
		border-radius: 12rpx;
		padding: 10rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 12rpx rgba(59, 125, 252, 0.25);
}
.section-icon image {
		width: 28rpx;
		height: 28rpx;
		filter: brightness(10);
}
.section-title {
		font-size: 34rpx;
		color: #333;
		font-weight: 600;
		letter-spacing: 1rpx;
}
