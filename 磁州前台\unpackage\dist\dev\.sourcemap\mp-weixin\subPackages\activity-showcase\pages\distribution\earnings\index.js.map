{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/distribution/earnings/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcZGlzdHJpYnV0aW9uXGVhcm5pbmdzXGluZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"earnings-container\">\r\n    <!-- 头部统计卡片 -->\r\n    <view class=\"statistics-card\" :style=\"{\r\n      borderRadius: '35px',\r\n      boxShadow: '0 8px 20px rgba(172,57,255,0.15)',\r\n      background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)',\r\n      padding: '30rpx',\r\n      marginBottom: '30rpx',\r\n      position: 'relative',\r\n      overflow: 'hidden'\r\n    }\">\r\n      <!-- 背景装饰 -->\r\n      <view class=\"bg-decoration\" :style=\"{\r\n        position: 'absolute',\r\n        top: '-50rpx',\r\n        right: '-50rpx',\r\n        width: '300rpx',\r\n        height: '300rpx',\r\n        borderRadius: '50%',\r\n        background: 'rgba(255,255,255,0.1)',\r\n        zIndex: '1'\r\n      }\"></view>\r\n      <view class=\"bg-decoration\" :style=\"{\r\n        position: 'absolute',\r\n        bottom: '-80rpx',\r\n        left: '-80rpx',\r\n        width: '250rpx',\r\n        height: '250rpx',\r\n        borderRadius: '50%',\r\n        background: 'rgba(255,255,255,0.08)',\r\n        zIndex: '1'\r\n      }\"></view>\r\n      \r\n      <!-- 总收益 -->\r\n      <view class=\"total-earnings\" :style=\"{\r\n        position: 'relative',\r\n        zIndex: '2'\r\n      }\">\r\n        <text class=\"label\" :style=\"{\r\n          fontSize: '28rpx',\r\n          color: 'rgba(255,255,255,0.9)',\r\n          marginBottom: '10rpx',\r\n          display: 'block'\r\n        }\">总收益(元)</text>\r\n        <text class=\"value\" :style=\"{\r\n          fontSize: '60rpx',\r\n          fontWeight: 'bold',\r\n          color: '#FFFFFF',\r\n          display: 'block',\r\n          textShadow: '0 2rpx 4rpx rgba(0,0,0,0.1)'\r\n        }\">{{ totalEarnings }}</text>\r\n      </view>\r\n      \r\n      <!-- 收益统计 -->\r\n      <view class=\"earnings-stats\" :style=\"{\r\n        display: 'flex',\r\n        justifyContent: 'space-around',\r\n        marginTop: '30rpx',\r\n        position: 'relative',\r\n        zIndex: '2',\r\n        background: 'rgba(255,255,255,0.1)',\r\n        borderRadius: '20rpx',\r\n        padding: '20rpx 0'\r\n      }\">\r\n        <view class=\"stat-item\">\r\n          <text class=\"stat-value\" :style=\"{\r\n            fontSize: '32rpx',\r\n            fontWeight: 'bold',\r\n            color: '#FFFFFF',\r\n            display: 'block',\r\n            textAlign: 'center'\r\n          }\">{{ waitingEarnings }}</text>\r\n          <text class=\"stat-label\" :style=\"{\r\n            fontSize: '24rpx',\r\n            color: 'rgba(255,255,255,0.8)',\r\n            display: 'block',\r\n            textAlign: 'center'\r\n          }\">待结算</text>\r\n        </view>\r\n        \r\n        <view class=\"stat-item\">\r\n          <text class=\"stat-value\" :style=\"{\r\n            fontSize: '32rpx',\r\n            fontWeight: 'bold',\r\n            color: '#FFFFFF',\r\n            display: 'block',\r\n            textAlign: 'center'\r\n          }\">{{ settledEarnings }}</text>\r\n          <text class=\"stat-label\" :style=\"{\r\n            fontSize: '24rpx',\r\n            color: 'rgba(255,255,255,0.8)',\r\n            display: 'block',\r\n            textAlign: 'center'\r\n          }\">已结算</text>\r\n        </view>\r\n        \r\n        <view class=\"stat-item\">\r\n          <text class=\"stat-value\" :style=\"{\r\n            fontSize: '32rpx',\r\n            fontWeight: 'bold',\r\n            color: '#FFFFFF',\r\n            display: 'block',\r\n            textAlign: 'center'\r\n          }\">{{ withdrawnEarnings }}</text>\r\n          <text class=\"stat-label\" :style=\"{\r\n            fontSize: '24rpx',\r\n            color: 'rgba(255,255,255,0.8)',\r\n            display: 'block',\r\n            textAlign: 'center'\r\n          }\">已提现</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 操作按钮 -->\r\n      <view class=\"action-buttons\" :style=\"{\r\n        display: 'flex',\r\n        justifyContent: 'space-between',\r\n        marginTop: '30rpx',\r\n        position: 'relative',\r\n        zIndex: '2'\r\n      }\">\r\n        <view class=\"action-btn withdraw-btn\" @click=\"navigateTo('/subPackages/activity-showcase/pages/distribution/withdraw/index')\" :style=\"{\r\n          flex: '1',\r\n          margin: '0 10rpx',\r\n          padding: '20rpx 0',\r\n          background: 'rgba(255,255,255,0.2)',\r\n          borderRadius: '35px',\r\n          textAlign: 'center',\r\n          color: '#FFFFFF',\r\n          fontSize: '28rpx',\r\n          fontWeight: '500',\r\n          border: '1rpx solid rgba(255,255,255,0.3)'\r\n        }\">\r\n          <text>申请提现</text>\r\n        </view>\r\n        \r\n        <view class=\"action-btn record-btn\" @click=\"navigateTo('/subPackages/activity-showcase/pages/distribution/records/index')\" :style=\"{\r\n          flex: '1',\r\n          margin: '0 10rpx',\r\n          padding: '20rpx 0',\r\n          background: 'rgba(255,255,255,0.2)',\r\n          borderRadius: '35px',\r\n          textAlign: 'center',\r\n          color: '#FFFFFF',\r\n          fontSize: '28rpx',\r\n          fontWeight: '500',\r\n          border: '1rpx solid rgba(255,255,255,0.3)'\r\n        }\">\r\n          <text>收益明细</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 收益趋势图 -->\r\n    <view class=\"trend-card\" :style=\"{\r\n      borderRadius: '35px',\r\n      boxShadow: '0 8px 20px rgba(0,0,0,0.08)',\r\n      background: '#FFFFFF',\r\n      padding: '30rpx',\r\n      marginBottom: '30rpx'\r\n    }\">\r\n      <view class=\"card-header\">\r\n        <text class=\"card-title\" :style=\"{\r\n          fontSize: '32rpx',\r\n          fontWeight: '600',\r\n          color: '#333333'\r\n        }\">收益趋势</text>\r\n        \r\n        <view class=\"time-filter\">\r\n          <view \r\n            v-for=\"(period, index) in timePeriods\" \r\n            :key=\"index\"\r\n            class=\"filter-item\"\r\n            :class=\"{ active: currentPeriod === index }\"\r\n            @click=\"switchPeriod(index)\"\r\n            :style=\"{\r\n              fontSize: '24rpx',\r\n              color: currentPeriod === index ? '#AC39FF' : '#666666',\r\n              padding: '6rpx 16rpx',\r\n              borderRadius: '20rpx',\r\n              marginLeft: '10rpx',\r\n              background: currentPeriod === index ? 'rgba(172,57,255,0.1)' : 'transparent'\r\n            }\"\r\n          >\r\n            {{ period }}\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 图表区域 -->\r\n      <view class=\"chart-area\" :style=\"{\r\n        height: '400rpx',\r\n        marginTop: '30rpx'\r\n      }\">\r\n        <!-- 这里可以集成第三方图表库，如ECharts或F2 -->\r\n        <view class=\"chart-placeholder\" :style=\"{\r\n          height: '100%',\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          background: '#F8F8F8',\r\n          borderRadius: '20rpx'\r\n        }\">\r\n          <text :style=\"{ color: '#999999' }\">收益趋势图表</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 收益来源 -->\r\n    <view class=\"source-card\" :style=\"{\r\n      borderRadius: '35px',\r\n      boxShadow: '0 8px 20px rgba(0,0,0,0.08)',\r\n      background: '#FFFFFF',\r\n      padding: '30rpx',\r\n      marginBottom: '30rpx'\r\n    }\">\r\n      <view class=\"card-header\">\r\n        <text class=\"card-title\" :style=\"{\r\n          fontSize: '32rpx',\r\n          fontWeight: '600',\r\n          color: '#333333'\r\n        }\">收益来源</text>\r\n      </view>\r\n      \r\n      <!-- 饼图区域 -->\r\n      <view class=\"pie-chart-area\" :style=\"{\r\n        height: '400rpx',\r\n        marginTop: '30rpx',\r\n        display: 'flex'\r\n      }\">\r\n        <!-- 饼图占位 -->\r\n        <view class=\"pie-placeholder\" :style=\"{\r\n          flex: '1',\r\n          height: '100%',\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          background: '#F8F8F8',\r\n          borderRadius: '20rpx'\r\n        }\">\r\n          <text :style=\"{ color: '#999999' }\">收益来源分布图</text>\r\n        </view>\r\n        \r\n        <!-- 图例 -->\r\n        <view class=\"legend\" :style=\"{\r\n          marginLeft: '20rpx',\r\n          width: '200rpx',\r\n          display: 'flex',\r\n          flexDirection: 'column',\r\n          justifyContent: 'center'\r\n        }\">\r\n          <view \r\n            v-for=\"(source, index) in incomeSources\" \r\n            :key=\"index\"\r\n            class=\"legend-item\"\r\n            :style=\"{\r\n              display: 'flex',\r\n              alignItems: 'center',\r\n              marginBottom: '15rpx'\r\n            }\"\r\n          >\r\n            <view class=\"color-dot\" :style=\"{\r\n              width: '20rpx',\r\n              height: '20rpx',\r\n              borderRadius: '50%',\r\n              background: source.color,\r\n              marginRight: '10rpx'\r\n            }\"></view>\r\n            <text class=\"source-name\" :style=\"{\r\n              fontSize: '24rpx',\r\n              color: '#666666'\r\n            }\">{{ source.name }}</text>\r\n            <text class=\"source-percent\" :style=\"{\r\n              fontSize: '24rpx',\r\n              color: '#333333',\r\n              fontWeight: '500',\r\n              marginLeft: 'auto'\r\n            }\">{{ source.percent }}%</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 收益规则 -->\r\n    <view class=\"rules-card\" :style=\"{\r\n      borderRadius: '35px',\r\n      boxShadow: '0 8px 20px rgba(0,0,0,0.08)',\r\n      background: '#FFFFFF',\r\n      padding: '30rpx',\r\n      marginBottom: '30rpx'\r\n    }\">\r\n      <view class=\"card-header\">\r\n        <text class=\"card-title\" :style=\"{\r\n          fontSize: '32rpx',\r\n          fontWeight: '600',\r\n          color: '#333333'\r\n        }\">收益规则</text>\r\n      </view>\r\n      \r\n      <view class=\"rules-content\" :style=\"{\r\n        marginTop: '20rpx'\r\n      }\">\r\n        <view \r\n          v-for=\"(rule, index) in earningsRules\" \r\n          :key=\"index\"\r\n          class=\"rule-item\"\r\n          :style=\"{\r\n            marginBottom: '20rpx',\r\n            paddingBottom: '20rpx',\r\n            borderBottom: index < earningsRules.length - 1 ? '1rpx solid #F2F2F7' : 'none'\r\n          }\"\r\n        >\r\n          <view class=\"rule-title\" :style=\"{\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            marginBottom: '10rpx'\r\n          }\">\r\n            <view class=\"rule-icon\" :style=\"{\r\n              width: '40rpx',\r\n              height: '40rpx',\r\n              borderRadius: '50%',\r\n              background: 'rgba(172,57,255,0.1)',\r\n              display: 'flex',\r\n              alignItems: 'center',\r\n              justifyContent: 'center',\r\n              marginRight: '10rpx'\r\n            }\">\r\n              <text :style=\"{\r\n                color: '#AC39FF',\r\n                fontSize: '24rpx',\r\n                fontWeight: '500'\r\n              }\">{{ index + 1 }}</text>\r\n            </view>\r\n            <text :style=\"{\r\n              fontSize: '28rpx',\r\n              fontWeight: '500',\r\n              color: '#333333'\r\n            }\">{{ rule.title }}</text>\r\n          </view>\r\n          \r\n          <text class=\"rule-desc\" :style=\"{\r\n            fontSize: '26rpx',\r\n            color: '#666666',\r\n            lineHeight: '1.6',\r\n            paddingLeft: '50rpx'\r\n          }\">{{ rule.description }}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 底部安全区域 -->\r\n    <view class=\"safe-area-bottom\" :style=\"{\r\n      height: '100rpx',\r\n      paddingBottom: 'env(safe-area-inset-bottom)'\r\n    }\"></view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue';\r\n\r\n// 收益数据\r\nconst totalEarnings = ref('1,234.56');\r\nconst waitingEarnings = ref('123.45');\r\nconst settledEarnings = ref('1,111.11');\r\nconst withdrawnEarnings = ref('800.00');\r\n\r\n// 时间周期\r\nconst timePeriods = ref(['7天', '30天', '90天', '全部']);\r\nconst currentPeriod = ref(1); // 默认选择30天\r\n\r\n// 收益来源数据\r\nconst incomeSources = ref([\r\n  { name: '直接佣金', percent: 65, color: '#AC39FF' },\r\n  { name: '团队佣金', percent: 25, color: '#FF3B69' },\r\n  { name: '活动奖励', percent: 10, color: '#FF9500' }\r\n]);\r\n\r\n// 收益规则\r\nconst earningsRules = ref([\r\n  { \r\n    title: '佣金结算规则', \r\n    description: '订单完成后，佣金将在7天后自动结算到您的账户，可随时申请提现。' \r\n  },\r\n  { \r\n    title: '提现规则', \r\n    description: '单笔提现金额不低于10元，每月可提现3次，超出次数将收取1%手续费。' \r\n  },\r\n  { \r\n    title: '团队收益规则', \r\n    description: '您可获得直接下级分销订单的10%佣金，二级下级的5%佣金作为团队收益。' \r\n  }\r\n]);\r\n\r\n// 切换时间周期\r\nfunction switchPeriod(index) {\r\n  currentPeriod.value = index;\r\n  // 这里可以添加加载对应时间段数据的逻辑\r\n}\r\n\r\n// 页面导航\r\nfunction navigateTo(url) {\r\n  uni.navigateTo({ url });\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.earnings-container {\r\n  padding: 30rpx;\r\n  background-color: #F2F2F7;\r\n  min-height: 100vh;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.time-filter {\r\n  display: flex;\r\n}\r\n\r\n.filter-item {\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.filter-item.active {\r\n  font-weight: 500;\r\n}\r\n\r\n.action-btn:active {\r\n  opacity: 0.9;\r\n  transform: scale(0.98);\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/distribution/earnings/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni"], "mappings": ";;;;;AA2WA,UAAM,gBAAgBA,cAAAA,IAAI,UAAU;AACpC,UAAM,kBAAkBA,cAAAA,IAAI,QAAQ;AACpC,UAAM,kBAAkBA,cAAAA,IAAI,UAAU;AACtC,UAAM,oBAAoBA,cAAAA,IAAI,QAAQ;AAGtC,UAAM,cAAcA,cAAG,IAAC,CAAC,MAAM,OAAO,OAAO,IAAI,CAAC;AAClD,UAAM,gBAAgBA,cAAAA,IAAI,CAAC;AAG3B,UAAM,gBAAgBA,cAAAA,IAAI;AAAA,MACxB,EAAE,MAAM,QAAQ,SAAS,IAAI,OAAO,UAAW;AAAA,MAC/C,EAAE,MAAM,QAAQ,SAAS,IAAI,OAAO,UAAW;AAAA,MAC/C,EAAE,MAAM,QAAQ,SAAS,IAAI,OAAO,UAAW;AAAA,IACjD,CAAC;AAGD,UAAM,gBAAgBA,cAAAA,IAAI;AAAA,MACxB;AAAA,QACE,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,MACD;AAAA,QACE,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,MACD;AAAA,QACE,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,IACH,CAAC;AAGD,aAAS,aAAa,OAAO;AAC3B,oBAAc,QAAQ;AAAA,IAExB;AAGA,aAAS,WAAW,KAAK;AACvBC,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IACxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnZA,GAAG,WAAW,eAAe;"}