/**
 * 权限控制工具类
 */
import store from '@/store';
import { secureStorage } from './securityUtils';

// 权限级别定义
export const PERMISSION_LEVELS = {
  GUEST: 0,       // 游客
  USER: 1,        // 普通用户
  MERCHANT: 2,    // 商家
  ADMIN: 3,       // 管理员
  SUPER_ADMIN: 4  // 超级管理员
};

/**
 * 检查用户是否拥有指定权限
 * @param {number} requiredLevel 需要的权限级别
 * @returns {boolean} 是否有权限
 */
export const hasPermission = (requiredLevel) => {
  const currentUser = store.state.user || {};
  const userLevel = currentUser.permissionLevel || PERMISSION_LEVELS.GUEST;
  
  return userLevel >= requiredLevel;
};

/**
 * 检查用户是否为内容所有者
 * @param {string} contentOwnerId 内容所有者ID
 * @returns {boolean} 是否为内容所有者
 */
export const isContentOwner = (contentOwnerId) => {
  const currentUser = store.state.user || {};
  return currentUser.userId === contentOwnerId;
};

/**
 * 检查用户是否有推广权限
 * @param {Object} content 内容对象
 * @returns {boolean} 是否有推广权限
 */
export const hasPromotionPermission = (content) => {
  // 管理员始终有权限
  if (hasPermission(PERMISSION_LEVELS.ADMIN)) {
    return true;
  }
  
  // 内容所有者有权限
  if (content && isContentOwner(content.ownerId)) {
    return true;
  }
  
  // 分销员且内容允许分销
  if (content && content.allowDistribution && store.state.user?.isDistributor) {
    return true;
  }
  
  return false;
};

/**
 * 安全地获取用户信息
 * @returns {Object} 用户信息
 */
export const getSecureUserInfo = () => {
  const userInfo = store.state.user || {};
  
  // 移除敏感信息
  const safeUserInfo = { ...userInfo };
  delete safeUserInfo.token;
  delete safeUserInfo.password;
  delete safeUserInfo.paymentInfo;
  
  return safeUserInfo;
};

export default {
  PERMISSION_LEVELS,
  hasPermission,
  isContentOwner,
  hasPromotionPermission,
  getSecureUserInfo
}; 