
.detail-container.data-v-d3c0eed0 {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 110rpx;
  padding-top: 150rpx; /* 增加顶部内边距，为固定导航栏留出空间 */
}

/* 自定义导航栏样式 */
.custom-navbar.data-v-d3c0eed0 {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}
.navbar-left.data-v-d3c0eed0 {
  width: 60rpx;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border-radius: 50%;
}
.navbar-title.data-v-d3c0eed0 {
  flex: 1;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  color: #FFFFFF; /* 修改为白色字体 */
}
.navbar-right.data-v-d3c0eed0 {
  width: 50px;
  text-align: right;
}
.back-icon.data-v-d3c0eed0 {
  width: 36rpx;
  height: 36rpx;
  display: block;
}
.detail-wrapper.data-v-d3c0eed0 {
  padding: 24rpx;
  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */
}

/* 店铺转让页面的特殊样式 */
.business-transfer-container.data-v-d3c0eed0 {
  /* 可以添加特定于店铺转让页面的样式 */
}

/* 隐藏的分享按钮 */
.hidden-share-btn.data-v-d3c0eed0 {
  position: fixed;
  width: 2rpx;
  height: 2rpx;
  opacity: 0;
  top: -999rpx;
  left: -999rpx;
  z-index: -1;
  overflow: hidden;
  padding: 0;
  margin: 0;
  border: none;
}
.hidden-share-btn.data-v-d3c0eed0::after {
  display: none;
}

/* 内容卡片通用样式 */
.content-card.data-v-d3c0eed0 {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

/* 标题区样式优化 */
.title-row.data-v-d3c0eed0 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}
.main-title.data-v-d3c0eed0 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-bottom: 12rpx;
}
.price-text.data-v-d3c0eed0 {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff4d4f;
  margin-bottom: 12rpx;
}

/* 元数据样式 */
.meta-info.data-v-d3c0eed0 {
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.tag-group.data-v-d3c0eed0 {
  display: flex;
  flex-wrap: wrap;
  margin-right: 20rpx;
  margin-bottom: 8rpx;
}
.info-tag.data-v-d3c0eed0 {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.08);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}
.publish-time.data-v-d3c0eed0 {
  font-size: 24rpx;
  color: #999;
}

/* 轮播图优化 */
.detail-swiper.data-v-d3c0eed0 {
  height: 420rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.swiper-image.data-v-d3c0eed0 {
  width: 100%;
  height: 100%;
}

/* 基本信息卡片内部布局 */
.basic-info.data-v-d3c0eed0 {
  display: flex;
  flex-wrap: wrap;
  background-color: #f9fafc;
  border-radius: 12rpx;
  padding: 16rpx 0;
  margin-top: 20rpx;
}
.info-item.data-v-d3c0eed0 {
  width: 50%;
  padding: 12rpx 24rpx;
  box-sizing: border-box;
}
.info-label.data-v-d3c0eed0 {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}
.info-value.data-v-d3c0eed0 {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 详情信息列表 */
.detail-list.data-v-d3c0eed0 {
  display: flex;
  flex-direction: column;
}
.detail-item.data-v-d3c0eed0 {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.detail-item.data-v-d3c0eed0:last-child {
  border-bottom: none;
}
.detail-label.data-v-d3c0eed0 {
  width: 160rpx;
  font-size: 28rpx;
  color: #888;
}
.detail-value.data-v-d3c0eed0 {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

/* 区块标题优化 */
.section-title.data-v-d3c0eed0 {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 16rpx;
}
.section-title.data-v-d3c0eed0::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 6rpx;
  height: 28rpx;
  background-color: #1890ff;
  border-radius: 3rpx;
}

/* 描述内容样式 */
.description-content.data-v-d3c0eed0 {
  padding: 20rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  line-height: 1.6;
}
.description-text.data-v-d3c0eed0 {
  font-size: 28rpx;
  color: #555;
  line-height: 1.6;
}

/* 联系人信息样式 */
.contact-content.data-v-d3c0eed0 {
  padding: 20rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}
.contact-item.data-v-d3c0eed0 {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.contact-label.data-v-d3c0eed0 {
  width: 120rpx;
  font-size: 28rpx;
  color: #888;
}
.contact-value.data-v-d3c0eed0 {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.contact-phone.data-v-d3c0eed0 {
  color: #1890ff;
}
.contact-tips.data-v-d3c0eed0 {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
}
.tips-icon.data-v-d3c0eed0 {
  font-size: 24rpx;
  color: #ff9800;
  margin-right: 8rpx;
}
.tips-text.data-v-d3c0eed0 {
  font-size: 24rpx;
  color: #ff9800;
}

/* 发布者信息 */
.publisher-header.data-v-d3c0eed0 {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}
.avatar-container.data-v-d3c0eed0 {
  width: 88rpx;
  height: 88rpx;
  border-radius: 44rpx;
  overflow: hidden;
  margin-right: 20rpx;
}
.avatar-image.data-v-d3c0eed0 {
  width: 100%;
  height: 100%;
}
.publisher-info.data-v-d3c0eed0 {
  flex: 1;
}
.publisher-name.data-v-d3c0eed0 {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}
.publisher-meta.data-v-d3c0eed0 {
  display: flex;
  flex-wrap: wrap;
}
.meta-text.data-v-d3c0eed0 {
  font-size: 24rpx;
  color: #888;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
}

/* 底部工具栏 */
.interaction-toolbar.data-v-d3c0eed0 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10rpx 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  height: 100rpx;
}
.toolbar-item.data-v-d3c0eed0 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6rpx 0;
}
.toolbar-icon.data-v-d3c0eed0 {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}
.toolbar-text.data-v-d3c0eed0 {
  font-size: 22rpx;
  color: #666;
}
.share-button.data-v-d3c0eed0 {
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
  line-height: normal;
  border-radius: 0;
  flex: 1;
}
.share-button.data-v-d3c0eed0::after {
  display: none;
}
.call-button.data-v-d3c0eed0 {
  flex: 3;
  background: linear-gradient(135deg, #0052CC, #0066FF);
  height: 80rpx;
  margin: 0 0 0 10rpx;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
}
.call-button-content.data-v-d3c0eed0 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.call-text.data-v-d3c0eed0 {
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  line-height: 1.2;
}
.call-subtitle.data-v-d3c0eed0 {
  color: rgba(255, 255, 255, 0.9);
  font-size: 20rpx;
  line-height: 1.2;
}

/* 悬浮海报按钮 */
.float-poster-btn.data-v-d3c0eed0 {
  position: fixed;
  right: 30rpx;
  bottom: 200rpx;
  width: 100rpx;
  height: 100rpx;
  background: rgba(240, 240, 240, 0.9);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.08);
  z-index: 90;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1rpx solid rgba(230, 230, 230, 0.6);
  transition: all 0.2s ease;
}
.float-poster-btn.data-v-d3c0eed0:active {
  transform: scale(0.95);
  box-shadow: 0 3rpx 10rpx rgba(0,0,0,0.08);
}
.poster-icon.data-v-d3c0eed0 {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 4rpx;
}
.poster-text.data-v-d3c0eed0 {
  font-size: 20rpx;
  color: #444;
  line-height: 1;
}

/* 举报按钮样式 */
/* 悬浮举报按钮已删除
.report-btn {
  position: fixed;
  right: 30rpx;
  top: 120rpx;
  z-index: 90;
  background-color: rgba(240, 240, 240, 0.9);
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.report-text {
  font-size: 24rpx;
  color: #666;
}
*/

/* 相似转让 */
.similar-transfers.data-v-d3c0eed0 {
  margin-bottom: 20rpx;
}
.similar-list.data-v-d3c0eed0 {
  display: flex;
  flex-direction: column;
}
.similar-item.data-v-d3c0eed0 {
  display: flex;
  padding: 20rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}
.similar-image.data-v-d3c0eed0 {
  width: 160rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}
.similar-info.data-v-d3c0eed0 {
  flex: 1;
}
.similar-title.data-v-d3c0eed0 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.similar-price.data-v-d3c0eed0 {
  font-size: 28rpx;
  color: #ff4d4f;
  margin-bottom: 8rpx;
}
.similar-meta.data-v-d3c0eed0 {
  font-size: 24rpx;
  color: #999;
}

/* 相关店铺推荐 */
.related-shops-card.data-v-d3c0eed0 {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
}
.related-shops-content.data-v-d3c0eed0 {
  display: flex;
  flex-direction: column;
}
.related-shops-list.data-v-d3c0eed0 {
  display: flex;
  flex-direction: column;
  margin-bottom: 20rpx;
}
.related-shop-item.data-v-d3c0eed0 {
  display: flex;
  padding: 20rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}
.shop-item-content.data-v-d3c0eed0 {
  display: flex;
  align-items: center;
}
.shop-item-left.data-v-d3c0eed0 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 20rpx;
}
.shop-item-middle.data-v-d3c0eed0 {
  flex: 1;
}
.shop-item-title.data-v-d3c0eed0 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}
.shop-item-area.data-v-d3c0eed0 {
  font-size: 24rpx;
  color: #999;
}
.shop-item-tags.data-v-d3c0eed0 {
  display: flex;
  flex-wrap: wrap;
}
.shop-item-tag.data-v-d3c0eed0 {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.08);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}
.shop-item-tag-more.data-v-d3c0eed0 {
  font-size: 24rpx;
  color: #999;
}
.shop-item-right.data-v-d3c0eed0 {
  width: 120rpx;
  text-align: right;
}
.shop-item-price.data-v-d3c0eed0 {
  font-size: 28rpx;
  color: #ff4d4f;
}
.empty-related-shops.data-v-d3c0eed0 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
}
.empty-image.data-v-d3c0eed0 {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}
.empty-text.data-v-d3c0eed0 {
  font-size: 24rpx;
  color: #999;
}
.view-more-btn.data-v-d3c0eed0 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  margin-top: 20rpx;
}
.view-more-text.data-v-d3c0eed0 {
  font-size: 24rpx;
  color: #1890ff;
  margin-right: 8rpx;
}
.view-more-icon.data-v-d3c0eed0 {
  font-size: 24rpx;
  color: #1890ff;
}
