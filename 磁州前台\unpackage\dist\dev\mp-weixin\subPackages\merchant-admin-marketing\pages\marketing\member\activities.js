"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      dateRange: "2023-04-01 ~ 2023-04-30",
      currentTab: 0,
      // 活动数据概览
      activityData: {
        totalActivities: 8,
        runningActivities: 5,
        participationCount: 1256,
        participationTrend: "up",
        participationGrowth: "18.5%",
        revenue: "28560.50",
        revenueTrend: "up",
        revenueGrowth: "12.3%"
      },
      // 活动分类标签
      tabs: ["全部活动", "会员专享", "积分活动", "抽奖活动", "限时活动"],
      // 活动列表
      activities: [
        {
          id: 1,
          name: "会员专享品鉴会",
          period: "2023-04-15 ~ 2023-04-20",
          image: "/static/images/activity-1.jpg",
          participantCount: 128,
          memberLevel: ["silver", "gold", "diamond"],
          memberLevelText: "银卡及以上",
          category: "会员专享",
          status: "active",
          statusText: "进行中",
          enabled: true
        },
        {
          id: 2,
          name: "积分翻倍周",
          period: "2023-04-10 ~ 2023-04-17",
          image: "/static/images/activity-2.jpg",
          participantCount: 356,
          memberLevel: ["all"],
          memberLevelText: "全部会员",
          category: "积分活动",
          status: "active",
          statusText: "进行中",
          enabled: true
        },
        {
          id: 3,
          name: "幸运大抽奖",
          period: "2023-04-20 ~ 2023-04-30",
          image: "/static/images/activity-3.jpg",
          participantCount: 215,
          memberLevel: ["all"],
          memberLevelText: "全部会员",
          category: "抽奖活动",
          status: "upcoming",
          statusText: "即将开始",
          enabled: true
        },
        {
          id: 4,
          name: "限时秒杀专场",
          period: "2023-04-05 ~ 2023-04-08",
          image: "/static/images/activity-4.jpg",
          participantCount: 432,
          memberLevel: ["all"],
          memberLevelText: "全部会员",
          category: "限时活动",
          status: "ended",
          statusText: "已结束",
          enabled: false
        },
        {
          id: 5,
          name: "钻石会员专享日",
          period: "2023-04-28",
          image: "/static/images/activity-5.jpg",
          participantCount: 68,
          memberLevel: ["diamond"],
          memberLevelText: "钻石会员",
          category: "会员专享",
          status: "upcoming",
          statusText: "即将开始",
          enabled: true
        },
        {
          id: 6,
          name: "积分兑换特惠",
          period: "2023-04-01 ~ 2023-04-07",
          image: "/static/images/activity-6.jpg",
          participantCount: 187,
          memberLevel: ["all"],
          memberLevelText: "全部会员",
          category: "积分活动",
          status: "ended",
          statusText: "已结束",
          enabled: false
        }
      ]
    };
  },
  computed: {
    filteredActivities() {
      if (this.currentTab === 0) {
        return this.activities;
      } else {
        const category = this.tabs[this.currentTab];
        return this.activities.filter((activity) => activity.category === category);
      }
    }
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    showHelp() {
      common_vendor.index.showModal({
        title: "会员活动帮助",
        content: "会员活动是指针对会员开展的各类营销活动，包括会员专享活动、积分活动、抽奖活动等，可以提高会员活跃度和忠诚度。",
        showCancel: false
      });
    },
    showDatePicker() {
      common_vendor.index.showToast({
        title: "日期选择功能开发中",
        icon: "none"
      });
    },
    switchTab(index) {
      this.currentTab = index;
    },
    createActivity() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/member/create-activity"
      });
    },
    viewActivityDetail(activity) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/member/activity-detail?id=${activity.id}`
      });
    },
    viewStats(activity) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/member/activity-stats?id=${activity.id}`
      });
    },
    shareActivity(activity) {
      common_vendor.index.showShareMenu({
        withShareTicket: true,
        menus: ["shareAppMessage", "shareTimeline"]
      });
    },
    toggleActivity(activity, e) {
      const index = this.activities.findIndex((item) => item.id === activity.id);
      if (index !== -1) {
        this.activities[index].enabled = e.detail.value;
      }
      common_vendor.index.showToast({
        title: e.detail.value ? `${activity.name}已启用` : `${activity.name}已禁用`,
        icon: "none"
      });
    }
  }
};
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: common_vendor.t($data.dateRange),
    d: common_vendor.o((...args) => $options.showDatePicker && $options.showDatePicker(...args)),
    e: common_vendor.t($data.activityData.totalActivities),
    f: common_vendor.t($data.activityData.runningActivities),
    g: common_vendor.t($data.activityData.participationCount),
    h: common_vendor.t($data.activityData.participationGrowth),
    i: common_vendor.n($data.activityData.participationTrend),
    j: common_vendor.t($data.activityData.revenue),
    k: common_vendor.t($data.activityData.revenueGrowth),
    l: common_vendor.n($data.activityData.revenueTrend),
    m: common_vendor.f($data.tabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab),
        b: index,
        c: $data.currentTab === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    }),
    n: common_vendor.t($data.tabs[$data.currentTab]),
    o: common_vendor.o((...args) => $options.createActivity && $options.createActivity(...args)),
    p: common_vendor.f($options.filteredActivities, (activity, index, i0) => {
      return {
        a: activity.image,
        b: common_vendor.n(activity.status),
        c: common_vendor.t(activity.statusText),
        d: common_vendor.n(activity.status),
        e: common_vendor.t(activity.name),
        f: common_vendor.t(activity.period),
        g: "1b251a04-1-" + i0 + "," + ("1b251a04-0-" + i0),
        h: "1b251a04-0-" + i0,
        i: common_vendor.t(activity.participantCount),
        j: "1b251a04-3-" + i0 + "," + ("1b251a04-2-" + i0),
        k: "1b251a04-2-" + i0,
        l: common_vendor.t(activity.memberLevelText),
        m: "1b251a04-5-" + i0 + "," + ("1b251a04-4-" + i0),
        n: "1b251a04-4-" + i0,
        o: common_vendor.o(($event) => $options.viewStats(activity), index),
        p: "1b251a04-7-" + i0 + "," + ("1b251a04-6-" + i0),
        q: "1b251a04-6-" + i0,
        r: common_vendor.o(($event) => $options.shareActivity(activity), index),
        s: activity.enabled,
        t: common_vendor.o((e) => $options.toggleActivity(activity, e), index),
        v: index,
        w: common_vendor.o(($event) => $options.viewActivityDetail(activity), index)
      };
    }),
    q: common_vendor.p({
      d: "M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"
    }),
    r: common_vendor.p({
      viewBox: "0 0 24 24",
      fill: "#43E97B"
    }),
    s: common_vendor.p({
      d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1.41 16.09V20h-2.67v-1.93c-1.71-.36-3.16-1.46-3.27-3.4h1.96c.1 1.05.82 1.87 2.65 1.87 1.96 0 2.4-.98 2.4-1.59 0-.83-.44-1.61-2.67-2.14-2.48-.6-4.18-1.62-4.18-3.67 0-1.72 1.39-2.84 3.11-3.21V4h2.67v1.95c1.86.45 2.79 1.86 2.85 3.39H14.3c-.05-1.11-.64-1.87-2.22-1.87-1.5 0-2.4.68-2.4 1.64 0 .84.65 1.39 2.67 1.91s4.18 1.39 4.18 3.91c-.01 1.83-1.38 2.83-3.12 3.16z"
    }),
    t: common_vendor.p({
      viewBox: "0 0 24 24",
      fill: "#38F9D7"
    }),
    v: common_vendor.p({
      d: "M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"
    }),
    w: common_vendor.p({
      viewBox: "0 0 24 24",
      fill: "#43E97B"
    }),
    x: common_vendor.p({
      d: "M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z"
    }),
    y: common_vendor.p({
      viewBox: "0 0 24 24",
      fill: "#38F9D7"
    }),
    z: common_vendor.o((...args) => $options.createActivity && $options.createActivity(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/member/activities.js.map
