package com.cizhou.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * C端用户实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("users")
public class User implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 微信OpenID
     */
    @TableField("openid")
    private String openid;

    /**
     * 微信UnionID
     */
    @TableField("unionid")
    private String unionid;

    /**
     * 用户昵称
     */
    @TableField("nickname")
    private String nickname;

    /**
     * 真实姓名
     */
    @TableField("real_name")
    private String realName;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 头像
     */
    @TableField("avatar")
    private String avatar;

    /**
     * 性别：0-未知，1-男，2-女
     */
    @TableField("gender")
    private Integer gender;

    /**
     * 生日
     */
    @TableField("birthday")
    private LocalDateTime birthday;

    /**
     * 省份
     */
    @TableField("province")
    private String province;

    /**
     * 城市
     */
    @TableField("city")
    private String city;

    /**
     * 区县
     */
    @TableField("district")
    private String district;

    /**
     * 详细地址
     */
    @TableField("address")
    private String address;

    /**
     * 用户状态：0-禁用，1-正常，2-冻结
     */
    @TableField("status")
    private Integer status;

    /**
     * 用户等级：1-普通用户，2-VIP用户，3-SVIP用户
     */
    @TableField("level")
    private Integer level;

    /**
     * 积分
     */
    @TableField("points")
    private Integer points;

    /**
     * 余额
     */
    @TableField("balance")
    private BigDecimal balance;

    /**
     * 冻结金额
     */
    @TableField("frozen_amount")
    private BigDecimal frozenAmount;

    /**
     * 累计消费金额
     */
    @TableField("total_spent")
    private BigDecimal totalSpent;

    /**
     * 注册来源：1-微信小程序，2-H5，3-APP
     */
    @TableField("register_source")
    private Integer registerSource;

    /**
     * 推荐人ID
     */
    @TableField("referrer_id")
    private Long referrerId;

    /**
     * 是否为分销员：0-否，1-是
     */
    @TableField("is_distributor")
    private Integer isDistributor;

    /**
     * 分销员等级
     */
    @TableField("distributor_level")
    private Integer distributorLevel;

    /**
     * 是否为合伙人：0-否，1-是
     */
    @TableField("is_partner")
    private Integer isPartner;

    /**
     * 合伙人等级
     */
    @TableField("partner_level")
    private Integer partnerLevel;

    /**
     * 最后登录时间
     */
    @TableField("last_login_time")
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    @TableField("last_login_ip")
    private String lastLoginIp;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    @TableField("is_deleted")
    private Integer isDeleted;
}
