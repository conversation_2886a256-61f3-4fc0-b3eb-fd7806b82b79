"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  __name: "call-history",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const navbarHeight = common_vendor.ref(64);
    const tabsHeight = common_vendor.ref(44);
    const tabs = common_vendor.ref([
      { name: "拨出" },
      { name: "接听" },
      { name: "未接" }
    ]);
    const currentTab = common_vendor.ref(0);
    const outCallList = common_vendor.ref([]);
    const inCallList = common_vendor.ref([]);
    const missedCallList = common_vendor.ref([]);
    const page = common_vendor.ref([1, 1, 1]);
    common_vendor.ref(10);
    const hasMore = common_vendor.ref([true, true, true]);
    const refreshing = common_vendor.ref([false, false, false]);
    const tabLineStyle = common_vendor.computed(() => {
      return {
        transform: `translateX(${currentTab.value * (100 / tabs.value.length)}%)`,
        width: `${100 / tabs.value.length}%`
      };
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const switchTab = (index) => {
      currentTab.value = index;
    };
    const onSwiperChange = (e) => {
      currentTab.value = e.detail.current;
    };
    const loadOutCallList = () => {
      setTimeout(() => {
        const mockData = Array.from({ length: 10 }, (_, i) => ({
          id: `out_${page.value[0]}_${i}`,
          name: `张师傅 ${page.value[0]}_${i}`,
          avatar: "/static/images/avatar.png",
          type: "店铺转让",
          desc: "通话时长: 2分30秒",
          time: "2023-10-15 14:30",
          phone: "13812345678"
        }));
        if (page.value[0] === 1) {
          outCallList.value = mockData;
        } else {
          outCallList.value = [...outCallList.value, ...mockData];
        }
        hasMore.value[0] = page.value[0] < 3;
        refreshing.value[0] = false;
      }, 500);
    };
    const loadInCallList = () => {
      setTimeout(() => {
        const mockData = Array.from({ length: 10 }, (_, i) => ({
          id: `in_${page.value[1]}_${i}`,
          name: `李客户 ${page.value[1]}_${i}`,
          avatar: "/static/images/avatar.png",
          type: "求职信息",
          desc: "通话时长: 1分45秒",
          time: "2023-10-14 10:20",
          phone: "13987654321"
        }));
        if (page.value[1] === 1) {
          inCallList.value = mockData;
        } else {
          inCallList.value = [...inCallList.value, ...mockData];
        }
        hasMore.value[1] = page.value[1] < 3;
        refreshing.value[1] = false;
      }, 500);
    };
    const loadMissedCallList = () => {
      setTimeout(() => {
        const mockData = Array.from({ length: 10 }, (_, i) => ({
          id: `missed_${page.value[2]}_${i}`,
          name: `王经理 ${page.value[2]}_${i}`,
          avatar: "/static/images/avatar.png",
          type: "招聘信息",
          desc: "未接来电",
          time: "2023-10-13 16:05",
          phone: "13755667788"
        }));
        if (page.value[2] === 1) {
          missedCallList.value = mockData;
        } else {
          missedCallList.value = [...missedCallList.value, ...mockData];
        }
        hasMore.value[2] = page.value[2] < 3;
        refreshing.value[2] = false;
      }, 500);
    };
    const loadMore = (tabIndex) => {
      if (!hasMore.value[tabIndex])
        return;
      page.value[tabIndex]++;
      if (tabIndex === 0) {
        loadOutCallList();
      } else if (tabIndex === 1) {
        loadInCallList();
      } else {
        loadMissedCallList();
      }
    };
    const onRefresh = (tabIndex) => {
      refreshing.value[tabIndex] = true;
      page.value[tabIndex] = 1;
      hasMore.value[tabIndex] = true;
      if (tabIndex === 0) {
        loadOutCallList();
      } else if (tabIndex === 1) {
        loadInCallList();
      } else {
        loadMissedCallList();
      }
    };
    const callAgain = (item) => {
      common_vendor.index.makePhoneCall({
        phoneNumber: item.phone,
        success: () => {
          common_vendor.index.__f__("log", "at subPackages/user/pages/call-history.vue:291", "拨打电话成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at subPackages/user/pages/call-history.vue:294", "拨打电话失败", err);
          common_vendor.index.showToast({
            title: "拨打电话失败",
            icon: "none"
          });
        }
      });
    };
    common_vendor.onMounted(() => {
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight;
      navbarHeight.value = statusBarHeight.value + 44;
      loadOutCallList();
      loadInCallList();
      loadMissedCallList();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$5,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_vendor.f(tabs.value, (tab, index, i0) => {
          return {
            a: common_vendor.t(tab.name),
            b: index,
            c: currentTab.value === index ? 1 : "",
            d: common_vendor.o(($event) => switchTab(index), index)
          };
        }),
        e: common_vendor.s(tabLineStyle.value),
        f: navbarHeight.value + "px",
        g: outCallList.value.length > 0
      }, outCallList.value.length > 0 ? {
        h: common_vendor.f(outCallList.value, (item, index, i0) => {
          return {
            a: item.avatar,
            b: common_vendor.t(item.name),
            c: common_vendor.t(item.type),
            d: common_vendor.t(item.desc),
            e: common_vendor.t(item.time),
            f: common_vendor.o(($event) => callAgain(item), index),
            g: index
          };
        }),
        i: common_assets._imports_1$11
      } : {
        j: common_assets._imports_1$3
      }, {
        k: outCallList.value.length > 0 && !hasMore.value[0]
      }, outCallList.value.length > 0 && !hasMore.value[0] ? {} : {}, {
        l: common_vendor.o(($event) => loadMore(0)),
        m: refreshing.value[0],
        n: common_vendor.o(($event) => onRefresh(0)),
        o: inCallList.value.length > 0
      }, inCallList.value.length > 0 ? {
        p: common_vendor.f(inCallList.value, (item, index, i0) => {
          return {
            a: item.avatar,
            b: common_vendor.t(item.name),
            c: common_vendor.t(item.type),
            d: common_vendor.t(item.desc),
            e: common_vendor.t(item.time),
            f: common_vendor.o(($event) => callAgain(item), index),
            g: index
          };
        }),
        q: common_assets._imports_1$11
      } : {
        r: common_assets._imports_1$3
      }, {
        s: inCallList.value.length > 0 && !hasMore.value[1]
      }, inCallList.value.length > 0 && !hasMore.value[1] ? {} : {}, {
        t: common_vendor.o(($event) => loadMore(1)),
        v: refreshing.value[1],
        w: common_vendor.o(($event) => onRefresh(1)),
        x: missedCallList.value.length > 0
      }, missedCallList.value.length > 0 ? {
        y: common_vendor.f(missedCallList.value, (item, index, i0) => {
          return {
            a: item.avatar,
            b: common_vendor.t(item.name),
            c: common_vendor.t(item.type),
            d: common_vendor.t(item.desc),
            e: common_vendor.t(item.time),
            f: common_vendor.o(($event) => callAgain(item), index),
            g: index
          };
        }),
        z: common_assets._imports_1$11
      } : {
        A: common_assets._imports_1$3
      }, {
        B: missedCallList.value.length > 0 && !hasMore.value[2]
      }, missedCallList.value.length > 0 && !hasMore.value[2] ? {} : {}, {
        C: common_vendor.o(($event) => loadMore(2)),
        D: refreshing.value[2],
        E: common_vendor.o(($event) => onRefresh(2)),
        F: currentTab.value,
        G: common_vendor.o(onSwiperChange),
        H: navbarHeight.value + tabsHeight.value + "px"
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/user/pages/call-history.js.map
