/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.page-root {
  position: relative;
  min-height: 100vh;
  background: #f6faff;
}
.nav-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #1677FF;
  z-index: 100;
  width: 100%;
}
.navbar-content {
  position: fixed;
  left: 0;
  right: 0;
  z-index: 101;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: transparent;
  width: 100%;
}
.navbar-left, .navbar-right {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 24px;
  height: 24px;
  display: block;
  background: none;
  border-radius: 0;
  margin: 0 auto;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  line-height: 44px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.history-btn {
  margin-top: 0;
  padding: 3px 18px;
  border: 1.5px solid #1677FF;
  color: #1677FF;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 500;
  background: #fff;
  transition: background 0.2s, color 0.2s;
  line-height: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.history-btn:active {
  background: #1677FF;
  color: #fff;
}
.points-mall-container {
  min-height: 100vh;
  position: relative;
  box-sizing: border-box;
  background: #f6faff;
}

/* 状态栏样式 */
.status-bar {
  background-color: #ffffff;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
}

/* 积分余额卡片 */
.points-balance-card {
  max-width: 320px;
  margin: 18px auto 0 auto;
  background: linear-gradient(135deg, #3a86ff, #1a56cc);
  border-radius: 24rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
  box-shadow: 0 10rpx 30rpx rgba(58, 134, 255, 0.2);
}
.balance-container {
  display: flex;
  flex-direction: column;
}
.balance-label {
  font-size: 26rpx;
  opacity: 0.9;
  margin-bottom: 8rpx;
}
.balance-value {
  font-size: 48rpx;
  font-weight: 700;
  font-family: "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif;
}
.balance-actions-vertical {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-end;
}
.balance-detail {
  width: 92px;
  padding: 6px 0;
  background: linear-gradient(90deg, #1677FF 0%, #007aff 100%);
  color: #fff;
  border-radius: 18px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  line-height: 22px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(22, 119, 255, 0.08);
}
.history-btn {
  width: 92px;
  padding: 6px 0;
  border: 1.5px solid #1677FF;
  color: #1677FF;
  border-radius: 18px;
  font-size: 14px;
  font-weight: 500;
  background: #fff;
  text-align: center;
  line-height: 22px;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.history-btn:active {
  background: #1677FF;
  color: #fff;
}

/* 分类标签 */
.category-tabs {
  background-color: #fff;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.03);
}
.tabs-scroll {
  white-space: nowrap;
  width: 100%;
}
.tab-item {
  display: inline-block;
  padding: 14rpx 30rpx;
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
  border-radius: 40rpx;
  transition: all 0.3s;
  background-color: #f5f5f5;
}
.tab-item.active {
  background: linear-gradient(135deg, #3a86ff, #1a56cc);
  color: #fff;
  font-weight: 500;
  box-shadow: 0 5rpx 15rpx rgba(58, 134, 255, 0.2);
}

/* 商品列表 */
.products-container {
  padding: 0;
}
.product-grid {
  display: grid;
  grid-template-columns: repeat(2, 160px);
  justify-content: center;
  gap: 16px;
  padding-bottom: 30rpx;
}
.product-item {
  width: 160px;
  margin: 0;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.05);
  position: relative;
}
.product-image {
  width: 100%;
  height: 240rpx;
  object-fit: cover;
}
.product-info {
  padding: 20rpx;
}
.product-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}
.product-description {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 24rpx;
  height: 68rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
.product-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.product-points {
  font-size: 26rpx;
  color: #ff6b6b;
  font-weight: 600;
}
.exchange-btn {
  background-color: rgba(58, 134, 255, 0.1);
  color: #3a86ff;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  font-size: 22rpx;
  font-weight: 500;
}
.exchange-btn.disabled {
  background-color: rgba(0, 0, 0, 0.05);
  color: #999;
}
.product-tag {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: #fff;
  padding: 6rpx 16rpx;
  border-radius: 20rpx 0 20rpx 0;
  font-size: 22rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 8rpx rgba(255, 107, 107, 0.2);
}

/* 空状态 */
.empty-state {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 商品详情弹窗 */
.product-detail-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}
.popup-content {
  width: 85%;
  max-width: 650rpx;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  position: relative;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
}
.popup-close {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.3);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  z-index: 10;
}
.popup-image {
  width: 100%;
  height: 350rpx;
  object-fit: cover;
}
.popup-info {
  padding: 30rpx;
}
.popup-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}
.popup-description {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
  line-height: 1.6;
}
.popup-points {
  font-size: 36rpx;
  color: #ff6b6b;
  font-weight: 600;
  margin-bottom: 24rpx;
}
.popup-rules {
  background-color: #f9f9f9;
  padding: 20rpx;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
}
.rules-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}
.rules-content {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
}
.popup-stock, .popup-validity {
  display: flex;
  font-size: 26rpx;
  margin-bottom: 16rpx;
}
.stock-label, .validity-label {
  color: #999;
  margin-right: 10rpx;
}
.stock-value, .validity-value {
  color: #333;
}
.popup-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #3a86ff, #1a56cc);
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30rpx;
  box-shadow: 0 10rpx 20rpx rgba(58, 134, 255, 0.2);
}
.popup-btn.disabled {
  background: #ccc;
  box-shadow: none;
}
.mall-history-row {
  display: flex;
  justify-content: flex-end;
  padding: 0 30rpx 10px 30rpx;
  background: transparent;
}