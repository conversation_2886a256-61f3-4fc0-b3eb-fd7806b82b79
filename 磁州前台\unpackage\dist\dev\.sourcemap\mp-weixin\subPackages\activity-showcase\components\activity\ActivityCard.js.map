{"version": 3, "file": "ActivityCard.js", "sources": ["subPackages/activity-showcase/components/activity/ActivityCard.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7Avc3ViUGFja2FnZXMvYWN0aXZpdHktc2hvd2Nhc2UvY29tcG9uZW50cy9hY3Rpdml0eS9BY3Rpdml0eUNhcmQudnVl"], "sourcesContent": ["<template>\n  <!-- 活动卡片组件 - 苹果风格设计 -->\n  <view class=\"activity-card\" :class=\"[`activity-type-${item.type}`, {'activity-ended': item.status === 'ended'}]\">\n    <!-- 活动类型标签（移至左上角） -->\n    <view class=\"activity-type-tag\" :class=\"`type-${item.type}`\">\n      <text class=\"type-text\">{{typeText}}</text>\n    </view>\n    \n    <!-- 活动封面图 -->\n    <!-- 活动封面图 -->\n    <!-- 活动封面图 -->\n    <view class=\"activity-cover-container\">\n      <image :src=\"item.coverImage\" class=\"activity-cover\" mode=\"aspectFill\"></image>\n      \n      <!-- 分销标识 - 新设计，左下角斜角标签 -->\n      <view class=\"distribution-badge-new\">\n        <view class=\"distribution-badge-inner\">\n          <view class=\"distribution-icon\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <path d=\"M12 2v4M12 18v4M4.93 4.93l2.83 2.83M16.24 16.24l2.83 2.83M2 12h4M18 12h4M4.93 19.07l2.83-2.83M16.24 7.76l2.83-2.83\"></path>\n            </svg>\n          </view>\n          <text class=\"distribution-text\">推广赚佣金</text>\n        </view>\n      </view>\n      \n    <!-- 置顶标签（右上角） -->\n    <view class=\"top-badge paid-badge\" v-if=\"item.isPaidTop\">\n      <view class=\"top-badge-icon\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n          <path d=\"M12 2v4M12 18v4M4.93 4.93l2.83 2.83M16.24 16.24l2.83 2.83M2 12h4M18 12h4M4.93 19.07l2.83-2.83M16.24 7.76l2.83-2.83\"></path>\n        </svg>\n      </view>\n      <text class=\"top-badge-text\">付费置顶</text>\n    </view>\n    \n    <view class=\"top-badge ad-badge\" v-if=\"item.isAdTop\">\n      <view class=\"top-badge-icon\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n          <rect x=\"2\" y=\"7\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\"></rect>\n          <path d=\"M16 3L12 7 8 3\"></path>\n        </svg>\n      </view>\n      <text class=\"top-badge-text\">广告置顶</text>\n    </view>\n      \n      <!-- 收藏按钮 -->\n      <view class=\"favorite-btn\" @click.stop=\"toggleFavorite\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"item.isFavorite ? 'currentColor' : 'none'\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n          <path d=\"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\"></path>\n        </svg>\n      </view>\n      \n      <!-- 活动热度 -->\n      <view class=\"activity-hot\" v-if=\"item.hot\">\n        <view class=\"hot-icon\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n            <path d=\"M8 2h8l4 10-4 10H8L4 12 8 2z\"></path>\n          </svg>\n        </view>\n        <text class=\"hot-text\">热门</text>\n      </view>\n    </view>\n    \n    <!-- 活动内容区 -->\n    <view class=\"activity-content\">\n      <!-- 活动标题 -->\n      <view class=\"activity-title-row\">\n        <text class=\"activity-title\">{{item.title}}</text>\n      </view>\n      \n      <!-- 活动时间 -->\n      <view class=\"activity-time\">\n        <view class=\"time-icon\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n            <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n            <polyline points=\"12 6 12 12 16 14\"></polyline>\n          </svg>\n        </view>\n        <text class=\"time-text\">{{item.startTime}} - {{item.endTime}}</text>\n      </view>\n      \n      <!-- 活动地点 -->\n      <view class=\"activity-location\">\n        <view class=\"location-icon\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n            <path d=\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\"></path>\n            <circle cx=\"12\" cy=\"10\" r=\"3\"></circle>\n          </svg>\n        </view>\n        <text class=\"location-text\">{{item.location}}</text>\n      </view>\n      \n      <!-- 活动特定信息区域 -->\n      <view class=\"activity-special-info\">\n        <!-- 插槽：特定活动类型的特殊信息 -->\n        <slot name=\"special-info\">\n          <!-- 拼团活动特定信息 -->\n          <view v-if=\"item.type === 'groupBuy'\" class=\"group-buy-info\">\n            <view class=\"price-info\">\n              <text class=\"current-price\">¥{{item.groupPrice}}</text>\n              <text class=\"original-price\">¥{{item.originalPrice}}</text>\n            </view>\n            <view class=\"group-progress\">\n              <view class=\"progress-bar\">\n                <view class=\"progress-inner\" :style=\"{width: groupProgress + '%'}\"></view>\n              </view>\n              <text class=\"progress-text\">还差{{item.groupSize - item.currentGroupMembers}}人成团</text>\n            </view>\n          </view>\n          \n          <!-- 秒杀活动特定信息 -->\n          <view v-else-if=\"item.type === 'flashSale'\" class=\"flash-sale-info\">\n            <view class=\"price-info\">\n              <text class=\"current-price\">¥{{item.salePrice}}</text>\n              <text class=\"original-price\">¥{{item.originalPrice}}</text>\n              <text class=\"discount\">{{discountPercent}}折</text>\n            </view>\n            <view class=\"countdown\" v-if=\"item.status === 'ongoing' || item.status === 'upcoming'\">\n              <text class=\"countdown-label\">{{item.status === 'ongoing' ? '距结束' : '距开始'}}</text>\n              <view class=\"countdown-time\">\n                <text class=\"time-block\">{{countdownHours}}</text>\n                <text class=\"time-separator\">:</text>\n                <text class=\"time-block\">{{countdownMinutes}}</text>\n                <text class=\"time-separator\">:</text>\n                <text class=\"time-block\">{{countdownSeconds}}</text>\n              </view>\n            </view>\n            <view class=\"stock-info\">\n              <view class=\"stock-progress\">\n                <view class=\"progress-inner\" :style=\"{width: stockProgress + '%'}\"></view>\n              </view>\n              <text class=\"stock-text\">已抢{{item.soldCount}}/{{item.totalStock}}</text>\n            </view>\n          </view>\n          \n          <!-- 优惠券活动特定信息 -->\n          <view v-else-if=\"item.type === 'coupon'\" class=\"coupon-info\">\n            <view class=\"coupon-value\">\n              <text class=\"value-symbol\" v-if=\"item.couponType === 'cash'\">¥</text>\n              <text class=\"value-number\">{{item.couponValue}}</text>\n              <text class=\"value-unit\" v-if=\"item.couponType === 'discount'\">折</text>\n            </view>\n            <view class=\"coupon-condition\" v-if=\"item.couponCondition\">\n              <text class=\"condition-text\">{{item.couponCondition}}</text>\n            </view>\n            <view class=\"coupon-validity\">\n              <text class=\"validity-text\">有效期至: {{item.couponValidity}}</text>\n            </view>\n          </view>\n          \n          <!-- 满减活动特定信息 -->\n          <view v-else-if=\"item.type === 'discount'\" class=\"discount-info\">\n            <view class=\"discount-rules\">\n              <view class=\"rule-item\" v-for=\"(rule, index) in item.discountRules\" :key=\"index\">\n                <text class=\"rule-text\">满{{rule.threshold}}减{{rule.discount}}</text>\n              </view>\n            </view>\n            <view class=\"merchant-count\">\n              <text class=\"merchant-text\">{{item.merchantCount}}家商家参与</text>\n            </view>\n          </view>\n        </slot>\n      </view>\n      \n      <!-- 活动参与信息 -->\n      <view class=\"activity-participation\">\n        <view class=\"participants\" v-if=\"item.participants && item.participants.length > 0\">\n          <view class=\"avatar-group\">\n            <image \n              v-for=\"(participant, index) in displayParticipants\" \n              :key=\"index\" \n              :src=\"participant.avatar\" \n              class=\"participant-avatar\"\n              :style=\"{zIndex: displayParticipants.length - index}\"\n            ></image>\n            <view class=\"more-participants\" v-if=\"item.participants.length > maxDisplayParticipants\">\n              <text>+{{item.participants.length - maxDisplayParticipants}}</text>\n            </view>\n          </view>\n          <text class=\"participant-count\">{{item.participants.length}}人参与</text>\n        </view>\n          \n          <!-- 分销收益标识 - 新设计 -->\n          <view class=\"distribution-profit-new\">\n            <view class=\"profit-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <line x1=\"12\" y1=\"1\" x2=\"12\" y2=\"23\"></line>\n                <path d=\"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"></path>\n              </svg>\n            </view>\n            <text class=\"profit-text-new\">最高赚¥{{getDistributionProfit()}}</text>\n          </view>\n        \n        <!-- 活动操作按钮 -->\n        <view class=\"activity-action\">\n          <view class=\"action-btn\" :class=\"actionBtnClass\" @click.stop=\"handleActionClick\">\n            <text class=\"action-text\">{{actionBtnText}}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed } from 'vue';\n\nconst props = defineProps({\n  item: {\n    type: Object,\n    required: true\n  }\n});\n\nconst emit = defineEmits(['favorite', 'action']);\n\n// 最大显示参与人数\nconst maxDisplayParticipants = 3;\n\n// 显示的参与者头像\nconst displayParticipants = computed(() => {\n  if (!props.item.participants) return [];\n  return props.item.participants.slice(0, maxDisplayParticipants);\n});\n\n// 活动类型文本\nconst typeText = computed(() => {\n  switch(props.item.type) {\n    case 'groupBuy':\n      return '拼团';\n    case 'flashSale':\n      return '秒杀';\n    case 'coupon':\n      return '优惠券';\n    case 'discount':\n      return '满减';\n    default:\n      return '活动';\n  }\n});\n\n// 拼团进度\nconst groupProgress = computed(() => {\n  if (props.item.type !== 'groupBuy') return 0;\n  return (props.item.currentGroupMembers / props.item.groupSize) * 100;\n});\n\n// 库存进度\nconst stockProgress = computed(() => {\n  if (props.item.type !== 'flashSale') return 0;\n  return (props.item.soldCount / props.item.totalStock) * 100;\n});\n\n// 折扣百分比\nconst discountPercent = computed(() => {\n  if (props.item.type !== 'flashSale') return '';\n  return ((props.item.salePrice / props.item.originalPrice) * 10).toFixed(1);\n});\n\n// 倒计时相关\nconst countdownHours = ref('00');\nconst countdownMinutes = ref('00');\nconst countdownSeconds = ref('00');\n\n// 初始化倒计时\nif (props.item.type === 'flashSale' && (props.item.status === 'ongoing' || props.item.status === 'upcoming')) {\n  // 实际项目中应该使用定时器更新倒计时\n  // 这里仅作为示例\n  countdownHours.value = '01';\n  countdownMinutes.value = '30';\n  countdownSeconds.value = '45';\n}\n\n// 操作按钮文本\nconst actionBtnText = computed(() => {\n  if (props.item.status === 'ended') return '查看详情';\n  \n  switch(props.item.type) {\n    case 'groupBuy':\n      return props.item.status === 'upcoming' ? '预约拼团' : '立即拼团';\n    case 'flashSale':\n      return props.item.status === 'upcoming' ? '提醒我' : '立即抢购';\n    case 'coupon':\n      return '立即领取';\n    case 'discount':\n      return '去使用';\n    default:\n      return '立即参与';\n  }\n});\n\n// 操作按钮样式类\nconst actionBtnClass = computed(() => {\n  if (props.item.status === 'ended') return 'btn-disabled';\n  return `btn-${props.item.type}`;\n});\n\n// 计算分销收益\nfunction getDistributionProfit() {\n  // 根据不同类型的活动计算不同的分销收益\n  switch(props.item.type) {\n    case 'groupBuy':\n      return (props.item.groupPrice * 0.1).toFixed(2);\n    case 'flashSale':\n      return (props.item.salePrice * 0.15).toFixed(2);\n    case 'coupon':\n      return props.item.couponType === 'cash' ? (props.item.couponValue * 0.05).toFixed(2) : '5.00';\n    case 'discount':\n      return props.item.discountRules && props.item.discountRules.length > 0 \n        ? (props.item.discountRules[0].discount * 0.2).toFixed(2) \n        : '8.00';\n    default:\n      return '10.00';\n  }\n}\n\n// 收藏切换\nfunction toggleFavorite(event) {\n  event.stopPropagation();\n  emit('favorite', props.item.id);\n}\n\n// 操作按钮点击\nfunction handleActionClick(event) {\n  event.stopPropagation();\n  emit('action', {\n    id: props.item.id,\n    type: props.item.type,\n    status: props.item.status\n  });\n}\n</script>\n\n<style scoped>\n/* 活动卡片基础样式 - 苹果风格 */\n.activity-card {\n  position: relative;\n  margin: 20rpx;\n  border-radius: 35rpx;\n  background-color: #ffffff;\n  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08), 0 2rpx 10rpx rgba(0, 0, 0, 0.04);\n  overflow: hidden;\n  transition: all 0.3s ease;\n}\n\n/* 分销标识 - 新设计（左下角斜角标签） */\n.distribution-badge-new {\n  position: absolute;\n  left: 0;\n  bottom: 60rpx;\n  background: linear-gradient(135deg, #FF9500, #FF3B30);\n  padding: 6rpx 16rpx;\n  border-top-right-radius: 16rpx;\n  border-bottom-right-radius: 16rpx;\n  box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.2);\n  z-index: 10;\n  display: flex;\n  align-items: center;\n}\n\n.distribution-badge-inner {\n  display: flex;\n  align-items: center;\n}\n\n.distribution-icon {\n  margin-right: 6rpx;\n  color: #FFFFFF;\n}\n\n.distribution-text {\n  font-size: 22rpx;\n  color: #FFFFFF;\n  font-weight: 600;\n  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);\n}\n\n/* 活动卡片悬停效果 */\n.activity-card:active {\n  transform: scale(0.98);\n  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.06);\n}\n\n/* 已结束活动样式 */\n.activity-ended {\n  opacity: 0.7;\n}\n\n/* 活动类型标签 - 移至左上角 */\n.activity-type-tag {\n  position: absolute;\n  top: 20rpx;\n  left: 20rpx;\n  padding: 6rpx 16rpx;\n  border-radius: 20rpx;\n  z-index: 10;\n}\n\n.type-groupBuy {\n  background-color: rgba(52, 199, 89, 0.9);\n  color: #ffffff;\n}\n\n.type-flashSale {\n  background-color: rgba(255, 59, 48, 0.9);\n  color: #ffffff;\n}\n\n.type-coupon {\n  background-color: rgba(255, 149, 0, 0.9);\n  color: #ffffff;\n}\n\n.type-discount {\n  background-color: rgba(90, 200, 250, 0.9);\n  color: #ffffff;\n}\n\n.type-text {\n  font-size: 22rpx;\n  font-weight: 500;\n}\n\n/* 置顶标签样式 */\n.top-badge {\n  position: absolute;\n  top: 0;\n  right: 0;\n  z-index: 10;\n  display: flex;\n  align-items: center;\n  padding: 10rpx 20rpx;\n  border-bottom-left-radius: 24rpx;\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15), 0 4rpx 8rpx rgba(0, 0, 0, 0.1);\n  transform: translateZ(0);\n  backdrop-filter: blur(10rpx);\n  -webkit-backdrop-filter: blur(10rpx);\n}\n\n.paid-badge {\n  background: linear-gradient(135deg, #FF9500, #FF3B30);\n  box-shadow: 0 8rpx 20rpx rgba(255, 59, 48, 0.2), 0 4rpx 8rpx rgba(255, 59, 48, 0.1);\n}\n\n.ad-badge {\n  background: linear-gradient(135deg, #007AFF, #5AC8FA);\n  box-shadow: 0 8rpx 20rpx rgba(0, 122, 255, 0.2), 0 4rpx 8rpx rgba(0, 122, 255, 0.1);\n}\n\n.top-badge-icon {\n  color: #FFFFFF;\n  margin-right: 10rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.top-badge-text {\n  font-size: 24rpx;\n  color: #FFFFFF;\n  font-weight: 600;\n  letter-spacing: 0.5rpx;\n  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);\n}\n\n/* 活动封面 */\n.activity-cover-container {\n  position: relative;\n  width: 100%;\n  height: 300rpx;\n  overflow: hidden;\n}\n\n.activity-cover {\n  width: 100%;\n  height: 100%;\n  transition: transform 0.3s ease;\n}\n\n.activity-card:active .activity-cover {\n  transform: scale(1.05);\n}\n\n/* 收藏按钮 */\n.favorite-btn {\n  position: absolute;\n  right: 20rpx;\n  bottom: 20rpx;\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 30rpx;\n  background-color: rgba(255, 255, 255, 0.9);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #ff3b30;\n  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);\n  z-index: 10;\n}\n\n/* 热门标签 */\n.activity-hot {\n  position: absolute;\n  left: 20rpx;\n  bottom: 20rpx;\n  padding: 6rpx 16rpx;\n  border-radius: 20rpx;\n  background-color: rgba(255, 59, 48, 0.9);\n  color: #ffffff;\n  display: flex;\n  align-items: center;\n  z-index: 10;\n}\n\n.hot-icon {\n  margin-right: 6rpx;\n}\n\n.hot-text {\n  font-size: 22rpx;\n  font-weight: 500;\n}\n\n/* 活动内容区 */\n.activity-content {\n  padding: 24rpx;\n}\n\n/* 活动标题 */\n.activity-title-row {\n  margin-bottom: 16rpx;\n}\n\n.activity-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #000000;\n  line-height: 1.4;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n}\n\n/* 活动时间 */\n.activity-time {\n  display: flex;\n  align-items: center;\n  margin-bottom: 12rpx;\n}\n\n.time-icon {\n  margin-right: 8rpx;\n  color: #8e8e93;\n}\n\n.time-text {\n  font-size: 24rpx;\n  color: #8e8e93;\n}\n\n/* 活动地点 */\n.activity-location {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.location-icon {\n  margin-right: 8rpx;\n  color: #8e8e93;\n}\n\n.location-text {\n  font-size: 24rpx;\n  color: #8e8e93;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  max-width: 90%;\n}\n\n/* 活动特定信息区域 */\n.activity-special-info {\n  margin-bottom: 20rpx;\n  padding: 16rpx;\n  background-color: #f9f9f9;\n  border-radius: 20rpx;\n}\n\n/* 拼团活动特定样式 */\n.group-buy-info {\n  display: flex;\n  flex-direction: column;\n}\n\n.price-info {\n  display: flex;\n  align-items: baseline;\n  margin-bottom: 12rpx;\n}\n\n.current-price {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #ff3b30;\n  margin-right: 12rpx;\n}\n\n.original-price {\n  font-size: 24rpx;\n  color: #8e8e93;\n  text-decoration: line-through;\n}\n\n.discount {\n  font-size: 24rpx;\n  color: #ff3b30;\n  margin-left: 12rpx;\n  padding: 2rpx 8rpx;\n  background-color: rgba(255, 59, 48, 0.1);\n  border-radius: 10rpx;\n}\n\n.group-progress {\n  margin-top: 12rpx;\n}\n\n.progress-bar {\n  height: 10rpx;\n  background-color: #e5e5ea;\n  border-radius: 5rpx;\n  overflow: hidden;\n  margin-bottom: 8rpx;\n}\n\n.progress-inner {\n  height: 100%;\n  background-color: #34c759;\n  border-radius: 5rpx;\n}\n\n.progress-text {\n  font-size: 22rpx;\n  color: #34c759;\n}\n\n/* 秒杀活动特定样式 */\n.flash-sale-info {\n  display: flex;\n  flex-direction: column;\n}\n\n.countdown {\n  display: flex;\n  align-items: center;\n  margin: 12rpx 0;\n}\n\n.countdown-label {\n  font-size: 24rpx;\n  color: #8e8e93;\n  margin-right: 12rpx;\n}\n\n.countdown-time {\n  display: flex;\n  align-items: center;\n}\n\n.time-block {\n  width: 40rpx;\n  height: 40rpx;\n  background-color: #1c1c1e;\n  color: #ffffff;\n  font-size: 24rpx;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 6rpx;\n}\n\n.time-separator {\n  margin: 0 6rpx;\n  color: #1c1c1e;\n  font-weight: 600;\n}\n\n.stock-info {\n  margin-top: 12rpx;\n}\n\n.stock-progress {\n  height: 10rpx;\n  background-color: #e5e5ea;\n  border-radius: 5rpx;\n  overflow: hidden;\n  margin-bottom: 8rpx;\n}\n\n.stock-text {\n  font-size: 22rpx;\n  color: #ff3b30;\n}\n\n/* 优惠券活动特定样式 */\n.coupon-info {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 12rpx;\n  background: linear-gradient(135deg, #ff9500, #ff3b30);\n  border-radius: 16rpx;\n  color: #ffffff;\n}\n\n.coupon-value {\n  display: flex;\n  align-items: baseline;\n  margin-bottom: 8rpx;\n}\n\n.value-symbol {\n  font-size: 24rpx;\n  font-weight: 500;\n}\n\n.value-number {\n  font-size: 48rpx;\n  font-weight: 700;\n}\n\n.value-unit {\n  font-size: 24rpx;\n  font-weight: 500;\n  margin-left: 4rpx;\n}\n\n.coupon-condition {\n  margin-bottom: 8rpx;\n}\n\n.condition-text {\n  font-size: 22rpx;\n}\n\n.coupon-validity {\n  font-size: 20rpx;\n  opacity: 0.8;\n}\n\n/* 满减活动特定样式 */\n.discount-info {\n  display: flex;\n  flex-direction: column;\n}\n\n.discount-rules {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 10rpx;\n  margin-bottom: 12rpx;\n}\n\n.rule-item {\n  padding: 4rpx 12rpx;\n  background-color: rgba(90, 200, 250, 0.1);\n  border-radius: 10rpx;\n  border: 1rpx solid rgba(90, 200, 250, 0.3);\n}\n\n.rule-text {\n  font-size: 22rpx;\n  color: #5ac8fa;\n}\n\n.merchant-count {\n  margin-top: 8rpx;\n}\n\n.merchant-text {\n  font-size: 22rpx;\n  color: #8e8e93;\n}\n\n/* 活动参与信息 */\n.activity-participation {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 20rpx;\n}\n\n.participants {\n  display: flex;\n  align-items: center;\n}\n\n.avatar-group {\n  display: flex;\n  margin-right: 12rpx;\n}\n\n.participant-avatar {\n  width: 50rpx;\n  height: 50rpx;\n  border-radius: 25rpx;\n  border: 2rpx solid #ffffff;\n  margin-left: -15rpx;\n}\n\n.participant-avatar:first-child {\n  margin-left: 0;\n}\n\n.more-participants {\n  width: 50rpx;\n  height: 50rpx;\n  border-radius: 25rpx;\n  background-color: #f2f2f7;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-left: -15rpx;\n  border: 2rpx solid #ffffff;\n}\n\n.more-participants text {\n  font-size: 18rpx;\n  color: #8e8e93;\n}\n\n.participant-count {\n  font-size: 24rpx;\n  color: #8e8e93;\n}\n\n/* 分销收益标识 - 新设计 */\n.distribution-profit-new {\n  display: flex;\n  align-items: center;\n  padding: 8rpx 16rpx;\n  background: linear-gradient(135deg, #FF9500, #FF3B30);\n  border-radius: 20rpx;\n  box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.15);\n  margin-right: 15rpx;\n}\n\n.profit-icon {\n  color: #FFFFFF;\n  margin-right: 6rpx;\n}\n\n.profit-text-new {\n  font-size: 22rpx;\n  color: #FFFFFF;\n  font-weight: 600;\n}\n\n/* 活动操作按钮 */\n.activity-action {\n  display: flex;\n}\n\n.action-btn {\n  padding: 12rpx 30rpx;\n  border-radius: 30rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 500;\n}\n\n.btn-groupBuy {\n  background-color: #34c759;\n  color: #ffffff;\n}\n\n.btn-flashSale {\n  background-color: #ff3b30;\n  color: #ffffff;\n}\n\n.btn-coupon {\n  background-color: #ff9500;\n  color: #ffffff;\n}\n\n.btn-discount {\n  background-color: #5ac8fa;\n  color: #ffffff;\n}\n\n.btn-disabled {\n  background-color: #e5e5ea;\n  color: #8e8e93;\n}\n\n.action-text {\n  font-size: 26rpx;\n}\n\n/* 活动类型特定卡片样式 */\n.activity-type-groupBuy {\n  border-left: 6rpx solid #34c759;\n}\n\n.activity-type-flashSale {\n  border-left: 6rpx solid #ff3b30;\n}\n\n.activity-type-coupon {\n  border-left: 6rpx solid #ff9500;\n}\n\n.activity-type-discount {\n  border-left: 6rpx solid #5ac8fa;\n}\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/components/activity/ActivityCard.vue'\nwx.createComponent(Component)"], "names": ["computed", "ref"], "mappings": ";;;;;;;;;;;AA0NA,MAAM,yBAAyB;;;;;;;;;;;AAV/B,UAAM,QAAQ;AAOd,UAAM,OAAO;AAMb,UAAM,sBAAsBA,cAAQ,SAAC,MAAM;AACzC,UAAI,CAAC,MAAM,KAAK;AAAc,eAAO,CAAA;AACrC,aAAO,MAAM,KAAK,aAAa,MAAM,GAAG,sBAAsB;AAAA,IAChE,CAAC;AAGD,UAAM,WAAWA,cAAQ,SAAC,MAAM;AAC9B,cAAO,MAAM,KAAK,MAAI;AAAA,QACpB,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH,CAAC;AAGD,UAAM,gBAAgBA,cAAQ,SAAC,MAAM;AACnC,UAAI,MAAM,KAAK,SAAS;AAAY,eAAO;AAC3C,aAAQ,MAAM,KAAK,sBAAsB,MAAM,KAAK,YAAa;AAAA,IACnE,CAAC;AAGD,UAAM,gBAAgBA,cAAQ,SAAC,MAAM;AACnC,UAAI,MAAM,KAAK,SAAS;AAAa,eAAO;AAC5C,aAAQ,MAAM,KAAK,YAAY,MAAM,KAAK,aAAc;AAAA,IAC1D,CAAC;AAGD,UAAM,kBAAkBA,cAAQ,SAAC,MAAM;AACrC,UAAI,MAAM,KAAK,SAAS;AAAa,eAAO;AAC5C,cAAS,MAAM,KAAK,YAAY,MAAM,KAAK,gBAAiB,IAAI,QAAQ,CAAC;AAAA,IAC3E,CAAC;AAGD,UAAM,iBAAiBC,cAAAA,IAAI,IAAI;AAC/B,UAAM,mBAAmBA,cAAAA,IAAI,IAAI;AACjC,UAAM,mBAAmBA,cAAAA,IAAI,IAAI;AAGjC,QAAI,MAAM,KAAK,SAAS,gBAAgB,MAAM,KAAK,WAAW,aAAa,MAAM,KAAK,WAAW,aAAa;AAG5G,qBAAe,QAAQ;AACvB,uBAAiB,QAAQ;AACzB,uBAAiB,QAAQ;AAAA,IAC3B;AAGA,UAAM,gBAAgBD,cAAQ,SAAC,MAAM;AACnC,UAAI,MAAM,KAAK,WAAW;AAAS,eAAO;AAE1C,cAAO,MAAM,KAAK,MAAI;AAAA,QACpB,KAAK;AACH,iBAAO,MAAM,KAAK,WAAW,aAAa,SAAS;AAAA,QACrD,KAAK;AACH,iBAAO,MAAM,KAAK,WAAW,aAAa,QAAQ;AAAA,QACpD,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH,CAAC;AAGD,UAAM,iBAAiBA,cAAQ,SAAC,MAAM;AACpC,UAAI,MAAM,KAAK,WAAW;AAAS,eAAO;AAC1C,aAAO,OAAO,MAAM,KAAK,IAAI;AAAA,IAC/B,CAAC;AAGD,aAAS,wBAAwB;AAE/B,cAAO,MAAM,KAAK,MAAI;AAAA,QACpB,KAAK;AACH,kBAAQ,MAAM,KAAK,aAAa,KAAK,QAAQ,CAAC;AAAA,QAChD,KAAK;AACH,kBAAQ,MAAM,KAAK,YAAY,MAAM,QAAQ,CAAC;AAAA,QAChD,KAAK;AACH,iBAAO,MAAM,KAAK,eAAe,UAAU,MAAM,KAAK,cAAc,MAAM,QAAQ,CAAC,IAAI;AAAA,QACzF,KAAK;AACH,iBAAO,MAAM,KAAK,iBAAiB,MAAM,KAAK,cAAc,SAAS,KAChE,MAAM,KAAK,cAAc,CAAC,EAAE,WAAW,KAAK,QAAQ,CAAC,IACtD;AAAA,QACN;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,aAAS,eAAe,OAAO;AAC7B,YAAM,gBAAe;AACrB,WAAK,YAAY,MAAM,KAAK,EAAE;AAAA,IAChC;AAGA,aAAS,kBAAkB,OAAO;AAChC,YAAM,gBAAe;AACrB,WAAK,UAAU;AAAA,QACb,IAAI,MAAM,KAAK;AAAA,QACf,MAAM,MAAM,KAAK;AAAA,QACjB,QAAQ,MAAM,KAAK;AAAA,MACvB,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1UA,GAAG,gBAAgB,SAAS;"}