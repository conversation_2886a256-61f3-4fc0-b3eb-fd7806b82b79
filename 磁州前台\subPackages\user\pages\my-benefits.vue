<template>
  <view class="benefits-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">我的福利</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 福利概览卡片 -->
    <view class="overview-card">
      <view class="overview-header">
        <text class="overview-title">福利概览</text>
        <text class="overview-subtitle">{{userInfo.nickname || '微信用户'}}，这是您的专属福利</text>
      </view>
      <view class="overview-content">
        <view class="overview-item" @click="navigateTo('/pages/services/coupon')">
          <text class="overview-number">{{statistics.couponCount || 0}}</text>
          <text class="overview-label">可用优惠券</text>
        </view>
        <view class="overview-item" @click="navigateTo('/pages/user/my-red-packets')">
          <text class="overview-number">{{statistics.redPacketCount || 0}}</text>
          <text class="overview-label">可领红包</text>
        </view>
        <view class="overview-item" @click="navigateTo('/subPackages/checkin/pages/points')">
          <text class="overview-number">{{statistics.pointsCount || 0}}</text>
          <text class="overview-label">积分</text>
        </view>
      </view>
    </view>
    
    <!-- 福利分类导航 -->
    <view class="benefits-nav">
      <view class="nav-item" @click="navigateTo('/pages/services/coupon')">
        <view class="nav-icon-wrap">
          <image class="nav-icon" src="/static/images/tabbar/卡券.png"></image>
          <text v-if="statistics.couponCount > 0" class="nav-badge">{{statistics.couponCount}}</text>
        </view>
        <text class="nav-label">优惠券</text>
        <text class="nav-desc">折扣、满减等优惠</text>
        <image class="nav-arrow" src="/static/images/tabbar/arrow-up.png"></image>
      </view>
      
      <view class="nav-item" @click="navigateTo('/pages/user/my-red-packets')">
        <view class="nav-icon-wrap">
          <image class="nav-icon" src="/static/images/tabbar/我的红包.png"></image>
          <text v-if="statistics.redPacketCount > 0" class="nav-badge">{{statistics.redPacketCount}}</text>
        </view>
        <text class="nav-label">红包</text>
        <text class="nav-desc">商家发放的现金红包</text>
        <image class="nav-arrow" src="/static/images/tabbar/arrow-up.png"></image>
      </view>
      
      <view class="nav-item" @click="navigateTo('/subPackages/checkin/pages/points')">
        <view class="nav-icon-wrap">
          <image class="nav-icon" src="/static/images/tabbar/每日签到.png"></image>
        </view>
        <text class="nav-label">积分商城</text>
        <text class="nav-desc">积分兑换礼品</text>
        <image class="nav-arrow" src="/static/images/tabbar/arrow-up.png"></image>
      </view>
      
      <view class="nav-item" @click="navigateTo('/pages/user-center/activity-rewards')">
        <view class="nav-icon-wrap">
          <image class="nav-icon" src="/static/images/tabbar/活动.png"></image>
          <text v-if="statistics.activityRewardCount > 0" class="nav-badge">{{statistics.activityRewardCount}}</text>
        </view>
        <text class="nav-label">活动奖励</text>
        <text class="nav-desc">参与活动获得的奖励</text>
        <image class="nav-arrow" src="/static/images/tabbar/arrow-up.png"></image>
      </view>
    </view>
    
    <!-- 最近获得的福利 -->
    <view class="recent-benefits">
      <view class="section-header">
        <text class="section-title">最近获得的福利</text>
        <text class="section-more" @click="showAllBenefits">查看全部</text>
      </view>
      
      <view class="benefits-list">
        <view v-if="recentBenefits.length > 0">
          <view class="benefit-item" v-for="(item, index) in recentBenefits" :key="index" @click="viewBenefitDetail(item)">
            <image class="benefit-icon" :src="getBenefitIcon(item.type)"></image>
            <view class="benefit-info">
              <text class="benefit-name">{{item.name}}</text>
              <text class="benefit-desc">{{item.description}}</text>
              <text class="benefit-time">{{item.time}}</text>
            </view>
            <view class="benefit-status" :class="{'status-expired': item.status === 'expired'}">
              {{getBenefitStatus(item.status)}}
            </view>
          </view>
        </view>
        <view v-else class="empty-view">
          <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
          <view class="empty-text">暂无福利记录</view>
        </view>
      </view>
    </view>
    
    <!-- 推荐活动 -->
    <view class="recommended-activities">
      <view class="section-header">
        <text class="section-title">推荐活动</text>
        <text class="section-more" @click="navigateTo('/pages/activity/list')">更多活动</text>
      </view>
      
      <scroll-view scroll-x class="activities-scroll">
        <view class="activities-list">
          <view class="activity-card" v-for="(item, index) in recommendedActivities" :key="index" @click="navigateTo('/pages/activity/detail?id=' + item.id)">
            <image class="activity-image" :src="item.image" mode="aspectFill"></image>
            <view class="activity-info">
              <text class="activity-name">{{item.name}}</text>
              <text class="activity-desc">{{item.description}}</text>
              <view class="activity-meta">
                <text class="activity-time">{{item.time}}</text>
                <text class="activity-tag">{{item.tag}}</text>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { getLocalUserInfo } from '@/utils/userProfile.js';
import { smartNavigate } from '@/utils/navigation.js';

// Vue 3 Composition API 代码开始
// 状态栏高度
const statusBarHeight = ref(20);

// 用户信息
const userInfo = ref({
  nickname: '',
  avatar: ''
});

// 统计数据
const statistics = ref({
  couponCount: 3,
  redPacketCount: 2,
  pointsCount: 520,
  activityRewardCount: 1
});

// 最近获得的福利
const recentBenefits = ref([
  {
    id: '1',
    type: 'coupon',
    name: '满100减20优惠券',
    description: '适用于全部商家',
    time: '2023-05-15 获得',
    status: 'available'
  },
  {
    id: '2',
    type: 'redPacket',
    name: '5元现金红包',
    description: '来自"品味咖啡"',
    time: '2023-05-14 获得',
    status: 'available'
  },
  {
    id: '3',
    type: 'points',
    name: '签到积分',
    description: '+10积分',
    time: '2023-05-13 获得',
    status: 'used'
  },
  {
    id: '4',
    type: 'coupon',
    name: '满50减10优惠券',
    description: '适用于餐饮美食',
    time: '2023-05-10 获得',
    status: 'expired'
  }
]);

// 推荐活动
const recommendedActivities = ref([
  {
    id: '1',
    name: '夏日狂欢购物节',
    description: '满100送50，多重好礼等你来',
    time: '05-20 至 05-30',
    tag: '购物',
    image: '/static/images/activity/shopping.jpg'
  },
  {
    id: '2',
    name: '美食品鉴会',
    description: '新店开业，免费品尝',
    time: '05-25 19:00',
    tag: '美食',
    image: '/static/images/activity/food.jpg'
  },
  {
    id: '3',
    name: '亲子嘉年华',
    description: '儿童乐园门票半价',
    time: '05-28 全天',
    tag: '亲子',
    image: '/static/images/activity/family.jpg'
  }
]);

// 生命周期钩子
onMounted(() => {
  // 获取状态栏高度
  const sysInfo = uni.getSystemInfoSync();
  statusBarHeight.value = sysInfo.statusBarHeight;
  
  // 获取用户信息
  getUserInfo();
  
  // 加载福利数据
  loadBenefitsData();
});

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 页面跳转
const navigateTo = (url) => {
  smartNavigate(url).catch(err => {
    console.error('智能导航失败:', err);
  });
};

// 获取用户信息
const getUserInfo = () => {
  // 从本地存储获取用户信息
  const localUserInfo = getLocalUserInfo();
  if (localUserInfo) {
    userInfo.value = localUserInfo;
  }
};

// 加载福利数据
const loadBenefitsData = () => {
  // 这里应该是实际的API调用，获取用户的福利数据
  // 当前使用模拟数据
  console.log('加载福利数据');
};

// 查看所有福利
const showAllBenefits = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  });
};

// 查看福利详情
const viewBenefitDetail = (item) => {
  switch(item.type) {
    case 'coupon':
      navigateTo('/pages/services/coupon');
      break;
    case 'redPacket':
      navigateTo('/pages/user/my-red-packets');
      break;
    case 'points':
      navigateTo('/subPackages/checkin/pages/points');
      break;
    default:
      uni.showToast({
        title: '查看详情',
        icon: 'none'
      });
  }
};

// 获取福利图标
const getBenefitIcon = (type) => {
  switch(type) {
    case 'coupon':
      return '/static/images/tabbar/卡券.png';
    case 'redPacket':
      return '/static/images/tabbar/我的红包.png';
    case 'points':
      return '/static/images/tabbar/每日签到.png';
    default:
      return '/static/images/tabbar/活动.png';
  }
};

// 获取福利状态文本
const getBenefitStatus = (status) => {
  switch(status) {
    case 'available':
      return '可使用';
    case 'used':
      return '已使用';
    case 'expired':
      return '已过期';
    default:
      return '未知';
  }
};
// Vue 3 Composition API 代码结束
</script>

<style lang="scss" scoped>
.benefits-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 20rpx;
}

/* 导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  background: linear-gradient(135deg, #3a7afe, #6ca6ff);
  color: #fff;
  z-index: 100;
}

.navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 36rpx;
  height: 36rpx;
}

.navbar-title {
  font-size: 18px;
  font-weight: 500;
}

.navbar-right {
  width: 60rpx;
}

/* 福利概览卡片 */
.overview-card {
  margin: calc(44px + var(--status-bar-height)) 20rpx 20rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, #3a7afe, #6ca6ff);
  border-radius: 16rpx;
  color: #fff;
  box-shadow: 0 4rpx 20rpx rgba(106, 166, 255, 0.3);
}

.overview-header {
  margin-bottom: 30rpx;
}

.overview-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  display: block;
}

.overview-subtitle {
  font-size: 24rpx;
  opacity: 0.8;
}

.overview-content {
  display: flex;
  justify-content: space-between;
}

.overview-item {
  text-align: center;
  flex: 1;
}

.overview-number {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
}

.overview-label {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 福利分类导航 */
.benefits-nav {
  margin: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.nav-item:last-child {
  border-bottom: none;
}

.nav-icon-wrap {
  width: 80rpx;
  height: 80rpx;
  background-color: #f0f5ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  position: relative;
}

.nav-icon {
  width: 40rpx;
  height: 40rpx;
}

.nav-badge {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  background-color: #ff4d4f;
  color: #fff;
  font-size: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6rpx;
}

.nav-label {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  flex: 1;
}

.nav-desc {
  font-size: 24rpx;
  color: #999;
  margin-right: 20rpx;
}

.nav-arrow {
  width: 32rpx;
  height: 32rpx;
  transform: rotate(90deg);
}

/* 最近获得的福利 */
.recent-benefits {
  margin: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.section-more {
  font-size: 24rpx;
  color: #3a7afe;
}

.benefits-list {
  margin-top: 20rpx;
}

.benefit-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.benefit-item:last-child {
  border-bottom: none;
}

.benefit-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}

.benefit-info {
  flex: 1;
}

.benefit-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
  display: block;
}

.benefit-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 6rpx;
  display: block;
}

.benefit-time {
  font-size: 22rpx;
  color: #999;
  display: block;
}

.benefit-status {
  font-size: 24rpx;
  color: #3a7afe;
  padding: 4rpx 12rpx;
  background-color: #f0f5ff;
  border-radius: 6rpx;
}

.status-expired {
  color: #999;
  background-color: #f5f5f5;
}

/* 推荐活动 */
.recommended-activities {
  margin: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.activities-scroll {
  width: 100%;
  white-space: nowrap;
}

.activities-list {
  display: flex;
  padding: 10rpx 0;
}

.activity-card {
  width: 400rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  display: inline-block;
}

.activity-image {
  width: 100%;
  height: 200rpx;
}

.activity-info {
  padding: 16rpx;
}

.activity-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 6rpx;
  display: block;
  white-space: normal;
}

.activity-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.activity-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.activity-time {
  font-size: 22rpx;
  color: #999;
}

.activity-tag {
  font-size: 22rpx;
  color: #3a7afe;
  background-color: #f0f5ff;
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
}

/* 空状态 */
.empty-view {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style> 