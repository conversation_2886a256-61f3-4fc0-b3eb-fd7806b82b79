{"version": 3, "file": "index.js", "sources": ["subPackages/merchant-admin-customer/pages/customer/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tY3VzdG9tZXJccGFnZXNcY3VzdG9tZXJcaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"customer-management-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">客户运营</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-icon\">?</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 客户数据概览 -->\r\n    <view class=\"data-overview\">\r\n      <view class=\"overview-header\">\r\n        <text class=\"overview-title\">客户数据概览</text>\r\n        <view class=\"date-selector\">\r\n          <text class=\"date-text\">{{selectedDateRange}}</text>\r\n          <view class=\"selector-icon\"></view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"metric-cards\">\r\n        <view class=\"metric-card\">\r\n          <view class=\"metric-icon customers\"></view>\r\n          <view class=\"metric-data\">\r\n            <text class=\"metric-value\">{{customerMetrics.totalCustomers}}</text>\r\n            <text class=\"metric-label\">总客户数</text>\r\n          </view>\r\n          <view class=\"metric-trend up\">\r\n            <view class=\"trend-icon\"></view>\r\n            <text class=\"trend-text\">{{customerMetrics.customerGrowth}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"metric-card\">\r\n          <view class=\"metric-icon new\"></view>\r\n          <view class=\"metric-data\">\r\n            <text class=\"metric-value\">{{customerMetrics.newCustomers}}</text>\r\n            <text class=\"metric-label\">新增客户</text>\r\n          </view>\r\n          <view class=\"metric-trend up\">\r\n            <view class=\"trend-icon\"></view>\r\n            <text class=\"trend-text\">{{customerMetrics.newCustomerGrowth}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"metric-card\">\r\n          <view class=\"metric-icon active\"></view>\r\n          <view class=\"metric-data\">\r\n            <text class=\"metric-value\">{{customerMetrics.activeRate}}%</text>\r\n            <text class=\"metric-label\">活跃率</text>\r\n          </view>\r\n          <view class=\"metric-trend down\">\r\n            <view class=\"trend-icon\"></view>\r\n            <text class=\"trend-text\">{{customerMetrics.activeRateChange}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"metric-card\">\r\n          <view class=\"metric-icon repurchase\"></view>\r\n          <view class=\"metric-data\">\r\n            <text class=\"metric-value\">{{customerMetrics.repurchaseRate}}%</text>\r\n            <text class=\"metric-label\">复购率</text>\r\n          </view>\r\n          <view class=\"metric-trend up\">\r\n            <view class=\"trend-icon\"></view>\r\n            <text class=\"trend-text\">{{customerMetrics.repurchaseRateChange}}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 客户分群分析 -->\r\n    <view class=\"customer-segments\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">客户分群分析</text>\r\n        <text class=\"section-action\" @tap=\"navigateTo('/pages/customer/segments')\">管理分群</text>\r\n      </view>\r\n      \r\n      <view class=\"segment-tabs\">\r\n        <view \r\n          class=\"segment-tab\" \r\n          v-for=\"(tab, index) in segmentTabs\" \r\n          :key=\"index\" \r\n          :class=\"{ active: currentSegment === tab.id }\"\r\n          @tap=\"switchSegment(tab.id)\">\r\n          {{tab.name}}\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"segment-info-card\">\r\n        <view class=\"segment-header\">\r\n          <view class=\"segment-title-container\">\r\n            <text class=\"segment-title\">{{currentSegmentData.name}}</text>\r\n            <view class=\"segment-tag\" :class=\"currentSegmentData.tagType\">{{currentSegmentData.tag}}</view>\r\n          </view>\r\n          <view class=\"segment-action\" @tap=\"targetSegment(currentSegmentData)\">\r\n            <text>定向营销</text>\r\n            <view class=\"action-icon\"></view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"segment-metrics\">\r\n          <view class=\"segment-metric\">\r\n            <text class=\"metric-value\">{{currentSegmentData.customerCount}}</text>\r\n            <text class=\"metric-label\">客户数量</text>\r\n          </view>\r\n          <view class=\"segment-metric\">\r\n            <text class=\"metric-value\">¥{{currentSegmentData.avgValue}}</text>\r\n            <text class=\"metric-label\">客户价值</text>\r\n          </view>\r\n          <view class=\"segment-metric\">\r\n            <text class=\"metric-value\">{{currentSegmentData.purchaseFrequency}}</text>\r\n            <text class=\"metric-label\">购买频次</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"segment-chart\">\r\n          <canvas canvas-id=\"segmentChart\" class=\"chart-canvas\"></canvas>\r\n          <text class=\"chart-title\">消费趋势 (近6个月)</text>\r\n        </view>\r\n        \r\n        <view class=\"segment-traits\">\r\n          <text class=\"traits-title\">客群特征:</text>\r\n          <view class=\"traits-tags\">\r\n            <view class=\"trait-tag\" v-for=\"(trait, index) in currentSegmentData.traits\" :key=\"index\">\r\n              {{trait}}\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"segment-actions\">\r\n          <view class=\"action-btn detail\" @tap=\"viewSegmentDetail(currentSegmentData)\">\r\n            <text>查看详情</text>\r\n          </view>\r\n          <view class=\"action-btn export\" @tap=\"exportSegmentData(currentSegmentData)\">\r\n            <text>导出数据</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 客户生命周期 -->\r\n    <view class=\"lifecycle-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">客户生命周期</text>\r\n        <text class=\"section-action\" @tap=\"navigateTo('/pages/customer/lifecycle')\">查看详情</text>\r\n      </view>\r\n      \r\n      <view class=\"lifecycle-card\">\r\n        <view class=\"lifecycle-chart\">\r\n          <image class=\"lifecycle-image\" src=\"/static/images/lifecycle-chart.png\" mode=\"widthFix\"></image>\r\n        </view>\r\n        \r\n        <view class=\"lifecycle-metrics\">\r\n          <view class=\"lifecycle-stage\">\r\n            <view class=\"stage-header\">\r\n              <view class=\"stage-icon new\"></view>\r\n              <text class=\"stage-name\">新客户</text>\r\n            </view>\r\n            <view class=\"stage-data\">\r\n              <text class=\"stage-value\">{{lifecycle.newCustomers}}</text>\r\n              <view class=\"stage-trend\" :class=\"lifecycle.newTrend\">\r\n                <view class=\"trend-icon\"></view>\r\n                <text class=\"trend-value\">{{lifecycle.newGrowth}}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"lifecycle-stage\">\r\n            <view class=\"stage-header\">\r\n              <view class=\"stage-icon growing\"></view>\r\n              <text class=\"stage-name\">成长期</text>\r\n            </view>\r\n            <view class=\"stage-data\">\r\n              <text class=\"stage-value\">{{lifecycle.growingCustomers}}</text>\r\n              <view class=\"stage-trend\" :class=\"lifecycle.growingTrend\">\r\n                <view class=\"trend-icon\"></view>\r\n                <text class=\"trend-value\">{{lifecycle.growingGrowth}}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"lifecycle-stage\">\r\n            <view class=\"stage-header\">\r\n              <view class=\"stage-icon mature\"></view>\r\n              <text class=\"stage-name\">成熟期</text>\r\n            </view>\r\n            <view class=\"stage-data\">\r\n              <text class=\"stage-value\">{{lifecycle.matureCustomers}}</text>\r\n              <view class=\"stage-trend\" :class=\"lifecycle.matureTrend\">\r\n                <view class=\"trend-icon\"></view>\r\n                <text class=\"trend-value\">{{lifecycle.matureGrowth}}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"lifecycle-stage\">\r\n            <view class=\"stage-header\">\r\n              <view class=\"stage-icon risk\"></view>\r\n              <text class=\"stage-name\">流失风险</text>\r\n            </view>\r\n            <view class=\"stage-data\">\r\n              <text class=\"stage-value\">{{lifecycle.riskCustomers}}</text>\r\n              <view class=\"stage-trend\" :class=\"lifecycle.riskTrend\">\r\n                <view class=\"trend-icon\"></view>\r\n                <text class=\"trend-value\">{{lifecycle.riskGrowth}}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"lifecycle-insights\">\r\n          <view class=\"insight-icon\"></view>\r\n          <text class=\"insight-text\">{{lifecycle.insight}}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- RFM 价值矩阵 -->\r\n    <view class=\"rfm-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">客户价值矩阵</text>\r\n        <text class=\"section-action\" @tap=\"navigateTo('/pages/customer/rfm')\">查看详情</text>\r\n      </view>\r\n      \r\n      <view class=\"rfm-card\">\r\n        <view class=\"rfm-matrix\">\r\n          <view class=\"rfm-row\" v-for=\"(row, rowIndex) in rfmMatrix\" :key=\"'row-'+rowIndex\">\r\n            <view \r\n              class=\"rfm-cell\" \r\n              v-for=\"(cell, cellIndex) in row\" \r\n              :key=\"'cell-'+cellIndex\"\r\n              :class=\"[cell.type]\"\r\n              @tap=\"viewRfmSegment(cell)\">\r\n              <text class=\"cell-name\">{{cell.name}}</text>\r\n              <text class=\"cell-value\">{{cell.value}}%</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"rfm-legend\">\r\n          <text class=\"legend-title\">价值: </text>\r\n          <view class=\"legend-item\">\r\n            <view class=\"legend-color high\"></view>\r\n            <text class=\"legend-text\">高价值</text>\r\n          </view>\r\n          <view class=\"legend-item\">\r\n            <view class=\"legend-color medium\"></view>\r\n            <text class=\"legend-text\">中价值</text>\r\n          </view>\r\n          <view class=\"legend-item\">\r\n            <view class=\"legend-color low\"></view>\r\n            <text class=\"legend-text\">低价值</text>\r\n          </view>\r\n          <view class=\"legend-item\">\r\n            <view class=\"legend-color potential\"></view>\r\n            <text class=\"legend-text\">潜力客户</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"rfm-desc\">\r\n          <text class=\"desc-title\">RFM分析: </text>\r\n          <text class=\"desc-text\">基于客户最近一次消费(Recency)、消费频率(Frequency)及消费金额(Monetary)进行客户分群</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 客户标签管理 -->\r\n    <view class=\"tag-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">客户标签管理</text>\r\n        <text class=\"section-action\" @tap=\"navigateTo('/pages/customer/tags')\">管理标签</text>\r\n      </view>\r\n      \r\n      <view class=\"tag-card\">\r\n        <view class=\"tag-categories\">\r\n          <view \r\n            class=\"tag-category\" \r\n            v-for=\"(category, index) in tagCategories\" \r\n            :key=\"index\"\r\n            :class=\"{ active: currentTagCategory === category.id }\"\r\n            @tap=\"switchTagCategory(category.id)\">\r\n            {{category.name}}\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"tag-cloud\">\r\n          <view \r\n            class=\"tag-item\" \r\n            v-for=\"(tag, index) in currentCategoryTags\" \r\n            :key=\"index\"\r\n            :class=\"tag.size\"\r\n            @tap=\"viewTagCustomers(tag)\">\r\n            <text>{{tag.name}}</text>\r\n            <text class=\"tag-count\">({{tag.count}})</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"tag-footer\">\r\n          <view class=\"create-tag-btn\" @tap=\"createNewTag\">\r\n            <view class=\"btn-icon\"></view>\r\n            <text class=\"btn-text\">创建新标签</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 客户互动管理 -->\r\n    <view class=\"interaction-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">客户互动</text>\r\n        <text class=\"section-action\" @tap=\"navigateTo('/pages/customer/interaction')\">全部消息</text>\r\n      </view>\r\n      \r\n      <view class=\"message-stats\">\r\n        <view class=\"message-stat-item\">\r\n          <text class=\"stat-value\">{{messageStats.unread}}</text>\r\n          <text class=\"stat-label\">未读消息</text>\r\n        </view>\r\n        <view class=\"message-stat-item\">\r\n          <text class=\"stat-value\">{{messageStats.today}}</text>\r\n          <text class=\"stat-label\">今日消息</text>\r\n        </view>\r\n        <view class=\"message-stat-item\">\r\n          <text class=\"stat-value\">{{messageStats.responseRate}}%</text>\r\n          <text class=\"stat-label\">响应率</text>\r\n        </view>\r\n        <view class=\"message-stat-item\">\r\n          <text class=\"stat-value\">{{messageStats.responseTime}}</text>\r\n          <text class=\"stat-label\">平均响应时间</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"recent-messages\">\r\n        <view class=\"message-item\" v-for=\"(message, index) in recentMessages\" :key=\"index\" @tap=\"viewConversation(message)\">\r\n          <image class=\"customer-avatar\" :src=\"message.customerAvatar\" mode=\"aspectFill\"></image>\r\n          <view class=\"message-content\">\r\n            <view class=\"message-header\">\r\n              <text class=\"customer-name\">{{message.customerName}}</text>\r\n              <text class=\"message-time\">{{message.time}}</text>\r\n            </view>\r\n            <text class=\"message-preview\">{{message.content}}</text>\r\n          </view>\r\n          <view class=\"message-badge\" v-if=\"message.unreadCount > 0\">{{message.unreadCount}}</view>\r\n        </view>\r\n        \r\n        <view class=\"interaction-footer\">\r\n          <view class=\"action-btn\" @tap=\"navigateTo('/pages/customer/message')\">\r\n            <view class=\"btn-icon message\"></view>\r\n            <text class=\"btn-text\">消息中心</text>\r\n          </view>\r\n          <view class=\"action-btn\" @tap=\"navigateTo('/pages/customer/notification')\">\r\n            <view class=\"btn-icon notify\"></view>\r\n            <text class=\"btn-text\">发送通知</text>\r\n          </view>\r\n          <view class=\"action-btn\" @tap=\"navigateTo('/pages/customer/survey')\">\r\n            <view class=\"btn-icon survey\"></view>\r\n            <text class=\"btn-text\">问卷调研</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 选中的日期范围\r\n      selectedDateRange: '过去30天',\r\n      \r\n      // 客户数据概览\r\n      customerMetrics: {\r\n        totalCustomers: '3,216',\r\n        customerGrowth: '+8.3%',\r\n        newCustomers: '238',\r\n        newCustomerGrowth: '+12.7%',\r\n        activeRate: '42.8',\r\n        activeRateChange: '-2.5%',\r\n        repurchaseRate: '31.5',\r\n        repurchaseRateChange: '+5.4%'\r\n      },\r\n      \r\n      // 客户分群标签页\r\n      segmentTabs: [\r\n        { id: 1, name: '高价值客户' },\r\n        { id: 2, name: '活跃消费者' },\r\n        { id: 3, name: '新客户' },\r\n        { id: 4, name: '流失风险' },\r\n        { id: 5, name: '低频消费' },\r\n      ],\r\n      currentSegment: 1,\r\n      \r\n      // 分群数据\r\n      segmentData: [\r\n        {\r\n          id: 1,\r\n          name: '高价值客户',\r\n          tagType: 'high-value',\r\n          tag: '高价值',\r\n          customerCount: '325',\r\n          avgValue: '4,625',\r\n          purchaseFrequency: '3.7次/月',\r\n          traits: ['高频消费者', '忠诚会员', '品质敏感', '夜间购物', '追求体验'],\r\n          chartData: [8500, 9200, 9800, 10200, 11500, 12300]\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '活跃消费者',\r\n          tagType: 'active',\r\n          tag: '活跃',\r\n          customerCount: '462',\r\n          avgValue: '1,850',\r\n          purchaseFrequency: '2.8次/月',\r\n          traits: ['周末消费者', '热衷促销', '追求性价比', '多频次', '购物篮多样'],\r\n          chartData: [3600, 3900, 4200, 4500, 4800, 5100]\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '新客户',\r\n          tagType: 'new',\r\n          tag: '新客',\r\n          customerCount: '538',\r\n          avgValue: '960',\r\n          purchaseFrequency: '1.2次/月',\r\n          traits: ['价格敏感', '尝试购买', '小额消费', '移动端', '社交媒体引导'],\r\n          chartData: [0, 0, 0, 0, 350, 950]\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '流失风险',\r\n          tagType: 'risk',\r\n          tag: '风险',\r\n          customerCount: '186',\r\n          avgValue: '2,340',\r\n          purchaseFrequency: '0.5次/月',\r\n          traits: ['消费频次下降', '曾为高价值', '购买周期拉长', '有投诉历史', '客服互动'],\r\n          chartData: [5200, 4800, 4200, 3600, 3200, 2800]\r\n        },\r\n        {\r\n          id: 5,\r\n          name: '低频消费',\r\n          tagType: 'low',\r\n          tag: '低频',\r\n          customerCount: '1,705',\r\n          avgValue: '780',\r\n          purchaseFrequency: '0.7次/月',\r\n          traits: ['季节性消费', '特定品类', '低参与度', '价格敏感', '偶发性购买'],\r\n          chartData: [1200, 1050, 1350, 1100, 950, 1300]\r\n        }\r\n      ],\r\n      \r\n      // 客户生命周期数据\r\n      lifecycle: {\r\n        newCustomers: '538',\r\n        newTrend: 'up',\r\n        newGrowth: '+16.7%',\r\n        growingCustomers: '782',\r\n        growingTrend: 'up',\r\n        growingGrowth: '+8.3%',\r\n        matureCustomers: '1,246',\r\n        matureTrend: 'down',\r\n        matureGrowth: '-2.1%',\r\n        riskCustomers: '650',\r\n        riskTrend: 'down',\r\n        riskGrowth: '-5.3%',\r\n        insight: '新客户增长强劲，但成熟客户群体略有下降，建议关注成熟客户的留存策略。'\r\n      },\r\n      \r\n      // RFM价值矩阵\r\n      rfmMatrix: [\r\n        [\r\n          { name: '重要价值', value: 12, type: 'high' },\r\n          { name: '重要保持', value: 8, type: 'high' },\r\n          { name: '重要发展', value: 6, type: 'medium' },\r\n        ],\r\n        [\r\n          { name: '一般价值', value: 10, type: 'medium' },\r\n          { name: '一般保持', value: 14, type: 'medium' },\r\n          { name: '一般发展', value: 11, type: 'low' },\r\n        ],\r\n        [\r\n          { name: '潜力客户', value: 7, type: 'potential' },\r\n          { name: '新客发展', value: 18, type: 'potential' },\r\n          { name: '低价值', value: 14, type: 'low' },\r\n        ]\r\n      ],\r\n      \r\n      // 客户标签分类\r\n      tagCategories: [\r\n        { id: 1, name: '消费习惯' },\r\n        { id: 2, name: '生活方式' },\r\n        { id: 3, name: '人口特征' },\r\n        { id: 4, name: '行为特征' },\r\n        { id: 5, name: '偏好喜好' }\r\n      ],\r\n      currentTagCategory: 1,\r\n      \r\n      // 标签数据\r\n      tagData: {\r\n        1: [ // 消费习惯标签\r\n          { name: '夜间购物', count: 625, size: 'large' },\r\n          { name: '周末消费', count: 842, size: 'large' },\r\n          { name: '促销敏感', count: 1236, size: 'xlarge' },\r\n          { name: '高频次', count: 465, size: 'medium' },\r\n          { name: '大额消费', count: 284, size: 'medium' },\r\n          { name: '理性决策', count: 356, size: 'medium' },\r\n          { name: '冲动购买', count: 478, size: 'medium' },\r\n          { name: '季节性', count: 592, size: 'large' },\r\n          { name: '特殊节日', count: 734, size: 'large' },\r\n          { name: '长期积累', count: 185, size: 'small' },\r\n          { name: '预算型', count: 605, size: 'large' },\r\n          { name: '奢侈型', count: 146, size: 'small' }\r\n        ],\r\n        2: [ // 生活方式标签\r\n          { name: '重视家庭', count: 865, size: 'large' },\r\n          { name: '现代简约', count: 742, size: 'large' },\r\n          { name: '工作繁忙', count: 638, size: 'large' },\r\n          { name: '喜爱旅行', count: 425, size: 'medium' },\r\n          { name: '健康生活', count: 528, size: 'medium' },\r\n          { name: '社交活跃', count: 356, size: 'medium' },\r\n          { name: '宅家爱好者', count: 492, size: 'medium' },\r\n          { name: '典雅风格', count: 168, size: 'small' },\r\n          { name: '环保意识', count: 274, size: 'medium' },\r\n          { name: '科技发烧友', count: 183, size: 'small' },\r\n          { name: '追求品质', count: 427, size: 'medium' }\r\n        ],\r\n        3: [ // 人口特征标签\r\n          { name: '25-34岁', count: 782, size: 'large' },\r\n          { name: '35-44岁', count: 864, size: 'large' },\r\n          { name: '城市居民', count: 1568, size: 'xlarge' },\r\n          { name: '高收入', count: 485, size: 'medium' },\r\n          { name: '中等收入', count: 1246, size: 'xlarge' },\r\n          { name: '已婚', count: 956, size: 'large' },\r\n          { name: '有子女', count: 842, size: 'large' },\r\n          { name: '高学历', count: 685, size: 'large' },\r\n          { name: '女性', count: 1842, size: 'xlarge' },\r\n          { name: '男性', count: 1374, size: 'xlarge' }\r\n        ],\r\n        4: [ // 行为特征标签\r\n          { name: '多渠道', count: 562, size: 'large' },\r\n          { name: '移动端', count: 1675, size: 'xlarge' },\r\n          { name: '评论积极', count: 246, size: 'medium' },\r\n          { name: '经常退货', count: 128, size: 'small' },\r\n          { name: '分享推荐', count: 184, size: 'small' },\r\n          { name: '货比三家', count: 567, size: 'large' },\r\n          { name: '忠诚会员', count: 425, size: 'medium' },\r\n          { name: '投诉历史', count: 78, size: 'xsmall' },\r\n          { name: '客服咨询', count: 265, size: 'medium' },\r\n          { name: '自助服务', count: 684, size: 'large' },\r\n          { name: '快速决策', count: 347, size: 'medium' },\r\n          { name: '深度研究', count: 428, size: 'medium' }\r\n        ],\r\n        5: [ // 偏好喜好标签\r\n          { name: '北欧风格', count: 456, size: 'medium' },\r\n          { name: '简约设计', count: 687, size: 'large' },\r\n          { name: '环保材质', count: 345, size: 'medium' },\r\n          { name: '明亮色调', count: 426, size: 'medium' },\r\n          { name: '温暖色系', count: 372, size: 'medium' },\r\n          { name: '多功能', count: 582, size: 'large' },\r\n          { name: '极简主义', count: 428, size: 'medium' },\r\n          { name: '复古风格', count: 216, size: 'small' },\r\n          { name: '高科技', count: 284, size: 'medium' },\r\n          { name: '实用主义', count: 745, size: 'large' },\r\n          { name: '精致装饰', count: 367, size: 'medium' },\r\n          { name: '舒适体验', count: 648, size: 'large' }\r\n        ]\r\n      },\r\n      \r\n      // 客户互动数据\r\n      messageStats: {\r\n        unread: 16,\r\n        today: 28,\r\n        responseRate: 93.5,\r\n        responseTime: '8分钟'\r\n      },\r\n      \r\n      // 最近消息\r\n      recentMessages: [\r\n        {\r\n          id: 1,\r\n          customerName: '张先生',\r\n          customerAvatar: '/static/images/avatar-1.png',\r\n          time: '10分钟前',\r\n          content: '请问我订购的沙发什么时候能到货？',\r\n          unreadCount: 1\r\n        },\r\n        {\r\n          id: 2,\r\n          customerName: '王女士',\r\n          customerAvatar: '/static/images/avatar-2.png',\r\n          time: '28分钟前',\r\n          content: '收到的床垫有点问题，可以联系我处理一下吗？',\r\n          unreadCount: 2\r\n        },\r\n        {\r\n          id: 3,\r\n          customerName: '李先生',\r\n          customerAvatar: '/static/images/avatar-3.png',\r\n          time: '1小时前',\r\n          content: '请问你们有没有提供上门测量服务？',\r\n          unreadCount: 0\r\n        },\r\n        {\r\n          id: 4,\r\n          customerName: '赵女士',\r\n          customerAvatar: '/static/images/avatar-4.png',\r\n          time: '2小时前',\r\n          content: '窗帘的安装视频我看了，但还是有些不明白...',\r\n          unreadCount: 0\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    // 获取当前选中的分群数据\r\n    currentSegmentData() {\r\n      return this.segmentData.find(segment => segment.id === this.currentSegment) || this.segmentData[0];\r\n    },\r\n    \r\n    // 获取当前标签分类的标签\r\n    currentCategoryTags() {\r\n      return this.tagData[this.currentTagCategory] || [];\r\n    }\r\n  },\r\n  \r\n  mounted() {\r\n    // 在实际项目中，这里应该调用API获取数据\r\n    this.$nextTick(() => {\r\n      this.drawSegmentChart();\r\n    });\r\n  },\r\n  \r\n  methods: {\r\n    // 基础导航\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    \r\n    navigateTo(url) {\r\n      uni.navigateTo({\r\n        url: url\r\n      });\r\n    },\r\n    \r\n    // 切换客户分群\r\n    switchSegment(segmentId) {\r\n      this.currentSegment = segmentId;\r\n      this.$nextTick(() => {\r\n        this.drawSegmentChart();\r\n      });\r\n    },\r\n    \r\n    // 定向营销客群\r\n    targetSegment(segment) {\r\n      uni.navigateTo({\r\n        url: `/pages/customer/target?id=${segment.id}&name=${segment.name}`\r\n      });\r\n    },\r\n    \r\n    // 查看分群详情\r\n    viewSegmentDetail(segment) {\r\n      uni.navigateTo({\r\n        url: `/pages/customer/segment-detail?id=${segment.id}`\r\n      });\r\n    },\r\n    \r\n    // 导出分群数据\r\n    exportSegmentData(segment) {\r\n      uni.showToast({\r\n        title: `正在导出${segment.name}数据`,\r\n        icon: 'none'\r\n      });\r\n    },\r\n    \r\n    // 绘制分群趋势图表\r\n    drawSegmentChart() {\r\n      // 在实际项目中，应该使用chart组件绘制图表\r\n      // 这里只是模拟\r\n      console.log('绘制图表，数据：', this.currentSegmentData.chartData);\r\n      // 如果需要实现真实图表，可以使用uCharts、ECharts等图表库\r\n    },\r\n    \r\n    // 查看RFM客群详情\r\n    viewRfmSegment(cell) {\r\n      uni.navigateTo({\r\n        url: `/pages/customer/rfm-detail?name=${cell.name}&type=${cell.type}`\r\n      });\r\n    },\r\n    \r\n    // 切换标签分类\r\n    switchTagCategory(categoryId) {\r\n      this.currentTagCategory = categoryId;\r\n    },\r\n    \r\n    // 查看标签客户列表\r\n    viewTagCustomers(tag) {\r\n      uni.navigateTo({\r\n        url: `/pages/customer/tag-customers?tag=${tag.name}&count=${tag.count}`\r\n      });\r\n    },\r\n    \r\n    // 创建新标签\r\n    createNewTag() {\r\n      uni.navigateTo({\r\n        url: '/pages/customer/create-tag'\r\n      });\r\n    },\r\n    \r\n    // 查看聊天记录\r\n    viewConversation(message) {\r\n      uni.navigateTo({\r\n        url: `/pages/customer/conversation?id=${message.id}&name=${message.customerName}`\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n/* 页面容器 */\r\n.customer-management-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 导航栏 */\r\n.navbar {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 44px 16px 10px;\r\n  background: linear-gradient(135deg, #1677FF, #065DD2);\r\n  position: relative;\r\n  z-index: 100;\r\n}\r\n\r\n.navbar-back {\r\n  width: 40px;\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  color: #fff;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  flex: 1;\r\n  text-align: center;\r\n}\r\n\r\n.navbar-right {\r\n  width: 40px;\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.help-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #fff;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 客户数据概览 */\r\n.data-overview {\r\n  background: #fff;\r\n  border-radius: 10px;\r\n  margin: 15px;\r\n  padding: 15px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);\r\n}\r\n\r\n.overview-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.overview-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.date-selector {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.selector-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  margin-left: 5px;\r\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%23666666\"><path d=\"M7 10l5 5 5-5z\"/></svg>');\r\n  background-repeat: no-repeat;\r\n  background-size: cover;\r\n}\r\n\r\n.metric-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: -5px;\r\n}\r\n\r\n.metric-card {\r\n  width: calc(50% - 10px);\r\n  background: #F8FAFC;\r\n  border-radius: 10px;\r\n  padding: 15px;\r\n  margin: 5px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.metric-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 20px;\r\n  margin-right: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.metric-icon.customers {\r\n  background: linear-gradient(135deg, #3498DB, #2980B9);\r\n}\r\n\r\n.metric-icon.new {\r\n  background: linear-gradient(135deg, #2ECC71, #27AE60);\r\n}\r\n\r\n.metric-icon.active {\r\n  background: linear-gradient(135deg, #F39C12, #E67E22);\r\n}\r\n\r\n.metric-icon.repurchase {\r\n  background: linear-gradient(135deg, #9B59B6, #8E44AD);\r\n}\r\n\r\n.metric-data {\r\n  flex: 1;\r\n}\r\n\r\n.metric-value {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 5px;\r\n  display: block;\r\n}\r\n\r\n.metric-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.metric-trend {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 12px;\r\n  margin-left: 5px;\r\n}\r\n\r\n.metric-trend.up {\r\n  color: #52C41A;\r\n}\r\n\r\n.metric-trend.down {\r\n  color: #FF4D4F;\r\n}\r\n\r\n.trend-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  margin-right: 2px;\r\n}\r\n\r\n.metric-trend.up .trend-icon {\r\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%2352C41A\"><path d=\"M7 14l5-5 5 5z\"/></svg>');\r\n  background-repeat: no-repeat;\r\n  background-size: cover;\r\n}\r\n\r\n.metric-trend.down .trend-icon {\r\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%23FF4D4F\"><path d=\"M7 10l5 5 5-5z\"/></svg>');\r\n  background-repeat: no-repeat;\r\n  background-size: cover;\r\n}\r\n\r\n/* 通用区块样式 */\r\n.customer-segments, .lifecycle-section, .rfm-section, .tag-section, .interaction-section {\r\n  background: #fff;\r\n  border-radius: 10px;\r\n  margin: 15px;\r\n  padding: 15px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.section-action {\r\n  font-size: 14px;\r\n  color: #1677FF;\r\n}\r\n\r\n/* 客户分群标签页 */\r\n.segment-tabs {\r\n  display: flex;\r\n  overflow-x: auto;\r\n  white-space: nowrap;\r\n  margin-bottom: 15px;\r\n  scrollbar-width: none; /* Firefox */\r\n}\r\n\r\n.segment-tabs::-webkit-scrollbar {\r\n  display: none; /* Chrome/Safari/Opera */\r\n}\r\n\r\n.segment-tab {\r\n  padding: 8px 16px;\r\n  margin-right: 10px;\r\n  font-size: 14px;\r\n  color: #666;\r\n  background: #F5F7FA;\r\n  border-radius: 16px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.segment-tab.active {\r\n  background: #F0F7FF;\r\n  color: #1677FF;\r\n  font-weight: 500;\r\n}\r\n\r\n.segment-info-card {\r\n  background: #F8FAFC;\r\n  border-radius: 10px;\r\n  padding: 15px;\r\n}\r\n\r\n.segment-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.segment-title-container {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.segment-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-right: 8px;\r\n}\r\n\r\n.segment-tag {\r\n  padding: 2px 8px;\r\n  border-radius: 10px;\r\n  font-size: 12px;\r\n}\r\n\r\n.segment-tag.high-value {\r\n  background: #E6F7FF;\r\n  color: #1677FF;\r\n}\r\n\r\n.segment-tag.active {\r\n  background: #F6FFED;\r\n  color: #52C41A;\r\n}\r\n\r\n.segment-tag.new {\r\n  background: #FCF5EB;\r\n  color: #FA8C16;\r\n}\r\n\r\n.segment-tag.risk {\r\n  background: #FFF1F0;\r\n  color: #FF4D4F;\r\n}\r\n\r\n.segment-tag.low {\r\n  background: #F5F5F5;\r\n  color: #999;\r\n}\r\n\r\n.segment-action {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 14px;\r\n  color: #1677FF;\r\n}\r\n\r\n.action-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  margin-left: 5px;\r\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%231677FF\"><path d=\"M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z\"/></svg>');\r\n  background-repeat: no-repeat;\r\n  background-size: cover;\r\n}\r\n\r\n.segment-metrics {\r\n  display: flex;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.segment-metric {\r\n  flex: 1;\r\n  text-align: center;\r\n  padding: 0 10px;\r\n}\r\n\r\n.segment-metric:not(:last-child) {\r\n  border-right: 1px solid #EAEAEA;\r\n}\r\n\r\n.segment-chart {\r\n  height: 200px;\r\n  margin-bottom: 15px;\r\n  position: relative;\r\n}\r\n\r\n.chart-canvas {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.chart-title {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  text-align: center;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.segment-traits {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.traits-title {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #333;\r\n  margin-bottom: 10px;\r\n  display: block;\r\n}\r\n\r\n.traits-tags {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.trait-tag {\r\n  padding: 6px 12px;\r\n  background: #F5F7FA;\r\n  border-radius: 16px;\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin: 0 8px 8px 0;\r\n}\r\n\r\n.segment-actions {\r\n  display: flex;\r\n}\r\n\r\n.action-btn {\r\n  flex: 1;\r\n  height: 40px;\r\n  border-radius: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  margin: 0 5px;\r\n}\r\n\r\n.action-btn.detail {\r\n  background: #F0F7FF;\r\n  color: #1677FF;\r\n}\r\n\r\n.action-btn.export {\r\n  background: #F5F5F5;\r\n  color: #666;\r\n}\r\n\r\n/* 客户生命周期 */\r\n.lifecycle-card {\r\n  background: #F8FAFC;\r\n  border-radius: 10px;\r\n  padding: 15px;\r\n}\r\n\r\n.lifecycle-chart {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.lifecycle-image {\r\n  width: 100%;\r\n  height: auto;\r\n  max-height: 180px;\r\n  border-radius: 8px;\r\n}\r\n\r\n.lifecycle-metrics {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: -5px;\r\n}\r\n\r\n.lifecycle-stage {\r\n  width: calc(50% - 10px);\r\n  margin: 5px;\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 10px;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);\r\n}\r\n\r\n.stage-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.stage-icon {\r\n  width: 16px;\r\n  height: 16px;\r\n  border-radius: 8px;\r\n  margin-right: 8px;\r\n}\r\n\r\n.stage-icon.new {\r\n  background: #52C41A;\r\n}\r\n\r\n.stage-icon.growing {\r\n  background: #1677FF;\r\n}\r\n\r\n.stage-icon.mature {\r\n  background: #722ED1;\r\n}\r\n\r\n.stage-icon.risk {\r\n  background: #FF4D4F;\r\n}\r\n\r\n.stage-name {\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.stage-data {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.stage-value {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.stage-trend {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 12px;\r\n}\r\n\r\n.stage-trend.up {\r\n  color: #52C41A;\r\n}\r\n\r\n.stage-trend.down {\r\n  color: #FF4D4F;\r\n}\r\n\r\n.trend-value {\r\n  font-size: 12px;\r\n}\r\n\r\n.lifecycle-insights {\r\n  margin-top: 15px;\r\n  padding: 10px;\r\n  background: #F0F7FF;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.insight-icon {\r\n  width: 20px;\r\n  height: 20px;\r\n  margin-right: 10px;\r\n  flex-shrink: 0;\r\n  margin-top: 2px;\r\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%231677FF\"><path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 15c-.55 0-1-.45-1-1v-4c0-.55.45-1 1-1s1 .45 1 1v4c0 .55-.45 1-1 1zm1-8h-2V7h2v2z\"/></svg>');\r\n  background-repeat: no-repeat;\r\n  background-size: cover;\r\n}\r\n\r\n.insight-text {\r\n  font-size: 14px;\r\n  color: #1677FF;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* RFM 价值矩阵 */\r\n.rfm-card {\r\n  background: #F8FAFC;\r\n  border-radius: 10px;\r\n  padding: 15px;\r\n}\r\n\r\n.rfm-matrix {\r\n  display: flex;\r\n  flex-direction: column;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.rfm-row {\r\n  display: flex;\r\n  height: 80px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.rfm-row:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.rfm-cell {\r\n  flex: 1;\r\n  margin: 0 5px;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n}\r\n\r\n.rfm-cell.high {\r\n  background: linear-gradient(135deg, #E6F7FF, #D4EFFF);\r\n}\r\n\r\n.rfm-cell.medium {\r\n  background: linear-gradient(135deg, #F0F7FF, #E8F4FF);\r\n}\r\n\r\n.rfm-cell.low {\r\n  background: linear-gradient(135deg, #F9F9F9, #F5F5F5);\r\n}\r\n\r\n.rfm-cell.potential {\r\n  background: linear-gradient(135deg, #FCF5EB, #FAEBD7);\r\n}\r\n\r\n.cell-name {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #333;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.cell-value {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.rfm-legend {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.legend-title {\r\n  font-size: 14px;\r\n  color: #666;\r\n  margin-right: 10px;\r\n}\r\n\r\n.legend-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-right: 15px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.legend-color {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-radius: 3px;\r\n  margin-right: 5px;\r\n}\r\n\r\n.legend-color.high {\r\n  background: #1677FF;\r\n}\r\n\r\n.legend-color.medium {\r\n  background: #52C41A;\r\n}\r\n\r\n.legend-color.low {\r\n  background: #BFBFBF;\r\n}\r\n\r\n.legend-color.potential {\r\n  background: #FA8C16;\r\n}\r\n\r\n.legend-text {\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n.rfm-desc {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 10px;\r\n}\r\n\r\n.desc-title {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.desc-text {\r\n  font-size: 12px;\r\n  color: #666;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 客户标签管理 */\r\n.tag-card {\r\n  background: #F8FAFC;\r\n  border-radius: 10px;\r\n  padding: 15px;\r\n}\r\n\r\n.tag-categories {\r\n  display: flex;\r\n  overflow-x: auto;\r\n  white-space: nowrap;\r\n  margin-bottom: 15px;\r\n  scrollbar-width: none;\r\n}\r\n\r\n.tag-categories::-webkit-scrollbar {\r\n  display: none;\r\n}\r\n\r\n.tag-category {\r\n  padding: 8px 16px;\r\n  margin-right: 10px;\r\n  font-size: 14px;\r\n  color: #666;\r\n  background: #fff;\r\n  border-radius: 16px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.tag-category.active {\r\n  background: #1677FF;\r\n  color: #fff;\r\n}\r\n\r\n.tag-cloud {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  padding: 10px;\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.tag-item {\r\n  padding: 6px 12px;\r\n  background: #F5F7FA;\r\n  border-radius: 16px;\r\n  margin: 5px;\r\n  font-size: 12px;\r\n  color: #666;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.tag-item.xsmall {\r\n  font-size: 10px;\r\n}\r\n\r\n.tag-item.small {\r\n  font-size: 12px;\r\n}\r\n\r\n.tag-item.medium {\r\n  font-size: 14px;\r\n}\r\n\r\n.tag-item.large {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.tag-item.xlarge {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.tag-count {\r\n  font-size: 10px;\r\n  color: #999;\r\n  margin-left: 5px;\r\n}\r\n\r\n.tag-footer {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.create-tag-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px 16px;\r\n  background: #1677FF;\r\n  border-radius: 16px;\r\n  color: #fff;\r\n}\r\n\r\n.btn-icon {\r\n  width: 16px;\r\n  height: 16px;\r\n  margin-right: 5px;\r\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"white\"><path d=\"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z\"/></svg>');\r\n  background-repeat: no-repeat;\r\n  background-size: cover;\r\n}\r\n\r\n.btn-text {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 客户互动 */\r\n.message-stats {\r\n  display: flex;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.message-stat-item {\r\n  flex: 1;\r\n  text-align: center;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  display: block;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.recent-messages {\r\n  background: #F8FAFC;\r\n  border-radius: 10px;\r\n  padding: 15px;\r\n}\r\n\r\n.message-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px;\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  margin-bottom: 10px;\r\n  position: relative;\r\n}\r\n\r\n.customer-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 20px;\r\n  margin-right: 10px;\r\n}\r\n\r\n.message-content {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.message-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.customer-name {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.message-time {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.message-preview {\r\n  font-size: 12px;\r\n  color: #666;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  max-width: 200px;\r\n}\r\n\r\n.message-badge {\r\n  position: absolute;\r\n  top: 10px;\r\n  right: 10px;\r\n  min-width: 18px;\r\n  height: 18px;\r\n  border-radius: 9px;\r\n  background: #FF4D4F;\r\n  color: #fff;\r\n  font-size: 10px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 0 6px;\r\n}\r\n\r\n.interaction-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-top: 15px;\r\n}\r\n\r\n.action-btn {\r\n  flex: 1;\r\n  height: 40px;\r\n  margin: 0 5px;\r\n  background: #fff;\r\n  border-radius: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.btn-icon {\r\n  width: 16px;\r\n  height: 16px;\r\n  margin-right: 5px;\r\n}\r\n\r\n.btn-icon.message {\r\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%231677FF\"><path d=\"M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z\"/></svg>');\r\n  background-repeat: no-repeat;\r\n  background-size: cover;\r\n}\r\n\r\n.btn-icon.notify {\r\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%231677FF\"><path d=\"M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z\"/></svg>');\r\n  background-repeat: no-repeat;\r\n  background-size: cover;\r\n}\r\n\r\n.btn-icon.survey {\r\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%231677FF\"><path d=\"M11 7h2v2h-2zm0 4h2v6h-2zm1-9C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z\"/></svg>');\r\n  background-repeat: no-repeat;\r\n  background-size: cover;\r\n}\r\n\r\n.btn-text {\r\n  font-size: 14px;\r\n  color: #1677FF;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-customer/pages/customer/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAiXA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA;AAAA,MAEL,mBAAmB;AAAA;AAAA,MAGnB,iBAAiB;AAAA,QACf,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,QAChB,cAAc;AAAA,QACd,mBAAmB;AAAA,QACnB,YAAY;AAAA,QACZ,kBAAkB;AAAA,QAClB,gBAAgB;AAAA,QAChB,sBAAsB;AAAA,MACvB;AAAA;AAAA,MAGD,aAAa;AAAA,QACX,EAAE,IAAI,GAAG,MAAM,QAAS;AAAA,QACxB,EAAE,IAAI,GAAG,MAAM,QAAS;AAAA,QACxB,EAAE,IAAI,GAAG,MAAM,MAAO;AAAA,QACtB,EAAE,IAAI,GAAG,MAAM,OAAQ;AAAA,QACvB,EAAE,IAAI,GAAG,MAAM,OAAQ;AAAA,MACxB;AAAA,MACD,gBAAgB;AAAA;AAAA,MAGhB,aAAa;AAAA,QACX;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,SAAS;AAAA,UACT,KAAK;AAAA,UACL,eAAe;AAAA,UACf,UAAU;AAAA,UACV,mBAAmB;AAAA,UACnB,QAAQ,CAAC,SAAS,QAAQ,QAAQ,QAAQ,MAAM;AAAA,UAChD,WAAW,CAAC,MAAM,MAAM,MAAM,OAAO,OAAO,KAAK;AAAA,QAClD;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,SAAS;AAAA,UACT,KAAK;AAAA,UACL,eAAe;AAAA,UACf,UAAU;AAAA,UACV,mBAAmB;AAAA,UACnB,QAAQ,CAAC,SAAS,QAAQ,SAAS,OAAO,OAAO;AAAA,UACjD,WAAW,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,QAC/C;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,SAAS;AAAA,UACT,KAAK;AAAA,UACL,eAAe;AAAA,UACf,UAAU;AAAA,UACV,mBAAmB;AAAA,UACnB,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,OAAO,QAAQ;AAAA,UAChD,WAAW,CAAC,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG;AAAA,QACjC;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,SAAS;AAAA,UACT,KAAK;AAAA,UACL,eAAe;AAAA,UACf,UAAU;AAAA,UACV,mBAAmB;AAAA,UACnB,QAAQ,CAAC,UAAU,SAAS,UAAU,SAAS,MAAM;AAAA,UACrD,WAAW,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,QAC/C;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,SAAS;AAAA,UACT,KAAK;AAAA,UACL,eAAe;AAAA,UACf,UAAU;AAAA,UACV,mBAAmB;AAAA,UACnB,QAAQ,CAAC,SAAS,QAAQ,QAAQ,QAAQ,OAAO;AAAA,UACjD,WAAW,CAAC,MAAM,MAAM,MAAM,MAAM,KAAK,IAAI;AAAA,QAC/C;AAAA,MACD;AAAA;AAAA,MAGD,WAAW;AAAA,QACT,cAAc;AAAA,QACd,UAAU;AAAA,QACV,WAAW;AAAA,QACX,kBAAkB;AAAA,QAClB,cAAc;AAAA,QACd,eAAe;AAAA,QACf,iBAAiB;AAAA,QACjB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,eAAe;AAAA,QACf,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,SAAS;AAAA,MACV;AAAA;AAAA,MAGD,WAAW;AAAA,QACT;AAAA,UACE,EAAE,MAAM,QAAQ,OAAO,IAAI,MAAM,OAAQ;AAAA,UACzC,EAAE,MAAM,QAAQ,OAAO,GAAG,MAAM,OAAQ;AAAA,UACxC,EAAE,MAAM,QAAQ,OAAO,GAAG,MAAM,SAAU;AAAA,QAC3C;AAAA,QACD;AAAA,UACE,EAAE,MAAM,QAAQ,OAAO,IAAI,MAAM,SAAU;AAAA,UAC3C,EAAE,MAAM,QAAQ,OAAO,IAAI,MAAM,SAAU;AAAA,UAC3C,EAAE,MAAM,QAAQ,OAAO,IAAI,MAAM,MAAO;AAAA,QACzC;AAAA,QACD;AAAA,UACE,EAAE,MAAM,QAAQ,OAAO,GAAG,MAAM,YAAa;AAAA,UAC7C,EAAE,MAAM,QAAQ,OAAO,IAAI,MAAM,YAAa;AAAA,UAC9C,EAAE,MAAM,OAAO,OAAO,IAAI,MAAM,MAAO;AAAA,QACzC;AAAA,MACD;AAAA;AAAA,MAGD,eAAe;AAAA,QACb,EAAE,IAAI,GAAG,MAAM,OAAQ;AAAA,QACvB,EAAE,IAAI,GAAG,MAAM,OAAQ;AAAA,QACvB,EAAE,IAAI,GAAG,MAAM,OAAQ;AAAA,QACvB,EAAE,IAAI,GAAG,MAAM,OAAQ;AAAA,QACvB,EAAE,IAAI,GAAG,MAAM,OAAO;AAAA,MACvB;AAAA,MACD,oBAAoB;AAAA;AAAA,MAGpB,SAAS;AAAA,QACP,GAAG;AAAA;AAAA,UACD,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAS;AAAA,UAC3C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAS;AAAA,UAC3C,EAAE,MAAM,QAAQ,OAAO,MAAM,MAAM,SAAU;AAAA,UAC7C,EAAE,MAAM,OAAO,OAAO,KAAK,MAAM,SAAU;AAAA,UAC3C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,SAAU;AAAA,UAC5C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,SAAU;AAAA,UAC5C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,SAAU;AAAA,UAC5C,EAAE,MAAM,OAAO,OAAO,KAAK,MAAM,QAAS;AAAA,UAC1C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAS;AAAA,UAC3C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAS;AAAA,UAC3C,EAAE,MAAM,OAAO,OAAO,KAAK,MAAM,QAAS;AAAA,UAC1C,EAAE,MAAM,OAAO,OAAO,KAAK,MAAM,QAAQ;AAAA,QAC1C;AAAA,QACD,GAAG;AAAA;AAAA,UACD,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAS;AAAA,UAC3C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAS;AAAA,UAC3C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAS;AAAA,UAC3C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,SAAU;AAAA,UAC5C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,SAAU;AAAA,UAC5C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,SAAU;AAAA,UAC5C,EAAE,MAAM,SAAS,OAAO,KAAK,MAAM,SAAU;AAAA,UAC7C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAS;AAAA,UAC3C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,SAAU;AAAA,UAC5C,EAAE,MAAM,SAAS,OAAO,KAAK,MAAM,QAAS;AAAA,UAC5C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,SAAS;AAAA,QAC5C;AAAA,QACD,GAAG;AAAA;AAAA,UACD,EAAE,MAAM,UAAU,OAAO,KAAK,MAAM,QAAS;AAAA,UAC7C,EAAE,MAAM,UAAU,OAAO,KAAK,MAAM,QAAS;AAAA,UAC7C,EAAE,MAAM,QAAQ,OAAO,MAAM,MAAM,SAAU;AAAA,UAC7C,EAAE,MAAM,OAAO,OAAO,KAAK,MAAM,SAAU;AAAA,UAC3C,EAAE,MAAM,QAAQ,OAAO,MAAM,MAAM,SAAU;AAAA,UAC7C,EAAE,MAAM,MAAM,OAAO,KAAK,MAAM,QAAS;AAAA,UACzC,EAAE,MAAM,OAAO,OAAO,KAAK,MAAM,QAAS;AAAA,UAC1C,EAAE,MAAM,OAAO,OAAO,KAAK,MAAM,QAAS;AAAA,UAC1C,EAAE,MAAM,MAAM,OAAO,MAAM,MAAM,SAAU;AAAA,UAC3C,EAAE,MAAM,MAAM,OAAO,MAAM,MAAM,SAAS;AAAA,QAC3C;AAAA,QACD,GAAG;AAAA;AAAA,UACD,EAAE,MAAM,OAAO,OAAO,KAAK,MAAM,QAAS;AAAA,UAC1C,EAAE,MAAM,OAAO,OAAO,MAAM,MAAM,SAAU;AAAA,UAC5C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,SAAU;AAAA,UAC5C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAS;AAAA,UAC3C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAS;AAAA,UAC3C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAS;AAAA,UAC3C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,SAAU;AAAA,UAC5C,EAAE,MAAM,QAAQ,OAAO,IAAI,MAAM,SAAU;AAAA,UAC3C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,SAAU;AAAA,UAC5C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAS;AAAA,UAC3C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,SAAU;AAAA,UAC5C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,SAAS;AAAA,QAC5C;AAAA,QACD,GAAG;AAAA;AAAA,UACD,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,SAAU;AAAA,UAC5C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAS;AAAA,UAC3C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,SAAU;AAAA,UAC5C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,SAAU;AAAA,UAC5C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,SAAU;AAAA,UAC5C,EAAE,MAAM,OAAO,OAAO,KAAK,MAAM,QAAS;AAAA,UAC1C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,SAAU;AAAA,UAC5C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAS;AAAA,UAC3C,EAAE,MAAM,OAAO,OAAO,KAAK,MAAM,SAAU;AAAA,UAC3C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAS;AAAA,UAC3C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,SAAU;AAAA,UAC5C,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAQ;AAAA,QAC5C;AAAA,MACD;AAAA;AAAA,MAGD,cAAc;AAAA,QACZ,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,cAAc;AAAA,QACd,cAAc;AAAA,MACf;AAAA;AAAA,MAGD,gBAAgB;AAAA,QACd;AAAA,UACE,IAAI;AAAA,UACJ,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,QACf;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EAED,UAAU;AAAA;AAAA,IAER,qBAAqB;AACnB,aAAO,KAAK,YAAY,KAAK,aAAW,QAAQ,OAAO,KAAK,cAAc,KAAK,KAAK,YAAY,CAAC;AAAA,IAClG;AAAA;AAAA,IAGD,sBAAsB;AACpB,aAAO,KAAK,QAAQ,KAAK,kBAAkB,KAAK,CAAA;AAAA,IAClD;AAAA,EACD;AAAA,EAED,UAAU;AAER,SAAK,UAAU,MAAM;AACnB,WAAK,iBAAgB;AAAA,IACvB,CAAC;AAAA,EACF;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IAED,WAAW,KAAK;AACdA,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,cAAc,WAAW;AACvB,WAAK,iBAAiB;AACtB,WAAK,UAAU,MAAM;AACnB,aAAK,iBAAgB;AAAA,MACvB,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,cAAc,SAAS;AACrBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,6BAA6B,QAAQ,EAAE,SAAS,QAAQ,IAAI;AAAA,MACnE,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,kBAAkB,SAAS;AACzBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,qCAAqC,QAAQ,EAAE;AAAA,MACtD,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,kBAAkB,SAAS;AACzBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,OAAO,QAAQ,IAAI;AAAA,QAC1B,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,mBAAmB;AAGjBA,8GAAY,YAAY,KAAK,mBAAmB,SAAS;AAAA,IAE1D;AAAA;AAAA,IAGD,eAAe,MAAM;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,mCAAmC,KAAK,IAAI,SAAS,KAAK,IAAI;AAAA,MACrE,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,kBAAkB,YAAY;AAC5B,WAAK,qBAAqB;AAAA,IAC3B;AAAA;AAAA,IAGD,iBAAiB,KAAK;AACpBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,qCAAqC,IAAI,IAAI,UAAU,IAAI,KAAK;AAAA,MACvE,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,eAAe;AACbA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB,SAAS;AACxBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,mCAAmC,QAAQ,EAAE,SAAS,QAAQ,YAAY;AAAA,MACjF,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChtBA,GAAG,WAAW,eAAe;"}