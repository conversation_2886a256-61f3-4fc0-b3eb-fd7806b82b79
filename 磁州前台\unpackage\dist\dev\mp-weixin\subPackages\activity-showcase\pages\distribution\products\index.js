"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
if (!Array) {
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_circle + _component_path + _component_svg)();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const searchKeyword = common_vendor.ref("");
    const categories = common_vendor.ref([
      { id: 0, name: "全部" },
      { id: 1, name: "数码" },
      { id: 2, name: "家电" },
      { id: 3, name: "服装" },
      { id: 4, name: "美妆" },
      { id: 5, name: "食品" },
      { id: 6, name: "家居" },
      { id: 7, name: "母婴" }
    ]);
    const currentCategory = common_vendor.ref(0);
    const sortOptions = common_vendor.ref([
      { name: "综合排序", field: "default", order: "desc" },
      { name: "销量优先", field: "soldCount", order: "desc" },
      { name: "价格升序", field: "price", order: "asc" },
      { name: "价格降序", field: "price", order: "desc" },
      { name: "佣金比例", field: "commissionRate", order: "desc" }
    ]);
    const currentSort = common_vendor.ref(0);
    const showFilterPanel = common_vendor.ref(false);
    const minPrice = common_vendor.ref("");
    const maxPrice = common_vendor.ref("");
    const commissionOptions = common_vendor.ref(["5%以上", "10%以上", "15%以上", "20%以上"]);
    const selectedCommission = common_vendor.ref(-1);
    const tagOptions = common_vendor.ref(["热销", "新品", "限时", "包邮", "满减", "秒杀"]);
    const selectedTags = common_vendor.ref([]);
    const loading = common_vendor.ref(false);
    const noMore = common_vendor.ref(false);
    const products = common_vendor.ref([
      {
        id: 1,
        name: "Apple iPhone 14 Pro Max 256GB 暗夜紫 移动联通电信5G双卡双待手机",
        image: "https://via.placeholder.com/300",
        price: "8999.00",
        originalPrice: "9999.00",
        commission: "300.00",
        commissionRate: "3%",
        soldCount: 1253,
        category: 1,
        tag: "热销"
      },
      {
        id: 2,
        name: "小米12S Ultra 12GB+256GB 丹青黑 骁龙8+旗舰处理器 徕卡专业光学镜头",
        image: "https://via.placeholder.com/300",
        price: "5999.00",
        originalPrice: "6999.00",
        commission: "200.00",
        commissionRate: "3.3%",
        soldCount: 985,
        category: 1,
        tag: "新品"
      },
      {
        id: 3,
        name: "华为Mate 50 Pro 8GB+256GB 曜金黑 超光变XMAGE影像 北斗卫星消息",
        image: "https://via.placeholder.com/300",
        price: "6799.00",
        originalPrice: "7299.00",
        commission: "250.00",
        commissionRate: "3.7%",
        soldCount: 756,
        category: 1,
        tag: "限时"
      },
      {
        id: 4,
        name: "戴森(Dyson) 吸尘器 V12 Detect Slim 手持无线吸尘器",
        image: "https://via.placeholder.com/300",
        price: "4290.00",
        originalPrice: "4990.00",
        commission: "215.00",
        commissionRate: "5%",
        soldCount: 432,
        category: 2,
        tag: "热销"
      },
      {
        id: 5,
        name: "美的(Midea) 新风空调 1.5匹 壁挂式 智能家电 KFR-35GW/N8XHA1",
        image: "https://via.placeholder.com/300",
        price: "3299.00",
        originalPrice: "3899.00",
        commission: "165.00",
        commissionRate: "5%",
        soldCount: 321,
        category: 2,
        tag: "满减"
      },
      {
        id: 6,
        name: "NIKE 耐克 AIR FORCE 1 男子运动鞋 休闲板鞋",
        image: "https://via.placeholder.com/300",
        price: "799.00",
        originalPrice: "899.00",
        commission: "80.00",
        commissionRate: "10%",
        soldCount: 1876,
        category: 3,
        tag: "包邮"
      }
    ]);
    const filteredProducts = common_vendor.computed(() => {
      let result = [...products.value];
      if (currentCategory.value !== 0) {
        result = result.filter((item) => item.category === categories.value[currentCategory.value].id);
      }
      if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase();
        result = result.filter((item) => item.name.toLowerCase().includes(keyword));
      }
      const { field, order } = sortOptions.value[currentSort.value];
      if (field !== "default") {
        result.sort((a, b) => {
          let comparison = 0;
          if (field === "price" || field === "commissionRate" || field === "soldCount") {
            const aValue = field === "commissionRate" ? parseFloat(a[field].replace("%", "")) : parseFloat(a[field]);
            const bValue = field === "commissionRate" ? parseFloat(b[field].replace("%", "")) : parseFloat(b[field]);
            comparison = aValue - bValue;
          } else {
            comparison = a[field].localeCompare(b[field]);
          }
          return order === "asc" ? comparison : -comparison;
        });
      }
      return result;
    });
    function searchProducts() {
      common_vendor.index.showToast({
        title: "搜索: " + searchKeyword.value,
        icon: "none"
      });
    }
    function switchCategory(index) {
      currentCategory.value = index;
    }
    function switchSort(index) {
      currentSort.value = index;
    }
    function toggleFilterPanel() {
      showFilterPanel.value = !showFilterPanel.value;
    }
    function selectCommission(index) {
      selectedCommission.value = selectedCommission.value === index ? -1 : index;
    }
    function toggleTag(tag) {
      const index = selectedTags.value.indexOf(tag);
      if (index === -1) {
        selectedTags.value.push(tag);
      } else {
        selectedTags.value.splice(index, 1);
      }
    }
    function resetFilter() {
      minPrice.value = "";
      maxPrice.value = "";
      selectedCommission.value = -1;
      selectedTags.value = [];
    }
    function applyFilter() {
      showFilterPanel.value = false;
      common_vendor.index.showToast({
        title: "筛选条件已应用",
        icon: "success"
      });
    }
    function viewProductDetail(product) {
      common_vendor.index.navigateTo({
        url: `/subPackages/activity-showcase/pages/detail/index?id=${product.id}&source=distribution`
      });
    }
    function createPoster(product) {
      common_vendor.index.navigateTo({
        url: `/subPackages/activity-showcase/pages/distribution/poster/index?id=${product.id}`
      });
    }
    function loadMore() {
      if (loading.value || noMore.value)
        return;
      loading.value = true;
      setTimeout(() => {
        noMore.value = true;
        loading.value = false;
      }, 1500);
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          cx: "11",
          cy: "11",
          r: "8",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        b: common_vendor.p({
          d: "M21 21l-4.35-4.35",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        c: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "20",
          height: "20"
        }),
        d: common_vendor.o(searchProducts),
        e: searchKeyword.value,
        f: common_vendor.o(($event) => searchKeyword.value = $event.detail.value),
        g: common_vendor.p({
          d: "M22 3H2l8 9.46V19l4 2v-8.54L22 3z",
          stroke: "#666666",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        h: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "20",
          height: "20"
        }),
        i: common_vendor.o(toggleFilterPanel),
        j: common_vendor.f(categories.value, (category, index, i0) => {
          return {
            a: common_vendor.t(category.name),
            b: index,
            c: currentCategory.value === index ? 1 : "",
            d: common_vendor.o(($event) => switchCategory(index), index),
            e: currentCategory.value === index ? "#FFFFFF" : "#666666",
            f: currentCategory.value === index ? "linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)" : "#F2F2F7",
            g: currentCategory.value === index ? "0 4px 10px rgba(172,57,255,0.2)" : "none"
          };
        }),
        k: common_vendor.f(sortOptions.value, (option, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(option.name),
            b: currentSort.value === index
          }, currentSort.value === index ? {} : {}, {
            c: index,
            d: currentSort.value === index ? 1 : "",
            e: common_vendor.o(($event) => switchSort(index), index),
            f: currentSort.value === index ? "#AC39FF" : "#666666",
            g: currentSort.value === index ? "500" : "400"
          });
        }),
        l: common_vendor.f(filteredProducts.value, (product, index, i0) => {
          return common_vendor.e({
            a: product.image,
            b: product.tag
          }, product.tag ? {
            c: common_vendor.t(product.tag)
          } : {}, {
            d: common_vendor.t(product.name),
            e: common_vendor.t(product.price),
            f: common_vendor.t(product.originalPrice),
            g: common_vendor.t(product.commission),
            h: common_vendor.t(product.soldCount),
            i: common_vendor.o(($event) => createPoster(product), index),
            j: index,
            k: common_vendor.o(($event) => viewProductDetail(product), index)
          });
        }),
        m: filteredProducts.value.length === 0
      }, filteredProducts.value.length === 0 ? {
        n: common_assets._imports_0$61
      } : {}, {
        o: filteredProducts.value.length > 0
      }, filteredProducts.value.length > 0 ? common_vendor.e({
        p: loading.value
      }, loading.value ? {} : noMore.value ? {} : {
        r: common_vendor.o(loadMore)
      }, {
        q: noMore.value
      }) : {}, {
        s: showFilterPanel.value
      }, showFilterPanel.value ? {
        t: common_vendor.o(toggleFilterPanel)
      } : {}, {
        v: showFilterPanel.value
      }, showFilterPanel.value ? {
        w: common_vendor.p({
          d: "M18 6L6 18M6 6l12 12",
          stroke: "#666666",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        x: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "20",
          height: "20"
        }),
        y: common_vendor.o(toggleFilterPanel),
        z: minPrice.value,
        A: common_vendor.o(($event) => minPrice.value = $event.detail.value),
        B: maxPrice.value,
        C: common_vendor.o(($event) => maxPrice.value = $event.detail.value),
        D: common_vendor.f(commissionOptions.value, (option, index, i0) => {
          return {
            a: common_vendor.t(option),
            b: index,
            c: selectedCommission.value === index ? 1 : "",
            d: common_vendor.o(($event) => selectCommission(index), index),
            e: selectedCommission.value === index ? "rgba(172,57,255,0.1)" : "#F2F2F7",
            f: selectedCommission.value === index ? "1rpx solid #AC39FF" : "1rpx solid transparent",
            g: selectedCommission.value === index ? "#AC39FF" : "#666666"
          };
        }),
        E: common_vendor.f(tagOptions.value, (tag, index, i0) => {
          return {
            a: common_vendor.t(tag),
            b: index,
            c: selectedTags.value.includes(tag) ? 1 : "",
            d: common_vendor.o(($event) => toggleTag(tag), index),
            e: selectedTags.value.includes(tag) ? "rgba(172,57,255,0.1)" : "#F2F2F7",
            f: selectedTags.value.includes(tag) ? "1rpx solid #AC39FF" : "1rpx solid transparent",
            g: selectedTags.value.includes(tag) ? "#AC39FF" : "#666666"
          };
        }),
        F: common_vendor.o(resetFilter),
        G: common_vendor.o(applyFilter)
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-8cd1929f"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/distribution/products/index.js.map
