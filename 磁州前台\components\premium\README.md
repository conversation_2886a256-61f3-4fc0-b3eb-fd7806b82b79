# PremiumActions 高级推广操作组件

这是一个多功能推广操作组件，支持广告/付费发布、置顶和刷新功能，采用Vue 3 Composition API开发，符合苹果设计风格。

## 功能特点

- 支持看广告发布、付费发布
- 支持看广告置顶、付费置顶
- 支持看广告刷新、付费刷新
- 苹果风格UI设计，高级感、层次感
- 完全响应式，使用Vue 3 Composition API
- 可扩展的模块化设计

## 使用方法

### 基本使用

```vue
<template>
  <premium-actions 
    title="推广操作" 
    info-id="your-info-id" 
    @action-completed="handleActionCompleted"
  />
</template>

<script setup>
import PremiumActions from '@/components/premium/PremiumActions.vue';

const handleActionCompleted = (result) => {
  console.log('操作完成:', result);
  // 处理操作完成事件
};
</script>
```

### 属性说明

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| title | String | '推广操作' | 组件标题 |
| infoId | String | '' | 信息ID，用于标识操作的对象 |

### 事件说明

| 事件名 | 参数 | 说明 |
|-------|------|------|
| action-completed | result: Object | 操作完成时触发，返回操作结果 |

#### result 对象结构

```js
{
  action: String,  // 'publish', 'top', 'refresh'
  type: String,    // 'ad', 'paid'
  infoId: String,  // 信息ID
  option: Object   // 选择的选项详情
}
```

## 自定义样式

组件使用SCSS编写样式，可以通过覆盖以下关键类名来自定义外观：

- `.premium-actions-container` - 主容器
- `.premium-panel` - 面板容器
- `.panel-header` - 面板头部
- `.action-item` - 操作项
- `.modal-content` - 模态框内容
- `.option-item` - 选项项

## 示例

查看 `pages/examples/premium-actions-demo.vue` 获取完整使用示例。

## 注意事项

1. 需要在 `static/images/premium/` 目录下放置对应的图标资源
2. 组件依赖uni-app环境，确保项目中已正确配置
3. 广告功能需要接入实际的广告SDK，当前仅为模拟实现 