"use strict";
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      showSearchBar: false,
      searchKeyword: "",
      currentStatus: "all",
      currentSort: "time",
      sortDirection: "desc",
      batchMode: false,
      selectedOrders: [],
      hasMoreOrders: true,
      orderStatuses: [
        { name: "全部", value: "all" },
        { name: "待处理", value: "pending" },
        { name: "处理中", value: "processing" },
        { name: "已完成", value: "completed" },
        { name: "已取消", value: "cancelled" },
        { name: "退款中", value: "refunding" }
      ],
      sortOptions: [
        { name: "下单时间", value: "time" },
        { name: "订单金额", value: "amount" },
        { name: "客户名称", value: "customer" }
      ],
      orders: [
        {
          id: "1001",
          orderNo: "CZ20230501001",
          status: "pending",
          customerName: "张三",
          createTime: "2023-05-01 10:30",
          totalAmount: "128.00",
          products: [
            { id: "2001", name: "精品水果礼盒", image: "/static/images/product-1.jpg" },
            { id: "2002", name: "有机蔬菜套餐", image: "/static/images/product-2.jpg" }
          ]
        },
        {
          id: "1002",
          orderNo: "CZ20230501002",
          status: "processing",
          customerName: "李四",
          createTime: "2023-05-01 11:45",
          totalAmount: "256.50",
          products: [
            { id: "2003", name: "生日蛋糕", image: "/static/images/product-3.jpg" },
            { id: "2004", name: "鲜花束", image: "/static/images/product-4.jpg" },
            { id: "2005", name: "贺卡", image: "/static/images/product-5.jpg" }
          ]
        },
        {
          id: "1003",
          orderNo: "CZ20230502003",
          status: "completed",
          customerName: "王五",
          createTime: "2023-05-02 09:15",
          totalAmount: "89.90",
          products: [
            { id: "2006", name: "进口零食礼包", image: "/static/images/product-6.jpg" }
          ]
        },
        {
          id: "1004",
          orderNo: "CZ20230502004",
          status: "cancelled",
          customerName: "赵六",
          createTime: "2023-05-02 14:20",
          totalAmount: "199.00",
          products: [
            { id: "2007", name: "红酒套装", image: "/static/images/product-7.jpg" },
            { id: "2008", name: "高档茶叶", image: "/static/images/product-8.jpg" }
          ]
        },
        {
          id: "1005",
          orderNo: "CZ20230503005",
          status: "refunding",
          customerName: "钱七",
          createTime: "2023-05-03 16:35",
          totalAmount: "158.80",
          products: [
            { id: "2009", name: "护肤品套装", image: "/static/images/product-9.jpg" },
            { id: "2010", name: "面膜", image: "/static/images/product-10.jpg" },
            { id: "2011", name: "洗面奶", image: "/static/images/product-11.jpg" },
            { id: "2012", name: "爽肤水", image: "/static/images/product-12.jpg" }
          ]
        }
      ]
    };
  },
  computed: {
    filteredOrders() {
      let result = [...this.orders];
      if (this.currentStatus !== "all") {
        result = result.filter((order) => order.status === this.currentStatus);
      }
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase();
        result = result.filter(
          (order) => order.orderNo.toLowerCase().includes(keyword) || order.customerName.toLowerCase().includes(keyword)
        );
      }
      result.sort((a, b) => {
        let compareResult = 0;
        if (this.currentSort === "time") {
          compareResult = new Date(a.createTime) - new Date(b.createTime);
        } else if (this.currentSort === "amount") {
          compareResult = parseFloat(a.totalAmount) - parseFloat(b.totalAmount);
        } else if (this.currentSort === "customer") {
          compareResult = a.customerName.localeCompare(b.customerName);
        }
        return this.sortDirection === "asc" ? compareResult : -compareResult;
      });
      return result;
    }
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    showSearch() {
      this.showSearchBar = true;
    },
    cancelSearch() {
      this.showSearchBar = false;
      this.searchKeyword = "";
    },
    searchOrders() {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-order/pages/orders/list.vue:266", "搜索关键词:", this.searchKeyword);
    },
    filterByStatus(status) {
      this.currentStatus = status;
    },
    showFilterOptions() {
      common_vendor.index.showActionSheet({
        itemList: ["按时间范围筛选", "按商品类型筛选", "按支付方式筛选"],
        success: (res) => {
          common_vendor.index.showToast({
            title: "高级筛选功能开发中",
            icon: "none"
          });
        }
      });
    },
    sortOrders(sortType) {
      if (this.currentSort === sortType) {
        this.sortDirection = this.sortDirection === "asc" ? "desc" : "asc";
      } else {
        this.currentSort = sortType;
        this.sortDirection = "desc";
      }
    },
    viewOrderDetail(orderId) {
      common_vendor.index.navigateTo({
        url: `./detail?id=${orderId}`
      });
    },
    handleOrder(orderId) {
      common_vendor.index.navigateTo({
        url: `./detail?id=${orderId}&action=process`
      });
    },
    contactCustomer(orderId) {
      common_vendor.index.showToast({
        title: "联系客户功能开发中",
        icon: "none"
      });
    },
    loadMoreOrders() {
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        this.hasMoreOrders = false;
        common_vendor.index.showToast({
          title: "没有更多订单了",
          icon: "none"
        });
      }, 1e3);
    },
    refreshOrders() {
      common_vendor.index.showLoading({
        title: "刷新中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "刷新成功",
          icon: "success"
        });
      }, 1e3);
    },
    toggleBatchMode() {
      this.batchMode = !this.batchMode;
      if (!this.batchMode) {
        this.selectedOrders = [];
      }
    },
    toggleOrderSelection(orderId) {
      const index = this.selectedOrders.indexOf(orderId);
      if (index === -1) {
        this.selectedOrders.push(orderId);
      } else {
        this.selectedOrders.splice(index, 1);
      }
    },
    batchExport() {
      if (this.selectedOrders.length === 0) {
        common_vendor.index.showToast({
          title: "请先选择订单",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showToast({
        title: "导出功能开发中",
        icon: "none"
      });
    },
    batchPrint() {
      if (this.selectedOrders.length === 0) {
        common_vendor.index.showToast({
          title: "请先选择订单",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showToast({
        title: "打印功能开发中",
        icon: "none"
      });
    },
    batchProcess() {
      if (this.selectedOrders.length === 0) {
        common_vendor.index.showToast({
          title: "请先选择订单",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "批量处理",
        content: `确认处理选中的 ${this.selectedOrders.length} 个订单？`,
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "批量处理功能开发中",
              icon: "none"
            });
          }
        }
      });
    },
    getStatusColor(status) {
      const colors = {
        pending: "#FF9800",
        processing: "#2196F3",
        completed: "#4CAF50",
        cancelled: "#9E9E9E",
        refunding: "#F44336"
      };
      return colors[status] || "#333333";
    },
    getStatusText(status) {
      const texts = {
        pending: "待处理",
        processing: "处理中",
        completed: "已完成",
        cancelled: "已取消",
        refunding: "退款中"
      };
      return texts[status] || "未知状态";
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showSearch && $options.showSearch(...args)),
    c: $data.showSearchBar
  }, $data.showSearchBar ? {
    d: common_vendor.o((...args) => $options.searchOrders && $options.searchOrders(...args)),
    e: $data.searchKeyword,
    f: common_vendor.o(($event) => $data.searchKeyword = $event.detail.value),
    g: common_vendor.o((...args) => $options.searchOrders && $options.searchOrders(...args)),
    h: common_vendor.o((...args) => $options.cancelSearch && $options.cancelSearch(...args))
  } : {}, {
    i: common_vendor.f($data.orderStatuses, (status, index, i0) => {
      return {
        a: common_vendor.t(status.name),
        b: index,
        c: common_vendor.n({
          "active": $data.currentStatus === status.value
        }),
        d: common_vendor.o(($event) => $options.filterByStatus(status.value), index)
      };
    }),
    j: common_vendor.o((...args) => $options.showFilterOptions && $options.showFilterOptions(...args)),
    k: common_vendor.f($data.sortOptions, (option, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(option.name),
        b: $data.currentSort === option.value
      }, $data.currentSort === option.value ? {
        c: common_vendor.t($data.sortDirection === "asc" ? "↑" : "↓")
      } : {}, {
        d: index,
        e: common_vendor.n({
          "active": $data.currentSort === option.value
        }),
        f: common_vendor.o(($event) => $options.sortOrders(option.value), index)
      });
    }),
    l: common_vendor.f($options.filteredOrders, (order, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(order.orderNo),
        b: common_vendor.t($options.getStatusText(order.status)),
        c: $options.getStatusColor(order.status),
        d: common_vendor.t(order.customerName),
        e: common_vendor.t(order.createTime),
        f: common_vendor.t(order.totalAmount),
        g: common_vendor.f(order.products.slice(0, 3), (product, productIndex, i1) => {
          return {
            a: productIndex,
            b: product.image
          };
        }),
        h: order.products.length > 3
      }, order.products.length > 3 ? {
        i: common_vendor.t(order.products.length - 3)
      } : {}, {
        j: common_vendor.o(($event) => $options.handleOrder(order.id), index),
        k: common_vendor.o(($event) => $options.contactCustomer(order.id), index),
        l: index,
        m: common_vendor.o(($event) => $options.viewOrderDetail(order.id), index)
      });
    }),
    m: $data.hasMoreOrders
  }, $data.hasMoreOrders ? {
    n: common_vendor.o((...args) => $options.loadMoreOrders && $options.loadMoreOrders(...args))
  } : {}, {
    o: $options.filteredOrders.length === 0
  }, $options.filteredOrders.length === 0 ? {
    p: common_vendor.o((...args) => $options.refreshOrders && $options.refreshOrders(...args))
  } : {}, {
    q: common_vendor.t($data.batchMode ? "✓" : "☰"),
    r: common_vendor.o((...args) => $options.toggleBatchMode && $options.toggleBatchMode(...args)),
    s: $data.batchMode
  }, $data.batchMode ? {
    t: common_vendor.t($data.selectedOrders.length),
    v: common_vendor.o((...args) => $options.batchExport && $options.batchExport(...args)),
    w: common_vendor.o((...args) => $options.batchPrint && $options.batchPrint(...args)),
    x: common_vendor.o((...args) => $options.batchProcess && $options.batchProcess(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-order/pages/orders/list.js.map
