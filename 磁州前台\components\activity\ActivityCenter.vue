<template>
  <!-- 同城活动中心模块 - 苹果风格设计 -->
  <view class="activity-center">
    <!-- 模块标题区 -->
    <view class="activity-header">
      <view class="title-container">
        <view class="title-bar"></view>
        <text class="activity-title">同城活动中心</text>
        <view class="activity-badge">
          <text class="badge-text">精选推荐</text>
        </view>
      </view>
      <view class="more-btn" @click="navigateTo('/subPackages/activity/pages/list')">
        <text class="more-text">更多</text>
        <view class="more-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="9 18 15 12 9 6"></polyline>
          </svg>
        </view>
      </view>
    </view>
    
    <!-- 标签导航栏 -->
    <view class="activity-tabs-container">
      <scroll-view 
        class="activity-tabs" 
        scroll-x 
        show-scrollbar="false"
        :scroll-with-animation="true"
        :enhanced="true"
        :bounces="true"
      >
        <view 
          v-for="(tab, index) in activityTabs" 
          :key="index"
          class="activity-tab" 
          :class="{active: currentTab === index}" 
          @click="switchTab(index)"
        >
          <view class="tab-icon" v-if="tab.icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path v-if="tab.name === '全部活动'" d="M3 3h18v18H3zM12 8v8M8 12h8"></path>
              <path v-else-if="tab.name === '拼团活动'" d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2M9 7a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM23 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75"></path>
              <path v-else-if="tab.name === '秒杀活动'" d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"></path>
              <path v-else-if="tab.name === '优惠券'" d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
              <path v-else-if="tab.name === '满减活动'" d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
              <circle v-else cx="12" cy="12" r="10"></circle>
            </svg>
          </view>
          <text class="tab-text">{{tab.name}}</text>
          <view class="tab-line" v-if="currentTab === index"></view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 活动列表 - 水平滚动 -->
    <scroll-view 
      class="activity-scroll" 
      scroll-x 
      show-scrollbar="false"
      :scroll-with-animation="true"
      :enhanced="true"
      :bounces="true"
    >
      <view class="activity-list">
        <view 
          class="activity-item" 
          v-for="(item, index) in filteredActivities" 
          :key="item.id"
        >
          <activity-card-factory 
            :item="item"
            @navigate="navigateToDetail"
            @favorite="toggleFavorite"
            @action="handleAction"
          />
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import ActivityCardFactory from './ActivityCardFactory.vue';

// 活动标签数据
const activityTabs = ref([
  { name: '全部活动', icon: true },
  { name: '拼团活动', icon: true },
  { name: '秒杀活动', icon: true },
  { name: '优惠券', icon: true },
  { name: '满减活动', icon: true }
]);

// 当前选中的标签
const currentTab = ref(0);

// 模拟活动数据
const activities = ref([
  {
    id: '1',
    type: 'groupBuy',
    title: '5人拼团 | 磁县特产礼盒',
    status: 'ongoing',
    startTime: '2024-07-10',
    endTime: '2024-07-20',
    location: '磁县特产展销中心',
    coverImage: '/static/images/activity/group-buy-1.jpg',
    isFavorite: false,
    hot: true,
    groupPrice: 99,
    originalPrice: 199,
    groupSize: 5,
    currentGroupMembers: 3,
    participants: [
      { avatar: '/static/images/avatar/avatar-1.jpg' },
      { avatar: '/static/images/avatar/avatar-2.jpg' },
      { avatar: '/static/images/avatar/avatar-3.jpg' },
      { avatar: '/static/images/avatar/avatar-4.jpg' }
    ]
  },
  {
    id: '2',
    type: 'flashSale',
    title: '限时秒杀 | 本地农产品特惠',
    status: 'ongoing',
    startTime: '2024-07-12',
    endTime: '2024-07-13',
    location: '磁县农贸市场',
    coverImage: '/static/images/activity/flash-sale-1.jpg',
    isFavorite: true,
    salePrice: 29.9,
    originalPrice: 59.9,
    totalStock: 100,
    soldCount: 78,
    participants: [
      { avatar: '/static/images/avatar/avatar-5.jpg' },
      { avatar: '/static/images/avatar/avatar-6.jpg' },
      { avatar: '/static/images/avatar/avatar-7.jpg' },
      { avatar: '/static/images/avatar/avatar-8.jpg' },
      { avatar: '/static/images/avatar/avatar-9.jpg' }
    ]
  },
  {
    id: '3',
    type: 'coupon',
    title: '周末专享 | 餐饮优惠券',
    status: 'upcoming',
    startTime: '2024-07-15',
    endTime: '2024-07-17',
    location: '磁县商业街',
    coverImage: '/static/images/activity/coupon-1.jpg',
    isFavorite: false,
    couponType: 'cash',
    couponValue: 50,
    couponCondition: '满200元可用',
    couponValidity: '2024-07-31',
    participants: [
      { avatar: '/static/images/avatar/avatar-10.jpg' },
      { avatar: '/static/images/avatar/avatar-11.jpg' }
    ]
  },
  {
    id: '4',
    type: 'discount',
    title: '商圈联合满减 | 暑期狂欢',
    status: 'ongoing',
    startTime: '2024-07-01',
    endTime: '2024-07-31',
    location: '磁县中心商场',
    coverImage: '/static/images/activity/discount-1.jpg',
    isFavorite: false,
    hot: true,
    discountRules: [
      { threshold: 100, discount: 20 },
      { threshold: 200, discount: 50 },
      { threshold: 300, discount: 100 }
    ],
    merchantCount: 28,
    participants: [
      { avatar: '/static/images/avatar/avatar-12.jpg' },
      { avatar: '/static/images/avatar/avatar-13.jpg' },
      { avatar: '/static/images/avatar/avatar-14.jpg' },
      { avatar: '/static/images/avatar/avatar-15.jpg' },
      { avatar: '/static/images/avatar/avatar-16.jpg' },
      { avatar: '/static/images/avatar/avatar-17.jpg' }
    ]
  },
  {
    id: '5',
    type: 'groupBuy',
    title: '亲子团购 | 磁县水上乐园门票',
    status: 'ongoing',
    startTime: '2024-07-05',
    endTime: '2024-08-05',
    location: '磁县水上乐园',
    coverImage: '/static/images/activity/group-buy-2.jpg',
    isFavorite: false,
    groupPrice: 79,
    originalPrice: 158,
    groupSize: 3,
    currentGroupMembers: 1,
    participants: [
      { avatar: '/static/images/avatar/avatar-18.jpg' }
    ]
  }
]);

// 根据当前标签过滤活动
const filteredActivities = computed(() => {
  if (currentTab.value === 0) {
    return activities.value;
  }
  
  const tabTypeMap = {
    1: 'groupBuy',
    2: 'flashSale',
    3: 'coupon',
    4: 'discount'
  };
  
  const selectedType = tabTypeMap[currentTab.value];
  return activities.value.filter(activity => activity.type === selectedType);
});

// 切换标签
function switchTab(index) {
  currentTab.value = index;
}

// 导航到详情页
function navigateToDetail(data) {
  uni.navigateTo({
    url: `/subPackages/activity/pages/detail?id=${data.id}&type=${data.type}`,
    fail: (err) => {
      console.error('导航失败:', err);
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    }
  });
}

// 切换收藏状态
function toggleFavorite(id) {
  const activity = activities.value.find(item => item.id === id);
  if (activity) {
    activity.isFavorite = !activity.isFavorite;
    
    // 显示提示
    uni.showToast({
      title: activity.isFavorite ? '已收藏' : '已取消收藏',
      icon: 'none'
    });
  }
}

// 处理操作按钮点击
function handleAction(data) {
  console.log('操作按钮点击:', data);
  
  switch(data.type) {
    case 'groupBuy':
      if (data.status === 'ongoing') {
        uni.navigateTo({
          url: `/subPackages/activity/pages/group-buy?id=${data.id}`
        });
      } else if (data.status === 'upcoming') {
        uni.showToast({
          title: '已预约拼团提醒',
          icon: 'success'
        });
      }
      break;
      
    case 'flashSale':
      if (data.status === 'ongoing') {
        uni.navigateTo({
          url: `/subPackages/activity/pages/flash-sale?id=${data.id}`
        });
      } else if (data.status === 'upcoming') {
        uni.showToast({
          title: '已设置开始提醒',
          icon: 'success'
        });
      }
      break;
      
    case 'coupon':
      uni.showToast({
        title: '优惠券已领取',
        icon: 'success'
      });
      break;
      
    case 'discount':
      uni.navigateTo({
        url: `/subPackages/activity/pages/discount?id=${data.id}`
      });
      break;
      
    default:
      navigateToDetail(data);
  }
}

// 导航到指定页面
function navigateTo(url) {
  uni.navigateTo({
    url: url,
    fail: (err) => {
      console.error('导航失败:', err);
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    }
  });
}
</script>

<style scoped>
/* 活动中心模块基础样式 */
.activity-center {
  margin: 30rpx 0;
  padding-bottom: 20rpx;
}

/* 模块标题区 */
.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  margin-bottom: 20rpx;
}

.title-container {
  display: flex;
  align-items: center;
}

.title-bar {
  width: 8rpx;
  height: 36rpx;
  background: linear-gradient(to bottom, #007aff, #5ac8fa);
  border-radius: 4rpx;
  margin-right: 16rpx;
}

.activity-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
}

.activity-badge {
  margin-left: 16rpx;
  padding: 4rpx 12rpx;
  background-color: rgba(0, 122, 255, 0.1);
  border-radius: 20rpx;
}

.badge-text {
  font-size: 22rpx;
  color: #007aff;
}

.more-btn {
  display: flex;
  align-items: center;
  padding: 10rpx;
}

.more-text {
  font-size: 28rpx;
  color: #8e8e93;
}

.more-icon {
  margin-left: 4rpx;
  color: #8e8e93;
}

/* 标签导航栏 */
.activity-tabs-container {
  margin-bottom: 20rpx;
}

.activity-tabs {
  white-space: nowrap;
  padding: 0 20rpx;
}

.activity-tab {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 24rpx;
  position: relative;
}

.tab-icon {
  margin-bottom: 8rpx;
  color: #8e8e93;
}

.activity-tab.active .tab-icon {
  color: #007aff;
}

.tab-text {
  font-size: 28rpx;
  color: #8e8e93;
  transition: color 0.3s ease;
}

.activity-tab.active .tab-text {
  color: #007aff;
  font-weight: 500;
}

.tab-line {
  position: absolute;
  bottom: 0;
  width: 40rpx;
  height: 4rpx;
  background-color: #007aff;
  border-radius: 2rpx;
  transition: all 0.3s ease;
}

/* 活动列表 */
.activity-scroll {
  width: 100%;
}

.activity-list {
  display: flex;
  padding: 10rpx 20rpx;
}

.activity-item {
  width: 600rpx;
  flex-shrink: 0;
  margin-right: 20rpx;
}

.activity-item:last-child {
  margin-right: 0;
}

/* 媒体查询 - 适配不同屏幕尺寸 */
@media screen and (max-width: 375px) {
  .activity-item {
    width: 550rpx;
  }
}

@media screen and (min-width: 768px) {
  .activity-item {
    width: 650rpx;
  }
}
</style> 