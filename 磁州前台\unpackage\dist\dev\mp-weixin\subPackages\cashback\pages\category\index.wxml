<view class="category-container data-v-8d06c5ee"><custom-navbar wx:if="{{a}}" class="data-v-8d06c5ee" u-i="8d06c5ee-0" bind:__l="__l" u-p="{{a}}"></custom-navbar><view class="content-container data-v-8d06c5ee"><scroll-view class="category-sidebar data-v-8d06c5ee" scroll-y><view wx:for="{{b}}" wx:for-item="category" wx:key="b" class="{{['category-item', 'data-v-8d06c5ee', category.c && 'category-item--active']}}" bindtap="{{category.d}}"><text class="data-v-8d06c5ee">{{category.a}}</text></view></scroll-view><scroll-view class="category-content data-v-8d06c5ee" scroll-y><view class="subcategory-grid data-v-8d06c5ee"><view wx:for="{{c}}" wx:for-item="subcategory" wx:key="c" class="subcategory-section data-v-8d06c5ee"><view class="subcategory-title data-v-8d06c5ee"><text class="data-v-8d06c5ee">{{subcategory.a}}</text></view><view class="subcategory-items data-v-8d06c5ee"><view wx:for="{{subcategory.b}}" wx:for-item="item" wx:key="c" class="subcategory-item data-v-8d06c5ee" bindtap="{{item.d}}"><image class="subcategory-icon data-v-8d06c5ee" src="{{item.a}}" mode="aspectFill"></image><text class="subcategory-name data-v-8d06c5ee">{{item.b}}</text></view></view></view></view></scroll-view></view></view>