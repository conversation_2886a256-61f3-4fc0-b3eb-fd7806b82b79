<template>
  <view class="follows-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">我的关注</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 关注列表 -->
    <view class="follows-list" :style="{ marginTop: (navbarHeight + 10) + 'px' }">
      <!-- 无数据提示 -->
      <view class="empty-container" v-if="followsList.length === 0">
        <image src="/static/images/empty.png" class="empty-icon"></image>
        <view class="empty-text">暂无关注</view>
      </view>
      
      <!-- 用户列表 -->
      <view class="user-item" v-for="(user, index) in followsList" :key="index">
        <!-- 用户头像 -->
        <view class="user-avatar" @click="goToUserProfile(user.userId)">
          <image :src="user.avatar || '/static/images/default-avatar.png'" class="avatar-img" mode="aspectFill"></image>
        </view>
        
        <!-- 用户信息 -->
        <view class="user-info" @click="goToUserProfile(user.userId)">
          <view class="user-name">{{ user.nickname }}</view>
          <view class="user-signature">{{ user.signature || '这个人很懒，什么都没留下' }}</view>
        </view>
        
        <!-- 关注按钮 -->
        <view class="follow-btn" :class="{ 'followed': user.isFollowed }" @click="toggleFollow(user)">
          {{ user.isFollowed ? '已关注' : '关注' }}
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view class="loading-more" v-if="followsList.length > 0 && hasMore">
        <text class="loading-text">加载中...</text>
      </view>
      <view class="no-more" v-if="followsList.length > 0 && !hasMore">
        <text class="no-more-text">没有更多了</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20,
      navbarHeight: 64,
      followsList: [],
      page: 1,
      pageSize: 10,
      hasMore: true,
      isLoading: false
    }
  },
  created() {
    // 获取状态栏高度
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 44;
  },
  onLoad() {
    this.getFollowsList();
  },
  onReachBottom() {
    if (this.hasMore && !this.isLoading) {
      this.page++;
      this.getFollowsList();
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 获取关注列表
    getFollowsList() {
      if (this.isLoading) return;
      this.isLoading = true;
      
      // 模拟获取关注列表数据
      setTimeout(() => {
        // 模拟数据
        const mockData = [
          {
            userId: '1001',
            nickname: '张三',
            avatar: '/static/images/default-avatar.png',
            signature: '生活不止眼前的苟且，还有诗和远方',
            isFollowed: true
          },
          {
            userId: '1002',
            nickname: '李四',
            avatar: '/static/images/default-avatar.png',
            signature: '做一个简单的人',
            isFollowed: true
          },
          {
            userId: '1003',
            nickname: '王五',
            avatar: '/static/images/default-avatar.png',
            signature: '每天都是新的开始',
            isFollowed: true
          },
          {
            userId: '1004',
            nickname: '赵六',
            avatar: '/static/images/default-avatar.png',
            signature: '人生如戏，全靠演技',
            isFollowed: true
          },
          {
            userId: '1005',
            nickname: '钱七',
            avatar: '/static/images/default-avatar.png',
            signature: '不忘初心，方得始终',
            isFollowed: true
          }
        ];
        
        // 第一页直接赋值，后续页面追加
        if (this.page === 1) {
          this.followsList = mockData;
        } else {
          this.followsList = [...this.followsList, ...mockData];
        }
        
        // 判断是否还有更多数据
        this.hasMore = this.page < 3; // 模拟只有3页数据
        this.isLoading = false;
      }, 500);
    },
    
    // 切换关注状态
    toggleFollow(user) {
      // 模拟取消关注
      uni.showModal({
        title: '提示',
        content: `确定取消关注 ${user.nickname} 吗？`,
        success: (res) => {
          if (res.confirm) {
            // 模拟取消关注操作
            user.isFollowed = false;
            
            // 延迟从列表中移除
            setTimeout(() => {
              const index = this.followsList.findIndex(item => item.userId === user.userId);
              if (index > -1) {
                this.followsList.splice(index, 1);
              }
              
              uni.showToast({
                title: '已取消关注',
                icon: 'success'
              });
            }, 300);
          }
        }
      });
    },
    
    // 跳转到用户主页
    goToUserProfile(userId) {
      uni.navigateTo({
        url: `/pages/user-center/profile?userId=${userId}`
      });
    }
  }
}
</script>

<style>
.follows-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
}

.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.navbar-right {
  width: 80rpx;
}

/* 关注列表样式 */
.follows-list {
  padding: 20rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 用户列表项 */
.user-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  margin-right: 20rpx;
}

.avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50rpx;
}

.user-info {
  flex: 1;
  overflow: hidden;
}

.user-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.user-signature {
  font-size: 24rpx;
  color: #999;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.follow-btn {
  min-width: 120rpx;
  height: 60rpx;
  background-color: #0052CC;
  color: #fff;
  font-size: 26rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20rpx;
}

.followed {
  background-color: #f0f0f0;
  color: #666;
}

/* 加载更多 */
.loading-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
}

.loading-text, .no-more-text {
  font-size: 24rpx;
  color: #999;
}
</style> 