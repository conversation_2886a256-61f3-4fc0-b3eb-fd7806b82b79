<template>
  <view class="withdraw-records-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg" :style="{
        background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)'
      }"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="navigateBack">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M19 12H5M12 19l-7-7 7-7" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        <view class="navbar-title">提现记录</view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view 
      class="content-scroll" 
      scroll-y="true"
      :scroll-anchoring="true"
      :enhanced="true"
      :bounces="true"
      :show-scrollbar="false"
      @scrolltolower="loadMore"
    >
      <!-- 提现记录列表 -->
      <view class="records-list">
        <view 
          v-for="(record, index) in withdrawRecords" 
          :key="record.id"
          class="record-item"
          :style="{
            borderRadius: '25px',
            boxShadow: '0 8px 20px rgba(172,57,255,0.1)',
            background: '#FFFFFF',
            padding: '30rpx',
            marginBottom: '30rpx'
          }"
        >
          <view class="record-header">
            <view class="record-amount-status">
              <text class="record-amount">¥{{ record.amount }}</text>
              <text class="record-status" :class="record.status">{{ getStatusText(record.status) }}</text>
            </view>
            <text class="record-time">{{ record.time }}</text>
          </view>
          
          <view class="record-info">
            <view class="info-item">
              <text class="info-label">提现单号</text>
              <view class="info-value-copy">
                <text class="info-value">{{ record.withdrawId }}</text>
                <view class="copy-btn" @click="copyWithdrawId(record.withdrawId)">
                  <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="#AC39FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></rect>
                    <path d="M5 15H4a2 2 0 01-2-2V4a2 2 0 012-2h9a2 2 0 012 2v1" stroke="#AC39FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                  </svg>
                </view>
              </view>
            </view>
            
            <view class="info-item">
              <text class="info-label">提现方式</text>
              <text class="info-value">{{ record.withdrawMethod }}</text>
            </view>
            
            <view class="info-item">
              <text class="info-label">收款账户</text>
              <text class="info-value">{{ getMaskedAccount(record.account) }}</text>
            </view>
            
            <view class="info-item">
              <text class="info-label">实际到账</text>
              <text class="info-value">¥{{ record.actualAmount }}</text>
            </view>
            
            <view class="info-item" v-if="record.fee > 0">
              <text class="info-label">手续费</text>
              <text class="info-value">¥{{ record.fee }}</text>
            </view>
            
            <view class="info-item" v-if="record.remark">
              <text class="info-label">备注</text>
              <text class="info-value">{{ record.remark }}</text>
            </view>
          </view>
          
          <view class="record-actions" v-if="record.status === 'failed'">
            <view class="action-btn retry-btn" @click="retryWithdraw(record.id)" :style="{
              background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)'
            }">
              <text>重新提现</text>
            </view>
            <view class="action-btn contact-btn" @click="contactCustomerService">
              <text>联系客服</text>
            </view>
          </view>
          
          <view class="record-progress" v-if="record.status === 'processing'">
            <view class="progress-title">
              <text>预计到账时间</text>
              <text>{{ record.estimatedArrivalTime }}</text>
            </view>
            <view class="progress-bar-bg" :style="{
              background: 'rgba(172,57,255,0.2)',
              borderRadius: '10rpx',
              height: '10rpx',
              width: '100%'
            }">
              <view class="progress-bar-fill" :style="{
                width: record.progressPercentage + '%',
                background: 'linear-gradient(90deg, #AC39FF 0%, #B87AFF 100%)',
                borderRadius: '10rpx',
                height: '100%'
              }"></view>
            </view>
          </view>
        </view>
        
        <!-- 加载更多 -->
        <view class="loading-more" v-if="isLoading">
          <text>加载中...</text>
        </view>
        
        <!-- 没有更多数据 -->
        <view class="no-more-data" v-if="!hasMoreData && withdrawRecords.length > 0">
          <text>没有更多数据了</text>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-state" v-if="withdrawRecords.length === 0">
          <text class="empty-text">暂无提现记录</text>
          <view class="action-btn withdraw-btn" @click="navigateToWithdraw" :style="{
            background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)'
          }">
            <text>去提现</text>
          </view>
        </view>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 提现记录列表
const withdrawRecords = ref([
  {
    id: 'W001',
    withdrawId: '***********',
    amount: 1000.00,
    actualAmount: 990.00,
    fee: 10.00,
    status: 'completed', // completed, processing, failed
    time: '2023-01-01 12:00:00',
    withdrawMethod: '微信零钱',
    account: 'wx_13800138000',
    remark: '',
    estimatedArrivalTime: '',
    progressPercentage: 0
  },
  {
    id: 'W002',
    withdrawId: '***********',
    amount: 500.00,
    actualAmount: 495.00,
    fee: 5.00,
    status: 'processing',
    time: '2023-01-02 12:00:00',
    withdrawMethod: '支付宝',
    account: 'alipay_13800138000',
    remark: '',
    estimatedArrivalTime: '预计1-2个工作日到账',
    progressPercentage: 60
  },
  {
    id: 'W003',
    withdrawId: '***********',
    amount: 200.00,
    actualAmount: 198.00,
    fee: 2.00,
    status: 'failed',
    time: '2023-01-03 12:00:00',
    withdrawMethod: '银行卡',
    account: 'bank_6222********1234',
    remark: '银行卡信息有误，请检查后重新提现',
    estimatedArrivalTime: '',
    progressPercentage: 0
  }
]);

// 加载状态
const isLoading = ref(false);

// 是否有更多数据
const hasMoreData = ref(true);

// 返回上一页
const navigateBack = () => {
  uni.navigateBack();
};

// 获取状态文本
const getStatusText = (status) => {
  switch(status) {
    case 'completed':
      return '提现成功';
    case 'processing':
      return '处理中';
    case 'failed':
      return '提现失败';
    default:
      return '未知状态';
  }
};

// 获取掩码账户
const getMaskedAccount = (account) => {
  if (!account) return '';
  
  // 根据账户类型进行不同的掩码处理
  if (account.startsWith('wx_')) {
    const phone = account.substring(3);
    return '微信账户：' + phone.substring(0, 3) + '****' + phone.substring(7);
  } else if (account.startsWith('alipay_')) {
    const phone = account.substring(7);
    return '支付宝账户：' + phone.substring(0, 3) + '****' + phone.substring(7);
  } else if (account.startsWith('bank_')) {
    return '银行卡：' + account.substring(5);
  } else {
    return account;
  }
};

// 复制提现单号
const copyWithdrawId = (withdrawId) => {
  uni.setClipboardData({
    data: withdrawId,
    success: () => {
      uni.showToast({
        title: '提现单号已复制',
        icon: 'none'
      });
    }
  });
};

// 重新提现
const retryWithdraw = (id) => {
  uni.showModal({
    title: '重新提现',
    content: '确定要重新提现吗？',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '已提交重新提现申请',
          icon: 'none'
        });
      }
    }
  });
};

// 联系客服
const contactCustomerService = () => {
  uni.showToast({
    title: '正在连接客服...',
    icon: 'none'
  });
};

// 导航到提现页面
const navigateToWithdraw = () => {
  uni.navigateTo({
    url: '/subPackages/activity-showcase/pages/distribution/withdraw/index'
  });
};

// 加载更多
const loadMore = () => {
  if (!hasMoreData.value || isLoading.value) return;
  
  isLoading.value = true;
  
  // 模拟加载更多数据
  setTimeout(() => {
    // 这里应该是实际的API调用
    // 示例中添加模拟数据
    if (withdrawRecords.value.length < 10) {
      withdrawRecords.value.push(
        {
          id: 'W00' + (withdrawRecords.value.length + 1),
          withdrawId: '*********' + withdrawRecords.value.length,
          amount: Math.floor(Math.random() * 1000) + 100,
          actualAmount: Math.floor(Math.random() * 990) + 100,
          fee: 10.00,
          status: ['completed', 'processing', 'failed'][Math.floor(Math.random() * 3)],
          time: '2023-01-04 12:00:00',
          withdrawMethod: ['微信零钱', '支付宝', '银行卡'][Math.floor(Math.random() * 3)],
          account: 'account_' + Math.floor(Math.random() * 1000000),
          remark: '',
          estimatedArrivalTime: '预计1-2个工作日到账',
          progressPercentage: Math.floor(Math.random() * 100)
        }
      );
    } else {
      hasMoreData.value = false;
    }
    
    isLoading.value = false;
  }, 1000);
};

// 页面加载
onMounted(() => {
  // 初始化数据
});
</script>

<style lang="scss" scoped>
.withdraw-records-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F8F8FA;
  position: relative;
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  
  .navbar-bg {
    height: 180rpx;
    width: 100%;
  }
  
  .navbar-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 180rpx;
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    padding-top: var(--status-bar-height);
    
    .back-btn {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .navbar-title {
      flex: 1;
      text-align: center;
      font-size: 36rpx;
      font-weight: 600;
      color: #FFFFFF;
    }
  }
}

.content-scroll {
  flex: 1;
  padding: 30rpx;
  padding-top: calc(180rpx + 30rpx);
}

.records-list {
  .record-item {
    .record-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30rpx;
      
      .record-amount-status {
        display: flex;
        align-items: center;
        
        .record-amount {
          font-size: 40rpx;
          font-weight: 600;
          color: #333333;
          margin-right: 20rpx;
        }
        
        .record-status {
          padding: 4rpx 16rpx;
          border-radius: 20rpx;
          font-size: 24rpx;
          
          &.completed {
            color: #34C759;
            background-color: rgba(52, 199, 89, 0.1);
          }
          
          &.processing {
            color: #FF9500;
            background-color: rgba(255, 149, 0, 0.1);
          }
          
          &.failed {
            color: #FF3B30;
            background-color: rgba(255, 59, 48, 0.1);
          }
        }
      }
      
      .record-time {
        font-size: 24rpx;
        color: #999999;
      }
    }
    
    .record-info {
      margin-bottom: 30rpx;
      
      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16rpx;
        
        .info-label {
          font-size: 28rpx;
          color: #666666;
        }
        
        .info-value {
          font-size: 28rpx;
          color: #333333;
        }
        
        .info-value-copy {
          display: flex;
          align-items: center;
          
          .copy-btn {
            margin-left: 10rpx;
            padding: 6rpx;
          }
        }
      }
    }
    
    .record-actions {
      display: flex;
      justify-content: flex-end;
      
      .action-btn {
        padding: 16rpx 30rpx;
        border-radius: 30rpx;
        margin-left: 20rpx;
        
        text {
          font-size: 28rpx;
        }
        
        &.retry-btn {
          text {
            color: #FFFFFF;
          }
        }
        
        &.contact-btn {
          border: 1px solid #AC39FF;
          
          text {
            color: #AC39FF;
          }
        }
      }
    }
    
    .record-progress {
      .progress-title {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16rpx;
        
        text {
          font-size: 24rpx;
          color: #666666;
        }
      }
    }
  }
}

.loading-more, .no-more-data {
  text-align: center;
  padding: 30rpx 0;
  
  text {
    font-size: 28rpx;
    color: #999999;
  }
}

.empty-state {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .empty-text {
    font-size: 28rpx;
    color: #999999;
    margin-bottom: 30rpx;
  }
  
  .action-btn {
    padding: 16rpx 60rpx;
    border-radius: 30rpx;
    
    text {
      font-size: 28rpx;
      color: #FFFFFF;
    }
  }
}

.safe-area-bottom {
  height: 50rpx;
}

/* 适配小屏幕手机 */
@media screen and (max-width: 375px) {
  .record-item {
    .record-header {
      .record-amount-status {
        .record-amount {
          font-size: 36rpx;
        }
      }
    }
    
    .record-info {
      .info-item {
        .info-label, .info-value {
          font-size: 24rpx;
        }
      }
    }
  }
}
</style> 