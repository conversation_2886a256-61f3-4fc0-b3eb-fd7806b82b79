"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const isEditMode = common_vendor.ref(false);
    const categories = common_vendor.ref([
      { id: 0, name: "全部" },
      { id: 1, name: "活动" },
      { id: 2, name: "商品" },
      { id: 3, name: "优惠券" },
      { id: 4, name: "拼团" },
      { id: 5, name: "秒杀" }
    ]);
    const currentCategory = common_vendor.ref(0);
    const loading = common_vendor.ref(false);
    const noMore = common_vendor.ref(false);
    const favorites = common_vendor.ref([
      {
        id: 1,
        title: "Apple iPhone 14 Pro Max 256GB 暗夜紫 移动联通电信5G双卡双待手机",
        image: "https://via.placeholder.com/200",
        shop: "Apple官方旗舰店",
        price: "8999.00",
        originalPrice: "9999.00",
        type: "product",
        favoriteTime: "2023-05-15 14:30",
        selected: false
      },
      {
        id: 2,
        title: "618年中大促全场低至5折起",
        image: "https://via.placeholder.com/200",
        shop: "京东自营",
        price: "0.00",
        originalPrice: "",
        type: "activity",
        favoriteTime: "2023-05-14 09:15",
        selected: false
      },
      {
        id: 3,
        title: "满300减50全场优惠券",
        image: "https://via.placeholder.com/200",
        shop: "京东自营",
        price: "50.00",
        originalPrice: "",
        type: "coupon",
        favoriteTime: "2023-05-10 16:42",
        selected: false
      },
      {
        id: 4,
        title: "小米12S Ultra 12GB+256GB 丹青黑 骁龙8+旗舰处理器 徕卡专业光学镜头",
        image: "https://via.placeholder.com/200",
        shop: "小米官方旗舰店",
        price: "5999.00",
        originalPrice: "6999.00",
        type: "product",
        favoriteTime: "2023-05-08 11:23",
        selected: false
      },
      {
        id: 5,
        title: "3人团：小米空气净化器",
        image: "https://via.placeholder.com/200",
        shop: "小米官方旗舰店",
        price: "699.00",
        originalPrice: "999.00",
        type: "group",
        favoriteTime: "2023-05-07 18:05",
        selected: false
      },
      {
        id: 6,
        title: "限时秒杀：iPhone 14 Pro",
        image: "https://via.placeholder.com/200",
        shop: "Apple授权专卖店",
        price: "6999.00",
        originalPrice: "8999.00",
        type: "flash",
        favoriteTime: "2023-05-05 10:30",
        selected: false
      }
    ]);
    const filteredFavorites = common_vendor.computed(() => {
      if (currentCategory.value === 0) {
        return favorites.value;
      } else {
        const categoryMap = {
          1: "activity",
          2: "product",
          3: "coupon",
          4: "group",
          5: "flash"
        };
        return favorites.value.filter((item) => item.type === categoryMap[currentCategory.value]);
      }
    });
    const selectedCount = common_vendor.computed(() => {
      return favorites.value.filter((item) => item.selected).length;
    });
    const isAllSelected = common_vendor.computed(() => {
      return favorites.value.length > 0 && favorites.value.every((item) => item.selected);
    });
    function goBack() {
      common_vendor.index.navigateBack();
    }
    function toggleEditMode() {
      isEditMode.value = !isEditMode.value;
      if (!isEditMode.value) {
        favorites.value.forEach((item) => {
          item.selected = false;
        });
      }
    }
    function switchCategory(index) {
      currentCategory.value = index;
    }
    function viewDetail(item) {
      if (isEditMode.value) {
        toggleSelect(item);
        return;
      }
      let url = "";
      switch (item.type) {
        case "product":
          url = `/subPackages/activity-showcase/pages/detail/index?id=${item.id}&type=product`;
          break;
        case "activity":
          url = `/subPackages/activity-showcase/pages/detail/index?id=${item.id}&type=activity`;
          break;
        case "coupon":
          url = `/subPackages/activity-showcase/pages/coupon/detail?id=${item.id}`;
          break;
        case "group":
          url = `/subPackages/activity-showcase/pages/group-buy/detail?id=${item.id}`;
          break;
        case "flash":
          url = `/subPackages/activity-showcase/pages/flash-sale/detail?id=${item.id}`;
          break;
        default:
          url = `/subPackages/activity-showcase/pages/detail/index?id=${item.id}`;
      }
      common_vendor.index.navigateTo({ url });
    }
    function toggleSelect(item) {
      item.selected = !item.selected;
    }
    function toggleSelectAll() {
      const newState = !isAllSelected.value;
      favorites.value.forEach((item) => {
        item.selected = newState;
      });
    }
    function batchDelete() {
      if (selectedCount.value === 0) {
        common_vendor.index.showToast({
          title: "请先选择要删除的收藏",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "删除收藏",
        content: `确定要删除选中的${selectedCount.value}个收藏吗？`,
        success: (res) => {
          if (res.confirm) {
            favorites.value = favorites.value.filter((item) => !item.selected);
            common_vendor.index.showToast({
              title: "删除成功",
              icon: "success"
            });
            if (favorites.value.length === 0) {
              isEditMode.value = false;
            }
          }
        }
      });
    }
    function removeFavorite(item) {
      common_vendor.index.showModal({
        title: "取消收藏",
        content: "确定要取消收藏该内容吗？",
        success: (res) => {
          if (res.confirm) {
            const index = favorites.value.findIndex((fav) => fav.id === item.id);
            if (index !== -1) {
              favorites.value.splice(index, 1);
            }
            common_vendor.index.showToast({
              title: "已取消收藏",
              icon: "success"
            });
          }
        }
      });
    }
    function goExplore() {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    }
    function loadMore() {
      if (loading.value || noMore.value)
        return;
      loading.value = true;
      setTimeout(() => {
        noMore.value = true;
        loading.value = false;
      }, 1500);
    }
    function getTagBackground(type) {
      switch (type) {
        case "product":
          return "linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%)";
        case "activity":
          return "linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)";
        case "coupon":
          return "linear-gradient(135deg, #FF3B30 0%, #FF5E3A 100%)";
        case "group":
          return "linear-gradient(135deg, #34C759 0%, #30D158 100%)";
        case "flash":
          return "linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)";
        default:
          return "linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)";
      }
    }
    function getTagText(type) {
      switch (type) {
        case "product":
          return "商品";
        case "activity":
          return "活动";
        case "coupon":
          return "优惠券";
        case "group":
          return "拼团";
        case "flash":
          return "秒杀";
        default:
          return "其他";
      }
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: common_vendor.f(categories.value, (category, index, i0) => {
          return {
            a: common_vendor.t(category.name),
            b: index,
            c: currentCategory.value === index ? 1 : "",
            d: common_vendor.o(($event) => switchCategory(index), index),
            e: currentCategory.value === index ? "#FFFFFF" : "#666666",
            f: currentCategory.value === index ? "linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)" : "#F2F2F7",
            g: currentCategory.value === index ? "0 4px 10px rgba(255,59,105,0.2)" : "none"
          };
        }),
        d: common_vendor.t(isEditMode.value ? "完成" : "编辑"),
        e: common_vendor.o(toggleEditMode),
        f: common_vendor.f(filteredFavorites.value, (item, index, i0) => {
          return common_vendor.e(isEditMode.value ? common_vendor.e({
            a: item.selected
          }, item.selected ? {
            b: "5e0c0445-1-" + i0 + "," + ("5e0c0445-0-" + i0),
            c: common_vendor.p({
              d: "M5 12l5 5L20 7",
              stroke: "#FFFFFF",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            d: "5e0c0445-0-" + i0,
            e: common_vendor.p({
              viewBox: "0 0 24 24",
              width: "16",
              height: "16"
            })
          } : {}, {
            f: common_vendor.o(($event) => toggleSelect(item), index),
            g: item.selected ? "0" : "2rpx solid #CCCCCC",
            h: item.selected ? "#FF3B69" : "#FFFFFF"
          }) : {}, {
            i: item.image,
            j: common_vendor.t(item.title),
            k: common_vendor.t(item.shop),
            l: common_vendor.t(item.price),
            m: item.originalPrice
          }, item.originalPrice ? {
            n: common_vendor.t(item.originalPrice)
          } : {}, {
            o: common_vendor.t(getTagText(item.type)),
            p: getTagBackground(item.type),
            q: common_vendor.o(($event) => viewDetail(item), index),
            r: common_vendor.t(item.favoriteTime),
            s: "5e0c0445-3-" + i0 + "," + ("5e0c0445-2-" + i0),
            t: "5e0c0445-2-" + i0,
            v: common_vendor.o(($event) => removeFavorite(item), index),
            w: index
          });
        }),
        g: isEditMode.value,
        h: common_vendor.p({
          d: "M19 21l-7-5-7 5V5a2 2 0 012-2h10a2 2 0 012 2v16z",
          fill: "#FF3B69",
          stroke: "#FF3B69",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        i: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        j: filteredFavorites.value.length === 0
      }, filteredFavorites.value.length === 0 ? {
        k: common_assets._imports_1$56,
        l: common_vendor.o(goExplore)
      } : {}, {
        m: filteredFavorites.value.length > 0 && !noMore.value
      }, filteredFavorites.value.length > 0 && !noMore.value ? common_vendor.e({
        n: loading.value
      }, loading.value ? {} : {
        o: common_vendor.o(loadMore)
      }) : {}, {
        p: filteredFavorites.value.length > 0 && noMore.value
      }, filteredFavorites.value.length > 0 && noMore.value ? {} : {}, {
        q: isEditMode.value ? "180rpx" : "0",
        r: isEditMode.value
      }, isEditMode.value ? common_vendor.e({
        s: isAllSelected.value
      }, isAllSelected.value ? {
        t: common_vendor.p({
          d: "M5 12l5 5L20 7",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        v: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        })
      } : {}, {
        w: isAllSelected.value ? "0" : "2rpx solid #CCCCCC",
        x: isAllSelected.value ? "#FF3B69" : "#FFFFFF",
        y: common_vendor.o(toggleSelectAll),
        z: common_vendor.t(selectedCount.value),
        A: common_vendor.o(batchDelete),
        B: selectedCount.value > 0 ? "linear-gradient(135deg, #FF3B30 0%, #FF5E3A 100%)" : "#CCCCCC",
        C: selectedCount.value > 0 ? "0 5px 15px rgba(255,59,48,0.3)" : "none"
      }) : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-5e0c0445"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/favorites/index.js.map
