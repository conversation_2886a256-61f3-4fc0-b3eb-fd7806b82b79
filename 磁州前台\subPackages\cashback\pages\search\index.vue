<template>
  <view class="search-container">
    <!-- 自定义导航栏 -->
    <custom-navbar title="商品搜索" :show-back="true"></custom-navbar>
    
    <!-- 内容区域 -->
    <view class="content-container">
      <!-- 搜索框 -->
      <view class="search-section">
        <view class="search-bar">
          <svg class="search-icon" viewBox="0 0 24 24" width="20" height="20">
            <path fill="#999999" d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z" />
          </svg>
          <input class="search-input" type="text" v-model="searchKeyword" placeholder="搜索商品名称" confirm-type="search" @confirm="searchProducts" />
          <view class="clear-button" v-if="searchKeyword" @tap="clearSearch">
            <svg class="clear-icon" viewBox="0 0 24 24" width="16" height="16">
              <path fill="#CCCCCC" d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
            </svg>
        </view>
      </view>
    </view>
    
    <!-- 热门搜索 -->
      <view class="hot-search-section" v-if="!searchKeyword && !searchResults.length">
        <view class="section-header">
          <text class="section-title">热门搜索</text>
      </view>
        <view class="hot-search-tags">
          <view class="tag-item" v-for="(tag, index) in hotSearchTags" :key="index" @tap="selectTag(tag)">
            <text>{{ tag }}</text>
        </view>
      </view>
    </view>
    
      <!-- 搜索历史 -->
      <view class="search-history-section" v-if="!searchKeyword && !searchResults.length && searchHistory.length > 0">
        <view class="section-header">
          <text class="section-title">搜索历史</text>
          <view class="clear-history" @tap="clearHistory">
            <svg class="delete-icon" viewBox="0 0 24 24" width="16" height="16">
              <path fill="#999999" d="M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19M8,9H16V19H8V9M15.5,4L14.5,3H9.5L8.5,4H5V6H19V4H15.5Z" />
            </svg>
            <text>清空</text>
        </view>
        </view>
        <view class="history-tags">
          <view class="tag-item" v-for="(item, index) in searchHistory" :key="index" @tap="selectTag(item)">
            <text>{{ item }}</text>
          </view>
        </view>
      </view>
      
      <!-- 搜索结果 -->
      <view class="search-results-section" v-if="searchResults.length > 0">
        <view class="filter-bar">
          <view class="filter-item" :class="{'filter-item--active': sortBy === 'default'}" @tap="sortResults('default')">
            <text>综合</text>
          </view>
          <view class="filter-item" :class="{'filter-item--active': sortBy === 'price'}" @tap="sortResults('price')">
            <text>价格</text>
            <view class="sort-arrows">
              <svg class="arrow-up" viewBox="0 0 24 24" width="12" height="12" :class="{'active': sortBy === 'price' && sortOrder === 'asc'}">
                <path fill="currentColor" d="M7,15L12,10L17,15H7Z" />
              </svg>
              <svg class="arrow-down" viewBox="0 0 24 24" width="12" height="12" :class="{'active': sortBy === 'price' && sortOrder === 'desc'}">
                <path fill="currentColor" d="M7,10L12,15L17,10H7Z" />
              </svg>
            </view>
          </view>
          <view class="filter-item" :class="{'filter-item--active': sortBy === 'cashback'}" @tap="sortResults('cashback')">
            <text>返利</text>
            <view class="sort-arrows">
              <svg class="arrow-up" viewBox="0 0 24 24" width="12" height="12" :class="{'active': sortBy === 'cashback' && sortOrder === 'asc'}">
                <path fill="currentColor" d="M7,15L12,10L17,15H7Z" />
              </svg>
              <svg class="arrow-down" viewBox="0 0 24 24" width="12" height="12" :class="{'active': sortBy === 'cashback' && sortOrder === 'desc'}">
                <path fill="currentColor" d="M7,10L12,15L17,10H7Z" />
              </svg>
            </view>
          </view>
          <view class="filter-item" :class="{'filter-item--active': showPriceCompare}" @tap="togglePriceCompare">
            <text>比价</text>
          </view>
        </view>
        
        <view class="products-grid" v-if="!showPriceCompare">
          <product-card
            v-for="(product, index) in sortedSearchResults"
            :key="index"
            :product="product"
            @tap="navigateToDetail(product)"
          ></product-card>
        </view>
        
        <view class="price-compare-list" v-else>
          <view class="compare-item" v-for="(product, index) in sortedSearchResults" :key="index">
            <view class="compare-header" @tap="toggleProductPlatforms(product.id)">
              <image class="product-image" :src="product.image" mode="aspectFill"></image>
            <view class="product-info">
                <text class="product-title">{{ product.title }}</text>
                <view class="price-range">
                  <text class="price-label">价格区间：</text>
                  <text class="price-value">¥{{ product.minPrice }} - ¥{{ product.maxPrice }}</text>
                </view>
              </view>
              <svg class="expand-icon" viewBox="0 0 24 24" width="24" height="24" :class="{'expanded': expandedProducts.includes(product.id)}">
                <path fill="#999999" d="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z" />
              </svg>
            </view>
            
            <view class="platform-list" v-if="expandedProducts.includes(product.id)">
              <view class="platform-item" v-for="(platform, pIndex) in product.platforms" :key="pIndex" @tap="navigateToPlatformDetail(product, platform)">
                <view class="platform-info">
                  <image class="platform-icon" :src="platform.icon" mode="aspectFit"></image>
                  <text class="platform-name">{{ platform.name }}</text>
                </view>
                <view class="platform-price">
                  <text class="price-value">¥{{ platform.price }}</text>
                  <text class="cashback-value">返¥{{ platform.cashback }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <view class="loading-more" v-if="loading">
          <text>加载中...</text>
        </view>
        <view class="no-more" v-if="!loading && !hasMore && searchResults.length > 0">
          <text>没有更多了</text>
        </view>
        </view>
        
      <!-- 无搜索结果 -->
      <view class="empty-result" v-if="searched && searchResults.length === 0">
        <image class="empty-image" src="/static/images/cashback/empty-search.png" mode="aspectFit"></image>
        <text>未找到相关商品</text>
          <text class="empty-tips">换个关键词试试吧</text>
        </view>
    </view>
  </view>
</template>

<script>
import CustomNavbar from '../../components/CustomNavbar.vue';
import ProductCard from '../../components/ProductCard.vue';

export default {
  components: {
    CustomNavbar,
    ProductCard
  },
  data() {
    return {
      searchKeyword: '',
      searched: false,
      loading: false,
      hasMore: true,
      page: 1,
      sortBy: 'default',
      sortOrder: 'asc',
      showPriceCompare: false,
      expandedProducts: [],
      hotSearchTags: ['iPhone 15', '华为Mate60', '小米14', '笔记本电脑', '耳机', '电视'],
      searchHistory: [],
      searchResults: []
    };
  },
  computed: {
    sortedSearchResults() {
      if (!this.searchResults.length) return [];
      
      let results = [...this.searchResults];
      
      if (this.sortBy === 'default') {
        return results;
      } else if (this.sortBy === 'price') {
        return this.sortOrder === 'asc' 
          ? results.sort((a, b) => parseFloat(a.price) - parseFloat(b.price))
          : results.sort((a, b) => parseFloat(b.price) - parseFloat(a.price));
      } else if (this.sortBy === 'cashback') {
        return this.sortOrder === 'asc'
          ? results.sort((a, b) => parseFloat(a.cashback) - parseFloat(b.cashback))
          : results.sort((a, b) => parseFloat(b.cashback) - parseFloat(a.cashback));
      }
      
      return results;
    }
  },
  onLoad(options) {
    // 设置页面不显示系统导航栏
    uni.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#9C27B0'
    });
    
    // 获取搜索历史
    this.getSearchHistory();
    
    // 如果有搜索关键词参数
    if (options.keyword) {
      this.searchKeyword = decodeURIComponent(options.keyword);
      this.searchProducts();
    }
  },
  methods: {
    // 搜索商品
    searchProducts() {
      if (!this.searchKeyword.trim()) return;
      
      this.searched = true;
      this.loading = true;
      this.page = 1;
      this.searchResults = [];
      this.hasMore = true;
      
      // 保存到搜索历史
      this.saveSearchHistory(this.searchKeyword);
      
      // 模拟搜索请求
      setTimeout(() => {
        // 这里应该是真实的API请求
        this.searchResults = this.getMockSearchResults();
        this.loading = false;
        
        // 计算每个商品的最高和最低价格
        this.searchResults.forEach(product => {
          const prices = product.platforms.map(p => parseFloat(p.price));
          product.minPrice = Math.min(...prices).toFixed(2);
          product.maxPrice = Math.max(...prices).toFixed(2);
        });
      }, 1000);
    },
    
    // 加载更多搜索结果
    loadMoreResults() {
      if (!this.hasMore || this.loading) return;
      
      this.loading = true;
      this.page++;
      
      // 模拟加载更多
      setTimeout(() => {
        // 这里应该是真实的API请求
        if (this.page > 2) {
          this.hasMore = false;
        } else {
          const moreResults = this.getMockSearchResults();
          this.searchResults = [...this.searchResults, ...moreResults];
        }
        this.loading = false;
      }, 1000);
    },
    
    // 排序搜索结果
    sortResults(type) {
      if (this.sortBy === type) {
        // 切换排序方向
        this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
      } else {
        this.sortBy = type;
        this.sortOrder = type === 'default' ? 'asc' : 'desc';
      }
    },
    
    // 切换比价显示
    togglePriceCompare() {
      this.showPriceCompare = !this.showPriceCompare;
    },
    
    // 切换产品平台列表展开/收起
    toggleProductPlatforms(productId) {
      const index = this.expandedProducts.indexOf(productId);
      if (index > -1) {
        this.expandedProducts.splice(index, 1);
      } else {
        this.expandedProducts.push(productId);
      }
    },
    
    // 选择标签
    selectTag(tag) {
      this.searchKeyword = tag;
      this.searchProducts();
    },
    
    // 清空搜索
    clearSearch() {
      this.searchKeyword = '';
      this.searched = false;
      this.searchResults = [];
    },
    
    // 获取搜索历史
    getSearchHistory() {
      const history = uni.getStorageSync('searchHistory');
        if (history) {
        this.searchHistory = JSON.parse(history);
      }
    },
    
    // 保存搜索历史
    saveSearchHistory(keyword) {
      if (!keyword.trim()) return;
      
      // 移除已存在的相同关键词
      const index = this.searchHistory.indexOf(keyword);
      if (index > -1) {
        this.searchHistory.splice(index, 1);
      }
      
      // 添加到历史记录开头
      this.searchHistory.unshift(keyword);
      
      // 限制历史记录数量
      if (this.searchHistory.length > 10) {
        this.searchHistory = this.searchHistory.slice(0, 10);
      }
      
      // 保存到本地存储
      uni.setStorageSync('searchHistory', JSON.stringify(this.searchHistory));
    },
    
    // 清空搜索历史
    clearHistory() {
      uni.showModal({
        title: '提示',
        content: '确定要清空搜索历史吗？',
        success: (res) => {
          if (res.confirm) {
            this.searchHistory = [];
            uni.removeStorageSync('searchHistory');
          }
        }
      });
    },
    
    // 导航到商品详情
    navigateToDetail(product) {
      uni.navigateTo({
        url: `/subPackages/cashback/pages/product-detail/index?id=${product.id}`
      });
    },
    
    // 导航到平台商品详情
    navigateToPlatformDetail(product, platform) {
      uni.navigateTo({
        url: `/subPackages/cashback/pages/product-detail/index?id=${product.id}&platform=${platform.name}`
      });
    },
    
    // 模拟搜索结果数据
    getMockSearchResults() {
      return [
        {
          id: 1,
          title: 'Apple iPhone 15 Pro Max (A2850) 256GB 原色钛金属',
          image: '/static/images/cashback/product-1.png',
          price: '9999.00',
          cashback: '300.00',
          platform: '京东',
          platforms: [
            {
              name: '京东',
              icon: '/static/images/cashback/platform-jd.png',
              price: '9999.00',
              cashback: '300.00'
            },
            {
              name: '天猫',
              icon: '/static/images/cashback/platform-tmall.png',
              price: '9989.00',
              cashback: '280.00'
            },
            {
              name: '苏宁',
              icon: '/static/images/cashback/platform-suning.png',
              price: '10099.00',
              cashback: '290.00'
            }
          ]
        },
        {
          id: 2,
          title: 'Apple iPhone 15 Pro 256GB 黑色钛金属',
          image: '/static/images/cashback/product-2.png',
          price: '8999.00',
          cashback: '270.00',
          platform: '天猫',
          platforms: [
            {
              name: '京东',
              icon: '/static/images/cashback/platform-jd.png',
              price: '8999.00',
              cashback: '260.00'
            },
            {
              name: '天猫',
              icon: '/static/images/cashback/platform-tmall.png',
              price: '8989.00',
              cashback: '270.00'
            },
            {
              name: '拼多多',
              icon: '/static/images/cashback/platform-pdd.png',
              price: '8899.00',
              cashback: '220.00'
            }
          ]
        },
        {
          id: 3,
          title: 'Apple iPhone 15 128GB 蓝色',
          image: '/static/images/cashback/product-3.png',
          price: '5999.00',
          cashback: '180.00',
          platform: '拼多多',
          platforms: [
            {
              name: '京东',
              icon: '/static/images/cashback/platform-jd.png',
              price: '5999.00',
              cashback: '150.00'
            },
            {
              name: '天猫',
              icon: '/static/images/cashback/platform-tmall.png',
              price: '6099.00',
              cashback: '160.00'
            },
            {
              name: '拼多多',
              icon: '/static/images/cashback/platform-pdd.png',
              price: '5899.00',
              cashback: '180.00'
            }
          ]
        },
        {
          id: 4,
          title: 'Apple iPhone 14 Pro Max 256GB 深空黑色',
          image: '/static/images/cashback/product-4.png',
          price: '8499.00',
          cashback: '250.00',
          platform: '京东',
          platforms: [
            {
              name: '京东',
              icon: '/static/images/cashback/platform-jd.png',
              price: '8499.00',
              cashback: '250.00'
            },
            {
              name: '天猫',
              icon: '/static/images/cashback/platform-tmall.png',
              price: '8599.00',
              cashback: '240.00'
            },
            {
              name: '苏宁',
              icon: '/static/images/cashback/platform-suning.png',
              price: '8549.00',
              cashback: '230.00'
            }
          ]
        }
      ];
    }
  },
  onReachBottom() {
    // 滚动到底部加载更多
    this.loadMoreResults();
  }
};
</script>

<style lang="scss" scoped>
.search-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content-container {
  padding-top: calc(var(--status-bar-height) + 44px);
  padding-bottom: 20px;
}

.search-section {
  padding: 16px;
  background: linear-gradient(135deg, #AB47BC 0%, #9C27B0 100%);
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  padding-top: 24px;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 10px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .search-icon {
    margin-right: 8px;
}

.search-input {
  flex: 1;
    height: 20px;
    font-size: 14px;
  color: #333333;
}

  .clear-button {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.hot-search-section, .search-history-section {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .section-title {
    font-size: 16px;
  font-weight: 600;
  color: #333333;
}

.clear-history {
  display: flex;
  align-items: center;

    .delete-icon {
      margin-right: 4px;
}

    text {
      font-size: 14px;
  color: #999999;
    }
  }
}

.hot-search-tags, .history-tags {
  display: flex;
  flex-wrap: wrap;
  
  .tag-item {
    background-color: #F5F5F5;
    border-radius: 16px;
    padding: 6px 12px;
    margin-right: 8px;
    margin-bottom: 8px;
    
    text {
      font-size: 14px;
      color: #666666;
    }
  }
}

.search-results-section {
  margin-top: 16px;
}

.filter-bar {
  display: flex;
  background-color: #FFFFFF;
  padding: 12px 16px;
  border-bottom: 1px solid #EEEEEE;

.filter-item {
  display: flex;
  align-items: center;
    margin-right: 24px;
  position: relative;
    
    text {
      font-size: 14px;
      color: #666666;
}

.sort-arrows {
  display: flex;
  flex-direction: column;
      margin-left: 4px;
      
      .arrow-up, .arrow-down {
        color: #CCCCCC;
        
        &.active {
          color: #9C27B0;
        }
      }
    }
    
    &--active {
      text {
        color: #9C27B0;
        font-weight: 500;
      }
      
      &::after {
        content: '';
        position: absolute;
        bottom: -12px;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #9C27B0;
      }
    }
  }
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  padding: 16px;
}

.price-compare-list {
  background-color: #FFFFFF;
  
  .compare-item {
    border-bottom: 8px solid #F5F5F5;
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .compare-header {
    display: flex;
    padding: 16px;
    position: relative;

.product-image {
      width: 80px;
      height: 80px;
      border-radius: 8px;
      margin-right: 12px;
}

.product-info {
      flex: 1;

.product-title {
        font-size: 14px;
  color: #333333;
  line-height: 1.4;
        margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .price-range {
        .price-label {
          font-size: 12px;
          color: #999999;
        }
        
        .price-value {
          font-size: 14px;
          color: #FF6B6B;
          font-weight: 500;
        }
      }
    }
    
    .expand-icon {
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      transition: transform 0.3s;
      
      &.expanded {
        transform: translateY(-50%) rotate(180deg);
      }
    }
  }
  
  .platform-list {
    background-color: #F9F9F9;
    padding: 0 16px;
    
    .platform-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #EEEEEE;
      
      &:last-child {
        border-bottom: none;
      }
      
      .platform-info {
  display: flex;
        align-items: center;
        
        .platform-icon {
          width: 20px;
          height: 20px;
          margin-right: 8px;
        }
        
        .platform-name {
          font-size: 14px;
          color: #666666;
        }
      }
      
      .platform-price {
  display: flex;
        flex-direction: column;
        align-items: flex-end;
        
        .price-value {
          font-size: 14px;
          color: #FF6B6B;
          font-weight: 500;
          margin-bottom: 2px;
}

.cashback-value {
          font-size: 12px;
          color: #9C27B0;
        }
      }
    }
  }
}

.loading-more, .no-more {
  text-align: center;
  padding: 16px 0;

  text {
    font-size: 14px;
  color: #999999;
  }
}

.empty-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 0;
  
  .empty-image {
    width: 120px;
    height: 120px;
    margin-bottom: 16px;
  }
  
  text {
    font-size: 16px;
    color: #666666;
    margin-bottom: 8px;
}

.empty-tips {
    font-size: 14px;
  color: #999999;
  }
}
</style> 