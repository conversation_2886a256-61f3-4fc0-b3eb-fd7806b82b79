{"version": 3, "file": "MerchantRecommend.js", "sources": ["components/index/MerchantRecommend.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7AvY29tcG9uZW50cy9pbmRleC9NZXJjaGFudFJlY29tbWVuZC52dWU"], "sourcesContent": ["<template>\n  <!-- 商家推荐模块 -->\n  <view class=\"merchant-recommend-section card-section fade-in\">\n    <view class=\"section-header\">\n      <view class=\"section-title-wrap\">\n        <text class=\"section-title\">商家推荐</text>\n      </view>\n      <view class=\"section-more\" @click=\"navigateToMore\">\n        <text class=\"more-text\">全部</text>\n        <text class=\"more-icon\">&gt;</text>\n      </view>\n    </view>\n    \n    <!-- 水平轮播，每页显示多个竖向卡片 -->\n    <swiper class=\"merchant-swiper\" \n            :current=\"currentMerchantPage\" \n            @change=\"onMerchantPageChange\" \n            circular \n            :autoplay=\"true\" \n            :interval=\"3000\" \n            :duration=\"500\">\n      <swiper-item v-for=\"pageIndex in Math.ceil(recommendBusinessList.length / 2)\" :key=\"'page-'+pageIndex\">\n        <view class=\"merchant-swiper-page\">\n          <view \n            class=\"merchant-card\" \n            v-for=\"merchant in getMerchantsForPage(pageIndex-1)\" \n            :key=\"merchant.id\"\n            @click=\"navigateToMerchant(merchant)\">\n            <!-- 商家图片 -->\n            <view class=\"merchant-image\">\n              <image :src=\"merchant.logo\" mode=\"aspectFill\"></image>\n              <!-- 标签覆盖层 -->\n              <view class=\"tag-overlay\">\n                <view v-if=\"merchant.isNew\" class=\"merchant-tag new-tag\">新店</view>\n                <view v-if=\"merchant.isHot\" class=\"merchant-tag hot-tag\">热门</view>\n              </view>\n            </view>\n            \n            <!-- 商家名称和评分 -->\n              <view class=\"merchant-info\">\n              <view class=\"merchant-name-row\">\n                <text class=\"merchant-name\">{{merchant.name}}</text>\n                <view class=\"merchant-rating\">\n                  <text class=\"rating-score\">{{merchant.rating || '4.8'}}</text>\n                  <text class=\"rating-icon\">★</text>\n                </view>\n              </view>\n              \n              <!-- 商家描述 -->\n              <text class=\"merchant-desc\">{{merchant.description}}</text>\n              \n              <!-- 分类和距离 -->\n              <view class=\"merchant-bottom\">\n                <view class=\"merchant-category\">{{merchant.category}}</view>\n                <view class=\"merchant-distance\">\n                  <text class=\"distance-icon\">📍</text>\n                  <text>{{merchant.distance || '500m'}}</text>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </swiper-item>\n    </swiper>\n    \n    <!-- 轮播指示器 -->\n    <view class=\"merchant-indicators\">\n      <view class=\"merchant-dot\" \n        v-for=\"(dot, i) in Math.ceil(recommendBusinessList.length / 2)\" \n        :key=\"i\" \n        :class=\"{ active: currentMerchantPage === i }\"\n        @click=\"changeMerchantPage(i)\"></view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref } from 'vue';\n\nconst currentMerchantPage = ref(0);\nconst recommendBusinessList = ref([\n        {\n          id: 1,\n          logo: '/static/images/merchant/logo1.jpg',\n          name: '品味小厨',\n          description: '特色家常菜 | 人均¥38',\n          category: '餐饮',\n          isFollowed: false,\n          url: '/pages/business/shop-detail?id=1',\n          rating: '4.9',\n          distance: '350m',\n          isHot: true\n        },\n        {\n          id: 2,\n          logo: '/static/images/merchant/logo2.jpg',\n          name: '鲜花花店',\n          description: '鲜花礼品 | 同城配送',\n          category: '礼品',\n          isFollowed: true,\n          url: '/pages/business/shop-detail?id=2',\n          rating: '4.7',\n          distance: '1.2km',\n          isNew: true\n        },\n        {\n          id: 3,\n          logo: '/static/images/merchant/logo3.jpg',\n          name: '快修先生',\n          description: '专业维修 | 上门服务',\n          category: '维修',\n          isFollowed: false,\n          url: '/pages/business/shop-detail?id=3',\n          rating: '4.8',\n          distance: '800m'\n        },\n        {\n          id: 4,\n          logo: '/static/images/merchant/logo4.jpg',\n          name: '鲜果超市',\n          description: '新鲜水果 | 当日配送',\n          category: '生鲜',\n          isFollowed: false,\n          url: '/pages/business/shop-detail?id=4',\n          rating: '4.6',\n          distance: '650m',\n          isHot: true\n        },\n        {\n          id: 5,\n          logo: '/static/images/merchant/logo1.jpg',\n          name: '川湘小馆',\n          description: '川湘菜 | 人均¥45',\n          category: '餐饮',\n          isFollowed: false,\n          url: '/pages/business/shop-detail?id=5',\n          rating: '4.5',\n          distance: '1.5km'\n        },\n        {\n          id: 6,\n          logo: '/static/images/merchant/logo2.jpg',\n          name: '优品超市',\n          description: '日用百货 | 满减活动',\n          category: '超市',\n          isFollowed: false,\n          url: '/pages/business/shop-detail?id=6',\n          rating: '4.7',\n          distance: '800m',\n          isNew: true\n        }\n]);\n\nfunction getMerchantsForPage(pageIndex) {\n      const startIndex = pageIndex * 2;\n  return recommendBusinessList.value.slice(startIndex, startIndex + 2);\n}\n\nfunction onMerchantPageChange(e) {\n  currentMerchantPage.value = e.detail.current;\n}\n\nfunction changeMerchantPage(index) {\n  currentMerchantPage.value = index;\n}\n\nfunction navigateToMerchant(item) {\n      if (!item.url) return;\n      \n      uni.navigateTo({\n        url: item.url,\n        fail: () => {\n          uni.showToast({\n            title: '页面跳转失败',\n            icon: 'none'\n          });\n        }\n      });\n}\n\nfunction followBusiness(id) {\n  const index = recommendBusinessList.value.findIndex(item => item.id === id);\n      if (index !== -1) {\n    recommendBusinessList.value[index].isFollowed = !recommendBusinessList.value[index].isFollowed;\n        \n        uni.showToast({\n      title: recommendBusinessList.value[index].isFollowed ? '收藏成功' : '取消收藏成功',\n          icon: 'success'\n        });\n      }\n}\n\nfunction navigateToMore() {\n      uni.navigateTo({\n        url: '/pages/business/list',\n        fail: () => {\n          uni.showToast({\n            title: '页面跳转失败',\n            icon: 'none'\n          });\n        }\n      });\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.merchant-recommend-section {\n  margin: 24rpx 30rpx 30rpx;\n  position: relative;\n  z-index: 2;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 35rpx; /* 35度圆角 */\n  padding: 30rpx 24rpx;\n  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08), \n              0 5rpx 15rpx rgba(0, 0, 0, 0.05);\n  overflow: hidden;\n  backdrop-filter: blur(20px);\n  -webkit-backdrop-filter: blur(20px);\n  border: 1rpx solid rgba(255, 255, 255, 0.8);\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24rpx;\n  padding: 0 10rpx;\n}\n\n.section-title-wrap {\n  display: flex;\n  align-items: center;\n}\n\n.section-title {\n  font-size: 34rpx;\n  font-weight: 700;\n  color: #1c1c1e;\n  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;\n  letter-spacing: 0.5rpx;\n  background: linear-gradient(135deg, #333, #666);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n}\n\n.section-more {\n  display: flex;\n  align-items: center;\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n  transition: all 0.2s ease;\n  background: rgba(0, 122, 255, 0.1);\n  border: 1rpx solid rgba(0, 122, 255, 0.2);\n}\n\n.section-more:active {\n  background: rgba(0, 122, 255, 0.2);\n  transform: scale(0.96);\n}\n\n.more-text {\n  font-size: 24rpx;\n  color: #007AFF;\n  font-weight: 600;\n}\n\n.more-icon {\n  font-size: 24rpx;\n  color: #007AFF;\n  margin-left: 4rpx;\n}\n\n/* 轮播区域 */\n.merchant-swiper {\n  width: 100%;\n  height: 380rpx;\n  margin-top: 20rpx;\n}\n\n.merchant-swiper-page {\n  height: 100%;\n  display: flex;\n  justify-content: space-between;\n  padding: 0 15rpx;\n  gap: 25rpx;\n}\n\n.merchant-card {\n  width: 310rpx;\n  height: 100%;\n  background: #ffffff;\n  border-radius: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.08),\n              0 5rpx 10rpx rgba(0, 0, 0, 0.05);\n  display: flex;\n  flex-direction: column;\n  transition: all 0.3s ease;\n  transform: perspective(800px) rotateX(5deg);\n  border: 1rpx solid rgba(255, 255, 255, 0.9);\n  position: relative;\n}\n\n.merchant-card:active {\n  transform: perspective(800px) rotateX(5deg) scale(0.98);\n  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.05);\n}\n\n/* 商家图片区域 */\n.merchant-image {\n  width: 100%;\n  height: 180rpx;\n  position: relative;\n  overflow: hidden;\n}\n\n.merchant-image::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 40rpx;\n  background: linear-gradient(to top, rgba(0,0,0,0.2), transparent);\n  z-index: 1;\n}\n\n.merchant-image image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: transform 0.5s ease;\n}\n\n.merchant-card:hover .merchant-image image {\n  transform: scale(1.05);\n}\n\n.tag-overlay {\n  position: absolute;\n  top: 12rpx;\n  right: 12rpx;\n  display: flex;\n  flex-direction: column;\n  gap: 8rpx;\n  z-index: 2;\n}\n\n.merchant-tag {\n  padding: 4rpx 12rpx;\n  border-radius: 12rpx;\n  font-size: 18rpx;\n  font-weight: 600;\n  color: white;\n  backdrop-filter: blur(8px);\n  -webkit-backdrop-filter: blur(8px);\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);\n  letter-spacing: 0.5rpx;\n}\n\n.new-tag {\n  background: linear-gradient(135deg, rgba(52, 199, 89, 0.9), rgba(48, 209, 88, 0.9));\n}\n\n.hot-tag {\n  background: linear-gradient(135deg, rgba(255, 69, 58, 0.9), rgba(255, 59, 48, 0.9));\n}\n\n/* 商家信息区域 */\n.merchant-info {\n  flex: 1;\n  padding: 16rpx;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  background: linear-gradient(to bottom, #ffffff, #f9f9f9);\n}\n\n.merchant-name-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8rpx;\n}\n\n.merchant-name {\n  font-size: 26rpx;\n  color: #1c1c1e;\n  font-weight: 700;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  max-width: 70%;\n  letter-spacing: 0.5rpx;\n}\n\n.merchant-rating {\n  display: flex;\n  align-items: center;\n  background: rgba(255, 149, 0, 0.1);\n  padding: 2rpx 8rpx;\n  border-radius: 10rpx;\n}\n\n.rating-score {\n  font-size: 20rpx;\n  color: #1c1c1e;\n  font-weight: 600;\n  margin-right: 4rpx;\n}\n\n.rating-icon {\n  font-size: 20rpx;\n  color: #FF9500;\n}\n\n.merchant-desc {\n  font-size: 22rpx;\n  color: #8a8a8e;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  margin-bottom: 12rpx;\n  line-height: 1.3;\n}\n\n.merchant-bottom {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 8rpx;\n}\n\n.merchant-category {\n  font-size: 20rpx;\n  color: #007AFF;\n  background: rgba(0, 122, 255, 0.1);\n  padding: 4rpx 12rpx;\n  border-radius: 12rpx;\n  font-weight: 600;\n  border: 1rpx solid rgba(0, 122, 255, 0.2);\n}\n\n.merchant-distance {\n  display: flex;\n  align-items: center;\n  font-size: 20rpx;\n  color: #8a8a8e;\n  background: rgba(142, 142, 147, 0.08);\n  padding: 4rpx 10rpx;\n  border-radius: 12rpx;\n}\n\n.distance-icon {\n  font-size: 20rpx;\n  margin-right: 4rpx;\n}\n\n/* 轮播指示器 */\n.merchant-indicators {\n  display: flex;\n  justify-content: center;\n  margin-top: 20rpx;\n}\n\n.merchant-dot {\n  width: 12rpx;\n  height: 12rpx;\n  border-radius: 6rpx;\n  background-color: rgba(0, 0, 0, 0.1);\n  margin: 0 6rpx;\n  transition: all 0.3s ease;\n  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.1);\n}\n\n.merchant-dot.active {\n  width: 28rpx;\n  background: linear-gradient(90deg, #007AFF, #5AC8FA);\n  box-shadow: 0 1rpx 3rpx rgba(0, 122, 255, 0.3);\n}\n\n.card-section {\n  margin-bottom: 20rpx;\n}\n\n.fade-in {\n  animation: fadeIn 0.5s ease-in-out;\n}\n\n@keyframes fadeIn {\n  from { opacity: 0; transform: translateY(20rpx); }\n  to { opacity: 1; transform: translateY(0); }\n}\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/components/index/MerchantRecommend.vue'\nwx.createComponent(Component)"], "names": ["ref", "uni"], "mappings": ";;;;;AA+EA,UAAM,sBAAsBA,cAAAA,IAAI,CAAC;AACjC,UAAM,wBAAwBA,cAAAA,IAAI;AAAA,MAC1B;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,UAAU;AAAA,MACX;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,UAAU;AAAA,MACX;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACR;AAAA,IACT,CAAC;AAED,aAAS,oBAAoB,WAAW;AAClC,YAAM,aAAa,YAAY;AACnC,aAAO,sBAAsB,MAAM,MAAM,YAAY,aAAa,CAAC;AAAA,IACrE;AAEA,aAAS,qBAAqB,GAAG;AAC/B,0BAAoB,QAAQ,EAAE,OAAO;AAAA,IACvC;AAEA,aAAS,mBAAmB,OAAO;AACjC,0BAAoB,QAAQ;AAAA,IAC9B;AAEA,aAAS,mBAAmB,MAAM;AAC5B,UAAI,CAAC,KAAK;AAAK;AAEfC,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,KAAK;AAAA,QACV,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAClB,CAAW;AAAA,QACF;AAAA,MACT,CAAO;AAAA,IACP;AAcA,aAAS,iBAAiB;AACpBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAClB,CAAW;AAAA,QACF;AAAA,MACT,CAAO;AAAA,IACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzMA,GAAG,gBAAgB,SAAS;"}