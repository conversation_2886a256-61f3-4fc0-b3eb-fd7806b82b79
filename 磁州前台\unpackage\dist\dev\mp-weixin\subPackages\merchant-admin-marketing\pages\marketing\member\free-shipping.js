"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      rules: [],
      // 免费配送规则列表
      ruleForm: {
        id: "",
        name: "",
        color: "#32CD32",
        // 默认绿色
        icon: "",
        condition: "",
        area: "",
        applicableLevels: [],
        description: ""
      },
      isEditing: false,
      // 是否为编辑模式
      currentRuleId: null,
      // 当前编辑的规则ID
      colorOptions: [
        "#FF6B22",
        // 橙色
        "#8A2BE2",
        // 紫色
        "#1E90FF",
        // 道奇蓝
        "#32CD32",
        // 酸橙绿
        "#FFD700",
        // 金色
        "#FF69B4",
        // 热粉红
        "#20B2AA",
        // 浅海绿
        "#FF8C00"
        // 深橙色
      ],
      conditionOptions: [
        "无门槛免费配送",
        "满50元免费配送",
        "满100元免费配送",
        "满200元免费配送"
      ],
      areaOptions: [
        "全城范围",
        "3公里内",
        "5公里内",
        "10公里内",
        "指定区域"
      ],
      memberLevels: []
      // 会员等级列表
    };
  },
  onLoad() {
    this.fetchRules();
    this.fetchMemberLevels();
  },
  methods: {
    // 获取免费配送规则列表
    fetchRules() {
      this.rules = [
        {
          id: "1",
          name: "银卡会员免费配送",
          color: "#C0C0C0",
          icon: "/static/images/shipping-silver.svg",
          condition: "满100元免费配送",
          area: "5公里内",
          applicableLevels: ["银卡会员"],
          description: "银卡会员订单满100元，5公里内免费配送"
        },
        {
          id: "2",
          name: "金卡会员免费配送",
          color: "#FFD700",
          icon: "/static/images/shipping-gold.svg",
          condition: "满50元免费配送",
          area: "10公里内",
          applicableLevels: ["金卡会员"],
          description: "金卡会员订单满50元，10公里内免费配送"
        },
        {
          id: "3",
          name: "钻石会员免费配送",
          color: "#B9F2FF",
          icon: "/static/images/shipping-diamond.svg",
          condition: "无门槛免费配送",
          area: "全城范围",
          applicableLevels: ["钻石会员"],
          description: "钻石会员无门槛享受全城免费配送服务"
        }
      ];
    },
    // 获取会员等级列表
    fetchMemberLevels() {
      this.memberLevels = [
        {
          id: "1",
          name: "普通会员"
        },
        {
          id: "2",
          name: "银卡会员"
        },
        {
          id: "3",
          name: "金卡会员"
        },
        {
          id: "4",
          name: "钻石会员"
        }
      ];
    },
    // 显示添加规则弹窗
    showAddRuleModal() {
      this.isEditing = false;
      this.ruleForm = {
        id: "",
        name: "",
        color: "#32CD32",
        icon: "",
        condition: "",
        area: "",
        applicableLevels: [],
        description: ""
      };
      this.$refs.ruleFormPopup.open();
    },
    // 编辑规则
    editRule(rule) {
      this.isEditing = true;
      this.currentRuleId = rule.id;
      this.ruleForm = JSON.parse(JSON.stringify(rule));
      this.$refs.ruleFormPopup.open();
    },
    // 关闭规则表单弹窗
    closeRuleModal() {
      this.$refs.ruleFormPopup.close();
    },
    // 保存规则表单
    saveRuleForm() {
      if (!this.ruleForm.name) {
        common_vendor.index.showToast({
          title: "请输入规则名称",
          icon: "none"
        });
        return;
      }
      if (!this.ruleForm.condition) {
        common_vendor.index.showToast({
          title: "请选择免费条件",
          icon: "none"
        });
        return;
      }
      if (!this.ruleForm.area) {
        common_vendor.index.showToast({
          title: "请选择配送范围",
          icon: "none"
        });
        return;
      }
      if (this.ruleForm.applicableLevels.length === 0) {
        common_vendor.index.showToast({
          title: "请选择适用等级",
          icon: "none"
        });
        return;
      }
      if (this.isEditing) {
        const index = this.rules.findIndex((item) => item.id === this.currentRuleId);
        if (index !== -1) {
          this.rules.splice(index, 1, JSON.parse(JSON.stringify(this.ruleForm)));
        }
      } else {
        this.ruleForm.id = Date.now().toString();
        this.rules.push(JSON.parse(JSON.stringify(this.ruleForm)));
      }
      this.closeRuleModal();
      common_vendor.index.showToast({
        title: this.isEditing ? "规则修改成功" : "规则添加成功"
      });
    },
    // 确认删除规则
    confirmDeleteRule(rule) {
      this.currentRuleId = rule.id;
      this.$refs.deleteConfirmPopup.open();
    },
    // 删除规则
    deleteRule() {
      const index = this.rules.findIndex((item) => item.id === this.currentRuleId);
      if (index !== -1) {
        this.rules.splice(index, 1);
      }
      this.$refs.deleteConfirmPopup.close();
      common_vendor.index.showToast({
        title: "规则删除成功"
      });
    },
    // 关闭删除确认弹窗
    closeDeleteConfirm() {
      this.$refs.deleteConfirmPopup.close();
    },
    // 选择图标
    chooseIcon() {
      common_vendor.index.chooseImage({
        count: 1,
        success: (res) => {
          this.ruleForm.icon = res.tempFilePaths[0];
        }
      });
    },
    // 选择配送范围变更
    onAreaChange(e) {
      const index = e.detail.value;
      this.ruleForm.area = this.areaOptions[index];
    },
    // 判断等级是否被选中
    isLevelSelected(levelName) {
      return this.ruleForm.applicableLevels.includes(levelName);
    },
    // 切换等级选择
    toggleLevelSelection(levelName) {
      const index = this.ruleForm.applicableLevels.indexOf(levelName);
      if (index === -1) {
        this.ruleForm.applicableLevels.push(levelName);
      } else {
        this.ruleForm.applicableLevels.splice(index, 1);
      }
    }
  }
};
if (!Array) {
  const _component_uni_popup = common_vendor.resolveComponent("uni-popup");
  const _component_uni_popup_dialog = common_vendor.resolveComponent("uni-popup-dialog");
  (_component_uni_popup + _component_uni_popup_dialog)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.showAddRuleModal && $options.showAddRuleModal(...args)),
    b: $data.rules.length === 0
  }, $data.rules.length === 0 ? {
    c: common_assets._imports_0$46
  } : {
    d: common_vendor.f($data.rules, (rule, index, i0) => {
      return {
        a: common_vendor.t(rule.name),
        b: common_vendor.o(($event) => $options.editRule(rule), index),
        c: common_vendor.o(($event) => $options.confirmDeleteRule(rule), index),
        d: rule.color,
        e: rule.icon,
        f: common_vendor.f(rule.applicableLevels, (level, idx, i1) => {
          return {
            a: common_vendor.t(level),
            b: idx
          };
        }),
        g: common_vendor.t(rule.condition),
        h: common_vendor.t(rule.area),
        i: common_vendor.t(rule.description || "暂无规则说明"),
        j: index
      };
    })
  }, {
    e: common_vendor.t($data.isEditing ? "编辑规则" : "添加规则"),
    f: common_vendor.o((...args) => $options.closeRuleModal && $options.closeRuleModal(...args)),
    g: $data.ruleForm.name,
    h: common_vendor.o(($event) => $data.ruleForm.name = $event.detail.value),
    i: common_vendor.f($data.colorOptions, (color, idx, i0) => {
      return {
        a: idx,
        b: $data.ruleForm.color === color ? 1 : "",
        c: color,
        d: common_vendor.o(($event) => $data.ruleForm.color = color, idx)
      };
    }),
    j: $data.ruleForm.icon
  }, $data.ruleForm.icon ? {
    k: $data.ruleForm.icon
  } : {
    l: common_vendor.o((...args) => $options.chooseIcon && $options.chooseIcon(...args))
  }, {
    m: common_vendor.f($data.conditionOptions, (condition, idx, i0) => {
      return {
        a: common_vendor.t(condition),
        b: idx,
        c: $data.ruleForm.condition === condition ? 1 : "",
        d: common_vendor.o(($event) => $data.ruleForm.condition = condition, idx)
      };
    }),
    n: common_vendor.t($data.ruleForm.area || "请选择配送范围"),
    o: $data.areaOptions,
    p: common_vendor.o((...args) => $options.onAreaChange && $options.onAreaChange(...args)),
    q: common_vendor.f($data.memberLevels, (level, idx, i0) => {
      return {
        a: common_vendor.t($options.isLevelSelected(level.name) ? "✓" : ""),
        b: common_vendor.t(level.name),
        c: idx,
        d: $options.isLevelSelected(level.name) ? 1 : "",
        e: common_vendor.o(($event) => $options.toggleLevelSelection(level.name), idx)
      };
    }),
    r: $data.ruleForm.description,
    s: common_vendor.o(($event) => $data.ruleForm.description = $event.detail.value),
    t: common_vendor.o((...args) => $options.closeRuleModal && $options.closeRuleModal(...args)),
    v: common_vendor.o((...args) => $options.saveRuleForm && $options.saveRuleForm(...args)),
    w: common_vendor.sr("ruleFormPopup", "7a206f96-0"),
    x: common_vendor.p({
      type: "center"
    }),
    y: common_vendor.o($options.deleteRule),
    z: common_vendor.o($options.closeDeleteConfirm),
    A: common_vendor.p({
      type: "warning",
      title: "删除确认",
      content: "确定要删除该免费配送规则吗？删除后将无法恢复。",
      ["before-close"]: true
    }),
    B: common_vendor.sr("deleteConfirmPopup", "7a206f96-1"),
    C: common_vendor.p({
      type: "dialog"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-7a206f96"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/member/free-shipping.js.map
