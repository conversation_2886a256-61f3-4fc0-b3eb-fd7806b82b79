/* 通用详情页样式优化 */

/* 详情页容器 */
.detail-container {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 150rpx;
}

.detail-wrapper {
  padding: 24rpx;
}

/* 内容卡片通用样式 */
.content-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

/* 标题区样式优化 */
.title-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.main-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  flex: 1;
  margin-right: 20rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.price-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff4d4f;
  white-space: nowrap;
}

/* 元数据样式 */
.meta-info {
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.tag-group {
  display: flex;
  flex-wrap: wrap;
  margin-right: 20rpx;
  margin-bottom: 8rpx;
}

.info-tag {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.08);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}

.publish-time {
  font-size: 24rpx;
  color: #999;
}

/* 轮播图优化 */
.detail-swiper {
  height: 420rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.swiper-image {
  width: 100%;
  height: 100%;
}

/* 基本信息卡片内部布局 */
.basic-info {
  display: flex;
  flex-wrap: wrap;
  background-color: #f9fafc;
  border-radius: 12rpx;
  padding: 16rpx 0;
  margin-top: 20rpx;
}

.info-item {
  width: 50%;
  padding: 12rpx 24rpx;
  box-sizing: border-box;
}

.info-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 详情信息列表 */
.detail-list {
  display: flex;
  flex-direction: column;
}

.detail-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #888;
}

.detail-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

/* 区块标题优化 */
.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 16rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 6rpx;
  height: 28rpx;
  background-color: #1890ff;
  border-radius: 3rpx;
}

/* 描述内容样式 */
.description-content {
  padding: 20rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  line-height: 1.6;
}

.description-text {
  font-size: 28rpx;
  color: #555;
  line-height: 1.6;
}

/* 联系人信息样式 */
.contact-content {
  padding: 20rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.contact-label {
  width: 120rpx;
  font-size: 28rpx;
  color: #888;
}

.contact-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.contact-phone {
  color: #52c41a;
  font-weight: 500;
}

.contact-tips {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
  padding: 10rpx 16rpx;
  background-color: rgba(255, 152, 0, 0.1);
  border-radius: 8rpx;
}

.tips-icon {
  font-size: 24rpx;
  color: #ff9800;
  margin-right: 8rpx;
}

.tips-text {
  font-size: 24rpx;
  color: #ff9800;
}

/* 发布者/卖家信息样式 */
.publisher-header {
  display: flex;
  align-items: center;
  padding-bottom: 16rpx;
}

.avatar-container {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
  border: 1rpx solid #f0f0f0;
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.publisher-info {
  flex: 1;
}

.publisher-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.publisher-meta {
  display: flex;
  align-items: center;
}

.meta-text {
  font-size: 24rpx;
  color: #888;
  margin-right: 16rpx;
  padding: 2rpx 12rpx;
  background-color: #f5f7fa;
  border-radius: 4rpx;
}

/* 相似推荐列表 */
.similar-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.similar-item {
  width: calc(50% - 20rpx);
  margin: 10rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  overflow: hidden;
}

.similar-image {
  height: 180rpx;
  width: 100%;
}

.similar-info {
  padding: 12rpx;
}

.similar-title {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.similar-price {
  font-size: 26rpx;
  color: #ff4d4f;
  font-weight: 500;
}

.similar-meta {
  font-size: 22rpx;
  color: #999;
  margin-top: 4rpx;
}

/* 底部互动工具栏优化 */
.interaction-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10rpx 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  height: 120rpx;
  z-index: 100;
}

.toolbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6rpx 0;
  margin: 0 4rpx;
}

.toolbar-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}

.toolbar-text {
  font-size: 22rpx;
  color: #666;
}

.share-button {
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
  line-height: normal;
  border-radius: 0;
  flex: 1;
}

.share-button::after {
  display: none;
}

.call-button {
  flex: 3;
  background: linear-gradient(135deg, #0052CC, #0066FF);
  height: 90rpx;
  margin: 0 0 0 10rpx;
  border-radius: 45rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
}

.call-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.call-text {
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  line-height: 1.2;
}

.call-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 20rpx;
  line-height: 1.2;
}

/* 自适应调整 */
@media screen and (max-width: 375px) {
  .content-card {
    padding: 20rpx;
  }
  
  .main-title {
    font-size: 32rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .detail-wrapper {
    padding: 16rpx;
  }
  
  .detail-swiper {
    height: 380rpx;
  }
} 