{"version": 3, "file": "partner-withdraw.js", "sources": ["subPackages/partner/pages/partner-withdraw.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNccGFydG5lclxwYWdlc1xwYXJ0bmVyLXdpdGhkcmF3LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"withdraw-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\">\r\n      <view class=\"navbar-left\" @click=\"goBack\">\r\n        <image src=\"/static/images/tabbar/最新返回键.png\" class=\"back-icon\"></image>\r\n      </view>\r\n      <view class=\"navbar-title\">收益提现</view>\r\n      <view class=\"navbar-right\">\r\n        <!-- 预留位置与发布页面保持一致 -->\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 添加顶部安全区域 -->\r\n    <view class=\"safe-area-top\"></view>\r\n    \r\n    <!-- 账户余额信息 -->\r\n    <view class=\"balance-card\">\r\n      <view class=\"balance-title\">可提现余额</view>\r\n      <view class=\"balance-amount\">¥ {{availableBalance}}</view>\r\n      <view class=\"balance-detail\">\r\n        <view class=\"detail-item\">\r\n          <text>总收益</text>\r\n          <text>¥ {{totalIncome}}</text>\r\n        </view>\r\n        <view class=\"detail-item\">\r\n          <text>已提现</text>\r\n          <text>¥ {{withdrawedAmount}}</text>\r\n        </view>\r\n        <view class=\"detail-item\">\r\n          <text>冻结中</text>\r\n          <text>¥ {{freezeAmount}}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 提现表单 -->\r\n    <view class=\"withdraw-form\">\r\n      <view class=\"form-title\">提现金额</view>\r\n      <view class=\"amount-input\">\r\n        <text class=\"currency\">¥</text>\r\n        <input type=\"digit\" v-model=\"amount\" placeholder=\"请输入提现金额\" />\r\n      </view>\r\n      <view class=\"amount-tips\">\r\n        <text>提现金额 ≥ 1元，提现手续费率{{feeRate}}%</text>\r\n        <text class=\"all-btn\" @tap=\"setMaxAmount\">全部提现</text>\r\n      </view>\r\n\r\n      <!-- 实际到账金额 -->\r\n      <view class=\"actual-amount\" v-if=\"amount > 0\">\r\n        <text>实际到账：</text>\r\n        <text class=\"amount\">¥ {{actualAmount}}</text>\r\n      </view>\r\n\r\n      <!-- 提现方式选择 -->\r\n      <view class=\"withdraw-method\">\r\n        <view class=\"method-title\">提现方式</view>\r\n        <view class=\"method-list\">\r\n          <view \r\n            class=\"method-item\" \r\n            :class=\"{'active': selectedMethod === 'wechat'}\" \r\n            @tap=\"selectMethod('wechat')\"\r\n          >\r\n            <image src=\"/static/images/wechat-pay.png\" mode=\"aspectFit\"></image>\r\n            <text>微信</text>\r\n            <view class=\"check-icon\" v-if=\"selectedMethod === 'wechat'\">✓</view>\r\n          </view>\r\n          <view \r\n            class=\"method-item\" \r\n            :class=\"{'active': selectedMethod === 'alipay'}\" \r\n            @tap=\"selectMethod('alipay')\"\r\n          >\r\n            <image src=\"/static/images/alipay.png\" mode=\"aspectFit\"></image>\r\n            <text>支付宝</text>\r\n            <view class=\"check-icon\" v-if=\"selectedMethod === 'alipay'\">✓</view>\r\n          </view>\r\n          <view \r\n            class=\"method-item\" \r\n            :class=\"{'active': selectedMethod === 'bank'}\" \r\n            @tap=\"selectMethod('bank')\"\r\n          >\r\n            <image src=\"/static/images/bank-card.png\" mode=\"aspectFit\"></image>\r\n            <text>银行卡</text>\r\n            <view class=\"check-icon\" v-if=\"selectedMethod === 'bank'\">✓</view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 支付宝账号输入 -->\r\n      <view class=\"account-input\" v-if=\"selectedMethod === 'alipay'\">\r\n        <view class=\"input-item\">\r\n          <text class=\"label\">支付宝账号</text>\r\n          <input type=\"text\" v-model=\"alipayAccount\" placeholder=\"请输入支付宝账号\" />\r\n        </view>\r\n        <view class=\"input-item\">\r\n          <text class=\"label\">真实姓名</text>\r\n          <input type=\"text\" v-model=\"alipayName\" placeholder=\"请输入真实姓名\" />\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 银行卡信息输入 -->\r\n      <view class=\"account-input\" v-if=\"selectedMethod === 'bank'\">\r\n        <view class=\"input-item\">\r\n          <text class=\"label\">开户银行</text>\r\n          <picker @change=\"bankChange\" :value=\"bankIndex\" :range=\"bankList\">\r\n            <view class=\"picker\">{{bankList[bankIndex] || '请选择开户银行'}}</view>\r\n          </picker>\r\n        </view>\r\n        <view class=\"input-item\">\r\n          <text class=\"label\">银行卡号</text>\r\n          <input type=\"number\" v-model=\"bankCard\" placeholder=\"请输入银行卡号\" />\r\n        </view>\r\n        <view class=\"input-item\">\r\n          <text class=\"label\">开户人</text>\r\n          <input type=\"text\" v-model=\"bankOwner\" placeholder=\"请输入开户人姓名\" />\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 提交按钮 -->\r\n      <button \r\n        class=\"withdraw-btn\" \r\n        :disabled=\"!canWithdraw\" \r\n        :class=\"{'disabled': !canWithdraw}\"\r\n        @tap=\"submitWithdraw\"\r\n      >\r\n        确认提现\r\n      </button>\r\n    </view>\r\n\r\n    <!-- 提现规则 -->\r\n    <view class=\"withdraw-rules\">\r\n      <view class=\"rules-title\">提现规则</view>\r\n      <view class=\"rules-content\">\r\n        <view class=\"rule-item\">1. 提现金额需≥1元，单笔提现上限为50000元</view>\r\n        <view class=\"rule-item\">2. 提现手续费率为{{feeRate}}%，从提现金额中扣除</view>\r\n        <view class=\"rule-item\">3. 提现申请提交后，平台将在1-3个工作日内审核</view>\r\n        <view class=\"rule-item\">4. 审核通过后，资金将在24小时内到账</view>\r\n        <view class=\"rule-item\">5. 请确保提供的账户信息准确无误，因信息错误导致的提现失败责任自负</view>\r\n        <view class=\"rule-item\">6. 如遇节假日，处理时间可能顺延</view>\r\n        <view class=\"rule-item\">7. 如有疑问，请联系客服</view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 提现记录 -->\r\n    <view class=\"withdraw-history\">\r\n      <view class=\"history-title\">最近提现记录</view>\r\n      <view class=\"history-list\" v-if=\"historyList.length > 0\">\r\n        <view class=\"history-item\" v-for=\"(item, index) in historyList\" :key=\"index\">\r\n          <view class=\"history-left\">\r\n            <view class=\"history-amount\">¥ {{item.amount}}</view>\r\n            <view class=\"history-time\">{{item.time}}</view>\r\n          </view>\r\n          <view class=\"history-right\">\r\n            <view class=\"history-status\" :class=\"'status-'+item.status\">\r\n              {{statusText[item.status]}}\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      <view class=\"no-record\" v-else>\r\n        <image src=\"/static/images/no-record.png\" mode=\"aspectFit\"></image>\r\n        <text>暂无提现记录</text>\r\n      </view>\r\n      <view class=\"view-more\" @tap=\"navigateToWithdrawHistory\" v-if=\"historyList.length > 0\">\r\n        查看更多记录\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 提现确认弹窗 -->\r\n    <view class=\"withdraw-modal\" v-if=\"showConfirmModal\">\r\n      <view class=\"modal-mask\" @tap=\"cancelWithdraw\"></view>\r\n      <view class=\"modal-content\">\r\n        <view class=\"modal-title\">确认提现</view>\r\n        <view class=\"modal-info\">\r\n          <view class=\"info-item\">\r\n            <text class=\"label\">提现金额</text>\r\n            <text class=\"value\">¥ {{amount}}</text>\r\n          </view>\r\n          <view class=\"info-item\">\r\n            <text class=\"label\">手续费</text>\r\n            <text class=\"value\">¥ {{fee}}</text>\r\n          </view>\r\n          <view class=\"info-item highlight\">\r\n            <text class=\"label\">实际到账</text>\r\n            <text class=\"value\">¥ {{actualAmount}}</text>\r\n          </view>\r\n          <view class=\"info-item\">\r\n            <text class=\"label\">提现方式</text>\r\n            <text class=\"value\">{{methodText[selectedMethod]}}</text>\r\n          </view>\r\n          <view class=\"info-item\" v-if=\"selectedMethod === 'alipay'\">\r\n            <text class=\"label\">支付宝账号</text>\r\n            <text class=\"value\">{{alipayAccount}}</text>\r\n          </view>\r\n          <view class=\"info-item\" v-if=\"selectedMethod === 'bank'\">\r\n            <text class=\"label\">银行信息</text>\r\n            <text class=\"value\">{{bankList[bankIndex]}} ({{bankCard}})</text>\r\n          </view>\r\n        </view>\r\n        <view class=\"modal-btns\">\r\n          <button class=\"cancel-btn\" @tap=\"cancelWithdraw\">取消</button>\r\n          <button class=\"confirm-btn\" @tap=\"confirmWithdraw\">确认</button>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 提现成功弹窗 -->\r\n    <view class=\"withdraw-modal\" v-if=\"showSuccessModal\">\r\n      <view class=\"modal-mask\"></view>\r\n      <view class=\"modal-content success-modal\">\r\n        <image src=\"/static/images/success-icon.png\" mode=\"aspectFit\" class=\"success-icon\"></image>\r\n        <view class=\"success-title\">提现申请已提交</view>\r\n        <view class=\"success-desc\">平台将在1-3个工作日内审核，请留意提现记录</view>\r\n        <button class=\"success-btn\" @tap=\"closeSuccessModal\">我知道了</button>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, computed } from 'vue';\r\n\r\n// 响应式状态\r\nconst availableBalance = ref(2358.65); // 可提现余额\r\nconst totalIncome = ref(3500.00); // 总收益\r\nconst withdrawedAmount = ref(1000.00); // 已提现金额\r\nconst freezeAmount = ref(141.35); // 冻结金额\r\nconst amount = ref(''); // 提现金额\r\nconst feeRate = ref(0.6); // 手续费率\r\nconst selectedMethod = ref('wechat'); // 选中的提现方式\r\nconst alipayAccount = ref(''); // 支付宝账号\r\nconst alipayName = ref(''); // 支付宝实名\r\nconst bankIndex = ref(0); // 选中的银行索引\r\nconst bankList = [\r\n  '中国工商银行', '中国农业银行', '中国建设银行', '中国银行', \r\n  '交通银行', '招商银行', '中信银行', '浦发银行', \r\n  '民生银行', '华夏银行', '兴业银行', '平安银行'\r\n];\r\nconst bankCard = ref(''); // 银行卡号\r\nconst bankOwner = ref(''); // 开户人\r\nconst showConfirmModal = ref(false); // 是否显示确认弹窗\r\nconst showSuccessModal = ref(false); // 是否显示成功弹窗\r\n\r\nconst historyList = ref([\r\n  { amount: 500.00, time: '2023-12-15 14:30:25', status: 'success' },\r\n  { amount: 300.00, time: '2023-11-20 10:15:36', status: 'pending' },\r\n  { amount: 200.00, time: '2023-10-05 16:45:12', status: 'success' }\r\n]);\r\n\r\nconst statusText = {\r\n  'success': '已到账',\r\n  'pending': '审核中',\r\n  'processing': '处理中',\r\n  'failed': '提现失败'\r\n};\r\n\r\nconst methodText = {\r\n  'wechat': '微信',\r\n  'alipay': '支付宝',\r\n  'bank': '银行卡'\r\n};\r\n\r\n// 计算属性\r\n// 计算实际到账金额\r\nconst actualAmount = computed(() => {\r\n  if (!amount.value || isNaN(amount.value)) return 0;\r\n  let fee = amount.value * (feeRate.value / 100);\r\n  return (amount.value - fee).toFixed(2);\r\n});\r\n\r\n// 计算手续费\r\nconst fee = computed(() => {\r\n  if (!amount.value || isNaN(amount.value)) return 0;\r\n  return (amount.value * (feeRate.value / 100)).toFixed(2);\r\n});\r\n\r\n// 是否可以提现\r\nconst canWithdraw = computed(() => {\r\n  if (!amount.value || isNaN(amount.value) || amount.value <= 0) return false;\r\n  if (amount.value > availableBalance.value) return false;\r\n  if (amount.value < 1) return false;\r\n  \r\n  if (selectedMethod.value === 'alipay' && (!alipayAccount.value || !alipayName.value)) return false;\r\n  if (selectedMethod.value === 'bank' && (!bankCard.value || !bankOwner.value)) return false;\r\n  \r\n  return true;\r\n});\r\n\r\n// 方法\r\n// 设置最大金额\r\nconst setMaxAmount = () => {\r\n  amount.value = availableBalance.value.toString();\r\n};\r\n\r\n// 选择提现方式\r\nconst selectMethod = (method) => {\r\n  selectedMethod.value = method;\r\n};\r\n\r\n// 银行选择变化\r\nconst bankChange = (e) => {\r\n  bankIndex.value = e.detail.value;\r\n};\r\n\r\n// 提交提现\r\nconst submitWithdraw = () => {\r\n  if (!canWithdraw.value) return;\r\n  showConfirmModal.value = true;\r\n};\r\n\r\n// 取消提现\r\nconst cancelWithdraw = () => {\r\n  showConfirmModal.value = false;\r\n};\r\n\r\n// 确认提现\r\nconst confirmWithdraw = () => {\r\n  // 这里调用提现API\r\n  uni.showLoading({\r\n    title: '提交中...'\r\n  });\r\n  \r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    showConfirmModal.value = false;\r\n    showSuccessModal.value = true;\r\n    \r\n    // 模拟提现成功后更新余额\r\n    availableBalance.value -= Number(amount.value);\r\n    withdrawedAmount.value += Number(amount.value);\r\n    \r\n    // 添加提现记录\r\n    historyList.value.unshift({\r\n      amount: Number(amount.value),\r\n      time: formatDate(new Date()),\r\n      status: 'pending'\r\n    });\r\n    \r\n    // 重置表单\r\n    amount.value = '';\r\n    alipayAccount.value = '';\r\n    alipayName.value = '';\r\n    bankCard.value = '';\r\n    bankOwner.value = '';\r\n  }, 1500);\r\n};\r\n\r\n// 关闭成功弹窗\r\nconst closeSuccessModal = () => {\r\n  showSuccessModal.value = false;\r\n};\r\n\r\n// 跳转到提现记录页面\r\nconst navigateToWithdrawHistory = () => {\r\n  uni.navigateTo({\r\n    url: '/pages/my/partner-withdraw-history'\r\n  });\r\n};\r\n\r\n// 格式化日期\r\nconst formatDate = (date) => {\r\n  const year = date.getFullYear();\r\n  const month = (date.getMonth() + 1).toString().padStart(2, '0');\r\n  const day = date.getDate().toString().padStart(2, '0');\r\n  const hour = date.getHours().toString().padStart(2, '0');\r\n  const minute = date.getMinutes().toString().padStart(2, '0');\r\n  const second = date.getSeconds().toString().padStart(2, '0');\r\n  \r\n  return `${year}-${month}-${day} ${hour}:${minute}:${second}`;\r\n};\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.withdraw-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: 30rpx;\r\n  padding-top: calc(44px + var(--status-bar-height));\r\n}\r\n\r\n.custom-navbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  height: 88rpx;\r\n  padding: 0 30rpx;\r\n  padding-top: 44px; /* 状态栏高度 */\r\n  position: fixed; /* 改为固定定位 */\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background-image: linear-gradient(135deg, #0066FF, #0052CC); /* 改为与发布页一致的渐变角度 */\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.15);\r\n  z-index: 100; /* 提高z-index确保在最上层 */\r\n}\r\n\r\n.navbar-left {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n  z-index: 20; /* 确保在标题上层，可以被点击 */\r\n}\r\n\r\n.back-icon {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.navbar-title {\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  font-size: 36rpx;\r\n  font-weight: 700;\r\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\r\n  text-align: center;\r\n}\r\n\r\n.navbar-right {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n}\r\n\r\n.safe-area-top {\r\n  height: var(--status-bar-height);\r\n  width: 100%;\r\n  background-image: linear-gradient(135deg, #0066FF, #0052CC);\r\n}\r\n\r\n.balance-card {\r\n  margin: 30rpx;\r\n  background: linear-gradient(135deg, #1677FF, #0E5FD8);\r\n  border-radius: 16rpx;\r\n  padding: 40rpx;\r\n  color: #ffffff;\r\n  box-shadow: 0 10rpx 20rpx rgba(22, 119, 255, 0.2);\r\n}\r\n\r\n.balance-title {\r\n  font-size: 28rpx;\r\n  opacity: 0.9;\r\n}\r\n\r\n.balance-amount {\r\n  font-size: 60rpx;\r\n  font-weight: bold;\r\n  margin: 20rpx 0 30rpx;\r\n}\r\n\r\n.balance-detail {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  border-top: 1px solid rgba(255, 255, 255, 0.2);\r\n  padding-top: 20rpx;\r\n}\r\n\r\n.detail-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  font-size: 24rpx;\r\n  \r\n  text:first-child {\r\n    opacity: 0.8;\r\n    margin-bottom: 8rpx;\r\n  }\r\n  \r\n  text:last-child {\r\n    font-size: 28rpx;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n.withdraw-form {\r\n  margin: 30rpx;\r\n  background-color: #ffffff;\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.form-title, .method-title {\r\n  font-size: 30rpx;\r\n  font-weight: 500;\r\n  margin-bottom: 20rpx;\r\n  color: #333333;\r\n}\r\n\r\n.amount-input {\r\n  display: flex;\r\n  align-items: center;\r\n  border-bottom: 1px solid #eeeeee;\r\n  padding-bottom: 20rpx;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.currency {\r\n  font-size: 50rpx;\r\n  font-weight: bold;\r\n  margin-right: 10rpx;\r\n  color: #333333;\r\n}\r\n\r\n.amount-input input {\r\n  font-size: 50rpx;\r\n  flex: 1;\r\n}\r\n\r\n.amount-tips {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  font-size: 24rpx;\r\n  color: #999999;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.all-btn {\r\n  color: #1677FF;\r\n}\r\n\r\n.actual-amount {\r\n  background-color: #f8f8f8;\r\n  padding: 20rpx;\r\n  border-radius: 8rpx;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  font-size: 28rpx;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.actual-amount .amount {\r\n  color: #1677FF;\r\n  font-weight: 500;\r\n}\r\n\r\n.withdraw-method {\r\n  margin-top: 30rpx;\r\n}\r\n\r\n.method-list {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.method-item {\r\n  width: 30%;\r\n  height: 150rpx;\r\n  border: 1px solid #eeeeee;\r\n  border-radius: 12rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  position: relative;\r\n  \r\n  image {\r\n    width: 60rpx;\r\n    height: 60rpx;\r\n    margin-bottom: 10rpx;\r\n  }\r\n  \r\n  text {\r\n    font-size: 26rpx;\r\n    color: #666666;\r\n  }\r\n  \r\n  &.active {\r\n    border-color: #1677FF;\r\n    background-color: rgba(22, 119, 255, 0.05);\r\n  }\r\n  \r\n  .check-icon {\r\n    position: absolute;\r\n    bottom: 0;\r\n    right: 0;\r\n    width: 40rpx;\r\n    height: 40rpx;\r\n    background-color: #1677FF;\r\n    color: #ffffff;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    font-size: 24rpx;\r\n    border-top-left-radius: 12rpx;\r\n  }\r\n}\r\n\r\n.account-input {\r\n  margin-top: 30rpx;\r\n}\r\n\r\n.input-item {\r\n  margin-bottom: 20rpx;\r\n  \r\n  .label {\r\n    font-size: 28rpx;\r\n    color: #666666;\r\n    margin-bottom: 10rpx;\r\n    display: block;\r\n  }\r\n  \r\n  input, .picker {\r\n    height: 80rpx;\r\n    background-color: #f8f8f8;\r\n    border-radius: 8rpx;\r\n    padding: 0 20rpx;\r\n    font-size: 28rpx;\r\n    width: 100%;\r\n    box-sizing: border-box;\r\n  }\r\n  \r\n  .picker {\r\n    display: flex;\r\n    align-items: center;\r\n    color: #333333;\r\n  }\r\n}\r\n\r\n.withdraw-btn {\r\n  background: linear-gradient(135deg, #1677FF, #0E5FD8);\r\n  color: #ffffff;\r\n  height: 88rpx;\r\n  border-radius: 44rpx;\r\n  font-size: 32rpx;\r\n  margin-top: 40rpx;\r\n  font-weight: 500;\r\n  box-shadow: 0 10rpx 20rpx rgba(22, 119, 255, 0.2);\r\n  \r\n  &.disabled {\r\n    background: linear-gradient(135deg, #cccccc, #999999);\r\n    box-shadow: none;\r\n  }\r\n}\r\n\r\n.withdraw-rules {\r\n  margin: 30rpx;\r\n  background-color: #ffffff;\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.rules-title {\r\n  font-size: 30rpx;\r\n  font-weight: 500;\r\n  margin-bottom: 20rpx;\r\n  color: #333333;\r\n}\r\n\r\n.rules-content {\r\n  font-size: 26rpx;\r\n  color: #666666;\r\n  line-height: 1.8;\r\n}\r\n\r\n.rule-item {\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.withdraw-history {\r\n  margin: 30rpx;\r\n  background-color: #ffffff;\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.history-title {\r\n  font-size: 30rpx;\r\n  font-weight: 500;\r\n  margin-bottom: 20rpx;\r\n  color: #333333;\r\n}\r\n\r\n.history-list {\r\n  max-height: 400rpx;\r\n  overflow-y: auto;\r\n}\r\n\r\n.history-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20rpx 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  \r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n}\r\n\r\n.history-left {\r\n  .history-amount {\r\n    font-size: 30rpx;\r\n    font-weight: 500;\r\n    color: #333333;\r\n    margin-bottom: 8rpx;\r\n  }\r\n  \r\n  .history-time {\r\n    font-size: 24rpx;\r\n    color: #999999;\r\n  }\r\n}\r\n\r\n.history-status {\r\n  font-size: 26rpx;\r\n  \r\n  &.status-success {\r\n    color: #07c160;\r\n  }\r\n  \r\n  &.status-pending, &.status-processing {\r\n    color: #1677FF;\r\n  }\r\n  \r\n  &.status-failed {\r\n    color: #ff4d4f;\r\n  }\r\n}\r\n\r\n.no-record {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 60rpx 0;\r\n  \r\n  image {\r\n    width: 200rpx;\r\n    height: 200rpx;\r\n    margin-bottom: 20rpx;\r\n  }\r\n  \r\n  text {\r\n    font-size: 28rpx;\r\n    color: #999999;\r\n  }\r\n}\r\n\r\n.view-more {\r\n  text-align: center;\r\n  font-size: 28rpx;\r\n  color: #1677FF;\r\n  margin-top: 20rpx;\r\n}\r\n\r\n.withdraw-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 999;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  \r\n  .modal-mask {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background-color: rgba(0, 0, 0, 0.6);\r\n  }\r\n  \r\n  .modal-content {\r\n    width: 600rpx;\r\n    background-color: #ffffff;\r\n    border-radius: 16rpx;\r\n    padding: 40rpx;\r\n    position: relative;\r\n    z-index: 1000;\r\n  }\r\n  \r\n  .modal-title {\r\n    font-size: 32rpx;\r\n    font-weight: 500;\r\n    text-align: center;\r\n    margin-bottom: 30rpx;\r\n  }\r\n  \r\n  .modal-info {\r\n    margin-bottom: 30rpx;\r\n    \r\n    .info-item {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      margin-bottom: 15rpx;\r\n      font-size: 28rpx;\r\n      \r\n      .label {\r\n        color: #666666;\r\n      }\r\n      \r\n      .value {\r\n        color: #333333;\r\n        font-weight: 500;\r\n      }\r\n      \r\n      &.highlight {\r\n        .label, .value {\r\n          color: #1677FF;\r\n          font-weight: bold;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .modal-btns {\r\n    display: flex;\r\n    \r\n    button {\r\n      flex: 1;\r\n      height: 80rpx;\r\n      font-size: 30rpx;\r\n      border-radius: 40rpx;\r\n      margin: 0 10rpx;\r\n    }\r\n    \r\n    .cancel-btn {\r\n      background-color: #f5f5f5;\r\n      color: #666666;\r\n    }\r\n    \r\n    .confirm-btn {\r\n      background: linear-gradient(135deg, #1677FF, #0E5FD8);\r\n      color: #ffffff;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n  \r\n  .success-modal {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    padding: 50rpx 40rpx;\r\n  }\r\n  \r\n  .success-icon {\r\n    width: 120rpx;\r\n    height: 120rpx;\r\n    margin-bottom: 20rpx;\r\n  }\r\n  \r\n  .success-title {\r\n    font-size: 32rpx;\r\n    font-weight: 500;\r\n    margin-bottom: 10rpx;\r\n  }\r\n  \r\n  .success-desc {\r\n    font-size: 26rpx;\r\n    color: #666666;\r\n    text-align: center;\r\n    margin-bottom: 30rpx;\r\n  }\r\n  \r\n  .success-btn {\r\n    width: 100%;\r\n    height: 80rpx;\r\n    background: linear-gradient(135deg, #1677FF, #0E5FD8);\r\n    color: #ffffff;\r\n    font-size: 30rpx;\r\n    border-radius: 40rpx;\r\n    font-weight: 500;\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/partner/pages/partner-withdraw.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "fee", "uni"], "mappings": ";;;;;;AA+NA,UAAM,mBAAmBA,cAAAA,IAAI,OAAO;AACpC,UAAM,cAAcA,cAAAA,IAAI,IAAO;AAC/B,UAAM,mBAAmBA,cAAAA,IAAI,GAAO;AACpC,UAAM,eAAeA,cAAAA,IAAI,MAAM;AAC/B,UAAM,SAASA,cAAAA,IAAI,EAAE;AACrB,UAAM,UAAUA,cAAAA,IAAI,GAAG;AACvB,UAAM,iBAAiBA,cAAAA,IAAI,QAAQ;AACnC,UAAM,gBAAgBA,cAAAA,IAAI,EAAE;AAC5B,UAAM,aAAaA,cAAAA,IAAI,EAAE;AACzB,UAAM,YAAYA,cAAAA,IAAI,CAAC;AACvB,UAAM,WAAW;AAAA,MACf;AAAA,MAAU;AAAA,MAAU;AAAA,MAAU;AAAA,MAC9B;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAQ;AAAA,MACxB;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAQ;AAAA,IAC1B;AACA,UAAM,WAAWA,cAAAA,IAAI,EAAE;AACvB,UAAM,YAAYA,cAAAA,IAAI,EAAE;AACxB,UAAM,mBAAmBA,cAAAA,IAAI,KAAK;AAClC,UAAM,mBAAmBA,cAAAA,IAAI,KAAK;AAElC,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB,EAAE,QAAQ,KAAQ,MAAM,uBAAuB,QAAQ,UAAW;AAAA,MAClE,EAAE,QAAQ,KAAQ,MAAM,uBAAuB,QAAQ,UAAW;AAAA,MAClE,EAAE,QAAQ,KAAQ,MAAM,uBAAuB,QAAQ,UAAW;AAAA,IACpE,CAAC;AAED,UAAM,aAAa;AAAA,MACjB,WAAW;AAAA,MACX,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAEA,UAAM,aAAa;AAAA,MACjB,UAAU;AAAA,MACV,UAAU;AAAA,MACV,QAAQ;AAAA,IACV;AAIA,UAAM,eAAeC,cAAQ,SAAC,MAAM;AAClC,UAAI,CAAC,OAAO,SAAS,MAAM,OAAO,KAAK;AAAG,eAAO;AACjD,UAAIC,OAAM,OAAO,SAAS,QAAQ,QAAQ;AAC1C,cAAQ,OAAO,QAAQA,MAAK,QAAQ,CAAC;AAAA,IACvC,CAAC;AAGD,UAAM,MAAMD,cAAQ,SAAC,MAAM;AACzB,UAAI,CAAC,OAAO,SAAS,MAAM,OAAO,KAAK;AAAG,eAAO;AACjD,cAAQ,OAAO,SAAS,QAAQ,QAAQ,MAAM,QAAQ,CAAC;AAAA,IACzD,CAAC;AAGD,UAAM,cAAcA,cAAQ,SAAC,MAAM;AACjC,UAAI,CAAC,OAAO,SAAS,MAAM,OAAO,KAAK,KAAK,OAAO,SAAS;AAAG,eAAO;AACtE,UAAI,OAAO,QAAQ,iBAAiB;AAAO,eAAO;AAClD,UAAI,OAAO,QAAQ;AAAG,eAAO;AAE7B,UAAI,eAAe,UAAU,aAAa,CAAC,cAAc,SAAS,CAAC,WAAW;AAAQ,eAAO;AAC7F,UAAI,eAAe,UAAU,WAAW,CAAC,SAAS,SAAS,CAAC,UAAU;AAAQ,eAAO;AAErF,aAAO;AAAA,IACT,CAAC;AAID,UAAM,eAAe,MAAM;AACzB,aAAO,QAAQ,iBAAiB,MAAM,SAAQ;AAAA,IAChD;AAGA,UAAM,eAAe,CAAC,WAAW;AAC/B,qBAAe,QAAQ;AAAA,IACzB;AAGA,UAAM,aAAa,CAAC,MAAM;AACxB,gBAAU,QAAQ,EAAE,OAAO;AAAA,IAC7B;AAGA,UAAM,iBAAiB,MAAM;AAC3B,UAAI,CAAC,YAAY;AAAO;AACxB,uBAAiB,QAAQ;AAAA,IAC3B;AAGA,UAAM,iBAAiB,MAAM;AAC3B,uBAAiB,QAAQ;AAAA,IAC3B;AAGA,UAAM,kBAAkB,MAAM;AAE5BE,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACf,yBAAiB,QAAQ;AACzB,yBAAiB,QAAQ;AAGzB,yBAAiB,SAAS,OAAO,OAAO,KAAK;AAC7C,yBAAiB,SAAS,OAAO,OAAO,KAAK;AAG7C,oBAAY,MAAM,QAAQ;AAAA,UACxB,QAAQ,OAAO,OAAO,KAAK;AAAA,UAC3B,MAAM,WAAW,oBAAI,MAAM;AAAA,UAC3B,QAAQ;AAAA,QACd,CAAK;AAGD,eAAO,QAAQ;AACf,sBAAc,QAAQ;AACtB,mBAAW,QAAQ;AACnB,iBAAS,QAAQ;AACjB,kBAAU,QAAQ;AAAA,MACnB,GAAE,IAAI;AAAA,IACT;AAGA,UAAM,oBAAoB,MAAM;AAC9B,uBAAiB,QAAQ;AAAA,IAC3B;AAGA,UAAM,4BAA4B,MAAM;AACtCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,SAAS;AAC3B,YAAM,OAAO,KAAK;AAClB,YAAM,SAAS,KAAK,aAAa,GAAG,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC9D,YAAM,MAAM,KAAK,QAAS,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACrD,YAAM,OAAO,KAAK,SAAU,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACvD,YAAM,SAAS,KAAK,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC3D,YAAM,SAAS,KAAK,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAE3D,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM,IAAI,MAAM;AAAA,IAC5D;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrXA,GAAG,WAAW,eAAe;"}