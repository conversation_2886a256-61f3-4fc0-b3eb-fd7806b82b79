<template>
  <view class="store-management-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">店铺管理</text>
      <view class="navbar-right">
        <view class="help-icon">?</view>
      </view>
    </view>
    
    <!-- 头部店铺信息卡片 -->
    <view class="store-header-card">
      <view class="store-header-content">
        <view class="store-avatar-container">
          <image class="store-avatar" :src="storeInfo.avatar || '/static/images/default-store.png'" mode="aspectFill"></image>
          <view class="avatar-edit-btn" @tap="uploadStoreAvatar">
            <view class="edit-icon"></view>
        </view>
      </view>
      
        <view class="store-header-info">
          <view class="store-name-container">
            <text class="store-name">{{storeInfo.name}}</text>
            <view class="store-badges">
              <view class="badge verified" v-if="storeInfo.isVerified">
                <view class="badge-icon verified"></view>
                <text class="badge-text">已认证</text>
          </view>
              <view class="badge premium" v-if="storeInfo.isPremium">
                <view class="badge-icon premium"></view>
                <text class="badge-text">优选商家</text>
              </view>
            </view>
          </view>
          <text class="store-desc">{{storeInfo.description || '暂无店铺介绍'}}</text>
          <view class="store-metrics">
            <view class="metric-item">
              <text class="metric-value">{{storeInfo.rating}}</text>
              <text class="metric-label">评分</text>
            </view>
            <view class="metric-divider"></view>
            <view class="metric-item">
              <text class="metric-value">{{storeInfo.orderCount}}</text>
              <text class="metric-label">总订单</text>
            </view>
            <view class="metric-divider"></view>
            <view class="metric-item">
              <text class="metric-value">{{storeInfo.followers}}</text>
              <text class="metric-label">粉丝数</text>
            </view>
          </view>
        </view>
        
        <view class="store-preview-btn" @tap="previewStore">
          <view class="preview-icon"></view>
          <text class="preview-text">预览店铺</text>
          </view>
      </view>
      
      <view class="store-status-bar">
        <view class="status-info">
          <view class="status-dot" :class="{active: storeInfo.status === 'open'}"></view>
          <text class="status-text">{{storeInfo.status === 'open' ? '营业中' : '休息中'}}</text>
        </view>
        <view class="operation-hours">
          <text class="hours-label">营业时间:</text>
          <text class="hours-value">{{storeInfo.operationHours}}</text>
          <view class="edit-hours-btn" @tap="editOperationHours">
            <view class="edit-icon small"></view>
          </view>
        </view>
          </view>
        </view>
        
    <!-- 店铺管理导航 -->
    <scroll-view class="store-nav" scroll-x show-scrollbar="false">
      <view 
        class="nav-item" 
        v-for="(item, index) in navItems" 
        :key="index"
        :class="{ active: currentNav === index }"
        @tap="switchNav(index)">
        <text class="nav-text">{{item}}</text>
      </view>
    </scroll-view>
    
    <!-- 内容区域容器 -->
    <scroll-view class="content-scroll" scroll-y refresher-enabled @refresherrefresh="refreshData" :refresher-triggered="refreshing">
      
      <!-- 商品服务管理区块 -->
      <view class="content-section" v-show="currentNav === 0">
        <view class="section-header">
          <text class="section-title">商品与服务</text>
          <text class="header-subtitle">管理您店铺提供的商品与服务</text>
        </view>
        
        <view class="data-overview-cards">
        <view class="data-card">
            <text class="data-value">{{productStats.total}}</text>
            <text class="data-label">商品总数</text>
          </view>
          <view class="data-card">
            <text class="data-value">{{productStats.onSale}}</text>
            <text class="data-label">在售商品</text>
          </view>
          <view class="data-card">
            <text class="data-value">{{productStats.outOfStock}}</text>
            <text class="data-label">缺货商品</text>
        </view>
        <view class="data-card">
            <text class="data-value">{{productStats.services}}</text>
            <text class="data-label">服务项目</text>
          </view>
          </view>
        
        <view class="action-buttons">
          <view class="action-button" @tap="navigateTo('/pages/store/product/list')">
            <view class="button-icon product"></view>
            <text class="button-text">商品管理</text>
        </view>
          <view class="action-button" @tap="navigateTo('/pages/store/category/list')">
            <view class="button-icon category"></view>
            <text class="button-text">分类管理</text>
          </view>
          <view class="action-button" @tap="navigateTo('/pages/store/service/list')">
            <view class="button-icon service"></view>
            <text class="button-text">服务项目</text>
          </view>
          <view class="action-button" @tap="navigateTo('/pages/store/stock/manage')">
            <view class="button-icon stock"></view>
            <text class="button-text">库存管理</text>
      </view>
    </view>
    
        <view class="hot-products-section">
          <view class="sub-section-header">
            <text class="sub-section-title">热门商品</text>
            <text class="view-more" @tap="navigateTo('/pages/store/product/hot')">查看全部</text>
          </view>
          
          <scroll-view class="product-scroll" scroll-x show-scrollbar="false">
            <view class="product-card" v-for="(product, index) in hotProducts" :key="index">
              <image class="product-image" :src="product.image" mode="aspectFill"></image>
              <view class="product-info">
                <text class="product-name">{{product.name}}</text>
                <text class="product-price">¥{{product.price}}</text>
                <view class="product-stats">
                  <text class="stats-item">销量: {{product.sales}}</text>
                  <text class="stats-item">好评率: {{product.rating}}%</text>
                </view>
              </view>
        </view>
      </scroll-view>
    </view>
    
        <view class="quick-actions">
          <view class="action-card" @tap="navigateTo('/pages/store/product/add')">
            <view class="action-icon add"></view>
            <text class="action-title">添加商品</text>
            <text class="action-desc">快速添加新商品</text>
          </view>
          <view class="action-card" @tap="navigateTo('/pages/store/product/batch')">
            <view class="action-icon batch"></view>
            <text class="action-title">批量管理</text>
            <text class="action-desc">批量编辑商品信息</text>
          </view>
          <view class="action-card" @tap="navigateTo('/pages/store/product/import')">
            <view class="action-icon import"></view>
            <text class="action-title">导入导出</text>
            <text class="action-desc">表格导入导出商品</text>
          </view>
        </view>
      </view>
      
      <!-- 店铺形象区块 -->
      <view class="content-section" v-show="currentNav === 1">
      <view class="section-header">
          <text class="section-title">店铺形象</text>
          <text class="header-subtitle">提升店铺形象，吸引更多顾客</text>
        </view>
        
        <!-- 店铺封面管理 -->
        <view class="cover-section">
          <text class="sub-section-title">店铺封面</text>
          <view class="cover-preview">
            <image class="cover-image" :src="storeInfo.coverImage || '/static/images/default-cover.jpg'" mode="aspectFill"></image>
            <view class="cover-edit-btn" @tap="uploadCoverImage">
              <view class="edit-cover-icon"></view>
              <text class="edit-text">更换封面</text>
            </view>
        </view>
      </view>
      
        <!-- 店铺风格设置 -->
        <view class="style-section">
          <text class="sub-section-title">店铺风格</text>
          <view class="style-cards">
            <view 
              class="style-card" 
              v-for="(style, index) in storeStyles" 
              :key="index"
              :class="{ active: storeInfo.styleId === style.id }"
              @tap="selectStoreStyle(style.id)">
              <image class="style-preview" :src="style.preview" mode="aspectFill"></image>
              <view class="style-info">
                <text class="style-name">{{style.name}}</text>
                <view class="style-check" v-if="storeInfo.styleId === style.id">
                  <view class="check-icon"></view>
                </view>
              </view>
            </view>
          </view>
      </view>
      
        <!-- 店铺相册 -->
        <view class="gallery-section">
          <view class="sub-section-header">
            <text class="sub-section-title">店铺相册</text>
            <text class="view-more" @tap="navigateTo('/pages/store/gallery')">管理相册</text>
        </view>
          
          <text class="gallery-hint">向顾客展示您的店铺环境、商品展示、团队风采等</text>
          
          <view class="gallery-grid">
            <view class="gallery-item" v-for="(image, index) in storeGallery" :key="index">
              <image class="gallery-image" :src="image.url" mode="aspectFill"></image>
              <text class="gallery-tag" v-if="image.tag">{{image.tag}}</text>
            </view>
            <view class="gallery-add" @tap="addGalleryImage">
              <view class="add-icon"></view>
              <text class="add-text">添加图片</text>
        </view>
      </view>
    </view>
    
        <!-- 宣传视频 -->
        <view class="video-section">
          <text class="sub-section-title">宣传视频</text>
          <view class="video-container" v-if="storeInfo.promotionVideo">
            <video class="store-video" :src="storeInfo.promotionVideo" object-fit="cover" poster="/static/images/video-poster.jpg"></video>
          </view>
          <view class="video-upload" v-else @tap="uploadVideo">
            <view class="upload-icon"></view>
            <text class="upload-text">上传宣传视频</text>
            <text class="upload-hint">支持MP4格式，大小不超过50MB，时长建议30秒-2分钟</text>
          </view>
        </view>
        
        <!-- 商家故事 -->
        <view class="story-section">
          <text class="sub-section-title">商家故事</text>
          <view class="story-editor">
            <textarea class="story-textarea" v-model="storeInfo.story" placeholder="讲述您的品牌故事，与顾客建立情感连接..." maxlength="500"></textarea>
            <text class="text-counter">{{(storeInfo.story || '').length}}/500</text>
          </view>
          <view class="save-story-btn" @tap="saveStoryContent">
            <text class="save-text">保存内容</text>
          </view>
        </view>
      </view>
      
      <!-- 位置与配送区块 -->
      <view class="content-section" v-show="currentNav === 2">
      <view class="section-header">
          <text class="section-title">位置与配送</text>
          <text class="header-subtitle">管理店铺位置和配送范围</text>
      </view>
      
        <!-- 店铺地址 -->
        <view class="location-card">
          <text class="location-title">店铺地址</text>
          
          <view class="address-container">
            <view class="address-info">
              <view class="address-detail">
                <view class="location-icon"></view>
                <text class="address-text">{{storeInfo.address || '点击设置店铺地址'}}</text>
          </view>
              <view class="address-actions">
                <view class="action-btn edit" @tap="editAddress">
                  <view class="btn-icon edit"></view>
                  <text class="btn-text">编辑</text>
            </view>
                <view class="action-btn navigate" @tap="navigateToStore">
                  <view class="btn-icon navigate"></view>
                  <text class="btn-text">导航</text>
            </view>
            </view>
          </view>
        </view>
          
          <!-- 地图预览 -->
          <view class="map-container">
            <image class="map-preview" src="/static/images/store-map.jpg" mode="aspectFill"></image>
            <view class="map-marker">
              <view class="marker-icon"></view>
            </view>
            <view class="map-tools">
              <view class="map-tool-btn" @tap="openMapSelection">
                <view class="tool-icon map"></view>
                <text class="tool-text">选择位置</text>
              </view>
              <view class="map-tool-btn" @tap="previewMapInApp">
                <view class="tool-icon preview"></view>
                <text class="tool-text">查看大图</text>
              </view>
            </view>
      </view>
    </view>
    
        <!-- 配送范围 -->
        <view class="delivery-section">
          <text class="section-subtitle">配送范围</text>
          
          <view class="delivery-toggle">
            <text class="toggle-label">提供配送服务</text>
            <switch :checked="storeInfo.providesDelivery" color="#1677FF" @change="toggleDelivery" />
      </view>
      
          <view class="delivery-settings" v-if="storeInfo.providesDelivery">
            <view class="radius-setting">
              <text class="setting-label">配送半径</text>
              <view class="radius-slider-container">
                <slider 
                  class="radius-slider" 
                  :min="1" 
                  :max="20" 
                  :value="storeInfo.deliveryRadius" 
                  show-value 
                  activeColor="#1677FF"
                  backgroundColor="#E1E8F0"
                  block-color="#1677FF"
                  @change="updateDeliveryRadius" />
                <text class="radius-value">{{storeInfo.deliveryRadius}}公里</text>
          </view>
        </view>
            
            <view class="delivery-fee-settings">
              <text class="setting-label">配送费设置</text>
              
              <view class="fee-type-toggle">
                <view 
                  class="toggle-option" 
                  :class="{active: storeInfo.deliveryFeeType === 'fixed'}" 
                  @tap="storeInfo.deliveryFeeType = 'fixed'">
                  <text class="option-text">固定费用</text>
                </view>
                <view 
                  class="toggle-option" 
                  :class="{active: storeInfo.deliveryFeeType === 'distance'}" 
                  @tap="storeInfo.deliveryFeeType = 'distance'">
                  <text class="option-text">按距离收费</text>
      </view>
    </view>
    
              <!-- 固定费用设置 -->
              <view class="fee-input-container" v-if="storeInfo.deliveryFeeType === 'fixed'">
                <text class="fee-input-label">配送费</text>
                <view class="fee-input-wrap">
                  <input type="digit" class="fee-input" v-model="storeInfo.fixedDeliveryFee" placeholder="请输入配送费" />
                  <text class="fee-unit">元</text>
                </view>
      </view>
      
              <!-- 按距离收费设置 -->
              <view class="distance-fee-table" v-if="storeInfo.deliveryFeeType === 'distance'">
                <view class="table-header">
                  <text class="header-cell">距离范围</text>
                  <text class="header-cell">配送费(元)</text>
                  <text class="header-cell">操作</text>
            </view>
                
                <view class="table-row" v-for="(fee, index) in distanceFeeTiers" :key="index">
                  <view class="row-cell distance">
                    <text v-if="index === 0">0 - {{fee.distance}}公里</text>
                    <text v-else>{{distanceFeeTiers[index-1].distance}} - {{fee.distance}}公里</text>
            </view>
                  <view class="row-cell fee">
                    <input type="digit" class="tier-fee-input" v-model="fee.price" />
          </view>
                  <view class="row-cell actions">
                    <view class="row-action delete" @tap="removeFeeTier(index)" v-if="distanceFeeTiers.length > 1">
                      <view class="delete-icon"></view>
            </view>
          </view>
        </view>
                
                <view class="add-tier-btn" @tap="addFeeTier">
                  <view class="add-icon"></view>
                  <text class="add-text">添加梯度</text>
      </view>
    </view>
    
              <!-- 起送价设置 -->
              <view class="minimum-order">
                <text class="setting-label">起送价</text>
                <view class="minimum-input-wrap">
                  <input type="digit" class="minimum-input" v-model="storeInfo.minimumOrder" placeholder="请输入起送价" />
                  <text class="minimum-unit">元</text>
                </view>
                <text class="minimum-hint">设置为0表示无起送价限制</text>
              </view>
            </view>
          </view>
          
          <view class="empty-delivery" v-else>
            <image class="empty-icon" src="/static/images/delivery-inactive.png"></image>
            <text class="empty-text">您当前未开启配送服务</text>
          </view>
        </view>
        
        <!-- 自提点设置 -->
        <view class="pickup-section">
          <view class="section-header-with-toggle">
            <text class="section-subtitle">自提点设置</text>
            <view class="toggle-container">
              <text class="toggle-label">支持自提</text>
              <switch :checked="storeInfo.supportsPickup" color="#1677FF" @change="togglePickup" />
            </view>
          </view>
          
          <view class="pickup-settings" v-if="storeInfo.supportsPickup">
            <view class="pickup-instructions">
              <text class="setting-label">自提说明</text>
              <textarea class="pickup-textarea" v-model="storeInfo.pickupInstructions" placeholder="请输入自提说明，如：请到前台出示订单号领取..." maxlength="200"></textarea>
              <text class="text-counter">{{(storeInfo.pickupInstructions || '').length}}/200</text>
            </view>
          </view>
          
          <view class="empty-pickup" v-else>
            <image class="empty-icon" src="/static/images/pickup-inactive.png"></image>
            <text class="empty-text">您当前未开启自提服务</text>
          </view>
        </view>
        
        <!-- 保存按钮 -->
        <view class="save-btn" @tap="saveLocationAndDelivery">
          <text class="save-text">保存设置</text>
        </view>
      </view>
      
      <!-- 认证与资质区块 -->
      <view class="content-section" v-show="currentNav === 3">
      <view class="section-header">
          <text class="section-title">认证与资质</text>
          <text class="header-subtitle">提高店铺可信度，增加顾客信任感</text>
      </view>
      
        <!-- 商家认证状态 -->
        <view class="certification-card">
          <view class="certification-header">
            <text class="card-title">商家认证</text>
            <view class="cert-status" :class="storeInfo.certificationStatus">
              <text class="status-text">
                {{certStatusText[storeInfo.certificationStatus] || '未认证'}}
              </text>
          </view>
            </view>
          
          <view class="certification-content">
            <!-- 未认证状态 -->
            <view class="uncertified" v-if="storeInfo.certificationStatus === 'uncertified'">
              <view class="uncert-icon"></view>
              <text class="uncert-title">您尚未完成商家认证</text>
              <text class="uncert-desc">完成认证后，将获得"已认证"标识，提高店铺可信度</text>
              <view class="start-cert-btn" @tap="startCertification">
                <text class="btn-text">开始认证</text>
          </view>
          </view>
            
            <!-- 审核中状态 -->
            <view class="in-progress" v-else-if="storeInfo.certificationStatus === 'pending'">
              <view class="progress-icon"></view>
              <text class="progress-title">认证审核中</text>
              <text class="progress-desc">预计{{storeInfo.certificationETA || '3个工作日'}}内完成审核，请耐心等待</text>
              <view class="cert-progress">
                <view class="progress-bar">
                  <view class="progress-fill" style="width: 50%;"></view>
                </view>
                <text class="progress-text">审核进行中</text>
        </view>
      </view>
      
            <!-- 已认证状态 -->
            <view class="certified" v-else-if="storeInfo.certificationStatus === 'certified'">
              <view class="cert-info">
                <view class="cert-icon"></view>
                <view class="cert-details">
                  <text class="cert-name">{{storeInfo.certType === 'individual' ? '个体工商户认证' : '企业认证'}}</text>
                  <text class="cert-time">认证时间：{{storeInfo.certTime || '2023-04-01'}}</text>
                  <text class="cert-number">{{storeInfo.certType === 'individual' ? '营业执照' : '统一社会信用代码'}}：{{storeInfo.certNumber}}</text>
                </view>
              </view>
              <view class="renewal-info" v-if="storeInfo.certExpiringSoon">
                <view class="alert-icon"></view>
                <text class="renewal-text">认证即将到期，请及时更新资质</text>
                <view class="renew-btn" @tap="renewCertification">更新</view>
      </view>
    </view>
    
            <!-- 认证失败状态 -->
            <view class="cert-failed" v-else-if="storeInfo.certificationStatus === 'failed'">
              <view class="failed-icon"></view>
              <text class="failed-title">认证未通过</text>
              <text class="failed-reason">原因：{{storeInfo.failReason || '资料不完整，请补充完整的营业执照信息'}}</text>
              <view class="retry-cert-btn" @tap="retryCertification">
                <text class="btn-text">重新提交</text>
              </view>
            </view>
          </view>
      </view>
      
        <!-- 行业资质 -->
        <view class="qualification-section">
          <text class="section-subtitle">行业资质</text>
          
          <view class="quals-container">
            <!-- 未添加资质时的提示 -->
            <view class="no-quals" v-if="qualifications.length === 0">
              <image class="no-quals-img" src="/static/images/no-qualification.png"></image>
              <text class="no-quals-text">您尚未添加行业资质</text>
              <text class="no-quals-desc">添加相关行业资质，提高顾客信任度</text>
          </view>
            
            <!-- 已添加的资质列表 -->
            <view class="qual-list" v-else>
              <view class="qual-item" v-for="(qual, index) in qualifications" :key="index">
                <view class="qual-icon" :class="qual.type"></view>
                <view class="qual-info">
                  <text class="qual-name">{{qual.name}}</text>
                  <text class="qual-desc">{{qual.validUntil ? '有效期至：' + qual.validUntil : '长期有效'}}</text>
          </view>
                <view class="qual-status" :class="qual.status">
                  <text class="status-text">{{qualStatusText[qual.status] || '待审核'}}</text>
        </view>
                <view class="qual-actions">
                  <view class="action-btn view" @tap="viewQualification(qual)">
                    <view class="btn-icon view"></view>
                  </view>
                  <view class="action-btn edit" @tap="editQualification(qual)">
                    <view class="btn-icon edit"></view>
                  </view>
                  <view class="action-btn delete" @tap="deleteQualification(qual)">
                    <view class="btn-icon delete"></view>
                  </view>
                </view>
      </view>
    </view>
    
            <view class="add-qual-btn" @tap="addQualification">
              <view class="add-icon"></view>
              <text class="add-text">添加资质</text>
    </view>
          </view>
        </view>
        
        <!-- 到期提醒设置 -->
        <view class="expiration-reminder">
          <text class="section-subtitle">到期提醒设置</text>
          
          <view class="reminder-card">
            <view class="reminder-item">
              <text class="reminder-label">提前提醒天数</text>
              <view class="reminder-input-wrap">
                <input type="number" class="reminder-input" v-model="storeInfo.expirationReminder" />
                <text class="reminder-unit">天</text>
              </view>
            </view>
            
            <view class="reminder-item">
              <text class="reminder-label">提醒方式</text>
              <view class="reminder-checkboxes">
                <view class="checkbox-item" @tap="toggleReminderMethod('app')">
                  <view class="checkbox" :class="{checked: reminderMethods.includes('app')}">
                    <view class="check-mark" v-if="reminderMethods.includes('app')"></view>
                  </view>
                  <text class="checkbox-label">应用通知</text>
                </view>
                
                <view class="checkbox-item" @tap="toggleReminderMethod('sms')">
                  <view class="checkbox" :class="{checked: reminderMethods.includes('sms')}">
                    <view class="check-mark" v-if="reminderMethods.includes('sms')"></view>
                  </view>
                  <text class="checkbox-label">短信通知</text>
                </view>
                
                <view class="checkbox-item" @tap="toggleReminderMethod('email')">
                  <view class="checkbox" :class="{checked: reminderMethods.includes('email')}">
                    <view class="check-mark" v-if="reminderMethods.includes('email')"></view>
                  </view>
                  <text class="checkbox-label">邮件通知</text>
                </view>
              </view>
            </view>
            
            <view class="save-reminder-btn" @tap="saveReminderSettings">
              <text class="save-text">保存设置</text>
            </view>
          </view>
        </view>
      </view>
      
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentNav: 0,
      refreshing: false,
      navItems: ['商品服务', '店铺形象', '位置配送', '认证资质', '基本信息'],
      
      storeInfo: {
        id: '12345678',
        name: '磁州生活家居旗舰店',
        avatar: '/static/images/store-logo.png',
        coverImage: '/static/images/store-cover.jpg',
        description: '为您提供高品质家居产品，打造舒适生活空间',
        rating: 4.8,
        orderCount: 1286,
        followers: 568,
        status: 'open',
        operationHours: '09:00 - 22:00',
        isVerified: true,
        isPremium: true,
        styleId: 2,
        story: '我们是一家专注于家居产品的本地商户，创立于2018年。始终坚持"品质优先，用户至上"的原则，精选每一件商品，只为给客户带来舒适的居家体验。',
        address: '河北省邯郸市磁县磁州生活广场A座1202室',
        providesDelivery: true,
        deliveryRadius: 5,
        deliveryFeeType: 'fixed',
        fixedDeliveryFee: '5',
        minimumOrder: '20',
        supportsPickup: true,
        pickupInstructions: '请到店铺前台出示订单号领取商品，工作时间9:00-22:00。',
        certificationStatus: 'certified', // uncertified, pending, certified, failed
        certType: 'enterprise',
        certTime: '2023-01-15',
        certNumber: '91130427MA0CXY2R5B',
        certExpiringSoon: false,
        expirationReminder: 30,
        promotionVideo: ''
      },
      
      productStats: {
        total: 128,
        onSale: 96,
        outOfStock: 8,
        services: 12
      },
      
      // 热门商品数据
      hotProducts: [
        {
          id: 1,
          name: '北欧风格实木沙发',
          price: '1299.00',
          image: '/static/images/product-1.jpg',
          sales: 56,
          rating: 98
        },
        {
          id: 2,
          name: '天然乳胶床垫',
          price: '2399.00',
          image: '/static/images/product-2.jpg',
          sales: 42,
          rating: 95
        },
        {
          id: 3,
          name: '日式简约茶几',
          price: '499.00',
          image: '/static/images/product-3.jpg',
          sales: 38,
          rating: 92
        },
        {
          id: 4,
          name: '环保棉麻窗帘',
          price: '298.00',
          image: '/static/images/product-4.jpg',
          sales: 127,
          rating: 97
        }
      ],
      
      // 店铺风格数据
      storeStyles: [
        {
          id: 1,
          name: '简约现代',
          preview: '/static/images/style-modern.jpg'
        },
        {
          id: 2,
          name: '轻奢风格',
          preview: '/static/images/style-luxury.jpg'
        },
        {
          id: 3,
          name: '田园清新',
          preview: '/static/images/style-country.jpg'
        },
        {
          id: 4,
          name: '工业风',
          preview: '/static/images/style-industrial.jpg'
        }
      ],
      
      // 店铺相册数据
      storeGallery: [
        {
          id: 1,
          url: '/static/images/gallery-1.jpg',
          tag: '店铺环境'
        },
        {
          id: 2,
          url: '/static/images/gallery-2.jpg',
          tag: '产品展示'
        },
        {
          id: 3,
          url: '/static/images/gallery-3.jpg',
          tag: '团队风采'
        },
        {
          id: 4,
          url: '/static/images/gallery-4.jpg',
          tag: '活动现场'
        },
        {
          id: 5,
          url: '/static/images/gallery-5.jpg',
          tag: '顾客好评'
        }
      ],
      
      // 配送费梯度
      distanceFeeTiers: [
        { distance: 3, price: '3' },
        { distance: 5, price: '5' },
        { distance: 10, price: '10' }
      ],
      
      // 行业资质数据
      qualifications: [
        {
          id: 1,
          name: '食品经营许可证',
          type: 'food',
          validUntil: '2025-12-31',
          status: 'approved',
          image: '/static/images/qual-food.jpg'
        },
        {
          id: 2,
          name: '卫生许可证',
          type: 'health',
          validUntil: '2024-10-15',
          status: 'approved',
          image: '/static/images/qual-health.jpg'
        }
      ],
      
      // 认证状态文本
      certStatusText: {
        'uncertified': '未认证',
        'pending': '审核中',
        'certified': '已认证',
        'failed': '未通过'
      },
      
      // 资质状态文本
      qualStatusText: {
        'pending': '审核中',
        'approved': '已通过',
        'rejected': '未通过',
        'expired': '已过期'
      },
      
      // 提醒方式
      reminderMethods: ['app', 'sms']
    }
  },
  
  methods: {
    // 通用导航方法
    goBack() {
      uni.navigateBack();
    },
    
    navigateTo(url) {
      uni.navigateTo({
        url: url
      });
    },
    
    // 切换导航选项卡
    switchNav(index) {
      this.currentNav = index;
    },
    
    // 下拉刷新
    refreshData(e) {
      this.refreshing = true;
      setTimeout(() => {
        // 模拟数据刷新
        this.refreshing = false;
      }, 1500);
    },
    
    // 商品服务相关方法
    previewStore() {
      uni.showToast({
        title: '预览功能开发中',
        icon: 'none'
      });
    },
    
    uploadStoreAvatar() {
      uni.chooseImage({
        count: 1,
        success: (res) => {
          // 模拟上传
          setTimeout(() => {
            this.storeInfo.avatar = res.tempFilePaths[0];
            uni.showToast({
              title: '上传成功',
              icon: 'success'
            });
          }, 1000);
        }
      });
    },
    
    editOperationHours() {
      uni.showToast({
        title: '编辑营业时间功能开发中',
        icon: 'none'
      });
    },
    
    // 店铺形象相关方法
    uploadCoverImage() {
      uni.chooseImage({
        count: 1,
        success: (res) => {
          // 模拟上传
          setTimeout(() => {
            this.storeInfo.coverImage = res.tempFilePaths[0];
            uni.showToast({
              title: '上传成功',
              icon: 'success'
            });
          }, 1000);
        }
      });
    },
    
    selectStoreStyle(styleId) {
      this.storeInfo.styleId = styleId;
    },
    
    addGalleryImage() {
      uni.chooseImage({
        count: 1,
        success: (res) => {
          // 模拟上传
          setTimeout(() => {
            this.storeGallery.push({
              id: this.storeGallery.length + 1,
              url: res.tempFilePaths[0],
              tag: '新增图片'
            });
            uni.showToast({
              title: '上传成功',
              icon: 'success'
            });
          }, 1000);
        }
      });
    },
    
    uploadVideo() {
      uni.chooseVideo({
        sourceType: ['album', 'camera'],
        maxDuration: 120,
        camera: 'back',
        success: (res) => {
          // 模拟上传
          setTimeout(() => {
            this.storeInfo.promotionVideo = res.tempFilePath;
            uni.showToast({
              title: '上传成功',
              icon: 'success'
            });
          }, 1500);
        }
      });
    },
    
    saveStoryContent() {
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      });
    },
    
    // 位置配送相关方法
    editAddress() {
      uni.showToast({
        title: '编辑地址功能开发中',
        icon: 'none'
      });
    },
    
    navigateToStore() {
      uni.showToast({
        title: '导航功能开发中',
        icon: 'none'
      });
    },
    
    openMapSelection() {
      uni.showToast({
        title: '地图选点功能开发中',
        icon: 'none'
      });
    },
    
    previewMapInApp() {
      uni.showToast({
        title: '地图预览功能开发中',
        icon: 'none'
      });
    },
    
    toggleDelivery(e) {
      this.storeInfo.providesDelivery = e.detail.value;
    },
    
    updateDeliveryRadius(e) {
      this.storeInfo.deliveryRadius = e.detail.value;
    },
    
    addFeeTier() {
      if (this.distanceFeeTiers.length >= 5) {
        uni.showToast({
          title: '最多添加5个梯度',
          icon: 'none'
        });
        return;
      }
      
      const lastTier = this.distanceFeeTiers[this.distanceFeeTiers.length - 1];
      this.distanceFeeTiers.push({
        distance: lastTier.distance + 5,
        price: (parseFloat(lastTier.price) + 5).toString()
      });
    },
    
    removeFeeTier(index) {
      this.distanceFeeTiers.splice(index, 1);
    },
    
    togglePickup(e) {
      this.storeInfo.supportsPickup = e.detail.value;
    },
    
    saveLocationAndDelivery() {
      uni.showLoading({
        title: '保存中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        });
      }, 1500);
    },
    
    // 认证资质相关方法
    startCertification() {
      uni.navigateTo({
        url: '/pages/store/certification/apply'
      });
    },
    
    retryCertification() {
      uni.navigateTo({
        url: '/pages/store/certification/retry'
      });
    },
    
    renewCertification() {
      uni.navigateTo({
        url: '/pages/store/certification/renew'
      });
    },
    
    viewQualification(qual) {
      uni.previewImage({
        urls: [qual.image],
        current: qual.image
      });
    },
    
    editQualification(qual) {
      uni.navigateTo({
        url: `/pages/store/qualification/edit?id=${qual.id}`
      });
    },
    
    deleteQualification(qual) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除"${qual.name}"资质吗？`,
        success: (res) => {
          if (res.confirm) {
            this.qualifications = this.qualifications.filter(q => q.id !== qual.id);
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });
          }
        }
      });
    },
    
    addQualification() {
          uni.navigateTo({
        url: '/pages/store/qualification/add'
      });
    },
    
    toggleReminderMethod(method) {
      const index = this.reminderMethods.indexOf(method);
      if (index > -1) {
        this.reminderMethods.splice(index, 1);
      } else {
        this.reminderMethods.push(method);
      }
    },
    
    saveReminderSettings() {
      uni.showToast({
        title: '设置已保存',
        icon: 'success'
      });
    }
  }
}
</script>

<style lang="scss">
/* 页面基础样式 */
.store-management-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  display: flex;
  flex-direction: column;
}

/* 导航栏样式 */
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}

/* 店铺头部卡片样式 */
.store-header-card {
  background: #FFFFFF;
  border-radius: 0 0 20px 20px;
  padding: 20px;
  margin-bottom: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.store-header-content {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
}

.store-avatar-container {
  position: relative;
  margin-right: 15px;
}

.store-avatar {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  background: #f0f0f0;
  border: 2px solid #fff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.avatar-edit-btn {
  position: absolute;
  right: -6px;
  bottom: -6px;
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: #1677FF;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.edit-icon {
  width: 14px;
  height: 14px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.996.996 0 0 0-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.edit-icon.small {
  width: 12px;
  height: 12px;
}

.store-header-info {
  flex: 1;
  min-width: 0;
}

.store-name-container {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
}

.store-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-right: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.store-badges {
  display: flex;
  align-items: center;
}

.badge {
  display: flex;
  align-items: center;
  background: rgba(22, 119, 255, 0.1);
  border-radius: 4px;
  padding: 2px 6px;
  margin-left: 6px;
}

.badge.verified {
  background: rgba(22, 119, 255, 0.1);
}

.badge.premium {
  background: rgba(255, 164, 28, 0.1);
}

.badge-icon {
  width: 12px;
  height: 12px;
  margin-right: 3px;
}

.badge-icon.verified {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231677FF"><path d="M12 2L4 5v6.09c0 5.05 3.41 9.76 8 10.91 4.59-1.15 8-5.86 8-10.91V5l-8-3zm-1.06 13.54L7.4 12l1.41-1.41 2.12 2.12 4.24-4.24 1.41 1.41-5.64 5.66z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.badge-icon.premium {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FFA41C"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.badge-text {
  font-size: 10px;
  font-weight: 500;
  color: #1677FF;
}

.badge.premium .badge-text {
  color: #FFA41C;
}

.store-desc {
  font-size: 12px;
  color: #666;
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.store-metrics {
  display: flex;
  align-items: center;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.metric-label {
  font-size: 12px;
  color: #999;
}

.metric-divider {
  width: 1px;
  height: 20px;
  background: #E8E8E8;
  margin: 0 15px;
}

.store-preview-btn {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  background: rgba(22, 119, 255, 0.1);
  border-radius: 16px;
  margin-left: 10px;
}

.preview-icon {
  width: 14px;
  height: 14px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231677FF"><path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
  margin-right: 4px;
}

.preview-text {
  font-size: 12px;
  color: #1677FF;
}

.store-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0 0;
  border-top: 1px solid #f0f0f0;
}

.status-info {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background: #ccc;
  margin-right: 5px;
}

.status-dot.active {
  background: #52C41A;
}

.status-text {
  font-size: 14px;
  color: #333;
}

.operation-hours {
  display: flex;
  align-items: center;
}

.hours-label {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.hours-value {
  font-size: 14px;
  color: #333;
}

.edit-hours-btn {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 5px;
}

/* 店铺管理导航样式 */
.store-nav {
  background: #fff;
  padding: 10px 0;
  white-space: nowrap;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.nav-item {
  display: inline-block;
  padding: 8px 20px;
  margin: 0 5px;
  font-size: 14px;
  color: #666;
  border-radius: 16px;
  position: relative;
}

.nav-item.active {
  color: #1677FF;
  background: rgba(22, 119, 255, 0.1);
  font-weight: 500;
}

.nav-item:first-child {
  margin-left: 15px;
}

.nav-item:last-child {
  margin-right: 15px;
}

/* 内容区域容器样式 */
.content-scroll {
  flex: 1;
  height: calc(100vh - 220px); /* 减去导航栏、店铺卡片和导航选项卡的高度 */
}

.content-section {
  padding: 15px;
}

.section-header {
  margin-bottom: 15px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.header-subtitle {
  font-size: 12px;
  color: #666;
}

/* 通用数据卡片样式 */
.data-overview-cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 20px;
}

.data-card {
  width: 48%;
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.data-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.data-label {
  font-size: 12px;
  color: #666;
}

/* 商品服务区块样式 */
.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.action-button {
  width: 23%;
  background: #fff;
  border-radius: 12px;
  padding: 15px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.button-icon {
  width: 28px;
  height: 28px;
  margin-bottom: 8px;
}

.button-icon.product {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231677FF"><path d="M19 6H5c-1.1 0-2 .9-2 2v9c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm0 11H5V8h14v9zm-7-5c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.button-icon.category {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231677FF"><path d="M4 11h5V5H4v6zm0 7h5v-6H4v6zm6 0h5v-6h-5v6zm6 0h5v-6h-5v6zm-6-7h5V5h-5v6zm6-6v6h5V5h-5z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.button-icon.service {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231677FF"><path d="M19 14V6c0-1.1-.9-2-2-2H3c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zm-2 0H3V6h14v8zm-7-1l-5-3v6z"/><path d="M23 15v-1c0-1.1-.9-2-2-2s-2 .9-2 2v1c-.55 0-1 .45-1 1v3c0 .55.45 1 1 1h4c.55 0 1-.45 1-1v-3c0-.55-.45-1-1-1zm-3 0v-1c0-.55.45-1 1-1s1 .45 1 1v1h-2z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.button-icon.stock {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231677FF"><path d="M2 20h20v-4H2v4zm2-3h2v2H4v-2zM2 4v4h20V4H2zm4 3H4V5h2v2zm-4 7h20v-4H2v4zm2-3h2v2H4v-2z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.button-text {
  font-size: 12px;
  color: #333;
}

.hot-products-section {
  margin-bottom: 20px;
}

.sub-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.sub-section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.view-more {
  font-size: 12px;
  color: #1677FF;
}

.product-scroll {
  white-space: nowrap;
  padding-bottom: 10px;
}

.product-card {
  display: inline-block;
  width: 140px;
  background: #fff;
  border-radius: 12px;
  margin-right: 10px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.product-image {
  width: 140px;
  height: 140px;
  background: #f5f5f5;
}

.product-info {
  padding: 10px;
}

.product-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 6px;
  white-space: normal;
  height: 40px;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-price {
  font-size: 16px;
  font-weight: 600;
  color: #FF5000;
  margin-bottom: 8px;
}

.product-stats {
  display: flex;
  justify-content: space-between;
  font-size: 10px;
  color: #999;
}

.stats-item {
  white-space: nowrap;
}

.quick-actions {
  display: flex;
  justify-content: space-between;
}

.action-card {
  width: 32%;
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.action-icon {
  width: 32px;
  height: 32px;
  margin-bottom: 10px;
}

.action-icon.add {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231677FF"><path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.action-icon.batch {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231677FF"><path d="M20 2H8c-1.1 0-1.99.9-1.99 2L6 20c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4c0-.55.45-1 1-1h1l3.6 3.6L7 10.1l4.9 4.9 3.6-3.6L20 16l0 4L6 4z"/><path d="M15.1 7.9l2 2 3-4-4 3z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.action-icon.import {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231677FF"><path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.action-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.action-desc {
  font-size: 12px;
  color: #999;
}

/* 店铺形象区块样式 */
.cover-section {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.sub-section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15px;
}

.cover-preview {
  position: relative;
  width: 100%;
  height: 150px;
  border-radius: 8px;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-edit-btn {
  position: absolute;
  right: 15px;
  bottom: 15px;
  height: 32px;
  border-radius: 16px;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  padding: 0 12px;
}

.edit-cover-icon {
  width: 16px;
  height: 16px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19 7v2.99s-1.99.01-2 0V7h-3s.01-1.99 0-2h3V2h2v3h3v2h-3zm-3 4V8h-3V5H5c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2v-8h-3zM5 19l3-4 2 3 3-4 4 5H5z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
  margin-right: 6px;
}

.edit-text {
  font-size: 12px;
  color: #fff;
}

.style-section {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.style-cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.style-card {
  width: 48%;
  margin-bottom: 15px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  position: relative;
}

.style-card.active {
  box-shadow: 0 0 0 2px #1677FF;
}

.style-preview {
  width: 100%;
  height: 90px;
  object-fit: cover;
}

.style-info {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
}

.style-name {
  font-size: 14px;
  color: #333;
}

.style-check {
  width: 18px;
  height: 18px;
  border-radius: 9px;
  background: #1677FF;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon {
  width: 10px;
  height: 6px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(-45deg);
  margin-top: -2px;
}

.gallery-section {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.gallery-hint {
  font-size: 12px;
  color: #666;
  margin: -10px 0 15px;
  display: block;
}

.gallery-grid {
  display: flex;
  flex-wrap: wrap;
  margin: -5px;
}

.gallery-item {
  width: calc(33.33% - 10px);
  margin: 5px;
  height: 100px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.gallery-tag {
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 10px;
  padding: 4px 8px;
  text-align: center;
}

.gallery-add {
  width: calc(33.33% - 10px);
  margin: 5px;
  height: 100px;
  border-radius: 8px;
  border: 1px dashed #ddd;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
}

.add-icon {
  width: 24px;
  height: 24px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23999999"><path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
  margin-bottom: 6px;
}

.add-text {
  font-size: 12px;
  color: #999;
}

.video-section {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.video-container {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
}

.store-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-upload {
  width: 100%;
  height: 150px;
  border-radius: 8px;
  border: 1px dashed #ddd;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  padding: 15px;
}

.upload-icon {
  width: 32px;
  height: 32px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23999999"><path d="M18 4l2 4h-3l-2-4h-2l2 4h-3l-2-4H8l2 4H7L5 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V4h-4z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
  margin-bottom: 10px;
}

.upload-text {
  font-size: 14px;
  color: #666;
  margin-bottom: 6px;
}

.upload-hint {
  font-size: 12px;
  color: #999;
  text-align: center;
}

.story-section {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.story-editor {
  position: relative;
  margin-bottom: 15px;
}

.story-textarea {
  width: 100%;
  height: 150px;
  background: #F8FAFC;
  border: 1px solid #EAEAEA;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  color: #333;
}

.text-counter {
  position: absolute;
  right: 10px;
  bottom: 10px;
  font-size: 12px;
  color: #999;
}

.save-story-btn {
  width: 100%;
  height: 44px;
  background: #1677FF;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.save-text {
  font-size: 16px;
  font-weight: 500;
  color: #fff;
}

/* 位置与配送区块样式 */
.location-card {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.location-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15px;
  display: block;
}

.address-container {
  margin-bottom: 15px;
}

.address-info {
  display: flex;
  flex-direction: column;
}

.address-detail {
  display: flex;
  margin-bottom: 10px;
}

.location-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  flex-shrink: 0;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231677FF"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.address-text {
  font-size: 14px;
  color: #333;
  line-height: 20px;
}

.address-actions {
  display: flex;
}

.action-btn {
  height: 32px;
  padding: 0 12px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.action-btn.edit {
  background: #F0F7FF;
}

.action-btn.navigate {
  background: #E6F7FF;
}

.btn-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.btn-icon.edit {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231677FF"><path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.996.996 0 0 0-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.btn-icon.navigate {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231677FF"><path d="M12 2L4.5 20.29l.71.71L12 18l6.79 3 .71-.71L12 2z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.btn-text {
  font-size: 12px;
  color: #1677FF;
}

.map-container {
  position: relative;
  height: 150px;
  border-radius: 8px;
  overflow: hidden;
}

.map-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.map-marker {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.marker-icon {
  width: 32px;
  height: 32px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23D83232"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.map-tools {
  position: absolute;
  right: 10px;
  bottom: 10px;
  display: flex;
}

.map-tool-btn {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

.tool-icon {
  width: 20px;
  height: 20px;
}

.tool-icon.map {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M20.5 3l-.16.03L15 5.1 9 3 3.36 4.9c-.21.07-.36.25-.36.48V20.5c0 .28.22.5.5.5l.16-.03L9 18.9l6 2.1 5.64-1.9c.21-.07.36-.25.36-.48V3.5c0-.28-.22-.5-.5-.5zM15 19l-6-2.11V5l6 2.11V19z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.tool-icon.preview {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.tool-text {
  display: none;
}

.delivery-section {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.section-subtitle {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15px;
  display: block;
}

.delivery-toggle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.toggle-label {
  font-size: 14px;
  color: #333;
}

.delivery-settings {
  margin-top: 15px;
}

.radius-setting {
  margin-bottom: 20px;
}

.setting-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 10px;
  display: block;
}

.radius-slider-container {
  padding: 0 10px;
  position: relative;
}

.radius-slider {
  margin: 10px 0;
}

.radius-value {
  position: absolute;
  right: 10px;
  top: -5px;
  font-size: 12px;
  color: #666;
}

.delivery-fee-settings {
  margin-bottom: 20px;
}

.fee-type-toggle {
  display: flex;
  background: #F5F7FA;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 15px;
}

.toggle-option {
  flex: 1;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #666;
  position: relative;
}

.toggle-option.active {
  color: #fff;
  background: #1677FF;
}

.option-text {
  position: relative;
  z-index: 1;
}

.fee-input-container {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.fee-input-label {
  font-size: 14px;
  color: #333;
  margin-right: 10px;
}

.fee-input-wrap {
  flex: 1;
  position: relative;
}

.fee-input {
  width: 100%;
  height: 40px;
  background: #F8FAFC;
  border: 1px solid #EAEAEA;
  border-radius: 8px;
  padding: 0 40px 0 15px;
  font-size: 14px;
  color: #333;
}

.fee-unit {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  color: #999;
}

.distance-fee-table {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #EAEAEA;
  margin-bottom: 15px;
}

.table-header {
  display: flex;
  background: #F8FAFC;
  height: 40px;
  border-bottom: 1px solid #EAEAEA;
}

.header-cell {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #666;
  text-align: center;
}

.header-cell:not(:last-child) {
  border-right: 1px solid #EAEAEA;
}

.table-row {
  display: flex;
  height: 50px;
  background: #fff;
}

.table-row:not(:last-child) {
  border-bottom: 1px solid #EAEAEA;
}

.row-cell {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #333;
}

.row-cell:not(:last-child) {
  border-right: 1px solid #EAEAEA;
}

.row-cell.distance {
  justify-content: flex-start;
  padding-left: 15px;
}

.tier-fee-input {
  width: 80%;
  height: 36px;
  background: #F8FAFC;
  border: 1px solid #EAEAEA;
  border-radius: 4px;
  padding: 0 10px;
  text-align: center;
}

.row-action {
  width: 28px;
  height: 28px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.row-action.delete {
  background: #FFF0F0;
}

.delete-icon {
  width: 16px;
  height: 16px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FF4D4F"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.add-tier-btn {
  height: 40px;
  background: #F0F7FF;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-text {
  font-size: 14px;
  color: #1677FF;
  margin-left: 5px;
}

.minimum-order {
  margin-top: 20px;
}

.minimum-input-wrap {
  position: relative;
  margin-bottom: 8px;
}

.minimum-input {
  width: 100%;
  height: 40px;
  background: #F8FAFC;
  border: 1px solid #EAEAEA;
  border-radius: 8px;
  padding: 0 40px 0 15px;
  font-size: 14px;
  color: #333;
}

.minimum-unit {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  color: #999;
}

.minimum-hint {
  font-size: 12px;
  color: #999;
}

.empty-delivery, .empty-pickup {
  padding: 30px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 15px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

.pickup-section {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.section-header-with-toggle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.toggle-container {
  display: flex;
  align-items: center;
}

.pickup-instructions {
  margin-bottom: 15px;
}

.pickup-textarea {
  width: 100%;
  height: 100px;
  background: #F8FAFC;
  border: 1px solid #EAEAEA;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  color: #333;
}

/* 认证与资质区块样式 */
.certification-card {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.certification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.cert-status {
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
}

.cert-status.uncertified {
  background: #F5F5F5;
}

.cert-status.pending {
  background: #FCF5EB;
}

.cert-status.certified {
  background: #F6FFED;
}

.cert-status.failed {
  background: #FFF2F0;
}

.cert-status.uncertified .status-text {
  color: #999;
}

.cert-status.pending .status-text {
  color: #FFA41C;
}

.cert-status.certified .status-text {
  color: #52C41A;
}

.cert-status.failed .status-text {
  color: #FF4D4F;
}

.certification-content {
  padding: 15px 0;
}

.uncertified, .in-progress, .cert-failed {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.uncert-icon, .progress-icon, .failed-icon {
  width: 60px;
  height: 60px;
  margin-bottom: 15px;
}

.uncert-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23CCCCCC"><path d="M12 2L4 5v6.09c0 5.05 3.41 9.76 8 10.91 4.59-1.15 8-5.86 8-10.91V5l-8-3zm-1.06 13.54L7.4 12l1.41-1.41 2.12 2.12 4.24-4.24 1.41 1.41-5.64 5.66z" opacity="0.3"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.progress-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FFA41C"><path d="M12 6v3l4-4-4-4v3c-4.42 0-8 3.58-8 8 0 1.57.46 3.03 1.24 4.26L6.7 14.8c-.45-.83-.7-1.79-.7-2.8 0-3.31 2.69-6 6-6zm6.76 1.74L17.3 9.2c.44.84.7 1.79.7 2.8 0 3.31-2.69 6-6 6v-3l-4 4 4 4v-3c4.42 0 8-3.58 8-8 0-1.57-.46-3.03-1.24-4.26z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.failed-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FF4D4F"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.uncert-title, .progress-title, .failed-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.uncert-desc, .progress-desc, .failed-reason {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
  text-align: center;
  padding: 0 20px;
}

.start-cert-btn, .retry-cert-btn {
  width: 140px;
  height: 40px;
  background: #1677FF;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-text {
  font-size: 14px;
  font-weight: 500;
  color: #fff;
}

.cert-progress {
  width: 80%;
  margin-top: 10px;
}

.progress-bar {
  height: 8px;
  background: #F0F0F0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: #FFA41C;
  border-radius: 4px;
}

.progress-text {
  font-size: 12px;
  color: #FFA41C;
  text-align: center;
}

.certified {
  padding: 10px 0;
}

.cert-info {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.cert-icon {
  width: 40px;
  height: 40px;
  margin-right: 15px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2352C41A"><path d="M12 2L4 5v6.09c0 5.05 3.41 9.76 8 10.91 4.59-1.15 8-5.86 8-10.91V5l-8-3zm-1.06 13.54L7.4 12l1.41-1.41 2.12 2.12 4.24-4.24 1.41 1.41-5.64 5.66z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.cert-details {
  flex: 1;
}

.cert-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.cert-time, .cert-number {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
  display: block;
}

.renewal-info {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  background: #FFF7E6;
  border-radius: 8px;
}

.alert-icon {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FFA41C"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.renewal-text {
  flex: 1;
  font-size: 12px;
  color: #FA8C16;
}

.renew-btn {
  width: 60px;
  height: 30px;
  background: #FFA41C;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #fff;
}

.qualification-section {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.no-quals {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 0;
}

.no-quals-img {
  width: 100px;
  height: 100px;
  margin-bottom: 15px;
}

.no-quals-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.no-quals-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
}

.qual-list {
  margin-bottom: 15px;
}

.qual-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #F0F0F0;
}

.qual-item:last-child {
  border-bottom: none;
}

.qual-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  margin-right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 20px;
  font-weight: 600;
}

.qual-icon.food {
  background: linear-gradient(135deg, #FFA62E, #EA4D2C);
}

.qual-icon.health {
  background: linear-gradient(135deg, #36D1DC, #5B86E5);
}

.qual-info {
  flex: 1;
}

.qual-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.qual-desc {
  font-size: 12px;
  color: #999;
}

.qual-status {
  padding: 4px 8px;
  border-radius: 10px;
  font-size: 10px;
  margin: 0 10px;
}

.qual-status.approved {
  background: #F6FFED;
  color: #52C41A;
}

.qual-status.pending {
  background: #FCF5EB;
  color: #FFA41C;
}

.qual-status.rejected {
  background: #FFF2F0;
  color: #FF4D4F;
}

.qual-status.expired {
  background: #F5F5F5;
  color: #999;
}

.qual-actions {
  display: flex;
}

.action-btn {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  margin-left: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.view {
  background: #E6F7FF;
}

.action-btn.edit {
  background: #F0F7FF;
}

.action-btn.delete {
  background: #FFF0F0;
}

.btn-icon.view {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231677FF"><path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.btn-icon.edit {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231677FF"><path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.996.996 0 0 0-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.btn-icon.delete {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FF4D4F"><path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.add-qual-btn {
  height: 44px;
  background: #F0F7FF;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.expiration-reminder {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.reminder-card {
  background: #FAFAFA;
  border-radius: 8px;
  padding: 15px;
}

.reminder-item {
  margin-bottom: 15px;
}

.reminder-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 10px;
  display: block;
}

.reminder-input-wrap {
  position: relative;
}

.reminder-input {
  width: 100%;
  height: 40px;
  background: #fff;
  border: 1px solid #EAEAEA;
  border-radius: 8px;
  padding: 0 40px 0 15px;
  font-size: 14px;
  color: #333;
}

.reminder-unit {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  color: #999;
}

.reminder-checkboxes {
  display: flex;
  flex-wrap: wrap;
}

.checkbox-item {
  display: flex;
  align-items: center;
  margin-right: 20px;
  margin-bottom: 10px;
}

.checkbox {
  width: 18px;
  height: 18px;
  border-radius: 4px;
  border: 1px solid #D9D9D9;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
}

.checkbox.checked {
  background: #1677FF;
  border-color: #1677FF;
}

.check-mark {
  width: 10px;
  height: 5px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(-45deg);
  margin-top: -2px;
}

.checkbox-label {
  font-size: 14px;
  color: #333;
}

.save-reminder-btn {
  width: 100%;
  height: 40px;
  background: #1677FF;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;
}

/* 底部保存按钮 */
.save-btn {
  width: calc(100% - 30px);
  height: 44px;
  background: #1677FF;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  box-shadow: 0 4px 12px rgba(22, 119, 255, 0.2);
}

.save-text {
  font-size: 16px;
  font-weight: 500;
  color: #fff;
}
</style> 