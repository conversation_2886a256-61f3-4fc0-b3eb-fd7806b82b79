/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-c2e1be1d, html.data-v-c2e1be1d, #app.data-v-c2e1be1d, .index-container.data-v-c2e1be1d {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.order-confirm-container.data-v-c2e1be1d {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F2F2F7;
}

/* 自定义导航栏 */
.custom-navbar.data-v-c2e1be1d {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
}
.custom-navbar .navbar-bg.data-v-c2e1be1d {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px rgba(255, 59, 105, 0.15);
}
.custom-navbar .navbar-content.data-v-c2e1be1d {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 30rpx;
  padding-top: var(--status-bar-height, 25px);
  box-sizing: border-box;
}
.custom-navbar .navbar-left.data-v-c2e1be1d, .custom-navbar .navbar-right.data-v-c2e1be1d {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
}
.custom-navbar .navbar-title.data-v-c2e1be1d {
  font-size: 36rpx;
  font-weight: 600;
  color: #FFFFFF;
  letter-spacing: 0.5px;
}
.custom-navbar .icon.data-v-c2e1be1d {
  width: 48rpx;
  height: 48rpx;
}

/* 内容区域 */
.content-scroll.data-v-c2e1be1d {
  flex: 1;
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
  margin-bottom: 100rpx;
  /* 底部结算栏高度 */
}

/* 收货地址 */
.address-section.data-v-c2e1be1d {
  background-color: #FFFFFF;
  margin-bottom: 20rpx;
  position: relative;
}
.address-section .address-content.data-v-c2e1be1d {
  display: flex;
  align-items: center;
  padding: 30rpx;
}
.address-section .address-content .address-info.data-v-c2e1be1d {
  flex: 1;
}
.address-section .address-content .address-info .user-info.data-v-c2e1be1d {
  margin-bottom: 10rpx;
}
.address-section .address-content .address-info .user-info .user-name.data-v-c2e1be1d {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-right: 20rpx;
}
.address-section .address-content .address-info .user-info .user-phone.data-v-c2e1be1d {
  font-size: 28rpx;
  color: #666666;
}
.address-section .address-content .address-info .address-detail.data-v-c2e1be1d {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.4;
}
.address-section .address-content .arrow-icon.data-v-c2e1be1d {
  width: 32rpx;
  height: 32rpx;
  color: #999999;
}
.address-section .address-empty.data-v-c2e1be1d {
  padding: 30rpx;
}
.address-section .address-empty .add-address.data-v-c2e1be1d {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.address-section .address-empty .add-address text.data-v-c2e1be1d {
  font-size: 28rpx;
  color: #666666;
}
.address-section .address-empty .add-address .arrow-icon.data-v-c2e1be1d {
  width: 32rpx;
  height: 32rpx;
  color: #999999;
}
.address-section .address-divider.data-v-c2e1be1d {
  height: 10rpx;
  width: 100%;
  overflow: hidden;
}
.address-section .address-divider .divider-image.data-v-c2e1be1d {
  width: 100%;
  height: 10rpx;
}

/* 商品列表 */
.products-section.data-v-c2e1be1d {
  margin-bottom: 20rpx;
}
.shop-group.data-v-c2e1be1d {
  background-color: #FFFFFF;
  margin-bottom: 20rpx;
}
.shop-group .shop-header.data-v-c2e1be1d {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #F2F2F7;
}
.shop-group .shop-header .shop-logo.data-v-c2e1be1d {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
}
.shop-group .shop-header .shop-name.data-v-c2e1be1d {
  font-size: 28rpx;
  color: #333333;
  margin-left: 10rpx;
  font-weight: 500;
}
.shop-group .product-list.data-v-c2e1be1d {
  padding: 0 30rpx;
}
.shop-group .product-item.data-v-c2e1be1d {
  display: flex;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #F2F2F7;
}
.shop-group .product-item.data-v-c2e1be1d:last-child {
  border-bottom: none;
}
.shop-group .product-item .product-image.data-v-c2e1be1d {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
}
.shop-group .product-item .product-info.data-v-c2e1be1d {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.shop-group .product-item .product-info .product-name.data-v-c2e1be1d {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.shop-group .product-item .product-info .product-specs.data-v-c2e1be1d {
  font-size: 24rpx;
  color: #999999;
  margin-top: 10rpx;
}
.shop-group .product-item .product-info .product-price-row.data-v-c2e1be1d {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}
.shop-group .product-item .product-info .product-price-row .product-price.data-v-c2e1be1d {
  font-size: 28rpx;
  color: #FF3B69;
  font-weight: 600;
}
.shop-group .product-item .product-info .product-price-row .product-quantity.data-v-c2e1be1d {
  font-size: 28rpx;
  color: #999999;
}
.shop-group .delivery-section.data-v-c2e1be1d, .shop-group .remark-section.data-v-c2e1be1d, .shop-group .coupon-section.data-v-c2e1be1d {
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #F2F2F7;
}
.shop-group .delivery-section .section-title.data-v-c2e1be1d, .shop-group .remark-section .section-title.data-v-c2e1be1d, .shop-group .coupon-section .section-title.data-v-c2e1be1d {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 20rpx;
}
.shop-group .delivery-options.data-v-c2e1be1d {
  display: flex;
}
.shop-group .delivery-options .delivery-option.data-v-c2e1be1d {
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #666666;
  background-color: #F2F2F7;
  margin-right: 20rpx;
}
.shop-group .delivery-options .delivery-option.active.data-v-c2e1be1d {
  color: #FFFFFF;
  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
}
.shop-group .remark-input.data-v-c2e1be1d {
  height: 80rpx;
  background-color: #F2F2F7;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
}
.shop-group .coupon-info.data-v-c2e1be1d {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.shop-group .coupon-info text.data-v-c2e1be1d {
  font-size: 28rpx;
  color: #FF3B69;
}
.shop-group .coupon-info .arrow-icon.data-v-c2e1be1d {
  width: 32rpx;
  height: 32rpx;
  color: #999999;
}
.shop-group .shop-subtotal.data-v-c2e1be1d {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #F2F2F7;
}
.shop-group .shop-subtotal text.data-v-c2e1be1d {
  font-size: 28rpx;
  color: #333333;
}
.shop-group .shop-subtotal text.data-v-c2e1be1d:last-child {
  font-weight: 600;
}

/* 支付方式 */
.payment-section.data-v-c2e1be1d {
  background-color: #FFFFFF;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
}
.payment-section .section-title.data-v-c2e1be1d {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 20rpx;
}
.payment-section .payment-options .payment-option.data-v-c2e1be1d {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #F2F2F7;
}
.payment-section .payment-options .payment-option.data-v-c2e1be1d:last-child {
  border-bottom: none;
}
.payment-section .payment-options .payment-option .payment-icon.data-v-c2e1be1d {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
}
.payment-section .payment-options .payment-option text.data-v-c2e1be1d {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}
.payment-section .payment-options .payment-option .checkbox.data-v-c2e1be1d {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  background-color: #FF3B69;
  display: flex;
  align-items: center;
  justify-content: center;
}
.payment-section .payment-options .payment-option .checkbox .check-icon.data-v-c2e1be1d {
  color: #FFFFFF;
}

/* 订单金额 */
.amount-section.data-v-c2e1be1d {
  background-color: #FFFFFF;
  padding: 20rpx 30rpx;
}
.amount-section .amount-row.data-v-c2e1be1d {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 0;
}
.amount-section .amount-row text.data-v-c2e1be1d {
  font-size: 28rpx;
  color: #333333;
}

/* 底部结算栏 */
.checkout-bar.data-v-c2e1be1d {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  padding-bottom: env(safe-area-inset-bottom);
  /* 适配 iPhone X 以上的底部安全区域 */
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 99;
}
.checkout-bar .total-amount.data-v-c2e1be1d {
  font-size: 28rpx;
  color: #333333;
}
.checkout-bar .total-amount .price.data-v-c2e1be1d {
  font-size: 32rpx;
  color: #FF3B69;
  font-weight: 600;
}
.checkout-bar .submit-btn.data-v-c2e1be1d {
  padding: 16rpx 40rpx;
  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
  color: #FFFFFF;
  font-size: 28rpx;
  border-radius: 40rpx;
}

/* 底部安全区域 */
.safe-area-bottom.data-v-c2e1be1d {
  height: 34px;
  /* iOS 安全区域高度 */
}