/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.top-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 120rpx;
  /* 为底部支付栏留出空间 */
}
.top-content {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 信息卡片 */
.info-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}
.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.info-tag {
  margin-left: 15rpx;
  font-size: 22rpx;
  color: #ffffff;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
}
.people-to-car {
  background-color: #0A84FF;
}
.car-to-people {
  background-color: #FF453A;
}
.goods-to-car {
  background-color: #30D158;
}
.car-to-goods {
  background-color: #FF9F0A;
}

/* 路线信息 */
.route-section {
  margin-bottom: 10rpx;
}
.route-points {
  margin-bottom: 10rpx;
}
.route-point {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.point-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 15rpx;
  flex-shrink: 0;
}
.start {
  background-color: #0A84FF;
}
.end {
  background-color: #FF453A;
}
.point-text {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
}
.route-divider {
  padding-left: 7rpx;
  margin: 10rpx 0;
  display: flex;
}
.divider-line {
  width: 2rpx;
  height: 30rpx;
  background-color: #dddddd;
}
.divider-arrow {
  margin-left: -7rpx;
  margin-top: 30rpx;
}
.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

/* 置顶选项卡片 */
.premium-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}
.premium-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.premium-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 15rpx;
}
.premium-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.premium-desc {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 30rpx;
  line-height: 1.5;
}
.options-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.option-item {
  background-color: #f8f8f8;
  border-radius: 16rpx;
  padding: 24rpx;
  position: relative;
  transition: all 0.3s;
}
.option-selected {
  background-color: rgba(10, 132, 255, 0.05);
  border: 1rpx solid #0A84FF;
}
.option-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.option-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}
.option-desc {
  font-size: 24rpx;
  color: #999999;
}
.option-price {
  font-size: 36rpx;
  font-weight: 700;
  color: #FF3B30;
}
.option-price::before {
  content: "¥";
  font-size: 24rpx;
  font-weight: 400;
}
.option-check {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  background-color: #0A84FF;
  display: flex;
  align-items: center;
  justify-content: center;
}
.check-icon {
  width: 20rpx;
  height: 20rpx;
}

/* 支付方式卡片 */
.payment-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}
.payment-header {
  margin-bottom: 20rpx;
}
.payment-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.payment-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.payment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background-color: #f8f8f8;
  border-radius: 16rpx;
  transition: all 0.3s;
}
.payment-selected {
  background-color: rgba(10, 132, 255, 0.05);
  border: 1rpx solid #0A84FF;
}
.payment-left {
  display: flex;
  align-items: center;
}
.payment-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}
.payment-name {
  font-size: 28rpx;
  color: #333333;
}
.payment-check {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  background-color: #0A84FF;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 温馨提示 */
.tips-card {
  background-color: rgba(10, 132, 255, 0.05);
  border-radius: 24rpx;
  padding: 20rpx 30rpx;
}
.tips-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}
.tips-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 10rpx;
  opacity: 0.7;
}
.tips-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}
.tips-content {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}
.tips-text {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
}

/* 底部支付栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}
.price-info {
  display: flex;
  align-items: center;
}
.price-label {
  font-size: 28rpx;
  color: #666666;
}
.price-value {
  font-size: 36rpx;
  font-weight: 700;
  color: #FF3B30;
}
.pay-button {
  height: 80rpx;
  border-radius: 40rpx;
  background: linear-gradient(135deg, #0A84FF, #0066CC);
  color: #ffffff;
  font-size: 30rpx;
  font-weight: 600;
  padding: 0 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 10rpx rgba(10, 132, 255, 0.3);
}