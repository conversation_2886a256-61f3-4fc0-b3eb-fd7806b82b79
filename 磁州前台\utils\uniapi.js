/**
 * uni-app API 封装
 * 提供常用的 uni API 的封装，使其更易于使用
 */

/**
 * 显示消息提示框
 * @param {string|object} options 提示内容或配置对象
 * @returns {Promise} Promise对象
 */
export function showToast(options) {
  return new Promise((resolve) => {
    if (typeof options === 'string') {
      uni.showToast({
        title: options,
        icon: 'none',
        duration: 2000,
        success: resolve,
        fail: resolve
      });
    } else {
      uni.showToast({
        title: options.title || '',
        icon: options.icon || 'none',
        duration: options.duration || 2000,
        mask: options.mask || false,
        success: resolve,
        fail: resolve
      });
    }
  });
}

/**
 * 显示加载中提示框
 * @param {string|object} options 提示内容或配置对象
 * @returns {Promise} Promise对象
 */
export function showLoading(options) {
  return new Promise((resolve) => {
    if (typeof options === 'string') {
      uni.showLoading({
        title: options,
        mask: true,
        success: resolve,
        fail: resolve
      });
    } else {
      uni.showLoading({
        title: options.title || '',
        mask: options.mask !== undefined ? options.mask : true,
        success: resolve,
        fail: resolve
      });
    }
  });
}

/**
 * 隐藏加载中提示框
 */
export function hideLoading() {
  uni.hideLoading();
}

/**
 * 显示模态对话框
 * @param {object} options 配置对象
 * @returns {Promise} Promise对象，resolve的结果为 { confirm: boolean, cancel: boolean }
 */
export function showModal(options) {
  return new Promise((resolve) => {
    uni.showModal({
      title: options.title || '提示',
      content: options.content || '',
      showCancel: options.showCancel !== undefined ? options.showCancel : true,
      cancelText: options.cancelText || '取消',
      confirmText: options.confirmText || '确定',
      success: resolve,
      fail: () => resolve({ confirm: false, cancel: true })
    });
  });
}

/**
 * 显示操作菜单
 * @param {object} options 配置对象
 * @returns {Promise} Promise对象，resolve的结果为 { tapIndex: number }
 */
export function showActionSheet(options) {
  return new Promise((resolve) => {
    uni.showActionSheet({
      itemList: options.itemList || [],
      itemColor: options.itemColor || '#000000',
      success: resolve,
      fail: () => resolve({ tapIndex: -1 })
    });
  });
}

/**
 * 设置剪贴板内容
 * @param {string} data 需要设置的内容
 * @returns {Promise} Promise对象
 */
export function setClipboardData(data) {
  return new Promise((resolve, reject) => {
    uni.setClipboardData({
      data,
      success: resolve,
      fail: reject
    });
  });
}

/**
 * 获取剪贴板内容
 * @returns {Promise<string>} Promise对象，resolve的结果为剪贴板内容
 */
export function getClipboardData() {
  return new Promise((resolve, reject) => {
    uni.getClipboardData({
      success: (res) => resolve(res.data),
      fail: reject
    });
  });
}

/**
 * 保存图片到相册
 * @param {string} filePath 图片文件路径
 * @returns {Promise} Promise对象
 */
export function saveImageToPhotosAlbum(filePath) {
  return new Promise((resolve, reject) => {
    uni.saveImageToPhotosAlbum({
      filePath,
      success: resolve,
      fail: reject
    });
  });
}

/**
 * 预览图片
 * @param {object} options 配置对象
 * @returns {Promise} Promise对象
 */
export function previewImage(options) {
  return new Promise((resolve) => {
    uni.previewImage({
      urls: options.urls || [],
      current: options.current || options.urls[0],
      success: resolve,
      fail: resolve
    });
  });
}

/**
 * 获取系统信息
 * @returns {Promise<object>} Promise对象，resolve的结果为系统信息
 */
export function getSystemInfo() {
  return new Promise((resolve, reject) => {
    uni.getSystemInfo({
      success: resolve,
      fail: reject
    });
  });
}

/**
 * 导航到指定页面
 * @param {string|object} options 页面路径或配置对象
 * @returns {Promise} Promise对象
 */
export function navigateTo(options) {
  return new Promise((resolve, reject) => {
    if (typeof options === 'string') {
      uni.navigateTo({
        url: options,
        success: resolve,
        fail: reject
      });
    } else {
      uni.navigateTo({
        url: options.url,
        success: resolve,
        fail: reject
      });
    }
  });
}

/**
 * 重定向到指定页面
 * @param {string|object} options 页面路径或配置对象
 * @returns {Promise} Promise对象
 */
export function redirectTo(options) {
  return new Promise((resolve, reject) => {
    if (typeof options === 'string') {
      uni.redirectTo({
        url: options,
        success: resolve,
        fail: reject
      });
    } else {
      uni.redirectTo({
        url: options.url,
        success: resolve,
        fail: reject
      });
    }
  });
}

/**
 * 返回上一页
 * @param {number|object} options 返回的页面数或配置对象
 * @returns {Promise} Promise对象
 */
export function navigateBack(options) {
  return new Promise((resolve) => {
    if (typeof options === 'number') {
      uni.navigateBack({
        delta: options,
        success: resolve,
        fail: resolve
      });
    } else {
      uni.navigateBack({
        delta: options?.delta || 1,
        success: resolve,
        fail: resolve
      });
    }
  });
} 