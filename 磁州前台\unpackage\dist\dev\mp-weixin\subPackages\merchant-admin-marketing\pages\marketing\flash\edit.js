"use strict";
const common_vendor = require("../../../../../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_circle = common_vendor.resolveComponent("circle");
  (_component_path + _component_svg + _component_circle)();
}
const _sfc_main = {
  __name: "edit",
  setup(__props) {
    const flashId = common_vendor.ref(null);
    const formData = common_vendor.reactive({
      name: "夏季清凉风扇特惠",
      images: [
        "/static/images/flash-item1.jpg",
        "/static/images/flash-item1-2.jpg",
        "/static/images/flash-item1-3.jpg"
      ],
      detailImages: [
        "/static/images/flash-detail1.jpg",
        "/static/images/flash-detail2.jpg",
        "/static/images/flash-detail3.jpg"
      ],
      originalPrice: "129.9",
      flashPrice: "59.9",
      stockTotal: "100",
      stockSold: "0",
      purchaseLimit: "2",
      startTime: "2023-07-01 12:00",
      endTime: "2023-07-01 14:00",
      rules: "活动期间每人限购2件，秒杀商品不支持退换货，数量有限，先到先得",
      description: "这是一款高品质的夏季清凉风扇，采用先进技术，风力强劲，静音设计，让您的夏天更加舒适。\n\n规格参数：\n- 功率：30W\n- 风速：3档可调\n- 电池容量：4000mAh\n- 续航时间：4-8小时\n- 材质：环保ABS",
      status: "active"
    });
    const timeError = common_vendor.ref("");
    const discountValue = common_vendor.computed(() => {
      if (!formData.originalPrice || !formData.flashPrice || formData.originalPrice <= 0) {
        return "- 折";
      }
      const discount = (formData.flashPrice / formData.originalPrice * 10).toFixed(1);
      return `${discount} 折`;
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const addImage = () => {
      common_vendor.index.chooseImage({
        count: 5 - formData.images.length,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          formData.images = [...formData.images, ...res.tempFilePaths];
        }
      });
    };
    const removeImage = (index) => {
      formData.images.splice(index, 1);
    };
    const addDetailImage = () => {
      common_vendor.index.chooseImage({
        count: 9,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          formData.detailImages = [...formData.detailImages, ...res.tempFilePaths];
        }
      });
    };
    const removeDetailImage = (index) => {
      formData.detailImages.splice(index, 1);
    };
    const startTimeChange = (e) => {
      formData.startTime = e.detail.value;
      validateTime();
    };
    const endTimeChange = (e) => {
      formData.endTime = e.detail.value;
      validateTime();
    };
    const validateTime = () => {
      if (!formData.startTime || !formData.endTime) {
        timeError.value = "";
        return;
      }
      const start = new Date(formData.startTime.replace(/-/g, "/"));
      const end = new Date(formData.endTime.replace(/-/g, "/"));
      if (end <= start) {
        timeError.value = "结束时间必须晚于开始时间";
      } else {
        timeError.value = "";
      }
    };
    const statusChange = (e) => {
      formData.status = e.detail.value ? "active" : "inactive";
    };
    const previewFlashSale = () => {
      if (!validateForm(true))
        return;
      common_vendor.index.showToast({
        title: "预览功能开发中",
        icon: "none"
      });
    };
    const saveFlashSale = () => {
      if (!validateForm())
        return;
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "保存成功",
          icon: "success",
          duration: 1500,
          success: () => {
            setTimeout(() => {
              common_vendor.index.navigateBack();
            }, 1500);
          }
        });
      }, 1500);
    };
    const validateForm = (isPreview = false) => {
      if (!formData.name.trim()) {
        common_vendor.index.showToast({
          title: "请输入活动名称",
          icon: "none"
        });
        return false;
      }
      if (formData.images.length === 0) {
        common_vendor.index.showToast({
          title: "请上传至少一张商品图片",
          icon: "none"
        });
        return false;
      }
      if (!formData.originalPrice || parseFloat(formData.originalPrice) <= 0) {
        common_vendor.index.showToast({
          title: "请输入有效的原价",
          icon: "none"
        });
        return false;
      }
      if (!formData.flashPrice || parseFloat(formData.flashPrice) <= 0) {
        common_vendor.index.showToast({
          title: "请输入有效的秒杀价",
          icon: "none"
        });
        return false;
      }
      if (parseFloat(formData.flashPrice) >= parseFloat(formData.originalPrice)) {
        common_vendor.index.showToast({
          title: "秒杀价必须低于原价",
          icon: "none"
        });
        return false;
      }
      if (!formData.stockTotal || parseInt(formData.stockTotal) <= 0) {
        common_vendor.index.showToast({
          title: "请输入有效的活动库存",
          icon: "none"
        });
        return false;
      }
      if (!formData.startTime) {
        common_vendor.index.showToast({
          title: "请选择开始时间",
          icon: "none"
        });
        return false;
      }
      if (!formData.endTime) {
        common_vendor.index.showToast({
          title: "请选择结束时间",
          icon: "none"
        });
        return false;
      }
      if (timeError.value) {
        common_vendor.index.showToast({
          title: timeError.value,
          icon: "none"
        });
        return false;
      }
      return true;
    };
    common_vendor.onMounted(() => {
      getOpenerEventChannel();
      const query = common_vendor.index.getSystemInfoSync().platform === "h5" ? location.href.split("?")[1] : "";
      if (query) {
        const params = query.split("&").reduce((res, item) => {
          const [key, value] = item.split("=");
          res[key] = value;
          return res;
        }, {});
        flashId.value = params.id;
      } else {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        if (currentPage && currentPage.options) {
          flashId.value = currentPage.options.id;
        }
      }
      loadFlashSaleData();
    });
    const loadFlashSaleData = () => {
      if (!flashId.value)
        return;
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-marketing/pages/marketing/flash/edit.vue:474", "加载秒杀活动数据, ID:", flashId.value);
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
      }, 500);
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          d: "M15 18L9 12L15 6",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        b: common_vendor.p({
          width: "24",
          height: "24",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        c: common_vendor.o(goBack),
        d: common_vendor.o(saveFlashSale),
        e: formData.name,
        f: common_vendor.o(($event) => formData.name = $event.detail.value),
        g: common_vendor.t(formData.name.length),
        h: common_vendor.f(formData.images, (image, index, i0) => {
          return {
            a: image,
            b: "6342107d-3-" + i0 + "," + ("6342107d-2-" + i0),
            c: "6342107d-4-" + i0 + "," + ("6342107d-2-" + i0),
            d: "6342107d-2-" + i0,
            e: common_vendor.o(($event) => removeImage(index), index),
            f: index
          };
        }),
        i: common_vendor.p({
          cx: "12",
          cy: "12",
          r: "10",
          fill: "rgba(0,0,0,0.5)"
        }),
        j: common_vendor.p({
          d: "M15 9L9 15M9 9L15 15",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round"
        }),
        k: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        l: formData.images.length < 5
      }, formData.images.length < 5 ? {
        m: common_vendor.p({
          d: "M12 5V19M5 12H19",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round"
        }),
        n: common_vendor.p({
          width: "24",
          height: "24",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        o: common_vendor.o(addImage)
      } : {}, {
        p: formData.originalPrice,
        q: common_vendor.o(($event) => formData.originalPrice = $event.detail.value),
        r: formData.flashPrice,
        s: common_vendor.o(($event) => formData.flashPrice = $event.detail.value),
        t: common_vendor.t(discountValue.value),
        v: formData.stockTotal,
        w: common_vendor.o(($event) => formData.stockTotal = $event.detail.value),
        x: formData.purchaseLimit,
        y: common_vendor.o(($event) => formData.purchaseLimit = $event.detail.value),
        z: common_vendor.t(formData.startTime || "请选择开始时间"),
        A: formData.startTime,
        B: common_vendor.o(startTimeChange),
        C: common_vendor.t(formData.endTime || "请选择结束时间"),
        D: formData.endTime,
        E: common_vendor.o(endTimeChange),
        F: timeError.value
      }, timeError.value ? {
        G: common_vendor.t(timeError.value)
      } : {}, {
        H: formData.rules,
        I: common_vendor.o(($event) => formData.rules = $event.detail.value),
        J: common_vendor.t(formData.rules.length),
        K: formData.description,
        L: common_vendor.o(($event) => formData.description = $event.detail.value),
        M: common_vendor.t(formData.description.length),
        N: common_vendor.f(formData.detailImages, (image, index, i0) => {
          return {
            a: image,
            b: "6342107d-8-" + i0 + "," + ("6342107d-7-" + i0),
            c: "6342107d-9-" + i0 + "," + ("6342107d-7-" + i0),
            d: "6342107d-7-" + i0,
            e: common_vendor.o(($event) => removeDetailImage(index), index),
            f: index
          };
        }),
        O: common_vendor.p({
          cx: "12",
          cy: "12",
          r: "10",
          fill: "rgba(0,0,0,0.5)"
        }),
        P: common_vendor.p({
          d: "M15 9L9 15M9 9L15 15",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round"
        }),
        Q: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        R: common_vendor.p({
          d: "M12 5V19M5 12H19",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round"
        }),
        S: common_vendor.p({
          width: "24",
          height: "24",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        T: common_vendor.o(addDetailImage),
        U: formData.status === "active",
        V: common_vendor.o(statusChange),
        W: common_vendor.t(formData.status === "active" ? "上架中" : "已下架"),
        X: common_vendor.o(previewFlashSale),
        Y: common_vendor.o(saveFlashSale)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/flash/edit.js.map
