<template>
  <view class="set-level-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">设置分销员等级</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 分销员信息 -->
    <view class="distributor-card">
      <view class="distributor-info">
        <image class="avatar" :src="distributorInfo.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
        <view class="info-content">
          <text class="name">{{distributorInfo.name || '未知用户'}}</text>
          <text class="phone">{{distributorInfo.phone || '无联系方式'}}</text>
        </view>
      </view>
      
      <view class="current-level">
        <text class="level-label">当前等级</text>
        <text class="level-value" :style="{ backgroundColor: distributorInfo.levelColor || '#67C23A' }">
          {{distributorInfo.levelName || '普通分销员'}}
        </text>
      </view>
    </view>
    
    <!-- 等级列表 -->
    <view class="level-list">
      <view class="list-header">
        <text class="header-title">选择等级</text>
      </view>
      
      <view 
        v-for="(level, index) in levels" 
        :key="index" 
        class="level-item"
        :class="{ 'active': selectedLevelId === level.id }"
        @click="selectLevel(level)"
      >
        <view class="level-left">
          <view class="level-radio">
            <view class="radio-inner" v-if="selectedLevelId === level.id"></view>
          </view>
          <view class="level-info">
            <view class="level-name-wrap">
              <text class="level-name">{{level.name}}</text>
              <text class="level-tag" :style="{ backgroundColor: level.color }">{{level.tag}}</text>
            </view>
            <text class="level-desc">{{level.description || '无等级说明'}}</text>
          </view>
        </view>
        
        <view class="level-right">
          <text class="commission-rate">佣金 {{level.commissionRates.level1}}%</text>
        </view>
      </view>
    </view>
    
    <!-- 提交按钮 -->
    <view class="submit-section">
      <button class="submit-btn" @click="submitSetLevel" :disabled="!canSubmit">确认设置</button>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import distributionService from '@/utils/distributionService';

// 分销员ID
const distributorId = ref('');

// 分销员信息
const distributorInfo = ref({});

// 等级列表
const levels = ref([]);

// 选中的等级ID
const selectedLevelId = ref(0);

// 是否可以提交
const canSubmit = computed(() => {
  return selectedLevelId.value > 0 && selectedLevelId.value !== distributorInfo.value.levelId;
});

// 页面加载
onMounted(() => {
  // 获取路由参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.$page?.options || {};
  
  // 设置分销员ID
  distributorId.value = options.id || '';
  
  if (distributorId.value) {
    // 获取分销员信息
    getDistributorDetail();
    
    // 获取等级列表
    getDistributionLevels();
  } else {
    uni.showToast({
      title: '缺少分销员ID',
      icon: 'none'
    });
    
    setTimeout(() => {
      goBack();
    }, 1500);
  }
});

// 获取分销员详情
const getDistributorDetail = async () => {
  try {
    uni.showLoading({
      title: '加载中...',
      mask: true
    });
    
    const result = await distributionService.getDistributorDetail(distributorId.value);
    
    uni.hideLoading();
    
    if (result) {
      distributorInfo.value = result;
      
      // 设置当前等级ID
      if (result.levelId) {
        selectedLevelId.value = result.levelId;
      }
    } else {
      uni.showToast({
        title: '获取分销员详情失败',
        icon: 'none'
      });
    }
  } catch (error) {
    uni.hideLoading();
    console.error('获取分销员详情失败', error);
    uni.showToast({
      title: '获取分销员详情失败',
      icon: 'none'
    });
  }
};

// 获取分销等级列表
const getDistributionLevels = async () => {
  try {
    const result = await distributionService.getDistributionLevels();
    
    if (result && result.levels && result.levels.length > 0) {
      levels.value = result.levels;
    }
  } catch (error) {
    console.error('获取分销等级列表失败', error);
    uni.showToast({
      title: '获取分销等级列表失败',
      icon: 'none'
    });
  }
};

// 选择等级
const selectLevel = (level) => {
  selectedLevelId.value = level.id;
};

// 提交设置等级
const submitSetLevel = async () => {
  if (!canSubmit.value) return;
  
  try {
    uni.showLoading({
      title: '设置中...',
      mask: true
    });
    
    const result = await distributionService.setDistributorLevel({
      id: distributorId.value,
      levelId: selectedLevelId.value
    });
    
    uni.hideLoading();
    
    if (result.success) {
      uni.showToast({
        title: '设置成功',
        icon: 'success'
      });
      
      // 返回上一页
      setTimeout(() => {
        goBack();
      }, 1500);
    } else {
      uni.showModal({
        title: '设置失败',
        content: result.message || '请稍后再试',
        showCancel: false
      });
    }
  } catch (error) {
    uni.hideLoading();
    console.error('设置等级失败', error);
    uni.showToast({
      title: '设置失败',
      icon: 'none'
    });
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 显示帮助
const showHelp = () => {
  uni.showModal({
    title: '设置分销员等级帮助',
    content: '在此页面您可以设置分销员的等级，不同等级的分销员享有不同的佣金比例和特权。',
    showCancel: false
  });
};
</script>

<style lang="scss">
.set-level-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 88rpx 32rpx 30rpx;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);
}

.navbar-back {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24rpx;
  height: 24rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.navbar-right {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

/* 分销员信息卡片 */
.distributor-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.distributor-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}

.info-content {
  flex: 1;
}

.name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.phone {
  font-size: 28rpx;
  color: #666;
}

.current-level {
  display: flex;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.level-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
}

.level-value {
  font-size: 24rpx;
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

/* 等级列表 */
.level-list {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.list-header {
  margin-bottom: 20rpx;
}

.header-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.level-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.level-item:last-child {
  border-bottom: none;
}

.level-item.active {
  background-color: rgba(107, 15, 190, 0.05);
}

.level-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.level-radio {
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  border: 2rpx solid #ddd;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.level-item.active .level-radio {
  border-color: #6B0FBE;
}

.radio-inner {
  width: 24rpx;
  height: 24rpx;
  border-radius: 12rpx;
  background-color: #6B0FBE;
}

.level-info {
  flex: 1;
}

.level-name-wrap {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.level-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-right: 16rpx;
}

.level-tag {
  font-size: 22rpx;
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.level-desc {
  font-size: 24rpx;
  color: #999;
}

.level-right {
  margin-left: 20rpx;
}

.commission-rate {
  font-size: 26rpx;
  color: #FF5722;
  font-weight: 600;
}

/* 提交按钮 */
.submit-section {
  margin: 30rpx;
}

.submit-btn {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 32rpx;
  padding: 20rpx 0;
  line-height: 1.5;
  width: 100%;
}

.submit-btn[disabled] {
  background: #cccccc;
  color: #ffffff;
}
</style> 