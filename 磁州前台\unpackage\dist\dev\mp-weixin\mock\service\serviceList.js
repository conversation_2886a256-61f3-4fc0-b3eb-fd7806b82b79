"use strict";
const serviceList = [
  {
    id: "service-001",
    title: "家电维修",
    icon: "/static/images/service/repair.png",
    description: "提供各类家电维修服务，包括空调、冰箱、洗衣机、电视等",
    price: "上门费30元起",
    rating: 4.8,
    orderCount: 1256,
    category: "家居服务",
    tags: ["上门服务", "快速响应", "专业维修"]
  },
  {
    id: "service-002",
    title: "保洁服务",
    icon: "/static/images/service/cleaning.png",
    description: "提供专业的家庭保洁、办公室保洁、开荒保洁等服务",
    price: "50元/小时起",
    rating: 4.7,
    orderCount: 986,
    category: "家居服务",
    tags: ["专业保洁", "定期保洁", "深度清洁"]
  },
  {
    id: "service-003",
    title: "搬家服务",
    icon: "/static/images/service/moving.png",
    description: "提供专业的居民搬家、企业搬迁、小型搬运等服务",
    price: "200元/次起",
    rating: 4.6,
    orderCount: 758,
    category: "家居服务",
    tags: ["专业搬运", "包装服务", "全程保险"]
  },
  {
    id: "service-004",
    title: "管道疏通",
    icon: "/static/images/service/plumbing.png",
    description: "提供厨房、卫生间等各类管道疏通服务",
    price: "80元/次起",
    rating: 4.5,
    orderCount: 632,
    category: "家居服务",
    tags: ["快速响应", "彻底疏通", "免费复查"]
  },
  {
    id: "service-005",
    title: "上门按摩",
    icon: "/static/images/service/massage.png",
    description: "提供专业的上门按摩、推拿、足疗等服务",
    price: "120元/小时起",
    rating: 4.9,
    orderCount: 1458,
    category: "生活服务",
    tags: ["专业技师", "舒适体验", "中医理疗"]
  },
  {
    id: "service-006",
    title: "美甲服务",
    icon: "/static/images/service/nail.png",
    description: "提供专业的美甲、修甲、卸甲等服务",
    price: "80元/次起",
    rating: 4.7,
    orderCount: 865,
    category: "生活服务",
    tags: ["时尚设计", "环保材料", "持久不掉色"]
  },
  {
    id: "service-007",
    title: "宠物寄养",
    icon: "/static/images/service/pet.png",
    description: "提供专业的宠物寄养、喂食、洗澡、遛狗等服务",
    price: "60元/天起",
    rating: 4.8,
    orderCount: 723,
    category: "生活服务",
    tags: ["专业护理", "舒适环境", "实时监控"]
  },
  {
    id: "service-008",
    title: "代驾服务",
    icon: "/static/images/service/driving.png",
    description: "提供专业的代驾服务，让您安全到家",
    price: "30元/次起",
    rating: 4.6,
    orderCount: 1562,
    category: "出行服务",
    tags: ["专业驾驶", "安全保障", "全程保险"]
  }
];
const serviceCategories = [
  { id: 0, name: "全部" },
  { id: 1, name: "家居服务" },
  { id: 2, name: "生活服务" },
  { id: 3, name: "出行服务" },
  { id: 4, name: "教育培训" },
  { id: 5, name: "健康医疗" },
  { id: 6, name: "商务服务" },
  { id: 7, name: "其他服务" }
];
const fetchServiceList = (categoryId = 0, page = 1, pageSize = 10) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let result = [...serviceList];
      if (categoryId !== 0) {
        const categoryMap = {
          1: "家居服务",
          2: "生活服务",
          3: "出行服务",
          4: "教育培训",
          5: "健康医疗",
          6: "商务服务",
          7: "其他服务"
        };
        result = result.filter((item) => item.category === categoryMap[categoryId]);
      }
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      const data = result.slice(start, end);
      resolve({
        list: data,
        total: result.length,
        hasMore: end < result.length
      });
    }, 500);
  });
};
const fetchServiceCategories = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(serviceCategories);
    }, 300);
  });
};
const fetchHotServices = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const hotServices = [...serviceList].sort((a, b) => b.orderCount - a.orderCount).slice(0, 4);
      resolve(hotServices);
    }, 500);
  });
};
exports.fetchHotServices = fetchHotServices;
exports.fetchServiceCategories = fetchServiceCategories;
exports.fetchServiceList = fetchServiceList;
exports.serviceCategories = serviceCategories;
exports.serviceList = serviceList;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/mock/service/serviceList.js.map
