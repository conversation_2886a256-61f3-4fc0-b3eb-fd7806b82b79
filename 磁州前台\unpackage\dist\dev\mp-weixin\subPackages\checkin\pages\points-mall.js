"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  __name: "points-mall",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const userPoints = common_vendor.ref(1280);
    const currentCategory = common_vendor.ref(0);
    const isRefreshing = common_vendor.ref(false);
    const showPopup = common_vendor.ref(false);
    const selectedProduct = common_vendor.ref({});
    const categories = [
      { id: 0, name: "全部" },
      { id: 3, name: "虚拟商品" },
      { id: 2, name: "实物商品" },
      { id: 4, name: "限时兑换" },
      { id: 1, name: "优惠券" }
    ];
    const products = common_vendor.ref([
      {
        id: 1,
        name: "5元通用券",
        description: "可用于平台内任意商家消费抵扣",
        points: 100,
        image: "/static/images/banner/coupon-1.png",
        category: 1,
        validity: "兑换后30天内有效",
        stock: 999,
        rules: "单笔消费满20元可用，每人每月限兑换5张",
        tag: "热门"
      },
      {
        id: 2,
        name: "10元外卖券",
        description: "仅限平台认证餐饮商家使用",
        points: 180,
        image: "/static/images/banner/coupon-2.png",
        category: 1,
        validity: "兑换后30天内有效",
        stock: 500,
        rules: "单笔消费满30元可用，每人每月限兑换3张"
      },
      {
        id: 3,
        name: "精美保温杯",
        description: "304不锈钢内胆，24小时保温",
        points: 1200,
        image: "/static/images/banner/product-1.png",
        category: 2,
        stock: 50,
        rules: "兑换后7天内发货，快递费用由平台承担"
      },
      {
        id: 4,
        name: "信息置顶服务",
        description: "发布信息优先展示24小时",
        points: 300,
        image: "/static/images/banner/service-1.png",
        category: 3,
        validity: "兑换后7天内有效",
        rules: "每个信息仅可使用一次，有效期内未使用将自动失效"
      },
      {
        id: 5,
        name: "会员月卡",
        description: "30天会员特权，免费发布信息",
        points: 500,
        image: "/static/images/banner/vip-1.png",
        category: 3,
        validity: "激活后30天内有效",
        rules: "包含会员所有特权，可与其他优惠共享使用",
        tag: "超值"
      },
      {
        id: 6,
        name: "限时抢购券",
        description: "周末专享，满100减50",
        points: 250,
        image: "/static/images/banner/coupon-3.png",
        category: 4,
        validity: "本周六、日两天有效",
        stock: 100,
        rules: "限指定商家使用，详见券面说明",
        tag: "限时"
      },
      {
        id: 7,
        name: "信息置顶卡（7天）",
        description: "让您的信息在同城列表置顶展示7天，曝光翻倍！",
        points: 500,
        image: "/static/images/banner/top-card-placeholder.png",
        category: 3,
        validity: "兑换后7天内有效",
        rules: "仅限同城信息使用，每条信息每月限用一次",
        tag: "虚拟"
      },
      {
        id: 8,
        name: "VIP会员月卡",
        description: "享受专属标识、免费发布、优先审核等特权30天",
        points: 1200,
        image: "/static/images/banner/vip-card-placeholder.png",
        category: 3,
        validity: "激活后30天内有效",
        rules: "会员期间享受所有VIP特权",
        tag: "虚拟"
      },
      {
        id: 9,
        name: "积分抽奖券",
        description: "可参与平台积分抽奖活动，赢取实物大奖",
        points: 100,
        image: "/static/images/banner/lottery-placeholder.png",
        category: 3,
        validity: "兑换后30天内有效",
        rules: "每次抽奖消耗1张抽奖券",
        tag: "虚拟"
      },
      {
        id: 10,
        name: "本地活动报名券",
        description: "免费报名本地线下活动、沙龙、讲座等",
        points: 80,
        image: "/static/images/banner/event-ticket-placeholder.png",
        category: 3,
        validity: "兑换后30天内有效",
        rules: "部分活动需提前预约，具体以活动规则为准",
        tag: "虚拟"
      },
      {
        id: 11,
        name: "专属头像框",
        description: "兑换后可装饰个人头像，彰显个性",
        points: 60,
        image: "/static/images/banner/avatar-frame-placeholder.png",
        category: 3,
        validity: "永久有效",
        rules: "可在个人中心-头像设置中更换",
        tag: "虚拟"
      },
      {
        id: 12,
        name: "话费充值10元",
        description: "兑换后系统将自动为您绑定的手机号充值",
        points: 1e3,
        image: "/static/images/banner/phone-recharge.png",
        category: 3,
        validity: "兑换后立即到账",
        rules: "仅限本人实名认证手机号使用，充值成功后不予退还",
        tag: "热门"
      },
      {
        id: 13,
        name: "话费充值30元",
        description: "兑换后系统将自动为您绑定的手机号充值",
        points: 2500,
        image: "/static/images/banner/phone-recharge.png",
        category: 3,
        validity: "兑换后立即到账",
        rules: "仅限本人实名认证手机号使用，充值成功后不予退还",
        tag: "超值"
      },
      {
        id: 14,
        name: "话费充值100元",
        description: "兑换后系统将自动为您绑定的手机号充值",
        points: 8e3,
        image: "/static/images/banner/phone-recharge.png",
        category: 3,
        validity: "兑换后立即到账",
        rules: "仅限本人实名认证手机号使用，充值成功后不予退还",
        tag: "限量"
      }
    ]);
    const filteredProducts = common_vendor.computed(() => {
      if (currentCategory.value === 0) {
        return [...products.value].sort((a, b) => {
          if (a.name.includes("话费") && !b.name.includes("话费"))
            return -1;
          if (!a.name.includes("话费") && b.name.includes("话费"))
            return 1;
          if (a.name.includes("话费") && b.name.includes("话费")) {
            const aValue = parseInt(a.name.match(/\d+/)[0]);
            const bValue = parseInt(b.name.match(/\d+/)[0]);
            return aValue - bValue;
          }
          if (a.category !== b.category) {
            if (a.category === 3)
              return -1;
            if (b.category === 3)
              return 1;
            if (a.category === 2)
              return -1;
            if (b.category === 2)
              return 1;
            if (a.category === 4)
              return -1;
            if (b.category === 4)
              return 1;
            return a.category - b.category;
          }
          return a.id - b.id;
        });
      } else {
        return products.value.filter((product) => product.category === currentCategory.value);
      }
    });
    common_vendor.onMounted(() => {
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight || 20;
      getUserPoints();
      currentCategory.value = 0;
    });
    function getUserPoints() {
      setTimeout(() => {
      }, 500);
    }
    function switchCategory(index) {
      currentCategory.value = categories[index].id;
    }
    function refreshProducts(e) {
      isRefreshing.value = true;
      setTimeout(() => {
        isRefreshing.value = false;
        common_vendor.index.showToast({
          title: "刷新成功",
          icon: "none"
        });
      }, 1500);
    }
    function showProductDetail(product) {
      selectedProduct.value = product;
      showPopup.value = true;
    }
    function exchangeProduct() {
      if (selectedProduct.value.points > userPoints.value) {
        common_vendor.index.showToast({
          title: "积分不足",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "确认兑换",
        content: `确定使用${selectedProduct.value.points}积分兑换"${selectedProduct.value.name}"吗？`,
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({
              title: "兑换中..."
            });
            setTimeout(() => {
              userPoints.value -= selectedProduct.value.points;
              common_vendor.index.hideLoading();
              showPopup.value = false;
              common_vendor.index.showToast({
                title: "兑换成功",
                icon: "success"
              });
            }, 1500);
          }
        }
      });
    }
    function navigateTo(url) {
      common_vendor.index.navigateTo({
        url
      });
    }
    function goBack() {
      common_vendor.index.navigateBack();
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: statusBarHeight.value + 44 + "px",
        b: common_assets._imports_0$7,
        c: common_vendor.o(goBack),
        d: statusBarHeight.value + "px",
        e: common_vendor.t(userPoints.value),
        f: common_vendor.o(($event) => navigateTo("/subPackages/checkin/pages/points-detail")),
        g: common_vendor.o(($event) => navigateTo("/subPackages/checkin/pages/exchange-history")),
        h: common_vendor.f(categories, (category, index, i0) => {
          return {
            a: common_vendor.t(category.name),
            b: currentCategory.value === category.id ? 1 : "",
            c: index,
            d: common_vendor.o(($event) => switchCategory(index), index)
          };
        }),
        i: common_vendor.f(filteredProducts.value, (product, index, i0) => {
          return common_vendor.e({
            a: product.image,
            b: common_vendor.t(product.name),
            c: common_vendor.t(product.description),
            d: common_vendor.t(product.points),
            e: common_vendor.t(product.points > userPoints.value ? "积分不足" : "立即兑换"),
            f: product.points > userPoints.value ? 1 : "",
            g: product.tag
          }, product.tag ? {
            h: common_vendor.t(product.tag)
          } : {}, {
            i: index,
            j: common_vendor.o(($event) => showProductDetail(product), index)
          });
        }),
        j: filteredProducts.value.length === 0
      }, filteredProducts.value.length === 0 ? {
        k: common_assets._imports_1$45
      } : {}, {
        l: common_vendor.o(refreshProducts),
        m: showPopup.value
      }, showPopup.value ? common_vendor.e({
        n: common_vendor.o(($event) => showPopup.value = false),
        o: selectedProduct.value.image,
        p: common_vendor.t(selectedProduct.value.name),
        q: common_vendor.t(selectedProduct.value.description),
        r: common_vendor.t(selectedProduct.value.points),
        s: selectedProduct.value.rules
      }, selectedProduct.value.rules ? {
        t: common_vendor.t(selectedProduct.value.rules)
      } : {}, {
        v: selectedProduct.value.stock
      }, selectedProduct.value.stock ? {
        w: common_vendor.t(selectedProduct.value.stock)
      } : {}, {
        x: selectedProduct.value.validity
      }, selectedProduct.value.validity ? {
        y: common_vendor.t(selectedProduct.value.validity)
      } : {}, {
        z: common_vendor.t(selectedProduct.value.points > userPoints.value ? "积分不足" : "立即兑换"),
        A: selectedProduct.value.points > userPoints.value ? 1 : "",
        B: selectedProduct.value.points > userPoints.value,
        C: common_vendor.o(exchangeProduct),
        D: common_vendor.o(($event) => showPopup.value = false)
      }) : {}, {
        E: statusBarHeight.value + 44 + "px"
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/checkin/pages/points-mall.js.map
