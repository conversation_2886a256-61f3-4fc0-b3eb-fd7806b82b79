version: '3.8'

# 开发环境Docker Compose配置
# 仅启动基础服务，应用服务在IDE中运行

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: cizhou-mysql-dev
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: cizhou123456
      MYSQL_DATABASE: cizhou_admin
      MYSQL_USER: cizhou
      MYSQL_PASSWORD: cizhou123456
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - cizhou-dev-network

  # Redis缓存
  redis:
    image: redis:7.0-alpine
    container_name: cizhou-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    command: redis-server --appendonly yes --requirepass cizhou123456
    networks:
      - cizhou-dev-network

  # Nacos服务注册中心
  nacos:
    image: nacos/nacos-server:v2.3.0
    container_name: cizhou-nacos-dev
    restart: unless-stopped
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql
      MYSQL_SERVICE_PORT: 3306
      MYSQL_SERVICE_DB_NAME: nacos
      MYSQL_SERVICE_USER: root
      MYSQL_SERVICE_PASSWORD: cizhou123456
      MYSQL_SERVICE_DB_PARAM: characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useSSL=false&allowPublicKeyRetrieval=true
      NACOS_AUTH_ENABLE: true
      NACOS_AUTH_TOKEN: SecretKey012345678901234567890123456789012345678901234567890123456789
      NACOS_AUTH_IDENTITY_KEY: nacos
      NACOS_AUTH_IDENTITY_VALUE: nacos
    ports:
      - "8848:8848"
      - "9848:9848"
    volumes:
      - nacos_dev_data:/home/<USER>/data
    depends_on:
      - mysql
    networks:
      - cizhou-dev-network

  # RabbitMQ消息队列
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: cizhou-rabbitmq-dev
    restart: unless-stopped
    environment:
      RABBITMQ_DEFAULT_USER: cizhou
      RABBITMQ_DEFAULT_PASS: cizhou123456
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_dev_data:/var/lib/rabbitmq
    networks:
      - cizhou-dev-network

  # Zipkin链路追踪
  zipkin:
    image: openzipkin/zipkin:latest
    container_name: cizhou-zipkin-dev
    restart: unless-stopped
    ports:
      - "9411:9411"
    networks:
      - cizhou-dev-network

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: cizhou-prometheus-dev
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_dev_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - cizhou-dev-network

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: cizhou-grafana-dev
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: cizhou123456
    volumes:
      - grafana_dev_data:/var/lib/grafana
    networks:
      - cizhou-dev-network

volumes:
  mysql_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  nacos_dev_data:
    driver: local
  rabbitmq_dev_data:
    driver: local
  prometheus_dev_data:
    driver: local
  grafana_dev_data:
    driver: local

networks:
  cizhou-dev-network:
    driver: bridge
