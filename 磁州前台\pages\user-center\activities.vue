<template>
  <view class="activities-container">
    <cu-custom bgColor="bg-white" :isBack="true">
      <template slot="backText">返回</template>
      <template slot="content">我参与的活动</template>
    </cu-custom>
    
    <view class="content">
      <!-- 活动类型选择 -->
      <view class="tab-nav">
        <view 
          class="tab-item" 
          v-for="(tab, index) in tabs" 
          :key="index"
          :class="{active: currentTab === index}"
          @tap="switchTab(index)"
        >
          <text>{{tab.name}}</text>
          <view class="tab-badge" v-if="tab.count > 0">{{tab.count}}</view>
        </view>
      </view>
      
      <!-- 活动列表 -->
      <swiper class="tab-content" :current="currentTab" @change="onSwiperChange" :style="{height: swiperHeight + 'px'}">
        <!-- 全部活动 -->
        <swiper-item>
          <scroll-view scroll-y class="scroll-view" @scrolltolower="loadMore(0)">
            <view class="activity-list">
              <view class="activity-item" v-for="(item, index) in allActivities" :key="index" @tap="viewActivity(item)">
                <image class="activity-image" :src="item.image" mode="aspectFill"></image>
                <view class="activity-info">
                  <view class="activity-header">
                    <text class="activity-tag" :class="'tag-' + item.type">{{getTypeName(item.type)}}</text>
                    <text class="activity-status" :class="'status-' + item.status">{{getStatusText(item.status)}}</text>
                  </view>
                  <text class="activity-title">{{item.title}}</text>
                  <view class="activity-shop">
                    <image class="shop-logo" :src="item.shop.logo" mode="aspectFill"></image>
                    <text class="shop-name">{{item.shop.name}}</text>
                  </view>
                  <view class="activity-time">
                    <text>{{item.time}}</text>
                  </view>
                </view>
              </view>
              
              <!-- 加载更多 -->
              <view class="load-more" v-if="loadingStatus[0]">
                <text>加载中...</text>
              </view>
              
              <!-- 没有更多数据 -->
              <view class="no-more" v-if="!hasMore[0] && allActivities.length > 0">
                <text>没有更多活动了</text>
              </view>
              
              <!-- 空状态 -->
              <view class="empty-state" v-if="allActivities.length === 0">
                <image class="empty-image" src="/static/images/empty-activity.png" mode="aspectFit"></image>
                <text class="empty-text">暂无参与的活动</text>
              </view>
            </view>
          </scroll-view>
        </swiper-item>
        
        <!-- 拼团活动 -->
        <swiper-item>
          <scroll-view scroll-y class="scroll-view" @scrolltolower="loadMore(1)">
            <view class="activity-list">
              <view class="activity-item" v-for="(item, index) in grouponActivities" :key="index" @tap="viewActivity(item)">
                <image class="activity-image" :src="item.image" mode="aspectFill"></image>
                <view class="activity-info">
                  <view class="activity-header">
                    <text class="activity-tag tag-groupon">拼团</text>
                    <text class="activity-status" :class="'status-' + item.status">{{getStatusText(item.status)}}</text>
                  </view>
                  <text class="activity-title">{{item.title}}</text>
                  <view class="activity-shop">
                    <image class="shop-logo" :src="item.shop.logo" mode="aspectFill"></image>
                    <text class="shop-name">{{item.shop.name}}</text>
                  </view>
                  <view class="activity-progress">
                    <text class="progress-text">{{item.currentCount}}/{{item.needCount}}人</text>
                    <view class="progress-bar">
                      <view class="progress-inner" :style="{width: (item.currentCount / item.needCount * 100) + '%'}"></view>
                    </view>
                  </view>
                  <view class="activity-time">
                    <text>{{item.time}}</text>
                    <text class="countdown" v-if="item.status === 'ongoing'">剩余 {{item.leftTime}}</text>
                  </view>
                </view>
              </view>
              
              <!-- 加载更多 -->
              <view class="load-more" v-if="loadingStatus[1]">
                <text>加载中...</text>
              </view>
              
              <!-- 没有更多数据 -->
              <view class="no-more" v-if="!hasMore[1] && grouponActivities.length > 0">
                <text>没有更多拼团了</text>
              </view>
              
              <!-- 空状态 -->
              <view class="empty-state" v-if="grouponActivities.length === 0">
                <image class="empty-image" src="/static/images/empty-groupon.png" mode="aspectFit"></image>
                <text class="empty-text">暂无参与的拼团</text>
              </view>
            </view>
          </scroll-view>
        </swiper-item>
        
        <!-- 红包活动 -->
        <swiper-item>
          <scroll-view scroll-y class="scroll-view" @scrolltolower="loadMore(2)">
            <view class="activity-list">
              <view class="activity-item" v-for="(item, index) in redpacketActivities" :key="index" @tap="viewActivity(item)">
                <image class="activity-image" :src="item.image" mode="aspectFill"></image>
                <view class="activity-info">
                  <view class="activity-header">
                    <text class="activity-tag tag-redpacket">红包</text>
                    <text class="activity-status" :class="'status-' + item.status">{{getStatusText(item.status)}}</text>
                  </view>
                  <text class="activity-title">{{item.title}}</text>
                  <view class="activity-shop">
                    <image class="shop-logo" :src="item.shop.logo" mode="aspectFill"></image>
                    <text class="shop-name">{{item.shop.name}}</text>
                  </view>
                  <view class="redpacket-amount">
                    <text class="amount-value">¥{{item.amount}}</text>
                    <text class="amount-desc">{{item.desc}}</text>
                  </view>
                  <view class="activity-time">
                    <text>{{item.time}}</text>
                    <text class="validity">有效期至 {{item.validTime}}</text>
                  </view>
                </view>
              </view>
              
              <!-- 加载更多 -->
              <view class="load-more" v-if="loadingStatus[2]">
                <text>加载中...</text>
              </view>
              
              <!-- 没有更多数据 -->
              <view class="no-more" v-if="!hasMore[2] && redpacketActivities.length > 0">
                <text>没有更多红包了</text>
              </view>
              
              <!-- 空状态 -->
              <view class="empty-state" v-if="redpacketActivities.length === 0">
                <image class="empty-image" src="/static/images/empty-redpacket.png" mode="aspectFit"></image>
                <text class="empty-text">暂无参与的红包活动</text>
              </view>
            </view>
          </scroll-view>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentTab: 0,
      swiperHeight: 500,
      tabs: [
        { name: '全部活动', count: 0 },
        { name: '我的拼团', count: 0 },
        { name: '我的红包', count: 0 }
      ],
      allActivities: [],
      grouponActivities: [],
      redpacketActivities: [],
      hasMore: [true, true, true],
      loadingStatus: [false, false, false],
      page: [1, 1, 1]
    }
  },
  onLoad(options) {
    // 根据URL参数切换标签页
    if (options && options.tab) {
      this.currentTab = parseInt(options.tab)
    }
    
    this.loadAllActivities()
    this.loadGrouponActivities()
    this.loadRedpacketActivities()
    this.calcSwiperHeight()
  },
  onReady() {
    this.calcSwiperHeight()
  },
  methods: {
    // 计算swiper高度
    calcSwiperHeight() {
      const query = uni.createSelectorQuery().in(this)
      query.select('.content').boundingClientRect(data => {
        const windowHeight = uni.getSystemInfoSync().windowHeight
        const navHeight = uni.getSystemInfoSync().statusBarHeight + 44 // 导航栏高度
        const tabHeight = 50 // tab栏高度
        this.swiperHeight = windowHeight - navHeight - tabHeight - 20 // 20是内边距
      }).exec()
    },
    
    // 切换标签
    switchTab(index) {
      this.currentTab = index
    },
    
    // 滑动切换
    onSwiperChange(e) {
      this.currentTab = e.detail.current
    },
    
    // 加载全部活动
    loadAllActivities() {
      if (!this.hasMore[0] || this.loadingStatus[0]) return
      
      this.loadingStatus[0] = true
      
      // 模拟API请求
      setTimeout(() => {
        const activities = [
          {
            id: 1,
            type: 'groupon',
            title: '新鲜水果大礼包限时拼团',
            image: '/static/images/activity-1.jpg',
            status: 'ongoing',
            time: '2023-05-03 14:23',
            shop: {
              id: 101,
              name: '鲜果日记',
              logo: '/static/images/shop-1.jpg'
            }
          },
          {
            id: 2,
            type: 'redpacket',
            title: '新店开业红包大派送',
            image: '/static/images/activity-2.jpg',
            status: 'used',
            time: '2023-05-02 10:15',
            shop: {
              id: 102,
              name: '时尚精品店',
              logo: '/static/images/shop-2.jpg'
            }
          },
          {
            id: 3,
            type: 'coupon',
            title: '满100减30优惠券',
            image: '/static/images/activity-4.jpg',
            status: 'unused',
            time: '2023-05-01 09:45',
            shop: {
              id: 104,
              name: '日用百货',
              logo: '/static/images/shop-4.jpg'
            }
          }
        ]
        
        this.allActivities = [...this.allActivities, ...activities]
        this.hasMore[0] = this.page[0] < 2 // 模拟只有2页数据
        this.loadingStatus[0] = false
        this.page[0]++
        
        // 更新tab计数
        this.tabs[0].count = this.allActivities.length
      }, 500)
    },
    
    // 加载拼团活动
    loadGrouponActivities() {
      if (!this.hasMore[1] || this.loadingStatus[1]) return
      
      this.loadingStatus[1] = true
      
      // 模拟API请求
      setTimeout(() => {
        const activities = [
          {
            id: 1,
            title: '新鲜水果大礼包限时拼团',
            image: '/static/images/activity-1.jpg',
            status: 'ongoing',
            currentCount: 2,
            needCount: 3,
            leftTime: '11:24:36',
            time: '2023-05-03 14:23',
            shop: {
              id: 101,
              name: '鲜果日记',
              logo: '/static/images/shop-1.jpg'
            }
          },
          {
            id: 5,
            title: '美食团购大优惠',
            image: '/static/images/activity-5.jpg',
            status: 'success',
            currentCount: 3,
            needCount: 3,
            leftTime: '00:00:00',
            time: '2023-04-28 19:45',
            shop: {
              id: 105,
              name: '美食广场',
              logo: '/static/images/shop-5.jpg'
            }
          }
        ]
        
        this.grouponActivities = [...this.grouponActivities, ...activities]
        this.hasMore[1] = this.page[1] < 2 // 模拟只有2页数据
        this.loadingStatus[1] = false
        this.page[1]++
        
        // 更新tab计数
        this.tabs[1].count = this.grouponActivities.length
      }, 500)
    },
    
    // 加载红包活动
    loadRedpacketActivities() {
      if (!this.hasMore[2] || this.loadingStatus[2]) return
      
      this.loadingStatus[2] = true
      
      // 模拟API请求
      setTimeout(() => {
        const activities = [
          {
            id: 2,
            title: '新店开业红包大派送',
            image: '/static/images/activity-2.jpg',
            status: 'used',
            amount: '15.00',
            desc: '已使用',
            time: '2023-05-02 10:15',
            validTime: '2023-05-10',
            shop: {
              id: 102,
              name: '时尚精品店',
              logo: '/static/images/shop-2.jpg'
            }
          },
          {
            id: 6,
            title: '周末红包雨',
            image: '/static/images/activity-6.jpg',
            status: 'unused',
            amount: '10.00',
            desc: '满100可用',
            time: '2023-04-30 16:28',
            validTime: '2023-05-15',
            shop: {
              id: 106,
              name: '数码专营店',
              logo: '/static/images/shop-6.jpg'
            }
          },
          {
            id: 8,
            title: '五一假期红包',
            image: '/static/images/activity-8.jpg',
            status: 'expired',
            amount: '20.00',
            desc: '已过期',
            time: '2023-04-25 09:10',
            validTime: '2023-05-01',
            shop: {
              id: 108,
              name: '生活超市',
              logo: '/static/images/shop-8.jpg'
            }
          }
        ]
        
        this.redpacketActivities = [...this.redpacketActivities, ...activities]
        this.hasMore[2] = this.page[2] < 2 // 模拟只有2页数据
        this.loadingStatus[2] = false
        this.page[2]++
        
        // 更新tab计数
        this.tabs[2].count = this.redpacketActivities.length
      }, 500)
    },
    
    // 加载更多
    loadMore(tabIndex) {
      switch(tabIndex) {
        case 0:
          this.loadAllActivities()
          break
        case 1:
          this.loadGrouponActivities()
          break
        case 2:
          this.loadRedpacketActivities()
          break
      }
    },
    
    // 查看活动详情
    viewActivity(item) {
      let url = ''
      
      if (item.type === 'groupon' || this.currentTab === 1) {
        url = `/pages/user-center/groupon-detail?id=${item.id}`
      } else if (item.type === 'redpacket' || this.currentTab === 2) {
        url = `/pages/user-center/redpacket-detail?id=${item.id}`
      } else if (item.type === 'coupon') {
        url = `/pages/user-center/coupon-detail?id=${item.id}`
      } else {
        url = `/pages/activity/detail?id=${item.id}`
      }
      
      uni.navigateTo({ url })
    },
    
    // 获取活动类型名称
    getTypeName(type) {
      const typeMap = {
        'groupon': '拼团',
        'redpacket': '红包',
        'seckill': '秒杀',
        'coupon': '优惠券'
      }
      return typeMap[type] || '活动'
    },
    
    // 获取活动状态文本
    getStatusText(status) {
      const statusMap = {
        'ongoing': '进行中',
        'success': '已成功',
        'failed': '已失败',
        'used': '已使用',
        'unused': '未使用',
        'expired': '已过期'
      }
      return statusMap[status] || '未知'
    }
  }
}
</script>

<style lang="scss">
.activities-container {
  min-height: 100vh;
  background-color: #F5F5F7;
}

.content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 44px); /* 减去导航栏高度 */
}

/* 标签导航 */
.tab-nav {
  display: flex;
  background-color: #FFFFFF;
  height: 90rpx;
  border-bottom: 1rpx solid #F2F2F7;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
  
  &.active {
    color: #007AFF;
    font-weight: 500;
    
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 40rpx;
      height: 4rpx;
      background-color: #007AFF;
      border-radius: 2rpx;
    }
  }
  
  .tab-badge {
    min-width: 32rpx;
    height: 32rpx;
    padding: 0 6rpx;
    background-color: #FF3B30;
    color: #FFFFFF;
    font-size: 20rpx;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 8rpx;
  }
}

/* 标签内容 */
.tab-content {
  flex: 1;
}

.scroll-view {
  height: 100%;
}

/* 活动列表 */
.activity-list {
  padding: 24rpx;
  
  .activity-item {
    background-color: #FFFFFF;
    border-radius: 16rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
    overflow: hidden;
  }
  
  .activity-image {
    width: 100%;
    height: 240rpx;
  }
  
  .activity-info {
    padding: 20rpx;
  }
  
  .activity-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16rpx;
  }
  
  .activity-tag {
    font-size: 24rpx;
    padding: 4rpx 16rpx;
    border-radius: 20rpx;
    
    &.tag-groupon {
      background-color: #FFF3E0;
      color: #FF9500;
    }
    
    &.tag-redpacket {
      background-color: #FFEBEE;
      color: #FF3B30;
    }
    
    &.tag-seckill {
      background-color: #E8F5E9;
      color: #4CD964;
    }
    
    &.tag-coupon {
      background-color: #E3F2FD;
      color: #007AFF;
    }
  }
  
  .activity-status {
    font-size: 24rpx;
    
    &.status-ongoing {
      color: #4CD964;
    }
    
    &.status-success {
      color: #007AFF;
    }
    
    &.status-failed {
      color: #8E8E93;
    }
    
    &.status-used {
      color: #8E8E93;
    }
    
    &.status-unused {
      color: #FF9500;
    }
    
    &.status-expired {
      color: #8E8E93;
    }
  }
  
  .activity-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
    display: block;
  }
  
  .activity-shop {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
    
    .shop-logo {
      width: 40rpx;
      height: 40rpx;
      border-radius: 20rpx;
      margin-right: 12rpx;
    }
    
    .shop-name {
      font-size: 26rpx;
      color: #666;
    }
  }
  
  .activity-time {
    display: flex;
    justify-content: space-between;
    font-size: 24rpx;
    color: #8E8E93;
  }
  
  .activity-progress {
    margin-bottom: 16rpx;
    
    .progress-text {
      font-size: 24rpx;
      color: #666;
      margin-bottom: 8rpx;
      display: block;
    }
    
    .progress-bar {
      height: 16rpx;
      background-color: #F2F2F7;
      border-radius: 8rpx;
      overflow: hidden;
    }
    
    .progress-inner {
      height: 100%;
      background-color: #FF9500;
      border-radius: 8rpx;
    }
  }
  
  .countdown {
    color: #FF3B30;
  }
  
  .redpacket-amount {
    display: flex;
    align-items: baseline;
    margin-bottom: 16rpx;
    
    .amount-value {
      font-size: 32rpx;
      color: #FF3B30;
      font-weight: 600;
      margin-right: 12rpx;
    }
    
    .amount-desc {
      font-size: 24rpx;
      color: #8E8E93;
    }
  }
  
  .validity {
    color: #FF9500;
  }
}

/* 加载更多 */
.load-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 26rpx;
  color: #8E8E93;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  
  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
  }
  
  .empty-text {
    font-size: 28rpx;
    color: #8E8E93;
  }
}
</style> 