<template>
  <view class="package-detail-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">套餐详情</text>
      <view class="navbar-right">
        <view class="more-icon" @click="showMoreOptions">···</view>
      </view>
    </view>
    
    <!-- 页面内容 -->
    <scroll-view scroll-y class="page-content">
      <!-- 套餐基本信息 -->
      <view class="package-header">
        <view class="package-title-row">
          <text class="package-name">{{packageDetail.name}}</text>
          <view class="package-status" :class="packageDetail.statusClass">{{packageDetail.statusText}}</view>
        </view>
        <text class="package-desc">{{packageDetail.description}}</text>
        <view class="package-meta">
          <view class="meta-item">
            <text class="meta-label">创建时间：</text>
            <text class="meta-value">{{packageDetail.createTime}}</text>
          </view>
          <view class="meta-item">
            <text class="meta-label">活动时间：</text>
            <text class="meta-value">{{packageDetail.activityTime}}</text>
          </view>
          <view class="meta-item">
            <text class="meta-label">成团人数：</text>
            <text class="meta-value">{{packageDetail.groupSize}}人</text>
          </view>
        </view>
      </view>
      
      <!-- 套餐价格信息 -->
      <view class="price-card">
        <view class="price-row">
          <text class="price-label">市场价</text>
          <text class="price-value original">¥{{packageDetail.originalPrice}}</text>
        </view>
        <view class="price-row">
          <text class="price-label">拼团价</text>
          <text class="price-value group">¥{{packageDetail.groupPrice}}</text>
        </view>
        <view class="price-row">
          <text class="price-label">节省金额</text>
          <text class="price-value save">¥{{packageDetail.savingsAmount}}</text>
        </view>
        <view class="price-row">
          <text class="price-label">折扣率</text>
          <text class="price-value discount">{{packageDetail.discountRate}}折</text>
        </view>
      </view>
      
      <!-- 套餐商品列表 -->
      <view class="items-card">
        <view class="card-header">
          <text class="card-title">套餐商品</text>
        </view>
        <view class="items-list">
          <view class="item-row" v-for="(item, index) in packageDetail.items" :key="index">
            <view class="item-number">{{index + 1}}</view>
            <view class="item-info">
              <text class="item-name">{{item.name}}</text>
              <view class="item-meta">
                <text class="item-price">¥{{item.price}}</text>
                <text class="item-quantity">x {{item.quantity}}</text>
              </view>
            </view>
          </view>
        </view>
        <view class="items-summary">
          <text class="summary-text">共 {{packageDetail.totalItems}} 件商品，原价总计 ¥{{packageDetail.originalPrice}}</text>
        </view>
      </view>
      
      <!-- 销售数据 -->
      <view class="sales-card">
        <view class="card-header">
          <text class="card-title">销售数据</text>
          <view class="date-picker" @click="showDatePicker">
            <text class="date-text">{{dateRange}}</text>
            <view class="date-icon"></view>
          </view>
        </view>
        
        <view class="sales-stats">
          <view class="stat-box">
            <text class="stat-value">{{packageDetail.salesCount}}</text>
            <text class="stat-label">总销量</text>
          </view>
          <view class="stat-box">
            <text class="stat-value">{{packageDetail.viewCount}}</text>
            <text class="stat-label">浏览量</text>
          </view>
          <view class="stat-box">
            <text class="stat-value">{{packageDetail.conversionRate}}%</text>
            <text class="stat-label">转化率</text>
          </view>
          <view class="stat-box">
            <text class="stat-value">¥{{formatNumber(packageDetail.totalRevenue)}}</text>
            <text class="stat-label">总收入</text>
          </view>
        </view>
        
        <!-- 销售趋势图表 -->
        <view class="sales-chart">
          <view class="chart-header">
            <text class="chart-title">销售趋势</text>
          </view>
          <view class="chart-container">
            <!-- 这里可以放置实际的图表组件 -->
            <view class="chart-placeholder">
              <text class="placeholder-text">销售趋势图表</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 拼团记录 -->
      <view class="groups-card">
        <view class="card-header">
          <text class="card-title">拼团记录</text>
          <text class="view-all" @click="viewAllGroups">查看全部</text>
        </view>
        
        <view class="groups-list">
          <view class="group-item" v-for="(group, index) in packageDetail.recentGroups" :key="index">
            <view class="group-info">
              <view class="group-leader">
                <image class="leader-avatar" :src="group.leaderAvatar" mode="aspectFill"></image>
                <text class="leader-name">{{group.leaderName}}</text>
              </view>
              <view class="group-status" :class="group.statusClass">{{group.statusText}}</view>
            </view>
            <view class="group-progress">
              <view class="progress-text">
                <text>已参与：{{group.currentMembers}}/{{group.requiredMembers}}人</text>
                <text>{{group.progressPercent}}%</text>
              </view>
              <view class="progress-bar">
                <view class="progress-fill" :style="{ width: group.progressPercent + '%' }"></view>
              </view>
            </view>
            <view class="group-time">
              <text class="time-label">{{group.timeType}}：</text>
              <text class="time-value">{{group.timeValue}}</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部操作栏 -->
    <view class="footer-actions">
      <view class="action-button edit" @click="editPackage">
        <view class="button-icon edit-icon"></view>
        <text class="button-text">编辑套餐</text>
      </view>
      <view class="action-button share" @click="sharePackage">
        <view class="button-icon share-icon"></view>
        <text class="button-text">分享套餐</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      packageId: null,
      dateRange: '最近7天',
      
      // 套餐详情数据
      packageDetail: {
        id: 1,
        name: '四菜一汤家庭套餐',
        description: '适合3-4人用餐，经典家庭聚餐套餐，荤素搭配，营养均衡',
        createTime: '2023-04-10 14:30',
        activityTime: '2023-04-10 ~ 2023-05-10',
        statusText: '进行中',
        statusClass: 'active',
        groupSize: 3,
        
        originalPrice: '168.00',
        groupPrice: '99.00',
        savingsAmount: '69.00',
        discountRate: '5.9',
        
        items: [
          { name: '红烧肉', price: '48.00', quantity: 1 },
          { name: '糖醋排骨', price: '42.00', quantity: 1 },
          { name: '鱼香肉丝', price: '38.00', quantity: 1 },
          { name: '清炒时蔬', price: '22.00', quantity: 1 },
          { name: '紫菜蛋花汤', price: '18.00', quantity: 1 }
        ],
        totalItems: 5,
        
        salesCount: 56,
        viewCount: 1280,
        conversionRate: 4.3,
        totalRevenue: 5544.00,
        
        recentGroups: [
          {
            id: 1,
            leaderName: '张先生',
            leaderAvatar: '/static/images/avatars/user1.jpg',
            requiredMembers: 3,
            currentMembers: 2,
            progressPercent: 66,
            timeType: '剩余时间',
            timeValue: '12小时23分',
            statusText: '拼团中',
            statusClass: 'active'
          },
          {
            id: 2,
            leaderName: '李女士',
            leaderAvatar: '/static/images/avatars/user2.jpg',
            requiredMembers: 3,
            currentMembers: 3,
            progressPercent: 100,
            timeType: '成团时间',
            timeValue: '2023-04-15 18:30',
            statusText: '已成团',
            statusClass: 'success'
          },
          {
            id: 3,
            leaderName: '王先生',
            leaderAvatar: '/static/images/avatars/user3.jpg',
            requiredMembers: 3,
            currentMembers: 1,
            progressPercent: 33,
            timeType: '失败时间',
            timeValue: '2023-04-14 10:15',
            statusText: '已失败',
            statusClass: 'failed'
          }
        ]
      }
    }
  },
  onLoad(options) {
    if (options.id) {
      this.packageId = options.id;
      // 这里可以添加实际的数据加载逻辑
      this.loadPackageDetail();
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showMoreOptions() {
      uni.showActionSheet({
        itemList: ['复制套餐', '下架套餐', '删除套餐'],
        success: (res) => {
          switch(res.tapIndex) {
            case 0:
              this.copyPackage();
              break;
            case 1:
              this.deactivatePackage();
              break;
            case 2:
              this.confirmDeletePackage();
              break;
          }
        }
      });
    },
    loadPackageDetail() {
      // 实际项目中应该从服务器加载数据
      console.log('加载套餐详情，ID:', this.packageId);
    },
    formatNumber(num) {
      return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    },
    showDatePicker() {
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    viewAllGroups() {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/group/package-groups?id=${this.packageId}`
      });
    },
    editPackage() {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/group/create?id=${this.packageId}&type=package&edit=true`
      });
    },
    sharePackage() {
      uni.showToast({
        title: '分享功能开发中',
        icon: 'none'
      });
    },
    copyPackage() {
      uni.showToast({
        title: '复制功能开发中',
        icon: 'none'
      });
    },
    deactivatePackage() {
      uni.showModal({
        title: '确认下架',
        content: '确定要下架该套餐吗？下架后用户将无法购买。',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '下架成功',
              icon: 'success'
            });
          }
        }
      });
    },
    confirmDeletePackage() {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除该套餐吗？此操作不可恢复。',
        confirmText: '删除',
        confirmColor: '#FF3B30',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          }
        }
      });
    }
  }
}
</script>

<style lang="scss">
.package-detail-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: calc(80px + env(safe-area-inset-bottom));
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.more-icon {
  font-size: 20px;
  font-weight: bold;
  transform: rotate(90deg);
}

.page-content {
  height: calc(100vh - 59px - 80px - env(safe-area-inset-bottom));
}

/* 套餐头部信息 */
.package-header {
  margin: 15px;
  padding: 15px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.package-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.package-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.package-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.package-status.active {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.package-status.inactive {
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}

.package-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 15px;
}

.package-meta {
  border-top: 1px dashed #E5E7EB;
  padding-top: 15px;
}

.meta-item {
  display: flex;
  margin-bottom: 5px;
}

.meta-item:last-child {
  margin-bottom: 0;
}

.meta-label {
  font-size: 12px;
  color: #999;
  width: 80px;
}

.meta-value {
  font-size: 12px;
  color: #666;
}

/* 价格卡片 */
.price-card {
  margin: 15px;
  padding: 15px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-wrap: wrap;
}

.price-row {
  width: 50%;
  margin-bottom: 15px;
}

.price-row:nth-last-child(-n+2) {
  margin-bottom: 0;
}

.price-label {
  font-size: 12px;
  color: #999;
  display: block;
  margin-bottom: 5px;
}

.price-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.price-value.original {
  text-decoration: line-through;
  color: #999;
}

.price-value.group {
  color: #FF3B30;
}

.price-value.save {
  color: #FF9500;
}

.price-value.discount {
  color: #34C759;
}

/* 商品列表卡片 */
.items-card {
  margin: 15px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-header {
  padding: 15px;
  border-bottom: 1px solid #F5F7FA;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.items-list {
  padding: 0 15px;
}

.item-row {
  display: flex;
  padding: 15px 0;
  border-bottom: 1px solid #F5F7FA;
}

.item-row:last-child {
  border-bottom: none;
}

.item-number {
  width: 24px;
  height: 24px;
  background: #F0F2F5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: #666;
  margin-right: 10px;
}

.item-info {
  flex: 1;
}

.item-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
}

.item-meta {
  display: flex;
  justify-content: space-between;
}

.item-price {
  font-size: 12px;
  color: #FF3B30;
}

.item-quantity {
  font-size: 12px;
  color: #999;
}

.items-summary {
  padding: 15px;
  background: #F8FAFC;
  border-top: 1px solid #F5F7FA;
}

.summary-text {
  font-size: 12px;
  color: #666;
  text-align: center;
}

/* 销售数据卡片 */
.sales-card {
  margin: 15px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.date-picker {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
  font-size: 12px;
  color: #666;
}

.date-icon {
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 5px solid #666;
  margin-left: 5px;
}

.sales-stats {
  display: flex;
  padding: 15px;
  border-bottom: 1px solid #F5F7FA;
}

.stat-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #999;
}

.sales-chart {
  padding: 15px;
}

.chart-header {
  margin-bottom: 15px;
}

.chart-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.chart-container {
  height: 200px;
}

.chart-placeholder {
  width: 100%;
  height: 100%;
  background: #F8FAFC;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-text {
  font-size: 14px;
  color: #999;
}

/* 拼团记录卡片 */
.groups-card {
  margin: 15px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 30px;
}

.view-all {
  font-size: 14px;
  color: #9040FF;
}

.groups-list {
  padding: 0 15px;
}

.group-item {
  padding: 15px 0;
  border-bottom: 1px solid #F5F7FA;
}

.group-item:last-child {
  border-bottom: none;
}

.group-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.group-leader {
  display: flex;
  align-items: center;
}

.leader-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 8px;
}

.leader-name {
  font-size: 14px;
  color: #333;
}

.group-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.group-status.active {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.group-status.success {
  background: rgba(0, 122, 255, 0.1);
  color: #007AFF;
}

.group-status.failed {
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}

.group-progress {
  margin-bottom: 10px;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.progress-bar {
  height: 4px;
  background-color: #EBEDF5;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #9040FF, #5E35B1);
  border-radius: 2px;
}

.group-time {
  font-size: 12px;
  color: #999;
}

.time-value {
  color: #666;
}

/* 底部操作栏 */
.footer-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 80px;
  background: #FFFFFF;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 10;
}

.action-button {
  flex: 1;
  height: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.button-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
}

.edit-icon {
  background: rgba(0, 122, 255, 0.1);
  position: relative;
}

.edit-icon:before {
  content: '';
  width: 12px;
  height: 12px;
  border: 1px solid #007AFF;
  border-radius: 2px;
  transform: rotate(45deg);
}

.share-icon {
  background: rgba(52, 199, 89, 0.1);
  position: relative;
}

.share-icon:before {
  content: '';
  width: 12px;
  height: 6px;
  border-left: 1px solid #34C759;
  border-right: 1px solid #34C759;
  border-bottom: 1px solid #34C759;
}

.button-text {
  font-size: 12px;
  color: #666;
}
</style> 