<template>
  <!-- 商家推荐模块 -->
  <view class="merchant-recommend-section card-section fade-in">
    <view class="section-header">
      <view class="section-title-wrap">
        <text class="section-title">商家推荐</text>
      </view>
      <view class="section-more" @click="navigateToMore">
        <text class="more-text">全部</text>
        <text class="more-icon">&gt;</text>
      </view>
    </view>
    
    <!-- 水平轮播，每页显示多个竖向卡片 -->
    <swiper class="merchant-swiper" 
            :current="currentMerchantPage" 
            @change="onMerchantPageChange" 
            circular 
            :autoplay="true" 
            :interval="3000" 
            :duration="500">
      <swiper-item v-for="pageIndex in Math.ceil(recommendBusinessList.length / 2)" :key="'page-'+pageIndex">
        <view class="merchant-swiper-page">
          <view 
            class="merchant-card" 
            v-for="merchant in getMerchantsForPage(pageIndex-1)" 
            :key="merchant.id"
            @click="navigateToMerchant(merchant)">
            <!-- 商家图片 -->
            <view class="merchant-image">
              <image :src="merchant.logo" mode="aspectFill"></image>
              <!-- 标签覆盖层 -->
              <view class="tag-overlay">
                <view v-if="merchant.isNew" class="merchant-tag new-tag">新店</view>
                <view v-if="merchant.isHot" class="merchant-tag hot-tag">热门</view>
              </view>
            </view>
            
            <!-- 商家名称和评分 -->
              <view class="merchant-info">
              <view class="merchant-name-row">
                <text class="merchant-name">{{merchant.name}}</text>
                <view class="merchant-rating">
                  <text class="rating-score">{{merchant.rating || '4.8'}}</text>
                  <text class="rating-icon">★</text>
                </view>
              </view>
              
              <!-- 商家描述 -->
              <text class="merchant-desc">{{merchant.description}}</text>
              
              <!-- 分类和距离 -->
              <view class="merchant-bottom">
                <view class="merchant-category">{{merchant.category}}</view>
                <view class="merchant-distance">
                  <text class="distance-icon">📍</text>
                  <text>{{merchant.distance || '500m'}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>
    
    <!-- 轮播指示器 -->
    <view class="merchant-indicators">
      <view class="merchant-dot" 
        v-for="(dot, i) in Math.ceil(recommendBusinessList.length / 2)" 
        :key="i" 
        :class="{ active: currentMerchantPage === i }"
        @click="changeMerchantPage(i)"></view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';

const currentMerchantPage = ref(0);
const recommendBusinessList = ref([
        {
          id: 1,
          logo: '/static/images/merchant/logo1.jpg',
          name: '品味小厨',
          description: '特色家常菜 | 人均¥38',
          category: '餐饮',
          isFollowed: false,
          url: '/pages/business/shop-detail?id=1',
          rating: '4.9',
          distance: '350m',
          isHot: true
        },
        {
          id: 2,
          logo: '/static/images/merchant/logo2.jpg',
          name: '鲜花花店',
          description: '鲜花礼品 | 同城配送',
          category: '礼品',
          isFollowed: true,
          url: '/pages/business/shop-detail?id=2',
          rating: '4.7',
          distance: '1.2km',
          isNew: true
        },
        {
          id: 3,
          logo: '/static/images/merchant/logo3.jpg',
          name: '快修先生',
          description: '专业维修 | 上门服务',
          category: '维修',
          isFollowed: false,
          url: '/pages/business/shop-detail?id=3',
          rating: '4.8',
          distance: '800m'
        },
        {
          id: 4,
          logo: '/static/images/merchant/logo4.jpg',
          name: '鲜果超市',
          description: '新鲜水果 | 当日配送',
          category: '生鲜',
          isFollowed: false,
          url: '/pages/business/shop-detail?id=4',
          rating: '4.6',
          distance: '650m',
          isHot: true
        },
        {
          id: 5,
          logo: '/static/images/merchant/logo1.jpg',
          name: '川湘小馆',
          description: '川湘菜 | 人均¥45',
          category: '餐饮',
          isFollowed: false,
          url: '/pages/business/shop-detail?id=5',
          rating: '4.5',
          distance: '1.5km'
        },
        {
          id: 6,
          logo: '/static/images/merchant/logo2.jpg',
          name: '优品超市',
          description: '日用百货 | 满减活动',
          category: '超市',
          isFollowed: false,
          url: '/pages/business/shop-detail?id=6',
          rating: '4.7',
          distance: '800m',
          isNew: true
        }
]);

function getMerchantsForPage(pageIndex) {
      const startIndex = pageIndex * 2;
  return recommendBusinessList.value.slice(startIndex, startIndex + 2);
}

function onMerchantPageChange(e) {
  currentMerchantPage.value = e.detail.current;
}

function changeMerchantPage(index) {
  currentMerchantPage.value = index;
}

function navigateToMerchant(item) {
      if (!item.url) return;
      
      uni.navigateTo({
        url: item.url,
        fail: () => {
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
}

function followBusiness(id) {
  const index = recommendBusinessList.value.findIndex(item => item.id === id);
      if (index !== -1) {
    recommendBusinessList.value[index].isFollowed = !recommendBusinessList.value[index].isFollowed;
        
        uni.showToast({
      title: recommendBusinessList.value[index].isFollowed ? '收藏成功' : '取消收藏成功',
          icon: 'success'
        });
      }
}

function navigateToMore() {
      uni.navigateTo({
        url: '/pages/business/list',
        fail: () => {
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
}
</script>

<style lang="scss" scoped>
.merchant-recommend-section {
  margin: 24rpx 30rpx 30rpx;
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 35rpx; /* 35度圆角 */
  padding: 30rpx 24rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08), 
              0 5rpx 15rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 0 10rpx;
}

.section-title-wrap {
  display: flex;
  align-items: center;
}

.section-title {
  font-size: 34rpx;
  font-weight: 700;
  color: #1c1c1e;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
  letter-spacing: 0.5rpx;
  background: linear-gradient(135deg, #333, #666);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.section-more {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  transition: all 0.2s ease;
  background: rgba(0, 122, 255, 0.1);
  border: 1rpx solid rgba(0, 122, 255, 0.2);
}

.section-more:active {
  background: rgba(0, 122, 255, 0.2);
  transform: scale(0.96);
}

.more-text {
  font-size: 24rpx;
  color: #007AFF;
  font-weight: 600;
}

.more-icon {
  font-size: 24rpx;
  color: #007AFF;
  margin-left: 4rpx;
}

/* 轮播区域 */
.merchant-swiper {
  width: 100%;
  height: 380rpx;
  margin-top: 20rpx;
}

.merchant-swiper-page {
  height: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 15rpx;
  gap: 25rpx;
}

.merchant-card {
  width: 310rpx;
  height: 100%;
  background: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.08),
              0 5rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  transform: perspective(800px) rotateX(5deg);
  border: 1rpx solid rgba(255, 255, 255, 0.9);
  position: relative;
}

.merchant-card:active {
  transform: perspective(800px) rotateX(5deg) scale(0.98);
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.05);
}

/* 商家图片区域 */
.merchant-image {
  width: 100%;
  height: 180rpx;
  position: relative;
  overflow: hidden;
}

.merchant-image::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40rpx;
  background: linear-gradient(to top, rgba(0,0,0,0.2), transparent);
  z-index: 1;
}

.merchant-image image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.merchant-card:hover .merchant-image image {
  transform: scale(1.05);
}

.tag-overlay {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  z-index: 2;
}

.merchant-tag {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
  font-weight: 600;
  color: white;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
  letter-spacing: 0.5rpx;
}

.new-tag {
  background: linear-gradient(135deg, rgba(52, 199, 89, 0.9), rgba(48, 209, 88, 0.9));
}

.hot-tag {
  background: linear-gradient(135deg, rgba(255, 69, 58, 0.9), rgba(255, 59, 48, 0.9));
}

/* 商家信息区域 */
.merchant-info {
  flex: 1;
  padding: 16rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: linear-gradient(to bottom, #ffffff, #f9f9f9);
}

.merchant-name-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.merchant-name {
  font-size: 26rpx;
  color: #1c1c1e;
  font-weight: 700;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 70%;
  letter-spacing: 0.5rpx;
}

.merchant-rating {
  display: flex;
  align-items: center;
  background: rgba(255, 149, 0, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 10rpx;
}

.rating-score {
  font-size: 20rpx;
  color: #1c1c1e;
  font-weight: 600;
  margin-right: 4rpx;
}

.rating-icon {
  font-size: 20rpx;
  color: #FF9500;
}

.merchant-desc {
  font-size: 22rpx;
  color: #8a8a8e;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 12rpx;
  line-height: 1.3;
}

.merchant-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8rpx;
}

.merchant-category {
  font-size: 20rpx;
  color: #007AFF;
  background: rgba(0, 122, 255, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 600;
  border: 1rpx solid rgba(0, 122, 255, 0.2);
}

.merchant-distance {
  display: flex;
  align-items: center;
  font-size: 20rpx;
  color: #8a8a8e;
  background: rgba(142, 142, 147, 0.08);
  padding: 4rpx 10rpx;
  border-radius: 12rpx;
}

.distance-icon {
  font-size: 20rpx;
  margin-right: 4rpx;
}

/* 轮播指示器 */
.merchant-indicators {
  display: flex;
  justify-content: center;
  margin-top: 20rpx;
}

.merchant-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 6rpx;
  background-color: rgba(0, 0, 0, 0.1);
  margin: 0 6rpx;
  transition: all 0.3s ease;
  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.merchant-dot.active {
  width: 28rpx;
  background: linear-gradient(90deg, #007AFF, #5AC8FA);
  box-shadow: 0 1rpx 3rpx rgba(0, 122, 255, 0.3);
}

.card-section {
  margin-bottom: 20rpx;
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}
</style> 