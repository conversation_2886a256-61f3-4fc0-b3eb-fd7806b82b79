<view class="location-select-container"><view class="search-box"><view class="search-input-box"><text class="iconfont icon-search"></text><input type="text" class="search-input" placeholder="搜索地址" confirm-type="search" bindconfirm="{{a}}" value="{{b}}" bindinput="{{c}}"/><text wx:if="{{d}}" class="clear-btn" bindtap="{{e}}">×</text></view><button class="cancel-btn" bindtap="{{f}}">取消</button></view><view class="current-location-section"><view class="section-title">当前位置</view><view class="current-location-box" bindtap="{{j}}"><view class="location-icon-box"><text class="iconfont icon-location"></text></view><view class="location-info"><text class="location-name">{{g}}</text><text wx:if="{{h}}" class="location-detail">{{i}}</text></view><view class="location-action"><text class="use-btn">使用</text></view></view></view><view wx:if="{{k}}" class="search-result-section"><view class="section-title">搜索结果</view><view class="location-list"><view wx:for="{{l}}" wx:for-item="item" wx:key="c" class="location-item" bindtap="{{item.d}}"><view class="location-icon-box"><text class="iconfont icon-pin"></text></view><view class="location-info"><text class="location-name">{{item.a}}</text><text class="location-detail">{{item.b}}</text></view></view></view></view><view wx:if="{{m}}" class="history-section"><view class="section-title-row"><text class="section-title">历史记录</text><text class="clear-history" bindtap="{{n}}">清空</text></view><view class="location-list"><view wx:for="{{o}}" wx:for-item="item" wx:key="c" class="location-item" bindtap="{{item.d}}"><view class="location-icon-box"><text class="iconfont icon-time"></text></view><view class="location-info"><text class="location-name">{{item.a}}</text><text class="location-detail">{{item.b}}</text></view></view></view></view><view wx:if="{{p}}" class="empty-result"><image src="{{q}}" mode="aspectFit" class="empty-image"></image><text class="empty-text">没有找到相关地址</text></view></view>