"use strict";
const common_vendor = require("../../../../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_rect = common_vendor.resolveComponent("rect");
  (_component_path + _component_svg + _component_rect)();
}
const _sfc_main = {
  __name: "SearchAndCategory",
  props: {
    categories: {
      type: Array,
      default: () => []
    }
  },
  emits: ["search", "scan", "categoryChange"],
  setup(__props, { emit: __emit }) {
    const emit = __emit;
    const currentCategory = common_vendor.ref(0);
    const selectCategory = (index) => {
      currentCategory.value = index;
      emit("categoryChange", index);
    };
    const getGradientBackground = (baseColor) => {
      return `linear-gradient(135deg, ${baseColor} 0%, ${lightenColor(baseColor, 20)} 100%)`;
    };
    const lightenColor = (hex, percent) => {
      let r = parseInt(hex.substring(1, 3), 16);
      let g = parseInt(hex.substring(3, 5), 16);
      let b = parseInt(hex.substring(5, 7), 16);
      r = Math.min(255, Math.floor(r * (1 + percent / 100)));
      g = Math.min(255, Math.floor(g * (1 + percent / 100)));
      b = Math.min(255, Math.floor(b * (1 + percent / 100)));
      return `#${r.toString(16).padStart(2, "0")}${g.toString(16).padStart(2, "0")}${b.toString(16).padStart(2, "0")}`;
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          d: "M11 17.25a6.25 6.25 0 110-12.5 6.25 6.25 0 010 12.5zm0 0L21 21",
          stroke: "#8E8E93",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        b: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "20",
          height: "20"
        }),
        c: common_vendor.p({
          d: "M7 3H5a2 2 0 0 0-2 2v2M17 3h2a2 2 0 0 1 2 2v2M7 21H5a2 2 0 0 1-2-2v-2M17 21h2a2 2 0 0 0 2-2v-2",
          stroke: "#FF3B69",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        d: common_vendor.p({
          x: "9",
          y: "9",
          width: "6",
          height: "6",
          rx: "1",
          stroke: "#FF3B69",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        e: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "20",
          height: "20"
        }),
        f: common_vendor.o(($event) => _ctx.$emit("scan")),
        g: common_vendor.o(($event) => _ctx.$emit("search")),
        h: common_vendor.f(__props.categories, (category, index, i0) => {
          return common_vendor.e({
            a: category.icon,
            b: getGradientBackground(category.bgColor),
            c: currentCategory.value === index
          }, currentCategory.value === index ? {} : {}, {
            d: common_vendor.t(category.name),
            e: index,
            f: currentCategory.value === index ? 1 : "",
            g: common_vendor.o(($event) => selectCategory(index), index)
          });
        })
      };
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-779f5f92"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/components/mall/SearchAndCategory.js.map
