<template>
  <view class="published-list-container">
    <!-- 自定义导航栏 -->
    <view class="custom-header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="left-action" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="action-icon back-icon"></image>
        </view>
        <view class="title-area">
          <text class="page-title">我的发布</text>
        </view>
        <view class="right-action" @click="publishNewTrip">
          <image src="/static/images/icons/plus-white.png" class="plus-icon"></image>
        </view>
      </view>
    </view>
    
    <!-- 状态切换标签 -->
    <scroll-view class="status-tabs" scroll-x="true" show-scrollbar="false" :style="{ top: (statusBarHeight + 44) + 'px' }">
      <view class="tabs-container">
        <view 
          class="tab-item" 
          v-for="(tab, index) in statusTabs" 
          :key="index"
          :class="{ active: currentStatus === tab.value }"
          @click="switchStatus(tab.value)"
        >
          <text class="tab-text">{{tab.label}}</text>
          <text class="tab-count" v-if="tab.count > 0">({{tab.count}})</text>
          <view class="tab-line" v-if="currentStatus === tab.value"></view>
            </view>
      </view>
    </scroll-view>

    <!-- 列表内容区域 -->
    <scroll-view 
      class="list-scroll" 
      scroll-y 
      refresher-enabled 
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="loadMore"
      :style="{ marginTop: (statusBarHeight + 88) + 'px', height: `calc(100vh - ${statusBarHeight + 88}px)` }"
    >
      <!-- 列表项 -->
      <view class="trip-list" v-if="tripList.length > 0">
        <view 
          class="trip-card" 
          v-for="(item, index) in tripList" 
          :key="item.id"
          :class="{ 'is-topped': item.isTopPinned }"
          @click="viewTripDetail(item)"
        >
          <!-- 行程基本信息 -->
          <view class="trip-route">
              <view class="route-points">
              <view class="route-line-container">
                  <view class="point-marker start"></view>
                <view class="route-line"></view>
                <view class="point-marker end"></view>
              </view>
              <view class="route-text">
                <view class="start-point">
                  <text class="point-text">{{item.startPoint}}</text>
                </view>
                <view class="end-point">
                  <text class="point-text">{{item.endPoint}}</text>
                </view>
                </view>
              </view>
            
            <view class="trip-meta">
              <view class="meta-left">
                <text class="time-text">{{item.departureTime}}</text>
                <text class="seat-text">{{item.totalSeats}}座</text>
                <view class="status-badge" :class="item.status">{{getStatusText(item.status)}}</view>
                <view class="top-badge" v-if="item.isTopPinned">
                  <text>置顶</text>
                </view>
                </view>
              <view class="meta-right">
                <text class="price-value">¥{{item.price}}</text>
          </view>
        </view>
      </view>
      
          <!-- 操作按钮 -->
          <view class="action-bar">
            <button class="action-btn" @click.stop="editContent(item)">编辑</button>
            <button class="action-btn" @click.stop="showTopOptions(item)">推广</button>
            <button class="action-btn" @click.stop="shareTrip(item)">转发</button>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="tripList.length === 0">
        <image src="/static/images/empty-state.png" class="empty-image"></image>
        <text class="empty-text">暂无{{getStatusText(currentStatus)}}的行程</text>
        <button class="publish-btn" @click="publishNewTrip">
          <text>发布新行程</text>
          <image src="/static/images/icons/arrow-right.png" class="arrow-icon"></image>
        </button>
      </view>
      
      <!-- 加载更多 -->
      <view class="loading-more" v-if="tripList.length > 0 && isLoading">
        <view class="loading-spinner"></view>
        <text>加载中...</text>
      </view>
      
      <view class="no-more" v-if="tripList.length > 0 && !hasMore && !isLoading">
        <text>没有更多了</text>
      </view>
    </scroll-view>
  </view>

  <!-- 更多操作弹窗 -->
  <view v-if="showMorePopup" class="popup-mask" @click="closeMorePopup">
    <view class="action-sheet" @click.stop>
        <view class="sheet-header">
        <text class="sheet-title">更多操作</text>
        <view class="close-btn" @click="closeMorePopup">
            <image src="/static/images/icons/close.png" class="close-icon"></image>
      </view>
            </view>
        
        <view class="sheet-content">
        <view class="option-item" @click="editContent(currentItem)">
            <view class="option-left">
            <view class="option-info">
              <text class="option-title">修改内容</text>
            </view>
            </view>
          </view>
          
        <view class="option-item" @click="deleteContent">
            <view class="option-left">
              <view class="option-info">
              <text class="option-title">删除内容</text>
            </view>
            </view>
          </view>
          
        <view class="option-item" @click="unpublishContent">
            <view class="option-left">
            <view class="option-info">
              <text class="option-title">下架内容</text>
            </view>
          </view>
        </view>
      </view>
        
        <view class="sheet-footer">
        <button class="cancel-btn" @click="closeMorePopup">取消</button>
      </view>
    </view>
  </view>
    
  <!-- 置顶选项弹窗 -->
  <view v-if="showTopPopup" class="popup-mask" @click="closeTopPopup">
    <view class="action-sheet promotion-sheet" @click.stop>
        <view class="sheet-header">
        <text class="sheet-title">推广选项</text>
        <view class="close-btn" @click="closeTopPopup">
            <image src="/static/images/icons/close.png" class="close-icon"></image>
      </view>
            </view>
        
      <!-- 推广选项列表 -->
      <view class="promotion-content">
        <!-- 置顶选项 -->
        <view class="promotion-card" @click="selectPromotionOption('top')">
          <view class="promotion-icon">
            <svg t="1692585872514" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2271" width="32" height="32">
              <path d="M857.088 224.256H166.912c-17.92 0-33.792 7.68-45.056 20.48-11.264 12.288-16.896 28.16-15.872 45.056l28.16 462.848c1.024 25.088 20.992 44.544 46.08 44.544H774.144c25.088 0 45.056-20.48 46.08-44.544l28.672-462.848c1.024-16.896-4.608-32.768-15.872-45.056-11.264-13.312-27.136-20.48-45.056-20.48z m-319.488 419.84h-51.2l25.088-227.84h1.024l25.088 227.84z m275.968 44.032c0 4.096-3.072 7.68-7.68 7.68H218.112c-4.096 0-7.68-3.584-7.68-7.68l-23.04-379.904c0-2.56 1.024-5.12 2.56-6.656 2.048-2.048 4.096-3.072 6.656-3.072h628.224c2.56 0 5.12 1.024 6.656 3.072 2.048 2.048 2.56 4.096 2.56 6.656l-20.48 379.904z" fill="#1296db" p-id="2272"></path>
              <path d="M512 143.872c10.24 0 18.944-7.168 21.504-17.408 0.512-3.072 0.512-5.632 0-8.704l-21.504-71.68-21.504 71.68c-0.512 3.072-0.512 5.632 0 8.704 2.56 10.24 11.264 17.408 21.504 17.408zM355.84 183.808c7.68-7.68 9.216-19.456 4.096-28.16-1.536-2.56-3.584-4.608-5.632-6.144L295.936 108.032l28.672 65.536c1.536 2.56 3.584 4.608 5.632 6.144 8.704 5.12 20.48 3.584 28.16-4.096 0-0.512-2.56-1.024-2.56-1.536zM206.336 276.992c3.072-10.752-1.536-22.528-11.264-27.648-3.072-1.536-5.632-2.048-8.704-2.56l-73.216-10.752 64.512 35.84c2.56 1.536 5.632 2.048 8.704 2.56 10.752 3.072 17.92 1.536 19.456 2.56h0.512zM819.2 236.032c-3.072 0.512-5.632 1.024-8.704 2.56-9.728 5.12-14.336 16.896-11.264 27.648l-0.512-0.512c1.536-1.024 8.704-0.512 19.456-2.56 3.072-0.512 5.632-1.024 8.704-2.56l64.512-35.84-73.216 10.752h1.024zM663.552 149.504c-2.048 1.536-4.096 3.584-5.632 6.144-5.12 9.216-3.584 20.992 4.096 28.16v0.512c7.68 7.68 19.456 9.216 28.16 4.096 2.048-1.536 4.096-3.584 5.632-6.144l28.672-65.536-58.368 41.472-2.56-8.704z" fill="#1296db" p-id="2273"></path>
            </svg>
            </view>
          <view class="promotion-info">
            <view class="promotion-title">置顶信息</view>
            <view class="promotion-desc">提升10倍曝光率，获得更多关注</view>
            <view class="promotion-tag">
              <view class="tag-item hot">热门</view>
            </view>
          </view>
          <view class="promotion-action">
            <view class="action-btn">立即置顶</view>
          </view>
        </view>
        
        <!-- 刷新选项 -->
        <view class="promotion-card" @click="selectPromotionOption('refresh')">
          <view class="promotion-icon refresh-icon">
            <svg t="1692586074385" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3295" width="32" height="32">
              <path d="M512 981.333333C252.8 981.333333 42.666667 771.2 42.666667 512S252.8 42.666667 512 42.666667s469.333333 210.133333 469.333333 469.333333-210.133333 469.333333-469.333333 469.333333z m0-853.333333C276.48 128 128 276.48 128 512s148.48 384 384 384 384-148.48 384-384S747.52 128 512 128z" fill="#6366f1" p-id="3296"></path>
              <path d="M512 725.333333c-117.76 0-213.333333-95.573333-213.333333-213.333333s95.573333-213.333333 213.333333-213.333333 213.333333 95.573333 213.333333 213.333333-95.573333 213.333333-213.333333 213.333333z m0-341.333333c-70.653333 0-128 57.346667-128 128s57.346667 128 128 128 128-57.346667 128-128-57.346667-128-128-128z" fill="#6366f1" p-id="3297"></path>
              <path d="M512 896c-23.466667 0-42.666667-19.2-42.666667-42.666667v-85.333333c0-23.466667 19.2-42.666667 42.666667-42.666667s42.666667 19.2 42.666667 42.666667v85.333333c0 23.466667-19.2 42.666667-42.666667 42.666667zM512 298.666667c-23.466667 0-42.666667-19.2-42.666667-42.666667V170.666667c0-23.466667 19.2-42.666667 42.666667-42.666667s42.666667 19.2 42.666667 42.666667v85.333333c0 23.466667-19.2 42.666667-42.666667 42.666667zM768 640h85.333333c23.466667 0 42.666667-19.2 42.666667-42.666667s-19.2-42.666667-42.666667-42.666666h-85.333333c-23.466667 0-42.666667 19.2-42.666667 42.666666s19.2 42.666667 42.666667 42.666667zM170.666667 640h85.333333c23.466667 0 42.666667-19.2 42.666667-42.666667s-19.2-42.666667-42.666667-42.666666H170.666667c-23.466667 0-42.666667 19.2-42.666667 42.666666s19.2 42.666667 42.666667 42.666667z" fill="#6366f1" p-id="3298"></path>
            </svg>
            </view>
          <view class="promotion-info">
            <view class="promotion-title">刷新信息</view>
            <view class="promotion-desc">刷新后置顶信息排名立刻升到置顶第一位！未置顶的会升到未置顶第一位！</view>
          </view>
          <view class="promotion-action">
            <view class="action-btn refresh-btn">立即刷新</view>
          </view>
            </view>
          
        <!-- 续费选项 -->
      </view>
        
        <view class="sheet-footer">
        <button class="cancel-btn" @click="closeTopPopup">取消</button>
      </view>
    </view>
  </view>
  
  <!-- 广告或付费选项弹窗 -->
  <view v-if="showPromotionOptionsPopup" class="popup-mask" @click="closePromotionOptionsPopup">
    <view class="action-sheet promotion-sheet" @click.stop>
      <view class="sheet-header">
        <text class="sheet-title">
          {{ 
            activePromotionType === 'top' ? '置顶方式' : 
            activePromotionType === 'refresh' ? '刷新方式' : 
            '推广方式'
          }}
        </text>
        <view class="close-btn" @click="closePromotionOptionsPopup">
          <image src="/static/images/icons/close.png" class="close-icon"></image>
        </view>
      </view>
        
      <!-- 引入统一推广组件 -->
      <view class="unified-promotion-container">
        <ConfigurablePremiumActions
          :info-id="currentItem ? currentItem.id : ''"
          :page-type="activePromotionType"
          show-mode="direct"
          :refresh-count="userRefreshCount"
          @action-completed="handlePromotionCompleted"
          class="compact-card"
        />
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import ConfigurablePremiumActions from '@/components/premium/ConfigurablePremiumActions.vue'

// 数据定义
const statusBarHeight = ref(20)
const statusTabs = ref([
  { label: '全部', value: 'all', count: 0 },
  { label: '待出行', value: 'pending', count: 0 },
  { label: '进行中', value: 'ongoing', count: 0 },
  { label: '已完成', value: 'completed', count: 0 },
  { label: '已取消', value: 'canceled', count: 0 }
])
const currentStatus = ref('all')
const tripList = ref([])
const page = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)
const isLoading = ref(false)
const isRefreshing = ref(false)
const currentItem = ref(null)
const showMorePopup = ref(false)
const showTopPopup = ref(false)
// 推广相关状态
const activePromotionType = ref('') // 当前激活的推广类型：'top', 'refresh', 'renew'
const userRefreshCount = ref(0) // 用户剩余的刷新次数
const showPromotionOptionsPopup = ref(false) // 是否显示推广选项弹窗

// 生命周期钩子
onMounted(() => {
  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight
  
  // 为组件计算CSS变量
  const app = getApp()
  if (app.globalData) {
    app.globalData.statusBarHeight = statusBarHeight.value
  }
  
  loadTripList()
  updateStatusCount()
})

// 组件渲染完成
nextTick(() => {
  // 组件已经渲染完毕
})

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 发布新行程
const publishNewTrip = () => {
  uni.navigateTo({
    url: '/carpool-package/pages/carpool/publish/index'
  })
}

// 切换状态标签
const switchStatus = (status) => {
  if (currentStatus.value === status) return
  currentStatus.value = status
  page.value = 1
  tripList.value = []
  hasMore.value = true
  loadTripList()
}

// 加载行程列表
const loadTripList = () => {
  if (isLoading.value) return
  isLoading.value = true
  
  // 模拟API调用
  setTimeout(() => {
    const mockData = getMockData()
    if (page.value === 1) {
      tripList.value = mockData
    } else {
      tripList.value = [...tripList.value, ...mockData]
    }

    hasMore.value = page.value < 3 // 模拟数据分页
    isLoading.value = false
    isRefreshing.value = false
  }, 1000)
}

// 获取模拟数据
const getMockData = () => {
  return [
    {
      id: '1001',
      status: 'pending',
      startPoint: '磁县火车站',
      endPoint: '邯郸东站',
      departureTime: '2024-01-20 14:30',
      totalSeats: 4,
      price: 35,
      orders: [
        {
          userAvatar: '/static/images/avatar/user1.png',
          userName: '张先生',
          seats: 2,
          status: 'confirmed'
        }
      ]
    },
    {
      id: '1002',
      status: 'ongoing',
      startPoint: '磁县汽车站',
      endPoint: '邯郸市区',
      departureTime: '2024-01-19 09:30',
      totalSeats: 3,
      price: 25,
      orders: [
        {
          userAvatar: '/static/images/avatar/user2.png',
          userName: '李女士',
          seats: 1,
          status: 'completed'
        }
      ]
    }
  ]
}

// 更新状态计数
const updateStatusCount = () => {
  // 实际应用中应该从API获取
  statusTabs.value = statusTabs.value.map(tab => ({
    ...tab,
    count: tab.value === 'all' ? 8 : 2
  }))
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    all: '全部',
    pending: '待出行',
    ongoing: '进行中',
    completed: '已完成',
    canceled: '已取消'
  }
  return statusMap[status] || status
}

// 下拉刷新
const onRefresh = () => {
  isRefreshing.value = true
  page.value = 1
  loadTripList()
}

// 加载更多
const loadMore = () => {
  if (hasMore.value && !isLoading.value) {
    page.value++
    loadTripList()
  }
}

// 查看行程详情
const viewTripDetail = (item) => {
  uni.navigateTo({
    url: `/carpool-package/pages/carpool/trip-detail/index?id=${item.id}`
  })
}

// 分享行程
const shareTrip = (item) => {
  uni.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage', 'shareTimeline']
  })
}

// 显示更多选项
const showMoreOptions = (item) => {
  currentItem.value = item
  showMorePopup.value = true
}

// 关闭更多弹窗
const closeMorePopup = () => {
  showMorePopup.value = false
}

// 修改内容
const editContent = (item) => {
  if (item) {
    showMorePopup.value = false
    uni.navigateTo({
      url: `/carpool-package/pages/carpool/edit-trip/index?id=${item.id}`
    })
  }
}

// 删除内容
const deleteContent = () => {
  uni.showModal({
    title: '删除内容',
    content: '确定要删除该行程吗？删除后无法恢复',
    success: (res) => {
      if (res.confirm) {
        // 实际应用中需要调用删除API
        showMorePopup.value = false
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
        // 从列表中移除
        tripList.value = tripList.value.filter(i => i.id !== currentItem.value.id)
      }
    }
  })
}

// 下架内容
const unpublishContent = () => {
  uni.showModal({
    title: '下架内容',
    content: '确定要下架该行程吗？下架后可在已下架列表中重新上架',
    success: (res) => {
      if (res.confirm) {
        // 实际应用中需要调用下架API
        showMorePopup.value = false
        uni.showToast({
          title: '下架成功',
          icon: 'success'
        })
        // 更新状态
        if (currentItem.value) {
          currentItem.value.status = 'unpublished'
          // 如果当前不是显示全部，则从列表中移除
          if (currentStatus.value !== 'all') {
            tripList.value = tripList.value.filter(i => i.id !== currentItem.value.id)
          }
        }
      }
    }
  })
}

// 显示置顶选项
const showTopOptions = (item) => {
  currentItem.value = item
  activePromotionType.value = ''
  showTopPopup.value = true
}

// 选择推广选项
const selectPromotionOption = (type) => {
  activePromotionType.value = type
  showTopPopup.value = false
  showPromotionOptionsPopup.value = true
}

// 关闭置顶弹窗
const closeTopPopup = () => {
  showTopPopup.value = false
  activePromotionType.value = ''
}

// 关闭推广选项弹窗
const closePromotionOptionsPopup = () => {
  showPromotionOptionsPopup.value = false
  activePromotionType.value = ''
}

// 广告刷新
const refreshByAd = () => {
  activePromotionType.value = 'refresh'
  showTopPopup.value = true
}

// 处理推广完成事件
const handlePromotionCompleted = (eventType, data) => {
  showPromotionOptionsPopup.value = false
  
  // 根据不同的推广类型处理结果
  if (data.action === 'top') {
    // 处理置顶完成
    if (currentItem.value) {
      currentItem.value.isTopPinned = true
      if (data.option && data.option.duration) {
        currentItem.value.topPinnedDays = parseInt(data.option.duration) || 1
      }
    }
    uni.showToast({
      title: '置顶成功',
      icon: 'success'
    })
  } else if (data.action === 'refresh') {
    // 处理刷新完成
    if (eventType === 'useRefreshCount') {
      // 更新剩余次数
      userRefreshCount.value = data.remainingCount || 0
    }
  uni.showToast({
    title: '刷新成功',
    icon: 'success'
  })
  // 重新加载列表
  loadTripList()
  }
  
  // 如果是购买刷新套餐
  if (eventType === 'refreshPackage') {
    userRefreshCount.value = data.totalCount || 0
  uni.showToast({
      title: `购买${data.count}次成功`,
    icon: 'success'
  })
  }
}

// 暴露方法给外部访问
defineExpose({
  loadTripList,
  switchStatus,
  publishNewTrip
})
</script>

<style lang="scss">
.published-list-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
}

/* 导航栏样式 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
}

.header-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
}

.left-action, .right-action {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  width: 24px;
  height: 24px;
}

.back-icon {
  width: 24px;
  height: 24px;
  /* 图标是黑色的，需要转为白色 */
  filter: brightness(0) invert(1);
}

.title-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.plus-icon {
  width: 24px;
  height: 24px;
  /* 确保图标可见 */
  filter: brightness(0) invert(1);
}

/* 状态标签栏 */
.status-tabs {
  width: 100%;
  height: 44px;
  background-color: #FFFFFF;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: fixed;
  left: 0;
  right: 0;
  z-index: 99;
  white-space: nowrap;
}

.tabs-container {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 12px;
}

.tab-item {
  position: relative;
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.tab-text {
  font-size: 15px;
  font-weight: 500;
  color: #64748B;
  transition: all 0.3s;
}

.tab-count {
  font-size: 13px;
  color: #94A3B8;
  margin-left: 3px;
}

.tab-item.active .tab-text {
  color: #0A84FF;
  font-weight: 600;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 3px;
  background-color: #0A84FF;
  border-radius: 1.5px;
}

/* 列表内容区域 */
.list-scroll {
  flex: 1;
  width: 100%;
}

/* 行程卡片 */
.trip-card {
  background: #FFFFFF;
  border-radius: 8px;
  margin: 10px 16px;
  padding: 14px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s;
}

.trip-card.is-topped {
  background: linear-gradient(to right, #FFFFFF, #F0F9FF);
  border-left: 3px solid #007AFF;
}

.trip-card:active {
  transform: scale(0.98);
}

/* 路线信息 */
.trip-route {
  margin-bottom: 12px;
}

.route-points {
  position: relative;
  margin-bottom: 10px;
  padding-left: 30px;
  min-height: 60px;
}

.route-line-container {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.point-marker {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  z-index: 2;
}

.point-marker.start {
  background-color: #10B981;
}

.point-marker.end {
  background-color: #EF4444;
  margin-top: auto;
}

.route-line {
  position: absolute;
  left: 50%;
  top: 10px;
  bottom: 10px;
  width: 1px;
  background-color: #E5E7EB;
  transform: translateX(-50%);
}

.route-text {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.start-point, .end-point {
  display: flex;
  flex-direction: column;
  padding: 4px 0;
}

.point-text {
  font-size: 15px;
  color: #1F2937;
  font-weight: 500;
}

.trip-meta {
  display: flex;
  padding: 10px;
  background-color: #F9FAFB;
  border-radius: 6px;
  align-items: center;
}

.meta-left {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-text {
  font-size: 13px;
  color: #4B5563;
}

.seat-text {
  font-size: 13px;
  color: #4B5563;
  padding: 2px 6px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.status-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.pending {
  background-color: #FFF7ED;
  color: #F59E0B;
}

.status-badge.ongoing {
  background-color: #EFF6FF;
  color: #3B82F6;
}

.status-badge.completed {
  background-color: #ECFDF5;
  color: #10B981;
}

.status-badge.canceled {
  background-color: #F3F4F6;
  color: #6B7280;
}

.top-badge {
  padding: 2px 6px;
  border-radius: 4px;
  background-color: #F0F9FF;
}

.top-badge text {
  font-size: 12px;
  color: #0284C7;
}

.meta-right {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.price-value {
  font-size: 16px;
  font-weight: 600;
  color: #EF4444;
}

/* 操作按钮区域 */
.action-bar {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 12px;
}

.action-btn {
  height: 32px;
  line-height: 32px;
  padding: 0 16px;
  border: none;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-btn:nth-child(1) {
  background: linear-gradient(135deg, #0acffe, #495aff);
}

.action-btn:nth-child(2) {
  background: linear-gradient(135deg, #ff6a88, #ff99ac);
}

.action-btn:nth-child(3) {
  background: linear-gradient(135deg, #b275ff, #7c4dff);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 24px;
}

.empty-text {
  font-size: 15px;
  color: #6B7280;
  margin-bottom: 24px;
}

.publish-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  background-color: #007AFF;
  border-radius: 20px;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.15);
}

.publish-btn text {
  font-size: 15px;
  color: #FFFFFF;
  margin-right: 8px;
  text-align: center;
}

.arrow-icon {
  width: 16px;
  height: 16px;
}

/* 加载更多 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 0;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #E5E7EB;
  border-top-color: #007AFF;
  border-radius: 50%;
  margin-right: 8px;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-more text {
  font-size: 13px;
  color: #6B7280;
}

.no-more {
  text-align: center;
  padding: 16px 0;
}

.no-more text {
  font-size: 13px;
  color: #9CA3AF;
}

/* 自定义弹窗样式 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  transition: all 0.3s;
}

.action-sheet {
  background-color: #FFFFFF;
  border-radius: 16px 16px 0 0;
  overflow: hidden;
  margin: 0 12px 12px;
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1000;
}

.sheet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #F3F4F6;
}

.sheet-title {
  font-size: 16px;
  font-weight: 600;
  color: #1F2937;
}

.close-btn {
  padding: 4px;
}

.close-icon {
  width: 20px;
  height: 20px;
  opacity: 0.6;
}

.sheet-content {
  padding: 8px 0;
}

.option-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #F3F4F6;
}

.option-item:last-child {
  border-bottom: none;
}

.option-left {
  display: flex;
  align-items: center;
}

.option-icon {
  width: 32px;
  height: 32px;
  margin-right: 12px;
}

.option-info {
  display: flex;
  flex-direction: column;
}

.option-title {
  font-size: 16px;
  color: #1F2937;
}

.option-desc {
  font-size: 13px;
  color: #6B7280;
  margin-top: 4px;
}

.option-price {
  font-size: 16px;
  font-weight: 600;
  color: #EF4444;
}

.option-price.free {
  color: #10B981;
}

.sheet-footer {
  padding: 16px;
  border-top: 1px solid #F3F4F6;
}

.cancel-btn {
  width: 100%;
  height: 44px;
  line-height: 44px;
  background-color: #F9FAFB;
  border: none;
  font-size: 16px;
  color: #4B5563;
  border-radius: 0;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 更多操作弹窗样式 */
.sheet-content .option-item {
  padding: 16px;
  border-bottom: 1px solid #F5F5F5;
}

.sheet-content .option-item .option-title {
  font-size: 16px;
  font-weight: normal;
  color: #333;
}

/* 推广弹窗底部取消按钮 */
.promotion-sheet .sheet-footer {
  padding: 16px 20px 25px;
}

.promotion-sheet .sheet-footer .cancel-btn {
  background: linear-gradient(to right, #f8f9fa, #ffffff, #f8f9fa);
  border: 1px solid #E5E7EB;
  border-radius: 22px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  height: 44px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
}

.promotion-sheet .sheet-footer .cancel-btn:active {
  transform: scale(0.98);
  background: #f8f9fa;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 推广内容区域样式 */
.promotion-content {
  padding: 10px 15px;
}

.promotion-card {
  display: flex;
  align-items: center;
  background: #FFFFFF;
  border-radius: 12px;
  margin-bottom: 15px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.promotion-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.5) 100%);
  z-index: 1;
}

.promotion-icon {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background-color: rgba(18, 150, 219, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
  box-shadow: 0 2px 6px rgba(18, 150, 219, 0.15);
}

.refresh-icon {
  background-color: rgba(99, 102, 241, 0.2);
  box-shadow: 0 2px 6px rgba(99, 102, 241, 0.15);
}

.promotion-icon .icon {
  width: 28px;
  height: 28px;
}

.promotion-info {
  flex: 1;
}

.promotion-title {
  font-size: 16px;
  font-weight: 600;
  color: #1F2937;
  margin-bottom: 6px;
}

.promotion-desc {
  font-size: 13px;
  color: #6B7280;
  margin-bottom: 6px;
}

.promotion-tag {
  display: flex;
  gap: 5px;
}

.tag-item {
  display: inline-block;
  padding: 2px 6px;
  font-size: 10px;
  color: #FFFFFF;
  border-radius: 4px;
}

.hot {
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
}

.promotion-action {
  margin-left: 10px;
  flex-shrink: 0;
}

.promotion-action .action-btn {
  padding: 6px 12px;
  background: linear-gradient(135deg, #1296DB, #0074CC);
  border-radius: 15px;
  font-size: 14px;
  color: #FFFFFF;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-btn {
  background: linear-gradient(135deg, #6366F1, #4F46E5);
}

.promotion-sheet {
  border-radius: 20px 20px 0 0;
  overflow: hidden;
}

/* 标签渐变效果 */
.tag-item.hot {
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);
}

/* 按钮悬浮效果 */
.action-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

/* 统一推广组件容器 */
.unified-promotion-container {
  padding: 10px 15px;
}

/* 紧凑卡片模式下的样式调整 */
:deep(.compact-card) {
  margin: 0;
}
</style> 