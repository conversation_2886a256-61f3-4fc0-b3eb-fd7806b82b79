<view class="house-rent-container"><view class="custom-navbar" style="{{'padding-top:' + c}}"><view class="navbar-left" bindtap="{{b}}"><image src="{{a}}" class="back-icon"></image></view><view class="navbar-title">房屋出租详情</view><view class="navbar-right"></view></view><button id="shareButton" class="hidden-share-btn" open-type="share"></button><canvas canvas-id="posterCanvas" class="poster-canvas" style="width:600px;height:900px;position:fixed;top:-9999px;left:-9999px"></canvas><view class="house-rent-wrapper"><view class="content-card house-info-card"><view class="house-header"><view class="house-title-row"><text class="house-title">{{d}}</text><text class="house-price">{{e}}</text></view><view class="house-meta"><view class="house-tag-group"><view wx:for="{{f}}" wx:for-item="tag" wx:key="b" class="house-tag">{{tag.a}}</view></view><text class="house-publish-time">发布于 {{g}}</text></view></view><swiper class="house-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}"><swiper-item wx:for="{{h}}" wx:for-item="image" wx:key="b"><image src="{{image.a}}" mode="aspectFill" class="house-image"></image></swiper-item></swiper><view class="house-basic-info"><view class="info-item"><text class="info-label">房屋类型</text><text class="info-value">{{i}}</text></view><view class="info-item"><text class="info-label">所在区域</text><text class="info-value">{{j}}</text></view><view class="info-item"><text class="info-label">房屋面积</text><text class="info-value">{{k}}</text></view><view class="info-item"><text class="info-label">房屋朝向</text><text class="info-value">{{l}}</text></view></view></view><view class="content-card house-config-card"><view class="section-title">房屋配置</view><view class="config-list"><view wx:for="{{m}}" wx:for-item="item" wx:key="c" class="config-item"><text class="{{['config-icon', 'iconfont', item.a]}}"></text><text class="config-text">{{item.b}}</text></view></view></view><view class="content-card house-detail-card"><view class="section-title">房屋详情</view><view class="detail-list"><view wx:for="{{n}}" wx:for-item="item" wx:key="c" class="detail-item"><text class="detail-label">{{item.a}}</text><text class="detail-value">{{item.b}}</text></view></view></view><view class="content-card location-card"><view class="section-title">位置信息</view><view class="location-content"><view class="location-address"><text class="address-icon iconfont icon-location"></text><text class="address-text">{{o}}</text></view><view class="location-map"><map class="map" latitude="{{p}}" longitude="{{q}}" markers="{{r}}"></map></view><view class="location-surroundings"><view wx:for="{{s}}" wx:for-item="item" wx:key="d" class="surrounding-item"><text class="{{['surrounding-icon', 'iconfont', item.a]}}"></text><text class="surrounding-text">{{item.b}}</text><text class="surrounding-distance">{{item.c}}</text></view></view></view></view><view class="content-card rent-requirements-card"><view class="section-title">出租要求</view><view class="requirements-content"><view wx:for="{{t}}" wx:for-item="item" wx:key="c" class="requirement-item"><text class="requirement-label">{{item.a}}</text><text class="requirement-value">{{item.b}}</text></view></view></view><view class="content-card landlord-card"><view class="landlord-header"><view class="landlord-avatar"><image src="{{v}}" mode="aspectFill"></image></view><view class="landlord-info"><text class="landlord-name">{{w}}</text><view class="landlord-meta"><text class="landlord-type">{{x}}</text><text class="landlord-rating">信用等级 {{y}}</text></view></view><view wx:if="{{z}}" class="landlord-auth"><text class="iconfont icon-verified"></text><text class="auth-text">已认证</text></view></view></view><view class="content-card contact-card"><view class="contact-header"><text class="section-title">联系方式</text></view><view class="contact-content"><view class="contact-item"><text class="contact-label">联系人</text><text class="contact-value">{{A}}</text></view><view class="contact-item"><text class="contact-label">电话</text><text class="contact-value contact-phone" bindtap="{{C}}">{{B}}</text></view><view class="contact-tips"><text class="tips-icon iconfont icon-info"></text><text class="tips-text">请说明在"磁州生活网"看到的信息</text></view></view></view><report-card wx:if="{{D}}" u-i="6da271c3-0" bind:__l="__l" u-p="{{D}}"></report-card><view class="content-card related-houses-card"><view class="section-title">相关推荐</view><view class="related-houses-content"><view class="related-houses-list"><view wx:for="{{E}}" wx:for-item="house" wx:key="i" class="related-house-item" bindtap="{{house.j}}"><view class="house-item-content"><view class="house-item-left"><image class="house-image" src="{{house.a}}" mode="aspectFill"></image></view><view class="house-item-middle"><text class="house-item-title">{{house.b}}</text><view class="house-item-meta">{{house.c}} · {{house.d}}</view><view class="house-item-tags"><text wx:for="{{house.e}}" wx:for-item="tag" wx:key="b" class="house-item-tag">{{tag.a}}</text><text wx:if="{{house.f}}" class="house-item-tag-more">+{{house.g}}</text></view></view><view class="house-item-right"><text class="house-item-price">{{house.h}}</text></view></view></view><view wx:if="{{F}}" class="empty-related-houses"><image src="{{G}}" class="empty-image" mode="aspectFit"></image><text class="empty-text">暂无相关推荐</text></view></view><view wx:if="{{H}}" class="view-more-btn" catchtap="{{I}}"><text class="view-more-text">查看更多房源</text><text class="view-more-icon iconfont icon-right"></text></view></view></view></view><view class="interaction-toolbar"><view class="toolbar-item" bindtap="{{K}}"><image src="{{J}}" class="toolbar-icon"></image><text class="toolbar-text">首页</text></view><view class="toolbar-item" bindtap="{{M}}"><image src="{{L}}" class="toolbar-icon"></image><text class="toolbar-text">收藏</text></view><button class="share-button toolbar-item" open-type="share"><image src="{{N}}" class="toolbar-icon"></image><text class="toolbar-text">分享</text></button><view class="toolbar-item" bindtap="{{P}}"><image src="{{O}}" class="toolbar-icon"></image><text class="toolbar-text">私信</text></view><view class="toolbar-item call-button" bindtap="{{Q}}"><view class="call-button-content"><text class="call-text">打电话</text><text class="call-subtitle">请说在磁州生活网看到的</text></view></view></view><view class="float-poster-btn" bindtap="{{S}}"><image src="{{R}}" class="poster-icon"></image><text class="poster-text">海报</text></view></view>